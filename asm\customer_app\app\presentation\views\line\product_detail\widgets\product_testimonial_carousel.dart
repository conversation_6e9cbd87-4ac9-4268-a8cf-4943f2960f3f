// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart

// class id: 1049562, size: 0x8
class :: {
}

// class id: 3221, size: 0x24, field offset: 0x14
class _ProductTestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x94af34, size: 0x1a0
    // 0x94af34: EnterFrame
    //     0x94af34: stp             fp, lr, [SP, #-0x10]!
    //     0x94af38: mov             fp, SP
    // 0x94af3c: AllocStack(0x30)
    //     0x94af3c: sub             SP, SP, #0x30
    // 0x94af40: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */)
    //     0x94af40: stur            x1, [fp, #-8]
    // 0x94af44: CheckStackOverflow
    //     0x94af44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94af48: cmp             SP, x16
    //     0x94af4c: b.ls            #0x94b0bc
    // 0x94af50: r0 = PageController()
    //     0x94af50: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x94af54: stur            x0, [fp, #-0x10]
    // 0x94af58: StoreField: r0->field_3f = rZR
    //     0x94af58: stur            xzr, [x0, #0x3f]
    // 0x94af5c: r2 = true
    //     0x94af5c: add             x2, NULL, #0x20  ; true
    // 0x94af60: StoreField: r0->field_47 = r2
    //     0x94af60: stur            w2, [x0, #0x47]
    // 0x94af64: d0 = 1.000000
    //     0x94af64: fmov            d0, #1.00000000
    // 0x94af68: StoreField: r0->field_4b = d0
    //     0x94af68: stur            d0, [x0, #0x4b]
    // 0x94af6c: mov             x1, x0
    // 0x94af70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94af70: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94af74: r0 = ScrollController()
    //     0x94af74: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94af78: ldur            x0, [fp, #-0x10]
    // 0x94af7c: ldur            x1, [fp, #-8]
    // 0x94af80: StoreField: r1->field_13 = r0
    //     0x94af80: stur            w0, [x1, #0x13]
    //     0x94af84: ldurb           w16, [x1, #-1]
    //     0x94af88: ldurb           w17, [x0, #-1]
    //     0x94af8c: and             x16, x17, x16, lsr #2
    //     0x94af90: tst             x16, HEAP, lsr #32
    //     0x94af94: b.eq            #0x94af9c
    //     0x94af98: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94af9c: LoadField: r0 = r1->field_b
    //     0x94af9c: ldur            w0, [x1, #0xb]
    // 0x94afa0: DecompressPointer r0
    //     0x94afa0: add             x0, x0, HEAP, lsl #32
    // 0x94afa4: cmp             w0, NULL
    // 0x94afa8: b.eq            #0x94b0c4
    // 0x94afac: LoadField: r2 = r1->field_1f
    //     0x94afac: ldur            w2, [x1, #0x1f]
    // 0x94afb0: DecompressPointer r2
    //     0x94afb0: add             x2, x2, HEAP, lsl #32
    // 0x94afb4: stur            x2, [fp, #-0x10]
    // 0x94afb8: r3 = 0
    //     0x94afb8: movz            x3, #0
    // 0x94afbc: stur            x3, [fp, #-0x18]
    // 0x94afc0: CheckStackOverflow
    //     0x94afc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94afc4: cmp             SP, x16
    //     0x94afc8: b.ls            #0x94b0c8
    // 0x94afcc: LoadField: r0 = r1->field_b
    //     0x94afcc: ldur            w0, [x1, #0xb]
    // 0x94afd0: DecompressPointer r0
    //     0x94afd0: add             x0, x0, HEAP, lsl #32
    // 0x94afd4: cmp             w0, NULL
    // 0x94afd8: b.eq            #0x94b0d0
    // 0x94afdc: LoadField: r4 = r0->field_b
    //     0x94afdc: ldur            w4, [x0, #0xb]
    // 0x94afe0: DecompressPointer r4
    //     0x94afe0: add             x4, x4, HEAP, lsl #32
    // 0x94afe4: r0 = LoadClassIdInstr(r4)
    //     0x94afe4: ldur            x0, [x4, #-1]
    //     0x94afe8: ubfx            x0, x0, #0xc, #0x14
    // 0x94afec: str             x4, [SP]
    // 0x94aff0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x94aff0: movz            x17, #0xc898
    //     0x94aff4: add             lr, x0, x17
    //     0x94aff8: ldr             lr, [x21, lr, lsl #3]
    //     0x94affc: blr             lr
    // 0x94b000: r1 = LoadInt32Instr(r0)
    //     0x94b000: sbfx            x1, x0, #1, #0x1f
    // 0x94b004: ldur            x0, [fp, #-0x18]
    // 0x94b008: cmp             x0, x1
    // 0x94b00c: b.ge            #0x94b0ac
    // 0x94b010: r1 = <bool>
    //     0x94b010: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x94b014: r0 = RxBool()
    //     0x94b014: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0x94b018: mov             x2, x0
    // 0x94b01c: r0 = Sentinel
    //     0x94b01c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94b020: stur            x2, [fp, #-0x20]
    // 0x94b024: StoreField: r2->field_13 = r0
    //     0x94b024: stur            w0, [x2, #0x13]
    // 0x94b028: r3 = true
    //     0x94b028: add             x3, NULL, #0x20  ; true
    // 0x94b02c: ArrayStore: r2[0] = r3  ; List_4
    //     0x94b02c: stur            w3, [x2, #0x17]
    // 0x94b030: mov             x1, x2
    // 0x94b034: r0 = RxNotifier()
    //     0x94b034: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x94b038: ldur            x3, [fp, #-0x20]
    // 0x94b03c: r4 = false
    //     0x94b03c: add             x4, NULL, #0x30  ; false
    // 0x94b040: StoreField: r3->field_13 = r4
    //     0x94b040: stur            w4, [x3, #0x13]
    // 0x94b044: ldur            x5, [fp, #-0x18]
    // 0x94b048: r0 = BoxInt64Instr(r5)
    //     0x94b048: sbfiz           x0, x5, #1, #0x1f
    //     0x94b04c: cmp             x5, x0, asr #1
    //     0x94b050: b.eq            #0x94b05c
    //     0x94b054: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94b058: stur            x5, [x0, #7]
    // 0x94b05c: ldur            x1, [fp, #-0x10]
    // 0x94b060: mov             x2, x0
    // 0x94b064: stur            x0, [fp, #-0x28]
    // 0x94b068: r0 = _hashCode()
    //     0x94b068: bl              #0x16f1c0c  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x94b06c: mov             x2, x0
    // 0x94b070: r0 = BoxInt64Instr(r2)
    //     0x94b070: sbfiz           x0, x2, #1, #0x1f
    //     0x94b074: cmp             x2, x0, asr #1
    //     0x94b078: b.eq            #0x94b084
    //     0x94b07c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94b080: stur            x2, [x0, #7]
    // 0x94b084: ldur            x1, [fp, #-0x10]
    // 0x94b088: ldur            x2, [fp, #-0x28]
    // 0x94b08c: ldur            x3, [fp, #-0x20]
    // 0x94b090: mov             x5, x0
    // 0x94b094: r0 = _set()
    //     0x94b094: bl              #0x16e32b4  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x94b098: ldur            x1, [fp, #-0x18]
    // 0x94b09c: add             x3, x1, #1
    // 0x94b0a0: ldur            x1, [fp, #-8]
    // 0x94b0a4: ldur            x2, [fp, #-0x10]
    // 0x94b0a8: b               #0x94afbc
    // 0x94b0ac: r0 = Null
    //     0x94b0ac: mov             x0, NULL
    // 0x94b0b0: LeaveFrame
    //     0x94b0b0: mov             SP, fp
    //     0x94b0b4: ldp             fp, lr, [SP], #0x10
    // 0x94b0b8: ret
    //     0x94b0b8: ret             
    // 0x94b0bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b0bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b0c0: b               #0x94af50
    // 0x94b0c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b0c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94b0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b0c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b0cc: b               #0x94afcc
    // 0x94b0d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b0d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int, RxBool) {
    // ** addr: 0xa5cd5c, size: 0x38
    // 0xa5cd5c: EnterFrame
    //     0xa5cd5c: stp             fp, lr, [SP, #-0x10]!
    //     0xa5cd60: mov             fp, SP
    // 0xa5cd64: CheckStackOverflow
    //     0xa5cd64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5cd68: cmp             SP, x16
    //     0xa5cd6c: b.ls            #0xa5cd8c
    // 0xa5cd70: ldr             x1, [fp, #0x10]
    // 0xa5cd74: r2 = false
    //     0xa5cd74: add             x2, NULL, #0x30  ; false
    // 0xa5cd78: r0 = value=()
    //     0xa5cd78: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa5cd7c: r0 = Null
    //     0xa5cd7c: mov             x0, NULL
    // 0xa5cd80: LeaveFrame
    //     0xa5cd80: mov             SP, fp
    //     0xa5cd84: ldp             fp, lr, [SP], #0x10
    // 0xa5cd88: ret
    //     0xa5cd88: ret             
    // 0xa5cd8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5cd8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5cd90: b               #0xa5cd70
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa5cd94, size: 0x8c
    // 0xa5cd94: EnterFrame
    //     0xa5cd94: stp             fp, lr, [SP, #-0x10]!
    //     0xa5cd98: mov             fp, SP
    // 0xa5cd9c: AllocStack(0x8)
    //     0xa5cd9c: sub             SP, SP, #8
    // 0xa5cda0: SetupParameters()
    //     0xa5cda0: ldr             x0, [fp, #0x10]
    //     0xa5cda4: ldur            w1, [x0, #0x17]
    //     0xa5cda8: add             x1, x1, HEAP, lsl #32
    // 0xa5cdac: CheckStackOverflow
    //     0xa5cdac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5cdb0: cmp             SP, x16
    //     0xa5cdb4: b.ls            #0xa5ce18
    // 0xa5cdb8: LoadField: r0 = r1->field_b
    //     0xa5cdb8: ldur            w0, [x1, #0xb]
    // 0xa5cdbc: DecompressPointer r0
    //     0xa5cdbc: add             x0, x0, HEAP, lsl #32
    // 0xa5cdc0: LoadField: r2 = r0->field_f
    //     0xa5cdc0: ldur            w2, [x0, #0xf]
    // 0xa5cdc4: DecompressPointer r2
    //     0xa5cdc4: add             x2, x2, HEAP, lsl #32
    // 0xa5cdc8: LoadField: r0 = r1->field_f
    //     0xa5cdc8: ldur            w0, [x1, #0xf]
    // 0xa5cdcc: DecompressPointer r0
    //     0xa5cdcc: add             x0, x0, HEAP, lsl #32
    // 0xa5cdd0: r1 = LoadInt32Instr(r0)
    //     0xa5cdd0: sbfx            x1, x0, #1, #0x1f
    //     0xa5cdd4: tbz             w0, #0, #0xa5cddc
    //     0xa5cdd8: ldur            x1, [x0, #7]
    // 0xa5cddc: ArrayStore: r2[0] = r1  ; List_8
    //     0xa5cddc: stur            x1, [x2, #0x17]
    // 0xa5cde0: LoadField: r0 = r2->field_1f
    //     0xa5cde0: ldur            w0, [x2, #0x1f]
    // 0xa5cde4: DecompressPointer r0
    //     0xa5cde4: add             x0, x0, HEAP, lsl #32
    // 0xa5cde8: stur            x0, [fp, #-8]
    // 0xa5cdec: r1 = Function '<anonymous closure>':.
    //     0xa5cdec: add             x1, PP, #0x52, lsl #12  ; [pp+0x526f0] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xa5cdf0: ldr             x1, [x1, #0x6f0]
    // 0xa5cdf4: r2 = Null
    //     0xa5cdf4: mov             x2, NULL
    // 0xa5cdf8: r0 = AllocateClosure()
    //     0xa5cdf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5cdfc: ldur            x1, [fp, #-8]
    // 0xa5ce00: mov             x2, x0
    // 0xa5ce04: r0 = forEach()
    //     0xa5ce04: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xa5ce08: r0 = Null
    //     0xa5ce08: mov             x0, NULL
    // 0xa5ce0c: LeaveFrame
    //     0xa5ce0c: mov             SP, fp
    //     0xa5ce10: ldp             fp, lr, [SP], #0x10
    // 0xa5ce14: ret
    //     0xa5ce14: ret             
    // 0xa5ce18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5ce18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5ce1c: b               #0xa5cdb8
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xa5ce20, size: 0x84
    // 0xa5ce20: EnterFrame
    //     0xa5ce20: stp             fp, lr, [SP, #-0x10]!
    //     0xa5ce24: mov             fp, SP
    // 0xa5ce28: AllocStack(0x10)
    //     0xa5ce28: sub             SP, SP, #0x10
    // 0xa5ce2c: SetupParameters()
    //     0xa5ce2c: ldr             x0, [fp, #0x18]
    //     0xa5ce30: ldur            w1, [x0, #0x17]
    //     0xa5ce34: add             x1, x1, HEAP, lsl #32
    //     0xa5ce38: stur            x1, [fp, #-8]
    // 0xa5ce3c: CheckStackOverflow
    //     0xa5ce3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa5ce40: cmp             SP, x16
    //     0xa5ce44: b.ls            #0xa5ce9c
    // 0xa5ce48: r1 = 1
    //     0xa5ce48: movz            x1, #0x1
    // 0xa5ce4c: r0 = AllocateContext()
    //     0xa5ce4c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa5ce50: mov             x1, x0
    // 0xa5ce54: ldur            x0, [fp, #-8]
    // 0xa5ce58: StoreField: r1->field_b = r0
    //     0xa5ce58: stur            w0, [x1, #0xb]
    // 0xa5ce5c: ldr             x2, [fp, #0x10]
    // 0xa5ce60: StoreField: r1->field_f = r2
    //     0xa5ce60: stur            w2, [x1, #0xf]
    // 0xa5ce64: LoadField: r3 = r0->field_f
    //     0xa5ce64: ldur            w3, [x0, #0xf]
    // 0xa5ce68: DecompressPointer r3
    //     0xa5ce68: add             x3, x3, HEAP, lsl #32
    // 0xa5ce6c: mov             x2, x1
    // 0xa5ce70: stur            x3, [fp, #-0x10]
    // 0xa5ce74: r1 = Function '<anonymous closure>':.
    //     0xa5ce74: add             x1, PP, #0x52, lsl #12  ; [pp+0x526e8] AnonymousClosure: (0xa5cd94), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xa5ce78: ldr             x1, [x1, #0x6e8]
    // 0xa5ce7c: r0 = AllocateClosure()
    //     0xa5ce7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5ce80: ldur            x1, [fp, #-0x10]
    // 0xa5ce84: mov             x2, x0
    // 0xa5ce88: r0 = setState()
    //     0xa5ce88: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa5ce8c: r0 = Null
    //     0xa5ce8c: mov             x0, NULL
    // 0xa5ce90: LeaveFrame
    //     0xa5ce90: mov             SP, fp
    //     0xa5ce94: ldp             fp, lr, [SP], #0x10
    // 0xa5ce98: ret
    //     0xa5ce98: ret             
    // 0xa5ce9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5ce9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa5cea0: b               #0xa5ce48
  }
  _ build(/* No info */) {
    // ** addr: 0xc06824, size: 0x914
    // 0xc06824: EnterFrame
    //     0xc06824: stp             fp, lr, [SP, #-0x10]!
    //     0xc06828: mov             fp, SP
    // 0xc0682c: AllocStack(0x78)
    //     0xc0682c: sub             SP, SP, #0x78
    // 0xc06830: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc06830: mov             x0, x1
    //     0xc06834: stur            x1, [fp, #-8]
    //     0xc06838: mov             x1, x2
    //     0xc0683c: stur            x2, [fp, #-0x10]
    // 0xc06840: CheckStackOverflow
    //     0xc06840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc06844: cmp             SP, x16
    //     0xc06848: b.ls            #0xc070f8
    // 0xc0684c: r1 = 1
    //     0xc0684c: movz            x1, #0x1
    // 0xc06850: r0 = AllocateContext()
    //     0xc06850: bl              #0x16f6108  ; AllocateContextStub
    // 0xc06854: mov             x3, x0
    // 0xc06858: ldur            x0, [fp, #-8]
    // 0xc0685c: stur            x3, [fp, #-0x20]
    // 0xc06860: StoreField: r3->field_f = r0
    //     0xc06860: stur            w0, [x3, #0xf]
    // 0xc06864: LoadField: r1 = r0->field_b
    //     0xc06864: ldur            w1, [x0, #0xb]
    // 0xc06868: DecompressPointer r1
    //     0xc06868: add             x1, x1, HEAP, lsl #32
    // 0xc0686c: cmp             w1, NULL
    // 0xc06870: b.eq            #0xc07100
    // 0xc06874: LoadField: r2 = r1->field_13
    //     0xc06874: ldur            w2, [x1, #0x13]
    // 0xc06878: DecompressPointer r2
    //     0xc06878: add             x2, x2, HEAP, lsl #32
    // 0xc0687c: LoadField: r1 = r2->field_7
    //     0xc0687c: ldur            w1, [x2, #7]
    // 0xc06880: DecompressPointer r1
    //     0xc06880: add             x1, x1, HEAP, lsl #32
    // 0xc06884: cmp             w1, NULL
    // 0xc06888: b.ne            #0xc06894
    // 0xc0688c: r1 = Instance_TitleAlignment
    //     0xc0688c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc06890: ldr             x1, [x1, #0x518]
    // 0xc06894: r16 = Instance_TitleAlignment
    //     0xc06894: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xc06898: ldr             x16, [x16, #0x520]
    // 0xc0689c: cmp             w1, w16
    // 0xc068a0: b.ne            #0xc068b0
    // 0xc068a4: r4 = Instance_CrossAxisAlignment
    //     0xc068a4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xc068a8: ldr             x4, [x4, #0xc68]
    // 0xc068ac: b               #0xc068d4
    // 0xc068b0: r16 = Instance_TitleAlignment
    //     0xc068b0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc068b4: ldr             x16, [x16, #0x518]
    // 0xc068b8: cmp             w1, w16
    // 0xc068bc: b.ne            #0xc068cc
    // 0xc068c0: r4 = Instance_CrossAxisAlignment
    //     0xc068c0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc068c4: ldr             x4, [x4, #0x890]
    // 0xc068c8: b               #0xc068d4
    // 0xc068cc: r4 = Instance_CrossAxisAlignment
    //     0xc068cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc068d0: ldr             x4, [x4, #0xa18]
    // 0xc068d4: stur            x4, [fp, #-0x18]
    // 0xc068d8: r1 = <Widget>
    //     0xc068d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc068dc: r2 = 0
    //     0xc068dc: movz            x2, #0
    // 0xc068e0: r0 = _GrowableList()
    //     0xc068e0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc068e4: mov             x2, x0
    // 0xc068e8: ldur            x1, [fp, #-8]
    // 0xc068ec: stur            x2, [fp, #-0x28]
    // 0xc068f0: LoadField: r0 = r1->field_b
    //     0xc068f0: ldur            w0, [x1, #0xb]
    // 0xc068f4: DecompressPointer r0
    //     0xc068f4: add             x0, x0, HEAP, lsl #32
    // 0xc068f8: cmp             w0, NULL
    // 0xc068fc: b.eq            #0xc07104
    // 0xc06900: LoadField: r3 = r0->field_f
    //     0xc06900: ldur            w3, [x0, #0xf]
    // 0xc06904: DecompressPointer r3
    //     0xc06904: add             x3, x3, HEAP, lsl #32
    // 0xc06908: LoadField: r0 = r3->field_7
    //     0xc06908: ldur            w0, [x3, #7]
    // 0xc0690c: cbz             w0, #0xc06a98
    // 0xc06910: r0 = LoadClassIdInstr(r3)
    //     0xc06910: ldur            x0, [x3, #-1]
    //     0xc06914: ubfx            x0, x0, #0xc, #0x14
    // 0xc06918: str             x3, [SP]
    // 0xc0691c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc0691c: sub             lr, x0, #1, lsl #12
    //     0xc06920: ldr             lr, [x21, lr, lsl #3]
    //     0xc06924: blr             lr
    // 0xc06928: mov             x2, x0
    // 0xc0692c: ldur            x0, [fp, #-8]
    // 0xc06930: stur            x2, [fp, #-0x38]
    // 0xc06934: LoadField: r1 = r0->field_b
    //     0xc06934: ldur            w1, [x0, #0xb]
    // 0xc06938: DecompressPointer r1
    //     0xc06938: add             x1, x1, HEAP, lsl #32
    // 0xc0693c: cmp             w1, NULL
    // 0xc06940: b.eq            #0xc07108
    // 0xc06944: LoadField: r3 = r1->field_13
    //     0xc06944: ldur            w3, [x1, #0x13]
    // 0xc06948: DecompressPointer r3
    //     0xc06948: add             x3, x3, HEAP, lsl #32
    // 0xc0694c: LoadField: r1 = r3->field_7
    //     0xc0694c: ldur            w1, [x3, #7]
    // 0xc06950: DecompressPointer r1
    //     0xc06950: add             x1, x1, HEAP, lsl #32
    // 0xc06954: cmp             w1, NULL
    // 0xc06958: b.ne            #0xc06964
    // 0xc0695c: r1 = Instance_TitleAlignment
    //     0xc0695c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc06960: ldr             x1, [x1, #0x518]
    // 0xc06964: r16 = Instance_TitleAlignment
    //     0xc06964: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xc06968: ldr             x16, [x16, #0x520]
    // 0xc0696c: cmp             w1, w16
    // 0xc06970: b.ne            #0xc0697c
    // 0xc06974: r4 = Instance_TextAlign
    //     0xc06974: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xc06978: b               #0xc06998
    // 0xc0697c: r16 = Instance_TitleAlignment
    //     0xc0697c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xc06980: ldr             x16, [x16, #0x518]
    // 0xc06984: cmp             w1, w16
    // 0xc06988: b.ne            #0xc06994
    // 0xc0698c: r4 = Instance_TextAlign
    //     0xc0698c: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc06990: b               #0xc06998
    // 0xc06994: r4 = Instance_TextAlign
    //     0xc06994: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xc06998: ldur            x3, [fp, #-0x28]
    // 0xc0699c: ldur            x1, [fp, #-0x10]
    // 0xc069a0: stur            x4, [fp, #-0x30]
    // 0xc069a4: r0 = of()
    //     0xc069a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc069a8: LoadField: r1 = r0->field_87
    //     0xc069a8: ldur            w1, [x0, #0x87]
    // 0xc069ac: DecompressPointer r1
    //     0xc069ac: add             x1, x1, HEAP, lsl #32
    // 0xc069b0: LoadField: r0 = r1->field_27
    //     0xc069b0: ldur            w0, [x1, #0x27]
    // 0xc069b4: DecompressPointer r0
    //     0xc069b4: add             x0, x0, HEAP, lsl #32
    // 0xc069b8: r16 = 21.000000
    //     0xc069b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xc069bc: ldr             x16, [x16, #0x9b0]
    // 0xc069c0: r30 = Instance_Color
    //     0xc069c0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc069c4: stp             lr, x16, [SP]
    // 0xc069c8: mov             x1, x0
    // 0xc069cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc069cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc069d0: ldr             x4, [x4, #0xaa0]
    // 0xc069d4: r0 = copyWith()
    //     0xc069d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc069d8: stur            x0, [fp, #-0x40]
    // 0xc069dc: r0 = Text()
    //     0xc069dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc069e0: mov             x1, x0
    // 0xc069e4: ldur            x0, [fp, #-0x38]
    // 0xc069e8: stur            x1, [fp, #-0x48]
    // 0xc069ec: StoreField: r1->field_b = r0
    //     0xc069ec: stur            w0, [x1, #0xb]
    // 0xc069f0: ldur            x0, [fp, #-0x40]
    // 0xc069f4: StoreField: r1->field_13 = r0
    //     0xc069f4: stur            w0, [x1, #0x13]
    // 0xc069f8: ldur            x0, [fp, #-0x30]
    // 0xc069fc: StoreField: r1->field_1b = r0
    //     0xc069fc: stur            w0, [x1, #0x1b]
    // 0xc06a00: r0 = Padding()
    //     0xc06a00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc06a04: mov             x2, x0
    // 0xc06a08: r0 = Instance_EdgeInsets
    //     0xc06a08: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xc06a0c: ldr             x0, [x0, #0x4c0]
    // 0xc06a10: stur            x2, [fp, #-0x30]
    // 0xc06a14: StoreField: r2->field_f = r0
    //     0xc06a14: stur            w0, [x2, #0xf]
    // 0xc06a18: ldur            x0, [fp, #-0x48]
    // 0xc06a1c: StoreField: r2->field_b = r0
    //     0xc06a1c: stur            w0, [x2, #0xb]
    // 0xc06a20: ldur            x0, [fp, #-0x28]
    // 0xc06a24: LoadField: r1 = r0->field_b
    //     0xc06a24: ldur            w1, [x0, #0xb]
    // 0xc06a28: LoadField: r3 = r0->field_f
    //     0xc06a28: ldur            w3, [x0, #0xf]
    // 0xc06a2c: DecompressPointer r3
    //     0xc06a2c: add             x3, x3, HEAP, lsl #32
    // 0xc06a30: LoadField: r4 = r3->field_b
    //     0xc06a30: ldur            w4, [x3, #0xb]
    // 0xc06a34: r3 = LoadInt32Instr(r1)
    //     0xc06a34: sbfx            x3, x1, #1, #0x1f
    // 0xc06a38: stur            x3, [fp, #-0x50]
    // 0xc06a3c: r1 = LoadInt32Instr(r4)
    //     0xc06a3c: sbfx            x1, x4, #1, #0x1f
    // 0xc06a40: cmp             x3, x1
    // 0xc06a44: b.ne            #0xc06a50
    // 0xc06a48: mov             x1, x0
    // 0xc06a4c: r0 = _growToNextCapacity()
    //     0xc06a4c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc06a50: ldur            x2, [fp, #-0x28]
    // 0xc06a54: ldur            x3, [fp, #-0x50]
    // 0xc06a58: add             x0, x3, #1
    // 0xc06a5c: lsl             x1, x0, #1
    // 0xc06a60: StoreField: r2->field_b = r1
    //     0xc06a60: stur            w1, [x2, #0xb]
    // 0xc06a64: LoadField: r1 = r2->field_f
    //     0xc06a64: ldur            w1, [x2, #0xf]
    // 0xc06a68: DecompressPointer r1
    //     0xc06a68: add             x1, x1, HEAP, lsl #32
    // 0xc06a6c: ldur            x0, [fp, #-0x30]
    // 0xc06a70: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc06a70: add             x25, x1, x3, lsl #2
    //     0xc06a74: add             x25, x25, #0xf
    //     0xc06a78: str             w0, [x25]
    //     0xc06a7c: tbz             w0, #0, #0xc06a98
    //     0xc06a80: ldurb           w16, [x1, #-1]
    //     0xc06a84: ldurb           w17, [x0, #-1]
    //     0xc06a88: and             x16, x17, x16, lsr #2
    //     0xc06a8c: tst             x16, HEAP, lsr #32
    //     0xc06a90: b.eq            #0xc06a98
    //     0xc06a94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc06a98: ldur            x1, [fp, #-8]
    // 0xc06a9c: LoadField: r0 = r1->field_b
    //     0xc06a9c: ldur            w0, [x1, #0xb]
    // 0xc06aa0: DecompressPointer r0
    //     0xc06aa0: add             x0, x0, HEAP, lsl #32
    // 0xc06aa4: cmp             w0, NULL
    // 0xc06aa8: b.eq            #0xc0710c
    // 0xc06aac: LoadField: r3 = r0->field_2b
    //     0xc06aac: ldur            w3, [x0, #0x2b]
    // 0xc06ab0: DecompressPointer r3
    //     0xc06ab0: add             x3, x3, HEAP, lsl #32
    // 0xc06ab4: cmp             w3, NULL
    // 0xc06ab8: b.ne            #0xc06ac4
    // 0xc06abc: r0 = Null
    //     0xc06abc: mov             x0, NULL
    // 0xc06ac0: b               #0xc06af0
    // 0xc06ac4: LoadField: r0 = r3->field_7
    //     0xc06ac4: ldur            w0, [x3, #7]
    // 0xc06ac8: DecompressPointer r0
    //     0xc06ac8: add             x0, x0, HEAP, lsl #32
    // 0xc06acc: cmp             w0, NULL
    // 0xc06ad0: b.ne            #0xc06adc
    // 0xc06ad4: r0 = Null
    //     0xc06ad4: mov             x0, NULL
    // 0xc06ad8: b               #0xc06af0
    // 0xc06adc: LoadField: r4 = r0->field_7
    //     0xc06adc: ldur            w4, [x0, #7]
    // 0xc06ae0: cbnz            w4, #0xc06aec
    // 0xc06ae4: r0 = false
    //     0xc06ae4: add             x0, NULL, #0x30  ; false
    // 0xc06ae8: b               #0xc06af0
    // 0xc06aec: r0 = true
    //     0xc06aec: add             x0, NULL, #0x20  ; true
    // 0xc06af0: cmp             w0, NULL
    // 0xc06af4: b.ne            #0xc06afc
    // 0xc06af8: r0 = false
    //     0xc06af8: add             x0, NULL, #0x30  ; false
    // 0xc06afc: stur            x0, [fp, #-0x30]
    // 0xc06b00: cmp             w3, NULL
    // 0xc06b04: b.ne            #0xc06b10
    // 0xc06b08: r3 = Null
    //     0xc06b08: mov             x3, NULL
    // 0xc06b0c: b               #0xc06b1c
    // 0xc06b10: LoadField: r4 = r3->field_7
    //     0xc06b10: ldur            w4, [x3, #7]
    // 0xc06b14: DecompressPointer r4
    //     0xc06b14: add             x4, x4, HEAP, lsl #32
    // 0xc06b18: mov             x3, x4
    // 0xc06b1c: str             x3, [SP]
    // 0xc06b20: r0 = _interpolateSingle()
    //     0xc06b20: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xc06b24: ldur            x1, [fp, #-0x10]
    // 0xc06b28: stur            x0, [fp, #-0x38]
    // 0xc06b2c: r0 = of()
    //     0xc06b2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc06b30: LoadField: r1 = r0->field_87
    //     0xc06b30: ldur            w1, [x0, #0x87]
    // 0xc06b34: DecompressPointer r1
    //     0xc06b34: add             x1, x1, HEAP, lsl #32
    // 0xc06b38: LoadField: r0 = r1->field_2b
    //     0xc06b38: ldur            w0, [x1, #0x2b]
    // 0xc06b3c: DecompressPointer r0
    //     0xc06b3c: add             x0, x0, HEAP, lsl #32
    // 0xc06b40: stur            x0, [fp, #-0x40]
    // 0xc06b44: r1 = Instance_Color
    //     0xc06b44: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc06b48: d0 = 0.700000
    //     0xc06b48: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc06b4c: ldr             d0, [x17, #0xf48]
    // 0xc06b50: r0 = withOpacity()
    //     0xc06b50: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc06b54: r16 = 12.000000
    //     0xc06b54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc06b58: ldr             x16, [x16, #0x9e8]
    // 0xc06b5c: stp             x0, x16, [SP, #8]
    // 0xc06b60: r16 = Instance_TextDecoration
    //     0xc06b60: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xc06b64: ldr             x16, [x16, #0x10]
    // 0xc06b68: str             x16, [SP]
    // 0xc06b6c: ldur            x1, [fp, #-0x40]
    // 0xc06b70: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xc06b70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xc06b74: ldr             x4, [x4, #0xe38]
    // 0xc06b78: r0 = copyWith()
    //     0xc06b78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc06b7c: stur            x0, [fp, #-0x40]
    // 0xc06b80: r0 = Text()
    //     0xc06b80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc06b84: mov             x1, x0
    // 0xc06b88: ldur            x0, [fp, #-0x38]
    // 0xc06b8c: stur            x1, [fp, #-0x48]
    // 0xc06b90: StoreField: r1->field_b = r0
    //     0xc06b90: stur            w0, [x1, #0xb]
    // 0xc06b94: ldur            x0, [fp, #-0x40]
    // 0xc06b98: StoreField: r1->field_13 = r0
    //     0xc06b98: stur            w0, [x1, #0x13]
    // 0xc06b9c: r0 = Visibility()
    //     0xc06b9c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc06ba0: mov             x1, x0
    // 0xc06ba4: ldur            x0, [fp, #-0x48]
    // 0xc06ba8: stur            x1, [fp, #-0x38]
    // 0xc06bac: StoreField: r1->field_b = r0
    //     0xc06bac: stur            w0, [x1, #0xb]
    // 0xc06bb0: r0 = Instance_SizedBox
    //     0xc06bb0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc06bb4: StoreField: r1->field_f = r0
    //     0xc06bb4: stur            w0, [x1, #0xf]
    // 0xc06bb8: ldur            x0, [fp, #-0x30]
    // 0xc06bbc: StoreField: r1->field_13 = r0
    //     0xc06bbc: stur            w0, [x1, #0x13]
    // 0xc06bc0: r0 = false
    //     0xc06bc0: add             x0, NULL, #0x30  ; false
    // 0xc06bc4: ArrayStore: r1[0] = r0  ; List_4
    //     0xc06bc4: stur            w0, [x1, #0x17]
    // 0xc06bc8: StoreField: r1->field_1b = r0
    //     0xc06bc8: stur            w0, [x1, #0x1b]
    // 0xc06bcc: StoreField: r1->field_1f = r0
    //     0xc06bcc: stur            w0, [x1, #0x1f]
    // 0xc06bd0: StoreField: r1->field_23 = r0
    //     0xc06bd0: stur            w0, [x1, #0x23]
    // 0xc06bd4: StoreField: r1->field_27 = r0
    //     0xc06bd4: stur            w0, [x1, #0x27]
    // 0xc06bd8: StoreField: r1->field_2b = r0
    //     0xc06bd8: stur            w0, [x1, #0x2b]
    // 0xc06bdc: r0 = InkWell()
    //     0xc06bdc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc06be0: mov             x3, x0
    // 0xc06be4: ldur            x0, [fp, #-0x38]
    // 0xc06be8: stur            x3, [fp, #-0x30]
    // 0xc06bec: StoreField: r3->field_b = r0
    //     0xc06bec: stur            w0, [x3, #0xb]
    // 0xc06bf0: ldur            x2, [fp, #-0x20]
    // 0xc06bf4: r1 = Function '<anonymous closure>':.
    //     0xc06bf4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52658] AnonymousClosure: (0xc08048), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xc06bf8: ldr             x1, [x1, #0x658]
    // 0xc06bfc: r0 = AllocateClosure()
    //     0xc06bfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc06c00: mov             x1, x0
    // 0xc06c04: ldur            x0, [fp, #-0x30]
    // 0xc06c08: StoreField: r0->field_f = r1
    //     0xc06c08: stur            w1, [x0, #0xf]
    // 0xc06c0c: r1 = true
    //     0xc06c0c: add             x1, NULL, #0x20  ; true
    // 0xc06c10: StoreField: r0->field_43 = r1
    //     0xc06c10: stur            w1, [x0, #0x43]
    // 0xc06c14: r2 = Instance_BoxShape
    //     0xc06c14: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc06c18: ldr             x2, [x2, #0x80]
    // 0xc06c1c: StoreField: r0->field_47 = r2
    //     0xc06c1c: stur            w2, [x0, #0x47]
    // 0xc06c20: StoreField: r0->field_6f = r1
    //     0xc06c20: stur            w1, [x0, #0x6f]
    // 0xc06c24: r2 = false
    //     0xc06c24: add             x2, NULL, #0x30  ; false
    // 0xc06c28: StoreField: r0->field_73 = r2
    //     0xc06c28: stur            w2, [x0, #0x73]
    // 0xc06c2c: StoreField: r0->field_83 = r1
    //     0xc06c2c: stur            w1, [x0, #0x83]
    // 0xc06c30: StoreField: r0->field_7b = r2
    //     0xc06c30: stur            w2, [x0, #0x7b]
    // 0xc06c34: ldur            x2, [fp, #-0x28]
    // 0xc06c38: LoadField: r1 = r2->field_b
    //     0xc06c38: ldur            w1, [x2, #0xb]
    // 0xc06c3c: LoadField: r3 = r2->field_f
    //     0xc06c3c: ldur            w3, [x2, #0xf]
    // 0xc06c40: DecompressPointer r3
    //     0xc06c40: add             x3, x3, HEAP, lsl #32
    // 0xc06c44: LoadField: r4 = r3->field_b
    //     0xc06c44: ldur            w4, [x3, #0xb]
    // 0xc06c48: r3 = LoadInt32Instr(r1)
    //     0xc06c48: sbfx            x3, x1, #1, #0x1f
    // 0xc06c4c: stur            x3, [fp, #-0x50]
    // 0xc06c50: r1 = LoadInt32Instr(r4)
    //     0xc06c50: sbfx            x1, x4, #1, #0x1f
    // 0xc06c54: cmp             x3, x1
    // 0xc06c58: b.ne            #0xc06c64
    // 0xc06c5c: mov             x1, x2
    // 0xc06c60: r0 = _growToNextCapacity()
    //     0xc06c60: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc06c64: ldur            x4, [fp, #-8]
    // 0xc06c68: ldur            x3, [fp, #-0x28]
    // 0xc06c6c: ldur            x2, [fp, #-0x50]
    // 0xc06c70: add             x0, x2, #1
    // 0xc06c74: lsl             x1, x0, #1
    // 0xc06c78: StoreField: r3->field_b = r1
    //     0xc06c78: stur            w1, [x3, #0xb]
    // 0xc06c7c: LoadField: r1 = r3->field_f
    //     0xc06c7c: ldur            w1, [x3, #0xf]
    // 0xc06c80: DecompressPointer r1
    //     0xc06c80: add             x1, x1, HEAP, lsl #32
    // 0xc06c84: ldur            x0, [fp, #-0x30]
    // 0xc06c88: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc06c88: add             x25, x1, x2, lsl #2
    //     0xc06c8c: add             x25, x25, #0xf
    //     0xc06c90: str             w0, [x25]
    //     0xc06c94: tbz             w0, #0, #0xc06cb0
    //     0xc06c98: ldurb           w16, [x1, #-1]
    //     0xc06c9c: ldurb           w17, [x0, #-1]
    //     0xc06ca0: and             x16, x17, x16, lsr #2
    //     0xc06ca4: tst             x16, HEAP, lsr #32
    //     0xc06ca8: b.eq            #0xc06cb0
    //     0xc06cac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc06cb0: ArrayLoad: r2 = r4[0]  ; List_8
    //     0xc06cb0: ldur            x2, [x4, #0x17]
    // 0xc06cb4: mov             x1, x4
    // 0xc06cb8: r0 = _calculateCardHeight()
    //     0xc06cb8: bl              #0xa87490  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_calculateCardHeight
    // 0xc06cbc: ldur            x1, [fp, #-8]
    // 0xc06cc0: stur            d0, [fp, #-0x60]
    // 0xc06cc4: LoadField: r0 = r1->field_b
    //     0xc06cc4: ldur            w0, [x1, #0xb]
    // 0xc06cc8: DecompressPointer r0
    //     0xc06cc8: add             x0, x0, HEAP, lsl #32
    // 0xc06ccc: cmp             w0, NULL
    // 0xc06cd0: b.eq            #0xc07110
    // 0xc06cd4: LoadField: r2 = r0->field_b
    //     0xc06cd4: ldur            w2, [x0, #0xb]
    // 0xc06cd8: DecompressPointer r2
    //     0xc06cd8: add             x2, x2, HEAP, lsl #32
    // 0xc06cdc: r0 = LoadClassIdInstr(r2)
    //     0xc06cdc: ldur            x0, [x2, #-1]
    //     0xc06ce0: ubfx            x0, x0, #0xc, #0x14
    // 0xc06ce4: str             x2, [SP]
    // 0xc06ce8: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc06ce8: movz            x17, #0xc898
    //     0xc06cec: add             lr, x0, x17
    //     0xc06cf0: ldr             lr, [x21, lr, lsl #3]
    //     0xc06cf4: blr             lr
    // 0xc06cf8: mov             x3, x0
    // 0xc06cfc: ldur            x0, [fp, #-8]
    // 0xc06d00: stur            x3, [fp, #-0x38]
    // 0xc06d04: LoadField: r4 = r0->field_13
    //     0xc06d04: ldur            w4, [x0, #0x13]
    // 0xc06d08: DecompressPointer r4
    //     0xc06d08: add             x4, x4, HEAP, lsl #32
    // 0xc06d0c: r16 = Sentinel
    //     0xc06d0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc06d10: cmp             w4, w16
    // 0xc06d14: b.eq            #0xc07114
    // 0xc06d18: ldur            x2, [fp, #-0x20]
    // 0xc06d1c: stur            x4, [fp, #-0x30]
    // 0xc06d20: r1 = Function '<anonymous closure>':.
    //     0xc06d20: add             x1, PP, #0x52, lsl #12  ; [pp+0x52660] AnonymousClosure: (0xa5ce20), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xc06d24: ldr             x1, [x1, #0x660]
    // 0xc06d28: r0 = AllocateClosure()
    //     0xc06d28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc06d2c: ldur            x2, [fp, #-0x20]
    // 0xc06d30: r1 = Function '<anonymous closure>':.
    //     0xc06d30: add             x1, PP, #0x52, lsl #12  ; [pp+0x52668] AnonymousClosure: (0xc07138), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xc06d34: ldr             x1, [x1, #0x668]
    // 0xc06d38: stur            x0, [fp, #-0x20]
    // 0xc06d3c: r0 = AllocateClosure()
    //     0xc06d3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc06d40: stur            x0, [fp, #-0x40]
    // 0xc06d44: r0 = PageView()
    //     0xc06d44: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xc06d48: stur            x0, [fp, #-0x48]
    // 0xc06d4c: r16 = Instance_BouncingScrollPhysics
    //     0xc06d4c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xc06d50: ldr             x16, [x16, #0x890]
    // 0xc06d54: ldur            lr, [fp, #-0x30]
    // 0xc06d58: stp             lr, x16, [SP]
    // 0xc06d5c: mov             x1, x0
    // 0xc06d60: ldur            x2, [fp, #-0x40]
    // 0xc06d64: ldur            x3, [fp, #-0x38]
    // 0xc06d68: ldur            x5, [fp, #-0x20]
    // 0xc06d6c: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xc06d6c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xc06d70: ldr             x4, [x4, #0xe40]
    // 0xc06d74: r0 = PageView.builder()
    //     0xc06d74: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xc06d78: ldur            d0, [fp, #-0x60]
    // 0xc06d7c: r0 = inline_Allocate_Double()
    //     0xc06d7c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc06d80: add             x0, x0, #0x10
    //     0xc06d84: cmp             x1, x0
    //     0xc06d88: b.ls            #0xc07120
    //     0xc06d8c: str             x0, [THR, #0x50]  ; THR::top
    //     0xc06d90: sub             x0, x0, #0xf
    //     0xc06d94: movz            x1, #0xe15c
    //     0xc06d98: movk            x1, #0x3, lsl #16
    //     0xc06d9c: stur            x1, [x0, #-1]
    // 0xc06da0: StoreField: r0->field_7 = d0
    //     0xc06da0: stur            d0, [x0, #7]
    // 0xc06da4: stur            x0, [fp, #-0x20]
    // 0xc06da8: r0 = SizedBox()
    //     0xc06da8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc06dac: mov             x1, x0
    // 0xc06db0: ldur            x0, [fp, #-0x20]
    // 0xc06db4: stur            x1, [fp, #-0x30]
    // 0xc06db8: StoreField: r1->field_13 = r0
    //     0xc06db8: stur            w0, [x1, #0x13]
    // 0xc06dbc: ldur            x0, [fp, #-0x48]
    // 0xc06dc0: StoreField: r1->field_b = r0
    //     0xc06dc0: stur            w0, [x1, #0xb]
    // 0xc06dc4: r0 = Padding()
    //     0xc06dc4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc06dc8: mov             x2, x0
    // 0xc06dcc: r0 = Instance_EdgeInsets
    //     0xc06dcc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xc06dd0: ldr             x0, [x0, #0x100]
    // 0xc06dd4: stur            x2, [fp, #-0x20]
    // 0xc06dd8: StoreField: r2->field_f = r0
    //     0xc06dd8: stur            w0, [x2, #0xf]
    // 0xc06ddc: ldur            x0, [fp, #-0x30]
    // 0xc06de0: StoreField: r2->field_b = r0
    //     0xc06de0: stur            w0, [x2, #0xb]
    // 0xc06de4: ldur            x0, [fp, #-0x28]
    // 0xc06de8: LoadField: r1 = r0->field_b
    //     0xc06de8: ldur            w1, [x0, #0xb]
    // 0xc06dec: LoadField: r3 = r0->field_f
    //     0xc06dec: ldur            w3, [x0, #0xf]
    // 0xc06df0: DecompressPointer r3
    //     0xc06df0: add             x3, x3, HEAP, lsl #32
    // 0xc06df4: LoadField: r4 = r3->field_b
    //     0xc06df4: ldur            w4, [x3, #0xb]
    // 0xc06df8: r3 = LoadInt32Instr(r1)
    //     0xc06df8: sbfx            x3, x1, #1, #0x1f
    // 0xc06dfc: stur            x3, [fp, #-0x50]
    // 0xc06e00: r1 = LoadInt32Instr(r4)
    //     0xc06e00: sbfx            x1, x4, #1, #0x1f
    // 0xc06e04: cmp             x3, x1
    // 0xc06e08: b.ne            #0xc06e14
    // 0xc06e0c: mov             x1, x0
    // 0xc06e10: r0 = _growToNextCapacity()
    //     0xc06e10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc06e14: ldur            x4, [fp, #-8]
    // 0xc06e18: ldur            x2, [fp, #-0x28]
    // 0xc06e1c: ldur            x3, [fp, #-0x50]
    // 0xc06e20: add             x0, x3, #1
    // 0xc06e24: lsl             x1, x0, #1
    // 0xc06e28: StoreField: r2->field_b = r1
    //     0xc06e28: stur            w1, [x2, #0xb]
    // 0xc06e2c: LoadField: r1 = r2->field_f
    //     0xc06e2c: ldur            w1, [x2, #0xf]
    // 0xc06e30: DecompressPointer r1
    //     0xc06e30: add             x1, x1, HEAP, lsl #32
    // 0xc06e34: ldur            x0, [fp, #-0x20]
    // 0xc06e38: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc06e38: add             x25, x1, x3, lsl #2
    //     0xc06e3c: add             x25, x25, #0xf
    //     0xc06e40: str             w0, [x25]
    //     0xc06e44: tbz             w0, #0, #0xc06e60
    //     0xc06e48: ldurb           w16, [x1, #-1]
    //     0xc06e4c: ldurb           w17, [x0, #-1]
    //     0xc06e50: and             x16, x17, x16, lsr #2
    //     0xc06e54: tst             x16, HEAP, lsr #32
    //     0xc06e58: b.eq            #0xc06e60
    //     0xc06e5c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc06e60: LoadField: r0 = r4->field_b
    //     0xc06e60: ldur            w0, [x4, #0xb]
    // 0xc06e64: DecompressPointer r0
    //     0xc06e64: add             x0, x0, HEAP, lsl #32
    // 0xc06e68: cmp             w0, NULL
    // 0xc06e6c: b.eq            #0xc07130
    // 0xc06e70: LoadField: r1 = r0->field_b
    //     0xc06e70: ldur            w1, [x0, #0xb]
    // 0xc06e74: DecompressPointer r1
    //     0xc06e74: add             x1, x1, HEAP, lsl #32
    // 0xc06e78: r0 = LoadClassIdInstr(r1)
    //     0xc06e78: ldur            x0, [x1, #-1]
    //     0xc06e7c: ubfx            x0, x0, #0xc, #0x14
    // 0xc06e80: str             x1, [SP]
    // 0xc06e84: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc06e84: movz            x17, #0xc898
    //     0xc06e88: add             lr, x0, x17
    //     0xc06e8c: ldr             lr, [x21, lr, lsl #3]
    //     0xc06e90: blr             lr
    // 0xc06e94: r1 = LoadInt32Instr(r0)
    //     0xc06e94: sbfx            x1, x0, #1, #0x1f
    // 0xc06e98: cmp             x1, #1
    // 0xc06e9c: b.le            #0xc07074
    // 0xc06ea0: ldur            x2, [fp, #-8]
    // 0xc06ea4: ldur            x1, [fp, #-0x28]
    // 0xc06ea8: LoadField: r0 = r2->field_b
    //     0xc06ea8: ldur            w0, [x2, #0xb]
    // 0xc06eac: DecompressPointer r0
    //     0xc06eac: add             x0, x0, HEAP, lsl #32
    // 0xc06eb0: cmp             w0, NULL
    // 0xc06eb4: b.eq            #0xc07134
    // 0xc06eb8: LoadField: r3 = r0->field_b
    //     0xc06eb8: ldur            w3, [x0, #0xb]
    // 0xc06ebc: DecompressPointer r3
    //     0xc06ebc: add             x3, x3, HEAP, lsl #32
    // 0xc06ec0: r0 = LoadClassIdInstr(r3)
    //     0xc06ec0: ldur            x0, [x3, #-1]
    //     0xc06ec4: ubfx            x0, x0, #0xc, #0x14
    // 0xc06ec8: str             x3, [SP]
    // 0xc06ecc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xc06ecc: movz            x17, #0xc898
    //     0xc06ed0: add             lr, x0, x17
    //     0xc06ed4: ldr             lr, [x21, lr, lsl #3]
    //     0xc06ed8: blr             lr
    // 0xc06edc: mov             x2, x0
    // 0xc06ee0: ldur            x0, [fp, #-8]
    // 0xc06ee4: stur            x2, [fp, #-0x20]
    // 0xc06ee8: ArrayLoad: r3 = r0[0]  ; List_8
    //     0xc06ee8: ldur            x3, [x0, #0x17]
    // 0xc06eec: ldur            x1, [fp, #-0x10]
    // 0xc06ef0: stur            x3, [fp, #-0x50]
    // 0xc06ef4: r0 = of()
    //     0xc06ef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc06ef8: LoadField: r1 = r0->field_5b
    //     0xc06ef8: ldur            w1, [x0, #0x5b]
    // 0xc06efc: DecompressPointer r1
    //     0xc06efc: add             x1, x1, HEAP, lsl #32
    // 0xc06f00: ldur            x0, [fp, #-0x20]
    // 0xc06f04: stur            x1, [fp, #-8]
    // 0xc06f08: r2 = LoadInt32Instr(r0)
    //     0xc06f08: sbfx            x2, x0, #1, #0x1f
    // 0xc06f0c: stur            x2, [fp, #-0x58]
    // 0xc06f10: r0 = CarouselIndicator()
    //     0xc06f10: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xc06f14: mov             x3, x0
    // 0xc06f18: ldur            x0, [fp, #-0x58]
    // 0xc06f1c: stur            x3, [fp, #-0x10]
    // 0xc06f20: StoreField: r3->field_b = r0
    //     0xc06f20: stur            x0, [x3, #0xb]
    // 0xc06f24: ldur            x0, [fp, #-0x50]
    // 0xc06f28: StoreField: r3->field_13 = r0
    //     0xc06f28: stur            x0, [x3, #0x13]
    // 0xc06f2c: ldur            x0, [fp, #-8]
    // 0xc06f30: StoreField: r3->field_1b = r0
    //     0xc06f30: stur            w0, [x3, #0x1b]
    // 0xc06f34: r0 = Instance_Color
    //     0xc06f34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xc06f38: ldr             x0, [x0, #0x90]
    // 0xc06f3c: StoreField: r3->field_1f = r0
    //     0xc06f3c: stur            w0, [x3, #0x1f]
    // 0xc06f40: r1 = Null
    //     0xc06f40: mov             x1, NULL
    // 0xc06f44: r2 = 2
    //     0xc06f44: movz            x2, #0x2
    // 0xc06f48: r0 = AllocateArray()
    //     0xc06f48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc06f4c: mov             x2, x0
    // 0xc06f50: ldur            x0, [fp, #-0x10]
    // 0xc06f54: stur            x2, [fp, #-8]
    // 0xc06f58: StoreField: r2->field_f = r0
    //     0xc06f58: stur            w0, [x2, #0xf]
    // 0xc06f5c: r1 = <Widget>
    //     0xc06f5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc06f60: r0 = AllocateGrowableArray()
    //     0xc06f60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc06f64: mov             x1, x0
    // 0xc06f68: ldur            x0, [fp, #-8]
    // 0xc06f6c: stur            x1, [fp, #-0x10]
    // 0xc06f70: StoreField: r1->field_f = r0
    //     0xc06f70: stur            w0, [x1, #0xf]
    // 0xc06f74: r0 = 2
    //     0xc06f74: movz            x0, #0x2
    // 0xc06f78: StoreField: r1->field_b = r0
    //     0xc06f78: stur            w0, [x1, #0xb]
    // 0xc06f7c: r0 = Row()
    //     0xc06f7c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc06f80: mov             x1, x0
    // 0xc06f84: r0 = Instance_Axis
    //     0xc06f84: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc06f88: stur            x1, [fp, #-8]
    // 0xc06f8c: StoreField: r1->field_f = r0
    //     0xc06f8c: stur            w0, [x1, #0xf]
    // 0xc06f90: r0 = Instance_MainAxisAlignment
    //     0xc06f90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc06f94: ldr             x0, [x0, #0xab0]
    // 0xc06f98: StoreField: r1->field_13 = r0
    //     0xc06f98: stur            w0, [x1, #0x13]
    // 0xc06f9c: r0 = Instance_MainAxisSize
    //     0xc06f9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc06fa0: ldr             x0, [x0, #0xa10]
    // 0xc06fa4: ArrayStore: r1[0] = r0  ; List_4
    //     0xc06fa4: stur            w0, [x1, #0x17]
    // 0xc06fa8: r2 = Instance_CrossAxisAlignment
    //     0xc06fa8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc06fac: ldr             x2, [x2, #0xa18]
    // 0xc06fb0: StoreField: r1->field_1b = r2
    //     0xc06fb0: stur            w2, [x1, #0x1b]
    // 0xc06fb4: r2 = Instance_VerticalDirection
    //     0xc06fb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc06fb8: ldr             x2, [x2, #0xa20]
    // 0xc06fbc: StoreField: r1->field_23 = r2
    //     0xc06fbc: stur            w2, [x1, #0x23]
    // 0xc06fc0: r3 = Instance_Clip
    //     0xc06fc0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc06fc4: ldr             x3, [x3, #0x38]
    // 0xc06fc8: StoreField: r1->field_2b = r3
    //     0xc06fc8: stur            w3, [x1, #0x2b]
    // 0xc06fcc: StoreField: r1->field_2f = rZR
    //     0xc06fcc: stur            xzr, [x1, #0x2f]
    // 0xc06fd0: ldur            x4, [fp, #-0x10]
    // 0xc06fd4: StoreField: r1->field_b = r4
    //     0xc06fd4: stur            w4, [x1, #0xb]
    // 0xc06fd8: r0 = Padding()
    //     0xc06fd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc06fdc: mov             x2, x0
    // 0xc06fe0: r0 = Instance_EdgeInsets
    //     0xc06fe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xc06fe4: ldr             x0, [x0, #0xa00]
    // 0xc06fe8: stur            x2, [fp, #-0x10]
    // 0xc06fec: StoreField: r2->field_f = r0
    //     0xc06fec: stur            w0, [x2, #0xf]
    // 0xc06ff0: ldur            x0, [fp, #-8]
    // 0xc06ff4: StoreField: r2->field_b = r0
    //     0xc06ff4: stur            w0, [x2, #0xb]
    // 0xc06ff8: ldur            x0, [fp, #-0x28]
    // 0xc06ffc: LoadField: r1 = r0->field_b
    //     0xc06ffc: ldur            w1, [x0, #0xb]
    // 0xc07000: LoadField: r3 = r0->field_f
    //     0xc07000: ldur            w3, [x0, #0xf]
    // 0xc07004: DecompressPointer r3
    //     0xc07004: add             x3, x3, HEAP, lsl #32
    // 0xc07008: LoadField: r4 = r3->field_b
    //     0xc07008: ldur            w4, [x3, #0xb]
    // 0xc0700c: r3 = LoadInt32Instr(r1)
    //     0xc0700c: sbfx            x3, x1, #1, #0x1f
    // 0xc07010: stur            x3, [fp, #-0x50]
    // 0xc07014: r1 = LoadInt32Instr(r4)
    //     0xc07014: sbfx            x1, x4, #1, #0x1f
    // 0xc07018: cmp             x3, x1
    // 0xc0701c: b.ne            #0xc07028
    // 0xc07020: mov             x1, x0
    // 0xc07024: r0 = _growToNextCapacity()
    //     0xc07024: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc07028: ldur            x2, [fp, #-0x28]
    // 0xc0702c: ldur            x3, [fp, #-0x50]
    // 0xc07030: add             x0, x3, #1
    // 0xc07034: lsl             x1, x0, #1
    // 0xc07038: StoreField: r2->field_b = r1
    //     0xc07038: stur            w1, [x2, #0xb]
    // 0xc0703c: LoadField: r1 = r2->field_f
    //     0xc0703c: ldur            w1, [x2, #0xf]
    // 0xc07040: DecompressPointer r1
    //     0xc07040: add             x1, x1, HEAP, lsl #32
    // 0xc07044: ldur            x0, [fp, #-0x10]
    // 0xc07048: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc07048: add             x25, x1, x3, lsl #2
    //     0xc0704c: add             x25, x25, #0xf
    //     0xc07050: str             w0, [x25]
    //     0xc07054: tbz             w0, #0, #0xc07070
    //     0xc07058: ldurb           w16, [x1, #-1]
    //     0xc0705c: ldurb           w17, [x0, #-1]
    //     0xc07060: and             x16, x17, x16, lsr #2
    //     0xc07064: tst             x16, HEAP, lsr #32
    //     0xc07068: b.eq            #0xc07070
    //     0xc0706c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc07070: b               #0xc07078
    // 0xc07074: ldur            x2, [fp, #-0x28]
    // 0xc07078: ldur            x0, [fp, #-0x18]
    // 0xc0707c: r0 = Column()
    //     0xc0707c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc07080: mov             x1, x0
    // 0xc07084: r0 = Instance_Axis
    //     0xc07084: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc07088: stur            x1, [fp, #-8]
    // 0xc0708c: StoreField: r1->field_f = r0
    //     0xc0708c: stur            w0, [x1, #0xf]
    // 0xc07090: r0 = Instance_MainAxisAlignment
    //     0xc07090: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc07094: ldr             x0, [x0, #0xa08]
    // 0xc07098: StoreField: r1->field_13 = r0
    //     0xc07098: stur            w0, [x1, #0x13]
    // 0xc0709c: r0 = Instance_MainAxisSize
    //     0xc0709c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc070a0: ldr             x0, [x0, #0xa10]
    // 0xc070a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xc070a4: stur            w0, [x1, #0x17]
    // 0xc070a8: ldur            x0, [fp, #-0x18]
    // 0xc070ac: StoreField: r1->field_1b = r0
    //     0xc070ac: stur            w0, [x1, #0x1b]
    // 0xc070b0: r0 = Instance_VerticalDirection
    //     0xc070b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc070b4: ldr             x0, [x0, #0xa20]
    // 0xc070b8: StoreField: r1->field_23 = r0
    //     0xc070b8: stur            w0, [x1, #0x23]
    // 0xc070bc: r0 = Instance_Clip
    //     0xc070bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc070c0: ldr             x0, [x0, #0x38]
    // 0xc070c4: StoreField: r1->field_2b = r0
    //     0xc070c4: stur            w0, [x1, #0x2b]
    // 0xc070c8: StoreField: r1->field_2f = rZR
    //     0xc070c8: stur            xzr, [x1, #0x2f]
    // 0xc070cc: ldur            x0, [fp, #-0x28]
    // 0xc070d0: StoreField: r1->field_b = r0
    //     0xc070d0: stur            w0, [x1, #0xb]
    // 0xc070d4: r0 = Padding()
    //     0xc070d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc070d8: r1 = Instance_EdgeInsets
    //     0xc070d8: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xc070dc: ldr             x1, [x1, #0x110]
    // 0xc070e0: StoreField: r0->field_f = r1
    //     0xc070e0: stur            w1, [x0, #0xf]
    // 0xc070e4: ldur            x1, [fp, #-8]
    // 0xc070e8: StoreField: r0->field_b = r1
    //     0xc070e8: stur            w1, [x0, #0xb]
    // 0xc070ec: LeaveFrame
    //     0xc070ec: mov             SP, fp
    //     0xc070f0: ldp             fp, lr, [SP], #0x10
    // 0xc070f4: ret
    //     0xc070f4: ret             
    // 0xc070f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc070f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc070fc: b               #0xc0684c
    // 0xc07100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07100: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07104: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07104: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07108: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07108: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0710c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0710c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07110: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc07110: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc07114: r9 = _pageController
    //     0xc07114: add             x9, PP, #0x52, lsl #12  ; [pp+0x52650] Field <_ProductTestimonialCarouselState@1740523543._pageController@1740523543>: late (offset: 0x14)
    //     0xc07118: ldr             x9, [x9, #0x650]
    // 0xc0711c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc0711c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc07120: SaveReg d0
    //     0xc07120: str             q0, [SP, #-0x10]!
    // 0xc07124: r0 = AllocateDouble()
    //     0xc07124: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc07128: RestoreReg d0
    //     0xc07128: ldr             q0, [SP], #0x10
    // 0xc0712c: b               #0xc06da0
    // 0xc07130: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07130: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07134: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07134: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xc07138, size: 0x70
    // 0xc07138: EnterFrame
    //     0xc07138: stp             fp, lr, [SP, #-0x10]!
    //     0xc0713c: mov             fp, SP
    // 0xc07140: ldr             x0, [fp, #0x20]
    // 0xc07144: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc07144: ldur            w1, [x0, #0x17]
    // 0xc07148: DecompressPointer r1
    //     0xc07148: add             x1, x1, HEAP, lsl #32
    // 0xc0714c: CheckStackOverflow
    //     0xc0714c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc07150: cmp             SP, x16
    //     0xc07154: b.ls            #0xc0719c
    // 0xc07158: LoadField: r0 = r1->field_f
    //     0xc07158: ldur            w0, [x1, #0xf]
    // 0xc0715c: DecompressPointer r0
    //     0xc0715c: add             x0, x0, HEAP, lsl #32
    // 0xc07160: LoadField: r1 = r0->field_b
    //     0xc07160: ldur            w1, [x0, #0xb]
    // 0xc07164: DecompressPointer r1
    //     0xc07164: add             x1, x1, HEAP, lsl #32
    // 0xc07168: cmp             w1, NULL
    // 0xc0716c: b.eq            #0xc071a4
    // 0xc07170: LoadField: r2 = r1->field_b
    //     0xc07170: ldur            w2, [x1, #0xb]
    // 0xc07174: DecompressPointer r2
    //     0xc07174: add             x2, x2, HEAP, lsl #32
    // 0xc07178: ldr             x1, [fp, #0x10]
    // 0xc0717c: r3 = LoadInt32Instr(r1)
    //     0xc0717c: sbfx            x3, x1, #1, #0x1f
    //     0xc07180: tbz             w1, #0, #0xc07188
    //     0xc07184: ldur            x3, [x1, #7]
    // 0xc07188: mov             x1, x0
    // 0xc0718c: r0 = _testimonialCard()
    //     0xc0718c: bl              #0xc071a8  ; [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard
    // 0xc07190: LeaveFrame
    //     0xc07190: mov             SP, fp
    //     0xc07194: ldp             fp, lr, [SP], #0x10
    // 0xc07198: ret
    //     0xc07198: ret             
    // 0xc0719c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0719c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc071a0: b               #0xc07158
    // 0xc071a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc071a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialCard(/* No info */) {
    // ** addr: 0xc071a8, size: 0xe14
    // 0xc071a8: EnterFrame
    //     0xc071a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc071ac: mov             fp, SP
    // 0xc071b0: AllocStack(0xa8)
    //     0xc071b0: sub             SP, SP, #0xa8
    // 0xc071b4: SetupParameters(_ProductTestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xc071b4: stur            x1, [fp, #-8]
    //     0xc071b8: stur            x2, [fp, #-0x10]
    //     0xc071bc: stur            x3, [fp, #-0x18]
    // 0xc071c0: CheckStackOverflow
    //     0xc071c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc071c4: cmp             SP, x16
    //     0xc071c8: b.ls            #0xc07f98
    // 0xc071cc: r1 = 2
    //     0xc071cc: movz            x1, #0x2
    // 0xc071d0: r0 = AllocateContext()
    //     0xc071d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xc071d4: mov             x3, x0
    // 0xc071d8: ldur            x2, [fp, #-8]
    // 0xc071dc: stur            x3, [fp, #-0x28]
    // 0xc071e0: StoreField: r3->field_f = r2
    //     0xc071e0: stur            w2, [x3, #0xf]
    // 0xc071e4: ldur            x4, [fp, #-0x18]
    // 0xc071e8: r0 = BoxInt64Instr(r4)
    //     0xc071e8: sbfiz           x0, x4, #1, #0x1f
    //     0xc071ec: cmp             x4, x0, asr #1
    //     0xc071f0: b.eq            #0xc071fc
    //     0xc071f4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc071f8: stur            x4, [x0, #7]
    // 0xc071fc: mov             x4, x0
    // 0xc07200: ldur            x1, [fp, #-0x10]
    // 0xc07204: stur            x4, [fp, #-0x20]
    // 0xc07208: r0 = LoadClassIdInstr(r1)
    //     0xc07208: ldur            x0, [x1, #-1]
    //     0xc0720c: ubfx            x0, x0, #0xc, #0x14
    // 0xc07210: stp             x4, x1, [SP]
    // 0xc07214: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc07214: sub             lr, x0, #0xb7
    //     0xc07218: ldr             lr, [x21, lr, lsl #3]
    //     0xc0721c: blr             lr
    // 0xc07220: stur            x0, [fp, #-0x40]
    // 0xc07224: cmp             w0, NULL
    // 0xc07228: b.ne            #0xc07234
    // 0xc0722c: r1 = Null
    //     0xc0722c: mov             x1, NULL
    // 0xc07230: b               #0xc07260
    // 0xc07234: LoadField: r1 = r0->field_b7
    //     0xc07234: ldur            w1, [x0, #0xb7]
    // 0xc07238: DecompressPointer r1
    //     0xc07238: add             x1, x1, HEAP, lsl #32
    // 0xc0723c: cmp             w1, NULL
    // 0xc07240: b.ne            #0xc0724c
    // 0xc07244: r1 = Null
    //     0xc07244: mov             x1, NULL
    // 0xc07248: b               #0xc07260
    // 0xc0724c: LoadField: r2 = r1->field_b
    //     0xc0724c: ldur            w2, [x1, #0xb]
    // 0xc07250: cbnz            w2, #0xc0725c
    // 0xc07254: r1 = false
    //     0xc07254: add             x1, NULL, #0x30  ; false
    // 0xc07258: b               #0xc07260
    // 0xc0725c: r1 = true
    //     0xc0725c: add             x1, NULL, #0x20  ; true
    // 0xc07260: cmp             w1, NULL
    // 0xc07264: b.ne            #0xc07270
    // 0xc07268: r4 = false
    //     0xc07268: add             x4, NULL, #0x30  ; false
    // 0xc0726c: b               #0xc07274
    // 0xc07270: mov             x4, x1
    // 0xc07274: ldur            x3, [fp, #-8]
    // 0xc07278: stur            x4, [fp, #-0x38]
    // 0xc0727c: LoadField: r5 = r3->field_1f
    //     0xc0727c: ldur            w5, [x3, #0x1f]
    // 0xc07280: DecompressPointer r5
    //     0xc07280: add             x5, x5, HEAP, lsl #32
    // 0xc07284: mov             x1, x5
    // 0xc07288: ldur            x2, [fp, #-0x20]
    // 0xc0728c: stur            x5, [fp, #-0x30]
    // 0xc07290: r0 = _getValueOrData()
    //     0xc07290: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc07294: mov             x1, x0
    // 0xc07298: ldur            x0, [fp, #-0x30]
    // 0xc0729c: LoadField: r2 = r0->field_f
    //     0xc0729c: ldur            w2, [x0, #0xf]
    // 0xc072a0: DecompressPointer r2
    //     0xc072a0: add             x2, x2, HEAP, lsl #32
    // 0xc072a4: cmp             w2, w1
    // 0xc072a8: b.ne            #0xc072b4
    // 0xc072ac: r0 = Null
    //     0xc072ac: mov             x0, NULL
    // 0xc072b0: b               #0xc072b8
    // 0xc072b4: mov             x0, x1
    // 0xc072b8: cmp             w0, NULL
    // 0xc072bc: b.ne            #0xc072fc
    // 0xc072c0: r1 = <bool>
    //     0xc072c0: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xc072c4: r0 = RxBool()
    //     0xc072c4: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xc072c8: mov             x2, x0
    // 0xc072cc: r0 = Sentinel
    //     0xc072cc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc072d0: stur            x2, [fp, #-0x30]
    // 0xc072d4: StoreField: r2->field_13 = r0
    //     0xc072d4: stur            w0, [x2, #0x13]
    // 0xc072d8: r0 = true
    //     0xc072d8: add             x0, NULL, #0x20  ; true
    // 0xc072dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xc072dc: stur            w0, [x2, #0x17]
    // 0xc072e0: mov             x1, x2
    // 0xc072e4: r0 = RxNotifier()
    //     0xc072e4: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xc072e8: ldur            x0, [fp, #-0x30]
    // 0xc072ec: r2 = false
    //     0xc072ec: add             x2, NULL, #0x30  ; false
    // 0xc072f0: StoreField: r0->field_13 = r2
    //     0xc072f0: stur            w2, [x0, #0x13]
    // 0xc072f4: mov             x5, x0
    // 0xc072f8: b               #0xc07304
    // 0xc072fc: r2 = false
    //     0xc072fc: add             x2, NULL, #0x30  ; false
    // 0xc07300: mov             x5, x0
    // 0xc07304: ldur            x4, [fp, #-0x28]
    // 0xc07308: ldur            x3, [fp, #-0x38]
    // 0xc0730c: mov             x0, x5
    // 0xc07310: stur            x5, [fp, #-0x30]
    // 0xc07314: StoreField: r4->field_13 = r0
    //     0xc07314: stur            w0, [x4, #0x13]
    //     0xc07318: ldurb           w16, [x4, #-1]
    //     0xc0731c: ldurb           w17, [x0, #-1]
    //     0xc07320: and             x16, x17, x16, lsr #2
    //     0xc07324: tst             x16, HEAP, lsr #32
    //     0xc07328: b.eq            #0xc07330
    //     0xc0732c: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xc07330: r1 = Instance_Color
    //     0xc07330: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc07334: d0 = 0.100000
    //     0xc07334: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc07338: r0 = withOpacity()
    //     0xc07338: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0733c: mov             x2, x0
    // 0xc07340: r1 = Null
    //     0xc07340: mov             x1, NULL
    // 0xc07344: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc07344: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc07348: r0 = Border.all()
    //     0xc07348: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0734c: stur            x0, [fp, #-0x48]
    // 0xc07350: r0 = BoxDecoration()
    //     0xc07350: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc07354: mov             x3, x0
    // 0xc07358: ldur            x0, [fp, #-0x48]
    // 0xc0735c: stur            x3, [fp, #-0x50]
    // 0xc07360: StoreField: r3->field_f = r0
    //     0xc07360: stur            w0, [x3, #0xf]
    // 0xc07364: r0 = Instance_BoxShape
    //     0xc07364: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc07368: ldr             x0, [x0, #0x80]
    // 0xc0736c: StoreField: r3->field_23 = r0
    //     0xc0736c: stur            w0, [x3, #0x23]
    // 0xc07370: r1 = <Widget>
    //     0xc07370: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc07374: r2 = 0
    //     0xc07374: movz            x2, #0
    // 0xc07378: r0 = _GrowableList()
    //     0xc07378: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0737c: mov             x1, x0
    // 0xc07380: ldur            x0, [fp, #-0x38]
    // 0xc07384: stur            x1, [fp, #-0x48]
    // 0xc07388: tbnz            w0, #4, #0xc0753c
    // 0xc0738c: ldur            x0, [fp, #-0x10]
    // 0xc07390: r0 = ImageHeaders.forImages()
    //     0xc07390: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xc07394: mov             x2, x0
    // 0xc07398: ldur            x1, [fp, #-0x10]
    // 0xc0739c: stur            x2, [fp, #-0x38]
    // 0xc073a0: r0 = LoadClassIdInstr(r1)
    //     0xc073a0: ldur            x0, [x1, #-1]
    //     0xc073a4: ubfx            x0, x0, #0xc, #0x14
    // 0xc073a8: ldur            x16, [fp, #-0x20]
    // 0xc073ac: stp             x16, x1, [SP]
    // 0xc073b0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc073b0: sub             lr, x0, #0xb7
    //     0xc073b4: ldr             lr, [x21, lr, lsl #3]
    //     0xc073b8: blr             lr
    // 0xc073bc: cmp             w0, NULL
    // 0xc073c0: b.ne            #0xc073cc
    // 0xc073c4: r0 = Null
    //     0xc073c4: mov             x0, NULL
    // 0xc073c8: b               #0xc07430
    // 0xc073cc: LoadField: r2 = r0->field_b7
    //     0xc073cc: ldur            w2, [x0, #0xb7]
    // 0xc073d0: DecompressPointer r2
    //     0xc073d0: add             x2, x2, HEAP, lsl #32
    // 0xc073d4: cmp             w2, NULL
    // 0xc073d8: b.ne            #0xc073e4
    // 0xc073dc: r0 = Null
    //     0xc073dc: mov             x0, NULL
    // 0xc073e0: b               #0xc07430
    // 0xc073e4: LoadField: r0 = r2->field_b
    //     0xc073e4: ldur            w0, [x2, #0xb]
    // 0xc073e8: r1 = LoadInt32Instr(r0)
    //     0xc073e8: sbfx            x1, x0, #1, #0x1f
    // 0xc073ec: mov             x0, x1
    // 0xc073f0: r1 = 0
    //     0xc073f0: movz            x1, #0
    // 0xc073f4: cmp             x1, x0
    // 0xc073f8: b.hs            #0xc07fa0
    // 0xc073fc: LoadField: r0 = r2->field_f
    //     0xc073fc: ldur            w0, [x2, #0xf]
    // 0xc07400: DecompressPointer r0
    //     0xc07400: add             x0, x0, HEAP, lsl #32
    // 0xc07404: LoadField: r1 = r0->field_f
    //     0xc07404: ldur            w1, [x0, #0xf]
    // 0xc07408: DecompressPointer r1
    //     0xc07408: add             x1, x1, HEAP, lsl #32
    // 0xc0740c: LoadField: r0 = r1->field_7
    //     0xc0740c: ldur            w0, [x1, #7]
    // 0xc07410: DecompressPointer r0
    //     0xc07410: add             x0, x0, HEAP, lsl #32
    // 0xc07414: cmp             w0, NULL
    // 0xc07418: b.ne            #0xc07424
    // 0xc0741c: r0 = Null
    //     0xc0741c: mov             x0, NULL
    // 0xc07420: b               #0xc07430
    // 0xc07424: LoadField: r1 = r0->field_b
    //     0xc07424: ldur            w1, [x0, #0xb]
    // 0xc07428: DecompressPointer r1
    //     0xc07428: add             x1, x1, HEAP, lsl #32
    // 0xc0742c: mov             x0, x1
    // 0xc07430: cmp             w0, NULL
    // 0xc07434: b.ne            #0xc07440
    // 0xc07438: r3 = ""
    //     0xc07438: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0743c: b               #0xc07444
    // 0xc07440: mov             x3, x0
    // 0xc07444: ldur            x0, [fp, #-0x48]
    // 0xc07448: stur            x3, [fp, #-0x58]
    // 0xc0744c: r1 = Function '<anonymous closure>':.
    //     0xc0744c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52670] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc07450: ldr             x1, [x1, #0x670]
    // 0xc07454: r2 = Null
    //     0xc07454: mov             x2, NULL
    // 0xc07458: r0 = AllocateClosure()
    //     0xc07458: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0745c: r1 = Function '<anonymous closure>':.
    //     0xc0745c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52678] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc07460: ldr             x1, [x1, #0x678]
    // 0xc07464: r2 = Null
    //     0xc07464: mov             x2, NULL
    // 0xc07468: stur            x0, [fp, #-0x60]
    // 0xc0746c: r0 = AllocateClosure()
    //     0xc0746c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc07470: stur            x0, [fp, #-0x68]
    // 0xc07474: r0 = CachedNetworkImage()
    //     0xc07474: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xc07478: stur            x0, [fp, #-0x70]
    // 0xc0747c: ldur            x16, [fp, #-0x38]
    // 0xc07480: ldur            lr, [fp, #-0x60]
    // 0xc07484: stp             lr, x16, [SP, #0x20]
    // 0xc07488: r16 = Instance_BoxFit
    //     0xc07488: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xc0748c: ldr             x16, [x16, #0x118]
    // 0xc07490: ldur            lr, [fp, #-0x68]
    // 0xc07494: stp             lr, x16, [SP, #0x10]
    // 0xc07498: r16 = inf
    //     0xc07498: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc0749c: ldr             x16, [x16, #0x9f8]
    // 0xc074a0: r30 = 366.000000
    //     0xc074a0: add             lr, PP, #0x52, lsl #12  ; [pp+0x52680] 366
    //     0xc074a4: ldr             lr, [lr, #0x680]
    // 0xc074a8: stp             lr, x16, [SP]
    // 0xc074ac: mov             x1, x0
    // 0xc074b0: ldur            x2, [fp, #-0x58]
    // 0xc074b4: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x5, fit, 0x4, height, 0x7, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, width, 0x6, null]
    //     0xc074b4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52688] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x5, "fit", 0x4, "height", 0x7, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, "width", 0x6, Null]
    //     0xc074b8: ldr             x4, [x4, #0x688]
    // 0xc074bc: r0 = CachedNetworkImage()
    //     0xc074bc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xc074c0: ldur            x0, [fp, #-0x48]
    // 0xc074c4: LoadField: r1 = r0->field_b
    //     0xc074c4: ldur            w1, [x0, #0xb]
    // 0xc074c8: LoadField: r2 = r0->field_f
    //     0xc074c8: ldur            w2, [x0, #0xf]
    // 0xc074cc: DecompressPointer r2
    //     0xc074cc: add             x2, x2, HEAP, lsl #32
    // 0xc074d0: LoadField: r3 = r2->field_b
    //     0xc074d0: ldur            w3, [x2, #0xb]
    // 0xc074d4: r2 = LoadInt32Instr(r1)
    //     0xc074d4: sbfx            x2, x1, #1, #0x1f
    // 0xc074d8: stur            x2, [fp, #-0x18]
    // 0xc074dc: r1 = LoadInt32Instr(r3)
    //     0xc074dc: sbfx            x1, x3, #1, #0x1f
    // 0xc074e0: cmp             x2, x1
    // 0xc074e4: b.ne            #0xc074f0
    // 0xc074e8: mov             x1, x0
    // 0xc074ec: r0 = _growToNextCapacity()
    //     0xc074ec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc074f0: ldur            x2, [fp, #-0x48]
    // 0xc074f4: ldur            x3, [fp, #-0x18]
    // 0xc074f8: add             x0, x3, #1
    // 0xc074fc: lsl             x1, x0, #1
    // 0xc07500: StoreField: r2->field_b = r1
    //     0xc07500: stur            w1, [x2, #0xb]
    // 0xc07504: LoadField: r1 = r2->field_f
    //     0xc07504: ldur            w1, [x2, #0xf]
    // 0xc07508: DecompressPointer r1
    //     0xc07508: add             x1, x1, HEAP, lsl #32
    // 0xc0750c: ldur            x0, [fp, #-0x70]
    // 0xc07510: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc07510: add             x25, x1, x3, lsl #2
    //     0xc07514: add             x25, x25, #0xf
    //     0xc07518: str             w0, [x25]
    //     0xc0751c: tbz             w0, #0, #0xc07538
    //     0xc07520: ldurb           w16, [x1, #-1]
    //     0xc07524: ldurb           w17, [x0, #-1]
    //     0xc07528: and             x16, x17, x16, lsr #2
    //     0xc0752c: tst             x16, HEAP, lsr #32
    //     0xc07530: b.eq            #0xc07538
    //     0xc07534: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc07538: b               #0xc075cc
    // 0xc0753c: mov             x2, x1
    // 0xc07540: r0 = Container()
    //     0xc07540: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc07544: mov             x1, x0
    // 0xc07548: stur            x0, [fp, #-0x38]
    // 0xc0754c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc0754c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc07550: r0 = Container()
    //     0xc07550: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc07554: ldur            x0, [fp, #-0x48]
    // 0xc07558: LoadField: r1 = r0->field_b
    //     0xc07558: ldur            w1, [x0, #0xb]
    // 0xc0755c: LoadField: r2 = r0->field_f
    //     0xc0755c: ldur            w2, [x0, #0xf]
    // 0xc07560: DecompressPointer r2
    //     0xc07560: add             x2, x2, HEAP, lsl #32
    // 0xc07564: LoadField: r3 = r2->field_b
    //     0xc07564: ldur            w3, [x2, #0xb]
    // 0xc07568: r2 = LoadInt32Instr(r1)
    //     0xc07568: sbfx            x2, x1, #1, #0x1f
    // 0xc0756c: stur            x2, [fp, #-0x18]
    // 0xc07570: r1 = LoadInt32Instr(r3)
    //     0xc07570: sbfx            x1, x3, #1, #0x1f
    // 0xc07574: cmp             x2, x1
    // 0xc07578: b.ne            #0xc07584
    // 0xc0757c: mov             x1, x0
    // 0xc07580: r0 = _growToNextCapacity()
    //     0xc07580: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc07584: ldur            x2, [fp, #-0x48]
    // 0xc07588: ldur            x3, [fp, #-0x18]
    // 0xc0758c: add             x0, x3, #1
    // 0xc07590: lsl             x1, x0, #1
    // 0xc07594: StoreField: r2->field_b = r1
    //     0xc07594: stur            w1, [x2, #0xb]
    // 0xc07598: LoadField: r1 = r2->field_f
    //     0xc07598: ldur            w1, [x2, #0xf]
    // 0xc0759c: DecompressPointer r1
    //     0xc0759c: add             x1, x1, HEAP, lsl #32
    // 0xc075a0: ldur            x0, [fp, #-0x38]
    // 0xc075a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc075a4: add             x25, x1, x3, lsl #2
    //     0xc075a8: add             x25, x25, #0xf
    //     0xc075ac: str             w0, [x25]
    //     0xc075b0: tbz             w0, #0, #0xc075cc
    //     0xc075b4: ldurb           w16, [x1, #-1]
    //     0xc075b8: ldurb           w17, [x0, #-1]
    //     0xc075bc: and             x16, x17, x16, lsr #2
    //     0xc075c0: tst             x16, HEAP, lsr #32
    //     0xc075c4: b.eq            #0xc075cc
    //     0xc075c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc075cc: ldur            x1, [fp, #-0x10]
    // 0xc075d0: r0 = LoadClassIdInstr(r1)
    //     0xc075d0: ldur            x0, [x1, #-1]
    //     0xc075d4: ubfx            x0, x0, #0xc, #0x14
    // 0xc075d8: ldur            x16, [fp, #-0x20]
    // 0xc075dc: stp             x16, x1, [SP]
    // 0xc075e0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc075e0: sub             lr, x0, #0xb7
    //     0xc075e4: ldr             lr, [x21, lr, lsl #3]
    //     0xc075e8: blr             lr
    // 0xc075ec: cmp             w0, NULL
    // 0xc075f0: b.ne            #0xc075fc
    // 0xc075f4: r0 = Null
    //     0xc075f4: mov             x0, NULL
    // 0xc075f8: b               #0xc07608
    // 0xc075fc: LoadField: r1 = r0->field_cb
    //     0xc075fc: ldur            w1, [x0, #0xcb]
    // 0xc07600: DecompressPointer r1
    //     0xc07600: add             x1, x1, HEAP, lsl #32
    // 0xc07604: mov             x0, x1
    // 0xc07608: cmp             w0, NULL
    // 0xc0760c: b.ne            #0xc07618
    // 0xc07610: r1 = ""
    //     0xc07610: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc07614: b               #0xc0761c
    // 0xc07618: mov             x1, x0
    // 0xc0761c: ldur            x2, [fp, #-8]
    // 0xc07620: ldur            x0, [fp, #-0x48]
    // 0xc07624: r0 = capitalizeFirstWord()
    //     0xc07624: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xc07628: mov             x2, x0
    // 0xc0762c: ldur            x0, [fp, #-8]
    // 0xc07630: stur            x2, [fp, #-0x38]
    // 0xc07634: LoadField: r1 = r0->field_f
    //     0xc07634: ldur            w1, [x0, #0xf]
    // 0xc07638: DecompressPointer r1
    //     0xc07638: add             x1, x1, HEAP, lsl #32
    // 0xc0763c: cmp             w1, NULL
    // 0xc07640: b.eq            #0xc07fa4
    // 0xc07644: r0 = of()
    //     0xc07644: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc07648: LoadField: r1 = r0->field_87
    //     0xc07648: ldur            w1, [x0, #0x87]
    // 0xc0764c: DecompressPointer r1
    //     0xc0764c: add             x1, x1, HEAP, lsl #32
    // 0xc07650: LoadField: r0 = r1->field_7
    //     0xc07650: ldur            w0, [x1, #7]
    // 0xc07654: DecompressPointer r0
    //     0xc07654: add             x0, x0, HEAP, lsl #32
    // 0xc07658: r16 = Instance_Color
    //     0xc07658: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0765c: r30 = 14.000000
    //     0xc0765c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc07660: ldr             lr, [lr, #0x1d8]
    // 0xc07664: stp             lr, x16, [SP]
    // 0xc07668: mov             x1, x0
    // 0xc0766c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0766c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc07670: ldr             x4, [x4, #0x9b8]
    // 0xc07674: r0 = copyWith()
    //     0xc07674: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc07678: stur            x0, [fp, #-0x58]
    // 0xc0767c: r0 = Text()
    //     0xc0767c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc07680: mov             x1, x0
    // 0xc07684: ldur            x0, [fp, #-0x38]
    // 0xc07688: stur            x1, [fp, #-0x60]
    // 0xc0768c: StoreField: r1->field_b = r0
    //     0xc0768c: stur            w0, [x1, #0xb]
    // 0xc07690: ldur            x0, [fp, #-0x58]
    // 0xc07694: StoreField: r1->field_13 = r0
    //     0xc07694: stur            w0, [x1, #0x13]
    // 0xc07698: r0 = Padding()
    //     0xc07698: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0769c: mov             x2, x0
    // 0xc076a0: r0 = Instance_EdgeInsets
    //     0xc076a0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52690] Obj!EdgeInsets@d59781
    //     0xc076a4: ldr             x0, [x0, #0x690]
    // 0xc076a8: stur            x2, [fp, #-0x38]
    // 0xc076ac: StoreField: r2->field_f = r0
    //     0xc076ac: stur            w0, [x2, #0xf]
    // 0xc076b0: ldur            x0, [fp, #-0x60]
    // 0xc076b4: StoreField: r2->field_b = r0
    //     0xc076b4: stur            w0, [x2, #0xb]
    // 0xc076b8: ldur            x0, [fp, #-0x48]
    // 0xc076bc: LoadField: r1 = r0->field_b
    //     0xc076bc: ldur            w1, [x0, #0xb]
    // 0xc076c0: LoadField: r3 = r0->field_f
    //     0xc076c0: ldur            w3, [x0, #0xf]
    // 0xc076c4: DecompressPointer r3
    //     0xc076c4: add             x3, x3, HEAP, lsl #32
    // 0xc076c8: LoadField: r4 = r3->field_b
    //     0xc076c8: ldur            w4, [x3, #0xb]
    // 0xc076cc: r3 = LoadInt32Instr(r1)
    //     0xc076cc: sbfx            x3, x1, #1, #0x1f
    // 0xc076d0: stur            x3, [fp, #-0x18]
    // 0xc076d4: r1 = LoadInt32Instr(r4)
    //     0xc076d4: sbfx            x1, x4, #1, #0x1f
    // 0xc076d8: cmp             x3, x1
    // 0xc076dc: b.ne            #0xc076e8
    // 0xc076e0: mov             x1, x0
    // 0xc076e4: r0 = _growToNextCapacity()
    //     0xc076e4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc076e8: ldur            x4, [fp, #-0x10]
    // 0xc076ec: ldur            x2, [fp, #-0x48]
    // 0xc076f0: ldur            x3, [fp, #-0x18]
    // 0xc076f4: add             x0, x3, #1
    // 0xc076f8: lsl             x1, x0, #1
    // 0xc076fc: StoreField: r2->field_b = r1
    //     0xc076fc: stur            w1, [x2, #0xb]
    // 0xc07700: LoadField: r1 = r2->field_f
    //     0xc07700: ldur            w1, [x2, #0xf]
    // 0xc07704: DecompressPointer r1
    //     0xc07704: add             x1, x1, HEAP, lsl #32
    // 0xc07708: ldur            x0, [fp, #-0x38]
    // 0xc0770c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0770c: add             x25, x1, x3, lsl #2
    //     0xc07710: add             x25, x25, #0xf
    //     0xc07714: str             w0, [x25]
    //     0xc07718: tbz             w0, #0, #0xc07734
    //     0xc0771c: ldurb           w16, [x1, #-1]
    //     0xc07720: ldurb           w17, [x0, #-1]
    //     0xc07724: and             x16, x17, x16, lsr #2
    //     0xc07728: tst             x16, HEAP, lsr #32
    //     0xc0772c: b.eq            #0xc07734
    //     0xc07730: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc07734: r0 = LoadClassIdInstr(r4)
    //     0xc07734: ldur            x0, [x4, #-1]
    //     0xc07738: ubfx            x0, x0, #0xc, #0x14
    // 0xc0773c: ldur            x16, [fp, #-0x20]
    // 0xc07740: stp             x16, x4, [SP]
    // 0xc07744: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc07744: sub             lr, x0, #0xb7
    //     0xc07748: ldr             lr, [x21, lr, lsl #3]
    //     0xc0774c: blr             lr
    // 0xc07750: cmp             w0, NULL
    // 0xc07754: b.ne            #0xc07760
    // 0xc07758: r0 = Null
    //     0xc07758: mov             x0, NULL
    // 0xc0775c: b               #0xc0776c
    // 0xc07760: LoadField: r1 = r0->field_bb
    //     0xc07760: ldur            w1, [x0, #0xbb]
    // 0xc07764: DecompressPointer r1
    //     0xc07764: add             x1, x1, HEAP, lsl #32
    // 0xc07768: mov             x0, x1
    // 0xc0776c: cmp             w0, NULL
    // 0xc07770: b.ne            #0xc0777c
    // 0xc07774: r1 = ""
    //     0xc07774: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc07778: b               #0xc07780
    // 0xc0777c: mov             x1, x0
    // 0xc07780: ldur            x0, [fp, #-0x10]
    // 0xc07784: r0 = parse()
    //     0xc07784: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc07788: ldur            x1, [fp, #-0x10]
    // 0xc0778c: stur            d0, [fp, #-0x78]
    // 0xc07790: r0 = LoadClassIdInstr(r1)
    //     0xc07790: ldur            x0, [x1, #-1]
    //     0xc07794: ubfx            x0, x0, #0xc, #0x14
    // 0xc07798: ldur            x16, [fp, #-0x20]
    // 0xc0779c: stp             x16, x1, [SP]
    // 0xc077a0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc077a0: sub             lr, x0, #0xb7
    //     0xc077a4: ldr             lr, [x21, lr, lsl #3]
    //     0xc077a8: blr             lr
    // 0xc077ac: cmp             w0, NULL
    // 0xc077b0: b.ne            #0xc077bc
    // 0xc077b4: r0 = Null
    //     0xc077b4: mov             x0, NULL
    // 0xc077b8: b               #0xc077c8
    // 0xc077bc: LoadField: r1 = r0->field_bb
    //     0xc077bc: ldur            w1, [x0, #0xbb]
    // 0xc077c0: DecompressPointer r1
    //     0xc077c0: add             x1, x1, HEAP, lsl #32
    // 0xc077c4: mov             x0, x1
    // 0xc077c8: cmp             w0, NULL
    // 0xc077cc: b.ne            #0xc077d8
    // 0xc077d0: r1 = "0"
    //     0xc077d0: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xc077d4: b               #0xc077dc
    // 0xc077d8: mov             x1, x0
    // 0xc077dc: ldur            x0, [fp, #-0x10]
    // 0xc077e0: ldur            d0, [fp, #-0x78]
    // 0xc077e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc077e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc077e8: r0 = parse()
    //     0xc077e8: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xc077ec: stur            x0, [fp, #-0x18]
    // 0xc077f0: r0 = RatingWidget()
    //     0xc077f0: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xc077f4: mov             x3, x0
    // 0xc077f8: r0 = Instance_Icon
    //     0xc077f8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xc077fc: ldr             x0, [x0, #0x190]
    // 0xc07800: stur            x3, [fp, #-0x38]
    // 0xc07804: StoreField: r3->field_7 = r0
    //     0xc07804: stur            w0, [x3, #7]
    // 0xc07808: r0 = Instance_Icon
    //     0xc07808: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xc0780c: ldr             x0, [x0, #0x198]
    // 0xc07810: StoreField: r3->field_b = r0
    //     0xc07810: stur            w0, [x3, #0xb]
    // 0xc07814: r0 = Instance_Icon
    //     0xc07814: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xc07818: ldr             x0, [x0, #0x1a0]
    // 0xc0781c: StoreField: r3->field_f = r0
    //     0xc0781c: stur            w0, [x3, #0xf]
    // 0xc07820: r1 = Function '<anonymous closure>':.
    //     0xc07820: add             x1, PP, #0x52, lsl #12  ; [pp+0x52698] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc07824: ldr             x1, [x1, #0x698]
    // 0xc07828: r2 = Null
    //     0xc07828: mov             x2, NULL
    // 0xc0782c: r0 = AllocateClosure()
    //     0xc0782c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc07830: stur            x0, [fp, #-0x58]
    // 0xc07834: r0 = RatingBar()
    //     0xc07834: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xc07838: mov             x1, x0
    // 0xc0783c: ldur            x0, [fp, #-0x58]
    // 0xc07840: stur            x1, [fp, #-0x60]
    // 0xc07844: StoreField: r1->field_b = r0
    //     0xc07844: stur            w0, [x1, #0xb]
    // 0xc07848: r0 = true
    //     0xc07848: add             x0, NULL, #0x20  ; true
    // 0xc0784c: StoreField: r1->field_1f = r0
    //     0xc0784c: stur            w0, [x1, #0x1f]
    // 0xc07850: r2 = Instance_Axis
    //     0xc07850: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc07854: StoreField: r1->field_23 = r2
    //     0xc07854: stur            w2, [x1, #0x23]
    // 0xc07858: StoreField: r1->field_27 = r0
    //     0xc07858: stur            w0, [x1, #0x27]
    // 0xc0785c: d0 = 2.000000
    //     0xc0785c: fmov            d0, #2.00000000
    // 0xc07860: StoreField: r1->field_2b = d0
    //     0xc07860: stur            d0, [x1, #0x2b]
    // 0xc07864: StoreField: r1->field_33 = r0
    //     0xc07864: stur            w0, [x1, #0x33]
    // 0xc07868: ldur            d0, [fp, #-0x78]
    // 0xc0786c: StoreField: r1->field_37 = d0
    //     0xc0786c: stur            d0, [x1, #0x37]
    // 0xc07870: ldur            x3, [fp, #-0x18]
    // 0xc07874: StoreField: r1->field_3f = r3
    //     0xc07874: stur            x3, [x1, #0x3f]
    // 0xc07878: r3 = Instance_EdgeInsets
    //     0xc07878: ldr             x3, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc0787c: StoreField: r1->field_47 = r3
    //     0xc0787c: stur            w3, [x1, #0x47]
    // 0xc07880: d0 = 20.000000
    //     0xc07880: fmov            d0, #20.00000000
    // 0xc07884: StoreField: r1->field_4b = d0
    //     0xc07884: stur            d0, [x1, #0x4b]
    // 0xc07888: StoreField: r1->field_53 = rZR
    //     0xc07888: stur            xzr, [x1, #0x53]
    // 0xc0788c: r3 = false
    //     0xc0788c: add             x3, NULL, #0x30  ; false
    // 0xc07890: StoreField: r1->field_5b = r3
    //     0xc07890: stur            w3, [x1, #0x5b]
    // 0xc07894: StoreField: r1->field_5f = r3
    //     0xc07894: stur            w3, [x1, #0x5f]
    // 0xc07898: ldur            x3, [fp, #-0x38]
    // 0xc0789c: StoreField: r1->field_67 = r3
    //     0xc0789c: stur            w3, [x1, #0x67]
    // 0xc078a0: r0 = SizedBox()
    //     0xc078a0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc078a4: mov             x1, x0
    // 0xc078a8: ldur            x0, [fp, #-0x60]
    // 0xc078ac: stur            x1, [fp, #-0x38]
    // 0xc078b0: StoreField: r1->field_b = r0
    //     0xc078b0: stur            w0, [x1, #0xb]
    // 0xc078b4: ldur            x0, [fp, #-0x10]
    // 0xc078b8: r2 = LoadClassIdInstr(r0)
    //     0xc078b8: ldur            x2, [x0, #-1]
    //     0xc078bc: ubfx            x2, x2, #0xc, #0x14
    // 0xc078c0: ldur            x16, [fp, #-0x20]
    // 0xc078c4: stp             x16, x0, [SP]
    // 0xc078c8: mov             x0, x2
    // 0xc078cc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xc078cc: sub             lr, x0, #0xb7
    //     0xc078d0: ldr             lr, [x21, lr, lsl #3]
    //     0xc078d4: blr             lr
    // 0xc078d8: cmp             w0, NULL
    // 0xc078dc: b.ne            #0xc078e8
    // 0xc078e0: r0 = Null
    //     0xc078e0: mov             x0, NULL
    // 0xc078e4: b               #0xc078f4
    // 0xc078e8: LoadField: r1 = r0->field_c3
    //     0xc078e8: ldur            w1, [x0, #0xc3]
    // 0xc078ec: DecompressPointer r1
    //     0xc078ec: add             x1, x1, HEAP, lsl #32
    // 0xc078f0: mov             x0, x1
    // 0xc078f4: cmp             w0, NULL
    // 0xc078f8: b.ne            #0xc07904
    // 0xc078fc: r4 = ""
    //     0xc078fc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc07900: b               #0xc07908
    // 0xc07904: mov             x4, x0
    // 0xc07908: ldur            x3, [fp, #-8]
    // 0xc0790c: ldur            x2, [fp, #-0x48]
    // 0xc07910: ldur            x0, [fp, #-0x38]
    // 0xc07914: stur            x4, [fp, #-0x10]
    // 0xc07918: LoadField: r1 = r3->field_f
    //     0xc07918: ldur            w1, [x3, #0xf]
    // 0xc0791c: DecompressPointer r1
    //     0xc0791c: add             x1, x1, HEAP, lsl #32
    // 0xc07920: cmp             w1, NULL
    // 0xc07924: b.eq            #0xc07fa8
    // 0xc07928: r0 = of()
    //     0xc07928: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0792c: LoadField: r1 = r0->field_87
    //     0xc0792c: ldur            w1, [x0, #0x87]
    // 0xc07930: DecompressPointer r1
    //     0xc07930: add             x1, x1, HEAP, lsl #32
    // 0xc07934: LoadField: r0 = r1->field_2b
    //     0xc07934: ldur            w0, [x1, #0x2b]
    // 0xc07938: DecompressPointer r0
    //     0xc07938: add             x0, x0, HEAP, lsl #32
    // 0xc0793c: r16 = 12.000000
    //     0xc0793c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc07940: ldr             x16, [x16, #0x9e8]
    // 0xc07944: r30 = Instance_Color
    //     0xc07944: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc07948: stp             lr, x16, [SP]
    // 0xc0794c: mov             x1, x0
    // 0xc07950: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc07950: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc07954: ldr             x4, [x4, #0xaa0]
    // 0xc07958: r0 = copyWith()
    //     0xc07958: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0795c: stur            x0, [fp, #-0x20]
    // 0xc07960: r0 = Text()
    //     0xc07960: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc07964: mov             x3, x0
    // 0xc07968: ldur            x0, [fp, #-0x10]
    // 0xc0796c: stur            x3, [fp, #-0x58]
    // 0xc07970: StoreField: r3->field_b = r0
    //     0xc07970: stur            w0, [x3, #0xb]
    // 0xc07974: ldur            x0, [fp, #-0x20]
    // 0xc07978: StoreField: r3->field_13 = r0
    //     0xc07978: stur            w0, [x3, #0x13]
    // 0xc0797c: r1 = Null
    //     0xc0797c: mov             x1, NULL
    // 0xc07980: r2 = 4
    //     0xc07980: movz            x2, #0x4
    // 0xc07984: r0 = AllocateArray()
    //     0xc07984: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc07988: mov             x2, x0
    // 0xc0798c: ldur            x0, [fp, #-0x38]
    // 0xc07990: stur            x2, [fp, #-0x10]
    // 0xc07994: StoreField: r2->field_f = r0
    //     0xc07994: stur            w0, [x2, #0xf]
    // 0xc07998: ldur            x0, [fp, #-0x58]
    // 0xc0799c: StoreField: r2->field_13 = r0
    //     0xc0799c: stur            w0, [x2, #0x13]
    // 0xc079a0: r1 = <Widget>
    //     0xc079a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc079a4: r0 = AllocateGrowableArray()
    //     0xc079a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc079a8: mov             x1, x0
    // 0xc079ac: ldur            x0, [fp, #-0x10]
    // 0xc079b0: stur            x1, [fp, #-0x20]
    // 0xc079b4: StoreField: r1->field_f = r0
    //     0xc079b4: stur            w0, [x1, #0xf]
    // 0xc079b8: r0 = 4
    //     0xc079b8: movz            x0, #0x4
    // 0xc079bc: StoreField: r1->field_b = r0
    //     0xc079bc: stur            w0, [x1, #0xb]
    // 0xc079c0: r0 = Row()
    //     0xc079c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc079c4: mov             x1, x0
    // 0xc079c8: r0 = Instance_Axis
    //     0xc079c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc079cc: stur            x1, [fp, #-0x10]
    // 0xc079d0: StoreField: r1->field_f = r0
    //     0xc079d0: stur            w0, [x1, #0xf]
    // 0xc079d4: r0 = Instance_MainAxisAlignment
    //     0xc079d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc079d8: ldr             x0, [x0, #0xa08]
    // 0xc079dc: StoreField: r1->field_13 = r0
    //     0xc079dc: stur            w0, [x1, #0x13]
    // 0xc079e0: r2 = Instance_MainAxisSize
    //     0xc079e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc079e4: ldr             x2, [x2, #0xa10]
    // 0xc079e8: ArrayStore: r1[0] = r2  ; List_4
    //     0xc079e8: stur            w2, [x1, #0x17]
    // 0xc079ec: r3 = Instance_CrossAxisAlignment
    //     0xc079ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc079f0: ldr             x3, [x3, #0xa18]
    // 0xc079f4: StoreField: r1->field_1b = r3
    //     0xc079f4: stur            w3, [x1, #0x1b]
    // 0xc079f8: r3 = Instance_VerticalDirection
    //     0xc079f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc079fc: ldr             x3, [x3, #0xa20]
    // 0xc07a00: StoreField: r1->field_23 = r3
    //     0xc07a00: stur            w3, [x1, #0x23]
    // 0xc07a04: r4 = Instance_Clip
    //     0xc07a04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc07a08: ldr             x4, [x4, #0x38]
    // 0xc07a0c: StoreField: r1->field_2b = r4
    //     0xc07a0c: stur            w4, [x1, #0x2b]
    // 0xc07a10: StoreField: r1->field_2f = rZR
    //     0xc07a10: stur            xzr, [x1, #0x2f]
    // 0xc07a14: ldur            x5, [fp, #-0x20]
    // 0xc07a18: StoreField: r1->field_b = r5
    //     0xc07a18: stur            w5, [x1, #0xb]
    // 0xc07a1c: r0 = Padding()
    //     0xc07a1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc07a20: mov             x2, x0
    // 0xc07a24: r0 = Instance_EdgeInsets
    //     0xc07a24: add             x0, PP, #0x52, lsl #12  ; [pp+0x526a0] Obj!EdgeInsets@d59751
    //     0xc07a28: ldr             x0, [x0, #0x6a0]
    // 0xc07a2c: stur            x2, [fp, #-0x20]
    // 0xc07a30: StoreField: r2->field_f = r0
    //     0xc07a30: stur            w0, [x2, #0xf]
    // 0xc07a34: ldur            x0, [fp, #-0x10]
    // 0xc07a38: StoreField: r2->field_b = r0
    //     0xc07a38: stur            w0, [x2, #0xb]
    // 0xc07a3c: ldur            x0, [fp, #-0x48]
    // 0xc07a40: LoadField: r1 = r0->field_b
    //     0xc07a40: ldur            w1, [x0, #0xb]
    // 0xc07a44: LoadField: r3 = r0->field_f
    //     0xc07a44: ldur            w3, [x0, #0xf]
    // 0xc07a48: DecompressPointer r3
    //     0xc07a48: add             x3, x3, HEAP, lsl #32
    // 0xc07a4c: LoadField: r4 = r3->field_b
    //     0xc07a4c: ldur            w4, [x3, #0xb]
    // 0xc07a50: r3 = LoadInt32Instr(r1)
    //     0xc07a50: sbfx            x3, x1, #1, #0x1f
    // 0xc07a54: stur            x3, [fp, #-0x18]
    // 0xc07a58: r1 = LoadInt32Instr(r4)
    //     0xc07a58: sbfx            x1, x4, #1, #0x1f
    // 0xc07a5c: cmp             x3, x1
    // 0xc07a60: b.ne            #0xc07a6c
    // 0xc07a64: mov             x1, x0
    // 0xc07a68: r0 = _growToNextCapacity()
    //     0xc07a68: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc07a6c: ldur            x4, [fp, #-0x40]
    // 0xc07a70: ldur            x2, [fp, #-0x48]
    // 0xc07a74: ldur            x3, [fp, #-0x18]
    // 0xc07a78: add             x0, x3, #1
    // 0xc07a7c: lsl             x1, x0, #1
    // 0xc07a80: StoreField: r2->field_b = r1
    //     0xc07a80: stur            w1, [x2, #0xb]
    // 0xc07a84: LoadField: r1 = r2->field_f
    //     0xc07a84: ldur            w1, [x2, #0xf]
    // 0xc07a88: DecompressPointer r1
    //     0xc07a88: add             x1, x1, HEAP, lsl #32
    // 0xc07a8c: ldur            x0, [fp, #-0x20]
    // 0xc07a90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc07a90: add             x25, x1, x3, lsl #2
    //     0xc07a94: add             x25, x25, #0xf
    //     0xc07a98: str             w0, [x25]
    //     0xc07a9c: tbz             w0, #0, #0xc07ab8
    //     0xc07aa0: ldurb           w16, [x1, #-1]
    //     0xc07aa4: ldurb           w17, [x0, #-1]
    //     0xc07aa8: and             x16, x17, x16, lsr #2
    //     0xc07aac: tst             x16, HEAP, lsr #32
    //     0xc07ab0: b.eq            #0xc07ab8
    //     0xc07ab4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc07ab8: cmp             w4, NULL
    // 0xc07abc: b.ne            #0xc07ac8
    // 0xc07ac0: r0 = Null
    //     0xc07ac0: mov             x0, NULL
    // 0xc07ac4: b               #0xc07ae4
    // 0xc07ac8: LoadField: r1 = r4->field_bf
    //     0xc07ac8: ldur            w1, [x4, #0xbf]
    // 0xc07acc: DecompressPointer r1
    //     0xc07acc: add             x1, x1, HEAP, lsl #32
    // 0xc07ad0: cmp             w1, NULL
    // 0xc07ad4: b.ne            #0xc07ae0
    // 0xc07ad8: r0 = Null
    //     0xc07ad8: mov             x0, NULL
    // 0xc07adc: b               #0xc07ae4
    // 0xc07ae0: r0 = trim()
    //     0xc07ae0: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xc07ae4: cmp             w0, NULL
    // 0xc07ae8: b.ne            #0xc07af0
    // 0xc07aec: r0 = ""
    //     0xc07aec: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc07af0: ldur            x1, [fp, #-0x30]
    // 0xc07af4: stur            x0, [fp, #-0x10]
    // 0xc07af8: r0 = value()
    //     0xc07af8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xc07afc: tbnz            w0, #4, #0xc07b08
    // 0xc07b00: r0 = Null
    //     0xc07b00: mov             x0, NULL
    // 0xc07b04: b               #0xc07b0c
    // 0xc07b08: r0 = 4
    //     0xc07b08: movz            x0, #0x4
    // 0xc07b0c: ldur            x1, [fp, #-0x30]
    // 0xc07b10: stur            x0, [fp, #-0x20]
    // 0xc07b14: r0 = value()
    //     0xc07b14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xc07b18: tbnz            w0, #4, #0xc07b28
    // 0xc07b1c: r5 = Instance_TextOverflow
    //     0xc07b1c: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xc07b20: ldr             x5, [x5, #0x3a8]
    // 0xc07b24: b               #0xc07b30
    // 0xc07b28: r5 = Instance_TextOverflow
    //     0xc07b28: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xc07b2c: ldr             x5, [x5, #0xe10]
    // 0xc07b30: ldur            x4, [fp, #-8]
    // 0xc07b34: ldur            x3, [fp, #-0x40]
    // 0xc07b38: ldur            x2, [fp, #-0x10]
    // 0xc07b3c: ldur            x0, [fp, #-0x20]
    // 0xc07b40: stur            x5, [fp, #-0x38]
    // 0xc07b44: LoadField: r1 = r4->field_f
    //     0xc07b44: ldur            w1, [x4, #0xf]
    // 0xc07b48: DecompressPointer r1
    //     0xc07b48: add             x1, x1, HEAP, lsl #32
    // 0xc07b4c: cmp             w1, NULL
    // 0xc07b50: b.eq            #0xc07fac
    // 0xc07b54: r0 = of()
    //     0xc07b54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc07b58: LoadField: r1 = r0->field_87
    //     0xc07b58: ldur            w1, [x0, #0x87]
    // 0xc07b5c: DecompressPointer r1
    //     0xc07b5c: add             x1, x1, HEAP, lsl #32
    // 0xc07b60: LoadField: r0 = r1->field_2b
    //     0xc07b60: ldur            w0, [x1, #0x2b]
    // 0xc07b64: DecompressPointer r0
    //     0xc07b64: add             x0, x0, HEAP, lsl #32
    // 0xc07b68: LoadField: r1 = r0->field_13
    //     0xc07b68: ldur            w1, [x0, #0x13]
    // 0xc07b6c: DecompressPointer r1
    //     0xc07b6c: add             x1, x1, HEAP, lsl #32
    // 0xc07b70: stur            x1, [fp, #-0x58]
    // 0xc07b74: r0 = TextStyle()
    //     0xc07b74: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xc07b78: mov             x1, x0
    // 0xc07b7c: r0 = true
    //     0xc07b7c: add             x0, NULL, #0x20  ; true
    // 0xc07b80: stur            x1, [fp, #-0x60]
    // 0xc07b84: StoreField: r1->field_7 = r0
    //     0xc07b84: stur            w0, [x1, #7]
    // 0xc07b88: r0 = Instance_Color
    //     0xc07b88: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc07b8c: StoreField: r1->field_b = r0
    //     0xc07b8c: stur            w0, [x1, #0xb]
    // 0xc07b90: r0 = 12.000000
    //     0xc07b90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc07b94: ldr             x0, [x0, #0x9e8]
    // 0xc07b98: StoreField: r1->field_1f = r0
    //     0xc07b98: stur            w0, [x1, #0x1f]
    // 0xc07b9c: ldur            x0, [fp, #-0x58]
    // 0xc07ba0: StoreField: r1->field_13 = r0
    //     0xc07ba0: stur            w0, [x1, #0x13]
    // 0xc07ba4: r0 = Text()
    //     0xc07ba4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc07ba8: mov             x3, x0
    // 0xc07bac: ldur            x0, [fp, #-0x10]
    // 0xc07bb0: stur            x3, [fp, #-0x58]
    // 0xc07bb4: StoreField: r3->field_b = r0
    //     0xc07bb4: stur            w0, [x3, #0xb]
    // 0xc07bb8: ldur            x0, [fp, #-0x60]
    // 0xc07bbc: StoreField: r3->field_13 = r0
    //     0xc07bbc: stur            w0, [x3, #0x13]
    // 0xc07bc0: r0 = Instance_TextAlign
    //     0xc07bc0: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc07bc4: StoreField: r3->field_1b = r0
    //     0xc07bc4: stur            w0, [x3, #0x1b]
    // 0xc07bc8: ldur            x0, [fp, #-0x38]
    // 0xc07bcc: StoreField: r3->field_2b = r0
    //     0xc07bcc: stur            w0, [x3, #0x2b]
    // 0xc07bd0: ldur            x0, [fp, #-0x20]
    // 0xc07bd4: StoreField: r3->field_37 = r0
    //     0xc07bd4: stur            w0, [x3, #0x37]
    // 0xc07bd8: r1 = Null
    //     0xc07bd8: mov             x1, NULL
    // 0xc07bdc: r2 = 2
    //     0xc07bdc: movz            x2, #0x2
    // 0xc07be0: r0 = AllocateArray()
    //     0xc07be0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc07be4: mov             x2, x0
    // 0xc07be8: ldur            x0, [fp, #-0x58]
    // 0xc07bec: stur            x2, [fp, #-0x10]
    // 0xc07bf0: StoreField: r2->field_f = r0
    //     0xc07bf0: stur            w0, [x2, #0xf]
    // 0xc07bf4: r1 = <Widget>
    //     0xc07bf4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc07bf8: r0 = AllocateGrowableArray()
    //     0xc07bf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc07bfc: mov             x2, x0
    // 0xc07c00: ldur            x0, [fp, #-0x10]
    // 0xc07c04: stur            x2, [fp, #-0x20]
    // 0xc07c08: StoreField: r2->field_f = r0
    //     0xc07c08: stur            w0, [x2, #0xf]
    // 0xc07c0c: r0 = 2
    //     0xc07c0c: movz            x0, #0x2
    // 0xc07c10: StoreField: r2->field_b = r0
    //     0xc07c10: stur            w0, [x2, #0xb]
    // 0xc07c14: ldur            x0, [fp, #-0x40]
    // 0xc07c18: cmp             w0, NULL
    // 0xc07c1c: b.ne            #0xc07c28
    // 0xc07c20: r0 = Null
    //     0xc07c20: mov             x0, NULL
    // 0xc07c24: b               #0xc07c44
    // 0xc07c28: LoadField: r1 = r0->field_bf
    //     0xc07c28: ldur            w1, [x0, #0xbf]
    // 0xc07c2c: DecompressPointer r1
    //     0xc07c2c: add             x1, x1, HEAP, lsl #32
    // 0xc07c30: cmp             w1, NULL
    // 0xc07c34: b.ne            #0xc07c40
    // 0xc07c38: r0 = Null
    //     0xc07c38: mov             x0, NULL
    // 0xc07c3c: b               #0xc07c44
    // 0xc07c40: r0 = trim()
    //     0xc07c40: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xc07c44: cmp             w0, NULL
    // 0xc07c48: b.ne            #0xc07c54
    // 0xc07c4c: r1 = ""
    //     0xc07c4c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc07c50: b               #0xc07c58
    // 0xc07c54: mov             x1, x0
    // 0xc07c58: ldur            x0, [fp, #-8]
    // 0xc07c5c: LoadField: r2 = r0->field_f
    //     0xc07c5c: ldur            w2, [x0, #0xf]
    // 0xc07c60: DecompressPointer r2
    //     0xc07c60: add             x2, x2, HEAP, lsl #32
    // 0xc07c64: cmp             w2, NULL
    // 0xc07c68: b.eq            #0xc07fb0
    // 0xc07c6c: r0 = TextExceeds.textExceedsLines()
    //     0xc07c6c: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xc07c70: tbnz            w0, #4, #0xc07e00
    // 0xc07c74: ldur            x1, [fp, #-0x30]
    // 0xc07c78: r0 = value()
    //     0xc07c78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xc07c7c: tbnz            w0, #4, #0xc07c8c
    // 0xc07c80: r3 = "Know Less"
    //     0xc07c80: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xc07c84: ldr             x3, [x3, #0x1d0]
    // 0xc07c88: b               #0xc07c94
    // 0xc07c8c: r3 = "Know more"
    //     0xc07c8c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xc07c90: ldr             x3, [x3, #0x20]
    // 0xc07c94: ldur            x0, [fp, #-8]
    // 0xc07c98: ldur            x2, [fp, #-0x20]
    // 0xc07c9c: stur            x3, [fp, #-0x10]
    // 0xc07ca0: LoadField: r1 = r0->field_f
    //     0xc07ca0: ldur            w1, [x0, #0xf]
    // 0xc07ca4: DecompressPointer r1
    //     0xc07ca4: add             x1, x1, HEAP, lsl #32
    // 0xc07ca8: cmp             w1, NULL
    // 0xc07cac: b.eq            #0xc07fb4
    // 0xc07cb0: r0 = of()
    //     0xc07cb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc07cb4: LoadField: r1 = r0->field_87
    //     0xc07cb4: ldur            w1, [x0, #0x87]
    // 0xc07cb8: DecompressPointer r1
    //     0xc07cb8: add             x1, x1, HEAP, lsl #32
    // 0xc07cbc: LoadField: r0 = r1->field_7
    //     0xc07cbc: ldur            w0, [x1, #7]
    // 0xc07cc0: DecompressPointer r0
    //     0xc07cc0: add             x0, x0, HEAP, lsl #32
    // 0xc07cc4: ldur            x1, [fp, #-8]
    // 0xc07cc8: stur            x0, [fp, #-0x30]
    // 0xc07ccc: LoadField: r2 = r1->field_f
    //     0xc07ccc: ldur            w2, [x1, #0xf]
    // 0xc07cd0: DecompressPointer r2
    //     0xc07cd0: add             x2, x2, HEAP, lsl #32
    // 0xc07cd4: cmp             w2, NULL
    // 0xc07cd8: b.eq            #0xc07fb8
    // 0xc07cdc: mov             x1, x2
    // 0xc07ce0: r0 = of()
    //     0xc07ce0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc07ce4: LoadField: r1 = r0->field_5b
    //     0xc07ce4: ldur            w1, [x0, #0x5b]
    // 0xc07ce8: DecompressPointer r1
    //     0xc07ce8: add             x1, x1, HEAP, lsl #32
    // 0xc07cec: r16 = 12.000000
    //     0xc07cec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc07cf0: ldr             x16, [x16, #0x9e8]
    // 0xc07cf4: stp             x1, x16, [SP, #8]
    // 0xc07cf8: r16 = Instance_TextDecoration
    //     0xc07cf8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xc07cfc: ldr             x16, [x16, #0x10]
    // 0xc07d00: str             x16, [SP]
    // 0xc07d04: ldur            x1, [fp, #-0x30]
    // 0xc07d08: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xc07d08: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xc07d0c: ldr             x4, [x4, #0xe38]
    // 0xc07d10: r0 = copyWith()
    //     0xc07d10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc07d14: stur            x0, [fp, #-8]
    // 0xc07d18: r0 = Text()
    //     0xc07d18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc07d1c: mov             x1, x0
    // 0xc07d20: ldur            x0, [fp, #-0x10]
    // 0xc07d24: stur            x1, [fp, #-0x30]
    // 0xc07d28: StoreField: r1->field_b = r0
    //     0xc07d28: stur            w0, [x1, #0xb]
    // 0xc07d2c: ldur            x0, [fp, #-8]
    // 0xc07d30: StoreField: r1->field_13 = r0
    //     0xc07d30: stur            w0, [x1, #0x13]
    // 0xc07d34: r0 = Padding()
    //     0xc07d34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc07d38: mov             x1, x0
    // 0xc07d3c: r0 = Instance_EdgeInsets
    //     0xc07d3c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc07d40: ldr             x0, [x0, #0x668]
    // 0xc07d44: stur            x1, [fp, #-8]
    // 0xc07d48: StoreField: r1->field_f = r0
    //     0xc07d48: stur            w0, [x1, #0xf]
    // 0xc07d4c: ldur            x0, [fp, #-0x30]
    // 0xc07d50: StoreField: r1->field_b = r0
    //     0xc07d50: stur            w0, [x1, #0xb]
    // 0xc07d54: r0 = GestureDetector()
    //     0xc07d54: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc07d58: ldur            x2, [fp, #-0x28]
    // 0xc07d5c: r1 = Function '<anonymous closure>':.
    //     0xc07d5c: add             x1, PP, #0x52, lsl #12  ; [pp+0x526a8] AnonymousClosure: (0xc07fbc), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::_testimonialCard (0xc071a8)
    //     0xc07d60: ldr             x1, [x1, #0x6a8]
    // 0xc07d64: stur            x0, [fp, #-0x10]
    // 0xc07d68: r0 = AllocateClosure()
    //     0xc07d68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc07d6c: ldur            x16, [fp, #-8]
    // 0xc07d70: stp             x16, x0, [SP]
    // 0xc07d74: ldur            x1, [fp, #-0x10]
    // 0xc07d78: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xc07d78: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xc07d7c: ldr             x4, [x4, #0xaf0]
    // 0xc07d80: r0 = GestureDetector()
    //     0xc07d80: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc07d84: ldur            x0, [fp, #-0x20]
    // 0xc07d88: LoadField: r1 = r0->field_b
    //     0xc07d88: ldur            w1, [x0, #0xb]
    // 0xc07d8c: LoadField: r2 = r0->field_f
    //     0xc07d8c: ldur            w2, [x0, #0xf]
    // 0xc07d90: DecompressPointer r2
    //     0xc07d90: add             x2, x2, HEAP, lsl #32
    // 0xc07d94: LoadField: r3 = r2->field_b
    //     0xc07d94: ldur            w3, [x2, #0xb]
    // 0xc07d98: r2 = LoadInt32Instr(r1)
    //     0xc07d98: sbfx            x2, x1, #1, #0x1f
    // 0xc07d9c: stur            x2, [fp, #-0x18]
    // 0xc07da0: r1 = LoadInt32Instr(r3)
    //     0xc07da0: sbfx            x1, x3, #1, #0x1f
    // 0xc07da4: cmp             x2, x1
    // 0xc07da8: b.ne            #0xc07db4
    // 0xc07dac: mov             x1, x0
    // 0xc07db0: r0 = _growToNextCapacity()
    //     0xc07db0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc07db4: ldur            x2, [fp, #-0x20]
    // 0xc07db8: ldur            x3, [fp, #-0x18]
    // 0xc07dbc: add             x0, x3, #1
    // 0xc07dc0: lsl             x1, x0, #1
    // 0xc07dc4: StoreField: r2->field_b = r1
    //     0xc07dc4: stur            w1, [x2, #0xb]
    // 0xc07dc8: LoadField: r1 = r2->field_f
    //     0xc07dc8: ldur            w1, [x2, #0xf]
    // 0xc07dcc: DecompressPointer r1
    //     0xc07dcc: add             x1, x1, HEAP, lsl #32
    // 0xc07dd0: ldur            x0, [fp, #-0x10]
    // 0xc07dd4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc07dd4: add             x25, x1, x3, lsl #2
    //     0xc07dd8: add             x25, x25, #0xf
    //     0xc07ddc: str             w0, [x25]
    //     0xc07de0: tbz             w0, #0, #0xc07dfc
    //     0xc07de4: ldurb           w16, [x1, #-1]
    //     0xc07de8: ldurb           w17, [x0, #-1]
    //     0xc07dec: and             x16, x17, x16, lsr #2
    //     0xc07df0: tst             x16, HEAP, lsr #32
    //     0xc07df4: b.eq            #0xc07dfc
    //     0xc07df8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc07dfc: b               #0xc07e04
    // 0xc07e00: ldur            x2, [fp, #-0x20]
    // 0xc07e04: ldur            x1, [fp, #-0x48]
    // 0xc07e08: r0 = Column()
    //     0xc07e08: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc07e0c: mov             x1, x0
    // 0xc07e10: r0 = Instance_Axis
    //     0xc07e10: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc07e14: stur            x1, [fp, #-8]
    // 0xc07e18: StoreField: r1->field_f = r0
    //     0xc07e18: stur            w0, [x1, #0xf]
    // 0xc07e1c: r0 = Instance_MainAxisAlignment
    //     0xc07e1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc07e20: ldr             x0, [x0, #0xa08]
    // 0xc07e24: StoreField: r1->field_13 = r0
    //     0xc07e24: stur            w0, [x1, #0x13]
    // 0xc07e28: r0 = Instance_MainAxisSize
    //     0xc07e28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc07e2c: ldr             x0, [x0, #0xa10]
    // 0xc07e30: ArrayStore: r1[0] = r0  ; List_4
    //     0xc07e30: stur            w0, [x1, #0x17]
    // 0xc07e34: r0 = Instance_CrossAxisAlignment
    //     0xc07e34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc07e38: ldr             x0, [x0, #0x890]
    // 0xc07e3c: StoreField: r1->field_1b = r0
    //     0xc07e3c: stur            w0, [x1, #0x1b]
    // 0xc07e40: r0 = Instance_VerticalDirection
    //     0xc07e40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc07e44: ldr             x0, [x0, #0xa20]
    // 0xc07e48: StoreField: r1->field_23 = r0
    //     0xc07e48: stur            w0, [x1, #0x23]
    // 0xc07e4c: r0 = Instance_Clip
    //     0xc07e4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc07e50: ldr             x0, [x0, #0x38]
    // 0xc07e54: StoreField: r1->field_2b = r0
    //     0xc07e54: stur            w0, [x1, #0x2b]
    // 0xc07e58: StoreField: r1->field_2f = rZR
    //     0xc07e58: stur            xzr, [x1, #0x2f]
    // 0xc07e5c: ldur            x0, [fp, #-0x20]
    // 0xc07e60: StoreField: r1->field_b = r0
    //     0xc07e60: stur            w0, [x1, #0xb]
    // 0xc07e64: r0 = Padding()
    //     0xc07e64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc07e68: mov             x2, x0
    // 0xc07e6c: r0 = Instance_EdgeInsets
    //     0xc07e6c: add             x0, PP, #0x52, lsl #12  ; [pp+0x526b0] Obj!EdgeInsets@d59721
    //     0xc07e70: ldr             x0, [x0, #0x6b0]
    // 0xc07e74: stur            x2, [fp, #-0x10]
    // 0xc07e78: StoreField: r2->field_f = r0
    //     0xc07e78: stur            w0, [x2, #0xf]
    // 0xc07e7c: ldur            x0, [fp, #-8]
    // 0xc07e80: StoreField: r2->field_b = r0
    //     0xc07e80: stur            w0, [x2, #0xb]
    // 0xc07e84: ldur            x0, [fp, #-0x48]
    // 0xc07e88: LoadField: r1 = r0->field_b
    //     0xc07e88: ldur            w1, [x0, #0xb]
    // 0xc07e8c: LoadField: r3 = r0->field_f
    //     0xc07e8c: ldur            w3, [x0, #0xf]
    // 0xc07e90: DecompressPointer r3
    //     0xc07e90: add             x3, x3, HEAP, lsl #32
    // 0xc07e94: LoadField: r4 = r3->field_b
    //     0xc07e94: ldur            w4, [x3, #0xb]
    // 0xc07e98: r3 = LoadInt32Instr(r1)
    //     0xc07e98: sbfx            x3, x1, #1, #0x1f
    // 0xc07e9c: stur            x3, [fp, #-0x18]
    // 0xc07ea0: r1 = LoadInt32Instr(r4)
    //     0xc07ea0: sbfx            x1, x4, #1, #0x1f
    // 0xc07ea4: cmp             x3, x1
    // 0xc07ea8: b.ne            #0xc07eb4
    // 0xc07eac: mov             x1, x0
    // 0xc07eb0: r0 = _growToNextCapacity()
    //     0xc07eb0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc07eb4: ldur            x2, [fp, #-0x48]
    // 0xc07eb8: ldur            x3, [fp, #-0x18]
    // 0xc07ebc: add             x0, x3, #1
    // 0xc07ec0: lsl             x1, x0, #1
    // 0xc07ec4: StoreField: r2->field_b = r1
    //     0xc07ec4: stur            w1, [x2, #0xb]
    // 0xc07ec8: LoadField: r1 = r2->field_f
    //     0xc07ec8: ldur            w1, [x2, #0xf]
    // 0xc07ecc: DecompressPointer r1
    //     0xc07ecc: add             x1, x1, HEAP, lsl #32
    // 0xc07ed0: ldur            x0, [fp, #-0x10]
    // 0xc07ed4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc07ed4: add             x25, x1, x3, lsl #2
    //     0xc07ed8: add             x25, x25, #0xf
    //     0xc07edc: str             w0, [x25]
    //     0xc07ee0: tbz             w0, #0, #0xc07efc
    //     0xc07ee4: ldurb           w16, [x1, #-1]
    //     0xc07ee8: ldurb           w17, [x0, #-1]
    //     0xc07eec: and             x16, x17, x16, lsr #2
    //     0xc07ef0: tst             x16, HEAP, lsr #32
    //     0xc07ef4: b.eq            #0xc07efc
    //     0xc07ef8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc07efc: r0 = ListView()
    //     0xc07efc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc07f00: stur            x0, [fp, #-8]
    // 0xc07f04: r16 = Instance_NeverScrollableScrollPhysics
    //     0xc07f04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc07f08: ldr             x16, [x16, #0x1c8]
    // 0xc07f0c: r30 = true
    //     0xc07f0c: add             lr, NULL, #0x20  ; true
    // 0xc07f10: stp             lr, x16, [SP, #8]
    // 0xc07f14: r16 = Instance_EdgeInsets
    //     0xc07f14: ldr             x16, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc07f18: str             x16, [SP]
    // 0xc07f1c: mov             x1, x0
    // 0xc07f20: ldur            x2, [fp, #-0x48]
    // 0xc07f24: r4 = const [0, 0x5, 0x3, 0x2, padding, 0x4, physics, 0x2, shrinkWrap, 0x3, null]
    //     0xc07f24: add             x4, PP, #0x52, lsl #12  ; [pp+0x526b8] List(11) [0, 0x5, 0x3, 0x2, "padding", 0x4, "physics", 0x2, "shrinkWrap", 0x3, Null]
    //     0xc07f28: ldr             x4, [x4, #0x6b8]
    // 0xc07f2c: r0 = ListView()
    //     0xc07f2c: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xc07f30: r0 = Container()
    //     0xc07f30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc07f34: stur            x0, [fp, #-0x10]
    // 0xc07f38: ldur            x16, [fp, #-0x50]
    // 0xc07f3c: ldur            lr, [fp, #-8]
    // 0xc07f40: stp             lr, x16, [SP]
    // 0xc07f44: mov             x1, x0
    // 0xc07f48: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xc07f48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xc07f4c: ldr             x4, [x4, #0x88]
    // 0xc07f50: r0 = Container()
    //     0xc07f50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc07f54: r0 = AnimatedContainer()
    //     0xc07f54: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xc07f58: stur            x0, [fp, #-8]
    // 0xc07f5c: r16 = Instance_Cubic
    //     0xc07f5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xc07f60: ldr             x16, [x16, #0xaf8]
    // 0xc07f64: r30 = Instance_EdgeInsets
    //     0xc07f64: add             lr, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xc07f68: ldr             lr, [lr, #0x18]
    // 0xc07f6c: stp             lr, x16, [SP]
    // 0xc07f70: mov             x1, x0
    // 0xc07f74: ldur            x2, [fp, #-0x10]
    // 0xc07f78: r3 = Instance_Duration
    //     0xc07f78: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xc07f7c: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, margin, 0x4, null]
    //     0xc07f7c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52218] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "margin", 0x4, Null]
    //     0xc07f80: ldr             x4, [x4, #0x218]
    // 0xc07f84: r0 = AnimatedContainer()
    //     0xc07f84: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xc07f88: ldur            x0, [fp, #-8]
    // 0xc07f8c: LeaveFrame
    //     0xc07f8c: mov             SP, fp
    //     0xc07f90: ldp             fp, lr, [SP], #0x10
    // 0xc07f94: ret
    //     0xc07f94: ret             
    // 0xc07f98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc07f98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc07f9c: b               #0xc071cc
    // 0xc07fa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc07fa0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc07fa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07fa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07fa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07fa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07fac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07fac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07fb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07fb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc07fb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc07fb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc07fbc, size: 0x8c
    // 0xc07fbc: EnterFrame
    //     0xc07fbc: stp             fp, lr, [SP, #-0x10]!
    //     0xc07fc0: mov             fp, SP
    // 0xc07fc4: AllocStack(0x10)
    //     0xc07fc4: sub             SP, SP, #0x10
    // 0xc07fc8: SetupParameters()
    //     0xc07fc8: ldr             x0, [fp, #0x10]
    //     0xc07fcc: ldur            w2, [x0, #0x17]
    //     0xc07fd0: add             x2, x2, HEAP, lsl #32
    //     0xc07fd4: stur            x2, [fp, #-0x10]
    // 0xc07fd8: CheckStackOverflow
    //     0xc07fd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc07fdc: cmp             SP, x16
    //     0xc07fe0: b.ls            #0xc08040
    // 0xc07fe4: LoadField: r0 = r2->field_13
    //     0xc07fe4: ldur            w0, [x2, #0x13]
    // 0xc07fe8: DecompressPointer r0
    //     0xc07fe8: add             x0, x0, HEAP, lsl #32
    // 0xc07fec: mov             x1, x0
    // 0xc07ff0: stur            x0, [fp, #-8]
    // 0xc07ff4: r0 = value()
    //     0xc07ff4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xc07ff8: eor             x2, x0, #0x10
    // 0xc07ffc: ldur            x1, [fp, #-8]
    // 0xc08000: r0 = value=()
    //     0xc08000: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xc08004: ldur            x0, [fp, #-0x10]
    // 0xc08008: LoadField: r3 = r0->field_f
    //     0xc08008: ldur            w3, [x0, #0xf]
    // 0xc0800c: DecompressPointer r3
    //     0xc0800c: add             x3, x3, HEAP, lsl #32
    // 0xc08010: stur            x3, [fp, #-8]
    // 0xc08014: r1 = Function '<anonymous closure>':.
    //     0xc08014: add             x1, PP, #0x52, lsl #12  ; [pp+0x526c0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc08018: ldr             x1, [x1, #0x6c0]
    // 0xc0801c: r2 = Null
    //     0xc0801c: mov             x2, NULL
    // 0xc08020: r0 = AllocateClosure()
    //     0xc08020: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc08024: ldur            x1, [fp, #-8]
    // 0xc08028: mov             x2, x0
    // 0xc0802c: r0 = setState()
    //     0xc0802c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc08030: r0 = Null
    //     0xc08030: mov             x0, NULL
    // 0xc08034: LeaveFrame
    //     0xc08034: mov             SP, fp
    //     0xc08038: ldp             fp, lr, [SP], #0x10
    // 0xc0803c: ret
    //     0xc0803c: ret             
    // 0xc08040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08040: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08044: b               #0xc07fe4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc08048, size: 0x1ac
    // 0xc08048: EnterFrame
    //     0xc08048: stp             fp, lr, [SP, #-0x10]!
    //     0xc0804c: mov             fp, SP
    // 0xc08050: AllocStack(0x40)
    //     0xc08050: sub             SP, SP, #0x40
    // 0xc08054: SetupParameters()
    //     0xc08054: ldr             x0, [fp, #0x10]
    //     0xc08058: ldur            w1, [x0, #0x17]
    //     0xc0805c: add             x1, x1, HEAP, lsl #32
    //     0xc08060: stur            x1, [fp, #-0x20]
    // 0xc08064: CheckStackOverflow
    //     0xc08064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08068: cmp             SP, x16
    //     0xc0806c: b.ls            #0xc081e4
    // 0xc08070: LoadField: r0 = r1->field_f
    //     0xc08070: ldur            w0, [x1, #0xf]
    // 0xc08074: DecompressPointer r0
    //     0xc08074: add             x0, x0, HEAP, lsl #32
    // 0xc08078: LoadField: r2 = r0->field_b
    //     0xc08078: ldur            w2, [x0, #0xb]
    // 0xc0807c: DecompressPointer r2
    //     0xc0807c: add             x2, x2, HEAP, lsl #32
    // 0xc08080: stur            x2, [fp, #-0x18]
    // 0xc08084: cmp             w2, NULL
    // 0xc08088: b.eq            #0xc081ec
    // 0xc0808c: LoadField: r0 = r2->field_1b
    //     0xc0808c: ldur            w0, [x2, #0x1b]
    // 0xc08090: DecompressPointer r0
    //     0xc08090: add             x0, x0, HEAP, lsl #32
    // 0xc08094: stur            x0, [fp, #-0x10]
    // 0xc08098: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xc08098: ldur            w3, [x2, #0x17]
    // 0xc0809c: DecompressPointer r3
    //     0xc0809c: add             x3, x3, HEAP, lsl #32
    // 0xc080a0: stur            x3, [fp, #-8]
    // 0xc080a4: r0 = EventData()
    //     0xc080a4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xc080a8: mov             x1, x0
    // 0xc080ac: r0 = "product_page"
    //     0xc080ac: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xc080b0: ldr             x0, [x0, #0x480]
    // 0xc080b4: stur            x1, [fp, #-0x28]
    // 0xc080b8: StoreField: r1->field_13 = r0
    //     0xc080b8: stur            w0, [x1, #0x13]
    // 0xc080bc: ldur            x0, [fp, #-8]
    // 0xc080c0: StoreField: r1->field_53 = r0
    //     0xc080c0: stur            w0, [x1, #0x53]
    // 0xc080c4: ldur            x0, [fp, #-0x10]
    // 0xc080c8: StoreField: r1->field_57 = r0
    //     0xc080c8: stur            w0, [x1, #0x57]
    // 0xc080cc: r0 = "view_all"
    //     0xc080cc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xc080d0: ldr             x0, [x0, #0xba0]
    // 0xc080d4: StoreField: r1->field_eb = r0
    //     0xc080d4: stur            w0, [x1, #0xeb]
    // 0xc080d8: r0 = EventsRequest()
    //     0xc080d8: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xc080dc: mov             x1, x0
    // 0xc080e0: r0 = "cta_clicked"
    //     0xc080e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0xc080e4: ldr             x0, [x0, #0xdf8]
    // 0xc080e8: StoreField: r1->field_7 = r0
    //     0xc080e8: stur            w0, [x1, #7]
    // 0xc080ec: ldur            x0, [fp, #-0x28]
    // 0xc080f0: StoreField: r1->field_b = r0
    //     0xc080f0: stur            w0, [x1, #0xb]
    // 0xc080f4: ldur            x0, [fp, #-0x18]
    // 0xc080f8: LoadField: r2 = r0->field_27
    //     0xc080f8: ldur            w2, [x0, #0x27]
    // 0xc080fc: DecompressPointer r2
    //     0xc080fc: add             x2, x2, HEAP, lsl #32
    // 0xc08100: stp             x1, x2, [SP]
    // 0xc08104: r4 = 0
    //     0xc08104: movz            x4, #0
    // 0xc08108: ldr             x0, [SP, #8]
    // 0xc0810c: r16 = UnlinkedCall_0x613b5c
    //     0xc0810c: add             x16, PP, #0x52, lsl #12  ; [pp+0x526f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc08110: add             x16, x16, #0x6f8
    // 0xc08114: ldp             x5, lr, [x16]
    // 0xc08118: blr             lr
    // 0xc0811c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc0811c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc08120: ldr             x0, [x0, #0x1c80]
    //     0xc08124: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc08128: cmp             w0, w16
    //     0xc0812c: b.ne            #0xc08138
    //     0xc08130: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc08134: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc08138: r1 = Null
    //     0xc08138: mov             x1, NULL
    // 0xc0813c: r2 = 12
    //     0xc0813c: movz            x2, #0xc
    // 0xc08140: r0 = AllocateArray()
    //     0xc08140: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc08144: r16 = "previousScreenSource"
    //     0xc08144: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xc08148: ldr             x16, [x16, #0x448]
    // 0xc0814c: StoreField: r0->field_f = r16
    //     0xc0814c: stur            w16, [x0, #0xf]
    // 0xc08150: ldur            x1, [fp, #-0x20]
    // 0xc08154: LoadField: r2 = r1->field_f
    //     0xc08154: ldur            w2, [x1, #0xf]
    // 0xc08158: DecompressPointer r2
    //     0xc08158: add             x2, x2, HEAP, lsl #32
    // 0xc0815c: LoadField: r1 = r2->field_b
    //     0xc0815c: ldur            w1, [x2, #0xb]
    // 0xc08160: DecompressPointer r1
    //     0xc08160: add             x1, x1, HEAP, lsl #32
    // 0xc08164: cmp             w1, NULL
    // 0xc08168: b.eq            #0xc081f0
    // 0xc0816c: LoadField: r2 = r1->field_23
    //     0xc0816c: ldur            w2, [x1, #0x23]
    // 0xc08170: DecompressPointer r2
    //     0xc08170: add             x2, x2, HEAP, lsl #32
    // 0xc08174: StoreField: r0->field_13 = r2
    //     0xc08174: stur            w2, [x0, #0x13]
    // 0xc08178: r16 = "screenSource"
    //     0xc08178: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xc0817c: ldr             x16, [x16, #0x450]
    // 0xc08180: ArrayStore: r0[0] = r16  ; List_4
    //     0xc08180: stur            w16, [x0, #0x17]
    // 0xc08184: LoadField: r2 = r1->field_1f
    //     0xc08184: ldur            w2, [x1, #0x1f]
    // 0xc08188: DecompressPointer r2
    //     0xc08188: add             x2, x2, HEAP, lsl #32
    // 0xc0818c: StoreField: r0->field_1b = r2
    //     0xc0818c: stur            w2, [x0, #0x1b]
    // 0xc08190: r16 = "widgetType"
    //     0xc08190: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f338] "widgetType"
    //     0xc08194: ldr             x16, [x16, #0x338]
    // 0xc08198: StoreField: r0->field_1f = r16
    //     0xc08198: stur            w16, [x0, #0x1f]
    // 0xc0819c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc0819c: ldur            w2, [x1, #0x17]
    // 0xc081a0: DecompressPointer r2
    //     0xc081a0: add             x2, x2, HEAP, lsl #32
    // 0xc081a4: StoreField: r0->field_23 = r2
    //     0xc081a4: stur            w2, [x0, #0x23]
    // 0xc081a8: r16 = <String, String?>
    //     0xc081a8: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xc081ac: ldr             x16, [x16, #0x3c8]
    // 0xc081b0: stp             x0, x16, [SP]
    // 0xc081b4: r0 = Map._fromLiteral()
    //     0xc081b4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc081b8: r16 = "/testimonials"
    //     0xc081b8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0xc081bc: ldr             x16, [x16, #0x898]
    // 0xc081c0: stp             x16, NULL, [SP, #8]
    // 0xc081c4: str             x0, [SP]
    // 0xc081c8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xc081c8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xc081cc: ldr             x4, [x4, #0x438]
    // 0xc081d0: r0 = GetNavigation.toNamed()
    //     0xc081d0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xc081d4: r0 = Null
    //     0xc081d4: mov             x0, NULL
    // 0xc081d8: LeaveFrame
    //     0xc081d8: mov             SP, fp
    //     0xc081dc: ldp             fp, lr, [SP], #0x10
    // 0xc081e0: ret
    //     0xc081e0: ret             
    // 0xc081e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc081e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc081e8: b               #0xc08070
    // 0xc081ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc081ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc081f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc081f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88514, size: 0x54
    // 0xc88514: EnterFrame
    //     0xc88514: stp             fp, lr, [SP, #-0x10]!
    //     0xc88518: mov             fp, SP
    // 0xc8851c: CheckStackOverflow
    //     0xc8851c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88520: cmp             SP, x16
    //     0xc88524: b.ls            #0xc88554
    // 0xc88528: LoadField: r0 = r1->field_13
    //     0xc88528: ldur            w0, [x1, #0x13]
    // 0xc8852c: DecompressPointer r0
    //     0xc8852c: add             x0, x0, HEAP, lsl #32
    // 0xc88530: r16 = Sentinel
    //     0xc88530: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88534: cmp             w0, w16
    // 0xc88538: b.eq            #0xc8855c
    // 0xc8853c: mov             x1, x0
    // 0xc88540: r0 = dispose()
    //     0xc88540: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc88544: r0 = Null
    //     0xc88544: mov             x0, NULL
    // 0xc88548: LeaveFrame
    //     0xc88548: mov             SP, fp
    //     0xc8854c: ldp             fp, lr, [SP], #0x10
    // 0xc88550: ret
    //     0xc88550: ret             
    // 0xc88554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88554: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88558: b               #0xc88528
    // 0xc8855c: r9 = _pageController
    //     0xc8855c: add             x9, PP, #0x52, lsl #12  ; [pp+0x52650] Field <_ProductTestimonialCarouselState@1740523543._pageController@1740523543>: late (offset: 0x14)
    //     0xc88560: ldr             x9, [x9, #0x650]
    // 0xc88564: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc88564: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3969, size: 0x30, field offset: 0xc
//   const constructor, 
class ProductTestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81094, size: 0x84
    // 0xc81094: EnterFrame
    //     0xc81094: stp             fp, lr, [SP, #-0x10]!
    //     0xc81098: mov             fp, SP
    // 0xc8109c: AllocStack(0x18)
    //     0xc8109c: sub             SP, SP, #0x18
    // 0xc810a0: CheckStackOverflow
    //     0xc810a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc810a4: cmp             SP, x16
    //     0xc810a8: b.ls            #0xc81110
    // 0xc810ac: r1 = <ProductTestimonialCarousel>
    //     0xc810ac: add             x1, PP, #0x48, lsl #12  ; [pp+0x48290] TypeArguments: <ProductTestimonialCarousel>
    //     0xc810b0: ldr             x1, [x1, #0x290]
    // 0xc810b4: r0 = _ProductTestimonialCarouselState()
    //     0xc810b4: bl              #0xc81118  ; Allocate_ProductTestimonialCarouselStateStub -> _ProductTestimonialCarouselState (size=0x24)
    // 0xc810b8: mov             x1, x0
    // 0xc810bc: r0 = Sentinel
    //     0xc810bc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc810c0: stur            x1, [fp, #-8]
    // 0xc810c4: StoreField: r1->field_13 = r0
    //     0xc810c4: stur            w0, [x1, #0x13]
    // 0xc810c8: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc810c8: stur            xzr, [x1, #0x17]
    // 0xc810cc: r16 = <int, RxBool>
    //     0xc810cc: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc810d0: ldr             x16, [x16, #0x298]
    // 0xc810d4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc810d8: stp             lr, x16, [SP]
    // 0xc810dc: r0 = Map._fromLiteral()
    //     0xc810dc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc810e0: ldur            x1, [fp, #-8]
    // 0xc810e4: StoreField: r1->field_1f = r0
    //     0xc810e4: stur            w0, [x1, #0x1f]
    //     0xc810e8: ldurb           w16, [x1, #-1]
    //     0xc810ec: ldurb           w17, [x0, #-1]
    //     0xc810f0: and             x16, x17, x16, lsr #2
    //     0xc810f4: tst             x16, HEAP, lsr #32
    //     0xc810f8: b.eq            #0xc81100
    //     0xc810fc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc81100: mov             x0, x1
    // 0xc81104: LeaveFrame
    //     0xc81104: mov             SP, fp
    //     0xc81108: ldp             fp, lr, [SP], #0x10
    // 0xc8110c: ret
    //     0xc8110c: ret             
    // 0xc81110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81110: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81114: b               #0xc810ac
  }
}
