// lib: , url: package:customer_app/app/presentation/views/line/bag/bottom_view.dart

// class id: 1049468, size: 0x8
class :: {
}

// class id: 3290, size: 0x14, field offset: 0x14
class _BottomViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xba3b28, size: 0x8bc
    // 0xba3b28: EnterFrame
    //     0xba3b28: stp             fp, lr, [SP, #-0x10]!
    //     0xba3b2c: mov             fp, SP
    // 0xba3b30: AllocStack(0x58)
    //     0xba3b30: sub             SP, SP, #0x58
    // 0xba3b34: SetupParameters(_BottomViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba3b34: mov             x0, x1
    //     0xba3b38: stur            x1, [fp, #-8]
    //     0xba3b3c: mov             x1, x2
    //     0xba3b40: stur            x2, [fp, #-0x10]
    // 0xba3b44: CheckStackOverflow
    //     0xba3b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba3b48: cmp             SP, x16
    //     0xba3b4c: b.ls            #0xba43cc
    // 0xba3b50: r1 = 1
    //     0xba3b50: movz            x1, #0x1
    // 0xba3b54: r0 = AllocateContext()
    //     0xba3b54: bl              #0x16f6108  ; AllocateContextStub
    // 0xba3b58: mov             x3, x0
    // 0xba3b5c: ldur            x0, [fp, #-8]
    // 0xba3b60: stur            x3, [fp, #-0x18]
    // 0xba3b64: StoreField: r3->field_f = r0
    //     0xba3b64: stur            w0, [x3, #0xf]
    // 0xba3b68: r1 = Null
    //     0xba3b68: mov             x1, NULL
    // 0xba3b6c: r2 = 6
    //     0xba3b6c: movz            x2, #0x6
    // 0xba3b70: r0 = AllocateArray()
    //     0xba3b70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba3b74: r16 = "Subtotal ("
    //     0xba3b74: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e0] "Subtotal ("
    //     0xba3b78: ldr             x16, [x16, #0x4e0]
    // 0xba3b7c: StoreField: r0->field_f = r16
    //     0xba3b7c: stur            w16, [x0, #0xf]
    // 0xba3b80: ldur            x1, [fp, #-8]
    // 0xba3b84: LoadField: r2 = r1->field_b
    //     0xba3b84: ldur            w2, [x1, #0xb]
    // 0xba3b88: DecompressPointer r2
    //     0xba3b88: add             x2, x2, HEAP, lsl #32
    // 0xba3b8c: cmp             w2, NULL
    // 0xba3b90: b.eq            #0xba43d4
    // 0xba3b94: LoadField: r3 = r2->field_b
    //     0xba3b94: ldur            w3, [x2, #0xb]
    // 0xba3b98: DecompressPointer r3
    //     0xba3b98: add             x3, x3, HEAP, lsl #32
    // 0xba3b9c: cmp             w3, NULL
    // 0xba3ba0: b.ne            #0xba3bac
    // 0xba3ba4: r2 = Null
    //     0xba3ba4: mov             x2, NULL
    // 0xba3ba8: b               #0xba3bb4
    // 0xba3bac: LoadField: r2 = r3->field_b
    //     0xba3bac: ldur            w2, [x3, #0xb]
    // 0xba3bb0: DecompressPointer r2
    //     0xba3bb0: add             x2, x2, HEAP, lsl #32
    // 0xba3bb4: StoreField: r0->field_13 = r2
    //     0xba3bb4: stur            w2, [x0, #0x13]
    // 0xba3bb8: r16 = " item)"
    //     0xba3bb8: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e8] " item)"
    //     0xba3bbc: ldr             x16, [x16, #0x4e8]
    // 0xba3bc0: ArrayStore: r0[0] = r16  ; List_4
    //     0xba3bc0: stur            w16, [x0, #0x17]
    // 0xba3bc4: str             x0, [SP]
    // 0xba3bc8: r0 = _interpolate()
    //     0xba3bc8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba3bcc: ldur            x1, [fp, #-0x10]
    // 0xba3bd0: stur            x0, [fp, #-0x20]
    // 0xba3bd4: r0 = of()
    //     0xba3bd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3bd8: LoadField: r1 = r0->field_87
    //     0xba3bd8: ldur            w1, [x0, #0x87]
    // 0xba3bdc: DecompressPointer r1
    //     0xba3bdc: add             x1, x1, HEAP, lsl #32
    // 0xba3be0: LoadField: r0 = r1->field_2b
    //     0xba3be0: ldur            w0, [x1, #0x2b]
    // 0xba3be4: DecompressPointer r0
    //     0xba3be4: add             x0, x0, HEAP, lsl #32
    // 0xba3be8: stur            x0, [fp, #-0x28]
    // 0xba3bec: r1 = Instance_Color
    //     0xba3bec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba3bf0: d0 = 0.700000
    //     0xba3bf0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba3bf4: ldr             d0, [x17, #0xf48]
    // 0xba3bf8: r0 = withOpacity()
    //     0xba3bf8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba3bfc: r16 = 16.000000
    //     0xba3bfc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba3c00: ldr             x16, [x16, #0x188]
    // 0xba3c04: stp             x0, x16, [SP]
    // 0xba3c08: ldur            x1, [fp, #-0x28]
    // 0xba3c0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba3c0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba3c10: ldr             x4, [x4, #0xaa0]
    // 0xba3c14: r0 = copyWith()
    //     0xba3c14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba3c18: stur            x0, [fp, #-0x28]
    // 0xba3c1c: r0 = Text()
    //     0xba3c1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba3c20: mov             x2, x0
    // 0xba3c24: ldur            x0, [fp, #-0x20]
    // 0xba3c28: stur            x2, [fp, #-0x30]
    // 0xba3c2c: StoreField: r2->field_b = r0
    //     0xba3c2c: stur            w0, [x2, #0xb]
    // 0xba3c30: ldur            x0, [fp, #-0x28]
    // 0xba3c34: StoreField: r2->field_13 = r0
    //     0xba3c34: stur            w0, [x2, #0x13]
    // 0xba3c38: ldur            x0, [fp, #-8]
    // 0xba3c3c: LoadField: r1 = r0->field_b
    //     0xba3c3c: ldur            w1, [x0, #0xb]
    // 0xba3c40: DecompressPointer r1
    //     0xba3c40: add             x1, x1, HEAP, lsl #32
    // 0xba3c44: cmp             w1, NULL
    // 0xba3c48: b.eq            #0xba43d8
    // 0xba3c4c: LoadField: r3 = r1->field_b
    //     0xba3c4c: ldur            w3, [x1, #0xb]
    // 0xba3c50: DecompressPointer r3
    //     0xba3c50: add             x3, x3, HEAP, lsl #32
    // 0xba3c54: cmp             w3, NULL
    // 0xba3c58: b.ne            #0xba3c64
    // 0xba3c5c: r1 = Null
    //     0xba3c5c: mov             x1, NULL
    // 0xba3c60: b               #0xba3c6c
    // 0xba3c64: LoadField: r1 = r3->field_13
    //     0xba3c64: ldur            w1, [x3, #0x13]
    // 0xba3c68: DecompressPointer r1
    //     0xba3c68: add             x1, x1, HEAP, lsl #32
    // 0xba3c6c: cmp             w1, NULL
    // 0xba3c70: b.ne            #0xba3c7c
    // 0xba3c74: r3 = ""
    //     0xba3c74: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba3c78: b               #0xba3c80
    // 0xba3c7c: mov             x3, x1
    // 0xba3c80: ldur            x1, [fp, #-0x10]
    // 0xba3c84: stur            x3, [fp, #-0x20]
    // 0xba3c88: r0 = of()
    //     0xba3c88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3c8c: LoadField: r1 = r0->field_87
    //     0xba3c8c: ldur            w1, [x0, #0x87]
    // 0xba3c90: DecompressPointer r1
    //     0xba3c90: add             x1, x1, HEAP, lsl #32
    // 0xba3c94: LoadField: r0 = r1->field_7
    //     0xba3c94: ldur            w0, [x1, #7]
    // 0xba3c98: DecompressPointer r0
    //     0xba3c98: add             x0, x0, HEAP, lsl #32
    // 0xba3c9c: r16 = 16.000000
    //     0xba3c9c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba3ca0: ldr             x16, [x16, #0x188]
    // 0xba3ca4: r30 = Instance_Color
    //     0xba3ca4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba3ca8: stp             lr, x16, [SP]
    // 0xba3cac: mov             x1, x0
    // 0xba3cb0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba3cb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba3cb4: ldr             x4, [x4, #0xaa0]
    // 0xba3cb8: r0 = copyWith()
    //     0xba3cb8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba3cbc: stur            x0, [fp, #-0x28]
    // 0xba3cc0: r0 = Text()
    //     0xba3cc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba3cc4: mov             x3, x0
    // 0xba3cc8: ldur            x0, [fp, #-0x20]
    // 0xba3ccc: stur            x3, [fp, #-0x38]
    // 0xba3cd0: StoreField: r3->field_b = r0
    //     0xba3cd0: stur            w0, [x3, #0xb]
    // 0xba3cd4: ldur            x0, [fp, #-0x28]
    // 0xba3cd8: StoreField: r3->field_13 = r0
    //     0xba3cd8: stur            w0, [x3, #0x13]
    // 0xba3cdc: r1 = Null
    //     0xba3cdc: mov             x1, NULL
    // 0xba3ce0: r2 = 6
    //     0xba3ce0: movz            x2, #0x6
    // 0xba3ce4: r0 = AllocateArray()
    //     0xba3ce4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba3ce8: mov             x2, x0
    // 0xba3cec: ldur            x0, [fp, #-0x30]
    // 0xba3cf0: stur            x2, [fp, #-0x20]
    // 0xba3cf4: StoreField: r2->field_f = r0
    //     0xba3cf4: stur            w0, [x2, #0xf]
    // 0xba3cf8: r16 = Instance_Spacer
    //     0xba3cf8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba3cfc: ldr             x16, [x16, #0xf0]
    // 0xba3d00: StoreField: r2->field_13 = r16
    //     0xba3d00: stur            w16, [x2, #0x13]
    // 0xba3d04: ldur            x0, [fp, #-0x38]
    // 0xba3d08: ArrayStore: r2[0] = r0  ; List_4
    //     0xba3d08: stur            w0, [x2, #0x17]
    // 0xba3d0c: r1 = <Widget>
    //     0xba3d0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba3d10: r0 = AllocateGrowableArray()
    //     0xba3d10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba3d14: mov             x1, x0
    // 0xba3d18: ldur            x0, [fp, #-0x20]
    // 0xba3d1c: stur            x1, [fp, #-0x28]
    // 0xba3d20: StoreField: r1->field_f = r0
    //     0xba3d20: stur            w0, [x1, #0xf]
    // 0xba3d24: r2 = 6
    //     0xba3d24: movz            x2, #0x6
    // 0xba3d28: StoreField: r1->field_b = r2
    //     0xba3d28: stur            w2, [x1, #0xb]
    // 0xba3d2c: r0 = Row()
    //     0xba3d2c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba3d30: mov             x1, x0
    // 0xba3d34: r0 = Instance_Axis
    //     0xba3d34: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba3d38: stur            x1, [fp, #-0x20]
    // 0xba3d3c: StoreField: r1->field_f = r0
    //     0xba3d3c: stur            w0, [x1, #0xf]
    // 0xba3d40: r0 = Instance_MainAxisAlignment
    //     0xba3d40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba3d44: ldr             x0, [x0, #0xa08]
    // 0xba3d48: StoreField: r1->field_13 = r0
    //     0xba3d48: stur            w0, [x1, #0x13]
    // 0xba3d4c: r2 = Instance_MainAxisSize
    //     0xba3d4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba3d50: ldr             x2, [x2, #0xa10]
    // 0xba3d54: ArrayStore: r1[0] = r2  ; List_4
    //     0xba3d54: stur            w2, [x1, #0x17]
    // 0xba3d58: r3 = Instance_CrossAxisAlignment
    //     0xba3d58: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba3d5c: ldr             x3, [x3, #0xa18]
    // 0xba3d60: StoreField: r1->field_1b = r3
    //     0xba3d60: stur            w3, [x1, #0x1b]
    // 0xba3d64: r3 = Instance_VerticalDirection
    //     0xba3d64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba3d68: ldr             x3, [x3, #0xa20]
    // 0xba3d6c: StoreField: r1->field_23 = r3
    //     0xba3d6c: stur            w3, [x1, #0x23]
    // 0xba3d70: r4 = Instance_Clip
    //     0xba3d70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba3d74: ldr             x4, [x4, #0x38]
    // 0xba3d78: StoreField: r1->field_2b = r4
    //     0xba3d78: stur            w4, [x1, #0x2b]
    // 0xba3d7c: StoreField: r1->field_2f = rZR
    //     0xba3d7c: stur            xzr, [x1, #0x2f]
    // 0xba3d80: ldur            x5, [fp, #-0x28]
    // 0xba3d84: StoreField: r1->field_b = r5
    //     0xba3d84: stur            w5, [x1, #0xb]
    // 0xba3d88: r0 = Padding()
    //     0xba3d88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba3d8c: mov             x1, x0
    // 0xba3d90: r0 = Instance_EdgeInsets
    //     0xba3d90: add             x0, PP, #0x53, lsl #12  ; [pp+0x539d8] Obj!EdgeInsets@d57c51
    //     0xba3d94: ldr             x0, [x0, #0x9d8]
    // 0xba3d98: stur            x1, [fp, #-0x28]
    // 0xba3d9c: StoreField: r1->field_f = r0
    //     0xba3d9c: stur            w0, [x1, #0xf]
    // 0xba3da0: ldur            x0, [fp, #-0x20]
    // 0xba3da4: StoreField: r1->field_b = r0
    //     0xba3da4: stur            w0, [x1, #0xb]
    // 0xba3da8: ldur            x2, [fp, #-8]
    // 0xba3dac: LoadField: r0 = r2->field_b
    //     0xba3dac: ldur            w0, [x2, #0xb]
    // 0xba3db0: DecompressPointer r0
    //     0xba3db0: add             x0, x0, HEAP, lsl #32
    // 0xba3db4: cmp             w0, NULL
    // 0xba3db8: b.eq            #0xba43dc
    // 0xba3dbc: LoadField: r3 = r0->field_b
    //     0xba3dbc: ldur            w3, [x0, #0xb]
    // 0xba3dc0: DecompressPointer r3
    //     0xba3dc0: add             x3, x3, HEAP, lsl #32
    // 0xba3dc4: cmp             w3, NULL
    // 0xba3dc8: b.ne            #0xba3dd4
    // 0xba3dcc: r0 = Null
    //     0xba3dcc: mov             x0, NULL
    // 0xba3dd0: b               #0xba3e00
    // 0xba3dd4: LoadField: r0 = r3->field_f
    //     0xba3dd4: ldur            w0, [x3, #0xf]
    // 0xba3dd8: DecompressPointer r0
    //     0xba3dd8: add             x0, x0, HEAP, lsl #32
    // 0xba3ddc: cmp             w0, NULL
    // 0xba3de0: b.ne            #0xba3dec
    // 0xba3de4: r0 = Null
    //     0xba3de4: mov             x0, NULL
    // 0xba3de8: b               #0xba3e00
    // 0xba3dec: LoadField: r4 = r0->field_7
    //     0xba3dec: ldur            w4, [x0, #7]
    // 0xba3df0: cbnz            w4, #0xba3dfc
    // 0xba3df4: r0 = false
    //     0xba3df4: add             x0, NULL, #0x30  ; false
    // 0xba3df8: b               #0xba3e00
    // 0xba3dfc: r0 = true
    //     0xba3dfc: add             x0, NULL, #0x20  ; true
    // 0xba3e00: cmp             w0, NULL
    // 0xba3e04: b.ne            #0xba3e10
    // 0xba3e08: r4 = false
    //     0xba3e08: add             x4, NULL, #0x30  ; false
    // 0xba3e0c: b               #0xba3e14
    // 0xba3e10: mov             x4, x0
    // 0xba3e14: stur            x4, [fp, #-0x20]
    // 0xba3e18: cmp             w3, NULL
    // 0xba3e1c: b.ne            #0xba3e28
    // 0xba3e20: r0 = Null
    //     0xba3e20: mov             x0, NULL
    // 0xba3e24: b               #0xba3e54
    // 0xba3e28: LoadField: r0 = r3->field_f
    //     0xba3e28: ldur            w0, [x3, #0xf]
    // 0xba3e2c: DecompressPointer r0
    //     0xba3e2c: add             x0, x0, HEAP, lsl #32
    // 0xba3e30: r3 = LoadClassIdInstr(r0)
    //     0xba3e30: ldur            x3, [x0, #-1]
    //     0xba3e34: ubfx            x3, x3, #0xc, #0x14
    // 0xba3e38: str             x0, [SP]
    // 0xba3e3c: mov             x0, x3
    // 0xba3e40: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba3e40: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba3e44: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba3e44: movz            x17, #0x2700
    //     0xba3e48: add             lr, x0, x17
    //     0xba3e4c: ldr             lr, [x21, lr, lsl #3]
    //     0xba3e50: blr             lr
    // 0xba3e54: cmp             w0, NULL
    // 0xba3e58: b.ne            #0xba3e64
    // 0xba3e5c: r3 = ""
    //     0xba3e5c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba3e60: b               #0xba3e68
    // 0xba3e64: mov             x3, x0
    // 0xba3e68: ldur            x0, [fp, #-8]
    // 0xba3e6c: ldur            x2, [fp, #-0x20]
    // 0xba3e70: ldur            x1, [fp, #-0x10]
    // 0xba3e74: stur            x3, [fp, #-0x30]
    // 0xba3e78: r0 = of()
    //     0xba3e78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3e7c: LoadField: r1 = r0->field_87
    //     0xba3e7c: ldur            w1, [x0, #0x87]
    // 0xba3e80: DecompressPointer r1
    //     0xba3e80: add             x1, x1, HEAP, lsl #32
    // 0xba3e84: LoadField: r0 = r1->field_7
    //     0xba3e84: ldur            w0, [x1, #7]
    // 0xba3e88: DecompressPointer r0
    //     0xba3e88: add             x0, x0, HEAP, lsl #32
    // 0xba3e8c: r16 = 16.000000
    //     0xba3e8c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba3e90: ldr             x16, [x16, #0x188]
    // 0xba3e94: r30 = Instance_Color
    //     0xba3e94: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba3e98: ldr             lr, [lr, #0x858]
    // 0xba3e9c: stp             lr, x16, [SP]
    // 0xba3ea0: mov             x1, x0
    // 0xba3ea4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba3ea4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba3ea8: ldr             x4, [x4, #0xaa0]
    // 0xba3eac: r0 = copyWith()
    //     0xba3eac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba3eb0: stur            x0, [fp, #-0x38]
    // 0xba3eb4: r0 = Text()
    //     0xba3eb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba3eb8: mov             x1, x0
    // 0xba3ebc: ldur            x0, [fp, #-0x30]
    // 0xba3ec0: stur            x1, [fp, #-0x40]
    // 0xba3ec4: StoreField: r1->field_b = r0
    //     0xba3ec4: stur            w0, [x1, #0xb]
    // 0xba3ec8: ldur            x0, [fp, #-0x38]
    // 0xba3ecc: StoreField: r1->field_13 = r0
    //     0xba3ecc: stur            w0, [x1, #0x13]
    // 0xba3ed0: r0 = Visibility()
    //     0xba3ed0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xba3ed4: mov             x1, x0
    // 0xba3ed8: ldur            x0, [fp, #-0x40]
    // 0xba3edc: stur            x1, [fp, #-0x30]
    // 0xba3ee0: StoreField: r1->field_b = r0
    //     0xba3ee0: stur            w0, [x1, #0xb]
    // 0xba3ee4: r0 = Instance_SizedBox
    //     0xba3ee4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xba3ee8: StoreField: r1->field_f = r0
    //     0xba3ee8: stur            w0, [x1, #0xf]
    // 0xba3eec: ldur            x0, [fp, #-0x20]
    // 0xba3ef0: StoreField: r1->field_13 = r0
    //     0xba3ef0: stur            w0, [x1, #0x13]
    // 0xba3ef4: r0 = false
    //     0xba3ef4: add             x0, NULL, #0x30  ; false
    // 0xba3ef8: ArrayStore: r1[0] = r0  ; List_4
    //     0xba3ef8: stur            w0, [x1, #0x17]
    // 0xba3efc: StoreField: r1->field_1b = r0
    //     0xba3efc: stur            w0, [x1, #0x1b]
    // 0xba3f00: StoreField: r1->field_1f = r0
    //     0xba3f00: stur            w0, [x1, #0x1f]
    // 0xba3f04: StoreField: r1->field_23 = r0
    //     0xba3f04: stur            w0, [x1, #0x23]
    // 0xba3f08: StoreField: r1->field_27 = r0
    //     0xba3f08: stur            w0, [x1, #0x27]
    // 0xba3f0c: StoreField: r1->field_2b = r0
    //     0xba3f0c: stur            w0, [x1, #0x2b]
    // 0xba3f10: r0 = Padding()
    //     0xba3f10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba3f14: mov             x1, x0
    // 0xba3f18: r0 = Instance_EdgeInsets
    //     0xba3f18: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xba3f1c: ldr             x0, [x0, #0xa78]
    // 0xba3f20: stur            x1, [fp, #-0x20]
    // 0xba3f24: StoreField: r1->field_f = r0
    //     0xba3f24: stur            w0, [x1, #0xf]
    // 0xba3f28: ldur            x0, [fp, #-0x30]
    // 0xba3f2c: StoreField: r1->field_b = r0
    //     0xba3f2c: stur            w0, [x1, #0xb]
    // 0xba3f30: r16 = <EdgeInsets>
    //     0xba3f30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xba3f34: ldr             x16, [x16, #0xda0]
    // 0xba3f38: r30 = Instance_EdgeInsets
    //     0xba3f38: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xba3f3c: ldr             lr, [lr, #0x1f0]
    // 0xba3f40: stp             lr, x16, [SP]
    // 0xba3f44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba3f44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba3f48: r0 = all()
    //     0xba3f48: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba3f4c: ldur            x1, [fp, #-0x10]
    // 0xba3f50: stur            x0, [fp, #-0x30]
    // 0xba3f54: r0 = of()
    //     0xba3f54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3f58: LoadField: r1 = r0->field_5b
    //     0xba3f58: ldur            w1, [x0, #0x5b]
    // 0xba3f5c: DecompressPointer r1
    //     0xba3f5c: add             x1, x1, HEAP, lsl #32
    // 0xba3f60: r16 = <Color>
    //     0xba3f60: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xba3f64: ldr             x16, [x16, #0xf80]
    // 0xba3f68: stp             x1, x16, [SP]
    // 0xba3f6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba3f6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba3f70: r0 = all()
    //     0xba3f70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba3f74: ldur            x1, [fp, #-0x10]
    // 0xba3f78: stur            x0, [fp, #-0x38]
    // 0xba3f7c: r0 = of()
    //     0xba3f7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3f80: LoadField: r1 = r0->field_5b
    //     0xba3f80: ldur            w1, [x0, #0x5b]
    // 0xba3f84: DecompressPointer r1
    //     0xba3f84: add             x1, x1, HEAP, lsl #32
    // 0xba3f88: stur            x1, [fp, #-0x40]
    // 0xba3f8c: r0 = BorderSide()
    //     0xba3f8c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xba3f90: mov             x1, x0
    // 0xba3f94: ldur            x0, [fp, #-0x40]
    // 0xba3f98: stur            x1, [fp, #-0x48]
    // 0xba3f9c: StoreField: r1->field_7 = r0
    //     0xba3f9c: stur            w0, [x1, #7]
    // 0xba3fa0: d0 = 1.000000
    //     0xba3fa0: fmov            d0, #1.00000000
    // 0xba3fa4: StoreField: r1->field_b = d0
    //     0xba3fa4: stur            d0, [x1, #0xb]
    // 0xba3fa8: r0 = Instance_BorderStyle
    //     0xba3fa8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xba3fac: ldr             x0, [x0, #0xf68]
    // 0xba3fb0: StoreField: r1->field_13 = r0
    //     0xba3fb0: stur            w0, [x1, #0x13]
    // 0xba3fb4: d0 = -1.000000
    //     0xba3fb4: fmov            d0, #-1.00000000
    // 0xba3fb8: ArrayStore: r1[0] = d0  ; List_8
    //     0xba3fb8: stur            d0, [x1, #0x17]
    // 0xba3fbc: r0 = RoundedRectangleBorder()
    //     0xba3fbc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xba3fc0: mov             x1, x0
    // 0xba3fc4: r0 = Instance_BorderRadius
    //     0xba3fc4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xba3fc8: ldr             x0, [x0, #0xf70]
    // 0xba3fcc: StoreField: r1->field_b = r0
    //     0xba3fcc: stur            w0, [x1, #0xb]
    // 0xba3fd0: ldur            x0, [fp, #-0x48]
    // 0xba3fd4: StoreField: r1->field_7 = r0
    //     0xba3fd4: stur            w0, [x1, #7]
    // 0xba3fd8: r16 = <RoundedRectangleBorder>
    //     0xba3fd8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xba3fdc: ldr             x16, [x16, #0xf78]
    // 0xba3fe0: stp             x1, x16, [SP]
    // 0xba3fe4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba3fe4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba3fe8: r0 = all()
    //     0xba3fe8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xba3fec: stur            x0, [fp, #-0x40]
    // 0xba3ff0: r0 = ButtonStyle()
    //     0xba3ff0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xba3ff4: mov             x1, x0
    // 0xba3ff8: ldur            x0, [fp, #-0x38]
    // 0xba3ffc: stur            x1, [fp, #-0x48]
    // 0xba4000: StoreField: r1->field_b = r0
    //     0xba4000: stur            w0, [x1, #0xb]
    // 0xba4004: ldur            x0, [fp, #-0x30]
    // 0xba4008: StoreField: r1->field_23 = r0
    //     0xba4008: stur            w0, [x1, #0x23]
    // 0xba400c: ldur            x0, [fp, #-0x40]
    // 0xba4010: StoreField: r1->field_43 = r0
    //     0xba4010: stur            w0, [x1, #0x43]
    // 0xba4014: r0 = TextButtonThemeData()
    //     0xba4014: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xba4018: mov             x3, x0
    // 0xba401c: ldur            x0, [fp, #-0x48]
    // 0xba4020: stur            x3, [fp, #-0x38]
    // 0xba4024: StoreField: r3->field_7 = r0
    //     0xba4024: stur            w0, [x3, #7]
    // 0xba4028: ldur            x0, [fp, #-8]
    // 0xba402c: LoadField: r4 = r0->field_b
    //     0xba402c: ldur            w4, [x0, #0xb]
    // 0xba4030: DecompressPointer r4
    //     0xba4030: add             x4, x4, HEAP, lsl #32
    // 0xba4034: stur            x4, [fp, #-0x30]
    // 0xba4038: cmp             w4, NULL
    // 0xba403c: b.eq            #0xba43e0
    // 0xba4040: LoadField: r0 = r4->field_13
    //     0xba4040: ldur            w0, [x4, #0x13]
    // 0xba4044: DecompressPointer r0
    //     0xba4044: add             x0, x0, HEAP, lsl #32
    // 0xba4048: tbz             w0, #4, #0xba4064
    // 0xba404c: ldur            x2, [fp, #-0x18]
    // 0xba4050: r1 = Function '<anonymous closure>':.
    //     0xba4050: add             x1, PP, #0x54, lsl #12  ; [pp+0x54b88] AnonymousClosure: (0xba4404), in [package:customer_app/app/presentation/views/line/bag/bottom_view.dart] _BottomViewState::build (0xba3b28)
    //     0xba4054: ldr             x1, [x1, #0xb88]
    // 0xba4058: r0 = AllocateClosure()
    //     0xba4058: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba405c: mov             x2, x0
    // 0xba4060: b               #0xba4068
    // 0xba4064: r2 = Null
    //     0xba4064: mov             x2, NULL
    // 0xba4068: ldur            x0, [fp, #-0x30]
    // 0xba406c: stur            x2, [fp, #-8]
    // 0xba4070: LoadField: r1 = r0->field_b
    //     0xba4070: ldur            w1, [x0, #0xb]
    // 0xba4074: DecompressPointer r1
    //     0xba4074: add             x1, x1, HEAP, lsl #32
    // 0xba4078: cmp             w1, NULL
    // 0xba407c: b.eq            #0xba408c
    // 0xba4080: LoadField: r3 = r1->field_b
    //     0xba4080: ldur            w3, [x1, #0xb]
    // 0xba4084: DecompressPointer r3
    //     0xba4084: add             x3, x3, HEAP, lsl #32
    // 0xba4088: cbz             w3, #0xba4100
    // 0xba408c: LoadField: r1 = r0->field_f
    //     0xba408c: ldur            w1, [x0, #0xf]
    // 0xba4090: DecompressPointer r1
    //     0xba4090: add             x1, x1, HEAP, lsl #32
    // 0xba4094: tbz             w1, #4, #0xba40f4
    // 0xba4098: ldur            x1, [fp, #-0x10]
    // 0xba409c: r0 = of()
    //     0xba409c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba40a0: LoadField: r1 = r0->field_87
    //     0xba40a0: ldur            w1, [x0, #0x87]
    // 0xba40a4: DecompressPointer r1
    //     0xba40a4: add             x1, x1, HEAP, lsl #32
    // 0xba40a8: LoadField: r0 = r1->field_7
    //     0xba40a8: ldur            w0, [x1, #7]
    // 0xba40ac: DecompressPointer r0
    //     0xba40ac: add             x0, x0, HEAP, lsl #32
    // 0xba40b0: r16 = 14.000000
    //     0xba40b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba40b4: ldr             x16, [x16, #0x1d8]
    // 0xba40b8: r30 = Instance_Color
    //     0xba40b8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba40bc: stp             lr, x16, [SP]
    // 0xba40c0: mov             x1, x0
    // 0xba40c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba40c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba40c8: ldr             x4, [x4, #0xaa0]
    // 0xba40cc: r0 = copyWith()
    //     0xba40cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba40d0: stur            x0, [fp, #-0x18]
    // 0xba40d4: r0 = Text()
    //     0xba40d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba40d8: r2 = "CHECKOUT"
    //     0xba40d8: add             x2, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0xba40dc: ldr             x2, [x2, #0x858]
    // 0xba40e0: StoreField: r0->field_b = r2
    //     0xba40e0: stur            w2, [x0, #0xb]
    // 0xba40e4: ldur            x1, [fp, #-0x18]
    // 0xba40e8: StoreField: r0->field_13 = r1
    //     0xba40e8: stur            w1, [x0, #0x13]
    // 0xba40ec: mov             x4, x0
    // 0xba40f0: b               #0xba4258
    // 0xba40f4: r2 = "CHECKOUT"
    //     0xba40f4: add             x2, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0xba40f8: ldr             x2, [x2, #0x858]
    // 0xba40fc: b               #0xba4108
    // 0xba4100: r2 = "CHECKOUT"
    //     0xba4100: add             x2, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0xba4104: ldr             x2, [x2, #0x858]
    // 0xba4108: LoadField: r1 = r0->field_f
    //     0xba4108: ldur            w1, [x0, #0xf]
    // 0xba410c: DecompressPointer r1
    //     0xba410c: add             x1, x1, HEAP, lsl #32
    // 0xba4110: tbnz            w1, #4, #0xba4184
    // 0xba4114: LoadField: r3 = r0->field_1b
    //     0xba4114: ldur            w3, [x0, #0x1b]
    // 0xba4118: DecompressPointer r3
    //     0xba4118: add             x3, x3, HEAP, lsl #32
    // 0xba411c: LoadField: r4 = r3->field_7
    //     0xba411c: ldur            w4, [x3, #7]
    // 0xba4120: cbnz            w4, #0xba4184
    // 0xba4124: ldur            x1, [fp, #-0x10]
    // 0xba4128: r0 = of()
    //     0xba4128: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba412c: LoadField: r1 = r0->field_87
    //     0xba412c: ldur            w1, [x0, #0x87]
    // 0xba4130: DecompressPointer r1
    //     0xba4130: add             x1, x1, HEAP, lsl #32
    // 0xba4134: LoadField: r0 = r1->field_7
    //     0xba4134: ldur            w0, [x1, #7]
    // 0xba4138: DecompressPointer r0
    //     0xba4138: add             x0, x0, HEAP, lsl #32
    // 0xba413c: r16 = 14.000000
    //     0xba413c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba4140: ldr             x16, [x16, #0x1d8]
    // 0xba4144: r30 = Instance_Color
    //     0xba4144: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba4148: stp             lr, x16, [SP]
    // 0xba414c: mov             x1, x0
    // 0xba4150: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba4150: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba4154: ldr             x4, [x4, #0xaa0]
    // 0xba4158: r0 = copyWith()
    //     0xba4158: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba415c: stur            x0, [fp, #-0x18]
    // 0xba4160: r0 = Text()
    //     0xba4160: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba4164: mov             x1, x0
    // 0xba4168: r0 = "CHECKOUT WITH IN-STOCK PRODUCT"
    //     0xba4168: add             x0, PP, #0x54, lsl #12  ; [pp+0x54b90] "CHECKOUT WITH IN-STOCK PRODUCT"
    //     0xba416c: ldr             x0, [x0, #0xb90]
    // 0xba4170: StoreField: r1->field_b = r0
    //     0xba4170: stur            w0, [x1, #0xb]
    // 0xba4174: ldur            x0, [fp, #-0x18]
    // 0xba4178: StoreField: r1->field_13 = r0
    //     0xba4178: stur            w0, [x1, #0x13]
    // 0xba417c: mov             x0, x1
    // 0xba4180: b               #0xba4254
    // 0xba4184: tbnz            w1, #4, #0xba41f8
    // 0xba4188: LoadField: r1 = r0->field_1b
    //     0xba4188: ldur            w1, [x0, #0x1b]
    // 0xba418c: DecompressPointer r1
    //     0xba418c: add             x1, x1, HEAP, lsl #32
    // 0xba4190: LoadField: r0 = r1->field_7
    //     0xba4190: ldur            w0, [x1, #7]
    // 0xba4194: cbz             w0, #0xba41f8
    // 0xba4198: ldur            x1, [fp, #-0x10]
    // 0xba419c: r0 = of()
    //     0xba419c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba41a0: LoadField: r1 = r0->field_87
    //     0xba41a0: ldur            w1, [x0, #0x87]
    // 0xba41a4: DecompressPointer r1
    //     0xba41a4: add             x1, x1, HEAP, lsl #32
    // 0xba41a8: LoadField: r0 = r1->field_7
    //     0xba41a8: ldur            w0, [x1, #7]
    // 0xba41ac: DecompressPointer r0
    //     0xba41ac: add             x0, x0, HEAP, lsl #32
    // 0xba41b0: r16 = 14.000000
    //     0xba41b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba41b4: ldr             x16, [x16, #0x1d8]
    // 0xba41b8: r30 = Instance_Color
    //     0xba41b8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba41bc: stp             lr, x16, [SP]
    // 0xba41c0: mov             x1, x0
    // 0xba41c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba41c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba41c8: ldr             x4, [x4, #0xaa0]
    // 0xba41cc: r0 = copyWith()
    //     0xba41cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba41d0: stur            x0, [fp, #-0x18]
    // 0xba41d4: r0 = Text()
    //     0xba41d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba41d8: mov             x1, x0
    // 0xba41dc: r0 = "CHECKOUT"
    //     0xba41dc: add             x0, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0xba41e0: ldr             x0, [x0, #0x858]
    // 0xba41e4: StoreField: r1->field_b = r0
    //     0xba41e4: stur            w0, [x1, #0xb]
    // 0xba41e8: ldur            x0, [fp, #-0x18]
    // 0xba41ec: StoreField: r1->field_13 = r0
    //     0xba41ec: stur            w0, [x1, #0x13]
    // 0xba41f0: mov             x0, x1
    // 0xba41f4: b               #0xba4254
    // 0xba41f8: ldur            x1, [fp, #-0x10]
    // 0xba41fc: r0 = of()
    //     0xba41fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba4200: LoadField: r1 = r0->field_87
    //     0xba4200: ldur            w1, [x0, #0x87]
    // 0xba4204: DecompressPointer r1
    //     0xba4204: add             x1, x1, HEAP, lsl #32
    // 0xba4208: LoadField: r0 = r1->field_7
    //     0xba4208: ldur            w0, [x1, #7]
    // 0xba420c: DecompressPointer r0
    //     0xba420c: add             x0, x0, HEAP, lsl #32
    // 0xba4210: r16 = 14.000000
    //     0xba4210: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba4214: ldr             x16, [x16, #0x1d8]
    // 0xba4218: r30 = Instance_Color
    //     0xba4218: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba421c: stp             lr, x16, [SP]
    // 0xba4220: mov             x1, x0
    // 0xba4224: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba4224: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba4228: ldr             x4, [x4, #0xaa0]
    // 0xba422c: r0 = copyWith()
    //     0xba422c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba4230: stur            x0, [fp, #-0x10]
    // 0xba4234: r0 = Text()
    //     0xba4234: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba4238: mov             x1, x0
    // 0xba423c: r0 = "CLEAR BAG"
    //     0xba423c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54b98] "CLEAR BAG"
    //     0xba4240: ldr             x0, [x0, #0xb98]
    // 0xba4244: StoreField: r1->field_b = r0
    //     0xba4244: stur            w0, [x1, #0xb]
    // 0xba4248: ldur            x0, [fp, #-0x10]
    // 0xba424c: StoreField: r1->field_13 = r0
    //     0xba424c: stur            w0, [x1, #0x13]
    // 0xba4250: mov             x0, x1
    // 0xba4254: mov             x4, x0
    // 0xba4258: ldur            x3, [fp, #-0x28]
    // 0xba425c: ldur            x2, [fp, #-0x20]
    // 0xba4260: ldur            x1, [fp, #-0x38]
    // 0xba4264: ldur            x0, [fp, #-8]
    // 0xba4268: stur            x4, [fp, #-0x10]
    // 0xba426c: r0 = TextButton()
    //     0xba426c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xba4270: mov             x1, x0
    // 0xba4274: ldur            x0, [fp, #-8]
    // 0xba4278: stur            x1, [fp, #-0x18]
    // 0xba427c: StoreField: r1->field_b = r0
    //     0xba427c: stur            w0, [x1, #0xb]
    // 0xba4280: r0 = false
    //     0xba4280: add             x0, NULL, #0x30  ; false
    // 0xba4284: StoreField: r1->field_27 = r0
    //     0xba4284: stur            w0, [x1, #0x27]
    // 0xba4288: r0 = true
    //     0xba4288: add             x0, NULL, #0x20  ; true
    // 0xba428c: StoreField: r1->field_2f = r0
    //     0xba428c: stur            w0, [x1, #0x2f]
    // 0xba4290: ldur            x0, [fp, #-0x10]
    // 0xba4294: StoreField: r1->field_37 = r0
    //     0xba4294: stur            w0, [x1, #0x37]
    // 0xba4298: r0 = TextButtonTheme()
    //     0xba4298: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xba429c: mov             x1, x0
    // 0xba42a0: ldur            x0, [fp, #-0x38]
    // 0xba42a4: stur            x1, [fp, #-8]
    // 0xba42a8: StoreField: r1->field_f = r0
    //     0xba42a8: stur            w0, [x1, #0xf]
    // 0xba42ac: ldur            x0, [fp, #-0x18]
    // 0xba42b0: StoreField: r1->field_b = r0
    //     0xba42b0: stur            w0, [x1, #0xb]
    // 0xba42b4: r0 = SizedBox()
    //     0xba42b4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba42b8: mov             x1, x0
    // 0xba42bc: r0 = inf
    //     0xba42bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xba42c0: ldr             x0, [x0, #0x9f8]
    // 0xba42c4: stur            x1, [fp, #-0x10]
    // 0xba42c8: StoreField: r1->field_f = r0
    //     0xba42c8: stur            w0, [x1, #0xf]
    // 0xba42cc: r0 = 44.000000
    //     0xba42cc: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xba42d0: ldr             x0, [x0, #0xad8]
    // 0xba42d4: StoreField: r1->field_13 = r0
    //     0xba42d4: stur            w0, [x1, #0x13]
    // 0xba42d8: ldur            x0, [fp, #-8]
    // 0xba42dc: StoreField: r1->field_b = r0
    //     0xba42dc: stur            w0, [x1, #0xb]
    // 0xba42e0: r0 = Padding()
    //     0xba42e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba42e4: mov             x3, x0
    // 0xba42e8: r0 = Instance_EdgeInsets
    //     0xba42e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0xba42ec: ldr             x0, [x0, #0xe68]
    // 0xba42f0: stur            x3, [fp, #-8]
    // 0xba42f4: StoreField: r3->field_f = r0
    //     0xba42f4: stur            w0, [x3, #0xf]
    // 0xba42f8: ldur            x0, [fp, #-0x10]
    // 0xba42fc: StoreField: r3->field_b = r0
    //     0xba42fc: stur            w0, [x3, #0xb]
    // 0xba4300: r1 = Null
    //     0xba4300: mov             x1, NULL
    // 0xba4304: r2 = 6
    //     0xba4304: movz            x2, #0x6
    // 0xba4308: r0 = AllocateArray()
    //     0xba4308: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba430c: mov             x2, x0
    // 0xba4310: ldur            x0, [fp, #-0x28]
    // 0xba4314: stur            x2, [fp, #-0x10]
    // 0xba4318: StoreField: r2->field_f = r0
    //     0xba4318: stur            w0, [x2, #0xf]
    // 0xba431c: ldur            x0, [fp, #-0x20]
    // 0xba4320: StoreField: r2->field_13 = r0
    //     0xba4320: stur            w0, [x2, #0x13]
    // 0xba4324: ldur            x0, [fp, #-8]
    // 0xba4328: ArrayStore: r2[0] = r0  ; List_4
    //     0xba4328: stur            w0, [x2, #0x17]
    // 0xba432c: r1 = <Widget>
    //     0xba432c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba4330: r0 = AllocateGrowableArray()
    //     0xba4330: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba4334: mov             x1, x0
    // 0xba4338: ldur            x0, [fp, #-0x10]
    // 0xba433c: stur            x1, [fp, #-8]
    // 0xba4340: StoreField: r1->field_f = r0
    //     0xba4340: stur            w0, [x1, #0xf]
    // 0xba4344: r0 = 6
    //     0xba4344: movz            x0, #0x6
    // 0xba4348: StoreField: r1->field_b = r0
    //     0xba4348: stur            w0, [x1, #0xb]
    // 0xba434c: r0 = Column()
    //     0xba434c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba4350: mov             x1, x0
    // 0xba4354: r0 = Instance_Axis
    //     0xba4354: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba4358: stur            x1, [fp, #-0x10]
    // 0xba435c: StoreField: r1->field_f = r0
    //     0xba435c: stur            w0, [x1, #0xf]
    // 0xba4360: r0 = Instance_MainAxisAlignment
    //     0xba4360: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba4364: ldr             x0, [x0, #0xa08]
    // 0xba4368: StoreField: r1->field_13 = r0
    //     0xba4368: stur            w0, [x1, #0x13]
    // 0xba436c: r0 = Instance_MainAxisSize
    //     0xba436c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba4370: ldr             x0, [x0, #0xa10]
    // 0xba4374: ArrayStore: r1[0] = r0  ; List_4
    //     0xba4374: stur            w0, [x1, #0x17]
    // 0xba4378: r0 = Instance_CrossAxisAlignment
    //     0xba4378: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba437c: ldr             x0, [x0, #0x890]
    // 0xba4380: StoreField: r1->field_1b = r0
    //     0xba4380: stur            w0, [x1, #0x1b]
    // 0xba4384: r0 = Instance_VerticalDirection
    //     0xba4384: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba4388: ldr             x0, [x0, #0xa20]
    // 0xba438c: StoreField: r1->field_23 = r0
    //     0xba438c: stur            w0, [x1, #0x23]
    // 0xba4390: r0 = Instance_Clip
    //     0xba4390: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba4394: ldr             x0, [x0, #0x38]
    // 0xba4398: StoreField: r1->field_2b = r0
    //     0xba4398: stur            w0, [x1, #0x2b]
    // 0xba439c: StoreField: r1->field_2f = rZR
    //     0xba439c: stur            xzr, [x1, #0x2f]
    // 0xba43a0: ldur            x0, [fp, #-8]
    // 0xba43a4: StoreField: r1->field_b = r0
    //     0xba43a4: stur            w0, [x1, #0xb]
    // 0xba43a8: r0 = SizedBox()
    //     0xba43a8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba43ac: r1 = 120.000000
    //     0xba43ac: add             x1, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xba43b0: ldr             x1, [x1, #0x3a0]
    // 0xba43b4: StoreField: r0->field_13 = r1
    //     0xba43b4: stur            w1, [x0, #0x13]
    // 0xba43b8: ldur            x1, [fp, #-0x10]
    // 0xba43bc: StoreField: r0->field_b = r1
    //     0xba43bc: stur            w1, [x0, #0xb]
    // 0xba43c0: LeaveFrame
    //     0xba43c0: mov             SP, fp
    //     0xba43c4: ldp             fp, lr, [SP], #0x10
    // 0xba43c8: ret
    //     0xba43c8: ret             
    // 0xba43cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba43cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba43d0: b               #0xba3b50
    // 0xba43d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba43d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba43d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba43d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba43dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba43dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba43e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba43e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xba4404, size: 0xa0
    // 0xba4404: EnterFrame
    //     0xba4404: stp             fp, lr, [SP, #-0x10]!
    //     0xba4408: mov             fp, SP
    // 0xba440c: AllocStack(0x10)
    //     0xba440c: sub             SP, SP, #0x10
    // 0xba4410: SetupParameters()
    //     0xba4410: ldr             x0, [fp, #0x10]
    //     0xba4414: ldur            w1, [x0, #0x17]
    //     0xba4418: add             x1, x1, HEAP, lsl #32
    // 0xba441c: CheckStackOverflow
    //     0xba441c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba4420: cmp             SP, x16
    //     0xba4424: b.ls            #0xba4498
    // 0xba4428: LoadField: r0 = r1->field_f
    //     0xba4428: ldur            w0, [x1, #0xf]
    // 0xba442c: DecompressPointer r0
    //     0xba442c: add             x0, x0, HEAP, lsl #32
    // 0xba4430: LoadField: r1 = r0->field_b
    //     0xba4430: ldur            w1, [x0, #0xb]
    // 0xba4434: DecompressPointer r1
    //     0xba4434: add             x1, x1, HEAP, lsl #32
    // 0xba4438: cmp             w1, NULL
    // 0xba443c: b.eq            #0xba44a0
    // 0xba4440: LoadField: r0 = r1->field_b
    //     0xba4440: ldur            w0, [x1, #0xb]
    // 0xba4444: DecompressPointer r0
    //     0xba4444: add             x0, x0, HEAP, lsl #32
    // 0xba4448: cmp             w0, NULL
    // 0xba444c: b.ne            #0xba4458
    // 0xba4450: r0 = Null
    //     0xba4450: mov             x0, NULL
    // 0xba4454: b               #0xba4464
    // 0xba4458: LoadField: r2 = r0->field_b
    //     0xba4458: ldur            w2, [x0, #0xb]
    // 0xba445c: DecompressPointer r2
    //     0xba445c: add             x2, x2, HEAP, lsl #32
    // 0xba4460: mov             x0, x2
    // 0xba4464: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xba4464: ldur            w2, [x1, #0x17]
    // 0xba4468: DecompressPointer r2
    //     0xba4468: add             x2, x2, HEAP, lsl #32
    // 0xba446c: stp             x0, x2, [SP]
    // 0xba4470: r4 = 0
    //     0xba4470: movz            x4, #0
    // 0xba4474: ldr             x0, [SP, #8]
    // 0xba4478: r16 = UnlinkedCall_0x613b5c
    //     0xba4478: add             x16, PP, #0x54, lsl #12  ; [pp+0x54ba0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba447c: add             x16, x16, #0xba0
    // 0xba4480: ldp             x5, lr, [x16]
    // 0xba4484: blr             lr
    // 0xba4488: r0 = Null
    //     0xba4488: mov             x0, NULL
    // 0xba448c: LeaveFrame
    //     0xba448c: mov             SP, fp
    //     0xba4490: ldp             fp, lr, [SP], #0x10
    // 0xba4494: ret
    //     0xba4494: ret             
    // 0xba4498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba4498: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba449c: b               #0xba4428
    // 0xba44a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba44a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4033, size: 0x20, field offset: 0xc
//   const constructor, 
class BottomView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ff88, size: 0x24
    // 0xc7ff88: EnterFrame
    //     0xc7ff88: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ff8c: mov             fp, SP
    // 0xc7ff90: mov             x0, x1
    // 0xc7ff94: r1 = <BottomView>
    //     0xc7ff94: add             x1, PP, #0x48, lsl #12  ; [pp+0x486e0] TypeArguments: <BottomView>
    //     0xc7ff98: ldr             x1, [x1, #0x6e0]
    // 0xc7ff9c: r0 = _BottomViewState()
    //     0xc7ff9c: bl              #0xc7ffac  ; Allocate_BottomViewStateStub -> _BottomViewState (size=0x14)
    // 0xc7ffa0: LeaveFrame
    //     0xc7ffa0: mov             SP, fp
    //     0xc7ffa4: ldp             fp, lr, [SP], #0x10
    // 0xc7ffa8: ret
    //     0xc7ffa8: ret             
  }
}
