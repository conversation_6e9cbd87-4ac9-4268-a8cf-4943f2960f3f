// lib: , url: package:customer_app/app/presentation/views/line/collections/collection_page.dart

// class id: 1049502, size: 0x8
class :: {
}

// class id: 4540, size: 0x2c, field offset: 0x14
class CollectionPage extends BaseView<dynamic> {

  [closure] Null <anonymous closure>(dynamic, String, double, Map<String, dynamic>) {
    // ** addr: 0x13afaf8, size: 0x88
    // 0x13afaf8: EnterFrame
    //     0x13afaf8: stp             fp, lr, [SP, #-0x10]!
    //     0x13afafc: mov             fp, SP
    // 0x13afb00: AllocStack(0x28)
    //     0x13afb00: sub             SP, SP, #0x28
    // 0x13afb04: SetupParameters()
    //     0x13afb04: ldr             x0, [fp, #0x28]
    //     0x13afb08: ldur            w1, [x0, #0x17]
    //     0x13afb0c: add             x1, x1, HEAP, lsl #32
    // 0x13afb10: CheckStackOverflow
    //     0x13afb10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13afb14: cmp             SP, x16
    //     0x13afb18: b.ls            #0x13afb78
    // 0x13afb1c: LoadField: r0 = r1->field_f
    //     0x13afb1c: ldur            w0, [x1, #0xf]
    // 0x13afb20: DecompressPointer r0
    //     0x13afb20: add             x0, x0, HEAP, lsl #32
    // 0x13afb24: mov             x1, x0
    // 0x13afb28: r0 = controller()
    //     0x13afb28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13afb2c: LoadField: r1 = r0->field_4f
    //     0x13afb2c: ldur            w1, [x0, #0x4f]
    // 0x13afb30: DecompressPointer r1
    //     0x13afb30: add             x1, x1, HEAP, lsl #32
    // 0x13afb34: ldr             x16, [fp, #0x20]
    // 0x13afb38: r30 = "product"
    //     0x13afb38: add             lr, PP, #0x12, lsl #12  ; [pp+0x12240] "product"
    //     0x13afb3c: ldr             lr, [lr, #0x240]
    // 0x13afb40: stp             lr, x16, [SP, #0x18]
    // 0x13afb44: r16 = "INR"
    //     0x13afb44: add             x16, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0x13afb48: ldr             x16, [x16, #0x4c0]
    // 0x13afb4c: ldr             lr, [fp, #0x18]
    // 0x13afb50: stp             lr, x16, [SP, #8]
    // 0x13afb54: ldr             x16, [fp, #0x10]
    // 0x13afb58: str             x16, [SP]
    // 0x13afb5c: r4 = const [0, 0x6, 0x5, 0x1, content, 0x5, currency, 0x3, id, 0x1, price, 0x4, type, 0x2, null]
    //     0x13afb5c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32318] List(15) [0, 0x6, 0x5, 0x1, "content", 0x5, "currency", 0x3, "id", 0x1, "price", 0x4, "type", 0x2, Null]
    //     0x13afb60: ldr             x4, [x4, #0x318]
    // 0x13afb64: r0 = logAddToCart()
    //     0x13afb64: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0x13afb68: r0 = Null
    //     0x13afb68: mov             x0, NULL
    // 0x13afb6c: LeaveFrame
    //     0x13afb6c: mov             SP, fp
    //     0x13afb70: ldp             fp, lr, [SP], #0x10
    // 0x13afb74: ret
    //     0x13afb74: ret             
    // 0x13afb78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13afb78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13afb7c: b               #0x13afb1c
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x13afb80, size: 0x1ec
    // 0x13afb80: EnterFrame
    //     0x13afb80: stp             fp, lr, [SP, #-0x10]!
    //     0x13afb84: mov             fp, SP
    // 0x13afb88: AllocStack(0x38)
    //     0x13afb88: sub             SP, SP, #0x38
    // 0x13afb8c: SetupParameters()
    //     0x13afb8c: ldr             x0, [fp, #0x18]
    //     0x13afb90: ldur            w2, [x0, #0x17]
    //     0x13afb94: add             x2, x2, HEAP, lsl #32
    //     0x13afb98: stur            x2, [fp, #-0x18]
    // 0x13afb9c: CheckStackOverflow
    //     0x13afb9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13afba0: cmp             SP, x16
    //     0x13afba4: b.ls            #0x13afd64
    // 0x13afba8: LoadField: r0 = r2->field_13
    //     0x13afba8: ldur            w0, [x2, #0x13]
    // 0x13afbac: DecompressPointer r0
    //     0x13afbac: add             x0, x0, HEAP, lsl #32
    // 0x13afbb0: stur            x0, [fp, #-0x10]
    // 0x13afbb4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x13afbb4: ldur            w3, [x2, #0x17]
    // 0x13afbb8: DecompressPointer r3
    //     0x13afbb8: add             x3, x3, HEAP, lsl #32
    // 0x13afbbc: stur            x3, [fp, #-8]
    // 0x13afbc0: LoadField: r1 = r2->field_f
    //     0x13afbc0: ldur            w1, [x2, #0xf]
    // 0x13afbc4: DecompressPointer r1
    //     0x13afbc4: add             x1, x1, HEAP, lsl #32
    // 0x13afbc8: r0 = controller()
    //     0x13afbc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13afbcc: ldur            x2, [fp, #-0x18]
    // 0x13afbd0: stur            x0, [fp, #-0x28]
    // 0x13afbd4: LoadField: r3 = r2->field_1b
    //     0x13afbd4: ldur            w3, [x2, #0x1b]
    // 0x13afbd8: DecompressPointer r3
    //     0x13afbd8: add             x3, x3, HEAP, lsl #32
    // 0x13afbdc: stur            x3, [fp, #-0x20]
    // 0x13afbe0: LoadField: r1 = r2->field_f
    //     0x13afbe0: ldur            w1, [x2, #0xf]
    // 0x13afbe4: DecompressPointer r1
    //     0x13afbe4: add             x1, x1, HEAP, lsl #32
    // 0x13afbe8: r0 = controller()
    //     0x13afbe8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13afbec: LoadField: r1 = r0->field_7f
    //     0x13afbec: ldur            w1, [x0, #0x7f]
    // 0x13afbf0: DecompressPointer r1
    //     0x13afbf0: add             x1, x1, HEAP, lsl #32
    // 0x13afbf4: r0 = value()
    //     0x13afbf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13afbf8: LoadField: r1 = r0->field_67
    //     0x13afbf8: ldur            w1, [x0, #0x67]
    // 0x13afbfc: DecompressPointer r1
    //     0x13afbfc: add             x1, x1, HEAP, lsl #32
    // 0x13afc00: cmp             w1, NULL
    // 0x13afc04: b.ne            #0x13afc10
    // 0x13afc08: r0 = Null
    //     0x13afc08: mov             x0, NULL
    // 0x13afc0c: b               #0x13afc48
    // 0x13afc10: LoadField: r0 = r1->field_7
    //     0x13afc10: ldur            w0, [x1, #7]
    // 0x13afc14: DecompressPointer r0
    //     0x13afc14: add             x0, x0, HEAP, lsl #32
    // 0x13afc18: cmp             w0, NULL
    // 0x13afc1c: b.ne            #0x13afc28
    // 0x13afc20: r0 = Null
    //     0x13afc20: mov             x0, NULL
    // 0x13afc24: b               #0x13afc48
    // 0x13afc28: LoadField: r1 = r0->field_b
    //     0x13afc28: ldur            w1, [x0, #0xb]
    // 0x13afc2c: DecompressPointer r1
    //     0x13afc2c: add             x1, x1, HEAP, lsl #32
    // 0x13afc30: cmp             w1, NULL
    // 0x13afc34: b.ne            #0x13afc40
    // 0x13afc38: r0 = Null
    //     0x13afc38: mov             x0, NULL
    // 0x13afc3c: b               #0x13afc48
    // 0x13afc40: LoadField: r0 = r1->field_7
    //     0x13afc40: ldur            w0, [x1, #7]
    // 0x13afc44: DecompressPointer r0
    //     0x13afc44: add             x0, x0, HEAP, lsl #32
    // 0x13afc48: cmp             w0, NULL
    // 0x13afc4c: b.ne            #0x13afc58
    // 0x13afc50: r3 = false
    //     0x13afc50: add             x3, NULL, #0x30  ; false
    // 0x13afc54: b               #0x13afc5c
    // 0x13afc58: mov             x3, x0
    // 0x13afc5c: ldur            x1, [fp, #-0x10]
    // 0x13afc60: ldur            x2, [fp, #-8]
    // 0x13afc64: ldur            x0, [fp, #-0x20]
    // 0x13afc68: stur            x3, [fp, #-0x30]
    // 0x13afc6c: r0 = SelectSizeBottomSheet()
    //     0x13afc6c: bl              #0x9c3384  ; AllocateSelectSizeBottomSheetStub -> SelectSizeBottomSheet (size=0x2c)
    // 0x13afc70: mov             x3, x0
    // 0x13afc74: ldur            x0, [fp, #-0x10]
    // 0x13afc78: stur            x3, [fp, #-0x38]
    // 0x13afc7c: StoreField: r3->field_b = r0
    //     0x13afc7c: stur            w0, [x3, #0xb]
    // 0x13afc80: ldur            x0, [fp, #-8]
    // 0x13afc84: StoreField: r3->field_f = r0
    //     0x13afc84: stur            w0, [x3, #0xf]
    // 0x13afc88: ldur            x2, [fp, #-0x28]
    // 0x13afc8c: r1 = Function 'postEvents':.
    //     0x13afc8c: add             x1, PP, #0x32, lsl #12  ; [pp+0x321b0] AnonymousClosure: (0x89c5cc), in [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents (0x8608dc)
    //     0x13afc90: ldr             x1, [x1, #0x1b0]
    // 0x13afc94: r0 = AllocateClosure()
    //     0x13afc94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13afc98: mov             x1, x0
    // 0x13afc9c: ldur            x0, [fp, #-0x38]
    // 0x13afca0: StoreField: r0->field_13 = r1
    //     0x13afca0: stur            w1, [x0, #0x13]
    // 0x13afca4: ldur            x1, [fp, #-0x20]
    // 0x13afca8: ArrayStore: r0[0] = r1  ; List_4
    //     0x13afca8: stur            w1, [x0, #0x17]
    // 0x13afcac: ldur            x2, [fp, #-0x18]
    // 0x13afcb0: r1 = Function '<anonymous closure>':.
    //     0x13afcb0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c160] AnonymousClosure: (0x13b0548), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x13afcb4: ldr             x1, [x1, #0x160]
    // 0x13afcb8: r0 = AllocateClosure()
    //     0x13afcb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13afcbc: mov             x1, x0
    // 0x13afcc0: ldur            x0, [fp, #-0x38]
    // 0x13afcc4: StoreField: r0->field_1b = r1
    //     0x13afcc4: stur            w1, [x0, #0x1b]
    // 0x13afcc8: ldur            x2, [fp, #-0x18]
    // 0x13afccc: r1 = Function '<anonymous closure>':.
    //     0x13afccc: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c168] AnonymousClosure: (0x13afd6c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x13afcd0: ldr             x1, [x1, #0x168]
    // 0x13afcd4: r0 = AllocateClosure()
    //     0x13afcd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13afcd8: mov             x1, x0
    // 0x13afcdc: ldur            x0, [fp, #-0x38]
    // 0x13afce0: StoreField: r0->field_1f = r1
    //     0x13afce0: stur            w1, [x0, #0x1f]
    // 0x13afce4: ldur            x2, [fp, #-0x18]
    // 0x13afce8: r1 = Function '<anonymous closure>':.
    //     0x13afce8: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c170] AnonymousClosure: (0x13afaf8), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x13afcec: ldr             x1, [x1, #0x170]
    // 0x13afcf0: r0 = AllocateClosure()
    //     0x13afcf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13afcf4: mov             x1, x0
    // 0x13afcf8: ldur            x0, [fp, #-0x38]
    // 0x13afcfc: StoreField: r0->field_23 = r1
    //     0x13afcfc: stur            w1, [x0, #0x23]
    // 0x13afd00: ldur            x1, [fp, #-0x30]
    // 0x13afd04: StoreField: r0->field_27 = r1
    //     0x13afd04: stur            w1, [x0, #0x27]
    // 0x13afd08: r0 = SafeArea()
    //     0x13afd08: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x13afd0c: mov             x1, x0
    // 0x13afd10: r0 = true
    //     0x13afd10: add             x0, NULL, #0x20  ; true
    // 0x13afd14: stur            x1, [fp, #-8]
    // 0x13afd18: StoreField: r1->field_b = r0
    //     0x13afd18: stur            w0, [x1, #0xb]
    // 0x13afd1c: StoreField: r1->field_f = r0
    //     0x13afd1c: stur            w0, [x1, #0xf]
    // 0x13afd20: StoreField: r1->field_13 = r0
    //     0x13afd20: stur            w0, [x1, #0x13]
    // 0x13afd24: ArrayStore: r1[0] = r0  ; List_4
    //     0x13afd24: stur            w0, [x1, #0x17]
    // 0x13afd28: r0 = Instance_EdgeInsets
    //     0x13afd28: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x13afd2c: StoreField: r1->field_1b = r0
    //     0x13afd2c: stur            w0, [x1, #0x1b]
    // 0x13afd30: r0 = false
    //     0x13afd30: add             x0, NULL, #0x30  ; false
    // 0x13afd34: StoreField: r1->field_1f = r0
    //     0x13afd34: stur            w0, [x1, #0x1f]
    // 0x13afd38: ldur            x0, [fp, #-0x38]
    // 0x13afd3c: StoreField: r1->field_23 = r0
    //     0x13afd3c: stur            w0, [x1, #0x23]
    // 0x13afd40: r0 = Padding()
    //     0x13afd40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13afd44: r1 = Instance_EdgeInsets
    //     0x13afd44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x13afd48: ldr             x1, [x1, #0x980]
    // 0x13afd4c: StoreField: r0->field_f = r1
    //     0x13afd4c: stur            w1, [x0, #0xf]
    // 0x13afd50: ldur            x1, [fp, #-8]
    // 0x13afd54: StoreField: r0->field_b = r1
    //     0x13afd54: stur            w1, [x0, #0xb]
    // 0x13afd58: LeaveFrame
    //     0x13afd58: mov             SP, fp
    //     0x13afd5c: ldp             fp, lr, [SP], #0x10
    // 0x13afd60: ret
    //     0x13afd60: ret             
    // 0x13afd64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13afd64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13afd68: b               #0x13afba8
  }
  [closure] Null <anonymous closure>(dynamic, String, bool, String, String, int, String) {
    // ** addr: 0x13afd6c, size: 0x70
    // 0x13afd6c: EnterFrame
    //     0x13afd6c: stp             fp, lr, [SP, #-0x10]!
    //     0x13afd70: mov             fp, SP
    // 0x13afd74: ldr             x0, [fp, #0x40]
    // 0x13afd78: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13afd78: ldur            w1, [x0, #0x17]
    // 0x13afd7c: DecompressPointer r1
    //     0x13afd7c: add             x1, x1, HEAP, lsl #32
    // 0x13afd80: CheckStackOverflow
    //     0x13afd80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13afd84: cmp             SP, x16
    //     0x13afd88: b.ls            #0x13afdd4
    // 0x13afd8c: LoadField: r0 = r1->field_f
    //     0x13afd8c: ldur            w0, [x1, #0xf]
    // 0x13afd90: DecompressPointer r0
    //     0x13afd90: add             x0, x0, HEAP, lsl #32
    // 0x13afd94: mov             x1, x0
    // 0x13afd98: r0 = controller()
    //     0x13afd98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13afd9c: mov             x1, x0
    // 0x13afda0: ldr             x0, [fp, #0x18]
    // 0x13afda4: r6 = LoadInt32Instr(r0)
    //     0x13afda4: sbfx            x6, x0, #1, #0x1f
    //     0x13afda8: tbz             w0, #0, #0x13afdb0
    //     0x13afdac: ldur            x6, [x0, #7]
    // 0x13afdb0: ldr             x2, [fp, #0x38]
    // 0x13afdb4: ldr             x3, [fp, #0x30]
    // 0x13afdb8: ldr             x5, [fp, #0x28]
    // 0x13afdbc: ldr             x7, [fp, #0x20]
    // 0x13afdc0: r0 = customisedResponse()
    //     0x13afdc0: bl              #0x13afddc  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::customisedResponse
    // 0x13afdc4: r0 = Null
    //     0x13afdc4: mov             x0, NULL
    // 0x13afdc8: LeaveFrame
    //     0x13afdc8: mov             SP, fp
    //     0x13afdcc: ldp             fp, lr, [SP], #0x10
    // 0x13afdd0: ret
    //     0x13afdd0: ret             
    // 0x13afdd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13afdd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13afdd8: b               #0x13afd8c
  }
  [closure] Null <anonymous closure>(dynamic, String, String, int, {String? customisationId}) {
    // ** addr: 0x13b0548, size: 0xe4
    // 0x13b0548: EnterFrame
    //     0x13b0548: stp             fp, lr, [SP, #-0x10]!
    //     0x13b054c: mov             fp, SP
    // 0x13b0550: AllocStack(0x20)
    //     0x13b0550: sub             SP, SP, #0x20
    // 0x13b0554: SetupParameters(CollectionPage this /* r2 */, dynamic _ /* r3, fp-0x18 */, dynamic _ /* r5, fp-0x10 */, {dynamic customisationId = Null /* r0, fp-0x8 */})
    //     0x13b0554: ldur            w0, [x4, #0x13]
    //     0x13b0558: sub             x1, x0, #8
    //     0x13b055c: add             x2, fp, w1, sxtw #2
    //     0x13b0560: ldr             x2, [x2, #0x28]
    //     0x13b0564: add             x3, fp, w1, sxtw #2
    //     0x13b0568: ldr             x3, [x3, #0x20]
    //     0x13b056c: stur            x3, [fp, #-0x18]
    //     0x13b0570: add             x5, fp, w1, sxtw #2
    //     0x13b0574: ldr             x5, [x5, #0x18]
    //     0x13b0578: stur            x5, [fp, #-0x10]
    //     0x13b057c: ldur            w1, [x4, #0x1f]
    //     0x13b0580: add             x1, x1, HEAP, lsl #32
    //     0x13b0584: add             x16, PP, #0x34, lsl #12  ; [pp+0x34390] "customisationId"
    //     0x13b0588: ldr             x16, [x16, #0x390]
    //     0x13b058c: cmp             w1, w16
    //     0x13b0590: b.ne            #0x13b05ac
    //     0x13b0594: ldur            w1, [x4, #0x23]
    //     0x13b0598: add             x1, x1, HEAP, lsl #32
    //     0x13b059c: sub             w4, w0, w1
    //     0x13b05a0: add             x0, fp, w4, sxtw #2
    //     0x13b05a4: ldr             x0, [x0, #8]
    //     0x13b05a8: b               #0x13b05b0
    //     0x13b05ac: mov             x0, NULL
    //     0x13b05b0: stur            x0, [fp, #-8]
    //     0x13b05b4: ldur            w1, [x2, #0x17]
    //     0x13b05b8: add             x1, x1, HEAP, lsl #32
    // 0x13b05bc: CheckStackOverflow
    //     0x13b05bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b05c0: cmp             SP, x16
    //     0x13b05c4: b.ls            #0x13b0624
    // 0x13b05c8: LoadField: r2 = r1->field_f
    //     0x13b05c8: ldur            w2, [x1, #0xf]
    // 0x13b05cc: DecompressPointer r2
    //     0x13b05cc: add             x2, x2, HEAP, lsl #32
    // 0x13b05d0: mov             x1, x2
    // 0x13b05d4: r0 = controller()
    //     0x13b05d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b05d8: stur            x0, [fp, #-0x20]
    // 0x13b05dc: r0 = AddToBagRequest()
    //     0x13b05dc: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0x13b05e0: mov             x1, x0
    // 0x13b05e4: ldur            x0, [fp, #-0x18]
    // 0x13b05e8: StoreField: r1->field_7 = r0
    //     0x13b05e8: stur            w0, [x1, #7]
    // 0x13b05ec: ldur            x0, [fp, #-0x10]
    // 0x13b05f0: StoreField: r1->field_b = r0
    //     0x13b05f0: stur            w0, [x1, #0xb]
    // 0x13b05f4: r0 = 1
    //     0x13b05f4: movz            x0, #0x1
    // 0x13b05f8: StoreField: r1->field_f = r0
    //     0x13b05f8: stur            x0, [x1, #0xf]
    // 0x13b05fc: ldur            x0, [fp, #-8]
    // 0x13b0600: ArrayStore: r1[0] = r0  ; List_4
    //     0x13b0600: stur            w0, [x1, #0x17]
    // 0x13b0604: mov             x2, x1
    // 0x13b0608: ldur            x1, [fp, #-0x20]
    // 0x13b060c: r3 = false
    //     0x13b060c: add             x3, NULL, #0x30  ; false
    // 0x13b0610: r0 = addToBag()
    //     0x13b0610: bl              #0x91db58  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::addToBag
    // 0x13b0614: r0 = Null
    //     0x13b0614: mov             x0, NULL
    // 0x13b0618: LeaveFrame
    //     0x13b0618: mov             SP, fp
    //     0x13b061c: ldp             fp, lr, [SP], #0x10
    // 0x13b0620: ret
    //     0x13b0620: ret             
    // 0x13b0624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b0624: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b0628: b               #0x13b05c8
  }
  _ showSizeBottomSheet(/* No info */) {
    // ** addr: 0x13b062c, size: 0xa0
    // 0x13b062c: EnterFrame
    //     0x13b062c: stp             fp, lr, [SP, #-0x10]!
    //     0x13b0630: mov             fp, SP
    // 0x13b0634: AllocStack(0x48)
    //     0x13b0634: sub             SP, SP, #0x48
    // 0x13b0638: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x13b0638: stur            x1, [fp, #-8]
    //     0x13b063c: stur            x2, [fp, #-0x10]
    //     0x13b0640: stur            x3, [fp, #-0x18]
    //     0x13b0644: stur            x5, [fp, #-0x20]
    //     0x13b0648: stur            x6, [fp, #-0x28]
    // 0x13b064c: CheckStackOverflow
    //     0x13b064c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b0650: cmp             SP, x16
    //     0x13b0654: b.ls            #0x13b06c4
    // 0x13b0658: r1 = 4
    //     0x13b0658: movz            x1, #0x4
    // 0x13b065c: r0 = AllocateContext()
    //     0x13b065c: bl              #0x16f6108  ; AllocateContextStub
    // 0x13b0660: mov             x1, x0
    // 0x13b0664: ldur            x0, [fp, #-8]
    // 0x13b0668: StoreField: r1->field_f = r0
    //     0x13b0668: stur            w0, [x1, #0xf]
    // 0x13b066c: ldur            x0, [fp, #-0x10]
    // 0x13b0670: StoreField: r1->field_13 = r0
    //     0x13b0670: stur            w0, [x1, #0x13]
    // 0x13b0674: ldur            x0, [fp, #-0x18]
    // 0x13b0678: ArrayStore: r1[0] = r0  ; List_4
    //     0x13b0678: stur            w0, [x1, #0x17]
    // 0x13b067c: ldur            x0, [fp, #-0x20]
    // 0x13b0680: StoreField: r1->field_1b = r0
    //     0x13b0680: stur            w0, [x1, #0x1b]
    // 0x13b0684: mov             x2, x1
    // 0x13b0688: r1 = Function '<anonymous closure>':.
    //     0x13b0688: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c158] AnonymousClosure: (0x13afb80), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x13b068c: ldr             x1, [x1, #0x158]
    // 0x13b0690: r0 = AllocateClosure()
    //     0x13b0690: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0694: stp             x0, NULL, [SP, #0x10]
    // 0x13b0698: ldur            x16, [fp, #-0x28]
    // 0x13b069c: r30 = Instance_RoundedRectangleBorder
    //     0x13b069c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x13b06a0: ldr             lr, [lr, #0xd68]
    // 0x13b06a4: stp             lr, x16, [SP]
    // 0x13b06a8: r4 = const [0x1, 0x3, 0x3, 0x2, shape, 0x2, null]
    //     0x13b06a8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fea0] List(7) [0x1, 0x3, 0x3, 0x2, "shape", 0x2, Null]
    //     0x13b06ac: ldr             x4, [x4, #0xea0]
    // 0x13b06b0: r0 = showModalBottomSheet()
    //     0x13b06b0: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x13b06b4: r0 = Null
    //     0x13b06b4: mov             x0, NULL
    // 0x13b06b8: LeaveFrame
    //     0x13b06b8: mov             SP, fp
    //     0x13b06bc: ldp             fp, lr, [SP], #0x10
    // 0x13b06c0: ret
    //     0x13b06c0: ret             
    // 0x13b06c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b06c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b06c8: b               #0x13b0658
  }
  [closure] Null <anonymous closure>(dynamic, String, int, String) {
    // ** addr: 0x13b3e3c, size: 0x98
    // 0x13b3e3c: EnterFrame
    //     0x13b3e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x13b3e40: mov             fp, SP
    // 0x13b3e44: AllocStack(0x10)
    //     0x13b3e44: sub             SP, SP, #0x10
    // 0x13b3e48: SetupParameters()
    //     0x13b3e48: ldr             x0, [fp, #0x28]
    //     0x13b3e4c: ldur            w2, [x0, #0x17]
    //     0x13b3e50: add             x2, x2, HEAP, lsl #32
    //     0x13b3e54: stur            x2, [fp, #-8]
    // 0x13b3e58: CheckStackOverflow
    //     0x13b3e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b3e5c: cmp             SP, x16
    //     0x13b3e60: b.ls            #0x13b3ecc
    // 0x13b3e64: LoadField: r1 = r2->field_f
    //     0x13b3e64: ldur            w1, [x2, #0xf]
    // 0x13b3e68: DecompressPointer r1
    //     0x13b3e68: add             x1, x1, HEAP, lsl #32
    // 0x13b3e6c: r0 = controller()
    //     0x13b3e6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3e70: mov             x1, x0
    // 0x13b3e74: ldr             x0, [fp, #0x18]
    // 0x13b3e78: r4 = LoadInt32Instr(r0)
    //     0x13b3e78: sbfx            x4, x0, #1, #0x1f
    //     0x13b3e7c: tbz             w0, #0, #0x13b3e84
    //     0x13b3e80: ldur            x4, [x0, #7]
    // 0x13b3e84: ldr             x2, [fp, #0x20]
    // 0x13b3e88: mov             x3, x4
    // 0x13b3e8c: ldr             x5, [fp, #0x10]
    // 0x13b3e90: stur            x4, [fp, #-0x10]
    // 0x13b3e94: r0 = checkoutTriggeredPostEvent()
    //     0x13b3e94: bl              #0x13bba98  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::checkoutTriggeredPostEvent
    // 0x13b3e98: ldur            x0, [fp, #-8]
    // 0x13b3e9c: LoadField: r1 = r0->field_f
    //     0x13b3e9c: ldur            w1, [x0, #0xf]
    // 0x13b3ea0: DecompressPointer r1
    //     0x13b3ea0: add             x1, x1, HEAP, lsl #32
    // 0x13b3ea4: r0 = controller()
    //     0x13b3ea4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3ea8: mov             x1, x0
    // 0x13b3eac: ldr             x2, [fp, #0x20]
    // 0x13b3eb0: ldur            x3, [fp, #-0x10]
    // 0x13b3eb4: ldr             x5, [fp, #0x10]
    // 0x13b3eb8: r0 = checkoutStartedPostEvent()
    //     0x13b3eb8: bl              #0x13bb908  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::checkoutStartedPostEvent
    // 0x13b3ebc: r0 = Null
    //     0x13b3ebc: mov             x0, NULL
    // 0x13b3ec0: LeaveFrame
    //     0x13b3ec0: mov             SP, fp
    //     0x13b3ec4: ldp             fp, lr, [SP], #0x10
    // 0x13b3ec8: ret
    //     0x13b3ec8: ret             
    // 0x13b3ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b3ecc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b3ed0: b               #0x13b3e64
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x13b3ed4, size: 0x10c4
    // 0x13b3ed4: EnterFrame
    //     0x13b3ed4: stp             fp, lr, [SP, #-0x10]!
    //     0x13b3ed8: mov             fp, SP
    // 0x13b3edc: AllocStack(0xa8)
    //     0x13b3edc: sub             SP, SP, #0xa8
    // 0x13b3ee0: SetupParameters()
    //     0x13b3ee0: ldr             x0, [fp, #0x10]
    //     0x13b3ee4: ldur            w2, [x0, #0x17]
    //     0x13b3ee8: add             x2, x2, HEAP, lsl #32
    //     0x13b3eec: stur            x2, [fp, #-8]
    // 0x13b3ef0: CheckStackOverflow
    //     0x13b3ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b3ef4: cmp             SP, x16
    //     0x13b3ef8: b.ls            #0x13b4f78
    // 0x13b3efc: LoadField: r1 = r2->field_f
    //     0x13b3efc: ldur            w1, [x2, #0xf]
    // 0x13b3f00: DecompressPointer r1
    //     0x13b3f00: add             x1, x1, HEAP, lsl #32
    // 0x13b3f04: r0 = controller()
    //     0x13b3f04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3f08: LoadField: r1 = r0->field_67
    //     0x13b3f08: ldur            w1, [x0, #0x67]
    // 0x13b3f0c: DecompressPointer r1
    //     0x13b3f0c: add             x1, x1, HEAP, lsl #32
    // 0x13b3f10: r0 = value()
    //     0x13b3f10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b3f14: LoadField: r1 = r0->field_b
    //     0x13b3f14: ldur            w1, [x0, #0xb]
    // 0x13b3f18: DecompressPointer r1
    //     0x13b3f18: add             x1, x1, HEAP, lsl #32
    // 0x13b3f1c: cmp             w1, NULL
    // 0x13b3f20: b.ne            #0x13b3f40
    // 0x13b3f24: r0 = Container()
    //     0x13b3f24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13b3f28: mov             x1, x0
    // 0x13b3f2c: stur            x0, [fp, #-0x10]
    // 0x13b3f30: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13b3f30: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13b3f34: r0 = Container()
    //     0x13b3f34: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13b3f38: ldur            x0, [fp, #-0x10]
    // 0x13b3f3c: b               #0x13b4f6c
    // 0x13b3f40: ldur            x2, [fp, #-8]
    // 0x13b3f44: r0 = Obx()
    //     0x13b3f44: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13b3f48: ldur            x2, [fp, #-8]
    // 0x13b3f4c: r1 = Function '<anonymous closure>':.
    //     0x13b3f4c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c080] AnonymousClosure: (0x13ba9d0), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b3f50: ldr             x1, [x1, #0x80]
    // 0x13b3f54: stur            x0, [fp, #-0x10]
    // 0x13b3f58: r0 = AllocateClosure()
    //     0x13b3f58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b3f5c: mov             x1, x0
    // 0x13b3f60: ldur            x0, [fp, #-0x10]
    // 0x13b3f64: StoreField: r0->field_b = r1
    //     0x13b3f64: stur            w1, [x0, #0xb]
    // 0x13b3f68: r0 = Obx()
    //     0x13b3f68: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13b3f6c: ldur            x2, [fp, #-8]
    // 0x13b3f70: r1 = Function '<anonymous closure>':.
    //     0x13b3f70: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c088] AnonymousClosure: (0x13ba5f4), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b3f74: ldr             x1, [x1, #0x88]
    // 0x13b3f78: stur            x0, [fp, #-0x18]
    // 0x13b3f7c: r0 = AllocateClosure()
    //     0x13b3f7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b3f80: mov             x1, x0
    // 0x13b3f84: ldur            x0, [fp, #-0x18]
    // 0x13b3f88: StoreField: r0->field_b = r1
    //     0x13b3f88: stur            w1, [x0, #0xb]
    // 0x13b3f8c: r0 = Container()
    //     0x13b3f8c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13b3f90: stur            x0, [fp, #-0x20]
    // 0x13b3f94: r16 = 20.000000
    //     0x13b3f94: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x13b3f98: ldr             x16, [x16, #0xac8]
    // 0x13b3f9c: str             x16, [SP]
    // 0x13b3fa0: mov             x1, x0
    // 0x13b3fa4: r4 = const [0, 0x2, 0x1, 0x1, height, 0x1, null]
    //     0x13b3fa4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c090] List(7) [0, 0x2, 0x1, 0x1, "height", 0x1, Null]
    //     0x13b3fa8: ldr             x4, [x4, #0x90]
    // 0x13b3fac: r0 = Container()
    //     0x13b3fac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13b3fb0: ldur            x2, [fp, #-8]
    // 0x13b3fb4: LoadField: r1 = r2->field_f
    //     0x13b3fb4: ldur            w1, [x2, #0xf]
    // 0x13b3fb8: DecompressPointer r1
    //     0x13b3fb8: add             x1, x1, HEAP, lsl #32
    // 0x13b3fbc: r0 = controller()
    //     0x13b3fbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3fc0: LoadField: r1 = r0->field_67
    //     0x13b3fc0: ldur            w1, [x0, #0x67]
    // 0x13b3fc4: DecompressPointer r1
    //     0x13b3fc4: add             x1, x1, HEAP, lsl #32
    // 0x13b3fc8: r0 = value()
    //     0x13b3fc8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b3fcc: LoadField: r1 = r0->field_b
    //     0x13b3fcc: ldur            w1, [x0, #0xb]
    // 0x13b3fd0: DecompressPointer r1
    //     0x13b3fd0: add             x1, x1, HEAP, lsl #32
    // 0x13b3fd4: cmp             w1, NULL
    // 0x13b3fd8: b.ne            #0x13b3fe4
    // 0x13b3fdc: r0 = Null
    //     0x13b3fdc: mov             x0, NULL
    // 0x13b3fe0: b               #0x13b3fec
    // 0x13b3fe4: LoadField: r0 = r1->field_f
    //     0x13b3fe4: ldur            w0, [x1, #0xf]
    // 0x13b3fe8: DecompressPointer r0
    //     0x13b3fe8: add             x0, x0, HEAP, lsl #32
    // 0x13b3fec: cmp             w0, NULL
    // 0x13b3ff0: b.ne            #0x13b3ff8
    // 0x13b3ff4: r0 = ""
    //     0x13b3ff4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b3ff8: ldur            x2, [fp, #-8]
    // 0x13b3ffc: stur            x0, [fp, #-0x28]
    // 0x13b4000: LoadField: r1 = r2->field_13
    //     0x13b4000: ldur            w1, [x2, #0x13]
    // 0x13b4004: DecompressPointer r1
    //     0x13b4004: add             x1, x1, HEAP, lsl #32
    // 0x13b4008: r0 = of()
    //     0x13b4008: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13b400c: LoadField: r1 = r0->field_87
    //     0x13b400c: ldur            w1, [x0, #0x87]
    // 0x13b4010: DecompressPointer r1
    //     0x13b4010: add             x1, x1, HEAP, lsl #32
    // 0x13b4014: LoadField: r0 = r1->field_27
    //     0x13b4014: ldur            w0, [x1, #0x27]
    // 0x13b4018: DecompressPointer r0
    //     0x13b4018: add             x0, x0, HEAP, lsl #32
    // 0x13b401c: r16 = 21.000000
    //     0x13b401c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x13b4020: ldr             x16, [x16, #0x9b0]
    // 0x13b4024: r30 = Instance_Color
    //     0x13b4024: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b4028: stp             lr, x16, [SP]
    // 0x13b402c: mov             x1, x0
    // 0x13b4030: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13b4030: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13b4034: ldr             x4, [x4, #0xaa0]
    // 0x13b4038: r0 = copyWith()
    //     0x13b4038: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13b403c: stur            x0, [fp, #-0x30]
    // 0x13b4040: r0 = Text()
    //     0x13b4040: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13b4044: mov             x2, x0
    // 0x13b4048: ldur            x0, [fp, #-0x28]
    // 0x13b404c: stur            x2, [fp, #-0x38]
    // 0x13b4050: StoreField: r2->field_b = r0
    //     0x13b4050: stur            w0, [x2, #0xb]
    // 0x13b4054: ldur            x0, [fp, #-0x30]
    // 0x13b4058: StoreField: r2->field_13 = r0
    //     0x13b4058: stur            w0, [x2, #0x13]
    // 0x13b405c: r1 = <FlexParentData>
    //     0x13b405c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13b4060: ldr             x1, [x1, #0xe00]
    // 0x13b4064: r0 = Flexible()
    //     0x13b4064: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x13b4068: mov             x1, x0
    // 0x13b406c: r0 = 1
    //     0x13b406c: movz            x0, #0x1
    // 0x13b4070: stur            x1, [fp, #-0x28]
    // 0x13b4074: StoreField: r1->field_13 = r0
    //     0x13b4074: stur            x0, [x1, #0x13]
    // 0x13b4078: r2 = Instance_FlexFit
    //     0x13b4078: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x13b407c: ldr             x2, [x2, #0xe20]
    // 0x13b4080: StoreField: r1->field_1b = r2
    //     0x13b4080: stur            w2, [x1, #0x1b]
    // 0x13b4084: ldur            x3, [fp, #-0x38]
    // 0x13b4088: StoreField: r1->field_b = r3
    //     0x13b4088: stur            w3, [x1, #0xb]
    // 0x13b408c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13b408c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13b4090: ldr             x0, [x0, #0x1c80]
    //     0x13b4094: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13b4098: cmp             w0, w16
    //     0x13b409c: b.ne            #0x13b40a8
    //     0x13b40a0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13b40a4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13b40a8: r0 = GetNavigation.size()
    //     0x13b40a8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13b40ac: LoadField: d1 = r0->field_7
    //     0x13b40ac: ldur            d1, [x0, #7]
    // 0x13b40b0: stur            d1, [fp, #-0x78]
    // 0x13b40b4: r1 = Instance_Color
    //     0x13b40b4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b40b8: d0 = 0.100000
    //     0x13b40b8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13b40bc: r0 = withOpacity()
    //     0x13b40bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13b40c0: mov             x2, x0
    // 0x13b40c4: r1 = Null
    //     0x13b40c4: mov             x1, NULL
    // 0x13b40c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13b40c8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13b40cc: r0 = Border.all()
    //     0x13b40cc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13b40d0: stur            x0, [fp, #-0x30]
    // 0x13b40d4: r0 = BoxDecoration()
    //     0x13b40d4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13b40d8: mov             x1, x0
    // 0x13b40dc: ldur            x0, [fp, #-0x30]
    // 0x13b40e0: stur            x1, [fp, #-0x38]
    // 0x13b40e4: StoreField: r1->field_f = r0
    //     0x13b40e4: stur            w0, [x1, #0xf]
    // 0x13b40e8: r0 = Instance_BoxShape
    //     0x13b40e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13b40ec: ldr             x0, [x0, #0x80]
    // 0x13b40f0: StoreField: r1->field_23 = r0
    //     0x13b40f0: stur            w0, [x1, #0x23]
    // 0x13b40f4: r0 = SvgPicture()
    //     0x13b40f4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x13b40f8: stur            x0, [fp, #-0x30]
    // 0x13b40fc: r16 = 24.000000
    //     0x13b40fc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x13b4100: ldr             x16, [x16, #0xba8]
    // 0x13b4104: r30 = 24.000000
    //     0x13b4104: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x13b4108: ldr             lr, [lr, #0xba8]
    // 0x13b410c: stp             lr, x16, [SP]
    // 0x13b4110: mov             x1, x0
    // 0x13b4114: r2 = "assets/images/sliders.svg"
    //     0x13b4114: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c098] "assets/images/sliders.svg"
    //     0x13b4118: ldr             x2, [x2, #0x98]
    // 0x13b411c: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x13b411c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x13b4120: ldr             x4, [x4, #0x900]
    // 0x13b4124: r0 = SvgPicture.asset()
    //     0x13b4124: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x13b4128: r0 = WidgetSpan()
    //     0x13b4128: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0x13b412c: mov             x2, x0
    // 0x13b4130: ldur            x0, [fp, #-0x30]
    // 0x13b4134: stur            x2, [fp, #-0x40]
    // 0x13b4138: StoreField: r2->field_13 = r0
    //     0x13b4138: stur            w0, [x2, #0x13]
    // 0x13b413c: r0 = Instance_PlaceholderAlignment
    //     0x13b413c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0x13b4140: ldr             x0, [x0, #0xa0]
    // 0x13b4144: StoreField: r2->field_b = r0
    //     0x13b4144: stur            w0, [x2, #0xb]
    // 0x13b4148: ldur            x3, [fp, #-8]
    // 0x13b414c: LoadField: r1 = r3->field_13
    //     0x13b414c: ldur            w1, [x3, #0x13]
    // 0x13b4150: DecompressPointer r1
    //     0x13b4150: add             x1, x1, HEAP, lsl #32
    // 0x13b4154: r0 = of()
    //     0x13b4154: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13b4158: LoadField: r1 = r0->field_87
    //     0x13b4158: ldur            w1, [x0, #0x87]
    // 0x13b415c: DecompressPointer r1
    //     0x13b415c: add             x1, x1, HEAP, lsl #32
    // 0x13b4160: LoadField: r0 = r1->field_7
    //     0x13b4160: ldur            w0, [x1, #7]
    // 0x13b4164: DecompressPointer r0
    //     0x13b4164: add             x0, x0, HEAP, lsl #32
    // 0x13b4168: stur            x0, [fp, #-0x30]
    // 0x13b416c: r1 = Instance_Color
    //     0x13b416c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b4170: d0 = 0.700000
    //     0x13b4170: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13b4174: ldr             d0, [x17, #0xf48]
    // 0x13b4178: r0 = withOpacity()
    //     0x13b4178: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13b417c: r16 = 12.000000
    //     0x13b417c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13b4180: ldr             x16, [x16, #0x9e8]
    // 0x13b4184: stp             x16, x0, [SP]
    // 0x13b4188: ldur            x1, [fp, #-0x30]
    // 0x13b418c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13b418c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13b4190: ldr             x4, [x4, #0x9b8]
    // 0x13b4194: r0 = copyWith()
    //     0x13b4194: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13b4198: stur            x0, [fp, #-0x30]
    // 0x13b419c: r0 = Text()
    //     0x13b419c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13b41a0: mov             x1, x0
    // 0x13b41a4: r0 = " Filters"
    //     0x13b41a4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a8] " Filters"
    //     0x13b41a8: ldr             x0, [x0, #0xa8]
    // 0x13b41ac: stur            x1, [fp, #-0x48]
    // 0x13b41b0: StoreField: r1->field_b = r0
    //     0x13b41b0: stur            w0, [x1, #0xb]
    // 0x13b41b4: ldur            x0, [fp, #-0x30]
    // 0x13b41b8: StoreField: r1->field_13 = r0
    //     0x13b41b8: stur            w0, [x1, #0x13]
    // 0x13b41bc: r0 = WidgetSpan()
    //     0x13b41bc: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0x13b41c0: mov             x3, x0
    // 0x13b41c4: ldur            x0, [fp, #-0x48]
    // 0x13b41c8: stur            x3, [fp, #-0x30]
    // 0x13b41cc: StoreField: r3->field_13 = r0
    //     0x13b41cc: stur            w0, [x3, #0x13]
    // 0x13b41d0: r0 = Instance_PlaceholderAlignment
    //     0x13b41d0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0x13b41d4: ldr             x0, [x0, #0xa0]
    // 0x13b41d8: StoreField: r3->field_b = r0
    //     0x13b41d8: stur            w0, [x3, #0xb]
    // 0x13b41dc: r1 = Null
    //     0x13b41dc: mov             x1, NULL
    // 0x13b41e0: r2 = 4
    //     0x13b41e0: movz            x2, #0x4
    // 0x13b41e4: r0 = AllocateArray()
    //     0x13b41e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b41e8: mov             x2, x0
    // 0x13b41ec: ldur            x0, [fp, #-0x40]
    // 0x13b41f0: stur            x2, [fp, #-0x48]
    // 0x13b41f4: StoreField: r2->field_f = r0
    //     0x13b41f4: stur            w0, [x2, #0xf]
    // 0x13b41f8: ldur            x0, [fp, #-0x30]
    // 0x13b41fc: StoreField: r2->field_13 = r0
    //     0x13b41fc: stur            w0, [x2, #0x13]
    // 0x13b4200: r1 = <InlineSpan>
    //     0x13b4200: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x13b4204: ldr             x1, [x1, #0xe40]
    // 0x13b4208: r0 = AllocateGrowableArray()
    //     0x13b4208: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b420c: mov             x1, x0
    // 0x13b4210: ldur            x0, [fp, #-0x48]
    // 0x13b4214: stur            x1, [fp, #-0x30]
    // 0x13b4218: StoreField: r1->field_f = r0
    //     0x13b4218: stur            w0, [x1, #0xf]
    // 0x13b421c: r0 = 4
    //     0x13b421c: movz            x0, #0x4
    // 0x13b4220: StoreField: r1->field_b = r0
    //     0x13b4220: stur            w0, [x1, #0xb]
    // 0x13b4224: r0 = TextSpan()
    //     0x13b4224: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13b4228: mov             x1, x0
    // 0x13b422c: ldur            x0, [fp, #-0x30]
    // 0x13b4230: stur            x1, [fp, #-0x40]
    // 0x13b4234: StoreField: r1->field_f = r0
    //     0x13b4234: stur            w0, [x1, #0xf]
    // 0x13b4238: r0 = Instance__DeferringMouseCursor
    //     0x13b4238: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13b423c: ArrayStore: r1[0] = r0  ; List_4
    //     0x13b423c: stur            w0, [x1, #0x17]
    // 0x13b4240: r0 = RichText()
    //     0x13b4240: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x13b4244: mov             x1, x0
    // 0x13b4248: ldur            x2, [fp, #-0x40]
    // 0x13b424c: stur            x0, [fp, #-0x30]
    // 0x13b4250: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13b4250: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13b4254: r0 = RichText()
    //     0x13b4254: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x13b4258: r0 = InkWell()
    //     0x13b4258: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13b425c: mov             x3, x0
    // 0x13b4260: ldur            x0, [fp, #-0x30]
    // 0x13b4264: stur            x3, [fp, #-0x40]
    // 0x13b4268: StoreField: r3->field_b = r0
    //     0x13b4268: stur            w0, [x3, #0xb]
    // 0x13b426c: ldur            x2, [fp, #-8]
    // 0x13b4270: r1 = Function '<anonymous closure>':.
    //     0x13b4270: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0b0] AnonymousClosure: (0x13ba2b0), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b4274: ldr             x1, [x1, #0xb0]
    // 0x13b4278: r0 = AllocateClosure()
    //     0x13b4278: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b427c: mov             x1, x0
    // 0x13b4280: ldur            x0, [fp, #-0x40]
    // 0x13b4284: StoreField: r0->field_f = r1
    //     0x13b4284: stur            w1, [x0, #0xf]
    // 0x13b4288: r1 = true
    //     0x13b4288: add             x1, NULL, #0x20  ; true
    // 0x13b428c: StoreField: r0->field_43 = r1
    //     0x13b428c: stur            w1, [x0, #0x43]
    // 0x13b4290: r2 = Instance_BoxShape
    //     0x13b4290: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13b4294: ldr             x2, [x2, #0x80]
    // 0x13b4298: StoreField: r0->field_47 = r2
    //     0x13b4298: stur            w2, [x0, #0x47]
    // 0x13b429c: StoreField: r0->field_6f = r1
    //     0x13b429c: stur            w1, [x0, #0x6f]
    // 0x13b42a0: r2 = false
    //     0x13b42a0: add             x2, NULL, #0x30  ; false
    // 0x13b42a4: StoreField: r0->field_73 = r2
    //     0x13b42a4: stur            w2, [x0, #0x73]
    // 0x13b42a8: StoreField: r0->field_83 = r1
    //     0x13b42a8: stur            w1, [x0, #0x83]
    // 0x13b42ac: StoreField: r0->field_7b = r2
    //     0x13b42ac: stur            w2, [x0, #0x7b]
    // 0x13b42b0: r0 = Center()
    //     0x13b42b0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13b42b4: mov             x1, x0
    // 0x13b42b8: r0 = Instance_Alignment
    //     0x13b42b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13b42bc: ldr             x0, [x0, #0xb10]
    // 0x13b42c0: stur            x1, [fp, #-0x30]
    // 0x13b42c4: StoreField: r1->field_f = r0
    //     0x13b42c4: stur            w0, [x1, #0xf]
    // 0x13b42c8: ldur            x2, [fp, #-0x40]
    // 0x13b42cc: StoreField: r1->field_b = r2
    //     0x13b42cc: stur            w2, [x1, #0xb]
    // 0x13b42d0: r0 = Padding()
    //     0x13b42d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13b42d4: mov             x2, x0
    // 0x13b42d8: r0 = Instance_EdgeInsets
    //     0x13b42d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x13b42dc: ldr             x0, [x0, #0x980]
    // 0x13b42e0: stur            x2, [fp, #-0x40]
    // 0x13b42e4: StoreField: r2->field_f = r0
    //     0x13b42e4: stur            w0, [x2, #0xf]
    // 0x13b42e8: ldur            x1, [fp, #-0x30]
    // 0x13b42ec: StoreField: r2->field_b = r1
    //     0x13b42ec: stur            w1, [x2, #0xb]
    // 0x13b42f0: r1 = <FlexParentData>
    //     0x13b42f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13b42f4: ldr             x1, [x1, #0xe00]
    // 0x13b42f8: r0 = Flexible()
    //     0x13b42f8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x13b42fc: mov             x2, x0
    // 0x13b4300: r0 = 1
    //     0x13b4300: movz            x0, #0x1
    // 0x13b4304: stur            x2, [fp, #-0x30]
    // 0x13b4308: StoreField: r2->field_13 = r0
    //     0x13b4308: stur            x0, [x2, #0x13]
    // 0x13b430c: r3 = Instance_FlexFit
    //     0x13b430c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x13b4310: ldr             x3, [x3, #0xe20]
    // 0x13b4314: StoreField: r2->field_1b = r3
    //     0x13b4314: stur            w3, [x2, #0x1b]
    // 0x13b4318: ldur            x1, [fp, #-0x40]
    // 0x13b431c: StoreField: r2->field_b = r1
    //     0x13b431c: stur            w1, [x2, #0xb]
    // 0x13b4320: r1 = Instance_Color
    //     0x13b4320: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b4324: d0 = 0.100000
    //     0x13b4324: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13b4328: r0 = withOpacity()
    //     0x13b4328: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13b432c: stur            x0, [fp, #-0x40]
    // 0x13b4330: r0 = VerticalDivider()
    //     0x13b4330: bl              #0x99b78c  ; AllocateVerticalDividerStub -> VerticalDivider (size=0x28)
    // 0x13b4334: d0 = 1.000000
    //     0x13b4334: fmov            d0, #1.00000000
    // 0x13b4338: stur            x0, [fp, #-0x48]
    // 0x13b433c: StoreField: r0->field_f = d0
    //     0x13b433c: stur            d0, [x0, #0xf]
    // 0x13b4340: ldur            x1, [fp, #-0x40]
    // 0x13b4344: StoreField: r0->field_1f = r1
    //     0x13b4344: stur            w1, [x0, #0x1f]
    // 0x13b4348: r0 = Padding()
    //     0x13b4348: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13b434c: mov             x1, x0
    // 0x13b4350: r0 = Instance_EdgeInsets
    //     0x13b4350: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x13b4354: ldr             x0, [x0, #0x980]
    // 0x13b4358: stur            x1, [fp, #-0x40]
    // 0x13b435c: StoreField: r1->field_f = r0
    //     0x13b435c: stur            w0, [x1, #0xf]
    // 0x13b4360: ldur            x0, [fp, #-0x48]
    // 0x13b4364: StoreField: r1->field_b = r0
    //     0x13b4364: stur            w0, [x1, #0xb]
    // 0x13b4368: r0 = SvgPicture()
    //     0x13b4368: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x13b436c: stur            x0, [fp, #-0x48]
    // 0x13b4370: r16 = Instance_ColorFilter
    //     0x13b4370: add             x16, PP, #0x33, lsl #12  ; [pp+0x33818] Obj!ColorFilter@d69801
    //     0x13b4374: ldr             x16, [x16, #0x818]
    // 0x13b4378: r30 = 24.000000
    //     0x13b4378: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x13b437c: ldr             lr, [lr, #0xba8]
    // 0x13b4380: stp             lr, x16, [SP, #8]
    // 0x13b4384: r16 = 24.000000
    //     0x13b4384: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x13b4388: ldr             x16, [x16, #0xba8]
    // 0x13b438c: str             x16, [SP]
    // 0x13b4390: mov             x1, x0
    // 0x13b4394: r2 = "assets/images/bar_chart.svg"
    //     0x13b4394: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f8f8] "assets/images/bar_chart.svg"
    //     0x13b4398: ldr             x2, [x2, #0x8f8]
    // 0x13b439c: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x2, height, 0x3, width, 0x4, null]
    //     0x13b439c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c0b8] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x13b43a0: ldr             x4, [x4, #0xb8]
    // 0x13b43a4: r0 = SvgPicture.asset()
    //     0x13b43a4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x13b43a8: ldur            x2, [fp, #-8]
    // 0x13b43ac: LoadField: r1 = r2->field_f
    //     0x13b43ac: ldur            w1, [x2, #0xf]
    // 0x13b43b0: DecompressPointer r1
    //     0x13b43b0: add             x1, x1, HEAP, lsl #32
    // 0x13b43b4: r0 = controller()
    //     0x13b43b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b43b8: mov             x1, x0
    // 0x13b43bc: r0 = bumperCouponData()
    //     0x13b43bc: bl              #0x9be348  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::bumperCouponData
    // 0x13b43c0: ldur            x2, [fp, #-8]
    // 0x13b43c4: stur            x0, [fp, #-0x50]
    // 0x13b43c8: LoadField: r1 = r2->field_f
    //     0x13b43c8: ldur            w1, [x2, #0xf]
    // 0x13b43cc: DecompressPointer r1
    //     0x13b43cc: add             x1, x1, HEAP, lsl #32
    // 0x13b43d0: r0 = controller()
    //     0x13b43d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b43d4: LoadField: r1 = r0->field_67
    //     0x13b43d4: ldur            w1, [x0, #0x67]
    // 0x13b43d8: DecompressPointer r1
    //     0x13b43d8: add             x1, x1, HEAP, lsl #32
    // 0x13b43dc: r0 = value()
    //     0x13b43dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b43e0: LoadField: r1 = r0->field_b
    //     0x13b43e0: ldur            w1, [x0, #0xb]
    // 0x13b43e4: DecompressPointer r1
    //     0x13b43e4: add             x1, x1, HEAP, lsl #32
    // 0x13b43e8: cmp             w1, NULL
    // 0x13b43ec: b.ne            #0x13b43f8
    // 0x13b43f0: r10 = Null
    //     0x13b43f0: mov             x10, NULL
    // 0x13b43f4: b               #0x13b4450
    // 0x13b43f8: LoadField: r0 = r1->field_13
    //     0x13b43f8: ldur            w0, [x1, #0x13]
    // 0x13b43fc: DecompressPointer r0
    //     0x13b43fc: add             x0, x0, HEAP, lsl #32
    // 0x13b4400: stur            x0, [fp, #-0x58]
    // 0x13b4404: cmp             w0, NULL
    // 0x13b4408: b.ne            #0x13b4414
    // 0x13b440c: r0 = Null
    //     0x13b440c: mov             x0, NULL
    // 0x13b4410: b               #0x13b444c
    // 0x13b4414: ldur            x2, [fp, #-8]
    // 0x13b4418: r1 = Function '<anonymous closure>':.
    //     0x13b4418: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0c0] AnonymousClosure: (0x13ba140), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b441c: ldr             x1, [x1, #0xc0]
    // 0x13b4420: r0 = AllocateClosure()
    //     0x13b4420: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4424: r16 = <DropdownMenuItem<Filter>>
    //     0x13b4424: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c0c8] TypeArguments: <DropdownMenuItem<Filter>>
    //     0x13b4428: ldr             x16, [x16, #0xc8]
    // 0x13b442c: ldur            lr, [fp, #-0x58]
    // 0x13b4430: stp             lr, x16, [SP, #8]
    // 0x13b4434: str             x0, [SP]
    // 0x13b4438: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x13b4438: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x13b443c: r0 = map()
    //     0x13b443c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x13b4440: mov             x1, x0
    // 0x13b4444: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13b4444: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13b4448: r0 = toList()
    //     0x13b4448: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x13b444c: mov             x10, x0
    // 0x13b4450: ldur            x0, [fp, #-8]
    // 0x13b4454: ldur            x9, [fp, #-0x10]
    // 0x13b4458: ldur            x8, [fp, #-0x18]
    // 0x13b445c: ldur            x7, [fp, #-0x20]
    // 0x13b4460: ldur            x6, [fp, #-0x28]
    // 0x13b4464: ldur            x5, [fp, #-0x30]
    // 0x13b4468: ldur            x4, [fp, #-0x40]
    // 0x13b446c: ldur            x3, [fp, #-0x48]
    // 0x13b4470: ldur            d0, [fp, #-0x78]
    // 0x13b4474: mov             x2, x0
    // 0x13b4478: stur            x10, [fp, #-0x58]
    // 0x13b447c: r1 = Function '<anonymous closure>':.
    //     0x13b447c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0d0] AnonymousClosure: (0x13ba044), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b4480: ldr             x1, [x1, #0xd0]
    // 0x13b4484: r0 = AllocateClosure()
    //     0x13b4484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4488: r1 = <Filter>
    //     0x13b4488: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bd10] TypeArguments: <Filter>
    //     0x13b448c: ldr             x1, [x1, #0xd10]
    // 0x13b4490: stur            x0, [fp, #-0x60]
    // 0x13b4494: r0 = DropdownButton()
    //     0x13b4494: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0x13b4498: stur            x0, [fp, #-0x68]
    // 0x13b449c: r16 = 0.000000
    //     0x13b449c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x13b44a0: str             x16, [SP]
    // 0x13b44a4: mov             x1, x0
    // 0x13b44a8: ldur            x2, [fp, #-0x58]
    // 0x13b44ac: ldur            x3, [fp, #-0x60]
    // 0x13b44b0: ldur            x5, [fp, #-0x50]
    // 0x13b44b4: r4 = const [0, 0x5, 0x1, 0x4, iconSize, 0x4, null]
    //     0x13b44b4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c0d8] List(7) [0, 0x5, 0x1, 0x4, "iconSize", 0x4, Null]
    //     0x13b44b8: ldr             x4, [x4, #0xd8]
    // 0x13b44bc: r0 = DropdownButton()
    //     0x13b44bc: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0x13b44c0: r0 = DropdownButtonHideUnderline()
    //     0x13b44c0: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0x13b44c4: mov             x2, x0
    // 0x13b44c8: ldur            x0, [fp, #-0x68]
    // 0x13b44cc: stur            x2, [fp, #-0x50]
    // 0x13b44d0: StoreField: r2->field_b = r0
    //     0x13b44d0: stur            w0, [x2, #0xb]
    // 0x13b44d4: r1 = <FlexParentData>
    //     0x13b44d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13b44d8: ldr             x1, [x1, #0xe00]
    // 0x13b44dc: r0 = Flexible()
    //     0x13b44dc: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x13b44e0: mov             x3, x0
    // 0x13b44e4: r0 = 1
    //     0x13b44e4: movz            x0, #0x1
    // 0x13b44e8: stur            x3, [fp, #-0x58]
    // 0x13b44ec: StoreField: r3->field_13 = r0
    //     0x13b44ec: stur            x0, [x3, #0x13]
    // 0x13b44f0: r4 = Instance_FlexFit
    //     0x13b44f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x13b44f4: ldr             x4, [x4, #0xe20]
    // 0x13b44f8: StoreField: r3->field_1b = r4
    //     0x13b44f8: stur            w4, [x3, #0x1b]
    // 0x13b44fc: ldur            x1, [fp, #-0x50]
    // 0x13b4500: StoreField: r3->field_b = r1
    //     0x13b4500: stur            w1, [x3, #0xb]
    // 0x13b4504: r1 = Null
    //     0x13b4504: mov             x1, NULL
    // 0x13b4508: r2 = 8
    //     0x13b4508: movz            x2, #0x8
    // 0x13b450c: r0 = AllocateArray()
    //     0x13b450c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b4510: mov             x2, x0
    // 0x13b4514: ldur            x0, [fp, #-0x30]
    // 0x13b4518: stur            x2, [fp, #-0x50]
    // 0x13b451c: StoreField: r2->field_f = r0
    //     0x13b451c: stur            w0, [x2, #0xf]
    // 0x13b4520: ldur            x0, [fp, #-0x40]
    // 0x13b4524: StoreField: r2->field_13 = r0
    //     0x13b4524: stur            w0, [x2, #0x13]
    // 0x13b4528: ldur            x0, [fp, #-0x48]
    // 0x13b452c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13b452c: stur            w0, [x2, #0x17]
    // 0x13b4530: ldur            x0, [fp, #-0x58]
    // 0x13b4534: StoreField: r2->field_1b = r0
    //     0x13b4534: stur            w0, [x2, #0x1b]
    // 0x13b4538: r1 = <Widget>
    //     0x13b4538: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13b453c: r0 = AllocateGrowableArray()
    //     0x13b453c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b4540: mov             x1, x0
    // 0x13b4544: ldur            x0, [fp, #-0x50]
    // 0x13b4548: stur            x1, [fp, #-0x30]
    // 0x13b454c: StoreField: r1->field_f = r0
    //     0x13b454c: stur            w0, [x1, #0xf]
    // 0x13b4550: r0 = 8
    //     0x13b4550: movz            x0, #0x8
    // 0x13b4554: StoreField: r1->field_b = r0
    //     0x13b4554: stur            w0, [x1, #0xb]
    // 0x13b4558: r0 = Row()
    //     0x13b4558: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13b455c: mov             x1, x0
    // 0x13b4560: r0 = Instance_Axis
    //     0x13b4560: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13b4564: stur            x1, [fp, #-0x40]
    // 0x13b4568: StoreField: r1->field_f = r0
    //     0x13b4568: stur            w0, [x1, #0xf]
    // 0x13b456c: r0 = Instance_MainAxisAlignment
    //     0x13b456c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x13b4570: ldr             x0, [x0, #0xd10]
    // 0x13b4574: StoreField: r1->field_13 = r0
    //     0x13b4574: stur            w0, [x1, #0x13]
    // 0x13b4578: r0 = Instance_MainAxisSize
    //     0x13b4578: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13b457c: ldr             x0, [x0, #0xdd0]
    // 0x13b4580: ArrayStore: r1[0] = r0  ; List_4
    //     0x13b4580: stur            w0, [x1, #0x17]
    // 0x13b4584: r2 = Instance_CrossAxisAlignment
    //     0x13b4584: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13b4588: ldr             x2, [x2, #0xa18]
    // 0x13b458c: StoreField: r1->field_1b = r2
    //     0x13b458c: stur            w2, [x1, #0x1b]
    // 0x13b4590: r3 = Instance_VerticalDirection
    //     0x13b4590: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13b4594: ldr             x3, [x3, #0xa20]
    // 0x13b4598: StoreField: r1->field_23 = r3
    //     0x13b4598: stur            w3, [x1, #0x23]
    // 0x13b459c: r4 = Instance_Clip
    //     0x13b459c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13b45a0: ldr             x4, [x4, #0x38]
    // 0x13b45a4: StoreField: r1->field_2b = r4
    //     0x13b45a4: stur            w4, [x1, #0x2b]
    // 0x13b45a8: StoreField: r1->field_2f = rZR
    //     0x13b45a8: stur            xzr, [x1, #0x2f]
    // 0x13b45ac: ldur            x5, [fp, #-0x30]
    // 0x13b45b0: StoreField: r1->field_b = r5
    //     0x13b45b0: stur            w5, [x1, #0xb]
    // 0x13b45b4: r0 = IntrinsicHeight()
    //     0x13b45b4: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x13b45b8: mov             x1, x0
    // 0x13b45bc: ldur            x0, [fp, #-0x40]
    // 0x13b45c0: stur            x1, [fp, #-0x48]
    // 0x13b45c4: StoreField: r1->field_b = r0
    //     0x13b45c4: stur            w0, [x1, #0xb]
    // 0x13b45c8: ldur            d0, [fp, #-0x78]
    // 0x13b45cc: r0 = inline_Allocate_Double()
    //     0x13b45cc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x13b45d0: add             x0, x0, #0x10
    //     0x13b45d4: cmp             x2, x0
    //     0x13b45d8: b.ls            #0x13b4f80
    //     0x13b45dc: str             x0, [THR, #0x50]  ; THR::top
    //     0x13b45e0: sub             x0, x0, #0xf
    //     0x13b45e4: movz            x2, #0xe15c
    //     0x13b45e8: movk            x2, #0x3, lsl #16
    //     0x13b45ec: stur            x2, [x0, #-1]
    // 0x13b45f0: StoreField: r0->field_7 = d0
    //     0x13b45f0: stur            d0, [x0, #7]
    // 0x13b45f4: stur            x0, [fp, #-0x30]
    // 0x13b45f8: r0 = Container()
    //     0x13b45f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13b45fc: stur            x0, [fp, #-0x40]
    // 0x13b4600: ldur            x16, [fp, #-0x30]
    // 0x13b4604: ldur            lr, [fp, #-0x38]
    // 0x13b4608: stp             lr, x16, [SP, #8]
    // 0x13b460c: ldur            x16, [fp, #-0x48]
    // 0x13b4610: str             x16, [SP]
    // 0x13b4614: mov             x1, x0
    // 0x13b4618: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0x13b4618: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0x13b461c: ldr             x4, [x4, #0x830]
    // 0x13b4620: r0 = Container()
    //     0x13b4620: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13b4624: r0 = Padding()
    //     0x13b4624: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13b4628: mov             x2, x0
    // 0x13b462c: r0 = Instance_EdgeInsets
    //     0x13b462c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0e0] Obj!EdgeInsets@d594b1
    //     0x13b4630: ldr             x0, [x0, #0xe0]
    // 0x13b4634: stur            x2, [fp, #-0x30]
    // 0x13b4638: StoreField: r2->field_f = r0
    //     0x13b4638: stur            w0, [x2, #0xf]
    // 0x13b463c: ldur            x0, [fp, #-0x40]
    // 0x13b4640: StoreField: r2->field_b = r0
    //     0x13b4640: stur            w0, [x2, #0xb]
    // 0x13b4644: r1 = <FlexParentData>
    //     0x13b4644: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13b4648: ldr             x1, [x1, #0xe00]
    // 0x13b464c: r0 = Flexible()
    //     0x13b464c: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x13b4650: mov             x3, x0
    // 0x13b4654: r0 = 1
    //     0x13b4654: movz            x0, #0x1
    // 0x13b4658: stur            x3, [fp, #-0x38]
    // 0x13b465c: StoreField: r3->field_13 = r0
    //     0x13b465c: stur            x0, [x3, #0x13]
    // 0x13b4660: r4 = Instance_FlexFit
    //     0x13b4660: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x13b4664: ldr             x4, [x4, #0xe20]
    // 0x13b4668: StoreField: r3->field_1b = r4
    //     0x13b4668: stur            w4, [x3, #0x1b]
    // 0x13b466c: ldur            x1, [fp, #-0x30]
    // 0x13b4670: StoreField: r3->field_b = r1
    //     0x13b4670: stur            w1, [x3, #0xb]
    // 0x13b4674: r1 = Null
    //     0x13b4674: mov             x1, NULL
    // 0x13b4678: r2 = 10
    //     0x13b4678: movz            x2, #0xa
    // 0x13b467c: r0 = AllocateArray()
    //     0x13b467c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b4680: mov             x2, x0
    // 0x13b4684: ldur            x0, [fp, #-0x10]
    // 0x13b4688: stur            x2, [fp, #-0x30]
    // 0x13b468c: StoreField: r2->field_f = r0
    //     0x13b468c: stur            w0, [x2, #0xf]
    // 0x13b4690: ldur            x0, [fp, #-0x18]
    // 0x13b4694: StoreField: r2->field_13 = r0
    //     0x13b4694: stur            w0, [x2, #0x13]
    // 0x13b4698: ldur            x0, [fp, #-0x20]
    // 0x13b469c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13b469c: stur            w0, [x2, #0x17]
    // 0x13b46a0: ldur            x0, [fp, #-0x28]
    // 0x13b46a4: StoreField: r2->field_1b = r0
    //     0x13b46a4: stur            w0, [x2, #0x1b]
    // 0x13b46a8: ldur            x0, [fp, #-0x38]
    // 0x13b46ac: StoreField: r2->field_1f = r0
    //     0x13b46ac: stur            w0, [x2, #0x1f]
    // 0x13b46b0: r1 = <Widget>
    //     0x13b46b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13b46b4: r0 = AllocateGrowableArray()
    //     0x13b46b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b46b8: mov             x2, x0
    // 0x13b46bc: ldur            x0, [fp, #-0x30]
    // 0x13b46c0: stur            x2, [fp, #-0x10]
    // 0x13b46c4: StoreField: r2->field_f = r0
    //     0x13b46c4: stur            w0, [x2, #0xf]
    // 0x13b46c8: r0 = 10
    //     0x13b46c8: movz            x0, #0xa
    // 0x13b46cc: StoreField: r2->field_b = r0
    //     0x13b46cc: stur            w0, [x2, #0xb]
    // 0x13b46d0: ldur            x0, [fp, #-8]
    // 0x13b46d4: LoadField: r1 = r0->field_f
    //     0x13b46d4: ldur            w1, [x0, #0xf]
    // 0x13b46d8: DecompressPointer r1
    //     0x13b46d8: add             x1, x1, HEAP, lsl #32
    // 0x13b46dc: r0 = controller()
    //     0x13b46dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b46e0: LoadField: r1 = r0->field_5f
    //     0x13b46e0: ldur            w1, [x0, #0x5f]
    // 0x13b46e4: DecompressPointer r1
    //     0x13b46e4: add             x1, x1, HEAP, lsl #32
    // 0x13b46e8: r0 = value()
    //     0x13b46e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b46ec: LoadField: r1 = r0->field_7
    //     0x13b46ec: ldur            w1, [x0, #7]
    // 0x13b46f0: DecompressPointer r1
    //     0x13b46f0: add             x1, x1, HEAP, lsl #32
    // 0x13b46f4: cmp             w1, NULL
    // 0x13b46f8: b.ne            #0x13b4704
    // 0x13b46fc: r0 = Null
    //     0x13b46fc: mov             x0, NULL
    // 0x13b4700: b               #0x13b471c
    // 0x13b4704: LoadField: r0 = r1->field_b
    //     0x13b4704: ldur            w0, [x1, #0xb]
    // 0x13b4708: cbnz            w0, #0x13b4714
    // 0x13b470c: r1 = false
    //     0x13b470c: add             x1, NULL, #0x30  ; false
    // 0x13b4710: b               #0x13b4718
    // 0x13b4714: r1 = true
    //     0x13b4714: add             x1, NULL, #0x20  ; true
    // 0x13b4718: mov             x0, x1
    // 0x13b471c: cmp             w0, NULL
    // 0x13b4720: b.ne            #0x13b472c
    // 0x13b4724: ldur            x2, [fp, #-0x10]
    // 0x13b4728: b               #0x13b4afc
    // 0x13b472c: tbnz            w0, #4, #0x13b4af8
    // 0x13b4730: ldur            x2, [fp, #-8]
    // 0x13b4734: LoadField: r1 = r2->field_13
    //     0x13b4734: ldur            w1, [x2, #0x13]
    // 0x13b4738: DecompressPointer r1
    //     0x13b4738: add             x1, x1, HEAP, lsl #32
    // 0x13b473c: r0 = of()
    //     0x13b473c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13b4740: LoadField: r1 = r0->field_87
    //     0x13b4740: ldur            w1, [x0, #0x87]
    // 0x13b4744: DecompressPointer r1
    //     0x13b4744: add             x1, x1, HEAP, lsl #32
    // 0x13b4748: LoadField: r0 = r1->field_2b
    //     0x13b4748: ldur            w0, [x1, #0x2b]
    // 0x13b474c: DecompressPointer r0
    //     0x13b474c: add             x0, x0, HEAP, lsl #32
    // 0x13b4750: stur            x0, [fp, #-0x18]
    // 0x13b4754: r1 = Instance_Color
    //     0x13b4754: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b4758: d0 = 0.700000
    //     0x13b4758: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13b475c: ldr             d0, [x17, #0xf48]
    // 0x13b4760: r0 = withOpacity()
    //     0x13b4760: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13b4764: r16 = 12.000000
    //     0x13b4764: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13b4768: ldr             x16, [x16, #0x9e8]
    // 0x13b476c: stp             x0, x16, [SP]
    // 0x13b4770: ldur            x1, [fp, #-0x18]
    // 0x13b4774: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13b4774: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13b4778: ldr             x4, [x4, #0xaa0]
    // 0x13b477c: r0 = copyWith()
    //     0x13b477c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13b4780: r1 = Instance_Color
    //     0x13b4780: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b4784: d0 = 0.100000
    //     0x13b4784: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13b4788: stur            x0, [fp, #-0x18]
    // 0x13b478c: r0 = withOpacity()
    //     0x13b478c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13b4790: stur            x0, [fp, #-0x20]
    // 0x13b4794: r0 = FlexiChipStyle()
    //     0x13b4794: bl              #0x808284  ; AllocateFlexiChipStyleStub -> FlexiChipStyle (size=0x8c)
    // 0x13b4798: mov             x1, x0
    // 0x13b479c: ldur            x0, [fp, #-0x18]
    // 0x13b47a0: StoreField: r1->field_23 = r0
    //     0x13b47a0: stur            w0, [x1, #0x23]
    // 0x13b47a4: r0 = Instance_Color
    //     0x13b47a4: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13b47a8: StoreField: r1->field_37 = r0
    //     0x13b47a8: stur            w0, [x1, #0x37]
    // 0x13b47ac: ldur            x0, [fp, #-0x20]
    // 0x13b47b0: StoreField: r1->field_43 = r0
    //     0x13b47b0: stur            w0, [x1, #0x43]
    // 0x13b47b4: r0 = 0.100000
    //     0x13b47b4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0e8] 0.1
    //     0x13b47b8: ldr             x0, [x0, #0xe8]
    // 0x13b47bc: StoreField: r1->field_47 = r0
    //     0x13b47bc: stur            w0, [x1, #0x47]
    // 0x13b47c0: r0 = 1.000000
    //     0x13b47c0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x13b47c4: StoreField: r1->field_4f = r0
    //     0x13b47c4: stur            w0, [x1, #0x4f]
    // 0x13b47c8: r0 = Instance_BorderRadius
    //     0x13b47c8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x13b47cc: ldr             x0, [x0, #0xf70]
    // 0x13b47d0: StoreField: r1->field_53 = r0
    //     0x13b47d0: stur            w0, [x1, #0x53]
    // 0x13b47d4: r0 = Instance_BorderStyle
    //     0x13b47d4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x13b47d8: ldr             x0, [x0, #0xf68]
    // 0x13b47dc: StoreField: r1->field_57 = r0
    //     0x13b47dc: stur            w0, [x1, #0x57]
    // 0x13b47e0: mov             x2, x1
    // 0x13b47e4: r1 = Null
    //     0x13b47e4: mov             x1, NULL
    // 0x13b47e8: r0 = FlexiChipStyle.filled()
    //     0x13b47e8: bl              #0x13af730  ; [package:flexi_chip/src/style.dart] FlexiChipStyle::FlexiChipStyle.filled
    // 0x13b47ec: ldur            x2, [fp, #-8]
    // 0x13b47f0: stur            x0, [fp, #-0x18]
    // 0x13b47f4: LoadField: r1 = r2->field_f
    //     0x13b47f4: ldur            w1, [x2, #0xf]
    // 0x13b47f8: DecompressPointer r1
    //     0x13b47f8: add             x1, x1, HEAP, lsl #32
    // 0x13b47fc: r0 = controller()
    //     0x13b47fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4800: LoadField: r1 = r0->field_5f
    //     0x13b4800: ldur            w1, [x0, #0x5f]
    // 0x13b4804: DecompressPointer r1
    //     0x13b4804: add             x1, x1, HEAP, lsl #32
    // 0x13b4808: r0 = value()
    //     0x13b4808: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b480c: LoadField: r1 = r0->field_7
    //     0x13b480c: ldur            w1, [x0, #7]
    // 0x13b4810: DecompressPointer r1
    //     0x13b4810: add             x1, x1, HEAP, lsl #32
    // 0x13b4814: cmp             w1, NULL
    // 0x13b4818: b.ne            #0x13b485c
    // 0x13b481c: r0 = 2
    //     0x13b481c: movz            x0, #0x2
    // 0x13b4820: mov             x2, x0
    // 0x13b4824: r1 = Null
    //     0x13b4824: mov             x1, NULL
    // 0x13b4828: r0 = AllocateArray()
    //     0x13b4828: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b482c: stur            x0, [fp, #-0x20]
    // 0x13b4830: r16 = ""
    //     0x13b4830: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b4834: StoreField: r0->field_f = r16
    //     0x13b4834: stur            w16, [x0, #0xf]
    // 0x13b4838: r1 = <String?>
    //     0x13b4838: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x13b483c: r0 = AllocateGrowableArray()
    //     0x13b483c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b4840: mov             x1, x0
    // 0x13b4844: ldur            x0, [fp, #-0x20]
    // 0x13b4848: StoreField: r1->field_f = r0
    //     0x13b4848: stur            w0, [x1, #0xf]
    // 0x13b484c: r2 = 2
    //     0x13b484c: movz            x2, #0x2
    // 0x13b4850: StoreField: r1->field_b = r2
    //     0x13b4850: stur            w2, [x1, #0xb]
    // 0x13b4854: mov             x6, x1
    // 0x13b4858: b               #0x13b48d8
    // 0x13b485c: ldur            x0, [fp, #-8]
    // 0x13b4860: r2 = 2
    //     0x13b4860: movz            x2, #0x2
    // 0x13b4864: LoadField: r1 = r0->field_f
    //     0x13b4864: ldur            w1, [x0, #0xf]
    // 0x13b4868: DecompressPointer r1
    //     0x13b4868: add             x1, x1, HEAP, lsl #32
    // 0x13b486c: r0 = controller()
    //     0x13b486c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4870: LoadField: r1 = r0->field_5f
    //     0x13b4870: ldur            w1, [x0, #0x5f]
    // 0x13b4874: DecompressPointer r1
    //     0x13b4874: add             x1, x1, HEAP, lsl #32
    // 0x13b4878: r0 = value()
    //     0x13b4878: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b487c: LoadField: r1 = r0->field_7
    //     0x13b487c: ldur            w1, [x0, #7]
    // 0x13b4880: DecompressPointer r1
    //     0x13b4880: add             x1, x1, HEAP, lsl #32
    // 0x13b4884: cmp             w1, NULL
    // 0x13b4888: b.ne            #0x13b48cc
    // 0x13b488c: r0 = 2
    //     0x13b488c: movz            x0, #0x2
    // 0x13b4890: mov             x2, x0
    // 0x13b4894: r1 = Null
    //     0x13b4894: mov             x1, NULL
    // 0x13b4898: r0 = AllocateArray()
    //     0x13b4898: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b489c: stur            x0, [fp, #-0x20]
    // 0x13b48a0: r16 = ""
    //     0x13b48a0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b48a4: StoreField: r0->field_f = r16
    //     0x13b48a4: stur            w16, [x0, #0xf]
    // 0x13b48a8: r1 = <String?>
    //     0x13b48a8: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x13b48ac: r0 = AllocateGrowableArray()
    //     0x13b48ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b48b0: mov             x1, x0
    // 0x13b48b4: ldur            x0, [fp, #-0x20]
    // 0x13b48b8: StoreField: r1->field_f = r0
    //     0x13b48b8: stur            w0, [x1, #0xf]
    // 0x13b48bc: r2 = 2
    //     0x13b48bc: movz            x2, #0x2
    // 0x13b48c0: StoreField: r1->field_b = r2
    //     0x13b48c0: stur            w2, [x1, #0xb]
    // 0x13b48c4: mov             x0, x1
    // 0x13b48c8: b               #0x13b48d4
    // 0x13b48cc: r2 = 2
    //     0x13b48cc: movz            x2, #0x2
    // 0x13b48d0: mov             x0, x1
    // 0x13b48d4: mov             x6, x0
    // 0x13b48d8: ldur            x0, [fp, #-8]
    // 0x13b48dc: stur            x6, [fp, #-0x20]
    // 0x13b48e0: LoadField: r1 = r0->field_f
    //     0x13b48e0: ldur            w1, [x0, #0xf]
    // 0x13b48e4: DecompressPointer r1
    //     0x13b48e4: add             x1, x1, HEAP, lsl #32
    // 0x13b48e8: r0 = controller()
    //     0x13b48e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b48ec: LoadField: r1 = r0->field_5f
    //     0x13b48ec: ldur            w1, [x0, #0x5f]
    // 0x13b48f0: DecompressPointer r1
    //     0x13b48f0: add             x1, x1, HEAP, lsl #32
    // 0x13b48f4: r0 = value()
    //     0x13b48f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b48f8: LoadField: r1 = r0->field_7
    //     0x13b48f8: ldur            w1, [x0, #7]
    // 0x13b48fc: DecompressPointer r1
    //     0x13b48fc: add             x1, x1, HEAP, lsl #32
    // 0x13b4900: cmp             w1, NULL
    // 0x13b4904: b.ne            #0x13b4948
    // 0x13b4908: r0 = 2
    //     0x13b4908: movz            x0, #0x2
    // 0x13b490c: mov             x2, x0
    // 0x13b4910: r1 = Null
    //     0x13b4910: mov             x1, NULL
    // 0x13b4914: r0 = AllocateArray()
    //     0x13b4914: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b4918: stur            x0, [fp, #-0x28]
    // 0x13b491c: r16 = ""
    //     0x13b491c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b4920: StoreField: r0->field_f = r16
    //     0x13b4920: stur            w16, [x0, #0xf]
    // 0x13b4924: r1 = <String>
    //     0x13b4924: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x13b4928: r0 = AllocateGrowableArray()
    //     0x13b4928: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b492c: mov             x1, x0
    // 0x13b4930: ldur            x0, [fp, #-0x28]
    // 0x13b4934: StoreField: r1->field_f = r0
    //     0x13b4934: stur            w0, [x1, #0xf]
    // 0x13b4938: r2 = 2
    //     0x13b4938: movz            x2, #0x2
    // 0x13b493c: StoreField: r1->field_b = r2
    //     0x13b493c: stur            w2, [x1, #0xb]
    // 0x13b4940: mov             x3, x1
    // 0x13b4944: b               #0x13b49bc
    // 0x13b4948: ldur            x0, [fp, #-8]
    // 0x13b494c: r2 = 2
    //     0x13b494c: movz            x2, #0x2
    // 0x13b4950: LoadField: r1 = r0->field_f
    //     0x13b4950: ldur            w1, [x0, #0xf]
    // 0x13b4954: DecompressPointer r1
    //     0x13b4954: add             x1, x1, HEAP, lsl #32
    // 0x13b4958: r0 = controller()
    //     0x13b4958: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b495c: mov             x1, x0
    // 0x13b4960: r0 = isShowSearch()
    //     0x13b4960: bl              #0x8adacc  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::isShowSearch
    // 0x13b4964: LoadField: r1 = r0->field_7
    //     0x13b4964: ldur            w1, [x0, #7]
    // 0x13b4968: DecompressPointer r1
    //     0x13b4968: add             x1, x1, HEAP, lsl #32
    // 0x13b496c: cmp             w1, NULL
    // 0x13b4970: b.ne            #0x13b49b4
    // 0x13b4974: r0 = 2
    //     0x13b4974: movz            x0, #0x2
    // 0x13b4978: mov             x2, x0
    // 0x13b497c: r1 = Null
    //     0x13b497c: mov             x1, NULL
    // 0x13b4980: r0 = AllocateArray()
    //     0x13b4980: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b4984: stur            x0, [fp, #-0x28]
    // 0x13b4988: r16 = ""
    //     0x13b4988: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b498c: StoreField: r0->field_f = r16
    //     0x13b498c: stur            w16, [x0, #0xf]
    // 0x13b4990: r1 = <String>
    //     0x13b4990: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x13b4994: r0 = AllocateGrowableArray()
    //     0x13b4994: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b4998: mov             x1, x0
    // 0x13b499c: ldur            x0, [fp, #-0x28]
    // 0x13b49a0: StoreField: r1->field_f = r0
    //     0x13b49a0: stur            w0, [x1, #0xf]
    // 0x13b49a4: r0 = 2
    //     0x13b49a4: movz            x0, #0x2
    // 0x13b49a8: StoreField: r1->field_b = r0
    //     0x13b49a8: stur            w0, [x1, #0xb]
    // 0x13b49ac: mov             x0, x1
    // 0x13b49b0: b               #0x13b49b8
    // 0x13b49b4: mov             x0, x1
    // 0x13b49b8: mov             x3, x0
    // 0x13b49bc: ldur            x0, [fp, #-0x10]
    // 0x13b49c0: stur            x3, [fp, #-0x28]
    // 0x13b49c4: r1 = Function '<anonymous closure>':.
    //     0x13b49c4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0f0] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x13b49c8: ldr             x1, [x1, #0xf0]
    // 0x13b49cc: r2 = Null
    //     0x13b49cc: mov             x2, NULL
    // 0x13b49d0: r0 = AllocateClosure()
    //     0x13b49d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b49d4: r1 = Function '<anonymous closure>':.
    //     0x13b49d4: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c0f8] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x13b49d8: ldr             x1, [x1, #0xf8]
    // 0x13b49dc: r2 = Null
    //     0x13b49dc: mov             x2, NULL
    // 0x13b49e0: stur            x0, [fp, #-0x30]
    // 0x13b49e4: r0 = AllocateClosure()
    //     0x13b49e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b49e8: r1 = Function '<anonymous closure>':.
    //     0x13b49e8: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c100] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x13b49ec: ldr             x1, [x1, #0x100]
    // 0x13b49f0: r2 = Null
    //     0x13b49f0: mov             x2, NULL
    // 0x13b49f4: stur            x0, [fp, #-0x38]
    // 0x13b49f8: r0 = AllocateClosure()
    //     0x13b49f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b49fc: ldur            x2, [fp, #-8]
    // 0x13b4a00: r1 = Function '<anonymous closure>':.
    //     0x13b4a00: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c108] AnonymousClosure: (0x13b5174), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b4a04: ldr             x1, [x1, #0x108]
    // 0x13b4a08: stur            x0, [fp, #-0x40]
    // 0x13b4a0c: r0 = AllocateClosure()
    //     0x13b4a0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4a10: r16 = <String, String>
    //     0x13b4a10: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x13b4a14: ldr             x16, [x16, #0x788]
    // 0x13b4a18: stp             x0, x16, [SP, #0x20]
    // 0x13b4a1c: ldur            x16, [fp, #-0x38]
    // 0x13b4a20: ldur            lr, [fp, #-0x28]
    // 0x13b4a24: stp             lr, x16, [SP, #0x10]
    // 0x13b4a28: ldur            x16, [fp, #-0x40]
    // 0x13b4a2c: ldur            lr, [fp, #-0x30]
    // 0x13b4a30: stp             lr, x16, [SP]
    // 0x13b4a34: r4 = const [0x2, 0x5, 0x5, 0x5, null]
    //     0x13b4a34: ldr             x4, [PP, #0x4e8]  ; [pp+0x4e8] List(5) [0x2, 0x5, 0x5, 0x5, Null]
    // 0x13b4a38: r0 = listFrom()
    //     0x13b4a38: bl              #0x13af444  ; [package:chips_choice/src/choice.dart] C2Choice::listFrom
    // 0x13b4a3c: r1 = Function '<anonymous closure>':.
    //     0x13b4a3c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c110] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x13b4a40: ldr             x1, [x1, #0x110]
    // 0x13b4a44: r2 = Null
    //     0x13b4a44: mov             x2, NULL
    // 0x13b4a48: stur            x0, [fp, #-0x28]
    // 0x13b4a4c: r0 = AllocateClosure()
    //     0x13b4a4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4a50: r1 = <String?>
    //     0x13b4a50: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x13b4a54: stur            x0, [fp, #-0x30]
    // 0x13b4a58: r0 = ChipsChoice()
    //     0x13b4a58: bl              #0x13af438  ; AllocateChipsChoiceStub -> ChipsChoice<X0> (size=0xa8)
    // 0x13b4a5c: mov             x1, x0
    // 0x13b4a60: ldur            x2, [fp, #-0x28]
    // 0x13b4a64: ldur            x3, [fp, #-0x18]
    // 0x13b4a68: ldur            x5, [fp, #-0x30]
    // 0x13b4a6c: ldur            x6, [fp, #-0x20]
    // 0x13b4a70: stur            x0, [fp, #-0x18]
    // 0x13b4a74: r0 = ChipsChoice.multiple()
    //     0x13b4a74: bl              #0x13af328  ; [package:chips_choice/src/widget.dart] ChipsChoice::ChipsChoice.multiple
    // 0x13b4a78: ldur            x0, [fp, #-0x10]
    // 0x13b4a7c: LoadField: r1 = r0->field_b
    //     0x13b4a7c: ldur            w1, [x0, #0xb]
    // 0x13b4a80: LoadField: r2 = r0->field_f
    //     0x13b4a80: ldur            w2, [x0, #0xf]
    // 0x13b4a84: DecompressPointer r2
    //     0x13b4a84: add             x2, x2, HEAP, lsl #32
    // 0x13b4a88: LoadField: r3 = r2->field_b
    //     0x13b4a88: ldur            w3, [x2, #0xb]
    // 0x13b4a8c: r2 = LoadInt32Instr(r1)
    //     0x13b4a8c: sbfx            x2, x1, #1, #0x1f
    // 0x13b4a90: stur            x2, [fp, #-0x70]
    // 0x13b4a94: r1 = LoadInt32Instr(r3)
    //     0x13b4a94: sbfx            x1, x3, #1, #0x1f
    // 0x13b4a98: cmp             x2, x1
    // 0x13b4a9c: b.ne            #0x13b4aa8
    // 0x13b4aa0: mov             x1, x0
    // 0x13b4aa4: r0 = _growToNextCapacity()
    //     0x13b4aa4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x13b4aa8: ldur            x2, [fp, #-0x10]
    // 0x13b4aac: ldur            x3, [fp, #-0x70]
    // 0x13b4ab0: add             x0, x3, #1
    // 0x13b4ab4: lsl             x1, x0, #1
    // 0x13b4ab8: StoreField: r2->field_b = r1
    //     0x13b4ab8: stur            w1, [x2, #0xb]
    // 0x13b4abc: LoadField: r1 = r2->field_f
    //     0x13b4abc: ldur            w1, [x2, #0xf]
    // 0x13b4ac0: DecompressPointer r1
    //     0x13b4ac0: add             x1, x1, HEAP, lsl #32
    // 0x13b4ac4: ldur            x0, [fp, #-0x18]
    // 0x13b4ac8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x13b4ac8: add             x25, x1, x3, lsl #2
    //     0x13b4acc: add             x25, x25, #0xf
    //     0x13b4ad0: str             w0, [x25]
    //     0x13b4ad4: tbz             w0, #0, #0x13b4af0
    //     0x13b4ad8: ldurb           w16, [x1, #-1]
    //     0x13b4adc: ldurb           w17, [x0, #-1]
    //     0x13b4ae0: and             x16, x17, x16, lsr #2
    //     0x13b4ae4: tst             x16, HEAP, lsr #32
    //     0x13b4ae8: b.eq            #0x13b4af0
    //     0x13b4aec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13b4af0: mov             x0, x2
    // 0x13b4af4: b               #0x13b4b50
    // 0x13b4af8: ldur            x2, [fp, #-0x10]
    // 0x13b4afc: LoadField: r0 = r2->field_b
    //     0x13b4afc: ldur            w0, [x2, #0xb]
    // 0x13b4b00: LoadField: r1 = r2->field_f
    //     0x13b4b00: ldur            w1, [x2, #0xf]
    // 0x13b4b04: DecompressPointer r1
    //     0x13b4b04: add             x1, x1, HEAP, lsl #32
    // 0x13b4b08: LoadField: r3 = r1->field_b
    //     0x13b4b08: ldur            w3, [x1, #0xb]
    // 0x13b4b0c: r4 = LoadInt32Instr(r0)
    //     0x13b4b0c: sbfx            x4, x0, #1, #0x1f
    // 0x13b4b10: stur            x4, [fp, #-0x70]
    // 0x13b4b14: r0 = LoadInt32Instr(r3)
    //     0x13b4b14: sbfx            x0, x3, #1, #0x1f
    // 0x13b4b18: cmp             x4, x0
    // 0x13b4b1c: b.ne            #0x13b4b28
    // 0x13b4b20: mov             x1, x2
    // 0x13b4b24: r0 = _growToNextCapacity()
    //     0x13b4b24: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x13b4b28: ldur            x0, [fp, #-0x10]
    // 0x13b4b2c: ldur            x1, [fp, #-0x70]
    // 0x13b4b30: add             x2, x1, #1
    // 0x13b4b34: lsl             x3, x2, #1
    // 0x13b4b38: StoreField: r0->field_b = r3
    //     0x13b4b38: stur            w3, [x0, #0xb]
    // 0x13b4b3c: LoadField: r2 = r0->field_f
    //     0x13b4b3c: ldur            w2, [x0, #0xf]
    // 0x13b4b40: DecompressPointer r2
    //     0x13b4b40: add             x2, x2, HEAP, lsl #32
    // 0x13b4b44: add             x3, x2, x1, lsl #2
    // 0x13b4b48: r16 = Instance_SizedBox
    //     0x13b4b48: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13b4b4c: StoreField: r3->field_f = r16
    //     0x13b4b4c: stur            w16, [x3, #0xf]
    // 0x13b4b50: ldur            x2, [fp, #-8]
    // 0x13b4b54: LoadField: r1 = r2->field_f
    //     0x13b4b54: ldur            w1, [x2, #0xf]
    // 0x13b4b58: DecompressPointer r1
    //     0x13b4b58: add             x1, x1, HEAP, lsl #32
    // 0x13b4b5c: r0 = controller()
    //     0x13b4b5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4b60: LoadField: r1 = r0->field_5b
    //     0x13b4b60: ldur            w1, [x0, #0x5b]
    // 0x13b4b64: DecompressPointer r1
    //     0x13b4b64: add             x1, x1, HEAP, lsl #32
    // 0x13b4b68: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13b4b68: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13b4b6c: r0 = toList()
    //     0x13b4b6c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x13b4b70: LoadField: r1 = r0->field_b
    //     0x13b4b70: ldur            w1, [x0, #0xb]
    // 0x13b4b74: cbz             w1, #0x13b4dc8
    // 0x13b4b78: ldur            x2, [fp, #-8]
    // 0x13b4b7c: ldur            x0, [fp, #-0x10]
    // 0x13b4b80: LoadField: r1 = r2->field_f
    //     0x13b4b80: ldur            w1, [x2, #0xf]
    // 0x13b4b84: DecompressPointer r1
    //     0x13b4b84: add             x1, x1, HEAP, lsl #32
    // 0x13b4b88: r0 = controller()
    //     0x13b4b88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4b8c: mov             x1, x0
    // 0x13b4b90: r0 = entityList()
    //     0x13b4b90: bl              #0x13af2ec  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::entityList
    // 0x13b4b94: ldur            x2, [fp, #-8]
    // 0x13b4b98: stur            x0, [fp, #-0x18]
    // 0x13b4b9c: LoadField: r1 = r2->field_f
    //     0x13b4b9c: ldur            w1, [x2, #0xf]
    // 0x13b4ba0: DecompressPointer r1
    //     0x13b4ba0: add             x1, x1, HEAP, lsl #32
    // 0x13b4ba4: r0 = controller()
    //     0x13b4ba4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4ba8: mov             x1, x0
    // 0x13b4bac: r0 = couponType()
    //     0x13b4bac: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x13b4bb0: ldur            x2, [fp, #-8]
    // 0x13b4bb4: stur            x0, [fp, #-0x20]
    // 0x13b4bb8: LoadField: r1 = r2->field_f
    //     0x13b4bb8: ldur            w1, [x2, #0xf]
    // 0x13b4bbc: DecompressPointer r1
    //     0x13b4bbc: add             x1, x1, HEAP, lsl #32
    // 0x13b4bc0: r0 = controller()
    //     0x13b4bc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4bc4: LoadField: r1 = r0->field_d3
    //     0x13b4bc4: ldur            w1, [x0, #0xd3]
    // 0x13b4bc8: DecompressPointer r1
    //     0x13b4bc8: add             x1, x1, HEAP, lsl #32
    // 0x13b4bcc: stur            x1, [fp, #-0x28]
    // 0x13b4bd0: r0 = CustomStyle()
    //     0x13b4bd0: bl              #0x910168  ; AllocateCustomStyleStub -> CustomStyle (size=0x10)
    // 0x13b4bd4: mov             x2, x0
    // 0x13b4bd8: r0 = Instance_TitleAlignment
    //     0x13b4bd8: add             x0, PP, #0x24, lsl #12  ; [pp+0x24510] Obj!TitleAlignment@d75601
    //     0x13b4bdc: ldr             x0, [x0, #0x510]
    // 0x13b4be0: stur            x2, [fp, #-0x30]
    // 0x13b4be4: StoreField: r2->field_7 = r0
    //     0x13b4be4: stur            w0, [x2, #7]
    // 0x13b4be8: ldur            x0, [fp, #-8]
    // 0x13b4bec: LoadField: r1 = r0->field_f
    //     0x13b4bec: ldur            w1, [x0, #0xf]
    // 0x13b4bf0: DecompressPointer r1
    //     0x13b4bf0: add             x1, x1, HEAP, lsl #32
    // 0x13b4bf4: r0 = controller()
    //     0x13b4bf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b4bf8: mov             x1, x0
    // 0x13b4bfc: r0 = couponType()
    //     0x13b4bfc: bl              #0x8a36ec  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::couponType
    // 0x13b4c00: stur            x0, [fp, #-0x38]
    // 0x13b4c04: r0 = ProductGridItemView()
    //     0x13b4c04: bl              #0x9c2a58  ; AllocateProductGridItemViewStub -> ProductGridItemView (size=0x5c)
    // 0x13b4c08: mov             x1, x0
    // 0x13b4c0c: ldur            x0, [fp, #-0x18]
    // 0x13b4c10: stur            x1, [fp, #-0x40]
    // 0x13b4c14: StoreField: r1->field_b = r0
    //     0x13b4c14: stur            w0, [x1, #0xb]
    // 0x13b4c18: r0 = ""
    //     0x13b4c18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b4c1c: StoreField: r1->field_f = r0
    //     0x13b4c1c: stur            w0, [x1, #0xf]
    // 0x13b4c20: r0 = true
    //     0x13b4c20: add             x0, NULL, #0x20  ; true
    // 0x13b4c24: StoreField: r1->field_13 = r0
    //     0x13b4c24: stur            w0, [x1, #0x13]
    // 0x13b4c28: r0 = ViewAll()
    //     0x13b4c28: bl              #0x90ff98  ; AllocateViewAllStub -> ViewAll (size=0x10)
    // 0x13b4c2c: mov             x1, x0
    // 0x13b4c30: ldur            x0, [fp, #-0x40]
    // 0x13b4c34: ArrayStore: r0[0] = r1  ; List_4
    //     0x13b4c34: stur            w1, [x0, #0x17]
    // 0x13b4c38: r1 = false
    //     0x13b4c38: add             x1, NULL, #0x30  ; false
    // 0x13b4c3c: StoreField: r0->field_1b = r1
    //     0x13b4c3c: stur            w1, [x0, #0x1b]
    // 0x13b4c40: ldur            x1, [fp, #-0x20]
    // 0x13b4c44: StoreField: r0->field_1f = r1
    //     0x13b4c44: stur            w1, [x0, #0x1f]
    // 0x13b4c48: ldur            x1, [fp, #-0x30]
    // 0x13b4c4c: StoreField: r0->field_23 = r1
    //     0x13b4c4c: stur            w1, [x0, #0x23]
    // 0x13b4c50: r1 = "collection_page"
    //     0x13b4c50: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0x13b4c54: ldr             x1, [x1, #0x118]
    // 0x13b4c58: StoreField: r0->field_27 = r1
    //     0x13b4c58: stur            w1, [x0, #0x27]
    // 0x13b4c5c: StoreField: r0->field_33 = r1
    //     0x13b4c5c: stur            w1, [x0, #0x33]
    // 0x13b4c60: StoreField: r0->field_37 = r1
    //     0x13b4c60: stur            w1, [x0, #0x37]
    // 0x13b4c64: ldur            x2, [fp, #-0x38]
    // 0x13b4c68: StoreField: r0->field_43 = r2
    //     0x13b4c68: stur            w2, [x0, #0x43]
    // 0x13b4c6c: StoreField: r0->field_3b = r1
    //     0x13b4c6c: stur            w1, [x0, #0x3b]
    // 0x13b4c70: ldur            x2, [fp, #-8]
    // 0x13b4c74: r1 = Function '<anonymous closure>':.
    //     0x13b4c74: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c120] AnonymousClosure: (0x13b3e3c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b4c78: ldr             x1, [x1, #0x120]
    // 0x13b4c7c: r0 = AllocateClosure()
    //     0x13b4c7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4c80: mov             x1, x0
    // 0x13b4c84: ldur            x0, [fp, #-0x40]
    // 0x13b4c88: StoreField: r0->field_47 = r1
    //     0x13b4c88: stur            w1, [x0, #0x47]
    // 0x13b4c8c: ldur            x2, [fp, #-8]
    // 0x13b4c90: r1 = Function '<anonymous closure>':.
    //     0x13b4c90: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c128] AnonymousClosure: (0x13b276c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b4c94: ldr             x1, [x1, #0x128]
    // 0x13b4c98: r0 = AllocateClosure()
    //     0x13b4c98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4c9c: mov             x1, x0
    // 0x13b4ca0: ldur            x0, [fp, #-0x40]
    // 0x13b4ca4: StoreField: r0->field_4b = r1
    //     0x13b4ca4: stur            w1, [x0, #0x4b]
    // 0x13b4ca8: ldur            x2, [fp, #-8]
    // 0x13b4cac: r1 = Function '<anonymous closure>':.
    //     0x13b4cac: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c130] AnonymousClosure: (0x13b0b1c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b4cb0: ldr             x1, [x1, #0x130]
    // 0x13b4cb4: r0 = AllocateClosure()
    //     0x13b4cb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4cb8: mov             x1, x0
    // 0x13b4cbc: ldur            x0, [fp, #-0x40]
    // 0x13b4cc0: StoreField: r0->field_4f = r1
    //     0x13b4cc0: stur            w1, [x0, #0x4f]
    // 0x13b4cc4: ldur            x1, [fp, #-0x28]
    // 0x13b4cc8: StoreField: r0->field_3f = r1
    //     0x13b4cc8: stur            w1, [x0, #0x3f]
    // 0x13b4ccc: ldur            x2, [fp, #-8]
    // 0x13b4cd0: r1 = Function '<anonymous closure>':.
    //     0x13b4cd0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c138] AnonymousClosure: (0x13b4ff4), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b4cd4: ldr             x1, [x1, #0x138]
    // 0x13b4cd8: r0 = AllocateClosure()
    //     0x13b4cd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4cdc: mov             x1, x0
    // 0x13b4ce0: ldur            x0, [fp, #-0x40]
    // 0x13b4ce4: StoreField: r0->field_53 = r1
    //     0x13b4ce4: stur            w1, [x0, #0x53]
    // 0x13b4ce8: ldur            x2, [fp, #-8]
    // 0x13b4cec: r1 = Function '<anonymous closure>':.
    //     0x13b4cec: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c140] AnonymousClosure: (0x13b4f98), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b4cf0: ldr             x1, [x1, #0x140]
    // 0x13b4cf4: r0 = AllocateClosure()
    //     0x13b4cf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b4cf8: mov             x1, x0
    // 0x13b4cfc: ldur            x0, [fp, #-0x40]
    // 0x13b4d00: StoreField: r0->field_57 = r1
    //     0x13b4d00: stur            w1, [x0, #0x57]
    // 0x13b4d04: r0 = Padding()
    //     0x13b4d04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13b4d08: mov             x2, x0
    // 0x13b4d0c: r0 = Instance_EdgeInsets
    //     0x13b4d0c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x13b4d10: ldr             x0, [x0, #0x668]
    // 0x13b4d14: stur            x2, [fp, #-0x18]
    // 0x13b4d18: StoreField: r2->field_f = r0
    //     0x13b4d18: stur            w0, [x2, #0xf]
    // 0x13b4d1c: ldur            x0, [fp, #-0x40]
    // 0x13b4d20: StoreField: r2->field_b = r0
    //     0x13b4d20: stur            w0, [x2, #0xb]
    // 0x13b4d24: r1 = <FlexParentData>
    //     0x13b4d24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13b4d28: ldr             x1, [x1, #0xe00]
    // 0x13b4d2c: r0 = Expanded()
    //     0x13b4d2c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x13b4d30: stur            x0, [fp, #-0x20]
    // 0x13b4d34: StoreField: r0->field_13 = rZR
    //     0x13b4d34: stur            xzr, [x0, #0x13]
    // 0x13b4d38: r1 = Instance_FlexFit
    //     0x13b4d38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x13b4d3c: ldr             x1, [x1, #0xe08]
    // 0x13b4d40: StoreField: r0->field_1b = r1
    //     0x13b4d40: stur            w1, [x0, #0x1b]
    // 0x13b4d44: ldur            x1, [fp, #-0x18]
    // 0x13b4d48: StoreField: r0->field_b = r1
    //     0x13b4d48: stur            w1, [x0, #0xb]
    // 0x13b4d4c: ldur            x2, [fp, #-0x10]
    // 0x13b4d50: LoadField: r1 = r2->field_b
    //     0x13b4d50: ldur            w1, [x2, #0xb]
    // 0x13b4d54: LoadField: r3 = r2->field_f
    //     0x13b4d54: ldur            w3, [x2, #0xf]
    // 0x13b4d58: DecompressPointer r3
    //     0x13b4d58: add             x3, x3, HEAP, lsl #32
    // 0x13b4d5c: LoadField: r4 = r3->field_b
    //     0x13b4d5c: ldur            w4, [x3, #0xb]
    // 0x13b4d60: r3 = LoadInt32Instr(r1)
    //     0x13b4d60: sbfx            x3, x1, #1, #0x1f
    // 0x13b4d64: stur            x3, [fp, #-0x70]
    // 0x13b4d68: r1 = LoadInt32Instr(r4)
    //     0x13b4d68: sbfx            x1, x4, #1, #0x1f
    // 0x13b4d6c: cmp             x3, x1
    // 0x13b4d70: b.ne            #0x13b4d7c
    // 0x13b4d74: mov             x1, x2
    // 0x13b4d78: r0 = _growToNextCapacity()
    //     0x13b4d78: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x13b4d7c: ldur            x2, [fp, #-0x10]
    // 0x13b4d80: ldur            x3, [fp, #-0x70]
    // 0x13b4d84: add             x0, x3, #1
    // 0x13b4d88: lsl             x1, x0, #1
    // 0x13b4d8c: StoreField: r2->field_b = r1
    //     0x13b4d8c: stur            w1, [x2, #0xb]
    // 0x13b4d90: LoadField: r1 = r2->field_f
    //     0x13b4d90: ldur            w1, [x2, #0xf]
    // 0x13b4d94: DecompressPointer r1
    //     0x13b4d94: add             x1, x1, HEAP, lsl #32
    // 0x13b4d98: ldur            x0, [fp, #-0x20]
    // 0x13b4d9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x13b4d9c: add             x25, x1, x3, lsl #2
    //     0x13b4da0: add             x25, x25, #0xf
    //     0x13b4da4: str             w0, [x25]
    //     0x13b4da8: tbz             w0, #0, #0x13b4dc4
    //     0x13b4dac: ldurb           w16, [x1, #-1]
    //     0x13b4db0: ldurb           w17, [x0, #-1]
    //     0x13b4db4: and             x16, x17, x16, lsr #2
    //     0x13b4db8: tst             x16, HEAP, lsr #32
    //     0x13b4dbc: b.eq            #0x13b4dc4
    //     0x13b4dc0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13b4dc4: b               #0x13b4f18
    // 0x13b4dc8: ldur            x0, [fp, #-8]
    // 0x13b4dcc: ldur            x2, [fp, #-0x10]
    // 0x13b4dd0: LoadField: r1 = r0->field_13
    //     0x13b4dd0: ldur            w1, [x0, #0x13]
    // 0x13b4dd4: DecompressPointer r1
    //     0x13b4dd4: add             x1, x1, HEAP, lsl #32
    // 0x13b4dd8: r0 = of()
    //     0x13b4dd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13b4ddc: LoadField: r1 = r0->field_87
    //     0x13b4ddc: ldur            w1, [x0, #0x87]
    // 0x13b4de0: DecompressPointer r1
    //     0x13b4de0: add             x1, x1, HEAP, lsl #32
    // 0x13b4de4: LoadField: r0 = r1->field_23
    //     0x13b4de4: ldur            w0, [x1, #0x23]
    // 0x13b4de8: DecompressPointer r0
    //     0x13b4de8: add             x0, x0, HEAP, lsl #32
    // 0x13b4dec: r16 = 21.000000
    //     0x13b4dec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x13b4df0: ldr             x16, [x16, #0x9b0]
    // 0x13b4df4: r30 = Instance_Color
    //     0x13b4df4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13b4df8: stp             lr, x16, [SP]
    // 0x13b4dfc: mov             x1, x0
    // 0x13b4e00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13b4e00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13b4e04: ldr             x4, [x4, #0xaa0]
    // 0x13b4e08: r0 = copyWith()
    //     0x13b4e08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13b4e0c: stur            x0, [fp, #-8]
    // 0x13b4e10: r0 = Text()
    //     0x13b4e10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13b4e14: mov             x1, x0
    // 0x13b4e18: r0 = "No Products Found!"
    //     0x13b4e18: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c148] "No Products Found!"
    //     0x13b4e1c: ldr             x0, [x0, #0x148]
    // 0x13b4e20: stur            x1, [fp, #-0x18]
    // 0x13b4e24: StoreField: r1->field_b = r0
    //     0x13b4e24: stur            w0, [x1, #0xb]
    // 0x13b4e28: ldur            x0, [fp, #-8]
    // 0x13b4e2c: StoreField: r1->field_13 = r0
    //     0x13b4e2c: stur            w0, [x1, #0x13]
    // 0x13b4e30: r0 = Padding()
    //     0x13b4e30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13b4e34: mov             x1, x0
    // 0x13b4e38: r0 = Instance_EdgeInsets
    //     0x13b4e38: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c150] Obj!EdgeInsets@d59bd1
    //     0x13b4e3c: ldr             x0, [x0, #0x150]
    // 0x13b4e40: stur            x1, [fp, #-8]
    // 0x13b4e44: StoreField: r1->field_f = r0
    //     0x13b4e44: stur            w0, [x1, #0xf]
    // 0x13b4e48: ldur            x0, [fp, #-0x18]
    // 0x13b4e4c: StoreField: r1->field_b = r0
    //     0x13b4e4c: stur            w0, [x1, #0xb]
    // 0x13b4e50: r0 = Align()
    //     0x13b4e50: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x13b4e54: mov             x2, x0
    // 0x13b4e58: r0 = Instance_Alignment
    //     0x13b4e58: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13b4e5c: ldr             x0, [x0, #0xb10]
    // 0x13b4e60: stur            x2, [fp, #-0x18]
    // 0x13b4e64: StoreField: r2->field_f = r0
    //     0x13b4e64: stur            w0, [x2, #0xf]
    // 0x13b4e68: ldur            x0, [fp, #-8]
    // 0x13b4e6c: StoreField: r2->field_b = r0
    //     0x13b4e6c: stur            w0, [x2, #0xb]
    // 0x13b4e70: r1 = <FlexParentData>
    //     0x13b4e70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x13b4e74: ldr             x1, [x1, #0xe00]
    // 0x13b4e78: r0 = Flexible()
    //     0x13b4e78: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x13b4e7c: mov             x2, x0
    // 0x13b4e80: r0 = 1
    //     0x13b4e80: movz            x0, #0x1
    // 0x13b4e84: stur            x2, [fp, #-8]
    // 0x13b4e88: StoreField: r2->field_13 = r0
    //     0x13b4e88: stur            x0, [x2, #0x13]
    // 0x13b4e8c: r0 = Instance_FlexFit
    //     0x13b4e8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x13b4e90: ldr             x0, [x0, #0xe20]
    // 0x13b4e94: StoreField: r2->field_1b = r0
    //     0x13b4e94: stur            w0, [x2, #0x1b]
    // 0x13b4e98: ldur            x0, [fp, #-0x18]
    // 0x13b4e9c: StoreField: r2->field_b = r0
    //     0x13b4e9c: stur            w0, [x2, #0xb]
    // 0x13b4ea0: ldur            x0, [fp, #-0x10]
    // 0x13b4ea4: LoadField: r1 = r0->field_b
    //     0x13b4ea4: ldur            w1, [x0, #0xb]
    // 0x13b4ea8: LoadField: r3 = r0->field_f
    //     0x13b4ea8: ldur            w3, [x0, #0xf]
    // 0x13b4eac: DecompressPointer r3
    //     0x13b4eac: add             x3, x3, HEAP, lsl #32
    // 0x13b4eb0: LoadField: r4 = r3->field_b
    //     0x13b4eb0: ldur            w4, [x3, #0xb]
    // 0x13b4eb4: r3 = LoadInt32Instr(r1)
    //     0x13b4eb4: sbfx            x3, x1, #1, #0x1f
    // 0x13b4eb8: stur            x3, [fp, #-0x70]
    // 0x13b4ebc: r1 = LoadInt32Instr(r4)
    //     0x13b4ebc: sbfx            x1, x4, #1, #0x1f
    // 0x13b4ec0: cmp             x3, x1
    // 0x13b4ec4: b.ne            #0x13b4ed0
    // 0x13b4ec8: mov             x1, x0
    // 0x13b4ecc: r0 = _growToNextCapacity()
    //     0x13b4ecc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x13b4ed0: ldur            x2, [fp, #-0x10]
    // 0x13b4ed4: ldur            x3, [fp, #-0x70]
    // 0x13b4ed8: add             x0, x3, #1
    // 0x13b4edc: lsl             x1, x0, #1
    // 0x13b4ee0: StoreField: r2->field_b = r1
    //     0x13b4ee0: stur            w1, [x2, #0xb]
    // 0x13b4ee4: LoadField: r1 = r2->field_f
    //     0x13b4ee4: ldur            w1, [x2, #0xf]
    // 0x13b4ee8: DecompressPointer r1
    //     0x13b4ee8: add             x1, x1, HEAP, lsl #32
    // 0x13b4eec: ldur            x0, [fp, #-8]
    // 0x13b4ef0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x13b4ef0: add             x25, x1, x3, lsl #2
    //     0x13b4ef4: add             x25, x25, #0xf
    //     0x13b4ef8: str             w0, [x25]
    //     0x13b4efc: tbz             w0, #0, #0x13b4f18
    //     0x13b4f00: ldurb           w16, [x1, #-1]
    //     0x13b4f04: ldurb           w17, [x0, #-1]
    //     0x13b4f08: and             x16, x17, x16, lsr #2
    //     0x13b4f0c: tst             x16, HEAP, lsr #32
    //     0x13b4f10: b.eq            #0x13b4f18
    //     0x13b4f14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13b4f18: r0 = Column()
    //     0x13b4f18: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13b4f1c: r1 = Instance_Axis
    //     0x13b4f1c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13b4f20: StoreField: r0->field_f = r1
    //     0x13b4f20: stur            w1, [x0, #0xf]
    // 0x13b4f24: r1 = Instance_MainAxisAlignment
    //     0x13b4f24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x13b4f28: ldr             x1, [x1, #0xab0]
    // 0x13b4f2c: StoreField: r0->field_13 = r1
    //     0x13b4f2c: stur            w1, [x0, #0x13]
    // 0x13b4f30: r1 = Instance_MainAxisSize
    //     0x13b4f30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13b4f34: ldr             x1, [x1, #0xdd0]
    // 0x13b4f38: ArrayStore: r0[0] = r1  ; List_4
    //     0x13b4f38: stur            w1, [x0, #0x17]
    // 0x13b4f3c: r1 = Instance_CrossAxisAlignment
    //     0x13b4f3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13b4f40: ldr             x1, [x1, #0xa18]
    // 0x13b4f44: StoreField: r0->field_1b = r1
    //     0x13b4f44: stur            w1, [x0, #0x1b]
    // 0x13b4f48: r1 = Instance_VerticalDirection
    //     0x13b4f48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13b4f4c: ldr             x1, [x1, #0xa20]
    // 0x13b4f50: StoreField: r0->field_23 = r1
    //     0x13b4f50: stur            w1, [x0, #0x23]
    // 0x13b4f54: r1 = Instance_Clip
    //     0x13b4f54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13b4f58: ldr             x1, [x1, #0x38]
    // 0x13b4f5c: StoreField: r0->field_2b = r1
    //     0x13b4f5c: stur            w1, [x0, #0x2b]
    // 0x13b4f60: StoreField: r0->field_2f = rZR
    //     0x13b4f60: stur            xzr, [x0, #0x2f]
    // 0x13b4f64: ldur            x1, [fp, #-0x10]
    // 0x13b4f68: StoreField: r0->field_b = r1
    //     0x13b4f68: stur            w1, [x0, #0xb]
    // 0x13b4f6c: LeaveFrame
    //     0x13b4f6c: mov             SP, fp
    //     0x13b4f70: ldp             fp, lr, [SP], #0x10
    // 0x13b4f74: ret
    //     0x13b4f74: ret             
    // 0x13b4f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b4f78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b4f7c: b               #0x13b3efc
    // 0x13b4f80: SaveReg d0
    //     0x13b4f80: str             q0, [SP, #-0x10]!
    // 0x13b4f84: SaveReg r1
    //     0x13b4f84: str             x1, [SP, #-8]!
    // 0x13b4f88: r0 = AllocateDouble()
    //     0x13b4f88: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13b4f8c: RestoreReg r1
    //     0x13b4f8c: ldr             x1, [SP], #8
    // 0x13b4f90: RestoreReg d0
    //     0x13b4f90: ldr             q0, [SP], #0x10
    // 0x13b4f94: b               #0x13b45f0
  }
  [closure] Null <anonymous closure>(dynamic, Entity, String, String) {
    // ** addr: 0x13b4f98, size: 0x5c
    // 0x13b4f98: EnterFrame
    //     0x13b4f98: stp             fp, lr, [SP, #-0x10]!
    //     0x13b4f9c: mov             fp, SP
    // 0x13b4fa0: ldr             x0, [fp, #0x28]
    // 0x13b4fa4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13b4fa4: ldur            w1, [x0, #0x17]
    // 0x13b4fa8: DecompressPointer r1
    //     0x13b4fa8: add             x1, x1, HEAP, lsl #32
    // 0x13b4fac: CheckStackOverflow
    //     0x13b4fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b4fb0: cmp             SP, x16
    //     0x13b4fb4: b.ls            #0x13b4fec
    // 0x13b4fb8: LoadField: r0 = r1->field_f
    //     0x13b4fb8: ldur            w0, [x1, #0xf]
    // 0x13b4fbc: DecompressPointer r0
    //     0x13b4fbc: add             x0, x0, HEAP, lsl #32
    // 0x13b4fc0: LoadField: r6 = r1->field_13
    //     0x13b4fc0: ldur            w6, [x1, #0x13]
    // 0x13b4fc4: DecompressPointer r6
    //     0x13b4fc4: add             x6, x6, HEAP, lsl #32
    // 0x13b4fc8: mov             x1, x0
    // 0x13b4fcc: ldr             x2, [fp, #0x20]
    // 0x13b4fd0: ldr             x3, [fp, #0x18]
    // 0x13b4fd4: ldr             x5, [fp, #0x10]
    // 0x13b4fd8: r0 = showSizeBottomSheet()
    //     0x13b4fd8: bl              #0x13b062c  ; [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet
    // 0x13b4fdc: r0 = Null
    //     0x13b4fdc: mov             x0, NULL
    // 0x13b4fe0: LeaveFrame
    //     0x13b4fe0: mov             SP, fp
    //     0x13b4fe4: ldp             fp, lr, [SP], #0x10
    // 0x13b4fe8: ret
    //     0x13b4fe8: ret             
    // 0x13b4fec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b4fec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b4ff0: b               #0x13b4fb8
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x13b4ff4, size: 0x180
    // 0x13b4ff4: EnterFrame
    //     0x13b4ff4: stp             fp, lr, [SP, #-0x10]!
    //     0x13b4ff8: mov             fp, SP
    // 0x13b4ffc: AllocStack(0x10)
    //     0x13b4ffc: sub             SP, SP, #0x10
    // 0x13b5000: SetupParameters()
    //     0x13b5000: ldr             x0, [fp, #0x38]
    //     0x13b5004: ldur            w1, [x0, #0x17]
    //     0x13b5008: add             x1, x1, HEAP, lsl #32
    // 0x13b500c: CheckStackOverflow
    //     0x13b500c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b5010: cmp             SP, x16
    //     0x13b5014: b.ls            #0x13b516c
    // 0x13b5018: LoadField: r0 = r1->field_f
    //     0x13b5018: ldur            w0, [x1, #0xf]
    // 0x13b501c: DecompressPointer r0
    //     0x13b501c: add             x0, x0, HEAP, lsl #32
    // 0x13b5020: mov             x1, x0
    // 0x13b5024: r0 = controller()
    //     0x13b5024: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5028: mov             x3, x0
    // 0x13b502c: ldr             x0, [fp, #0x28]
    // 0x13b5030: r2 = Null
    //     0x13b5030: mov             x2, NULL
    // 0x13b5034: r1 = Null
    //     0x13b5034: mov             x1, NULL
    // 0x13b5038: stur            x3, [fp, #-8]
    // 0x13b503c: r4 = 60
    //     0x13b503c: movz            x4, #0x3c
    // 0x13b5040: branchIfSmi(r0, 0x13b504c)
    //     0x13b5040: tbz             w0, #0, #0x13b504c
    // 0x13b5044: r4 = LoadClassIdInstr(r0)
    //     0x13b5044: ldur            x4, [x0, #-1]
    //     0x13b5048: ubfx            x4, x4, #0xc, #0x14
    // 0x13b504c: sub             x4, x4, #0x5e
    // 0x13b5050: cmp             x4, #1
    // 0x13b5054: b.ls            #0x13b5068
    // 0x13b5058: r8 = String
    //     0x13b5058: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x13b505c: r3 = Null
    //     0x13b505c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c270] Null
    //     0x13b5060: ldr             x3, [x3, #0x270]
    // 0x13b5064: r0 = String()
    //     0x13b5064: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x13b5068: ldr             x0, [fp, #0x20]
    // 0x13b506c: r2 = Null
    //     0x13b506c: mov             x2, NULL
    // 0x13b5070: r1 = Null
    //     0x13b5070: mov             x1, NULL
    // 0x13b5074: r4 = 60
    //     0x13b5074: movz            x4, #0x3c
    // 0x13b5078: branchIfSmi(r0, 0x13b5084)
    //     0x13b5078: tbz             w0, #0, #0x13b5084
    // 0x13b507c: r4 = LoadClassIdInstr(r0)
    //     0x13b507c: ldur            x4, [x0, #-1]
    //     0x13b5080: ubfx            x4, x4, #0xc, #0x14
    // 0x13b5084: sub             x4, x4, #0x5e
    // 0x13b5088: cmp             x4, #1
    // 0x13b508c: b.ls            #0x13b50a0
    // 0x13b5090: r8 = String
    //     0x13b5090: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x13b5094: r3 = Null
    //     0x13b5094: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c280] Null
    //     0x13b5098: ldr             x3, [x3, #0x280]
    // 0x13b509c: r0 = String()
    //     0x13b509c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x13b50a0: ldr             x0, [fp, #0x18]
    // 0x13b50a4: r2 = Null
    //     0x13b50a4: mov             x2, NULL
    // 0x13b50a8: r1 = Null
    //     0x13b50a8: mov             x1, NULL
    // 0x13b50ac: branchIfSmi(r0, 0x13b50d4)
    //     0x13b50ac: tbz             w0, #0, #0x13b50d4
    // 0x13b50b0: r4 = LoadClassIdInstr(r0)
    //     0x13b50b0: ldur            x4, [x0, #-1]
    //     0x13b50b4: ubfx            x4, x4, #0xc, #0x14
    // 0x13b50b8: sub             x4, x4, #0x3c
    // 0x13b50bc: cmp             x4, #1
    // 0x13b50c0: b.ls            #0x13b50d4
    // 0x13b50c4: r8 = int
    //     0x13b50c4: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x13b50c8: r3 = Null
    //     0x13b50c8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c290] Null
    //     0x13b50cc: ldr             x3, [x3, #0x290]
    // 0x13b50d0: r0 = int()
    //     0x13b50d0: bl              #0x16fc548  ; IsType_int_Stub
    // 0x13b50d4: ldr             x0, [fp, #0x10]
    // 0x13b50d8: cmp             w0, NULL
    // 0x13b50dc: b.ne            #0x13b50ec
    // 0x13b50e0: r0 = ProductRating()
    //     0x13b50e0: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0x13b50e4: mov             x4, x0
    // 0x13b50e8: b               #0x13b50f0
    // 0x13b50ec: mov             x4, x0
    // 0x13b50f0: ldr             x3, [fp, #0x18]
    // 0x13b50f4: mov             x0, x4
    // 0x13b50f8: stur            x4, [fp, #-0x10]
    // 0x13b50fc: r2 = Null
    //     0x13b50fc: mov             x2, NULL
    // 0x13b5100: r1 = Null
    //     0x13b5100: mov             x1, NULL
    // 0x13b5104: r4 = 60
    //     0x13b5104: movz            x4, #0x3c
    // 0x13b5108: branchIfSmi(r0, 0x13b5114)
    //     0x13b5108: tbz             w0, #0, #0x13b5114
    // 0x13b510c: r4 = LoadClassIdInstr(r0)
    //     0x13b510c: ldur            x4, [x0, #-1]
    //     0x13b5110: ubfx            x4, x4, #0xc, #0x14
    // 0x13b5114: r17 = 5178
    //     0x13b5114: movz            x17, #0x143a
    // 0x13b5118: cmp             x4, x17
    // 0x13b511c: b.eq            #0x13b5134
    // 0x13b5120: r8 = ProductRating
    //     0x13b5120: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee30] Type: ProductRating
    //     0x13b5124: ldr             x8, [x8, #0xe30]
    // 0x13b5128: r3 = Null
    //     0x13b5128: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c2a0] Null
    //     0x13b512c: ldr             x3, [x3, #0x2a0]
    // 0x13b5130: r0 = DefaultTypeTest()
    //     0x13b5130: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x13b5134: ldr             x0, [fp, #0x18]
    // 0x13b5138: r6 = LoadInt32Instr(r0)
    //     0x13b5138: sbfx            x6, x0, #1, #0x1f
    //     0x13b513c: tbz             w0, #0, #0x13b5144
    //     0x13b5140: ldur            x6, [x0, #7]
    // 0x13b5144: ldur            x1, [fp, #-8]
    // 0x13b5148: ldr             x2, [fp, #0x30]
    // 0x13b514c: ldr             x3, [fp, #0x28]
    // 0x13b5150: ldr             x5, [fp, #0x20]
    // 0x13b5154: ldur            x7, [fp, #-0x10]
    // 0x13b5158: r0 = productViewedEvent()
    //     0x13b5158: bl              #0x13b084c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::productViewedEvent
    // 0x13b515c: r0 = Null
    //     0x13b515c: mov             x0, NULL
    // 0x13b5160: LeaveFrame
    //     0x13b5160: mov             SP, fp
    //     0x13b5164: ldp             fp, lr, [SP], #0x10
    // 0x13b5168: ret
    //     0x13b5168: ret             
    // 0x13b516c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b516c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b5170: b               #0x13b5018
  }
  [closure] (dynamic) => void <anonymous closure>(dynamic, int, String) {
    // ** addr: 0x13b5174, size: 0x54
    // 0x13b5174: EnterFrame
    //     0x13b5174: stp             fp, lr, [SP, #-0x10]!
    //     0x13b5178: mov             fp, SP
    // 0x13b517c: AllocStack(0x8)
    //     0x13b517c: sub             SP, SP, #8
    // 0x13b5180: SetupParameters()
    //     0x13b5180: ldr             x0, [fp, #0x20]
    //     0x13b5184: ldur            w1, [x0, #0x17]
    //     0x13b5188: add             x1, x1, HEAP, lsl #32
    //     0x13b518c: stur            x1, [fp, #-8]
    // 0x13b5190: r1 = 1
    //     0x13b5190: movz            x1, #0x1
    // 0x13b5194: r0 = AllocateContext()
    //     0x13b5194: bl              #0x16f6108  ; AllocateContextStub
    // 0x13b5198: mov             x1, x0
    // 0x13b519c: ldur            x0, [fp, #-8]
    // 0x13b51a0: StoreField: r1->field_b = r0
    //     0x13b51a0: stur            w0, [x1, #0xb]
    // 0x13b51a4: ldr             x0, [fp, #0x10]
    // 0x13b51a8: StoreField: r1->field_f = r0
    //     0x13b51a8: stur            w0, [x1, #0xf]
    // 0x13b51ac: mov             x2, x1
    // 0x13b51b0: r1 = Function '<anonymous closure>':.
    //     0x13b51b0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c2c0] AnonymousClosure: (0x13b51c8), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13b51b4: ldr             x1, [x1, #0x2c0]
    // 0x13b51b8: r0 = AllocateClosure()
    //     0x13b51b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b51bc: LeaveFrame
    //     0x13b51bc: mov             SP, fp
    //     0x13b51c0: ldp             fp, lr, [SP], #0x10
    // 0x13b51c4: ret
    //     0x13b51c4: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13b51c8, size: 0xb60
    // 0x13b51c8: EnterFrame
    //     0x13b51c8: stp             fp, lr, [SP, #-0x10]!
    //     0x13b51cc: mov             fp, SP
    // 0x13b51d0: AllocStack(0x50)
    //     0x13b51d0: sub             SP, SP, #0x50
    // 0x13b51d4: SetupParameters()
    //     0x13b51d4: ldr             x0, [fp, #0x10]
    //     0x13b51d8: ldur            w1, [x0, #0x17]
    //     0x13b51dc: add             x1, x1, HEAP, lsl #32
    //     0x13b51e0: stur            x1, [fp, #-0x18]
    // 0x13b51e4: CheckStackOverflow
    //     0x13b51e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b51e8: cmp             SP, x16
    //     0x13b51ec: b.ls            #0x13b5cfc
    // 0x13b51f0: LoadField: r0 = r1->field_b
    //     0x13b51f0: ldur            w0, [x1, #0xb]
    // 0x13b51f4: DecompressPointer r0
    //     0x13b51f4: add             x0, x0, HEAP, lsl #32
    // 0x13b51f8: stur            x0, [fp, #-0x10]
    // 0x13b51fc: LoadField: r2 = r0->field_f
    //     0x13b51fc: ldur            w2, [x0, #0xf]
    // 0x13b5200: DecompressPointer r2
    //     0x13b5200: add             x2, x2, HEAP, lsl #32
    // 0x13b5204: stur            x2, [fp, #-8]
    // 0x13b5208: r0 = FilterResponse()
    //     0x13b5208: bl              #0x91d7fc  ; AllocateFilterResponseStub -> FilterResponse (size=0x18)
    // 0x13b520c: ldur            x1, [fp, #-8]
    // 0x13b5210: StoreField: r1->field_13 = r0
    //     0x13b5210: stur            w0, [x1, #0x13]
    //     0x13b5214: ldurb           w16, [x1, #-1]
    //     0x13b5218: ldurb           w17, [x0, #-1]
    //     0x13b521c: and             x16, x17, x16, lsr #2
    //     0x13b5220: tst             x16, HEAP, lsr #32
    //     0x13b5224: b.eq            #0x13b522c
    //     0x13b5228: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13b522c: r0 = controller()
    //     0x13b522c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5230: LoadField: r1 = r0->field_5f
    //     0x13b5230: ldur            w1, [x0, #0x5f]
    // 0x13b5234: DecompressPointer r1
    //     0x13b5234: add             x1, x1, HEAP, lsl #32
    // 0x13b5238: r0 = value()
    //     0x13b5238: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b523c: LoadField: r1 = r0->field_7
    //     0x13b523c: ldur            w1, [x0, #7]
    // 0x13b5240: DecompressPointer r1
    //     0x13b5240: add             x1, x1, HEAP, lsl #32
    // 0x13b5244: cmp             w1, NULL
    // 0x13b5248: b.eq            #0x13b525c
    // 0x13b524c: ldur            x0, [fp, #-0x18]
    // 0x13b5250: LoadField: r2 = r0->field_f
    //     0x13b5250: ldur            w2, [x0, #0xf]
    // 0x13b5254: DecompressPointer r2
    //     0x13b5254: add             x2, x2, HEAP, lsl #32
    // 0x13b5258: r0 = remove()
    //     0x13b5258: bl              #0x71df18  ; [dart:core] _GrowableList::remove
    // 0x13b525c: ldur            x0, [fp, #-0x10]
    // 0x13b5260: LoadField: r1 = r0->field_f
    //     0x13b5260: ldur            w1, [x0, #0xf]
    // 0x13b5264: DecompressPointer r1
    //     0x13b5264: add             x1, x1, HEAP, lsl #32
    // 0x13b5268: r0 = controller()
    //     0x13b5268: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b526c: LoadField: r1 = r0->field_63
    //     0x13b526c: ldur            w1, [x0, #0x63]
    // 0x13b5270: DecompressPointer r1
    //     0x13b5270: add             x1, x1, HEAP, lsl #32
    // 0x13b5274: r0 = value()
    //     0x13b5274: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b5278: LoadField: r1 = r0->field_7
    //     0x13b5278: ldur            w1, [x0, #7]
    // 0x13b527c: DecompressPointer r1
    //     0x13b527c: add             x1, x1, HEAP, lsl #32
    // 0x13b5280: cmp             w1, NULL
    // 0x13b5284: b.eq            #0x13b5554
    // 0x13b5288: ldur            x0, [fp, #-0x10]
    // 0x13b528c: LoadField: r1 = r0->field_f
    //     0x13b528c: ldur            w1, [x0, #0xf]
    // 0x13b5290: DecompressPointer r1
    //     0x13b5290: add             x1, x1, HEAP, lsl #32
    // 0x13b5294: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x13b5294: ldur            w2, [x1, #0x17]
    // 0x13b5298: DecompressPointer r2
    //     0x13b5298: add             x2, x2, HEAP, lsl #32
    // 0x13b529c: stur            x2, [fp, #-8]
    // 0x13b52a0: r0 = controller()
    //     0x13b52a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b52a4: LoadField: r1 = r0->field_63
    //     0x13b52a4: ldur            w1, [x0, #0x63]
    // 0x13b52a8: DecompressPointer r1
    //     0x13b52a8: add             x1, x1, HEAP, lsl #32
    // 0x13b52ac: r0 = value()
    //     0x13b52ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b52b0: LoadField: r2 = r0->field_7
    //     0x13b52b0: ldur            w2, [x0, #7]
    // 0x13b52b4: DecompressPointer r2
    //     0x13b52b4: add             x2, x2, HEAP, lsl #32
    // 0x13b52b8: cmp             w2, NULL
    // 0x13b52bc: b.eq            #0x13b5d04
    // 0x13b52c0: ldur            x1, [fp, #-8]
    // 0x13b52c4: r0 = addAll()
    //     0x13b52c4: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x13b52c8: ldur            x0, [fp, #-0x10]
    // 0x13b52cc: LoadField: r1 = r0->field_f
    //     0x13b52cc: ldur            w1, [x0, #0xf]
    // 0x13b52d0: DecompressPointer r1
    //     0x13b52d0: add             x1, x1, HEAP, lsl #32
    // 0x13b52d4: r0 = controller()
    //     0x13b52d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b52d8: LoadField: r1 = r0->field_67
    //     0x13b52d8: ldur            w1, [x0, #0x67]
    // 0x13b52dc: DecompressPointer r1
    //     0x13b52dc: add             x1, x1, HEAP, lsl #32
    // 0x13b52e0: r0 = value()
    //     0x13b52e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b52e4: LoadField: r1 = r0->field_b
    //     0x13b52e4: ldur            w1, [x0, #0xb]
    // 0x13b52e8: DecompressPointer r1
    //     0x13b52e8: add             x1, x1, HEAP, lsl #32
    // 0x13b52ec: cmp             w1, NULL
    // 0x13b52f0: b.ne            #0x13b52fc
    // 0x13b52f4: r0 = Null
    //     0x13b52f4: mov             x0, NULL
    // 0x13b52f8: b               #0x13b5320
    // 0x13b52fc: LoadField: r0 = r1->field_27
    //     0x13b52fc: ldur            w0, [x1, #0x27]
    // 0x13b5300: DecompressPointer r0
    //     0x13b5300: add             x0, x0, HEAP, lsl #32
    // 0x13b5304: cmp             w0, NULL
    // 0x13b5308: b.ne            #0x13b5314
    // 0x13b530c: r0 = Null
    //     0x13b530c: mov             x0, NULL
    // 0x13b5310: b               #0x13b5320
    // 0x13b5314: LoadField: r1 = r0->field_7
    //     0x13b5314: ldur            w1, [x0, #7]
    // 0x13b5318: DecompressPointer r1
    //     0x13b5318: add             x1, x1, HEAP, lsl #32
    // 0x13b531c: mov             x0, x1
    // 0x13b5320: cmp             w0, NULL
    // 0x13b5324: b.ne            #0x13b536c
    // 0x13b5328: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x13b5328: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13b532c: ldr             x0, [x0]
    //     0x13b5330: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13b5334: cmp             w0, w16
    //     0x13b5338: b.ne            #0x13b5344
    //     0x13b533c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x13b5340: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13b5344: r1 = <ProductType>
    //     0x13b5344: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bbd0] TypeArguments: <ProductType>
    //     0x13b5348: ldr             x1, [x1, #0xbd0]
    // 0x13b534c: stur            x0, [fp, #-8]
    // 0x13b5350: r0 = AllocateGrowableArray()
    //     0x13b5350: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b5354: mov             x1, x0
    // 0x13b5358: ldur            x0, [fp, #-8]
    // 0x13b535c: StoreField: r1->field_f = r0
    //     0x13b535c: stur            w0, [x1, #0xf]
    // 0x13b5360: StoreField: r1->field_b = rZR
    //     0x13b5360: stur            wzr, [x1, #0xb]
    // 0x13b5364: mov             x3, x1
    // 0x13b5368: b               #0x13b5370
    // 0x13b536c: mov             x3, x0
    // 0x13b5370: stur            x3, [fp, #-0x38]
    // 0x13b5374: LoadField: r4 = r3->field_7
    //     0x13b5374: ldur            w4, [x3, #7]
    // 0x13b5378: DecompressPointer r4
    //     0x13b5378: add             x4, x4, HEAP, lsl #32
    // 0x13b537c: stur            x4, [fp, #-0x30]
    // 0x13b5380: LoadField: r0 = r3->field_b
    //     0x13b5380: ldur            w0, [x3, #0xb]
    // 0x13b5384: r5 = LoadInt32Instr(r0)
    //     0x13b5384: sbfx            x5, x0, #1, #0x1f
    // 0x13b5388: stur            x5, [fp, #-0x28]
    // 0x13b538c: r0 = 0
    //     0x13b538c: movz            x0, #0
    // 0x13b5390: ldur            x7, [fp, #-0x18]
    // 0x13b5394: ldur            x6, [fp, #-0x10]
    // 0x13b5398: CheckStackOverflow
    //     0x13b5398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b539c: cmp             SP, x16
    //     0x13b53a0: b.ls            #0x13b5d08
    // 0x13b53a4: LoadField: r1 = r3->field_b
    //     0x13b53a4: ldur            w1, [x3, #0xb]
    // 0x13b53a8: r2 = LoadInt32Instr(r1)
    //     0x13b53a8: sbfx            x2, x1, #1, #0x1f
    // 0x13b53ac: cmp             x5, x2
    // 0x13b53b0: b.ne            #0x13b5cdc
    // 0x13b53b4: cmp             x0, x2
    // 0x13b53b8: b.ge            #0x13b54dc
    // 0x13b53bc: LoadField: r1 = r3->field_f
    //     0x13b53bc: ldur            w1, [x3, #0xf]
    // 0x13b53c0: DecompressPointer r1
    //     0x13b53c0: add             x1, x1, HEAP, lsl #32
    // 0x13b53c4: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x13b53c4: add             x16, x1, x0, lsl #2
    //     0x13b53c8: ldur            w8, [x16, #0xf]
    // 0x13b53cc: DecompressPointer r8
    //     0x13b53cc: add             x8, x8, HEAP, lsl #32
    // 0x13b53d0: stur            x8, [fp, #-8]
    // 0x13b53d4: add             x9, x0, #1
    // 0x13b53d8: stur            x9, [fp, #-0x20]
    // 0x13b53dc: cmp             w8, NULL
    // 0x13b53e0: b.ne            #0x13b5414
    // 0x13b53e4: mov             x0, x8
    // 0x13b53e8: mov             x2, x4
    // 0x13b53ec: r1 = Null
    //     0x13b53ec: mov             x1, NULL
    // 0x13b53f0: cmp             w2, NULL
    // 0x13b53f4: b.eq            #0x13b5414
    // 0x13b53f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13b53f8: ldur            w4, [x2, #0x17]
    // 0x13b53fc: DecompressPointer r4
    //     0x13b53fc: add             x4, x4, HEAP, lsl #32
    // 0x13b5400: r8 = X0
    //     0x13b5400: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x13b5404: LoadField: r9 = r4->field_7
    //     0x13b5404: ldur            x9, [x4, #7]
    // 0x13b5408: r3 = Null
    //     0x13b5408: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c2c8] Null
    //     0x13b540c: ldr             x3, [x3, #0x2c8]
    // 0x13b5410: blr             x9
    // 0x13b5414: ldur            x1, [fp, #-0x18]
    // 0x13b5418: ldur            x2, [fp, #-8]
    // 0x13b541c: LoadField: r0 = r2->field_f
    //     0x13b541c: ldur            w0, [x2, #0xf]
    // 0x13b5420: DecompressPointer r0
    //     0x13b5420: add             x0, x0, HEAP, lsl #32
    // 0x13b5424: LoadField: r3 = r1->field_f
    //     0x13b5424: ldur            w3, [x1, #0xf]
    // 0x13b5428: DecompressPointer r3
    //     0x13b5428: add             x3, x3, HEAP, lsl #32
    // 0x13b542c: r4 = LoadClassIdInstr(r0)
    //     0x13b542c: ldur            x4, [x0, #-1]
    //     0x13b5430: ubfx            x4, x4, #0xc, #0x14
    // 0x13b5434: stp             x3, x0, [SP]
    // 0x13b5438: mov             x0, x4
    // 0x13b543c: mov             lr, x0
    // 0x13b5440: ldr             lr, [x21, lr, lsl #3]
    // 0x13b5444: blr             lr
    // 0x13b5448: tbnz            w0, #4, #0x13b54c8
    // 0x13b544c: ldur            x3, [fp, #-0x10]
    // 0x13b5450: ldur            x0, [fp, #-8]
    // 0x13b5454: LoadField: r1 = r3->field_f
    //     0x13b5454: ldur            w1, [x3, #0xf]
    // 0x13b5458: DecompressPointer r1
    //     0x13b5458: add             x1, x1, HEAP, lsl #32
    // 0x13b545c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x13b545c: ldur            w2, [x1, #0x17]
    // 0x13b5460: DecompressPointer r2
    //     0x13b5460: add             x2, x2, HEAP, lsl #32
    // 0x13b5464: LoadField: r1 = r0->field_7
    //     0x13b5464: ldur            w1, [x0, #7]
    // 0x13b5468: DecompressPointer r1
    //     0x13b5468: add             x1, x1, HEAP, lsl #32
    // 0x13b546c: LoadField: r4 = r2->field_f
    //     0x13b546c: ldur            w4, [x2, #0xf]
    // 0x13b5470: DecompressPointer r4
    //     0x13b5470: add             x4, x4, HEAP, lsl #32
    // 0x13b5474: mov             x16, x1
    // 0x13b5478: mov             x1, x2
    // 0x13b547c: mov             x2, x16
    // 0x13b5480: stur            x4, [fp, #-0x40]
    // 0x13b5484: r0 = _getKeyOrData()
    //     0x13b5484: bl              #0x69cfb4  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_getKeyOrData
    // 0x13b5488: mov             x1, x0
    // 0x13b548c: ldur            x0, [fp, #-0x40]
    // 0x13b5490: cmp             w0, w1
    // 0x13b5494: b.eq            #0x13b54c8
    // 0x13b5498: ldur            x3, [fp, #-0x10]
    // 0x13b549c: ldur            x0, [fp, #-8]
    // 0x13b54a0: LoadField: r1 = r3->field_f
    //     0x13b54a0: ldur            w1, [x3, #0xf]
    // 0x13b54a4: DecompressPointer r1
    //     0x13b54a4: add             x1, x1, HEAP, lsl #32
    // 0x13b54a8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x13b54a8: ldur            w2, [x1, #0x17]
    // 0x13b54ac: DecompressPointer r2
    //     0x13b54ac: add             x2, x2, HEAP, lsl #32
    // 0x13b54b0: LoadField: r1 = r0->field_7
    //     0x13b54b0: ldur            w1, [x0, #7]
    // 0x13b54b4: DecompressPointer r1
    //     0x13b54b4: add             x1, x1, HEAP, lsl #32
    // 0x13b54b8: mov             x16, x1
    // 0x13b54bc: mov             x1, x2
    // 0x13b54c0: mov             x2, x16
    // 0x13b54c4: r0 = remove()
    //     0x13b54c4: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x13b54c8: ldur            x0, [fp, #-0x20]
    // 0x13b54cc: ldur            x3, [fp, #-0x38]
    // 0x13b54d0: ldur            x4, [fp, #-0x30]
    // 0x13b54d4: ldur            x5, [fp, #-0x28]
    // 0x13b54d8: b               #0x13b5390
    // 0x13b54dc: mov             x2, x6
    // 0x13b54e0: LoadField: r0 = r2->field_f
    //     0x13b54e0: ldur            w0, [x2, #0xf]
    // 0x13b54e4: DecompressPointer r0
    //     0x13b54e4: add             x0, x0, HEAP, lsl #32
    // 0x13b54e8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13b54e8: ldur            w1, [x0, #0x17]
    // 0x13b54ec: DecompressPointer r1
    //     0x13b54ec: add             x1, x1, HEAP, lsl #32
    // 0x13b54f0: LoadField: r3 = r1->field_13
    //     0x13b54f0: ldur            w3, [x1, #0x13]
    // 0x13b54f4: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x13b54f4: ldur            w4, [x1, #0x17]
    // 0x13b54f8: r5 = LoadInt32Instr(r3)
    //     0x13b54f8: sbfx            x5, x3, #1, #0x1f
    // 0x13b54fc: r3 = LoadInt32Instr(r4)
    //     0x13b54fc: sbfx            x3, x4, #1, #0x1f
    // 0x13b5500: sub             x4, x5, x3
    // 0x13b5504: cbnz            x4, #0x13b5520
    // 0x13b5508: LoadField: r1 = r0->field_13
    //     0x13b5508: ldur            w1, [x0, #0x13]
    // 0x13b550c: DecompressPointer r1
    //     0x13b550c: add             x1, x1, HEAP, lsl #32
    // 0x13b5510: cmp             w1, NULL
    // 0x13b5514: b.eq            #0x13b5558
    // 0x13b5518: StoreField: r1->field_7 = rNULL
    //     0x13b5518: stur            NULL, [x1, #7]
    // 0x13b551c: b               #0x13b5558
    // 0x13b5520: LoadField: r3 = r0->field_13
    //     0x13b5520: ldur            w3, [x0, #0x13]
    // 0x13b5524: DecompressPointer r3
    //     0x13b5524: add             x3, x3, HEAP, lsl #32
    // 0x13b5528: cmp             w3, NULL
    // 0x13b552c: b.eq            #0x13b5558
    // 0x13b5530: mov             x0, x1
    // 0x13b5534: StoreField: r3->field_7 = r0
    //     0x13b5534: stur            w0, [x3, #7]
    //     0x13b5538: ldurb           w16, [x3, #-1]
    //     0x13b553c: ldurb           w17, [x0, #-1]
    //     0x13b5540: and             x16, x17, x16, lsr #2
    //     0x13b5544: tst             x16, HEAP, lsr #32
    //     0x13b5548: b.eq            #0x13b5550
    //     0x13b554c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x13b5550: b               #0x13b5558
    // 0x13b5554: ldur            x2, [fp, #-0x10]
    // 0x13b5558: LoadField: r1 = r2->field_f
    //     0x13b5558: ldur            w1, [x2, #0xf]
    // 0x13b555c: DecompressPointer r1
    //     0x13b555c: add             x1, x1, HEAP, lsl #32
    // 0x13b5560: r0 = controller()
    //     0x13b5560: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5564: LoadField: r1 = r0->field_63
    //     0x13b5564: ldur            w1, [x0, #0x63]
    // 0x13b5568: DecompressPointer r1
    //     0x13b5568: add             x1, x1, HEAP, lsl #32
    // 0x13b556c: r0 = value()
    //     0x13b556c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b5570: LoadField: r1 = r0->field_b
    //     0x13b5570: ldur            w1, [x0, #0xb]
    // 0x13b5574: DecompressPointer r1
    //     0x13b5574: add             x1, x1, HEAP, lsl #32
    // 0x13b5578: cmp             w1, NULL
    // 0x13b557c: b.eq            #0x13b56a8
    // 0x13b5580: ldur            x2, [fp, #-0x18]
    // 0x13b5584: ldur            x0, [fp, #-0x10]
    // 0x13b5588: LoadField: r1 = r0->field_f
    //     0x13b5588: ldur            w1, [x0, #0xf]
    // 0x13b558c: DecompressPointer r1
    //     0x13b558c: add             x1, x1, HEAP, lsl #32
    // 0x13b5590: LoadField: r3 = r1->field_27
    //     0x13b5590: ldur            w3, [x1, #0x27]
    // 0x13b5594: DecompressPointer r3
    //     0x13b5594: add             x3, x3, HEAP, lsl #32
    // 0x13b5598: stur            x3, [fp, #-8]
    // 0x13b559c: r0 = controller()
    //     0x13b559c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b55a0: LoadField: r1 = r0->field_63
    //     0x13b55a0: ldur            w1, [x0, #0x63]
    // 0x13b55a4: DecompressPointer r1
    //     0x13b55a4: add             x1, x1, HEAP, lsl #32
    // 0x13b55a8: r0 = value()
    //     0x13b55a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b55ac: LoadField: r2 = r0->field_b
    //     0x13b55ac: ldur            w2, [x0, #0xb]
    // 0x13b55b0: DecompressPointer r2
    //     0x13b55b0: add             x2, x2, HEAP, lsl #32
    // 0x13b55b4: cmp             w2, NULL
    // 0x13b55b8: b.eq            #0x13b5d10
    // 0x13b55bc: ldur            x1, [fp, #-8]
    // 0x13b55c0: r0 = addAll()
    //     0x13b55c0: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x13b55c4: ldur            x0, [fp, #-0x10]
    // 0x13b55c8: LoadField: r1 = r0->field_f
    //     0x13b55c8: ldur            w1, [x0, #0xf]
    // 0x13b55cc: DecompressPointer r1
    //     0x13b55cc: add             x1, x1, HEAP, lsl #32
    // 0x13b55d0: LoadField: r2 = r1->field_27
    //     0x13b55d0: ldur            w2, [x1, #0x27]
    // 0x13b55d4: DecompressPointer r2
    //     0x13b55d4: add             x2, x2, HEAP, lsl #32
    // 0x13b55d8: ldur            x3, [fp, #-0x18]
    // 0x13b55dc: LoadField: r1 = r3->field_f
    //     0x13b55dc: ldur            w1, [x3, #0xf]
    // 0x13b55e0: DecompressPointer r1
    //     0x13b55e0: add             x1, x1, HEAP, lsl #32
    // 0x13b55e4: mov             x16, x1
    // 0x13b55e8: mov             x1, x2
    // 0x13b55ec: mov             x2, x16
    // 0x13b55f0: r0 = contains()
    //     0x13b55f0: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x13b55f4: tbnz            w0, #4, #0x13b5628
    // 0x13b55f8: ldur            x3, [fp, #-0x18]
    // 0x13b55fc: ldur            x0, [fp, #-0x10]
    // 0x13b5600: LoadField: r1 = r0->field_f
    //     0x13b5600: ldur            w1, [x0, #0xf]
    // 0x13b5604: DecompressPointer r1
    //     0x13b5604: add             x1, x1, HEAP, lsl #32
    // 0x13b5608: LoadField: r2 = r1->field_27
    //     0x13b5608: ldur            w2, [x1, #0x27]
    // 0x13b560c: DecompressPointer r2
    //     0x13b560c: add             x2, x2, HEAP, lsl #32
    // 0x13b5610: LoadField: r1 = r3->field_f
    //     0x13b5610: ldur            w1, [x3, #0xf]
    // 0x13b5614: DecompressPointer r1
    //     0x13b5614: add             x1, x1, HEAP, lsl #32
    // 0x13b5618: mov             x16, x1
    // 0x13b561c: mov             x1, x2
    // 0x13b5620: mov             x2, x16
    // 0x13b5624: r0 = remove()
    //     0x13b5624: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x13b5628: ldur            x0, [fp, #-0x10]
    // 0x13b562c: LoadField: r1 = r0->field_f
    //     0x13b562c: ldur            w1, [x0, #0xf]
    // 0x13b5630: DecompressPointer r1
    //     0x13b5630: add             x1, x1, HEAP, lsl #32
    // 0x13b5634: LoadField: r2 = r1->field_27
    //     0x13b5634: ldur            w2, [x1, #0x27]
    // 0x13b5638: DecompressPointer r2
    //     0x13b5638: add             x2, x2, HEAP, lsl #32
    // 0x13b563c: LoadField: r3 = r2->field_13
    //     0x13b563c: ldur            w3, [x2, #0x13]
    // 0x13b5640: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13b5640: ldur            w4, [x2, #0x17]
    // 0x13b5644: r5 = LoadInt32Instr(r3)
    //     0x13b5644: sbfx            x5, x3, #1, #0x1f
    // 0x13b5648: r3 = LoadInt32Instr(r4)
    //     0x13b5648: sbfx            x3, x4, #1, #0x1f
    // 0x13b564c: sub             x4, x5, x3
    // 0x13b5650: cbnz            x4, #0x13b566c
    // 0x13b5654: LoadField: r2 = r1->field_13
    //     0x13b5654: ldur            w2, [x1, #0x13]
    // 0x13b5658: DecompressPointer r2
    //     0x13b5658: add             x2, x2, HEAP, lsl #32
    // 0x13b565c: cmp             w2, NULL
    // 0x13b5660: b.eq            #0x13b56a8
    // 0x13b5664: StoreField: r2->field_f = rNULL
    //     0x13b5664: stur            NULL, [x2, #0xf]
    // 0x13b5668: b               #0x13b56a8
    // 0x13b566c: LoadField: r3 = r1->field_13
    //     0x13b566c: ldur            w3, [x1, #0x13]
    // 0x13b5670: DecompressPointer r3
    //     0x13b5670: add             x3, x3, HEAP, lsl #32
    // 0x13b5674: stur            x3, [fp, #-8]
    // 0x13b5678: cmp             w3, NULL
    // 0x13b567c: b.eq            #0x13b56a8
    // 0x13b5680: mov             x1, x2
    // 0x13b5684: r0 = toSet()
    //     0x13b5684: bl              #0x7d6b1c  ; [dart:_compact_hash] _Set::toSet
    // 0x13b5688: ldur            x1, [fp, #-8]
    // 0x13b568c: StoreField: r1->field_f = r0
    //     0x13b568c: stur            w0, [x1, #0xf]
    //     0x13b5690: ldurb           w16, [x1, #-1]
    //     0x13b5694: ldurb           w17, [x0, #-1]
    //     0x13b5698: and             x16, x17, x16, lsr #2
    //     0x13b569c: tst             x16, HEAP, lsr #32
    //     0x13b56a0: b.eq            #0x13b56a8
    //     0x13b56a4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13b56a8: ldur            x0, [fp, #-0x10]
    // 0x13b56ac: LoadField: r1 = r0->field_f
    //     0x13b56ac: ldur            w1, [x0, #0xf]
    // 0x13b56b0: DecompressPointer r1
    //     0x13b56b0: add             x1, x1, HEAP, lsl #32
    // 0x13b56b4: r0 = controller()
    //     0x13b56b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b56b8: LoadField: r1 = r0->field_63
    //     0x13b56b8: ldur            w1, [x0, #0x63]
    // 0x13b56bc: DecompressPointer r1
    //     0x13b56bc: add             x1, x1, HEAP, lsl #32
    // 0x13b56c0: r0 = value()
    //     0x13b56c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b56c4: LoadField: r1 = r0->field_f
    //     0x13b56c4: ldur            w1, [x0, #0xf]
    // 0x13b56c8: DecompressPointer r1
    //     0x13b56c8: add             x1, x1, HEAP, lsl #32
    // 0x13b56cc: cmp             w1, NULL
    // 0x13b56d0: b.eq            #0x13b57f4
    // 0x13b56d4: ldur            x2, [fp, #-0x18]
    // 0x13b56d8: ldur            x0, [fp, #-0x10]
    // 0x13b56dc: LoadField: r1 = r0->field_f
    //     0x13b56dc: ldur            w1, [x0, #0xf]
    // 0x13b56e0: DecompressPointer r1
    //     0x13b56e0: add             x1, x1, HEAP, lsl #32
    // 0x13b56e4: LoadField: r3 = r1->field_1b
    //     0x13b56e4: ldur            w3, [x1, #0x1b]
    // 0x13b56e8: DecompressPointer r3
    //     0x13b56e8: add             x3, x3, HEAP, lsl #32
    // 0x13b56ec: stur            x3, [fp, #-8]
    // 0x13b56f0: r0 = controller()
    //     0x13b56f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b56f4: LoadField: r1 = r0->field_63
    //     0x13b56f4: ldur            w1, [x0, #0x63]
    // 0x13b56f8: DecompressPointer r1
    //     0x13b56f8: add             x1, x1, HEAP, lsl #32
    // 0x13b56fc: r0 = value()
    //     0x13b56fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b5700: LoadField: r2 = r0->field_f
    //     0x13b5700: ldur            w2, [x0, #0xf]
    // 0x13b5704: DecompressPointer r2
    //     0x13b5704: add             x2, x2, HEAP, lsl #32
    // 0x13b5708: cmp             w2, NULL
    // 0x13b570c: b.eq            #0x13b5d14
    // 0x13b5710: ldur            x1, [fp, #-8]
    // 0x13b5714: r0 = addAll()
    //     0x13b5714: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x13b5718: ldur            x0, [fp, #-0x10]
    // 0x13b571c: LoadField: r1 = r0->field_f
    //     0x13b571c: ldur            w1, [x0, #0xf]
    // 0x13b5720: DecompressPointer r1
    //     0x13b5720: add             x1, x1, HEAP, lsl #32
    // 0x13b5724: LoadField: r2 = r1->field_1b
    //     0x13b5724: ldur            w2, [x1, #0x1b]
    // 0x13b5728: DecompressPointer r2
    //     0x13b5728: add             x2, x2, HEAP, lsl #32
    // 0x13b572c: ldur            x3, [fp, #-0x18]
    // 0x13b5730: LoadField: r1 = r3->field_f
    //     0x13b5730: ldur            w1, [x3, #0xf]
    // 0x13b5734: DecompressPointer r1
    //     0x13b5734: add             x1, x1, HEAP, lsl #32
    // 0x13b5738: mov             x16, x1
    // 0x13b573c: mov             x1, x2
    // 0x13b5740: mov             x2, x16
    // 0x13b5744: r0 = contains()
    //     0x13b5744: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x13b5748: tbnz            w0, #4, #0x13b577c
    // 0x13b574c: ldur            x3, [fp, #-0x18]
    // 0x13b5750: ldur            x0, [fp, #-0x10]
    // 0x13b5754: LoadField: r1 = r0->field_f
    //     0x13b5754: ldur            w1, [x0, #0xf]
    // 0x13b5758: DecompressPointer r1
    //     0x13b5758: add             x1, x1, HEAP, lsl #32
    // 0x13b575c: LoadField: r2 = r1->field_1b
    //     0x13b575c: ldur            w2, [x1, #0x1b]
    // 0x13b5760: DecompressPointer r2
    //     0x13b5760: add             x2, x2, HEAP, lsl #32
    // 0x13b5764: LoadField: r1 = r3->field_f
    //     0x13b5764: ldur            w1, [x3, #0xf]
    // 0x13b5768: DecompressPointer r1
    //     0x13b5768: add             x1, x1, HEAP, lsl #32
    // 0x13b576c: mov             x16, x1
    // 0x13b5770: mov             x1, x2
    // 0x13b5774: mov             x2, x16
    // 0x13b5778: r0 = remove()
    //     0x13b5778: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x13b577c: ldur            x2, [fp, #-0x10]
    // 0x13b5780: LoadField: r0 = r2->field_f
    //     0x13b5780: ldur            w0, [x2, #0xf]
    // 0x13b5784: DecompressPointer r0
    //     0x13b5784: add             x0, x0, HEAP, lsl #32
    // 0x13b5788: LoadField: r1 = r0->field_1b
    //     0x13b5788: ldur            w1, [x0, #0x1b]
    // 0x13b578c: DecompressPointer r1
    //     0x13b578c: add             x1, x1, HEAP, lsl #32
    // 0x13b5790: LoadField: r3 = r1->field_13
    //     0x13b5790: ldur            w3, [x1, #0x13]
    // 0x13b5794: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x13b5794: ldur            w4, [x1, #0x17]
    // 0x13b5798: r5 = LoadInt32Instr(r3)
    //     0x13b5798: sbfx            x5, x3, #1, #0x1f
    // 0x13b579c: r3 = LoadInt32Instr(r4)
    //     0x13b579c: sbfx            x3, x4, #1, #0x1f
    // 0x13b57a0: sub             x4, x5, x3
    // 0x13b57a4: cbnz            x4, #0x13b57c0
    // 0x13b57a8: LoadField: r1 = r0->field_13
    //     0x13b57a8: ldur            w1, [x0, #0x13]
    // 0x13b57ac: DecompressPointer r1
    //     0x13b57ac: add             x1, x1, HEAP, lsl #32
    // 0x13b57b0: cmp             w1, NULL
    // 0x13b57b4: b.eq            #0x13b57f8
    // 0x13b57b8: StoreField: r1->field_b = rNULL
    //     0x13b57b8: stur            NULL, [x1, #0xb]
    // 0x13b57bc: b               #0x13b57f8
    // 0x13b57c0: LoadField: r3 = r0->field_13
    //     0x13b57c0: ldur            w3, [x0, #0x13]
    // 0x13b57c4: DecompressPointer r3
    //     0x13b57c4: add             x3, x3, HEAP, lsl #32
    // 0x13b57c8: cmp             w3, NULL
    // 0x13b57cc: b.eq            #0x13b57f8
    // 0x13b57d0: mov             x0, x1
    // 0x13b57d4: StoreField: r3->field_b = r0
    //     0x13b57d4: stur            w0, [x3, #0xb]
    //     0x13b57d8: ldurb           w16, [x3, #-1]
    //     0x13b57dc: ldurb           w17, [x0, #-1]
    //     0x13b57e0: and             x16, x17, x16, lsr #2
    //     0x13b57e4: tst             x16, HEAP, lsr #32
    //     0x13b57e8: b.eq            #0x13b57f0
    //     0x13b57ec: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x13b57f0: b               #0x13b57f8
    // 0x13b57f4: ldur            x2, [fp, #-0x10]
    // 0x13b57f8: LoadField: r1 = r2->field_f
    //     0x13b57f8: ldur            w1, [x2, #0xf]
    // 0x13b57fc: DecompressPointer r1
    //     0x13b57fc: add             x1, x1, HEAP, lsl #32
    // 0x13b5800: r0 = controller()
    //     0x13b5800: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5804: LoadField: r1 = r0->field_63
    //     0x13b5804: ldur            w1, [x0, #0x63]
    // 0x13b5808: DecompressPointer r1
    //     0x13b5808: add             x1, x1, HEAP, lsl #32
    // 0x13b580c: r0 = value()
    //     0x13b580c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b5810: LoadField: r1 = r0->field_13
    //     0x13b5810: ldur            w1, [x0, #0x13]
    // 0x13b5814: DecompressPointer r1
    //     0x13b5814: add             x1, x1, HEAP, lsl #32
    // 0x13b5818: cmp             w1, NULL
    // 0x13b581c: b.eq            #0x13b5b64
    // 0x13b5820: ldur            x0, [fp, #-0x10]
    // 0x13b5824: LoadField: r1 = r0->field_f
    //     0x13b5824: ldur            w1, [x0, #0xf]
    // 0x13b5828: DecompressPointer r1
    //     0x13b5828: add             x1, x1, HEAP, lsl #32
    // 0x13b582c: LoadField: r2 = r1->field_23
    //     0x13b582c: ldur            w2, [x1, #0x23]
    // 0x13b5830: DecompressPointer r2
    //     0x13b5830: add             x2, x2, HEAP, lsl #32
    // 0x13b5834: stur            x2, [fp, #-8]
    // 0x13b5838: r0 = controller()
    //     0x13b5838: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b583c: LoadField: r1 = r0->field_63
    //     0x13b583c: ldur            w1, [x0, #0x63]
    // 0x13b5840: DecompressPointer r1
    //     0x13b5840: add             x1, x1, HEAP, lsl #32
    // 0x13b5844: r0 = value()
    //     0x13b5844: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b5848: LoadField: r1 = r0->field_13
    //     0x13b5848: ldur            w1, [x0, #0x13]
    // 0x13b584c: DecompressPointer r1
    //     0x13b584c: add             x1, x1, HEAP, lsl #32
    // 0x13b5850: cmp             w1, NULL
    // 0x13b5854: b.ne            #0x13b589c
    // 0x13b5858: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x13b5858: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13b585c: ldr             x0, [x0]
    //     0x13b5860: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13b5864: cmp             w0, w16
    //     0x13b5868: b.ne            #0x13b5874
    //     0x13b586c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x13b5870: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13b5874: r1 = <PriceRangeFilter>
    //     0x13b5874: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb08] TypeArguments: <PriceRangeFilter>
    //     0x13b5878: ldr             x1, [x1, #0xb08]
    // 0x13b587c: stur            x0, [fp, #-0x30]
    // 0x13b5880: r0 = AllocateGrowableArray()
    //     0x13b5880: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13b5884: mov             x1, x0
    // 0x13b5888: ldur            x0, [fp, #-0x30]
    // 0x13b588c: StoreField: r1->field_f = r0
    //     0x13b588c: stur            w0, [x1, #0xf]
    // 0x13b5890: StoreField: r1->field_b = rZR
    //     0x13b5890: stur            wzr, [x1, #0xb]
    // 0x13b5894: mov             x2, x1
    // 0x13b5898: b               #0x13b58a0
    // 0x13b589c: mov             x2, x1
    // 0x13b58a0: ldur            x0, [fp, #-0x18]
    // 0x13b58a4: ldur            x1, [fp, #-8]
    // 0x13b58a8: r0 = addAll()
    //     0x13b58a8: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x13b58ac: ldur            x0, [fp, #-0x18]
    // 0x13b58b0: LoadField: r1 = r0->field_f
    //     0x13b58b0: ldur            w1, [x0, #0xf]
    // 0x13b58b4: DecompressPointer r1
    //     0x13b58b4: add             x1, x1, HEAP, lsl #32
    // 0x13b58b8: r0 = LoadClassIdInstr(r1)
    //     0x13b58b8: ldur            x0, [x1, #-1]
    //     0x13b58bc: ubfx            x0, x0, #0xc, #0x14
    // 0x13b58c0: r2 = " - "
    //     0x13b58c0: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0x13b58c4: ldr             x2, [x2, #0xc08]
    // 0x13b58c8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x13b58c8: sub             lr, x0, #0xffc
    //     0x13b58cc: ldr             lr, [x21, lr, lsl #3]
    //     0x13b58d0: blr             lr
    // 0x13b58d4: mov             x2, x0
    // 0x13b58d8: stur            x2, [fp, #-8]
    // 0x13b58dc: LoadField: r0 = r2->field_b
    //     0x13b58dc: ldur            w0, [x2, #0xb]
    // 0x13b58e0: r1 = LoadInt32Instr(r0)
    //     0x13b58e0: sbfx            x1, x0, #1, #0x1f
    // 0x13b58e4: mov             x0, x1
    // 0x13b58e8: r1 = 1
    //     0x13b58e8: movz            x1, #0x1
    // 0x13b58ec: cmp             x1, x0
    // 0x13b58f0: b.hs            #0x13b5d18
    // 0x13b58f4: LoadField: r0 = r2->field_f
    //     0x13b58f4: ldur            w0, [x2, #0xf]
    // 0x13b58f8: DecompressPointer r0
    //     0x13b58f8: add             x0, x0, HEAP, lsl #32
    // 0x13b58fc: LoadField: r1 = r0->field_13
    //     0x13b58fc: ldur            w1, [x0, #0x13]
    // 0x13b5900: DecompressPointer r1
    //     0x13b5900: add             x1, x1, HEAP, lsl #32
    // 0x13b5904: r0 = LoadClassIdInstr(r1)
    //     0x13b5904: ldur            x0, [x1, #-1]
    //     0x13b5908: ubfx            x0, x0, #0xc, #0x14
    // 0x13b590c: r16 = "above"
    //     0x13b590c: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0x13b5910: ldr             x16, [x16, #0xc10]
    // 0x13b5914: stp             x16, x1, [SP]
    // 0x13b5918: mov             lr, x0
    // 0x13b591c: ldr             lr, [x21, lr, lsl #3]
    // 0x13b5920: blr             lr
    // 0x13b5924: tbnz            w0, #4, #0x13b59b8
    // 0x13b5928: ldur            x3, [fp, #-0x10]
    // 0x13b592c: ldur            x2, [fp, #-8]
    // 0x13b5930: LoadField: r0 = r3->field_f
    //     0x13b5930: ldur            w0, [x3, #0xf]
    // 0x13b5934: DecompressPointer r0
    //     0x13b5934: add             x0, x0, HEAP, lsl #32
    // 0x13b5938: LoadField: r4 = r0->field_1f
    //     0x13b5938: ldur            w4, [x0, #0x1f]
    // 0x13b593c: DecompressPointer r4
    //     0x13b593c: add             x4, x4, HEAP, lsl #32
    // 0x13b5940: stur            x4, [fp, #-0x18]
    // 0x13b5944: LoadField: r0 = r2->field_b
    //     0x13b5944: ldur            w0, [x2, #0xb]
    // 0x13b5948: r1 = LoadInt32Instr(r0)
    //     0x13b5948: sbfx            x1, x0, #1, #0x1f
    // 0x13b594c: mov             x0, x1
    // 0x13b5950: r1 = 0
    //     0x13b5950: movz            x1, #0
    // 0x13b5954: cmp             x1, x0
    // 0x13b5958: b.hs            #0x13b5d1c
    // 0x13b595c: LoadField: r0 = r2->field_f
    //     0x13b595c: ldur            w0, [x2, #0xf]
    // 0x13b5960: DecompressPointer r0
    //     0x13b5960: add             x0, x0, HEAP, lsl #32
    // 0x13b5964: LoadField: r1 = r0->field_f
    //     0x13b5964: ldur            w1, [x0, #0xf]
    // 0x13b5968: DecompressPointer r1
    //     0x13b5968: add             x1, x1, HEAP, lsl #32
    // 0x13b596c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13b596c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13b5970: r0 = tryParse()
    //     0x13b5970: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x13b5974: ldur            x1, [fp, #-0x18]
    // 0x13b5978: StoreField: r1->field_7 = r0
    //     0x13b5978: stur            w0, [x1, #7]
    //     0x13b597c: tbz             w0, #0, #0x13b5998
    //     0x13b5980: ldurb           w16, [x1, #-1]
    //     0x13b5984: ldurb           w17, [x0, #-1]
    //     0x13b5988: and             x16, x17, x16, lsr #2
    //     0x13b598c: tst             x16, HEAP, lsr #32
    //     0x13b5990: b.eq            #0x13b5998
    //     0x13b5994: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13b5998: ldur            x3, [fp, #-0x10]
    // 0x13b599c: LoadField: r0 = r3->field_f
    //     0x13b599c: ldur            w0, [x3, #0xf]
    // 0x13b59a0: DecompressPointer r0
    //     0x13b59a0: add             x0, x0, HEAP, lsl #32
    // 0x13b59a4: LoadField: r1 = r0->field_1f
    //     0x13b59a4: ldur            w1, [x0, #0x1f]
    // 0x13b59a8: DecompressPointer r1
    //     0x13b59a8: add             x1, x1, HEAP, lsl #32
    // 0x13b59ac: StoreField: r1->field_b = rNULL
    //     0x13b59ac: stur            NULL, [x1, #0xb]
    // 0x13b59b0: mov             x0, x3
    // 0x13b59b4: b               #0x13b5a9c
    // 0x13b59b8: ldur            x3, [fp, #-0x10]
    // 0x13b59bc: ldur            x2, [fp, #-8]
    // 0x13b59c0: LoadField: r0 = r3->field_f
    //     0x13b59c0: ldur            w0, [x3, #0xf]
    // 0x13b59c4: DecompressPointer r0
    //     0x13b59c4: add             x0, x0, HEAP, lsl #32
    // 0x13b59c8: LoadField: r4 = r0->field_1f
    //     0x13b59c8: ldur            w4, [x0, #0x1f]
    // 0x13b59cc: DecompressPointer r4
    //     0x13b59cc: add             x4, x4, HEAP, lsl #32
    // 0x13b59d0: stur            x4, [fp, #-0x18]
    // 0x13b59d4: LoadField: r0 = r2->field_b
    //     0x13b59d4: ldur            w0, [x2, #0xb]
    // 0x13b59d8: r1 = LoadInt32Instr(r0)
    //     0x13b59d8: sbfx            x1, x0, #1, #0x1f
    // 0x13b59dc: mov             x0, x1
    // 0x13b59e0: r1 = 0
    //     0x13b59e0: movz            x1, #0
    // 0x13b59e4: cmp             x1, x0
    // 0x13b59e8: b.hs            #0x13b5d20
    // 0x13b59ec: LoadField: r0 = r2->field_f
    //     0x13b59ec: ldur            w0, [x2, #0xf]
    // 0x13b59f0: DecompressPointer r0
    //     0x13b59f0: add             x0, x0, HEAP, lsl #32
    // 0x13b59f4: LoadField: r1 = r0->field_f
    //     0x13b59f4: ldur            w1, [x0, #0xf]
    // 0x13b59f8: DecompressPointer r1
    //     0x13b59f8: add             x1, x1, HEAP, lsl #32
    // 0x13b59fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13b59fc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13b5a00: r0 = tryParse()
    //     0x13b5a00: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x13b5a04: ldur            x1, [fp, #-0x18]
    // 0x13b5a08: StoreField: r1->field_7 = r0
    //     0x13b5a08: stur            w0, [x1, #7]
    //     0x13b5a0c: tbz             w0, #0, #0x13b5a28
    //     0x13b5a10: ldurb           w16, [x1, #-1]
    //     0x13b5a14: ldurb           w17, [x0, #-1]
    //     0x13b5a18: and             x16, x17, x16, lsr #2
    //     0x13b5a1c: tst             x16, HEAP, lsr #32
    //     0x13b5a20: b.eq            #0x13b5a28
    //     0x13b5a24: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13b5a28: ldur            x2, [fp, #-0x10]
    // 0x13b5a2c: LoadField: r0 = r2->field_f
    //     0x13b5a2c: ldur            w0, [x2, #0xf]
    // 0x13b5a30: DecompressPointer r0
    //     0x13b5a30: add             x0, x0, HEAP, lsl #32
    // 0x13b5a34: LoadField: r3 = r0->field_1f
    //     0x13b5a34: ldur            w3, [x0, #0x1f]
    // 0x13b5a38: DecompressPointer r3
    //     0x13b5a38: add             x3, x3, HEAP, lsl #32
    // 0x13b5a3c: ldur            x4, [fp, #-8]
    // 0x13b5a40: stur            x3, [fp, #-0x18]
    // 0x13b5a44: LoadField: r0 = r4->field_b
    //     0x13b5a44: ldur            w0, [x4, #0xb]
    // 0x13b5a48: r1 = LoadInt32Instr(r0)
    //     0x13b5a48: sbfx            x1, x0, #1, #0x1f
    // 0x13b5a4c: mov             x0, x1
    // 0x13b5a50: r1 = 1
    //     0x13b5a50: movz            x1, #0x1
    // 0x13b5a54: cmp             x1, x0
    // 0x13b5a58: b.hs            #0x13b5d24
    // 0x13b5a5c: LoadField: r0 = r4->field_f
    //     0x13b5a5c: ldur            w0, [x4, #0xf]
    // 0x13b5a60: DecompressPointer r0
    //     0x13b5a60: add             x0, x0, HEAP, lsl #32
    // 0x13b5a64: LoadField: r1 = r0->field_13
    //     0x13b5a64: ldur            w1, [x0, #0x13]
    // 0x13b5a68: DecompressPointer r1
    //     0x13b5a68: add             x1, x1, HEAP, lsl #32
    // 0x13b5a6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13b5a6c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13b5a70: r0 = tryParse()
    //     0x13b5a70: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x13b5a74: ldur            x1, [fp, #-0x18]
    // 0x13b5a78: StoreField: r1->field_b = r0
    //     0x13b5a78: stur            w0, [x1, #0xb]
    //     0x13b5a7c: tbz             w0, #0, #0x13b5a98
    //     0x13b5a80: ldurb           w16, [x1, #-1]
    //     0x13b5a84: ldurb           w17, [x0, #-1]
    //     0x13b5a88: and             x16, x17, x16, lsr #2
    //     0x13b5a8c: tst             x16, HEAP, lsr #32
    //     0x13b5a90: b.eq            #0x13b5a98
    //     0x13b5a94: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13b5a98: ldur            x0, [fp, #-0x10]
    // 0x13b5a9c: LoadField: r1 = r0->field_f
    //     0x13b5a9c: ldur            w1, [x0, #0xf]
    // 0x13b5aa0: DecompressPointer r1
    //     0x13b5aa0: add             x1, x1, HEAP, lsl #32
    // 0x13b5aa4: LoadField: r2 = r1->field_23
    //     0x13b5aa4: ldur            w2, [x1, #0x23]
    // 0x13b5aa8: DecompressPointer r2
    //     0x13b5aa8: add             x2, x2, HEAP, lsl #32
    // 0x13b5aac: LoadField: r3 = r1->field_1f
    //     0x13b5aac: ldur            w3, [x1, #0x1f]
    // 0x13b5ab0: DecompressPointer r3
    //     0x13b5ab0: add             x3, x3, HEAP, lsl #32
    // 0x13b5ab4: mov             x1, x2
    // 0x13b5ab8: mov             x2, x3
    // 0x13b5abc: r0 = contains()
    //     0x13b5abc: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x13b5ac0: tbnz            w0, #4, #0x13b5aec
    // 0x13b5ac4: ldur            x0, [fp, #-0x10]
    // 0x13b5ac8: LoadField: r1 = r0->field_f
    //     0x13b5ac8: ldur            w1, [x0, #0xf]
    // 0x13b5acc: DecompressPointer r1
    //     0x13b5acc: add             x1, x1, HEAP, lsl #32
    // 0x13b5ad0: LoadField: r2 = r1->field_23
    //     0x13b5ad0: ldur            w2, [x1, #0x23]
    // 0x13b5ad4: DecompressPointer r2
    //     0x13b5ad4: add             x2, x2, HEAP, lsl #32
    // 0x13b5ad8: LoadField: r3 = r1->field_1f
    //     0x13b5ad8: ldur            w3, [x1, #0x1f]
    // 0x13b5adc: DecompressPointer r3
    //     0x13b5adc: add             x3, x3, HEAP, lsl #32
    // 0x13b5ae0: mov             x1, x2
    // 0x13b5ae4: mov             x2, x3
    // 0x13b5ae8: r0 = remove()
    //     0x13b5ae8: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x13b5aec: ldur            x2, [fp, #-0x10]
    // 0x13b5af0: LoadField: r0 = r2->field_f
    //     0x13b5af0: ldur            w0, [x2, #0xf]
    // 0x13b5af4: DecompressPointer r0
    //     0x13b5af4: add             x0, x0, HEAP, lsl #32
    // 0x13b5af8: LoadField: r1 = r0->field_23
    //     0x13b5af8: ldur            w1, [x0, #0x23]
    // 0x13b5afc: DecompressPointer r1
    //     0x13b5afc: add             x1, x1, HEAP, lsl #32
    // 0x13b5b00: LoadField: r3 = r1->field_13
    //     0x13b5b00: ldur            w3, [x1, #0x13]
    // 0x13b5b04: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x13b5b04: ldur            w4, [x1, #0x17]
    // 0x13b5b08: r5 = LoadInt32Instr(r3)
    //     0x13b5b08: sbfx            x5, x3, #1, #0x1f
    // 0x13b5b0c: r3 = LoadInt32Instr(r4)
    //     0x13b5b0c: sbfx            x3, x4, #1, #0x1f
    // 0x13b5b10: sub             x4, x5, x3
    // 0x13b5b14: cbnz            x4, #0x13b5b30
    // 0x13b5b18: LoadField: r1 = r0->field_13
    //     0x13b5b18: ldur            w1, [x0, #0x13]
    // 0x13b5b1c: DecompressPointer r1
    //     0x13b5b1c: add             x1, x1, HEAP, lsl #32
    // 0x13b5b20: cmp             w1, NULL
    // 0x13b5b24: b.eq            #0x13b5b68
    // 0x13b5b28: StoreField: r1->field_13 = rNULL
    //     0x13b5b28: stur            NULL, [x1, #0x13]
    // 0x13b5b2c: b               #0x13b5b68
    // 0x13b5b30: LoadField: r3 = r0->field_13
    //     0x13b5b30: ldur            w3, [x0, #0x13]
    // 0x13b5b34: DecompressPointer r3
    //     0x13b5b34: add             x3, x3, HEAP, lsl #32
    // 0x13b5b38: cmp             w3, NULL
    // 0x13b5b3c: b.eq            #0x13b5b68
    // 0x13b5b40: mov             x0, x1
    // 0x13b5b44: StoreField: r3->field_13 = r0
    //     0x13b5b44: stur            w0, [x3, #0x13]
    //     0x13b5b48: ldurb           w16, [x3, #-1]
    //     0x13b5b4c: ldurb           w17, [x0, #-1]
    //     0x13b5b50: and             x16, x17, x16, lsr #2
    //     0x13b5b54: tst             x16, HEAP, lsr #32
    //     0x13b5b58: b.eq            #0x13b5b60
    //     0x13b5b5c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x13b5b60: b               #0x13b5b68
    // 0x13b5b64: ldur            x2, [fp, #-0x10]
    // 0x13b5b68: LoadField: r1 = r2->field_f
    //     0x13b5b68: ldur            w1, [x2, #0xf]
    // 0x13b5b6c: DecompressPointer r1
    //     0x13b5b6c: add             x1, x1, HEAP, lsl #32
    // 0x13b5b70: LoadField: r0 = r1->field_13
    //     0x13b5b70: ldur            w0, [x1, #0x13]
    // 0x13b5b74: DecompressPointer r0
    //     0x13b5b74: add             x0, x0, HEAP, lsl #32
    // 0x13b5b78: cmp             w0, NULL
    // 0x13b5b7c: b.eq            #0x13b5b94
    // 0x13b5b80: LoadField: r3 = r0->field_13
    //     0x13b5b80: ldur            w3, [x0, #0x13]
    // 0x13b5b84: DecompressPointer r3
    //     0x13b5b84: add             x3, x3, HEAP, lsl #32
    // 0x13b5b88: cmp             w3, NULL
    // 0x13b5b8c: b.ne            #0x13b5b94
    // 0x13b5b90: StoreField: r0->field_13 = rNULL
    //     0x13b5b90: stur            NULL, [x0, #0x13]
    // 0x13b5b94: cmp             w0, NULL
    // 0x13b5b98: b.eq            #0x13b5bb0
    // 0x13b5b9c: LoadField: r3 = r0->field_f
    //     0x13b5b9c: ldur            w3, [x0, #0xf]
    // 0x13b5ba0: DecompressPointer r3
    //     0x13b5ba0: add             x3, x3, HEAP, lsl #32
    // 0x13b5ba4: cmp             w3, NULL
    // 0x13b5ba8: b.ne            #0x13b5bb0
    // 0x13b5bac: StoreField: r0->field_f = rNULL
    //     0x13b5bac: stur            NULL, [x0, #0xf]
    // 0x13b5bb0: cmp             w0, NULL
    // 0x13b5bb4: b.eq            #0x13b5bcc
    // 0x13b5bb8: LoadField: r3 = r0->field_b
    //     0x13b5bb8: ldur            w3, [x0, #0xb]
    // 0x13b5bbc: DecompressPointer r3
    //     0x13b5bbc: add             x3, x3, HEAP, lsl #32
    // 0x13b5bc0: cmp             w3, NULL
    // 0x13b5bc4: b.ne            #0x13b5bcc
    // 0x13b5bc8: StoreField: r0->field_b = rNULL
    //     0x13b5bc8: stur            NULL, [x0, #0xb]
    // 0x13b5bcc: cmp             w0, NULL
    // 0x13b5bd0: b.eq            #0x13b5be8
    // 0x13b5bd4: LoadField: r3 = r0->field_7
    //     0x13b5bd4: ldur            w3, [x0, #7]
    // 0x13b5bd8: DecompressPointer r3
    //     0x13b5bd8: add             x3, x3, HEAP, lsl #32
    // 0x13b5bdc: cmp             w3, NULL
    // 0x13b5be0: b.ne            #0x13b5be8
    // 0x13b5be4: StoreField: r0->field_7 = rNULL
    //     0x13b5be4: stur            NULL, [x0, #7]
    // 0x13b5be8: cmp             w0, NULL
    // 0x13b5bec: b.eq            #0x13b5c00
    // 0x13b5bf0: LoadField: r3 = r0->field_13
    //     0x13b5bf0: ldur            w3, [x0, #0x13]
    // 0x13b5bf4: DecompressPointer r3
    //     0x13b5bf4: add             x3, x3, HEAP, lsl #32
    // 0x13b5bf8: cmp             w3, NULL
    // 0x13b5bfc: b.ne            #0x13b5c50
    // 0x13b5c00: cmp             w0, NULL
    // 0x13b5c04: b.eq            #0x13b5c18
    // 0x13b5c08: LoadField: r3 = r0->field_f
    //     0x13b5c08: ldur            w3, [x0, #0xf]
    // 0x13b5c0c: DecompressPointer r3
    //     0x13b5c0c: add             x3, x3, HEAP, lsl #32
    // 0x13b5c10: cmp             w3, NULL
    // 0x13b5c14: b.ne            #0x13b5c50
    // 0x13b5c18: cmp             w0, NULL
    // 0x13b5c1c: b.eq            #0x13b5c30
    // 0x13b5c20: LoadField: r3 = r0->field_b
    //     0x13b5c20: ldur            w3, [x0, #0xb]
    // 0x13b5c24: DecompressPointer r3
    //     0x13b5c24: add             x3, x3, HEAP, lsl #32
    // 0x13b5c28: cmp             w3, NULL
    // 0x13b5c2c: b.ne            #0x13b5c50
    // 0x13b5c30: cmp             w0, NULL
    // 0x13b5c34: b.eq            #0x13b5c48
    // 0x13b5c38: LoadField: r3 = r0->field_7
    //     0x13b5c38: ldur            w3, [x0, #7]
    // 0x13b5c3c: DecompressPointer r3
    //     0x13b5c3c: add             x3, x3, HEAP, lsl #32
    // 0x13b5c40: cmp             w3, NULL
    // 0x13b5c44: b.ne            #0x13b5c50
    // 0x13b5c48: StoreField: r1->field_13 = rNULL
    //     0x13b5c48: stur            NULL, [x1, #0x13]
    // 0x13b5c4c: r0 = Null
    //     0x13b5c4c: mov             x0, NULL
    // 0x13b5c50: cmp             w0, NULL
    // 0x13b5c54: b.eq            #0x13b5cc0
    // 0x13b5c58: r0 = controller()
    //     0x13b5c58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5c5c: LoadField: r1 = r0->field_57
    //     0x13b5c5c: ldur            w1, [x0, #0x57]
    // 0x13b5c60: DecompressPointer r1
    //     0x13b5c60: add             x1, x1, HEAP, lsl #32
    // 0x13b5c64: r0 = initRefresh()
    //     0x13b5c64: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x13b5c68: ldur            x0, [fp, #-0x10]
    // 0x13b5c6c: LoadField: r1 = r0->field_f
    //     0x13b5c6c: ldur            w1, [x0, #0xf]
    // 0x13b5c70: DecompressPointer r1
    //     0x13b5c70: add             x1, x1, HEAP, lsl #32
    // 0x13b5c74: r0 = controller()
    //     0x13b5c74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5c78: mov             x2, x0
    // 0x13b5c7c: ldur            x0, [fp, #-0x10]
    // 0x13b5c80: stur            x2, [fp, #-0x18]
    // 0x13b5c84: LoadField: r1 = r0->field_f
    //     0x13b5c84: ldur            w1, [x0, #0xf]
    // 0x13b5c88: DecompressPointer r1
    //     0x13b5c88: add             x1, x1, HEAP, lsl #32
    // 0x13b5c8c: LoadField: r0 = r1->field_13
    //     0x13b5c8c: ldur            w0, [x1, #0x13]
    // 0x13b5c90: DecompressPointer r0
    //     0x13b5c90: add             x0, x0, HEAP, lsl #32
    // 0x13b5c94: stur            x0, [fp, #-8]
    // 0x13b5c98: r0 = controller()
    //     0x13b5c98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5c9c: LoadField: r1 = r0->field_87
    //     0x13b5c9c: ldur            w1, [x0, #0x87]
    // 0x13b5ca0: DecompressPointer r1
    //     0x13b5ca0: add             x1, x1, HEAP, lsl #32
    // 0x13b5ca4: str             x1, [SP]
    // 0x13b5ca8: ldur            x1, [fp, #-0x18]
    // 0x13b5cac: ldur            x2, [fp, #-8]
    // 0x13b5cb0: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0x13b5cb0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0x13b5cb4: ldr             x4, [x4, #0x220]
    // 0x13b5cb8: r0 = getFilterResponse()
    //     0x13b5cb8: bl              #0x13b9e7c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getFilterResponse
    // 0x13b5cbc: b               #0x13b5ccc
    // 0x13b5cc0: r0 = controller()
    //     0x13b5cc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b5cc4: mov             x1, x0
    // 0x13b5cc8: r0 = onRefreshPage()
    //     0x13b5cc8: bl              #0x13b5d28  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::onRefreshPage
    // 0x13b5ccc: r0 = Null
    //     0x13b5ccc: mov             x0, NULL
    // 0x13b5cd0: LeaveFrame
    //     0x13b5cd0: mov             SP, fp
    //     0x13b5cd4: ldp             fp, lr, [SP], #0x10
    // 0x13b5cd8: ret
    //     0x13b5cd8: ret             
    // 0x13b5cdc: mov             x0, x3
    // 0x13b5ce0: r0 = ConcurrentModificationError()
    //     0x13b5ce0: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x13b5ce4: mov             x1, x0
    // 0x13b5ce8: ldur            x0, [fp, #-0x38]
    // 0x13b5cec: StoreField: r1->field_b = r0
    //     0x13b5cec: stur            w0, [x1, #0xb]
    // 0x13b5cf0: mov             x0, x1
    // 0x13b5cf4: r0 = Throw()
    //     0x13b5cf4: bl              #0x16f5420  ; ThrowStub
    // 0x13b5cf8: brk             #0
    // 0x13b5cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b5cfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b5d00: b               #0x13b51f0
    // 0x13b5d04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13b5d04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x13b5d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b5d08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b5d0c: b               #0x13b53a4
    // 0x13b5d10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13b5d10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x13b5d14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13b5d14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x13b5d18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13b5d18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13b5d1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13b5d1c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13b5d20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13b5d20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13b5d24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13b5d24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Filter?) {
    // ** addr: 0x13ba044, size: 0xfc
    // 0x13ba044: EnterFrame
    //     0x13ba044: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba048: mov             fp, SP
    // 0x13ba04c: AllocStack(0x18)
    //     0x13ba04c: sub             SP, SP, #0x18
    // 0x13ba050: SetupParameters()
    //     0x13ba050: ldr             x0, [fp, #0x18]
    //     0x13ba054: ldur            w2, [x0, #0x17]
    //     0x13ba058: add             x2, x2, HEAP, lsl #32
    //     0x13ba05c: stur            x2, [fp, #-8]
    // 0x13ba060: CheckStackOverflow
    //     0x13ba060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba064: cmp             SP, x16
    //     0x13ba068: b.ls            #0x13ba138
    // 0x13ba06c: LoadField: r1 = r2->field_f
    //     0x13ba06c: ldur            w1, [x2, #0xf]
    // 0x13ba070: DecompressPointer r1
    //     0x13ba070: add             x1, x1, HEAP, lsl #32
    // 0x13ba074: r0 = controller()
    //     0x13ba074: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba078: LoadField: r3 = r0->field_77
    //     0x13ba078: ldur            w3, [x0, #0x77]
    // 0x13ba07c: DecompressPointer r3
    //     0x13ba07c: add             x3, x3, HEAP, lsl #32
    // 0x13ba080: ldr             x0, [fp, #0x10]
    // 0x13ba084: stur            x3, [fp, #-0x10]
    // 0x13ba088: r2 = Null
    //     0x13ba088: mov             x2, NULL
    // 0x13ba08c: r1 = Null
    //     0x13ba08c: mov             x1, NULL
    // 0x13ba090: r4 = LoadClassIdInstr(r0)
    //     0x13ba090: ldur            x4, [x0, #-1]
    //     0x13ba094: ubfx            x4, x4, #0xc, #0x14
    // 0x13ba098: r17 = 5367
    //     0x13ba098: movz            x17, #0x14f7
    // 0x13ba09c: cmp             x4, x17
    // 0x13ba0a0: b.eq            #0x13ba0b8
    // 0x13ba0a4: r8 = Filter
    //     0x13ba0a4: add             x8, PP, #0x3b, lsl #12  ; [pp+0x3bc18] Type: Filter
    //     0x13ba0a8: ldr             x8, [x8, #0xc18]
    // 0x13ba0ac: r3 = Null
    //     0x13ba0ac: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c2d8] Null
    //     0x13ba0b0: ldr             x3, [x3, #0x2d8]
    // 0x13ba0b4: r0 = Filter()
    //     0x13ba0b4: bl              #0x13b74c8  ; IsType_Filter_Stub
    // 0x13ba0b8: ldur            x1, [fp, #-0x10]
    // 0x13ba0bc: ldr             x2, [fp, #0x10]
    // 0x13ba0c0: r0 = value=()
    //     0x13ba0c0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13ba0c4: ldur            x0, [fp, #-8]
    // 0x13ba0c8: LoadField: r1 = r0->field_f
    //     0x13ba0c8: ldur            w1, [x0, #0xf]
    // 0x13ba0cc: DecompressPointer r1
    //     0x13ba0cc: add             x1, x1, HEAP, lsl #32
    // 0x13ba0d0: r0 = controller()
    //     0x13ba0d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba0d4: LoadField: r1 = r0->field_57
    //     0x13ba0d4: ldur            w1, [x0, #0x57]
    // 0x13ba0d8: DecompressPointer r1
    //     0x13ba0d8: add             x1, x1, HEAP, lsl #32
    // 0x13ba0dc: r0 = initRefresh()
    //     0x13ba0dc: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x13ba0e0: ldur            x0, [fp, #-8]
    // 0x13ba0e4: LoadField: r1 = r0->field_f
    //     0x13ba0e4: ldur            w1, [x0, #0xf]
    // 0x13ba0e8: DecompressPointer r1
    //     0x13ba0e8: add             x1, x1, HEAP, lsl #32
    // 0x13ba0ec: r0 = controller()
    //     0x13ba0ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba0f0: mov             x1, x0
    // 0x13ba0f4: ldur            x0, [fp, #-8]
    // 0x13ba0f8: LoadField: r2 = r0->field_f
    //     0x13ba0f8: ldur            w2, [x0, #0xf]
    // 0x13ba0fc: DecompressPointer r2
    //     0x13ba0fc: add             x2, x2, HEAP, lsl #32
    // 0x13ba100: LoadField: r0 = r2->field_13
    //     0x13ba100: ldur            w0, [x2, #0x13]
    // 0x13ba104: DecompressPointer r0
    //     0x13ba104: add             x0, x0, HEAP, lsl #32
    // 0x13ba108: ldr             x2, [fp, #0x10]
    // 0x13ba10c: LoadField: r3 = r2->field_7
    //     0x13ba10c: ldur            w3, [x2, #7]
    // 0x13ba110: DecompressPointer r3
    //     0x13ba110: add             x3, x3, HEAP, lsl #32
    // 0x13ba114: str             x3, [SP]
    // 0x13ba118: mov             x2, x0
    // 0x13ba11c: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0x13ba11c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0x13ba120: ldr             x4, [x4, #0x220]
    // 0x13ba124: r0 = getFilterResponse()
    //     0x13ba124: bl              #0x13b9e7c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getFilterResponse
    // 0x13ba128: r0 = Null
    //     0x13ba128: mov             x0, NULL
    // 0x13ba12c: LeaveFrame
    //     0x13ba12c: mov             SP, fp
    //     0x13ba130: ldp             fp, lr, [SP], #0x10
    // 0x13ba134: ret
    //     0x13ba134: ret             
    // 0x13ba138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba138: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba13c: b               #0x13ba06c
  }
  [closure] DropdownMenuItem<Filter> <anonymous closure>(dynamic, Filter) {
    // ** addr: 0x13ba140, size: 0x170
    // 0x13ba140: EnterFrame
    //     0x13ba140: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba144: mov             fp, SP
    // 0x13ba148: AllocStack(0x28)
    //     0x13ba148: sub             SP, SP, #0x28
    // 0x13ba14c: SetupParameters()
    //     0x13ba14c: ldr             x0, [fp, #0x18]
    //     0x13ba150: ldur            w1, [x0, #0x17]
    //     0x13ba154: add             x1, x1, HEAP, lsl #32
    // 0x13ba158: CheckStackOverflow
    //     0x13ba158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba15c: cmp             SP, x16
    //     0x13ba160: b.ls            #0x13ba2a8
    // 0x13ba164: ldr             x0, [fp, #0x10]
    // 0x13ba168: LoadField: r2 = r0->field_b
    //     0x13ba168: ldur            w2, [x0, #0xb]
    // 0x13ba16c: DecompressPointer r2
    //     0x13ba16c: add             x2, x2, HEAP, lsl #32
    // 0x13ba170: cmp             w2, NULL
    // 0x13ba174: b.ne            #0x13ba17c
    // 0x13ba178: r2 = ""
    //     0x13ba178: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13ba17c: stur            x2, [fp, #-8]
    // 0x13ba180: LoadField: r3 = r1->field_13
    //     0x13ba180: ldur            w3, [x1, #0x13]
    // 0x13ba184: DecompressPointer r3
    //     0x13ba184: add             x3, x3, HEAP, lsl #32
    // 0x13ba188: mov             x1, x3
    // 0x13ba18c: r0 = of()
    //     0x13ba18c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ba190: LoadField: r1 = r0->field_87
    //     0x13ba190: ldur            w1, [x0, #0x87]
    // 0x13ba194: DecompressPointer r1
    //     0x13ba194: add             x1, x1, HEAP, lsl #32
    // 0x13ba198: LoadField: r0 = r1->field_7
    //     0x13ba198: ldur            w0, [x1, #7]
    // 0x13ba19c: DecompressPointer r0
    //     0x13ba19c: add             x0, x0, HEAP, lsl #32
    // 0x13ba1a0: stur            x0, [fp, #-0x10]
    // 0x13ba1a4: r1 = Instance_Color
    //     0x13ba1a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ba1a8: d0 = 0.700000
    //     0x13ba1a8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13ba1ac: ldr             d0, [x17, #0xf48]
    // 0x13ba1b0: r0 = withOpacity()
    //     0x13ba1b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13ba1b4: r16 = 12.000000
    //     0x13ba1b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13ba1b8: ldr             x16, [x16, #0x9e8]
    // 0x13ba1bc: stp             x16, x0, [SP]
    // 0x13ba1c0: ldur            x1, [fp, #-0x10]
    // 0x13ba1c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13ba1c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13ba1c8: ldr             x4, [x4, #0x9b8]
    // 0x13ba1cc: r0 = copyWith()
    //     0x13ba1cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ba1d0: stur            x0, [fp, #-0x10]
    // 0x13ba1d4: r0 = TextSpan()
    //     0x13ba1d4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13ba1d8: mov             x3, x0
    // 0x13ba1dc: ldur            x0, [fp, #-8]
    // 0x13ba1e0: stur            x3, [fp, #-0x18]
    // 0x13ba1e4: StoreField: r3->field_b = r0
    //     0x13ba1e4: stur            w0, [x3, #0xb]
    // 0x13ba1e8: r0 = Instance__DeferringMouseCursor
    //     0x13ba1e8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13ba1ec: ArrayStore: r3[0] = r0  ; List_4
    //     0x13ba1ec: stur            w0, [x3, #0x17]
    // 0x13ba1f0: ldur            x1, [fp, #-0x10]
    // 0x13ba1f4: StoreField: r3->field_7 = r1
    //     0x13ba1f4: stur            w1, [x3, #7]
    // 0x13ba1f8: r1 = Null
    //     0x13ba1f8: mov             x1, NULL
    // 0x13ba1fc: r2 = 2
    //     0x13ba1fc: movz            x2, #0x2
    // 0x13ba200: r0 = AllocateArray()
    //     0x13ba200: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ba204: mov             x2, x0
    // 0x13ba208: ldur            x0, [fp, #-0x18]
    // 0x13ba20c: stur            x2, [fp, #-8]
    // 0x13ba210: StoreField: r2->field_f = r0
    //     0x13ba210: stur            w0, [x2, #0xf]
    // 0x13ba214: r1 = <InlineSpan>
    //     0x13ba214: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x13ba218: ldr             x1, [x1, #0xe40]
    // 0x13ba21c: r0 = AllocateGrowableArray()
    //     0x13ba21c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ba220: mov             x1, x0
    // 0x13ba224: ldur            x0, [fp, #-8]
    // 0x13ba228: stur            x1, [fp, #-0x10]
    // 0x13ba22c: StoreField: r1->field_f = r0
    //     0x13ba22c: stur            w0, [x1, #0xf]
    // 0x13ba230: r0 = 2
    //     0x13ba230: movz            x0, #0x2
    // 0x13ba234: StoreField: r1->field_b = r0
    //     0x13ba234: stur            w0, [x1, #0xb]
    // 0x13ba238: r0 = TextSpan()
    //     0x13ba238: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13ba23c: mov             x1, x0
    // 0x13ba240: ldur            x0, [fp, #-0x10]
    // 0x13ba244: stur            x1, [fp, #-8]
    // 0x13ba248: StoreField: r1->field_f = r0
    //     0x13ba248: stur            w0, [x1, #0xf]
    // 0x13ba24c: r0 = Instance__DeferringMouseCursor
    //     0x13ba24c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13ba250: ArrayStore: r1[0] = r0  ; List_4
    //     0x13ba250: stur            w0, [x1, #0x17]
    // 0x13ba254: r0 = RichText()
    //     0x13ba254: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x13ba258: mov             x1, x0
    // 0x13ba25c: ldur            x2, [fp, #-8]
    // 0x13ba260: stur            x0, [fp, #-8]
    // 0x13ba264: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13ba264: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13ba268: r0 = RichText()
    //     0x13ba268: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x13ba26c: r1 = <Filter>
    //     0x13ba26c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bd10] TypeArguments: <Filter>
    //     0x13ba270: ldr             x1, [x1, #0xd10]
    // 0x13ba274: r0 = DropdownMenuItem()
    //     0x13ba274: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0x13ba278: ldr             x1, [fp, #0x10]
    // 0x13ba27c: StoreField: r0->field_1b = r1
    //     0x13ba27c: stur            w1, [x0, #0x1b]
    // 0x13ba280: r1 = true
    //     0x13ba280: add             x1, NULL, #0x20  ; true
    // 0x13ba284: StoreField: r0->field_1f = r1
    //     0x13ba284: stur            w1, [x0, #0x1f]
    // 0x13ba288: r1 = Instance_AlignmentDirectional
    //     0x13ba288: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0x13ba28c: ldr             x1, [x1, #0xb70]
    // 0x13ba290: StoreField: r0->field_f = r1
    //     0x13ba290: stur            w1, [x0, #0xf]
    // 0x13ba294: ldur            x1, [fp, #-8]
    // 0x13ba298: StoreField: r0->field_b = r1
    //     0x13ba298: stur            w1, [x0, #0xb]
    // 0x13ba29c: LeaveFrame
    //     0x13ba29c: mov             SP, fp
    //     0x13ba2a0: ldp             fp, lr, [SP], #0x10
    // 0x13ba2a4: ret
    //     0x13ba2a4: ret             
    // 0x13ba2a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba2a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba2ac: b               #0x13ba164
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x13ba2b0, size: 0x1dc
    // 0x13ba2b0: EnterFrame
    //     0x13ba2b0: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba2b4: mov             fp, SP
    // 0x13ba2b8: AllocStack(0x38)
    //     0x13ba2b8: sub             SP, SP, #0x38
    // 0x13ba2bc: SetupParameters(CollectionPage this /* r1 */)
    //     0x13ba2bc: stur            NULL, [fp, #-8]
    //     0x13ba2c0: movz            x0, #0
    //     0x13ba2c4: add             x1, fp, w0, sxtw #2
    //     0x13ba2c8: ldr             x1, [x1, #0x10]
    //     0x13ba2cc: ldur            w2, [x1, #0x17]
    //     0x13ba2d0: add             x2, x2, HEAP, lsl #32
    //     0x13ba2d4: stur            x2, [fp, #-0x10]
    // 0x13ba2d8: CheckStackOverflow
    //     0x13ba2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba2dc: cmp             SP, x16
    //     0x13ba2e0: b.ls            #0x13ba484
    // 0x13ba2e4: InitAsync() -> Future<void?>
    //     0x13ba2e4: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x13ba2e8: bl              #0x6326e0  ; InitAsyncStub
    // 0x13ba2ec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13ba2ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13ba2f0: ldr             x0, [x0, #0x1c80]
    //     0x13ba2f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13ba2f8: cmp             w0, w16
    //     0x13ba2fc: b.ne            #0x13ba308
    //     0x13ba300: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13ba304: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13ba308: ldur            x2, [fp, #-0x10]
    // 0x13ba30c: r1 = Function '<anonymous closure>':.
    //     0x13ba30c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c2e8] AnonymousClosure: (0x13ba48c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13ba310: ldr             x1, [x1, #0x2e8]
    // 0x13ba314: r0 = AllocateClosure()
    //     0x13ba314: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ba318: stp             x0, NULL, [SP]
    // 0x13ba31c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13ba31c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13ba320: r0 = GetNavigation.to()
    //     0x13ba320: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0x13ba324: mov             x1, x0
    // 0x13ba328: stur            x1, [fp, #-0x20]
    // 0x13ba32c: cmp             w1, NULL
    // 0x13ba330: b.eq            #0x13ba464
    // 0x13ba334: ldur            x2, [fp, #-0x10]
    // 0x13ba338: LoadField: r3 = r2->field_f
    //     0x13ba338: ldur            w3, [x2, #0xf]
    // 0x13ba33c: DecompressPointer r3
    //     0x13ba33c: add             x3, x3, HEAP, lsl #32
    // 0x13ba340: mov             x0, x1
    // 0x13ba344: stur            x3, [fp, #-0x18]
    // 0x13ba348: r0 = Await()
    //     0x13ba348: bl              #0x63248c  ; AwaitStub
    // 0x13ba34c: mov             x3, x0
    // 0x13ba350: r2 = Null
    //     0x13ba350: mov             x2, NULL
    // 0x13ba354: r1 = Null
    //     0x13ba354: mov             x1, NULL
    // 0x13ba358: stur            x3, [fp, #-0x28]
    // 0x13ba35c: r4 = 60
    //     0x13ba35c: movz            x4, #0x3c
    // 0x13ba360: branchIfSmi(r0, 0x13ba36c)
    //     0x13ba360: tbz             w0, #0, #0x13ba36c
    // 0x13ba364: r4 = LoadClassIdInstr(r0)
    //     0x13ba364: ldur            x4, [x0, #-1]
    //     0x13ba368: ubfx            x4, x4, #0xc, #0x14
    // 0x13ba36c: r17 = 5353
    //     0x13ba36c: movz            x17, #0x14e9
    // 0x13ba370: cmp             x4, x17
    // 0x13ba374: b.eq            #0x13ba38c
    // 0x13ba378: r8 = FilterResponse?
    //     0x13ba378: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c2f0] Type: FilterResponse?
    //     0x13ba37c: ldr             x8, [x8, #0x2f0]
    // 0x13ba380: r3 = Null
    //     0x13ba380: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c2f8] Null
    //     0x13ba384: ldr             x3, [x3, #0x2f8]
    // 0x13ba388: r0 = DefaultNullableTypeTest()
    //     0x13ba388: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x13ba38c: ldur            x0, [fp, #-0x28]
    // 0x13ba390: ldur            x1, [fp, #-0x18]
    // 0x13ba394: StoreField: r1->field_13 = r0
    //     0x13ba394: stur            w0, [x1, #0x13]
    //     0x13ba398: ldurb           w16, [x1, #-1]
    //     0x13ba39c: ldurb           w17, [x0, #-1]
    //     0x13ba3a0: and             x16, x17, x16, lsr #2
    //     0x13ba3a4: tst             x16, HEAP, lsr #32
    //     0x13ba3a8: b.eq            #0x13ba3b0
    //     0x13ba3ac: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13ba3b0: ldur            x0, [fp, #-0x10]
    // 0x13ba3b4: LoadField: r1 = r0->field_f
    //     0x13ba3b4: ldur            w1, [x0, #0xf]
    // 0x13ba3b8: DecompressPointer r1
    //     0x13ba3b8: add             x1, x1, HEAP, lsl #32
    // 0x13ba3bc: r0 = controller()
    //     0x13ba3bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba3c0: LoadField: r1 = r0->field_57
    //     0x13ba3c0: ldur            w1, [x0, #0x57]
    // 0x13ba3c4: DecompressPointer r1
    //     0x13ba3c4: add             x1, x1, HEAP, lsl #32
    // 0x13ba3c8: r0 = initRefresh()
    //     0x13ba3c8: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x13ba3cc: ldur            x0, [fp, #-0x10]
    // 0x13ba3d0: LoadField: r1 = r0->field_f
    //     0x13ba3d0: ldur            w1, [x0, #0xf]
    // 0x13ba3d4: DecompressPointer r1
    //     0x13ba3d4: add             x1, x1, HEAP, lsl #32
    // 0x13ba3d8: r0 = controller()
    //     0x13ba3d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba3dc: mov             x1, x0
    // 0x13ba3e0: ldur            x0, [fp, #-0x20]
    // 0x13ba3e4: stur            x1, [fp, #-0x18]
    // 0x13ba3e8: r0 = Await()
    //     0x13ba3e8: bl              #0x63248c  ; AwaitStub
    // 0x13ba3ec: mov             x3, x0
    // 0x13ba3f0: r2 = Null
    //     0x13ba3f0: mov             x2, NULL
    // 0x13ba3f4: r1 = Null
    //     0x13ba3f4: mov             x1, NULL
    // 0x13ba3f8: stur            x3, [fp, #-0x20]
    // 0x13ba3fc: r4 = 60
    //     0x13ba3fc: movz            x4, #0x3c
    // 0x13ba400: branchIfSmi(r0, 0x13ba40c)
    //     0x13ba400: tbz             w0, #0, #0x13ba40c
    // 0x13ba404: r4 = LoadClassIdInstr(r0)
    //     0x13ba404: ldur            x4, [x0, #-1]
    //     0x13ba408: ubfx            x4, x4, #0xc, #0x14
    // 0x13ba40c: r17 = 5353
    //     0x13ba40c: movz            x17, #0x14e9
    // 0x13ba410: cmp             x4, x17
    // 0x13ba414: b.eq            #0x13ba42c
    // 0x13ba418: r8 = FilterResponse?
    //     0x13ba418: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c2f0] Type: FilterResponse?
    //     0x13ba41c: ldr             x8, [x8, #0x2f0]
    // 0x13ba420: r3 = Null
    //     0x13ba420: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c308] Null
    //     0x13ba424: ldr             x3, [x3, #0x308]
    // 0x13ba428: r0 = DefaultNullableTypeTest()
    //     0x13ba428: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x13ba42c: ldur            x0, [fp, #-0x10]
    // 0x13ba430: LoadField: r1 = r0->field_f
    //     0x13ba430: ldur            w1, [x0, #0xf]
    // 0x13ba434: DecompressPointer r1
    //     0x13ba434: add             x1, x1, HEAP, lsl #32
    // 0x13ba438: r0 = controller()
    //     0x13ba438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba43c: LoadField: r1 = r0->field_87
    //     0x13ba43c: ldur            w1, [x0, #0x87]
    // 0x13ba440: DecompressPointer r1
    //     0x13ba440: add             x1, x1, HEAP, lsl #32
    // 0x13ba444: r16 = true
    //     0x13ba444: add             x16, NULL, #0x20  ; true
    // 0x13ba448: stp             x1, x16, [SP]
    // 0x13ba44c: ldur            x1, [fp, #-0x18]
    // 0x13ba450: ldur            x2, [fp, #-0x20]
    // 0x13ba454: r4 = const [0, 0x4, 0x2, 0x2, canReset, 0x2, sortBy, 0x3, null]
    //     0x13ba454: add             x4, PP, #0x3b, lsl #12  ; [pp+0x3bb88] List(9) [0, 0x4, 0x2, 0x2, "canReset", 0x2, "sortBy", 0x3, Null]
    //     0x13ba458: ldr             x4, [x4, #0xb88]
    // 0x13ba45c: r0 = getCollectionResponse()
    //     0x13ba45c: bl              #0x13b5d8c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getCollectionResponse
    // 0x13ba460: b               #0x13ba47c
    // 0x13ba464: ldur            x0, [fp, #-0x10]
    // 0x13ba468: LoadField: r1 = r0->field_f
    //     0x13ba468: ldur            w1, [x0, #0xf]
    // 0x13ba46c: DecompressPointer r1
    //     0x13ba46c: add             x1, x1, HEAP, lsl #32
    // 0x13ba470: r0 = controller()
    //     0x13ba470: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba474: mov             x1, x0
    // 0x13ba478: r0 = onRefreshPage()
    //     0x13ba478: bl              #0x13b5d28  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::onRefreshPage
    // 0x13ba47c: r0 = Null
    //     0x13ba47c: mov             x0, NULL
    // 0x13ba480: r0 = ReturnAsyncNotFuture()
    //     0x13ba480: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x13ba484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba484: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba488: b               #0x13ba2e4
  }
  [closure] CollectionFilter <anonymous closure>(dynamic) {
    // ** addr: 0x13ba48c, size: 0xd0
    // 0x13ba48c: EnterFrame
    //     0x13ba48c: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba490: mov             fp, SP
    // 0x13ba494: AllocStack(0x20)
    //     0x13ba494: sub             SP, SP, #0x20
    // 0x13ba498: SetupParameters()
    //     0x13ba498: ldr             x0, [fp, #0x10]
    //     0x13ba49c: ldur            w2, [x0, #0x17]
    //     0x13ba4a0: add             x2, x2, HEAP, lsl #32
    //     0x13ba4a4: stur            x2, [fp, #-8]
    // 0x13ba4a8: CheckStackOverflow
    //     0x13ba4a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba4ac: cmp             SP, x16
    //     0x13ba4b0: b.ls            #0x13ba554
    // 0x13ba4b4: LoadField: r1 = r2->field_f
    //     0x13ba4b4: ldur            w1, [x2, #0xf]
    // 0x13ba4b8: DecompressPointer r1
    //     0x13ba4b8: add             x1, x1, HEAP, lsl #32
    // 0x13ba4bc: r0 = controller()
    //     0x13ba4bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba4c0: LoadField: r1 = r0->field_67
    //     0x13ba4c0: ldur            w1, [x0, #0x67]
    // 0x13ba4c4: DecompressPointer r1
    //     0x13ba4c4: add             x1, x1, HEAP, lsl #32
    // 0x13ba4c8: r0 = value()
    //     0x13ba4c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ba4cc: LoadField: r1 = r0->field_b
    //     0x13ba4cc: ldur            w1, [x0, #0xb]
    // 0x13ba4d0: DecompressPointer r1
    //     0x13ba4d0: add             x1, x1, HEAP, lsl #32
    // 0x13ba4d4: cmp             w1, NULL
    // 0x13ba4d8: b.ne            #0x13ba4e4
    // 0x13ba4dc: r0 = Null
    //     0x13ba4dc: mov             x0, NULL
    // 0x13ba4e0: b               #0x13ba4ec
    // 0x13ba4e4: LoadField: r0 = r1->field_27
    //     0x13ba4e4: ldur            w0, [x1, #0x27]
    // 0x13ba4e8: DecompressPointer r0
    //     0x13ba4e8: add             x0, x0, HEAP, lsl #32
    // 0x13ba4ec: ldur            x2, [fp, #-8]
    // 0x13ba4f0: stur            x0, [fp, #-0x10]
    // 0x13ba4f4: LoadField: r1 = r2->field_f
    //     0x13ba4f4: ldur            w1, [x2, #0xf]
    // 0x13ba4f8: DecompressPointer r1
    //     0x13ba4f8: add             x1, x1, HEAP, lsl #32
    // 0x13ba4fc: r0 = controller()
    //     0x13ba4fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba500: LoadField: r1 = r0->field_63
    //     0x13ba500: ldur            w1, [x0, #0x63]
    // 0x13ba504: DecompressPointer r1
    //     0x13ba504: add             x1, x1, HEAP, lsl #32
    // 0x13ba508: r0 = value()
    //     0x13ba508: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ba50c: stur            x0, [fp, #-0x18]
    // 0x13ba510: r0 = CollectionFilter()
    //     0x13ba510: bl              #0x13ba55c  ; AllocateCollectionFilterStub -> CollectionFilter (size=0x18)
    // 0x13ba514: mov             x3, x0
    // 0x13ba518: ldur            x0, [fp, #-0x10]
    // 0x13ba51c: stur            x3, [fp, #-0x20]
    // 0x13ba520: StoreField: r3->field_b = r0
    //     0x13ba520: stur            w0, [x3, #0xb]
    // 0x13ba524: ldur            x0, [fp, #-0x18]
    // 0x13ba528: StoreField: r3->field_f = r0
    //     0x13ba528: stur            w0, [x3, #0xf]
    // 0x13ba52c: ldur            x2, [fp, #-8]
    // 0x13ba530: r1 = Function '<anonymous closure>':.
    //     0x13ba530: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c318] AnonymousClosure: (0x13ba568), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13ba534: ldr             x1, [x1, #0x318]
    // 0x13ba538: r0 = AllocateClosure()
    //     0x13ba538: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ba53c: mov             x1, x0
    // 0x13ba540: ldur            x0, [fp, #-0x20]
    // 0x13ba544: StoreField: r0->field_13 = r1
    //     0x13ba544: stur            w1, [x0, #0x13]
    // 0x13ba548: LeaveFrame
    //     0x13ba548: mov             SP, fp
    //     0x13ba54c: ldp             fp, lr, [SP], #0x10
    // 0x13ba550: ret
    //     0x13ba550: ret             
    // 0x13ba554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba554: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba558: b               #0x13ba4b4
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13ba568, size: 0x8c
    // 0x13ba568: EnterFrame
    //     0x13ba568: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba56c: mov             fp, SP
    // 0x13ba570: AllocStack(0x20)
    //     0x13ba570: sub             SP, SP, #0x20
    // 0x13ba574: SetupParameters()
    //     0x13ba574: ldr             x0, [fp, #0x10]
    //     0x13ba578: ldur            w2, [x0, #0x17]
    //     0x13ba57c: add             x2, x2, HEAP, lsl #32
    //     0x13ba580: stur            x2, [fp, #-8]
    // 0x13ba584: CheckStackOverflow
    //     0x13ba584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba588: cmp             SP, x16
    //     0x13ba58c: b.ls            #0x13ba5ec
    // 0x13ba590: LoadField: r1 = r2->field_f
    //     0x13ba590: ldur            w1, [x2, #0xf]
    // 0x13ba594: DecompressPointer r1
    //     0x13ba594: add             x1, x1, HEAP, lsl #32
    // 0x13ba598: r0 = controller()
    //     0x13ba598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba59c: LoadField: r1 = r0->field_5f
    //     0x13ba59c: ldur            w1, [x0, #0x5f]
    // 0x13ba5a0: DecompressPointer r1
    //     0x13ba5a0: add             x1, x1, HEAP, lsl #32
    // 0x13ba5a4: r0 = value()
    //     0x13ba5a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ba5a8: LoadField: r1 = r0->field_7
    //     0x13ba5a8: ldur            w1, [x0, #7]
    // 0x13ba5ac: DecompressPointer r1
    //     0x13ba5ac: add             x1, x1, HEAP, lsl #32
    // 0x13ba5b0: cmp             w1, NULL
    // 0x13ba5b4: b.eq            #0x13ba5bc
    // 0x13ba5b8: r0 = clear()
    //     0x13ba5b8: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x13ba5bc: ldur            x0, [fp, #-8]
    // 0x13ba5c0: LoadField: r1 = r0->field_13
    //     0x13ba5c0: ldur            w1, [x0, #0x13]
    // 0x13ba5c4: DecompressPointer r1
    //     0x13ba5c4: add             x1, x1, HEAP, lsl #32
    // 0x13ba5c8: r16 = <Null?>
    //     0x13ba5c8: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x13ba5cc: stp             x1, x16, [SP, #8]
    // 0x13ba5d0: str             NULL, [SP]
    // 0x13ba5d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x13ba5d4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x13ba5d8: r0 = pop()
    //     0x13ba5d8: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0x13ba5dc: r0 = Null
    //     0x13ba5dc: mov             x0, NULL
    // 0x13ba5e0: LeaveFrame
    //     0x13ba5e0: mov             SP, fp
    //     0x13ba5e4: ldp             fp, lr, [SP], #0x10
    // 0x13ba5e8: ret
    //     0x13ba5e8: ret             
    // 0x13ba5ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba5ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba5f0: b               #0x13ba590
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x13ba5f4, size: 0x164
    // 0x13ba5f4: EnterFrame
    //     0x13ba5f4: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba5f8: mov             fp, SP
    // 0x13ba5fc: AllocStack(0x20)
    //     0x13ba5fc: sub             SP, SP, #0x20
    // 0x13ba600: SetupParameters()
    //     0x13ba600: ldr             x0, [fp, #0x10]
    //     0x13ba604: ldur            w2, [x0, #0x17]
    //     0x13ba608: add             x2, x2, HEAP, lsl #32
    //     0x13ba60c: stur            x2, [fp, #-8]
    // 0x13ba610: CheckStackOverflow
    //     0x13ba610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba614: cmp             SP, x16
    //     0x13ba618: b.ls            #0x13ba74c
    // 0x13ba61c: LoadField: r1 = r2->field_f
    //     0x13ba61c: ldur            w1, [x2, #0xf]
    // 0x13ba620: DecompressPointer r1
    //     0x13ba620: add             x1, x1, HEAP, lsl #32
    // 0x13ba624: r0 = controller()
    //     0x13ba624: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba628: LoadField: r1 = r0->field_a3
    //     0x13ba628: ldur            w1, [x0, #0xa3]
    // 0x13ba62c: DecompressPointer r1
    //     0x13ba62c: add             x1, x1, HEAP, lsl #32
    // 0x13ba630: r0 = value()
    //     0x13ba630: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ba634: tbnz            w0, #4, #0x13ba73c
    // 0x13ba638: ldur            x2, [fp, #-8]
    // 0x13ba63c: LoadField: r1 = r2->field_f
    //     0x13ba63c: ldur            w1, [x2, #0xf]
    // 0x13ba640: DecompressPointer r1
    //     0x13ba640: add             x1, x1, HEAP, lsl #32
    // 0x13ba644: r0 = controller()
    //     0x13ba644: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba648: LoadField: r1 = r0->field_a3
    //     0x13ba648: ldur            w1, [x0, #0xa3]
    // 0x13ba64c: DecompressPointer r1
    //     0x13ba64c: add             x1, x1, HEAP, lsl #32
    // 0x13ba650: r2 = false
    //     0x13ba650: add             x2, NULL, #0x30  ; false
    // 0x13ba654: r0 = value=()
    //     0x13ba654: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13ba658: r0 = LoadStaticField(0x878)
    //     0x13ba658: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13ba65c: ldr             x0, [x0, #0x10f0]
    // 0x13ba660: cmp             w0, NULL
    // 0x13ba664: b.eq            #0x13ba754
    // 0x13ba668: LoadField: r3 = r0->field_53
    //     0x13ba668: ldur            w3, [x0, #0x53]
    // 0x13ba66c: DecompressPointer r3
    //     0x13ba66c: add             x3, x3, HEAP, lsl #32
    // 0x13ba670: stur            x3, [fp, #-0x18]
    // 0x13ba674: LoadField: r0 = r3->field_7
    //     0x13ba674: ldur            w0, [x3, #7]
    // 0x13ba678: DecompressPointer r0
    //     0x13ba678: add             x0, x0, HEAP, lsl #32
    // 0x13ba67c: ldur            x2, [fp, #-8]
    // 0x13ba680: stur            x0, [fp, #-0x10]
    // 0x13ba684: r1 = Function '<anonymous closure>':.
    //     0x13ba684: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c320] AnonymousClosure: (0x13ba758), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13ba688: ldr             x1, [x1, #0x320]
    // 0x13ba68c: r0 = AllocateClosure()
    //     0x13ba68c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ba690: ldur            x2, [fp, #-0x10]
    // 0x13ba694: mov             x3, x0
    // 0x13ba698: r1 = Null
    //     0x13ba698: mov             x1, NULL
    // 0x13ba69c: stur            x3, [fp, #-8]
    // 0x13ba6a0: cmp             w2, NULL
    // 0x13ba6a4: b.eq            #0x13ba6c4
    // 0x13ba6a8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13ba6a8: ldur            w4, [x2, #0x17]
    // 0x13ba6ac: DecompressPointer r4
    //     0x13ba6ac: add             x4, x4, HEAP, lsl #32
    // 0x13ba6b0: r8 = X0
    //     0x13ba6b0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x13ba6b4: LoadField: r9 = r4->field_7
    //     0x13ba6b4: ldur            x9, [x4, #7]
    // 0x13ba6b8: r3 = Null
    //     0x13ba6b8: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c328] Null
    //     0x13ba6bc: ldr             x3, [x3, #0x328]
    // 0x13ba6c0: blr             x9
    // 0x13ba6c4: ldur            x0, [fp, #-0x18]
    // 0x13ba6c8: LoadField: r1 = r0->field_b
    //     0x13ba6c8: ldur            w1, [x0, #0xb]
    // 0x13ba6cc: LoadField: r2 = r0->field_f
    //     0x13ba6cc: ldur            w2, [x0, #0xf]
    // 0x13ba6d0: DecompressPointer r2
    //     0x13ba6d0: add             x2, x2, HEAP, lsl #32
    // 0x13ba6d4: LoadField: r3 = r2->field_b
    //     0x13ba6d4: ldur            w3, [x2, #0xb]
    // 0x13ba6d8: r2 = LoadInt32Instr(r1)
    //     0x13ba6d8: sbfx            x2, x1, #1, #0x1f
    // 0x13ba6dc: stur            x2, [fp, #-0x20]
    // 0x13ba6e0: r1 = LoadInt32Instr(r3)
    //     0x13ba6e0: sbfx            x1, x3, #1, #0x1f
    // 0x13ba6e4: cmp             x2, x1
    // 0x13ba6e8: b.ne            #0x13ba6f4
    // 0x13ba6ec: mov             x1, x0
    // 0x13ba6f0: r0 = _growToNextCapacity()
    //     0x13ba6f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x13ba6f4: ldur            x2, [fp, #-0x18]
    // 0x13ba6f8: ldur            x3, [fp, #-0x20]
    // 0x13ba6fc: add             x4, x3, #1
    // 0x13ba700: lsl             x5, x4, #1
    // 0x13ba704: StoreField: r2->field_b = r5
    //     0x13ba704: stur            w5, [x2, #0xb]
    // 0x13ba708: LoadField: r1 = r2->field_f
    //     0x13ba708: ldur            w1, [x2, #0xf]
    // 0x13ba70c: DecompressPointer r1
    //     0x13ba70c: add             x1, x1, HEAP, lsl #32
    // 0x13ba710: ldur            x0, [fp, #-8]
    // 0x13ba714: ArrayStore: r1[r3] = r0  ; List_4
    //     0x13ba714: add             x25, x1, x3, lsl #2
    //     0x13ba718: add             x25, x25, #0xf
    //     0x13ba71c: str             w0, [x25]
    //     0x13ba720: tbz             w0, #0, #0x13ba73c
    //     0x13ba724: ldurb           w16, [x1, #-1]
    //     0x13ba728: ldurb           w17, [x0, #-1]
    //     0x13ba72c: and             x16, x17, x16, lsr #2
    //     0x13ba730: tst             x16, HEAP, lsr #32
    //     0x13ba734: b.eq            #0x13ba73c
    //     0x13ba738: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13ba73c: r0 = Instance_SizedBox
    //     0x13ba73c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13ba740: LeaveFrame
    //     0x13ba740: mov             SP, fp
    //     0x13ba744: ldp             fp, lr, [SP], #0x10
    // 0x13ba748: ret
    //     0x13ba748: ret             
    // 0x13ba74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba74c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba750: b               #0x13ba61c
    // 0x13ba754: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13ba754: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x13ba758, size: 0x50
    // 0x13ba758: EnterFrame
    //     0x13ba758: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba75c: mov             fp, SP
    // 0x13ba760: ldr             x0, [fp, #0x18]
    // 0x13ba764: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13ba764: ldur            w1, [x0, #0x17]
    // 0x13ba768: DecompressPointer r1
    //     0x13ba768: add             x1, x1, HEAP, lsl #32
    // 0x13ba76c: CheckStackOverflow
    //     0x13ba76c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba770: cmp             SP, x16
    //     0x13ba774: b.ls            #0x13ba7a0
    // 0x13ba778: LoadField: r0 = r1->field_f
    //     0x13ba778: ldur            w0, [x1, #0xf]
    // 0x13ba77c: DecompressPointer r0
    //     0x13ba77c: add             x0, x0, HEAP, lsl #32
    // 0x13ba780: LoadField: r2 = r1->field_13
    //     0x13ba780: ldur            w2, [x1, #0x13]
    // 0x13ba784: DecompressPointer r2
    //     0x13ba784: add             x2, x2, HEAP, lsl #32
    // 0x13ba788: mov             x1, x0
    // 0x13ba78c: r0 = _showCustomisedBottomSheet()
    //     0x13ba78c: bl              #0x13ba7a8  ; [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showCustomisedBottomSheet
    // 0x13ba790: r0 = Null
    //     0x13ba790: mov             x0, NULL
    // 0x13ba794: LeaveFrame
    //     0x13ba794: mov             SP, fp
    //     0x13ba798: ldp             fp, lr, [SP], #0x10
    // 0x13ba79c: ret
    //     0x13ba79c: ret             
    // 0x13ba7a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba7a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba7a4: b               #0x13ba778
  }
  _ _showCustomisedBottomSheet(/* No info */) {
    // ** addr: 0x13ba7a8, size: 0x80
    // 0x13ba7a8: EnterFrame
    //     0x13ba7a8: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba7ac: mov             fp, SP
    // 0x13ba7b0: AllocStack(0x38)
    //     0x13ba7b0: sub             SP, SP, #0x38
    // 0x13ba7b4: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x13ba7b4: stur            x1, [fp, #-8]
    //     0x13ba7b8: stur            x2, [fp, #-0x10]
    // 0x13ba7bc: CheckStackOverflow
    //     0x13ba7bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba7c0: cmp             SP, x16
    //     0x13ba7c4: b.ls            #0x13ba820
    // 0x13ba7c8: r1 = 1
    //     0x13ba7c8: movz            x1, #0x1
    // 0x13ba7cc: r0 = AllocateContext()
    //     0x13ba7cc: bl              #0x16f6108  ; AllocateContextStub
    // 0x13ba7d0: mov             x1, x0
    // 0x13ba7d4: ldur            x0, [fp, #-8]
    // 0x13ba7d8: StoreField: r1->field_f = r0
    //     0x13ba7d8: stur            w0, [x1, #0xf]
    // 0x13ba7dc: mov             x2, x1
    // 0x13ba7e0: r1 = Function '<anonymous closure>':.
    //     0x13ba7e0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c338] AnonymousClosure: (0x13ba828), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showCustomisedBottomSheet (0x13ba7a8)
    //     0x13ba7e4: ldr             x1, [x1, #0x338]
    // 0x13ba7e8: r0 = AllocateClosure()
    //     0x13ba7e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ba7ec: stp             x0, NULL, [SP, #0x18]
    // 0x13ba7f0: ldur            x16, [fp, #-0x10]
    // 0x13ba7f4: r30 = Instance_Color
    //     0x13ba7f4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13ba7f8: stp             lr, x16, [SP, #8]
    // 0x13ba7fc: r16 = true
    //     0x13ba7fc: add             x16, NULL, #0x20  ; true
    // 0x13ba800: str             x16, [SP]
    // 0x13ba804: r4 = const [0x1, 0x4, 0x4, 0x2, backgroundColor, 0x2, isScrollControlled, 0x3, null]
    //     0x13ba804: add             x4, PP, #0x34, lsl #12  ; [pp+0x345c0] List(9) [0x1, 0x4, 0x4, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x3, Null]
    //     0x13ba808: ldr             x4, [x4, #0x5c0]
    // 0x13ba80c: r0 = showModalBottomSheet()
    //     0x13ba80c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x13ba810: r0 = Null
    //     0x13ba810: mov             x0, NULL
    // 0x13ba814: LeaveFrame
    //     0x13ba814: mov             SP, fp
    //     0x13ba818: ldp             fp, lr, [SP], #0x10
    // 0x13ba81c: ret
    //     0x13ba81c: ret             
    // 0x13ba820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba820: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba824: b               #0x13ba7c8
  }
  [closure] CustomizedBottomSheet <anonymous closure>(dynamic, BuildContext?) {
    // ** addr: 0x13ba828, size: 0x158
    // 0x13ba828: EnterFrame
    //     0x13ba828: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba82c: mov             fp, SP
    // 0x13ba830: AllocStack(0x40)
    //     0x13ba830: sub             SP, SP, #0x40
    // 0x13ba834: SetupParameters()
    //     0x13ba834: ldr             x0, [fp, #0x18]
    //     0x13ba838: ldur            w2, [x0, #0x17]
    //     0x13ba83c: add             x2, x2, HEAP, lsl #32
    //     0x13ba840: stur            x2, [fp, #-8]
    // 0x13ba844: CheckStackOverflow
    //     0x13ba844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba848: cmp             SP, x16
    //     0x13ba84c: b.ls            #0x13ba978
    // 0x13ba850: LoadField: r1 = r2->field_f
    //     0x13ba850: ldur            w1, [x2, #0xf]
    // 0x13ba854: DecompressPointer r1
    //     0x13ba854: add             x1, x1, HEAP, lsl #32
    // 0x13ba858: r0 = controller()
    //     0x13ba858: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba85c: LoadField: r1 = r0->field_73
    //     0x13ba85c: ldur            w1, [x0, #0x73]
    // 0x13ba860: DecompressPointer r1
    //     0x13ba860: add             x1, x1, HEAP, lsl #32
    // 0x13ba864: r0 = value()
    //     0x13ba864: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ba868: ldur            x2, [fp, #-8]
    // 0x13ba86c: stur            x0, [fp, #-0x10]
    // 0x13ba870: LoadField: r1 = r2->field_f
    //     0x13ba870: ldur            w1, [x2, #0xf]
    // 0x13ba874: DecompressPointer r1
    //     0x13ba874: add             x1, x1, HEAP, lsl #32
    // 0x13ba878: r0 = controller()
    //     0x13ba878: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba87c: LoadField: r2 = r0->field_c3
    //     0x13ba87c: ldur            w2, [x0, #0xc3]
    // 0x13ba880: DecompressPointer r2
    //     0x13ba880: add             x2, x2, HEAP, lsl #32
    // 0x13ba884: ldur            x0, [fp, #-8]
    // 0x13ba888: stur            x2, [fp, #-0x18]
    // 0x13ba88c: LoadField: r1 = r0->field_f
    //     0x13ba88c: ldur            w1, [x0, #0xf]
    // 0x13ba890: DecompressPointer r1
    //     0x13ba890: add             x1, x1, HEAP, lsl #32
    // 0x13ba894: r0 = controller()
    //     0x13ba894: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba898: LoadField: r2 = r0->field_c7
    //     0x13ba898: ldur            w2, [x0, #0xc7]
    // 0x13ba89c: DecompressPointer r2
    //     0x13ba89c: add             x2, x2, HEAP, lsl #32
    // 0x13ba8a0: ldur            x0, [fp, #-8]
    // 0x13ba8a4: stur            x2, [fp, #-0x20]
    // 0x13ba8a8: LoadField: r1 = r0->field_f
    //     0x13ba8a8: ldur            w1, [x0, #0xf]
    // 0x13ba8ac: DecompressPointer r1
    //     0x13ba8ac: add             x1, x1, HEAP, lsl #32
    // 0x13ba8b0: r0 = controller()
    //     0x13ba8b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba8b4: LoadField: r2 = r0->field_cb
    //     0x13ba8b4: ldur            x2, [x0, #0xcb]
    // 0x13ba8b8: ldur            x0, [fp, #-8]
    // 0x13ba8bc: stur            x2, [fp, #-0x28]
    // 0x13ba8c0: LoadField: r1 = r0->field_f
    //     0x13ba8c0: ldur            w1, [x0, #0xf]
    // 0x13ba8c4: DecompressPointer r1
    //     0x13ba8c4: add             x1, x1, HEAP, lsl #32
    // 0x13ba8c8: r0 = controller()
    //     0x13ba8c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba8cc: LoadField: r2 = r0->field_d3
    //     0x13ba8cc: ldur            w2, [x0, #0xd3]
    // 0x13ba8d0: DecompressPointer r2
    //     0x13ba8d0: add             x2, x2, HEAP, lsl #32
    // 0x13ba8d4: ldur            x0, [fp, #-8]
    // 0x13ba8d8: stur            x2, [fp, #-0x30]
    // 0x13ba8dc: LoadField: r1 = r0->field_f
    //     0x13ba8dc: ldur            w1, [x0, #0xf]
    // 0x13ba8e0: DecompressPointer r1
    //     0x13ba8e0: add             x1, x1, HEAP, lsl #32
    // 0x13ba8e4: r0 = controller()
    //     0x13ba8e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba8e8: LoadField: r1 = r0->field_7f
    //     0x13ba8e8: ldur            w1, [x0, #0x7f]
    // 0x13ba8ec: DecompressPointer r1
    //     0x13ba8ec: add             x1, x1, HEAP, lsl #32
    // 0x13ba8f0: r0 = value()
    //     0x13ba8f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ba8f4: stur            x0, [fp, #-0x38]
    // 0x13ba8f8: r0 = CustomizedBottomSheet()
    //     0x13ba8f8: bl              #0x9cf7bc  ; AllocateCustomizedBottomSheetStub -> CustomizedBottomSheet (size=0x50)
    // 0x13ba8fc: mov             x3, x0
    // 0x13ba900: ldur            x0, [fp, #-0x10]
    // 0x13ba904: stur            x3, [fp, #-0x40]
    // 0x13ba908: StoreField: r3->field_b = r0
    //     0x13ba908: stur            w0, [x3, #0xb]
    // 0x13ba90c: ldur            x0, [fp, #-0x18]
    // 0x13ba910: StoreField: r3->field_f = r0
    //     0x13ba910: stur            w0, [x3, #0xf]
    // 0x13ba914: ldur            x0, [fp, #-0x20]
    // 0x13ba918: StoreField: r3->field_13 = r0
    //     0x13ba918: stur            w0, [x3, #0x13]
    // 0x13ba91c: r0 = 1
    //     0x13ba91c: movz            x0, #0x1
    // 0x13ba920: ArrayStore: r3[0] = r0  ; List_8
    //     0x13ba920: stur            x0, [x3, #0x17]
    // 0x13ba924: ldur            x2, [fp, #-8]
    // 0x13ba928: r1 = Function '<anonymous closure>':.
    //     0x13ba928: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c340] AnonymousClosure: (0x13ba980), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showCustomisedBottomSheet (0x13ba7a8)
    //     0x13ba92c: ldr             x1, [x1, #0x340]
    // 0x13ba930: r0 = AllocateClosure()
    //     0x13ba930: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ba934: mov             x1, x0
    // 0x13ba938: ldur            x0, [fp, #-0x40]
    // 0x13ba93c: StoreField: r0->field_1f = r1
    //     0x13ba93c: stur            w1, [x0, #0x1f]
    // 0x13ba940: r1 = "home_page"
    //     0x13ba940: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x13ba944: ldr             x1, [x1, #0xe60]
    // 0x13ba948: StoreField: r0->field_23 = r1
    //     0x13ba948: stur            w1, [x0, #0x23]
    // 0x13ba94c: ldur            x1, [fp, #-0x28]
    // 0x13ba950: StoreField: r0->field_27 = r1
    //     0x13ba950: stur            x1, [x0, #0x27]
    // 0x13ba954: ldur            x1, [fp, #-0x30]
    // 0x13ba958: StoreField: r0->field_2f = r1
    //     0x13ba958: stur            w1, [x0, #0x2f]
    // 0x13ba95c: ldur            x1, [fp, #-0x38]
    // 0x13ba960: StoreField: r0->field_33 = r1
    //     0x13ba960: stur            w1, [x0, #0x33]
    // 0x13ba964: r1 = ""
    //     0x13ba964: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13ba968: StoreField: r0->field_43 = r1
    //     0x13ba968: stur            w1, [x0, #0x43]
    // 0x13ba96c: LeaveFrame
    //     0x13ba96c: mov             SP, fp
    //     0x13ba970: ldp             fp, lr, [SP], #0x10
    // 0x13ba974: ret
    //     0x13ba974: ret             
    // 0x13ba978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba978: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba97c: b               #0x13ba850
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13ba980, size: 0x50
    // 0x13ba980: EnterFrame
    //     0x13ba980: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba984: mov             fp, SP
    // 0x13ba988: ldr             x0, [fp, #0x10]
    // 0x13ba98c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13ba98c: ldur            w1, [x0, #0x17]
    // 0x13ba990: DecompressPointer r1
    //     0x13ba990: add             x1, x1, HEAP, lsl #32
    // 0x13ba994: CheckStackOverflow
    //     0x13ba994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba998: cmp             SP, x16
    //     0x13ba99c: b.ls            #0x13ba9c8
    // 0x13ba9a0: LoadField: r0 = r1->field_f
    //     0x13ba9a0: ldur            w0, [x1, #0xf]
    // 0x13ba9a4: DecompressPointer r0
    //     0x13ba9a4: add             x0, x0, HEAP, lsl #32
    // 0x13ba9a8: mov             x1, x0
    // 0x13ba9ac: r0 = controller()
    //     0x13ba9ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ba9b0: mov             x1, x0
    // 0x13ba9b4: r0 = getBagData()
    //     0x13ba9b4: bl              #0x91dec4  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getBagData
    // 0x13ba9b8: r0 = Null
    //     0x13ba9b8: mov             x0, NULL
    // 0x13ba9bc: LeaveFrame
    //     0x13ba9bc: mov             SP, fp
    //     0x13ba9c0: ldp             fp, lr, [SP], #0x10
    // 0x13ba9c4: ret
    //     0x13ba9c4: ret             
    // 0x13ba9c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13ba9c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13ba9cc: b               #0x13ba9a0
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x13ba9d0, size: 0x164
    // 0x13ba9d0: EnterFrame
    //     0x13ba9d0: stp             fp, lr, [SP, #-0x10]!
    //     0x13ba9d4: mov             fp, SP
    // 0x13ba9d8: AllocStack(0x20)
    //     0x13ba9d8: sub             SP, SP, #0x20
    // 0x13ba9dc: SetupParameters()
    //     0x13ba9dc: ldr             x0, [fp, #0x10]
    //     0x13ba9e0: ldur            w2, [x0, #0x17]
    //     0x13ba9e4: add             x2, x2, HEAP, lsl #32
    //     0x13ba9e8: stur            x2, [fp, #-8]
    // 0x13ba9ec: CheckStackOverflow
    //     0x13ba9ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13ba9f0: cmp             SP, x16
    //     0x13ba9f4: b.ls            #0x13bab28
    // 0x13ba9f8: LoadField: r1 = r2->field_f
    //     0x13ba9f8: ldur            w1, [x2, #0xf]
    // 0x13ba9fc: DecompressPointer r1
    //     0x13ba9fc: add             x1, x1, HEAP, lsl #32
    // 0x13baa00: r0 = controller()
    //     0x13baa00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13baa04: LoadField: r1 = r0->field_9f
    //     0x13baa04: ldur            w1, [x0, #0x9f]
    // 0x13baa08: DecompressPointer r1
    //     0x13baa08: add             x1, x1, HEAP, lsl #32
    // 0x13baa0c: r0 = value()
    //     0x13baa0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13baa10: tbnz            w0, #4, #0x13bab18
    // 0x13baa14: ldur            x2, [fp, #-8]
    // 0x13baa18: LoadField: r1 = r2->field_f
    //     0x13baa18: ldur            w1, [x2, #0xf]
    // 0x13baa1c: DecompressPointer r1
    //     0x13baa1c: add             x1, x1, HEAP, lsl #32
    // 0x13baa20: r0 = controller()
    //     0x13baa20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13baa24: LoadField: r1 = r0->field_9f
    //     0x13baa24: ldur            w1, [x0, #0x9f]
    // 0x13baa28: DecompressPointer r1
    //     0x13baa28: add             x1, x1, HEAP, lsl #32
    // 0x13baa2c: r2 = false
    //     0x13baa2c: add             x2, NULL, #0x30  ; false
    // 0x13baa30: r0 = value=()
    //     0x13baa30: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13baa34: r0 = LoadStaticField(0x878)
    //     0x13baa34: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13baa38: ldr             x0, [x0, #0x10f0]
    // 0x13baa3c: cmp             w0, NULL
    // 0x13baa40: b.eq            #0x13bab30
    // 0x13baa44: LoadField: r3 = r0->field_53
    //     0x13baa44: ldur            w3, [x0, #0x53]
    // 0x13baa48: DecompressPointer r3
    //     0x13baa48: add             x3, x3, HEAP, lsl #32
    // 0x13baa4c: stur            x3, [fp, #-0x18]
    // 0x13baa50: LoadField: r0 = r3->field_7
    //     0x13baa50: ldur            w0, [x3, #7]
    // 0x13baa54: DecompressPointer r0
    //     0x13baa54: add             x0, x0, HEAP, lsl #32
    // 0x13baa58: ldur            x2, [fp, #-8]
    // 0x13baa5c: stur            x0, [fp, #-0x10]
    // 0x13baa60: r1 = Function '<anonymous closure>':.
    //     0x13baa60: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c348] AnonymousClosure: (0x13bab34), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x13baa64: ldr             x1, [x1, #0x348]
    // 0x13baa68: r0 = AllocateClosure()
    //     0x13baa68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13baa6c: ldur            x2, [fp, #-0x10]
    // 0x13baa70: mov             x3, x0
    // 0x13baa74: r1 = Null
    //     0x13baa74: mov             x1, NULL
    // 0x13baa78: stur            x3, [fp, #-8]
    // 0x13baa7c: cmp             w2, NULL
    // 0x13baa80: b.eq            #0x13baaa0
    // 0x13baa84: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13baa84: ldur            w4, [x2, #0x17]
    // 0x13baa88: DecompressPointer r4
    //     0x13baa88: add             x4, x4, HEAP, lsl #32
    // 0x13baa8c: r8 = X0
    //     0x13baa8c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x13baa90: LoadField: r9 = r4->field_7
    //     0x13baa90: ldur            x9, [x4, #7]
    // 0x13baa94: r3 = Null
    //     0x13baa94: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c350] Null
    //     0x13baa98: ldr             x3, [x3, #0x350]
    // 0x13baa9c: blr             x9
    // 0x13baaa0: ldur            x0, [fp, #-0x18]
    // 0x13baaa4: LoadField: r1 = r0->field_b
    //     0x13baaa4: ldur            w1, [x0, #0xb]
    // 0x13baaa8: LoadField: r2 = r0->field_f
    //     0x13baaa8: ldur            w2, [x0, #0xf]
    // 0x13baaac: DecompressPointer r2
    //     0x13baaac: add             x2, x2, HEAP, lsl #32
    // 0x13baab0: LoadField: r3 = r2->field_b
    //     0x13baab0: ldur            w3, [x2, #0xb]
    // 0x13baab4: r2 = LoadInt32Instr(r1)
    //     0x13baab4: sbfx            x2, x1, #1, #0x1f
    // 0x13baab8: stur            x2, [fp, #-0x20]
    // 0x13baabc: r1 = LoadInt32Instr(r3)
    //     0x13baabc: sbfx            x1, x3, #1, #0x1f
    // 0x13baac0: cmp             x2, x1
    // 0x13baac4: b.ne            #0x13baad0
    // 0x13baac8: mov             x1, x0
    // 0x13baacc: r0 = _growToNextCapacity()
    //     0x13baacc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x13baad0: ldur            x2, [fp, #-0x18]
    // 0x13baad4: ldur            x3, [fp, #-0x20]
    // 0x13baad8: add             x4, x3, #1
    // 0x13baadc: lsl             x5, x4, #1
    // 0x13baae0: StoreField: r2->field_b = r5
    //     0x13baae0: stur            w5, [x2, #0xb]
    // 0x13baae4: LoadField: r1 = r2->field_f
    //     0x13baae4: ldur            w1, [x2, #0xf]
    // 0x13baae8: DecompressPointer r1
    //     0x13baae8: add             x1, x1, HEAP, lsl #32
    // 0x13baaec: ldur            x0, [fp, #-8]
    // 0x13baaf0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x13baaf0: add             x25, x1, x3, lsl #2
    //     0x13baaf4: add             x25, x25, #0xf
    //     0x13baaf8: str             w0, [x25]
    //     0x13baafc: tbz             w0, #0, #0x13bab18
    //     0x13bab00: ldurb           w16, [x1, #-1]
    //     0x13bab04: ldurb           w17, [x0, #-1]
    //     0x13bab08: and             x16, x17, x16, lsr #2
    //     0x13bab0c: tst             x16, HEAP, lsr #32
    //     0x13bab10: b.eq            #0x13bab18
    //     0x13bab14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13bab18: r0 = Instance_SizedBox
    //     0x13bab18: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13bab1c: LeaveFrame
    //     0x13bab1c: mov             SP, fp
    //     0x13bab20: ldp             fp, lr, [SP], #0x10
    // 0x13bab24: ret
    //     0x13bab24: ret             
    // 0x13bab28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bab28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bab2c: b               #0x13ba9f8
    // 0x13bab30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13bab30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x13bab34, size: 0x48
    // 0x13bab34: EnterFrame
    //     0x13bab34: stp             fp, lr, [SP, #-0x10]!
    //     0x13bab38: mov             fp, SP
    // 0x13bab3c: ldr             x0, [fp, #0x18]
    // 0x13bab40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13bab40: ldur            w1, [x0, #0x17]
    // 0x13bab44: DecompressPointer r1
    //     0x13bab44: add             x1, x1, HEAP, lsl #32
    // 0x13bab48: CheckStackOverflow
    //     0x13bab48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bab4c: cmp             SP, x16
    //     0x13bab50: b.ls            #0x13bab74
    // 0x13bab54: LoadField: r0 = r1->field_f
    //     0x13bab54: ldur            w0, [x1, #0xf]
    // 0x13bab58: DecompressPointer r0
    //     0x13bab58: add             x0, x0, HEAP, lsl #32
    // 0x13bab5c: mov             x1, x0
    // 0x13bab60: r0 = _showBagBottomSheet()
    //     0x13bab60: bl              #0x13bab7c  ; [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet
    // 0x13bab64: r0 = Null
    //     0x13bab64: mov             x0, NULL
    // 0x13bab68: LeaveFrame
    //     0x13bab68: mov             SP, fp
    //     0x13bab6c: ldp             fp, lr, [SP], #0x10
    // 0x13bab70: ret
    //     0x13bab70: ret             
    // 0x13bab74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bab74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bab78: b               #0x13bab54
  }
  _ _showBagBottomSheet(/* No info */) {
    // ** addr: 0x13bab7c, size: 0xb8
    // 0x13bab7c: EnterFrame
    //     0x13bab7c: stp             fp, lr, [SP, #-0x10]!
    //     0x13bab80: mov             fp, SP
    // 0x13bab84: AllocStack(0x40)
    //     0x13bab84: sub             SP, SP, #0x40
    // 0x13bab88: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */)
    //     0x13bab88: stur            x1, [fp, #-8]
    // 0x13bab8c: CheckStackOverflow
    //     0x13bab8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bab90: cmp             SP, x16
    //     0x13bab94: b.ls            #0x13bac28
    // 0x13bab98: r1 = 1
    //     0x13bab98: movz            x1, #0x1
    // 0x13bab9c: r0 = AllocateContext()
    //     0x13bab9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x13baba0: mov             x1, x0
    // 0x13baba4: ldur            x0, [fp, #-8]
    // 0x13baba8: stur            x1, [fp, #-0x10]
    // 0x13babac: StoreField: r1->field_f = r0
    //     0x13babac: stur            w0, [x1, #0xf]
    // 0x13babb0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13babb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13babb4: ldr             x0, [x0, #0x1c80]
    //     0x13babb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13babbc: cmp             w0, w16
    //     0x13babc0: b.ne            #0x13babcc
    //     0x13babc4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13babc8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13babcc: r0 = GetNavigation.context()
    //     0x13babcc: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x13babd0: stur            x0, [fp, #-8]
    // 0x13babd4: cmp             w0, NULL
    // 0x13babd8: b.eq            #0x13bac30
    // 0x13babdc: ldur            x2, [fp, #-0x10]
    // 0x13babe0: r1 = Function '<anonymous closure>':.
    //     0x13babe0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c360] AnonymousClosure: (0x13bac34), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x13bab7c)
    //     0x13babe4: ldr             x1, [x1, #0x360]
    // 0x13babe8: r0 = AllocateClosure()
    //     0x13babe8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13babec: stp             x0, NULL, [SP, #0x20]
    // 0x13babf0: ldur            x16, [fp, #-8]
    // 0x13babf4: r30 = Instance_Color
    //     0x13babf4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13babf8: stp             lr, x16, [SP, #0x10]
    // 0x13babfc: r16 = Instance_RoundedRectangleBorder
    //     0x13babfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x13bac00: ldr             x16, [x16, #0xd68]
    // 0x13bac04: r30 = true
    //     0x13bac04: add             lr, NULL, #0x20  ; true
    // 0x13bac08: stp             lr, x16, [SP]
    // 0x13bac0c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x2, isScrollControlled, 0x4, shape, 0x3, null]
    //     0x13bac0c: add             x4, PP, #0x34, lsl #12  ; [pp+0x345f0] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x4, "shape", 0x3, Null]
    //     0x13bac10: ldr             x4, [x4, #0x5f0]
    // 0x13bac14: r0 = showModalBottomSheet()
    //     0x13bac14: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x13bac18: r0 = Null
    //     0x13bac18: mov             x0, NULL
    // 0x13bac1c: LeaveFrame
    //     0x13bac1c: mov             SP, fp
    //     0x13bac20: ldp             fp, lr, [SP], #0x10
    // 0x13bac24: ret
    //     0x13bac24: ret             
    // 0x13bac28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bac28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bac2c: b               #0x13bab98
    // 0x13bac30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13bac30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x13bac34, size: 0x204
    // 0x13bac34: EnterFrame
    //     0x13bac34: stp             fp, lr, [SP, #-0x10]!
    //     0x13bac38: mov             fp, SP
    // 0x13bac3c: AllocStack(0x58)
    //     0x13bac3c: sub             SP, SP, #0x58
    // 0x13bac40: SetupParameters()
    //     0x13bac40: ldr             x0, [fp, #0x18]
    //     0x13bac44: ldur            w2, [x0, #0x17]
    //     0x13bac48: add             x2, x2, HEAP, lsl #32
    //     0x13bac4c: stur            x2, [fp, #-8]
    // 0x13bac50: CheckStackOverflow
    //     0x13bac50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bac54: cmp             SP, x16
    //     0x13bac58: b.ls            #0x13bae30
    // 0x13bac5c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13bac5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13bac60: ldr             x0, [x0, #0x1c80]
    //     0x13bac64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13bac68: cmp             w0, w16
    //     0x13bac6c: b.ne            #0x13bac78
    //     0x13bac70: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13bac74: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13bac78: r0 = GetNavigation.size()
    //     0x13bac78: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13bac7c: LoadField: d0 = r0->field_f
    //     0x13bac7c: ldur            d0, [x0, #0xf]
    // 0x13bac80: d1 = 0.750000
    //     0x13bac80: fmov            d1, #0.75000000
    // 0x13bac84: fmul            d2, d0, d1
    // 0x13bac88: stur            d2, [fp, #-0x48]
    // 0x13bac8c: r0 = GetNavigation.size()
    //     0x13bac8c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13bac90: LoadField: d0 = r0->field_f
    //     0x13bac90: ldur            d0, [x0, #0xf]
    // 0x13bac94: d1 = 0.350000
    //     0x13bac94: add             x17, PP, #0x34, lsl #12  ; [pp+0x34ab8] IMM: double(0.35) from 0x3fd6666666666666
    //     0x13bac98: ldr             d1, [x17, #0xab8]
    // 0x13bac9c: fmul            d2, d0, d1
    // 0x13baca0: stur            d2, [fp, #-0x50]
    // 0x13baca4: r0 = BoxConstraints()
    //     0x13baca4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x13baca8: stur            x0, [fp, #-0x10]
    // 0x13bacac: StoreField: r0->field_7 = rZR
    //     0x13bacac: stur            xzr, [x0, #7]
    // 0x13bacb0: d0 = inf
    //     0x13bacb0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x13bacb4: StoreField: r0->field_f = d0
    //     0x13bacb4: stur            d0, [x0, #0xf]
    // 0x13bacb8: ldur            d0, [fp, #-0x50]
    // 0x13bacbc: ArrayStore: r0[0] = d0  ; List_8
    //     0x13bacbc: stur            d0, [x0, #0x17]
    // 0x13bacc0: ldur            d0, [fp, #-0x48]
    // 0x13bacc4: StoreField: r0->field_1f = d0
    //     0x13bacc4: stur            d0, [x0, #0x1f]
    // 0x13bacc8: ldur            x2, [fp, #-8]
    // 0x13baccc: LoadField: r1 = r2->field_f
    //     0x13baccc: ldur            w1, [x2, #0xf]
    // 0x13bacd0: DecompressPointer r1
    //     0x13bacd0: add             x1, x1, HEAP, lsl #32
    // 0x13bacd4: r0 = controller()
    //     0x13bacd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bacd8: LoadField: r1 = r0->field_7f
    //     0x13bacd8: ldur            w1, [x0, #0x7f]
    // 0x13bacdc: DecompressPointer r1
    //     0x13bacdc: add             x1, x1, HEAP, lsl #32
    // 0x13bace0: r0 = value()
    //     0x13bace0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bace4: ldur            x2, [fp, #-8]
    // 0x13bace8: stur            x0, [fp, #-0x18]
    // 0x13bacec: LoadField: r1 = r2->field_f
    //     0x13bacec: ldur            w1, [x2, #0xf]
    // 0x13bacf0: DecompressPointer r1
    //     0x13bacf0: add             x1, x1, HEAP, lsl #32
    // 0x13bacf4: r0 = controller()
    //     0x13bacf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bacf8: LoadField: r1 = r0->field_6f
    //     0x13bacf8: ldur            w1, [x0, #0x6f]
    // 0x13bacfc: DecompressPointer r1
    //     0x13bacfc: add             x1, x1, HEAP, lsl #32
    // 0x13bad00: r0 = value()
    //     0x13bad00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bad04: ldur            x2, [fp, #-8]
    // 0x13bad08: stur            x0, [fp, #-0x20]
    // 0x13bad0c: LoadField: r1 = r2->field_f
    //     0x13bad0c: ldur            w1, [x2, #0xf]
    // 0x13bad10: DecompressPointer r1
    //     0x13bad10: add             x1, x1, HEAP, lsl #32
    // 0x13bad14: r0 = controller()
    //     0x13bad14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bad18: ldur            x2, [fp, #-8]
    // 0x13bad1c: stur            x0, [fp, #-0x28]
    // 0x13bad20: LoadField: r1 = r2->field_f
    //     0x13bad20: ldur            w1, [x2, #0xf]
    // 0x13bad24: DecompressPointer r1
    //     0x13bad24: add             x1, x1, HEAP, lsl #32
    // 0x13bad28: r0 = controller()
    //     0x13bad28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bad2c: ldur            x2, [fp, #-8]
    // 0x13bad30: LoadField: r1 = r2->field_f
    //     0x13bad30: ldur            w1, [x2, #0xf]
    // 0x13bad34: DecompressPointer r1
    //     0x13bad34: add             x1, x1, HEAP, lsl #32
    // 0x13bad38: r0 = controller()
    //     0x13bad38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bad3c: mov             x1, x0
    // 0x13bad40: r0 = bagEntities()
    //     0x13bad40: bl              #0x89ec4c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bagEntities
    // 0x13bad44: LoadField: r1 = r0->field_b
    //     0x13bad44: ldur            w1, [x0, #0xb]
    // 0x13bad48: DecompressPointer r1
    //     0x13bad48: add             x1, x1, HEAP, lsl #32
    // 0x13bad4c: cmp             w1, NULL
    // 0x13bad50: b.ne            #0x13bad5c
    // 0x13bad54: r4 = Null
    //     0x13bad54: mov             x4, NULL
    // 0x13bad58: b               #0x13bad68
    // 0x13bad5c: LoadField: r0 = r1->field_7
    //     0x13bad5c: ldur            w0, [x1, #7]
    // 0x13bad60: DecompressPointer r0
    //     0x13bad60: add             x0, x0, HEAP, lsl #32
    // 0x13bad64: mov             x4, x0
    // 0x13bad68: ldur            x3, [fp, #-0x10]
    // 0x13bad6c: mov             x0, x4
    // 0x13bad70: stur            x4, [fp, #-0x30]
    // 0x13bad74: r2 = Null
    //     0x13bad74: mov             x2, NULL
    // 0x13bad78: r1 = Null
    //     0x13bad78: mov             x1, NULL
    // 0x13bad7c: r4 = 60
    //     0x13bad7c: movz            x4, #0x3c
    // 0x13bad80: branchIfSmi(r0, 0x13bad8c)
    //     0x13bad80: tbz             w0, #0, #0x13bad8c
    // 0x13bad84: r4 = LoadClassIdInstr(r0)
    //     0x13bad84: ldur            x4, [x0, #-1]
    //     0x13bad88: ubfx            x4, x4, #0xc, #0x14
    // 0x13bad8c: sub             x4, x4, #0x5e
    // 0x13bad90: cmp             x4, #1
    // 0x13bad94: b.ls            #0x13bada8
    // 0x13bad98: r8 = String?
    //     0x13bad98: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x13bad9c: r3 = Null
    //     0x13bad9c: add             x3, PP, #0x3c, lsl #12  ; [pp+0x3c368] Null
    //     0x13bada0: ldr             x3, [x3, #0x368]
    // 0x13bada4: r0 = String?()
    //     0x13bada4: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x13bada8: ldur            x2, [fp, #-0x28]
    // 0x13badac: r1 = Function 'removeItem':.
    //     0x13badac: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c378] AnonymousClosure: (0x13bb6ac), in [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::removeItem (0x13bb6e8)
    //     0x13badb0: ldr             x1, [x1, #0x378]
    // 0x13badb4: r0 = AllocateClosure()
    //     0x13badb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13badb8: ldur            x2, [fp, #-8]
    // 0x13badbc: r1 = Function '<anonymous closure>':.
    //     0x13badbc: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c380] AnonymousClosure: (0x13bb574), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x13bab7c)
    //     0x13badc0: ldr             x1, [x1, #0x380]
    // 0x13badc4: stur            x0, [fp, #-0x28]
    // 0x13badc8: r0 = AllocateClosure()
    //     0x13badc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13badcc: ldur            x2, [fp, #-8]
    // 0x13badd0: r1 = Function '<anonymous closure>':.
    //     0x13badd0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c388] AnonymousClosure: (0x13bae38), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x13bab7c)
    //     0x13badd4: ldr             x1, [x1, #0x388]
    // 0x13badd8: stur            x0, [fp, #-8]
    // 0x13baddc: r0 = AllocateClosure()
    //     0x13baddc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bade0: stur            x0, [fp, #-0x38]
    // 0x13bade4: r0 = BagBottomSheet()
    //     0x13bade4: bl              #0x9d003c  ; AllocateBagBottomSheetStub -> BagBottomSheet (size=0x28)
    // 0x13bade8: stur            x0, [fp, #-0x40]
    // 0x13badec: ldur            x16, [fp, #-8]
    // 0x13badf0: str             x16, [SP]
    // 0x13badf4: mov             x1, x0
    // 0x13badf8: ldur            x2, [fp, #-0x20]
    // 0x13badfc: ldur            x3, [fp, #-0x38]
    // 0x13bae00: ldur            x5, [fp, #-0x18]
    // 0x13bae04: ldur            x6, [fp, #-0x30]
    // 0x13bae08: ldur            x7, [fp, #-0x28]
    // 0x13bae0c: r0 = BagBottomSheet()
    //     0x13bae0c: bl              #0x9cfc38  ; [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::BagBottomSheet
    // 0x13bae10: r0 = ConstrainedBox()
    //     0x13bae10: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x13bae14: ldur            x1, [fp, #-0x10]
    // 0x13bae18: StoreField: r0->field_f = r1
    //     0x13bae18: stur            w1, [x0, #0xf]
    // 0x13bae1c: ldur            x1, [fp, #-0x40]
    // 0x13bae20: StoreField: r0->field_b = r1
    //     0x13bae20: stur            w1, [x0, #0xb]
    // 0x13bae24: LeaveFrame
    //     0x13bae24: mov             SP, fp
    //     0x13bae28: ldp             fp, lr, [SP], #0x10
    // 0x13bae2c: ret
    //     0x13bae2c: ret             
    // 0x13bae30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bae30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bae34: b               #0x13bac5c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13bae38, size: 0x80
    // 0x13bae38: EnterFrame
    //     0x13bae38: stp             fp, lr, [SP, #-0x10]!
    //     0x13bae3c: mov             fp, SP
    // 0x13bae40: AllocStack(0x10)
    //     0x13bae40: sub             SP, SP, #0x10
    // 0x13bae44: SetupParameters()
    //     0x13bae44: ldr             x0, [fp, #0x10]
    //     0x13bae48: ldur            w1, [x0, #0x17]
    //     0x13bae4c: add             x1, x1, HEAP, lsl #32
    //     0x13bae50: stur            x1, [fp, #-8]
    // 0x13bae54: CheckStackOverflow
    //     0x13bae54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bae58: cmp             SP, x16
    //     0x13bae5c: b.ls            #0x13baeb0
    // 0x13bae60: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13bae60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13bae64: ldr             x0, [x0, #0x1c80]
    //     0x13bae68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13bae6c: cmp             w0, w16
    //     0x13bae70: b.ne            #0x13bae7c
    //     0x13bae74: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13bae78: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13bae7c: str             NULL, [SP]
    // 0x13bae80: r4 = const [0x1, 0, 0, 0, null]
    //     0x13bae80: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x13bae84: r0 = GetNavigation.back()
    //     0x13bae84: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x13bae88: ldur            x0, [fp, #-8]
    // 0x13bae8c: LoadField: r1 = r0->field_f
    //     0x13bae8c: ldur            w1, [x0, #0xf]
    // 0x13bae90: DecompressPointer r1
    //     0x13bae90: add             x1, x1, HEAP, lsl #32
    // 0x13bae94: r0 = controller()
    //     0x13bae94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bae98: mov             x1, x0
    // 0x13bae9c: r0 = checkoutVariant()
    //     0x13bae9c: bl              #0x13baeb8  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::checkoutVariant
    // 0x13baea0: r0 = Null
    //     0x13baea0: mov             x0, NULL
    // 0x13baea4: LeaveFrame
    //     0x13baea4: mov             SP, fp
    //     0x13baea8: ldp             fp, lr, [SP], #0x10
    // 0x13baeac: ret
    //     0x13baeac: ret             
    // 0x13baeb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13baeb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13baeb4: b               #0x13bae60
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13bb574, size: 0xe8
    // 0x13bb574: EnterFrame
    //     0x13bb574: stp             fp, lr, [SP, #-0x10]!
    //     0x13bb578: mov             fp, SP
    // 0x13bb57c: AllocStack(0x28)
    //     0x13bb57c: sub             SP, SP, #0x28
    // 0x13bb580: SetupParameters()
    //     0x13bb580: ldr             x0, [fp, #0x10]
    //     0x13bb584: ldur            w2, [x0, #0x17]
    //     0x13bb588: add             x2, x2, HEAP, lsl #32
    //     0x13bb58c: stur            x2, [fp, #-8]
    // 0x13bb590: CheckStackOverflow
    //     0x13bb590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bb594: cmp             SP, x16
    //     0x13bb598: b.ls            #0x13bb654
    // 0x13bb59c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13bb59c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13bb5a0: ldr             x0, [x0, #0x1c80]
    //     0x13bb5a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13bb5a8: cmp             w0, w16
    //     0x13bb5ac: b.ne            #0x13bb5b8
    //     0x13bb5b0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13bb5b4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13bb5b8: str             NULL, [SP]
    // 0x13bb5bc: r4 = const [0x1, 0, 0, 0, null]
    //     0x13bb5bc: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x13bb5c0: r0 = GetNavigation.back()
    //     0x13bb5c0: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x13bb5c4: r1 = Null
    //     0x13bb5c4: mov             x1, NULL
    // 0x13bb5c8: r2 = 4
    //     0x13bb5c8: movz            x2, #0x4
    // 0x13bb5cc: r0 = AllocateArray()
    //     0x13bb5cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13bb5d0: r16 = "previousScreenSource"
    //     0x13bb5d0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x13bb5d4: ldr             x16, [x16, #0x448]
    // 0x13bb5d8: StoreField: r0->field_f = r16
    //     0x13bb5d8: stur            w16, [x0, #0xf]
    // 0x13bb5dc: r16 = "home_page"
    //     0x13bb5dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x13bb5e0: ldr             x16, [x16, #0xe60]
    // 0x13bb5e4: StoreField: r0->field_13 = r16
    //     0x13bb5e4: stur            w16, [x0, #0x13]
    // 0x13bb5e8: r16 = <String, String>
    //     0x13bb5e8: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x13bb5ec: ldr             x16, [x16, #0x788]
    // 0x13bb5f0: stp             x0, x16, [SP]
    // 0x13bb5f4: r0 = Map._fromLiteral()
    //     0x13bb5f4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13bb5f8: r16 = "/bag"
    //     0x13bb5f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x13bb5fc: ldr             x16, [x16, #0x468]
    // 0x13bb600: stp             x16, NULL, [SP, #8]
    // 0x13bb604: str             x0, [SP]
    // 0x13bb608: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13bb608: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13bb60c: ldr             x4, [x4, #0x438]
    // 0x13bb610: r0 = GetNavigation.toNamed()
    //     0x13bb610: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13bb614: stur            x0, [fp, #-0x10]
    // 0x13bb618: cmp             w0, NULL
    // 0x13bb61c: b.eq            #0x13bb644
    // 0x13bb620: ldur            x2, [fp, #-8]
    // 0x13bb624: r1 = Function '<anonymous closure>':.
    //     0x13bb624: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c3a8] AnonymousClosure: (0x13bb65c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x13bab7c)
    //     0x13bb628: ldr             x1, [x1, #0x3a8]
    // 0x13bb62c: r0 = AllocateClosure()
    //     0x13bb62c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bb630: ldur            x16, [fp, #-0x10]
    // 0x13bb634: stp             x16, NULL, [SP, #8]
    // 0x13bb638: str             x0, [SP]
    // 0x13bb63c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x13bb63c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x13bb640: r0 = then()
    //     0x13bb640: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x13bb644: r0 = Null
    //     0x13bb644: mov             x0, NULL
    // 0x13bb648: LeaveFrame
    //     0x13bb648: mov             SP, fp
    //     0x13bb64c: ldp             fp, lr, [SP], #0x10
    // 0x13bb650: ret
    //     0x13bb650: ret             
    // 0x13bb654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bb654: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bb658: b               #0x13bb59c
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x13bb65c, size: 0x50
    // 0x13bb65c: EnterFrame
    //     0x13bb65c: stp             fp, lr, [SP, #-0x10]!
    //     0x13bb660: mov             fp, SP
    // 0x13bb664: ldr             x0, [fp, #0x18]
    // 0x13bb668: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13bb668: ldur            w1, [x0, #0x17]
    // 0x13bb66c: DecompressPointer r1
    //     0x13bb66c: add             x1, x1, HEAP, lsl #32
    // 0x13bb670: CheckStackOverflow
    //     0x13bb670: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bb674: cmp             SP, x16
    //     0x13bb678: b.ls            #0x13bb6a4
    // 0x13bb67c: LoadField: r0 = r1->field_f
    //     0x13bb67c: ldur            w0, [x1, #0xf]
    // 0x13bb680: DecompressPointer r0
    //     0x13bb680: add             x0, x0, HEAP, lsl #32
    // 0x13bb684: mov             x1, x0
    // 0x13bb688: r0 = controller()
    //     0x13bb688: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bb68c: mov             x1, x0
    // 0x13bb690: r0 = getBagCount()
    //     0x13bb690: bl              #0x91e28c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getBagCount
    // 0x13bb694: r0 = Null
    //     0x13bb694: mov             x0, NULL
    // 0x13bb698: LeaveFrame
    //     0x13bb698: mov             SP, fp
    //     0x13bb69c: ldp             fp, lr, [SP], #0x10
    // 0x13bb6a0: ret
    //     0x13bb6a0: ret             
    // 0x13bb6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bb6a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bb6a8: b               #0x13bb67c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x148fba8, size: 0x50
    // 0x148fba8: EnterFrame
    //     0x148fba8: stp             fp, lr, [SP, #-0x10]!
    //     0x148fbac: mov             fp, SP
    // 0x148fbb0: ldr             x0, [fp, #0x10]
    // 0x148fbb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x148fbb4: ldur            w1, [x0, #0x17]
    // 0x148fbb8: DecompressPointer r1
    //     0x148fbb8: add             x1, x1, HEAP, lsl #32
    // 0x148fbbc: CheckStackOverflow
    //     0x148fbbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148fbc0: cmp             SP, x16
    //     0x148fbc4: b.ls            #0x148fbf0
    // 0x148fbc8: LoadField: r0 = r1->field_f
    //     0x148fbc8: ldur            w0, [x1, #0xf]
    // 0x148fbcc: DecompressPointer r0
    //     0x148fbcc: add             x0, x0, HEAP, lsl #32
    // 0x148fbd0: mov             x1, x0
    // 0x148fbd4: r0 = controller()
    //     0x148fbd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148fbd8: mov             x1, x0
    // 0x148fbdc: r0 = onLoadNextPage()
    //     0x148fbdc: bl              #0x148fbf8  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::onLoadNextPage
    // 0x148fbe0: r0 = Null
    //     0x148fbe0: mov             x0, NULL
    // 0x148fbe4: LeaveFrame
    //     0x148fbe4: mov             SP, fp
    //     0x148fbe8: ldp             fp, lr, [SP], #0x10
    // 0x148fbec: ret
    //     0x148fbec: ret             
    // 0x148fbf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148fbf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148fbf4: b               #0x148fbc8
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x148fc58, size: 0x64
    // 0x148fc58: EnterFrame
    //     0x148fc58: stp             fp, lr, [SP, #-0x10]!
    //     0x148fc5c: mov             fp, SP
    // 0x148fc60: AllocStack(0x10)
    //     0x148fc60: sub             SP, SP, #0x10
    // 0x148fc64: SetupParameters(CollectionPage this /* r1 */)
    //     0x148fc64: stur            NULL, [fp, #-8]
    //     0x148fc68: movz            x0, #0
    //     0x148fc6c: add             x1, fp, w0, sxtw #2
    //     0x148fc70: ldr             x1, [x1, #0x10]
    //     0x148fc74: ldur            w2, [x1, #0x17]
    //     0x148fc78: add             x2, x2, HEAP, lsl #32
    //     0x148fc7c: stur            x2, [fp, #-0x10]
    // 0x148fc80: CheckStackOverflow
    //     0x148fc80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148fc84: cmp             SP, x16
    //     0x148fc88: b.ls            #0x148fcb4
    // 0x148fc8c: InitAsync() -> Future<void?>
    //     0x148fc8c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x148fc90: bl              #0x6326e0  ; InitAsyncStub
    // 0x148fc94: ldur            x0, [fp, #-0x10]
    // 0x148fc98: LoadField: r1 = r0->field_f
    //     0x148fc98: ldur            w1, [x0, #0xf]
    // 0x148fc9c: DecompressPointer r1
    //     0x148fc9c: add             x1, x1, HEAP, lsl #32
    // 0x148fca0: r0 = controller()
    //     0x148fca0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x148fca4: mov             x1, x0
    // 0x148fca8: r0 = onRefreshPage()
    //     0x148fca8: bl              #0x13b5d28  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::onRefreshPage
    // 0x148fcac: r0 = Null
    //     0x148fcac: mov             x0, NULL
    // 0x148fcb0: r0 = ReturnAsyncNotFuture()
    //     0x148fcb0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x148fcb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148fcb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148fcb8: b               #0x148fc8c
  }
  _ body(/* No info */) {
    // ** addr: 0x1504b2c, size: 0xec
    // 0x1504b2c: EnterFrame
    //     0x1504b2c: stp             fp, lr, [SP, #-0x10]!
    //     0x1504b30: mov             fp, SP
    // 0x1504b34: AllocStack(0x20)
    //     0x1504b34: sub             SP, SP, #0x20
    // 0x1504b38: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1504b38: stur            x1, [fp, #-8]
    //     0x1504b3c: stur            x2, [fp, #-0x10]
    // 0x1504b40: CheckStackOverflow
    //     0x1504b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1504b44: cmp             SP, x16
    //     0x1504b48: b.ls            #0x1504c10
    // 0x1504b4c: r1 = 2
    //     0x1504b4c: movz            x1, #0x2
    // 0x1504b50: r0 = AllocateContext()
    //     0x1504b50: bl              #0x16f6108  ; AllocateContextStub
    // 0x1504b54: mov             x1, x0
    // 0x1504b58: ldur            x0, [fp, #-8]
    // 0x1504b5c: stur            x1, [fp, #-0x18]
    // 0x1504b60: StoreField: r1->field_f = r0
    //     0x1504b60: stur            w0, [x1, #0xf]
    // 0x1504b64: ldur            x0, [fp, #-0x10]
    // 0x1504b68: StoreField: r1->field_13 = r0
    //     0x1504b68: stur            w0, [x1, #0x13]
    // 0x1504b6c: r0 = Obx()
    //     0x1504b6c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1504b70: ldur            x2, [fp, #-0x18]
    // 0x1504b74: r1 = Function '<anonymous closure>':.
    //     0x1504b74: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bb68] AnonymousClosure: (0x13b3ed4), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x1504b78: ldr             x1, [x1, #0xb68]
    // 0x1504b7c: stur            x0, [fp, #-8]
    // 0x1504b80: r0 = AllocateClosure()
    //     0x1504b80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1504b84: mov             x1, x0
    // 0x1504b88: ldur            x0, [fp, #-8]
    // 0x1504b8c: StoreField: r0->field_b = r1
    //     0x1504b8c: stur            w1, [x0, #0xb]
    // 0x1504b90: ldur            x2, [fp, #-0x18]
    // 0x1504b94: r1 = Function '<anonymous closure>':.
    //     0x1504b94: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bb70] AnonymousClosure: (0x148fc58), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x1504b98: ldr             x1, [x1, #0xb70]
    // 0x1504b9c: r0 = AllocateClosure()
    //     0x1504b9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1504ba0: ldur            x2, [fp, #-0x18]
    // 0x1504ba4: r1 = Function '<anonymous closure>':.
    //     0x1504ba4: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bb78] AnonymousClosure: (0x148fba8), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x1504ba8: ldr             x1, [x1, #0xb78]
    // 0x1504bac: stur            x0, [fp, #-0x10]
    // 0x1504bb0: r0 = AllocateClosure()
    //     0x1504bb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1504bb4: stur            x0, [fp, #-0x20]
    // 0x1504bb8: r0 = PagingView()
    //     0x1504bb8: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x1504bbc: mov             x1, x0
    // 0x1504bc0: ldur            x2, [fp, #-8]
    // 0x1504bc4: ldur            x3, [fp, #-0x20]
    // 0x1504bc8: ldur            x5, [fp, #-0x10]
    // 0x1504bcc: stur            x0, [fp, #-8]
    // 0x1504bd0: r0 = PagingView()
    //     0x1504bd0: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x1504bd4: r0 = WillPopScope()
    //     0x1504bd4: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1504bd8: mov             x3, x0
    // 0x1504bdc: ldur            x0, [fp, #-8]
    // 0x1504be0: stur            x3, [fp, #-0x10]
    // 0x1504be4: StoreField: r3->field_b = r0
    //     0x1504be4: stur            w0, [x3, #0xb]
    // 0x1504be8: ldur            x2, [fp, #-0x18]
    // 0x1504bec: r1 = Function '<anonymous closure>':.
    //     0x1504bec: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bb80] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x1504bf0: ldr             x1, [x1, #0xb80]
    // 0x1504bf4: r0 = AllocateClosure()
    //     0x1504bf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1504bf8: mov             x1, x0
    // 0x1504bfc: ldur            x0, [fp, #-0x10]
    // 0x1504c00: StoreField: r0->field_f = r1
    //     0x1504c00: stur            w1, [x0, #0xf]
    // 0x1504c04: LeaveFrame
    //     0x1504c04: mov             SP, fp
    //     0x1504c08: ldp             fp, lr, [SP], #0x10
    // 0x1504c0c: ret
    //     0x1504c0c: ret             
    // 0x1504c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1504c10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1504c14: b               #0x1504b4c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15cae64, size: 0x60
    // 0x15cae64: EnterFrame
    //     0x15cae64: stp             fp, lr, [SP, #-0x10]!
    //     0x15cae68: mov             fp, SP
    // 0x15cae6c: AllocStack(0x10)
    //     0x15cae6c: sub             SP, SP, #0x10
    // 0x15cae70: CheckStackOverflow
    //     0x15cae70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cae74: cmp             SP, x16
    //     0x15cae78: b.ls            #0x15caebc
    // 0x15cae7c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cae7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cae80: ldr             x0, [x0, #0x1c80]
    //     0x15cae84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cae88: cmp             w0, w16
    //     0x15cae8c: b.ne            #0x15cae98
    //     0x15cae90: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cae94: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cae98: r16 = "/bag"
    //     0x15cae98: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15cae9c: ldr             x16, [x16, #0x468]
    // 0x15caea0: stp             x16, NULL, [SP]
    // 0x15caea4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15caea4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15caea8: r0 = GetNavigation.toNamed()
    //     0x15caea8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15caeac: r0 = Null
    //     0x15caeac: mov             x0, NULL
    // 0x15caeb0: LeaveFrame
    //     0x15caeb0: mov             SP, fp
    //     0x15caeb4: ldp             fp, lr, [SP], #0x10
    // 0x15caeb8: ret
    //     0x15caeb8: ret             
    // 0x15caebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15caebc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15caec0: b               #0x15cae7c
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15caec4, size: 0x300
    // 0x15caec4: EnterFrame
    //     0x15caec4: stp             fp, lr, [SP, #-0x10]!
    //     0x15caec8: mov             fp, SP
    // 0x15caecc: AllocStack(0x58)
    //     0x15caecc: sub             SP, SP, #0x58
    // 0x15caed0: SetupParameters()
    //     0x15caed0: ldr             x0, [fp, #0x10]
    //     0x15caed4: ldur            w2, [x0, #0x17]
    //     0x15caed8: add             x2, x2, HEAP, lsl #32
    //     0x15caedc: stur            x2, [fp, #-8]
    // 0x15caee0: CheckStackOverflow
    //     0x15caee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15caee4: cmp             SP, x16
    //     0x15caee8: b.ls            #0x15cb1bc
    // 0x15caeec: LoadField: r1 = r2->field_f
    //     0x15caeec: ldur            w1, [x2, #0xf]
    // 0x15caef0: DecompressPointer r1
    //     0x15caef0: add             x1, x1, HEAP, lsl #32
    // 0x15caef4: r0 = controller()
    //     0x15caef4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15caef8: LoadField: r1 = r0->field_7f
    //     0x15caef8: ldur            w1, [x0, #0x7f]
    // 0x15caefc: DecompressPointer r1
    //     0x15caefc: add             x1, x1, HEAP, lsl #32
    // 0x15caf00: r0 = value()
    //     0x15caf00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15caf04: LoadField: r1 = r0->field_1f
    //     0x15caf04: ldur            w1, [x0, #0x1f]
    // 0x15caf08: DecompressPointer r1
    //     0x15caf08: add             x1, x1, HEAP, lsl #32
    // 0x15caf0c: cmp             w1, NULL
    // 0x15caf10: b.ne            #0x15caf1c
    // 0x15caf14: r0 = Null
    //     0x15caf14: mov             x0, NULL
    // 0x15caf18: b               #0x15caf24
    // 0x15caf1c: LoadField: r0 = r1->field_7
    //     0x15caf1c: ldur            w0, [x1, #7]
    // 0x15caf20: DecompressPointer r0
    //     0x15caf20: add             x0, x0, HEAP, lsl #32
    // 0x15caf24: cmp             w0, NULL
    // 0x15caf28: b.ne            #0x15caf34
    // 0x15caf2c: r0 = false
    //     0x15caf2c: add             x0, NULL, #0x30  ; false
    // 0x15caf30: b               #0x15cb124
    // 0x15caf34: tbnz            w0, #4, #0x15cb120
    // 0x15caf38: ldur            x0, [fp, #-8]
    // 0x15caf3c: LoadField: r1 = r0->field_f
    //     0x15caf3c: ldur            w1, [x0, #0xf]
    // 0x15caf40: DecompressPointer r1
    //     0x15caf40: add             x1, x1, HEAP, lsl #32
    // 0x15caf44: r0 = controller()
    //     0x15caf44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15caf48: mov             x1, x0
    // 0x15caf4c: r0 = offerParams()
    //     0x15caf4c: bl              #0x12e3b98  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_variants_controller.dart] CheckoutVariantsController::offerParams
    // 0x15caf50: mov             x2, x0
    // 0x15caf54: ldur            x0, [fp, #-8]
    // 0x15caf58: stur            x2, [fp, #-0x10]
    // 0x15caf5c: LoadField: r1 = r0->field_13
    //     0x15caf5c: ldur            w1, [x0, #0x13]
    // 0x15caf60: DecompressPointer r1
    //     0x15caf60: add             x1, x1, HEAP, lsl #32
    // 0x15caf64: r0 = of()
    //     0x15caf64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15caf68: LoadField: r2 = r0->field_5b
    //     0x15caf68: ldur            w2, [x0, #0x5b]
    // 0x15caf6c: DecompressPointer r2
    //     0x15caf6c: add             x2, x2, HEAP, lsl #32
    // 0x15caf70: ldur            x0, [fp, #-8]
    // 0x15caf74: stur            x2, [fp, #-0x18]
    // 0x15caf78: LoadField: r1 = r0->field_f
    //     0x15caf78: ldur            w1, [x0, #0xf]
    // 0x15caf7c: DecompressPointer r1
    //     0x15caf7c: add             x1, x1, HEAP, lsl #32
    // 0x15caf80: r0 = controller()
    //     0x15caf80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15caf84: LoadField: r1 = r0->field_df
    //     0x15caf84: ldur            w1, [x0, #0xdf]
    // 0x15caf88: DecompressPointer r1
    //     0x15caf88: add             x1, x1, HEAP, lsl #32
    // 0x15caf8c: r0 = value()
    //     0x15caf8c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15caf90: cmp             w0, NULL
    // 0x15caf94: r16 = true
    //     0x15caf94: add             x16, NULL, #0x20  ; true
    // 0x15caf98: r17 = false
    //     0x15caf98: add             x17, NULL, #0x30  ; false
    // 0x15caf9c: csel            x2, x16, x17, ne
    // 0x15cafa0: ldur            x0, [fp, #-8]
    // 0x15cafa4: stur            x2, [fp, #-0x20]
    // 0x15cafa8: LoadField: r1 = r0->field_f
    //     0x15cafa8: ldur            w1, [x0, #0xf]
    // 0x15cafac: DecompressPointer r1
    //     0x15cafac: add             x1, x1, HEAP, lsl #32
    // 0x15cafb0: r0 = controller()
    //     0x15cafb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cafb4: LoadField: r1 = r0->field_df
    //     0x15cafb4: ldur            w1, [x0, #0xdf]
    // 0x15cafb8: DecompressPointer r1
    //     0x15cafb8: add             x1, x1, HEAP, lsl #32
    // 0x15cafbc: r0 = value()
    //     0x15cafbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cafc0: str             x0, [SP]
    // 0x15cafc4: r0 = _interpolateSingle()
    //     0x15cafc4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15cafc8: mov             x2, x0
    // 0x15cafcc: ldur            x0, [fp, #-8]
    // 0x15cafd0: stur            x2, [fp, #-0x28]
    // 0x15cafd4: LoadField: r1 = r0->field_13
    //     0x15cafd4: ldur            w1, [x0, #0x13]
    // 0x15cafd8: DecompressPointer r1
    //     0x15cafd8: add             x1, x1, HEAP, lsl #32
    // 0x15cafdc: r0 = of()
    //     0x15cafdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cafe0: LoadField: r1 = r0->field_87
    //     0x15cafe0: ldur            w1, [x0, #0x87]
    // 0x15cafe4: DecompressPointer r1
    //     0x15cafe4: add             x1, x1, HEAP, lsl #32
    // 0x15cafe8: LoadField: r0 = r1->field_27
    //     0x15cafe8: ldur            w0, [x1, #0x27]
    // 0x15cafec: DecompressPointer r0
    //     0x15cafec: add             x0, x0, HEAP, lsl #32
    // 0x15caff0: r16 = Instance_Color
    //     0x15caff0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15caff4: str             x16, [SP]
    // 0x15caff8: mov             x1, x0
    // 0x15caffc: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15caffc: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15cb000: ldr             x4, [x4, #0xf40]
    // 0x15cb004: r0 = copyWith()
    //     0x15cb004: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cb008: stur            x0, [fp, #-0x30]
    // 0x15cb00c: r0 = Text()
    //     0x15cb00c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cb010: mov             x2, x0
    // 0x15cb014: ldur            x0, [fp, #-0x28]
    // 0x15cb018: stur            x2, [fp, #-0x38]
    // 0x15cb01c: StoreField: r2->field_b = r0
    //     0x15cb01c: stur            w0, [x2, #0xb]
    // 0x15cb020: ldur            x0, [fp, #-0x30]
    // 0x15cb024: StoreField: r2->field_13 = r0
    //     0x15cb024: stur            w0, [x2, #0x13]
    // 0x15cb028: ldur            x0, [fp, #-8]
    // 0x15cb02c: LoadField: r1 = r0->field_13
    //     0x15cb02c: ldur            w1, [x0, #0x13]
    // 0x15cb030: DecompressPointer r1
    //     0x15cb030: add             x1, x1, HEAP, lsl #32
    // 0x15cb034: r0 = of()
    //     0x15cb034: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cb038: LoadField: r1 = r0->field_5b
    //     0x15cb038: ldur            w1, [x0, #0x5b]
    // 0x15cb03c: DecompressPointer r1
    //     0x15cb03c: add             x1, x1, HEAP, lsl #32
    // 0x15cb040: stur            x1, [fp, #-8]
    // 0x15cb044: r0 = ColorFilter()
    //     0x15cb044: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15cb048: mov             x1, x0
    // 0x15cb04c: ldur            x0, [fp, #-8]
    // 0x15cb050: stur            x1, [fp, #-0x28]
    // 0x15cb054: StoreField: r1->field_7 = r0
    //     0x15cb054: stur            w0, [x1, #7]
    // 0x15cb058: r0 = Instance_BlendMode
    //     0x15cb058: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15cb05c: ldr             x0, [x0, #0xb30]
    // 0x15cb060: StoreField: r1->field_b = r0
    //     0x15cb060: stur            w0, [x1, #0xb]
    // 0x15cb064: r0 = 1
    //     0x15cb064: movz            x0, #0x1
    // 0x15cb068: StoreField: r1->field_13 = r0
    //     0x15cb068: stur            x0, [x1, #0x13]
    // 0x15cb06c: r0 = SvgPicture()
    //     0x15cb06c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15cb070: stur            x0, [fp, #-8]
    // 0x15cb074: r16 = Instance_BoxFit
    //     0x15cb074: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cb078: ldr             x16, [x16, #0xb18]
    // 0x15cb07c: r30 = 24.000000
    //     0x15cb07c: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15cb080: ldr             lr, [lr, #0xba8]
    // 0x15cb084: stp             lr, x16, [SP, #0x10]
    // 0x15cb088: r16 = 24.000000
    //     0x15cb088: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15cb08c: ldr             x16, [x16, #0xba8]
    // 0x15cb090: ldur            lr, [fp, #-0x28]
    // 0x15cb094: stp             lr, x16, [SP]
    // 0x15cb098: mov             x1, x0
    // 0x15cb09c: r2 = "assets/images/shopping_bag.svg"
    //     0x15cb09c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15cb0a0: ldr             x2, [x2, #0xa60]
    // 0x15cb0a4: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15cb0a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15cb0a8: ldr             x4, [x4, #0xa68]
    // 0x15cb0ac: r0 = SvgPicture.asset()
    //     0x15cb0ac: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15cb0b0: r0 = Badge()
    //     0x15cb0b0: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15cb0b4: mov             x1, x0
    // 0x15cb0b8: ldur            x0, [fp, #-0x18]
    // 0x15cb0bc: stur            x1, [fp, #-0x28]
    // 0x15cb0c0: StoreField: r1->field_b = r0
    //     0x15cb0c0: stur            w0, [x1, #0xb]
    // 0x15cb0c4: ldur            x0, [fp, #-0x38]
    // 0x15cb0c8: StoreField: r1->field_27 = r0
    //     0x15cb0c8: stur            w0, [x1, #0x27]
    // 0x15cb0cc: ldur            x0, [fp, #-0x20]
    // 0x15cb0d0: StoreField: r1->field_2b = r0
    //     0x15cb0d0: stur            w0, [x1, #0x2b]
    // 0x15cb0d4: ldur            x0, [fp, #-8]
    // 0x15cb0d8: StoreField: r1->field_2f = r0
    //     0x15cb0d8: stur            w0, [x1, #0x2f]
    // 0x15cb0dc: r0 = Visibility()
    //     0x15cb0dc: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cb0e0: mov             x1, x0
    // 0x15cb0e4: ldur            x0, [fp, #-0x28]
    // 0x15cb0e8: StoreField: r1->field_b = r0
    //     0x15cb0e8: stur            w0, [x1, #0xb]
    // 0x15cb0ec: r0 = Instance_SizedBox
    //     0x15cb0ec: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cb0f0: StoreField: r1->field_f = r0
    //     0x15cb0f0: stur            w0, [x1, #0xf]
    // 0x15cb0f4: ldur            x0, [fp, #-0x10]
    // 0x15cb0f8: StoreField: r1->field_13 = r0
    //     0x15cb0f8: stur            w0, [x1, #0x13]
    // 0x15cb0fc: r0 = false
    //     0x15cb0fc: add             x0, NULL, #0x30  ; false
    // 0x15cb100: ArrayStore: r1[0] = r0  ; List_4
    //     0x15cb100: stur            w0, [x1, #0x17]
    // 0x15cb104: StoreField: r1->field_1b = r0
    //     0x15cb104: stur            w0, [x1, #0x1b]
    // 0x15cb108: StoreField: r1->field_1f = r0
    //     0x15cb108: stur            w0, [x1, #0x1f]
    // 0x15cb10c: StoreField: r1->field_23 = r0
    //     0x15cb10c: stur            w0, [x1, #0x23]
    // 0x15cb110: StoreField: r1->field_27 = r0
    //     0x15cb110: stur            w0, [x1, #0x27]
    // 0x15cb114: StoreField: r1->field_2b = r0
    //     0x15cb114: stur            w0, [x1, #0x2b]
    // 0x15cb118: mov             x0, x1
    // 0x15cb11c: b               #0x15cb13c
    // 0x15cb120: r0 = false
    //     0x15cb120: add             x0, NULL, #0x30  ; false
    // 0x15cb124: r0 = Container()
    //     0x15cb124: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15cb128: mov             x1, x0
    // 0x15cb12c: stur            x0, [fp, #-8]
    // 0x15cb130: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15cb130: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15cb134: r0 = Container()
    //     0x15cb134: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15cb138: ldur            x0, [fp, #-8]
    // 0x15cb13c: stur            x0, [fp, #-8]
    // 0x15cb140: r0 = InkWell()
    //     0x15cb140: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15cb144: mov             x3, x0
    // 0x15cb148: ldur            x0, [fp, #-8]
    // 0x15cb14c: stur            x3, [fp, #-0x10]
    // 0x15cb150: StoreField: r3->field_b = r0
    //     0x15cb150: stur            w0, [x3, #0xb]
    // 0x15cb154: r1 = Function '<anonymous closure>':.
    //     0x15cb154: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c470] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15cb158: ldr             x1, [x1, #0x470]
    // 0x15cb15c: r2 = Null
    //     0x15cb15c: mov             x2, NULL
    // 0x15cb160: r0 = AllocateClosure()
    //     0x15cb160: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15cb164: mov             x1, x0
    // 0x15cb168: ldur            x0, [fp, #-0x10]
    // 0x15cb16c: StoreField: r0->field_f = r1
    //     0x15cb16c: stur            w1, [x0, #0xf]
    // 0x15cb170: r1 = true
    //     0x15cb170: add             x1, NULL, #0x20  ; true
    // 0x15cb174: StoreField: r0->field_43 = r1
    //     0x15cb174: stur            w1, [x0, #0x43]
    // 0x15cb178: r2 = Instance_BoxShape
    //     0x15cb178: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15cb17c: ldr             x2, [x2, #0x80]
    // 0x15cb180: StoreField: r0->field_47 = r2
    //     0x15cb180: stur            w2, [x0, #0x47]
    // 0x15cb184: StoreField: r0->field_6f = r1
    //     0x15cb184: stur            w1, [x0, #0x6f]
    // 0x15cb188: r2 = false
    //     0x15cb188: add             x2, NULL, #0x30  ; false
    // 0x15cb18c: StoreField: r0->field_73 = r2
    //     0x15cb18c: stur            w2, [x0, #0x73]
    // 0x15cb190: StoreField: r0->field_83 = r1
    //     0x15cb190: stur            w1, [x0, #0x83]
    // 0x15cb194: StoreField: r0->field_7b = r2
    //     0x15cb194: stur            w2, [x0, #0x7b]
    // 0x15cb198: r0 = Padding()
    //     0x15cb198: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15cb19c: r1 = Instance_EdgeInsets
    //     0x15cb19c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15cb1a0: ldr             x1, [x1, #0xa78]
    // 0x15cb1a4: StoreField: r0->field_f = r1
    //     0x15cb1a4: stur            w1, [x0, #0xf]
    // 0x15cb1a8: ldur            x1, [fp, #-0x10]
    // 0x15cb1ac: StoreField: r0->field_b = r1
    //     0x15cb1ac: stur            w1, [x0, #0xb]
    // 0x15cb1b0: LeaveFrame
    //     0x15cb1b0: mov             SP, fp
    //     0x15cb1b4: ldp             fp, lr, [SP], #0x10
    // 0x15cb1b8: ret
    //     0x15cb1b8: ret             
    // 0x15cb1bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cb1bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cb1c0: b               #0x15caeec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15cee30, size: 0xcc
    // 0x15cee30: EnterFrame
    //     0x15cee30: stp             fp, lr, [SP, #-0x10]!
    //     0x15cee34: mov             fp, SP
    // 0x15cee38: AllocStack(0x18)
    //     0x15cee38: sub             SP, SP, #0x18
    // 0x15cee3c: SetupParameters()
    //     0x15cee3c: ldr             x0, [fp, #0x10]
    //     0x15cee40: ldur            w2, [x0, #0x17]
    //     0x15cee44: add             x2, x2, HEAP, lsl #32
    //     0x15cee48: stur            x2, [fp, #-8]
    // 0x15cee4c: CheckStackOverflow
    //     0x15cee4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cee50: cmp             SP, x16
    //     0x15cee54: b.ls            #0x15ceef4
    // 0x15cee58: LoadField: r1 = r2->field_f
    //     0x15cee58: ldur            w1, [x2, #0xf]
    // 0x15cee5c: DecompressPointer r1
    //     0x15cee5c: add             x1, x1, HEAP, lsl #32
    // 0x15cee60: r0 = controller()
    //     0x15cee60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cee64: LoadField: r1 = r0->field_d7
    //     0x15cee64: ldur            w1, [x0, #0xd7]
    // 0x15cee68: DecompressPointer r1
    //     0x15cee68: add             x1, x1, HEAP, lsl #32
    // 0x15cee6c: r0 = value()
    //     0x15cee6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cee70: tbnz            w0, #4, #0x15ceea8
    // 0x15cee74: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cee74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cee78: ldr             x0, [x0, #0x1c80]
    //     0x15cee7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cee80: cmp             w0, w16
    //     0x15cee84: b.ne            #0x15cee90
    //     0x15cee88: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cee8c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cee90: r16 = "/search"
    //     0x15cee90: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15cee94: ldr             x16, [x16, #0x838]
    // 0x15cee98: stp             x16, NULL, [SP]
    // 0x15cee9c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15cee9c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15ceea0: r0 = GetNavigation.toNamed()
    //     0x15ceea0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15ceea4: b               #0x15ceee4
    // 0x15ceea8: ldur            x0, [fp, #-8]
    // 0x15ceeac: LoadField: r1 = r0->field_f
    //     0x15ceeac: ldur            w1, [x0, #0xf]
    // 0x15ceeb0: DecompressPointer r1
    //     0x15ceeb0: add             x1, x1, HEAP, lsl #32
    // 0x15ceeb4: r2 = false
    //     0x15ceeb4: add             x2, NULL, #0x30  ; false
    // 0x15ceeb8: r0 = showLoading()
    //     0x15ceeb8: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15ceebc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15ceebc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15ceec0: ldr             x0, [x0, #0x1c80]
    //     0x15ceec4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15ceec8: cmp             w0, w16
    //     0x15ceecc: b.ne            #0x15ceed8
    //     0x15ceed0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15ceed4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15ceed8: str             NULL, [SP]
    // 0x15ceedc: r4 = const [0x1, 0, 0, 0, null]
    //     0x15ceedc: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15ceee0: r0 = GetNavigation.back()
    //     0x15ceee0: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15ceee4: r0 = Null
    //     0x15ceee4: mov             x0, NULL
    // 0x15ceee8: LeaveFrame
    //     0x15ceee8: mov             SP, fp
    //     0x15ceeec: ldp             fp, lr, [SP], #0x10
    // 0x15ceef0: ret
    //     0x15ceef0: ret             
    // 0x15ceef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ceef4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ceef8: b               #0x15cee58
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15ceefc, size: 0x42c
    // 0x15ceefc: EnterFrame
    //     0x15ceefc: stp             fp, lr, [SP, #-0x10]!
    //     0x15cef00: mov             fp, SP
    // 0x15cef04: AllocStack(0x48)
    //     0x15cef04: sub             SP, SP, #0x48
    // 0x15cef08: SetupParameters()
    //     0x15cef08: ldr             x0, [fp, #0x10]
    //     0x15cef0c: ldur            w2, [x0, #0x17]
    //     0x15cef10: add             x2, x2, HEAP, lsl #32
    //     0x15cef14: stur            x2, [fp, #-8]
    // 0x15cef18: CheckStackOverflow
    //     0x15cef18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cef1c: cmp             SP, x16
    //     0x15cef20: b.ls            #0x15cf320
    // 0x15cef24: LoadField: r1 = r2->field_f
    //     0x15cef24: ldur            w1, [x2, #0xf]
    // 0x15cef28: DecompressPointer r1
    //     0x15cef28: add             x1, x1, HEAP, lsl #32
    // 0x15cef2c: r0 = controller()
    //     0x15cef2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cef30: LoadField: r1 = r0->field_7f
    //     0x15cef30: ldur            w1, [x0, #0x7f]
    // 0x15cef34: DecompressPointer r1
    //     0x15cef34: add             x1, x1, HEAP, lsl #32
    // 0x15cef38: r0 = value()
    //     0x15cef38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cef3c: LoadField: r1 = r0->field_3f
    //     0x15cef3c: ldur            w1, [x0, #0x3f]
    // 0x15cef40: DecompressPointer r1
    //     0x15cef40: add             x1, x1, HEAP, lsl #32
    // 0x15cef44: cmp             w1, NULL
    // 0x15cef48: b.ne            #0x15cef54
    // 0x15cef4c: r0 = Null
    //     0x15cef4c: mov             x0, NULL
    // 0x15cef50: b               #0x15cef5c
    // 0x15cef54: LoadField: r0 = r1->field_f
    //     0x15cef54: ldur            w0, [x1, #0xf]
    // 0x15cef58: DecompressPointer r0
    //     0x15cef58: add             x0, x0, HEAP, lsl #32
    // 0x15cef5c: r1 = LoadClassIdInstr(r0)
    //     0x15cef5c: ldur            x1, [x0, #-1]
    //     0x15cef60: ubfx            x1, x1, #0xc, #0x14
    // 0x15cef64: r16 = "image_text"
    //     0x15cef64: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cef68: ldr             x16, [x16, #0xa88]
    // 0x15cef6c: stp             x16, x0, [SP]
    // 0x15cef70: mov             x0, x1
    // 0x15cef74: mov             lr, x0
    // 0x15cef78: ldr             lr, [x21, lr, lsl #3]
    // 0x15cef7c: blr             lr
    // 0x15cef80: tbnz            w0, #4, #0x15cef8c
    // 0x15cef84: r2 = true
    //     0x15cef84: add             x2, NULL, #0x20  ; true
    // 0x15cef88: b               #0x15cefec
    // 0x15cef8c: ldur            x0, [fp, #-8]
    // 0x15cef90: LoadField: r1 = r0->field_f
    //     0x15cef90: ldur            w1, [x0, #0xf]
    // 0x15cef94: DecompressPointer r1
    //     0x15cef94: add             x1, x1, HEAP, lsl #32
    // 0x15cef98: r0 = controller()
    //     0x15cef98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cef9c: LoadField: r1 = r0->field_7f
    //     0x15cef9c: ldur            w1, [x0, #0x7f]
    // 0x15cefa0: DecompressPointer r1
    //     0x15cefa0: add             x1, x1, HEAP, lsl #32
    // 0x15cefa4: r0 = value()
    //     0x15cefa4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cefa8: LoadField: r1 = r0->field_3f
    //     0x15cefa8: ldur            w1, [x0, #0x3f]
    // 0x15cefac: DecompressPointer r1
    //     0x15cefac: add             x1, x1, HEAP, lsl #32
    // 0x15cefb0: cmp             w1, NULL
    // 0x15cefb4: b.ne            #0x15cefc0
    // 0x15cefb8: r0 = Null
    //     0x15cefb8: mov             x0, NULL
    // 0x15cefbc: b               #0x15cefc8
    // 0x15cefc0: LoadField: r0 = r1->field_f
    //     0x15cefc0: ldur            w0, [x1, #0xf]
    // 0x15cefc4: DecompressPointer r0
    //     0x15cefc4: add             x0, x0, HEAP, lsl #32
    // 0x15cefc8: r1 = LoadClassIdInstr(r0)
    //     0x15cefc8: ldur            x1, [x0, #-1]
    //     0x15cefcc: ubfx            x1, x1, #0xc, #0x14
    // 0x15cefd0: r16 = "image"
    //     0x15cefd0: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15cefd4: stp             x16, x0, [SP]
    // 0x15cefd8: mov             x0, x1
    // 0x15cefdc: mov             lr, x0
    // 0x15cefe0: ldr             lr, [x21, lr, lsl #3]
    // 0x15cefe4: blr             lr
    // 0x15cefe8: mov             x2, x0
    // 0x15cefec: ldur            x0, [fp, #-8]
    // 0x15ceff0: stur            x2, [fp, #-0x10]
    // 0x15ceff4: LoadField: r1 = r0->field_f
    //     0x15ceff4: ldur            w1, [x0, #0xf]
    // 0x15ceff8: DecompressPointer r1
    //     0x15ceff8: add             x1, x1, HEAP, lsl #32
    // 0x15ceffc: r0 = controller()
    //     0x15ceffc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf000: LoadField: r1 = r0->field_7f
    //     0x15cf000: ldur            w1, [x0, #0x7f]
    // 0x15cf004: DecompressPointer r1
    //     0x15cf004: add             x1, x1, HEAP, lsl #32
    // 0x15cf008: r0 = value()
    //     0x15cf008: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf00c: LoadField: r1 = r0->field_27
    //     0x15cf00c: ldur            w1, [x0, #0x27]
    // 0x15cf010: DecompressPointer r1
    //     0x15cf010: add             x1, x1, HEAP, lsl #32
    // 0x15cf014: cmp             w1, NULL
    // 0x15cf018: b.ne            #0x15cf024
    // 0x15cf01c: r2 = ""
    //     0x15cf01c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cf020: b               #0x15cf028
    // 0x15cf024: mov             x2, x1
    // 0x15cf028: ldur            x0, [fp, #-8]
    // 0x15cf02c: ldur            x1, [fp, #-0x10]
    // 0x15cf030: stur            x2, [fp, #-0x18]
    // 0x15cf034: r0 = ImageHeaders.forImages()
    //     0x15cf034: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15cf038: stur            x0, [fp, #-0x20]
    // 0x15cf03c: r0 = CachedNetworkImage()
    //     0x15cf03c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15cf040: stur            x0, [fp, #-0x28]
    // 0x15cf044: ldur            x16, [fp, #-0x20]
    // 0x15cf048: r30 = Instance_BoxFit
    //     0x15cf048: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cf04c: ldr             lr, [lr, #0xb18]
    // 0x15cf050: stp             lr, x16, [SP, #0x10]
    // 0x15cf054: r16 = 50.000000
    //     0x15cf054: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cf058: ldr             x16, [x16, #0xa90]
    // 0x15cf05c: r30 = 50.000000
    //     0x15cf05c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cf060: ldr             lr, [lr, #0xa90]
    // 0x15cf064: stp             lr, x16, [SP]
    // 0x15cf068: mov             x1, x0
    // 0x15cf06c: ldur            x2, [fp, #-0x18]
    // 0x15cf070: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15cf070: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15cf074: ldr             x4, [x4, #0xa98]
    // 0x15cf078: r0 = CachedNetworkImage()
    //     0x15cf078: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15cf07c: r0 = Visibility()
    //     0x15cf07c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cf080: mov             x2, x0
    // 0x15cf084: ldur            x0, [fp, #-0x28]
    // 0x15cf088: stur            x2, [fp, #-0x18]
    // 0x15cf08c: StoreField: r2->field_b = r0
    //     0x15cf08c: stur            w0, [x2, #0xb]
    // 0x15cf090: r0 = Instance_SizedBox
    //     0x15cf090: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cf094: StoreField: r2->field_f = r0
    //     0x15cf094: stur            w0, [x2, #0xf]
    // 0x15cf098: ldur            x1, [fp, #-0x10]
    // 0x15cf09c: StoreField: r2->field_13 = r1
    //     0x15cf09c: stur            w1, [x2, #0x13]
    // 0x15cf0a0: r3 = false
    //     0x15cf0a0: add             x3, NULL, #0x30  ; false
    // 0x15cf0a4: ArrayStore: r2[0] = r3  ; List_4
    //     0x15cf0a4: stur            w3, [x2, #0x17]
    // 0x15cf0a8: StoreField: r2->field_1b = r3
    //     0x15cf0a8: stur            w3, [x2, #0x1b]
    // 0x15cf0ac: StoreField: r2->field_1f = r3
    //     0x15cf0ac: stur            w3, [x2, #0x1f]
    // 0x15cf0b0: StoreField: r2->field_23 = r3
    //     0x15cf0b0: stur            w3, [x2, #0x23]
    // 0x15cf0b4: StoreField: r2->field_27 = r3
    //     0x15cf0b4: stur            w3, [x2, #0x27]
    // 0x15cf0b8: StoreField: r2->field_2b = r3
    //     0x15cf0b8: stur            w3, [x2, #0x2b]
    // 0x15cf0bc: ldur            x4, [fp, #-8]
    // 0x15cf0c0: LoadField: r1 = r4->field_f
    //     0x15cf0c0: ldur            w1, [x4, #0xf]
    // 0x15cf0c4: DecompressPointer r1
    //     0x15cf0c4: add             x1, x1, HEAP, lsl #32
    // 0x15cf0c8: r0 = controller()
    //     0x15cf0c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf0cc: LoadField: r1 = r0->field_7f
    //     0x15cf0cc: ldur            w1, [x0, #0x7f]
    // 0x15cf0d0: DecompressPointer r1
    //     0x15cf0d0: add             x1, x1, HEAP, lsl #32
    // 0x15cf0d4: r0 = value()
    //     0x15cf0d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf0d8: LoadField: r1 = r0->field_3f
    //     0x15cf0d8: ldur            w1, [x0, #0x3f]
    // 0x15cf0dc: DecompressPointer r1
    //     0x15cf0dc: add             x1, x1, HEAP, lsl #32
    // 0x15cf0e0: cmp             w1, NULL
    // 0x15cf0e4: b.ne            #0x15cf0f0
    // 0x15cf0e8: r0 = Null
    //     0x15cf0e8: mov             x0, NULL
    // 0x15cf0ec: b               #0x15cf0f8
    // 0x15cf0f0: LoadField: r0 = r1->field_f
    //     0x15cf0f0: ldur            w0, [x1, #0xf]
    // 0x15cf0f4: DecompressPointer r0
    //     0x15cf0f4: add             x0, x0, HEAP, lsl #32
    // 0x15cf0f8: r1 = LoadClassIdInstr(r0)
    //     0x15cf0f8: ldur            x1, [x0, #-1]
    //     0x15cf0fc: ubfx            x1, x1, #0xc, #0x14
    // 0x15cf100: r16 = "image_text"
    //     0x15cf100: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cf104: ldr             x16, [x16, #0xa88]
    // 0x15cf108: stp             x16, x0, [SP]
    // 0x15cf10c: mov             x0, x1
    // 0x15cf110: mov             lr, x0
    // 0x15cf114: ldr             lr, [x21, lr, lsl #3]
    // 0x15cf118: blr             lr
    // 0x15cf11c: tbnz            w0, #4, #0x15cf128
    // 0x15cf120: r2 = true
    //     0x15cf120: add             x2, NULL, #0x20  ; true
    // 0x15cf124: b               #0x15cf188
    // 0x15cf128: ldur            x0, [fp, #-8]
    // 0x15cf12c: LoadField: r1 = r0->field_f
    //     0x15cf12c: ldur            w1, [x0, #0xf]
    // 0x15cf130: DecompressPointer r1
    //     0x15cf130: add             x1, x1, HEAP, lsl #32
    // 0x15cf134: r0 = controller()
    //     0x15cf134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf138: LoadField: r1 = r0->field_7f
    //     0x15cf138: ldur            w1, [x0, #0x7f]
    // 0x15cf13c: DecompressPointer r1
    //     0x15cf13c: add             x1, x1, HEAP, lsl #32
    // 0x15cf140: r0 = value()
    //     0x15cf140: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf144: LoadField: r1 = r0->field_3f
    //     0x15cf144: ldur            w1, [x0, #0x3f]
    // 0x15cf148: DecompressPointer r1
    //     0x15cf148: add             x1, x1, HEAP, lsl #32
    // 0x15cf14c: cmp             w1, NULL
    // 0x15cf150: b.ne            #0x15cf15c
    // 0x15cf154: r0 = Null
    //     0x15cf154: mov             x0, NULL
    // 0x15cf158: b               #0x15cf164
    // 0x15cf15c: LoadField: r0 = r1->field_f
    //     0x15cf15c: ldur            w0, [x1, #0xf]
    // 0x15cf160: DecompressPointer r0
    //     0x15cf160: add             x0, x0, HEAP, lsl #32
    // 0x15cf164: r1 = LoadClassIdInstr(r0)
    //     0x15cf164: ldur            x1, [x0, #-1]
    //     0x15cf168: ubfx            x1, x1, #0xc, #0x14
    // 0x15cf16c: r16 = "text"
    //     0x15cf16c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15cf170: stp             x16, x0, [SP]
    // 0x15cf174: mov             x0, x1
    // 0x15cf178: mov             lr, x0
    // 0x15cf17c: ldr             lr, [x21, lr, lsl #3]
    // 0x15cf180: blr             lr
    // 0x15cf184: mov             x2, x0
    // 0x15cf188: ldur            x0, [fp, #-8]
    // 0x15cf18c: stur            x2, [fp, #-0x10]
    // 0x15cf190: LoadField: r1 = r0->field_f
    //     0x15cf190: ldur            w1, [x0, #0xf]
    // 0x15cf194: DecompressPointer r1
    //     0x15cf194: add             x1, x1, HEAP, lsl #32
    // 0x15cf198: r0 = controller()
    //     0x15cf198: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf19c: LoadField: r1 = r0->field_7f
    //     0x15cf19c: ldur            w1, [x0, #0x7f]
    // 0x15cf1a0: DecompressPointer r1
    //     0x15cf1a0: add             x1, x1, HEAP, lsl #32
    // 0x15cf1a4: r0 = value()
    //     0x15cf1a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cf1a8: LoadField: r1 = r0->field_2b
    //     0x15cf1a8: ldur            w1, [x0, #0x2b]
    // 0x15cf1ac: DecompressPointer r1
    //     0x15cf1ac: add             x1, x1, HEAP, lsl #32
    // 0x15cf1b0: cmp             w1, NULL
    // 0x15cf1b4: b.ne            #0x15cf1c0
    // 0x15cf1b8: r4 = ""
    //     0x15cf1b8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cf1bc: b               #0x15cf1c4
    // 0x15cf1c0: mov             x4, x1
    // 0x15cf1c4: ldur            x0, [fp, #-8]
    // 0x15cf1c8: ldur            x3, [fp, #-0x18]
    // 0x15cf1cc: ldur            x2, [fp, #-0x10]
    // 0x15cf1d0: stur            x4, [fp, #-0x20]
    // 0x15cf1d4: LoadField: r1 = r0->field_13
    //     0x15cf1d4: ldur            w1, [x0, #0x13]
    // 0x15cf1d8: DecompressPointer r1
    //     0x15cf1d8: add             x1, x1, HEAP, lsl #32
    // 0x15cf1dc: r0 = of()
    //     0x15cf1dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cf1e0: LoadField: r1 = r0->field_87
    //     0x15cf1e0: ldur            w1, [x0, #0x87]
    // 0x15cf1e4: DecompressPointer r1
    //     0x15cf1e4: add             x1, x1, HEAP, lsl #32
    // 0x15cf1e8: LoadField: r0 = r1->field_2b
    //     0x15cf1e8: ldur            w0, [x1, #0x2b]
    // 0x15cf1ec: DecompressPointer r0
    //     0x15cf1ec: add             x0, x0, HEAP, lsl #32
    // 0x15cf1f0: r16 = 16.000000
    //     0x15cf1f0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15cf1f4: ldr             x16, [x16, #0x188]
    // 0x15cf1f8: r30 = Instance_Color
    //     0x15cf1f8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15cf1fc: stp             lr, x16, [SP]
    // 0x15cf200: mov             x1, x0
    // 0x15cf204: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15cf204: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15cf208: ldr             x4, [x4, #0xaa0]
    // 0x15cf20c: r0 = copyWith()
    //     0x15cf20c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cf210: stur            x0, [fp, #-8]
    // 0x15cf214: r0 = Text()
    //     0x15cf214: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cf218: mov             x1, x0
    // 0x15cf21c: ldur            x0, [fp, #-0x20]
    // 0x15cf220: stur            x1, [fp, #-0x28]
    // 0x15cf224: StoreField: r1->field_b = r0
    //     0x15cf224: stur            w0, [x1, #0xb]
    // 0x15cf228: ldur            x0, [fp, #-8]
    // 0x15cf22c: StoreField: r1->field_13 = r0
    //     0x15cf22c: stur            w0, [x1, #0x13]
    // 0x15cf230: r0 = Visibility()
    //     0x15cf230: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cf234: mov             x3, x0
    // 0x15cf238: ldur            x0, [fp, #-0x28]
    // 0x15cf23c: stur            x3, [fp, #-8]
    // 0x15cf240: StoreField: r3->field_b = r0
    //     0x15cf240: stur            w0, [x3, #0xb]
    // 0x15cf244: r0 = Instance_SizedBox
    //     0x15cf244: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cf248: StoreField: r3->field_f = r0
    //     0x15cf248: stur            w0, [x3, #0xf]
    // 0x15cf24c: ldur            x0, [fp, #-0x10]
    // 0x15cf250: StoreField: r3->field_13 = r0
    //     0x15cf250: stur            w0, [x3, #0x13]
    // 0x15cf254: r0 = false
    //     0x15cf254: add             x0, NULL, #0x30  ; false
    // 0x15cf258: ArrayStore: r3[0] = r0  ; List_4
    //     0x15cf258: stur            w0, [x3, #0x17]
    // 0x15cf25c: StoreField: r3->field_1b = r0
    //     0x15cf25c: stur            w0, [x3, #0x1b]
    // 0x15cf260: StoreField: r3->field_1f = r0
    //     0x15cf260: stur            w0, [x3, #0x1f]
    // 0x15cf264: StoreField: r3->field_23 = r0
    //     0x15cf264: stur            w0, [x3, #0x23]
    // 0x15cf268: StoreField: r3->field_27 = r0
    //     0x15cf268: stur            w0, [x3, #0x27]
    // 0x15cf26c: StoreField: r3->field_2b = r0
    //     0x15cf26c: stur            w0, [x3, #0x2b]
    // 0x15cf270: r1 = Null
    //     0x15cf270: mov             x1, NULL
    // 0x15cf274: r2 = 6
    //     0x15cf274: movz            x2, #0x6
    // 0x15cf278: r0 = AllocateArray()
    //     0x15cf278: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15cf27c: mov             x2, x0
    // 0x15cf280: ldur            x0, [fp, #-0x18]
    // 0x15cf284: stur            x2, [fp, #-0x10]
    // 0x15cf288: StoreField: r2->field_f = r0
    //     0x15cf288: stur            w0, [x2, #0xf]
    // 0x15cf28c: r16 = Instance_SizedBox
    //     0x15cf28c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15cf290: ldr             x16, [x16, #0xaa8]
    // 0x15cf294: StoreField: r2->field_13 = r16
    //     0x15cf294: stur            w16, [x2, #0x13]
    // 0x15cf298: ldur            x0, [fp, #-8]
    // 0x15cf29c: ArrayStore: r2[0] = r0  ; List_4
    //     0x15cf29c: stur            w0, [x2, #0x17]
    // 0x15cf2a0: r1 = <Widget>
    //     0x15cf2a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15cf2a4: r0 = AllocateGrowableArray()
    //     0x15cf2a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15cf2a8: mov             x1, x0
    // 0x15cf2ac: ldur            x0, [fp, #-0x10]
    // 0x15cf2b0: stur            x1, [fp, #-8]
    // 0x15cf2b4: StoreField: r1->field_f = r0
    //     0x15cf2b4: stur            w0, [x1, #0xf]
    // 0x15cf2b8: r0 = 6
    //     0x15cf2b8: movz            x0, #0x6
    // 0x15cf2bc: StoreField: r1->field_b = r0
    //     0x15cf2bc: stur            w0, [x1, #0xb]
    // 0x15cf2c0: r0 = Row()
    //     0x15cf2c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15cf2c4: r1 = Instance_Axis
    //     0x15cf2c4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15cf2c8: StoreField: r0->field_f = r1
    //     0x15cf2c8: stur            w1, [x0, #0xf]
    // 0x15cf2cc: r1 = Instance_MainAxisAlignment
    //     0x15cf2cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15cf2d0: ldr             x1, [x1, #0xab0]
    // 0x15cf2d4: StoreField: r0->field_13 = r1
    //     0x15cf2d4: stur            w1, [x0, #0x13]
    // 0x15cf2d8: r1 = Instance_MainAxisSize
    //     0x15cf2d8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15cf2dc: ldr             x1, [x1, #0xa10]
    // 0x15cf2e0: ArrayStore: r0[0] = r1  ; List_4
    //     0x15cf2e0: stur            w1, [x0, #0x17]
    // 0x15cf2e4: r1 = Instance_CrossAxisAlignment
    //     0x15cf2e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15cf2e8: ldr             x1, [x1, #0xa18]
    // 0x15cf2ec: StoreField: r0->field_1b = r1
    //     0x15cf2ec: stur            w1, [x0, #0x1b]
    // 0x15cf2f0: r1 = Instance_VerticalDirection
    //     0x15cf2f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15cf2f4: ldr             x1, [x1, #0xa20]
    // 0x15cf2f8: StoreField: r0->field_23 = r1
    //     0x15cf2f8: stur            w1, [x0, #0x23]
    // 0x15cf2fc: r1 = Instance_Clip
    //     0x15cf2fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15cf300: ldr             x1, [x1, #0x38]
    // 0x15cf304: StoreField: r0->field_2b = r1
    //     0x15cf304: stur            w1, [x0, #0x2b]
    // 0x15cf308: StoreField: r0->field_2f = rZR
    //     0x15cf308: stur            xzr, [x0, #0x2f]
    // 0x15cf30c: ldur            x1, [fp, #-8]
    // 0x15cf310: StoreField: r0->field_b = r1
    //     0x15cf310: stur            w1, [x0, #0xb]
    // 0x15cf314: LeaveFrame
    //     0x15cf314: mov             SP, fp
    //     0x15cf318: ldp             fp, lr, [SP], #0x10
    // 0x15cf31c: ret
    //     0x15cf31c: ret             
    // 0x15cf320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cf320: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cf324: b               #0x15cef24
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e9764, size: 0x2b0
    // 0x15e9764: EnterFrame
    //     0x15e9764: stp             fp, lr, [SP, #-0x10]!
    //     0x15e9768: mov             fp, SP
    // 0x15e976c: AllocStack(0x30)
    //     0x15e976c: sub             SP, SP, #0x30
    // 0x15e9770: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e9770: stur            x1, [fp, #-8]
    //     0x15e9774: stur            x2, [fp, #-0x10]
    // 0x15e9778: CheckStackOverflow
    //     0x15e9778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e977c: cmp             SP, x16
    //     0x15e9780: b.ls            #0x15e9a0c
    // 0x15e9784: r1 = 2
    //     0x15e9784: movz            x1, #0x2
    // 0x15e9788: r0 = AllocateContext()
    //     0x15e9788: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e978c: ldur            x1, [fp, #-8]
    // 0x15e9790: stur            x0, [fp, #-0x18]
    // 0x15e9794: StoreField: r0->field_f = r1
    //     0x15e9794: stur            w1, [x0, #0xf]
    // 0x15e9798: ldur            x2, [fp, #-0x10]
    // 0x15e979c: StoreField: r0->field_13 = r2
    //     0x15e979c: stur            w2, [x0, #0x13]
    // 0x15e97a0: r0 = Obx()
    //     0x15e97a0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e97a4: ldur            x2, [fp, #-0x18]
    // 0x15e97a8: r1 = Function '<anonymous closure>':.
    //     0x15e97a8: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c458] AnonymousClosure: (0x15ceefc), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e97ac: ldr             x1, [x1, #0x458]
    // 0x15e97b0: stur            x0, [fp, #-0x10]
    // 0x15e97b4: r0 = AllocateClosure()
    //     0x15e97b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e97b8: mov             x1, x0
    // 0x15e97bc: ldur            x0, [fp, #-0x10]
    // 0x15e97c0: StoreField: r0->field_b = r1
    //     0x15e97c0: stur            w1, [x0, #0xb]
    // 0x15e97c4: ldur            x1, [fp, #-8]
    // 0x15e97c8: r0 = controller()
    //     0x15e97c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e97cc: mov             x1, x0
    // 0x15e97d0: r0 = showBagAccordion()
    //     0x15e97d0: bl              #0x1395ad8  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_variants_controller.dart] CheckoutVariantsController::showBagAccordion
    // 0x15e97d4: tbnz            w0, #4, #0x15e986c
    // 0x15e97d8: ldur            x2, [fp, #-0x18]
    // 0x15e97dc: LoadField: r1 = r2->field_13
    //     0x15e97dc: ldur            w1, [x2, #0x13]
    // 0x15e97e0: DecompressPointer r1
    //     0x15e97e0: add             x1, x1, HEAP, lsl #32
    // 0x15e97e4: r0 = of()
    //     0x15e97e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e97e8: LoadField: r1 = r0->field_5b
    //     0x15e97e8: ldur            w1, [x0, #0x5b]
    // 0x15e97ec: DecompressPointer r1
    //     0x15e97ec: add             x1, x1, HEAP, lsl #32
    // 0x15e97f0: stur            x1, [fp, #-8]
    // 0x15e97f4: r0 = ColorFilter()
    //     0x15e97f4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e97f8: mov             x1, x0
    // 0x15e97fc: ldur            x0, [fp, #-8]
    // 0x15e9800: stur            x1, [fp, #-0x20]
    // 0x15e9804: StoreField: r1->field_7 = r0
    //     0x15e9804: stur            w0, [x1, #7]
    // 0x15e9808: r0 = Instance_BlendMode
    //     0x15e9808: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e980c: ldr             x0, [x0, #0xb30]
    // 0x15e9810: StoreField: r1->field_b = r0
    //     0x15e9810: stur            w0, [x1, #0xb]
    // 0x15e9814: r2 = 1
    //     0x15e9814: movz            x2, #0x1
    // 0x15e9818: StoreField: r1->field_13 = r2
    //     0x15e9818: stur            x2, [x1, #0x13]
    // 0x15e981c: r0 = SvgPicture()
    //     0x15e981c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e9820: stur            x0, [fp, #-8]
    // 0x15e9824: ldur            x16, [fp, #-0x20]
    // 0x15e9828: str             x16, [SP]
    // 0x15e982c: mov             x1, x0
    // 0x15e9830: r2 = "assets/images/search.svg"
    //     0x15e9830: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e9834: ldr             x2, [x2, #0xa30]
    // 0x15e9838: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e9838: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e983c: ldr             x4, [x4, #0xa38]
    // 0x15e9840: r0 = SvgPicture.asset()
    //     0x15e9840: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e9844: r0 = Align()
    //     0x15e9844: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e9848: r3 = Instance_Alignment
    //     0x15e9848: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e984c: ldr             x3, [x3, #0xb10]
    // 0x15e9850: StoreField: r0->field_f = r3
    //     0x15e9850: stur            w3, [x0, #0xf]
    // 0x15e9854: r4 = 1.000000
    //     0x15e9854: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9858: StoreField: r0->field_13 = r4
    //     0x15e9858: stur            w4, [x0, #0x13]
    // 0x15e985c: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e985c: stur            w4, [x0, #0x17]
    // 0x15e9860: ldur            x1, [fp, #-8]
    // 0x15e9864: StoreField: r0->field_b = r1
    //     0x15e9864: stur            w1, [x0, #0xb]
    // 0x15e9868: b               #0x15e991c
    // 0x15e986c: ldur            x5, [fp, #-0x18]
    // 0x15e9870: r4 = 1.000000
    //     0x15e9870: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9874: r0 = Instance_BlendMode
    //     0x15e9874: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9878: ldr             x0, [x0, #0xb30]
    // 0x15e987c: r3 = Instance_Alignment
    //     0x15e987c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e9880: ldr             x3, [x3, #0xb10]
    // 0x15e9884: r2 = 1
    //     0x15e9884: movz            x2, #0x1
    // 0x15e9888: LoadField: r1 = r5->field_13
    //     0x15e9888: ldur            w1, [x5, #0x13]
    // 0x15e988c: DecompressPointer r1
    //     0x15e988c: add             x1, x1, HEAP, lsl #32
    // 0x15e9890: r0 = of()
    //     0x15e9890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e9894: LoadField: r1 = r0->field_5b
    //     0x15e9894: ldur            w1, [x0, #0x5b]
    // 0x15e9898: DecompressPointer r1
    //     0x15e9898: add             x1, x1, HEAP, lsl #32
    // 0x15e989c: stur            x1, [fp, #-8]
    // 0x15e98a0: r0 = ColorFilter()
    //     0x15e98a0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e98a4: mov             x1, x0
    // 0x15e98a8: ldur            x0, [fp, #-8]
    // 0x15e98ac: stur            x1, [fp, #-0x20]
    // 0x15e98b0: StoreField: r1->field_7 = r0
    //     0x15e98b0: stur            w0, [x1, #7]
    // 0x15e98b4: r0 = Instance_BlendMode
    //     0x15e98b4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e98b8: ldr             x0, [x0, #0xb30]
    // 0x15e98bc: StoreField: r1->field_b = r0
    //     0x15e98bc: stur            w0, [x1, #0xb]
    // 0x15e98c0: r0 = 1
    //     0x15e98c0: movz            x0, #0x1
    // 0x15e98c4: StoreField: r1->field_13 = r0
    //     0x15e98c4: stur            x0, [x1, #0x13]
    // 0x15e98c8: r0 = SvgPicture()
    //     0x15e98c8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e98cc: stur            x0, [fp, #-8]
    // 0x15e98d0: ldur            x16, [fp, #-0x20]
    // 0x15e98d4: str             x16, [SP]
    // 0x15e98d8: mov             x1, x0
    // 0x15e98dc: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e98dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e98e0: ldr             x2, [x2, #0xa40]
    // 0x15e98e4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e98e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e98e8: ldr             x4, [x4, #0xa38]
    // 0x15e98ec: r0 = SvgPicture.asset()
    //     0x15e98ec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e98f0: r0 = Align()
    //     0x15e98f0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e98f4: mov             x1, x0
    // 0x15e98f8: r0 = Instance_Alignment
    //     0x15e98f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e98fc: ldr             x0, [x0, #0xb10]
    // 0x15e9900: StoreField: r1->field_f = r0
    //     0x15e9900: stur            w0, [x1, #0xf]
    // 0x15e9904: r0 = 1.000000
    //     0x15e9904: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9908: StoreField: r1->field_13 = r0
    //     0x15e9908: stur            w0, [x1, #0x13]
    // 0x15e990c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e990c: stur            w0, [x1, #0x17]
    // 0x15e9910: ldur            x0, [fp, #-8]
    // 0x15e9914: StoreField: r1->field_b = r0
    //     0x15e9914: stur            w0, [x1, #0xb]
    // 0x15e9918: mov             x0, x1
    // 0x15e991c: stur            x0, [fp, #-8]
    // 0x15e9920: r0 = InkWell()
    //     0x15e9920: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e9924: mov             x3, x0
    // 0x15e9928: ldur            x0, [fp, #-8]
    // 0x15e992c: stur            x3, [fp, #-0x20]
    // 0x15e9930: StoreField: r3->field_b = r0
    //     0x15e9930: stur            w0, [x3, #0xb]
    // 0x15e9934: ldur            x2, [fp, #-0x18]
    // 0x15e9938: r1 = Function '<anonymous closure>':.
    //     0x15e9938: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c460] AnonymousClosure: (0x15cee30), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e993c: ldr             x1, [x1, #0x460]
    // 0x15e9940: r0 = AllocateClosure()
    //     0x15e9940: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e9944: ldur            x2, [fp, #-0x20]
    // 0x15e9948: StoreField: r2->field_f = r0
    //     0x15e9948: stur            w0, [x2, #0xf]
    // 0x15e994c: r0 = true
    //     0x15e994c: add             x0, NULL, #0x20  ; true
    // 0x15e9950: StoreField: r2->field_43 = r0
    //     0x15e9950: stur            w0, [x2, #0x43]
    // 0x15e9954: r1 = Instance_BoxShape
    //     0x15e9954: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e9958: ldr             x1, [x1, #0x80]
    // 0x15e995c: StoreField: r2->field_47 = r1
    //     0x15e995c: stur            w1, [x2, #0x47]
    // 0x15e9960: StoreField: r2->field_6f = r0
    //     0x15e9960: stur            w0, [x2, #0x6f]
    // 0x15e9964: r1 = false
    //     0x15e9964: add             x1, NULL, #0x30  ; false
    // 0x15e9968: StoreField: r2->field_73 = r1
    //     0x15e9968: stur            w1, [x2, #0x73]
    // 0x15e996c: StoreField: r2->field_83 = r0
    //     0x15e996c: stur            w0, [x2, #0x83]
    // 0x15e9970: StoreField: r2->field_7b = r1
    //     0x15e9970: stur            w1, [x2, #0x7b]
    // 0x15e9974: r0 = Obx()
    //     0x15e9974: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e9978: ldur            x2, [fp, #-0x18]
    // 0x15e997c: r1 = Function '<anonymous closure>':.
    //     0x15e997c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c468] AnonymousClosure: (0x15caec4), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e9980: ldr             x1, [x1, #0x468]
    // 0x15e9984: stur            x0, [fp, #-8]
    // 0x15e9988: r0 = AllocateClosure()
    //     0x15e9988: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e998c: mov             x1, x0
    // 0x15e9990: ldur            x0, [fp, #-8]
    // 0x15e9994: StoreField: r0->field_b = r1
    //     0x15e9994: stur            w1, [x0, #0xb]
    // 0x15e9998: r1 = Null
    //     0x15e9998: mov             x1, NULL
    // 0x15e999c: r2 = 2
    //     0x15e999c: movz            x2, #0x2
    // 0x15e99a0: r0 = AllocateArray()
    //     0x15e99a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e99a4: mov             x2, x0
    // 0x15e99a8: ldur            x0, [fp, #-8]
    // 0x15e99ac: stur            x2, [fp, #-0x18]
    // 0x15e99b0: StoreField: r2->field_f = r0
    //     0x15e99b0: stur            w0, [x2, #0xf]
    // 0x15e99b4: r1 = <Widget>
    //     0x15e99b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e99b8: r0 = AllocateGrowableArray()
    //     0x15e99b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e99bc: mov             x1, x0
    // 0x15e99c0: ldur            x0, [fp, #-0x18]
    // 0x15e99c4: stur            x1, [fp, #-8]
    // 0x15e99c8: StoreField: r1->field_f = r0
    //     0x15e99c8: stur            w0, [x1, #0xf]
    // 0x15e99cc: r0 = 2
    //     0x15e99cc: movz            x0, #0x2
    // 0x15e99d0: StoreField: r1->field_b = r0
    //     0x15e99d0: stur            w0, [x1, #0xb]
    // 0x15e99d4: r0 = AppBar()
    //     0x15e99d4: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e99d8: stur            x0, [fp, #-0x18]
    // 0x15e99dc: ldur            x16, [fp, #-0x10]
    // 0x15e99e0: ldur            lr, [fp, #-8]
    // 0x15e99e4: stp             lr, x16, [SP]
    // 0x15e99e8: mov             x1, x0
    // 0x15e99ec: ldur            x2, [fp, #-0x20]
    // 0x15e99f0: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e99f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e99f4: ldr             x4, [x4, #0xa58]
    // 0x15e99f8: r0 = AppBar()
    //     0x15e99f8: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e99fc: ldur            x0, [fp, #-0x18]
    // 0x15e9a00: LeaveFrame
    //     0x15e9a00: mov             SP, fp
    //     0x15e9a04: ldp             fp, lr, [SP], #0x10
    // 0x15e9a08: ret
    //     0x15e9a08: ret             
    // 0x15e9a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e9a0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e9a10: b               #0x15e9784
  }
}
