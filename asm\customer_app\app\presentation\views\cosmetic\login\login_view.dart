// lib: , url: package:customer_app/app/presentation/views/cosmetic/login/login_view.dart

// class id: 1049288, size: 0x8
class :: {
}

// class id: 4597, size: 0x20, field offset: 0x14
class LoginView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x13516b0, size: 0x64
    // 0x13516b0: EnterFrame
    //     0x13516b0: stp             fp, lr, [SP, #-0x10]!
    //     0x13516b4: mov             fp, SP
    // 0x13516b8: AllocStack(0x18)
    //     0x13516b8: sub             SP, SP, #0x18
    // 0x13516bc: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x13516bc: stur            x1, [fp, #-8]
    //     0x13516c0: stur            x2, [fp, #-0x10]
    // 0x13516c4: r1 = 2
    //     0x13516c4: movz            x1, #0x2
    // 0x13516c8: r0 = AllocateContext()
    //     0x13516c8: bl              #0x16f6108  ; AllocateContextStub
    // 0x13516cc: mov             x1, x0
    // 0x13516d0: ldur            x0, [fp, #-8]
    // 0x13516d4: stur            x1, [fp, #-0x18]
    // 0x13516d8: StoreField: r1->field_f = r0
    //     0x13516d8: stur            w0, [x1, #0xf]
    // 0x13516dc: ldur            x0, [fp, #-0x10]
    // 0x13516e0: StoreField: r1->field_13 = r0
    //     0x13516e0: stur            w0, [x1, #0x13]
    // 0x13516e4: r0 = Obx()
    //     0x13516e4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13516e8: ldur            x2, [fp, #-0x18]
    // 0x13516ec: r1 = Function '<anonymous closure>':.
    //     0x13516ec: add             x1, PP, #0x43, lsl #12  ; [pp+0x43208] AnonymousClosure: (0x1351714), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::bottomNavigationBar (0x13516b0)
    //     0x13516f0: ldr             x1, [x1, #0x208]
    // 0x13516f4: stur            x0, [fp, #-8]
    // 0x13516f8: r0 = AllocateClosure()
    //     0x13516f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13516fc: mov             x1, x0
    // 0x1351700: ldur            x0, [fp, #-8]
    // 0x1351704: StoreField: r0->field_b = r1
    //     0x1351704: stur            w1, [x0, #0xb]
    // 0x1351708: LeaveFrame
    //     0x1351708: mov             SP, fp
    //     0x135170c: ldp             fp, lr, [SP], #0x10
    // 0x1351710: ret
    //     0x1351710: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x1351714, size: 0x30c
    // 0x1351714: EnterFrame
    //     0x1351714: stp             fp, lr, [SP, #-0x10]!
    //     0x1351718: mov             fp, SP
    // 0x135171c: AllocStack(0x60)
    //     0x135171c: sub             SP, SP, #0x60
    // 0x1351720: SetupParameters()
    //     0x1351720: ldr             x0, [fp, #0x10]
    //     0x1351724: ldur            w2, [x0, #0x17]
    //     0x1351728: add             x2, x2, HEAP, lsl #32
    //     0x135172c: stur            x2, [fp, #-8]
    // 0x1351730: CheckStackOverflow
    //     0x1351730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1351734: cmp             SP, x16
    //     0x1351738: b.ls            #0x1351a18
    // 0x135173c: LoadField: r1 = r2->field_13
    //     0x135173c: ldur            w1, [x2, #0x13]
    // 0x1351740: DecompressPointer r1
    //     0x1351740: add             x1, x1, HEAP, lsl #32
    // 0x1351744: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1351744: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1351748: r0 = _of()
    //     0x1351748: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x135174c: LoadField: r1 = r0->field_23
    //     0x135174c: ldur            w1, [x0, #0x23]
    // 0x1351750: DecompressPointer r1
    //     0x1351750: add             x1, x1, HEAP, lsl #32
    // 0x1351754: LoadField: d0 = r1->field_1f
    //     0x1351754: ldur            d0, [x1, #0x1f]
    // 0x1351758: stur            d0, [fp, #-0x48]
    // 0x135175c: r0 = EdgeInsets()
    //     0x135175c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1351760: stur            x0, [fp, #-0x10]
    // 0x1351764: StoreField: r0->field_7 = rZR
    //     0x1351764: stur            xzr, [x0, #7]
    // 0x1351768: StoreField: r0->field_f = rZR
    //     0x1351768: stur            xzr, [x0, #0xf]
    // 0x135176c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x135176c: stur            xzr, [x0, #0x17]
    // 0x1351770: ldur            d0, [fp, #-0x48]
    // 0x1351774: StoreField: r0->field_1f = d0
    //     0x1351774: stur            d0, [x0, #0x1f]
    // 0x1351778: r1 = _ConstMap len:11
    //     0x1351778: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x135177c: ldr             x1, [x1, #0xc28]
    // 0x1351780: r2 = 8
    //     0x1351780: movz            x2, #0x8
    // 0x1351784: r0 = []()
    //     0x1351784: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1351788: stur            x0, [fp, #-0x18]
    // 0x135178c: r0 = BoxDecoration()
    //     0x135178c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1351790: mov             x2, x0
    // 0x1351794: r0 = Instance_Color
    //     0x1351794: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1351798: stur            x2, [fp, #-0x20]
    // 0x135179c: StoreField: r2->field_7 = r0
    //     0x135179c: stur            w0, [x2, #7]
    // 0x13517a0: r1 = Instance_BorderRadius
    //     0x13517a0: add             x1, PP, #0x43, lsl #12  ; [pp+0x43210] Obj!BorderRadius@d5a501
    //     0x13517a4: ldr             x1, [x1, #0x210]
    // 0x13517a8: StoreField: r2->field_13 = r1
    //     0x13517a8: stur            w1, [x2, #0x13]
    // 0x13517ac: ldur            x1, [fp, #-0x18]
    // 0x13517b0: ArrayStore: r2[0] = r1  ; List_4
    //     0x13517b0: stur            w1, [x2, #0x17]
    // 0x13517b4: r1 = Instance_BoxShape
    //     0x13517b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13517b8: ldr             x1, [x1, #0x80]
    // 0x13517bc: StoreField: r2->field_23 = r1
    //     0x13517bc: stur            w1, [x2, #0x23]
    // 0x13517c0: ldur            x3, [fp, #-8]
    // 0x13517c4: LoadField: r1 = r3->field_f
    //     0x13517c4: ldur            w1, [x3, #0xf]
    // 0x13517c8: DecompressPointer r1
    //     0x13517c8: add             x1, x1, HEAP, lsl #32
    // 0x13517cc: r0 = controller()
    //     0x13517cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13517d0: LoadField: r1 = r0->field_8b
    //     0x13517d0: ldur            w1, [x0, #0x8b]
    // 0x13517d4: DecompressPointer r1
    //     0x13517d4: add             x1, x1, HEAP, lsl #32
    // 0x13517d8: r0 = value()
    //     0x13517d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13517dc: cmp             w0, NULL
    // 0x13517e0: b.ne            #0x13517ec
    // 0x13517e4: r1 = true
    //     0x13517e4: add             x1, NULL, #0x20  ; true
    // 0x13517e8: b               #0x13517f0
    // 0x13517ec: mov             x1, x0
    // 0x13517f0: ldur            x2, [fp, #-8]
    // 0x13517f4: ldur            x0, [fp, #-0x10]
    // 0x13517f8: eor             x3, x1, #0x10
    // 0x13517fc: stur            x3, [fp, #-0x18]
    // 0x1351800: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1351800: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1351804: ldr             x0, [x0, #0x1c80]
    //     0x1351808: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x135180c: cmp             w0, w16
    //     0x1351810: b.ne            #0x135181c
    //     0x1351814: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1351818: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x135181c: r0 = GetNavigation.width()
    //     0x135181c: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x1351820: stur            d0, [fp, #-0x48]
    // 0x1351824: r0 = GetNavigation.size()
    //     0x1351824: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1351828: LoadField: d0 = r0->field_f
    //     0x1351828: ldur            d0, [x0, #0xf]
    // 0x135182c: d1 = 0.060000
    //     0x135182c: add             x17, PP, #0x36, lsl #12  ; [pp+0x36f28] IMM: double(0.06) from 0x3faeb851eb851eb8
    //     0x1351830: ldr             d1, [x17, #0xf28]
    // 0x1351834: fmul            d2, d0, d1
    // 0x1351838: ldur            x2, [fp, #-8]
    // 0x135183c: stur            d2, [fp, #-0x50]
    // 0x1351840: LoadField: r1 = r2->field_13
    //     0x1351840: ldur            w1, [x2, #0x13]
    // 0x1351844: DecompressPointer r1
    //     0x1351844: add             x1, x1, HEAP, lsl #32
    // 0x1351848: r0 = of()
    //     0x1351848: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135184c: LoadField: r2 = r0->field_5b
    //     0x135184c: ldur            w2, [x0, #0x5b]
    // 0x1351850: DecompressPointer r2
    //     0x1351850: add             x2, x2, HEAP, lsl #32
    // 0x1351854: stur            x2, [fp, #-0x28]
    // 0x1351858: r1 = "confirm number"
    //     0x1351858: add             x1, PP, #0x40, lsl #12  ; [pp+0x40270] "confirm number"
    //     0x135185c: ldr             x1, [x1, #0x270]
    // 0x1351860: r0 = capitalizeFirstWord()
    //     0x1351860: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x1351864: ldur            x2, [fp, #-8]
    // 0x1351868: stur            x0, [fp, #-0x30]
    // 0x135186c: LoadField: r1 = r2->field_13
    //     0x135186c: ldur            w1, [x2, #0x13]
    // 0x1351870: DecompressPointer r1
    //     0x1351870: add             x1, x1, HEAP, lsl #32
    // 0x1351874: r0 = of()
    //     0x1351874: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1351878: LoadField: r1 = r0->field_87
    //     0x1351878: ldur            w1, [x0, #0x87]
    // 0x135187c: DecompressPointer r1
    //     0x135187c: add             x1, x1, HEAP, lsl #32
    // 0x1351880: LoadField: r0 = r1->field_7
    //     0x1351880: ldur            w0, [x1, #7]
    // 0x1351884: DecompressPointer r0
    //     0x1351884: add             x0, x0, HEAP, lsl #32
    // 0x1351888: r16 = 16.000000
    //     0x1351888: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x135188c: ldr             x16, [x16, #0x188]
    // 0x1351890: r30 = Instance_Color
    //     0x1351890: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1351894: stp             lr, x16, [SP]
    // 0x1351898: mov             x1, x0
    // 0x135189c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x135189c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13518a0: ldr             x4, [x4, #0xaa0]
    // 0x13518a4: r0 = copyWith()
    //     0x13518a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13518a8: stur            x0, [fp, #-0x38]
    // 0x13518ac: r0 = Text()
    //     0x13518ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13518b0: mov             x3, x0
    // 0x13518b4: ldur            x0, [fp, #-0x30]
    // 0x13518b8: stur            x3, [fp, #-0x40]
    // 0x13518bc: StoreField: r3->field_b = r0
    //     0x13518bc: stur            w0, [x3, #0xb]
    // 0x13518c0: ldur            x0, [fp, #-0x38]
    // 0x13518c4: StoreField: r3->field_13 = r0
    //     0x13518c4: stur            w0, [x3, #0x13]
    // 0x13518c8: ldur            x2, [fp, #-8]
    // 0x13518cc: r1 = Function '<anonymous closure>':.
    //     0x13518cc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43218] AnonymousClosure: (0x1351a20), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::bottomNavigationBar (0x13516b0)
    //     0x13518d0: ldr             x1, [x1, #0x218]
    // 0x13518d4: r0 = AllocateClosure()
    //     0x13518d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13518d8: stur            x0, [fp, #-8]
    // 0x13518dc: r0 = MaterialButton()
    //     0x13518dc: bl              #0x131db78  ; AllocateMaterialButtonStub -> MaterialButton (size=0x88)
    // 0x13518e0: mov             x1, x0
    // 0x13518e4: ldur            x0, [fp, #-8]
    // 0x13518e8: stur            x1, [fp, #-0x30]
    // 0x13518ec: StoreField: r1->field_b = r0
    //     0x13518ec: stur            w0, [x1, #0xb]
    // 0x13518f0: r0 = Instance_Color
    //     0x13518f0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13518f4: StoreField: r1->field_1f = r0
    //     0x13518f4: stur            w0, [x1, #0x1f]
    // 0x13518f8: ldur            x0, [fp, #-0x28]
    // 0x13518fc: StoreField: r1->field_23 = r0
    //     0x13518fc: stur            w0, [x1, #0x23]
    // 0x1351900: r0 = Instance_RoundedRectangleBorder
    //     0x1351900: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x1351904: ldr             x0, [x0, #0x888]
    // 0x1351908: StoreField: r1->field_5b = r0
    //     0x1351908: stur            w0, [x1, #0x5b]
    // 0x135190c: r0 = Instance_Clip
    //     0x135190c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1351910: ldr             x0, [x0, #0x38]
    // 0x1351914: StoreField: r1->field_5f = r0
    //     0x1351914: stur            w0, [x1, #0x5f]
    // 0x1351918: r0 = false
    //     0x1351918: add             x0, NULL, #0x30  ; false
    // 0x135191c: StoreField: r1->field_67 = r0
    //     0x135191c: stur            w0, [x1, #0x67]
    // 0x1351920: ldur            d0, [fp, #-0x48]
    // 0x1351924: StoreField: r1->field_73 = d0
    //     0x1351924: stur            d0, [x1, #0x73]
    // 0x1351928: ldur            d0, [fp, #-0x50]
    // 0x135192c: StoreField: r1->field_7b = d0
    //     0x135192c: stur            d0, [x1, #0x7b]
    // 0x1351930: r2 = true
    //     0x1351930: add             x2, NULL, #0x20  ; true
    // 0x1351934: StoreField: r1->field_83 = r2
    //     0x1351934: stur            w2, [x1, #0x83]
    // 0x1351938: ldur            x2, [fp, #-0x40]
    // 0x135193c: StoreField: r1->field_4f = r2
    //     0x135193c: stur            w2, [x1, #0x4f]
    // 0x1351940: r2 = Instance_ValueKey
    //     0x1351940: add             x2, PP, #0x36, lsl #12  ; [pp+0x36f40] Obj!ValueKey<String>@d5b381
    //     0x1351944: ldr             x2, [x2, #0xf40]
    // 0x1351948: StoreField: r1->field_7 = r2
    //     0x1351948: stur            w2, [x1, #7]
    // 0x135194c: r0 = Padding()
    //     0x135194c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1351950: mov             x1, x0
    // 0x1351954: r0 = Instance_EdgeInsets
    //     0x1351954: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x1351958: ldr             x0, [x0, #0xf98]
    // 0x135195c: stur            x1, [fp, #-8]
    // 0x1351960: StoreField: r1->field_f = r0
    //     0x1351960: stur            w0, [x1, #0xf]
    // 0x1351964: ldur            x0, [fp, #-0x30]
    // 0x1351968: StoreField: r1->field_b = r0
    //     0x1351968: stur            w0, [x1, #0xb]
    // 0x135196c: r0 = Container()
    //     0x135196c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1351970: stur            x0, [fp, #-0x28]
    // 0x1351974: r16 = Instance_BoxDecoration
    //     0x1351974: add             x16, PP, #0x40, lsl #12  ; [pp+0x40280] Obj!BoxDecoration@d64ce1
    //     0x1351978: ldr             x16, [x16, #0x280]
    // 0x135197c: ldur            lr, [fp, #-8]
    // 0x1351980: stp             lr, x16, [SP]
    // 0x1351984: mov             x1, x0
    // 0x1351988: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1351988: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x135198c: ldr             x4, [x4, #0x88]
    // 0x1351990: r0 = Container()
    //     0x1351990: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1351994: r0 = Visibility()
    //     0x1351994: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1351998: mov             x1, x0
    // 0x135199c: ldur            x0, [fp, #-0x28]
    // 0x13519a0: stur            x1, [fp, #-8]
    // 0x13519a4: StoreField: r1->field_b = r0
    //     0x13519a4: stur            w0, [x1, #0xb]
    // 0x13519a8: r0 = Instance_SizedBox
    //     0x13519a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13519ac: StoreField: r1->field_f = r0
    //     0x13519ac: stur            w0, [x1, #0xf]
    // 0x13519b0: ldur            x0, [fp, #-0x18]
    // 0x13519b4: StoreField: r1->field_13 = r0
    //     0x13519b4: stur            w0, [x1, #0x13]
    // 0x13519b8: r0 = false
    //     0x13519b8: add             x0, NULL, #0x30  ; false
    // 0x13519bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x13519bc: stur            w0, [x1, #0x17]
    // 0x13519c0: StoreField: r1->field_1b = r0
    //     0x13519c0: stur            w0, [x1, #0x1b]
    // 0x13519c4: StoreField: r1->field_1f = r0
    //     0x13519c4: stur            w0, [x1, #0x1f]
    // 0x13519c8: StoreField: r1->field_23 = r0
    //     0x13519c8: stur            w0, [x1, #0x23]
    // 0x13519cc: StoreField: r1->field_27 = r0
    //     0x13519cc: stur            w0, [x1, #0x27]
    // 0x13519d0: StoreField: r1->field_2b = r0
    //     0x13519d0: stur            w0, [x1, #0x2b]
    // 0x13519d4: r0 = Container()
    //     0x13519d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13519d8: stur            x0, [fp, #-0x18]
    // 0x13519dc: ldur            x16, [fp, #-0x20]
    // 0x13519e0: ldur            lr, [fp, #-8]
    // 0x13519e4: stp             lr, x16, [SP]
    // 0x13519e8: mov             x1, x0
    // 0x13519ec: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13519ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13519f0: ldr             x4, [x4, #0x88]
    // 0x13519f4: r0 = Container()
    //     0x13519f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13519f8: r0 = Padding()
    //     0x13519f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13519fc: ldur            x1, [fp, #-0x10]
    // 0x1351a00: StoreField: r0->field_f = r1
    //     0x1351a00: stur            w1, [x0, #0xf]
    // 0x1351a04: ldur            x1, [fp, #-0x18]
    // 0x1351a08: StoreField: r0->field_b = r1
    //     0x1351a08: stur            w1, [x0, #0xb]
    // 0x1351a0c: LeaveFrame
    //     0x1351a0c: mov             SP, fp
    //     0x1351a10: ldp             fp, lr, [SP], #0x10
    // 0x1351a14: ret
    //     0x1351a14: ret             
    // 0x1351a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1351a18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1351a1c: b               #0x135173c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1351a20, size: 0x48
    // 0x1351a20: EnterFrame
    //     0x1351a20: stp             fp, lr, [SP, #-0x10]!
    //     0x1351a24: mov             fp, SP
    // 0x1351a28: ldr             x0, [fp, #0x10]
    // 0x1351a2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1351a2c: ldur            w1, [x0, #0x17]
    // 0x1351a30: DecompressPointer r1
    //     0x1351a30: add             x1, x1, HEAP, lsl #32
    // 0x1351a34: CheckStackOverflow
    //     0x1351a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1351a38: cmp             SP, x16
    //     0x1351a3c: b.ls            #0x1351a60
    // 0x1351a40: LoadField: r0 = r1->field_f
    //     0x1351a40: ldur            w0, [x1, #0xf]
    // 0x1351a44: DecompressPointer r0
    //     0x1351a44: add             x0, x0, HEAP, lsl #32
    // 0x1351a48: mov             x1, x0
    // 0x1351a4c: r0 = onPhoneSubmitted()
    //     0x1351a4c: bl              #0x131dbcc  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onPhoneSubmitted
    // 0x1351a50: r0 = Null
    //     0x1351a50: mov             x0, NULL
    // 0x1351a54: LeaveFrame
    //     0x1351a54: mov             SP, fp
    //     0x1351a58: ldp             fp, lr, [SP], #0x10
    // 0x1351a5c: ret
    //     0x1351a5c: ret             
    // 0x1351a60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1351a60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1351a64: b               #0x1351a40
  }
  _ body(/* No info */) {
    // ** addr: 0x14a71c0, size: 0xc4
    // 0x14a71c0: EnterFrame
    //     0x14a71c0: stp             fp, lr, [SP, #-0x10]!
    //     0x14a71c4: mov             fp, SP
    // 0x14a71c8: AllocStack(0x18)
    //     0x14a71c8: sub             SP, SP, #0x18
    // 0x14a71cc: SetupParameters(LoginView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14a71cc: mov             x0, x1
    //     0x14a71d0: stur            x1, [fp, #-8]
    //     0x14a71d4: stur            x2, [fp, #-0x10]
    // 0x14a71d8: r1 = 2
    //     0x14a71d8: movz            x1, #0x2
    // 0x14a71dc: r0 = AllocateContext()
    //     0x14a71dc: bl              #0x16f6108  ; AllocateContextStub
    // 0x14a71e0: ldur            x2, [fp, #-8]
    // 0x14a71e4: stur            x0, [fp, #-0x18]
    // 0x14a71e8: StoreField: r0->field_f = r2
    //     0x14a71e8: stur            w2, [x0, #0xf]
    // 0x14a71ec: ldur            x1, [fp, #-0x10]
    // 0x14a71f0: StoreField: r0->field_13 = r1
    //     0x14a71f0: stur            w1, [x0, #0x13]
    // 0x14a71f4: r0 = Obx()
    //     0x14a71f4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14a71f8: ldur            x2, [fp, #-0x18]
    // 0x14a71fc: r1 = Function '<anonymous closure>':.
    //     0x14a71fc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43220] AnonymousClosure: (0x14a72bc), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::body (0x14a71c0)
    //     0x14a7200: ldr             x1, [x1, #0x220]
    // 0x14a7204: stur            x0, [fp, #-0x10]
    // 0x14a7208: r0 = AllocateClosure()
    //     0x14a7208: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a720c: mov             x1, x0
    // 0x14a7210: ldur            x0, [fp, #-0x10]
    // 0x14a7214: StoreField: r0->field_b = r1
    //     0x14a7214: stur            w1, [x0, #0xb]
    // 0x14a7218: r0 = WillPopScope()
    //     0x14a7218: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14a721c: mov             x3, x0
    // 0x14a7220: ldur            x0, [fp, #-0x10]
    // 0x14a7224: stur            x3, [fp, #-0x18]
    // 0x14a7228: StoreField: r3->field_b = r0
    //     0x14a7228: stur            w0, [x3, #0xb]
    // 0x14a722c: ldur            x2, [fp, #-8]
    // 0x14a7230: r1 = Function 'onBackPress':.
    //     0x14a7230: add             x1, PP, #0x43, lsl #12  ; [pp+0x43228] AnonymousClosure: (0x14a7284), in [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onBackPress (0x1401b54)
    //     0x14a7234: ldr             x1, [x1, #0x228]
    // 0x14a7238: r0 = AllocateClosure()
    //     0x14a7238: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a723c: mov             x1, x0
    // 0x14a7240: ldur            x0, [fp, #-0x18]
    // 0x14a7244: StoreField: r0->field_f = r1
    //     0x14a7244: stur            w1, [x0, #0xf]
    // 0x14a7248: r0 = SafeArea()
    //     0x14a7248: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14a724c: r1 = true
    //     0x14a724c: add             x1, NULL, #0x20  ; true
    // 0x14a7250: StoreField: r0->field_b = r1
    //     0x14a7250: stur            w1, [x0, #0xb]
    // 0x14a7254: StoreField: r0->field_f = r1
    //     0x14a7254: stur            w1, [x0, #0xf]
    // 0x14a7258: StoreField: r0->field_13 = r1
    //     0x14a7258: stur            w1, [x0, #0x13]
    // 0x14a725c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14a725c: stur            w1, [x0, #0x17]
    // 0x14a7260: r1 = Instance_EdgeInsets
    //     0x14a7260: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14a7264: StoreField: r0->field_1b = r1
    //     0x14a7264: stur            w1, [x0, #0x1b]
    // 0x14a7268: r1 = false
    //     0x14a7268: add             x1, NULL, #0x30  ; false
    // 0x14a726c: StoreField: r0->field_1f = r1
    //     0x14a726c: stur            w1, [x0, #0x1f]
    // 0x14a7270: ldur            x1, [fp, #-0x18]
    // 0x14a7274: StoreField: r0->field_23 = r1
    //     0x14a7274: stur            w1, [x0, #0x23]
    // 0x14a7278: LeaveFrame
    //     0x14a7278: mov             SP, fp
    //     0x14a727c: ldp             fp, lr, [SP], #0x10
    // 0x14a7280: ret
    //     0x14a7280: ret             
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14a7284, size: 0x38
    // 0x14a7284: EnterFrame
    //     0x14a7284: stp             fp, lr, [SP, #-0x10]!
    //     0x14a7288: mov             fp, SP
    // 0x14a728c: ldr             x0, [fp, #0x10]
    // 0x14a7290: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14a7290: ldur            w1, [x0, #0x17]
    // 0x14a7294: DecompressPointer r1
    //     0x14a7294: add             x1, x1, HEAP, lsl #32
    // 0x14a7298: CheckStackOverflow
    //     0x14a7298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a729c: cmp             SP, x16
    //     0x14a72a0: b.ls            #0x14a72b4
    // 0x14a72a4: r0 = onBackPress()
    //     0x14a72a4: bl              #0x1401b54  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onBackPress
    // 0x14a72a8: LeaveFrame
    //     0x14a72a8: mov             SP, fp
    //     0x14a72ac: ldp             fp, lr, [SP], #0x10
    // 0x14a72b0: ret
    //     0x14a72b0: ret             
    // 0x14a72b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a72b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a72b8: b               #0x14a72a4
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14a72bc, size: 0x428
    // 0x14a72bc: EnterFrame
    //     0x14a72bc: stp             fp, lr, [SP, #-0x10]!
    //     0x14a72c0: mov             fp, SP
    // 0x14a72c4: AllocStack(0x58)
    //     0x14a72c4: sub             SP, SP, #0x58
    // 0x14a72c8: SetupParameters()
    //     0x14a72c8: ldr             x0, [fp, #0x10]
    //     0x14a72cc: ldur            w2, [x0, #0x17]
    //     0x14a72d0: add             x2, x2, HEAP, lsl #32
    //     0x14a72d4: stur            x2, [fp, #-8]
    // 0x14a72d8: CheckStackOverflow
    //     0x14a72d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a72dc: cmp             SP, x16
    //     0x14a72e0: b.ls            #0x14a76dc
    // 0x14a72e4: LoadField: r1 = r2->field_f
    //     0x14a72e4: ldur            w1, [x2, #0xf]
    // 0x14a72e8: DecompressPointer r1
    //     0x14a72e8: add             x1, x1, HEAP, lsl #32
    // 0x14a72ec: r0 = controller()
    //     0x14a72ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a72f0: LoadField: r1 = r0->field_6b
    //     0x14a72f0: ldur            w1, [x0, #0x6b]
    // 0x14a72f4: DecompressPointer r1
    //     0x14a72f4: add             x1, x1, HEAP, lsl #32
    // 0x14a72f8: r0 = value()
    //     0x14a72f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a72fc: tbnz            w0, #4, #0x14a731c
    // 0x14a7300: r0 = Container()
    //     0x14a7300: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a7304: mov             x1, x0
    // 0x14a7308: stur            x0, [fp, #-0x10]
    // 0x14a730c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14a730c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14a7310: r0 = Container()
    //     0x14a7310: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a7314: ldur            x0, [fp, #-0x10]
    // 0x14a7318: b               #0x14a76d0
    // 0x14a731c: ldur            x0, [fp, #-8]
    // 0x14a7320: LoadField: r1 = r0->field_f
    //     0x14a7320: ldur            w1, [x0, #0xf]
    // 0x14a7324: DecompressPointer r1
    //     0x14a7324: add             x1, x1, HEAP, lsl #32
    // 0x14a7328: LoadField: r2 = r0->field_13
    //     0x14a7328: ldur            w2, [x0, #0x13]
    // 0x14a732c: DecompressPointer r2
    //     0x14a732c: add             x2, x2, HEAP, lsl #32
    // 0x14a7330: r0 = _getCosmeticThemeLoginForm()
    //     0x14a7330: bl              #0x14a76e4  ; [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::_getCosmeticThemeLoginForm
    // 0x14a7334: ldur            x2, [fp, #-8]
    // 0x14a7338: stur            x0, [fp, #-0x10]
    // 0x14a733c: LoadField: r1 = r2->field_f
    //     0x14a733c: ldur            w1, [x2, #0xf]
    // 0x14a7340: DecompressPointer r1
    //     0x14a7340: add             x1, x1, HEAP, lsl #32
    // 0x14a7344: r0 = controller()
    //     0x14a7344: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a7348: LoadField: r1 = r0->field_8b
    //     0x14a7348: ldur            w1, [x0, #0x8b]
    // 0x14a734c: DecompressPointer r1
    //     0x14a734c: add             x1, x1, HEAP, lsl #32
    // 0x14a7350: r0 = value()
    //     0x14a7350: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7354: cmp             w0, NULL
    // 0x14a7358: b.ne            #0x14a7360
    // 0x14a735c: r0 = true
    //     0x14a735c: add             x0, NULL, #0x20  ; true
    // 0x14a7360: ldur            x2, [fp, #-8]
    // 0x14a7364: stur            x0, [fp, #-0x18]
    // 0x14a7368: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14a7368: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14a736c: ldr             x0, [x0, #0x1c80]
    //     0x14a7370: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14a7374: cmp             w0, w16
    //     0x14a7378: b.ne            #0x14a7384
    //     0x14a737c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14a7380: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14a7384: r0 = GetNavigation.width()
    //     0x14a7384: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x14a7388: stur            d0, [fp, #-0x40]
    // 0x14a738c: r0 = GetNavigation.size()
    //     0x14a738c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14a7390: LoadField: d0 = r0->field_f
    //     0x14a7390: ldur            d0, [x0, #0xf]
    // 0x14a7394: d1 = 0.060000
    //     0x14a7394: add             x17, PP, #0x36, lsl #12  ; [pp+0x36f28] IMM: double(0.06) from 0x3faeb851eb851eb8
    //     0x14a7398: ldr             d1, [x17, #0xf28]
    // 0x14a739c: fmul            d2, d0, d1
    // 0x14a73a0: ldur            x2, [fp, #-8]
    // 0x14a73a4: stur            d2, [fp, #-0x48]
    // 0x14a73a8: LoadField: r1 = r2->field_13
    //     0x14a73a8: ldur            w1, [x2, #0x13]
    // 0x14a73ac: DecompressPointer r1
    //     0x14a73ac: add             x1, x1, HEAP, lsl #32
    // 0x14a73b0: r0 = of()
    //     0x14a73b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a73b4: LoadField: r2 = r0->field_5b
    //     0x14a73b4: ldur            w2, [x0, #0x5b]
    // 0x14a73b8: DecompressPointer r2
    //     0x14a73b8: add             x2, x2, HEAP, lsl #32
    // 0x14a73bc: ldur            x0, [fp, #-8]
    // 0x14a73c0: stur            x2, [fp, #-0x20]
    // 0x14a73c4: LoadField: r1 = r0->field_f
    //     0x14a73c4: ldur            w1, [x0, #0xf]
    // 0x14a73c8: DecompressPointer r1
    //     0x14a73c8: add             x1, x1, HEAP, lsl #32
    // 0x14a73cc: r0 = controller()
    //     0x14a73cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a73d0: LoadField: r1 = r0->field_8f
    //     0x14a73d0: ldur            w1, [x0, #0x8f]
    // 0x14a73d4: DecompressPointer r1
    //     0x14a73d4: add             x1, x1, HEAP, lsl #32
    // 0x14a73d8: r0 = value()
    //     0x14a73d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a73dc: cmp             w0, NULL
    // 0x14a73e0: b.eq            #0x14a7400
    // 0x14a73e4: tbnz            w0, #4, #0x14a7400
    // 0x14a73e8: ldur            x2, [fp, #-8]
    // 0x14a73ec: r1 = Function '<anonymous closure>':.
    //     0x14a73ec: add             x1, PP, #0x43, lsl #12  ; [pp+0x43230] AnonymousClosure: (0x14a8754), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::body (0x14a71c0)
    //     0x14a73f0: ldr             x1, [x1, #0x230]
    // 0x14a73f4: r0 = AllocateClosure()
    //     0x14a73f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a73f8: mov             x5, x0
    // 0x14a73fc: b               #0x14a7404
    // 0x14a7400: r5 = Null
    //     0x14a7400: mov             x5, NULL
    // 0x14a7404: ldur            x2, [fp, #-8]
    // 0x14a7408: ldur            x4, [fp, #-0x10]
    // 0x14a740c: ldur            x3, [fp, #-0x18]
    // 0x14a7410: ldur            d1, [fp, #-0x40]
    // 0x14a7414: ldur            d0, [fp, #-0x48]
    // 0x14a7418: ldur            x0, [fp, #-0x20]
    // 0x14a741c: stur            x5, [fp, #-0x28]
    // 0x14a7420: r1 = "verify"
    //     0x14a7420: add             x1, PP, #0x40, lsl #12  ; [pp+0x402a0] "verify"
    //     0x14a7424: ldr             x1, [x1, #0x2a0]
    // 0x14a7428: r0 = capitalizeFirstWord()
    //     0x14a7428: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14a742c: mov             x2, x0
    // 0x14a7430: ldur            x0, [fp, #-8]
    // 0x14a7434: stur            x2, [fp, #-0x30]
    // 0x14a7438: LoadField: r1 = r0->field_13
    //     0x14a7438: ldur            w1, [x0, #0x13]
    // 0x14a743c: DecompressPointer r1
    //     0x14a743c: add             x1, x1, HEAP, lsl #32
    // 0x14a7440: r0 = of()
    //     0x14a7440: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a7444: LoadField: r1 = r0->field_87
    //     0x14a7444: ldur            w1, [x0, #0x87]
    // 0x14a7448: DecompressPointer r1
    //     0x14a7448: add             x1, x1, HEAP, lsl #32
    // 0x14a744c: LoadField: r0 = r1->field_7
    //     0x14a744c: ldur            w0, [x1, #7]
    // 0x14a7450: DecompressPointer r0
    //     0x14a7450: add             x0, x0, HEAP, lsl #32
    // 0x14a7454: r16 = 16.000000
    //     0x14a7454: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14a7458: ldr             x16, [x16, #0x188]
    // 0x14a745c: r30 = Instance_Color
    //     0x14a745c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a7460: stp             lr, x16, [SP]
    // 0x14a7464: mov             x1, x0
    // 0x14a7468: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a7468: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a746c: ldr             x4, [x4, #0xaa0]
    // 0x14a7470: r0 = copyWith()
    //     0x14a7470: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a7474: stur            x0, [fp, #-8]
    // 0x14a7478: r0 = Text()
    //     0x14a7478: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a747c: mov             x1, x0
    // 0x14a7480: ldur            x0, [fp, #-0x30]
    // 0x14a7484: stur            x1, [fp, #-0x38]
    // 0x14a7488: StoreField: r1->field_b = r0
    //     0x14a7488: stur            w0, [x1, #0xb]
    // 0x14a748c: ldur            x0, [fp, #-8]
    // 0x14a7490: StoreField: r1->field_13 = r0
    //     0x14a7490: stur            w0, [x1, #0x13]
    // 0x14a7494: r0 = MaterialButton()
    //     0x14a7494: bl              #0x131db78  ; AllocateMaterialButtonStub -> MaterialButton (size=0x88)
    // 0x14a7498: mov             x1, x0
    // 0x14a749c: ldur            x0, [fp, #-0x28]
    // 0x14a74a0: stur            x1, [fp, #-8]
    // 0x14a74a4: StoreField: r1->field_b = r0
    //     0x14a74a4: stur            w0, [x1, #0xb]
    // 0x14a74a8: r0 = Instance_Color
    //     0x14a74a8: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14a74ac: StoreField: r1->field_1f = r0
    //     0x14a74ac: stur            w0, [x1, #0x1f]
    // 0x14a74b0: ldur            x0, [fp, #-0x20]
    // 0x14a74b4: StoreField: r1->field_23 = r0
    //     0x14a74b4: stur            w0, [x1, #0x23]
    // 0x14a74b8: r0 = Instance_Color
    //     0x14a74b8: add             x0, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0x14a74bc: ldr             x0, [x0, #0x7b8]
    // 0x14a74c0: StoreField: r1->field_27 = r0
    //     0x14a74c0: stur            w0, [x1, #0x27]
    // 0x14a74c4: r0 = Instance_RoundedRectangleBorder
    //     0x14a74c4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x14a74c8: ldr             x0, [x0, #0x888]
    // 0x14a74cc: StoreField: r1->field_5b = r0
    //     0x14a74cc: stur            w0, [x1, #0x5b]
    // 0x14a74d0: r0 = Instance_Clip
    //     0x14a74d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a74d4: ldr             x0, [x0, #0x38]
    // 0x14a74d8: StoreField: r1->field_5f = r0
    //     0x14a74d8: stur            w0, [x1, #0x5f]
    // 0x14a74dc: r2 = false
    //     0x14a74dc: add             x2, NULL, #0x30  ; false
    // 0x14a74e0: StoreField: r1->field_67 = r2
    //     0x14a74e0: stur            w2, [x1, #0x67]
    // 0x14a74e4: ldur            d0, [fp, #-0x40]
    // 0x14a74e8: StoreField: r1->field_73 = d0
    //     0x14a74e8: stur            d0, [x1, #0x73]
    // 0x14a74ec: ldur            d0, [fp, #-0x48]
    // 0x14a74f0: StoreField: r1->field_7b = d0
    //     0x14a74f0: stur            d0, [x1, #0x7b]
    // 0x14a74f4: r3 = true
    //     0x14a74f4: add             x3, NULL, #0x20  ; true
    // 0x14a74f8: StoreField: r1->field_83 = r3
    //     0x14a74f8: stur            w3, [x1, #0x83]
    // 0x14a74fc: ldur            x3, [fp, #-0x38]
    // 0x14a7500: StoreField: r1->field_4f = r3
    //     0x14a7500: stur            w3, [x1, #0x4f]
    // 0x14a7504: r3 = Instance_ValueKey
    //     0x14a7504: add             x3, PP, #0x37, lsl #12  ; [pp+0x37098] Obj!ValueKey<String>@d5b3c1
    //     0x14a7508: ldr             x3, [x3, #0x98]
    // 0x14a750c: StoreField: r1->field_7 = r3
    //     0x14a750c: stur            w3, [x1, #7]
    // 0x14a7510: r0 = Padding()
    //     0x14a7510: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a7514: mov             x1, x0
    // 0x14a7518: r0 = Instance_EdgeInsets
    //     0x14a7518: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x14a751c: ldr             x0, [x0, #0xf98]
    // 0x14a7520: stur            x1, [fp, #-0x20]
    // 0x14a7524: StoreField: r1->field_f = r0
    //     0x14a7524: stur            w0, [x1, #0xf]
    // 0x14a7528: ldur            x0, [fp, #-8]
    // 0x14a752c: StoreField: r1->field_b = r0
    //     0x14a752c: stur            w0, [x1, #0xb]
    // 0x14a7530: r0 = Container()
    //     0x14a7530: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a7534: stur            x0, [fp, #-8]
    // 0x14a7538: r16 = Instance_BoxDecoration
    //     0x14a7538: add             x16, PP, #0x40, lsl #12  ; [pp+0x40280] Obj!BoxDecoration@d64ce1
    //     0x14a753c: ldr             x16, [x16, #0x280]
    // 0x14a7540: ldur            lr, [fp, #-0x20]
    // 0x14a7544: stp             lr, x16, [SP]
    // 0x14a7548: mov             x1, x0
    // 0x14a754c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14a754c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14a7550: ldr             x4, [x4, #0x88]
    // 0x14a7554: r0 = Container()
    //     0x14a7554: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a7558: r0 = Visibility()
    //     0x14a7558: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14a755c: mov             x3, x0
    // 0x14a7560: ldur            x0, [fp, #-8]
    // 0x14a7564: stur            x3, [fp, #-0x20]
    // 0x14a7568: StoreField: r3->field_b = r0
    //     0x14a7568: stur            w0, [x3, #0xb]
    // 0x14a756c: r0 = Instance_SizedBox
    //     0x14a756c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14a7570: StoreField: r3->field_f = r0
    //     0x14a7570: stur            w0, [x3, #0xf]
    // 0x14a7574: ldur            x0, [fp, #-0x18]
    // 0x14a7578: StoreField: r3->field_13 = r0
    //     0x14a7578: stur            w0, [x3, #0x13]
    // 0x14a757c: r0 = false
    //     0x14a757c: add             x0, NULL, #0x30  ; false
    // 0x14a7580: ArrayStore: r3[0] = r0  ; List_4
    //     0x14a7580: stur            w0, [x3, #0x17]
    // 0x14a7584: StoreField: r3->field_1b = r0
    //     0x14a7584: stur            w0, [x3, #0x1b]
    // 0x14a7588: StoreField: r3->field_1f = r0
    //     0x14a7588: stur            w0, [x3, #0x1f]
    // 0x14a758c: StoreField: r3->field_23 = r0
    //     0x14a758c: stur            w0, [x3, #0x23]
    // 0x14a7590: StoreField: r3->field_27 = r0
    //     0x14a7590: stur            w0, [x3, #0x27]
    // 0x14a7594: StoreField: r3->field_2b = r0
    //     0x14a7594: stur            w0, [x3, #0x2b]
    // 0x14a7598: r1 = Null
    //     0x14a7598: mov             x1, NULL
    // 0x14a759c: r2 = 2
    //     0x14a759c: movz            x2, #0x2
    // 0x14a75a0: r0 = AllocateArray()
    //     0x14a75a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a75a4: mov             x2, x0
    // 0x14a75a8: ldur            x0, [fp, #-0x20]
    // 0x14a75ac: stur            x2, [fp, #-8]
    // 0x14a75b0: StoreField: r2->field_f = r0
    //     0x14a75b0: stur            w0, [x2, #0xf]
    // 0x14a75b4: r1 = <Widget>
    //     0x14a75b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a75b8: r0 = AllocateGrowableArray()
    //     0x14a75b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a75bc: mov             x1, x0
    // 0x14a75c0: ldur            x0, [fp, #-8]
    // 0x14a75c4: stur            x1, [fp, #-0x18]
    // 0x14a75c8: StoreField: r1->field_f = r0
    //     0x14a75c8: stur            w0, [x1, #0xf]
    // 0x14a75cc: r0 = 2
    //     0x14a75cc: movz            x0, #0x2
    // 0x14a75d0: StoreField: r1->field_b = r0
    //     0x14a75d0: stur            w0, [x1, #0xb]
    // 0x14a75d4: r0 = Column()
    //     0x14a75d4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14a75d8: mov             x2, x0
    // 0x14a75dc: r0 = Instance_Axis
    //     0x14a75dc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a75e0: stur            x2, [fp, #-8]
    // 0x14a75e4: StoreField: r2->field_f = r0
    //     0x14a75e4: stur            w0, [x2, #0xf]
    // 0x14a75e8: r0 = Instance_MainAxisAlignment
    //     0x14a75e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a75ec: ldr             x0, [x0, #0xa08]
    // 0x14a75f0: StoreField: r2->field_13 = r0
    //     0x14a75f0: stur            w0, [x2, #0x13]
    // 0x14a75f4: r0 = Instance_MainAxisSize
    //     0x14a75f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a75f8: ldr             x0, [x0, #0xa10]
    // 0x14a75fc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14a75fc: stur            w0, [x2, #0x17]
    // 0x14a7600: r0 = Instance_CrossAxisAlignment
    //     0x14a7600: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14a7604: ldr             x0, [x0, #0xa18]
    // 0x14a7608: StoreField: r2->field_1b = r0
    //     0x14a7608: stur            w0, [x2, #0x1b]
    // 0x14a760c: r0 = Instance_VerticalDirection
    //     0x14a760c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a7610: ldr             x0, [x0, #0xa20]
    // 0x14a7614: StoreField: r2->field_23 = r0
    //     0x14a7614: stur            w0, [x2, #0x23]
    // 0x14a7618: r0 = Instance_Clip
    //     0x14a7618: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a761c: ldr             x0, [x0, #0x38]
    // 0x14a7620: StoreField: r2->field_2b = r0
    //     0x14a7620: stur            w0, [x2, #0x2b]
    // 0x14a7624: StoreField: r2->field_2f = rZR
    //     0x14a7624: stur            xzr, [x2, #0x2f]
    // 0x14a7628: ldur            x0, [fp, #-0x18]
    // 0x14a762c: StoreField: r2->field_b = r0
    //     0x14a762c: stur            w0, [x2, #0xb]
    // 0x14a7630: r1 = <StackParentData>
    //     0x14a7630: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14a7634: ldr             x1, [x1, #0x8e0]
    // 0x14a7638: r0 = Positioned()
    //     0x14a7638: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14a763c: mov             x3, x0
    // 0x14a7640: r0 = 0.000000
    //     0x14a7640: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14a7644: stur            x3, [fp, #-0x18]
    // 0x14a7648: StoreField: r3->field_13 = r0
    //     0x14a7648: stur            w0, [x3, #0x13]
    // 0x14a764c: StoreField: r3->field_1b = r0
    //     0x14a764c: stur            w0, [x3, #0x1b]
    // 0x14a7650: StoreField: r3->field_1f = r0
    //     0x14a7650: stur            w0, [x3, #0x1f]
    // 0x14a7654: ldur            x0, [fp, #-8]
    // 0x14a7658: StoreField: r3->field_b = r0
    //     0x14a7658: stur            w0, [x3, #0xb]
    // 0x14a765c: r1 = Null
    //     0x14a765c: mov             x1, NULL
    // 0x14a7660: r2 = 4
    //     0x14a7660: movz            x2, #0x4
    // 0x14a7664: r0 = AllocateArray()
    //     0x14a7664: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a7668: mov             x2, x0
    // 0x14a766c: ldur            x0, [fp, #-0x10]
    // 0x14a7670: stur            x2, [fp, #-8]
    // 0x14a7674: StoreField: r2->field_f = r0
    //     0x14a7674: stur            w0, [x2, #0xf]
    // 0x14a7678: ldur            x0, [fp, #-0x18]
    // 0x14a767c: StoreField: r2->field_13 = r0
    //     0x14a767c: stur            w0, [x2, #0x13]
    // 0x14a7680: r1 = <Widget>
    //     0x14a7680: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a7684: r0 = AllocateGrowableArray()
    //     0x14a7684: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a7688: mov             x1, x0
    // 0x14a768c: ldur            x0, [fp, #-8]
    // 0x14a7690: stur            x1, [fp, #-0x10]
    // 0x14a7694: StoreField: r1->field_f = r0
    //     0x14a7694: stur            w0, [x1, #0xf]
    // 0x14a7698: r0 = 4
    //     0x14a7698: movz            x0, #0x4
    // 0x14a769c: StoreField: r1->field_b = r0
    //     0x14a769c: stur            w0, [x1, #0xb]
    // 0x14a76a0: r0 = Stack()
    //     0x14a76a0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14a76a4: r1 = Instance_AlignmentDirectional
    //     0x14a76a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0x14a76a8: ldr             x1, [x1, #0xd08]
    // 0x14a76ac: StoreField: r0->field_f = r1
    //     0x14a76ac: stur            w1, [x0, #0xf]
    // 0x14a76b0: r1 = Instance_StackFit
    //     0x14a76b0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14a76b4: ldr             x1, [x1, #0xfa8]
    // 0x14a76b8: ArrayStore: r0[0] = r1  ; List_4
    //     0x14a76b8: stur            w1, [x0, #0x17]
    // 0x14a76bc: r1 = Instance_Clip
    //     0x14a76bc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14a76c0: ldr             x1, [x1, #0x7e0]
    // 0x14a76c4: StoreField: r0->field_1b = r1
    //     0x14a76c4: stur            w1, [x0, #0x1b]
    // 0x14a76c8: ldur            x1, [fp, #-0x10]
    // 0x14a76cc: StoreField: r0->field_b = r1
    //     0x14a76cc: stur            w1, [x0, #0xb]
    // 0x14a76d0: LeaveFrame
    //     0x14a76d0: mov             SP, fp
    //     0x14a76d4: ldp             fp, lr, [SP], #0x10
    // 0x14a76d8: ret
    //     0x14a76d8: ret             
    // 0x14a76dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a76dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a76e0: b               #0x14a72e4
  }
  _ _getCosmeticThemeLoginForm(/* No info */) {
    // ** addr: 0x14a76e4, size: 0xa8
    // 0x14a76e4: EnterFrame
    //     0x14a76e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14a76e8: mov             fp, SP
    // 0x14a76ec: AllocStack(0x18)
    //     0x14a76ec: sub             SP, SP, #0x18
    // 0x14a76f0: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14a76f0: stur            x1, [fp, #-8]
    //     0x14a76f4: stur            x2, [fp, #-0x10]
    // 0x14a76f8: CheckStackOverflow
    //     0x14a76f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a76fc: cmp             SP, x16
    //     0x14a7700: b.ls            #0x14a7784
    // 0x14a7704: r1 = 2
    //     0x14a7704: movz            x1, #0x2
    // 0x14a7708: r0 = AllocateContext()
    //     0x14a7708: bl              #0x16f6108  ; AllocateContextStub
    // 0x14a770c: mov             x2, x0
    // 0x14a7710: ldur            x0, [fp, #-8]
    // 0x14a7714: stur            x2, [fp, #-0x18]
    // 0x14a7718: StoreField: r2->field_f = r0
    //     0x14a7718: stur            w0, [x2, #0xf]
    // 0x14a771c: ldur            x1, [fp, #-0x10]
    // 0x14a7720: StoreField: r2->field_13 = r1
    //     0x14a7720: stur            w1, [x2, #0x13]
    // 0x14a7724: mov             x1, x0
    // 0x14a7728: r0 = controller()
    //     0x14a7728: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a772c: LoadField: r1 = r0->field_8b
    //     0x14a772c: ldur            w1, [x0, #0x8b]
    // 0x14a7730: DecompressPointer r1
    //     0x14a7730: add             x1, x1, HEAP, lsl #32
    // 0x14a7734: r0 = value()
    //     0x14a7734: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7738: cmp             w0, NULL
    // 0x14a773c: b.eq            #0x14a7744
    // 0x14a7740: tbnz            w0, #4, #0x14a7754
    // 0x14a7744: ldur            x1, [fp, #-8]
    // 0x14a7748: r0 = controller()
    //     0x14a7748: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a774c: mov             x1, x0
    // 0x14a7750: r0 = startTimer()
    //     0x14a7750: bl              #0x1402244  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::startTimer
    // 0x14a7754: r0 = Obx()
    //     0x14a7754: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14a7758: ldur            x2, [fp, #-0x18]
    // 0x14a775c: r1 = Function '<anonymous closure>':.
    //     0x14a775c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43238] AnonymousClosure: (0x14a778c), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::_getCosmeticThemeLoginForm (0x14a76e4)
    //     0x14a7760: ldr             x1, [x1, #0x238]
    // 0x14a7764: stur            x0, [fp, #-8]
    // 0x14a7768: r0 = AllocateClosure()
    //     0x14a7768: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a776c: mov             x1, x0
    // 0x14a7770: ldur            x0, [fp, #-8]
    // 0x14a7774: StoreField: r0->field_b = r1
    //     0x14a7774: stur            w1, [x0, #0xb]
    // 0x14a7778: LeaveFrame
    //     0x14a7778: mov             SP, fp
    //     0x14a777c: ldp             fp, lr, [SP], #0x10
    // 0x14a7780: ret
    //     0x14a7780: ret             
    // 0x14a7784: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a7784: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a7788: b               #0x14a7704
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x14a778c, size: 0xd60
    // 0x14a778c: EnterFrame
    //     0x14a778c: stp             fp, lr, [SP, #-0x10]!
    //     0x14a7790: mov             fp, SP
    // 0x14a7794: AllocStack(0xd0)
    //     0x14a7794: sub             SP, SP, #0xd0
    // 0x14a7798: SetupParameters()
    //     0x14a7798: ldr             x0, [fp, #0x10]
    //     0x14a779c: ldur            w2, [x0, #0x17]
    //     0x14a77a0: add             x2, x2, HEAP, lsl #32
    //     0x14a77a4: stur            x2, [fp, #-8]
    // 0x14a77a8: CheckStackOverflow
    //     0x14a77a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a77ac: cmp             SP, x16
    //     0x14a77b0: b.ls            #0x14a84c8
    // 0x14a77b4: LoadField: r1 = r2->field_f
    //     0x14a77b4: ldur            w1, [x2, #0xf]
    // 0x14a77b8: DecompressPointer r1
    //     0x14a77b8: add             x1, x1, HEAP, lsl #32
    // 0x14a77bc: r0 = controller()
    //     0x14a77bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a77c0: LoadField: r1 = r0->field_8b
    //     0x14a77c0: ldur            w1, [x0, #0x8b]
    // 0x14a77c4: DecompressPointer r1
    //     0x14a77c4: add             x1, x1, HEAP, lsl #32
    // 0x14a77c8: r0 = value()
    //     0x14a77c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a77cc: cmp             w0, NULL
    // 0x14a77d0: b.eq            #0x14a77e8
    // 0x14a77d4: tbz             w0, #4, #0x14a77e8
    // 0x14a77d8: r1 = "enter mobile no."
    //     0x14a77d8: add             x1, PP, #0x43, lsl #12  ; [pp+0x43240] "enter mobile no."
    //     0x14a77dc: ldr             x1, [x1, #0x240]
    // 0x14a77e0: r0 = capitalizeFirstWord()
    //     0x14a77e0: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14a77e4: b               #0x14a77f0
    // 0x14a77e8: r0 = "Enter OTP"
    //     0x14a77e8: add             x0, PP, #0x40, lsl #12  ; [pp+0x402b8] "Enter OTP"
    //     0x14a77ec: ldr             x0, [x0, #0x2b8]
    // 0x14a77f0: ldur            x2, [fp, #-8]
    // 0x14a77f4: stur            x0, [fp, #-0x10]
    // 0x14a77f8: LoadField: r1 = r2->field_13
    //     0x14a77f8: ldur            w1, [x2, #0x13]
    // 0x14a77fc: DecompressPointer r1
    //     0x14a77fc: add             x1, x1, HEAP, lsl #32
    // 0x14a7800: r0 = of()
    //     0x14a7800: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a7804: LoadField: r1 = r0->field_87
    //     0x14a7804: ldur            w1, [x0, #0x87]
    // 0x14a7808: DecompressPointer r1
    //     0x14a7808: add             x1, x1, HEAP, lsl #32
    // 0x14a780c: LoadField: r0 = r1->field_7
    //     0x14a780c: ldur            w0, [x1, #7]
    // 0x14a7810: DecompressPointer r0
    //     0x14a7810: add             x0, x0, HEAP, lsl #32
    // 0x14a7814: r16 = 16.000000
    //     0x14a7814: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14a7818: ldr             x16, [x16, #0x188]
    // 0x14a781c: r30 = Instance_Color
    //     0x14a781c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a7820: stp             lr, x16, [SP]
    // 0x14a7824: mov             x1, x0
    // 0x14a7828: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a7828: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a782c: ldr             x4, [x4, #0xaa0]
    // 0x14a7830: r0 = copyWith()
    //     0x14a7830: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a7834: stur            x0, [fp, #-0x18]
    // 0x14a7838: r0 = Text()
    //     0x14a7838: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a783c: mov             x1, x0
    // 0x14a7840: ldur            x0, [fp, #-0x10]
    // 0x14a7844: stur            x1, [fp, #-0x20]
    // 0x14a7848: StoreField: r1->field_b = r0
    //     0x14a7848: stur            w0, [x1, #0xb]
    // 0x14a784c: ldur            x0, [fp, #-0x18]
    // 0x14a7850: StoreField: r1->field_13 = r0
    //     0x14a7850: stur            w0, [x1, #0x13]
    // 0x14a7854: r0 = Padding()
    //     0x14a7854: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a7858: mov             x2, x0
    // 0x14a785c: r0 = Instance_EdgeInsets
    //     0x14a785c: add             x0, PP, #0x40, lsl #12  ; [pp+0x402c0] Obj!EdgeInsets@d59e11
    //     0x14a7860: ldr             x0, [x0, #0x2c0]
    // 0x14a7864: stur            x2, [fp, #-0x10]
    // 0x14a7868: StoreField: r2->field_f = r0
    //     0x14a7868: stur            w0, [x2, #0xf]
    // 0x14a786c: ldur            x0, [fp, #-0x20]
    // 0x14a7870: StoreField: r2->field_b = r0
    //     0x14a7870: stur            w0, [x2, #0xb]
    // 0x14a7874: ldur            x0, [fp, #-8]
    // 0x14a7878: LoadField: r1 = r0->field_f
    //     0x14a7878: ldur            w1, [x0, #0xf]
    // 0x14a787c: DecompressPointer r1
    //     0x14a787c: add             x1, x1, HEAP, lsl #32
    // 0x14a7880: r0 = controller()
    //     0x14a7880: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a7884: LoadField: r1 = r0->field_8b
    //     0x14a7884: ldur            w1, [x0, #0x8b]
    // 0x14a7888: DecompressPointer r1
    //     0x14a7888: add             x1, x1, HEAP, lsl #32
    // 0x14a788c: r0 = value()
    //     0x14a788c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7890: cmp             w0, NULL
    // 0x14a7894: b.ne            #0x14a78a0
    // 0x14a7898: r3 = true
    //     0x14a7898: add             x3, NULL, #0x20  ; true
    // 0x14a789c: b               #0x14a78a4
    // 0x14a78a0: mov             x3, x0
    // 0x14a78a4: ldur            x0, [fp, #-8]
    // 0x14a78a8: stur            x3, [fp, #-0x18]
    // 0x14a78ac: r1 = Null
    //     0x14a78ac: mov             x1, NULL
    // 0x14a78b0: r2 = 4
    //     0x14a78b0: movz            x2, #0x4
    // 0x14a78b4: r0 = AllocateArray()
    //     0x14a78b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a78b8: r16 = "A 4 digit code has been sent on "
    //     0x14a78b8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37110] "A 4 digit code has been sent on "
    //     0x14a78bc: ldr             x16, [x16, #0x110]
    // 0x14a78c0: StoreField: r0->field_f = r16
    //     0x14a78c0: stur            w16, [x0, #0xf]
    // 0x14a78c4: ldur            x2, [fp, #-8]
    // 0x14a78c8: LoadField: r1 = r2->field_f
    //     0x14a78c8: ldur            w1, [x2, #0xf]
    // 0x14a78cc: DecompressPointer r1
    //     0x14a78cc: add             x1, x1, HEAP, lsl #32
    // 0x14a78d0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x14a78d0: ldur            w3, [x1, #0x17]
    // 0x14a78d4: DecompressPointer r3
    //     0x14a78d4: add             x3, x3, HEAP, lsl #32
    // 0x14a78d8: StoreField: r0->field_13 = r3
    //     0x14a78d8: stur            w3, [x0, #0x13]
    // 0x14a78dc: str             x0, [SP]
    // 0x14a78e0: r0 = _interpolate()
    //     0x14a78e0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14a78e4: ldur            x2, [fp, #-8]
    // 0x14a78e8: stur            x0, [fp, #-0x20]
    // 0x14a78ec: LoadField: r1 = r2->field_13
    //     0x14a78ec: ldur            w1, [x2, #0x13]
    // 0x14a78f0: DecompressPointer r1
    //     0x14a78f0: add             x1, x1, HEAP, lsl #32
    // 0x14a78f4: r0 = of()
    //     0x14a78f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a78f8: LoadField: r1 = r0->field_87
    //     0x14a78f8: ldur            w1, [x0, #0x87]
    // 0x14a78fc: DecompressPointer r1
    //     0x14a78fc: add             x1, x1, HEAP, lsl #32
    // 0x14a7900: LoadField: r0 = r1->field_2b
    //     0x14a7900: ldur            w0, [x1, #0x2b]
    // 0x14a7904: DecompressPointer r0
    //     0x14a7904: add             x0, x0, HEAP, lsl #32
    // 0x14a7908: stur            x0, [fp, #-0x28]
    // 0x14a790c: r1 = Instance_Color
    //     0x14a790c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a7910: d0 = 0.400000
    //     0x14a7910: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14a7914: r0 = withOpacity()
    //     0x14a7914: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a7918: r16 = 14.000000
    //     0x14a7918: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14a791c: ldr             x16, [x16, #0x1d8]
    // 0x14a7920: stp             x0, x16, [SP]
    // 0x14a7924: ldur            x1, [fp, #-0x28]
    // 0x14a7928: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a7928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a792c: ldr             x4, [x4, #0xaa0]
    // 0x14a7930: r0 = copyWith()
    //     0x14a7930: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a7934: stur            x0, [fp, #-0x28]
    // 0x14a7938: r0 = Text()
    //     0x14a7938: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a793c: mov             x1, x0
    // 0x14a7940: ldur            x0, [fp, #-0x20]
    // 0x14a7944: stur            x1, [fp, #-0x30]
    // 0x14a7948: StoreField: r1->field_b = r0
    //     0x14a7948: stur            w0, [x1, #0xb]
    // 0x14a794c: ldur            x0, [fp, #-0x28]
    // 0x14a7950: StoreField: r1->field_13 = r0
    //     0x14a7950: stur            w0, [x1, #0x13]
    // 0x14a7954: r0 = Padding()
    //     0x14a7954: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a7958: mov             x1, x0
    // 0x14a795c: r0 = Instance_EdgeInsets
    //     0x14a795c: add             x0, PP, #0x40, lsl #12  ; [pp+0x402c8] Obj!EdgeInsets@d59de1
    //     0x14a7960: ldr             x0, [x0, #0x2c8]
    // 0x14a7964: stur            x1, [fp, #-0x20]
    // 0x14a7968: StoreField: r1->field_f = r0
    //     0x14a7968: stur            w0, [x1, #0xf]
    // 0x14a796c: ldur            x0, [fp, #-0x30]
    // 0x14a7970: StoreField: r1->field_b = r0
    //     0x14a7970: stur            w0, [x1, #0xb]
    // 0x14a7974: r0 = Visibility()
    //     0x14a7974: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14a7978: mov             x2, x0
    // 0x14a797c: ldur            x0, [fp, #-0x20]
    // 0x14a7980: stur            x2, [fp, #-0x28]
    // 0x14a7984: StoreField: r2->field_b = r0
    //     0x14a7984: stur            w0, [x2, #0xb]
    // 0x14a7988: r0 = Instance_SizedBox
    //     0x14a7988: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14a798c: StoreField: r2->field_f = r0
    //     0x14a798c: stur            w0, [x2, #0xf]
    // 0x14a7990: ldur            x1, [fp, #-0x18]
    // 0x14a7994: StoreField: r2->field_13 = r1
    //     0x14a7994: stur            w1, [x2, #0x13]
    // 0x14a7998: r3 = false
    //     0x14a7998: add             x3, NULL, #0x30  ; false
    // 0x14a799c: ArrayStore: r2[0] = r3  ; List_4
    //     0x14a799c: stur            w3, [x2, #0x17]
    // 0x14a79a0: StoreField: r2->field_1b = r3
    //     0x14a79a0: stur            w3, [x2, #0x1b]
    // 0x14a79a4: StoreField: r2->field_1f = r3
    //     0x14a79a4: stur            w3, [x2, #0x1f]
    // 0x14a79a8: StoreField: r2->field_23 = r3
    //     0x14a79a8: stur            w3, [x2, #0x23]
    // 0x14a79ac: StoreField: r2->field_27 = r3
    //     0x14a79ac: stur            w3, [x2, #0x27]
    // 0x14a79b0: StoreField: r2->field_2b = r3
    //     0x14a79b0: stur            w3, [x2, #0x2b]
    // 0x14a79b4: ldur            x4, [fp, #-8]
    // 0x14a79b8: LoadField: r1 = r4->field_f
    //     0x14a79b8: ldur            w1, [x4, #0xf]
    // 0x14a79bc: DecompressPointer r1
    //     0x14a79bc: add             x1, x1, HEAP, lsl #32
    // 0x14a79c0: r0 = controller()
    //     0x14a79c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a79c4: LoadField: r1 = r0->field_8b
    //     0x14a79c4: ldur            w1, [x0, #0x8b]
    // 0x14a79c8: DecompressPointer r1
    //     0x14a79c8: add             x1, x1, HEAP, lsl #32
    // 0x14a79cc: r0 = value()
    //     0x14a79cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a79d0: cmp             w0, NULL
    // 0x14a79d4: b.ne            #0x14a79dc
    // 0x14a79d8: r0 = true
    //     0x14a79d8: add             x0, NULL, #0x20  ; true
    // 0x14a79dc: ldur            x2, [fp, #-8]
    // 0x14a79e0: eor             x1, x0, #0x10
    // 0x14a79e4: stur            x1, [fp, #-0x30]
    // 0x14a79e8: LoadField: r0 = r2->field_f
    //     0x14a79e8: ldur            w0, [x2, #0xf]
    // 0x14a79ec: DecompressPointer r0
    //     0x14a79ec: add             x0, x0, HEAP, lsl #32
    // 0x14a79f0: stur            x0, [fp, #-0x20]
    // 0x14a79f4: LoadField: r3 = r0->field_13
    //     0x14a79f4: ldur            w3, [x0, #0x13]
    // 0x14a79f8: DecompressPointer r3
    //     0x14a79f8: add             x3, x3, HEAP, lsl #32
    // 0x14a79fc: stur            x3, [fp, #-0x18]
    // 0x14a7a00: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0x14a7a00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14a7a04: ldr             x0, [x0, #0x1530]
    //     0x14a7a08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14a7a0c: cmp             w0, w16
    //     0x14a7a10: b.ne            #0x14a7a20
    //     0x14a7a14: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0x14a7a18: ldr             x2, [x2, #0x120]
    //     0x14a7a1c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14a7a20: stur            x0, [fp, #-0x38]
    // 0x14a7a24: r16 = "[0-9]"
    //     0x14a7a24: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0x14a7a28: ldr             x16, [x16, #0x128]
    // 0x14a7a2c: stp             x16, NULL, [SP, #0x20]
    // 0x14a7a30: r16 = false
    //     0x14a7a30: add             x16, NULL, #0x30  ; false
    // 0x14a7a34: r30 = true
    //     0x14a7a34: add             lr, NULL, #0x20  ; true
    // 0x14a7a38: stp             lr, x16, [SP, #0x10]
    // 0x14a7a3c: r16 = false
    //     0x14a7a3c: add             x16, NULL, #0x30  ; false
    // 0x14a7a40: r30 = false
    //     0x14a7a40: add             lr, NULL, #0x30  ; false
    // 0x14a7a44: stp             lr, x16, [SP]
    // 0x14a7a48: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x14a7a48: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x14a7a4c: r0 = _RegExp()
    //     0x14a7a4c: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x14a7a50: stur            x0, [fp, #-0x40]
    // 0x14a7a54: r0 = FilteringTextInputFormatter()
    //     0x14a7a54: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0x14a7a58: mov             x1, x0
    // 0x14a7a5c: ldur            x0, [fp, #-0x40]
    // 0x14a7a60: stur            x1, [fp, #-0x48]
    // 0x14a7a64: StoreField: r1->field_b = r0
    //     0x14a7a64: stur            w0, [x1, #0xb]
    // 0x14a7a68: r0 = true
    //     0x14a7a68: add             x0, NULL, #0x20  ; true
    // 0x14a7a6c: StoreField: r1->field_7 = r0
    //     0x14a7a6c: stur            w0, [x1, #7]
    // 0x14a7a70: r2 = ""
    //     0x14a7a70: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a7a74: StoreField: r1->field_f = r2
    //     0x14a7a74: stur            w2, [x1, #0xf]
    // 0x14a7a78: r0 = LengthLimitingTextInputFormatter()
    //     0x14a7a78: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14a7a7c: mov             x3, x0
    // 0x14a7a80: r0 = 20
    //     0x14a7a80: movz            x0, #0x14
    // 0x14a7a84: stur            x3, [fp, #-0x40]
    // 0x14a7a88: StoreField: r3->field_7 = r0
    //     0x14a7a88: stur            w0, [x3, #7]
    // 0x14a7a8c: r1 = Null
    //     0x14a7a8c: mov             x1, NULL
    // 0x14a7a90: r2 = 6
    //     0x14a7a90: movz            x2, #0x6
    // 0x14a7a94: r0 = AllocateArray()
    //     0x14a7a94: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a7a98: mov             x2, x0
    // 0x14a7a9c: ldur            x0, [fp, #-0x38]
    // 0x14a7aa0: stur            x2, [fp, #-0x50]
    // 0x14a7aa4: StoreField: r2->field_f = r0
    //     0x14a7aa4: stur            w0, [x2, #0xf]
    // 0x14a7aa8: ldur            x1, [fp, #-0x48]
    // 0x14a7aac: StoreField: r2->field_13 = r1
    //     0x14a7aac: stur            w1, [x2, #0x13]
    // 0x14a7ab0: ldur            x1, [fp, #-0x40]
    // 0x14a7ab4: ArrayStore: r2[0] = r1  ; List_4
    //     0x14a7ab4: stur            w1, [x2, #0x17]
    // 0x14a7ab8: r1 = <TextInputFormatter>
    //     0x14a7ab8: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14a7abc: ldr             x1, [x1, #0x7b0]
    // 0x14a7ac0: r0 = AllocateGrowableArray()
    //     0x14a7ac0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a7ac4: mov             x2, x0
    // 0x14a7ac8: ldur            x0, [fp, #-0x50]
    // 0x14a7acc: stur            x2, [fp, #-0x40]
    // 0x14a7ad0: StoreField: r2->field_f = r0
    //     0x14a7ad0: stur            w0, [x2, #0xf]
    // 0x14a7ad4: r0 = 6
    //     0x14a7ad4: movz            x0, #0x6
    // 0x14a7ad8: StoreField: r2->field_b = r0
    //     0x14a7ad8: stur            w0, [x2, #0xb]
    // 0x14a7adc: ldur            x0, [fp, #-8]
    // 0x14a7ae0: LoadField: r1 = r0->field_13
    //     0x14a7ae0: ldur            w1, [x0, #0x13]
    // 0x14a7ae4: DecompressPointer r1
    //     0x14a7ae4: add             x1, x1, HEAP, lsl #32
    // 0x14a7ae8: r0 = of()
    //     0x14a7ae8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a7aec: LoadField: r1 = r0->field_87
    //     0x14a7aec: ldur            w1, [x0, #0x87]
    // 0x14a7af0: DecompressPointer r1
    //     0x14a7af0: add             x1, x1, HEAP, lsl #32
    // 0x14a7af4: LoadField: r0 = r1->field_2b
    //     0x14a7af4: ldur            w0, [x1, #0x2b]
    // 0x14a7af8: DecompressPointer r0
    //     0x14a7af8: add             x0, x0, HEAP, lsl #32
    // 0x14a7afc: r16 = Instance_Color
    //     0x14a7afc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a7b00: r30 = 12.000000
    //     0x14a7b00: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a7b04: ldr             lr, [lr, #0x9e8]
    // 0x14a7b08: stp             lr, x16, [SP]
    // 0x14a7b0c: mov             x1, x0
    // 0x14a7b10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14a7b10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14a7b14: ldr             x4, [x4, #0x9b8]
    // 0x14a7b18: r0 = copyWith()
    //     0x14a7b18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a7b1c: ldur            x2, [fp, #-8]
    // 0x14a7b20: stur            x0, [fp, #-0x48]
    // 0x14a7b24: LoadField: r1 = r2->field_13
    //     0x14a7b24: ldur            w1, [x2, #0x13]
    // 0x14a7b28: DecompressPointer r1
    //     0x14a7b28: add             x1, x1, HEAP, lsl #32
    // 0x14a7b2c: r0 = getTextFormFieldInputDecorationCircle()
    //     0x14a7b2c: bl              #0xac2dd8  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircle
    // 0x14a7b30: ldur            x2, [fp, #-8]
    // 0x14a7b34: stur            x0, [fp, #-0x50]
    // 0x14a7b38: LoadField: r1 = r2->field_13
    //     0x14a7b38: ldur            w1, [x2, #0x13]
    // 0x14a7b3c: DecompressPointer r1
    //     0x14a7b3c: add             x1, x1, HEAP, lsl #32
    // 0x14a7b40: r0 = of()
    //     0x14a7b40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a7b44: LoadField: r1 = r0->field_87
    //     0x14a7b44: ldur            w1, [x0, #0x87]
    // 0x14a7b48: DecompressPointer r1
    //     0x14a7b48: add             x1, x1, HEAP, lsl #32
    // 0x14a7b4c: LoadField: r0 = r1->field_2b
    //     0x14a7b4c: ldur            w0, [x1, #0x2b]
    // 0x14a7b50: DecompressPointer r0
    //     0x14a7b50: add             x0, x0, HEAP, lsl #32
    // 0x14a7b54: stur            x0, [fp, #-0x58]
    // 0x14a7b58: r1 = Instance_Color
    //     0x14a7b58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a7b5c: d0 = 0.400000
    //     0x14a7b5c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14a7b60: r0 = withOpacity()
    //     0x14a7b60: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a7b64: r16 = 12.000000
    //     0x14a7b64: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14a7b68: ldr             x16, [x16, #0x9e8]
    // 0x14a7b6c: stp             x0, x16, [SP]
    // 0x14a7b70: ldur            x1, [fp, #-0x58]
    // 0x14a7b74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a7b74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a7b78: ldr             x4, [x4, #0xaa0]
    // 0x14a7b7c: r0 = copyWith()
    //     0x14a7b7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a7b80: ldur            x2, [fp, #-8]
    // 0x14a7b84: stur            x0, [fp, #-0x58]
    // 0x14a7b88: LoadField: r1 = r2->field_f
    //     0x14a7b88: ldur            w1, [x2, #0xf]
    // 0x14a7b8c: DecompressPointer r1
    //     0x14a7b8c: add             x1, x1, HEAP, lsl #32
    // 0x14a7b90: r0 = controller()
    //     0x14a7b90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a7b94: LoadField: r1 = r0->field_bb
    //     0x14a7b94: ldur            w1, [x0, #0xbb]
    // 0x14a7b98: DecompressPointer r1
    //     0x14a7b98: add             x1, x1, HEAP, lsl #32
    // 0x14a7b9c: r0 = value()
    //     0x14a7b9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7ba0: tbnz            w0, #4, #0x14a7c38
    // 0x14a7ba4: ldur            x2, [fp, #-8]
    // 0x14a7ba8: LoadField: r1 = r2->field_f
    //     0x14a7ba8: ldur            w1, [x2, #0xf]
    // 0x14a7bac: DecompressPointer r1
    //     0x14a7bac: add             x1, x1, HEAP, lsl #32
    // 0x14a7bb0: r0 = controller()
    //     0x14a7bb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a7bb4: LoadField: r1 = r0->field_b7
    //     0x14a7bb4: ldur            w1, [x0, #0xb7]
    // 0x14a7bb8: DecompressPointer r1
    //     0x14a7bb8: add             x1, x1, HEAP, lsl #32
    // 0x14a7bbc: r0 = value()
    //     0x14a7bbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7bc0: tbnz            w0, #4, #0x14a7bd0
    // 0x14a7bc4: r0 = Instance_IconData
    //     0x14a7bc4: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0x14a7bc8: ldr             x0, [x0, #0x130]
    // 0x14a7bcc: b               #0x14a7bd8
    // 0x14a7bd0: r0 = Instance_IconData
    //     0x14a7bd0: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0x14a7bd4: ldr             x0, [x0, #0x138]
    // 0x14a7bd8: ldur            x2, [fp, #-8]
    // 0x14a7bdc: stur            x0, [fp, #-0x60]
    // 0x14a7be0: LoadField: r1 = r2->field_f
    //     0x14a7be0: ldur            w1, [x2, #0xf]
    // 0x14a7be4: DecompressPointer r1
    //     0x14a7be4: add             x1, x1, HEAP, lsl #32
    // 0x14a7be8: r0 = controller()
    //     0x14a7be8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a7bec: LoadField: r1 = r0->field_b7
    //     0x14a7bec: ldur            w1, [x0, #0xb7]
    // 0x14a7bf0: DecompressPointer r1
    //     0x14a7bf0: add             x1, x1, HEAP, lsl #32
    // 0x14a7bf4: r0 = value()
    //     0x14a7bf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7bf8: tbnz            w0, #4, #0x14a7c08
    // 0x14a7bfc: r1 = Instance_Color
    //     0x14a7bfc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x14a7c00: ldr             x1, [x1, #0x858]
    // 0x14a7c04: b               #0x14a7c10
    // 0x14a7c08: r1 = Instance_Color
    //     0x14a7c08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14a7c0c: ldr             x1, [x1, #0x50]
    // 0x14a7c10: ldur            x0, [fp, #-0x60]
    // 0x14a7c14: stur            x1, [fp, #-0x68]
    // 0x14a7c18: r0 = Icon()
    //     0x14a7c18: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x14a7c1c: mov             x1, x0
    // 0x14a7c20: ldur            x0, [fp, #-0x60]
    // 0x14a7c24: StoreField: r1->field_b = r0
    //     0x14a7c24: stur            w0, [x1, #0xb]
    // 0x14a7c28: ldur            x0, [fp, #-0x68]
    // 0x14a7c2c: StoreField: r1->field_23 = r0
    //     0x14a7c2c: stur            w0, [x1, #0x23]
    // 0x14a7c30: mov             x3, x1
    // 0x14a7c34: b               #0x14a7c3c
    // 0x14a7c38: r3 = Null
    //     0x14a7c38: mov             x3, NULL
    // 0x14a7c3c: ldur            x2, [fp, #-8]
    // 0x14a7c40: ldur            x0, [fp, #-0x30]
    // 0x14a7c44: ldur            x1, [fp, #-0x18]
    // 0x14a7c48: stur            x3, [fp, #-0x60]
    // 0x14a7c4c: r0 = SvgPicture()
    //     0x14a7c4c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14a7c50: mov             x1, x0
    // 0x14a7c54: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0x14a7c54: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0x14a7c58: ldr             x2, [x2, #0x140]
    // 0x14a7c5c: stur            x0, [fp, #-0x68]
    // 0x14a7c60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14a7c60: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14a7c64: r0 = SvgPicture.asset()
    //     0x14a7c64: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14a7c68: ldur            x2, [fp, #-8]
    // 0x14a7c6c: LoadField: r1 = r2->field_13
    //     0x14a7c6c: ldur            w1, [x2, #0x13]
    // 0x14a7c70: DecompressPointer r1
    //     0x14a7c70: add             x1, x1, HEAP, lsl #32
    // 0x14a7c74: r0 = of()
    //     0x14a7c74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a7c78: LoadField: r1 = r0->field_87
    //     0x14a7c78: ldur            w1, [x0, #0x87]
    // 0x14a7c7c: DecompressPointer r1
    //     0x14a7c7c: add             x1, x1, HEAP, lsl #32
    // 0x14a7c80: LoadField: r0 = r1->field_2b
    //     0x14a7c80: ldur            w0, [x1, #0x2b]
    // 0x14a7c84: DecompressPointer r0
    //     0x14a7c84: add             x0, x0, HEAP, lsl #32
    // 0x14a7c88: ldur            x2, [fp, #-8]
    // 0x14a7c8c: stur            x0, [fp, #-0x70]
    // 0x14a7c90: LoadField: r1 = r2->field_13
    //     0x14a7c90: ldur            w1, [x2, #0x13]
    // 0x14a7c94: DecompressPointer r1
    //     0x14a7c94: add             x1, x1, HEAP, lsl #32
    // 0x14a7c98: r0 = of()
    //     0x14a7c98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a7c9c: LoadField: r1 = r0->field_5b
    //     0x14a7c9c: ldur            w1, [x0, #0x5b]
    // 0x14a7ca0: DecompressPointer r1
    //     0x14a7ca0: add             x1, x1, HEAP, lsl #32
    // 0x14a7ca4: r16 = 14.000000
    //     0x14a7ca4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14a7ca8: ldr             x16, [x16, #0x1d8]
    // 0x14a7cac: stp             x1, x16, [SP]
    // 0x14a7cb0: ldur            x1, [fp, #-0x70]
    // 0x14a7cb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a7cb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a7cb8: ldr             x4, [x4, #0xaa0]
    // 0x14a7cbc: r0 = copyWith()
    //     0x14a7cbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a7cc0: stur            x0, [fp, #-0x70]
    // 0x14a7cc4: r0 = Text()
    //     0x14a7cc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a7cc8: mov             x3, x0
    // 0x14a7ccc: r0 = "+91 "
    //     0x14a7ccc: add             x0, PP, #0x37, lsl #12  ; [pp+0x37148] "+91 "
    //     0x14a7cd0: ldr             x0, [x0, #0x148]
    // 0x14a7cd4: stur            x3, [fp, #-0x78]
    // 0x14a7cd8: StoreField: r3->field_b = r0
    //     0x14a7cd8: stur            w0, [x3, #0xb]
    // 0x14a7cdc: ldur            x0, [fp, #-0x70]
    // 0x14a7ce0: StoreField: r3->field_13 = r0
    //     0x14a7ce0: stur            w0, [x3, #0x13]
    // 0x14a7ce4: r1 = Null
    //     0x14a7ce4: mov             x1, NULL
    // 0x14a7ce8: r2 = 4
    //     0x14a7ce8: movz            x2, #0x4
    // 0x14a7cec: r0 = AllocateArray()
    //     0x14a7cec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a7cf0: mov             x2, x0
    // 0x14a7cf4: ldur            x0, [fp, #-0x68]
    // 0x14a7cf8: stur            x2, [fp, #-0x70]
    // 0x14a7cfc: StoreField: r2->field_f = r0
    //     0x14a7cfc: stur            w0, [x2, #0xf]
    // 0x14a7d00: ldur            x0, [fp, #-0x78]
    // 0x14a7d04: StoreField: r2->field_13 = r0
    //     0x14a7d04: stur            w0, [x2, #0x13]
    // 0x14a7d08: r1 = <Widget>
    //     0x14a7d08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a7d0c: r0 = AllocateGrowableArray()
    //     0x14a7d0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a7d10: mov             x1, x0
    // 0x14a7d14: ldur            x0, [fp, #-0x70]
    // 0x14a7d18: stur            x1, [fp, #-0x68]
    // 0x14a7d1c: StoreField: r1->field_f = r0
    //     0x14a7d1c: stur            w0, [x1, #0xf]
    // 0x14a7d20: r2 = 4
    //     0x14a7d20: movz            x2, #0x4
    // 0x14a7d24: StoreField: r1->field_b = r2
    //     0x14a7d24: stur            w2, [x1, #0xb]
    // 0x14a7d28: r0 = Row()
    //     0x14a7d28: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14a7d2c: mov             x1, x0
    // 0x14a7d30: r0 = Instance_Axis
    //     0x14a7d30: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14a7d34: stur            x1, [fp, #-0x70]
    // 0x14a7d38: StoreField: r1->field_f = r0
    //     0x14a7d38: stur            w0, [x1, #0xf]
    // 0x14a7d3c: r0 = Instance_MainAxisAlignment
    //     0x14a7d3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a7d40: ldr             x0, [x0, #0xa08]
    // 0x14a7d44: StoreField: r1->field_13 = r0
    //     0x14a7d44: stur            w0, [x1, #0x13]
    // 0x14a7d48: r2 = Instance_MainAxisSize
    //     0x14a7d48: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14a7d4c: ldr             x2, [x2, #0xdd0]
    // 0x14a7d50: ArrayStore: r1[0] = r2  ; List_4
    //     0x14a7d50: stur            w2, [x1, #0x17]
    // 0x14a7d54: r2 = Instance_CrossAxisAlignment
    //     0x14a7d54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14a7d58: ldr             x2, [x2, #0xa18]
    // 0x14a7d5c: StoreField: r1->field_1b = r2
    //     0x14a7d5c: stur            w2, [x1, #0x1b]
    // 0x14a7d60: r2 = Instance_VerticalDirection
    //     0x14a7d60: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a7d64: ldr             x2, [x2, #0xa20]
    // 0x14a7d68: StoreField: r1->field_23 = r2
    //     0x14a7d68: stur            w2, [x1, #0x23]
    // 0x14a7d6c: r3 = Instance_Clip
    //     0x14a7d6c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a7d70: ldr             x3, [x3, #0x38]
    // 0x14a7d74: StoreField: r1->field_2b = r3
    //     0x14a7d74: stur            w3, [x1, #0x2b]
    // 0x14a7d78: StoreField: r1->field_2f = rZR
    //     0x14a7d78: stur            xzr, [x1, #0x2f]
    // 0x14a7d7c: ldur            x4, [fp, #-0x68]
    // 0x14a7d80: StoreField: r1->field_b = r4
    //     0x14a7d80: stur            w4, [x1, #0xb]
    // 0x14a7d84: r0 = Padding()
    //     0x14a7d84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a7d88: mov             x1, x0
    // 0x14a7d8c: r0 = Instance_EdgeInsets
    //     0x14a7d8c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x14a7d90: ldr             x0, [x0, #0xc40]
    // 0x14a7d94: stur            x1, [fp, #-0x68]
    // 0x14a7d98: StoreField: r1->field_f = r0
    //     0x14a7d98: stur            w0, [x1, #0xf]
    // 0x14a7d9c: ldur            x0, [fp, #-0x70]
    // 0x14a7da0: StoreField: r1->field_b = r0
    //     0x14a7da0: stur            w0, [x1, #0xb]
    // 0x14a7da4: r0 = Align()
    //     0x14a7da4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14a7da8: mov             x1, x0
    // 0x14a7dac: r0 = Instance_Alignment
    //     0x14a7dac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14a7db0: ldr             x0, [x0, #0xb10]
    // 0x14a7db4: stur            x1, [fp, #-0x70]
    // 0x14a7db8: StoreField: r1->field_f = r0
    //     0x14a7db8: stur            w0, [x1, #0xf]
    // 0x14a7dbc: r0 = 1.000000
    //     0x14a7dbc: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14a7dc0: StoreField: r1->field_13 = r0
    //     0x14a7dc0: stur            w0, [x1, #0x13]
    // 0x14a7dc4: ArrayStore: r1[0] = r0  ; List_4
    //     0x14a7dc4: stur            w0, [x1, #0x17]
    // 0x14a7dc8: ldur            x0, [fp, #-0x68]
    // 0x14a7dcc: StoreField: r1->field_b = r0
    //     0x14a7dcc: stur            w0, [x1, #0xb]
    // 0x14a7dd0: r0 = Padding()
    //     0x14a7dd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a7dd4: mov             x1, x0
    // 0x14a7dd8: r0 = Instance_EdgeInsets
    //     0x14a7dd8: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0x14a7ddc: ldr             x0, [x0, #0xe0]
    // 0x14a7de0: StoreField: r1->field_f = r0
    //     0x14a7de0: stur            w0, [x1, #0xf]
    // 0x14a7de4: ldur            x0, [fp, #-0x70]
    // 0x14a7de8: StoreField: r1->field_b = r0
    //     0x14a7de8: stur            w0, [x1, #0xb]
    // 0x14a7dec: r16 = Instance_EdgeInsets
    //     0x14a7dec: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x14a7df0: ldr             x16, [x16, #0xa78]
    // 0x14a7df4: r30 = "Enter Whatsapp no."
    //     0x14a7df4: add             lr, PP, #0x37, lsl #12  ; [pp+0x37150] "Enter Whatsapp no."
    //     0x14a7df8: ldr             lr, [lr, #0x150]
    // 0x14a7dfc: stp             lr, x16, [SP, #0x18]
    // 0x14a7e00: ldur            x16, [fp, #-0x58]
    // 0x14a7e04: ldur            lr, [fp, #-0x60]
    // 0x14a7e08: stp             lr, x16, [SP, #8]
    // 0x14a7e0c: str             x1, [SP]
    // 0x14a7e10: ldur            x1, [fp, #-0x50]
    // 0x14a7e14: r4 = const [0, 0x6, 0x5, 0x1, contentPadding, 0x1, hintStyle, 0x3, hintText, 0x2, prefixIcon, 0x5, suffixIcon, 0x4, null]
    //     0x14a7e14: add             x4, PP, #0x40, lsl #12  ; [pp+0x402d0] List(15) [0, 0x6, 0x5, 0x1, "contentPadding", 0x1, "hintStyle", 0x3, "hintText", 0x2, "prefixIcon", 0x5, "suffixIcon", 0x4, Null]
    //     0x14a7e18: ldr             x4, [x4, #0x2d0]
    // 0x14a7e1c: r0 = copyWith()
    //     0x14a7e1c: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x14a7e20: ldur            x2, [fp, #-0x20]
    // 0x14a7e24: r1 = Function '_validatePhoneNumber@1472061739':.
    //     0x14a7e24: add             x1, PP, #0x43, lsl #12  ; [pp+0x43248] AnonymousClosure: (0x14a8718), in [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validatePhoneNumber (0x1404d40)
    //     0x14a7e28: ldr             x1, [x1, #0x248]
    // 0x14a7e2c: stur            x0, [fp, #-0x20]
    // 0x14a7e30: r0 = AllocateClosure()
    //     0x14a7e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a7e34: ldur            x2, [fp, #-8]
    // 0x14a7e38: r1 = Function '<anonymous closure>':.
    //     0x14a7e38: add             x1, PP, #0x43, lsl #12  ; [pp+0x43250] AnonymousClosure: (0x14a86a0), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::_getCosmeticThemeLoginForm (0x14a76e4)
    //     0x14a7e3c: ldr             x1, [x1, #0x250]
    // 0x14a7e40: stur            x0, [fp, #-0x50]
    // 0x14a7e44: r0 = AllocateClosure()
    //     0x14a7e44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a7e48: ldur            x2, [fp, #-8]
    // 0x14a7e4c: r1 = Function '<anonymous closure>':.
    //     0x14a7e4c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43258] AnonymousClosure: (0x14a8600), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::_getCosmeticThemeLoginForm (0x14a76e4)
    //     0x14a7e50: ldr             x1, [x1, #0x258]
    // 0x14a7e54: stur            x0, [fp, #-0x58]
    // 0x14a7e58: r0 = AllocateClosure()
    //     0x14a7e58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a7e5c: r1 = <String>
    //     0x14a7e5c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14a7e60: stur            x0, [fp, #-0x60]
    // 0x14a7e64: r0 = TextFormField()
    //     0x14a7e64: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x14a7e68: stur            x0, [fp, #-0x68]
    // 0x14a7e6c: r16 = true
    //     0x14a7e6c: add             x16, NULL, #0x20  ; true
    // 0x14a7e70: ldur            lr, [fp, #-0x50]
    // 0x14a7e74: stp             lr, x16, [SP, #0x40]
    // 0x14a7e78: r16 = Instance_AutovalidateMode
    //     0x14a7e78: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14a7e7c: ldr             x16, [x16, #0x7e8]
    // 0x14a7e80: r30 = false
    //     0x14a7e80: add             lr, NULL, #0x30  ; false
    // 0x14a7e84: stp             lr, x16, [SP, #0x30]
    // 0x14a7e88: ldur            x16, [fp, #-0x58]
    // 0x14a7e8c: ldur            lr, [fp, #-0x40]
    // 0x14a7e90: stp             lr, x16, [SP, #0x20]
    // 0x14a7e94: r16 = Instance_TextInputType
    //     0x14a7e94: add             x16, PP, #0x37, lsl #12  ; [pp+0x37178] Obj!TextInputType@d55be1
    //     0x14a7e98: ldr             x16, [x16, #0x178]
    // 0x14a7e9c: r30 = 2
    //     0x14a7e9c: movz            lr, #0x2
    // 0x14a7ea0: stp             lr, x16, [SP, #0x10]
    // 0x14a7ea4: ldur            x16, [fp, #-0x60]
    // 0x14a7ea8: ldur            lr, [fp, #-0x48]
    // 0x14a7eac: stp             lr, x16, [SP]
    // 0x14a7eb0: mov             x1, x0
    // 0x14a7eb4: ldur            x2, [fp, #-0x20]
    // 0x14a7eb8: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x2, autovalidateMode, 0x4, enableSuggestions, 0x5, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0x9, onChanged, 0xa, onFieldSubmitted, 0x6, style, 0xb, validator, 0x3, null]
    //     0x14a7eb8: add             x4, PP, #0x37, lsl #12  ; [pp+0x37180] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x2, "autovalidateMode", 0x4, "enableSuggestions", 0x5, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0x9, "onChanged", 0xa, "onFieldSubmitted", 0x6, "style", 0xb, "validator", 0x3, Null]
    //     0x14a7ebc: ldr             x4, [x4, #0x180]
    // 0x14a7ec0: r0 = TextFormField()
    //     0x14a7ec0: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14a7ec4: r0 = Form()
    //     0x14a7ec4: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14a7ec8: mov             x1, x0
    // 0x14a7ecc: ldur            x0, [fp, #-0x68]
    // 0x14a7ed0: stur            x1, [fp, #-0x20]
    // 0x14a7ed4: StoreField: r1->field_b = r0
    //     0x14a7ed4: stur            w0, [x1, #0xb]
    // 0x14a7ed8: r0 = Instance_AutovalidateMode
    //     0x14a7ed8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14a7edc: ldr             x0, [x0, #0x800]
    // 0x14a7ee0: StoreField: r1->field_23 = r0
    //     0x14a7ee0: stur            w0, [x1, #0x23]
    // 0x14a7ee4: ldur            x0, [fp, #-0x18]
    // 0x14a7ee8: StoreField: r1->field_7 = r0
    //     0x14a7ee8: stur            w0, [x1, #7]
    // 0x14a7eec: r0 = Padding()
    //     0x14a7eec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a7ef0: mov             x1, x0
    // 0x14a7ef4: r0 = Instance_EdgeInsets
    //     0x14a7ef4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14a7ef8: ldr             x0, [x0, #0x1f0]
    // 0x14a7efc: stur            x1, [fp, #-0x18]
    // 0x14a7f00: StoreField: r1->field_f = r0
    //     0x14a7f00: stur            w0, [x1, #0xf]
    // 0x14a7f04: ldur            x0, [fp, #-0x20]
    // 0x14a7f08: StoreField: r1->field_b = r0
    //     0x14a7f08: stur            w0, [x1, #0xb]
    // 0x14a7f0c: r0 = Visibility()
    //     0x14a7f0c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14a7f10: mov             x2, x0
    // 0x14a7f14: ldur            x0, [fp, #-0x18]
    // 0x14a7f18: stur            x2, [fp, #-0x20]
    // 0x14a7f1c: StoreField: r2->field_b = r0
    //     0x14a7f1c: stur            w0, [x2, #0xb]
    // 0x14a7f20: r0 = Instance_SizedBox
    //     0x14a7f20: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14a7f24: StoreField: r2->field_f = r0
    //     0x14a7f24: stur            w0, [x2, #0xf]
    // 0x14a7f28: ldur            x1, [fp, #-0x30]
    // 0x14a7f2c: StoreField: r2->field_13 = r1
    //     0x14a7f2c: stur            w1, [x2, #0x13]
    // 0x14a7f30: r3 = false
    //     0x14a7f30: add             x3, NULL, #0x30  ; false
    // 0x14a7f34: ArrayStore: r2[0] = r3  ; List_4
    //     0x14a7f34: stur            w3, [x2, #0x17]
    // 0x14a7f38: StoreField: r2->field_1b = r3
    //     0x14a7f38: stur            w3, [x2, #0x1b]
    // 0x14a7f3c: StoreField: r2->field_1f = r3
    //     0x14a7f3c: stur            w3, [x2, #0x1f]
    // 0x14a7f40: StoreField: r2->field_23 = r3
    //     0x14a7f40: stur            w3, [x2, #0x23]
    // 0x14a7f44: StoreField: r2->field_27 = r3
    //     0x14a7f44: stur            w3, [x2, #0x27]
    // 0x14a7f48: StoreField: r2->field_2b = r3
    //     0x14a7f48: stur            w3, [x2, #0x2b]
    // 0x14a7f4c: ldur            x4, [fp, #-8]
    // 0x14a7f50: LoadField: r1 = r4->field_f
    //     0x14a7f50: ldur            w1, [x4, #0xf]
    // 0x14a7f54: DecompressPointer r1
    //     0x14a7f54: add             x1, x1, HEAP, lsl #32
    // 0x14a7f58: r0 = controller()
    //     0x14a7f58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a7f5c: LoadField: r1 = r0->field_8b
    //     0x14a7f5c: ldur            w1, [x0, #0x8b]
    // 0x14a7f60: DecompressPointer r1
    //     0x14a7f60: add             x1, x1, HEAP, lsl #32
    // 0x14a7f64: r0 = value()
    //     0x14a7f64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a7f68: cmp             w0, NULL
    // 0x14a7f6c: b.ne            #0x14a7f78
    // 0x14a7f70: r1 = true
    //     0x14a7f70: add             x1, NULL, #0x20  ; true
    // 0x14a7f74: b               #0x14a7f7c
    // 0x14a7f78: mov             x1, x0
    // 0x14a7f7c: ldur            x2, [fp, #-8]
    // 0x14a7f80: ldur            x0, [fp, #-0x38]
    // 0x14a7f84: stur            x1, [fp, #-0x18]
    // 0x14a7f88: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14a7f88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14a7f8c: ldr             x0, [x0, #0x1c80]
    //     0x14a7f90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14a7f94: cmp             w0, w16
    //     0x14a7f98: b.ne            #0x14a7fa4
    //     0x14a7f9c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14a7fa0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14a7fa4: r0 = GetNavigation.width()
    //     0x14a7fa4: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x14a7fa8: stur            d0, [fp, #-0x80]
    // 0x14a7fac: r16 = "[0-9]"
    //     0x14a7fac: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0x14a7fb0: ldr             x16, [x16, #0x128]
    // 0x14a7fb4: stp             x16, NULL, [SP, #0x20]
    // 0x14a7fb8: r16 = false
    //     0x14a7fb8: add             x16, NULL, #0x30  ; false
    // 0x14a7fbc: r30 = true
    //     0x14a7fbc: add             lr, NULL, #0x20  ; true
    // 0x14a7fc0: stp             lr, x16, [SP, #0x10]
    // 0x14a7fc4: r16 = false
    //     0x14a7fc4: add             x16, NULL, #0x30  ; false
    // 0x14a7fc8: r30 = false
    //     0x14a7fc8: add             lr, NULL, #0x30  ; false
    // 0x14a7fcc: stp             lr, x16, [SP]
    // 0x14a7fd0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x14a7fd0: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x14a7fd4: r0 = _RegExp()
    //     0x14a7fd4: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x14a7fd8: stur            x0, [fp, #-0x30]
    // 0x14a7fdc: r0 = FilteringTextInputFormatter()
    //     0x14a7fdc: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0x14a7fe0: mov             x3, x0
    // 0x14a7fe4: ldur            x0, [fp, #-0x30]
    // 0x14a7fe8: stur            x3, [fp, #-0x40]
    // 0x14a7fec: StoreField: r3->field_b = r0
    //     0x14a7fec: stur            w0, [x3, #0xb]
    // 0x14a7ff0: r0 = true
    //     0x14a7ff0: add             x0, NULL, #0x20  ; true
    // 0x14a7ff4: StoreField: r3->field_7 = r0
    //     0x14a7ff4: stur            w0, [x3, #7]
    // 0x14a7ff8: r1 = ""
    //     0x14a7ff8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14a7ffc: StoreField: r3->field_f = r1
    //     0x14a7ffc: stur            w1, [x3, #0xf]
    // 0x14a8000: r1 = Null
    //     0x14a8000: mov             x1, NULL
    // 0x14a8004: r2 = 4
    //     0x14a8004: movz            x2, #0x4
    // 0x14a8008: r0 = AllocateArray()
    //     0x14a8008: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a800c: mov             x2, x0
    // 0x14a8010: ldur            x0, [fp, #-0x38]
    // 0x14a8014: stur            x2, [fp, #-0x30]
    // 0x14a8018: StoreField: r2->field_f = r0
    //     0x14a8018: stur            w0, [x2, #0xf]
    // 0x14a801c: ldur            x0, [fp, #-0x40]
    // 0x14a8020: StoreField: r2->field_13 = r0
    //     0x14a8020: stur            w0, [x2, #0x13]
    // 0x14a8024: r1 = <TextInputFormatter>
    //     0x14a8024: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14a8028: ldr             x1, [x1, #0x7b0]
    // 0x14a802c: r0 = AllocateGrowableArray()
    //     0x14a802c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a8030: mov             x2, x0
    // 0x14a8034: ldur            x0, [fp, #-0x30]
    // 0x14a8038: stur            x2, [fp, #-0x38]
    // 0x14a803c: StoreField: r2->field_f = r0
    //     0x14a803c: stur            w0, [x2, #0xf]
    // 0x14a8040: r0 = 4
    //     0x14a8040: movz            x0, #0x4
    // 0x14a8044: StoreField: r2->field_b = r0
    //     0x14a8044: stur            w0, [x2, #0xb]
    // 0x14a8048: ldur            x0, [fp, #-8]
    // 0x14a804c: LoadField: r1 = r0->field_13
    //     0x14a804c: ldur            w1, [x0, #0x13]
    // 0x14a8050: DecompressPointer r1
    //     0x14a8050: add             x1, x1, HEAP, lsl #32
    // 0x14a8054: r0 = of()
    //     0x14a8054: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a8058: LoadField: r1 = r0->field_87
    //     0x14a8058: ldur            w1, [x0, #0x87]
    // 0x14a805c: DecompressPointer r1
    //     0x14a805c: add             x1, x1, HEAP, lsl #32
    // 0x14a8060: LoadField: r0 = r1->field_27
    //     0x14a8060: ldur            w0, [x1, #0x27]
    // 0x14a8064: DecompressPointer r0
    //     0x14a8064: add             x0, x0, HEAP, lsl #32
    // 0x14a8068: r16 = 16.000000
    //     0x14a8068: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x14a806c: ldr             x16, [x16, #0x188]
    // 0x14a8070: r30 = Instance_Color
    //     0x14a8070: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a8074: stp             lr, x16, [SP]
    // 0x14a8078: mov             x1, x0
    // 0x14a807c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14a807c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14a8080: ldr             x4, [x4, #0xaa0]
    // 0x14a8084: r0 = copyWith()
    //     0x14a8084: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a8088: ldur            x2, [fp, #-8]
    // 0x14a808c: stur            x0, [fp, #-0x30]
    // 0x14a8090: LoadField: r1 = r2->field_13
    //     0x14a8090: ldur            w1, [x2, #0x13]
    // 0x14a8094: DecompressPointer r1
    //     0x14a8094: add             x1, x1, HEAP, lsl #32
    // 0x14a8098: r0 = of()
    //     0x14a8098: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a809c: LoadField: r1 = r0->field_5b
    //     0x14a809c: ldur            w1, [x0, #0x5b]
    // 0x14a80a0: DecompressPointer r1
    //     0x14a80a0: add             x1, x1, HEAP, lsl #32
    // 0x14a80a4: r0 = LoadClassIdInstr(r1)
    //     0x14a80a4: ldur            x0, [x1, #-1]
    //     0x14a80a8: ubfx            x0, x0, #0xc, #0x14
    // 0x14a80ac: d0 = 0.300000
    //     0x14a80ac: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x14a80b0: ldr             d0, [x17, #0x658]
    // 0x14a80b4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14a80b4: sub             lr, x0, #0xffa
    //     0x14a80b8: ldr             lr, [x21, lr, lsl #3]
    //     0x14a80bc: blr             lr
    // 0x14a80c0: r1 = <Color>
    //     0x14a80c0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x14a80c4: ldr             x1, [x1, #0xf80]
    // 0x14a80c8: stur            x0, [fp, #-0x40]
    // 0x14a80cc: r0 = FixedColorBuilder()
    //     0x14a80cc: bl              #0xa0985c  ; AllocateFixedColorBuilderStub -> FixedColorBuilder (size=0x10)
    // 0x14a80d0: mov             x1, x0
    // 0x14a80d4: ldur            x0, [fp, #-0x40]
    // 0x14a80d8: stur            x1, [fp, #-0x48]
    // 0x14a80dc: StoreField: r1->field_b = r0
    //     0x14a80dc: stur            w0, [x1, #0xb]
    // 0x14a80e0: r0 = BoxLooseDecoration()
    //     0x14a80e0: bl              #0xb4d24c  ; AllocateBoxLooseDecorationStub -> BoxLooseDecoration (size=0x48)
    // 0x14a80e4: stur            x0, [fp, #-0x40]
    // 0x14a80e8: r16 = Instance_FixedColorBuilder
    //     0x14a80e8: add             x16, PP, #0x40, lsl #12  ; [pp+0x402f8] Obj!FixedColorBuilder@d53ab1
    //     0x14a80ec: ldr             x16, [x16, #0x2f8]
    // 0x14a80f0: str             x16, [SP]
    // 0x14a80f4: mov             x1, x0
    // 0x14a80f8: ldur            x2, [fp, #-0x48]
    // 0x14a80fc: ldur            x3, [fp, #-0x30]
    // 0x14a8100: r4 = const [0, 0x4, 0x1, 0x3, bgColorBuilder, 0x3, null]
    //     0x14a8100: add             x4, PP, #0x40, lsl #12  ; [pp+0x40300] List(7) [0, 0x4, 0x1, 0x3, "bgColorBuilder", 0x3, Null]
    //     0x14a8104: ldr             x4, [x4, #0x300]
    // 0x14a8108: r0 = BoxLooseDecoration()
    //     0x14a8108: bl              #0xb4cebc  ; [package:pin_input_text_field/src/decoration/pin_decoration.dart] BoxLooseDecoration::BoxLooseDecoration
    // 0x14a810c: ldur            x2, [fp, #-8]
    // 0x14a8110: LoadField: r1 = r2->field_f
    //     0x14a8110: ldur            w1, [x2, #0xf]
    // 0x14a8114: DecompressPointer r1
    //     0x14a8114: add             x1, x1, HEAP, lsl #32
    // 0x14a8118: r0 = controller()
    //     0x14a8118: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a811c: LoadField: r1 = r0->field_7f
    //     0x14a811c: ldur            w1, [x0, #0x7f]
    // 0x14a8120: DecompressPointer r1
    //     0x14a8120: add             x1, x1, HEAP, lsl #32
    // 0x14a8124: r0 = value()
    //     0x14a8124: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a8128: stur            x0, [fp, #-0x30]
    // 0x14a812c: r0 = PinFieldAutoFill()
    //     0x14a812c: bl              #0xa09844  ; AllocatePinFieldAutoFillStub -> PinFieldAutoFill (size=0x4c)
    // 0x14a8130: mov             x3, x0
    // 0x14a8134: r0 = Instance_TextInputType
    //     0x14a8134: add             x0, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0x14a8138: ldr             x0, [x0, #0x1a0]
    // 0x14a813c: stur            x3, [fp, #-0x48]
    // 0x14a8140: StoreField: r3->field_33 = r0
    //     0x14a8140: stur            w0, [x3, #0x33]
    // 0x14a8144: r0 = Instance_TextInputAction
    //     0x14a8144: ldr             x0, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0x14a8148: StoreField: r3->field_37 = r0
    //     0x14a8148: stur            w0, [x3, #0x37]
    // 0x14a814c: ldur            x0, [fp, #-0x38]
    // 0x14a8150: StoreField: r3->field_47 = r0
    //     0x14a8150: stur            w0, [x3, #0x47]
    // 0x14a8154: r0 = true
    //     0x14a8154: add             x0, NULL, #0x20  ; true
    // 0x14a8158: StoreField: r3->field_3b = r0
    //     0x14a8158: stur            w0, [x3, #0x3b]
    // 0x14a815c: StoreField: r3->field_3f = r0
    //     0x14a815c: stur            w0, [x3, #0x3f]
    // 0x14a8160: ldur            x1, [fp, #-0x40]
    // 0x14a8164: StoreField: r3->field_27 = r1
    //     0x14a8164: stur            w1, [x3, #0x27]
    // 0x14a8168: ldur            x2, [fp, #-8]
    // 0x14a816c: r1 = Function '<anonymous closure>':.
    //     0x14a816c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43260] AnonymousClosure: (0x1404280), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x14a8170: ldr             x1, [x1, #0x260]
    // 0x14a8174: r0 = AllocateClosure()
    //     0x14a8174: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8178: mov             x1, x0
    // 0x14a817c: ldur            x0, [fp, #-0x48]
    // 0x14a8180: StoreField: r0->field_1f = r1
    //     0x14a8180: stur            w1, [x0, #0x1f]
    // 0x14a8184: ldur            x2, [fp, #-8]
    // 0x14a8188: r1 = Function '<anonymous closure>':.
    //     0x14a8188: add             x1, PP, #0x43, lsl #12  ; [pp+0x43268] AnonymousClosure: (0x14a84ec), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::_getCosmeticThemeLoginForm (0x14a76e4)
    //     0x14a818c: ldr             x1, [x1, #0x268]
    // 0x14a8190: r0 = AllocateClosure()
    //     0x14a8190: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8194: mov             x1, x0
    // 0x14a8198: ldur            x0, [fp, #-0x48]
    // 0x14a819c: StoreField: r0->field_23 = r1
    //     0x14a819c: stur            w1, [x0, #0x23]
    // 0x14a81a0: ldur            x1, [fp, #-0x30]
    // 0x14a81a4: StoreField: r0->field_1b = r1
    //     0x14a81a4: stur            w1, [x0, #0x1b]
    // 0x14a81a8: r1 = false
    //     0x14a81a8: add             x1, NULL, #0x30  ; false
    // 0x14a81ac: StoreField: r0->field_13 = r1
    //     0x14a81ac: stur            w1, [x0, #0x13]
    // 0x14a81b0: r2 = 4
    //     0x14a81b0: movz            x2, #0x4
    // 0x14a81b4: StoreField: r0->field_b = r2
    //     0x14a81b4: stur            x2, [x0, #0xb]
    // 0x14a81b8: ldur            d0, [fp, #-0x80]
    // 0x14a81bc: r2 = inline_Allocate_Double()
    //     0x14a81bc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x14a81c0: add             x2, x2, #0x10
    //     0x14a81c4: cmp             x3, x2
    //     0x14a81c8: b.ls            #0x14a84d0
    //     0x14a81cc: str             x2, [THR, #0x50]  ; THR::top
    //     0x14a81d0: sub             x2, x2, #0xf
    //     0x14a81d4: movz            x3, #0xe15c
    //     0x14a81d8: movk            x3, #0x3, lsl #16
    //     0x14a81dc: stur            x3, [x2, #-1]
    // 0x14a81e0: StoreField: r2->field_7 = d0
    //     0x14a81e0: stur            d0, [x2, #7]
    // 0x14a81e4: stur            x2, [fp, #-0x30]
    // 0x14a81e8: r0 = Container()
    //     0x14a81e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14a81ec: stur            x0, [fp, #-0x38]
    // 0x14a81f0: r16 = Instance_EdgeInsets
    //     0x14a81f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x14a81f4: ldr             x16, [x16, #0xe68]
    // 0x14a81f8: ldur            lr, [fp, #-0x30]
    // 0x14a81fc: stp             lr, x16, [SP, #8]
    // 0x14a8200: ldur            x16, [fp, #-0x48]
    // 0x14a8204: str             x16, [SP]
    // 0x14a8208: mov             x1, x0
    // 0x14a820c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0x14a820c: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0x14a8210: ldr             x4, [x4, #0x1b8]
    // 0x14a8214: r0 = Container()
    //     0x14a8214: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14a8218: r0 = Visibility()
    //     0x14a8218: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14a821c: mov             x2, x0
    // 0x14a8220: ldur            x0, [fp, #-0x38]
    // 0x14a8224: stur            x2, [fp, #-0x30]
    // 0x14a8228: StoreField: r2->field_b = r0
    //     0x14a8228: stur            w0, [x2, #0xb]
    // 0x14a822c: r0 = Instance_SizedBox
    //     0x14a822c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14a8230: StoreField: r2->field_f = r0
    //     0x14a8230: stur            w0, [x2, #0xf]
    // 0x14a8234: ldur            x1, [fp, #-0x18]
    // 0x14a8238: StoreField: r2->field_13 = r1
    //     0x14a8238: stur            w1, [x2, #0x13]
    // 0x14a823c: r3 = false
    //     0x14a823c: add             x3, NULL, #0x30  ; false
    // 0x14a8240: ArrayStore: r2[0] = r3  ; List_4
    //     0x14a8240: stur            w3, [x2, #0x17]
    // 0x14a8244: StoreField: r2->field_1b = r3
    //     0x14a8244: stur            w3, [x2, #0x1b]
    // 0x14a8248: StoreField: r2->field_1f = r3
    //     0x14a8248: stur            w3, [x2, #0x1f]
    // 0x14a824c: StoreField: r2->field_23 = r3
    //     0x14a824c: stur            w3, [x2, #0x23]
    // 0x14a8250: StoreField: r2->field_27 = r3
    //     0x14a8250: stur            w3, [x2, #0x27]
    // 0x14a8254: StoreField: r2->field_2b = r3
    //     0x14a8254: stur            w3, [x2, #0x2b]
    // 0x14a8258: ldur            x4, [fp, #-8]
    // 0x14a825c: LoadField: r1 = r4->field_f
    //     0x14a825c: ldur            w1, [x4, #0xf]
    // 0x14a8260: DecompressPointer r1
    //     0x14a8260: add             x1, x1, HEAP, lsl #32
    // 0x14a8264: r0 = controller()
    //     0x14a8264: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8268: LoadField: r1 = r0->field_8b
    //     0x14a8268: ldur            w1, [x0, #0x8b]
    // 0x14a826c: DecompressPointer r1
    //     0x14a826c: add             x1, x1, HEAP, lsl #32
    // 0x14a8270: r0 = value()
    //     0x14a8270: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a8274: cmp             w0, NULL
    // 0x14a8278: b.ne            #0x14a8280
    // 0x14a827c: r0 = true
    //     0x14a827c: add             x0, NULL, #0x20  ; true
    // 0x14a8280: ldur            x2, [fp, #-8]
    // 0x14a8284: stur            x0, [fp, #-0x18]
    // 0x14a8288: LoadField: r1 = r2->field_13
    //     0x14a8288: ldur            w1, [x2, #0x13]
    // 0x14a828c: DecompressPointer r1
    //     0x14a828c: add             x1, x1, HEAP, lsl #32
    // 0x14a8290: r0 = of()
    //     0x14a8290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14a8294: LoadField: r1 = r0->field_87
    //     0x14a8294: ldur            w1, [x0, #0x87]
    // 0x14a8298: DecompressPointer r1
    //     0x14a8298: add             x1, x1, HEAP, lsl #32
    // 0x14a829c: LoadField: r0 = r1->field_2b
    //     0x14a829c: ldur            w0, [x1, #0x2b]
    // 0x14a82a0: DecompressPointer r0
    //     0x14a82a0: add             x0, x0, HEAP, lsl #32
    // 0x14a82a4: ldur            x2, [fp, #-8]
    // 0x14a82a8: stur            x0, [fp, #-0x38]
    // 0x14a82ac: LoadField: r1 = r2->field_f
    //     0x14a82ac: ldur            w1, [x2, #0xf]
    // 0x14a82b0: DecompressPointer r1
    //     0x14a82b0: add             x1, x1, HEAP, lsl #32
    // 0x14a82b4: r0 = controller()
    //     0x14a82b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a82b8: LoadField: r1 = r0->field_97
    //     0x14a82b8: ldur            w1, [x0, #0x97]
    // 0x14a82bc: DecompressPointer r1
    //     0x14a82bc: add             x1, x1, HEAP, lsl #32
    // 0x14a82c0: r0 = value()
    //     0x14a82c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14a82c4: cmp             w0, NULL
    // 0x14a82c8: b.eq            #0x14a82d8
    // 0x14a82cc: tbnz            w0, #4, #0x14a82d8
    // 0x14a82d0: r1 = Instance_Color
    //     0x14a82d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a82d4: b               #0x14a82e8
    // 0x14a82d8: r1 = Instance_Color
    //     0x14a82d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14a82dc: d0 = 0.400000
    //     0x14a82dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14a82e0: r0 = withOpacity()
    //     0x14a82e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14a82e4: mov             x1, x0
    // 0x14a82e8: ldur            x5, [fp, #-0x10]
    // 0x14a82ec: ldur            x4, [fp, #-0x28]
    // 0x14a82f0: ldur            x3, [fp, #-0x20]
    // 0x14a82f4: ldur            x2, [fp, #-0x30]
    // 0x14a82f8: ldur            x0, [fp, #-0x18]
    // 0x14a82fc: r16 = 14.000000
    //     0x14a82fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14a8300: ldr             x16, [x16, #0x1d8]
    // 0x14a8304: stp             x16, x1, [SP]
    // 0x14a8308: ldur            x1, [fp, #-0x38]
    // 0x14a830c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14a830c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14a8310: ldr             x4, [x4, #0x9b8]
    // 0x14a8314: r0 = copyWith()
    //     0x14a8314: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14a8318: stur            x0, [fp, #-0x38]
    // 0x14a831c: r0 = Text()
    //     0x14a831c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14a8320: mov             x1, x0
    // 0x14a8324: r0 = "Resend OTP"
    //     0x14a8324: add             x0, PP, #0x40, lsl #12  ; [pp+0x40318] "Resend OTP"
    //     0x14a8328: ldr             x0, [x0, #0x318]
    // 0x14a832c: stur            x1, [fp, #-0x40]
    // 0x14a8330: StoreField: r1->field_b = r0
    //     0x14a8330: stur            w0, [x1, #0xb]
    // 0x14a8334: ldur            x0, [fp, #-0x38]
    // 0x14a8338: StoreField: r1->field_13 = r0
    //     0x14a8338: stur            w0, [x1, #0x13]
    // 0x14a833c: r0 = InkWell()
    //     0x14a833c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14a8340: mov             x3, x0
    // 0x14a8344: ldur            x0, [fp, #-0x40]
    // 0x14a8348: stur            x3, [fp, #-0x38]
    // 0x14a834c: StoreField: r3->field_b = r0
    //     0x14a834c: stur            w0, [x3, #0xb]
    // 0x14a8350: ldur            x2, [fp, #-8]
    // 0x14a8354: r1 = Function '<anonymous closure>':.
    //     0x14a8354: add             x1, PP, #0x43, lsl #12  ; [pp+0x43270] AnonymousClosure: (0x140313c), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::_getLineThemeLoginForm (0x1404eac)
    //     0x14a8358: ldr             x1, [x1, #0x270]
    // 0x14a835c: r0 = AllocateClosure()
    //     0x14a835c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8360: mov             x1, x0
    // 0x14a8364: ldur            x0, [fp, #-0x38]
    // 0x14a8368: StoreField: r0->field_f = r1
    //     0x14a8368: stur            w1, [x0, #0xf]
    // 0x14a836c: r1 = true
    //     0x14a836c: add             x1, NULL, #0x20  ; true
    // 0x14a8370: StoreField: r0->field_43 = r1
    //     0x14a8370: stur            w1, [x0, #0x43]
    // 0x14a8374: r2 = Instance_BoxShape
    //     0x14a8374: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14a8378: ldr             x2, [x2, #0x80]
    // 0x14a837c: StoreField: r0->field_47 = r2
    //     0x14a837c: stur            w2, [x0, #0x47]
    // 0x14a8380: StoreField: r0->field_6f = r1
    //     0x14a8380: stur            w1, [x0, #0x6f]
    // 0x14a8384: r2 = false
    //     0x14a8384: add             x2, NULL, #0x30  ; false
    // 0x14a8388: StoreField: r0->field_73 = r2
    //     0x14a8388: stur            w2, [x0, #0x73]
    // 0x14a838c: StoreField: r0->field_83 = r1
    //     0x14a838c: stur            w1, [x0, #0x83]
    // 0x14a8390: StoreField: r0->field_7b = r2
    //     0x14a8390: stur            w2, [x0, #0x7b]
    // 0x14a8394: r0 = Visibility()
    //     0x14a8394: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14a8398: mov             x1, x0
    // 0x14a839c: ldur            x0, [fp, #-0x38]
    // 0x14a83a0: stur            x1, [fp, #-8]
    // 0x14a83a4: StoreField: r1->field_b = r0
    //     0x14a83a4: stur            w0, [x1, #0xb]
    // 0x14a83a8: r0 = Instance_SizedBox
    //     0x14a83a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14a83ac: StoreField: r1->field_f = r0
    //     0x14a83ac: stur            w0, [x1, #0xf]
    // 0x14a83b0: ldur            x0, [fp, #-0x18]
    // 0x14a83b4: StoreField: r1->field_13 = r0
    //     0x14a83b4: stur            w0, [x1, #0x13]
    // 0x14a83b8: r0 = false
    //     0x14a83b8: add             x0, NULL, #0x30  ; false
    // 0x14a83bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14a83bc: stur            w0, [x1, #0x17]
    // 0x14a83c0: StoreField: r1->field_1b = r0
    //     0x14a83c0: stur            w0, [x1, #0x1b]
    // 0x14a83c4: StoreField: r1->field_1f = r0
    //     0x14a83c4: stur            w0, [x1, #0x1f]
    // 0x14a83c8: StoreField: r1->field_23 = r0
    //     0x14a83c8: stur            w0, [x1, #0x23]
    // 0x14a83cc: StoreField: r1->field_27 = r0
    //     0x14a83cc: stur            w0, [x1, #0x27]
    // 0x14a83d0: StoreField: r1->field_2b = r0
    //     0x14a83d0: stur            w0, [x1, #0x2b]
    // 0x14a83d4: r0 = Padding()
    //     0x14a83d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14a83d8: mov             x3, x0
    // 0x14a83dc: r0 = Instance_EdgeInsets
    //     0x14a83dc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x14a83e0: ldr             x0, [x0, #0xa78]
    // 0x14a83e4: stur            x3, [fp, #-0x18]
    // 0x14a83e8: StoreField: r3->field_f = r0
    //     0x14a83e8: stur            w0, [x3, #0xf]
    // 0x14a83ec: ldur            x0, [fp, #-8]
    // 0x14a83f0: StoreField: r3->field_b = r0
    //     0x14a83f0: stur            w0, [x3, #0xb]
    // 0x14a83f4: r1 = Null
    //     0x14a83f4: mov             x1, NULL
    // 0x14a83f8: r2 = 14
    //     0x14a83f8: movz            x2, #0xe
    // 0x14a83fc: r0 = AllocateArray()
    //     0x14a83fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14a8400: mov             x2, x0
    // 0x14a8404: ldur            x0, [fp, #-0x10]
    // 0x14a8408: stur            x2, [fp, #-8]
    // 0x14a840c: StoreField: r2->field_f = r0
    //     0x14a840c: stur            w0, [x2, #0xf]
    // 0x14a8410: ldur            x0, [fp, #-0x28]
    // 0x14a8414: StoreField: r2->field_13 = r0
    //     0x14a8414: stur            w0, [x2, #0x13]
    // 0x14a8418: ldur            x0, [fp, #-0x20]
    // 0x14a841c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14a841c: stur            w0, [x2, #0x17]
    // 0x14a8420: ldur            x0, [fp, #-0x30]
    // 0x14a8424: StoreField: r2->field_1b = r0
    //     0x14a8424: stur            w0, [x2, #0x1b]
    // 0x14a8428: r16 = Instance_SizedBox
    //     0x14a8428: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0x14a842c: ldr             x16, [x16, #0x328]
    // 0x14a8430: StoreField: r2->field_1f = r16
    //     0x14a8430: stur            w16, [x2, #0x1f]
    // 0x14a8434: ldur            x0, [fp, #-0x18]
    // 0x14a8438: StoreField: r2->field_23 = r0
    //     0x14a8438: stur            w0, [x2, #0x23]
    // 0x14a843c: r16 = Instance_SizedBox
    //     0x14a843c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14a8440: ldr             x16, [x16, #0x9f0]
    // 0x14a8444: StoreField: r2->field_27 = r16
    //     0x14a8444: stur            w16, [x2, #0x27]
    // 0x14a8448: r1 = <Widget>
    //     0x14a8448: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14a844c: r0 = AllocateGrowableArray()
    //     0x14a844c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14a8450: mov             x1, x0
    // 0x14a8454: ldur            x0, [fp, #-8]
    // 0x14a8458: stur            x1, [fp, #-0x10]
    // 0x14a845c: StoreField: r1->field_f = r0
    //     0x14a845c: stur            w0, [x1, #0xf]
    // 0x14a8460: r0 = 14
    //     0x14a8460: movz            x0, #0xe
    // 0x14a8464: StoreField: r1->field_b = r0
    //     0x14a8464: stur            w0, [x1, #0xb]
    // 0x14a8468: r0 = Column()
    //     0x14a8468: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14a846c: r1 = Instance_Axis
    //     0x14a846c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14a8470: StoreField: r0->field_f = r1
    //     0x14a8470: stur            w1, [x0, #0xf]
    // 0x14a8474: r1 = Instance_MainAxisAlignment
    //     0x14a8474: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14a8478: ldr             x1, [x1, #0xa08]
    // 0x14a847c: StoreField: r0->field_13 = r1
    //     0x14a847c: stur            w1, [x0, #0x13]
    // 0x14a8480: r1 = Instance_MainAxisSize
    //     0x14a8480: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14a8484: ldr             x1, [x1, #0xa10]
    // 0x14a8488: ArrayStore: r0[0] = r1  ; List_4
    //     0x14a8488: stur            w1, [x0, #0x17]
    // 0x14a848c: r1 = Instance_CrossAxisAlignment
    //     0x14a848c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14a8490: ldr             x1, [x1, #0x890]
    // 0x14a8494: StoreField: r0->field_1b = r1
    //     0x14a8494: stur            w1, [x0, #0x1b]
    // 0x14a8498: r1 = Instance_VerticalDirection
    //     0x14a8498: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14a849c: ldr             x1, [x1, #0xa20]
    // 0x14a84a0: StoreField: r0->field_23 = r1
    //     0x14a84a0: stur            w1, [x0, #0x23]
    // 0x14a84a4: r1 = Instance_Clip
    //     0x14a84a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14a84a8: ldr             x1, [x1, #0x38]
    // 0x14a84ac: StoreField: r0->field_2b = r1
    //     0x14a84ac: stur            w1, [x0, #0x2b]
    // 0x14a84b0: StoreField: r0->field_2f = rZR
    //     0x14a84b0: stur            xzr, [x0, #0x2f]
    // 0x14a84b4: ldur            x1, [fp, #-0x10]
    // 0x14a84b8: StoreField: r0->field_b = r1
    //     0x14a84b8: stur            w1, [x0, #0xb]
    // 0x14a84bc: LeaveFrame
    //     0x14a84bc: mov             SP, fp
    //     0x14a84c0: ldp             fp, lr, [SP], #0x10
    // 0x14a84c4: ret
    //     0x14a84c4: ret             
    // 0x14a84c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a84c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a84cc: b               #0x14a77b4
    // 0x14a84d0: SaveReg d0
    //     0x14a84d0: str             q0, [SP, #-0x10]!
    // 0x14a84d4: stp             x0, x1, [SP, #-0x10]!
    // 0x14a84d8: r0 = AllocateDouble()
    //     0x14a84d8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14a84dc: mov             x2, x0
    // 0x14a84e0: ldp             x0, x1, [SP], #0x10
    // 0x14a84e4: RestoreReg d0
    //     0x14a84e4: ldr             q0, [SP], #0x10
    // 0x14a84e8: b               #0x14a81e0
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14a84ec, size: 0x114
    // 0x14a84ec: EnterFrame
    //     0x14a84ec: stp             fp, lr, [SP, #-0x10]!
    //     0x14a84f0: mov             fp, SP
    // 0x14a84f4: AllocStack(0x8)
    //     0x14a84f4: sub             SP, SP, #8
    // 0x14a84f8: SetupParameters()
    //     0x14a84f8: ldr             x0, [fp, #0x18]
    //     0x14a84fc: ldur            w2, [x0, #0x17]
    //     0x14a8500: add             x2, x2, HEAP, lsl #32
    //     0x14a8504: stur            x2, [fp, #-8]
    // 0x14a8508: CheckStackOverflow
    //     0x14a8508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a850c: cmp             SP, x16
    //     0x14a8510: b.ls            #0x14a85f4
    // 0x14a8514: ldr             x0, [fp, #0x10]
    // 0x14a8518: cmp             w0, NULL
    // 0x14a851c: b.eq            #0x14a85fc
    // 0x14a8520: LoadField: r1 = r0->field_7
    //     0x14a8520: ldur            w1, [x0, #7]
    // 0x14a8524: cmp             w1, #8
    // 0x14a8528: b.ne            #0x14a8588
    // 0x14a852c: LoadField: r1 = r2->field_f
    //     0x14a852c: ldur            w1, [x2, #0xf]
    // 0x14a8530: DecompressPointer r1
    //     0x14a8530: add             x1, x1, HEAP, lsl #32
    // 0x14a8534: r0 = controller()
    //     0x14a8534: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8538: LoadField: r1 = r0->field_8f
    //     0x14a8538: ldur            w1, [x0, #0x8f]
    // 0x14a853c: DecompressPointer r1
    //     0x14a853c: add             x1, x1, HEAP, lsl #32
    // 0x14a8540: r2 = true
    //     0x14a8540: add             x2, NULL, #0x20  ; true
    // 0x14a8544: r0 = value=()
    //     0x14a8544: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14a8548: ldur            x0, [fp, #-8]
    // 0x14a854c: LoadField: r1 = r0->field_f
    //     0x14a854c: ldur            w1, [x0, #0xf]
    // 0x14a8550: DecompressPointer r1
    //     0x14a8550: add             x1, x1, HEAP, lsl #32
    // 0x14a8554: ldr             x0, [fp, #0x10]
    // 0x14a8558: StoreField: r1->field_1b = r0
    //     0x14a8558: stur            w0, [x1, #0x1b]
    //     0x14a855c: ldurb           w16, [x1, #-1]
    //     0x14a8560: ldurb           w17, [x0, #-1]
    //     0x14a8564: and             x16, x17, x16, lsr #2
    //     0x14a8568: tst             x16, HEAP, lsr #32
    //     0x14a856c: b.eq            #0x14a8574
    //     0x14a8570: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14a8574: r0 = controller()
    //     0x14a8574: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8578: mov             x1, x0
    // 0x14a857c: ldr             x2, [fp, #0x10]
    // 0x14a8580: r0 = onCodeChanged()
    //     0x14a8580: bl              #0x1404230  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onCodeChanged
    // 0x14a8584: b               #0x14a85e4
    // 0x14a8588: mov             x0, x2
    // 0x14a858c: LoadField: r1 = r0->field_f
    //     0x14a858c: ldur            w1, [x0, #0xf]
    // 0x14a8590: DecompressPointer r1
    //     0x14a8590: add             x1, x1, HEAP, lsl #32
    // 0x14a8594: r0 = controller()
    //     0x14a8594: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8598: LoadField: r1 = r0->field_8f
    //     0x14a8598: ldur            w1, [x0, #0x8f]
    // 0x14a859c: DecompressPointer r1
    //     0x14a859c: add             x1, x1, HEAP, lsl #32
    // 0x14a85a0: r2 = false
    //     0x14a85a0: add             x2, NULL, #0x30  ; false
    // 0x14a85a4: r0 = value=()
    //     0x14a85a4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14a85a8: ldur            x0, [fp, #-8]
    // 0x14a85ac: LoadField: r1 = r0->field_f
    //     0x14a85ac: ldur            w1, [x0, #0xf]
    // 0x14a85b0: DecompressPointer r1
    //     0x14a85b0: add             x1, x1, HEAP, lsl #32
    // 0x14a85b4: ldr             x0, [fp, #0x10]
    // 0x14a85b8: StoreField: r1->field_1b = r0
    //     0x14a85b8: stur            w0, [x1, #0x1b]
    //     0x14a85bc: ldurb           w16, [x1, #-1]
    //     0x14a85c0: ldurb           w17, [x0, #-1]
    //     0x14a85c4: and             x16, x17, x16, lsr #2
    //     0x14a85c8: tst             x16, HEAP, lsr #32
    //     0x14a85cc: b.eq            #0x14a85d4
    //     0x14a85d0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14a85d4: r0 = controller()
    //     0x14a85d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a85d8: mov             x1, x0
    // 0x14a85dc: ldr             x2, [fp, #0x10]
    // 0x14a85e0: r0 = onCodeChanged()
    //     0x14a85e0: bl              #0x1404230  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onCodeChanged
    // 0x14a85e4: r0 = Null
    //     0x14a85e4: mov             x0, NULL
    // 0x14a85e8: LeaveFrame
    //     0x14a85e8: mov             SP, fp
    //     0x14a85ec: ldp             fp, lr, [SP], #0x10
    // 0x14a85f0: ret
    //     0x14a85f0: ret             
    // 0x14a85f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a85f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a85f8: b               #0x14a8514
    // 0x14a85fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14a85fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14a8600, size: 0xa0
    // 0x14a8600: EnterFrame
    //     0x14a8600: stp             fp, lr, [SP, #-0x10]!
    //     0x14a8604: mov             fp, SP
    // 0x14a8608: AllocStack(0x8)
    //     0x14a8608: sub             SP, SP, #8
    // 0x14a860c: SetupParameters()
    //     0x14a860c: ldr             x0, [fp, #0x18]
    //     0x14a8610: ldur            w2, [x0, #0x17]
    //     0x14a8614: add             x2, x2, HEAP, lsl #32
    //     0x14a8618: stur            x2, [fp, #-8]
    // 0x14a861c: CheckStackOverflow
    //     0x14a861c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a8620: cmp             SP, x16
    //     0x14a8624: b.ls            #0x14a8698
    // 0x14a8628: LoadField: r1 = r2->field_f
    //     0x14a8628: ldur            w1, [x2, #0xf]
    // 0x14a862c: DecompressPointer r1
    //     0x14a862c: add             x1, x1, HEAP, lsl #32
    // 0x14a8630: ldr             x0, [fp, #0x10]
    // 0x14a8634: cmp             w0, NULL
    // 0x14a8638: b.ne            #0x14a8640
    // 0x14a863c: r0 = "0"
    //     0x14a863c: ldr             x0, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x14a8640: ArrayStore: r1[0] = r0  ; List_4
    //     0x14a8640: stur            w0, [x1, #0x17]
    //     0x14a8644: ldurb           w16, [x1, #-1]
    //     0x14a8648: ldurb           w17, [x0, #-1]
    //     0x14a864c: and             x16, x17, x16, lsr #2
    //     0x14a8650: tst             x16, HEAP, lsr #32
    //     0x14a8654: b.eq            #0x14a865c
    //     0x14a8658: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14a865c: r0 = controller()
    //     0x14a865c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14a8660: LoadField: r1 = r0->field_bb
    //     0x14a8660: ldur            w1, [x0, #0xbb]
    // 0x14a8664: DecompressPointer r1
    //     0x14a8664: add             x1, x1, HEAP, lsl #32
    // 0x14a8668: r2 = true
    //     0x14a8668: add             x2, NULL, #0x20  ; true
    // 0x14a866c: r0 = value=()
    //     0x14a866c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14a8670: ldur            x0, [fp, #-8]
    // 0x14a8674: LoadField: r1 = r0->field_f
    //     0x14a8674: ldur            w1, [x0, #0xf]
    // 0x14a8678: DecompressPointer r1
    //     0x14a8678: add             x1, x1, HEAP, lsl #32
    // 0x14a867c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x14a867c: ldur            w2, [x1, #0x17]
    // 0x14a8680: DecompressPointer r2
    //     0x14a8680: add             x2, x2, HEAP, lsl #32
    // 0x14a8684: r0 = _validateInput()
    //     0x14a8684: bl              #0x14054b8  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validateInput
    // 0x14a8688: r0 = Null
    //     0x14a8688: mov             x0, NULL
    // 0x14a868c: LeaveFrame
    //     0x14a868c: mov             SP, fp
    //     0x14a8690: ldp             fp, lr, [SP], #0x10
    // 0x14a8694: ret
    //     0x14a8694: ret             
    // 0x14a8698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a8698: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a869c: b               #0x14a8628
  }
  [closure] Future<void> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x14a86a0, size: 0x78
    // 0x14a86a0: EnterFrame
    //     0x14a86a0: stp             fp, lr, [SP, #-0x10]!
    //     0x14a86a4: mov             fp, SP
    // 0x14a86a8: AllocStack(0x18)
    //     0x14a86a8: sub             SP, SP, #0x18
    // 0x14a86ac: SetupParameters(LoginView this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x14a86ac: stur            NULL, [fp, #-8]
    //     0x14a86b0: movz            x0, #0
    //     0x14a86b4: add             x1, fp, w0, sxtw #2
    //     0x14a86b8: ldr             x1, [x1, #0x18]
    //     0x14a86bc: add             x2, fp, w0, sxtw #2
    //     0x14a86c0: ldr             x2, [x2, #0x10]
    //     0x14a86c4: stur            x2, [fp, #-0x18]
    //     0x14a86c8: ldur            w3, [x1, #0x17]
    //     0x14a86cc: add             x3, x3, HEAP, lsl #32
    //     0x14a86d0: stur            x3, [fp, #-0x10]
    // 0x14a86d4: CheckStackOverflow
    //     0x14a86d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a86d8: cmp             SP, x16
    //     0x14a86dc: b.ls            #0x14a8710
    // 0x14a86e0: InitAsync() -> Future<void?>
    //     0x14a86e0: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14a86e4: bl              #0x6326e0  ; InitAsyncStub
    // 0x14a86e8: ldur            x0, [fp, #-0x18]
    // 0x14a86ec: LoadField: r1 = r0->field_7
    //     0x14a86ec: ldur            w1, [x0, #7]
    // 0x14a86f0: cmp             w1, #0x14
    // 0x14a86f4: b.ne            #0x14a8708
    // 0x14a86f8: ldur            x0, [fp, #-0x10]
    // 0x14a86fc: LoadField: r1 = r0->field_f
    //     0x14a86fc: ldur            w1, [x0, #0xf]
    // 0x14a8700: DecompressPointer r1
    //     0x14a8700: add             x1, x1, HEAP, lsl #32
    // 0x14a8704: r0 = onPhoneSubmitted()
    //     0x14a8704: bl              #0x131dbcc  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onPhoneSubmitted
    // 0x14a8708: r0 = Null
    //     0x14a8708: mov             x0, NULL
    // 0x14a870c: r0 = ReturnAsyncNotFuture()
    //     0x14a870c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14a8710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a8710: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a8714: b               #0x14a86e0
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0x14a8718, size: 0x3c
    // 0x14a8718: EnterFrame
    //     0x14a8718: stp             fp, lr, [SP, #-0x10]!
    //     0x14a871c: mov             fp, SP
    // 0x14a8720: ldr             x0, [fp, #0x18]
    // 0x14a8724: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14a8724: ldur            w1, [x0, #0x17]
    // 0x14a8728: DecompressPointer r1
    //     0x14a8728: add             x1, x1, HEAP, lsl #32
    // 0x14a872c: CheckStackOverflow
    //     0x14a872c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a8730: cmp             SP, x16
    //     0x14a8734: b.ls            #0x14a874c
    // 0x14a8738: ldr             x2, [fp, #0x10]
    // 0x14a873c: r0 = _validatePhoneNumber()
    //     0x14a873c: bl              #0x1404d40  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::_validatePhoneNumber
    // 0x14a8740: LeaveFrame
    //     0x14a8740: mov             SP, fp
    //     0x14a8744: ldp             fp, lr, [SP], #0x10
    // 0x14a8748: ret
    //     0x14a8748: ret             
    // 0x14a874c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a874c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a8750: b               #0x14a8738
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14a8754, size: 0x48
    // 0x14a8754: EnterFrame
    //     0x14a8754: stp             fp, lr, [SP, #-0x10]!
    //     0x14a8758: mov             fp, SP
    // 0x14a875c: ldr             x0, [fp, #0x10]
    // 0x14a8760: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14a8760: ldur            w1, [x0, #0x17]
    // 0x14a8764: DecompressPointer r1
    //     0x14a8764: add             x1, x1, HEAP, lsl #32
    // 0x14a8768: CheckStackOverflow
    //     0x14a8768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a876c: cmp             SP, x16
    //     0x14a8770: b.ls            #0x14a8794
    // 0x14a8774: LoadField: r0 = r1->field_f
    //     0x14a8774: ldur            w0, [x1, #0xf]
    // 0x14a8778: DecompressPointer r0
    //     0x14a8778: add             x0, x0, HEAP, lsl #32
    // 0x14a877c: mov             x1, x0
    // 0x14a8780: r0 = onOtpSubmit()
    //     0x14a8780: bl              #0x14056a0  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onOtpSubmit
    // 0x14a8784: r0 = Null
    //     0x14a8784: mov             x0, NULL
    // 0x14a8788: LeaveFrame
    //     0x14a8788: mov             SP, fp
    //     0x14a878c: ldp             fp, lr, [SP], #0x10
    // 0x14a8790: ret
    //     0x14a8790: ret             
    // 0x14a8794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a8794: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a8798: b               #0x14a8774
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15db654, size: 0x2b4
    // 0x15db654: EnterFrame
    //     0x15db654: stp             fp, lr, [SP, #-0x10]!
    //     0x15db658: mov             fp, SP
    // 0x15db65c: AllocStack(0x30)
    //     0x15db65c: sub             SP, SP, #0x30
    // 0x15db660: SetupParameters(LoginView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15db660: stur            x1, [fp, #-8]
    //     0x15db664: stur            x2, [fp, #-0x10]
    // 0x15db668: CheckStackOverflow
    //     0x15db668: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15db66c: cmp             SP, x16
    //     0x15db670: b.ls            #0x15db900
    // 0x15db674: r1 = 2
    //     0x15db674: movz            x1, #0x2
    // 0x15db678: r0 = AllocateContext()
    //     0x15db678: bl              #0x16f6108  ; AllocateContextStub
    // 0x15db67c: ldur            x1, [fp, #-8]
    // 0x15db680: stur            x0, [fp, #-0x18]
    // 0x15db684: StoreField: r0->field_f = r1
    //     0x15db684: stur            w1, [x0, #0xf]
    // 0x15db688: ldur            x2, [fp, #-0x10]
    // 0x15db68c: StoreField: r0->field_13 = r2
    //     0x15db68c: stur            w2, [x0, #0x13]
    // 0x15db690: r0 = Obx()
    //     0x15db690: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15db694: ldur            x2, [fp, #-0x18]
    // 0x15db698: r1 = Function '<anonymous closure>':.
    //     0x15db698: add             x1, PP, #0x43, lsl #12  ; [pp+0x43278] AnonymousClosure: (0x15d22bc), in [package:customer_app/app/presentation/views/glass/login/login_view.dart] LoginView::appBar (0x15e26c0)
    //     0x15db69c: ldr             x1, [x1, #0x278]
    // 0x15db6a0: stur            x0, [fp, #-0x10]
    // 0x15db6a4: r0 = AllocateClosure()
    //     0x15db6a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15db6a8: mov             x1, x0
    // 0x15db6ac: ldur            x0, [fp, #-0x10]
    // 0x15db6b0: StoreField: r0->field_b = r1
    //     0x15db6b0: stur            w1, [x0, #0xb]
    // 0x15db6b4: ldur            x1, [fp, #-8]
    // 0x15db6b8: r0 = controller()
    //     0x15db6b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15db6bc: LoadField: r1 = r0->field_bf
    //     0x15db6bc: ldur            w1, [x0, #0xbf]
    // 0x15db6c0: DecompressPointer r1
    //     0x15db6c0: add             x1, x1, HEAP, lsl #32
    // 0x15db6c4: r0 = value()
    //     0x15db6c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15db6c8: tbnz            w0, #4, #0x15db760
    // 0x15db6cc: ldur            x2, [fp, #-0x18]
    // 0x15db6d0: LoadField: r1 = r2->field_13
    //     0x15db6d0: ldur            w1, [x2, #0x13]
    // 0x15db6d4: DecompressPointer r1
    //     0x15db6d4: add             x1, x1, HEAP, lsl #32
    // 0x15db6d8: r0 = of()
    //     0x15db6d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15db6dc: LoadField: r1 = r0->field_5b
    //     0x15db6dc: ldur            w1, [x0, #0x5b]
    // 0x15db6e0: DecompressPointer r1
    //     0x15db6e0: add             x1, x1, HEAP, lsl #32
    // 0x15db6e4: stur            x1, [fp, #-8]
    // 0x15db6e8: r0 = ColorFilter()
    //     0x15db6e8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15db6ec: mov             x1, x0
    // 0x15db6f0: ldur            x0, [fp, #-8]
    // 0x15db6f4: stur            x1, [fp, #-0x20]
    // 0x15db6f8: StoreField: r1->field_7 = r0
    //     0x15db6f8: stur            w0, [x1, #7]
    // 0x15db6fc: r0 = Instance_BlendMode
    //     0x15db6fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15db700: ldr             x0, [x0, #0xb30]
    // 0x15db704: StoreField: r1->field_b = r0
    //     0x15db704: stur            w0, [x1, #0xb]
    // 0x15db708: r2 = 1
    //     0x15db708: movz            x2, #0x1
    // 0x15db70c: StoreField: r1->field_13 = r2
    //     0x15db70c: stur            x2, [x1, #0x13]
    // 0x15db710: r0 = SvgPicture()
    //     0x15db710: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15db714: stur            x0, [fp, #-8]
    // 0x15db718: ldur            x16, [fp, #-0x20]
    // 0x15db71c: str             x16, [SP]
    // 0x15db720: mov             x1, x0
    // 0x15db724: r2 = "assets/images/search.svg"
    //     0x15db724: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15db728: ldr             x2, [x2, #0xa30]
    // 0x15db72c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15db72c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15db730: ldr             x4, [x4, #0xa38]
    // 0x15db734: r0 = SvgPicture.asset()
    //     0x15db734: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15db738: r0 = Align()
    //     0x15db738: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15db73c: r3 = Instance_Alignment
    //     0x15db73c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15db740: ldr             x3, [x3, #0xb10]
    // 0x15db744: StoreField: r0->field_f = r3
    //     0x15db744: stur            w3, [x0, #0xf]
    // 0x15db748: r4 = 1.000000
    //     0x15db748: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15db74c: StoreField: r0->field_13 = r4
    //     0x15db74c: stur            w4, [x0, #0x13]
    // 0x15db750: ArrayStore: r0[0] = r4  ; List_4
    //     0x15db750: stur            w4, [x0, #0x17]
    // 0x15db754: ldur            x1, [fp, #-8]
    // 0x15db758: StoreField: r0->field_b = r1
    //     0x15db758: stur            w1, [x0, #0xb]
    // 0x15db75c: b               #0x15db810
    // 0x15db760: ldur            x5, [fp, #-0x18]
    // 0x15db764: r4 = 1.000000
    //     0x15db764: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15db768: r0 = Instance_BlendMode
    //     0x15db768: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15db76c: ldr             x0, [x0, #0xb30]
    // 0x15db770: r3 = Instance_Alignment
    //     0x15db770: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15db774: ldr             x3, [x3, #0xb10]
    // 0x15db778: r2 = 1
    //     0x15db778: movz            x2, #0x1
    // 0x15db77c: LoadField: r1 = r5->field_13
    //     0x15db77c: ldur            w1, [x5, #0x13]
    // 0x15db780: DecompressPointer r1
    //     0x15db780: add             x1, x1, HEAP, lsl #32
    // 0x15db784: r0 = of()
    //     0x15db784: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15db788: LoadField: r1 = r0->field_5b
    //     0x15db788: ldur            w1, [x0, #0x5b]
    // 0x15db78c: DecompressPointer r1
    //     0x15db78c: add             x1, x1, HEAP, lsl #32
    // 0x15db790: stur            x1, [fp, #-8]
    // 0x15db794: r0 = ColorFilter()
    //     0x15db794: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15db798: mov             x1, x0
    // 0x15db79c: ldur            x0, [fp, #-8]
    // 0x15db7a0: stur            x1, [fp, #-0x20]
    // 0x15db7a4: StoreField: r1->field_7 = r0
    //     0x15db7a4: stur            w0, [x1, #7]
    // 0x15db7a8: r0 = Instance_BlendMode
    //     0x15db7a8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15db7ac: ldr             x0, [x0, #0xb30]
    // 0x15db7b0: StoreField: r1->field_b = r0
    //     0x15db7b0: stur            w0, [x1, #0xb]
    // 0x15db7b4: r0 = 1
    //     0x15db7b4: movz            x0, #0x1
    // 0x15db7b8: StoreField: r1->field_13 = r0
    //     0x15db7b8: stur            x0, [x1, #0x13]
    // 0x15db7bc: r0 = SvgPicture()
    //     0x15db7bc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15db7c0: stur            x0, [fp, #-8]
    // 0x15db7c4: ldur            x16, [fp, #-0x20]
    // 0x15db7c8: str             x16, [SP]
    // 0x15db7cc: mov             x1, x0
    // 0x15db7d0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15db7d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15db7d4: ldr             x2, [x2, #0xa40]
    // 0x15db7d8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15db7d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15db7dc: ldr             x4, [x4, #0xa38]
    // 0x15db7e0: r0 = SvgPicture.asset()
    //     0x15db7e0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15db7e4: r0 = Align()
    //     0x15db7e4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15db7e8: mov             x1, x0
    // 0x15db7ec: r0 = Instance_Alignment
    //     0x15db7ec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15db7f0: ldr             x0, [x0, #0xb10]
    // 0x15db7f4: StoreField: r1->field_f = r0
    //     0x15db7f4: stur            w0, [x1, #0xf]
    // 0x15db7f8: r0 = 1.000000
    //     0x15db7f8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15db7fc: StoreField: r1->field_13 = r0
    //     0x15db7fc: stur            w0, [x1, #0x13]
    // 0x15db800: ArrayStore: r1[0] = r0  ; List_4
    //     0x15db800: stur            w0, [x1, #0x17]
    // 0x15db804: ldur            x0, [fp, #-8]
    // 0x15db808: StoreField: r1->field_b = r0
    //     0x15db808: stur            w0, [x1, #0xb]
    // 0x15db80c: mov             x0, x1
    // 0x15db810: stur            x0, [fp, #-8]
    // 0x15db814: r0 = InkWell()
    //     0x15db814: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15db818: mov             x3, x0
    // 0x15db81c: ldur            x0, [fp, #-8]
    // 0x15db820: stur            x3, [fp, #-0x20]
    // 0x15db824: StoreField: r3->field_b = r0
    //     0x15db824: stur            w0, [x3, #0xb]
    // 0x15db828: ldur            x2, [fp, #-0x18]
    // 0x15db82c: r1 = Function '<anonymous closure>':.
    //     0x15db82c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43280] AnonymousClosure: (0x15dbce0), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::appBar (0x15db654)
    //     0x15db830: ldr             x1, [x1, #0x280]
    // 0x15db834: r0 = AllocateClosure()
    //     0x15db834: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15db838: ldur            x2, [fp, #-0x20]
    // 0x15db83c: StoreField: r2->field_f = r0
    //     0x15db83c: stur            w0, [x2, #0xf]
    // 0x15db840: r0 = true
    //     0x15db840: add             x0, NULL, #0x20  ; true
    // 0x15db844: StoreField: r2->field_43 = r0
    //     0x15db844: stur            w0, [x2, #0x43]
    // 0x15db848: r1 = Instance_BoxShape
    //     0x15db848: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15db84c: ldr             x1, [x1, #0x80]
    // 0x15db850: StoreField: r2->field_47 = r1
    //     0x15db850: stur            w1, [x2, #0x47]
    // 0x15db854: StoreField: r2->field_6f = r0
    //     0x15db854: stur            w0, [x2, #0x6f]
    // 0x15db858: r1 = false
    //     0x15db858: add             x1, NULL, #0x30  ; false
    // 0x15db85c: StoreField: r2->field_73 = r1
    //     0x15db85c: stur            w1, [x2, #0x73]
    // 0x15db860: StoreField: r2->field_83 = r0
    //     0x15db860: stur            w0, [x2, #0x83]
    // 0x15db864: StoreField: r2->field_7b = r1
    //     0x15db864: stur            w1, [x2, #0x7b]
    // 0x15db868: r0 = Obx()
    //     0x15db868: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15db86c: ldur            x2, [fp, #-0x18]
    // 0x15db870: r1 = Function '<anonymous closure>':.
    //     0x15db870: add             x1, PP, #0x43, lsl #12  ; [pp+0x43288] AnonymousClosure: (0x15db908), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::appBar (0x15db654)
    //     0x15db874: ldr             x1, [x1, #0x288]
    // 0x15db878: stur            x0, [fp, #-8]
    // 0x15db87c: r0 = AllocateClosure()
    //     0x15db87c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15db880: mov             x1, x0
    // 0x15db884: ldur            x0, [fp, #-8]
    // 0x15db888: StoreField: r0->field_b = r1
    //     0x15db888: stur            w1, [x0, #0xb]
    // 0x15db88c: r1 = Null
    //     0x15db88c: mov             x1, NULL
    // 0x15db890: r2 = 2
    //     0x15db890: movz            x2, #0x2
    // 0x15db894: r0 = AllocateArray()
    //     0x15db894: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15db898: mov             x2, x0
    // 0x15db89c: ldur            x0, [fp, #-8]
    // 0x15db8a0: stur            x2, [fp, #-0x18]
    // 0x15db8a4: StoreField: r2->field_f = r0
    //     0x15db8a4: stur            w0, [x2, #0xf]
    // 0x15db8a8: r1 = <Widget>
    //     0x15db8a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15db8ac: r0 = AllocateGrowableArray()
    //     0x15db8ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15db8b0: mov             x1, x0
    // 0x15db8b4: ldur            x0, [fp, #-0x18]
    // 0x15db8b8: stur            x1, [fp, #-8]
    // 0x15db8bc: StoreField: r1->field_f = r0
    //     0x15db8bc: stur            w0, [x1, #0xf]
    // 0x15db8c0: r0 = 2
    //     0x15db8c0: movz            x0, #0x2
    // 0x15db8c4: StoreField: r1->field_b = r0
    //     0x15db8c4: stur            w0, [x1, #0xb]
    // 0x15db8c8: r0 = AppBar()
    //     0x15db8c8: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15db8cc: stur            x0, [fp, #-0x18]
    // 0x15db8d0: ldur            x16, [fp, #-0x10]
    // 0x15db8d4: ldur            lr, [fp, #-8]
    // 0x15db8d8: stp             lr, x16, [SP]
    // 0x15db8dc: mov             x1, x0
    // 0x15db8e0: ldur            x2, [fp, #-0x20]
    // 0x15db8e4: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15db8e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15db8e8: ldr             x4, [x4, #0xa58]
    // 0x15db8ec: r0 = AppBar()
    //     0x15db8ec: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15db8f0: ldur            x0, [fp, #-0x18]
    // 0x15db8f4: LeaveFrame
    //     0x15db8f4: mov             SP, fp
    //     0x15db8f8: ldp             fp, lr, [SP], #0x10
    // 0x15db8fc: ret
    //     0x15db8fc: ret             
    // 0x15db900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15db900: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15db904: b               #0x15db674
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15db908, size: 0x2fc
    // 0x15db908: EnterFrame
    //     0x15db908: stp             fp, lr, [SP, #-0x10]!
    //     0x15db90c: mov             fp, SP
    // 0x15db910: AllocStack(0x58)
    //     0x15db910: sub             SP, SP, #0x58
    // 0x15db914: SetupParameters()
    //     0x15db914: ldr             x0, [fp, #0x10]
    //     0x15db918: ldur            w2, [x0, #0x17]
    //     0x15db91c: add             x2, x2, HEAP, lsl #32
    //     0x15db920: stur            x2, [fp, #-8]
    // 0x15db924: CheckStackOverflow
    //     0x15db924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15db928: cmp             SP, x16
    //     0x15db92c: b.ls            #0x15dbbfc
    // 0x15db930: LoadField: r1 = r2->field_f
    //     0x15db930: ldur            w1, [x2, #0xf]
    // 0x15db934: DecompressPointer r1
    //     0x15db934: add             x1, x1, HEAP, lsl #32
    // 0x15db938: r0 = controller()
    //     0x15db938: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15db93c: LoadField: r1 = r0->field_7b
    //     0x15db93c: ldur            w1, [x0, #0x7b]
    // 0x15db940: DecompressPointer r1
    //     0x15db940: add             x1, x1, HEAP, lsl #32
    // 0x15db944: r0 = value()
    //     0x15db944: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15db948: LoadField: r1 = r0->field_1f
    //     0x15db948: ldur            w1, [x0, #0x1f]
    // 0x15db94c: DecompressPointer r1
    //     0x15db94c: add             x1, x1, HEAP, lsl #32
    // 0x15db950: cmp             w1, NULL
    // 0x15db954: b.ne            #0x15db960
    // 0x15db958: r0 = Null
    //     0x15db958: mov             x0, NULL
    // 0x15db95c: b               #0x15db968
    // 0x15db960: LoadField: r0 = r1->field_7
    //     0x15db960: ldur            w0, [x1, #7]
    // 0x15db964: DecompressPointer r0
    //     0x15db964: add             x0, x0, HEAP, lsl #32
    // 0x15db968: cmp             w0, NULL
    // 0x15db96c: b.ne            #0x15db978
    // 0x15db970: r0 = false
    //     0x15db970: add             x0, NULL, #0x30  ; false
    // 0x15db974: b               #0x15dbb64
    // 0x15db978: tbnz            w0, #4, #0x15dbb60
    // 0x15db97c: ldur            x2, [fp, #-8]
    // 0x15db980: LoadField: r1 = r2->field_f
    //     0x15db980: ldur            w1, [x2, #0xf]
    // 0x15db984: DecompressPointer r1
    //     0x15db984: add             x1, x1, HEAP, lsl #32
    // 0x15db988: r0 = controller()
    //     0x15db988: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15db98c: LoadField: r1 = r0->field_c3
    //     0x15db98c: ldur            w1, [x0, #0xc3]
    // 0x15db990: DecompressPointer r1
    //     0x15db990: add             x1, x1, HEAP, lsl #32
    // 0x15db994: r0 = value()
    //     0x15db994: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15db998: ldur            x2, [fp, #-8]
    // 0x15db99c: stur            x0, [fp, #-0x10]
    // 0x15db9a0: LoadField: r1 = r2->field_13
    //     0x15db9a0: ldur            w1, [x2, #0x13]
    // 0x15db9a4: DecompressPointer r1
    //     0x15db9a4: add             x1, x1, HEAP, lsl #32
    // 0x15db9a8: r0 = of()
    //     0x15db9a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15db9ac: LoadField: r2 = r0->field_5b
    //     0x15db9ac: ldur            w2, [x0, #0x5b]
    // 0x15db9b0: DecompressPointer r2
    //     0x15db9b0: add             x2, x2, HEAP, lsl #32
    // 0x15db9b4: ldur            x0, [fp, #-8]
    // 0x15db9b8: stur            x2, [fp, #-0x18]
    // 0x15db9bc: LoadField: r1 = r0->field_f
    //     0x15db9bc: ldur            w1, [x0, #0xf]
    // 0x15db9c0: DecompressPointer r1
    //     0x15db9c0: add             x1, x1, HEAP, lsl #32
    // 0x15db9c4: r0 = controller()
    //     0x15db9c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15db9c8: LoadField: r1 = r0->field_c7
    //     0x15db9c8: ldur            w1, [x0, #0xc7]
    // 0x15db9cc: DecompressPointer r1
    //     0x15db9cc: add             x1, x1, HEAP, lsl #32
    // 0x15db9d0: r0 = value()
    //     0x15db9d0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15db9d4: cmp             w0, NULL
    // 0x15db9d8: r16 = true
    //     0x15db9d8: add             x16, NULL, #0x20  ; true
    // 0x15db9dc: r17 = false
    //     0x15db9dc: add             x17, NULL, #0x30  ; false
    // 0x15db9e0: csel            x2, x16, x17, ne
    // 0x15db9e4: ldur            x0, [fp, #-8]
    // 0x15db9e8: stur            x2, [fp, #-0x20]
    // 0x15db9ec: LoadField: r1 = r0->field_f
    //     0x15db9ec: ldur            w1, [x0, #0xf]
    // 0x15db9f0: DecompressPointer r1
    //     0x15db9f0: add             x1, x1, HEAP, lsl #32
    // 0x15db9f4: r0 = controller()
    //     0x15db9f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15db9f8: LoadField: r1 = r0->field_c7
    //     0x15db9f8: ldur            w1, [x0, #0xc7]
    // 0x15db9fc: DecompressPointer r1
    //     0x15db9fc: add             x1, x1, HEAP, lsl #32
    // 0x15dba00: r0 = value()
    //     0x15dba00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dba04: str             x0, [SP]
    // 0x15dba08: r0 = _interpolateSingle()
    //     0x15dba08: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15dba0c: ldur            x2, [fp, #-8]
    // 0x15dba10: stur            x0, [fp, #-0x28]
    // 0x15dba14: LoadField: r1 = r2->field_13
    //     0x15dba14: ldur            w1, [x2, #0x13]
    // 0x15dba18: DecompressPointer r1
    //     0x15dba18: add             x1, x1, HEAP, lsl #32
    // 0x15dba1c: r0 = of()
    //     0x15dba1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dba20: LoadField: r1 = r0->field_87
    //     0x15dba20: ldur            w1, [x0, #0x87]
    // 0x15dba24: DecompressPointer r1
    //     0x15dba24: add             x1, x1, HEAP, lsl #32
    // 0x15dba28: LoadField: r0 = r1->field_27
    //     0x15dba28: ldur            w0, [x1, #0x27]
    // 0x15dba2c: DecompressPointer r0
    //     0x15dba2c: add             x0, x0, HEAP, lsl #32
    // 0x15dba30: r16 = Instance_Color
    //     0x15dba30: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15dba34: str             x16, [SP]
    // 0x15dba38: mov             x1, x0
    // 0x15dba3c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15dba3c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15dba40: ldr             x4, [x4, #0xf40]
    // 0x15dba44: r0 = copyWith()
    //     0x15dba44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15dba48: stur            x0, [fp, #-0x30]
    // 0x15dba4c: r0 = Text()
    //     0x15dba4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15dba50: mov             x2, x0
    // 0x15dba54: ldur            x0, [fp, #-0x28]
    // 0x15dba58: stur            x2, [fp, #-0x38]
    // 0x15dba5c: StoreField: r2->field_b = r0
    //     0x15dba5c: stur            w0, [x2, #0xb]
    // 0x15dba60: ldur            x0, [fp, #-0x30]
    // 0x15dba64: StoreField: r2->field_13 = r0
    //     0x15dba64: stur            w0, [x2, #0x13]
    // 0x15dba68: ldur            x0, [fp, #-8]
    // 0x15dba6c: LoadField: r1 = r0->field_13
    //     0x15dba6c: ldur            w1, [x0, #0x13]
    // 0x15dba70: DecompressPointer r1
    //     0x15dba70: add             x1, x1, HEAP, lsl #32
    // 0x15dba74: r0 = of()
    //     0x15dba74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dba78: LoadField: r1 = r0->field_5b
    //     0x15dba78: ldur            w1, [x0, #0x5b]
    // 0x15dba7c: DecompressPointer r1
    //     0x15dba7c: add             x1, x1, HEAP, lsl #32
    // 0x15dba80: stur            x1, [fp, #-0x28]
    // 0x15dba84: r0 = ColorFilter()
    //     0x15dba84: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dba88: mov             x1, x0
    // 0x15dba8c: ldur            x0, [fp, #-0x28]
    // 0x15dba90: stur            x1, [fp, #-0x30]
    // 0x15dba94: StoreField: r1->field_7 = r0
    //     0x15dba94: stur            w0, [x1, #7]
    // 0x15dba98: r0 = Instance_BlendMode
    //     0x15dba98: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dba9c: ldr             x0, [x0, #0xb30]
    // 0x15dbaa0: StoreField: r1->field_b = r0
    //     0x15dbaa0: stur            w0, [x1, #0xb]
    // 0x15dbaa4: r0 = 1
    //     0x15dbaa4: movz            x0, #0x1
    // 0x15dbaa8: StoreField: r1->field_13 = r0
    //     0x15dbaa8: stur            x0, [x1, #0x13]
    // 0x15dbaac: r0 = SvgPicture()
    //     0x15dbaac: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dbab0: stur            x0, [fp, #-0x28]
    // 0x15dbab4: r16 = Instance_BoxFit
    //     0x15dbab4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15dbab8: ldr             x16, [x16, #0xb18]
    // 0x15dbabc: r30 = 24.000000
    //     0x15dbabc: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15dbac0: ldr             lr, [lr, #0xba8]
    // 0x15dbac4: stp             lr, x16, [SP, #0x10]
    // 0x15dbac8: r16 = 24.000000
    //     0x15dbac8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15dbacc: ldr             x16, [x16, #0xba8]
    // 0x15dbad0: ldur            lr, [fp, #-0x30]
    // 0x15dbad4: stp             lr, x16, [SP]
    // 0x15dbad8: mov             x1, x0
    // 0x15dbadc: r2 = "assets/images/shopping_bag.svg"
    //     0x15dbadc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15dbae0: ldr             x2, [x2, #0xa60]
    // 0x15dbae4: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15dbae4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15dbae8: ldr             x4, [x4, #0xa68]
    // 0x15dbaec: r0 = SvgPicture.asset()
    //     0x15dbaec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dbaf0: r0 = Badge()
    //     0x15dbaf0: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15dbaf4: mov             x1, x0
    // 0x15dbaf8: ldur            x0, [fp, #-0x18]
    // 0x15dbafc: stur            x1, [fp, #-0x30]
    // 0x15dbb00: StoreField: r1->field_b = r0
    //     0x15dbb00: stur            w0, [x1, #0xb]
    // 0x15dbb04: ldur            x0, [fp, #-0x38]
    // 0x15dbb08: StoreField: r1->field_27 = r0
    //     0x15dbb08: stur            w0, [x1, #0x27]
    // 0x15dbb0c: ldur            x0, [fp, #-0x20]
    // 0x15dbb10: StoreField: r1->field_2b = r0
    //     0x15dbb10: stur            w0, [x1, #0x2b]
    // 0x15dbb14: ldur            x0, [fp, #-0x28]
    // 0x15dbb18: StoreField: r1->field_2f = r0
    //     0x15dbb18: stur            w0, [x1, #0x2f]
    // 0x15dbb1c: r0 = Visibility()
    //     0x15dbb1c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15dbb20: mov             x1, x0
    // 0x15dbb24: ldur            x0, [fp, #-0x30]
    // 0x15dbb28: StoreField: r1->field_b = r0
    //     0x15dbb28: stur            w0, [x1, #0xb]
    // 0x15dbb2c: r0 = Instance_SizedBox
    //     0x15dbb2c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15dbb30: StoreField: r1->field_f = r0
    //     0x15dbb30: stur            w0, [x1, #0xf]
    // 0x15dbb34: ldur            x0, [fp, #-0x10]
    // 0x15dbb38: StoreField: r1->field_13 = r0
    //     0x15dbb38: stur            w0, [x1, #0x13]
    // 0x15dbb3c: r0 = false
    //     0x15dbb3c: add             x0, NULL, #0x30  ; false
    // 0x15dbb40: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dbb40: stur            w0, [x1, #0x17]
    // 0x15dbb44: StoreField: r1->field_1b = r0
    //     0x15dbb44: stur            w0, [x1, #0x1b]
    // 0x15dbb48: StoreField: r1->field_1f = r0
    //     0x15dbb48: stur            w0, [x1, #0x1f]
    // 0x15dbb4c: StoreField: r1->field_23 = r0
    //     0x15dbb4c: stur            w0, [x1, #0x23]
    // 0x15dbb50: StoreField: r1->field_27 = r0
    //     0x15dbb50: stur            w0, [x1, #0x27]
    // 0x15dbb54: StoreField: r1->field_2b = r0
    //     0x15dbb54: stur            w0, [x1, #0x2b]
    // 0x15dbb58: mov             x0, x1
    // 0x15dbb5c: b               #0x15dbb7c
    // 0x15dbb60: r0 = false
    //     0x15dbb60: add             x0, NULL, #0x30  ; false
    // 0x15dbb64: r0 = Container()
    //     0x15dbb64: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15dbb68: mov             x1, x0
    // 0x15dbb6c: stur            x0, [fp, #-0x10]
    // 0x15dbb70: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15dbb70: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15dbb74: r0 = Container()
    //     0x15dbb74: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15dbb78: ldur            x0, [fp, #-0x10]
    // 0x15dbb7c: stur            x0, [fp, #-0x10]
    // 0x15dbb80: r0 = InkWell()
    //     0x15dbb80: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dbb84: mov             x3, x0
    // 0x15dbb88: ldur            x0, [fp, #-0x10]
    // 0x15dbb8c: stur            x3, [fp, #-0x18]
    // 0x15dbb90: StoreField: r3->field_b = r0
    //     0x15dbb90: stur            w0, [x3, #0xb]
    // 0x15dbb94: ldur            x2, [fp, #-8]
    // 0x15dbb98: r1 = Function '<anonymous closure>':.
    //     0x15dbb98: add             x1, PP, #0x43, lsl #12  ; [pp+0x43290] AnonymousClosure: (0x15dbc04), in [package:customer_app/app/presentation/views/cosmetic/login/login_view.dart] LoginView::appBar (0x15db654)
    //     0x15dbb9c: ldr             x1, [x1, #0x290]
    // 0x15dbba0: r0 = AllocateClosure()
    //     0x15dbba0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dbba4: mov             x1, x0
    // 0x15dbba8: ldur            x0, [fp, #-0x18]
    // 0x15dbbac: StoreField: r0->field_f = r1
    //     0x15dbbac: stur            w1, [x0, #0xf]
    // 0x15dbbb0: r1 = true
    //     0x15dbbb0: add             x1, NULL, #0x20  ; true
    // 0x15dbbb4: StoreField: r0->field_43 = r1
    //     0x15dbbb4: stur            w1, [x0, #0x43]
    // 0x15dbbb8: r2 = Instance_BoxShape
    //     0x15dbbb8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dbbbc: ldr             x2, [x2, #0x80]
    // 0x15dbbc0: StoreField: r0->field_47 = r2
    //     0x15dbbc0: stur            w2, [x0, #0x47]
    // 0x15dbbc4: StoreField: r0->field_6f = r1
    //     0x15dbbc4: stur            w1, [x0, #0x6f]
    // 0x15dbbc8: r2 = false
    //     0x15dbbc8: add             x2, NULL, #0x30  ; false
    // 0x15dbbcc: StoreField: r0->field_73 = r2
    //     0x15dbbcc: stur            w2, [x0, #0x73]
    // 0x15dbbd0: StoreField: r0->field_83 = r1
    //     0x15dbbd0: stur            w1, [x0, #0x83]
    // 0x15dbbd4: StoreField: r0->field_7b = r2
    //     0x15dbbd4: stur            w2, [x0, #0x7b]
    // 0x15dbbd8: r0 = Padding()
    //     0x15dbbd8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15dbbdc: r1 = Instance_EdgeInsets
    //     0x15dbbdc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37290] Obj!EdgeInsets@d5a111
    //     0x15dbbe0: ldr             x1, [x1, #0x290]
    // 0x15dbbe4: StoreField: r0->field_f = r1
    //     0x15dbbe4: stur            w1, [x0, #0xf]
    // 0x15dbbe8: ldur            x1, [fp, #-0x18]
    // 0x15dbbec: StoreField: r0->field_b = r1
    //     0x15dbbec: stur            w1, [x0, #0xb]
    // 0x15dbbf0: LeaveFrame
    //     0x15dbbf0: mov             SP, fp
    //     0x15dbbf4: ldp             fp, lr, [SP], #0x10
    // 0x15dbbf8: ret
    //     0x15dbbf8: ret             
    // 0x15dbbfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dbbfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dbc00: b               #0x15db930
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15dbc04, size: 0xdc
    // 0x15dbc04: EnterFrame
    //     0x15dbc04: stp             fp, lr, [SP, #-0x10]!
    //     0x15dbc08: mov             fp, SP
    // 0x15dbc0c: AllocStack(0x28)
    //     0x15dbc0c: sub             SP, SP, #0x28
    // 0x15dbc10: SetupParameters()
    //     0x15dbc10: ldr             x0, [fp, #0x10]
    //     0x15dbc14: ldur            w2, [x0, #0x17]
    //     0x15dbc18: add             x2, x2, HEAP, lsl #32
    //     0x15dbc1c: stur            x2, [fp, #-8]
    // 0x15dbc20: CheckStackOverflow
    //     0x15dbc20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dbc24: cmp             SP, x16
    //     0x15dbc28: b.ls            #0x15dbcd8
    // 0x15dbc2c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15dbc2c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15dbc30: ldr             x0, [x0, #0x1c80]
    //     0x15dbc34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15dbc38: cmp             w0, w16
    //     0x15dbc3c: b.ne            #0x15dbc48
    //     0x15dbc40: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15dbc44: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15dbc48: r1 = Null
    //     0x15dbc48: mov             x1, NULL
    // 0x15dbc4c: r2 = 4
    //     0x15dbc4c: movz            x2, #0x4
    // 0x15dbc50: r0 = AllocateArray()
    //     0x15dbc50: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15dbc54: r16 = "previousScreenSource"
    //     0x15dbc54: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15dbc58: ldr             x16, [x16, #0x448]
    // 0x15dbc5c: StoreField: r0->field_f = r16
    //     0x15dbc5c: stur            w16, [x0, #0xf]
    // 0x15dbc60: r16 = "login_page"
    //     0x15dbc60: add             x16, PP, #0x37, lsl #12  ; [pp+0x37298] "login_page"
    //     0x15dbc64: ldr             x16, [x16, #0x298]
    // 0x15dbc68: StoreField: r0->field_13 = r16
    //     0x15dbc68: stur            w16, [x0, #0x13]
    // 0x15dbc6c: r16 = <String, String>
    //     0x15dbc6c: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15dbc70: ldr             x16, [x16, #0x788]
    // 0x15dbc74: stp             x0, x16, [SP]
    // 0x15dbc78: r0 = Map._fromLiteral()
    //     0x15dbc78: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15dbc7c: r16 = "/bag"
    //     0x15dbc7c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15dbc80: ldr             x16, [x16, #0x468]
    // 0x15dbc84: stp             x16, NULL, [SP, #8]
    // 0x15dbc88: str             x0, [SP]
    // 0x15dbc8c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15dbc8c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15dbc90: ldr             x4, [x4, #0x438]
    // 0x15dbc94: r0 = GetNavigation.toNamed()
    //     0x15dbc94: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15dbc98: stur            x0, [fp, #-0x10]
    // 0x15dbc9c: cmp             w0, NULL
    // 0x15dbca0: b.eq            #0x15dbcc8
    // 0x15dbca4: ldur            x2, [fp, #-8]
    // 0x15dbca8: r1 = Function '<anonymous closure>':.
    //     0x15dbca8: add             x1, PP, #0x43, lsl #12  ; [pp+0x43298] AnonymousClosure: (0x15d1de4), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15dbcac: ldr             x1, [x1, #0x298]
    // 0x15dbcb0: r0 = AllocateClosure()
    //     0x15dbcb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dbcb4: ldur            x16, [fp, #-0x10]
    // 0x15dbcb8: stp             x16, NULL, [SP, #8]
    // 0x15dbcbc: str             x0, [SP]
    // 0x15dbcc0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15dbcc0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15dbcc4: r0 = then()
    //     0x15dbcc4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15dbcc8: r0 = Null
    //     0x15dbcc8: mov             x0, NULL
    // 0x15dbccc: LeaveFrame
    //     0x15dbccc: mov             SP, fp
    //     0x15dbcd0: ldp             fp, lr, [SP], #0x10
    // 0x15dbcd4: ret
    //     0x15dbcd4: ret             
    // 0x15dbcd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dbcd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dbcdc: b               #0x15dbc2c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15dbce0, size: 0xb4
    // 0x15dbce0: EnterFrame
    //     0x15dbce0: stp             fp, lr, [SP, #-0x10]!
    //     0x15dbce4: mov             fp, SP
    // 0x15dbce8: AllocStack(0x18)
    //     0x15dbce8: sub             SP, SP, #0x18
    // 0x15dbcec: SetupParameters()
    //     0x15dbcec: ldr             x0, [fp, #0x10]
    //     0x15dbcf0: ldur            w3, [x0, #0x17]
    //     0x15dbcf4: add             x3, x3, HEAP, lsl #32
    //     0x15dbcf8: stur            x3, [fp, #-8]
    // 0x15dbcfc: CheckStackOverflow
    //     0x15dbcfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dbd00: cmp             SP, x16
    //     0x15dbd04: b.ls            #0x15dbd8c
    // 0x15dbd08: LoadField: r1 = r3->field_f
    //     0x15dbd08: ldur            w1, [x3, #0xf]
    // 0x15dbd0c: DecompressPointer r1
    //     0x15dbd0c: add             x1, x1, HEAP, lsl #32
    // 0x15dbd10: r2 = false
    //     0x15dbd10: add             x2, NULL, #0x30  ; false
    // 0x15dbd14: r0 = showLoading()
    //     0x15dbd14: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15dbd18: ldur            x0, [fp, #-8]
    // 0x15dbd1c: LoadField: r1 = r0->field_f
    //     0x15dbd1c: ldur            w1, [x0, #0xf]
    // 0x15dbd20: DecompressPointer r1
    //     0x15dbd20: add             x1, x1, HEAP, lsl #32
    // 0x15dbd24: r0 = controller()
    //     0x15dbd24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dbd28: LoadField: r1 = r0->field_bf
    //     0x15dbd28: ldur            w1, [x0, #0xbf]
    // 0x15dbd2c: DecompressPointer r1
    //     0x15dbd2c: add             x1, x1, HEAP, lsl #32
    // 0x15dbd30: r0 = value()
    //     0x15dbd30: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dbd34: tbnz            w0, #4, #0x15dbd6c
    // 0x15dbd38: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15dbd38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15dbd3c: ldr             x0, [x0, #0x1c80]
    //     0x15dbd40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15dbd44: cmp             w0, w16
    //     0x15dbd48: b.ne            #0x15dbd54
    //     0x15dbd4c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15dbd50: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15dbd54: r16 = "/search"
    //     0x15dbd54: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15dbd58: ldr             x16, [x16, #0x838]
    // 0x15dbd5c: stp             x16, NULL, [SP]
    // 0x15dbd60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15dbd60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15dbd64: r0 = GetNavigation.toNamed()
    //     0x15dbd64: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15dbd68: b               #0x15dbd7c
    // 0x15dbd6c: ldur            x0, [fp, #-8]
    // 0x15dbd70: LoadField: r1 = r0->field_f
    //     0x15dbd70: ldur            w1, [x0, #0xf]
    // 0x15dbd74: DecompressPointer r1
    //     0x15dbd74: add             x1, x1, HEAP, lsl #32
    // 0x15dbd78: r0 = onBackPress()
    //     0x15dbd78: bl              #0x1401b54  ; [package:customer_app/app/presentation/views/basic/login/login_view.dart] LoginView::onBackPress
    // 0x15dbd7c: r0 = Null
    //     0x15dbd7c: mov             x0, NULL
    // 0x15dbd80: LeaveFrame
    //     0x15dbd80: mov             SP, fp
    //     0x15dbd84: ldp             fp, lr, [SP], #0x10
    // 0x15dbd88: ret
    //     0x15dbd88: ret             
    // 0x15dbd8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dbd8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dbd90: b               #0x15dbd08
  }
}
