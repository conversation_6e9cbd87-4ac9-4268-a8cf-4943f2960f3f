// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/partial_popup_bottom_sheet.dart

// class id: 1049498, size: 0x8
class :: {
}

// class id: 3264, size: 0x14, field offset: 0x14
class _PartialCodPopupBottomSheet extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbc2c90, size: 0xd9c
    // 0xbc2c90: EnterFrame
    //     0xbc2c90: stp             fp, lr, [SP, #-0x10]!
    //     0xbc2c94: mov             fp, SP
    // 0xbc2c98: AllocStack(0x70)
    //     0xbc2c98: sub             SP, SP, #0x70
    // 0xbc2c9c: SetupParameters(_PartialCodPopupBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbc2c9c: mov             x0, x1
    //     0xbc2ca0: stur            x1, [fp, #-8]
    //     0xbc2ca4: mov             x1, x2
    //     0xbc2ca8: stur            x2, [fp, #-0x10]
    // 0xbc2cac: CheckStackOverflow
    //     0xbc2cac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc2cb0: cmp             SP, x16
    //     0xbc2cb4: b.ls            #0xbc39f4
    // 0xbc2cb8: r1 = 1
    //     0xbc2cb8: movz            x1, #0x1
    // 0xbc2cbc: r0 = AllocateContext()
    //     0xbc2cbc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbc2cc0: mov             x2, x0
    // 0xbc2cc4: ldur            x0, [fp, #-8]
    // 0xbc2cc8: stur            x2, [fp, #-0x18]
    // 0xbc2ccc: StoreField: r2->field_f = r0
    //     0xbc2ccc: stur            w0, [x2, #0xf]
    // 0xbc2cd0: ldur            x1, [fp, #-0x10]
    // 0xbc2cd4: r0 = of()
    //     0xbc2cd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2cd8: LoadField: r1 = r0->field_87
    //     0xbc2cd8: ldur            w1, [x0, #0x87]
    // 0xbc2cdc: DecompressPointer r1
    //     0xbc2cdc: add             x1, x1, HEAP, lsl #32
    // 0xbc2ce0: LoadField: r0 = r1->field_7
    //     0xbc2ce0: ldur            w0, [x1, #7]
    // 0xbc2ce4: DecompressPointer r0
    //     0xbc2ce4: add             x0, x0, HEAP, lsl #32
    // 0xbc2ce8: r16 = Instance_Color
    //     0xbc2ce8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc2cec: r30 = 21.000000
    //     0xbc2cec: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbc2cf0: ldr             lr, [lr, #0x9b0]
    // 0xbc2cf4: stp             lr, x16, [SP]
    // 0xbc2cf8: mov             x1, x0
    // 0xbc2cfc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc2cfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc2d00: ldr             x4, [x4, #0x9b8]
    // 0xbc2d04: r0 = copyWith()
    //     0xbc2d04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2d08: stur            x0, [fp, #-0x20]
    // 0xbc2d0c: r0 = Text()
    //     0xbc2d0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2d10: mov             x2, x0
    // 0xbc2d14: r0 = "Pay in Advance"
    //     0xbc2d14: add             x0, PP, #0x56, lsl #12  ; [pp+0x56aa8] "Pay in Advance"
    //     0xbc2d18: ldr             x0, [x0, #0xaa8]
    // 0xbc2d1c: stur            x2, [fp, #-0x28]
    // 0xbc2d20: StoreField: r2->field_b = r0
    //     0xbc2d20: stur            w0, [x2, #0xb]
    // 0xbc2d24: ldur            x0, [fp, #-0x20]
    // 0xbc2d28: StoreField: r2->field_13 = r0
    //     0xbc2d28: stur            w0, [x2, #0x13]
    // 0xbc2d2c: ldur            x0, [fp, #-8]
    // 0xbc2d30: LoadField: r1 = r0->field_b
    //     0xbc2d30: ldur            w1, [x0, #0xb]
    // 0xbc2d34: DecompressPointer r1
    //     0xbc2d34: add             x1, x1, HEAP, lsl #32
    // 0xbc2d38: cmp             w1, NULL
    // 0xbc2d3c: b.eq            #0xbc39fc
    // 0xbc2d40: LoadField: r3 = r1->field_b
    //     0xbc2d40: ldur            w3, [x1, #0xb]
    // 0xbc2d44: DecompressPointer r3
    //     0xbc2d44: add             x3, x3, HEAP, lsl #32
    // 0xbc2d48: LoadField: r1 = r3->field_b
    //     0xbc2d48: ldur            w1, [x3, #0xb]
    // 0xbc2d4c: DecompressPointer r1
    //     0xbc2d4c: add             x1, x1, HEAP, lsl #32
    // 0xbc2d50: cmp             w1, NULL
    // 0xbc2d54: b.ne            #0xbc2d60
    // 0xbc2d58: r3 = ""
    //     0xbc2d58: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc2d5c: b               #0xbc2d64
    // 0xbc2d60: mov             x3, x1
    // 0xbc2d64: ldur            x1, [fp, #-0x10]
    // 0xbc2d68: stur            x3, [fp, #-0x20]
    // 0xbc2d6c: r0 = of()
    //     0xbc2d6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2d70: LoadField: r1 = r0->field_87
    //     0xbc2d70: ldur            w1, [x0, #0x87]
    // 0xbc2d74: DecompressPointer r1
    //     0xbc2d74: add             x1, x1, HEAP, lsl #32
    // 0xbc2d78: LoadField: r0 = r1->field_7
    //     0xbc2d78: ldur            w0, [x1, #7]
    // 0xbc2d7c: DecompressPointer r0
    //     0xbc2d7c: add             x0, x0, HEAP, lsl #32
    // 0xbc2d80: r16 = 12.000000
    //     0xbc2d80: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc2d84: ldr             x16, [x16, #0x9e8]
    // 0xbc2d88: r30 = Instance_Color
    //     0xbc2d88: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbc2d8c: stp             lr, x16, [SP]
    // 0xbc2d90: mov             x1, x0
    // 0xbc2d94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc2d94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc2d98: ldr             x4, [x4, #0xaa0]
    // 0xbc2d9c: r0 = copyWith()
    //     0xbc2d9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2da0: stur            x0, [fp, #-0x30]
    // 0xbc2da4: r0 = Text()
    //     0xbc2da4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2da8: mov             x1, x0
    // 0xbc2dac: ldur            x0, [fp, #-0x20]
    // 0xbc2db0: stur            x1, [fp, #-0x38]
    // 0xbc2db4: StoreField: r1->field_b = r0
    //     0xbc2db4: stur            w0, [x1, #0xb]
    // 0xbc2db8: ldur            x0, [fp, #-0x30]
    // 0xbc2dbc: StoreField: r1->field_13 = r0
    //     0xbc2dbc: stur            w0, [x1, #0x13]
    // 0xbc2dc0: r0 = Padding()
    //     0xbc2dc0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc2dc4: mov             x1, x0
    // 0xbc2dc8: r0 = Instance_EdgeInsets
    //     0xbc2dc8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xbc2dcc: ldr             x0, [x0, #0xdb0]
    // 0xbc2dd0: stur            x1, [fp, #-0x20]
    // 0xbc2dd4: StoreField: r1->field_f = r0
    //     0xbc2dd4: stur            w0, [x1, #0xf]
    // 0xbc2dd8: ldur            x0, [fp, #-0x38]
    // 0xbc2ddc: StoreField: r1->field_b = r0
    //     0xbc2ddc: stur            w0, [x1, #0xb]
    // 0xbc2de0: r0 = Container()
    //     0xbc2de0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbc2de4: stur            x0, [fp, #-0x30]
    // 0xbc2de8: r16 = Instance_BoxDecoration
    //     0xbc2de8: add             x16, PP, #0x56, lsl #12  ; [pp+0x56998] Obj!BoxDecoration@d64951
    //     0xbc2dec: ldr             x16, [x16, #0x998]
    // 0xbc2df0: ldur            lr, [fp, #-0x20]
    // 0xbc2df4: stp             lr, x16, [SP]
    // 0xbc2df8: mov             x1, x0
    // 0xbc2dfc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbc2dfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbc2e00: ldr             x4, [x4, #0x88]
    // 0xbc2e04: r0 = Container()
    //     0xbc2e04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbc2e08: r1 = Null
    //     0xbc2e08: mov             x1, NULL
    // 0xbc2e0c: r2 = 6
    //     0xbc2e0c: movz            x2, #0x6
    // 0xbc2e10: r0 = AllocateArray()
    //     0xbc2e10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc2e14: mov             x2, x0
    // 0xbc2e18: ldur            x0, [fp, #-0x28]
    // 0xbc2e1c: stur            x2, [fp, #-0x20]
    // 0xbc2e20: StoreField: r2->field_f = r0
    //     0xbc2e20: stur            w0, [x2, #0xf]
    // 0xbc2e24: r16 = Instance_SizedBox
    //     0xbc2e24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbc2e28: ldr             x16, [x16, #0xb20]
    // 0xbc2e2c: StoreField: r2->field_13 = r16
    //     0xbc2e2c: stur            w16, [x2, #0x13]
    // 0xbc2e30: ldur            x0, [fp, #-0x30]
    // 0xbc2e34: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc2e34: stur            w0, [x2, #0x17]
    // 0xbc2e38: r1 = <Widget>
    //     0xbc2e38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc2e3c: r0 = AllocateGrowableArray()
    //     0xbc2e3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc2e40: mov             x1, x0
    // 0xbc2e44: ldur            x0, [fp, #-0x20]
    // 0xbc2e48: stur            x1, [fp, #-0x28]
    // 0xbc2e4c: StoreField: r1->field_f = r0
    //     0xbc2e4c: stur            w0, [x1, #0xf]
    // 0xbc2e50: r0 = 6
    //     0xbc2e50: movz            x0, #0x6
    // 0xbc2e54: StoreField: r1->field_b = r0
    //     0xbc2e54: stur            w0, [x1, #0xb]
    // 0xbc2e58: r0 = Row()
    //     0xbc2e58: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc2e5c: mov             x2, x0
    // 0xbc2e60: r0 = Instance_Axis
    //     0xbc2e60: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc2e64: stur            x2, [fp, #-0x30]
    // 0xbc2e68: StoreField: r2->field_f = r0
    //     0xbc2e68: stur            w0, [x2, #0xf]
    // 0xbc2e6c: r3 = Instance_MainAxisAlignment
    //     0xbc2e6c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc2e70: ldr             x3, [x3, #0xa08]
    // 0xbc2e74: StoreField: r2->field_13 = r3
    //     0xbc2e74: stur            w3, [x2, #0x13]
    // 0xbc2e78: r4 = Instance_MainAxisSize
    //     0xbc2e78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc2e7c: ldr             x4, [x4, #0xa10]
    // 0xbc2e80: ArrayStore: r2[0] = r4  ; List_4
    //     0xbc2e80: stur            w4, [x2, #0x17]
    // 0xbc2e84: r5 = Instance_CrossAxisAlignment
    //     0xbc2e84: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc2e88: ldr             x5, [x5, #0xa18]
    // 0xbc2e8c: StoreField: r2->field_1b = r5
    //     0xbc2e8c: stur            w5, [x2, #0x1b]
    // 0xbc2e90: r6 = Instance_VerticalDirection
    //     0xbc2e90: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc2e94: ldr             x6, [x6, #0xa20]
    // 0xbc2e98: StoreField: r2->field_23 = r6
    //     0xbc2e98: stur            w6, [x2, #0x23]
    // 0xbc2e9c: r7 = Instance_Clip
    //     0xbc2e9c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc2ea0: ldr             x7, [x7, #0x38]
    // 0xbc2ea4: StoreField: r2->field_2b = r7
    //     0xbc2ea4: stur            w7, [x2, #0x2b]
    // 0xbc2ea8: StoreField: r2->field_2f = rZR
    //     0xbc2ea8: stur            xzr, [x2, #0x2f]
    // 0xbc2eac: ldur            x1, [fp, #-0x28]
    // 0xbc2eb0: StoreField: r2->field_b = r1
    //     0xbc2eb0: stur            w1, [x2, #0xb]
    // 0xbc2eb4: ldur            x8, [fp, #-8]
    // 0xbc2eb8: LoadField: r1 = r8->field_b
    //     0xbc2eb8: ldur            w1, [x8, #0xb]
    // 0xbc2ebc: DecompressPointer r1
    //     0xbc2ebc: add             x1, x1, HEAP, lsl #32
    // 0xbc2ec0: cmp             w1, NULL
    // 0xbc2ec4: b.eq            #0xbc3a00
    // 0xbc2ec8: LoadField: r9 = r1->field_b
    //     0xbc2ec8: ldur            w9, [x1, #0xb]
    // 0xbc2ecc: DecompressPointer r9
    //     0xbc2ecc: add             x9, x9, HEAP, lsl #32
    // 0xbc2ed0: LoadField: r1 = r9->field_f
    //     0xbc2ed0: ldur            w1, [x9, #0xf]
    // 0xbc2ed4: DecompressPointer r1
    //     0xbc2ed4: add             x1, x1, HEAP, lsl #32
    // 0xbc2ed8: cmp             w1, NULL
    // 0xbc2edc: b.ne            #0xbc2ee8
    // 0xbc2ee0: r9 = ""
    //     0xbc2ee0: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc2ee4: b               #0xbc2eec
    // 0xbc2ee8: mov             x9, x1
    // 0xbc2eec: ldur            x1, [fp, #-0x10]
    // 0xbc2ef0: stur            x9, [fp, #-0x20]
    // 0xbc2ef4: r0 = of()
    //     0xbc2ef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2ef8: LoadField: r1 = r0->field_87
    //     0xbc2ef8: ldur            w1, [x0, #0x87]
    // 0xbc2efc: DecompressPointer r1
    //     0xbc2efc: add             x1, x1, HEAP, lsl #32
    // 0xbc2f00: LoadField: r0 = r1->field_2b
    //     0xbc2f00: ldur            w0, [x1, #0x2b]
    // 0xbc2f04: DecompressPointer r0
    //     0xbc2f04: add             x0, x0, HEAP, lsl #32
    // 0xbc2f08: stur            x0, [fp, #-0x28]
    // 0xbc2f0c: r1 = Instance_Color
    //     0xbc2f0c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc2f10: d0 = 0.400000
    //     0xbc2f10: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbc2f14: r0 = withOpacity()
    //     0xbc2f14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc2f18: r16 = 13.000000
    //     0xbc2f18: add             x16, PP, #0x4b, lsl #12  ; [pp+0x4bfa0] 13
    //     0xbc2f1c: ldr             x16, [x16, #0xfa0]
    // 0xbc2f20: stp             x0, x16, [SP]
    // 0xbc2f24: ldur            x1, [fp, #-0x28]
    // 0xbc2f28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc2f28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc2f2c: ldr             x4, [x4, #0xaa0]
    // 0xbc2f30: r0 = copyWith()
    //     0xbc2f30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2f34: stur            x0, [fp, #-0x28]
    // 0xbc2f38: r0 = Text()
    //     0xbc2f38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2f3c: mov             x2, x0
    // 0xbc2f40: ldur            x0, [fp, #-0x20]
    // 0xbc2f44: stur            x2, [fp, #-0x38]
    // 0xbc2f48: StoreField: r2->field_b = r0
    //     0xbc2f48: stur            w0, [x2, #0xb]
    // 0xbc2f4c: ldur            x0, [fp, #-0x28]
    // 0xbc2f50: StoreField: r2->field_13 = r0
    //     0xbc2f50: stur            w0, [x2, #0x13]
    // 0xbc2f54: ldur            x0, [fp, #-8]
    // 0xbc2f58: LoadField: r1 = r0->field_b
    //     0xbc2f58: ldur            w1, [x0, #0xb]
    // 0xbc2f5c: DecompressPointer r1
    //     0xbc2f5c: add             x1, x1, HEAP, lsl #32
    // 0xbc2f60: cmp             w1, NULL
    // 0xbc2f64: b.eq            #0xbc3a04
    // 0xbc2f68: LoadField: r3 = r1->field_b
    //     0xbc2f68: ldur            w3, [x1, #0xb]
    // 0xbc2f6c: DecompressPointer r3
    //     0xbc2f6c: add             x3, x3, HEAP, lsl #32
    // 0xbc2f70: LoadField: r1 = r3->field_13
    //     0xbc2f70: ldur            w1, [x3, #0x13]
    // 0xbc2f74: DecompressPointer r1
    //     0xbc2f74: add             x1, x1, HEAP, lsl #32
    // 0xbc2f78: cmp             w1, NULL
    // 0xbc2f7c: b.ne            #0xbc2f88
    // 0xbc2f80: r1 = Null
    //     0xbc2f80: mov             x1, NULL
    // 0xbc2f84: b               #0xbc2f94
    // 0xbc2f88: LoadField: r3 = r1->field_7
    //     0xbc2f88: ldur            w3, [x1, #7]
    // 0xbc2f8c: DecompressPointer r3
    //     0xbc2f8c: add             x3, x3, HEAP, lsl #32
    // 0xbc2f90: mov             x1, x3
    // 0xbc2f94: cmp             w1, NULL
    // 0xbc2f98: b.ne            #0xbc2fa4
    // 0xbc2f9c: r3 = ""
    //     0xbc2f9c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbc2fa0: b               #0xbc2fa8
    // 0xbc2fa4: mov             x3, x1
    // 0xbc2fa8: ldur            x1, [fp, #-0x10]
    // 0xbc2fac: stur            x3, [fp, #-0x20]
    // 0xbc2fb0: r0 = of()
    //     0xbc2fb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc2fb4: LoadField: r1 = r0->field_87
    //     0xbc2fb4: ldur            w1, [x0, #0x87]
    // 0xbc2fb8: DecompressPointer r1
    //     0xbc2fb8: add             x1, x1, HEAP, lsl #32
    // 0xbc2fbc: LoadField: r0 = r1->field_7
    //     0xbc2fbc: ldur            w0, [x1, #7]
    // 0xbc2fc0: DecompressPointer r0
    //     0xbc2fc0: add             x0, x0, HEAP, lsl #32
    // 0xbc2fc4: r16 = Instance_Color
    //     0xbc2fc4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc2fc8: r30 = 14.000000
    //     0xbc2fc8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc2fcc: ldr             lr, [lr, #0x1d8]
    // 0xbc2fd0: stp             lr, x16, [SP]
    // 0xbc2fd4: mov             x1, x0
    // 0xbc2fd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc2fd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc2fdc: ldr             x4, [x4, #0x9b8]
    // 0xbc2fe0: r0 = copyWith()
    //     0xbc2fe0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc2fe4: stur            x0, [fp, #-0x28]
    // 0xbc2fe8: r0 = Text()
    //     0xbc2fe8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc2fec: mov             x2, x0
    // 0xbc2ff0: ldur            x0, [fp, #-0x20]
    // 0xbc2ff4: stur            x2, [fp, #-0x40]
    // 0xbc2ff8: StoreField: r2->field_b = r0
    //     0xbc2ff8: stur            w0, [x2, #0xb]
    // 0xbc2ffc: ldur            x0, [fp, #-0x28]
    // 0xbc3000: StoreField: r2->field_13 = r0
    //     0xbc3000: stur            w0, [x2, #0x13]
    // 0xbc3004: ldur            x1, [fp, #-0x10]
    // 0xbc3008: r0 = of()
    //     0xbc3008: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc300c: LoadField: r1 = r0->field_87
    //     0xbc300c: ldur            w1, [x0, #0x87]
    // 0xbc3010: DecompressPointer r1
    //     0xbc3010: add             x1, x1, HEAP, lsl #32
    // 0xbc3014: LoadField: r0 = r1->field_2b
    //     0xbc3014: ldur            w0, [x1, #0x2b]
    // 0xbc3018: DecompressPointer r0
    //     0xbc3018: add             x0, x0, HEAP, lsl #32
    // 0xbc301c: stur            x0, [fp, #-0x20]
    // 0xbc3020: r1 = Instance_Color
    //     0xbc3020: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc3024: d0 = 0.400000
    //     0xbc3024: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbc3028: r0 = withOpacity()
    //     0xbc3028: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc302c: r16 = 14.000000
    //     0xbc302c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc3030: ldr             x16, [x16, #0x1d8]
    // 0xbc3034: stp             x16, x0, [SP]
    // 0xbc3038: ldur            x1, [fp, #-0x20]
    // 0xbc303c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc303c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc3040: ldr             x4, [x4, #0x9b8]
    // 0xbc3044: r0 = copyWith()
    //     0xbc3044: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc3048: stur            x0, [fp, #-0x20]
    // 0xbc304c: r0 = Text()
    //     0xbc304c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc3050: mov             x1, x0
    // 0xbc3054: r0 = "Final Price"
    //     0xbc3054: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ab8] "Final Price"
    //     0xbc3058: ldr             x0, [x0, #0xab8]
    // 0xbc305c: stur            x1, [fp, #-0x28]
    // 0xbc3060: StoreField: r1->field_b = r0
    //     0xbc3060: stur            w0, [x1, #0xb]
    // 0xbc3064: ldur            x0, [fp, #-0x20]
    // 0xbc3068: StoreField: r1->field_13 = r0
    //     0xbc3068: stur            w0, [x1, #0x13]
    // 0xbc306c: ldur            x0, [fp, #-8]
    // 0xbc3070: LoadField: r2 = r0->field_b
    //     0xbc3070: ldur            w2, [x0, #0xb]
    // 0xbc3074: DecompressPointer r2
    //     0xbc3074: add             x2, x2, HEAP, lsl #32
    // 0xbc3078: cmp             w2, NULL
    // 0xbc307c: b.eq            #0xbc3a08
    // 0xbc3080: LoadField: r3 = r2->field_b
    //     0xbc3080: ldur            w3, [x2, #0xb]
    // 0xbc3084: DecompressPointer r3
    //     0xbc3084: add             x3, x3, HEAP, lsl #32
    // 0xbc3088: LoadField: r2 = r3->field_13
    //     0xbc3088: ldur            w2, [x3, #0x13]
    // 0xbc308c: DecompressPointer r2
    //     0xbc308c: add             x2, x2, HEAP, lsl #32
    // 0xbc3090: cmp             w2, NULL
    // 0xbc3094: b.ne            #0xbc30a0
    // 0xbc3098: r2 = Null
    //     0xbc3098: mov             x2, NULL
    // 0xbc309c: b               #0xbc30ac
    // 0xbc30a0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbc30a0: ldur            w3, [x2, #0x17]
    // 0xbc30a4: DecompressPointer r3
    //     0xbc30a4: add             x3, x3, HEAP, lsl #32
    // 0xbc30a8: mov             x2, x3
    // 0xbc30ac: str             x2, [SP]
    // 0xbc30b0: r0 = _interpolateSingle()
    //     0xbc30b0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbc30b4: ldur            x1, [fp, #-0x10]
    // 0xbc30b8: stur            x0, [fp, #-0x20]
    // 0xbc30bc: r0 = of()
    //     0xbc30bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc30c0: LoadField: r1 = r0->field_87
    //     0xbc30c0: ldur            w1, [x0, #0x87]
    // 0xbc30c4: DecompressPointer r1
    //     0xbc30c4: add             x1, x1, HEAP, lsl #32
    // 0xbc30c8: LoadField: r0 = r1->field_2b
    //     0xbc30c8: ldur            w0, [x1, #0x2b]
    // 0xbc30cc: DecompressPointer r0
    //     0xbc30cc: add             x0, x0, HEAP, lsl #32
    // 0xbc30d0: stur            x0, [fp, #-0x48]
    // 0xbc30d4: r1 = Instance_Color
    //     0xbc30d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc30d8: d0 = 0.400000
    //     0xbc30d8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbc30dc: r0 = withOpacity()
    //     0xbc30dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc30e0: r16 = 12.000000
    //     0xbc30e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc30e4: ldr             x16, [x16, #0x9e8]
    // 0xbc30e8: stp             x16, x0, [SP]
    // 0xbc30ec: ldur            x1, [fp, #-0x48]
    // 0xbc30f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc30f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc30f4: ldr             x4, [x4, #0x9b8]
    // 0xbc30f8: r0 = copyWith()
    //     0xbc30f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc30fc: stur            x0, [fp, #-0x48]
    // 0xbc3100: r0 = Text()
    //     0xbc3100: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc3104: mov             x3, x0
    // 0xbc3108: ldur            x0, [fp, #-0x20]
    // 0xbc310c: stur            x3, [fp, #-0x50]
    // 0xbc3110: StoreField: r3->field_b = r0
    //     0xbc3110: stur            w0, [x3, #0xb]
    // 0xbc3114: ldur            x0, [fp, #-0x48]
    // 0xbc3118: StoreField: r3->field_13 = r0
    //     0xbc3118: stur            w0, [x3, #0x13]
    // 0xbc311c: r1 = Null
    //     0xbc311c: mov             x1, NULL
    // 0xbc3120: r2 = 4
    //     0xbc3120: movz            x2, #0x4
    // 0xbc3124: r0 = AllocateArray()
    //     0xbc3124: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3128: mov             x2, x0
    // 0xbc312c: ldur            x0, [fp, #-0x28]
    // 0xbc3130: stur            x2, [fp, #-0x20]
    // 0xbc3134: StoreField: r2->field_f = r0
    //     0xbc3134: stur            w0, [x2, #0xf]
    // 0xbc3138: ldur            x0, [fp, #-0x50]
    // 0xbc313c: StoreField: r2->field_13 = r0
    //     0xbc313c: stur            w0, [x2, #0x13]
    // 0xbc3140: r1 = <Widget>
    //     0xbc3140: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc3144: r0 = AllocateGrowableArray()
    //     0xbc3144: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3148: mov             x1, x0
    // 0xbc314c: ldur            x0, [fp, #-0x20]
    // 0xbc3150: stur            x1, [fp, #-0x28]
    // 0xbc3154: StoreField: r1->field_f = r0
    //     0xbc3154: stur            w0, [x1, #0xf]
    // 0xbc3158: r2 = 4
    //     0xbc3158: movz            x2, #0x4
    // 0xbc315c: StoreField: r1->field_b = r2
    //     0xbc315c: stur            w2, [x1, #0xb]
    // 0xbc3160: r0 = Row()
    //     0xbc3160: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc3164: mov             x2, x0
    // 0xbc3168: r0 = Instance_Axis
    //     0xbc3168: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc316c: stur            x2, [fp, #-0x20]
    // 0xbc3170: StoreField: r2->field_f = r0
    //     0xbc3170: stur            w0, [x2, #0xf]
    // 0xbc3174: r3 = Instance_MainAxisAlignment
    //     0xbc3174: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbc3178: ldr             x3, [x3, #0xa8]
    // 0xbc317c: StoreField: r2->field_13 = r3
    //     0xbc317c: stur            w3, [x2, #0x13]
    // 0xbc3180: r4 = Instance_MainAxisSize
    //     0xbc3180: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc3184: ldr             x4, [x4, #0xa10]
    // 0xbc3188: ArrayStore: r2[0] = r4  ; List_4
    //     0xbc3188: stur            w4, [x2, #0x17]
    // 0xbc318c: r5 = Instance_CrossAxisAlignment
    //     0xbc318c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc3190: ldr             x5, [x5, #0xa18]
    // 0xbc3194: StoreField: r2->field_1b = r5
    //     0xbc3194: stur            w5, [x2, #0x1b]
    // 0xbc3198: r6 = Instance_VerticalDirection
    //     0xbc3198: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc319c: ldr             x6, [x6, #0xa20]
    // 0xbc31a0: StoreField: r2->field_23 = r6
    //     0xbc31a0: stur            w6, [x2, #0x23]
    // 0xbc31a4: r7 = Instance_Clip
    //     0xbc31a4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc31a8: ldr             x7, [x7, #0x38]
    // 0xbc31ac: StoreField: r2->field_2b = r7
    //     0xbc31ac: stur            w7, [x2, #0x2b]
    // 0xbc31b0: StoreField: r2->field_2f = rZR
    //     0xbc31b0: stur            xzr, [x2, #0x2f]
    // 0xbc31b4: ldur            x1, [fp, #-0x28]
    // 0xbc31b8: StoreField: r2->field_b = r1
    //     0xbc31b8: stur            w1, [x2, #0xb]
    // 0xbc31bc: ldur            x1, [fp, #-0x10]
    // 0xbc31c0: r0 = of()
    //     0xbc31c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc31c4: LoadField: r1 = r0->field_87
    //     0xbc31c4: ldur            w1, [x0, #0x87]
    // 0xbc31c8: DecompressPointer r1
    //     0xbc31c8: add             x1, x1, HEAP, lsl #32
    // 0xbc31cc: LoadField: r0 = r1->field_2b
    //     0xbc31cc: ldur            w0, [x1, #0x2b]
    // 0xbc31d0: DecompressPointer r0
    //     0xbc31d0: add             x0, x0, HEAP, lsl #32
    // 0xbc31d4: stur            x0, [fp, #-0x28]
    // 0xbc31d8: r1 = Instance_Color
    //     0xbc31d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc31dc: d0 = 0.400000
    //     0xbc31dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbc31e0: r0 = withOpacity()
    //     0xbc31e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc31e4: r16 = 14.000000
    //     0xbc31e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc31e8: ldr             x16, [x16, #0x1d8]
    // 0xbc31ec: stp             x16, x0, [SP]
    // 0xbc31f0: ldur            x1, [fp, #-0x28]
    // 0xbc31f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc31f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc31f8: ldr             x4, [x4, #0x9b8]
    // 0xbc31fc: r0 = copyWith()
    //     0xbc31fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc3200: stur            x0, [fp, #-0x28]
    // 0xbc3204: r0 = Text()
    //     0xbc3204: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc3208: mov             x3, x0
    // 0xbc320c: r0 = "Online Advance"
    //     0xbc320c: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ac0] "Online Advance"
    //     0xbc3210: ldr             x0, [x0, #0xac0]
    // 0xbc3214: stur            x3, [fp, #-0x48]
    // 0xbc3218: StoreField: r3->field_b = r0
    //     0xbc3218: stur            w0, [x3, #0xb]
    // 0xbc321c: ldur            x0, [fp, #-0x28]
    // 0xbc3220: StoreField: r3->field_13 = r0
    //     0xbc3220: stur            w0, [x3, #0x13]
    // 0xbc3224: ldur            x0, [fp, #-8]
    // 0xbc3228: LoadField: r1 = r0->field_b
    //     0xbc3228: ldur            w1, [x0, #0xb]
    // 0xbc322c: DecompressPointer r1
    //     0xbc322c: add             x1, x1, HEAP, lsl #32
    // 0xbc3230: cmp             w1, NULL
    // 0xbc3234: b.eq            #0xbc3a0c
    // 0xbc3238: LoadField: r2 = r1->field_b
    //     0xbc3238: ldur            w2, [x1, #0xb]
    // 0xbc323c: DecompressPointer r2
    //     0xbc323c: add             x2, x2, HEAP, lsl #32
    // 0xbc3240: LoadField: r1 = r2->field_13
    //     0xbc3240: ldur            w1, [x2, #0x13]
    // 0xbc3244: DecompressPointer r1
    //     0xbc3244: add             x1, x1, HEAP, lsl #32
    // 0xbc3248: cmp             w1, NULL
    // 0xbc324c: b.ne            #0xbc3258
    // 0xbc3250: r5 = Null
    //     0xbc3250: mov             x5, NULL
    // 0xbc3254: b               #0xbc3264
    // 0xbc3258: LoadField: r2 = r1->field_27
    //     0xbc3258: ldur            w2, [x1, #0x27]
    // 0xbc325c: DecompressPointer r2
    //     0xbc325c: add             x2, x2, HEAP, lsl #32
    // 0xbc3260: mov             x5, x2
    // 0xbc3264: r4 = 4
    //     0xbc3264: movz            x4, #0x4
    // 0xbc3268: mov             x2, x4
    // 0xbc326c: stur            x5, [fp, #-0x28]
    // 0xbc3270: r1 = Null
    //     0xbc3270: mov             x1, NULL
    // 0xbc3274: r0 = AllocateArray()
    //     0xbc3274: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3278: mov             x1, x0
    // 0xbc327c: ldur            x0, [fp, #-0x28]
    // 0xbc3280: StoreField: r1->field_f = r0
    //     0xbc3280: stur            w0, [x1, #0xf]
    // 0xbc3284: r16 = " "
    //     0xbc3284: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbc3288: StoreField: r1->field_13 = r16
    //     0xbc3288: stur            w16, [x1, #0x13]
    // 0xbc328c: str             x1, [SP]
    // 0xbc3290: r0 = _interpolate()
    //     0xbc3290: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbc3294: ldur            x1, [fp, #-0x10]
    // 0xbc3298: stur            x0, [fp, #-0x28]
    // 0xbc329c: r0 = of()
    //     0xbc329c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc32a0: LoadField: r1 = r0->field_87
    //     0xbc32a0: ldur            w1, [x0, #0x87]
    // 0xbc32a4: DecompressPointer r1
    //     0xbc32a4: add             x1, x1, HEAP, lsl #32
    // 0xbc32a8: LoadField: r0 = r1->field_2b
    //     0xbc32a8: ldur            w0, [x1, #0x2b]
    // 0xbc32ac: DecompressPointer r0
    //     0xbc32ac: add             x0, x0, HEAP, lsl #32
    // 0xbc32b0: stur            x0, [fp, #-0x50]
    // 0xbc32b4: r1 = Instance_Color
    //     0xbc32b4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc32b8: d0 = 0.400000
    //     0xbc32b8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbc32bc: r0 = withOpacity()
    //     0xbc32bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbc32c0: r16 = 12.000000
    //     0xbc32c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbc32c4: ldr             x16, [x16, #0x9e8]
    // 0xbc32c8: stp             x16, x0, [SP]
    // 0xbc32cc: ldur            x1, [fp, #-0x50]
    // 0xbc32d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc32d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc32d4: ldr             x4, [x4, #0x9b8]
    // 0xbc32d8: r0 = copyWith()
    //     0xbc32d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc32dc: stur            x0, [fp, #-0x50]
    // 0xbc32e0: r0 = Text()
    //     0xbc32e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc32e4: mov             x3, x0
    // 0xbc32e8: ldur            x0, [fp, #-0x28]
    // 0xbc32ec: stur            x3, [fp, #-0x58]
    // 0xbc32f0: StoreField: r3->field_b = r0
    //     0xbc32f0: stur            w0, [x3, #0xb]
    // 0xbc32f4: ldur            x0, [fp, #-0x50]
    // 0xbc32f8: StoreField: r3->field_13 = r0
    //     0xbc32f8: stur            w0, [x3, #0x13]
    // 0xbc32fc: r1 = Null
    //     0xbc32fc: mov             x1, NULL
    // 0xbc3300: r2 = 4
    //     0xbc3300: movz            x2, #0x4
    // 0xbc3304: r0 = AllocateArray()
    //     0xbc3304: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3308: mov             x2, x0
    // 0xbc330c: ldur            x0, [fp, #-0x48]
    // 0xbc3310: stur            x2, [fp, #-0x28]
    // 0xbc3314: StoreField: r2->field_f = r0
    //     0xbc3314: stur            w0, [x2, #0xf]
    // 0xbc3318: ldur            x0, [fp, #-0x58]
    // 0xbc331c: StoreField: r2->field_13 = r0
    //     0xbc331c: stur            w0, [x2, #0x13]
    // 0xbc3320: r1 = <Widget>
    //     0xbc3320: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc3324: r0 = AllocateGrowableArray()
    //     0xbc3324: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3328: mov             x1, x0
    // 0xbc332c: ldur            x0, [fp, #-0x28]
    // 0xbc3330: stur            x1, [fp, #-0x48]
    // 0xbc3334: StoreField: r1->field_f = r0
    //     0xbc3334: stur            w0, [x1, #0xf]
    // 0xbc3338: r2 = 4
    //     0xbc3338: movz            x2, #0x4
    // 0xbc333c: StoreField: r1->field_b = r2
    //     0xbc333c: stur            w2, [x1, #0xb]
    // 0xbc3340: r0 = Row()
    //     0xbc3340: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc3344: mov             x2, x0
    // 0xbc3348: r0 = Instance_Axis
    //     0xbc3348: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc334c: stur            x2, [fp, #-0x28]
    // 0xbc3350: StoreField: r2->field_f = r0
    //     0xbc3350: stur            w0, [x2, #0xf]
    // 0xbc3354: r3 = Instance_MainAxisAlignment
    //     0xbc3354: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbc3358: ldr             x3, [x3, #0xa8]
    // 0xbc335c: StoreField: r2->field_13 = r3
    //     0xbc335c: stur            w3, [x2, #0x13]
    // 0xbc3360: r4 = Instance_MainAxisSize
    //     0xbc3360: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc3364: ldr             x4, [x4, #0xa10]
    // 0xbc3368: ArrayStore: r2[0] = r4  ; List_4
    //     0xbc3368: stur            w4, [x2, #0x17]
    // 0xbc336c: r5 = Instance_CrossAxisAlignment
    //     0xbc336c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc3370: ldr             x5, [x5, #0xa18]
    // 0xbc3374: StoreField: r2->field_1b = r5
    //     0xbc3374: stur            w5, [x2, #0x1b]
    // 0xbc3378: r6 = Instance_VerticalDirection
    //     0xbc3378: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc337c: ldr             x6, [x6, #0xa20]
    // 0xbc3380: StoreField: r2->field_23 = r6
    //     0xbc3380: stur            w6, [x2, #0x23]
    // 0xbc3384: r7 = Instance_Clip
    //     0xbc3384: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc3388: ldr             x7, [x7, #0x38]
    // 0xbc338c: StoreField: r2->field_2b = r7
    //     0xbc338c: stur            w7, [x2, #0x2b]
    // 0xbc3390: StoreField: r2->field_2f = rZR
    //     0xbc3390: stur            xzr, [x2, #0x2f]
    // 0xbc3394: ldur            x1, [fp, #-0x48]
    // 0xbc3398: StoreField: r2->field_b = r1
    //     0xbc3398: stur            w1, [x2, #0xb]
    // 0xbc339c: ldur            x1, [fp, #-0x10]
    // 0xbc33a0: r0 = of()
    //     0xbc33a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc33a4: LoadField: r1 = r0->field_87
    //     0xbc33a4: ldur            w1, [x0, #0x87]
    // 0xbc33a8: DecompressPointer r1
    //     0xbc33a8: add             x1, x1, HEAP, lsl #32
    // 0xbc33ac: LoadField: r0 = r1->field_7
    //     0xbc33ac: ldur            w0, [x1, #7]
    // 0xbc33b0: DecompressPointer r0
    //     0xbc33b0: add             x0, x0, HEAP, lsl #32
    // 0xbc33b4: r16 = Instance_Color
    //     0xbc33b4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc33b8: r30 = 14.000000
    //     0xbc33b8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc33bc: ldr             lr, [lr, #0x1d8]
    // 0xbc33c0: stp             lr, x16, [SP]
    // 0xbc33c4: mov             x1, x0
    // 0xbc33c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc33c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc33cc: ldr             x4, [x4, #0x9b8]
    // 0xbc33d0: r0 = copyWith()
    //     0xbc33d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc33d4: stur            x0, [fp, #-0x48]
    // 0xbc33d8: r0 = Text()
    //     0xbc33d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc33dc: mov             x3, x0
    // 0xbc33e0: r0 = "Cash to be paid on Delivery"
    //     0xbc33e0: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ac8] "Cash to be paid on Delivery"
    //     0xbc33e4: ldr             x0, [x0, #0xac8]
    // 0xbc33e8: stur            x3, [fp, #-0x50]
    // 0xbc33ec: StoreField: r3->field_b = r0
    //     0xbc33ec: stur            w0, [x3, #0xb]
    // 0xbc33f0: ldur            x0, [fp, #-0x48]
    // 0xbc33f4: StoreField: r3->field_13 = r0
    //     0xbc33f4: stur            w0, [x3, #0x13]
    // 0xbc33f8: ldur            x0, [fp, #-8]
    // 0xbc33fc: LoadField: r1 = r0->field_b
    //     0xbc33fc: ldur            w1, [x0, #0xb]
    // 0xbc3400: DecompressPointer r1
    //     0xbc3400: add             x1, x1, HEAP, lsl #32
    // 0xbc3404: cmp             w1, NULL
    // 0xbc3408: b.eq            #0xbc3a10
    // 0xbc340c: LoadField: r0 = r1->field_b
    //     0xbc340c: ldur            w0, [x1, #0xb]
    // 0xbc3410: DecompressPointer r0
    //     0xbc3410: add             x0, x0, HEAP, lsl #32
    // 0xbc3414: LoadField: r1 = r0->field_13
    //     0xbc3414: ldur            w1, [x0, #0x13]
    // 0xbc3418: DecompressPointer r1
    //     0xbc3418: add             x1, x1, HEAP, lsl #32
    // 0xbc341c: cmp             w1, NULL
    // 0xbc3420: b.ne            #0xbc342c
    // 0xbc3424: r9 = Null
    //     0xbc3424: mov             x9, NULL
    // 0xbc3428: b               #0xbc3438
    // 0xbc342c: LoadField: r0 = r1->field_2f
    //     0xbc342c: ldur            w0, [x1, #0x2f]
    // 0xbc3430: DecompressPointer r0
    //     0xbc3430: add             x0, x0, HEAP, lsl #32
    // 0xbc3434: mov             x9, x0
    // 0xbc3438: ldur            x8, [fp, #-0x30]
    // 0xbc343c: ldur            x7, [fp, #-0x38]
    // 0xbc3440: ldur            x6, [fp, #-0x40]
    // 0xbc3444: ldur            x5, [fp, #-0x20]
    // 0xbc3448: ldur            x0, [fp, #-0x28]
    // 0xbc344c: r4 = 4
    //     0xbc344c: movz            x4, #0x4
    // 0xbc3450: mov             x2, x4
    // 0xbc3454: stur            x9, [fp, #-8]
    // 0xbc3458: r1 = Null
    //     0xbc3458: mov             x1, NULL
    // 0xbc345c: r0 = AllocateArray()
    //     0xbc345c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3460: mov             x1, x0
    // 0xbc3464: ldur            x0, [fp, #-8]
    // 0xbc3468: StoreField: r1->field_f = r0
    //     0xbc3468: stur            w0, [x1, #0xf]
    // 0xbc346c: r16 = " "
    //     0xbc346c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbc3470: StoreField: r1->field_13 = r16
    //     0xbc3470: stur            w16, [x1, #0x13]
    // 0xbc3474: str             x1, [SP]
    // 0xbc3478: r0 = _interpolate()
    //     0xbc3478: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbc347c: ldur            x1, [fp, #-0x10]
    // 0xbc3480: stur            x0, [fp, #-8]
    // 0xbc3484: r0 = of()
    //     0xbc3484: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc3488: LoadField: r1 = r0->field_87
    //     0xbc3488: ldur            w1, [x0, #0x87]
    // 0xbc348c: DecompressPointer r1
    //     0xbc348c: add             x1, x1, HEAP, lsl #32
    // 0xbc3490: LoadField: r0 = r1->field_7
    //     0xbc3490: ldur            w0, [x1, #7]
    // 0xbc3494: DecompressPointer r0
    //     0xbc3494: add             x0, x0, HEAP, lsl #32
    // 0xbc3498: r16 = Instance_Color
    //     0xbc3498: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbc349c: r30 = 14.000000
    //     0xbc349c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc34a0: ldr             lr, [lr, #0x1d8]
    // 0xbc34a4: stp             lr, x16, [SP]
    // 0xbc34a8: mov             x1, x0
    // 0xbc34ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbc34ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbc34b0: ldr             x4, [x4, #0x9b8]
    // 0xbc34b4: r0 = copyWith()
    //     0xbc34b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc34b8: stur            x0, [fp, #-0x48]
    // 0xbc34bc: r0 = Text()
    //     0xbc34bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc34c0: mov             x3, x0
    // 0xbc34c4: ldur            x0, [fp, #-8]
    // 0xbc34c8: stur            x3, [fp, #-0x58]
    // 0xbc34cc: StoreField: r3->field_b = r0
    //     0xbc34cc: stur            w0, [x3, #0xb]
    // 0xbc34d0: ldur            x0, [fp, #-0x48]
    // 0xbc34d4: StoreField: r3->field_13 = r0
    //     0xbc34d4: stur            w0, [x3, #0x13]
    // 0xbc34d8: r1 = Null
    //     0xbc34d8: mov             x1, NULL
    // 0xbc34dc: r2 = 4
    //     0xbc34dc: movz            x2, #0x4
    // 0xbc34e0: r0 = AllocateArray()
    //     0xbc34e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc34e4: mov             x2, x0
    // 0xbc34e8: ldur            x0, [fp, #-0x50]
    // 0xbc34ec: stur            x2, [fp, #-8]
    // 0xbc34f0: StoreField: r2->field_f = r0
    //     0xbc34f0: stur            w0, [x2, #0xf]
    // 0xbc34f4: ldur            x0, [fp, #-0x58]
    // 0xbc34f8: StoreField: r2->field_13 = r0
    //     0xbc34f8: stur            w0, [x2, #0x13]
    // 0xbc34fc: r1 = <Widget>
    //     0xbc34fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc3500: r0 = AllocateGrowableArray()
    //     0xbc3500: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3504: mov             x1, x0
    // 0xbc3508: ldur            x0, [fp, #-8]
    // 0xbc350c: stur            x1, [fp, #-0x48]
    // 0xbc3510: StoreField: r1->field_f = r0
    //     0xbc3510: stur            w0, [x1, #0xf]
    // 0xbc3514: r2 = 4
    //     0xbc3514: movz            x2, #0x4
    // 0xbc3518: StoreField: r1->field_b = r2
    //     0xbc3518: stur            w2, [x1, #0xb]
    // 0xbc351c: r0 = Row()
    //     0xbc351c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbc3520: mov             x3, x0
    // 0xbc3524: r0 = Instance_Axis
    //     0xbc3524: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbc3528: stur            x3, [fp, #-8]
    // 0xbc352c: StoreField: r3->field_f = r0
    //     0xbc352c: stur            w0, [x3, #0xf]
    // 0xbc3530: r0 = Instance_MainAxisAlignment
    //     0xbc3530: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbc3534: ldr             x0, [x0, #0xa8]
    // 0xbc3538: StoreField: r3->field_13 = r0
    //     0xbc3538: stur            w0, [x3, #0x13]
    // 0xbc353c: r0 = Instance_MainAxisSize
    //     0xbc353c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc3540: ldr             x0, [x0, #0xa10]
    // 0xbc3544: ArrayStore: r3[0] = r0  ; List_4
    //     0xbc3544: stur            w0, [x3, #0x17]
    // 0xbc3548: r1 = Instance_CrossAxisAlignment
    //     0xbc3548: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbc354c: ldr             x1, [x1, #0xa18]
    // 0xbc3550: StoreField: r3->field_1b = r1
    //     0xbc3550: stur            w1, [x3, #0x1b]
    // 0xbc3554: r4 = Instance_VerticalDirection
    //     0xbc3554: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc3558: ldr             x4, [x4, #0xa20]
    // 0xbc355c: StoreField: r3->field_23 = r4
    //     0xbc355c: stur            w4, [x3, #0x23]
    // 0xbc3560: r5 = Instance_Clip
    //     0xbc3560: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc3564: ldr             x5, [x5, #0x38]
    // 0xbc3568: StoreField: r3->field_2b = r5
    //     0xbc3568: stur            w5, [x3, #0x2b]
    // 0xbc356c: StoreField: r3->field_2f = rZR
    //     0xbc356c: stur            xzr, [x3, #0x2f]
    // 0xbc3570: ldur            x1, [fp, #-0x48]
    // 0xbc3574: StoreField: r3->field_b = r1
    //     0xbc3574: stur            w1, [x3, #0xb]
    // 0xbc3578: r1 = Null
    //     0xbc3578: mov             x1, NULL
    // 0xbc357c: r2 = 14
    //     0xbc357c: movz            x2, #0xe
    // 0xbc3580: r0 = AllocateArray()
    //     0xbc3580: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3584: mov             x2, x0
    // 0xbc3588: ldur            x0, [fp, #-0x40]
    // 0xbc358c: stur            x2, [fp, #-0x48]
    // 0xbc3590: StoreField: r2->field_f = r0
    //     0xbc3590: stur            w0, [x2, #0xf]
    // 0xbc3594: r16 = Instance_SizedBox
    //     0xbc3594: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbc3598: ldr             x16, [x16, #0x8f0]
    // 0xbc359c: StoreField: r2->field_13 = r16
    //     0xbc359c: stur            w16, [x2, #0x13]
    // 0xbc35a0: ldur            x0, [fp, #-0x20]
    // 0xbc35a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc35a4: stur            w0, [x2, #0x17]
    // 0xbc35a8: r16 = Instance_SizedBox
    //     0xbc35a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xbc35ac: ldr             x16, [x16, #0x8b8]
    // 0xbc35b0: StoreField: r2->field_1b = r16
    //     0xbc35b0: stur            w16, [x2, #0x1b]
    // 0xbc35b4: ldur            x0, [fp, #-0x28]
    // 0xbc35b8: StoreField: r2->field_1f = r0
    //     0xbc35b8: stur            w0, [x2, #0x1f]
    // 0xbc35bc: r16 = Instance_Divider
    //     0xbc35bc: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0xbc35c0: ldr             x16, [x16, #0x2e0]
    // 0xbc35c4: StoreField: r2->field_23 = r16
    //     0xbc35c4: stur            w16, [x2, #0x23]
    // 0xbc35c8: ldur            x0, [fp, #-8]
    // 0xbc35cc: StoreField: r2->field_27 = r0
    //     0xbc35cc: stur            w0, [x2, #0x27]
    // 0xbc35d0: r1 = <Widget>
    //     0xbc35d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc35d4: r0 = AllocateGrowableArray()
    //     0xbc35d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc35d8: mov             x1, x0
    // 0xbc35dc: ldur            x0, [fp, #-0x48]
    // 0xbc35e0: stur            x1, [fp, #-8]
    // 0xbc35e4: StoreField: r1->field_f = r0
    //     0xbc35e4: stur            w0, [x1, #0xf]
    // 0xbc35e8: r0 = 14
    //     0xbc35e8: movz            x0, #0xe
    // 0xbc35ec: StoreField: r1->field_b = r0
    //     0xbc35ec: stur            w0, [x1, #0xb]
    // 0xbc35f0: r0 = Column()
    //     0xbc35f0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbc35f4: mov             x3, x0
    // 0xbc35f8: r0 = Instance_Axis
    //     0xbc35f8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbc35fc: stur            x3, [fp, #-0x20]
    // 0xbc3600: StoreField: r3->field_f = r0
    //     0xbc3600: stur            w0, [x3, #0xf]
    // 0xbc3604: r0 = Instance_MainAxisAlignment
    //     0xbc3604: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbc3608: ldr             x0, [x0, #0xa08]
    // 0xbc360c: StoreField: r3->field_13 = r0
    //     0xbc360c: stur            w0, [x3, #0x13]
    // 0xbc3610: r0 = Instance_MainAxisSize
    //     0xbc3610: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbc3614: ldr             x0, [x0, #0xa10]
    // 0xbc3618: ArrayStore: r3[0] = r0  ; List_4
    //     0xbc3618: stur            w0, [x3, #0x17]
    // 0xbc361c: r0 = Instance_CrossAxisAlignment
    //     0xbc361c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbc3620: ldr             x0, [x0, #0x890]
    // 0xbc3624: StoreField: r3->field_1b = r0
    //     0xbc3624: stur            w0, [x3, #0x1b]
    // 0xbc3628: r0 = Instance_VerticalDirection
    //     0xbc3628: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbc362c: ldr             x0, [x0, #0xa20]
    // 0xbc3630: StoreField: r3->field_23 = r0
    //     0xbc3630: stur            w0, [x3, #0x23]
    // 0xbc3634: r0 = Instance_Clip
    //     0xbc3634: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbc3638: ldr             x0, [x0, #0x38]
    // 0xbc363c: StoreField: r3->field_2b = r0
    //     0xbc363c: stur            w0, [x3, #0x2b]
    // 0xbc3640: StoreField: r3->field_2f = rZR
    //     0xbc3640: stur            xzr, [x3, #0x2f]
    // 0xbc3644: ldur            x0, [fp, #-8]
    // 0xbc3648: StoreField: r3->field_b = r0
    //     0xbc3648: stur            w0, [x3, #0xb]
    // 0xbc364c: r1 = Null
    //     0xbc364c: mov             x1, NULL
    // 0xbc3650: r2 = 10
    //     0xbc3650: movz            x2, #0xa
    // 0xbc3654: r0 = AllocateArray()
    //     0xbc3654: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3658: mov             x2, x0
    // 0xbc365c: ldur            x0, [fp, #-0x30]
    // 0xbc3660: stur            x2, [fp, #-8]
    // 0xbc3664: StoreField: r2->field_f = r0
    //     0xbc3664: stur            w0, [x2, #0xf]
    // 0xbc3668: r16 = Instance_SizedBox
    //     0xbc3668: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbc366c: ldr             x16, [x16, #0x578]
    // 0xbc3670: StoreField: r2->field_13 = r16
    //     0xbc3670: stur            w16, [x2, #0x13]
    // 0xbc3674: ldur            x0, [fp, #-0x38]
    // 0xbc3678: ArrayStore: r2[0] = r0  ; List_4
    //     0xbc3678: stur            w0, [x2, #0x17]
    // 0xbc367c: r16 = Instance_SizedBox
    //     0xbc367c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbc3680: ldr             x16, [x16, #0x578]
    // 0xbc3684: StoreField: r2->field_1b = r16
    //     0xbc3684: stur            w16, [x2, #0x1b]
    // 0xbc3688: ldur            x0, [fp, #-0x20]
    // 0xbc368c: StoreField: r2->field_1f = r0
    //     0xbc368c: stur            w0, [x2, #0x1f]
    // 0xbc3690: r1 = <Widget>
    //     0xbc3690: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc3694: r0 = AllocateGrowableArray()
    //     0xbc3694: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3698: mov             x1, x0
    // 0xbc369c: ldur            x0, [fp, #-8]
    // 0xbc36a0: stur            x1, [fp, #-0x20]
    // 0xbc36a4: StoreField: r1->field_f = r0
    //     0xbc36a4: stur            w0, [x1, #0xf]
    // 0xbc36a8: r0 = 10
    //     0xbc36a8: movz            x0, #0xa
    // 0xbc36ac: StoreField: r1->field_b = r0
    //     0xbc36ac: stur            w0, [x1, #0xb]
    // 0xbc36b0: r0 = ListView()
    //     0xbc36b0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbc36b4: stur            x0, [fp, #-8]
    // 0xbc36b8: r16 = Instance_BouncingScrollPhysics
    //     0xbc36b8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbc36bc: ldr             x16, [x16, #0x890]
    // 0xbc36c0: str             x16, [SP]
    // 0xbc36c4: mov             x1, x0
    // 0xbc36c8: ldur            x2, [fp, #-0x20]
    // 0xbc36cc: r4 = const [0, 0x3, 0x1, 0x2, physics, 0x2, null]
    //     0xbc36cc: add             x4, PP, #0x54, lsl #12  ; [pp+0x542e8] List(7) [0, 0x3, 0x1, 0x2, "physics", 0x2, Null]
    //     0xbc36d0: ldr             x4, [x4, #0x2e8]
    // 0xbc36d4: r0 = ListView()
    //     0xbc36d4: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xbc36d8: r0 = SizedBox()
    //     0xbc36d8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbc36dc: mov             x1, x0
    // 0xbc36e0: ldur            x0, [fp, #-8]
    // 0xbc36e4: stur            x1, [fp, #-0x20]
    // 0xbc36e8: StoreField: r1->field_b = r0
    //     0xbc36e8: stur            w0, [x1, #0xb]
    // 0xbc36ec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc36ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc36f0: ldr             x0, [x0, #0x1c80]
    //     0xbc36f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc36f8: cmp             w0, w16
    //     0xbc36fc: b.ne            #0xbc3708
    //     0xbc3700: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbc3704: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbc3708: r0 = GetNavigation.size()
    //     0xbc3708: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbc370c: LoadField: d0 = r0->field_7
    //     0xbc370c: ldur            d0, [x0, #7]
    // 0xbc3710: d1 = 0.940000
    //     0xbc3710: add             x17, PP, #0x54, lsl #12  ; [pp+0x542f0] IMM: double(0.94) from 0x3fee147ae147ae14
    //     0xbc3714: ldr             d1, [x17, #0x2f0]
    // 0xbc3718: fmul            d2, d0, d1
    // 0xbc371c: stur            d2, [fp, #-0x60]
    // 0xbc3720: r16 = <EdgeInsets>
    //     0xbc3720: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbc3724: ldr             x16, [x16, #0xda0]
    // 0xbc3728: r30 = Instance_EdgeInsets
    //     0xbc3728: add             lr, PP, #0x46, lsl #12  ; [pp+0x46f10] Obj!EdgeInsets@d58191
    //     0xbc372c: ldr             lr, [lr, #0xf10]
    // 0xbc3730: stp             lr, x16, [SP]
    // 0xbc3734: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbc3734: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbc3738: r0 = all()
    //     0xbc3738: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbc373c: ldur            x1, [fp, #-0x10]
    // 0xbc3740: stur            x0, [fp, #-8]
    // 0xbc3744: r0 = of()
    //     0xbc3744: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc3748: LoadField: r1 = r0->field_5b
    //     0xbc3748: ldur            w1, [x0, #0x5b]
    // 0xbc374c: DecompressPointer r1
    //     0xbc374c: add             x1, x1, HEAP, lsl #32
    // 0xbc3750: r16 = <Color>
    //     0xbc3750: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbc3754: ldr             x16, [x16, #0xf80]
    // 0xbc3758: stp             x1, x16, [SP]
    // 0xbc375c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbc375c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbc3760: r0 = all()
    //     0xbc3760: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbc3764: ldur            x1, [fp, #-0x10]
    // 0xbc3768: stur            x0, [fp, #-0x28]
    // 0xbc376c: r0 = of()
    //     0xbc376c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc3770: LoadField: r1 = r0->field_5b
    //     0xbc3770: ldur            w1, [x0, #0x5b]
    // 0xbc3774: DecompressPointer r1
    //     0xbc3774: add             x1, x1, HEAP, lsl #32
    // 0xbc3778: stur            x1, [fp, #-0x30]
    // 0xbc377c: r0 = BorderSide()
    //     0xbc377c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbc3780: mov             x1, x0
    // 0xbc3784: ldur            x0, [fp, #-0x30]
    // 0xbc3788: stur            x1, [fp, #-0x38]
    // 0xbc378c: StoreField: r1->field_7 = r0
    //     0xbc378c: stur            w0, [x1, #7]
    // 0xbc3790: d0 = 1.000000
    //     0xbc3790: fmov            d0, #1.00000000
    // 0xbc3794: StoreField: r1->field_b = d0
    //     0xbc3794: stur            d0, [x1, #0xb]
    // 0xbc3798: r0 = Instance_BorderStyle
    //     0xbc3798: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbc379c: ldr             x0, [x0, #0xf68]
    // 0xbc37a0: StoreField: r1->field_13 = r0
    //     0xbc37a0: stur            w0, [x1, #0x13]
    // 0xbc37a4: d0 = -1.000000
    //     0xbc37a4: fmov            d0, #-1.00000000
    // 0xbc37a8: ArrayStore: r1[0] = d0  ; List_8
    //     0xbc37a8: stur            d0, [x1, #0x17]
    // 0xbc37ac: r0 = RoundedRectangleBorder()
    //     0xbc37ac: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbc37b0: mov             x1, x0
    // 0xbc37b4: r0 = Instance_BorderRadius
    //     0xbc37b4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbc37b8: ldr             x0, [x0, #0xf70]
    // 0xbc37bc: StoreField: r1->field_b = r0
    //     0xbc37bc: stur            w0, [x1, #0xb]
    // 0xbc37c0: ldur            x0, [fp, #-0x38]
    // 0xbc37c4: StoreField: r1->field_7 = r0
    //     0xbc37c4: stur            w0, [x1, #7]
    // 0xbc37c8: r16 = <RoundedRectangleBorder>
    //     0xbc37c8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbc37cc: ldr             x16, [x16, #0xf78]
    // 0xbc37d0: stp             x1, x16, [SP]
    // 0xbc37d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbc37d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbc37d8: r0 = all()
    //     0xbc37d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbc37dc: stur            x0, [fp, #-0x30]
    // 0xbc37e0: r0 = ButtonStyle()
    //     0xbc37e0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbc37e4: mov             x1, x0
    // 0xbc37e8: ldur            x0, [fp, #-0x28]
    // 0xbc37ec: stur            x1, [fp, #-0x38]
    // 0xbc37f0: StoreField: r1->field_b = r0
    //     0xbc37f0: stur            w0, [x1, #0xb]
    // 0xbc37f4: ldur            x0, [fp, #-8]
    // 0xbc37f8: StoreField: r1->field_23 = r0
    //     0xbc37f8: stur            w0, [x1, #0x23]
    // 0xbc37fc: ldur            x0, [fp, #-0x30]
    // 0xbc3800: StoreField: r1->field_43 = r0
    //     0xbc3800: stur            w0, [x1, #0x43]
    // 0xbc3804: r0 = TextButtonThemeData()
    //     0xbc3804: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbc3808: mov             x2, x0
    // 0xbc380c: ldur            x0, [fp, #-0x38]
    // 0xbc3810: stur            x2, [fp, #-8]
    // 0xbc3814: StoreField: r2->field_7 = r0
    //     0xbc3814: stur            w0, [x2, #7]
    // 0xbc3818: ldur            x1, [fp, #-0x10]
    // 0xbc381c: r0 = of()
    //     0xbc381c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbc3820: LoadField: r1 = r0->field_87
    //     0xbc3820: ldur            w1, [x0, #0x87]
    // 0xbc3824: DecompressPointer r1
    //     0xbc3824: add             x1, x1, HEAP, lsl #32
    // 0xbc3828: LoadField: r0 = r1->field_7
    //     0xbc3828: ldur            w0, [x1, #7]
    // 0xbc382c: DecompressPointer r0
    //     0xbc382c: add             x0, x0, HEAP, lsl #32
    // 0xbc3830: r16 = 14.000000
    //     0xbc3830: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbc3834: ldr             x16, [x16, #0x1d8]
    // 0xbc3838: r30 = Instance_Color
    //     0xbc3838: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbc383c: stp             lr, x16, [SP]
    // 0xbc3840: mov             x1, x0
    // 0xbc3844: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbc3844: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbc3848: ldr             x4, [x4, #0xaa0]
    // 0xbc384c: r0 = copyWith()
    //     0xbc384c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbc3850: stur            x0, [fp, #-0x10]
    // 0xbc3854: r0 = Text()
    //     0xbc3854: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbc3858: mov             x3, x0
    // 0xbc385c: r0 = "OKAY, GOT IT"
    //     0xbc385c: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c2d8] "OKAY, GOT IT"
    //     0xbc3860: ldr             x0, [x0, #0x2d8]
    // 0xbc3864: stur            x3, [fp, #-0x28]
    // 0xbc3868: StoreField: r3->field_b = r0
    //     0xbc3868: stur            w0, [x3, #0xb]
    // 0xbc386c: ldur            x0, [fp, #-0x10]
    // 0xbc3870: StoreField: r3->field_13 = r0
    //     0xbc3870: stur            w0, [x3, #0x13]
    // 0xbc3874: ldur            x2, [fp, #-0x18]
    // 0xbc3878: r1 = Function '<anonymous closure>':.
    //     0xbc3878: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c2e0] AnonymousClosure: (0xbc3a4c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/partial_popup_bottom_sheet.dart] _PartialCodPopupBottomSheet::build (0xbc2c90)
    //     0xbc387c: ldr             x1, [x1, #0x2e0]
    // 0xbc3880: r0 = AllocateClosure()
    //     0xbc3880: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbc3884: stur            x0, [fp, #-0x10]
    // 0xbc3888: r0 = TextButton()
    //     0xbc3888: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbc388c: mov             x1, x0
    // 0xbc3890: ldur            x0, [fp, #-0x10]
    // 0xbc3894: stur            x1, [fp, #-0x18]
    // 0xbc3898: StoreField: r1->field_b = r0
    //     0xbc3898: stur            w0, [x1, #0xb]
    // 0xbc389c: r0 = false
    //     0xbc389c: add             x0, NULL, #0x30  ; false
    // 0xbc38a0: StoreField: r1->field_27 = r0
    //     0xbc38a0: stur            w0, [x1, #0x27]
    // 0xbc38a4: r0 = true
    //     0xbc38a4: add             x0, NULL, #0x20  ; true
    // 0xbc38a8: StoreField: r1->field_2f = r0
    //     0xbc38a8: stur            w0, [x1, #0x2f]
    // 0xbc38ac: ldur            x0, [fp, #-0x28]
    // 0xbc38b0: StoreField: r1->field_37 = r0
    //     0xbc38b0: stur            w0, [x1, #0x37]
    // 0xbc38b4: r0 = TextButtonTheme()
    //     0xbc38b4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbc38b8: mov             x1, x0
    // 0xbc38bc: ldur            x0, [fp, #-8]
    // 0xbc38c0: stur            x1, [fp, #-0x10]
    // 0xbc38c4: StoreField: r1->field_f = r0
    //     0xbc38c4: stur            w0, [x1, #0xf]
    // 0xbc38c8: ldur            x0, [fp, #-0x18]
    // 0xbc38cc: StoreField: r1->field_b = r0
    //     0xbc38cc: stur            w0, [x1, #0xb]
    // 0xbc38d0: ldur            d0, [fp, #-0x60]
    // 0xbc38d4: r0 = inline_Allocate_Double()
    //     0xbc38d4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbc38d8: add             x0, x0, #0x10
    //     0xbc38dc: cmp             x2, x0
    //     0xbc38e0: b.ls            #0xbc3a14
    //     0xbc38e4: str             x0, [THR, #0x50]  ; THR::top
    //     0xbc38e8: sub             x0, x0, #0xf
    //     0xbc38ec: movz            x2, #0xe15c
    //     0xbc38f0: movk            x2, #0x3, lsl #16
    //     0xbc38f4: stur            x2, [x0, #-1]
    // 0xbc38f8: StoreField: r0->field_7 = d0
    //     0xbc38f8: stur            d0, [x0, #7]
    // 0xbc38fc: stur            x0, [fp, #-8]
    // 0xbc3900: r0 = SizedBox()
    //     0xbc3900: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbc3904: mov             x2, x0
    // 0xbc3908: ldur            x0, [fp, #-8]
    // 0xbc390c: stur            x2, [fp, #-0x18]
    // 0xbc3910: StoreField: r2->field_f = r0
    //     0xbc3910: stur            w0, [x2, #0xf]
    // 0xbc3914: r0 = 45.000000
    //     0xbc3914: add             x0, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xbc3918: ldr             x0, [x0, #0xc88]
    // 0xbc391c: StoreField: r2->field_13 = r0
    //     0xbc391c: stur            w0, [x2, #0x13]
    // 0xbc3920: ldur            x0, [fp, #-0x10]
    // 0xbc3924: StoreField: r2->field_b = r0
    //     0xbc3924: stur            w0, [x2, #0xb]
    // 0xbc3928: r1 = <StackParentData>
    //     0xbc3928: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xbc392c: ldr             x1, [x1, #0x8e0]
    // 0xbc3930: r0 = Positioned()
    //     0xbc3930: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xbc3934: mov             x3, x0
    // 0xbc3938: r0 = 0.000000
    //     0xbc3938: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbc393c: stur            x3, [fp, #-8]
    // 0xbc3940: StoreField: r3->field_13 = r0
    //     0xbc3940: stur            w0, [x3, #0x13]
    // 0xbc3944: StoreField: r3->field_1b = r0
    //     0xbc3944: stur            w0, [x3, #0x1b]
    // 0xbc3948: StoreField: r3->field_1f = r0
    //     0xbc3948: stur            w0, [x3, #0x1f]
    // 0xbc394c: ldur            x0, [fp, #-0x18]
    // 0xbc3950: StoreField: r3->field_b = r0
    //     0xbc3950: stur            w0, [x3, #0xb]
    // 0xbc3954: r1 = Null
    //     0xbc3954: mov             x1, NULL
    // 0xbc3958: r2 = 4
    //     0xbc3958: movz            x2, #0x4
    // 0xbc395c: r0 = AllocateArray()
    //     0xbc395c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbc3960: mov             x2, x0
    // 0xbc3964: ldur            x0, [fp, #-0x20]
    // 0xbc3968: stur            x2, [fp, #-0x10]
    // 0xbc396c: StoreField: r2->field_f = r0
    //     0xbc396c: stur            w0, [x2, #0xf]
    // 0xbc3970: ldur            x0, [fp, #-8]
    // 0xbc3974: StoreField: r2->field_13 = r0
    //     0xbc3974: stur            w0, [x2, #0x13]
    // 0xbc3978: r1 = <Widget>
    //     0xbc3978: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbc397c: r0 = AllocateGrowableArray()
    //     0xbc397c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbc3980: mov             x1, x0
    // 0xbc3984: ldur            x0, [fp, #-0x10]
    // 0xbc3988: stur            x1, [fp, #-8]
    // 0xbc398c: StoreField: r1->field_f = r0
    //     0xbc398c: stur            w0, [x1, #0xf]
    // 0xbc3990: r0 = 4
    //     0xbc3990: movz            x0, #0x4
    // 0xbc3994: StoreField: r1->field_b = r0
    //     0xbc3994: stur            w0, [x1, #0xb]
    // 0xbc3998: r0 = Stack()
    //     0xbc3998: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbc399c: mov             x1, x0
    // 0xbc39a0: r0 = Instance_AlignmentDirectional
    //     0xbc39a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xbc39a4: ldr             x0, [x0, #0xd08]
    // 0xbc39a8: stur            x1, [fp, #-0x10]
    // 0xbc39ac: StoreField: r1->field_f = r0
    //     0xbc39ac: stur            w0, [x1, #0xf]
    // 0xbc39b0: r0 = Instance_StackFit
    //     0xbc39b0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbc39b4: ldr             x0, [x0, #0xfa8]
    // 0xbc39b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbc39b8: stur            w0, [x1, #0x17]
    // 0xbc39bc: r0 = Instance_Clip
    //     0xbc39bc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbc39c0: ldr             x0, [x0, #0x7e0]
    // 0xbc39c4: StoreField: r1->field_1b = r0
    //     0xbc39c4: stur            w0, [x1, #0x1b]
    // 0xbc39c8: ldur            x0, [fp, #-8]
    // 0xbc39cc: StoreField: r1->field_b = r0
    //     0xbc39cc: stur            w0, [x1, #0xb]
    // 0xbc39d0: r0 = Padding()
    //     0xbc39d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbc39d4: r1 = Instance_EdgeInsets
    //     0xbc39d4: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5b1b8] Obj!EdgeInsets@d581f1
    //     0xbc39d8: ldr             x1, [x1, #0x1b8]
    // 0xbc39dc: StoreField: r0->field_f = r1
    //     0xbc39dc: stur            w1, [x0, #0xf]
    // 0xbc39e0: ldur            x1, [fp, #-0x10]
    // 0xbc39e4: StoreField: r0->field_b = r1
    //     0xbc39e4: stur            w1, [x0, #0xb]
    // 0xbc39e8: LeaveFrame
    //     0xbc39e8: mov             SP, fp
    //     0xbc39ec: ldp             fp, lr, [SP], #0x10
    // 0xbc39f0: ret
    //     0xbc39f0: ret             
    // 0xbc39f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc39f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc39f8: b               #0xbc2cb8
    // 0xbc39fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc39fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc3a00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3a00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc3a04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3a04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc3a08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3a08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc3a0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3a0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc3a10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3a10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbc3a14: SaveReg d0
    //     0xbc3a14: str             q0, [SP, #-0x10]!
    // 0xbc3a18: SaveReg r1
    //     0xbc3a18: str             x1, [SP, #-8]!
    // 0xbc3a1c: r0 = AllocateDouble()
    //     0xbc3a1c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbc3a20: RestoreReg r1
    //     0xbc3a20: ldr             x1, [SP], #8
    // 0xbc3a24: RestoreReg d0
    //     0xbc3a24: ldr             q0, [SP], #0x10
    // 0xbc3a28: b               #0xbc38f8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbc3a4c, size: 0xac
    // 0xbc3a4c: EnterFrame
    //     0xbc3a4c: stp             fp, lr, [SP, #-0x10]!
    //     0xbc3a50: mov             fp, SP
    // 0xbc3a54: AllocStack(0x10)
    //     0xbc3a54: sub             SP, SP, #0x10
    // 0xbc3a58: SetupParameters()
    //     0xbc3a58: ldr             x0, [fp, #0x10]
    //     0xbc3a5c: ldur            w1, [x0, #0x17]
    //     0xbc3a60: add             x1, x1, HEAP, lsl #32
    //     0xbc3a64: stur            x1, [fp, #-8]
    // 0xbc3a68: CheckStackOverflow
    //     0xbc3a68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbc3a6c: cmp             SP, x16
    //     0xbc3a70: b.ls            #0xbc3aec
    // 0xbc3a74: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbc3a74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbc3a78: ldr             x0, [x0, #0x1c80]
    //     0xbc3a7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbc3a80: cmp             w0, w16
    //     0xbc3a84: b.ne            #0xbc3a90
    //     0xbc3a88: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbc3a8c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbc3a90: str             NULL, [SP]
    // 0xbc3a94: r4 = const [0x1, 0, 0, 0, null]
    //     0xbc3a94: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbc3a98: r0 = GetNavigation.back()
    //     0xbc3a98: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbc3a9c: ldur            x0, [fp, #-8]
    // 0xbc3aa0: LoadField: r1 = r0->field_f
    //     0xbc3aa0: ldur            w1, [x0, #0xf]
    // 0xbc3aa4: DecompressPointer r1
    //     0xbc3aa4: add             x1, x1, HEAP, lsl #32
    // 0xbc3aa8: LoadField: r0 = r1->field_b
    //     0xbc3aa8: ldur            w0, [x1, #0xb]
    // 0xbc3aac: DecompressPointer r0
    //     0xbc3aac: add             x0, x0, HEAP, lsl #32
    // 0xbc3ab0: cmp             w0, NULL
    // 0xbc3ab4: b.eq            #0xbc3af4
    // 0xbc3ab8: LoadField: r1 = r0->field_f
    //     0xbc3ab8: ldur            w1, [x0, #0xf]
    // 0xbc3abc: DecompressPointer r1
    //     0xbc3abc: add             x1, x1, HEAP, lsl #32
    // 0xbc3ac0: str             x1, [SP]
    // 0xbc3ac4: r4 = 0
    //     0xbc3ac4: movz            x4, #0
    // 0xbc3ac8: ldr             x0, [SP]
    // 0xbc3acc: r16 = UnlinkedCall_0x613b5c
    //     0xbc3acc: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c2e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbc3ad0: add             x16, x16, #0x2e8
    // 0xbc3ad4: ldp             x5, lr, [x16]
    // 0xbc3ad8: blr             lr
    // 0xbc3adc: r0 = Null
    //     0xbc3adc: mov             x0, NULL
    // 0xbc3ae0: LeaveFrame
    //     0xbc3ae0: mov             SP, fp
    //     0xbc3ae4: ldp             fp, lr, [SP], #0x10
    // 0xbc3ae8: ret
    //     0xbc3ae8: ret             
    // 0xbc3aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbc3aec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbc3af0: b               #0xbc3a74
    // 0xbc3af4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbc3af4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4010, size: 0x14, field offset: 0xc
//   const constructor, 
class PartialCodPopupBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80670, size: 0x24
    // 0xc80670: EnterFrame
    //     0xc80670: stp             fp, lr, [SP, #-0x10]!
    //     0xc80674: mov             fp, SP
    // 0xc80678: mov             x0, x1
    // 0xc8067c: r1 = <PartialCodPopupBottomSheet>
    //     0xc8067c: add             x1, PP, #0x49, lsl #12  ; [pp+0x493e8] TypeArguments: <PartialCodPopupBottomSheet>
    //     0xc80680: ldr             x1, [x1, #0x3e8]
    // 0xc80684: r0 = _PartialCodPopupBottomSheet()
    //     0xc80684: bl              #0xc80694  ; Allocate_PartialCodPopupBottomSheetStub -> _PartialCodPopupBottomSheet (size=0x14)
    // 0xc80688: LeaveFrame
    //     0xc80688: mov             SP, fp
    //     0xc8068c: ldp             fp, lr, [SP], #0x10
    // 0xc80690: ret
    //     0xc80690: ret             
  }
}
