// lib: , url: package:customer_app/app/routes/line.dart

// class id: 1049589, size: 0x8
class :: {
}

// class id: 5004, size: 0x8, field offset: 0x8
abstract class LineAppPages extends Object {

  static late final List<GetPage<dynamic>> linePages; // offset: 0xe88

  static List<GetPage<dynamic>> linePages() {
    // ** addr: 0x9227b8, size: 0xf18
    // 0x9227b8: EnterFrame
    //     0x9227b8: stp             fp, lr, [SP, #-0x10]!
    //     0x9227bc: mov             fp, SP
    // 0x9227c0: AllocStack(0x18)
    //     0x9227c0: sub             SP, SP, #0x18
    // 0x9227c4: CheckStackOverflow
    //     0x9227c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9227c8: cmp             SP, x16
    //     0x9227cc: b.ls            #0x9236c8
    // 0x9227d0: r1 = Function '<anonymous closure>': static.
    //     0x9227d0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf38] AnonymousClosure: static (0x923948), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9227d4: ldr             x1, [x1, #0xf38]
    // 0x9227d8: r2 = Null
    //     0x9227d8: mov             x2, NULL
    // 0x9227dc: r0 = AllocateClosure()
    //     0x9227dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9227e0: stur            x0, [fp, #-8]
    // 0x9227e4: r0 = InitialBinding()
    //     0x9227e4: bl              #0x91be08  ; AllocateInitialBindingStub -> InitialBinding (size=0x8)
    // 0x9227e8: r1 = Null
    //     0x9227e8: mov             x1, NULL
    // 0x9227ec: stur            x0, [fp, #-0x10]
    // 0x9227f0: r0 = GetPage()
    //     0x9227f0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9227f4: mov             x1, x0
    // 0x9227f8: ldur            x2, [fp, #-0x10]
    // 0x9227fc: ldur            x5, [fp, #-8]
    // 0x922800: r3 = "/"
    //     0x922800: ldr             x3, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x922804: stur            x0, [fp, #-8]
    // 0x922808: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922808: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x92280c: r0 = GetPage()
    //     0x92280c: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922810: r1 = <GetPage>
    //     0x922810: add             x1, PP, #0xb, lsl #12  ; [pp+0xba40] TypeArguments: <GetPage>
    //     0x922814: ldr             x1, [x1, #0xa40]
    // 0x922818: r2 = 68
    //     0x922818: movz            x2, #0x44
    // 0x92281c: r0 = AllocateArray()
    //     0x92281c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x922820: mov             x3, x0
    // 0x922824: ldur            x0, [fp, #-8]
    // 0x922828: stur            x3, [fp, #-0x10]
    // 0x92282c: StoreField: r3->field_f = r0
    //     0x92282c: stur            w0, [x3, #0xf]
    // 0x922830: r1 = Function '<anonymous closure>': static.
    //     0x922830: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf40] AnonymousClosure: static (0x92393c), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922834: ldr             x1, [x1, #0xf40]
    // 0x922838: r2 = Null
    //     0x922838: mov             x2, NULL
    // 0x92283c: r0 = AllocateClosure()
    //     0x92283c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922840: stur            x0, [fp, #-8]
    // 0x922844: r0 = HomePageBinding()
    //     0x922844: bl              #0x91cfe4  ; AllocateHomePageBindingStub -> HomePageBinding (size=0x8)
    // 0x922848: r1 = Null
    //     0x922848: mov             x1, NULL
    // 0x92284c: stur            x0, [fp, #-0x18]
    // 0x922850: r0 = GetPage()
    //     0x922850: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922854: mov             x1, x0
    // 0x922858: ldur            x2, [fp, #-0x18]
    // 0x92285c: ldur            x5, [fp, #-8]
    // 0x922860: r3 = "/home"
    //     0x922860: add             x3, PP, #0xd, lsl #12  ; [pp+0xd828] "/home"
    //     0x922864: ldr             x3, [x3, #0x828]
    // 0x922868: stur            x0, [fp, #-8]
    // 0x92286c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92286c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922870: r0 = GetPage()
    //     0x922870: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922874: ldur            x1, [fp, #-0x10]
    // 0x922878: ldur            x0, [fp, #-8]
    // 0x92287c: ArrayStore: r1[1] = r0  ; List_4
    //     0x92287c: add             x25, x1, #0x13
    //     0x922880: str             w0, [x25]
    //     0x922884: tbz             w0, #0, #0x9228a0
    //     0x922888: ldurb           w16, [x1, #-1]
    //     0x92288c: ldurb           w17, [x0, #-1]
    //     0x922890: and             x16, x17, x16, lsr #2
    //     0x922894: tst             x16, HEAP, lsr #32
    //     0x922898: b.eq            #0x9228a0
    //     0x92289c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9228a0: r1 = Function '<anonymous closure>': static.
    //     0x9228a0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf48] AnonymousClosure: static (0x923930), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9228a4: ldr             x1, [x1, #0xf48]
    // 0x9228a8: r2 = Null
    //     0x9228a8: mov             x2, NULL
    // 0x9228ac: r0 = AllocateClosure()
    //     0x9228ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9228b0: stur            x0, [fp, #-8]
    // 0x9228b4: r0 = SearchPageBinding()
    //     0x9228b4: bl              #0x91cfd8  ; AllocateSearchPageBindingStub -> SearchPageBinding (size=0x8)
    // 0x9228b8: r1 = Null
    //     0x9228b8: mov             x1, NULL
    // 0x9228bc: stur            x0, [fp, #-0x18]
    // 0x9228c0: r0 = GetPage()
    //     0x9228c0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9228c4: mov             x1, x0
    // 0x9228c8: ldur            x2, [fp, #-0x18]
    // 0x9228cc: ldur            x5, [fp, #-8]
    // 0x9228d0: r3 = "/search"
    //     0x9228d0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x9228d4: ldr             x3, [x3, #0x838]
    // 0x9228d8: stur            x0, [fp, #-8]
    // 0x9228dc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9228dc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9228e0: r0 = GetPage()
    //     0x9228e0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9228e4: ldur            x1, [fp, #-0x10]
    // 0x9228e8: ldur            x0, [fp, #-8]
    // 0x9228ec: ArrayStore: r1[2] = r0  ; List_4
    //     0x9228ec: add             x25, x1, #0x17
    //     0x9228f0: str             w0, [x25]
    //     0x9228f4: tbz             w0, #0, #0x922910
    //     0x9228f8: ldurb           w16, [x1, #-1]
    //     0x9228fc: ldurb           w17, [x0, #-1]
    //     0x922900: and             x16, x17, x16, lsr #2
    //     0x922904: tst             x16, HEAP, lsr #32
    //     0x922908: b.eq            #0x922910
    //     0x92290c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922910: r1 = Function '<anonymous closure>': static.
    //     0x922910: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf50] AnonymousClosure: static (0x9238dc), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922914: ldr             x1, [x1, #0xf50]
    // 0x922918: r2 = Null
    //     0x922918: mov             x2, NULL
    // 0x92291c: r0 = AllocateClosure()
    //     0x92291c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922920: stur            x0, [fp, #-8]
    // 0x922924: r0 = CollectionPageBinding()
    //     0x922924: bl              #0x91cfcc  ; AllocateCollectionPageBindingStub -> CollectionPageBinding (size=0x8)
    // 0x922928: r1 = Null
    //     0x922928: mov             x1, NULL
    // 0x92292c: stur            x0, [fp, #-0x18]
    // 0x922930: r0 = GetPage()
    //     0x922930: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922934: mov             x1, x0
    // 0x922938: ldur            x2, [fp, #-0x18]
    // 0x92293c: ldur            x5, [fp, #-8]
    // 0x922940: r3 = "/collection"
    //     0x922940: add             x3, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x922944: ldr             x3, [x3, #0x458]
    // 0x922948: stur            x0, [fp, #-8]
    // 0x92294c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92294c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922950: r0 = GetPage()
    //     0x922950: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922954: ldur            x1, [fp, #-0x10]
    // 0x922958: ldur            x0, [fp, #-8]
    // 0x92295c: ArrayStore: r1[3] = r0  ; List_4
    //     0x92295c: add             x25, x1, #0x1b
    //     0x922960: str             w0, [x25]
    //     0x922964: tbz             w0, #0, #0x922980
    //     0x922968: ldurb           w16, [x1, #-1]
    //     0x92296c: ldurb           w17, [x0, #-1]
    //     0x922970: and             x16, x17, x16, lsr #2
    //     0x922974: tst             x16, HEAP, lsr #32
    //     0x922978: b.eq            #0x922980
    //     0x92297c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922980: r1 = Function '<anonymous closure>': static.
    //     0x922980: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf58] AnonymousClosure: static (0x9238d0), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922984: ldr             x1, [x1, #0xf58]
    // 0x922988: r2 = Null
    //     0x922988: mov             x2, NULL
    // 0x92298c: r0 = AllocateClosure()
    //     0x92298c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922990: stur            x0, [fp, #-8]
    // 0x922994: r0 = ProductDetailBinding()
    //     0x922994: bl              #0x91cfc0  ; AllocateProductDetailBindingStub -> ProductDetailBinding (size=0x8)
    // 0x922998: r1 = Null
    //     0x922998: mov             x1, NULL
    // 0x92299c: stur            x0, [fp, #-0x18]
    // 0x9229a0: r0 = GetPage()
    //     0x9229a0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9229a4: mov             x1, x0
    // 0x9229a8: ldur            x2, [fp, #-0x18]
    // 0x9229ac: ldur            x5, [fp, #-8]
    // 0x9229b0: r3 = "/product-detail"
    //     0x9229b0: add             x3, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x9229b4: ldr             x3, [x3, #0x4a8]
    // 0x9229b8: stur            x0, [fp, #-8]
    // 0x9229bc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9229bc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9229c0: r0 = GetPage()
    //     0x9229c0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9229c4: ldur            x1, [fp, #-0x10]
    // 0x9229c8: ldur            x0, [fp, #-8]
    // 0x9229cc: ArrayStore: r1[4] = r0  ; List_4
    //     0x9229cc: add             x25, x1, #0x1f
    //     0x9229d0: str             w0, [x25]
    //     0x9229d4: tbz             w0, #0, #0x9229f0
    //     0x9229d8: ldurb           w16, [x1, #-1]
    //     0x9229dc: ldurb           w17, [x0, #-1]
    //     0x9229e0: and             x16, x17, x16, lsr #2
    //     0x9229e4: tst             x16, HEAP, lsr #32
    //     0x9229e8: b.eq            #0x9229f0
    //     0x9229ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9229f0: r1 = Function '<anonymous closure>': static.
    //     0x9229f0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf60] AnonymousClosure: static (0x9238c4), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9229f4: ldr             x1, [x1, #0xf60]
    // 0x9229f8: r2 = Null
    //     0x9229f8: mov             x2, NULL
    // 0x9229fc: r0 = AllocateClosure()
    //     0x9229fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922a00: stur            x0, [fp, #-8]
    // 0x922a04: r0 = BagBinding()
    //     0x922a04: bl              #0x91cfb4  ; AllocateBagBindingStub -> BagBinding (size=0x8)
    // 0x922a08: r1 = Null
    //     0x922a08: mov             x1, NULL
    // 0x922a0c: stur            x0, [fp, #-0x18]
    // 0x922a10: r0 = GetPage()
    //     0x922a10: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922a14: mov             x1, x0
    // 0x922a18: ldur            x2, [fp, #-0x18]
    // 0x922a1c: ldur            x5, [fp, #-8]
    // 0x922a20: r3 = "/bag"
    //     0x922a20: add             x3, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x922a24: ldr             x3, [x3, #0x468]
    // 0x922a28: stur            x0, [fp, #-8]
    // 0x922a2c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922a2c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922a30: r0 = GetPage()
    //     0x922a30: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922a34: ldur            x1, [fp, #-0x10]
    // 0x922a38: ldur            x0, [fp, #-8]
    // 0x922a3c: ArrayStore: r1[5] = r0  ; List_4
    //     0x922a3c: add             x25, x1, #0x23
    //     0x922a40: str             w0, [x25]
    //     0x922a44: tbz             w0, #0, #0x922a60
    //     0x922a48: ldurb           w16, [x1, #-1]
    //     0x922a4c: ldurb           w17, [x0, #-1]
    //     0x922a50: and             x16, x17, x16, lsr #2
    //     0x922a54: tst             x16, HEAP, lsr #32
    //     0x922a58: b.eq            #0x922a60
    //     0x922a5c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922a60: r1 = Function '<anonymous closure>': static.
    //     0x922a60: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf68] AnonymousClosure: static (0x9238b8), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922a64: ldr             x1, [x1, #0xf68]
    // 0x922a68: r2 = Null
    //     0x922a68: mov             x2, NULL
    // 0x922a6c: r0 = AllocateClosure()
    //     0x922a6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922a70: stur            x0, [fp, #-8]
    // 0x922a74: r0 = ProfileBinding()
    //     0x922a74: bl              #0x91cfa8  ; AllocateProfileBindingStub -> ProfileBinding (size=0x8)
    // 0x922a78: r1 = Null
    //     0x922a78: mov             x1, NULL
    // 0x922a7c: stur            x0, [fp, #-0x18]
    // 0x922a80: r0 = GetPage()
    //     0x922a80: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922a84: mov             x1, x0
    // 0x922a88: ldur            x2, [fp, #-0x18]
    // 0x922a8c: ldur            x5, [fp, #-8]
    // 0x922a90: r3 = "/profile"
    //     0x922a90: add             x3, PP, #0xd, lsl #12  ; [pp+0xd860] "/profile"
    //     0x922a94: ldr             x3, [x3, #0x860]
    // 0x922a98: stur            x0, [fp, #-8]
    // 0x922a9c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922a9c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922aa0: r0 = GetPage()
    //     0x922aa0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922aa4: ldur            x1, [fp, #-0x10]
    // 0x922aa8: ldur            x0, [fp, #-8]
    // 0x922aac: ArrayStore: r1[6] = r0  ; List_4
    //     0x922aac: add             x25, x1, #0x27
    //     0x922ab0: str             w0, [x25]
    //     0x922ab4: tbz             w0, #0, #0x922ad0
    //     0x922ab8: ldurb           w16, [x1, #-1]
    //     0x922abc: ldurb           w17, [x0, #-1]
    //     0x922ac0: and             x16, x17, x16, lsr #2
    //     0x922ac4: tst             x16, HEAP, lsr #32
    //     0x922ac8: b.eq            #0x922ad0
    //     0x922acc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922ad0: r1 = Function '<anonymous closure>': static.
    //     0x922ad0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf70] AnonymousClosure: static (0x9238ac), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922ad4: ldr             x1, [x1, #0xf70]
    // 0x922ad8: r2 = Null
    //     0x922ad8: mov             x2, NULL
    // 0x922adc: r0 = AllocateClosure()
    //     0x922adc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922ae0: stur            x0, [fp, #-8]
    // 0x922ae4: r0 = OrdersBinding()
    //     0x922ae4: bl              #0x91cf9c  ; AllocateOrdersBindingStub -> OrdersBinding (size=0x8)
    // 0x922ae8: r1 = Null
    //     0x922ae8: mov             x1, NULL
    // 0x922aec: stur            x0, [fp, #-0x18]
    // 0x922af0: r0 = GetPage()
    //     0x922af0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922af4: mov             x1, x0
    // 0x922af8: ldur            x2, [fp, #-0x18]
    // 0x922afc: ldur            x5, [fp, #-8]
    // 0x922b00: r3 = "/orderss"
    //     0x922b00: add             x3, PP, #0xd, lsl #12  ; [pp+0xd870] "/orderss"
    //     0x922b04: ldr             x3, [x3, #0x870]
    // 0x922b08: stur            x0, [fp, #-8]
    // 0x922b0c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922b0c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922b10: r0 = GetPage()
    //     0x922b10: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922b14: ldur            x1, [fp, #-0x10]
    // 0x922b18: ldur            x0, [fp, #-8]
    // 0x922b1c: ArrayStore: r1[7] = r0  ; List_4
    //     0x922b1c: add             x25, x1, #0x2b
    //     0x922b20: str             w0, [x25]
    //     0x922b24: tbz             w0, #0, #0x922b40
    //     0x922b28: ldurb           w16, [x1, #-1]
    //     0x922b2c: ldurb           w17, [x0, #-1]
    //     0x922b30: and             x16, x17, x16, lsr #2
    //     0x922b34: tst             x16, HEAP, lsr #32
    //     0x922b38: b.eq            #0x922b40
    //     0x922b3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922b40: r1 = Function '<anonymous closure>': static.
    //     0x922b40: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf78] AnonymousClosure: static (0x923850), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922b44: ldr             x1, [x1, #0xf78]
    // 0x922b48: r2 = Null
    //     0x922b48: mov             x2, NULL
    // 0x922b4c: r0 = AllocateClosure()
    //     0x922b4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922b50: stur            x0, [fp, #-8]
    // 0x922b54: r0 = LoginBinding()
    //     0x922b54: bl              #0x91cf90  ; AllocateLoginBindingStub -> LoginBinding (size=0x8)
    // 0x922b58: r1 = Null
    //     0x922b58: mov             x1, NULL
    // 0x922b5c: stur            x0, [fp, #-0x18]
    // 0x922b60: r0 = GetPage()
    //     0x922b60: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922b64: mov             x1, x0
    // 0x922b68: ldur            x2, [fp, #-0x18]
    // 0x922b6c: ldur            x5, [fp, #-8]
    // 0x922b70: r3 = "/login"
    //     0x922b70: add             x3, PP, #0xd, lsl #12  ; [pp+0xd880] "/login"
    //     0x922b74: ldr             x3, [x3, #0x880]
    // 0x922b78: stur            x0, [fp, #-8]
    // 0x922b7c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922b7c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922b80: r0 = GetPage()
    //     0x922b80: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922b84: ldur            x1, [fp, #-0x10]
    // 0x922b88: ldur            x0, [fp, #-8]
    // 0x922b8c: ArrayStore: r1[8] = r0  ; List_4
    //     0x922b8c: add             x25, x1, #0x2f
    //     0x922b90: str             w0, [x25]
    //     0x922b94: tbz             w0, #0, #0x922bb0
    //     0x922b98: ldurb           w16, [x1, #-1]
    //     0x922b9c: ldurb           w17, [x0, #-1]
    //     0x922ba0: and             x16, x17, x16, lsr #2
    //     0x922ba4: tst             x16, HEAP, lsr #32
    //     0x922ba8: b.eq            #0x922bb0
    //     0x922bac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922bb0: r1 = Function '<anonymous closure>': static.
    //     0x922bb0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf80] AnonymousClosure: static (0x9237f0), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922bb4: ldr             x1, [x1, #0xf80]
    // 0x922bb8: r2 = Null
    //     0x922bb8: mov             x2, NULL
    // 0x922bbc: r0 = AllocateClosure()
    //     0x922bbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922bc0: stur            x0, [fp, #-8]
    // 0x922bc4: r0 = OrderDetailBinding()
    //     0x922bc4: bl              #0x91cf84  ; AllocateOrderDetailBindingStub -> OrderDetailBinding (size=0x8)
    // 0x922bc8: r1 = Null
    //     0x922bc8: mov             x1, NULL
    // 0x922bcc: stur            x0, [fp, #-0x18]
    // 0x922bd0: r0 = GetPage()
    //     0x922bd0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922bd4: mov             x1, x0
    // 0x922bd8: ldur            x2, [fp, #-0x18]
    // 0x922bdc: ldur            x5, [fp, #-8]
    // 0x922be0: r3 = "/order"
    //     0x922be0: add             x3, PP, #0xb, lsl #12  ; [pp+0xb430] "/order"
    //     0x922be4: ldr             x3, [x3, #0x430]
    // 0x922be8: stur            x0, [fp, #-8]
    // 0x922bec: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922bec: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922bf0: r0 = GetPage()
    //     0x922bf0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922bf4: ldur            x1, [fp, #-0x10]
    // 0x922bf8: ldur            x0, [fp, #-8]
    // 0x922bfc: ArrayStore: r1[9] = r0  ; List_4
    //     0x922bfc: add             x25, x1, #0x33
    //     0x922c00: str             w0, [x25]
    //     0x922c04: tbz             w0, #0, #0x922c20
    //     0x922c08: ldurb           w16, [x1, #-1]
    //     0x922c0c: ldurb           w17, [x0, #-1]
    //     0x922c10: and             x16, x17, x16, lsr #2
    //     0x922c14: tst             x16, HEAP, lsr #32
    //     0x922c18: b.eq            #0x922c20
    //     0x922c1c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922c20: r1 = Function '<anonymous closure>': static.
    //     0x922c20: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf88] AnonymousClosure: static (0x9237e4), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922c24: ldr             x1, [x1, #0xf88]
    // 0x922c28: r2 = Null
    //     0x922c28: mov             x2, NULL
    // 0x922c2c: r0 = AllocateClosure()
    //     0x922c2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922c30: stur            x0, [fp, #-8]
    // 0x922c34: r0 = TestimonialsBinding()
    //     0x922c34: bl              #0x91cf78  ; AllocateTestimonialsBindingStub -> TestimonialsBinding (size=0x8)
    // 0x922c38: r1 = Null
    //     0x922c38: mov             x1, NULL
    // 0x922c3c: stur            x0, [fp, #-0x18]
    // 0x922c40: r0 = GetPage()
    //     0x922c40: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922c44: mov             x1, x0
    // 0x922c48: ldur            x2, [fp, #-0x18]
    // 0x922c4c: ldur            x5, [fp, #-8]
    // 0x922c50: r3 = "/testimonials"
    //     0x922c50: add             x3, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0x922c54: ldr             x3, [x3, #0x898]
    // 0x922c58: stur            x0, [fp, #-8]
    // 0x922c5c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922c5c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922c60: r0 = GetPage()
    //     0x922c60: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922c64: ldur            x1, [fp, #-0x10]
    // 0x922c68: ldur            x0, [fp, #-8]
    // 0x922c6c: ArrayStore: r1[10] = r0  ; List_4
    //     0x922c6c: add             x25, x1, #0x37
    //     0x922c70: str             w0, [x25]
    //     0x922c74: tbz             w0, #0, #0x922c90
    //     0x922c78: ldurb           w16, [x1, #-1]
    //     0x922c7c: ldurb           w17, [x0, #-1]
    //     0x922c80: and             x16, x17, x16, lsr #2
    //     0x922c84: tst             x16, HEAP, lsr #32
    //     0x922c88: b.eq            #0x922c90
    //     0x922c8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922c90: r1 = Function '<anonymous closure>': static.
    //     0x922c90: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf90] AnonymousClosure: static (0x9237d8), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922c94: ldr             x1, [x1, #0xf90]
    // 0x922c98: r2 = Null
    //     0x922c98: mov             x2, NULL
    // 0x922c9c: r0 = AllocateClosure()
    //     0x922c9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922ca0: stur            x0, [fp, #-8]
    // 0x922ca4: r0 = CustomizationBinding()
    //     0x922ca4: bl              #0x91cf6c  ; AllocateCustomizationBindingStub -> CustomizationBinding (size=0x8)
    // 0x922ca8: r1 = Null
    //     0x922ca8: mov             x1, NULL
    // 0x922cac: stur            x0, [fp, #-0x18]
    // 0x922cb0: r0 = GetPage()
    //     0x922cb0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922cb4: mov             x1, x0
    // 0x922cb8: ldur            x2, [fp, #-0x18]
    // 0x922cbc: ldur            x5, [fp, #-8]
    // 0x922cc0: r3 = "/customization"
    //     0x922cc0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8a8] "/customization"
    //     0x922cc4: ldr             x3, [x3, #0x8a8]
    // 0x922cc8: stur            x0, [fp, #-8]
    // 0x922ccc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922ccc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922cd0: r0 = GetPage()
    //     0x922cd0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922cd4: ldur            x1, [fp, #-0x10]
    // 0x922cd8: ldur            x0, [fp, #-8]
    // 0x922cdc: ArrayStore: r1[11] = r0  ; List_4
    //     0x922cdc: add             x25, x1, #0x3b
    //     0x922ce0: str             w0, [x25]
    //     0x922ce4: tbz             w0, #0, #0x922d00
    //     0x922ce8: ldurb           w16, [x1, #-1]
    //     0x922cec: ldurb           w17, [x0, #-1]
    //     0x922cf0: and             x16, x17, x16, lsr #2
    //     0x922cf4: tst             x16, HEAP, lsr #32
    //     0x922cf8: b.eq            #0x922d00
    //     0x922cfc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922d00: r1 = Function '<anonymous closure>': static.
    //     0x922d00: add             x1, PP, #0xd, lsl #12  ; [pp+0xdf98] AnonymousClosure: static (0x9237cc), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922d04: ldr             x1, [x1, #0xf98]
    // 0x922d08: r2 = Null
    //     0x922d08: mov             x2, NULL
    // 0x922d0c: r0 = AllocateClosure()
    //     0x922d0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922d10: stur            x0, [fp, #-8]
    // 0x922d14: r0 = ReturnOrderBinding()
    //     0x922d14: bl              #0x91cf60  ; AllocateReturnOrderBindingStub -> ReturnOrderBinding (size=0x8)
    // 0x922d18: r1 = Null
    //     0x922d18: mov             x1, NULL
    // 0x922d1c: stur            x0, [fp, #-0x18]
    // 0x922d20: r0 = GetPage()
    //     0x922d20: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922d24: mov             x1, x0
    // 0x922d28: ldur            x2, [fp, #-0x18]
    // 0x922d2c: ldur            x5, [fp, #-8]
    // 0x922d30: r3 = "/return-order"
    //     0x922d30: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x922d34: ldr             x3, [x3, #0x8b8]
    // 0x922d38: stur            x0, [fp, #-8]
    // 0x922d3c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922d3c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922d40: r0 = GetPage()
    //     0x922d40: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922d44: ldur            x1, [fp, #-0x10]
    // 0x922d48: ldur            x0, [fp, #-8]
    // 0x922d4c: ArrayStore: r1[12] = r0  ; List_4
    //     0x922d4c: add             x25, x1, #0x3f
    //     0x922d50: str             w0, [x25]
    //     0x922d54: tbz             w0, #0, #0x922d70
    //     0x922d58: ldurb           w16, [x1, #-1]
    //     0x922d5c: ldurb           w17, [x0, #-1]
    //     0x922d60: and             x16, x17, x16, lsr #2
    //     0x922d64: tst             x16, HEAP, lsr #32
    //     0x922d68: b.eq            #0x922d70
    //     0x922d6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922d70: r1 = Function '<anonymous closure>': static.
    //     0x922d70: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfa0] AnonymousClosure: static (0x9237c0), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922d74: ldr             x1, [x1, #0xfa0]
    // 0x922d78: r2 = Null
    //     0x922d78: mov             x2, NULL
    // 0x922d7c: r0 = AllocateClosure()
    //     0x922d7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922d80: stur            x0, [fp, #-8]
    // 0x922d84: r0 = ReturnOrderBinding()
    //     0x922d84: bl              #0x91cf60  ; AllocateReturnOrderBindingStub -> ReturnOrderBinding (size=0x8)
    // 0x922d88: r1 = Null
    //     0x922d88: mov             x1, NULL
    // 0x922d8c: stur            x0, [fp, #-0x18]
    // 0x922d90: r0 = GetPage()
    //     0x922d90: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922d94: mov             x1, x0
    // 0x922d98: ldur            x2, [fp, #-0x18]
    // 0x922d9c: ldur            x5, [fp, #-8]
    // 0x922da0: r3 = "/return-order-confirm"
    //     0x922da0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8c8] "/return-order-confirm"
    //     0x922da4: ldr             x3, [x3, #0x8c8]
    // 0x922da8: stur            x0, [fp, #-8]
    // 0x922dac: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922dac: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922db0: r0 = GetPage()
    //     0x922db0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922db4: ldur            x1, [fp, #-0x10]
    // 0x922db8: ldur            x0, [fp, #-8]
    // 0x922dbc: ArrayStore: r1[13] = r0  ; List_4
    //     0x922dbc: add             x25, x1, #0x43
    //     0x922dc0: str             w0, [x25]
    //     0x922dc4: tbz             w0, #0, #0x922de0
    //     0x922dc8: ldurb           w16, [x1, #-1]
    //     0x922dcc: ldurb           w17, [x0, #-1]
    //     0x922dd0: and             x16, x17, x16, lsr #2
    //     0x922dd4: tst             x16, HEAP, lsr #32
    //     0x922dd8: b.eq            #0x922de0
    //     0x922ddc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922de0: r1 = Function '<anonymous closure>': static.
    //     0x922de0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfa8] AnonymousClosure: static (0x9237b4), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922de4: ldr             x1, [x1, #0xfa8]
    // 0x922de8: r2 = Null
    //     0x922de8: mov             x2, NULL
    // 0x922dec: r0 = AllocateClosure()
    //     0x922dec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922df0: stur            x0, [fp, #-8]
    // 0x922df4: r0 = BrowseBinding()
    //     0x922df4: bl              #0x91cf54  ; AllocateBrowseBindingStub -> BrowseBinding (size=0x8)
    // 0x922df8: r1 = Null
    //     0x922df8: mov             x1, NULL
    // 0x922dfc: stur            x0, [fp, #-0x18]
    // 0x922e00: r0 = GetPage()
    //     0x922e00: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922e04: mov             x1, x0
    // 0x922e08: ldur            x2, [fp, #-0x18]
    // 0x922e0c: ldur            x5, [fp, #-8]
    // 0x922e10: r3 = "/browse"
    //     0x922e10: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8d8] "/browse"
    //     0x922e14: ldr             x3, [x3, #0x8d8]
    // 0x922e18: stur            x0, [fp, #-8]
    // 0x922e1c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922e1c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922e20: r0 = GetPage()
    //     0x922e20: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922e24: ldur            x1, [fp, #-0x10]
    // 0x922e28: ldur            x0, [fp, #-8]
    // 0x922e2c: ArrayStore: r1[14] = r0  ; List_4
    //     0x922e2c: add             x25, x1, #0x47
    //     0x922e30: str             w0, [x25]
    //     0x922e34: tbz             w0, #0, #0x922e50
    //     0x922e38: ldurb           w16, [x1, #-1]
    //     0x922e3c: ldurb           w17, [x0, #-1]
    //     0x922e40: and             x16, x17, x16, lsr #2
    //     0x922e44: tst             x16, HEAP, lsr #32
    //     0x922e48: b.eq            #0x922e50
    //     0x922e4c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922e50: r1 = Function '<anonymous closure>': static.
    //     0x922e50: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfb0] AnonymousClosure: static (0x9237a8), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922e54: ldr             x1, [x1, #0xfb0]
    // 0x922e58: r2 = Null
    //     0x922e58: mov             x2, NULL
    // 0x922e5c: r0 = AllocateClosure()
    //     0x922e5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922e60: stur            x0, [fp, #-8]
    // 0x922e64: r0 = EnlargeImageBinding()
    //     0x922e64: bl              #0x91cf48  ; AllocateEnlargeImageBindingStub -> EnlargeImageBinding (size=0x8)
    // 0x922e68: r1 = Null
    //     0x922e68: mov             x1, NULL
    // 0x922e6c: stur            x0, [fp, #-0x18]
    // 0x922e70: r0 = GetPage()
    //     0x922e70: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922e74: mov             x1, x0
    // 0x922e78: ldur            x2, [fp, #-0x18]
    // 0x922e7c: ldur            x5, [fp, #-8]
    // 0x922e80: r3 = "/enlarge_image_view"
    //     0x922e80: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8e8] "/enlarge_image_view"
    //     0x922e84: ldr             x3, [x3, #0x8e8]
    // 0x922e88: stur            x0, [fp, #-8]
    // 0x922e8c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922e8c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922e90: r0 = GetPage()
    //     0x922e90: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922e94: ldur            x1, [fp, #-0x10]
    // 0x922e98: ldur            x0, [fp, #-8]
    // 0x922e9c: ArrayStore: r1[15] = r0  ; List_4
    //     0x922e9c: add             x25, x1, #0x4b
    //     0x922ea0: str             w0, [x25]
    //     0x922ea4: tbz             w0, #0, #0x922ec0
    //     0x922ea8: ldurb           w16, [x1, #-1]
    //     0x922eac: ldurb           w17, [x0, #-1]
    //     0x922eb0: and             x16, x17, x16, lsr #2
    //     0x922eb4: tst             x16, HEAP, lsr #32
    //     0x922eb8: b.eq            #0x922ec0
    //     0x922ebc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922ec0: r1 = Function '<anonymous closure>': static.
    //     0x922ec0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfb8] AnonymousClosure: static (0x92379c), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922ec4: ldr             x1, [x1, #0xfb8]
    // 0x922ec8: r2 = Null
    //     0x922ec8: mov             x2, NULL
    // 0x922ecc: r0 = AllocateClosure()
    //     0x922ecc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922ed0: stur            x0, [fp, #-8]
    // 0x922ed4: r0 = ReplaceCallOrderBinding()
    //     0x922ed4: bl              #0x91cf3c  ; AllocateReplaceCallOrderBindingStub -> ReplaceCallOrderBinding (size=0x8)
    // 0x922ed8: r1 = Null
    //     0x922ed8: mov             x1, NULL
    // 0x922edc: stur            x0, [fp, #-0x18]
    // 0x922ee0: r0 = GetPage()
    //     0x922ee0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922ee4: mov             x1, x0
    // 0x922ee8: ldur            x2, [fp, #-0x18]
    // 0x922eec: ldur            x5, [fp, #-8]
    // 0x922ef0: r3 = "/replace_call_order"
    //     0x922ef0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8f8] "/replace_call_order"
    //     0x922ef4: ldr             x3, [x3, #0x8f8]
    // 0x922ef8: stur            x0, [fp, #-8]
    // 0x922efc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922efc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922f00: r0 = GetPage()
    //     0x922f00: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922f04: ldur            x1, [fp, #-0x10]
    // 0x922f08: ldur            x0, [fp, #-8]
    // 0x922f0c: ArrayStore: r1[16] = r0  ; List_4
    //     0x922f0c: add             x25, x1, #0x4f
    //     0x922f10: str             w0, [x25]
    //     0x922f14: tbz             w0, #0, #0x922f30
    //     0x922f18: ldurb           w16, [x1, #-1]
    //     0x922f1c: ldurb           w17, [x0, #-1]
    //     0x922f20: and             x16, x17, x16, lsr #2
    //     0x922f24: tst             x16, HEAP, lsr #32
    //     0x922f28: b.eq            #0x922f30
    //     0x922f2c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922f30: r1 = Function '<anonymous closure>': static.
    //     0x922f30: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfc0] AnonymousClosure: static (0x923790), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922f34: ldr             x1, [x1, #0xfc0]
    // 0x922f38: r2 = Null
    //     0x922f38: mov             x2, NULL
    // 0x922f3c: r0 = AllocateClosure()
    //     0x922f3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922f40: stur            x0, [fp, #-8]
    // 0x922f44: r0 = CheckoutPaymentMethodBinding()
    //     0x922f44: bl              #0x91cf30  ; AllocateCheckoutPaymentMethodBindingStub -> CheckoutPaymentMethodBinding (size=0x8)
    // 0x922f48: r1 = Null
    //     0x922f48: mov             x1, NULL
    // 0x922f4c: stur            x0, [fp, #-0x18]
    // 0x922f50: r0 = GetPage()
    //     0x922f50: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922f54: mov             x1, x0
    // 0x922f58: ldur            x2, [fp, #-0x18]
    // 0x922f5c: ldur            x5, [fp, #-8]
    // 0x922f60: r3 = "/cod_online_payment_methods"
    //     0x922f60: add             x3, PP, #0xd, lsl #12  ; [pp+0xd908] "/cod_online_payment_methods"
    //     0x922f64: ldr             x3, [x3, #0x908]
    // 0x922f68: stur            x0, [fp, #-8]
    // 0x922f6c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922f6c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922f70: r0 = GetPage()
    //     0x922f70: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922f74: ldur            x1, [fp, #-0x10]
    // 0x922f78: ldur            x0, [fp, #-8]
    // 0x922f7c: ArrayStore: r1[17] = r0  ; List_4
    //     0x922f7c: add             x25, x1, #0x53
    //     0x922f80: str             w0, [x25]
    //     0x922f84: tbz             w0, #0, #0x922fa0
    //     0x922f88: ldurb           w16, [x1, #-1]
    //     0x922f8c: ldurb           w17, [x0, #-1]
    //     0x922f90: and             x16, x17, x16, lsr #2
    //     0x922f94: tst             x16, HEAP, lsr #32
    //     0x922f98: b.eq            #0x922fa0
    //     0x922f9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x922fa0: r1 = Function '<anonymous closure>': static.
    //     0x922fa0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfc8] AnonymousClosure: static (0x923784), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x922fa4: ldr             x1, [x1, #0xfc8]
    // 0x922fa8: r2 = Null
    //     0x922fa8: mov             x2, NULL
    // 0x922fac: r0 = AllocateClosure()
    //     0x922fac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x922fb0: stur            x0, [fp, #-8]
    // 0x922fb4: r0 = OrderSuccessBindings()
    //     0x922fb4: bl              #0x91cf24  ; AllocateOrderSuccessBindingsStub -> OrderSuccessBindings (size=0x8)
    // 0x922fb8: r1 = Null
    //     0x922fb8: mov             x1, NULL
    // 0x922fbc: stur            x0, [fp, #-0x18]
    // 0x922fc0: r0 = GetPage()
    //     0x922fc0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x922fc4: mov             x1, x0
    // 0x922fc8: ldur            x2, [fp, #-0x18]
    // 0x922fcc: ldur            x5, [fp, #-8]
    // 0x922fd0: r3 = "/orderSuccess"
    //     0x922fd0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd918] "/orderSuccess"
    //     0x922fd4: ldr             x3, [x3, #0x918]
    // 0x922fd8: stur            x0, [fp, #-8]
    // 0x922fdc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x922fdc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x922fe0: r0 = GetPage()
    //     0x922fe0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x922fe4: ldur            x1, [fp, #-0x10]
    // 0x922fe8: ldur            x0, [fp, #-8]
    // 0x922fec: ArrayStore: r1[18] = r0  ; List_4
    //     0x922fec: add             x25, x1, #0x57
    //     0x922ff0: str             w0, [x25]
    //     0x922ff4: tbz             w0, #0, #0x923010
    //     0x922ff8: ldurb           w16, [x1, #-1]
    //     0x922ffc: ldurb           w17, [x0, #-1]
    //     0x923000: and             x16, x17, x16, lsr #2
    //     0x923004: tst             x16, HEAP, lsr #32
    //     0x923008: b.eq            #0x923010
    //     0x92300c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923010: r1 = Function '<anonymous closure>': static.
    //     0x923010: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfd0] AnonymousClosure: static (0x923778), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923014: ldr             x1, [x1, #0xfd0]
    // 0x923018: r2 = Null
    //     0x923018: mov             x2, NULL
    // 0x92301c: r0 = AllocateClosure()
    //     0x92301c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923020: stur            x0, [fp, #-8]
    // 0x923024: r0 = OrderFailureBindings()
    //     0x923024: bl              #0x91cf18  ; AllocateOrderFailureBindingsStub -> OrderFailureBindings (size=0x8)
    // 0x923028: r1 = Null
    //     0x923028: mov             x1, NULL
    // 0x92302c: stur            x0, [fp, #-0x18]
    // 0x923030: r0 = GetPage()
    //     0x923030: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923034: mov             x1, x0
    // 0x923038: ldur            x2, [fp, #-0x18]
    // 0x92303c: ldur            x5, [fp, #-8]
    // 0x923040: r3 = "/order_failure"
    //     0x923040: add             x3, PP, #0xd, lsl #12  ; [pp+0xd928] "/order_failure"
    //     0x923044: ldr             x3, [x3, #0x928]
    // 0x923048: stur            x0, [fp, #-8]
    // 0x92304c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92304c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923050: r0 = GetPage()
    //     0x923050: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923054: ldur            x1, [fp, #-0x10]
    // 0x923058: ldur            x0, [fp, #-8]
    // 0x92305c: ArrayStore: r1[19] = r0  ; List_4
    //     0x92305c: add             x25, x1, #0x5b
    //     0x923060: str             w0, [x25]
    //     0x923064: tbz             w0, #0, #0x923080
    //     0x923068: ldurb           w16, [x1, #-1]
    //     0x92306c: ldurb           w17, [x0, #-1]
    //     0x923070: and             x16, x17, x16, lsr #2
    //     0x923074: tst             x16, HEAP, lsr #32
    //     0x923078: b.eq            #0x923080
    //     0x92307c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923080: r1 = Function '<anonymous closure>': static.
    //     0x923080: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfd8] AnonymousClosure: static (0x92376c), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923084: ldr             x1, [x1, #0xfd8]
    // 0x923088: r2 = Null
    //     0x923088: mov             x2, NULL
    // 0x92308c: r0 = AllocateClosure()
    //     0x92308c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923090: stur            x0, [fp, #-8]
    // 0x923094: r0 = PoliciesBinding()
    //     0x923094: bl              #0x91cf0c  ; AllocatePoliciesBindingStub -> PoliciesBinding (size=0x8)
    // 0x923098: r1 = Null
    //     0x923098: mov             x1, NULL
    // 0x92309c: stur            x0, [fp, #-0x18]
    // 0x9230a0: r0 = GetPage()
    //     0x9230a0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9230a4: mov             x1, x0
    // 0x9230a8: ldur            x2, [fp, #-0x18]
    // 0x9230ac: ldur            x5, [fp, #-8]
    // 0x9230b0: r3 = "/policies"
    //     0x9230b0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd938] "/policies"
    //     0x9230b4: ldr             x3, [x3, #0x938]
    // 0x9230b8: stur            x0, [fp, #-8]
    // 0x9230bc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9230bc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9230c0: r0 = GetPage()
    //     0x9230c0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9230c4: ldur            x1, [fp, #-0x10]
    // 0x9230c8: ldur            x0, [fp, #-8]
    // 0x9230cc: ArrayStore: r1[20] = r0  ; List_4
    //     0x9230cc: add             x25, x1, #0x5f
    //     0x9230d0: str             w0, [x25]
    //     0x9230d4: tbz             w0, #0, #0x9230f0
    //     0x9230d8: ldurb           w16, [x1, #-1]
    //     0x9230dc: ldurb           w17, [x0, #-1]
    //     0x9230e0: and             x16, x17, x16, lsr #2
    //     0x9230e4: tst             x16, HEAP, lsr #32
    //     0x9230e8: b.eq            #0x9230f0
    //     0x9230ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9230f0: r1 = Function '<anonymous closure>': static.
    //     0x9230f0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfe0] AnonymousClosure: static (0x923760), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9230f4: ldr             x1, [x1, #0xfe0]
    // 0x9230f8: r2 = Null
    //     0x9230f8: mov             x2, NULL
    // 0x9230fc: r0 = AllocateClosure()
    //     0x9230fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923100: stur            x0, [fp, #-8]
    // 0x923104: r0 = ViewAllReviewsBinding()
    //     0x923104: bl              #0x91cf00  ; AllocateViewAllReviewsBindingStub -> ViewAllReviewsBinding (size=0x8)
    // 0x923108: r1 = Null
    //     0x923108: mov             x1, NULL
    // 0x92310c: stur            x0, [fp, #-0x18]
    // 0x923110: r0 = GetPage()
    //     0x923110: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923114: mov             x1, x0
    // 0x923118: ldur            x2, [fp, #-0x18]
    // 0x92311c: ldur            x5, [fp, #-8]
    // 0x923120: r3 = "/all-reviews"
    //     0x923120: add             x3, PP, #0xd, lsl #12  ; [pp+0xd948] "/all-reviews"
    //     0x923124: ldr             x3, [x3, #0x948]
    // 0x923128: stur            x0, [fp, #-8]
    // 0x92312c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92312c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923130: r0 = GetPage()
    //     0x923130: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923134: ldur            x1, [fp, #-0x10]
    // 0x923138: ldur            x0, [fp, #-8]
    // 0x92313c: ArrayStore: r1[21] = r0  ; List_4
    //     0x92313c: add             x25, x1, #0x63
    //     0x923140: str             w0, [x25]
    //     0x923144: tbz             w0, #0, #0x923160
    //     0x923148: ldurb           w16, [x1, #-1]
    //     0x92314c: ldurb           w17, [x0, #-1]
    //     0x923150: and             x16, x17, x16, lsr #2
    //     0x923154: tst             x16, HEAP, lsr #32
    //     0x923158: b.eq            #0x923160
    //     0x92315c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923160: r1 = Function '<anonymous closure>': static.
    //     0x923160: add             x1, PP, #0xd, lsl #12  ; [pp+0xdfe8] AnonymousClosure: static (0x923754), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923164: ldr             x1, [x1, #0xfe8]
    // 0x923168: r2 = Null
    //     0x923168: mov             x2, NULL
    // 0x92316c: r0 = AllocateClosure()
    //     0x92316c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923170: stur            x0, [fp, #-8]
    // 0x923174: r0 = ContactUsBinding()
    //     0x923174: bl              #0x91cef4  ; AllocateContactUsBindingStub -> ContactUsBinding (size=0x8)
    // 0x923178: r1 = Null
    //     0x923178: mov             x1, NULL
    // 0x92317c: stur            x0, [fp, #-0x18]
    // 0x923180: r0 = GetPage()
    //     0x923180: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923184: mov             x1, x0
    // 0x923188: ldur            x2, [fp, #-0x18]
    // 0x92318c: ldur            x5, [fp, #-8]
    // 0x923190: r3 = "/contact-us"
    //     0x923190: add             x3, PP, #0xd, lsl #12  ; [pp+0xd958] "/contact-us"
    //     0x923194: ldr             x3, [x3, #0x958]
    // 0x923198: stur            x0, [fp, #-8]
    // 0x92319c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92319c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9231a0: r0 = GetPage()
    //     0x9231a0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9231a4: ldur            x1, [fp, #-0x10]
    // 0x9231a8: ldur            x0, [fp, #-8]
    // 0x9231ac: ArrayStore: r1[22] = r0  ; List_4
    //     0x9231ac: add             x25, x1, #0x67
    //     0x9231b0: str             w0, [x25]
    //     0x9231b4: tbz             w0, #0, #0x9231d0
    //     0x9231b8: ldurb           w16, [x1, #-1]
    //     0x9231bc: ldurb           w17, [x0, #-1]
    //     0x9231c0: and             x16, x17, x16, lsr #2
    //     0x9231c4: tst             x16, HEAP, lsr #32
    //     0x9231c8: b.eq            #0x9231d0
    //     0x9231cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9231d0: r1 = Function '<anonymous closure>': static.
    //     0x9231d0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdff0] AnonymousClosure: static (0x923748), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9231d4: ldr             x1, [x1, #0xff0]
    // 0x9231d8: r2 = Null
    //     0x9231d8: mov             x2, NULL
    // 0x9231dc: r0 = AllocateClosure()
    //     0x9231dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9231e0: stur            x0, [fp, #-8]
    // 0x9231e4: r0 = ExchangeBinding()
    //     0x9231e4: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x9231e8: r1 = Null
    //     0x9231e8: mov             x1, NULL
    // 0x9231ec: stur            x0, [fp, #-0x18]
    // 0x9231f0: r0 = GetPage()
    //     0x9231f0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9231f4: mov             x1, x0
    // 0x9231f8: ldur            x2, [fp, #-0x18]
    // 0x9231fc: ldur            x5, [fp, #-8]
    // 0x923200: r3 = "/exchange-return-intermediate"
    //     0x923200: add             x3, PP, #0xd, lsl #12  ; [pp+0xd968] "/exchange-return-intermediate"
    //     0x923204: ldr             x3, [x3, #0x968]
    // 0x923208: stur            x0, [fp, #-8]
    // 0x92320c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92320c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923210: r0 = GetPage()
    //     0x923210: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923214: ldur            x1, [fp, #-0x10]
    // 0x923218: ldur            x0, [fp, #-8]
    // 0x92321c: ArrayStore: r1[23] = r0  ; List_4
    //     0x92321c: add             x25, x1, #0x6b
    //     0x923220: str             w0, [x25]
    //     0x923224: tbz             w0, #0, #0x923240
    //     0x923228: ldurb           w16, [x1, #-1]
    //     0x92322c: ldurb           w17, [x0, #-1]
    //     0x923230: and             x16, x17, x16, lsr #2
    //     0x923234: tst             x16, HEAP, lsr #32
    //     0x923238: b.eq            #0x923240
    //     0x92323c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923240: r1 = Function '<anonymous closure>': static.
    //     0x923240: add             x1, PP, #0xd, lsl #12  ; [pp+0xdff8] AnonymousClosure: static (0x92373c), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923244: ldr             x1, [x1, #0xff8]
    // 0x923248: r2 = Null
    //     0x923248: mov             x2, NULL
    // 0x92324c: r0 = AllocateClosure()
    //     0x92324c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923250: stur            x0, [fp, #-8]
    // 0x923254: r0 = ExchangeBinding()
    //     0x923254: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x923258: r1 = Null
    //     0x923258: mov             x1, NULL
    // 0x92325c: stur            x0, [fp, #-0x18]
    // 0x923260: r0 = GetPage()
    //     0x923260: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923264: mov             x1, x0
    // 0x923268: ldur            x2, [fp, #-0x18]
    // 0x92326c: ldur            x5, [fp, #-8]
    // 0x923270: r3 = "/exchange-product-skus"
    //     0x923270: add             x3, PP, #0xd, lsl #12  ; [pp+0xd978] "/exchange-product-skus"
    //     0x923274: ldr             x3, [x3, #0x978]
    // 0x923278: stur            x0, [fp, #-8]
    // 0x92327c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92327c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923280: r0 = GetPage()
    //     0x923280: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923284: ldur            x1, [fp, #-0x10]
    // 0x923288: ldur            x0, [fp, #-8]
    // 0x92328c: ArrayStore: r1[24] = r0  ; List_4
    //     0x92328c: add             x25, x1, #0x6f
    //     0x923290: str             w0, [x25]
    //     0x923294: tbz             w0, #0, #0x9232b0
    //     0x923298: ldurb           w16, [x1, #-1]
    //     0x92329c: ldurb           w17, [x0, #-1]
    //     0x9232a0: and             x16, x17, x16, lsr #2
    //     0x9232a4: tst             x16, HEAP, lsr #32
    //     0x9232a8: b.eq            #0x9232b0
    //     0x9232ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9232b0: r1 = Function '<anonymous closure>': static.
    //     0x9232b0: add             x1, PP, #0xe, lsl #12  ; [pp+0xe000] AnonymousClosure: static (0x923730), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9232b4: ldr             x1, [x1]
    // 0x9232b8: r2 = Null
    //     0x9232b8: mov             x2, NULL
    // 0x9232bc: r0 = AllocateClosure()
    //     0x9232bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9232c0: stur            x0, [fp, #-8]
    // 0x9232c4: r0 = ExchangeBinding()
    //     0x9232c4: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x9232c8: r1 = Null
    //     0x9232c8: mov             x1, NULL
    // 0x9232cc: stur            x0, [fp, #-0x18]
    // 0x9232d0: r0 = GetPage()
    //     0x9232d0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9232d4: mov             x1, x0
    // 0x9232d8: ldur            x2, [fp, #-0x18]
    // 0x9232dc: ldur            x5, [fp, #-8]
    // 0x9232e0: r3 = "/exchange-checkout"
    //     0x9232e0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd988] "/exchange-checkout"
    //     0x9232e4: ldr             x3, [x3, #0x988]
    // 0x9232e8: stur            x0, [fp, #-8]
    // 0x9232ec: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9232ec: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9232f0: r0 = GetPage()
    //     0x9232f0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9232f4: ldur            x1, [fp, #-0x10]
    // 0x9232f8: ldur            x0, [fp, #-8]
    // 0x9232fc: ArrayStore: r1[25] = r0  ; List_4
    //     0x9232fc: add             x25, x1, #0x73
    //     0x923300: str             w0, [x25]
    //     0x923304: tbz             w0, #0, #0x923320
    //     0x923308: ldurb           w16, [x1, #-1]
    //     0x92330c: ldurb           w17, [x0, #-1]
    //     0x923310: and             x16, x17, x16, lsr #2
    //     0x923314: tst             x16, HEAP, lsr #32
    //     0x923318: b.eq            #0x923320
    //     0x92331c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923320: r1 = Function '<anonymous closure>': static.
    //     0x923320: add             x1, PP, #0xe, lsl #12  ; [pp+0xe008] AnonymousClosure: static (0x923724), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923324: ldr             x1, [x1, #8]
    // 0x923328: r2 = Null
    //     0x923328: mov             x2, NULL
    // 0x92332c: r0 = AllocateClosure()
    //     0x92332c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923330: stur            x0, [fp, #-8]
    // 0x923334: r0 = ExchangeBinding()
    //     0x923334: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x923338: r1 = Null
    //     0x923338: mov             x1, NULL
    // 0x92333c: stur            x0, [fp, #-0x18]
    // 0x923340: r0 = GetPage()
    //     0x923340: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923344: mov             x1, x0
    // 0x923348: ldur            x2, [fp, #-0x18]
    // 0x92334c: ldur            x5, [fp, #-8]
    // 0x923350: r3 = "/exchange_online_payment_methods"
    //     0x923350: add             x3, PP, #0xd, lsl #12  ; [pp+0xd998] "/exchange_online_payment_methods"
    //     0x923354: ldr             x3, [x3, #0x998]
    // 0x923358: stur            x0, [fp, #-8]
    // 0x92335c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92335c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923360: r0 = GetPage()
    //     0x923360: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923364: ldur            x1, [fp, #-0x10]
    // 0x923368: ldur            x0, [fp, #-8]
    // 0x92336c: ArrayStore: r1[26] = r0  ; List_4
    //     0x92336c: add             x25, x1, #0x77
    //     0x923370: str             w0, [x25]
    //     0x923374: tbz             w0, #0, #0x923390
    //     0x923378: ldurb           w16, [x1, #-1]
    //     0x92337c: ldurb           w17, [x0, #-1]
    //     0x923380: and             x16, x17, x16, lsr #2
    //     0x923384: tst             x16, HEAP, lsr #32
    //     0x923388: b.eq            #0x923390
    //     0x92338c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923390: r1 = Function '<anonymous closure>': static.
    //     0x923390: add             x1, PP, #0xe, lsl #12  ; [pp+0xe010] AnonymousClosure: static (0x923718), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923394: ldr             x1, [x1, #0x10]
    // 0x923398: r2 = Null
    //     0x923398: mov             x2, NULL
    // 0x92339c: r0 = AllocateClosure()
    //     0x92339c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9233a0: stur            x0, [fp, #-8]
    // 0x9233a4: r0 = CheckoutVariantsBindings()
    //     0x9233a4: bl              #0x91cedc  ; AllocateCheckoutVariantsBindingsStub -> CheckoutVariantsBindings (size=0x8)
    // 0x9233a8: r1 = Null
    //     0x9233a8: mov             x1, NULL
    // 0x9233ac: stur            x0, [fp, #-0x18]
    // 0x9233b0: r0 = GetPage()
    //     0x9233b0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9233b4: mov             x1, x0
    // 0x9233b8: ldur            x2, [fp, #-0x18]
    // 0x9233bc: ldur            x5, [fp, #-8]
    // 0x9233c0: r3 = "/checkout_variants"
    //     0x9233c0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9a8] "/checkout_variants"
    //     0x9233c4: ldr             x3, [x3, #0x9a8]
    // 0x9233c8: stur            x0, [fp, #-8]
    // 0x9233cc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9233cc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9233d0: r0 = GetPage()
    //     0x9233d0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9233d4: ldur            x1, [fp, #-0x10]
    // 0x9233d8: ldur            x0, [fp, #-8]
    // 0x9233dc: ArrayStore: r1[27] = r0  ; List_4
    //     0x9233dc: add             x25, x1, #0x7b
    //     0x9233e0: str             w0, [x25]
    //     0x9233e4: tbz             w0, #0, #0x923400
    //     0x9233e8: ldurb           w16, [x1, #-1]
    //     0x9233ec: ldurb           w17, [x0, #-1]
    //     0x9233f0: and             x16, x17, x16, lsr #2
    //     0x9233f4: tst             x16, HEAP, lsr #32
    //     0x9233f8: b.eq            #0x923400
    //     0x9233fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923400: r1 = Function '<anonymous closure>': static.
    //     0x923400: add             x1, PP, #0xe, lsl #12  ; [pp+0xe018] AnonymousClosure: static (0x92370c), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923404: ldr             x1, [x1, #0x18]
    // 0x923408: r2 = Null
    //     0x923408: mov             x2, NULL
    // 0x92340c: r0 = AllocateClosure()
    //     0x92340c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923410: stur            x0, [fp, #-8]
    // 0x923414: r0 = RatingReviewOrderBinding()
    //     0x923414: bl              #0x91ced0  ; AllocateRatingReviewOrderBindingStub -> RatingReviewOrderBinding (size=0x8)
    // 0x923418: r1 = Null
    //     0x923418: mov             x1, NULL
    // 0x92341c: stur            x0, [fp, #-0x18]
    // 0x923420: r0 = GetPage()
    //     0x923420: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923424: mov             x1, x0
    // 0x923428: ldur            x2, [fp, #-0x18]
    // 0x92342c: ldur            x5, [fp, #-8]
    // 0x923430: r3 = "/rating_review_for_order"
    //     0x923430: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9b8] "/rating_review_for_order"
    //     0x923434: ldr             x3, [x3, #0x9b8]
    // 0x923438: stur            x0, [fp, #-8]
    // 0x92343c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92343c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923440: r0 = GetPage()
    //     0x923440: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923444: ldur            x1, [fp, #-0x10]
    // 0x923448: ldur            x0, [fp, #-8]
    // 0x92344c: ArrayStore: r1[28] = r0  ; List_4
    //     0x92344c: add             x25, x1, #0x7f
    //     0x923450: str             w0, [x25]
    //     0x923454: tbz             w0, #0, #0x923470
    //     0x923458: ldurb           w16, [x1, #-1]
    //     0x92345c: ldurb           w17, [x0, #-1]
    //     0x923460: and             x16, x17, x16, lsr #2
    //     0x923464: tst             x16, HEAP, lsr #32
    //     0x923468: b.eq            #0x923470
    //     0x92346c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923470: r1 = Function '<anonymous closure>': static.
    //     0x923470: add             x1, PP, #0xe, lsl #12  ; [pp+0xe020] AnonymousClosure: static (0x923700), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923474: ldr             x1, [x1, #0x20]
    // 0x923478: r2 = Null
    //     0x923478: mov             x2, NULL
    // 0x92347c: r0 = AllocateClosure()
    //     0x92347c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923480: stur            x0, [fp, #-8]
    // 0x923484: r0 = RatingReviewBinding()
    //     0x923484: bl              #0x91cec4  ; AllocateRatingReviewBindingStub -> RatingReviewBinding (size=0x8)
    // 0x923488: r1 = Null
    //     0x923488: mov             x1, NULL
    // 0x92348c: stur            x0, [fp, #-0x18]
    // 0x923490: r0 = GetPage()
    //     0x923490: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923494: mov             x1, x0
    // 0x923498: ldur            x2, [fp, #-0x18]
    // 0x92349c: ldur            x5, [fp, #-8]
    // 0x9234a0: r3 = "/rating_review_media_screen"
    //     0x9234a0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9c8] "/rating_review_media_screen"
    //     0x9234a4: ldr             x3, [x3, #0x9c8]
    // 0x9234a8: stur            x0, [fp, #-8]
    // 0x9234ac: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9234ac: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x9234b0: r0 = GetPage()
    //     0x9234b0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x9234b4: ldur            x1, [fp, #-0x10]
    // 0x9234b8: ldur            x0, [fp, #-8]
    // 0x9234bc: ArrayStore: r1[29] = r0  ; List_4
    //     0x9234bc: add             x25, x1, #0x83
    //     0x9234c0: str             w0, [x25]
    //     0x9234c4: tbz             w0, #0, #0x9234e0
    //     0x9234c8: ldurb           w16, [x1, #-1]
    //     0x9234cc: ldurb           w17, [x0, #-1]
    //     0x9234d0: and             x16, x17, x16, lsr #2
    //     0x9234d4: tst             x16, HEAP, lsr #32
    //     0x9234d8: b.eq            #0x9234e0
    //     0x9234dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9234e0: r1 = Function '<anonymous closure>': static.
    //     0x9234e0: add             x1, PP, #0xe, lsl #12  ; [pp+0xe028] AnonymousClosure: static (0x9236f4), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9234e4: ldr             x1, [x1, #0x28]
    // 0x9234e8: r2 = Null
    //     0x9234e8: mov             x2, NULL
    // 0x9234ec: r0 = AllocateClosure()
    //     0x9234ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9234f0: stur            x0, [fp, #-8]
    // 0x9234f4: r0 = CheckoutOrderSummaryBinding()
    //     0x9234f4: bl              #0x91ceb8  ; AllocateCheckoutOrderSummaryBindingStub -> CheckoutOrderSummaryBinding (size=0x8)
    // 0x9234f8: r1 = Null
    //     0x9234f8: mov             x1, NULL
    // 0x9234fc: stur            x0, [fp, #-0x18]
    // 0x923500: r0 = GetPage()
    //     0x923500: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923504: mov             x1, x0
    // 0x923508: ldur            x2, [fp, #-0x18]
    // 0x92350c: ldur            x5, [fp, #-8]
    // 0x923510: r3 = "/checkout_order_summary_page"
    //     0x923510: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x923514: ldr             x3, [x3, #0x9d8]
    // 0x923518: stur            x0, [fp, #-8]
    // 0x92351c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92351c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923520: r0 = GetPage()
    //     0x923520: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923524: ldur            x1, [fp, #-0x10]
    // 0x923528: ldur            x0, [fp, #-8]
    // 0x92352c: ArrayStore: r1[30] = r0  ; List_4
    //     0x92352c: add             x25, x1, #0x87
    //     0x923530: str             w0, [x25]
    //     0x923534: tbz             w0, #0, #0x923550
    //     0x923538: ldurb           w16, [x1, #-1]
    //     0x92353c: ldurb           w17, [x0, #-1]
    //     0x923540: and             x16, x17, x16, lsr #2
    //     0x923544: tst             x16, HEAP, lsr #32
    //     0x923548: b.eq            #0x923550
    //     0x92354c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923550: r1 = Function '<anonymous closure>': static.
    //     0x923550: add             x1, PP, #0xe, lsl #12  ; [pp+0xe030] AnonymousClosure: static (0x9236e8), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923554: ldr             x1, [x1, #0x30]
    // 0x923558: r2 = Null
    //     0x923558: mov             x2, NULL
    // 0x92355c: r0 = AllocateClosure()
    //     0x92355c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923560: stur            x0, [fp, #-8]
    // 0x923564: r0 = CheckoutRequestAddressBinding()
    //     0x923564: bl              #0x91ceac  ; AllocateCheckoutRequestAddressBindingStub -> CheckoutRequestAddressBinding (size=0x8)
    // 0x923568: r1 = Null
    //     0x923568: mov             x1, NULL
    // 0x92356c: stur            x0, [fp, #-0x18]
    // 0x923570: r0 = GetPage()
    //     0x923570: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923574: mov             x1, x0
    // 0x923578: ldur            x2, [fp, #-0x18]
    // 0x92357c: ldur            x5, [fp, #-8]
    // 0x923580: r3 = "/checkout_request_address_page"
    //     0x923580: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0x923584: ldr             x3, [x3, #0x9e8]
    // 0x923588: stur            x0, [fp, #-8]
    // 0x92358c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92358c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923590: r0 = GetPage()
    //     0x923590: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923594: ldur            x1, [fp, #-0x10]
    // 0x923598: ldur            x0, [fp, #-8]
    // 0x92359c: ArrayStore: r1[31] = r0  ; List_4
    //     0x92359c: add             x25, x1, #0x8b
    //     0x9235a0: str             w0, [x25]
    //     0x9235a4: tbz             w0, #0, #0x9235c0
    //     0x9235a8: ldurb           w16, [x1, #-1]
    //     0x9235ac: ldurb           w17, [x0, #-1]
    //     0x9235b0: and             x16, x17, x16, lsr #2
    //     0x9235b4: tst             x16, HEAP, lsr #32
    //     0x9235b8: b.eq            #0x9235c0
    //     0x9235bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9235c0: r1 = Function '<anonymous closure>': static.
    //     0x9235c0: add             x1, PP, #0xe, lsl #12  ; [pp+0xe038] AnonymousClosure: static (0x9236dc), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x9235c4: ldr             x1, [x1, #0x38]
    // 0x9235c8: r2 = Null
    //     0x9235c8: mov             x2, NULL
    // 0x9235cc: r0 = AllocateClosure()
    //     0x9235cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9235d0: stur            x0, [fp, #-8]
    // 0x9235d4: r0 = CheckoutRequestNumberBinding()
    //     0x9235d4: bl              #0x91cea0  ; AllocateCheckoutRequestNumberBindingStub -> CheckoutRequestNumberBinding (size=0x8)
    // 0x9235d8: r1 = Null
    //     0x9235d8: mov             x1, NULL
    // 0x9235dc: stur            x0, [fp, #-0x18]
    // 0x9235e0: r0 = GetPage()
    //     0x9235e0: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x9235e4: mov             x1, x0
    // 0x9235e8: ldur            x2, [fp, #-0x18]
    // 0x9235ec: ldur            x5, [fp, #-8]
    // 0x9235f0: r3 = "/checkout_request_number_page"
    //     0x9235f0: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x9235f4: ldr             x3, [x3, #0x9f8]
    // 0x9235f8: stur            x0, [fp, #-8]
    // 0x9235fc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x9235fc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923600: r0 = GetPage()
    //     0x923600: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923604: ldur            x1, [fp, #-0x10]
    // 0x923608: ldur            x0, [fp, #-8]
    // 0x92360c: ArrayStore: r1[32] = r0  ; List_4
    //     0x92360c: add             x25, x1, #0x8f
    //     0x923610: str             w0, [x25]
    //     0x923614: tbz             w0, #0, #0x923630
    //     0x923618: ldurb           w16, [x1, #-1]
    //     0x92361c: ldurb           w17, [x0, #-1]
    //     0x923620: and             x16, x17, x16, lsr #2
    //     0x923624: tst             x16, HEAP, lsr #32
    //     0x923628: b.eq            #0x923630
    //     0x92362c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x923630: r1 = Function '<anonymous closure>': static.
    //     0x923630: add             x1, PP, #0xe, lsl #12  ; [pp+0xe040] AnonymousClosure: static (0x9236d0), in [package:customer_app/app/routes/line.dart] LineAppPages::linePages (0x9227b8)
    //     0x923634: ldr             x1, [x1, #0x40]
    // 0x923638: r2 = Null
    //     0x923638: mov             x2, NULL
    // 0x92363c: r0 = AllocateClosure()
    //     0x92363c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x923640: stur            x0, [fp, #-8]
    // 0x923644: r0 = CheckoutRequestOtpBinding()
    //     0x923644: bl              #0x91ce94  ; AllocateCheckoutRequestOtpBindingStub -> CheckoutRequestOtpBinding (size=0x8)
    // 0x923648: r1 = Null
    //     0x923648: mov             x1, NULL
    // 0x92364c: stur            x0, [fp, #-0x18]
    // 0x923650: r0 = GetPage()
    //     0x923650: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x923654: mov             x1, x0
    // 0x923658: ldur            x2, [fp, #-0x18]
    // 0x92365c: ldur            x5, [fp, #-8]
    // 0x923660: r3 = "/checkout_request_otp_page"
    //     0x923660: add             x3, PP, #0xd, lsl #12  ; [pp+0xda08] "/checkout_request_otp_page"
    //     0x923664: ldr             x3, [x3, #0xa08]
    // 0x923668: stur            x0, [fp, #-8]
    // 0x92366c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x92366c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x923670: r0 = GetPage()
    //     0x923670: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x923674: ldur            x1, [fp, #-0x10]
    // 0x923678: ldur            x0, [fp, #-8]
    // 0x92367c: ArrayStore: r1[33] = r0  ; List_4
    //     0x92367c: add             x25, x1, #0x93
    //     0x923680: str             w0, [x25]
    //     0x923684: tbz             w0, #0, #0x9236a0
    //     0x923688: ldurb           w16, [x1, #-1]
    //     0x92368c: ldurb           w17, [x0, #-1]
    //     0x923690: and             x16, x17, x16, lsr #2
    //     0x923694: tst             x16, HEAP, lsr #32
    //     0x923698: b.eq            #0x9236a0
    //     0x92369c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9236a0: r1 = <GetPage>
    //     0x9236a0: add             x1, PP, #0xb, lsl #12  ; [pp+0xba40] TypeArguments: <GetPage>
    //     0x9236a4: ldr             x1, [x1, #0xa40]
    // 0x9236a8: r0 = AllocateGrowableArray()
    //     0x9236a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9236ac: ldur            x1, [fp, #-0x10]
    // 0x9236b0: StoreField: r0->field_f = r1
    //     0x9236b0: stur            w1, [x0, #0xf]
    // 0x9236b4: r1 = 68
    //     0x9236b4: movz            x1, #0x44
    // 0x9236b8: StoreField: r0->field_b = r1
    //     0x9236b8: stur            w1, [x0, #0xb]
    // 0x9236bc: LeaveFrame
    //     0x9236bc: mov             SP, fp
    //     0x9236c0: ldp             fp, lr, [SP], #0x10
    // 0x9236c4: ret
    //     0x9236c4: ret             
    // 0x9236c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9236c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9236cc: b               #0x9227d0
  }
  [closure] static CheckoutRequestOtpPage <anonymous closure>(dynamic) {
    // ** addr: 0x9236d0, size: 0xc
    // 0x9236d0: r0 = Instance_CheckoutRequestOtpPage
    //     0x9236d0: add             x0, PP, #0xe, lsl #12  ; [pp+0xe048] Obj!CheckoutRequestOtpPage@d670a1
    //     0x9236d4: ldr             x0, [x0, #0x48]
    // 0x9236d8: ret
    //     0x9236d8: ret             
  }
  [closure] static CheckoutRequestNumberPage <anonymous closure>(dynamic) {
    // ** addr: 0x9236dc, size: 0xc
    // 0x9236dc: r0 = Instance_CheckoutRequestNumberPage
    //     0x9236dc: add             x0, PP, #0xe, lsl #12  ; [pp+0xe050] Obj!CheckoutRequestNumberPage@d670c1
    //     0x9236e0: ldr             x0, [x0, #0x50]
    // 0x9236e4: ret
    //     0x9236e4: ret             
  }
  [closure] static CheckoutRequestAddressPage <anonymous closure>(dynamic) {
    // ** addr: 0x9236e8, size: 0xc
    // 0x9236e8: r0 = Instance_CheckoutRequestAddressPage
    //     0x9236e8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe058] Obj!CheckoutRequestAddressPage@d670e1
    //     0x9236ec: ldr             x0, [x0, #0x58]
    // 0x9236f0: ret
    //     0x9236f0: ret             
  }
  [closure] static CheckoutOrderSummaryPage <anonymous closure>(dynamic) {
    // ** addr: 0x9236f4, size: 0xc
    // 0x9236f4: r0 = Instance_CheckoutOrderSummaryPage
    //     0x9236f4: add             x0, PP, #0xe, lsl #12  ; [pp+0xe060] Obj!CheckoutOrderSummaryPage@d67101
    //     0x9236f8: ldr             x0, [x0, #0x60]
    // 0x9236fc: ret
    //     0x9236fc: ret             
  }
  [closure] static RatingReviewMediaScreen <anonymous closure>(dynamic) {
    // ** addr: 0x923700, size: 0xc
    // 0x923700: r0 = Instance_RatingReviewMediaScreen
    //     0x923700: add             x0, PP, #0xe, lsl #12  ; [pp+0xe068] Obj!RatingReviewMediaScreen@d66de1
    //     0x923704: ldr             x0, [x0, #0x68]
    // 0x923708: ret
    //     0x923708: ret             
  }
  [closure] static RatingReviewOrderPage <anonymous closure>(dynamic) {
    // ** addr: 0x92370c, size: 0xc
    // 0x92370c: r0 = Instance_RatingReviewOrderPage
    //     0x92370c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe070] Obj!RatingReviewOrderPage@d66f21
    //     0x923710: ldr             x0, [x0, #0x70]
    // 0x923714: ret
    //     0x923714: ret             
  }
  [closure] static CheckoutVariantsView <anonymous closure>(dynamic) {
    // ** addr: 0x923718, size: 0xc
    // 0x923718: r0 = Instance_CheckoutVariantsView
    //     0x923718: add             x0, PP, #0xe, lsl #12  ; [pp+0xe078] Obj!CheckoutVariantsView@d67081
    //     0x92371c: ldr             x0, [x0, #0x78]
    // 0x923720: ret
    //     0x923720: ret             
  }
  [closure] static ExchangeCheckoutOnlinePaymentMethod <anonymous closure>(dynamic) {
    // ** addr: 0x923724, size: 0xc
    // 0x923724: r0 = Instance_ExchangeCheckoutOnlinePaymentMethod
    //     0x923724: add             x0, PP, #0xe, lsl #12  ; [pp+0xe080] Obj!ExchangeCheckoutOnlinePaymentMethod@d67001
    //     0x923728: ldr             x0, [x0, #0x80]
    // 0x92372c: ret
    //     0x92372c: ret             
  }
  [closure] static ExchangeCheckoutScreen <anonymous closure>(dynamic) {
    // ** addr: 0x923730, size: 0xc
    // 0x923730: r0 = Instance_ExchangeCheckoutScreen
    //     0x923730: add             x0, PP, #0xe, lsl #12  ; [pp+0xe088] Obj!ExchangeCheckoutScreen@d66fe1
    //     0x923734: ldr             x0, [x0, #0x88]
    // 0x923738: ret
    //     0x923738: ret             
  }
  [closure] static ExchangeProductSkusScreen <anonymous closure>(dynamic) {
    // ** addr: 0x92373c, size: 0xc
    // 0x92373c: r0 = Instance_ExchangeProductSkusScreen
    //     0x92373c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe090] Obj!ExchangeProductSkusScreen@d66fc1
    //     0x923740: ldr             x0, [x0, #0x90]
    // 0x923744: ret
    //     0x923744: ret             
  }
  [closure] static ExchangeReturnIntermediateScreen <anonymous closure>(dynamic) {
    // ** addr: 0x923748, size: 0xc
    // 0x923748: r0 = Instance_ExchangeReturnIntermediateScreen
    //     0x923748: add             x0, PP, #0xe, lsl #12  ; [pp+0xe098] Obj!ExchangeReturnIntermediateScreen@d66fa1
    //     0x92374c: ldr             x0, [x0, #0x98]
    // 0x923750: ret
    //     0x923750: ret             
  }
  [closure] static ContactUsWidget <anonymous closure>(dynamic) {
    // ** addr: 0x923754, size: 0xc
    // 0x923754: r0 = Instance_ContactUsWidget
    //     0x923754: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0a0] Obj!ContactUsWidget@d67041
    //     0x923758: ldr             x0, [x0, #0xa0]
    // 0x92375c: ret
    //     0x92375c: ret             
  }
  [closure] static ReviewListWidget <anonymous closure>(dynamic) {
    // ** addr: 0x923760, size: 0xc
    // 0x923760: r0 = Instance_ReviewListWidget
    //     0x923760: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0a8] Obj!ReviewListWidget@d66e41
    //     0x923764: ldr             x0, [x0, #0xa8]
    // 0x923768: ret
    //     0x923768: ret             
  }
  [closure] static PolicyWidget <anonymous closure>(dynamic) {
    // ** addr: 0x92376c, size: 0xc
    // 0x92376c: r0 = Instance_PolicyWidget
    //     0x92376c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0b0] Obj!PolicyWidget@d66e01
    //     0x923770: ldr             x0, [x0, #0xb0]
    // 0x923774: ret
    //     0x923774: ret             
  }
  [closure] static OrderFailedWidget <anonymous closure>(dynamic) {
    // ** addr: 0x923778, size: 0xc
    // 0x923778: r0 = Instance_OrderFailedWidget
    //     0x923778: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0b8] Obj!OrderFailedWidget@d66f01
    //     0x92377c: ldr             x0, [x0, #0xb8]
    // 0x923780: ret
    //     0x923780: ret             
  }
  [closure] static OrderSuccessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x923784, size: 0xc
    // 0x923784: r0 = Instance_OrderSuccessWidget
    //     0x923784: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0c0] Obj!OrderSuccessWidget@d66ee1
    //     0x923788: ldr             x0, [x0, #0xc0]
    // 0x92378c: ret
    //     0x92378c: ret             
  }
  [closure] static PaymentMethodsCheckoutWidget <anonymous closure>(dynamic) {
    // ** addr: 0x923790, size: 0xc
    // 0x923790: r0 = Instance_PaymentMethodsCheckoutWidget
    //     0x923790: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0c8] Obj!PaymentMethodsCheckoutWidget@d67061
    //     0x923794: ldr             x0, [x0, #0xc8]
    // 0x923798: ret
    //     0x923798: ret             
  }
  [closure] static ReplaceCallOrderView <anonymous closure>(dynamic) {
    // ** addr: 0x92379c, size: 0xc
    // 0x92379c: r0 = Instance_ReplaceCallOrderView
    //     0x92379c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0d0] Obj!ReplaceCallOrderView@d66ec1
    //     0x9237a0: ldr             x0, [x0, #0xd0]
    // 0x9237a4: ret
    //     0x9237a4: ret             
  }
  [closure] static EnlargeImageView <anonymous closure>(dynamic) {
    // ** addr: 0x9237a8, size: 0xc
    // 0x9237a8: r0 = Instance_EnlargeImageView
    //     0x9237a8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0d8] Obj!EnlargeImageView@d66f61
    //     0x9237ac: ldr             x0, [x0, #0xd8]
    // 0x9237b0: ret
    //     0x9237b0: ret             
  }
  [closure] static BrowseView <anonymous closure>(dynamic) {
    // ** addr: 0x9237b4, size: 0xc
    // 0x9237b4: r0 = Instance_BrowseView
    //     0x9237b4: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0e0] Obj!BrowseView@d67121
    //     0x9237b8: ldr             x0, [x0, #0xe0]
    // 0x9237bc: ret
    //     0x9237bc: ret             
  }
  [closure] static ReturnOrderWithProofView <anonymous closure>(dynamic) {
    // ** addr: 0x9237c0, size: 0xc
    // 0x9237c0: r0 = Instance_ReturnOrderWithProofView
    //     0x9237c0: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0e8] Obj!ReturnOrderWithProofView@d66e81
    //     0x9237c4: ldr             x0, [x0, #0xe8]
    // 0x9237c8: ret
    //     0x9237c8: ret             
  }
  [closure] static ReturnOrderView <anonymous closure>(dynamic) {
    // ** addr: 0x9237cc, size: 0xc
    // 0x9237cc: r0 = Instance_ReturnOrderView
    //     0x9237cc: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0f0] Obj!ReturnOrderView@d66ea1
    //     0x9237d0: ldr             x0, [x0, #0xf0]
    // 0x9237d4: ret
    //     0x9237d4: ret             
  }
  [closure] static CustomizedPage <anonymous closure>(dynamic) {
    // ** addr: 0x9237d8, size: 0xc
    // 0x9237d8: r0 = Instance_CustomizedPage
    //     0x9237d8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe0f8] Obj!CustomizedPage@d67021
    //     0x9237dc: ldr             x0, [x0, #0xf8]
    // 0x9237e0: ret
    //     0x9237e0: ret             
  }
  [closure] static TestimonialsView <anonymous closure>(dynamic) {
    // ** addr: 0x9237e4, size: 0xc
    // 0x9237e4: r0 = Instance_TestimonialsView
    //     0x9237e4: add             x0, PP, #0xe, lsl #12  ; [pp+0xe100] Obj!TestimonialsView@d66da1
    //     0x9237e8: ldr             x0, [x0, #0x100]
    // 0x9237ec: ret
    //     0x9237ec: ret             
  }
  [closure] static OrderDetailView <anonymous closure>(dynamic) {
    // ** addr: 0x9237f0, size: 0x54
    // 0x9237f0: EnterFrame
    //     0x9237f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9237f4: mov             fp, SP
    // 0x9237f8: AllocStack(0x8)
    //     0x9237f8: sub             SP, SP, #8
    // 0x9237fc: CheckStackOverflow
    //     0x9237fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x923800: cmp             SP, x16
    //     0x923804: b.ls            #0x92383c
    // 0x923808: r0 = ScrollController()
    //     0x923808: bl              #0x675ac8  ; AllocateScrollControllerStub -> ScrollController (size=0x40)
    // 0x92380c: mov             x1, x0
    // 0x923810: stur            x0, [fp, #-8]
    // 0x923814: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x923814: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x923818: r0 = ScrollController()
    //     0x923818: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x92381c: r1 = <OrderDetailController>
    //     0x92381c: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad0] TypeArguments: <OrderDetailController>
    //     0x923820: ldr             x1, [x1, #0xad0]
    // 0x923824: r0 = OrderDetailView()
    //     0x923824: bl              #0x923844  ; AllocateOrderDetailViewStub -> OrderDetailView (size=0x18)
    // 0x923828: ldur            x1, [fp, #-8]
    // 0x92382c: StoreField: r0->field_13 = r1
    //     0x92382c: stur            w1, [x0, #0x13]
    // 0x923830: LeaveFrame
    //     0x923830: mov             SP, fp
    //     0x923834: ldp             fp, lr, [SP], #0x10
    // 0x923838: ret
    //     0x923838: ret             
    // 0x92383c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92383c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923840: b               #0x923808
  }
  [closure] static LoginView <anonymous closure>(dynamic) {
    // ** addr: 0x923850, size: 0x50
    // 0x923850: EnterFrame
    //     0x923850: stp             fp, lr, [SP, #-0x10]!
    //     0x923854: mov             fp, SP
    // 0x923858: AllocStack(0x8)
    //     0x923858: sub             SP, SP, #8
    // 0x92385c: SetupParameters()
    //     0x92385c: add             x1, PP, #0xa, lsl #12  ; [pp+0xac08] TypeArguments: <LoginController>
    //     0x923860: ldr             x1, [x1, #0xc08]
    // 0x92385c: r1 = <LoginController>
    // 0x923864: r0 = LoginView()
    //     0x923864: bl              #0x9238a0  ; AllocateLoginViewStub -> LoginView (size=0x20)
    // 0x923868: mov             x2, x0
    // 0x92386c: r0 = ""
    //     0x92386c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x923870: stur            x2, [fp, #-8]
    // 0x923874: ArrayStore: r2[0] = r0  ; List_4
    //     0x923874: stur            w0, [x2, #0x17]
    // 0x923878: StoreField: r2->field_1b = r0
    //     0x923878: stur            w0, [x2, #0x1b]
    // 0x92387c: r1 = <FormState>
    //     0x92387c: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0x923880: ldr             x1, [x1, #0xad8]
    // 0x923884: r0 = LabeledGlobalKey()
    //     0x923884: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x923888: mov             x1, x0
    // 0x92388c: ldur            x0, [fp, #-8]
    // 0x923890: StoreField: r0->field_13 = r1
    //     0x923890: stur            w1, [x0, #0x13]
    // 0x923894: LeaveFrame
    //     0x923894: mov             SP, fp
    //     0x923898: ldp             fp, lr, [SP], #0x10
    // 0x92389c: ret
    //     0x92389c: ret             
  }
  [closure] static OrdersView <anonymous closure>(dynamic) {
    // ** addr: 0x9238ac, size: 0xc
    // 0x9238ac: r0 = Instance_OrdersView
    //     0x9238ac: add             x0, PP, #0xe, lsl #12  ; [pp+0xe108] Obj!OrdersView@d66f41
    //     0x9238b0: ldr             x0, [x0, #0x108]
    // 0x9238b4: ret
    //     0x9238b4: ret             
  }
  [closure] static ProfileView <anonymous closure>(dynamic) {
    // ** addr: 0x9238b8, size: 0xc
    // 0x9238b8: r0 = Instance_ProfileView
    //     0x9238b8: add             x0, PP, #0xe, lsl #12  ; [pp+0xe110] Obj!ProfileView@d66e21
    //     0x9238bc: ldr             x0, [x0, #0x110]
    // 0x9238c0: ret
    //     0x9238c0: ret             
  }
  [closure] static BagView <anonymous closure>(dynamic) {
    // ** addr: 0x9238c4, size: 0xc
    // 0x9238c4: r0 = Instance_BagView
    //     0x9238c4: add             x0, PP, #0xe, lsl #12  ; [pp+0xe118] Obj!BagView@d67141
    //     0x9238c8: ldr             x0, [x0, #0x118]
    // 0x9238cc: ret
    //     0x9238cc: ret             
  }
  [closure] static ProductDetailView <anonymous closure>(dynamic) {
    // ** addr: 0x9238d0, size: 0xc
    // 0x9238d0: r0 = Instance_ProductDetailView
    //     0x9238d0: add             x0, PP, #0xe, lsl #12  ; [pp+0xe120] Obj!ProductDetailView@d66e61
    //     0x9238d4: ldr             x0, [x0, #0x120]
    // 0x9238d8: ret
    //     0x9238d8: ret             
  }
  [closure] static CollectionPage <anonymous closure>(dynamic) {
    // ** addr: 0x9238dc, size: 0x48
    // 0x9238dc: EnterFrame
    //     0x9238dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9238e0: mov             fp, SP
    // 0x9238e4: AllocStack(0x8)
    //     0x9238e4: sub             SP, SP, #8
    // 0x9238e8: CheckStackOverflow
    //     0x9238e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9238ec: cmp             SP, x16
    //     0x9238f0: b.ls            #0x92391c
    // 0x9238f4: r1 = <CollectionsController>
    //     0x9238f4: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0x9238f8: ldr             x1, [x1, #0xb00]
    // 0x9238fc: r0 = CollectionPage()
    //     0x9238fc: bl              #0x923924  ; AllocateCollectionPageStub -> CollectionPage (size=0x2c)
    // 0x923900: mov             x1, x0
    // 0x923904: stur            x0, [fp, #-8]
    // 0x923908: r0 = CollectionPage()
    //     0x923908: bl              #0x91d674  ; [package:customer_app/app/presentation/views/basic/collections/collection_page.dart] CollectionPage::CollectionPage
    // 0x92390c: ldur            x0, [fp, #-8]
    // 0x923910: LeaveFrame
    //     0x923910: mov             SP, fp
    //     0x923914: ldp             fp, lr, [SP], #0x10
    // 0x923918: ret
    //     0x923918: ret             
    // 0x92391c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92391c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923920: b               #0x9238f4
  }
  [closure] static SearchPage <anonymous closure>(dynamic) {
    // ** addr: 0x923930, size: 0xc
    // 0x923930: r0 = Instance_SearchPage
    //     0x923930: add             x0, PP, #0xe, lsl #12  ; [pp+0xe128] Obj!SearchPage@d66dc1
    //     0x923934: ldr             x0, [x0, #0x128]
    // 0x923938: ret
    //     0x923938: ret             
  }
  [closure] static HomePage <anonymous closure>(dynamic) {
    // ** addr: 0x92393c, size: 0xc
    // 0x92393c: r0 = Instance_HomePage
    //     0x92393c: add             x0, PP, #0xe, lsl #12  ; [pp+0xe130] Obj!HomePage@d66f81
    //     0x923940: ldr             x0, [x0, #0x130]
    // 0x923944: ret
    //     0x923944: ret             
  }
  [closure] static MainPage <anonymous closure>(dynamic) {
    // ** addr: 0x923948, size: 0xc
    // 0x923948: r0 = Instance_MainPage
    //     0x923948: add             x0, PP, #0xe, lsl #12  ; [pp+0xe138] Obj!MainPage@d65881
    //     0x92394c: ldr             x0, [x0, #0x138]
    // 0x923950: ret
    //     0x923950: ret             
  }
}
