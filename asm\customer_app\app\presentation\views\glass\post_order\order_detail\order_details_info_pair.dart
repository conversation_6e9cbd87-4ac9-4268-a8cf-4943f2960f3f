// lib: , url: package:customer_app/app/presentation/views/glass/post_order/order_detail/order_details_info_pair.dart

// class id: 1049421, size: 0x8
class :: {
}

// class id: 4493, size: 0x10, field offset: 0xc
//   const constructor, 
class OrderDetailsInfoPair extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x1292d60, size: 0x250
    // 0x1292d60: EnterFrame
    //     0x1292d60: stp             fp, lr, [SP, #-0x10]!
    //     0x1292d64: mov             fp, SP
    // 0x1292d68: AllocStack(0x40)
    //     0x1292d68: sub             SP, SP, #0x40
    // 0x1292d6c: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x1292d6c: mov             x0, x2
    //     0x1292d70: stur            x2, [fp, #-0x18]
    // 0x1292d74: CheckStackOverflow
    //     0x1292d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1292d78: cmp             SP, x16
    //     0x1292d7c: b.ls            #0x1292fa8
    // 0x1292d80: LoadField: r2 = r1->field_b
    //     0x1292d80: ldur            w2, [x1, #0xb]
    // 0x1292d84: DecompressPointer r2
    //     0x1292d84: add             x2, x2, HEAP, lsl #32
    // 0x1292d88: stur            x2, [fp, #-0x10]
    // 0x1292d8c: LoadField: r1 = r2->field_7
    //     0x1292d8c: ldur            w1, [x2, #7]
    // 0x1292d90: DecompressPointer r1
    //     0x1292d90: add             x1, x1, HEAP, lsl #32
    // 0x1292d94: cmp             w1, NULL
    // 0x1292d98: b.ne            #0x1292da4
    // 0x1292d9c: r3 = ""
    //     0x1292d9c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1292da0: b               #0x1292da8
    // 0x1292da4: mov             x3, x1
    // 0x1292da8: mov             x1, x0
    // 0x1292dac: stur            x3, [fp, #-8]
    // 0x1292db0: r0 = of()
    //     0x1292db0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1292db4: LoadField: r1 = r0->field_87
    //     0x1292db4: ldur            w1, [x0, #0x87]
    // 0x1292db8: DecompressPointer r1
    //     0x1292db8: add             x1, x1, HEAP, lsl #32
    // 0x1292dbc: LoadField: r0 = r1->field_7
    //     0x1292dbc: ldur            w0, [x1, #7]
    // 0x1292dc0: DecompressPointer r0
    //     0x1292dc0: add             x0, x0, HEAP, lsl #32
    // 0x1292dc4: r16 = 12.000000
    //     0x1292dc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1292dc8: ldr             x16, [x16, #0x9e8]
    // 0x1292dcc: r30 = Instance_Color
    //     0x1292dcc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1292dd0: stp             lr, x16, [SP]
    // 0x1292dd4: mov             x1, x0
    // 0x1292dd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1292dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1292ddc: ldr             x4, [x4, #0xaa0]
    // 0x1292de0: r0 = copyWith()
    //     0x1292de0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1292de4: stur            x0, [fp, #-0x20]
    // 0x1292de8: r0 = Text()
    //     0x1292de8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292dec: mov             x1, x0
    // 0x1292df0: ldur            x0, [fp, #-8]
    // 0x1292df4: stur            x1, [fp, #-0x28]
    // 0x1292df8: StoreField: r1->field_b = r0
    //     0x1292df8: stur            w0, [x1, #0xb]
    // 0x1292dfc: ldur            x0, [fp, #-0x20]
    // 0x1292e00: StoreField: r1->field_13 = r0
    //     0x1292e00: stur            w0, [x1, #0x13]
    // 0x1292e04: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1292e04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1292e08: ldr             x0, [x0, #0x1c80]
    //     0x1292e0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1292e10: cmp             w0, w16
    //     0x1292e14: b.ne            #0x1292e20
    //     0x1292e18: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1292e1c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1292e20: r0 = GetNavigation.size()
    //     0x1292e20: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1292e24: LoadField: d0 = r0->field_7
    //     0x1292e24: ldur            d0, [x0, #7]
    // 0x1292e28: d1 = 0.900000
    //     0x1292e28: ldr             d1, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0x1292e2c: fmul            d2, d0, d1
    // 0x1292e30: stur            d2, [fp, #-0x30]
    // 0x1292e34: r0 = BoxConstraints()
    //     0x1292e34: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1292e38: stur            x0, [fp, #-0x20]
    // 0x1292e3c: StoreField: r0->field_7 = rZR
    //     0x1292e3c: stur            xzr, [x0, #7]
    // 0x1292e40: ldur            d0, [fp, #-0x30]
    // 0x1292e44: StoreField: r0->field_f = d0
    //     0x1292e44: stur            d0, [x0, #0xf]
    // 0x1292e48: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1292e48: stur            xzr, [x0, #0x17]
    // 0x1292e4c: d0 = inf
    //     0x1292e4c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1292e50: StoreField: r0->field_1f = d0
    //     0x1292e50: stur            d0, [x0, #0x1f]
    // 0x1292e54: ldur            x1, [fp, #-0x10]
    // 0x1292e58: LoadField: r2 = r1->field_b
    //     0x1292e58: ldur            w2, [x1, #0xb]
    // 0x1292e5c: DecompressPointer r2
    //     0x1292e5c: add             x2, x2, HEAP, lsl #32
    // 0x1292e60: cmp             w2, NULL
    // 0x1292e64: b.ne            #0x1292e70
    // 0x1292e68: r3 = ""
    //     0x1292e68: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1292e6c: b               #0x1292e74
    // 0x1292e70: mov             x3, x2
    // 0x1292e74: ldur            x2, [fp, #-0x28]
    // 0x1292e78: ldur            x1, [fp, #-0x18]
    // 0x1292e7c: stur            x3, [fp, #-8]
    // 0x1292e80: r0 = of()
    //     0x1292e80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1292e84: LoadField: r1 = r0->field_87
    //     0x1292e84: ldur            w1, [x0, #0x87]
    // 0x1292e88: DecompressPointer r1
    //     0x1292e88: add             x1, x1, HEAP, lsl #32
    // 0x1292e8c: LoadField: r0 = r1->field_2b
    //     0x1292e8c: ldur            w0, [x1, #0x2b]
    // 0x1292e90: DecompressPointer r0
    //     0x1292e90: add             x0, x0, HEAP, lsl #32
    // 0x1292e94: stur            x0, [fp, #-0x10]
    // 0x1292e98: r1 = Instance_Color
    //     0x1292e98: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1292e9c: d0 = 0.400000
    //     0x1292e9c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1292ea0: r0 = withOpacity()
    //     0x1292ea0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1292ea4: r16 = 12.000000
    //     0x1292ea4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1292ea8: ldr             x16, [x16, #0x9e8]
    // 0x1292eac: stp             x0, x16, [SP]
    // 0x1292eb0: ldur            x1, [fp, #-0x10]
    // 0x1292eb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1292eb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1292eb8: ldr             x4, [x4, #0xaa0]
    // 0x1292ebc: r0 = copyWith()
    //     0x1292ebc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1292ec0: stur            x0, [fp, #-0x10]
    // 0x1292ec4: r0 = Text()
    //     0x1292ec4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292ec8: mov             x1, x0
    // 0x1292ecc: ldur            x0, [fp, #-8]
    // 0x1292ed0: stur            x1, [fp, #-0x18]
    // 0x1292ed4: StoreField: r1->field_b = r0
    //     0x1292ed4: stur            w0, [x1, #0xb]
    // 0x1292ed8: ldur            x0, [fp, #-0x10]
    // 0x1292edc: StoreField: r1->field_13 = r0
    //     0x1292edc: stur            w0, [x1, #0x13]
    // 0x1292ee0: r0 = ConstrainedBox()
    //     0x1292ee0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1292ee4: mov             x3, x0
    // 0x1292ee8: ldur            x0, [fp, #-0x20]
    // 0x1292eec: stur            x3, [fp, #-8]
    // 0x1292ef0: StoreField: r3->field_f = r0
    //     0x1292ef0: stur            w0, [x3, #0xf]
    // 0x1292ef4: ldur            x0, [fp, #-0x18]
    // 0x1292ef8: StoreField: r3->field_b = r0
    //     0x1292ef8: stur            w0, [x3, #0xb]
    // 0x1292efc: r1 = Null
    //     0x1292efc: mov             x1, NULL
    // 0x1292f00: r2 = 6
    //     0x1292f00: movz            x2, #0x6
    // 0x1292f04: r0 = AllocateArray()
    //     0x1292f04: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1292f08: mov             x2, x0
    // 0x1292f0c: ldur            x0, [fp, #-0x28]
    // 0x1292f10: stur            x2, [fp, #-0x10]
    // 0x1292f14: StoreField: r2->field_f = r0
    //     0x1292f14: stur            w0, [x2, #0xf]
    // 0x1292f18: ldur            x0, [fp, #-8]
    // 0x1292f1c: StoreField: r2->field_13 = r0
    //     0x1292f1c: stur            w0, [x2, #0x13]
    // 0x1292f20: r16 = Instance_SizedBox
    //     0x1292f20: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x1292f24: ldr             x16, [x16, #0x8b8]
    // 0x1292f28: ArrayStore: r2[0] = r16  ; List_4
    //     0x1292f28: stur            w16, [x2, #0x17]
    // 0x1292f2c: r1 = <Widget>
    //     0x1292f2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1292f30: r0 = AllocateGrowableArray()
    //     0x1292f30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1292f34: mov             x1, x0
    // 0x1292f38: ldur            x0, [fp, #-0x10]
    // 0x1292f3c: stur            x1, [fp, #-8]
    // 0x1292f40: StoreField: r1->field_f = r0
    //     0x1292f40: stur            w0, [x1, #0xf]
    // 0x1292f44: r0 = 6
    //     0x1292f44: movz            x0, #0x6
    // 0x1292f48: StoreField: r1->field_b = r0
    //     0x1292f48: stur            w0, [x1, #0xb]
    // 0x1292f4c: r0 = Wrap()
    //     0x1292f4c: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x1292f50: r1 = Instance_Axis
    //     0x1292f50: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1292f54: StoreField: r0->field_f = r1
    //     0x1292f54: stur            w1, [x0, #0xf]
    // 0x1292f58: r1 = Instance_WrapAlignment
    //     0x1292f58: add             x1, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x1292f5c: ldr             x1, [x1, #0x6e8]
    // 0x1292f60: StoreField: r0->field_13 = r1
    //     0x1292f60: stur            w1, [x0, #0x13]
    // 0x1292f64: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1292f64: stur            xzr, [x0, #0x17]
    // 0x1292f68: StoreField: r0->field_1f = r1
    //     0x1292f68: stur            w1, [x0, #0x1f]
    // 0x1292f6c: StoreField: r0->field_23 = rZR
    //     0x1292f6c: stur            xzr, [x0, #0x23]
    // 0x1292f70: r1 = Instance_WrapCrossAlignment
    //     0x1292f70: add             x1, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x1292f74: ldr             x1, [x1, #0x6f0]
    // 0x1292f78: StoreField: r0->field_2b = r1
    //     0x1292f78: stur            w1, [x0, #0x2b]
    // 0x1292f7c: r1 = Instance_VerticalDirection
    //     0x1292f7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1292f80: ldr             x1, [x1, #0xa20]
    // 0x1292f84: StoreField: r0->field_33 = r1
    //     0x1292f84: stur            w1, [x0, #0x33]
    // 0x1292f88: r1 = Instance_Clip
    //     0x1292f88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1292f8c: ldr             x1, [x1, #0x38]
    // 0x1292f90: StoreField: r0->field_37 = r1
    //     0x1292f90: stur            w1, [x0, #0x37]
    // 0x1292f94: ldur            x1, [fp, #-8]
    // 0x1292f98: StoreField: r0->field_b = r1
    //     0x1292f98: stur            w1, [x0, #0xb]
    // 0x1292f9c: LeaveFrame
    //     0x1292f9c: mov             SP, fp
    //     0x1292fa0: ldp             fp, lr, [SP], #0x10
    // 0x1292fa4: ret
    //     0x1292fa4: ret             
    // 0x1292fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1292fa8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1292fac: b               #0x1292d80
  }
}
