// lib: , url: package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart

// class id: 1049515, size: 0x8
class :: {
}

// class id: 4535, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeProductSkusScreen extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1368c14, size: 0x64
    // 0x1368c14: EnterFrame
    //     0x1368c14: stp             fp, lr, [SP, #-0x10]!
    //     0x1368c18: mov             fp, SP
    // 0x1368c1c: AllocStack(0x18)
    //     0x1368c1c: sub             SP, SP, #0x18
    // 0x1368c20: SetupParameters(ExchangeProductSkusScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1368c20: stur            x1, [fp, #-8]
    //     0x1368c24: stur            x2, [fp, #-0x10]
    // 0x1368c28: r1 = 2
    //     0x1368c28: movz            x1, #0x2
    // 0x1368c2c: r0 = AllocateContext()
    //     0x1368c2c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1368c30: mov             x1, x0
    // 0x1368c34: ldur            x0, [fp, #-8]
    // 0x1368c38: stur            x1, [fp, #-0x18]
    // 0x1368c3c: StoreField: r1->field_f = r0
    //     0x1368c3c: stur            w0, [x1, #0xf]
    // 0x1368c40: ldur            x0, [fp, #-0x10]
    // 0x1368c44: StoreField: r1->field_13 = r0
    //     0x1368c44: stur            w0, [x1, #0x13]
    // 0x1368c48: r0 = Obx()
    //     0x1368c48: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1368c4c: ldur            x2, [fp, #-0x18]
    // 0x1368c50: r1 = Function '<anonymous closure>':.
    //     0x1368c50: add             x1, PP, #0x38, lsl #12  ; [pp+0x38078] AnonymousClosure: (0x1368c78), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::bottomNavigationBar (0x1368c14)
    //     0x1368c54: ldr             x1, [x1, #0x78]
    // 0x1368c58: stur            x0, [fp, #-8]
    // 0x1368c5c: r0 = AllocateClosure()
    //     0x1368c5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1368c60: mov             x1, x0
    // 0x1368c64: ldur            x0, [fp, #-8]
    // 0x1368c68: StoreField: r0->field_b = r1
    //     0x1368c68: stur            w1, [x0, #0xb]
    // 0x1368c6c: LeaveFrame
    //     0x1368c6c: mov             SP, fp
    //     0x1368c70: ldp             fp, lr, [SP], #0x10
    // 0x1368c74: ret
    //     0x1368c74: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x1368c78, size: 0x2c8
    // 0x1368c78: EnterFrame
    //     0x1368c78: stp             fp, lr, [SP, #-0x10]!
    //     0x1368c7c: mov             fp, SP
    // 0x1368c80: AllocStack(0x40)
    //     0x1368c80: sub             SP, SP, #0x40
    // 0x1368c84: SetupParameters()
    //     0x1368c84: ldr             x0, [fp, #0x10]
    //     0x1368c88: ldur            w2, [x0, #0x17]
    //     0x1368c8c: add             x2, x2, HEAP, lsl #32
    //     0x1368c90: stur            x2, [fp, #-8]
    // 0x1368c94: CheckStackOverflow
    //     0x1368c94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1368c98: cmp             SP, x16
    //     0x1368c9c: b.ls            #0x1368f38
    // 0x1368ca0: r16 = <EdgeInsets>
    //     0x1368ca0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1368ca4: ldr             x16, [x16, #0xda0]
    // 0x1368ca8: r30 = Instance_EdgeInsets
    //     0x1368ca8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1368cac: ldr             lr, [lr, #0x1f0]
    // 0x1368cb0: stp             lr, x16, [SP]
    // 0x1368cb4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1368cb4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1368cb8: r0 = all()
    //     0x1368cb8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1368cbc: ldur            x2, [fp, #-8]
    // 0x1368cc0: stur            x0, [fp, #-0x10]
    // 0x1368cc4: LoadField: r1 = r2->field_f
    //     0x1368cc4: ldur            w1, [x2, #0xf]
    // 0x1368cc8: DecompressPointer r1
    //     0x1368cc8: add             x1, x1, HEAP, lsl #32
    // 0x1368ccc: r0 = controller()
    //     0x1368ccc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368cd0: LoadField: r1 = r0->field_5b
    //     0x1368cd0: ldur            w1, [x0, #0x5b]
    // 0x1368cd4: DecompressPointer r1
    //     0x1368cd4: add             x1, x1, HEAP, lsl #32
    // 0x1368cd8: r0 = RxStringExt.isNotEmpty()
    //     0x1368cd8: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1368cdc: tbnz            w0, #4, #0x1368d00
    // 0x1368ce0: ldur            x2, [fp, #-8]
    // 0x1368ce4: LoadField: r1 = r2->field_13
    //     0x1368ce4: ldur            w1, [x2, #0x13]
    // 0x1368ce8: DecompressPointer r1
    //     0x1368ce8: add             x1, x1, HEAP, lsl #32
    // 0x1368cec: r0 = of()
    //     0x1368cec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1368cf0: LoadField: r1 = r0->field_5b
    //     0x1368cf0: ldur            w1, [x0, #0x5b]
    // 0x1368cf4: DecompressPointer r1
    //     0x1368cf4: add             x1, x1, HEAP, lsl #32
    // 0x1368cf8: mov             x0, x1
    // 0x1368cfc: b               #0x1368d0c
    // 0x1368d00: r1 = Instance_Color
    //     0x1368d00: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1368d04: d0 = 0.400000
    //     0x1368d04: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1368d08: r0 = withOpacity()
    //     0x1368d08: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1368d0c: ldur            x2, [fp, #-8]
    // 0x1368d10: r16 = <Color>
    //     0x1368d10: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1368d14: ldr             x16, [x16, #0xf80]
    // 0x1368d18: stp             x0, x16, [SP]
    // 0x1368d1c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1368d1c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1368d20: r0 = all()
    //     0x1368d20: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1368d24: ldur            x2, [fp, #-8]
    // 0x1368d28: stur            x0, [fp, #-0x18]
    // 0x1368d2c: LoadField: r1 = r2->field_f
    //     0x1368d2c: ldur            w1, [x2, #0xf]
    // 0x1368d30: DecompressPointer r1
    //     0x1368d30: add             x1, x1, HEAP, lsl #32
    // 0x1368d34: r0 = controller()
    //     0x1368d34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368d38: LoadField: r1 = r0->field_5b
    //     0x1368d38: ldur            w1, [x0, #0x5b]
    // 0x1368d3c: DecompressPointer r1
    //     0x1368d3c: add             x1, x1, HEAP, lsl #32
    // 0x1368d40: r0 = RxStringExt.isNotEmpty()
    //     0x1368d40: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1368d44: tbnz            w0, #4, #0x1368d68
    // 0x1368d48: ldur            x2, [fp, #-8]
    // 0x1368d4c: LoadField: r1 = r2->field_13
    //     0x1368d4c: ldur            w1, [x2, #0x13]
    // 0x1368d50: DecompressPointer r1
    //     0x1368d50: add             x1, x1, HEAP, lsl #32
    // 0x1368d54: r0 = of()
    //     0x1368d54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1368d58: LoadField: r1 = r0->field_5b
    //     0x1368d58: ldur            w1, [x0, #0x5b]
    // 0x1368d5c: DecompressPointer r1
    //     0x1368d5c: add             x1, x1, HEAP, lsl #32
    // 0x1368d60: mov             x3, x1
    // 0x1368d64: b               #0x1368d78
    // 0x1368d68: r1 = Instance_Color
    //     0x1368d68: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1368d6c: d0 = 0.400000
    //     0x1368d6c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1368d70: r0 = withOpacity()
    //     0x1368d70: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1368d74: mov             x3, x0
    // 0x1368d78: ldur            x2, [fp, #-8]
    // 0x1368d7c: ldur            x1, [fp, #-0x10]
    // 0x1368d80: ldur            x0, [fp, #-0x18]
    // 0x1368d84: r16 = <Color>
    //     0x1368d84: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1368d88: ldr             x16, [x16, #0xf80]
    // 0x1368d8c: stp             x3, x16, [SP]
    // 0x1368d90: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1368d90: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1368d94: r0 = all()
    //     0x1368d94: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1368d98: stur            x0, [fp, #-0x20]
    // 0x1368d9c: r16 = <RoundedRectangleBorder>
    //     0x1368d9c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1368da0: ldr             x16, [x16, #0xf78]
    // 0x1368da4: r30 = Instance_RoundedRectangleBorder
    //     0x1368da4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1368da8: ldr             lr, [lr, #0xd68]
    // 0x1368dac: stp             lr, x16, [SP]
    // 0x1368db0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1368db0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1368db4: r0 = all()
    //     0x1368db4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1368db8: stur            x0, [fp, #-0x28]
    // 0x1368dbc: r0 = ButtonStyle()
    //     0x1368dbc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1368dc0: mov             x1, x0
    // 0x1368dc4: ldur            x0, [fp, #-0x20]
    // 0x1368dc8: stur            x1, [fp, #-0x30]
    // 0x1368dcc: StoreField: r1->field_b = r0
    //     0x1368dcc: stur            w0, [x1, #0xb]
    // 0x1368dd0: ldur            x0, [fp, #-0x18]
    // 0x1368dd4: StoreField: r1->field_f = r0
    //     0x1368dd4: stur            w0, [x1, #0xf]
    // 0x1368dd8: ldur            x0, [fp, #-0x10]
    // 0x1368ddc: StoreField: r1->field_23 = r0
    //     0x1368ddc: stur            w0, [x1, #0x23]
    // 0x1368de0: ldur            x0, [fp, #-0x28]
    // 0x1368de4: StoreField: r1->field_43 = r0
    //     0x1368de4: stur            w0, [x1, #0x43]
    // 0x1368de8: r0 = TextButtonThemeData()
    //     0x1368de8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1368dec: mov             x2, x0
    // 0x1368df0: ldur            x0, [fp, #-0x30]
    // 0x1368df4: stur            x2, [fp, #-0x10]
    // 0x1368df8: StoreField: r2->field_7 = r0
    //     0x1368df8: stur            w0, [x2, #7]
    // 0x1368dfc: ldur            x0, [fp, #-8]
    // 0x1368e00: LoadField: r1 = r0->field_f
    //     0x1368e00: ldur            w1, [x0, #0xf]
    // 0x1368e04: DecompressPointer r1
    //     0x1368e04: add             x1, x1, HEAP, lsl #32
    // 0x1368e08: r0 = controller()
    //     0x1368e08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368e0c: LoadField: r1 = r0->field_5b
    //     0x1368e0c: ldur            w1, [x0, #0x5b]
    // 0x1368e10: DecompressPointer r1
    //     0x1368e10: add             x1, x1, HEAP, lsl #32
    // 0x1368e14: r0 = RxStringExt.isNotEmpty()
    //     0x1368e14: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1368e18: tbnz            w0, #4, #0x1368e34
    // 0x1368e1c: ldur            x2, [fp, #-8]
    // 0x1368e20: r1 = Function '<anonymous closure>':.
    //     0x1368e20: add             x1, PP, #0x38, lsl #12  ; [pp+0x38080] AnonymousClosure: (0x1368f40), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::bottomNavigationBar (0x1368c14)
    //     0x1368e24: ldr             x1, [x1, #0x80]
    // 0x1368e28: r0 = AllocateClosure()
    //     0x1368e28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1368e2c: mov             x2, x0
    // 0x1368e30: b               #0x1368e38
    // 0x1368e34: r2 = Null
    //     0x1368e34: mov             x2, NULL
    // 0x1368e38: ldur            x1, [fp, #-8]
    // 0x1368e3c: ldur            x0, [fp, #-0x10]
    // 0x1368e40: stur            x2, [fp, #-0x18]
    // 0x1368e44: LoadField: r3 = r1->field_13
    //     0x1368e44: ldur            w3, [x1, #0x13]
    // 0x1368e48: DecompressPointer r3
    //     0x1368e48: add             x3, x3, HEAP, lsl #32
    // 0x1368e4c: mov             x1, x3
    // 0x1368e50: r0 = of()
    //     0x1368e50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1368e54: LoadField: r1 = r0->field_87
    //     0x1368e54: ldur            w1, [x0, #0x87]
    // 0x1368e58: DecompressPointer r1
    //     0x1368e58: add             x1, x1, HEAP, lsl #32
    // 0x1368e5c: LoadField: r0 = r1->field_2b
    //     0x1368e5c: ldur            w0, [x1, #0x2b]
    // 0x1368e60: DecompressPointer r0
    //     0x1368e60: add             x0, x0, HEAP, lsl #32
    // 0x1368e64: r16 = Instance_Color
    //     0x1368e64: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1368e68: str             x16, [SP]
    // 0x1368e6c: mov             x1, x0
    // 0x1368e70: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x1368e70: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x1368e74: ldr             x4, [x4, #0xf40]
    // 0x1368e78: r0 = copyWith()
    //     0x1368e78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1368e7c: stur            x0, [fp, #-8]
    // 0x1368e80: r0 = Text()
    //     0x1368e80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1368e84: mov             x1, x0
    // 0x1368e88: r0 = "NEXT"
    //     0x1368e88: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f20] "NEXT"
    //     0x1368e8c: ldr             x0, [x0, #0xf20]
    // 0x1368e90: stur            x1, [fp, #-0x20]
    // 0x1368e94: StoreField: r1->field_b = r0
    //     0x1368e94: stur            w0, [x1, #0xb]
    // 0x1368e98: ldur            x0, [fp, #-8]
    // 0x1368e9c: StoreField: r1->field_13 = r0
    //     0x1368e9c: stur            w0, [x1, #0x13]
    // 0x1368ea0: r0 = TextButton()
    //     0x1368ea0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1368ea4: mov             x1, x0
    // 0x1368ea8: ldur            x0, [fp, #-0x18]
    // 0x1368eac: stur            x1, [fp, #-8]
    // 0x1368eb0: StoreField: r1->field_b = r0
    //     0x1368eb0: stur            w0, [x1, #0xb]
    // 0x1368eb4: r0 = false
    //     0x1368eb4: add             x0, NULL, #0x30  ; false
    // 0x1368eb8: StoreField: r1->field_27 = r0
    //     0x1368eb8: stur            w0, [x1, #0x27]
    // 0x1368ebc: r0 = true
    //     0x1368ebc: add             x0, NULL, #0x20  ; true
    // 0x1368ec0: StoreField: r1->field_2f = r0
    //     0x1368ec0: stur            w0, [x1, #0x2f]
    // 0x1368ec4: ldur            x0, [fp, #-0x20]
    // 0x1368ec8: StoreField: r1->field_37 = r0
    //     0x1368ec8: stur            w0, [x1, #0x37]
    // 0x1368ecc: r0 = TextButtonTheme()
    //     0x1368ecc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1368ed0: mov             x1, x0
    // 0x1368ed4: ldur            x0, [fp, #-0x10]
    // 0x1368ed8: stur            x1, [fp, #-0x18]
    // 0x1368edc: StoreField: r1->field_f = r0
    //     0x1368edc: stur            w0, [x1, #0xf]
    // 0x1368ee0: ldur            x0, [fp, #-8]
    // 0x1368ee4: StoreField: r1->field_b = r0
    //     0x1368ee4: stur            w0, [x1, #0xb]
    // 0x1368ee8: r0 = SizedBox()
    //     0x1368ee8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1368eec: mov             x1, x0
    // 0x1368ef0: r0 = inf
    //     0x1368ef0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1368ef4: ldr             x0, [x0, #0x9f8]
    // 0x1368ef8: stur            x1, [fp, #-8]
    // 0x1368efc: StoreField: r1->field_f = r0
    //     0x1368efc: stur            w0, [x1, #0xf]
    // 0x1368f00: r0 = 48.000000
    //     0x1368f00: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x1368f04: ldr             x0, [x0, #0xad8]
    // 0x1368f08: StoreField: r1->field_13 = r0
    //     0x1368f08: stur            w0, [x1, #0x13]
    // 0x1368f0c: ldur            x0, [fp, #-0x18]
    // 0x1368f10: StoreField: r1->field_b = r0
    //     0x1368f10: stur            w0, [x1, #0xb]
    // 0x1368f14: r0 = Padding()
    //     0x1368f14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1368f18: r1 = Instance_EdgeInsets
    //     0x1368f18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1368f1c: ldr             x1, [x1, #0x1f0]
    // 0x1368f20: StoreField: r0->field_f = r1
    //     0x1368f20: stur            w1, [x0, #0xf]
    // 0x1368f24: ldur            x1, [fp, #-8]
    // 0x1368f28: StoreField: r0->field_b = r1
    //     0x1368f28: stur            w1, [x0, #0xb]
    // 0x1368f2c: LeaveFrame
    //     0x1368f2c: mov             SP, fp
    //     0x1368f30: ldp             fp, lr, [SP], #0x10
    // 0x1368f34: ret
    //     0x1368f34: ret             
    // 0x1368f38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1368f38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1368f3c: b               #0x1368ca0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1368f40, size: 0x260
    // 0x1368f40: EnterFrame
    //     0x1368f40: stp             fp, lr, [SP, #-0x10]!
    //     0x1368f44: mov             fp, SP
    // 0x1368f48: AllocStack(0x28)
    //     0x1368f48: sub             SP, SP, #0x28
    // 0x1368f4c: SetupParameters()
    //     0x1368f4c: ldr             x0, [fp, #0x10]
    //     0x1368f50: ldur            w2, [x0, #0x17]
    //     0x1368f54: add             x2, x2, HEAP, lsl #32
    //     0x1368f58: stur            x2, [fp, #-8]
    // 0x1368f5c: CheckStackOverflow
    //     0x1368f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1368f60: cmp             SP, x16
    //     0x1368f64: b.ls            #0x1369198
    // 0x1368f68: LoadField: r1 = r2->field_f
    //     0x1368f68: ldur            w1, [x2, #0xf]
    // 0x1368f6c: DecompressPointer r1
    //     0x1368f6c: add             x1, x1, HEAP, lsl #32
    // 0x1368f70: r0 = controller()
    //     0x1368f70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368f74: mov             x1, x0
    // 0x1368f78: r2 = "NEXT"
    //     0x1368f78: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f20] "NEXT"
    //     0x1368f7c: ldr             x2, [x2, #0xf20]
    // 0x1368f80: r3 = "return_exchange_next"
    //     0x1368f80: add             x3, PP, #0x33, lsl #12  ; [pp+0x33f60] "return_exchange_next"
    //     0x1368f84: ldr             x3, [x3, #0xf60]
    // 0x1368f88: r0 = ctaPostEvent()
    //     0x1368f88: bl              #0x1318484  ; [package:customer_app/app/presentation/controllers/exchange/exchange_same_product_controller.dart] ExchangeSameProductController::ctaPostEvent
    // 0x1368f8c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1368f8c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1368f90: ldr             x0, [x0, #0x1c80]
    //     0x1368f94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1368f98: cmp             w0, w16
    //     0x1368f9c: b.ne            #0x1368fa8
    //     0x1368fa0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1368fa4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1368fa8: r1 = Null
    //     0x1368fa8: mov             x1, NULL
    // 0x1368fac: r2 = 20
    //     0x1368fac: movz            x2, #0x14
    // 0x1368fb0: r0 = AllocateArray()
    //     0x1368fb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1368fb4: stur            x0, [fp, #-0x10]
    // 0x1368fb8: r16 = "order_id"
    //     0x1368fb8: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x1368fbc: ldr             x16, [x16, #0xa38]
    // 0x1368fc0: StoreField: r0->field_f = r16
    //     0x1368fc0: stur            w16, [x0, #0xf]
    // 0x1368fc4: ldur            x2, [fp, #-8]
    // 0x1368fc8: LoadField: r1 = r2->field_f
    //     0x1368fc8: ldur            w1, [x2, #0xf]
    // 0x1368fcc: DecompressPointer r1
    //     0x1368fcc: add             x1, x1, HEAP, lsl #32
    // 0x1368fd0: r0 = controller()
    //     0x1368fd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368fd4: LoadField: r1 = r0->field_6f
    //     0x1368fd4: ldur            w1, [x0, #0x6f]
    // 0x1368fd8: DecompressPointer r1
    //     0x1368fd8: add             x1, x1, HEAP, lsl #32
    // 0x1368fdc: r0 = value()
    //     0x1368fdc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1368fe0: ldur            x1, [fp, #-0x10]
    // 0x1368fe4: ArrayStore: r1[1] = r0  ; List_4
    //     0x1368fe4: add             x25, x1, #0x13
    //     0x1368fe8: str             w0, [x25]
    //     0x1368fec: tbz             w0, #0, #0x1369008
    //     0x1368ff0: ldurb           w16, [x1, #-1]
    //     0x1368ff4: ldurb           w17, [x0, #-1]
    //     0x1368ff8: and             x16, x17, x16, lsr #2
    //     0x1368ffc: tst             x16, HEAP, lsr #32
    //     0x1369000: b.eq            #0x1369008
    //     0x1369004: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1369008: ldur            x0, [fp, #-0x10]
    // 0x136900c: r16 = "charge"
    //     0x136900c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b28] "charge"
    //     0x1369010: ldr             x16, [x16, #0xb28]
    // 0x1369014: ArrayStore: r0[0] = r16  ; List_4
    //     0x1369014: stur            w16, [x0, #0x17]
    // 0x1369018: ldur            x2, [fp, #-8]
    // 0x136901c: LoadField: r1 = r2->field_f
    //     0x136901c: ldur            w1, [x2, #0xf]
    // 0x1369020: DecompressPointer r1
    //     0x1369020: add             x1, x1, HEAP, lsl #32
    // 0x1369024: r0 = controller()
    //     0x1369024: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1369028: LoadField: r1 = r0->field_67
    //     0x1369028: ldur            w1, [x0, #0x67]
    // 0x136902c: DecompressPointer r1
    //     0x136902c: add             x1, x1, HEAP, lsl #32
    // 0x1369030: mov             x0, x1
    // 0x1369034: ldur            x1, [fp, #-0x10]
    // 0x1369038: ArrayStore: r1[3] = r0  ; List_4
    //     0x1369038: add             x25, x1, #0x1b
    //     0x136903c: str             w0, [x25]
    //     0x1369040: tbz             w0, #0, #0x136905c
    //     0x1369044: ldurb           w16, [x1, #-1]
    //     0x1369048: ldurb           w17, [x0, #-1]
    //     0x136904c: and             x16, x17, x16, lsr #2
    //     0x1369050: tst             x16, HEAP, lsr #32
    //     0x1369054: b.eq            #0x136905c
    //     0x1369058: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x136905c: ldur            x0, [fp, #-0x10]
    // 0x1369060: r16 = "type"
    //     0x1369060: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x1369064: StoreField: r0->field_1f = r16
    //     0x1369064: stur            w16, [x0, #0x1f]
    // 0x1369068: ldur            x2, [fp, #-8]
    // 0x136906c: LoadField: r1 = r2->field_f
    //     0x136906c: ldur            w1, [x2, #0xf]
    // 0x1369070: DecompressPointer r1
    //     0x1369070: add             x1, x1, HEAP, lsl #32
    // 0x1369074: r0 = controller()
    //     0x1369074: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1369078: LoadField: r1 = r0->field_6b
    //     0x1369078: ldur            w1, [x0, #0x6b]
    // 0x136907c: DecompressPointer r1
    //     0x136907c: add             x1, x1, HEAP, lsl #32
    // 0x1369080: mov             x0, x1
    // 0x1369084: ldur            x1, [fp, #-0x10]
    // 0x1369088: ArrayStore: r1[5] = r0  ; List_4
    //     0x1369088: add             x25, x1, #0x23
    //     0x136908c: str             w0, [x25]
    //     0x1369090: tbz             w0, #0, #0x13690ac
    //     0x1369094: ldurb           w16, [x1, #-1]
    //     0x1369098: ldurb           w17, [x0, #-1]
    //     0x136909c: and             x16, x17, x16, lsr #2
    //     0x13690a0: tst             x16, HEAP, lsr #32
    //     0x13690a4: b.eq            #0x13690ac
    //     0x13690a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13690ac: ldur            x0, [fp, #-0x10]
    // 0x13690b0: r16 = "sku_short_id"
    //     0x13690b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a0] "sku_short_id"
    //     0x13690b4: ldr             x16, [x16, #0x4a0]
    // 0x13690b8: StoreField: r0->field_27 = r16
    //     0x13690b8: stur            w16, [x0, #0x27]
    // 0x13690bc: ldur            x2, [fp, #-8]
    // 0x13690c0: LoadField: r1 = r2->field_f
    //     0x13690c0: ldur            w1, [x2, #0xf]
    // 0x13690c4: DecompressPointer r1
    //     0x13690c4: add             x1, x1, HEAP, lsl #32
    // 0x13690c8: r0 = controller()
    //     0x13690c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13690cc: LoadField: r1 = r0->field_5b
    //     0x13690cc: ldur            w1, [x0, #0x5b]
    // 0x13690d0: DecompressPointer r1
    //     0x13690d0: add             x1, x1, HEAP, lsl #32
    // 0x13690d4: r0 = value()
    //     0x13690d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13690d8: ldur            x1, [fp, #-0x10]
    // 0x13690dc: ArrayStore: r1[7] = r0  ; List_4
    //     0x13690dc: add             x25, x1, #0x2b
    //     0x13690e0: str             w0, [x25]
    //     0x13690e4: tbz             w0, #0, #0x1369100
    //     0x13690e8: ldurb           w16, [x1, #-1]
    //     0x13690ec: ldurb           w17, [x0, #-1]
    //     0x13690f0: and             x16, x17, x16, lsr #2
    //     0x13690f4: tst             x16, HEAP, lsr #32
    //     0x13690f8: b.eq            #0x1369100
    //     0x13690fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1369100: ldur            x0, [fp, #-0x10]
    // 0x1369104: r16 = "customer_product_skus_id"
    //     0x1369104: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cd8] "customer_product_skus_id"
    //     0x1369108: ldr             x16, [x16, #0xcd8]
    // 0x136910c: StoreField: r0->field_2f = r16
    //     0x136910c: stur            w16, [x0, #0x2f]
    // 0x1369110: ldur            x1, [fp, #-8]
    // 0x1369114: LoadField: r2 = r1->field_f
    //     0x1369114: ldur            w2, [x1, #0xf]
    // 0x1369118: DecompressPointer r2
    //     0x1369118: add             x2, x2, HEAP, lsl #32
    // 0x136911c: mov             x1, x2
    // 0x1369120: r0 = controller()
    //     0x1369120: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1369124: LoadField: r1 = r0->field_5f
    //     0x1369124: ldur            w1, [x0, #0x5f]
    // 0x1369128: DecompressPointer r1
    //     0x1369128: add             x1, x1, HEAP, lsl #32
    // 0x136912c: mov             x0, x1
    // 0x1369130: ldur            x1, [fp, #-0x10]
    // 0x1369134: ArrayStore: r1[9] = r0  ; List_4
    //     0x1369134: add             x25, x1, #0x33
    //     0x1369138: str             w0, [x25]
    //     0x136913c: tbz             w0, #0, #0x1369158
    //     0x1369140: ldurb           w16, [x1, #-1]
    //     0x1369144: ldurb           w17, [x0, #-1]
    //     0x1369148: and             x16, x17, x16, lsr #2
    //     0x136914c: tst             x16, HEAP, lsr #32
    //     0x1369150: b.eq            #0x1369158
    //     0x1369154: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1369158: r16 = <String, Object?>
    //     0x1369158: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x136915c: ldr             x16, [x16, #0xc28]
    // 0x1369160: ldur            lr, [fp, #-0x10]
    // 0x1369164: stp             lr, x16, [SP]
    // 0x1369168: r0 = Map._fromLiteral()
    //     0x1369168: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x136916c: r16 = "/return-order"
    //     0x136916c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x1369170: ldr             x16, [x16, #0x8b8]
    // 0x1369174: stp             x16, NULL, [SP, #8]
    // 0x1369178: str             x0, [SP]
    // 0x136917c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x136917c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x1369180: ldr             x4, [x4, #0x438]
    // 0x1369184: r0 = GetNavigation.toNamed()
    //     0x1369184: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1369188: r0 = Null
    //     0x1369188: mov             x0, NULL
    // 0x136918c: LeaveFrame
    //     0x136918c: mov             SP, fp
    //     0x1369190: ldp             fp, lr, [SP], #0x10
    // 0x1369194: ret
    //     0x1369194: ret             
    // 0x1369198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1369198: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x136919c: b               #0x1368f68
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13cb5b4, size: 0x1c4
    // 0x13cb5b4: EnterFrame
    //     0x13cb5b4: stp             fp, lr, [SP, #-0x10]!
    //     0x13cb5b8: mov             fp, SP
    // 0x13cb5bc: AllocStack(0x18)
    //     0x13cb5bc: sub             SP, SP, #0x18
    // 0x13cb5c0: SetupParameters()
    //     0x13cb5c0: ldr             x0, [fp, #0x10]
    //     0x13cb5c4: ldur            w2, [x0, #0x17]
    //     0x13cb5c8: add             x2, x2, HEAP, lsl #32
    //     0x13cb5cc: stur            x2, [fp, #-0x10]
    // 0x13cb5d0: CheckStackOverflow
    //     0x13cb5d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cb5d4: cmp             SP, x16
    //     0x13cb5d8: b.ls            #0x13cb76c
    // 0x13cb5dc: LoadField: r0 = r2->field_b
    //     0x13cb5dc: ldur            w0, [x2, #0xb]
    // 0x13cb5e0: DecompressPointer r0
    //     0x13cb5e0: add             x0, x0, HEAP, lsl #32
    // 0x13cb5e4: stur            x0, [fp, #-8]
    // 0x13cb5e8: LoadField: r1 = r0->field_f
    //     0x13cb5e8: ldur            w1, [x0, #0xf]
    // 0x13cb5ec: DecompressPointer r1
    //     0x13cb5ec: add             x1, x1, HEAP, lsl #32
    // 0x13cb5f0: r0 = controller()
    //     0x13cb5f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb5f4: LoadField: r2 = r0->field_5b
    //     0x13cb5f4: ldur            w2, [x0, #0x5b]
    // 0x13cb5f8: DecompressPointer r2
    //     0x13cb5f8: add             x2, x2, HEAP, lsl #32
    // 0x13cb5fc: ldur            x0, [fp, #-8]
    // 0x13cb600: stur            x2, [fp, #-0x18]
    // 0x13cb604: LoadField: r1 = r0->field_f
    //     0x13cb604: ldur            w1, [x0, #0xf]
    // 0x13cb608: DecompressPointer r1
    //     0x13cb608: add             x1, x1, HEAP, lsl #32
    // 0x13cb60c: r0 = controller()
    //     0x13cb60c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb610: LoadField: r1 = r0->field_4b
    //     0x13cb610: ldur            w1, [x0, #0x4b]
    // 0x13cb614: DecompressPointer r1
    //     0x13cb614: add             x1, x1, HEAP, lsl #32
    // 0x13cb618: r0 = value()
    //     0x13cb618: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cb61c: LoadField: r1 = r0->field_b
    //     0x13cb61c: ldur            w1, [x0, #0xb]
    // 0x13cb620: DecompressPointer r1
    //     0x13cb620: add             x1, x1, HEAP, lsl #32
    // 0x13cb624: cmp             w1, NULL
    // 0x13cb628: b.ne            #0x13cb634
    // 0x13cb62c: r0 = Null
    //     0x13cb62c: mov             x0, NULL
    // 0x13cb630: b               #0x13cb69c
    // 0x13cb634: LoadField: r0 = r1->field_2f
    //     0x13cb634: ldur            w0, [x1, #0x2f]
    // 0x13cb638: DecompressPointer r0
    //     0x13cb638: add             x0, x0, HEAP, lsl #32
    // 0x13cb63c: cmp             w0, NULL
    // 0x13cb640: b.ne            #0x13cb64c
    // 0x13cb644: r0 = Null
    //     0x13cb644: mov             x0, NULL
    // 0x13cb648: b               #0x13cb69c
    // 0x13cb64c: ldur            x1, [fp, #-0x10]
    // 0x13cb650: LoadField: r2 = r0->field_b
    //     0x13cb650: ldur            w2, [x0, #0xb]
    // 0x13cb654: DecompressPointer r2
    //     0x13cb654: add             x2, x2, HEAP, lsl #32
    // 0x13cb658: LoadField: r0 = r1->field_13
    //     0x13cb658: ldur            w0, [x1, #0x13]
    // 0x13cb65c: DecompressPointer r0
    //     0x13cb65c: add             x0, x0, HEAP, lsl #32
    // 0x13cb660: LoadField: r1 = r2->field_b
    //     0x13cb660: ldur            w1, [x2, #0xb]
    // 0x13cb664: r3 = LoadInt32Instr(r0)
    //     0x13cb664: sbfx            x3, x0, #1, #0x1f
    //     0x13cb668: tbz             w0, #0, #0x13cb670
    //     0x13cb66c: ldur            x3, [x0, #7]
    // 0x13cb670: r0 = LoadInt32Instr(r1)
    //     0x13cb670: sbfx            x0, x1, #1, #0x1f
    // 0x13cb674: mov             x1, x3
    // 0x13cb678: cmp             x1, x0
    // 0x13cb67c: b.hs            #0x13cb774
    // 0x13cb680: LoadField: r0 = r2->field_f
    //     0x13cb680: ldur            w0, [x2, #0xf]
    // 0x13cb684: DecompressPointer r0
    //     0x13cb684: add             x0, x0, HEAP, lsl #32
    // 0x13cb688: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x13cb688: add             x16, x0, x3, lsl #2
    //     0x13cb68c: ldur            w1, [x16, #0xf]
    // 0x13cb690: DecompressPointer r1
    //     0x13cb690: add             x1, x1, HEAP, lsl #32
    // 0x13cb694: LoadField: r0 = r1->field_7
    //     0x13cb694: ldur            w0, [x1, #7]
    // 0x13cb698: DecompressPointer r0
    //     0x13cb698: add             x0, x0, HEAP, lsl #32
    // 0x13cb69c: cmp             w0, NULL
    // 0x13cb6a0: b.ne            #0x13cb6ac
    // 0x13cb6a4: r2 = ""
    //     0x13cb6a4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cb6a8: b               #0x13cb6b0
    // 0x13cb6ac: mov             x2, x0
    // 0x13cb6b0: ldur            x0, [fp, #-8]
    // 0x13cb6b4: ldur            x1, [fp, #-0x18]
    // 0x13cb6b8: r0 = value=()
    //     0x13cb6b8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13cb6bc: ldur            x0, [fp, #-8]
    // 0x13cb6c0: LoadField: r1 = r0->field_f
    //     0x13cb6c0: ldur            w1, [x0, #0xf]
    // 0x13cb6c4: DecompressPointer r1
    //     0x13cb6c4: add             x1, x1, HEAP, lsl #32
    // 0x13cb6c8: r0 = controller()
    //     0x13cb6c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb6cc: mov             x2, x0
    // 0x13cb6d0: ldur            x0, [fp, #-8]
    // 0x13cb6d4: stur            x2, [fp, #-0x10]
    // 0x13cb6d8: LoadField: r1 = r0->field_f
    //     0x13cb6d8: ldur            w1, [x0, #0xf]
    // 0x13cb6dc: DecompressPointer r1
    //     0x13cb6dc: add             x1, x1, HEAP, lsl #32
    // 0x13cb6e0: r0 = controller()
    //     0x13cb6e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb6e4: LoadField: r1 = r0->field_4b
    //     0x13cb6e4: ldur            w1, [x0, #0x4b]
    // 0x13cb6e8: DecompressPointer r1
    //     0x13cb6e8: add             x1, x1, HEAP, lsl #32
    // 0x13cb6ec: r0 = value()
    //     0x13cb6ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cb6f0: LoadField: r1 = r0->field_b
    //     0x13cb6f0: ldur            w1, [x0, #0xb]
    // 0x13cb6f4: DecompressPointer r1
    //     0x13cb6f4: add             x1, x1, HEAP, lsl #32
    // 0x13cb6f8: cmp             w1, NULL
    // 0x13cb6fc: b.ne            #0x13cb708
    // 0x13cb700: r1 = Null
    //     0x13cb700: mov             x1, NULL
    // 0x13cb704: b               #0x13cb728
    // 0x13cb708: LoadField: r2 = r1->field_2f
    //     0x13cb708: ldur            w2, [x1, #0x2f]
    // 0x13cb70c: DecompressPointer r2
    //     0x13cb70c: add             x2, x2, HEAP, lsl #32
    // 0x13cb710: cmp             w2, NULL
    // 0x13cb714: b.ne            #0x13cb720
    // 0x13cb718: r1 = Null
    //     0x13cb718: mov             x1, NULL
    // 0x13cb71c: b               #0x13cb728
    // 0x13cb720: LoadField: r1 = r2->field_7
    //     0x13cb720: ldur            w1, [x2, #7]
    // 0x13cb724: DecompressPointer r1
    //     0x13cb724: add             x1, x1, HEAP, lsl #32
    // 0x13cb728: cmp             w1, NULL
    // 0x13cb72c: b.ne            #0x13cb738
    // 0x13cb730: r0 = ""
    //     0x13cb730: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cb734: b               #0x13cb73c
    // 0x13cb738: mov             x0, x1
    // 0x13cb73c: ldur            x1, [fp, #-0x10]
    // 0x13cb740: StoreField: r1->field_5f = r0
    //     0x13cb740: stur            w0, [x1, #0x5f]
    //     0x13cb744: ldurb           w16, [x1, #-1]
    //     0x13cb748: ldurb           w17, [x0, #-1]
    //     0x13cb74c: and             x16, x17, x16, lsr #2
    //     0x13cb750: tst             x16, HEAP, lsr #32
    //     0x13cb754: b.eq            #0x13cb75c
    //     0x13cb758: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13cb75c: r0 = Null
    //     0x13cb75c: mov             x0, NULL
    // 0x13cb760: LeaveFrame
    //     0x13cb760: mov             SP, fp
    //     0x13cb764: ldp             fp, lr, [SP], #0x10
    // 0x13cb768: ret
    //     0x13cb768: ret             
    // 0x13cb76c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cb76c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cb770: b               #0x13cb5dc
    // 0x13cb774: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cb774: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] TextButtonTheme <anonymous closure>(dynamic) {
    // ** addr: 0x13cb778, size: 0x434
    // 0x13cb778: EnterFrame
    //     0x13cb778: stp             fp, lr, [SP, #-0x10]!
    //     0x13cb77c: mov             fp, SP
    // 0x13cb780: AllocStack(0x40)
    //     0x13cb780: sub             SP, SP, #0x40
    // 0x13cb784: SetupParameters()
    //     0x13cb784: ldr             x0, [fp, #0x10]
    //     0x13cb788: ldur            w2, [x0, #0x17]
    //     0x13cb78c: add             x2, x2, HEAP, lsl #32
    //     0x13cb790: stur            x2, [fp, #-0x10]
    // 0x13cb794: CheckStackOverflow
    //     0x13cb794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cb798: cmp             SP, x16
    //     0x13cb79c: b.ls            #0x13cbb9c
    // 0x13cb7a0: LoadField: r0 = r2->field_b
    //     0x13cb7a0: ldur            w0, [x2, #0xb]
    // 0x13cb7a4: DecompressPointer r0
    //     0x13cb7a4: add             x0, x0, HEAP, lsl #32
    // 0x13cb7a8: stur            x0, [fp, #-8]
    // 0x13cb7ac: LoadField: r1 = r0->field_f
    //     0x13cb7ac: ldur            w1, [x0, #0xf]
    // 0x13cb7b0: DecompressPointer r1
    //     0x13cb7b0: add             x1, x1, HEAP, lsl #32
    // 0x13cb7b4: r0 = controller()
    //     0x13cb7b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb7b8: LoadField: r2 = r0->field_5b
    //     0x13cb7b8: ldur            w2, [x0, #0x5b]
    // 0x13cb7bc: DecompressPointer r2
    //     0x13cb7bc: add             x2, x2, HEAP, lsl #32
    // 0x13cb7c0: ldur            x0, [fp, #-8]
    // 0x13cb7c4: stur            x2, [fp, #-0x18]
    // 0x13cb7c8: LoadField: r1 = r0->field_f
    //     0x13cb7c8: ldur            w1, [x0, #0xf]
    // 0x13cb7cc: DecompressPointer r1
    //     0x13cb7cc: add             x1, x1, HEAP, lsl #32
    // 0x13cb7d0: r0 = controller()
    //     0x13cb7d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb7d4: LoadField: r1 = r0->field_4b
    //     0x13cb7d4: ldur            w1, [x0, #0x4b]
    // 0x13cb7d8: DecompressPointer r1
    //     0x13cb7d8: add             x1, x1, HEAP, lsl #32
    // 0x13cb7dc: r0 = value()
    //     0x13cb7dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cb7e0: LoadField: r1 = r0->field_b
    //     0x13cb7e0: ldur            w1, [x0, #0xb]
    // 0x13cb7e4: DecompressPointer r1
    //     0x13cb7e4: add             x1, x1, HEAP, lsl #32
    // 0x13cb7e8: cmp             w1, NULL
    // 0x13cb7ec: b.ne            #0x13cb7fc
    // 0x13cb7f0: ldur            x3, [fp, #-0x10]
    // 0x13cb7f4: r0 = Null
    //     0x13cb7f4: mov             x0, NULL
    // 0x13cb7f8: b               #0x13cb868
    // 0x13cb7fc: LoadField: r0 = r1->field_2f
    //     0x13cb7fc: ldur            w0, [x1, #0x2f]
    // 0x13cb800: DecompressPointer r0
    //     0x13cb800: add             x0, x0, HEAP, lsl #32
    // 0x13cb804: cmp             w0, NULL
    // 0x13cb808: b.ne            #0x13cb818
    // 0x13cb80c: ldur            x3, [fp, #-0x10]
    // 0x13cb810: r0 = Null
    //     0x13cb810: mov             x0, NULL
    // 0x13cb814: b               #0x13cb868
    // 0x13cb818: ldur            x3, [fp, #-0x10]
    // 0x13cb81c: LoadField: r2 = r0->field_b
    //     0x13cb81c: ldur            w2, [x0, #0xb]
    // 0x13cb820: DecompressPointer r2
    //     0x13cb820: add             x2, x2, HEAP, lsl #32
    // 0x13cb824: LoadField: r0 = r3->field_13
    //     0x13cb824: ldur            w0, [x3, #0x13]
    // 0x13cb828: DecompressPointer r0
    //     0x13cb828: add             x0, x0, HEAP, lsl #32
    // 0x13cb82c: LoadField: r1 = r2->field_b
    //     0x13cb82c: ldur            w1, [x2, #0xb]
    // 0x13cb830: r4 = LoadInt32Instr(r0)
    //     0x13cb830: sbfx            x4, x0, #1, #0x1f
    //     0x13cb834: tbz             w0, #0, #0x13cb83c
    //     0x13cb838: ldur            x4, [x0, #7]
    // 0x13cb83c: r0 = LoadInt32Instr(r1)
    //     0x13cb83c: sbfx            x0, x1, #1, #0x1f
    // 0x13cb840: mov             x1, x4
    // 0x13cb844: cmp             x1, x0
    // 0x13cb848: b.hs            #0x13cbba4
    // 0x13cb84c: LoadField: r0 = r2->field_f
    //     0x13cb84c: ldur            w0, [x2, #0xf]
    // 0x13cb850: DecompressPointer r0
    //     0x13cb850: add             x0, x0, HEAP, lsl #32
    // 0x13cb854: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cb854: add             x16, x0, x4, lsl #2
    //     0x13cb858: ldur            w1, [x16, #0xf]
    // 0x13cb85c: DecompressPointer r1
    //     0x13cb85c: add             x1, x1, HEAP, lsl #32
    // 0x13cb860: LoadField: r0 = r1->field_7
    //     0x13cb860: ldur            w0, [x1, #7]
    // 0x13cb864: DecompressPointer r0
    //     0x13cb864: add             x0, x0, HEAP, lsl #32
    // 0x13cb868: cmp             w0, NULL
    // 0x13cb86c: b.ne            #0x13cb878
    // 0x13cb870: r2 = ""
    //     0x13cb870: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cb874: b               #0x13cb87c
    // 0x13cb878: mov             x2, x0
    // 0x13cb87c: ldur            x1, [fp, #-0x18]
    // 0x13cb880: r0 = RxStringExt.contains()
    //     0x13cb880: bl              #0x13cb554  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.contains
    // 0x13cb884: tbnz            w0, #4, #0x13cb93c
    // 0x13cb888: r16 = <EdgeInsets>
    //     0x13cb888: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13cb88c: ldr             x16, [x16, #0xda0]
    // 0x13cb890: r30 = Instance_EdgeInsets
    //     0x13cb890: add             lr, PP, #0x38, lsl #12  ; [pp+0x38140] Obj!EdgeInsets@d59c91
    //     0x13cb894: ldr             lr, [lr, #0x140]
    // 0x13cb898: stp             lr, x16, [SP]
    // 0x13cb89c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb89c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb8a0: r0 = all()
    //     0x13cb8a0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb8a4: r1 = Instance_Color
    //     0x13cb8a4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb8a8: d0 = 0.050000
    //     0x13cb8a8: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x13cb8ac: stur            x0, [fp, #-0x18]
    // 0x13cb8b0: r0 = withOpacity()
    //     0x13cb8b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb8b4: r16 = <Color>
    //     0x13cb8b4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb8b8: ldr             x16, [x16, #0xf80]
    // 0x13cb8bc: stp             x0, x16, [SP]
    // 0x13cb8c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb8c0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb8c4: r0 = all()
    //     0x13cb8c4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb8c8: r1 = Instance_Color
    //     0x13cb8c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb8cc: d0 = 0.050000
    //     0x13cb8cc: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x13cb8d0: stur            x0, [fp, #-0x20]
    // 0x13cb8d4: r0 = withOpacity()
    //     0x13cb8d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb8d8: r16 = <Color>
    //     0x13cb8d8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb8dc: ldr             x16, [x16, #0xf80]
    // 0x13cb8e0: stp             x0, x16, [SP]
    // 0x13cb8e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb8e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb8e8: r0 = all()
    //     0x13cb8e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb8ec: stur            x0, [fp, #-0x28]
    // 0x13cb8f0: r16 = <RoundedRectangleBorder>
    //     0x13cb8f0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13cb8f4: ldr             x16, [x16, #0xf78]
    // 0x13cb8f8: r30 = Instance_RoundedRectangleBorder
    //     0x13cb8f8: add             lr, PP, #0x38, lsl #12  ; [pp+0x38148] Obj!RoundedRectangleBorder@d5acf1
    //     0x13cb8fc: ldr             lr, [lr, #0x148]
    // 0x13cb900: stp             lr, x16, [SP]
    // 0x13cb904: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb904: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb908: r0 = all()
    //     0x13cb908: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb90c: stur            x0, [fp, #-0x30]
    // 0x13cb910: r0 = ButtonStyle()
    //     0x13cb910: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13cb914: mov             x1, x0
    // 0x13cb918: ldur            x0, [fp, #-0x28]
    // 0x13cb91c: StoreField: r1->field_b = r0
    //     0x13cb91c: stur            w0, [x1, #0xb]
    // 0x13cb920: ldur            x0, [fp, #-0x20]
    // 0x13cb924: StoreField: r1->field_f = r0
    //     0x13cb924: stur            w0, [x1, #0xf]
    // 0x13cb928: ldur            x0, [fp, #-0x18]
    // 0x13cb92c: StoreField: r1->field_23 = r0
    //     0x13cb92c: stur            w0, [x1, #0x23]
    // 0x13cb930: ldur            x0, [fp, #-0x30]
    // 0x13cb934: StoreField: r1->field_43 = r0
    //     0x13cb934: stur            w0, [x1, #0x43]
    // 0x13cb938: b               #0x13cb9ec
    // 0x13cb93c: r16 = <EdgeInsets>
    //     0x13cb93c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13cb940: ldr             x16, [x16, #0xda0]
    // 0x13cb944: r30 = Instance_EdgeInsets
    //     0x13cb944: add             lr, PP, #0x38, lsl #12  ; [pp+0x38140] Obj!EdgeInsets@d59c91
    //     0x13cb948: ldr             lr, [lr, #0x140]
    // 0x13cb94c: stp             lr, x16, [SP]
    // 0x13cb950: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb950: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb954: r0 = all()
    //     0x13cb954: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb958: r1 = Instance_Color
    //     0x13cb958: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb95c: d0 = 0.050000
    //     0x13cb95c: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x13cb960: stur            x0, [fp, #-0x18]
    // 0x13cb964: r0 = withOpacity()
    //     0x13cb964: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb968: r16 = <Color>
    //     0x13cb968: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb96c: ldr             x16, [x16, #0xf80]
    // 0x13cb970: stp             x0, x16, [SP]
    // 0x13cb974: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb974: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb978: r0 = all()
    //     0x13cb978: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb97c: r1 = Instance_Color
    //     0x13cb97c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb980: d0 = 0.050000
    //     0x13cb980: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x13cb984: stur            x0, [fp, #-0x20]
    // 0x13cb988: r0 = withOpacity()
    //     0x13cb988: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb98c: r16 = <Color>
    //     0x13cb98c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb990: ldr             x16, [x16, #0xf80]
    // 0x13cb994: stp             x0, x16, [SP]
    // 0x13cb998: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb998: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb99c: r0 = all()
    //     0x13cb99c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb9a0: stur            x0, [fp, #-0x28]
    // 0x13cb9a4: r16 = <RoundedRectangleBorder>
    //     0x13cb9a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13cb9a8: ldr             x16, [x16, #0xf78]
    // 0x13cb9ac: r30 = Instance_RoundedRectangleBorder
    //     0x13cb9ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x13cb9b0: ldr             lr, [lr, #0xd68]
    // 0x13cb9b4: stp             lr, x16, [SP]
    // 0x13cb9b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb9b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb9bc: r0 = all()
    //     0x13cb9bc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb9c0: stur            x0, [fp, #-0x30]
    // 0x13cb9c4: r0 = ButtonStyle()
    //     0x13cb9c4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13cb9c8: mov             x1, x0
    // 0x13cb9cc: ldur            x0, [fp, #-0x28]
    // 0x13cb9d0: StoreField: r1->field_b = r0
    //     0x13cb9d0: stur            w0, [x1, #0xb]
    // 0x13cb9d4: ldur            x0, [fp, #-0x20]
    // 0x13cb9d8: StoreField: r1->field_f = r0
    //     0x13cb9d8: stur            w0, [x1, #0xf]
    // 0x13cb9dc: ldur            x0, [fp, #-0x18]
    // 0x13cb9e0: StoreField: r1->field_23 = r0
    //     0x13cb9e0: stur            w0, [x1, #0x23]
    // 0x13cb9e4: ldur            x0, [fp, #-0x30]
    // 0x13cb9e8: StoreField: r1->field_43 = r0
    //     0x13cb9e8: stur            w0, [x1, #0x43]
    // 0x13cb9ec: ldur            x0, [fp, #-8]
    // 0x13cb9f0: stur            x1, [fp, #-0x18]
    // 0x13cb9f4: r0 = TextButtonThemeData()
    //     0x13cb9f4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x13cb9f8: mov             x2, x0
    // 0x13cb9fc: ldur            x0, [fp, #-0x18]
    // 0x13cba00: stur            x2, [fp, #-0x20]
    // 0x13cba04: StoreField: r2->field_7 = r0
    //     0x13cba04: stur            w0, [x2, #7]
    // 0x13cba08: ldur            x0, [fp, #-8]
    // 0x13cba0c: LoadField: r1 = r0->field_f
    //     0x13cba0c: ldur            w1, [x0, #0xf]
    // 0x13cba10: DecompressPointer r1
    //     0x13cba10: add             x1, x1, HEAP, lsl #32
    // 0x13cba14: r0 = controller()
    //     0x13cba14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cba18: LoadField: r1 = r0->field_4b
    //     0x13cba18: ldur            w1, [x0, #0x4b]
    // 0x13cba1c: DecompressPointer r1
    //     0x13cba1c: add             x1, x1, HEAP, lsl #32
    // 0x13cba20: r0 = value()
    //     0x13cba20: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cba24: LoadField: r1 = r0->field_b
    //     0x13cba24: ldur            w1, [x0, #0xb]
    // 0x13cba28: DecompressPointer r1
    //     0x13cba28: add             x1, x1, HEAP, lsl #32
    // 0x13cba2c: cmp             w1, NULL
    // 0x13cba30: b.ne            #0x13cba40
    // 0x13cba34: ldur            x2, [fp, #-0x10]
    // 0x13cba38: r1 = Null
    //     0x13cba38: mov             x1, NULL
    // 0x13cba3c: b               #0x13cbab0
    // 0x13cba40: LoadField: r0 = r1->field_2f
    //     0x13cba40: ldur            w0, [x1, #0x2f]
    // 0x13cba44: DecompressPointer r0
    //     0x13cba44: add             x0, x0, HEAP, lsl #32
    // 0x13cba48: cmp             w0, NULL
    // 0x13cba4c: b.ne            #0x13cba5c
    // 0x13cba50: ldur            x2, [fp, #-0x10]
    // 0x13cba54: r0 = Null
    //     0x13cba54: mov             x0, NULL
    // 0x13cba58: b               #0x13cbaac
    // 0x13cba5c: ldur            x2, [fp, #-0x10]
    // 0x13cba60: LoadField: r3 = r0->field_b
    //     0x13cba60: ldur            w3, [x0, #0xb]
    // 0x13cba64: DecompressPointer r3
    //     0x13cba64: add             x3, x3, HEAP, lsl #32
    // 0x13cba68: LoadField: r0 = r2->field_13
    //     0x13cba68: ldur            w0, [x2, #0x13]
    // 0x13cba6c: DecompressPointer r0
    //     0x13cba6c: add             x0, x0, HEAP, lsl #32
    // 0x13cba70: LoadField: r1 = r3->field_b
    //     0x13cba70: ldur            w1, [x3, #0xb]
    // 0x13cba74: r4 = LoadInt32Instr(r0)
    //     0x13cba74: sbfx            x4, x0, #1, #0x1f
    //     0x13cba78: tbz             w0, #0, #0x13cba80
    //     0x13cba7c: ldur            x4, [x0, #7]
    // 0x13cba80: r0 = LoadInt32Instr(r1)
    //     0x13cba80: sbfx            x0, x1, #1, #0x1f
    // 0x13cba84: mov             x1, x4
    // 0x13cba88: cmp             x1, x0
    // 0x13cba8c: b.hs            #0x13cbba8
    // 0x13cba90: LoadField: r0 = r3->field_f
    //     0x13cba90: ldur            w0, [x3, #0xf]
    // 0x13cba94: DecompressPointer r0
    //     0x13cba94: add             x0, x0, HEAP, lsl #32
    // 0x13cba98: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cba98: add             x16, x0, x4, lsl #2
    //     0x13cba9c: ldur            w1, [x16, #0xf]
    // 0x13cbaa0: DecompressPointer r1
    //     0x13cbaa0: add             x1, x1, HEAP, lsl #32
    // 0x13cbaa4: LoadField: r0 = r1->field_f
    //     0x13cbaa4: ldur            w0, [x1, #0xf]
    // 0x13cbaa8: DecompressPointer r0
    //     0x13cbaa8: add             x0, x0, HEAP, lsl #32
    // 0x13cbaac: mov             x1, x0
    // 0x13cbab0: ldur            x0, [fp, #-0x20]
    // 0x13cbab4: str             x1, [SP]
    // 0x13cbab8: r0 = _interpolateSingle()
    //     0x13cbab8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x13cbabc: ldur            x2, [fp, #-0x10]
    // 0x13cbac0: stur            x0, [fp, #-8]
    // 0x13cbac4: LoadField: r1 = r2->field_f
    //     0x13cbac4: ldur            w1, [x2, #0xf]
    // 0x13cbac8: DecompressPointer r1
    //     0x13cbac8: add             x1, x1, HEAP, lsl #32
    // 0x13cbacc: r0 = of()
    //     0x13cbacc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cbad0: LoadField: r1 = r0->field_87
    //     0x13cbad0: ldur            w1, [x0, #0x87]
    // 0x13cbad4: DecompressPointer r1
    //     0x13cbad4: add             x1, x1, HEAP, lsl #32
    // 0x13cbad8: LoadField: r0 = r1->field_2b
    //     0x13cbad8: ldur            w0, [x1, #0x2b]
    // 0x13cbadc: DecompressPointer r0
    //     0x13cbadc: add             x0, x0, HEAP, lsl #32
    // 0x13cbae0: stur            x0, [fp, #-0x18]
    // 0x13cbae4: r1 = Instance_Color
    //     0x13cbae4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cbae8: d0 = 0.700000
    //     0x13cbae8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13cbaec: ldr             d0, [x17, #0xf48]
    // 0x13cbaf0: r0 = withOpacity()
    //     0x13cbaf0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cbaf4: r16 = 14.000000
    //     0x13cbaf4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13cbaf8: ldr             x16, [x16, #0x1d8]
    // 0x13cbafc: stp             x16, x0, [SP]
    // 0x13cbb00: ldur            x1, [fp, #-0x18]
    // 0x13cbb04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cbb04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cbb08: ldr             x4, [x4, #0x9b8]
    // 0x13cbb0c: r0 = copyWith()
    //     0x13cbb0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cbb10: stur            x0, [fp, #-0x18]
    // 0x13cbb14: r0 = Text()
    //     0x13cbb14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cbb18: mov             x3, x0
    // 0x13cbb1c: ldur            x0, [fp, #-8]
    // 0x13cbb20: stur            x3, [fp, #-0x28]
    // 0x13cbb24: StoreField: r3->field_b = r0
    //     0x13cbb24: stur            w0, [x3, #0xb]
    // 0x13cbb28: ldur            x0, [fp, #-0x18]
    // 0x13cbb2c: StoreField: r3->field_13 = r0
    //     0x13cbb2c: stur            w0, [x3, #0x13]
    // 0x13cbb30: r0 = Instance_TextOverflow
    //     0x13cbb30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x13cbb34: ldr             x0, [x0, #0xe10]
    // 0x13cbb38: StoreField: r3->field_2b = r0
    //     0x13cbb38: stur            w0, [x3, #0x2b]
    // 0x13cbb3c: ldur            x2, [fp, #-0x10]
    // 0x13cbb40: r1 = Function '<anonymous closure>':.
    //     0x13cbb40: add             x1, PP, #0x38, lsl #12  ; [pp+0x38150] AnonymousClosure: (0x13cb5b4), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13cbb44: ldr             x1, [x1, #0x150]
    // 0x13cbb48: r0 = AllocateClosure()
    //     0x13cbb48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cbb4c: stur            x0, [fp, #-8]
    // 0x13cbb50: r0 = TextButton()
    //     0x13cbb50: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13cbb54: mov             x1, x0
    // 0x13cbb58: ldur            x0, [fp, #-8]
    // 0x13cbb5c: stur            x1, [fp, #-0x10]
    // 0x13cbb60: StoreField: r1->field_b = r0
    //     0x13cbb60: stur            w0, [x1, #0xb]
    // 0x13cbb64: r0 = false
    //     0x13cbb64: add             x0, NULL, #0x30  ; false
    // 0x13cbb68: StoreField: r1->field_27 = r0
    //     0x13cbb68: stur            w0, [x1, #0x27]
    // 0x13cbb6c: r0 = true
    //     0x13cbb6c: add             x0, NULL, #0x20  ; true
    // 0x13cbb70: StoreField: r1->field_2f = r0
    //     0x13cbb70: stur            w0, [x1, #0x2f]
    // 0x13cbb74: ldur            x0, [fp, #-0x28]
    // 0x13cbb78: StoreField: r1->field_37 = r0
    //     0x13cbb78: stur            w0, [x1, #0x37]
    // 0x13cbb7c: r0 = TextButtonTheme()
    //     0x13cbb7c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13cbb80: ldur            x1, [fp, #-0x20]
    // 0x13cbb84: StoreField: r0->field_f = r1
    //     0x13cbb84: stur            w1, [x0, #0xf]
    // 0x13cbb88: ldur            x1, [fp, #-0x10]
    // 0x13cbb8c: StoreField: r0->field_b = r1
    //     0x13cbb8c: stur            w1, [x0, #0xb]
    // 0x13cbb90: LeaveFrame
    //     0x13cbb90: mov             SP, fp
    //     0x13cbb94: ldp             fp, lr, [SP], #0x10
    // 0x13cbb98: ret
    //     0x13cbb98: ret             
    // 0x13cbb9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cbb9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cbba0: b               #0x13cb7a0
    // 0x13cbba4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cbba4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13cbba8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cbba8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x13cbbac, size: 0x74
    // 0x13cbbac: EnterFrame
    //     0x13cbbac: stp             fp, lr, [SP, #-0x10]!
    //     0x13cbbb0: mov             fp, SP
    // 0x13cbbb4: AllocStack(0x10)
    //     0x13cbbb4: sub             SP, SP, #0x10
    // 0x13cbbb8: SetupParameters()
    //     0x13cbbb8: ldr             x0, [fp, #0x20]
    //     0x13cbbbc: ldur            w1, [x0, #0x17]
    //     0x13cbbc0: add             x1, x1, HEAP, lsl #32
    //     0x13cbbc4: stur            x1, [fp, #-8]
    // 0x13cbbc8: r1 = 2
    //     0x13cbbc8: movz            x1, #0x2
    // 0x13cbbcc: r0 = AllocateContext()
    //     0x13cbbcc: bl              #0x16f6108  ; AllocateContextStub
    // 0x13cbbd0: mov             x1, x0
    // 0x13cbbd4: ldur            x0, [fp, #-8]
    // 0x13cbbd8: stur            x1, [fp, #-0x10]
    // 0x13cbbdc: StoreField: r1->field_b = r0
    //     0x13cbbdc: stur            w0, [x1, #0xb]
    // 0x13cbbe0: ldr             x0, [fp, #0x18]
    // 0x13cbbe4: StoreField: r1->field_f = r0
    //     0x13cbbe4: stur            w0, [x1, #0xf]
    // 0x13cbbe8: ldr             x0, [fp, #0x10]
    // 0x13cbbec: StoreField: r1->field_13 = r0
    //     0x13cbbec: stur            w0, [x1, #0x13]
    // 0x13cbbf0: r0 = Obx()
    //     0x13cbbf0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13cbbf4: ldur            x2, [fp, #-0x10]
    // 0x13cbbf8: r1 = Function '<anonymous closure>':.
    //     0x13cbbf8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38138] AnonymousClosure: (0x13cb778), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13cbbfc: ldr             x1, [x1, #0x138]
    // 0x13cbc00: stur            x0, [fp, #-8]
    // 0x13cbc04: r0 = AllocateClosure()
    //     0x13cbc04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cbc08: mov             x1, x0
    // 0x13cbc0c: ldur            x0, [fp, #-8]
    // 0x13cbc10: StoreField: r0->field_b = r1
    //     0x13cbc10: stur            w1, [x0, #0xb]
    // 0x13cbc14: LeaveFrame
    //     0x13cbc14: mov             SP, fp
    //     0x13cbc18: ldp             fp, lr, [SP], #0x10
    // 0x13cbc1c: ret
    //     0x13cbc1c: ret             
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x13cbc20, size: 0x15f4
    // 0x13cbc20: EnterFrame
    //     0x13cbc20: stp             fp, lr, [SP, #-0x10]!
    //     0x13cbc24: mov             fp, SP
    // 0x13cbc28: AllocStack(0x88)
    //     0x13cbc28: sub             SP, SP, #0x88
    // 0x13cbc2c: SetupParameters()
    //     0x13cbc2c: ldr             x0, [fp, #0x10]
    //     0x13cbc30: ldur            w2, [x0, #0x17]
    //     0x13cbc34: add             x2, x2, HEAP, lsl #32
    //     0x13cbc38: stur            x2, [fp, #-8]
    // 0x13cbc3c: CheckStackOverflow
    //     0x13cbc3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cbc40: cmp             SP, x16
    //     0x13cbc44: b.ls            #0x13cd1e0
    // 0x13cbc48: LoadField: r1 = r2->field_13
    //     0x13cbc48: ldur            w1, [x2, #0x13]
    // 0x13cbc4c: DecompressPointer r1
    //     0x13cbc4c: add             x1, x1, HEAP, lsl #32
    // 0x13cbc50: r0 = of()
    //     0x13cbc50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cbc54: LoadField: r1 = r0->field_5b
    //     0x13cbc54: ldur            w1, [x0, #0x5b]
    // 0x13cbc58: DecompressPointer r1
    //     0x13cbc58: add             x1, x1, HEAP, lsl #32
    // 0x13cbc5c: r0 = LoadClassIdInstr(r1)
    //     0x13cbc5c: ldur            x0, [x1, #-1]
    //     0x13cbc60: ubfx            x0, x0, #0xc, #0x14
    // 0x13cbc64: d0 = 0.100000
    //     0x13cbc64: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13cbc68: r0 = GDT[cid_x0 + -0xffa]()
    //     0x13cbc68: sub             lr, x0, #0xffa
    //     0x13cbc6c: ldr             lr, [x21, lr, lsl #3]
    //     0x13cbc70: blr             lr
    // 0x13cbc74: mov             x2, x0
    // 0x13cbc78: r1 = Null
    //     0x13cbc78: mov             x1, NULL
    // 0x13cbc7c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13cbc7c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13cbc80: r0 = Border.all()
    //     0x13cbc80: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13cbc84: stur            x0, [fp, #-0x10]
    // 0x13cbc88: r0 = BoxDecoration()
    //     0x13cbc88: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13cbc8c: mov             x2, x0
    // 0x13cbc90: ldur            x0, [fp, #-0x10]
    // 0x13cbc94: stur            x2, [fp, #-0x18]
    // 0x13cbc98: StoreField: r2->field_f = r0
    //     0x13cbc98: stur            w0, [x2, #0xf]
    // 0x13cbc9c: r0 = Instance_BoxShape
    //     0x13cbc9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13cbca0: ldr             x0, [x0, #0x80]
    // 0x13cbca4: StoreField: r2->field_23 = r0
    //     0x13cbca4: stur            w0, [x2, #0x23]
    // 0x13cbca8: ldur            x3, [fp, #-8]
    // 0x13cbcac: LoadField: r1 = r3->field_f
    //     0x13cbcac: ldur            w1, [x3, #0xf]
    // 0x13cbcb0: DecompressPointer r1
    //     0x13cbcb0: add             x1, x1, HEAP, lsl #32
    // 0x13cbcb4: r0 = controller()
    //     0x13cbcb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cbcb8: LoadField: r1 = r0->field_4b
    //     0x13cbcb8: ldur            w1, [x0, #0x4b]
    // 0x13cbcbc: DecompressPointer r1
    //     0x13cbcbc: add             x1, x1, HEAP, lsl #32
    // 0x13cbcc0: r0 = value()
    //     0x13cbcc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cbcc4: LoadField: r1 = r0->field_b
    //     0x13cbcc4: ldur            w1, [x0, #0xb]
    // 0x13cbcc8: DecompressPointer r1
    //     0x13cbcc8: add             x1, x1, HEAP, lsl #32
    // 0x13cbccc: cmp             w1, NULL
    // 0x13cbcd0: b.ne            #0x13cbcdc
    // 0x13cbcd4: r0 = Null
    //     0x13cbcd4: mov             x0, NULL
    // 0x13cbcd8: b               #0x13cbd00
    // 0x13cbcdc: LoadField: r0 = r1->field_f
    //     0x13cbcdc: ldur            w0, [x1, #0xf]
    // 0x13cbce0: DecompressPointer r0
    //     0x13cbce0: add             x0, x0, HEAP, lsl #32
    // 0x13cbce4: cmp             w0, NULL
    // 0x13cbce8: b.ne            #0x13cbcf4
    // 0x13cbcec: r0 = Null
    //     0x13cbcec: mov             x0, NULL
    // 0x13cbcf0: b               #0x13cbd00
    // 0x13cbcf4: LoadField: r1 = r0->field_7
    //     0x13cbcf4: ldur            w1, [x0, #7]
    // 0x13cbcf8: DecompressPointer r1
    //     0x13cbcf8: add             x1, x1, HEAP, lsl #32
    // 0x13cbcfc: mov             x0, x1
    // 0x13cbd00: cmp             w0, NULL
    // 0x13cbd04: b.ne            #0x13cbd0c
    // 0x13cbd08: r0 = ""
    //     0x13cbd08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cbd0c: ldur            x2, [fp, #-8]
    // 0x13cbd10: stur            x0, [fp, #-0x10]
    // 0x13cbd14: r0 = ImageHeaders.forImages()
    //     0x13cbd14: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x13cbd18: r1 = Function '<anonymous closure>':.
    //     0x13cbd18: add             x1, PP, #0x38, lsl #12  ; [pp+0x38098] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x13cbd1c: ldr             x1, [x1, #0x98]
    // 0x13cbd20: r2 = Null
    //     0x13cbd20: mov             x2, NULL
    // 0x13cbd24: stur            x0, [fp, #-0x20]
    // 0x13cbd28: r0 = AllocateClosure()
    //     0x13cbd28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cbd2c: r1 = Function '<anonymous closure>':.
    //     0x13cbd2c: add             x1, PP, #0x38, lsl #12  ; [pp+0x380a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x13cbd30: ldr             x1, [x1, #0xa0]
    // 0x13cbd34: r2 = Null
    //     0x13cbd34: mov             x2, NULL
    // 0x13cbd38: stur            x0, [fp, #-0x28]
    // 0x13cbd3c: r0 = AllocateClosure()
    //     0x13cbd3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cbd40: stur            x0, [fp, #-0x30]
    // 0x13cbd44: r0 = CachedNetworkImage()
    //     0x13cbd44: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13cbd48: stur            x0, [fp, #-0x38]
    // 0x13cbd4c: r16 = 84.000000
    //     0x13cbd4c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0x13cbd50: ldr             x16, [x16, #0xf90]
    // 0x13cbd54: r30 = 56.000000
    //     0x13cbd54: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x13cbd58: ldr             lr, [lr, #0xb78]
    // 0x13cbd5c: stp             lr, x16, [SP, #0x20]
    // 0x13cbd60: r16 = Instance_BoxFit
    //     0x13cbd60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x13cbd64: ldr             x16, [x16, #0x118]
    // 0x13cbd68: ldur            lr, [fp, #-0x20]
    // 0x13cbd6c: stp             lr, x16, [SP, #0x10]
    // 0x13cbd70: ldur            x16, [fp, #-0x28]
    // 0x13cbd74: ldur            lr, [fp, #-0x30]
    // 0x13cbd78: stp             lr, x16, [SP]
    // 0x13cbd7c: mov             x1, x0
    // 0x13cbd80: ldur            x2, [fp, #-0x10]
    // 0x13cbd84: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x4, height, 0x2, httpHeaders, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0x13cbd84: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f120] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x4, "height", 0x2, "httpHeaders", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0x13cbd88: ldr             x4, [x4, #0x120]
    // 0x13cbd8c: r0 = CachedNetworkImage()
    //     0x13cbd8c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13cbd90: ldur            x2, [fp, #-8]
    // 0x13cbd94: LoadField: r1 = r2->field_13
    //     0x13cbd94: ldur            w1, [x2, #0x13]
    // 0x13cbd98: DecompressPointer r1
    //     0x13cbd98: add             x1, x1, HEAP, lsl #32
    // 0x13cbd9c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13cbd9c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13cbda0: r0 = _of()
    //     0x13cbda0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13cbda4: LoadField: r1 = r0->field_7
    //     0x13cbda4: ldur            w1, [x0, #7]
    // 0x13cbda8: DecompressPointer r1
    //     0x13cbda8: add             x1, x1, HEAP, lsl #32
    // 0x13cbdac: LoadField: d0 = r1->field_7
    //     0x13cbdac: ldur            d0, [x1, #7]
    // 0x13cbdb0: d1 = 0.500000
    //     0x13cbdb0: fmov            d1, #0.50000000
    // 0x13cbdb4: fmul            d2, d0, d1
    // 0x13cbdb8: ldur            x2, [fp, #-8]
    // 0x13cbdbc: stur            d2, [fp, #-0x58]
    // 0x13cbdc0: LoadField: r1 = r2->field_f
    //     0x13cbdc0: ldur            w1, [x2, #0xf]
    // 0x13cbdc4: DecompressPointer r1
    //     0x13cbdc4: add             x1, x1, HEAP, lsl #32
    // 0x13cbdc8: r0 = controller()
    //     0x13cbdc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cbdcc: LoadField: r1 = r0->field_4b
    //     0x13cbdcc: ldur            w1, [x0, #0x4b]
    // 0x13cbdd0: DecompressPointer r1
    //     0x13cbdd0: add             x1, x1, HEAP, lsl #32
    // 0x13cbdd4: r0 = value()
    //     0x13cbdd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cbdd8: LoadField: r1 = r0->field_b
    //     0x13cbdd8: ldur            w1, [x0, #0xb]
    // 0x13cbddc: DecompressPointer r1
    //     0x13cbddc: add             x1, x1, HEAP, lsl #32
    // 0x13cbde0: cmp             w1, NULL
    // 0x13cbde4: b.ne            #0x13cbdf0
    // 0x13cbde8: r0 = Null
    //     0x13cbde8: mov             x0, NULL
    // 0x13cbdec: b               #0x13cbe14
    // 0x13cbdf0: LoadField: r0 = r1->field_f
    //     0x13cbdf0: ldur            w0, [x1, #0xf]
    // 0x13cbdf4: DecompressPointer r0
    //     0x13cbdf4: add             x0, x0, HEAP, lsl #32
    // 0x13cbdf8: cmp             w0, NULL
    // 0x13cbdfc: b.ne            #0x13cbe08
    // 0x13cbe00: r0 = Null
    //     0x13cbe00: mov             x0, NULL
    // 0x13cbe04: b               #0x13cbe14
    // 0x13cbe08: LoadField: r1 = r0->field_b
    //     0x13cbe08: ldur            w1, [x0, #0xb]
    // 0x13cbe0c: DecompressPointer r1
    //     0x13cbe0c: add             x1, x1, HEAP, lsl #32
    // 0x13cbe10: mov             x0, x1
    // 0x13cbe14: cmp             w0, NULL
    // 0x13cbe18: b.ne            #0x13cbe20
    // 0x13cbe1c: r0 = ""
    //     0x13cbe1c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cbe20: ldur            x2, [fp, #-8]
    // 0x13cbe24: stur            x0, [fp, #-0x10]
    // 0x13cbe28: LoadField: r1 = r2->field_13
    //     0x13cbe28: ldur            w1, [x2, #0x13]
    // 0x13cbe2c: DecompressPointer r1
    //     0x13cbe2c: add             x1, x1, HEAP, lsl #32
    // 0x13cbe30: r0 = of()
    //     0x13cbe30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cbe34: LoadField: r1 = r0->field_87
    //     0x13cbe34: ldur            w1, [x0, #0x87]
    // 0x13cbe38: DecompressPointer r1
    //     0x13cbe38: add             x1, x1, HEAP, lsl #32
    // 0x13cbe3c: LoadField: r0 = r1->field_7
    //     0x13cbe3c: ldur            w0, [x1, #7]
    // 0x13cbe40: DecompressPointer r0
    //     0x13cbe40: add             x0, x0, HEAP, lsl #32
    // 0x13cbe44: r16 = Instance_Color
    //     0x13cbe44: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cbe48: r30 = 12.000000
    //     0x13cbe48: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cbe4c: ldr             lr, [lr, #0x9e8]
    // 0x13cbe50: stp             lr, x16, [SP]
    // 0x13cbe54: mov             x1, x0
    // 0x13cbe58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cbe58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cbe5c: ldr             x4, [x4, #0x9b8]
    // 0x13cbe60: r0 = copyWith()
    //     0x13cbe60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cbe64: stur            x0, [fp, #-0x20]
    // 0x13cbe68: r0 = Text()
    //     0x13cbe68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cbe6c: mov             x2, x0
    // 0x13cbe70: ldur            x0, [fp, #-0x10]
    // 0x13cbe74: stur            x2, [fp, #-0x28]
    // 0x13cbe78: StoreField: r2->field_b = r0
    //     0x13cbe78: stur            w0, [x2, #0xb]
    // 0x13cbe7c: ldur            x0, [fp, #-0x20]
    // 0x13cbe80: StoreField: r2->field_13 = r0
    //     0x13cbe80: stur            w0, [x2, #0x13]
    // 0x13cbe84: r0 = Instance_TextOverflow
    //     0x13cbe84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x13cbe88: ldr             x0, [x0, #0xe10]
    // 0x13cbe8c: StoreField: r2->field_2b = r0
    //     0x13cbe8c: stur            w0, [x2, #0x2b]
    // 0x13cbe90: r0 = 4
    //     0x13cbe90: movz            x0, #0x4
    // 0x13cbe94: StoreField: r2->field_37 = r0
    //     0x13cbe94: stur            w0, [x2, #0x37]
    // 0x13cbe98: ldur            x3, [fp, #-8]
    // 0x13cbe9c: LoadField: r1 = r3->field_f
    //     0x13cbe9c: ldur            w1, [x3, #0xf]
    // 0x13cbea0: DecompressPointer r1
    //     0x13cbea0: add             x1, x1, HEAP, lsl #32
    // 0x13cbea4: r0 = controller()
    //     0x13cbea4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cbea8: LoadField: r1 = r0->field_4b
    //     0x13cbea8: ldur            w1, [x0, #0x4b]
    // 0x13cbeac: DecompressPointer r1
    //     0x13cbeac: add             x1, x1, HEAP, lsl #32
    // 0x13cbeb0: r0 = value()
    //     0x13cbeb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cbeb4: LoadField: r1 = r0->field_b
    //     0x13cbeb4: ldur            w1, [x0, #0xb]
    // 0x13cbeb8: DecompressPointer r1
    //     0x13cbeb8: add             x1, x1, HEAP, lsl #32
    // 0x13cbebc: cmp             w1, NULL
    // 0x13cbec0: b.ne            #0x13cbecc
    // 0x13cbec4: r0 = Null
    //     0x13cbec4: mov             x0, NULL
    // 0x13cbec8: b               #0x13cbef0
    // 0x13cbecc: LoadField: r0 = r1->field_f
    //     0x13cbecc: ldur            w0, [x1, #0xf]
    // 0x13cbed0: DecompressPointer r0
    //     0x13cbed0: add             x0, x0, HEAP, lsl #32
    // 0x13cbed4: cmp             w0, NULL
    // 0x13cbed8: b.ne            #0x13cbee4
    // 0x13cbedc: r0 = Null
    //     0x13cbedc: mov             x0, NULL
    // 0x13cbee0: b               #0x13cbef0
    // 0x13cbee4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13cbee4: ldur            w1, [x0, #0x17]
    // 0x13cbee8: DecompressPointer r1
    //     0x13cbee8: add             x1, x1, HEAP, lsl #32
    // 0x13cbeec: mov             x0, x1
    // 0x13cbef0: r1 = LoadClassIdInstr(r0)
    //     0x13cbef0: ldur            x1, [x0, #-1]
    //     0x13cbef4: ubfx            x1, x1, #0xc, #0x14
    // 0x13cbef8: r16 = "size"
    //     0x13cbef8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x13cbefc: ldr             x16, [x16, #0x9c0]
    // 0x13cbf00: stp             x16, x0, [SP]
    // 0x13cbf04: mov             x0, x1
    // 0x13cbf08: mov             lr, x0
    // 0x13cbf0c: ldr             lr, [x21, lr, lsl #3]
    // 0x13cbf10: blr             lr
    // 0x13cbf14: tbnz            w0, #4, #0x13cc0f0
    // 0x13cbf18: ldur            x0, [fp, #-8]
    // 0x13cbf1c: r1 = Null
    //     0x13cbf1c: mov             x1, NULL
    // 0x13cbf20: r2 = 8
    //     0x13cbf20: movz            x2, #0x8
    // 0x13cbf24: r0 = AllocateArray()
    //     0x13cbf24: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cbf28: stur            x0, [fp, #-0x10]
    // 0x13cbf2c: r16 = "Size: "
    //     0x13cbf2c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x13cbf30: ldr             x16, [x16, #0xf00]
    // 0x13cbf34: StoreField: r0->field_f = r16
    //     0x13cbf34: stur            w16, [x0, #0xf]
    // 0x13cbf38: ldur            x2, [fp, #-8]
    // 0x13cbf3c: LoadField: r1 = r2->field_f
    //     0x13cbf3c: ldur            w1, [x2, #0xf]
    // 0x13cbf40: DecompressPointer r1
    //     0x13cbf40: add             x1, x1, HEAP, lsl #32
    // 0x13cbf44: r0 = controller()
    //     0x13cbf44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cbf48: LoadField: r1 = r0->field_4b
    //     0x13cbf48: ldur            w1, [x0, #0x4b]
    // 0x13cbf4c: DecompressPointer r1
    //     0x13cbf4c: add             x1, x1, HEAP, lsl #32
    // 0x13cbf50: r0 = value()
    //     0x13cbf50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cbf54: LoadField: r1 = r0->field_b
    //     0x13cbf54: ldur            w1, [x0, #0xb]
    // 0x13cbf58: DecompressPointer r1
    //     0x13cbf58: add             x1, x1, HEAP, lsl #32
    // 0x13cbf5c: cmp             w1, NULL
    // 0x13cbf60: b.ne            #0x13cbf6c
    // 0x13cbf64: r0 = Null
    //     0x13cbf64: mov             x0, NULL
    // 0x13cbf68: b               #0x13cbf90
    // 0x13cbf6c: LoadField: r0 = r1->field_f
    //     0x13cbf6c: ldur            w0, [x1, #0xf]
    // 0x13cbf70: DecompressPointer r0
    //     0x13cbf70: add             x0, x0, HEAP, lsl #32
    // 0x13cbf74: cmp             w0, NULL
    // 0x13cbf78: b.ne            #0x13cbf84
    // 0x13cbf7c: r0 = Null
    //     0x13cbf7c: mov             x0, NULL
    // 0x13cbf80: b               #0x13cbf90
    // 0x13cbf84: LoadField: r1 = r0->field_f
    //     0x13cbf84: ldur            w1, [x0, #0xf]
    // 0x13cbf88: DecompressPointer r1
    //     0x13cbf88: add             x1, x1, HEAP, lsl #32
    // 0x13cbf8c: mov             x0, x1
    // 0x13cbf90: cmp             w0, NULL
    // 0x13cbf94: b.ne            #0x13cbf9c
    // 0x13cbf98: r0 = ""
    //     0x13cbf98: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cbf9c: ldur            x3, [fp, #-8]
    // 0x13cbfa0: ldur            x2, [fp, #-0x10]
    // 0x13cbfa4: mov             x1, x2
    // 0x13cbfa8: ArrayStore: r1[1] = r0  ; List_4
    //     0x13cbfa8: add             x25, x1, #0x13
    //     0x13cbfac: str             w0, [x25]
    //     0x13cbfb0: tbz             w0, #0, #0x13cbfcc
    //     0x13cbfb4: ldurb           w16, [x1, #-1]
    //     0x13cbfb8: ldurb           w17, [x0, #-1]
    //     0x13cbfbc: and             x16, x17, x16, lsr #2
    //     0x13cbfc0: tst             x16, HEAP, lsr #32
    //     0x13cbfc4: b.eq            #0x13cbfcc
    //     0x13cbfc8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cbfcc: r16 = " / Qty: "
    //     0x13cbfcc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13cbfd0: ldr             x16, [x16, #0x760]
    // 0x13cbfd4: ArrayStore: r2[0] = r16  ; List_4
    //     0x13cbfd4: stur            w16, [x2, #0x17]
    // 0x13cbfd8: LoadField: r1 = r3->field_f
    //     0x13cbfd8: ldur            w1, [x3, #0xf]
    // 0x13cbfdc: DecompressPointer r1
    //     0x13cbfdc: add             x1, x1, HEAP, lsl #32
    // 0x13cbfe0: r0 = controller()
    //     0x13cbfe0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cbfe4: LoadField: r1 = r0->field_4b
    //     0x13cbfe4: ldur            w1, [x0, #0x4b]
    // 0x13cbfe8: DecompressPointer r1
    //     0x13cbfe8: add             x1, x1, HEAP, lsl #32
    // 0x13cbfec: r0 = value()
    //     0x13cbfec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cbff0: LoadField: r1 = r0->field_b
    //     0x13cbff0: ldur            w1, [x0, #0xb]
    // 0x13cbff4: DecompressPointer r1
    //     0x13cbff4: add             x1, x1, HEAP, lsl #32
    // 0x13cbff8: cmp             w1, NULL
    // 0x13cbffc: b.ne            #0x13cc008
    // 0x13cc000: r0 = Null
    //     0x13cc000: mov             x0, NULL
    // 0x13cc004: b               #0x13cc02c
    // 0x13cc008: LoadField: r0 = r1->field_f
    //     0x13cc008: ldur            w0, [x1, #0xf]
    // 0x13cc00c: DecompressPointer r0
    //     0x13cc00c: add             x0, x0, HEAP, lsl #32
    // 0x13cc010: cmp             w0, NULL
    // 0x13cc014: b.ne            #0x13cc020
    // 0x13cc018: r0 = Null
    //     0x13cc018: mov             x0, NULL
    // 0x13cc01c: b               #0x13cc02c
    // 0x13cc020: LoadField: r1 = r0->field_13
    //     0x13cc020: ldur            w1, [x0, #0x13]
    // 0x13cc024: DecompressPointer r1
    //     0x13cc024: add             x1, x1, HEAP, lsl #32
    // 0x13cc028: mov             x0, x1
    // 0x13cc02c: cmp             w0, NULL
    // 0x13cc030: b.ne            #0x13cc038
    // 0x13cc034: r0 = ""
    //     0x13cc034: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cc038: ldur            x2, [fp, #-8]
    // 0x13cc03c: ldur            x1, [fp, #-0x10]
    // 0x13cc040: ArrayStore: r1[3] = r0  ; List_4
    //     0x13cc040: add             x25, x1, #0x1b
    //     0x13cc044: str             w0, [x25]
    //     0x13cc048: tbz             w0, #0, #0x13cc064
    //     0x13cc04c: ldurb           w16, [x1, #-1]
    //     0x13cc050: ldurb           w17, [x0, #-1]
    //     0x13cc054: and             x16, x17, x16, lsr #2
    //     0x13cc058: tst             x16, HEAP, lsr #32
    //     0x13cc05c: b.eq            #0x13cc064
    //     0x13cc060: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cc064: ldur            x16, [fp, #-0x10]
    // 0x13cc068: str             x16, [SP]
    // 0x13cc06c: r0 = _interpolate()
    //     0x13cc06c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13cc070: ldur            x2, [fp, #-8]
    // 0x13cc074: stur            x0, [fp, #-0x10]
    // 0x13cc078: LoadField: r1 = r2->field_13
    //     0x13cc078: ldur            w1, [x2, #0x13]
    // 0x13cc07c: DecompressPointer r1
    //     0x13cc07c: add             x1, x1, HEAP, lsl #32
    // 0x13cc080: r0 = of()
    //     0x13cc080: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cc084: LoadField: r1 = r0->field_87
    //     0x13cc084: ldur            w1, [x0, #0x87]
    // 0x13cc088: DecompressPointer r1
    //     0x13cc088: add             x1, x1, HEAP, lsl #32
    // 0x13cc08c: LoadField: r0 = r1->field_33
    //     0x13cc08c: ldur            w0, [x1, #0x33]
    // 0x13cc090: DecompressPointer r0
    //     0x13cc090: add             x0, x0, HEAP, lsl #32
    // 0x13cc094: cmp             w0, NULL
    // 0x13cc098: b.ne            #0x13cc0a4
    // 0x13cc09c: r1 = Null
    //     0x13cc09c: mov             x1, NULL
    // 0x13cc0a0: b               #0x13cc0c8
    // 0x13cc0a4: r16 = 12.000000
    //     0x13cc0a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cc0a8: ldr             x16, [x16, #0x9e8]
    // 0x13cc0ac: r30 = Instance_Color
    //     0x13cc0ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc0b0: stp             lr, x16, [SP]
    // 0x13cc0b4: mov             x1, x0
    // 0x13cc0b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13cc0b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13cc0bc: ldr             x4, [x4, #0xaa0]
    // 0x13cc0c0: r0 = copyWith()
    //     0x13cc0c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cc0c4: mov             x1, x0
    // 0x13cc0c8: ldur            x0, [fp, #-0x10]
    // 0x13cc0cc: stur            x1, [fp, #-0x20]
    // 0x13cc0d0: r0 = Text()
    //     0x13cc0d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cc0d4: mov             x1, x0
    // 0x13cc0d8: ldur            x0, [fp, #-0x10]
    // 0x13cc0dc: StoreField: r1->field_b = r0
    //     0x13cc0dc: stur            w0, [x1, #0xb]
    // 0x13cc0e0: ldur            x0, [fp, #-0x20]
    // 0x13cc0e4: StoreField: r1->field_13 = r0
    //     0x13cc0e4: stur            w0, [x1, #0x13]
    // 0x13cc0e8: mov             x0, x1
    // 0x13cc0ec: b               #0x13cc2c4
    // 0x13cc0f0: ldur            x0, [fp, #-8]
    // 0x13cc0f4: r1 = Null
    //     0x13cc0f4: mov             x1, NULL
    // 0x13cc0f8: r2 = 8
    //     0x13cc0f8: movz            x2, #0x8
    // 0x13cc0fc: r0 = AllocateArray()
    //     0x13cc0fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cc100: stur            x0, [fp, #-0x10]
    // 0x13cc104: r16 = "Variant: "
    //     0x13cc104: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x13cc108: ldr             x16, [x16, #0xf08]
    // 0x13cc10c: StoreField: r0->field_f = r16
    //     0x13cc10c: stur            w16, [x0, #0xf]
    // 0x13cc110: ldur            x2, [fp, #-8]
    // 0x13cc114: LoadField: r1 = r2->field_f
    //     0x13cc114: ldur            w1, [x2, #0xf]
    // 0x13cc118: DecompressPointer r1
    //     0x13cc118: add             x1, x1, HEAP, lsl #32
    // 0x13cc11c: r0 = controller()
    //     0x13cc11c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc120: LoadField: r1 = r0->field_4b
    //     0x13cc120: ldur            w1, [x0, #0x4b]
    // 0x13cc124: DecompressPointer r1
    //     0x13cc124: add             x1, x1, HEAP, lsl #32
    // 0x13cc128: r0 = value()
    //     0x13cc128: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc12c: LoadField: r1 = r0->field_b
    //     0x13cc12c: ldur            w1, [x0, #0xb]
    // 0x13cc130: DecompressPointer r1
    //     0x13cc130: add             x1, x1, HEAP, lsl #32
    // 0x13cc134: cmp             w1, NULL
    // 0x13cc138: b.ne            #0x13cc144
    // 0x13cc13c: r0 = Null
    //     0x13cc13c: mov             x0, NULL
    // 0x13cc140: b               #0x13cc168
    // 0x13cc144: LoadField: r0 = r1->field_f
    //     0x13cc144: ldur            w0, [x1, #0xf]
    // 0x13cc148: DecompressPointer r0
    //     0x13cc148: add             x0, x0, HEAP, lsl #32
    // 0x13cc14c: cmp             w0, NULL
    // 0x13cc150: b.ne            #0x13cc15c
    // 0x13cc154: r0 = Null
    //     0x13cc154: mov             x0, NULL
    // 0x13cc158: b               #0x13cc168
    // 0x13cc15c: LoadField: r1 = r0->field_f
    //     0x13cc15c: ldur            w1, [x0, #0xf]
    // 0x13cc160: DecompressPointer r1
    //     0x13cc160: add             x1, x1, HEAP, lsl #32
    // 0x13cc164: mov             x0, x1
    // 0x13cc168: cmp             w0, NULL
    // 0x13cc16c: b.ne            #0x13cc174
    // 0x13cc170: r0 = ""
    //     0x13cc170: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cc174: ldur            x3, [fp, #-8]
    // 0x13cc178: ldur            x2, [fp, #-0x10]
    // 0x13cc17c: mov             x1, x2
    // 0x13cc180: ArrayStore: r1[1] = r0  ; List_4
    //     0x13cc180: add             x25, x1, #0x13
    //     0x13cc184: str             w0, [x25]
    //     0x13cc188: tbz             w0, #0, #0x13cc1a4
    //     0x13cc18c: ldurb           w16, [x1, #-1]
    //     0x13cc190: ldurb           w17, [x0, #-1]
    //     0x13cc194: and             x16, x17, x16, lsr #2
    //     0x13cc198: tst             x16, HEAP, lsr #32
    //     0x13cc19c: b.eq            #0x13cc1a4
    //     0x13cc1a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cc1a4: r16 = " / Qty: "
    //     0x13cc1a4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13cc1a8: ldr             x16, [x16, #0x760]
    // 0x13cc1ac: ArrayStore: r2[0] = r16  ; List_4
    //     0x13cc1ac: stur            w16, [x2, #0x17]
    // 0x13cc1b0: LoadField: r1 = r3->field_f
    //     0x13cc1b0: ldur            w1, [x3, #0xf]
    // 0x13cc1b4: DecompressPointer r1
    //     0x13cc1b4: add             x1, x1, HEAP, lsl #32
    // 0x13cc1b8: r0 = controller()
    //     0x13cc1b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc1bc: LoadField: r1 = r0->field_4b
    //     0x13cc1bc: ldur            w1, [x0, #0x4b]
    // 0x13cc1c0: DecompressPointer r1
    //     0x13cc1c0: add             x1, x1, HEAP, lsl #32
    // 0x13cc1c4: r0 = value()
    //     0x13cc1c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc1c8: LoadField: r1 = r0->field_b
    //     0x13cc1c8: ldur            w1, [x0, #0xb]
    // 0x13cc1cc: DecompressPointer r1
    //     0x13cc1cc: add             x1, x1, HEAP, lsl #32
    // 0x13cc1d0: cmp             w1, NULL
    // 0x13cc1d4: b.ne            #0x13cc1e0
    // 0x13cc1d8: r0 = Null
    //     0x13cc1d8: mov             x0, NULL
    // 0x13cc1dc: b               #0x13cc204
    // 0x13cc1e0: LoadField: r0 = r1->field_f
    //     0x13cc1e0: ldur            w0, [x1, #0xf]
    // 0x13cc1e4: DecompressPointer r0
    //     0x13cc1e4: add             x0, x0, HEAP, lsl #32
    // 0x13cc1e8: cmp             w0, NULL
    // 0x13cc1ec: b.ne            #0x13cc1f8
    // 0x13cc1f0: r0 = Null
    //     0x13cc1f0: mov             x0, NULL
    // 0x13cc1f4: b               #0x13cc204
    // 0x13cc1f8: LoadField: r1 = r0->field_13
    //     0x13cc1f8: ldur            w1, [x0, #0x13]
    // 0x13cc1fc: DecompressPointer r1
    //     0x13cc1fc: add             x1, x1, HEAP, lsl #32
    // 0x13cc200: mov             x0, x1
    // 0x13cc204: cmp             w0, NULL
    // 0x13cc208: b.ne            #0x13cc210
    // 0x13cc20c: r0 = ""
    //     0x13cc20c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cc210: ldur            x2, [fp, #-8]
    // 0x13cc214: ldur            x1, [fp, #-0x10]
    // 0x13cc218: ArrayStore: r1[3] = r0  ; List_4
    //     0x13cc218: add             x25, x1, #0x1b
    //     0x13cc21c: str             w0, [x25]
    //     0x13cc220: tbz             w0, #0, #0x13cc23c
    //     0x13cc224: ldurb           w16, [x1, #-1]
    //     0x13cc228: ldurb           w17, [x0, #-1]
    //     0x13cc22c: and             x16, x17, x16, lsr #2
    //     0x13cc230: tst             x16, HEAP, lsr #32
    //     0x13cc234: b.eq            #0x13cc23c
    //     0x13cc238: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cc23c: ldur            x16, [fp, #-0x10]
    // 0x13cc240: str             x16, [SP]
    // 0x13cc244: r0 = _interpolate()
    //     0x13cc244: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13cc248: ldur            x2, [fp, #-8]
    // 0x13cc24c: stur            x0, [fp, #-0x10]
    // 0x13cc250: LoadField: r1 = r2->field_13
    //     0x13cc250: ldur            w1, [x2, #0x13]
    // 0x13cc254: DecompressPointer r1
    //     0x13cc254: add             x1, x1, HEAP, lsl #32
    // 0x13cc258: r0 = of()
    //     0x13cc258: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cc25c: LoadField: r1 = r0->field_87
    //     0x13cc25c: ldur            w1, [x0, #0x87]
    // 0x13cc260: DecompressPointer r1
    //     0x13cc260: add             x1, x1, HEAP, lsl #32
    // 0x13cc264: LoadField: r0 = r1->field_33
    //     0x13cc264: ldur            w0, [x1, #0x33]
    // 0x13cc268: DecompressPointer r0
    //     0x13cc268: add             x0, x0, HEAP, lsl #32
    // 0x13cc26c: cmp             w0, NULL
    // 0x13cc270: b.ne            #0x13cc27c
    // 0x13cc274: r1 = Null
    //     0x13cc274: mov             x1, NULL
    // 0x13cc278: b               #0x13cc2a0
    // 0x13cc27c: r16 = 12.000000
    //     0x13cc27c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cc280: ldr             x16, [x16, #0x9e8]
    // 0x13cc284: r30 = Instance_Color
    //     0x13cc284: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc288: stp             lr, x16, [SP]
    // 0x13cc28c: mov             x1, x0
    // 0x13cc290: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13cc290: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13cc294: ldr             x4, [x4, #0xaa0]
    // 0x13cc298: r0 = copyWith()
    //     0x13cc298: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cc29c: mov             x1, x0
    // 0x13cc2a0: ldur            x0, [fp, #-0x10]
    // 0x13cc2a4: stur            x1, [fp, #-0x20]
    // 0x13cc2a8: r0 = Text()
    //     0x13cc2a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cc2ac: mov             x1, x0
    // 0x13cc2b0: ldur            x0, [fp, #-0x10]
    // 0x13cc2b4: StoreField: r1->field_b = r0
    //     0x13cc2b4: stur            w0, [x1, #0xb]
    // 0x13cc2b8: ldur            x0, [fp, #-0x20]
    // 0x13cc2bc: StoreField: r1->field_13 = r0
    //     0x13cc2bc: stur            w0, [x1, #0x13]
    // 0x13cc2c0: mov             x0, x1
    // 0x13cc2c4: ldur            x2, [fp, #-8]
    // 0x13cc2c8: stur            x0, [fp, #-0x10]
    // 0x13cc2cc: r0 = Padding()
    //     0x13cc2cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cc2d0: mov             x2, x0
    // 0x13cc2d4: r0 = Instance_EdgeInsets
    //     0x13cc2d4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x13cc2d8: ldr             x0, [x0, #0x770]
    // 0x13cc2dc: stur            x2, [fp, #-0x20]
    // 0x13cc2e0: StoreField: r2->field_f = r0
    //     0x13cc2e0: stur            w0, [x2, #0xf]
    // 0x13cc2e4: ldur            x1, [fp, #-0x10]
    // 0x13cc2e8: StoreField: r2->field_b = r1
    //     0x13cc2e8: stur            w1, [x2, #0xb]
    // 0x13cc2ec: ldur            x3, [fp, #-8]
    // 0x13cc2f0: LoadField: r1 = r3->field_f
    //     0x13cc2f0: ldur            w1, [x3, #0xf]
    // 0x13cc2f4: DecompressPointer r1
    //     0x13cc2f4: add             x1, x1, HEAP, lsl #32
    // 0x13cc2f8: r0 = controller()
    //     0x13cc2f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc2fc: LoadField: r1 = r0->field_4b
    //     0x13cc2fc: ldur            w1, [x0, #0x4b]
    // 0x13cc300: DecompressPointer r1
    //     0x13cc300: add             x1, x1, HEAP, lsl #32
    // 0x13cc304: r0 = value()
    //     0x13cc304: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc308: LoadField: r1 = r0->field_b
    //     0x13cc308: ldur            w1, [x0, #0xb]
    // 0x13cc30c: DecompressPointer r1
    //     0x13cc30c: add             x1, x1, HEAP, lsl #32
    // 0x13cc310: cmp             w1, NULL
    // 0x13cc314: b.ne            #0x13cc320
    // 0x13cc318: r0 = Null
    //     0x13cc318: mov             x0, NULL
    // 0x13cc31c: b               #0x13cc358
    // 0x13cc320: LoadField: r0 = r1->field_f
    //     0x13cc320: ldur            w0, [x1, #0xf]
    // 0x13cc324: DecompressPointer r0
    //     0x13cc324: add             x0, x0, HEAP, lsl #32
    // 0x13cc328: cmp             w0, NULL
    // 0x13cc32c: b.ne            #0x13cc338
    // 0x13cc330: r0 = Null
    //     0x13cc330: mov             x0, NULL
    // 0x13cc334: b               #0x13cc358
    // 0x13cc338: LoadField: r1 = r0->field_1b
    //     0x13cc338: ldur            w1, [x0, #0x1b]
    // 0x13cc33c: DecompressPointer r1
    //     0x13cc33c: add             x1, x1, HEAP, lsl #32
    // 0x13cc340: cmp             w1, NULL
    // 0x13cc344: b.ne            #0x13cc350
    // 0x13cc348: r0 = Null
    //     0x13cc348: mov             x0, NULL
    // 0x13cc34c: b               #0x13cc358
    // 0x13cc350: LoadField: r0 = r1->field_7
    //     0x13cc350: ldur            w0, [x1, #7]
    // 0x13cc354: DecompressPointer r0
    //     0x13cc354: add             x0, x0, HEAP, lsl #32
    // 0x13cc358: cmp             w0, NULL
    // 0x13cc35c: b.ne            #0x13cc368
    // 0x13cc360: r5 = ""
    //     0x13cc360: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cc364: b               #0x13cc36c
    // 0x13cc368: mov             x5, x0
    // 0x13cc36c: ldur            x2, [fp, #-8]
    // 0x13cc370: ldur            x4, [fp, #-0x38]
    // 0x13cc374: ldur            d0, [fp, #-0x58]
    // 0x13cc378: ldur            x3, [fp, #-0x28]
    // 0x13cc37c: ldur            x0, [fp, #-0x20]
    // 0x13cc380: stur            x5, [fp, #-0x10]
    // 0x13cc384: LoadField: r1 = r2->field_13
    //     0x13cc384: ldur            w1, [x2, #0x13]
    // 0x13cc388: DecompressPointer r1
    //     0x13cc388: add             x1, x1, HEAP, lsl #32
    // 0x13cc38c: r0 = of()
    //     0x13cc38c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cc390: LoadField: r1 = r0->field_87
    //     0x13cc390: ldur            w1, [x0, #0x87]
    // 0x13cc394: DecompressPointer r1
    //     0x13cc394: add             x1, x1, HEAP, lsl #32
    // 0x13cc398: LoadField: r0 = r1->field_2b
    //     0x13cc398: ldur            w0, [x1, #0x2b]
    // 0x13cc39c: DecompressPointer r0
    //     0x13cc39c: add             x0, x0, HEAP, lsl #32
    // 0x13cc3a0: r16 = 12.000000
    //     0x13cc3a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cc3a4: ldr             x16, [x16, #0x9e8]
    // 0x13cc3a8: r30 = Instance_Color
    //     0x13cc3a8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc3ac: stp             lr, x16, [SP]
    // 0x13cc3b0: mov             x1, x0
    // 0x13cc3b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13cc3b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13cc3b8: ldr             x4, [x4, #0xaa0]
    // 0x13cc3bc: r0 = copyWith()
    //     0x13cc3bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cc3c0: stur            x0, [fp, #-0x30]
    // 0x13cc3c4: r0 = Text()
    //     0x13cc3c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cc3c8: mov             x1, x0
    // 0x13cc3cc: ldur            x0, [fp, #-0x10]
    // 0x13cc3d0: stur            x1, [fp, #-0x40]
    // 0x13cc3d4: StoreField: r1->field_b = r0
    //     0x13cc3d4: stur            w0, [x1, #0xb]
    // 0x13cc3d8: ldur            x0, [fp, #-0x30]
    // 0x13cc3dc: StoreField: r1->field_13 = r0
    //     0x13cc3dc: stur            w0, [x1, #0x13]
    // 0x13cc3e0: r0 = Padding()
    //     0x13cc3e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cc3e4: mov             x3, x0
    // 0x13cc3e8: r0 = Instance_EdgeInsets
    //     0x13cc3e8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x13cc3ec: ldr             x0, [x0, #0x770]
    // 0x13cc3f0: stur            x3, [fp, #-0x10]
    // 0x13cc3f4: StoreField: r3->field_f = r0
    //     0x13cc3f4: stur            w0, [x3, #0xf]
    // 0x13cc3f8: ldur            x0, [fp, #-0x40]
    // 0x13cc3fc: StoreField: r3->field_b = r0
    //     0x13cc3fc: stur            w0, [x3, #0xb]
    // 0x13cc400: r1 = Null
    //     0x13cc400: mov             x1, NULL
    // 0x13cc404: r2 = 6
    //     0x13cc404: movz            x2, #0x6
    // 0x13cc408: r0 = AllocateArray()
    //     0x13cc408: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cc40c: mov             x2, x0
    // 0x13cc410: ldur            x0, [fp, #-0x28]
    // 0x13cc414: stur            x2, [fp, #-0x30]
    // 0x13cc418: StoreField: r2->field_f = r0
    //     0x13cc418: stur            w0, [x2, #0xf]
    // 0x13cc41c: ldur            x0, [fp, #-0x20]
    // 0x13cc420: StoreField: r2->field_13 = r0
    //     0x13cc420: stur            w0, [x2, #0x13]
    // 0x13cc424: ldur            x0, [fp, #-0x10]
    // 0x13cc428: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cc428: stur            w0, [x2, #0x17]
    // 0x13cc42c: r1 = <Widget>
    //     0x13cc42c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cc430: r0 = AllocateGrowableArray()
    //     0x13cc430: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cc434: mov             x1, x0
    // 0x13cc438: ldur            x0, [fp, #-0x30]
    // 0x13cc43c: stur            x1, [fp, #-0x10]
    // 0x13cc440: StoreField: r1->field_f = r0
    //     0x13cc440: stur            w0, [x1, #0xf]
    // 0x13cc444: r2 = 6
    //     0x13cc444: movz            x2, #0x6
    // 0x13cc448: StoreField: r1->field_b = r2
    //     0x13cc448: stur            w2, [x1, #0xb]
    // 0x13cc44c: r0 = Column()
    //     0x13cc44c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13cc450: mov             x1, x0
    // 0x13cc454: r0 = Instance_Axis
    //     0x13cc454: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cc458: stur            x1, [fp, #-0x20]
    // 0x13cc45c: StoreField: r1->field_f = r0
    //     0x13cc45c: stur            w0, [x1, #0xf]
    // 0x13cc460: r2 = Instance_MainAxisAlignment
    //     0x13cc460: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cc464: ldr             x2, [x2, #0xa08]
    // 0x13cc468: StoreField: r1->field_13 = r2
    //     0x13cc468: stur            w2, [x1, #0x13]
    // 0x13cc46c: r3 = Instance_MainAxisSize
    //     0x13cc46c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13cc470: ldr             x3, [x3, #0xdd0]
    // 0x13cc474: ArrayStore: r1[0] = r3  ; List_4
    //     0x13cc474: stur            w3, [x1, #0x17]
    // 0x13cc478: r3 = Instance_CrossAxisAlignment
    //     0x13cc478: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13cc47c: ldr             x3, [x3, #0x890]
    // 0x13cc480: StoreField: r1->field_1b = r3
    //     0x13cc480: stur            w3, [x1, #0x1b]
    // 0x13cc484: r4 = Instance_VerticalDirection
    //     0x13cc484: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cc488: ldr             x4, [x4, #0xa20]
    // 0x13cc48c: StoreField: r1->field_23 = r4
    //     0x13cc48c: stur            w4, [x1, #0x23]
    // 0x13cc490: r5 = Instance_Clip
    //     0x13cc490: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13cc494: ldr             x5, [x5, #0x38]
    // 0x13cc498: StoreField: r1->field_2b = r5
    //     0x13cc498: stur            w5, [x1, #0x2b]
    // 0x13cc49c: StoreField: r1->field_2f = rZR
    //     0x13cc49c: stur            xzr, [x1, #0x2f]
    // 0x13cc4a0: ldur            x6, [fp, #-0x10]
    // 0x13cc4a4: StoreField: r1->field_b = r6
    //     0x13cc4a4: stur            w6, [x1, #0xb]
    // 0x13cc4a8: ldur            d0, [fp, #-0x58]
    // 0x13cc4ac: r6 = inline_Allocate_Double()
    //     0x13cc4ac: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x13cc4b0: add             x6, x6, #0x10
    //     0x13cc4b4: cmp             x7, x6
    //     0x13cc4b8: b.ls            #0x13cd1e8
    //     0x13cc4bc: str             x6, [THR, #0x50]  ; THR::top
    //     0x13cc4c0: sub             x6, x6, #0xf
    //     0x13cc4c4: movz            x7, #0xe15c
    //     0x13cc4c8: movk            x7, #0x3, lsl #16
    //     0x13cc4cc: stur            x7, [x6, #-1]
    // 0x13cc4d0: StoreField: r6->field_7 = d0
    //     0x13cc4d0: stur            d0, [x6, #7]
    // 0x13cc4d4: stur            x6, [fp, #-0x10]
    // 0x13cc4d8: r0 = SizedBox()
    //     0x13cc4d8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13cc4dc: mov             x3, x0
    // 0x13cc4e0: ldur            x0, [fp, #-0x10]
    // 0x13cc4e4: stur            x3, [fp, #-0x28]
    // 0x13cc4e8: StoreField: r3->field_f = r0
    //     0x13cc4e8: stur            w0, [x3, #0xf]
    // 0x13cc4ec: ldur            x0, [fp, #-0x20]
    // 0x13cc4f0: StoreField: r3->field_b = r0
    //     0x13cc4f0: stur            w0, [x3, #0xb]
    // 0x13cc4f4: r1 = Null
    //     0x13cc4f4: mov             x1, NULL
    // 0x13cc4f8: r2 = 6
    //     0x13cc4f8: movz            x2, #0x6
    // 0x13cc4fc: r0 = AllocateArray()
    //     0x13cc4fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cc500: mov             x2, x0
    // 0x13cc504: ldur            x0, [fp, #-0x38]
    // 0x13cc508: stur            x2, [fp, #-0x10]
    // 0x13cc50c: StoreField: r2->field_f = r0
    //     0x13cc50c: stur            w0, [x2, #0xf]
    // 0x13cc510: r16 = Instance_SizedBox
    //     0x13cc510: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x13cc514: ldr             x16, [x16, #0xb20]
    // 0x13cc518: StoreField: r2->field_13 = r16
    //     0x13cc518: stur            w16, [x2, #0x13]
    // 0x13cc51c: ldur            x0, [fp, #-0x28]
    // 0x13cc520: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cc520: stur            w0, [x2, #0x17]
    // 0x13cc524: r1 = <Widget>
    //     0x13cc524: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cc528: r0 = AllocateGrowableArray()
    //     0x13cc528: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cc52c: mov             x1, x0
    // 0x13cc530: ldur            x0, [fp, #-0x10]
    // 0x13cc534: stur            x1, [fp, #-0x20]
    // 0x13cc538: StoreField: r1->field_f = r0
    //     0x13cc538: stur            w0, [x1, #0xf]
    // 0x13cc53c: r2 = 6
    //     0x13cc53c: movz            x2, #0x6
    // 0x13cc540: StoreField: r1->field_b = r2
    //     0x13cc540: stur            w2, [x1, #0xb]
    // 0x13cc544: r0 = Row()
    //     0x13cc544: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13cc548: mov             x1, x0
    // 0x13cc54c: r0 = Instance_Axis
    //     0x13cc54c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13cc550: stur            x1, [fp, #-0x10]
    // 0x13cc554: StoreField: r1->field_f = r0
    //     0x13cc554: stur            w0, [x1, #0xf]
    // 0x13cc558: r0 = Instance_MainAxisAlignment
    //     0x13cc558: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cc55c: ldr             x0, [x0, #0xa08]
    // 0x13cc560: StoreField: r1->field_13 = r0
    //     0x13cc560: stur            w0, [x1, #0x13]
    // 0x13cc564: r2 = Instance_MainAxisSize
    //     0x13cc564: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13cc568: ldr             x2, [x2, #0xa10]
    // 0x13cc56c: ArrayStore: r1[0] = r2  ; List_4
    //     0x13cc56c: stur            w2, [x1, #0x17]
    // 0x13cc570: r3 = Instance_CrossAxisAlignment
    //     0x13cc570: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13cc574: ldr             x3, [x3, #0xa18]
    // 0x13cc578: StoreField: r1->field_1b = r3
    //     0x13cc578: stur            w3, [x1, #0x1b]
    // 0x13cc57c: r3 = Instance_VerticalDirection
    //     0x13cc57c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cc580: ldr             x3, [x3, #0xa20]
    // 0x13cc584: StoreField: r1->field_23 = r3
    //     0x13cc584: stur            w3, [x1, #0x23]
    // 0x13cc588: r4 = Instance_Clip
    //     0x13cc588: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13cc58c: ldr             x4, [x4, #0x38]
    // 0x13cc590: StoreField: r1->field_2b = r4
    //     0x13cc590: stur            w4, [x1, #0x2b]
    // 0x13cc594: StoreField: r1->field_2f = rZR
    //     0x13cc594: stur            xzr, [x1, #0x2f]
    // 0x13cc598: ldur            x5, [fp, #-0x20]
    // 0x13cc59c: StoreField: r1->field_b = r5
    //     0x13cc59c: stur            w5, [x1, #0xb]
    // 0x13cc5a0: r0 = Padding()
    //     0x13cc5a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cc5a4: mov             x1, x0
    // 0x13cc5a8: r0 = Instance_EdgeInsets
    //     0x13cc5a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x13cc5ac: ldr             x0, [x0, #0x980]
    // 0x13cc5b0: stur            x1, [fp, #-0x20]
    // 0x13cc5b4: StoreField: r1->field_f = r0
    //     0x13cc5b4: stur            w0, [x1, #0xf]
    // 0x13cc5b8: ldur            x0, [fp, #-0x10]
    // 0x13cc5bc: StoreField: r1->field_b = r0
    //     0x13cc5bc: stur            w0, [x1, #0xb]
    // 0x13cc5c0: r0 = Container()
    //     0x13cc5c0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13cc5c4: stur            x0, [fp, #-0x10]
    // 0x13cc5c8: ldur            x16, [fp, #-0x18]
    // 0x13cc5cc: ldur            lr, [fp, #-0x20]
    // 0x13cc5d0: stp             lr, x16, [SP]
    // 0x13cc5d4: mov             x1, x0
    // 0x13cc5d8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13cc5d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13cc5dc: ldr             x4, [x4, #0x88]
    // 0x13cc5e0: r0 = Container()
    //     0x13cc5e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13cc5e4: r0 = Padding()
    //     0x13cc5e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cc5e8: mov             x2, x0
    // 0x13cc5ec: r0 = Instance_EdgeInsets
    //     0x13cc5ec: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x13cc5f0: ldr             x0, [x0, #0x240]
    // 0x13cc5f4: stur            x2, [fp, #-0x18]
    // 0x13cc5f8: StoreField: r2->field_f = r0
    //     0x13cc5f8: stur            w0, [x2, #0xf]
    // 0x13cc5fc: ldur            x0, [fp, #-0x10]
    // 0x13cc600: StoreField: r2->field_b = r0
    //     0x13cc600: stur            w0, [x2, #0xb]
    // 0x13cc604: ldur            x0, [fp, #-8]
    // 0x13cc608: LoadField: r1 = r0->field_f
    //     0x13cc608: ldur            w1, [x0, #0xf]
    // 0x13cc60c: DecompressPointer r1
    //     0x13cc60c: add             x1, x1, HEAP, lsl #32
    // 0x13cc610: r0 = controller()
    //     0x13cc610: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc614: LoadField: r1 = r0->field_4b
    //     0x13cc614: ldur            w1, [x0, #0x4b]
    // 0x13cc618: DecompressPointer r1
    //     0x13cc618: add             x1, x1, HEAP, lsl #32
    // 0x13cc61c: r0 = value()
    //     0x13cc61c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc620: LoadField: r1 = r0->field_b
    //     0x13cc620: ldur            w1, [x0, #0xb]
    // 0x13cc624: DecompressPointer r1
    //     0x13cc624: add             x1, x1, HEAP, lsl #32
    // 0x13cc628: cmp             w1, NULL
    // 0x13cc62c: b.ne            #0x13cc638
    // 0x13cc630: r0 = Null
    //     0x13cc630: mov             x0, NULL
    // 0x13cc634: b               #0x13cc670
    // 0x13cc638: LoadField: r0 = r1->field_2f
    //     0x13cc638: ldur            w0, [x1, #0x2f]
    // 0x13cc63c: DecompressPointer r0
    //     0x13cc63c: add             x0, x0, HEAP, lsl #32
    // 0x13cc640: cmp             w0, NULL
    // 0x13cc644: b.ne            #0x13cc650
    // 0x13cc648: r0 = Null
    //     0x13cc648: mov             x0, NULL
    // 0x13cc64c: b               #0x13cc670
    // 0x13cc650: LoadField: r1 = r0->field_b
    //     0x13cc650: ldur            w1, [x0, #0xb]
    // 0x13cc654: DecompressPointer r1
    //     0x13cc654: add             x1, x1, HEAP, lsl #32
    // 0x13cc658: LoadField: r0 = r1->field_b
    //     0x13cc658: ldur            w0, [x1, #0xb]
    // 0x13cc65c: cbnz            w0, #0x13cc668
    // 0x13cc660: r1 = false
    //     0x13cc660: add             x1, NULL, #0x30  ; false
    // 0x13cc664: b               #0x13cc66c
    // 0x13cc668: r1 = true
    //     0x13cc668: add             x1, NULL, #0x20  ; true
    // 0x13cc66c: mov             x0, x1
    // 0x13cc670: cmp             w0, NULL
    // 0x13cc674: b.ne            #0x13cc67c
    // 0x13cc678: r0 = false
    //     0x13cc678: add             x0, NULL, #0x30  ; false
    // 0x13cc67c: ldur            x2, [fp, #-8]
    // 0x13cc680: stur            x0, [fp, #-0x10]
    // 0x13cc684: LoadField: r1 = r2->field_13
    //     0x13cc684: ldur            w1, [x2, #0x13]
    // 0x13cc688: DecompressPointer r1
    //     0x13cc688: add             x1, x1, HEAP, lsl #32
    // 0x13cc68c: r0 = of()
    //     0x13cc68c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cc690: LoadField: r1 = r0->field_87
    //     0x13cc690: ldur            w1, [x0, #0x87]
    // 0x13cc694: DecompressPointer r1
    //     0x13cc694: add             x1, x1, HEAP, lsl #32
    // 0x13cc698: LoadField: r0 = r1->field_7
    //     0x13cc698: ldur            w0, [x1, #7]
    // 0x13cc69c: DecompressPointer r0
    //     0x13cc69c: add             x0, x0, HEAP, lsl #32
    // 0x13cc6a0: r16 = Instance_Color
    //     0x13cc6a0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc6a4: r30 = 16.000000
    //     0x13cc6a4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x13cc6a8: ldr             lr, [lr, #0x188]
    // 0x13cc6ac: stp             lr, x16, [SP]
    // 0x13cc6b0: mov             x1, x0
    // 0x13cc6b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cc6b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cc6b8: ldr             x4, [x4, #0x9b8]
    // 0x13cc6bc: r0 = copyWith()
    //     0x13cc6bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cc6c0: stur            x0, [fp, #-0x20]
    // 0x13cc6c4: r0 = Text()
    //     0x13cc6c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cc6c8: mov             x2, x0
    // 0x13cc6cc: r0 = "Replacement order details"
    //     0x13cc6cc: add             x0, PP, #0x38, lsl #12  ; [pp+0x380a8] "Replacement order details"
    //     0x13cc6d0: ldr             x0, [x0, #0xa8]
    // 0x13cc6d4: stur            x2, [fp, #-0x28]
    // 0x13cc6d8: StoreField: r2->field_b = r0
    //     0x13cc6d8: stur            w0, [x2, #0xb]
    // 0x13cc6dc: ldur            x0, [fp, #-0x20]
    // 0x13cc6e0: StoreField: r2->field_13 = r0
    //     0x13cc6e0: stur            w0, [x2, #0x13]
    // 0x13cc6e4: ldur            x0, [fp, #-8]
    // 0x13cc6e8: LoadField: r1 = r0->field_f
    //     0x13cc6e8: ldur            w1, [x0, #0xf]
    // 0x13cc6ec: DecompressPointer r1
    //     0x13cc6ec: add             x1, x1, HEAP, lsl #32
    // 0x13cc6f0: r0 = controller()
    //     0x13cc6f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc6f4: LoadField: r1 = r0->field_4b
    //     0x13cc6f4: ldur            w1, [x0, #0x4b]
    // 0x13cc6f8: DecompressPointer r1
    //     0x13cc6f8: add             x1, x1, HEAP, lsl #32
    // 0x13cc6fc: r0 = value()
    //     0x13cc6fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc700: LoadField: r1 = r0->field_b
    //     0x13cc700: ldur            w1, [x0, #0xb]
    // 0x13cc704: DecompressPointer r1
    //     0x13cc704: add             x1, x1, HEAP, lsl #32
    // 0x13cc708: cmp             w1, NULL
    // 0x13cc70c: b.ne            #0x13cc718
    // 0x13cc710: r0 = Null
    //     0x13cc710: mov             x0, NULL
    // 0x13cc714: b               #0x13cc73c
    // 0x13cc718: LoadField: r0 = r1->field_2f
    //     0x13cc718: ldur            w0, [x1, #0x2f]
    // 0x13cc71c: DecompressPointer r0
    //     0x13cc71c: add             x0, x0, HEAP, lsl #32
    // 0x13cc720: cmp             w0, NULL
    // 0x13cc724: b.ne            #0x13cc730
    // 0x13cc728: r0 = Null
    //     0x13cc728: mov             x0, NULL
    // 0x13cc72c: b               #0x13cc73c
    // 0x13cc730: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13cc730: ldur            w1, [x0, #0x17]
    // 0x13cc734: DecompressPointer r1
    //     0x13cc734: add             x1, x1, HEAP, lsl #32
    // 0x13cc738: mov             x0, x1
    // 0x13cc73c: cmp             w0, NULL
    // 0x13cc740: b.ne            #0x13cc750
    // 0x13cc744: r1 = "size"
    //     0x13cc744: add             x1, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x13cc748: ldr             x1, [x1, #0x9c0]
    // 0x13cc74c: b               #0x13cc754
    // 0x13cc750: mov             x1, x0
    // 0x13cc754: ldur            x2, [fp, #-8]
    // 0x13cc758: r0 = capitalizeFirstWord()
    //     0x13cc758: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x13cc75c: ldur            x2, [fp, #-8]
    // 0x13cc760: stur            x0, [fp, #-0x20]
    // 0x13cc764: LoadField: r1 = r2->field_13
    //     0x13cc764: ldur            w1, [x2, #0x13]
    // 0x13cc768: DecompressPointer r1
    //     0x13cc768: add             x1, x1, HEAP, lsl #32
    // 0x13cc76c: r0 = of()
    //     0x13cc76c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cc770: LoadField: r1 = r0->field_87
    //     0x13cc770: ldur            w1, [x0, #0x87]
    // 0x13cc774: DecompressPointer r1
    //     0x13cc774: add             x1, x1, HEAP, lsl #32
    // 0x13cc778: LoadField: r0 = r1->field_7
    //     0x13cc778: ldur            w0, [x1, #7]
    // 0x13cc77c: DecompressPointer r0
    //     0x13cc77c: add             x0, x0, HEAP, lsl #32
    // 0x13cc780: r16 = 14.000000
    //     0x13cc780: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13cc784: ldr             x16, [x16, #0x1d8]
    // 0x13cc788: r30 = Instance_Color
    //     0x13cc788: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc78c: stp             lr, x16, [SP]
    // 0x13cc790: mov             x1, x0
    // 0x13cc794: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13cc794: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13cc798: ldr             x4, [x4, #0xaa0]
    // 0x13cc79c: r0 = copyWith()
    //     0x13cc79c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cc7a0: stur            x0, [fp, #-0x30]
    // 0x13cc7a4: r0 = Text()
    //     0x13cc7a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cc7a8: mov             x1, x0
    // 0x13cc7ac: ldur            x0, [fp, #-0x20]
    // 0x13cc7b0: stur            x1, [fp, #-0x38]
    // 0x13cc7b4: StoreField: r1->field_b = r0
    //     0x13cc7b4: stur            w0, [x1, #0xb]
    // 0x13cc7b8: ldur            x0, [fp, #-0x30]
    // 0x13cc7bc: StoreField: r1->field_13 = r0
    //     0x13cc7bc: stur            w0, [x1, #0x13]
    // 0x13cc7c0: r0 = Padding()
    //     0x13cc7c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cc7c4: mov             x2, x0
    // 0x13cc7c8: r0 = Instance_EdgeInsets
    //     0x13cc7c8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0x13cc7cc: ldr             x0, [x0, #0x100]
    // 0x13cc7d0: stur            x2, [fp, #-0x20]
    // 0x13cc7d4: StoreField: r2->field_f = r0
    //     0x13cc7d4: stur            w0, [x2, #0xf]
    // 0x13cc7d8: ldur            x0, [fp, #-0x38]
    // 0x13cc7dc: StoreField: r2->field_b = r0
    //     0x13cc7dc: stur            w0, [x2, #0xb]
    // 0x13cc7e0: ldur            x0, [fp, #-8]
    // 0x13cc7e4: LoadField: r1 = r0->field_f
    //     0x13cc7e4: ldur            w1, [x0, #0xf]
    // 0x13cc7e8: DecompressPointer r1
    //     0x13cc7e8: add             x1, x1, HEAP, lsl #32
    // 0x13cc7ec: r0 = controller()
    //     0x13cc7ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc7f0: LoadField: r1 = r0->field_4b
    //     0x13cc7f0: ldur            w1, [x0, #0x4b]
    // 0x13cc7f4: DecompressPointer r1
    //     0x13cc7f4: add             x1, x1, HEAP, lsl #32
    // 0x13cc7f8: r0 = value()
    //     0x13cc7f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc7fc: LoadField: r1 = r0->field_b
    //     0x13cc7fc: ldur            w1, [x0, #0xb]
    // 0x13cc800: DecompressPointer r1
    //     0x13cc800: add             x1, x1, HEAP, lsl #32
    // 0x13cc804: cmp             w1, NULL
    // 0x13cc808: b.ne            #0x13cc814
    // 0x13cc80c: r5 = Null
    //     0x13cc80c: mov             x5, NULL
    // 0x13cc810: b               #0x13cc83c
    // 0x13cc814: LoadField: r0 = r1->field_2f
    //     0x13cc814: ldur            w0, [x1, #0x2f]
    // 0x13cc818: DecompressPointer r0
    //     0x13cc818: add             x0, x0, HEAP, lsl #32
    // 0x13cc81c: cmp             w0, NULL
    // 0x13cc820: b.ne            #0x13cc82c
    // 0x13cc824: r0 = Null
    //     0x13cc824: mov             x0, NULL
    // 0x13cc828: b               #0x13cc838
    // 0x13cc82c: LoadField: r1 = r0->field_b
    //     0x13cc82c: ldur            w1, [x0, #0xb]
    // 0x13cc830: DecompressPointer r1
    //     0x13cc830: add             x1, x1, HEAP, lsl #32
    // 0x13cc834: LoadField: r0 = r1->field_b
    //     0x13cc834: ldur            w0, [x1, #0xb]
    // 0x13cc838: mov             x5, x0
    // 0x13cc83c: ldur            x0, [fp, #-8]
    // 0x13cc840: mov             x2, x0
    // 0x13cc844: stur            x5, [fp, #-0x30]
    // 0x13cc848: r1 = Function '<anonymous closure>':.
    //     0x13cc848: add             x1, PP, #0x38, lsl #12  ; [pp+0x380b0] AnonymousClosure: (0x13cbbac), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13cc84c: ldr             x1, [x1, #0xb0]
    // 0x13cc850: r0 = AllocateClosure()
    //     0x13cc850: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cc854: stur            x0, [fp, #-0x38]
    // 0x13cc858: r0 = GridView()
    //     0x13cc858: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0x13cc85c: mov             x1, x0
    // 0x13cc860: ldur            x3, [fp, #-0x38]
    // 0x13cc864: ldur            x5, [fp, #-0x30]
    // 0x13cc868: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0x13cc868: add             x2, PP, #0x38, lsl #12  ; [pp+0x380b8] Obj!SliverGridDelegateWithFixedCrossAxisCount@d564e1
    //     0x13cc86c: ldr             x2, [x2, #0xb8]
    // 0x13cc870: stur            x0, [fp, #-0x30]
    // 0x13cc874: r0 = GridView.builder()
    //     0x13cc874: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0x13cc878: r0 = SizedBox()
    //     0x13cc878: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13cc87c: mov             x1, x0
    // 0x13cc880: r0 = 100.000000
    //     0x13cc880: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x13cc884: stur            x1, [fp, #-0x38]
    // 0x13cc888: StoreField: r1->field_13 = r0
    //     0x13cc888: stur            w0, [x1, #0x13]
    // 0x13cc88c: ldur            x0, [fp, #-0x30]
    // 0x13cc890: StoreField: r1->field_b = r0
    //     0x13cc890: stur            w0, [x1, #0xb]
    // 0x13cc894: r0 = Padding()
    //     0x13cc894: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cc898: mov             x2, x0
    // 0x13cc89c: r0 = Instance_EdgeInsets
    //     0x13cc89c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x13cc8a0: ldr             x0, [x0, #0x868]
    // 0x13cc8a4: stur            x2, [fp, #-0x30]
    // 0x13cc8a8: StoreField: r2->field_f = r0
    //     0x13cc8a8: stur            w0, [x2, #0xf]
    // 0x13cc8ac: ldur            x0, [fp, #-0x38]
    // 0x13cc8b0: StoreField: r2->field_b = r0
    //     0x13cc8b0: stur            w0, [x2, #0xb]
    // 0x13cc8b4: ldur            x0, [fp, #-8]
    // 0x13cc8b8: LoadField: r1 = r0->field_f
    //     0x13cc8b8: ldur            w1, [x0, #0xf]
    // 0x13cc8bc: DecompressPointer r1
    //     0x13cc8bc: add             x1, x1, HEAP, lsl #32
    // 0x13cc8c0: r0 = controller()
    //     0x13cc8c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cc8c4: LoadField: r1 = r0->field_4b
    //     0x13cc8c4: ldur            w1, [x0, #0x4b]
    // 0x13cc8c8: DecompressPointer r1
    //     0x13cc8c8: add             x1, x1, HEAP, lsl #32
    // 0x13cc8cc: r0 = value()
    //     0x13cc8cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cc8d0: LoadField: r1 = r0->field_b
    //     0x13cc8d0: ldur            w1, [x0, #0xb]
    // 0x13cc8d4: DecompressPointer r1
    //     0x13cc8d4: add             x1, x1, HEAP, lsl #32
    // 0x13cc8d8: cmp             w1, NULL
    // 0x13cc8dc: b.ne            #0x13cc8e8
    // 0x13cc8e0: r0 = Null
    //     0x13cc8e0: mov             x0, NULL
    // 0x13cc8e4: b               #0x13cc930
    // 0x13cc8e8: LoadField: r0 = r1->field_2f
    //     0x13cc8e8: ldur            w0, [x1, #0x2f]
    // 0x13cc8ec: DecompressPointer r0
    //     0x13cc8ec: add             x0, x0, HEAP, lsl #32
    // 0x13cc8f0: cmp             w0, NULL
    // 0x13cc8f4: b.ne            #0x13cc900
    // 0x13cc8f8: r0 = Null
    //     0x13cc8f8: mov             x0, NULL
    // 0x13cc8fc: b               #0x13cc930
    // 0x13cc900: LoadField: r1 = r0->field_f
    //     0x13cc900: ldur            w1, [x0, #0xf]
    // 0x13cc904: DecompressPointer r1
    //     0x13cc904: add             x1, x1, HEAP, lsl #32
    // 0x13cc908: cmp             w1, NULL
    // 0x13cc90c: b.ne            #0x13cc918
    // 0x13cc910: r0 = Null
    //     0x13cc910: mov             x0, NULL
    // 0x13cc914: b               #0x13cc930
    // 0x13cc918: LoadField: r0 = r1->field_7
    //     0x13cc918: ldur            w0, [x1, #7]
    // 0x13cc91c: cbnz            w0, #0x13cc928
    // 0x13cc920: r1 = false
    //     0x13cc920: add             x1, NULL, #0x30  ; false
    // 0x13cc924: b               #0x13cc92c
    // 0x13cc928: r1 = true
    //     0x13cc928: add             x1, NULL, #0x20  ; true
    // 0x13cc92c: mov             x0, x1
    // 0x13cc930: cmp             w0, NULL
    // 0x13cc934: b.ne            #0x13cc940
    // 0x13cc938: r6 = false
    //     0x13cc938: add             x6, NULL, #0x30  ; false
    // 0x13cc93c: b               #0x13cc944
    // 0x13cc940: mov             x6, x0
    // 0x13cc944: ldur            x2, [fp, #-8]
    // 0x13cc948: ldur            x5, [fp, #-0x10]
    // 0x13cc94c: ldur            x4, [fp, #-0x28]
    // 0x13cc950: ldur            x3, [fp, #-0x20]
    // 0x13cc954: ldur            x0, [fp, #-0x30]
    // 0x13cc958: stur            x6, [fp, #-0x38]
    // 0x13cc95c: r1 = Instance_Color
    //     0x13cc95c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc960: d0 = 0.100000
    //     0x13cc960: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13cc964: r0 = withOpacity()
    //     0x13cc964: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cc968: mov             x2, x0
    // 0x13cc96c: r1 = Null
    //     0x13cc96c: mov             x1, NULL
    // 0x13cc970: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13cc970: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13cc974: r0 = Border.all()
    //     0x13cc974: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x13cc978: stur            x0, [fp, #-0x40]
    // 0x13cc97c: r0 = BoxDecoration()
    //     0x13cc97c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x13cc980: mov             x2, x0
    // 0x13cc984: ldur            x0, [fp, #-0x40]
    // 0x13cc988: stur            x2, [fp, #-0x48]
    // 0x13cc98c: StoreField: r2->field_f = r0
    //     0x13cc98c: stur            w0, [x2, #0xf]
    // 0x13cc990: r0 = Instance_BoxShape
    //     0x13cc990: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13cc994: ldr             x0, [x0, #0x80]
    // 0x13cc998: StoreField: r2->field_23 = r0
    //     0x13cc998: stur            w0, [x2, #0x23]
    // 0x13cc99c: ldur            x3, [fp, #-8]
    // 0x13cc9a0: LoadField: r1 = r3->field_13
    //     0x13cc9a0: ldur            w1, [x3, #0x13]
    // 0x13cc9a4: DecompressPointer r1
    //     0x13cc9a4: add             x1, x1, HEAP, lsl #32
    // 0x13cc9a8: r0 = of()
    //     0x13cc9a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cc9ac: LoadField: r1 = r0->field_87
    //     0x13cc9ac: ldur            w1, [x0, #0x87]
    // 0x13cc9b0: DecompressPointer r1
    //     0x13cc9b0: add             x1, x1, HEAP, lsl #32
    // 0x13cc9b4: LoadField: r0 = r1->field_7
    //     0x13cc9b4: ldur            w0, [x1, #7]
    // 0x13cc9b8: DecompressPointer r0
    //     0x13cc9b8: add             x0, x0, HEAP, lsl #32
    // 0x13cc9bc: stur            x0, [fp, #-0x40]
    // 0x13cc9c0: r1 = Instance_Color
    //     0x13cc9c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cc9c4: d0 = 0.700000
    //     0x13cc9c4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13cc9c8: ldr             d0, [x17, #0xf48]
    // 0x13cc9cc: r0 = withOpacity()
    //     0x13cc9cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cc9d0: r16 = 14.000000
    //     0x13cc9d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13cc9d4: ldr             x16, [x16, #0x1d8]
    // 0x13cc9d8: stp             x16, x0, [SP]
    // 0x13cc9dc: ldur            x1, [fp, #-0x40]
    // 0x13cc9e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cc9e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cc9e4: ldr             x4, [x4, #0x9b8]
    // 0x13cc9e8: r0 = copyWith()
    //     0x13cc9e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cc9ec: stur            x0, [fp, #-0x40]
    // 0x13cc9f0: r0 = Text()
    //     0x13cc9f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cc9f4: mov             x1, x0
    // 0x13cc9f8: r0 = "SIZE CHART"
    //     0x13cc9f8: add             x0, PP, #0x38, lsl #12  ; [pp+0x380c0] "SIZE CHART"
    //     0x13cc9fc: ldr             x0, [x0, #0xc0]
    // 0x13cca00: stur            x1, [fp, #-0x50]
    // 0x13cca04: StoreField: r1->field_b = r0
    //     0x13cca04: stur            w0, [x1, #0xb]
    // 0x13cca08: ldur            x0, [fp, #-0x40]
    // 0x13cca0c: StoreField: r1->field_13 = r0
    //     0x13cca0c: stur            w0, [x1, #0x13]
    // 0x13cca10: r0 = Center()
    //     0x13cca10: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13cca14: mov             x1, x0
    // 0x13cca18: r0 = Instance_Alignment
    //     0x13cca18: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13cca1c: ldr             x0, [x0, #0xb10]
    // 0x13cca20: stur            x1, [fp, #-0x40]
    // 0x13cca24: StoreField: r1->field_f = r0
    //     0x13cca24: stur            w0, [x1, #0xf]
    // 0x13cca28: ldur            x0, [fp, #-0x50]
    // 0x13cca2c: StoreField: r1->field_b = r0
    //     0x13cca2c: stur            w0, [x1, #0xb]
    // 0x13cca30: r0 = Container()
    //     0x13cca30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13cca34: stur            x0, [fp, #-0x50]
    // 0x13cca38: r16 = 44.000000
    //     0x13cca38: add             x16, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0x13cca3c: ldr             x16, [x16, #0xad8]
    // 0x13cca40: ldur            lr, [fp, #-0x48]
    // 0x13cca44: stp             lr, x16, [SP, #8]
    // 0x13cca48: ldur            x16, [fp, #-0x40]
    // 0x13cca4c: str             x16, [SP]
    // 0x13cca50: mov             x1, x0
    // 0x13cca54: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x13cca54: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x13cca58: ldr             x4, [x4, #0xc78]
    // 0x13cca5c: r0 = Container()
    //     0x13cca5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13cca60: r0 = InkWell()
    //     0x13cca60: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13cca64: mov             x3, x0
    // 0x13cca68: ldur            x0, [fp, #-0x50]
    // 0x13cca6c: stur            x3, [fp, #-0x40]
    // 0x13cca70: StoreField: r3->field_b = r0
    //     0x13cca70: stur            w0, [x3, #0xb]
    // 0x13cca74: ldur            x2, [fp, #-8]
    // 0x13cca78: r1 = Function '<anonymous closure>':.
    //     0x13cca78: add             x1, PP, #0x38, lsl #12  ; [pp+0x380c8] AnonymousClosure: (0x13cd390), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13cca7c: ldr             x1, [x1, #0xc8]
    // 0x13cca80: r0 = AllocateClosure()
    //     0x13cca80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cca84: mov             x1, x0
    // 0x13cca88: ldur            x0, [fp, #-0x40]
    // 0x13cca8c: StoreField: r0->field_f = r1
    //     0x13cca8c: stur            w1, [x0, #0xf]
    // 0x13cca90: r1 = true
    //     0x13cca90: add             x1, NULL, #0x20  ; true
    // 0x13cca94: StoreField: r0->field_43 = r1
    //     0x13cca94: stur            w1, [x0, #0x43]
    // 0x13cca98: r2 = Instance_BoxShape
    //     0x13cca98: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13cca9c: ldr             x2, [x2, #0x80]
    // 0x13ccaa0: StoreField: r0->field_47 = r2
    //     0x13ccaa0: stur            w2, [x0, #0x47]
    // 0x13ccaa4: StoreField: r0->field_6f = r1
    //     0x13ccaa4: stur            w1, [x0, #0x6f]
    // 0x13ccaa8: r2 = false
    //     0x13ccaa8: add             x2, NULL, #0x30  ; false
    // 0x13ccaac: StoreField: r0->field_73 = r2
    //     0x13ccaac: stur            w2, [x0, #0x73]
    // 0x13ccab0: StoreField: r0->field_83 = r1
    //     0x13ccab0: stur            w1, [x0, #0x83]
    // 0x13ccab4: StoreField: r0->field_7b = r2
    //     0x13ccab4: stur            w2, [x0, #0x7b]
    // 0x13ccab8: r0 = Visibility()
    //     0x13ccab8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13ccabc: mov             x3, x0
    // 0x13ccac0: ldur            x0, [fp, #-0x40]
    // 0x13ccac4: stur            x3, [fp, #-0x48]
    // 0x13ccac8: StoreField: r3->field_b = r0
    //     0x13ccac8: stur            w0, [x3, #0xb]
    // 0x13ccacc: r0 = Instance_SizedBox
    //     0x13ccacc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13ccad0: StoreField: r3->field_f = r0
    //     0x13ccad0: stur            w0, [x3, #0xf]
    // 0x13ccad4: ldur            x1, [fp, #-0x38]
    // 0x13ccad8: StoreField: r3->field_13 = r1
    //     0x13ccad8: stur            w1, [x3, #0x13]
    // 0x13ccadc: r4 = false
    //     0x13ccadc: add             x4, NULL, #0x30  ; false
    // 0x13ccae0: ArrayStore: r3[0] = r4  ; List_4
    //     0x13ccae0: stur            w4, [x3, #0x17]
    // 0x13ccae4: StoreField: r3->field_1b = r4
    //     0x13ccae4: stur            w4, [x3, #0x1b]
    // 0x13ccae8: StoreField: r3->field_1f = r4
    //     0x13ccae8: stur            w4, [x3, #0x1f]
    // 0x13ccaec: StoreField: r3->field_23 = r4
    //     0x13ccaec: stur            w4, [x3, #0x23]
    // 0x13ccaf0: StoreField: r3->field_27 = r4
    //     0x13ccaf0: stur            w4, [x3, #0x27]
    // 0x13ccaf4: StoreField: r3->field_2b = r4
    //     0x13ccaf4: stur            w4, [x3, #0x2b]
    // 0x13ccaf8: r1 = Null
    //     0x13ccaf8: mov             x1, NULL
    // 0x13ccafc: r2 = 10
    //     0x13ccafc: movz            x2, #0xa
    // 0x13ccb00: r0 = AllocateArray()
    //     0x13ccb00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ccb04: mov             x2, x0
    // 0x13ccb08: ldur            x0, [fp, #-0x28]
    // 0x13ccb0c: stur            x2, [fp, #-0x38]
    // 0x13ccb10: StoreField: r2->field_f = r0
    //     0x13ccb10: stur            w0, [x2, #0xf]
    // 0x13ccb14: ldur            x0, [fp, #-0x20]
    // 0x13ccb18: StoreField: r2->field_13 = r0
    //     0x13ccb18: stur            w0, [x2, #0x13]
    // 0x13ccb1c: ldur            x0, [fp, #-0x30]
    // 0x13ccb20: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ccb20: stur            w0, [x2, #0x17]
    // 0x13ccb24: ldur            x0, [fp, #-0x48]
    // 0x13ccb28: StoreField: r2->field_1b = r0
    //     0x13ccb28: stur            w0, [x2, #0x1b]
    // 0x13ccb2c: r16 = Instance_SizedBox
    //     0x13ccb2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x13ccb30: ldr             x16, [x16, #0x9f0]
    // 0x13ccb34: StoreField: r2->field_1f = r16
    //     0x13ccb34: stur            w16, [x2, #0x1f]
    // 0x13ccb38: r1 = <Widget>
    //     0x13ccb38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13ccb3c: r0 = AllocateGrowableArray()
    //     0x13ccb3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ccb40: mov             x1, x0
    // 0x13ccb44: ldur            x0, [fp, #-0x38]
    // 0x13ccb48: stur            x1, [fp, #-0x20]
    // 0x13ccb4c: StoreField: r1->field_f = r0
    //     0x13ccb4c: stur            w0, [x1, #0xf]
    // 0x13ccb50: r0 = 10
    //     0x13ccb50: movz            x0, #0xa
    // 0x13ccb54: StoreField: r1->field_b = r0
    //     0x13ccb54: stur            w0, [x1, #0xb]
    // 0x13ccb58: r0 = Column()
    //     0x13ccb58: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13ccb5c: mov             x1, x0
    // 0x13ccb60: r0 = Instance_Axis
    //     0x13ccb60: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13ccb64: stur            x1, [fp, #-0x28]
    // 0x13ccb68: StoreField: r1->field_f = r0
    //     0x13ccb68: stur            w0, [x1, #0xf]
    // 0x13ccb6c: r2 = Instance_MainAxisAlignment
    //     0x13ccb6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13ccb70: ldr             x2, [x2, #0xa08]
    // 0x13ccb74: StoreField: r1->field_13 = r2
    //     0x13ccb74: stur            w2, [x1, #0x13]
    // 0x13ccb78: r3 = Instance_MainAxisSize
    //     0x13ccb78: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13ccb7c: ldr             x3, [x3, #0xa10]
    // 0x13ccb80: ArrayStore: r1[0] = r3  ; List_4
    //     0x13ccb80: stur            w3, [x1, #0x17]
    // 0x13ccb84: r4 = Instance_CrossAxisAlignment
    //     0x13ccb84: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13ccb88: ldr             x4, [x4, #0x890]
    // 0x13ccb8c: StoreField: r1->field_1b = r4
    //     0x13ccb8c: stur            w4, [x1, #0x1b]
    // 0x13ccb90: r5 = Instance_VerticalDirection
    //     0x13ccb90: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13ccb94: ldr             x5, [x5, #0xa20]
    // 0x13ccb98: StoreField: r1->field_23 = r5
    //     0x13ccb98: stur            w5, [x1, #0x23]
    // 0x13ccb9c: r6 = Instance_Clip
    //     0x13ccb9c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13ccba0: ldr             x6, [x6, #0x38]
    // 0x13ccba4: StoreField: r1->field_2b = r6
    //     0x13ccba4: stur            w6, [x1, #0x2b]
    // 0x13ccba8: StoreField: r1->field_2f = rZR
    //     0x13ccba8: stur            xzr, [x1, #0x2f]
    // 0x13ccbac: ldur            x7, [fp, #-0x20]
    // 0x13ccbb0: StoreField: r1->field_b = r7
    //     0x13ccbb0: stur            w7, [x1, #0xb]
    // 0x13ccbb4: r0 = Visibility()
    //     0x13ccbb4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13ccbb8: mov             x2, x0
    // 0x13ccbbc: ldur            x0, [fp, #-0x28]
    // 0x13ccbc0: stur            x2, [fp, #-0x20]
    // 0x13ccbc4: StoreField: r2->field_b = r0
    //     0x13ccbc4: stur            w0, [x2, #0xb]
    // 0x13ccbc8: r0 = Instance_SizedBox
    //     0x13ccbc8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13ccbcc: StoreField: r2->field_f = r0
    //     0x13ccbcc: stur            w0, [x2, #0xf]
    // 0x13ccbd0: ldur            x1, [fp, #-0x10]
    // 0x13ccbd4: StoreField: r2->field_13 = r1
    //     0x13ccbd4: stur            w1, [x2, #0x13]
    // 0x13ccbd8: r3 = false
    //     0x13ccbd8: add             x3, NULL, #0x30  ; false
    // 0x13ccbdc: ArrayStore: r2[0] = r3  ; List_4
    //     0x13ccbdc: stur            w3, [x2, #0x17]
    // 0x13ccbe0: StoreField: r2->field_1b = r3
    //     0x13ccbe0: stur            w3, [x2, #0x1b]
    // 0x13ccbe4: StoreField: r2->field_1f = r3
    //     0x13ccbe4: stur            w3, [x2, #0x1f]
    // 0x13ccbe8: StoreField: r2->field_23 = r3
    //     0x13ccbe8: stur            w3, [x2, #0x23]
    // 0x13ccbec: StoreField: r2->field_27 = r3
    //     0x13ccbec: stur            w3, [x2, #0x27]
    // 0x13ccbf0: StoreField: r2->field_2b = r3
    //     0x13ccbf0: stur            w3, [x2, #0x2b]
    // 0x13ccbf4: ldur            x4, [fp, #-8]
    // 0x13ccbf8: LoadField: r1 = r4->field_f
    //     0x13ccbf8: ldur            w1, [x4, #0xf]
    // 0x13ccbfc: DecompressPointer r1
    //     0x13ccbfc: add             x1, x1, HEAP, lsl #32
    // 0x13ccc00: r0 = controller()
    //     0x13ccc00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ccc04: LoadField: r1 = r0->field_4b
    //     0x13ccc04: ldur            w1, [x0, #0x4b]
    // 0x13ccc08: DecompressPointer r1
    //     0x13ccc08: add             x1, x1, HEAP, lsl #32
    // 0x13ccc0c: r0 = value()
    //     0x13ccc0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ccc10: LoadField: r1 = r0->field_b
    //     0x13ccc10: ldur            w1, [x0, #0xb]
    // 0x13ccc14: DecompressPointer r1
    //     0x13ccc14: add             x1, x1, HEAP, lsl #32
    // 0x13ccc18: cmp             w1, NULL
    // 0x13ccc1c: b.ne            #0x13ccc28
    // 0x13ccc20: r0 = Null
    //     0x13ccc20: mov             x0, NULL
    // 0x13ccc24: b               #0x13ccc60
    // 0x13ccc28: LoadField: r0 = r1->field_2f
    //     0x13ccc28: ldur            w0, [x1, #0x2f]
    // 0x13ccc2c: DecompressPointer r0
    //     0x13ccc2c: add             x0, x0, HEAP, lsl #32
    // 0x13ccc30: cmp             w0, NULL
    // 0x13ccc34: b.ne            #0x13ccc40
    // 0x13ccc38: r0 = Null
    //     0x13ccc38: mov             x0, NULL
    // 0x13ccc3c: b               #0x13ccc60
    // 0x13ccc40: LoadField: r1 = r0->field_13
    //     0x13ccc40: ldur            w1, [x0, #0x13]
    // 0x13ccc44: DecompressPointer r1
    //     0x13ccc44: add             x1, x1, HEAP, lsl #32
    // 0x13ccc48: LoadField: r0 = r1->field_b
    //     0x13ccc48: ldur            w0, [x1, #0xb]
    // 0x13ccc4c: cbnz            w0, #0x13ccc58
    // 0x13ccc50: r1 = false
    //     0x13ccc50: add             x1, NULL, #0x30  ; false
    // 0x13ccc54: b               #0x13ccc5c
    // 0x13ccc58: r1 = true
    //     0x13ccc58: add             x1, NULL, #0x20  ; true
    // 0x13ccc5c: mov             x0, x1
    // 0x13ccc60: cmp             w0, NULL
    // 0x13ccc64: b.ne            #0x13ccc70
    // 0x13ccc68: r4 = false
    //     0x13ccc68: add             x4, NULL, #0x30  ; false
    // 0x13ccc6c: b               #0x13ccc74
    // 0x13ccc70: mov             x4, x0
    // 0x13ccc74: ldur            x2, [fp, #-8]
    // 0x13ccc78: ldur            x3, [fp, #-0x18]
    // 0x13ccc7c: ldur            x0, [fp, #-0x20]
    // 0x13ccc80: stur            x4, [fp, #-0x10]
    // 0x13ccc84: LoadField: r1 = r2->field_13
    //     0x13ccc84: ldur            w1, [x2, #0x13]
    // 0x13ccc88: DecompressPointer r1
    //     0x13ccc88: add             x1, x1, HEAP, lsl #32
    // 0x13ccc8c: r0 = of()
    //     0x13ccc8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ccc90: LoadField: r1 = r0->field_87
    //     0x13ccc90: ldur            w1, [x0, #0x87]
    // 0x13ccc94: DecompressPointer r1
    //     0x13ccc94: add             x1, x1, HEAP, lsl #32
    // 0x13ccc98: LoadField: r0 = r1->field_7
    //     0x13ccc98: ldur            w0, [x1, #7]
    // 0x13ccc9c: DecompressPointer r0
    //     0x13ccc9c: add             x0, x0, HEAP, lsl #32
    // 0x13ccca0: r16 = Instance_Color
    //     0x13ccca0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ccca4: r30 = 14.000000
    //     0x13ccca4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ccca8: ldr             lr, [lr, #0x1d8]
    // 0x13cccac: stp             lr, x16, [SP]
    // 0x13cccb0: mov             x1, x0
    // 0x13cccb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cccb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cccb8: ldr             x4, [x4, #0x9b8]
    // 0x13cccbc: r0 = copyWith()
    //     0x13cccbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cccc0: stur            x0, [fp, #-0x28]
    // 0x13cccc4: r0 = TextSpan()
    //     0x13cccc4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13cccc8: mov             x3, x0
    // 0x13ccccc: r0 = "Size with different prices"
    //     0x13ccccc: add             x0, PP, #0x38, lsl #12  ; [pp+0x380d0] "Size with different prices"
    //     0x13cccd0: ldr             x0, [x0, #0xd0]
    // 0x13cccd4: stur            x3, [fp, #-0x30]
    // 0x13cccd8: StoreField: r3->field_b = r0
    //     0x13cccd8: stur            w0, [x3, #0xb]
    // 0x13cccdc: r0 = Instance__DeferringMouseCursor
    //     0x13cccdc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13ccce0: ArrayStore: r3[0] = r0  ; List_4
    //     0x13ccce0: stur            w0, [x3, #0x17]
    // 0x13ccce4: ldur            x1, [fp, #-0x28]
    // 0x13ccce8: StoreField: r3->field_7 = r1
    //     0x13ccce8: stur            w1, [x3, #7]
    // 0x13cccec: r1 = Null
    //     0x13cccec: mov             x1, NULL
    // 0x13cccf0: r2 = 2
    //     0x13cccf0: movz            x2, #0x2
    // 0x13cccf4: r0 = AllocateArray()
    //     0x13cccf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cccf8: mov             x2, x0
    // 0x13cccfc: ldur            x0, [fp, #-0x30]
    // 0x13ccd00: stur            x2, [fp, #-0x28]
    // 0x13ccd04: StoreField: r2->field_f = r0
    //     0x13ccd04: stur            w0, [x2, #0xf]
    // 0x13ccd08: r1 = <InlineSpan>
    //     0x13ccd08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x13ccd0c: ldr             x1, [x1, #0xe40]
    // 0x13ccd10: r0 = AllocateGrowableArray()
    //     0x13ccd10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ccd14: mov             x1, x0
    // 0x13ccd18: ldur            x0, [fp, #-0x28]
    // 0x13ccd1c: stur            x1, [fp, #-0x30]
    // 0x13ccd20: StoreField: r1->field_f = r0
    //     0x13ccd20: stur            w0, [x1, #0xf]
    // 0x13ccd24: r0 = 2
    //     0x13ccd24: movz            x0, #0x2
    // 0x13ccd28: StoreField: r1->field_b = r0
    //     0x13ccd28: stur            w0, [x1, #0xb]
    // 0x13ccd2c: r0 = TextSpan()
    //     0x13ccd2c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13ccd30: mov             x1, x0
    // 0x13ccd34: ldur            x0, [fp, #-0x30]
    // 0x13ccd38: stur            x1, [fp, #-0x28]
    // 0x13ccd3c: StoreField: r1->field_f = r0
    //     0x13ccd3c: stur            w0, [x1, #0xf]
    // 0x13ccd40: r0 = Instance__DeferringMouseCursor
    //     0x13ccd40: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13ccd44: ArrayStore: r1[0] = r0  ; List_4
    //     0x13ccd44: stur            w0, [x1, #0x17]
    // 0x13ccd48: r0 = RichText()
    //     0x13ccd48: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x13ccd4c: mov             x1, x0
    // 0x13ccd50: ldur            x2, [fp, #-0x28]
    // 0x13ccd54: stur            x0, [fp, #-0x28]
    // 0x13ccd58: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13ccd58: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13ccd5c: r0 = RichText()
    //     0x13ccd5c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x13ccd60: ldur            x2, [fp, #-8]
    // 0x13ccd64: LoadField: r1 = r2->field_13
    //     0x13ccd64: ldur            w1, [x2, #0x13]
    // 0x13ccd68: DecompressPointer r1
    //     0x13ccd68: add             x1, x1, HEAP, lsl #32
    // 0x13ccd6c: r0 = of()
    //     0x13ccd6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ccd70: LoadField: r1 = r0->field_87
    //     0x13ccd70: ldur            w1, [x0, #0x87]
    // 0x13ccd74: DecompressPointer r1
    //     0x13ccd74: add             x1, x1, HEAP, lsl #32
    // 0x13ccd78: LoadField: r0 = r1->field_2b
    //     0x13ccd78: ldur            w0, [x1, #0x2b]
    // 0x13ccd7c: DecompressPointer r0
    //     0x13ccd7c: add             x0, x0, HEAP, lsl #32
    // 0x13ccd80: stur            x0, [fp, #-0x30]
    // 0x13ccd84: r1 = Instance_Color
    //     0x13ccd84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ccd88: d0 = 0.700000
    //     0x13ccd88: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13ccd8c: ldr             d0, [x17, #0xf48]
    // 0x13ccd90: r0 = withOpacity()
    //     0x13ccd90: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13ccd94: r16 = 12.000000
    //     0x13ccd94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13ccd98: ldr             x16, [x16, #0x9e8]
    // 0x13ccd9c: stp             x16, x0, [SP]
    // 0x13ccda0: ldur            x1, [fp, #-0x30]
    // 0x13ccda4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13ccda4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13ccda8: ldr             x4, [x4, #0x9b8]
    // 0x13ccdac: r0 = copyWith()
    //     0x13ccdac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ccdb0: stur            x0, [fp, #-0x30]
    // 0x13ccdb4: r0 = Text()
    //     0x13ccdb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ccdb8: mov             x3, x0
    // 0x13ccdbc: r0 = "Price of some variants/sizes of this product doesn’t match with the previously ordered product. Tap on the below button if you want to exchange with a different priced variant/size."
    //     0x13ccdbc: add             x0, PP, #0x38, lsl #12  ; [pp+0x380d8] "Price of some variants/sizes of this product doesn’t match with the previously ordered product. Tap on the below button if you want to exchange with a different priced variant/size."
    //     0x13ccdc0: ldr             x0, [x0, #0xd8]
    // 0x13ccdc4: stur            x3, [fp, #-0x38]
    // 0x13ccdc8: StoreField: r3->field_b = r0
    //     0x13ccdc8: stur            w0, [x3, #0xb]
    // 0x13ccdcc: ldur            x0, [fp, #-0x30]
    // 0x13ccdd0: StoreField: r3->field_13 = r0
    //     0x13ccdd0: stur            w0, [x3, #0x13]
    // 0x13ccdd4: r1 = Null
    //     0x13ccdd4: mov             x1, NULL
    // 0x13ccdd8: r2 = 4
    //     0x13ccdd8: movz            x2, #0x4
    // 0x13ccddc: r0 = AllocateArray()
    //     0x13ccddc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ccde0: stur            x0, [fp, #-0x30]
    // 0x13ccde4: r16 = "Available sizes: "
    //     0x13ccde4: add             x16, PP, #0x38, lsl #12  ; [pp+0x380e0] "Available sizes: "
    //     0x13ccde8: ldr             x16, [x16, #0xe0]
    // 0x13ccdec: StoreField: r0->field_f = r16
    //     0x13ccdec: stur            w16, [x0, #0xf]
    // 0x13ccdf0: ldur            x2, [fp, #-8]
    // 0x13ccdf4: LoadField: r1 = r2->field_f
    //     0x13ccdf4: ldur            w1, [x2, #0xf]
    // 0x13ccdf8: DecompressPointer r1
    //     0x13ccdf8: add             x1, x1, HEAP, lsl #32
    // 0x13ccdfc: r0 = controller()
    //     0x13ccdfc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cce00: LoadField: r1 = r0->field_57
    //     0x13cce00: ldur            w1, [x0, #0x57]
    // 0x13cce04: DecompressPointer r1
    //     0x13cce04: add             x1, x1, HEAP, lsl #32
    // 0x13cce08: r16 = ", "
    //     0x13cce08: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x13cce0c: str             x16, [SP]
    // 0x13cce10: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x13cce10: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x13cce14: r0 = join()
    //     0x13cce14: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0x13cce18: ldur            x1, [fp, #-0x30]
    // 0x13cce1c: ArrayStore: r1[1] = r0  ; List_4
    //     0x13cce1c: add             x25, x1, #0x13
    //     0x13cce20: str             w0, [x25]
    //     0x13cce24: tbz             w0, #0, #0x13cce40
    //     0x13cce28: ldurb           w16, [x1, #-1]
    //     0x13cce2c: ldurb           w17, [x0, #-1]
    //     0x13cce30: and             x16, x17, x16, lsr #2
    //     0x13cce34: tst             x16, HEAP, lsr #32
    //     0x13cce38: b.eq            #0x13cce40
    //     0x13cce3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cce40: ldur            x16, [fp, #-0x30]
    // 0x13cce44: str             x16, [SP]
    // 0x13cce48: r0 = _interpolate()
    //     0x13cce48: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13cce4c: ldur            x2, [fp, #-8]
    // 0x13cce50: stur            x0, [fp, #-0x30]
    // 0x13cce54: LoadField: r1 = r2->field_13
    //     0x13cce54: ldur            w1, [x2, #0x13]
    // 0x13cce58: DecompressPointer r1
    //     0x13cce58: add             x1, x1, HEAP, lsl #32
    // 0x13cce5c: r0 = of()
    //     0x13cce5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cce60: LoadField: r1 = r0->field_87
    //     0x13cce60: ldur            w1, [x0, #0x87]
    // 0x13cce64: DecompressPointer r1
    //     0x13cce64: add             x1, x1, HEAP, lsl #32
    // 0x13cce68: LoadField: r0 = r1->field_7
    //     0x13cce68: ldur            w0, [x1, #7]
    // 0x13cce6c: DecompressPointer r0
    //     0x13cce6c: add             x0, x0, HEAP, lsl #32
    // 0x13cce70: r16 = Instance_Color
    //     0x13cce70: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cce74: r30 = 12.000000
    //     0x13cce74: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13cce78: ldr             lr, [lr, #0x9e8]
    // 0x13cce7c: stp             lr, x16, [SP]
    // 0x13cce80: mov             x1, x0
    // 0x13cce84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cce84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cce88: ldr             x4, [x4, #0x9b8]
    // 0x13cce8c: r0 = copyWith()
    //     0x13cce8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cce90: stur            x0, [fp, #-0x40]
    // 0x13cce94: r0 = Text()
    //     0x13cce94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cce98: mov             x2, x0
    // 0x13cce9c: ldur            x0, [fp, #-0x30]
    // 0x13ccea0: stur            x2, [fp, #-0x48]
    // 0x13ccea4: StoreField: r2->field_b = r0
    //     0x13ccea4: stur            w0, [x2, #0xb]
    // 0x13ccea8: ldur            x0, [fp, #-0x40]
    // 0x13cceac: StoreField: r2->field_13 = r0
    //     0x13cceac: stur            w0, [x2, #0x13]
    // 0x13cceb0: ldur            x0, [fp, #-8]
    // 0x13cceb4: LoadField: r1 = r0->field_13
    //     0x13cceb4: ldur            w1, [x0, #0x13]
    // 0x13cceb8: DecompressPointer r1
    //     0x13cceb8: add             x1, x1, HEAP, lsl #32
    // 0x13ccebc: r0 = of()
    //     0x13ccebc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ccec0: r17 = 307
    //     0x13ccec0: movz            x17, #0x133
    // 0x13ccec4: ldr             w2, [x0, x17]
    // 0x13ccec8: DecompressPointer r2
    //     0x13ccec8: add             x2, x2, HEAP, lsl #32
    // 0x13ccecc: ldur            x0, [fp, #-8]
    // 0x13cced0: stur            x2, [fp, #-0x30]
    // 0x13cced4: LoadField: r1 = r0->field_13
    //     0x13cced4: ldur            w1, [x0, #0x13]
    // 0x13cced8: DecompressPointer r1
    //     0x13cced8: add             x1, x1, HEAP, lsl #32
    // 0x13ccedc: r0 = of()
    //     0x13ccedc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ccee0: LoadField: r1 = r0->field_87
    //     0x13ccee0: ldur            w1, [x0, #0x87]
    // 0x13ccee4: DecompressPointer r1
    //     0x13ccee4: add             x1, x1, HEAP, lsl #32
    // 0x13ccee8: LoadField: r0 = r1->field_7
    //     0x13ccee8: ldur            w0, [x1, #7]
    // 0x13cceec: DecompressPointer r0
    //     0x13cceec: add             x0, x0, HEAP, lsl #32
    // 0x13ccef0: r16 = 14.000000
    //     0x13ccef0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ccef4: ldr             x16, [x16, #0x1d8]
    // 0x13ccef8: r30 = Instance_Color
    //     0x13ccef8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13ccefc: stp             lr, x16, [SP]
    // 0x13ccf00: mov             x1, x0
    // 0x13ccf04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13ccf04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13ccf08: ldr             x4, [x4, #0xaa0]
    // 0x13ccf0c: r0 = copyWith()
    //     0x13ccf0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ccf10: stur            x0, [fp, #-0x40]
    // 0x13ccf14: r0 = Text()
    //     0x13ccf14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ccf18: mov             x3, x0
    // 0x13ccf1c: r0 = "EXCHANGE WITH NEW PRODUCT"
    //     0x13ccf1c: add             x0, PP, #0x38, lsl #12  ; [pp+0x380e8] "EXCHANGE WITH NEW PRODUCT"
    //     0x13ccf20: ldr             x0, [x0, #0xe8]
    // 0x13ccf24: stur            x3, [fp, #-0x50]
    // 0x13ccf28: StoreField: r3->field_b = r0
    //     0x13ccf28: stur            w0, [x3, #0xb]
    // 0x13ccf2c: ldur            x0, [fp, #-0x40]
    // 0x13ccf30: StoreField: r3->field_13 = r0
    //     0x13ccf30: stur            w0, [x3, #0x13]
    // 0x13ccf34: ldur            x2, [fp, #-8]
    // 0x13ccf38: r1 = Function '<anonymous closure>':.
    //     0x13ccf38: add             x1, PP, #0x38, lsl #12  ; [pp+0x380f0] AnonymousClosure: (0x13cd214), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13ccf3c: ldr             x1, [x1, #0xf0]
    // 0x13ccf40: r0 = AllocateClosure()
    //     0x13ccf40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ccf44: stur            x0, [fp, #-8]
    // 0x13ccf48: r0 = TextButton()
    //     0x13ccf48: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13ccf4c: mov             x1, x0
    // 0x13ccf50: ldur            x0, [fp, #-8]
    // 0x13ccf54: stur            x1, [fp, #-0x40]
    // 0x13ccf58: StoreField: r1->field_b = r0
    //     0x13ccf58: stur            w0, [x1, #0xb]
    // 0x13ccf5c: r0 = false
    //     0x13ccf5c: add             x0, NULL, #0x30  ; false
    // 0x13ccf60: StoreField: r1->field_27 = r0
    //     0x13ccf60: stur            w0, [x1, #0x27]
    // 0x13ccf64: r2 = true
    //     0x13ccf64: add             x2, NULL, #0x20  ; true
    // 0x13ccf68: StoreField: r1->field_2f = r2
    //     0x13ccf68: stur            w2, [x1, #0x2f]
    // 0x13ccf6c: ldur            x2, [fp, #-0x50]
    // 0x13ccf70: StoreField: r1->field_37 = r2
    //     0x13ccf70: stur            w2, [x1, #0x37]
    // 0x13ccf74: r0 = TextButtonTheme()
    //     0x13ccf74: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13ccf78: mov             x1, x0
    // 0x13ccf7c: ldur            x0, [fp, #-0x30]
    // 0x13ccf80: stur            x1, [fp, #-8]
    // 0x13ccf84: StoreField: r1->field_f = r0
    //     0x13ccf84: stur            w0, [x1, #0xf]
    // 0x13ccf88: ldur            x0, [fp, #-0x40]
    // 0x13ccf8c: StoreField: r1->field_b = r0
    //     0x13ccf8c: stur            w0, [x1, #0xb]
    // 0x13ccf90: r0 = SizedBox()
    //     0x13ccf90: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13ccf94: mov             x3, x0
    // 0x13ccf98: r0 = inf
    //     0x13ccf98: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x13ccf9c: ldr             x0, [x0, #0x9f8]
    // 0x13ccfa0: stur            x3, [fp, #-0x30]
    // 0x13ccfa4: StoreField: r3->field_f = r0
    //     0x13ccfa4: stur            w0, [x3, #0xf]
    // 0x13ccfa8: r0 = 48.000000
    //     0x13ccfa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13ccfac: ldr             x0, [x0, #0xad8]
    // 0x13ccfb0: StoreField: r3->field_13 = r0
    //     0x13ccfb0: stur            w0, [x3, #0x13]
    // 0x13ccfb4: ldur            x0, [fp, #-8]
    // 0x13ccfb8: StoreField: r3->field_b = r0
    //     0x13ccfb8: stur            w0, [x3, #0xb]
    // 0x13ccfbc: r1 = Null
    //     0x13ccfbc: mov             x1, NULL
    // 0x13ccfc0: r2 = 14
    //     0x13ccfc0: movz            x2, #0xe
    // 0x13ccfc4: r0 = AllocateArray()
    //     0x13ccfc4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ccfc8: mov             x2, x0
    // 0x13ccfcc: ldur            x0, [fp, #-0x28]
    // 0x13ccfd0: stur            x2, [fp, #-8]
    // 0x13ccfd4: StoreField: r2->field_f = r0
    //     0x13ccfd4: stur            w0, [x2, #0xf]
    // 0x13ccfd8: r16 = Instance_SizedBox
    //     0x13ccfd8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x13ccfdc: ldr             x16, [x16, #0xc70]
    // 0x13ccfe0: StoreField: r2->field_13 = r16
    //     0x13ccfe0: stur            w16, [x2, #0x13]
    // 0x13ccfe4: ldur            x0, [fp, #-0x38]
    // 0x13ccfe8: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ccfe8: stur            w0, [x2, #0x17]
    // 0x13ccfec: r16 = Instance_SizedBox
    //     0x13ccfec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x13ccff0: ldr             x16, [x16, #0x8f0]
    // 0x13ccff4: StoreField: r2->field_1b = r16
    //     0x13ccff4: stur            w16, [x2, #0x1b]
    // 0x13ccff8: ldur            x0, [fp, #-0x48]
    // 0x13ccffc: StoreField: r2->field_1f = r0
    //     0x13ccffc: stur            w0, [x2, #0x1f]
    // 0x13cd000: r16 = Instance_SizedBox
    //     0x13cd000: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x13cd004: ldr             x16, [x16, #0x8f0]
    // 0x13cd008: StoreField: r2->field_23 = r16
    //     0x13cd008: stur            w16, [x2, #0x23]
    // 0x13cd00c: ldur            x0, [fp, #-0x30]
    // 0x13cd010: StoreField: r2->field_27 = r0
    //     0x13cd010: stur            w0, [x2, #0x27]
    // 0x13cd014: r1 = <Widget>
    //     0x13cd014: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cd018: r0 = AllocateGrowableArray()
    //     0x13cd018: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cd01c: mov             x1, x0
    // 0x13cd020: ldur            x0, [fp, #-8]
    // 0x13cd024: stur            x1, [fp, #-0x28]
    // 0x13cd028: StoreField: r1->field_f = r0
    //     0x13cd028: stur            w0, [x1, #0xf]
    // 0x13cd02c: r0 = 14
    //     0x13cd02c: movz            x0, #0xe
    // 0x13cd030: StoreField: r1->field_b = r0
    //     0x13cd030: stur            w0, [x1, #0xb]
    // 0x13cd034: r0 = Column()
    //     0x13cd034: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13cd038: mov             x1, x0
    // 0x13cd03c: r0 = Instance_Axis
    //     0x13cd03c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cd040: stur            x1, [fp, #-8]
    // 0x13cd044: StoreField: r1->field_f = r0
    //     0x13cd044: stur            w0, [x1, #0xf]
    // 0x13cd048: r2 = Instance_MainAxisAlignment
    //     0x13cd048: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cd04c: ldr             x2, [x2, #0xa08]
    // 0x13cd050: StoreField: r1->field_13 = r2
    //     0x13cd050: stur            w2, [x1, #0x13]
    // 0x13cd054: r3 = Instance_MainAxisSize
    //     0x13cd054: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13cd058: ldr             x3, [x3, #0xa10]
    // 0x13cd05c: ArrayStore: r1[0] = r3  ; List_4
    //     0x13cd05c: stur            w3, [x1, #0x17]
    // 0x13cd060: r4 = Instance_CrossAxisAlignment
    //     0x13cd060: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13cd064: ldr             x4, [x4, #0x890]
    // 0x13cd068: StoreField: r1->field_1b = r4
    //     0x13cd068: stur            w4, [x1, #0x1b]
    // 0x13cd06c: r5 = Instance_VerticalDirection
    //     0x13cd06c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cd070: ldr             x5, [x5, #0xa20]
    // 0x13cd074: StoreField: r1->field_23 = r5
    //     0x13cd074: stur            w5, [x1, #0x23]
    // 0x13cd078: r6 = Instance_Clip
    //     0x13cd078: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13cd07c: ldr             x6, [x6, #0x38]
    // 0x13cd080: StoreField: r1->field_2b = r6
    //     0x13cd080: stur            w6, [x1, #0x2b]
    // 0x13cd084: StoreField: r1->field_2f = rZR
    //     0x13cd084: stur            xzr, [x1, #0x2f]
    // 0x13cd088: ldur            x7, [fp, #-0x28]
    // 0x13cd08c: StoreField: r1->field_b = r7
    //     0x13cd08c: stur            w7, [x1, #0xb]
    // 0x13cd090: r0 = Visibility()
    //     0x13cd090: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13cd094: mov             x3, x0
    // 0x13cd098: ldur            x0, [fp, #-8]
    // 0x13cd09c: stur            x3, [fp, #-0x28]
    // 0x13cd0a0: StoreField: r3->field_b = r0
    //     0x13cd0a0: stur            w0, [x3, #0xb]
    // 0x13cd0a4: r0 = Instance_SizedBox
    //     0x13cd0a4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13cd0a8: StoreField: r3->field_f = r0
    //     0x13cd0a8: stur            w0, [x3, #0xf]
    // 0x13cd0ac: ldur            x0, [fp, #-0x10]
    // 0x13cd0b0: StoreField: r3->field_13 = r0
    //     0x13cd0b0: stur            w0, [x3, #0x13]
    // 0x13cd0b4: r0 = false
    //     0x13cd0b4: add             x0, NULL, #0x30  ; false
    // 0x13cd0b8: ArrayStore: r3[0] = r0  ; List_4
    //     0x13cd0b8: stur            w0, [x3, #0x17]
    // 0x13cd0bc: StoreField: r3->field_1b = r0
    //     0x13cd0bc: stur            w0, [x3, #0x1b]
    // 0x13cd0c0: StoreField: r3->field_1f = r0
    //     0x13cd0c0: stur            w0, [x3, #0x1f]
    // 0x13cd0c4: StoreField: r3->field_23 = r0
    //     0x13cd0c4: stur            w0, [x3, #0x23]
    // 0x13cd0c8: StoreField: r3->field_27 = r0
    //     0x13cd0c8: stur            w0, [x3, #0x27]
    // 0x13cd0cc: StoreField: r3->field_2b = r0
    //     0x13cd0cc: stur            w0, [x3, #0x2b]
    // 0x13cd0d0: r1 = Null
    //     0x13cd0d0: mov             x1, NULL
    // 0x13cd0d4: r2 = 6
    //     0x13cd0d4: movz            x2, #0x6
    // 0x13cd0d8: r0 = AllocateArray()
    //     0x13cd0d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cd0dc: mov             x2, x0
    // 0x13cd0e0: ldur            x0, [fp, #-0x18]
    // 0x13cd0e4: stur            x2, [fp, #-8]
    // 0x13cd0e8: StoreField: r2->field_f = r0
    //     0x13cd0e8: stur            w0, [x2, #0xf]
    // 0x13cd0ec: ldur            x0, [fp, #-0x20]
    // 0x13cd0f0: StoreField: r2->field_13 = r0
    //     0x13cd0f0: stur            w0, [x2, #0x13]
    // 0x13cd0f4: ldur            x0, [fp, #-0x28]
    // 0x13cd0f8: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cd0f8: stur            w0, [x2, #0x17]
    // 0x13cd0fc: r1 = <Widget>
    //     0x13cd0fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cd100: r0 = AllocateGrowableArray()
    //     0x13cd100: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cd104: mov             x1, x0
    // 0x13cd108: ldur            x0, [fp, #-8]
    // 0x13cd10c: stur            x1, [fp, #-0x10]
    // 0x13cd110: StoreField: r1->field_f = r0
    //     0x13cd110: stur            w0, [x1, #0xf]
    // 0x13cd114: r0 = 6
    //     0x13cd114: movz            x0, #0x6
    // 0x13cd118: StoreField: r1->field_b = r0
    //     0x13cd118: stur            w0, [x1, #0xb]
    // 0x13cd11c: r0 = Column()
    //     0x13cd11c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13cd120: mov             x1, x0
    // 0x13cd124: r0 = Instance_Axis
    //     0x13cd124: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cd128: stur            x1, [fp, #-8]
    // 0x13cd12c: StoreField: r1->field_f = r0
    //     0x13cd12c: stur            w0, [x1, #0xf]
    // 0x13cd130: r2 = Instance_MainAxisAlignment
    //     0x13cd130: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cd134: ldr             x2, [x2, #0xa08]
    // 0x13cd138: StoreField: r1->field_13 = r2
    //     0x13cd138: stur            w2, [x1, #0x13]
    // 0x13cd13c: r2 = Instance_MainAxisSize
    //     0x13cd13c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13cd140: ldr             x2, [x2, #0xa10]
    // 0x13cd144: ArrayStore: r1[0] = r2  ; List_4
    //     0x13cd144: stur            w2, [x1, #0x17]
    // 0x13cd148: r2 = Instance_CrossAxisAlignment
    //     0x13cd148: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13cd14c: ldr             x2, [x2, #0x890]
    // 0x13cd150: StoreField: r1->field_1b = r2
    //     0x13cd150: stur            w2, [x1, #0x1b]
    // 0x13cd154: r2 = Instance_VerticalDirection
    //     0x13cd154: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cd158: ldr             x2, [x2, #0xa20]
    // 0x13cd15c: StoreField: r1->field_23 = r2
    //     0x13cd15c: stur            w2, [x1, #0x23]
    // 0x13cd160: r2 = Instance_Clip
    //     0x13cd160: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13cd164: ldr             x2, [x2, #0x38]
    // 0x13cd168: StoreField: r1->field_2b = r2
    //     0x13cd168: stur            w2, [x1, #0x2b]
    // 0x13cd16c: StoreField: r1->field_2f = rZR
    //     0x13cd16c: stur            xzr, [x1, #0x2f]
    // 0x13cd170: ldur            x2, [fp, #-0x10]
    // 0x13cd174: StoreField: r1->field_b = r2
    //     0x13cd174: stur            w2, [x1, #0xb]
    // 0x13cd178: r0 = Padding()
    //     0x13cd178: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cd17c: mov             x1, x0
    // 0x13cd180: r0 = Instance_EdgeInsets
    //     0x13cd180: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13cd184: ldr             x0, [x0, #0x1f0]
    // 0x13cd188: stur            x1, [fp, #-0x10]
    // 0x13cd18c: StoreField: r1->field_f = r0
    //     0x13cd18c: stur            w0, [x1, #0xf]
    // 0x13cd190: ldur            x0, [fp, #-8]
    // 0x13cd194: StoreField: r1->field_b = r0
    //     0x13cd194: stur            w0, [x1, #0xb]
    // 0x13cd198: r0 = SingleChildScrollView()
    //     0x13cd198: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x13cd19c: r1 = Instance_Axis
    //     0x13cd19c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cd1a0: StoreField: r0->field_b = r1
    //     0x13cd1a0: stur            w1, [x0, #0xb]
    // 0x13cd1a4: r1 = false
    //     0x13cd1a4: add             x1, NULL, #0x30  ; false
    // 0x13cd1a8: StoreField: r0->field_f = r1
    //     0x13cd1a8: stur            w1, [x0, #0xf]
    // 0x13cd1ac: ldur            x1, [fp, #-0x10]
    // 0x13cd1b0: StoreField: r0->field_23 = r1
    //     0x13cd1b0: stur            w1, [x0, #0x23]
    // 0x13cd1b4: r1 = Instance_DragStartBehavior
    //     0x13cd1b4: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x13cd1b8: StoreField: r0->field_27 = r1
    //     0x13cd1b8: stur            w1, [x0, #0x27]
    // 0x13cd1bc: r1 = Instance_Clip
    //     0x13cd1bc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x13cd1c0: ldr             x1, [x1, #0x7e0]
    // 0x13cd1c4: StoreField: r0->field_2b = r1
    //     0x13cd1c4: stur            w1, [x0, #0x2b]
    // 0x13cd1c8: r1 = Instance_HitTestBehavior
    //     0x13cd1c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x13cd1cc: ldr             x1, [x1, #0x288]
    // 0x13cd1d0: StoreField: r0->field_2f = r1
    //     0x13cd1d0: stur            w1, [x0, #0x2f]
    // 0x13cd1d4: LeaveFrame
    //     0x13cd1d4: mov             SP, fp
    //     0x13cd1d8: ldp             fp, lr, [SP], #0x10
    // 0x13cd1dc: ret
    //     0x13cd1dc: ret             
    // 0x13cd1e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cd1e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cd1e4: b               #0x13cbc48
    // 0x13cd1e8: SaveReg d0
    //     0x13cd1e8: str             q0, [SP, #-0x10]!
    // 0x13cd1ec: stp             x4, x5, [SP, #-0x10]!
    // 0x13cd1f0: stp             x2, x3, [SP, #-0x10]!
    // 0x13cd1f4: stp             x0, x1, [SP, #-0x10]!
    // 0x13cd1f8: r0 = AllocateDouble()
    //     0x13cd1f8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13cd1fc: mov             x6, x0
    // 0x13cd200: ldp             x0, x1, [SP], #0x10
    // 0x13cd204: ldp             x2, x3, [SP], #0x10
    // 0x13cd208: ldp             x4, x5, [SP], #0x10
    // 0x13cd20c: RestoreReg d0
    //     0x13cd20c: ldr             q0, [SP], #0x10
    // 0x13cd210: b               #0x13cc4d0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13cd214, size: 0x17c
    // 0x13cd214: EnterFrame
    //     0x13cd214: stp             fp, lr, [SP, #-0x10]!
    //     0x13cd218: mov             fp, SP
    // 0x13cd21c: AllocStack(0x28)
    //     0x13cd21c: sub             SP, SP, #0x28
    // 0x13cd220: SetupParameters()
    //     0x13cd220: ldr             x0, [fp, #0x10]
    //     0x13cd224: ldur            w2, [x0, #0x17]
    //     0x13cd228: add             x2, x2, HEAP, lsl #32
    //     0x13cd22c: stur            x2, [fp, #-8]
    // 0x13cd230: CheckStackOverflow
    //     0x13cd230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cd234: cmp             SP, x16
    //     0x13cd238: b.ls            #0x13cd388
    // 0x13cd23c: LoadField: r1 = r2->field_f
    //     0x13cd23c: ldur            w1, [x2, #0xf]
    // 0x13cd240: DecompressPointer r1
    //     0x13cd240: add             x1, x1, HEAP, lsl #32
    // 0x13cd244: r0 = controller()
    //     0x13cd244: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cd248: mov             x1, x0
    // 0x13cd24c: r2 = "EXCHANGE WITH NEW PRODUCT"
    //     0x13cd24c: add             x2, PP, #0x38, lsl #12  ; [pp+0x380e8] "EXCHANGE WITH NEW PRODUCT"
    //     0x13cd250: ldr             x2, [x2, #0xe8]
    // 0x13cd254: r3 = "return_exchange_with_different_product"
    //     0x13cd254: add             x3, PP, #0x38, lsl #12  ; [pp+0x380f8] "return_exchange_with_different_product"
    //     0x13cd258: ldr             x3, [x3, #0xf8]
    // 0x13cd25c: r0 = ctaPostEvent()
    //     0x13cd25c: bl              #0x1318484  ; [package:customer_app/app/presentation/controllers/exchange/exchange_same_product_controller.dart] ExchangeSameProductController::ctaPostEvent
    // 0x13cd260: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13cd260: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13cd264: ldr             x0, [x0, #0x1c80]
    //     0x13cd268: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13cd26c: cmp             w0, w16
    //     0x13cd270: b.ne            #0x13cd27c
    //     0x13cd274: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13cd278: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13cd27c: r1 = Null
    //     0x13cd27c: mov             x1, NULL
    // 0x13cd280: r2 = 12
    //     0x13cd280: movz            x2, #0xc
    // 0x13cd284: r0 = AllocateArray()
    //     0x13cd284: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cd288: stur            x0, [fp, #-0x10]
    // 0x13cd28c: r16 = "order_id"
    //     0x13cd28c: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x13cd290: ldr             x16, [x16, #0xa38]
    // 0x13cd294: StoreField: r0->field_f = r16
    //     0x13cd294: stur            w16, [x0, #0xf]
    // 0x13cd298: ldur            x2, [fp, #-8]
    // 0x13cd29c: LoadField: r1 = r2->field_f
    //     0x13cd29c: ldur            w1, [x2, #0xf]
    // 0x13cd2a0: DecompressPointer r1
    //     0x13cd2a0: add             x1, x1, HEAP, lsl #32
    // 0x13cd2a4: r0 = controller()
    //     0x13cd2a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cd2a8: LoadField: r1 = r0->field_6f
    //     0x13cd2a8: ldur            w1, [x0, #0x6f]
    // 0x13cd2ac: DecompressPointer r1
    //     0x13cd2ac: add             x1, x1, HEAP, lsl #32
    // 0x13cd2b0: r0 = value()
    //     0x13cd2b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cd2b4: ldur            x1, [fp, #-0x10]
    // 0x13cd2b8: ArrayStore: r1[1] = r0  ; List_4
    //     0x13cd2b8: add             x25, x1, #0x13
    //     0x13cd2bc: str             w0, [x25]
    //     0x13cd2c0: tbz             w0, #0, #0x13cd2dc
    //     0x13cd2c4: ldurb           w16, [x1, #-1]
    //     0x13cd2c8: ldurb           w17, [x0, #-1]
    //     0x13cd2cc: and             x16, x17, x16, lsr #2
    //     0x13cd2d0: tst             x16, HEAP, lsr #32
    //     0x13cd2d4: b.eq            #0x13cd2dc
    //     0x13cd2d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cd2dc: ldur            x0, [fp, #-0x10]
    // 0x13cd2e0: r16 = "charge"
    //     0x13cd2e0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b28] "charge"
    //     0x13cd2e4: ldr             x16, [x16, #0xb28]
    // 0x13cd2e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x13cd2e8: stur            w16, [x0, #0x17]
    // 0x13cd2ec: ldur            x1, [fp, #-8]
    // 0x13cd2f0: LoadField: r2 = r1->field_f
    //     0x13cd2f0: ldur            w2, [x1, #0xf]
    // 0x13cd2f4: DecompressPointer r2
    //     0x13cd2f4: add             x2, x2, HEAP, lsl #32
    // 0x13cd2f8: mov             x1, x2
    // 0x13cd2fc: r0 = controller()
    //     0x13cd2fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cd300: LoadField: r1 = r0->field_67
    //     0x13cd300: ldur            w1, [x0, #0x67]
    // 0x13cd304: DecompressPointer r1
    //     0x13cd304: add             x1, x1, HEAP, lsl #32
    // 0x13cd308: mov             x0, x1
    // 0x13cd30c: ldur            x1, [fp, #-0x10]
    // 0x13cd310: ArrayStore: r1[3] = r0  ; List_4
    //     0x13cd310: add             x25, x1, #0x1b
    //     0x13cd314: str             w0, [x25]
    //     0x13cd318: tbz             w0, #0, #0x13cd334
    //     0x13cd31c: ldurb           w16, [x1, #-1]
    //     0x13cd320: ldurb           w17, [x0, #-1]
    //     0x13cd324: and             x16, x17, x16, lsr #2
    //     0x13cd328: tst             x16, HEAP, lsr #32
    //     0x13cd32c: b.eq            #0x13cd334
    //     0x13cd330: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cd334: ldur            x0, [fp, #-0x10]
    // 0x13cd338: r16 = "type"
    //     0x13cd338: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x13cd33c: StoreField: r0->field_1f = r16
    //     0x13cd33c: stur            w16, [x0, #0x1f]
    // 0x13cd340: r16 = "replace-new"
    //     0x13cd340: add             x16, PP, #0x32, lsl #12  ; [pp+0x32ad8] "replace-new"
    //     0x13cd344: ldr             x16, [x16, #0xad8]
    // 0x13cd348: StoreField: r0->field_23 = r16
    //     0x13cd348: stur            w16, [x0, #0x23]
    // 0x13cd34c: r16 = <String, Object?>
    //     0x13cd34c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x13cd350: ldr             x16, [x16, #0xc28]
    // 0x13cd354: stp             x0, x16, [SP]
    // 0x13cd358: r0 = Map._fromLiteral()
    //     0x13cd358: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13cd35c: r16 = "/return-order"
    //     0x13cd35c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x13cd360: ldr             x16, [x16, #0x8b8]
    // 0x13cd364: stp             x16, NULL, [SP, #8]
    // 0x13cd368: str             x0, [SP]
    // 0x13cd36c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13cd36c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13cd370: ldr             x4, [x4, #0x438]
    // 0x13cd374: r0 = GetNavigation.offAndToNamed()
    //     0x13cd374: bl              #0x8f4af0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offAndToNamed
    // 0x13cd378: r0 = Null
    //     0x13cd378: mov             x0, NULL
    // 0x13cd37c: LeaveFrame
    //     0x13cd37c: mov             SP, fp
    //     0x13cd380: ldp             fp, lr, [SP], #0x10
    // 0x13cd384: ret
    //     0x13cd384: ret             
    // 0x13cd388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cd388: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cd38c: b               #0x13cd23c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13cd390, size: 0x78
    // 0x13cd390: EnterFrame
    //     0x13cd390: stp             fp, lr, [SP, #-0x10]!
    //     0x13cd394: mov             fp, SP
    // 0x13cd398: AllocStack(0x18)
    //     0x13cd398: sub             SP, SP, #0x18
    // 0x13cd39c: SetupParameters()
    //     0x13cd39c: ldr             x0, [fp, #0x10]
    //     0x13cd3a0: ldur            w2, [x0, #0x17]
    //     0x13cd3a4: add             x2, x2, HEAP, lsl #32
    //     0x13cd3a8: stur            x2, [fp, #-8]
    // 0x13cd3ac: CheckStackOverflow
    //     0x13cd3ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cd3b0: cmp             SP, x16
    //     0x13cd3b4: b.ls            #0x13cd400
    // 0x13cd3b8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13cd3b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13cd3bc: ldr             x0, [x0, #0x1c80]
    //     0x13cd3c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13cd3c4: cmp             w0, w16
    //     0x13cd3c8: b.ne            #0x13cd3d4
    //     0x13cd3cc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13cd3d0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13cd3d4: ldur            x2, [fp, #-8]
    // 0x13cd3d8: r1 = Function '<anonymous closure>':.
    //     0x13cd3d8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38100] AnonymousClosure: (0x13cd408), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13cd3dc: ldr             x1, [x1, #0x100]
    // 0x13cd3e0: r0 = AllocateClosure()
    //     0x13cd3e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cd3e4: stp             x0, NULL, [SP]
    // 0x13cd3e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cd3e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cd3ec: r0 = GetNavigation.to()
    //     0x13cd3ec: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0x13cd3f0: r0 = Null
    //     0x13cd3f0: mov             x0, NULL
    // 0x13cd3f4: LeaveFrame
    //     0x13cd3f4: mov             SP, fp
    //     0x13cd3f8: ldp             fp, lr, [SP], #0x10
    // 0x13cd3fc: ret
    //     0x13cd3fc: ret             
    // 0x13cd400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cd400: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cd404: b               #0x13cd3b8
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0x13cd408, size: 0xa0
    // 0x13cd408: EnterFrame
    //     0x13cd408: stp             fp, lr, [SP, #-0x10]!
    //     0x13cd40c: mov             fp, SP
    // 0x13cd410: AllocStack(0x8)
    //     0x13cd410: sub             SP, SP, #8
    // 0x13cd414: SetupParameters()
    //     0x13cd414: ldr             x0, [fp, #0x10]
    //     0x13cd418: ldur            w1, [x0, #0x17]
    //     0x13cd41c: add             x1, x1, HEAP, lsl #32
    // 0x13cd420: CheckStackOverflow
    //     0x13cd420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cd424: cmp             SP, x16
    //     0x13cd428: b.ls            #0x13cd4a0
    // 0x13cd42c: LoadField: r0 = r1->field_f
    //     0x13cd42c: ldur            w0, [x1, #0xf]
    // 0x13cd430: DecompressPointer r0
    //     0x13cd430: add             x0, x0, HEAP, lsl #32
    // 0x13cd434: mov             x1, x0
    // 0x13cd438: r0 = controller()
    //     0x13cd438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cd43c: LoadField: r1 = r0->field_4b
    //     0x13cd43c: ldur            w1, [x0, #0x4b]
    // 0x13cd440: DecompressPointer r1
    //     0x13cd440: add             x1, x1, HEAP, lsl #32
    // 0x13cd444: r0 = value()
    //     0x13cd444: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cd448: LoadField: r1 = r0->field_b
    //     0x13cd448: ldur            w1, [x0, #0xb]
    // 0x13cd44c: DecompressPointer r1
    //     0x13cd44c: add             x1, x1, HEAP, lsl #32
    // 0x13cd450: cmp             w1, NULL
    // 0x13cd454: b.ne            #0x13cd460
    // 0x13cd458: r0 = Null
    //     0x13cd458: mov             x0, NULL
    // 0x13cd45c: b               #0x13cd484
    // 0x13cd460: LoadField: r0 = r1->field_2f
    //     0x13cd460: ldur            w0, [x1, #0x2f]
    // 0x13cd464: DecompressPointer r0
    //     0x13cd464: add             x0, x0, HEAP, lsl #32
    // 0x13cd468: cmp             w0, NULL
    // 0x13cd46c: b.ne            #0x13cd478
    // 0x13cd470: r0 = Null
    //     0x13cd470: mov             x0, NULL
    // 0x13cd474: b               #0x13cd484
    // 0x13cd478: LoadField: r1 = r0->field_f
    //     0x13cd478: ldur            w1, [x0, #0xf]
    // 0x13cd47c: DecompressPointer r1
    //     0x13cd47c: add             x1, x1, HEAP, lsl #32
    // 0x13cd480: mov             x0, x1
    // 0x13cd484: stur            x0, [fp, #-8]
    // 0x13cd488: r0 = ViewSizeChart()
    //     0x13cd488: bl              #0xbbb748  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0x13cd48c: ldur            x1, [fp, #-8]
    // 0x13cd490: StoreField: r0->field_b = r1
    //     0x13cd490: stur            w1, [x0, #0xb]
    // 0x13cd494: LeaveFrame
    //     0x13cd494: mov             SP, fp
    //     0x13cd498: ldp             fp, lr, [SP], #0x10
    // 0x13cd49c: ret
    //     0x13cd49c: ret             
    // 0x13cd4a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cd4a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cd4a4: b               #0x13cd42c
  }
  _ body(/* No info */) {
    // ** addr: 0x1505a40, size: 0x64
    // 0x1505a40: EnterFrame
    //     0x1505a40: stp             fp, lr, [SP, #-0x10]!
    //     0x1505a44: mov             fp, SP
    // 0x1505a48: AllocStack(0x18)
    //     0x1505a48: sub             SP, SP, #0x18
    // 0x1505a4c: SetupParameters(ExchangeProductSkusScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1505a4c: stur            x1, [fp, #-8]
    //     0x1505a50: stur            x2, [fp, #-0x10]
    // 0x1505a54: r1 = 2
    //     0x1505a54: movz            x1, #0x2
    // 0x1505a58: r0 = AllocateContext()
    //     0x1505a58: bl              #0x16f6108  ; AllocateContextStub
    // 0x1505a5c: mov             x1, x0
    // 0x1505a60: ldur            x0, [fp, #-8]
    // 0x1505a64: stur            x1, [fp, #-0x18]
    // 0x1505a68: StoreField: r1->field_f = r0
    //     0x1505a68: stur            w0, [x1, #0xf]
    // 0x1505a6c: ldur            x0, [fp, #-0x10]
    // 0x1505a70: StoreField: r1->field_13 = r0
    //     0x1505a70: stur            w0, [x1, #0x13]
    // 0x1505a74: r0 = Obx()
    //     0x1505a74: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1505a78: ldur            x2, [fp, #-0x18]
    // 0x1505a7c: r1 = Function '<anonymous closure>':.
    //     0x1505a7c: add             x1, PP, #0x38, lsl #12  ; [pp+0x38090] AnonymousClosure: (0x13cbc20), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x1505a80: ldr             x1, [x1, #0x90]
    // 0x1505a84: stur            x0, [fp, #-8]
    // 0x1505a88: r0 = AllocateClosure()
    //     0x1505a88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505a8c: mov             x1, x0
    // 0x1505a90: ldur            x0, [fp, #-8]
    // 0x1505a94: StoreField: r0->field_b = r1
    //     0x1505a94: stur            w1, [x0, #0xb]
    // 0x1505a98: LeaveFrame
    //     0x1505a98: mov             SP, fp
    //     0x1505a9c: ldp             fp, lr, [SP], #0x10
    // 0x1505aa0: ret
    //     0x1505aa0: ret             
  }
  [closure] Text <anonymous closure>(dynamic) {
    // ** addr: 0x15d03f0, size: 0xe4
    // 0x15d03f0: EnterFrame
    //     0x15d03f0: stp             fp, lr, [SP, #-0x10]!
    //     0x15d03f4: mov             fp, SP
    // 0x15d03f8: AllocStack(0x20)
    //     0x15d03f8: sub             SP, SP, #0x20
    // 0x15d03fc: SetupParameters()
    //     0x15d03fc: ldr             x0, [fp, #0x10]
    //     0x15d0400: ldur            w2, [x0, #0x17]
    //     0x15d0404: add             x2, x2, HEAP, lsl #32
    //     0x15d0408: stur            x2, [fp, #-8]
    // 0x15d040c: CheckStackOverflow
    //     0x15d040c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d0410: cmp             SP, x16
    //     0x15d0414: b.ls            #0x15d04cc
    // 0x15d0418: LoadField: r1 = r2->field_f
    //     0x15d0418: ldur            w1, [x2, #0xf]
    // 0x15d041c: DecompressPointer r1
    //     0x15d041c: add             x1, x1, HEAP, lsl #32
    // 0x15d0420: r0 = controller()
    //     0x15d0420: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d0424: LoadField: r1 = r0->field_4b
    //     0x15d0424: ldur            w1, [x0, #0x4b]
    // 0x15d0428: DecompressPointer r1
    //     0x15d0428: add             x1, x1, HEAP, lsl #32
    // 0x15d042c: r0 = value()
    //     0x15d042c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d0430: LoadField: r1 = r0->field_b
    //     0x15d0430: ldur            w1, [x0, #0xb]
    // 0x15d0434: DecompressPointer r1
    //     0x15d0434: add             x1, x1, HEAP, lsl #32
    // 0x15d0438: cmp             w1, NULL
    // 0x15d043c: b.ne            #0x15d0448
    // 0x15d0440: r0 = Null
    //     0x15d0440: mov             x0, NULL
    // 0x15d0444: b               #0x15d0450
    // 0x15d0448: LoadField: r0 = r1->field_37
    //     0x15d0448: ldur            w0, [x1, #0x37]
    // 0x15d044c: DecompressPointer r0
    //     0x15d044c: add             x0, x0, HEAP, lsl #32
    // 0x15d0450: cmp             w0, NULL
    // 0x15d0454: b.ne            #0x15d0460
    // 0x15d0458: r2 = ""
    //     0x15d0458: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15d045c: b               #0x15d0464
    // 0x15d0460: mov             x2, x0
    // 0x15d0464: ldur            x0, [fp, #-8]
    // 0x15d0468: stur            x2, [fp, #-0x10]
    // 0x15d046c: LoadField: r1 = r0->field_13
    //     0x15d046c: ldur            w1, [x0, #0x13]
    // 0x15d0470: DecompressPointer r1
    //     0x15d0470: add             x1, x1, HEAP, lsl #32
    // 0x15d0474: r0 = of()
    //     0x15d0474: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d0478: LoadField: r1 = r0->field_87
    //     0x15d0478: ldur            w1, [x0, #0x87]
    // 0x15d047c: DecompressPointer r1
    //     0x15d047c: add             x1, x1, HEAP, lsl #32
    // 0x15d0480: LoadField: r0 = r1->field_7
    //     0x15d0480: ldur            w0, [x1, #7]
    // 0x15d0484: DecompressPointer r0
    //     0x15d0484: add             x0, x0, HEAP, lsl #32
    // 0x15d0488: r16 = Instance_Color
    //     0x15d0488: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15d048c: r30 = 16.000000
    //     0x15d048c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15d0490: ldr             lr, [lr, #0x188]
    // 0x15d0494: stp             lr, x16, [SP]
    // 0x15d0498: mov             x1, x0
    // 0x15d049c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15d049c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15d04a0: ldr             x4, [x4, #0x9b8]
    // 0x15d04a4: r0 = copyWith()
    //     0x15d04a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d04a8: stur            x0, [fp, #-8]
    // 0x15d04ac: r0 = Text()
    //     0x15d04ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d04b0: ldur            x1, [fp, #-0x10]
    // 0x15d04b4: StoreField: r0->field_b = r1
    //     0x15d04b4: stur            w1, [x0, #0xb]
    // 0x15d04b8: ldur            x1, [fp, #-8]
    // 0x15d04bc: StoreField: r0->field_13 = r1
    //     0x15d04bc: stur            w1, [x0, #0x13]
    // 0x15d04c0: LeaveFrame
    //     0x15d04c0: mov             SP, fp
    //     0x15d04c4: ldp             fp, lr, [SP], #0x10
    // 0x15d04c8: ret
    //     0x15d04c8: ret             
    // 0x15d04cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d04cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d04d0: b               #0x15d0418
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e9f10, size: 0x18c
    // 0x15e9f10: EnterFrame
    //     0x15e9f10: stp             fp, lr, [SP, #-0x10]!
    //     0x15e9f14: mov             fp, SP
    // 0x15e9f18: AllocStack(0x28)
    //     0x15e9f18: sub             SP, SP, #0x28
    // 0x15e9f1c: SetupParameters(ExchangeProductSkusScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e9f1c: mov             x0, x1
    //     0x15e9f20: stur            x1, [fp, #-8]
    //     0x15e9f24: mov             x1, x2
    //     0x15e9f28: stur            x2, [fp, #-0x10]
    // 0x15e9f2c: CheckStackOverflow
    //     0x15e9f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e9f30: cmp             SP, x16
    //     0x15e9f34: b.ls            #0x15ea094
    // 0x15e9f38: r1 = 2
    //     0x15e9f38: movz            x1, #0x2
    // 0x15e9f3c: r0 = AllocateContext()
    //     0x15e9f3c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e9f40: mov             x1, x0
    // 0x15e9f44: ldur            x0, [fp, #-8]
    // 0x15e9f48: stur            x1, [fp, #-0x18]
    // 0x15e9f4c: StoreField: r1->field_f = r0
    //     0x15e9f4c: stur            w0, [x1, #0xf]
    // 0x15e9f50: ldur            x0, [fp, #-0x10]
    // 0x15e9f54: StoreField: r1->field_13 = r0
    //     0x15e9f54: stur            w0, [x1, #0x13]
    // 0x15e9f58: r0 = Obx()
    //     0x15e9f58: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e9f5c: ldur            x2, [fp, #-0x18]
    // 0x15e9f60: r1 = Function '<anonymous closure>':.
    //     0x15e9f60: add             x1, PP, #0x38, lsl #12  ; [pp+0x38158] AnonymousClosure: (0x15d03f0), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::appBar (0x15e9f10)
    //     0x15e9f64: ldr             x1, [x1, #0x158]
    // 0x15e9f68: stur            x0, [fp, #-8]
    // 0x15e9f6c: r0 = AllocateClosure()
    //     0x15e9f6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e9f70: mov             x1, x0
    // 0x15e9f74: ldur            x0, [fp, #-8]
    // 0x15e9f78: StoreField: r0->field_b = r1
    //     0x15e9f78: stur            w1, [x0, #0xb]
    // 0x15e9f7c: ldur            x1, [fp, #-0x10]
    // 0x15e9f80: r0 = of()
    //     0x15e9f80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e9f84: LoadField: r1 = r0->field_5b
    //     0x15e9f84: ldur            w1, [x0, #0x5b]
    // 0x15e9f88: DecompressPointer r1
    //     0x15e9f88: add             x1, x1, HEAP, lsl #32
    // 0x15e9f8c: stur            x1, [fp, #-0x10]
    // 0x15e9f90: r0 = ColorFilter()
    //     0x15e9f90: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e9f94: mov             x1, x0
    // 0x15e9f98: ldur            x0, [fp, #-0x10]
    // 0x15e9f9c: stur            x1, [fp, #-0x20]
    // 0x15e9fa0: StoreField: r1->field_7 = r0
    //     0x15e9fa0: stur            w0, [x1, #7]
    // 0x15e9fa4: r0 = Instance_BlendMode
    //     0x15e9fa4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9fa8: ldr             x0, [x0, #0xb30]
    // 0x15e9fac: StoreField: r1->field_b = r0
    //     0x15e9fac: stur            w0, [x1, #0xb]
    // 0x15e9fb0: r0 = 1
    //     0x15e9fb0: movz            x0, #0x1
    // 0x15e9fb4: StoreField: r1->field_13 = r0
    //     0x15e9fb4: stur            x0, [x1, #0x13]
    // 0x15e9fb8: r0 = SvgPicture()
    //     0x15e9fb8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e9fbc: stur            x0, [fp, #-0x10]
    // 0x15e9fc0: ldur            x16, [fp, #-0x20]
    // 0x15e9fc4: str             x16, [SP]
    // 0x15e9fc8: mov             x1, x0
    // 0x15e9fcc: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e9fcc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e9fd0: ldr             x2, [x2, #0xa40]
    // 0x15e9fd4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e9fd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e9fd8: ldr             x4, [x4, #0xa38]
    // 0x15e9fdc: r0 = SvgPicture.asset()
    //     0x15e9fdc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e9fe0: r0 = Align()
    //     0x15e9fe0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e9fe4: mov             x1, x0
    // 0x15e9fe8: r0 = Instance_Alignment
    //     0x15e9fe8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e9fec: ldr             x0, [x0, #0xb10]
    // 0x15e9ff0: stur            x1, [fp, #-0x20]
    // 0x15e9ff4: StoreField: r1->field_f = r0
    //     0x15e9ff4: stur            w0, [x1, #0xf]
    // 0x15e9ff8: r0 = 1.000000
    //     0x15e9ff8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9ffc: StoreField: r1->field_13 = r0
    //     0x15e9ffc: stur            w0, [x1, #0x13]
    // 0x15ea000: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ea000: stur            w0, [x1, #0x17]
    // 0x15ea004: ldur            x0, [fp, #-0x10]
    // 0x15ea008: StoreField: r1->field_b = r0
    //     0x15ea008: stur            w0, [x1, #0xb]
    // 0x15ea00c: r0 = InkWell()
    //     0x15ea00c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ea010: mov             x3, x0
    // 0x15ea014: ldur            x0, [fp, #-0x20]
    // 0x15ea018: stur            x3, [fp, #-0x10]
    // 0x15ea01c: StoreField: r3->field_b = r0
    //     0x15ea01c: stur            w0, [x3, #0xb]
    // 0x15ea020: ldur            x2, [fp, #-0x18]
    // 0x15ea024: r1 = Function '<anonymous closure>':.
    //     0x15ea024: add             x1, PP, #0x38, lsl #12  ; [pp+0x38160] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15ea028: ldr             x1, [x1, #0x160]
    // 0x15ea02c: r0 = AllocateClosure()
    //     0x15ea02c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ea030: ldur            x2, [fp, #-0x10]
    // 0x15ea034: StoreField: r2->field_f = r0
    //     0x15ea034: stur            w0, [x2, #0xf]
    // 0x15ea038: r0 = true
    //     0x15ea038: add             x0, NULL, #0x20  ; true
    // 0x15ea03c: StoreField: r2->field_43 = r0
    //     0x15ea03c: stur            w0, [x2, #0x43]
    // 0x15ea040: r1 = Instance_BoxShape
    //     0x15ea040: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ea044: ldr             x1, [x1, #0x80]
    // 0x15ea048: StoreField: r2->field_47 = r1
    //     0x15ea048: stur            w1, [x2, #0x47]
    // 0x15ea04c: StoreField: r2->field_6f = r0
    //     0x15ea04c: stur            w0, [x2, #0x6f]
    // 0x15ea050: r1 = false
    //     0x15ea050: add             x1, NULL, #0x30  ; false
    // 0x15ea054: StoreField: r2->field_73 = r1
    //     0x15ea054: stur            w1, [x2, #0x73]
    // 0x15ea058: StoreField: r2->field_83 = r0
    //     0x15ea058: stur            w0, [x2, #0x83]
    // 0x15ea05c: StoreField: r2->field_7b = r1
    //     0x15ea05c: stur            w1, [x2, #0x7b]
    // 0x15ea060: r0 = AppBar()
    //     0x15ea060: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ea064: stur            x0, [fp, #-0x18]
    // 0x15ea068: ldur            x16, [fp, #-8]
    // 0x15ea06c: str             x16, [SP]
    // 0x15ea070: mov             x1, x0
    // 0x15ea074: ldur            x2, [fp, #-0x10]
    // 0x15ea078: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15ea078: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15ea07c: ldr             x4, [x4, #0xf00]
    // 0x15ea080: r0 = AppBar()
    //     0x15ea080: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ea084: ldur            x0, [fp, #-0x18]
    // 0x15ea088: LeaveFrame
    //     0x15ea088: mov             SP, fp
    //     0x15ea08c: ldp             fp, lr, [SP], #0x10
    // 0x15ea090: ret
    //     0x15ea090: ret             
    // 0x15ea094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ea094: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ea098: b               #0x15e9f38
  }
}
