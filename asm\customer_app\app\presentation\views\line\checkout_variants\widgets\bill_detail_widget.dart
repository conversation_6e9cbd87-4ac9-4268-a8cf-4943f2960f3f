// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/bill_detail_widget.dart

// class id: 1049482, size: 0x8
class :: {
}

// class id: 3283, size: 0x14, field offset: 0x14
class _BillDetailWidgetState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x902f44, size: 0x6c
    // 0x902f44: EnterFrame
    //     0x902f44: stp             fp, lr, [SP, #-0x10]!
    //     0x902f48: mov             fp, SP
    // 0x902f4c: AllocStack(0x8)
    //     0x902f4c: sub             SP, SP, #8
    // 0x902f50: SetupParameters()
    //     0x902f50: ldr             x0, [fp, #0x18]
    //     0x902f54: ldur            w1, [x0, #0x17]
    //     0x902f58: add             x1, x1, HEAP, lsl #32
    // 0x902f5c: CheckStackOverflow
    //     0x902f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x902f60: cmp             SP, x16
    //     0x902f64: b.ls            #0x902fa4
    // 0x902f68: LoadField: r0 = r1->field_f
    //     0x902f68: ldur            w0, [x1, #0xf]
    // 0x902f6c: DecompressPointer r0
    //     0x902f6c: add             x0, x0, HEAP, lsl #32
    // 0x902f70: LoadField: r1 = r0->field_b
    //     0x902f70: ldur            w1, [x0, #0xb]
    // 0x902f74: DecompressPointer r1
    //     0x902f74: add             x1, x1, HEAP, lsl #32
    // 0x902f78: cmp             w1, NULL
    // 0x902f7c: b.eq            #0x902fac
    // 0x902f80: LoadField: r0 = r1->field_f
    //     0x902f80: ldur            w0, [x1, #0xf]
    // 0x902f84: DecompressPointer r0
    //     0x902f84: add             x0, x0, HEAP, lsl #32
    // 0x902f88: str             x0, [SP]
    // 0x902f8c: ClosureCall
    //     0x902f8c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x902f90: ldur            x2, [x0, #0x1f]
    //     0x902f94: blr             x2
    // 0x902f98: LeaveFrame
    //     0x902f98: mov             SP, fp
    //     0x902f9c: ldp             fp, lr, [SP], #0x10
    // 0x902fa0: ret
    //     0x902fa0: ret             
    // 0x902fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x902fa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x902fa8: b               #0x902f68
    // 0x902fac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x902fac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x945a78, size: 0x130
    // 0x945a78: EnterFrame
    //     0x945a78: stp             fp, lr, [SP, #-0x10]!
    //     0x945a7c: mov             fp, SP
    // 0x945a80: AllocStack(0x18)
    //     0x945a80: sub             SP, SP, #0x18
    // 0x945a84: SetupParameters(_BillDetailWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x945a84: stur            x1, [fp, #-8]
    // 0x945a88: CheckStackOverflow
    //     0x945a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x945a8c: cmp             SP, x16
    //     0x945a90: b.ls            #0x945b9c
    // 0x945a94: r1 = 1
    //     0x945a94: movz            x1, #0x1
    // 0x945a98: r0 = AllocateContext()
    //     0x945a98: bl              #0x16f6108  ; AllocateContextStub
    // 0x945a9c: mov             x1, x0
    // 0x945aa0: ldur            x0, [fp, #-8]
    // 0x945aa4: StoreField: r1->field_f = r0
    //     0x945aa4: stur            w0, [x1, #0xf]
    // 0x945aa8: r0 = LoadStaticField(0x878)
    //     0x945aa8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x945aac: ldr             x0, [x0, #0x10f0]
    // 0x945ab0: cmp             w0, NULL
    // 0x945ab4: b.eq            #0x945ba4
    // 0x945ab8: LoadField: r3 = r0->field_53
    //     0x945ab8: ldur            w3, [x0, #0x53]
    // 0x945abc: DecompressPointer r3
    //     0x945abc: add             x3, x3, HEAP, lsl #32
    // 0x945ac0: stur            x3, [fp, #-0x10]
    // 0x945ac4: LoadField: r0 = r3->field_7
    //     0x945ac4: ldur            w0, [x3, #7]
    // 0x945ac8: DecompressPointer r0
    //     0x945ac8: add             x0, x0, HEAP, lsl #32
    // 0x945acc: mov             x2, x1
    // 0x945ad0: stur            x0, [fp, #-8]
    // 0x945ad4: r1 = Function '<anonymous closure>':.
    //     0x945ad4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a28] AnonymousClosure: (0x902f44), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::initState (0x945a78)
    //     0x945ad8: ldr             x1, [x1, #0xa28]
    // 0x945adc: r0 = AllocateClosure()
    //     0x945adc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x945ae0: ldur            x2, [fp, #-8]
    // 0x945ae4: mov             x3, x0
    // 0x945ae8: r1 = Null
    //     0x945ae8: mov             x1, NULL
    // 0x945aec: stur            x3, [fp, #-8]
    // 0x945af0: cmp             w2, NULL
    // 0x945af4: b.eq            #0x945b14
    // 0x945af8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x945af8: ldur            w4, [x2, #0x17]
    // 0x945afc: DecompressPointer r4
    //     0x945afc: add             x4, x4, HEAP, lsl #32
    // 0x945b00: r8 = X0
    //     0x945b00: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x945b04: LoadField: r9 = r4->field_7
    //     0x945b04: ldur            x9, [x4, #7]
    // 0x945b08: r3 = Null
    //     0x945b08: add             x3, PP, #0x54, lsl #12  ; [pp+0x54a30] Null
    //     0x945b0c: ldr             x3, [x3, #0xa30]
    // 0x945b10: blr             x9
    // 0x945b14: ldur            x0, [fp, #-0x10]
    // 0x945b18: LoadField: r1 = r0->field_b
    //     0x945b18: ldur            w1, [x0, #0xb]
    // 0x945b1c: LoadField: r2 = r0->field_f
    //     0x945b1c: ldur            w2, [x0, #0xf]
    // 0x945b20: DecompressPointer r2
    //     0x945b20: add             x2, x2, HEAP, lsl #32
    // 0x945b24: LoadField: r3 = r2->field_b
    //     0x945b24: ldur            w3, [x2, #0xb]
    // 0x945b28: r2 = LoadInt32Instr(r1)
    //     0x945b28: sbfx            x2, x1, #1, #0x1f
    // 0x945b2c: stur            x2, [fp, #-0x18]
    // 0x945b30: r1 = LoadInt32Instr(r3)
    //     0x945b30: sbfx            x1, x3, #1, #0x1f
    // 0x945b34: cmp             x2, x1
    // 0x945b38: b.ne            #0x945b44
    // 0x945b3c: mov             x1, x0
    // 0x945b40: r0 = _growToNextCapacity()
    //     0x945b40: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945b44: ldur            x2, [fp, #-0x10]
    // 0x945b48: ldur            x3, [fp, #-0x18]
    // 0x945b4c: add             x4, x3, #1
    // 0x945b50: lsl             x5, x4, #1
    // 0x945b54: StoreField: r2->field_b = r5
    //     0x945b54: stur            w5, [x2, #0xb]
    // 0x945b58: LoadField: r1 = r2->field_f
    //     0x945b58: ldur            w1, [x2, #0xf]
    // 0x945b5c: DecompressPointer r1
    //     0x945b5c: add             x1, x1, HEAP, lsl #32
    // 0x945b60: ldur            x0, [fp, #-8]
    // 0x945b64: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945b64: add             x25, x1, x3, lsl #2
    //     0x945b68: add             x25, x25, #0xf
    //     0x945b6c: str             w0, [x25]
    //     0x945b70: tbz             w0, #0, #0x945b8c
    //     0x945b74: ldurb           w16, [x1, #-1]
    //     0x945b78: ldurb           w17, [x0, #-1]
    //     0x945b7c: and             x16, x17, x16, lsr #2
    //     0x945b80: tst             x16, HEAP, lsr #32
    //     0x945b84: b.eq            #0x945b8c
    //     0x945b88: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945b8c: r0 = Null
    //     0x945b8c: mov             x0, NULL
    // 0x945b90: LeaveFrame
    //     0x945b90: mov             SP, fp
    //     0x945b94: ldp             fp, lr, [SP], #0x10
    // 0x945b98: ret
    //     0x945b98: ret             
    // 0x945b9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945b9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945ba0: b               #0x945a94
    // 0x945ba4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x945ba4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbace20, size: 0x338
    // 0xbace20: EnterFrame
    //     0xbace20: stp             fp, lr, [SP, #-0x10]!
    //     0xbace24: mov             fp, SP
    // 0xbace28: AllocStack(0x40)
    //     0xbace28: sub             SP, SP, #0x40
    // 0xbace2c: SetupParameters(_BillDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbace2c: mov             x0, x1
    //     0xbace30: stur            x1, [fp, #-8]
    //     0xbace34: mov             x1, x2
    //     0xbace38: stur            x2, [fp, #-0x10]
    // 0xbace3c: CheckStackOverflow
    //     0xbace3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbace40: cmp             SP, x16
    //     0xbace44: b.ls            #0xbad144
    // 0xbace48: r1 = 1
    //     0xbace48: movz            x1, #0x1
    // 0xbace4c: r0 = AllocateContext()
    //     0xbace4c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbace50: mov             x2, x0
    // 0xbace54: ldur            x0, [fp, #-8]
    // 0xbace58: stur            x2, [fp, #-0x20]
    // 0xbace5c: StoreField: r2->field_f = r0
    //     0xbace5c: stur            w0, [x2, #0xf]
    // 0xbace60: LoadField: r1 = r0->field_b
    //     0xbace60: ldur            w1, [x0, #0xb]
    // 0xbace64: DecompressPointer r1
    //     0xbace64: add             x1, x1, HEAP, lsl #32
    // 0xbace68: cmp             w1, NULL
    // 0xbace6c: b.eq            #0xbad14c
    // 0xbace70: LoadField: r3 = r1->field_b
    //     0xbace70: ldur            w3, [x1, #0xb]
    // 0xbace74: DecompressPointer r3
    //     0xbace74: add             x3, x3, HEAP, lsl #32
    // 0xbace78: LoadField: r1 = r3->field_b
    //     0xbace78: ldur            w1, [x3, #0xb]
    // 0xbace7c: DecompressPointer r1
    //     0xbace7c: add             x1, x1, HEAP, lsl #32
    // 0xbace80: cmp             w1, NULL
    // 0xbace84: b.ne            #0xbace90
    // 0xbace88: r1 = Null
    //     0xbace88: mov             x1, NULL
    // 0xbace8c: b               #0xbaceb0
    // 0xbace90: LoadField: r3 = r1->field_1b
    //     0xbace90: ldur            w3, [x1, #0x1b]
    // 0xbace94: DecompressPointer r3
    //     0xbace94: add             x3, x3, HEAP, lsl #32
    // 0xbace98: cmp             w3, NULL
    // 0xbace9c: b.ne            #0xbacea8
    // 0xbacea0: r1 = Null
    //     0xbacea0: mov             x1, NULL
    // 0xbacea4: b               #0xbaceb0
    // 0xbacea8: LoadField: r1 = r3->field_7
    //     0xbacea8: ldur            w1, [x3, #7]
    // 0xbaceac: DecompressPointer r1
    //     0xbaceac: add             x1, x1, HEAP, lsl #32
    // 0xbaceb0: cmp             w1, NULL
    // 0xbaceb4: b.ne            #0xbacec0
    // 0xbaceb8: r3 = ""
    //     0xbaceb8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbacebc: b               #0xbacec4
    // 0xbacec0: mov             x3, x1
    // 0xbacec4: ldur            x1, [fp, #-0x10]
    // 0xbacec8: stur            x3, [fp, #-0x18]
    // 0xbacecc: r0 = of()
    //     0xbacecc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbaced0: LoadField: r1 = r0->field_87
    //     0xbaced0: ldur            w1, [x0, #0x87]
    // 0xbaced4: DecompressPointer r1
    //     0xbaced4: add             x1, x1, HEAP, lsl #32
    // 0xbaced8: LoadField: r0 = r1->field_7
    //     0xbaced8: ldur            w0, [x1, #7]
    // 0xbacedc: DecompressPointer r0
    //     0xbacedc: add             x0, x0, HEAP, lsl #32
    // 0xbacee0: stur            x0, [fp, #-0x10]
    // 0xbacee4: r1 = Instance_Color
    //     0xbacee4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbacee8: d0 = 0.700000
    //     0xbacee8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbaceec: ldr             d0, [x17, #0xf48]
    // 0xbacef0: r0 = withOpacity()
    //     0xbacef0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbacef4: r16 = 14.000000
    //     0xbacef4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbacef8: ldr             x16, [x16, #0x1d8]
    // 0xbacefc: stp             x0, x16, [SP]
    // 0xbacf00: ldur            x1, [fp, #-0x10]
    // 0xbacf04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbacf04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbacf08: ldr             x4, [x4, #0xaa0]
    // 0xbacf0c: r0 = copyWith()
    //     0xbacf0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbacf10: stur            x0, [fp, #-0x10]
    // 0xbacf14: r0 = Text()
    //     0xbacf14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbacf18: mov             x3, x0
    // 0xbacf1c: ldur            x0, [fp, #-0x18]
    // 0xbacf20: stur            x3, [fp, #-0x28]
    // 0xbacf24: StoreField: r3->field_b = r0
    //     0xbacf24: stur            w0, [x3, #0xb]
    // 0xbacf28: ldur            x0, [fp, #-0x10]
    // 0xbacf2c: StoreField: r3->field_13 = r0
    //     0xbacf2c: stur            w0, [x3, #0x13]
    // 0xbacf30: ldur            x0, [fp, #-8]
    // 0xbacf34: LoadField: r1 = r0->field_b
    //     0xbacf34: ldur            w1, [x0, #0xb]
    // 0xbacf38: DecompressPointer r1
    //     0xbacf38: add             x1, x1, HEAP, lsl #32
    // 0xbacf3c: cmp             w1, NULL
    // 0xbacf40: b.eq            #0xbad150
    // 0xbacf44: LoadField: r2 = r1->field_b
    //     0xbacf44: ldur            w2, [x1, #0xb]
    // 0xbacf48: DecompressPointer r2
    //     0xbacf48: add             x2, x2, HEAP, lsl #32
    // 0xbacf4c: LoadField: r1 = r2->field_b
    //     0xbacf4c: ldur            w1, [x2, #0xb]
    // 0xbacf50: DecompressPointer r1
    //     0xbacf50: add             x1, x1, HEAP, lsl #32
    // 0xbacf54: cmp             w1, NULL
    // 0xbacf58: b.ne            #0xbacf64
    // 0xbacf5c: r1 = Null
    //     0xbacf5c: mov             x1, NULL
    // 0xbacf60: b               #0xbacf84
    // 0xbacf64: LoadField: r2 = r1->field_1b
    //     0xbacf64: ldur            w2, [x1, #0x1b]
    // 0xbacf68: DecompressPointer r2
    //     0xbacf68: add             x2, x2, HEAP, lsl #32
    // 0xbacf6c: cmp             w2, NULL
    // 0xbacf70: b.ne            #0xbacf7c
    // 0xbacf74: r1 = Null
    //     0xbacf74: mov             x1, NULL
    // 0xbacf78: b               #0xbacf84
    // 0xbacf7c: LoadField: r1 = r2->field_f
    //     0xbacf7c: ldur            w1, [x2, #0xf]
    // 0xbacf80: DecompressPointer r1
    //     0xbacf80: add             x1, x1, HEAP, lsl #32
    // 0xbacf84: cmp             w1, NULL
    // 0xbacf88: b.ne            #0xbacfa0
    // 0xbacf8c: r1 = <Entities>
    //     0xbacf8c: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0xbacf90: ldr             x1, [x1, #0xea8]
    // 0xbacf94: r2 = 0
    //     0xbacf94: movz            x2, #0
    // 0xbacf98: r0 = AllocateArray()
    //     0xbacf98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbacf9c: b               #0xbacfa4
    // 0xbacfa0: mov             x0, x1
    // 0xbacfa4: ldur            x2, [fp, #-8]
    // 0xbacfa8: ldur            x1, [fp, #-0x28]
    // 0xbacfac: r3 = LoadClassIdInstr(r0)
    //     0xbacfac: ldur            x3, [x0, #-1]
    //     0xbacfb0: ubfx            x3, x3, #0xc, #0x14
    // 0xbacfb4: str             x0, [SP]
    // 0xbacfb8: mov             x0, x3
    // 0xbacfbc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbacfbc: movz            x17, #0xc898
    //     0xbacfc0: add             lr, x0, x17
    //     0xbacfc4: ldr             lr, [x21, lr, lsl #3]
    //     0xbacfc8: blr             lr
    // 0xbacfcc: r3 = LoadInt32Instr(r0)
    //     0xbacfcc: sbfx            x3, x0, #1, #0x1f
    // 0xbacfd0: ldur            x2, [fp, #-0x20]
    // 0xbacfd4: stur            x3, [fp, #-0x30]
    // 0xbacfd8: r1 = Function '<anonymous closure>':.
    //     0xbacfd8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a10] AnonymousClosure: (0xbad2bc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::build (0xbace20)
    //     0xbacfdc: ldr             x1, [x1, #0xa10]
    // 0xbacfe0: r0 = AllocateClosure()
    //     0xbacfe0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbacfe4: ldur            x2, [fp, #-0x20]
    // 0xbacfe8: r1 = Function '<anonymous closure>':.
    //     0xbacfe8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a18] AnonymousClosure: (0xbad164), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::build (0xbace20)
    //     0xbacfec: ldr             x1, [x1, #0xa18]
    // 0xbacff0: stur            x0, [fp, #-0x10]
    // 0xbacff4: r0 = AllocateClosure()
    //     0xbacff4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbacff8: stur            x0, [fp, #-0x18]
    // 0xbacffc: r0 = ListView()
    //     0xbacffc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbad000: stur            x0, [fp, #-0x20]
    // 0xbad004: r16 = true
    //     0xbad004: add             x16, NULL, #0x20  ; true
    // 0xbad008: r30 = false
    //     0xbad008: add             lr, NULL, #0x30  ; false
    // 0xbad00c: stp             lr, x16, [SP]
    // 0xbad010: mov             x1, x0
    // 0xbad014: ldur            x2, [fp, #-0x10]
    // 0xbad018: ldur            x3, [fp, #-0x30]
    // 0xbad01c: ldur            x5, [fp, #-0x18]
    // 0xbad020: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xbad020: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbad024: ldr             x4, [x4, #0xc98]
    // 0xbad028: r0 = ListView.separated()
    //     0xbad028: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbad02c: ldur            x0, [fp, #-8]
    // 0xbad030: LoadField: r1 = r0->field_b
    //     0xbad030: ldur            w1, [x0, #0xb]
    // 0xbad034: DecompressPointer r1
    //     0xbad034: add             x1, x1, HEAP, lsl #32
    // 0xbad038: cmp             w1, NULL
    // 0xbad03c: b.eq            #0xbad154
    // 0xbad040: LoadField: r0 = r1->field_b
    //     0xbad040: ldur            w0, [x1, #0xb]
    // 0xbad044: DecompressPointer r0
    //     0xbad044: add             x0, x0, HEAP, lsl #32
    // 0xbad048: stur            x0, [fp, #-8]
    // 0xbad04c: r0 = DeliveryInfoWidget()
    //     0xbad04c: bl              #0xbad158  ; AllocateDeliveryInfoWidgetStub -> DeliveryInfoWidget (size=0x10)
    // 0xbad050: mov             x3, x0
    // 0xbad054: ldur            x0, [fp, #-8]
    // 0xbad058: stur            x3, [fp, #-0x10]
    // 0xbad05c: StoreField: r3->field_b = r0
    //     0xbad05c: stur            w0, [x3, #0xb]
    // 0xbad060: r1 = Null
    //     0xbad060: mov             x1, NULL
    // 0xbad064: r2 = 10
    //     0xbad064: movz            x2, #0xa
    // 0xbad068: r0 = AllocateArray()
    //     0xbad068: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbad06c: mov             x2, x0
    // 0xbad070: ldur            x0, [fp, #-0x28]
    // 0xbad074: stur            x2, [fp, #-8]
    // 0xbad078: StoreField: r2->field_f = r0
    //     0xbad078: stur            w0, [x2, #0xf]
    // 0xbad07c: r16 = Instance_SizedBox
    //     0xbad07c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbad080: ldr             x16, [x16, #0x8f0]
    // 0xbad084: StoreField: r2->field_13 = r16
    //     0xbad084: stur            w16, [x2, #0x13]
    // 0xbad088: ldur            x0, [fp, #-0x20]
    // 0xbad08c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbad08c: stur            w0, [x2, #0x17]
    // 0xbad090: r16 = Instance_SizedBox
    //     0xbad090: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbad094: ldr             x16, [x16, #0x8f0]
    // 0xbad098: StoreField: r2->field_1b = r16
    //     0xbad098: stur            w16, [x2, #0x1b]
    // 0xbad09c: ldur            x0, [fp, #-0x10]
    // 0xbad0a0: StoreField: r2->field_1f = r0
    //     0xbad0a0: stur            w0, [x2, #0x1f]
    // 0xbad0a4: r1 = <Widget>
    //     0xbad0a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbad0a8: r0 = AllocateGrowableArray()
    //     0xbad0a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbad0ac: mov             x1, x0
    // 0xbad0b0: ldur            x0, [fp, #-8]
    // 0xbad0b4: stur            x1, [fp, #-0x10]
    // 0xbad0b8: StoreField: r1->field_f = r0
    //     0xbad0b8: stur            w0, [x1, #0xf]
    // 0xbad0bc: r0 = 10
    //     0xbad0bc: movz            x0, #0xa
    // 0xbad0c0: StoreField: r1->field_b = r0
    //     0xbad0c0: stur            w0, [x1, #0xb]
    // 0xbad0c4: r0 = Column()
    //     0xbad0c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbad0c8: mov             x1, x0
    // 0xbad0cc: r0 = Instance_Axis
    //     0xbad0cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbad0d0: stur            x1, [fp, #-8]
    // 0xbad0d4: StoreField: r1->field_f = r0
    //     0xbad0d4: stur            w0, [x1, #0xf]
    // 0xbad0d8: r0 = Instance_MainAxisAlignment
    //     0xbad0d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbad0dc: ldr             x0, [x0, #0xa08]
    // 0xbad0e0: StoreField: r1->field_13 = r0
    //     0xbad0e0: stur            w0, [x1, #0x13]
    // 0xbad0e4: r0 = Instance_MainAxisSize
    //     0xbad0e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbad0e8: ldr             x0, [x0, #0xa10]
    // 0xbad0ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xbad0ec: stur            w0, [x1, #0x17]
    // 0xbad0f0: r0 = Instance_CrossAxisAlignment
    //     0xbad0f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbad0f4: ldr             x0, [x0, #0x890]
    // 0xbad0f8: StoreField: r1->field_1b = r0
    //     0xbad0f8: stur            w0, [x1, #0x1b]
    // 0xbad0fc: r0 = Instance_VerticalDirection
    //     0xbad0fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbad100: ldr             x0, [x0, #0xa20]
    // 0xbad104: StoreField: r1->field_23 = r0
    //     0xbad104: stur            w0, [x1, #0x23]
    // 0xbad108: r0 = Instance_Clip
    //     0xbad108: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbad10c: ldr             x0, [x0, #0x38]
    // 0xbad110: StoreField: r1->field_2b = r0
    //     0xbad110: stur            w0, [x1, #0x2b]
    // 0xbad114: StoreField: r1->field_2f = rZR
    //     0xbad114: stur            xzr, [x1, #0x2f]
    // 0xbad118: ldur            x0, [fp, #-0x10]
    // 0xbad11c: StoreField: r1->field_b = r0
    //     0xbad11c: stur            w0, [x1, #0xb]
    // 0xbad120: r0 = Padding()
    //     0xbad120: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbad124: r1 = Instance_EdgeInsets
    //     0xbad124: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xbad128: ldr             x1, [x1, #0xd0]
    // 0xbad12c: StoreField: r0->field_f = r1
    //     0xbad12c: stur            w1, [x0, #0xf]
    // 0xbad130: ldur            x1, [fp, #-8]
    // 0xbad134: StoreField: r0->field_b = r1
    //     0xbad134: stur            w1, [x0, #0xb]
    // 0xbad138: LeaveFrame
    //     0xbad138: mov             SP, fp
    //     0xbad13c: ldp             fp, lr, [SP], #0x10
    // 0xbad140: ret
    //     0xbad140: ret             
    // 0xbad144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbad144: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbad148: b               #0xbace48
    // 0xbad14c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbad14c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbad150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbad150: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbad154: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbad154: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbad164, size: 0x158
    // 0xbad164: EnterFrame
    //     0xbad164: stp             fp, lr, [SP, #-0x10]!
    //     0xbad168: mov             fp, SP
    // 0xbad16c: AllocStack(0x18)
    //     0xbad16c: sub             SP, SP, #0x18
    // 0xbad170: SetupParameters()
    //     0xbad170: ldr             x0, [fp, #0x20]
    //     0xbad174: ldur            w1, [x0, #0x17]
    //     0xbad178: add             x1, x1, HEAP, lsl #32
    // 0xbad17c: CheckStackOverflow
    //     0xbad17c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbad180: cmp             SP, x16
    //     0xbad184: b.ls            #0xbad2b0
    // 0xbad188: LoadField: r0 = r1->field_f
    //     0xbad188: ldur            w0, [x1, #0xf]
    // 0xbad18c: DecompressPointer r0
    //     0xbad18c: add             x0, x0, HEAP, lsl #32
    // 0xbad190: LoadField: r1 = r0->field_b
    //     0xbad190: ldur            w1, [x0, #0xb]
    // 0xbad194: DecompressPointer r1
    //     0xbad194: add             x1, x1, HEAP, lsl #32
    // 0xbad198: cmp             w1, NULL
    // 0xbad19c: b.eq            #0xbad2b8
    // 0xbad1a0: LoadField: r0 = r1->field_b
    //     0xbad1a0: ldur            w0, [x1, #0xb]
    // 0xbad1a4: DecompressPointer r0
    //     0xbad1a4: add             x0, x0, HEAP, lsl #32
    // 0xbad1a8: LoadField: r1 = r0->field_b
    //     0xbad1a8: ldur            w1, [x0, #0xb]
    // 0xbad1ac: DecompressPointer r1
    //     0xbad1ac: add             x1, x1, HEAP, lsl #32
    // 0xbad1b0: cmp             w1, NULL
    // 0xbad1b4: b.ne            #0xbad1c0
    // 0xbad1b8: r0 = Null
    //     0xbad1b8: mov             x0, NULL
    // 0xbad1bc: b               #0xbad1e4
    // 0xbad1c0: LoadField: r0 = r1->field_1b
    //     0xbad1c0: ldur            w0, [x1, #0x1b]
    // 0xbad1c4: DecompressPointer r0
    //     0xbad1c4: add             x0, x0, HEAP, lsl #32
    // 0xbad1c8: cmp             w0, NULL
    // 0xbad1cc: b.ne            #0xbad1d8
    // 0xbad1d0: r0 = Null
    //     0xbad1d0: mov             x0, NULL
    // 0xbad1d4: b               #0xbad1e4
    // 0xbad1d8: LoadField: r1 = r0->field_f
    //     0xbad1d8: ldur            w1, [x0, #0xf]
    // 0xbad1dc: DecompressPointer r1
    //     0xbad1dc: add             x1, x1, HEAP, lsl #32
    // 0xbad1e0: mov             x0, x1
    // 0xbad1e4: cmp             w0, NULL
    // 0xbad1e8: b.ne            #0xbad1fc
    // 0xbad1ec: r1 = <Entities>
    //     0xbad1ec: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0xbad1f0: ldr             x1, [x1, #0xea8]
    // 0xbad1f4: r2 = 0
    //     0xbad1f4: movz            x2, #0
    // 0xbad1f8: r0 = AllocateArray()
    //     0xbad1f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbad1fc: ldr             x1, [fp, #0x10]
    // 0xbad200: r2 = LoadClassIdInstr(r0)
    //     0xbad200: ldur            x2, [x0, #-1]
    //     0xbad204: ubfx            x2, x2, #0xc, #0x14
    // 0xbad208: str             x0, [SP]
    // 0xbad20c: mov             x0, x2
    // 0xbad210: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbad210: movz            x17, #0xc898
    //     0xbad214: add             lr, x0, x17
    //     0xbad218: ldr             lr, [x21, lr, lsl #3]
    //     0xbad21c: blr             lr
    // 0xbad220: r1 = LoadInt32Instr(r0)
    //     0xbad220: sbfx            x1, x0, #1, #0x1f
    // 0xbad224: sub             x0, x1, #2
    // 0xbad228: ldr             x1, [fp, #0x10]
    // 0xbad22c: r2 = LoadInt32Instr(r1)
    //     0xbad22c: sbfx            x2, x1, #1, #0x1f
    //     0xbad230: tbz             w1, #0, #0xbad238
    //     0xbad234: ldur            x2, [x1, #7]
    // 0xbad238: cmp             x2, x0
    // 0xbad23c: b.ge            #0xbad24c
    // 0xbad240: r0 = Instance_SizedBox
    //     0xbad240: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbad244: ldr             x0, [x0, #0x8f0]
    // 0xbad248: b               #0xbad2a4
    // 0xbad24c: ldr             x1, [fp, #0x18]
    // 0xbad250: r0 = of()
    //     0xbad250: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbad254: LoadField: r1 = r0->field_5b
    //     0xbad254: ldur            w1, [x0, #0x5b]
    // 0xbad258: DecompressPointer r1
    //     0xbad258: add             x1, x1, HEAP, lsl #32
    // 0xbad25c: r0 = LoadClassIdInstr(r1)
    //     0xbad25c: ldur            x0, [x1, #-1]
    //     0xbad260: ubfx            x0, x0, #0xc, #0x14
    // 0xbad264: d0 = 0.100000
    //     0xbad264: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbad268: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbad268: sub             lr, x0, #0xffa
    //     0xbad26c: ldr             lr, [x21, lr, lsl #3]
    //     0xbad270: blr             lr
    // 0xbad274: stur            x0, [fp, #-8]
    // 0xbad278: r0 = Divider()
    //     0xbad278: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbad27c: mov             x1, x0
    // 0xbad280: ldur            x0, [fp, #-8]
    // 0xbad284: stur            x1, [fp, #-0x10]
    // 0xbad288: StoreField: r1->field_1f = r0
    //     0xbad288: stur            w0, [x1, #0x1f]
    // 0xbad28c: r0 = Padding()
    //     0xbad28c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbad290: r1 = Instance_EdgeInsets
    //     0xbad290: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a20] Obj!EdgeInsets@d59241
    //     0xbad294: ldr             x1, [x1, #0xa20]
    // 0xbad298: StoreField: r0->field_f = r1
    //     0xbad298: stur            w1, [x0, #0xf]
    // 0xbad29c: ldur            x1, [fp, #-0x10]
    // 0xbad2a0: StoreField: r0->field_b = r1
    //     0xbad2a0: stur            w1, [x0, #0xb]
    // 0xbad2a4: LeaveFrame
    //     0xbad2a4: mov             SP, fp
    //     0xbad2a8: ldp             fp, lr, [SP], #0x10
    // 0xbad2ac: ret
    //     0xbad2ac: ret             
    // 0xbad2b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbad2b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbad2b4: b               #0xbad188
    // 0xbad2b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbad2b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbad2bc, size: 0x8f4
    // 0xbad2bc: EnterFrame
    //     0xbad2bc: stp             fp, lr, [SP, #-0x10]!
    //     0xbad2c0: mov             fp, SP
    // 0xbad2c4: AllocStack(0x38)
    //     0xbad2c4: sub             SP, SP, #0x38
    // 0xbad2c8: SetupParameters()
    //     0xbad2c8: ldr             x0, [fp, #0x20]
    //     0xbad2cc: ldur            w3, [x0, #0x17]
    //     0xbad2d0: add             x3, x3, HEAP, lsl #32
    //     0xbad2d4: stur            x3, [fp, #-0x10]
    // 0xbad2d8: CheckStackOverflow
    //     0xbad2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbad2dc: cmp             SP, x16
    //     0xbad2e0: b.ls            #0xbadb78
    // 0xbad2e4: LoadField: r0 = r3->field_f
    //     0xbad2e4: ldur            w0, [x3, #0xf]
    // 0xbad2e8: DecompressPointer r0
    //     0xbad2e8: add             x0, x0, HEAP, lsl #32
    // 0xbad2ec: LoadField: r1 = r0->field_b
    //     0xbad2ec: ldur            w1, [x0, #0xb]
    // 0xbad2f0: DecompressPointer r1
    //     0xbad2f0: add             x1, x1, HEAP, lsl #32
    // 0xbad2f4: cmp             w1, NULL
    // 0xbad2f8: b.eq            #0xbadb80
    // 0xbad2fc: LoadField: r2 = r1->field_b
    //     0xbad2fc: ldur            w2, [x1, #0xb]
    // 0xbad300: DecompressPointer r2
    //     0xbad300: add             x2, x2, HEAP, lsl #32
    // 0xbad304: LoadField: r0 = r2->field_b
    //     0xbad304: ldur            w0, [x2, #0xb]
    // 0xbad308: DecompressPointer r0
    //     0xbad308: add             x0, x0, HEAP, lsl #32
    // 0xbad30c: cmp             w0, NULL
    // 0xbad310: b.ne            #0xbad320
    // 0xbad314: ldr             x5, [fp, #0x10]
    // 0xbad318: r0 = Null
    //     0xbad318: mov             x0, NULL
    // 0xbad31c: b               #0xbad39c
    // 0xbad320: LoadField: r1 = r0->field_1b
    //     0xbad320: ldur            w1, [x0, #0x1b]
    // 0xbad324: DecompressPointer r1
    //     0xbad324: add             x1, x1, HEAP, lsl #32
    // 0xbad328: cmp             w1, NULL
    // 0xbad32c: b.ne            #0xbad33c
    // 0xbad330: ldr             x5, [fp, #0x10]
    // 0xbad334: r0 = Null
    //     0xbad334: mov             x0, NULL
    // 0xbad338: b               #0xbad39c
    // 0xbad33c: LoadField: r4 = r1->field_f
    //     0xbad33c: ldur            w4, [x1, #0xf]
    // 0xbad340: DecompressPointer r4
    //     0xbad340: add             x4, x4, HEAP, lsl #32
    // 0xbad344: cmp             w4, NULL
    // 0xbad348: b.ne            #0xbad358
    // 0xbad34c: ldr             x5, [fp, #0x10]
    // 0xbad350: r0 = Null
    //     0xbad350: mov             x0, NULL
    // 0xbad354: b               #0xbad39c
    // 0xbad358: ldr             x5, [fp, #0x10]
    // 0xbad35c: LoadField: r0 = r4->field_b
    //     0xbad35c: ldur            w0, [x4, #0xb]
    // 0xbad360: r6 = LoadInt32Instr(r5)
    //     0xbad360: sbfx            x6, x5, #1, #0x1f
    //     0xbad364: tbz             w5, #0, #0xbad36c
    //     0xbad368: ldur            x6, [x5, #7]
    // 0xbad36c: r1 = LoadInt32Instr(r0)
    //     0xbad36c: sbfx            x1, x0, #1, #0x1f
    // 0xbad370: mov             x0, x1
    // 0xbad374: mov             x1, x6
    // 0xbad378: cmp             x1, x0
    // 0xbad37c: b.hs            #0xbadb84
    // 0xbad380: LoadField: r0 = r4->field_f
    //     0xbad380: ldur            w0, [x4, #0xf]
    // 0xbad384: DecompressPointer r0
    //     0xbad384: add             x0, x0, HEAP, lsl #32
    // 0xbad388: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbad388: add             x16, x0, x6, lsl #2
    //     0xbad38c: ldur            w1, [x16, #0xf]
    // 0xbad390: DecompressPointer r1
    //     0xbad390: add             x1, x1, HEAP, lsl #32
    // 0xbad394: LoadField: r0 = r1->field_7
    //     0xbad394: ldur            w0, [x1, #7]
    // 0xbad398: DecompressPointer r0
    //     0xbad398: add             x0, x0, HEAP, lsl #32
    // 0xbad39c: cmp             w0, NULL
    // 0xbad3a0: b.ne            #0xbad3a8
    // 0xbad3a4: r0 = ""
    //     0xbad3a4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbad3a8: stur            x0, [fp, #-8]
    // 0xbad3ac: LoadField: r1 = r2->field_b
    //     0xbad3ac: ldur            w1, [x2, #0xb]
    // 0xbad3b0: DecompressPointer r1
    //     0xbad3b0: add             x1, x1, HEAP, lsl #32
    // 0xbad3b4: cmp             w1, NULL
    // 0xbad3b8: b.ne            #0xbad3c4
    // 0xbad3bc: r1 = Null
    //     0xbad3bc: mov             x1, NULL
    // 0xbad3c0: b               #0xbad3e4
    // 0xbad3c4: LoadField: r2 = r1->field_1b
    //     0xbad3c4: ldur            w2, [x1, #0x1b]
    // 0xbad3c8: DecompressPointer r2
    //     0xbad3c8: add             x2, x2, HEAP, lsl #32
    // 0xbad3cc: cmp             w2, NULL
    // 0xbad3d0: b.ne            #0xbad3dc
    // 0xbad3d4: r1 = Null
    //     0xbad3d4: mov             x1, NULL
    // 0xbad3d8: b               #0xbad3e4
    // 0xbad3dc: LoadField: r1 = r2->field_f
    //     0xbad3dc: ldur            w1, [x2, #0xf]
    // 0xbad3e0: DecompressPointer r1
    //     0xbad3e0: add             x1, x1, HEAP, lsl #32
    // 0xbad3e4: cmp             w1, NULL
    // 0xbad3e8: b.ne            #0xbad400
    // 0xbad3ec: r1 = <Entities>
    //     0xbad3ec: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0xbad3f0: ldr             x1, [x1, #0xea8]
    // 0xbad3f4: r2 = 0
    //     0xbad3f4: movz            x2, #0
    // 0xbad3f8: r0 = AllocateArray()
    //     0xbad3f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbad3fc: b               #0xbad404
    // 0xbad400: mov             x0, x1
    // 0xbad404: ldr             x1, [fp, #0x10]
    // 0xbad408: r2 = LoadClassIdInstr(r0)
    //     0xbad408: ldur            x2, [x0, #-1]
    //     0xbad40c: ubfx            x2, x2, #0xc, #0x14
    // 0xbad410: str             x0, [SP]
    // 0xbad414: mov             x0, x2
    // 0xbad418: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbad418: movz            x17, #0xc898
    //     0xbad41c: add             lr, x0, x17
    //     0xbad420: ldr             lr, [x21, lr, lsl #3]
    //     0xbad424: blr             lr
    // 0xbad428: r1 = LoadInt32Instr(r0)
    //     0xbad428: sbfx            x1, x0, #1, #0x1f
    // 0xbad42c: sub             x0, x1, #1
    // 0xbad430: ldr             x1, [fp, #0x10]
    // 0xbad434: r2 = LoadInt32Instr(r1)
    //     0xbad434: sbfx            x2, x1, #1, #0x1f
    //     0xbad438: tbz             w1, #0, #0xbad440
    //     0xbad43c: ldur            x2, [x1, #7]
    // 0xbad440: stur            x2, [fp, #-0x18]
    // 0xbad444: cmp             x2, x0
    // 0xbad448: b.ge            #0xbad684
    // 0xbad44c: ldur            x0, [fp, #-0x10]
    // 0xbad450: ldr             x1, [fp, #0x18]
    // 0xbad454: r0 = of()
    //     0xbad454: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbad458: LoadField: r1 = r0->field_87
    //     0xbad458: ldur            w1, [x0, #0x87]
    // 0xbad45c: DecompressPointer r1
    //     0xbad45c: add             x1, x1, HEAP, lsl #32
    // 0xbad460: LoadField: r3 = r1->field_2b
    //     0xbad460: ldur            w3, [x1, #0x2b]
    // 0xbad464: DecompressPointer r3
    //     0xbad464: add             x3, x3, HEAP, lsl #32
    // 0xbad468: ldur            x4, [fp, #-0x10]
    // 0xbad46c: stur            x3, [fp, #-0x20]
    // 0xbad470: LoadField: r0 = r4->field_f
    //     0xbad470: ldur            w0, [x4, #0xf]
    // 0xbad474: DecompressPointer r0
    //     0xbad474: add             x0, x0, HEAP, lsl #32
    // 0xbad478: LoadField: r1 = r0->field_b
    //     0xbad478: ldur            w1, [x0, #0xb]
    // 0xbad47c: DecompressPointer r1
    //     0xbad47c: add             x1, x1, HEAP, lsl #32
    // 0xbad480: cmp             w1, NULL
    // 0xbad484: b.eq            #0xbadb88
    // 0xbad488: LoadField: r0 = r1->field_b
    //     0xbad488: ldur            w0, [x1, #0xb]
    // 0xbad48c: DecompressPointer r0
    //     0xbad48c: add             x0, x0, HEAP, lsl #32
    // 0xbad490: LoadField: r1 = r0->field_b
    //     0xbad490: ldur            w1, [x0, #0xb]
    // 0xbad494: DecompressPointer r1
    //     0xbad494: add             x1, x1, HEAP, lsl #32
    // 0xbad498: cmp             w1, NULL
    // 0xbad49c: b.ne            #0xbad4ac
    // 0xbad4a0: ldur            x5, [fp, #-0x18]
    // 0xbad4a4: r0 = Null
    //     0xbad4a4: mov             x0, NULL
    // 0xbad4a8: b               #0xbad51c
    // 0xbad4ac: LoadField: r0 = r1->field_1b
    //     0xbad4ac: ldur            w0, [x1, #0x1b]
    // 0xbad4b0: DecompressPointer r0
    //     0xbad4b0: add             x0, x0, HEAP, lsl #32
    // 0xbad4b4: cmp             w0, NULL
    // 0xbad4b8: b.ne            #0xbad4c8
    // 0xbad4bc: ldur            x5, [fp, #-0x18]
    // 0xbad4c0: r0 = Null
    //     0xbad4c0: mov             x0, NULL
    // 0xbad4c4: b               #0xbad51c
    // 0xbad4c8: LoadField: r2 = r0->field_f
    //     0xbad4c8: ldur            w2, [x0, #0xf]
    // 0xbad4cc: DecompressPointer r2
    //     0xbad4cc: add             x2, x2, HEAP, lsl #32
    // 0xbad4d0: cmp             w2, NULL
    // 0xbad4d4: b.ne            #0xbad4e4
    // 0xbad4d8: ldur            x5, [fp, #-0x18]
    // 0xbad4dc: r0 = Null
    //     0xbad4dc: mov             x0, NULL
    // 0xbad4e0: b               #0xbad51c
    // 0xbad4e4: ldur            x5, [fp, #-0x18]
    // 0xbad4e8: LoadField: r0 = r2->field_b
    //     0xbad4e8: ldur            w0, [x2, #0xb]
    // 0xbad4ec: r1 = LoadInt32Instr(r0)
    //     0xbad4ec: sbfx            x1, x0, #1, #0x1f
    // 0xbad4f0: mov             x0, x1
    // 0xbad4f4: mov             x1, x5
    // 0xbad4f8: cmp             x1, x0
    // 0xbad4fc: b.hs            #0xbadb8c
    // 0xbad500: LoadField: r0 = r2->field_f
    //     0xbad500: ldur            w0, [x2, #0xf]
    // 0xbad504: DecompressPointer r0
    //     0xbad504: add             x0, x0, HEAP, lsl #32
    // 0xbad508: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbad508: add             x16, x0, x5, lsl #2
    //     0xbad50c: ldur            w1, [x16, #0xf]
    // 0xbad510: DecompressPointer r1
    //     0xbad510: add             x1, x1, HEAP, lsl #32
    // 0xbad514: LoadField: r0 = r1->field_b
    //     0xbad514: ldur            w0, [x1, #0xb]
    // 0xbad518: DecompressPointer r0
    //     0xbad518: add             x0, x0, HEAP, lsl #32
    // 0xbad51c: cmp             w0, NULL
    // 0xbad520: b.ne            #0xbad52c
    // 0xbad524: r1 = ""
    //     0xbad524: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbad528: b               #0xbad530
    // 0xbad52c: mov             x1, x0
    // 0xbad530: r0 = LoadClassIdInstr(r1)
    //     0xbad530: ldur            x0, [x1, #-1]
    //     0xbad534: ubfx            x0, x0, #0xc, #0x14
    // 0xbad538: r2 = "-"
    //     0xbad538: ldr             x2, [PP, #0x31a8]  ; [pp+0x31a8] "-"
    // 0xbad53c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbad53c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbad540: r0 = GDT[cid_x0 + -0xffe]()
    //     0xbad540: sub             lr, x0, #0xffe
    //     0xbad544: ldr             lr, [x21, lr, lsl #3]
    //     0xbad548: blr             lr
    // 0xbad54c: tbz             w0, #4, #0xbad658
    // 0xbad550: ldur            x3, [fp, #-0x10]
    // 0xbad554: LoadField: r0 = r3->field_f
    //     0xbad554: ldur            w0, [x3, #0xf]
    // 0xbad558: DecompressPointer r0
    //     0xbad558: add             x0, x0, HEAP, lsl #32
    // 0xbad55c: LoadField: r1 = r0->field_b
    //     0xbad55c: ldur            w1, [x0, #0xb]
    // 0xbad560: DecompressPointer r1
    //     0xbad560: add             x1, x1, HEAP, lsl #32
    // 0xbad564: cmp             w1, NULL
    // 0xbad568: b.eq            #0xbadb90
    // 0xbad56c: LoadField: r0 = r1->field_b
    //     0xbad56c: ldur            w0, [x1, #0xb]
    // 0xbad570: DecompressPointer r0
    //     0xbad570: add             x0, x0, HEAP, lsl #32
    // 0xbad574: LoadField: r1 = r0->field_b
    //     0xbad574: ldur            w1, [x0, #0xb]
    // 0xbad578: DecompressPointer r1
    //     0xbad578: add             x1, x1, HEAP, lsl #32
    // 0xbad57c: cmp             w1, NULL
    // 0xbad580: b.ne            #0xbad590
    // 0xbad584: ldur            x4, [fp, #-0x18]
    // 0xbad588: r0 = Null
    //     0xbad588: mov             x0, NULL
    // 0xbad58c: b               #0xbad600
    // 0xbad590: LoadField: r0 = r1->field_1b
    //     0xbad590: ldur            w0, [x1, #0x1b]
    // 0xbad594: DecompressPointer r0
    //     0xbad594: add             x0, x0, HEAP, lsl #32
    // 0xbad598: cmp             w0, NULL
    // 0xbad59c: b.ne            #0xbad5ac
    // 0xbad5a0: ldur            x4, [fp, #-0x18]
    // 0xbad5a4: r0 = Null
    //     0xbad5a4: mov             x0, NULL
    // 0xbad5a8: b               #0xbad600
    // 0xbad5ac: LoadField: r2 = r0->field_f
    //     0xbad5ac: ldur            w2, [x0, #0xf]
    // 0xbad5b0: DecompressPointer r2
    //     0xbad5b0: add             x2, x2, HEAP, lsl #32
    // 0xbad5b4: cmp             w2, NULL
    // 0xbad5b8: b.ne            #0xbad5c8
    // 0xbad5bc: ldur            x4, [fp, #-0x18]
    // 0xbad5c0: r0 = Null
    //     0xbad5c0: mov             x0, NULL
    // 0xbad5c4: b               #0xbad600
    // 0xbad5c8: ldur            x4, [fp, #-0x18]
    // 0xbad5cc: LoadField: r0 = r2->field_b
    //     0xbad5cc: ldur            w0, [x2, #0xb]
    // 0xbad5d0: r1 = LoadInt32Instr(r0)
    //     0xbad5d0: sbfx            x1, x0, #1, #0x1f
    // 0xbad5d4: mov             x0, x1
    // 0xbad5d8: mov             x1, x4
    // 0xbad5dc: cmp             x1, x0
    // 0xbad5e0: b.hs            #0xbadb94
    // 0xbad5e4: LoadField: r0 = r2->field_f
    //     0xbad5e4: ldur            w0, [x2, #0xf]
    // 0xbad5e8: DecompressPointer r0
    //     0xbad5e8: add             x0, x0, HEAP, lsl #32
    // 0xbad5ec: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbad5ec: add             x16, x0, x4, lsl #2
    //     0xbad5f0: ldur            w1, [x16, #0xf]
    // 0xbad5f4: DecompressPointer r1
    //     0xbad5f4: add             x1, x1, HEAP, lsl #32
    // 0xbad5f8: LoadField: r0 = r1->field_7
    //     0xbad5f8: ldur            w0, [x1, #7]
    // 0xbad5fc: DecompressPointer r0
    //     0xbad5fc: add             x0, x0, HEAP, lsl #32
    // 0xbad600: cmp             w0, NULL
    // 0xbad604: b.ne            #0xbad610
    // 0xbad608: r1 = ""
    //     0xbad608: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbad60c: b               #0xbad614
    // 0xbad610: mov             x1, x0
    // 0xbad614: r0 = LoadClassIdInstr(r1)
    //     0xbad614: ldur            x0, [x1, #-1]
    //     0xbad618: ubfx            x0, x0, #0xc, #0x14
    // 0xbad61c: r2 = "COD"
    //     0xbad61c: add             x2, PP, #0x39, lsl #12  ; [pp+0x39838] "COD"
    //     0xbad620: ldr             x2, [x2, #0x838]
    // 0xbad624: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbad624: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbad628: r0 = GDT[cid_x0 + -0xffe]()
    //     0xbad628: sub             lr, x0, #0xffe
    //     0xbad62c: ldr             lr, [x21, lr, lsl #3]
    //     0xbad630: blr             lr
    // 0xbad634: tbnz            w0, #4, #0xbad644
    // 0xbad638: r0 = Instance_MaterialColor
    //     0xbad638: add             x0, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbad63c: ldr             x0, [x0, #0x180]
    // 0xbad640: b               #0xbad660
    // 0xbad644: r1 = Instance_Color
    //     0xbad644: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbad648: d0 = 0.700000
    //     0xbad648: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbad64c: ldr             d0, [x17, #0xf48]
    // 0xbad650: r0 = withOpacity()
    //     0xbad650: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbad654: b               #0xbad660
    // 0xbad658: r0 = Instance_Color
    //     0xbad658: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbad65c: ldr             x0, [x0, #0x858]
    // 0xbad660: r16 = 12.000000
    //     0xbad660: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbad664: ldr             x16, [x16, #0x9e8]
    // 0xbad668: stp             x16, x0, [SP]
    // 0xbad66c: ldur            x1, [fp, #-0x20]
    // 0xbad670: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbad670: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbad674: ldr             x4, [x4, #0x9b8]
    // 0xbad678: r0 = copyWith()
    //     0xbad678: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbad67c: mov             x2, x0
    // 0xbad680: b               #0xbad6c0
    // 0xbad684: ldr             x1, [fp, #0x18]
    // 0xbad688: r0 = of()
    //     0xbad688: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbad68c: LoadField: r1 = r0->field_87
    //     0xbad68c: ldur            w1, [x0, #0x87]
    // 0xbad690: DecompressPointer r1
    //     0xbad690: add             x1, x1, HEAP, lsl #32
    // 0xbad694: LoadField: r0 = r1->field_7
    //     0xbad694: ldur            w0, [x1, #7]
    // 0xbad698: DecompressPointer r0
    //     0xbad698: add             x0, x0, HEAP, lsl #32
    // 0xbad69c: r16 = Instance_Color
    //     0xbad69c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbad6a0: r30 = 12.000000
    //     0xbad6a0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbad6a4: ldr             lr, [lr, #0x9e8]
    // 0xbad6a8: stp             lr, x16, [SP]
    // 0xbad6ac: mov             x1, x0
    // 0xbad6b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbad6b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbad6b4: ldr             x4, [x4, #0x9b8]
    // 0xbad6b8: r0 = copyWith()
    //     0xbad6b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbad6bc: mov             x2, x0
    // 0xbad6c0: ldur            x0, [fp, #-0x10]
    // 0xbad6c4: ldur            x1, [fp, #-8]
    // 0xbad6c8: stur            x2, [fp, #-0x20]
    // 0xbad6cc: r0 = Text()
    //     0xbad6cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbad6d0: mov             x3, x0
    // 0xbad6d4: ldur            x0, [fp, #-8]
    // 0xbad6d8: stur            x3, [fp, #-0x28]
    // 0xbad6dc: StoreField: r3->field_b = r0
    //     0xbad6dc: stur            w0, [x3, #0xb]
    // 0xbad6e0: ldur            x0, [fp, #-0x20]
    // 0xbad6e4: StoreField: r3->field_13 = r0
    //     0xbad6e4: stur            w0, [x3, #0x13]
    // 0xbad6e8: ldur            x4, [fp, #-0x10]
    // 0xbad6ec: LoadField: r0 = r4->field_f
    //     0xbad6ec: ldur            w0, [x4, #0xf]
    // 0xbad6f0: DecompressPointer r0
    //     0xbad6f0: add             x0, x0, HEAP, lsl #32
    // 0xbad6f4: LoadField: r1 = r0->field_b
    //     0xbad6f4: ldur            w1, [x0, #0xb]
    // 0xbad6f8: DecompressPointer r1
    //     0xbad6f8: add             x1, x1, HEAP, lsl #32
    // 0xbad6fc: cmp             w1, NULL
    // 0xbad700: b.eq            #0xbadb98
    // 0xbad704: LoadField: r2 = r1->field_b
    //     0xbad704: ldur            w2, [x1, #0xb]
    // 0xbad708: DecompressPointer r2
    //     0xbad708: add             x2, x2, HEAP, lsl #32
    // 0xbad70c: LoadField: r0 = r2->field_b
    //     0xbad70c: ldur            w0, [x2, #0xb]
    // 0xbad710: DecompressPointer r0
    //     0xbad710: add             x0, x0, HEAP, lsl #32
    // 0xbad714: cmp             w0, NULL
    // 0xbad718: b.ne            #0xbad728
    // 0xbad71c: ldur            x6, [fp, #-0x18]
    // 0xbad720: r0 = Null
    //     0xbad720: mov             x0, NULL
    // 0xbad724: b               #0xbad798
    // 0xbad728: LoadField: r1 = r0->field_1b
    //     0xbad728: ldur            w1, [x0, #0x1b]
    // 0xbad72c: DecompressPointer r1
    //     0xbad72c: add             x1, x1, HEAP, lsl #32
    // 0xbad730: cmp             w1, NULL
    // 0xbad734: b.ne            #0xbad744
    // 0xbad738: ldur            x6, [fp, #-0x18]
    // 0xbad73c: r0 = Null
    //     0xbad73c: mov             x0, NULL
    // 0xbad740: b               #0xbad798
    // 0xbad744: LoadField: r5 = r1->field_f
    //     0xbad744: ldur            w5, [x1, #0xf]
    // 0xbad748: DecompressPointer r5
    //     0xbad748: add             x5, x5, HEAP, lsl #32
    // 0xbad74c: cmp             w5, NULL
    // 0xbad750: b.ne            #0xbad760
    // 0xbad754: ldur            x6, [fp, #-0x18]
    // 0xbad758: r0 = Null
    //     0xbad758: mov             x0, NULL
    // 0xbad75c: b               #0xbad798
    // 0xbad760: ldur            x6, [fp, #-0x18]
    // 0xbad764: LoadField: r0 = r5->field_b
    //     0xbad764: ldur            w0, [x5, #0xb]
    // 0xbad768: r1 = LoadInt32Instr(r0)
    //     0xbad768: sbfx            x1, x0, #1, #0x1f
    // 0xbad76c: mov             x0, x1
    // 0xbad770: mov             x1, x6
    // 0xbad774: cmp             x1, x0
    // 0xbad778: b.hs            #0xbadb9c
    // 0xbad77c: LoadField: r0 = r5->field_f
    //     0xbad77c: ldur            w0, [x5, #0xf]
    // 0xbad780: DecompressPointer r0
    //     0xbad780: add             x0, x0, HEAP, lsl #32
    // 0xbad784: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbad784: add             x16, x0, x6, lsl #2
    //     0xbad788: ldur            w1, [x16, #0xf]
    // 0xbad78c: DecompressPointer r1
    //     0xbad78c: add             x1, x1, HEAP, lsl #32
    // 0xbad790: LoadField: r0 = r1->field_b
    //     0xbad790: ldur            w0, [x1, #0xb]
    // 0xbad794: DecompressPointer r0
    //     0xbad794: add             x0, x0, HEAP, lsl #32
    // 0xbad798: cmp             w0, NULL
    // 0xbad79c: b.ne            #0xbad7a4
    // 0xbad7a0: r0 = ""
    //     0xbad7a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbad7a4: stur            x0, [fp, #-8]
    // 0xbad7a8: LoadField: r1 = r2->field_b
    //     0xbad7a8: ldur            w1, [x2, #0xb]
    // 0xbad7ac: DecompressPointer r1
    //     0xbad7ac: add             x1, x1, HEAP, lsl #32
    // 0xbad7b0: cmp             w1, NULL
    // 0xbad7b4: b.ne            #0xbad7c0
    // 0xbad7b8: r1 = Null
    //     0xbad7b8: mov             x1, NULL
    // 0xbad7bc: b               #0xbad7e0
    // 0xbad7c0: LoadField: r2 = r1->field_1b
    //     0xbad7c0: ldur            w2, [x1, #0x1b]
    // 0xbad7c4: DecompressPointer r2
    //     0xbad7c4: add             x2, x2, HEAP, lsl #32
    // 0xbad7c8: cmp             w2, NULL
    // 0xbad7cc: b.ne            #0xbad7d8
    // 0xbad7d0: r1 = Null
    //     0xbad7d0: mov             x1, NULL
    // 0xbad7d4: b               #0xbad7e0
    // 0xbad7d8: LoadField: r1 = r2->field_f
    //     0xbad7d8: ldur            w1, [x2, #0xf]
    // 0xbad7dc: DecompressPointer r1
    //     0xbad7dc: add             x1, x1, HEAP, lsl #32
    // 0xbad7e0: cmp             w1, NULL
    // 0xbad7e4: b.ne            #0xbad7fc
    // 0xbad7e8: r1 = <Entities>
    //     0xbad7e8: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0xbad7ec: ldr             x1, [x1, #0xea8]
    // 0xbad7f0: r2 = 0
    //     0xbad7f0: movz            x2, #0
    // 0xbad7f4: r0 = AllocateArray()
    //     0xbad7f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbad7f8: b               #0xbad800
    // 0xbad7fc: mov             x0, x1
    // 0xbad800: ldur            x1, [fp, #-0x18]
    // 0xbad804: r2 = LoadClassIdInstr(r0)
    //     0xbad804: ldur            x2, [x0, #-1]
    //     0xbad808: ubfx            x2, x2, #0xc, #0x14
    // 0xbad80c: str             x0, [SP]
    // 0xbad810: mov             x0, x2
    // 0xbad814: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbad814: movz            x17, #0xc898
    //     0xbad818: add             lr, x0, x17
    //     0xbad81c: ldr             lr, [x21, lr, lsl #3]
    //     0xbad820: blr             lr
    // 0xbad824: r1 = LoadInt32Instr(r0)
    //     0xbad824: sbfx            x1, x0, #1, #0x1f
    // 0xbad828: sub             x0, x1, #1
    // 0xbad82c: ldur            x2, [fp, #-0x18]
    // 0xbad830: cmp             x2, x0
    // 0xbad834: b.ge            #0xbada64
    // 0xbad838: ldur            x0, [fp, #-0x10]
    // 0xbad83c: ldr             x1, [fp, #0x18]
    // 0xbad840: r0 = of()
    //     0xbad840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbad844: LoadField: r1 = r0->field_87
    //     0xbad844: ldur            w1, [x0, #0x87]
    // 0xbad848: DecompressPointer r1
    //     0xbad848: add             x1, x1, HEAP, lsl #32
    // 0xbad84c: LoadField: r3 = r1->field_2b
    //     0xbad84c: ldur            w3, [x1, #0x2b]
    // 0xbad850: DecompressPointer r3
    //     0xbad850: add             x3, x3, HEAP, lsl #32
    // 0xbad854: ldur            x4, [fp, #-0x10]
    // 0xbad858: stur            x3, [fp, #-0x20]
    // 0xbad85c: LoadField: r0 = r4->field_f
    //     0xbad85c: ldur            w0, [x4, #0xf]
    // 0xbad860: DecompressPointer r0
    //     0xbad860: add             x0, x0, HEAP, lsl #32
    // 0xbad864: LoadField: r1 = r0->field_b
    //     0xbad864: ldur            w1, [x0, #0xb]
    // 0xbad868: DecompressPointer r1
    //     0xbad868: add             x1, x1, HEAP, lsl #32
    // 0xbad86c: cmp             w1, NULL
    // 0xbad870: b.eq            #0xbadba0
    // 0xbad874: LoadField: r0 = r1->field_b
    //     0xbad874: ldur            w0, [x1, #0xb]
    // 0xbad878: DecompressPointer r0
    //     0xbad878: add             x0, x0, HEAP, lsl #32
    // 0xbad87c: LoadField: r1 = r0->field_b
    //     0xbad87c: ldur            w1, [x0, #0xb]
    // 0xbad880: DecompressPointer r1
    //     0xbad880: add             x1, x1, HEAP, lsl #32
    // 0xbad884: cmp             w1, NULL
    // 0xbad888: b.ne            #0xbad898
    // 0xbad88c: ldur            x5, [fp, #-0x18]
    // 0xbad890: r0 = Null
    //     0xbad890: mov             x0, NULL
    // 0xbad894: b               #0xbad908
    // 0xbad898: LoadField: r0 = r1->field_1b
    //     0xbad898: ldur            w0, [x1, #0x1b]
    // 0xbad89c: DecompressPointer r0
    //     0xbad89c: add             x0, x0, HEAP, lsl #32
    // 0xbad8a0: cmp             w0, NULL
    // 0xbad8a4: b.ne            #0xbad8b4
    // 0xbad8a8: ldur            x5, [fp, #-0x18]
    // 0xbad8ac: r0 = Null
    //     0xbad8ac: mov             x0, NULL
    // 0xbad8b0: b               #0xbad908
    // 0xbad8b4: LoadField: r2 = r0->field_f
    //     0xbad8b4: ldur            w2, [x0, #0xf]
    // 0xbad8b8: DecompressPointer r2
    //     0xbad8b8: add             x2, x2, HEAP, lsl #32
    // 0xbad8bc: cmp             w2, NULL
    // 0xbad8c0: b.ne            #0xbad8d0
    // 0xbad8c4: ldur            x5, [fp, #-0x18]
    // 0xbad8c8: r0 = Null
    //     0xbad8c8: mov             x0, NULL
    // 0xbad8cc: b               #0xbad908
    // 0xbad8d0: ldur            x5, [fp, #-0x18]
    // 0xbad8d4: LoadField: r0 = r2->field_b
    //     0xbad8d4: ldur            w0, [x2, #0xb]
    // 0xbad8d8: r1 = LoadInt32Instr(r0)
    //     0xbad8d8: sbfx            x1, x0, #1, #0x1f
    // 0xbad8dc: mov             x0, x1
    // 0xbad8e0: mov             x1, x5
    // 0xbad8e4: cmp             x1, x0
    // 0xbad8e8: b.hs            #0xbadba4
    // 0xbad8ec: LoadField: r0 = r2->field_f
    //     0xbad8ec: ldur            w0, [x2, #0xf]
    // 0xbad8f0: DecompressPointer r0
    //     0xbad8f0: add             x0, x0, HEAP, lsl #32
    // 0xbad8f4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbad8f4: add             x16, x0, x5, lsl #2
    //     0xbad8f8: ldur            w1, [x16, #0xf]
    // 0xbad8fc: DecompressPointer r1
    //     0xbad8fc: add             x1, x1, HEAP, lsl #32
    // 0xbad900: LoadField: r0 = r1->field_b
    //     0xbad900: ldur            w0, [x1, #0xb]
    // 0xbad904: DecompressPointer r0
    //     0xbad904: add             x0, x0, HEAP, lsl #32
    // 0xbad908: cmp             w0, NULL
    // 0xbad90c: b.ne            #0xbad918
    // 0xbad910: r1 = ""
    //     0xbad910: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbad914: b               #0xbad91c
    // 0xbad918: mov             x1, x0
    // 0xbad91c: r0 = LoadClassIdInstr(r1)
    //     0xbad91c: ldur            x0, [x1, #-1]
    //     0xbad920: ubfx            x0, x0, #0xc, #0x14
    // 0xbad924: r2 = "-"
    //     0xbad924: ldr             x2, [PP, #0x31a8]  ; [pp+0x31a8] "-"
    // 0xbad928: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbad928: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbad92c: r0 = GDT[cid_x0 + -0xffe]()
    //     0xbad92c: sub             lr, x0, #0xffe
    //     0xbad930: ldr             lr, [x21, lr, lsl #3]
    //     0xbad934: blr             lr
    // 0xbad938: tbz             w0, #4, #0xbada38
    // 0xbad93c: ldur            x0, [fp, #-0x10]
    // 0xbad940: LoadField: r1 = r0->field_f
    //     0xbad940: ldur            w1, [x0, #0xf]
    // 0xbad944: DecompressPointer r1
    //     0xbad944: add             x1, x1, HEAP, lsl #32
    // 0xbad948: LoadField: r0 = r1->field_b
    //     0xbad948: ldur            w0, [x1, #0xb]
    // 0xbad94c: DecompressPointer r0
    //     0xbad94c: add             x0, x0, HEAP, lsl #32
    // 0xbad950: cmp             w0, NULL
    // 0xbad954: b.eq            #0xbadba8
    // 0xbad958: LoadField: r1 = r0->field_b
    //     0xbad958: ldur            w1, [x0, #0xb]
    // 0xbad95c: DecompressPointer r1
    //     0xbad95c: add             x1, x1, HEAP, lsl #32
    // 0xbad960: LoadField: r0 = r1->field_b
    //     0xbad960: ldur            w0, [x1, #0xb]
    // 0xbad964: DecompressPointer r0
    //     0xbad964: add             x0, x0, HEAP, lsl #32
    // 0xbad968: cmp             w0, NULL
    // 0xbad96c: b.ne            #0xbad978
    // 0xbad970: r0 = Null
    //     0xbad970: mov             x0, NULL
    // 0xbad974: b               #0xbad9e0
    // 0xbad978: LoadField: r1 = r0->field_1b
    //     0xbad978: ldur            w1, [x0, #0x1b]
    // 0xbad97c: DecompressPointer r1
    //     0xbad97c: add             x1, x1, HEAP, lsl #32
    // 0xbad980: cmp             w1, NULL
    // 0xbad984: b.ne            #0xbad990
    // 0xbad988: r0 = Null
    //     0xbad988: mov             x0, NULL
    // 0xbad98c: b               #0xbad9e0
    // 0xbad990: LoadField: r2 = r1->field_f
    //     0xbad990: ldur            w2, [x1, #0xf]
    // 0xbad994: DecompressPointer r2
    //     0xbad994: add             x2, x2, HEAP, lsl #32
    // 0xbad998: cmp             w2, NULL
    // 0xbad99c: b.ne            #0xbad9a8
    // 0xbad9a0: r0 = Null
    //     0xbad9a0: mov             x0, NULL
    // 0xbad9a4: b               #0xbad9e0
    // 0xbad9a8: ldur            x3, [fp, #-0x18]
    // 0xbad9ac: LoadField: r0 = r2->field_b
    //     0xbad9ac: ldur            w0, [x2, #0xb]
    // 0xbad9b0: r1 = LoadInt32Instr(r0)
    //     0xbad9b0: sbfx            x1, x0, #1, #0x1f
    // 0xbad9b4: mov             x0, x1
    // 0xbad9b8: mov             x1, x3
    // 0xbad9bc: cmp             x1, x0
    // 0xbad9c0: b.hs            #0xbadbac
    // 0xbad9c4: LoadField: r0 = r2->field_f
    //     0xbad9c4: ldur            w0, [x2, #0xf]
    // 0xbad9c8: DecompressPointer r0
    //     0xbad9c8: add             x0, x0, HEAP, lsl #32
    // 0xbad9cc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbad9cc: add             x16, x0, x3, lsl #2
    //     0xbad9d0: ldur            w1, [x16, #0xf]
    // 0xbad9d4: DecompressPointer r1
    //     0xbad9d4: add             x1, x1, HEAP, lsl #32
    // 0xbad9d8: LoadField: r0 = r1->field_7
    //     0xbad9d8: ldur            w0, [x1, #7]
    // 0xbad9dc: DecompressPointer r0
    //     0xbad9dc: add             x0, x0, HEAP, lsl #32
    // 0xbad9e0: cmp             w0, NULL
    // 0xbad9e4: b.ne            #0xbad9f0
    // 0xbad9e8: r1 = ""
    //     0xbad9e8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbad9ec: b               #0xbad9f4
    // 0xbad9f0: mov             x1, x0
    // 0xbad9f4: r0 = LoadClassIdInstr(r1)
    //     0xbad9f4: ldur            x0, [x1, #-1]
    //     0xbad9f8: ubfx            x0, x0, #0xc, #0x14
    // 0xbad9fc: r2 = "COD"
    //     0xbad9fc: add             x2, PP, #0x39, lsl #12  ; [pp+0x39838] "COD"
    //     0xbada00: ldr             x2, [x2, #0x838]
    // 0xbada04: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbada04: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbada08: r0 = GDT[cid_x0 + -0xffe]()
    //     0xbada08: sub             lr, x0, #0xffe
    //     0xbada0c: ldr             lr, [x21, lr, lsl #3]
    //     0xbada10: blr             lr
    // 0xbada14: tbnz            w0, #4, #0xbada24
    // 0xbada18: r0 = Instance_MaterialColor
    //     0xbada18: add             x0, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbada1c: ldr             x0, [x0, #0x180]
    // 0xbada20: b               #0xbada40
    // 0xbada24: r1 = Instance_Color
    //     0xbada24: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbada28: d0 = 0.700000
    //     0xbada28: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbada2c: ldr             d0, [x17, #0xf48]
    // 0xbada30: r0 = withOpacity()
    //     0xbada30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbada34: b               #0xbada40
    // 0xbada38: r0 = Instance_Color
    //     0xbada38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbada3c: ldr             x0, [x0, #0x858]
    // 0xbada40: r16 = 12.000000
    //     0xbada40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbada44: ldr             x16, [x16, #0x9e8]
    // 0xbada48: stp             x16, x0, [SP]
    // 0xbada4c: ldur            x1, [fp, #-0x20]
    // 0xbada50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbada50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbada54: ldr             x4, [x4, #0x9b8]
    // 0xbada58: r0 = copyWith()
    //     0xbada58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbada5c: mov             x2, x0
    // 0xbada60: b               #0xbadaa0
    // 0xbada64: ldr             x1, [fp, #0x18]
    // 0xbada68: r0 = of()
    //     0xbada68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbada6c: LoadField: r1 = r0->field_87
    //     0xbada6c: ldur            w1, [x0, #0x87]
    // 0xbada70: DecompressPointer r1
    //     0xbada70: add             x1, x1, HEAP, lsl #32
    // 0xbada74: LoadField: r0 = r1->field_7
    //     0xbada74: ldur            w0, [x1, #7]
    // 0xbada78: DecompressPointer r0
    //     0xbada78: add             x0, x0, HEAP, lsl #32
    // 0xbada7c: r16 = Instance_Color
    //     0xbada7c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbada80: r30 = 12.000000
    //     0xbada80: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbada84: ldr             lr, [lr, #0x9e8]
    // 0xbada88: stp             lr, x16, [SP]
    // 0xbada8c: mov             x1, x0
    // 0xbada90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbada90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbada94: ldr             x4, [x4, #0x9b8]
    // 0xbada98: r0 = copyWith()
    //     0xbada98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbada9c: mov             x2, x0
    // 0xbadaa0: ldur            x0, [fp, #-0x28]
    // 0xbadaa4: ldur            x1, [fp, #-8]
    // 0xbadaa8: stur            x2, [fp, #-0x10]
    // 0xbadaac: r0 = Text()
    //     0xbadaac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbadab0: mov             x3, x0
    // 0xbadab4: ldur            x0, [fp, #-8]
    // 0xbadab8: stur            x3, [fp, #-0x20]
    // 0xbadabc: StoreField: r3->field_b = r0
    //     0xbadabc: stur            w0, [x3, #0xb]
    // 0xbadac0: ldur            x0, [fp, #-0x10]
    // 0xbadac4: StoreField: r3->field_13 = r0
    //     0xbadac4: stur            w0, [x3, #0x13]
    // 0xbadac8: r1 = Null
    //     0xbadac8: mov             x1, NULL
    // 0xbadacc: r2 = 6
    //     0xbadacc: movz            x2, #0x6
    // 0xbadad0: r0 = AllocateArray()
    //     0xbadad0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbadad4: mov             x2, x0
    // 0xbadad8: ldur            x0, [fp, #-0x28]
    // 0xbadadc: stur            x2, [fp, #-8]
    // 0xbadae0: StoreField: r2->field_f = r0
    //     0xbadae0: stur            w0, [x2, #0xf]
    // 0xbadae4: r16 = Instance_Spacer
    //     0xbadae4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbadae8: ldr             x16, [x16, #0xf0]
    // 0xbadaec: StoreField: r2->field_13 = r16
    //     0xbadaec: stur            w16, [x2, #0x13]
    // 0xbadaf0: ldur            x0, [fp, #-0x20]
    // 0xbadaf4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbadaf4: stur            w0, [x2, #0x17]
    // 0xbadaf8: r1 = <Widget>
    //     0xbadaf8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbadafc: r0 = AllocateGrowableArray()
    //     0xbadafc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbadb00: mov             x1, x0
    // 0xbadb04: ldur            x0, [fp, #-8]
    // 0xbadb08: stur            x1, [fp, #-0x10]
    // 0xbadb0c: StoreField: r1->field_f = r0
    //     0xbadb0c: stur            w0, [x1, #0xf]
    // 0xbadb10: r0 = 6
    //     0xbadb10: movz            x0, #0x6
    // 0xbadb14: StoreField: r1->field_b = r0
    //     0xbadb14: stur            w0, [x1, #0xb]
    // 0xbadb18: r0 = Row()
    //     0xbadb18: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbadb1c: r1 = Instance_Axis
    //     0xbadb1c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbadb20: StoreField: r0->field_f = r1
    //     0xbadb20: stur            w1, [x0, #0xf]
    // 0xbadb24: r1 = Instance_MainAxisAlignment
    //     0xbadb24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbadb28: ldr             x1, [x1, #0xa08]
    // 0xbadb2c: StoreField: r0->field_13 = r1
    //     0xbadb2c: stur            w1, [x0, #0x13]
    // 0xbadb30: r1 = Instance_MainAxisSize
    //     0xbadb30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbadb34: ldr             x1, [x1, #0xa10]
    // 0xbadb38: ArrayStore: r0[0] = r1  ; List_4
    //     0xbadb38: stur            w1, [x0, #0x17]
    // 0xbadb3c: r1 = Instance_CrossAxisAlignment
    //     0xbadb3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbadb40: ldr             x1, [x1, #0xa18]
    // 0xbadb44: StoreField: r0->field_1b = r1
    //     0xbadb44: stur            w1, [x0, #0x1b]
    // 0xbadb48: r1 = Instance_VerticalDirection
    //     0xbadb48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbadb4c: ldr             x1, [x1, #0xa20]
    // 0xbadb50: StoreField: r0->field_23 = r1
    //     0xbadb50: stur            w1, [x0, #0x23]
    // 0xbadb54: r1 = Instance_Clip
    //     0xbadb54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbadb58: ldr             x1, [x1, #0x38]
    // 0xbadb5c: StoreField: r0->field_2b = r1
    //     0xbadb5c: stur            w1, [x0, #0x2b]
    // 0xbadb60: StoreField: r0->field_2f = rZR
    //     0xbadb60: stur            xzr, [x0, #0x2f]
    // 0xbadb64: ldur            x1, [fp, #-0x10]
    // 0xbadb68: StoreField: r0->field_b = r1
    //     0xbadb68: stur            w1, [x0, #0xb]
    // 0xbadb6c: LeaveFrame
    //     0xbadb6c: mov             SP, fp
    //     0xbadb70: ldp             fp, lr, [SP], #0x10
    // 0xbadb74: ret
    //     0xbadb74: ret             
    // 0xbadb78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbadb78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbadb7c: b               #0xbad2e4
    // 0xbadb80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbadb80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbadb84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbadb84: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbadb88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbadb88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbadb8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbadb8c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbadb90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbadb90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbadb94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbadb94: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbadb98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbadb98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbadb9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbadb9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbadba0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbadba0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbadba4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbadba4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbadba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbadba8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbadbac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbadbac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4026, size: 0x14, field offset: 0xc
class BillDetailWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc800e0, size: 0x24
    // 0xc800e0: EnterFrame
    //     0xc800e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc800e4: mov             fp, SP
    // 0xc800e8: mov             x0, x1
    // 0xc800ec: r1 = <BillDetailWidget>
    //     0xc800ec: add             x1, PP, #0x48, lsl #12  ; [pp+0x486b8] TypeArguments: <BillDetailWidget>
    //     0xc800f0: ldr             x1, [x1, #0x6b8]
    // 0xc800f4: r0 = _BillDetailWidgetState()
    //     0xc800f4: bl              #0xc80104  ; Allocate_BillDetailWidgetStateStub -> _BillDetailWidgetState (size=0x14)
    // 0xc800f8: LeaveFrame
    //     0xc800f8: mov             SP, fp
    //     0xc800fc: ldp             fp, lr, [SP], #0x10
    // 0xc80100: ret
    //     0xc80100: ret             
  }
}
