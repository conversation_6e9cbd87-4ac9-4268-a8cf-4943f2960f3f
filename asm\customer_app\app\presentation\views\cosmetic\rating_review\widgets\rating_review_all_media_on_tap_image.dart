// lib: , url: package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart

// class id: 1049334, size: 0x8
class :: {
}

// class id: 3388, size: 0x40, field offset: 0x14
class _RatingReviewAllMediaOnTapImageState extends State<dynamic> {

  late PageController _pageController; // offset: 0x24

  _ initState(/* No info */) {
    // ** addr: 0x93cf2c, size: 0x18c
    // 0x93cf2c: EnterFrame
    //     0x93cf2c: stp             fp, lr, [SP, #-0x10]!
    //     0x93cf30: mov             fp, SP
    // 0x93cf34: AllocStack(0x18)
    //     0x93cf34: sub             SP, SP, #0x18
    // 0x93cf38: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */)
    //     0x93cf38: mov             x0, x1
    //     0x93cf3c: stur            x1, [fp, #-8]
    // 0x93cf40: CheckStackOverflow
    //     0x93cf40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93cf44: cmp             SP, x16
    //     0x93cf48: b.ls            #0x93d0a4
    // 0x93cf4c: LoadField: r1 = r0->field_b
    //     0x93cf4c: ldur            w1, [x0, #0xb]
    // 0x93cf50: DecompressPointer r1
    //     0x93cf50: add             x1, x1, HEAP, lsl #32
    // 0x93cf54: cmp             w1, NULL
    // 0x93cf58: b.eq            #0x93d0ac
    // 0x93cf5c: LoadField: r2 = r1->field_13
    //     0x93cf5c: ldur            x2, [x1, #0x13]
    // 0x93cf60: StoreField: r0->field_13 = r2
    //     0x93cf60: stur            x2, [x0, #0x13]
    // 0x93cf64: LoadField: r2 = r1->field_1b
    //     0x93cf64: ldur            w2, [x1, #0x1b]
    // 0x93cf68: DecompressPointer r2
    //     0x93cf68: add             x2, x2, HEAP, lsl #32
    // 0x93cf6c: cmp             w2, NULL
    // 0x93cf70: b.ne            #0x93cf7c
    // 0x93cf74: r1 = 0
    //     0x93cf74: movz            x1, #0
    // 0x93cf78: b               #0x93cf88
    // 0x93cf7c: r1 = LoadInt32Instr(r2)
    //     0x93cf7c: sbfx            x1, x2, #1, #0x1f
    //     0x93cf80: tbz             w2, #0, #0x93cf88
    //     0x93cf84: ldur            x1, [x2, #7]
    // 0x93cf88: StoreField: r0->field_1b = r1
    //     0x93cf88: stur            x1, [x0, #0x1b]
    // 0x93cf8c: mov             x1, x0
    // 0x93cf90: r0 = _flattenMediaList()
    //     0x93cf90: bl              #0x93d0dc  ; [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_flattenMediaList
    // 0x93cf94: ldur            x0, [fp, #-8]
    // 0x93cf98: LoadField: r1 = r0->field_b
    //     0x93cf98: ldur            w1, [x0, #0xb]
    // 0x93cf9c: DecompressPointer r1
    //     0x93cf9c: add             x1, x1, HEAP, lsl #32
    // 0x93cfa0: cmp             w1, NULL
    // 0x93cfa4: b.eq            #0x93d0b0
    // 0x93cfa8: LoadField: r2 = r1->field_13
    //     0x93cfa8: ldur            x2, [x1, #0x13]
    // 0x93cfac: LoadField: r3 = r1->field_1b
    //     0x93cfac: ldur            w3, [x1, #0x1b]
    // 0x93cfb0: DecompressPointer r3
    //     0x93cfb0: add             x3, x3, HEAP, lsl #32
    // 0x93cfb4: cmp             w3, NULL
    // 0x93cfb8: b.ne            #0x93cfc4
    // 0x93cfbc: r3 = 0
    //     0x93cfbc: movz            x3, #0
    // 0x93cfc0: b               #0x93cfd4
    // 0x93cfc4: r1 = LoadInt32Instr(r3)
    //     0x93cfc4: sbfx            x1, x3, #1, #0x1f
    //     0x93cfc8: tbz             w3, #0, #0x93cfd0
    //     0x93cfcc: ldur            x1, [x3, #7]
    // 0x93cfd0: mov             x3, x1
    // 0x93cfd4: mov             x1, x0
    // 0x93cfd8: r0 = _getGlobalIndex()
    //     0x93cfd8: bl              #0x935398  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_getGlobalIndex
    // 0x93cfdc: stur            x0, [fp, #-0x10]
    // 0x93cfe0: r0 = PageController()
    //     0x93cfe0: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x93cfe4: mov             x2, x0
    // 0x93cfe8: ldur            x0, [fp, #-0x10]
    // 0x93cfec: stur            x2, [fp, #-0x18]
    // 0x93cff0: StoreField: r2->field_3f = r0
    //     0x93cff0: stur            x0, [x2, #0x3f]
    // 0x93cff4: r0 = true
    //     0x93cff4: add             x0, NULL, #0x20  ; true
    // 0x93cff8: StoreField: r2->field_47 = r0
    //     0x93cff8: stur            w0, [x2, #0x47]
    // 0x93cffc: d0 = 1.000000
    //     0x93cffc: fmov            d0, #1.00000000
    // 0x93d000: StoreField: r2->field_4b = d0
    //     0x93d000: stur            d0, [x2, #0x4b]
    // 0x93d004: mov             x1, x2
    // 0x93d008: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x93d008: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x93d00c: r0 = ScrollController()
    //     0x93d00c: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x93d010: ldur            x0, [fp, #-0x18]
    // 0x93d014: ldur            x3, [fp, #-8]
    // 0x93d018: StoreField: r3->field_23 = r0
    //     0x93d018: stur            w0, [x3, #0x23]
    //     0x93d01c: ldurb           w16, [x3, #-1]
    //     0x93d020: ldurb           w17, [x0, #-1]
    //     0x93d024: and             x16, x17, x16, lsr #2
    //     0x93d028: tst             x16, HEAP, lsr #32
    //     0x93d02c: b.eq            #0x93d034
    //     0x93d030: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x93d034: LoadField: r0 = r3->field_27
    //     0x93d034: ldur            w0, [x3, #0x27]
    // 0x93d038: DecompressPointer r0
    //     0x93d038: add             x0, x0, HEAP, lsl #32
    // 0x93d03c: mov             x2, x3
    // 0x93d040: stur            x0, [fp, #-0x18]
    // 0x93d044: r1 = Function '_onCollapseChanged@1511364025':.
    //     0x93d044: add             x1, PP, #0x57, lsl #12  ; [pp+0x57658] AnonymousClosure: (0x93d3e0), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x93d418)
    //     0x93d048: ldr             x1, [x1, #0x658]
    // 0x93d04c: r0 = AllocateClosure()
    //     0x93d04c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d050: ldur            x1, [fp, #-0x18]
    // 0x93d054: mov             x2, x0
    // 0x93d058: r0 = addListener()
    //     0x93d058: bl              #0x7b8dac  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x93d05c: ldur            x1, [fp, #-8]
    // 0x93d060: LoadField: r2 = r1->field_b
    //     0x93d060: ldur            w2, [x1, #0xb]
    // 0x93d064: DecompressPointer r2
    //     0x93d064: add             x2, x2, HEAP, lsl #32
    // 0x93d068: cmp             w2, NULL
    // 0x93d06c: b.eq            #0x93d0b4
    // 0x93d070: LoadField: r0 = r2->field_23
    //     0x93d070: ldur            w0, [x2, #0x23]
    // 0x93d074: DecompressPointer r0
    //     0x93d074: add             x0, x0, HEAP, lsl #32
    // 0x93d078: StoreField: r1->field_2f = r0
    //     0x93d078: stur            w0, [x1, #0x2f]
    //     0x93d07c: ldurb           w16, [x1, #-1]
    //     0x93d080: ldurb           w17, [x0, #-1]
    //     0x93d084: and             x16, x17, x16, lsr #2
    //     0x93d088: tst             x16, HEAP, lsr #32
    //     0x93d08c: b.eq            #0x93d094
    //     0x93d090: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93d094: r0 = Null
    //     0x93d094: mov             x0, NULL
    // 0x93d098: LeaveFrame
    //     0x93d098: mov             SP, fp
    //     0x93d09c: ldp             fp, lr, [SP], #0x10
    // 0x93d0a0: ret
    //     0x93d0a0: ret             
    // 0x93d0a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d0a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d0a8: b               #0x93cf4c
    // 0x93d0ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d0ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93d0b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d0b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93d0b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d0b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _flattenMediaList(/* No info */) {
    // ** addr: 0x93d0dc, size: 0x304
    // 0x93d0dc: EnterFrame
    //     0x93d0dc: stp             fp, lr, [SP, #-0x10]!
    //     0x93d0e0: mov             fp, SP
    // 0x93d0e4: AllocStack(0x58)
    //     0x93d0e4: sub             SP, SP, #0x58
    // 0x93d0e8: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */)
    //     0x93d0e8: mov             x0, x1
    //     0x93d0ec: stur            x1, [fp, #-8]
    // 0x93d0f0: CheckStackOverflow
    //     0x93d0f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d0f4: cmp             SP, x16
    //     0x93d0f8: b.ls            #0x93d3c4
    // 0x93d0fc: LoadField: r1 = r0->field_37
    //     0x93d0fc: ldur            w1, [x0, #0x37]
    // 0x93d100: DecompressPointer r1
    //     0x93d100: add             x1, x1, HEAP, lsl #32
    // 0x93d104: r0 = clear()
    //     0x93d104: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x93d108: ldur            x0, [fp, #-8]
    // 0x93d10c: LoadField: r1 = r0->field_3b
    //     0x93d10c: ldur            w1, [x0, #0x3b]
    // 0x93d110: DecompressPointer r1
    //     0x93d110: add             x1, x1, HEAP, lsl #32
    // 0x93d114: r0 = clear()
    //     0x93d114: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x93d118: r4 = 0
    //     0x93d118: movz            x4, #0
    // 0x93d11c: ldur            x3, [fp, #-8]
    // 0x93d120: stur            x4, [fp, #-0x40]
    // 0x93d124: CheckStackOverflow
    //     0x93d124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d128: cmp             SP, x16
    //     0x93d12c: b.ls            #0x93d3cc
    // 0x93d130: LoadField: r0 = r3->field_b
    //     0x93d130: ldur            w0, [x3, #0xb]
    // 0x93d134: DecompressPointer r0
    //     0x93d134: add             x0, x0, HEAP, lsl #32
    // 0x93d138: cmp             w0, NULL
    // 0x93d13c: b.eq            #0x93d3d4
    // 0x93d140: LoadField: r1 = r0->field_f
    //     0x93d140: ldur            w1, [x0, #0xf]
    // 0x93d144: DecompressPointer r1
    //     0x93d144: add             x1, x1, HEAP, lsl #32
    // 0x93d148: LoadField: r0 = r1->field_b
    //     0x93d148: ldur            w0, [x1, #0xb]
    // 0x93d14c: r2 = LoadInt32Instr(r0)
    //     0x93d14c: sbfx            x2, x0, #1, #0x1f
    // 0x93d150: cmp             x4, x2
    // 0x93d154: b.ge            #0x93d3b4
    // 0x93d158: LoadField: r0 = r1->field_f
    //     0x93d158: ldur            w0, [x1, #0xf]
    // 0x93d15c: DecompressPointer r0
    //     0x93d15c: add             x0, x0, HEAP, lsl #32
    // 0x93d160: lsl             x5, x4, #1
    // 0x93d164: stur            x5, [fp, #-0x38]
    // 0x93d168: ArrayLoad: r6 = r0[r4]  ; Unknown_4
    //     0x93d168: add             x16, x0, x4, lsl #2
    //     0x93d16c: ldur            w6, [x16, #0xf]
    // 0x93d170: DecompressPointer r6
    //     0x93d170: add             x6, x6, HEAP, lsl #32
    // 0x93d174: stur            x6, [fp, #-0x30]
    // 0x93d178: r7 = 0
    //     0x93d178: movz            x7, #0
    // 0x93d17c: stur            x7, [fp, #-0x28]
    // 0x93d180: CheckStackOverflow
    //     0x93d180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d184: cmp             SP, x16
    //     0x93d188: b.ls            #0x93d3d8
    // 0x93d18c: LoadField: r0 = r6->field_1b
    //     0x93d18c: ldur            w0, [x6, #0x1b]
    // 0x93d190: DecompressPointer r0
    //     0x93d190: add             x0, x0, HEAP, lsl #32
    // 0x93d194: LoadField: r1 = r0->field_b
    //     0x93d194: ldur            w1, [x0, #0xb]
    // 0x93d198: r2 = LoadInt32Instr(r1)
    //     0x93d198: sbfx            x2, x1, #1, #0x1f
    // 0x93d19c: cmp             x7, x2
    // 0x93d1a0: b.ge            #0x93d3a8
    // 0x93d1a4: LoadField: r8 = r3->field_37
    //     0x93d1a4: ldur            w8, [x3, #0x37]
    // 0x93d1a8: DecompressPointer r8
    //     0x93d1a8: add             x8, x8, HEAP, lsl #32
    // 0x93d1ac: stur            x8, [fp, #-0x20]
    // 0x93d1b0: LoadField: r1 = r0->field_f
    //     0x93d1b0: ldur            w1, [x0, #0xf]
    // 0x93d1b4: DecompressPointer r1
    //     0x93d1b4: add             x1, x1, HEAP, lsl #32
    // 0x93d1b8: lsl             x9, x7, #1
    // 0x93d1bc: stur            x9, [fp, #-0x18]
    // 0x93d1c0: ArrayLoad: r10 = r1[r7]  ; Unknown_4
    //     0x93d1c0: add             x16, x1, x7, lsl #2
    //     0x93d1c4: ldur            w10, [x16, #0xf]
    // 0x93d1c8: DecompressPointer r10
    //     0x93d1c8: add             x10, x10, HEAP, lsl #32
    // 0x93d1cc: stur            x10, [fp, #-0x10]
    // 0x93d1d0: LoadField: r2 = r8->field_7
    //     0x93d1d0: ldur            w2, [x8, #7]
    // 0x93d1d4: DecompressPointer r2
    //     0x93d1d4: add             x2, x2, HEAP, lsl #32
    // 0x93d1d8: mov             x0, x10
    // 0x93d1dc: r1 = Null
    //     0x93d1dc: mov             x1, NULL
    // 0x93d1e0: cmp             w2, NULL
    // 0x93d1e4: b.eq            #0x93d204
    // 0x93d1e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93d1e8: ldur            w4, [x2, #0x17]
    // 0x93d1ec: DecompressPointer r4
    //     0x93d1ec: add             x4, x4, HEAP, lsl #32
    // 0x93d1f0: r8 = X0
    //     0x93d1f0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93d1f4: LoadField: r9 = r4->field_7
    //     0x93d1f4: ldur            x9, [x4, #7]
    // 0x93d1f8: r3 = Null
    //     0x93d1f8: add             x3, PP, #0x57, lsl #12  ; [pp+0x57668] Null
    //     0x93d1fc: ldr             x3, [x3, #0x668]
    // 0x93d200: blr             x9
    // 0x93d204: ldur            x0, [fp, #-0x20]
    // 0x93d208: LoadField: r1 = r0->field_b
    //     0x93d208: ldur            w1, [x0, #0xb]
    // 0x93d20c: LoadField: r2 = r0->field_f
    //     0x93d20c: ldur            w2, [x0, #0xf]
    // 0x93d210: DecompressPointer r2
    //     0x93d210: add             x2, x2, HEAP, lsl #32
    // 0x93d214: LoadField: r3 = r2->field_b
    //     0x93d214: ldur            w3, [x2, #0xb]
    // 0x93d218: r2 = LoadInt32Instr(r1)
    //     0x93d218: sbfx            x2, x1, #1, #0x1f
    // 0x93d21c: stur            x2, [fp, #-0x48]
    // 0x93d220: r1 = LoadInt32Instr(r3)
    //     0x93d220: sbfx            x1, x3, #1, #0x1f
    // 0x93d224: cmp             x2, x1
    // 0x93d228: b.ne            #0x93d234
    // 0x93d22c: mov             x1, x0
    // 0x93d230: r0 = _growToNextCapacity()
    //     0x93d230: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93d234: ldur            x3, [fp, #-8]
    // 0x93d238: ldur            x0, [fp, #-0x20]
    // 0x93d23c: ldur            x4, [fp, #-0x38]
    // 0x93d240: ldur            x5, [fp, #-0x18]
    // 0x93d244: ldur            x2, [fp, #-0x48]
    // 0x93d248: add             x1, x2, #1
    // 0x93d24c: lsl             x6, x1, #1
    // 0x93d250: StoreField: r0->field_b = r6
    //     0x93d250: stur            w6, [x0, #0xb]
    // 0x93d254: LoadField: r1 = r0->field_f
    //     0x93d254: ldur            w1, [x0, #0xf]
    // 0x93d258: DecompressPointer r1
    //     0x93d258: add             x1, x1, HEAP, lsl #32
    // 0x93d25c: ldur            x0, [fp, #-0x10]
    // 0x93d260: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93d260: add             x25, x1, x2, lsl #2
    //     0x93d264: add             x25, x25, #0xf
    //     0x93d268: str             w0, [x25]
    //     0x93d26c: tbz             w0, #0, #0x93d288
    //     0x93d270: ldurb           w16, [x1, #-1]
    //     0x93d274: ldurb           w17, [x0, #-1]
    //     0x93d278: and             x16, x17, x16, lsr #2
    //     0x93d27c: tst             x16, HEAP, lsr #32
    //     0x93d280: b.eq            #0x93d288
    //     0x93d284: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93d288: LoadField: r0 = r3->field_3b
    //     0x93d288: ldur            w0, [x3, #0x3b]
    // 0x93d28c: DecompressPointer r0
    //     0x93d28c: add             x0, x0, HEAP, lsl #32
    // 0x93d290: stur            x0, [fp, #-0x10]
    // 0x93d294: r1 = Null
    //     0x93d294: mov             x1, NULL
    // 0x93d298: r2 = 8
    //     0x93d298: movz            x2, #0x8
    // 0x93d29c: r0 = AllocateArray()
    //     0x93d29c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93d2a0: r16 = "parentIndex"
    //     0x93d2a0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52048] "parentIndex"
    //     0x93d2a4: ldr             x16, [x16, #0x48]
    // 0x93d2a8: StoreField: r0->field_f = r16
    //     0x93d2a8: stur            w16, [x0, #0xf]
    // 0x93d2ac: ldur            x1, [fp, #-0x38]
    // 0x93d2b0: StoreField: r0->field_13 = r1
    //     0x93d2b0: stur            w1, [x0, #0x13]
    // 0x93d2b4: r16 = "childIndex"
    //     0x93d2b4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52050] "childIndex"
    //     0x93d2b8: ldr             x16, [x16, #0x50]
    // 0x93d2bc: ArrayStore: r0[0] = r16  ; List_4
    //     0x93d2bc: stur            w16, [x0, #0x17]
    // 0x93d2c0: ldur            x2, [fp, #-0x18]
    // 0x93d2c4: StoreField: r0->field_1b = r2
    //     0x93d2c4: stur            w2, [x0, #0x1b]
    // 0x93d2c8: r16 = <String, int>
    //     0x93d2c8: ldr             x16, [PP, #0xd40]  ; [pp+0xd40] TypeArguments: <String, int>
    // 0x93d2cc: stp             x0, x16, [SP]
    // 0x93d2d0: r0 = Map._fromLiteral()
    //     0x93d2d0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x93d2d4: mov             x4, x0
    // 0x93d2d8: ldur            x3, [fp, #-0x10]
    // 0x93d2dc: stur            x4, [fp, #-0x18]
    // 0x93d2e0: LoadField: r2 = r3->field_7
    //     0x93d2e0: ldur            w2, [x3, #7]
    // 0x93d2e4: DecompressPointer r2
    //     0x93d2e4: add             x2, x2, HEAP, lsl #32
    // 0x93d2e8: mov             x0, x4
    // 0x93d2ec: r1 = Null
    //     0x93d2ec: mov             x1, NULL
    // 0x93d2f0: cmp             w2, NULL
    // 0x93d2f4: b.eq            #0x93d314
    // 0x93d2f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93d2f8: ldur            w4, [x2, #0x17]
    // 0x93d2fc: DecompressPointer r4
    //     0x93d2fc: add             x4, x4, HEAP, lsl #32
    // 0x93d300: r8 = X0
    //     0x93d300: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93d304: LoadField: r9 = r4->field_7
    //     0x93d304: ldur            x9, [x4, #7]
    // 0x93d308: r3 = Null
    //     0x93d308: add             x3, PP, #0x57, lsl #12  ; [pp+0x57678] Null
    //     0x93d30c: ldr             x3, [x3, #0x678]
    // 0x93d310: blr             x9
    // 0x93d314: ldur            x0, [fp, #-0x10]
    // 0x93d318: LoadField: r1 = r0->field_b
    //     0x93d318: ldur            w1, [x0, #0xb]
    // 0x93d31c: LoadField: r2 = r0->field_f
    //     0x93d31c: ldur            w2, [x0, #0xf]
    // 0x93d320: DecompressPointer r2
    //     0x93d320: add             x2, x2, HEAP, lsl #32
    // 0x93d324: LoadField: r3 = r2->field_b
    //     0x93d324: ldur            w3, [x2, #0xb]
    // 0x93d328: r2 = LoadInt32Instr(r1)
    //     0x93d328: sbfx            x2, x1, #1, #0x1f
    // 0x93d32c: stur            x2, [fp, #-0x48]
    // 0x93d330: r1 = LoadInt32Instr(r3)
    //     0x93d330: sbfx            x1, x3, #1, #0x1f
    // 0x93d334: cmp             x2, x1
    // 0x93d338: b.ne            #0x93d344
    // 0x93d33c: mov             x1, x0
    // 0x93d340: r0 = _growToNextCapacity()
    //     0x93d340: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93d344: ldur            x4, [fp, #-0x28]
    // 0x93d348: ldur            x2, [fp, #-0x10]
    // 0x93d34c: ldur            x3, [fp, #-0x48]
    // 0x93d350: add             x5, x3, #1
    // 0x93d354: lsl             x6, x5, #1
    // 0x93d358: StoreField: r2->field_b = r6
    //     0x93d358: stur            w6, [x2, #0xb]
    // 0x93d35c: LoadField: r1 = r2->field_f
    //     0x93d35c: ldur            w1, [x2, #0xf]
    // 0x93d360: DecompressPointer r1
    //     0x93d360: add             x1, x1, HEAP, lsl #32
    // 0x93d364: ldur            x0, [fp, #-0x18]
    // 0x93d368: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93d368: add             x25, x1, x3, lsl #2
    //     0x93d36c: add             x25, x25, #0xf
    //     0x93d370: str             w0, [x25]
    //     0x93d374: tbz             w0, #0, #0x93d390
    //     0x93d378: ldurb           w16, [x1, #-1]
    //     0x93d37c: ldurb           w17, [x0, #-1]
    //     0x93d380: and             x16, x17, x16, lsr #2
    //     0x93d384: tst             x16, HEAP, lsr #32
    //     0x93d388: b.eq            #0x93d390
    //     0x93d38c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93d390: add             x7, x4, #1
    // 0x93d394: ldur            x3, [fp, #-8]
    // 0x93d398: ldur            x4, [fp, #-0x40]
    // 0x93d39c: ldur            x5, [fp, #-0x38]
    // 0x93d3a0: ldur            x6, [fp, #-0x30]
    // 0x93d3a4: b               #0x93d17c
    // 0x93d3a8: mov             x1, x4
    // 0x93d3ac: add             x4, x1, #1
    // 0x93d3b0: b               #0x93d11c
    // 0x93d3b4: r0 = Null
    //     0x93d3b4: mov             x0, NULL
    // 0x93d3b8: LeaveFrame
    //     0x93d3b8: mov             SP, fp
    //     0x93d3bc: ldp             fp, lr, [SP], #0x10
    // 0x93d3c0: ret
    //     0x93d3c0: ret             
    // 0x93d3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d3c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d3c8: b               #0x93d0fc
    // 0x93d3cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d3cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d3d0: b               #0x93d130
    // 0x93d3d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d3d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93d3d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d3d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d3dc: b               #0x93d18c
  }
  [closure] void _onCollapseChanged(dynamic) {
    // ** addr: 0x93d3e0, size: 0x38
    // 0x93d3e0: EnterFrame
    //     0x93d3e0: stp             fp, lr, [SP, #-0x10]!
    //     0x93d3e4: mov             fp, SP
    // 0x93d3e8: ldr             x0, [fp, #0x10]
    // 0x93d3ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x93d3ec: ldur            w1, [x0, #0x17]
    // 0x93d3f0: DecompressPointer r1
    //     0x93d3f0: add             x1, x1, HEAP, lsl #32
    // 0x93d3f4: CheckStackOverflow
    //     0x93d3f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d3f8: cmp             SP, x16
    //     0x93d3fc: b.ls            #0x93d410
    // 0x93d400: r0 = _onCollapseChanged()
    //     0x93d400: bl              #0x93d418  ; [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged
    // 0x93d404: LeaveFrame
    //     0x93d404: mov             SP, fp
    //     0x93d408: ldp             fp, lr, [SP], #0x10
    // 0x93d40c: ret
    //     0x93d40c: ret             
    // 0x93d410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d414: b               #0x93d400
  }
  _ _onCollapseChanged(/* No info */) {
    // ** addr: 0x93d418, size: 0x64
    // 0x93d418: EnterFrame
    //     0x93d418: stp             fp, lr, [SP, #-0x10]!
    //     0x93d41c: mov             fp, SP
    // 0x93d420: AllocStack(0x8)
    //     0x93d420: sub             SP, SP, #8
    // 0x93d424: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r1, fp-0x8 */)
    //     0x93d424: stur            x1, [fp, #-8]
    // 0x93d428: CheckStackOverflow
    //     0x93d428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d42c: cmp             SP, x16
    //     0x93d430: b.ls            #0x93d474
    // 0x93d434: r1 = 1
    //     0x93d434: movz            x1, #0x1
    // 0x93d438: r0 = AllocateContext()
    //     0x93d438: bl              #0x16f6108  ; AllocateContextStub
    // 0x93d43c: mov             x1, x0
    // 0x93d440: ldur            x0, [fp, #-8]
    // 0x93d444: StoreField: r1->field_f = r0
    //     0x93d444: stur            w0, [x1, #0xf]
    // 0x93d448: mov             x2, x1
    // 0x93d44c: r1 = Function '<anonymous closure>':.
    //     0x93d44c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57660] AnonymousClosure: (0x9357e0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x935814)
    //     0x93d450: ldr             x1, [x1, #0x660]
    // 0x93d454: r0 = AllocateClosure()
    //     0x93d454: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d458: ldur            x1, [fp, #-8]
    // 0x93d45c: mov             x2, x0
    // 0x93d460: r0 = setState()
    //     0x93d460: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93d464: r0 = Null
    //     0x93d464: mov             x0, NULL
    // 0x93d468: LeaveFrame
    //     0x93d468: mov             SP, fp
    //     0x93d46c: ldp             fp, lr, [SP], #0x10
    // 0x93d470: ret
    //     0x93d470: ret             
    // 0x93d474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d474: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d478: b               #0x93d434
  }
  _ build(/* No info */) {
    // ** addr: 0xb21348, size: 0x2134
    // 0xb21348: EnterFrame
    //     0xb21348: stp             fp, lr, [SP, #-0x10]!
    //     0xb2134c: mov             fp, SP
    // 0xb21350: AllocStack(0x80)
    //     0xb21350: sub             SP, SP, #0x80
    // 0xb21354: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb21354: stur            x1, [fp, #-8]
    //     0xb21358: stur            x2, [fp, #-0x10]
    // 0xb2135c: CheckStackOverflow
    //     0xb2135c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb21360: cmp             SP, x16
    //     0xb21364: b.ls            #0xb233c8
    // 0xb21368: r1 = 2
    //     0xb21368: movz            x1, #0x2
    // 0xb2136c: r0 = AllocateContext()
    //     0xb2136c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb21370: mov             x3, x0
    // 0xb21374: ldur            x0, [fp, #-8]
    // 0xb21378: stur            x3, [fp, #-0x20]
    // 0xb2137c: StoreField: r3->field_f = r0
    //     0xb2137c: stur            w0, [x3, #0xf]
    // 0xb21380: ldur            x1, [fp, #-0x10]
    // 0xb21384: StoreField: r3->field_13 = r1
    //     0xb21384: stur            w1, [x3, #0x13]
    // 0xb21388: LoadField: r4 = r0->field_23
    //     0xb21388: ldur            w4, [x0, #0x23]
    // 0xb2138c: DecompressPointer r4
    //     0xb2138c: add             x4, x4, HEAP, lsl #32
    // 0xb21390: r16 = Sentinel
    //     0xb21390: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb21394: cmp             w4, w16
    // 0xb21398: b.eq            #0xb233d0
    // 0xb2139c: stur            x4, [fp, #-0x18]
    // 0xb213a0: LoadField: r1 = r0->field_37
    //     0xb213a0: ldur            w1, [x0, #0x37]
    // 0xb213a4: DecompressPointer r1
    //     0xb213a4: add             x1, x1, HEAP, lsl #32
    // 0xb213a8: LoadField: r5 = r1->field_b
    //     0xb213a8: ldur            w5, [x1, #0xb]
    // 0xb213ac: mov             x2, x3
    // 0xb213b0: stur            x5, [fp, #-0x10]
    // 0xb213b4: r1 = Function '<anonymous closure>':.
    //     0xb213b4: add             x1, PP, #0x57, lsl #12  ; [pp+0x575c0] AnonymousClosure: (0xb2451c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb21348)
    //     0xb213b8: ldr             x1, [x1, #0x5c0]
    // 0xb213bc: r0 = AllocateClosure()
    //     0xb213bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb213c0: ldur            x2, [fp, #-0x20]
    // 0xb213c4: r1 = Function '<anonymous closure>':.
    //     0xb213c4: add             x1, PP, #0x57, lsl #12  ; [pp+0x575c8] AnonymousClosure: (0xb23d84), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb21348)
    //     0xb213c8: ldr             x1, [x1, #0x5c8]
    // 0xb213cc: stur            x0, [fp, #-0x28]
    // 0xb213d0: r0 = AllocateClosure()
    //     0xb213d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb213d4: stur            x0, [fp, #-0x30]
    // 0xb213d8: r0 = PageView()
    //     0xb213d8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb213dc: stur            x0, [fp, #-0x38]
    // 0xb213e0: ldur            x16, [fp, #-0x18]
    // 0xb213e4: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb213e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb213e8: ldr             lr, [lr, #0x1c8]
    // 0xb213ec: stp             lr, x16, [SP]
    // 0xb213f0: mov             x1, x0
    // 0xb213f4: ldur            x2, [fp, #-0x30]
    // 0xb213f8: ldur            x3, [fp, #-0x10]
    // 0xb213fc: ldur            x5, [fp, #-0x28]
    // 0xb21400: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x4, physics, 0x5, null]
    //     0xb21400: add             x4, PP, #0x51, lsl #12  ; [pp+0x51f98] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x4, "physics", 0x5, Null]
    //     0xb21404: ldr             x4, [x4, #0xf98]
    // 0xb21408: r0 = PageView.builder()
    //     0xb21408: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb2140c: ldur            x3, [fp, #-8]
    // 0xb21410: LoadField: r0 = r3->field_b
    //     0xb21410: ldur            w0, [x3, #0xb]
    // 0xb21414: DecompressPointer r0
    //     0xb21414: add             x0, x0, HEAP, lsl #32
    // 0xb21418: cmp             w0, NULL
    // 0xb2141c: b.eq            #0xb233dc
    // 0xb21420: LoadField: r2 = r0->field_f
    //     0xb21420: ldur            w2, [x0, #0xf]
    // 0xb21424: DecompressPointer r2
    //     0xb21424: add             x2, x2, HEAP, lsl #32
    // 0xb21428: LoadField: r4 = r3->field_13
    //     0xb21428: ldur            x4, [x3, #0x13]
    // 0xb2142c: LoadField: r0 = r2->field_b
    //     0xb2142c: ldur            w0, [x2, #0xb]
    // 0xb21430: r1 = LoadInt32Instr(r0)
    //     0xb21430: sbfx            x1, x0, #1, #0x1f
    // 0xb21434: mov             x0, x1
    // 0xb21438: mov             x1, x4
    // 0xb2143c: cmp             x1, x0
    // 0xb21440: b.hs            #0xb233e0
    // 0xb21444: LoadField: r0 = r2->field_f
    //     0xb21444: ldur            w0, [x2, #0xf]
    // 0xb21448: DecompressPointer r0
    //     0xb21448: add             x0, x0, HEAP, lsl #32
    // 0xb2144c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb2144c: add             x16, x0, x4, lsl #2
    //     0xb21450: ldur            w1, [x16, #0xf]
    // 0xb21454: DecompressPointer r1
    //     0xb21454: add             x1, x1, HEAP, lsl #32
    // 0xb21458: LoadField: r0 = r1->field_1b
    //     0xb21458: ldur            w0, [x1, #0x1b]
    // 0xb2145c: DecompressPointer r0
    //     0xb2145c: add             x0, x0, HEAP, lsl #32
    // 0xb21460: LoadField: r4 = r0->field_b
    //     0xb21460: ldur            w4, [x0, #0xb]
    // 0xb21464: ldur            x2, [fp, #-0x20]
    // 0xb21468: stur            x4, [fp, #-0x10]
    // 0xb2146c: r1 = Function '<anonymous closure>':.
    //     0xb2146c: add             x1, PP, #0x57, lsl #12  ; [pp+0x575d0] AnonymousClosure: (0xaa55c4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb21470: ldr             x1, [x1, #0x5d0]
    // 0xb21474: r0 = AllocateClosure()
    //     0xb21474: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb21478: mov             x3, x0
    // 0xb2147c: ldur            x0, [fp, #-0x10]
    // 0xb21480: stur            x3, [fp, #-0x18]
    // 0xb21484: r2 = LoadInt32Instr(r0)
    //     0xb21484: sbfx            x2, x0, #1, #0x1f
    // 0xb21488: r1 = <Widget>
    //     0xb21488: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2148c: r0 = _GrowableList()
    //     0xb2148c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb21490: mov             x1, x0
    // 0xb21494: stur            x1, [fp, #-0x10]
    // 0xb21498: r2 = 0
    //     0xb21498: movz            x2, #0
    // 0xb2149c: stur            x2, [fp, #-0x40]
    // 0xb214a0: CheckStackOverflow
    //     0xb214a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb214a4: cmp             SP, x16
    //     0xb214a8: b.ls            #0xb233e4
    // 0xb214ac: LoadField: r0 = r1->field_b
    //     0xb214ac: ldur            w0, [x1, #0xb]
    // 0xb214b0: r3 = LoadInt32Instr(r0)
    //     0xb214b0: sbfx            x3, x0, #1, #0x1f
    // 0xb214b4: cmp             x2, x3
    // 0xb214b8: b.ge            #0xb2157c
    // 0xb214bc: lsl             x0, x2, #1
    // 0xb214c0: ldur            x16, [fp, #-0x18]
    // 0xb214c4: stp             x0, x16, [SP]
    // 0xb214c8: ldur            x0, [fp, #-0x18]
    // 0xb214cc: ClosureCall
    //     0xb214cc: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb214d0: ldur            x2, [x0, #0x1f]
    //     0xb214d4: blr             x2
    // 0xb214d8: mov             x3, x0
    // 0xb214dc: r2 = Null
    //     0xb214dc: mov             x2, NULL
    // 0xb214e0: r1 = Null
    //     0xb214e0: mov             x1, NULL
    // 0xb214e4: stur            x3, [fp, #-0x28]
    // 0xb214e8: r4 = 60
    //     0xb214e8: movz            x4, #0x3c
    // 0xb214ec: branchIfSmi(r0, 0xb214f8)
    //     0xb214ec: tbz             w0, #0, #0xb214f8
    // 0xb214f0: r4 = LoadClassIdInstr(r0)
    //     0xb214f0: ldur            x4, [x0, #-1]
    //     0xb214f4: ubfx            x4, x4, #0xc, #0x14
    // 0xb214f8: sub             x4, x4, #0xe60
    // 0xb214fc: cmp             x4, #0x464
    // 0xb21500: b.ls            #0xb21518
    // 0xb21504: r8 = Widget
    //     0xb21504: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xb21508: ldr             x8, [x8, #0xe68]
    // 0xb2150c: r3 = Null
    //     0xb2150c: add             x3, PP, #0x57, lsl #12  ; [pp+0x575d8] Null
    //     0xb21510: ldr             x3, [x3, #0x5d8]
    // 0xb21514: r0 = Widget()
    //     0xb21514: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xb21518: ldur            x3, [fp, #-0x10]
    // 0xb2151c: LoadField: r0 = r3->field_b
    //     0xb2151c: ldur            w0, [x3, #0xb]
    // 0xb21520: r1 = LoadInt32Instr(r0)
    //     0xb21520: sbfx            x1, x0, #1, #0x1f
    // 0xb21524: mov             x0, x1
    // 0xb21528: ldur            x1, [fp, #-0x40]
    // 0xb2152c: cmp             x1, x0
    // 0xb21530: b.hs            #0xb233ec
    // 0xb21534: LoadField: r1 = r3->field_f
    //     0xb21534: ldur            w1, [x3, #0xf]
    // 0xb21538: DecompressPointer r1
    //     0xb21538: add             x1, x1, HEAP, lsl #32
    // 0xb2153c: ldur            x0, [fp, #-0x28]
    // 0xb21540: ldur            x2, [fp, #-0x40]
    // 0xb21544: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb21544: add             x25, x1, x2, lsl #2
    //     0xb21548: add             x25, x25, #0xf
    //     0xb2154c: str             w0, [x25]
    //     0xb21550: tbz             w0, #0, #0xb2156c
    //     0xb21554: ldurb           w16, [x1, #-1]
    //     0xb21558: ldurb           w17, [x0, #-1]
    //     0xb2155c: and             x16, x17, x16, lsr #2
    //     0xb21560: tst             x16, HEAP, lsr #32
    //     0xb21564: b.eq            #0xb2156c
    //     0xb21568: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2156c: add             x0, x2, #1
    // 0xb21570: mov             x2, x0
    // 0xb21574: mov             x1, x3
    // 0xb21578: b               #0xb2149c
    // 0xb2157c: ldur            x0, [fp, #-8]
    // 0xb21580: mov             x3, x1
    // 0xb21584: ldur            x1, [fp, #-0x38]
    // 0xb21588: r0 = Row()
    //     0xb21588: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb2158c: mov             x2, x0
    // 0xb21590: r0 = Instance_Axis
    //     0xb21590: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb21594: stur            x2, [fp, #-0x18]
    // 0xb21598: StoreField: r2->field_f = r0
    //     0xb21598: stur            w0, [x2, #0xf]
    // 0xb2159c: r1 = Instance_MainAxisAlignment
    //     0xb2159c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb215a0: ldr             x1, [x1, #0xab0]
    // 0xb215a4: StoreField: r2->field_13 = r1
    //     0xb215a4: stur            w1, [x2, #0x13]
    // 0xb215a8: r3 = Instance_MainAxisSize
    //     0xb215a8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb215ac: ldr             x3, [x3, #0xa10]
    // 0xb215b0: ArrayStore: r2[0] = r3  ; List_4
    //     0xb215b0: stur            w3, [x2, #0x17]
    // 0xb215b4: r4 = Instance_CrossAxisAlignment
    //     0xb215b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb215b8: ldr             x4, [x4, #0xa18]
    // 0xb215bc: StoreField: r2->field_1b = r4
    //     0xb215bc: stur            w4, [x2, #0x1b]
    // 0xb215c0: r5 = Instance_VerticalDirection
    //     0xb215c0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb215c4: ldr             x5, [x5, #0xa20]
    // 0xb215c8: StoreField: r2->field_23 = r5
    //     0xb215c8: stur            w5, [x2, #0x23]
    // 0xb215cc: r6 = Instance_Clip
    //     0xb215cc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb215d0: ldr             x6, [x6, #0x38]
    // 0xb215d4: StoreField: r2->field_2b = r6
    //     0xb215d4: stur            w6, [x2, #0x2b]
    // 0xb215d8: StoreField: r2->field_2f = rZR
    //     0xb215d8: stur            xzr, [x2, #0x2f]
    // 0xb215dc: ldur            x1, [fp, #-0x10]
    // 0xb215e0: StoreField: r2->field_b = r1
    //     0xb215e0: stur            w1, [x2, #0xb]
    // 0xb215e4: r1 = <StackParentData>
    //     0xb215e4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb215e8: ldr             x1, [x1, #0x8e0]
    // 0xb215ec: r0 = Positioned()
    //     0xb215ec: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb215f0: mov             x3, x0
    // 0xb215f4: r0 = 12.000000
    //     0xb215f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb215f8: ldr             x0, [x0, #0x9e8]
    // 0xb215fc: stur            x3, [fp, #-0x10]
    // 0xb21600: StoreField: r3->field_13 = r0
    //     0xb21600: stur            w0, [x3, #0x13]
    // 0xb21604: ArrayStore: r3[0] = r0  ; List_4
    //     0xb21604: stur            w0, [x3, #0x17]
    // 0xb21608: ldur            x1, [fp, #-0x18]
    // 0xb2160c: StoreField: r3->field_b = r1
    //     0xb2160c: stur            w1, [x3, #0xb]
    // 0xb21610: r1 = Null
    //     0xb21610: mov             x1, NULL
    // 0xb21614: r2 = 4
    //     0xb21614: movz            x2, #0x4
    // 0xb21618: r0 = AllocateArray()
    //     0xb21618: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2161c: mov             x2, x0
    // 0xb21620: ldur            x0, [fp, #-0x38]
    // 0xb21624: stur            x2, [fp, #-0x18]
    // 0xb21628: StoreField: r2->field_f = r0
    //     0xb21628: stur            w0, [x2, #0xf]
    // 0xb2162c: ldur            x0, [fp, #-0x10]
    // 0xb21630: StoreField: r2->field_13 = r0
    //     0xb21630: stur            w0, [x2, #0x13]
    // 0xb21634: r1 = <Widget>
    //     0xb21634: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb21638: r0 = AllocateGrowableArray()
    //     0xb21638: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2163c: mov             x1, x0
    // 0xb21640: ldur            x0, [fp, #-0x18]
    // 0xb21644: stur            x1, [fp, #-0x10]
    // 0xb21648: StoreField: r1->field_f = r0
    //     0xb21648: stur            w0, [x1, #0xf]
    // 0xb2164c: r2 = 4
    //     0xb2164c: movz            x2, #0x4
    // 0xb21650: StoreField: r1->field_b = r2
    //     0xb21650: stur            w2, [x1, #0xb]
    // 0xb21654: ldur            x3, [fp, #-8]
    // 0xb21658: LoadField: r0 = r3->field_b
    //     0xb21658: ldur            w0, [x3, #0xb]
    // 0xb2165c: DecompressPointer r0
    //     0xb2165c: add             x0, x0, HEAP, lsl #32
    // 0xb21660: cmp             w0, NULL
    // 0xb21664: b.eq            #0xb233f0
    // 0xb21668: LoadField: r4 = r0->field_b
    //     0xb21668: ldur            w4, [x0, #0xb]
    // 0xb2166c: DecompressPointer r4
    //     0xb2166c: add             x4, x4, HEAP, lsl #32
    // 0xb21670: r0 = LoadClassIdInstr(r4)
    //     0xb21670: ldur            x0, [x4, #-1]
    //     0xb21674: ubfx            x0, x0, #0xc, #0x14
    // 0xb21678: r16 = "direct_image"
    //     0xb21678: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xb2167c: ldr             x16, [x16, #0xc98]
    // 0xb21680: stp             x16, x4, [SP]
    // 0xb21684: mov             lr, x0
    // 0xb21688: ldr             lr, [x21, lr, lsl #3]
    // 0xb2168c: blr             lr
    // 0xb21690: tbz             w0, #4, #0xb217a4
    // 0xb21694: ldur            x1, [fp, #-0x10]
    // 0xb21698: r0 = InkWell()
    //     0xb21698: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb2169c: mov             x3, x0
    // 0xb216a0: r0 = Instance_Icon
    //     0xb216a0: add             x0, PP, #0x51, lsl #12  ; [pp+0x51fb8] Obj!Icon@d665b1
    //     0xb216a4: ldr             x0, [x0, #0xfb8]
    // 0xb216a8: stur            x3, [fp, #-0x18]
    // 0xb216ac: StoreField: r3->field_b = r0
    //     0xb216ac: stur            w0, [x3, #0xb]
    // 0xb216b0: r1 = Function '<anonymous closure>':.
    //     0xb216b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x575e8] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb216b4: ldr             x1, [x1, #0x5e8]
    // 0xb216b8: r2 = Null
    //     0xb216b8: mov             x2, NULL
    // 0xb216bc: r0 = AllocateClosure()
    //     0xb216bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb216c0: mov             x1, x0
    // 0xb216c4: ldur            x0, [fp, #-0x18]
    // 0xb216c8: StoreField: r0->field_f = r1
    //     0xb216c8: stur            w1, [x0, #0xf]
    // 0xb216cc: r2 = true
    //     0xb216cc: add             x2, NULL, #0x20  ; true
    // 0xb216d0: StoreField: r0->field_43 = r2
    //     0xb216d0: stur            w2, [x0, #0x43]
    // 0xb216d4: r3 = Instance_BoxShape
    //     0xb216d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb216d8: ldr             x3, [x3, #0x80]
    // 0xb216dc: StoreField: r0->field_47 = r3
    //     0xb216dc: stur            w3, [x0, #0x47]
    // 0xb216e0: StoreField: r0->field_6f = r2
    //     0xb216e0: stur            w2, [x0, #0x6f]
    // 0xb216e4: r4 = false
    //     0xb216e4: add             x4, NULL, #0x30  ; false
    // 0xb216e8: StoreField: r0->field_73 = r4
    //     0xb216e8: stur            w4, [x0, #0x73]
    // 0xb216ec: StoreField: r0->field_83 = r2
    //     0xb216ec: stur            w2, [x0, #0x83]
    // 0xb216f0: StoreField: r0->field_7b = r4
    //     0xb216f0: stur            w4, [x0, #0x7b]
    // 0xb216f4: r1 = <StackParentData>
    //     0xb216f4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb216f8: ldr             x1, [x1, #0x8e0]
    // 0xb216fc: r0 = Positioned()
    //     0xb216fc: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb21700: mov             x2, x0
    // 0xb21704: r0 = 12.000000
    //     0xb21704: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb21708: ldr             x0, [x0, #0x9e8]
    // 0xb2170c: stur            x2, [fp, #-0x28]
    // 0xb21710: StoreField: r2->field_13 = r0
    //     0xb21710: stur            w0, [x2, #0x13]
    // 0xb21714: r3 = 24.000000
    //     0xb21714: add             x3, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb21718: ldr             x3, [x3, #0xba8]
    // 0xb2171c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb2171c: stur            w3, [x2, #0x17]
    // 0xb21720: ldur            x1, [fp, #-0x18]
    // 0xb21724: StoreField: r2->field_b = r1
    //     0xb21724: stur            w1, [x2, #0xb]
    // 0xb21728: ldur            x4, [fp, #-0x10]
    // 0xb2172c: LoadField: r1 = r4->field_b
    //     0xb2172c: ldur            w1, [x4, #0xb]
    // 0xb21730: LoadField: r5 = r4->field_f
    //     0xb21730: ldur            w5, [x4, #0xf]
    // 0xb21734: DecompressPointer r5
    //     0xb21734: add             x5, x5, HEAP, lsl #32
    // 0xb21738: LoadField: r6 = r5->field_b
    //     0xb21738: ldur            w6, [x5, #0xb]
    // 0xb2173c: r5 = LoadInt32Instr(r1)
    //     0xb2173c: sbfx            x5, x1, #1, #0x1f
    // 0xb21740: stur            x5, [fp, #-0x40]
    // 0xb21744: r1 = LoadInt32Instr(r6)
    //     0xb21744: sbfx            x1, x6, #1, #0x1f
    // 0xb21748: cmp             x5, x1
    // 0xb2174c: b.ne            #0xb21758
    // 0xb21750: mov             x1, x4
    // 0xb21754: r0 = _growToNextCapacity()
    //     0xb21754: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb21758: ldur            x2, [fp, #-0x10]
    // 0xb2175c: ldur            x3, [fp, #-0x40]
    // 0xb21760: add             x0, x3, #1
    // 0xb21764: lsl             x1, x0, #1
    // 0xb21768: StoreField: r2->field_b = r1
    //     0xb21768: stur            w1, [x2, #0xb]
    // 0xb2176c: LoadField: r1 = r2->field_f
    //     0xb2176c: ldur            w1, [x2, #0xf]
    // 0xb21770: DecompressPointer r1
    //     0xb21770: add             x1, x1, HEAP, lsl #32
    // 0xb21774: ldur            x0, [fp, #-0x28]
    // 0xb21778: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb21778: add             x25, x1, x3, lsl #2
    //     0xb2177c: add             x25, x25, #0xf
    //     0xb21780: str             w0, [x25]
    //     0xb21784: tbz             w0, #0, #0xb217a0
    //     0xb21788: ldurb           w16, [x1, #-1]
    //     0xb2178c: ldurb           w17, [x0, #-1]
    //     0xb21790: and             x16, x17, x16, lsr #2
    //     0xb21794: tst             x16, HEAP, lsr #32
    //     0xb21798: b.eq            #0xb217a0
    //     0xb2179c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb217a0: b               #0xb217a8
    // 0xb217a4: ldur            x2, [fp, #-0x10]
    // 0xb217a8: r0 = InkWell()
    //     0xb217a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb217ac: mov             x3, x0
    // 0xb217b0: r0 = Instance_Icon
    //     0xb217b0: add             x0, PP, #0x51, lsl #12  ; [pp+0x51fc8] Obj!Icon@d66571
    //     0xb217b4: ldr             x0, [x0, #0xfc8]
    // 0xb217b8: stur            x3, [fp, #-0x18]
    // 0xb217bc: StoreField: r3->field_b = r0
    //     0xb217bc: stur            w0, [x3, #0xb]
    // 0xb217c0: ldur            x2, [fp, #-0x20]
    // 0xb217c4: r1 = Function '<anonymous closure>':.
    //     0xb217c4: add             x1, PP, #0x57, lsl #12  ; [pp+0x575f0] AnonymousClosure: (0xaa54dc), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb217c8: ldr             x1, [x1, #0x5f0]
    // 0xb217cc: r0 = AllocateClosure()
    //     0xb217cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb217d0: mov             x1, x0
    // 0xb217d4: ldur            x0, [fp, #-0x18]
    // 0xb217d8: StoreField: r0->field_f = r1
    //     0xb217d8: stur            w1, [x0, #0xf]
    // 0xb217dc: r2 = true
    //     0xb217dc: add             x2, NULL, #0x20  ; true
    // 0xb217e0: StoreField: r0->field_43 = r2
    //     0xb217e0: stur            w2, [x0, #0x43]
    // 0xb217e4: r3 = Instance_BoxShape
    //     0xb217e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb217e8: ldr             x3, [x3, #0x80]
    // 0xb217ec: StoreField: r0->field_47 = r3
    //     0xb217ec: stur            w3, [x0, #0x47]
    // 0xb217f0: StoreField: r0->field_6f = r2
    //     0xb217f0: stur            w2, [x0, #0x6f]
    // 0xb217f4: r4 = false
    //     0xb217f4: add             x4, NULL, #0x30  ; false
    // 0xb217f8: StoreField: r0->field_73 = r4
    //     0xb217f8: stur            w4, [x0, #0x73]
    // 0xb217fc: StoreField: r0->field_83 = r2
    //     0xb217fc: stur            w2, [x0, #0x83]
    // 0xb21800: StoreField: r0->field_7b = r4
    //     0xb21800: stur            w4, [x0, #0x7b]
    // 0xb21804: r1 = <StackParentData>
    //     0xb21804: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb21808: ldr             x1, [x1, #0x8e0]
    // 0xb2180c: r0 = Positioned()
    //     0xb2180c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb21810: mov             x2, x0
    // 0xb21814: r0 = 24.000000
    //     0xb21814: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb21818: ldr             x0, [x0, #0xba8]
    // 0xb2181c: stur            x2, [fp, #-0x28]
    // 0xb21820: ArrayStore: r2[0] = r0  ; List_4
    //     0xb21820: stur            w0, [x2, #0x17]
    // 0xb21824: r0 = 12.000000
    //     0xb21824: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb21828: ldr             x0, [x0, #0x9e8]
    // 0xb2182c: StoreField: r2->field_1b = r0
    //     0xb2182c: stur            w0, [x2, #0x1b]
    // 0xb21830: ldur            x0, [fp, #-0x18]
    // 0xb21834: StoreField: r2->field_b = r0
    //     0xb21834: stur            w0, [x2, #0xb]
    // 0xb21838: ldur            x0, [fp, #-0x10]
    // 0xb2183c: LoadField: r1 = r0->field_b
    //     0xb2183c: ldur            w1, [x0, #0xb]
    // 0xb21840: LoadField: r3 = r0->field_f
    //     0xb21840: ldur            w3, [x0, #0xf]
    // 0xb21844: DecompressPointer r3
    //     0xb21844: add             x3, x3, HEAP, lsl #32
    // 0xb21848: LoadField: r4 = r3->field_b
    //     0xb21848: ldur            w4, [x3, #0xb]
    // 0xb2184c: r3 = LoadInt32Instr(r1)
    //     0xb2184c: sbfx            x3, x1, #1, #0x1f
    // 0xb21850: stur            x3, [fp, #-0x40]
    // 0xb21854: r1 = LoadInt32Instr(r4)
    //     0xb21854: sbfx            x1, x4, #1, #0x1f
    // 0xb21858: cmp             x3, x1
    // 0xb2185c: b.ne            #0xb21868
    // 0xb21860: mov             x1, x0
    // 0xb21864: r0 = _growToNextCapacity()
    //     0xb21864: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb21868: ldur            x4, [fp, #-8]
    // 0xb2186c: ldur            x2, [fp, #-0x10]
    // 0xb21870: ldur            x3, [fp, #-0x40]
    // 0xb21874: add             x0, x3, #1
    // 0xb21878: lsl             x1, x0, #1
    // 0xb2187c: StoreField: r2->field_b = r1
    //     0xb2187c: stur            w1, [x2, #0xb]
    // 0xb21880: LoadField: r1 = r2->field_f
    //     0xb21880: ldur            w1, [x2, #0xf]
    // 0xb21884: DecompressPointer r1
    //     0xb21884: add             x1, x1, HEAP, lsl #32
    // 0xb21888: ldur            x0, [fp, #-0x28]
    // 0xb2188c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb2188c: add             x25, x1, x3, lsl #2
    //     0xb21890: add             x25, x25, #0xf
    //     0xb21894: str             w0, [x25]
    //     0xb21898: tbz             w0, #0, #0xb218b4
    //     0xb2189c: ldurb           w16, [x1, #-1]
    //     0xb218a0: ldurb           w17, [x0, #-1]
    //     0xb218a4: and             x16, x17, x16, lsr #2
    //     0xb218a8: tst             x16, HEAP, lsr #32
    //     0xb218ac: b.eq            #0xb218b4
    //     0xb218b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb218b4: LoadField: r0 = r4->field_2b
    //     0xb218b4: ldur            w0, [x4, #0x2b]
    // 0xb218b8: DecompressPointer r0
    //     0xb218b8: add             x0, x0, HEAP, lsl #32
    // 0xb218bc: tbnz            w0, #4, #0xb223d8
    // 0xb218c0: ldur            x0, [fp, #-0x20]
    // 0xb218c4: LoadField: r1 = r0->field_13
    //     0xb218c4: ldur            w1, [x0, #0x13]
    // 0xb218c8: DecompressPointer r1
    //     0xb218c8: add             x1, x1, HEAP, lsl #32
    // 0xb218cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb218cc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb218d0: r0 = _of()
    //     0xb218d0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb218d4: LoadField: r1 = r0->field_7
    //     0xb218d4: ldur            w1, [x0, #7]
    // 0xb218d8: DecompressPointer r1
    //     0xb218d8: add             x1, x1, HEAP, lsl #32
    // 0xb218dc: LoadField: d1 = r1->field_7
    //     0xb218dc: ldur            d1, [x1, #7]
    // 0xb218e0: stur            d1, [fp, #-0x58]
    // 0xb218e4: r1 = Instance_Color
    //     0xb218e4: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb218e8: d0 = 0.700000
    //     0xb218e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb218ec: ldr             d0, [x17, #0xf48]
    // 0xb218f0: r0 = withOpacity()
    //     0xb218f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb218f4: stur            x0, [fp, #-0x18]
    // 0xb218f8: r0 = BoxDecoration()
    //     0xb218f8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb218fc: mov             x2, x0
    // 0xb21900: ldur            x0, [fp, #-0x18]
    // 0xb21904: stur            x2, [fp, #-0x28]
    // 0xb21908: StoreField: r2->field_7 = r0
    //     0xb21908: stur            w0, [x2, #7]
    // 0xb2190c: r0 = Instance_BorderRadius
    //     0xb2190c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] Obj!BorderRadius@d5a201
    //     0xb21910: ldr             x0, [x0, #0xbf8]
    // 0xb21914: StoreField: r2->field_13 = r0
    //     0xb21914: stur            w0, [x2, #0x13]
    // 0xb21918: r0 = Instance_BoxShape
    //     0xb21918: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2191c: ldr             x0, [x0, #0x80]
    // 0xb21920: StoreField: r2->field_23 = r0
    //     0xb21920: stur            w0, [x2, #0x23]
    // 0xb21924: r1 = Instance_Color
    //     0xb21924: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb21928: d0 = 0.500000
    //     0xb21928: fmov            d0, #0.50000000
    // 0xb2192c: r0 = withOpacity()
    //     0xb2192c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb21930: stur            x0, [fp, #-0x18]
    // 0xb21934: r0 = BoxDecoration()
    //     0xb21934: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb21938: mov             x2, x0
    // 0xb2193c: ldur            x0, [fp, #-0x18]
    // 0xb21940: stur            x2, [fp, #-0x30]
    // 0xb21944: StoreField: r2->field_7 = r0
    //     0xb21944: stur            w0, [x2, #7]
    // 0xb21948: r3 = Instance_BoxShape
    //     0xb21948: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb2194c: ldr             x3, [x3, #0x970]
    // 0xb21950: StoreField: r2->field_23 = r3
    //     0xb21950: stur            w3, [x2, #0x23]
    // 0xb21954: ldur            x4, [fp, #-8]
    // 0xb21958: LoadField: r0 = r4->field_b
    //     0xb21958: ldur            w0, [x4, #0xb]
    // 0xb2195c: DecompressPointer r0
    //     0xb2195c: add             x0, x0, HEAP, lsl #32
    // 0xb21960: cmp             w0, NULL
    // 0xb21964: b.eq            #0xb233f4
    // 0xb21968: LoadField: r5 = r0->field_f
    //     0xb21968: ldur            w5, [x0, #0xf]
    // 0xb2196c: DecompressPointer r5
    //     0xb2196c: add             x5, x5, HEAP, lsl #32
    // 0xb21970: LoadField: r6 = r4->field_13
    //     0xb21970: ldur            x6, [x4, #0x13]
    // 0xb21974: LoadField: r0 = r5->field_b
    //     0xb21974: ldur            w0, [x5, #0xb]
    // 0xb21978: r1 = LoadInt32Instr(r0)
    //     0xb21978: sbfx            x1, x0, #1, #0x1f
    // 0xb2197c: mov             x0, x1
    // 0xb21980: mov             x1, x6
    // 0xb21984: cmp             x1, x0
    // 0xb21988: b.hs            #0xb233f8
    // 0xb2198c: LoadField: r0 = r5->field_f
    //     0xb2198c: ldur            w0, [x5, #0xf]
    // 0xb21990: DecompressPointer r0
    //     0xb21990: add             x0, x0, HEAP, lsl #32
    // 0xb21994: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb21994: add             x16, x0, x6, lsl #2
    //     0xb21998: ldur            w1, [x16, #0xf]
    // 0xb2199c: DecompressPointer r1
    //     0xb2199c: add             x1, x1, HEAP, lsl #32
    // 0xb219a0: LoadField: r0 = r1->field_7
    //     0xb219a0: ldur            w0, [x1, #7]
    // 0xb219a4: DecompressPointer r0
    //     0xb219a4: add             x0, x0, HEAP, lsl #32
    // 0xb219a8: cmp             w0, NULL
    // 0xb219ac: b.ne            #0xb219b8
    // 0xb219b0: r0 = Null
    //     0xb219b0: mov             x0, NULL
    // 0xb219b4: b               #0xb219dc
    // 0xb219b8: stp             xzr, x0, [SP]
    // 0xb219bc: r0 = []()
    //     0xb219bc: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb219c0: r1 = LoadClassIdInstr(r0)
    //     0xb219c0: ldur            x1, [x0, #-1]
    //     0xb219c4: ubfx            x1, x1, #0xc, #0x14
    // 0xb219c8: str             x0, [SP]
    // 0xb219cc: mov             x0, x1
    // 0xb219d0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb219d0: sub             lr, x0, #1, lsl #12
    //     0xb219d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb219d8: blr             lr
    // 0xb219dc: cmp             w0, NULL
    // 0xb219e0: b.ne            #0xb219ec
    // 0xb219e4: r3 = ""
    //     0xb219e4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb219e8: b               #0xb219f0
    // 0xb219ec: mov             x3, x0
    // 0xb219f0: ldur            x0, [fp, #-8]
    // 0xb219f4: ldur            x2, [fp, #-0x20]
    // 0xb219f8: stur            x3, [fp, #-0x18]
    // 0xb219fc: LoadField: r1 = r2->field_13
    //     0xb219fc: ldur            w1, [x2, #0x13]
    // 0xb21a00: DecompressPointer r1
    //     0xb21a00: add             x1, x1, HEAP, lsl #32
    // 0xb21a04: r0 = of()
    //     0xb21a04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb21a08: LoadField: r1 = r0->field_87
    //     0xb21a08: ldur            w1, [x0, #0x87]
    // 0xb21a0c: DecompressPointer r1
    //     0xb21a0c: add             x1, x1, HEAP, lsl #32
    // 0xb21a10: LoadField: r0 = r1->field_7
    //     0xb21a10: ldur            w0, [x1, #7]
    // 0xb21a14: DecompressPointer r0
    //     0xb21a14: add             x0, x0, HEAP, lsl #32
    // 0xb21a18: r16 = 16.000000
    //     0xb21a18: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb21a1c: ldr             x16, [x16, #0x188]
    // 0xb21a20: r30 = Instance_Color
    //     0xb21a20: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb21a24: stp             lr, x16, [SP]
    // 0xb21a28: mov             x1, x0
    // 0xb21a2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb21a2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb21a30: ldr             x4, [x4, #0xaa0]
    // 0xb21a34: r0 = copyWith()
    //     0xb21a34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb21a38: stur            x0, [fp, #-0x38]
    // 0xb21a3c: r0 = Text()
    //     0xb21a3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb21a40: mov             x1, x0
    // 0xb21a44: ldur            x0, [fp, #-0x18]
    // 0xb21a48: stur            x1, [fp, #-0x48]
    // 0xb21a4c: StoreField: r1->field_b = r0
    //     0xb21a4c: stur            w0, [x1, #0xb]
    // 0xb21a50: ldur            x0, [fp, #-0x38]
    // 0xb21a54: StoreField: r1->field_13 = r0
    //     0xb21a54: stur            w0, [x1, #0x13]
    // 0xb21a58: r0 = Center()
    //     0xb21a58: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb21a5c: mov             x1, x0
    // 0xb21a60: r0 = Instance_Alignment
    //     0xb21a60: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb21a64: ldr             x0, [x0, #0xb10]
    // 0xb21a68: stur            x1, [fp, #-0x18]
    // 0xb21a6c: StoreField: r1->field_f = r0
    //     0xb21a6c: stur            w0, [x1, #0xf]
    // 0xb21a70: ldur            x2, [fp, #-0x48]
    // 0xb21a74: StoreField: r1->field_b = r2
    //     0xb21a74: stur            w2, [x1, #0xb]
    // 0xb21a78: r0 = Container()
    //     0xb21a78: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb21a7c: stur            x0, [fp, #-0x38]
    // 0xb21a80: r16 = 34.000000
    //     0xb21a80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb21a84: ldr             x16, [x16, #0x978]
    // 0xb21a88: r30 = 34.000000
    //     0xb21a88: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb21a8c: ldr             lr, [lr, #0x978]
    // 0xb21a90: stp             lr, x16, [SP, #0x18]
    // 0xb21a94: r16 = Instance_EdgeInsets
    //     0xb21a94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb21a98: ldr             x16, [x16, #0x980]
    // 0xb21a9c: ldur            lr, [fp, #-0x30]
    // 0xb21aa0: stp             lr, x16, [SP, #8]
    // 0xb21aa4: ldur            x16, [fp, #-0x18]
    // 0xb21aa8: str             x16, [SP]
    // 0xb21aac: mov             x1, x0
    // 0xb21ab0: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb21ab0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb21ab4: ldr             x4, [x4, #0x988]
    // 0xb21ab8: r0 = Container()
    //     0xb21ab8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb21abc: ldur            x2, [fp, #-8]
    // 0xb21ac0: LoadField: r0 = r2->field_b
    //     0xb21ac0: ldur            w0, [x2, #0xb]
    // 0xb21ac4: DecompressPointer r0
    //     0xb21ac4: add             x0, x0, HEAP, lsl #32
    // 0xb21ac8: cmp             w0, NULL
    // 0xb21acc: b.eq            #0xb233fc
    // 0xb21ad0: LoadField: r3 = r0->field_f
    //     0xb21ad0: ldur            w3, [x0, #0xf]
    // 0xb21ad4: DecompressPointer r3
    //     0xb21ad4: add             x3, x3, HEAP, lsl #32
    // 0xb21ad8: LoadField: r4 = r2->field_13
    //     0xb21ad8: ldur            x4, [x2, #0x13]
    // 0xb21adc: LoadField: r0 = r3->field_b
    //     0xb21adc: ldur            w0, [x3, #0xb]
    // 0xb21ae0: r1 = LoadInt32Instr(r0)
    //     0xb21ae0: sbfx            x1, x0, #1, #0x1f
    // 0xb21ae4: mov             x0, x1
    // 0xb21ae8: mov             x1, x4
    // 0xb21aec: cmp             x1, x0
    // 0xb21af0: b.hs            #0xb23400
    // 0xb21af4: LoadField: r0 = r3->field_f
    //     0xb21af4: ldur            w0, [x3, #0xf]
    // 0xb21af8: DecompressPointer r0
    //     0xb21af8: add             x0, x0, HEAP, lsl #32
    // 0xb21afc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb21afc: add             x16, x0, x4, lsl #2
    //     0xb21b00: ldur            w1, [x16, #0xf]
    // 0xb21b04: DecompressPointer r1
    //     0xb21b04: add             x1, x1, HEAP, lsl #32
    // 0xb21b08: LoadField: r0 = r1->field_7
    //     0xb21b08: ldur            w0, [x1, #7]
    // 0xb21b0c: DecompressPointer r0
    //     0xb21b0c: add             x0, x0, HEAP, lsl #32
    // 0xb21b10: cmp             w0, NULL
    // 0xb21b14: b.ne            #0xb21b20
    // 0xb21b18: r3 = ""
    //     0xb21b18: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb21b1c: b               #0xb21b24
    // 0xb21b20: mov             x3, x0
    // 0xb21b24: ldur            x0, [fp, #-0x20]
    // 0xb21b28: stur            x3, [fp, #-0x18]
    // 0xb21b2c: LoadField: r1 = r0->field_13
    //     0xb21b2c: ldur            w1, [x0, #0x13]
    // 0xb21b30: DecompressPointer r1
    //     0xb21b30: add             x1, x1, HEAP, lsl #32
    // 0xb21b34: r0 = of()
    //     0xb21b34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb21b38: LoadField: r1 = r0->field_87
    //     0xb21b38: ldur            w1, [x0, #0x87]
    // 0xb21b3c: DecompressPointer r1
    //     0xb21b3c: add             x1, x1, HEAP, lsl #32
    // 0xb21b40: LoadField: r0 = r1->field_7
    //     0xb21b40: ldur            w0, [x1, #7]
    // 0xb21b44: DecompressPointer r0
    //     0xb21b44: add             x0, x0, HEAP, lsl #32
    // 0xb21b48: r16 = 14.000000
    //     0xb21b48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb21b4c: ldr             x16, [x16, #0x1d8]
    // 0xb21b50: r30 = Instance_Color
    //     0xb21b50: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb21b54: stp             lr, x16, [SP]
    // 0xb21b58: mov             x1, x0
    // 0xb21b5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb21b5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb21b60: ldr             x4, [x4, #0xaa0]
    // 0xb21b64: r0 = copyWith()
    //     0xb21b64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb21b68: stur            x0, [fp, #-0x30]
    // 0xb21b6c: r0 = Text()
    //     0xb21b6c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb21b70: mov             x2, x0
    // 0xb21b74: ldur            x0, [fp, #-0x18]
    // 0xb21b78: stur            x2, [fp, #-0x48]
    // 0xb21b7c: StoreField: r2->field_b = r0
    //     0xb21b7c: stur            w0, [x2, #0xb]
    // 0xb21b80: ldur            x0, [fp, #-0x30]
    // 0xb21b84: StoreField: r2->field_13 = r0
    //     0xb21b84: stur            w0, [x2, #0x13]
    // 0xb21b88: ldur            x3, [fp, #-8]
    // 0xb21b8c: LoadField: r0 = r3->field_b
    //     0xb21b8c: ldur            w0, [x3, #0xb]
    // 0xb21b90: DecompressPointer r0
    //     0xb21b90: add             x0, x0, HEAP, lsl #32
    // 0xb21b94: cmp             w0, NULL
    // 0xb21b98: b.eq            #0xb23404
    // 0xb21b9c: LoadField: r4 = r0->field_f
    //     0xb21b9c: ldur            w4, [x0, #0xf]
    // 0xb21ba0: DecompressPointer r4
    //     0xb21ba0: add             x4, x4, HEAP, lsl #32
    // 0xb21ba4: LoadField: r5 = r3->field_13
    //     0xb21ba4: ldur            x5, [x3, #0x13]
    // 0xb21ba8: LoadField: r0 = r4->field_b
    //     0xb21ba8: ldur            w0, [x4, #0xb]
    // 0xb21bac: r1 = LoadInt32Instr(r0)
    //     0xb21bac: sbfx            x1, x0, #1, #0x1f
    // 0xb21bb0: mov             x0, x1
    // 0xb21bb4: mov             x1, x5
    // 0xb21bb8: cmp             x1, x0
    // 0xb21bbc: b.hs            #0xb23408
    // 0xb21bc0: LoadField: r0 = r4->field_f
    //     0xb21bc0: ldur            w0, [x4, #0xf]
    // 0xb21bc4: DecompressPointer r0
    //     0xb21bc4: add             x0, x0, HEAP, lsl #32
    // 0xb21bc8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb21bc8: add             x16, x0, x5, lsl #2
    //     0xb21bcc: ldur            w1, [x16, #0xf]
    // 0xb21bd0: DecompressPointer r1
    //     0xb21bd0: add             x1, x1, HEAP, lsl #32
    // 0xb21bd4: LoadField: r0 = r1->field_1f
    //     0xb21bd4: ldur            w0, [x1, #0x1f]
    // 0xb21bd8: DecompressPointer r0
    //     0xb21bd8: add             x0, x0, HEAP, lsl #32
    // 0xb21bdc: cmp             w0, NULL
    // 0xb21be0: b.ne            #0xb21bec
    // 0xb21be4: r4 = ""
    //     0xb21be4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb21be8: b               #0xb21bf0
    // 0xb21bec: mov             x4, x0
    // 0xb21bf0: ldur            x0, [fp, #-0x20]
    // 0xb21bf4: stur            x4, [fp, #-0x18]
    // 0xb21bf8: LoadField: r1 = r0->field_13
    //     0xb21bf8: ldur            w1, [x0, #0x13]
    // 0xb21bfc: DecompressPointer r1
    //     0xb21bfc: add             x1, x1, HEAP, lsl #32
    // 0xb21c00: r0 = of()
    //     0xb21c00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb21c04: LoadField: r1 = r0->field_87
    //     0xb21c04: ldur            w1, [x0, #0x87]
    // 0xb21c08: DecompressPointer r1
    //     0xb21c08: add             x1, x1, HEAP, lsl #32
    // 0xb21c0c: LoadField: r0 = r1->field_33
    //     0xb21c0c: ldur            w0, [x1, #0x33]
    // 0xb21c10: DecompressPointer r0
    //     0xb21c10: add             x0, x0, HEAP, lsl #32
    // 0xb21c14: stur            x0, [fp, #-0x30]
    // 0xb21c18: cmp             w0, NULL
    // 0xb21c1c: b.ne            #0xb21c28
    // 0xb21c20: r4 = Null
    //     0xb21c20: mov             x4, NULL
    // 0xb21c24: b               #0xb21c50
    // 0xb21c28: r1 = Instance_Color
    //     0xb21c28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb21c2c: d0 = 0.500000
    //     0xb21c2c: fmov            d0, #0.50000000
    // 0xb21c30: r0 = withOpacity()
    //     0xb21c30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb21c34: r16 = 10.000000
    //     0xb21c34: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb21c38: stp             x0, x16, [SP]
    // 0xb21c3c: ldur            x1, [fp, #-0x30]
    // 0xb21c40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb21c40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb21c44: ldr             x4, [x4, #0xaa0]
    // 0xb21c48: r0 = copyWith()
    //     0xb21c48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb21c4c: mov             x4, x0
    // 0xb21c50: ldur            x1, [fp, #-8]
    // 0xb21c54: ldur            x3, [fp, #-0x38]
    // 0xb21c58: ldur            x0, [fp, #-0x48]
    // 0xb21c5c: ldur            x2, [fp, #-0x18]
    // 0xb21c60: stur            x4, [fp, #-0x30]
    // 0xb21c64: r0 = Text()
    //     0xb21c64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb21c68: mov             x1, x0
    // 0xb21c6c: ldur            x0, [fp, #-0x18]
    // 0xb21c70: stur            x1, [fp, #-0x50]
    // 0xb21c74: StoreField: r1->field_b = r0
    //     0xb21c74: stur            w0, [x1, #0xb]
    // 0xb21c78: ldur            x0, [fp, #-0x30]
    // 0xb21c7c: StoreField: r1->field_13 = r0
    //     0xb21c7c: stur            w0, [x1, #0x13]
    // 0xb21c80: r0 = Padding()
    //     0xb21c80: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb21c84: mov             x3, x0
    // 0xb21c88: r0 = Instance_EdgeInsets
    //     0xb21c88: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb21c8c: ldr             x0, [x0, #0xe90]
    // 0xb21c90: stur            x3, [fp, #-0x18]
    // 0xb21c94: StoreField: r3->field_f = r0
    //     0xb21c94: stur            w0, [x3, #0xf]
    // 0xb21c98: ldur            x1, [fp, #-0x50]
    // 0xb21c9c: StoreField: r3->field_b = r1
    //     0xb21c9c: stur            w1, [x3, #0xb]
    // 0xb21ca0: r1 = Null
    //     0xb21ca0: mov             x1, NULL
    // 0xb21ca4: r2 = 4
    //     0xb21ca4: movz            x2, #0x4
    // 0xb21ca8: r0 = AllocateArray()
    //     0xb21ca8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb21cac: mov             x2, x0
    // 0xb21cb0: ldur            x0, [fp, #-0x48]
    // 0xb21cb4: stur            x2, [fp, #-0x30]
    // 0xb21cb8: StoreField: r2->field_f = r0
    //     0xb21cb8: stur            w0, [x2, #0xf]
    // 0xb21cbc: ldur            x0, [fp, #-0x18]
    // 0xb21cc0: StoreField: r2->field_13 = r0
    //     0xb21cc0: stur            w0, [x2, #0x13]
    // 0xb21cc4: r1 = <Widget>
    //     0xb21cc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb21cc8: r0 = AllocateGrowableArray()
    //     0xb21cc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb21ccc: mov             x1, x0
    // 0xb21cd0: ldur            x0, [fp, #-0x30]
    // 0xb21cd4: stur            x1, [fp, #-0x18]
    // 0xb21cd8: StoreField: r1->field_f = r0
    //     0xb21cd8: stur            w0, [x1, #0xf]
    // 0xb21cdc: r2 = 4
    //     0xb21cdc: movz            x2, #0x4
    // 0xb21ce0: StoreField: r1->field_b = r2
    //     0xb21ce0: stur            w2, [x1, #0xb]
    // 0xb21ce4: r0 = Column()
    //     0xb21ce4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb21ce8: mov             x3, x0
    // 0xb21cec: r0 = Instance_Axis
    //     0xb21cec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb21cf0: stur            x3, [fp, #-0x30]
    // 0xb21cf4: StoreField: r3->field_f = r0
    //     0xb21cf4: stur            w0, [x3, #0xf]
    // 0xb21cf8: r4 = Instance_MainAxisAlignment
    //     0xb21cf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb21cfc: ldr             x4, [x4, #0xa08]
    // 0xb21d00: StoreField: r3->field_13 = r4
    //     0xb21d00: stur            w4, [x3, #0x13]
    // 0xb21d04: r5 = Instance_MainAxisSize
    //     0xb21d04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb21d08: ldr             x5, [x5, #0xa10]
    // 0xb21d0c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb21d0c: stur            w5, [x3, #0x17]
    // 0xb21d10: r6 = Instance_CrossAxisAlignment
    //     0xb21d10: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb21d14: ldr             x6, [x6, #0x890]
    // 0xb21d18: StoreField: r3->field_1b = r6
    //     0xb21d18: stur            w6, [x3, #0x1b]
    // 0xb21d1c: r7 = Instance_VerticalDirection
    //     0xb21d1c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb21d20: ldr             x7, [x7, #0xa20]
    // 0xb21d24: StoreField: r3->field_23 = r7
    //     0xb21d24: stur            w7, [x3, #0x23]
    // 0xb21d28: r8 = Instance_Clip
    //     0xb21d28: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb21d2c: ldr             x8, [x8, #0x38]
    // 0xb21d30: StoreField: r3->field_2b = r8
    //     0xb21d30: stur            w8, [x3, #0x2b]
    // 0xb21d34: StoreField: r3->field_2f = rZR
    //     0xb21d34: stur            xzr, [x3, #0x2f]
    // 0xb21d38: ldur            x1, [fp, #-0x18]
    // 0xb21d3c: StoreField: r3->field_b = r1
    //     0xb21d3c: stur            w1, [x3, #0xb]
    // 0xb21d40: r1 = Null
    //     0xb21d40: mov             x1, NULL
    // 0xb21d44: r2 = 6
    //     0xb21d44: movz            x2, #0x6
    // 0xb21d48: r0 = AllocateArray()
    //     0xb21d48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb21d4c: mov             x2, x0
    // 0xb21d50: ldur            x0, [fp, #-0x38]
    // 0xb21d54: stur            x2, [fp, #-0x18]
    // 0xb21d58: StoreField: r2->field_f = r0
    //     0xb21d58: stur            w0, [x2, #0xf]
    // 0xb21d5c: r16 = Instance_SizedBox
    //     0xb21d5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb21d60: ldr             x16, [x16, #0x998]
    // 0xb21d64: StoreField: r2->field_13 = r16
    //     0xb21d64: stur            w16, [x2, #0x13]
    // 0xb21d68: ldur            x0, [fp, #-0x30]
    // 0xb21d6c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb21d6c: stur            w0, [x2, #0x17]
    // 0xb21d70: r1 = <Widget>
    //     0xb21d70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb21d74: r0 = AllocateGrowableArray()
    //     0xb21d74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb21d78: mov             x1, x0
    // 0xb21d7c: ldur            x0, [fp, #-0x18]
    // 0xb21d80: stur            x1, [fp, #-0x30]
    // 0xb21d84: StoreField: r1->field_f = r0
    //     0xb21d84: stur            w0, [x1, #0xf]
    // 0xb21d88: r2 = 6
    //     0xb21d88: movz            x2, #0x6
    // 0xb21d8c: StoreField: r1->field_b = r2
    //     0xb21d8c: stur            w2, [x1, #0xb]
    // 0xb21d90: r0 = Row()
    //     0xb21d90: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb21d94: mov             x3, x0
    // 0xb21d98: r2 = Instance_Axis
    //     0xb21d98: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb21d9c: stur            x3, [fp, #-0x18]
    // 0xb21da0: StoreField: r3->field_f = r2
    //     0xb21da0: stur            w2, [x3, #0xf]
    // 0xb21da4: r4 = Instance_MainAxisAlignment
    //     0xb21da4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb21da8: ldr             x4, [x4, #0xa08]
    // 0xb21dac: StoreField: r3->field_13 = r4
    //     0xb21dac: stur            w4, [x3, #0x13]
    // 0xb21db0: r5 = Instance_MainAxisSize
    //     0xb21db0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb21db4: ldr             x5, [x5, #0xa10]
    // 0xb21db8: ArrayStore: r3[0] = r5  ; List_4
    //     0xb21db8: stur            w5, [x3, #0x17]
    // 0xb21dbc: r6 = Instance_CrossAxisAlignment
    //     0xb21dbc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb21dc0: ldr             x6, [x6, #0xa18]
    // 0xb21dc4: StoreField: r3->field_1b = r6
    //     0xb21dc4: stur            w6, [x3, #0x1b]
    // 0xb21dc8: r7 = Instance_VerticalDirection
    //     0xb21dc8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb21dcc: ldr             x7, [x7, #0xa20]
    // 0xb21dd0: StoreField: r3->field_23 = r7
    //     0xb21dd0: stur            w7, [x3, #0x23]
    // 0xb21dd4: r8 = Instance_Clip
    //     0xb21dd4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb21dd8: ldr             x8, [x8, #0x38]
    // 0xb21ddc: StoreField: r3->field_2b = r8
    //     0xb21ddc: stur            w8, [x3, #0x2b]
    // 0xb21de0: StoreField: r3->field_2f = rZR
    //     0xb21de0: stur            xzr, [x3, #0x2f]
    // 0xb21de4: ldur            x0, [fp, #-0x30]
    // 0xb21de8: StoreField: r3->field_b = r0
    //     0xb21de8: stur            w0, [x3, #0xb]
    // 0xb21dec: ldur            x9, [fp, #-8]
    // 0xb21df0: LoadField: r0 = r9->field_b
    //     0xb21df0: ldur            w0, [x9, #0xb]
    // 0xb21df4: DecompressPointer r0
    //     0xb21df4: add             x0, x0, HEAP, lsl #32
    // 0xb21df8: cmp             w0, NULL
    // 0xb21dfc: b.eq            #0xb2340c
    // 0xb21e00: LoadField: r10 = r0->field_f
    //     0xb21e00: ldur            w10, [x0, #0xf]
    // 0xb21e04: DecompressPointer r10
    //     0xb21e04: add             x10, x10, HEAP, lsl #32
    // 0xb21e08: LoadField: r11 = r9->field_13
    //     0xb21e08: ldur            x11, [x9, #0x13]
    // 0xb21e0c: LoadField: r0 = r10->field_b
    //     0xb21e0c: ldur            w0, [x10, #0xb]
    // 0xb21e10: r1 = LoadInt32Instr(r0)
    //     0xb21e10: sbfx            x1, x0, #1, #0x1f
    // 0xb21e14: mov             x0, x1
    // 0xb21e18: mov             x1, x11
    // 0xb21e1c: cmp             x1, x0
    // 0xb21e20: b.hs            #0xb23410
    // 0xb21e24: LoadField: r0 = r10->field_f
    //     0xb21e24: ldur            w0, [x10, #0xf]
    // 0xb21e28: DecompressPointer r0
    //     0xb21e28: add             x0, x0, HEAP, lsl #32
    // 0xb21e2c: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xb21e2c: add             x16, x0, x11, lsl #2
    //     0xb21e30: ldur            w1, [x16, #0xf]
    // 0xb21e34: DecompressPointer r1
    //     0xb21e34: add             x1, x1, HEAP, lsl #32
    // 0xb21e38: LoadField: r0 = r1->field_f
    //     0xb21e38: ldur            w0, [x1, #0xf]
    // 0xb21e3c: DecompressPointer r0
    //     0xb21e3c: add             x0, x0, HEAP, lsl #32
    // 0xb21e40: r1 = 60
    //     0xb21e40: movz            x1, #0x3c
    // 0xb21e44: branchIfSmi(r0, 0xb21e50)
    //     0xb21e44: tbz             w0, #0, #0xb21e50
    // 0xb21e48: r1 = LoadClassIdInstr(r0)
    //     0xb21e48: ldur            x1, [x0, #-1]
    //     0xb21e4c: ubfx            x1, x1, #0xc, #0x14
    // 0xb21e50: str             x0, [SP]
    // 0xb21e54: mov             x0, x1
    // 0xb21e58: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb21e58: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb21e5c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb21e5c: movz            x17, #0x2700
    //     0xb21e60: add             lr, x0, x17
    //     0xb21e64: ldr             lr, [x21, lr, lsl #3]
    //     0xb21e68: blr             lr
    // 0xb21e6c: mov             x1, x0
    // 0xb21e70: r0 = parse()
    //     0xb21e70: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb21e74: mov             v1.16b, v0.16b
    // 0xb21e78: d0 = 4.000000
    //     0xb21e78: fmov            d0, #4.00000000
    // 0xb21e7c: fcmp            d1, d0
    // 0xb21e80: b.lt            #0xb21e94
    // 0xb21e84: r4 = Instance_Color
    //     0xb21e84: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb21e88: ldr             x4, [x4, #0x858]
    // 0xb21e8c: d0 = 2.000000
    //     0xb21e8c: fmov            d0, #2.00000000
    // 0xb21e90: b               #0xb21ff4
    // 0xb21e94: ldur            x2, [fp, #-8]
    // 0xb21e98: LoadField: r0 = r2->field_b
    //     0xb21e98: ldur            w0, [x2, #0xb]
    // 0xb21e9c: DecompressPointer r0
    //     0xb21e9c: add             x0, x0, HEAP, lsl #32
    // 0xb21ea0: cmp             w0, NULL
    // 0xb21ea4: b.eq            #0xb23414
    // 0xb21ea8: LoadField: r3 = r0->field_f
    //     0xb21ea8: ldur            w3, [x0, #0xf]
    // 0xb21eac: DecompressPointer r3
    //     0xb21eac: add             x3, x3, HEAP, lsl #32
    // 0xb21eb0: LoadField: r4 = r2->field_13
    //     0xb21eb0: ldur            x4, [x2, #0x13]
    // 0xb21eb4: LoadField: r0 = r3->field_b
    //     0xb21eb4: ldur            w0, [x3, #0xb]
    // 0xb21eb8: r1 = LoadInt32Instr(r0)
    //     0xb21eb8: sbfx            x1, x0, #1, #0x1f
    // 0xb21ebc: mov             x0, x1
    // 0xb21ec0: mov             x1, x4
    // 0xb21ec4: cmp             x1, x0
    // 0xb21ec8: b.hs            #0xb23418
    // 0xb21ecc: LoadField: r0 = r3->field_f
    //     0xb21ecc: ldur            w0, [x3, #0xf]
    // 0xb21ed0: DecompressPointer r0
    //     0xb21ed0: add             x0, x0, HEAP, lsl #32
    // 0xb21ed4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb21ed4: add             x16, x0, x4, lsl #2
    //     0xb21ed8: ldur            w1, [x16, #0xf]
    // 0xb21edc: DecompressPointer r1
    //     0xb21edc: add             x1, x1, HEAP, lsl #32
    // 0xb21ee0: LoadField: r0 = r1->field_f
    //     0xb21ee0: ldur            w0, [x1, #0xf]
    // 0xb21ee4: DecompressPointer r0
    //     0xb21ee4: add             x0, x0, HEAP, lsl #32
    // 0xb21ee8: r1 = 60
    //     0xb21ee8: movz            x1, #0x3c
    // 0xb21eec: branchIfSmi(r0, 0xb21ef8)
    //     0xb21eec: tbz             w0, #0, #0xb21ef8
    // 0xb21ef0: r1 = LoadClassIdInstr(r0)
    //     0xb21ef0: ldur            x1, [x0, #-1]
    //     0xb21ef4: ubfx            x1, x1, #0xc, #0x14
    // 0xb21ef8: str             x0, [SP]
    // 0xb21efc: mov             x0, x1
    // 0xb21f00: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb21f00: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb21f04: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb21f04: movz            x17, #0x2700
    //     0xb21f08: add             lr, x0, x17
    //     0xb21f0c: ldr             lr, [x21, lr, lsl #3]
    //     0xb21f10: blr             lr
    // 0xb21f14: mov             x1, x0
    // 0xb21f18: r0 = parse()
    //     0xb21f18: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb21f1c: d1 = 3.500000
    //     0xb21f1c: fmov            d1, #3.50000000
    // 0xb21f20: fcmp            d0, d1
    // 0xb21f24: b.lt            #0xb21f44
    // 0xb21f28: r1 = Instance_Color
    //     0xb21f28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb21f2c: ldr             x1, [x1, #0x858]
    // 0xb21f30: d0 = 0.700000
    //     0xb21f30: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb21f34: ldr             d0, [x17, #0xf48]
    // 0xb21f38: r0 = withOpacity()
    //     0xb21f38: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb21f3c: d0 = 2.000000
    //     0xb21f3c: fmov            d0, #2.00000000
    // 0xb21f40: b               #0xb21ff0
    // 0xb21f44: ldur            x2, [fp, #-8]
    // 0xb21f48: LoadField: r0 = r2->field_b
    //     0xb21f48: ldur            w0, [x2, #0xb]
    // 0xb21f4c: DecompressPointer r0
    //     0xb21f4c: add             x0, x0, HEAP, lsl #32
    // 0xb21f50: cmp             w0, NULL
    // 0xb21f54: b.eq            #0xb2341c
    // 0xb21f58: LoadField: r3 = r0->field_f
    //     0xb21f58: ldur            w3, [x0, #0xf]
    // 0xb21f5c: DecompressPointer r3
    //     0xb21f5c: add             x3, x3, HEAP, lsl #32
    // 0xb21f60: LoadField: r4 = r2->field_13
    //     0xb21f60: ldur            x4, [x2, #0x13]
    // 0xb21f64: LoadField: r0 = r3->field_b
    //     0xb21f64: ldur            w0, [x3, #0xb]
    // 0xb21f68: r1 = LoadInt32Instr(r0)
    //     0xb21f68: sbfx            x1, x0, #1, #0x1f
    // 0xb21f6c: mov             x0, x1
    // 0xb21f70: mov             x1, x4
    // 0xb21f74: cmp             x1, x0
    // 0xb21f78: b.hs            #0xb23420
    // 0xb21f7c: LoadField: r0 = r3->field_f
    //     0xb21f7c: ldur            w0, [x3, #0xf]
    // 0xb21f80: DecompressPointer r0
    //     0xb21f80: add             x0, x0, HEAP, lsl #32
    // 0xb21f84: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb21f84: add             x16, x0, x4, lsl #2
    //     0xb21f88: ldur            w1, [x16, #0xf]
    // 0xb21f8c: DecompressPointer r1
    //     0xb21f8c: add             x1, x1, HEAP, lsl #32
    // 0xb21f90: LoadField: r0 = r1->field_f
    //     0xb21f90: ldur            w0, [x1, #0xf]
    // 0xb21f94: DecompressPointer r0
    //     0xb21f94: add             x0, x0, HEAP, lsl #32
    // 0xb21f98: r1 = 60
    //     0xb21f98: movz            x1, #0x3c
    // 0xb21f9c: branchIfSmi(r0, 0xb21fa8)
    //     0xb21f9c: tbz             w0, #0, #0xb21fa8
    // 0xb21fa0: r1 = LoadClassIdInstr(r0)
    //     0xb21fa0: ldur            x1, [x0, #-1]
    //     0xb21fa4: ubfx            x1, x1, #0xc, #0x14
    // 0xb21fa8: str             x0, [SP]
    // 0xb21fac: mov             x0, x1
    // 0xb21fb0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb21fb0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb21fb4: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb21fb4: movz            x17, #0x2700
    //     0xb21fb8: add             lr, x0, x17
    //     0xb21fbc: ldr             lr, [x21, lr, lsl #3]
    //     0xb21fc0: blr             lr
    // 0xb21fc4: mov             x1, x0
    // 0xb21fc8: r0 = parse()
    //     0xb21fc8: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb21fcc: mov             v1.16b, v0.16b
    // 0xb21fd0: d0 = 2.000000
    //     0xb21fd0: fmov            d0, #2.00000000
    // 0xb21fd4: fcmp            d1, d0
    // 0xb21fd8: b.lt            #0xb21fe8
    // 0xb21fdc: r0 = Instance_Color
    //     0xb21fdc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb21fe0: ldr             x0, [x0, #0x860]
    // 0xb21fe4: b               #0xb21ff0
    // 0xb21fe8: r0 = Instance_Color
    //     0xb21fe8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb21fec: ldr             x0, [x0, #0x50]
    // 0xb21ff0: mov             x4, x0
    // 0xb21ff4: ldur            x0, [fp, #-8]
    // 0xb21ff8: ldur            x2, [fp, #-0x20]
    // 0xb21ffc: ldur            x1, [fp, #-0x18]
    // 0xb22000: ldur            d1, [fp, #-0x58]
    // 0xb22004: ldur            x3, [fp, #-0x10]
    // 0xb22008: stur            x4, [fp, #-0x30]
    // 0xb2200c: r0 = ColorFilter()
    //     0xb2200c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb22010: mov             x1, x0
    // 0xb22014: ldur            x0, [fp, #-0x30]
    // 0xb22018: stur            x1, [fp, #-0x38]
    // 0xb2201c: StoreField: r1->field_7 = r0
    //     0xb2201c: stur            w0, [x1, #7]
    // 0xb22020: r0 = Instance_BlendMode
    //     0xb22020: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb22024: ldr             x0, [x0, #0xb30]
    // 0xb22028: StoreField: r1->field_b = r0
    //     0xb22028: stur            w0, [x1, #0xb]
    // 0xb2202c: r2 = 1
    //     0xb2202c: movz            x2, #0x1
    // 0xb22030: StoreField: r1->field_13 = r2
    //     0xb22030: stur            x2, [x1, #0x13]
    // 0xb22034: r0 = SvgPicture()
    //     0xb22034: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb22038: stur            x0, [fp, #-0x30]
    // 0xb2203c: ldur            x16, [fp, #-0x38]
    // 0xb22040: str             x16, [SP]
    // 0xb22044: mov             x1, x0
    // 0xb22048: r2 = "assets/images/green_star.svg"
    //     0xb22048: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb2204c: ldr             x2, [x2, #0x9a0]
    // 0xb22050: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb22050: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb22054: ldr             x4, [x4, #0xa38]
    // 0xb22058: r0 = SvgPicture.asset()
    //     0xb22058: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb2205c: ldur            x2, [fp, #-8]
    // 0xb22060: LoadField: r0 = r2->field_b
    //     0xb22060: ldur            w0, [x2, #0xb]
    // 0xb22064: DecompressPointer r0
    //     0xb22064: add             x0, x0, HEAP, lsl #32
    // 0xb22068: cmp             w0, NULL
    // 0xb2206c: b.eq            #0xb23424
    // 0xb22070: LoadField: r3 = r0->field_f
    //     0xb22070: ldur            w3, [x0, #0xf]
    // 0xb22074: DecompressPointer r3
    //     0xb22074: add             x3, x3, HEAP, lsl #32
    // 0xb22078: LoadField: r4 = r2->field_13
    //     0xb22078: ldur            x4, [x2, #0x13]
    // 0xb2207c: LoadField: r0 = r3->field_b
    //     0xb2207c: ldur            w0, [x3, #0xb]
    // 0xb22080: r1 = LoadInt32Instr(r0)
    //     0xb22080: sbfx            x1, x0, #1, #0x1f
    // 0xb22084: mov             x0, x1
    // 0xb22088: mov             x1, x4
    // 0xb2208c: cmp             x1, x0
    // 0xb22090: b.hs            #0xb23428
    // 0xb22094: LoadField: r0 = r3->field_f
    //     0xb22094: ldur            w0, [x3, #0xf]
    // 0xb22098: DecompressPointer r0
    //     0xb22098: add             x0, x0, HEAP, lsl #32
    // 0xb2209c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb2209c: add             x16, x0, x4, lsl #2
    //     0xb220a0: ldur            w1, [x16, #0xf]
    // 0xb220a4: DecompressPointer r1
    //     0xb220a4: add             x1, x1, HEAP, lsl #32
    // 0xb220a8: LoadField: r0 = r1->field_f
    //     0xb220a8: ldur            w0, [x1, #0xf]
    // 0xb220ac: DecompressPointer r0
    //     0xb220ac: add             x0, x0, HEAP, lsl #32
    // 0xb220b0: r1 = 60
    //     0xb220b0: movz            x1, #0x3c
    // 0xb220b4: branchIfSmi(r0, 0xb220c0)
    //     0xb220b4: tbz             w0, #0, #0xb220c0
    // 0xb220b8: r1 = LoadClassIdInstr(r0)
    //     0xb220b8: ldur            x1, [x0, #-1]
    //     0xb220bc: ubfx            x1, x1, #0xc, #0x14
    // 0xb220c0: str             x0, [SP]
    // 0xb220c4: mov             x0, x1
    // 0xb220c8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb220c8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb220cc: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb220cc: movz            x17, #0x2700
    //     0xb220d0: add             lr, x0, x17
    //     0xb220d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb220d8: blr             lr
    // 0xb220dc: ldur            x2, [fp, #-0x20]
    // 0xb220e0: stur            x0, [fp, #-0x38]
    // 0xb220e4: LoadField: r1 = r2->field_13
    //     0xb220e4: ldur            w1, [x2, #0x13]
    // 0xb220e8: DecompressPointer r1
    //     0xb220e8: add             x1, x1, HEAP, lsl #32
    // 0xb220ec: r0 = of()
    //     0xb220ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb220f0: LoadField: r1 = r0->field_87
    //     0xb220f0: ldur            w1, [x0, #0x87]
    // 0xb220f4: DecompressPointer r1
    //     0xb220f4: add             x1, x1, HEAP, lsl #32
    // 0xb220f8: LoadField: r0 = r1->field_7
    //     0xb220f8: ldur            w0, [x1, #7]
    // 0xb220fc: DecompressPointer r0
    //     0xb220fc: add             x0, x0, HEAP, lsl #32
    // 0xb22100: r16 = 12.000000
    //     0xb22100: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb22104: ldr             x16, [x16, #0x9e8]
    // 0xb22108: r30 = Instance_Color
    //     0xb22108: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb2210c: stp             lr, x16, [SP]
    // 0xb22110: mov             x1, x0
    // 0xb22114: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb22114: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb22118: ldr             x4, [x4, #0xaa0]
    // 0xb2211c: r0 = copyWith()
    //     0xb2211c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb22120: stur            x0, [fp, #-0x48]
    // 0xb22124: r0 = Text()
    //     0xb22124: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb22128: mov             x3, x0
    // 0xb2212c: ldur            x0, [fp, #-0x38]
    // 0xb22130: stur            x3, [fp, #-0x50]
    // 0xb22134: StoreField: r3->field_b = r0
    //     0xb22134: stur            w0, [x3, #0xb]
    // 0xb22138: ldur            x0, [fp, #-0x48]
    // 0xb2213c: StoreField: r3->field_13 = r0
    //     0xb2213c: stur            w0, [x3, #0x13]
    // 0xb22140: r1 = Null
    //     0xb22140: mov             x1, NULL
    // 0xb22144: r2 = 6
    //     0xb22144: movz            x2, #0x6
    // 0xb22148: r0 = AllocateArray()
    //     0xb22148: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2214c: mov             x2, x0
    // 0xb22150: ldur            x0, [fp, #-0x30]
    // 0xb22154: stur            x2, [fp, #-0x38]
    // 0xb22158: StoreField: r2->field_f = r0
    //     0xb22158: stur            w0, [x2, #0xf]
    // 0xb2215c: r16 = Instance_SizedBox
    //     0xb2215c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb22160: ldr             x16, [x16, #0xe98]
    // 0xb22164: StoreField: r2->field_13 = r16
    //     0xb22164: stur            w16, [x2, #0x13]
    // 0xb22168: ldur            x0, [fp, #-0x50]
    // 0xb2216c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2216c: stur            w0, [x2, #0x17]
    // 0xb22170: r1 = <Widget>
    //     0xb22170: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb22174: r0 = AllocateGrowableArray()
    //     0xb22174: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb22178: mov             x1, x0
    // 0xb2217c: ldur            x0, [fp, #-0x38]
    // 0xb22180: stur            x1, [fp, #-0x30]
    // 0xb22184: StoreField: r1->field_f = r0
    //     0xb22184: stur            w0, [x1, #0xf]
    // 0xb22188: r2 = 6
    //     0xb22188: movz            x2, #0x6
    // 0xb2218c: StoreField: r1->field_b = r2
    //     0xb2218c: stur            w2, [x1, #0xb]
    // 0xb22190: r0 = Row()
    //     0xb22190: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb22194: mov             x1, x0
    // 0xb22198: r0 = Instance_Axis
    //     0xb22198: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2219c: stur            x1, [fp, #-0x38]
    // 0xb221a0: StoreField: r1->field_f = r0
    //     0xb221a0: stur            w0, [x1, #0xf]
    // 0xb221a4: r2 = Instance_MainAxisAlignment
    //     0xb221a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb221a8: ldr             x2, [x2, #0xa08]
    // 0xb221ac: StoreField: r1->field_13 = r2
    //     0xb221ac: stur            w2, [x1, #0x13]
    // 0xb221b0: r3 = Instance_MainAxisSize
    //     0xb221b0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb221b4: ldr             x3, [x3, #0xa10]
    // 0xb221b8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb221b8: stur            w3, [x1, #0x17]
    // 0xb221bc: r4 = Instance_CrossAxisAlignment
    //     0xb221bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb221c0: ldr             x4, [x4, #0xa18]
    // 0xb221c4: StoreField: r1->field_1b = r4
    //     0xb221c4: stur            w4, [x1, #0x1b]
    // 0xb221c8: r5 = Instance_VerticalDirection
    //     0xb221c8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb221cc: ldr             x5, [x5, #0xa20]
    // 0xb221d0: StoreField: r1->field_23 = r5
    //     0xb221d0: stur            w5, [x1, #0x23]
    // 0xb221d4: r6 = Instance_Clip
    //     0xb221d4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb221d8: ldr             x6, [x6, #0x38]
    // 0xb221dc: StoreField: r1->field_2b = r6
    //     0xb221dc: stur            w6, [x1, #0x2b]
    // 0xb221e0: StoreField: r1->field_2f = rZR
    //     0xb221e0: stur            xzr, [x1, #0x2f]
    // 0xb221e4: ldur            x7, [fp, #-0x30]
    // 0xb221e8: StoreField: r1->field_b = r7
    //     0xb221e8: stur            w7, [x1, #0xb]
    // 0xb221ec: r0 = Align()
    //     0xb221ec: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb221f0: mov             x3, x0
    // 0xb221f4: r0 = Instance_Alignment
    //     0xb221f4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb221f8: ldr             x0, [x0, #0xa78]
    // 0xb221fc: stur            x3, [fp, #-0x30]
    // 0xb22200: StoreField: r3->field_f = r0
    //     0xb22200: stur            w0, [x3, #0xf]
    // 0xb22204: ldur            x1, [fp, #-0x38]
    // 0xb22208: StoreField: r3->field_b = r1
    //     0xb22208: stur            w1, [x3, #0xb]
    // 0xb2220c: r1 = Null
    //     0xb2220c: mov             x1, NULL
    // 0xb22210: r2 = 4
    //     0xb22210: movz            x2, #0x4
    // 0xb22214: r0 = AllocateArray()
    //     0xb22214: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb22218: mov             x2, x0
    // 0xb2221c: ldur            x0, [fp, #-0x18]
    // 0xb22220: stur            x2, [fp, #-0x38]
    // 0xb22224: StoreField: r2->field_f = r0
    //     0xb22224: stur            w0, [x2, #0xf]
    // 0xb22228: ldur            x0, [fp, #-0x30]
    // 0xb2222c: StoreField: r2->field_13 = r0
    //     0xb2222c: stur            w0, [x2, #0x13]
    // 0xb22230: r1 = <Widget>
    //     0xb22230: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb22234: r0 = AllocateGrowableArray()
    //     0xb22234: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb22238: mov             x1, x0
    // 0xb2223c: ldur            x0, [fp, #-0x38]
    // 0xb22240: stur            x1, [fp, #-0x18]
    // 0xb22244: StoreField: r1->field_f = r0
    //     0xb22244: stur            w0, [x1, #0xf]
    // 0xb22248: r2 = 4
    //     0xb22248: movz            x2, #0x4
    // 0xb2224c: StoreField: r1->field_b = r2
    //     0xb2224c: stur            w2, [x1, #0xb]
    // 0xb22250: r0 = Row()
    //     0xb22250: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb22254: mov             x1, x0
    // 0xb22258: r0 = Instance_Axis
    //     0xb22258: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2225c: stur            x1, [fp, #-0x30]
    // 0xb22260: StoreField: r1->field_f = r0
    //     0xb22260: stur            w0, [x1, #0xf]
    // 0xb22264: r2 = Instance_MainAxisAlignment
    //     0xb22264: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb22268: ldr             x2, [x2, #0xa8]
    // 0xb2226c: StoreField: r1->field_13 = r2
    //     0xb2226c: stur            w2, [x1, #0x13]
    // 0xb22270: r3 = Instance_MainAxisSize
    //     0xb22270: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb22274: ldr             x3, [x3, #0xa10]
    // 0xb22278: ArrayStore: r1[0] = r3  ; List_4
    //     0xb22278: stur            w3, [x1, #0x17]
    // 0xb2227c: r4 = Instance_CrossAxisAlignment
    //     0xb2227c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb22280: ldr             x4, [x4, #0xa18]
    // 0xb22284: StoreField: r1->field_1b = r4
    //     0xb22284: stur            w4, [x1, #0x1b]
    // 0xb22288: r5 = Instance_VerticalDirection
    //     0xb22288: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2228c: ldr             x5, [x5, #0xa20]
    // 0xb22290: StoreField: r1->field_23 = r5
    //     0xb22290: stur            w5, [x1, #0x23]
    // 0xb22294: r6 = Instance_Clip
    //     0xb22294: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb22298: ldr             x6, [x6, #0x38]
    // 0xb2229c: StoreField: r1->field_2b = r6
    //     0xb2229c: stur            w6, [x1, #0x2b]
    // 0xb222a0: StoreField: r1->field_2f = rZR
    //     0xb222a0: stur            xzr, [x1, #0x2f]
    // 0xb222a4: ldur            x7, [fp, #-0x18]
    // 0xb222a8: StoreField: r1->field_b = r7
    //     0xb222a8: stur            w7, [x1, #0xb]
    // 0xb222ac: r0 = Padding()
    //     0xb222ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb222b0: mov             x1, x0
    // 0xb222b4: r0 = Instance_EdgeInsets
    //     0xb222b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb222b8: ldr             x0, [x0, #0xd0]
    // 0xb222bc: stur            x1, [fp, #-0x18]
    // 0xb222c0: StoreField: r1->field_f = r0
    //     0xb222c0: stur            w0, [x1, #0xf]
    // 0xb222c4: ldur            x0, [fp, #-0x30]
    // 0xb222c8: StoreField: r1->field_b = r0
    //     0xb222c8: stur            w0, [x1, #0xb]
    // 0xb222cc: r0 = Container()
    //     0xb222cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb222d0: stur            x0, [fp, #-0x30]
    // 0xb222d4: ldur            x16, [fp, #-0x28]
    // 0xb222d8: ldur            lr, [fp, #-0x18]
    // 0xb222dc: stp             lr, x16, [SP]
    // 0xb222e0: mov             x1, x0
    // 0xb222e4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb222e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb222e8: ldr             x4, [x4, #0x88]
    // 0xb222ec: r0 = Container()
    //     0xb222ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb222f0: ldur            d0, [fp, #-0x58]
    // 0xb222f4: r0 = inline_Allocate_Double()
    //     0xb222f4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb222f8: add             x0, x0, #0x10
    //     0xb222fc: cmp             x1, x0
    //     0xb22300: b.ls            #0xb2342c
    //     0xb22304: str             x0, [THR, #0x50]  ; THR::top
    //     0xb22308: sub             x0, x0, #0xf
    //     0xb2230c: movz            x1, #0xe15c
    //     0xb22310: movk            x1, #0x3, lsl #16
    //     0xb22314: stur            x1, [x0, #-1]
    // 0xb22318: StoreField: r0->field_7 = d0
    //     0xb22318: stur            d0, [x0, #7]
    // 0xb2231c: stur            x0, [fp, #-0x18]
    // 0xb22320: r0 = SizedBox()
    //     0xb22320: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb22324: mov             x2, x0
    // 0xb22328: ldur            x0, [fp, #-0x18]
    // 0xb2232c: stur            x2, [fp, #-0x28]
    // 0xb22330: StoreField: r2->field_f = r0
    //     0xb22330: stur            w0, [x2, #0xf]
    // 0xb22334: ldur            x0, [fp, #-0x30]
    // 0xb22338: StoreField: r2->field_b = r0
    //     0xb22338: stur            w0, [x2, #0xb]
    // 0xb2233c: r1 = <StackParentData>
    //     0xb2233c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb22340: ldr             x1, [x1, #0x8e0]
    // 0xb22344: r0 = Positioned()
    //     0xb22344: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb22348: mov             x2, x0
    // 0xb2234c: r0 = 0.000000
    //     0xb2234c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb22350: stur            x2, [fp, #-0x18]
    // 0xb22354: StoreField: r2->field_1f = r0
    //     0xb22354: stur            w0, [x2, #0x1f]
    // 0xb22358: ldur            x0, [fp, #-0x28]
    // 0xb2235c: StoreField: r2->field_b = r0
    //     0xb2235c: stur            w0, [x2, #0xb]
    // 0xb22360: ldur            x0, [fp, #-0x10]
    // 0xb22364: LoadField: r1 = r0->field_b
    //     0xb22364: ldur            w1, [x0, #0xb]
    // 0xb22368: LoadField: r3 = r0->field_f
    //     0xb22368: ldur            w3, [x0, #0xf]
    // 0xb2236c: DecompressPointer r3
    //     0xb2236c: add             x3, x3, HEAP, lsl #32
    // 0xb22370: LoadField: r4 = r3->field_b
    //     0xb22370: ldur            w4, [x3, #0xb]
    // 0xb22374: r3 = LoadInt32Instr(r1)
    //     0xb22374: sbfx            x3, x1, #1, #0x1f
    // 0xb22378: stur            x3, [fp, #-0x40]
    // 0xb2237c: r1 = LoadInt32Instr(r4)
    //     0xb2237c: sbfx            x1, x4, #1, #0x1f
    // 0xb22380: cmp             x3, x1
    // 0xb22384: b.ne            #0xb22390
    // 0xb22388: mov             x1, x0
    // 0xb2238c: r0 = _growToNextCapacity()
    //     0xb2238c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb22390: ldur            x2, [fp, #-0x10]
    // 0xb22394: ldur            x3, [fp, #-0x40]
    // 0xb22398: add             x0, x3, #1
    // 0xb2239c: lsl             x1, x0, #1
    // 0xb223a0: StoreField: r2->field_b = r1
    //     0xb223a0: stur            w1, [x2, #0xb]
    // 0xb223a4: LoadField: r1 = r2->field_f
    //     0xb223a4: ldur            w1, [x2, #0xf]
    // 0xb223a8: DecompressPointer r1
    //     0xb223a8: add             x1, x1, HEAP, lsl #32
    // 0xb223ac: ldur            x0, [fp, #-0x18]
    // 0xb223b0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb223b0: add             x25, x1, x3, lsl #2
    //     0xb223b4: add             x25, x25, #0xf
    //     0xb223b8: str             w0, [x25]
    //     0xb223bc: tbz             w0, #0, #0xb223d8
    //     0xb223c0: ldurb           w16, [x1, #-1]
    //     0xb223c4: ldurb           w17, [x0, #-1]
    //     0xb223c8: and             x16, x17, x16, lsr #2
    //     0xb223cc: tst             x16, HEAP, lsr #32
    //     0xb223d0: b.eq            #0xb223d8
    //     0xb223d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb223d8: ldur            x0, [fp, #-8]
    // 0xb223dc: r0 = Stack()
    //     0xb223dc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb223e0: mov             x1, x0
    // 0xb223e4: r0 = Instance_AlignmentDirectional
    //     0xb223e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb223e8: ldr             x0, [x0, #0xd08]
    // 0xb223ec: stur            x1, [fp, #-0x18]
    // 0xb223f0: StoreField: r1->field_f = r0
    //     0xb223f0: stur            w0, [x1, #0xf]
    // 0xb223f4: r0 = Instance_StackFit
    //     0xb223f4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb223f8: ldr             x0, [x0, #0xfa8]
    // 0xb223fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb223fc: stur            w0, [x1, #0x17]
    // 0xb22400: r0 = Instance_Clip
    //     0xb22400: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb22404: ldr             x0, [x0, #0x7e0]
    // 0xb22408: StoreField: r1->field_1b = r0
    //     0xb22408: stur            w0, [x1, #0x1b]
    // 0xb2240c: ldur            x0, [fp, #-0x10]
    // 0xb22410: StoreField: r1->field_b = r0
    //     0xb22410: stur            w0, [x1, #0xb]
    // 0xb22414: r0 = ColoredBox()
    //     0xb22414: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb22418: mov             x2, x0
    // 0xb2241c: r0 = Instance_Color
    //     0xb2241c: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22420: stur            x2, [fp, #-0x10]
    // 0xb22424: StoreField: r2->field_f = r0
    //     0xb22424: stur            w0, [x2, #0xf]
    // 0xb22428: ldur            x1, [fp, #-0x18]
    // 0xb2242c: StoreField: r2->field_b = r1
    //     0xb2242c: stur            w1, [x2, #0xb]
    // 0xb22430: r1 = <FlexParentData>
    //     0xb22430: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb22434: ldr             x1, [x1, #0xe00]
    // 0xb22438: r0 = Expanded()
    //     0xb22438: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb2243c: mov             x3, x0
    // 0xb22440: r0 = 1
    //     0xb22440: movz            x0, #0x1
    // 0xb22444: stur            x3, [fp, #-0x18]
    // 0xb22448: StoreField: r3->field_13 = r0
    //     0xb22448: stur            x0, [x3, #0x13]
    // 0xb2244c: r1 = Instance_FlexFit
    //     0xb2244c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb22450: ldr             x1, [x1, #0xe08]
    // 0xb22454: StoreField: r3->field_1b = r1
    //     0xb22454: stur            w1, [x3, #0x1b]
    // 0xb22458: ldur            x1, [fp, #-0x10]
    // 0xb2245c: StoreField: r3->field_b = r1
    //     0xb2245c: stur            w1, [x3, #0xb]
    // 0xb22460: r1 = <Widget>
    //     0xb22460: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb22464: r2 = 0
    //     0xb22464: movz            x2, #0
    // 0xb22468: r0 = _GrowableList()
    //     0xb22468: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb2246c: mov             x2, x0
    // 0xb22470: ldur            x0, [fp, #-8]
    // 0xb22474: stur            x2, [fp, #-0x10]
    // 0xb22478: LoadField: r1 = r0->field_2b
    //     0xb22478: ldur            w1, [x0, #0x2b]
    // 0xb2247c: DecompressPointer r1
    //     0xb2247c: add             x1, x1, HEAP, lsl #32
    // 0xb22480: tbz             w1, #4, #0xb22e9c
    // 0xb22484: r1 = Instance_Color
    //     0xb22484: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22488: d0 = 0.050000
    //     0xb22488: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb2248c: r0 = withOpacity()
    //     0xb2248c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb22490: stur            x0, [fp, #-0x28]
    // 0xb22494: r0 = BoxDecoration()
    //     0xb22494: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb22498: mov             x2, x0
    // 0xb2249c: ldur            x0, [fp, #-0x28]
    // 0xb224a0: stur            x2, [fp, #-0x30]
    // 0xb224a4: StoreField: r2->field_7 = r0
    //     0xb224a4: stur            w0, [x2, #7]
    // 0xb224a8: r0 = Instance_BoxShape
    //     0xb224a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb224ac: ldr             x0, [x0, #0x970]
    // 0xb224b0: StoreField: r2->field_23 = r0
    //     0xb224b0: stur            w0, [x2, #0x23]
    // 0xb224b4: ldur            x3, [fp, #-8]
    // 0xb224b8: LoadField: r0 = r3->field_b
    //     0xb224b8: ldur            w0, [x3, #0xb]
    // 0xb224bc: DecompressPointer r0
    //     0xb224bc: add             x0, x0, HEAP, lsl #32
    // 0xb224c0: cmp             w0, NULL
    // 0xb224c4: b.eq            #0xb2343c
    // 0xb224c8: LoadField: r4 = r0->field_f
    //     0xb224c8: ldur            w4, [x0, #0xf]
    // 0xb224cc: DecompressPointer r4
    //     0xb224cc: add             x4, x4, HEAP, lsl #32
    // 0xb224d0: LoadField: r5 = r3->field_13
    //     0xb224d0: ldur            x5, [x3, #0x13]
    // 0xb224d4: LoadField: r0 = r4->field_b
    //     0xb224d4: ldur            w0, [x4, #0xb]
    // 0xb224d8: r1 = LoadInt32Instr(r0)
    //     0xb224d8: sbfx            x1, x0, #1, #0x1f
    // 0xb224dc: mov             x0, x1
    // 0xb224e0: mov             x1, x5
    // 0xb224e4: cmp             x1, x0
    // 0xb224e8: b.hs            #0xb23440
    // 0xb224ec: LoadField: r0 = r4->field_f
    //     0xb224ec: ldur            w0, [x4, #0xf]
    // 0xb224f0: DecompressPointer r0
    //     0xb224f0: add             x0, x0, HEAP, lsl #32
    // 0xb224f4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb224f4: add             x16, x0, x5, lsl #2
    //     0xb224f8: ldur            w1, [x16, #0xf]
    // 0xb224fc: DecompressPointer r1
    //     0xb224fc: add             x1, x1, HEAP, lsl #32
    // 0xb22500: LoadField: r0 = r1->field_7
    //     0xb22500: ldur            w0, [x1, #7]
    // 0xb22504: DecompressPointer r0
    //     0xb22504: add             x0, x0, HEAP, lsl #32
    // 0xb22508: cmp             w0, NULL
    // 0xb2250c: b.ne            #0xb22518
    // 0xb22510: r0 = Null
    //     0xb22510: mov             x0, NULL
    // 0xb22514: b               #0xb2253c
    // 0xb22518: stp             xzr, x0, [SP]
    // 0xb2251c: r0 = []()
    //     0xb2251c: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb22520: r1 = LoadClassIdInstr(r0)
    //     0xb22520: ldur            x1, [x0, #-1]
    //     0xb22524: ubfx            x1, x1, #0xc, #0x14
    // 0xb22528: str             x0, [SP]
    // 0xb2252c: mov             x0, x1
    // 0xb22530: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb22530: sub             lr, x0, #1, lsl #12
    //     0xb22534: ldr             lr, [x21, lr, lsl #3]
    //     0xb22538: blr             lr
    // 0xb2253c: cmp             w0, NULL
    // 0xb22540: b.ne            #0xb2254c
    // 0xb22544: r3 = ""
    //     0xb22544: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb22548: b               #0xb22550
    // 0xb2254c: mov             x3, x0
    // 0xb22550: ldur            x0, [fp, #-8]
    // 0xb22554: ldur            x2, [fp, #-0x20]
    // 0xb22558: stur            x3, [fp, #-0x28]
    // 0xb2255c: LoadField: r1 = r2->field_13
    //     0xb2255c: ldur            w1, [x2, #0x13]
    // 0xb22560: DecompressPointer r1
    //     0xb22560: add             x1, x1, HEAP, lsl #32
    // 0xb22564: r0 = of()
    //     0xb22564: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb22568: LoadField: r1 = r0->field_87
    //     0xb22568: ldur            w1, [x0, #0x87]
    // 0xb2256c: DecompressPointer r1
    //     0xb2256c: add             x1, x1, HEAP, lsl #32
    // 0xb22570: LoadField: r0 = r1->field_7
    //     0xb22570: ldur            w0, [x1, #7]
    // 0xb22574: DecompressPointer r0
    //     0xb22574: add             x0, x0, HEAP, lsl #32
    // 0xb22578: stur            x0, [fp, #-0x38]
    // 0xb2257c: r1 = Instance_Color
    //     0xb2257c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22580: d0 = 0.500000
    //     0xb22580: fmov            d0, #0.50000000
    // 0xb22584: r0 = withOpacity()
    //     0xb22584: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb22588: r16 = 16.000000
    //     0xb22588: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb2258c: ldr             x16, [x16, #0x188]
    // 0xb22590: stp             x0, x16, [SP]
    // 0xb22594: ldur            x1, [fp, #-0x38]
    // 0xb22598: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb22598: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2259c: ldr             x4, [x4, #0xaa0]
    // 0xb225a0: r0 = copyWith()
    //     0xb225a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb225a4: stur            x0, [fp, #-0x38]
    // 0xb225a8: r0 = Text()
    //     0xb225a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb225ac: mov             x1, x0
    // 0xb225b0: ldur            x0, [fp, #-0x28]
    // 0xb225b4: stur            x1, [fp, #-0x48]
    // 0xb225b8: StoreField: r1->field_b = r0
    //     0xb225b8: stur            w0, [x1, #0xb]
    // 0xb225bc: ldur            x0, [fp, #-0x38]
    // 0xb225c0: StoreField: r1->field_13 = r0
    //     0xb225c0: stur            w0, [x1, #0x13]
    // 0xb225c4: r0 = Center()
    //     0xb225c4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb225c8: mov             x1, x0
    // 0xb225cc: r0 = Instance_Alignment
    //     0xb225cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb225d0: ldr             x0, [x0, #0xb10]
    // 0xb225d4: stur            x1, [fp, #-0x28]
    // 0xb225d8: StoreField: r1->field_f = r0
    //     0xb225d8: stur            w0, [x1, #0xf]
    // 0xb225dc: ldur            x0, [fp, #-0x48]
    // 0xb225e0: StoreField: r1->field_b = r0
    //     0xb225e0: stur            w0, [x1, #0xb]
    // 0xb225e4: r0 = Container()
    //     0xb225e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb225e8: stur            x0, [fp, #-0x38]
    // 0xb225ec: r16 = 34.000000
    //     0xb225ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb225f0: ldr             x16, [x16, #0x978]
    // 0xb225f4: r30 = 34.000000
    //     0xb225f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb225f8: ldr             lr, [lr, #0x978]
    // 0xb225fc: stp             lr, x16, [SP, #0x18]
    // 0xb22600: r16 = Instance_EdgeInsets
    //     0xb22600: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb22604: ldr             x16, [x16, #0x980]
    // 0xb22608: ldur            lr, [fp, #-0x30]
    // 0xb2260c: stp             lr, x16, [SP, #8]
    // 0xb22610: ldur            x16, [fp, #-0x28]
    // 0xb22614: str             x16, [SP]
    // 0xb22618: mov             x1, x0
    // 0xb2261c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb2261c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb22620: ldr             x4, [x4, #0x988]
    // 0xb22624: r0 = Container()
    //     0xb22624: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb22628: ldur            x2, [fp, #-8]
    // 0xb2262c: LoadField: r0 = r2->field_b
    //     0xb2262c: ldur            w0, [x2, #0xb]
    // 0xb22630: DecompressPointer r0
    //     0xb22630: add             x0, x0, HEAP, lsl #32
    // 0xb22634: cmp             w0, NULL
    // 0xb22638: b.eq            #0xb23444
    // 0xb2263c: LoadField: r3 = r0->field_f
    //     0xb2263c: ldur            w3, [x0, #0xf]
    // 0xb22640: DecompressPointer r3
    //     0xb22640: add             x3, x3, HEAP, lsl #32
    // 0xb22644: LoadField: r4 = r2->field_13
    //     0xb22644: ldur            x4, [x2, #0x13]
    // 0xb22648: LoadField: r0 = r3->field_b
    //     0xb22648: ldur            w0, [x3, #0xb]
    // 0xb2264c: r1 = LoadInt32Instr(r0)
    //     0xb2264c: sbfx            x1, x0, #1, #0x1f
    // 0xb22650: mov             x0, x1
    // 0xb22654: mov             x1, x4
    // 0xb22658: cmp             x1, x0
    // 0xb2265c: b.hs            #0xb23448
    // 0xb22660: LoadField: r0 = r3->field_f
    //     0xb22660: ldur            w0, [x3, #0xf]
    // 0xb22664: DecompressPointer r0
    //     0xb22664: add             x0, x0, HEAP, lsl #32
    // 0xb22668: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb22668: add             x16, x0, x4, lsl #2
    //     0xb2266c: ldur            w1, [x16, #0xf]
    // 0xb22670: DecompressPointer r1
    //     0xb22670: add             x1, x1, HEAP, lsl #32
    // 0xb22674: LoadField: r0 = r1->field_7
    //     0xb22674: ldur            w0, [x1, #7]
    // 0xb22678: DecompressPointer r0
    //     0xb22678: add             x0, x0, HEAP, lsl #32
    // 0xb2267c: cmp             w0, NULL
    // 0xb22680: b.ne            #0xb2268c
    // 0xb22684: r3 = ""
    //     0xb22684: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb22688: b               #0xb22690
    // 0xb2268c: mov             x3, x0
    // 0xb22690: ldur            x0, [fp, #-0x20]
    // 0xb22694: stur            x3, [fp, #-0x28]
    // 0xb22698: LoadField: r1 = r0->field_13
    //     0xb22698: ldur            w1, [x0, #0x13]
    // 0xb2269c: DecompressPointer r1
    //     0xb2269c: add             x1, x1, HEAP, lsl #32
    // 0xb226a0: r0 = of()
    //     0xb226a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb226a4: LoadField: r1 = r0->field_87
    //     0xb226a4: ldur            w1, [x0, #0x87]
    // 0xb226a8: DecompressPointer r1
    //     0xb226a8: add             x1, x1, HEAP, lsl #32
    // 0xb226ac: LoadField: r0 = r1->field_7
    //     0xb226ac: ldur            w0, [x1, #7]
    // 0xb226b0: DecompressPointer r0
    //     0xb226b0: add             x0, x0, HEAP, lsl #32
    // 0xb226b4: r16 = 14.000000
    //     0xb226b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb226b8: ldr             x16, [x16, #0x1d8]
    // 0xb226bc: r30 = Instance_Color
    //     0xb226bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb226c0: stp             lr, x16, [SP]
    // 0xb226c4: mov             x1, x0
    // 0xb226c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb226c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb226cc: ldr             x4, [x4, #0xaa0]
    // 0xb226d0: r0 = copyWith()
    //     0xb226d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb226d4: stur            x0, [fp, #-0x30]
    // 0xb226d8: r0 = Text()
    //     0xb226d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb226dc: mov             x2, x0
    // 0xb226e0: ldur            x0, [fp, #-0x28]
    // 0xb226e4: stur            x2, [fp, #-0x48]
    // 0xb226e8: StoreField: r2->field_b = r0
    //     0xb226e8: stur            w0, [x2, #0xb]
    // 0xb226ec: ldur            x0, [fp, #-0x30]
    // 0xb226f0: StoreField: r2->field_13 = r0
    //     0xb226f0: stur            w0, [x2, #0x13]
    // 0xb226f4: ldur            x3, [fp, #-8]
    // 0xb226f8: LoadField: r0 = r3->field_b
    //     0xb226f8: ldur            w0, [x3, #0xb]
    // 0xb226fc: DecompressPointer r0
    //     0xb226fc: add             x0, x0, HEAP, lsl #32
    // 0xb22700: cmp             w0, NULL
    // 0xb22704: b.eq            #0xb2344c
    // 0xb22708: LoadField: r4 = r0->field_f
    //     0xb22708: ldur            w4, [x0, #0xf]
    // 0xb2270c: DecompressPointer r4
    //     0xb2270c: add             x4, x4, HEAP, lsl #32
    // 0xb22710: LoadField: r5 = r3->field_13
    //     0xb22710: ldur            x5, [x3, #0x13]
    // 0xb22714: LoadField: r0 = r4->field_b
    //     0xb22714: ldur            w0, [x4, #0xb]
    // 0xb22718: r1 = LoadInt32Instr(r0)
    //     0xb22718: sbfx            x1, x0, #1, #0x1f
    // 0xb2271c: mov             x0, x1
    // 0xb22720: mov             x1, x5
    // 0xb22724: cmp             x1, x0
    // 0xb22728: b.hs            #0xb23450
    // 0xb2272c: LoadField: r0 = r4->field_f
    //     0xb2272c: ldur            w0, [x4, #0xf]
    // 0xb22730: DecompressPointer r0
    //     0xb22730: add             x0, x0, HEAP, lsl #32
    // 0xb22734: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb22734: add             x16, x0, x5, lsl #2
    //     0xb22738: ldur            w1, [x16, #0xf]
    // 0xb2273c: DecompressPointer r1
    //     0xb2273c: add             x1, x1, HEAP, lsl #32
    // 0xb22740: LoadField: r0 = r1->field_1f
    //     0xb22740: ldur            w0, [x1, #0x1f]
    // 0xb22744: DecompressPointer r0
    //     0xb22744: add             x0, x0, HEAP, lsl #32
    // 0xb22748: cmp             w0, NULL
    // 0xb2274c: b.ne            #0xb22758
    // 0xb22750: r4 = ""
    //     0xb22750: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb22754: b               #0xb2275c
    // 0xb22758: mov             x4, x0
    // 0xb2275c: ldur            x0, [fp, #-0x20]
    // 0xb22760: stur            x4, [fp, #-0x28]
    // 0xb22764: LoadField: r1 = r0->field_13
    //     0xb22764: ldur            w1, [x0, #0x13]
    // 0xb22768: DecompressPointer r1
    //     0xb22768: add             x1, x1, HEAP, lsl #32
    // 0xb2276c: r0 = of()
    //     0xb2276c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb22770: LoadField: r1 = r0->field_87
    //     0xb22770: ldur            w1, [x0, #0x87]
    // 0xb22774: DecompressPointer r1
    //     0xb22774: add             x1, x1, HEAP, lsl #32
    // 0xb22778: LoadField: r0 = r1->field_33
    //     0xb22778: ldur            w0, [x1, #0x33]
    // 0xb2277c: DecompressPointer r0
    //     0xb2277c: add             x0, x0, HEAP, lsl #32
    // 0xb22780: stur            x0, [fp, #-0x30]
    // 0xb22784: cmp             w0, NULL
    // 0xb22788: b.ne            #0xb22794
    // 0xb2278c: r4 = Null
    //     0xb2278c: mov             x4, NULL
    // 0xb22790: b               #0xb227bc
    // 0xb22794: r1 = Instance_Color
    //     0xb22794: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22798: d0 = 0.500000
    //     0xb22798: fmov            d0, #0.50000000
    // 0xb2279c: r0 = withOpacity()
    //     0xb2279c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb227a0: r16 = 10.000000
    //     0xb227a0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb227a4: stp             x0, x16, [SP]
    // 0xb227a8: ldur            x1, [fp, #-0x30]
    // 0xb227ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb227ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb227b0: ldr             x4, [x4, #0xaa0]
    // 0xb227b4: r0 = copyWith()
    //     0xb227b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb227b8: mov             x4, x0
    // 0xb227bc: ldur            x1, [fp, #-8]
    // 0xb227c0: ldur            x3, [fp, #-0x38]
    // 0xb227c4: ldur            x0, [fp, #-0x48]
    // 0xb227c8: ldur            x2, [fp, #-0x28]
    // 0xb227cc: stur            x4, [fp, #-0x30]
    // 0xb227d0: r0 = Text()
    //     0xb227d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb227d4: mov             x1, x0
    // 0xb227d8: ldur            x0, [fp, #-0x28]
    // 0xb227dc: stur            x1, [fp, #-0x50]
    // 0xb227e0: StoreField: r1->field_b = r0
    //     0xb227e0: stur            w0, [x1, #0xb]
    // 0xb227e4: ldur            x0, [fp, #-0x30]
    // 0xb227e8: StoreField: r1->field_13 = r0
    //     0xb227e8: stur            w0, [x1, #0x13]
    // 0xb227ec: r0 = Padding()
    //     0xb227ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb227f0: mov             x3, x0
    // 0xb227f4: r0 = Instance_EdgeInsets
    //     0xb227f4: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb227f8: ldr             x0, [x0, #0xe90]
    // 0xb227fc: stur            x3, [fp, #-0x28]
    // 0xb22800: StoreField: r3->field_f = r0
    //     0xb22800: stur            w0, [x3, #0xf]
    // 0xb22804: ldur            x0, [fp, #-0x50]
    // 0xb22808: StoreField: r3->field_b = r0
    //     0xb22808: stur            w0, [x3, #0xb]
    // 0xb2280c: r1 = Null
    //     0xb2280c: mov             x1, NULL
    // 0xb22810: r2 = 4
    //     0xb22810: movz            x2, #0x4
    // 0xb22814: r0 = AllocateArray()
    //     0xb22814: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb22818: mov             x2, x0
    // 0xb2281c: ldur            x0, [fp, #-0x48]
    // 0xb22820: stur            x2, [fp, #-0x30]
    // 0xb22824: StoreField: r2->field_f = r0
    //     0xb22824: stur            w0, [x2, #0xf]
    // 0xb22828: ldur            x0, [fp, #-0x28]
    // 0xb2282c: StoreField: r2->field_13 = r0
    //     0xb2282c: stur            w0, [x2, #0x13]
    // 0xb22830: r1 = <Widget>
    //     0xb22830: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb22834: r0 = AllocateGrowableArray()
    //     0xb22834: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb22838: mov             x1, x0
    // 0xb2283c: ldur            x0, [fp, #-0x30]
    // 0xb22840: stur            x1, [fp, #-0x28]
    // 0xb22844: StoreField: r1->field_f = r0
    //     0xb22844: stur            w0, [x1, #0xf]
    // 0xb22848: r2 = 4
    //     0xb22848: movz            x2, #0x4
    // 0xb2284c: StoreField: r1->field_b = r2
    //     0xb2284c: stur            w2, [x1, #0xb]
    // 0xb22850: r0 = Column()
    //     0xb22850: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb22854: mov             x3, x0
    // 0xb22858: r0 = Instance_Axis
    //     0xb22858: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2285c: stur            x3, [fp, #-0x30]
    // 0xb22860: StoreField: r3->field_f = r0
    //     0xb22860: stur            w0, [x3, #0xf]
    // 0xb22864: r4 = Instance_MainAxisAlignment
    //     0xb22864: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb22868: ldr             x4, [x4, #0xa08]
    // 0xb2286c: StoreField: r3->field_13 = r4
    //     0xb2286c: stur            w4, [x3, #0x13]
    // 0xb22870: r5 = Instance_MainAxisSize
    //     0xb22870: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb22874: ldr             x5, [x5, #0xa10]
    // 0xb22878: ArrayStore: r3[0] = r5  ; List_4
    //     0xb22878: stur            w5, [x3, #0x17]
    // 0xb2287c: r6 = Instance_CrossAxisAlignment
    //     0xb2287c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb22880: ldr             x6, [x6, #0x890]
    // 0xb22884: StoreField: r3->field_1b = r6
    //     0xb22884: stur            w6, [x3, #0x1b]
    // 0xb22888: r7 = Instance_VerticalDirection
    //     0xb22888: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2288c: ldr             x7, [x7, #0xa20]
    // 0xb22890: StoreField: r3->field_23 = r7
    //     0xb22890: stur            w7, [x3, #0x23]
    // 0xb22894: r8 = Instance_Clip
    //     0xb22894: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb22898: ldr             x8, [x8, #0x38]
    // 0xb2289c: StoreField: r3->field_2b = r8
    //     0xb2289c: stur            w8, [x3, #0x2b]
    // 0xb228a0: StoreField: r3->field_2f = rZR
    //     0xb228a0: stur            xzr, [x3, #0x2f]
    // 0xb228a4: ldur            x1, [fp, #-0x28]
    // 0xb228a8: StoreField: r3->field_b = r1
    //     0xb228a8: stur            w1, [x3, #0xb]
    // 0xb228ac: r1 = Null
    //     0xb228ac: mov             x1, NULL
    // 0xb228b0: r2 = 6
    //     0xb228b0: movz            x2, #0x6
    // 0xb228b4: r0 = AllocateArray()
    //     0xb228b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb228b8: mov             x2, x0
    // 0xb228bc: ldur            x0, [fp, #-0x38]
    // 0xb228c0: stur            x2, [fp, #-0x28]
    // 0xb228c4: StoreField: r2->field_f = r0
    //     0xb228c4: stur            w0, [x2, #0xf]
    // 0xb228c8: r16 = Instance_SizedBox
    //     0xb228c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb228cc: ldr             x16, [x16, #0x998]
    // 0xb228d0: StoreField: r2->field_13 = r16
    //     0xb228d0: stur            w16, [x2, #0x13]
    // 0xb228d4: ldur            x0, [fp, #-0x30]
    // 0xb228d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb228d8: stur            w0, [x2, #0x17]
    // 0xb228dc: r1 = <Widget>
    //     0xb228dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb228e0: r0 = AllocateGrowableArray()
    //     0xb228e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb228e4: mov             x1, x0
    // 0xb228e8: ldur            x0, [fp, #-0x28]
    // 0xb228ec: stur            x1, [fp, #-0x30]
    // 0xb228f0: StoreField: r1->field_f = r0
    //     0xb228f0: stur            w0, [x1, #0xf]
    // 0xb228f4: r2 = 6
    //     0xb228f4: movz            x2, #0x6
    // 0xb228f8: StoreField: r1->field_b = r2
    //     0xb228f8: stur            w2, [x1, #0xb]
    // 0xb228fc: r0 = Row()
    //     0xb228fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb22900: mov             x3, x0
    // 0xb22904: r2 = Instance_Axis
    //     0xb22904: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb22908: stur            x3, [fp, #-0x28]
    // 0xb2290c: StoreField: r3->field_f = r2
    //     0xb2290c: stur            w2, [x3, #0xf]
    // 0xb22910: r4 = Instance_MainAxisAlignment
    //     0xb22910: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb22914: ldr             x4, [x4, #0xa08]
    // 0xb22918: StoreField: r3->field_13 = r4
    //     0xb22918: stur            w4, [x3, #0x13]
    // 0xb2291c: r5 = Instance_MainAxisSize
    //     0xb2291c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb22920: ldr             x5, [x5, #0xa10]
    // 0xb22924: ArrayStore: r3[0] = r5  ; List_4
    //     0xb22924: stur            w5, [x3, #0x17]
    // 0xb22928: r6 = Instance_CrossAxisAlignment
    //     0xb22928: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2292c: ldr             x6, [x6, #0xa18]
    // 0xb22930: StoreField: r3->field_1b = r6
    //     0xb22930: stur            w6, [x3, #0x1b]
    // 0xb22934: r7 = Instance_VerticalDirection
    //     0xb22934: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb22938: ldr             x7, [x7, #0xa20]
    // 0xb2293c: StoreField: r3->field_23 = r7
    //     0xb2293c: stur            w7, [x3, #0x23]
    // 0xb22940: r8 = Instance_Clip
    //     0xb22940: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb22944: ldr             x8, [x8, #0x38]
    // 0xb22948: StoreField: r3->field_2b = r8
    //     0xb22948: stur            w8, [x3, #0x2b]
    // 0xb2294c: StoreField: r3->field_2f = rZR
    //     0xb2294c: stur            xzr, [x3, #0x2f]
    // 0xb22950: ldur            x0, [fp, #-0x30]
    // 0xb22954: StoreField: r3->field_b = r0
    //     0xb22954: stur            w0, [x3, #0xb]
    // 0xb22958: ldur            x9, [fp, #-8]
    // 0xb2295c: LoadField: r0 = r9->field_b
    //     0xb2295c: ldur            w0, [x9, #0xb]
    // 0xb22960: DecompressPointer r0
    //     0xb22960: add             x0, x0, HEAP, lsl #32
    // 0xb22964: cmp             w0, NULL
    // 0xb22968: b.eq            #0xb23454
    // 0xb2296c: LoadField: r10 = r0->field_f
    //     0xb2296c: ldur            w10, [x0, #0xf]
    // 0xb22970: DecompressPointer r10
    //     0xb22970: add             x10, x10, HEAP, lsl #32
    // 0xb22974: LoadField: r11 = r9->field_13
    //     0xb22974: ldur            x11, [x9, #0x13]
    // 0xb22978: LoadField: r0 = r10->field_b
    //     0xb22978: ldur            w0, [x10, #0xb]
    // 0xb2297c: r1 = LoadInt32Instr(r0)
    //     0xb2297c: sbfx            x1, x0, #1, #0x1f
    // 0xb22980: mov             x0, x1
    // 0xb22984: mov             x1, x11
    // 0xb22988: cmp             x1, x0
    // 0xb2298c: b.hs            #0xb23458
    // 0xb22990: LoadField: r0 = r10->field_f
    //     0xb22990: ldur            w0, [x10, #0xf]
    // 0xb22994: DecompressPointer r0
    //     0xb22994: add             x0, x0, HEAP, lsl #32
    // 0xb22998: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xb22998: add             x16, x0, x11, lsl #2
    //     0xb2299c: ldur            w1, [x16, #0xf]
    // 0xb229a0: DecompressPointer r1
    //     0xb229a0: add             x1, x1, HEAP, lsl #32
    // 0xb229a4: LoadField: r0 = r1->field_f
    //     0xb229a4: ldur            w0, [x1, #0xf]
    // 0xb229a8: DecompressPointer r0
    //     0xb229a8: add             x0, x0, HEAP, lsl #32
    // 0xb229ac: r1 = 60
    //     0xb229ac: movz            x1, #0x3c
    // 0xb229b0: branchIfSmi(r0, 0xb229bc)
    //     0xb229b0: tbz             w0, #0, #0xb229bc
    // 0xb229b4: r1 = LoadClassIdInstr(r0)
    //     0xb229b4: ldur            x1, [x0, #-1]
    //     0xb229b8: ubfx            x1, x1, #0xc, #0x14
    // 0xb229bc: str             x0, [SP]
    // 0xb229c0: mov             x0, x1
    // 0xb229c4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb229c4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb229c8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb229c8: movz            x17, #0x2700
    //     0xb229cc: add             lr, x0, x17
    //     0xb229d0: ldr             lr, [x21, lr, lsl #3]
    //     0xb229d4: blr             lr
    // 0xb229d8: mov             x1, x0
    // 0xb229dc: r0 = parse()
    //     0xb229dc: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb229e0: mov             v1.16b, v0.16b
    // 0xb229e4: d0 = 4.000000
    //     0xb229e4: fmov            d0, #4.00000000
    // 0xb229e8: fcmp            d1, d0
    // 0xb229ec: b.lt            #0xb229fc
    // 0xb229f0: r4 = Instance_Color
    //     0xb229f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb229f4: ldr             x4, [x4, #0x858]
    // 0xb229f8: b               #0xb22b5c
    // 0xb229fc: ldur            x2, [fp, #-8]
    // 0xb22a00: LoadField: r0 = r2->field_b
    //     0xb22a00: ldur            w0, [x2, #0xb]
    // 0xb22a04: DecompressPointer r0
    //     0xb22a04: add             x0, x0, HEAP, lsl #32
    // 0xb22a08: cmp             w0, NULL
    // 0xb22a0c: b.eq            #0xb2345c
    // 0xb22a10: LoadField: r3 = r0->field_f
    //     0xb22a10: ldur            w3, [x0, #0xf]
    // 0xb22a14: DecompressPointer r3
    //     0xb22a14: add             x3, x3, HEAP, lsl #32
    // 0xb22a18: LoadField: r4 = r2->field_13
    //     0xb22a18: ldur            x4, [x2, #0x13]
    // 0xb22a1c: LoadField: r0 = r3->field_b
    //     0xb22a1c: ldur            w0, [x3, #0xb]
    // 0xb22a20: r1 = LoadInt32Instr(r0)
    //     0xb22a20: sbfx            x1, x0, #1, #0x1f
    // 0xb22a24: mov             x0, x1
    // 0xb22a28: mov             x1, x4
    // 0xb22a2c: cmp             x1, x0
    // 0xb22a30: b.hs            #0xb23460
    // 0xb22a34: LoadField: r0 = r3->field_f
    //     0xb22a34: ldur            w0, [x3, #0xf]
    // 0xb22a38: DecompressPointer r0
    //     0xb22a38: add             x0, x0, HEAP, lsl #32
    // 0xb22a3c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb22a3c: add             x16, x0, x4, lsl #2
    //     0xb22a40: ldur            w1, [x16, #0xf]
    // 0xb22a44: DecompressPointer r1
    //     0xb22a44: add             x1, x1, HEAP, lsl #32
    // 0xb22a48: LoadField: r0 = r1->field_f
    //     0xb22a48: ldur            w0, [x1, #0xf]
    // 0xb22a4c: DecompressPointer r0
    //     0xb22a4c: add             x0, x0, HEAP, lsl #32
    // 0xb22a50: r1 = 60
    //     0xb22a50: movz            x1, #0x3c
    // 0xb22a54: branchIfSmi(r0, 0xb22a60)
    //     0xb22a54: tbz             w0, #0, #0xb22a60
    // 0xb22a58: r1 = LoadClassIdInstr(r0)
    //     0xb22a58: ldur            x1, [x0, #-1]
    //     0xb22a5c: ubfx            x1, x1, #0xc, #0x14
    // 0xb22a60: str             x0, [SP]
    // 0xb22a64: mov             x0, x1
    // 0xb22a68: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb22a68: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb22a6c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb22a6c: movz            x17, #0x2700
    //     0xb22a70: add             lr, x0, x17
    //     0xb22a74: ldr             lr, [x21, lr, lsl #3]
    //     0xb22a78: blr             lr
    // 0xb22a7c: mov             x1, x0
    // 0xb22a80: r0 = parse()
    //     0xb22a80: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb22a84: mov             v1.16b, v0.16b
    // 0xb22a88: d0 = 3.500000
    //     0xb22a88: fmov            d0, #3.50000000
    // 0xb22a8c: fcmp            d1, d0
    // 0xb22a90: b.lt            #0xb22aac
    // 0xb22a94: r1 = Instance_Color
    //     0xb22a94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb22a98: ldr             x1, [x1, #0x858]
    // 0xb22a9c: d0 = 0.700000
    //     0xb22a9c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb22aa0: ldr             d0, [x17, #0xf48]
    // 0xb22aa4: r0 = withOpacity()
    //     0xb22aa4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb22aa8: b               #0xb22b58
    // 0xb22aac: ldur            x2, [fp, #-8]
    // 0xb22ab0: LoadField: r0 = r2->field_b
    //     0xb22ab0: ldur            w0, [x2, #0xb]
    // 0xb22ab4: DecompressPointer r0
    //     0xb22ab4: add             x0, x0, HEAP, lsl #32
    // 0xb22ab8: cmp             w0, NULL
    // 0xb22abc: b.eq            #0xb23464
    // 0xb22ac0: LoadField: r3 = r0->field_f
    //     0xb22ac0: ldur            w3, [x0, #0xf]
    // 0xb22ac4: DecompressPointer r3
    //     0xb22ac4: add             x3, x3, HEAP, lsl #32
    // 0xb22ac8: LoadField: r4 = r2->field_13
    //     0xb22ac8: ldur            x4, [x2, #0x13]
    // 0xb22acc: LoadField: r0 = r3->field_b
    //     0xb22acc: ldur            w0, [x3, #0xb]
    // 0xb22ad0: r1 = LoadInt32Instr(r0)
    //     0xb22ad0: sbfx            x1, x0, #1, #0x1f
    // 0xb22ad4: mov             x0, x1
    // 0xb22ad8: mov             x1, x4
    // 0xb22adc: cmp             x1, x0
    // 0xb22ae0: b.hs            #0xb23468
    // 0xb22ae4: LoadField: r0 = r3->field_f
    //     0xb22ae4: ldur            w0, [x3, #0xf]
    // 0xb22ae8: DecompressPointer r0
    //     0xb22ae8: add             x0, x0, HEAP, lsl #32
    // 0xb22aec: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb22aec: add             x16, x0, x4, lsl #2
    //     0xb22af0: ldur            w1, [x16, #0xf]
    // 0xb22af4: DecompressPointer r1
    //     0xb22af4: add             x1, x1, HEAP, lsl #32
    // 0xb22af8: LoadField: r0 = r1->field_f
    //     0xb22af8: ldur            w0, [x1, #0xf]
    // 0xb22afc: DecompressPointer r0
    //     0xb22afc: add             x0, x0, HEAP, lsl #32
    // 0xb22b00: r1 = 60
    //     0xb22b00: movz            x1, #0x3c
    // 0xb22b04: branchIfSmi(r0, 0xb22b10)
    //     0xb22b04: tbz             w0, #0, #0xb22b10
    // 0xb22b08: r1 = LoadClassIdInstr(r0)
    //     0xb22b08: ldur            x1, [x0, #-1]
    //     0xb22b0c: ubfx            x1, x1, #0xc, #0x14
    // 0xb22b10: str             x0, [SP]
    // 0xb22b14: mov             x0, x1
    // 0xb22b18: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb22b18: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb22b1c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb22b1c: movz            x17, #0x2700
    //     0xb22b20: add             lr, x0, x17
    //     0xb22b24: ldr             lr, [x21, lr, lsl #3]
    //     0xb22b28: blr             lr
    // 0xb22b2c: mov             x1, x0
    // 0xb22b30: r0 = parse()
    //     0xb22b30: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb22b34: mov             v1.16b, v0.16b
    // 0xb22b38: d0 = 2.000000
    //     0xb22b38: fmov            d0, #2.00000000
    // 0xb22b3c: fcmp            d1, d0
    // 0xb22b40: b.lt            #0xb22b50
    // 0xb22b44: r0 = Instance_Color
    //     0xb22b44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb22b48: ldr             x0, [x0, #0x860]
    // 0xb22b4c: b               #0xb22b58
    // 0xb22b50: r0 = Instance_Color
    //     0xb22b50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb22b54: ldr             x0, [x0, #0x50]
    // 0xb22b58: mov             x4, x0
    // 0xb22b5c: ldur            x0, [fp, #-8]
    // 0xb22b60: ldur            x2, [fp, #-0x20]
    // 0xb22b64: ldur            x3, [fp, #-0x10]
    // 0xb22b68: ldur            x1, [fp, #-0x28]
    // 0xb22b6c: stur            x4, [fp, #-0x30]
    // 0xb22b70: r0 = ColorFilter()
    //     0xb22b70: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb22b74: mov             x1, x0
    // 0xb22b78: ldur            x0, [fp, #-0x30]
    // 0xb22b7c: stur            x1, [fp, #-0x38]
    // 0xb22b80: StoreField: r1->field_7 = r0
    //     0xb22b80: stur            w0, [x1, #7]
    // 0xb22b84: r0 = Instance_BlendMode
    //     0xb22b84: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb22b88: ldr             x0, [x0, #0xb30]
    // 0xb22b8c: StoreField: r1->field_b = r0
    //     0xb22b8c: stur            w0, [x1, #0xb]
    // 0xb22b90: r0 = 1
    //     0xb22b90: movz            x0, #0x1
    // 0xb22b94: StoreField: r1->field_13 = r0
    //     0xb22b94: stur            x0, [x1, #0x13]
    // 0xb22b98: r0 = SvgPicture()
    //     0xb22b98: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb22b9c: stur            x0, [fp, #-0x30]
    // 0xb22ba0: ldur            x16, [fp, #-0x38]
    // 0xb22ba4: str             x16, [SP]
    // 0xb22ba8: mov             x1, x0
    // 0xb22bac: r2 = "assets/images/green_star.svg"
    //     0xb22bac: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb22bb0: ldr             x2, [x2, #0x9a0]
    // 0xb22bb4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb22bb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb22bb8: ldr             x4, [x4, #0xa38]
    // 0xb22bbc: r0 = SvgPicture.asset()
    //     0xb22bbc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb22bc0: ldur            x2, [fp, #-8]
    // 0xb22bc4: LoadField: r0 = r2->field_b
    //     0xb22bc4: ldur            w0, [x2, #0xb]
    // 0xb22bc8: DecompressPointer r0
    //     0xb22bc8: add             x0, x0, HEAP, lsl #32
    // 0xb22bcc: cmp             w0, NULL
    // 0xb22bd0: b.eq            #0xb2346c
    // 0xb22bd4: LoadField: r3 = r0->field_f
    //     0xb22bd4: ldur            w3, [x0, #0xf]
    // 0xb22bd8: DecompressPointer r3
    //     0xb22bd8: add             x3, x3, HEAP, lsl #32
    // 0xb22bdc: LoadField: r4 = r2->field_13
    //     0xb22bdc: ldur            x4, [x2, #0x13]
    // 0xb22be0: LoadField: r0 = r3->field_b
    //     0xb22be0: ldur            w0, [x3, #0xb]
    // 0xb22be4: r1 = LoadInt32Instr(r0)
    //     0xb22be4: sbfx            x1, x0, #1, #0x1f
    // 0xb22be8: mov             x0, x1
    // 0xb22bec: mov             x1, x4
    // 0xb22bf0: cmp             x1, x0
    // 0xb22bf4: b.hs            #0xb23470
    // 0xb22bf8: LoadField: r0 = r3->field_f
    //     0xb22bf8: ldur            w0, [x3, #0xf]
    // 0xb22bfc: DecompressPointer r0
    //     0xb22bfc: add             x0, x0, HEAP, lsl #32
    // 0xb22c00: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb22c00: add             x16, x0, x4, lsl #2
    //     0xb22c04: ldur            w1, [x16, #0xf]
    // 0xb22c08: DecompressPointer r1
    //     0xb22c08: add             x1, x1, HEAP, lsl #32
    // 0xb22c0c: LoadField: r0 = r1->field_f
    //     0xb22c0c: ldur            w0, [x1, #0xf]
    // 0xb22c10: DecompressPointer r0
    //     0xb22c10: add             x0, x0, HEAP, lsl #32
    // 0xb22c14: r1 = 60
    //     0xb22c14: movz            x1, #0x3c
    // 0xb22c18: branchIfSmi(r0, 0xb22c24)
    //     0xb22c18: tbz             w0, #0, #0xb22c24
    // 0xb22c1c: r1 = LoadClassIdInstr(r0)
    //     0xb22c1c: ldur            x1, [x0, #-1]
    //     0xb22c20: ubfx            x1, x1, #0xc, #0x14
    // 0xb22c24: str             x0, [SP]
    // 0xb22c28: mov             x0, x1
    // 0xb22c2c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb22c2c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb22c30: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb22c30: movz            x17, #0x2700
    //     0xb22c34: add             lr, x0, x17
    //     0xb22c38: ldr             lr, [x21, lr, lsl #3]
    //     0xb22c3c: blr             lr
    // 0xb22c40: ldur            x2, [fp, #-0x20]
    // 0xb22c44: stur            x0, [fp, #-0x38]
    // 0xb22c48: LoadField: r1 = r2->field_13
    //     0xb22c48: ldur            w1, [x2, #0x13]
    // 0xb22c4c: DecompressPointer r1
    //     0xb22c4c: add             x1, x1, HEAP, lsl #32
    // 0xb22c50: r0 = of()
    //     0xb22c50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb22c54: LoadField: r1 = r0->field_87
    //     0xb22c54: ldur            w1, [x0, #0x87]
    // 0xb22c58: DecompressPointer r1
    //     0xb22c58: add             x1, x1, HEAP, lsl #32
    // 0xb22c5c: LoadField: r0 = r1->field_7
    //     0xb22c5c: ldur            w0, [x1, #7]
    // 0xb22c60: DecompressPointer r0
    //     0xb22c60: add             x0, x0, HEAP, lsl #32
    // 0xb22c64: r16 = 12.000000
    //     0xb22c64: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb22c68: ldr             x16, [x16, #0x9e8]
    // 0xb22c6c: r30 = Instance_Color
    //     0xb22c6c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22c70: stp             lr, x16, [SP]
    // 0xb22c74: mov             x1, x0
    // 0xb22c78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb22c78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb22c7c: ldr             x4, [x4, #0xaa0]
    // 0xb22c80: r0 = copyWith()
    //     0xb22c80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb22c84: stur            x0, [fp, #-0x48]
    // 0xb22c88: r0 = Text()
    //     0xb22c88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb22c8c: mov             x3, x0
    // 0xb22c90: ldur            x0, [fp, #-0x38]
    // 0xb22c94: stur            x3, [fp, #-0x50]
    // 0xb22c98: StoreField: r3->field_b = r0
    //     0xb22c98: stur            w0, [x3, #0xb]
    // 0xb22c9c: ldur            x0, [fp, #-0x48]
    // 0xb22ca0: StoreField: r3->field_13 = r0
    //     0xb22ca0: stur            w0, [x3, #0x13]
    // 0xb22ca4: r1 = Null
    //     0xb22ca4: mov             x1, NULL
    // 0xb22ca8: r2 = 6
    //     0xb22ca8: movz            x2, #0x6
    // 0xb22cac: r0 = AllocateArray()
    //     0xb22cac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb22cb0: mov             x2, x0
    // 0xb22cb4: ldur            x0, [fp, #-0x30]
    // 0xb22cb8: stur            x2, [fp, #-0x38]
    // 0xb22cbc: StoreField: r2->field_f = r0
    //     0xb22cbc: stur            w0, [x2, #0xf]
    // 0xb22cc0: r16 = Instance_SizedBox
    //     0xb22cc0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb22cc4: ldr             x16, [x16, #0xe98]
    // 0xb22cc8: StoreField: r2->field_13 = r16
    //     0xb22cc8: stur            w16, [x2, #0x13]
    // 0xb22ccc: ldur            x0, [fp, #-0x50]
    // 0xb22cd0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb22cd0: stur            w0, [x2, #0x17]
    // 0xb22cd4: r1 = <Widget>
    //     0xb22cd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb22cd8: r0 = AllocateGrowableArray()
    //     0xb22cd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb22cdc: mov             x1, x0
    // 0xb22ce0: ldur            x0, [fp, #-0x38]
    // 0xb22ce4: stur            x1, [fp, #-0x30]
    // 0xb22ce8: StoreField: r1->field_f = r0
    //     0xb22ce8: stur            w0, [x1, #0xf]
    // 0xb22cec: r0 = 6
    //     0xb22cec: movz            x0, #0x6
    // 0xb22cf0: StoreField: r1->field_b = r0
    //     0xb22cf0: stur            w0, [x1, #0xb]
    // 0xb22cf4: r0 = Row()
    //     0xb22cf4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb22cf8: mov             x3, x0
    // 0xb22cfc: r0 = Instance_Axis
    //     0xb22cfc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb22d00: stur            x3, [fp, #-0x38]
    // 0xb22d04: StoreField: r3->field_f = r0
    //     0xb22d04: stur            w0, [x3, #0xf]
    // 0xb22d08: r4 = Instance_MainAxisAlignment
    //     0xb22d08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb22d0c: ldr             x4, [x4, #0xa08]
    // 0xb22d10: StoreField: r3->field_13 = r4
    //     0xb22d10: stur            w4, [x3, #0x13]
    // 0xb22d14: r5 = Instance_MainAxisSize
    //     0xb22d14: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb22d18: ldr             x5, [x5, #0xa10]
    // 0xb22d1c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb22d1c: stur            w5, [x3, #0x17]
    // 0xb22d20: r6 = Instance_CrossAxisAlignment
    //     0xb22d20: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb22d24: ldr             x6, [x6, #0xa18]
    // 0xb22d28: StoreField: r3->field_1b = r6
    //     0xb22d28: stur            w6, [x3, #0x1b]
    // 0xb22d2c: r7 = Instance_VerticalDirection
    //     0xb22d2c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb22d30: ldr             x7, [x7, #0xa20]
    // 0xb22d34: StoreField: r3->field_23 = r7
    //     0xb22d34: stur            w7, [x3, #0x23]
    // 0xb22d38: r8 = Instance_Clip
    //     0xb22d38: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb22d3c: ldr             x8, [x8, #0x38]
    // 0xb22d40: StoreField: r3->field_2b = r8
    //     0xb22d40: stur            w8, [x3, #0x2b]
    // 0xb22d44: StoreField: r3->field_2f = rZR
    //     0xb22d44: stur            xzr, [x3, #0x2f]
    // 0xb22d48: ldur            x1, [fp, #-0x30]
    // 0xb22d4c: StoreField: r3->field_b = r1
    //     0xb22d4c: stur            w1, [x3, #0xb]
    // 0xb22d50: r1 = Null
    //     0xb22d50: mov             x1, NULL
    // 0xb22d54: r2 = 4
    //     0xb22d54: movz            x2, #0x4
    // 0xb22d58: r0 = AllocateArray()
    //     0xb22d58: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb22d5c: mov             x2, x0
    // 0xb22d60: ldur            x0, [fp, #-0x28]
    // 0xb22d64: stur            x2, [fp, #-0x30]
    // 0xb22d68: StoreField: r2->field_f = r0
    //     0xb22d68: stur            w0, [x2, #0xf]
    // 0xb22d6c: ldur            x0, [fp, #-0x38]
    // 0xb22d70: StoreField: r2->field_13 = r0
    //     0xb22d70: stur            w0, [x2, #0x13]
    // 0xb22d74: r1 = <Widget>
    //     0xb22d74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb22d78: r0 = AllocateGrowableArray()
    //     0xb22d78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb22d7c: mov             x1, x0
    // 0xb22d80: ldur            x0, [fp, #-0x30]
    // 0xb22d84: stur            x1, [fp, #-0x28]
    // 0xb22d88: StoreField: r1->field_f = r0
    //     0xb22d88: stur            w0, [x1, #0xf]
    // 0xb22d8c: r2 = 4
    //     0xb22d8c: movz            x2, #0x4
    // 0xb22d90: StoreField: r1->field_b = r2
    //     0xb22d90: stur            w2, [x1, #0xb]
    // 0xb22d94: r0 = Row()
    //     0xb22d94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb22d98: mov             x1, x0
    // 0xb22d9c: r0 = Instance_Axis
    //     0xb22d9c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb22da0: stur            x1, [fp, #-0x30]
    // 0xb22da4: StoreField: r1->field_f = r0
    //     0xb22da4: stur            w0, [x1, #0xf]
    // 0xb22da8: r0 = Instance_MainAxisAlignment
    //     0xb22da8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb22dac: ldr             x0, [x0, #0xa8]
    // 0xb22db0: StoreField: r1->field_13 = r0
    //     0xb22db0: stur            w0, [x1, #0x13]
    // 0xb22db4: r0 = Instance_MainAxisSize
    //     0xb22db4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb22db8: ldr             x0, [x0, #0xa10]
    // 0xb22dbc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb22dbc: stur            w0, [x1, #0x17]
    // 0xb22dc0: r2 = Instance_CrossAxisAlignment
    //     0xb22dc0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb22dc4: ldr             x2, [x2, #0xa18]
    // 0xb22dc8: StoreField: r1->field_1b = r2
    //     0xb22dc8: stur            w2, [x1, #0x1b]
    // 0xb22dcc: r3 = Instance_VerticalDirection
    //     0xb22dcc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb22dd0: ldr             x3, [x3, #0xa20]
    // 0xb22dd4: StoreField: r1->field_23 = r3
    //     0xb22dd4: stur            w3, [x1, #0x23]
    // 0xb22dd8: r4 = Instance_Clip
    //     0xb22dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb22ddc: ldr             x4, [x4, #0x38]
    // 0xb22de0: StoreField: r1->field_2b = r4
    //     0xb22de0: stur            w4, [x1, #0x2b]
    // 0xb22de4: StoreField: r1->field_2f = rZR
    //     0xb22de4: stur            xzr, [x1, #0x2f]
    // 0xb22de8: ldur            x5, [fp, #-0x28]
    // 0xb22dec: StoreField: r1->field_b = r5
    //     0xb22dec: stur            w5, [x1, #0xb]
    // 0xb22df0: r0 = Container()
    //     0xb22df0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb22df4: stur            x0, [fp, #-0x28]
    // 0xb22df8: r16 = Instance_BoxDecoration
    //     0xb22df8: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb22dfc: ldr             x16, [x16, #0x5a8]
    // 0xb22e00: r30 = Instance_EdgeInsets
    //     0xb22e00: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb22e04: ldr             lr, [lr, #0xd0]
    // 0xb22e08: stp             lr, x16, [SP, #8]
    // 0xb22e0c: ldur            x16, [fp, #-0x30]
    // 0xb22e10: str             x16, [SP]
    // 0xb22e14: mov             x1, x0
    // 0xb22e18: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb22e18: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb22e1c: ldr             x4, [x4, #0xb40]
    // 0xb22e20: r0 = Container()
    //     0xb22e20: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb22e24: ldur            x0, [fp, #-0x10]
    // 0xb22e28: LoadField: r1 = r0->field_b
    //     0xb22e28: ldur            w1, [x0, #0xb]
    // 0xb22e2c: LoadField: r2 = r0->field_f
    //     0xb22e2c: ldur            w2, [x0, #0xf]
    // 0xb22e30: DecompressPointer r2
    //     0xb22e30: add             x2, x2, HEAP, lsl #32
    // 0xb22e34: LoadField: r3 = r2->field_b
    //     0xb22e34: ldur            w3, [x2, #0xb]
    // 0xb22e38: r2 = LoadInt32Instr(r1)
    //     0xb22e38: sbfx            x2, x1, #1, #0x1f
    // 0xb22e3c: stur            x2, [fp, #-0x40]
    // 0xb22e40: r1 = LoadInt32Instr(r3)
    //     0xb22e40: sbfx            x1, x3, #1, #0x1f
    // 0xb22e44: cmp             x2, x1
    // 0xb22e48: b.ne            #0xb22e54
    // 0xb22e4c: mov             x1, x0
    // 0xb22e50: r0 = _growToNextCapacity()
    //     0xb22e50: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb22e54: ldur            x2, [fp, #-0x10]
    // 0xb22e58: ldur            x3, [fp, #-0x40]
    // 0xb22e5c: add             x0, x3, #1
    // 0xb22e60: lsl             x1, x0, #1
    // 0xb22e64: StoreField: r2->field_b = r1
    //     0xb22e64: stur            w1, [x2, #0xb]
    // 0xb22e68: LoadField: r1 = r2->field_f
    //     0xb22e68: ldur            w1, [x2, #0xf]
    // 0xb22e6c: DecompressPointer r1
    //     0xb22e6c: add             x1, x1, HEAP, lsl #32
    // 0xb22e70: ldur            x0, [fp, #-0x28]
    // 0xb22e74: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb22e74: add             x25, x1, x3, lsl #2
    //     0xb22e78: add             x25, x25, #0xf
    //     0xb22e7c: str             w0, [x25]
    //     0xb22e80: tbz             w0, #0, #0xb22e9c
    //     0xb22e84: ldurb           w16, [x1, #-1]
    //     0xb22e88: ldurb           w17, [x0, #-1]
    //     0xb22e8c: and             x16, x17, x16, lsr #2
    //     0xb22e90: tst             x16, HEAP, lsr #32
    //     0xb22e94: b.eq            #0xb22e9c
    //     0xb22e98: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb22e9c: ldur            x3, [fp, #-8]
    // 0xb22ea0: LoadField: r0 = r3->field_b
    //     0xb22ea0: ldur            w0, [x3, #0xb]
    // 0xb22ea4: DecompressPointer r0
    //     0xb22ea4: add             x0, x0, HEAP, lsl #32
    // 0xb22ea8: cmp             w0, NULL
    // 0xb22eac: b.eq            #0xb23474
    // 0xb22eb0: LoadField: r4 = r0->field_f
    //     0xb22eb0: ldur            w4, [x0, #0xf]
    // 0xb22eb4: DecompressPointer r4
    //     0xb22eb4: add             x4, x4, HEAP, lsl #32
    // 0xb22eb8: LoadField: r5 = r3->field_13
    //     0xb22eb8: ldur            x5, [x3, #0x13]
    // 0xb22ebc: LoadField: r0 = r4->field_b
    //     0xb22ebc: ldur            w0, [x4, #0xb]
    // 0xb22ec0: r1 = LoadInt32Instr(r0)
    //     0xb22ec0: sbfx            x1, x0, #1, #0x1f
    // 0xb22ec4: mov             x0, x1
    // 0xb22ec8: mov             x1, x5
    // 0xb22ecc: cmp             x1, x0
    // 0xb22ed0: b.hs            #0xb23478
    // 0xb22ed4: LoadField: r0 = r4->field_f
    //     0xb22ed4: ldur            w0, [x4, #0xf]
    // 0xb22ed8: DecompressPointer r0
    //     0xb22ed8: add             x0, x0, HEAP, lsl #32
    // 0xb22edc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb22edc: add             x16, x0, x5, lsl #2
    //     0xb22ee0: ldur            w1, [x16, #0xf]
    // 0xb22ee4: DecompressPointer r1
    //     0xb22ee4: add             x1, x1, HEAP, lsl #32
    // 0xb22ee8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb22ee8: ldur            w0, [x1, #0x17]
    // 0xb22eec: DecompressPointer r0
    //     0xb22eec: add             x0, x0, HEAP, lsl #32
    // 0xb22ef0: cmp             w0, NULL
    // 0xb22ef4: b.ne            #0xb22f00
    // 0xb22ef8: r4 = ""
    //     0xb22ef8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb22efc: b               #0xb22f04
    // 0xb22f00: mov             x4, x0
    // 0xb22f04: ldur            x0, [fp, #-0x20]
    // 0xb22f08: stur            x4, [fp, #-0x28]
    // 0xb22f0c: LoadField: r1 = r0->field_13
    //     0xb22f0c: ldur            w1, [x0, #0x13]
    // 0xb22f10: DecompressPointer r1
    //     0xb22f10: add             x1, x1, HEAP, lsl #32
    // 0xb22f14: r0 = of()
    //     0xb22f14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb22f18: LoadField: r1 = r0->field_87
    //     0xb22f18: ldur            w1, [x0, #0x87]
    // 0xb22f1c: DecompressPointer r1
    //     0xb22f1c: add             x1, x1, HEAP, lsl #32
    // 0xb22f20: LoadField: r0 = r1->field_2b
    //     0xb22f20: ldur            w0, [x1, #0x2b]
    // 0xb22f24: DecompressPointer r0
    //     0xb22f24: add             x0, x0, HEAP, lsl #32
    // 0xb22f28: LoadField: r1 = r0->field_13
    //     0xb22f28: ldur            w1, [x0, #0x13]
    // 0xb22f2c: DecompressPointer r1
    //     0xb22f2c: add             x1, x1, HEAP, lsl #32
    // 0xb22f30: r16 = Instance_Color
    //     0xb22f30: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22f34: stp             x16, x1, [SP]
    // 0xb22f38: r1 = Instance_TextStyle
    //     0xb22f38: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xb22f3c: ldr             x1, [x1, #0x9b0]
    // 0xb22f40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb22f40: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb22f44: ldr             x4, [x4, #0x9b8]
    // 0xb22f48: r0 = copyWith()
    //     0xb22f48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb22f4c: ldur            x2, [fp, #-0x20]
    // 0xb22f50: stur            x0, [fp, #-0x30]
    // 0xb22f54: LoadField: r1 = r2->field_13
    //     0xb22f54: ldur            w1, [x2, #0x13]
    // 0xb22f58: DecompressPointer r1
    //     0xb22f58: add             x1, x1, HEAP, lsl #32
    // 0xb22f5c: r0 = of()
    //     0xb22f5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb22f60: LoadField: r1 = r0->field_87
    //     0xb22f60: ldur            w1, [x0, #0x87]
    // 0xb22f64: DecompressPointer r1
    //     0xb22f64: add             x1, x1, HEAP, lsl #32
    // 0xb22f68: LoadField: r0 = r1->field_7
    //     0xb22f68: ldur            w0, [x1, #7]
    // 0xb22f6c: DecompressPointer r0
    //     0xb22f6c: add             x0, x0, HEAP, lsl #32
    // 0xb22f70: r16 = 12.000000
    //     0xb22f70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb22f74: ldr             x16, [x16, #0x9e8]
    // 0xb22f78: r30 = Instance_Color
    //     0xb22f78: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22f7c: stp             lr, x16, [SP, #8]
    // 0xb22f80: r16 = Instance_FontWeight
    //     0xb22f80: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb22f84: ldr             x16, [x16, #0x20]
    // 0xb22f88: str             x16, [SP]
    // 0xb22f8c: mov             x1, x0
    // 0xb22f90: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb22f90: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb22f94: ldr             x4, [x4, #0xc48]
    // 0xb22f98: r0 = copyWith()
    //     0xb22f98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb22f9c: ldur            x2, [fp, #-0x20]
    // 0xb22fa0: stur            x0, [fp, #-0x38]
    // 0xb22fa4: LoadField: r1 = r2->field_13
    //     0xb22fa4: ldur            w1, [x2, #0x13]
    // 0xb22fa8: DecompressPointer r1
    //     0xb22fa8: add             x1, x1, HEAP, lsl #32
    // 0xb22fac: r0 = of()
    //     0xb22fac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb22fb0: LoadField: r1 = r0->field_87
    //     0xb22fb0: ldur            w1, [x0, #0x87]
    // 0xb22fb4: DecompressPointer r1
    //     0xb22fb4: add             x1, x1, HEAP, lsl #32
    // 0xb22fb8: LoadField: r0 = r1->field_7
    //     0xb22fb8: ldur            w0, [x1, #7]
    // 0xb22fbc: DecompressPointer r0
    //     0xb22fbc: add             x0, x0, HEAP, lsl #32
    // 0xb22fc0: r16 = 12.000000
    //     0xb22fc0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb22fc4: ldr             x16, [x16, #0x9e8]
    // 0xb22fc8: r30 = Instance_Color
    //     0xb22fc8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb22fcc: stp             lr, x16, [SP, #8]
    // 0xb22fd0: r16 = Instance_FontWeight
    //     0xb22fd0: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb22fd4: ldr             x16, [x16, #0x20]
    // 0xb22fd8: str             x16, [SP]
    // 0xb22fdc: mov             x1, x0
    // 0xb22fe0: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb22fe0: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb22fe4: ldr             x4, [x4, #0xc48]
    // 0xb22fe8: r0 = copyWith()
    //     0xb22fe8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb22fec: mov             x1, x0
    // 0xb22ff0: ldur            x0, [fp, #-8]
    // 0xb22ff4: stur            x1, [fp, #-0x50]
    // 0xb22ff8: LoadField: r2 = r0->field_27
    //     0xb22ff8: ldur            w2, [x0, #0x27]
    // 0xb22ffc: DecompressPointer r2
    //     0xb22ffc: add             x2, x2, HEAP, lsl #32
    // 0xb23000: stur            x2, [fp, #-0x48]
    // 0xb23004: r0 = ReadMoreText()
    //     0xb23004: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb23008: mov             x1, x0
    // 0xb2300c: ldur            x0, [fp, #-0x28]
    // 0xb23010: stur            x1, [fp, #-8]
    // 0xb23014: StoreField: r1->field_3f = r0
    //     0xb23014: stur            w0, [x1, #0x3f]
    // 0xb23018: ldur            x0, [fp, #-0x48]
    // 0xb2301c: StoreField: r1->field_b = r0
    //     0xb2301c: stur            w0, [x1, #0xb]
    // 0xb23020: r0 = " Read Less"
    //     0xb23020: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xb23024: ldr             x0, [x0, #0x9c0]
    // 0xb23028: StoreField: r1->field_43 = r0
    //     0xb23028: stur            w0, [x1, #0x43]
    // 0xb2302c: r0 = "Read More"
    //     0xb2302c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xb23030: ldr             x0, [x0, #0x9c8]
    // 0xb23034: StoreField: r1->field_47 = r0
    //     0xb23034: stur            w0, [x1, #0x47]
    // 0xb23038: r0 = 240
    //     0xb23038: movz            x0, #0xf0
    // 0xb2303c: StoreField: r1->field_f = r0
    //     0xb2303c: stur            x0, [x1, #0xf]
    // 0xb23040: r0 = 2
    //     0xb23040: movz            x0, #0x2
    // 0xb23044: ArrayStore: r1[0] = r0  ; List_8
    //     0xb23044: stur            x0, [x1, #0x17]
    // 0xb23048: r0 = Instance_TrimMode
    //     0xb23048: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb2304c: ldr             x0, [x0, #0x9d0]
    // 0xb23050: StoreField: r1->field_1f = r0
    //     0xb23050: stur            w0, [x1, #0x1f]
    // 0xb23054: ldur            x0, [fp, #-0x30]
    // 0xb23058: StoreField: r1->field_4f = r0
    //     0xb23058: stur            w0, [x1, #0x4f]
    // 0xb2305c: r0 = Instance_TextAlign
    //     0xb2305c: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb23060: StoreField: r1->field_53 = r0
    //     0xb23060: stur            w0, [x1, #0x53]
    // 0xb23064: ldur            x0, [fp, #-0x38]
    // 0xb23068: StoreField: r1->field_23 = r0
    //     0xb23068: stur            w0, [x1, #0x23]
    // 0xb2306c: ldur            x0, [fp, #-0x50]
    // 0xb23070: StoreField: r1->field_27 = r0
    //     0xb23070: stur            w0, [x1, #0x27]
    // 0xb23074: r0 = "… "
    //     0xb23074: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb23078: ldr             x0, [x0, #0x9d8]
    // 0xb2307c: StoreField: r1->field_3b = r0
    //     0xb2307c: stur            w0, [x1, #0x3b]
    // 0xb23080: r0 = true
    //     0xb23080: add             x0, NULL, #0x20  ; true
    // 0xb23084: StoreField: r1->field_37 = r0
    //     0xb23084: stur            w0, [x1, #0x37]
    // 0xb23088: r0 = Container()
    //     0xb23088: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb2308c: stur            x0, [fp, #-0x28]
    // 0xb23090: r16 = Instance_EdgeInsets
    //     0xb23090: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb23094: ldr             x16, [x16, #0x668]
    // 0xb23098: r30 = Instance_BoxDecoration
    //     0xb23098: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb2309c: ldr             lr, [lr, #0x5a8]
    // 0xb230a0: stp             lr, x16, [SP, #8]
    // 0xb230a4: ldur            x16, [fp, #-8]
    // 0xb230a8: str             x16, [SP]
    // 0xb230ac: mov             x1, x0
    // 0xb230b0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb230b0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb230b4: ldr             x4, [x4, #0x610]
    // 0xb230b8: r0 = Container()
    //     0xb230b8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb230bc: r0 = Padding()
    //     0xb230bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb230c0: mov             x2, x0
    // 0xb230c4: r0 = Instance_EdgeInsets
    //     0xb230c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb230c8: ldr             x0, [x0, #0xa00]
    // 0xb230cc: stur            x2, [fp, #-8]
    // 0xb230d0: StoreField: r2->field_f = r0
    //     0xb230d0: stur            w0, [x2, #0xf]
    // 0xb230d4: ldur            x0, [fp, #-0x28]
    // 0xb230d8: StoreField: r2->field_b = r0
    //     0xb230d8: stur            w0, [x2, #0xb]
    // 0xb230dc: ldur            x0, [fp, #-0x10]
    // 0xb230e0: LoadField: r1 = r0->field_b
    //     0xb230e0: ldur            w1, [x0, #0xb]
    // 0xb230e4: LoadField: r3 = r0->field_f
    //     0xb230e4: ldur            w3, [x0, #0xf]
    // 0xb230e8: DecompressPointer r3
    //     0xb230e8: add             x3, x3, HEAP, lsl #32
    // 0xb230ec: LoadField: r4 = r3->field_b
    //     0xb230ec: ldur            w4, [x3, #0xb]
    // 0xb230f0: r3 = LoadInt32Instr(r1)
    //     0xb230f0: sbfx            x3, x1, #1, #0x1f
    // 0xb230f4: stur            x3, [fp, #-0x40]
    // 0xb230f8: r1 = LoadInt32Instr(r4)
    //     0xb230f8: sbfx            x1, x4, #1, #0x1f
    // 0xb230fc: cmp             x3, x1
    // 0xb23100: b.ne            #0xb2310c
    // 0xb23104: mov             x1, x0
    // 0xb23108: r0 = _growToNextCapacity()
    //     0xb23108: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb2310c: ldur            x2, [fp, #-0x10]
    // 0xb23110: ldur            x3, [fp, #-0x40]
    // 0xb23114: add             x0, x3, #1
    // 0xb23118: lsl             x1, x0, #1
    // 0xb2311c: StoreField: r2->field_b = r1
    //     0xb2311c: stur            w1, [x2, #0xb]
    // 0xb23120: LoadField: r1 = r2->field_f
    //     0xb23120: ldur            w1, [x2, #0xf]
    // 0xb23124: DecompressPointer r1
    //     0xb23124: add             x1, x1, HEAP, lsl #32
    // 0xb23128: ldur            x0, [fp, #-8]
    // 0xb2312c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb2312c: add             x25, x1, x3, lsl #2
    //     0xb23130: add             x25, x25, #0xf
    //     0xb23134: str             w0, [x25]
    //     0xb23138: tbz             w0, #0, #0xb23154
    //     0xb2313c: ldurb           w16, [x1, #-1]
    //     0xb23140: ldurb           w17, [x0, #-1]
    //     0xb23144: and             x16, x17, x16, lsr #2
    //     0xb23148: tst             x16, HEAP, lsr #32
    //     0xb2314c: b.eq            #0xb23154
    //     0xb23150: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb23154: r0 = GestureDetector()
    //     0xb23154: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb23158: ldur            x2, [fp, #-0x20]
    // 0xb2315c: r1 = Function '<anonymous closure>':.
    //     0xb2315c: add             x1, PP, #0x57, lsl #12  ; [pp+0x575f8] AnonymousClosure: (0xaa5490), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb23160: ldr             x1, [x1, #0x5f8]
    // 0xb23164: stur            x0, [fp, #-8]
    // 0xb23168: r0 = AllocateClosure()
    //     0xb23168: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2316c: ldur            x2, [fp, #-0x20]
    // 0xb23170: r1 = Function '<anonymous closure>':.
    //     0xb23170: add             x1, PP, #0x57, lsl #12  ; [pp+0x57600] AnonymousClosure: (0xb2347c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb21348)
    //     0xb23174: ldr             x1, [x1, #0x600]
    // 0xb23178: stur            x0, [fp, #-0x20]
    // 0xb2317c: r0 = AllocateClosure()
    //     0xb2317c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23180: ldur            x16, [fp, #-0x20]
    // 0xb23184: stp             x0, x16, [SP, #8]
    // 0xb23188: r16 = Instance_Icon
    //     0xb23188: add             x16, PP, #0x51, lsl #12  ; [pp+0x51eb8] Obj!Icon@d66531
    //     0xb2318c: ldr             x16, [x16, #0xeb8]
    // 0xb23190: str             x16, [SP]
    // 0xb23194: ldur            x1, [fp, #-8]
    // 0xb23198: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xb23198: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xb2319c: ldr             x4, [x4, #0xa20]
    // 0xb231a0: r0 = GestureDetector()
    //     0xb231a0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb231a4: r0 = Align()
    //     0xb231a4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb231a8: mov             x1, x0
    // 0xb231ac: r0 = Instance_Alignment
    //     0xb231ac: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb231b0: ldr             x0, [x0, #0xa78]
    // 0xb231b4: stur            x1, [fp, #-0x20]
    // 0xb231b8: StoreField: r1->field_f = r0
    //     0xb231b8: stur            w0, [x1, #0xf]
    // 0xb231bc: ldur            x0, [fp, #-8]
    // 0xb231c0: StoreField: r1->field_b = r0
    //     0xb231c0: stur            w0, [x1, #0xb]
    // 0xb231c4: r0 = Padding()
    //     0xb231c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb231c8: mov             x2, x0
    // 0xb231cc: r0 = Instance_EdgeInsets
    //     0xb231cc: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ec0] Obj!EdgeInsets@d58af1
    //     0xb231d0: ldr             x0, [x0, #0xec0]
    // 0xb231d4: stur            x2, [fp, #-8]
    // 0xb231d8: StoreField: r2->field_f = r0
    //     0xb231d8: stur            w0, [x2, #0xf]
    // 0xb231dc: ldur            x0, [fp, #-0x20]
    // 0xb231e0: StoreField: r2->field_b = r0
    //     0xb231e0: stur            w0, [x2, #0xb]
    // 0xb231e4: ldur            x0, [fp, #-0x10]
    // 0xb231e8: LoadField: r1 = r0->field_b
    //     0xb231e8: ldur            w1, [x0, #0xb]
    // 0xb231ec: LoadField: r3 = r0->field_f
    //     0xb231ec: ldur            w3, [x0, #0xf]
    // 0xb231f0: DecompressPointer r3
    //     0xb231f0: add             x3, x3, HEAP, lsl #32
    // 0xb231f4: LoadField: r4 = r3->field_b
    //     0xb231f4: ldur            w4, [x3, #0xb]
    // 0xb231f8: r3 = LoadInt32Instr(r1)
    //     0xb231f8: sbfx            x3, x1, #1, #0x1f
    // 0xb231fc: stur            x3, [fp, #-0x40]
    // 0xb23200: r1 = LoadInt32Instr(r4)
    //     0xb23200: sbfx            x1, x4, #1, #0x1f
    // 0xb23204: cmp             x3, x1
    // 0xb23208: b.ne            #0xb23214
    // 0xb2320c: mov             x1, x0
    // 0xb23210: r0 = _growToNextCapacity()
    //     0xb23210: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb23214: ldur            x4, [fp, #-0x18]
    // 0xb23218: ldur            x2, [fp, #-0x10]
    // 0xb2321c: ldur            x3, [fp, #-0x40]
    // 0xb23220: add             x0, x3, #1
    // 0xb23224: lsl             x1, x0, #1
    // 0xb23228: StoreField: r2->field_b = r1
    //     0xb23228: stur            w1, [x2, #0xb]
    // 0xb2322c: LoadField: r1 = r2->field_f
    //     0xb2322c: ldur            w1, [x2, #0xf]
    // 0xb23230: DecompressPointer r1
    //     0xb23230: add             x1, x1, HEAP, lsl #32
    // 0xb23234: ldur            x0, [fp, #-8]
    // 0xb23238: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb23238: add             x25, x1, x3, lsl #2
    //     0xb2323c: add             x25, x25, #0xf
    //     0xb23240: str             w0, [x25]
    //     0xb23244: tbz             w0, #0, #0xb23260
    //     0xb23248: ldurb           w16, [x1, #-1]
    //     0xb2324c: ldurb           w17, [x0, #-1]
    //     0xb23250: and             x16, x17, x16, lsr #2
    //     0xb23254: tst             x16, HEAP, lsr #32
    //     0xb23258: b.eq            #0xb23260
    //     0xb2325c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb23260: r0 = Column()
    //     0xb23260: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb23264: mov             x3, x0
    // 0xb23268: r0 = Instance_Axis
    //     0xb23268: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2326c: stur            x3, [fp, #-8]
    // 0xb23270: StoreField: r3->field_f = r0
    //     0xb23270: stur            w0, [x3, #0xf]
    // 0xb23274: r4 = Instance_MainAxisAlignment
    //     0xb23274: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb23278: ldr             x4, [x4, #0xa08]
    // 0xb2327c: StoreField: r3->field_13 = r4
    //     0xb2327c: stur            w4, [x3, #0x13]
    // 0xb23280: r5 = Instance_MainAxisSize
    //     0xb23280: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb23284: ldr             x5, [x5, #0xa10]
    // 0xb23288: ArrayStore: r3[0] = r5  ; List_4
    //     0xb23288: stur            w5, [x3, #0x17]
    // 0xb2328c: r1 = Instance_CrossAxisAlignment
    //     0xb2328c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb23290: ldr             x1, [x1, #0x890]
    // 0xb23294: StoreField: r3->field_1b = r1
    //     0xb23294: stur            w1, [x3, #0x1b]
    // 0xb23298: r6 = Instance_VerticalDirection
    //     0xb23298: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2329c: ldr             x6, [x6, #0xa20]
    // 0xb232a0: StoreField: r3->field_23 = r6
    //     0xb232a0: stur            w6, [x3, #0x23]
    // 0xb232a4: r7 = Instance_Clip
    //     0xb232a4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb232a8: ldr             x7, [x7, #0x38]
    // 0xb232ac: StoreField: r3->field_2b = r7
    //     0xb232ac: stur            w7, [x3, #0x2b]
    // 0xb232b0: StoreField: r3->field_2f = rZR
    //     0xb232b0: stur            xzr, [x3, #0x2f]
    // 0xb232b4: ldur            x1, [fp, #-0x10]
    // 0xb232b8: StoreField: r3->field_b = r1
    //     0xb232b8: stur            w1, [x3, #0xb]
    // 0xb232bc: r1 = Null
    //     0xb232bc: mov             x1, NULL
    // 0xb232c0: r2 = 4
    //     0xb232c0: movz            x2, #0x4
    // 0xb232c4: r0 = AllocateArray()
    //     0xb232c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb232c8: mov             x2, x0
    // 0xb232cc: ldur            x0, [fp, #-0x18]
    // 0xb232d0: stur            x2, [fp, #-0x10]
    // 0xb232d4: StoreField: r2->field_f = r0
    //     0xb232d4: stur            w0, [x2, #0xf]
    // 0xb232d8: ldur            x0, [fp, #-8]
    // 0xb232dc: StoreField: r2->field_13 = r0
    //     0xb232dc: stur            w0, [x2, #0x13]
    // 0xb232e0: r1 = <Widget>
    //     0xb232e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb232e4: r0 = AllocateGrowableArray()
    //     0xb232e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb232e8: mov             x1, x0
    // 0xb232ec: ldur            x0, [fp, #-0x10]
    // 0xb232f0: stur            x1, [fp, #-8]
    // 0xb232f4: StoreField: r1->field_f = r0
    //     0xb232f4: stur            w0, [x1, #0xf]
    // 0xb232f8: r0 = 4
    //     0xb232f8: movz            x0, #0x4
    // 0xb232fc: StoreField: r1->field_b = r0
    //     0xb232fc: stur            w0, [x1, #0xb]
    // 0xb23300: r0 = Column()
    //     0xb23300: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb23304: mov             x1, x0
    // 0xb23308: r0 = Instance_Axis
    //     0xb23308: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2330c: stur            x1, [fp, #-0x10]
    // 0xb23310: StoreField: r1->field_f = r0
    //     0xb23310: stur            w0, [x1, #0xf]
    // 0xb23314: r0 = Instance_MainAxisAlignment
    //     0xb23314: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb23318: ldr             x0, [x0, #0xa08]
    // 0xb2331c: StoreField: r1->field_13 = r0
    //     0xb2331c: stur            w0, [x1, #0x13]
    // 0xb23320: r0 = Instance_MainAxisSize
    //     0xb23320: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb23324: ldr             x0, [x0, #0xa10]
    // 0xb23328: ArrayStore: r1[0] = r0  ; List_4
    //     0xb23328: stur            w0, [x1, #0x17]
    // 0xb2332c: r0 = Instance_CrossAxisAlignment
    //     0xb2332c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb23330: ldr             x0, [x0, #0xa18]
    // 0xb23334: StoreField: r1->field_1b = r0
    //     0xb23334: stur            w0, [x1, #0x1b]
    // 0xb23338: r0 = Instance_VerticalDirection
    //     0xb23338: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2333c: ldr             x0, [x0, #0xa20]
    // 0xb23340: StoreField: r1->field_23 = r0
    //     0xb23340: stur            w0, [x1, #0x23]
    // 0xb23344: r0 = Instance_Clip
    //     0xb23344: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb23348: ldr             x0, [x0, #0x38]
    // 0xb2334c: StoreField: r1->field_2b = r0
    //     0xb2334c: stur            w0, [x1, #0x2b]
    // 0xb23350: StoreField: r1->field_2f = rZR
    //     0xb23350: stur            xzr, [x1, #0x2f]
    // 0xb23354: ldur            x0, [fp, #-8]
    // 0xb23358: StoreField: r1->field_b = r0
    //     0xb23358: stur            w0, [x1, #0xb]
    // 0xb2335c: r0 = SafeArea()
    //     0xb2335c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb23360: mov             x1, x0
    // 0xb23364: r0 = true
    //     0xb23364: add             x0, NULL, #0x20  ; true
    // 0xb23368: stur            x1, [fp, #-8]
    // 0xb2336c: StoreField: r1->field_b = r0
    //     0xb2336c: stur            w0, [x1, #0xb]
    // 0xb23370: StoreField: r1->field_f = r0
    //     0xb23370: stur            w0, [x1, #0xf]
    // 0xb23374: StoreField: r1->field_13 = r0
    //     0xb23374: stur            w0, [x1, #0x13]
    // 0xb23378: ArrayStore: r1[0] = r0  ; List_4
    //     0xb23378: stur            w0, [x1, #0x17]
    // 0xb2337c: r2 = Instance_EdgeInsets
    //     0xb2337c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb23380: StoreField: r1->field_1b = r2
    //     0xb23380: stur            w2, [x1, #0x1b]
    // 0xb23384: r2 = false
    //     0xb23384: add             x2, NULL, #0x30  ; false
    // 0xb23388: StoreField: r1->field_1f = r2
    //     0xb23388: stur            w2, [x1, #0x1f]
    // 0xb2338c: ldur            x3, [fp, #-0x10]
    // 0xb23390: StoreField: r1->field_23 = r3
    //     0xb23390: stur            w3, [x1, #0x23]
    // 0xb23394: r0 = Scaffold()
    //     0xb23394: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb23398: ldur            x1, [fp, #-8]
    // 0xb2339c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2339c: stur            w1, [x0, #0x17]
    // 0xb233a0: r1 = Instance_Color
    //     0xb233a0: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb233a4: StoreField: r0->field_33 = r1
    //     0xb233a4: stur            w1, [x0, #0x33]
    // 0xb233a8: r1 = true
    //     0xb233a8: add             x1, NULL, #0x20  ; true
    // 0xb233ac: StoreField: r0->field_43 = r1
    //     0xb233ac: stur            w1, [x0, #0x43]
    // 0xb233b0: r1 = false
    //     0xb233b0: add             x1, NULL, #0x30  ; false
    // 0xb233b4: StoreField: r0->field_b = r1
    //     0xb233b4: stur            w1, [x0, #0xb]
    // 0xb233b8: StoreField: r0->field_f = r1
    //     0xb233b8: stur            w1, [x0, #0xf]
    // 0xb233bc: LeaveFrame
    //     0xb233bc: mov             SP, fp
    //     0xb233c0: ldp             fp, lr, [SP], #0x10
    // 0xb233c4: ret
    //     0xb233c4: ret             
    // 0xb233c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb233c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb233cc: b               #0xb21368
    // 0xb233d0: r9 = _pageController
    //     0xb233d0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57608] Field <_RatingReviewAllMediaOnTapImageState@1511364025._pageController@1511364025>: late (offset: 0x24)
    //     0xb233d4: ldr             x9, [x9, #0x608]
    // 0xb233d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb233d8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb233dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb233dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb233e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb233e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb233e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb233e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb233e8: b               #0xb214ac
    // 0xb233ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb233ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb233f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb233f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb233f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb233f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb233f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb233f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb233fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb233fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23400: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23400: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23404: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23404: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23408: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23408: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2340c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2340c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23410: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23410: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23414: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb23414: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb23418: r0 = RangeErrorSharedWithFPURegs()
    //     0xb23418: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xb2341c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2341c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23420: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23420: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23428: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2342c: SaveReg d0
    //     0xb2342c: str             q0, [SP, #-0x10]!
    // 0xb23430: r0 = AllocateDouble()
    //     0xb23430: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb23434: RestoreReg d0
    //     0xb23434: ldr             q0, [SP], #0x10
    // 0xb23438: b               #0xb22318
    // 0xb2343c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2343c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23440: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23440: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23444: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23444: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23448: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23448: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2344c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2344c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23450: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23450: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23454: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23458: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23458: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2345c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2345c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23460: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23460: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23464: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23468: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23468: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb2346c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2346c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23470: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23470: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb23474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23478: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23478: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2347c, size: 0xd0
    // 0xb2347c: EnterFrame
    //     0xb2347c: stp             fp, lr, [SP, #-0x10]!
    //     0xb23480: mov             fp, SP
    // 0xb23484: ldr             x0, [fp, #0x10]
    // 0xb23488: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb23488: ldur            w1, [x0, #0x17]
    // 0xb2348c: DecompressPointer r1
    //     0xb2348c: add             x1, x1, HEAP, lsl #32
    // 0xb23490: CheckStackOverflow
    //     0xb23490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb23494: cmp             SP, x16
    //     0xb23498: b.ls            #0xb2353c
    // 0xb2349c: LoadField: r2 = r1->field_f
    //     0xb2349c: ldur            w2, [x1, #0xf]
    // 0xb234a0: DecompressPointer r2
    //     0xb234a0: add             x2, x2, HEAP, lsl #32
    // 0xb234a4: LoadField: r3 = r2->field_33
    //     0xb234a4: ldur            w3, [x2, #0x33]
    // 0xb234a8: DecompressPointer r3
    //     0xb234a8: add             x3, x3, HEAP, lsl #32
    // 0xb234ac: cmp             w3, NULL
    // 0xb234b0: b.eq            #0xb2352c
    // 0xb234b4: LoadField: r4 = r1->field_13
    //     0xb234b4: ldur            w4, [x1, #0x13]
    // 0xb234b8: DecompressPointer r4
    //     0xb234b8: add             x4, x4, HEAP, lsl #32
    // 0xb234bc: LoadField: r0 = r2->field_b
    //     0xb234bc: ldur            w0, [x2, #0xb]
    // 0xb234c0: DecompressPointer r0
    //     0xb234c0: add             x0, x0, HEAP, lsl #32
    // 0xb234c4: cmp             w0, NULL
    // 0xb234c8: b.eq            #0xb23544
    // 0xb234cc: LoadField: r5 = r0->field_f
    //     0xb234cc: ldur            w5, [x0, #0xf]
    // 0xb234d0: DecompressPointer r5
    //     0xb234d0: add             x5, x5, HEAP, lsl #32
    // 0xb234d4: LoadField: r6 = r2->field_13
    //     0xb234d4: ldur            x6, [x2, #0x13]
    // 0xb234d8: LoadField: r0 = r5->field_b
    //     0xb234d8: ldur            w0, [x5, #0xb]
    // 0xb234dc: r1 = LoadInt32Instr(r0)
    //     0xb234dc: sbfx            x1, x0, #1, #0x1f
    // 0xb234e0: mov             x0, x1
    // 0xb234e4: mov             x1, x6
    // 0xb234e8: cmp             x1, x0
    // 0xb234ec: b.hs            #0xb23548
    // 0xb234f0: LoadField: r0 = r5->field_f
    //     0xb234f0: ldur            w0, [x5, #0xf]
    // 0xb234f4: DecompressPointer r0
    //     0xb234f4: add             x0, x0, HEAP, lsl #32
    // 0xb234f8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb234f8: add             x16, x0, x6, lsl #2
    //     0xb234fc: ldur            w1, [x16, #0xf]
    // 0xb23500: DecompressPointer r1
    //     0xb23500: add             x1, x1, HEAP, lsl #32
    // 0xb23504: LoadField: r0 = r1->field_b
    //     0xb23504: ldur            w0, [x1, #0xb]
    // 0xb23508: DecompressPointer r0
    //     0xb23508: add             x0, x0, HEAP, lsl #32
    // 0xb2350c: cmp             w0, NULL
    // 0xb23510: b.ne            #0xb2351c
    // 0xb23514: r5 = ""
    //     0xb23514: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb23518: b               #0xb23520
    // 0xb2351c: mov             x5, x0
    // 0xb23520: mov             x1, x2
    // 0xb23524: mov             x2, x4
    // 0xb23528: r0 = showMenuItem()
    //     0xb23528: bl              #0xb2354c  ; [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem
    // 0xb2352c: r0 = Null
    //     0xb2352c: mov             x0, NULL
    // 0xb23530: LeaveFrame
    //     0xb23530: mov             SP, fp
    //     0xb23534: ldp             fp, lr, [SP], #0x10
    // 0xb23538: ret
    //     0xb23538: ret             
    // 0xb2353c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2353c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb23540: b               #0xb2349c
    // 0xb23544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb23544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb23548: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb23548: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xb2354c, size: 0x744
    // 0xb2354c: EnterFrame
    //     0xb2354c: stp             fp, lr, [SP, #-0x10]!
    //     0xb23550: mov             fp, SP
    // 0xb23554: AllocStack(0xa0)
    //     0xb23554: sub             SP, SP, #0xa0
    // 0xb23558: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xb23558: mov             x0, x1
    //     0xb2355c: stur            x1, [fp, #-8]
    //     0xb23560: mov             x1, x2
    //     0xb23564: stur            x2, [fp, #-0x10]
    //     0xb23568: mov             x2, x5
    //     0xb2356c: stur            x3, [fp, #-0x18]
    //     0xb23570: stur            x5, [fp, #-0x20]
    // 0xb23574: CheckStackOverflow
    //     0xb23574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb23578: cmp             SP, x16
    //     0xb2357c: b.ls            #0xb23c08
    // 0xb23580: r1 = 2
    //     0xb23580: movz            x1, #0x2
    // 0xb23584: r0 = AllocateContext()
    //     0xb23584: bl              #0x16f6108  ; AllocateContextStub
    // 0xb23588: mov             x4, x0
    // 0xb2358c: ldur            x3, [fp, #-8]
    // 0xb23590: stur            x4, [fp, #-0x28]
    // 0xb23594: StoreField: r4->field_f = r3
    //     0xb23594: stur            w3, [x4, #0xf]
    // 0xb23598: ldur            x2, [fp, #-0x20]
    // 0xb2359c: StoreField: r4->field_13 = r2
    //     0xb2359c: stur            w2, [x4, #0x13]
    // 0xb235a0: LoadField: r1 = r3->field_2f
    //     0xb235a0: ldur            w1, [x3, #0x2f]
    // 0xb235a4: DecompressPointer r1
    //     0xb235a4: add             x1, x1, HEAP, lsl #32
    // 0xb235a8: r0 = LoadClassIdInstr(r1)
    //     0xb235a8: ldur            x0, [x1, #-1]
    //     0xb235ac: ubfx            x0, x0, #0xc, #0x14
    // 0xb235b0: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb235b0: sub             lr, x0, #0xfe
    //     0xb235b4: ldr             lr, [x21, lr, lsl #3]
    //     0xb235b8: blr             lr
    // 0xb235bc: r1 = 60
    //     0xb235bc: movz            x1, #0x3c
    // 0xb235c0: branchIfSmi(r0, 0xb235cc)
    //     0xb235c0: tbz             w0, #0, #0xb235cc
    // 0xb235c4: r1 = LoadClassIdInstr(r0)
    //     0xb235c4: ldur            x1, [x0, #-1]
    //     0xb235c8: ubfx            x1, x1, #0xc, #0x14
    // 0xb235cc: r16 = true
    //     0xb235cc: add             x16, NULL, #0x20  ; true
    // 0xb235d0: stp             x16, x0, [SP]
    // 0xb235d4: mov             x0, x1
    // 0xb235d8: mov             lr, x0
    // 0xb235dc: ldr             lr, [x21, lr, lsl #3]
    // 0xb235e0: blr             lr
    // 0xb235e4: tbnz            w0, #4, #0xb235f0
    // 0xb235e8: d0 = 100.000000
    //     0xb235e8: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb235ec: b               #0xb235f8
    // 0xb235f0: d0 = 120.000000
    //     0xb235f0: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xb235f4: ldr             d0, [x17, #0xa38]
    // 0xb235f8: ldur            x0, [fp, #-0x18]
    // 0xb235fc: stur            d0, [fp, #-0x58]
    // 0xb23600: r0 = BoxConstraints()
    //     0xb23600: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb23604: stur            x0, [fp, #-0x20]
    // 0xb23608: StoreField: r0->field_7 = rZR
    //     0xb23608: stur            xzr, [x0, #7]
    // 0xb2360c: ldur            d0, [fp, #-0x58]
    // 0xb23610: StoreField: r0->field_f = d0
    //     0xb23610: stur            d0, [x0, #0xf]
    // 0xb23614: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb23614: stur            xzr, [x0, #0x17]
    // 0xb23618: d0 = inf
    //     0xb23618: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb2361c: StoreField: r0->field_1f = d0
    //     0xb2361c: stur            d0, [x0, #0x1f]
    // 0xb23620: ldur            x1, [fp, #-0x18]
    // 0xb23624: cmp             w1, NULL
    // 0xb23628: b.ne            #0xb23634
    // 0xb2362c: r2 = Null
    //     0xb2362c: mov             x2, NULL
    // 0xb23630: b               #0xb23660
    // 0xb23634: LoadField: d0 = r1->field_7
    //     0xb23634: ldur            d0, [x1, #7]
    // 0xb23638: r2 = inline_Allocate_Double()
    //     0xb23638: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb2363c: add             x2, x2, #0x10
    //     0xb23640: cmp             x3, x2
    //     0xb23644: b.ls            #0xb23c10
    //     0xb23648: str             x2, [THR, #0x50]  ; THR::top
    //     0xb2364c: sub             x2, x2, #0xf
    //     0xb23650: movz            x3, #0xe15c
    //     0xb23654: movk            x3, #0x3, lsl #16
    //     0xb23658: stur            x3, [x2, #-1]
    // 0xb2365c: StoreField: r2->field_7 = d0
    //     0xb2365c: stur            d0, [x2, #7]
    // 0xb23660: cmp             w2, NULL
    // 0xb23664: b.ne            #0xb23670
    // 0xb23668: d0 = 0.000000
    //     0xb23668: eor             v0.16b, v0.16b, v0.16b
    // 0xb2366c: b               #0xb23674
    // 0xb23670: LoadField: d0 = r2->field_7
    //     0xb23670: ldur            d0, [x2, #7]
    // 0xb23674: stur            d0, [fp, #-0x70]
    // 0xb23678: cmp             w1, NULL
    // 0xb2367c: b.ne            #0xb23688
    // 0xb23680: r2 = Null
    //     0xb23680: mov             x2, NULL
    // 0xb23684: b               #0xb236b4
    // 0xb23688: LoadField: d1 = r1->field_f
    //     0xb23688: ldur            d1, [x1, #0xf]
    // 0xb2368c: r2 = inline_Allocate_Double()
    //     0xb2368c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb23690: add             x2, x2, #0x10
    //     0xb23694: cmp             x3, x2
    //     0xb23698: b.ls            #0xb23c2c
    //     0xb2369c: str             x2, [THR, #0x50]  ; THR::top
    //     0xb236a0: sub             x2, x2, #0xf
    //     0xb236a4: movz            x3, #0xe15c
    //     0xb236a8: movk            x3, #0x3, lsl #16
    //     0xb236ac: stur            x3, [x2, #-1]
    // 0xb236b0: StoreField: r2->field_7 = d1
    //     0xb236b0: stur            d1, [x2, #7]
    // 0xb236b4: cmp             w2, NULL
    // 0xb236b8: b.ne            #0xb236c4
    // 0xb236bc: d2 = 0.000000
    //     0xb236bc: eor             v2.16b, v2.16b, v2.16b
    // 0xb236c0: b               #0xb236cc
    // 0xb236c4: LoadField: d1 = r2->field_7
    //     0xb236c4: ldur            d1, [x2, #7]
    // 0xb236c8: mov             v2.16b, v1.16b
    // 0xb236cc: d1 = 50.000000
    //     0xb236cc: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb236d0: fsub            d3, d2, d1
    // 0xb236d4: stur            d3, [fp, #-0x68]
    // 0xb236d8: cmp             w1, NULL
    // 0xb236dc: b.ne            #0xb236e8
    // 0xb236e0: r2 = Null
    //     0xb236e0: mov             x2, NULL
    // 0xb236e4: b               #0xb23714
    // 0xb236e8: LoadField: d2 = r1->field_7
    //     0xb236e8: ldur            d2, [x1, #7]
    // 0xb236ec: r2 = inline_Allocate_Double()
    //     0xb236ec: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb236f0: add             x2, x2, #0x10
    //     0xb236f4: cmp             x3, x2
    //     0xb236f8: b.ls            #0xb23c48
    //     0xb236fc: str             x2, [THR, #0x50]  ; THR::top
    //     0xb23700: sub             x2, x2, #0xf
    //     0xb23704: movz            x3, #0xe15c
    //     0xb23708: movk            x3, #0x3, lsl #16
    //     0xb2370c: stur            x3, [x2, #-1]
    // 0xb23710: StoreField: r2->field_7 = d2
    //     0xb23710: stur            d2, [x2, #7]
    // 0xb23714: cmp             w2, NULL
    // 0xb23718: b.ne            #0xb23724
    // 0xb2371c: d2 = 0.000000
    //     0xb2371c: eor             v2.16b, v2.16b, v2.16b
    // 0xb23720: b               #0xb23728
    // 0xb23724: LoadField: d2 = r2->field_7
    //     0xb23724: ldur            d2, [x2, #7]
    // 0xb23728: fadd            d4, d2, d1
    // 0xb2372c: stur            d4, [fp, #-0x60]
    // 0xb23730: cmp             w1, NULL
    // 0xb23734: b.ne            #0xb23740
    // 0xb23738: r1 = Null
    //     0xb23738: mov             x1, NULL
    // 0xb2373c: b               #0xb2376c
    // 0xb23740: LoadField: d1 = r1->field_f
    //     0xb23740: ldur            d1, [x1, #0xf]
    // 0xb23744: r1 = inline_Allocate_Double()
    //     0xb23744: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb23748: add             x1, x1, #0x10
    //     0xb2374c: cmp             x2, x1
    //     0xb23750: b.ls            #0xb23c6c
    //     0xb23754: str             x1, [THR, #0x50]  ; THR::top
    //     0xb23758: sub             x1, x1, #0xf
    //     0xb2375c: movz            x2, #0xe15c
    //     0xb23760: movk            x2, #0x3, lsl #16
    //     0xb23764: stur            x2, [x1, #-1]
    // 0xb23768: StoreField: r1->field_7 = d1
    //     0xb23768: stur            d1, [x1, #7]
    // 0xb2376c: cmp             w1, NULL
    // 0xb23770: b.ne            #0xb2377c
    // 0xb23774: d1 = 0.000000
    //     0xb23774: eor             v1.16b, v1.16b, v1.16b
    // 0xb23778: b               #0xb23780
    // 0xb2377c: LoadField: d1 = r1->field_7
    //     0xb2377c: ldur            d1, [x1, #7]
    // 0xb23780: ldur            x1, [fp, #-8]
    // 0xb23784: ldur            x2, [fp, #-0x28]
    // 0xb23788: stur            d1, [fp, #-0x58]
    // 0xb2378c: r0 = RelativeRect()
    //     0xb2378c: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xb23790: mov             x3, x0
    // 0xb23794: ldur            d0, [fp, #-0x70]
    // 0xb23798: stur            x3, [fp, #-0x18]
    // 0xb2379c: StoreField: r3->field_7 = d0
    //     0xb2379c: stur            d0, [x3, #7]
    // 0xb237a0: ldur            d0, [fp, #-0x68]
    // 0xb237a4: StoreField: r3->field_f = d0
    //     0xb237a4: stur            d0, [x3, #0xf]
    // 0xb237a8: ldur            d0, [fp, #-0x60]
    // 0xb237ac: ArrayStore: r3[0] = d0  ; List_8
    //     0xb237ac: stur            d0, [x3, #0x17]
    // 0xb237b0: ldur            d0, [fp, #-0x58]
    // 0xb237b4: StoreField: r3->field_1f = d0
    //     0xb237b4: stur            d0, [x3, #0x1f]
    // 0xb237b8: ldur            x4, [fp, #-8]
    // 0xb237bc: LoadField: r1 = r4->field_2f
    //     0xb237bc: ldur            w1, [x4, #0x2f]
    // 0xb237c0: DecompressPointer r1
    //     0xb237c0: add             x1, x1, HEAP, lsl #32
    // 0xb237c4: ldur            x5, [fp, #-0x28]
    // 0xb237c8: LoadField: r2 = r5->field_13
    //     0xb237c8: ldur            w2, [x5, #0x13]
    // 0xb237cc: DecompressPointer r2
    //     0xb237cc: add             x2, x2, HEAP, lsl #32
    // 0xb237d0: r0 = LoadClassIdInstr(r1)
    //     0xb237d0: ldur            x0, [x1, #-1]
    //     0xb237d4: ubfx            x0, x0, #0xc, #0x14
    // 0xb237d8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb237d8: sub             lr, x0, #0xfe
    //     0xb237dc: ldr             lr, [x21, lr, lsl #3]
    //     0xb237e0: blr             lr
    // 0xb237e4: r1 = 60
    //     0xb237e4: movz            x1, #0x3c
    // 0xb237e8: branchIfSmi(r0, 0xb237f4)
    //     0xb237e8: tbz             w0, #0, #0xb237f4
    // 0xb237ec: r1 = LoadClassIdInstr(r0)
    //     0xb237ec: ldur            x1, [x0, #-1]
    //     0xb237f0: ubfx            x1, x1, #0xc, #0x14
    // 0xb237f4: r16 = true
    //     0xb237f4: add             x16, NULL, #0x20  ; true
    // 0xb237f8: stp             x16, x0, [SP]
    // 0xb237fc: mov             x0, x1
    // 0xb23800: mov             lr, x0
    // 0xb23804: ldr             lr, [x21, lr, lsl #3]
    // 0xb23808: blr             lr
    // 0xb2380c: tbnz            w0, #4, #0xb23818
    // 0xb23810: r4 = Null
    //     0xb23810: mov             x4, NULL
    // 0xb23814: b               #0xb2382c
    // 0xb23818: ldur            x2, [fp, #-0x28]
    // 0xb2381c: r1 = Function '<anonymous closure>':.
    //     0xb2381c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57610] AnonymousClosure: (0xb23d24), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xb2354c)
    //     0xb23820: ldr             x1, [x1, #0x610]
    // 0xb23824: r0 = AllocateClosure()
    //     0xb23824: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23828: mov             x4, x0
    // 0xb2382c: ldur            x0, [fp, #-8]
    // 0xb23830: ldur            x3, [fp, #-0x28]
    // 0xb23834: stur            x4, [fp, #-0x30]
    // 0xb23838: r1 = <Widget>
    //     0xb23838: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb2383c: r2 = 0
    //     0xb2383c: movz            x2, #0
    // 0xb23840: r0 = _GrowableList()
    //     0xb23840: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb23844: mov             x4, x0
    // 0xb23848: ldur            x3, [fp, #-8]
    // 0xb2384c: stur            x4, [fp, #-0x38]
    // 0xb23850: LoadField: r1 = r3->field_2f
    //     0xb23850: ldur            w1, [x3, #0x2f]
    // 0xb23854: DecompressPointer r1
    //     0xb23854: add             x1, x1, HEAP, lsl #32
    // 0xb23858: ldur            x5, [fp, #-0x28]
    // 0xb2385c: LoadField: r2 = r5->field_13
    //     0xb2385c: ldur            w2, [x5, #0x13]
    // 0xb23860: DecompressPointer r2
    //     0xb23860: add             x2, x2, HEAP, lsl #32
    // 0xb23864: r0 = LoadClassIdInstr(r1)
    //     0xb23864: ldur            x0, [x1, #-1]
    //     0xb23868: ubfx            x0, x0, #0xc, #0x14
    // 0xb2386c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb2386c: sub             lr, x0, #0xfe
    //     0xb23870: ldr             lr, [x21, lr, lsl #3]
    //     0xb23874: blr             lr
    // 0xb23878: r1 = 60
    //     0xb23878: movz            x1, #0x3c
    // 0xb2387c: branchIfSmi(r0, 0xb23888)
    //     0xb2387c: tbz             w0, #0, #0xb23888
    // 0xb23880: r1 = LoadClassIdInstr(r0)
    //     0xb23880: ldur            x1, [x0, #-1]
    //     0xb23884: ubfx            x1, x1, #0xc, #0x14
    // 0xb23888: r16 = true
    //     0xb23888: add             x16, NULL, #0x20  ; true
    // 0xb2388c: stp             x16, x0, [SP]
    // 0xb23890: mov             x0, x1
    // 0xb23894: mov             lr, x0
    // 0xb23898: ldr             lr, [x21, lr, lsl #3]
    // 0xb2389c: blr             lr
    // 0xb238a0: tbnz            w0, #4, #0xb23904
    // 0xb238a4: ldur            x0, [fp, #-0x38]
    // 0xb238a8: LoadField: r1 = r0->field_b
    //     0xb238a8: ldur            w1, [x0, #0xb]
    // 0xb238ac: LoadField: r2 = r0->field_f
    //     0xb238ac: ldur            w2, [x0, #0xf]
    // 0xb238b0: DecompressPointer r2
    //     0xb238b0: add             x2, x2, HEAP, lsl #32
    // 0xb238b4: LoadField: r3 = r2->field_b
    //     0xb238b4: ldur            w3, [x2, #0xb]
    // 0xb238b8: r2 = LoadInt32Instr(r1)
    //     0xb238b8: sbfx            x2, x1, #1, #0x1f
    // 0xb238bc: stur            x2, [fp, #-0x40]
    // 0xb238c0: r1 = LoadInt32Instr(r3)
    //     0xb238c0: sbfx            x1, x3, #1, #0x1f
    // 0xb238c4: cmp             x2, x1
    // 0xb238c8: b.ne            #0xb238d4
    // 0xb238cc: mov             x1, x0
    // 0xb238d0: r0 = _growToNextCapacity()
    //     0xb238d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb238d4: ldur            x0, [fp, #-0x38]
    // 0xb238d8: ldur            x1, [fp, #-0x40]
    // 0xb238dc: add             x2, x1, #1
    // 0xb238e0: lsl             x3, x2, #1
    // 0xb238e4: StoreField: r0->field_b = r3
    //     0xb238e4: stur            w3, [x0, #0xb]
    // 0xb238e8: LoadField: r2 = r0->field_f
    //     0xb238e8: ldur            w2, [x0, #0xf]
    // 0xb238ec: DecompressPointer r2
    //     0xb238ec: add             x2, x2, HEAP, lsl #32
    // 0xb238f0: add             x3, x2, x1, lsl #2
    // 0xb238f4: r16 = Instance_Icon
    //     0xb238f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xb238f8: ldr             x16, [x16, #0xa48]
    // 0xb238fc: StoreField: r3->field_f = r16
    //     0xb238fc: stur            w16, [x3, #0xf]
    // 0xb23900: b               #0xb23908
    // 0xb23904: ldur            x0, [fp, #-0x38]
    // 0xb23908: LoadField: r1 = r0->field_b
    //     0xb23908: ldur            w1, [x0, #0xb]
    // 0xb2390c: LoadField: r2 = r0->field_f
    //     0xb2390c: ldur            w2, [x0, #0xf]
    // 0xb23910: DecompressPointer r2
    //     0xb23910: add             x2, x2, HEAP, lsl #32
    // 0xb23914: LoadField: r3 = r2->field_b
    //     0xb23914: ldur            w3, [x2, #0xb]
    // 0xb23918: r2 = LoadInt32Instr(r1)
    //     0xb23918: sbfx            x2, x1, #1, #0x1f
    // 0xb2391c: stur            x2, [fp, #-0x40]
    // 0xb23920: r1 = LoadInt32Instr(r3)
    //     0xb23920: sbfx            x1, x3, #1, #0x1f
    // 0xb23924: cmp             x2, x1
    // 0xb23928: b.ne            #0xb23934
    // 0xb2392c: mov             x1, x0
    // 0xb23930: r0 = _growToNextCapacity()
    //     0xb23930: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb23934: ldur            x1, [fp, #-8]
    // 0xb23938: ldur            x4, [fp, #-0x28]
    // 0xb2393c: ldur            x3, [fp, #-0x38]
    // 0xb23940: ldur            x0, [fp, #-0x40]
    // 0xb23944: add             x2, x0, #1
    // 0xb23948: lsl             x5, x2, #1
    // 0xb2394c: StoreField: r3->field_b = r5
    //     0xb2394c: stur            w5, [x3, #0xb]
    // 0xb23950: LoadField: r2 = r3->field_f
    //     0xb23950: ldur            w2, [x3, #0xf]
    // 0xb23954: DecompressPointer r2
    //     0xb23954: add             x2, x2, HEAP, lsl #32
    // 0xb23958: add             x5, x2, x0, lsl #2
    // 0xb2395c: r16 = Instance_SizedBox
    //     0xb2395c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb23960: ldr             x16, [x16, #0xa50]
    // 0xb23964: StoreField: r5->field_f = r16
    //     0xb23964: stur            w16, [x5, #0xf]
    // 0xb23968: LoadField: r0 = r1->field_2f
    //     0xb23968: ldur            w0, [x1, #0x2f]
    // 0xb2396c: DecompressPointer r0
    //     0xb2396c: add             x0, x0, HEAP, lsl #32
    // 0xb23970: LoadField: r2 = r4->field_13
    //     0xb23970: ldur            w2, [x4, #0x13]
    // 0xb23974: DecompressPointer r2
    //     0xb23974: add             x2, x2, HEAP, lsl #32
    // 0xb23978: r1 = LoadClassIdInstr(r0)
    //     0xb23978: ldur            x1, [x0, #-1]
    //     0xb2397c: ubfx            x1, x1, #0xc, #0x14
    // 0xb23980: mov             x16, x0
    // 0xb23984: mov             x0, x1
    // 0xb23988: mov             x1, x16
    // 0xb2398c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb2398c: sub             lr, x0, #0xfe
    //     0xb23990: ldr             lr, [x21, lr, lsl #3]
    //     0xb23994: blr             lr
    // 0xb23998: r1 = 60
    //     0xb23998: movz            x1, #0x3c
    // 0xb2399c: branchIfSmi(r0, 0xb239a8)
    //     0xb2399c: tbz             w0, #0, #0xb239a8
    // 0xb239a0: r1 = LoadClassIdInstr(r0)
    //     0xb239a0: ldur            x1, [x0, #-1]
    //     0xb239a4: ubfx            x1, x1, #0xc, #0x14
    // 0xb239a8: r16 = true
    //     0xb239a8: add             x16, NULL, #0x20  ; true
    // 0xb239ac: stp             x16, x0, [SP]
    // 0xb239b0: mov             x0, x1
    // 0xb239b4: mov             lr, x0
    // 0xb239b8: ldr             lr, [x21, lr, lsl #3]
    // 0xb239bc: blr             lr
    // 0xb239c0: tbnz            w0, #4, #0xb239d0
    // 0xb239c4: r0 = "Flagged"
    //     0xb239c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xb239c8: ldr             x0, [x0, #0xa58]
    // 0xb239cc: b               #0xb239d8
    // 0xb239d0: r0 = "Flag as abusive"
    //     0xb239d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xb239d4: ldr             x0, [x0, #0xa60]
    // 0xb239d8: ldur            x1, [fp, #-0x10]
    // 0xb239dc: stur            x0, [fp, #-8]
    // 0xb239e0: r0 = of()
    //     0xb239e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb239e4: LoadField: r1 = r0->field_87
    //     0xb239e4: ldur            w1, [x0, #0x87]
    // 0xb239e8: DecompressPointer r1
    //     0xb239e8: add             x1, x1, HEAP, lsl #32
    // 0xb239ec: LoadField: r0 = r1->field_33
    //     0xb239ec: ldur            w0, [x1, #0x33]
    // 0xb239f0: DecompressPointer r0
    //     0xb239f0: add             x0, x0, HEAP, lsl #32
    // 0xb239f4: cmp             w0, NULL
    // 0xb239f8: b.ne            #0xb23a04
    // 0xb239fc: r2 = Null
    //     0xb239fc: mov             x2, NULL
    // 0xb23a00: b               #0xb23a28
    // 0xb23a04: r16 = 12.000000
    //     0xb23a04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb23a08: ldr             x16, [x16, #0x9e8]
    // 0xb23a0c: r30 = Instance_Color
    //     0xb23a0c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb23a10: stp             lr, x16, [SP]
    // 0xb23a14: mov             x1, x0
    // 0xb23a18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb23a18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb23a1c: ldr             x4, [x4, #0xaa0]
    // 0xb23a20: r0 = copyWith()
    //     0xb23a20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb23a24: mov             x2, x0
    // 0xb23a28: ldur            x1, [fp, #-0x38]
    // 0xb23a2c: ldur            x0, [fp, #-8]
    // 0xb23a30: stur            x2, [fp, #-0x48]
    // 0xb23a34: r0 = Text()
    //     0xb23a34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb23a38: mov             x2, x0
    // 0xb23a3c: ldur            x0, [fp, #-8]
    // 0xb23a40: stur            x2, [fp, #-0x50]
    // 0xb23a44: StoreField: r2->field_b = r0
    //     0xb23a44: stur            w0, [x2, #0xb]
    // 0xb23a48: ldur            x0, [fp, #-0x48]
    // 0xb23a4c: StoreField: r2->field_13 = r0
    //     0xb23a4c: stur            w0, [x2, #0x13]
    // 0xb23a50: ldur            x0, [fp, #-0x38]
    // 0xb23a54: LoadField: r1 = r0->field_b
    //     0xb23a54: ldur            w1, [x0, #0xb]
    // 0xb23a58: LoadField: r3 = r0->field_f
    //     0xb23a58: ldur            w3, [x0, #0xf]
    // 0xb23a5c: DecompressPointer r3
    //     0xb23a5c: add             x3, x3, HEAP, lsl #32
    // 0xb23a60: LoadField: r4 = r3->field_b
    //     0xb23a60: ldur            w4, [x3, #0xb]
    // 0xb23a64: r3 = LoadInt32Instr(r1)
    //     0xb23a64: sbfx            x3, x1, #1, #0x1f
    // 0xb23a68: stur            x3, [fp, #-0x40]
    // 0xb23a6c: r1 = LoadInt32Instr(r4)
    //     0xb23a6c: sbfx            x1, x4, #1, #0x1f
    // 0xb23a70: cmp             x3, x1
    // 0xb23a74: b.ne            #0xb23a80
    // 0xb23a78: mov             x1, x0
    // 0xb23a7c: r0 = _growToNextCapacity()
    //     0xb23a7c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb23a80: ldur            x4, [fp, #-0x30]
    // 0xb23a84: ldur            x2, [fp, #-0x38]
    // 0xb23a88: ldur            x3, [fp, #-0x40]
    // 0xb23a8c: add             x0, x3, #1
    // 0xb23a90: lsl             x1, x0, #1
    // 0xb23a94: StoreField: r2->field_b = r1
    //     0xb23a94: stur            w1, [x2, #0xb]
    // 0xb23a98: LoadField: r1 = r2->field_f
    //     0xb23a98: ldur            w1, [x2, #0xf]
    // 0xb23a9c: DecompressPointer r1
    //     0xb23a9c: add             x1, x1, HEAP, lsl #32
    // 0xb23aa0: ldur            x0, [fp, #-0x50]
    // 0xb23aa4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb23aa4: add             x25, x1, x3, lsl #2
    //     0xb23aa8: add             x25, x25, #0xf
    //     0xb23aac: str             w0, [x25]
    //     0xb23ab0: tbz             w0, #0, #0xb23acc
    //     0xb23ab4: ldurb           w16, [x1, #-1]
    //     0xb23ab8: ldurb           w17, [x0, #-1]
    //     0xb23abc: and             x16, x17, x16, lsr #2
    //     0xb23ac0: tst             x16, HEAP, lsr #32
    //     0xb23ac4: b.eq            #0xb23acc
    //     0xb23ac8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb23acc: r0 = Row()
    //     0xb23acc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb23ad0: mov             x2, x0
    // 0xb23ad4: r0 = Instance_Axis
    //     0xb23ad4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb23ad8: stur            x2, [fp, #-8]
    // 0xb23adc: StoreField: r2->field_f = r0
    //     0xb23adc: stur            w0, [x2, #0xf]
    // 0xb23ae0: r0 = Instance_MainAxisAlignment
    //     0xb23ae0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb23ae4: ldr             x0, [x0, #0xa08]
    // 0xb23ae8: StoreField: r2->field_13 = r0
    //     0xb23ae8: stur            w0, [x2, #0x13]
    // 0xb23aec: r0 = Instance_MainAxisSize
    //     0xb23aec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb23af0: ldr             x0, [x0, #0xa10]
    // 0xb23af4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb23af4: stur            w0, [x2, #0x17]
    // 0xb23af8: r0 = Instance_CrossAxisAlignment
    //     0xb23af8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb23afc: ldr             x0, [x0, #0xa18]
    // 0xb23b00: StoreField: r2->field_1b = r0
    //     0xb23b00: stur            w0, [x2, #0x1b]
    // 0xb23b04: r0 = Instance_VerticalDirection
    //     0xb23b04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb23b08: ldr             x0, [x0, #0xa20]
    // 0xb23b0c: StoreField: r2->field_23 = r0
    //     0xb23b0c: stur            w0, [x2, #0x23]
    // 0xb23b10: r0 = Instance_Clip
    //     0xb23b10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb23b14: ldr             x0, [x0, #0x38]
    // 0xb23b18: StoreField: r2->field_2b = r0
    //     0xb23b18: stur            w0, [x2, #0x2b]
    // 0xb23b1c: StoreField: r2->field_2f = rZR
    //     0xb23b1c: stur            xzr, [x2, #0x2f]
    // 0xb23b20: ldur            x0, [fp, #-0x38]
    // 0xb23b24: StoreField: r2->field_b = r0
    //     0xb23b24: stur            w0, [x2, #0xb]
    // 0xb23b28: r1 = <String>
    //     0xb23b28: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb23b2c: r0 = PopupMenuItem()
    //     0xb23b2c: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xb23b30: mov             x3, x0
    // 0xb23b34: r0 = "flag"
    //     0xb23b34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb23b38: ldr             x0, [x0, #0xa68]
    // 0xb23b3c: stur            x3, [fp, #-0x38]
    // 0xb23b40: StoreField: r3->field_f = r0
    //     0xb23b40: stur            w0, [x3, #0xf]
    // 0xb23b44: ldur            x0, [fp, #-0x30]
    // 0xb23b48: StoreField: r3->field_13 = r0
    //     0xb23b48: stur            w0, [x3, #0x13]
    // 0xb23b4c: r0 = true
    //     0xb23b4c: add             x0, NULL, #0x20  ; true
    // 0xb23b50: ArrayStore: r3[0] = r0  ; List_4
    //     0xb23b50: stur            w0, [x3, #0x17]
    // 0xb23b54: d0 = 25.000000
    //     0xb23b54: fmov            d0, #25.00000000
    // 0xb23b58: StoreField: r3->field_1b = d0
    //     0xb23b58: stur            d0, [x3, #0x1b]
    // 0xb23b5c: ldur            x0, [fp, #-8]
    // 0xb23b60: StoreField: r3->field_33 = r0
    //     0xb23b60: stur            w0, [x3, #0x33]
    // 0xb23b64: r1 = Null
    //     0xb23b64: mov             x1, NULL
    // 0xb23b68: r2 = 2
    //     0xb23b68: movz            x2, #0x2
    // 0xb23b6c: r0 = AllocateArray()
    //     0xb23b6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb23b70: mov             x2, x0
    // 0xb23b74: ldur            x0, [fp, #-0x38]
    // 0xb23b78: stur            x2, [fp, #-8]
    // 0xb23b7c: StoreField: r2->field_f = r0
    //     0xb23b7c: stur            w0, [x2, #0xf]
    // 0xb23b80: r1 = <PopupMenuEntry<String>>
    //     0xb23b80: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xb23b84: ldr             x1, [x1, #0xa70]
    // 0xb23b88: r0 = AllocateGrowableArray()
    //     0xb23b88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb23b8c: mov             x1, x0
    // 0xb23b90: ldur            x0, [fp, #-8]
    // 0xb23b94: StoreField: r1->field_f = r0
    //     0xb23b94: stur            w0, [x1, #0xf]
    // 0xb23b98: r0 = 2
    //     0xb23b98: movz            x0, #0x2
    // 0xb23b9c: StoreField: r1->field_b = r0
    //     0xb23b9c: stur            w0, [x1, #0xb]
    // 0xb23ba0: r16 = <String>
    //     0xb23ba0: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb23ba4: ldur            lr, [fp, #-0x10]
    // 0xb23ba8: stp             lr, x16, [SP, #0x20]
    // 0xb23bac: ldur            x16, [fp, #-0x18]
    // 0xb23bb0: stp             x16, x1, [SP, #0x10]
    // 0xb23bb4: r16 = Instance_Color
    //     0xb23bb4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb23bb8: ldur            lr, [fp, #-0x20]
    // 0xb23bbc: stp             lr, x16, [SP]
    // 0xb23bc0: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xb23bc0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xb23bc4: ldr             x4, [x4, #0xa78]
    // 0xb23bc8: r0 = showMenu()
    //     0xb23bc8: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xb23bcc: ldur            x2, [fp, #-0x28]
    // 0xb23bd0: r1 = Function '<anonymous closure>':.
    //     0xb23bd0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57618] AnonymousClosure: (0xb23c90), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xb2354c)
    //     0xb23bd4: ldr             x1, [x1, #0x618]
    // 0xb23bd8: stur            x0, [fp, #-8]
    // 0xb23bdc: r0 = AllocateClosure()
    //     0xb23bdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23be0: r16 = <Null?>
    //     0xb23be0: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xb23be4: ldur            lr, [fp, #-8]
    // 0xb23be8: stp             lr, x16, [SP, #8]
    // 0xb23bec: str             x0, [SP]
    // 0xb23bf0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb23bf0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb23bf4: r0 = then()
    //     0xb23bf4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb23bf8: r0 = Null
    //     0xb23bf8: mov             x0, NULL
    // 0xb23bfc: LeaveFrame
    //     0xb23bfc: mov             SP, fp
    //     0xb23c00: ldp             fp, lr, [SP], #0x10
    // 0xb23c04: ret
    //     0xb23c04: ret             
    // 0xb23c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb23c08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb23c0c: b               #0xb23580
    // 0xb23c10: SaveReg d0
    //     0xb23c10: str             q0, [SP, #-0x10]!
    // 0xb23c14: stp             x0, x1, [SP, #-0x10]!
    // 0xb23c18: r0 = AllocateDouble()
    //     0xb23c18: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb23c1c: mov             x2, x0
    // 0xb23c20: ldp             x0, x1, [SP], #0x10
    // 0xb23c24: RestoreReg d0
    //     0xb23c24: ldr             q0, [SP], #0x10
    // 0xb23c28: b               #0xb2365c
    // 0xb23c2c: stp             q0, q1, [SP, #-0x20]!
    // 0xb23c30: stp             x0, x1, [SP, #-0x10]!
    // 0xb23c34: r0 = AllocateDouble()
    //     0xb23c34: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb23c38: mov             x2, x0
    // 0xb23c3c: ldp             x0, x1, [SP], #0x10
    // 0xb23c40: ldp             q0, q1, [SP], #0x20
    // 0xb23c44: b               #0xb236b0
    // 0xb23c48: stp             q2, q3, [SP, #-0x20]!
    // 0xb23c4c: stp             q0, q1, [SP, #-0x20]!
    // 0xb23c50: stp             x0, x1, [SP, #-0x10]!
    // 0xb23c54: r0 = AllocateDouble()
    //     0xb23c54: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb23c58: mov             x2, x0
    // 0xb23c5c: ldp             x0, x1, [SP], #0x10
    // 0xb23c60: ldp             q0, q1, [SP], #0x20
    // 0xb23c64: ldp             q2, q3, [SP], #0x20
    // 0xb23c68: b               #0xb23710
    // 0xb23c6c: stp             q3, q4, [SP, #-0x20]!
    // 0xb23c70: stp             q0, q1, [SP, #-0x20]!
    // 0xb23c74: SaveReg r0
    //     0xb23c74: str             x0, [SP, #-8]!
    // 0xb23c78: r0 = AllocateDouble()
    //     0xb23c78: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb23c7c: mov             x1, x0
    // 0xb23c80: RestoreReg r0
    //     0xb23c80: ldr             x0, [SP], #8
    // 0xb23c84: ldp             q0, q1, [SP], #0x20
    // 0xb23c88: ldp             q3, q4, [SP], #0x20
    // 0xb23c8c: b               #0xb23768
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb23c90, size: 0x94
    // 0xb23c90: EnterFrame
    //     0xb23c90: stp             fp, lr, [SP, #-0x10]!
    //     0xb23c94: mov             fp, SP
    // 0xb23c98: AllocStack(0x20)
    //     0xb23c98: sub             SP, SP, #0x20
    // 0xb23c9c: SetupParameters()
    //     0xb23c9c: ldr             x0, [fp, #0x18]
    //     0xb23ca0: ldur            w2, [x0, #0x17]
    //     0xb23ca4: add             x2, x2, HEAP, lsl #32
    //     0xb23ca8: stur            x2, [fp, #-8]
    // 0xb23cac: CheckStackOverflow
    //     0xb23cac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb23cb0: cmp             SP, x16
    //     0xb23cb4: b.ls            #0xb23d1c
    // 0xb23cb8: ldr             x0, [fp, #0x10]
    // 0xb23cbc: r1 = LoadClassIdInstr(r0)
    //     0xb23cbc: ldur            x1, [x0, #-1]
    //     0xb23cc0: ubfx            x1, x1, #0xc, #0x14
    // 0xb23cc4: r16 = "flag"
    //     0xb23cc4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb23cc8: ldr             x16, [x16, #0xa68]
    // 0xb23ccc: stp             x16, x0, [SP]
    // 0xb23cd0: mov             x0, x1
    // 0xb23cd4: mov             lr, x0
    // 0xb23cd8: ldr             lr, [x21, lr, lsl #3]
    // 0xb23cdc: blr             lr
    // 0xb23ce0: tbnz            w0, #4, #0xb23d0c
    // 0xb23ce4: ldur            x2, [fp, #-8]
    // 0xb23ce8: LoadField: r0 = r2->field_f
    //     0xb23ce8: ldur            w0, [x2, #0xf]
    // 0xb23cec: DecompressPointer r0
    //     0xb23cec: add             x0, x0, HEAP, lsl #32
    // 0xb23cf0: stur            x0, [fp, #-0x10]
    // 0xb23cf4: r1 = Function '<anonymous closure>':.
    //     0xb23cf4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57620] AnonymousClosure: (0xaa4b44), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xb23cf8: ldr             x1, [x1, #0x620]
    // 0xb23cfc: r0 = AllocateClosure()
    //     0xb23cfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23d00: ldur            x1, [fp, #-0x10]
    // 0xb23d04: mov             x2, x0
    // 0xb23d08: r0 = setState()
    //     0xb23d08: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb23d0c: r0 = Null
    //     0xb23d0c: mov             x0, NULL
    // 0xb23d10: LeaveFrame
    //     0xb23d10: mov             SP, fp
    //     0xb23d14: ldp             fp, lr, [SP], #0x10
    // 0xb23d18: ret
    //     0xb23d18: ret             
    // 0xb23d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb23d1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb23d20: b               #0xb23cb8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb23d24, size: 0x60
    // 0xb23d24: EnterFrame
    //     0xb23d24: stp             fp, lr, [SP, #-0x10]!
    //     0xb23d28: mov             fp, SP
    // 0xb23d2c: AllocStack(0x8)
    //     0xb23d2c: sub             SP, SP, #8
    // 0xb23d30: SetupParameters()
    //     0xb23d30: ldr             x0, [fp, #0x10]
    //     0xb23d34: ldur            w2, [x0, #0x17]
    //     0xb23d38: add             x2, x2, HEAP, lsl #32
    // 0xb23d3c: CheckStackOverflow
    //     0xb23d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb23d40: cmp             SP, x16
    //     0xb23d44: b.ls            #0xb23d7c
    // 0xb23d48: LoadField: r0 = r2->field_f
    //     0xb23d48: ldur            w0, [x2, #0xf]
    // 0xb23d4c: DecompressPointer r0
    //     0xb23d4c: add             x0, x0, HEAP, lsl #32
    // 0xb23d50: stur            x0, [fp, #-8]
    // 0xb23d54: r1 = Function '<anonymous closure>':.
    //     0xb23d54: add             x1, PP, #0x57, lsl #12  ; [pp+0x57628] AnonymousClosure: (0xaa4b44), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xb23d58: ldr             x1, [x1, #0x628]
    // 0xb23d5c: r0 = AllocateClosure()
    //     0xb23d5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23d60: ldur            x1, [fp, #-8]
    // 0xb23d64: mov             x2, x0
    // 0xb23d68: r0 = setState()
    //     0xb23d68: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb23d6c: r0 = Null
    //     0xb23d6c: mov             x0, NULL
    // 0xb23d70: LeaveFrame
    //     0xb23d70: mov             SP, fp
    //     0xb23d74: ldp             fp, lr, [SP], #0x10
    // 0xb23d78: ret
    //     0xb23d78: ret             
    // 0xb23d7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb23d7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb23d80: b               #0xb23d48
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb23d84, size: 0x6d8
    // 0xb23d84: EnterFrame
    //     0xb23d84: stp             fp, lr, [SP, #-0x10]!
    //     0xb23d88: mov             fp, SP
    // 0xb23d8c: AllocStack(0x78)
    //     0xb23d8c: sub             SP, SP, #0x78
    // 0xb23d90: SetupParameters()
    //     0xb23d90: ldr             x0, [fp, #0x20]
    //     0xb23d94: ldur            w3, [x0, #0x17]
    //     0xb23d98: add             x3, x3, HEAP, lsl #32
    //     0xb23d9c: stur            x3, [fp, #-0x18]
    // 0xb23da0: CheckStackOverflow
    //     0xb23da0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb23da4: cmp             SP, x16
    //     0xb23da8: b.ls            #0xb2441c
    // 0xb23dac: LoadField: r0 = r3->field_f
    //     0xb23dac: ldur            w0, [x3, #0xf]
    // 0xb23db0: DecompressPointer r0
    //     0xb23db0: add             x0, x0, HEAP, lsl #32
    // 0xb23db4: LoadField: r2 = r0->field_37
    //     0xb23db4: ldur            w2, [x0, #0x37]
    // 0xb23db8: DecompressPointer r2
    //     0xb23db8: add             x2, x2, HEAP, lsl #32
    // 0xb23dbc: LoadField: r0 = r2->field_b
    //     0xb23dbc: ldur            w0, [x2, #0xb]
    // 0xb23dc0: ldr             x1, [fp, #0x10]
    // 0xb23dc4: r4 = LoadInt32Instr(r1)
    //     0xb23dc4: sbfx            x4, x1, #1, #0x1f
    //     0xb23dc8: tbz             w1, #0, #0xb23dd0
    //     0xb23dcc: ldur            x4, [x1, #7]
    // 0xb23dd0: stur            x4, [fp, #-0x10]
    // 0xb23dd4: r1 = LoadInt32Instr(r0)
    //     0xb23dd4: sbfx            x1, x0, #1, #0x1f
    // 0xb23dd8: mov             x0, x1
    // 0xb23ddc: mov             x1, x4
    // 0xb23de0: cmp             x1, x0
    // 0xb23de4: b.hs            #0xb24424
    // 0xb23de8: LoadField: r0 = r2->field_f
    //     0xb23de8: ldur            w0, [x2, #0xf]
    // 0xb23dec: DecompressPointer r0
    //     0xb23dec: add             x0, x0, HEAP, lsl #32
    // 0xb23df0: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0xb23df0: add             x16, x0, x4, lsl #2
    //     0xb23df4: ldur            w5, [x16, #0xf]
    // 0xb23df8: DecompressPointer r5
    //     0xb23df8: add             x5, x5, HEAP, lsl #32
    // 0xb23dfc: stur            x5, [fp, #-8]
    // 0xb23e00: r1 = <Widget>
    //     0xb23e00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb23e04: r2 = 0
    //     0xb23e04: movz            x2, #0
    // 0xb23e08: r0 = _GrowableList()
    //     0xb23e08: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb23e0c: mov             x2, x0
    // 0xb23e10: ldur            x1, [fp, #-8]
    // 0xb23e14: stur            x2, [fp, #-0x20]
    // 0xb23e18: LoadField: r0 = r1->field_f
    //     0xb23e18: ldur            w0, [x1, #0xf]
    // 0xb23e1c: DecompressPointer r0
    //     0xb23e1c: add             x0, x0, HEAP, lsl #32
    // 0xb23e20: r3 = LoadClassIdInstr(r0)
    //     0xb23e20: ldur            x3, [x0, #-1]
    //     0xb23e24: ubfx            x3, x3, #0xc, #0x14
    // 0xb23e28: r16 = "image"
    //     0xb23e28: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb23e2c: stp             x16, x0, [SP]
    // 0xb23e30: mov             x0, x3
    // 0xb23e34: mov             lr, x0
    // 0xb23e38: ldr             lr, [x21, lr, lsl #3]
    // 0xb23e3c: blr             lr
    // 0xb23e40: tbnz            w0, #4, #0xb2402c
    // 0xb23e44: ldur            x0, [fp, #-0x18]
    // 0xb23e48: LoadField: r1 = r0->field_f
    //     0xb23e48: ldur            w1, [x0, #0xf]
    // 0xb23e4c: DecompressPointer r1
    //     0xb23e4c: add             x1, x1, HEAP, lsl #32
    // 0xb23e50: LoadField: r2 = r1->field_2b
    //     0xb23e50: ldur            w2, [x1, #0x2b]
    // 0xb23e54: DecompressPointer r2
    //     0xb23e54: add             x2, x2, HEAP, lsl #32
    // 0xb23e58: tbnz            w2, #4, #0xb23e68
    // 0xb23e5c: r3 = Instance_BoxFit
    //     0xb23e5c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb23e60: ldr             x3, [x3, #0x118]
    // 0xb23e64: b               #0xb23e70
    // 0xb23e68: r3 = Instance_BoxFit
    //     0xb23e68: add             x3, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xb23e6c: ldr             x3, [x3, #0xf38]
    // 0xb23e70: ldur            x1, [fp, #-8]
    // 0xb23e74: stur            x3, [fp, #-0x30]
    // 0xb23e78: LoadField: r2 = r1->field_13
    //     0xb23e78: ldur            w2, [x1, #0x13]
    // 0xb23e7c: DecompressPointer r2
    //     0xb23e7c: add             x2, x2, HEAP, lsl #32
    // 0xb23e80: cmp             w2, NULL
    // 0xb23e84: b.ne            #0xb23e90
    // 0xb23e88: r5 = ""
    //     0xb23e88: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb23e8c: b               #0xb23e94
    // 0xb23e90: mov             x5, x2
    // 0xb23e94: ldur            x4, [fp, #-0x20]
    // 0xb23e98: stur            x5, [fp, #-0x28]
    // 0xb23e9c: r1 = Function '<anonymous closure>':.
    //     0xb23e9c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57630] AnonymousClosure: (0xaa613c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb23ea0: ldr             x1, [x1, #0x630]
    // 0xb23ea4: r2 = Null
    //     0xb23ea4: mov             x2, NULL
    // 0xb23ea8: r0 = AllocateClosure()
    //     0xb23ea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23eac: r1 = Function '<anonymous closure>':.
    //     0xb23eac: add             x1, PP, #0x57, lsl #12  ; [pp+0x57638] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb23eb0: ldr             x1, [x1, #0x638]
    // 0xb23eb4: r2 = Null
    //     0xb23eb4: mov             x2, NULL
    // 0xb23eb8: stur            x0, [fp, #-0x38]
    // 0xb23ebc: r0 = AllocateClosure()
    //     0xb23ebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb23ec0: stur            x0, [fp, #-0x40]
    // 0xb23ec4: r0 = CachedNetworkImage()
    //     0xb23ec4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb23ec8: stur            x0, [fp, #-0x48]
    // 0xb23ecc: ldur            x16, [fp, #-0x30]
    // 0xb23ed0: r30 = inf
    //     0xb23ed0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb23ed4: ldr             lr, [lr, #0x9f8]
    // 0xb23ed8: stp             lr, x16, [SP, #0x10]
    // 0xb23edc: ldur            x16, [fp, #-0x38]
    // 0xb23ee0: ldur            lr, [fp, #-0x40]
    // 0xb23ee4: stp             lr, x16, [SP]
    // 0xb23ee8: mov             x1, x0
    // 0xb23eec: ldur            x2, [fp, #-0x28]
    // 0xb23ef0: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb23ef0: add             x4, PP, #0x54, lsl #12  ; [pp+0x54f70] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb23ef4: ldr             x4, [x4, #0xf70]
    // 0xb23ef8: r0 = CachedNetworkImage()
    //     0xb23ef8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb23efc: r1 = Null
    //     0xb23efc: mov             x1, NULL
    // 0xb23f00: r2 = 2
    //     0xb23f00: movz            x2, #0x2
    // 0xb23f04: r0 = AllocateArray()
    //     0xb23f04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb23f08: mov             x2, x0
    // 0xb23f0c: ldur            x0, [fp, #-0x48]
    // 0xb23f10: stur            x2, [fp, #-0x28]
    // 0xb23f14: StoreField: r2->field_f = r0
    //     0xb23f14: stur            w0, [x2, #0xf]
    // 0xb23f18: r1 = <Widget>
    //     0xb23f18: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb23f1c: r0 = AllocateGrowableArray()
    //     0xb23f1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb23f20: mov             x1, x0
    // 0xb23f24: ldur            x0, [fp, #-0x28]
    // 0xb23f28: stur            x1, [fp, #-0x30]
    // 0xb23f2c: StoreField: r1->field_f = r0
    //     0xb23f2c: stur            w0, [x1, #0xf]
    // 0xb23f30: r0 = 2
    //     0xb23f30: movz            x0, #0x2
    // 0xb23f34: StoreField: r1->field_b = r0
    //     0xb23f34: stur            w0, [x1, #0xb]
    // 0xb23f38: r0 = Stack()
    //     0xb23f38: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb23f3c: mov             x1, x0
    // 0xb23f40: r0 = Instance_AlignmentDirectional
    //     0xb23f40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb23f44: ldr             x0, [x0, #0xd08]
    // 0xb23f48: stur            x1, [fp, #-0x28]
    // 0xb23f4c: StoreField: r1->field_f = r0
    //     0xb23f4c: stur            w0, [x1, #0xf]
    // 0xb23f50: r2 = Instance_StackFit
    //     0xb23f50: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb23f54: ldr             x2, [x2, #0xfa8]
    // 0xb23f58: ArrayStore: r1[0] = r2  ; List_4
    //     0xb23f58: stur            w2, [x1, #0x17]
    // 0xb23f5c: r3 = Instance_Clip
    //     0xb23f5c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb23f60: ldr             x3, [x3, #0x7e0]
    // 0xb23f64: StoreField: r1->field_1b = r3
    //     0xb23f64: stur            w3, [x1, #0x1b]
    // 0xb23f68: ldur            x4, [fp, #-0x30]
    // 0xb23f6c: StoreField: r1->field_b = r4
    //     0xb23f6c: stur            w4, [x1, #0xb]
    // 0xb23f70: r0 = GestureDetector()
    //     0xb23f70: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb23f74: stur            x0, [fp, #-0x30]
    // 0xb23f78: ldur            x16, [fp, #-0x28]
    // 0xb23f7c: str             x16, [SP]
    // 0xb23f80: mov             x1, x0
    // 0xb23f84: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xb23f84: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfb0] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xb23f88: ldr             x4, [x4, #0xfb0]
    // 0xb23f8c: r0 = GestureDetector()
    //     0xb23f8c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb23f90: r0 = Center()
    //     0xb23f90: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb23f94: mov             x2, x0
    // 0xb23f98: r0 = Instance_Alignment
    //     0xb23f98: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb23f9c: ldr             x0, [x0, #0xb10]
    // 0xb23fa0: stur            x2, [fp, #-0x28]
    // 0xb23fa4: StoreField: r2->field_f = r0
    //     0xb23fa4: stur            w0, [x2, #0xf]
    // 0xb23fa8: ldur            x0, [fp, #-0x30]
    // 0xb23fac: StoreField: r2->field_b = r0
    //     0xb23fac: stur            w0, [x2, #0xb]
    // 0xb23fb0: ldur            x0, [fp, #-0x20]
    // 0xb23fb4: LoadField: r1 = r0->field_b
    //     0xb23fb4: ldur            w1, [x0, #0xb]
    // 0xb23fb8: LoadField: r3 = r0->field_f
    //     0xb23fb8: ldur            w3, [x0, #0xf]
    // 0xb23fbc: DecompressPointer r3
    //     0xb23fbc: add             x3, x3, HEAP, lsl #32
    // 0xb23fc0: LoadField: r4 = r3->field_b
    //     0xb23fc0: ldur            w4, [x3, #0xb]
    // 0xb23fc4: r3 = LoadInt32Instr(r1)
    //     0xb23fc4: sbfx            x3, x1, #1, #0x1f
    // 0xb23fc8: stur            x3, [fp, #-0x50]
    // 0xb23fcc: r1 = LoadInt32Instr(r4)
    //     0xb23fcc: sbfx            x1, x4, #1, #0x1f
    // 0xb23fd0: cmp             x3, x1
    // 0xb23fd4: b.ne            #0xb23fe0
    // 0xb23fd8: mov             x1, x0
    // 0xb23fdc: r0 = _growToNextCapacity()
    //     0xb23fdc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb23fe0: ldur            x2, [fp, #-0x20]
    // 0xb23fe4: ldur            x3, [fp, #-0x50]
    // 0xb23fe8: add             x0, x3, #1
    // 0xb23fec: lsl             x1, x0, #1
    // 0xb23ff0: StoreField: r2->field_b = r1
    //     0xb23ff0: stur            w1, [x2, #0xb]
    // 0xb23ff4: LoadField: r1 = r2->field_f
    //     0xb23ff4: ldur            w1, [x2, #0xf]
    // 0xb23ff8: DecompressPointer r1
    //     0xb23ff8: add             x1, x1, HEAP, lsl #32
    // 0xb23ffc: ldur            x0, [fp, #-0x28]
    // 0xb24000: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb24000: add             x25, x1, x3, lsl #2
    //     0xb24004: add             x25, x25, #0xf
    //     0xb24008: str             w0, [x25]
    //     0xb2400c: tbz             w0, #0, #0xb24028
    //     0xb24010: ldurb           w16, [x1, #-1]
    //     0xb24014: ldurb           w17, [x0, #-1]
    //     0xb24018: and             x16, x17, x16, lsr #2
    //     0xb2401c: tst             x16, HEAP, lsr #32
    //     0xb24020: b.eq            #0xb24028
    //     0xb24024: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24028: b               #0xb24110
    // 0xb2402c: ldur            x2, [fp, #-0x20]
    // 0xb24030: ldur            x1, [fp, #-8]
    // 0xb24034: r0 = Instance_Alignment
    //     0xb24034: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb24038: ldr             x0, [x0, #0xb10]
    // 0xb2403c: LoadField: r3 = r1->field_13
    //     0xb2403c: ldur            w3, [x1, #0x13]
    // 0xb24040: DecompressPointer r3
    //     0xb24040: add             x3, x3, HEAP, lsl #32
    // 0xb24044: cmp             w3, NULL
    // 0xb24048: b.ne            #0xb24054
    // 0xb2404c: r1 = ""
    //     0xb2404c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb24050: b               #0xb24058
    // 0xb24054: mov             x1, x3
    // 0xb24058: stur            x1, [fp, #-8]
    // 0xb2405c: r0 = VideoPlayerWidget()
    //     0xb2405c: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb24060: mov             x1, x0
    // 0xb24064: ldur            x0, [fp, #-8]
    // 0xb24068: stur            x1, [fp, #-0x28]
    // 0xb2406c: StoreField: r1->field_b = r0
    //     0xb2406c: stur            w0, [x1, #0xb]
    // 0xb24070: r0 = true
    //     0xb24070: add             x0, NULL, #0x20  ; true
    // 0xb24074: StoreField: r1->field_f = r0
    //     0xb24074: stur            w0, [x1, #0xf]
    // 0xb24078: r0 = Center()
    //     0xb24078: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb2407c: mov             x2, x0
    // 0xb24080: r0 = Instance_Alignment
    //     0xb24080: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb24084: ldr             x0, [x0, #0xb10]
    // 0xb24088: stur            x2, [fp, #-8]
    // 0xb2408c: StoreField: r2->field_f = r0
    //     0xb2408c: stur            w0, [x2, #0xf]
    // 0xb24090: ldur            x0, [fp, #-0x28]
    // 0xb24094: StoreField: r2->field_b = r0
    //     0xb24094: stur            w0, [x2, #0xb]
    // 0xb24098: ldur            x0, [fp, #-0x20]
    // 0xb2409c: LoadField: r1 = r0->field_b
    //     0xb2409c: ldur            w1, [x0, #0xb]
    // 0xb240a0: LoadField: r3 = r0->field_f
    //     0xb240a0: ldur            w3, [x0, #0xf]
    // 0xb240a4: DecompressPointer r3
    //     0xb240a4: add             x3, x3, HEAP, lsl #32
    // 0xb240a8: LoadField: r4 = r3->field_b
    //     0xb240a8: ldur            w4, [x3, #0xb]
    // 0xb240ac: r3 = LoadInt32Instr(r1)
    //     0xb240ac: sbfx            x3, x1, #1, #0x1f
    // 0xb240b0: stur            x3, [fp, #-0x50]
    // 0xb240b4: r1 = LoadInt32Instr(r4)
    //     0xb240b4: sbfx            x1, x4, #1, #0x1f
    // 0xb240b8: cmp             x3, x1
    // 0xb240bc: b.ne            #0xb240c8
    // 0xb240c0: mov             x1, x0
    // 0xb240c4: r0 = _growToNextCapacity()
    //     0xb240c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb240c8: ldur            x2, [fp, #-0x20]
    // 0xb240cc: ldur            x3, [fp, #-0x50]
    // 0xb240d0: add             x0, x3, #1
    // 0xb240d4: lsl             x1, x0, #1
    // 0xb240d8: StoreField: r2->field_b = r1
    //     0xb240d8: stur            w1, [x2, #0xb]
    // 0xb240dc: LoadField: r1 = r2->field_f
    //     0xb240dc: ldur            w1, [x2, #0xf]
    // 0xb240e0: DecompressPointer r1
    //     0xb240e0: add             x1, x1, HEAP, lsl #32
    // 0xb240e4: ldur            x0, [fp, #-8]
    // 0xb240e8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb240e8: add             x25, x1, x3, lsl #2
    //     0xb240ec: add             x25, x25, #0xf
    //     0xb240f0: str             w0, [x25]
    //     0xb240f4: tbz             w0, #0, #0xb24110
    //     0xb240f8: ldurb           w16, [x1, #-1]
    //     0xb240fc: ldurb           w17, [x0, #-1]
    //     0xb24100: and             x16, x17, x16, lsr #2
    //     0xb24104: tst             x16, HEAP, lsr #32
    //     0xb24108: b.eq            #0xb24110
    //     0xb2410c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24110: ldur            x0, [fp, #-0x10]
    // 0xb24114: cmp             x0, #0
    // 0xb24118: b.le            #0xb24268
    // 0xb2411c: ldr             x1, [fp, #0x18]
    // 0xb24120: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb24120: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb24124: r0 = _of()
    //     0xb24124: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb24128: LoadField: r1 = r0->field_7
    //     0xb24128: ldur            w1, [x0, #7]
    // 0xb2412c: DecompressPointer r1
    //     0xb2412c: add             x1, x1, HEAP, lsl #32
    // 0xb24130: LoadField: d0 = r1->field_7
    //     0xb24130: ldur            d0, [x1, #7]
    // 0xb24134: d1 = 0.200000
    //     0xb24134: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb24138: fmul            d2, d0, d1
    // 0xb2413c: stur            d2, [fp, #-0x58]
    // 0xb24140: r0 = Container()
    //     0xb24140: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb24144: stur            x0, [fp, #-8]
    // 0xb24148: r16 = Instance_Color
    //     0xb24148: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb2414c: ldr             x16, [x16, #0xf88]
    // 0xb24150: str             x16, [SP]
    // 0xb24154: mov             x1, x0
    // 0xb24158: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb24158: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2415c: ldr             x4, [x4, #0xf40]
    // 0xb24160: r0 = Container()
    //     0xb24160: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb24164: r0 = GestureDetector()
    //     0xb24164: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb24168: ldur            x2, [fp, #-0x18]
    // 0xb2416c: r1 = Function '<anonymous closure>':.
    //     0xb2416c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57640] AnonymousClosure: (0xb244bc), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb21348)
    //     0xb24170: ldr             x1, [x1, #0x640]
    // 0xb24174: stur            x0, [fp, #-0x28]
    // 0xb24178: r0 = AllocateClosure()
    //     0xb24178: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2417c: ldur            x16, [fp, #-8]
    // 0xb24180: stp             x16, x0, [SP]
    // 0xb24184: ldur            x1, [fp, #-0x28]
    // 0xb24188: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb24188: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb2418c: ldr             x4, [x4, #0xaf0]
    // 0xb24190: r0 = GestureDetector()
    //     0xb24190: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb24194: r1 = <StackParentData>
    //     0xb24194: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb24198: ldr             x1, [x1, #0x8e0]
    // 0xb2419c: r0 = Positioned()
    //     0xb2419c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb241a0: mov             x2, x0
    // 0xb241a4: r0 = 0.000000
    //     0xb241a4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb241a8: stur            x2, [fp, #-8]
    // 0xb241ac: StoreField: r2->field_13 = r0
    //     0xb241ac: stur            w0, [x2, #0x13]
    // 0xb241b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb241b0: stur            w0, [x2, #0x17]
    // 0xb241b4: StoreField: r2->field_1f = r0
    //     0xb241b4: stur            w0, [x2, #0x1f]
    // 0xb241b8: ldur            d0, [fp, #-0x58]
    // 0xb241bc: r1 = inline_Allocate_Double()
    //     0xb241bc: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xb241c0: add             x1, x1, #0x10
    //     0xb241c4: cmp             x3, x1
    //     0xb241c8: b.ls            #0xb24428
    //     0xb241cc: str             x1, [THR, #0x50]  ; THR::top
    //     0xb241d0: sub             x1, x1, #0xf
    //     0xb241d4: movz            x3, #0xe15c
    //     0xb241d8: movk            x3, #0x3, lsl #16
    //     0xb241dc: stur            x3, [x1, #-1]
    // 0xb241e0: StoreField: r1->field_7 = d0
    //     0xb241e0: stur            d0, [x1, #7]
    // 0xb241e4: StoreField: r2->field_23 = r1
    //     0xb241e4: stur            w1, [x2, #0x23]
    // 0xb241e8: ldur            x1, [fp, #-0x28]
    // 0xb241ec: StoreField: r2->field_b = r1
    //     0xb241ec: stur            w1, [x2, #0xb]
    // 0xb241f0: ldur            x3, [fp, #-0x20]
    // 0xb241f4: LoadField: r1 = r3->field_b
    //     0xb241f4: ldur            w1, [x3, #0xb]
    // 0xb241f8: LoadField: r4 = r3->field_f
    //     0xb241f8: ldur            w4, [x3, #0xf]
    // 0xb241fc: DecompressPointer r4
    //     0xb241fc: add             x4, x4, HEAP, lsl #32
    // 0xb24200: LoadField: r5 = r4->field_b
    //     0xb24200: ldur            w5, [x4, #0xb]
    // 0xb24204: r4 = LoadInt32Instr(r1)
    //     0xb24204: sbfx            x4, x1, #1, #0x1f
    // 0xb24208: stur            x4, [fp, #-0x50]
    // 0xb2420c: r1 = LoadInt32Instr(r5)
    //     0xb2420c: sbfx            x1, x5, #1, #0x1f
    // 0xb24210: cmp             x4, x1
    // 0xb24214: b.ne            #0xb24220
    // 0xb24218: mov             x1, x3
    // 0xb2421c: r0 = _growToNextCapacity()
    //     0xb2421c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb24220: ldur            x2, [fp, #-0x20]
    // 0xb24224: ldur            x3, [fp, #-0x50]
    // 0xb24228: add             x0, x3, #1
    // 0xb2422c: lsl             x1, x0, #1
    // 0xb24230: StoreField: r2->field_b = r1
    //     0xb24230: stur            w1, [x2, #0xb]
    // 0xb24234: LoadField: r1 = r2->field_f
    //     0xb24234: ldur            w1, [x2, #0xf]
    // 0xb24238: DecompressPointer r1
    //     0xb24238: add             x1, x1, HEAP, lsl #32
    // 0xb2423c: ldur            x0, [fp, #-8]
    // 0xb24240: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb24240: add             x25, x1, x3, lsl #2
    //     0xb24244: add             x25, x25, #0xf
    //     0xb24248: str             w0, [x25]
    //     0xb2424c: tbz             w0, #0, #0xb24268
    //     0xb24250: ldurb           w16, [x1, #-1]
    //     0xb24254: ldurb           w17, [x0, #-1]
    //     0xb24258: and             x16, x17, x16, lsr #2
    //     0xb2425c: tst             x16, HEAP, lsr #32
    //     0xb24260: b.eq            #0xb24268
    //     0xb24264: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24268: ldur            x3, [fp, #-0x18]
    // 0xb2426c: ldur            x0, [fp, #-0x10]
    // 0xb24270: LoadField: r1 = r3->field_f
    //     0xb24270: ldur            w1, [x3, #0xf]
    // 0xb24274: DecompressPointer r1
    //     0xb24274: add             x1, x1, HEAP, lsl #32
    // 0xb24278: LoadField: r4 = r1->field_37
    //     0xb24278: ldur            w4, [x1, #0x37]
    // 0xb2427c: DecompressPointer r4
    //     0xb2427c: add             x4, x4, HEAP, lsl #32
    // 0xb24280: LoadField: r1 = r4->field_b
    //     0xb24280: ldur            w1, [x4, #0xb]
    // 0xb24284: r4 = LoadInt32Instr(r1)
    //     0xb24284: sbfx            x4, x1, #1, #0x1f
    // 0xb24288: sub             x1, x4, #1
    // 0xb2428c: cmp             x0, x1
    // 0xb24290: b.ge            #0xb243e0
    // 0xb24294: ldr             x1, [fp, #0x18]
    // 0xb24298: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb24298: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb2429c: r0 = _of()
    //     0xb2429c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb242a0: LoadField: r1 = r0->field_7
    //     0xb242a0: ldur            w1, [x0, #7]
    // 0xb242a4: DecompressPointer r1
    //     0xb242a4: add             x1, x1, HEAP, lsl #32
    // 0xb242a8: LoadField: d0 = r1->field_7
    //     0xb242a8: ldur            d0, [x1, #7]
    // 0xb242ac: d1 = 0.200000
    //     0xb242ac: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb242b0: fmul            d2, d0, d1
    // 0xb242b4: stur            d2, [fp, #-0x58]
    // 0xb242b8: r0 = Container()
    //     0xb242b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb242bc: stur            x0, [fp, #-8]
    // 0xb242c0: r16 = Instance_Color
    //     0xb242c0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb242c4: ldr             x16, [x16, #0xf88]
    // 0xb242c8: str             x16, [SP]
    // 0xb242cc: mov             x1, x0
    // 0xb242d0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb242d0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb242d4: ldr             x4, [x4, #0xf40]
    // 0xb242d8: r0 = Container()
    //     0xb242d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb242dc: r0 = GestureDetector()
    //     0xb242dc: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb242e0: ldur            x2, [fp, #-0x18]
    // 0xb242e4: r1 = Function '<anonymous closure>':.
    //     0xb242e4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57648] AnonymousClosure: (0xb2445c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb21348)
    //     0xb242e8: ldr             x1, [x1, #0x648]
    // 0xb242ec: stur            x0, [fp, #-0x18]
    // 0xb242f0: r0 = AllocateClosure()
    //     0xb242f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb242f4: ldur            x16, [fp, #-8]
    // 0xb242f8: stp             x16, x0, [SP]
    // 0xb242fc: ldur            x1, [fp, #-0x18]
    // 0xb24300: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb24300: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb24304: ldr             x4, [x4, #0xaf0]
    // 0xb24308: r0 = GestureDetector()
    //     0xb24308: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb2430c: r1 = <StackParentData>
    //     0xb2430c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb24310: ldr             x1, [x1, #0x8e0]
    // 0xb24314: r0 = Positioned()
    //     0xb24314: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb24318: mov             x2, x0
    // 0xb2431c: r0 = 0.000000
    //     0xb2431c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb24320: stur            x2, [fp, #-8]
    // 0xb24324: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24324: stur            w0, [x2, #0x17]
    // 0xb24328: StoreField: r2->field_1b = r0
    //     0xb24328: stur            w0, [x2, #0x1b]
    // 0xb2432c: StoreField: r2->field_1f = r0
    //     0xb2432c: stur            w0, [x2, #0x1f]
    // 0xb24330: ldur            d0, [fp, #-0x58]
    // 0xb24334: r0 = inline_Allocate_Double()
    //     0xb24334: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb24338: add             x0, x0, #0x10
    //     0xb2433c: cmp             x1, x0
    //     0xb24340: b.ls            #0xb24444
    //     0xb24344: str             x0, [THR, #0x50]  ; THR::top
    //     0xb24348: sub             x0, x0, #0xf
    //     0xb2434c: movz            x1, #0xe15c
    //     0xb24350: movk            x1, #0x3, lsl #16
    //     0xb24354: stur            x1, [x0, #-1]
    // 0xb24358: StoreField: r0->field_7 = d0
    //     0xb24358: stur            d0, [x0, #7]
    // 0xb2435c: StoreField: r2->field_23 = r0
    //     0xb2435c: stur            w0, [x2, #0x23]
    // 0xb24360: ldur            x0, [fp, #-0x18]
    // 0xb24364: StoreField: r2->field_b = r0
    //     0xb24364: stur            w0, [x2, #0xb]
    // 0xb24368: ldur            x0, [fp, #-0x20]
    // 0xb2436c: LoadField: r1 = r0->field_b
    //     0xb2436c: ldur            w1, [x0, #0xb]
    // 0xb24370: LoadField: r3 = r0->field_f
    //     0xb24370: ldur            w3, [x0, #0xf]
    // 0xb24374: DecompressPointer r3
    //     0xb24374: add             x3, x3, HEAP, lsl #32
    // 0xb24378: LoadField: r4 = r3->field_b
    //     0xb24378: ldur            w4, [x3, #0xb]
    // 0xb2437c: r3 = LoadInt32Instr(r1)
    //     0xb2437c: sbfx            x3, x1, #1, #0x1f
    // 0xb24380: stur            x3, [fp, #-0x10]
    // 0xb24384: r1 = LoadInt32Instr(r4)
    //     0xb24384: sbfx            x1, x4, #1, #0x1f
    // 0xb24388: cmp             x3, x1
    // 0xb2438c: b.ne            #0xb24398
    // 0xb24390: mov             x1, x0
    // 0xb24394: r0 = _growToNextCapacity()
    //     0xb24394: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb24398: ldur            x2, [fp, #-0x20]
    // 0xb2439c: ldur            x3, [fp, #-0x10]
    // 0xb243a0: add             x0, x3, #1
    // 0xb243a4: lsl             x1, x0, #1
    // 0xb243a8: StoreField: r2->field_b = r1
    //     0xb243a8: stur            w1, [x2, #0xb]
    // 0xb243ac: LoadField: r1 = r2->field_f
    //     0xb243ac: ldur            w1, [x2, #0xf]
    // 0xb243b0: DecompressPointer r1
    //     0xb243b0: add             x1, x1, HEAP, lsl #32
    // 0xb243b4: ldur            x0, [fp, #-8]
    // 0xb243b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb243b8: add             x25, x1, x3, lsl #2
    //     0xb243bc: add             x25, x25, #0xf
    //     0xb243c0: str             w0, [x25]
    //     0xb243c4: tbz             w0, #0, #0xb243e0
    //     0xb243c8: ldurb           w16, [x1, #-1]
    //     0xb243cc: ldurb           w17, [x0, #-1]
    //     0xb243d0: and             x16, x17, x16, lsr #2
    //     0xb243d4: tst             x16, HEAP, lsr #32
    //     0xb243d8: b.eq            #0xb243e0
    //     0xb243dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb243e0: r0 = Stack()
    //     0xb243e0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb243e4: r1 = Instance_AlignmentDirectional
    //     0xb243e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb243e8: ldr             x1, [x1, #0xd08]
    // 0xb243ec: StoreField: r0->field_f = r1
    //     0xb243ec: stur            w1, [x0, #0xf]
    // 0xb243f0: r1 = Instance_StackFit
    //     0xb243f0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb243f4: ldr             x1, [x1, #0xfa8]
    // 0xb243f8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb243f8: stur            w1, [x0, #0x17]
    // 0xb243fc: r1 = Instance_Clip
    //     0xb243fc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb24400: ldr             x1, [x1, #0x7e0]
    // 0xb24404: StoreField: r0->field_1b = r1
    //     0xb24404: stur            w1, [x0, #0x1b]
    // 0xb24408: ldur            x1, [fp, #-0x20]
    // 0xb2440c: StoreField: r0->field_b = r1
    //     0xb2440c: stur            w1, [x0, #0xb]
    // 0xb24410: LeaveFrame
    //     0xb24410: mov             SP, fp
    //     0xb24414: ldp             fp, lr, [SP], #0x10
    // 0xb24418: ret
    //     0xb24418: ret             
    // 0xb2441c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2441c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb24420: b               #0xb23dac
    // 0xb24424: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb24424: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb24428: SaveReg d0
    //     0xb24428: str             q0, [SP, #-0x10]!
    // 0xb2442c: stp             x0, x2, [SP, #-0x10]!
    // 0xb24430: r0 = AllocateDouble()
    //     0xb24430: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb24434: mov             x1, x0
    // 0xb24438: ldp             x0, x2, [SP], #0x10
    // 0xb2443c: RestoreReg d0
    //     0xb2443c: ldr             q0, [SP], #0x10
    // 0xb24440: b               #0xb241e0
    // 0xb24444: SaveReg d0
    //     0xb24444: str             q0, [SP, #-0x10]!
    // 0xb24448: SaveReg r2
    //     0xb24448: str             x2, [SP, #-8]!
    // 0xb2444c: r0 = AllocateDouble()
    //     0xb2444c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb24450: RestoreReg r2
    //     0xb24450: ldr             x2, [SP], #8
    // 0xb24454: RestoreReg d0
    //     0xb24454: ldr             q0, [SP], #0x10
    // 0xb24458: b               #0xb24358
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2445c, size: 0x60
    // 0xb2445c: EnterFrame
    //     0xb2445c: stp             fp, lr, [SP, #-0x10]!
    //     0xb24460: mov             fp, SP
    // 0xb24464: ldr             x0, [fp, #0x10]
    // 0xb24468: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb24468: ldur            w1, [x0, #0x17]
    // 0xb2446c: DecompressPointer r1
    //     0xb2446c: add             x1, x1, HEAP, lsl #32
    // 0xb24470: CheckStackOverflow
    //     0xb24470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb24474: cmp             SP, x16
    //     0xb24478: b.ls            #0xb244a8
    // 0xb2447c: LoadField: r0 = r1->field_f
    //     0xb2447c: ldur            w0, [x1, #0xf]
    // 0xb24480: DecompressPointer r0
    //     0xb24480: add             x0, x0, HEAP, lsl #32
    // 0xb24484: LoadField: r1 = r0->field_23
    //     0xb24484: ldur            w1, [x0, #0x23]
    // 0xb24488: DecompressPointer r1
    //     0xb24488: add             x1, x1, HEAP, lsl #32
    // 0xb2448c: r16 = Sentinel
    //     0xb2448c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb24490: cmp             w1, w16
    // 0xb24494: b.eq            #0xb244b0
    // 0xb24498: r0 = nextPage()
    //     0xb24498: bl              #0xaa5ed0  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xb2449c: LeaveFrame
    //     0xb2449c: mov             SP, fp
    //     0xb244a0: ldp             fp, lr, [SP], #0x10
    // 0xb244a4: ret
    //     0xb244a4: ret             
    // 0xb244a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb244a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb244ac: b               #0xb2447c
    // 0xb244b0: r9 = _pageController
    //     0xb244b0: add             x9, PP, #0x57, lsl #12  ; [pp+0x57608] Field <_RatingReviewAllMediaOnTapImageState@1511364025._pageController@1511364025>: late (offset: 0x24)
    //     0xb244b4: ldr             x9, [x9, #0x608]
    // 0xb244b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb244b8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb244bc, size: 0x60
    // 0xb244bc: EnterFrame
    //     0xb244bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb244c0: mov             fp, SP
    // 0xb244c4: ldr             x0, [fp, #0x10]
    // 0xb244c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb244c8: ldur            w1, [x0, #0x17]
    // 0xb244cc: DecompressPointer r1
    //     0xb244cc: add             x1, x1, HEAP, lsl #32
    // 0xb244d0: CheckStackOverflow
    //     0xb244d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb244d4: cmp             SP, x16
    //     0xb244d8: b.ls            #0xb24508
    // 0xb244dc: LoadField: r0 = r1->field_f
    //     0xb244dc: ldur            w0, [x1, #0xf]
    // 0xb244e0: DecompressPointer r0
    //     0xb244e0: add             x0, x0, HEAP, lsl #32
    // 0xb244e4: LoadField: r1 = r0->field_23
    //     0xb244e4: ldur            w1, [x0, #0x23]
    // 0xb244e8: DecompressPointer r1
    //     0xb244e8: add             x1, x1, HEAP, lsl #32
    // 0xb244ec: r16 = Sentinel
    //     0xb244ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb244f0: cmp             w1, w16
    // 0xb244f4: b.eq            #0xb24510
    // 0xb244f8: r0 = previousPage()
    //     0xb244f8: bl              #0xaa6010  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xb244fc: LeaveFrame
    //     0xb244fc: mov             SP, fp
    //     0xb24500: ldp             fp, lr, [SP], #0x10
    // 0xb24504: ret
    //     0xb24504: ret             
    // 0xb24508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb24508: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2450c: b               #0xb244dc
    // 0xb24510: r9 = _pageController
    //     0xb24510: add             x9, PP, #0x57, lsl #12  ; [pp+0x57608] Field <_RatingReviewAllMediaOnTapImageState@1511364025._pageController@1511364025>: late (offset: 0x24)
    //     0xb24514: ldr             x9, [x9, #0x608]
    // 0xb24518: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb24518: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb2451c, size: 0x84
    // 0xb2451c: EnterFrame
    //     0xb2451c: stp             fp, lr, [SP, #-0x10]!
    //     0xb24520: mov             fp, SP
    // 0xb24524: AllocStack(0x10)
    //     0xb24524: sub             SP, SP, #0x10
    // 0xb24528: SetupParameters()
    //     0xb24528: ldr             x0, [fp, #0x18]
    //     0xb2452c: ldur            w1, [x0, #0x17]
    //     0xb24530: add             x1, x1, HEAP, lsl #32
    //     0xb24534: stur            x1, [fp, #-8]
    // 0xb24538: CheckStackOverflow
    //     0xb24538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2453c: cmp             SP, x16
    //     0xb24540: b.ls            #0xb24598
    // 0xb24544: r1 = 1
    //     0xb24544: movz            x1, #0x1
    // 0xb24548: r0 = AllocateContext()
    //     0xb24548: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2454c: mov             x1, x0
    // 0xb24550: ldur            x0, [fp, #-8]
    // 0xb24554: StoreField: r1->field_b = r0
    //     0xb24554: stur            w0, [x1, #0xb]
    // 0xb24558: ldr             x2, [fp, #0x10]
    // 0xb2455c: StoreField: r1->field_f = r2
    //     0xb2455c: stur            w2, [x1, #0xf]
    // 0xb24560: LoadField: r3 = r0->field_f
    //     0xb24560: ldur            w3, [x0, #0xf]
    // 0xb24564: DecompressPointer r3
    //     0xb24564: add             x3, x3, HEAP, lsl #32
    // 0xb24568: mov             x2, x1
    // 0xb2456c: stur            x3, [fp, #-0x10]
    // 0xb24570: r1 = Function '<anonymous closure>':.
    //     0xb24570: add             x1, PP, #0x57, lsl #12  ; [pp+0x57650] AnonymousClosure: (0xaa6a74), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb24574: ldr             x1, [x1, #0x650]
    // 0xb24578: r0 = AllocateClosure()
    //     0xb24578: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2457c: ldur            x1, [fp, #-0x10]
    // 0xb24580: mov             x2, x0
    // 0xb24584: r0 = setState()
    //     0xb24584: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb24588: r0 = Null
    //     0xb24588: mov             x0, NULL
    // 0xb2458c: LeaveFrame
    //     0xb2458c: mov             SP, fp
    //     0xb24590: ldp             fp, lr, [SP], #0x10
    // 0xb24594: ret
    //     0xb24594: ret             
    // 0xb24598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb24598: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2459c: b               #0xb24544
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc877b8, size: 0x8c
    // 0xc877b8: EnterFrame
    //     0xc877b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc877bc: mov             fp, SP
    // 0xc877c0: AllocStack(0x10)
    //     0xc877c0: sub             SP, SP, #0x10
    // 0xc877c4: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r2, fp-0x8 */)
    //     0xc877c4: mov             x2, x1
    //     0xc877c8: stur            x1, [fp, #-8]
    // 0xc877cc: CheckStackOverflow
    //     0xc877cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc877d0: cmp             SP, x16
    //     0xc877d4: b.ls            #0xc87830
    // 0xc877d8: LoadField: r1 = r2->field_23
    //     0xc877d8: ldur            w1, [x2, #0x23]
    // 0xc877dc: DecompressPointer r1
    //     0xc877dc: add             x1, x1, HEAP, lsl #32
    // 0xc877e0: r16 = Sentinel
    //     0xc877e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc877e4: cmp             w1, w16
    // 0xc877e8: b.eq            #0xc87838
    // 0xc877ec: r0 = dispose()
    //     0xc877ec: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc877f0: ldur            x2, [fp, #-8]
    // 0xc877f4: LoadField: r0 = r2->field_27
    //     0xc877f4: ldur            w0, [x2, #0x27]
    // 0xc877f8: DecompressPointer r0
    //     0xc877f8: add             x0, x0, HEAP, lsl #32
    // 0xc877fc: stur            x0, [fp, #-0x10]
    // 0xc87800: r1 = Function '_onCollapseChanged@1511364025':.
    //     0xc87800: add             x1, PP, #0x57, lsl #12  ; [pp+0x57658] AnonymousClosure: (0x93d3e0), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x93d418)
    //     0xc87804: ldr             x1, [x1, #0x658]
    // 0xc87808: r0 = AllocateClosure()
    //     0xc87808: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc8780c: ldur            x1, [fp, #-0x10]
    // 0xc87810: mov             x2, x0
    // 0xc87814: r0 = removeListener()
    //     0xc87814: bl              #0x7b91b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc87818: ldur            x1, [fp, #-0x10]
    // 0xc8781c: r0 = dispose()
    //     0xc8781c: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87820: r0 = Null
    //     0xc87820: mov             x0, NULL
    // 0xc87824: LeaveFrame
    //     0xc87824: mov             SP, fp
    //     0xc87828: ldp             fp, lr, [SP], #0x10
    // 0xc8782c: ret
    //     0xc8782c: ret             
    // 0xc87830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87830: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87834: b               #0xc877d8
    // 0xc87838: r9 = _pageController
    //     0xc87838: add             x9, PP, #0x57, lsl #12  ; [pp+0x57608] Field <_RatingReviewAllMediaOnTapImageState@1511364025._pageController@1511364025>: late (offset: 0x24)
    //     0xc8783c: ldr             x9, [x9, #0x608]
    // 0xc87840: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87840: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4126, size: 0x28, field offset: 0xc
class RatingReviewAllMediaOnTapImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e40c, size: 0x48
    // 0xc7e40c: EnterFrame
    //     0xc7e40c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e410: mov             fp, SP
    // 0xc7e414: AllocStack(0x8)
    //     0xc7e414: sub             SP, SP, #8
    // 0xc7e418: CheckStackOverflow
    //     0xc7e418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e41c: cmp             SP, x16
    //     0xc7e420: b.ls            #0xc7e44c
    // 0xc7e424: r1 = <RatingReviewAllMediaOnTapImage>
    //     0xc7e424: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ac8] TypeArguments: <RatingReviewAllMediaOnTapImage>
    //     0xc7e428: ldr             x1, [x1, #0xac8]
    // 0xc7e42c: r0 = _RatingReviewAllMediaOnTapImageState()
    //     0xc7e42c: bl              #0xc7e454  ; Allocate_RatingReviewAllMediaOnTapImageStateStub -> _RatingReviewAllMediaOnTapImageState (size=0x40)
    // 0xc7e430: mov             x1, x0
    // 0xc7e434: stur            x0, [fp, #-8]
    // 0xc7e438: r0 = _RatingReviewAllMediaOnTapImageState()
    //     0xc7e438: bl              #0xc7c8bc  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_RatingReviewAllMediaOnTapImageState
    // 0xc7e43c: ldur            x0, [fp, #-8]
    // 0xc7e440: LeaveFrame
    //     0xc7e440: mov             SP, fp
    //     0xc7e444: ldp             fp, lr, [SP], #0x10
    // 0xc7e448: ret
    //     0xc7e448: ret             
    // 0xc7e44c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e44c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e450: b               #0xc7e424
  }
}
