// lib: , url: package:dio/src/dio_exception.dart

// class id: 1049603, size: 0x8
class :: {

  [closure] static String defaultDioExceptionReadableStringBuilder(dynamic, DioException) {
    // ** addr: 0x156824c, size: 0x30
    // 0x156824c: EnterFrame
    //     0x156824c: stp             fp, lr, [SP, #-0x10]!
    //     0x1568250: mov             fp, SP
    // 0x1568254: CheckStackOverflow
    //     0x1568254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1568258: cmp             SP, x16
    //     0x156825c: b.ls            #0x1568274
    // 0x1568260: ldr             x1, [fp, #0x10]
    // 0x1568264: r0 = defaultDioExceptionReadableStringBuilder()
    //     0x1568264: bl              #0x156827c  ; [package:dio/src/dio_exception.dart] ::defaultDioExceptionReadableStringBuilder
    // 0x1568268: LeaveFrame
    //     0x1568268: mov             SP, fp
    //     0x156826c: ldp             fp, lr, [SP], #0x10
    // 0x1568270: ret
    //     0x1568270: ret             
    // 0x1568274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568274: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1568278: b               #0x1568260
  }
  static _ defaultDioExceptionReadableStringBuilder(/* No info */) {
    // ** addr: 0x156827c, size: 0x198
    // 0x156827c: EnterFrame
    //     0x156827c: stp             fp, lr, [SP, #-0x10]!
    //     0x1568280: mov             fp, SP
    // 0x1568284: AllocStack(0x20)
    //     0x1568284: sub             SP, SP, #0x20
    // 0x1568288: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x1568288: mov             x0, x1
    //     0x156828c: stur            x1, [fp, #-8]
    // 0x1568290: CheckStackOverflow
    //     0x1568290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1568294: cmp             SP, x16
    //     0x1568298: b.ls            #0x156840c
    // 0x156829c: r1 = Null
    //     0x156829c: mov             x1, NULL
    // 0x15682a0: r2 = 8
    //     0x15682a0: movz            x2, #0x8
    // 0x15682a4: r0 = AllocateArray()
    //     0x15682a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15682a8: r16 = "DioException ["
    //     0x15682a8: add             x16, PP, #0x26, lsl #12  ; [pp+0x26320] "DioException ["
    //     0x15682ac: ldr             x16, [x16, #0x320]
    // 0x15682b0: StoreField: r0->field_f = r16
    //     0x15682b0: stur            w16, [x0, #0xf]
    // 0x15682b4: ldur            x1, [fp, #-8]
    // 0x15682b8: LoadField: r2 = r1->field_b
    //     0x15682b8: ldur            w2, [x1, #0xb]
    // 0x15682bc: DecompressPointer r2
    //     0x15682bc: add             x2, x2, HEAP, lsl #32
    // 0x15682c0: LoadField: r3 = r2->field_7
    //     0x15682c0: ldur            x3, [x2, #7]
    // 0x15682c4: cmp             x3, #3
    // 0x15682c8: b.gt            #0x1568314
    // 0x15682cc: cmp             x3, #1
    // 0x15682d0: b.gt            #0x15682f4
    // 0x15682d4: cmp             x3, #0
    // 0x15682d8: b.gt            #0x15682e8
    // 0x15682dc: r2 = "connection timeout"
    //     0x15682dc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26328] "connection timeout"
    //     0x15682e0: ldr             x2, [x2, #0x328]
    // 0x15682e4: b               #0x1568358
    // 0x15682e8: r2 = "send timeout"
    //     0x15682e8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26330] "send timeout"
    //     0x15682ec: ldr             x2, [x2, #0x330]
    // 0x15682f0: b               #0x1568358
    // 0x15682f4: cmp             x3, #2
    // 0x15682f8: b.gt            #0x1568308
    // 0x15682fc: r2 = "receive timeout"
    //     0x15682fc: add             x2, PP, #0x26, lsl #12  ; [pp+0x26338] "receive timeout"
    //     0x1568300: ldr             x2, [x2, #0x338]
    // 0x1568304: b               #0x1568358
    // 0x1568308: r2 = "bad certificate"
    //     0x1568308: add             x2, PP, #0x26, lsl #12  ; [pp+0x26340] "bad certificate"
    //     0x156830c: ldr             x2, [x2, #0x340]
    // 0x1568310: b               #0x1568358
    // 0x1568314: cmp             x3, #5
    // 0x1568318: b.gt            #0x156833c
    // 0x156831c: cmp             x3, #4
    // 0x1568320: b.gt            #0x1568330
    // 0x1568324: r2 = "bad response"
    //     0x1568324: add             x2, PP, #0x26, lsl #12  ; [pp+0x26348] "bad response"
    //     0x1568328: ldr             x2, [x2, #0x348]
    // 0x156832c: b               #0x1568358
    // 0x1568330: r2 = "request cancelled"
    //     0x1568330: add             x2, PP, #0x26, lsl #12  ; [pp+0x26350] "request cancelled"
    //     0x1568334: ldr             x2, [x2, #0x350]
    // 0x1568338: b               #0x1568358
    // 0x156833c: cmp             x3, #6
    // 0x1568340: b.gt            #0x1568350
    // 0x1568344: r2 = "connection error"
    //     0x1568344: add             x2, PP, #0x26, lsl #12  ; [pp+0x26358] "connection error"
    //     0x1568348: ldr             x2, [x2, #0x358]
    // 0x156834c: b               #0x1568358
    // 0x1568350: r2 = "unknown"
    //     0x1568350: add             x2, PP, #0xd, lsl #12  ; [pp+0xd318] "unknown"
    //     0x1568354: ldr             x2, [x2, #0x318]
    // 0x1568358: StoreField: r0->field_13 = r2
    //     0x1568358: stur            w2, [x0, #0x13]
    // 0x156835c: r16 = "]: "
    //     0x156835c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26360] "]: "
    //     0x1568360: ldr             x16, [x16, #0x360]
    // 0x1568364: ArrayStore: r0[0] = r16  ; List_4
    //     0x1568364: stur            w16, [x0, #0x17]
    // 0x1568368: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x1568368: ldur            w2, [x1, #0x17]
    // 0x156836c: DecompressPointer r2
    //     0x156836c: add             x2, x2, HEAP, lsl #32
    // 0x1568370: StoreField: r0->field_1b = r2
    //     0x1568370: stur            w2, [x0, #0x1b]
    // 0x1568374: str             x0, [SP]
    // 0x1568378: r0 = _interpolate()
    //     0x1568378: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x156837c: stur            x0, [fp, #-0x10]
    // 0x1568380: r0 = StringBuffer()
    //     0x1568380: bl              #0x620890  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x1568384: stur            x0, [fp, #-0x18]
    // 0x1568388: ldur            x16, [fp, #-0x10]
    // 0x156838c: str             x16, [SP]
    // 0x1568390: mov             x1, x0
    // 0x1568394: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x1568394: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x1568398: r0 = StringBuffer()
    //     0x1568398: bl              #0x6200bc  ; [dart:core] StringBuffer::StringBuffer
    // 0x156839c: ldur            x0, [fp, #-8]
    // 0x15683a0: LoadField: r2 = r0->field_f
    //     0x15683a0: ldur            w2, [x0, #0xf]
    // 0x15683a4: DecompressPointer r2
    //     0x15683a4: add             x2, x2, HEAP, lsl #32
    // 0x15683a8: stur            x2, [fp, #-0x10]
    // 0x15683ac: cmp             w2, NULL
    // 0x15683b0: b.eq            #0x15683f4
    // 0x15683b4: ldur            x1, [fp, #-0x18]
    // 0x15683b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15683b8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15683bc: r0 = writeln()
    //     0x15683bc: bl              #0x86444c  ; [dart:core] StringBuffer::writeln
    // 0x15683c0: r1 = Null
    //     0x15683c0: mov             x1, NULL
    // 0x15683c4: r2 = 4
    //     0x15683c4: movz            x2, #0x4
    // 0x15683c8: r0 = AllocateArray()
    //     0x15683c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15683cc: r16 = "Error: "
    //     0x15683cc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26368] "Error: "
    //     0x15683d0: ldr             x16, [x16, #0x368]
    // 0x15683d4: StoreField: r0->field_f = r16
    //     0x15683d4: stur            w16, [x0, #0xf]
    // 0x15683d8: ldur            x1, [fp, #-0x10]
    // 0x15683dc: StoreField: r0->field_13 = r1
    //     0x15683dc: stur            w1, [x0, #0x13]
    // 0x15683e0: str             x0, [SP]
    // 0x15683e4: r0 = _interpolate()
    //     0x15683e4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x15683e8: ldur            x1, [fp, #-0x18]
    // 0x15683ec: mov             x2, x0
    // 0x15683f0: r0 = write()
    //     0x15683f0: bl              #0x165e2d8  ; [dart:core] StringBuffer::write
    // 0x15683f4: ldur            x16, [fp, #-0x18]
    // 0x15683f8: str             x16, [SP]
    // 0x15683fc: r0 = toString()
    //     0x15683fc: bl              #0x1558aa8  ; [dart:core] StringBuffer::toString
    // 0x1568400: LeaveFrame
    //     0x1568400: mov             SP, fp
    //     0x1568404: ldp             fp, lr, [SP], #0x10
    // 0x1568408: ret
    //     0x1568408: ret             
    // 0x156840c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x156840c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1568410: b               #0x156829c
  }
}

// class id: 4989, size: 0x20, field offset: 0x8
class DioException extends Object
    implements Exception {

  static late (dynamic, DioException) => String readableStringBuilder; // offset: 0xd34

  _ DioException(/* No info */) {
    // ** addr: 0x861da8, size: 0x280
    // 0x861da8: EnterFrame
    //     0x861da8: stp             fp, lr, [SP, #-0x10]!
    //     0x861dac: mov             fp, SP
    // 0x861db0: AllocStack(0x8)
    //     0x861db0: sub             SP, SP, #8
    // 0x861db4: SetupParameters(DioException this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1 */, {dynamic message = Null /* r6 */, dynamic response = Null /* r7 */, dynamic stackTrace = Null /* r8 */, dynamic type = Instance_DioExceptionType /* r4 */})
    //     0x861db4: stur            x1, [fp, #-8]
    //     0x861db8: mov             x16, x2
    //     0x861dbc: mov             x2, x1
    //     0x861dc0: mov             x1, x16
    //     0x861dc4: ldur            w0, [x4, #0x13]
    //     0x861dc8: ldur            w5, [x4, #0x1f]
    //     0x861dcc: add             x5, x5, HEAP, lsl #32
    //     0x861dd0: ldr             x16, [PP, #0x7998]  ; [pp+0x7998] "message"
    //     0x861dd4: cmp             w5, w16
    //     0x861dd8: b.ne            #0x861dfc
    //     0x861ddc: ldur            w5, [x4, #0x23]
    //     0x861de0: add             x5, x5, HEAP, lsl #32
    //     0x861de4: sub             w6, w0, w5
    //     0x861de8: add             x5, fp, w6, sxtw #2
    //     0x861dec: ldr             x5, [x5, #8]
    //     0x861df0: mov             x6, x5
    //     0x861df4: movz            x5, #0x1
    //     0x861df8: b               #0x861e04
    //     0x861dfc: mov             x6, NULL
    //     0x861e00: movz            x5, #0
    //     0x861e04: lsl             x7, x5, #1
    //     0x861e08: lsl             w8, w7, #1
    //     0x861e0c: add             w9, w8, #8
    //     0x861e10: add             x16, x4, w9, sxtw #1
    //     0x861e14: ldur            w10, [x16, #0xf]
    //     0x861e18: add             x10, x10, HEAP, lsl #32
    //     0x861e1c: ldr             x16, [PP, #0x2d00]  ; [pp+0x2d00] "response"
    //     0x861e20: cmp             w10, w16
    //     0x861e24: b.ne            #0x861e58
    //     0x861e28: add             w5, w8, #0xa
    //     0x861e2c: add             x16, x4, w5, sxtw #1
    //     0x861e30: ldur            w8, [x16, #0xf]
    //     0x861e34: add             x8, x8, HEAP, lsl #32
    //     0x861e38: sub             w5, w0, w8
    //     0x861e3c: add             x8, fp, w5, sxtw #2
    //     0x861e40: ldr             x8, [x8, #8]
    //     0x861e44: add             w5, w7, #2
    //     0x861e48: sbfx            x7, x5, #1, #0x1f
    //     0x861e4c: mov             x5, x7
    //     0x861e50: mov             x7, x8
    //     0x861e54: b               #0x861e5c
    //     0x861e58: mov             x7, NULL
    //     0x861e5c: lsl             x8, x5, #1
    //     0x861e60: lsl             w9, w8, #1
    //     0x861e64: add             w10, w9, #8
    //     0x861e68: add             x16, x4, w10, sxtw #1
    //     0x861e6c: ldur            w11, [x16, #0xf]
    //     0x861e70: add             x11, x11, HEAP, lsl #32
    //     0x861e74: add             x16, PP, #8, lsl #12  ; [pp+0x84f0] "stackTrace"
    //     0x861e78: ldr             x16, [x16, #0x4f0]
    //     0x861e7c: cmp             w11, w16
    //     0x861e80: b.ne            #0x861eb4
    //     0x861e84: add             w5, w9, #0xa
    //     0x861e88: add             x16, x4, w5, sxtw #1
    //     0x861e8c: ldur            w9, [x16, #0xf]
    //     0x861e90: add             x9, x9, HEAP, lsl #32
    //     0x861e94: sub             w5, w0, w9
    //     0x861e98: add             x9, fp, w5, sxtw #2
    //     0x861e9c: ldr             x9, [x9, #8]
    //     0x861ea0: add             w5, w8, #2
    //     0x861ea4: sbfx            x8, x5, #1, #0x1f
    //     0x861ea8: mov             x5, x8
    //     0x861eac: mov             x8, x9
    //     0x861eb0: b               #0x861eb8
    //     0x861eb4: mov             x8, NULL
    //     0x861eb8: lsl             x9, x5, #1
    //     0x861ebc: lsl             w5, w9, #1
    //     0x861ec0: add             w9, w5, #8
    //     0x861ec4: add             x16, x4, w9, sxtw #1
    //     0x861ec8: ldur            w10, [x16, #0xf]
    //     0x861ecc: add             x10, x10, HEAP, lsl #32
    //     0x861ed0: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    //     0x861ed4: cmp             w10, w16
    //     0x861ed8: b.ne            #0x861f00
    //     0x861edc: add             w9, w5, #0xa
    //     0x861ee0: add             x16, x4, w9, sxtw #1
    //     0x861ee4: ldur            w5, [x16, #0xf]
    //     0x861ee8: add             x5, x5, HEAP, lsl #32
    //     0x861eec: sub             w4, w0, w5
    //     0x861ef0: add             x0, fp, w4, sxtw #2
    //     0x861ef4: ldr             x0, [x0, #8]
    //     0x861ef8: mov             x4, x0
    //     0x861efc: b               #0x861f08
    //     0x861f00: add             x4, PP, #8, lsl #12  ; [pp+0x84f8] Obj!DioExceptionType@d751e1
    //     0x861f04: ldr             x4, [x4, #0x4f8]
    // 0x861f08: CheckStackOverflow
    //     0x861f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x861f0c: cmp             SP, x16
    //     0x861f10: b.ls            #0x862020
    // 0x861f14: mov             x0, x7
    // 0x861f18: StoreField: r2->field_7 = r0
    //     0x861f18: stur            w0, [x2, #7]
    //     0x861f1c: ldurb           w16, [x2, #-1]
    //     0x861f20: ldurb           w17, [x0, #-1]
    //     0x861f24: and             x16, x17, x16, lsr #2
    //     0x861f28: tst             x16, HEAP, lsr #32
    //     0x861f2c: b.eq            #0x861f34
    //     0x861f30: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x861f34: mov             x0, x4
    // 0x861f38: StoreField: r2->field_b = r0
    //     0x861f38: stur            w0, [x2, #0xb]
    //     0x861f3c: ldurb           w16, [x2, #-1]
    //     0x861f40: ldurb           w17, [x0, #-1]
    //     0x861f44: and             x16, x17, x16, lsr #2
    //     0x861f48: tst             x16, HEAP, lsr #32
    //     0x861f4c: b.eq            #0x861f54
    //     0x861f50: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x861f54: mov             x0, x1
    // 0x861f58: StoreField: r2->field_f = r0
    //     0x861f58: stur            w0, [x2, #0xf]
    //     0x861f5c: tbz             w0, #0, #0x861f78
    //     0x861f60: ldurb           w16, [x2, #-1]
    //     0x861f64: ldurb           w17, [x0, #-1]
    //     0x861f68: and             x16, x17, x16, lsr #2
    //     0x861f6c: tst             x16, HEAP, lsr #32
    //     0x861f70: b.eq            #0x861f78
    //     0x861f74: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x861f78: mov             x0, x6
    // 0x861f7c: ArrayStore: r2[0] = r0  ; List_4
    //     0x861f7c: stur            w0, [x2, #0x17]
    //     0x861f80: ldurb           w16, [x2, #-1]
    //     0x861f84: ldurb           w17, [x0, #-1]
    //     0x861f88: and             x16, x17, x16, lsr #2
    //     0x861f8c: tst             x16, HEAP, lsr #32
    //     0x861f90: b.eq            #0x861f98
    //     0x861f94: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x861f98: r16 = Instance__StringStackTrace
    //     0x861f98: ldr             x16, [PP, #0x1580]  ; [pp+0x1580] Obj!_StringStackTrace@d6f171
    // 0x861f9c: cmp             w8, w16
    // 0x861fa0: b.ne            #0x861fbc
    // 0x861fa4: LoadField: r0 = r3->field_53
    //     0x861fa4: ldur            w0, [x3, #0x53]
    // 0x861fa8: DecompressPointer r0
    //     0x861fa8: add             x0, x0, HEAP, lsl #32
    // 0x861fac: cmp             w0, NULL
    // 0x861fb0: b.ne            #0x861ff0
    // 0x861fb4: r0 = current()
    //     0x861fb4: bl              #0x61b99c  ; [dart:core] StackTrace::current
    // 0x861fb8: b               #0x861ff0
    // 0x861fbc: cmp             w8, NULL
    // 0x861fc0: b.ne            #0x861fd0
    // 0x861fc4: LoadField: r0 = r3->field_53
    //     0x861fc4: ldur            w0, [x3, #0x53]
    // 0x861fc8: DecompressPointer r0
    //     0x861fc8: add             x0, x0, HEAP, lsl #32
    // 0x861fcc: b               #0x861fd4
    // 0x861fd0: mov             x0, x8
    // 0x861fd4: cmp             w0, NULL
    // 0x861fd8: b.ne            #0x861fe8
    // 0x861fdc: r0 = current()
    //     0x861fdc: bl              #0x61b99c  ; [dart:core] StackTrace::current
    // 0x861fe0: mov             x1, x0
    // 0x861fe4: b               #0x861fec
    // 0x861fe8: mov             x1, x0
    // 0x861fec: mov             x0, x1
    // 0x861ff0: ldur            x1, [fp, #-8]
    // 0x861ff4: StoreField: r1->field_13 = r0
    //     0x861ff4: stur            w0, [x1, #0x13]
    //     0x861ff8: ldurb           w16, [x1, #-1]
    //     0x861ffc: ldurb           w17, [x0, #-1]
    //     0x862000: and             x16, x17, x16, lsr #2
    //     0x862004: tst             x16, HEAP, lsr #32
    //     0x862008: b.eq            #0x862010
    //     0x86200c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x862010: r0 = Null
    //     0x862010: mov             x0, NULL
    // 0x862014: LeaveFrame
    //     0x862014: mov             SP, fp
    //     0x862018: ldp             fp, lr, [SP], #0x10
    // 0x86201c: ret
    //     0x86201c: ret             
    // 0x862020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862020: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862024: b               #0x861f14
  }
  factory _ DioException.badResponse(/* No info */) {
    // ** addr: 0x8641f8, size: 0x84
    // 0x8641f8: EnterFrame
    //     0x8641f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8641fc: mov             fp, SP
    // 0x864200: AllocStack(0x38)
    //     0x864200: sub             SP, SP, #0x38
    // 0x864204: SetupParameters(dynamic _ /* r1 => r2 */, dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1 */)
    //     0x864204: mov             x0, x2
    //     0x864208: stur            x2, [fp, #-8]
    //     0x86420c: mov             x2, x1
    //     0x864210: mov             x1, x5
    //     0x864214: stur            x3, [fp, #-0x10]
    // 0x864218: CheckStackOverflow
    //     0x864218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86421c: cmp             SP, x16
    //     0x864220: b.ls            #0x864274
    // 0x864224: r0 = _badResponseExceptionMessage()
    //     0x864224: bl              #0x86427c  ; [package:dio/src/dio_exception.dart] DioException::_badResponseExceptionMessage
    // 0x864228: stur            x0, [fp, #-0x18]
    // 0x86422c: r0 = DioException()
    //     0x86422c: bl              #0x86204c  ; AllocateDioExceptionStub -> DioException (size=0x20)
    // 0x864230: stur            x0, [fp, #-0x20]
    // 0x864234: r16 = Instance_DioExceptionType
    //     0x864234: add             x16, PP, #8, lsl #12  ; [pp+0x84e0] Obj!DioExceptionType@d75221
    //     0x864238: ldr             x16, [x16, #0x4e0]
    // 0x86423c: ldur            lr, [fp, #-0x10]
    // 0x864240: stp             lr, x16, [SP, #8]
    // 0x864244: ldur            x16, [fp, #-0x18]
    // 0x864248: str             x16, [SP]
    // 0x86424c: mov             x1, x0
    // 0x864250: ldur            x3, [fp, #-8]
    // 0x864254: r2 = Null
    //     0x864254: mov             x2, NULL
    // 0x864258: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x864258: add             x4, PP, #8, lsl #12  ; [pp+0x84e8] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x86425c: ldr             x4, [x4, #0x4e8]
    // 0x864260: r0 = DioException()
    //     0x864260: bl              #0x861da8  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x864264: ldur            x0, [fp, #-0x20]
    // 0x864268: LeaveFrame
    //     0x864268: mov             SP, fp
    //     0x86426c: ldp             fp, lr, [SP], #0x10
    // 0x864270: ret
    //     0x864270: ret             
    // 0x864274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x864274: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x864278: b               #0x864224
  }
  static String _badResponseExceptionMessage(int) {
    // ** addr: 0x86427c, size: 0x1d0
    // 0x86427c: EnterFrame
    //     0x86427c: stp             fp, lr, [SP, #-0x10]!
    //     0x864280: mov             fp, SP
    // 0x864284: AllocStack(0x28)
    //     0x864284: sub             SP, SP, #0x28
    // 0x864288: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x864288: stur            x1, [fp, #-0x10]
    // 0x86428c: CheckStackOverflow
    //     0x86428c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x864290: cmp             SP, x16
    //     0x864294: b.ls            #0x864444
    // 0x864298: cmp             x1, #0x64
    // 0x86429c: b.lt            #0x8642b4
    // 0x8642a0: cmp             x1, #0xc8
    // 0x8642a4: b.ge            #0x8642b4
    // 0x8642a8: r0 = "This is an informational response - the request was received, continuing processing"
    //     0x8642a8: add             x0, PP, #8, lsl #12  ; [pp+0x8500] "This is an informational response - the request was received, continuing processing"
    //     0x8642ac: ldr             x0, [x0, #0x500]
    // 0x8642b0: b               #0x86432c
    // 0x8642b4: cmp             x1, #0xc8
    // 0x8642b8: b.lt            #0x8642d0
    // 0x8642bc: cmp             x1, #0x12c
    // 0x8642c0: b.ge            #0x8642d0
    // 0x8642c4: r0 = "The request was successfully received, understood, and accepted"
    //     0x8642c4: add             x0, PP, #8, lsl #12  ; [pp+0x8508] "The request was successfully received, understood, and accepted"
    //     0x8642c8: ldr             x0, [x0, #0x508]
    // 0x8642cc: b               #0x86432c
    // 0x8642d0: cmp             x1, #0x12c
    // 0x8642d4: b.lt            #0x8642ec
    // 0x8642d8: cmp             x1, #0x190
    // 0x8642dc: b.ge            #0x8642ec
    // 0x8642e0: r0 = "Redirection: further action needs to be taken in order to complete the request"
    //     0x8642e0: add             x0, PP, #8, lsl #12  ; [pp+0x8510] "Redirection: further action needs to be taken in order to complete the request"
    //     0x8642e4: ldr             x0, [x0, #0x510]
    // 0x8642e8: b               #0x86432c
    // 0x8642ec: cmp             x1, #0x190
    // 0x8642f0: b.lt            #0x864308
    // 0x8642f4: cmp             x1, #0x1f4
    // 0x8642f8: b.ge            #0x864308
    // 0x8642fc: r0 = "Client error - the request contains bad syntax or cannot be fulfilled"
    //     0x8642fc: add             x0, PP, #8, lsl #12  ; [pp+0x8518] "Client error - the request contains bad syntax or cannot be fulfilled"
    //     0x864300: ldr             x0, [x0, #0x518]
    // 0x864304: b               #0x86432c
    // 0x864308: cmp             x1, #0x1f4
    // 0x86430c: b.lt            #0x864324
    // 0x864310: cmp             x1, #0x258
    // 0x864314: b.ge            #0x864324
    // 0x864318: r0 = "Server error - the server failed to fulfil an apparently valid request"
    //     0x864318: add             x0, PP, #8, lsl #12  ; [pp+0x8520] "Server error - the server failed to fulfil an apparently valid request"
    //     0x86431c: ldr             x0, [x0, #0x520]
    // 0x864320: b               #0x86432c
    // 0x864324: r0 = "A response with a status code that is not within the range of inclusive 100 to exclusive 600is a non-standard response, possibly due to the server\'s software"
    //     0x864324: add             x0, PP, #8, lsl #12  ; [pp+0x8528] "A response with a status code that is not within the range of inclusive 100 to exclusive 600is a non-standard response, possibly due to the server\'s software"
    //     0x864328: ldr             x0, [x0, #0x528]
    // 0x86432c: stur            x0, [fp, #-8]
    // 0x864330: r0 = StringBuffer()
    //     0x864330: bl              #0x620890  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x864334: mov             x1, x0
    // 0x864338: stur            x0, [fp, #-0x18]
    // 0x86433c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x86433c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x864340: r0 = StringBuffer()
    //     0x864340: bl              #0x6200bc  ; [dart:core] StringBuffer::StringBuffer
    // 0x864344: r1 = Null
    //     0x864344: mov             x1, NULL
    // 0x864348: r2 = 6
    //     0x864348: movz            x2, #0x6
    // 0x86434c: r0 = AllocateArray()
    //     0x86434c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x864350: mov             x2, x0
    // 0x864354: r16 = "This exception was thrown because the response has a status code of "
    //     0x864354: add             x16, PP, #8, lsl #12  ; [pp+0x8530] "This exception was thrown because the response has a status code of "
    //     0x864358: ldr             x16, [x16, #0x530]
    // 0x86435c: StoreField: r2->field_f = r16
    //     0x86435c: stur            w16, [x2, #0xf]
    // 0x864360: ldur            x3, [fp, #-0x10]
    // 0x864364: r0 = BoxInt64Instr(r3)
    //     0x864364: sbfiz           x0, x3, #1, #0x1f
    //     0x864368: cmp             x3, x0, asr #1
    //     0x86436c: b.eq            #0x864378
    //     0x864370: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x864374: stur            x3, [x0, #7]
    // 0x864378: stur            x0, [fp, #-0x20]
    // 0x86437c: StoreField: r2->field_13 = r0
    //     0x86437c: stur            w0, [x2, #0x13]
    // 0x864380: r16 = " and RequestOptions.validateStatus was configured to throw for this status code."
    //     0x864380: add             x16, PP, #8, lsl #12  ; [pp+0x8538] " and RequestOptions.validateStatus was configured to throw for this status code."
    //     0x864384: ldr             x16, [x16, #0x538]
    // 0x864388: ArrayStore: r2[0] = r16  ; List_4
    //     0x864388: stur            w16, [x2, #0x17]
    // 0x86438c: str             x2, [SP]
    // 0x864390: r0 = _interpolate()
    //     0x864390: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x864394: str             x0, [SP]
    // 0x864398: ldur            x1, [fp, #-0x18]
    // 0x86439c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x86439c: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8643a0: r0 = writeln()
    //     0x8643a0: bl              #0x86444c  ; [dart:core] StringBuffer::writeln
    // 0x8643a4: r1 = Null
    //     0x8643a4: mov             x1, NULL
    // 0x8643a8: r2 = 10
    //     0x8643a8: movz            x2, #0xa
    // 0x8643ac: r0 = AllocateArray()
    //     0x8643ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8643b0: r16 = "The status code of "
    //     0x8643b0: add             x16, PP, #8, lsl #12  ; [pp+0x8540] "The status code of "
    //     0x8643b4: ldr             x16, [x16, #0x540]
    // 0x8643b8: StoreField: r0->field_f = r16
    //     0x8643b8: stur            w16, [x0, #0xf]
    // 0x8643bc: ldur            x1, [fp, #-0x20]
    // 0x8643c0: StoreField: r0->field_13 = r1
    //     0x8643c0: stur            w1, [x0, #0x13]
    // 0x8643c4: r16 = " has the following meaning: \""
    //     0x8643c4: add             x16, PP, #8, lsl #12  ; [pp+0x8548] " has the following meaning: \""
    //     0x8643c8: ldr             x16, [x16, #0x548]
    // 0x8643cc: ArrayStore: r0[0] = r16  ; List_4
    //     0x8643cc: stur            w16, [x0, #0x17]
    // 0x8643d0: ldur            x1, [fp, #-8]
    // 0x8643d4: StoreField: r0->field_1b = r1
    //     0x8643d4: stur            w1, [x0, #0x1b]
    // 0x8643d8: r16 = "\""
    //     0x8643d8: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0x8643dc: ldr             x16, [x16, #0x550]
    // 0x8643e0: StoreField: r0->field_1f = r16
    //     0x8643e0: stur            w16, [x0, #0x1f]
    // 0x8643e4: str             x0, [SP]
    // 0x8643e8: r0 = _interpolate()
    //     0x8643e8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8643ec: str             x0, [SP]
    // 0x8643f0: ldur            x1, [fp, #-0x18]
    // 0x8643f4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8643f4: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8643f8: r0 = writeln()
    //     0x8643f8: bl              #0x86444c  ; [dart:core] StringBuffer::writeln
    // 0x8643fc: r16 = "Read more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status"
    //     0x8643fc: add             x16, PP, #8, lsl #12  ; [pp+0x8558] "Read more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status"
    //     0x864400: ldr             x16, [x16, #0x558]
    // 0x864404: str             x16, [SP]
    // 0x864408: ldur            x1, [fp, #-0x18]
    // 0x86440c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x86440c: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x864410: r0 = writeln()
    //     0x864410: bl              #0x86444c  ; [dart:core] StringBuffer::writeln
    // 0x864414: r16 = "In order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code."
    //     0x864414: add             x16, PP, #8, lsl #12  ; [pp+0x8560] "In order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code."
    //     0x864418: ldr             x16, [x16, #0x560]
    // 0x86441c: str             x16, [SP]
    // 0x864420: ldur            x1, [fp, #-0x18]
    // 0x864424: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x864424: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x864428: r0 = writeln()
    //     0x864428: bl              #0x86444c  ; [dart:core] StringBuffer::writeln
    // 0x86442c: ldur            x16, [fp, #-0x18]
    // 0x864430: str             x16, [SP]
    // 0x864434: r0 = toString()
    //     0x864434: bl              #0x1558aa8  ; [dart:core] StringBuffer::toString
    // 0x864438: LeaveFrame
    //     0x864438: mov             SP, fp
    //     0x86443c: ldp             fp, lr, [SP], #0x10
    // 0x864440: ret
    //     0x864440: ret             
    // 0x864444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x864444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x864448: b               #0x864298
  }
  factory _ DioException.receiveTimeout(/* No info */) {
    // ** addr: 0x868dbc, size: 0xb8
    // 0x868dbc: EnterFrame
    //     0x868dbc: stp             fp, lr, [SP, #-0x10]!
    //     0x868dc0: mov             fp, SP
    // 0x868dc4: AllocStack(0x30)
    //     0x868dc4: sub             SP, SP, #0x30
    // 0x868dc8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x868dc8: mov             x0, x2
    //     0x868dcc: stur            x2, [fp, #-8]
    //     0x868dd0: stur            x3, [fp, #-0x10]
    // 0x868dd4: CheckStackOverflow
    //     0x868dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868dd8: cmp             SP, x16
    //     0x868ddc: b.ls            #0x868e6c
    // 0x868de0: r1 = Null
    //     0x868de0: mov             x1, NULL
    // 0x868de4: r2 = 10
    //     0x868de4: movz            x2, #0xa
    // 0x868de8: r0 = AllocateArray()
    //     0x868de8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x868dec: r16 = "The request took longer than "
    //     0x868dec: add             x16, PP, #8, lsl #12  ; [pp+0x8b00] "The request took longer than "
    //     0x868df0: ldr             x16, [x16, #0xb00]
    // 0x868df4: StoreField: r0->field_f = r16
    //     0x868df4: stur            w16, [x0, #0xf]
    // 0x868df8: ldur            x1, [fp, #-0x10]
    // 0x868dfc: StoreField: r0->field_13 = r1
    //     0x868dfc: stur            w1, [x0, #0x13]
    // 0x868e00: r16 = " to receive data. It was aborted. To get rid of this exception, try raising the RequestOptions.receiveTimeout above the duration of "
    //     0x868e00: add             x16, PP, #8, lsl #12  ; [pp+0x8b08] " to receive data. It was aborted. To get rid of this exception, try raising the RequestOptions.receiveTimeout above the duration of "
    //     0x868e04: ldr             x16, [x16, #0xb08]
    // 0x868e08: ArrayStore: r0[0] = r16  ; List_4
    //     0x868e08: stur            w16, [x0, #0x17]
    // 0x868e0c: StoreField: r0->field_1b = r1
    //     0x868e0c: stur            w1, [x0, #0x1b]
    // 0x868e10: r16 = " or improve the response time of the server."
    //     0x868e10: add             x16, PP, #8, lsl #12  ; [pp+0x8b10] " or improve the response time of the server."
    //     0x868e14: ldr             x16, [x16, #0xb10]
    // 0x868e18: StoreField: r0->field_1f = r16
    //     0x868e18: stur            w16, [x0, #0x1f]
    // 0x868e1c: str             x0, [SP]
    // 0x868e20: r0 = _interpolate()
    //     0x868e20: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x868e24: stur            x0, [fp, #-0x10]
    // 0x868e28: r0 = DioException()
    //     0x868e28: bl              #0x86204c  ; AllocateDioExceptionStub -> DioException (size=0x20)
    // 0x868e2c: stur            x0, [fp, #-0x18]
    // 0x868e30: r16 = Instance_DioExceptionType
    //     0x868e30: add             x16, PP, #8, lsl #12  ; [pp+0x8b18] Obj!DioExceptionType@d75241
    //     0x868e34: ldr             x16, [x16, #0xb18]
    // 0x868e38: stp             NULL, x16, [SP, #8]
    // 0x868e3c: ldur            x16, [fp, #-0x10]
    // 0x868e40: str             x16, [SP]
    // 0x868e44: mov             x1, x0
    // 0x868e48: ldur            x3, [fp, #-8]
    // 0x868e4c: r2 = Null
    //     0x868e4c: mov             x2, NULL
    // 0x868e50: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x868e50: add             x4, PP, #8, lsl #12  ; [pp+0x84e8] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x868e54: ldr             x4, [x4, #0x4e8]
    // 0x868e58: r0 = DioException()
    //     0x868e58: bl              #0x861da8  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x868e5c: ldur            x0, [fp, #-0x18]
    // 0x868e60: LeaveFrame
    //     0x868e60: mov             SP, fp
    //     0x868e64: ldp             fp, lr, [SP], #0x10
    // 0x868e68: ret
    //     0x868e68: ret             
    // 0x868e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868e70: b               #0x868de0
  }
  factory _ DioException.connectionError(/* No info */) {
    // ** addr: 0x869e18, size: 0xb4
    // 0x869e18: EnterFrame
    //     0x869e18: stp             fp, lr, [SP, #-0x10]!
    //     0x869e1c: mov             fp, SP
    // 0x869e20: AllocStack(0x38)
    //     0x869e20: sub             SP, SP, #0x38
    // 0x869e24: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0x869e24: mov             x4, x2
    //     0x869e28: mov             x0, x3
    //     0x869e2c: stur            x3, [fp, #-0x10]
    //     0x869e30: mov             x3, x5
    //     0x869e34: stur            x2, [fp, #-8]
    //     0x869e38: stur            x5, [fp, #-0x18]
    // 0x869e3c: CheckStackOverflow
    //     0x869e3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869e40: cmp             SP, x16
    //     0x869e44: b.ls            #0x869ec4
    // 0x869e48: r1 = Null
    //     0x869e48: mov             x1, NULL
    // 0x869e4c: r2 = 6
    //     0x869e4c: movz            x2, #0x6
    // 0x869e50: r0 = AllocateArray()
    //     0x869e50: bl              #0x16f7198  ; AllocateArrayStub
    // 0x869e54: r16 = "The connection errored: "
    //     0x869e54: add             x16, PP, #8, lsl #12  ; [pp+0x8f40] "The connection errored: "
    //     0x869e58: ldr             x16, [x16, #0xf40]
    // 0x869e5c: StoreField: r0->field_f = r16
    //     0x869e5c: stur            w16, [x0, #0xf]
    // 0x869e60: ldur            x1, [fp, #-0x10]
    // 0x869e64: StoreField: r0->field_13 = r1
    //     0x869e64: stur            w1, [x0, #0x13]
    // 0x869e68: r16 = " This indicates an error which most likely cannot be solved by the library."
    //     0x869e68: add             x16, PP, #8, lsl #12  ; [pp+0x8f48] " This indicates an error which most likely cannot be solved by the library."
    //     0x869e6c: ldr             x16, [x16, #0xf48]
    // 0x869e70: ArrayStore: r0[0] = r16  ; List_4
    //     0x869e70: stur            w16, [x0, #0x17]
    // 0x869e74: str             x0, [SP]
    // 0x869e78: r0 = _interpolate()
    //     0x869e78: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x869e7c: stur            x0, [fp, #-0x10]
    // 0x869e80: r0 = DioException()
    //     0x869e80: bl              #0x86204c  ; AllocateDioExceptionStub -> DioException (size=0x20)
    // 0x869e84: stur            x0, [fp, #-0x20]
    // 0x869e88: r16 = Instance_DioExceptionType
    //     0x869e88: add             x16, PP, #8, lsl #12  ; [pp+0x8f50] Obj!DioExceptionType@d75261
    //     0x869e8c: ldr             x16, [x16, #0xf50]
    // 0x869e90: ldur            lr, [fp, #-0x10]
    // 0x869e94: stp             lr, x16, [SP, #8]
    // 0x869e98: str             NULL, [SP]
    // 0x869e9c: mov             x1, x0
    // 0x869ea0: ldur            x2, [fp, #-8]
    // 0x869ea4: ldur            x3, [fp, #-0x18]
    // 0x869ea8: r4 = const [0, 0x6, 0x3, 0x3, message, 0x4, response, 0x5, type, 0x3, null]
    //     0x869ea8: add             x4, PP, #8, lsl #12  ; [pp+0x8f58] List(11) [0, 0x6, 0x3, 0x3, "message", 0x4, "response", 0x5, "type", 0x3, Null]
    //     0x869eac: ldr             x4, [x4, #0xf58]
    // 0x869eb0: r0 = DioException()
    //     0x869eb0: bl              #0x861da8  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x869eb4: ldur            x0, [fp, #-0x20]
    // 0x869eb8: LeaveFrame
    //     0x869eb8: mov             SP, fp
    //     0x869ebc: ldp             fp, lr, [SP], #0x10
    // 0x869ec0: ret
    //     0x869ec0: ret             
    // 0x869ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869ec4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869ec8: b               #0x869e48
  }
  factory _ DioException.connectionTimeout(/* No info */) {
    // ** addr: 0x869ecc, size: 0xf4
    // 0x869ecc: EnterFrame
    //     0x869ecc: stp             fp, lr, [SP, #-0x10]!
    //     0x869ed0: mov             fp, SP
    // 0x869ed4: AllocStack(0x38)
    //     0x869ed4: sub             SP, SP, #0x38
    // 0x869ed8: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, {dynamic error = Null /* r4, fp-0x8 */})
    //     0x869ed8: mov             x0, x2
    //     0x869edc: stur            x2, [fp, #-0x10]
    //     0x869ee0: stur            x3, [fp, #-0x18]
    //     0x869ee4: ldur            w1, [x4, #0x13]
    //     0x869ee8: ldur            w2, [x4, #0x1f]
    //     0x869eec: add             x2, x2, HEAP, lsl #32
    //     0x869ef0: ldr             x16, [PP, #0x1b10]  ; [pp+0x1b10] "error"
    //     0x869ef4: cmp             w2, w16
    //     0x869ef8: b.ne            #0x869f18
    //     0x869efc: ldur            w2, [x4, #0x23]
    //     0x869f00: add             x2, x2, HEAP, lsl #32
    //     0x869f04: sub             w4, w1, w2
    //     0x869f08: add             x1, fp, w4, sxtw #2
    //     0x869f0c: ldr             x1, [x1, #8]
    //     0x869f10: mov             x4, x1
    //     0x869f14: b               #0x869f1c
    //     0x869f18: mov             x4, NULL
    //     0x869f1c: stur            x4, [fp, #-8]
    // 0x869f20: CheckStackOverflow
    //     0x869f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869f24: cmp             SP, x16
    //     0x869f28: b.ls            #0x869fb8
    // 0x869f2c: r1 = Null
    //     0x869f2c: mov             x1, NULL
    // 0x869f30: r2 = 10
    //     0x869f30: movz            x2, #0xa
    // 0x869f34: r0 = AllocateArray()
    //     0x869f34: bl              #0x16f7198  ; AllocateArrayStub
    // 0x869f38: r16 = "The request connection took longer than "
    //     0x869f38: add             x16, PP, #8, lsl #12  ; [pp+0x8f60] "The request connection took longer than "
    //     0x869f3c: ldr             x16, [x16, #0xf60]
    // 0x869f40: StoreField: r0->field_f = r16
    //     0x869f40: stur            w16, [x0, #0xf]
    // 0x869f44: ldur            x1, [fp, #-0x18]
    // 0x869f48: StoreField: r0->field_13 = r1
    //     0x869f48: stur            w1, [x0, #0x13]
    // 0x869f4c: r16 = " and it was aborted. To get rid of this exception, try raising the RequestOptions.connectTimeout above the duration of "
    //     0x869f4c: add             x16, PP, #8, lsl #12  ; [pp+0x8f68] " and it was aborted. To get rid of this exception, try raising the RequestOptions.connectTimeout above the duration of "
    //     0x869f50: ldr             x16, [x16, #0xf68]
    // 0x869f54: ArrayStore: r0[0] = r16  ; List_4
    //     0x869f54: stur            w16, [x0, #0x17]
    // 0x869f58: StoreField: r0->field_1b = r1
    //     0x869f58: stur            w1, [x0, #0x1b]
    // 0x869f5c: r16 = " or improve the response time of the server."
    //     0x869f5c: add             x16, PP, #8, lsl #12  ; [pp+0x8b10] " or improve the response time of the server."
    //     0x869f60: ldr             x16, [x16, #0xb10]
    // 0x869f64: StoreField: r0->field_1f = r16
    //     0x869f64: stur            w16, [x0, #0x1f]
    // 0x869f68: str             x0, [SP]
    // 0x869f6c: r0 = _interpolate()
    //     0x869f6c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x869f70: stur            x0, [fp, #-0x18]
    // 0x869f74: r0 = DioException()
    //     0x869f74: bl              #0x86204c  ; AllocateDioExceptionStub -> DioException (size=0x20)
    // 0x869f78: stur            x0, [fp, #-0x20]
    // 0x869f7c: r16 = Instance_DioExceptionType
    //     0x869f7c: add             x16, PP, #8, lsl #12  ; [pp+0x8f70] Obj!DioExceptionType@d75281
    //     0x869f80: ldr             x16, [x16, #0xf70]
    // 0x869f84: stp             NULL, x16, [SP, #8]
    // 0x869f88: ldur            x16, [fp, #-0x18]
    // 0x869f8c: str             x16, [SP]
    // 0x869f90: mov             x1, x0
    // 0x869f94: ldur            x2, [fp, #-8]
    // 0x869f98: ldur            x3, [fp, #-0x10]
    // 0x869f9c: r4 = const [0, 0x6, 0x3, 0x3, message, 0x5, response, 0x4, type, 0x3, null]
    //     0x869f9c: add             x4, PP, #8, lsl #12  ; [pp+0x84e8] List(11) [0, 0x6, 0x3, 0x3, "message", 0x5, "response", 0x4, "type", 0x3, Null]
    //     0x869fa0: ldr             x4, [x4, #0x4e8]
    // 0x869fa4: r0 = DioException()
    //     0x869fa4: bl              #0x861da8  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x869fa8: ldur            x0, [fp, #-0x20]
    // 0x869fac: LeaveFrame
    //     0x869fac: mov             SP, fp
    //     0x869fb0: ldp             fp, lr, [SP], #0x10
    // 0x869fb4: ret
    //     0x869fb4: ret             
    // 0x869fb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x869fb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x869fbc: b               #0x869f2c
  }
  factory _ DioException.requestCancelled(/* No info */) {
    // ** addr: 0xa10184, size: 0x78
    // 0xa10184: EnterFrame
    //     0xa10184: stp             fp, lr, [SP, #-0x10]!
    //     0xa10188: mov             fp, SP
    // 0xa1018c: AllocStack(0x38)
    //     0xa1018c: sub             SP, SP, #0x38
    // 0xa10190: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xa10190: mov             x0, x2
    //     0xa10194: stur            x2, [fp, #-8]
    //     0xa10198: stur            x3, [fp, #-0x10]
    // 0xa1019c: CheckStackOverflow
    //     0xa1019c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa101a0: cmp             SP, x16
    //     0xa101a4: b.ls            #0xa101f4
    // 0xa101a8: r0 = DioException()
    //     0xa101a8: bl              #0x86204c  ; AllocateDioExceptionStub -> DioException (size=0x20)
    // 0xa101ac: stur            x0, [fp, #-0x18]
    // 0xa101b0: r16 = Instance_DioExceptionType
    //     0xa101b0: add             x16, PP, #8, lsl #12  ; [pp+0x83e8] Obj!DioExceptionType@d75201
    //     0xa101b4: ldr             x16, [x16, #0x3e8]
    // 0xa101b8: stp             NULL, x16, [SP, #0x10]
    // 0xa101bc: ldur            x16, [fp, #-0x10]
    // 0xa101c0: r30 = "The request was manually cancelled by the user."
    //     0xa101c0: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3cba0] "The request was manually cancelled by the user."
    //     0xa101c4: ldr             lr, [lr, #0xba0]
    // 0xa101c8: stp             lr, x16, [SP]
    // 0xa101cc: mov             x1, x0
    // 0xa101d0: ldur            x3, [fp, #-8]
    // 0xa101d4: r2 = Null
    //     0xa101d4: mov             x2, NULL
    // 0xa101d8: r4 = const [0, 0x7, 0x4, 0x3, message, 0x6, response, 0x4, stackTrace, 0x5, type, 0x3, null]
    //     0xa101d8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3cba8] List(13) [0, 0x7, 0x4, 0x3, "message", 0x6, "response", 0x4, "stackTrace", 0x5, "type", 0x3, Null]
    //     0xa101dc: ldr             x4, [x4, #0xba8]
    // 0xa101e0: r0 = DioException()
    //     0xa101e0: bl              #0x861da8  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0xa101e4: ldur            x0, [fp, #-0x18]
    // 0xa101e8: LeaveFrame
    //     0xa101e8: mov             SP, fp
    //     0xa101ec: ldp             fp, lr, [SP], #0x10
    // 0xa101f0: ret
    //     0xa101f0: ret             
    // 0xa101f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa101f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa101f8: b               #0xa101a8
  }
  _ toString(/* No info */) {
    // ** addr: 0x15681cc, size: 0x80
    // 0x15681cc: EnterFrame
    //     0x15681cc: stp             fp, lr, [SP, #-0x10]!
    //     0x15681d0: mov             fp, SP
    // 0x15681d4: AllocStack(0x50)
    //     0x15681d4: sub             SP, SP, #0x50
    // 0x15681d8: CheckStackOverflow
    //     0x15681d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15681dc: cmp             SP, x16
    //     0x15681e0: b.ls            #0x1568244
    // 0x15681e4: r0 = InitLateStaticField(0xd34) // [package:dio/src/dio_exception.dart] DioException::readableStringBuilder
    //     0x15681e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15681e8: ldr             x0, [x0, #0x1a68]
    //     0x15681ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15681f0: cmp             w0, w16
    //     0x15681f4: b.ne            #0x1568204
    //     0x15681f8: add             x2, PP, #0x26, lsl #12  ; [pp+0x26310] Field <DioException.readableStringBuilder>: static late (offset: 0xd34)
    //     0x15681fc: ldr             x2, [x2, #0x310]
    //     0x1568200: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x1568204: r0 = Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static.
    //     0x1568204: add             x0, PP, #0x26, lsl #12  ; [pp+0x26318] Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static. (0x7fa73856824c)
    //     0x1568208: ldr             x0, [x0, #0x318]
    // 0x156820c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x156820c: ldur            w2, [x0, #0x17]
    // 0x1568210: DecompressPointer r2
    //     0x1568210: add             x2, x2, HEAP, lsl #32
    // 0x1568214: ldr             x1, [fp, #0x10]
    // 0x1568218: stur            x2, [fp, #-0x50]
    // 0x156821c: r0 = defaultDioExceptionReadableStringBuilder()
    //     0x156821c: bl              #0x156827c  ; [package:dio/src/dio_exception.dart] ::defaultDioExceptionReadableStringBuilder
    // 0x1568220: LeaveFrame
    //     0x1568220: mov             SP, fp
    //     0x1568224: ldp             fp, lr, [SP], #0x10
    // 0x1568228: ret
    //     0x1568228: ret             
    // 0x156822c: sub             SP, fp, #0x50
    // 0x1568230: ldr             x1, [fp, #0x10]
    // 0x1568234: r0 = defaultDioExceptionReadableStringBuilder()
    //     0x1568234: bl              #0x156827c  ; [package:dio/src/dio_exception.dart] ::defaultDioExceptionReadableStringBuilder
    // 0x1568238: LeaveFrame
    //     0x1568238: mov             SP, fp
    //     0x156823c: ldp             fp, lr, [SP], #0x10
    // 0x1568240: ret
    //     0x1568240: ret             
    // 0x1568244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568244: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1568248: b               #0x15681e4
  }
  static (dynamic, DioException) => String readableStringBuilder() {
    // ** addr: 0x1568414, size: 0xc
    // 0x1568414: r0 = Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static.
    //     0x1568414: add             x0, PP, #0x26, lsl #12  ; [pp+0x26318] Closure: (DioException) => String from Function 'defaultDioExceptionReadableStringBuilder': static. (0x7fa73856824c)
    //     0x1568418: ldr             x0, [x0, #0x318]
    // 0x156841c: ret
    //     0x156841c: ret             
  }
}

// class id: 7080, size: 0x14, field offset: 0x14
enum DioExceptionType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x1585754, size: 0x64
    // 0x1585754: EnterFrame
    //     0x1585754: stp             fp, lr, [SP, #-0x10]!
    //     0x1585758: mov             fp, SP
    // 0x158575c: AllocStack(0x10)
    //     0x158575c: sub             SP, SP, #0x10
    // 0x1585760: SetupParameters(DioExceptionType this /* r1 => r0, fp-0x8 */)
    //     0x1585760: mov             x0, x1
    //     0x1585764: stur            x1, [fp, #-8]
    // 0x1585768: CheckStackOverflow
    //     0x1585768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x158576c: cmp             SP, x16
    //     0x1585770: b.ls            #0x15857b0
    // 0x1585774: r1 = Null
    //     0x1585774: mov             x1, NULL
    // 0x1585778: r2 = 4
    //     0x1585778: movz            x2, #0x4
    // 0x158577c: r0 = AllocateArray()
    //     0x158577c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1585780: r16 = "DioExceptionType."
    //     0x1585780: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2afb8] "DioExceptionType."
    //     0x1585784: ldr             x16, [x16, #0xfb8]
    // 0x1585788: StoreField: r0->field_f = r16
    //     0x1585788: stur            w16, [x0, #0xf]
    // 0x158578c: ldur            x1, [fp, #-8]
    // 0x1585790: LoadField: r2 = r1->field_f
    //     0x1585790: ldur            w2, [x1, #0xf]
    // 0x1585794: DecompressPointer r2
    //     0x1585794: add             x2, x2, HEAP, lsl #32
    // 0x1585798: StoreField: r0->field_13 = r2
    //     0x1585798: stur            w2, [x0, #0x13]
    // 0x158579c: str             x0, [SP]
    // 0x15857a0: r0 = _interpolate()
    //     0x15857a0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x15857a4: LeaveFrame
    //     0x15857a4: mov             SP, fp
    //     0x15857a8: ldp             fp, lr, [SP], #0x10
    // 0x15857ac: ret
    //     0x15857ac: ret             
    // 0x15857b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15857b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15857b4: b               #0x1585774
  }
}
