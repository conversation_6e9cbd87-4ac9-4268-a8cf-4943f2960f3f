// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart

// class id: 1049436, size: 0x8
class :: {
}

// class id: 3315, size: 0x14, field offset: 0x14
class _ProductGridItemState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb82b68, size: 0xf4
    // 0xb82b68: EnterFrame
    //     0xb82b68: stp             fp, lr, [SP, #-0x10]!
    //     0xb82b6c: mov             fp, SP
    // 0xb82b70: AllocStack(0x28)
    //     0xb82b70: sub             SP, SP, #0x28
    // 0xb82b74: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */)
    //     0xb82b74: stur            x1, [fp, #-8]
    // 0xb82b78: CheckStackOverflow
    //     0xb82b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb82b7c: cmp             SP, x16
    //     0xb82b80: b.ls            #0xb82c50
    // 0xb82b84: r1 = 1
    //     0xb82b84: movz            x1, #0x1
    // 0xb82b88: r0 = AllocateContext()
    //     0xb82b88: bl              #0x16f6108  ; AllocateContextStub
    // 0xb82b8c: mov             x2, x0
    // 0xb82b90: ldur            x0, [fp, #-8]
    // 0xb82b94: stur            x2, [fp, #-0x10]
    // 0xb82b98: StoreField: r2->field_f = r0
    //     0xb82b98: stur            w0, [x2, #0xf]
    // 0xb82b9c: mov             x1, x0
    // 0xb82ba0: r0 = _calculateAspectRatio()
    //     0xb82ba0: bl              #0xb82c7c  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_calculateAspectRatio
    // 0xb82ba4: stur            d0, [fp, #-0x20]
    // 0xb82ba8: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xb82ba8: bl              #0xa4d630  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xb82bac: mov             x1, x0
    // 0xb82bb0: r0 = 2
    //     0xb82bb0: movz            x0, #0x2
    // 0xb82bb4: stur            x1, [fp, #-0x18]
    // 0xb82bb8: StoreField: r1->field_7 = r0
    //     0xb82bb8: stur            x0, [x1, #7]
    // 0xb82bbc: StoreField: r1->field_f = rZR
    //     0xb82bbc: stur            xzr, [x1, #0xf]
    // 0xb82bc0: d0 = 4.000000
    //     0xb82bc0: fmov            d0, #4.00000000
    // 0xb82bc4: ArrayStore: r1[0] = d0  ; List_8
    //     0xb82bc4: stur            d0, [x1, #0x17]
    // 0xb82bc8: ldur            d0, [fp, #-0x20]
    // 0xb82bcc: StoreField: r1->field_1f = d0
    //     0xb82bcc: stur            d0, [x1, #0x1f]
    // 0xb82bd0: ldur            x0, [fp, #-8]
    // 0xb82bd4: LoadField: r2 = r0->field_b
    //     0xb82bd4: ldur            w2, [x0, #0xb]
    // 0xb82bd8: DecompressPointer r2
    //     0xb82bd8: add             x2, x2, HEAP, lsl #32
    // 0xb82bdc: cmp             w2, NULL
    // 0xb82be0: b.eq            #0xb82c58
    // 0xb82be4: LoadField: r0 = r2->field_b
    //     0xb82be4: ldur            w0, [x2, #0xb]
    // 0xb82be8: DecompressPointer r0
    //     0xb82be8: add             x0, x0, HEAP, lsl #32
    // 0xb82bec: r2 = LoadClassIdInstr(r0)
    //     0xb82bec: ldur            x2, [x0, #-1]
    //     0xb82bf0: ubfx            x2, x2, #0xc, #0x14
    // 0xb82bf4: str             x0, [SP]
    // 0xb82bf8: mov             x0, x2
    // 0xb82bfc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb82bfc: movz            x17, #0xc898
    //     0xb82c00: add             lr, x0, x17
    //     0xb82c04: ldr             lr, [x21, lr, lsl #3]
    //     0xb82c08: blr             lr
    // 0xb82c0c: ldur            x2, [fp, #-0x10]
    // 0xb82c10: r1 = Function '<anonymous closure>':.
    //     0xb82c10: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d78] AnonymousClosure: (0xb82d18), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xb82b68)
    //     0xb82c14: ldr             x1, [x1, #0xd78]
    // 0xb82c18: stur            x0, [fp, #-8]
    // 0xb82c1c: r0 = AllocateClosure()
    //     0xb82c1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb82c20: stur            x0, [fp, #-0x10]
    // 0xb82c24: r0 = GridView()
    //     0xb82c24: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xb82c28: mov             x1, x0
    // 0xb82c2c: ldur            x2, [fp, #-0x18]
    // 0xb82c30: ldur            x3, [fp, #-0x10]
    // 0xb82c34: ldur            x5, [fp, #-8]
    // 0xb82c38: stur            x0, [fp, #-8]
    // 0xb82c3c: r0 = GridView.builder()
    //     0xb82c3c: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xb82c40: ldur            x0, [fp, #-8]
    // 0xb82c44: LeaveFrame
    //     0xb82c44: mov             SP, fp
    //     0xb82c48: ldp             fp, lr, [SP], #0x10
    // 0xb82c4c: ret
    //     0xb82c4c: ret             
    // 0xb82c50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb82c50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb82c54: b               #0xb82b84
    // 0xb82c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82c58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateAspectRatio(/* No info */) {
    // ** addr: 0xb82c7c, size: 0x9c
    // 0xb82c7c: LoadField: r0 = r1->field_b
    //     0xb82c7c: ldur            w0, [x1, #0xb]
    // 0xb82c80: DecompressPointer r0
    //     0xb82c80: add             x0, x0, HEAP, lsl #32
    // 0xb82c84: cmp             w0, NULL
    // 0xb82c88: b.eq            #0xb82d0c
    // 0xb82c8c: LoadField: r1 = r0->field_f
    //     0xb82c8c: ldur            w1, [x0, #0xf]
    // 0xb82c90: DecompressPointer r1
    //     0xb82c90: add             x1, x1, HEAP, lsl #32
    // 0xb82c94: LoadField: r0 = r1->field_1f
    //     0xb82c94: ldur            w0, [x1, #0x1f]
    // 0xb82c98: DecompressPointer r0
    //     0xb82c98: add             x0, x0, HEAP, lsl #32
    // 0xb82c9c: cmp             w0, NULL
    // 0xb82ca0: b.ne            #0xb82cac
    // 0xb82ca4: r0 = Null
    //     0xb82ca4: mov             x0, NULL
    // 0xb82ca8: b               #0xb82cb8
    // 0xb82cac: LoadField: r2 = r0->field_7
    //     0xb82cac: ldur            w2, [x0, #7]
    // 0xb82cb0: DecompressPointer r2
    //     0xb82cb0: add             x2, x2, HEAP, lsl #32
    // 0xb82cb4: mov             x0, x2
    // 0xb82cb8: cmp             w0, NULL
    // 0xb82cbc: b.eq            #0xb82d00
    // 0xb82cc0: tbnz            w0, #4, #0xb82d00
    // 0xb82cc4: LoadField: r0 = r1->field_3f
    //     0xb82cc4: ldur            w0, [x1, #0x3f]
    // 0xb82cc8: DecompressPointer r0
    //     0xb82cc8: add             x0, x0, HEAP, lsl #32
    // 0xb82ccc: cmp             w0, NULL
    // 0xb82cd0: b.ne            #0xb82cdc
    // 0xb82cd4: r0 = Null
    //     0xb82cd4: mov             x0, NULL
    // 0xb82cd8: b               #0xb82ce8
    // 0xb82cdc: LoadField: r1 = r0->field_23
    //     0xb82cdc: ldur            w1, [x0, #0x23]
    // 0xb82ce0: DecompressPointer r1
    //     0xb82ce0: add             x1, x1, HEAP, lsl #32
    // 0xb82ce4: mov             x0, x1
    // 0xb82ce8: cmp             w0, NULL
    // 0xb82cec: b.eq            #0xb82d00
    // 0xb82cf0: tbnz            w0, #4, #0xb82d00
    // 0xb82cf4: d0 = 0.689655
    //     0xb82cf4: add             x17, PP, #0x61, lsl #12  ; [pp+0x61df8] IMM: double(0.6896551724137931) from 0x3fe611a7b9611a7c
    //     0xb82cf8: ldr             d0, [x17, #0xdf8]
    // 0xb82cfc: b               #0xb82d08
    // 0xb82d00: d0 = 0.571429
    //     0xb82d00: add             x17, PP, #0x61, lsl #12  ; [pp+0x61e00] IMM: double(0.5714285714285714) from 0x3fe2492492492492
    //     0xb82d04: ldr             d0, [x17, #0xe00]
    // 0xb82d08: ret
    //     0xb82d08: ret             
    // 0xb82d0c: EnterFrame
    //     0xb82d0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb82d10: mov             fp, SP
    // 0xb82d14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb82d14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb82d18, size: 0x82c
    // 0xb82d18: EnterFrame
    //     0xb82d18: stp             fp, lr, [SP, #-0x10]!
    //     0xb82d1c: mov             fp, SP
    // 0xb82d20: AllocStack(0x70)
    //     0xb82d20: sub             SP, SP, #0x70
    // 0xb82d24: SetupParameters()
    //     0xb82d24: ldr             x0, [fp, #0x20]
    //     0xb82d28: ldur            w1, [x0, #0x17]
    //     0xb82d2c: add             x1, x1, HEAP, lsl #32
    //     0xb82d30: stur            x1, [fp, #-8]
    // 0xb82d34: CheckStackOverflow
    //     0xb82d34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb82d38: cmp             SP, x16
    //     0xb82d3c: b.ls            #0xb83538
    // 0xb82d40: r1 = 1
    //     0xb82d40: movz            x1, #0x1
    // 0xb82d44: r0 = AllocateContext()
    //     0xb82d44: bl              #0x16f6108  ; AllocateContextStub
    // 0xb82d48: mov             x2, x0
    // 0xb82d4c: ldur            x1, [fp, #-8]
    // 0xb82d50: stur            x2, [fp, #-0x10]
    // 0xb82d54: StoreField: r2->field_b = r1
    //     0xb82d54: stur            w1, [x2, #0xb]
    // 0xb82d58: LoadField: r0 = r1->field_f
    //     0xb82d58: ldur            w0, [x1, #0xf]
    // 0xb82d5c: DecompressPointer r0
    //     0xb82d5c: add             x0, x0, HEAP, lsl #32
    // 0xb82d60: LoadField: r3 = r0->field_b
    //     0xb82d60: ldur            w3, [x0, #0xb]
    // 0xb82d64: DecompressPointer r3
    //     0xb82d64: add             x3, x3, HEAP, lsl #32
    // 0xb82d68: cmp             w3, NULL
    // 0xb82d6c: b.eq            #0xb83540
    // 0xb82d70: LoadField: r0 = r3->field_b
    //     0xb82d70: ldur            w0, [x3, #0xb]
    // 0xb82d74: DecompressPointer r0
    //     0xb82d74: add             x0, x0, HEAP, lsl #32
    // 0xb82d78: r3 = LoadClassIdInstr(r0)
    //     0xb82d78: ldur            x3, [x0, #-1]
    //     0xb82d7c: ubfx            x3, x3, #0xc, #0x14
    // 0xb82d80: ldr             x16, [fp, #0x10]
    // 0xb82d84: stp             x16, x0, [SP]
    // 0xb82d88: mov             x0, x3
    // 0xb82d8c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb82d8c: sub             lr, x0, #0xb7
    //     0xb82d90: ldr             lr, [x21, lr, lsl #3]
    //     0xb82d94: blr             lr
    // 0xb82d98: mov             x4, x0
    // 0xb82d9c: ldur            x3, [fp, #-0x10]
    // 0xb82da0: stur            x4, [fp, #-0x18]
    // 0xb82da4: StoreField: r3->field_f = r0
    //     0xb82da4: stur            w0, [x3, #0xf]
    //     0xb82da8: ldurb           w16, [x3, #-1]
    //     0xb82dac: ldurb           w17, [x0, #-1]
    //     0xb82db0: and             x16, x17, x16, lsr #2
    //     0xb82db4: tst             x16, HEAP, lsr #32
    //     0xb82db8: b.eq            #0xb82dc0
    //     0xb82dbc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb82dc0: cmp             w4, NULL
    // 0xb82dc4: b.ne            #0xb82dd8
    // 0xb82dc8: r0 = Instance_SizedBox
    //     0xb82dc8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb82dcc: LeaveFrame
    //     0xb82dcc: mov             SP, fp
    //     0xb82dd0: ldp             fp, lr, [SP], #0x10
    // 0xb82dd4: ret
    //     0xb82dd4: ret             
    // 0xb82dd8: ldr             x0, [fp, #0x10]
    // 0xb82ddc: r1 = Null
    //     0xb82ddc: mov             x1, NULL
    // 0xb82de0: r2 = 8
    //     0xb82de0: movz            x2, #0x8
    // 0xb82de4: r0 = AllocateArray()
    //     0xb82de4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb82de8: r16 = "product_"
    //     0xb82de8: add             x16, PP, #0x61, lsl #12  ; [pp+0x61b88] "product_"
    //     0xb82dec: ldr             x16, [x16, #0xb88]
    // 0xb82df0: StoreField: r0->field_f = r16
    //     0xb82df0: stur            w16, [x0, #0xf]
    // 0xb82df4: ldur            x1, [fp, #-0x18]
    // 0xb82df8: LoadField: r2 = r1->field_47
    //     0xb82df8: ldur            w2, [x1, #0x47]
    // 0xb82dfc: DecompressPointer r2
    //     0xb82dfc: add             x2, x2, HEAP, lsl #32
    // 0xb82e00: StoreField: r0->field_13 = r2
    //     0xb82e00: stur            w2, [x0, #0x13]
    // 0xb82e04: r16 = "_"
    //     0xb82e04: ldr             x16, [PP, #0x45a8]  ; [pp+0x45a8] "_"
    // 0xb82e08: ArrayStore: r0[0] = r16  ; List_4
    //     0xb82e08: stur            w16, [x0, #0x17]
    // 0xb82e0c: ldr             x2, [fp, #0x10]
    // 0xb82e10: StoreField: r0->field_1b = r2
    //     0xb82e10: stur            w2, [x0, #0x1b]
    // 0xb82e14: str             x0, [SP]
    // 0xb82e18: r0 = _interpolate()
    //     0xb82e18: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb82e1c: r1 = <String>
    //     0xb82e1c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb82e20: stur            x0, [fp, #-0x20]
    // 0xb82e24: r0 = ValueKey()
    //     0xb82e24: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xb82e28: mov             x1, x0
    // 0xb82e2c: ldur            x0, [fp, #-0x20]
    // 0xb82e30: stur            x1, [fp, #-0x28]
    // 0xb82e34: StoreField: r1->field_b = r0
    //     0xb82e34: stur            w0, [x1, #0xb]
    // 0xb82e38: r0 = Radius()
    //     0xb82e38: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb82e3c: d0 = 12.000000
    //     0xb82e3c: fmov            d0, #12.00000000
    // 0xb82e40: stur            x0, [fp, #-0x20]
    // 0xb82e44: StoreField: r0->field_7 = d0
    //     0xb82e44: stur            d0, [x0, #7]
    // 0xb82e48: StoreField: r0->field_f = d0
    //     0xb82e48: stur            d0, [x0, #0xf]
    // 0xb82e4c: r0 = BorderRadius()
    //     0xb82e4c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb82e50: mov             x1, x0
    // 0xb82e54: ldur            x0, [fp, #-0x20]
    // 0xb82e58: stur            x1, [fp, #-0x30]
    // 0xb82e5c: StoreField: r1->field_7 = r0
    //     0xb82e5c: stur            w0, [x1, #7]
    // 0xb82e60: StoreField: r1->field_b = r0
    //     0xb82e60: stur            w0, [x1, #0xb]
    // 0xb82e64: StoreField: r1->field_f = r0
    //     0xb82e64: stur            w0, [x1, #0xf]
    // 0xb82e68: StoreField: r1->field_13 = r0
    //     0xb82e68: stur            w0, [x1, #0x13]
    // 0xb82e6c: r0 = RoundedRectangleBorder()
    //     0xb82e6c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb82e70: mov             x2, x0
    // 0xb82e74: ldur            x0, [fp, #-0x30]
    // 0xb82e78: stur            x2, [fp, #-0x20]
    // 0xb82e7c: StoreField: r2->field_b = r0
    //     0xb82e7c: stur            w0, [x2, #0xb]
    // 0xb82e80: r0 = Instance_BorderSide
    //     0xb82e80: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb82e84: ldr             x0, [x0, #0xe20]
    // 0xb82e88: StoreField: r2->field_7 = r0
    //     0xb82e88: stur            w0, [x2, #7]
    // 0xb82e8c: ldr             x1, [fp, #0x18]
    // 0xb82e90: r0 = of()
    //     0xb82e90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb82e94: LoadField: r1 = r0->field_5b
    //     0xb82e94: ldur            w1, [x0, #0x5b]
    // 0xb82e98: DecompressPointer r1
    //     0xb82e98: add             x1, x1, HEAP, lsl #32
    // 0xb82e9c: r0 = LoadClassIdInstr(r1)
    //     0xb82e9c: ldur            x0, [x1, #-1]
    //     0xb82ea0: ubfx            x0, x0, #0xc, #0x14
    // 0xb82ea4: d0 = 0.030000
    //     0xb82ea4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb82ea8: ldr             d0, [x17, #0x238]
    // 0xb82eac: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb82eac: sub             lr, x0, #0xffa
    //     0xb82eb0: ldr             lr, [x21, lr, lsl #3]
    //     0xb82eb4: blr             lr
    // 0xb82eb8: mov             x3, x0
    // 0xb82ebc: ldur            x0, [fp, #-0x18]
    // 0xb82ec0: stur            x3, [fp, #-0x38]
    // 0xb82ec4: LoadField: r1 = r0->field_e7
    //     0xb82ec4: ldur            w1, [x0, #0xe7]
    // 0xb82ec8: DecompressPointer r1
    //     0xb82ec8: add             x1, x1, HEAP, lsl #32
    // 0xb82ecc: cmp             w1, NULL
    // 0xb82ed0: b.ne            #0xb82edc
    // 0xb82ed4: r1 = Null
    //     0xb82ed4: mov             x1, NULL
    // 0xb82ed8: b               #0xb82ee4
    // 0xb82edc: LoadField: r2 = r1->field_b
    //     0xb82edc: ldur            w2, [x1, #0xb]
    // 0xb82ee0: mov             x1, x2
    // 0xb82ee4: cmp             w1, NULL
    // 0xb82ee8: b.ne            #0xb82ef4
    // 0xb82eec: r1 = 0
    //     0xb82eec: movz            x1, #0
    // 0xb82ef0: b               #0xb82efc
    // 0xb82ef4: r2 = LoadInt32Instr(r1)
    //     0xb82ef4: sbfx            x2, x1, #1, #0x1f
    // 0xb82ef8: mov             x1, x2
    // 0xb82efc: ldur            x4, [fp, #-0x20]
    // 0xb82f00: lsl             x5, x1, #1
    // 0xb82f04: stur            x5, [fp, #-0x30]
    // 0xb82f08: r1 = Function '<anonymous closure>':.
    //     0xb82f08: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d80] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb82f0c: ldr             x1, [x1, #0xd80]
    // 0xb82f10: r2 = Null
    //     0xb82f10: mov             x2, NULL
    // 0xb82f14: r0 = AllocateClosure()
    //     0xb82f14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb82f18: ldur            x2, [fp, #-0x10]
    // 0xb82f1c: r1 = Function '<anonymous closure>':.
    //     0xb82f1c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d88] AnonymousClosure: (0xb843ac), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xb82b68)
    //     0xb82f20: ldr             x1, [x1, #0xd88]
    // 0xb82f24: stur            x0, [fp, #-0x40]
    // 0xb82f28: r0 = AllocateClosure()
    //     0xb82f28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb82f2c: stur            x0, [fp, #-0x48]
    // 0xb82f30: r0 = PageView()
    //     0xb82f30: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb82f34: mov             x1, x0
    // 0xb82f38: ldur            x2, [fp, #-0x48]
    // 0xb82f3c: ldur            x3, [fp, #-0x30]
    // 0xb82f40: ldur            x5, [fp, #-0x40]
    // 0xb82f44: stur            x0, [fp, #-0x30]
    // 0xb82f48: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xb82f48: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xb82f4c: r0 = PageView.builder()
    //     0xb82f4c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb82f50: r0 = AspectRatio()
    //     0xb82f50: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb82f54: d0 = 1.000000
    //     0xb82f54: fmov            d0, #1.00000000
    // 0xb82f58: stur            x0, [fp, #-0x40]
    // 0xb82f5c: StoreField: r0->field_f = d0
    //     0xb82f5c: stur            d0, [x0, #0xf]
    // 0xb82f60: ldur            x1, [fp, #-0x30]
    // 0xb82f64: StoreField: r0->field_b = r1
    //     0xb82f64: stur            w1, [x0, #0xb]
    // 0xb82f68: r0 = Card()
    //     0xb82f68: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb82f6c: mov             x2, x0
    // 0xb82f70: ldur            x0, [fp, #-0x38]
    // 0xb82f74: stur            x2, [fp, #-0x30]
    // 0xb82f78: StoreField: r2->field_b = r0
    //     0xb82f78: stur            w0, [x2, #0xb]
    // 0xb82f7c: r0 = 0.000000
    //     0xb82f7c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb82f80: ArrayStore: r2[0] = r0  ; List_4
    //     0xb82f80: stur            w0, [x2, #0x17]
    // 0xb82f84: ldur            x0, [fp, #-0x20]
    // 0xb82f88: StoreField: r2->field_1b = r0
    //     0xb82f88: stur            w0, [x2, #0x1b]
    // 0xb82f8c: r0 = true
    //     0xb82f8c: add             x0, NULL, #0x20  ; true
    // 0xb82f90: StoreField: r2->field_1f = r0
    //     0xb82f90: stur            w0, [x2, #0x1f]
    // 0xb82f94: ldur            x1, [fp, #-0x40]
    // 0xb82f98: StoreField: r2->field_2f = r1
    //     0xb82f98: stur            w1, [x2, #0x2f]
    // 0xb82f9c: StoreField: r2->field_2b = r0
    //     0xb82f9c: stur            w0, [x2, #0x2b]
    // 0xb82fa0: r0 = Instance__CardVariant
    //     0xb82fa0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb82fa4: ldr             x0, [x0, #0xa68]
    // 0xb82fa8: StoreField: r2->field_33 = r0
    //     0xb82fa8: stur            w0, [x2, #0x33]
    // 0xb82fac: ldur            x0, [fp, #-0x18]
    // 0xb82fb0: LoadField: r1 = r0->field_53
    //     0xb82fb0: ldur            w1, [x0, #0x53]
    // 0xb82fb4: DecompressPointer r1
    //     0xb82fb4: add             x1, x1, HEAP, lsl #32
    // 0xb82fb8: cmp             w1, NULL
    // 0xb82fbc: b.ne            #0xb82fc8
    // 0xb82fc0: r1 = Null
    //     0xb82fc0: mov             x1, NULL
    // 0xb82fc4: b               #0xb82fd4
    // 0xb82fc8: LoadField: r3 = r1->field_b
    //     0xb82fc8: ldur            w3, [x1, #0xb]
    // 0xb82fcc: DecompressPointer r3
    //     0xb82fcc: add             x3, x3, HEAP, lsl #32
    // 0xb82fd0: mov             x1, x3
    // 0xb82fd4: cmp             w1, NULL
    // 0xb82fd8: b.ne            #0xb82fe0
    // 0xb82fdc: r1 = ""
    //     0xb82fdc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb82fe0: ldr             x4, [fp, #0x10]
    // 0xb82fe4: ldur            x5, [fp, #-8]
    // 0xb82fe8: ldur            x3, [fp, #-0x28]
    // 0xb82fec: r0 = capitalizeFirstWord()
    //     0xb82fec: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb82ff0: ldr             x1, [fp, #0x18]
    // 0xb82ff4: stur            x0, [fp, #-0x20]
    // 0xb82ff8: r0 = of()
    //     0xb82ff8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb82ffc: LoadField: r1 = r0->field_87
    //     0xb82ffc: ldur            w1, [x0, #0x87]
    // 0xb83000: DecompressPointer r1
    //     0xb83000: add             x1, x1, HEAP, lsl #32
    // 0xb83004: LoadField: r0 = r1->field_2b
    //     0xb83004: ldur            w0, [x1, #0x2b]
    // 0xb83008: DecompressPointer r0
    //     0xb83008: add             x0, x0, HEAP, lsl #32
    // 0xb8300c: r16 = 12.000000
    //     0xb8300c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb83010: ldr             x16, [x16, #0x9e8]
    // 0xb83014: r30 = Instance_Color
    //     0xb83014: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb83018: stp             lr, x16, [SP]
    // 0xb8301c: mov             x1, x0
    // 0xb83020: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb83020: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb83024: ldr             x4, [x4, #0xaa0]
    // 0xb83028: r0 = copyWith()
    //     0xb83028: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8302c: stur            x0, [fp, #-0x38]
    // 0xb83030: r0 = Text()
    //     0xb83030: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb83034: mov             x1, x0
    // 0xb83038: ldur            x0, [fp, #-0x20]
    // 0xb8303c: stur            x1, [fp, #-0x40]
    // 0xb83040: StoreField: r1->field_b = r0
    //     0xb83040: stur            w0, [x1, #0xb]
    // 0xb83044: ldur            x0, [fp, #-0x38]
    // 0xb83048: StoreField: r1->field_13 = r0
    //     0xb83048: stur            w0, [x1, #0x13]
    // 0xb8304c: r0 = Instance_TextOverflow
    //     0xb8304c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb83050: ldr             x0, [x0, #0xe10]
    // 0xb83054: StoreField: r1->field_2b = r0
    //     0xb83054: stur            w0, [x1, #0x2b]
    // 0xb83058: r2 = 4
    //     0xb83058: movz            x2, #0x4
    // 0xb8305c: StoreField: r1->field_37 = r2
    //     0xb8305c: stur            w2, [x1, #0x37]
    // 0xb83060: r0 = Container()
    //     0xb83060: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb83064: stur            x0, [fp, #-0x20]
    // 0xb83068: r16 = 100.000000
    //     0xb83068: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb8306c: r30 = Instance_EdgeInsets
    //     0xb8306c: add             lr, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb83070: ldr             lr, [lr, #0x930]
    // 0xb83074: stp             lr, x16, [SP, #8]
    // 0xb83078: ldur            x16, [fp, #-0x40]
    // 0xb8307c: str             x16, [SP]
    // 0xb83080: mov             x1, x0
    // 0xb83084: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb83084: add             x4, PP, #0x44, lsl #12  ; [pp+0x44260] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb83088: ldr             x4, [x4, #0x260]
    // 0xb8308c: r0 = Container()
    //     0xb8308c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb83090: ldur            x0, [fp, #-8]
    // 0xb83094: LoadField: r1 = r0->field_f
    //     0xb83094: ldur            w1, [x0, #0xf]
    // 0xb83098: DecompressPointer r1
    //     0xb83098: add             x1, x1, HEAP, lsl #32
    // 0xb8309c: ldr             x2, [fp, #0x10]
    // 0xb830a0: r3 = LoadInt32Instr(r2)
    //     0xb830a0: sbfx            x3, x2, #1, #0x1f
    //     0xb830a4: tbz             w2, #0, #0xb830ac
    //     0xb830a8: ldur            x3, [x2, #7]
    // 0xb830ac: mov             x2, x3
    // 0xb830b0: stur            x3, [fp, #-0x50]
    // 0xb830b4: r0 = _buildRatingSection()
    //     0xb830b4: bl              #0xb839a4  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildRatingSection
    // 0xb830b8: r1 = Null
    //     0xb830b8: mov             x1, NULL
    // 0xb830bc: r2 = 6
    //     0xb830bc: movz            x2, #0x6
    // 0xb830c0: stur            x0, [fp, #-0x38]
    // 0xb830c4: r0 = AllocateArray()
    //     0xb830c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb830c8: r16 = " "
    //     0xb830c8: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb830cc: StoreField: r0->field_f = r16
    //     0xb830cc: stur            w16, [x0, #0xf]
    // 0xb830d0: ldur            x1, [fp, #-0x18]
    // 0xb830d4: LoadField: r2 = r1->field_f3
    //     0xb830d4: ldur            w2, [x1, #0xf3]
    // 0xb830d8: DecompressPointer r2
    //     0xb830d8: add             x2, x2, HEAP, lsl #32
    // 0xb830dc: StoreField: r0->field_13 = r2
    //     0xb830dc: stur            w2, [x0, #0x13]
    // 0xb830e0: r16 = " "
    //     0xb830e0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb830e4: ArrayStore: r0[0] = r16  ; List_4
    //     0xb830e4: stur            w16, [x0, #0x17]
    // 0xb830e8: str             x0, [SP]
    // 0xb830ec: r0 = _interpolate()
    //     0xb830ec: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb830f0: ldr             x1, [fp, #0x18]
    // 0xb830f4: stur            x0, [fp, #-0x40]
    // 0xb830f8: r0 = of()
    //     0xb830f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb830fc: LoadField: r1 = r0->field_87
    //     0xb830fc: ldur            w1, [x0, #0x87]
    // 0xb83100: DecompressPointer r1
    //     0xb83100: add             x1, x1, HEAP, lsl #32
    // 0xb83104: LoadField: r0 = r1->field_2b
    //     0xb83104: ldur            w0, [x1, #0x2b]
    // 0xb83108: DecompressPointer r0
    //     0xb83108: add             x0, x0, HEAP, lsl #32
    // 0xb8310c: stur            x0, [fp, #-0x48]
    // 0xb83110: r1 = Instance_Color
    //     0xb83110: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb83114: d0 = 0.700000
    //     0xb83114: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb83118: ldr             d0, [x17, #0xf48]
    // 0xb8311c: r0 = withOpacity()
    //     0xb8311c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb83120: r16 = 12.000000
    //     0xb83120: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb83124: ldr             x16, [x16, #0x9e8]
    // 0xb83128: stp             x0, x16, [SP]
    // 0xb8312c: ldur            x1, [fp, #-0x48]
    // 0xb83130: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb83130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb83134: ldr             x4, [x4, #0xaa0]
    // 0xb83138: r0 = copyWith()
    //     0xb83138: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8313c: stur            x0, [fp, #-0x48]
    // 0xb83140: r0 = Text()
    //     0xb83140: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb83144: mov             x1, x0
    // 0xb83148: ldur            x0, [fp, #-0x40]
    // 0xb8314c: stur            x1, [fp, #-0x58]
    // 0xb83150: StoreField: r1->field_b = r0
    //     0xb83150: stur            w0, [x1, #0xb]
    // 0xb83154: ldur            x0, [fp, #-0x48]
    // 0xb83158: StoreField: r1->field_13 = r0
    //     0xb83158: stur            w0, [x1, #0x13]
    // 0xb8315c: r0 = WidgetSpan()
    //     0xb8315c: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xb83160: mov             x3, x0
    // 0xb83164: ldur            x0, [fp, #-0x58]
    // 0xb83168: stur            x3, [fp, #-0x48]
    // 0xb8316c: StoreField: r3->field_13 = r0
    //     0xb8316c: stur            w0, [x3, #0x13]
    // 0xb83170: r0 = Instance_PlaceholderAlignment
    //     0xb83170: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xb83174: ldr             x0, [x0, #0xa0]
    // 0xb83178: StoreField: r3->field_b = r0
    //     0xb83178: stur            w0, [x3, #0xb]
    // 0xb8317c: ldur            x0, [fp, #-0x18]
    // 0xb83180: LoadField: r4 = r0->field_fb
    //     0xb83180: ldur            w4, [x0, #0xfb]
    // 0xb83184: DecompressPointer r4
    //     0xb83184: add             x4, x4, HEAP, lsl #32
    // 0xb83188: stur            x4, [fp, #-0x40]
    // 0xb8318c: r1 = Null
    //     0xb8318c: mov             x1, NULL
    // 0xb83190: r2 = 4
    //     0xb83190: movz            x2, #0x4
    // 0xb83194: r0 = AllocateArray()
    //     0xb83194: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb83198: mov             x1, x0
    // 0xb8319c: ldur            x0, [fp, #-0x40]
    // 0xb831a0: StoreField: r1->field_f = r0
    //     0xb831a0: stur            w0, [x1, #0xf]
    // 0xb831a4: r16 = " "
    //     0xb831a4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb831a8: StoreField: r1->field_13 = r16
    //     0xb831a8: stur            w16, [x1, #0x13]
    // 0xb831ac: str             x1, [SP]
    // 0xb831b0: r0 = _interpolate()
    //     0xb831b0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb831b4: ldr             x1, [fp, #0x18]
    // 0xb831b8: stur            x0, [fp, #-0x18]
    // 0xb831bc: r0 = of()
    //     0xb831bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb831c0: LoadField: r1 = r0->field_87
    //     0xb831c0: ldur            w1, [x0, #0x87]
    // 0xb831c4: DecompressPointer r1
    //     0xb831c4: add             x1, x1, HEAP, lsl #32
    // 0xb831c8: LoadField: r0 = r1->field_2b
    //     0xb831c8: ldur            w0, [x1, #0x2b]
    // 0xb831cc: DecompressPointer r0
    //     0xb831cc: add             x0, x0, HEAP, lsl #32
    // 0xb831d0: stur            x0, [fp, #-0x40]
    // 0xb831d4: r1 = Instance_Color
    //     0xb831d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb831d8: d0 = 0.250000
    //     0xb831d8: fmov            d0, #0.25000000
    // 0xb831dc: r0 = withOpacity()
    //     0xb831dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb831e0: r16 = Instance_TextDecoration
    //     0xb831e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb831e4: ldr             x16, [x16, #0xe30]
    // 0xb831e8: r30 = 12.000000
    //     0xb831e8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb831ec: ldr             lr, [lr, #0x9e8]
    // 0xb831f0: stp             lr, x16, [SP, #8]
    // 0xb831f4: str             x0, [SP]
    // 0xb831f8: ldur            x1, [fp, #-0x40]
    // 0xb831fc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb831fc: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb83200: ldr             x4, [x4, #0xb60]
    // 0xb83204: r0 = copyWith()
    //     0xb83204: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb83208: stur            x0, [fp, #-0x40]
    // 0xb8320c: r0 = TextSpan()
    //     0xb8320c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb83210: mov             x3, x0
    // 0xb83214: ldur            x0, [fp, #-0x18]
    // 0xb83218: stur            x3, [fp, #-0x58]
    // 0xb8321c: StoreField: r3->field_b = r0
    //     0xb8321c: stur            w0, [x3, #0xb]
    // 0xb83220: r0 = Instance__DeferringMouseCursor
    //     0xb83220: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb83224: ArrayStore: r3[0] = r0  ; List_4
    //     0xb83224: stur            w0, [x3, #0x17]
    // 0xb83228: ldur            x1, [fp, #-0x40]
    // 0xb8322c: StoreField: r3->field_7 = r1
    //     0xb8322c: stur            w1, [x3, #7]
    // 0xb83230: r1 = Null
    //     0xb83230: mov             x1, NULL
    // 0xb83234: r2 = 4
    //     0xb83234: movz            x2, #0x4
    // 0xb83238: r0 = AllocateArray()
    //     0xb83238: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb8323c: mov             x2, x0
    // 0xb83240: ldur            x0, [fp, #-0x48]
    // 0xb83244: stur            x2, [fp, #-0x18]
    // 0xb83248: StoreField: r2->field_f = r0
    //     0xb83248: stur            w0, [x2, #0xf]
    // 0xb8324c: ldur            x0, [fp, #-0x58]
    // 0xb83250: StoreField: r2->field_13 = r0
    //     0xb83250: stur            w0, [x2, #0x13]
    // 0xb83254: r1 = <InlineSpan>
    //     0xb83254: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb83258: ldr             x1, [x1, #0xe40]
    // 0xb8325c: r0 = AllocateGrowableArray()
    //     0xb8325c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb83260: mov             x1, x0
    // 0xb83264: ldur            x0, [fp, #-0x18]
    // 0xb83268: stur            x1, [fp, #-0x40]
    // 0xb8326c: StoreField: r1->field_f = r0
    //     0xb8326c: stur            w0, [x1, #0xf]
    // 0xb83270: r2 = 4
    //     0xb83270: movz            x2, #0x4
    // 0xb83274: StoreField: r1->field_b = r2
    //     0xb83274: stur            w2, [x1, #0xb]
    // 0xb83278: r0 = TextSpan()
    //     0xb83278: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb8327c: mov             x1, x0
    // 0xb83280: ldur            x0, [fp, #-0x40]
    // 0xb83284: stur            x1, [fp, #-0x18]
    // 0xb83288: StoreField: r1->field_f = r0
    //     0xb83288: stur            w0, [x1, #0xf]
    // 0xb8328c: r0 = Instance__DeferringMouseCursor
    //     0xb8328c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb83290: ArrayStore: r1[0] = r0  ; List_4
    //     0xb83290: stur            w0, [x1, #0x17]
    // 0xb83294: r0 = RichText()
    //     0xb83294: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb83298: mov             x1, x0
    // 0xb8329c: ldur            x2, [fp, #-0x18]
    // 0xb832a0: stur            x0, [fp, #-0x18]
    // 0xb832a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb832a4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb832a8: r0 = RichText()
    //     0xb832a8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb832ac: r0 = Padding()
    //     0xb832ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb832b0: mov             x3, x0
    // 0xb832b4: r0 = Instance_EdgeInsets
    //     0xb832b4: add             x0, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb832b8: ldr             x0, [x0, #0xc98]
    // 0xb832bc: stur            x3, [fp, #-0x40]
    // 0xb832c0: StoreField: r3->field_f = r0
    //     0xb832c0: stur            w0, [x3, #0xf]
    // 0xb832c4: ldur            x0, [fp, #-0x18]
    // 0xb832c8: StoreField: r3->field_b = r0
    //     0xb832c8: stur            w0, [x3, #0xb]
    // 0xb832cc: r1 = Null
    //     0xb832cc: mov             x1, NULL
    // 0xb832d0: r2 = 6
    //     0xb832d0: movz            x2, #0x6
    // 0xb832d4: r0 = AllocateArray()
    //     0xb832d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb832d8: mov             x2, x0
    // 0xb832dc: ldur            x0, [fp, #-0x20]
    // 0xb832e0: stur            x2, [fp, #-0x18]
    // 0xb832e4: StoreField: r2->field_f = r0
    //     0xb832e4: stur            w0, [x2, #0xf]
    // 0xb832e8: ldur            x0, [fp, #-0x38]
    // 0xb832ec: StoreField: r2->field_13 = r0
    //     0xb832ec: stur            w0, [x2, #0x13]
    // 0xb832f0: ldur            x0, [fp, #-0x40]
    // 0xb832f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb832f4: stur            w0, [x2, #0x17]
    // 0xb832f8: r1 = <Widget>
    //     0xb832f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb832fc: r0 = AllocateGrowableArray()
    //     0xb832fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb83300: mov             x1, x0
    // 0xb83304: ldur            x0, [fp, #-0x18]
    // 0xb83308: stur            x1, [fp, #-0x20]
    // 0xb8330c: StoreField: r1->field_f = r0
    //     0xb8330c: stur            w0, [x1, #0xf]
    // 0xb83310: r0 = 6
    //     0xb83310: movz            x0, #0x6
    // 0xb83314: StoreField: r1->field_b = r0
    //     0xb83314: stur            w0, [x1, #0xb]
    // 0xb83318: r0 = Column()
    //     0xb83318: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb8331c: mov             x2, x0
    // 0xb83320: r0 = Instance_Axis
    //     0xb83320: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb83324: stur            x2, [fp, #-0x18]
    // 0xb83328: StoreField: r2->field_f = r0
    //     0xb83328: stur            w0, [x2, #0xf]
    // 0xb8332c: r3 = Instance_MainAxisAlignment
    //     0xb8332c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb83330: ldr             x3, [x3, #0xa08]
    // 0xb83334: StoreField: r2->field_13 = r3
    //     0xb83334: stur            w3, [x2, #0x13]
    // 0xb83338: r4 = Instance_MainAxisSize
    //     0xb83338: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb8333c: ldr             x4, [x4, #0xa10]
    // 0xb83340: ArrayStore: r2[0] = r4  ; List_4
    //     0xb83340: stur            w4, [x2, #0x17]
    // 0xb83344: r5 = Instance_CrossAxisAlignment
    //     0xb83344: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb83348: ldr             x5, [x5, #0x890]
    // 0xb8334c: StoreField: r2->field_1b = r5
    //     0xb8334c: stur            w5, [x2, #0x1b]
    // 0xb83350: r6 = Instance_VerticalDirection
    //     0xb83350: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb83354: ldr             x6, [x6, #0xa20]
    // 0xb83358: StoreField: r2->field_23 = r6
    //     0xb83358: stur            w6, [x2, #0x23]
    // 0xb8335c: r7 = Instance_Clip
    //     0xb8335c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb83360: ldr             x7, [x7, #0x38]
    // 0xb83364: StoreField: r2->field_2b = r7
    //     0xb83364: stur            w7, [x2, #0x2b]
    // 0xb83368: StoreField: r2->field_2f = rZR
    //     0xb83368: stur            xzr, [x2, #0x2f]
    // 0xb8336c: ldur            x1, [fp, #-0x20]
    // 0xb83370: StoreField: r2->field_b = r1
    //     0xb83370: stur            w1, [x2, #0xb]
    // 0xb83374: r1 = <FlexParentData>
    //     0xb83374: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb83378: ldr             x1, [x1, #0xe00]
    // 0xb8337c: r0 = Expanded()
    //     0xb8337c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb83380: mov             x3, x0
    // 0xb83384: r0 = 1
    //     0xb83384: movz            x0, #0x1
    // 0xb83388: stur            x3, [fp, #-0x20]
    // 0xb8338c: StoreField: r3->field_13 = r0
    //     0xb8338c: stur            x0, [x3, #0x13]
    // 0xb83390: r0 = Instance_FlexFit
    //     0xb83390: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb83394: ldr             x0, [x0, #0xe08]
    // 0xb83398: StoreField: r3->field_1b = r0
    //     0xb83398: stur            w0, [x3, #0x1b]
    // 0xb8339c: ldur            x0, [fp, #-0x18]
    // 0xb833a0: StoreField: r3->field_b = r0
    //     0xb833a0: stur            w0, [x3, #0xb]
    // 0xb833a4: ldur            x0, [fp, #-8]
    // 0xb833a8: LoadField: r1 = r0->field_f
    //     0xb833a8: ldur            w1, [x0, #0xf]
    // 0xb833ac: DecompressPointer r1
    //     0xb833ac: add             x1, x1, HEAP, lsl #32
    // 0xb833b0: ldur            x2, [fp, #-0x50]
    // 0xb833b4: r0 = _buildAddToBagButton()
    //     0xb833b4: bl              #0xb83544  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildAddToBagButton
    // 0xb833b8: r1 = Null
    //     0xb833b8: mov             x1, NULL
    // 0xb833bc: r2 = 4
    //     0xb833bc: movz            x2, #0x4
    // 0xb833c0: stur            x0, [fp, #-8]
    // 0xb833c4: r0 = AllocateArray()
    //     0xb833c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb833c8: mov             x2, x0
    // 0xb833cc: ldur            x0, [fp, #-0x20]
    // 0xb833d0: stur            x2, [fp, #-0x18]
    // 0xb833d4: StoreField: r2->field_f = r0
    //     0xb833d4: stur            w0, [x2, #0xf]
    // 0xb833d8: ldur            x0, [fp, #-8]
    // 0xb833dc: StoreField: r2->field_13 = r0
    //     0xb833dc: stur            w0, [x2, #0x13]
    // 0xb833e0: r1 = <Widget>
    //     0xb833e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb833e4: r0 = AllocateGrowableArray()
    //     0xb833e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb833e8: mov             x1, x0
    // 0xb833ec: ldur            x0, [fp, #-0x18]
    // 0xb833f0: stur            x1, [fp, #-8]
    // 0xb833f4: StoreField: r1->field_f = r0
    //     0xb833f4: stur            w0, [x1, #0xf]
    // 0xb833f8: r2 = 4
    //     0xb833f8: movz            x2, #0x4
    // 0xb833fc: StoreField: r1->field_b = r2
    //     0xb833fc: stur            w2, [x1, #0xb]
    // 0xb83400: r0 = Row()
    //     0xb83400: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb83404: mov             x3, x0
    // 0xb83408: r0 = Instance_Axis
    //     0xb83408: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb8340c: stur            x3, [fp, #-0x18]
    // 0xb83410: StoreField: r3->field_f = r0
    //     0xb83410: stur            w0, [x3, #0xf]
    // 0xb83414: r0 = Instance_MainAxisAlignment
    //     0xb83414: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb83418: ldr             x0, [x0, #0xa8]
    // 0xb8341c: StoreField: r3->field_13 = r0
    //     0xb8341c: stur            w0, [x3, #0x13]
    // 0xb83420: r0 = Instance_MainAxisSize
    //     0xb83420: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb83424: ldr             x0, [x0, #0xa10]
    // 0xb83428: ArrayStore: r3[0] = r0  ; List_4
    //     0xb83428: stur            w0, [x3, #0x17]
    // 0xb8342c: r0 = Instance_CrossAxisAlignment
    //     0xb8342c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb83430: ldr             x0, [x0, #0xa18]
    // 0xb83434: StoreField: r3->field_1b = r0
    //     0xb83434: stur            w0, [x3, #0x1b]
    // 0xb83438: r0 = Instance_VerticalDirection
    //     0xb83438: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8343c: ldr             x0, [x0, #0xa20]
    // 0xb83440: StoreField: r3->field_23 = r0
    //     0xb83440: stur            w0, [x3, #0x23]
    // 0xb83444: r4 = Instance_Clip
    //     0xb83444: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb83448: ldr             x4, [x4, #0x38]
    // 0xb8344c: StoreField: r3->field_2b = r4
    //     0xb8344c: stur            w4, [x3, #0x2b]
    // 0xb83450: StoreField: r3->field_2f = rZR
    //     0xb83450: stur            xzr, [x3, #0x2f]
    // 0xb83454: ldur            x1, [fp, #-8]
    // 0xb83458: StoreField: r3->field_b = r1
    //     0xb83458: stur            w1, [x3, #0xb]
    // 0xb8345c: r1 = Null
    //     0xb8345c: mov             x1, NULL
    // 0xb83460: r2 = 4
    //     0xb83460: movz            x2, #0x4
    // 0xb83464: r0 = AllocateArray()
    //     0xb83464: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb83468: mov             x2, x0
    // 0xb8346c: ldur            x0, [fp, #-0x30]
    // 0xb83470: stur            x2, [fp, #-8]
    // 0xb83474: StoreField: r2->field_f = r0
    //     0xb83474: stur            w0, [x2, #0xf]
    // 0xb83478: ldur            x0, [fp, #-0x18]
    // 0xb8347c: StoreField: r2->field_13 = r0
    //     0xb8347c: stur            w0, [x2, #0x13]
    // 0xb83480: r1 = <Widget>
    //     0xb83480: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb83484: r0 = AllocateGrowableArray()
    //     0xb83484: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb83488: mov             x1, x0
    // 0xb8348c: ldur            x0, [fp, #-8]
    // 0xb83490: stur            x1, [fp, #-0x18]
    // 0xb83494: StoreField: r1->field_f = r0
    //     0xb83494: stur            w0, [x1, #0xf]
    // 0xb83498: r0 = 4
    //     0xb83498: movz            x0, #0x4
    // 0xb8349c: StoreField: r1->field_b = r0
    //     0xb8349c: stur            w0, [x1, #0xb]
    // 0xb834a0: r0 = Column()
    //     0xb834a0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb834a4: mov             x3, x0
    // 0xb834a8: r0 = Instance_Axis
    //     0xb834a8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb834ac: stur            x3, [fp, #-8]
    // 0xb834b0: StoreField: r3->field_f = r0
    //     0xb834b0: stur            w0, [x3, #0xf]
    // 0xb834b4: r0 = Instance_MainAxisAlignment
    //     0xb834b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb834b8: ldr             x0, [x0, #0xa08]
    // 0xb834bc: StoreField: r3->field_13 = r0
    //     0xb834bc: stur            w0, [x3, #0x13]
    // 0xb834c0: r0 = Instance_MainAxisSize
    //     0xb834c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb834c4: ldr             x0, [x0, #0xdd0]
    // 0xb834c8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb834c8: stur            w0, [x3, #0x17]
    // 0xb834cc: r0 = Instance_CrossAxisAlignment
    //     0xb834cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb834d0: ldr             x0, [x0, #0x890]
    // 0xb834d4: StoreField: r3->field_1b = r0
    //     0xb834d4: stur            w0, [x3, #0x1b]
    // 0xb834d8: r0 = Instance_VerticalDirection
    //     0xb834d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb834dc: ldr             x0, [x0, #0xa20]
    // 0xb834e0: StoreField: r3->field_23 = r0
    //     0xb834e0: stur            w0, [x3, #0x23]
    // 0xb834e4: r0 = Instance_Clip
    //     0xb834e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb834e8: ldr             x0, [x0, #0x38]
    // 0xb834ec: StoreField: r3->field_2b = r0
    //     0xb834ec: stur            w0, [x3, #0x2b]
    // 0xb834f0: StoreField: r3->field_2f = rZR
    //     0xb834f0: stur            xzr, [x3, #0x2f]
    // 0xb834f4: ldur            x0, [fp, #-0x18]
    // 0xb834f8: StoreField: r3->field_b = r0
    //     0xb834f8: stur            w0, [x3, #0xb]
    // 0xb834fc: ldur            x2, [fp, #-0x10]
    // 0xb83500: r1 = Function '<anonymous closure>':.
    //     0xb83500: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d90] AnonymousClosure: (0xb84288), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::build (0xb82b68)
    //     0xb83504: ldr             x1, [x1, #0xd90]
    // 0xb83508: r0 = AllocateClosure()
    //     0xb83508: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8350c: stur            x0, [fp, #-0x10]
    // 0xb83510: r0 = VisibilityDetector()
    //     0xb83510: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xb83514: ldur            x1, [fp, #-0x10]
    // 0xb83518: StoreField: r0->field_f = r1
    //     0xb83518: stur            w1, [x0, #0xf]
    // 0xb8351c: ldur            x1, [fp, #-8]
    // 0xb83520: StoreField: r0->field_b = r1
    //     0xb83520: stur            w1, [x0, #0xb]
    // 0xb83524: ldur            x1, [fp, #-0x28]
    // 0xb83528: StoreField: r0->field_7 = r1
    //     0xb83528: stur            w1, [x0, #7]
    // 0xb8352c: LeaveFrame
    //     0xb8352c: mov             SP, fp
    //     0xb83530: ldp             fp, lr, [SP], #0x10
    // 0xb83534: ret
    //     0xb83534: ret             
    // 0xb83538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb83538: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8353c: b               #0xb82d40
    // 0xb83540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb83540: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagButton(/* No info */) {
    // ** addr: 0xb83544, size: 0x368
    // 0xb83544: EnterFrame
    //     0xb83544: stp             fp, lr, [SP, #-0x10]!
    //     0xb83548: mov             fp, SP
    // 0xb8354c: AllocStack(0x50)
    //     0xb8354c: sub             SP, SP, #0x50
    // 0xb83550: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb83550: stur            x1, [fp, #-8]
    //     0xb83554: stur            x2, [fp, #-0x10]
    // 0xb83558: CheckStackOverflow
    //     0xb83558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8355c: cmp             SP, x16
    //     0xb83560: b.ls            #0xb83898
    // 0xb83564: r1 = 2
    //     0xb83564: movz            x1, #0x2
    // 0xb83568: r0 = AllocateContext()
    //     0xb83568: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8356c: mov             x3, x0
    // 0xb83570: ldur            x2, [fp, #-8]
    // 0xb83574: stur            x3, [fp, #-0x18]
    // 0xb83578: StoreField: r3->field_f = r2
    //     0xb83578: stur            w2, [x3, #0xf]
    // 0xb8357c: LoadField: r0 = r2->field_b
    //     0xb8357c: ldur            w0, [x2, #0xb]
    // 0xb83580: DecompressPointer r0
    //     0xb83580: add             x0, x0, HEAP, lsl #32
    // 0xb83584: cmp             w0, NULL
    // 0xb83588: b.eq            #0xb838a0
    // 0xb8358c: LoadField: r4 = r0->field_b
    //     0xb8358c: ldur            w4, [x0, #0xb]
    // 0xb83590: DecompressPointer r4
    //     0xb83590: add             x4, x4, HEAP, lsl #32
    // 0xb83594: ldur            x5, [fp, #-0x10]
    // 0xb83598: r0 = BoxInt64Instr(r5)
    //     0xb83598: sbfiz           x0, x5, #1, #0x1f
    //     0xb8359c: cmp             x5, x0, asr #1
    //     0xb835a0: b.eq            #0xb835ac
    //     0xb835a4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb835a8: stur            x5, [x0, #7]
    // 0xb835ac: r1 = LoadClassIdInstr(r4)
    //     0xb835ac: ldur            x1, [x4, #-1]
    //     0xb835b0: ubfx            x1, x1, #0xc, #0x14
    // 0xb835b4: stp             x0, x4, [SP]
    // 0xb835b8: mov             x0, x1
    // 0xb835bc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb835bc: sub             lr, x0, #0xb7
    //     0xb835c0: ldr             lr, [x21, lr, lsl #3]
    //     0xb835c4: blr             lr
    // 0xb835c8: mov             x3, x0
    // 0xb835cc: ldur            x2, [fp, #-0x18]
    // 0xb835d0: stur            x3, [fp, #-0x28]
    // 0xb835d4: StoreField: r2->field_13 = r0
    //     0xb835d4: stur            w0, [x2, #0x13]
    //     0xb835d8: ldurb           w16, [x2, #-1]
    //     0xb835dc: ldurb           w17, [x0, #-1]
    //     0xb835e0: and             x16, x17, x16, lsr #2
    //     0xb835e4: tst             x16, HEAP, lsr #32
    //     0xb835e8: b.eq            #0xb835f0
    //     0xb835ec: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb835f0: cmp             w3, NULL
    // 0xb835f4: b.ne            #0xb83608
    // 0xb835f8: r0 = Instance_SizedBox
    //     0xb835f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb835fc: LeaveFrame
    //     0xb835fc: mov             SP, fp
    //     0xb83600: ldp             fp, lr, [SP], #0x10
    // 0xb83604: ret
    //     0xb83604: ret             
    // 0xb83608: ldur            x0, [fp, #-8]
    // 0xb8360c: LoadField: r1 = r0->field_b
    //     0xb8360c: ldur            w1, [x0, #0xb]
    // 0xb83610: DecompressPointer r1
    //     0xb83610: add             x1, x1, HEAP, lsl #32
    // 0xb83614: cmp             w1, NULL
    // 0xb83618: b.eq            #0xb838a4
    // 0xb8361c: LoadField: r4 = r1->field_f
    //     0xb8361c: ldur            w4, [x1, #0xf]
    // 0xb83620: DecompressPointer r4
    //     0xb83620: add             x4, x4, HEAP, lsl #32
    // 0xb83624: LoadField: r1 = r4->field_1f
    //     0xb83624: ldur            w1, [x4, #0x1f]
    // 0xb83628: DecompressPointer r1
    //     0xb83628: add             x1, x1, HEAP, lsl #32
    // 0xb8362c: cmp             w1, NULL
    // 0xb83630: b.ne            #0xb8363c
    // 0xb83634: r1 = Null
    //     0xb83634: mov             x1, NULL
    // 0xb83638: b               #0xb83648
    // 0xb8363c: LoadField: r5 = r1->field_7
    //     0xb8363c: ldur            w5, [x1, #7]
    // 0xb83640: DecompressPointer r5
    //     0xb83640: add             x5, x5, HEAP, lsl #32
    // 0xb83644: mov             x1, x5
    // 0xb83648: cmp             w1, NULL
    // 0xb8364c: b.ne            #0xb83658
    // 0xb83650: r5 = false
    //     0xb83650: add             x5, NULL, #0x30  ; false
    // 0xb83654: b               #0xb8365c
    // 0xb83658: mov             x5, x1
    // 0xb8365c: stur            x5, [fp, #-0x20]
    // 0xb83660: LoadField: r1 = r4->field_3f
    //     0xb83660: ldur            w1, [x4, #0x3f]
    // 0xb83664: DecompressPointer r1
    //     0xb83664: add             x1, x1, HEAP, lsl #32
    // 0xb83668: cmp             w1, NULL
    // 0xb8366c: b.ne            #0xb83678
    // 0xb83670: r1 = Null
    //     0xb83670: mov             x1, NULL
    // 0xb83674: b               #0xb83684
    // 0xb83678: LoadField: r4 = r1->field_23
    //     0xb83678: ldur            w4, [x1, #0x23]
    // 0xb8367c: DecompressPointer r4
    //     0xb8367c: add             x4, x4, HEAP, lsl #32
    // 0xb83680: mov             x1, x4
    // 0xb83684: cmp             w1, NULL
    // 0xb83688: b.eq            #0xb83834
    // 0xb8368c: tbnz            w1, #4, #0xb83834
    // 0xb83690: r17 = 275
    //     0xb83690: movz            x17, #0x113
    // 0xb83694: ldr             w1, [x3, x17]
    // 0xb83698: DecompressPointer r1
    //     0xb83698: add             x1, x1, HEAP, lsl #32
    // 0xb8369c: cmp             w1, NULL
    // 0xb836a0: b.eq            #0xb836a8
    // 0xb836a4: tbnz            w1, #4, #0xb836b8
    // 0xb836a8: mov             x0, x3
    // 0xb836ac: r1 = Instance_Color
    //     0xb836ac: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb836b0: ldr             x1, [x1, #0xf88]
    // 0xb836b4: b               #0xb836d8
    // 0xb836b8: LoadField: r1 = r0->field_f
    //     0xb836b8: ldur            w1, [x0, #0xf]
    // 0xb836bc: DecompressPointer r1
    //     0xb836bc: add             x1, x1, HEAP, lsl #32
    // 0xb836c0: cmp             w1, NULL
    // 0xb836c4: b.eq            #0xb838a8
    // 0xb836c8: r0 = of()
    //     0xb836c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb836cc: LoadField: r1 = r0->field_5b
    //     0xb836cc: ldur            w1, [x0, #0x5b]
    // 0xb836d0: DecompressPointer r1
    //     0xb836d0: add             x1, x1, HEAP, lsl #32
    // 0xb836d4: ldur            x0, [fp, #-0x28]
    // 0xb836d8: stur            x1, [fp, #-8]
    // 0xb836dc: r0 = Radius()
    //     0xb836dc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb836e0: d0 = 100.000000
    //     0xb836e0: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb836e4: stur            x0, [fp, #-0x30]
    // 0xb836e8: StoreField: r0->field_7 = d0
    //     0xb836e8: stur            d0, [x0, #7]
    // 0xb836ec: StoreField: r0->field_f = d0
    //     0xb836ec: stur            d0, [x0, #0xf]
    // 0xb836f0: r0 = BorderRadius()
    //     0xb836f0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb836f4: mov             x1, x0
    // 0xb836f8: ldur            x0, [fp, #-0x30]
    // 0xb836fc: stur            x1, [fp, #-0x38]
    // 0xb83700: StoreField: r1->field_7 = r0
    //     0xb83700: stur            w0, [x1, #7]
    // 0xb83704: StoreField: r1->field_b = r0
    //     0xb83704: stur            w0, [x1, #0xb]
    // 0xb83708: StoreField: r1->field_f = r0
    //     0xb83708: stur            w0, [x1, #0xf]
    // 0xb8370c: StoreField: r1->field_13 = r0
    //     0xb8370c: stur            w0, [x1, #0x13]
    // 0xb83710: r0 = BoxDecoration()
    //     0xb83710: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb83714: mov             x1, x0
    // 0xb83718: ldur            x0, [fp, #-8]
    // 0xb8371c: stur            x1, [fp, #-0x30]
    // 0xb83720: StoreField: r1->field_7 = r0
    //     0xb83720: stur            w0, [x1, #7]
    // 0xb83724: ldur            x0, [fp, #-0x38]
    // 0xb83728: StoreField: r1->field_13 = r0
    //     0xb83728: stur            w0, [x1, #0x13]
    // 0xb8372c: r0 = Instance_BoxShape
    //     0xb8372c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb83730: ldr             x0, [x0, #0x80]
    // 0xb83734: StoreField: r1->field_23 = r0
    //     0xb83734: stur            w0, [x1, #0x23]
    // 0xb83738: ldur            x2, [fp, #-0x28]
    // 0xb8373c: r17 = 275
    //     0xb8373c: movz            x17, #0x113
    // 0xb83740: ldr             w3, [x2, x17]
    // 0xb83744: DecompressPointer r3
    //     0xb83744: add             x3, x3, HEAP, lsl #32
    // 0xb83748: cmp             w3, NULL
    // 0xb8374c: b.eq            #0xb83754
    // 0xb83750: tbnz            w3, #4, #0xb83770
    // 0xb83754: r0 = Container()
    //     0xb83754: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb83758: mov             x1, x0
    // 0xb8375c: stur            x0, [fp, #-8]
    // 0xb83760: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb83760: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb83764: r0 = Container()
    //     0xb83764: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb83768: ldur            x0, [fp, #-8]
    // 0xb8376c: b               #0xb837a0
    // 0xb83770: r0 = SvgPicture()
    //     0xb83770: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb83774: stur            x0, [fp, #-8]
    // 0xb83778: r16 = Instance_BoxFit
    //     0xb83778: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb8377c: ldr             x16, [x16, #0xb18]
    // 0xb83780: str             x16, [SP]
    // 0xb83784: mov             x1, x0
    // 0xb83788: r2 = "assets/images/add_to_bag_glass.svg"
    //     0xb83788: add             x2, PP, #0x55, lsl #12  ; [pp+0x55cf0] "assets/images/add_to_bag_glass.svg"
    //     0xb8378c: ldr             x2, [x2, #0xcf0]
    // 0xb83790: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb83790: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb83794: ldr             x4, [x4, #0xb0]
    // 0xb83798: r0 = SvgPicture.asset()
    //     0xb83798: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb8379c: ldur            x0, [fp, #-8]
    // 0xb837a0: stur            x0, [fp, #-8]
    // 0xb837a4: r0 = InkWell()
    //     0xb837a4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb837a8: mov             x3, x0
    // 0xb837ac: ldur            x0, [fp, #-8]
    // 0xb837b0: stur            x3, [fp, #-0x28]
    // 0xb837b4: StoreField: r3->field_b = r0
    //     0xb837b4: stur            w0, [x3, #0xb]
    // 0xb837b8: ldur            x2, [fp, #-0x18]
    // 0xb837bc: r1 = Function '<anonymous closure>':.
    //     0xb837bc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61de0] AnonymousClosure: (0xb838ac), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildAddToBagButton (0xb83544)
    //     0xb837c0: ldr             x1, [x1, #0xde0]
    // 0xb837c4: r0 = AllocateClosure()
    //     0xb837c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb837c8: mov             x1, x0
    // 0xb837cc: ldur            x0, [fp, #-0x28]
    // 0xb837d0: StoreField: r0->field_f = r1
    //     0xb837d0: stur            w1, [x0, #0xf]
    // 0xb837d4: r1 = true
    //     0xb837d4: add             x1, NULL, #0x20  ; true
    // 0xb837d8: StoreField: r0->field_43 = r1
    //     0xb837d8: stur            w1, [x0, #0x43]
    // 0xb837dc: r2 = Instance_BoxShape
    //     0xb837dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb837e0: ldr             x2, [x2, #0x80]
    // 0xb837e4: StoreField: r0->field_47 = r2
    //     0xb837e4: stur            w2, [x0, #0x47]
    // 0xb837e8: StoreField: r0->field_6f = r1
    //     0xb837e8: stur            w1, [x0, #0x6f]
    // 0xb837ec: r2 = false
    //     0xb837ec: add             x2, NULL, #0x30  ; false
    // 0xb837f0: StoreField: r0->field_73 = r2
    //     0xb837f0: stur            w2, [x0, #0x73]
    // 0xb837f4: StoreField: r0->field_83 = r1
    //     0xb837f4: stur            w1, [x0, #0x83]
    // 0xb837f8: StoreField: r0->field_7b = r2
    //     0xb837f8: stur            w2, [x0, #0x7b]
    // 0xb837fc: r0 = Container()
    //     0xb837fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb83800: stur            x0, [fp, #-8]
    // 0xb83804: ldur            x16, [fp, #-0x30]
    // 0xb83808: r30 = Instance_EdgeInsets
    //     0xb83808: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb8380c: ldr             lr, [lr, #0x980]
    // 0xb83810: stp             lr, x16, [SP, #8]
    // 0xb83814: ldur            x16, [fp, #-0x28]
    // 0xb83818: str             x16, [SP]
    // 0xb8381c: mov             x1, x0
    // 0xb83820: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb83820: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb83824: ldr             x4, [x4, #0xb40]
    // 0xb83828: r0 = Container()
    //     0xb83828: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8382c: ldur            x1, [fp, #-8]
    // 0xb83830: b               #0xb8384c
    // 0xb83834: r0 = Container()
    //     0xb83834: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb83838: mov             x1, x0
    // 0xb8383c: stur            x0, [fp, #-8]
    // 0xb83840: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb83840: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb83844: r0 = Container()
    //     0xb83844: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb83848: ldur            x1, [fp, #-8]
    // 0xb8384c: ldur            x0, [fp, #-0x20]
    // 0xb83850: stur            x1, [fp, #-8]
    // 0xb83854: r0 = Visibility()
    //     0xb83854: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb83858: ldur            x1, [fp, #-8]
    // 0xb8385c: StoreField: r0->field_b = r1
    //     0xb8385c: stur            w1, [x0, #0xb]
    // 0xb83860: r1 = Instance_SizedBox
    //     0xb83860: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb83864: StoreField: r0->field_f = r1
    //     0xb83864: stur            w1, [x0, #0xf]
    // 0xb83868: ldur            x1, [fp, #-0x20]
    // 0xb8386c: StoreField: r0->field_13 = r1
    //     0xb8386c: stur            w1, [x0, #0x13]
    // 0xb83870: r1 = false
    //     0xb83870: add             x1, NULL, #0x30  ; false
    // 0xb83874: ArrayStore: r0[0] = r1  ; List_4
    //     0xb83874: stur            w1, [x0, #0x17]
    // 0xb83878: StoreField: r0->field_1b = r1
    //     0xb83878: stur            w1, [x0, #0x1b]
    // 0xb8387c: StoreField: r0->field_1f = r1
    //     0xb8387c: stur            w1, [x0, #0x1f]
    // 0xb83880: StoreField: r0->field_23 = r1
    //     0xb83880: stur            w1, [x0, #0x23]
    // 0xb83884: StoreField: r0->field_27 = r1
    //     0xb83884: stur            w1, [x0, #0x27]
    // 0xb83888: StoreField: r0->field_2b = r1
    //     0xb83888: stur            w1, [x0, #0x2b]
    // 0xb8388c: LeaveFrame
    //     0xb8388c: mov             SP, fp
    //     0xb83890: ldp             fp, lr, [SP], #0x10
    // 0xb83894: ret
    //     0xb83894: ret             
    // 0xb83898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb83898: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8389c: b               #0xb83564
    // 0xb838a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb838a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb838a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb838a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb838a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb838a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb838ac, size: 0xf8
    // 0xb838ac: EnterFrame
    //     0xb838ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb838b0: mov             fp, SP
    // 0xb838b4: AllocStack(0x20)
    //     0xb838b4: sub             SP, SP, #0x20
    // 0xb838b8: SetupParameters()
    //     0xb838b8: ldr             x0, [fp, #0x10]
    //     0xb838bc: ldur            w1, [x0, #0x17]
    //     0xb838c0: add             x1, x1, HEAP, lsl #32
    //     0xb838c4: stur            x1, [fp, #-0x10]
    // 0xb838c8: CheckStackOverflow
    //     0xb838c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb838cc: cmp             SP, x16
    //     0xb838d0: b.ls            #0xb83990
    // 0xb838d4: LoadField: r0 = r1->field_13
    //     0xb838d4: ldur            w0, [x1, #0x13]
    // 0xb838d8: DecompressPointer r0
    //     0xb838d8: add             x0, x0, HEAP, lsl #32
    // 0xb838dc: stur            x0, [fp, #-8]
    // 0xb838e0: cmp             w0, NULL
    // 0xb838e4: b.eq            #0xb83998
    // 0xb838e8: r17 = 275
    //     0xb838e8: movz            x17, #0x113
    // 0xb838ec: ldr             w2, [x0, x17]
    // 0xb838f0: DecompressPointer r2
    //     0xb838f0: add             x2, x2, HEAP, lsl #32
    // 0xb838f4: cmp             w2, NULL
    // 0xb838f8: b.eq            #0xb83900
    // 0xb838fc: tbz             w2, #4, #0xb83980
    // 0xb83900: LoadField: r2 = r1->field_f
    //     0xb83900: ldur            w2, [x1, #0xf]
    // 0xb83904: DecompressPointer r2
    //     0xb83904: add             x2, x2, HEAP, lsl #32
    // 0xb83908: LoadField: r3 = r2->field_b
    //     0xb83908: ldur            w3, [x2, #0xb]
    // 0xb8390c: DecompressPointer r3
    //     0xb8390c: add             x3, x3, HEAP, lsl #32
    // 0xb83910: cmp             w3, NULL
    // 0xb83914: b.eq            #0xb8399c
    // 0xb83918: LoadField: r2 = r3->field_2b
    //     0xb83918: ldur            w2, [x3, #0x2b]
    // 0xb8391c: DecompressPointer r2
    //     0xb8391c: add             x2, x2, HEAP, lsl #32
    // 0xb83920: r16 = "add_to_bag"
    //     0xb83920: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb83924: ldr             x16, [x16, #0xa38]
    // 0xb83928: stp             x16, x2, [SP]
    // 0xb8392c: r4 = 0
    //     0xb8392c: movz            x4, #0
    // 0xb83930: ldr             x0, [SP, #8]
    // 0xb83934: r16 = UnlinkedCall_0x613b5c
    //     0xb83934: add             x16, PP, #0x61, lsl #12  ; [pp+0x61de8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb83938: add             x16, x16, #0xde8
    // 0xb8393c: ldp             x5, lr, [x16]
    // 0xb83940: blr             lr
    // 0xb83944: ldur            x0, [fp, #-0x10]
    // 0xb83948: LoadField: r1 = r0->field_f
    //     0xb83948: ldur            w1, [x0, #0xf]
    // 0xb8394c: DecompressPointer r1
    //     0xb8394c: add             x1, x1, HEAP, lsl #32
    // 0xb83950: LoadField: r0 = r1->field_b
    //     0xb83950: ldur            w0, [x1, #0xb]
    // 0xb83954: DecompressPointer r0
    //     0xb83954: add             x0, x0, HEAP, lsl #32
    // 0xb83958: cmp             w0, NULL
    // 0xb8395c: b.eq            #0xb839a0
    // 0xb83960: LoadField: r1 = r0->field_37
    //     0xb83960: ldur            w1, [x0, #0x37]
    // 0xb83964: DecompressPointer r1
    //     0xb83964: add             x1, x1, HEAP, lsl #32
    // 0xb83968: ldur            x16, [fp, #-8]
    // 0xb8396c: stp             x16, x1, [SP]
    // 0xb83970: mov             x0, x1
    // 0xb83974: ClosureCall
    //     0xb83974: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb83978: ldur            x2, [x0, #0x1f]
    //     0xb8397c: blr             x2
    // 0xb83980: r0 = Null
    //     0xb83980: mov             x0, NULL
    // 0xb83984: LeaveFrame
    //     0xb83984: mov             SP, fp
    //     0xb83988: ldp             fp, lr, [SP], #0x10
    // 0xb8398c: ret
    //     0xb8398c: ret             
    // 0xb83990: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb83990: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb83994: b               #0xb838d4
    // 0xb83998: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb83998: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb8399c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8399c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb839a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb839a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingSection(/* No info */) {
    // ** addr: 0xb839a4, size: 0x8e4
    // 0xb839a4: EnterFrame
    //     0xb839a4: stp             fp, lr, [SP, #-0x10]!
    //     0xb839a8: mov             fp, SP
    // 0xb839ac: AllocStack(0x50)
    //     0xb839ac: sub             SP, SP, #0x50
    // 0xb839b0: SetupParameters(_ProductGridItemState this /* r1 => r3, fp-0x8 */)
    //     0xb839b0: mov             x3, x1
    //     0xb839b4: stur            x1, [fp, #-8]
    // 0xb839b8: CheckStackOverflow
    //     0xb839b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb839bc: cmp             SP, x16
    //     0xb839c0: b.ls            #0xb8423c
    // 0xb839c4: LoadField: r0 = r3->field_b
    //     0xb839c4: ldur            w0, [x3, #0xb]
    // 0xb839c8: DecompressPointer r0
    //     0xb839c8: add             x0, x0, HEAP, lsl #32
    // 0xb839cc: cmp             w0, NULL
    // 0xb839d0: b.eq            #0xb84244
    // 0xb839d4: LoadField: r4 = r0->field_b
    //     0xb839d4: ldur            w4, [x0, #0xb]
    // 0xb839d8: DecompressPointer r4
    //     0xb839d8: add             x4, x4, HEAP, lsl #32
    // 0xb839dc: r0 = BoxInt64Instr(r2)
    //     0xb839dc: sbfiz           x0, x2, #1, #0x1f
    //     0xb839e0: cmp             x2, x0, asr #1
    //     0xb839e4: b.eq            #0xb839f0
    //     0xb839e8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb839ec: stur            x2, [x0, #7]
    // 0xb839f0: r1 = LoadClassIdInstr(r4)
    //     0xb839f0: ldur            x1, [x4, #-1]
    //     0xb839f4: ubfx            x1, x1, #0xc, #0x14
    // 0xb839f8: stp             x0, x4, [SP]
    // 0xb839fc: mov             x0, x1
    // 0xb83a00: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb83a00: sub             lr, x0, #0xb7
    //     0xb83a04: ldr             lr, [x21, lr, lsl #3]
    //     0xb83a08: blr             lr
    // 0xb83a0c: mov             x1, x0
    // 0xb83a10: stur            x1, [fp, #-0x10]
    // 0xb83a14: cmp             w1, NULL
    // 0xb83a18: b.ne            #0xb83a2c
    // 0xb83a1c: r0 = Instance_SizedBox
    //     0xb83a1c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb83a20: LeaveFrame
    //     0xb83a20: mov             SP, fp
    //     0xb83a24: ldp             fp, lr, [SP], #0x10
    // 0xb83a28: ret
    //     0xb83a28: ret             
    // 0xb83a2c: LoadField: r0 = r1->field_e3
    //     0xb83a2c: ldur            w0, [x1, #0xe3]
    // 0xb83a30: DecompressPointer r0
    //     0xb83a30: add             x0, x0, HEAP, lsl #32
    // 0xb83a34: cmp             w0, NULL
    // 0xb83a38: b.ne            #0xb83a44
    // 0xb83a3c: r0 = Null
    //     0xb83a3c: mov             x0, NULL
    // 0xb83a40: b               #0xb83a50
    // 0xb83a44: LoadField: r2 = r0->field_7
    //     0xb83a44: ldur            w2, [x0, #7]
    // 0xb83a48: DecompressPointer r2
    //     0xb83a48: add             x2, x2, HEAP, lsl #32
    // 0xb83a4c: mov             x0, x2
    // 0xb83a50: r2 = LoadClassIdInstr(r0)
    //     0xb83a50: ldur            x2, [x0, #-1]
    //     0xb83a54: ubfx            x2, x2, #0xc, #0x14
    // 0xb83a58: r16 = 0.000000
    //     0xb83a58: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb83a5c: stp             x16, x0, [SP]
    // 0xb83a60: mov             x0, x2
    // 0xb83a64: mov             lr, x0
    // 0xb83a68: ldr             lr, [x21, lr, lsl #3]
    // 0xb83a6c: blr             lr
    // 0xb83a70: eor             x1, x0, #0x10
    // 0xb83a74: tbz             w1, #4, #0xb83a88
    // 0xb83a78: r0 = Instance_SizedBox
    //     0xb83a78: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb83a7c: LeaveFrame
    //     0xb83a7c: mov             SP, fp
    //     0xb83a80: ldp             fp, lr, [SP], #0x10
    // 0xb83a84: ret
    //     0xb83a84: ret             
    // 0xb83a88: ldur            x1, [fp, #-0x10]
    // 0xb83a8c: LoadField: r0 = r1->field_e3
    //     0xb83a8c: ldur            w0, [x1, #0xe3]
    // 0xb83a90: DecompressPointer r0
    //     0xb83a90: add             x0, x0, HEAP, lsl #32
    // 0xb83a94: cmp             w0, NULL
    // 0xb83a98: b.ne            #0xb83aa4
    // 0xb83a9c: r2 = Null
    //     0xb83a9c: mov             x2, NULL
    // 0xb83aa0: b               #0xb83aac
    // 0xb83aa4: LoadField: r2 = r0->field_7
    //     0xb83aa4: ldur            w2, [x0, #7]
    // 0xb83aa8: DecompressPointer r2
    //     0xb83aa8: add             x2, x2, HEAP, lsl #32
    // 0xb83aac: cmp             w2, NULL
    // 0xb83ab0: r16 = true
    //     0xb83ab0: add             x16, NULL, #0x20  ; true
    // 0xb83ab4: r17 = false
    //     0xb83ab4: add             x17, NULL, #0x30  ; false
    // 0xb83ab8: csel            x3, x16, x17, ne
    // 0xb83abc: stur            x3, [fp, #-0x18]
    // 0xb83ac0: cmp             w0, NULL
    // 0xb83ac4: b.ne            #0xb83ad0
    // 0xb83ac8: r0 = Null
    //     0xb83ac8: mov             x0, NULL
    // 0xb83acc: b               #0xb83adc
    // 0xb83ad0: LoadField: r2 = r0->field_f
    //     0xb83ad0: ldur            w2, [x0, #0xf]
    // 0xb83ad4: DecompressPointer r2
    //     0xb83ad4: add             x2, x2, HEAP, lsl #32
    // 0xb83ad8: mov             x0, x2
    // 0xb83adc: r2 = LoadClassIdInstr(r0)
    //     0xb83adc: ldur            x2, [x0, #-1]
    //     0xb83ae0: ubfx            x2, x2, #0xc, #0x14
    // 0xb83ae4: r16 = "product_rating"
    //     0xb83ae4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xb83ae8: ldr             x16, [x16, #0xf20]
    // 0xb83aec: stp             x16, x0, [SP]
    // 0xb83af0: mov             x0, x2
    // 0xb83af4: mov             lr, x0
    // 0xb83af8: ldr             lr, [x21, lr, lsl #3]
    // 0xb83afc: blr             lr
    // 0xb83b00: tbnz            w0, #4, #0xb83f30
    // 0xb83b04: ldur            x0, [fp, #-0x10]
    // 0xb83b08: LoadField: r1 = r0->field_e3
    //     0xb83b08: ldur            w1, [x0, #0xe3]
    // 0xb83b0c: DecompressPointer r1
    //     0xb83b0c: add             x1, x1, HEAP, lsl #32
    // 0xb83b10: cmp             w1, NULL
    // 0xb83b14: b.ne            #0xb83b20
    // 0xb83b18: r2 = Null
    //     0xb83b18: mov             x2, NULL
    // 0xb83b1c: b               #0xb83b28
    // 0xb83b20: LoadField: r2 = r1->field_7
    //     0xb83b20: ldur            w2, [x1, #7]
    // 0xb83b24: DecompressPointer r2
    //     0xb83b24: add             x2, x2, HEAP, lsl #32
    // 0xb83b28: cmp             w2, NULL
    // 0xb83b2c: b.ne            #0xb83b38
    // 0xb83b30: d1 = 0.000000
    //     0xb83b30: eor             v1.16b, v1.16b, v1.16b
    // 0xb83b34: b               #0xb83b40
    // 0xb83b38: LoadField: d0 = r2->field_7
    //     0xb83b38: ldur            d0, [x2, #7]
    // 0xb83b3c: mov             v1.16b, v0.16b
    // 0xb83b40: d0 = 4.000000
    //     0xb83b40: fmov            d0, #4.00000000
    // 0xb83b44: fcmp            d1, d0
    // 0xb83b48: b.lt            #0xb83b58
    // 0xb83b4c: r1 = Instance_Color
    //     0xb83b4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb83b50: ldr             x1, [x1, #0x858]
    // 0xb83b54: b               #0xb83c04
    // 0xb83b58: cmp             w1, NULL
    // 0xb83b5c: b.ne            #0xb83b68
    // 0xb83b60: r2 = Null
    //     0xb83b60: mov             x2, NULL
    // 0xb83b64: b               #0xb83b70
    // 0xb83b68: LoadField: r2 = r1->field_7
    //     0xb83b68: ldur            w2, [x1, #7]
    // 0xb83b6c: DecompressPointer r2
    //     0xb83b6c: add             x2, x2, HEAP, lsl #32
    // 0xb83b70: cmp             w2, NULL
    // 0xb83b74: b.ne            #0xb83b80
    // 0xb83b78: d1 = 0.000000
    //     0xb83b78: eor             v1.16b, v1.16b, v1.16b
    // 0xb83b7c: b               #0xb83b88
    // 0xb83b80: LoadField: d0 = r2->field_7
    //     0xb83b80: ldur            d0, [x2, #7]
    // 0xb83b84: mov             v1.16b, v0.16b
    // 0xb83b88: d0 = 3.500000
    //     0xb83b88: fmov            d0, #3.50000000
    // 0xb83b8c: fcmp            d1, d0
    // 0xb83b90: b.lt            #0xb83bac
    // 0xb83b94: r1 = Instance_Color
    //     0xb83b94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb83b98: ldr             x1, [x1, #0x858]
    // 0xb83b9c: d0 = 0.700000
    //     0xb83b9c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb83ba0: ldr             d0, [x17, #0xf48]
    // 0xb83ba4: r0 = withOpacity()
    //     0xb83ba4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb83ba8: b               #0xb83bfc
    // 0xb83bac: cmp             w1, NULL
    // 0xb83bb0: b.ne            #0xb83bbc
    // 0xb83bb4: r0 = Null
    //     0xb83bb4: mov             x0, NULL
    // 0xb83bb8: b               #0xb83bc4
    // 0xb83bbc: LoadField: r0 = r1->field_7
    //     0xb83bbc: ldur            w0, [x1, #7]
    // 0xb83bc0: DecompressPointer r0
    //     0xb83bc0: add             x0, x0, HEAP, lsl #32
    // 0xb83bc4: cmp             w0, NULL
    // 0xb83bc8: b.ne            #0xb83bd4
    // 0xb83bcc: d1 = 0.000000
    //     0xb83bcc: eor             v1.16b, v1.16b, v1.16b
    // 0xb83bd0: b               #0xb83bdc
    // 0xb83bd4: LoadField: d0 = r0->field_7
    //     0xb83bd4: ldur            d0, [x0, #7]
    // 0xb83bd8: mov             v1.16b, v0.16b
    // 0xb83bdc: d0 = 2.000000
    //     0xb83bdc: fmov            d0, #2.00000000
    // 0xb83be0: fcmp            d1, d0
    // 0xb83be4: b.lt            #0xb83bf4
    // 0xb83be8: r0 = Instance_Color
    //     0xb83be8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb83bec: ldr             x0, [x0, #0x860]
    // 0xb83bf0: b               #0xb83bfc
    // 0xb83bf4: r0 = Instance_Color
    //     0xb83bf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb83bf8: ldr             x0, [x0, #0x50]
    // 0xb83bfc: mov             x1, x0
    // 0xb83c00: ldur            x0, [fp, #-0x10]
    // 0xb83c04: stur            x1, [fp, #-0x20]
    // 0xb83c08: r0 = ColorFilter()
    //     0xb83c08: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb83c0c: mov             x1, x0
    // 0xb83c10: ldur            x0, [fp, #-0x20]
    // 0xb83c14: stur            x1, [fp, #-0x28]
    // 0xb83c18: StoreField: r1->field_7 = r0
    //     0xb83c18: stur            w0, [x1, #7]
    // 0xb83c1c: r0 = Instance_BlendMode
    //     0xb83c1c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb83c20: ldr             x0, [x0, #0xb30]
    // 0xb83c24: StoreField: r1->field_b = r0
    //     0xb83c24: stur            w0, [x1, #0xb]
    // 0xb83c28: r2 = 1
    //     0xb83c28: movz            x2, #0x1
    // 0xb83c2c: StoreField: r1->field_13 = r2
    //     0xb83c2c: stur            x2, [x1, #0x13]
    // 0xb83c30: r0 = SvgPicture()
    //     0xb83c30: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb83c34: stur            x0, [fp, #-0x20]
    // 0xb83c38: ldur            x16, [fp, #-0x28]
    // 0xb83c3c: str             x16, [SP]
    // 0xb83c40: mov             x1, x0
    // 0xb83c44: r2 = "assets/images/green_star.svg"
    //     0xb83c44: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb83c48: ldr             x2, [x2, #0x9a0]
    // 0xb83c4c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb83c4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb83c50: ldr             x4, [x4, #0xa38]
    // 0xb83c54: r0 = SvgPicture.asset()
    //     0xb83c54: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb83c58: ldur            x0, [fp, #-0x10]
    // 0xb83c5c: LoadField: r1 = r0->field_e3
    //     0xb83c5c: ldur            w1, [x0, #0xe3]
    // 0xb83c60: DecompressPointer r1
    //     0xb83c60: add             x1, x1, HEAP, lsl #32
    // 0xb83c64: cmp             w1, NULL
    // 0xb83c68: b.ne            #0xb83c74
    // 0xb83c6c: r0 = Null
    //     0xb83c6c: mov             x0, NULL
    // 0xb83c70: b               #0xb83c98
    // 0xb83c74: LoadField: r2 = r1->field_7
    //     0xb83c74: ldur            w2, [x1, #7]
    // 0xb83c78: DecompressPointer r2
    //     0xb83c78: add             x2, x2, HEAP, lsl #32
    // 0xb83c7c: cmp             w2, NULL
    // 0xb83c80: b.ne            #0xb83c8c
    // 0xb83c84: r0 = Null
    //     0xb83c84: mov             x0, NULL
    // 0xb83c88: b               #0xb83c98
    // 0xb83c8c: mov             x1, x2
    // 0xb83c90: r2 = 1
    //     0xb83c90: movz            x2, #0x1
    // 0xb83c94: r0 = toStringAsFixed()
    //     0xb83c94: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb83c98: cmp             w0, NULL
    // 0xb83c9c: b.ne            #0xb83ca8
    // 0xb83ca0: r4 = ""
    //     0xb83ca0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb83ca4: b               #0xb83cac
    // 0xb83ca8: mov             x4, x0
    // 0xb83cac: ldur            x3, [fp, #-8]
    // 0xb83cb0: ldur            x0, [fp, #-0x10]
    // 0xb83cb4: ldur            x2, [fp, #-0x20]
    // 0xb83cb8: stur            x4, [fp, #-0x28]
    // 0xb83cbc: LoadField: r1 = r3->field_f
    //     0xb83cbc: ldur            w1, [x3, #0xf]
    // 0xb83cc0: DecompressPointer r1
    //     0xb83cc0: add             x1, x1, HEAP, lsl #32
    // 0xb83cc4: cmp             w1, NULL
    // 0xb83cc8: b.eq            #0xb84248
    // 0xb83ccc: r0 = of()
    //     0xb83ccc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb83cd0: LoadField: r1 = r0->field_87
    //     0xb83cd0: ldur            w1, [x0, #0x87]
    // 0xb83cd4: DecompressPointer r1
    //     0xb83cd4: add             x1, x1, HEAP, lsl #32
    // 0xb83cd8: LoadField: r0 = r1->field_7
    //     0xb83cd8: ldur            w0, [x1, #7]
    // 0xb83cdc: DecompressPointer r0
    //     0xb83cdc: add             x0, x0, HEAP, lsl #32
    // 0xb83ce0: r16 = 12.000000
    //     0xb83ce0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb83ce4: ldr             x16, [x16, #0x9e8]
    // 0xb83ce8: r30 = Instance_Color
    //     0xb83ce8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb83cec: stp             lr, x16, [SP]
    // 0xb83cf0: mov             x1, x0
    // 0xb83cf4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb83cf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb83cf8: ldr             x4, [x4, #0xaa0]
    // 0xb83cfc: r0 = copyWith()
    //     0xb83cfc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb83d00: stur            x0, [fp, #-0x30]
    // 0xb83d04: r0 = Text()
    //     0xb83d04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb83d08: mov             x3, x0
    // 0xb83d0c: ldur            x0, [fp, #-0x28]
    // 0xb83d10: stur            x3, [fp, #-0x38]
    // 0xb83d14: StoreField: r3->field_b = r0
    //     0xb83d14: stur            w0, [x3, #0xb]
    // 0xb83d18: ldur            x0, [fp, #-0x30]
    // 0xb83d1c: StoreField: r3->field_13 = r0
    //     0xb83d1c: stur            w0, [x3, #0x13]
    // 0xb83d20: r1 = Null
    //     0xb83d20: mov             x1, NULL
    // 0xb83d24: r2 = 4
    //     0xb83d24: movz            x2, #0x4
    // 0xb83d28: r0 = AllocateArray()
    //     0xb83d28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb83d2c: mov             x2, x0
    // 0xb83d30: ldur            x0, [fp, #-0x20]
    // 0xb83d34: stur            x2, [fp, #-0x28]
    // 0xb83d38: StoreField: r2->field_f = r0
    //     0xb83d38: stur            w0, [x2, #0xf]
    // 0xb83d3c: ldur            x0, [fp, #-0x38]
    // 0xb83d40: StoreField: r2->field_13 = r0
    //     0xb83d40: stur            w0, [x2, #0x13]
    // 0xb83d44: r1 = <Widget>
    //     0xb83d44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb83d48: r0 = AllocateGrowableArray()
    //     0xb83d48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb83d4c: mov             x3, x0
    // 0xb83d50: ldur            x0, [fp, #-0x28]
    // 0xb83d54: stur            x3, [fp, #-0x30]
    // 0xb83d58: StoreField: r3->field_f = r0
    //     0xb83d58: stur            w0, [x3, #0xf]
    // 0xb83d5c: r0 = 4
    //     0xb83d5c: movz            x0, #0x4
    // 0xb83d60: StoreField: r3->field_b = r0
    //     0xb83d60: stur            w0, [x3, #0xb]
    // 0xb83d64: ldur            x2, [fp, #-0x10]
    // 0xb83d68: LoadField: r0 = r2->field_e3
    //     0xb83d68: ldur            w0, [x2, #0xe3]
    // 0xb83d6c: DecompressPointer r0
    //     0xb83d6c: add             x0, x0, HEAP, lsl #32
    // 0xb83d70: cmp             w0, NULL
    // 0xb83d74: b.ne            #0xb83d80
    // 0xb83d78: mov             x2, x3
    // 0xb83d7c: b               #0xb83ed4
    // 0xb83d80: LoadField: r4 = r0->field_b
    //     0xb83d80: ldur            w4, [x0, #0xb]
    // 0xb83d84: DecompressPointer r4
    //     0xb83d84: add             x4, x4, HEAP, lsl #32
    // 0xb83d88: stur            x4, [fp, #-0x20]
    // 0xb83d8c: cmp             w4, NULL
    // 0xb83d90: b.eq            #0xb83ed0
    // 0xb83d94: ldur            x0, [fp, #-8]
    // 0xb83d98: r1 = Null
    //     0xb83d98: mov             x1, NULL
    // 0xb83d9c: r2 = 6
    //     0xb83d9c: movz            x2, #0x6
    // 0xb83da0: r0 = AllocateArray()
    //     0xb83da0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb83da4: r16 = " | ("
    //     0xb83da4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xb83da8: ldr             x16, [x16, #0xd70]
    // 0xb83dac: StoreField: r0->field_f = r16
    //     0xb83dac: stur            w16, [x0, #0xf]
    // 0xb83db0: ldur            x1, [fp, #-0x20]
    // 0xb83db4: LoadField: d0 = r1->field_7
    //     0xb83db4: ldur            d0, [x1, #7]
    // 0xb83db8: fcmp            d0, d0
    // 0xb83dbc: b.vs            #0xb8424c
    // 0xb83dc0: fcvtzs          x1, d0
    // 0xb83dc4: asr             x16, x1, #0x1e
    // 0xb83dc8: cmp             x16, x1, asr #63
    // 0xb83dcc: b.ne            #0xb8424c
    // 0xb83dd0: lsl             x1, x1, #1
    // 0xb83dd4: StoreField: r0->field_13 = r1
    //     0xb83dd4: stur            w1, [x0, #0x13]
    // 0xb83dd8: r16 = ")"
    //     0xb83dd8: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb83ddc: ArrayStore: r0[0] = r16  ; List_4
    //     0xb83ddc: stur            w16, [x0, #0x17]
    // 0xb83de0: str             x0, [SP]
    // 0xb83de4: r0 = _interpolate()
    //     0xb83de4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb83de8: ldur            x3, [fp, #-8]
    // 0xb83dec: stur            x0, [fp, #-0x20]
    // 0xb83df0: LoadField: r1 = r3->field_f
    //     0xb83df0: ldur            w1, [x3, #0xf]
    // 0xb83df4: DecompressPointer r1
    //     0xb83df4: add             x1, x1, HEAP, lsl #32
    // 0xb83df8: cmp             w1, NULL
    // 0xb83dfc: b.eq            #0xb84274
    // 0xb83e00: r0 = of()
    //     0xb83e00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb83e04: LoadField: r1 = r0->field_87
    //     0xb83e04: ldur            w1, [x0, #0x87]
    // 0xb83e08: DecompressPointer r1
    //     0xb83e08: add             x1, x1, HEAP, lsl #32
    // 0xb83e0c: LoadField: r0 = r1->field_2b
    //     0xb83e0c: ldur            w0, [x1, #0x2b]
    // 0xb83e10: DecompressPointer r0
    //     0xb83e10: add             x0, x0, HEAP, lsl #32
    // 0xb83e14: r16 = 12.000000
    //     0xb83e14: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb83e18: ldr             x16, [x16, #0x9e8]
    // 0xb83e1c: r30 = Instance_Color
    //     0xb83e1c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb83e20: stp             lr, x16, [SP]
    // 0xb83e24: mov             x1, x0
    // 0xb83e28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb83e28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb83e2c: ldr             x4, [x4, #0xaa0]
    // 0xb83e30: r0 = copyWith()
    //     0xb83e30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb83e34: stur            x0, [fp, #-0x28]
    // 0xb83e38: r0 = Text()
    //     0xb83e38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb83e3c: mov             x2, x0
    // 0xb83e40: ldur            x0, [fp, #-0x20]
    // 0xb83e44: stur            x2, [fp, #-0x38]
    // 0xb83e48: StoreField: r2->field_b = r0
    //     0xb83e48: stur            w0, [x2, #0xb]
    // 0xb83e4c: ldur            x0, [fp, #-0x28]
    // 0xb83e50: StoreField: r2->field_13 = r0
    //     0xb83e50: stur            w0, [x2, #0x13]
    // 0xb83e54: ldur            x0, [fp, #-0x30]
    // 0xb83e58: LoadField: r1 = r0->field_b
    //     0xb83e58: ldur            w1, [x0, #0xb]
    // 0xb83e5c: LoadField: r3 = r0->field_f
    //     0xb83e5c: ldur            w3, [x0, #0xf]
    // 0xb83e60: DecompressPointer r3
    //     0xb83e60: add             x3, x3, HEAP, lsl #32
    // 0xb83e64: LoadField: r4 = r3->field_b
    //     0xb83e64: ldur            w4, [x3, #0xb]
    // 0xb83e68: r3 = LoadInt32Instr(r1)
    //     0xb83e68: sbfx            x3, x1, #1, #0x1f
    // 0xb83e6c: stur            x3, [fp, #-0x40]
    // 0xb83e70: r1 = LoadInt32Instr(r4)
    //     0xb83e70: sbfx            x1, x4, #1, #0x1f
    // 0xb83e74: cmp             x3, x1
    // 0xb83e78: b.ne            #0xb83e84
    // 0xb83e7c: mov             x1, x0
    // 0xb83e80: r0 = _growToNextCapacity()
    //     0xb83e80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb83e84: ldur            x2, [fp, #-0x30]
    // 0xb83e88: ldur            x3, [fp, #-0x40]
    // 0xb83e8c: add             x0, x3, #1
    // 0xb83e90: lsl             x1, x0, #1
    // 0xb83e94: StoreField: r2->field_b = r1
    //     0xb83e94: stur            w1, [x2, #0xb]
    // 0xb83e98: LoadField: r1 = r2->field_f
    //     0xb83e98: ldur            w1, [x2, #0xf]
    // 0xb83e9c: DecompressPointer r1
    //     0xb83e9c: add             x1, x1, HEAP, lsl #32
    // 0xb83ea0: ldur            x0, [fp, #-0x38]
    // 0xb83ea4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb83ea4: add             x25, x1, x3, lsl #2
    //     0xb83ea8: add             x25, x25, #0xf
    //     0xb83eac: str             w0, [x25]
    //     0xb83eb0: tbz             w0, #0, #0xb83ecc
    //     0xb83eb4: ldurb           w16, [x1, #-1]
    //     0xb83eb8: ldurb           w17, [x0, #-1]
    //     0xb83ebc: and             x16, x17, x16, lsr #2
    //     0xb83ec0: tst             x16, HEAP, lsr #32
    //     0xb83ec4: b.eq            #0xb83ecc
    //     0xb83ec8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb83ecc: b               #0xb83ed4
    // 0xb83ed0: mov             x2, x3
    // 0xb83ed4: r0 = Row()
    //     0xb83ed4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb83ed8: r4 = Instance_Axis
    //     0xb83ed8: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb83edc: StoreField: r0->field_f = r4
    //     0xb83edc: stur            w4, [x0, #0xf]
    // 0xb83ee0: r5 = Instance_MainAxisAlignment
    //     0xb83ee0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb83ee4: ldr             x5, [x5, #0xa08]
    // 0xb83ee8: StoreField: r0->field_13 = r5
    //     0xb83ee8: stur            w5, [x0, #0x13]
    // 0xb83eec: r6 = Instance_MainAxisSize
    //     0xb83eec: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb83ef0: ldr             x6, [x6, #0xa10]
    // 0xb83ef4: ArrayStore: r0[0] = r6  ; List_4
    //     0xb83ef4: stur            w6, [x0, #0x17]
    // 0xb83ef8: r7 = Instance_CrossAxisAlignment
    //     0xb83ef8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb83efc: ldr             x7, [x7, #0xa18]
    // 0xb83f00: StoreField: r0->field_1b = r7
    //     0xb83f00: stur            w7, [x0, #0x1b]
    // 0xb83f04: r8 = Instance_VerticalDirection
    //     0xb83f04: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb83f08: ldr             x8, [x8, #0xa20]
    // 0xb83f0c: StoreField: r0->field_23 = r8
    //     0xb83f0c: stur            w8, [x0, #0x23]
    // 0xb83f10: r9 = Instance_Clip
    //     0xb83f10: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb83f14: ldr             x9, [x9, #0x38]
    // 0xb83f18: StoreField: r0->field_2b = r9
    //     0xb83f18: stur            w9, [x0, #0x2b]
    // 0xb83f1c: StoreField: r0->field_2f = rZR
    //     0xb83f1c: stur            xzr, [x0, #0x2f]
    // 0xb83f20: ldur            x1, [fp, #-0x30]
    // 0xb83f24: StoreField: r0->field_b = r1
    //     0xb83f24: stur            w1, [x0, #0xb]
    // 0xb83f28: mov             x1, x0
    // 0xb83f2c: b               #0xb841d0
    // 0xb83f30: ldur            x3, [fp, #-8]
    // 0xb83f34: ldur            x2, [fp, #-0x10]
    // 0xb83f38: r7 = Instance_CrossAxisAlignment
    //     0xb83f38: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb83f3c: ldr             x7, [x7, #0xa18]
    // 0xb83f40: r5 = Instance_MainAxisAlignment
    //     0xb83f40: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb83f44: ldr             x5, [x5, #0xa08]
    // 0xb83f48: r6 = Instance_MainAxisSize
    //     0xb83f48: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb83f4c: ldr             x6, [x6, #0xa10]
    // 0xb83f50: r4 = Instance_Axis
    //     0xb83f50: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb83f54: r8 = Instance_VerticalDirection
    //     0xb83f54: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb83f58: ldr             x8, [x8, #0xa20]
    // 0xb83f5c: r0 = Instance_BlendMode
    //     0xb83f5c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb83f60: ldr             x0, [x0, #0xb30]
    // 0xb83f64: r9 = Instance_Clip
    //     0xb83f64: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb83f68: ldr             x9, [x9, #0x38]
    // 0xb83f6c: LoadField: r1 = r3->field_f
    //     0xb83f6c: ldur            w1, [x3, #0xf]
    // 0xb83f70: DecompressPointer r1
    //     0xb83f70: add             x1, x1, HEAP, lsl #32
    // 0xb83f74: cmp             w1, NULL
    // 0xb83f78: b.eq            #0xb84278
    // 0xb83f7c: r0 = of()
    //     0xb83f7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb83f80: LoadField: r1 = r0->field_5b
    //     0xb83f80: ldur            w1, [x0, #0x5b]
    // 0xb83f84: DecompressPointer r1
    //     0xb83f84: add             x1, x1, HEAP, lsl #32
    // 0xb83f88: stur            x1, [fp, #-0x20]
    // 0xb83f8c: r0 = ColorFilter()
    //     0xb83f8c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb83f90: mov             x1, x0
    // 0xb83f94: ldur            x0, [fp, #-0x20]
    // 0xb83f98: stur            x1, [fp, #-0x28]
    // 0xb83f9c: StoreField: r1->field_7 = r0
    //     0xb83f9c: stur            w0, [x1, #7]
    // 0xb83fa0: r0 = Instance_BlendMode
    //     0xb83fa0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb83fa4: ldr             x0, [x0, #0xb30]
    // 0xb83fa8: StoreField: r1->field_b = r0
    //     0xb83fa8: stur            w0, [x1, #0xb]
    // 0xb83fac: r2 = 1
    //     0xb83fac: movz            x2, #0x1
    // 0xb83fb0: StoreField: r1->field_13 = r2
    //     0xb83fb0: stur            x2, [x1, #0x13]
    // 0xb83fb4: r0 = SvgPicture()
    //     0xb83fb4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb83fb8: stur            x0, [fp, #-0x20]
    // 0xb83fbc: ldur            x16, [fp, #-0x28]
    // 0xb83fc0: str             x16, [SP]
    // 0xb83fc4: mov             x1, x0
    // 0xb83fc8: r2 = "assets/images/green_star.svg"
    //     0xb83fc8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb83fcc: ldr             x2, [x2, #0x9a0]
    // 0xb83fd0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb83fd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb83fd4: ldr             x4, [x4, #0xa38]
    // 0xb83fd8: r0 = SvgPicture.asset()
    //     0xb83fd8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb83fdc: ldur            x0, [fp, #-0x10]
    // 0xb83fe0: LoadField: r1 = r0->field_e3
    //     0xb83fe0: ldur            w1, [x0, #0xe3]
    // 0xb83fe4: DecompressPointer r1
    //     0xb83fe4: add             x1, x1, HEAP, lsl #32
    // 0xb83fe8: cmp             w1, NULL
    // 0xb83fec: b.ne            #0xb83ff8
    // 0xb83ff0: r0 = Null
    //     0xb83ff0: mov             x0, NULL
    // 0xb83ff4: b               #0xb8401c
    // 0xb83ff8: LoadField: r0 = r1->field_7
    //     0xb83ff8: ldur            w0, [x1, #7]
    // 0xb83ffc: DecompressPointer r0
    //     0xb83ffc: add             x0, x0, HEAP, lsl #32
    // 0xb84000: cmp             w0, NULL
    // 0xb84004: b.ne            #0xb84010
    // 0xb84008: r0 = Null
    //     0xb84008: mov             x0, NULL
    // 0xb8400c: b               #0xb8401c
    // 0xb84010: mov             x1, x0
    // 0xb84014: r2 = 1
    //     0xb84014: movz            x2, #0x1
    // 0xb84018: r0 = toStringAsFixed()
    //     0xb84018: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb8401c: cmp             w0, NULL
    // 0xb84020: b.ne            #0xb8402c
    // 0xb84024: r3 = ""
    //     0xb84024: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb84028: b               #0xb84030
    // 0xb8402c: mov             x3, x0
    // 0xb84030: ldur            x2, [fp, #-8]
    // 0xb84034: ldur            x0, [fp, #-0x20]
    // 0xb84038: stur            x3, [fp, #-0x10]
    // 0xb8403c: LoadField: r1 = r2->field_f
    //     0xb8403c: ldur            w1, [x2, #0xf]
    // 0xb84040: DecompressPointer r1
    //     0xb84040: add             x1, x1, HEAP, lsl #32
    // 0xb84044: cmp             w1, NULL
    // 0xb84048: b.eq            #0xb8427c
    // 0xb8404c: r0 = of()
    //     0xb8404c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb84050: LoadField: r1 = r0->field_87
    //     0xb84050: ldur            w1, [x0, #0x87]
    // 0xb84054: DecompressPointer r1
    //     0xb84054: add             x1, x1, HEAP, lsl #32
    // 0xb84058: LoadField: r0 = r1->field_7
    //     0xb84058: ldur            w0, [x1, #7]
    // 0xb8405c: DecompressPointer r0
    //     0xb8405c: add             x0, x0, HEAP, lsl #32
    // 0xb84060: r16 = 12.000000
    //     0xb84060: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb84064: ldr             x16, [x16, #0x9e8]
    // 0xb84068: r30 = Instance_Color
    //     0xb84068: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8406c: stp             lr, x16, [SP]
    // 0xb84070: mov             x1, x0
    // 0xb84074: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb84074: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb84078: ldr             x4, [x4, #0xaa0]
    // 0xb8407c: r0 = copyWith()
    //     0xb8407c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb84080: stur            x0, [fp, #-0x28]
    // 0xb84084: r0 = Text()
    //     0xb84084: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb84088: mov             x2, x0
    // 0xb8408c: ldur            x0, [fp, #-0x10]
    // 0xb84090: stur            x2, [fp, #-0x30]
    // 0xb84094: StoreField: r2->field_b = r0
    //     0xb84094: stur            w0, [x2, #0xb]
    // 0xb84098: ldur            x0, [fp, #-0x28]
    // 0xb8409c: StoreField: r2->field_13 = r0
    //     0xb8409c: stur            w0, [x2, #0x13]
    // 0xb840a0: ldur            x0, [fp, #-8]
    // 0xb840a4: LoadField: r1 = r0->field_f
    //     0xb840a4: ldur            w1, [x0, #0xf]
    // 0xb840a8: DecompressPointer r1
    //     0xb840a8: add             x1, x1, HEAP, lsl #32
    // 0xb840ac: cmp             w1, NULL
    // 0xb840b0: b.eq            #0xb84280
    // 0xb840b4: r0 = of()
    //     0xb840b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb840b8: LoadField: r1 = r0->field_87
    //     0xb840b8: ldur            w1, [x0, #0x87]
    // 0xb840bc: DecompressPointer r1
    //     0xb840bc: add             x1, x1, HEAP, lsl #32
    // 0xb840c0: LoadField: r0 = r1->field_2b
    //     0xb840c0: ldur            w0, [x1, #0x2b]
    // 0xb840c4: DecompressPointer r0
    //     0xb840c4: add             x0, x0, HEAP, lsl #32
    // 0xb840c8: ldur            x1, [fp, #-8]
    // 0xb840cc: stur            x0, [fp, #-0x10]
    // 0xb840d0: LoadField: r2 = r1->field_f
    //     0xb840d0: ldur            w2, [x1, #0xf]
    // 0xb840d4: DecompressPointer r2
    //     0xb840d4: add             x2, x2, HEAP, lsl #32
    // 0xb840d8: cmp             w2, NULL
    // 0xb840dc: b.eq            #0xb84284
    // 0xb840e0: mov             x1, x2
    // 0xb840e4: r0 = of()
    //     0xb840e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb840e8: LoadField: r1 = r0->field_5b
    //     0xb840e8: ldur            w1, [x0, #0x5b]
    // 0xb840ec: DecompressPointer r1
    //     0xb840ec: add             x1, x1, HEAP, lsl #32
    // 0xb840f0: r16 = 10.000000
    //     0xb840f0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb840f4: stp             x1, x16, [SP]
    // 0xb840f8: ldur            x1, [fp, #-0x10]
    // 0xb840fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb840fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb84100: ldr             x4, [x4, #0xaa0]
    // 0xb84104: r0 = copyWith()
    //     0xb84104: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb84108: stur            x0, [fp, #-8]
    // 0xb8410c: r0 = Text()
    //     0xb8410c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb84110: mov             x3, x0
    // 0xb84114: r0 = " Brand Rating"
    //     0xb84114: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xb84118: ldr             x0, [x0, #0xd78]
    // 0xb8411c: stur            x3, [fp, #-0x10]
    // 0xb84120: StoreField: r3->field_b = r0
    //     0xb84120: stur            w0, [x3, #0xb]
    // 0xb84124: ldur            x0, [fp, #-8]
    // 0xb84128: StoreField: r3->field_13 = r0
    //     0xb84128: stur            w0, [x3, #0x13]
    // 0xb8412c: r1 = Null
    //     0xb8412c: mov             x1, NULL
    // 0xb84130: r2 = 6
    //     0xb84130: movz            x2, #0x6
    // 0xb84134: r0 = AllocateArray()
    //     0xb84134: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb84138: mov             x2, x0
    // 0xb8413c: ldur            x0, [fp, #-0x20]
    // 0xb84140: stur            x2, [fp, #-8]
    // 0xb84144: StoreField: r2->field_f = r0
    //     0xb84144: stur            w0, [x2, #0xf]
    // 0xb84148: ldur            x0, [fp, #-0x30]
    // 0xb8414c: StoreField: r2->field_13 = r0
    //     0xb8414c: stur            w0, [x2, #0x13]
    // 0xb84150: ldur            x0, [fp, #-0x10]
    // 0xb84154: ArrayStore: r2[0] = r0  ; List_4
    //     0xb84154: stur            w0, [x2, #0x17]
    // 0xb84158: r1 = <Widget>
    //     0xb84158: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8415c: r0 = AllocateGrowableArray()
    //     0xb8415c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb84160: mov             x1, x0
    // 0xb84164: ldur            x0, [fp, #-8]
    // 0xb84168: stur            x1, [fp, #-0x10]
    // 0xb8416c: StoreField: r1->field_f = r0
    //     0xb8416c: stur            w0, [x1, #0xf]
    // 0xb84170: r0 = 6
    //     0xb84170: movz            x0, #0x6
    // 0xb84174: StoreField: r1->field_b = r0
    //     0xb84174: stur            w0, [x1, #0xb]
    // 0xb84178: r0 = Row()
    //     0xb84178: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb8417c: mov             x1, x0
    // 0xb84180: r0 = Instance_Axis
    //     0xb84180: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb84184: StoreField: r1->field_f = r0
    //     0xb84184: stur            w0, [x1, #0xf]
    // 0xb84188: r0 = Instance_MainAxisAlignment
    //     0xb84188: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb8418c: ldr             x0, [x0, #0xa08]
    // 0xb84190: StoreField: r1->field_13 = r0
    //     0xb84190: stur            w0, [x1, #0x13]
    // 0xb84194: r0 = Instance_MainAxisSize
    //     0xb84194: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb84198: ldr             x0, [x0, #0xa10]
    // 0xb8419c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb8419c: stur            w0, [x1, #0x17]
    // 0xb841a0: r0 = Instance_CrossAxisAlignment
    //     0xb841a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb841a4: ldr             x0, [x0, #0xa18]
    // 0xb841a8: StoreField: r1->field_1b = r0
    //     0xb841a8: stur            w0, [x1, #0x1b]
    // 0xb841ac: r0 = Instance_VerticalDirection
    //     0xb841ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb841b0: ldr             x0, [x0, #0xa20]
    // 0xb841b4: StoreField: r1->field_23 = r0
    //     0xb841b4: stur            w0, [x1, #0x23]
    // 0xb841b8: r0 = Instance_Clip
    //     0xb841b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb841bc: ldr             x0, [x0, #0x38]
    // 0xb841c0: StoreField: r1->field_2b = r0
    //     0xb841c0: stur            w0, [x1, #0x2b]
    // 0xb841c4: StoreField: r1->field_2f = rZR
    //     0xb841c4: stur            xzr, [x1, #0x2f]
    // 0xb841c8: ldur            x0, [fp, #-0x10]
    // 0xb841cc: StoreField: r1->field_b = r0
    //     0xb841cc: stur            w0, [x1, #0xb]
    // 0xb841d0: ldur            x0, [fp, #-0x18]
    // 0xb841d4: stur            x1, [fp, #-8]
    // 0xb841d8: r0 = Padding()
    //     0xb841d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb841dc: mov             x1, x0
    // 0xb841e0: r0 = Instance_EdgeInsets
    //     0xb841e0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb841e4: ldr             x0, [x0, #0xc98]
    // 0xb841e8: stur            x1, [fp, #-0x10]
    // 0xb841ec: StoreField: r1->field_f = r0
    //     0xb841ec: stur            w0, [x1, #0xf]
    // 0xb841f0: ldur            x0, [fp, #-8]
    // 0xb841f4: StoreField: r1->field_b = r0
    //     0xb841f4: stur            w0, [x1, #0xb]
    // 0xb841f8: r0 = Visibility()
    //     0xb841f8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb841fc: ldur            x1, [fp, #-0x10]
    // 0xb84200: StoreField: r0->field_b = r1
    //     0xb84200: stur            w1, [x0, #0xb]
    // 0xb84204: r1 = Instance_SizedBox
    //     0xb84204: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb84208: StoreField: r0->field_f = r1
    //     0xb84208: stur            w1, [x0, #0xf]
    // 0xb8420c: ldur            x1, [fp, #-0x18]
    // 0xb84210: StoreField: r0->field_13 = r1
    //     0xb84210: stur            w1, [x0, #0x13]
    // 0xb84214: r1 = false
    //     0xb84214: add             x1, NULL, #0x30  ; false
    // 0xb84218: ArrayStore: r0[0] = r1  ; List_4
    //     0xb84218: stur            w1, [x0, #0x17]
    // 0xb8421c: StoreField: r0->field_1b = r1
    //     0xb8421c: stur            w1, [x0, #0x1b]
    // 0xb84220: StoreField: r0->field_1f = r1
    //     0xb84220: stur            w1, [x0, #0x1f]
    // 0xb84224: StoreField: r0->field_23 = r1
    //     0xb84224: stur            w1, [x0, #0x23]
    // 0xb84228: StoreField: r0->field_27 = r1
    //     0xb84228: stur            w1, [x0, #0x27]
    // 0xb8422c: StoreField: r0->field_2b = r1
    //     0xb8422c: stur            w1, [x0, #0x2b]
    // 0xb84230: LeaveFrame
    //     0xb84230: mov             SP, fp
    //     0xb84234: ldp             fp, lr, [SP], #0x10
    // 0xb84238: ret
    //     0xb84238: ret             
    // 0xb8423c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8423c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84240: b               #0xb839c4
    // 0xb84244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84244: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84248: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84248: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8424c: SaveReg d0
    //     0xb8424c: str             q0, [SP, #-0x10]!
    // 0xb84250: SaveReg r0
    //     0xb84250: str             x0, [SP, #-8]!
    // 0xb84254: r0 = 74
    //     0xb84254: movz            x0, #0x4a
    // 0xb84258: r30 = DoubleToIntegerStub
    //     0xb84258: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb8425c: LoadField: r30 = r30->field_7
    //     0xb8425c: ldur            lr, [lr, #7]
    // 0xb84260: blr             lr
    // 0xb84264: mov             x1, x0
    // 0xb84268: RestoreReg r0
    //     0xb84268: ldr             x0, [SP], #8
    // 0xb8426c: RestoreReg d0
    //     0xb8426c: ldr             q0, [SP], #0x10
    // 0xb84270: b               #0xb83dd4
    // 0xb84274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84274: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84278: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8427c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8427c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84280: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84284: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xb84288, size: 0x124
    // 0xb84288: EnterFrame
    //     0xb84288: stp             fp, lr, [SP, #-0x10]!
    //     0xb8428c: mov             fp, SP
    // 0xb84290: AllocStack(0x38)
    //     0xb84290: sub             SP, SP, #0x38
    // 0xb84294: SetupParameters()
    //     0xb84294: ldr             x0, [fp, #0x18]
    //     0xb84298: ldur            w2, [x0, #0x17]
    //     0xb8429c: add             x2, x2, HEAP, lsl #32
    //     0xb842a0: stur            x2, [fp, #-8]
    // 0xb842a4: CheckStackOverflow
    //     0xb842a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb842a8: cmp             SP, x16
    //     0xb842ac: b.ls            #0xb8439c
    // 0xb842b0: ldr             x1, [fp, #0x10]
    // 0xb842b4: r0 = visibleFraction()
    //     0xb842b4: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xb842b8: mov             v1.16b, v0.16b
    // 0xb842bc: d0 = 0.500000
    //     0xb842bc: fmov            d0, #0.50000000
    // 0xb842c0: fcmp            d1, d0
    // 0xb842c4: b.le            #0xb8438c
    // 0xb842c8: ldur            x0, [fp, #-8]
    // 0xb842cc: LoadField: r1 = r0->field_b
    //     0xb842cc: ldur            w1, [x0, #0xb]
    // 0xb842d0: DecompressPointer r1
    //     0xb842d0: add             x1, x1, HEAP, lsl #32
    // 0xb842d4: LoadField: r2 = r1->field_f
    //     0xb842d4: ldur            w2, [x1, #0xf]
    // 0xb842d8: DecompressPointer r2
    //     0xb842d8: add             x2, x2, HEAP, lsl #32
    // 0xb842dc: LoadField: r1 = r2->field_b
    //     0xb842dc: ldur            w1, [x2, #0xb]
    // 0xb842e0: DecompressPointer r1
    //     0xb842e0: add             x1, x1, HEAP, lsl #32
    // 0xb842e4: cmp             w1, NULL
    // 0xb842e8: b.eq            #0xb843a4
    // 0xb842ec: LoadField: r2 = r0->field_f
    //     0xb842ec: ldur            w2, [x0, #0xf]
    // 0xb842f0: DecompressPointer r2
    //     0xb842f0: add             x2, x2, HEAP, lsl #32
    // 0xb842f4: cmp             w2, NULL
    // 0xb842f8: b.eq            #0xb843a8
    // 0xb842fc: LoadField: r0 = r2->field_eb
    //     0xb842fc: ldur            w0, [x2, #0xeb]
    // 0xb84300: DecompressPointer r0
    //     0xb84300: add             x0, x0, HEAP, lsl #32
    // 0xb84304: cmp             w0, NULL
    // 0xb84308: b.ne            #0xb84314
    // 0xb8430c: r3 = Null
    //     0xb8430c: mov             x3, NULL
    // 0xb84310: b               #0xb8431c
    // 0xb84314: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb84314: ldur            w3, [x0, #0x17]
    // 0xb84318: DecompressPointer r3
    //     0xb84318: add             x3, x3, HEAP, lsl #32
    // 0xb8431c: cmp             w0, NULL
    // 0xb84320: b.ne            #0xb8432c
    // 0xb84324: r4 = Null
    //     0xb84324: mov             x4, NULL
    // 0xb84328: b               #0xb84334
    // 0xb8432c: LoadField: r4 = r0->field_23
    //     0xb8432c: ldur            w4, [x0, #0x23]
    // 0xb84330: DecompressPointer r4
    //     0xb84330: add             x4, x4, HEAP, lsl #32
    // 0xb84334: LoadField: r5 = r2->field_47
    //     0xb84334: ldur            w5, [x2, #0x47]
    // 0xb84338: DecompressPointer r5
    //     0xb84338: add             x5, x5, HEAP, lsl #32
    // 0xb8433c: cmp             w0, NULL
    // 0xb84340: b.ne            #0xb8434c
    // 0xb84344: r0 = Null
    //     0xb84344: mov             x0, NULL
    // 0xb84348: b               #0xb84358
    // 0xb8434c: LoadField: r6 = r0->field_13
    //     0xb8434c: ldur            w6, [x0, #0x13]
    // 0xb84350: DecompressPointer r6
    //     0xb84350: add             x6, x6, HEAP, lsl #32
    // 0xb84354: mov             x0, x6
    // 0xb84358: LoadField: r6 = r2->field_e3
    //     0xb84358: ldur            w6, [x2, #0xe3]
    // 0xb8435c: DecompressPointer r6
    //     0xb8435c: add             x6, x6, HEAP, lsl #32
    // 0xb84360: LoadField: r2 = r1->field_33
    //     0xb84360: ldur            w2, [x1, #0x33]
    // 0xb84364: DecompressPointer r2
    //     0xb84364: add             x2, x2, HEAP, lsl #32
    // 0xb84368: stp             x3, x2, [SP, #0x20]
    // 0xb8436c: stp             x5, x4, [SP, #0x10]
    // 0xb84370: stp             x6, x0, [SP]
    // 0xb84374: r4 = 0
    //     0xb84374: movz            x4, #0
    // 0xb84378: ldr             x0, [SP, #0x28]
    // 0xb8437c: r16 = UnlinkedCall_0x613b5c
    //     0xb8437c: add             x16, PP, #0x61, lsl #12  ; [pp+0x61d98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb84380: add             x16, x16, #0xd98
    // 0xb84384: ldp             x5, lr, [x16]
    // 0xb84388: blr             lr
    // 0xb8438c: r0 = Null
    //     0xb8438c: mov             x0, NULL
    // 0xb84390: LeaveFrame
    //     0xb84390: mov             SP, fp
    //     0xb84394: ldp             fp, lr, [SP], #0x10
    // 0xb84398: ret
    //     0xb84398: ret             
    // 0xb8439c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8439c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb843a0: b               #0xb842b0
    // 0xb843a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb843a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb843a8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb843a8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb843ac, size: 0x218
    // 0xb843ac: EnterFrame
    //     0xb843ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb843b0: mov             fp, SP
    // 0xb843b4: AllocStack(0x20)
    //     0xb843b4: sub             SP, SP, #0x20
    // 0xb843b8: SetupParameters()
    //     0xb843b8: ldr             x0, [fp, #0x20]
    //     0xb843bc: ldur            w1, [x0, #0x17]
    //     0xb843c0: add             x1, x1, HEAP, lsl #32
    // 0xb843c4: CheckStackOverflow
    //     0xb843c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb843c8: cmp             SP, x16
    //     0xb843cc: b.ls            #0xb845b4
    // 0xb843d0: LoadField: r0 = r1->field_b
    //     0xb843d0: ldur            w0, [x1, #0xb]
    // 0xb843d4: DecompressPointer r0
    //     0xb843d4: add             x0, x0, HEAP, lsl #32
    // 0xb843d8: LoadField: r2 = r0->field_f
    //     0xb843d8: ldur            w2, [x0, #0xf]
    // 0xb843dc: DecompressPointer r2
    //     0xb843dc: add             x2, x2, HEAP, lsl #32
    // 0xb843e0: LoadField: r4 = r1->field_f
    //     0xb843e0: ldur            w4, [x1, #0xf]
    // 0xb843e4: DecompressPointer r4
    //     0xb843e4: add             x4, x4, HEAP, lsl #32
    // 0xb843e8: stur            x4, [fp, #-8]
    // 0xb843ec: cmp             w4, NULL
    // 0xb843f0: b.eq            #0xb845bc
    // 0xb843f4: LoadField: r3 = r4->field_e7
    //     0xb843f4: ldur            w3, [x4, #0xe7]
    // 0xb843f8: DecompressPointer r3
    //     0xb843f8: add             x3, x3, HEAP, lsl #32
    // 0xb843fc: cmp             w3, NULL
    // 0xb84400: b.ne            #0xb8440c
    // 0xb84404: r0 = Null
    //     0xb84404: mov             x0, NULL
    // 0xb84408: b               #0xb84448
    // 0xb8440c: ldr             x0, [fp, #0x10]
    // 0xb84410: LoadField: r1 = r3->field_b
    //     0xb84410: ldur            w1, [x3, #0xb]
    // 0xb84414: r5 = LoadInt32Instr(r0)
    //     0xb84414: sbfx            x5, x0, #1, #0x1f
    //     0xb84418: tbz             w0, #0, #0xb84420
    //     0xb8441c: ldur            x5, [x0, #7]
    // 0xb84420: r0 = LoadInt32Instr(r1)
    //     0xb84420: sbfx            x0, x1, #1, #0x1f
    // 0xb84424: mov             x1, x5
    // 0xb84428: cmp             x1, x0
    // 0xb8442c: b.hs            #0xb845c0
    // 0xb84430: LoadField: r0 = r3->field_f
    //     0xb84430: ldur            w0, [x3, #0xf]
    // 0xb84434: DecompressPointer r0
    //     0xb84434: add             x0, x0, HEAP, lsl #32
    // 0xb84438: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb84438: add             x16, x0, x5, lsl #2
    //     0xb8443c: ldur            w1, [x16, #0xf]
    // 0xb84440: DecompressPointer r1
    //     0xb84440: add             x1, x1, HEAP, lsl #32
    // 0xb84444: mov             x0, x1
    // 0xb84448: mov             x1, x2
    // 0xb8444c: mov             x2, x0
    // 0xb84450: mov             x3, x4
    // 0xb84454: r0 = getGlassThemeSlider()
    //     0xb84454: bl              #0xb845c4  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::getGlassThemeSlider
    // 0xb84458: r1 = Null
    //     0xb84458: mov             x1, NULL
    // 0xb8445c: r2 = 2
    //     0xb8445c: movz            x2, #0x2
    // 0xb84460: stur            x0, [fp, #-0x10]
    // 0xb84464: r0 = AllocateArray()
    //     0xb84464: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb84468: mov             x2, x0
    // 0xb8446c: ldur            x0, [fp, #-0x10]
    // 0xb84470: stur            x2, [fp, #-0x18]
    // 0xb84474: StoreField: r2->field_f = r0
    //     0xb84474: stur            w0, [x2, #0xf]
    // 0xb84478: r1 = <Widget>
    //     0xb84478: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb8447c: r0 = AllocateGrowableArray()
    //     0xb8447c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb84480: mov             x1, x0
    // 0xb84484: ldur            x0, [fp, #-0x18]
    // 0xb84488: stur            x1, [fp, #-0x10]
    // 0xb8448c: StoreField: r1->field_f = r0
    //     0xb8448c: stur            w0, [x1, #0xf]
    // 0xb84490: r0 = 2
    //     0xb84490: movz            x0, #0x2
    // 0xb84494: StoreField: r1->field_b = r0
    //     0xb84494: stur            w0, [x1, #0xb]
    // 0xb84498: ldur            x0, [fp, #-8]
    // 0xb8449c: r17 = 315
    //     0xb8449c: movz            x17, #0x13b
    // 0xb844a0: ldr             w2, [x0, x17]
    // 0xb844a4: DecompressPointer r2
    //     0xb844a4: add             x2, x2, HEAP, lsl #32
    // 0xb844a8: cmp             w2, NULL
    // 0xb844ac: b.ne            #0xb844b8
    // 0xb844b0: mov             x2, x1
    // 0xb844b4: b               #0xb84578
    // 0xb844b8: tbnz            w2, #4, #0xb84574
    // 0xb844bc: r0 = SvgPicture()
    //     0xb844bc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb844c0: mov             x1, x0
    // 0xb844c4: r2 = "assets/images/free-gift-icon.svg"
    //     0xb844c4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xb844c8: ldr             x2, [x2, #0xd40]
    // 0xb844cc: stur            x0, [fp, #-8]
    // 0xb844d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb844d0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb844d4: r0 = SvgPicture.asset()
    //     0xb844d4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb844d8: r0 = Padding()
    //     0xb844d8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb844dc: mov             x2, x0
    // 0xb844e0: r0 = Instance_EdgeInsets
    //     0xb844e0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xb844e4: ldr             x0, [x0, #0xd48]
    // 0xb844e8: stur            x2, [fp, #-0x18]
    // 0xb844ec: StoreField: r2->field_f = r0
    //     0xb844ec: stur            w0, [x2, #0xf]
    // 0xb844f0: ldur            x0, [fp, #-8]
    // 0xb844f4: StoreField: r2->field_b = r0
    //     0xb844f4: stur            w0, [x2, #0xb]
    // 0xb844f8: ldur            x0, [fp, #-0x10]
    // 0xb844fc: LoadField: r1 = r0->field_b
    //     0xb844fc: ldur            w1, [x0, #0xb]
    // 0xb84500: LoadField: r3 = r0->field_f
    //     0xb84500: ldur            w3, [x0, #0xf]
    // 0xb84504: DecompressPointer r3
    //     0xb84504: add             x3, x3, HEAP, lsl #32
    // 0xb84508: LoadField: r4 = r3->field_b
    //     0xb84508: ldur            w4, [x3, #0xb]
    // 0xb8450c: r3 = LoadInt32Instr(r1)
    //     0xb8450c: sbfx            x3, x1, #1, #0x1f
    // 0xb84510: stur            x3, [fp, #-0x20]
    // 0xb84514: r1 = LoadInt32Instr(r4)
    //     0xb84514: sbfx            x1, x4, #1, #0x1f
    // 0xb84518: cmp             x3, x1
    // 0xb8451c: b.ne            #0xb84528
    // 0xb84520: mov             x1, x0
    // 0xb84524: r0 = _growToNextCapacity()
    //     0xb84524: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb84528: ldur            x2, [fp, #-0x10]
    // 0xb8452c: ldur            x3, [fp, #-0x20]
    // 0xb84530: add             x0, x3, #1
    // 0xb84534: lsl             x1, x0, #1
    // 0xb84538: StoreField: r2->field_b = r1
    //     0xb84538: stur            w1, [x2, #0xb]
    // 0xb8453c: LoadField: r1 = r2->field_f
    //     0xb8453c: ldur            w1, [x2, #0xf]
    // 0xb84540: DecompressPointer r1
    //     0xb84540: add             x1, x1, HEAP, lsl #32
    // 0xb84544: ldur            x0, [fp, #-0x18]
    // 0xb84548: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb84548: add             x25, x1, x3, lsl #2
    //     0xb8454c: add             x25, x25, #0xf
    //     0xb84550: str             w0, [x25]
    //     0xb84554: tbz             w0, #0, #0xb84570
    //     0xb84558: ldurb           w16, [x1, #-1]
    //     0xb8455c: ldurb           w17, [x0, #-1]
    //     0xb84560: and             x16, x17, x16, lsr #2
    //     0xb84564: tst             x16, HEAP, lsr #32
    //     0xb84568: b.eq            #0xb84570
    //     0xb8456c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb84570: b               #0xb84578
    // 0xb84574: mov             x2, x1
    // 0xb84578: r0 = Stack()
    //     0xb84578: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb8457c: r1 = Instance_Alignment
    //     0xb8457c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb84580: ldr             x1, [x1, #0x950]
    // 0xb84584: StoreField: r0->field_f = r1
    //     0xb84584: stur            w1, [x0, #0xf]
    // 0xb84588: r1 = Instance_StackFit
    //     0xb84588: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb8458c: ldr             x1, [x1, #0xfa8]
    // 0xb84590: ArrayStore: r0[0] = r1  ; List_4
    //     0xb84590: stur            w1, [x0, #0x17]
    // 0xb84594: r1 = Instance_Clip
    //     0xb84594: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb84598: ldr             x1, [x1, #0x7e0]
    // 0xb8459c: StoreField: r0->field_1b = r1
    //     0xb8459c: stur            w1, [x0, #0x1b]
    // 0xb845a0: ldur            x1, [fp, #-0x10]
    // 0xb845a4: StoreField: r0->field_b = r1
    //     0xb845a4: stur            w1, [x0, #0xb]
    // 0xb845a8: LeaveFrame
    //     0xb845a8: mov             SP, fp
    //     0xb845ac: ldp             fp, lr, [SP], #0x10
    // 0xb845b0: ret
    //     0xb845b0: ret             
    // 0xb845b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb845b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb845b8: b               #0xb843d0
    // 0xb845bc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb845bc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb845c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb845c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getGlassThemeSlider(/* No info */) {
    // ** addr: 0xb845c4, size: 0x528
    // 0xb845c4: EnterFrame
    //     0xb845c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb845c8: mov             fp, SP
    // 0xb845cc: AllocStack(0x60)
    //     0xb845cc: sub             SP, SP, #0x60
    // 0xb845d0: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xb845d0: mov             x0, x2
    //     0xb845d4: stur            x2, [fp, #-0x10]
    //     0xb845d8: mov             x2, x3
    //     0xb845dc: stur            x1, [fp, #-8]
    //     0xb845e0: stur            x3, [fp, #-0x18]
    // 0xb845e4: CheckStackOverflow
    //     0xb845e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb845e8: cmp             SP, x16
    //     0xb845ec: b.ls            #0xb84adc
    // 0xb845f0: r1 = 2
    //     0xb845f0: movz            x1, #0x2
    // 0xb845f4: r0 = AllocateContext()
    //     0xb845f4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb845f8: ldur            x1, [fp, #-8]
    // 0xb845fc: stur            x0, [fp, #-0x20]
    // 0xb84600: StoreField: r0->field_f = r1
    //     0xb84600: stur            w1, [x0, #0xf]
    // 0xb84604: ldur            x2, [fp, #-0x18]
    // 0xb84608: StoreField: r0->field_13 = r2
    //     0xb84608: stur            w2, [x0, #0x13]
    // 0xb8460c: r0 = Radius()
    //     0xb8460c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb84610: d0 = 10.000000
    //     0xb84610: fmov            d0, #10.00000000
    // 0xb84614: stur            x0, [fp, #-0x28]
    // 0xb84618: StoreField: r0->field_7 = d0
    //     0xb84618: stur            d0, [x0, #7]
    // 0xb8461c: StoreField: r0->field_f = d0
    //     0xb8461c: stur            d0, [x0, #0xf]
    // 0xb84620: r0 = BorderRadius()
    //     0xb84620: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb84624: mov             x1, x0
    // 0xb84628: ldur            x0, [fp, #-0x28]
    // 0xb8462c: stur            x1, [fp, #-0x30]
    // 0xb84630: StoreField: r1->field_7 = r0
    //     0xb84630: stur            w0, [x1, #7]
    // 0xb84634: StoreField: r1->field_b = r0
    //     0xb84634: stur            w0, [x1, #0xb]
    // 0xb84638: StoreField: r1->field_f = r0
    //     0xb84638: stur            w0, [x1, #0xf]
    // 0xb8463c: StoreField: r1->field_13 = r0
    //     0xb8463c: stur            w0, [x1, #0x13]
    // 0xb84640: r0 = RoundedRectangleBorder()
    //     0xb84640: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb84644: mov             x3, x0
    // 0xb84648: ldur            x0, [fp, #-0x30]
    // 0xb8464c: stur            x3, [fp, #-0x28]
    // 0xb84650: StoreField: r3->field_b = r0
    //     0xb84650: stur            w0, [x3, #0xb]
    // 0xb84654: r0 = Instance_BorderSide
    //     0xb84654: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb84658: ldr             x0, [x0, #0xe20]
    // 0xb8465c: StoreField: r3->field_7 = r0
    //     0xb8465c: stur            w0, [x3, #7]
    // 0xb84660: ldur            x0, [fp, #-0x10]
    // 0xb84664: cmp             w0, NULL
    // 0xb84668: b.ne            #0xb84674
    // 0xb8466c: r0 = Null
    //     0xb8466c: mov             x0, NULL
    // 0xb84670: b               #0xb84680
    // 0xb84674: LoadField: r1 = r0->field_b
    //     0xb84674: ldur            w1, [x0, #0xb]
    // 0xb84678: DecompressPointer r1
    //     0xb84678: add             x1, x1, HEAP, lsl #32
    // 0xb8467c: mov             x0, x1
    // 0xb84680: cmp             w0, NULL
    // 0xb84684: b.ne            #0xb84690
    // 0xb84688: r4 = ""
    //     0xb84688: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb8468c: b               #0xb84694
    // 0xb84690: mov             x4, x0
    // 0xb84694: ldur            x0, [fp, #-0x18]
    // 0xb84698: stur            x4, [fp, #-0x10]
    // 0xb8469c: r1 = Function '<anonymous closure>':.
    //     0xb8469c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61da8] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb846a0: ldr             x1, [x1, #0xda8]
    // 0xb846a4: r2 = Null
    //     0xb846a4: mov             x2, NULL
    // 0xb846a8: r0 = AllocateClosure()
    //     0xb846a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb846ac: r1 = Function '<anonymous closure>':.
    //     0xb846ac: add             x1, PP, #0x61, lsl #12  ; [pp+0x61db0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb846b0: ldr             x1, [x1, #0xdb0]
    // 0xb846b4: r2 = Null
    //     0xb846b4: mov             x2, NULL
    // 0xb846b8: stur            x0, [fp, #-0x30]
    // 0xb846bc: r0 = AllocateClosure()
    //     0xb846bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb846c0: stur            x0, [fp, #-0x38]
    // 0xb846c4: r0 = CachedNetworkImage()
    //     0xb846c4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb846c8: stur            x0, [fp, #-0x40]
    // 0xb846cc: r16 = Instance_BoxFit
    //     0xb846cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb846d0: ldr             x16, [x16, #0x118]
    // 0xb846d4: ldur            lr, [fp, #-0x30]
    // 0xb846d8: stp             lr, x16, [SP, #8]
    // 0xb846dc: ldur            x16, [fp, #-0x38]
    // 0xb846e0: str             x16, [SP]
    // 0xb846e4: mov             x1, x0
    // 0xb846e8: ldur            x2, [fp, #-0x10]
    // 0xb846ec: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb846ec: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb846f0: ldr             x4, [x4, #0x638]
    // 0xb846f4: r0 = CachedNetworkImage()
    //     0xb846f4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb846f8: r0 = Card()
    //     0xb846f8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb846fc: mov             x1, x0
    // 0xb84700: r0 = Instance_Color
    //     0xb84700: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb84704: ldr             x0, [x0, #0xf88]
    // 0xb84708: stur            x1, [fp, #-0x10]
    // 0xb8470c: StoreField: r1->field_b = r0
    //     0xb8470c: stur            w0, [x1, #0xb]
    // 0xb84710: r0 = 0.000000
    //     0xb84710: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb84714: ArrayStore: r1[0] = r0  ; List_4
    //     0xb84714: stur            w0, [x1, #0x17]
    // 0xb84718: ldur            x0, [fp, #-0x28]
    // 0xb8471c: StoreField: r1->field_1b = r0
    //     0xb8471c: stur            w0, [x1, #0x1b]
    // 0xb84720: r0 = true
    //     0xb84720: add             x0, NULL, #0x20  ; true
    // 0xb84724: StoreField: r1->field_1f = r0
    //     0xb84724: stur            w0, [x1, #0x1f]
    // 0xb84728: r2 = Instance_EdgeInsets
    //     0xb84728: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb8472c: StoreField: r1->field_27 = r2
    //     0xb8472c: stur            w2, [x1, #0x27]
    // 0xb84730: r2 = Instance_Clip
    //     0xb84730: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb84734: ldr             x2, [x2, #0xb50]
    // 0xb84738: StoreField: r1->field_23 = r2
    //     0xb84738: stur            w2, [x1, #0x23]
    // 0xb8473c: ldur            x2, [fp, #-0x40]
    // 0xb84740: StoreField: r1->field_2f = r2
    //     0xb84740: stur            w2, [x1, #0x2f]
    // 0xb84744: StoreField: r1->field_2b = r0
    //     0xb84744: stur            w0, [x1, #0x2b]
    // 0xb84748: r2 = Instance__CardVariant
    //     0xb84748: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb8474c: ldr             x2, [x2, #0xa68]
    // 0xb84750: StoreField: r1->field_33 = r2
    //     0xb84750: stur            w2, [x1, #0x33]
    // 0xb84754: r0 = Center()
    //     0xb84754: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb84758: mov             x3, x0
    // 0xb8475c: r0 = Instance_Alignment
    //     0xb8475c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb84760: ldr             x0, [x0, #0xb10]
    // 0xb84764: stur            x3, [fp, #-0x28]
    // 0xb84768: StoreField: r3->field_f = r0
    //     0xb84768: stur            w0, [x3, #0xf]
    // 0xb8476c: ldur            x0, [fp, #-0x10]
    // 0xb84770: StoreField: r3->field_b = r0
    //     0xb84770: stur            w0, [x3, #0xb]
    // 0xb84774: r1 = Null
    //     0xb84774: mov             x1, NULL
    // 0xb84778: r2 = 2
    //     0xb84778: movz            x2, #0x2
    // 0xb8477c: r0 = AllocateArray()
    //     0xb8477c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb84780: mov             x2, x0
    // 0xb84784: ldur            x0, [fp, #-0x28]
    // 0xb84788: stur            x2, [fp, #-0x10]
    // 0xb8478c: StoreField: r2->field_f = r0
    //     0xb8478c: stur            w0, [x2, #0xf]
    // 0xb84790: r1 = <Widget>
    //     0xb84790: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb84794: r0 = AllocateGrowableArray()
    //     0xb84794: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb84798: mov             x3, x0
    // 0xb8479c: ldur            x0, [fp, #-0x10]
    // 0xb847a0: stur            x3, [fp, #-0x28]
    // 0xb847a4: StoreField: r3->field_f = r0
    //     0xb847a4: stur            w0, [x3, #0xf]
    // 0xb847a8: r0 = 2
    //     0xb847a8: movz            x0, #0x2
    // 0xb847ac: StoreField: r3->field_b = r0
    //     0xb847ac: stur            w0, [x3, #0xb]
    // 0xb847b0: ldur            x0, [fp, #-0x18]
    // 0xb847b4: cmp             w0, NULL
    // 0xb847b8: b.ne            #0xb847c4
    // 0xb847bc: r1 = Null
    //     0xb847bc: mov             x1, NULL
    // 0xb847c0: b               #0xb847f4
    // 0xb847c4: r17 = 295
    //     0xb847c4: movz            x17, #0x127
    // 0xb847c8: ldr             w1, [x0, x17]
    // 0xb847cc: DecompressPointer r1
    //     0xb847cc: add             x1, x1, HEAP, lsl #32
    // 0xb847d0: cmp             w1, NULL
    // 0xb847d4: b.ne            #0xb847e0
    // 0xb847d8: r1 = Null
    //     0xb847d8: mov             x1, NULL
    // 0xb847dc: b               #0xb847f4
    // 0xb847e0: LoadField: r2 = r1->field_7
    //     0xb847e0: ldur            w2, [x1, #7]
    // 0xb847e4: cbnz            w2, #0xb847f0
    // 0xb847e8: r1 = false
    //     0xb847e8: add             x1, NULL, #0x30  ; false
    // 0xb847ec: b               #0xb847f4
    // 0xb847f0: r1 = true
    //     0xb847f0: add             x1, NULL, #0x20  ; true
    // 0xb847f4: cmp             w1, NULL
    // 0xb847f8: b.eq            #0xb84894
    // 0xb847fc: tbnz            w1, #4, #0xb84894
    // 0xb84800: cmp             w0, NULL
    // 0xb84804: b.eq            #0xb84ae4
    // 0xb84808: ldur            x1, [fp, #-8]
    // 0xb8480c: mov             x2, x0
    // 0xb84810: r0 = _buildDiscountBadge()
    //     0xb84810: bl              #0xb0b75c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildDiscountBadge
    // 0xb84814: mov             x2, x0
    // 0xb84818: ldur            x0, [fp, #-0x28]
    // 0xb8481c: stur            x2, [fp, #-0x10]
    // 0xb84820: LoadField: r1 = r0->field_b
    //     0xb84820: ldur            w1, [x0, #0xb]
    // 0xb84824: LoadField: r3 = r0->field_f
    //     0xb84824: ldur            w3, [x0, #0xf]
    // 0xb84828: DecompressPointer r3
    //     0xb84828: add             x3, x3, HEAP, lsl #32
    // 0xb8482c: LoadField: r4 = r3->field_b
    //     0xb8482c: ldur            w4, [x3, #0xb]
    // 0xb84830: r3 = LoadInt32Instr(r1)
    //     0xb84830: sbfx            x3, x1, #1, #0x1f
    // 0xb84834: stur            x3, [fp, #-0x48]
    // 0xb84838: r1 = LoadInt32Instr(r4)
    //     0xb84838: sbfx            x1, x4, #1, #0x1f
    // 0xb8483c: cmp             x3, x1
    // 0xb84840: b.ne            #0xb8484c
    // 0xb84844: mov             x1, x0
    // 0xb84848: r0 = _growToNextCapacity()
    //     0xb84848: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8484c: ldur            x3, [fp, #-0x28]
    // 0xb84850: ldur            x2, [fp, #-0x48]
    // 0xb84854: add             x0, x2, #1
    // 0xb84858: lsl             x1, x0, #1
    // 0xb8485c: StoreField: r3->field_b = r1
    //     0xb8485c: stur            w1, [x3, #0xb]
    // 0xb84860: LoadField: r1 = r3->field_f
    //     0xb84860: ldur            w1, [x3, #0xf]
    // 0xb84864: DecompressPointer r1
    //     0xb84864: add             x1, x1, HEAP, lsl #32
    // 0xb84868: ldur            x0, [fp, #-0x10]
    // 0xb8486c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb8486c: add             x25, x1, x2, lsl #2
    //     0xb84870: add             x25, x25, #0xf
    //     0xb84874: str             w0, [x25]
    //     0xb84878: tbz             w0, #0, #0xb84894
    //     0xb8487c: ldurb           w16, [x1, #-1]
    //     0xb84880: ldurb           w17, [x0, #-1]
    //     0xb84884: and             x16, x17, x16, lsr #2
    //     0xb84888: tst             x16, HEAP, lsr #32
    //     0xb8488c: b.eq            #0xb84894
    //     0xb84890: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb84894: ldur            x0, [fp, #-0x18]
    // 0xb84898: cmp             w0, NULL
    // 0xb8489c: b.ne            #0xb848a8
    // 0xb848a0: r1 = Null
    //     0xb848a0: mov             x1, NULL
    // 0xb848a4: b               #0xb848d8
    // 0xb848a8: r17 = 311
    //     0xb848a8: movz            x17, #0x137
    // 0xb848ac: ldr             w1, [x0, x17]
    // 0xb848b0: DecompressPointer r1
    //     0xb848b0: add             x1, x1, HEAP, lsl #32
    // 0xb848b4: cmp             w1, NULL
    // 0xb848b8: b.ne            #0xb848c4
    // 0xb848bc: r1 = Null
    //     0xb848bc: mov             x1, NULL
    // 0xb848c0: b               #0xb848d8
    // 0xb848c4: LoadField: r2 = r1->field_7
    //     0xb848c4: ldur            w2, [x1, #7]
    // 0xb848c8: cbnz            w2, #0xb848d4
    // 0xb848cc: r1 = false
    //     0xb848cc: add             x1, NULL, #0x30  ; false
    // 0xb848d0: b               #0xb848d8
    // 0xb848d4: r1 = true
    //     0xb848d4: add             x1, NULL, #0x20  ; true
    // 0xb848d8: cmp             w1, NULL
    // 0xb848dc: b.ne            #0xb848e8
    // 0xb848e0: mov             x2, x3
    // 0xb848e4: b               #0xb84988
    // 0xb848e8: tbnz            w1, #4, #0xb84984
    // 0xb848ec: cmp             w0, NULL
    // 0xb848f0: b.eq            #0xb84ae8
    // 0xb848f4: ldur            x1, [fp, #-8]
    // 0xb848f8: mov             x2, x0
    // 0xb848fc: r0 = _buildStockAlert()
    //     0xb848fc: bl              #0xb84cd0  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildStockAlert
    // 0xb84900: mov             x2, x0
    // 0xb84904: ldur            x0, [fp, #-0x28]
    // 0xb84908: stur            x2, [fp, #-0x10]
    // 0xb8490c: LoadField: r1 = r0->field_b
    //     0xb8490c: ldur            w1, [x0, #0xb]
    // 0xb84910: LoadField: r3 = r0->field_f
    //     0xb84910: ldur            w3, [x0, #0xf]
    // 0xb84914: DecompressPointer r3
    //     0xb84914: add             x3, x3, HEAP, lsl #32
    // 0xb84918: LoadField: r4 = r3->field_b
    //     0xb84918: ldur            w4, [x3, #0xb]
    // 0xb8491c: r3 = LoadInt32Instr(r1)
    //     0xb8491c: sbfx            x3, x1, #1, #0x1f
    // 0xb84920: stur            x3, [fp, #-0x48]
    // 0xb84924: r1 = LoadInt32Instr(r4)
    //     0xb84924: sbfx            x1, x4, #1, #0x1f
    // 0xb84928: cmp             x3, x1
    // 0xb8492c: b.ne            #0xb84938
    // 0xb84930: mov             x1, x0
    // 0xb84934: r0 = _growToNextCapacity()
    //     0xb84934: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb84938: ldur            x2, [fp, #-0x28]
    // 0xb8493c: ldur            x3, [fp, #-0x48]
    // 0xb84940: add             x0, x3, #1
    // 0xb84944: lsl             x1, x0, #1
    // 0xb84948: StoreField: r2->field_b = r1
    //     0xb84948: stur            w1, [x2, #0xb]
    // 0xb8494c: LoadField: r1 = r2->field_f
    //     0xb8494c: ldur            w1, [x2, #0xf]
    // 0xb84950: DecompressPointer r1
    //     0xb84950: add             x1, x1, HEAP, lsl #32
    // 0xb84954: ldur            x0, [fp, #-0x10]
    // 0xb84958: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb84958: add             x25, x1, x3, lsl #2
    //     0xb8495c: add             x25, x25, #0xf
    //     0xb84960: str             w0, [x25]
    //     0xb84964: tbz             w0, #0, #0xb84980
    //     0xb84968: ldurb           w16, [x1, #-1]
    //     0xb8496c: ldurb           w17, [x0, #-1]
    //     0xb84970: and             x16, x17, x16, lsr #2
    //     0xb84974: tst             x16, HEAP, lsr #32
    //     0xb84978: b.eq            #0xb84980
    //     0xb8497c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb84980: b               #0xb84988
    // 0xb84984: mov             x2, x3
    // 0xb84988: ldur            x0, [fp, #-0x18]
    // 0xb8498c: cmp             w0, NULL
    // 0xb84990: b.ne            #0xb8499c
    // 0xb84994: r0 = Null
    //     0xb84994: mov             x0, NULL
    // 0xb84998: b               #0xb849ac
    // 0xb8499c: r17 = 263
    //     0xb8499c: movz            x17, #0x107
    // 0xb849a0: ldr             w1, [x0, x17]
    // 0xb849a4: DecompressPointer r1
    //     0xb849a4: add             x1, x1, HEAP, lsl #32
    // 0xb849a8: mov             x0, x1
    // 0xb849ac: cmp             w0, NULL
    // 0xb849b0: b.eq            #0xb84a40
    // 0xb849b4: tbnz            w0, #4, #0xb84a40
    // 0xb849b8: ldur            x1, [fp, #-8]
    // 0xb849bc: r0 = _buildCustomizationBadge()
    //     0xb849bc: bl              #0xb84aec  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::_buildCustomizationBadge
    // 0xb849c0: mov             x2, x0
    // 0xb849c4: ldur            x0, [fp, #-0x28]
    // 0xb849c8: stur            x2, [fp, #-8]
    // 0xb849cc: LoadField: r1 = r0->field_b
    //     0xb849cc: ldur            w1, [x0, #0xb]
    // 0xb849d0: LoadField: r3 = r0->field_f
    //     0xb849d0: ldur            w3, [x0, #0xf]
    // 0xb849d4: DecompressPointer r3
    //     0xb849d4: add             x3, x3, HEAP, lsl #32
    // 0xb849d8: LoadField: r4 = r3->field_b
    //     0xb849d8: ldur            w4, [x3, #0xb]
    // 0xb849dc: r3 = LoadInt32Instr(r1)
    //     0xb849dc: sbfx            x3, x1, #1, #0x1f
    // 0xb849e0: stur            x3, [fp, #-0x48]
    // 0xb849e4: r1 = LoadInt32Instr(r4)
    //     0xb849e4: sbfx            x1, x4, #1, #0x1f
    // 0xb849e8: cmp             x3, x1
    // 0xb849ec: b.ne            #0xb849f8
    // 0xb849f0: mov             x1, x0
    // 0xb849f4: r0 = _growToNextCapacity()
    //     0xb849f4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb849f8: ldur            x2, [fp, #-0x28]
    // 0xb849fc: ldur            x3, [fp, #-0x48]
    // 0xb84a00: add             x0, x3, #1
    // 0xb84a04: lsl             x1, x0, #1
    // 0xb84a08: StoreField: r2->field_b = r1
    //     0xb84a08: stur            w1, [x2, #0xb]
    // 0xb84a0c: LoadField: r1 = r2->field_f
    //     0xb84a0c: ldur            w1, [x2, #0xf]
    // 0xb84a10: DecompressPointer r1
    //     0xb84a10: add             x1, x1, HEAP, lsl #32
    // 0xb84a14: ldur            x0, [fp, #-8]
    // 0xb84a18: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb84a18: add             x25, x1, x3, lsl #2
    //     0xb84a1c: add             x25, x25, #0xf
    //     0xb84a20: str             w0, [x25]
    //     0xb84a24: tbz             w0, #0, #0xb84a40
    //     0xb84a28: ldurb           w16, [x1, #-1]
    //     0xb84a2c: ldurb           w17, [x0, #-1]
    //     0xb84a30: and             x16, x17, x16, lsr #2
    //     0xb84a34: tst             x16, HEAP, lsr #32
    //     0xb84a38: b.eq            #0xb84a40
    //     0xb84a3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb84a40: r0 = Stack()
    //     0xb84a40: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb84a44: mov             x1, x0
    // 0xb84a48: r0 = Instance_Alignment
    //     0xb84a48: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb84a4c: ldr             x0, [x0, #0x5b8]
    // 0xb84a50: stur            x1, [fp, #-8]
    // 0xb84a54: StoreField: r1->field_f = r0
    //     0xb84a54: stur            w0, [x1, #0xf]
    // 0xb84a58: r0 = Instance_StackFit
    //     0xb84a58: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb84a5c: ldr             x0, [x0, #0xfa8]
    // 0xb84a60: ArrayStore: r1[0] = r0  ; List_4
    //     0xb84a60: stur            w0, [x1, #0x17]
    // 0xb84a64: r0 = Instance_Clip
    //     0xb84a64: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb84a68: ldr             x0, [x0, #0x7e0]
    // 0xb84a6c: StoreField: r1->field_1b = r0
    //     0xb84a6c: stur            w0, [x1, #0x1b]
    // 0xb84a70: ldur            x0, [fp, #-0x28]
    // 0xb84a74: StoreField: r1->field_b = r0
    //     0xb84a74: stur            w0, [x1, #0xb]
    // 0xb84a78: r0 = InkWell()
    //     0xb84a78: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb84a7c: mov             x3, x0
    // 0xb84a80: ldur            x0, [fp, #-8]
    // 0xb84a84: stur            x3, [fp, #-0x10]
    // 0xb84a88: StoreField: r3->field_b = r0
    //     0xb84a88: stur            w0, [x3, #0xb]
    // 0xb84a8c: ldur            x2, [fp, #-0x20]
    // 0xb84a90: r1 = Function '<anonymous closure>':.
    //     0xb84a90: add             x1, PP, #0x61, lsl #12  ; [pp+0x61db8] AnonymousClosure: (0xb84f38), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] _ProductGridItemState::getGlassThemeSlider (0xb845c4)
    //     0xb84a94: ldr             x1, [x1, #0xdb8]
    // 0xb84a98: r0 = AllocateClosure()
    //     0xb84a98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb84a9c: mov             x1, x0
    // 0xb84aa0: ldur            x0, [fp, #-0x10]
    // 0xb84aa4: StoreField: r0->field_f = r1
    //     0xb84aa4: stur            w1, [x0, #0xf]
    // 0xb84aa8: r1 = true
    //     0xb84aa8: add             x1, NULL, #0x20  ; true
    // 0xb84aac: StoreField: r0->field_43 = r1
    //     0xb84aac: stur            w1, [x0, #0x43]
    // 0xb84ab0: r2 = Instance_BoxShape
    //     0xb84ab0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb84ab4: ldr             x2, [x2, #0x80]
    // 0xb84ab8: StoreField: r0->field_47 = r2
    //     0xb84ab8: stur            w2, [x0, #0x47]
    // 0xb84abc: StoreField: r0->field_6f = r1
    //     0xb84abc: stur            w1, [x0, #0x6f]
    // 0xb84ac0: r2 = false
    //     0xb84ac0: add             x2, NULL, #0x30  ; false
    // 0xb84ac4: StoreField: r0->field_73 = r2
    //     0xb84ac4: stur            w2, [x0, #0x73]
    // 0xb84ac8: StoreField: r0->field_83 = r1
    //     0xb84ac8: stur            w1, [x0, #0x83]
    // 0xb84acc: StoreField: r0->field_7b = r2
    //     0xb84acc: stur            w2, [x0, #0x7b]
    // 0xb84ad0: LeaveFrame
    //     0xb84ad0: mov             SP, fp
    //     0xb84ad4: ldp             fp, lr, [SP], #0x10
    // 0xb84ad8: ret
    //     0xb84ad8: ret             
    // 0xb84adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84adc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84ae0: b               #0xb845f0
    // 0xb84ae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84ae4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84ae8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84ae8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationBadge(/* No info */) {
    // ** addr: 0xb84aec, size: 0x1e4
    // 0xb84aec: EnterFrame
    //     0xb84aec: stp             fp, lr, [SP, #-0x10]!
    //     0xb84af0: mov             fp, SP
    // 0xb84af4: AllocStack(0x38)
    //     0xb84af4: sub             SP, SP, #0x38
    // 0xb84af8: SetupParameters(_ProductGridItemState this /* r1 => r0, fp-0x8 */)
    //     0xb84af8: mov             x0, x1
    //     0xb84afc: stur            x1, [fp, #-8]
    // 0xb84b00: CheckStackOverflow
    //     0xb84b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84b04: cmp             SP, x16
    //     0xb84b08: b.ls            #0xb84cc0
    // 0xb84b0c: LoadField: r1 = r0->field_f
    //     0xb84b0c: ldur            w1, [x0, #0xf]
    // 0xb84b10: DecompressPointer r1
    //     0xb84b10: add             x1, x1, HEAP, lsl #32
    // 0xb84b14: cmp             w1, NULL
    // 0xb84b18: b.eq            #0xb84cc8
    // 0xb84b1c: r0 = of()
    //     0xb84b1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb84b20: r17 = 307
    //     0xb84b20: movz            x17, #0x133
    // 0xb84b24: ldr             w1, [x0, x17]
    // 0xb84b28: DecompressPointer r1
    //     0xb84b28: add             x1, x1, HEAP, lsl #32
    // 0xb84b2c: stur            x1, [fp, #-0x10]
    // 0xb84b30: r0 = Radius()
    //     0xb84b30: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb84b34: d0 = 5.000000
    //     0xb84b34: fmov            d0, #5.00000000
    // 0xb84b38: stur            x0, [fp, #-0x18]
    // 0xb84b3c: StoreField: r0->field_7 = d0
    //     0xb84b3c: stur            d0, [x0, #7]
    // 0xb84b40: StoreField: r0->field_f = d0
    //     0xb84b40: stur            d0, [x0, #0xf]
    // 0xb84b44: r0 = BorderRadius()
    //     0xb84b44: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb84b48: mov             x1, x0
    // 0xb84b4c: ldur            x0, [fp, #-0x18]
    // 0xb84b50: stur            x1, [fp, #-0x20]
    // 0xb84b54: StoreField: r1->field_7 = r0
    //     0xb84b54: stur            w0, [x1, #7]
    // 0xb84b58: StoreField: r1->field_b = r0
    //     0xb84b58: stur            w0, [x1, #0xb]
    // 0xb84b5c: StoreField: r1->field_f = r0
    //     0xb84b5c: stur            w0, [x1, #0xf]
    // 0xb84b60: StoreField: r1->field_13 = r0
    //     0xb84b60: stur            w0, [x1, #0x13]
    // 0xb84b64: r0 = RoundedRectangleBorder()
    //     0xb84b64: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb84b68: mov             x1, x0
    // 0xb84b6c: ldur            x0, [fp, #-0x20]
    // 0xb84b70: StoreField: r1->field_b = r0
    //     0xb84b70: stur            w0, [x1, #0xb]
    // 0xb84b74: r0 = Instance_BorderSide
    //     0xb84b74: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb84b78: ldr             x0, [x0, #0xe20]
    // 0xb84b7c: StoreField: r1->field_7 = r0
    //     0xb84b7c: stur            w0, [x1, #7]
    // 0xb84b80: r16 = <RoundedRectangleBorder>
    //     0xb84b80: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb84b84: ldr             x16, [x16, #0xf78]
    // 0xb84b88: stp             x1, x16, [SP]
    // 0xb84b8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb84b8c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb84b90: r0 = all()
    //     0xb84b90: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb84b94: stur            x0, [fp, #-0x18]
    // 0xb84b98: r0 = ButtonStyle()
    //     0xb84b98: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb84b9c: mov             x2, x0
    // 0xb84ba0: ldur            x0, [fp, #-0x18]
    // 0xb84ba4: stur            x2, [fp, #-0x20]
    // 0xb84ba8: StoreField: r2->field_43 = r0
    //     0xb84ba8: stur            w0, [x2, #0x43]
    // 0xb84bac: ldur            x0, [fp, #-8]
    // 0xb84bb0: LoadField: r1 = r0->field_f
    //     0xb84bb0: ldur            w1, [x0, #0xf]
    // 0xb84bb4: DecompressPointer r1
    //     0xb84bb4: add             x1, x1, HEAP, lsl #32
    // 0xb84bb8: cmp             w1, NULL
    // 0xb84bbc: b.eq            #0xb84ccc
    // 0xb84bc0: r0 = of()
    //     0xb84bc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb84bc4: LoadField: r1 = r0->field_87
    //     0xb84bc4: ldur            w1, [x0, #0x87]
    // 0xb84bc8: DecompressPointer r1
    //     0xb84bc8: add             x1, x1, HEAP, lsl #32
    // 0xb84bcc: LoadField: r0 = r1->field_2b
    //     0xb84bcc: ldur            w0, [x1, #0x2b]
    // 0xb84bd0: DecompressPointer r0
    //     0xb84bd0: add             x0, x0, HEAP, lsl #32
    // 0xb84bd4: r16 = 12.000000
    //     0xb84bd4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb84bd8: ldr             x16, [x16, #0x9e8]
    // 0xb84bdc: r30 = Instance_Color
    //     0xb84bdc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb84be0: stp             lr, x16, [SP]
    // 0xb84be4: mov             x1, x0
    // 0xb84be8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb84be8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb84bec: ldr             x4, [x4, #0xaa0]
    // 0xb84bf0: r0 = copyWith()
    //     0xb84bf0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb84bf4: stur            x0, [fp, #-8]
    // 0xb84bf8: r0 = Text()
    //     0xb84bf8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb84bfc: mov             x3, x0
    // 0xb84c00: r0 = "Customisable"
    //     0xb84c00: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb84c04: ldr             x0, [x0, #0x970]
    // 0xb84c08: stur            x3, [fp, #-0x18]
    // 0xb84c0c: StoreField: r3->field_b = r0
    //     0xb84c0c: stur            w0, [x3, #0xb]
    // 0xb84c10: ldur            x0, [fp, #-8]
    // 0xb84c14: StoreField: r3->field_13 = r0
    //     0xb84c14: stur            w0, [x3, #0x13]
    // 0xb84c18: r1 = Function '<anonymous closure>':.
    //     0xb84c18: add             x1, PP, #0x61, lsl #12  ; [pp+0x61dd0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb84c1c: ldr             x1, [x1, #0xdd0]
    // 0xb84c20: r2 = Null
    //     0xb84c20: mov             x2, NULL
    // 0xb84c24: r0 = AllocateClosure()
    //     0xb84c24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb84c28: stur            x0, [fp, #-8]
    // 0xb84c2c: r0 = TextButton()
    //     0xb84c2c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb84c30: mov             x1, x0
    // 0xb84c34: ldur            x0, [fp, #-8]
    // 0xb84c38: stur            x1, [fp, #-0x28]
    // 0xb84c3c: StoreField: r1->field_b = r0
    //     0xb84c3c: stur            w0, [x1, #0xb]
    // 0xb84c40: ldur            x0, [fp, #-0x20]
    // 0xb84c44: StoreField: r1->field_1b = r0
    //     0xb84c44: stur            w0, [x1, #0x1b]
    // 0xb84c48: r0 = false
    //     0xb84c48: add             x0, NULL, #0x30  ; false
    // 0xb84c4c: StoreField: r1->field_27 = r0
    //     0xb84c4c: stur            w0, [x1, #0x27]
    // 0xb84c50: r0 = true
    //     0xb84c50: add             x0, NULL, #0x20  ; true
    // 0xb84c54: StoreField: r1->field_2f = r0
    //     0xb84c54: stur            w0, [x1, #0x2f]
    // 0xb84c58: ldur            x0, [fp, #-0x18]
    // 0xb84c5c: StoreField: r1->field_37 = r0
    //     0xb84c5c: stur            w0, [x1, #0x37]
    // 0xb84c60: r0 = TextButtonTheme()
    //     0xb84c60: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb84c64: mov             x1, x0
    // 0xb84c68: ldur            x0, [fp, #-0x10]
    // 0xb84c6c: stur            x1, [fp, #-8]
    // 0xb84c70: StoreField: r1->field_f = r0
    //     0xb84c70: stur            w0, [x1, #0xf]
    // 0xb84c74: ldur            x0, [fp, #-0x28]
    // 0xb84c78: StoreField: r1->field_b = r0
    //     0xb84c78: stur            w0, [x1, #0xb]
    // 0xb84c7c: r0 = SizedBox()
    //     0xb84c7c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb84c80: mov             x1, x0
    // 0xb84c84: r0 = 30.000000
    //     0xb84c84: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb84c88: ldr             x0, [x0, #0x768]
    // 0xb84c8c: stur            x1, [fp, #-0x10]
    // 0xb84c90: StoreField: r1->field_13 = r0
    //     0xb84c90: stur            w0, [x1, #0x13]
    // 0xb84c94: ldur            x0, [fp, #-8]
    // 0xb84c98: StoreField: r1->field_b = r0
    //     0xb84c98: stur            w0, [x1, #0xb]
    // 0xb84c9c: r0 = Padding()
    //     0xb84c9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb84ca0: r1 = Instance_EdgeInsets
    //     0xb84ca0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb84ca4: ldr             x1, [x1, #0xe68]
    // 0xb84ca8: StoreField: r0->field_f = r1
    //     0xb84ca8: stur            w1, [x0, #0xf]
    // 0xb84cac: ldur            x1, [fp, #-0x10]
    // 0xb84cb0: StoreField: r0->field_b = r1
    //     0xb84cb0: stur            w1, [x0, #0xb]
    // 0xb84cb4: LeaveFrame
    //     0xb84cb4: mov             SP, fp
    //     0xb84cb8: ldp             fp, lr, [SP], #0x10
    // 0xb84cbc: ret
    //     0xb84cbc: ret             
    // 0xb84cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84cc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84cc4: b               #0xb84b0c
    // 0xb84cc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84cc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb84ccc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84ccc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStockAlert(/* No info */) {
    // ** addr: 0xb84cd0, size: 0x268
    // 0xb84cd0: EnterFrame
    //     0xb84cd0: stp             fp, lr, [SP, #-0x10]!
    //     0xb84cd4: mov             fp, SP
    // 0xb84cd8: AllocStack(0x50)
    //     0xb84cd8: sub             SP, SP, #0x50
    // 0xb84cdc: SetupParameters(_ProductGridItemState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb84cdc: stur            x1, [fp, #-8]
    //     0xb84ce0: stur            x2, [fp, #-0x10]
    // 0xb84ce4: CheckStackOverflow
    //     0xb84ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84ce8: cmp             SP, x16
    //     0xb84cec: b.ls            #0xb84f2c
    // 0xb84cf0: r17 = 263
    //     0xb84cf0: movz            x17, #0x107
    // 0xb84cf4: ldr             w0, [x2, x17]
    // 0xb84cf8: DecompressPointer r0
    //     0xb84cf8: add             x0, x0, HEAP, lsl #32
    // 0xb84cfc: cmp             w0, NULL
    // 0xb84d00: b.eq            #0xb84d14
    // 0xb84d04: tbnz            w0, #4, #0xb84d14
    // 0xb84d08: d0 = 38.000000
    //     0xb84d08: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xb84d0c: ldr             d0, [x17, #0xd10]
    // 0xb84d10: b               #0xb84d18
    // 0xb84d14: d0 = 4.000000
    //     0xb84d14: fmov            d0, #4.00000000
    // 0xb84d18: stur            d0, [fp, #-0x40]
    // 0xb84d1c: r0 = EdgeInsets()
    //     0xb84d1c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb84d20: d0 = 8.000000
    //     0xb84d20: fmov            d0, #8.00000000
    // 0xb84d24: stur            x0, [fp, #-0x18]
    // 0xb84d28: StoreField: r0->field_7 = d0
    //     0xb84d28: stur            d0, [x0, #7]
    // 0xb84d2c: StoreField: r0->field_f = rZR
    //     0xb84d2c: stur            xzr, [x0, #0xf]
    // 0xb84d30: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb84d30: stur            xzr, [x0, #0x17]
    // 0xb84d34: ldur            d0, [fp, #-0x40]
    // 0xb84d38: StoreField: r0->field_1f = d0
    //     0xb84d38: stur            d0, [x0, #0x1f]
    // 0xb84d3c: r16 = <EdgeInsets>
    //     0xb84d3c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb84d40: ldr             x16, [x16, #0xda0]
    // 0xb84d44: r30 = Instance_EdgeInsets
    //     0xb84d44: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb84d48: ldr             lr, [lr, #0x668]
    // 0xb84d4c: stp             lr, x16, [SP]
    // 0xb84d50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb84d50: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb84d54: r0 = all()
    //     0xb84d54: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb84d58: stur            x0, [fp, #-0x20]
    // 0xb84d5c: r16 = <Color>
    //     0xb84d5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb84d60: ldr             x16, [x16, #0xf80]
    // 0xb84d64: r30 = Instance_Color
    //     0xb84d64: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb84d68: stp             lr, x16, [SP]
    // 0xb84d6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb84d6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb84d70: r0 = all()
    //     0xb84d70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb84d74: stur            x0, [fp, #-0x28]
    // 0xb84d78: r0 = Radius()
    //     0xb84d78: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb84d7c: d0 = 5.000000
    //     0xb84d7c: fmov            d0, #5.00000000
    // 0xb84d80: stur            x0, [fp, #-0x30]
    // 0xb84d84: StoreField: r0->field_7 = d0
    //     0xb84d84: stur            d0, [x0, #7]
    // 0xb84d88: StoreField: r0->field_f = d0
    //     0xb84d88: stur            d0, [x0, #0xf]
    // 0xb84d8c: r0 = BorderRadius()
    //     0xb84d8c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb84d90: mov             x1, x0
    // 0xb84d94: ldur            x0, [fp, #-0x30]
    // 0xb84d98: stur            x1, [fp, #-0x38]
    // 0xb84d9c: StoreField: r1->field_7 = r0
    //     0xb84d9c: stur            w0, [x1, #7]
    // 0xb84da0: StoreField: r1->field_b = r0
    //     0xb84da0: stur            w0, [x1, #0xb]
    // 0xb84da4: StoreField: r1->field_f = r0
    //     0xb84da4: stur            w0, [x1, #0xf]
    // 0xb84da8: StoreField: r1->field_13 = r0
    //     0xb84da8: stur            w0, [x1, #0x13]
    // 0xb84dac: r0 = RoundedRectangleBorder()
    //     0xb84dac: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb84db0: mov             x1, x0
    // 0xb84db4: ldur            x0, [fp, #-0x38]
    // 0xb84db8: StoreField: r1->field_b = r0
    //     0xb84db8: stur            w0, [x1, #0xb]
    // 0xb84dbc: r0 = Instance_BorderSide
    //     0xb84dbc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb84dc0: ldr             x0, [x0, #0xe20]
    // 0xb84dc4: StoreField: r1->field_7 = r0
    //     0xb84dc4: stur            w0, [x1, #7]
    // 0xb84dc8: r16 = <RoundedRectangleBorder>
    //     0xb84dc8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb84dcc: ldr             x16, [x16, #0xf78]
    // 0xb84dd0: stp             x1, x16, [SP]
    // 0xb84dd4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb84dd4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb84dd8: r0 = all()
    //     0xb84dd8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb84ddc: stur            x0, [fp, #-0x30]
    // 0xb84de0: r0 = ButtonStyle()
    //     0xb84de0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb84de4: mov             x1, x0
    // 0xb84de8: ldur            x0, [fp, #-0x28]
    // 0xb84dec: stur            x1, [fp, #-0x38]
    // 0xb84df0: StoreField: r1->field_b = r0
    //     0xb84df0: stur            w0, [x1, #0xb]
    // 0xb84df4: ldur            x0, [fp, #-0x20]
    // 0xb84df8: StoreField: r1->field_23 = r0
    //     0xb84df8: stur            w0, [x1, #0x23]
    // 0xb84dfc: ldur            x0, [fp, #-0x30]
    // 0xb84e00: StoreField: r1->field_43 = r0
    //     0xb84e00: stur            w0, [x1, #0x43]
    // 0xb84e04: r0 = TextButtonThemeData()
    //     0xb84e04: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb84e08: mov             x2, x0
    // 0xb84e0c: ldur            x0, [fp, #-0x38]
    // 0xb84e10: stur            x2, [fp, #-0x20]
    // 0xb84e14: StoreField: r2->field_7 = r0
    //     0xb84e14: stur            w0, [x2, #7]
    // 0xb84e18: ldur            x0, [fp, #-0x10]
    // 0xb84e1c: r17 = 311
    //     0xb84e1c: movz            x17, #0x137
    // 0xb84e20: ldr             w1, [x0, x17]
    // 0xb84e24: DecompressPointer r1
    //     0xb84e24: add             x1, x1, HEAP, lsl #32
    // 0xb84e28: cmp             w1, NULL
    // 0xb84e2c: b.ne            #0xb84e38
    // 0xb84e30: r3 = ""
    //     0xb84e30: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb84e34: b               #0xb84e3c
    // 0xb84e38: mov             x3, x1
    // 0xb84e3c: ldur            x1, [fp, #-8]
    // 0xb84e40: ldur            x0, [fp, #-0x18]
    // 0xb84e44: stur            x3, [fp, #-0x10]
    // 0xb84e48: LoadField: r4 = r1->field_f
    //     0xb84e48: ldur            w4, [x1, #0xf]
    // 0xb84e4c: DecompressPointer r4
    //     0xb84e4c: add             x4, x4, HEAP, lsl #32
    // 0xb84e50: cmp             w4, NULL
    // 0xb84e54: b.eq            #0xb84f34
    // 0xb84e58: mov             x1, x4
    // 0xb84e5c: r0 = of()
    //     0xb84e5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb84e60: LoadField: r1 = r0->field_87
    //     0xb84e60: ldur            w1, [x0, #0x87]
    // 0xb84e64: DecompressPointer r1
    //     0xb84e64: add             x1, x1, HEAP, lsl #32
    // 0xb84e68: LoadField: r0 = r1->field_2b
    //     0xb84e68: ldur            w0, [x1, #0x2b]
    // 0xb84e6c: DecompressPointer r0
    //     0xb84e6c: add             x0, x0, HEAP, lsl #32
    // 0xb84e70: r16 = 12.000000
    //     0xb84e70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb84e74: ldr             x16, [x16, #0x9e8]
    // 0xb84e78: r30 = Instance_Color
    //     0xb84e78: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb84e7c: stp             lr, x16, [SP]
    // 0xb84e80: mov             x1, x0
    // 0xb84e84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb84e84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb84e88: ldr             x4, [x4, #0xaa0]
    // 0xb84e8c: r0 = copyWith()
    //     0xb84e8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb84e90: stur            x0, [fp, #-8]
    // 0xb84e94: r0 = Text()
    //     0xb84e94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb84e98: mov             x3, x0
    // 0xb84e9c: ldur            x0, [fp, #-0x10]
    // 0xb84ea0: stur            x3, [fp, #-0x28]
    // 0xb84ea4: StoreField: r3->field_b = r0
    //     0xb84ea4: stur            w0, [x3, #0xb]
    // 0xb84ea8: ldur            x0, [fp, #-8]
    // 0xb84eac: StoreField: r3->field_13 = r0
    //     0xb84eac: stur            w0, [x3, #0x13]
    // 0xb84eb0: r1 = Function '<anonymous closure>':.
    //     0xb84eb0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61dd8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb84eb4: ldr             x1, [x1, #0xdd8]
    // 0xb84eb8: r2 = Null
    //     0xb84eb8: mov             x2, NULL
    // 0xb84ebc: r0 = AllocateClosure()
    //     0xb84ebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb84ec0: stur            x0, [fp, #-8]
    // 0xb84ec4: r0 = TextButton()
    //     0xb84ec4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb84ec8: mov             x1, x0
    // 0xb84ecc: ldur            x0, [fp, #-8]
    // 0xb84ed0: stur            x1, [fp, #-0x10]
    // 0xb84ed4: StoreField: r1->field_b = r0
    //     0xb84ed4: stur            w0, [x1, #0xb]
    // 0xb84ed8: r0 = false
    //     0xb84ed8: add             x0, NULL, #0x30  ; false
    // 0xb84edc: StoreField: r1->field_27 = r0
    //     0xb84edc: stur            w0, [x1, #0x27]
    // 0xb84ee0: r0 = true
    //     0xb84ee0: add             x0, NULL, #0x20  ; true
    // 0xb84ee4: StoreField: r1->field_2f = r0
    //     0xb84ee4: stur            w0, [x1, #0x2f]
    // 0xb84ee8: ldur            x0, [fp, #-0x28]
    // 0xb84eec: StoreField: r1->field_37 = r0
    //     0xb84eec: stur            w0, [x1, #0x37]
    // 0xb84ef0: r0 = TextButtonTheme()
    //     0xb84ef0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb84ef4: mov             x1, x0
    // 0xb84ef8: ldur            x0, [fp, #-0x20]
    // 0xb84efc: stur            x1, [fp, #-8]
    // 0xb84f00: StoreField: r1->field_f = r0
    //     0xb84f00: stur            w0, [x1, #0xf]
    // 0xb84f04: ldur            x0, [fp, #-0x10]
    // 0xb84f08: StoreField: r1->field_b = r0
    //     0xb84f08: stur            w0, [x1, #0xb]
    // 0xb84f0c: r0 = Padding()
    //     0xb84f0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb84f10: ldur            x1, [fp, #-0x18]
    // 0xb84f14: StoreField: r0->field_f = r1
    //     0xb84f14: stur            w1, [x0, #0xf]
    // 0xb84f18: ldur            x1, [fp, #-8]
    // 0xb84f1c: StoreField: r0->field_b = r1
    //     0xb84f1c: stur            w1, [x0, #0xb]
    // 0xb84f20: LeaveFrame
    //     0xb84f20: mov             SP, fp
    //     0xb84f24: ldp             fp, lr, [SP], #0x10
    // 0xb84f28: ret
    //     0xb84f28: ret             
    // 0xb84f2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb84f2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb84f30: b               #0xb84cf0
    // 0xb84f34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb84f34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb84f38, size: 0x130
    // 0xb84f38: EnterFrame
    //     0xb84f38: stp             fp, lr, [SP, #-0x10]!
    //     0xb84f3c: mov             fp, SP
    // 0xb84f40: AllocStack(0x40)
    //     0xb84f40: sub             SP, SP, #0x40
    // 0xb84f44: SetupParameters()
    //     0xb84f44: ldr             x0, [fp, #0x10]
    //     0xb84f48: ldur            w1, [x0, #0x17]
    //     0xb84f4c: add             x1, x1, HEAP, lsl #32
    // 0xb84f50: CheckStackOverflow
    //     0xb84f50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb84f54: cmp             SP, x16
    //     0xb84f58: b.ls            #0xb8505c
    // 0xb84f5c: LoadField: r0 = r1->field_f
    //     0xb84f5c: ldur            w0, [x1, #0xf]
    // 0xb84f60: DecompressPointer r0
    //     0xb84f60: add             x0, x0, HEAP, lsl #32
    // 0xb84f64: LoadField: r2 = r0->field_b
    //     0xb84f64: ldur            w2, [x0, #0xb]
    // 0xb84f68: DecompressPointer r2
    //     0xb84f68: add             x2, x2, HEAP, lsl #32
    // 0xb84f6c: cmp             w2, NULL
    // 0xb84f70: b.eq            #0xb85064
    // 0xb84f74: LoadField: r0 = r2->field_1b
    //     0xb84f74: ldur            w0, [x2, #0x1b]
    // 0xb84f78: DecompressPointer r0
    //     0xb84f78: add             x0, x0, HEAP, lsl #32
    // 0xb84f7c: cmp             w0, NULL
    // 0xb84f80: b.ne            #0xb84f88
    // 0xb84f84: r0 = ""
    //     0xb84f84: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb84f88: LoadField: r3 = r2->field_13
    //     0xb84f88: ldur            w3, [x2, #0x13]
    // 0xb84f8c: DecompressPointer r3
    //     0xb84f8c: add             x3, x3, HEAP, lsl #32
    // 0xb84f90: cmp             w3, NULL
    // 0xb84f94: b.ne            #0xb84f9c
    // 0xb84f98: r3 = ""
    //     0xb84f98: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb84f9c: LoadField: r4 = r2->field_1f
    //     0xb84f9c: ldur            w4, [x2, #0x1f]
    // 0xb84fa0: DecompressPointer r4
    //     0xb84fa0: add             x4, x4, HEAP, lsl #32
    // 0xb84fa4: cmp             w4, NULL
    // 0xb84fa8: b.ne            #0xb84fb0
    // 0xb84fac: r4 = ""
    //     0xb84fac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb84fb0: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xb84fb0: ldur            w5, [x2, #0x17]
    // 0xb84fb4: DecompressPointer r5
    //     0xb84fb4: add             x5, x5, HEAP, lsl #32
    // 0xb84fb8: cmp             w5, NULL
    // 0xb84fbc: b.ne            #0xb84fc4
    // 0xb84fc0: r5 = ""
    //     0xb84fc0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb84fc4: LoadField: r6 = r1->field_13
    //     0xb84fc4: ldur            w6, [x1, #0x13]
    // 0xb84fc8: DecompressPointer r6
    //     0xb84fc8: add             x6, x6, HEAP, lsl #32
    // 0xb84fcc: cmp             w6, NULL
    // 0xb84fd0: b.ne            #0xb84fdc
    // 0xb84fd4: r1 = Null
    //     0xb84fd4: mov             x1, NULL
    // 0xb84fd8: b               #0xb84fe4
    // 0xb84fdc: LoadField: r1 = r6->field_47
    //     0xb84fdc: ldur            w1, [x6, #0x47]
    // 0xb84fe0: DecompressPointer r1
    //     0xb84fe0: add             x1, x1, HEAP, lsl #32
    // 0xb84fe4: cmp             w6, NULL
    // 0xb84fe8: b.ne            #0xb84ff4
    // 0xb84fec: r6 = Null
    //     0xb84fec: mov             x6, NULL
    // 0xb84ff0: b               #0xb85014
    // 0xb84ff4: LoadField: r7 = r6->field_eb
    //     0xb84ff4: ldur            w7, [x6, #0xeb]
    // 0xb84ff8: DecompressPointer r7
    //     0xb84ff8: add             x7, x7, HEAP, lsl #32
    // 0xb84ffc: cmp             w7, NULL
    // 0xb85000: b.ne            #0xb8500c
    // 0xb85004: r6 = Null
    //     0xb85004: mov             x6, NULL
    // 0xb85008: b               #0xb85014
    // 0xb8500c: LoadField: r6 = r7->field_23
    //     0xb8500c: ldur            w6, [x7, #0x23]
    // 0xb85010: DecompressPointer r6
    //     0xb85010: add             x6, x6, HEAP, lsl #32
    // 0xb85014: LoadField: r7 = r2->field_2f
    //     0xb85014: ldur            w7, [x2, #0x2f]
    // 0xb85018: DecompressPointer r7
    //     0xb85018: add             x7, x7, HEAP, lsl #32
    // 0xb8501c: stp             x0, x7, [SP, #0x30]
    // 0xb85020: r16 = "product_page"
    //     0xb85020: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb85024: ldr             x16, [x16, #0x480]
    // 0xb85028: stp             x16, x3, [SP, #0x20]
    // 0xb8502c: stp             x5, x4, [SP, #0x10]
    // 0xb85030: stp             x6, x1, [SP]
    // 0xb85034: r4 = 0
    //     0xb85034: movz            x4, #0
    // 0xb85038: ldr             x0, [SP, #0x38]
    // 0xb8503c: r16 = UnlinkedCall_0x613b5c
    //     0xb8503c: add             x16, PP, #0x61, lsl #12  ; [pp+0x61dc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb85040: add             x16, x16, #0xdc0
    // 0xb85044: ldp             x5, lr, [x16]
    // 0xb85048: blr             lr
    // 0xb8504c: r0 = Null
    //     0xb8504c: mov             x0, NULL
    // 0xb85050: LeaveFrame
    //     0xb85050: mov             SP, fp
    //     0xb85054: ldp             fp, lr, [SP], #0x10
    // 0xb85058: ret
    //     0xb85058: ret             
    // 0xb8505c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8505c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb85060: b               #0xb84f5c
    // 0xb85064: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb85064: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4057, size: 0x3c, field offset: 0xc
//   const constructor, 
class _ProductGridItem extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f820, size: 0x24
    // 0xc7f820: EnterFrame
    //     0xc7f820: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f824: mov             fp, SP
    // 0xc7f828: mov             x0, x1
    // 0xc7f82c: r1 = <_ProductGridItem>
    //     0xc7f82c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55770] TypeArguments: <_ProductGridItem>
    //     0xc7f830: ldr             x1, [x1, #0x770]
    // 0xc7f834: r0 = _ProductGridItemState()
    //     0xc7f834: bl              #0xc7f844  ; Allocate_ProductGridItemStateStub -> _ProductGridItemState (size=0x14)
    // 0xc7f838: LeaveFrame
    //     0xc7f838: mov             SP, fp
    //     0xc7f83c: ldp             fp, lr, [SP], #0x10
    // 0xc7f840: ret
    //     0xc7f840: ret             
  }
}

// class id: 4490, size: 0x50, field offset: 0xc
//   const constructor, 
class ProductDetailGridItemView extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x129349c, size: 0xf48
    // 0x129349c: EnterFrame
    //     0x129349c: stp             fp, lr, [SP, #-0x10]!
    //     0x12934a0: mov             fp, SP
    // 0x12934a4: AllocStack(0xc8)
    //     0x12934a4: sub             SP, SP, #0xc8
    // 0x12934a8: SetupParameters(ProductDetailGridItemView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x12934a8: mov             x0, x1
    //     0x12934ac: stur            x1, [fp, #-8]
    //     0x12934b0: mov             x1, x2
    //     0x12934b4: stur            x2, [fp, #-0x10]
    // 0x12934b8: CheckStackOverflow
    //     0x12934b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12934bc: cmp             SP, x16
    //     0x12934c0: b.ls            #0x12943dc
    // 0x12934c4: r1 = 1
    //     0x12934c4: movz            x1, #0x1
    // 0x12934c8: r0 = AllocateContext()
    //     0x12934c8: bl              #0x16f6108  ; AllocateContextStub
    // 0x12934cc: mov             x3, x0
    // 0x12934d0: ldur            x0, [fp, #-8]
    // 0x12934d4: stur            x3, [fp, #-0x28]
    // 0x12934d8: StoreField: r3->field_f = r0
    //     0x12934d8: stur            w0, [x3, #0xf]
    // 0x12934dc: LoadField: r4 = r0->field_23
    //     0x12934dc: ldur            w4, [x0, #0x23]
    // 0x12934e0: DecompressPointer r4
    //     0x12934e0: add             x4, x4, HEAP, lsl #32
    // 0x12934e4: stur            x4, [fp, #-0x20]
    // 0x12934e8: LoadField: r1 = r4->field_7
    //     0x12934e8: ldur            w1, [x4, #7]
    // 0x12934ec: DecompressPointer r1
    //     0x12934ec: add             x1, x1, HEAP, lsl #32
    // 0x12934f0: cmp             w1, NULL
    // 0x12934f4: b.ne            #0x1293500
    // 0x12934f8: r1 = Instance_TitleAlignment
    //     0x12934f8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x12934fc: ldr             x1, [x1, #0x518]
    // 0x1293500: r16 = Instance_TitleAlignment
    //     0x1293500: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x1293504: ldr             x16, [x16, #0x520]
    // 0x1293508: cmp             w1, w16
    // 0x129350c: b.ne            #0x129351c
    // 0x1293510: r5 = Instance_CrossAxisAlignment
    //     0x1293510: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x1293514: ldr             x5, [x5, #0xc68]
    // 0x1293518: b               #0x1293540
    // 0x129351c: r16 = Instance_TitleAlignment
    //     0x129351c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1293520: ldr             x16, [x16, #0x518]
    // 0x1293524: cmp             w1, w16
    // 0x1293528: b.ne            #0x1293538
    // 0x129352c: r5 = Instance_CrossAxisAlignment
    //     0x129352c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1293530: ldr             x5, [x5, #0x890]
    // 0x1293534: b               #0x1293540
    // 0x1293538: r5 = Instance_CrossAxisAlignment
    //     0x1293538: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x129353c: ldr             x5, [x5, #0xa18]
    // 0x1293540: stur            x5, [fp, #-0x18]
    // 0x1293544: r1 = <Widget>
    //     0x1293544: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1293548: r2 = 0
    //     0x1293548: movz            x2, #0
    // 0x129354c: r0 = _GrowableList()
    //     0x129354c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x1293550: mov             x2, x0
    // 0x1293554: ldur            x0, [fp, #-8]
    // 0x1293558: stur            x2, [fp, #-0x38]
    // 0x129355c: LoadField: r3 = r0->field_f
    //     0x129355c: ldur            w3, [x0, #0xf]
    // 0x1293560: DecompressPointer r3
    //     0x1293560: add             x3, x3, HEAP, lsl #32
    // 0x1293564: stur            x3, [fp, #-0x30]
    // 0x1293568: cmp             w3, NULL
    // 0x129356c: b.ne            #0x1293578
    // 0x1293570: r1 = Null
    //     0x1293570: mov             x1, NULL
    // 0x1293574: b               #0x1293590
    // 0x1293578: LoadField: r1 = r3->field_7
    //     0x1293578: ldur            w1, [x3, #7]
    // 0x129357c: cbnz            w1, #0x1293588
    // 0x1293580: r4 = false
    //     0x1293580: add             x4, NULL, #0x30  ; false
    // 0x1293584: b               #0x129358c
    // 0x1293588: r4 = true
    //     0x1293588: add             x4, NULL, #0x20  ; true
    // 0x129358c: mov             x1, x4
    // 0x1293590: cmp             w1, NULL
    // 0x1293594: b.eq            #0x1293718
    // 0x1293598: tbnz            w1, #4, #0x1293718
    // 0x129359c: cmp             w3, NULL
    // 0x12935a0: b.ne            #0x12935ac
    // 0x12935a4: r1 = ""
    //     0x12935a4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12935a8: b               #0x12935b0
    // 0x12935ac: mov             x1, x3
    // 0x12935b0: ldur            x4, [fp, #-0x20]
    // 0x12935b4: r0 = capitalizeFirstWord()
    //     0x12935b4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x12935b8: mov             x2, x0
    // 0x12935bc: ldur            x0, [fp, #-0x20]
    // 0x12935c0: stur            x2, [fp, #-0x40]
    // 0x12935c4: LoadField: r1 = r0->field_7
    //     0x12935c4: ldur            w1, [x0, #7]
    // 0x12935c8: DecompressPointer r1
    //     0x12935c8: add             x1, x1, HEAP, lsl #32
    // 0x12935cc: cmp             w1, NULL
    // 0x12935d0: b.ne            #0x12935e0
    // 0x12935d4: r0 = Instance_TitleAlignment
    //     0x12935d4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x12935d8: ldr             x0, [x0, #0x518]
    // 0x12935dc: b               #0x12935e4
    // 0x12935e0: mov             x0, x1
    // 0x12935e4: r16 = Instance_TitleAlignment
    //     0x12935e4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0x12935e8: ldr             x16, [x16, #0x520]
    // 0x12935ec: cmp             w0, w16
    // 0x12935f0: b.ne            #0x12935fc
    // 0x12935f4: r3 = Instance_TextAlign
    //     0x12935f4: ldr             x3, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0x12935f8: b               #0x1293618
    // 0x12935fc: r16 = Instance_TitleAlignment
    //     0x12935fc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0x1293600: ldr             x16, [x16, #0x518]
    // 0x1293604: cmp             w0, w16
    // 0x1293608: b.ne            #0x1293614
    // 0x129360c: r3 = Instance_TextAlign
    //     0x129360c: ldr             x3, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0x1293610: b               #0x1293618
    // 0x1293614: r3 = Instance_TextAlign
    //     0x1293614: ldr             x3, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1293618: ldur            x0, [fp, #-0x38]
    // 0x129361c: ldur            x1, [fp, #-0x10]
    // 0x1293620: stur            x3, [fp, #-0x20]
    // 0x1293624: r0 = of()
    //     0x1293624: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293628: LoadField: r1 = r0->field_87
    //     0x1293628: ldur            w1, [x0, #0x87]
    // 0x129362c: DecompressPointer r1
    //     0x129362c: add             x1, x1, HEAP, lsl #32
    // 0x1293630: LoadField: r0 = r1->field_7
    //     0x1293630: ldur            w0, [x1, #7]
    // 0x1293634: DecompressPointer r0
    //     0x1293634: add             x0, x0, HEAP, lsl #32
    // 0x1293638: r16 = 32.000000
    //     0x1293638: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x129363c: ldr             x16, [x16, #0x848]
    // 0x1293640: r30 = Instance_Color
    //     0x1293640: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1293644: stp             lr, x16, [SP]
    // 0x1293648: mov             x1, x0
    // 0x129364c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x129364c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1293650: ldr             x4, [x4, #0xaa0]
    // 0x1293654: r0 = copyWith()
    //     0x1293654: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293658: stur            x0, [fp, #-0x48]
    // 0x129365c: r0 = Text()
    //     0x129365c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1293660: mov             x1, x0
    // 0x1293664: ldur            x0, [fp, #-0x40]
    // 0x1293668: stur            x1, [fp, #-0x50]
    // 0x129366c: StoreField: r1->field_b = r0
    //     0x129366c: stur            w0, [x1, #0xb]
    // 0x1293670: ldur            x0, [fp, #-0x48]
    // 0x1293674: StoreField: r1->field_13 = r0
    //     0x1293674: stur            w0, [x1, #0x13]
    // 0x1293678: ldur            x0, [fp, #-0x20]
    // 0x129367c: StoreField: r1->field_1b = r0
    //     0x129367c: stur            w0, [x1, #0x1b]
    // 0x1293680: r0 = Padding()
    //     0x1293680: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1293684: mov             x2, x0
    // 0x1293688: r0 = Instance_EdgeInsets
    //     0x1293688: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0x129368c: ldr             x0, [x0, #0x4c0]
    // 0x1293690: stur            x2, [fp, #-0x20]
    // 0x1293694: StoreField: r2->field_f = r0
    //     0x1293694: stur            w0, [x2, #0xf]
    // 0x1293698: ldur            x0, [fp, #-0x50]
    // 0x129369c: StoreField: r2->field_b = r0
    //     0x129369c: stur            w0, [x2, #0xb]
    // 0x12936a0: ldur            x0, [fp, #-0x38]
    // 0x12936a4: LoadField: r1 = r0->field_b
    //     0x12936a4: ldur            w1, [x0, #0xb]
    // 0x12936a8: LoadField: r3 = r0->field_f
    //     0x12936a8: ldur            w3, [x0, #0xf]
    // 0x12936ac: DecompressPointer r3
    //     0x12936ac: add             x3, x3, HEAP, lsl #32
    // 0x12936b0: LoadField: r4 = r3->field_b
    //     0x12936b0: ldur            w4, [x3, #0xb]
    // 0x12936b4: r3 = LoadInt32Instr(r1)
    //     0x12936b4: sbfx            x3, x1, #1, #0x1f
    // 0x12936b8: stur            x3, [fp, #-0x58]
    // 0x12936bc: r1 = LoadInt32Instr(r4)
    //     0x12936bc: sbfx            x1, x4, #1, #0x1f
    // 0x12936c0: cmp             x3, x1
    // 0x12936c4: b.ne            #0x12936d0
    // 0x12936c8: mov             x1, x0
    // 0x12936cc: r0 = _growToNextCapacity()
    //     0x12936cc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12936d0: ldur            x2, [fp, #-0x38]
    // 0x12936d4: ldur            x3, [fp, #-0x58]
    // 0x12936d8: add             x0, x3, #1
    // 0x12936dc: lsl             x1, x0, #1
    // 0x12936e0: StoreField: r2->field_b = r1
    //     0x12936e0: stur            w1, [x2, #0xb]
    // 0x12936e4: LoadField: r1 = r2->field_f
    //     0x12936e4: ldur            w1, [x2, #0xf]
    // 0x12936e8: DecompressPointer r1
    //     0x12936e8: add             x1, x1, HEAP, lsl #32
    // 0x12936ec: ldur            x0, [fp, #-0x20]
    // 0x12936f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x12936f0: add             x25, x1, x3, lsl #2
    //     0x12936f4: add             x25, x25, #0xf
    //     0x12936f8: str             w0, [x25]
    //     0x12936fc: tbz             w0, #0, #0x1293718
    //     0x1293700: ldurb           w16, [x1, #-1]
    //     0x1293704: ldurb           w17, [x0, #-1]
    //     0x1293708: and             x16, x17, x16, lsr #2
    //     0x129370c: tst             x16, HEAP, lsr #32
    //     0x1293710: b.eq            #0x1293718
    //     0x1293714: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1293718: ldur            x0, [fp, #-8]
    // 0x129371c: LoadField: r1 = r0->field_37
    //     0x129371c: ldur            w1, [x0, #0x37]
    // 0x1293720: DecompressPointer r1
    //     0x1293720: add             x1, x1, HEAP, lsl #32
    // 0x1293724: stur            x1, [fp, #-0x48]
    // 0x1293728: LoadField: r3 = r1->field_f
    //     0x1293728: ldur            w3, [x1, #0xf]
    // 0x129372c: DecompressPointer r3
    //     0x129372c: add             x3, x3, HEAP, lsl #32
    // 0x1293730: cmp             w3, NULL
    // 0x1293734: r16 = true
    //     0x1293734: add             x16, NULL, #0x20  ; true
    // 0x1293738: r17 = false
    //     0x1293738: add             x17, NULL, #0x30  ; false
    // 0x129373c: csel            x4, x16, x17, ne
    // 0x1293740: stur            x4, [fp, #-0x40]
    // 0x1293744: LoadField: r3 = r1->field_13
    //     0x1293744: ldur            w3, [x1, #0x13]
    // 0x1293748: DecompressPointer r3
    //     0x1293748: add             x3, x3, HEAP, lsl #32
    // 0x129374c: stur            x3, [fp, #-0x20]
    // 0x1293750: cmp             w3, NULL
    // 0x1293754: b.ne            #0x1293760
    // 0x1293758: r5 = Null
    //     0x1293758: mov             x5, NULL
    // 0x129375c: b               #0x1293768
    // 0x1293760: LoadField: r5 = r3->field_7
    //     0x1293760: ldur            w5, [x3, #7]
    // 0x1293764: DecompressPointer r5
    //     0x1293764: add             x5, x5, HEAP, lsl #32
    // 0x1293768: cmp             w5, NULL
    // 0x129376c: b.ne            #0x1293778
    // 0x1293770: r5 = 0
    //     0x1293770: movz            x5, #0
    // 0x1293774: b               #0x1293788
    // 0x1293778: r6 = LoadInt32Instr(r5)
    //     0x1293778: sbfx            x6, x5, #1, #0x1f
    //     0x129377c: tbz             w5, #0, #0x1293784
    //     0x1293780: ldur            x6, [x5, #7]
    // 0x1293784: mov             x5, x6
    // 0x1293788: stur            x5, [fp, #-0x68]
    // 0x129378c: cmp             w3, NULL
    // 0x1293790: b.ne            #0x129379c
    // 0x1293794: r6 = Null
    //     0x1293794: mov             x6, NULL
    // 0x1293798: b               #0x12937a4
    // 0x129379c: LoadField: r6 = r3->field_b
    //     0x129379c: ldur            w6, [x3, #0xb]
    // 0x12937a0: DecompressPointer r6
    //     0x12937a0: add             x6, x6, HEAP, lsl #32
    // 0x12937a4: cmp             w6, NULL
    // 0x12937a8: b.ne            #0x12937b4
    // 0x12937ac: r6 = 0
    //     0x12937ac: movz            x6, #0
    // 0x12937b0: b               #0x12937c4
    // 0x12937b4: r7 = LoadInt32Instr(r6)
    //     0x12937b4: sbfx            x7, x6, #1, #0x1f
    //     0x12937b8: tbz             w6, #0, #0x12937c0
    //     0x12937bc: ldur            x7, [x6, #7]
    // 0x12937c0: mov             x6, x7
    // 0x12937c4: stur            x6, [fp, #-0x60]
    // 0x12937c8: cmp             w3, NULL
    // 0x12937cc: b.ne            #0x12937d8
    // 0x12937d0: r7 = Null
    //     0x12937d0: mov             x7, NULL
    // 0x12937d4: b               #0x12937e0
    // 0x12937d8: LoadField: r7 = r3->field_f
    //     0x12937d8: ldur            w7, [x3, #0xf]
    // 0x12937dc: DecompressPointer r7
    //     0x12937dc: add             x7, x7, HEAP, lsl #32
    // 0x12937e0: cmp             w7, NULL
    // 0x12937e4: b.ne            #0x12937f0
    // 0x12937e8: r7 = 0
    //     0x12937e8: movz            x7, #0
    // 0x12937ec: b               #0x1293800
    // 0x12937f0: r8 = LoadInt32Instr(r7)
    //     0x12937f0: sbfx            x8, x7, #1, #0x1f
    //     0x12937f4: tbz             w7, #0, #0x12937fc
    //     0x12937f8: ldur            x8, [x7, #7]
    // 0x12937fc: mov             x7, x8
    // 0x1293800: stur            x7, [fp, #-0x58]
    // 0x1293804: r0 = Color()
    //     0x1293804: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1293808: mov             x1, x0
    // 0x129380c: r0 = Instance_ColorSpace
    //     0x129380c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1293810: stur            x1, [fp, #-0x50]
    // 0x1293814: StoreField: r1->field_27 = r0
    //     0x1293814: stur            w0, [x1, #0x27]
    // 0x1293818: d0 = 1.000000
    //     0x1293818: fmov            d0, #1.00000000
    // 0x129381c: StoreField: r1->field_7 = d0
    //     0x129381c: stur            d0, [x1, #7]
    // 0x1293820: ldur            x2, [fp, #-0x68]
    // 0x1293824: ubfx            x2, x2, #0, #0x20
    // 0x1293828: and             w3, w2, #0xff
    // 0x129382c: ubfx            x3, x3, #0, #0x20
    // 0x1293830: scvtf           d0, x3
    // 0x1293834: d1 = 255.000000
    //     0x1293834: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1293838: fdiv            d2, d0, d1
    // 0x129383c: StoreField: r1->field_f = d2
    //     0x129383c: stur            d2, [x1, #0xf]
    // 0x1293840: ldur            x2, [fp, #-0x60]
    // 0x1293844: ubfx            x2, x2, #0, #0x20
    // 0x1293848: and             w3, w2, #0xff
    // 0x129384c: ubfx            x3, x3, #0, #0x20
    // 0x1293850: scvtf           d0, x3
    // 0x1293854: fdiv            d2, d0, d1
    // 0x1293858: ArrayStore: r1[0] = d2  ; List_8
    //     0x1293858: stur            d2, [x1, #0x17]
    // 0x129385c: ldur            x2, [fp, #-0x58]
    // 0x1293860: ubfx            x2, x2, #0, #0x20
    // 0x1293864: and             w3, w2, #0xff
    // 0x1293868: ubfx            x3, x3, #0, #0x20
    // 0x129386c: scvtf           d0, x3
    // 0x1293870: fdiv            d2, d0, d1
    // 0x1293874: StoreField: r1->field_1f = d2
    //     0x1293874: stur            d2, [x1, #0x1f]
    // 0x1293878: ldur            x2, [fp, #-0x20]
    // 0x129387c: cmp             w2, NULL
    // 0x1293880: b.ne            #0x129388c
    // 0x1293884: r3 = Null
    //     0x1293884: mov             x3, NULL
    // 0x1293888: b               #0x1293894
    // 0x129388c: LoadField: r3 = r2->field_7
    //     0x129388c: ldur            w3, [x2, #7]
    // 0x1293890: DecompressPointer r3
    //     0x1293890: add             x3, x3, HEAP, lsl #32
    // 0x1293894: cmp             w3, NULL
    // 0x1293898: b.ne            #0x12938a4
    // 0x129389c: r3 = 0
    //     0x129389c: movz            x3, #0
    // 0x12938a0: b               #0x12938b4
    // 0x12938a4: r4 = LoadInt32Instr(r3)
    //     0x12938a4: sbfx            x4, x3, #1, #0x1f
    //     0x12938a8: tbz             w3, #0, #0x12938b0
    //     0x12938ac: ldur            x4, [x3, #7]
    // 0x12938b0: mov             x3, x4
    // 0x12938b4: stur            x3, [fp, #-0x68]
    // 0x12938b8: cmp             w2, NULL
    // 0x12938bc: b.ne            #0x12938c8
    // 0x12938c0: r4 = Null
    //     0x12938c0: mov             x4, NULL
    // 0x12938c4: b               #0x12938d0
    // 0x12938c8: LoadField: r4 = r2->field_b
    //     0x12938c8: ldur            w4, [x2, #0xb]
    // 0x12938cc: DecompressPointer r4
    //     0x12938cc: add             x4, x4, HEAP, lsl #32
    // 0x12938d0: cmp             w4, NULL
    // 0x12938d4: b.ne            #0x12938e0
    // 0x12938d8: r4 = 0
    //     0x12938d8: movz            x4, #0
    // 0x12938dc: b               #0x12938f0
    // 0x12938e0: r5 = LoadInt32Instr(r4)
    //     0x12938e0: sbfx            x5, x4, #1, #0x1f
    //     0x12938e4: tbz             w4, #0, #0x12938ec
    //     0x12938e8: ldur            x5, [x4, #7]
    // 0x12938ec: mov             x4, x5
    // 0x12938f0: stur            x4, [fp, #-0x60]
    // 0x12938f4: cmp             w2, NULL
    // 0x12938f8: b.ne            #0x1293904
    // 0x12938fc: r2 = Null
    //     0x12938fc: mov             x2, NULL
    // 0x1293900: b               #0x1293910
    // 0x1293904: LoadField: r5 = r2->field_f
    //     0x1293904: ldur            w5, [x2, #0xf]
    // 0x1293908: DecompressPointer r5
    //     0x1293908: add             x5, x5, HEAP, lsl #32
    // 0x129390c: mov             x2, x5
    // 0x1293910: cmp             w2, NULL
    // 0x1293914: b.ne            #0x1293920
    // 0x1293918: r7 = 0
    //     0x1293918: movz            x7, #0
    // 0x129391c: b               #0x1293930
    // 0x1293920: r5 = LoadInt32Instr(r2)
    //     0x1293920: sbfx            x5, x2, #1, #0x1f
    //     0x1293924: tbz             w2, #0, #0x129392c
    //     0x1293928: ldur            x5, [x2, #7]
    // 0x129392c: mov             x7, x5
    // 0x1293930: ldur            x2, [fp, #-0x38]
    // 0x1293934: ldur            x5, [fp, #-0x48]
    // 0x1293938: ldur            x6, [fp, #-0x40]
    // 0x129393c: stur            x7, [fp, #-0x58]
    // 0x1293940: r0 = Color()
    //     0x1293940: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1293944: mov             x3, x0
    // 0x1293948: r0 = Instance_ColorSpace
    //     0x1293948: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x129394c: stur            x3, [fp, #-0x20]
    // 0x1293950: StoreField: r3->field_27 = r0
    //     0x1293950: stur            w0, [x3, #0x27]
    // 0x1293954: d0 = 0.700000
    //     0x1293954: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1293958: ldr             d0, [x17, #0xf48]
    // 0x129395c: StoreField: r3->field_7 = d0
    //     0x129395c: stur            d0, [x3, #7]
    // 0x1293960: ldur            x0, [fp, #-0x68]
    // 0x1293964: ubfx            x0, x0, #0, #0x20
    // 0x1293968: and             w1, w0, #0xff
    // 0x129396c: ubfx            x1, x1, #0, #0x20
    // 0x1293970: scvtf           d0, x1
    // 0x1293974: d1 = 255.000000
    //     0x1293974: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1293978: fdiv            d2, d0, d1
    // 0x129397c: StoreField: r3->field_f = d2
    //     0x129397c: stur            d2, [x3, #0xf]
    // 0x1293980: ldur            x0, [fp, #-0x60]
    // 0x1293984: ubfx            x0, x0, #0, #0x20
    // 0x1293988: and             w1, w0, #0xff
    // 0x129398c: ubfx            x1, x1, #0, #0x20
    // 0x1293990: scvtf           d0, x1
    // 0x1293994: fdiv            d2, d0, d1
    // 0x1293998: ArrayStore: r3[0] = d2  ; List_8
    //     0x1293998: stur            d2, [x3, #0x17]
    // 0x129399c: ldur            x0, [fp, #-0x58]
    // 0x12939a0: ubfx            x0, x0, #0, #0x20
    // 0x12939a4: and             w1, w0, #0xff
    // 0x12939a8: ubfx            x1, x1, #0, #0x20
    // 0x12939ac: scvtf           d0, x1
    // 0x12939b0: fdiv            d2, d0, d1
    // 0x12939b4: StoreField: r3->field_1f = d2
    //     0x12939b4: stur            d2, [x3, #0x1f]
    // 0x12939b8: r1 = Null
    //     0x12939b8: mov             x1, NULL
    // 0x12939bc: r2 = 4
    //     0x12939bc: movz            x2, #0x4
    // 0x12939c0: r0 = AllocateArray()
    //     0x12939c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12939c4: mov             x2, x0
    // 0x12939c8: ldur            x0, [fp, #-0x50]
    // 0x12939cc: stur            x2, [fp, #-0x70]
    // 0x12939d0: StoreField: r2->field_f = r0
    //     0x12939d0: stur            w0, [x2, #0xf]
    // 0x12939d4: ldur            x0, [fp, #-0x20]
    // 0x12939d8: StoreField: r2->field_13 = r0
    //     0x12939d8: stur            w0, [x2, #0x13]
    // 0x12939dc: r1 = <Color>
    //     0x12939dc: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x12939e0: ldr             x1, [x1, #0xf80]
    // 0x12939e4: r0 = AllocateGrowableArray()
    //     0x12939e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12939e8: mov             x1, x0
    // 0x12939ec: ldur            x0, [fp, #-0x70]
    // 0x12939f0: stur            x1, [fp, #-0x20]
    // 0x12939f4: StoreField: r1->field_f = r0
    //     0x12939f4: stur            w0, [x1, #0xf]
    // 0x12939f8: r2 = 4
    //     0x12939f8: movz            x2, #0x4
    // 0x12939fc: StoreField: r1->field_b = r2
    //     0x12939fc: stur            w2, [x1, #0xb]
    // 0x1293a00: r0 = LinearGradient()
    //     0x1293a00: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0x1293a04: mov             x1, x0
    // 0x1293a08: r0 = Instance_Alignment
    //     0x1293a08: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x1293a0c: ldr             x0, [x0, #0xce0]
    // 0x1293a10: stur            x1, [fp, #-0x50]
    // 0x1293a14: StoreField: r1->field_13 = r0
    //     0x1293a14: stur            w0, [x1, #0x13]
    // 0x1293a18: r0 = Instance_Alignment
    //     0x1293a18: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0x1293a1c: ldr             x0, [x0, #0xce8]
    // 0x1293a20: ArrayStore: r1[0] = r0  ; List_4
    //     0x1293a20: stur            w0, [x1, #0x17]
    // 0x1293a24: r0 = Instance_TileMode
    //     0x1293a24: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0x1293a28: ldr             x0, [x0, #0xcf0]
    // 0x1293a2c: StoreField: r1->field_1b = r0
    //     0x1293a2c: stur            w0, [x1, #0x1b]
    // 0x1293a30: ldur            x0, [fp, #-0x20]
    // 0x1293a34: StoreField: r1->field_7 = r0
    //     0x1293a34: stur            w0, [x1, #7]
    // 0x1293a38: r0 = BoxDecoration()
    //     0x1293a38: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1293a3c: mov             x2, x0
    // 0x1293a40: r0 = Instance_BorderRadius
    //     0x1293a40: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0x1293a44: ldr             x0, [x0, #0xe10]
    // 0x1293a48: stur            x2, [fp, #-0x20]
    // 0x1293a4c: StoreField: r2->field_13 = r0
    //     0x1293a4c: stur            w0, [x2, #0x13]
    // 0x1293a50: ldur            x0, [fp, #-0x50]
    // 0x1293a54: StoreField: r2->field_1b = r0
    //     0x1293a54: stur            w0, [x2, #0x1b]
    // 0x1293a58: r0 = Instance_BoxShape
    //     0x1293a58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1293a5c: ldr             x0, [x0, #0x80]
    // 0x1293a60: StoreField: r2->field_23 = r0
    //     0x1293a60: stur            w0, [x2, #0x23]
    // 0x1293a64: ldur            x1, [fp, #-0x10]
    // 0x1293a68: r0 = of()
    //     0x1293a68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293a6c: LoadField: r1 = r0->field_87
    //     0x1293a6c: ldur            w1, [x0, #0x87]
    // 0x1293a70: DecompressPointer r1
    //     0x1293a70: add             x1, x1, HEAP, lsl #32
    // 0x1293a74: LoadField: r0 = r1->field_7
    //     0x1293a74: ldur            w0, [x1, #7]
    // 0x1293a78: DecompressPointer r0
    //     0x1293a78: add             x0, x0, HEAP, lsl #32
    // 0x1293a7c: r16 = 16.000000
    //     0x1293a7c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1293a80: ldr             x16, [x16, #0x188]
    // 0x1293a84: r30 = Instance_Color
    //     0x1293a84: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1293a88: stp             lr, x16, [SP]
    // 0x1293a8c: mov             x1, x0
    // 0x1293a90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1293a90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1293a94: ldr             x4, [x4, #0xaa0]
    // 0x1293a98: r0 = copyWith()
    //     0x1293a98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293a9c: stur            x0, [fp, #-0x50]
    // 0x1293aa0: r0 = TextSpan()
    //     0x1293aa0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1293aa4: mov             x2, x0
    // 0x1293aa8: r0 = "BUMPER OFFER\n"
    //     0x1293aa8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0x1293aac: ldr             x0, [x0, #0x338]
    // 0x1293ab0: stur            x2, [fp, #-0x70]
    // 0x1293ab4: StoreField: r2->field_b = r0
    //     0x1293ab4: stur            w0, [x2, #0xb]
    // 0x1293ab8: r0 = Instance__DeferringMouseCursor
    //     0x1293ab8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1293abc: ArrayStore: r2[0] = r0  ; List_4
    //     0x1293abc: stur            w0, [x2, #0x17]
    // 0x1293ac0: ldur            x1, [fp, #-0x50]
    // 0x1293ac4: StoreField: r2->field_7 = r1
    //     0x1293ac4: stur            w1, [x2, #7]
    // 0x1293ac8: ldur            x1, [fp, #-0x10]
    // 0x1293acc: r0 = of()
    //     0x1293acc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293ad0: LoadField: r1 = r0->field_87
    //     0x1293ad0: ldur            w1, [x0, #0x87]
    // 0x1293ad4: DecompressPointer r1
    //     0x1293ad4: add             x1, x1, HEAP, lsl #32
    // 0x1293ad8: LoadField: r0 = r1->field_2b
    //     0x1293ad8: ldur            w0, [x1, #0x2b]
    // 0x1293adc: DecompressPointer r0
    //     0x1293adc: add             x0, x0, HEAP, lsl #32
    // 0x1293ae0: r16 = 12.000000
    //     0x1293ae0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1293ae4: ldr             x16, [x16, #0x9e8]
    // 0x1293ae8: r30 = Instance_Color
    //     0x1293ae8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1293aec: stp             lr, x16, [SP]
    // 0x1293af0: mov             x1, x0
    // 0x1293af4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1293af4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1293af8: ldr             x4, [x4, #0xaa0]
    // 0x1293afc: r0 = copyWith()
    //     0x1293afc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293b00: stur            x0, [fp, #-0x50]
    // 0x1293b04: r0 = TextSpan()
    //     0x1293b04: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1293b08: mov             x3, x0
    // 0x1293b0c: r0 = "Unlocked from your last order"
    //     0x1293b0c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0x1293b10: ldr             x0, [x0, #0x340]
    // 0x1293b14: stur            x3, [fp, #-0x78]
    // 0x1293b18: StoreField: r3->field_b = r0
    //     0x1293b18: stur            w0, [x3, #0xb]
    // 0x1293b1c: r0 = Instance__DeferringMouseCursor
    //     0x1293b1c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1293b20: ArrayStore: r3[0] = r0  ; List_4
    //     0x1293b20: stur            w0, [x3, #0x17]
    // 0x1293b24: ldur            x1, [fp, #-0x50]
    // 0x1293b28: StoreField: r3->field_7 = r1
    //     0x1293b28: stur            w1, [x3, #7]
    // 0x1293b2c: r1 = Null
    //     0x1293b2c: mov             x1, NULL
    // 0x1293b30: r2 = 4
    //     0x1293b30: movz            x2, #0x4
    // 0x1293b34: r0 = AllocateArray()
    //     0x1293b34: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1293b38: mov             x2, x0
    // 0x1293b3c: ldur            x0, [fp, #-0x70]
    // 0x1293b40: stur            x2, [fp, #-0x50]
    // 0x1293b44: StoreField: r2->field_f = r0
    //     0x1293b44: stur            w0, [x2, #0xf]
    // 0x1293b48: ldur            x0, [fp, #-0x78]
    // 0x1293b4c: StoreField: r2->field_13 = r0
    //     0x1293b4c: stur            w0, [x2, #0x13]
    // 0x1293b50: r1 = <InlineSpan>
    //     0x1293b50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1293b54: ldr             x1, [x1, #0xe40]
    // 0x1293b58: r0 = AllocateGrowableArray()
    //     0x1293b58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1293b5c: mov             x1, x0
    // 0x1293b60: ldur            x0, [fp, #-0x50]
    // 0x1293b64: stur            x1, [fp, #-0x70]
    // 0x1293b68: StoreField: r1->field_f = r0
    //     0x1293b68: stur            w0, [x1, #0xf]
    // 0x1293b6c: r2 = 4
    //     0x1293b6c: movz            x2, #0x4
    // 0x1293b70: StoreField: r1->field_b = r2
    //     0x1293b70: stur            w2, [x1, #0xb]
    // 0x1293b74: r0 = TextSpan()
    //     0x1293b74: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1293b78: mov             x1, x0
    // 0x1293b7c: ldur            x0, [fp, #-0x70]
    // 0x1293b80: stur            x1, [fp, #-0x50]
    // 0x1293b84: StoreField: r1->field_f = r0
    //     0x1293b84: stur            w0, [x1, #0xf]
    // 0x1293b88: r0 = Instance__DeferringMouseCursor
    //     0x1293b88: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1293b8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x1293b8c: stur            w0, [x1, #0x17]
    // 0x1293b90: r0 = RichText()
    //     0x1293b90: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1293b94: mov             x1, x0
    // 0x1293b98: ldur            x2, [fp, #-0x50]
    // 0x1293b9c: stur            x0, [fp, #-0x50]
    // 0x1293ba0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1293ba0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1293ba4: r0 = RichText()
    //     0x1293ba4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1293ba8: ldur            x0, [fp, #-0x48]
    // 0x1293bac: LoadField: r3 = r0->field_7
    //     0x1293bac: ldur            w3, [x0, #7]
    // 0x1293bb0: DecompressPointer r3
    //     0x1293bb0: add             x3, x3, HEAP, lsl #32
    // 0x1293bb4: stur            x3, [fp, #-0x70]
    // 0x1293bb8: r1 = Null
    //     0x1293bb8: mov             x1, NULL
    // 0x1293bbc: r2 = 4
    //     0x1293bbc: movz            x2, #0x4
    // 0x1293bc0: r0 = AllocateArray()
    //     0x1293bc0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1293bc4: mov             x1, x0
    // 0x1293bc8: ldur            x0, [fp, #-0x70]
    // 0x1293bcc: StoreField: r1->field_f = r0
    //     0x1293bcc: stur            w0, [x1, #0xf]
    // 0x1293bd0: r16 = "\n"
    //     0x1293bd0: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0x1293bd4: StoreField: r1->field_13 = r16
    //     0x1293bd4: stur            w16, [x1, #0x13]
    // 0x1293bd8: str             x1, [SP]
    // 0x1293bdc: r0 = _interpolate()
    //     0x1293bdc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1293be0: ldur            x1, [fp, #-0x10]
    // 0x1293be4: stur            x0, [fp, #-0x70]
    // 0x1293be8: r0 = of()
    //     0x1293be8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293bec: LoadField: r1 = r0->field_87
    //     0x1293bec: ldur            w1, [x0, #0x87]
    // 0x1293bf0: DecompressPointer r1
    //     0x1293bf0: add             x1, x1, HEAP, lsl #32
    // 0x1293bf4: LoadField: r0 = r1->field_7
    //     0x1293bf4: ldur            w0, [x1, #7]
    // 0x1293bf8: DecompressPointer r0
    //     0x1293bf8: add             x0, x0, HEAP, lsl #32
    // 0x1293bfc: r16 = 32.000000
    //     0x1293bfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x1293c00: ldr             x16, [x16, #0x848]
    // 0x1293c04: r30 = Instance_Color
    //     0x1293c04: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1293c08: stp             lr, x16, [SP]
    // 0x1293c0c: mov             x1, x0
    // 0x1293c10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1293c10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1293c14: ldr             x4, [x4, #0xaa0]
    // 0x1293c18: r0 = copyWith()
    //     0x1293c18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293c1c: ldur            x1, [fp, #-0x10]
    // 0x1293c20: stur            x0, [fp, #-0x78]
    // 0x1293c24: r0 = of()
    //     0x1293c24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1293c28: LoadField: r1 = r0->field_87
    //     0x1293c28: ldur            w1, [x0, #0x87]
    // 0x1293c2c: DecompressPointer r1
    //     0x1293c2c: add             x1, x1, HEAP, lsl #32
    // 0x1293c30: LoadField: r0 = r1->field_2b
    //     0x1293c30: ldur            w0, [x1, #0x2b]
    // 0x1293c34: DecompressPointer r0
    //     0x1293c34: add             x0, x0, HEAP, lsl #32
    // 0x1293c38: r16 = Instance_Color
    //     0x1293c38: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1293c3c: r30 = 16.000000
    //     0x1293c3c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1293c40: ldr             lr, [lr, #0x188]
    // 0x1293c44: stp             lr, x16, [SP]
    // 0x1293c48: mov             x1, x0
    // 0x1293c4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1293c4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1293c50: ldr             x4, [x4, #0x9b8]
    // 0x1293c54: r0 = copyWith()
    //     0x1293c54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1293c58: stur            x0, [fp, #-0x80]
    // 0x1293c5c: r0 = TextSpan()
    //     0x1293c5c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1293c60: mov             x3, x0
    // 0x1293c64: r0 = "OFF"
    //     0x1293c64: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0x1293c68: ldr             x0, [x0, #0x348]
    // 0x1293c6c: stur            x3, [fp, #-0x88]
    // 0x1293c70: StoreField: r3->field_b = r0
    //     0x1293c70: stur            w0, [x3, #0xb]
    // 0x1293c74: r0 = Instance__DeferringMouseCursor
    //     0x1293c74: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1293c78: ArrayStore: r3[0] = r0  ; List_4
    //     0x1293c78: stur            w0, [x3, #0x17]
    // 0x1293c7c: ldur            x1, [fp, #-0x80]
    // 0x1293c80: StoreField: r3->field_7 = r1
    //     0x1293c80: stur            w1, [x3, #7]
    // 0x1293c84: r1 = Null
    //     0x1293c84: mov             x1, NULL
    // 0x1293c88: r2 = 2
    //     0x1293c88: movz            x2, #0x2
    // 0x1293c8c: r0 = AllocateArray()
    //     0x1293c8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1293c90: mov             x2, x0
    // 0x1293c94: ldur            x0, [fp, #-0x88]
    // 0x1293c98: stur            x2, [fp, #-0x80]
    // 0x1293c9c: StoreField: r2->field_f = r0
    //     0x1293c9c: stur            w0, [x2, #0xf]
    // 0x1293ca0: r1 = <InlineSpan>
    //     0x1293ca0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x1293ca4: ldr             x1, [x1, #0xe40]
    // 0x1293ca8: r0 = AllocateGrowableArray()
    //     0x1293ca8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1293cac: mov             x1, x0
    // 0x1293cb0: ldur            x0, [fp, #-0x80]
    // 0x1293cb4: stur            x1, [fp, #-0x88]
    // 0x1293cb8: StoreField: r1->field_f = r0
    //     0x1293cb8: stur            w0, [x1, #0xf]
    // 0x1293cbc: r0 = 2
    //     0x1293cbc: movz            x0, #0x2
    // 0x1293cc0: StoreField: r1->field_b = r0
    //     0x1293cc0: stur            w0, [x1, #0xb]
    // 0x1293cc4: r0 = TextSpan()
    //     0x1293cc4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x1293cc8: mov             x1, x0
    // 0x1293ccc: ldur            x0, [fp, #-0x70]
    // 0x1293cd0: stur            x1, [fp, #-0x80]
    // 0x1293cd4: StoreField: r1->field_b = r0
    //     0x1293cd4: stur            w0, [x1, #0xb]
    // 0x1293cd8: ldur            x0, [fp, #-0x88]
    // 0x1293cdc: StoreField: r1->field_f = r0
    //     0x1293cdc: stur            w0, [x1, #0xf]
    // 0x1293ce0: r0 = Instance__DeferringMouseCursor
    //     0x1293ce0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x1293ce4: ArrayStore: r1[0] = r0  ; List_4
    //     0x1293ce4: stur            w0, [x1, #0x17]
    // 0x1293ce8: ldur            x0, [fp, #-0x78]
    // 0x1293cec: StoreField: r1->field_7 = r0
    //     0x1293cec: stur            w0, [x1, #7]
    // 0x1293cf0: r0 = RichText()
    //     0x1293cf0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x1293cf4: stur            x0, [fp, #-0x70]
    // 0x1293cf8: r16 = Instance_TextAlign
    //     0x1293cf8: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1293cfc: str             x16, [SP]
    // 0x1293d00: mov             x1, x0
    // 0x1293d04: ldur            x2, [fp, #-0x80]
    // 0x1293d08: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0x1293d08: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0x1293d0c: ldr             x4, [x4, #0x350]
    // 0x1293d10: r0 = RichText()
    //     0x1293d10: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x1293d14: r1 = Null
    //     0x1293d14: mov             x1, NULL
    // 0x1293d18: r2 = 6
    //     0x1293d18: movz            x2, #0x6
    // 0x1293d1c: r0 = AllocateArray()
    //     0x1293d1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1293d20: mov             x2, x0
    // 0x1293d24: ldur            x0, [fp, #-0x50]
    // 0x1293d28: stur            x2, [fp, #-0x78]
    // 0x1293d2c: StoreField: r2->field_f = r0
    //     0x1293d2c: stur            w0, [x2, #0xf]
    // 0x1293d30: r16 = Instance_VerticalDivider
    //     0x1293d30: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0x1293d34: ldr             x16, [x16, #0x760]
    // 0x1293d38: StoreField: r2->field_13 = r16
    //     0x1293d38: stur            w16, [x2, #0x13]
    // 0x1293d3c: ldur            x0, [fp, #-0x70]
    // 0x1293d40: ArrayStore: r2[0] = r0  ; List_4
    //     0x1293d40: stur            w0, [x2, #0x17]
    // 0x1293d44: r1 = <Widget>
    //     0x1293d44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1293d48: r0 = AllocateGrowableArray()
    //     0x1293d48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1293d4c: mov             x1, x0
    // 0x1293d50: ldur            x0, [fp, #-0x78]
    // 0x1293d54: stur            x1, [fp, #-0x50]
    // 0x1293d58: StoreField: r1->field_f = r0
    //     0x1293d58: stur            w0, [x1, #0xf]
    // 0x1293d5c: r0 = 6
    //     0x1293d5c: movz            x0, #0x6
    // 0x1293d60: StoreField: r1->field_b = r0
    //     0x1293d60: stur            w0, [x1, #0xb]
    // 0x1293d64: r0 = Row()
    //     0x1293d64: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1293d68: mov             x1, x0
    // 0x1293d6c: r0 = Instance_Axis
    //     0x1293d6c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1293d70: stur            x1, [fp, #-0x70]
    // 0x1293d74: StoreField: r1->field_f = r0
    //     0x1293d74: stur            w0, [x1, #0xf]
    // 0x1293d78: r0 = Instance_MainAxisAlignment
    //     0x1293d78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x1293d7c: ldr             x0, [x0, #0xa8]
    // 0x1293d80: StoreField: r1->field_13 = r0
    //     0x1293d80: stur            w0, [x1, #0x13]
    // 0x1293d84: r0 = Instance_MainAxisSize
    //     0x1293d84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1293d88: ldr             x0, [x0, #0xa10]
    // 0x1293d8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x1293d8c: stur            w0, [x1, #0x17]
    // 0x1293d90: r2 = Instance_CrossAxisAlignment
    //     0x1293d90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1293d94: ldr             x2, [x2, #0xa18]
    // 0x1293d98: StoreField: r1->field_1b = r2
    //     0x1293d98: stur            w2, [x1, #0x1b]
    // 0x1293d9c: r2 = Instance_VerticalDirection
    //     0x1293d9c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1293da0: ldr             x2, [x2, #0xa20]
    // 0x1293da4: StoreField: r1->field_23 = r2
    //     0x1293da4: stur            w2, [x1, #0x23]
    // 0x1293da8: r3 = Instance_Clip
    //     0x1293da8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1293dac: ldr             x3, [x3, #0x38]
    // 0x1293db0: StoreField: r1->field_2b = r3
    //     0x1293db0: stur            w3, [x1, #0x2b]
    // 0x1293db4: StoreField: r1->field_2f = rZR
    //     0x1293db4: stur            xzr, [x1, #0x2f]
    // 0x1293db8: ldur            x4, [fp, #-0x50]
    // 0x1293dbc: StoreField: r1->field_b = r4
    //     0x1293dbc: stur            w4, [x1, #0xb]
    // 0x1293dc0: r0 = Padding()
    //     0x1293dc0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1293dc4: mov             x1, x0
    // 0x1293dc8: r0 = Instance_EdgeInsets
    //     0x1293dc8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48768] Obj!EdgeInsets@d585b1
    //     0x1293dcc: ldr             x0, [x0, #0x768]
    // 0x1293dd0: stur            x1, [fp, #-0x50]
    // 0x1293dd4: StoreField: r1->field_f = r0
    //     0x1293dd4: stur            w0, [x1, #0xf]
    // 0x1293dd8: ldur            x0, [fp, #-0x70]
    // 0x1293ddc: StoreField: r1->field_b = r0
    //     0x1293ddc: stur            w0, [x1, #0xb]
    // 0x1293de0: r0 = IntrinsicHeight()
    //     0x1293de0: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x1293de4: mov             x1, x0
    // 0x1293de8: ldur            x0, [fp, #-0x50]
    // 0x1293dec: stur            x1, [fp, #-0x70]
    // 0x1293df0: StoreField: r1->field_b = r0
    //     0x1293df0: stur            w0, [x1, #0xb]
    // 0x1293df4: r0 = Container()
    //     0x1293df4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1293df8: stur            x0, [fp, #-0x50]
    // 0x1293dfc: r16 = 100.000000
    //     0x1293dfc: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x1293e00: ldur            lr, [fp, #-0x20]
    // 0x1293e04: stp             lr, x16, [SP, #8]
    // 0x1293e08: ldur            x16, [fp, #-0x70]
    // 0x1293e0c: str             x16, [SP]
    // 0x1293e10: mov             x1, x0
    // 0x1293e14: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x1293e14: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x1293e18: ldr             x4, [x4, #0xc78]
    // 0x1293e1c: r0 = Container()
    //     0x1293e1c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1293e20: r1 = <Path>
    //     0x1293e20: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0x1293e24: ldr             x1, [x1, #0xd30]
    // 0x1293e28: r0 = MovieTicketClipper()
    //     0x1293e28: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0x1293e2c: stur            x0, [fp, #-0x20]
    // 0x1293e30: r0 = ClipPath()
    //     0x1293e30: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0x1293e34: mov             x1, x0
    // 0x1293e38: ldur            x0, [fp, #-0x20]
    // 0x1293e3c: stur            x1, [fp, #-0x70]
    // 0x1293e40: StoreField: r1->field_f = r0
    //     0x1293e40: stur            w0, [x1, #0xf]
    // 0x1293e44: r0 = Instance_Clip
    //     0x1293e44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x1293e48: ldr             x0, [x0, #0x138]
    // 0x1293e4c: StoreField: r1->field_13 = r0
    //     0x1293e4c: stur            w0, [x1, #0x13]
    // 0x1293e50: ldur            x0, [fp, #-0x50]
    // 0x1293e54: StoreField: r1->field_b = r0
    //     0x1293e54: stur            w0, [x1, #0xb]
    // 0x1293e58: r0 = Visibility()
    //     0x1293e58: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1293e5c: mov             x2, x0
    // 0x1293e60: ldur            x0, [fp, #-0x70]
    // 0x1293e64: stur            x2, [fp, #-0x20]
    // 0x1293e68: StoreField: r2->field_b = r0
    //     0x1293e68: stur            w0, [x2, #0xb]
    // 0x1293e6c: r0 = Instance_SizedBox
    //     0x1293e6c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1293e70: StoreField: r2->field_f = r0
    //     0x1293e70: stur            w0, [x2, #0xf]
    // 0x1293e74: ldur            x1, [fp, #-0x40]
    // 0x1293e78: StoreField: r2->field_13 = r1
    //     0x1293e78: stur            w1, [x2, #0x13]
    // 0x1293e7c: r3 = false
    //     0x1293e7c: add             x3, NULL, #0x30  ; false
    // 0x1293e80: ArrayStore: r2[0] = r3  ; List_4
    //     0x1293e80: stur            w3, [x2, #0x17]
    // 0x1293e84: StoreField: r2->field_1b = r3
    //     0x1293e84: stur            w3, [x2, #0x1b]
    // 0x1293e88: StoreField: r2->field_1f = r3
    //     0x1293e88: stur            w3, [x2, #0x1f]
    // 0x1293e8c: StoreField: r2->field_23 = r3
    //     0x1293e8c: stur            w3, [x2, #0x23]
    // 0x1293e90: StoreField: r2->field_27 = r3
    //     0x1293e90: stur            w3, [x2, #0x27]
    // 0x1293e94: StoreField: r2->field_2b = r3
    //     0x1293e94: stur            w3, [x2, #0x2b]
    // 0x1293e98: ldur            x4, [fp, #-0x38]
    // 0x1293e9c: LoadField: r1 = r4->field_b
    //     0x1293e9c: ldur            w1, [x4, #0xb]
    // 0x1293ea0: LoadField: r5 = r4->field_f
    //     0x1293ea0: ldur            w5, [x4, #0xf]
    // 0x1293ea4: DecompressPointer r5
    //     0x1293ea4: add             x5, x5, HEAP, lsl #32
    // 0x1293ea8: LoadField: r6 = r5->field_b
    //     0x1293ea8: ldur            w6, [x5, #0xb]
    // 0x1293eac: r5 = LoadInt32Instr(r1)
    //     0x1293eac: sbfx            x5, x1, #1, #0x1f
    // 0x1293eb0: stur            x5, [fp, #-0x58]
    // 0x1293eb4: r1 = LoadInt32Instr(r6)
    //     0x1293eb4: sbfx            x1, x6, #1, #0x1f
    // 0x1293eb8: cmp             x5, x1
    // 0x1293ebc: b.ne            #0x1293ec8
    // 0x1293ec0: mov             x1, x4
    // 0x1293ec4: r0 = _growToNextCapacity()
    //     0x1293ec4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1293ec8: ldur            x5, [fp, #-8]
    // 0x1293ecc: ldur            x2, [fp, #-0x38]
    // 0x1293ed0: ldur            x6, [fp, #-0x30]
    // 0x1293ed4: ldur            x4, [fp, #-0x48]
    // 0x1293ed8: ldur            x3, [fp, #-0x58]
    // 0x1293edc: add             x7, x3, #1
    // 0x1293ee0: stur            x7, [fp, #-0x60]
    // 0x1293ee4: lsl             x0, x7, #1
    // 0x1293ee8: StoreField: r2->field_b = r0
    //     0x1293ee8: stur            w0, [x2, #0xb]
    // 0x1293eec: LoadField: r8 = r2->field_f
    //     0x1293eec: ldur            w8, [x2, #0xf]
    // 0x1293ef0: DecompressPointer r8
    //     0x1293ef0: add             x8, x8, HEAP, lsl #32
    // 0x1293ef4: mov             x1, x8
    // 0x1293ef8: ldur            x0, [fp, #-0x20]
    // 0x1293efc: stur            x8, [fp, #-0xa0]
    // 0x1293f00: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1293f00: add             x25, x1, x3, lsl #2
    //     0x1293f04: add             x25, x25, #0xf
    //     0x1293f08: str             w0, [x25]
    //     0x1293f0c: tbz             w0, #0, #0x1293f28
    //     0x1293f10: ldurb           w16, [x1, #-1]
    //     0x1293f14: ldurb           w17, [x0, #-1]
    //     0x1293f18: and             x16, x17, x16, lsr #2
    //     0x1293f1c: tst             x16, HEAP, lsr #32
    //     0x1293f20: b.eq            #0x1293f28
    //     0x1293f24: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1293f28: LoadField: r0 = r5->field_b
    //     0x1293f28: ldur            w0, [x5, #0xb]
    // 0x1293f2c: DecompressPointer r0
    //     0x1293f2c: add             x0, x0, HEAP, lsl #32
    // 0x1293f30: stur            x0, [fp, #-0x98]
    // 0x1293f34: LoadField: r1 = r5->field_1f
    //     0x1293f34: ldur            w1, [x5, #0x1f]
    // 0x1293f38: DecompressPointer r1
    //     0x1293f38: add             x1, x1, HEAP, lsl #32
    // 0x1293f3c: stur            x1, [fp, #-0x90]
    // 0x1293f40: LoadField: r3 = r5->field_2b
    //     0x1293f40: ldur            w3, [x5, #0x2b]
    // 0x1293f44: DecompressPointer r3
    //     0x1293f44: add             x3, x3, HEAP, lsl #32
    // 0x1293f48: stur            x3, [fp, #-0x88]
    // 0x1293f4c: LoadField: r9 = r5->field_27
    //     0x1293f4c: ldur            w9, [x5, #0x27]
    // 0x1293f50: DecompressPointer r9
    //     0x1293f50: add             x9, x9, HEAP, lsl #32
    // 0x1293f54: stur            x9, [fp, #-0x80]
    // 0x1293f58: LoadField: r10 = r5->field_2f
    //     0x1293f58: ldur            w10, [x5, #0x2f]
    // 0x1293f5c: DecompressPointer r10
    //     0x1293f5c: add             x10, x10, HEAP, lsl #32
    // 0x1293f60: stur            x10, [fp, #-0x78]
    // 0x1293f64: LoadField: r11 = r5->field_3f
    //     0x1293f64: ldur            w11, [x5, #0x3f]
    // 0x1293f68: DecompressPointer r11
    //     0x1293f68: add             x11, x11, HEAP, lsl #32
    // 0x1293f6c: stur            x11, [fp, #-0x70]
    // 0x1293f70: LoadField: r12 = r5->field_47
    //     0x1293f70: ldur            w12, [x5, #0x47]
    // 0x1293f74: DecompressPointer r12
    //     0x1293f74: add             x12, x12, HEAP, lsl #32
    // 0x1293f78: stur            x12, [fp, #-0x50]
    // 0x1293f7c: LoadField: r13 = r5->field_3b
    //     0x1293f7c: ldur            w13, [x5, #0x3b]
    // 0x1293f80: DecompressPointer r13
    //     0x1293f80: add             x13, x13, HEAP, lsl #32
    // 0x1293f84: stur            x13, [fp, #-0x40]
    // 0x1293f88: LoadField: r14 = r5->field_4b
    //     0x1293f88: ldur            w14, [x5, #0x4b]
    // 0x1293f8c: DecompressPointer r14
    //     0x1293f8c: add             x14, x14, HEAP, lsl #32
    // 0x1293f90: stur            x14, [fp, #-0x20]
    // 0x1293f94: r0 = _ProductGridItem()
    //     0x1293f94: bl              #0x12943e4  ; Allocate_ProductGridItemStub -> _ProductGridItem (size=0x3c)
    // 0x1293f98: mov             x2, x0
    // 0x1293f9c: ldur            x0, [fp, #-0x98]
    // 0x1293fa0: stur            x2, [fp, #-0xa8]
    // 0x1293fa4: StoreField: r2->field_b = r0
    //     0x1293fa4: stur            w0, [x2, #0xb]
    // 0x1293fa8: ldur            x0, [fp, #-0x90]
    // 0x1293fac: StoreField: r2->field_f = r0
    //     0x1293fac: stur            w0, [x2, #0xf]
    // 0x1293fb0: ldur            x0, [fp, #-0x30]
    // 0x1293fb4: ArrayStore: r2[0] = r0  ; List_4
    //     0x1293fb4: stur            w0, [x2, #0x17]
    // 0x1293fb8: ldur            x0, [fp, #-0x88]
    // 0x1293fbc: StoreField: r2->field_1b = r0
    //     0x1293fbc: stur            w0, [x2, #0x1b]
    // 0x1293fc0: ldur            x0, [fp, #-0x80]
    // 0x1293fc4: StoreField: r2->field_13 = r0
    //     0x1293fc4: stur            w0, [x2, #0x13]
    // 0x1293fc8: ldur            x0, [fp, #-0x78]
    // 0x1293fcc: StoreField: r2->field_1f = r0
    //     0x1293fcc: stur            w0, [x2, #0x1f]
    // 0x1293fd0: r0 = "product_page"
    //     0x1293fd0: add             x0, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1293fd4: ldr             x0, [x0, #0x480]
    // 0x1293fd8: StoreField: r2->field_23 = r0
    //     0x1293fd8: stur            w0, [x2, #0x23]
    // 0x1293fdc: ldur            x0, [fp, #-0x48]
    // 0x1293fe0: StoreField: r2->field_27 = r0
    //     0x1293fe0: stur            w0, [x2, #0x27]
    // 0x1293fe4: ldur            x0, [fp, #-0x70]
    // 0x1293fe8: StoreField: r2->field_2b = r0
    //     0x1293fe8: stur            w0, [x2, #0x2b]
    // 0x1293fec: ldur            x0, [fp, #-0x50]
    // 0x1293ff0: StoreField: r2->field_2f = r0
    //     0x1293ff0: stur            w0, [x2, #0x2f]
    // 0x1293ff4: ldur            x0, [fp, #-0x40]
    // 0x1293ff8: StoreField: r2->field_33 = r0
    //     0x1293ff8: stur            w0, [x2, #0x33]
    // 0x1293ffc: ldur            x0, [fp, #-0x20]
    // 0x1294000: StoreField: r2->field_37 = r0
    //     0x1294000: stur            w0, [x2, #0x37]
    // 0x1294004: ldur            x0, [fp, #-0xa0]
    // 0x1294008: LoadField: r1 = r0->field_b
    //     0x1294008: ldur            w1, [x0, #0xb]
    // 0x129400c: r0 = LoadInt32Instr(r1)
    //     0x129400c: sbfx            x0, x1, #1, #0x1f
    // 0x1294010: ldur            x3, [fp, #-0x60]
    // 0x1294014: cmp             x3, x0
    // 0x1294018: b.ne            #0x1294024
    // 0x129401c: ldur            x1, [fp, #-0x38]
    // 0x1294020: r0 = _growToNextCapacity()
    //     0x1294020: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1294024: ldur            x3, [fp, #-0x38]
    // 0x1294028: ldur            x2, [fp, #-0x60]
    // 0x129402c: add             x4, x2, #1
    // 0x1294030: stur            x4, [fp, #-0x58]
    // 0x1294034: lsl             x0, x4, #1
    // 0x1294038: StoreField: r3->field_b = r0
    //     0x1294038: stur            w0, [x3, #0xb]
    // 0x129403c: LoadField: r5 = r3->field_f
    //     0x129403c: ldur            w5, [x3, #0xf]
    // 0x1294040: DecompressPointer r5
    //     0x1294040: add             x5, x5, HEAP, lsl #32
    // 0x1294044: mov             x1, x5
    // 0x1294048: ldur            x0, [fp, #-0xa8]
    // 0x129404c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x129404c: add             x25, x1, x2, lsl #2
    //     0x1294050: add             x25, x25, #0xf
    //     0x1294054: str             w0, [x25]
    //     0x1294058: tbz             w0, #0, #0x1294074
    //     0x129405c: ldurb           w16, [x1, #-1]
    //     0x1294060: ldurb           w17, [x0, #-1]
    //     0x1294064: and             x16, x17, x16, lsr #2
    //     0x1294068: tst             x16, HEAP, lsr #32
    //     0x129406c: b.eq            #0x1294074
    //     0x1294070: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1294074: LoadField: r0 = r5->field_b
    //     0x1294074: ldur            w0, [x5, #0xb]
    // 0x1294078: r1 = LoadInt32Instr(r0)
    //     0x1294078: sbfx            x1, x0, #1, #0x1f
    // 0x129407c: cmp             x4, x1
    // 0x1294080: b.ne            #0x129408c
    // 0x1294084: mov             x1, x3
    // 0x1294088: r0 = _growToNextCapacity()
    //     0x1294088: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x129408c: ldur            x2, [fp, #-8]
    // 0x1294090: ldur            x0, [fp, #-0x38]
    // 0x1294094: ldur            x1, [fp, #-0x58]
    // 0x1294098: add             x3, x1, #1
    // 0x129409c: lsl             x4, x3, #1
    // 0x12940a0: StoreField: r0->field_b = r4
    //     0x12940a0: stur            w4, [x0, #0xb]
    // 0x12940a4: LoadField: r3 = r0->field_f
    //     0x12940a4: ldur            w3, [x0, #0xf]
    // 0x12940a8: DecompressPointer r3
    //     0x12940a8: add             x3, x3, HEAP, lsl #32
    // 0x12940ac: add             x4, x3, x1, lsl #2
    // 0x12940b0: r16 = Instance_SizedBox
    //     0x12940b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x12940b4: ldr             x16, [x16, #0x8f0]
    // 0x12940b8: StoreField: r4->field_f = r16
    //     0x12940b8: stur            w16, [x4, #0xf]
    // 0x12940bc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x12940bc: ldur            w3, [x2, #0x17]
    // 0x12940c0: DecompressPointer r3
    //     0x12940c0: add             x3, x3, HEAP, lsl #32
    // 0x12940c4: stur            x3, [fp, #-0x20]
    // 0x12940c8: cmp             w3, NULL
    // 0x12940cc: b.ne            #0x12940d8
    // 0x12940d0: r1 = Null
    //     0x12940d0: mov             x1, NULL
    // 0x12940d4: b               #0x1294104
    // 0x12940d8: LoadField: r1 = r3->field_7
    //     0x12940d8: ldur            w1, [x3, #7]
    // 0x12940dc: DecompressPointer r1
    //     0x12940dc: add             x1, x1, HEAP, lsl #32
    // 0x12940e0: cmp             w1, NULL
    // 0x12940e4: b.ne            #0x12940f0
    // 0x12940e8: r1 = Null
    //     0x12940e8: mov             x1, NULL
    // 0x12940ec: b               #0x1294104
    // 0x12940f0: LoadField: r2 = r1->field_7
    //     0x12940f0: ldur            w2, [x1, #7]
    // 0x12940f4: cbnz            w2, #0x1294100
    // 0x12940f8: r1 = false
    //     0x12940f8: add             x1, NULL, #0x30  ; false
    // 0x12940fc: b               #0x1294104
    // 0x1294100: r1 = true
    //     0x1294100: add             x1, NULL, #0x20  ; true
    // 0x1294104: cmp             w1, NULL
    // 0x1294108: b.ne            #0x1294114
    // 0x129410c: r2 = false
    //     0x129410c: add             x2, NULL, #0x30  ; false
    // 0x1294110: b               #0x1294118
    // 0x1294114: mov             x2, x1
    // 0x1294118: ldur            x1, [fp, #-0x10]
    // 0x129411c: stur            x2, [fp, #-8]
    // 0x1294120: r0 = of()
    //     0x1294120: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1294124: LoadField: r1 = r0->field_5b
    //     0x1294124: ldur            w1, [x0, #0x5b]
    // 0x1294128: DecompressPointer r1
    //     0x1294128: add             x1, x1, HEAP, lsl #32
    // 0x129412c: stur            x1, [fp, #-0x30]
    // 0x1294130: r0 = BoxDecoration()
    //     0x1294130: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1294134: mov             x2, x0
    // 0x1294138: ldur            x0, [fp, #-0x30]
    // 0x129413c: stur            x2, [fp, #-0x40]
    // 0x1294140: StoreField: r2->field_7 = r0
    //     0x1294140: stur            w0, [x2, #7]
    // 0x1294144: r0 = Instance_BorderRadius
    //     0x1294144: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0x1294148: ldr             x0, [x0, #0x460]
    // 0x129414c: StoreField: r2->field_13 = r0
    //     0x129414c: stur            w0, [x2, #0x13]
    // 0x1294150: r0 = Instance_BoxShape
    //     0x1294150: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1294154: ldr             x0, [x0, #0x80]
    // 0x1294158: StoreField: r2->field_23 = r0
    //     0x1294158: stur            w0, [x2, #0x23]
    // 0x129415c: ldur            x1, [fp, #-0x20]
    // 0x1294160: cmp             w1, NULL
    // 0x1294164: b.ne            #0x1294170
    // 0x1294168: r1 = Null
    //     0x1294168: mov             x1, NULL
    // 0x129416c: b               #0x129417c
    // 0x1294170: LoadField: r3 = r1->field_7
    //     0x1294170: ldur            w3, [x1, #7]
    // 0x1294174: DecompressPointer r3
    //     0x1294174: add             x3, x3, HEAP, lsl #32
    // 0x1294178: mov             x1, x3
    // 0x129417c: cmp             w1, NULL
    // 0x1294180: b.ne            #0x129418c
    // 0x1294184: r5 = ""
    //     0x1294184: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1294188: b               #0x1294190
    // 0x129418c: mov             x5, x1
    // 0x1294190: ldur            x3, [fp, #-0x38]
    // 0x1294194: ldur            x4, [fp, #-8]
    // 0x1294198: ldur            x1, [fp, #-0x10]
    // 0x129419c: stur            x5, [fp, #-0x20]
    // 0x12941a0: r0 = of()
    //     0x12941a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12941a4: LoadField: r1 = r0->field_87
    //     0x12941a4: ldur            w1, [x0, #0x87]
    // 0x12941a8: DecompressPointer r1
    //     0x12941a8: add             x1, x1, HEAP, lsl #32
    // 0x12941ac: LoadField: r0 = r1->field_2b
    //     0x12941ac: ldur            w0, [x1, #0x2b]
    // 0x12941b0: DecompressPointer r0
    //     0x12941b0: add             x0, x0, HEAP, lsl #32
    // 0x12941b4: r16 = 16.000000
    //     0x12941b4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x12941b8: ldr             x16, [x16, #0x188]
    // 0x12941bc: r30 = Instance_Color
    //     0x12941bc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12941c0: stp             lr, x16, [SP]
    // 0x12941c4: mov             x1, x0
    // 0x12941c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12941c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12941cc: ldr             x4, [x4, #0xaa0]
    // 0x12941d0: r0 = copyWith()
    //     0x12941d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12941d4: stur            x0, [fp, #-0x10]
    // 0x12941d8: r0 = Text()
    //     0x12941d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12941dc: mov             x1, x0
    // 0x12941e0: ldur            x0, [fp, #-0x20]
    // 0x12941e4: stur            x1, [fp, #-0x30]
    // 0x12941e8: StoreField: r1->field_b = r0
    //     0x12941e8: stur            w0, [x1, #0xb]
    // 0x12941ec: ldur            x0, [fp, #-0x10]
    // 0x12941f0: StoreField: r1->field_13 = r0
    //     0x12941f0: stur            w0, [x1, #0x13]
    // 0x12941f4: r0 = Center()
    //     0x12941f4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x12941f8: mov             x1, x0
    // 0x12941fc: r0 = Instance_Alignment
    //     0x12941fc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1294200: ldr             x0, [x0, #0xb10]
    // 0x1294204: stur            x1, [fp, #-0x10]
    // 0x1294208: StoreField: r1->field_f = r0
    //     0x1294208: stur            w0, [x1, #0xf]
    // 0x129420c: ldur            x0, [fp, #-0x30]
    // 0x1294210: StoreField: r1->field_b = r0
    //     0x1294210: stur            w0, [x1, #0xb]
    // 0x1294214: r0 = Container()
    //     0x1294214: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1294218: stur            x0, [fp, #-0x20]
    // 0x129421c: r16 = 40.000000
    //     0x129421c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0x1294220: ldr             x16, [x16, #8]
    // 0x1294224: r30 = 110.000000
    //     0x1294224: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0x1294228: ldr             lr, [lr, #0x770]
    // 0x129422c: stp             lr, x16, [SP, #0x10]
    // 0x1294230: ldur            x16, [fp, #-0x40]
    // 0x1294234: ldur            lr, [fp, #-0x10]
    // 0x1294238: stp             lr, x16, [SP]
    // 0x129423c: mov             x1, x0
    // 0x1294240: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x1294240: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x1294244: ldr             x4, [x4, #0x8c0]
    // 0x1294248: r0 = Container()
    //     0x1294248: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x129424c: r0 = InkWell()
    //     0x129424c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1294250: mov             x3, x0
    // 0x1294254: ldur            x0, [fp, #-0x20]
    // 0x1294258: stur            x3, [fp, #-0x10]
    // 0x129425c: StoreField: r3->field_b = r0
    //     0x129425c: stur            w0, [x3, #0xb]
    // 0x1294260: ldur            x2, [fp, #-0x28]
    // 0x1294264: r1 = Function '<anonymous closure>':.
    //     0x1294264: add             x1, PP, #0x48, lsl #12  ; [pp+0x48778] AnonymousClosure: (0x12943f0), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_detail_grid_item_view.dart] ProductDetailGridItemView::build (0x129349c)
    //     0x1294268: ldr             x1, [x1, #0x778]
    // 0x129426c: r0 = AllocateClosure()
    //     0x129426c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1294270: mov             x1, x0
    // 0x1294274: ldur            x0, [fp, #-0x10]
    // 0x1294278: StoreField: r0->field_f = r1
    //     0x1294278: stur            w1, [x0, #0xf]
    // 0x129427c: r1 = true
    //     0x129427c: add             x1, NULL, #0x20  ; true
    // 0x1294280: StoreField: r0->field_43 = r1
    //     0x1294280: stur            w1, [x0, #0x43]
    // 0x1294284: r2 = Instance_BoxShape
    //     0x1294284: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1294288: ldr             x2, [x2, #0x80]
    // 0x129428c: StoreField: r0->field_47 = r2
    //     0x129428c: stur            w2, [x0, #0x47]
    // 0x1294290: StoreField: r0->field_6f = r1
    //     0x1294290: stur            w1, [x0, #0x6f]
    // 0x1294294: r2 = false
    //     0x1294294: add             x2, NULL, #0x30  ; false
    // 0x1294298: StoreField: r0->field_73 = r2
    //     0x1294298: stur            w2, [x0, #0x73]
    // 0x129429c: StoreField: r0->field_83 = r1
    //     0x129429c: stur            w1, [x0, #0x83]
    // 0x12942a0: StoreField: r0->field_7b = r2
    //     0x12942a0: stur            w2, [x0, #0x7b]
    // 0x12942a4: r0 = Visibility()
    //     0x12942a4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x12942a8: mov             x2, x0
    // 0x12942ac: ldur            x0, [fp, #-0x10]
    // 0x12942b0: stur            x2, [fp, #-0x20]
    // 0x12942b4: StoreField: r2->field_b = r0
    //     0x12942b4: stur            w0, [x2, #0xb]
    // 0x12942b8: r0 = Instance_SizedBox
    //     0x12942b8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x12942bc: StoreField: r2->field_f = r0
    //     0x12942bc: stur            w0, [x2, #0xf]
    // 0x12942c0: ldur            x0, [fp, #-8]
    // 0x12942c4: StoreField: r2->field_13 = r0
    //     0x12942c4: stur            w0, [x2, #0x13]
    // 0x12942c8: r0 = false
    //     0x12942c8: add             x0, NULL, #0x30  ; false
    // 0x12942cc: ArrayStore: r2[0] = r0  ; List_4
    //     0x12942cc: stur            w0, [x2, #0x17]
    // 0x12942d0: StoreField: r2->field_1b = r0
    //     0x12942d0: stur            w0, [x2, #0x1b]
    // 0x12942d4: StoreField: r2->field_1f = r0
    //     0x12942d4: stur            w0, [x2, #0x1f]
    // 0x12942d8: StoreField: r2->field_23 = r0
    //     0x12942d8: stur            w0, [x2, #0x23]
    // 0x12942dc: StoreField: r2->field_27 = r0
    //     0x12942dc: stur            w0, [x2, #0x27]
    // 0x12942e0: StoreField: r2->field_2b = r0
    //     0x12942e0: stur            w0, [x2, #0x2b]
    // 0x12942e4: ldur            x0, [fp, #-0x38]
    // 0x12942e8: LoadField: r1 = r0->field_b
    //     0x12942e8: ldur            w1, [x0, #0xb]
    // 0x12942ec: LoadField: r3 = r0->field_f
    //     0x12942ec: ldur            w3, [x0, #0xf]
    // 0x12942f0: DecompressPointer r3
    //     0x12942f0: add             x3, x3, HEAP, lsl #32
    // 0x12942f4: LoadField: r4 = r3->field_b
    //     0x12942f4: ldur            w4, [x3, #0xb]
    // 0x12942f8: r3 = LoadInt32Instr(r1)
    //     0x12942f8: sbfx            x3, x1, #1, #0x1f
    // 0x12942fc: stur            x3, [fp, #-0x58]
    // 0x1294300: r1 = LoadInt32Instr(r4)
    //     0x1294300: sbfx            x1, x4, #1, #0x1f
    // 0x1294304: cmp             x3, x1
    // 0x1294308: b.ne            #0x1294314
    // 0x129430c: mov             x1, x0
    // 0x1294310: r0 = _growToNextCapacity()
    //     0x1294310: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1294314: ldur            x2, [fp, #-0x38]
    // 0x1294318: ldur            x4, [fp, #-0x18]
    // 0x129431c: ldur            x3, [fp, #-0x58]
    // 0x1294320: add             x0, x3, #1
    // 0x1294324: lsl             x1, x0, #1
    // 0x1294328: StoreField: r2->field_b = r1
    //     0x1294328: stur            w1, [x2, #0xb]
    // 0x129432c: LoadField: r1 = r2->field_f
    //     0x129432c: ldur            w1, [x2, #0xf]
    // 0x1294330: DecompressPointer r1
    //     0x1294330: add             x1, x1, HEAP, lsl #32
    // 0x1294334: ldur            x0, [fp, #-0x20]
    // 0x1294338: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1294338: add             x25, x1, x3, lsl #2
    //     0x129433c: add             x25, x25, #0xf
    //     0x1294340: str             w0, [x25]
    //     0x1294344: tbz             w0, #0, #0x1294360
    //     0x1294348: ldurb           w16, [x1, #-1]
    //     0x129434c: ldurb           w17, [x0, #-1]
    //     0x1294350: and             x16, x17, x16, lsr #2
    //     0x1294354: tst             x16, HEAP, lsr #32
    //     0x1294358: b.eq            #0x1294360
    //     0x129435c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1294360: r0 = Column()
    //     0x1294360: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1294364: mov             x1, x0
    // 0x1294368: r0 = Instance_Axis
    //     0x1294368: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x129436c: stur            x1, [fp, #-8]
    // 0x1294370: StoreField: r1->field_f = r0
    //     0x1294370: stur            w0, [x1, #0xf]
    // 0x1294374: r0 = Instance_MainAxisAlignment
    //     0x1294374: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1294378: ldr             x0, [x0, #0xa08]
    // 0x129437c: StoreField: r1->field_13 = r0
    //     0x129437c: stur            w0, [x1, #0x13]
    // 0x1294380: r0 = Instance_MainAxisSize
    //     0x1294380: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1294384: ldr             x0, [x0, #0xa10]
    // 0x1294388: ArrayStore: r1[0] = r0  ; List_4
    //     0x1294388: stur            w0, [x1, #0x17]
    // 0x129438c: ldur            x0, [fp, #-0x18]
    // 0x1294390: StoreField: r1->field_1b = r0
    //     0x1294390: stur            w0, [x1, #0x1b]
    // 0x1294394: r0 = Instance_VerticalDirection
    //     0x1294394: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1294398: ldr             x0, [x0, #0xa20]
    // 0x129439c: StoreField: r1->field_23 = r0
    //     0x129439c: stur            w0, [x1, #0x23]
    // 0x12943a0: r0 = Instance_Clip
    //     0x12943a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12943a4: ldr             x0, [x0, #0x38]
    // 0x12943a8: StoreField: r1->field_2b = r0
    //     0x12943a8: stur            w0, [x1, #0x2b]
    // 0x12943ac: StoreField: r1->field_2f = rZR
    //     0x12943ac: stur            xzr, [x1, #0x2f]
    // 0x12943b0: ldur            x0, [fp, #-0x38]
    // 0x12943b4: StoreField: r1->field_b = r0
    //     0x12943b4: stur            w0, [x1, #0xb]
    // 0x12943b8: r0 = Padding()
    //     0x12943b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12943bc: r1 = Instance_EdgeInsets
    //     0x12943bc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3edd8] Obj!EdgeInsets@d58f41
    //     0x12943c0: ldr             x1, [x1, #0xdd8]
    // 0x12943c4: StoreField: r0->field_f = r1
    //     0x12943c4: stur            w1, [x0, #0xf]
    // 0x12943c8: ldur            x1, [fp, #-8]
    // 0x12943cc: StoreField: r0->field_b = r1
    //     0x12943cc: stur            w1, [x0, #0xb]
    // 0x12943d0: LeaveFrame
    //     0x12943d0: mov             SP, fp
    //     0x12943d4: ldp             fp, lr, [SP], #0x10
    // 0x12943d8: ret
    //     0x12943d8: ret             
    // 0x12943dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12943dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12943e0: b               #0x12934c4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x12943f0, size: 0xc0
    // 0x12943f0: EnterFrame
    //     0x12943f0: stp             fp, lr, [SP, #-0x10]!
    //     0x12943f4: mov             fp, SP
    // 0x12943f8: AllocStack(0x38)
    //     0x12943f8: sub             SP, SP, #0x38
    // 0x12943fc: SetupParameters()
    //     0x12943fc: ldr             x0, [fp, #0x10]
    //     0x1294400: ldur            w1, [x0, #0x17]
    //     0x1294404: add             x1, x1, HEAP, lsl #32
    // 0x1294408: CheckStackOverflow
    //     0x1294408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x129440c: cmp             SP, x16
    //     0x1294410: b.ls            #0x12944a8
    // 0x1294414: LoadField: r0 = r1->field_f
    //     0x1294414: ldur            w0, [x1, #0xf]
    // 0x1294418: DecompressPointer r0
    //     0x1294418: add             x0, x0, HEAP, lsl #32
    // 0x129441c: LoadField: r1 = r0->field_2b
    //     0x129441c: ldur            w1, [x0, #0x2b]
    // 0x1294420: DecompressPointer r1
    //     0x1294420: add             x1, x1, HEAP, lsl #32
    // 0x1294424: LoadField: r2 = r0->field_27
    //     0x1294424: ldur            w2, [x0, #0x27]
    // 0x1294428: DecompressPointer r2
    //     0x1294428: add             x2, x2, HEAP, lsl #32
    // 0x129442c: LoadField: r3 = r0->field_2f
    //     0x129442c: ldur            w3, [x0, #0x2f]
    // 0x1294430: DecompressPointer r3
    //     0x1294430: add             x3, x3, HEAP, lsl #32
    // 0x1294434: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x1294434: ldur            w4, [x0, #0x17]
    // 0x1294438: DecompressPointer r4
    //     0x1294438: add             x4, x4, HEAP, lsl #32
    // 0x129443c: cmp             w4, NULL
    // 0x1294440: b.ne            #0x129444c
    // 0x1294444: r4 = Null
    //     0x1294444: mov             x4, NULL
    // 0x1294448: b               #0x1294458
    // 0x129444c: LoadField: r5 = r4->field_b
    //     0x129444c: ldur            w5, [x4, #0xb]
    // 0x1294450: DecompressPointer r5
    //     0x1294450: add             x5, x5, HEAP, lsl #32
    // 0x1294454: mov             x4, x5
    // 0x1294458: LoadField: r5 = r0->field_f
    //     0x1294458: ldur            w5, [x0, #0xf]
    // 0x129445c: DecompressPointer r5
    //     0x129445c: add             x5, x5, HEAP, lsl #32
    // 0x1294460: LoadField: r6 = r0->field_43
    //     0x1294460: ldur            w6, [x0, #0x43]
    // 0x1294464: DecompressPointer r6
    //     0x1294464: add             x6, x6, HEAP, lsl #32
    // 0x1294468: stp             x1, x6, [SP, #0x28]
    // 0x129446c: r16 = "product_page"
    //     0x129446c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1294470: ldr             x16, [x16, #0x480]
    // 0x1294474: stp             x16, x2, [SP, #0x18]
    // 0x1294478: stp             x4, x3, [SP, #8]
    // 0x129447c: str             x5, [SP]
    // 0x1294480: r4 = 0
    //     0x1294480: movz            x4, #0
    // 0x1294484: ldr             x0, [SP, #0x30]
    // 0x1294488: r16 = UnlinkedCall_0x613b5c
    //     0x1294488: add             x16, PP, #0x48, lsl #12  ; [pp+0x48780] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x129448c: add             x16, x16, #0x780
    // 0x1294490: ldp             x5, lr, [x16]
    // 0x1294494: blr             lr
    // 0x1294498: r0 = Null
    //     0x1294498: mov             x0, NULL
    // 0x129449c: LeaveFrame
    //     0x129449c: mov             SP, fp
    //     0x12944a0: ldp             fp, lr, [SP], #0x10
    // 0x12944a4: ret
    //     0x12944a4: ret             
    // 0x12944a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12944a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12944ac: b               #0x1294414
  }
}
