// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart

// class id: 1049397, size: 0x8
class :: {
}

// class id: 4494, size: 0x2c, field offset: 0xc
class BagBottomSheet extends StatelessWidget {

  [closure] BagImage <anonymous closure>(dynamic) {
    // ** addr: 0x8fb1b8, size: 0x18
    // 0x8fb1b8: EnterFrame
    //     0x8fb1b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8fb1bc: mov             fp, SP
    // 0x8fb1c0: r0 = BagImage()
    //     0x8fb1c0: bl              #0x8a272c  ; AllocateBagImageStub -> BagImage (size=0x20)
    // 0x8fb1c4: LeaveFrame
    //     0x8fb1c4: mov             SP, fp
    //     0x8fb1c8: ldp             fp, lr, [SP], #0x10
    // 0x8fb1cc: ret
    //     0x8fb1cc: ret             
  }
  _ _buildProductItem(/* No info */) {
    // ** addr: 0x8fb1d0, size: 0xa78
    // 0x8fb1d0: EnterFrame
    //     0x8fb1d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8fb1d4: mov             fp, SP
    // 0x8fb1d8: AllocStack(0xb8)
    //     0x8fb1d8: sub             SP, SP, #0xb8
    // 0x8fb1dc: SetupParameters(BagBottomSheet this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x8fb1dc: mov             x3, x1
    //     0x8fb1e0: mov             x0, x2
    //     0x8fb1e4: stur            x1, [fp, #-8]
    //     0x8fb1e8: stur            x2, [fp, #-0x10]
    // 0x8fb1ec: CheckStackOverflow
    //     0x8fb1ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fb1f0: cmp             SP, x16
    //     0x8fb1f4: b.ls            #0x8fbc40
    // 0x8fb1f8: LoadField: r1 = r0->field_1b
    //     0x8fb1f8: ldur            w1, [x0, #0x1b]
    // 0x8fb1fc: DecompressPointer r1
    //     0x8fb1fc: add             x1, x1, HEAP, lsl #32
    // 0x8fb200: cmp             w1, NULL
    // 0x8fb204: b.ne            #0x8fb220
    // 0x8fb208: r1 = <BagImage>
    //     0x8fb208: add             x1, PP, #0x25, lsl #12  ; [pp+0x25528] TypeArguments: <BagImage>
    //     0x8fb20c: ldr             x1, [x1, #0x528]
    // 0x8fb210: r2 = 0
    //     0x8fb210: movz            x2, #0
    // 0x8fb214: r0 = AllocateArray()
    //     0x8fb214: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fb218: mov             x2, x0
    // 0x8fb21c: b               #0x8fb224
    // 0x8fb220: mov             x2, x1
    // 0x8fb224: stur            x2, [fp, #-0x18]
    // 0x8fb228: r0 = LoadClassIdInstr(r2)
    //     0x8fb228: ldur            x0, [x2, #-1]
    //     0x8fb22c: ubfx            x0, x0, #0xc, #0x14
    // 0x8fb230: mov             x1, x2
    // 0x8fb234: r0 = GDT[cid_x0 + 0xe517]()
    //     0x8fb234: movz            x17, #0xe517
    //     0x8fb238: add             lr, x0, x17
    //     0x8fb23c: ldr             lr, [x21, lr, lsl #3]
    //     0x8fb240: blr             lr
    // 0x8fb244: tbnz            w0, #4, #0x8fb288
    // 0x8fb248: r1 = Function '<anonymous closure>':.
    //     0x8fb248: add             x1, PP, #0x48, lsl #12  ; [pp+0x48900] AnonymousClosure: (0x901144), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x8fb24c: ldr             x1, [x1, #0x900]
    // 0x8fb250: r2 = Null
    //     0x8fb250: mov             x2, NULL
    // 0x8fb254: r0 = AllocateClosure()
    //     0x8fb254: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fb258: r1 = Function '<anonymous closure>':.
    //     0x8fb258: add             x1, PP, #0x48, lsl #12  ; [pp+0x48908] AnonymousClosure: (0x8fb1b8), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductItem (0x8fb1d0)
    //     0x8fb25c: ldr             x1, [x1, #0x908]
    // 0x8fb260: r2 = Null
    //     0x8fb260: mov             x2, NULL
    // 0x8fb264: stur            x0, [fp, #-0x20]
    // 0x8fb268: r0 = AllocateClosure()
    //     0x8fb268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fb26c: str             x0, [SP]
    // 0x8fb270: ldur            x1, [fp, #-0x18]
    // 0x8fb274: ldur            x2, [fp, #-0x20]
    // 0x8fb278: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x8fb278: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x8fb27c: ldr             x4, [x4, #0xb48]
    // 0x8fb280: r0 = firstWhere()
    //     0x8fb280: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x8fb284: b               #0x8fb28c
    // 0x8fb288: r0 = Null
    //     0x8fb288: mov             x0, NULL
    // 0x8fb28c: cmp             w0, NULL
    // 0x8fb290: b.ne            #0x8fb29c
    // 0x8fb294: r0 = Null
    //     0x8fb294: mov             x0, NULL
    // 0x8fb298: b               #0x8fb2a8
    // 0x8fb29c: LoadField: r1 = r0->field_b
    //     0x8fb29c: ldur            w1, [x0, #0xb]
    // 0x8fb2a0: DecompressPointer r1
    //     0x8fb2a0: add             x1, x1, HEAP, lsl #32
    // 0x8fb2a4: mov             x0, x1
    // 0x8fb2a8: cmp             w0, NULL
    // 0x8fb2ac: b.ne            #0x8fb2b8
    // 0x8fb2b0: r2 = ""
    //     0x8fb2b0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fb2b4: b               #0x8fb2bc
    // 0x8fb2b8: mov             x2, x0
    // 0x8fb2bc: ldur            x0, [fp, #-0x10]
    // 0x8fb2c0: stur            x2, [fp, #-0x60]
    // 0x8fb2c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8fb2c4: ldur            w1, [x0, #0x17]
    // 0x8fb2c8: DecompressPointer r1
    //     0x8fb2c8: add             x1, x1, HEAP, lsl #32
    // 0x8fb2cc: cmp             w1, NULL
    // 0x8fb2d0: b.ne            #0x8fb2dc
    // 0x8fb2d4: r1 = Null
    //     0x8fb2d4: mov             x1, NULL
    // 0x8fb2d8: b               #0x8fb2e8
    // 0x8fb2dc: LoadField: r3 = r1->field_7
    //     0x8fb2dc: ldur            w3, [x1, #7]
    // 0x8fb2e0: DecompressPointer r3
    //     0x8fb2e0: add             x3, x3, HEAP, lsl #32
    // 0x8fb2e4: mov             x1, x3
    // 0x8fb2e8: cmp             w1, NULL
    // 0x8fb2ec: b.ne            #0x8fb2f4
    // 0x8fb2f0: r1 = ""
    //     0x8fb2f0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fb2f4: stur            x1, [fp, #-0x58]
    // 0x8fb2f8: LoadField: r3 = r0->field_53
    //     0x8fb2f8: ldur            w3, [x0, #0x53]
    // 0x8fb2fc: DecompressPointer r3
    //     0x8fb2fc: add             x3, x3, HEAP, lsl #32
    // 0x8fb300: cmp             w3, NULL
    // 0x8fb304: b.ne            #0x8fb30c
    // 0x8fb308: r3 = ""
    //     0x8fb308: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fb30c: stur            x3, [fp, #-0x50]
    // 0x8fb310: LoadField: r4 = r0->field_5f
    //     0x8fb310: ldur            w4, [x0, #0x5f]
    // 0x8fb314: DecompressPointer r4
    //     0x8fb314: add             x4, x4, HEAP, lsl #32
    // 0x8fb318: cmp             w4, NULL
    // 0x8fb31c: b.ne            #0x8fb328
    // 0x8fb320: r4 = 0
    //     0x8fb320: movz            x4, #0
    // 0x8fb324: b               #0x8fb338
    // 0x8fb328: r5 = LoadInt32Instr(r4)
    //     0x8fb328: sbfx            x5, x4, #1, #0x1f
    //     0x8fb32c: tbz             w4, #0, #0x8fb334
    //     0x8fb330: ldur            x5, [x4, #7]
    // 0x8fb334: mov             x4, x5
    // 0x8fb338: stur            x4, [fp, #-0x48]
    // 0x8fb33c: LoadField: r5 = r0->field_3f
    //     0x8fb33c: ldur            w5, [x0, #0x3f]
    // 0x8fb340: DecompressPointer r5
    //     0x8fb340: add             x5, x5, HEAP, lsl #32
    // 0x8fb344: cmp             w5, NULL
    // 0x8fb348: b.ne            #0x8fb350
    // 0x8fb34c: r5 = ""
    //     0x8fb34c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fb350: stur            x5, [fp, #-0x40]
    // 0x8fb354: LoadField: r6 = r0->field_2f
    //     0x8fb354: ldur            w6, [x0, #0x2f]
    // 0x8fb358: DecompressPointer r6
    //     0x8fb358: add             x6, x6, HEAP, lsl #32
    // 0x8fb35c: cmp             w6, NULL
    // 0x8fb360: b.ne            #0x8fb368
    // 0x8fb364: r6 = ""
    //     0x8fb364: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8fb368: stur            x6, [fp, #-0x38]
    // 0x8fb36c: LoadField: r7 = r0->field_6b
    //     0x8fb36c: ldur            w7, [x0, #0x6b]
    // 0x8fb370: DecompressPointer r7
    //     0x8fb370: add             x7, x7, HEAP, lsl #32
    // 0x8fb374: cmp             w7, NULL
    // 0x8fb378: b.ne            #0x8fb384
    // 0x8fb37c: r7 = Null
    //     0x8fb37c: mov             x7, NULL
    // 0x8fb380: b               #0x8fb398
    // 0x8fb384: LoadField: r8 = r7->field_b
    //     0x8fb384: ldur            w8, [x7, #0xb]
    // 0x8fb388: cbnz            w8, #0x8fb394
    // 0x8fb38c: r7 = false
    //     0x8fb38c: add             x7, NULL, #0x30  ; false
    // 0x8fb390: b               #0x8fb398
    // 0x8fb394: r7 = true
    //     0x8fb394: add             x7, NULL, #0x20  ; true
    // 0x8fb398: cmp             w7, NULL
    // 0x8fb39c: b.ne            #0x8fb3a4
    // 0x8fb3a0: r7 = false
    //     0x8fb3a0: add             x7, NULL, #0x30  ; false
    // 0x8fb3a4: stur            x7, [fp, #-0x30]
    // 0x8fb3a8: LoadField: r8 = r0->field_b
    //     0x8fb3a8: ldur            w8, [x0, #0xb]
    // 0x8fb3ac: DecompressPointer r8
    //     0x8fb3ac: add             x8, x8, HEAP, lsl #32
    // 0x8fb3b0: stur            x8, [fp, #-0x28]
    // 0x8fb3b4: LoadField: r9 = r0->field_1f
    //     0x8fb3b4: ldur            w9, [x0, #0x1f]
    // 0x8fb3b8: DecompressPointer r9
    //     0x8fb3b8: add             x9, x9, HEAP, lsl #32
    // 0x8fb3bc: cmp             w9, NULL
    // 0x8fb3c0: b.ne            #0x8fb3cc
    // 0x8fb3c4: r9 = Null
    //     0x8fb3c4: mov             x9, NULL
    // 0x8fb3c8: b               #0x8fb3d8
    // 0x8fb3cc: LoadField: r10 = r9->field_b
    //     0x8fb3cc: ldur            w10, [x9, #0xb]
    // 0x8fb3d0: DecompressPointer r10
    //     0x8fb3d0: add             x10, x10, HEAP, lsl #32
    // 0x8fb3d4: mov             x9, x10
    // 0x8fb3d8: stur            x9, [fp, #-0x20]
    // 0x8fb3dc: LoadField: r10 = r0->field_83
    //     0x8fb3dc: ldur            w10, [x0, #0x83]
    // 0x8fb3e0: DecompressPointer r10
    //     0x8fb3e0: add             x10, x10, HEAP, lsl #32
    // 0x8fb3e4: stur            x10, [fp, #-0x18]
    // 0x8fb3e8: r0 = Radius()
    //     0x8fb3e8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8fb3ec: d0 = 12.000000
    //     0x8fb3ec: fmov            d0, #12.00000000
    // 0x8fb3f0: stur            x0, [fp, #-0x10]
    // 0x8fb3f4: StoreField: r0->field_7 = d0
    //     0x8fb3f4: stur            d0, [x0, #7]
    // 0x8fb3f8: StoreField: r0->field_f = d0
    //     0x8fb3f8: stur            d0, [x0, #0xf]
    // 0x8fb3fc: r0 = BorderRadius()
    //     0x8fb3fc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8fb400: mov             x1, x0
    // 0x8fb404: ldur            x0, [fp, #-0x10]
    // 0x8fb408: stur            x1, [fp, #-0x68]
    // 0x8fb40c: StoreField: r1->field_7 = r0
    //     0x8fb40c: stur            w0, [x1, #7]
    // 0x8fb410: StoreField: r1->field_b = r0
    //     0x8fb410: stur            w0, [x1, #0xb]
    // 0x8fb414: StoreField: r1->field_f = r0
    //     0x8fb414: stur            w0, [x1, #0xf]
    // 0x8fb418: StoreField: r1->field_13 = r0
    //     0x8fb418: stur            w0, [x1, #0x13]
    // 0x8fb41c: r0 = CachedNetworkImage()
    //     0x8fb41c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x8fb420: r1 = Function '<anonymous closure>':.
    //     0x8fb420: add             x1, PP, #0x48, lsl #12  ; [pp+0x48910] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x8fb424: ldr             x1, [x1, #0x910]
    // 0x8fb428: r2 = Null
    //     0x8fb428: mov             x2, NULL
    // 0x8fb42c: stur            x0, [fp, #-0x10]
    // 0x8fb430: r0 = AllocateClosure()
    //     0x8fb430: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fb434: r1 = Function '<anonymous closure>':.
    //     0x8fb434: add             x1, PP, #0x48, lsl #12  ; [pp+0x48918] AnonymousClosure: (0x8fc4fc), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductItem (0x8fb1d0)
    //     0x8fb438: ldr             x1, [x1, #0x918]
    // 0x8fb43c: r2 = Null
    //     0x8fb43c: mov             x2, NULL
    // 0x8fb440: stur            x0, [fp, #-0x70]
    // 0x8fb444: r0 = AllocateClosure()
    //     0x8fb444: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fb448: r16 = 80.000000
    //     0x8fb448: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x8fb44c: ldr             x16, [x16, #0x2f8]
    // 0x8fb450: r30 = 80.000000
    //     0x8fb450: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x8fb454: ldr             lr, [lr, #0x2f8]
    // 0x8fb458: stp             lr, x16, [SP, #0x28]
    // 0x8fb45c: r16 = Instance_BoxFit
    //     0x8fb45c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x8fb460: ldr             x16, [x16, #0x118]
    // 0x8fb464: r30 = 320
    //     0x8fb464: movz            lr, #0x140
    // 0x8fb468: stp             lr, x16, [SP, #0x18]
    // 0x8fb46c: r16 = 320
    //     0x8fb46c: movz            x16, #0x140
    // 0x8fb470: ldur            lr, [fp, #-0x70]
    // 0x8fb474: stp             lr, x16, [SP, #8]
    // 0x8fb478: str             x0, [SP]
    // 0x8fb47c: ldur            x1, [fp, #-0x10]
    // 0x8fb480: ldur            x2, [fp, #-0x60]
    // 0x8fb484: r4 = const [0, 0x9, 0x7, 0x2, errorWidget, 0x8, fit, 0x4, height, 0x2, memCacheHeight, 0x6, memCacheWidth, 0x5, progressIndicatorBuilder, 0x7, width, 0x3, null]
    //     0x8fb484: add             x4, PP, #0x48, lsl #12  ; [pp+0x48920] List(19) [0, 0x9, 0x7, 0x2, "errorWidget", 0x8, "fit", 0x4, "height", 0x2, "memCacheHeight", 0x6, "memCacheWidth", 0x5, "progressIndicatorBuilder", 0x7, "width", 0x3, Null]
    //     0x8fb488: ldr             x4, [x4, #0x920]
    // 0x8fb48c: r0 = CachedNetworkImage()
    //     0x8fb48c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x8fb490: r0 = ClipRRect()
    //     0x8fb490: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x8fb494: mov             x2, x0
    // 0x8fb498: ldur            x0, [fp, #-0x68]
    // 0x8fb49c: stur            x2, [fp, #-0x60]
    // 0x8fb4a0: StoreField: r2->field_f = r0
    //     0x8fb4a0: stur            w0, [x2, #0xf]
    // 0x8fb4a4: r0 = Instance_Clip
    //     0x8fb4a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x8fb4a8: ldr             x0, [x0, #0x138]
    // 0x8fb4ac: ArrayStore: r2[0] = r0  ; List_4
    //     0x8fb4ac: stur            w0, [x2, #0x17]
    // 0x8fb4b0: ldur            x0, [fp, #-0x10]
    // 0x8fb4b4: StoreField: r2->field_b = r0
    //     0x8fb4b4: stur            w0, [x2, #0xb]
    // 0x8fb4b8: ldur            x1, [fp, #-0x58]
    // 0x8fb4bc: r0 = capitalizeFirstWord()
    //     0x8fb4bc: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x8fb4c0: stur            x0, [fp, #-0x10]
    // 0x8fb4c4: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x8fb4c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8fb4c8: ldr             x0, [x0, #0x1ab0]
    //     0x8fb4cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8fb4d0: cmp             w0, w16
    //     0x8fb4d4: b.ne            #0x8fb4e4
    //     0x8fb4d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x8fb4dc: ldr             x2, [x2, #0x60]
    //     0x8fb4e0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8fb4e4: LoadField: r2 = r0->field_87
    //     0x8fb4e4: ldur            w2, [x0, #0x87]
    // 0x8fb4e8: DecompressPointer r2
    //     0x8fb4e8: add             x2, x2, HEAP, lsl #32
    // 0x8fb4ec: stur            x2, [fp, #-0x70]
    // 0x8fb4f0: LoadField: r0 = r2->field_7
    //     0x8fb4f0: ldur            w0, [x2, #7]
    // 0x8fb4f4: DecompressPointer r0
    //     0x8fb4f4: add             x0, x0, HEAP, lsl #32
    // 0x8fb4f8: stur            x0, [fp, #-0x68]
    // 0x8fb4fc: r16 = 12.000000
    //     0x8fb4fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x8fb500: ldr             x16, [x16, #0x9e8]
    // 0x8fb504: r30 = Instance_Color
    //     0x8fb504: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8fb508: stp             lr, x16, [SP]
    // 0x8fb50c: mov             x1, x0
    // 0x8fb510: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x8fb510: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x8fb514: ldr             x4, [x4, #0xaa0]
    // 0x8fb518: r0 = copyWith()
    //     0x8fb518: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8fb51c: stur            x0, [fp, #-0x78]
    // 0x8fb520: r0 = Text()
    //     0x8fb520: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x8fb524: mov             x1, x0
    // 0x8fb528: ldur            x0, [fp, #-0x10]
    // 0x8fb52c: stur            x1, [fp, #-0x80]
    // 0x8fb530: StoreField: r1->field_b = r0
    //     0x8fb530: stur            w0, [x1, #0xb]
    // 0x8fb534: ldur            x0, [fp, #-0x78]
    // 0x8fb538: StoreField: r1->field_13 = r0
    //     0x8fb538: stur            w0, [x1, #0x13]
    // 0x8fb53c: r2 = 4
    //     0x8fb53c: movz            x2, #0x4
    // 0x8fb540: StoreField: r1->field_37 = r2
    //     0x8fb540: stur            w2, [x1, #0x37]
    // 0x8fb544: r0 = SizedBox()
    //     0x8fb544: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x8fb548: mov             x1, x0
    // 0x8fb54c: r0 = 200.000000
    //     0x8fb54c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0x8fb550: ldr             x0, [x0, #0x570]
    // 0x8fb554: stur            x1, [fp, #-0x10]
    // 0x8fb558: StoreField: r1->field_f = r0
    //     0x8fb558: stur            w0, [x1, #0xf]
    // 0x8fb55c: ldur            x0, [fp, #-0x80]
    // 0x8fb560: StoreField: r1->field_b = r0
    //     0x8fb560: stur            w0, [x1, #0xb]
    // 0x8fb564: r0 = Padding()
    //     0x8fb564: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x8fb568: mov             x3, x0
    // 0x8fb56c: r0 = Instance_EdgeInsets
    //     0x8fb56c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48928] Obj!EdgeInsets@d56f01
    //     0x8fb570: ldr             x0, [x0, #0x928]
    // 0x8fb574: stur            x3, [fp, #-0x78]
    // 0x8fb578: StoreField: r3->field_f = r0
    //     0x8fb578: stur            w0, [x3, #0xf]
    // 0x8fb57c: ldur            x0, [fp, #-0x10]
    // 0x8fb580: StoreField: r3->field_b = r0
    //     0x8fb580: stur            w0, [x3, #0xb]
    // 0x8fb584: r1 = Null
    //     0x8fb584: mov             x1, NULL
    // 0x8fb588: r2 = 4
    //     0x8fb588: movz            x2, #0x4
    // 0x8fb58c: r0 = AllocateArray()
    //     0x8fb58c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fb590: r16 = "Size: "
    //     0x8fb590: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x8fb594: ldr             x16, [x16, #0xf00]
    // 0x8fb598: StoreField: r0->field_f = r16
    //     0x8fb598: stur            w16, [x0, #0xf]
    // 0x8fb59c: ldur            x1, [fp, #-0x50]
    // 0x8fb5a0: StoreField: r0->field_13 = r1
    //     0x8fb5a0: stur            w1, [x0, #0x13]
    // 0x8fb5a4: str             x0, [SP]
    // 0x8fb5a8: r0 = _interpolate()
    //     0x8fb5a8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8fb5ac: r1 = Instance_Color
    //     0x8fb5ac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8fb5b0: d0 = 0.700000
    //     0x8fb5b0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x8fb5b4: ldr             d0, [x17, #0xf48]
    // 0x8fb5b8: stur            x0, [fp, #-0x10]
    // 0x8fb5bc: r0 = withOpacity()
    //     0x8fb5bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8fb5c0: r16 = 12.000000
    //     0x8fb5c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x8fb5c4: ldr             x16, [x16, #0x9e8]
    // 0x8fb5c8: stp             x0, x16, [SP]
    // 0x8fb5cc: ldur            x1, [fp, #-0x68]
    // 0x8fb5d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x8fb5d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x8fb5d4: ldr             x4, [x4, #0xaa0]
    // 0x8fb5d8: r0 = copyWith()
    //     0x8fb5d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8fb5dc: stur            x0, [fp, #-0x50]
    // 0x8fb5e0: r0 = Text()
    //     0x8fb5e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x8fb5e4: mov             x1, x0
    // 0x8fb5e8: ldur            x0, [fp, #-0x10]
    // 0x8fb5ec: stur            x1, [fp, #-0x68]
    // 0x8fb5f0: StoreField: r1->field_b = r0
    //     0x8fb5f0: stur            w0, [x1, #0xb]
    // 0x8fb5f4: ldur            x0, [fp, #-0x50]
    // 0x8fb5f8: StoreField: r1->field_13 = r0
    //     0x8fb5f8: stur            w0, [x1, #0x13]
    // 0x8fb5fc: r2 = 2
    //     0x8fb5fc: movz            x2, #0x2
    // 0x8fb600: StoreField: r1->field_37 = r2
    //     0x8fb600: stur            w2, [x1, #0x37]
    // 0x8fb604: r0 = Padding()
    //     0x8fb604: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x8fb608: mov             x3, x0
    // 0x8fb60c: r0 = Instance_EdgeInsets
    //     0x8fb60c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48930] Obj!EdgeInsets@d56ed1
    //     0x8fb610: ldr             x0, [x0, #0x930]
    // 0x8fb614: stur            x3, [fp, #-0x50]
    // 0x8fb618: StoreField: r3->field_f = r0
    //     0x8fb618: stur            w0, [x3, #0xf]
    // 0x8fb61c: ldur            x0, [fp, #-0x68]
    // 0x8fb620: StoreField: r3->field_b = r0
    //     0x8fb620: stur            w0, [x3, #0xb]
    // 0x8fb624: ldur            x2, [fp, #-0x48]
    // 0x8fb628: r0 = BoxInt64Instr(r2)
    //     0x8fb628: sbfiz           x0, x2, #1, #0x1f
    //     0x8fb62c: cmp             x2, x0, asr #1
    //     0x8fb630: b.eq            #0x8fb63c
    //     0x8fb634: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fb638: stur            x2, [x0, #7]
    // 0x8fb63c: r1 = Null
    //     0x8fb63c: mov             x1, NULL
    // 0x8fb640: r2 = 8
    //     0x8fb640: movz            x2, #0x8
    // 0x8fb644: stur            x0, [fp, #-0x10]
    // 0x8fb648: r0 = AllocateArray()
    //     0x8fb648: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fb64c: mov             x1, x0
    // 0x8fb650: ldur            x0, [fp, #-0x10]
    // 0x8fb654: StoreField: r1->field_f = r0
    //     0x8fb654: stur            w0, [x1, #0xf]
    // 0x8fb658: r16 = "* "
    //     0x8fb658: add             x16, PP, #0x48, lsl #12  ; [pp+0x48938] "* "
    //     0x8fb65c: ldr             x16, [x16, #0x938]
    // 0x8fb660: StoreField: r1->field_13 = r16
    //     0x8fb660: stur            w16, [x1, #0x13]
    // 0x8fb664: ldur            x0, [fp, #-0x40]
    // 0x8fb668: ArrayStore: r1[0] = r0  ; List_4
    //     0x8fb668: stur            w0, [x1, #0x17]
    // 0x8fb66c: r16 = " "
    //     0x8fb66c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x8fb670: StoreField: r1->field_1b = r16
    //     0x8fb670: stur            w16, [x1, #0x1b]
    // 0x8fb674: str             x1, [SP]
    // 0x8fb678: r0 = _interpolate()
    //     0x8fb678: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8fb67c: mov             x2, x0
    // 0x8fb680: ldur            x0, [fp, #-0x70]
    // 0x8fb684: stur            x2, [fp, #-0x40]
    // 0x8fb688: LoadField: r3 = r0->field_2b
    //     0x8fb688: ldur            w3, [x0, #0x2b]
    // 0x8fb68c: DecompressPointer r3
    //     0x8fb68c: add             x3, x3, HEAP, lsl #32
    // 0x8fb690: stur            x3, [fp, #-0x10]
    // 0x8fb694: r1 = Instance_Color
    //     0x8fb694: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8fb698: d0 = 0.700000
    //     0x8fb698: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x8fb69c: ldr             d0, [x17, #0xf48]
    // 0x8fb6a0: r0 = withOpacity()
    //     0x8fb6a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8fb6a4: r16 = 12.000000
    //     0x8fb6a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x8fb6a8: ldr             x16, [x16, #0x9e8]
    // 0x8fb6ac: stp             x0, x16, [SP]
    // 0x8fb6b0: ldur            x1, [fp, #-0x10]
    // 0x8fb6b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x8fb6b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x8fb6b8: ldr             x4, [x4, #0xaa0]
    // 0x8fb6bc: r0 = copyWith()
    //     0x8fb6bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8fb6c0: stur            x0, [fp, #-0x68]
    // 0x8fb6c4: r0 = TextSpan()
    //     0x8fb6c4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x8fb6c8: mov             x2, x0
    // 0x8fb6cc: ldur            x0, [fp, #-0x40]
    // 0x8fb6d0: stur            x2, [fp, #-0x70]
    // 0x8fb6d4: StoreField: r2->field_b = r0
    //     0x8fb6d4: stur            w0, [x2, #0xb]
    // 0x8fb6d8: r0 = Instance__DeferringMouseCursor
    //     0x8fb6d8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x8fb6dc: ArrayStore: r2[0] = r0  ; List_4
    //     0x8fb6dc: stur            w0, [x2, #0x17]
    // 0x8fb6e0: ldur            x1, [fp, #-0x68]
    // 0x8fb6e4: StoreField: r2->field_7 = r1
    //     0x8fb6e4: stur            w1, [x2, #7]
    // 0x8fb6e8: r1 = Instance_Color
    //     0x8fb6e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8fb6ec: d0 = 0.400000
    //     0x8fb6ec: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x8fb6f0: r0 = withOpacity()
    //     0x8fb6f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8fb6f4: r16 = 12.000000
    //     0x8fb6f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x8fb6f8: ldr             x16, [x16, #0x9e8]
    // 0x8fb6fc: stp             x16, x0, [SP, #8]
    // 0x8fb700: r16 = Instance_TextDecoration
    //     0x8fb700: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x8fb704: ldr             x16, [x16, #0xe30]
    // 0x8fb708: str             x16, [SP]
    // 0x8fb70c: ldur            x1, [fp, #-0x10]
    // 0x8fb710: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0x8fb710: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0x8fb714: ldr             x4, [x4, #0x7c8]
    // 0x8fb718: r0 = copyWith()
    //     0x8fb718: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8fb71c: stur            x0, [fp, #-0x40]
    // 0x8fb720: r0 = TextSpan()
    //     0x8fb720: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x8fb724: mov             x3, x0
    // 0x8fb728: ldur            x0, [fp, #-0x38]
    // 0x8fb72c: stur            x3, [fp, #-0x68]
    // 0x8fb730: StoreField: r3->field_b = r0
    //     0x8fb730: stur            w0, [x3, #0xb]
    // 0x8fb734: r0 = Instance__DeferringMouseCursor
    //     0x8fb734: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x8fb738: ArrayStore: r3[0] = r0  ; List_4
    //     0x8fb738: stur            w0, [x3, #0x17]
    // 0x8fb73c: ldur            x1, [fp, #-0x40]
    // 0x8fb740: StoreField: r3->field_7 = r1
    //     0x8fb740: stur            w1, [x3, #7]
    // 0x8fb744: r1 = Null
    //     0x8fb744: mov             x1, NULL
    // 0x8fb748: r2 = 4
    //     0x8fb748: movz            x2, #0x4
    // 0x8fb74c: r0 = AllocateArray()
    //     0x8fb74c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fb750: mov             x2, x0
    // 0x8fb754: ldur            x0, [fp, #-0x70]
    // 0x8fb758: stur            x2, [fp, #-0x38]
    // 0x8fb75c: StoreField: r2->field_f = r0
    //     0x8fb75c: stur            w0, [x2, #0xf]
    // 0x8fb760: ldur            x0, [fp, #-0x68]
    // 0x8fb764: StoreField: r2->field_13 = r0
    //     0x8fb764: stur            w0, [x2, #0x13]
    // 0x8fb768: r1 = <TextSpan>
    //     0x8fb768: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0x8fb76c: ldr             x1, [x1, #0x940]
    // 0x8fb770: r0 = AllocateGrowableArray()
    //     0x8fb770: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8fb774: mov             x1, x0
    // 0x8fb778: ldur            x0, [fp, #-0x38]
    // 0x8fb77c: stur            x1, [fp, #-0x40]
    // 0x8fb780: StoreField: r1->field_f = r0
    //     0x8fb780: stur            w0, [x1, #0xf]
    // 0x8fb784: r2 = 4
    //     0x8fb784: movz            x2, #0x4
    // 0x8fb788: StoreField: r1->field_b = r2
    //     0x8fb788: stur            w2, [x1, #0xb]
    // 0x8fb78c: r0 = TextSpan()
    //     0x8fb78c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x8fb790: mov             x1, x0
    // 0x8fb794: ldur            x0, [fp, #-0x40]
    // 0x8fb798: stur            x1, [fp, #-0x38]
    // 0x8fb79c: StoreField: r1->field_f = r0
    //     0x8fb79c: stur            w0, [x1, #0xf]
    // 0x8fb7a0: r0 = Instance__DeferringMouseCursor
    //     0x8fb7a0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x8fb7a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x8fb7a4: stur            w0, [x1, #0x17]
    // 0x8fb7a8: r0 = RichText()
    //     0x8fb7a8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x8fb7ac: mov             x1, x0
    // 0x8fb7b0: ldur            x2, [fp, #-0x38]
    // 0x8fb7b4: stur            x0, [fp, #-0x38]
    // 0x8fb7b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8fb7b8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8fb7bc: r0 = RichText()
    //     0x8fb7bc: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x8fb7c0: r0 = Padding()
    //     0x8fb7c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x8fb7c4: mov             x3, x0
    // 0x8fb7c8: r0 = Instance_EdgeInsets
    //     0x8fb7c8: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0x8fb7cc: ldr             x0, [x0, #0xe0]
    // 0x8fb7d0: stur            x3, [fp, #-0x40]
    // 0x8fb7d4: StoreField: r3->field_f = r0
    //     0x8fb7d4: stur            w0, [x3, #0xf]
    // 0x8fb7d8: ldur            x0, [fp, #-0x38]
    // 0x8fb7dc: StoreField: r3->field_b = r0
    //     0x8fb7dc: stur            w0, [x3, #0xb]
    // 0x8fb7e0: r1 = Null
    //     0x8fb7e0: mov             x1, NULL
    // 0x8fb7e4: r2 = 2
    //     0x8fb7e4: movz            x2, #0x2
    // 0x8fb7e8: r0 = AllocateArray()
    //     0x8fb7e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fb7ec: mov             x2, x0
    // 0x8fb7f0: ldur            x0, [fp, #-0x40]
    // 0x8fb7f4: stur            x2, [fp, #-0x38]
    // 0x8fb7f8: StoreField: r2->field_f = r0
    //     0x8fb7f8: stur            w0, [x2, #0xf]
    // 0x8fb7fc: r1 = <Widget>
    //     0x8fb7fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8fb800: r0 = AllocateGrowableArray()
    //     0x8fb800: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8fb804: mov             x1, x0
    // 0x8fb808: ldur            x0, [fp, #-0x38]
    // 0x8fb80c: stur            x1, [fp, #-0x40]
    // 0x8fb810: StoreField: r1->field_f = r0
    //     0x8fb810: stur            w0, [x1, #0xf]
    // 0x8fb814: r0 = 2
    //     0x8fb814: movz            x0, #0x2
    // 0x8fb818: StoreField: r1->field_b = r0
    //     0x8fb818: stur            w0, [x1, #0xb]
    // 0x8fb81c: ldur            x0, [fp, #-0x30]
    // 0x8fb820: tbnz            w0, #4, #0x8fb98c
    // 0x8fb824: ldur            x0, [fp, #-8]
    // 0x8fb828: LoadField: r2 = r0->field_27
    //     0x8fb828: ldur            w2, [x0, #0x27]
    // 0x8fb82c: DecompressPointer r2
    //     0x8fb82c: add             x2, x2, HEAP, lsl #32
    // 0x8fb830: stur            x2, [fp, #-0x30]
    // 0x8fb834: r0 = Radius()
    //     0x8fb834: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8fb838: d0 = 20.000000
    //     0x8fb838: fmov            d0, #20.00000000
    // 0x8fb83c: stur            x0, [fp, #-0x38]
    // 0x8fb840: StoreField: r0->field_7 = d0
    //     0x8fb840: stur            d0, [x0, #7]
    // 0x8fb844: StoreField: r0->field_f = d0
    //     0x8fb844: stur            d0, [x0, #0xf]
    // 0x8fb848: r0 = BorderRadius()
    //     0x8fb848: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8fb84c: mov             x1, x0
    // 0x8fb850: ldur            x0, [fp, #-0x38]
    // 0x8fb854: stur            x1, [fp, #-0x68]
    // 0x8fb858: StoreField: r1->field_7 = r0
    //     0x8fb858: stur            w0, [x1, #7]
    // 0x8fb85c: StoreField: r1->field_b = r0
    //     0x8fb85c: stur            w0, [x1, #0xb]
    // 0x8fb860: StoreField: r1->field_f = r0
    //     0x8fb860: stur            w0, [x1, #0xf]
    // 0x8fb864: StoreField: r1->field_13 = r0
    //     0x8fb864: stur            w0, [x1, #0x13]
    // 0x8fb868: r0 = BoxDecoration()
    //     0x8fb868: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x8fb86c: mov             x2, x0
    // 0x8fb870: ldur            x0, [fp, #-0x30]
    // 0x8fb874: stur            x2, [fp, #-0x38]
    // 0x8fb878: StoreField: r2->field_7 = r0
    //     0x8fb878: stur            w0, [x2, #7]
    // 0x8fb87c: ldur            x0, [fp, #-0x68]
    // 0x8fb880: StoreField: r2->field_13 = r0
    //     0x8fb880: stur            w0, [x2, #0x13]
    // 0x8fb884: r0 = Instance_BoxShape
    //     0x8fb884: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x8fb888: ldr             x0, [x0, #0x80]
    // 0x8fb88c: StoreField: r2->field_23 = r0
    //     0x8fb88c: stur            w0, [x2, #0x23]
    // 0x8fb890: r1 = Instance_Color
    //     0x8fb890: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8fb894: d0 = 0.700000
    //     0x8fb894: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x8fb898: ldr             d0, [x17, #0xf48]
    // 0x8fb89c: r0 = withOpacity()
    //     0x8fb89c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8fb8a0: r16 = 12.000000
    //     0x8fb8a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x8fb8a4: ldr             x16, [x16, #0x9e8]
    // 0x8fb8a8: stp             x16, x0, [SP]
    // 0x8fb8ac: ldur            x1, [fp, #-0x10]
    // 0x8fb8b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x8fb8b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x8fb8b4: ldr             x4, [x4, #0x9b8]
    // 0x8fb8b8: r0 = copyWith()
    //     0x8fb8b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8fb8bc: stur            x0, [fp, #-0x10]
    // 0x8fb8c0: r0 = Text()
    //     0x8fb8c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x8fb8c4: mov             x1, x0
    // 0x8fb8c8: r0 = "Customised"
    //     0x8fb8c8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0x8fb8cc: ldr             x0, [x0, #0xd88]
    // 0x8fb8d0: stur            x1, [fp, #-0x30]
    // 0x8fb8d4: StoreField: r1->field_b = r0
    //     0x8fb8d4: stur            w0, [x1, #0xb]
    // 0x8fb8d8: ldur            x0, [fp, #-0x10]
    // 0x8fb8dc: StoreField: r1->field_13 = r0
    //     0x8fb8dc: stur            w0, [x1, #0x13]
    // 0x8fb8e0: r0 = Container()
    //     0x8fb8e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x8fb8e4: stur            x0, [fp, #-0x10]
    // 0x8fb8e8: r16 = Instance_EdgeInsets
    //     0x8fb8e8: add             x16, PP, #0x48, lsl #12  ; [pp+0x48948] Obj!EdgeInsets@d56ea1
    //     0x8fb8ec: ldr             x16, [x16, #0x948]
    // 0x8fb8f0: ldur            lr, [fp, #-0x38]
    // 0x8fb8f4: stp             lr, x16, [SP, #8]
    // 0x8fb8f8: ldur            x16, [fp, #-0x30]
    // 0x8fb8fc: str             x16, [SP]
    // 0x8fb900: mov             x1, x0
    // 0x8fb904: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x8fb904: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x8fb908: ldr             x4, [x4, #0x610]
    // 0x8fb90c: r0 = Container()
    //     0x8fb90c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x8fb910: ldur            x0, [fp, #-0x40]
    // 0x8fb914: LoadField: r1 = r0->field_b
    //     0x8fb914: ldur            w1, [x0, #0xb]
    // 0x8fb918: LoadField: r2 = r0->field_f
    //     0x8fb918: ldur            w2, [x0, #0xf]
    // 0x8fb91c: DecompressPointer r2
    //     0x8fb91c: add             x2, x2, HEAP, lsl #32
    // 0x8fb920: LoadField: r3 = r2->field_b
    //     0x8fb920: ldur            w3, [x2, #0xb]
    // 0x8fb924: r2 = LoadInt32Instr(r1)
    //     0x8fb924: sbfx            x2, x1, #1, #0x1f
    // 0x8fb928: stur            x2, [fp, #-0x48]
    // 0x8fb92c: r1 = LoadInt32Instr(r3)
    //     0x8fb92c: sbfx            x1, x3, #1, #0x1f
    // 0x8fb930: cmp             x2, x1
    // 0x8fb934: b.ne            #0x8fb940
    // 0x8fb938: mov             x1, x0
    // 0x8fb93c: r0 = _growToNextCapacity()
    //     0x8fb93c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8fb940: ldur            x2, [fp, #-0x40]
    // 0x8fb944: ldur            x3, [fp, #-0x48]
    // 0x8fb948: add             x0, x3, #1
    // 0x8fb94c: lsl             x1, x0, #1
    // 0x8fb950: StoreField: r2->field_b = r1
    //     0x8fb950: stur            w1, [x2, #0xb]
    // 0x8fb954: LoadField: r1 = r2->field_f
    //     0x8fb954: ldur            w1, [x2, #0xf]
    // 0x8fb958: DecompressPointer r1
    //     0x8fb958: add             x1, x1, HEAP, lsl #32
    // 0x8fb95c: ldur            x0, [fp, #-0x10]
    // 0x8fb960: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8fb960: add             x25, x1, x3, lsl #2
    //     0x8fb964: add             x25, x25, #0xf
    //     0x8fb968: str             w0, [x25]
    //     0x8fb96c: tbz             w0, #0, #0x8fb988
    //     0x8fb970: ldurb           w16, [x1, #-1]
    //     0x8fb974: ldurb           w17, [x0, #-1]
    //     0x8fb978: and             x16, x17, x16, lsr #2
    //     0x8fb97c: tst             x16, HEAP, lsr #32
    //     0x8fb980: b.eq            #0x8fb988
    //     0x8fb984: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8fb988: b               #0x8fb990
    // 0x8fb98c: mov             x2, x1
    // 0x8fb990: ldur            x3, [fp, #-0x60]
    // 0x8fb994: ldur            x1, [fp, #-0x78]
    // 0x8fb998: ldur            x0, [fp, #-0x50]
    // 0x8fb99c: r0 = Row()
    //     0x8fb99c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x8fb9a0: mov             x1, x0
    // 0x8fb9a4: r0 = Instance_Axis
    //     0x8fb9a4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x8fb9a8: stur            x1, [fp, #-0x10]
    // 0x8fb9ac: StoreField: r1->field_f = r0
    //     0x8fb9ac: stur            w0, [x1, #0xf]
    // 0x8fb9b0: r2 = Instance_MainAxisAlignment
    //     0x8fb9b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x8fb9b4: ldr             x2, [x2, #0xa08]
    // 0x8fb9b8: StoreField: r1->field_13 = r2
    //     0x8fb9b8: stur            w2, [x1, #0x13]
    // 0x8fb9bc: r3 = Instance_MainAxisSize
    //     0x8fb9bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x8fb9c0: ldr             x3, [x3, #0xa10]
    // 0x8fb9c4: ArrayStore: r1[0] = r3  ; List_4
    //     0x8fb9c4: stur            w3, [x1, #0x17]
    // 0x8fb9c8: r4 = Instance_CrossAxisAlignment
    //     0x8fb9c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x8fb9cc: ldr             x4, [x4, #0xa18]
    // 0x8fb9d0: StoreField: r1->field_1b = r4
    //     0x8fb9d0: stur            w4, [x1, #0x1b]
    // 0x8fb9d4: r5 = Instance_VerticalDirection
    //     0x8fb9d4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x8fb9d8: ldr             x5, [x5, #0xa20]
    // 0x8fb9dc: StoreField: r1->field_23 = r5
    //     0x8fb9dc: stur            w5, [x1, #0x23]
    // 0x8fb9e0: r6 = Instance_Clip
    //     0x8fb9e0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8fb9e4: ldr             x6, [x6, #0x38]
    // 0x8fb9e8: StoreField: r1->field_2b = r6
    //     0x8fb9e8: stur            w6, [x1, #0x2b]
    // 0x8fb9ec: StoreField: r1->field_2f = rZR
    //     0x8fb9ec: stur            xzr, [x1, #0x2f]
    // 0x8fb9f0: ldur            x7, [fp, #-0x40]
    // 0x8fb9f4: StoreField: r1->field_b = r7
    //     0x8fb9f4: stur            w7, [x1, #0xb]
    // 0x8fb9f8: r0 = Padding()
    //     0x8fb9f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x8fb9fc: mov             x3, x0
    // 0x8fba00: r0 = Instance_EdgeInsets
    //     0x8fba00: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x8fba04: ldr             x0, [x0, #0xc40]
    // 0x8fba08: stur            x3, [fp, #-0x30]
    // 0x8fba0c: StoreField: r3->field_f = r0
    //     0x8fba0c: stur            w0, [x3, #0xf]
    // 0x8fba10: ldur            x0, [fp, #-0x10]
    // 0x8fba14: StoreField: r3->field_b = r0
    //     0x8fba14: stur            w0, [x3, #0xb]
    // 0x8fba18: r1 = Null
    //     0x8fba18: mov             x1, NULL
    // 0x8fba1c: r2 = 6
    //     0x8fba1c: movz            x2, #0x6
    // 0x8fba20: r0 = AllocateArray()
    //     0x8fba20: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fba24: mov             x2, x0
    // 0x8fba28: ldur            x0, [fp, #-0x78]
    // 0x8fba2c: stur            x2, [fp, #-0x10]
    // 0x8fba30: StoreField: r2->field_f = r0
    //     0x8fba30: stur            w0, [x2, #0xf]
    // 0x8fba34: ldur            x0, [fp, #-0x50]
    // 0x8fba38: StoreField: r2->field_13 = r0
    //     0x8fba38: stur            w0, [x2, #0x13]
    // 0x8fba3c: ldur            x0, [fp, #-0x30]
    // 0x8fba40: ArrayStore: r2[0] = r0  ; List_4
    //     0x8fba40: stur            w0, [x2, #0x17]
    // 0x8fba44: r1 = <Widget>
    //     0x8fba44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8fba48: r0 = AllocateGrowableArray()
    //     0x8fba48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8fba4c: mov             x1, x0
    // 0x8fba50: ldur            x0, [fp, #-0x10]
    // 0x8fba54: stur            x1, [fp, #-0x30]
    // 0x8fba58: StoreField: r1->field_f = r0
    //     0x8fba58: stur            w0, [x1, #0xf]
    // 0x8fba5c: r0 = 6
    //     0x8fba5c: movz            x0, #0x6
    // 0x8fba60: StoreField: r1->field_b = r0
    //     0x8fba60: stur            w0, [x1, #0xb]
    // 0x8fba64: r0 = Column()
    //     0x8fba64: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x8fba68: mov             x3, x0
    // 0x8fba6c: r0 = Instance_Axis
    //     0x8fba6c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x8fba70: stur            x3, [fp, #-0x10]
    // 0x8fba74: StoreField: r3->field_f = r0
    //     0x8fba74: stur            w0, [x3, #0xf]
    // 0x8fba78: r0 = Instance_MainAxisAlignment
    //     0x8fba78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x8fba7c: ldr             x0, [x0, #0xa08]
    // 0x8fba80: StoreField: r3->field_13 = r0
    //     0x8fba80: stur            w0, [x3, #0x13]
    // 0x8fba84: r4 = Instance_MainAxisSize
    //     0x8fba84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x8fba88: ldr             x4, [x4, #0xa10]
    // 0x8fba8c: ArrayStore: r3[0] = r4  ; List_4
    //     0x8fba8c: stur            w4, [x3, #0x17]
    // 0x8fba90: r1 = Instance_CrossAxisAlignment
    //     0x8fba90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x8fba94: ldr             x1, [x1, #0x890]
    // 0x8fba98: StoreField: r3->field_1b = r1
    //     0x8fba98: stur            w1, [x3, #0x1b]
    // 0x8fba9c: r5 = Instance_VerticalDirection
    //     0x8fba9c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x8fbaa0: ldr             x5, [x5, #0xa20]
    // 0x8fbaa4: StoreField: r3->field_23 = r5
    //     0x8fbaa4: stur            w5, [x3, #0x23]
    // 0x8fbaa8: r6 = Instance_Clip
    //     0x8fbaa8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8fbaac: ldr             x6, [x6, #0x38]
    // 0x8fbab0: StoreField: r3->field_2b = r6
    //     0x8fbab0: stur            w6, [x3, #0x2b]
    // 0x8fbab4: StoreField: r3->field_2f = rZR
    //     0x8fbab4: stur            xzr, [x3, #0x2f]
    // 0x8fbab8: ldur            x1, [fp, #-0x30]
    // 0x8fbabc: StoreField: r3->field_b = r1
    //     0x8fbabc: stur            w1, [x3, #0xb]
    // 0x8fbac0: r1 = Null
    //     0x8fbac0: mov             x1, NULL
    // 0x8fbac4: r2 = 4
    //     0x8fbac4: movz            x2, #0x4
    // 0x8fbac8: r0 = AllocateArray()
    //     0x8fbac8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fbacc: mov             x2, x0
    // 0x8fbad0: ldur            x0, [fp, #-0x60]
    // 0x8fbad4: stur            x2, [fp, #-0x30]
    // 0x8fbad8: StoreField: r2->field_f = r0
    //     0x8fbad8: stur            w0, [x2, #0xf]
    // 0x8fbadc: ldur            x0, [fp, #-0x10]
    // 0x8fbae0: StoreField: r2->field_13 = r0
    //     0x8fbae0: stur            w0, [x2, #0x13]
    // 0x8fbae4: r1 = <Widget>
    //     0x8fbae4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8fbae8: r0 = AllocateGrowableArray()
    //     0x8fbae8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8fbaec: mov             x1, x0
    // 0x8fbaf0: ldur            x0, [fp, #-0x30]
    // 0x8fbaf4: stur            x1, [fp, #-0x10]
    // 0x8fbaf8: StoreField: r1->field_f = r0
    //     0x8fbaf8: stur            w0, [x1, #0xf]
    // 0x8fbafc: r2 = 4
    //     0x8fbafc: movz            x2, #0x4
    // 0x8fbb00: StoreField: r1->field_b = r2
    //     0x8fbb00: stur            w2, [x1, #0xb]
    // 0x8fbb04: r0 = Row()
    //     0x8fbb04: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x8fbb08: mov             x4, x0
    // 0x8fbb0c: r0 = Instance_Axis
    //     0x8fbb0c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x8fbb10: stur            x4, [fp, #-0x30]
    // 0x8fbb14: StoreField: r4->field_f = r0
    //     0x8fbb14: stur            w0, [x4, #0xf]
    // 0x8fbb18: r1 = Instance_MainAxisAlignment
    //     0x8fbb18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x8fbb1c: ldr             x1, [x1, #0xa08]
    // 0x8fbb20: StoreField: r4->field_13 = r1
    //     0x8fbb20: stur            w1, [x4, #0x13]
    // 0x8fbb24: r7 = Instance_MainAxisSize
    //     0x8fbb24: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x8fbb28: ldr             x7, [x7, #0xa10]
    // 0x8fbb2c: ArrayStore: r4[0] = r7  ; List_4
    //     0x8fbb2c: stur            w7, [x4, #0x17]
    // 0x8fbb30: r8 = Instance_CrossAxisAlignment
    //     0x8fbb30: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x8fbb34: ldr             x8, [x8, #0xa18]
    // 0x8fbb38: StoreField: r4->field_1b = r8
    //     0x8fbb38: stur            w8, [x4, #0x1b]
    // 0x8fbb3c: r9 = Instance_VerticalDirection
    //     0x8fbb3c: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x8fbb40: ldr             x9, [x9, #0xa20]
    // 0x8fbb44: StoreField: r4->field_23 = r9
    //     0x8fbb44: stur            w9, [x4, #0x23]
    // 0x8fbb48: r10 = Instance_Clip
    //     0x8fbb48: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8fbb4c: ldr             x10, [x10, #0x38]
    // 0x8fbb50: StoreField: r4->field_2b = r10
    //     0x8fbb50: stur            w10, [x4, #0x2b]
    // 0x8fbb54: StoreField: r4->field_2f = rZR
    //     0x8fbb54: stur            xzr, [x4, #0x2f]
    // 0x8fbb58: ldur            x1, [fp, #-0x10]
    // 0x8fbb5c: StoreField: r4->field_b = r1
    //     0x8fbb5c: stur            w1, [x4, #0xb]
    // 0x8fbb60: ldur            x1, [fp, #-8]
    // 0x8fbb64: ldur            x2, [fp, #-0x28]
    // 0x8fbb68: ldur            x3, [fp, #-0x20]
    // 0x8fbb6c: ldur            x5, [fp, #-0x58]
    // 0x8fbb70: ldur            x6, [fp, #-0x18]
    // 0x8fbb74: r0 = _buildRemoveButton()
    //     0x8fbb74: bl              #0x8fbc48  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildRemoveButton
    // 0x8fbb78: r1 = Null
    //     0x8fbb78: mov             x1, NULL
    // 0x8fbb7c: r2 = 4
    //     0x8fbb7c: movz            x2, #0x4
    // 0x8fbb80: stur            x0, [fp, #-8]
    // 0x8fbb84: r0 = AllocateArray()
    //     0x8fbb84: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8fbb88: mov             x2, x0
    // 0x8fbb8c: ldur            x0, [fp, #-0x30]
    // 0x8fbb90: stur            x2, [fp, #-0x10]
    // 0x8fbb94: StoreField: r2->field_f = r0
    //     0x8fbb94: stur            w0, [x2, #0xf]
    // 0x8fbb98: ldur            x0, [fp, #-8]
    // 0x8fbb9c: StoreField: r2->field_13 = r0
    //     0x8fbb9c: stur            w0, [x2, #0x13]
    // 0x8fbba0: r1 = <Widget>
    //     0x8fbba0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8fbba4: r0 = AllocateGrowableArray()
    //     0x8fbba4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8fbba8: mov             x1, x0
    // 0x8fbbac: ldur            x0, [fp, #-0x10]
    // 0x8fbbb0: stur            x1, [fp, #-8]
    // 0x8fbbb4: StoreField: r1->field_f = r0
    //     0x8fbbb4: stur            w0, [x1, #0xf]
    // 0x8fbbb8: r0 = 4
    //     0x8fbbb8: movz            x0, #0x4
    // 0x8fbbbc: StoreField: r1->field_b = r0
    //     0x8fbbbc: stur            w0, [x1, #0xb]
    // 0x8fbbc0: r0 = Row()
    //     0x8fbbc0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x8fbbc4: mov             x1, x0
    // 0x8fbbc8: r0 = Instance_Axis
    //     0x8fbbc8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x8fbbcc: stur            x1, [fp, #-0x10]
    // 0x8fbbd0: StoreField: r1->field_f = r0
    //     0x8fbbd0: stur            w0, [x1, #0xf]
    // 0x8fbbd4: r0 = Instance_MainAxisAlignment
    //     0x8fbbd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x8fbbd8: ldr             x0, [x0, #0xa8]
    // 0x8fbbdc: StoreField: r1->field_13 = r0
    //     0x8fbbdc: stur            w0, [x1, #0x13]
    // 0x8fbbe0: r0 = Instance_MainAxisSize
    //     0x8fbbe0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x8fbbe4: ldr             x0, [x0, #0xa10]
    // 0x8fbbe8: ArrayStore: r1[0] = r0  ; List_4
    //     0x8fbbe8: stur            w0, [x1, #0x17]
    // 0x8fbbec: r0 = Instance_CrossAxisAlignment
    //     0x8fbbec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x8fbbf0: ldr             x0, [x0, #0xa18]
    // 0x8fbbf4: StoreField: r1->field_1b = r0
    //     0x8fbbf4: stur            w0, [x1, #0x1b]
    // 0x8fbbf8: r0 = Instance_VerticalDirection
    //     0x8fbbf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x8fbbfc: ldr             x0, [x0, #0xa20]
    // 0x8fbc00: StoreField: r1->field_23 = r0
    //     0x8fbc00: stur            w0, [x1, #0x23]
    // 0x8fbc04: r0 = Instance_Clip
    //     0x8fbc04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8fbc08: ldr             x0, [x0, #0x38]
    // 0x8fbc0c: StoreField: r1->field_2b = r0
    //     0x8fbc0c: stur            w0, [x1, #0x2b]
    // 0x8fbc10: StoreField: r1->field_2f = rZR
    //     0x8fbc10: stur            xzr, [x1, #0x2f]
    // 0x8fbc14: ldur            x0, [fp, #-8]
    // 0x8fbc18: StoreField: r1->field_b = r0
    //     0x8fbc18: stur            w0, [x1, #0xb]
    // 0x8fbc1c: r0 = Padding()
    //     0x8fbc1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x8fbc20: r1 = Instance_EdgeInsets
    //     0x8fbc20: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x8fbc24: ldr             x1, [x1, #0x240]
    // 0x8fbc28: StoreField: r0->field_f = r1
    //     0x8fbc28: stur            w1, [x0, #0xf]
    // 0x8fbc2c: ldur            x1, [fp, #-0x10]
    // 0x8fbc30: StoreField: r0->field_b = r1
    //     0x8fbc30: stur            w1, [x0, #0xb]
    // 0x8fbc34: LeaveFrame
    //     0x8fbc34: mov             SP, fp
    //     0x8fbc38: ldp             fp, lr, [SP], #0x10
    // 0x8fbc3c: ret
    //     0x8fbc3c: ret             
    // 0x8fbc40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fbc40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fbc44: b               #0x8fb1f8
  }
  _ _buildRemoveButton(/* No info */) {
    // ** addr: 0x8fbc48, size: 0x134
    // 0x8fbc48: EnterFrame
    //     0x8fbc48: stp             fp, lr, [SP, #-0x10]!
    //     0x8fbc4c: mov             fp, SP
    // 0x8fbc50: AllocStack(0x40)
    //     0x8fbc50: sub             SP, SP, #0x40
    // 0x8fbc54: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x8fbc54: stur            x1, [fp, #-8]
    //     0x8fbc58: stur            x2, [fp, #-0x10]
    //     0x8fbc5c: stur            x3, [fp, #-0x18]
    //     0x8fbc60: stur            x5, [fp, #-0x20]
    //     0x8fbc64: stur            x6, [fp, #-0x28]
    // 0x8fbc68: CheckStackOverflow
    //     0x8fbc68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fbc6c: cmp             SP, x16
    //     0x8fbc70: b.ls            #0x8fbd74
    // 0x8fbc74: r1 = 5
    //     0x8fbc74: movz            x1, #0x5
    // 0x8fbc78: r0 = AllocateContext()
    //     0x8fbc78: bl              #0x16f6108  ; AllocateContextStub
    // 0x8fbc7c: mov             x1, x0
    // 0x8fbc80: ldur            x0, [fp, #-8]
    // 0x8fbc84: stur            x1, [fp, #-0x30]
    // 0x8fbc88: StoreField: r1->field_f = r0
    //     0x8fbc88: stur            w0, [x1, #0xf]
    // 0x8fbc8c: ldur            x2, [fp, #-0x10]
    // 0x8fbc90: StoreField: r1->field_13 = r2
    //     0x8fbc90: stur            w2, [x1, #0x13]
    // 0x8fbc94: ldur            x2, [fp, #-0x18]
    // 0x8fbc98: ArrayStore: r1[0] = r2  ; List_4
    //     0x8fbc98: stur            w2, [x1, #0x17]
    // 0x8fbc9c: ldur            x2, [fp, #-0x20]
    // 0x8fbca0: StoreField: r1->field_1b = r2
    //     0x8fbca0: stur            w2, [x1, #0x1b]
    // 0x8fbca4: ldur            x2, [fp, #-0x28]
    // 0x8fbca8: StoreField: r1->field_1f = r2
    //     0x8fbca8: stur            w2, [x1, #0x1f]
    // 0x8fbcac: LoadField: r2 = r0->field_23
    //     0x8fbcac: ldur            w2, [x0, #0x23]
    // 0x8fbcb0: DecompressPointer r2
    //     0x8fbcb0: add             x2, x2, HEAP, lsl #32
    // 0x8fbcb4: stur            x2, [fp, #-0x10]
    // 0x8fbcb8: r0 = ColorFilter()
    //     0x8fbcb8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x8fbcbc: mov             x1, x0
    // 0x8fbcc0: ldur            x0, [fp, #-0x10]
    // 0x8fbcc4: stur            x1, [fp, #-8]
    // 0x8fbcc8: StoreField: r1->field_7 = r0
    //     0x8fbcc8: stur            w0, [x1, #7]
    // 0x8fbccc: r0 = Instance_BlendMode
    //     0x8fbccc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x8fbcd0: ldr             x0, [x0, #0xb30]
    // 0x8fbcd4: StoreField: r1->field_b = r0
    //     0x8fbcd4: stur            w0, [x1, #0xb]
    // 0x8fbcd8: r0 = 1
    //     0x8fbcd8: movz            x0, #0x1
    // 0x8fbcdc: StoreField: r1->field_13 = r0
    //     0x8fbcdc: stur            x0, [x1, #0x13]
    // 0x8fbce0: r0 = SvgPicture()
    //     0x8fbce0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x8fbce4: stur            x0, [fp, #-0x10]
    // 0x8fbce8: r16 = Instance_BoxFit
    //     0x8fbce8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x8fbcec: ldr             x16, [x16, #0xb18]
    // 0x8fbcf0: ldur            lr, [fp, #-8]
    // 0x8fbcf4: stp             lr, x16, [SP]
    // 0x8fbcf8: mov             x1, x0
    // 0x8fbcfc: r2 = "assets/images/bin_glass.svg"
    //     0x8fbcfc: add             x2, PP, #0x48, lsl #12  ; [pp+0x48950] "assets/images/bin_glass.svg"
    //     0x8fbd00: ldr             x2, [x2, #0x950]
    // 0x8fbd04: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x8fbd04: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x8fbd08: ldr             x4, [x4, #0x820]
    // 0x8fbd0c: r0 = SvgPicture.asset()
    //     0x8fbd0c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x8fbd10: r0 = InkWell()
    //     0x8fbd10: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x8fbd14: mov             x3, x0
    // 0x8fbd18: ldur            x0, [fp, #-0x10]
    // 0x8fbd1c: stur            x3, [fp, #-8]
    // 0x8fbd20: StoreField: r3->field_b = r0
    //     0x8fbd20: stur            w0, [x3, #0xb]
    // 0x8fbd24: ldur            x2, [fp, #-0x30]
    // 0x8fbd28: r1 = Function '<anonymous closure>':.
    //     0x8fbd28: add             x1, PP, #0x48, lsl #12  ; [pp+0x48958] AnonymousClosure: (0x8fc068), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildRemoveButton (0x8fbc48)
    //     0x8fbd2c: ldr             x1, [x1, #0x958]
    // 0x8fbd30: r0 = AllocateClosure()
    //     0x8fbd30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fbd34: mov             x1, x0
    // 0x8fbd38: ldur            x0, [fp, #-8]
    // 0x8fbd3c: StoreField: r0->field_f = r1
    //     0x8fbd3c: stur            w1, [x0, #0xf]
    // 0x8fbd40: r1 = true
    //     0x8fbd40: add             x1, NULL, #0x20  ; true
    // 0x8fbd44: StoreField: r0->field_43 = r1
    //     0x8fbd44: stur            w1, [x0, #0x43]
    // 0x8fbd48: r2 = Instance_BoxShape
    //     0x8fbd48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x8fbd4c: ldr             x2, [x2, #0x80]
    // 0x8fbd50: StoreField: r0->field_47 = r2
    //     0x8fbd50: stur            w2, [x0, #0x47]
    // 0x8fbd54: StoreField: r0->field_6f = r1
    //     0x8fbd54: stur            w1, [x0, #0x6f]
    // 0x8fbd58: r2 = false
    //     0x8fbd58: add             x2, NULL, #0x30  ; false
    // 0x8fbd5c: StoreField: r0->field_73 = r2
    //     0x8fbd5c: stur            w2, [x0, #0x73]
    // 0x8fbd60: StoreField: r0->field_83 = r1
    //     0x8fbd60: stur            w1, [x0, #0x83]
    // 0x8fbd64: StoreField: r0->field_7b = r2
    //     0x8fbd64: stur            w2, [x0, #0x7b]
    // 0x8fbd68: LeaveFrame
    //     0x8fbd68: mov             SP, fp
    //     0x8fbd6c: ldp             fp, lr, [SP], #0x10
    // 0x8fbd70: ret
    //     0x8fbd70: ret             
    // 0x8fbd74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fbd74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fbd78: b               #0x8fbc74
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8fc068, size: 0x50
    // 0x8fc068: EnterFrame
    //     0x8fc068: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc06c: mov             fp, SP
    // 0x8fc070: ldr             x0, [fp, #0x10]
    // 0x8fc074: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8fc074: ldur            w2, [x0, #0x17]
    // 0x8fc078: DecompressPointer r2
    //     0x8fc078: add             x2, x2, HEAP, lsl #32
    // 0x8fc07c: CheckStackOverflow
    //     0x8fc07c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc080: cmp             SP, x16
    //     0x8fc084: b.ls            #0x8fc0b0
    // 0x8fc088: r1 = Function '<anonymous closure>':.
    //     0x8fc088: add             x1, PP, #0x48, lsl #12  ; [pp+0x48960] AnonymousClosure: (0x8fc0b8), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildRemoveButton (0x8fbc48)
    //     0x8fc08c: ldr             x1, [x1, #0x960]
    // 0x8fc090: r0 = AllocateClosure()
    //     0x8fc090: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8fc094: mov             x2, x0
    // 0x8fc098: r1 = <Null?>
    //     0x8fc098: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x8fc09c: r0 = Future.microtask()
    //     0x8fc09c: bl              #0x801434  ; [dart:async] Future::Future.microtask
    // 0x8fc0a0: r0 = Null
    //     0x8fc0a0: mov             x0, NULL
    // 0x8fc0a4: LeaveFrame
    //     0x8fc0a4: mov             SP, fp
    //     0x8fc0a8: ldp             fp, lr, [SP], #0x10
    // 0x8fc0ac: ret
    //     0x8fc0ac: ret             
    // 0x8fc0b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc0b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc0b4: b               #0x8fc088
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x8fc0b8, size: 0x180
    // 0x8fc0b8: EnterFrame
    //     0x8fc0b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc0bc: mov             fp, SP
    // 0x8fc0c0: AllocStack(0x38)
    //     0x8fc0c0: sub             SP, SP, #0x38
    // 0x8fc0c4: SetupParameters()
    //     0x8fc0c4: ldr             x0, [fp, #0x10]
    //     0x8fc0c8: ldur            w1, [x0, #0x17]
    //     0x8fc0cc: add             x1, x1, HEAP, lsl #32
    //     0x8fc0d0: stur            x1, [fp, #-8]
    // 0x8fc0d4: CheckStackOverflow
    //     0x8fc0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc0d8: cmp             SP, x16
    //     0x8fc0dc: b.ls            #0x8fc230
    // 0x8fc0e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8fc0e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8fc0e4: ldr             x0, [x0, #0x1c80]
    //     0x8fc0e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8fc0ec: cmp             w0, w16
    //     0x8fc0f0: b.ne            #0x8fc0fc
    //     0x8fc0f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8fc0f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8fc0fc: str             NULL, [SP]
    // 0x8fc100: r4 = const [0x1, 0, 0, 0, null]
    //     0x8fc100: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x8fc104: r0 = GetNavigation.back()
    //     0x8fc104: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x8fc108: ldur            x0, [fp, #-8]
    // 0x8fc10c: LoadField: r1 = r0->field_13
    //     0x8fc10c: ldur            w1, [x0, #0x13]
    // 0x8fc110: DecompressPointer r1
    //     0x8fc110: add             x1, x1, HEAP, lsl #32
    // 0x8fc114: stur            x1, [fp, #-0x20]
    // 0x8fc118: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8fc118: ldur            w2, [x0, #0x17]
    // 0x8fc11c: DecompressPointer r2
    //     0x8fc11c: add             x2, x2, HEAP, lsl #32
    // 0x8fc120: stur            x2, [fp, #-0x18]
    // 0x8fc124: LoadField: r3 = r0->field_1b
    //     0x8fc124: ldur            w3, [x0, #0x1b]
    // 0x8fc128: DecompressPointer r3
    //     0x8fc128: add             x3, x3, HEAP, lsl #32
    // 0x8fc12c: stur            x3, [fp, #-0x10]
    // 0x8fc130: r0 = EventData()
    //     0x8fc130: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8fc134: mov             x1, x0
    // 0x8fc138: ldur            x0, [fp, #-0x20]
    // 0x8fc13c: stur            x1, [fp, #-0x28]
    // 0x8fc140: StoreField: r1->field_33 = r0
    //     0x8fc140: stur            w0, [x1, #0x33]
    // 0x8fc144: ldur            x0, [fp, #-0x18]
    // 0x8fc148: StoreField: r1->field_8f = r0
    //     0x8fc148: stur            w0, [x1, #0x8f]
    // 0x8fc14c: ldur            x0, [fp, #-0x10]
    // 0x8fc150: StoreField: r1->field_93 = r0
    //     0x8fc150: stur            w0, [x1, #0x93]
    // 0x8fc154: r0 = EventsRequest()
    //     0x8fc154: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8fc158: mov             x1, x0
    // 0x8fc15c: r0 = "product_removed"
    //     0x8fc15c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7e0] "product_removed"
    //     0x8fc160: ldr             x0, [x0, #0x7e0]
    // 0x8fc164: StoreField: r1->field_7 = r0
    //     0x8fc164: stur            w0, [x1, #7]
    // 0x8fc168: ldur            x0, [fp, #-0x28]
    // 0x8fc16c: StoreField: r1->field_b = r0
    //     0x8fc16c: stur            w0, [x1, #0xb]
    // 0x8fc170: ldur            x0, [fp, #-8]
    // 0x8fc174: LoadField: r2 = r0->field_f
    //     0x8fc174: ldur            w2, [x0, #0xf]
    // 0x8fc178: DecompressPointer r2
    //     0x8fc178: add             x2, x2, HEAP, lsl #32
    // 0x8fc17c: LoadField: r3 = r2->field_1b
    //     0x8fc17c: ldur            w3, [x2, #0x1b]
    // 0x8fc180: DecompressPointer r3
    //     0x8fc180: add             x3, x3, HEAP, lsl #32
    // 0x8fc184: stp             x1, x3, [SP]
    // 0x8fc188: r4 = 0
    //     0x8fc188: movz            x4, #0
    // 0x8fc18c: ldr             x0, [SP, #8]
    // 0x8fc190: r16 = UnlinkedCall_0x613b5c
    //     0x8fc190: add             x16, PP, #0x48, lsl #12  ; [pp+0x48968] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8fc194: add             x16, x16, #0x968
    // 0x8fc198: ldp             x5, lr, [x16]
    // 0x8fc19c: blr             lr
    // 0x8fc1a0: ldur            x0, [fp, #-8]
    // 0x8fc1a4: LoadField: r1 = r0->field_1f
    //     0x8fc1a4: ldur            w1, [x0, #0x1f]
    // 0x8fc1a8: DecompressPointer r1
    //     0x8fc1a8: add             x1, x1, HEAP, lsl #32
    // 0x8fc1ac: stur            x1, [fp, #-0x20]
    // 0x8fc1b0: LoadField: r2 = r0->field_13
    //     0x8fc1b0: ldur            w2, [x0, #0x13]
    // 0x8fc1b4: DecompressPointer r2
    //     0x8fc1b4: add             x2, x2, HEAP, lsl #32
    // 0x8fc1b8: stur            x2, [fp, #-0x18]
    // 0x8fc1bc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8fc1bc: ldur            w3, [x0, #0x17]
    // 0x8fc1c0: DecompressPointer r3
    //     0x8fc1c0: add             x3, x3, HEAP, lsl #32
    // 0x8fc1c4: stur            x3, [fp, #-0x10]
    // 0x8fc1c8: r0 = RemoveItemRequest()
    //     0x8fc1c8: bl              #0x8fc238  ; AllocateRemoveItemRequestStub -> RemoveItemRequest (size=0x18)
    // 0x8fc1cc: mov             x1, x0
    // 0x8fc1d0: ldur            x0, [fp, #-0x18]
    // 0x8fc1d4: StoreField: r1->field_7 = r0
    //     0x8fc1d4: stur            w0, [x1, #7]
    // 0x8fc1d8: ldur            x0, [fp, #-0x10]
    // 0x8fc1dc: StoreField: r1->field_b = r0
    //     0x8fc1dc: stur            w0, [x1, #0xb]
    // 0x8fc1e0: r0 = true
    //     0x8fc1e0: add             x0, NULL, #0x20  ; true
    // 0x8fc1e4: StoreField: r1->field_f = r0
    //     0x8fc1e4: stur            w0, [x1, #0xf]
    // 0x8fc1e8: ldur            x0, [fp, #-0x20]
    // 0x8fc1ec: StoreField: r1->field_13 = r0
    //     0x8fc1ec: stur            w0, [x1, #0x13]
    // 0x8fc1f0: ldur            x0, [fp, #-8]
    // 0x8fc1f4: LoadField: r2 = r0->field_f
    //     0x8fc1f4: ldur            w2, [x0, #0xf]
    // 0x8fc1f8: DecompressPointer r2
    //     0x8fc1f8: add             x2, x2, HEAP, lsl #32
    // 0x8fc1fc: LoadField: r0 = r2->field_f
    //     0x8fc1fc: ldur            w0, [x2, #0xf]
    // 0x8fc200: DecompressPointer r0
    //     0x8fc200: add             x0, x0, HEAP, lsl #32
    // 0x8fc204: stp             x1, x0, [SP]
    // 0x8fc208: r4 = 0
    //     0x8fc208: movz            x4, #0
    // 0x8fc20c: ldr             x0, [SP, #8]
    // 0x8fc210: r16 = UnlinkedCall_0x613b5c
    //     0x8fc210: add             x16, PP, #0x48, lsl #12  ; [pp+0x48978] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x8fc214: add             x16, x16, #0x978
    // 0x8fc218: ldp             x5, lr, [x16]
    // 0x8fc21c: blr             lr
    // 0x8fc220: r0 = Null
    //     0x8fc220: mov             x0, NULL
    // 0x8fc224: LeaveFrame
    //     0x8fc224: mov             SP, fp
    //     0x8fc228: ldp             fp, lr, [SP], #0x10
    // 0x8fc22c: ret
    //     0x8fc22c: ret             
    // 0x8fc230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc230: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc234: b               #0x8fc0e0
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0x8fc4fc, size: 0x7c
    // 0x8fc4fc: EnterFrame
    //     0x8fc4fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc500: mov             fp, SP
    // 0x8fc504: AllocStack(0x30)
    //     0x8fc504: sub             SP, SP, #0x30
    // 0x8fc508: CheckStackOverflow
    //     0x8fc508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc50c: cmp             SP, x16
    //     0x8fc510: b.ls            #0x8fc570
    // 0x8fc514: r1 = Instance_Color
    //     0x8fc514: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8fc518: d0 = 0.100000
    //     0x8fc518: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x8fc51c: r0 = withOpacity()
    //     0x8fc51c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8fc520: stur            x0, [fp, #-8]
    // 0x8fc524: r0 = Container()
    //     0x8fc524: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x8fc528: stur            x0, [fp, #-0x10]
    // 0x8fc52c: r16 = 80.000000
    //     0x8fc52c: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x8fc530: ldr             x16, [x16, #0x2f8]
    // 0x8fc534: r30 = 80.000000
    //     0x8fc534: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x8fc538: ldr             lr, [lr, #0x2f8]
    // 0x8fc53c: stp             lr, x16, [SP, #0x10]
    // 0x8fc540: ldur            x16, [fp, #-8]
    // 0x8fc544: r30 = Instance_Icon
    //     0x8fc544: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1a8] Obj!Icon@d65c31
    //     0x8fc548: ldr             lr, [lr, #0x1a8]
    // 0x8fc54c: stp             lr, x16, [SP]
    // 0x8fc550: mov             x1, x0
    // 0x8fc554: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0x8fc554: add             x4, PP, #0x48, lsl #12  ; [pp+0x48588] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x8fc558: ldr             x4, [x4, #0x588]
    // 0x8fc55c: r0 = Container()
    //     0x8fc55c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x8fc560: ldur            x0, [fp, #-0x10]
    // 0x8fc564: LeaveFrame
    //     0x8fc564: mov             SP, fp
    //     0x8fc568: ldp             fp, lr, [SP], #0x10
    // 0x8fc56c: ret
    //     0x8fc56c: ret             
    // 0x8fc570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc570: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc574: b               #0x8fc514
  }
  _ build(/* No info */) {
    // ** addr: 0x12912d8, size: 0x3cc
    // 0x12912d8: EnterFrame
    //     0x12912d8: stp             fp, lr, [SP, #-0x10]!
    //     0x12912dc: mov             fp, SP
    // 0x12912e0: AllocStack(0x50)
    //     0x12912e0: sub             SP, SP, #0x50
    // 0x12912e4: SetupParameters(BagBottomSheet this /* r1 => r0, fp-0x20 */, dynamic _ /* r2 => r5, fp-0x28 */)
    //     0x12912e4: mov             x0, x1
    //     0x12912e8: mov             x5, x2
    //     0x12912ec: stur            x1, [fp, #-0x20]
    //     0x12912f0: stur            x2, [fp, #-0x28]
    // 0x12912f4: CheckStackOverflow
    //     0x12912f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12912f8: cmp             SP, x16
    //     0x12912fc: b.ls            #0x129169c
    // 0x1291300: LoadField: r3 = r0->field_b
    //     0x1291300: ldur            w3, [x0, #0xb]
    // 0x1291304: DecompressPointer r3
    //     0x1291304: add             x3, x3, HEAP, lsl #32
    // 0x1291308: stur            x3, [fp, #-0x18]
    // 0x129130c: LoadField: r1 = r3->field_b
    //     0x129130c: ldur            w1, [x3, #0xb]
    // 0x1291310: DecompressPointer r1
    //     0x1291310: add             x1, x1, HEAP, lsl #32
    // 0x1291314: cmp             w1, NULL
    // 0x1291318: b.ne            #0x1291324
    // 0x129131c: r2 = Null
    //     0x129131c: mov             x2, NULL
    // 0x1291320: b               #0x129132c
    // 0x1291324: LoadField: r2 = r1->field_b
    //     0x1291324: ldur            w2, [x1, #0xb]
    // 0x1291328: DecompressPointer r2
    //     0x1291328: add             x2, x2, HEAP, lsl #32
    // 0x129132c: cmp             w2, NULL
    // 0x1291330: b.ne            #0x129133c
    // 0x1291334: r4 = 0
    //     0x1291334: movz            x4, #0
    // 0x1291338: b               #0x1291348
    // 0x129133c: r4 = LoadInt32Instr(r2)
    //     0x129133c: sbfx            x4, x2, #1, #0x1f
    //     0x1291340: tbz             w2, #0, #0x1291348
    //     0x1291344: ldur            x4, [x2, #7]
    // 0x1291348: stur            x4, [fp, #-0x10]
    // 0x129134c: cmp             w1, NULL
    // 0x1291350: b.ne            #0x129135c
    // 0x1291354: r2 = Null
    //     0x1291354: mov             x2, NULL
    // 0x1291358: b               #0x1291364
    // 0x129135c: LoadField: r2 = r1->field_13
    //     0x129135c: ldur            w2, [x1, #0x13]
    // 0x1291360: DecompressPointer r2
    //     0x1291360: add             x2, x2, HEAP, lsl #32
    // 0x1291364: cmp             w2, NULL
    // 0x1291368: b.ne            #0x1291374
    // 0x129136c: r6 = ""
    //     0x129136c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1291370: b               #0x1291378
    // 0x1291374: mov             x6, x2
    // 0x1291378: stur            x6, [fp, #-8]
    // 0x129137c: cmp             w1, NULL
    // 0x1291380: b.ne            #0x129138c
    // 0x1291384: r1 = Null
    //     0x1291384: mov             x1, NULL
    // 0x1291388: b               #0x1291398
    // 0x129138c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x129138c: ldur            w2, [x1, #0x17]
    // 0x1291390: DecompressPointer r2
    //     0x1291390: add             x2, x2, HEAP, lsl #32
    // 0x1291394: mov             x1, x2
    // 0x1291398: cmp             w1, NULL
    // 0x129139c: b.ne            #0x12913b8
    // 0x12913a0: r1 = <Catalogue>
    //     0x12913a0: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x12913a4: ldr             x1, [x1, #0x418]
    // 0x12913a8: r2 = 0
    //     0x12913a8: movz            x2, #0
    // 0x12913ac: r0 = _GrowableList()
    //     0x12913ac: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x12913b0: mov             x2, x0
    // 0x12913b4: b               #0x12913bc
    // 0x12913b8: mov             x2, x1
    // 0x12913bc: ldur            x0, [fp, #-0x18]
    // 0x12913c0: stur            x2, [fp, #-0x38]
    // 0x12913c4: LoadField: r1 = r0->field_b
    //     0x12913c4: ldur            w1, [x0, #0xb]
    // 0x12913c8: DecompressPointer r1
    //     0x12913c8: add             x1, x1, HEAP, lsl #32
    // 0x12913cc: cmp             w1, NULL
    // 0x12913d0: b.ne            #0x12913dc
    // 0x12913d4: r3 = Null
    //     0x12913d4: mov             x3, NULL
    // 0x12913d8: b               #0x12913e8
    // 0x12913dc: LoadField: r0 = r1->field_2f
    //     0x12913dc: ldur            w0, [x1, #0x2f]
    // 0x12913e0: DecompressPointer r0
    //     0x12913e0: add             x0, x0, HEAP, lsl #32
    // 0x12913e4: mov             x3, x0
    // 0x12913e8: stur            x3, [fp, #-0x30]
    // 0x12913ec: cmp             w3, NULL
    // 0x12913f0: b.ne            #0x12913fc
    // 0x12913f4: r0 = Null
    //     0x12913f4: mov             x0, NULL
    // 0x12913f8: b               #0x1291404
    // 0x12913fc: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x12913fc: ldur            w0, [x3, #0x17]
    // 0x1291400: DecompressPointer r0
    //     0x1291400: add             x0, x0, HEAP, lsl #32
    // 0x1291404: cmp             w0, NULL
    // 0x1291408: b.ne            #0x1291410
    // 0x129140c: r0 = false
    //     0x129140c: add             x0, NULL, #0x30  ; false
    // 0x1291410: ldur            x1, [fp, #-0x20]
    // 0x1291414: stur            x0, [fp, #-0x18]
    // 0x1291418: r0 = _buildHeader()
    //     0x1291418: bl              #0x1292b60  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildHeader
    // 0x129141c: r1 = Null
    //     0x129141c: mov             x1, NULL
    // 0x1291420: r2 = 2
    //     0x1291420: movz            x2, #0x2
    // 0x1291424: stur            x0, [fp, #-0x40]
    // 0x1291428: r0 = AllocateArray()
    //     0x1291428: bl              #0x16f7198  ; AllocateArrayStub
    // 0x129142c: mov             x2, x0
    // 0x1291430: ldur            x0, [fp, #-0x40]
    // 0x1291434: stur            x2, [fp, #-0x48]
    // 0x1291438: StoreField: r2->field_f = r0
    //     0x1291438: stur            w0, [x2, #0xf]
    // 0x129143c: r1 = <Widget>
    //     0x129143c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1291440: r0 = AllocateGrowableArray()
    //     0x1291440: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1291444: mov             x4, x0
    // 0x1291448: ldur            x0, [fp, #-0x48]
    // 0x129144c: stur            x4, [fp, #-0x40]
    // 0x1291450: StoreField: r4->field_f = r0
    //     0x1291450: stur            w0, [x4, #0xf]
    // 0x1291454: r0 = 2
    //     0x1291454: movz            x0, #0x2
    // 0x1291458: StoreField: r4->field_b = r0
    //     0x1291458: stur            w0, [x4, #0xb]
    // 0x129145c: ldur            x3, [fp, #-0x30]
    // 0x1291460: cmp             w3, NULL
    // 0x1291464: b.eq            #0x12914fc
    // 0x1291468: ldur            x1, [fp, #-0x20]
    // 0x129146c: ldur            x2, [fp, #-0x18]
    // 0x1291470: ldur            x5, [fp, #-0x28]
    // 0x1291474: r0 = _buildFreeGiftSection()
    //     0x1291474: bl              #0x1292368  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFreeGiftSection
    // 0x1291478: mov             x2, x0
    // 0x129147c: ldur            x0, [fp, #-0x40]
    // 0x1291480: stur            x2, [fp, #-0x18]
    // 0x1291484: LoadField: r1 = r0->field_b
    //     0x1291484: ldur            w1, [x0, #0xb]
    // 0x1291488: LoadField: r3 = r0->field_f
    //     0x1291488: ldur            w3, [x0, #0xf]
    // 0x129148c: DecompressPointer r3
    //     0x129148c: add             x3, x3, HEAP, lsl #32
    // 0x1291490: LoadField: r4 = r3->field_b
    //     0x1291490: ldur            w4, [x3, #0xb]
    // 0x1291494: r3 = LoadInt32Instr(r1)
    //     0x1291494: sbfx            x3, x1, #1, #0x1f
    // 0x1291498: stur            x3, [fp, #-0x50]
    // 0x129149c: r1 = LoadInt32Instr(r4)
    //     0x129149c: sbfx            x1, x4, #1, #0x1f
    // 0x12914a0: cmp             x3, x1
    // 0x12914a4: b.ne            #0x12914b0
    // 0x12914a8: mov             x1, x0
    // 0x12914ac: r0 = _growToNextCapacity()
    //     0x12914ac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12914b0: ldur            x3, [fp, #-0x40]
    // 0x12914b4: ldur            x2, [fp, #-0x50]
    // 0x12914b8: add             x0, x2, #1
    // 0x12914bc: lsl             x1, x0, #1
    // 0x12914c0: StoreField: r3->field_b = r1
    //     0x12914c0: stur            w1, [x3, #0xb]
    // 0x12914c4: LoadField: r1 = r3->field_f
    //     0x12914c4: ldur            w1, [x3, #0xf]
    // 0x12914c8: DecompressPointer r1
    //     0x12914c8: add             x1, x1, HEAP, lsl #32
    // 0x12914cc: ldur            x0, [fp, #-0x18]
    // 0x12914d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x12914d0: add             x25, x1, x2, lsl #2
    //     0x12914d4: add             x25, x25, #0xf
    //     0x12914d8: str             w0, [x25]
    //     0x12914dc: tbz             w0, #0, #0x12914f8
    //     0x12914e0: ldurb           w16, [x1, #-1]
    //     0x12914e4: ldurb           w17, [x0, #-1]
    //     0x12914e8: and             x16, x17, x16, lsr #2
    //     0x12914ec: tst             x16, HEAP, lsr #32
    //     0x12914f0: b.eq            #0x12914f8
    //     0x12914f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12914f8: b               #0x1291500
    // 0x12914fc: mov             x3, x4
    // 0x1291500: ldur            x1, [fp, #-0x20]
    // 0x1291504: ldur            x2, [fp, #-0x38]
    // 0x1291508: r0 = _buildProductList()
    //     0x1291508: bl              #0x1292204  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductList
    // 0x129150c: mov             x2, x0
    // 0x1291510: ldur            x0, [fp, #-0x40]
    // 0x1291514: stur            x2, [fp, #-0x18]
    // 0x1291518: LoadField: r1 = r0->field_b
    //     0x1291518: ldur            w1, [x0, #0xb]
    // 0x129151c: LoadField: r3 = r0->field_f
    //     0x129151c: ldur            w3, [x0, #0xf]
    // 0x1291520: DecompressPointer r3
    //     0x1291520: add             x3, x3, HEAP, lsl #32
    // 0x1291524: LoadField: r4 = r3->field_b
    //     0x1291524: ldur            w4, [x3, #0xb]
    // 0x1291528: r3 = LoadInt32Instr(r1)
    //     0x1291528: sbfx            x3, x1, #1, #0x1f
    // 0x129152c: stur            x3, [fp, #-0x50]
    // 0x1291530: r1 = LoadInt32Instr(r4)
    //     0x1291530: sbfx            x1, x4, #1, #0x1f
    // 0x1291534: cmp             x3, x1
    // 0x1291538: b.ne            #0x1291544
    // 0x129153c: mov             x1, x0
    // 0x1291540: r0 = _growToNextCapacity()
    //     0x1291540: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1291544: ldur            x4, [fp, #-0x40]
    // 0x1291548: ldur            x2, [fp, #-0x50]
    // 0x129154c: add             x0, x2, #1
    // 0x1291550: lsl             x1, x0, #1
    // 0x1291554: StoreField: r4->field_b = r1
    //     0x1291554: stur            w1, [x4, #0xb]
    // 0x1291558: LoadField: r1 = r4->field_f
    //     0x1291558: ldur            w1, [x4, #0xf]
    // 0x129155c: DecompressPointer r1
    //     0x129155c: add             x1, x1, HEAP, lsl #32
    // 0x1291560: ldur            x0, [fp, #-0x18]
    // 0x1291564: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1291564: add             x25, x1, x2, lsl #2
    //     0x1291568: add             x25, x25, #0xf
    //     0x129156c: str             w0, [x25]
    //     0x1291570: tbz             w0, #0, #0x129158c
    //     0x1291574: ldurb           w16, [x1, #-1]
    //     0x1291578: ldurb           w17, [x0, #-1]
    //     0x129157c: and             x16, x17, x16, lsr #2
    //     0x1291580: tst             x16, HEAP, lsr #32
    //     0x1291584: b.eq            #0x129158c
    //     0x1291588: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x129158c: ldur            x1, [fp, #-0x20]
    // 0x1291590: ldur            x2, [fp, #-0x10]
    // 0x1291594: ldur            x3, [fp, #-8]
    // 0x1291598: r0 = _buildFooter()
    //     0x1291598: bl              #0x12916a4  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFooter
    // 0x129159c: mov             x2, x0
    // 0x12915a0: ldur            x0, [fp, #-0x40]
    // 0x12915a4: stur            x2, [fp, #-8]
    // 0x12915a8: LoadField: r1 = r0->field_b
    //     0x12915a8: ldur            w1, [x0, #0xb]
    // 0x12915ac: LoadField: r3 = r0->field_f
    //     0x12915ac: ldur            w3, [x0, #0xf]
    // 0x12915b0: DecompressPointer r3
    //     0x12915b0: add             x3, x3, HEAP, lsl #32
    // 0x12915b4: LoadField: r4 = r3->field_b
    //     0x12915b4: ldur            w4, [x3, #0xb]
    // 0x12915b8: r3 = LoadInt32Instr(r1)
    //     0x12915b8: sbfx            x3, x1, #1, #0x1f
    // 0x12915bc: stur            x3, [fp, #-0x10]
    // 0x12915c0: r1 = LoadInt32Instr(r4)
    //     0x12915c0: sbfx            x1, x4, #1, #0x1f
    // 0x12915c4: cmp             x3, x1
    // 0x12915c8: b.ne            #0x12915d4
    // 0x12915cc: mov             x1, x0
    // 0x12915d0: r0 = _growToNextCapacity()
    //     0x12915d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x12915d4: ldur            x2, [fp, #-0x40]
    // 0x12915d8: ldur            x3, [fp, #-0x10]
    // 0x12915dc: add             x0, x3, #1
    // 0x12915e0: lsl             x1, x0, #1
    // 0x12915e4: StoreField: r2->field_b = r1
    //     0x12915e4: stur            w1, [x2, #0xb]
    // 0x12915e8: LoadField: r1 = r2->field_f
    //     0x12915e8: ldur            w1, [x2, #0xf]
    // 0x12915ec: DecompressPointer r1
    //     0x12915ec: add             x1, x1, HEAP, lsl #32
    // 0x12915f0: ldur            x0, [fp, #-8]
    // 0x12915f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x12915f4: add             x25, x1, x3, lsl #2
    //     0x12915f8: add             x25, x25, #0xf
    //     0x12915fc: str             w0, [x25]
    //     0x1291600: tbz             w0, #0, #0x129161c
    //     0x1291604: ldurb           w16, [x1, #-1]
    //     0x1291608: ldurb           w17, [x0, #-1]
    //     0x129160c: and             x16, x17, x16, lsr #2
    //     0x1291610: tst             x16, HEAP, lsr #32
    //     0x1291614: b.eq            #0x129161c
    //     0x1291618: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x129161c: r0 = Column()
    //     0x129161c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1291620: mov             x1, x0
    // 0x1291624: r0 = Instance_Axis
    //     0x1291624: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1291628: stur            x1, [fp, #-8]
    // 0x129162c: StoreField: r1->field_f = r0
    //     0x129162c: stur            w0, [x1, #0xf]
    // 0x1291630: r0 = Instance_MainAxisAlignment
    //     0x1291630: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1291634: ldr             x0, [x0, #0xa08]
    // 0x1291638: StoreField: r1->field_13 = r0
    //     0x1291638: stur            w0, [x1, #0x13]
    // 0x129163c: r0 = Instance_MainAxisSize
    //     0x129163c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1291640: ldr             x0, [x0, #0xdd0]
    // 0x1291644: ArrayStore: r1[0] = r0  ; List_4
    //     0x1291644: stur            w0, [x1, #0x17]
    // 0x1291648: r0 = Instance_CrossAxisAlignment
    //     0x1291648: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x129164c: ldr             x0, [x0, #0xa18]
    // 0x1291650: StoreField: r1->field_1b = r0
    //     0x1291650: stur            w0, [x1, #0x1b]
    // 0x1291654: r0 = Instance_VerticalDirection
    //     0x1291654: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1291658: ldr             x0, [x0, #0xa20]
    // 0x129165c: StoreField: r1->field_23 = r0
    //     0x129165c: stur            w0, [x1, #0x23]
    // 0x1291660: r0 = Instance_Clip
    //     0x1291660: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1291664: ldr             x0, [x0, #0x38]
    // 0x1291668: StoreField: r1->field_2b = r0
    //     0x1291668: stur            w0, [x1, #0x2b]
    // 0x129166c: StoreField: r1->field_2f = rZR
    //     0x129166c: stur            xzr, [x1, #0x2f]
    // 0x1291670: ldur            x0, [fp, #-0x40]
    // 0x1291674: StoreField: r1->field_b = r0
    //     0x1291674: stur            w0, [x1, #0xb]
    // 0x1291678: r0 = Padding()
    //     0x1291678: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x129167c: r1 = Instance_EdgeInsets
    //     0x129167c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40be0] Obj!EdgeInsets@d57da1
    //     0x1291680: ldr             x1, [x1, #0xbe0]
    // 0x1291684: StoreField: r0->field_f = r1
    //     0x1291684: stur            w1, [x0, #0xf]
    // 0x1291688: ldur            x1, [fp, #-8]
    // 0x129168c: StoreField: r0->field_b = r1
    //     0x129168c: stur            w1, [x0, #0xb]
    // 0x1291690: LeaveFrame
    //     0x1291690: mov             SP, fp
    //     0x1291694: ldp             fp, lr, [SP], #0x10
    // 0x1291698: ret
    //     0x1291698: ret             
    // 0x129169c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x129169c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12916a0: b               #0x1291300
  }
  _ _buildFooter(/* No info */) {
    // ** addr: 0x12916a4, size: 0x428
    // 0x12916a4: EnterFrame
    //     0x12916a4: stp             fp, lr, [SP, #-0x10]!
    //     0x12916a8: mov             fp, SP
    // 0x12916ac: AllocStack(0x58)
    //     0x12916ac: sub             SP, SP, #0x58
    // 0x12916b0: SetupParameters(BagBottomSheet this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x12916b0: mov             x0, x1
    //     0x12916b4: stur            x1, [fp, #-0x10]
    //     0x12916b8: stur            x2, [fp, #-0x18]
    //     0x12916bc: stur            x3, [fp, #-0x20]
    // 0x12916c0: CheckStackOverflow
    //     0x12916c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12916c4: cmp             SP, x16
    //     0x12916c8: b.ls            #0x1291ac4
    // 0x12916cc: LoadField: r4 = r0->field_1f
    //     0x12916cc: ldur            w4, [x0, #0x1f]
    // 0x12916d0: DecompressPointer r4
    //     0x12916d0: add             x4, x4, HEAP, lsl #32
    // 0x12916d4: stur            x4, [fp, #-8]
    // 0x12916d8: r1 = Instance_Color
    //     0x12916d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12916dc: d0 = 0.100000
    //     0x12916dc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x12916e0: r0 = withOpacity()
    //     0x12916e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12916e4: stur            x0, [fp, #-0x28]
    // 0x12916e8: r0 = Divider()
    //     0x12916e8: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x12916ec: mov             x3, x0
    // 0x12916f0: ldur            x0, [fp, #-0x28]
    // 0x12916f4: stur            x3, [fp, #-0x30]
    // 0x12916f8: StoreField: r3->field_1f = r0
    //     0x12916f8: stur            w0, [x3, #0x1f]
    // 0x12916fc: r1 = Null
    //     0x12916fc: mov             x1, NULL
    // 0x1291700: r2 = 6
    //     0x1291700: movz            x2, #0x6
    // 0x1291704: r0 = AllocateArray()
    //     0x1291704: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1291708: mov             x2, x0
    // 0x129170c: r16 = "Subtotal ("
    //     0x129170c: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e0] "Subtotal ("
    //     0x1291710: ldr             x16, [x16, #0x4e0]
    // 0x1291714: StoreField: r2->field_f = r16
    //     0x1291714: stur            w16, [x2, #0xf]
    // 0x1291718: ldur            x3, [fp, #-0x18]
    // 0x129171c: r0 = BoxInt64Instr(r3)
    //     0x129171c: sbfiz           x0, x3, #1, #0x1f
    //     0x1291720: cmp             x3, x0, asr #1
    //     0x1291724: b.eq            #0x1291730
    //     0x1291728: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x129172c: stur            x3, [x0, #7]
    // 0x1291730: StoreField: r2->field_13 = r0
    //     0x1291730: stur            w0, [x2, #0x13]
    // 0x1291734: r16 = " item)"
    //     0x1291734: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e8] " item)"
    //     0x1291738: ldr             x16, [x16, #0x4e8]
    // 0x129173c: ArrayStore: r2[0] = r16  ; List_4
    //     0x129173c: stur            w16, [x2, #0x17]
    // 0x1291740: str             x2, [SP]
    // 0x1291744: r0 = _interpolate()
    //     0x1291744: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1291748: stur            x0, [fp, #-0x28]
    // 0x129174c: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x129174c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1291750: ldr             x0, [x0, #0x1ab0]
    //     0x1291754: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1291758: cmp             w0, w16
    //     0x129175c: b.ne            #0x129176c
    //     0x1291760: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x1291764: ldr             x2, [x2, #0x60]
    //     0x1291768: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x129176c: LoadField: r2 = r0->field_87
    //     0x129176c: ldur            w2, [x0, #0x87]
    // 0x1291770: DecompressPointer r2
    //     0x1291770: add             x2, x2, HEAP, lsl #32
    // 0x1291774: stur            x2, [fp, #-0x40]
    // 0x1291778: LoadField: r0 = r2->field_2b
    //     0x1291778: ldur            w0, [x2, #0x2b]
    // 0x129177c: DecompressPointer r0
    //     0x129177c: add             x0, x0, HEAP, lsl #32
    // 0x1291780: stur            x0, [fp, #-0x38]
    // 0x1291784: r1 = Instance_Color
    //     0x1291784: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1291788: d0 = 0.700000
    //     0x1291788: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x129178c: ldr             d0, [x17, #0xf48]
    // 0x1291790: r0 = withOpacity()
    //     0x1291790: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1291794: r16 = 14.000000
    //     0x1291794: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1291798: ldr             x16, [x16, #0x1d8]
    // 0x129179c: stp             x16, x0, [SP]
    // 0x12917a0: ldur            x1, [fp, #-0x38]
    // 0x12917a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x12917a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x12917a8: ldr             x4, [x4, #0x9b8]
    // 0x12917ac: r0 = copyWith()
    //     0x12917ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12917b0: stur            x0, [fp, #-0x38]
    // 0x12917b4: r0 = Text()
    //     0x12917b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12917b8: mov             x2, x0
    // 0x12917bc: ldur            x0, [fp, #-0x28]
    // 0x12917c0: stur            x2, [fp, #-0x48]
    // 0x12917c4: StoreField: r2->field_b = r0
    //     0x12917c4: stur            w0, [x2, #0xb]
    // 0x12917c8: ldur            x0, [fp, #-0x38]
    // 0x12917cc: StoreField: r2->field_13 = r0
    //     0x12917cc: stur            w0, [x2, #0x13]
    // 0x12917d0: ldur            x0, [fp, #-0x40]
    // 0x12917d4: LoadField: r1 = r0->field_7
    //     0x12917d4: ldur            w1, [x0, #7]
    // 0x12917d8: DecompressPointer r1
    //     0x12917d8: add             x1, x1, HEAP, lsl #32
    // 0x12917dc: r16 = Instance_Color
    //     0x12917dc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12917e0: r30 = 21.000000
    //     0x12917e0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x12917e4: ldr             lr, [lr, #0x9b0]
    // 0x12917e8: stp             lr, x16, [SP]
    // 0x12917ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x12917ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x12917f0: ldr             x4, [x4, #0x9b8]
    // 0x12917f4: r0 = copyWith()
    //     0x12917f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12917f8: stur            x0, [fp, #-0x28]
    // 0x12917fc: r0 = Text()
    //     0x12917fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1291800: mov             x3, x0
    // 0x1291804: ldur            x0, [fp, #-0x20]
    // 0x1291808: stur            x3, [fp, #-0x38]
    // 0x129180c: StoreField: r3->field_b = r0
    //     0x129180c: stur            w0, [x3, #0xb]
    // 0x1291810: ldur            x0, [fp, #-0x28]
    // 0x1291814: StoreField: r3->field_13 = r0
    //     0x1291814: stur            w0, [x3, #0x13]
    // 0x1291818: r1 = Null
    //     0x1291818: mov             x1, NULL
    // 0x129181c: r2 = 6
    //     0x129181c: movz            x2, #0x6
    // 0x1291820: r0 = AllocateArray()
    //     0x1291820: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1291824: mov             x2, x0
    // 0x1291828: ldur            x0, [fp, #-0x48]
    // 0x129182c: stur            x2, [fp, #-0x20]
    // 0x1291830: StoreField: r2->field_f = r0
    //     0x1291830: stur            w0, [x2, #0xf]
    // 0x1291834: r16 = Instance_Spacer
    //     0x1291834: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x1291838: ldr             x16, [x16, #0xf0]
    // 0x129183c: StoreField: r2->field_13 = r16
    //     0x129183c: stur            w16, [x2, #0x13]
    // 0x1291840: ldur            x0, [fp, #-0x38]
    // 0x1291844: ArrayStore: r2[0] = r0  ; List_4
    //     0x1291844: stur            w0, [x2, #0x17]
    // 0x1291848: r1 = <Widget>
    //     0x1291848: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129184c: r0 = AllocateGrowableArray()
    //     0x129184c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1291850: mov             x1, x0
    // 0x1291854: ldur            x0, [fp, #-0x20]
    // 0x1291858: stur            x1, [fp, #-0x28]
    // 0x129185c: StoreField: r1->field_f = r0
    //     0x129185c: stur            w0, [x1, #0xf]
    // 0x1291860: r0 = 6
    //     0x1291860: movz            x0, #0x6
    // 0x1291864: StoreField: r1->field_b = r0
    //     0x1291864: stur            w0, [x1, #0xb]
    // 0x1291868: r0 = Row()
    //     0x1291868: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x129186c: mov             x1, x0
    // 0x1291870: r0 = Instance_Axis
    //     0x1291870: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1291874: stur            x1, [fp, #-0x20]
    // 0x1291878: StoreField: r1->field_f = r0
    //     0x1291878: stur            w0, [x1, #0xf]
    // 0x129187c: r0 = Instance_MainAxisAlignment
    //     0x129187c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1291880: ldr             x0, [x0, #0xa08]
    // 0x1291884: StoreField: r1->field_13 = r0
    //     0x1291884: stur            w0, [x1, #0x13]
    // 0x1291888: r2 = Instance_MainAxisSize
    //     0x1291888: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x129188c: ldr             x2, [x2, #0xa10]
    // 0x1291890: ArrayStore: r1[0] = r2  ; List_4
    //     0x1291890: stur            w2, [x1, #0x17]
    // 0x1291894: r3 = Instance_CrossAxisAlignment
    //     0x1291894: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1291898: ldr             x3, [x3, #0xa18]
    // 0x129189c: StoreField: r1->field_1b = r3
    //     0x129189c: stur            w3, [x1, #0x1b]
    // 0x12918a0: r3 = Instance_VerticalDirection
    //     0x12918a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12918a4: ldr             x3, [x3, #0xa20]
    // 0x12918a8: StoreField: r1->field_23 = r3
    //     0x12918a8: stur            w3, [x1, #0x23]
    // 0x12918ac: r4 = Instance_Clip
    //     0x12918ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12918b0: ldr             x4, [x4, #0x38]
    // 0x12918b4: StoreField: r1->field_2b = r4
    //     0x12918b4: stur            w4, [x1, #0x2b]
    // 0x12918b8: StoreField: r1->field_2f = rZR
    //     0x12918b8: stur            xzr, [x1, #0x2f]
    // 0x12918bc: ldur            x5, [fp, #-0x28]
    // 0x12918c0: StoreField: r1->field_b = r5
    //     0x12918c0: stur            w5, [x1, #0xb]
    // 0x12918c4: r0 = Padding()
    //     0x12918c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12918c8: mov             x3, x0
    // 0x12918cc: r0 = Instance_EdgeInsets
    //     0x12918cc: add             x0, PP, #0x48, lsl #12  ; [pp+0x48888] Obj!EdgeInsets@d59361
    //     0x12918d0: ldr             x0, [x0, #0x888]
    // 0x12918d4: stur            x3, [fp, #-0x28]
    // 0x12918d8: StoreField: r3->field_f = r0
    //     0x12918d8: stur            w0, [x3, #0xf]
    // 0x12918dc: ldur            x0, [fp, #-0x20]
    // 0x12918e0: StoreField: r3->field_b = r0
    //     0x12918e0: stur            w0, [x3, #0xb]
    // 0x12918e4: r1 = Null
    //     0x12918e4: mov             x1, NULL
    // 0x12918e8: r2 = 4
    //     0x12918e8: movz            x2, #0x4
    // 0x12918ec: r0 = AllocateArray()
    //     0x12918ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12918f0: mov             x2, x0
    // 0x12918f4: ldur            x0, [fp, #-0x30]
    // 0x12918f8: stur            x2, [fp, #-0x20]
    // 0x12918fc: StoreField: r2->field_f = r0
    //     0x12918fc: stur            w0, [x2, #0xf]
    // 0x1291900: ldur            x0, [fp, #-0x28]
    // 0x1291904: StoreField: r2->field_13 = r0
    //     0x1291904: stur            w0, [x2, #0x13]
    // 0x1291908: r1 = <Widget>
    //     0x1291908: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129190c: r0 = AllocateGrowableArray()
    //     0x129190c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1291910: mov             x2, x0
    // 0x1291914: ldur            x0, [fp, #-0x20]
    // 0x1291918: stur            x2, [fp, #-0x28]
    // 0x129191c: StoreField: r2->field_f = r0
    //     0x129191c: stur            w0, [x2, #0xf]
    // 0x1291920: r0 = 4
    //     0x1291920: movz            x0, #0x4
    // 0x1291924: StoreField: r2->field_b = r0
    //     0x1291924: stur            w0, [x2, #0xb]
    // 0x1291928: ldur            x0, [fp, #-8]
    // 0x129192c: cmp             w0, NULL
    // 0x1291930: b.eq            #0x12919c0
    // 0x1291934: ldur            x1, [fp, #-0x10]
    // 0x1291938: r0 = _buildSingleButton()
    //     0x1291938: bl              #0x1291fb0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildSingleButton
    // 0x129193c: mov             x2, x0
    // 0x1291940: ldur            x0, [fp, #-0x28]
    // 0x1291944: stur            x2, [fp, #-8]
    // 0x1291948: LoadField: r1 = r0->field_b
    //     0x1291948: ldur            w1, [x0, #0xb]
    // 0x129194c: LoadField: r3 = r0->field_f
    //     0x129194c: ldur            w3, [x0, #0xf]
    // 0x1291950: DecompressPointer r3
    //     0x1291950: add             x3, x3, HEAP, lsl #32
    // 0x1291954: LoadField: r4 = r3->field_b
    //     0x1291954: ldur            w4, [x3, #0xb]
    // 0x1291958: r3 = LoadInt32Instr(r1)
    //     0x1291958: sbfx            x3, x1, #1, #0x1f
    // 0x129195c: stur            x3, [fp, #-0x18]
    // 0x1291960: r1 = LoadInt32Instr(r4)
    //     0x1291960: sbfx            x1, x4, #1, #0x1f
    // 0x1291964: cmp             x3, x1
    // 0x1291968: b.ne            #0x1291974
    // 0x129196c: mov             x1, x0
    // 0x1291970: r0 = _growToNextCapacity()
    //     0x1291970: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1291974: ldur            x2, [fp, #-0x28]
    // 0x1291978: ldur            x3, [fp, #-0x18]
    // 0x129197c: add             x0, x3, #1
    // 0x1291980: lsl             x1, x0, #1
    // 0x1291984: StoreField: r2->field_b = r1
    //     0x1291984: stur            w1, [x2, #0xb]
    // 0x1291988: LoadField: r1 = r2->field_f
    //     0x1291988: ldur            w1, [x2, #0xf]
    // 0x129198c: DecompressPointer r1
    //     0x129198c: add             x1, x1, HEAP, lsl #32
    // 0x1291990: ldur            x0, [fp, #-8]
    // 0x1291994: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1291994: add             x25, x1, x3, lsl #2
    //     0x1291998: add             x25, x25, #0xf
    //     0x129199c: str             w0, [x25]
    //     0x12919a0: tbz             w0, #0, #0x12919bc
    //     0x12919a4: ldurb           w16, [x1, #-1]
    //     0x12919a8: ldurb           w17, [x0, #-1]
    //     0x12919ac: and             x16, x17, x16, lsr #2
    //     0x12919b0: tst             x16, HEAP, lsr #32
    //     0x12919b4: b.eq            #0x12919bc
    //     0x12919b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x12919bc: b               #0x1291a48
    // 0x12919c0: ldur            x1, [fp, #-0x10]
    // 0x12919c4: r0 = _buildDualButtons()
    //     0x12919c4: bl              #0x1291acc  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons
    // 0x12919c8: mov             x2, x0
    // 0x12919cc: ldur            x0, [fp, #-0x28]
    // 0x12919d0: stur            x2, [fp, #-8]
    // 0x12919d4: LoadField: r1 = r0->field_b
    //     0x12919d4: ldur            w1, [x0, #0xb]
    // 0x12919d8: LoadField: r3 = r0->field_f
    //     0x12919d8: ldur            w3, [x0, #0xf]
    // 0x12919dc: DecompressPointer r3
    //     0x12919dc: add             x3, x3, HEAP, lsl #32
    // 0x12919e0: LoadField: r4 = r3->field_b
    //     0x12919e0: ldur            w4, [x3, #0xb]
    // 0x12919e4: r3 = LoadInt32Instr(r1)
    //     0x12919e4: sbfx            x3, x1, #1, #0x1f
    // 0x12919e8: stur            x3, [fp, #-0x18]
    // 0x12919ec: r1 = LoadInt32Instr(r4)
    //     0x12919ec: sbfx            x1, x4, #1, #0x1f
    // 0x12919f0: cmp             x3, x1
    // 0x12919f4: b.ne            #0x1291a00
    // 0x12919f8: mov             x1, x0
    // 0x12919fc: r0 = _growToNextCapacity()
    //     0x12919fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1291a00: ldur            x2, [fp, #-0x28]
    // 0x1291a04: ldur            x3, [fp, #-0x18]
    // 0x1291a08: add             x0, x3, #1
    // 0x1291a0c: lsl             x1, x0, #1
    // 0x1291a10: StoreField: r2->field_b = r1
    //     0x1291a10: stur            w1, [x2, #0xb]
    // 0x1291a14: LoadField: r1 = r2->field_f
    //     0x1291a14: ldur            w1, [x2, #0xf]
    // 0x1291a18: DecompressPointer r1
    //     0x1291a18: add             x1, x1, HEAP, lsl #32
    // 0x1291a1c: ldur            x0, [fp, #-8]
    // 0x1291a20: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1291a20: add             x25, x1, x3, lsl #2
    //     0x1291a24: add             x25, x25, #0xf
    //     0x1291a28: str             w0, [x25]
    //     0x1291a2c: tbz             w0, #0, #0x1291a48
    //     0x1291a30: ldurb           w16, [x1, #-1]
    //     0x1291a34: ldurb           w17, [x0, #-1]
    //     0x1291a38: and             x16, x17, x16, lsr #2
    //     0x1291a3c: tst             x16, HEAP, lsr #32
    //     0x1291a40: b.eq            #0x1291a48
    //     0x1291a44: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1291a48: r0 = Column()
    //     0x1291a48: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1291a4c: mov             x1, x0
    // 0x1291a50: r0 = Instance_Axis
    //     0x1291a50: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1291a54: stur            x1, [fp, #-8]
    // 0x1291a58: StoreField: r1->field_f = r0
    //     0x1291a58: stur            w0, [x1, #0xf]
    // 0x1291a5c: r0 = Instance_MainAxisAlignment
    //     0x1291a5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1291a60: ldr             x0, [x0, #0xa08]
    // 0x1291a64: StoreField: r1->field_13 = r0
    //     0x1291a64: stur            w0, [x1, #0x13]
    // 0x1291a68: r0 = Instance_MainAxisSize
    //     0x1291a68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1291a6c: ldr             x0, [x0, #0xa10]
    // 0x1291a70: ArrayStore: r1[0] = r0  ; List_4
    //     0x1291a70: stur            w0, [x1, #0x17]
    // 0x1291a74: r0 = Instance_CrossAxisAlignment
    //     0x1291a74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1291a78: ldr             x0, [x0, #0x890]
    // 0x1291a7c: StoreField: r1->field_1b = r0
    //     0x1291a7c: stur            w0, [x1, #0x1b]
    // 0x1291a80: r0 = Instance_VerticalDirection
    //     0x1291a80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1291a84: ldr             x0, [x0, #0xa20]
    // 0x1291a88: StoreField: r1->field_23 = r0
    //     0x1291a88: stur            w0, [x1, #0x23]
    // 0x1291a8c: r0 = Instance_Clip
    //     0x1291a8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1291a90: ldr             x0, [x0, #0x38]
    // 0x1291a94: StoreField: r1->field_2b = r0
    //     0x1291a94: stur            w0, [x1, #0x2b]
    // 0x1291a98: StoreField: r1->field_2f = rZR
    //     0x1291a98: stur            xzr, [x1, #0x2f]
    // 0x1291a9c: ldur            x0, [fp, #-0x28]
    // 0x1291aa0: StoreField: r1->field_b = r0
    //     0x1291aa0: stur            w0, [x1, #0xb]
    // 0x1291aa4: r0 = ColoredBox()
    //     0x1291aa4: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x1291aa8: r1 = Instance_Color
    //     0x1291aa8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1291aac: StoreField: r0->field_f = r1
    //     0x1291aac: stur            w1, [x0, #0xf]
    // 0x1291ab0: ldur            x1, [fp, #-8]
    // 0x1291ab4: StoreField: r0->field_b = r1
    //     0x1291ab4: stur            w1, [x0, #0xb]
    // 0x1291ab8: LeaveFrame
    //     0x1291ab8: mov             SP, fp
    //     0x1291abc: ldp             fp, lr, [SP], #0x10
    // 0x1291ac0: ret
    //     0x1291ac0: ret             
    // 0x1291ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1291ac4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1291ac8: b               #0x12916cc
  }
  _ _buildDualButtons(/* No info */) {
    // ** addr: 0x1291acc, size: 0x41c
    // 0x1291acc: EnterFrame
    //     0x1291acc: stp             fp, lr, [SP, #-0x10]!
    //     0x1291ad0: mov             fp, SP
    // 0x1291ad4: AllocStack(0x48)
    //     0x1291ad4: sub             SP, SP, #0x48
    // 0x1291ad8: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x1291ad8: stur            x1, [fp, #-8]
    // 0x1291adc: CheckStackOverflow
    //     0x1291adc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1291ae0: cmp             SP, x16
    //     0x1291ae4: b.ls            #0x1291ee0
    // 0x1291ae8: r1 = 1
    //     0x1291ae8: movz            x1, #0x1
    // 0x1291aec: r0 = AllocateContext()
    //     0x1291aec: bl              #0x16f6108  ; AllocateContextStub
    // 0x1291af0: mov             x1, x0
    // 0x1291af4: ldur            x0, [fp, #-8]
    // 0x1291af8: stur            x1, [fp, #-0x10]
    // 0x1291afc: StoreField: r1->field_f = r0
    //     0x1291afc: stur            w0, [x1, #0xf]
    // 0x1291b00: r16 = <EdgeInsets>
    //     0x1291b00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1291b04: ldr             x16, [x16, #0xda0]
    // 0x1291b08: r30 = Instance_EdgeInsets
    //     0x1291b08: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1291b0c: ldr             lr, [lr, #0x1f0]
    // 0x1291b10: stp             lr, x16, [SP]
    // 0x1291b14: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1291b14: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1291b18: r0 = all()
    //     0x1291b18: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1291b1c: mov             x1, x0
    // 0x1291b20: ldur            x0, [fp, #-8]
    // 0x1291b24: stur            x1, [fp, #-0x20]
    // 0x1291b28: LoadField: r2 = r0->field_23
    //     0x1291b28: ldur            w2, [x0, #0x23]
    // 0x1291b2c: DecompressPointer r2
    //     0x1291b2c: add             x2, x2, HEAP, lsl #32
    // 0x1291b30: stur            x2, [fp, #-0x18]
    // 0x1291b34: r0 = BorderSide()
    //     0x1291b34: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1291b38: mov             x1, x0
    // 0x1291b3c: ldur            x0, [fp, #-0x18]
    // 0x1291b40: stur            x1, [fp, #-8]
    // 0x1291b44: StoreField: r1->field_7 = r0
    //     0x1291b44: stur            w0, [x1, #7]
    // 0x1291b48: d0 = 1.000000
    //     0x1291b48: fmov            d0, #1.00000000
    // 0x1291b4c: StoreField: r1->field_b = d0
    //     0x1291b4c: stur            d0, [x1, #0xb]
    // 0x1291b50: r2 = Instance_BorderStyle
    //     0x1291b50: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1291b54: ldr             x2, [x2, #0xf68]
    // 0x1291b58: StoreField: r1->field_13 = r2
    //     0x1291b58: stur            w2, [x1, #0x13]
    // 0x1291b5c: d0 = -1.000000
    //     0x1291b5c: fmov            d0, #-1.00000000
    // 0x1291b60: ArrayStore: r1[0] = d0  ; List_8
    //     0x1291b60: stur            d0, [x1, #0x17]
    // 0x1291b64: r0 = RoundedRectangleBorder()
    //     0x1291b64: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1291b68: mov             x1, x0
    // 0x1291b6c: r0 = Instance_BorderRadius
    //     0x1291b6c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fdc0] Obj!BorderRadius@d5a301
    //     0x1291b70: ldr             x0, [x0, #0xdc0]
    // 0x1291b74: StoreField: r1->field_b = r0
    //     0x1291b74: stur            w0, [x1, #0xb]
    // 0x1291b78: ldur            x0, [fp, #-8]
    // 0x1291b7c: StoreField: r1->field_7 = r0
    //     0x1291b7c: stur            w0, [x1, #7]
    // 0x1291b80: r16 = <RoundedRectangleBorder>
    //     0x1291b80: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1291b84: ldr             x16, [x16, #0xf78]
    // 0x1291b88: stp             x1, x16, [SP]
    // 0x1291b8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1291b8c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1291b90: r0 = all()
    //     0x1291b90: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1291b94: stur            x0, [fp, #-8]
    // 0x1291b98: r0 = ButtonStyle()
    //     0x1291b98: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1291b9c: mov             x1, x0
    // 0x1291ba0: ldur            x0, [fp, #-0x20]
    // 0x1291ba4: stur            x1, [fp, #-0x28]
    // 0x1291ba8: StoreField: r1->field_23 = r0
    //     0x1291ba8: stur            w0, [x1, #0x23]
    // 0x1291bac: ldur            x0, [fp, #-8]
    // 0x1291bb0: StoreField: r1->field_43 = r0
    //     0x1291bb0: stur            w0, [x1, #0x43]
    // 0x1291bb4: r16 = <EdgeInsets>
    //     0x1291bb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1291bb8: ldr             x16, [x16, #0xda0]
    // 0x1291bbc: r30 = Instance_EdgeInsets
    //     0x1291bbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1291bc0: ldr             lr, [lr, #0x1f0]
    // 0x1291bc4: stp             lr, x16, [SP]
    // 0x1291bc8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1291bc8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1291bcc: r0 = all()
    //     0x1291bcc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1291bd0: stur            x0, [fp, #-8]
    // 0x1291bd4: r16 = <Color>
    //     0x1291bd4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1291bd8: ldr             x16, [x16, #0xf80]
    // 0x1291bdc: ldur            lr, [fp, #-0x18]
    // 0x1291be0: stp             lr, x16, [SP]
    // 0x1291be4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1291be4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1291be8: r0 = all()
    //     0x1291be8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1291bec: stur            x0, [fp, #-0x20]
    // 0x1291bf0: r16 = <RoundedRectangleBorder>
    //     0x1291bf0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1291bf4: ldr             x16, [x16, #0xf78]
    // 0x1291bf8: r30 = Instance_RoundedRectangleBorder
    //     0x1291bf8: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x1291bfc: ldr             lr, [lr, #0x888]
    // 0x1291c00: stp             lr, x16, [SP]
    // 0x1291c04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1291c04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1291c08: r0 = all()
    //     0x1291c08: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1291c0c: stur            x0, [fp, #-0x30]
    // 0x1291c10: r0 = ButtonStyle()
    //     0x1291c10: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1291c14: mov             x1, x0
    // 0x1291c18: ldur            x0, [fp, #-0x20]
    // 0x1291c1c: stur            x1, [fp, #-0x38]
    // 0x1291c20: StoreField: r1->field_b = r0
    //     0x1291c20: stur            w0, [x1, #0xb]
    // 0x1291c24: ldur            x0, [fp, #-8]
    // 0x1291c28: StoreField: r1->field_23 = r0
    //     0x1291c28: stur            w0, [x1, #0x23]
    // 0x1291c2c: ldur            x0, [fp, #-0x30]
    // 0x1291c30: StoreField: r1->field_43 = r0
    //     0x1291c30: stur            w0, [x1, #0x43]
    // 0x1291c34: r0 = TextButtonThemeData()
    //     0x1291c34: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1291c38: mov             x1, x0
    // 0x1291c3c: ldur            x0, [fp, #-0x28]
    // 0x1291c40: stur            x1, [fp, #-8]
    // 0x1291c44: StoreField: r1->field_7 = r0
    //     0x1291c44: stur            w0, [x1, #7]
    // 0x1291c48: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1291c48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1291c4c: ldr             x0, [x0, #0x1ab0]
    //     0x1291c50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1291c54: cmp             w0, w16
    //     0x1291c58: b.ne            #0x1291c68
    //     0x1291c5c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x1291c60: ldr             x2, [x2, #0x60]
    //     0x1291c64: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1291c68: LoadField: r1 = r0->field_87
    //     0x1291c68: ldur            w1, [x0, #0x87]
    // 0x1291c6c: DecompressPointer r1
    //     0x1291c6c: add             x1, x1, HEAP, lsl #32
    // 0x1291c70: LoadField: r0 = r1->field_7
    //     0x1291c70: ldur            w0, [x1, #7]
    // 0x1291c74: DecompressPointer r0
    //     0x1291c74: add             x0, x0, HEAP, lsl #32
    // 0x1291c78: stur            x0, [fp, #-0x20]
    // 0x1291c7c: r16 = 16.000000
    //     0x1291c7c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1291c80: ldr             x16, [x16, #0x188]
    // 0x1291c84: ldur            lr, [fp, #-0x18]
    // 0x1291c88: stp             lr, x16, [SP]
    // 0x1291c8c: mov             x1, x0
    // 0x1291c90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1291c90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1291c94: ldr             x4, [x4, #0xaa0]
    // 0x1291c98: r0 = copyWith()
    //     0x1291c98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1291c9c: stur            x0, [fp, #-0x18]
    // 0x1291ca0: r0 = Text()
    //     0x1291ca0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1291ca4: mov             x3, x0
    // 0x1291ca8: r0 = "View bag"
    //     0x1291ca8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48890] "View bag"
    //     0x1291cac: ldr             x0, [x0, #0x890]
    // 0x1291cb0: stur            x3, [fp, #-0x28]
    // 0x1291cb4: StoreField: r3->field_b = r0
    //     0x1291cb4: stur            w0, [x3, #0xb]
    // 0x1291cb8: ldur            x0, [fp, #-0x18]
    // 0x1291cbc: StoreField: r3->field_13 = r0
    //     0x1291cbc: stur            w0, [x3, #0x13]
    // 0x1291cc0: ldur            x2, [fp, #-0x10]
    // 0x1291cc4: r1 = Function '<anonymous closure>':.
    //     0x1291cc4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48898] AnonymousClosure: (0x1291f4c), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons (0x1291acc)
    //     0x1291cc8: ldr             x1, [x1, #0x898]
    // 0x1291ccc: r0 = AllocateClosure()
    //     0x1291ccc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1291cd0: stur            x0, [fp, #-0x18]
    // 0x1291cd4: r0 = TextButton()
    //     0x1291cd4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1291cd8: mov             x1, x0
    // 0x1291cdc: ldur            x0, [fp, #-0x18]
    // 0x1291ce0: stur            x1, [fp, #-0x30]
    // 0x1291ce4: StoreField: r1->field_b = r0
    //     0x1291ce4: stur            w0, [x1, #0xb]
    // 0x1291ce8: r0 = false
    //     0x1291ce8: add             x0, NULL, #0x30  ; false
    // 0x1291cec: StoreField: r1->field_27 = r0
    //     0x1291cec: stur            w0, [x1, #0x27]
    // 0x1291cf0: r2 = true
    //     0x1291cf0: add             x2, NULL, #0x20  ; true
    // 0x1291cf4: StoreField: r1->field_2f = r2
    //     0x1291cf4: stur            w2, [x1, #0x2f]
    // 0x1291cf8: ldur            x3, [fp, #-0x28]
    // 0x1291cfc: StoreField: r1->field_37 = r3
    //     0x1291cfc: stur            w3, [x1, #0x37]
    // 0x1291d00: r0 = TextButtonTheme()
    //     0x1291d00: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1291d04: mov             x2, x0
    // 0x1291d08: ldur            x0, [fp, #-8]
    // 0x1291d0c: stur            x2, [fp, #-0x18]
    // 0x1291d10: StoreField: r2->field_f = r0
    //     0x1291d10: stur            w0, [x2, #0xf]
    // 0x1291d14: ldur            x0, [fp, #-0x30]
    // 0x1291d18: StoreField: r2->field_b = r0
    //     0x1291d18: stur            w0, [x2, #0xb]
    // 0x1291d1c: r1 = <FlexParentData>
    //     0x1291d1c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1291d20: ldr             x1, [x1, #0xe00]
    // 0x1291d24: r0 = Flexible()
    //     0x1291d24: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1291d28: mov             x1, x0
    // 0x1291d2c: r0 = 1
    //     0x1291d2c: movz            x0, #0x1
    // 0x1291d30: stur            x1, [fp, #-8]
    // 0x1291d34: StoreField: r1->field_13 = r0
    //     0x1291d34: stur            x0, [x1, #0x13]
    // 0x1291d38: r2 = Instance_FlexFit
    //     0x1291d38: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1291d3c: ldr             x2, [x2, #0xe08]
    // 0x1291d40: StoreField: r1->field_1b = r2
    //     0x1291d40: stur            w2, [x1, #0x1b]
    // 0x1291d44: ldur            x3, [fp, #-0x18]
    // 0x1291d48: StoreField: r1->field_b = r3
    //     0x1291d48: stur            w3, [x1, #0xb]
    // 0x1291d4c: r0 = TextButtonThemeData()
    //     0x1291d4c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1291d50: mov             x2, x0
    // 0x1291d54: ldur            x0, [fp, #-0x38]
    // 0x1291d58: stur            x2, [fp, #-0x18]
    // 0x1291d5c: StoreField: r2->field_7 = r0
    //     0x1291d5c: stur            w0, [x2, #7]
    // 0x1291d60: r16 = 16.000000
    //     0x1291d60: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1291d64: ldr             x16, [x16, #0x188]
    // 0x1291d68: r30 = Instance_Color
    //     0x1291d68: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1291d6c: stp             lr, x16, [SP]
    // 0x1291d70: ldur            x1, [fp, #-0x20]
    // 0x1291d74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1291d74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1291d78: ldr             x4, [x4, #0xaa0]
    // 0x1291d7c: r0 = copyWith()
    //     0x1291d7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1291d80: stur            x0, [fp, #-0x20]
    // 0x1291d84: r0 = Text()
    //     0x1291d84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1291d88: mov             x3, x0
    // 0x1291d8c: r0 = "Checkout"
    //     0x1291d8c: add             x0, PP, #0x48, lsl #12  ; [pp+0x488a0] "Checkout"
    //     0x1291d90: ldr             x0, [x0, #0x8a0]
    // 0x1291d94: stur            x3, [fp, #-0x28]
    // 0x1291d98: StoreField: r3->field_b = r0
    //     0x1291d98: stur            w0, [x3, #0xb]
    // 0x1291d9c: ldur            x0, [fp, #-0x20]
    // 0x1291da0: StoreField: r3->field_13 = r0
    //     0x1291da0: stur            w0, [x3, #0x13]
    // 0x1291da4: ldur            x2, [fp, #-0x10]
    // 0x1291da8: r1 = Function '<anonymous closure>':.
    //     0x1291da8: add             x1, PP, #0x48, lsl #12  ; [pp+0x488a8] AnonymousClosure: (0x1291ee8), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons (0x1291acc)
    //     0x1291dac: ldr             x1, [x1, #0x8a8]
    // 0x1291db0: r0 = AllocateClosure()
    //     0x1291db0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1291db4: stur            x0, [fp, #-0x10]
    // 0x1291db8: r0 = TextButton()
    //     0x1291db8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1291dbc: mov             x1, x0
    // 0x1291dc0: ldur            x0, [fp, #-0x10]
    // 0x1291dc4: stur            x1, [fp, #-0x20]
    // 0x1291dc8: StoreField: r1->field_b = r0
    //     0x1291dc8: stur            w0, [x1, #0xb]
    // 0x1291dcc: r0 = false
    //     0x1291dcc: add             x0, NULL, #0x30  ; false
    // 0x1291dd0: StoreField: r1->field_27 = r0
    //     0x1291dd0: stur            w0, [x1, #0x27]
    // 0x1291dd4: r0 = true
    //     0x1291dd4: add             x0, NULL, #0x20  ; true
    // 0x1291dd8: StoreField: r1->field_2f = r0
    //     0x1291dd8: stur            w0, [x1, #0x2f]
    // 0x1291ddc: ldur            x0, [fp, #-0x28]
    // 0x1291de0: StoreField: r1->field_37 = r0
    //     0x1291de0: stur            w0, [x1, #0x37]
    // 0x1291de4: r0 = TextButtonTheme()
    //     0x1291de4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1291de8: mov             x2, x0
    // 0x1291dec: ldur            x0, [fp, #-0x18]
    // 0x1291df0: stur            x2, [fp, #-0x10]
    // 0x1291df4: StoreField: r2->field_f = r0
    //     0x1291df4: stur            w0, [x2, #0xf]
    // 0x1291df8: ldur            x0, [fp, #-0x20]
    // 0x1291dfc: StoreField: r2->field_b = r0
    //     0x1291dfc: stur            w0, [x2, #0xb]
    // 0x1291e00: r1 = <FlexParentData>
    //     0x1291e00: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1291e04: ldr             x1, [x1, #0xe00]
    // 0x1291e08: r0 = Flexible()
    //     0x1291e08: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1291e0c: mov             x3, x0
    // 0x1291e10: r0 = 1
    //     0x1291e10: movz            x0, #0x1
    // 0x1291e14: stur            x3, [fp, #-0x18]
    // 0x1291e18: StoreField: r3->field_13 = r0
    //     0x1291e18: stur            x0, [x3, #0x13]
    // 0x1291e1c: r0 = Instance_FlexFit
    //     0x1291e1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1291e20: ldr             x0, [x0, #0xe08]
    // 0x1291e24: StoreField: r3->field_1b = r0
    //     0x1291e24: stur            w0, [x3, #0x1b]
    // 0x1291e28: ldur            x0, [fp, #-0x10]
    // 0x1291e2c: StoreField: r3->field_b = r0
    //     0x1291e2c: stur            w0, [x3, #0xb]
    // 0x1291e30: r1 = Null
    //     0x1291e30: mov             x1, NULL
    // 0x1291e34: r2 = 6
    //     0x1291e34: movz            x2, #0x6
    // 0x1291e38: r0 = AllocateArray()
    //     0x1291e38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1291e3c: mov             x2, x0
    // 0x1291e40: ldur            x0, [fp, #-8]
    // 0x1291e44: stur            x2, [fp, #-0x10]
    // 0x1291e48: StoreField: r2->field_f = r0
    //     0x1291e48: stur            w0, [x2, #0xf]
    // 0x1291e4c: r16 = Instance_SizedBox
    //     0x1291e4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x1291e50: ldr             x16, [x16, #0x998]
    // 0x1291e54: StoreField: r2->field_13 = r16
    //     0x1291e54: stur            w16, [x2, #0x13]
    // 0x1291e58: ldur            x0, [fp, #-0x18]
    // 0x1291e5c: ArrayStore: r2[0] = r0  ; List_4
    //     0x1291e5c: stur            w0, [x2, #0x17]
    // 0x1291e60: r1 = <Widget>
    //     0x1291e60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1291e64: r0 = AllocateGrowableArray()
    //     0x1291e64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1291e68: mov             x1, x0
    // 0x1291e6c: ldur            x0, [fp, #-0x10]
    // 0x1291e70: stur            x1, [fp, #-8]
    // 0x1291e74: StoreField: r1->field_f = r0
    //     0x1291e74: stur            w0, [x1, #0xf]
    // 0x1291e78: r0 = 6
    //     0x1291e78: movz            x0, #0x6
    // 0x1291e7c: StoreField: r1->field_b = r0
    //     0x1291e7c: stur            w0, [x1, #0xb]
    // 0x1291e80: r0 = Row()
    //     0x1291e80: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1291e84: r1 = Instance_Axis
    //     0x1291e84: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1291e88: StoreField: r0->field_f = r1
    //     0x1291e88: stur            w1, [x0, #0xf]
    // 0x1291e8c: r1 = Instance_MainAxisAlignment
    //     0x1291e8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1291e90: ldr             x1, [x1, #0xa08]
    // 0x1291e94: StoreField: r0->field_13 = r1
    //     0x1291e94: stur            w1, [x0, #0x13]
    // 0x1291e98: r1 = Instance_MainAxisSize
    //     0x1291e98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1291e9c: ldr             x1, [x1, #0xa10]
    // 0x1291ea0: ArrayStore: r0[0] = r1  ; List_4
    //     0x1291ea0: stur            w1, [x0, #0x17]
    // 0x1291ea4: r1 = Instance_CrossAxisAlignment
    //     0x1291ea4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1291ea8: ldr             x1, [x1, #0xa18]
    // 0x1291eac: StoreField: r0->field_1b = r1
    //     0x1291eac: stur            w1, [x0, #0x1b]
    // 0x1291eb0: r1 = Instance_VerticalDirection
    //     0x1291eb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1291eb4: ldr             x1, [x1, #0xa20]
    // 0x1291eb8: StoreField: r0->field_23 = r1
    //     0x1291eb8: stur            w1, [x0, #0x23]
    // 0x1291ebc: r1 = Instance_Clip
    //     0x1291ebc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1291ec0: ldr             x1, [x1, #0x38]
    // 0x1291ec4: StoreField: r0->field_2b = r1
    //     0x1291ec4: stur            w1, [x0, #0x2b]
    // 0x1291ec8: StoreField: r0->field_2f = rZR
    //     0x1291ec8: stur            xzr, [x0, #0x2f]
    // 0x1291ecc: ldur            x1, [fp, #-8]
    // 0x1291ed0: StoreField: r0->field_b = r1
    //     0x1291ed0: stur            w1, [x0, #0xb]
    // 0x1291ed4: LeaveFrame
    //     0x1291ed4: mov             SP, fp
    //     0x1291ed8: ldp             fp, lr, [SP], #0x10
    // 0x1291edc: ret
    //     0x1291edc: ret             
    // 0x1291ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1291ee0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1291ee4: b               #0x1291ae8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1291ee8, size: 0x64
    // 0x1291ee8: EnterFrame
    //     0x1291ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x1291eec: mov             fp, SP
    // 0x1291ef0: AllocStack(0x8)
    //     0x1291ef0: sub             SP, SP, #8
    // 0x1291ef4: SetupParameters()
    //     0x1291ef4: ldr             x0, [fp, #0x10]
    //     0x1291ef8: ldur            w1, [x0, #0x17]
    //     0x1291efc: add             x1, x1, HEAP, lsl #32
    // 0x1291f00: CheckStackOverflow
    //     0x1291f00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1291f04: cmp             SP, x16
    //     0x1291f08: b.ls            #0x1291f44
    // 0x1291f0c: LoadField: r0 = r1->field_f
    //     0x1291f0c: ldur            w0, [x1, #0xf]
    // 0x1291f10: DecompressPointer r0
    //     0x1291f10: add             x0, x0, HEAP, lsl #32
    // 0x1291f14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1291f14: ldur            w1, [x0, #0x17]
    // 0x1291f18: DecompressPointer r1
    //     0x1291f18: add             x1, x1, HEAP, lsl #32
    // 0x1291f1c: str             x1, [SP]
    // 0x1291f20: r4 = 0
    //     0x1291f20: movz            x4, #0
    // 0x1291f24: ldr             x0, [SP]
    // 0x1291f28: r16 = UnlinkedCall_0x613b5c
    //     0x1291f28: add             x16, PP, #0x48, lsl #12  ; [pp+0x488b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1291f2c: add             x16, x16, #0x8b0
    // 0x1291f30: ldp             x5, lr, [x16]
    // 0x1291f34: blr             lr
    // 0x1291f38: LeaveFrame
    //     0x1291f38: mov             SP, fp
    //     0x1291f3c: ldp             fp, lr, [SP], #0x10
    // 0x1291f40: ret
    //     0x1291f40: ret             
    // 0x1291f44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1291f44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1291f48: b               #0x1291f0c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1291f4c, size: 0x64
    // 0x1291f4c: EnterFrame
    //     0x1291f4c: stp             fp, lr, [SP, #-0x10]!
    //     0x1291f50: mov             fp, SP
    // 0x1291f54: AllocStack(0x8)
    //     0x1291f54: sub             SP, SP, #8
    // 0x1291f58: SetupParameters()
    //     0x1291f58: ldr             x0, [fp, #0x10]
    //     0x1291f5c: ldur            w1, [x0, #0x17]
    //     0x1291f60: add             x1, x1, HEAP, lsl #32
    // 0x1291f64: CheckStackOverflow
    //     0x1291f64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1291f68: cmp             SP, x16
    //     0x1291f6c: b.ls            #0x1291fa8
    // 0x1291f70: LoadField: r0 = r1->field_f
    //     0x1291f70: ldur            w0, [x1, #0xf]
    // 0x1291f74: DecompressPointer r0
    //     0x1291f74: add             x0, x0, HEAP, lsl #32
    // 0x1291f78: LoadField: r1 = r0->field_13
    //     0x1291f78: ldur            w1, [x0, #0x13]
    // 0x1291f7c: DecompressPointer r1
    //     0x1291f7c: add             x1, x1, HEAP, lsl #32
    // 0x1291f80: str             x1, [SP]
    // 0x1291f84: r4 = 0
    //     0x1291f84: movz            x4, #0
    // 0x1291f88: ldr             x0, [SP]
    // 0x1291f8c: r16 = UnlinkedCall_0x613b5c
    //     0x1291f8c: add             x16, PP, #0x48, lsl #12  ; [pp+0x488c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x1291f90: add             x16, x16, #0x8c0
    // 0x1291f94: ldp             x5, lr, [x16]
    // 0x1291f98: blr             lr
    // 0x1291f9c: LeaveFrame
    //     0x1291f9c: mov             SP, fp
    //     0x1291fa0: ldp             fp, lr, [SP], #0x10
    // 0x1291fa4: ret
    //     0x1291fa4: ret             
    // 0x1291fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1291fa8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1291fac: b               #0x1291f70
  }
  _ _buildSingleButton(/* No info */) {
    // ** addr: 0x1291fb0, size: 0x1f0
    // 0x1291fb0: EnterFrame
    //     0x1291fb0: stp             fp, lr, [SP, #-0x10]!
    //     0x1291fb4: mov             fp, SP
    // 0x1291fb8: AllocStack(0x38)
    //     0x1291fb8: sub             SP, SP, #0x38
    // 0x1291fbc: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x1291fbc: stur            x1, [fp, #-8]
    // 0x1291fc0: CheckStackOverflow
    //     0x1291fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1291fc4: cmp             SP, x16
    //     0x1291fc8: b.ls            #0x1292198
    // 0x1291fcc: r1 = 1
    //     0x1291fcc: movz            x1, #0x1
    // 0x1291fd0: r0 = AllocateContext()
    //     0x1291fd0: bl              #0x16f6108  ; AllocateContextStub
    // 0x1291fd4: mov             x1, x0
    // 0x1291fd8: ldur            x0, [fp, #-8]
    // 0x1291fdc: stur            x1, [fp, #-0x10]
    // 0x1291fe0: StoreField: r1->field_f = r0
    //     0x1291fe0: stur            w0, [x1, #0xf]
    // 0x1291fe4: r16 = <EdgeInsets>
    //     0x1291fe4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1291fe8: ldr             x16, [x16, #0xda0]
    // 0x1291fec: r30 = Instance_EdgeInsets
    //     0x1291fec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1291ff0: ldr             lr, [lr, #0x1f0]
    // 0x1291ff4: stp             lr, x16, [SP]
    // 0x1291ff8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1291ff8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1291ffc: r0 = all()
    //     0x1291ffc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1292000: mov             x1, x0
    // 0x1292004: ldur            x0, [fp, #-8]
    // 0x1292008: stur            x1, [fp, #-0x18]
    // 0x129200c: LoadField: r2 = r0->field_23
    //     0x129200c: ldur            w2, [x0, #0x23]
    // 0x1292010: DecompressPointer r2
    //     0x1292010: add             x2, x2, HEAP, lsl #32
    // 0x1292014: r16 = <Color>
    //     0x1292014: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1292018: ldr             x16, [x16, #0xf80]
    // 0x129201c: stp             x2, x16, [SP]
    // 0x1292020: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1292020: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1292024: r0 = all()
    //     0x1292024: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1292028: stur            x0, [fp, #-8]
    // 0x129202c: r16 = <RoundedRectangleBorder>
    //     0x129202c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1292030: ldr             x16, [x16, #0xf78]
    // 0x1292034: r30 = Instance_RoundedRectangleBorder
    //     0x1292034: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x1292038: ldr             lr, [lr, #0x888]
    // 0x129203c: stp             lr, x16, [SP]
    // 0x1292040: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1292040: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1292044: r0 = all()
    //     0x1292044: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1292048: stur            x0, [fp, #-0x20]
    // 0x129204c: r0 = ButtonStyle()
    //     0x129204c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1292050: mov             x1, x0
    // 0x1292054: ldur            x0, [fp, #-8]
    // 0x1292058: stur            x1, [fp, #-0x28]
    // 0x129205c: StoreField: r1->field_b = r0
    //     0x129205c: stur            w0, [x1, #0xb]
    // 0x1292060: ldur            x0, [fp, #-0x18]
    // 0x1292064: StoreField: r1->field_23 = r0
    //     0x1292064: stur            w0, [x1, #0x23]
    // 0x1292068: ldur            x0, [fp, #-0x20]
    // 0x129206c: StoreField: r1->field_43 = r0
    //     0x129206c: stur            w0, [x1, #0x43]
    // 0x1292070: r0 = TextButtonThemeData()
    //     0x1292070: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1292074: mov             x1, x0
    // 0x1292078: ldur            x0, [fp, #-0x28]
    // 0x129207c: stur            x1, [fp, #-8]
    // 0x1292080: StoreField: r1->field_7 = r0
    //     0x1292080: stur            w0, [x1, #7]
    // 0x1292084: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1292084: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1292088: ldr             x0, [x0, #0x1ab0]
    //     0x129208c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1292090: cmp             w0, w16
    //     0x1292094: b.ne            #0x12920a4
    //     0x1292098: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x129209c: ldr             x2, [x2, #0x60]
    //     0x12920a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12920a4: LoadField: r1 = r0->field_87
    //     0x12920a4: ldur            w1, [x0, #0x87]
    // 0x12920a8: DecompressPointer r1
    //     0x12920a8: add             x1, x1, HEAP, lsl #32
    // 0x12920ac: LoadField: r0 = r1->field_7
    //     0x12920ac: ldur            w0, [x1, #7]
    // 0x12920b0: DecompressPointer r0
    //     0x12920b0: add             x0, x0, HEAP, lsl #32
    // 0x12920b4: r16 = 16.000000
    //     0x12920b4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x12920b8: ldr             x16, [x16, #0x188]
    // 0x12920bc: r30 = Instance_Color
    //     0x12920bc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12920c0: stp             lr, x16, [SP]
    // 0x12920c4: mov             x1, x0
    // 0x12920c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12920c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12920cc: ldr             x4, [x4, #0xaa0]
    // 0x12920d0: r0 = copyWith()
    //     0x12920d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12920d4: stur            x0, [fp, #-0x18]
    // 0x12920d8: r0 = Text()
    //     0x12920d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12920dc: mov             x3, x0
    // 0x12920e0: r0 = "View Bag"
    //     0x12920e0: add             x0, PP, #0x48, lsl #12  ; [pp+0x488d0] "View Bag"
    //     0x12920e4: ldr             x0, [x0, #0x8d0]
    // 0x12920e8: stur            x3, [fp, #-0x20]
    // 0x12920ec: StoreField: r3->field_b = r0
    //     0x12920ec: stur            w0, [x3, #0xb]
    // 0x12920f0: ldur            x0, [fp, #-0x18]
    // 0x12920f4: StoreField: r3->field_13 = r0
    //     0x12920f4: stur            w0, [x3, #0x13]
    // 0x12920f8: ldur            x2, [fp, #-0x10]
    // 0x12920fc: r1 = Function '<anonymous closure>':.
    //     0x12920fc: add             x1, PP, #0x48, lsl #12  ; [pp+0x488d8] AnonymousClosure: (0x12921a0), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildSingleButton (0x1291fb0)
    //     0x1292100: ldr             x1, [x1, #0x8d8]
    // 0x1292104: r0 = AllocateClosure()
    //     0x1292104: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1292108: stur            x0, [fp, #-0x10]
    // 0x129210c: r0 = TextButton()
    //     0x129210c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1292110: mov             x1, x0
    // 0x1292114: ldur            x0, [fp, #-0x10]
    // 0x1292118: stur            x1, [fp, #-0x18]
    // 0x129211c: StoreField: r1->field_b = r0
    //     0x129211c: stur            w0, [x1, #0xb]
    // 0x1292120: r0 = false
    //     0x1292120: add             x0, NULL, #0x30  ; false
    // 0x1292124: StoreField: r1->field_27 = r0
    //     0x1292124: stur            w0, [x1, #0x27]
    // 0x1292128: r0 = true
    //     0x1292128: add             x0, NULL, #0x20  ; true
    // 0x129212c: StoreField: r1->field_2f = r0
    //     0x129212c: stur            w0, [x1, #0x2f]
    // 0x1292130: ldur            x0, [fp, #-0x20]
    // 0x1292134: StoreField: r1->field_37 = r0
    //     0x1292134: stur            w0, [x1, #0x37]
    // 0x1292138: r0 = TextButtonTheme()
    //     0x1292138: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x129213c: mov             x1, x0
    // 0x1292140: ldur            x0, [fp, #-8]
    // 0x1292144: stur            x1, [fp, #-0x10]
    // 0x1292148: StoreField: r1->field_f = r0
    //     0x1292148: stur            w0, [x1, #0xf]
    // 0x129214c: ldur            x0, [fp, #-0x18]
    // 0x1292150: StoreField: r1->field_b = r0
    //     0x1292150: stur            w0, [x1, #0xb]
    // 0x1292154: r0 = SizedBox()
    //     0x1292154: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1292158: mov             x1, x0
    // 0x129215c: r0 = inf
    //     0x129215c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1292160: ldr             x0, [x0, #0x9f8]
    // 0x1292164: stur            x1, [fp, #-8]
    // 0x1292168: StoreField: r1->field_f = r0
    //     0x1292168: stur            w0, [x1, #0xf]
    // 0x129216c: ldur            x0, [fp, #-0x10]
    // 0x1292170: StoreField: r1->field_b = r0
    //     0x1292170: stur            w0, [x1, #0xb]
    // 0x1292174: r0 = Padding()
    //     0x1292174: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1292178: r1 = Instance_EdgeInsets
    //     0x1292178: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x129217c: ldr             x1, [x1, #0xb0]
    // 0x1292180: StoreField: r0->field_f = r1
    //     0x1292180: stur            w1, [x0, #0xf]
    // 0x1292184: ldur            x1, [fp, #-8]
    // 0x1292188: StoreField: r0->field_b = r1
    //     0x1292188: stur            w1, [x0, #0xb]
    // 0x129218c: LeaveFrame
    //     0x129218c: mov             SP, fp
    //     0x1292190: ldp             fp, lr, [SP], #0x10
    // 0x1292194: ret
    //     0x1292194: ret             
    // 0x1292198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1292198: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129219c: b               #0x1291fcc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x12921a0, size: 0x64
    // 0x12921a0: EnterFrame
    //     0x12921a0: stp             fp, lr, [SP, #-0x10]!
    //     0x12921a4: mov             fp, SP
    // 0x12921a8: AllocStack(0x8)
    //     0x12921a8: sub             SP, SP, #8
    // 0x12921ac: SetupParameters()
    //     0x12921ac: ldr             x0, [fp, #0x10]
    //     0x12921b0: ldur            w1, [x0, #0x17]
    //     0x12921b4: add             x1, x1, HEAP, lsl #32
    // 0x12921b8: CheckStackOverflow
    //     0x12921b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12921bc: cmp             SP, x16
    //     0x12921c0: b.ls            #0x12921fc
    // 0x12921c4: LoadField: r0 = r1->field_f
    //     0x12921c4: ldur            w0, [x1, #0xf]
    // 0x12921c8: DecompressPointer r0
    //     0x12921c8: add             x0, x0, HEAP, lsl #32
    // 0x12921cc: LoadField: r1 = r0->field_13
    //     0x12921cc: ldur            w1, [x0, #0x13]
    // 0x12921d0: DecompressPointer r1
    //     0x12921d0: add             x1, x1, HEAP, lsl #32
    // 0x12921d4: str             x1, [SP]
    // 0x12921d8: r4 = 0
    //     0x12921d8: movz            x4, #0
    // 0x12921dc: ldr             x0, [SP]
    // 0x12921e0: r16 = UnlinkedCall_0x613b5c
    //     0x12921e0: add             x16, PP, #0x48, lsl #12  ; [pp+0x488e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x12921e4: add             x16, x16, #0x8e0
    // 0x12921e8: ldp             x5, lr, [x16]
    // 0x12921ec: blr             lr
    // 0x12921f0: LeaveFrame
    //     0x12921f0: mov             SP, fp
    //     0x12921f4: ldp             fp, lr, [SP], #0x10
    // 0x12921f8: ret
    //     0x12921f8: ret             
    // 0x12921fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12921fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1292200: b               #0x12921c4
  }
  _ _buildProductList(/* No info */) {
    // ** addr: 0x1292204, size: 0xd0
    // 0x1292204: EnterFrame
    //     0x1292204: stp             fp, lr, [SP, #-0x10]!
    //     0x1292208: mov             fp, SP
    // 0x129220c: AllocStack(0x30)
    //     0x129220c: sub             SP, SP, #0x30
    // 0x1292210: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1292210: stur            x1, [fp, #-8]
    //     0x1292214: stur            x2, [fp, #-0x10]
    // 0x1292218: CheckStackOverflow
    //     0x1292218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x129221c: cmp             SP, x16
    //     0x1292220: b.ls            #0x12922cc
    // 0x1292224: r1 = 2
    //     0x1292224: movz            x1, #0x2
    // 0x1292228: r0 = AllocateContext()
    //     0x1292228: bl              #0x16f6108  ; AllocateContextStub
    // 0x129222c: mov             x1, x0
    // 0x1292230: ldur            x0, [fp, #-8]
    // 0x1292234: StoreField: r1->field_f = r0
    //     0x1292234: stur            w0, [x1, #0xf]
    // 0x1292238: ldur            x0, [fp, #-0x10]
    // 0x129223c: StoreField: r1->field_13 = r0
    //     0x129223c: stur            w0, [x1, #0x13]
    // 0x1292240: LoadField: r3 = r0->field_b
    //     0x1292240: ldur            w3, [x0, #0xb]
    // 0x1292244: mov             x2, x1
    // 0x1292248: stur            x3, [fp, #-8]
    // 0x129224c: r1 = Function '<anonymous closure>':.
    //     0x129224c: add             x1, PP, #0x48, lsl #12  ; [pp+0x488f0] AnonymousClosure: (0x12922d4), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductList (0x1292204)
    //     0x1292250: ldr             x1, [x1, #0x8f0]
    // 0x1292254: r0 = AllocateClosure()
    //     0x1292254: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1292258: stur            x0, [fp, #-0x10]
    // 0x129225c: r0 = ListView()
    //     0x129225c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1292260: stur            x0, [fp, #-0x18]
    // 0x1292264: r16 = true
    //     0x1292264: add             x16, NULL, #0x20  ; true
    // 0x1292268: r30 = Instance_BouncingScrollPhysics
    //     0x1292268: add             lr, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x129226c: ldr             lr, [lr, #0x890]
    // 0x1292270: stp             lr, x16, [SP, #8]
    // 0x1292274: r16 = 200.000000
    //     0x1292274: add             x16, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0x1292278: ldr             x16, [x16, #0x570]
    // 0x129227c: str             x16, [SP]
    // 0x1292280: mov             x1, x0
    // 0x1292284: ldur            x2, [fp, #-0x10]
    // 0x1292288: ldur            x3, [fp, #-8]
    // 0x129228c: r4 = const [0, 0x6, 0x3, 0x3, cacheExtent, 0x5, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x129228c: add             x4, PP, #0x48, lsl #12  ; [pp+0x488f8] List(11) [0, 0x6, 0x3, 0x3, "cacheExtent", 0x5, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x1292290: ldr             x4, [x4, #0x8f8]
    // 0x1292294: r0 = ListView.builder()
    //     0x1292294: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1292298: r1 = <FlexParentData>
    //     0x1292298: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x129229c: ldr             x1, [x1, #0xe00]
    // 0x12922a0: r0 = Flexible()
    //     0x12922a0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x12922a4: r1 = 1
    //     0x12922a4: movz            x1, #0x1
    // 0x12922a8: StoreField: r0->field_13 = r1
    //     0x12922a8: stur            x1, [x0, #0x13]
    // 0x12922ac: r1 = Instance_FlexFit
    //     0x12922ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x12922b0: ldr             x1, [x1, #0xe20]
    // 0x12922b4: StoreField: r0->field_1b = r1
    //     0x12922b4: stur            w1, [x0, #0x1b]
    // 0x12922b8: ldur            x1, [fp, #-0x18]
    // 0x12922bc: StoreField: r0->field_b = r1
    //     0x12922bc: stur            w1, [x0, #0xb]
    // 0x12922c0: LeaveFrame
    //     0x12922c0: mov             SP, fp
    //     0x12922c4: ldp             fp, lr, [SP], #0x10
    // 0x12922c8: ret
    //     0x12922c8: ret             
    // 0x12922cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12922cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12922d0: b               #0x1292224
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x12922d4, size: 0x94
    // 0x12922d4: EnterFrame
    //     0x12922d4: stp             fp, lr, [SP, #-0x10]!
    //     0x12922d8: mov             fp, SP
    // 0x12922dc: ldr             x0, [fp, #0x20]
    // 0x12922e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x12922e0: ldur            w1, [x0, #0x17]
    // 0x12922e4: DecompressPointer r1
    //     0x12922e4: add             x1, x1, HEAP, lsl #32
    // 0x12922e8: CheckStackOverflow
    //     0x12922e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12922ec: cmp             SP, x16
    //     0x12922f0: b.ls            #0x129235c
    // 0x12922f4: LoadField: r2 = r1->field_f
    //     0x12922f4: ldur            w2, [x1, #0xf]
    // 0x12922f8: DecompressPointer r2
    //     0x12922f8: add             x2, x2, HEAP, lsl #32
    // 0x12922fc: LoadField: r3 = r1->field_13
    //     0x12922fc: ldur            w3, [x1, #0x13]
    // 0x1292300: DecompressPointer r3
    //     0x1292300: add             x3, x3, HEAP, lsl #32
    // 0x1292304: LoadField: r0 = r3->field_b
    //     0x1292304: ldur            w0, [x3, #0xb]
    // 0x1292308: ldr             x1, [fp, #0x10]
    // 0x129230c: r4 = LoadInt32Instr(r1)
    //     0x129230c: sbfx            x4, x1, #1, #0x1f
    //     0x1292310: tbz             w1, #0, #0x1292318
    //     0x1292314: ldur            x4, [x1, #7]
    // 0x1292318: r1 = LoadInt32Instr(r0)
    //     0x1292318: sbfx            x1, x0, #1, #0x1f
    // 0x129231c: mov             x0, x1
    // 0x1292320: mov             x1, x4
    // 0x1292324: cmp             x1, x0
    // 0x1292328: b.hs            #0x1292364
    // 0x129232c: LoadField: r0 = r3->field_f
    //     0x129232c: ldur            w0, [x3, #0xf]
    // 0x1292330: DecompressPointer r0
    //     0x1292330: add             x0, x0, HEAP, lsl #32
    // 0x1292334: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x1292334: add             x16, x0, x4, lsl #2
    //     0x1292338: ldur            w1, [x16, #0xf]
    // 0x129233c: DecompressPointer r1
    //     0x129233c: add             x1, x1, HEAP, lsl #32
    // 0x1292340: mov             x16, x1
    // 0x1292344: mov             x1, x2
    // 0x1292348: mov             x2, x16
    // 0x129234c: r0 = _buildProductItem()
    //     0x129234c: bl              #0x8fb1d0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductItem
    // 0x1292350: LeaveFrame
    //     0x1292350: mov             SP, fp
    //     0x1292354: ldp             fp, lr, [SP], #0x10
    // 0x1292358: ret
    //     0x1292358: ret             
    // 0x129235c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x129235c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1292360: b               #0x12922f4
    // 0x1292364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1292364: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildFreeGiftSection(/* No info */) {
    // ** addr: 0x1292368, size: 0x784
    // 0x1292368: EnterFrame
    //     0x1292368: stp             fp, lr, [SP, #-0x10]!
    //     0x129236c: mov             fp, SP
    // 0x1292370: AllocStack(0x70)
    //     0x1292370: sub             SP, SP, #0x70
    // 0x1292374: SetupParameters(BagBottomSheet this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x1292374: mov             x4, x1
    //     0x1292378: mov             x0, x5
    //     0x129237c: stur            x1, [fp, #-8]
    //     0x1292380: stur            x2, [fp, #-0x10]
    //     0x1292384: stur            x3, [fp, #-0x18]
    //     0x1292388: stur            x5, [fp, #-0x20]
    // 0x129238c: CheckStackOverflow
    //     0x129238c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1292390: cmp             SP, x16
    //     0x1292394: b.ls            #0x1292ae4
    // 0x1292398: tbnz            w2, #4, #0x1292438
    // 0x129239c: r1 = Instance_Color
    //     0x129239c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12923a0: d0 = 0.070000
    //     0x12923a0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x12923a4: ldr             d0, [x17, #0x5f8]
    // 0x12923a8: r0 = withOpacity()
    //     0x12923a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12923ac: r16 = 1.000000
    //     0x12923ac: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x12923b0: str             x16, [SP]
    // 0x12923b4: mov             x2, x0
    // 0x12923b8: r1 = Null
    //     0x12923b8: mov             x1, NULL
    // 0x12923bc: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x12923bc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x12923c0: ldr             x4, [x4, #0x108]
    // 0x12923c4: r0 = Border.all()
    //     0x12923c4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x12923c8: stur            x0, [fp, #-0x28]
    // 0x12923cc: r0 = Radius()
    //     0x12923cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x12923d0: d1 = 12.000000
    //     0x12923d0: fmov            d1, #12.00000000
    // 0x12923d4: stur            x0, [fp, #-0x30]
    // 0x12923d8: StoreField: r0->field_7 = d1
    //     0x12923d8: stur            d1, [x0, #7]
    // 0x12923dc: StoreField: r0->field_f = d1
    //     0x12923dc: stur            d1, [x0, #0xf]
    // 0x12923e0: r0 = BorderRadius()
    //     0x12923e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x12923e4: mov             x1, x0
    // 0x12923e8: ldur            x0, [fp, #-0x30]
    // 0x12923ec: stur            x1, [fp, #-0x38]
    // 0x12923f0: StoreField: r1->field_7 = r0
    //     0x12923f0: stur            w0, [x1, #7]
    // 0x12923f4: StoreField: r1->field_b = r0
    //     0x12923f4: stur            w0, [x1, #0xb]
    // 0x12923f8: StoreField: r1->field_f = r0
    //     0x12923f8: stur            w0, [x1, #0xf]
    // 0x12923fc: StoreField: r1->field_13 = r0
    //     0x12923fc: stur            w0, [x1, #0x13]
    // 0x1292400: r0 = BoxDecoration()
    //     0x1292400: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1292404: mov             x1, x0
    // 0x1292408: ldur            x0, [fp, #-0x28]
    // 0x129240c: StoreField: r1->field_f = r0
    //     0x129240c: stur            w0, [x1, #0xf]
    // 0x1292410: ldur            x0, [fp, #-0x38]
    // 0x1292414: StoreField: r1->field_13 = r0
    //     0x1292414: stur            w0, [x1, #0x13]
    // 0x1292418: r0 = Instance_LinearGradient
    //     0x1292418: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0x129241c: ldr             x0, [x0, #0x660]
    // 0x1292420: StoreField: r1->field_1b = r0
    //     0x1292420: stur            w0, [x1, #0x1b]
    // 0x1292424: r0 = Instance_BoxShape
    //     0x1292424: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1292428: ldr             x0, [x0, #0x80]
    // 0x129242c: StoreField: r1->field_23 = r0
    //     0x129242c: stur            w0, [x1, #0x23]
    // 0x1292430: mov             x2, x1
    // 0x1292434: b               #0x12924d0
    // 0x1292438: r0 = Instance_BoxShape
    //     0x1292438: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x129243c: ldr             x0, [x0, #0x80]
    // 0x1292440: d1 = 12.000000
    //     0x1292440: fmov            d1, #12.00000000
    // 0x1292444: r1 = Instance_Color
    //     0x1292444: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1292448: d0 = 0.070000
    //     0x1292448: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x129244c: ldr             d0, [x17, #0x5f8]
    // 0x1292450: r0 = withOpacity()
    //     0x1292450: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1292454: r16 = 1.000000
    //     0x1292454: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1292458: str             x16, [SP]
    // 0x129245c: mov             x2, x0
    // 0x1292460: r1 = Null
    //     0x1292460: mov             x1, NULL
    // 0x1292464: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x1292464: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x1292468: ldr             x4, [x4, #0x108]
    // 0x129246c: r0 = Border.all()
    //     0x129246c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1292470: stur            x0, [fp, #-0x28]
    // 0x1292474: r0 = Radius()
    //     0x1292474: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1292478: d0 = 12.000000
    //     0x1292478: fmov            d0, #12.00000000
    // 0x129247c: stur            x0, [fp, #-0x30]
    // 0x1292480: StoreField: r0->field_7 = d0
    //     0x1292480: stur            d0, [x0, #7]
    // 0x1292484: StoreField: r0->field_f = d0
    //     0x1292484: stur            d0, [x0, #0xf]
    // 0x1292488: r0 = BorderRadius()
    //     0x1292488: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x129248c: mov             x1, x0
    // 0x1292490: ldur            x0, [fp, #-0x30]
    // 0x1292494: stur            x1, [fp, #-0x38]
    // 0x1292498: StoreField: r1->field_7 = r0
    //     0x1292498: stur            w0, [x1, #7]
    // 0x129249c: StoreField: r1->field_b = r0
    //     0x129249c: stur            w0, [x1, #0xb]
    // 0x12924a0: StoreField: r1->field_f = r0
    //     0x12924a0: stur            w0, [x1, #0xf]
    // 0x12924a4: StoreField: r1->field_13 = r0
    //     0x12924a4: stur            w0, [x1, #0x13]
    // 0x12924a8: r0 = BoxDecoration()
    //     0x12924a8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x12924ac: mov             x1, x0
    // 0x12924b0: ldur            x0, [fp, #-0x28]
    // 0x12924b4: StoreField: r1->field_f = r0
    //     0x12924b4: stur            w0, [x1, #0xf]
    // 0x12924b8: ldur            x0, [fp, #-0x38]
    // 0x12924bc: StoreField: r1->field_13 = r0
    //     0x12924bc: stur            w0, [x1, #0x13]
    // 0x12924c0: r0 = Instance_BoxShape
    //     0x12924c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x12924c4: ldr             x0, [x0, #0x80]
    // 0x12924c8: StoreField: r1->field_23 = r0
    //     0x12924c8: stur            w0, [x1, #0x23]
    // 0x12924cc: mov             x2, x1
    // 0x12924d0: ldur            x1, [fp, #-0x10]
    // 0x12924d4: stur            x2, [fp, #-0x28]
    // 0x12924d8: tbnz            w1, #4, #0x12924e8
    // 0x12924dc: r3 = Instance_Color
    //     0x12924dc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x12924e0: ldr             x3, [x3, #0x858]
    // 0x12924e4: b               #0x12924f4
    // 0x12924e8: ldur            x1, [fp, #-8]
    // 0x12924ec: LoadField: r3 = r1->field_27
    //     0x12924ec: ldur            w3, [x1, #0x27]
    // 0x12924f0: DecompressPointer r3
    //     0x12924f0: add             x3, x3, HEAP, lsl #32
    // 0x12924f4: ldur            x1, [fp, #-0x18]
    // 0x12924f8: stur            x3, [fp, #-8]
    // 0x12924fc: r0 = BoxDecoration()
    //     0x12924fc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1292500: mov             x2, x0
    // 0x1292504: ldur            x0, [fp, #-8]
    // 0x1292508: stur            x2, [fp, #-0x10]
    // 0x129250c: StoreField: r2->field_7 = r0
    //     0x129250c: stur            w0, [x2, #7]
    // 0x1292510: r0 = Instance_BorderRadius
    //     0x1292510: add             x0, PP, #0x48, lsl #12  ; [pp+0x48990] Obj!BorderRadius@d5a281
    //     0x1292514: ldr             x0, [x0, #0x990]
    // 0x1292518: StoreField: r2->field_13 = r0
    //     0x1292518: stur            w0, [x2, #0x13]
    // 0x129251c: r0 = Instance_BoxShape
    //     0x129251c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1292520: ldr             x0, [x0, #0x80]
    // 0x1292524: StoreField: r2->field_23 = r0
    //     0x1292524: stur            w0, [x2, #0x23]
    // 0x1292528: ldur            x1, [fp, #-0x20]
    // 0x129252c: r0 = of()
    //     0x129252c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1292530: LoadField: r1 = r0->field_87
    //     0x1292530: ldur            w1, [x0, #0x87]
    // 0x1292534: DecompressPointer r1
    //     0x1292534: add             x1, x1, HEAP, lsl #32
    // 0x1292538: LoadField: r0 = r1->field_7
    //     0x1292538: ldur            w0, [x1, #7]
    // 0x129253c: DecompressPointer r0
    //     0x129253c: add             x0, x0, HEAP, lsl #32
    // 0x1292540: r16 = 12.000000
    //     0x1292540: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1292544: ldr             x16, [x16, #0x9e8]
    // 0x1292548: r30 = Instance_Color
    //     0x1292548: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x129254c: stp             lr, x16, [SP]
    // 0x1292550: mov             x1, x0
    // 0x1292554: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1292554: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1292558: ldr             x4, [x4, #0xaa0]
    // 0x129255c: r0 = copyWith()
    //     0x129255c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1292560: stur            x0, [fp, #-8]
    // 0x1292564: r0 = Text()
    //     0x1292564: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292568: mov             x1, x0
    // 0x129256c: r0 = "Free"
    //     0x129256c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x1292570: ldr             x0, [x0, #0x668]
    // 0x1292574: stur            x1, [fp, #-0x30]
    // 0x1292578: StoreField: r1->field_b = r0
    //     0x1292578: stur            w0, [x1, #0xb]
    // 0x129257c: ldur            x2, [fp, #-8]
    // 0x1292580: StoreField: r1->field_13 = r2
    //     0x1292580: stur            w2, [x1, #0x13]
    // 0x1292584: r0 = Center()
    //     0x1292584: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1292588: mov             x1, x0
    // 0x129258c: r0 = Instance_Alignment
    //     0x129258c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1292590: ldr             x0, [x0, #0xb10]
    // 0x1292594: stur            x1, [fp, #-8]
    // 0x1292598: StoreField: r1->field_f = r0
    //     0x1292598: stur            w0, [x1, #0xf]
    // 0x129259c: ldur            x0, [fp, #-0x30]
    // 0x12925a0: StoreField: r1->field_b = r0
    //     0x12925a0: stur            w0, [x1, #0xb]
    // 0x12925a4: r0 = RotatedBox()
    //     0x12925a4: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0x12925a8: mov             x1, x0
    // 0x12925ac: r0 = -1
    //     0x12925ac: movn            x0, #0
    // 0x12925b0: stur            x1, [fp, #-0x30]
    // 0x12925b4: StoreField: r1->field_f = r0
    //     0x12925b4: stur            x0, [x1, #0xf]
    // 0x12925b8: ldur            x0, [fp, #-8]
    // 0x12925bc: StoreField: r1->field_b = r0
    //     0x12925bc: stur            w0, [x1, #0xb]
    // 0x12925c0: r0 = Container()
    //     0x12925c0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12925c4: stur            x0, [fp, #-8]
    // 0x12925c8: ldur            x16, [fp, #-0x10]
    // 0x12925cc: r30 = 24.000000
    //     0x12925cc: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x12925d0: ldr             lr, [lr, #0xba8]
    // 0x12925d4: stp             lr, x16, [SP, #0x10]
    // 0x12925d8: r16 = 56.000000
    //     0x12925d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x12925dc: ldr             x16, [x16, #0xb78]
    // 0x12925e0: ldur            lr, [fp, #-0x30]
    // 0x12925e4: stp             lr, x16, [SP]
    // 0x12925e8: mov             x1, x0
    // 0x12925ec: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0x12925ec: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0x12925f0: ldr             x4, [x4, #0x5d0]
    // 0x12925f4: r0 = Container()
    //     0x12925f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12925f8: ldur            x0, [fp, #-0x18]
    // 0x12925fc: cmp             w0, NULL
    // 0x1292600: b.ne            #0x129260c
    // 0x1292604: r1 = Null
    //     0x1292604: mov             x1, NULL
    // 0x1292608: b               #0x1292614
    // 0x129260c: LoadField: r1 = r0->field_7
    //     0x129260c: ldur            w1, [x0, #7]
    // 0x1292610: DecompressPointer r1
    //     0x1292610: add             x1, x1, HEAP, lsl #32
    // 0x1292614: cmp             w1, NULL
    // 0x1292618: b.ne            #0x1292624
    // 0x129261c: r3 = ""
    //     0x129261c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1292620: b               #0x1292628
    // 0x1292624: mov             x3, x1
    // 0x1292628: stur            x3, [fp, #-0x10]
    // 0x129262c: r1 = Function '<anonymous closure>':.
    //     0x129262c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48998] AnonymousClosure: (0x1292aec), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFreeGiftSection (0x1292368)
    //     0x1292630: ldr             x1, [x1, #0x998]
    // 0x1292634: r2 = Null
    //     0x1292634: mov             x2, NULL
    // 0x1292638: r0 = AllocateClosure()
    //     0x1292638: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x129263c: stur            x0, [fp, #-0x30]
    // 0x1292640: r0 = CachedNetworkImage()
    //     0x1292640: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1292644: stur            x0, [fp, #-0x38]
    // 0x1292648: r16 = 56.000000
    //     0x1292648: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x129264c: ldr             x16, [x16, #0xb78]
    // 0x1292650: r30 = 56.000000
    //     0x1292650: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1292654: ldr             lr, [lr, #0xb78]
    // 0x1292658: stp             lr, x16, [SP, #0x20]
    // 0x129265c: r16 = Instance_BoxFit
    //     0x129265c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x1292660: ldr             x16, [x16, #0x118]
    // 0x1292664: r30 = 224
    //     0x1292664: movz            lr, #0xe0
    // 0x1292668: stp             lr, x16, [SP, #0x10]
    // 0x129266c: r16 = 224
    //     0x129266c: movz            x16, #0xe0
    // 0x1292670: ldur            lr, [fp, #-0x30]
    // 0x1292674: stp             lr, x16, [SP]
    // 0x1292678: mov             x1, x0
    // 0x129267c: ldur            x2, [fp, #-0x10]
    // 0x1292680: r4 = const [0, 0x8, 0x6, 0x2, fit, 0x4, height, 0x3, memCacheHeight, 0x6, memCacheWidth, 0x5, placeholder, 0x7, width, 0x2, null]
    //     0x1292680: add             x4, PP, #0x48, lsl #12  ; [pp+0x489a0] List(17) [0, 0x8, 0x6, 0x2, "fit", 0x4, "height", 0x3, "memCacheHeight", 0x6, "memCacheWidth", 0x5, "placeholder", 0x7, "width", 0x2, Null]
    //     0x1292684: ldr             x4, [x4, #0x9a0]
    // 0x1292688: r0 = CachedNetworkImage()
    //     0x1292688: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x129268c: ldur            x0, [fp, #-0x18]
    // 0x1292690: cmp             w0, NULL
    // 0x1292694: b.ne            #0x12926a0
    // 0x1292698: r1 = Null
    //     0x1292698: mov             x1, NULL
    // 0x129269c: b               #0x12926a8
    // 0x12926a0: LoadField: r1 = r0->field_b
    //     0x12926a0: ldur            w1, [x0, #0xb]
    // 0x12926a4: DecompressPointer r1
    //     0x12926a4: add             x1, x1, HEAP, lsl #32
    // 0x12926a8: cmp             w1, NULL
    // 0x12926ac: b.ne            #0x12926b8
    // 0x12926b0: r2 = ""
    //     0x12926b0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12926b4: b               #0x12926bc
    // 0x12926b8: mov             x2, x1
    // 0x12926bc: ldur            x1, [fp, #-0x20]
    // 0x12926c0: stur            x2, [fp, #-0x10]
    // 0x12926c4: r0 = of()
    //     0x12926c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12926c8: LoadField: r1 = r0->field_87
    //     0x12926c8: ldur            w1, [x0, #0x87]
    // 0x12926cc: DecompressPointer r1
    //     0x12926cc: add             x1, x1, HEAP, lsl #32
    // 0x12926d0: LoadField: r0 = r1->field_7
    //     0x12926d0: ldur            w0, [x1, #7]
    // 0x12926d4: DecompressPointer r0
    //     0x12926d4: add             x0, x0, HEAP, lsl #32
    // 0x12926d8: r16 = 12.000000
    //     0x12926d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x12926dc: ldr             x16, [x16, #0x9e8]
    // 0x12926e0: r30 = Instance_Color
    //     0x12926e0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12926e4: stp             lr, x16, [SP]
    // 0x12926e8: mov             x1, x0
    // 0x12926ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12926ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12926f0: ldr             x4, [x4, #0xaa0]
    // 0x12926f4: r0 = copyWith()
    //     0x12926f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12926f8: stur            x0, [fp, #-0x30]
    // 0x12926fc: r0 = Text()
    //     0x12926fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292700: mov             x1, x0
    // 0x1292704: ldur            x0, [fp, #-0x10]
    // 0x1292708: stur            x1, [fp, #-0x40]
    // 0x129270c: StoreField: r1->field_b = r0
    //     0x129270c: stur            w0, [x1, #0xb]
    // 0x1292710: ldur            x0, [fp, #-0x30]
    // 0x1292714: StoreField: r1->field_13 = r0
    //     0x1292714: stur            w0, [x1, #0x13]
    // 0x1292718: r0 = Instance_TextOverflow
    //     0x1292718: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x129271c: ldr             x0, [x0, #0xe10]
    // 0x1292720: StoreField: r1->field_2b = r0
    //     0x1292720: stur            w0, [x1, #0x2b]
    // 0x1292724: r0 = 2
    //     0x1292724: movz            x0, #0x2
    // 0x1292728: StoreField: r1->field_37 = r0
    //     0x1292728: stur            w0, [x1, #0x37]
    // 0x129272c: r0 = SizedBox()
    //     0x129272c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1292730: mov             x2, x0
    // 0x1292734: r0 = 150.000000
    //     0x1292734: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0x1292738: ldr             x0, [x0, #0x690]
    // 0x129273c: stur            x2, [fp, #-0x10]
    // 0x1292740: StoreField: r2->field_f = r0
    //     0x1292740: stur            w0, [x2, #0xf]
    // 0x1292744: ldur            x0, [fp, #-0x40]
    // 0x1292748: StoreField: r2->field_b = r0
    //     0x1292748: stur            w0, [x2, #0xb]
    // 0x129274c: ldur            x1, [fp, #-0x20]
    // 0x1292750: r0 = of()
    //     0x1292750: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1292754: LoadField: r1 = r0->field_87
    //     0x1292754: ldur            w1, [x0, #0x87]
    // 0x1292758: DecompressPointer r1
    //     0x1292758: add             x1, x1, HEAP, lsl #32
    // 0x129275c: LoadField: r0 = r1->field_2b
    //     0x129275c: ldur            w0, [x1, #0x2b]
    // 0x1292760: DecompressPointer r0
    //     0x1292760: add             x0, x0, HEAP, lsl #32
    // 0x1292764: r16 = 12.000000
    //     0x1292764: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1292768: ldr             x16, [x16, #0x9e8]
    // 0x129276c: r30 = Instance_Color
    //     0x129276c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x1292770: ldr             lr, [lr, #0x858]
    // 0x1292774: stp             lr, x16, [SP]
    // 0x1292778: mov             x1, x0
    // 0x129277c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x129277c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1292780: ldr             x4, [x4, #0xaa0]
    // 0x1292784: r0 = copyWith()
    //     0x1292784: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1292788: stur            x0, [fp, #-0x30]
    // 0x129278c: r0 = Text()
    //     0x129278c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292790: mov             x2, x0
    // 0x1292794: r0 = "Free"
    //     0x1292794: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0x1292798: ldr             x0, [x0, #0x668]
    // 0x129279c: stur            x2, [fp, #-0x40]
    // 0x12927a0: StoreField: r2->field_b = r0
    //     0x12927a0: stur            w0, [x2, #0xb]
    // 0x12927a4: ldur            x0, [fp, #-0x30]
    // 0x12927a8: StoreField: r2->field_13 = r0
    //     0x12927a8: stur            w0, [x2, #0x13]
    // 0x12927ac: ldur            x0, [fp, #-0x18]
    // 0x12927b0: cmp             w0, NULL
    // 0x12927b4: b.ne            #0x12927c0
    // 0x12927b8: r0 = Null
    //     0x12927b8: mov             x0, NULL
    // 0x12927bc: b               #0x12927cc
    // 0x12927c0: LoadField: r1 = r0->field_13
    //     0x12927c0: ldur            w1, [x0, #0x13]
    // 0x12927c4: DecompressPointer r1
    //     0x12927c4: add             x1, x1, HEAP, lsl #32
    // 0x12927c8: mov             x0, x1
    // 0x12927cc: cmp             w0, NULL
    // 0x12927d0: b.ne            #0x12927dc
    // 0x12927d4: r5 = ""
    //     0x12927d4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12927d8: b               #0x12927e0
    // 0x12927dc: mov             x5, x0
    // 0x12927e0: ldur            x4, [fp, #-8]
    // 0x12927e4: ldur            x3, [fp, #-0x38]
    // 0x12927e8: ldur            x0, [fp, #-0x10]
    // 0x12927ec: ldur            x1, [fp, #-0x20]
    // 0x12927f0: stur            x5, [fp, #-0x18]
    // 0x12927f4: r0 = of()
    //     0x12927f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12927f8: LoadField: r1 = r0->field_87
    //     0x12927f8: ldur            w1, [x0, #0x87]
    // 0x12927fc: DecompressPointer r1
    //     0x12927fc: add             x1, x1, HEAP, lsl #32
    // 0x1292800: LoadField: r0 = r1->field_2b
    //     0x1292800: ldur            w0, [x1, #0x2b]
    // 0x1292804: DecompressPointer r0
    //     0x1292804: add             x0, x0, HEAP, lsl #32
    // 0x1292808: r16 = 12.000000
    //     0x1292808: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x129280c: ldr             x16, [x16, #0x9e8]
    // 0x1292810: r30 = Instance_TextDecoration
    //     0x1292810: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x1292814: ldr             lr, [lr, #0xe30]
    // 0x1292818: stp             lr, x16, [SP]
    // 0x129281c: mov             x1, x0
    // 0x1292820: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0x1292820: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0x1292824: ldr             x4, [x4, #0x698]
    // 0x1292828: r0 = copyWith()
    //     0x1292828: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x129282c: stur            x0, [fp, #-0x20]
    // 0x1292830: r0 = Text()
    //     0x1292830: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292834: mov             x3, x0
    // 0x1292838: ldur            x0, [fp, #-0x18]
    // 0x129283c: stur            x3, [fp, #-0x30]
    // 0x1292840: StoreField: r3->field_b = r0
    //     0x1292840: stur            w0, [x3, #0xb]
    // 0x1292844: ldur            x0, [fp, #-0x20]
    // 0x1292848: StoreField: r3->field_13 = r0
    //     0x1292848: stur            w0, [x3, #0x13]
    // 0x129284c: r1 = Null
    //     0x129284c: mov             x1, NULL
    // 0x1292850: r2 = 6
    //     0x1292850: movz            x2, #0x6
    // 0x1292854: r0 = AllocateArray()
    //     0x1292854: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1292858: mov             x2, x0
    // 0x129285c: ldur            x0, [fp, #-0x40]
    // 0x1292860: stur            x2, [fp, #-0x18]
    // 0x1292864: StoreField: r2->field_f = r0
    //     0x1292864: stur            w0, [x2, #0xf]
    // 0x1292868: r16 = Instance_SizedBox
    //     0x1292868: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x129286c: ldr             x16, [x16, #0xa50]
    // 0x1292870: StoreField: r2->field_13 = r16
    //     0x1292870: stur            w16, [x2, #0x13]
    // 0x1292874: ldur            x0, [fp, #-0x30]
    // 0x1292878: ArrayStore: r2[0] = r0  ; List_4
    //     0x1292878: stur            w0, [x2, #0x17]
    // 0x129287c: r1 = <Widget>
    //     0x129287c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1292880: r0 = AllocateGrowableArray()
    //     0x1292880: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1292884: mov             x1, x0
    // 0x1292888: ldur            x0, [fp, #-0x18]
    // 0x129288c: stur            x1, [fp, #-0x20]
    // 0x1292890: StoreField: r1->field_f = r0
    //     0x1292890: stur            w0, [x1, #0xf]
    // 0x1292894: r2 = 6
    //     0x1292894: movz            x2, #0x6
    // 0x1292898: StoreField: r1->field_b = r2
    //     0x1292898: stur            w2, [x1, #0xb]
    // 0x129289c: r0 = Row()
    //     0x129289c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12928a0: mov             x3, x0
    // 0x12928a4: r0 = Instance_Axis
    //     0x12928a4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12928a8: stur            x3, [fp, #-0x18]
    // 0x12928ac: StoreField: r3->field_f = r0
    //     0x12928ac: stur            w0, [x3, #0xf]
    // 0x12928b0: r4 = Instance_MainAxisAlignment
    //     0x12928b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12928b4: ldr             x4, [x4, #0xa08]
    // 0x12928b8: StoreField: r3->field_13 = r4
    //     0x12928b8: stur            w4, [x3, #0x13]
    // 0x12928bc: r5 = Instance_MainAxisSize
    //     0x12928bc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12928c0: ldr             x5, [x5, #0xa10]
    // 0x12928c4: ArrayStore: r3[0] = r5  ; List_4
    //     0x12928c4: stur            w5, [x3, #0x17]
    // 0x12928c8: r6 = Instance_CrossAxisAlignment
    //     0x12928c8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12928cc: ldr             x6, [x6, #0xa18]
    // 0x12928d0: StoreField: r3->field_1b = r6
    //     0x12928d0: stur            w6, [x3, #0x1b]
    // 0x12928d4: r7 = Instance_VerticalDirection
    //     0x12928d4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12928d8: ldr             x7, [x7, #0xa20]
    // 0x12928dc: StoreField: r3->field_23 = r7
    //     0x12928dc: stur            w7, [x3, #0x23]
    // 0x12928e0: r8 = Instance_Clip
    //     0x12928e0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12928e4: ldr             x8, [x8, #0x38]
    // 0x12928e8: StoreField: r3->field_2b = r8
    //     0x12928e8: stur            w8, [x3, #0x2b]
    // 0x12928ec: StoreField: r3->field_2f = rZR
    //     0x12928ec: stur            xzr, [x3, #0x2f]
    // 0x12928f0: ldur            x1, [fp, #-0x20]
    // 0x12928f4: StoreField: r3->field_b = r1
    //     0x12928f4: stur            w1, [x3, #0xb]
    // 0x12928f8: r1 = Null
    //     0x12928f8: mov             x1, NULL
    // 0x12928fc: r2 = 6
    //     0x12928fc: movz            x2, #0x6
    // 0x1292900: r0 = AllocateArray()
    //     0x1292900: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1292904: mov             x2, x0
    // 0x1292908: ldur            x0, [fp, #-0x10]
    // 0x129290c: stur            x2, [fp, #-0x20]
    // 0x1292910: StoreField: r2->field_f = r0
    //     0x1292910: stur            w0, [x2, #0xf]
    // 0x1292914: r16 = Instance_SizedBox
    //     0x1292914: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1292918: ldr             x16, [x16, #0xc70]
    // 0x129291c: StoreField: r2->field_13 = r16
    //     0x129291c: stur            w16, [x2, #0x13]
    // 0x1292920: ldur            x0, [fp, #-0x18]
    // 0x1292924: ArrayStore: r2[0] = r0  ; List_4
    //     0x1292924: stur            w0, [x2, #0x17]
    // 0x1292928: r1 = <Widget>
    //     0x1292928: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x129292c: r0 = AllocateGrowableArray()
    //     0x129292c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1292930: mov             x1, x0
    // 0x1292934: ldur            x0, [fp, #-0x20]
    // 0x1292938: stur            x1, [fp, #-0x10]
    // 0x129293c: StoreField: r1->field_f = r0
    //     0x129293c: stur            w0, [x1, #0xf]
    // 0x1292940: r2 = 6
    //     0x1292940: movz            x2, #0x6
    // 0x1292944: StoreField: r1->field_b = r2
    //     0x1292944: stur            w2, [x1, #0xb]
    // 0x1292948: r0 = Column()
    //     0x1292948: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x129294c: mov             x1, x0
    // 0x1292950: r0 = Instance_Axis
    //     0x1292950: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1292954: stur            x1, [fp, #-0x18]
    // 0x1292958: StoreField: r1->field_f = r0
    //     0x1292958: stur            w0, [x1, #0xf]
    // 0x129295c: r0 = Instance_MainAxisAlignment
    //     0x129295c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1292960: ldr             x0, [x0, #0xa08]
    // 0x1292964: StoreField: r1->field_13 = r0
    //     0x1292964: stur            w0, [x1, #0x13]
    // 0x1292968: r2 = Instance_MainAxisSize
    //     0x1292968: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x129296c: ldr             x2, [x2, #0xa10]
    // 0x1292970: ArrayStore: r1[0] = r2  ; List_4
    //     0x1292970: stur            w2, [x1, #0x17]
    // 0x1292974: r3 = Instance_CrossAxisAlignment
    //     0x1292974: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1292978: ldr             x3, [x3, #0x890]
    // 0x129297c: StoreField: r1->field_1b = r3
    //     0x129297c: stur            w3, [x1, #0x1b]
    // 0x1292980: r3 = Instance_VerticalDirection
    //     0x1292980: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1292984: ldr             x3, [x3, #0xa20]
    // 0x1292988: StoreField: r1->field_23 = r3
    //     0x1292988: stur            w3, [x1, #0x23]
    // 0x129298c: r4 = Instance_Clip
    //     0x129298c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1292990: ldr             x4, [x4, #0x38]
    // 0x1292994: StoreField: r1->field_2b = r4
    //     0x1292994: stur            w4, [x1, #0x2b]
    // 0x1292998: StoreField: r1->field_2f = rZR
    //     0x1292998: stur            xzr, [x1, #0x2f]
    // 0x129299c: ldur            x5, [fp, #-0x10]
    // 0x12929a0: StoreField: r1->field_b = r5
    //     0x12929a0: stur            w5, [x1, #0xb]
    // 0x12929a4: r0 = Padding()
    //     0x12929a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12929a8: mov             x2, x0
    // 0x12929ac: r0 = Instance_EdgeInsets
    //     0x12929ac: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x12929b0: ldr             x0, [x0, #0xa78]
    // 0x12929b4: stur            x2, [fp, #-0x10]
    // 0x12929b8: StoreField: r2->field_f = r0
    //     0x12929b8: stur            w0, [x2, #0xf]
    // 0x12929bc: ldur            x0, [fp, #-0x18]
    // 0x12929c0: StoreField: r2->field_b = r0
    //     0x12929c0: stur            w0, [x2, #0xb]
    // 0x12929c4: r1 = <FlexParentData>
    //     0x12929c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x12929c8: ldr             x1, [x1, #0xe00]
    // 0x12929cc: r0 = Expanded()
    //     0x12929cc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x12929d0: mov             x3, x0
    // 0x12929d4: r0 = 1
    //     0x12929d4: movz            x0, #0x1
    // 0x12929d8: stur            x3, [fp, #-0x18]
    // 0x12929dc: StoreField: r3->field_13 = r0
    //     0x12929dc: stur            x0, [x3, #0x13]
    // 0x12929e0: r0 = Instance_FlexFit
    //     0x12929e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x12929e4: ldr             x0, [x0, #0xe08]
    // 0x12929e8: StoreField: r3->field_1b = r0
    //     0x12929e8: stur            w0, [x3, #0x1b]
    // 0x12929ec: ldur            x0, [fp, #-0x10]
    // 0x12929f0: StoreField: r3->field_b = r0
    //     0x12929f0: stur            w0, [x3, #0xb]
    // 0x12929f4: r1 = Null
    //     0x12929f4: mov             x1, NULL
    // 0x12929f8: r2 = 6
    //     0x12929f8: movz            x2, #0x6
    // 0x12929fc: r0 = AllocateArray()
    //     0x12929fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1292a00: mov             x2, x0
    // 0x1292a04: ldur            x0, [fp, #-8]
    // 0x1292a08: stur            x2, [fp, #-0x10]
    // 0x1292a0c: StoreField: r2->field_f = r0
    //     0x1292a0c: stur            w0, [x2, #0xf]
    // 0x1292a10: ldur            x0, [fp, #-0x38]
    // 0x1292a14: StoreField: r2->field_13 = r0
    //     0x1292a14: stur            w0, [x2, #0x13]
    // 0x1292a18: ldur            x0, [fp, #-0x18]
    // 0x1292a1c: ArrayStore: r2[0] = r0  ; List_4
    //     0x1292a1c: stur            w0, [x2, #0x17]
    // 0x1292a20: r1 = <Widget>
    //     0x1292a20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1292a24: r0 = AllocateGrowableArray()
    //     0x1292a24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1292a28: mov             x1, x0
    // 0x1292a2c: ldur            x0, [fp, #-0x10]
    // 0x1292a30: stur            x1, [fp, #-8]
    // 0x1292a34: StoreField: r1->field_f = r0
    //     0x1292a34: stur            w0, [x1, #0xf]
    // 0x1292a38: r0 = 6
    //     0x1292a38: movz            x0, #0x6
    // 0x1292a3c: StoreField: r1->field_b = r0
    //     0x1292a3c: stur            w0, [x1, #0xb]
    // 0x1292a40: r0 = Row()
    //     0x1292a40: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1292a44: mov             x1, x0
    // 0x1292a48: r0 = Instance_Axis
    //     0x1292a48: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1292a4c: stur            x1, [fp, #-0x10]
    // 0x1292a50: StoreField: r1->field_f = r0
    //     0x1292a50: stur            w0, [x1, #0xf]
    // 0x1292a54: r0 = Instance_MainAxisAlignment
    //     0x1292a54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1292a58: ldr             x0, [x0, #0xa08]
    // 0x1292a5c: StoreField: r1->field_13 = r0
    //     0x1292a5c: stur            w0, [x1, #0x13]
    // 0x1292a60: r0 = Instance_MainAxisSize
    //     0x1292a60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1292a64: ldr             x0, [x0, #0xa10]
    // 0x1292a68: ArrayStore: r1[0] = r0  ; List_4
    //     0x1292a68: stur            w0, [x1, #0x17]
    // 0x1292a6c: r0 = Instance_CrossAxisAlignment
    //     0x1292a6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1292a70: ldr             x0, [x0, #0xa18]
    // 0x1292a74: StoreField: r1->field_1b = r0
    //     0x1292a74: stur            w0, [x1, #0x1b]
    // 0x1292a78: r0 = Instance_VerticalDirection
    //     0x1292a78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1292a7c: ldr             x0, [x0, #0xa20]
    // 0x1292a80: StoreField: r1->field_23 = r0
    //     0x1292a80: stur            w0, [x1, #0x23]
    // 0x1292a84: r0 = Instance_Clip
    //     0x1292a84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1292a88: ldr             x0, [x0, #0x38]
    // 0x1292a8c: StoreField: r1->field_2b = r0
    //     0x1292a8c: stur            w0, [x1, #0x2b]
    // 0x1292a90: StoreField: r1->field_2f = rZR
    //     0x1292a90: stur            xzr, [x1, #0x2f]
    // 0x1292a94: ldur            x0, [fp, #-8]
    // 0x1292a98: StoreField: r1->field_b = r0
    //     0x1292a98: stur            w0, [x1, #0xb]
    // 0x1292a9c: r0 = Container()
    //     0x1292a9c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1292aa0: stur            x0, [fp, #-8]
    // 0x1292aa4: ldur            x16, [fp, #-0x28]
    // 0x1292aa8: ldur            lr, [fp, #-0x10]
    // 0x1292aac: stp             lr, x16, [SP]
    // 0x1292ab0: mov             x1, x0
    // 0x1292ab4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1292ab4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x1292ab8: ldr             x4, [x4, #0x88]
    // 0x1292abc: r0 = Container()
    //     0x1292abc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1292ac0: r0 = Padding()
    //     0x1292ac0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1292ac4: r1 = Instance_EdgeInsets
    //     0x1292ac4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0x1292ac8: ldr             x1, [x1, #0xb00]
    // 0x1292acc: StoreField: r0->field_f = r1
    //     0x1292acc: stur            w1, [x0, #0xf]
    // 0x1292ad0: ldur            x1, [fp, #-8]
    // 0x1292ad4: StoreField: r0->field_b = r1
    //     0x1292ad4: stur            w1, [x0, #0xb]
    // 0x1292ad8: LeaveFrame
    //     0x1292ad8: mov             SP, fp
    //     0x1292adc: ldp             fp, lr, [SP], #0x10
    // 0x1292ae0: ret
    //     0x1292ae0: ret             
    // 0x1292ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1292ae4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1292ae8: b               #0x1292398
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, String) {
    // ** addr: 0x1292aec, size: 0x74
    // 0x1292aec: EnterFrame
    //     0x1292aec: stp             fp, lr, [SP, #-0x10]!
    //     0x1292af0: mov             fp, SP
    // 0x1292af4: AllocStack(0x28)
    //     0x1292af4: sub             SP, SP, #0x28
    // 0x1292af8: CheckStackOverflow
    //     0x1292af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1292afc: cmp             SP, x16
    //     0x1292b00: b.ls            #0x1292b58
    // 0x1292b04: r1 = Instance_Color
    //     0x1292b04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1292b08: d0 = 0.100000
    //     0x1292b08: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1292b0c: r0 = withOpacity()
    //     0x1292b0c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1292b10: stur            x0, [fp, #-8]
    // 0x1292b14: r0 = Container()
    //     0x1292b14: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1292b18: stur            x0, [fp, #-0x10]
    // 0x1292b1c: r16 = 56.000000
    //     0x1292b1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1292b20: ldr             x16, [x16, #0xb78]
    // 0x1292b24: r30 = 56.000000
    //     0x1292b24: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1292b28: ldr             lr, [lr, #0xb78]
    // 0x1292b2c: stp             lr, x16, [SP, #8]
    // 0x1292b30: ldur            x16, [fp, #-8]
    // 0x1292b34: str             x16, [SP]
    // 0x1292b38: mov             x1, x0
    // 0x1292b3c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, height, 0x2, width, 0x1, null]
    //     0x1292b3c: add             x4, PP, #0x48, lsl #12  ; [pp+0x489a8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x1292b40: ldr             x4, [x4, #0x9a8]
    // 0x1292b44: r0 = Container()
    //     0x1292b44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1292b48: ldur            x0, [fp, #-0x10]
    // 0x1292b4c: LeaveFrame
    //     0x1292b4c: mov             SP, fp
    //     0x1292b50: ldp             fp, lr, [SP], #0x10
    // 0x1292b54: ret
    //     0x1292b54: ret             
    // 0x1292b58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1292b58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1292b5c: b               #0x1292b04
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0x1292b60, size: 0x200
    // 0x1292b60: EnterFrame
    //     0x1292b60: stp             fp, lr, [SP, #-0x10]!
    //     0x1292b64: mov             fp, SP
    // 0x1292b68: AllocStack(0x28)
    //     0x1292b68: sub             SP, SP, #0x28
    // 0x1292b6c: CheckStackOverflow
    //     0x1292b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1292b70: cmp             SP, x16
    //     0x1292b74: b.ls            #0x1292d58
    // 0x1292b78: LoadField: r0 = r1->field_23
    //     0x1292b78: ldur            w0, [x1, #0x23]
    // 0x1292b7c: DecompressPointer r0
    //     0x1292b7c: add             x0, x0, HEAP, lsl #32
    // 0x1292b80: stur            x0, [fp, #-8]
    // 0x1292b84: r0 = ColorFilter()
    //     0x1292b84: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x1292b88: mov             x1, x0
    // 0x1292b8c: ldur            x0, [fp, #-8]
    // 0x1292b90: stur            x1, [fp, #-0x10]
    // 0x1292b94: StoreField: r1->field_7 = r0
    //     0x1292b94: stur            w0, [x1, #7]
    // 0x1292b98: r0 = Instance_BlendMode
    //     0x1292b98: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x1292b9c: ldr             x0, [x0, #0xb30]
    // 0x1292ba0: StoreField: r1->field_b = r0
    //     0x1292ba0: stur            w0, [x1, #0xb]
    // 0x1292ba4: r0 = 1
    //     0x1292ba4: movz            x0, #0x1
    // 0x1292ba8: StoreField: r1->field_13 = r0
    //     0x1292ba8: stur            x0, [x1, #0x13]
    // 0x1292bac: r0 = SvgPicture()
    //     0x1292bac: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1292bb0: stur            x0, [fp, #-8]
    // 0x1292bb4: r16 = Instance_BoxFit
    //     0x1292bb4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x1292bb8: ldr             x16, [x16, #0xb18]
    // 0x1292bbc: ldur            lr, [fp, #-0x10]
    // 0x1292bc0: stp             lr, x16, [SP]
    // 0x1292bc4: mov             x1, x0
    // 0x1292bc8: r2 = "assets/images/x.svg"
    //     0x1292bc8: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0x1292bcc: ldr             x2, [x2, #0x5e8]
    // 0x1292bd0: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x1292bd0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x1292bd4: ldr             x4, [x4, #0x820]
    // 0x1292bd8: r0 = SvgPicture.asset()
    //     0x1292bd8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1292bdc: r0 = InkWell()
    //     0x1292bdc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1292be0: mov             x3, x0
    // 0x1292be4: ldur            x0, [fp, #-8]
    // 0x1292be8: stur            x3, [fp, #-0x10]
    // 0x1292bec: StoreField: r3->field_b = r0
    //     0x1292bec: stur            w0, [x3, #0xb]
    // 0x1292bf0: r1 = Function '<anonymous closure>':.
    //     0x1292bf0: add             x1, PP, #0x48, lsl #12  ; [pp+0x489b0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x1292bf4: ldr             x1, [x1, #0x9b0]
    // 0x1292bf8: r2 = Null
    //     0x1292bf8: mov             x2, NULL
    // 0x1292bfc: r0 = AllocateClosure()
    //     0x1292bfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1292c00: mov             x1, x0
    // 0x1292c04: ldur            x0, [fp, #-0x10]
    // 0x1292c08: StoreField: r0->field_f = r1
    //     0x1292c08: stur            w1, [x0, #0xf]
    // 0x1292c0c: r1 = true
    //     0x1292c0c: add             x1, NULL, #0x20  ; true
    // 0x1292c10: StoreField: r0->field_43 = r1
    //     0x1292c10: stur            w1, [x0, #0x43]
    // 0x1292c14: r2 = Instance_BoxShape
    //     0x1292c14: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1292c18: ldr             x2, [x2, #0x80]
    // 0x1292c1c: StoreField: r0->field_47 = r2
    //     0x1292c1c: stur            w2, [x0, #0x47]
    // 0x1292c20: StoreField: r0->field_6f = r1
    //     0x1292c20: stur            w1, [x0, #0x6f]
    // 0x1292c24: r2 = false
    //     0x1292c24: add             x2, NULL, #0x30  ; false
    // 0x1292c28: StoreField: r0->field_73 = r2
    //     0x1292c28: stur            w2, [x0, #0x73]
    // 0x1292c2c: StoreField: r0->field_83 = r1
    //     0x1292c2c: stur            w1, [x0, #0x83]
    // 0x1292c30: StoreField: r0->field_7b = r2
    //     0x1292c30: stur            w2, [x0, #0x7b]
    // 0x1292c34: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x1292c34: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1292c38: ldr             x0, [x0, #0x1ab0]
    //     0x1292c3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1292c40: cmp             w0, w16
    //     0x1292c44: b.ne            #0x1292c54
    //     0x1292c48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x1292c4c: ldr             x2, [x2, #0x60]
    //     0x1292c50: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1292c54: LoadField: r1 = r0->field_87
    //     0x1292c54: ldur            w1, [x0, #0x87]
    // 0x1292c58: DecompressPointer r1
    //     0x1292c58: add             x1, x1, HEAP, lsl #32
    // 0x1292c5c: LoadField: r0 = r1->field_7
    //     0x1292c5c: ldur            w0, [x1, #7]
    // 0x1292c60: DecompressPointer r0
    //     0x1292c60: add             x0, x0, HEAP, lsl #32
    // 0x1292c64: r16 = Instance_Color
    //     0x1292c64: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1292c68: r30 = 16.000000
    //     0x1292c68: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1292c6c: ldr             lr, [lr, #0x188]
    // 0x1292c70: stp             lr, x16, [SP]
    // 0x1292c74: mov             x1, x0
    // 0x1292c78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1292c78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1292c7c: ldr             x4, [x4, #0x9b8]
    // 0x1292c80: r0 = copyWith()
    //     0x1292c80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1292c84: stur            x0, [fp, #-8]
    // 0x1292c88: r0 = Text()
    //     0x1292c88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1292c8c: mov             x3, x0
    // 0x1292c90: r0 = "My Bag"
    //     0x1292c90: add             x0, PP, #0x48, lsl #12  ; [pp+0x489b8] "My Bag"
    //     0x1292c94: ldr             x0, [x0, #0x9b8]
    // 0x1292c98: stur            x3, [fp, #-0x18]
    // 0x1292c9c: StoreField: r3->field_b = r0
    //     0x1292c9c: stur            w0, [x3, #0xb]
    // 0x1292ca0: ldur            x0, [fp, #-8]
    // 0x1292ca4: StoreField: r3->field_13 = r0
    //     0x1292ca4: stur            w0, [x3, #0x13]
    // 0x1292ca8: r1 = Null
    //     0x1292ca8: mov             x1, NULL
    // 0x1292cac: r2 = 6
    //     0x1292cac: movz            x2, #0x6
    // 0x1292cb0: r0 = AllocateArray()
    //     0x1292cb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1292cb4: mov             x2, x0
    // 0x1292cb8: ldur            x0, [fp, #-0x10]
    // 0x1292cbc: stur            x2, [fp, #-8]
    // 0x1292cc0: StoreField: r2->field_f = r0
    //     0x1292cc0: stur            w0, [x2, #0xf]
    // 0x1292cc4: r16 = Instance_SizedBox
    //     0x1292cc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x1292cc8: ldr             x16, [x16, #0xaa8]
    // 0x1292ccc: StoreField: r2->field_13 = r16
    //     0x1292ccc: stur            w16, [x2, #0x13]
    // 0x1292cd0: ldur            x0, [fp, #-0x18]
    // 0x1292cd4: ArrayStore: r2[0] = r0  ; List_4
    //     0x1292cd4: stur            w0, [x2, #0x17]
    // 0x1292cd8: r1 = <Widget>
    //     0x1292cd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1292cdc: r0 = AllocateGrowableArray()
    //     0x1292cdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1292ce0: mov             x1, x0
    // 0x1292ce4: ldur            x0, [fp, #-8]
    // 0x1292ce8: stur            x1, [fp, #-0x10]
    // 0x1292cec: StoreField: r1->field_f = r0
    //     0x1292cec: stur            w0, [x1, #0xf]
    // 0x1292cf0: r0 = 6
    //     0x1292cf0: movz            x0, #0x6
    // 0x1292cf4: StoreField: r1->field_b = r0
    //     0x1292cf4: stur            w0, [x1, #0xb]
    // 0x1292cf8: r0 = Row()
    //     0x1292cf8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1292cfc: r1 = Instance_Axis
    //     0x1292cfc: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1292d00: StoreField: r0->field_f = r1
    //     0x1292d00: stur            w1, [x0, #0xf]
    // 0x1292d04: r1 = Instance_MainAxisAlignment
    //     0x1292d04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1292d08: ldr             x1, [x1, #0xa08]
    // 0x1292d0c: StoreField: r0->field_13 = r1
    //     0x1292d0c: stur            w1, [x0, #0x13]
    // 0x1292d10: r1 = Instance_MainAxisSize
    //     0x1292d10: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1292d14: ldr             x1, [x1, #0xa10]
    // 0x1292d18: ArrayStore: r0[0] = r1  ; List_4
    //     0x1292d18: stur            w1, [x0, #0x17]
    // 0x1292d1c: r1 = Instance_CrossAxisAlignment
    //     0x1292d1c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1292d20: ldr             x1, [x1, #0xa18]
    // 0x1292d24: StoreField: r0->field_1b = r1
    //     0x1292d24: stur            w1, [x0, #0x1b]
    // 0x1292d28: r1 = Instance_VerticalDirection
    //     0x1292d28: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1292d2c: ldr             x1, [x1, #0xa20]
    // 0x1292d30: StoreField: r0->field_23 = r1
    //     0x1292d30: stur            w1, [x0, #0x23]
    // 0x1292d34: r1 = Instance_Clip
    //     0x1292d34: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1292d38: ldr             x1, [x1, #0x38]
    // 0x1292d3c: StoreField: r0->field_2b = r1
    //     0x1292d3c: stur            w1, [x0, #0x2b]
    // 0x1292d40: StoreField: r0->field_2f = rZR
    //     0x1292d40: stur            xzr, [x0, #0x2f]
    // 0x1292d44: ldur            x1, [fp, #-0x10]
    // 0x1292d48: StoreField: r0->field_b = r1
    //     0x1292d48: stur            w1, [x0, #0xb]
    // 0x1292d4c: LeaveFrame
    //     0x1292d4c: mov             SP, fp
    //     0x1292d50: ldp             fp, lr, [SP], #0x10
    // 0x1292d54: ret
    //     0x1292d54: ret             
    // 0x1292d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1292d58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1292d5c: b               #0x1292b78
  }
  _ BagBottomSheet(/* No info */) {
    // ** addr: 0x143e0f0, size: 0x424
    // 0x143e0f0: EnterFrame
    //     0x143e0f0: stp             fp, lr, [SP, #-0x10]!
    //     0x143e0f4: mov             fp, SP
    // 0x143e0f8: AllocStack(0x28)
    //     0x143e0f8: sub             SP, SP, #0x28
    // 0x143e0fc: SetupParameters(BagBottomSheet this /* r1 => r6, fp-0x28 */, dynamic _ /* r2 => r0 */, dynamic _ /* r6 => r2 */, dynamic _ /* r7 => r1 */)
    //     0x143e0fc: mov             x0, x2
    //     0x143e100: mov             x4, x2
    //     0x143e104: mov             x2, x6
    //     0x143e108: mov             x6, x1
    //     0x143e10c: stur            x1, [fp, #-0x28]
    //     0x143e110: mov             x1, x7
    // 0x143e114: StoreField: r6->field_b = r0
    //     0x143e114: stur            w0, [x6, #0xb]
    //     0x143e118: ldurb           w16, [x6, #-1]
    //     0x143e11c: ldurb           w17, [x0, #-1]
    //     0x143e120: and             x16, x17, x16, lsr #2
    //     0x143e124: tst             x16, HEAP, lsr #32
    //     0x143e128: b.eq            #0x143e130
    //     0x143e12c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x143e130: ldr             x0, [fp, #0x18]
    // 0x143e134: StoreField: r6->field_f = r0
    //     0x143e134: stur            w0, [x6, #0xf]
    //     0x143e138: ldurb           w16, [x6, #-1]
    //     0x143e13c: ldurb           w17, [x0, #-1]
    //     0x143e140: and             x16, x17, x16, lsr #2
    //     0x143e144: tst             x16, HEAP, lsr #32
    //     0x143e148: b.eq            #0x143e150
    //     0x143e14c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x143e150: ldr             x0, [fp, #0x10]
    // 0x143e154: StoreField: r6->field_13 = r0
    //     0x143e154: stur            w0, [x6, #0x13]
    //     0x143e158: ldurb           w16, [x6, #-1]
    //     0x143e15c: ldurb           w17, [x0, #-1]
    //     0x143e160: and             x16, x17, x16, lsr #2
    //     0x143e164: tst             x16, HEAP, lsr #32
    //     0x143e168: b.eq            #0x143e170
    //     0x143e16c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x143e170: mov             x0, x3
    // 0x143e174: ArrayStore: r6[0] = r0  ; List_4
    //     0x143e174: stur            w0, [x6, #0x17]
    //     0x143e178: ldurb           w16, [x6, #-1]
    //     0x143e17c: ldurb           w17, [x0, #-1]
    //     0x143e180: and             x16, x17, x16, lsr #2
    //     0x143e184: tst             x16, HEAP, lsr #32
    //     0x143e188: b.eq            #0x143e190
    //     0x143e18c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x143e190: mov             x0, x1
    // 0x143e194: StoreField: r6->field_1b = r0
    //     0x143e194: stur            w0, [x6, #0x1b]
    //     0x143e198: ldurb           w16, [x6, #-1]
    //     0x143e19c: ldurb           w17, [x0, #-1]
    //     0x143e1a0: and             x16, x17, x16, lsr #2
    //     0x143e1a4: tst             x16, HEAP, lsr #32
    //     0x143e1a8: b.eq            #0x143e1b0
    //     0x143e1ac: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x143e1b0: mov             x0, x2
    // 0x143e1b4: StoreField: r6->field_1f = r0
    //     0x143e1b4: stur            w0, [x6, #0x1f]
    //     0x143e1b8: ldurb           w16, [x6, #-1]
    //     0x143e1bc: ldurb           w17, [x0, #-1]
    //     0x143e1c0: and             x16, x17, x16, lsr #2
    //     0x143e1c4: tst             x16, HEAP, lsr #32
    //     0x143e1c8: b.eq            #0x143e1d0
    //     0x143e1cc: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x143e1d0: LoadField: r0 = r5->field_3f
    //     0x143e1d0: ldur            w0, [x5, #0x3f]
    // 0x143e1d4: DecompressPointer r0
    //     0x143e1d4: add             x0, x0, HEAP, lsl #32
    // 0x143e1d8: stur            x0, [fp, #-0x20]
    // 0x143e1dc: cmp             w0, NULL
    // 0x143e1e0: b.ne            #0x143e1ec
    // 0x143e1e4: r1 = Null
    //     0x143e1e4: mov             x1, NULL
    // 0x143e1e8: b               #0x143e210
    // 0x143e1ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x143e1ec: ldur            w1, [x0, #0x17]
    // 0x143e1f0: DecompressPointer r1
    //     0x143e1f0: add             x1, x1, HEAP, lsl #32
    // 0x143e1f4: cmp             w1, NULL
    // 0x143e1f8: b.ne            #0x143e204
    // 0x143e1fc: r1 = Null
    //     0x143e1fc: mov             x1, NULL
    // 0x143e200: b               #0x143e210
    // 0x143e204: LoadField: r2 = r1->field_7
    //     0x143e204: ldur            w2, [x1, #7]
    // 0x143e208: DecompressPointer r2
    //     0x143e208: add             x2, x2, HEAP, lsl #32
    // 0x143e20c: mov             x1, x2
    // 0x143e210: cmp             w1, NULL
    // 0x143e214: b.ne            #0x143e220
    // 0x143e218: r1 = 0
    //     0x143e218: movz            x1, #0
    // 0x143e21c: b               #0x143e230
    // 0x143e220: r2 = LoadInt32Instr(r1)
    //     0x143e220: sbfx            x2, x1, #1, #0x1f
    //     0x143e224: tbz             w1, #0, #0x143e22c
    //     0x143e228: ldur            x2, [x1, #7]
    // 0x143e22c: mov             x1, x2
    // 0x143e230: stur            x1, [fp, #-0x18]
    // 0x143e234: cmp             w0, NULL
    // 0x143e238: b.ne            #0x143e244
    // 0x143e23c: r2 = Null
    //     0x143e23c: mov             x2, NULL
    // 0x143e240: b               #0x143e268
    // 0x143e244: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x143e244: ldur            w2, [x0, #0x17]
    // 0x143e248: DecompressPointer r2
    //     0x143e248: add             x2, x2, HEAP, lsl #32
    // 0x143e24c: cmp             w2, NULL
    // 0x143e250: b.ne            #0x143e25c
    // 0x143e254: r2 = Null
    //     0x143e254: mov             x2, NULL
    // 0x143e258: b               #0x143e268
    // 0x143e25c: LoadField: r3 = r2->field_b
    //     0x143e25c: ldur            w3, [x2, #0xb]
    // 0x143e260: DecompressPointer r3
    //     0x143e260: add             x3, x3, HEAP, lsl #32
    // 0x143e264: mov             x2, x3
    // 0x143e268: cmp             w2, NULL
    // 0x143e26c: b.ne            #0x143e278
    // 0x143e270: r2 = 0
    //     0x143e270: movz            x2, #0
    // 0x143e274: b               #0x143e288
    // 0x143e278: r3 = LoadInt32Instr(r2)
    //     0x143e278: sbfx            x3, x2, #1, #0x1f
    //     0x143e27c: tbz             w2, #0, #0x143e284
    //     0x143e280: ldur            x3, [x2, #7]
    // 0x143e284: mov             x2, x3
    // 0x143e288: stur            x2, [fp, #-0x10]
    // 0x143e28c: cmp             w0, NULL
    // 0x143e290: b.ne            #0x143e29c
    // 0x143e294: r3 = Null
    //     0x143e294: mov             x3, NULL
    // 0x143e298: b               #0x143e2c0
    // 0x143e29c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x143e29c: ldur            w3, [x0, #0x17]
    // 0x143e2a0: DecompressPointer r3
    //     0x143e2a0: add             x3, x3, HEAP, lsl #32
    // 0x143e2a4: cmp             w3, NULL
    // 0x143e2a8: b.ne            #0x143e2b4
    // 0x143e2ac: r3 = Null
    //     0x143e2ac: mov             x3, NULL
    // 0x143e2b0: b               #0x143e2c0
    // 0x143e2b4: LoadField: r4 = r3->field_f
    //     0x143e2b4: ldur            w4, [x3, #0xf]
    // 0x143e2b8: DecompressPointer r4
    //     0x143e2b8: add             x4, x4, HEAP, lsl #32
    // 0x143e2bc: mov             x3, x4
    // 0x143e2c0: cmp             w3, NULL
    // 0x143e2c4: b.ne            #0x143e2d0
    // 0x143e2c8: r3 = 0
    //     0x143e2c8: movz            x3, #0
    // 0x143e2cc: b               #0x143e2e0
    // 0x143e2d0: r4 = LoadInt32Instr(r3)
    //     0x143e2d0: sbfx            x4, x3, #1, #0x1f
    //     0x143e2d4: tbz             w3, #0, #0x143e2dc
    //     0x143e2d8: ldur            x4, [x3, #7]
    // 0x143e2dc: mov             x3, x4
    // 0x143e2e0: stur            x3, [fp, #-8]
    // 0x143e2e4: r0 = Color()
    //     0x143e2e4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x143e2e8: r1 = Instance_ColorSpace
    //     0x143e2e8: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x143e2ec: StoreField: r0->field_27 = r1
    //     0x143e2ec: stur            w1, [x0, #0x27]
    // 0x143e2f0: d0 = 1.000000
    //     0x143e2f0: fmov            d0, #1.00000000
    // 0x143e2f4: StoreField: r0->field_7 = d0
    //     0x143e2f4: stur            d0, [x0, #7]
    // 0x143e2f8: ldur            x2, [fp, #-0x18]
    // 0x143e2fc: ubfx            x2, x2, #0, #0x20
    // 0x143e300: and             w3, w2, #0xff
    // 0x143e304: ubfx            x3, x3, #0, #0x20
    // 0x143e308: scvtf           d0, x3
    // 0x143e30c: d1 = 255.000000
    //     0x143e30c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x143e310: fdiv            d2, d0, d1
    // 0x143e314: StoreField: r0->field_f = d2
    //     0x143e314: stur            d2, [x0, #0xf]
    // 0x143e318: ldur            x2, [fp, #-0x10]
    // 0x143e31c: ubfx            x2, x2, #0, #0x20
    // 0x143e320: and             w3, w2, #0xff
    // 0x143e324: ubfx            x3, x3, #0, #0x20
    // 0x143e328: scvtf           d0, x3
    // 0x143e32c: fdiv            d2, d0, d1
    // 0x143e330: ArrayStore: r0[0] = d2  ; List_8
    //     0x143e330: stur            d2, [x0, #0x17]
    // 0x143e334: ldur            x2, [fp, #-8]
    // 0x143e338: ubfx            x2, x2, #0, #0x20
    // 0x143e33c: and             w3, w2, #0xff
    // 0x143e340: ubfx            x3, x3, #0, #0x20
    // 0x143e344: scvtf           d0, x3
    // 0x143e348: fdiv            d2, d0, d1
    // 0x143e34c: StoreField: r0->field_1f = d2
    //     0x143e34c: stur            d2, [x0, #0x1f]
    // 0x143e350: ldur            x2, [fp, #-0x28]
    // 0x143e354: StoreField: r2->field_23 = r0
    //     0x143e354: stur            w0, [x2, #0x23]
    //     0x143e358: ldurb           w16, [x2, #-1]
    //     0x143e35c: ldurb           w17, [x0, #-1]
    //     0x143e360: and             x16, x17, x16, lsr #2
    //     0x143e364: tst             x16, HEAP, lsr #32
    //     0x143e368: b.eq            #0x143e370
    //     0x143e36c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x143e370: ldur            x0, [fp, #-0x20]
    // 0x143e374: cmp             w0, NULL
    // 0x143e378: b.ne            #0x143e384
    // 0x143e37c: r3 = Null
    //     0x143e37c: mov             x3, NULL
    // 0x143e380: b               #0x143e3a8
    // 0x143e384: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x143e384: ldur            w3, [x0, #0x17]
    // 0x143e388: DecompressPointer r3
    //     0x143e388: add             x3, x3, HEAP, lsl #32
    // 0x143e38c: cmp             w3, NULL
    // 0x143e390: b.ne            #0x143e39c
    // 0x143e394: r3 = Null
    //     0x143e394: mov             x3, NULL
    // 0x143e398: b               #0x143e3a8
    // 0x143e39c: LoadField: r4 = r3->field_7
    //     0x143e39c: ldur            w4, [x3, #7]
    // 0x143e3a0: DecompressPointer r4
    //     0x143e3a0: add             x4, x4, HEAP, lsl #32
    // 0x143e3a4: mov             x3, x4
    // 0x143e3a8: cmp             w3, NULL
    // 0x143e3ac: b.ne            #0x143e3b8
    // 0x143e3b0: r3 = 0
    //     0x143e3b0: movz            x3, #0
    // 0x143e3b4: b               #0x143e3c8
    // 0x143e3b8: r4 = LoadInt32Instr(r3)
    //     0x143e3b8: sbfx            x4, x3, #1, #0x1f
    //     0x143e3bc: tbz             w3, #0, #0x143e3c4
    //     0x143e3c0: ldur            x4, [x3, #7]
    // 0x143e3c4: mov             x3, x4
    // 0x143e3c8: stur            x3, [fp, #-0x18]
    // 0x143e3cc: cmp             w0, NULL
    // 0x143e3d0: b.ne            #0x143e3dc
    // 0x143e3d4: r4 = Null
    //     0x143e3d4: mov             x4, NULL
    // 0x143e3d8: b               #0x143e400
    // 0x143e3dc: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x143e3dc: ldur            w4, [x0, #0x17]
    // 0x143e3e0: DecompressPointer r4
    //     0x143e3e0: add             x4, x4, HEAP, lsl #32
    // 0x143e3e4: cmp             w4, NULL
    // 0x143e3e8: b.ne            #0x143e3f4
    // 0x143e3ec: r4 = Null
    //     0x143e3ec: mov             x4, NULL
    // 0x143e3f0: b               #0x143e400
    // 0x143e3f4: LoadField: r5 = r4->field_b
    //     0x143e3f4: ldur            w5, [x4, #0xb]
    // 0x143e3f8: DecompressPointer r5
    //     0x143e3f8: add             x5, x5, HEAP, lsl #32
    // 0x143e3fc: mov             x4, x5
    // 0x143e400: cmp             w4, NULL
    // 0x143e404: b.ne            #0x143e410
    // 0x143e408: r4 = 0
    //     0x143e408: movz            x4, #0
    // 0x143e40c: b               #0x143e420
    // 0x143e410: r5 = LoadInt32Instr(r4)
    //     0x143e410: sbfx            x5, x4, #1, #0x1f
    //     0x143e414: tbz             w4, #0, #0x143e41c
    //     0x143e418: ldur            x5, [x4, #7]
    // 0x143e41c: mov             x4, x5
    // 0x143e420: stur            x4, [fp, #-0x10]
    // 0x143e424: cmp             w0, NULL
    // 0x143e428: b.ne            #0x143e434
    // 0x143e42c: r0 = Null
    //     0x143e42c: mov             x0, NULL
    // 0x143e430: b               #0x143e454
    // 0x143e434: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x143e434: ldur            w5, [x0, #0x17]
    // 0x143e438: DecompressPointer r5
    //     0x143e438: add             x5, x5, HEAP, lsl #32
    // 0x143e43c: cmp             w5, NULL
    // 0x143e440: b.ne            #0x143e44c
    // 0x143e444: r0 = Null
    //     0x143e444: mov             x0, NULL
    // 0x143e448: b               #0x143e454
    // 0x143e44c: LoadField: r0 = r5->field_f
    //     0x143e44c: ldur            w0, [x5, #0xf]
    // 0x143e450: DecompressPointer r0
    //     0x143e450: add             x0, x0, HEAP, lsl #32
    // 0x143e454: cmp             w0, NULL
    // 0x143e458: b.ne            #0x143e464
    // 0x143e45c: r0 = 0
    //     0x143e45c: movz            x0, #0
    // 0x143e460: b               #0x143e474
    // 0x143e464: r5 = LoadInt32Instr(r0)
    //     0x143e464: sbfx            x5, x0, #1, #0x1f
    //     0x143e468: tbz             w0, #0, #0x143e470
    //     0x143e46c: ldur            x5, [x0, #7]
    // 0x143e470: mov             x0, x5
    // 0x143e474: stur            x0, [fp, #-8]
    // 0x143e478: r0 = Color()
    //     0x143e478: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x143e47c: r1 = Instance_ColorSpace
    //     0x143e47c: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x143e480: StoreField: r0->field_27 = r1
    //     0x143e480: stur            w1, [x0, #0x27]
    // 0x143e484: d0 = 0.400000
    //     0x143e484: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x143e488: StoreField: r0->field_7 = d0
    //     0x143e488: stur            d0, [x0, #7]
    // 0x143e48c: ldur            x1, [fp, #-0x18]
    // 0x143e490: ubfx            x1, x1, #0, #0x20
    // 0x143e494: and             w2, w1, #0xff
    // 0x143e498: ubfx            x2, x2, #0, #0x20
    // 0x143e49c: scvtf           d0, x2
    // 0x143e4a0: d1 = 255.000000
    //     0x143e4a0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x143e4a4: fdiv            d2, d0, d1
    // 0x143e4a8: StoreField: r0->field_f = d2
    //     0x143e4a8: stur            d2, [x0, #0xf]
    // 0x143e4ac: ldur            x1, [fp, #-0x10]
    // 0x143e4b0: ubfx            x1, x1, #0, #0x20
    // 0x143e4b4: and             w2, w1, #0xff
    // 0x143e4b8: ubfx            x2, x2, #0, #0x20
    // 0x143e4bc: scvtf           d0, x2
    // 0x143e4c0: fdiv            d2, d0, d1
    // 0x143e4c4: ArrayStore: r0[0] = d2  ; List_8
    //     0x143e4c4: stur            d2, [x0, #0x17]
    // 0x143e4c8: ldur            x1, [fp, #-8]
    // 0x143e4cc: ubfx            x1, x1, #0, #0x20
    // 0x143e4d0: and             w2, w1, #0xff
    // 0x143e4d4: ubfx            x2, x2, #0, #0x20
    // 0x143e4d8: scvtf           d0, x2
    // 0x143e4dc: fdiv            d2, d0, d1
    // 0x143e4e0: StoreField: r0->field_1f = d2
    //     0x143e4e0: stur            d2, [x0, #0x1f]
    // 0x143e4e4: ldur            x1, [fp, #-0x28]
    // 0x143e4e8: StoreField: r1->field_27 = r0
    //     0x143e4e8: stur            w0, [x1, #0x27]
    //     0x143e4ec: ldurb           w16, [x1, #-1]
    //     0x143e4f0: ldurb           w17, [x0, #-1]
    //     0x143e4f4: and             x16, x17, x16, lsr #2
    //     0x143e4f8: tst             x16, HEAP, lsr #32
    //     0x143e4fc: b.eq            #0x143e504
    //     0x143e500: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x143e504: r0 = Null
    //     0x143e504: mov             x0, NULL
    // 0x143e508: LeaveFrame
    //     0x143e508: mov             SP, fp
    //     0x143e50c: ldp             fp, lr, [SP], #0x10
    // 0x143e510: ret
    //     0x143e510: ret             
  }
}
