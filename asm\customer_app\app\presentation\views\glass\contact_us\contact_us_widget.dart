// lib: , url: package:customer_app/app/presentation/views/glass/contact_us/contact_us_widget.dart

// class id: 1049382, size: 0x8
class :: {
}

// class id: 4572, size: 0x14, field offset: 0x14
//   const constructor, 
class ContactUsWidget extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14db01c, size: 0xc3c
    // 0x14db01c: EnterFrame
    //     0x14db01c: stp             fp, lr, [SP, #-0x10]!
    //     0x14db020: mov             fp, SP
    // 0x14db024: AllocStack(0x50)
    //     0x14db024: sub             SP, SP, #0x50
    // 0x14db028: SetupParameters(ContactUsWidget this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x14db028: mov             x0, x2
    //     0x14db02c: stur            x2, [fp, #-0x10]
    //     0x14db030: mov             x2, x1
    //     0x14db034: stur            x1, [fp, #-8]
    // 0x14db038: CheckStackOverflow
    //     0x14db038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14db03c: cmp             SP, x16
    //     0x14db040: b.ls            #0x14dbc50
    // 0x14db044: mov             x1, x2
    // 0x14db048: r0 = controller()
    //     0x14db048: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db04c: LoadField: r1 = r0->field_4b
    //     0x14db04c: ldur            w1, [x0, #0x4b]
    // 0x14db050: DecompressPointer r1
    //     0x14db050: add             x1, x1, HEAP, lsl #32
    // 0x14db054: r0 = value()
    //     0x14db054: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db058: LoadField: r1 = r0->field_5f
    //     0x14db058: ldur            w1, [x0, #0x5f]
    // 0x14db05c: DecompressPointer r1
    //     0x14db05c: add             x1, x1, HEAP, lsl #32
    // 0x14db060: cmp             w1, NULL
    // 0x14db064: b.ne            #0x14db070
    // 0x14db068: r0 = Null
    //     0x14db068: mov             x0, NULL
    // 0x14db06c: b               #0x14db078
    // 0x14db070: LoadField: r0 = r1->field_7
    //     0x14db070: ldur            w0, [x1, #7]
    // 0x14db074: DecompressPointer r0
    //     0x14db074: add             x0, x0, HEAP, lsl #32
    // 0x14db078: cmp             w0, NULL
    // 0x14db07c: r16 = true
    //     0x14db07c: add             x16, NULL, #0x20  ; true
    // 0x14db080: r17 = false
    //     0x14db080: add             x17, NULL, #0x30  ; false
    // 0x14db084: csel            x2, x16, x17, ne
    // 0x14db088: ldur            x1, [fp, #-0x10]
    // 0x14db08c: stur            x2, [fp, #-0x18]
    // 0x14db090: r0 = of()
    //     0x14db090: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db094: LoadField: r1 = r0->field_87
    //     0x14db094: ldur            w1, [x0, #0x87]
    // 0x14db098: DecompressPointer r1
    //     0x14db098: add             x1, x1, HEAP, lsl #32
    // 0x14db09c: LoadField: r0 = r1->field_2b
    //     0x14db09c: ldur            w0, [x1, #0x2b]
    // 0x14db0a0: DecompressPointer r0
    //     0x14db0a0: add             x0, x0, HEAP, lsl #32
    // 0x14db0a4: r16 = Instance_Color
    //     0x14db0a4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db0a8: r30 = 14.000000
    //     0x14db0a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db0ac: ldr             lr, [lr, #0x1d8]
    // 0x14db0b0: stp             lr, x16, [SP]
    // 0x14db0b4: mov             x1, x0
    // 0x14db0b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db0b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db0bc: ldr             x4, [x4, #0x9b8]
    // 0x14db0c0: r0 = copyWith()
    //     0x14db0c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db0c4: stur            x0, [fp, #-0x20]
    // 0x14db0c8: r0 = TextSpan()
    //     0x14db0c8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db0cc: mov             x2, x0
    // 0x14db0d0: r0 = "Call: + 91 - "
    //     0x14db0d0: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb28] "Call: + 91 - "
    //     0x14db0d4: ldr             x0, [x0, #0xb28]
    // 0x14db0d8: stur            x2, [fp, #-0x28]
    // 0x14db0dc: StoreField: r2->field_b = r0
    //     0x14db0dc: stur            w0, [x2, #0xb]
    // 0x14db0e0: r0 = Instance__DeferringMouseCursor
    //     0x14db0e0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db0e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14db0e4: stur            w0, [x2, #0x17]
    // 0x14db0e8: ldur            x1, [fp, #-0x20]
    // 0x14db0ec: StoreField: r2->field_7 = r1
    //     0x14db0ec: stur            w1, [x2, #7]
    // 0x14db0f0: ldur            x1, [fp, #-8]
    // 0x14db0f4: r0 = controller()
    //     0x14db0f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db0f8: LoadField: r1 = r0->field_4b
    //     0x14db0f8: ldur            w1, [x0, #0x4b]
    // 0x14db0fc: DecompressPointer r1
    //     0x14db0fc: add             x1, x1, HEAP, lsl #32
    // 0x14db100: r0 = value()
    //     0x14db100: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db104: LoadField: r1 = r0->field_5f
    //     0x14db104: ldur            w1, [x0, #0x5f]
    // 0x14db108: DecompressPointer r1
    //     0x14db108: add             x1, x1, HEAP, lsl #32
    // 0x14db10c: cmp             w1, NULL
    // 0x14db110: b.ne            #0x14db11c
    // 0x14db114: r2 = Null
    //     0x14db114: mov             x2, NULL
    // 0x14db118: b               #0x14db128
    // 0x14db11c: LoadField: r0 = r1->field_7
    //     0x14db11c: ldur            w0, [x1, #7]
    // 0x14db120: DecompressPointer r0
    //     0x14db120: add             x0, x0, HEAP, lsl #32
    // 0x14db124: mov             x2, x0
    // 0x14db128: ldur            x1, [fp, #-0x18]
    // 0x14db12c: ldur            x0, [fp, #-0x28]
    // 0x14db130: str             x2, [SP]
    // 0x14db134: r0 = _interpolateSingle()
    //     0x14db134: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14db138: ldur            x1, [fp, #-0x10]
    // 0x14db13c: stur            x0, [fp, #-0x20]
    // 0x14db140: r0 = of()
    //     0x14db140: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db144: LoadField: r1 = r0->field_87
    //     0x14db144: ldur            w1, [x0, #0x87]
    // 0x14db148: DecompressPointer r1
    //     0x14db148: add             x1, x1, HEAP, lsl #32
    // 0x14db14c: LoadField: r0 = r1->field_2b
    //     0x14db14c: ldur            w0, [x1, #0x2b]
    // 0x14db150: DecompressPointer r0
    //     0x14db150: add             x0, x0, HEAP, lsl #32
    // 0x14db154: r16 = Instance_Color
    //     0x14db154: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db158: r30 = 14.000000
    //     0x14db158: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db15c: ldr             lr, [lr, #0x1d8]
    // 0x14db160: stp             lr, x16, [SP]
    // 0x14db164: mov             x1, x0
    // 0x14db168: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db168: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db16c: ldr             x4, [x4, #0x9b8]
    // 0x14db170: r0 = copyWith()
    //     0x14db170: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db174: stur            x0, [fp, #-0x30]
    // 0x14db178: r0 = TextSpan()
    //     0x14db178: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db17c: mov             x3, x0
    // 0x14db180: ldur            x0, [fp, #-0x20]
    // 0x14db184: stur            x3, [fp, #-0x38]
    // 0x14db188: StoreField: r3->field_b = r0
    //     0x14db188: stur            w0, [x3, #0xb]
    // 0x14db18c: r0 = Instance__DeferringMouseCursor
    //     0x14db18c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db190: ArrayStore: r3[0] = r0  ; List_4
    //     0x14db190: stur            w0, [x3, #0x17]
    // 0x14db194: ldur            x1, [fp, #-0x30]
    // 0x14db198: StoreField: r3->field_7 = r1
    //     0x14db198: stur            w1, [x3, #7]
    // 0x14db19c: r1 = Null
    //     0x14db19c: mov             x1, NULL
    // 0x14db1a0: r2 = 4
    //     0x14db1a0: movz            x2, #0x4
    // 0x14db1a4: r0 = AllocateArray()
    //     0x14db1a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14db1a8: mov             x2, x0
    // 0x14db1ac: ldur            x0, [fp, #-0x28]
    // 0x14db1b0: stur            x2, [fp, #-0x20]
    // 0x14db1b4: StoreField: r2->field_f = r0
    //     0x14db1b4: stur            w0, [x2, #0xf]
    // 0x14db1b8: ldur            x0, [fp, #-0x38]
    // 0x14db1bc: StoreField: r2->field_13 = r0
    //     0x14db1bc: stur            w0, [x2, #0x13]
    // 0x14db1c0: r1 = <InlineSpan>
    //     0x14db1c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14db1c4: ldr             x1, [x1, #0xe40]
    // 0x14db1c8: r0 = AllocateGrowableArray()
    //     0x14db1c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14db1cc: mov             x1, x0
    // 0x14db1d0: ldur            x0, [fp, #-0x20]
    // 0x14db1d4: stur            x1, [fp, #-0x28]
    // 0x14db1d8: StoreField: r1->field_f = r0
    //     0x14db1d8: stur            w0, [x1, #0xf]
    // 0x14db1dc: r2 = 4
    //     0x14db1dc: movz            x2, #0x4
    // 0x14db1e0: StoreField: r1->field_b = r2
    //     0x14db1e0: stur            w2, [x1, #0xb]
    // 0x14db1e4: r0 = TextSpan()
    //     0x14db1e4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db1e8: mov             x1, x0
    // 0x14db1ec: ldur            x0, [fp, #-0x28]
    // 0x14db1f0: stur            x1, [fp, #-0x20]
    // 0x14db1f4: StoreField: r1->field_f = r0
    //     0x14db1f4: stur            w0, [x1, #0xf]
    // 0x14db1f8: r0 = Instance__DeferringMouseCursor
    //     0x14db1f8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db1fc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14db1fc: stur            w0, [x1, #0x17]
    // 0x14db200: r0 = RichText()
    //     0x14db200: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14db204: mov             x1, x0
    // 0x14db208: ldur            x2, [fp, #-0x20]
    // 0x14db20c: stur            x0, [fp, #-0x20]
    // 0x14db210: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14db210: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14db214: r0 = RichText()
    //     0x14db214: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14db218: r0 = Visibility()
    //     0x14db218: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14db21c: mov             x3, x0
    // 0x14db220: ldur            x0, [fp, #-0x20]
    // 0x14db224: stur            x3, [fp, #-0x28]
    // 0x14db228: StoreField: r3->field_b = r0
    //     0x14db228: stur            w0, [x3, #0xb]
    // 0x14db22c: r0 = Instance_SizedBox
    //     0x14db22c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14db230: StoreField: r3->field_f = r0
    //     0x14db230: stur            w0, [x3, #0xf]
    // 0x14db234: ldur            x1, [fp, #-0x18]
    // 0x14db238: StoreField: r3->field_13 = r1
    //     0x14db238: stur            w1, [x3, #0x13]
    // 0x14db23c: r4 = false
    //     0x14db23c: add             x4, NULL, #0x30  ; false
    // 0x14db240: ArrayStore: r3[0] = r4  ; List_4
    //     0x14db240: stur            w4, [x3, #0x17]
    // 0x14db244: StoreField: r3->field_1b = r4
    //     0x14db244: stur            w4, [x3, #0x1b]
    // 0x14db248: StoreField: r3->field_1f = r4
    //     0x14db248: stur            w4, [x3, #0x1f]
    // 0x14db24c: StoreField: r3->field_23 = r4
    //     0x14db24c: stur            w4, [x3, #0x23]
    // 0x14db250: StoreField: r3->field_27 = r4
    //     0x14db250: stur            w4, [x3, #0x27]
    // 0x14db254: StoreField: r3->field_2b = r4
    //     0x14db254: stur            w4, [x3, #0x2b]
    // 0x14db258: r1 = <Widget>
    //     0x14db258: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14db25c: r2 = 18
    //     0x14db25c: movz            x2, #0x12
    // 0x14db260: r0 = AllocateArray()
    //     0x14db260: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14db264: mov             x2, x0
    // 0x14db268: ldur            x0, [fp, #-0x28]
    // 0x14db26c: stur            x2, [fp, #-0x18]
    // 0x14db270: StoreField: r2->field_f = r0
    //     0x14db270: stur            w0, [x2, #0xf]
    // 0x14db274: r16 = Instance_SizedBox
    //     0x14db274: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14db278: ldr             x16, [x16, #0x9f0]
    // 0x14db27c: StoreField: r2->field_13 = r16
    //     0x14db27c: stur            w16, [x2, #0x13]
    // 0x14db280: ldur            x1, [fp, #-8]
    // 0x14db284: r0 = controller()
    //     0x14db284: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db288: LoadField: r1 = r0->field_4b
    //     0x14db288: ldur            w1, [x0, #0x4b]
    // 0x14db28c: DecompressPointer r1
    //     0x14db28c: add             x1, x1, HEAP, lsl #32
    // 0x14db290: r0 = value()
    //     0x14db290: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db294: LoadField: r1 = r0->field_5f
    //     0x14db294: ldur            w1, [x0, #0x5f]
    // 0x14db298: DecompressPointer r1
    //     0x14db298: add             x1, x1, HEAP, lsl #32
    // 0x14db29c: cmp             w1, NULL
    // 0x14db2a0: b.ne            #0x14db2ac
    // 0x14db2a4: r0 = Null
    //     0x14db2a4: mov             x0, NULL
    // 0x14db2a8: b               #0x14db2b4
    // 0x14db2ac: LoadField: r0 = r1->field_b
    //     0x14db2ac: ldur            w0, [x1, #0xb]
    // 0x14db2b0: DecompressPointer r0
    //     0x14db2b0: add             x0, x0, HEAP, lsl #32
    // 0x14db2b4: cmp             w0, NULL
    // 0x14db2b8: r16 = true
    //     0x14db2b8: add             x16, NULL, #0x20  ; true
    // 0x14db2bc: r17 = false
    //     0x14db2bc: add             x17, NULL, #0x30  ; false
    // 0x14db2c0: csel            x2, x16, x17, ne
    // 0x14db2c4: ldur            x1, [fp, #-0x10]
    // 0x14db2c8: stur            x2, [fp, #-0x20]
    // 0x14db2cc: r0 = of()
    //     0x14db2cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db2d0: LoadField: r1 = r0->field_87
    //     0x14db2d0: ldur            w1, [x0, #0x87]
    // 0x14db2d4: DecompressPointer r1
    //     0x14db2d4: add             x1, x1, HEAP, lsl #32
    // 0x14db2d8: LoadField: r0 = r1->field_2b
    //     0x14db2d8: ldur            w0, [x1, #0x2b]
    // 0x14db2dc: DecompressPointer r0
    //     0x14db2dc: add             x0, x0, HEAP, lsl #32
    // 0x14db2e0: r16 = Instance_Color
    //     0x14db2e0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db2e4: r30 = 14.000000
    //     0x14db2e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db2e8: ldr             lr, [lr, #0x1d8]
    // 0x14db2ec: stp             lr, x16, [SP]
    // 0x14db2f0: mov             x1, x0
    // 0x14db2f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db2f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db2f8: ldr             x4, [x4, #0x9b8]
    // 0x14db2fc: r0 = copyWith()
    //     0x14db2fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db300: stur            x0, [fp, #-0x28]
    // 0x14db304: r0 = TextSpan()
    //     0x14db304: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db308: mov             x2, x0
    // 0x14db30c: r0 = "WhatsApp: "
    //     0x14db30c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40b68] "WhatsApp: "
    //     0x14db310: ldr             x0, [x0, #0xb68]
    // 0x14db314: stur            x2, [fp, #-0x30]
    // 0x14db318: StoreField: r2->field_b = r0
    //     0x14db318: stur            w0, [x2, #0xb]
    // 0x14db31c: r0 = Instance__DeferringMouseCursor
    //     0x14db31c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db320: ArrayStore: r2[0] = r0  ; List_4
    //     0x14db320: stur            w0, [x2, #0x17]
    // 0x14db324: ldur            x1, [fp, #-0x28]
    // 0x14db328: StoreField: r2->field_7 = r1
    //     0x14db328: stur            w1, [x2, #7]
    // 0x14db32c: ldur            x1, [fp, #-8]
    // 0x14db330: r0 = controller()
    //     0x14db330: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db334: LoadField: r1 = r0->field_4b
    //     0x14db334: ldur            w1, [x0, #0x4b]
    // 0x14db338: DecompressPointer r1
    //     0x14db338: add             x1, x1, HEAP, lsl #32
    // 0x14db33c: r0 = value()
    //     0x14db33c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db340: LoadField: r1 = r0->field_5f
    //     0x14db340: ldur            w1, [x0, #0x5f]
    // 0x14db344: DecompressPointer r1
    //     0x14db344: add             x1, x1, HEAP, lsl #32
    // 0x14db348: cmp             w1, NULL
    // 0x14db34c: b.ne            #0x14db358
    // 0x14db350: r3 = Null
    //     0x14db350: mov             x3, NULL
    // 0x14db354: b               #0x14db364
    // 0x14db358: LoadField: r0 = r1->field_b
    //     0x14db358: ldur            w0, [x1, #0xb]
    // 0x14db35c: DecompressPointer r0
    //     0x14db35c: add             x0, x0, HEAP, lsl #32
    // 0x14db360: mov             x3, x0
    // 0x14db364: ldur            x2, [fp, #-0x18]
    // 0x14db368: ldur            x1, [fp, #-0x20]
    // 0x14db36c: ldur            x0, [fp, #-0x30]
    // 0x14db370: str             x3, [SP]
    // 0x14db374: r0 = _interpolateSingle()
    //     0x14db374: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14db378: ldur            x1, [fp, #-0x10]
    // 0x14db37c: stur            x0, [fp, #-0x28]
    // 0x14db380: r0 = of()
    //     0x14db380: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db384: LoadField: r1 = r0->field_87
    //     0x14db384: ldur            w1, [x0, #0x87]
    // 0x14db388: DecompressPointer r1
    //     0x14db388: add             x1, x1, HEAP, lsl #32
    // 0x14db38c: LoadField: r0 = r1->field_2b
    //     0x14db38c: ldur            w0, [x1, #0x2b]
    // 0x14db390: DecompressPointer r0
    //     0x14db390: add             x0, x0, HEAP, lsl #32
    // 0x14db394: r16 = Instance_Color
    //     0x14db394: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db398: r30 = 14.000000
    //     0x14db398: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db39c: ldr             lr, [lr, #0x1d8]
    // 0x14db3a0: stp             lr, x16, [SP]
    // 0x14db3a4: mov             x1, x0
    // 0x14db3a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db3a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db3ac: ldr             x4, [x4, #0x9b8]
    // 0x14db3b0: r0 = copyWith()
    //     0x14db3b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db3b4: stur            x0, [fp, #-0x38]
    // 0x14db3b8: r0 = TextSpan()
    //     0x14db3b8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db3bc: mov             x3, x0
    // 0x14db3c0: ldur            x0, [fp, #-0x28]
    // 0x14db3c4: stur            x3, [fp, #-0x40]
    // 0x14db3c8: StoreField: r3->field_b = r0
    //     0x14db3c8: stur            w0, [x3, #0xb]
    // 0x14db3cc: r0 = Instance__DeferringMouseCursor
    //     0x14db3cc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db3d0: ArrayStore: r3[0] = r0  ; List_4
    //     0x14db3d0: stur            w0, [x3, #0x17]
    // 0x14db3d4: ldur            x1, [fp, #-0x38]
    // 0x14db3d8: StoreField: r3->field_7 = r1
    //     0x14db3d8: stur            w1, [x3, #7]
    // 0x14db3dc: r1 = Null
    //     0x14db3dc: mov             x1, NULL
    // 0x14db3e0: r2 = 4
    //     0x14db3e0: movz            x2, #0x4
    // 0x14db3e4: r0 = AllocateArray()
    //     0x14db3e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14db3e8: mov             x2, x0
    // 0x14db3ec: ldur            x0, [fp, #-0x30]
    // 0x14db3f0: stur            x2, [fp, #-0x28]
    // 0x14db3f4: StoreField: r2->field_f = r0
    //     0x14db3f4: stur            w0, [x2, #0xf]
    // 0x14db3f8: ldur            x0, [fp, #-0x40]
    // 0x14db3fc: StoreField: r2->field_13 = r0
    //     0x14db3fc: stur            w0, [x2, #0x13]
    // 0x14db400: r1 = <InlineSpan>
    //     0x14db400: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14db404: ldr             x1, [x1, #0xe40]
    // 0x14db408: r0 = AllocateGrowableArray()
    //     0x14db408: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14db40c: mov             x1, x0
    // 0x14db410: ldur            x0, [fp, #-0x28]
    // 0x14db414: stur            x1, [fp, #-0x30]
    // 0x14db418: StoreField: r1->field_f = r0
    //     0x14db418: stur            w0, [x1, #0xf]
    // 0x14db41c: r2 = 4
    //     0x14db41c: movz            x2, #0x4
    // 0x14db420: StoreField: r1->field_b = r2
    //     0x14db420: stur            w2, [x1, #0xb]
    // 0x14db424: r0 = TextSpan()
    //     0x14db424: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db428: mov             x1, x0
    // 0x14db42c: ldur            x0, [fp, #-0x30]
    // 0x14db430: stur            x1, [fp, #-0x28]
    // 0x14db434: StoreField: r1->field_f = r0
    //     0x14db434: stur            w0, [x1, #0xf]
    // 0x14db438: r0 = Instance__DeferringMouseCursor
    //     0x14db438: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db43c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14db43c: stur            w0, [x1, #0x17]
    // 0x14db440: r0 = RichText()
    //     0x14db440: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14db444: mov             x1, x0
    // 0x14db448: ldur            x2, [fp, #-0x28]
    // 0x14db44c: stur            x0, [fp, #-0x28]
    // 0x14db450: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14db450: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14db454: r0 = RichText()
    //     0x14db454: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14db458: r0 = Visibility()
    //     0x14db458: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14db45c: mov             x1, x0
    // 0x14db460: ldur            x0, [fp, #-0x28]
    // 0x14db464: StoreField: r1->field_b = r0
    //     0x14db464: stur            w0, [x1, #0xb]
    // 0x14db468: r2 = Instance_SizedBox
    //     0x14db468: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14db46c: StoreField: r1->field_f = r2
    //     0x14db46c: stur            w2, [x1, #0xf]
    // 0x14db470: ldur            x0, [fp, #-0x20]
    // 0x14db474: StoreField: r1->field_13 = r0
    //     0x14db474: stur            w0, [x1, #0x13]
    // 0x14db478: r3 = false
    //     0x14db478: add             x3, NULL, #0x30  ; false
    // 0x14db47c: ArrayStore: r1[0] = r3  ; List_4
    //     0x14db47c: stur            w3, [x1, #0x17]
    // 0x14db480: StoreField: r1->field_1b = r3
    //     0x14db480: stur            w3, [x1, #0x1b]
    // 0x14db484: StoreField: r1->field_1f = r3
    //     0x14db484: stur            w3, [x1, #0x1f]
    // 0x14db488: StoreField: r1->field_23 = r3
    //     0x14db488: stur            w3, [x1, #0x23]
    // 0x14db48c: StoreField: r1->field_27 = r3
    //     0x14db48c: stur            w3, [x1, #0x27]
    // 0x14db490: StoreField: r1->field_2b = r3
    //     0x14db490: stur            w3, [x1, #0x2b]
    // 0x14db494: mov             x0, x1
    // 0x14db498: ldur            x1, [fp, #-0x18]
    // 0x14db49c: ArrayStore: r1[2] = r0  ; List_4
    //     0x14db49c: add             x25, x1, #0x17
    //     0x14db4a0: str             w0, [x25]
    //     0x14db4a4: tbz             w0, #0, #0x14db4c0
    //     0x14db4a8: ldurb           w16, [x1, #-1]
    //     0x14db4ac: ldurb           w17, [x0, #-1]
    //     0x14db4b0: and             x16, x17, x16, lsr #2
    //     0x14db4b4: tst             x16, HEAP, lsr #32
    //     0x14db4b8: b.eq            #0x14db4c0
    //     0x14db4bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14db4c0: ldur            x0, [fp, #-0x18]
    // 0x14db4c4: r16 = Instance_SizedBox
    //     0x14db4c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14db4c8: ldr             x16, [x16, #0x9f0]
    // 0x14db4cc: StoreField: r0->field_1b = r16
    //     0x14db4cc: stur            w16, [x0, #0x1b]
    // 0x14db4d0: ldur            x1, [fp, #-8]
    // 0x14db4d4: r0 = controller()
    //     0x14db4d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db4d8: LoadField: r1 = r0->field_4b
    //     0x14db4d8: ldur            w1, [x0, #0x4b]
    // 0x14db4dc: DecompressPointer r1
    //     0x14db4dc: add             x1, x1, HEAP, lsl #32
    // 0x14db4e0: r0 = value()
    //     0x14db4e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db4e4: LoadField: r1 = r0->field_5f
    //     0x14db4e4: ldur            w1, [x0, #0x5f]
    // 0x14db4e8: DecompressPointer r1
    //     0x14db4e8: add             x1, x1, HEAP, lsl #32
    // 0x14db4ec: cmp             w1, NULL
    // 0x14db4f0: b.ne            #0x14db4fc
    // 0x14db4f4: r0 = Null
    //     0x14db4f4: mov             x0, NULL
    // 0x14db4f8: b               #0x14db504
    // 0x14db4fc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14db4fc: ldur            w0, [x1, #0x17]
    // 0x14db500: DecompressPointer r0
    //     0x14db500: add             x0, x0, HEAP, lsl #32
    // 0x14db504: cmp             w0, NULL
    // 0x14db508: r16 = true
    //     0x14db508: add             x16, NULL, #0x20  ; true
    // 0x14db50c: r17 = false
    //     0x14db50c: add             x17, NULL, #0x30  ; false
    // 0x14db510: csel            x2, x16, x17, ne
    // 0x14db514: ldur            x1, [fp, #-0x10]
    // 0x14db518: stur            x2, [fp, #-0x20]
    // 0x14db51c: r0 = of()
    //     0x14db51c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db520: LoadField: r1 = r0->field_87
    //     0x14db520: ldur            w1, [x0, #0x87]
    // 0x14db524: DecompressPointer r1
    //     0x14db524: add             x1, x1, HEAP, lsl #32
    // 0x14db528: LoadField: r0 = r1->field_2b
    //     0x14db528: ldur            w0, [x1, #0x2b]
    // 0x14db52c: DecompressPointer r0
    //     0x14db52c: add             x0, x0, HEAP, lsl #32
    // 0x14db530: r16 = Instance_Color
    //     0x14db530: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db534: r30 = 14.000000
    //     0x14db534: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db538: ldr             lr, [lr, #0x1d8]
    // 0x14db53c: stp             lr, x16, [SP]
    // 0x14db540: mov             x1, x0
    // 0x14db544: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db544: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db548: ldr             x4, [x4, #0x9b8]
    // 0x14db54c: r0 = copyWith()
    //     0x14db54c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db550: stur            x0, [fp, #-0x28]
    // 0x14db554: r0 = TextSpan()
    //     0x14db554: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db558: mov             x2, x0
    // 0x14db55c: r0 = "Customer Support Time: "
    //     0x14db55c: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb38] "Customer Support Time: "
    //     0x14db560: ldr             x0, [x0, #0xb38]
    // 0x14db564: stur            x2, [fp, #-0x30]
    // 0x14db568: StoreField: r2->field_b = r0
    //     0x14db568: stur            w0, [x2, #0xb]
    // 0x14db56c: r0 = Instance__DeferringMouseCursor
    //     0x14db56c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db570: ArrayStore: r2[0] = r0  ; List_4
    //     0x14db570: stur            w0, [x2, #0x17]
    // 0x14db574: ldur            x1, [fp, #-0x28]
    // 0x14db578: StoreField: r2->field_7 = r1
    //     0x14db578: stur            w1, [x2, #7]
    // 0x14db57c: ldur            x1, [fp, #-8]
    // 0x14db580: r0 = controller()
    //     0x14db580: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db584: LoadField: r1 = r0->field_4b
    //     0x14db584: ldur            w1, [x0, #0x4b]
    // 0x14db588: DecompressPointer r1
    //     0x14db588: add             x1, x1, HEAP, lsl #32
    // 0x14db58c: r0 = value()
    //     0x14db58c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db590: LoadField: r1 = r0->field_5f
    //     0x14db590: ldur            w1, [x0, #0x5f]
    // 0x14db594: DecompressPointer r1
    //     0x14db594: add             x1, x1, HEAP, lsl #32
    // 0x14db598: cmp             w1, NULL
    // 0x14db59c: b.ne            #0x14db5a8
    // 0x14db5a0: r3 = Null
    //     0x14db5a0: mov             x3, NULL
    // 0x14db5a4: b               #0x14db5b4
    // 0x14db5a8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14db5a8: ldur            w0, [x1, #0x17]
    // 0x14db5ac: DecompressPointer r0
    //     0x14db5ac: add             x0, x0, HEAP, lsl #32
    // 0x14db5b0: mov             x3, x0
    // 0x14db5b4: ldur            x2, [fp, #-0x18]
    // 0x14db5b8: ldur            x1, [fp, #-0x20]
    // 0x14db5bc: ldur            x0, [fp, #-0x30]
    // 0x14db5c0: str             x3, [SP]
    // 0x14db5c4: r0 = _interpolateSingle()
    //     0x14db5c4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14db5c8: ldur            x1, [fp, #-0x10]
    // 0x14db5cc: stur            x0, [fp, #-0x28]
    // 0x14db5d0: r0 = of()
    //     0x14db5d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db5d4: LoadField: r1 = r0->field_87
    //     0x14db5d4: ldur            w1, [x0, #0x87]
    // 0x14db5d8: DecompressPointer r1
    //     0x14db5d8: add             x1, x1, HEAP, lsl #32
    // 0x14db5dc: LoadField: r0 = r1->field_2b
    //     0x14db5dc: ldur            w0, [x1, #0x2b]
    // 0x14db5e0: DecompressPointer r0
    //     0x14db5e0: add             x0, x0, HEAP, lsl #32
    // 0x14db5e4: r16 = Instance_Color
    //     0x14db5e4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db5e8: r30 = 14.000000
    //     0x14db5e8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db5ec: ldr             lr, [lr, #0x1d8]
    // 0x14db5f0: stp             lr, x16, [SP]
    // 0x14db5f4: mov             x1, x0
    // 0x14db5f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db5f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db5fc: ldr             x4, [x4, #0x9b8]
    // 0x14db600: r0 = copyWith()
    //     0x14db600: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db604: stur            x0, [fp, #-0x38]
    // 0x14db608: r0 = TextSpan()
    //     0x14db608: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db60c: mov             x3, x0
    // 0x14db610: ldur            x0, [fp, #-0x28]
    // 0x14db614: stur            x3, [fp, #-0x40]
    // 0x14db618: StoreField: r3->field_b = r0
    //     0x14db618: stur            w0, [x3, #0xb]
    // 0x14db61c: r0 = Instance__DeferringMouseCursor
    //     0x14db61c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db620: ArrayStore: r3[0] = r0  ; List_4
    //     0x14db620: stur            w0, [x3, #0x17]
    // 0x14db624: ldur            x1, [fp, #-0x38]
    // 0x14db628: StoreField: r3->field_7 = r1
    //     0x14db628: stur            w1, [x3, #7]
    // 0x14db62c: r1 = Null
    //     0x14db62c: mov             x1, NULL
    // 0x14db630: r2 = 4
    //     0x14db630: movz            x2, #0x4
    // 0x14db634: r0 = AllocateArray()
    //     0x14db634: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14db638: mov             x2, x0
    // 0x14db63c: ldur            x0, [fp, #-0x30]
    // 0x14db640: stur            x2, [fp, #-0x28]
    // 0x14db644: StoreField: r2->field_f = r0
    //     0x14db644: stur            w0, [x2, #0xf]
    // 0x14db648: ldur            x0, [fp, #-0x40]
    // 0x14db64c: StoreField: r2->field_13 = r0
    //     0x14db64c: stur            w0, [x2, #0x13]
    // 0x14db650: r1 = <InlineSpan>
    //     0x14db650: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14db654: ldr             x1, [x1, #0xe40]
    // 0x14db658: r0 = AllocateGrowableArray()
    //     0x14db658: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14db65c: mov             x1, x0
    // 0x14db660: ldur            x0, [fp, #-0x28]
    // 0x14db664: stur            x1, [fp, #-0x30]
    // 0x14db668: StoreField: r1->field_f = r0
    //     0x14db668: stur            w0, [x1, #0xf]
    // 0x14db66c: r2 = 4
    //     0x14db66c: movz            x2, #0x4
    // 0x14db670: StoreField: r1->field_b = r2
    //     0x14db670: stur            w2, [x1, #0xb]
    // 0x14db674: r0 = TextSpan()
    //     0x14db674: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db678: mov             x1, x0
    // 0x14db67c: ldur            x0, [fp, #-0x30]
    // 0x14db680: stur            x1, [fp, #-0x28]
    // 0x14db684: StoreField: r1->field_f = r0
    //     0x14db684: stur            w0, [x1, #0xf]
    // 0x14db688: r0 = Instance__DeferringMouseCursor
    //     0x14db688: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db68c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14db68c: stur            w0, [x1, #0x17]
    // 0x14db690: r0 = RichText()
    //     0x14db690: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14db694: mov             x1, x0
    // 0x14db698: ldur            x2, [fp, #-0x28]
    // 0x14db69c: stur            x0, [fp, #-0x28]
    // 0x14db6a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14db6a0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14db6a4: r0 = RichText()
    //     0x14db6a4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14db6a8: r0 = Visibility()
    //     0x14db6a8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14db6ac: mov             x1, x0
    // 0x14db6b0: ldur            x0, [fp, #-0x28]
    // 0x14db6b4: StoreField: r1->field_b = r0
    //     0x14db6b4: stur            w0, [x1, #0xb]
    // 0x14db6b8: r2 = Instance_SizedBox
    //     0x14db6b8: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14db6bc: StoreField: r1->field_f = r2
    //     0x14db6bc: stur            w2, [x1, #0xf]
    // 0x14db6c0: ldur            x0, [fp, #-0x20]
    // 0x14db6c4: StoreField: r1->field_13 = r0
    //     0x14db6c4: stur            w0, [x1, #0x13]
    // 0x14db6c8: r3 = false
    //     0x14db6c8: add             x3, NULL, #0x30  ; false
    // 0x14db6cc: ArrayStore: r1[0] = r3  ; List_4
    //     0x14db6cc: stur            w3, [x1, #0x17]
    // 0x14db6d0: StoreField: r1->field_1b = r3
    //     0x14db6d0: stur            w3, [x1, #0x1b]
    // 0x14db6d4: StoreField: r1->field_1f = r3
    //     0x14db6d4: stur            w3, [x1, #0x1f]
    // 0x14db6d8: StoreField: r1->field_23 = r3
    //     0x14db6d8: stur            w3, [x1, #0x23]
    // 0x14db6dc: StoreField: r1->field_27 = r3
    //     0x14db6dc: stur            w3, [x1, #0x27]
    // 0x14db6e0: StoreField: r1->field_2b = r3
    //     0x14db6e0: stur            w3, [x1, #0x2b]
    // 0x14db6e4: mov             x0, x1
    // 0x14db6e8: ldur            x1, [fp, #-0x18]
    // 0x14db6ec: ArrayStore: r1[4] = r0  ; List_4
    //     0x14db6ec: add             x25, x1, #0x1f
    //     0x14db6f0: str             w0, [x25]
    //     0x14db6f4: tbz             w0, #0, #0x14db710
    //     0x14db6f8: ldurb           w16, [x1, #-1]
    //     0x14db6fc: ldurb           w17, [x0, #-1]
    //     0x14db700: and             x16, x17, x16, lsr #2
    //     0x14db704: tst             x16, HEAP, lsr #32
    //     0x14db708: b.eq            #0x14db710
    //     0x14db70c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14db710: ldur            x0, [fp, #-0x18]
    // 0x14db714: r16 = Instance_SizedBox
    //     0x14db714: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14db718: ldr             x16, [x16, #0x9f0]
    // 0x14db71c: StoreField: r0->field_23 = r16
    //     0x14db71c: stur            w16, [x0, #0x23]
    // 0x14db720: ldur            x1, [fp, #-8]
    // 0x14db724: r0 = controller()
    //     0x14db724: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db728: LoadField: r1 = r0->field_4b
    //     0x14db728: ldur            w1, [x0, #0x4b]
    // 0x14db72c: DecompressPointer r1
    //     0x14db72c: add             x1, x1, HEAP, lsl #32
    // 0x14db730: r0 = value()
    //     0x14db730: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db734: LoadField: r1 = r0->field_5f
    //     0x14db734: ldur            w1, [x0, #0x5f]
    // 0x14db738: DecompressPointer r1
    //     0x14db738: add             x1, x1, HEAP, lsl #32
    // 0x14db73c: cmp             w1, NULL
    // 0x14db740: b.ne            #0x14db74c
    // 0x14db744: r0 = Null
    //     0x14db744: mov             x0, NULL
    // 0x14db748: b               #0x14db754
    // 0x14db74c: LoadField: r0 = r1->field_f
    //     0x14db74c: ldur            w0, [x1, #0xf]
    // 0x14db750: DecompressPointer r0
    //     0x14db750: add             x0, x0, HEAP, lsl #32
    // 0x14db754: cmp             w0, NULL
    // 0x14db758: r16 = true
    //     0x14db758: add             x16, NULL, #0x20  ; true
    // 0x14db75c: r17 = false
    //     0x14db75c: add             x17, NULL, #0x30  ; false
    // 0x14db760: csel            x2, x16, x17, ne
    // 0x14db764: ldur            x1, [fp, #-0x10]
    // 0x14db768: stur            x2, [fp, #-0x20]
    // 0x14db76c: r0 = of()
    //     0x14db76c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db770: LoadField: r1 = r0->field_87
    //     0x14db770: ldur            w1, [x0, #0x87]
    // 0x14db774: DecompressPointer r1
    //     0x14db774: add             x1, x1, HEAP, lsl #32
    // 0x14db778: LoadField: r0 = r1->field_2b
    //     0x14db778: ldur            w0, [x1, #0x2b]
    // 0x14db77c: DecompressPointer r0
    //     0x14db77c: add             x0, x0, HEAP, lsl #32
    // 0x14db780: r16 = Instance_Color
    //     0x14db780: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db784: r30 = 14.000000
    //     0x14db784: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db788: ldr             lr, [lr, #0x1d8]
    // 0x14db78c: stp             lr, x16, [SP]
    // 0x14db790: mov             x1, x0
    // 0x14db794: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db794: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db798: ldr             x4, [x4, #0x9b8]
    // 0x14db79c: r0 = copyWith()
    //     0x14db79c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db7a0: stur            x0, [fp, #-0x28]
    // 0x14db7a4: r0 = TextSpan()
    //     0x14db7a4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db7a8: mov             x2, x0
    // 0x14db7ac: r0 = "Email: "
    //     0x14db7ac: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb40] "Email: "
    //     0x14db7b0: ldr             x0, [x0, #0xb40]
    // 0x14db7b4: stur            x2, [fp, #-0x30]
    // 0x14db7b8: StoreField: r2->field_b = r0
    //     0x14db7b8: stur            w0, [x2, #0xb]
    // 0x14db7bc: r0 = Instance__DeferringMouseCursor
    //     0x14db7bc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db7c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14db7c0: stur            w0, [x2, #0x17]
    // 0x14db7c4: ldur            x1, [fp, #-0x28]
    // 0x14db7c8: StoreField: r2->field_7 = r1
    //     0x14db7c8: stur            w1, [x2, #7]
    // 0x14db7cc: ldur            x1, [fp, #-8]
    // 0x14db7d0: r0 = controller()
    //     0x14db7d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db7d4: LoadField: r1 = r0->field_4b
    //     0x14db7d4: ldur            w1, [x0, #0x4b]
    // 0x14db7d8: DecompressPointer r1
    //     0x14db7d8: add             x1, x1, HEAP, lsl #32
    // 0x14db7dc: r0 = value()
    //     0x14db7dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db7e0: LoadField: r1 = r0->field_5f
    //     0x14db7e0: ldur            w1, [x0, #0x5f]
    // 0x14db7e4: DecompressPointer r1
    //     0x14db7e4: add             x1, x1, HEAP, lsl #32
    // 0x14db7e8: cmp             w1, NULL
    // 0x14db7ec: b.ne            #0x14db7f8
    // 0x14db7f0: r3 = Null
    //     0x14db7f0: mov             x3, NULL
    // 0x14db7f4: b               #0x14db804
    // 0x14db7f8: LoadField: r0 = r1->field_f
    //     0x14db7f8: ldur            w0, [x1, #0xf]
    // 0x14db7fc: DecompressPointer r0
    //     0x14db7fc: add             x0, x0, HEAP, lsl #32
    // 0x14db800: mov             x3, x0
    // 0x14db804: ldur            x2, [fp, #-0x18]
    // 0x14db808: ldur            x1, [fp, #-0x20]
    // 0x14db80c: ldur            x0, [fp, #-0x30]
    // 0x14db810: str             x3, [SP]
    // 0x14db814: r0 = _interpolateSingle()
    //     0x14db814: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14db818: ldur            x1, [fp, #-0x10]
    // 0x14db81c: stur            x0, [fp, #-0x28]
    // 0x14db820: r0 = of()
    //     0x14db820: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db824: LoadField: r1 = r0->field_87
    //     0x14db824: ldur            w1, [x0, #0x87]
    // 0x14db828: DecompressPointer r1
    //     0x14db828: add             x1, x1, HEAP, lsl #32
    // 0x14db82c: LoadField: r0 = r1->field_2b
    //     0x14db82c: ldur            w0, [x1, #0x2b]
    // 0x14db830: DecompressPointer r0
    //     0x14db830: add             x0, x0, HEAP, lsl #32
    // 0x14db834: r16 = Instance_Color
    //     0x14db834: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db838: r30 = 14.000000
    //     0x14db838: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db83c: ldr             lr, [lr, #0x1d8]
    // 0x14db840: stp             lr, x16, [SP]
    // 0x14db844: mov             x1, x0
    // 0x14db848: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db848: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db84c: ldr             x4, [x4, #0x9b8]
    // 0x14db850: r0 = copyWith()
    //     0x14db850: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db854: stur            x0, [fp, #-0x38]
    // 0x14db858: r0 = TextSpan()
    //     0x14db858: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db85c: mov             x3, x0
    // 0x14db860: ldur            x0, [fp, #-0x28]
    // 0x14db864: stur            x3, [fp, #-0x40]
    // 0x14db868: StoreField: r3->field_b = r0
    //     0x14db868: stur            w0, [x3, #0xb]
    // 0x14db86c: r0 = Instance__DeferringMouseCursor
    //     0x14db86c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db870: ArrayStore: r3[0] = r0  ; List_4
    //     0x14db870: stur            w0, [x3, #0x17]
    // 0x14db874: ldur            x1, [fp, #-0x38]
    // 0x14db878: StoreField: r3->field_7 = r1
    //     0x14db878: stur            w1, [x3, #7]
    // 0x14db87c: r1 = Null
    //     0x14db87c: mov             x1, NULL
    // 0x14db880: r2 = 4
    //     0x14db880: movz            x2, #0x4
    // 0x14db884: r0 = AllocateArray()
    //     0x14db884: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14db888: mov             x2, x0
    // 0x14db88c: ldur            x0, [fp, #-0x30]
    // 0x14db890: stur            x2, [fp, #-0x28]
    // 0x14db894: StoreField: r2->field_f = r0
    //     0x14db894: stur            w0, [x2, #0xf]
    // 0x14db898: ldur            x0, [fp, #-0x40]
    // 0x14db89c: StoreField: r2->field_13 = r0
    //     0x14db89c: stur            w0, [x2, #0x13]
    // 0x14db8a0: r1 = <InlineSpan>
    //     0x14db8a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14db8a4: ldr             x1, [x1, #0xe40]
    // 0x14db8a8: r0 = AllocateGrowableArray()
    //     0x14db8a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14db8ac: mov             x1, x0
    // 0x14db8b0: ldur            x0, [fp, #-0x28]
    // 0x14db8b4: stur            x1, [fp, #-0x30]
    // 0x14db8b8: StoreField: r1->field_f = r0
    //     0x14db8b8: stur            w0, [x1, #0xf]
    // 0x14db8bc: r2 = 4
    //     0x14db8bc: movz            x2, #0x4
    // 0x14db8c0: StoreField: r1->field_b = r2
    //     0x14db8c0: stur            w2, [x1, #0xb]
    // 0x14db8c4: r0 = TextSpan()
    //     0x14db8c4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db8c8: mov             x1, x0
    // 0x14db8cc: ldur            x0, [fp, #-0x30]
    // 0x14db8d0: stur            x1, [fp, #-0x28]
    // 0x14db8d4: StoreField: r1->field_f = r0
    //     0x14db8d4: stur            w0, [x1, #0xf]
    // 0x14db8d8: r0 = Instance__DeferringMouseCursor
    //     0x14db8d8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14db8dc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14db8dc: stur            w0, [x1, #0x17]
    // 0x14db8e0: r0 = RichText()
    //     0x14db8e0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14db8e4: mov             x1, x0
    // 0x14db8e8: ldur            x2, [fp, #-0x28]
    // 0x14db8ec: stur            x0, [fp, #-0x28]
    // 0x14db8f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14db8f0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14db8f4: r0 = RichText()
    //     0x14db8f4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14db8f8: r0 = Visibility()
    //     0x14db8f8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14db8fc: mov             x1, x0
    // 0x14db900: ldur            x0, [fp, #-0x28]
    // 0x14db904: StoreField: r1->field_b = r0
    //     0x14db904: stur            w0, [x1, #0xb]
    // 0x14db908: r2 = Instance_SizedBox
    //     0x14db908: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14db90c: StoreField: r1->field_f = r2
    //     0x14db90c: stur            w2, [x1, #0xf]
    // 0x14db910: ldur            x0, [fp, #-0x20]
    // 0x14db914: StoreField: r1->field_13 = r0
    //     0x14db914: stur            w0, [x1, #0x13]
    // 0x14db918: r3 = false
    //     0x14db918: add             x3, NULL, #0x30  ; false
    // 0x14db91c: ArrayStore: r1[0] = r3  ; List_4
    //     0x14db91c: stur            w3, [x1, #0x17]
    // 0x14db920: StoreField: r1->field_1b = r3
    //     0x14db920: stur            w3, [x1, #0x1b]
    // 0x14db924: StoreField: r1->field_1f = r3
    //     0x14db924: stur            w3, [x1, #0x1f]
    // 0x14db928: StoreField: r1->field_23 = r3
    //     0x14db928: stur            w3, [x1, #0x23]
    // 0x14db92c: StoreField: r1->field_27 = r3
    //     0x14db92c: stur            w3, [x1, #0x27]
    // 0x14db930: StoreField: r1->field_2b = r3
    //     0x14db930: stur            w3, [x1, #0x2b]
    // 0x14db934: mov             x0, x1
    // 0x14db938: ldur            x1, [fp, #-0x18]
    // 0x14db93c: ArrayStore: r1[6] = r0  ; List_4
    //     0x14db93c: add             x25, x1, #0x27
    //     0x14db940: str             w0, [x25]
    //     0x14db944: tbz             w0, #0, #0x14db960
    //     0x14db948: ldurb           w16, [x1, #-1]
    //     0x14db94c: ldurb           w17, [x0, #-1]
    //     0x14db950: and             x16, x17, x16, lsr #2
    //     0x14db954: tst             x16, HEAP, lsr #32
    //     0x14db958: b.eq            #0x14db960
    //     0x14db95c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14db960: ldur            x0, [fp, #-0x18]
    // 0x14db964: r16 = Instance_SizedBox
    //     0x14db964: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14db968: ldr             x16, [x16, #0x9f0]
    // 0x14db96c: StoreField: r0->field_2b = r16
    //     0x14db96c: stur            w16, [x0, #0x2b]
    // 0x14db970: ldur            x1, [fp, #-8]
    // 0x14db974: r0 = controller()
    //     0x14db974: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14db978: LoadField: r1 = r0->field_4b
    //     0x14db978: ldur            w1, [x0, #0x4b]
    // 0x14db97c: DecompressPointer r1
    //     0x14db97c: add             x1, x1, HEAP, lsl #32
    // 0x14db980: r0 = value()
    //     0x14db980: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14db984: LoadField: r1 = r0->field_5f
    //     0x14db984: ldur            w1, [x0, #0x5f]
    // 0x14db988: DecompressPointer r1
    //     0x14db988: add             x1, x1, HEAP, lsl #32
    // 0x14db98c: cmp             w1, NULL
    // 0x14db990: b.ne            #0x14db99c
    // 0x14db994: r0 = Null
    //     0x14db994: mov             x0, NULL
    // 0x14db998: b               #0x14db9a4
    // 0x14db99c: LoadField: r0 = r1->field_13
    //     0x14db99c: ldur            w0, [x1, #0x13]
    // 0x14db9a0: DecompressPointer r0
    //     0x14db9a0: add             x0, x0, HEAP, lsl #32
    // 0x14db9a4: cmp             w0, NULL
    // 0x14db9a8: r16 = true
    //     0x14db9a8: add             x16, NULL, #0x20  ; true
    // 0x14db9ac: r17 = false
    //     0x14db9ac: add             x17, NULL, #0x30  ; false
    // 0x14db9b0: csel            x2, x16, x17, ne
    // 0x14db9b4: ldur            x1, [fp, #-0x10]
    // 0x14db9b8: stur            x2, [fp, #-0x20]
    // 0x14db9bc: r0 = of()
    //     0x14db9bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14db9c0: LoadField: r1 = r0->field_87
    //     0x14db9c0: ldur            w1, [x0, #0x87]
    // 0x14db9c4: DecompressPointer r1
    //     0x14db9c4: add             x1, x1, HEAP, lsl #32
    // 0x14db9c8: LoadField: r0 = r1->field_2b
    //     0x14db9c8: ldur            w0, [x1, #0x2b]
    // 0x14db9cc: DecompressPointer r0
    //     0x14db9cc: add             x0, x0, HEAP, lsl #32
    // 0x14db9d0: r16 = Instance_Color
    //     0x14db9d0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14db9d4: r30 = 14.000000
    //     0x14db9d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14db9d8: ldr             lr, [lr, #0x1d8]
    // 0x14db9dc: stp             lr, x16, [SP]
    // 0x14db9e0: mov             x1, x0
    // 0x14db9e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14db9e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14db9e8: ldr             x4, [x4, #0x9b8]
    // 0x14db9ec: r0 = copyWith()
    //     0x14db9ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14db9f0: stur            x0, [fp, #-0x28]
    // 0x14db9f4: r0 = TextSpan()
    //     0x14db9f4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14db9f8: mov             x2, x0
    // 0x14db9fc: r0 = "Address: "
    //     0x14db9fc: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bb48] "Address: "
    //     0x14dba00: ldr             x0, [x0, #0xb48]
    // 0x14dba04: stur            x2, [fp, #-0x30]
    // 0x14dba08: StoreField: r2->field_b = r0
    //     0x14dba08: stur            w0, [x2, #0xb]
    // 0x14dba0c: r0 = Instance__DeferringMouseCursor
    //     0x14dba0c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14dba10: ArrayStore: r2[0] = r0  ; List_4
    //     0x14dba10: stur            w0, [x2, #0x17]
    // 0x14dba14: ldur            x1, [fp, #-0x28]
    // 0x14dba18: StoreField: r2->field_7 = r1
    //     0x14dba18: stur            w1, [x2, #7]
    // 0x14dba1c: ldur            x1, [fp, #-8]
    // 0x14dba20: r0 = controller()
    //     0x14dba20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dba24: LoadField: r1 = r0->field_4b
    //     0x14dba24: ldur            w1, [x0, #0x4b]
    // 0x14dba28: DecompressPointer r1
    //     0x14dba28: add             x1, x1, HEAP, lsl #32
    // 0x14dba2c: r0 = value()
    //     0x14dba2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dba30: LoadField: r1 = r0->field_5f
    //     0x14dba30: ldur            w1, [x0, #0x5f]
    // 0x14dba34: DecompressPointer r1
    //     0x14dba34: add             x1, x1, HEAP, lsl #32
    // 0x14dba38: cmp             w1, NULL
    // 0x14dba3c: b.ne            #0x14dba48
    // 0x14dba40: r3 = Null
    //     0x14dba40: mov             x3, NULL
    // 0x14dba44: b               #0x14dba54
    // 0x14dba48: LoadField: r0 = r1->field_13
    //     0x14dba48: ldur            w0, [x1, #0x13]
    // 0x14dba4c: DecompressPointer r0
    //     0x14dba4c: add             x0, x0, HEAP, lsl #32
    // 0x14dba50: mov             x3, x0
    // 0x14dba54: ldur            x2, [fp, #-0x18]
    // 0x14dba58: ldur            x1, [fp, #-0x20]
    // 0x14dba5c: ldur            x0, [fp, #-0x30]
    // 0x14dba60: str             x3, [SP]
    // 0x14dba64: r0 = _interpolateSingle()
    //     0x14dba64: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14dba68: ldur            x1, [fp, #-0x10]
    // 0x14dba6c: stur            x0, [fp, #-8]
    // 0x14dba70: r0 = of()
    //     0x14dba70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14dba74: LoadField: r1 = r0->field_87
    //     0x14dba74: ldur            w1, [x0, #0x87]
    // 0x14dba78: DecompressPointer r1
    //     0x14dba78: add             x1, x1, HEAP, lsl #32
    // 0x14dba7c: LoadField: r0 = r1->field_2b
    //     0x14dba7c: ldur            w0, [x1, #0x2b]
    // 0x14dba80: DecompressPointer r0
    //     0x14dba80: add             x0, x0, HEAP, lsl #32
    // 0x14dba84: r16 = Instance_Color
    //     0x14dba84: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14dba88: r30 = 14.000000
    //     0x14dba88: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14dba8c: ldr             lr, [lr, #0x1d8]
    // 0x14dba90: stp             lr, x16, [SP]
    // 0x14dba94: mov             x1, x0
    // 0x14dba98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14dba98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14dba9c: ldr             x4, [x4, #0x9b8]
    // 0x14dbaa0: r0 = copyWith()
    //     0x14dbaa0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14dbaa4: stur            x0, [fp, #-0x10]
    // 0x14dbaa8: r0 = TextSpan()
    //     0x14dbaa8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14dbaac: mov             x3, x0
    // 0x14dbab0: ldur            x0, [fp, #-8]
    // 0x14dbab4: stur            x3, [fp, #-0x28]
    // 0x14dbab8: StoreField: r3->field_b = r0
    //     0x14dbab8: stur            w0, [x3, #0xb]
    // 0x14dbabc: r0 = Instance__DeferringMouseCursor
    //     0x14dbabc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14dbac0: ArrayStore: r3[0] = r0  ; List_4
    //     0x14dbac0: stur            w0, [x3, #0x17]
    // 0x14dbac4: ldur            x1, [fp, #-0x10]
    // 0x14dbac8: StoreField: r3->field_7 = r1
    //     0x14dbac8: stur            w1, [x3, #7]
    // 0x14dbacc: r1 = Null
    //     0x14dbacc: mov             x1, NULL
    // 0x14dbad0: r2 = 4
    //     0x14dbad0: movz            x2, #0x4
    // 0x14dbad4: r0 = AllocateArray()
    //     0x14dbad4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14dbad8: mov             x2, x0
    // 0x14dbadc: ldur            x0, [fp, #-0x30]
    // 0x14dbae0: stur            x2, [fp, #-8]
    // 0x14dbae4: StoreField: r2->field_f = r0
    //     0x14dbae4: stur            w0, [x2, #0xf]
    // 0x14dbae8: ldur            x0, [fp, #-0x28]
    // 0x14dbaec: StoreField: r2->field_13 = r0
    //     0x14dbaec: stur            w0, [x2, #0x13]
    // 0x14dbaf0: r1 = <InlineSpan>
    //     0x14dbaf0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14dbaf4: ldr             x1, [x1, #0xe40]
    // 0x14dbaf8: r0 = AllocateGrowableArray()
    //     0x14dbaf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dbafc: mov             x1, x0
    // 0x14dbb00: ldur            x0, [fp, #-8]
    // 0x14dbb04: stur            x1, [fp, #-0x10]
    // 0x14dbb08: StoreField: r1->field_f = r0
    //     0x14dbb08: stur            w0, [x1, #0xf]
    // 0x14dbb0c: r0 = 4
    //     0x14dbb0c: movz            x0, #0x4
    // 0x14dbb10: StoreField: r1->field_b = r0
    //     0x14dbb10: stur            w0, [x1, #0xb]
    // 0x14dbb14: r0 = TextSpan()
    //     0x14dbb14: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14dbb18: mov             x1, x0
    // 0x14dbb1c: ldur            x0, [fp, #-0x10]
    // 0x14dbb20: stur            x1, [fp, #-8]
    // 0x14dbb24: StoreField: r1->field_f = r0
    //     0x14dbb24: stur            w0, [x1, #0xf]
    // 0x14dbb28: r0 = Instance__DeferringMouseCursor
    //     0x14dbb28: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14dbb2c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dbb2c: stur            w0, [x1, #0x17]
    // 0x14dbb30: r0 = RichText()
    //     0x14dbb30: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14dbb34: mov             x1, x0
    // 0x14dbb38: ldur            x2, [fp, #-8]
    // 0x14dbb3c: stur            x0, [fp, #-8]
    // 0x14dbb40: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14dbb40: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14dbb44: r0 = RichText()
    //     0x14dbb44: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14dbb48: r0 = Visibility()
    //     0x14dbb48: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14dbb4c: mov             x1, x0
    // 0x14dbb50: ldur            x0, [fp, #-8]
    // 0x14dbb54: StoreField: r1->field_b = r0
    //     0x14dbb54: stur            w0, [x1, #0xb]
    // 0x14dbb58: r0 = Instance_SizedBox
    //     0x14dbb58: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14dbb5c: StoreField: r1->field_f = r0
    //     0x14dbb5c: stur            w0, [x1, #0xf]
    // 0x14dbb60: ldur            x0, [fp, #-0x20]
    // 0x14dbb64: StoreField: r1->field_13 = r0
    //     0x14dbb64: stur            w0, [x1, #0x13]
    // 0x14dbb68: r0 = false
    //     0x14dbb68: add             x0, NULL, #0x30  ; false
    // 0x14dbb6c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dbb6c: stur            w0, [x1, #0x17]
    // 0x14dbb70: StoreField: r1->field_1b = r0
    //     0x14dbb70: stur            w0, [x1, #0x1b]
    // 0x14dbb74: StoreField: r1->field_1f = r0
    //     0x14dbb74: stur            w0, [x1, #0x1f]
    // 0x14dbb78: StoreField: r1->field_23 = r0
    //     0x14dbb78: stur            w0, [x1, #0x23]
    // 0x14dbb7c: StoreField: r1->field_27 = r0
    //     0x14dbb7c: stur            w0, [x1, #0x27]
    // 0x14dbb80: StoreField: r1->field_2b = r0
    //     0x14dbb80: stur            w0, [x1, #0x2b]
    // 0x14dbb84: mov             x0, x1
    // 0x14dbb88: ldur            x1, [fp, #-0x18]
    // 0x14dbb8c: ArrayStore: r1[8] = r0  ; List_4
    //     0x14dbb8c: add             x25, x1, #0x2f
    //     0x14dbb90: str             w0, [x25]
    //     0x14dbb94: tbz             w0, #0, #0x14dbbb0
    //     0x14dbb98: ldurb           w16, [x1, #-1]
    //     0x14dbb9c: ldurb           w17, [x0, #-1]
    //     0x14dbba0: and             x16, x17, x16, lsr #2
    //     0x14dbba4: tst             x16, HEAP, lsr #32
    //     0x14dbba8: b.eq            #0x14dbbb0
    //     0x14dbbac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dbbb0: r1 = <Widget>
    //     0x14dbbb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14dbbb4: r0 = AllocateGrowableArray()
    //     0x14dbbb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14dbbb8: mov             x1, x0
    // 0x14dbbbc: ldur            x0, [fp, #-0x18]
    // 0x14dbbc0: stur            x1, [fp, #-8]
    // 0x14dbbc4: StoreField: r1->field_f = r0
    //     0x14dbbc4: stur            w0, [x1, #0xf]
    // 0x14dbbc8: r0 = 18
    //     0x14dbbc8: movz            x0, #0x12
    // 0x14dbbcc: StoreField: r1->field_b = r0
    //     0x14dbbcc: stur            w0, [x1, #0xb]
    // 0x14dbbd0: r0 = Column()
    //     0x14dbbd0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14dbbd4: mov             x1, x0
    // 0x14dbbd8: r0 = Instance_Axis
    //     0x14dbbd8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14dbbdc: stur            x1, [fp, #-0x10]
    // 0x14dbbe0: StoreField: r1->field_f = r0
    //     0x14dbbe0: stur            w0, [x1, #0xf]
    // 0x14dbbe4: r0 = Instance_MainAxisAlignment
    //     0x14dbbe4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14dbbe8: ldr             x0, [x0, #0xa08]
    // 0x14dbbec: StoreField: r1->field_13 = r0
    //     0x14dbbec: stur            w0, [x1, #0x13]
    // 0x14dbbf0: r0 = Instance_MainAxisSize
    //     0x14dbbf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14dbbf4: ldr             x0, [x0, #0xa10]
    // 0x14dbbf8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14dbbf8: stur            w0, [x1, #0x17]
    // 0x14dbbfc: r0 = Instance_CrossAxisAlignment
    //     0x14dbbfc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14dbc00: ldr             x0, [x0, #0x890]
    // 0x14dbc04: StoreField: r1->field_1b = r0
    //     0x14dbc04: stur            w0, [x1, #0x1b]
    // 0x14dbc08: r0 = Instance_VerticalDirection
    //     0x14dbc08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14dbc0c: ldr             x0, [x0, #0xa20]
    // 0x14dbc10: StoreField: r1->field_23 = r0
    //     0x14dbc10: stur            w0, [x1, #0x23]
    // 0x14dbc14: r0 = Instance_Clip
    //     0x14dbc14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14dbc18: ldr             x0, [x0, #0x38]
    // 0x14dbc1c: StoreField: r1->field_2b = r0
    //     0x14dbc1c: stur            w0, [x1, #0x2b]
    // 0x14dbc20: StoreField: r1->field_2f = rZR
    //     0x14dbc20: stur            xzr, [x1, #0x2f]
    // 0x14dbc24: ldur            x0, [fp, #-8]
    // 0x14dbc28: StoreField: r1->field_b = r0
    //     0x14dbc28: stur            w0, [x1, #0xb]
    // 0x14dbc2c: r0 = Padding()
    //     0x14dbc2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14dbc30: r1 = Instance_EdgeInsets
    //     0x14dbc30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14dbc34: ldr             x1, [x1, #0x1f0]
    // 0x14dbc38: StoreField: r0->field_f = r1
    //     0x14dbc38: stur            w1, [x0, #0xf]
    // 0x14dbc3c: ldur            x1, [fp, #-0x10]
    // 0x14dbc40: StoreField: r0->field_b = r1
    //     0x14dbc40: stur            w1, [x0, #0xb]
    // 0x14dbc44: LeaveFrame
    //     0x14dbc44: mov             SP, fp
    //     0x14dbc48: ldp             fp, lr, [SP], #0x10
    // 0x14dbc4c: ret
    //     0x14dbc4c: ret             
    // 0x14dbc50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14dbc50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14dbc54: b               #0x14db044
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e175c, size: 0x19c
    // 0x15e175c: EnterFrame
    //     0x15e175c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e1760: mov             fp, SP
    // 0x15e1764: AllocStack(0x28)
    //     0x15e1764: sub             SP, SP, #0x28
    // 0x15e1768: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x15e1768: mov             x0, x2
    //     0x15e176c: stur            x2, [fp, #-8]
    // 0x15e1770: CheckStackOverflow
    //     0x15e1770: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1774: cmp             SP, x16
    //     0x15e1778: b.ls            #0x15e18f0
    // 0x15e177c: mov             x1, x0
    // 0x15e1780: r0 = of()
    //     0x15e1780: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e1784: LoadField: r1 = r0->field_87
    //     0x15e1784: ldur            w1, [x0, #0x87]
    // 0x15e1788: DecompressPointer r1
    //     0x15e1788: add             x1, x1, HEAP, lsl #32
    // 0x15e178c: LoadField: r0 = r1->field_27
    //     0x15e178c: ldur            w0, [x1, #0x27]
    // 0x15e1790: DecompressPointer r0
    //     0x15e1790: add             x0, x0, HEAP, lsl #32
    // 0x15e1794: r16 = Instance_Color
    //     0x15e1794: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e1798: r30 = 21.000000
    //     0x15e1798: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x15e179c: ldr             lr, [lr, #0x9b0]
    // 0x15e17a0: stp             lr, x16, [SP]
    // 0x15e17a4: mov             x1, x0
    // 0x15e17a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15e17a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15e17ac: ldr             x4, [x4, #0x9b8]
    // 0x15e17b0: r0 = copyWith()
    //     0x15e17b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e17b4: stur            x0, [fp, #-0x10]
    // 0x15e17b8: r0 = Text()
    //     0x15e17b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e17bc: mov             x2, x0
    // 0x15e17c0: r0 = "Contact Us"
    //     0x15e17c0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ca8] "Contact Us"
    //     0x15e17c4: ldr             x0, [x0, #0xca8]
    // 0x15e17c8: stur            x2, [fp, #-0x18]
    // 0x15e17cc: StoreField: r2->field_b = r0
    //     0x15e17cc: stur            w0, [x2, #0xb]
    // 0x15e17d0: ldur            x0, [fp, #-0x10]
    // 0x15e17d4: StoreField: r2->field_13 = r0
    //     0x15e17d4: stur            w0, [x2, #0x13]
    // 0x15e17d8: ldur            x1, [fp, #-8]
    // 0x15e17dc: r0 = of()
    //     0x15e17dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e17e0: LoadField: r1 = r0->field_5b
    //     0x15e17e0: ldur            w1, [x0, #0x5b]
    // 0x15e17e4: DecompressPointer r1
    //     0x15e17e4: add             x1, x1, HEAP, lsl #32
    // 0x15e17e8: stur            x1, [fp, #-8]
    // 0x15e17ec: r0 = ColorFilter()
    //     0x15e17ec: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e17f0: mov             x1, x0
    // 0x15e17f4: ldur            x0, [fp, #-8]
    // 0x15e17f8: stur            x1, [fp, #-0x10]
    // 0x15e17fc: StoreField: r1->field_7 = r0
    //     0x15e17fc: stur            w0, [x1, #7]
    // 0x15e1800: r0 = Instance_BlendMode
    //     0x15e1800: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e1804: ldr             x0, [x0, #0xb30]
    // 0x15e1808: StoreField: r1->field_b = r0
    //     0x15e1808: stur            w0, [x1, #0xb]
    // 0x15e180c: r0 = 1
    //     0x15e180c: movz            x0, #0x1
    // 0x15e1810: StoreField: r1->field_13 = r0
    //     0x15e1810: stur            x0, [x1, #0x13]
    // 0x15e1814: r0 = SvgPicture()
    //     0x15e1814: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1818: stur            x0, [fp, #-8]
    // 0x15e181c: ldur            x16, [fp, #-0x10]
    // 0x15e1820: str             x16, [SP]
    // 0x15e1824: mov             x1, x0
    // 0x15e1828: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e1828: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e182c: ldr             x2, [x2, #0xa40]
    // 0x15e1830: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e1830: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e1834: ldr             x4, [x4, #0xa38]
    // 0x15e1838: r0 = SvgPicture.asset()
    //     0x15e1838: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e183c: r0 = Align()
    //     0x15e183c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e1840: mov             x1, x0
    // 0x15e1844: r0 = Instance_Alignment
    //     0x15e1844: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1848: ldr             x0, [x0, #0xb10]
    // 0x15e184c: stur            x1, [fp, #-0x10]
    // 0x15e1850: StoreField: r1->field_f = r0
    //     0x15e1850: stur            w0, [x1, #0xf]
    // 0x15e1854: r0 = 1.000000
    //     0x15e1854: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e1858: StoreField: r1->field_13 = r0
    //     0x15e1858: stur            w0, [x1, #0x13]
    // 0x15e185c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e185c: stur            w0, [x1, #0x17]
    // 0x15e1860: ldur            x0, [fp, #-8]
    // 0x15e1864: StoreField: r1->field_b = r0
    //     0x15e1864: stur            w0, [x1, #0xb]
    // 0x15e1868: r0 = InkWell()
    //     0x15e1868: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e186c: mov             x3, x0
    // 0x15e1870: ldur            x0, [fp, #-0x10]
    // 0x15e1874: stur            x3, [fp, #-8]
    // 0x15e1878: StoreField: r3->field_b = r0
    //     0x15e1878: stur            w0, [x3, #0xb]
    // 0x15e187c: r1 = Function '<anonymous closure>':.
    //     0x15e187c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b70] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e1880: ldr             x1, [x1, #0xb70]
    // 0x15e1884: r2 = Null
    //     0x15e1884: mov             x2, NULL
    // 0x15e1888: r0 = AllocateClosure()
    //     0x15e1888: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e188c: ldur            x2, [fp, #-8]
    // 0x15e1890: StoreField: r2->field_f = r0
    //     0x15e1890: stur            w0, [x2, #0xf]
    // 0x15e1894: r0 = true
    //     0x15e1894: add             x0, NULL, #0x20  ; true
    // 0x15e1898: StoreField: r2->field_43 = r0
    //     0x15e1898: stur            w0, [x2, #0x43]
    // 0x15e189c: r1 = Instance_BoxShape
    //     0x15e189c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e18a0: ldr             x1, [x1, #0x80]
    // 0x15e18a4: StoreField: r2->field_47 = r1
    //     0x15e18a4: stur            w1, [x2, #0x47]
    // 0x15e18a8: StoreField: r2->field_6f = r0
    //     0x15e18a8: stur            w0, [x2, #0x6f]
    // 0x15e18ac: r1 = false
    //     0x15e18ac: add             x1, NULL, #0x30  ; false
    // 0x15e18b0: StoreField: r2->field_73 = r1
    //     0x15e18b0: stur            w1, [x2, #0x73]
    // 0x15e18b4: StoreField: r2->field_83 = r0
    //     0x15e18b4: stur            w0, [x2, #0x83]
    // 0x15e18b8: StoreField: r2->field_7b = r1
    //     0x15e18b8: stur            w1, [x2, #0x7b]
    // 0x15e18bc: r0 = AppBar()
    //     0x15e18bc: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e18c0: stur            x0, [fp, #-0x10]
    // 0x15e18c4: ldur            x16, [fp, #-0x18]
    // 0x15e18c8: str             x16, [SP]
    // 0x15e18cc: mov             x1, x0
    // 0x15e18d0: ldur            x2, [fp, #-8]
    // 0x15e18d4: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e18d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e18d8: ldr             x4, [x4, #0xf00]
    // 0x15e18dc: r0 = AppBar()
    //     0x15e18dc: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e18e0: ldur            x0, [fp, #-0x10]
    // 0x15e18e4: LeaveFrame
    //     0x15e18e4: mov             SP, fp
    //     0x15e18e8: ldp             fp, lr, [SP], #0x10
    // 0x15e18ec: ret
    //     0x15e18ec: ret             
    // 0x15e18f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e18f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e18f4: b               #0x15e177c
  }
}
