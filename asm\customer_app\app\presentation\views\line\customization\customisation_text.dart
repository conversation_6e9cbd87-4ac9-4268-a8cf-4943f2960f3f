// lib: , url: package:customer_app/app/presentation/views/line/customization/customisation_text.dart

// class id: 1049506, size: 0x8
class :: {
}

// class id: 3258, size: 0x1c, field offset: 0x14
class _CustomisationTextState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbd7ed0, size: 0x7b4
    // 0xbd7ed0: EnterFrame
    //     0xbd7ed0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7ed4: mov             fp, SP
    // 0xbd7ed8: AllocStack(0x88)
    //     0xbd7ed8: sub             SP, SP, #0x88
    // 0xbd7edc: SetupParameters(_CustomisationTextState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbd7edc: mov             x0, x1
    //     0xbd7ee0: stur            x1, [fp, #-8]
    //     0xbd7ee4: mov             x1, x2
    //     0xbd7ee8: stur            x2, [fp, #-0x10]
    // 0xbd7eec: CheckStackOverflow
    //     0xbd7eec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd7ef0: cmp             SP, x16
    //     0xbd7ef4: b.ls            #0xbd8670
    // 0xbd7ef8: r1 = 1
    //     0xbd7ef8: movz            x1, #0x1
    // 0xbd7efc: r0 = AllocateContext()
    //     0xbd7efc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd7f00: mov             x3, x0
    // 0xbd7f04: ldur            x0, [fp, #-8]
    // 0xbd7f08: stur            x3, [fp, #-0x20]
    // 0xbd7f0c: StoreField: r3->field_f = r0
    //     0xbd7f0c: stur            w0, [x3, #0xf]
    // 0xbd7f10: LoadField: r1 = r0->field_b
    //     0xbd7f10: ldur            w1, [x0, #0xb]
    // 0xbd7f14: DecompressPointer r1
    //     0xbd7f14: add             x1, x1, HEAP, lsl #32
    // 0xbd7f18: cmp             w1, NULL
    // 0xbd7f1c: b.eq            #0xbd8678
    // 0xbd7f20: LoadField: r2 = r1->field_b
    //     0xbd7f20: ldur            w2, [x1, #0xb]
    // 0xbd7f24: DecompressPointer r2
    //     0xbd7f24: add             x2, x2, HEAP, lsl #32
    // 0xbd7f28: cmp             w2, NULL
    // 0xbd7f2c: b.ne            #0xbd7f38
    // 0xbd7f30: r1 = Null
    //     0xbd7f30: mov             x1, NULL
    // 0xbd7f34: b               #0xbd7f40
    // 0xbd7f38: LoadField: r1 = r2->field_2b
    //     0xbd7f38: ldur            w1, [x2, #0x2b]
    // 0xbd7f3c: DecompressPointer r1
    //     0xbd7f3c: add             x1, x1, HEAP, lsl #32
    // 0xbd7f40: cmp             w1, NULL
    // 0xbd7f44: b.eq            #0xbd7fa0
    // 0xbd7f48: tbnz            w1, #4, #0xbd7fa0
    // 0xbd7f4c: cmp             w2, NULL
    // 0xbd7f50: b.ne            #0xbd7f5c
    // 0xbd7f54: r4 = Null
    //     0xbd7f54: mov             x4, NULL
    // 0xbd7f58: b               #0xbd7f68
    // 0xbd7f5c: LoadField: r1 = r2->field_1b
    //     0xbd7f5c: ldur            w1, [x2, #0x1b]
    // 0xbd7f60: DecompressPointer r1
    //     0xbd7f60: add             x1, x1, HEAP, lsl #32
    // 0xbd7f64: mov             x4, x1
    // 0xbd7f68: stur            x4, [fp, #-0x18]
    // 0xbd7f6c: r1 = Null
    //     0xbd7f6c: mov             x1, NULL
    // 0xbd7f70: r2 = 4
    //     0xbd7f70: movz            x2, #0x4
    // 0xbd7f74: r0 = AllocateArray()
    //     0xbd7f74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd7f78: mov             x1, x0
    // 0xbd7f7c: ldur            x0, [fp, #-0x18]
    // 0xbd7f80: StoreField: r1->field_f = r0
    //     0xbd7f80: stur            w0, [x1, #0xf]
    // 0xbd7f84: r16 = " *"
    //     0xbd7f84: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xbd7f88: ldr             x16, [x16, #0xfc8]
    // 0xbd7f8c: StoreField: r1->field_13 = r16
    //     0xbd7f8c: stur            w16, [x1, #0x13]
    // 0xbd7f90: str             x1, [SP]
    // 0xbd7f94: r0 = _interpolate()
    //     0xbd7f94: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd7f98: mov             x2, x0
    // 0xbd7f9c: b               #0xbd7fc4
    // 0xbd7fa0: cmp             w2, NULL
    // 0xbd7fa4: b.ne            #0xbd7fb0
    // 0xbd7fa8: r0 = Null
    //     0xbd7fa8: mov             x0, NULL
    // 0xbd7fac: b               #0xbd7fb8
    // 0xbd7fb0: LoadField: r0 = r2->field_1b
    //     0xbd7fb0: ldur            w0, [x2, #0x1b]
    // 0xbd7fb4: DecompressPointer r0
    //     0xbd7fb4: add             x0, x0, HEAP, lsl #32
    // 0xbd7fb8: str             x0, [SP]
    // 0xbd7fbc: r0 = _interpolateSingle()
    //     0xbd7fbc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbd7fc0: mov             x2, x0
    // 0xbd7fc4: ldur            x0, [fp, #-8]
    // 0xbd7fc8: ldur            x1, [fp, #-0x10]
    // 0xbd7fcc: stur            x2, [fp, #-0x18]
    // 0xbd7fd0: r0 = of()
    //     0xbd7fd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd7fd4: LoadField: r1 = r0->field_87
    //     0xbd7fd4: ldur            w1, [x0, #0x87]
    // 0xbd7fd8: DecompressPointer r1
    //     0xbd7fd8: add             x1, x1, HEAP, lsl #32
    // 0xbd7fdc: LoadField: r0 = r1->field_7
    //     0xbd7fdc: ldur            w0, [x1, #7]
    // 0xbd7fe0: DecompressPointer r0
    //     0xbd7fe0: add             x0, x0, HEAP, lsl #32
    // 0xbd7fe4: stur            x0, [fp, #-0x28]
    // 0xbd7fe8: r1 = Instance_Color
    //     0xbd7fe8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd7fec: d0 = 0.700000
    //     0xbd7fec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd7ff0: ldr             d0, [x17, #0xf48]
    // 0xbd7ff4: r0 = withOpacity()
    //     0xbd7ff4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd7ff8: r16 = 14.000000
    //     0xbd7ff8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd7ffc: ldr             x16, [x16, #0x1d8]
    // 0xbd8000: stp             x0, x16, [SP]
    // 0xbd8004: ldur            x1, [fp, #-0x28]
    // 0xbd8008: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd8008: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd800c: ldr             x4, [x4, #0xaa0]
    // 0xbd8010: r0 = copyWith()
    //     0xbd8010: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd8014: stur            x0, [fp, #-0x28]
    // 0xbd8018: r0 = Text()
    //     0xbd8018: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd801c: mov             x1, x0
    // 0xbd8020: ldur            x0, [fp, #-0x18]
    // 0xbd8024: stur            x1, [fp, #-0x30]
    // 0xbd8028: StoreField: r1->field_b = r0
    //     0xbd8028: stur            w0, [x1, #0xb]
    // 0xbd802c: ldur            x0, [fp, #-0x28]
    // 0xbd8030: StoreField: r1->field_13 = r0
    //     0xbd8030: stur            w0, [x1, #0x13]
    // 0xbd8034: r0 = Padding()
    //     0xbd8034: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd8038: mov             x3, x0
    // 0xbd803c: r0 = Instance_EdgeInsets
    //     0xbd803c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd8040: ldr             x0, [x0, #0x980]
    // 0xbd8044: stur            x3, [fp, #-0x38]
    // 0xbd8048: StoreField: r3->field_f = r0
    //     0xbd8048: stur            w0, [x3, #0xf]
    // 0xbd804c: ldur            x1, [fp, #-0x30]
    // 0xbd8050: StoreField: r3->field_b = r1
    //     0xbd8050: stur            w1, [x3, #0xb]
    // 0xbd8054: ldur            x4, [fp, #-8]
    // 0xbd8058: LoadField: r1 = r4->field_b
    //     0xbd8058: ldur            w1, [x4, #0xb]
    // 0xbd805c: DecompressPointer r1
    //     0xbd805c: add             x1, x1, HEAP, lsl #32
    // 0xbd8060: cmp             w1, NULL
    // 0xbd8064: b.eq            #0xbd867c
    // 0xbd8068: LoadField: r5 = r1->field_b
    //     0xbd8068: ldur            w5, [x1, #0xb]
    // 0xbd806c: DecompressPointer r5
    //     0xbd806c: add             x5, x5, HEAP, lsl #32
    // 0xbd8070: stur            x5, [fp, #-0x28]
    // 0xbd8074: cmp             w5, NULL
    // 0xbd8078: b.ne            #0xbd8084
    // 0xbd807c: r1 = Null
    //     0xbd807c: mov             x1, NULL
    // 0xbd8080: b               #0xbd808c
    // 0xbd8084: LoadField: r1 = r5->field_23
    //     0xbd8084: ldur            w1, [x5, #0x23]
    // 0xbd8088: DecompressPointer r1
    //     0xbd8088: add             x1, x1, HEAP, lsl #32
    // 0xbd808c: cbnz            w1, #0xbd8098
    // 0xbd8090: r6 = false
    //     0xbd8090: add             x6, NULL, #0x30  ; false
    // 0xbd8094: b               #0xbd809c
    // 0xbd8098: r6 = true
    //     0xbd8098: add             x6, NULL, #0x20  ; true
    // 0xbd809c: stur            x6, [fp, #-0x18]
    // 0xbd80a0: r1 = Null
    //     0xbd80a0: mov             x1, NULL
    // 0xbd80a4: r2 = 4
    //     0xbd80a4: movz            x2, #0x4
    // 0xbd80a8: r0 = AllocateArray()
    //     0xbd80a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd80ac: mov             x1, x0
    // 0xbd80b0: stur            x1, [fp, #-0x30]
    // 0xbd80b4: r16 = "+ "
    //     0xbd80b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xbd80b8: ldr             x16, [x16, #0xc30]
    // 0xbd80bc: StoreField: r1->field_f = r16
    //     0xbd80bc: stur            w16, [x1, #0xf]
    // 0xbd80c0: ldur            x0, [fp, #-0x28]
    // 0xbd80c4: cmp             w0, NULL
    // 0xbd80c8: b.ne            #0xbd80d4
    // 0xbd80cc: r0 = Null
    //     0xbd80cc: mov             x0, NULL
    // 0xbd80d0: b               #0xbd80fc
    // 0xbd80d4: LoadField: r2 = r0->field_27
    //     0xbd80d4: ldur            w2, [x0, #0x27]
    // 0xbd80d8: DecompressPointer r2
    //     0xbd80d8: add             x2, x2, HEAP, lsl #32
    // 0xbd80dc: r0 = LoadClassIdInstr(r2)
    //     0xbd80dc: ldur            x0, [x2, #-1]
    //     0xbd80e0: ubfx            x0, x0, #0xc, #0x14
    // 0xbd80e4: str             x2, [SP]
    // 0xbd80e8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd80e8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd80ec: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd80ec: movz            x17, #0x2700
    //     0xbd80f0: add             lr, x0, x17
    //     0xbd80f4: ldr             lr, [x21, lr, lsl #3]
    //     0xbd80f8: blr             lr
    // 0xbd80fc: cmp             w0, NULL
    // 0xbd8100: b.ne            #0xbd8108
    // 0xbd8104: r0 = " "
    //     0xbd8104: ldr             x0, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbd8108: ldur            x3, [fp, #-8]
    // 0xbd810c: ldur            x2, [fp, #-0x38]
    // 0xbd8110: ldur            x4, [fp, #-0x18]
    // 0xbd8114: ldur            x1, [fp, #-0x30]
    // 0xbd8118: ArrayStore: r1[1] = r0  ; List_4
    //     0xbd8118: add             x25, x1, #0x13
    //     0xbd811c: str             w0, [x25]
    //     0xbd8120: tbz             w0, #0, #0xbd813c
    //     0xbd8124: ldurb           w16, [x1, #-1]
    //     0xbd8128: ldurb           w17, [x0, #-1]
    //     0xbd812c: and             x16, x17, x16, lsr #2
    //     0xbd8130: tst             x16, HEAP, lsr #32
    //     0xbd8134: b.eq            #0xbd813c
    //     0xbd8138: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd813c: ldur            x16, [fp, #-0x30]
    // 0xbd8140: str             x16, [SP]
    // 0xbd8144: r0 = _interpolate()
    //     0xbd8144: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd8148: ldur            x1, [fp, #-0x10]
    // 0xbd814c: stur            x0, [fp, #-0x28]
    // 0xbd8150: r0 = of()
    //     0xbd8150: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd8154: LoadField: r1 = r0->field_87
    //     0xbd8154: ldur            w1, [x0, #0x87]
    // 0xbd8158: DecompressPointer r1
    //     0xbd8158: add             x1, x1, HEAP, lsl #32
    // 0xbd815c: LoadField: r0 = r1->field_2b
    //     0xbd815c: ldur            w0, [x1, #0x2b]
    // 0xbd8160: DecompressPointer r0
    //     0xbd8160: add             x0, x0, HEAP, lsl #32
    // 0xbd8164: stur            x0, [fp, #-0x30]
    // 0xbd8168: r1 = Instance_Color
    //     0xbd8168: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd816c: d0 = 0.700000
    //     0xbd816c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd8170: ldr             d0, [x17, #0xf48]
    // 0xbd8174: r0 = withOpacity()
    //     0xbd8174: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd8178: r16 = 14.000000
    //     0xbd8178: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd817c: ldr             x16, [x16, #0x1d8]
    // 0xbd8180: stp             x0, x16, [SP]
    // 0xbd8184: ldur            x1, [fp, #-0x30]
    // 0xbd8188: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd8188: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd818c: ldr             x4, [x4, #0xaa0]
    // 0xbd8190: r0 = copyWith()
    //     0xbd8190: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd8194: stur            x0, [fp, #-0x30]
    // 0xbd8198: r0 = Text()
    //     0xbd8198: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd819c: mov             x1, x0
    // 0xbd81a0: ldur            x0, [fp, #-0x28]
    // 0xbd81a4: stur            x1, [fp, #-0x40]
    // 0xbd81a8: StoreField: r1->field_b = r0
    //     0xbd81a8: stur            w0, [x1, #0xb]
    // 0xbd81ac: ldur            x0, [fp, #-0x30]
    // 0xbd81b0: StoreField: r1->field_13 = r0
    //     0xbd81b0: stur            w0, [x1, #0x13]
    // 0xbd81b4: r0 = Visibility()
    //     0xbd81b4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd81b8: mov             x1, x0
    // 0xbd81bc: ldur            x0, [fp, #-0x40]
    // 0xbd81c0: stur            x1, [fp, #-0x28]
    // 0xbd81c4: StoreField: r1->field_b = r0
    //     0xbd81c4: stur            w0, [x1, #0xb]
    // 0xbd81c8: r0 = Instance_SizedBox
    //     0xbd81c8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd81cc: StoreField: r1->field_f = r0
    //     0xbd81cc: stur            w0, [x1, #0xf]
    // 0xbd81d0: ldur            x2, [fp, #-0x18]
    // 0xbd81d4: StoreField: r1->field_13 = r2
    //     0xbd81d4: stur            w2, [x1, #0x13]
    // 0xbd81d8: r2 = false
    //     0xbd81d8: add             x2, NULL, #0x30  ; false
    // 0xbd81dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd81dc: stur            w2, [x1, #0x17]
    // 0xbd81e0: StoreField: r1->field_1b = r2
    //     0xbd81e0: stur            w2, [x1, #0x1b]
    // 0xbd81e4: StoreField: r1->field_1f = r2
    //     0xbd81e4: stur            w2, [x1, #0x1f]
    // 0xbd81e8: StoreField: r1->field_23 = r2
    //     0xbd81e8: stur            w2, [x1, #0x23]
    // 0xbd81ec: StoreField: r1->field_27 = r2
    //     0xbd81ec: stur            w2, [x1, #0x27]
    // 0xbd81f0: StoreField: r1->field_2b = r2
    //     0xbd81f0: stur            w2, [x1, #0x2b]
    // 0xbd81f4: r0 = Padding()
    //     0xbd81f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd81f8: mov             x3, x0
    // 0xbd81fc: r0 = Instance_EdgeInsets
    //     0xbd81fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd8200: ldr             x0, [x0, #0x980]
    // 0xbd8204: stur            x3, [fp, #-0x18]
    // 0xbd8208: StoreField: r3->field_f = r0
    //     0xbd8208: stur            w0, [x3, #0xf]
    // 0xbd820c: ldur            x0, [fp, #-0x28]
    // 0xbd8210: StoreField: r3->field_b = r0
    //     0xbd8210: stur            w0, [x3, #0xb]
    // 0xbd8214: r1 = Null
    //     0xbd8214: mov             x1, NULL
    // 0xbd8218: r2 = 6
    //     0xbd8218: movz            x2, #0x6
    // 0xbd821c: r0 = AllocateArray()
    //     0xbd821c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd8220: mov             x2, x0
    // 0xbd8224: ldur            x0, [fp, #-0x38]
    // 0xbd8228: stur            x2, [fp, #-0x28]
    // 0xbd822c: StoreField: r2->field_f = r0
    //     0xbd822c: stur            w0, [x2, #0xf]
    // 0xbd8230: r16 = Instance_Spacer
    //     0xbd8230: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd8234: ldr             x16, [x16, #0xf0]
    // 0xbd8238: StoreField: r2->field_13 = r16
    //     0xbd8238: stur            w16, [x2, #0x13]
    // 0xbd823c: ldur            x0, [fp, #-0x18]
    // 0xbd8240: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd8240: stur            w0, [x2, #0x17]
    // 0xbd8244: r1 = <Widget>
    //     0xbd8244: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd8248: r0 = AllocateGrowableArray()
    //     0xbd8248: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd824c: mov             x1, x0
    // 0xbd8250: ldur            x0, [fp, #-0x28]
    // 0xbd8254: stur            x1, [fp, #-0x18]
    // 0xbd8258: StoreField: r1->field_f = r0
    //     0xbd8258: stur            w0, [x1, #0xf]
    // 0xbd825c: r2 = 6
    //     0xbd825c: movz            x2, #0x6
    // 0xbd8260: StoreField: r1->field_b = r2
    //     0xbd8260: stur            w2, [x1, #0xb]
    // 0xbd8264: r0 = Row()
    //     0xbd8264: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd8268: mov             x2, x0
    // 0xbd826c: r0 = Instance_Axis
    //     0xbd826c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd8270: stur            x2, [fp, #-0x30]
    // 0xbd8274: StoreField: r2->field_f = r0
    //     0xbd8274: stur            w0, [x2, #0xf]
    // 0xbd8278: r0 = Instance_MainAxisAlignment
    //     0xbd8278: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd827c: ldr             x0, [x0, #0xa08]
    // 0xbd8280: StoreField: r2->field_13 = r0
    //     0xbd8280: stur            w0, [x2, #0x13]
    // 0xbd8284: r3 = Instance_MainAxisSize
    //     0xbd8284: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd8288: ldr             x3, [x3, #0xa10]
    // 0xbd828c: ArrayStore: r2[0] = r3  ; List_4
    //     0xbd828c: stur            w3, [x2, #0x17]
    // 0xbd8290: r1 = Instance_CrossAxisAlignment
    //     0xbd8290: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd8294: ldr             x1, [x1, #0xa18]
    // 0xbd8298: StoreField: r2->field_1b = r1
    //     0xbd8298: stur            w1, [x2, #0x1b]
    // 0xbd829c: r4 = Instance_VerticalDirection
    //     0xbd829c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd82a0: ldr             x4, [x4, #0xa20]
    // 0xbd82a4: StoreField: r2->field_23 = r4
    //     0xbd82a4: stur            w4, [x2, #0x23]
    // 0xbd82a8: r5 = Instance_Clip
    //     0xbd82a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd82ac: ldr             x5, [x5, #0x38]
    // 0xbd82b0: StoreField: r2->field_2b = r5
    //     0xbd82b0: stur            w5, [x2, #0x2b]
    // 0xbd82b4: StoreField: r2->field_2f = rZR
    //     0xbd82b4: stur            xzr, [x2, #0x2f]
    // 0xbd82b8: ldur            x1, [fp, #-0x18]
    // 0xbd82bc: StoreField: r2->field_b = r1
    //     0xbd82bc: stur            w1, [x2, #0xb]
    // 0xbd82c0: ldur            x6, [fp, #-8]
    // 0xbd82c4: LoadField: r1 = r6->field_b
    //     0xbd82c4: ldur            w1, [x6, #0xb]
    // 0xbd82c8: DecompressPointer r1
    //     0xbd82c8: add             x1, x1, HEAP, lsl #32
    // 0xbd82cc: cmp             w1, NULL
    // 0xbd82d0: b.eq            #0xbd8680
    // 0xbd82d4: LoadField: r7 = r1->field_b
    //     0xbd82d4: ldur            w7, [x1, #0xb]
    // 0xbd82d8: DecompressPointer r7
    //     0xbd82d8: add             x7, x7, HEAP, lsl #32
    // 0xbd82dc: cmp             w7, NULL
    // 0xbd82e0: b.ne            #0xbd82ec
    // 0xbd82e4: r1 = Null
    //     0xbd82e4: mov             x1, NULL
    // 0xbd82e8: b               #0xbd8318
    // 0xbd82ec: LoadField: r1 = r7->field_1f
    //     0xbd82ec: ldur            w1, [x7, #0x1f]
    // 0xbd82f0: DecompressPointer r1
    //     0xbd82f0: add             x1, x1, HEAP, lsl #32
    // 0xbd82f4: cmp             w1, NULL
    // 0xbd82f8: b.ne            #0xbd8304
    // 0xbd82fc: r1 = Null
    //     0xbd82fc: mov             x1, NULL
    // 0xbd8300: b               #0xbd8318
    // 0xbd8304: LoadField: r8 = r1->field_7
    //     0xbd8304: ldur            w8, [x1, #7]
    // 0xbd8308: cbnz            w8, #0xbd8314
    // 0xbd830c: r1 = false
    //     0xbd830c: add             x1, NULL, #0x30  ; false
    // 0xbd8310: b               #0xbd8318
    // 0xbd8314: r1 = true
    //     0xbd8314: add             x1, NULL, #0x20  ; true
    // 0xbd8318: cmp             w1, NULL
    // 0xbd831c: b.ne            #0xbd8328
    // 0xbd8320: r8 = false
    //     0xbd8320: add             x8, NULL, #0x30  ; false
    // 0xbd8324: b               #0xbd832c
    // 0xbd8328: mov             x8, x1
    // 0xbd832c: stur            x8, [fp, #-0x28]
    // 0xbd8330: cmp             w7, NULL
    // 0xbd8334: b.ne            #0xbd8340
    // 0xbd8338: r1 = Null
    //     0xbd8338: mov             x1, NULL
    // 0xbd833c: b               #0xbd8348
    // 0xbd8340: LoadField: r1 = r7->field_1f
    //     0xbd8340: ldur            w1, [x7, #0x1f]
    // 0xbd8344: DecompressPointer r1
    //     0xbd8344: add             x1, x1, HEAP, lsl #32
    // 0xbd8348: cmp             w1, NULL
    // 0xbd834c: b.ne            #0xbd8358
    // 0xbd8350: r7 = ""
    //     0xbd8350: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd8354: b               #0xbd835c
    // 0xbd8358: mov             x7, x1
    // 0xbd835c: ldur            x1, [fp, #-0x10]
    // 0xbd8360: stur            x7, [fp, #-0x18]
    // 0xbd8364: r0 = of()
    //     0xbd8364: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd8368: LoadField: r1 = r0->field_87
    //     0xbd8368: ldur            w1, [x0, #0x87]
    // 0xbd836c: DecompressPointer r1
    //     0xbd836c: add             x1, x1, HEAP, lsl #32
    // 0xbd8370: LoadField: r0 = r1->field_2b
    //     0xbd8370: ldur            w0, [x1, #0x2b]
    // 0xbd8374: DecompressPointer r0
    //     0xbd8374: add             x0, x0, HEAP, lsl #32
    // 0xbd8378: stur            x0, [fp, #-0x38]
    // 0xbd837c: r1 = Instance_Color
    //     0xbd837c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd8380: d0 = 0.400000
    //     0xbd8380: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd8384: r0 = withOpacity()
    //     0xbd8384: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd8388: r16 = 12.000000
    //     0xbd8388: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd838c: ldr             x16, [x16, #0x9e8]
    // 0xbd8390: stp             x0, x16, [SP]
    // 0xbd8394: ldur            x1, [fp, #-0x38]
    // 0xbd8398: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd8398: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd839c: ldr             x4, [x4, #0xaa0]
    // 0xbd83a0: r0 = copyWith()
    //     0xbd83a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd83a4: stur            x0, [fp, #-0x38]
    // 0xbd83a8: r0 = Text()
    //     0xbd83a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd83ac: mov             x1, x0
    // 0xbd83b0: ldur            x0, [fp, #-0x18]
    // 0xbd83b4: stur            x1, [fp, #-0x40]
    // 0xbd83b8: StoreField: r1->field_b = r0
    //     0xbd83b8: stur            w0, [x1, #0xb]
    // 0xbd83bc: ldur            x0, [fp, #-0x38]
    // 0xbd83c0: StoreField: r1->field_13 = r0
    //     0xbd83c0: stur            w0, [x1, #0x13]
    // 0xbd83c4: r0 = Padding()
    //     0xbd83c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd83c8: mov             x1, x0
    // 0xbd83cc: r0 = Instance_EdgeInsets
    //     0xbd83cc: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a408] Obj!EdgeInsets@d582b1
    //     0xbd83d0: ldr             x0, [x0, #0x408]
    // 0xbd83d4: stur            x1, [fp, #-0x18]
    // 0xbd83d8: StoreField: r1->field_f = r0
    //     0xbd83d8: stur            w0, [x1, #0xf]
    // 0xbd83dc: ldur            x0, [fp, #-0x40]
    // 0xbd83e0: StoreField: r1->field_b = r0
    //     0xbd83e0: stur            w0, [x1, #0xb]
    // 0xbd83e4: r0 = Visibility()
    //     0xbd83e4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd83e8: mov             x1, x0
    // 0xbd83ec: ldur            x0, [fp, #-0x18]
    // 0xbd83f0: stur            x1, [fp, #-0x38]
    // 0xbd83f4: StoreField: r1->field_b = r0
    //     0xbd83f4: stur            w0, [x1, #0xb]
    // 0xbd83f8: r0 = Instance_SizedBox
    //     0xbd83f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd83fc: StoreField: r1->field_f = r0
    //     0xbd83fc: stur            w0, [x1, #0xf]
    // 0xbd8400: ldur            x0, [fp, #-0x28]
    // 0xbd8404: StoreField: r1->field_13 = r0
    //     0xbd8404: stur            w0, [x1, #0x13]
    // 0xbd8408: r0 = false
    //     0xbd8408: add             x0, NULL, #0x30  ; false
    // 0xbd840c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd840c: stur            w0, [x1, #0x17]
    // 0xbd8410: StoreField: r1->field_1b = r0
    //     0xbd8410: stur            w0, [x1, #0x1b]
    // 0xbd8414: StoreField: r1->field_1f = r0
    //     0xbd8414: stur            w0, [x1, #0x1f]
    // 0xbd8418: StoreField: r1->field_23 = r0
    //     0xbd8418: stur            w0, [x1, #0x23]
    // 0xbd841c: StoreField: r1->field_27 = r0
    //     0xbd841c: stur            w0, [x1, #0x27]
    // 0xbd8420: StoreField: r1->field_2b = r0
    //     0xbd8420: stur            w0, [x1, #0x2b]
    // 0xbd8424: ldur            x0, [fp, #-8]
    // 0xbd8428: LoadField: r2 = r0->field_13
    //     0xbd8428: ldur            w2, [x0, #0x13]
    // 0xbd842c: DecompressPointer r2
    //     0xbd842c: add             x2, x2, HEAP, lsl #32
    // 0xbd8430: stur            x2, [fp, #-0x18]
    // 0xbd8434: r0 = LengthLimitingTextInputFormatter()
    //     0xbd8434: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbd8438: mov             x3, x0
    // 0xbd843c: r0 = 60
    //     0xbd843c: movz            x0, #0x3c
    // 0xbd8440: stur            x3, [fp, #-0x28]
    // 0xbd8444: StoreField: r3->field_7 = r0
    //     0xbd8444: stur            w0, [x3, #7]
    // 0xbd8448: r1 = Null
    //     0xbd8448: mov             x1, NULL
    // 0xbd844c: r2 = 2
    //     0xbd844c: movz            x2, #0x2
    // 0xbd8450: r0 = AllocateArray()
    //     0xbd8450: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd8454: mov             x2, x0
    // 0xbd8458: ldur            x0, [fp, #-0x28]
    // 0xbd845c: stur            x2, [fp, #-0x40]
    // 0xbd8460: StoreField: r2->field_f = r0
    //     0xbd8460: stur            w0, [x2, #0xf]
    // 0xbd8464: r1 = <TextInputFormatter>
    //     0xbd8464: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbd8468: ldr             x1, [x1, #0x7b0]
    // 0xbd846c: r0 = AllocateGrowableArray()
    //     0xbd846c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd8470: mov             x2, x0
    // 0xbd8474: ldur            x0, [fp, #-0x40]
    // 0xbd8478: stur            x2, [fp, #-0x28]
    // 0xbd847c: StoreField: r2->field_f = r0
    //     0xbd847c: stur            w0, [x2, #0xf]
    // 0xbd8480: r0 = 2
    //     0xbd8480: movz            x0, #0x2
    // 0xbd8484: StoreField: r2->field_b = r0
    //     0xbd8484: stur            w0, [x2, #0xb]
    // 0xbd8488: ldur            x1, [fp, #-0x10]
    // 0xbd848c: r0 = of()
    //     0xbd848c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd8490: LoadField: r1 = r0->field_87
    //     0xbd8490: ldur            w1, [x0, #0x87]
    // 0xbd8494: DecompressPointer r1
    //     0xbd8494: add             x1, x1, HEAP, lsl #32
    // 0xbd8498: LoadField: r0 = r1->field_2b
    //     0xbd8498: ldur            w0, [x1, #0x2b]
    // 0xbd849c: DecompressPointer r0
    //     0xbd849c: add             x0, x0, HEAP, lsl #32
    // 0xbd84a0: ldur            x1, [fp, #-0x10]
    // 0xbd84a4: stur            x0, [fp, #-0x40]
    // 0xbd84a8: r0 = of()
    //     0xbd84a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd84ac: LoadField: r1 = r0->field_5b
    //     0xbd84ac: ldur            w1, [x0, #0x5b]
    // 0xbd84b0: DecompressPointer r1
    //     0xbd84b0: add             x1, x1, HEAP, lsl #32
    // 0xbd84b4: r16 = 12.000000
    //     0xbd84b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd84b8: ldr             x16, [x16, #0x9e8]
    // 0xbd84bc: stp             x1, x16, [SP]
    // 0xbd84c0: ldur            x1, [fp, #-0x40]
    // 0xbd84c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd84c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd84c8: ldr             x4, [x4, #0xaa0]
    // 0xbd84cc: r0 = copyWith()
    //     0xbd84cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd84d0: mov             x2, x0
    // 0xbd84d4: ldur            x0, [fp, #-8]
    // 0xbd84d8: stur            x2, [fp, #-0x48]
    // 0xbd84dc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbd84dc: ldur            w3, [x0, #0x17]
    // 0xbd84e0: DecompressPointer r3
    //     0xbd84e0: add             x3, x3, HEAP, lsl #32
    // 0xbd84e4: ldur            x1, [fp, #-0x10]
    // 0xbd84e8: stur            x3, [fp, #-0x40]
    // 0xbd84ec: r0 = getTextFormFieldInputDecoration()
    //     0xbd84ec: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbd84f0: r16 = Instance_EdgeInsets
    //     0xbd84f0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbd84f4: ldr             x16, [x16, #0xc40]
    // 0xbd84f8: str             x16, [SP]
    // 0xbd84fc: mov             x1, x0
    // 0xbd8500: r4 = const [0, 0x2, 0x1, 0x1, contentPadding, 0x1, null]
    //     0xbd8500: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a410] List(7) [0, 0x2, 0x1, 0x1, "contentPadding", 0x1, Null]
    //     0xbd8504: ldr             x4, [x4, #0x410]
    // 0xbd8508: r0 = copyWith()
    //     0xbd8508: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbd850c: ldur            x2, [fp, #-0x20]
    // 0xbd8510: r1 = Function '<anonymous closure>':.
    //     0xbd8510: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a4c8] AnonymousClosure: (0xbd86a4), in [package:customer_app/app/presentation/views/line/customization/customisation_text.dart] _CustomisationTextState::build (0xbd7ed0)
    //     0xbd8514: ldr             x1, [x1, #0x4c8]
    // 0xbd8518: stur            x0, [fp, #-8]
    // 0xbd851c: r0 = AllocateClosure()
    //     0xbd851c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd8520: r1 = <String>
    //     0xbd8520: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbd8524: stur            x0, [fp, #-0x10]
    // 0xbd8528: r0 = TextFormField()
    //     0xbd8528: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbd852c: stur            x0, [fp, #-0x20]
    // 0xbd8530: r16 = false
    //     0xbd8530: add             x16, NULL, #0x30  ; false
    // 0xbd8534: r30 = Instance_AutovalidateMode
    //     0xbd8534: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbd8538: ldr             lr, [lr, #0x7e8]
    // 0xbd853c: stp             lr, x16, [SP, #0x30]
    // 0xbd8540: ldur            x16, [fp, #-0x28]
    // 0xbd8544: r30 = Instance_TextInputType
    //     0xbd8544: add             lr, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xbd8548: ldr             lr, [lr, #0x68]
    // 0xbd854c: stp             lr, x16, [SP, #0x20]
    // 0xbd8550: r16 = 2
    //     0xbd8550: movz            x16, #0x2
    // 0xbd8554: ldur            lr, [fp, #-0x48]
    // 0xbd8558: stp             lr, x16, [SP, #0x10]
    // 0xbd855c: ldur            x16, [fp, #-0x40]
    // 0xbd8560: ldur            lr, [fp, #-0x10]
    // 0xbd8564: stp             lr, x16, [SP]
    // 0xbd8568: mov             x1, x0
    // 0xbd856c: ldur            x2, [fp, #-8]
    // 0xbd8570: r4 = const [0, 0xa, 0x8, 0x2, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x6, onChanged, 0x9, style, 0x7, null]
    //     0xbd8570: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a428] List(21) [0, 0xa, 0x8, 0x2, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x6, "onChanged", 0x9, "style", 0x7, Null]
    //     0xbd8574: ldr             x4, [x4, #0x428]
    // 0xbd8578: r0 = TextFormField()
    //     0xbd8578: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbd857c: r0 = Form()
    //     0xbd857c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbd8580: mov             x1, x0
    // 0xbd8584: ldur            x0, [fp, #-0x20]
    // 0xbd8588: stur            x1, [fp, #-8]
    // 0xbd858c: StoreField: r1->field_b = r0
    //     0xbd858c: stur            w0, [x1, #0xb]
    // 0xbd8590: r0 = Instance_AutovalidateMode
    //     0xbd8590: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbd8594: ldr             x0, [x0, #0x800]
    // 0xbd8598: StoreField: r1->field_23 = r0
    //     0xbd8598: stur            w0, [x1, #0x23]
    // 0xbd859c: ldur            x0, [fp, #-0x18]
    // 0xbd85a0: StoreField: r1->field_7 = r0
    //     0xbd85a0: stur            w0, [x1, #7]
    // 0xbd85a4: r0 = Padding()
    //     0xbd85a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd85a8: mov             x3, x0
    // 0xbd85ac: r0 = Instance_EdgeInsets
    //     0xbd85ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbd85b0: ldr             x0, [x0, #0x668]
    // 0xbd85b4: stur            x3, [fp, #-0x10]
    // 0xbd85b8: StoreField: r3->field_f = r0
    //     0xbd85b8: stur            w0, [x3, #0xf]
    // 0xbd85bc: ldur            x0, [fp, #-8]
    // 0xbd85c0: StoreField: r3->field_b = r0
    //     0xbd85c0: stur            w0, [x3, #0xb]
    // 0xbd85c4: r1 = Null
    //     0xbd85c4: mov             x1, NULL
    // 0xbd85c8: r2 = 6
    //     0xbd85c8: movz            x2, #0x6
    // 0xbd85cc: r0 = AllocateArray()
    //     0xbd85cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd85d0: mov             x2, x0
    // 0xbd85d4: ldur            x0, [fp, #-0x30]
    // 0xbd85d8: stur            x2, [fp, #-8]
    // 0xbd85dc: StoreField: r2->field_f = r0
    //     0xbd85dc: stur            w0, [x2, #0xf]
    // 0xbd85e0: ldur            x0, [fp, #-0x38]
    // 0xbd85e4: StoreField: r2->field_13 = r0
    //     0xbd85e4: stur            w0, [x2, #0x13]
    // 0xbd85e8: ldur            x0, [fp, #-0x10]
    // 0xbd85ec: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd85ec: stur            w0, [x2, #0x17]
    // 0xbd85f0: r1 = <Widget>
    //     0xbd85f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd85f4: r0 = AllocateGrowableArray()
    //     0xbd85f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd85f8: mov             x1, x0
    // 0xbd85fc: ldur            x0, [fp, #-8]
    // 0xbd8600: stur            x1, [fp, #-0x10]
    // 0xbd8604: StoreField: r1->field_f = r0
    //     0xbd8604: stur            w0, [x1, #0xf]
    // 0xbd8608: r0 = 6
    //     0xbd8608: movz            x0, #0x6
    // 0xbd860c: StoreField: r1->field_b = r0
    //     0xbd860c: stur            w0, [x1, #0xb]
    // 0xbd8610: r0 = Column()
    //     0xbd8610: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbd8614: r1 = Instance_Axis
    //     0xbd8614: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd8618: StoreField: r0->field_f = r1
    //     0xbd8618: stur            w1, [x0, #0xf]
    // 0xbd861c: r1 = Instance_MainAxisAlignment
    //     0xbd861c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd8620: ldr             x1, [x1, #0xa08]
    // 0xbd8624: StoreField: r0->field_13 = r1
    //     0xbd8624: stur            w1, [x0, #0x13]
    // 0xbd8628: r1 = Instance_MainAxisSize
    //     0xbd8628: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd862c: ldr             x1, [x1, #0xa10]
    // 0xbd8630: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd8630: stur            w1, [x0, #0x17]
    // 0xbd8634: r1 = Instance_CrossAxisAlignment
    //     0xbd8634: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbd8638: ldr             x1, [x1, #0x890]
    // 0xbd863c: StoreField: r0->field_1b = r1
    //     0xbd863c: stur            w1, [x0, #0x1b]
    // 0xbd8640: r1 = Instance_VerticalDirection
    //     0xbd8640: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd8644: ldr             x1, [x1, #0xa20]
    // 0xbd8648: StoreField: r0->field_23 = r1
    //     0xbd8648: stur            w1, [x0, #0x23]
    // 0xbd864c: r1 = Instance_Clip
    //     0xbd864c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd8650: ldr             x1, [x1, #0x38]
    // 0xbd8654: StoreField: r0->field_2b = r1
    //     0xbd8654: stur            w1, [x0, #0x2b]
    // 0xbd8658: StoreField: r0->field_2f = rZR
    //     0xbd8658: stur            xzr, [x0, #0x2f]
    // 0xbd865c: ldur            x1, [fp, #-0x10]
    // 0xbd8660: StoreField: r0->field_b = r1
    //     0xbd8660: stur            w1, [x0, #0xb]
    // 0xbd8664: LeaveFrame
    //     0xbd8664: mov             SP, fp
    //     0xbd8668: ldp             fp, lr, [SP], #0x10
    // 0xbd866c: ret
    //     0xbd866c: ret             
    // 0xbd8670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd8670: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8674: b               #0xbd7ef8
    // 0xbd8678: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8678: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd867c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd867c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8680: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xbd86a4, size: 0x5cc
    // 0xbd86a4: EnterFrame
    //     0xbd86a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd86a8: mov             fp, SP
    // 0xbd86ac: AllocStack(0x78)
    //     0xbd86ac: sub             SP, SP, #0x78
    // 0xbd86b0: SetupParameters()
    //     0xbd86b0: ldr             x0, [fp, #0x18]
    //     0xbd86b4: ldur            w3, [x0, #0x17]
    //     0xbd86b8: add             x3, x3, HEAP, lsl #32
    //     0xbd86bc: stur            x3, [fp, #-0x10]
    // 0xbd86c0: CheckStackOverflow
    //     0xbd86c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd86c4: cmp             SP, x16
    //     0xbd86c8: b.ls            #0xbd8c4c
    // 0xbd86cc: LoadField: r0 = r3->field_f
    //     0xbd86cc: ldur            w0, [x3, #0xf]
    // 0xbd86d0: DecompressPointer r0
    //     0xbd86d0: add             x0, x0, HEAP, lsl #32
    // 0xbd86d4: LoadField: r1 = r0->field_b
    //     0xbd86d4: ldur            w1, [x0, #0xb]
    // 0xbd86d8: DecompressPointer r1
    //     0xbd86d8: add             x1, x1, HEAP, lsl #32
    // 0xbd86dc: cmp             w1, NULL
    // 0xbd86e0: b.eq            #0xbd8c54
    // 0xbd86e4: LoadField: r0 = r1->field_f
    //     0xbd86e4: ldur            w0, [x1, #0xf]
    // 0xbd86e8: DecompressPointer r0
    //     0xbd86e8: add             x0, x0, HEAP, lsl #32
    // 0xbd86ec: stur            x0, [fp, #-8]
    // 0xbd86f0: LoadField: r1 = r0->field_b
    //     0xbd86f0: ldur            w1, [x0, #0xb]
    // 0xbd86f4: cbz             w1, #0xbd87ac
    // 0xbd86f8: mov             x2, x3
    // 0xbd86fc: r1 = Function '<anonymous closure>':.
    //     0xbd86fc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a4d0] AnonymousClosure: (0xa3afc0), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xbd8700: ldr             x1, [x1, #0x4d0]
    // 0xbd8704: r0 = AllocateClosure()
    //     0xbd8704: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd8708: r1 = Function '<anonymous closure>':.
    //     0xbd8708: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a4d8] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xbd870c: ldr             x1, [x1, #0x4d8]
    // 0xbd8710: r2 = Null
    //     0xbd8710: mov             x2, NULL
    // 0xbd8714: stur            x0, [fp, #-0x18]
    // 0xbd8718: r0 = AllocateClosure()
    //     0xbd8718: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd871c: str             x0, [SP]
    // 0xbd8720: ldur            x1, [fp, #-8]
    // 0xbd8724: ldur            x2, [fp, #-0x18]
    // 0xbd8728: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xbd8728: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xbd872c: ldr             x4, [x4, #0xb48]
    // 0xbd8730: r0 = firstWhere()
    //     0xbd8730: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xbd8734: LoadField: r1 = r0->field_b
    //     0xbd8734: ldur            w1, [x0, #0xb]
    // 0xbd8738: DecompressPointer r1
    //     0xbd8738: add             x1, x1, HEAP, lsl #32
    // 0xbd873c: cmp             w1, NULL
    // 0xbd8740: b.eq            #0xbd87ac
    // 0xbd8744: ldur            x1, [fp, #-0x10]
    // 0xbd8748: LoadField: r2 = r1->field_f
    //     0xbd8748: ldur            w2, [x1, #0xf]
    // 0xbd874c: DecompressPointer r2
    //     0xbd874c: add             x2, x2, HEAP, lsl #32
    // 0xbd8750: LoadField: r3 = r2->field_b
    //     0xbd8750: ldur            w3, [x2, #0xb]
    // 0xbd8754: DecompressPointer r3
    //     0xbd8754: add             x3, x3, HEAP, lsl #32
    // 0xbd8758: cmp             w3, NULL
    // 0xbd875c: b.eq            #0xbd8c58
    // 0xbd8760: LoadField: r2 = r3->field_b
    //     0xbd8760: ldur            w2, [x3, #0xb]
    // 0xbd8764: DecompressPointer r2
    //     0xbd8764: add             x2, x2, HEAP, lsl #32
    // 0xbd8768: cmp             w2, NULL
    // 0xbd876c: b.ne            #0xbd8778
    // 0xbd8770: r2 = Null
    //     0xbd8770: mov             x2, NULL
    // 0xbd8774: b               #0xbd8784
    // 0xbd8778: LoadField: r4 = r2->field_23
    //     0xbd8778: ldur            w4, [x2, #0x23]
    // 0xbd877c: DecompressPointer r4
    //     0xbd877c: add             x4, x4, HEAP, lsl #32
    // 0xbd8780: mov             x2, x4
    // 0xbd8784: LoadField: r4 = r3->field_13
    //     0xbd8784: ldur            w4, [x3, #0x13]
    // 0xbd8788: DecompressPointer r4
    //     0xbd8788: add             x4, x4, HEAP, lsl #32
    // 0xbd878c: stp             x2, x4, [SP, #8]
    // 0xbd8790: str             x0, [SP]
    // 0xbd8794: r4 = 0
    //     0xbd8794: movz            x4, #0
    // 0xbd8798: ldr             x0, [SP, #0x10]
    // 0xbd879c: r16 = UnlinkedCall_0x613b5c
    //     0xbd879c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a4e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd87a0: add             x16, x16, #0x4e0
    // 0xbd87a4: ldp             x5, lr, [x16]
    // 0xbd87a8: blr             lr
    // 0xbd87ac: ldr             x0, [fp, #0x10]
    // 0xbd87b0: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xbd87b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd87b4: ldr             x0, [x0]
    //     0xbd87b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd87bc: cmp             w0, w16
    //     0xbd87c0: b.ne            #0xbd87cc
    //     0xbd87c4: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xbd87c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd87cc: r1 = <CustomerResponse>
    //     0xbd87cc: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xbd87d0: ldr             x1, [x1, #0x5a8]
    // 0xbd87d4: stur            x0, [fp, #-8]
    // 0xbd87d8: r0 = AllocateGrowableArray()
    //     0xbd87d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd87dc: mov             x2, x0
    // 0xbd87e0: ldur            x0, [fp, #-8]
    // 0xbd87e4: stur            x2, [fp, #-0x18]
    // 0xbd87e8: StoreField: r2->field_f = r0
    //     0xbd87e8: stur            w0, [x2, #0xf]
    // 0xbd87ec: StoreField: r2->field_b = rZR
    //     0xbd87ec: stur            wzr, [x2, #0xb]
    // 0xbd87f0: r1 = <ProductCustomisation>
    //     0xbd87f0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xbd87f4: ldr             x1, [x1, #0x370]
    // 0xbd87f8: r0 = AllocateGrowableArray()
    //     0xbd87f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd87fc: mov             x1, x0
    // 0xbd8800: ldur            x0, [fp, #-8]
    // 0xbd8804: stur            x1, [fp, #-0x28]
    // 0xbd8808: StoreField: r1->field_f = r0
    //     0xbd8808: stur            w0, [x1, #0xf]
    // 0xbd880c: StoreField: r1->field_b = rZR
    //     0xbd880c: stur            wzr, [x1, #0xb]
    // 0xbd8810: ldr             x2, [fp, #0x10]
    // 0xbd8814: cmp             w2, NULL
    // 0xbd8818: b.eq            #0xbd8b9c
    // 0xbd881c: ldur            x3, [fp, #-0x10]
    // 0xbd8820: LoadField: r4 = r3->field_f
    //     0xbd8820: ldur            w4, [x3, #0xf]
    // 0xbd8824: DecompressPointer r4
    //     0xbd8824: add             x4, x4, HEAP, lsl #32
    // 0xbd8828: LoadField: r5 = r4->field_b
    //     0xbd8828: ldur            w5, [x4, #0xb]
    // 0xbd882c: DecompressPointer r5
    //     0xbd882c: add             x5, x5, HEAP, lsl #32
    // 0xbd8830: cmp             w5, NULL
    // 0xbd8834: b.eq            #0xbd8c5c
    // 0xbd8838: LoadField: r4 = r5->field_b
    //     0xbd8838: ldur            w4, [x5, #0xb]
    // 0xbd883c: DecompressPointer r4
    //     0xbd883c: add             x4, x4, HEAP, lsl #32
    // 0xbd8840: cmp             w4, NULL
    // 0xbd8844: b.ne            #0xbd8850
    // 0xbd8848: r4 = Null
    //     0xbd8848: mov             x4, NULL
    // 0xbd884c: b               #0xbd885c
    // 0xbd8850: LoadField: r5 = r4->field_1b
    //     0xbd8850: ldur            w5, [x4, #0x1b]
    // 0xbd8854: DecompressPointer r5
    //     0xbd8854: add             x5, x5, HEAP, lsl #32
    // 0xbd8858: mov             x4, x5
    // 0xbd885c: stur            x4, [fp, #-0x20]
    // 0xbd8860: r0 = CustomerResponse()
    //     0xbd8860: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xbd8864: mov             x2, x0
    // 0xbd8868: ldur            x0, [fp, #-0x20]
    // 0xbd886c: stur            x2, [fp, #-0x30]
    // 0xbd8870: StoreField: r2->field_7 = r0
    //     0xbd8870: stur            w0, [x2, #7]
    // 0xbd8874: ldr             x0, [fp, #0x10]
    // 0xbd8878: StoreField: r2->field_b = r0
    //     0xbd8878: stur            w0, [x2, #0xb]
    // 0xbd887c: ldur            x1, [fp, #-8]
    // 0xbd8880: LoadField: r3 = r1->field_b
    //     0xbd8880: ldur            w3, [x1, #0xb]
    // 0xbd8884: cbnz            w3, #0xbd8890
    // 0xbd8888: ldur            x1, [fp, #-0x18]
    // 0xbd888c: r0 = _growToNextCapacity()
    //     0xbd888c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd8890: ldur            x3, [fp, #-0x10]
    // 0xbd8894: ldur            x4, [fp, #-0x18]
    // 0xbd8898: r0 = 2
    //     0xbd8898: movz            x0, #0x2
    // 0xbd889c: StoreField: r4->field_b = r0
    //     0xbd889c: stur            w0, [x4, #0xb]
    // 0xbd88a0: LoadField: r1 = r4->field_f
    //     0xbd88a0: ldur            w1, [x4, #0xf]
    // 0xbd88a4: DecompressPointer r1
    //     0xbd88a4: add             x1, x1, HEAP, lsl #32
    // 0xbd88a8: ldur            x0, [fp, #-0x30]
    // 0xbd88ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd88ac: add             x25, x1, #0xf
    //     0xbd88b0: str             w0, [x25]
    //     0xbd88b4: tbz             w0, #0, #0xbd88d0
    //     0xbd88b8: ldurb           w16, [x1, #-1]
    //     0xbd88bc: ldurb           w17, [x0, #-1]
    //     0xbd88c0: and             x16, x17, x16, lsr #2
    //     0xbd88c4: tst             x16, HEAP, lsr #32
    //     0xbd88c8: b.eq            #0xbd88d0
    //     0xbd88cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd88d0: LoadField: r0 = r3->field_f
    //     0xbd88d0: ldur            w0, [x3, #0xf]
    // 0xbd88d4: DecompressPointer r0
    //     0xbd88d4: add             x0, x0, HEAP, lsl #32
    // 0xbd88d8: LoadField: r1 = r0->field_b
    //     0xbd88d8: ldur            w1, [x0, #0xb]
    // 0xbd88dc: DecompressPointer r1
    //     0xbd88dc: add             x1, x1, HEAP, lsl #32
    // 0xbd88e0: cmp             w1, NULL
    // 0xbd88e4: b.eq            #0xbd8c60
    // 0xbd88e8: LoadField: r0 = r1->field_b
    //     0xbd88e8: ldur            w0, [x1, #0xb]
    // 0xbd88ec: DecompressPointer r0
    //     0xbd88ec: add             x0, x0, HEAP, lsl #32
    // 0xbd88f0: cmp             w0, NULL
    // 0xbd88f4: b.ne            #0xbd8900
    // 0xbd88f8: r5 = Null
    //     0xbd88f8: mov             x5, NULL
    // 0xbd88fc: b               #0xbd890c
    // 0xbd8900: LoadField: r1 = r0->field_7
    //     0xbd8900: ldur            w1, [x0, #7]
    // 0xbd8904: DecompressPointer r1
    //     0xbd8904: add             x1, x1, HEAP, lsl #32
    // 0xbd8908: mov             x5, x1
    // 0xbd890c: stur            x5, [fp, #-0x38]
    // 0xbd8910: cmp             w0, NULL
    // 0xbd8914: b.ne            #0xbd8920
    // 0xbd8918: r6 = Null
    //     0xbd8918: mov             x6, NULL
    // 0xbd891c: b               #0xbd892c
    // 0xbd8920: LoadField: r1 = r0->field_b
    //     0xbd8920: ldur            w1, [x0, #0xb]
    // 0xbd8924: DecompressPointer r1
    //     0xbd8924: add             x1, x1, HEAP, lsl #32
    // 0xbd8928: mov             x6, x1
    // 0xbd892c: stur            x6, [fp, #-0x30]
    // 0xbd8930: cmp             w0, NULL
    // 0xbd8934: b.ne            #0xbd8940
    // 0xbd8938: r7 = Null
    //     0xbd8938: mov             x7, NULL
    // 0xbd893c: b               #0xbd894c
    // 0xbd8940: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd8940: ldur            w1, [x0, #0x17]
    // 0xbd8944: DecompressPointer r1
    //     0xbd8944: add             x1, x1, HEAP, lsl #32
    // 0xbd8948: mov             x7, x1
    // 0xbd894c: stur            x7, [fp, #-0x20]
    // 0xbd8950: cmp             w0, NULL
    // 0xbd8954: b.ne            #0xbd8960
    // 0xbd8958: r0 = Null
    //     0xbd8958: mov             x0, NULL
    // 0xbd895c: b               #0xbd896c
    // 0xbd8960: LoadField: r1 = r0->field_23
    //     0xbd8960: ldur            w1, [x0, #0x23]
    // 0xbd8964: DecompressPointer r1
    //     0xbd8964: add             x1, x1, HEAP, lsl #32
    // 0xbd8968: mov             x0, x1
    // 0xbd896c: stur            x0, [fp, #-8]
    // 0xbd8970: r1 = Null
    //     0xbd8970: mov             x1, NULL
    // 0xbd8974: r2 = 4
    //     0xbd8974: movz            x2, #0x4
    // 0xbd8978: r0 = AllocateArray()
    //     0xbd8978: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd897c: stur            x0, [fp, #-0x40]
    // 0xbd8980: r16 = "₹"
    //     0xbd8980: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xbd8984: ldr             x16, [x16, #0x360]
    // 0xbd8988: StoreField: r0->field_f = r16
    //     0xbd8988: stur            w16, [x0, #0xf]
    // 0xbd898c: r1 = Function '<anonymous closure>': static.
    //     0xbd898c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xbd8990: ldr             x1, [x1, #0x1a0]
    // 0xbd8994: r2 = Null
    //     0xbd8994: mov             x2, NULL
    // 0xbd8998: r0 = AllocateClosure()
    //     0xbd8998: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd899c: mov             x3, x0
    // 0xbd89a0: r1 = Null
    //     0xbd89a0: mov             x1, NULL
    // 0xbd89a4: r2 = Null
    //     0xbd89a4: mov             x2, NULL
    // 0xbd89a8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xbd89a8: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xbd89ac: r0 = NumberFormat._forPattern()
    //     0xbd89ac: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xbd89b0: mov             x1, x0
    // 0xbd89b4: ldur            x0, [fp, #-0x10]
    // 0xbd89b8: LoadField: r2 = r0->field_f
    //     0xbd89b8: ldur            w2, [x0, #0xf]
    // 0xbd89bc: DecompressPointer r2
    //     0xbd89bc: add             x2, x2, HEAP, lsl #32
    // 0xbd89c0: LoadField: r3 = r2->field_b
    //     0xbd89c0: ldur            w3, [x2, #0xb]
    // 0xbd89c4: DecompressPointer r3
    //     0xbd89c4: add             x3, x3, HEAP, lsl #32
    // 0xbd89c8: cmp             w3, NULL
    // 0xbd89cc: b.eq            #0xbd8c64
    // 0xbd89d0: LoadField: r2 = r3->field_b
    //     0xbd89d0: ldur            w2, [x3, #0xb]
    // 0xbd89d4: DecompressPointer r2
    //     0xbd89d4: add             x2, x2, HEAP, lsl #32
    // 0xbd89d8: cmp             w2, NULL
    // 0xbd89dc: b.ne            #0xbd89e8
    // 0xbd89e0: r2 = Null
    //     0xbd89e0: mov             x2, NULL
    // 0xbd89e4: b               #0xbd89f4
    // 0xbd89e8: LoadField: r3 = r2->field_23
    //     0xbd89e8: ldur            w3, [x2, #0x23]
    // 0xbd89ec: DecompressPointer r3
    //     0xbd89ec: add             x3, x3, HEAP, lsl #32
    // 0xbd89f0: mov             x2, x3
    // 0xbd89f4: ldur            x4, [fp, #-0x38]
    // 0xbd89f8: ldur            x5, [fp, #-0x30]
    // 0xbd89fc: ldur            x6, [fp, #-0x20]
    // 0xbd8a00: ldur            x7, [fp, #-8]
    // 0xbd8a04: ldur            x3, [fp, #-0x18]
    // 0xbd8a08: ldur            x8, [fp, #-0x28]
    // 0xbd8a0c: r0 = format()
    //     0xbd8a0c: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xbd8a10: ldur            x1, [fp, #-0x40]
    // 0xbd8a14: ArrayStore: r1[1] = r0  ; List_4
    //     0xbd8a14: add             x25, x1, #0x13
    //     0xbd8a18: str             w0, [x25]
    //     0xbd8a1c: tbz             w0, #0, #0xbd8a38
    //     0xbd8a20: ldurb           w16, [x1, #-1]
    //     0xbd8a24: ldurb           w17, [x0, #-1]
    //     0xbd8a28: and             x16, x17, x16, lsr #2
    //     0xbd8a2c: tst             x16, HEAP, lsr #32
    //     0xbd8a30: b.eq            #0xbd8a38
    //     0xbd8a34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd8a38: ldur            x16, [fp, #-0x40]
    // 0xbd8a3c: str             x16, [SP]
    // 0xbd8a40: r0 = _interpolate()
    //     0xbd8a40: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd8a44: stur            x0, [fp, #-0x40]
    // 0xbd8a48: r0 = ProductCustomisation()
    //     0xbd8a48: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xbd8a4c: mov             x2, x0
    // 0xbd8a50: ldur            x0, [fp, #-0x38]
    // 0xbd8a54: stur            x2, [fp, #-0x48]
    // 0xbd8a58: StoreField: r2->field_b = r0
    //     0xbd8a58: stur            w0, [x2, #0xb]
    // 0xbd8a5c: ldur            x0, [fp, #-0x30]
    // 0xbd8a60: StoreField: r2->field_f = r0
    //     0xbd8a60: stur            w0, [x2, #0xf]
    // 0xbd8a64: ldur            x0, [fp, #-0x20]
    // 0xbd8a68: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd8a68: stur            w0, [x2, #0x17]
    // 0xbd8a6c: ldur            x0, [fp, #-0x18]
    // 0xbd8a70: StoreField: r2->field_23 = r0
    //     0xbd8a70: stur            w0, [x2, #0x23]
    // 0xbd8a74: ldur            x0, [fp, #-8]
    // 0xbd8a78: StoreField: r2->field_27 = r0
    //     0xbd8a78: stur            w0, [x2, #0x27]
    // 0xbd8a7c: ldur            x0, [fp, #-0x40]
    // 0xbd8a80: StoreField: r2->field_2b = r0
    //     0xbd8a80: stur            w0, [x2, #0x2b]
    // 0xbd8a84: ldur            x1, [fp, #-0x28]
    // 0xbd8a88: r0 = clear()
    //     0xbd8a88: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xbd8a8c: ldur            x0, [fp, #-0x28]
    // 0xbd8a90: LoadField: r1 = r0->field_b
    //     0xbd8a90: ldur            w1, [x0, #0xb]
    // 0xbd8a94: LoadField: r2 = r0->field_f
    //     0xbd8a94: ldur            w2, [x0, #0xf]
    // 0xbd8a98: DecompressPointer r2
    //     0xbd8a98: add             x2, x2, HEAP, lsl #32
    // 0xbd8a9c: LoadField: r3 = r2->field_b
    //     0xbd8a9c: ldur            w3, [x2, #0xb]
    // 0xbd8aa0: r2 = LoadInt32Instr(r1)
    //     0xbd8aa0: sbfx            x2, x1, #1, #0x1f
    // 0xbd8aa4: stur            x2, [fp, #-0x50]
    // 0xbd8aa8: r1 = LoadInt32Instr(r3)
    //     0xbd8aa8: sbfx            x1, x3, #1, #0x1f
    // 0xbd8aac: cmp             x2, x1
    // 0xbd8ab0: b.ne            #0xbd8abc
    // 0xbd8ab4: mov             x1, x0
    // 0xbd8ab8: r0 = _growToNextCapacity()
    //     0xbd8ab8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd8abc: ldur            x4, [fp, #-0x10]
    // 0xbd8ac0: ldur            x2, [fp, #-0x28]
    // 0xbd8ac4: ldur            x3, [fp, #-0x50]
    // 0xbd8ac8: add             x0, x3, #1
    // 0xbd8acc: lsl             x1, x0, #1
    // 0xbd8ad0: StoreField: r2->field_b = r1
    //     0xbd8ad0: stur            w1, [x2, #0xb]
    // 0xbd8ad4: LoadField: r1 = r2->field_f
    //     0xbd8ad4: ldur            w1, [x2, #0xf]
    // 0xbd8ad8: DecompressPointer r1
    //     0xbd8ad8: add             x1, x1, HEAP, lsl #32
    // 0xbd8adc: ldur            x0, [fp, #-0x48]
    // 0xbd8ae0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbd8ae0: add             x25, x1, x3, lsl #2
    //     0xbd8ae4: add             x25, x25, #0xf
    //     0xbd8ae8: str             w0, [x25]
    //     0xbd8aec: tbz             w0, #0, #0xbd8b08
    //     0xbd8af0: ldurb           w16, [x1, #-1]
    //     0xbd8af4: ldurb           w17, [x0, #-1]
    //     0xbd8af8: and             x16, x17, x16, lsr #2
    //     0xbd8afc: tst             x16, HEAP, lsr #32
    //     0xbd8b00: b.eq            #0xbd8b08
    //     0xbd8b04: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd8b08: LoadField: r0 = r4->field_f
    //     0xbd8b08: ldur            w0, [x4, #0xf]
    // 0xbd8b0c: DecompressPointer r0
    //     0xbd8b0c: add             x0, x0, HEAP, lsl #32
    // 0xbd8b10: LoadField: r1 = r0->field_b
    //     0xbd8b10: ldur            w1, [x0, #0xb]
    // 0xbd8b14: DecompressPointer r1
    //     0xbd8b14: add             x1, x1, HEAP, lsl #32
    // 0xbd8b18: cmp             w1, NULL
    // 0xbd8b1c: b.eq            #0xbd8c68
    // 0xbd8b20: LoadField: r0 = r1->field_b
    //     0xbd8b20: ldur            w0, [x1, #0xb]
    // 0xbd8b24: DecompressPointer r0
    //     0xbd8b24: add             x0, x0, HEAP, lsl #32
    // 0xbd8b28: cmp             w0, NULL
    // 0xbd8b2c: b.ne            #0xbd8b38
    // 0xbd8b30: r3 = Null
    //     0xbd8b30: mov             x3, NULL
    // 0xbd8b34: b               #0xbd8b40
    // 0xbd8b38: LoadField: r3 = r0->field_23
    //     0xbd8b38: ldur            w3, [x0, #0x23]
    // 0xbd8b3c: DecompressPointer r3
    //     0xbd8b3c: add             x3, x3, HEAP, lsl #32
    // 0xbd8b40: cmp             w0, NULL
    // 0xbd8b44: b.ne            #0xbd8b50
    // 0xbd8b48: r0 = Null
    //     0xbd8b48: mov             x0, NULL
    // 0xbd8b4c: b               #0xbd8b5c
    // 0xbd8b50: LoadField: r4 = r0->field_2b
    //     0xbd8b50: ldur            w4, [x0, #0x2b]
    // 0xbd8b54: DecompressPointer r4
    //     0xbd8b54: add             x4, x4, HEAP, lsl #32
    // 0xbd8b58: mov             x0, x4
    // 0xbd8b5c: cmp             w0, NULL
    // 0xbd8b60: b.ne            #0xbd8b68
    // 0xbd8b64: r0 = false
    //     0xbd8b64: add             x0, NULL, #0x30  ; false
    // 0xbd8b68: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xbd8b68: ldur            w4, [x1, #0x17]
    // 0xbd8b6c: DecompressPointer r4
    //     0xbd8b6c: add             x4, x4, HEAP, lsl #32
    // 0xbd8b70: ldr             x16, [fp, #0x10]
    // 0xbd8b74: stp             x16, x4, [SP, #0x18]
    // 0xbd8b78: stp             x2, x3, [SP, #8]
    // 0xbd8b7c: str             x0, [SP]
    // 0xbd8b80: r4 = 0
    //     0xbd8b80: movz            x4, #0
    // 0xbd8b84: ldr             x0, [SP, #0x20]
    // 0xbd8b88: r16 = UnlinkedCall_0x613b5c
    //     0xbd8b88: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a4f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd8b8c: add             x16, x16, #0x4f0
    // 0xbd8b90: ldp             x5, lr, [x16]
    // 0xbd8b94: blr             lr
    // 0xbd8b98: b               #0xbd8c3c
    // 0xbd8b9c: ldur            x4, [fp, #-0x10]
    // 0xbd8ba0: mov             x2, x1
    // 0xbd8ba4: mov             x1, x2
    // 0xbd8ba8: r0 = clear()
    //     0xbd8ba8: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xbd8bac: ldur            x0, [fp, #-0x10]
    // 0xbd8bb0: LoadField: r1 = r0->field_f
    //     0xbd8bb0: ldur            w1, [x0, #0xf]
    // 0xbd8bb4: DecompressPointer r1
    //     0xbd8bb4: add             x1, x1, HEAP, lsl #32
    // 0xbd8bb8: LoadField: r0 = r1->field_b
    //     0xbd8bb8: ldur            w0, [x1, #0xb]
    // 0xbd8bbc: DecompressPointer r0
    //     0xbd8bbc: add             x0, x0, HEAP, lsl #32
    // 0xbd8bc0: cmp             w0, NULL
    // 0xbd8bc4: b.eq            #0xbd8c6c
    // 0xbd8bc8: LoadField: r1 = r0->field_b
    //     0xbd8bc8: ldur            w1, [x0, #0xb]
    // 0xbd8bcc: DecompressPointer r1
    //     0xbd8bcc: add             x1, x1, HEAP, lsl #32
    // 0xbd8bd0: cmp             w1, NULL
    // 0xbd8bd4: b.ne            #0xbd8be0
    // 0xbd8bd8: r2 = Null
    //     0xbd8bd8: mov             x2, NULL
    // 0xbd8bdc: b               #0xbd8be8
    // 0xbd8be0: LoadField: r2 = r1->field_23
    //     0xbd8be0: ldur            w2, [x1, #0x23]
    // 0xbd8be4: DecompressPointer r2
    //     0xbd8be4: add             x2, x2, HEAP, lsl #32
    // 0xbd8be8: cmp             w1, NULL
    // 0xbd8bec: b.ne            #0xbd8bf8
    // 0xbd8bf0: r1 = Null
    //     0xbd8bf0: mov             x1, NULL
    // 0xbd8bf4: b               #0xbd8c04
    // 0xbd8bf8: LoadField: r3 = r1->field_2b
    //     0xbd8bf8: ldur            w3, [x1, #0x2b]
    // 0xbd8bfc: DecompressPointer r3
    //     0xbd8bfc: add             x3, x3, HEAP, lsl #32
    // 0xbd8c00: mov             x1, x3
    // 0xbd8c04: cmp             w1, NULL
    // 0xbd8c08: b.ne            #0xbd8c10
    // 0xbd8c0c: r1 = false
    //     0xbd8c0c: add             x1, NULL, #0x30  ; false
    // 0xbd8c10: LoadField: r3 = r0->field_1b
    //     0xbd8c10: ldur            w3, [x0, #0x1b]
    // 0xbd8c14: DecompressPointer r3
    //     0xbd8c14: add             x3, x3, HEAP, lsl #32
    // 0xbd8c18: stp             x2, x3, [SP, #0x10]
    // 0xbd8c1c: ldur            x16, [fp, #-0x28]
    // 0xbd8c20: stp             x1, x16, [SP]
    // 0xbd8c24: r4 = 0
    //     0xbd8c24: movz            x4, #0
    // 0xbd8c28: ldr             x0, [SP, #0x18]
    // 0xbd8c2c: r16 = UnlinkedCall_0x613b5c
    //     0xbd8c2c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a500] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd8c30: add             x16, x16, #0x500
    // 0xbd8c34: ldp             x5, lr, [x16]
    // 0xbd8c38: blr             lr
    // 0xbd8c3c: r0 = Null
    //     0xbd8c3c: mov             x0, NULL
    // 0xbd8c40: LeaveFrame
    //     0xbd8c40: mov             SP, fp
    //     0xbd8c44: ldp             fp, lr, [SP], #0x10
    // 0xbd8c48: ret
    //     0xbd8c48: ret             
    // 0xbd8c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd8c4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8c50: b               #0xbd86cc
    // 0xbd8c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8c5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8c60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8c64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8c68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd8c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8c6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4004, size: 0x20, field offset: 0xc
//   const constructor, 
class CustomisationText extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc807c4, size: 0x48
    // 0xc807c4: EnterFrame
    //     0xc807c4: stp             fp, lr, [SP, #-0x10]!
    //     0xc807c8: mov             fp, SP
    // 0xc807cc: AllocStack(0x8)
    //     0xc807cc: sub             SP, SP, #8
    // 0xc807d0: CheckStackOverflow
    //     0xc807d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc807d4: cmp             SP, x16
    //     0xc807d8: b.ls            #0xc80804
    // 0xc807dc: r1 = <CustomisationText>
    //     0xc807dc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c40] TypeArguments: <CustomisationText>
    //     0xc807e0: ldr             x1, [x1, #0xc40]
    // 0xc807e4: r0 = _CustomisationTextState()
    //     0xc807e4: bl              #0xc8080c  ; Allocate_CustomisationTextStateStub -> _CustomisationTextState (size=0x1c)
    // 0xc807e8: mov             x1, x0
    // 0xc807ec: stur            x0, [fp, #-8]
    // 0xc807f0: r0 = _CustomisationTextState()
    //     0xc807f0: bl              #0xc7b760  ; [package:customer_app/app/presentation/views/basic/customization/customisation_text.dart] _CustomisationTextState::_CustomisationTextState
    // 0xc807f4: ldur            x0, [fp, #-8]
    // 0xc807f8: LeaveFrame
    //     0xc807f8: mov             SP, fp
    //     0xc807fc: ldp             fp, lr, [SP], #0x10
    // 0xc80800: ret
    //     0xc80800: ret             
    // 0xc80804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80804: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80808: b               #0xc807dc
  }
}
