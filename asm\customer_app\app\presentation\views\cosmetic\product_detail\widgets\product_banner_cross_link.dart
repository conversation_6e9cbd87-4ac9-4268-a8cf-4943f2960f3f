// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_banner_cross_link.dart

// class id: 1049311, size: 0x8
class :: {
}

// class id: 3407, size: 0x14, field offset: 0x14
class _ProductBannerCrossLinkState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb0660c, size: 0x16c
    // 0xb0660c: EnterFrame
    //     0xb0660c: stp             fp, lr, [SP, #-0x10]!
    //     0xb06610: mov             fp, SP
    // 0xb06614: AllocStack(0x40)
    //     0xb06614: sub             SP, SP, #0x40
    // 0xb06618: SetupParameters(_ProductBannerCrossLinkState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb06618: mov             x0, x1
    //     0xb0661c: stur            x1, [fp, #-8]
    //     0xb06620: mov             x1, x2
    //     0xb06624: stur            x2, [fp, #-0x10]
    // 0xb06628: CheckStackOverflow
    //     0xb06628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0662c: cmp             SP, x16
    //     0xb06630: b.ls            #0xb0676c
    // 0xb06634: r1 = 1
    //     0xb06634: movz            x1, #0x1
    // 0xb06638: r0 = AllocateContext()
    //     0xb06638: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0663c: mov             x2, x0
    // 0xb06640: ldur            x0, [fp, #-8]
    // 0xb06644: stur            x2, [fp, #-0x38]
    // 0xb06648: StoreField: r2->field_f = r0
    //     0xb06648: stur            w0, [x2, #0xf]
    // 0xb0664c: LoadField: r1 = r0->field_b
    //     0xb0664c: ldur            w1, [x0, #0xb]
    // 0xb06650: DecompressPointer r1
    //     0xb06650: add             x1, x1, HEAP, lsl #32
    // 0xb06654: cmp             w1, NULL
    // 0xb06658: b.eq            #0xb06774
    // 0xb0665c: LoadField: r0 = r1->field_b
    //     0xb0665c: ldur            w0, [x1, #0xb]
    // 0xb06660: DecompressPointer r0
    //     0xb06660: add             x0, x0, HEAP, lsl #32
    // 0xb06664: stur            x0, [fp, #-0x30]
    // 0xb06668: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb06668: ldur            w3, [x1, #0x17]
    // 0xb0666c: DecompressPointer r3
    //     0xb0666c: add             x3, x3, HEAP, lsl #32
    // 0xb06670: stur            x3, [fp, #-0x28]
    // 0xb06674: LoadField: r4 = r1->field_13
    //     0xb06674: ldur            w4, [x1, #0x13]
    // 0xb06678: DecompressPointer r4
    //     0xb06678: add             x4, x4, HEAP, lsl #32
    // 0xb0667c: stur            x4, [fp, #-0x20]
    // 0xb06680: LoadField: r5 = r1->field_f
    //     0xb06680: ldur            w5, [x1, #0xf]
    // 0xb06684: DecompressPointer r5
    //     0xb06684: add             x5, x5, HEAP, lsl #32
    // 0xb06688: stur            x5, [fp, #-0x18]
    // 0xb0668c: LoadField: r6 = r1->field_27
    //     0xb0668c: ldur            w6, [x1, #0x27]
    // 0xb06690: DecompressPointer r6
    //     0xb06690: add             x6, x6, HEAP, lsl #32
    // 0xb06694: ldur            x1, [fp, #-0x10]
    // 0xb06698: stur            x6, [fp, #-8]
    // 0xb0669c: r0 = of()
    //     0xb0669c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb066a0: LoadField: r1 = r0->field_5b
    //     0xb066a0: ldur            w1, [x0, #0x5b]
    // 0xb066a4: DecompressPointer r1
    //     0xb066a4: add             x1, x1, HEAP, lsl #32
    // 0xb066a8: stur            x1, [fp, #-0x10]
    // 0xb066ac: r0 = ProductBannerCrossWidget()
    //     0xb066ac: bl              #0xa79dc0  ; AllocateProductBannerCrossWidgetStub -> ProductBannerCrossWidget (size=0x54)
    // 0xb066b0: d0 = 200.000000
    //     0xb066b0: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xb066b4: ldr             d0, [x17, #0x360]
    // 0xb066b8: stur            x0, [fp, #-0x40]
    // 0xb066bc: StoreField: r0->field_b = d0
    //     0xb066bc: stur            d0, [x0, #0xb]
    // 0xb066c0: r1 = true
    //     0xb066c0: add             x1, NULL, #0x20  ; true
    // 0xb066c4: StoreField: r0->field_13 = r1
    //     0xb066c4: stur            w1, [x0, #0x13]
    // 0xb066c8: r1 = Instance_Duration
    //     0xb066c8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0xb066cc: ldr             x1, [x1, #0xbd8]
    // 0xb066d0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb066d0: stur            w1, [x0, #0x17]
    // 0xb066d4: ldur            x1, [fp, #-0x10]
    // 0xb066d8: StoreField: r0->field_1b = r1
    //     0xb066d8: stur            w1, [x0, #0x1b]
    // 0xb066dc: r1 = Instance_Color
    //     0xb066dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb066e0: ldr             x1, [x1, #0x90]
    // 0xb066e4: StoreField: r0->field_1f = r1
    //     0xb066e4: stur            w1, [x0, #0x1f]
    // 0xb066e8: d0 = 48.000000
    //     0xb066e8: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0xb066ec: StoreField: r0->field_27 = d0
    //     0xb066ec: stur            d0, [x0, #0x27]
    // 0xb066f0: ldur            x1, [fp, #-0x30]
    // 0xb066f4: StoreField: r0->field_2f = r1
    //     0xb066f4: stur            w1, [x0, #0x2f]
    // 0xb066f8: ldur            x2, [fp, #-0x38]
    // 0xb066fc: r1 = Function '<anonymous closure>':.
    //     0xb066fc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57df0] AnonymousClosure: (0xb0751c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::build (0xb0660c)
    //     0xb06700: ldr             x1, [x1, #0xdf0]
    // 0xb06704: r0 = AllocateClosure()
    //     0xb06704: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb06708: mov             x1, x0
    // 0xb0670c: ldur            x0, [fp, #-0x40]
    // 0xb06710: StoreField: r0->field_33 = r1
    //     0xb06710: stur            w1, [x0, #0x33]
    // 0xb06714: r1 = "product_page"
    //     0xb06714: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb06718: ldr             x1, [x1, #0x480]
    // 0xb0671c: StoreField: r0->field_37 = r1
    //     0xb0671c: stur            w1, [x0, #0x37]
    // 0xb06720: ldur            x2, [fp, #-0x28]
    // 0xb06724: StoreField: r0->field_3b = r2
    //     0xb06724: stur            w2, [x0, #0x3b]
    // 0xb06728: StoreField: r0->field_3f = r1
    //     0xb06728: stur            w1, [x0, #0x3f]
    // 0xb0672c: ldur            x1, [fp, #-0x20]
    // 0xb06730: StoreField: r0->field_43 = r1
    //     0xb06730: stur            w1, [x0, #0x43]
    // 0xb06734: ldur            x1, [fp, #-0x18]
    // 0xb06738: StoreField: r0->field_47 = r1
    //     0xb06738: stur            w1, [x0, #0x47]
    // 0xb0673c: ldur            x1, [fp, #-8]
    // 0xb06740: StoreField: r0->field_4b = r1
    //     0xb06740: stur            w1, [x0, #0x4b]
    // 0xb06744: ldur            x2, [fp, #-0x38]
    // 0xb06748: r1 = Function '<anonymous closure>':.
    //     0xb06748: add             x1, PP, #0x57, lsl #12  ; [pp+0x57df8] AnonymousClosure: (0xb0679c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::build (0xb0660c)
    //     0xb0674c: ldr             x1, [x1, #0xdf8]
    // 0xb06750: r0 = AllocateClosure()
    //     0xb06750: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb06754: mov             x1, x0
    // 0xb06758: ldur            x0, [fp, #-0x40]
    // 0xb0675c: StoreField: r0->field_4f = r1
    //     0xb0675c: stur            w1, [x0, #0x4f]
    // 0xb06760: LeaveFrame
    //     0xb06760: mov             SP, fp
    //     0xb06764: ldp             fp, lr, [SP], #0x10
    // 0xb06768: ret
    //     0xb06768: ret             
    // 0xb0676c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0676c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb06770: b               #0xb06634
    // 0xb06774: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb06774: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, dynamic, int) {
    // ** addr: 0xb0679c, size: 0x80
    // 0xb0679c: EnterFrame
    //     0xb0679c: stp             fp, lr, [SP, #-0x10]!
    //     0xb067a0: mov             fp, SP
    // 0xb067a4: AllocStack(0x8)
    //     0xb067a4: sub             SP, SP, #8
    // 0xb067a8: SetupParameters()
    //     0xb067a8: ldr             x0, [fp, #0x20]
    //     0xb067ac: ldur            w1, [x0, #0x17]
    //     0xb067b0: add             x1, x1, HEAP, lsl #32
    // 0xb067b4: CheckStackOverflow
    //     0xb067b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb067b8: cmp             SP, x16
    //     0xb067bc: b.ls            #0xb06814
    // 0xb067c0: LoadField: r3 = r1->field_f
    //     0xb067c0: ldur            w3, [x1, #0xf]
    // 0xb067c4: DecompressPointer r3
    //     0xb067c4: add             x3, x3, HEAP, lsl #32
    // 0xb067c8: ldr             x0, [fp, #0x18]
    // 0xb067cc: stur            x3, [fp, #-8]
    // 0xb067d0: r2 = Null
    //     0xb067d0: mov             x2, NULL
    // 0xb067d4: r1 = Null
    //     0xb067d4: mov             x1, NULL
    // 0xb067d8: r8 = List<WidgetEntity>
    //     0xb067d8: add             x8, PP, #0x52, lsl #12  ; [pp+0x52bf0] Type: List<WidgetEntity>
    //     0xb067dc: ldr             x8, [x8, #0xbf0]
    // 0xb067e0: r3 = Null
    //     0xb067e0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57e00] Null
    //     0xb067e4: ldr             x3, [x3, #0xe00]
    // 0xb067e8: r0 = List<WidgetEntity>()
    //     0xb067e8: bl              #0xa7aae4  ; IsType_List<WidgetEntity>_Stub
    // 0xb067ec: ldr             x0, [fp, #0x10]
    // 0xb067f0: r3 = LoadInt32Instr(r0)
    //     0xb067f0: sbfx            x3, x0, #1, #0x1f
    //     0xb067f4: tbz             w0, #0, #0xb067fc
    //     0xb067f8: ldur            x3, [x0, #7]
    // 0xb067fc: ldur            x1, [fp, #-8]
    // 0xb06800: ldr             x2, [fp, #0x18]
    // 0xb06804: r0 = bannerSlider()
    //     0xb06804: bl              #0xb0681c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider
    // 0xb06808: LeaveFrame
    //     0xb06808: mov             SP, fp
    //     0xb0680c: ldp             fp, lr, [SP], #0x10
    // 0xb06810: ret
    //     0xb06810: ret             
    // 0xb06814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb06814: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb06818: b               #0xb067c0
  }
  _ bannerSlider(/* No info */) {
    // ** addr: 0xb0681c, size: 0xc20
    // 0xb0681c: EnterFrame
    //     0xb0681c: stp             fp, lr, [SP, #-0x10]!
    //     0xb06820: mov             fp, SP
    // 0xb06824: AllocStack(0x78)
    //     0xb06824: sub             SP, SP, #0x78
    // 0xb06828: SetupParameters(_ProductBannerCrossLinkState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb06828: stur            x1, [fp, #-8]
    //     0xb0682c: stur            x2, [fp, #-0x10]
    //     0xb06830: stur            x3, [fp, #-0x18]
    // 0xb06834: CheckStackOverflow
    //     0xb06834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb06838: cmp             SP, x16
    //     0xb0683c: b.ls            #0xb073e0
    // 0xb06840: r1 = 2
    //     0xb06840: movz            x1, #0x2
    // 0xb06844: r0 = AllocateContext()
    //     0xb06844: bl              #0x16f6108  ; AllocateContextStub
    // 0xb06848: mov             x3, x0
    // 0xb0684c: ldur            x2, [fp, #-8]
    // 0xb06850: stur            x3, [fp, #-0x20]
    // 0xb06854: StoreField: r3->field_f = r2
    //     0xb06854: stur            w2, [x3, #0xf]
    // 0xb06858: ldur            x4, [fp, #-0x18]
    // 0xb0685c: r0 = BoxInt64Instr(r4)
    //     0xb0685c: sbfiz           x0, x4, #1, #0x1f
    //     0xb06860: cmp             x4, x0, asr #1
    //     0xb06864: b.eq            #0xb06870
    //     0xb06868: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb0686c: stur            x4, [x0, #7]
    // 0xb06870: StoreField: r3->field_13 = r0
    //     0xb06870: stur            w0, [x3, #0x13]
    // 0xb06874: ldur            x1, [fp, #-0x10]
    // 0xb06878: r4 = LoadClassIdInstr(r1)
    //     0xb06878: ldur            x4, [x1, #-1]
    //     0xb0687c: ubfx            x4, x4, #0xc, #0x14
    // 0xb06880: stp             x0, x1, [SP]
    // 0xb06884: mov             x0, x4
    // 0xb06888: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06888: sub             lr, x0, #0xb7
    //     0xb0688c: ldr             lr, [x21, lr, lsl #3]
    //     0xb06890: blr             lr
    // 0xb06894: r17 = 299
    //     0xb06894: movz            x17, #0x12b
    // 0xb06898: ldr             w1, [x0, x17]
    // 0xb0689c: DecompressPointer r1
    //     0xb0689c: add             x1, x1, HEAP, lsl #32
    // 0xb068a0: cmp             w1, NULL
    // 0xb068a4: b.ne            #0xb068d4
    // 0xb068a8: ldur            x3, [fp, #-8]
    // 0xb068ac: ldur            x5, [fp, #-0x10]
    // 0xb068b0: ldur            x4, [fp, #-0x20]
    // 0xb068b4: r2 = 4
    //     0xb068b4: movz            x2, #0x4
    // 0xb068b8: r7 = Instance_Clip
    //     0xb068b8: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb068bc: ldr             x7, [x7, #0x7e0]
    // 0xb068c0: r6 = Instance_StackFit
    //     0xb068c0: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb068c4: ldr             x6, [x6, #0xfa8]
    // 0xb068c8: r0 = Instance_Clip
    //     0xb068c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb068cc: ldr             x0, [x0, #0x138]
    // 0xb068d0: b               #0xb06c50
    // 0xb068d4: tbnz            w1, #4, #0xb06c28
    // 0xb068d8: ldur            x0, [fp, #-8]
    // 0xb068dc: ldur            x3, [fp, #-0x10]
    // 0xb068e0: ldur            x2, [fp, #-0x20]
    // 0xb068e4: LoadField: r1 = r0->field_b
    //     0xb068e4: ldur            w1, [x0, #0xb]
    // 0xb068e8: DecompressPointer r1
    //     0xb068e8: add             x1, x1, HEAP, lsl #32
    // 0xb068ec: cmp             w1, NULL
    // 0xb068f0: b.eq            #0xb073e8
    // 0xb068f4: LoadField: r4 = r1->field_2f
    //     0xb068f4: ldur            w4, [x1, #0x2f]
    // 0xb068f8: DecompressPointer r4
    //     0xb068f8: add             x4, x4, HEAP, lsl #32
    // 0xb068fc: stur            x4, [fp, #-0x28]
    // 0xb06900: LoadField: r1 = r0->field_f
    //     0xb06900: ldur            w1, [x0, #0xf]
    // 0xb06904: DecompressPointer r1
    //     0xb06904: add             x1, x1, HEAP, lsl #32
    // 0xb06908: cmp             w1, NULL
    // 0xb0690c: b.eq            #0xb073ec
    // 0xb06910: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb06910: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb06914: r0 = _of()
    //     0xb06914: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb06918: LoadField: r1 = r0->field_7
    //     0xb06918: ldur            w1, [x0, #7]
    // 0xb0691c: DecompressPointer r1
    //     0xb0691c: add             x1, x1, HEAP, lsl #32
    // 0xb06920: LoadField: d0 = r1->field_7
    //     0xb06920: ldur            d0, [x1, #7]
    // 0xb06924: ldur            x1, [fp, #-8]
    // 0xb06928: stur            d0, [fp, #-0x60]
    // 0xb0692c: LoadField: r0 = r1->field_b
    //     0xb0692c: ldur            w0, [x1, #0xb]
    // 0xb06930: DecompressPointer r0
    //     0xb06930: add             x0, x0, HEAP, lsl #32
    // 0xb06934: cmp             w0, NULL
    // 0xb06938: b.eq            #0xb073f0
    // 0xb0693c: LoadField: r2 = r0->field_33
    //     0xb0693c: ldur            w2, [x0, #0x33]
    // 0xb06940: DecompressPointer r2
    //     0xb06940: add             x2, x2, HEAP, lsl #32
    // 0xb06944: ldur            x3, [fp, #-0x20]
    // 0xb06948: stur            x2, [fp, #-0x30]
    // 0xb0694c: LoadField: r0 = r3->field_13
    //     0xb0694c: ldur            w0, [x3, #0x13]
    // 0xb06950: DecompressPointer r0
    //     0xb06950: add             x0, x0, HEAP, lsl #32
    // 0xb06954: ldur            x4, [fp, #-0x10]
    // 0xb06958: r5 = LoadClassIdInstr(r4)
    //     0xb06958: ldur            x5, [x4, #-1]
    //     0xb0695c: ubfx            x5, x5, #0xc, #0x14
    // 0xb06960: stp             x0, x4, [SP]
    // 0xb06964: mov             x0, x5
    // 0xb06968: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06968: sub             lr, x0, #0xb7
    //     0xb0696c: ldr             lr, [x21, lr, lsl #3]
    //     0xb06970: blr             lr
    // 0xb06974: LoadField: r1 = r0->field_13
    //     0xb06974: ldur            w1, [x0, #0x13]
    // 0xb06978: DecompressPointer r1
    //     0xb06978: add             x1, x1, HEAP, lsl #32
    // 0xb0697c: cmp             w1, NULL
    // 0xb06980: b.ne            #0xb0698c
    // 0xb06984: r5 = ""
    //     0xb06984: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb06988: b               #0xb06990
    // 0xb0698c: mov             x5, x1
    // 0xb06990: ldur            x4, [fp, #-0x10]
    // 0xb06994: ldur            x3, [fp, #-0x20]
    // 0xb06998: ldur            x0, [fp, #-0x30]
    // 0xb0699c: ldur            d0, [fp, #-0x60]
    // 0xb069a0: stur            x5, [fp, #-0x38]
    // 0xb069a4: r1 = Function '<anonymous closure>':.
    //     0xb069a4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e10] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb069a8: ldr             x1, [x1, #0xe10]
    // 0xb069ac: r2 = Null
    //     0xb069ac: mov             x2, NULL
    // 0xb069b0: r0 = AllocateClosure()
    //     0xb069b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb069b4: r1 = Function '<anonymous closure>':.
    //     0xb069b4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e18] AnonymousClosure: (0xa422dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xb069b8: ldr             x1, [x1, #0xe18]
    // 0xb069bc: r2 = Null
    //     0xb069bc: mov             x2, NULL
    // 0xb069c0: stur            x0, [fp, #-0x40]
    // 0xb069c4: r0 = AllocateClosure()
    //     0xb069c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb069c8: stur            x0, [fp, #-0x48]
    // 0xb069cc: r0 = CachedNetworkImage()
    //     0xb069cc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb069d0: stur            x0, [fp, #-0x50]
    // 0xb069d4: r16 = Instance_BoxFit
    //     0xb069d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb069d8: ldr             x16, [x16, #0x118]
    // 0xb069dc: ldur            lr, [fp, #-0x40]
    // 0xb069e0: stp             lr, x16, [SP, #8]
    // 0xb069e4: ldur            x16, [fp, #-0x48]
    // 0xb069e8: str             x16, [SP]
    // 0xb069ec: mov             x1, x0
    // 0xb069f0: ldur            x2, [fp, #-0x38]
    // 0xb069f4: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb069f4: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb069f8: ldr             x4, [x4, #0x638]
    // 0xb069fc: r0 = CachedNetworkImage()
    //     0xb069fc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb06a00: r0 = ClipRRect()
    //     0xb06a00: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb06a04: mov             x1, x0
    // 0xb06a08: ldur            x0, [fp, #-0x30]
    // 0xb06a0c: stur            x1, [fp, #-0x38]
    // 0xb06a10: StoreField: r1->field_f = r0
    //     0xb06a10: stur            w0, [x1, #0xf]
    // 0xb06a14: r0 = Instance_Clip
    //     0xb06a14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb06a18: ldr             x0, [x0, #0x138]
    // 0xb06a1c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb06a1c: stur            w0, [x1, #0x17]
    // 0xb06a20: ldur            x0, [fp, #-0x50]
    // 0xb06a24: StoreField: r1->field_b = r0
    //     0xb06a24: stur            w0, [x1, #0xb]
    // 0xb06a28: ldur            d0, [fp, #-0x60]
    // 0xb06a2c: r0 = inline_Allocate_Double()
    //     0xb06a2c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb06a30: add             x0, x0, #0x10
    //     0xb06a34: cmp             x2, x0
    //     0xb06a38: b.ls            #0xb073f4
    //     0xb06a3c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb06a40: sub             x0, x0, #0xf
    //     0xb06a44: movz            x2, #0xe15c
    //     0xb06a48: movk            x2, #0x3, lsl #16
    //     0xb06a4c: stur            x2, [x0, #-1]
    // 0xb06a50: StoreField: r0->field_7 = d0
    //     0xb06a50: stur            d0, [x0, #7]
    // 0xb06a54: stur            x0, [fp, #-0x30]
    // 0xb06a58: r0 = Container()
    //     0xb06a58: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb06a5c: stur            x0, [fp, #-0x40]
    // 0xb06a60: ldur            x16, [fp, #-0x28]
    // 0xb06a64: ldur            lr, [fp, #-0x30]
    // 0xb06a68: stp             lr, x16, [SP, #8]
    // 0xb06a6c: ldur            x16, [fp, #-0x38]
    // 0xb06a70: str             x16, [SP]
    // 0xb06a74: mov             x1, x0
    // 0xb06a78: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb06a78: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb06a7c: ldr             x4, [x4, #0x1b8]
    // 0xb06a80: r0 = Container()
    //     0xb06a80: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb06a84: ldur            x2, [fp, #-0x20]
    // 0xb06a88: LoadField: r0 = r2->field_13
    //     0xb06a88: ldur            w0, [x2, #0x13]
    // 0xb06a8c: DecompressPointer r0
    //     0xb06a8c: add             x0, x0, HEAP, lsl #32
    // 0xb06a90: ldur            x1, [fp, #-0x10]
    // 0xb06a94: r3 = LoadClassIdInstr(r1)
    //     0xb06a94: ldur            x3, [x1, #-1]
    //     0xb06a98: ubfx            x3, x3, #0xc, #0x14
    // 0xb06a9c: stp             x0, x1, [SP]
    // 0xb06aa0: mov             x0, x3
    // 0xb06aa4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06aa4: sub             lr, x0, #0xb7
    //     0xb06aa8: ldr             lr, [x21, lr, lsl #3]
    //     0xb06aac: blr             lr
    // 0xb06ab0: LoadField: r1 = r0->field_7
    //     0xb06ab0: ldur            w1, [x0, #7]
    // 0xb06ab4: DecompressPointer r1
    //     0xb06ab4: add             x1, x1, HEAP, lsl #32
    // 0xb06ab8: cmp             w1, NULL
    // 0xb06abc: b.ne            #0xb06ac8
    // 0xb06ac0: r4 = ""
    //     0xb06ac0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb06ac4: b               #0xb06acc
    // 0xb06ac8: mov             x4, x1
    // 0xb06acc: ldur            x3, [fp, #-8]
    // 0xb06ad0: ldur            x0, [fp, #-0x10]
    // 0xb06ad4: ldur            x2, [fp, #-0x20]
    // 0xb06ad8: stur            x4, [fp, #-0x28]
    // 0xb06adc: LoadField: r1 = r3->field_f
    //     0xb06adc: ldur            w1, [x3, #0xf]
    // 0xb06ae0: DecompressPointer r1
    //     0xb06ae0: add             x1, x1, HEAP, lsl #32
    // 0xb06ae4: cmp             w1, NULL
    // 0xb06ae8: b.eq            #0xb0740c
    // 0xb06aec: r0 = of()
    //     0xb06aec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb06af0: LoadField: r1 = r0->field_87
    //     0xb06af0: ldur            w1, [x0, #0x87]
    // 0xb06af4: DecompressPointer r1
    //     0xb06af4: add             x1, x1, HEAP, lsl #32
    // 0xb06af8: LoadField: r2 = r1->field_7
    //     0xb06af8: ldur            w2, [x1, #7]
    // 0xb06afc: DecompressPointer r2
    //     0xb06afc: add             x2, x2, HEAP, lsl #32
    // 0xb06b00: ldur            x4, [fp, #-0x20]
    // 0xb06b04: stur            x2, [fp, #-0x30]
    // 0xb06b08: LoadField: r0 = r4->field_13
    //     0xb06b08: ldur            w0, [x4, #0x13]
    // 0xb06b0c: DecompressPointer r0
    //     0xb06b0c: add             x0, x0, HEAP, lsl #32
    // 0xb06b10: ldur            x5, [fp, #-0x10]
    // 0xb06b14: r1 = LoadClassIdInstr(r5)
    //     0xb06b14: ldur            x1, [x5, #-1]
    //     0xb06b18: ubfx            x1, x1, #0xc, #0x14
    // 0xb06b1c: stp             x0, x5, [SP]
    // 0xb06b20: mov             x0, x1
    // 0xb06b24: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06b24: sub             lr, x0, #0xb7
    //     0xb06b28: ldr             lr, [x21, lr, lsl #3]
    //     0xb06b2c: blr             lr
    // 0xb06b30: r17 = 307
    //     0xb06b30: movz            x17, #0x133
    // 0xb06b34: ldr             w1, [x0, x17]
    // 0xb06b38: DecompressPointer r1
    //     0xb06b38: add             x1, x1, HEAP, lsl #32
    // 0xb06b3c: cmp             w1, NULL
    // 0xb06b40: b.ne            #0xb06b4c
    // 0xb06b44: r0 = Null
    //     0xb06b44: mov             x0, NULL
    // 0xb06b48: b               #0xb06b50
    // 0xb06b4c: r0 = ColorExtension.toColor()
    //     0xb06b4c: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0xb06b50: cmp             w0, NULL
    // 0xb06b54: b.ne            #0xb06b60
    // 0xb06b58: r1 = Instance_Color
    //     0xb06b58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb06b5c: b               #0xb06b64
    // 0xb06b60: mov             x1, x0
    // 0xb06b64: ldur            x2, [fp, #-0x40]
    // 0xb06b68: ldur            x0, [fp, #-0x28]
    // 0xb06b6c: r16 = 16.000000
    //     0xb06b6c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb06b70: ldr             x16, [x16, #0x188]
    // 0xb06b74: stp             x16, x1, [SP]
    // 0xb06b78: ldur            x1, [fp, #-0x30]
    // 0xb06b7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb06b7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb06b80: ldr             x4, [x4, #0x9b8]
    // 0xb06b84: r0 = copyWith()
    //     0xb06b84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb06b88: stur            x0, [fp, #-0x30]
    // 0xb06b8c: r0 = Text()
    //     0xb06b8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb06b90: mov             x3, x0
    // 0xb06b94: ldur            x0, [fp, #-0x28]
    // 0xb06b98: stur            x3, [fp, #-0x38]
    // 0xb06b9c: StoreField: r3->field_b = r0
    //     0xb06b9c: stur            w0, [x3, #0xb]
    // 0xb06ba0: ldur            x0, [fp, #-0x30]
    // 0xb06ba4: StoreField: r3->field_13 = r0
    //     0xb06ba4: stur            w0, [x3, #0x13]
    // 0xb06ba8: r1 = Null
    //     0xb06ba8: mov             x1, NULL
    // 0xb06bac: r2 = 4
    //     0xb06bac: movz            x2, #0x4
    // 0xb06bb0: r0 = AllocateArray()
    //     0xb06bb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb06bb4: mov             x2, x0
    // 0xb06bb8: ldur            x0, [fp, #-0x40]
    // 0xb06bbc: stur            x2, [fp, #-0x28]
    // 0xb06bc0: StoreField: r2->field_f = r0
    //     0xb06bc0: stur            w0, [x2, #0xf]
    // 0xb06bc4: ldur            x0, [fp, #-0x38]
    // 0xb06bc8: StoreField: r2->field_13 = r0
    //     0xb06bc8: stur            w0, [x2, #0x13]
    // 0xb06bcc: r1 = <Widget>
    //     0xb06bcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb06bd0: r0 = AllocateGrowableArray()
    //     0xb06bd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb06bd4: mov             x1, x0
    // 0xb06bd8: ldur            x0, [fp, #-0x28]
    // 0xb06bdc: stur            x1, [fp, #-0x30]
    // 0xb06be0: StoreField: r1->field_f = r0
    //     0xb06be0: stur            w0, [x1, #0xf]
    // 0xb06be4: r2 = 4
    //     0xb06be4: movz            x2, #0x4
    // 0xb06be8: StoreField: r1->field_b = r2
    //     0xb06be8: stur            w2, [x1, #0xb]
    // 0xb06bec: r0 = Stack()
    //     0xb06bec: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb06bf0: mov             x1, x0
    // 0xb06bf4: r0 = Instance_Alignment
    //     0xb06bf4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb06bf8: ldr             x0, [x0, #0xb10]
    // 0xb06bfc: StoreField: r1->field_f = r0
    //     0xb06bfc: stur            w0, [x1, #0xf]
    // 0xb06c00: r6 = Instance_StackFit
    //     0xb06c00: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb06c04: ldr             x6, [x6, #0xfa8]
    // 0xb06c08: ArrayStore: r1[0] = r6  ; List_4
    //     0xb06c08: stur            w6, [x1, #0x17]
    // 0xb06c0c: r7 = Instance_Clip
    //     0xb06c0c: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb06c10: ldr             x7, [x7, #0x7e0]
    // 0xb06c14: StoreField: r1->field_1b = r7
    //     0xb06c14: stur            w7, [x1, #0x1b]
    // 0xb06c18: ldur            x0, [fp, #-0x30]
    // 0xb06c1c: StoreField: r1->field_b = r0
    //     0xb06c1c: stur            w0, [x1, #0xb]
    // 0xb06c20: mov             x0, x1
    // 0xb06c24: b               #0xb073d4
    // 0xb06c28: ldur            x3, [fp, #-8]
    // 0xb06c2c: ldur            x5, [fp, #-0x10]
    // 0xb06c30: ldur            x4, [fp, #-0x20]
    // 0xb06c34: r2 = 4
    //     0xb06c34: movz            x2, #0x4
    // 0xb06c38: r7 = Instance_Clip
    //     0xb06c38: add             x7, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb06c3c: ldr             x7, [x7, #0x7e0]
    // 0xb06c40: r6 = Instance_StackFit
    //     0xb06c40: add             x6, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb06c44: ldr             x6, [x6, #0xfa8]
    // 0xb06c48: r0 = Instance_Clip
    //     0xb06c48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb06c4c: ldr             x0, [x0, #0x138]
    // 0xb06c50: LoadField: r1 = r3->field_f
    //     0xb06c50: ldur            w1, [x3, #0xf]
    // 0xb06c54: DecompressPointer r1
    //     0xb06c54: add             x1, x1, HEAP, lsl #32
    // 0xb06c58: cmp             w1, NULL
    // 0xb06c5c: b.eq            #0xb07410
    // 0xb06c60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb06c60: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb06c64: r0 = _of()
    //     0xb06c64: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb06c68: LoadField: r1 = r0->field_7
    //     0xb06c68: ldur            w1, [x0, #7]
    // 0xb06c6c: DecompressPointer r1
    //     0xb06c6c: add             x1, x1, HEAP, lsl #32
    // 0xb06c70: LoadField: d0 = r1->field_7
    //     0xb06c70: ldur            d0, [x1, #7]
    // 0xb06c74: ldur            x1, [fp, #-8]
    // 0xb06c78: stur            d0, [fp, #-0x60]
    // 0xb06c7c: LoadField: r0 = r1->field_b
    //     0xb06c7c: ldur            w0, [x1, #0xb]
    // 0xb06c80: DecompressPointer r0
    //     0xb06c80: add             x0, x0, HEAP, lsl #32
    // 0xb06c84: cmp             w0, NULL
    // 0xb06c88: b.eq            #0xb07414
    // 0xb06c8c: LoadField: r2 = r0->field_2f
    //     0xb06c8c: ldur            w2, [x0, #0x2f]
    // 0xb06c90: DecompressPointer r2
    //     0xb06c90: add             x2, x2, HEAP, lsl #32
    // 0xb06c94: stur            x2, [fp, #-0x30]
    // 0xb06c98: LoadField: r3 = r0->field_33
    //     0xb06c98: ldur            w3, [x0, #0x33]
    // 0xb06c9c: DecompressPointer r3
    //     0xb06c9c: add             x3, x3, HEAP, lsl #32
    // 0xb06ca0: ldur            x4, [fp, #-0x20]
    // 0xb06ca4: stur            x3, [fp, #-0x28]
    // 0xb06ca8: LoadField: r0 = r4->field_13
    //     0xb06ca8: ldur            w0, [x4, #0x13]
    // 0xb06cac: DecompressPointer r0
    //     0xb06cac: add             x0, x0, HEAP, lsl #32
    // 0xb06cb0: ldur            x5, [fp, #-0x10]
    // 0xb06cb4: r6 = LoadClassIdInstr(r5)
    //     0xb06cb4: ldur            x6, [x5, #-1]
    //     0xb06cb8: ubfx            x6, x6, #0xc, #0x14
    // 0xb06cbc: stp             x0, x5, [SP]
    // 0xb06cc0: mov             x0, x6
    // 0xb06cc4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06cc4: sub             lr, x0, #0xb7
    //     0xb06cc8: ldr             lr, [x21, lr, lsl #3]
    //     0xb06ccc: blr             lr
    // 0xb06cd0: LoadField: r1 = r0->field_13
    //     0xb06cd0: ldur            w1, [x0, #0x13]
    // 0xb06cd4: DecompressPointer r1
    //     0xb06cd4: add             x1, x1, HEAP, lsl #32
    // 0xb06cd8: cmp             w1, NULL
    // 0xb06cdc: b.ne            #0xb06ce8
    // 0xb06ce0: r5 = ""
    //     0xb06ce0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb06ce4: b               #0xb06cec
    // 0xb06ce8: mov             x5, x1
    // 0xb06cec: ldur            x4, [fp, #-0x10]
    // 0xb06cf0: ldur            x3, [fp, #-0x20]
    // 0xb06cf4: ldur            x0, [fp, #-0x28]
    // 0xb06cf8: ldur            d0, [fp, #-0x60]
    // 0xb06cfc: stur            x5, [fp, #-0x38]
    // 0xb06d00: r1 = Function '<anonymous closure>':.
    //     0xb06d00: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e20] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb06d04: ldr             x1, [x1, #0xe20]
    // 0xb06d08: r2 = Null
    //     0xb06d08: mov             x2, NULL
    // 0xb06d0c: r0 = AllocateClosure()
    //     0xb06d0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb06d10: r1 = Function '<anonymous closure>':.
    //     0xb06d10: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e28] AnonymousClosure: (0xa422dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xb06d14: ldr             x1, [x1, #0xe28]
    // 0xb06d18: r2 = Null
    //     0xb06d18: mov             x2, NULL
    // 0xb06d1c: stur            x0, [fp, #-0x40]
    // 0xb06d20: r0 = AllocateClosure()
    //     0xb06d20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb06d24: stur            x0, [fp, #-0x48]
    // 0xb06d28: r0 = CachedNetworkImage()
    //     0xb06d28: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb06d2c: stur            x0, [fp, #-0x50]
    // 0xb06d30: r16 = Instance_BoxFit
    //     0xb06d30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb06d34: ldr             x16, [x16, #0x118]
    // 0xb06d38: ldur            lr, [fp, #-0x40]
    // 0xb06d3c: stp             lr, x16, [SP, #8]
    // 0xb06d40: ldur            x16, [fp, #-0x48]
    // 0xb06d44: str             x16, [SP]
    // 0xb06d48: mov             x1, x0
    // 0xb06d4c: ldur            x2, [fp, #-0x38]
    // 0xb06d50: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb06d50: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb06d54: ldr             x4, [x4, #0x638]
    // 0xb06d58: r0 = CachedNetworkImage()
    //     0xb06d58: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb06d5c: r0 = ClipRRect()
    //     0xb06d5c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb06d60: mov             x1, x0
    // 0xb06d64: ldur            x0, [fp, #-0x28]
    // 0xb06d68: stur            x1, [fp, #-0x38]
    // 0xb06d6c: StoreField: r1->field_f = r0
    //     0xb06d6c: stur            w0, [x1, #0xf]
    // 0xb06d70: r0 = Instance_Clip
    //     0xb06d70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb06d74: ldr             x0, [x0, #0x138]
    // 0xb06d78: ArrayStore: r1[0] = r0  ; List_4
    //     0xb06d78: stur            w0, [x1, #0x17]
    // 0xb06d7c: ldur            x0, [fp, #-0x50]
    // 0xb06d80: StoreField: r1->field_b = r0
    //     0xb06d80: stur            w0, [x1, #0xb]
    // 0xb06d84: ldur            d0, [fp, #-0x60]
    // 0xb06d88: r0 = inline_Allocate_Double()
    //     0xb06d88: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb06d8c: add             x0, x0, #0x10
    //     0xb06d90: cmp             x2, x0
    //     0xb06d94: b.ls            #0xb07418
    //     0xb06d98: str             x0, [THR, #0x50]  ; THR::top
    //     0xb06d9c: sub             x0, x0, #0xf
    //     0xb06da0: movz            x2, #0xe15c
    //     0xb06da4: movk            x2, #0x3, lsl #16
    //     0xb06da8: stur            x2, [x0, #-1]
    // 0xb06dac: StoreField: r0->field_7 = d0
    //     0xb06dac: stur            d0, [x0, #7]
    // 0xb06db0: stur            x0, [fp, #-0x28]
    // 0xb06db4: r0 = Container()
    //     0xb06db4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb06db8: stur            x0, [fp, #-0x40]
    // 0xb06dbc: ldur            x16, [fp, #-0x28]
    // 0xb06dc0: ldur            lr, [fp, #-0x30]
    // 0xb06dc4: stp             lr, x16, [SP, #8]
    // 0xb06dc8: ldur            x16, [fp, #-0x38]
    // 0xb06dcc: str             x16, [SP]
    // 0xb06dd0: mov             x1, x0
    // 0xb06dd4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xb06dd4: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xb06dd8: ldr             x4, [x4, #0x628]
    // 0xb06ddc: r0 = Container()
    //     0xb06ddc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb06de0: ldur            x2, [fp, #-0x20]
    // 0xb06de4: LoadField: r0 = r2->field_13
    //     0xb06de4: ldur            w0, [x2, #0x13]
    // 0xb06de8: DecompressPointer r0
    //     0xb06de8: add             x0, x0, HEAP, lsl #32
    // 0xb06dec: ldur            x1, [fp, #-0x10]
    // 0xb06df0: r3 = LoadClassIdInstr(r1)
    //     0xb06df0: ldur            x3, [x1, #-1]
    //     0xb06df4: ubfx            x3, x3, #0xc, #0x14
    // 0xb06df8: stp             x0, x1, [SP]
    // 0xb06dfc: mov             x0, x3
    // 0xb06e00: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06e00: sub             lr, x0, #0xb7
    //     0xb06e04: ldr             lr, [x21, lr, lsl #3]
    //     0xb06e08: blr             lr
    // 0xb06e0c: LoadField: r1 = r0->field_7
    //     0xb06e0c: ldur            w1, [x0, #7]
    // 0xb06e10: DecompressPointer r1
    //     0xb06e10: add             x1, x1, HEAP, lsl #32
    // 0xb06e14: cmp             w1, NULL
    // 0xb06e18: b.ne            #0xb06e24
    // 0xb06e1c: r0 = Null
    //     0xb06e1c: mov             x0, NULL
    // 0xb06e20: b               #0xb06e3c
    // 0xb06e24: LoadField: r0 = r1->field_7
    //     0xb06e24: ldur            w0, [x1, #7]
    // 0xb06e28: cbnz            w0, #0xb06e34
    // 0xb06e2c: r1 = false
    //     0xb06e2c: add             x1, NULL, #0x30  ; false
    // 0xb06e30: b               #0xb06e38
    // 0xb06e34: r1 = true
    //     0xb06e34: add             x1, NULL, #0x20  ; true
    // 0xb06e38: mov             x0, x1
    // 0xb06e3c: cmp             w0, NULL
    // 0xb06e40: b.ne            #0xb06e4c
    // 0xb06e44: r3 = false
    //     0xb06e44: add             x3, NULL, #0x30  ; false
    // 0xb06e48: b               #0xb06e50
    // 0xb06e4c: mov             x3, x0
    // 0xb06e50: ldur            x1, [fp, #-0x10]
    // 0xb06e54: ldur            x2, [fp, #-0x20]
    // 0xb06e58: stur            x3, [fp, #-0x28]
    // 0xb06e5c: LoadField: r0 = r2->field_13
    //     0xb06e5c: ldur            w0, [x2, #0x13]
    // 0xb06e60: DecompressPointer r0
    //     0xb06e60: add             x0, x0, HEAP, lsl #32
    // 0xb06e64: r4 = LoadClassIdInstr(r1)
    //     0xb06e64: ldur            x4, [x1, #-1]
    //     0xb06e68: ubfx            x4, x4, #0xc, #0x14
    // 0xb06e6c: stp             x0, x1, [SP]
    // 0xb06e70: mov             x0, x4
    // 0xb06e74: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06e74: sub             lr, x0, #0xb7
    //     0xb06e78: ldr             lr, [x21, lr, lsl #3]
    //     0xb06e7c: blr             lr
    // 0xb06e80: LoadField: r1 = r0->field_7
    //     0xb06e80: ldur            w1, [x0, #7]
    // 0xb06e84: DecompressPointer r1
    //     0xb06e84: add             x1, x1, HEAP, lsl #32
    // 0xb06e88: cmp             w1, NULL
    // 0xb06e8c: b.ne            #0xb06e98
    // 0xb06e90: r4 = ""
    //     0xb06e90: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb06e94: b               #0xb06e9c
    // 0xb06e98: mov             x4, x1
    // 0xb06e9c: ldur            x3, [fp, #-8]
    // 0xb06ea0: ldur            x0, [fp, #-0x10]
    // 0xb06ea4: ldur            x2, [fp, #-0x20]
    // 0xb06ea8: stur            x4, [fp, #-0x30]
    // 0xb06eac: LoadField: r1 = r3->field_f
    //     0xb06eac: ldur            w1, [x3, #0xf]
    // 0xb06eb0: DecompressPointer r1
    //     0xb06eb0: add             x1, x1, HEAP, lsl #32
    // 0xb06eb4: cmp             w1, NULL
    // 0xb06eb8: b.eq            #0xb07430
    // 0xb06ebc: r0 = of()
    //     0xb06ebc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb06ec0: LoadField: r1 = r0->field_87
    //     0xb06ec0: ldur            w1, [x0, #0x87]
    // 0xb06ec4: DecompressPointer r1
    //     0xb06ec4: add             x1, x1, HEAP, lsl #32
    // 0xb06ec8: LoadField: r0 = r1->field_27
    //     0xb06ec8: ldur            w0, [x1, #0x27]
    // 0xb06ecc: DecompressPointer r0
    //     0xb06ecc: add             x0, x0, HEAP, lsl #32
    // 0xb06ed0: r16 = 21.000000
    //     0xb06ed0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb06ed4: ldr             x16, [x16, #0x9b0]
    // 0xb06ed8: r30 = Instance_Color
    //     0xb06ed8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb06edc: stp             lr, x16, [SP]
    // 0xb06ee0: mov             x1, x0
    // 0xb06ee4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb06ee4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb06ee8: ldr             x4, [x4, #0xaa0]
    // 0xb06eec: r0 = copyWith()
    //     0xb06eec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb06ef0: stur            x0, [fp, #-0x38]
    // 0xb06ef4: r0 = Text()
    //     0xb06ef4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb06ef8: mov             x1, x0
    // 0xb06efc: ldur            x0, [fp, #-0x30]
    // 0xb06f00: stur            x1, [fp, #-0x48]
    // 0xb06f04: StoreField: r1->field_b = r0
    //     0xb06f04: stur            w0, [x1, #0xb]
    // 0xb06f08: ldur            x0, [fp, #-0x38]
    // 0xb06f0c: StoreField: r1->field_13 = r0
    //     0xb06f0c: stur            w0, [x1, #0x13]
    // 0xb06f10: r0 = Padding()
    //     0xb06f10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb06f14: mov             x1, x0
    // 0xb06f18: r0 = Instance_EdgeInsets
    //     0xb06f18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f078] Obj!EdgeInsets@d571a1
    //     0xb06f1c: ldr             x0, [x0, #0x78]
    // 0xb06f20: stur            x1, [fp, #-0x30]
    // 0xb06f24: StoreField: r1->field_f = r0
    //     0xb06f24: stur            w0, [x1, #0xf]
    // 0xb06f28: ldur            x0, [fp, #-0x48]
    // 0xb06f2c: StoreField: r1->field_b = r0
    //     0xb06f2c: stur            w0, [x1, #0xb]
    // 0xb06f30: ldur            x2, [fp, #-0x20]
    // 0xb06f34: LoadField: r0 = r2->field_13
    //     0xb06f34: ldur            w0, [x2, #0x13]
    // 0xb06f38: DecompressPointer r0
    //     0xb06f38: add             x0, x0, HEAP, lsl #32
    // 0xb06f3c: ldur            x3, [fp, #-0x10]
    // 0xb06f40: r4 = LoadClassIdInstr(r3)
    //     0xb06f40: ldur            x4, [x3, #-1]
    //     0xb06f44: ubfx            x4, x4, #0xc, #0x14
    // 0xb06f48: stp             x0, x3, [SP]
    // 0xb06f4c: mov             x0, x4
    // 0xb06f50: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb06f50: sub             lr, x0, #0xb7
    //     0xb06f54: ldr             lr, [x21, lr, lsl #3]
    //     0xb06f58: blr             lr
    // 0xb06f5c: LoadField: r1 = r0->field_f
    //     0xb06f5c: ldur            w1, [x0, #0xf]
    // 0xb06f60: DecompressPointer r1
    //     0xb06f60: add             x1, x1, HEAP, lsl #32
    // 0xb06f64: cmp             w1, NULL
    // 0xb06f68: b.ne            #0xb06f74
    // 0xb06f6c: r0 = Null
    //     0xb06f6c: mov             x0, NULL
    // 0xb06f70: b               #0xb06f8c
    // 0xb06f74: LoadField: r0 = r1->field_7
    //     0xb06f74: ldur            w0, [x1, #7]
    // 0xb06f78: cbnz            w0, #0xb06f84
    // 0xb06f7c: r1 = false
    //     0xb06f7c: add             x1, NULL, #0x30  ; false
    // 0xb06f80: b               #0xb06f88
    // 0xb06f84: r1 = true
    //     0xb06f84: add             x1, NULL, #0x20  ; true
    // 0xb06f88: mov             x0, x1
    // 0xb06f8c: cmp             w0, NULL
    // 0xb06f90: b.ne            #0xb06f9c
    // 0xb06f94: r1 = false
    //     0xb06f94: add             x1, NULL, #0x30  ; false
    // 0xb06f98: b               #0xb06fa0
    // 0xb06f9c: mov             x1, x0
    // 0xb06fa0: ldur            x0, [fp, #-0x10]
    // 0xb06fa4: ldur            x2, [fp, #-0x20]
    // 0xb06fa8: stur            x1, [fp, #-0x38]
    // 0xb06fac: r16 = <Color>
    //     0xb06fac: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb06fb0: ldr             x16, [x16, #0xf80]
    // 0xb06fb4: r30 = Instance_Color
    //     0xb06fb4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb06fb8: stp             lr, x16, [SP]
    // 0xb06fbc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb06fbc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb06fc0: r0 = all()
    //     0xb06fc0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb06fc4: stur            x0, [fp, #-0x48]
    // 0xb06fc8: r16 = <RoundedRectangleBorder>
    //     0xb06fc8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb06fcc: ldr             x16, [x16, #0xf78]
    // 0xb06fd0: r30 = Instance_RoundedRectangleBorder
    //     0xb06fd0: add             lr, PP, #0x57, lsl #12  ; [pp+0x57e30] Obj!RoundedRectangleBorder@d5ac21
    //     0xb06fd4: ldr             lr, [lr, #0xe30]
    // 0xb06fd8: stp             lr, x16, [SP]
    // 0xb06fdc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb06fdc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb06fe0: r0 = all()
    //     0xb06fe0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb06fe4: stur            x0, [fp, #-0x50]
    // 0xb06fe8: r0 = ButtonStyle()
    //     0xb06fe8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb06fec: mov             x1, x0
    // 0xb06ff0: ldur            x0, [fp, #-0x48]
    // 0xb06ff4: stur            x1, [fp, #-0x58]
    // 0xb06ff8: StoreField: r1->field_b = r0
    //     0xb06ff8: stur            w0, [x1, #0xb]
    // 0xb06ffc: ldur            x0, [fp, #-0x50]
    // 0xb07000: StoreField: r1->field_43 = r0
    //     0xb07000: stur            w0, [x1, #0x43]
    // 0xb07004: r0 = TextButtonThemeData()
    //     0xb07004: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb07008: mov             x1, x0
    // 0xb0700c: ldur            x0, [fp, #-0x58]
    // 0xb07010: stur            x1, [fp, #-0x48]
    // 0xb07014: StoreField: r1->field_7 = r0
    //     0xb07014: stur            w0, [x1, #7]
    // 0xb07018: ldur            x2, [fp, #-0x20]
    // 0xb0701c: LoadField: r0 = r2->field_13
    //     0xb0701c: ldur            w0, [x2, #0x13]
    // 0xb07020: DecompressPointer r0
    //     0xb07020: add             x0, x0, HEAP, lsl #32
    // 0xb07024: ldur            x3, [fp, #-0x10]
    // 0xb07028: r4 = LoadClassIdInstr(r3)
    //     0xb07028: ldur            x4, [x3, #-1]
    //     0xb0702c: ubfx            x4, x4, #0xc, #0x14
    // 0xb07030: stp             x0, x3, [SP]
    // 0xb07034: mov             x0, x4
    // 0xb07038: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb07038: sub             lr, x0, #0xb7
    //     0xb0703c: ldr             lr, [x21, lr, lsl #3]
    //     0xb07040: blr             lr
    // 0xb07044: LoadField: r1 = r0->field_f
    //     0xb07044: ldur            w1, [x0, #0xf]
    // 0xb07048: DecompressPointer r1
    //     0xb07048: add             x1, x1, HEAP, lsl #32
    // 0xb0704c: cmp             w1, NULL
    // 0xb07050: b.ne            #0xb0705c
    // 0xb07054: r7 = ""
    //     0xb07054: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb07058: b               #0xb07060
    // 0xb0705c: mov             x7, x1
    // 0xb07060: ldur            x4, [fp, #-8]
    // 0xb07064: ldur            x6, [fp, #-0x40]
    // 0xb07068: ldur            x5, [fp, #-0x28]
    // 0xb0706c: ldur            x3, [fp, #-0x30]
    // 0xb07070: ldur            x2, [fp, #-0x38]
    // 0xb07074: ldur            x0, [fp, #-0x48]
    // 0xb07078: stur            x7, [fp, #-0x10]
    // 0xb0707c: LoadField: r1 = r4->field_f
    //     0xb0707c: ldur            w1, [x4, #0xf]
    // 0xb07080: DecompressPointer r1
    //     0xb07080: add             x1, x1, HEAP, lsl #32
    // 0xb07084: cmp             w1, NULL
    // 0xb07088: b.eq            #0xb07434
    // 0xb0708c: r0 = of()
    //     0xb0708c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb07090: LoadField: r1 = r0->field_87
    //     0xb07090: ldur            w1, [x0, #0x87]
    // 0xb07094: DecompressPointer r1
    //     0xb07094: add             x1, x1, HEAP, lsl #32
    // 0xb07098: LoadField: r0 = r1->field_7
    //     0xb07098: ldur            w0, [x1, #7]
    // 0xb0709c: DecompressPointer r0
    //     0xb0709c: add             x0, x0, HEAP, lsl #32
    // 0xb070a0: ldur            x1, [fp, #-8]
    // 0xb070a4: stur            x0, [fp, #-0x50]
    // 0xb070a8: LoadField: r2 = r1->field_f
    //     0xb070a8: ldur            w2, [x1, #0xf]
    // 0xb070ac: DecompressPointer r2
    //     0xb070ac: add             x2, x2, HEAP, lsl #32
    // 0xb070b0: cmp             w2, NULL
    // 0xb070b4: b.eq            #0xb07438
    // 0xb070b8: mov             x1, x2
    // 0xb070bc: r0 = of()
    //     0xb070bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb070c0: LoadField: r1 = r0->field_5b
    //     0xb070c0: ldur            w1, [x0, #0x5b]
    // 0xb070c4: DecompressPointer r1
    //     0xb070c4: add             x1, x1, HEAP, lsl #32
    // 0xb070c8: r16 = 16.000000
    //     0xb070c8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb070cc: ldr             x16, [x16, #0x188]
    // 0xb070d0: stp             x16, x1, [SP]
    // 0xb070d4: ldur            x1, [fp, #-0x50]
    // 0xb070d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb070d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb070dc: ldr             x4, [x4, #0x9b8]
    // 0xb070e0: r0 = copyWith()
    //     0xb070e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb070e4: stur            x0, [fp, #-8]
    // 0xb070e8: r0 = Text()
    //     0xb070e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb070ec: mov             x1, x0
    // 0xb070f0: ldur            x0, [fp, #-0x10]
    // 0xb070f4: stur            x1, [fp, #-0x50]
    // 0xb070f8: StoreField: r1->field_b = r0
    //     0xb070f8: stur            w0, [x1, #0xb]
    // 0xb070fc: ldur            x0, [fp, #-8]
    // 0xb07100: StoreField: r1->field_13 = r0
    //     0xb07100: stur            w0, [x1, #0x13]
    // 0xb07104: r0 = Padding()
    //     0xb07104: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb07108: mov             x3, x0
    // 0xb0710c: r0 = Instance_EdgeInsets
    //     0xb0710c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xb07110: ldr             x0, [x0, #0xc10]
    // 0xb07114: stur            x3, [fp, #-8]
    // 0xb07118: StoreField: r3->field_f = r0
    //     0xb07118: stur            w0, [x3, #0xf]
    // 0xb0711c: ldur            x0, [fp, #-0x50]
    // 0xb07120: StoreField: r3->field_b = r0
    //     0xb07120: stur            w0, [x3, #0xb]
    // 0xb07124: ldur            x2, [fp, #-0x20]
    // 0xb07128: r1 = Function '<anonymous closure>':.
    //     0xb07128: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e38] AnonymousClosure: (0xb0743c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xb0681c)
    //     0xb0712c: ldr             x1, [x1, #0xe38]
    // 0xb07130: r0 = AllocateClosure()
    //     0xb07130: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb07134: stur            x0, [fp, #-0x10]
    // 0xb07138: r0 = TextButton()
    //     0xb07138: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb0713c: mov             x1, x0
    // 0xb07140: ldur            x0, [fp, #-0x10]
    // 0xb07144: stur            x1, [fp, #-0x20]
    // 0xb07148: StoreField: r1->field_b = r0
    //     0xb07148: stur            w0, [x1, #0xb]
    // 0xb0714c: r0 = false
    //     0xb0714c: add             x0, NULL, #0x30  ; false
    // 0xb07150: StoreField: r1->field_27 = r0
    //     0xb07150: stur            w0, [x1, #0x27]
    // 0xb07154: r2 = true
    //     0xb07154: add             x2, NULL, #0x20  ; true
    // 0xb07158: StoreField: r1->field_2f = r2
    //     0xb07158: stur            w2, [x1, #0x2f]
    // 0xb0715c: ldur            x2, [fp, #-8]
    // 0xb07160: StoreField: r1->field_37 = r2
    //     0xb07160: stur            w2, [x1, #0x37]
    // 0xb07164: r0 = TextButtonTheme()
    //     0xb07164: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb07168: mov             x1, x0
    // 0xb0716c: ldur            x0, [fp, #-0x48]
    // 0xb07170: stur            x1, [fp, #-8]
    // 0xb07174: StoreField: r1->field_f = r0
    //     0xb07174: stur            w0, [x1, #0xf]
    // 0xb07178: ldur            x0, [fp, #-0x20]
    // 0xb0717c: StoreField: r1->field_b = r0
    //     0xb0717c: stur            w0, [x1, #0xb]
    // 0xb07180: r0 = Visibility()
    //     0xb07180: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb07184: mov             x3, x0
    // 0xb07188: ldur            x0, [fp, #-8]
    // 0xb0718c: stur            x3, [fp, #-0x10]
    // 0xb07190: StoreField: r3->field_b = r0
    //     0xb07190: stur            w0, [x3, #0xb]
    // 0xb07194: r0 = Instance_SizedBox
    //     0xb07194: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb07198: StoreField: r3->field_f = r0
    //     0xb07198: stur            w0, [x3, #0xf]
    // 0xb0719c: ldur            x1, [fp, #-0x38]
    // 0xb071a0: StoreField: r3->field_13 = r1
    //     0xb071a0: stur            w1, [x3, #0x13]
    // 0xb071a4: r4 = false
    //     0xb071a4: add             x4, NULL, #0x30  ; false
    // 0xb071a8: ArrayStore: r3[0] = r4  ; List_4
    //     0xb071a8: stur            w4, [x3, #0x17]
    // 0xb071ac: StoreField: r3->field_1b = r4
    //     0xb071ac: stur            w4, [x3, #0x1b]
    // 0xb071b0: StoreField: r3->field_1f = r4
    //     0xb071b0: stur            w4, [x3, #0x1f]
    // 0xb071b4: StoreField: r3->field_23 = r4
    //     0xb071b4: stur            w4, [x3, #0x23]
    // 0xb071b8: StoreField: r3->field_27 = r4
    //     0xb071b8: stur            w4, [x3, #0x27]
    // 0xb071bc: StoreField: r3->field_2b = r4
    //     0xb071bc: stur            w4, [x3, #0x2b]
    // 0xb071c0: r1 = Null
    //     0xb071c0: mov             x1, NULL
    // 0xb071c4: r2 = 4
    //     0xb071c4: movz            x2, #0x4
    // 0xb071c8: r0 = AllocateArray()
    //     0xb071c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb071cc: mov             x2, x0
    // 0xb071d0: ldur            x0, [fp, #-0x30]
    // 0xb071d4: stur            x2, [fp, #-8]
    // 0xb071d8: StoreField: r2->field_f = r0
    //     0xb071d8: stur            w0, [x2, #0xf]
    // 0xb071dc: ldur            x0, [fp, #-0x10]
    // 0xb071e0: StoreField: r2->field_13 = r0
    //     0xb071e0: stur            w0, [x2, #0x13]
    // 0xb071e4: r1 = <Widget>
    //     0xb071e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb071e8: r0 = AllocateGrowableArray()
    //     0xb071e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb071ec: mov             x1, x0
    // 0xb071f0: ldur            x0, [fp, #-8]
    // 0xb071f4: stur            x1, [fp, #-0x10]
    // 0xb071f8: StoreField: r1->field_f = r0
    //     0xb071f8: stur            w0, [x1, #0xf]
    // 0xb071fc: r2 = 4
    //     0xb071fc: movz            x2, #0x4
    // 0xb07200: StoreField: r1->field_b = r2
    //     0xb07200: stur            w2, [x1, #0xb]
    // 0xb07204: r0 = Column()
    //     0xb07204: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb07208: mov             x3, x0
    // 0xb0720c: r0 = Instance_Axis
    //     0xb0720c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb07210: stur            x3, [fp, #-8]
    // 0xb07214: StoreField: r3->field_f = r0
    //     0xb07214: stur            w0, [x3, #0xf]
    // 0xb07218: r0 = Instance_MainAxisAlignment
    //     0xb07218: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb0721c: ldr             x0, [x0, #0xab0]
    // 0xb07220: StoreField: r3->field_13 = r0
    //     0xb07220: stur            w0, [x3, #0x13]
    // 0xb07224: r0 = Instance_MainAxisSize
    //     0xb07224: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb07228: ldr             x0, [x0, #0xa10]
    // 0xb0722c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb0722c: stur            w0, [x3, #0x17]
    // 0xb07230: r0 = Instance_CrossAxisAlignment
    //     0xb07230: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb07234: ldr             x0, [x0, #0xa18]
    // 0xb07238: StoreField: r3->field_1b = r0
    //     0xb07238: stur            w0, [x3, #0x1b]
    // 0xb0723c: r0 = Instance_VerticalDirection
    //     0xb0723c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb07240: ldr             x0, [x0, #0xa20]
    // 0xb07244: StoreField: r3->field_23 = r0
    //     0xb07244: stur            w0, [x3, #0x23]
    // 0xb07248: r0 = Instance_Clip
    //     0xb07248: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0724c: ldr             x0, [x0, #0x38]
    // 0xb07250: StoreField: r3->field_2b = r0
    //     0xb07250: stur            w0, [x3, #0x2b]
    // 0xb07254: StoreField: r3->field_2f = rZR
    //     0xb07254: stur            xzr, [x3, #0x2f]
    // 0xb07258: ldur            x0, [fp, #-0x10]
    // 0xb0725c: StoreField: r3->field_b = r0
    //     0xb0725c: stur            w0, [x3, #0xb]
    // 0xb07260: r1 = Null
    //     0xb07260: mov             x1, NULL
    // 0xb07264: r2 = 2
    //     0xb07264: movz            x2, #0x2
    // 0xb07268: r0 = AllocateArray()
    //     0xb07268: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0726c: mov             x2, x0
    // 0xb07270: ldur            x0, [fp, #-8]
    // 0xb07274: stur            x2, [fp, #-0x10]
    // 0xb07278: StoreField: r2->field_f = r0
    //     0xb07278: stur            w0, [x2, #0xf]
    // 0xb0727c: r1 = <Widget>
    //     0xb0727c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb07280: r0 = AllocateGrowableArray()
    //     0xb07280: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb07284: mov             x1, x0
    // 0xb07288: ldur            x0, [fp, #-0x10]
    // 0xb0728c: stur            x1, [fp, #-8]
    // 0xb07290: StoreField: r1->field_f = r0
    //     0xb07290: stur            w0, [x1, #0xf]
    // 0xb07294: r0 = 2
    //     0xb07294: movz            x0, #0x2
    // 0xb07298: StoreField: r1->field_b = r0
    //     0xb07298: stur            w0, [x1, #0xb]
    // 0xb0729c: r0 = Stack()
    //     0xb0729c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb072a0: mov             x1, x0
    // 0xb072a4: r0 = Instance_Alignment
    //     0xb072a4: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xb072a8: ldr             x0, [x0, #0xcb0]
    // 0xb072ac: stur            x1, [fp, #-0x10]
    // 0xb072b0: StoreField: r1->field_f = r0
    //     0xb072b0: stur            w0, [x1, #0xf]
    // 0xb072b4: r2 = Instance_StackFit
    //     0xb072b4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb072b8: ldr             x2, [x2, #0xfa8]
    // 0xb072bc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb072bc: stur            w2, [x1, #0x17]
    // 0xb072c0: r3 = Instance_Clip
    //     0xb072c0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb072c4: ldr             x3, [x3, #0x7e0]
    // 0xb072c8: StoreField: r1->field_1b = r3
    //     0xb072c8: stur            w3, [x1, #0x1b]
    // 0xb072cc: ldur            x4, [fp, #-8]
    // 0xb072d0: StoreField: r1->field_b = r4
    //     0xb072d0: stur            w4, [x1, #0xb]
    // 0xb072d4: r0 = SizedBox()
    //     0xb072d4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb072d8: mov             x1, x0
    // 0xb072dc: r0 = 210.000000
    //     0xb072dc: add             x0, PP, #0x55, lsl #12  ; [pp+0x55888] 210
    //     0xb072e0: ldr             x0, [x0, #0x888]
    // 0xb072e4: stur            x1, [fp, #-8]
    // 0xb072e8: StoreField: r1->field_f = r0
    //     0xb072e8: stur            w0, [x1, #0xf]
    // 0xb072ec: r0 = 110.000000
    //     0xb072ec: add             x0, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb072f0: ldr             x0, [x0, #0x770]
    // 0xb072f4: StoreField: r1->field_13 = r0
    //     0xb072f4: stur            w0, [x1, #0x13]
    // 0xb072f8: ldur            x0, [fp, #-0x10]
    // 0xb072fc: StoreField: r1->field_b = r0
    //     0xb072fc: stur            w0, [x1, #0xb]
    // 0xb07300: r0 = Padding()
    //     0xb07300: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb07304: mov             x1, x0
    // 0xb07308: r0 = Instance_EdgeInsets
    //     0xb07308: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb0730c: ldr             x0, [x0, #0x240]
    // 0xb07310: stur            x1, [fp, #-0x10]
    // 0xb07314: StoreField: r1->field_f = r0
    //     0xb07314: stur            w0, [x1, #0xf]
    // 0xb07318: ldur            x0, [fp, #-8]
    // 0xb0731c: StoreField: r1->field_b = r0
    //     0xb0731c: stur            w0, [x1, #0xb]
    // 0xb07320: r0 = Visibility()
    //     0xb07320: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb07324: mov             x3, x0
    // 0xb07328: ldur            x0, [fp, #-0x10]
    // 0xb0732c: stur            x3, [fp, #-8]
    // 0xb07330: StoreField: r3->field_b = r0
    //     0xb07330: stur            w0, [x3, #0xb]
    // 0xb07334: r0 = Instance_SizedBox
    //     0xb07334: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb07338: StoreField: r3->field_f = r0
    //     0xb07338: stur            w0, [x3, #0xf]
    // 0xb0733c: ldur            x0, [fp, #-0x28]
    // 0xb07340: StoreField: r3->field_13 = r0
    //     0xb07340: stur            w0, [x3, #0x13]
    // 0xb07344: r0 = false
    //     0xb07344: add             x0, NULL, #0x30  ; false
    // 0xb07348: ArrayStore: r3[0] = r0  ; List_4
    //     0xb07348: stur            w0, [x3, #0x17]
    // 0xb0734c: StoreField: r3->field_1b = r0
    //     0xb0734c: stur            w0, [x3, #0x1b]
    // 0xb07350: StoreField: r3->field_1f = r0
    //     0xb07350: stur            w0, [x3, #0x1f]
    // 0xb07354: StoreField: r3->field_23 = r0
    //     0xb07354: stur            w0, [x3, #0x23]
    // 0xb07358: StoreField: r3->field_27 = r0
    //     0xb07358: stur            w0, [x3, #0x27]
    // 0xb0735c: StoreField: r3->field_2b = r0
    //     0xb0735c: stur            w0, [x3, #0x2b]
    // 0xb07360: r1 = Null
    //     0xb07360: mov             x1, NULL
    // 0xb07364: r2 = 4
    //     0xb07364: movz            x2, #0x4
    // 0xb07368: r0 = AllocateArray()
    //     0xb07368: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb0736c: mov             x2, x0
    // 0xb07370: ldur            x0, [fp, #-0x40]
    // 0xb07374: stur            x2, [fp, #-0x10]
    // 0xb07378: StoreField: r2->field_f = r0
    //     0xb07378: stur            w0, [x2, #0xf]
    // 0xb0737c: ldur            x0, [fp, #-8]
    // 0xb07380: StoreField: r2->field_13 = r0
    //     0xb07380: stur            w0, [x2, #0x13]
    // 0xb07384: r1 = <Widget>
    //     0xb07384: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb07388: r0 = AllocateGrowableArray()
    //     0xb07388: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0738c: mov             x1, x0
    // 0xb07390: ldur            x0, [fp, #-0x10]
    // 0xb07394: stur            x1, [fp, #-8]
    // 0xb07398: StoreField: r1->field_f = r0
    //     0xb07398: stur            w0, [x1, #0xf]
    // 0xb0739c: r0 = 4
    //     0xb0739c: movz            x0, #0x4
    // 0xb073a0: StoreField: r1->field_b = r0
    //     0xb073a0: stur            w0, [x1, #0xb]
    // 0xb073a4: r0 = Stack()
    //     0xb073a4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb073a8: r1 = Instance_Alignment
    //     0xb073a8: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xb073ac: ldr             x1, [x1, #0xcb0]
    // 0xb073b0: StoreField: r0->field_f = r1
    //     0xb073b0: stur            w1, [x0, #0xf]
    // 0xb073b4: r1 = Instance_StackFit
    //     0xb073b4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb073b8: ldr             x1, [x1, #0xfa8]
    // 0xb073bc: ArrayStore: r0[0] = r1  ; List_4
    //     0xb073bc: stur            w1, [x0, #0x17]
    // 0xb073c0: r1 = Instance_Clip
    //     0xb073c0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb073c4: ldr             x1, [x1, #0x7e0]
    // 0xb073c8: StoreField: r0->field_1b = r1
    //     0xb073c8: stur            w1, [x0, #0x1b]
    // 0xb073cc: ldur            x1, [fp, #-8]
    // 0xb073d0: StoreField: r0->field_b = r1
    //     0xb073d0: stur            w1, [x0, #0xb]
    // 0xb073d4: LeaveFrame
    //     0xb073d4: mov             SP, fp
    //     0xb073d8: ldp             fp, lr, [SP], #0x10
    // 0xb073dc: ret
    //     0xb073dc: ret             
    // 0xb073e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb073e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb073e4: b               #0xb06840
    // 0xb073e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb073e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb073ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb073ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb073f0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb073f0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb073f4: SaveReg d0
    //     0xb073f4: str             q0, [SP, #-0x10]!
    // 0xb073f8: SaveReg r1
    //     0xb073f8: str             x1, [SP, #-8]!
    // 0xb073fc: r0 = AllocateDouble()
    //     0xb073fc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb07400: RestoreReg r1
    //     0xb07400: ldr             x1, [SP], #8
    // 0xb07404: RestoreReg d0
    //     0xb07404: ldr             q0, [SP], #0x10
    // 0xb07408: b               #0xb06a50
    // 0xb0740c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0740c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07410: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07414: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb07414: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb07418: SaveReg d0
    //     0xb07418: str             q0, [SP, #-0x10]!
    // 0xb0741c: SaveReg r1
    //     0xb0741c: str             x1, [SP, #-8]!
    // 0xb07420: r0 = AllocateDouble()
    //     0xb07420: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb07424: RestoreReg r1
    //     0xb07424: ldr             x1, [SP], #8
    // 0xb07428: RestoreReg d0
    //     0xb07428: ldr             q0, [SP], #0x10
    // 0xb0742c: b               #0xb06dac
    // 0xb07430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07430: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07438: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07438: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb0743c, size: 0xe0
    // 0xb0743c: EnterFrame
    //     0xb0743c: stp             fp, lr, [SP, #-0x10]!
    //     0xb07440: mov             fp, SP
    // 0xb07444: AllocStack(0x20)
    //     0xb07444: sub             SP, SP, #0x20
    // 0xb07448: SetupParameters()
    //     0xb07448: ldr             x0, [fp, #0x10]
    //     0xb0744c: ldur            w1, [x0, #0x17]
    //     0xb07450: add             x1, x1, HEAP, lsl #32
    // 0xb07454: CheckStackOverflow
    //     0xb07454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb07458: cmp             SP, x16
    //     0xb0745c: b.ls            #0xb0750c
    // 0xb07460: LoadField: r0 = r1->field_f
    //     0xb07460: ldur            w0, [x1, #0xf]
    // 0xb07464: DecompressPointer r0
    //     0xb07464: add             x0, x0, HEAP, lsl #32
    // 0xb07468: LoadField: r2 = r0->field_b
    //     0xb07468: ldur            w2, [x0, #0xb]
    // 0xb0746c: DecompressPointer r2
    //     0xb0746c: add             x2, x2, HEAP, lsl #32
    // 0xb07470: cmp             w2, NULL
    // 0xb07474: b.eq            #0xb07514
    // 0xb07478: LoadField: r3 = r2->field_b
    //     0xb07478: ldur            w3, [x2, #0xb]
    // 0xb0747c: DecompressPointer r3
    //     0xb0747c: add             x3, x3, HEAP, lsl #32
    // 0xb07480: LoadField: r0 = r1->field_13
    //     0xb07480: ldur            w0, [x1, #0x13]
    // 0xb07484: DecompressPointer r0
    //     0xb07484: add             x0, x0, HEAP, lsl #32
    // 0xb07488: LoadField: r1 = r3->field_b
    //     0xb07488: ldur            w1, [x3, #0xb]
    // 0xb0748c: r4 = LoadInt32Instr(r0)
    //     0xb0748c: sbfx            x4, x0, #1, #0x1f
    //     0xb07490: tbz             w0, #0, #0xb07498
    //     0xb07494: ldur            x4, [x0, #7]
    // 0xb07498: r0 = LoadInt32Instr(r1)
    //     0xb07498: sbfx            x0, x1, #1, #0x1f
    // 0xb0749c: mov             x1, x4
    // 0xb074a0: cmp             x1, x0
    // 0xb074a4: b.hs            #0xb07518
    // 0xb074a8: LoadField: r0 = r3->field_f
    //     0xb074a8: ldur            w0, [x3, #0xf]
    // 0xb074ac: DecompressPointer r0
    //     0xb074ac: add             x0, x0, HEAP, lsl #32
    // 0xb074b0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb074b0: add             x16, x0, x4, lsl #2
    //     0xb074b4: ldur            w1, [x16, #0xf]
    // 0xb074b8: DecompressPointer r1
    //     0xb074b8: add             x1, x1, HEAP, lsl #32
    // 0xb074bc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb074bc: ldur            w0, [x1, #0x17]
    // 0xb074c0: DecompressPointer r0
    //     0xb074c0: add             x0, x0, HEAP, lsl #32
    // 0xb074c4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb074c4: ldur            w1, [x2, #0x17]
    // 0xb074c8: DecompressPointer r1
    //     0xb074c8: add             x1, x1, HEAP, lsl #32
    // 0xb074cc: LoadField: r3 = r2->field_23
    //     0xb074cc: ldur            w3, [x2, #0x23]
    // 0xb074d0: DecompressPointer r3
    //     0xb074d0: add             x3, x3, HEAP, lsl #32
    // 0xb074d4: stp             x0, x3, [SP, #0x10]
    // 0xb074d8: r16 = "product_page"
    //     0xb074d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb074dc: ldr             x16, [x16, #0x480]
    // 0xb074e0: stp             x1, x16, [SP]
    // 0xb074e4: r4 = 0
    //     0xb074e4: movz            x4, #0
    // 0xb074e8: ldr             x0, [SP, #0x18]
    // 0xb074ec: r16 = UnlinkedCall_0x613b5c
    //     0xb074ec: add             x16, PP, #0x57, lsl #12  ; [pp+0x57e40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb074f0: add             x16, x16, #0xe40
    // 0xb074f4: ldp             x5, lr, [x16]
    // 0xb074f8: blr             lr
    // 0xb074fc: r0 = Null
    //     0xb074fc: mov             x0, NULL
    // 0xb07500: LeaveFrame
    //     0xb07500: mov             SP, fp
    //     0xb07504: ldp             fp, lr, [SP], #0x10
    // 0xb07508: ret
    //     0xb07508: ret             
    // 0xb0750c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0750c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb07510: b               #0xb07460
    // 0xb07514: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07514: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb07518: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String) {
    // ** addr: 0xb0751c, size: 0xa4
    // 0xb0751c: EnterFrame
    //     0xb0751c: stp             fp, lr, [SP, #-0x10]!
    //     0xb07520: mov             fp, SP
    // 0xb07524: AllocStack(0x40)
    //     0xb07524: sub             SP, SP, #0x40
    // 0xb07528: SetupParameters()
    //     0xb07528: ldr             x0, [fp, #0x48]
    //     0xb0752c: ldur            w1, [x0, #0x17]
    //     0xb07530: add             x1, x1, HEAP, lsl #32
    // 0xb07534: CheckStackOverflow
    //     0xb07534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb07538: cmp             SP, x16
    //     0xb0753c: b.ls            #0xb075b4
    // 0xb07540: LoadField: r0 = r1->field_f
    //     0xb07540: ldur            w0, [x1, #0xf]
    // 0xb07544: DecompressPointer r0
    //     0xb07544: add             x0, x0, HEAP, lsl #32
    // 0xb07548: LoadField: r1 = r0->field_b
    //     0xb07548: ldur            w1, [x0, #0xb]
    // 0xb0754c: DecompressPointer r1
    //     0xb0754c: add             x1, x1, HEAP, lsl #32
    // 0xb07550: cmp             w1, NULL
    // 0xb07554: b.eq            #0xb075bc
    // 0xb07558: LoadField: r0 = r1->field_1f
    //     0xb07558: ldur            w0, [x1, #0x1f]
    // 0xb0755c: DecompressPointer r0
    //     0xb0755c: add             x0, x0, HEAP, lsl #32
    // 0xb07560: ldr             x16, [fp, #0x40]
    // 0xb07564: stp             x16, x0, [SP, #0x30]
    // 0xb07568: ldr             x16, [fp, #0x38]
    // 0xb0756c: ldr             lr, [fp, #0x30]
    // 0xb07570: stp             lr, x16, [SP, #0x20]
    // 0xb07574: ldr             x16, [fp, #0x28]
    // 0xb07578: ldr             lr, [fp, #0x20]
    // 0xb0757c: stp             lr, x16, [SP, #0x10]
    // 0xb07580: ldr             x16, [fp, #0x18]
    // 0xb07584: ldr             lr, [fp, #0x10]
    // 0xb07588: stp             lr, x16, [SP]
    // 0xb0758c: r4 = 0
    //     0xb0758c: movz            x4, #0
    // 0xb07590: ldr             x0, [SP, #0x38]
    // 0xb07594: r16 = UnlinkedCall_0x613b5c
    //     0xb07594: add             x16, PP, #0x57, lsl #12  ; [pp+0x57e50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb07598: add             x16, x16, #0xe50
    // 0xb0759c: ldp             x5, lr, [x16]
    // 0xb075a0: blr             lr
    // 0xb075a4: r0 = Null
    //     0xb075a4: mov             x0, NULL
    // 0xb075a8: LeaveFrame
    //     0xb075a8: mov             SP, fp
    //     0xb075ac: ldp             fp, lr, [SP], #0x10
    // 0xb075b0: ret
    //     0xb075b0: ret             
    // 0xb075b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb075b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb075b8: b               #0xb07540
    // 0xb075bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb075bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4144, size: 0x38, field offset: 0xc
//   const constructor, 
class ProductBannerCrossLink extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7df14, size: 0x24
    // 0xc7df14: EnterFrame
    //     0xc7df14: stp             fp, lr, [SP, #-0x10]!
    //     0xc7df18: mov             fp, SP
    // 0xc7df1c: mov             x0, x1
    // 0xc7df20: r1 = <ProductBannerCrossLink>
    //     0xc7df20: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b48] TypeArguments: <ProductBannerCrossLink>
    //     0xc7df24: ldr             x1, [x1, #0xb48]
    // 0xc7df28: r0 = _ProductBannerCrossLinkState()
    //     0xc7df28: bl              #0xc7df38  ; Allocate_ProductBannerCrossLinkStateStub -> _ProductBannerCrossLinkState (size=0x14)
    // 0xc7df2c: LeaveFrame
    //     0xc7df2c: mov             SP, fp
    //     0xc7df30: ldp             fp, lr, [SP], #0x10
    // 0xc7df34: ret
    //     0xc7df34: ret             
  }
}
