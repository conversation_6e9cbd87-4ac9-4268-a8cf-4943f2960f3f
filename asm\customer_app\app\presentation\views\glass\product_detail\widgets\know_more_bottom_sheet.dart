// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/know_more_bottom_sheet.dart

// class id: 1049432, size: 0x8
class :: {
}

// class id: 3319, size: 0x14, field offset: 0x14
class _KnowMoreBottomSheetState extends State<dynamic> {

  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb0623c, size: 0x3b0
    // 0xb0623c: EnterFrame
    //     0xb0623c: stp             fp, lr, [SP, #-0x10]!
    //     0xb06240: mov             fp, SP
    // 0xb06244: AllocStack(0x38)
    //     0xb06244: sub             SP, SP, #0x38
    // 0xb06248: SetupParameters()
    //     0xb06248: ldr             x0, [fp, #0x20]
    //     0xb0624c: ldur            w3, [x0, #0x17]
    //     0xb06250: add             x3, x3, HEAP, lsl #32
    //     0xb06254: stur            x3, [fp, #-8]
    // 0xb06258: CheckStackOverflow
    //     0xb06258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0625c: cmp             SP, x16
    //     0xb06260: b.ls            #0xb065d4
    // 0xb06264: r1 = <Widget>
    //     0xb06264: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb06268: r2 = 0
    //     0xb06268: movz            x2, #0
    // 0xb0626c: r0 = _GrowableList()
    //     0xb0626c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb06270: mov             x3, x0
    // 0xb06274: ldur            x2, [fp, #-8]
    // 0xb06278: stur            x3, [fp, #-0x18]
    // 0xb0627c: LoadField: r0 = r2->field_f
    //     0xb0627c: ldur            w0, [x2, #0xf]
    // 0xb06280: DecompressPointer r0
    //     0xb06280: add             x0, x0, HEAP, lsl #32
    // 0xb06284: LoadField: r1 = r0->field_b
    //     0xb06284: ldur            w1, [x0, #0xb]
    // 0xb06288: DecompressPointer r1
    //     0xb06288: add             x1, x1, HEAP, lsl #32
    // 0xb0628c: cmp             w1, NULL
    // 0xb06290: b.eq            #0xb065dc
    // 0xb06294: LoadField: r0 = r1->field_b
    //     0xb06294: ldur            w0, [x1, #0xb]
    // 0xb06298: DecompressPointer r0
    //     0xb06298: add             x0, x0, HEAP, lsl #32
    // 0xb0629c: LoadField: r1 = r0->field_b
    //     0xb0629c: ldur            w1, [x0, #0xb]
    // 0xb062a0: DecompressPointer r1
    //     0xb062a0: add             x1, x1, HEAP, lsl #32
    // 0xb062a4: cmp             w1, NULL
    // 0xb062a8: b.ne            #0xb062b8
    // 0xb062ac: ldr             x4, [fp, #0x10]
    // 0xb062b0: r0 = Null
    //     0xb062b0: mov             x0, NULL
    // 0xb062b4: b               #0xb0630c
    // 0xb062b8: ldr             x4, [fp, #0x10]
    // 0xb062bc: LoadField: r0 = r1->field_b
    //     0xb062bc: ldur            w0, [x1, #0xb]
    // 0xb062c0: DecompressPointer r0
    //     0xb062c0: add             x0, x0, HEAP, lsl #32
    // 0xb062c4: LoadField: r5 = r0->field_7
    //     0xb062c4: ldur            w5, [x0, #7]
    // 0xb062c8: DecompressPointer r5
    //     0xb062c8: add             x5, x5, HEAP, lsl #32
    // 0xb062cc: LoadField: r0 = r5->field_b
    //     0xb062cc: ldur            w0, [x5, #0xb]
    // 0xb062d0: r6 = LoadInt32Instr(r4)
    //     0xb062d0: sbfx            x6, x4, #1, #0x1f
    //     0xb062d4: tbz             w4, #0, #0xb062dc
    //     0xb062d8: ldur            x6, [x4, #7]
    // 0xb062dc: r1 = LoadInt32Instr(r0)
    //     0xb062dc: sbfx            x1, x0, #1, #0x1f
    // 0xb062e0: mov             x0, x1
    // 0xb062e4: mov             x1, x6
    // 0xb062e8: cmp             x1, x0
    // 0xb062ec: b.hs            #0xb065e0
    // 0xb062f0: LoadField: r0 = r5->field_f
    //     0xb062f0: ldur            w0, [x5, #0xf]
    // 0xb062f4: DecompressPointer r0
    //     0xb062f4: add             x0, x0, HEAP, lsl #32
    // 0xb062f8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb062f8: add             x16, x0, x6, lsl #2
    //     0xb062fc: ldur            w1, [x16, #0xf]
    // 0xb06300: DecompressPointer r1
    //     0xb06300: add             x1, x1, HEAP, lsl #32
    // 0xb06304: LoadField: r0 = r1->field_b
    //     0xb06304: ldur            w0, [x1, #0xb]
    // 0xb06308: DecompressPointer r0
    //     0xb06308: add             x0, x0, HEAP, lsl #32
    // 0xb0630c: cmp             w0, NULL
    // 0xb06310: b.ne            #0xb0631c
    // 0xb06314: mov             x0, x3
    // 0xb06318: b               #0xb06384
    // 0xb0631c: tbnz            w0, #4, #0xb06380
    // 0xb06320: LoadField: r0 = r3->field_b
    //     0xb06320: ldur            w0, [x3, #0xb]
    // 0xb06324: LoadField: r1 = r3->field_f
    //     0xb06324: ldur            w1, [x3, #0xf]
    // 0xb06328: DecompressPointer r1
    //     0xb06328: add             x1, x1, HEAP, lsl #32
    // 0xb0632c: LoadField: r5 = r1->field_b
    //     0xb0632c: ldur            w5, [x1, #0xb]
    // 0xb06330: r6 = LoadInt32Instr(r0)
    //     0xb06330: sbfx            x6, x0, #1, #0x1f
    // 0xb06334: stur            x6, [fp, #-0x10]
    // 0xb06338: r0 = LoadInt32Instr(r5)
    //     0xb06338: sbfx            x0, x5, #1, #0x1f
    // 0xb0633c: cmp             x6, x0
    // 0xb06340: b.ne            #0xb0634c
    // 0xb06344: mov             x1, x3
    // 0xb06348: r0 = _growToNextCapacity()
    //     0xb06348: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0634c: ldur            x0, [fp, #-0x18]
    // 0xb06350: ldur            x1, [fp, #-0x10]
    // 0xb06354: add             x2, x1, #1
    // 0xb06358: lsl             x3, x2, #1
    // 0xb0635c: StoreField: r0->field_b = r3
    //     0xb0635c: stur            w3, [x0, #0xb]
    // 0xb06360: LoadField: r2 = r0->field_f
    //     0xb06360: ldur            w2, [x0, #0xf]
    // 0xb06364: DecompressPointer r2
    //     0xb06364: add             x2, x2, HEAP, lsl #32
    // 0xb06368: add             x3, x2, x1, lsl #2
    // 0xb0636c: r16 = Instance_Icon
    //     0xb0636c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52ce8] Obj!Icon@d664f1
    //     0xb06370: ldr             x16, [x16, #0xce8]
    // 0xb06374: StoreField: r3->field_f = r16
    //     0xb06374: stur            w16, [x3, #0xf]
    // 0xb06378: mov             x2, x0
    // 0xb0637c: b               #0xb063dc
    // 0xb06380: mov             x0, x3
    // 0xb06384: LoadField: r1 = r0->field_b
    //     0xb06384: ldur            w1, [x0, #0xb]
    // 0xb06388: LoadField: r2 = r0->field_f
    //     0xb06388: ldur            w2, [x0, #0xf]
    // 0xb0638c: DecompressPointer r2
    //     0xb0638c: add             x2, x2, HEAP, lsl #32
    // 0xb06390: LoadField: r3 = r2->field_b
    //     0xb06390: ldur            w3, [x2, #0xb]
    // 0xb06394: r2 = LoadInt32Instr(r1)
    //     0xb06394: sbfx            x2, x1, #1, #0x1f
    // 0xb06398: stur            x2, [fp, #-0x10]
    // 0xb0639c: r1 = LoadInt32Instr(r3)
    //     0xb0639c: sbfx            x1, x3, #1, #0x1f
    // 0xb063a0: cmp             x2, x1
    // 0xb063a4: b.ne            #0xb063b0
    // 0xb063a8: mov             x1, x0
    // 0xb063ac: r0 = _growToNextCapacity()
    //     0xb063ac: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb063b0: ldur            x2, [fp, #-0x18]
    // 0xb063b4: ldur            x0, [fp, #-0x10]
    // 0xb063b8: add             x1, x0, #1
    // 0xb063bc: lsl             x3, x1, #1
    // 0xb063c0: StoreField: r2->field_b = r3
    //     0xb063c0: stur            w3, [x2, #0xb]
    // 0xb063c4: LoadField: r1 = r2->field_f
    //     0xb063c4: ldur            w1, [x2, #0xf]
    // 0xb063c8: DecompressPointer r1
    //     0xb063c8: add             x1, x1, HEAP, lsl #32
    // 0xb063cc: add             x3, x1, x0, lsl #2
    // 0xb063d0: r16 = Instance_Icon
    //     0xb063d0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52cf0] Obj!Icon@d664b1
    //     0xb063d4: ldr             x16, [x16, #0xcf0]
    // 0xb063d8: StoreField: r3->field_f = r16
    //     0xb063d8: stur            w16, [x3, #0xf]
    // 0xb063dc: ldur            x0, [fp, #-8]
    // 0xb063e0: LoadField: r1 = r0->field_f
    //     0xb063e0: ldur            w1, [x0, #0xf]
    // 0xb063e4: DecompressPointer r1
    //     0xb063e4: add             x1, x1, HEAP, lsl #32
    // 0xb063e8: LoadField: r0 = r1->field_b
    //     0xb063e8: ldur            w0, [x1, #0xb]
    // 0xb063ec: DecompressPointer r0
    //     0xb063ec: add             x0, x0, HEAP, lsl #32
    // 0xb063f0: cmp             w0, NULL
    // 0xb063f4: b.eq            #0xb065e4
    // 0xb063f8: LoadField: r1 = r0->field_b
    //     0xb063f8: ldur            w1, [x0, #0xb]
    // 0xb063fc: DecompressPointer r1
    //     0xb063fc: add             x1, x1, HEAP, lsl #32
    // 0xb06400: LoadField: r0 = r1->field_b
    //     0xb06400: ldur            w0, [x1, #0xb]
    // 0xb06404: DecompressPointer r0
    //     0xb06404: add             x0, x0, HEAP, lsl #32
    // 0xb06408: cmp             w0, NULL
    // 0xb0640c: b.ne            #0xb06418
    // 0xb06410: r0 = Null
    //     0xb06410: mov             x0, NULL
    // 0xb06414: b               #0xb0646c
    // 0xb06418: ldr             x1, [fp, #0x10]
    // 0xb0641c: LoadField: r3 = r0->field_b
    //     0xb0641c: ldur            w3, [x0, #0xb]
    // 0xb06420: DecompressPointer r3
    //     0xb06420: add             x3, x3, HEAP, lsl #32
    // 0xb06424: LoadField: r4 = r3->field_7
    //     0xb06424: ldur            w4, [x3, #7]
    // 0xb06428: DecompressPointer r4
    //     0xb06428: add             x4, x4, HEAP, lsl #32
    // 0xb0642c: LoadField: r0 = r4->field_b
    //     0xb0642c: ldur            w0, [x4, #0xb]
    // 0xb06430: r3 = LoadInt32Instr(r1)
    //     0xb06430: sbfx            x3, x1, #1, #0x1f
    //     0xb06434: tbz             w1, #0, #0xb0643c
    //     0xb06438: ldur            x3, [x1, #7]
    // 0xb0643c: r1 = LoadInt32Instr(r0)
    //     0xb0643c: sbfx            x1, x0, #1, #0x1f
    // 0xb06440: mov             x0, x1
    // 0xb06444: mov             x1, x3
    // 0xb06448: cmp             x1, x0
    // 0xb0644c: b.hs            #0xb065e8
    // 0xb06450: LoadField: r0 = r4->field_f
    //     0xb06450: ldur            w0, [x4, #0xf]
    // 0xb06454: DecompressPointer r0
    //     0xb06454: add             x0, x0, HEAP, lsl #32
    // 0xb06458: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb06458: add             x16, x0, x3, lsl #2
    //     0xb0645c: ldur            w1, [x16, #0xf]
    // 0xb06460: DecompressPointer r1
    //     0xb06460: add             x1, x1, HEAP, lsl #32
    // 0xb06464: LoadField: r0 = r1->field_7
    //     0xb06464: ldur            w0, [x1, #7]
    // 0xb06468: DecompressPointer r0
    //     0xb06468: add             x0, x0, HEAP, lsl #32
    // 0xb0646c: str             x0, [SP]
    // 0xb06470: r0 = _interpolateSingle()
    //     0xb06470: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb06474: ldr             x1, [fp, #0x18]
    // 0xb06478: stur            x0, [fp, #-8]
    // 0xb0647c: r0 = of()
    //     0xb0647c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb06480: LoadField: r1 = r0->field_87
    //     0xb06480: ldur            w1, [x0, #0x87]
    // 0xb06484: DecompressPointer r1
    //     0xb06484: add             x1, x1, HEAP, lsl #32
    // 0xb06488: LoadField: r0 = r1->field_2b
    //     0xb06488: ldur            w0, [x1, #0x2b]
    // 0xb0648c: DecompressPointer r0
    //     0xb0648c: add             x0, x0, HEAP, lsl #32
    // 0xb06490: stur            x0, [fp, #-0x20]
    // 0xb06494: r1 = Instance_Color
    //     0xb06494: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb06498: d0 = 0.400000
    //     0xb06498: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb0649c: r0 = withOpacity()
    //     0xb0649c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb064a0: r16 = 14.000000
    //     0xb064a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb064a4: ldr             x16, [x16, #0x1d8]
    // 0xb064a8: stp             x0, x16, [SP]
    // 0xb064ac: ldur            x1, [fp, #-0x20]
    // 0xb064b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb064b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb064b4: ldr             x4, [x4, #0xaa0]
    // 0xb064b8: r0 = copyWith()
    //     0xb064b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb064bc: stur            x0, [fp, #-0x20]
    // 0xb064c0: r0 = Text()
    //     0xb064c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb064c4: mov             x2, x0
    // 0xb064c8: ldur            x0, [fp, #-8]
    // 0xb064cc: stur            x2, [fp, #-0x28]
    // 0xb064d0: StoreField: r2->field_b = r0
    //     0xb064d0: stur            w0, [x2, #0xb]
    // 0xb064d4: ldur            x0, [fp, #-0x20]
    // 0xb064d8: StoreField: r2->field_13 = r0
    //     0xb064d8: stur            w0, [x2, #0x13]
    // 0xb064dc: ldur            x0, [fp, #-0x18]
    // 0xb064e0: LoadField: r1 = r0->field_b
    //     0xb064e0: ldur            w1, [x0, #0xb]
    // 0xb064e4: LoadField: r3 = r0->field_f
    //     0xb064e4: ldur            w3, [x0, #0xf]
    // 0xb064e8: DecompressPointer r3
    //     0xb064e8: add             x3, x3, HEAP, lsl #32
    // 0xb064ec: LoadField: r4 = r3->field_b
    //     0xb064ec: ldur            w4, [x3, #0xb]
    // 0xb064f0: r3 = LoadInt32Instr(r1)
    //     0xb064f0: sbfx            x3, x1, #1, #0x1f
    // 0xb064f4: stur            x3, [fp, #-0x10]
    // 0xb064f8: r1 = LoadInt32Instr(r4)
    //     0xb064f8: sbfx            x1, x4, #1, #0x1f
    // 0xb064fc: cmp             x3, x1
    // 0xb06500: b.ne            #0xb0650c
    // 0xb06504: mov             x1, x0
    // 0xb06508: r0 = _growToNextCapacity()
    //     0xb06508: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0650c: ldur            x2, [fp, #-0x18]
    // 0xb06510: ldur            x3, [fp, #-0x10]
    // 0xb06514: add             x0, x3, #1
    // 0xb06518: lsl             x1, x0, #1
    // 0xb0651c: StoreField: r2->field_b = r1
    //     0xb0651c: stur            w1, [x2, #0xb]
    // 0xb06520: LoadField: r1 = r2->field_f
    //     0xb06520: ldur            w1, [x2, #0xf]
    // 0xb06524: DecompressPointer r1
    //     0xb06524: add             x1, x1, HEAP, lsl #32
    // 0xb06528: ldur            x0, [fp, #-0x28]
    // 0xb0652c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb0652c: add             x25, x1, x3, lsl #2
    //     0xb06530: add             x25, x25, #0xf
    //     0xb06534: str             w0, [x25]
    //     0xb06538: tbz             w0, #0, #0xb06554
    //     0xb0653c: ldurb           w16, [x1, #-1]
    //     0xb06540: ldurb           w17, [x0, #-1]
    //     0xb06544: and             x16, x17, x16, lsr #2
    //     0xb06548: tst             x16, HEAP, lsr #32
    //     0xb0654c: b.eq            #0xb06554
    //     0xb06550: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb06554: r0 = Row()
    //     0xb06554: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb06558: mov             x1, x0
    // 0xb0655c: r0 = Instance_Axis
    //     0xb0655c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb06560: stur            x1, [fp, #-8]
    // 0xb06564: StoreField: r1->field_f = r0
    //     0xb06564: stur            w0, [x1, #0xf]
    // 0xb06568: r0 = Instance_MainAxisAlignment
    //     0xb06568: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb0656c: ldr             x0, [x0, #0xa08]
    // 0xb06570: StoreField: r1->field_13 = r0
    //     0xb06570: stur            w0, [x1, #0x13]
    // 0xb06574: r0 = Instance_MainAxisSize
    //     0xb06574: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb06578: ldr             x0, [x0, #0xa10]
    // 0xb0657c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb0657c: stur            w0, [x1, #0x17]
    // 0xb06580: r0 = Instance_CrossAxisAlignment
    //     0xb06580: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb06584: ldr             x0, [x0, #0xa18]
    // 0xb06588: StoreField: r1->field_1b = r0
    //     0xb06588: stur            w0, [x1, #0x1b]
    // 0xb0658c: r0 = Instance_VerticalDirection
    //     0xb0658c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb06590: ldr             x0, [x0, #0xa20]
    // 0xb06594: StoreField: r1->field_23 = r0
    //     0xb06594: stur            w0, [x1, #0x23]
    // 0xb06598: r0 = Instance_Clip
    //     0xb06598: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb0659c: ldr             x0, [x0, #0x38]
    // 0xb065a0: StoreField: r1->field_2b = r0
    //     0xb065a0: stur            w0, [x1, #0x2b]
    // 0xb065a4: StoreField: r1->field_2f = rZR
    //     0xb065a4: stur            xzr, [x1, #0x2f]
    // 0xb065a8: ldur            x0, [fp, #-0x18]
    // 0xb065ac: StoreField: r1->field_b = r0
    //     0xb065ac: stur            w0, [x1, #0xb]
    // 0xb065b0: r0 = Padding()
    //     0xb065b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb065b4: r1 = Instance_EdgeInsets
    //     0xb065b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!EdgeInsets@d577d1
    //     0xb065b8: ldr             x1, [x1, #0xb28]
    // 0xb065bc: StoreField: r0->field_f = r1
    //     0xb065bc: stur            w1, [x0, #0xf]
    // 0xb065c0: ldur            x1, [fp, #-8]
    // 0xb065c4: StoreField: r0->field_b = r1
    //     0xb065c4: stur            w1, [x0, #0xb]
    // 0xb065c8: LeaveFrame
    //     0xb065c8: mov             SP, fp
    //     0xb065cc: ldp             fp, lr, [SP], #0x10
    // 0xb065d0: ret
    //     0xb065d0: ret             
    // 0xb065d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb065d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb065d8: b               #0xb06264
    // 0xb065dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb065dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb065e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb065e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb065e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb065e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb065e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb065e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb7f450, size: 0x1138
    // 0xb7f450: EnterFrame
    //     0xb7f450: stp             fp, lr, [SP, #-0x10]!
    //     0xb7f454: mov             fp, SP
    // 0xb7f458: AllocStack(0x78)
    //     0xb7f458: sub             SP, SP, #0x78
    // 0xb7f45c: SetupParameters(_KnowMoreBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb7f45c: mov             x0, x1
    //     0xb7f460: stur            x1, [fp, #-8]
    //     0xb7f464: mov             x1, x2
    //     0xb7f468: stur            x2, [fp, #-0x10]
    // 0xb7f46c: CheckStackOverflow
    //     0xb7f46c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7f470: cmp             SP, x16
    //     0xb7f474: b.ls            #0xb80530
    // 0xb7f478: r1 = 1
    //     0xb7f478: movz            x1, #0x1
    // 0xb7f47c: r0 = AllocateContext()
    //     0xb7f47c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7f480: mov             x2, x0
    // 0xb7f484: ldur            x0, [fp, #-8]
    // 0xb7f488: stur            x2, [fp, #-0x18]
    // 0xb7f48c: StoreField: r2->field_f = r0
    //     0xb7f48c: stur            w0, [x2, #0xf]
    // 0xb7f490: ldur            x1, [fp, #-0x10]
    // 0xb7f494: r0 = of()
    //     0xb7f494: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7f498: LoadField: r1 = r0->field_5b
    //     0xb7f498: ldur            w1, [x0, #0x5b]
    // 0xb7f49c: DecompressPointer r1
    //     0xb7f49c: add             x1, x1, HEAP, lsl #32
    // 0xb7f4a0: stur            x1, [fp, #-0x20]
    // 0xb7f4a4: r0 = BoxDecoration()
    //     0xb7f4a4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7f4a8: mov             x1, x0
    // 0xb7f4ac: ldur            x0, [fp, #-0x20]
    // 0xb7f4b0: stur            x1, [fp, #-0x28]
    // 0xb7f4b4: StoreField: r1->field_7 = r0
    //     0xb7f4b4: stur            w0, [x1, #7]
    // 0xb7f4b8: r0 = Instance_BoxShape
    //     0xb7f4b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb7f4bc: ldr             x0, [x0, #0x970]
    // 0xb7f4c0: StoreField: r1->field_23 = r0
    //     0xb7f4c0: stur            w0, [x1, #0x23]
    // 0xb7f4c4: r0 = SvgPicture()
    //     0xb7f4c4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7f4c8: stur            x0, [fp, #-0x20]
    // 0xb7f4cc: r16 = Instance_BoxFit
    //     0xb7f4cc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7f4d0: ldr             x16, [x16, #0xb18]
    // 0xb7f4d4: str             x16, [SP]
    // 0xb7f4d8: mov             x1, x0
    // 0xb7f4dc: r2 = "assets/images/return_order.svg"
    //     0xb7f4dc: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c80] "assets/images/return_order.svg"
    //     0xb7f4e0: ldr             x2, [x2, #0xc80]
    // 0xb7f4e4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb7f4e4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb7f4e8: ldr             x4, [x4, #0xb0]
    // 0xb7f4ec: r0 = SvgPicture.asset()
    //     0xb7f4ec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7f4f0: r0 = Container()
    //     0xb7f4f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7f4f4: stur            x0, [fp, #-0x30]
    // 0xb7f4f8: r16 = 45.000000
    //     0xb7f4f8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb7f4fc: ldr             x16, [x16, #0xc88]
    // 0xb7f500: r30 = 45.000000
    //     0xb7f500: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb7f504: ldr             lr, [lr, #0xc88]
    // 0xb7f508: stp             lr, x16, [SP, #0x10]
    // 0xb7f50c: ldur            x16, [fp, #-0x28]
    // 0xb7f510: ldur            lr, [fp, #-0x20]
    // 0xb7f514: stp             lr, x16, [SP]
    // 0xb7f518: mov             x1, x0
    // 0xb7f51c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb7f51c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb7f520: ldr             x4, [x4, #0x870]
    // 0xb7f524: r0 = Container()
    //     0xb7f524: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7f528: ldur            x0, [fp, #-8]
    // 0xb7f52c: LoadField: r1 = r0->field_b
    //     0xb7f52c: ldur            w1, [x0, #0xb]
    // 0xb7f530: DecompressPointer r1
    //     0xb7f530: add             x1, x1, HEAP, lsl #32
    // 0xb7f534: cmp             w1, NULL
    // 0xb7f538: b.eq            #0xb80538
    // 0xb7f53c: LoadField: r2 = r1->field_b
    //     0xb7f53c: ldur            w2, [x1, #0xb]
    // 0xb7f540: DecompressPointer r2
    //     0xb7f540: add             x2, x2, HEAP, lsl #32
    // 0xb7f544: LoadField: r1 = r2->field_b
    //     0xb7f544: ldur            w1, [x2, #0xb]
    // 0xb7f548: DecompressPointer r1
    //     0xb7f548: add             x1, x1, HEAP, lsl #32
    // 0xb7f54c: cmp             w1, NULL
    // 0xb7f550: b.ne            #0xb7f55c
    // 0xb7f554: r1 = Null
    //     0xb7f554: mov             x1, NULL
    // 0xb7f558: b               #0xb7f578
    // 0xb7f55c: LoadField: r2 = r1->field_7
    //     0xb7f55c: ldur            w2, [x1, #7]
    // 0xb7f560: DecompressPointer r2
    //     0xb7f560: add             x2, x2, HEAP, lsl #32
    // 0xb7f564: LoadField: r1 = r2->field_7
    //     0xb7f564: ldur            w1, [x2, #7]
    // 0xb7f568: DecompressPointer r1
    //     0xb7f568: add             x1, x1, HEAP, lsl #32
    // 0xb7f56c: LoadField: r2 = r1->field_7
    //     0xb7f56c: ldur            w2, [x1, #7]
    // 0xb7f570: DecompressPointer r2
    //     0xb7f570: add             x2, x2, HEAP, lsl #32
    // 0xb7f574: mov             x1, x2
    // 0xb7f578: cmp             w1, NULL
    // 0xb7f57c: b.ne            #0xb7f584
    // 0xb7f580: r1 = false
    //     0xb7f580: add             x1, NULL, #0x30  ; false
    // 0xb7f584: stur            x1, [fp, #-0x20]
    // 0xb7f588: r0 = SvgPicture()
    //     0xb7f588: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7f58c: stur            x0, [fp, #-0x28]
    // 0xb7f590: r16 = Instance_BoxFit
    //     0xb7f590: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7f594: ldr             x16, [x16, #0xb18]
    // 0xb7f598: str             x16, [SP]
    // 0xb7f59c: mov             x1, x0
    // 0xb7f5a0: r2 = "assets/images/replacement_check.svg"
    //     0xb7f5a0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xb7f5a4: ldr             x2, [x2, #0xc90]
    // 0xb7f5a8: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb7f5a8: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb7f5ac: ldr             x4, [x4, #0xb0]
    // 0xb7f5b0: r0 = SvgPicture.asset()
    //     0xb7f5b0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7f5b4: r0 = Visibility()
    //     0xb7f5b4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7f5b8: mov             x1, x0
    // 0xb7f5bc: ldur            x0, [fp, #-0x28]
    // 0xb7f5c0: stur            x1, [fp, #-0x38]
    // 0xb7f5c4: StoreField: r1->field_b = r0
    //     0xb7f5c4: stur            w0, [x1, #0xb]
    // 0xb7f5c8: r0 = Instance_SizedBox
    //     0xb7f5c8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7f5cc: StoreField: r1->field_f = r0
    //     0xb7f5cc: stur            w0, [x1, #0xf]
    // 0xb7f5d0: ldur            x2, [fp, #-0x20]
    // 0xb7f5d4: StoreField: r1->field_13 = r2
    //     0xb7f5d4: stur            w2, [x1, #0x13]
    // 0xb7f5d8: r2 = false
    //     0xb7f5d8: add             x2, NULL, #0x30  ; false
    // 0xb7f5dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7f5dc: stur            w2, [x1, #0x17]
    // 0xb7f5e0: StoreField: r1->field_1b = r2
    //     0xb7f5e0: stur            w2, [x1, #0x1b]
    // 0xb7f5e4: StoreField: r1->field_1f = r2
    //     0xb7f5e4: stur            w2, [x1, #0x1f]
    // 0xb7f5e8: StoreField: r1->field_23 = r2
    //     0xb7f5e8: stur            w2, [x1, #0x23]
    // 0xb7f5ec: StoreField: r1->field_27 = r2
    //     0xb7f5ec: stur            w2, [x1, #0x27]
    // 0xb7f5f0: StoreField: r1->field_2b = r2
    //     0xb7f5f0: stur            w2, [x1, #0x2b]
    // 0xb7f5f4: ldur            x3, [fp, #-8]
    // 0xb7f5f8: LoadField: r4 = r3->field_b
    //     0xb7f5f8: ldur            w4, [x3, #0xb]
    // 0xb7f5fc: DecompressPointer r4
    //     0xb7f5fc: add             x4, x4, HEAP, lsl #32
    // 0xb7f600: cmp             w4, NULL
    // 0xb7f604: b.eq            #0xb8053c
    // 0xb7f608: LoadField: r5 = r4->field_b
    //     0xb7f608: ldur            w5, [x4, #0xb]
    // 0xb7f60c: DecompressPointer r5
    //     0xb7f60c: add             x5, x5, HEAP, lsl #32
    // 0xb7f610: LoadField: r4 = r5->field_b
    //     0xb7f610: ldur            w4, [x5, #0xb]
    // 0xb7f614: DecompressPointer r4
    //     0xb7f614: add             x4, x4, HEAP, lsl #32
    // 0xb7f618: cmp             w4, NULL
    // 0xb7f61c: b.ne            #0xb7f628
    // 0xb7f620: r4 = Null
    //     0xb7f620: mov             x4, NULL
    // 0xb7f624: b               #0xb7f644
    // 0xb7f628: LoadField: r5 = r4->field_7
    //     0xb7f628: ldur            w5, [x4, #7]
    // 0xb7f62c: DecompressPointer r5
    //     0xb7f62c: add             x5, x5, HEAP, lsl #32
    // 0xb7f630: LoadField: r4 = r5->field_7
    //     0xb7f630: ldur            w4, [x5, #7]
    // 0xb7f634: DecompressPointer r4
    //     0xb7f634: add             x4, x4, HEAP, lsl #32
    // 0xb7f638: LoadField: r5 = r4->field_7
    //     0xb7f638: ldur            w5, [x4, #7]
    // 0xb7f63c: DecompressPointer r5
    //     0xb7f63c: add             x5, x5, HEAP, lsl #32
    // 0xb7f640: mov             x4, x5
    // 0xb7f644: cmp             w4, NULL
    // 0xb7f648: b.ne            #0xb7f654
    // 0xb7f64c: r5 = false
    //     0xb7f64c: add             x5, NULL, #0x30  ; false
    // 0xb7f650: b               #0xb7f658
    // 0xb7f654: mov             x5, x4
    // 0xb7f658: ldur            x4, [fp, #-0x30]
    // 0xb7f65c: stur            x5, [fp, #-0x20]
    // 0xb7f660: r0 = SvgPicture()
    //     0xb7f660: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7f664: stur            x0, [fp, #-0x28]
    // 0xb7f668: r16 = Instance_BoxFit
    //     0xb7f668: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7f66c: ldr             x16, [x16, #0xb18]
    // 0xb7f670: str             x16, [SP]
    // 0xb7f674: mov             x1, x0
    // 0xb7f678: r2 = "assets/images/exchange_check.svg"
    //     0xb7f678: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xb7f67c: ldr             x2, [x2, #0xc98]
    // 0xb7f680: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb7f680: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb7f684: ldr             x4, [x4, #0xb0]
    // 0xb7f688: r0 = SvgPicture.asset()
    //     0xb7f688: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7f68c: r0 = Visibility()
    //     0xb7f68c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7f690: mov             x3, x0
    // 0xb7f694: ldur            x0, [fp, #-0x28]
    // 0xb7f698: stur            x3, [fp, #-0x40]
    // 0xb7f69c: StoreField: r3->field_b = r0
    //     0xb7f69c: stur            w0, [x3, #0xb]
    // 0xb7f6a0: r0 = Instance_SizedBox
    //     0xb7f6a0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7f6a4: StoreField: r3->field_f = r0
    //     0xb7f6a4: stur            w0, [x3, #0xf]
    // 0xb7f6a8: ldur            x1, [fp, #-0x20]
    // 0xb7f6ac: StoreField: r3->field_13 = r1
    //     0xb7f6ac: stur            w1, [x3, #0x13]
    // 0xb7f6b0: r4 = false
    //     0xb7f6b0: add             x4, NULL, #0x30  ; false
    // 0xb7f6b4: ArrayStore: r3[0] = r4  ; List_4
    //     0xb7f6b4: stur            w4, [x3, #0x17]
    // 0xb7f6b8: StoreField: r3->field_1b = r4
    //     0xb7f6b8: stur            w4, [x3, #0x1b]
    // 0xb7f6bc: StoreField: r3->field_1f = r4
    //     0xb7f6bc: stur            w4, [x3, #0x1f]
    // 0xb7f6c0: StoreField: r3->field_23 = r4
    //     0xb7f6c0: stur            w4, [x3, #0x23]
    // 0xb7f6c4: StoreField: r3->field_27 = r4
    //     0xb7f6c4: stur            w4, [x3, #0x27]
    // 0xb7f6c8: StoreField: r3->field_2b = r4
    //     0xb7f6c8: stur            w4, [x3, #0x2b]
    // 0xb7f6cc: r1 = Null
    //     0xb7f6cc: mov             x1, NULL
    // 0xb7f6d0: r2 = 6
    //     0xb7f6d0: movz            x2, #0x6
    // 0xb7f6d4: r0 = AllocateArray()
    //     0xb7f6d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7f6d8: mov             x2, x0
    // 0xb7f6dc: ldur            x0, [fp, #-0x30]
    // 0xb7f6e0: stur            x2, [fp, #-0x20]
    // 0xb7f6e4: StoreField: r2->field_f = r0
    //     0xb7f6e4: stur            w0, [x2, #0xf]
    // 0xb7f6e8: ldur            x0, [fp, #-0x38]
    // 0xb7f6ec: StoreField: r2->field_13 = r0
    //     0xb7f6ec: stur            w0, [x2, #0x13]
    // 0xb7f6f0: ldur            x0, [fp, #-0x40]
    // 0xb7f6f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7f6f4: stur            w0, [x2, #0x17]
    // 0xb7f6f8: r1 = <Widget>
    //     0xb7f6f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7f6fc: r0 = AllocateGrowableArray()
    //     0xb7f6fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7f700: mov             x1, x0
    // 0xb7f704: ldur            x0, [fp, #-0x20]
    // 0xb7f708: stur            x1, [fp, #-0x28]
    // 0xb7f70c: StoreField: r1->field_f = r0
    //     0xb7f70c: stur            w0, [x1, #0xf]
    // 0xb7f710: r2 = 6
    //     0xb7f710: movz            x2, #0x6
    // 0xb7f714: StoreField: r1->field_b = r2
    //     0xb7f714: stur            w2, [x1, #0xb]
    // 0xb7f718: r0 = Stack()
    //     0xb7f718: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb7f71c: mov             x1, x0
    // 0xb7f720: r0 = Instance_Alignment
    //     0xb7f720: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb7f724: ldr             x0, [x0, #0x950]
    // 0xb7f728: stur            x1, [fp, #-0x20]
    // 0xb7f72c: StoreField: r1->field_f = r0
    //     0xb7f72c: stur            w0, [x1, #0xf]
    // 0xb7f730: r2 = Instance_StackFit
    //     0xb7f730: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb7f734: ldr             x2, [x2, #0xfa8]
    // 0xb7f738: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7f738: stur            w2, [x1, #0x17]
    // 0xb7f73c: r3 = Instance_Clip
    //     0xb7f73c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb7f740: ldr             x3, [x3, #0x7e0]
    // 0xb7f744: StoreField: r1->field_1b = r3
    //     0xb7f744: stur            w3, [x1, #0x1b]
    // 0xb7f748: ldur            x4, [fp, #-0x28]
    // 0xb7f74c: StoreField: r1->field_b = r4
    //     0xb7f74c: stur            w4, [x1, #0xb]
    // 0xb7f750: ldur            x4, [fp, #-8]
    // 0xb7f754: LoadField: r5 = r4->field_b
    //     0xb7f754: ldur            w5, [x4, #0xb]
    // 0xb7f758: DecompressPointer r5
    //     0xb7f758: add             x5, x5, HEAP, lsl #32
    // 0xb7f75c: cmp             w5, NULL
    // 0xb7f760: b.eq            #0xb80540
    // 0xb7f764: LoadField: r6 = r5->field_b
    //     0xb7f764: ldur            w6, [x5, #0xb]
    // 0xb7f768: DecompressPointer r6
    //     0xb7f768: add             x6, x6, HEAP, lsl #32
    // 0xb7f76c: LoadField: r5 = r6->field_b
    //     0xb7f76c: ldur            w5, [x6, #0xb]
    // 0xb7f770: DecompressPointer r5
    //     0xb7f770: add             x5, x5, HEAP, lsl #32
    // 0xb7f774: cmp             w5, NULL
    // 0xb7f778: b.ne            #0xb7f784
    // 0xb7f77c: r5 = Null
    //     0xb7f77c: mov             x5, NULL
    // 0xb7f780: b               #0xb7f7a0
    // 0xb7f784: LoadField: r6 = r5->field_7
    //     0xb7f784: ldur            w6, [x5, #7]
    // 0xb7f788: DecompressPointer r6
    //     0xb7f788: add             x6, x6, HEAP, lsl #32
    // 0xb7f78c: LoadField: r5 = r6->field_7
    //     0xb7f78c: ldur            w5, [x6, #7]
    // 0xb7f790: DecompressPointer r5
    //     0xb7f790: add             x5, x5, HEAP, lsl #32
    // 0xb7f794: LoadField: r6 = r5->field_b
    //     0xb7f794: ldur            w6, [x5, #0xb]
    // 0xb7f798: DecompressPointer r6
    //     0xb7f798: add             x6, x6, HEAP, lsl #32
    // 0xb7f79c: mov             x5, x6
    // 0xb7f7a0: str             x5, [SP]
    // 0xb7f7a4: r0 = _interpolateSingle()
    //     0xb7f7a4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb7f7a8: ldur            x1, [fp, #-0x10]
    // 0xb7f7ac: stur            x0, [fp, #-0x28]
    // 0xb7f7b0: r0 = of()
    //     0xb7f7b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7f7b4: LoadField: r1 = r0->field_87
    //     0xb7f7b4: ldur            w1, [x0, #0x87]
    // 0xb7f7b8: DecompressPointer r1
    //     0xb7f7b8: add             x1, x1, HEAP, lsl #32
    // 0xb7f7bc: LoadField: r0 = r1->field_2b
    //     0xb7f7bc: ldur            w0, [x1, #0x2b]
    // 0xb7f7c0: DecompressPointer r0
    //     0xb7f7c0: add             x0, x0, HEAP, lsl #32
    // 0xb7f7c4: r16 = 12.000000
    //     0xb7f7c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7f7c8: ldr             x16, [x16, #0x9e8]
    // 0xb7f7cc: r30 = Instance_Color
    //     0xb7f7cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7f7d0: stp             lr, x16, [SP]
    // 0xb7f7d4: mov             x1, x0
    // 0xb7f7d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7f7d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7f7dc: ldr             x4, [x4, #0xaa0]
    // 0xb7f7e0: r0 = copyWith()
    //     0xb7f7e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7f7e4: stur            x0, [fp, #-0x30]
    // 0xb7f7e8: r0 = Text()
    //     0xb7f7e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7f7ec: mov             x1, x0
    // 0xb7f7f0: ldur            x0, [fp, #-0x28]
    // 0xb7f7f4: stur            x1, [fp, #-0x38]
    // 0xb7f7f8: StoreField: r1->field_b = r0
    //     0xb7f7f8: stur            w0, [x1, #0xb]
    // 0xb7f7fc: ldur            x0, [fp, #-0x30]
    // 0xb7f800: StoreField: r1->field_13 = r0
    //     0xb7f800: stur            w0, [x1, #0x13]
    // 0xb7f804: r0 = Padding()
    //     0xb7f804: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7f808: mov             x3, x0
    // 0xb7f80c: r0 = Instance_EdgeInsets
    //     0xb7f80c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb7f810: ldr             x0, [x0, #0x770]
    // 0xb7f814: stur            x3, [fp, #-0x28]
    // 0xb7f818: StoreField: r3->field_f = r0
    //     0xb7f818: stur            w0, [x3, #0xf]
    // 0xb7f81c: ldur            x1, [fp, #-0x38]
    // 0xb7f820: StoreField: r3->field_b = r1
    //     0xb7f820: stur            w1, [x3, #0xb]
    // 0xb7f824: r1 = Null
    //     0xb7f824: mov             x1, NULL
    // 0xb7f828: r2 = 4
    //     0xb7f828: movz            x2, #0x4
    // 0xb7f82c: r0 = AllocateArray()
    //     0xb7f82c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7f830: mov             x2, x0
    // 0xb7f834: ldur            x0, [fp, #-0x20]
    // 0xb7f838: stur            x2, [fp, #-0x30]
    // 0xb7f83c: StoreField: r2->field_f = r0
    //     0xb7f83c: stur            w0, [x2, #0xf]
    // 0xb7f840: ldur            x0, [fp, #-0x28]
    // 0xb7f844: StoreField: r2->field_13 = r0
    //     0xb7f844: stur            w0, [x2, #0x13]
    // 0xb7f848: r1 = <Widget>
    //     0xb7f848: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7f84c: r0 = AllocateGrowableArray()
    //     0xb7f84c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7f850: mov             x1, x0
    // 0xb7f854: ldur            x0, [fp, #-0x30]
    // 0xb7f858: stur            x1, [fp, #-0x20]
    // 0xb7f85c: StoreField: r1->field_f = r0
    //     0xb7f85c: stur            w0, [x1, #0xf]
    // 0xb7f860: r2 = 4
    //     0xb7f860: movz            x2, #0x4
    // 0xb7f864: StoreField: r1->field_b = r2
    //     0xb7f864: stur            w2, [x1, #0xb]
    // 0xb7f868: r0 = Column()
    //     0xb7f868: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7f86c: mov             x2, x0
    // 0xb7f870: r0 = Instance_Axis
    //     0xb7f870: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7f874: stur            x2, [fp, #-0x28]
    // 0xb7f878: StoreField: r2->field_f = r0
    //     0xb7f878: stur            w0, [x2, #0xf]
    // 0xb7f87c: r3 = Instance_MainAxisAlignment
    //     0xb7f87c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7f880: ldr             x3, [x3, #0xa08]
    // 0xb7f884: StoreField: r2->field_13 = r3
    //     0xb7f884: stur            w3, [x2, #0x13]
    // 0xb7f888: r4 = Instance_MainAxisSize
    //     0xb7f888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7f88c: ldr             x4, [x4, #0xa10]
    // 0xb7f890: ArrayStore: r2[0] = r4  ; List_4
    //     0xb7f890: stur            w4, [x2, #0x17]
    // 0xb7f894: r5 = Instance_CrossAxisAlignment
    //     0xb7f894: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7f898: ldr             x5, [x5, #0xa18]
    // 0xb7f89c: StoreField: r2->field_1b = r5
    //     0xb7f89c: stur            w5, [x2, #0x1b]
    // 0xb7f8a0: r6 = Instance_VerticalDirection
    //     0xb7f8a0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7f8a4: ldr             x6, [x6, #0xa20]
    // 0xb7f8a8: StoreField: r2->field_23 = r6
    //     0xb7f8a8: stur            w6, [x2, #0x23]
    // 0xb7f8ac: r7 = Instance_Clip
    //     0xb7f8ac: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7f8b0: ldr             x7, [x7, #0x38]
    // 0xb7f8b4: StoreField: r2->field_2b = r7
    //     0xb7f8b4: stur            w7, [x2, #0x2b]
    // 0xb7f8b8: StoreField: r2->field_2f = rZR
    //     0xb7f8b8: stur            xzr, [x2, #0x2f]
    // 0xb7f8bc: ldur            x1, [fp, #-0x20]
    // 0xb7f8c0: StoreField: r2->field_b = r1
    //     0xb7f8c0: stur            w1, [x2, #0xb]
    // 0xb7f8c4: ldur            x1, [fp, #-0x10]
    // 0xb7f8c8: r0 = of()
    //     0xb7f8c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7f8cc: LoadField: r1 = r0->field_5b
    //     0xb7f8cc: ldur            w1, [x0, #0x5b]
    // 0xb7f8d0: DecompressPointer r1
    //     0xb7f8d0: add             x1, x1, HEAP, lsl #32
    // 0xb7f8d4: stur            x1, [fp, #-0x20]
    // 0xb7f8d8: r0 = BoxDecoration()
    //     0xb7f8d8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7f8dc: mov             x1, x0
    // 0xb7f8e0: ldur            x0, [fp, #-0x20]
    // 0xb7f8e4: stur            x1, [fp, #-0x30]
    // 0xb7f8e8: StoreField: r1->field_7 = r0
    //     0xb7f8e8: stur            w0, [x1, #7]
    // 0xb7f8ec: r0 = Instance_BoxShape
    //     0xb7f8ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb7f8f0: ldr             x0, [x0, #0x970]
    // 0xb7f8f4: StoreField: r1->field_23 = r0
    //     0xb7f8f4: stur            w0, [x1, #0x23]
    // 0xb7f8f8: r0 = SvgPicture()
    //     0xb7f8f8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7f8fc: stur            x0, [fp, #-0x20]
    // 0xb7f900: r16 = Instance_BoxFit
    //     0xb7f900: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7f904: ldr             x16, [x16, #0xb18]
    // 0xb7f908: str             x16, [SP]
    // 0xb7f90c: mov             x1, x0
    // 0xb7f910: r2 = "assets/images/exchange.svg"
    //     0xb7f910: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xb7f914: ldr             x2, [x2, #0xca0]
    // 0xb7f918: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb7f918: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb7f91c: ldr             x4, [x4, #0xb0]
    // 0xb7f920: r0 = SvgPicture.asset()
    //     0xb7f920: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7f924: r0 = Container()
    //     0xb7f924: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7f928: stur            x0, [fp, #-0x38]
    // 0xb7f92c: r16 = 45.000000
    //     0xb7f92c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb7f930: ldr             x16, [x16, #0xc88]
    // 0xb7f934: r30 = 45.000000
    //     0xb7f934: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb7f938: ldr             lr, [lr, #0xc88]
    // 0xb7f93c: stp             lr, x16, [SP, #0x10]
    // 0xb7f940: ldur            x16, [fp, #-0x30]
    // 0xb7f944: ldur            lr, [fp, #-0x20]
    // 0xb7f948: stp             lr, x16, [SP]
    // 0xb7f94c: mov             x1, x0
    // 0xb7f950: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb7f950: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb7f954: ldr             x4, [x4, #0x870]
    // 0xb7f958: r0 = Container()
    //     0xb7f958: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7f95c: ldur            x0, [fp, #-8]
    // 0xb7f960: LoadField: r1 = r0->field_b
    //     0xb7f960: ldur            w1, [x0, #0xb]
    // 0xb7f964: DecompressPointer r1
    //     0xb7f964: add             x1, x1, HEAP, lsl #32
    // 0xb7f968: cmp             w1, NULL
    // 0xb7f96c: b.eq            #0xb80544
    // 0xb7f970: LoadField: r2 = r1->field_b
    //     0xb7f970: ldur            w2, [x1, #0xb]
    // 0xb7f974: DecompressPointer r2
    //     0xb7f974: add             x2, x2, HEAP, lsl #32
    // 0xb7f978: LoadField: r1 = r2->field_b
    //     0xb7f978: ldur            w1, [x2, #0xb]
    // 0xb7f97c: DecompressPointer r1
    //     0xb7f97c: add             x1, x1, HEAP, lsl #32
    // 0xb7f980: cmp             w1, NULL
    // 0xb7f984: b.ne            #0xb7f990
    // 0xb7f988: r1 = Null
    //     0xb7f988: mov             x1, NULL
    // 0xb7f98c: b               #0xb7f9ac
    // 0xb7f990: LoadField: r2 = r1->field_7
    //     0xb7f990: ldur            w2, [x1, #7]
    // 0xb7f994: DecompressPointer r2
    //     0xb7f994: add             x2, x2, HEAP, lsl #32
    // 0xb7f998: LoadField: r1 = r2->field_7
    //     0xb7f998: ldur            w1, [x2, #7]
    // 0xb7f99c: DecompressPointer r1
    //     0xb7f99c: add             x1, x1, HEAP, lsl #32
    // 0xb7f9a0: LoadField: r2 = r1->field_7
    //     0xb7f9a0: ldur            w2, [x1, #7]
    // 0xb7f9a4: DecompressPointer r2
    //     0xb7f9a4: add             x2, x2, HEAP, lsl #32
    // 0xb7f9a8: mov             x1, x2
    // 0xb7f9ac: cmp             w1, NULL
    // 0xb7f9b0: b.ne            #0xb7f9b8
    // 0xb7f9b4: r1 = false
    //     0xb7f9b4: add             x1, NULL, #0x30  ; false
    // 0xb7f9b8: stur            x1, [fp, #-0x20]
    // 0xb7f9bc: r0 = SvgPicture()
    //     0xb7f9bc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7f9c0: stur            x0, [fp, #-0x30]
    // 0xb7f9c4: r16 = Instance_BoxFit
    //     0xb7f9c4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7f9c8: ldr             x16, [x16, #0xb18]
    // 0xb7f9cc: str             x16, [SP]
    // 0xb7f9d0: mov             x1, x0
    // 0xb7f9d4: r2 = "assets/images/exchange_check.svg"
    //     0xb7f9d4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xb7f9d8: ldr             x2, [x2, #0xc98]
    // 0xb7f9dc: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb7f9dc: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb7f9e0: ldr             x4, [x4, #0xb0]
    // 0xb7f9e4: r0 = SvgPicture.asset()
    //     0xb7f9e4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7f9e8: r0 = Visibility()
    //     0xb7f9e8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7f9ec: mov             x1, x0
    // 0xb7f9f0: ldur            x0, [fp, #-0x30]
    // 0xb7f9f4: stur            x1, [fp, #-0x40]
    // 0xb7f9f8: StoreField: r1->field_b = r0
    //     0xb7f9f8: stur            w0, [x1, #0xb]
    // 0xb7f9fc: r0 = Instance_SizedBox
    //     0xb7f9fc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7fa00: StoreField: r1->field_f = r0
    //     0xb7fa00: stur            w0, [x1, #0xf]
    // 0xb7fa04: ldur            x2, [fp, #-0x20]
    // 0xb7fa08: StoreField: r1->field_13 = r2
    //     0xb7fa08: stur            w2, [x1, #0x13]
    // 0xb7fa0c: r2 = false
    //     0xb7fa0c: add             x2, NULL, #0x30  ; false
    // 0xb7fa10: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7fa10: stur            w2, [x1, #0x17]
    // 0xb7fa14: StoreField: r1->field_1b = r2
    //     0xb7fa14: stur            w2, [x1, #0x1b]
    // 0xb7fa18: StoreField: r1->field_1f = r2
    //     0xb7fa18: stur            w2, [x1, #0x1f]
    // 0xb7fa1c: StoreField: r1->field_23 = r2
    //     0xb7fa1c: stur            w2, [x1, #0x23]
    // 0xb7fa20: StoreField: r1->field_27 = r2
    //     0xb7fa20: stur            w2, [x1, #0x27]
    // 0xb7fa24: StoreField: r1->field_2b = r2
    //     0xb7fa24: stur            w2, [x1, #0x2b]
    // 0xb7fa28: ldur            x3, [fp, #-8]
    // 0xb7fa2c: LoadField: r4 = r3->field_b
    //     0xb7fa2c: ldur            w4, [x3, #0xb]
    // 0xb7fa30: DecompressPointer r4
    //     0xb7fa30: add             x4, x4, HEAP, lsl #32
    // 0xb7fa34: cmp             w4, NULL
    // 0xb7fa38: b.eq            #0xb80548
    // 0xb7fa3c: LoadField: r5 = r4->field_b
    //     0xb7fa3c: ldur            w5, [x4, #0xb]
    // 0xb7fa40: DecompressPointer r5
    //     0xb7fa40: add             x5, x5, HEAP, lsl #32
    // 0xb7fa44: LoadField: r4 = r5->field_b
    //     0xb7fa44: ldur            w4, [x5, #0xb]
    // 0xb7fa48: DecompressPointer r4
    //     0xb7fa48: add             x4, x4, HEAP, lsl #32
    // 0xb7fa4c: cmp             w4, NULL
    // 0xb7fa50: b.ne            #0xb7fa5c
    // 0xb7fa54: r4 = Null
    //     0xb7fa54: mov             x4, NULL
    // 0xb7fa58: b               #0xb7fa78
    // 0xb7fa5c: LoadField: r5 = r4->field_7
    //     0xb7fa5c: ldur            w5, [x4, #7]
    // 0xb7fa60: DecompressPointer r5
    //     0xb7fa60: add             x5, x5, HEAP, lsl #32
    // 0xb7fa64: LoadField: r4 = r5->field_7
    //     0xb7fa64: ldur            w4, [x5, #7]
    // 0xb7fa68: DecompressPointer r4
    //     0xb7fa68: add             x4, x4, HEAP, lsl #32
    // 0xb7fa6c: LoadField: r5 = r4->field_7
    //     0xb7fa6c: ldur            w5, [x4, #7]
    // 0xb7fa70: DecompressPointer r5
    //     0xb7fa70: add             x5, x5, HEAP, lsl #32
    // 0xb7fa74: mov             x4, x5
    // 0xb7fa78: cmp             w4, NULL
    // 0xb7fa7c: b.ne            #0xb7fa88
    // 0xb7fa80: r5 = false
    //     0xb7fa80: add             x5, NULL, #0x30  ; false
    // 0xb7fa84: b               #0xb7fa8c
    // 0xb7fa88: mov             x5, x4
    // 0xb7fa8c: ldur            x4, [fp, #-0x38]
    // 0xb7fa90: stur            x5, [fp, #-0x20]
    // 0xb7fa94: r0 = SvgPicture()
    //     0xb7fa94: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7fa98: stur            x0, [fp, #-0x30]
    // 0xb7fa9c: r16 = Instance_BoxFit
    //     0xb7fa9c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7faa0: ldr             x16, [x16, #0xb18]
    // 0xb7faa4: str             x16, [SP]
    // 0xb7faa8: mov             x1, x0
    // 0xb7faac: r2 = "assets/images/replacement_check.svg"
    //     0xb7faac: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xb7fab0: ldr             x2, [x2, #0xc90]
    // 0xb7fab4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb7fab4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb7fab8: ldr             x4, [x4, #0xb0]
    // 0xb7fabc: r0 = SvgPicture.asset()
    //     0xb7fabc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7fac0: r0 = Visibility()
    //     0xb7fac0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7fac4: mov             x3, x0
    // 0xb7fac8: ldur            x0, [fp, #-0x30]
    // 0xb7facc: stur            x3, [fp, #-0x48]
    // 0xb7fad0: StoreField: r3->field_b = r0
    //     0xb7fad0: stur            w0, [x3, #0xb]
    // 0xb7fad4: r0 = Instance_SizedBox
    //     0xb7fad4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7fad8: StoreField: r3->field_f = r0
    //     0xb7fad8: stur            w0, [x3, #0xf]
    // 0xb7fadc: ldur            x0, [fp, #-0x20]
    // 0xb7fae0: StoreField: r3->field_13 = r0
    //     0xb7fae0: stur            w0, [x3, #0x13]
    // 0xb7fae4: r0 = false
    //     0xb7fae4: add             x0, NULL, #0x30  ; false
    // 0xb7fae8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7fae8: stur            w0, [x3, #0x17]
    // 0xb7faec: StoreField: r3->field_1b = r0
    //     0xb7faec: stur            w0, [x3, #0x1b]
    // 0xb7faf0: StoreField: r3->field_1f = r0
    //     0xb7faf0: stur            w0, [x3, #0x1f]
    // 0xb7faf4: StoreField: r3->field_23 = r0
    //     0xb7faf4: stur            w0, [x3, #0x23]
    // 0xb7faf8: StoreField: r3->field_27 = r0
    //     0xb7faf8: stur            w0, [x3, #0x27]
    // 0xb7fafc: StoreField: r3->field_2b = r0
    //     0xb7fafc: stur            w0, [x3, #0x2b]
    // 0xb7fb00: r1 = Null
    //     0xb7fb00: mov             x1, NULL
    // 0xb7fb04: r2 = 6
    //     0xb7fb04: movz            x2, #0x6
    // 0xb7fb08: r0 = AllocateArray()
    //     0xb7fb08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7fb0c: mov             x2, x0
    // 0xb7fb10: ldur            x0, [fp, #-0x38]
    // 0xb7fb14: stur            x2, [fp, #-0x20]
    // 0xb7fb18: StoreField: r2->field_f = r0
    //     0xb7fb18: stur            w0, [x2, #0xf]
    // 0xb7fb1c: ldur            x0, [fp, #-0x40]
    // 0xb7fb20: StoreField: r2->field_13 = r0
    //     0xb7fb20: stur            w0, [x2, #0x13]
    // 0xb7fb24: ldur            x0, [fp, #-0x48]
    // 0xb7fb28: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7fb28: stur            w0, [x2, #0x17]
    // 0xb7fb2c: r1 = <Widget>
    //     0xb7fb2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7fb30: r0 = AllocateGrowableArray()
    //     0xb7fb30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7fb34: mov             x1, x0
    // 0xb7fb38: ldur            x0, [fp, #-0x20]
    // 0xb7fb3c: stur            x1, [fp, #-0x30]
    // 0xb7fb40: StoreField: r1->field_f = r0
    //     0xb7fb40: stur            w0, [x1, #0xf]
    // 0xb7fb44: r0 = 6
    //     0xb7fb44: movz            x0, #0x6
    // 0xb7fb48: StoreField: r1->field_b = r0
    //     0xb7fb48: stur            w0, [x1, #0xb]
    // 0xb7fb4c: r0 = Stack()
    //     0xb7fb4c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb7fb50: mov             x1, x0
    // 0xb7fb54: r0 = Instance_Alignment
    //     0xb7fb54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb7fb58: ldr             x0, [x0, #0x950]
    // 0xb7fb5c: stur            x1, [fp, #-0x20]
    // 0xb7fb60: StoreField: r1->field_f = r0
    //     0xb7fb60: stur            w0, [x1, #0xf]
    // 0xb7fb64: r0 = Instance_StackFit
    //     0xb7fb64: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb7fb68: ldr             x0, [x0, #0xfa8]
    // 0xb7fb6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7fb6c: stur            w0, [x1, #0x17]
    // 0xb7fb70: r0 = Instance_Clip
    //     0xb7fb70: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb7fb74: ldr             x0, [x0, #0x7e0]
    // 0xb7fb78: StoreField: r1->field_1b = r0
    //     0xb7fb78: stur            w0, [x1, #0x1b]
    // 0xb7fb7c: ldur            x2, [fp, #-0x30]
    // 0xb7fb80: StoreField: r1->field_b = r2
    //     0xb7fb80: stur            w2, [x1, #0xb]
    // 0xb7fb84: ldur            x2, [fp, #-8]
    // 0xb7fb88: LoadField: r3 = r2->field_b
    //     0xb7fb88: ldur            w3, [x2, #0xb]
    // 0xb7fb8c: DecompressPointer r3
    //     0xb7fb8c: add             x3, x3, HEAP, lsl #32
    // 0xb7fb90: cmp             w3, NULL
    // 0xb7fb94: b.eq            #0xb8054c
    // 0xb7fb98: LoadField: r4 = r3->field_b
    //     0xb7fb98: ldur            w4, [x3, #0xb]
    // 0xb7fb9c: DecompressPointer r4
    //     0xb7fb9c: add             x4, x4, HEAP, lsl #32
    // 0xb7fba0: LoadField: r3 = r4->field_b
    //     0xb7fba0: ldur            w3, [x4, #0xb]
    // 0xb7fba4: DecompressPointer r3
    //     0xb7fba4: add             x3, x3, HEAP, lsl #32
    // 0xb7fba8: cmp             w3, NULL
    // 0xb7fbac: b.ne            #0xb7fbb8
    // 0xb7fbb0: r4 = Null
    //     0xb7fbb0: mov             x4, NULL
    // 0xb7fbb4: b               #0xb7fbd0
    // 0xb7fbb8: LoadField: r4 = r3->field_7
    //     0xb7fbb8: ldur            w4, [x3, #7]
    // 0xb7fbbc: DecompressPointer r4
    //     0xb7fbbc: add             x4, x4, HEAP, lsl #32
    // 0xb7fbc0: LoadField: r3 = r4->field_b
    //     0xb7fbc0: ldur            w3, [x4, #0xb]
    // 0xb7fbc4: DecompressPointer r3
    //     0xb7fbc4: add             x3, x3, HEAP, lsl #32
    // 0xb7fbc8: LoadField: r4 = r3->field_b
    //     0xb7fbc8: ldur            w4, [x3, #0xb]
    // 0xb7fbcc: DecompressPointer r4
    //     0xb7fbcc: add             x4, x4, HEAP, lsl #32
    // 0xb7fbd0: ldur            x3, [fp, #-0x28]
    // 0xb7fbd4: str             x4, [SP]
    // 0xb7fbd8: r0 = _interpolateSingle()
    //     0xb7fbd8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb7fbdc: ldur            x1, [fp, #-0x10]
    // 0xb7fbe0: stur            x0, [fp, #-0x30]
    // 0xb7fbe4: r0 = of()
    //     0xb7fbe4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7fbe8: LoadField: r1 = r0->field_87
    //     0xb7fbe8: ldur            w1, [x0, #0x87]
    // 0xb7fbec: DecompressPointer r1
    //     0xb7fbec: add             x1, x1, HEAP, lsl #32
    // 0xb7fbf0: LoadField: r0 = r1->field_2b
    //     0xb7fbf0: ldur            w0, [x1, #0x2b]
    // 0xb7fbf4: DecompressPointer r0
    //     0xb7fbf4: add             x0, x0, HEAP, lsl #32
    // 0xb7fbf8: r16 = 12.000000
    //     0xb7fbf8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7fbfc: ldr             x16, [x16, #0x9e8]
    // 0xb7fc00: r30 = Instance_Color
    //     0xb7fc00: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7fc04: stp             lr, x16, [SP]
    // 0xb7fc08: mov             x1, x0
    // 0xb7fc0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7fc0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7fc10: ldr             x4, [x4, #0xaa0]
    // 0xb7fc14: r0 = copyWith()
    //     0xb7fc14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7fc18: stur            x0, [fp, #-0x38]
    // 0xb7fc1c: r0 = Text()
    //     0xb7fc1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7fc20: mov             x1, x0
    // 0xb7fc24: ldur            x0, [fp, #-0x30]
    // 0xb7fc28: stur            x1, [fp, #-0x40]
    // 0xb7fc2c: StoreField: r1->field_b = r0
    //     0xb7fc2c: stur            w0, [x1, #0xb]
    // 0xb7fc30: ldur            x0, [fp, #-0x38]
    // 0xb7fc34: StoreField: r1->field_13 = r0
    //     0xb7fc34: stur            w0, [x1, #0x13]
    // 0xb7fc38: r0 = Padding()
    //     0xb7fc38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7fc3c: mov             x3, x0
    // 0xb7fc40: r0 = Instance_EdgeInsets
    //     0xb7fc40: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb7fc44: ldr             x0, [x0, #0x770]
    // 0xb7fc48: stur            x3, [fp, #-0x30]
    // 0xb7fc4c: StoreField: r3->field_f = r0
    //     0xb7fc4c: stur            w0, [x3, #0xf]
    // 0xb7fc50: ldur            x0, [fp, #-0x40]
    // 0xb7fc54: StoreField: r3->field_b = r0
    //     0xb7fc54: stur            w0, [x3, #0xb]
    // 0xb7fc58: r1 = Null
    //     0xb7fc58: mov             x1, NULL
    // 0xb7fc5c: r2 = 4
    //     0xb7fc5c: movz            x2, #0x4
    // 0xb7fc60: r0 = AllocateArray()
    //     0xb7fc60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7fc64: mov             x2, x0
    // 0xb7fc68: ldur            x0, [fp, #-0x20]
    // 0xb7fc6c: stur            x2, [fp, #-0x38]
    // 0xb7fc70: StoreField: r2->field_f = r0
    //     0xb7fc70: stur            w0, [x2, #0xf]
    // 0xb7fc74: ldur            x0, [fp, #-0x30]
    // 0xb7fc78: StoreField: r2->field_13 = r0
    //     0xb7fc78: stur            w0, [x2, #0x13]
    // 0xb7fc7c: r1 = <Widget>
    //     0xb7fc7c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7fc80: r0 = AllocateGrowableArray()
    //     0xb7fc80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7fc84: mov             x1, x0
    // 0xb7fc88: ldur            x0, [fp, #-0x38]
    // 0xb7fc8c: stur            x1, [fp, #-0x20]
    // 0xb7fc90: StoreField: r1->field_f = r0
    //     0xb7fc90: stur            w0, [x1, #0xf]
    // 0xb7fc94: r2 = 4
    //     0xb7fc94: movz            x2, #0x4
    // 0xb7fc98: StoreField: r1->field_b = r2
    //     0xb7fc98: stur            w2, [x1, #0xb]
    // 0xb7fc9c: r0 = Column()
    //     0xb7fc9c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7fca0: mov             x3, x0
    // 0xb7fca4: r0 = Instance_Axis
    //     0xb7fca4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7fca8: stur            x3, [fp, #-0x30]
    // 0xb7fcac: StoreField: r3->field_f = r0
    //     0xb7fcac: stur            w0, [x3, #0xf]
    // 0xb7fcb0: r4 = Instance_MainAxisAlignment
    //     0xb7fcb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7fcb4: ldr             x4, [x4, #0xa08]
    // 0xb7fcb8: StoreField: r3->field_13 = r4
    //     0xb7fcb8: stur            w4, [x3, #0x13]
    // 0xb7fcbc: r5 = Instance_MainAxisSize
    //     0xb7fcbc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7fcc0: ldr             x5, [x5, #0xa10]
    // 0xb7fcc4: ArrayStore: r3[0] = r5  ; List_4
    //     0xb7fcc4: stur            w5, [x3, #0x17]
    // 0xb7fcc8: r6 = Instance_CrossAxisAlignment
    //     0xb7fcc8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7fccc: ldr             x6, [x6, #0xa18]
    // 0xb7fcd0: StoreField: r3->field_1b = r6
    //     0xb7fcd0: stur            w6, [x3, #0x1b]
    // 0xb7fcd4: r7 = Instance_VerticalDirection
    //     0xb7fcd4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7fcd8: ldr             x7, [x7, #0xa20]
    // 0xb7fcdc: StoreField: r3->field_23 = r7
    //     0xb7fcdc: stur            w7, [x3, #0x23]
    // 0xb7fce0: r8 = Instance_Clip
    //     0xb7fce0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7fce4: ldr             x8, [x8, #0x38]
    // 0xb7fce8: StoreField: r3->field_2b = r8
    //     0xb7fce8: stur            w8, [x3, #0x2b]
    // 0xb7fcec: StoreField: r3->field_2f = rZR
    //     0xb7fcec: stur            xzr, [x3, #0x2f]
    // 0xb7fcf0: ldur            x1, [fp, #-0x20]
    // 0xb7fcf4: StoreField: r3->field_b = r1
    //     0xb7fcf4: stur            w1, [x3, #0xb]
    // 0xb7fcf8: r1 = Null
    //     0xb7fcf8: mov             x1, NULL
    // 0xb7fcfc: r2 = 4
    //     0xb7fcfc: movz            x2, #0x4
    // 0xb7fd00: r0 = AllocateArray()
    //     0xb7fd00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7fd04: mov             x2, x0
    // 0xb7fd08: ldur            x0, [fp, #-0x28]
    // 0xb7fd0c: stur            x2, [fp, #-0x20]
    // 0xb7fd10: StoreField: r2->field_f = r0
    //     0xb7fd10: stur            w0, [x2, #0xf]
    // 0xb7fd14: ldur            x0, [fp, #-0x30]
    // 0xb7fd18: StoreField: r2->field_13 = r0
    //     0xb7fd18: stur            w0, [x2, #0x13]
    // 0xb7fd1c: r1 = <Widget>
    //     0xb7fd1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7fd20: r0 = AllocateGrowableArray()
    //     0xb7fd20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7fd24: mov             x1, x0
    // 0xb7fd28: ldur            x0, [fp, #-0x20]
    // 0xb7fd2c: stur            x1, [fp, #-0x28]
    // 0xb7fd30: StoreField: r1->field_f = r0
    //     0xb7fd30: stur            w0, [x1, #0xf]
    // 0xb7fd34: r0 = 4
    //     0xb7fd34: movz            x0, #0x4
    // 0xb7fd38: StoreField: r1->field_b = r0
    //     0xb7fd38: stur            w0, [x1, #0xb]
    // 0xb7fd3c: r0 = Row()
    //     0xb7fd3c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7fd40: mov             x3, x0
    // 0xb7fd44: r0 = Instance_Axis
    //     0xb7fd44: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7fd48: stur            x3, [fp, #-0x20]
    // 0xb7fd4c: StoreField: r3->field_f = r0
    //     0xb7fd4c: stur            w0, [x3, #0xf]
    // 0xb7fd50: r0 = Instance_MainAxisAlignment
    //     0xb7fd50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb7fd54: ldr             x0, [x0, #0xd10]
    // 0xb7fd58: StoreField: r3->field_13 = r0
    //     0xb7fd58: stur            w0, [x3, #0x13]
    // 0xb7fd5c: r0 = Instance_MainAxisSize
    //     0xb7fd5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7fd60: ldr             x0, [x0, #0xa10]
    // 0xb7fd64: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7fd64: stur            w0, [x3, #0x17]
    // 0xb7fd68: r1 = Instance_CrossAxisAlignment
    //     0xb7fd68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7fd6c: ldr             x1, [x1, #0xa18]
    // 0xb7fd70: StoreField: r3->field_1b = r1
    //     0xb7fd70: stur            w1, [x3, #0x1b]
    // 0xb7fd74: r4 = Instance_VerticalDirection
    //     0xb7fd74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7fd78: ldr             x4, [x4, #0xa20]
    // 0xb7fd7c: StoreField: r3->field_23 = r4
    //     0xb7fd7c: stur            w4, [x3, #0x23]
    // 0xb7fd80: r5 = Instance_Clip
    //     0xb7fd80: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7fd84: ldr             x5, [x5, #0x38]
    // 0xb7fd88: StoreField: r3->field_2b = r5
    //     0xb7fd88: stur            w5, [x3, #0x2b]
    // 0xb7fd8c: StoreField: r3->field_2f = rZR
    //     0xb7fd8c: stur            xzr, [x3, #0x2f]
    // 0xb7fd90: ldur            x1, [fp, #-0x28]
    // 0xb7fd94: StoreField: r3->field_b = r1
    //     0xb7fd94: stur            w1, [x3, #0xb]
    // 0xb7fd98: r1 = <Widget>
    //     0xb7fd98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7fd9c: r2 = 20
    //     0xb7fd9c: movz            x2, #0x14
    // 0xb7fda0: r0 = AllocateArray()
    //     0xb7fda0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7fda4: mov             x1, x0
    // 0xb7fda8: ldur            x0, [fp, #-0x20]
    // 0xb7fdac: stur            x1, [fp, #-0x28]
    // 0xb7fdb0: StoreField: r1->field_f = r0
    //     0xb7fdb0: stur            w0, [x1, #0xf]
    // 0xb7fdb4: r16 = Instance_SizedBox
    //     0xb7fdb4: add             x16, PP, #0x55, lsl #12  ; [pp+0x558b0] Obj!SizedBox@d68081
    //     0xb7fdb8: ldr             x16, [x16, #0x8b0]
    // 0xb7fdbc: StoreField: r1->field_13 = r16
    //     0xb7fdbc: stur            w16, [x1, #0x13]
    // 0xb7fdc0: ldur            x0, [fp, #-8]
    // 0xb7fdc4: LoadField: r2 = r0->field_b
    //     0xb7fdc4: ldur            w2, [x0, #0xb]
    // 0xb7fdc8: DecompressPointer r2
    //     0xb7fdc8: add             x2, x2, HEAP, lsl #32
    // 0xb7fdcc: cmp             w2, NULL
    // 0xb7fdd0: b.eq            #0xb80550
    // 0xb7fdd4: LoadField: r3 = r2->field_b
    //     0xb7fdd4: ldur            w3, [x2, #0xb]
    // 0xb7fdd8: DecompressPointer r3
    //     0xb7fdd8: add             x3, x3, HEAP, lsl #32
    // 0xb7fddc: LoadField: r2 = r3->field_b
    //     0xb7fddc: ldur            w2, [x3, #0xb]
    // 0xb7fde0: DecompressPointer r2
    //     0xb7fde0: add             x2, x2, HEAP, lsl #32
    // 0xb7fde4: cmp             w2, NULL
    // 0xb7fde8: b.ne            #0xb7fdf4
    // 0xb7fdec: r2 = Null
    //     0xb7fdec: mov             x2, NULL
    // 0xb7fdf0: b               #0xb7fe04
    // 0xb7fdf4: LoadField: r3 = r2->field_b
    //     0xb7fdf4: ldur            w3, [x2, #0xb]
    // 0xb7fdf8: DecompressPointer r3
    //     0xb7fdf8: add             x3, x3, HEAP, lsl #32
    // 0xb7fdfc: LoadField: r2 = r3->field_b
    //     0xb7fdfc: ldur            w2, [x3, #0xb]
    // 0xb7fe00: DecompressPointer r2
    //     0xb7fe00: add             x2, x2, HEAP, lsl #32
    // 0xb7fe04: str             x2, [SP]
    // 0xb7fe08: r0 = _interpolateSingle()
    //     0xb7fe08: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb7fe0c: ldur            x1, [fp, #-0x10]
    // 0xb7fe10: stur            x0, [fp, #-0x20]
    // 0xb7fe14: r0 = of()
    //     0xb7fe14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7fe18: LoadField: r1 = r0->field_87
    //     0xb7fe18: ldur            w1, [x0, #0x87]
    // 0xb7fe1c: DecompressPointer r1
    //     0xb7fe1c: add             x1, x1, HEAP, lsl #32
    // 0xb7fe20: LoadField: r0 = r1->field_7
    //     0xb7fe20: ldur            w0, [x1, #7]
    // 0xb7fe24: DecompressPointer r0
    //     0xb7fe24: add             x0, x0, HEAP, lsl #32
    // 0xb7fe28: r16 = 16.000000
    //     0xb7fe28: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb7fe2c: ldr             x16, [x16, #0x188]
    // 0xb7fe30: r30 = Instance_Color
    //     0xb7fe30: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7fe34: stp             lr, x16, [SP]
    // 0xb7fe38: mov             x1, x0
    // 0xb7fe3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7fe3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7fe40: ldr             x4, [x4, #0xaa0]
    // 0xb7fe44: r0 = copyWith()
    //     0xb7fe44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7fe48: stur            x0, [fp, #-0x30]
    // 0xb7fe4c: r0 = Text()
    //     0xb7fe4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7fe50: mov             x1, x0
    // 0xb7fe54: ldur            x0, [fp, #-0x20]
    // 0xb7fe58: stur            x1, [fp, #-0x38]
    // 0xb7fe5c: StoreField: r1->field_b = r0
    //     0xb7fe5c: stur            w0, [x1, #0xb]
    // 0xb7fe60: ldur            x0, [fp, #-0x30]
    // 0xb7fe64: StoreField: r1->field_13 = r0
    //     0xb7fe64: stur            w0, [x1, #0x13]
    // 0xb7fe68: r0 = SizedBox()
    //     0xb7fe68: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb7fe6c: mov             x1, x0
    // 0xb7fe70: r0 = inf
    //     0xb7fe70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb7fe74: ldr             x0, [x0, #0x9f8]
    // 0xb7fe78: StoreField: r1->field_f = r0
    //     0xb7fe78: stur            w0, [x1, #0xf]
    // 0xb7fe7c: ldur            x0, [fp, #-0x38]
    // 0xb7fe80: StoreField: r1->field_b = r0
    //     0xb7fe80: stur            w0, [x1, #0xb]
    // 0xb7fe84: mov             x0, x1
    // 0xb7fe88: ldur            x1, [fp, #-0x28]
    // 0xb7fe8c: ArrayStore: r1[2] = r0  ; List_4
    //     0xb7fe8c: add             x25, x1, #0x17
    //     0xb7fe90: str             w0, [x25]
    //     0xb7fe94: tbz             w0, #0, #0xb7feb0
    //     0xb7fe98: ldurb           w16, [x1, #-1]
    //     0xb7fe9c: ldurb           w17, [x0, #-1]
    //     0xb7fea0: and             x16, x17, x16, lsr #2
    //     0xb7fea4: tst             x16, HEAP, lsr #32
    //     0xb7fea8: b.eq            #0xb7feb0
    //     0xb7feac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7feb0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb7feb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb7feb4: ldr             x0, [x0, #0x1c80]
    //     0xb7feb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb7febc: cmp             w0, w16
    //     0xb7fec0: b.ne            #0xb7fecc
    //     0xb7fec4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb7fec8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb7fecc: r0 = GetNavigation.width()
    //     0xb7fecc: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xb7fed0: mov             v1.16b, v0.16b
    // 0xb7fed4: d0 = 0.800000
    //     0xb7fed4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0xb7fed8: ldr             d0, [x17, #0xb28]
    // 0xb7fedc: fmul            d2, d1, d0
    // 0xb7fee0: ldur            x0, [fp, #-8]
    // 0xb7fee4: stur            d2, [fp, #-0x58]
    // 0xb7fee8: LoadField: r1 = r0->field_b
    //     0xb7fee8: ldur            w1, [x0, #0xb]
    // 0xb7feec: DecompressPointer r1
    //     0xb7feec: add             x1, x1, HEAP, lsl #32
    // 0xb7fef0: cmp             w1, NULL
    // 0xb7fef4: b.eq            #0xb80554
    // 0xb7fef8: LoadField: r2 = r1->field_b
    //     0xb7fef8: ldur            w2, [x1, #0xb]
    // 0xb7fefc: DecompressPointer r2
    //     0xb7fefc: add             x2, x2, HEAP, lsl #32
    // 0xb7ff00: LoadField: r1 = r2->field_b
    //     0xb7ff00: ldur            w1, [x2, #0xb]
    // 0xb7ff04: DecompressPointer r1
    //     0xb7ff04: add             x1, x1, HEAP, lsl #32
    // 0xb7ff08: cmp             w1, NULL
    // 0xb7ff0c: b.ne            #0xb7ff18
    // 0xb7ff10: r4 = Null
    //     0xb7ff10: mov             x4, NULL
    // 0xb7ff14: b               #0xb7ff30
    // 0xb7ff18: LoadField: r2 = r1->field_b
    //     0xb7ff18: ldur            w2, [x1, #0xb]
    // 0xb7ff1c: DecompressPointer r2
    //     0xb7ff1c: add             x2, x2, HEAP, lsl #32
    // 0xb7ff20: LoadField: r1 = r2->field_7
    //     0xb7ff20: ldur            w1, [x2, #7]
    // 0xb7ff24: DecompressPointer r1
    //     0xb7ff24: add             x1, x1, HEAP, lsl #32
    // 0xb7ff28: LoadField: r2 = r1->field_b
    //     0xb7ff28: ldur            w2, [x1, #0xb]
    // 0xb7ff2c: mov             x4, x2
    // 0xb7ff30: ldur            x3, [fp, #-0x28]
    // 0xb7ff34: ldur            x2, [fp, #-0x18]
    // 0xb7ff38: stur            x4, [fp, #-0x20]
    // 0xb7ff3c: r1 = Function '<anonymous closure>':.
    //     0xb7ff3c: add             x1, PP, #0x55, lsl #12  ; [pp+0x558b8] AnonymousClosure: (0xb0623c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xb7f450)
    //     0xb7ff40: ldr             x1, [x1, #0x8b8]
    // 0xb7ff44: r0 = AllocateClosure()
    //     0xb7ff44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7ff48: stur            x0, [fp, #-0x30]
    // 0xb7ff4c: r0 = ListView()
    //     0xb7ff4c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb7ff50: stur            x0, [fp, #-0x38]
    // 0xb7ff54: r16 = true
    //     0xb7ff54: add             x16, NULL, #0x20  ; true
    // 0xb7ff58: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb7ff58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb7ff5c: ldr             lr, [lr, #0x1c8]
    // 0xb7ff60: stp             lr, x16, [SP]
    // 0xb7ff64: mov             x1, x0
    // 0xb7ff68: ldur            x2, [fp, #-0x30]
    // 0xb7ff6c: ldur            x3, [fp, #-0x20]
    // 0xb7ff70: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb7ff70: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb7ff74: ldr             x4, [x4, #8]
    // 0xb7ff78: r0 = ListView.builder()
    //     0xb7ff78: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb7ff7c: ldur            d0, [fp, #-0x58]
    // 0xb7ff80: r0 = inline_Allocate_Double()
    //     0xb7ff80: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb7ff84: add             x0, x0, #0x10
    //     0xb7ff88: cmp             x1, x0
    //     0xb7ff8c: b.ls            #0xb80558
    //     0xb7ff90: str             x0, [THR, #0x50]  ; THR::top
    //     0xb7ff94: sub             x0, x0, #0xf
    //     0xb7ff98: movz            x1, #0xe15c
    //     0xb7ff9c: movk            x1, #0x3, lsl #16
    //     0xb7ffa0: stur            x1, [x0, #-1]
    // 0xb7ffa4: StoreField: r0->field_7 = d0
    //     0xb7ffa4: stur            d0, [x0, #7]
    // 0xb7ffa8: stur            x0, [fp, #-0x20]
    // 0xb7ffac: r0 = Container()
    //     0xb7ffac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7ffb0: stur            x0, [fp, #-0x30]
    // 0xb7ffb4: ldur            x16, [fp, #-0x20]
    // 0xb7ffb8: r30 = Instance_EdgeInsets
    //     0xb7ffb8: add             lr, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb7ffbc: ldr             lr, [lr, #0x858]
    // 0xb7ffc0: stp             lr, x16, [SP, #8]
    // 0xb7ffc4: ldur            x16, [fp, #-0x38]
    // 0xb7ffc8: str             x16, [SP]
    // 0xb7ffcc: mov             x1, x0
    // 0xb7ffd0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xb7ffd0: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xb7ffd4: ldr             x4, [x4, #0x628]
    // 0xb7ffd8: r0 = Container()
    //     0xb7ffd8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7ffdc: ldur            x1, [fp, #-0x28]
    // 0xb7ffe0: ldur            x0, [fp, #-0x30]
    // 0xb7ffe4: ArrayStore: r1[3] = r0  ; List_4
    //     0xb7ffe4: add             x25, x1, #0x1b
    //     0xb7ffe8: str             w0, [x25]
    //     0xb7ffec: tbz             w0, #0, #0xb80008
    //     0xb7fff0: ldurb           w16, [x1, #-1]
    //     0xb7fff4: ldurb           w17, [x0, #-1]
    //     0xb7fff8: and             x16, x17, x16, lsr #2
    //     0xb7fffc: tst             x16, HEAP, lsr #32
    //     0xb80000: b.eq            #0xb80008
    //     0xb80004: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80008: ldur            x1, [fp, #-0x28]
    // 0xb8000c: r16 = Instance_SizedBox
    //     0xb8000c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb80010: ldr             x16, [x16, #0x9f0]
    // 0xb80014: StoreField: r1->field_1f = r16
    //     0xb80014: stur            w16, [x1, #0x1f]
    // 0xb80018: ldur            x0, [fp, #-8]
    // 0xb8001c: LoadField: r2 = r0->field_b
    //     0xb8001c: ldur            w2, [x0, #0xb]
    // 0xb80020: DecompressPointer r2
    //     0xb80020: add             x2, x2, HEAP, lsl #32
    // 0xb80024: cmp             w2, NULL
    // 0xb80028: b.eq            #0xb80568
    // 0xb8002c: LoadField: r3 = r2->field_b
    //     0xb8002c: ldur            w3, [x2, #0xb]
    // 0xb80030: DecompressPointer r3
    //     0xb80030: add             x3, x3, HEAP, lsl #32
    // 0xb80034: LoadField: r2 = r3->field_b
    //     0xb80034: ldur            w2, [x3, #0xb]
    // 0xb80038: DecompressPointer r2
    //     0xb80038: add             x2, x2, HEAP, lsl #32
    // 0xb8003c: cmp             w2, NULL
    // 0xb80040: b.ne            #0xb8004c
    // 0xb80044: r2 = Null
    //     0xb80044: mov             x2, NULL
    // 0xb80048: b               #0xb8005c
    // 0xb8004c: LoadField: r3 = r2->field_f
    //     0xb8004c: ldur            w3, [x2, #0xf]
    // 0xb80050: DecompressPointer r3
    //     0xb80050: add             x3, x3, HEAP, lsl #32
    // 0xb80054: LoadField: r2 = r3->field_7
    //     0xb80054: ldur            w2, [x3, #7]
    // 0xb80058: DecompressPointer r2
    //     0xb80058: add             x2, x2, HEAP, lsl #32
    // 0xb8005c: str             x2, [SP]
    // 0xb80060: r0 = _interpolateSingle()
    //     0xb80060: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb80064: ldur            x1, [fp, #-0x10]
    // 0xb80068: stur            x0, [fp, #-0x20]
    // 0xb8006c: r0 = of()
    //     0xb8006c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb80070: LoadField: r1 = r0->field_87
    //     0xb80070: ldur            w1, [x0, #0x87]
    // 0xb80074: DecompressPointer r1
    //     0xb80074: add             x1, x1, HEAP, lsl #32
    // 0xb80078: LoadField: r0 = r1->field_7
    //     0xb80078: ldur            w0, [x1, #7]
    // 0xb8007c: DecompressPointer r0
    //     0xb8007c: add             x0, x0, HEAP, lsl #32
    // 0xb80080: r16 = 16.000000
    //     0xb80080: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb80084: ldr             x16, [x16, #0x188]
    // 0xb80088: r30 = Instance_Color
    //     0xb80088: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8008c: stp             lr, x16, [SP]
    // 0xb80090: mov             x1, x0
    // 0xb80094: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb80094: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb80098: ldr             x4, [x4, #0xaa0]
    // 0xb8009c: r0 = copyWith()
    //     0xb8009c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb800a0: stur            x0, [fp, #-0x30]
    // 0xb800a4: r0 = Text()
    //     0xb800a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb800a8: mov             x1, x0
    // 0xb800ac: ldur            x0, [fp, #-0x20]
    // 0xb800b0: StoreField: r1->field_b = r0
    //     0xb800b0: stur            w0, [x1, #0xb]
    // 0xb800b4: ldur            x0, [fp, #-0x30]
    // 0xb800b8: StoreField: r1->field_13 = r0
    //     0xb800b8: stur            w0, [x1, #0x13]
    // 0xb800bc: mov             x0, x1
    // 0xb800c0: ldur            x1, [fp, #-0x28]
    // 0xb800c4: ArrayStore: r1[5] = r0  ; List_4
    //     0xb800c4: add             x25, x1, #0x23
    //     0xb800c8: str             w0, [x25]
    //     0xb800cc: tbz             w0, #0, #0xb800e8
    //     0xb800d0: ldurb           w16, [x1, #-1]
    //     0xb800d4: ldurb           w17, [x0, #-1]
    //     0xb800d8: and             x16, x17, x16, lsr #2
    //     0xb800dc: tst             x16, HEAP, lsr #32
    //     0xb800e0: b.eq            #0xb800e8
    //     0xb800e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb800e8: ldur            x0, [fp, #-0x28]
    // 0xb800ec: r16 = Instance_SizedBox
    //     0xb800ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb800f0: ldr             x16, [x16, #0x8b8]
    // 0xb800f4: StoreField: r0->field_27 = r16
    //     0xb800f4: stur            w16, [x0, #0x27]
    // 0xb800f8: r16 = 0.000000
    //     0xb800f8: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb800fc: r30 = Instance_ConnectorThemeData
    //     0xb800fc: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3fe88] Obj!ConnectorThemeData@d5bb71
    //     0xb80100: ldr             lr, [lr, #0xe88]
    // 0xb80104: stp             lr, x16, [SP]
    // 0xb80108: r1 = Null
    //     0xb80108: mov             x1, NULL
    // 0xb8010c: r4 = const [0, 0x3, 0x2, 0x1, connectorTheme, 0x2, nodePosition, 0x1, null]
    //     0xb8010c: add             x4, PP, #0x36, lsl #12  ; [pp+0x365b8] List(9) [0, 0x3, 0x2, 0x1, "connectorTheme", 0x2, "nodePosition", 0x1, Null]
    //     0xb80110: ldr             x4, [x4, #0x5b8]
    // 0xb80114: r0 = TimelineThemeData()
    //     0xb80114: bl              #0x9dffe8  ; [package:timelines_plus/src/timeline_theme.dart] TimelineThemeData::TimelineThemeData
    // 0xb80118: mov             x3, x0
    // 0xb8011c: ldur            x0, [fp, #-8]
    // 0xb80120: stur            x3, [fp, #-0x20]
    // 0xb80124: LoadField: r1 = r0->field_b
    //     0xb80124: ldur            w1, [x0, #0xb]
    // 0xb80128: DecompressPointer r1
    //     0xb80128: add             x1, x1, HEAP, lsl #32
    // 0xb8012c: cmp             w1, NULL
    // 0xb80130: b.eq            #0xb8056c
    // 0xb80134: LoadField: r0 = r1->field_b
    //     0xb80134: ldur            w0, [x1, #0xb]
    // 0xb80138: DecompressPointer r0
    //     0xb80138: add             x0, x0, HEAP, lsl #32
    // 0xb8013c: LoadField: r1 = r0->field_b
    //     0xb8013c: ldur            w1, [x0, #0xb]
    // 0xb80140: DecompressPointer r1
    //     0xb80140: add             x1, x1, HEAP, lsl #32
    // 0xb80144: cmp             w1, NULL
    // 0xb80148: b.ne            #0xb80154
    // 0xb8014c: r0 = Null
    //     0xb8014c: mov             x0, NULL
    // 0xb80150: b               #0xb80168
    // 0xb80154: LoadField: r0 = r1->field_f
    //     0xb80154: ldur            w0, [x1, #0xf]
    // 0xb80158: DecompressPointer r0
    //     0xb80158: add             x0, x0, HEAP, lsl #32
    // 0xb8015c: LoadField: r1 = r0->field_b
    //     0xb8015c: ldur            w1, [x0, #0xb]
    // 0xb80160: DecompressPointer r1
    //     0xb80160: add             x1, x1, HEAP, lsl #32
    // 0xb80164: LoadField: r0 = r1->field_b
    //     0xb80164: ldur            w0, [x1, #0xb]
    // 0xb80168: cmp             w0, NULL
    // 0xb8016c: b.ne            #0xb80178
    // 0xb80170: r6 = 0
    //     0xb80170: movz            x6, #0
    // 0xb80174: b               #0xb80180
    // 0xb80178: r1 = LoadInt32Instr(r0)
    //     0xb80178: sbfx            x1, x0, #1, #0x1f
    // 0xb8017c: mov             x6, x1
    // 0xb80180: ldur            x0, [fp, #-0x28]
    // 0xb80184: ldur            x2, [fp, #-0x18]
    // 0xb80188: stur            x6, [fp, #-0x50]
    // 0xb8018c: r1 = Function '<anonymous closure>':.
    //     0xb8018c: add             x1, PP, #0x55, lsl #12  ; [pp+0x558c0] AnonymousClosure: (0xa7971c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xb80190: ldr             x1, [x1, #0x8c0]
    // 0xb80194: r0 = AllocateClosure()
    //     0xb80194: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb80198: r1 = Function '<anonymous closure>':.
    //     0xb80198: add             x1, PP, #0x55, lsl #12  ; [pp+0x558c8] AnonymousClosure: (0xa79710), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xb8019c: ldr             x1, [x1, #0x8c8]
    // 0xb801a0: r2 = Null
    //     0xb801a0: mov             x2, NULL
    // 0xb801a4: stur            x0, [fp, #-8]
    // 0xb801a8: r0 = AllocateClosure()
    //     0xb801a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb801ac: r1 = Function '<anonymous closure>':.
    //     0xb801ac: add             x1, PP, #0x55, lsl #12  ; [pp+0x558d0] AnonymousClosure: (0xa796c4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xb801b0: ldr             x1, [x1, #0x8d0]
    // 0xb801b4: r2 = Null
    //     0xb801b4: mov             x2, NULL
    // 0xb801b8: stur            x0, [fp, #-0x18]
    // 0xb801bc: r0 = AllocateClosure()
    //     0xb801bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb801c0: mov             x2, x0
    // 0xb801c4: ldur            x3, [fp, #-8]
    // 0xb801c8: ldur            x5, [fp, #-0x18]
    // 0xb801cc: ldur            x6, [fp, #-0x50]
    // 0xb801d0: r1 = Null
    //     0xb801d0: mov             x1, NULL
    // 0xb801d4: r0 = TimelineTileBuilder.connected()
    //     0xb801d4: bl              #0x9dfab0  ; [package:timelines_plus/src/timeline_tile_builder.dart] TimelineTileBuilder::TimelineTileBuilder.connected
    // 0xb801d8: mov             x2, x0
    // 0xb801dc: ldur            x3, [fp, #-0x20]
    // 0xb801e0: r1 = Null
    //     0xb801e0: mov             x1, NULL
    // 0xb801e4: r0 = Timeline.tileBuilder()
    //     0xb801e4: bl              #0x9df894  ; [package:timelines_plus/src/timelines.dart] Timeline::Timeline.tileBuilder
    // 0xb801e8: ldur            x1, [fp, #-0x28]
    // 0xb801ec: ArrayStore: r1[7] = r0  ; List_4
    //     0xb801ec: add             x25, x1, #0x2b
    //     0xb801f0: str             w0, [x25]
    //     0xb801f4: tbz             w0, #0, #0xb80210
    //     0xb801f8: ldurb           w16, [x1, #-1]
    //     0xb801fc: ldurb           w17, [x0, #-1]
    //     0xb80200: and             x16, x17, x16, lsr #2
    //     0xb80204: tst             x16, HEAP, lsr #32
    //     0xb80208: b.eq            #0xb80210
    //     0xb8020c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80210: ldur            x1, [fp, #-0x28]
    // 0xb80214: r16 = Instance_SizedBox
    //     0xb80214: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb80218: ldr             x16, [x16, #0x8b8]
    // 0xb8021c: StoreField: r1->field_2f = r16
    //     0xb8021c: stur            w16, [x1, #0x2f]
    // 0xb80220: r0 = GetNavigation.size()
    //     0xb80220: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb80224: LoadField: d0 = r0->field_7
    //     0xb80224: ldur            d0, [x0, #7]
    // 0xb80228: stur            d0, [fp, #-0x58]
    // 0xb8022c: r16 = <EdgeInsets>
    //     0xb8022c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb80230: ldr             x16, [x16, #0xda0]
    // 0xb80234: r30 = Instance_EdgeInsets
    //     0xb80234: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb80238: ldr             lr, [lr, #0x1f0]
    // 0xb8023c: stp             lr, x16, [SP]
    // 0xb80240: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb80240: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb80244: r0 = all()
    //     0xb80244: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb80248: ldur            x1, [fp, #-0x10]
    // 0xb8024c: stur            x0, [fp, #-8]
    // 0xb80250: r0 = of()
    //     0xb80250: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb80254: LoadField: r1 = r0->field_5b
    //     0xb80254: ldur            w1, [x0, #0x5b]
    // 0xb80258: DecompressPointer r1
    //     0xb80258: add             x1, x1, HEAP, lsl #32
    // 0xb8025c: r16 = <Color>
    //     0xb8025c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb80260: ldr             x16, [x16, #0xf80]
    // 0xb80264: stp             x1, x16, [SP]
    // 0xb80268: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb80268: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb8026c: r0 = all()
    //     0xb8026c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb80270: stur            x0, [fp, #-0x18]
    // 0xb80274: r0 = Radius()
    //     0xb80274: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb80278: d0 = 30.000000
    //     0xb80278: fmov            d0, #30.00000000
    // 0xb8027c: stur            x0, [fp, #-0x20]
    // 0xb80280: StoreField: r0->field_7 = d0
    //     0xb80280: stur            d0, [x0, #7]
    // 0xb80284: StoreField: r0->field_f = d0
    //     0xb80284: stur            d0, [x0, #0xf]
    // 0xb80288: r0 = BorderRadius()
    //     0xb80288: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb8028c: mov             x1, x0
    // 0xb80290: ldur            x0, [fp, #-0x20]
    // 0xb80294: stur            x1, [fp, #-0x30]
    // 0xb80298: StoreField: r1->field_7 = r0
    //     0xb80298: stur            w0, [x1, #7]
    // 0xb8029c: StoreField: r1->field_b = r0
    //     0xb8029c: stur            w0, [x1, #0xb]
    // 0xb802a0: StoreField: r1->field_f = r0
    //     0xb802a0: stur            w0, [x1, #0xf]
    // 0xb802a4: StoreField: r1->field_13 = r0
    //     0xb802a4: stur            w0, [x1, #0x13]
    // 0xb802a8: r0 = RoundedRectangleBorder()
    //     0xb802a8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb802ac: mov             x1, x0
    // 0xb802b0: ldur            x0, [fp, #-0x30]
    // 0xb802b4: StoreField: r1->field_b = r0
    //     0xb802b4: stur            w0, [x1, #0xb]
    // 0xb802b8: r0 = Instance_BorderSide
    //     0xb802b8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb802bc: ldr             x0, [x0, #0xe20]
    // 0xb802c0: StoreField: r1->field_7 = r0
    //     0xb802c0: stur            w0, [x1, #7]
    // 0xb802c4: r16 = <RoundedRectangleBorder>
    //     0xb802c4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb802c8: ldr             x16, [x16, #0xf78]
    // 0xb802cc: stp             x1, x16, [SP]
    // 0xb802d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb802d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb802d4: r0 = all()
    //     0xb802d4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb802d8: stur            x0, [fp, #-0x20]
    // 0xb802dc: r0 = ButtonStyle()
    //     0xb802dc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb802e0: mov             x1, x0
    // 0xb802e4: ldur            x0, [fp, #-0x18]
    // 0xb802e8: stur            x1, [fp, #-0x30]
    // 0xb802ec: StoreField: r1->field_b = r0
    //     0xb802ec: stur            w0, [x1, #0xb]
    // 0xb802f0: ldur            x0, [fp, #-8]
    // 0xb802f4: StoreField: r1->field_23 = r0
    //     0xb802f4: stur            w0, [x1, #0x23]
    // 0xb802f8: ldur            x0, [fp, #-0x20]
    // 0xb802fc: StoreField: r1->field_43 = r0
    //     0xb802fc: stur            w0, [x1, #0x43]
    // 0xb80300: r0 = TextButtonThemeData()
    //     0xb80300: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb80304: mov             x2, x0
    // 0xb80308: ldur            x0, [fp, #-0x30]
    // 0xb8030c: stur            x2, [fp, #-8]
    // 0xb80310: StoreField: r2->field_7 = r0
    //     0xb80310: stur            w0, [x2, #7]
    // 0xb80314: ldur            x1, [fp, #-0x10]
    // 0xb80318: r0 = of()
    //     0xb80318: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8031c: LoadField: r1 = r0->field_87
    //     0xb8031c: ldur            w1, [x0, #0x87]
    // 0xb80320: DecompressPointer r1
    //     0xb80320: add             x1, x1, HEAP, lsl #32
    // 0xb80324: LoadField: r0 = r1->field_7
    //     0xb80324: ldur            w0, [x1, #7]
    // 0xb80328: DecompressPointer r0
    //     0xb80328: add             x0, x0, HEAP, lsl #32
    // 0xb8032c: r16 = 16.000000
    //     0xb8032c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb80330: ldr             x16, [x16, #0x188]
    // 0xb80334: r30 = Instance_Color
    //     0xb80334: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb80338: stp             lr, x16, [SP]
    // 0xb8033c: mov             x1, x0
    // 0xb80340: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb80340: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb80344: ldr             x4, [x4, #0xaa0]
    // 0xb80348: r0 = copyWith()
    //     0xb80348: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb8034c: stur            x0, [fp, #-0x10]
    // 0xb80350: r0 = Text()
    //     0xb80350: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb80354: mov             x3, x0
    // 0xb80358: r0 = "OKAY, GO BACK"
    //     0xb80358: add             x0, PP, #0x55, lsl #12  ; [pp+0x558d8] "OKAY, GO BACK"
    //     0xb8035c: ldr             x0, [x0, #0x8d8]
    // 0xb80360: stur            x3, [fp, #-0x18]
    // 0xb80364: StoreField: r3->field_b = r0
    //     0xb80364: stur            w0, [x3, #0xb]
    // 0xb80368: ldur            x0, [fp, #-0x10]
    // 0xb8036c: StoreField: r3->field_13 = r0
    //     0xb8036c: stur            w0, [x3, #0x13]
    // 0xb80370: r1 = Function '<anonymous closure>':.
    //     0xb80370: add             x1, PP, #0x55, lsl #12  ; [pp+0x558e0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb80374: ldr             x1, [x1, #0x8e0]
    // 0xb80378: r2 = Null
    //     0xb80378: mov             x2, NULL
    // 0xb8037c: r0 = AllocateClosure()
    //     0xb8037c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb80380: stur            x0, [fp, #-0x10]
    // 0xb80384: r0 = TextButton()
    //     0xb80384: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb80388: mov             x1, x0
    // 0xb8038c: ldur            x0, [fp, #-0x10]
    // 0xb80390: stur            x1, [fp, #-0x20]
    // 0xb80394: StoreField: r1->field_b = r0
    //     0xb80394: stur            w0, [x1, #0xb]
    // 0xb80398: r0 = false
    //     0xb80398: add             x0, NULL, #0x30  ; false
    // 0xb8039c: StoreField: r1->field_27 = r0
    //     0xb8039c: stur            w0, [x1, #0x27]
    // 0xb803a0: r2 = true
    //     0xb803a0: add             x2, NULL, #0x20  ; true
    // 0xb803a4: StoreField: r1->field_2f = r2
    //     0xb803a4: stur            w2, [x1, #0x2f]
    // 0xb803a8: ldur            x2, [fp, #-0x18]
    // 0xb803ac: StoreField: r1->field_37 = r2
    //     0xb803ac: stur            w2, [x1, #0x37]
    // 0xb803b0: r0 = TextButtonTheme()
    //     0xb803b0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb803b4: mov             x1, x0
    // 0xb803b8: ldur            x0, [fp, #-8]
    // 0xb803bc: stur            x1, [fp, #-0x10]
    // 0xb803c0: StoreField: r1->field_f = r0
    //     0xb803c0: stur            w0, [x1, #0xf]
    // 0xb803c4: ldur            x0, [fp, #-0x20]
    // 0xb803c8: StoreField: r1->field_b = r0
    //     0xb803c8: stur            w0, [x1, #0xb]
    // 0xb803cc: ldur            d0, [fp, #-0x58]
    // 0xb803d0: r0 = inline_Allocate_Double()
    //     0xb803d0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb803d4: add             x0, x0, #0x10
    //     0xb803d8: cmp             x2, x0
    //     0xb803dc: b.ls            #0xb80570
    //     0xb803e0: str             x0, [THR, #0x50]  ; THR::top
    //     0xb803e4: sub             x0, x0, #0xf
    //     0xb803e8: movz            x2, #0xe15c
    //     0xb803ec: movk            x2, #0x3, lsl #16
    //     0xb803f0: stur            x2, [x0, #-1]
    // 0xb803f4: StoreField: r0->field_7 = d0
    //     0xb803f4: stur            d0, [x0, #7]
    // 0xb803f8: stur            x0, [fp, #-8]
    // 0xb803fc: r0 = SizedBox()
    //     0xb803fc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb80400: mov             x1, x0
    // 0xb80404: ldur            x0, [fp, #-8]
    // 0xb80408: StoreField: r1->field_f = r0
    //     0xb80408: stur            w0, [x1, #0xf]
    // 0xb8040c: ldur            x0, [fp, #-0x10]
    // 0xb80410: StoreField: r1->field_b = r0
    //     0xb80410: stur            w0, [x1, #0xb]
    // 0xb80414: mov             x0, x1
    // 0xb80418: ldur            x1, [fp, #-0x28]
    // 0xb8041c: ArrayStore: r1[9] = r0  ; List_4
    //     0xb8041c: add             x25, x1, #0x33
    //     0xb80420: str             w0, [x25]
    //     0xb80424: tbz             w0, #0, #0xb80440
    //     0xb80428: ldurb           w16, [x1, #-1]
    //     0xb8042c: ldurb           w17, [x0, #-1]
    //     0xb80430: and             x16, x17, x16, lsr #2
    //     0xb80434: tst             x16, HEAP, lsr #32
    //     0xb80438: b.eq            #0xb80440
    //     0xb8043c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80440: r1 = <Widget>
    //     0xb80440: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb80444: r0 = AllocateGrowableArray()
    //     0xb80444: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb80448: mov             x1, x0
    // 0xb8044c: ldur            x0, [fp, #-0x28]
    // 0xb80450: stur            x1, [fp, #-8]
    // 0xb80454: StoreField: r1->field_f = r0
    //     0xb80454: stur            w0, [x1, #0xf]
    // 0xb80458: r0 = 20
    //     0xb80458: movz            x0, #0x14
    // 0xb8045c: StoreField: r1->field_b = r0
    //     0xb8045c: stur            w0, [x1, #0xb]
    // 0xb80460: r0 = Column()
    //     0xb80460: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb80464: mov             x1, x0
    // 0xb80468: r0 = Instance_Axis
    //     0xb80468: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb8046c: stur            x1, [fp, #-0x10]
    // 0xb80470: StoreField: r1->field_f = r0
    //     0xb80470: stur            w0, [x1, #0xf]
    // 0xb80474: r2 = Instance_MainAxisAlignment
    //     0xb80474: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb80478: ldr             x2, [x2, #0xa08]
    // 0xb8047c: StoreField: r1->field_13 = r2
    //     0xb8047c: stur            w2, [x1, #0x13]
    // 0xb80480: r2 = Instance_MainAxisSize
    //     0xb80480: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb80484: ldr             x2, [x2, #0xa10]
    // 0xb80488: ArrayStore: r1[0] = r2  ; List_4
    //     0xb80488: stur            w2, [x1, #0x17]
    // 0xb8048c: r2 = Instance_CrossAxisAlignment
    //     0xb8048c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb80490: ldr             x2, [x2, #0x890]
    // 0xb80494: StoreField: r1->field_1b = r2
    //     0xb80494: stur            w2, [x1, #0x1b]
    // 0xb80498: r2 = Instance_VerticalDirection
    //     0xb80498: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb8049c: ldr             x2, [x2, #0xa20]
    // 0xb804a0: StoreField: r1->field_23 = r2
    //     0xb804a0: stur            w2, [x1, #0x23]
    // 0xb804a4: r2 = Instance_Clip
    //     0xb804a4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb804a8: ldr             x2, [x2, #0x38]
    // 0xb804ac: StoreField: r1->field_2b = r2
    //     0xb804ac: stur            w2, [x1, #0x2b]
    // 0xb804b0: StoreField: r1->field_2f = rZR
    //     0xb804b0: stur            xzr, [x1, #0x2f]
    // 0xb804b4: ldur            x2, [fp, #-8]
    // 0xb804b8: StoreField: r1->field_b = r2
    //     0xb804b8: stur            w2, [x1, #0xb]
    // 0xb804bc: r0 = SingleChildScrollView()
    //     0xb804bc: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb804c0: mov             x1, x0
    // 0xb804c4: r0 = Instance_Axis
    //     0xb804c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb804c8: stur            x1, [fp, #-8]
    // 0xb804cc: StoreField: r1->field_b = r0
    //     0xb804cc: stur            w0, [x1, #0xb]
    // 0xb804d0: r0 = false
    //     0xb804d0: add             x0, NULL, #0x30  ; false
    // 0xb804d4: StoreField: r1->field_f = r0
    //     0xb804d4: stur            w0, [x1, #0xf]
    // 0xb804d8: r0 = Instance_EdgeInsets
    //     0xb804d8: add             x0, PP, #0x55, lsl #12  ; [pp+0x558e8] Obj!EdgeInsets@d58fa1
    //     0xb804dc: ldr             x0, [x0, #0x8e8]
    // 0xb804e0: StoreField: r1->field_13 = r0
    //     0xb804e0: stur            w0, [x1, #0x13]
    // 0xb804e4: ldur            x0, [fp, #-0x10]
    // 0xb804e8: StoreField: r1->field_23 = r0
    //     0xb804e8: stur            w0, [x1, #0x23]
    // 0xb804ec: r0 = Instance_DragStartBehavior
    //     0xb804ec: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb804f0: StoreField: r1->field_27 = r0
    //     0xb804f0: stur            w0, [x1, #0x27]
    // 0xb804f4: r0 = Instance_Clip
    //     0xb804f4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb804f8: ldr             x0, [x0, #0x7e0]
    // 0xb804fc: StoreField: r1->field_2b = r0
    //     0xb804fc: stur            w0, [x1, #0x2b]
    // 0xb80500: r0 = Instance_HitTestBehavior
    //     0xb80500: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb80504: ldr             x0, [x0, #0x288]
    // 0xb80508: StoreField: r1->field_2f = r0
    //     0xb80508: stur            w0, [x1, #0x2f]
    // 0xb8050c: r0 = ColoredBox()
    //     0xb8050c: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb80510: r1 = Instance_Color
    //     0xb80510: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb80514: ldr             x1, [x1, #0x90]
    // 0xb80518: StoreField: r0->field_f = r1
    //     0xb80518: stur            w1, [x0, #0xf]
    // 0xb8051c: ldur            x1, [fp, #-8]
    // 0xb80520: StoreField: r0->field_b = r1
    //     0xb80520: stur            w1, [x0, #0xb]
    // 0xb80524: LeaveFrame
    //     0xb80524: mov             SP, fp
    //     0xb80528: ldp             fp, lr, [SP], #0x10
    // 0xb8052c: ret
    //     0xb8052c: ret             
    // 0xb80530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb80530: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb80534: b               #0xb7f478
    // 0xb80538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80538: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8053c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8053c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80540: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8054c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8054c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80550: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80550: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80554: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb80554: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb80558: SaveReg d0
    //     0xb80558: str             q0, [SP, #-0x10]!
    // 0xb8055c: r0 = AllocateDouble()
    //     0xb8055c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb80560: RestoreReg d0
    //     0xb80560: ldr             q0, [SP], #0x10
    // 0xb80564: b               #0xb7ffa4
    // 0xb80568: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80568: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb8056c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8056c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80570: SaveReg d0
    //     0xb80570: str             q0, [SP, #-0x10]!
    // 0xb80574: SaveReg r1
    //     0xb80574: str             x1, [SP, #-8]!
    // 0xb80578: r0 = AllocateDouble()
    //     0xb80578: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb8057c: RestoreReg r1
    //     0xb8057c: ldr             x1, [SP], #8
    // 0xb80580: RestoreReg d0
    //     0xb80580: ldr             q0, [SP], #0x10
    // 0xb80584: b               #0xb803f4
  }
}

// class id: 4061, size: 0x10, field offset: 0xc
//   const constructor, 
class KnowMoreBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f754, size: 0x24
    // 0xc7f754: EnterFrame
    //     0xc7f754: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f758: mov             fp, SP
    // 0xc7f75c: mov             x0, x1
    // 0xc7f760: r1 = <KnowMoreBottomSheet>
    //     0xc7f760: add             x1, PP, #0x48, lsl #12  ; [pp+0x487a8] TypeArguments: <KnowMoreBottomSheet>
    //     0xc7f764: ldr             x1, [x1, #0x7a8]
    // 0xc7f768: r0 = _KnowMoreBottomSheetState()
    //     0xc7f768: bl              #0xc7f778  ; Allocate_KnowMoreBottomSheetStateStub -> _KnowMoreBottomSheetState (size=0x14)
    // 0xc7f76c: LeaveFrame
    //     0xc7f76c: mov             SP, fp
    //     0xc7f770: ldp             fp, lr, [SP], #0x10
    // 0xc7f774: ret
    //     0xc7f774: ret             
  }
}
