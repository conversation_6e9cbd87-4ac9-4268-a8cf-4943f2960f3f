// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/share_to_whatsapp.dart

// class id: 1049571, size: 0x8
class :: {
}

// class id: 3214, size: 0x14, field offset: 0x14
class _ShareToWhatsAppState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc0cb54, size: 0xbc
    // 0xc0cb54: EnterFrame
    //     0xc0cb54: stp             fp, lr, [SP, #-0x10]!
    //     0xc0cb58: mov             fp, SP
    // 0xc0cb5c: AllocStack(0x18)
    //     0xc0cb5c: sub             SP, SP, #0x18
    // 0xc0cb60: SetupParameters(_ShareToWhatsAppState this /* r1 => r1, fp-0x8 */)
    //     0xc0cb60: stur            x1, [fp, #-8]
    // 0xc0cb64: CheckStackOverflow
    //     0xc0cb64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0cb68: cmp             SP, x16
    //     0xc0cb6c: b.ls            #0xc0cc08
    // 0xc0cb70: r1 = 1
    //     0xc0cb70: movz            x1, #0x1
    // 0xc0cb74: r0 = AllocateContext()
    //     0xc0cb74: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0cb78: mov             x1, x0
    // 0xc0cb7c: ldur            x0, [fp, #-8]
    // 0xc0cb80: stur            x1, [fp, #-0x10]
    // 0xc0cb84: StoreField: r1->field_f = r0
    //     0xc0cb84: stur            w0, [x1, #0xf]
    // 0xc0cb88: r0 = SvgPicture()
    //     0xc0cb88: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc0cb8c: mov             x1, x0
    // 0xc0cb90: r2 = "assets/images/share.svg"
    //     0xc0cb90: add             x2, PP, #0x71, lsl #12  ; [pp+0x71600] "assets/images/share.svg"
    //     0xc0cb94: ldr             x2, [x2, #0x600]
    // 0xc0cb98: stur            x0, [fp, #-8]
    // 0xc0cb9c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0cb9c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0cba0: r0 = SvgPicture.asset()
    //     0xc0cba0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc0cba4: r0 = InkWell()
    //     0xc0cba4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc0cba8: mov             x3, x0
    // 0xc0cbac: ldur            x0, [fp, #-8]
    // 0xc0cbb0: stur            x3, [fp, #-0x18]
    // 0xc0cbb4: StoreField: r3->field_b = r0
    //     0xc0cbb4: stur            w0, [x3, #0xb]
    // 0xc0cbb8: ldur            x2, [fp, #-0x10]
    // 0xc0cbbc: r1 = Function '<anonymous closure>':.
    //     0xc0cbbc: add             x1, PP, #0x71, lsl #12  ; [pp+0x71608] AnonymousClosure: (0xc0cc10), in [package:customer_app/app/presentation/views/line/product_detail/widgets/share_to_whatsapp.dart] _ShareToWhatsAppState::build (0xc0cb54)
    //     0xc0cbc0: ldr             x1, [x1, #0x608]
    // 0xc0cbc4: r0 = AllocateClosure()
    //     0xc0cbc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0cbc8: mov             x1, x0
    // 0xc0cbcc: ldur            x0, [fp, #-0x18]
    // 0xc0cbd0: StoreField: r0->field_f = r1
    //     0xc0cbd0: stur            w1, [x0, #0xf]
    // 0xc0cbd4: r1 = true
    //     0xc0cbd4: add             x1, NULL, #0x20  ; true
    // 0xc0cbd8: StoreField: r0->field_43 = r1
    //     0xc0cbd8: stur            w1, [x0, #0x43]
    // 0xc0cbdc: r2 = Instance_BoxShape
    //     0xc0cbdc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0cbe0: ldr             x2, [x2, #0x80]
    // 0xc0cbe4: StoreField: r0->field_47 = r2
    //     0xc0cbe4: stur            w2, [x0, #0x47]
    // 0xc0cbe8: StoreField: r0->field_6f = r1
    //     0xc0cbe8: stur            w1, [x0, #0x6f]
    // 0xc0cbec: r2 = false
    //     0xc0cbec: add             x2, NULL, #0x30  ; false
    // 0xc0cbf0: StoreField: r0->field_73 = r2
    //     0xc0cbf0: stur            w2, [x0, #0x73]
    // 0xc0cbf4: StoreField: r0->field_83 = r1
    //     0xc0cbf4: stur            w1, [x0, #0x83]
    // 0xc0cbf8: StoreField: r0->field_7b = r2
    //     0xc0cbf8: stur            w2, [x0, #0x7b]
    // 0xc0cbfc: LeaveFrame
    //     0xc0cbfc: mov             SP, fp
    //     0xc0cc00: ldp             fp, lr, [SP], #0x10
    // 0xc0cc04: ret
    //     0xc0cc04: ret             
    // 0xc0cc08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0cc08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0cc0c: b               #0xc0cb70
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0cc10, size: 0x74
    // 0xc0cc10: EnterFrame
    //     0xc0cc10: stp             fp, lr, [SP, #-0x10]!
    //     0xc0cc14: mov             fp, SP
    // 0xc0cc18: ldr             x0, [fp, #0x10]
    // 0xc0cc1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc0cc1c: ldur            w1, [x0, #0x17]
    // 0xc0cc20: DecompressPointer r1
    //     0xc0cc20: add             x1, x1, HEAP, lsl #32
    // 0xc0cc24: CheckStackOverflow
    //     0xc0cc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0cc28: cmp             SP, x16
    //     0xc0cc2c: b.ls            #0xc0cc78
    // 0xc0cc30: LoadField: r0 = r1->field_f
    //     0xc0cc30: ldur            w0, [x1, #0xf]
    // 0xc0cc34: DecompressPointer r0
    //     0xc0cc34: add             x0, x0, HEAP, lsl #32
    // 0xc0cc38: LoadField: r1 = r0->field_b
    //     0xc0cc38: ldur            w1, [x0, #0xb]
    // 0xc0cc3c: DecompressPointer r1
    //     0xc0cc3c: add             x1, x1, HEAP, lsl #32
    // 0xc0cc40: cmp             w1, NULL
    // 0xc0cc44: b.eq            #0xc0cc80
    // 0xc0cc48: LoadField: r0 = r1->field_b
    //     0xc0cc48: ldur            w0, [x1, #0xb]
    // 0xc0cc4c: DecompressPointer r0
    //     0xc0cc4c: add             x0, x0, HEAP, lsl #32
    // 0xc0cc50: LoadField: r1 = r0->field_53
    //     0xc0cc50: ldur            w1, [x0, #0x53]
    // 0xc0cc54: DecompressPointer r1
    //     0xc0cc54: add             x1, x1, HEAP, lsl #32
    // 0xc0cc58: cmp             w1, NULL
    // 0xc0cc5c: b.ne            #0xc0cc64
    // 0xc0cc60: r1 = ""
    //     0xc0cc60: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0cc64: r0 = openWhatsApp()
    //     0xc0cc64: bl              #0xa8e820  ; [package:customer_app/app/core/utils/utils.dart] Utils::openWhatsApp
    // 0xc0cc68: r0 = Null
    //     0xc0cc68: mov             x0, NULL
    // 0xc0cc6c: LeaveFrame
    //     0xc0cc6c: mov             SP, fp
    //     0xc0cc70: ldp             fp, lr, [SP], #0x10
    // 0xc0cc74: ret
    //     0xc0cc74: ret             
    // 0xc0cc78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0cc78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0cc7c: b               #0xc0cc30
    // 0xc0cc80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0cc80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3962, size: 0x10, field offset: 0xc
//   const constructor, 
class ShareToWhatsApp extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc812a8, size: 0x24
    // 0xc812a8: EnterFrame
    //     0xc812a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc812ac: mov             fp, SP
    // 0xc812b0: mov             x0, x1
    // 0xc812b4: r1 = <ShareToWhatsApp>
    //     0xc812b4: add             x1, PP, #0x6e, lsl #12  ; [pp+0x6eca8] TypeArguments: <ShareToWhatsApp>
    //     0xc812b8: ldr             x1, [x1, #0xca8]
    // 0xc812bc: r0 = _ShareToWhatsAppState()
    //     0xc812bc: bl              #0xc812cc  ; Allocate_ShareToWhatsAppStateStub -> _ShareToWhatsAppState (size=0x14)
    // 0xc812c0: LeaveFrame
    //     0xc812c0: mov             SP, fp
    //     0xc812c4: ldp             fp, lr, [SP], #0x10
    // 0xc812c8: ret
    //     0xc812c8: ret             
  }
}
