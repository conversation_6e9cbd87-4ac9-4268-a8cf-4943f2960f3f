// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart

// class id: 1049403, size: 0x8
class :: {
}

// class id: 3335, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ProductGridItemViewState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ activate(/* No info */) {
    // ** addr: 0x7f50f4, size: 0x30
    // 0x7f50f4: EnterFrame
    //     0x7f50f4: stp             fp, lr, [SP, #-0x10]!
    //     0x7f50f8: mov             fp, SP
    // 0x7f50fc: CheckStackOverflow
    //     0x7f50fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f5100: cmp             SP, x16
    //     0x7f5104: b.ls            #0x7f511c
    // 0x7f5108: r0 = _updateTickerModeNotifier()
    //     0x7f5108: bl              #0x7f5144  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] __ProductGridItemViewState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f510c: r0 = Null
    //     0x7f510c: mov             x0, NULL
    // 0x7f5110: LeaveFrame
    //     0x7f5110: mov             SP, fp
    //     0x7f5114: ldp             fp, lr, [SP], #0x10
    // 0x7f5118: ret
    //     0x7f5118: ret             
    // 0x7f511c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f511c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5120: b               #0x7f5108
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x7f5144, size: 0x124
    // 0x7f5144: EnterFrame
    //     0x7f5144: stp             fp, lr, [SP, #-0x10]!
    //     0x7f5148: mov             fp, SP
    // 0x7f514c: AllocStack(0x18)
    //     0x7f514c: sub             SP, SP, #0x18
    // 0x7f5150: SetupParameters(__ProductGridItemViewState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x7f5150: mov             x2, x1
    //     0x7f5154: stur            x1, [fp, #-8]
    // 0x7f5158: CheckStackOverflow
    //     0x7f5158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f515c: cmp             SP, x16
    //     0x7f5160: b.ls            #0x7f525c
    // 0x7f5164: LoadField: r1 = r2->field_f
    //     0x7f5164: ldur            w1, [x2, #0xf]
    // 0x7f5168: DecompressPointer r1
    //     0x7f5168: add             x1, x1, HEAP, lsl #32
    // 0x7f516c: cmp             w1, NULL
    // 0x7f5170: b.eq            #0x7f5264
    // 0x7f5174: r0 = getNotifier()
    //     0x7f5174: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x7f5178: mov             x3, x0
    // 0x7f517c: ldur            x0, [fp, #-8]
    // 0x7f5180: stur            x3, [fp, #-0x18]
    // 0x7f5184: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x7f5184: ldur            w4, [x0, #0x17]
    // 0x7f5188: DecompressPointer r4
    //     0x7f5188: add             x4, x4, HEAP, lsl #32
    // 0x7f518c: stur            x4, [fp, #-0x10]
    // 0x7f5190: cmp             w3, w4
    // 0x7f5194: b.ne            #0x7f51a8
    // 0x7f5198: r0 = Null
    //     0x7f5198: mov             x0, NULL
    // 0x7f519c: LeaveFrame
    //     0x7f519c: mov             SP, fp
    //     0x7f51a0: ldp             fp, lr, [SP], #0x10
    // 0x7f51a4: ret
    //     0x7f51a4: ret             
    // 0x7f51a8: cmp             w4, NULL
    // 0x7f51ac: b.eq            #0x7f51f0
    // 0x7f51b0: mov             x2, x0
    // 0x7f51b4: r1 = Function '_updateTicker@356311458':.
    //     0x7f51b4: add             x1, PP, #0x56, lsl #12  ; [pp+0x562b8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f51b8: ldr             x1, [x1, #0x2b8]
    // 0x7f51bc: r0 = AllocateClosure()
    //     0x7f51bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f51c0: ldur            x1, [fp, #-0x10]
    // 0x7f51c4: r2 = LoadClassIdInstr(r1)
    //     0x7f51c4: ldur            x2, [x1, #-1]
    //     0x7f51c8: ubfx            x2, x2, #0xc, #0x14
    // 0x7f51cc: mov             x16, x0
    // 0x7f51d0: mov             x0, x2
    // 0x7f51d4: mov             x2, x16
    // 0x7f51d8: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x7f51d8: movz            x17, #0xdc2b
    //     0x7f51dc: add             lr, x0, x17
    //     0x7f51e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7f51e4: blr             lr
    // 0x7f51e8: ldur            x0, [fp, #-8]
    // 0x7f51ec: ldur            x3, [fp, #-0x18]
    // 0x7f51f0: mov             x2, x0
    // 0x7f51f4: r1 = Function '_updateTicker@356311458':.
    //     0x7f51f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x562b8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f51f8: ldr             x1, [x1, #0x2b8]
    // 0x7f51fc: r0 = AllocateClosure()
    //     0x7f51fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f5200: ldur            x3, [fp, #-0x18]
    // 0x7f5204: r1 = LoadClassIdInstr(r3)
    //     0x7f5204: ldur            x1, [x3, #-1]
    //     0x7f5208: ubfx            x1, x1, #0xc, #0x14
    // 0x7f520c: mov             x2, x0
    // 0x7f5210: mov             x0, x1
    // 0x7f5214: mov             x1, x3
    // 0x7f5218: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x7f5218: movz            x17, #0xdc71
    //     0x7f521c: add             lr, x0, x17
    //     0x7f5220: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5224: blr             lr
    // 0x7f5228: ldur            x0, [fp, #-0x18]
    // 0x7f522c: ldur            x1, [fp, #-8]
    // 0x7f5230: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f5230: stur            w0, [x1, #0x17]
    //     0x7f5234: ldurb           w16, [x1, #-1]
    //     0x7f5238: ldurb           w17, [x0, #-1]
    //     0x7f523c: and             x16, x17, x16, lsr #2
    //     0x7f5240: tst             x16, HEAP, lsr #32
    //     0x7f5244: b.eq            #0x7f524c
    //     0x7f5248: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x7f524c: r0 = Null
    //     0x7f524c: mov             x0, NULL
    // 0x7f5250: LeaveFrame
    //     0x7f5250: mov             SP, fp
    //     0x7f5254: ldp             fp, lr, [SP], #0x10
    // 0x7f5258: ret
    //     0x7f5258: ret             
    // 0x7f525c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f525c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5260: b               #0x7f5164
    // 0x7f5264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f5264: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87a30, size: 0x94
    // 0xc87a30: EnterFrame
    //     0xc87a30: stp             fp, lr, [SP, #-0x10]!
    //     0xc87a34: mov             fp, SP
    // 0xc87a38: AllocStack(0x10)
    //     0xc87a38: sub             SP, SP, #0x10
    // 0xc87a3c: SetupParameters(__ProductGridItemViewState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc87a3c: mov             x0, x1
    //     0xc87a40: stur            x1, [fp, #-0x10]
    // 0xc87a44: CheckStackOverflow
    //     0xc87a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87a48: cmp             SP, x16
    //     0xc87a4c: b.ls            #0xc87abc
    // 0xc87a50: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc87a50: ldur            w3, [x0, #0x17]
    // 0xc87a54: DecompressPointer r3
    //     0xc87a54: add             x3, x3, HEAP, lsl #32
    // 0xc87a58: stur            x3, [fp, #-8]
    // 0xc87a5c: cmp             w3, NULL
    // 0xc87a60: b.ne            #0xc87a6c
    // 0xc87a64: mov             x1, x0
    // 0xc87a68: b               #0xc87aa8
    // 0xc87a6c: mov             x2, x0
    // 0xc87a70: r1 = Function '_updateTicker@356311458':.
    //     0xc87a70: add             x1, PP, #0x56, lsl #12  ; [pp+0x562b8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc87a74: ldr             x1, [x1, #0x2b8]
    // 0xc87a78: r0 = AllocateClosure()
    //     0xc87a78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc87a7c: ldur            x1, [fp, #-8]
    // 0xc87a80: r2 = LoadClassIdInstr(r1)
    //     0xc87a80: ldur            x2, [x1, #-1]
    //     0xc87a84: ubfx            x2, x2, #0xc, #0x14
    // 0xc87a88: mov             x16, x0
    // 0xc87a8c: mov             x0, x2
    // 0xc87a90: mov             x2, x16
    // 0xc87a94: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc87a94: movz            x17, #0xdc2b
    //     0xc87a98: add             lr, x0, x17
    //     0xc87a9c: ldr             lr, [x21, lr, lsl #3]
    //     0xc87aa0: blr             lr
    // 0xc87aa4: ldur            x1, [fp, #-0x10]
    // 0xc87aa8: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc87aa8: stur            NULL, [x1, #0x17]
    // 0xc87aac: r0 = Null
    //     0xc87aac: mov             x0, NULL
    // 0xc87ab0: LeaveFrame
    //     0xc87ab0: mov             SP, fp
    //     0xc87ab4: ldp             fp, lr, [SP], #0x10
    // 0xc87ab8: ret
    //     0xc87ab8: ret             
    // 0xc87abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87abc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87ac0: b               #0xc87a50
  }
}

// class id: 3336, size: 0x28, field offset: 0x1c
class _ProductGridItemViewState extends __ProductGridItemViewState&State&SingleTickerProviderStateMixin {

  late PageController _pageController; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x941f70, size: 0x80
    // 0x941f70: EnterFrame
    //     0x941f70: stp             fp, lr, [SP, #-0x10]!
    //     0x941f74: mov             fp, SP
    // 0x941f78: AllocStack(0x10)
    //     0x941f78: sub             SP, SP, #0x10
    // 0x941f7c: SetupParameters(_ProductGridItemViewState this /* r1 => r1, fp-0x8 */)
    //     0x941f7c: stur            x1, [fp, #-8]
    // 0x941f80: CheckStackOverflow
    //     0x941f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941f84: cmp             SP, x16
    //     0x941f88: b.ls            #0x941fe8
    // 0x941f8c: r0 = PageController()
    //     0x941f8c: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x941f90: stur            x0, [fp, #-0x10]
    // 0x941f94: StoreField: r0->field_3f = rZR
    //     0x941f94: stur            xzr, [x0, #0x3f]
    // 0x941f98: r1 = true
    //     0x941f98: add             x1, NULL, #0x20  ; true
    // 0x941f9c: StoreField: r0->field_47 = r1
    //     0x941f9c: stur            w1, [x0, #0x47]
    // 0x941fa0: d0 = 1.000000
    //     0x941fa0: fmov            d0, #1.00000000
    // 0x941fa4: StoreField: r0->field_4b = d0
    //     0x941fa4: stur            d0, [x0, #0x4b]
    // 0x941fa8: mov             x1, x0
    // 0x941fac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x941fac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x941fb0: r0 = ScrollController()
    //     0x941fb0: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x941fb4: ldur            x0, [fp, #-0x10]
    // 0x941fb8: ldur            x1, [fp, #-8]
    // 0x941fbc: StoreField: r1->field_1b = r0
    //     0x941fbc: stur            w0, [x1, #0x1b]
    //     0x941fc0: ldurb           w16, [x1, #-1]
    //     0x941fc4: ldurb           w17, [x0, #-1]
    //     0x941fc8: and             x16, x17, x16, lsr #2
    //     0x941fcc: tst             x16, HEAP, lsr #32
    //     0x941fd0: b.eq            #0x941fd8
    //     0x941fd4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x941fd8: r0 = Null
    //     0x941fd8: mov             x0, NULL
    // 0x941fdc: LeaveFrame
    //     0x941fdc: mov             SP, fp
    //     0x941fe0: ldp             fp, lr, [SP], #0x10
    // 0x941fe4: ret
    //     0x941fe4: ret             
    // 0x941fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941fe8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941fec: b               #0x941f8c
  }
  _ build(/* No info */) {
    // ** addr: 0xb62208, size: 0x122c
    // 0xb62208: EnterFrame
    //     0xb62208: stp             fp, lr, [SP, #-0x10]!
    //     0xb6220c: mov             fp, SP
    // 0xb62210: AllocStack(0xa8)
    //     0xb62210: sub             SP, SP, #0xa8
    // 0xb62214: SetupParameters(_ProductGridItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb62214: mov             x0, x1
    //     0xb62218: stur            x1, [fp, #-8]
    //     0xb6221c: mov             x1, x2
    //     0xb62220: stur            x2, [fp, #-0x10]
    // 0xb62224: CheckStackOverflow
    //     0xb62224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb62228: cmp             SP, x16
    //     0xb6222c: b.ls            #0xb63408
    // 0xb62230: r1 = 1
    //     0xb62230: movz            x1, #0x1
    // 0xb62234: r0 = AllocateContext()
    //     0xb62234: bl              #0x16f6108  ; AllocateContextStub
    // 0xb62238: mov             x1, x0
    // 0xb6223c: ldur            x0, [fp, #-8]
    // 0xb62240: stur            x1, [fp, #-0x20]
    // 0xb62244: StoreField: r1->field_f = r0
    //     0xb62244: stur            w0, [x1, #0xf]
    // 0xb62248: LoadField: r2 = r0->field_b
    //     0xb62248: ldur            w2, [x0, #0xb]
    // 0xb6224c: DecompressPointer r2
    //     0xb6224c: add             x2, x2, HEAP, lsl #32
    // 0xb62250: stur            x2, [fp, #-0x18]
    // 0xb62254: cmp             w2, NULL
    // 0xb62258: b.eq            #0xb63410
    // 0xb6225c: LoadField: r3 = r2->field_f
    //     0xb6225c: ldur            w3, [x2, #0xf]
    // 0xb62260: DecompressPointer r3
    //     0xb62260: add             x3, x3, HEAP, lsl #32
    // 0xb62264: cmp             w3, NULL
    // 0xb62268: b.ne            #0xb62274
    // 0xb6226c: r3 = Null
    //     0xb6226c: mov             x3, NULL
    // 0xb62270: b               #0xb62288
    // 0xb62274: LoadField: r4 = r3->field_7
    //     0xb62274: ldur            w4, [x3, #7]
    // 0xb62278: cbz             w4, #0xb62284
    // 0xb6227c: r3 = false
    //     0xb6227c: add             x3, NULL, #0x30  ; false
    // 0xb62280: b               #0xb62288
    // 0xb62284: r3 = true
    //     0xb62284: add             x3, NULL, #0x20  ; true
    // 0xb62288: cmp             w3, NULL
    // 0xb6228c: b.eq            #0xb622f0
    // 0xb62290: tbnz            w3, #4, #0xb622f0
    // 0xb62294: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb62294: ldur            w3, [x2, #0x17]
    // 0xb62298: DecompressPointer r3
    //     0xb62298: add             x3, x3, HEAP, lsl #32
    // 0xb6229c: cmp             w3, NULL
    // 0xb622a0: b.ne            #0xb622ac
    // 0xb622a4: r3 = Null
    //     0xb622a4: mov             x3, NULL
    // 0xb622a8: b               #0xb622dc
    // 0xb622ac: LoadField: r4 = r3->field_7
    //     0xb622ac: ldur            w4, [x3, #7]
    // 0xb622b0: DecompressPointer r4
    //     0xb622b0: add             x4, x4, HEAP, lsl #32
    // 0xb622b4: cmp             w4, NULL
    // 0xb622b8: b.ne            #0xb622c4
    // 0xb622bc: r3 = Null
    //     0xb622bc: mov             x3, NULL
    // 0xb622c0: b               #0xb622dc
    // 0xb622c4: LoadField: r3 = r4->field_7
    //     0xb622c4: ldur            w3, [x4, #7]
    // 0xb622c8: cbnz            w3, #0xb622d4
    // 0xb622cc: r4 = false
    //     0xb622cc: add             x4, NULL, #0x30  ; false
    // 0xb622d0: b               #0xb622d8
    // 0xb622d4: r4 = true
    //     0xb622d4: add             x4, NULL, #0x20  ; true
    // 0xb622d8: mov             x3, x4
    // 0xb622dc: cmp             w3, NULL
    // 0xb622e0: b.eq            #0xb622f0
    // 0xb622e4: tbnz            w3, #4, #0xb622f0
    // 0xb622e8: d0 = 12.000000
    //     0xb622e8: fmov            d0, #12.00000000
    // 0xb622ec: b               #0xb622f4
    // 0xb622f0: d0 = 0.000000
    //     0xb622f0: eor             v0.16b, v0.16b, v0.16b
    // 0xb622f4: stur            d0, [fp, #-0x88]
    // 0xb622f8: r0 = EdgeInsets()
    //     0xb622f8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb622fc: d0 = 12.000000
    //     0xb622fc: fmov            d0, #12.00000000
    // 0xb62300: stur            x0, [fp, #-0x30]
    // 0xb62304: StoreField: r0->field_7 = d0
    //     0xb62304: stur            d0, [x0, #7]
    // 0xb62308: ldur            d1, [fp, #-0x88]
    // 0xb6230c: StoreField: r0->field_f = d1
    //     0xb6230c: stur            d1, [x0, #0xf]
    // 0xb62310: ArrayStore: r0[0] = d0  ; List_8
    //     0xb62310: stur            d0, [x0, #0x17]
    // 0xb62314: d0 = 48.000000
    //     0xb62314: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0xb62318: StoreField: r0->field_1f = d0
    //     0xb62318: stur            d0, [x0, #0x1f]
    // 0xb6231c: ldur            x1, [fp, #-0x18]
    // 0xb62320: LoadField: r2 = r1->field_1b
    //     0xb62320: ldur            w2, [x1, #0x1b]
    // 0xb62324: DecompressPointer r2
    //     0xb62324: add             x2, x2, HEAP, lsl #32
    // 0xb62328: tbnz            w2, #4, #0xb62338
    // 0xb6232c: r3 = Instance_MainAxisAlignment
    //     0xb6232c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb62330: ldr             x3, [x3, #0xa08]
    // 0xb62334: b               #0xb62340
    // 0xb62338: r3 = Instance_MainAxisAlignment
    //     0xb62338: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb6233c: ldr             x3, [x3, #0xab0]
    // 0xb62340: stur            x3, [fp, #-0x28]
    // 0xb62344: LoadField: r2 = r1->field_23
    //     0xb62344: ldur            w2, [x1, #0x23]
    // 0xb62348: DecompressPointer r2
    //     0xb62348: add             x2, x2, HEAP, lsl #32
    // 0xb6234c: LoadField: r1 = r2->field_7
    //     0xb6234c: ldur            w1, [x2, #7]
    // 0xb62350: DecompressPointer r1
    //     0xb62350: add             x1, x1, HEAP, lsl #32
    // 0xb62354: cmp             w1, NULL
    // 0xb62358: b.ne            #0xb62364
    // 0xb6235c: r1 = Instance_TitleAlignment
    //     0xb6235c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb62360: ldr             x1, [x1, #0x518]
    // 0xb62364: r16 = Instance_TitleAlignment
    //     0xb62364: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb62368: ldr             x16, [x16, #0x520]
    // 0xb6236c: cmp             w1, w16
    // 0xb62370: b.ne            #0xb62380
    // 0xb62374: r5 = Instance_CrossAxisAlignment
    //     0xb62374: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb62378: ldr             x5, [x5, #0xc68]
    // 0xb6237c: b               #0xb623a4
    // 0xb62380: r16 = Instance_TitleAlignment
    //     0xb62380: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb62384: ldr             x16, [x16, #0x518]
    // 0xb62388: cmp             w1, w16
    // 0xb6238c: b.ne            #0xb6239c
    // 0xb62390: r5 = Instance_CrossAxisAlignment
    //     0xb62390: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb62394: ldr             x5, [x5, #0x890]
    // 0xb62398: b               #0xb623a4
    // 0xb6239c: r5 = Instance_CrossAxisAlignment
    //     0xb6239c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb623a0: ldr             x5, [x5, #0xa18]
    // 0xb623a4: ldur            x4, [fp, #-8]
    // 0xb623a8: stur            x5, [fp, #-0x18]
    // 0xb623ac: r1 = <Widget>
    //     0xb623ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb623b0: r2 = 0
    //     0xb623b0: movz            x2, #0
    // 0xb623b4: r0 = _GrowableList()
    //     0xb623b4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb623b8: mov             x2, x0
    // 0xb623bc: ldur            x1, [fp, #-8]
    // 0xb623c0: stur            x2, [fp, #-0x38]
    // 0xb623c4: LoadField: r0 = r1->field_b
    //     0xb623c4: ldur            w0, [x1, #0xb]
    // 0xb623c8: DecompressPointer r0
    //     0xb623c8: add             x0, x0, HEAP, lsl #32
    // 0xb623cc: cmp             w0, NULL
    // 0xb623d0: b.eq            #0xb63414
    // 0xb623d4: LoadField: r3 = r0->field_f
    //     0xb623d4: ldur            w3, [x0, #0xf]
    // 0xb623d8: DecompressPointer r3
    //     0xb623d8: add             x3, x3, HEAP, lsl #32
    // 0xb623dc: cmp             w3, NULL
    // 0xb623e0: b.ne            #0xb623ec
    // 0xb623e4: r0 = Null
    //     0xb623e4: mov             x0, NULL
    // 0xb623e8: b               #0xb62404
    // 0xb623ec: LoadField: r0 = r3->field_7
    //     0xb623ec: ldur            w0, [x3, #7]
    // 0xb623f0: cbnz            w0, #0xb623fc
    // 0xb623f4: r4 = false
    //     0xb623f4: add             x4, NULL, #0x30  ; false
    // 0xb623f8: b               #0xb62400
    // 0xb623fc: r4 = true
    //     0xb623fc: add             x4, NULL, #0x20  ; true
    // 0xb62400: mov             x0, x4
    // 0xb62404: cmp             w0, NULL
    // 0xb62408: b.eq            #0xb625c8
    // 0xb6240c: tbnz            w0, #4, #0xb625c8
    // 0xb62410: cmp             w3, NULL
    // 0xb62414: b.ne            #0xb62420
    // 0xb62418: r0 = Null
    //     0xb62418: mov             x0, NULL
    // 0xb6241c: b               #0xb62438
    // 0xb62420: r0 = LoadClassIdInstr(r3)
    //     0xb62420: ldur            x0, [x3, #-1]
    //     0xb62424: ubfx            x0, x0, #0xc, #0x14
    // 0xb62428: str             x3, [SP]
    // 0xb6242c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb6242c: sub             lr, x0, #1, lsl #12
    //     0xb62430: ldr             lr, [x21, lr, lsl #3]
    //     0xb62434: blr             lr
    // 0xb62438: cmp             w0, NULL
    // 0xb6243c: b.ne            #0xb62448
    // 0xb62440: r2 = ""
    //     0xb62440: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb62444: b               #0xb6244c
    // 0xb62448: mov             x2, x0
    // 0xb6244c: ldur            x0, [fp, #-8]
    // 0xb62450: stur            x2, [fp, #-0x48]
    // 0xb62454: LoadField: r1 = r0->field_b
    //     0xb62454: ldur            w1, [x0, #0xb]
    // 0xb62458: DecompressPointer r1
    //     0xb62458: add             x1, x1, HEAP, lsl #32
    // 0xb6245c: cmp             w1, NULL
    // 0xb62460: b.eq            #0xb63418
    // 0xb62464: LoadField: r3 = r1->field_23
    //     0xb62464: ldur            w3, [x1, #0x23]
    // 0xb62468: DecompressPointer r3
    //     0xb62468: add             x3, x3, HEAP, lsl #32
    // 0xb6246c: LoadField: r1 = r3->field_7
    //     0xb6246c: ldur            w1, [x3, #7]
    // 0xb62470: DecompressPointer r1
    //     0xb62470: add             x1, x1, HEAP, lsl #32
    // 0xb62474: cmp             w1, NULL
    // 0xb62478: b.ne            #0xb62484
    // 0xb6247c: r1 = Instance_TitleAlignment
    //     0xb6247c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb62480: ldr             x1, [x1, #0x518]
    // 0xb62484: r16 = Instance_TitleAlignment
    //     0xb62484: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb62488: ldr             x16, [x16, #0x520]
    // 0xb6248c: cmp             w1, w16
    // 0xb62490: b.ne            #0xb6249c
    // 0xb62494: r4 = Instance_TextAlign
    //     0xb62494: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb62498: b               #0xb624b8
    // 0xb6249c: r16 = Instance_TitleAlignment
    //     0xb6249c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb624a0: ldr             x16, [x16, #0x518]
    // 0xb624a4: cmp             w1, w16
    // 0xb624a8: b.ne            #0xb624b4
    // 0xb624ac: r4 = Instance_TextAlign
    //     0xb624ac: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb624b0: b               #0xb624b8
    // 0xb624b4: r4 = Instance_TextAlign
    //     0xb624b4: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb624b8: ldur            x3, [fp, #-0x38]
    // 0xb624bc: ldur            x1, [fp, #-0x10]
    // 0xb624c0: stur            x4, [fp, #-0x40]
    // 0xb624c4: r0 = of()
    //     0xb624c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb624c8: LoadField: r1 = r0->field_87
    //     0xb624c8: ldur            w1, [x0, #0x87]
    // 0xb624cc: DecompressPointer r1
    //     0xb624cc: add             x1, x1, HEAP, lsl #32
    // 0xb624d0: LoadField: r0 = r1->field_7
    //     0xb624d0: ldur            w0, [x1, #7]
    // 0xb624d4: DecompressPointer r0
    //     0xb624d4: add             x0, x0, HEAP, lsl #32
    // 0xb624d8: stur            x0, [fp, #-0x50]
    // 0xb624dc: r1 = Instance_Color
    //     0xb624dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb624e0: d0 = 0.700000
    //     0xb624e0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb624e4: ldr             d0, [x17, #0xf48]
    // 0xb624e8: r0 = withOpacity()
    //     0xb624e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb624ec: r16 = 32.000000
    //     0xb624ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb624f0: ldr             x16, [x16, #0x848]
    // 0xb624f4: stp             x0, x16, [SP]
    // 0xb624f8: ldur            x1, [fp, #-0x50]
    // 0xb624fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb624fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb62500: ldr             x4, [x4, #0xaa0]
    // 0xb62504: r0 = copyWith()
    //     0xb62504: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb62508: stur            x0, [fp, #-0x50]
    // 0xb6250c: r0 = Text()
    //     0xb6250c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb62510: mov             x1, x0
    // 0xb62514: ldur            x0, [fp, #-0x48]
    // 0xb62518: stur            x1, [fp, #-0x58]
    // 0xb6251c: StoreField: r1->field_b = r0
    //     0xb6251c: stur            w0, [x1, #0xb]
    // 0xb62520: ldur            x0, [fp, #-0x50]
    // 0xb62524: StoreField: r1->field_13 = r0
    //     0xb62524: stur            w0, [x1, #0x13]
    // 0xb62528: ldur            x0, [fp, #-0x40]
    // 0xb6252c: StoreField: r1->field_1b = r0
    //     0xb6252c: stur            w0, [x1, #0x1b]
    // 0xb62530: r0 = Padding()
    //     0xb62530: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb62534: mov             x2, x0
    // 0xb62538: r0 = Instance_EdgeInsets
    //     0xb62538: add             x0, PP, #0x45, lsl #12  ; [pp+0x45c98] Obj!EdgeInsets@d589a1
    //     0xb6253c: ldr             x0, [x0, #0xc98]
    // 0xb62540: stur            x2, [fp, #-0x40]
    // 0xb62544: StoreField: r2->field_f = r0
    //     0xb62544: stur            w0, [x2, #0xf]
    // 0xb62548: ldur            x0, [fp, #-0x58]
    // 0xb6254c: StoreField: r2->field_b = r0
    //     0xb6254c: stur            w0, [x2, #0xb]
    // 0xb62550: ldur            x0, [fp, #-0x38]
    // 0xb62554: LoadField: r1 = r0->field_b
    //     0xb62554: ldur            w1, [x0, #0xb]
    // 0xb62558: LoadField: r3 = r0->field_f
    //     0xb62558: ldur            w3, [x0, #0xf]
    // 0xb6255c: DecompressPointer r3
    //     0xb6255c: add             x3, x3, HEAP, lsl #32
    // 0xb62560: LoadField: r4 = r3->field_b
    //     0xb62560: ldur            w4, [x3, #0xb]
    // 0xb62564: r3 = LoadInt32Instr(r1)
    //     0xb62564: sbfx            x3, x1, #1, #0x1f
    // 0xb62568: stur            x3, [fp, #-0x60]
    // 0xb6256c: r1 = LoadInt32Instr(r4)
    //     0xb6256c: sbfx            x1, x4, #1, #0x1f
    // 0xb62570: cmp             x3, x1
    // 0xb62574: b.ne            #0xb62580
    // 0xb62578: mov             x1, x0
    // 0xb6257c: r0 = _growToNextCapacity()
    //     0xb6257c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb62580: ldur            x2, [fp, #-0x38]
    // 0xb62584: ldur            x3, [fp, #-0x60]
    // 0xb62588: add             x0, x3, #1
    // 0xb6258c: lsl             x1, x0, #1
    // 0xb62590: StoreField: r2->field_b = r1
    //     0xb62590: stur            w1, [x2, #0xb]
    // 0xb62594: LoadField: r1 = r2->field_f
    //     0xb62594: ldur            w1, [x2, #0xf]
    // 0xb62598: DecompressPointer r1
    //     0xb62598: add             x1, x1, HEAP, lsl #32
    // 0xb6259c: ldur            x0, [fp, #-0x40]
    // 0xb625a0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb625a0: add             x25, x1, x3, lsl #2
    //     0xb625a4: add             x25, x25, #0xf
    //     0xb625a8: str             w0, [x25]
    //     0xb625ac: tbz             w0, #0, #0xb625c8
    //     0xb625b0: ldurb           w16, [x1, #-1]
    //     0xb625b4: ldurb           w17, [x0, #-1]
    //     0xb625b8: and             x16, x17, x16, lsr #2
    //     0xb625bc: tst             x16, HEAP, lsr #32
    //     0xb625c0: b.eq            #0xb625c8
    //     0xb625c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb625c8: ldur            x0, [fp, #-8]
    // 0xb625cc: LoadField: r1 = r0->field_b
    //     0xb625cc: ldur            w1, [x0, #0xb]
    // 0xb625d0: DecompressPointer r1
    //     0xb625d0: add             x1, x1, HEAP, lsl #32
    // 0xb625d4: cmp             w1, NULL
    // 0xb625d8: b.eq            #0xb6341c
    // 0xb625dc: LoadField: r3 = r1->field_13
    //     0xb625dc: ldur            w3, [x1, #0x13]
    // 0xb625e0: DecompressPointer r3
    //     0xb625e0: add             x3, x3, HEAP, lsl #32
    // 0xb625e4: tbnz            w3, #4, #0xb62900
    // 0xb625e8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb625e8: ldur            w3, [x1, #0x17]
    // 0xb625ec: DecompressPointer r3
    //     0xb625ec: add             x3, x3, HEAP, lsl #32
    // 0xb625f0: cmp             w3, NULL
    // 0xb625f4: b.ne            #0xb62600
    // 0xb625f8: r1 = Null
    //     0xb625f8: mov             x1, NULL
    // 0xb625fc: b               #0xb6262c
    // 0xb62600: LoadField: r1 = r3->field_7
    //     0xb62600: ldur            w1, [x3, #7]
    // 0xb62604: DecompressPointer r1
    //     0xb62604: add             x1, x1, HEAP, lsl #32
    // 0xb62608: cmp             w1, NULL
    // 0xb6260c: b.ne            #0xb62618
    // 0xb62610: r1 = Null
    //     0xb62610: mov             x1, NULL
    // 0xb62614: b               #0xb6262c
    // 0xb62618: LoadField: r4 = r1->field_7
    //     0xb62618: ldur            w4, [x1, #7]
    // 0xb6261c: cbnz            w4, #0xb62628
    // 0xb62620: r1 = false
    //     0xb62620: add             x1, NULL, #0x30  ; false
    // 0xb62624: b               #0xb6262c
    // 0xb62628: r1 = true
    //     0xb62628: add             x1, NULL, #0x20  ; true
    // 0xb6262c: cmp             w1, NULL
    // 0xb62630: b.eq            #0xb62900
    // 0xb62634: tbnz            w1, #4, #0xb62900
    // 0xb62638: cmp             w3, NULL
    // 0xb6263c: b.ne            #0xb62648
    // 0xb62640: r1 = Null
    //     0xb62640: mov             x1, NULL
    // 0xb62644: b               #0xb62674
    // 0xb62648: LoadField: r1 = r3->field_7
    //     0xb62648: ldur            w1, [x3, #7]
    // 0xb6264c: DecompressPointer r1
    //     0xb6264c: add             x1, x1, HEAP, lsl #32
    // 0xb62650: cmp             w1, NULL
    // 0xb62654: b.ne            #0xb62660
    // 0xb62658: r1 = Null
    //     0xb62658: mov             x1, NULL
    // 0xb6265c: b               #0xb62674
    // 0xb62660: LoadField: r3 = r1->field_7
    //     0xb62660: ldur            w3, [x1, #7]
    // 0xb62664: cbnz            w3, #0xb62670
    // 0xb62668: r1 = false
    //     0xb62668: add             x1, NULL, #0x30  ; false
    // 0xb6266c: b               #0xb62674
    // 0xb62670: r1 = true
    //     0xb62670: add             x1, NULL, #0x20  ; true
    // 0xb62674: cmp             w1, NULL
    // 0xb62678: b.ne            #0xb62684
    // 0xb6267c: r3 = false
    //     0xb6267c: add             x3, NULL, #0x30  ; false
    // 0xb62680: b               #0xb62688
    // 0xb62684: mov             x3, x1
    // 0xb62688: ldur            x1, [fp, #-0x10]
    // 0xb6268c: stur            x3, [fp, #-0x40]
    // 0xb62690: r0 = of()
    //     0xb62690: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb62694: LoadField: r1 = r0->field_5b
    //     0xb62694: ldur            w1, [x0, #0x5b]
    // 0xb62698: DecompressPointer r1
    //     0xb62698: add             x1, x1, HEAP, lsl #32
    // 0xb6269c: stur            x1, [fp, #-0x48]
    // 0xb626a0: r0 = BoxDecoration()
    //     0xb626a0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb626a4: mov             x2, x0
    // 0xb626a8: ldur            x0, [fp, #-0x48]
    // 0xb626ac: stur            x2, [fp, #-0x50]
    // 0xb626b0: StoreField: r2->field_7 = r0
    //     0xb626b0: stur            w0, [x2, #7]
    // 0xb626b4: r0 = Instance_BorderRadius
    //     0xb626b4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb626b8: ldr             x0, [x0, #0x460]
    // 0xb626bc: StoreField: r2->field_13 = r0
    //     0xb626bc: stur            w0, [x2, #0x13]
    // 0xb626c0: r0 = Instance_BoxShape
    //     0xb626c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb626c4: ldr             x0, [x0, #0x80]
    // 0xb626c8: StoreField: r2->field_23 = r0
    //     0xb626c8: stur            w0, [x2, #0x23]
    // 0xb626cc: ldur            x3, [fp, #-8]
    // 0xb626d0: LoadField: r1 = r3->field_b
    //     0xb626d0: ldur            w1, [x3, #0xb]
    // 0xb626d4: DecompressPointer r1
    //     0xb626d4: add             x1, x1, HEAP, lsl #32
    // 0xb626d8: cmp             w1, NULL
    // 0xb626dc: b.eq            #0xb63420
    // 0xb626e0: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xb626e0: ldur            w4, [x1, #0x17]
    // 0xb626e4: DecompressPointer r4
    //     0xb626e4: add             x4, x4, HEAP, lsl #32
    // 0xb626e8: cmp             w4, NULL
    // 0xb626ec: b.ne            #0xb626f8
    // 0xb626f0: r1 = Null
    //     0xb626f0: mov             x1, NULL
    // 0xb626f4: b               #0xb62700
    // 0xb626f8: LoadField: r1 = r4->field_7
    //     0xb626f8: ldur            w1, [x4, #7]
    // 0xb626fc: DecompressPointer r1
    //     0xb626fc: add             x1, x1, HEAP, lsl #32
    // 0xb62700: cmp             w1, NULL
    // 0xb62704: b.ne            #0xb62710
    // 0xb62708: r6 = ""
    //     0xb62708: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6270c: b               #0xb62714
    // 0xb62710: mov             x6, x1
    // 0xb62714: ldur            x4, [fp, #-0x38]
    // 0xb62718: ldur            x5, [fp, #-0x40]
    // 0xb6271c: ldur            x1, [fp, #-0x10]
    // 0xb62720: stur            x6, [fp, #-0x48]
    // 0xb62724: r0 = of()
    //     0xb62724: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb62728: LoadField: r1 = r0->field_87
    //     0xb62728: ldur            w1, [x0, #0x87]
    // 0xb6272c: DecompressPointer r1
    //     0xb6272c: add             x1, x1, HEAP, lsl #32
    // 0xb62730: LoadField: r0 = r1->field_2b
    //     0xb62730: ldur            w0, [x1, #0x2b]
    // 0xb62734: DecompressPointer r0
    //     0xb62734: add             x0, x0, HEAP, lsl #32
    // 0xb62738: r16 = 16.000000
    //     0xb62738: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6273c: ldr             x16, [x16, #0x188]
    // 0xb62740: r30 = Instance_Color
    //     0xb62740: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb62744: stp             lr, x16, [SP]
    // 0xb62748: mov             x1, x0
    // 0xb6274c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6274c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb62750: ldr             x4, [x4, #0xaa0]
    // 0xb62754: r0 = copyWith()
    //     0xb62754: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb62758: stur            x0, [fp, #-0x58]
    // 0xb6275c: r0 = Text()
    //     0xb6275c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb62760: mov             x1, x0
    // 0xb62764: ldur            x0, [fp, #-0x48]
    // 0xb62768: stur            x1, [fp, #-0x68]
    // 0xb6276c: StoreField: r1->field_b = r0
    //     0xb6276c: stur            w0, [x1, #0xb]
    // 0xb62770: ldur            x0, [fp, #-0x58]
    // 0xb62774: StoreField: r1->field_13 = r0
    //     0xb62774: stur            w0, [x1, #0x13]
    // 0xb62778: r0 = Center()
    //     0xb62778: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb6277c: mov             x1, x0
    // 0xb62780: r0 = Instance_Alignment
    //     0xb62780: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb62784: ldr             x0, [x0, #0xb10]
    // 0xb62788: stur            x1, [fp, #-0x48]
    // 0xb6278c: StoreField: r1->field_f = r0
    //     0xb6278c: stur            w0, [x1, #0xf]
    // 0xb62790: ldur            x0, [fp, #-0x68]
    // 0xb62794: StoreField: r1->field_b = r0
    //     0xb62794: stur            w0, [x1, #0xb]
    // 0xb62798: r0 = Container()
    //     0xb62798: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6279c: stur            x0, [fp, #-0x58]
    // 0xb627a0: r16 = 40.000000
    //     0xb627a0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb627a4: ldr             x16, [x16, #8]
    // 0xb627a8: r30 = 110.000000
    //     0xb627a8: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb627ac: ldr             lr, [lr, #0x770]
    // 0xb627b0: stp             lr, x16, [SP, #0x10]
    // 0xb627b4: ldur            x16, [fp, #-0x50]
    // 0xb627b8: ldur            lr, [fp, #-0x48]
    // 0xb627bc: stp             lr, x16, [SP]
    // 0xb627c0: mov             x1, x0
    // 0xb627c4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb627c4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb627c8: ldr             x4, [x4, #0x8c0]
    // 0xb627cc: r0 = Container()
    //     0xb627cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb627d0: r0 = Padding()
    //     0xb627d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb627d4: mov             x1, x0
    // 0xb627d8: r0 = Instance_EdgeInsets
    //     0xb627d8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb627dc: ldr             x0, [x0, #0x778]
    // 0xb627e0: stur            x1, [fp, #-0x48]
    // 0xb627e4: StoreField: r1->field_f = r0
    //     0xb627e4: stur            w0, [x1, #0xf]
    // 0xb627e8: ldur            x0, [fp, #-0x58]
    // 0xb627ec: StoreField: r1->field_b = r0
    //     0xb627ec: stur            w0, [x1, #0xb]
    // 0xb627f0: r0 = Visibility()
    //     0xb627f0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb627f4: mov             x1, x0
    // 0xb627f8: ldur            x0, [fp, #-0x48]
    // 0xb627fc: stur            x1, [fp, #-0x50]
    // 0xb62800: StoreField: r1->field_b = r0
    //     0xb62800: stur            w0, [x1, #0xb]
    // 0xb62804: r0 = Instance_SizedBox
    //     0xb62804: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb62808: StoreField: r1->field_f = r0
    //     0xb62808: stur            w0, [x1, #0xf]
    // 0xb6280c: ldur            x2, [fp, #-0x40]
    // 0xb62810: StoreField: r1->field_13 = r2
    //     0xb62810: stur            w2, [x1, #0x13]
    // 0xb62814: r2 = false
    //     0xb62814: add             x2, NULL, #0x30  ; false
    // 0xb62818: ArrayStore: r1[0] = r2  ; List_4
    //     0xb62818: stur            w2, [x1, #0x17]
    // 0xb6281c: StoreField: r1->field_1b = r2
    //     0xb6281c: stur            w2, [x1, #0x1b]
    // 0xb62820: StoreField: r1->field_1f = r2
    //     0xb62820: stur            w2, [x1, #0x1f]
    // 0xb62824: StoreField: r1->field_23 = r2
    //     0xb62824: stur            w2, [x1, #0x23]
    // 0xb62828: StoreField: r1->field_27 = r2
    //     0xb62828: stur            w2, [x1, #0x27]
    // 0xb6282c: StoreField: r1->field_2b = r2
    //     0xb6282c: stur            w2, [x1, #0x2b]
    // 0xb62830: r0 = InkWell()
    //     0xb62830: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb62834: mov             x3, x0
    // 0xb62838: ldur            x0, [fp, #-0x50]
    // 0xb6283c: stur            x3, [fp, #-0x40]
    // 0xb62840: StoreField: r3->field_b = r0
    //     0xb62840: stur            w0, [x3, #0xb]
    // 0xb62844: ldur            x2, [fp, #-0x20]
    // 0xb62848: r1 = Function '<anonymous closure>':.
    //     0xb62848: add             x1, PP, #0x56, lsl #12  ; [pp+0x562c0] AnonymousClosure: (0xb66534), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb6284c: ldr             x1, [x1, #0x2c0]
    // 0xb62850: r0 = AllocateClosure()
    //     0xb62850: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb62854: mov             x1, x0
    // 0xb62858: ldur            x0, [fp, #-0x40]
    // 0xb6285c: StoreField: r0->field_f = r1
    //     0xb6285c: stur            w1, [x0, #0xf]
    // 0xb62860: r1 = true
    //     0xb62860: add             x1, NULL, #0x20  ; true
    // 0xb62864: StoreField: r0->field_43 = r1
    //     0xb62864: stur            w1, [x0, #0x43]
    // 0xb62868: r2 = Instance_BoxShape
    //     0xb62868: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6286c: ldr             x2, [x2, #0x80]
    // 0xb62870: StoreField: r0->field_47 = r2
    //     0xb62870: stur            w2, [x0, #0x47]
    // 0xb62874: StoreField: r0->field_6f = r1
    //     0xb62874: stur            w1, [x0, #0x6f]
    // 0xb62878: r3 = false
    //     0xb62878: add             x3, NULL, #0x30  ; false
    // 0xb6287c: StoreField: r0->field_73 = r3
    //     0xb6287c: stur            w3, [x0, #0x73]
    // 0xb62880: StoreField: r0->field_83 = r1
    //     0xb62880: stur            w1, [x0, #0x83]
    // 0xb62884: StoreField: r0->field_7b = r3
    //     0xb62884: stur            w3, [x0, #0x7b]
    // 0xb62888: ldur            x4, [fp, #-0x38]
    // 0xb6288c: LoadField: r1 = r4->field_b
    //     0xb6288c: ldur            w1, [x4, #0xb]
    // 0xb62890: LoadField: r5 = r4->field_f
    //     0xb62890: ldur            w5, [x4, #0xf]
    // 0xb62894: DecompressPointer r5
    //     0xb62894: add             x5, x5, HEAP, lsl #32
    // 0xb62898: LoadField: r6 = r5->field_b
    //     0xb62898: ldur            w6, [x5, #0xb]
    // 0xb6289c: r5 = LoadInt32Instr(r1)
    //     0xb6289c: sbfx            x5, x1, #1, #0x1f
    // 0xb628a0: stur            x5, [fp, #-0x60]
    // 0xb628a4: r1 = LoadInt32Instr(r6)
    //     0xb628a4: sbfx            x1, x6, #1, #0x1f
    // 0xb628a8: cmp             x5, x1
    // 0xb628ac: b.ne            #0xb628b8
    // 0xb628b0: mov             x1, x4
    // 0xb628b4: r0 = _growToNextCapacity()
    //     0xb628b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb628b8: ldur            x2, [fp, #-0x38]
    // 0xb628bc: ldur            x3, [fp, #-0x60]
    // 0xb628c0: add             x0, x3, #1
    // 0xb628c4: lsl             x1, x0, #1
    // 0xb628c8: StoreField: r2->field_b = r1
    //     0xb628c8: stur            w1, [x2, #0xb]
    // 0xb628cc: LoadField: r1 = r2->field_f
    //     0xb628cc: ldur            w1, [x2, #0xf]
    // 0xb628d0: DecompressPointer r1
    //     0xb628d0: add             x1, x1, HEAP, lsl #32
    // 0xb628d4: ldur            x0, [fp, #-0x40]
    // 0xb628d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb628d8: add             x25, x1, x3, lsl #2
    //     0xb628dc: add             x25, x25, #0xf
    //     0xb628e0: str             w0, [x25]
    //     0xb628e4: tbz             w0, #0, #0xb62900
    //     0xb628e8: ldurb           w16, [x1, #-1]
    //     0xb628ec: ldurb           w17, [x0, #-1]
    //     0xb628f0: and             x16, x17, x16, lsr #2
    //     0xb628f4: tst             x16, HEAP, lsr #32
    //     0xb628f8: b.eq            #0xb62900
    //     0xb628fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb62900: ldur            x0, [fp, #-8]
    // 0xb62904: LoadField: r1 = r0->field_b
    //     0xb62904: ldur            w1, [x0, #0xb]
    // 0xb62908: DecompressPointer r1
    //     0xb62908: add             x1, x1, HEAP, lsl #32
    // 0xb6290c: cmp             w1, NULL
    // 0xb62910: b.eq            #0xb63424
    // 0xb62914: LoadField: r3 = r1->field_43
    //     0xb62914: ldur            w3, [x1, #0x43]
    // 0xb62918: DecompressPointer r3
    //     0xb62918: add             x3, x3, HEAP, lsl #32
    // 0xb6291c: stur            x3, [fp, #-0x48]
    // 0xb62920: cmp             w3, NULL
    // 0xb62924: b.ne            #0xb62930
    // 0xb62928: r1 = Null
    //     0xb62928: mov             x1, NULL
    // 0xb6292c: b               #0xb62938
    // 0xb62930: LoadField: r1 = r3->field_f
    //     0xb62930: ldur            w1, [x3, #0xf]
    // 0xb62934: DecompressPointer r1
    //     0xb62934: add             x1, x1, HEAP, lsl #32
    // 0xb62938: cmp             w1, NULL
    // 0xb6293c: r16 = true
    //     0xb6293c: add             x16, NULL, #0x20  ; true
    // 0xb62940: r17 = false
    //     0xb62940: add             x17, NULL, #0x30  ; false
    // 0xb62944: csel            x4, x16, x17, ne
    // 0xb62948: stur            x4, [fp, #-0x40]
    // 0xb6294c: r0 = Radius()
    //     0xb6294c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb62950: d0 = 20.000000
    //     0xb62950: fmov            d0, #20.00000000
    // 0xb62954: stur            x0, [fp, #-0x50]
    // 0xb62958: StoreField: r0->field_7 = d0
    //     0xb62958: stur            d0, [x0, #7]
    // 0xb6295c: StoreField: r0->field_f = d0
    //     0xb6295c: stur            d0, [x0, #0xf]
    // 0xb62960: r0 = BorderRadius()
    //     0xb62960: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb62964: mov             x1, x0
    // 0xb62968: ldur            x0, [fp, #-0x50]
    // 0xb6296c: stur            x1, [fp, #-0x58]
    // 0xb62970: StoreField: r1->field_7 = r0
    //     0xb62970: stur            w0, [x1, #7]
    // 0xb62974: StoreField: r1->field_b = r0
    //     0xb62974: stur            w0, [x1, #0xb]
    // 0xb62978: StoreField: r1->field_f = r0
    //     0xb62978: stur            w0, [x1, #0xf]
    // 0xb6297c: StoreField: r1->field_13 = r0
    //     0xb6297c: stur            w0, [x1, #0x13]
    // 0xb62980: ldur            x0, [fp, #-0x48]
    // 0xb62984: cmp             w0, NULL
    // 0xb62988: b.ne            #0xb62994
    // 0xb6298c: r2 = Null
    //     0xb6298c: mov             x2, NULL
    // 0xb62990: b               #0xb629b8
    // 0xb62994: LoadField: r2 = r0->field_13
    //     0xb62994: ldur            w2, [x0, #0x13]
    // 0xb62998: DecompressPointer r2
    //     0xb62998: add             x2, x2, HEAP, lsl #32
    // 0xb6299c: cmp             w2, NULL
    // 0xb629a0: b.ne            #0xb629ac
    // 0xb629a4: r2 = Null
    //     0xb629a4: mov             x2, NULL
    // 0xb629a8: b               #0xb629b8
    // 0xb629ac: LoadField: r3 = r2->field_7
    //     0xb629ac: ldur            w3, [x2, #7]
    // 0xb629b0: DecompressPointer r3
    //     0xb629b0: add             x3, x3, HEAP, lsl #32
    // 0xb629b4: mov             x2, x3
    // 0xb629b8: cmp             w2, NULL
    // 0xb629bc: b.ne            #0xb629c8
    // 0xb629c0: r2 = 0
    //     0xb629c0: movz            x2, #0
    // 0xb629c4: b               #0xb629d8
    // 0xb629c8: r3 = LoadInt32Instr(r2)
    //     0xb629c8: sbfx            x3, x2, #1, #0x1f
    //     0xb629cc: tbz             w2, #0, #0xb629d4
    //     0xb629d0: ldur            x3, [x2, #7]
    // 0xb629d4: mov             x2, x3
    // 0xb629d8: stur            x2, [fp, #-0x78]
    // 0xb629dc: cmp             w0, NULL
    // 0xb629e0: b.ne            #0xb629ec
    // 0xb629e4: r3 = Null
    //     0xb629e4: mov             x3, NULL
    // 0xb629e8: b               #0xb62a10
    // 0xb629ec: LoadField: r3 = r0->field_13
    //     0xb629ec: ldur            w3, [x0, #0x13]
    // 0xb629f0: DecompressPointer r3
    //     0xb629f0: add             x3, x3, HEAP, lsl #32
    // 0xb629f4: cmp             w3, NULL
    // 0xb629f8: b.ne            #0xb62a04
    // 0xb629fc: r3 = Null
    //     0xb629fc: mov             x3, NULL
    // 0xb62a00: b               #0xb62a10
    // 0xb62a04: LoadField: r4 = r3->field_b
    //     0xb62a04: ldur            w4, [x3, #0xb]
    // 0xb62a08: DecompressPointer r4
    //     0xb62a08: add             x4, x4, HEAP, lsl #32
    // 0xb62a0c: mov             x3, x4
    // 0xb62a10: cmp             w3, NULL
    // 0xb62a14: b.ne            #0xb62a20
    // 0xb62a18: r3 = 0
    //     0xb62a18: movz            x3, #0
    // 0xb62a1c: b               #0xb62a30
    // 0xb62a20: r4 = LoadInt32Instr(r3)
    //     0xb62a20: sbfx            x4, x3, #1, #0x1f
    //     0xb62a24: tbz             w3, #0, #0xb62a2c
    //     0xb62a28: ldur            x4, [x3, #7]
    // 0xb62a2c: mov             x3, x4
    // 0xb62a30: stur            x3, [fp, #-0x70]
    // 0xb62a34: cmp             w0, NULL
    // 0xb62a38: b.ne            #0xb62a44
    // 0xb62a3c: r4 = Null
    //     0xb62a3c: mov             x4, NULL
    // 0xb62a40: b               #0xb62a68
    // 0xb62a44: LoadField: r4 = r0->field_13
    //     0xb62a44: ldur            w4, [x0, #0x13]
    // 0xb62a48: DecompressPointer r4
    //     0xb62a48: add             x4, x4, HEAP, lsl #32
    // 0xb62a4c: cmp             w4, NULL
    // 0xb62a50: b.ne            #0xb62a5c
    // 0xb62a54: r4 = Null
    //     0xb62a54: mov             x4, NULL
    // 0xb62a58: b               #0xb62a68
    // 0xb62a5c: LoadField: r5 = r4->field_f
    //     0xb62a5c: ldur            w5, [x4, #0xf]
    // 0xb62a60: DecompressPointer r5
    //     0xb62a60: add             x5, x5, HEAP, lsl #32
    // 0xb62a64: mov             x4, x5
    // 0xb62a68: cmp             w4, NULL
    // 0xb62a6c: b.ne            #0xb62a78
    // 0xb62a70: r4 = 0
    //     0xb62a70: movz            x4, #0
    // 0xb62a74: b               #0xb62a88
    // 0xb62a78: r5 = LoadInt32Instr(r4)
    //     0xb62a78: sbfx            x5, x4, #1, #0x1f
    //     0xb62a7c: tbz             w4, #0, #0xb62a84
    //     0xb62a80: ldur            x5, [x4, #7]
    // 0xb62a84: mov             x4, x5
    // 0xb62a88: stur            x4, [fp, #-0x60]
    // 0xb62a8c: r0 = Color()
    //     0xb62a8c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb62a90: mov             x1, x0
    // 0xb62a94: r0 = Instance_ColorSpace
    //     0xb62a94: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb62a98: stur            x1, [fp, #-0x50]
    // 0xb62a9c: StoreField: r1->field_27 = r0
    //     0xb62a9c: stur            w0, [x1, #0x27]
    // 0xb62aa0: d0 = 1.000000
    //     0xb62aa0: fmov            d0, #1.00000000
    // 0xb62aa4: StoreField: r1->field_7 = d0
    //     0xb62aa4: stur            d0, [x1, #7]
    // 0xb62aa8: ldur            x2, [fp, #-0x78]
    // 0xb62aac: ubfx            x2, x2, #0, #0x20
    // 0xb62ab0: and             w3, w2, #0xff
    // 0xb62ab4: ubfx            x3, x3, #0, #0x20
    // 0xb62ab8: scvtf           d0, x3
    // 0xb62abc: d1 = 255.000000
    //     0xb62abc: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb62ac0: fdiv            d2, d0, d1
    // 0xb62ac4: StoreField: r1->field_f = d2
    //     0xb62ac4: stur            d2, [x1, #0xf]
    // 0xb62ac8: ldur            x2, [fp, #-0x70]
    // 0xb62acc: ubfx            x2, x2, #0, #0x20
    // 0xb62ad0: and             w3, w2, #0xff
    // 0xb62ad4: ubfx            x3, x3, #0, #0x20
    // 0xb62ad8: scvtf           d0, x3
    // 0xb62adc: fdiv            d2, d0, d1
    // 0xb62ae0: ArrayStore: r1[0] = d2  ; List_8
    //     0xb62ae0: stur            d2, [x1, #0x17]
    // 0xb62ae4: ldur            x2, [fp, #-0x60]
    // 0xb62ae8: ubfx            x2, x2, #0, #0x20
    // 0xb62aec: and             w3, w2, #0xff
    // 0xb62af0: ubfx            x3, x3, #0, #0x20
    // 0xb62af4: scvtf           d0, x3
    // 0xb62af8: fdiv            d2, d0, d1
    // 0xb62afc: StoreField: r1->field_1f = d2
    //     0xb62afc: stur            d2, [x1, #0x1f]
    // 0xb62b00: ldur            x2, [fp, #-0x48]
    // 0xb62b04: cmp             w2, NULL
    // 0xb62b08: b.ne            #0xb62b14
    // 0xb62b0c: r3 = Null
    //     0xb62b0c: mov             x3, NULL
    // 0xb62b10: b               #0xb62b38
    // 0xb62b14: LoadField: r3 = r2->field_13
    //     0xb62b14: ldur            w3, [x2, #0x13]
    // 0xb62b18: DecompressPointer r3
    //     0xb62b18: add             x3, x3, HEAP, lsl #32
    // 0xb62b1c: cmp             w3, NULL
    // 0xb62b20: b.ne            #0xb62b2c
    // 0xb62b24: r3 = Null
    //     0xb62b24: mov             x3, NULL
    // 0xb62b28: b               #0xb62b38
    // 0xb62b2c: LoadField: r4 = r3->field_7
    //     0xb62b2c: ldur            w4, [x3, #7]
    // 0xb62b30: DecompressPointer r4
    //     0xb62b30: add             x4, x4, HEAP, lsl #32
    // 0xb62b34: mov             x3, x4
    // 0xb62b38: cmp             w3, NULL
    // 0xb62b3c: b.ne            #0xb62b48
    // 0xb62b40: r3 = 0
    //     0xb62b40: movz            x3, #0
    // 0xb62b44: b               #0xb62b58
    // 0xb62b48: r4 = LoadInt32Instr(r3)
    //     0xb62b48: sbfx            x4, x3, #1, #0x1f
    //     0xb62b4c: tbz             w3, #0, #0xb62b54
    //     0xb62b50: ldur            x4, [x3, #7]
    // 0xb62b54: mov             x3, x4
    // 0xb62b58: stur            x3, [fp, #-0x78]
    // 0xb62b5c: cmp             w2, NULL
    // 0xb62b60: b.ne            #0xb62b6c
    // 0xb62b64: r4 = Null
    //     0xb62b64: mov             x4, NULL
    // 0xb62b68: b               #0xb62b90
    // 0xb62b6c: LoadField: r4 = r2->field_13
    //     0xb62b6c: ldur            w4, [x2, #0x13]
    // 0xb62b70: DecompressPointer r4
    //     0xb62b70: add             x4, x4, HEAP, lsl #32
    // 0xb62b74: cmp             w4, NULL
    // 0xb62b78: b.ne            #0xb62b84
    // 0xb62b7c: r4 = Null
    //     0xb62b7c: mov             x4, NULL
    // 0xb62b80: b               #0xb62b90
    // 0xb62b84: LoadField: r5 = r4->field_b
    //     0xb62b84: ldur            w5, [x4, #0xb]
    // 0xb62b88: DecompressPointer r5
    //     0xb62b88: add             x5, x5, HEAP, lsl #32
    // 0xb62b8c: mov             x4, x5
    // 0xb62b90: cmp             w4, NULL
    // 0xb62b94: b.ne            #0xb62ba0
    // 0xb62b98: r4 = 0
    //     0xb62b98: movz            x4, #0
    // 0xb62b9c: b               #0xb62bb0
    // 0xb62ba0: r5 = LoadInt32Instr(r4)
    //     0xb62ba0: sbfx            x5, x4, #1, #0x1f
    //     0xb62ba4: tbz             w4, #0, #0xb62bac
    //     0xb62ba8: ldur            x5, [x4, #7]
    // 0xb62bac: mov             x4, x5
    // 0xb62bb0: stur            x4, [fp, #-0x70]
    // 0xb62bb4: cmp             w2, NULL
    // 0xb62bb8: b.ne            #0xb62bc4
    // 0xb62bbc: r2 = Null
    //     0xb62bbc: mov             x2, NULL
    // 0xb62bc0: b               #0xb62be4
    // 0xb62bc4: LoadField: r5 = r2->field_13
    //     0xb62bc4: ldur            w5, [x2, #0x13]
    // 0xb62bc8: DecompressPointer r5
    //     0xb62bc8: add             x5, x5, HEAP, lsl #32
    // 0xb62bcc: cmp             w5, NULL
    // 0xb62bd0: b.ne            #0xb62bdc
    // 0xb62bd4: r2 = Null
    //     0xb62bd4: mov             x2, NULL
    // 0xb62bd8: b               #0xb62be4
    // 0xb62bdc: LoadField: r2 = r5->field_f
    //     0xb62bdc: ldur            w2, [x5, #0xf]
    // 0xb62be0: DecompressPointer r2
    //     0xb62be0: add             x2, x2, HEAP, lsl #32
    // 0xb62be4: cmp             w2, NULL
    // 0xb62be8: b.ne            #0xb62bf4
    // 0xb62bec: r6 = 0
    //     0xb62bec: movz            x6, #0
    // 0xb62bf0: b               #0xb62c04
    // 0xb62bf4: r5 = LoadInt32Instr(r2)
    //     0xb62bf4: sbfx            x5, x2, #1, #0x1f
    //     0xb62bf8: tbz             w2, #0, #0xb62c00
    //     0xb62bfc: ldur            x5, [x2, #7]
    // 0xb62c00: mov             x6, x5
    // 0xb62c04: ldur            x5, [fp, #-8]
    // 0xb62c08: ldur            x2, [fp, #-0x58]
    // 0xb62c0c: stur            x6, [fp, #-0x60]
    // 0xb62c10: r0 = Color()
    //     0xb62c10: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb62c14: mov             x3, x0
    // 0xb62c18: r0 = Instance_ColorSpace
    //     0xb62c18: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb62c1c: stur            x3, [fp, #-0x48]
    // 0xb62c20: StoreField: r3->field_27 = r0
    //     0xb62c20: stur            w0, [x3, #0x27]
    // 0xb62c24: d0 = 0.700000
    //     0xb62c24: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb62c28: ldr             d0, [x17, #0xf48]
    // 0xb62c2c: StoreField: r3->field_7 = d0
    //     0xb62c2c: stur            d0, [x3, #7]
    // 0xb62c30: ldur            x0, [fp, #-0x78]
    // 0xb62c34: ubfx            x0, x0, #0, #0x20
    // 0xb62c38: and             w1, w0, #0xff
    // 0xb62c3c: ubfx            x1, x1, #0, #0x20
    // 0xb62c40: scvtf           d0, x1
    // 0xb62c44: d1 = 255.000000
    //     0xb62c44: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb62c48: fdiv            d2, d0, d1
    // 0xb62c4c: StoreField: r3->field_f = d2
    //     0xb62c4c: stur            d2, [x3, #0xf]
    // 0xb62c50: ldur            x0, [fp, #-0x70]
    // 0xb62c54: ubfx            x0, x0, #0, #0x20
    // 0xb62c58: and             w1, w0, #0xff
    // 0xb62c5c: ubfx            x1, x1, #0, #0x20
    // 0xb62c60: scvtf           d0, x1
    // 0xb62c64: fdiv            d2, d0, d1
    // 0xb62c68: ArrayStore: r3[0] = d2  ; List_8
    //     0xb62c68: stur            d2, [x3, #0x17]
    // 0xb62c6c: ldur            x0, [fp, #-0x60]
    // 0xb62c70: ubfx            x0, x0, #0, #0x20
    // 0xb62c74: and             w1, w0, #0xff
    // 0xb62c78: ubfx            x1, x1, #0, #0x20
    // 0xb62c7c: scvtf           d0, x1
    // 0xb62c80: fdiv            d2, d0, d1
    // 0xb62c84: StoreField: r3->field_1f = d2
    //     0xb62c84: stur            d2, [x3, #0x1f]
    // 0xb62c88: r1 = Null
    //     0xb62c88: mov             x1, NULL
    // 0xb62c8c: r2 = 4
    //     0xb62c8c: movz            x2, #0x4
    // 0xb62c90: r0 = AllocateArray()
    //     0xb62c90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb62c94: mov             x2, x0
    // 0xb62c98: ldur            x0, [fp, #-0x50]
    // 0xb62c9c: stur            x2, [fp, #-0x68]
    // 0xb62ca0: StoreField: r2->field_f = r0
    //     0xb62ca0: stur            w0, [x2, #0xf]
    // 0xb62ca4: ldur            x0, [fp, #-0x48]
    // 0xb62ca8: StoreField: r2->field_13 = r0
    //     0xb62ca8: stur            w0, [x2, #0x13]
    // 0xb62cac: r1 = <Color>
    //     0xb62cac: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb62cb0: ldr             x1, [x1, #0xf80]
    // 0xb62cb4: r0 = AllocateGrowableArray()
    //     0xb62cb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb62cb8: mov             x1, x0
    // 0xb62cbc: ldur            x0, [fp, #-0x68]
    // 0xb62cc0: stur            x1, [fp, #-0x48]
    // 0xb62cc4: StoreField: r1->field_f = r0
    //     0xb62cc4: stur            w0, [x1, #0xf]
    // 0xb62cc8: r2 = 4
    //     0xb62cc8: movz            x2, #0x4
    // 0xb62ccc: StoreField: r1->field_b = r2
    //     0xb62ccc: stur            w2, [x1, #0xb]
    // 0xb62cd0: r0 = LinearGradient()
    //     0xb62cd0: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb62cd4: mov             x1, x0
    // 0xb62cd8: r0 = Instance_Alignment
    //     0xb62cd8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb62cdc: ldr             x0, [x0, #0xce0]
    // 0xb62ce0: stur            x1, [fp, #-0x50]
    // 0xb62ce4: StoreField: r1->field_13 = r0
    //     0xb62ce4: stur            w0, [x1, #0x13]
    // 0xb62ce8: r0 = Instance_Alignment
    //     0xb62ce8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb62cec: ldr             x0, [x0, #0xce8]
    // 0xb62cf0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb62cf0: stur            w0, [x1, #0x17]
    // 0xb62cf4: r0 = Instance_TileMode
    //     0xb62cf4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb62cf8: ldr             x0, [x0, #0xcf0]
    // 0xb62cfc: StoreField: r1->field_1b = r0
    //     0xb62cfc: stur            w0, [x1, #0x1b]
    // 0xb62d00: ldur            x0, [fp, #-0x48]
    // 0xb62d04: StoreField: r1->field_7 = r0
    //     0xb62d04: stur            w0, [x1, #7]
    // 0xb62d08: r0 = BoxDecoration()
    //     0xb62d08: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb62d0c: mov             x2, x0
    // 0xb62d10: ldur            x0, [fp, #-0x58]
    // 0xb62d14: stur            x2, [fp, #-0x48]
    // 0xb62d18: StoreField: r2->field_13 = r0
    //     0xb62d18: stur            w0, [x2, #0x13]
    // 0xb62d1c: ldur            x0, [fp, #-0x50]
    // 0xb62d20: StoreField: r2->field_1b = r0
    //     0xb62d20: stur            w0, [x2, #0x1b]
    // 0xb62d24: r0 = Instance_BoxShape
    //     0xb62d24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb62d28: ldr             x0, [x0, #0x80]
    // 0xb62d2c: StoreField: r2->field_23 = r0
    //     0xb62d2c: stur            w0, [x2, #0x23]
    // 0xb62d30: ldur            x1, [fp, #-0x10]
    // 0xb62d34: r0 = of()
    //     0xb62d34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb62d38: LoadField: r1 = r0->field_87
    //     0xb62d38: ldur            w1, [x0, #0x87]
    // 0xb62d3c: DecompressPointer r1
    //     0xb62d3c: add             x1, x1, HEAP, lsl #32
    // 0xb62d40: LoadField: r0 = r1->field_7
    //     0xb62d40: ldur            w0, [x1, #7]
    // 0xb62d44: DecompressPointer r0
    //     0xb62d44: add             x0, x0, HEAP, lsl #32
    // 0xb62d48: r16 = 16.000000
    //     0xb62d48: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb62d4c: ldr             x16, [x16, #0x188]
    // 0xb62d50: r30 = Instance_Color
    //     0xb62d50: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb62d54: stp             lr, x16, [SP]
    // 0xb62d58: mov             x1, x0
    // 0xb62d5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb62d5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb62d60: ldr             x4, [x4, #0xaa0]
    // 0xb62d64: r0 = copyWith()
    //     0xb62d64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb62d68: stur            x0, [fp, #-0x50]
    // 0xb62d6c: r0 = TextSpan()
    //     0xb62d6c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb62d70: mov             x2, x0
    // 0xb62d74: r0 = "BUMPER OFFER\n"
    //     0xb62d74: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xb62d78: ldr             x0, [x0, #0x338]
    // 0xb62d7c: stur            x2, [fp, #-0x58]
    // 0xb62d80: StoreField: r2->field_b = r0
    //     0xb62d80: stur            w0, [x2, #0xb]
    // 0xb62d84: r0 = Instance__DeferringMouseCursor
    //     0xb62d84: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb62d88: ArrayStore: r2[0] = r0  ; List_4
    //     0xb62d88: stur            w0, [x2, #0x17]
    // 0xb62d8c: ldur            x1, [fp, #-0x50]
    // 0xb62d90: StoreField: r2->field_7 = r1
    //     0xb62d90: stur            w1, [x2, #7]
    // 0xb62d94: ldur            x1, [fp, #-0x10]
    // 0xb62d98: r0 = of()
    //     0xb62d98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb62d9c: LoadField: r1 = r0->field_87
    //     0xb62d9c: ldur            w1, [x0, #0x87]
    // 0xb62da0: DecompressPointer r1
    //     0xb62da0: add             x1, x1, HEAP, lsl #32
    // 0xb62da4: LoadField: r0 = r1->field_2b
    //     0xb62da4: ldur            w0, [x1, #0x2b]
    // 0xb62da8: DecompressPointer r0
    //     0xb62da8: add             x0, x0, HEAP, lsl #32
    // 0xb62dac: r16 = 12.000000
    //     0xb62dac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb62db0: ldr             x16, [x16, #0x9e8]
    // 0xb62db4: r30 = Instance_Color
    //     0xb62db4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb62db8: stp             lr, x16, [SP]
    // 0xb62dbc: mov             x1, x0
    // 0xb62dc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb62dc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb62dc4: ldr             x4, [x4, #0xaa0]
    // 0xb62dc8: r0 = copyWith()
    //     0xb62dc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb62dcc: stur            x0, [fp, #-0x50]
    // 0xb62dd0: r0 = TextSpan()
    //     0xb62dd0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb62dd4: mov             x3, x0
    // 0xb62dd8: r0 = "Unlocked from your last order"
    //     0xb62dd8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xb62ddc: ldr             x0, [x0, #0x340]
    // 0xb62de0: stur            x3, [fp, #-0x68]
    // 0xb62de4: StoreField: r3->field_b = r0
    //     0xb62de4: stur            w0, [x3, #0xb]
    // 0xb62de8: r0 = Instance__DeferringMouseCursor
    //     0xb62de8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb62dec: ArrayStore: r3[0] = r0  ; List_4
    //     0xb62dec: stur            w0, [x3, #0x17]
    // 0xb62df0: ldur            x1, [fp, #-0x50]
    // 0xb62df4: StoreField: r3->field_7 = r1
    //     0xb62df4: stur            w1, [x3, #7]
    // 0xb62df8: r1 = Null
    //     0xb62df8: mov             x1, NULL
    // 0xb62dfc: r2 = 4
    //     0xb62dfc: movz            x2, #0x4
    // 0xb62e00: r0 = AllocateArray()
    //     0xb62e00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb62e04: mov             x2, x0
    // 0xb62e08: ldur            x0, [fp, #-0x58]
    // 0xb62e0c: stur            x2, [fp, #-0x50]
    // 0xb62e10: StoreField: r2->field_f = r0
    //     0xb62e10: stur            w0, [x2, #0xf]
    // 0xb62e14: ldur            x0, [fp, #-0x68]
    // 0xb62e18: StoreField: r2->field_13 = r0
    //     0xb62e18: stur            w0, [x2, #0x13]
    // 0xb62e1c: r1 = <InlineSpan>
    //     0xb62e1c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb62e20: ldr             x1, [x1, #0xe40]
    // 0xb62e24: r0 = AllocateGrowableArray()
    //     0xb62e24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb62e28: mov             x1, x0
    // 0xb62e2c: ldur            x0, [fp, #-0x50]
    // 0xb62e30: stur            x1, [fp, #-0x58]
    // 0xb62e34: StoreField: r1->field_f = r0
    //     0xb62e34: stur            w0, [x1, #0xf]
    // 0xb62e38: r2 = 4
    //     0xb62e38: movz            x2, #0x4
    // 0xb62e3c: StoreField: r1->field_b = r2
    //     0xb62e3c: stur            w2, [x1, #0xb]
    // 0xb62e40: r0 = TextSpan()
    //     0xb62e40: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb62e44: mov             x1, x0
    // 0xb62e48: ldur            x0, [fp, #-0x58]
    // 0xb62e4c: stur            x1, [fp, #-0x50]
    // 0xb62e50: StoreField: r1->field_f = r0
    //     0xb62e50: stur            w0, [x1, #0xf]
    // 0xb62e54: r0 = Instance__DeferringMouseCursor
    //     0xb62e54: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb62e58: ArrayStore: r1[0] = r0  ; List_4
    //     0xb62e58: stur            w0, [x1, #0x17]
    // 0xb62e5c: r0 = RichText()
    //     0xb62e5c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb62e60: mov             x1, x0
    // 0xb62e64: ldur            x2, [fp, #-0x50]
    // 0xb62e68: stur            x0, [fp, #-0x50]
    // 0xb62e6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb62e6c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb62e70: r0 = RichText()
    //     0xb62e70: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb62e74: ldur            x0, [fp, #-8]
    // 0xb62e78: LoadField: r1 = r0->field_b
    //     0xb62e78: ldur            w1, [x0, #0xb]
    // 0xb62e7c: DecompressPointer r1
    //     0xb62e7c: add             x1, x1, HEAP, lsl #32
    // 0xb62e80: cmp             w1, NULL
    // 0xb62e84: b.eq            #0xb63428
    // 0xb62e88: LoadField: r2 = r1->field_43
    //     0xb62e88: ldur            w2, [x1, #0x43]
    // 0xb62e8c: DecompressPointer r2
    //     0xb62e8c: add             x2, x2, HEAP, lsl #32
    // 0xb62e90: cmp             w2, NULL
    // 0xb62e94: b.ne            #0xb62ea0
    // 0xb62e98: r6 = Null
    //     0xb62e98: mov             x6, NULL
    // 0xb62e9c: b               #0xb62eac
    // 0xb62ea0: LoadField: r1 = r2->field_7
    //     0xb62ea0: ldur            w1, [x2, #7]
    // 0xb62ea4: DecompressPointer r1
    //     0xb62ea4: add             x1, x1, HEAP, lsl #32
    // 0xb62ea8: mov             x6, x1
    // 0xb62eac: ldur            x4, [fp, #-0x38]
    // 0xb62eb0: ldur            x5, [fp, #-0x40]
    // 0xb62eb4: ldur            x3, [fp, #-0x50]
    // 0xb62eb8: stur            x6, [fp, #-0x58]
    // 0xb62ebc: r1 = Null
    //     0xb62ebc: mov             x1, NULL
    // 0xb62ec0: r2 = 4
    //     0xb62ec0: movz            x2, #0x4
    // 0xb62ec4: r0 = AllocateArray()
    //     0xb62ec4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb62ec8: mov             x1, x0
    // 0xb62ecc: ldur            x0, [fp, #-0x58]
    // 0xb62ed0: StoreField: r1->field_f = r0
    //     0xb62ed0: stur            w0, [x1, #0xf]
    // 0xb62ed4: r16 = "\n"
    //     0xb62ed4: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xb62ed8: StoreField: r1->field_13 = r16
    //     0xb62ed8: stur            w16, [x1, #0x13]
    // 0xb62edc: str             x1, [SP]
    // 0xb62ee0: r0 = _interpolate()
    //     0xb62ee0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb62ee4: ldur            x1, [fp, #-0x10]
    // 0xb62ee8: stur            x0, [fp, #-0x58]
    // 0xb62eec: r0 = of()
    //     0xb62eec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb62ef0: LoadField: r1 = r0->field_87
    //     0xb62ef0: ldur            w1, [x0, #0x87]
    // 0xb62ef4: DecompressPointer r1
    //     0xb62ef4: add             x1, x1, HEAP, lsl #32
    // 0xb62ef8: LoadField: r0 = r1->field_7
    //     0xb62ef8: ldur            w0, [x1, #7]
    // 0xb62efc: DecompressPointer r0
    //     0xb62efc: add             x0, x0, HEAP, lsl #32
    // 0xb62f00: r16 = 32.000000
    //     0xb62f00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb62f04: ldr             x16, [x16, #0x848]
    // 0xb62f08: r30 = Instance_Color
    //     0xb62f08: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb62f0c: stp             lr, x16, [SP]
    // 0xb62f10: mov             x1, x0
    // 0xb62f14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb62f14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb62f18: ldr             x4, [x4, #0xaa0]
    // 0xb62f1c: r0 = copyWith()
    //     0xb62f1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb62f20: ldur            x1, [fp, #-0x10]
    // 0xb62f24: stur            x0, [fp, #-0x10]
    // 0xb62f28: r0 = of()
    //     0xb62f28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb62f2c: LoadField: r1 = r0->field_87
    //     0xb62f2c: ldur            w1, [x0, #0x87]
    // 0xb62f30: DecompressPointer r1
    //     0xb62f30: add             x1, x1, HEAP, lsl #32
    // 0xb62f34: LoadField: r0 = r1->field_2b
    //     0xb62f34: ldur            w0, [x1, #0x2b]
    // 0xb62f38: DecompressPointer r0
    //     0xb62f38: add             x0, x0, HEAP, lsl #32
    // 0xb62f3c: r16 = Instance_Color
    //     0xb62f3c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb62f40: r30 = 16.000000
    //     0xb62f40: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb62f44: ldr             lr, [lr, #0x188]
    // 0xb62f48: stp             lr, x16, [SP]
    // 0xb62f4c: mov             x1, x0
    // 0xb62f50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb62f50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb62f54: ldr             x4, [x4, #0x9b8]
    // 0xb62f58: r0 = copyWith()
    //     0xb62f58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb62f5c: stur            x0, [fp, #-0x68]
    // 0xb62f60: r0 = TextSpan()
    //     0xb62f60: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb62f64: mov             x3, x0
    // 0xb62f68: r0 = "OFF"
    //     0xb62f68: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xb62f6c: ldr             x0, [x0, #0x348]
    // 0xb62f70: stur            x3, [fp, #-0x80]
    // 0xb62f74: StoreField: r3->field_b = r0
    //     0xb62f74: stur            w0, [x3, #0xb]
    // 0xb62f78: r0 = Instance__DeferringMouseCursor
    //     0xb62f78: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb62f7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb62f7c: stur            w0, [x3, #0x17]
    // 0xb62f80: ldur            x1, [fp, #-0x68]
    // 0xb62f84: StoreField: r3->field_7 = r1
    //     0xb62f84: stur            w1, [x3, #7]
    // 0xb62f88: r1 = Null
    //     0xb62f88: mov             x1, NULL
    // 0xb62f8c: r2 = 2
    //     0xb62f8c: movz            x2, #0x2
    // 0xb62f90: r0 = AllocateArray()
    //     0xb62f90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb62f94: mov             x2, x0
    // 0xb62f98: ldur            x0, [fp, #-0x80]
    // 0xb62f9c: stur            x2, [fp, #-0x68]
    // 0xb62fa0: StoreField: r2->field_f = r0
    //     0xb62fa0: stur            w0, [x2, #0xf]
    // 0xb62fa4: r1 = <InlineSpan>
    //     0xb62fa4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb62fa8: ldr             x1, [x1, #0xe40]
    // 0xb62fac: r0 = AllocateGrowableArray()
    //     0xb62fac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb62fb0: mov             x1, x0
    // 0xb62fb4: ldur            x0, [fp, #-0x68]
    // 0xb62fb8: stur            x1, [fp, #-0x80]
    // 0xb62fbc: StoreField: r1->field_f = r0
    //     0xb62fbc: stur            w0, [x1, #0xf]
    // 0xb62fc0: r0 = 2
    //     0xb62fc0: movz            x0, #0x2
    // 0xb62fc4: StoreField: r1->field_b = r0
    //     0xb62fc4: stur            w0, [x1, #0xb]
    // 0xb62fc8: r0 = TextSpan()
    //     0xb62fc8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb62fcc: mov             x1, x0
    // 0xb62fd0: ldur            x0, [fp, #-0x58]
    // 0xb62fd4: stur            x1, [fp, #-0x68]
    // 0xb62fd8: StoreField: r1->field_b = r0
    //     0xb62fd8: stur            w0, [x1, #0xb]
    // 0xb62fdc: ldur            x0, [fp, #-0x80]
    // 0xb62fe0: StoreField: r1->field_f = r0
    //     0xb62fe0: stur            w0, [x1, #0xf]
    // 0xb62fe4: r0 = Instance__DeferringMouseCursor
    //     0xb62fe4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb62fe8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb62fe8: stur            w0, [x1, #0x17]
    // 0xb62fec: ldur            x0, [fp, #-0x10]
    // 0xb62ff0: StoreField: r1->field_7 = r0
    //     0xb62ff0: stur            w0, [x1, #7]
    // 0xb62ff4: r0 = RichText()
    //     0xb62ff4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb62ff8: stur            x0, [fp, #-0x10]
    // 0xb62ffc: r16 = Instance_TextAlign
    //     0xb62ffc: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb63000: str             x16, [SP]
    // 0xb63004: mov             x1, x0
    // 0xb63008: ldur            x2, [fp, #-0x68]
    // 0xb6300c: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xb6300c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xb63010: ldr             x4, [x4, #0x350]
    // 0xb63014: r0 = RichText()
    //     0xb63014: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb63018: r1 = Null
    //     0xb63018: mov             x1, NULL
    // 0xb6301c: r2 = 6
    //     0xb6301c: movz            x2, #0x6
    // 0xb63020: r0 = AllocateArray()
    //     0xb63020: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb63024: mov             x2, x0
    // 0xb63028: ldur            x0, [fp, #-0x50]
    // 0xb6302c: stur            x2, [fp, #-0x58]
    // 0xb63030: StoreField: r2->field_f = r0
    //     0xb63030: stur            w0, [x2, #0xf]
    // 0xb63034: r16 = Instance_VerticalDivider
    //     0xb63034: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0xb63038: ldr             x16, [x16, #0x760]
    // 0xb6303c: StoreField: r2->field_13 = r16
    //     0xb6303c: stur            w16, [x2, #0x13]
    // 0xb63040: ldur            x0, [fp, #-0x10]
    // 0xb63044: ArrayStore: r2[0] = r0  ; List_4
    //     0xb63044: stur            w0, [x2, #0x17]
    // 0xb63048: r1 = <Widget>
    //     0xb63048: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6304c: r0 = AllocateGrowableArray()
    //     0xb6304c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb63050: mov             x1, x0
    // 0xb63054: ldur            x0, [fp, #-0x58]
    // 0xb63058: stur            x1, [fp, #-0x10]
    // 0xb6305c: StoreField: r1->field_f = r0
    //     0xb6305c: stur            w0, [x1, #0xf]
    // 0xb63060: r0 = 6
    //     0xb63060: movz            x0, #0x6
    // 0xb63064: StoreField: r1->field_b = r0
    //     0xb63064: stur            w0, [x1, #0xb]
    // 0xb63068: r0 = Row()
    //     0xb63068: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6306c: mov             x1, x0
    // 0xb63070: r0 = Instance_Axis
    //     0xb63070: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb63074: stur            x1, [fp, #-0x50]
    // 0xb63078: StoreField: r1->field_f = r0
    //     0xb63078: stur            w0, [x1, #0xf]
    // 0xb6307c: r0 = Instance_MainAxisAlignment
    //     0xb6307c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb63080: ldr             x0, [x0, #0xa8]
    // 0xb63084: StoreField: r1->field_13 = r0
    //     0xb63084: stur            w0, [x1, #0x13]
    // 0xb63088: r0 = Instance_MainAxisSize
    //     0xb63088: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6308c: ldr             x0, [x0, #0xa10]
    // 0xb63090: ArrayStore: r1[0] = r0  ; List_4
    //     0xb63090: stur            w0, [x1, #0x17]
    // 0xb63094: r2 = Instance_CrossAxisAlignment
    //     0xb63094: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb63098: ldr             x2, [x2, #0xa18]
    // 0xb6309c: StoreField: r1->field_1b = r2
    //     0xb6309c: stur            w2, [x1, #0x1b]
    // 0xb630a0: r2 = Instance_VerticalDirection
    //     0xb630a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb630a4: ldr             x2, [x2, #0xa20]
    // 0xb630a8: StoreField: r1->field_23 = r2
    //     0xb630a8: stur            w2, [x1, #0x23]
    // 0xb630ac: r3 = Instance_Clip
    //     0xb630ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb630b0: ldr             x3, [x3, #0x38]
    // 0xb630b4: StoreField: r1->field_2b = r3
    //     0xb630b4: stur            w3, [x1, #0x2b]
    // 0xb630b8: StoreField: r1->field_2f = rZR
    //     0xb630b8: stur            xzr, [x1, #0x2f]
    // 0xb630bc: ldur            x4, [fp, #-0x10]
    // 0xb630c0: StoreField: r1->field_b = r4
    //     0xb630c0: stur            w4, [x1, #0xb]
    // 0xb630c4: r0 = Padding()
    //     0xb630c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb630c8: mov             x1, x0
    // 0xb630cc: r0 = Instance_EdgeInsets
    //     0xb630cc: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0xb630d0: ldr             x0, [x0, #0x358]
    // 0xb630d4: stur            x1, [fp, #-0x10]
    // 0xb630d8: StoreField: r1->field_f = r0
    //     0xb630d8: stur            w0, [x1, #0xf]
    // 0xb630dc: ldur            x0, [fp, #-0x50]
    // 0xb630e0: StoreField: r1->field_b = r0
    //     0xb630e0: stur            w0, [x1, #0xb]
    // 0xb630e4: r0 = IntrinsicHeight()
    //     0xb630e4: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb630e8: mov             x1, x0
    // 0xb630ec: ldur            x0, [fp, #-0x10]
    // 0xb630f0: stur            x1, [fp, #-0x50]
    // 0xb630f4: StoreField: r1->field_b = r0
    //     0xb630f4: stur            w0, [x1, #0xb]
    // 0xb630f8: r0 = Container()
    //     0xb630f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb630fc: stur            x0, [fp, #-0x10]
    // 0xb63100: r16 = 100.000000
    //     0xb63100: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb63104: ldur            lr, [fp, #-0x48]
    // 0xb63108: stp             lr, x16, [SP, #8]
    // 0xb6310c: ldur            x16, [fp, #-0x50]
    // 0xb63110: str             x16, [SP]
    // 0xb63114: mov             x1, x0
    // 0xb63118: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb63118: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb6311c: ldr             x4, [x4, #0xc78]
    // 0xb63120: r0 = Container()
    //     0xb63120: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb63124: r1 = <Path>
    //     0xb63124: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb63128: ldr             x1, [x1, #0xd30]
    // 0xb6312c: r0 = MovieTicketClipper()
    //     0xb6312c: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb63130: stur            x0, [fp, #-0x48]
    // 0xb63134: r0 = ClipPath()
    //     0xb63134: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb63138: mov             x1, x0
    // 0xb6313c: ldur            x0, [fp, #-0x48]
    // 0xb63140: stur            x1, [fp, #-0x50]
    // 0xb63144: StoreField: r1->field_f = r0
    //     0xb63144: stur            w0, [x1, #0xf]
    // 0xb63148: r0 = Instance_Clip
    //     0xb63148: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb6314c: ldr             x0, [x0, #0x138]
    // 0xb63150: StoreField: r1->field_13 = r0
    //     0xb63150: stur            w0, [x1, #0x13]
    // 0xb63154: ldur            x0, [fp, #-0x10]
    // 0xb63158: StoreField: r1->field_b = r0
    //     0xb63158: stur            w0, [x1, #0xb]
    // 0xb6315c: r0 = Visibility()
    //     0xb6315c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb63160: mov             x2, x0
    // 0xb63164: ldur            x0, [fp, #-0x50]
    // 0xb63168: stur            x2, [fp, #-0x10]
    // 0xb6316c: StoreField: r2->field_b = r0
    //     0xb6316c: stur            w0, [x2, #0xb]
    // 0xb63170: r0 = Instance_SizedBox
    //     0xb63170: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb63174: StoreField: r2->field_f = r0
    //     0xb63174: stur            w0, [x2, #0xf]
    // 0xb63178: ldur            x0, [fp, #-0x40]
    // 0xb6317c: StoreField: r2->field_13 = r0
    //     0xb6317c: stur            w0, [x2, #0x13]
    // 0xb63180: r0 = false
    //     0xb63180: add             x0, NULL, #0x30  ; false
    // 0xb63184: ArrayStore: r2[0] = r0  ; List_4
    //     0xb63184: stur            w0, [x2, #0x17]
    // 0xb63188: StoreField: r2->field_1b = r0
    //     0xb63188: stur            w0, [x2, #0x1b]
    // 0xb6318c: StoreField: r2->field_1f = r0
    //     0xb6318c: stur            w0, [x2, #0x1f]
    // 0xb63190: StoreField: r2->field_23 = r0
    //     0xb63190: stur            w0, [x2, #0x23]
    // 0xb63194: StoreField: r2->field_27 = r0
    //     0xb63194: stur            w0, [x2, #0x27]
    // 0xb63198: StoreField: r2->field_2b = r0
    //     0xb63198: stur            w0, [x2, #0x2b]
    // 0xb6319c: ldur            x0, [fp, #-0x38]
    // 0xb631a0: LoadField: r1 = r0->field_b
    //     0xb631a0: ldur            w1, [x0, #0xb]
    // 0xb631a4: LoadField: r3 = r0->field_f
    //     0xb631a4: ldur            w3, [x0, #0xf]
    // 0xb631a8: DecompressPointer r3
    //     0xb631a8: add             x3, x3, HEAP, lsl #32
    // 0xb631ac: LoadField: r4 = r3->field_b
    //     0xb631ac: ldur            w4, [x3, #0xb]
    // 0xb631b0: r3 = LoadInt32Instr(r1)
    //     0xb631b0: sbfx            x3, x1, #1, #0x1f
    // 0xb631b4: stur            x3, [fp, #-0x60]
    // 0xb631b8: r1 = LoadInt32Instr(r4)
    //     0xb631b8: sbfx            x1, x4, #1, #0x1f
    // 0xb631bc: cmp             x3, x1
    // 0xb631c0: b.ne            #0xb631cc
    // 0xb631c4: mov             x1, x0
    // 0xb631c8: r0 = _growToNextCapacity()
    //     0xb631c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb631cc: ldur            x4, [fp, #-8]
    // 0xb631d0: ldur            x2, [fp, #-0x38]
    // 0xb631d4: ldur            x3, [fp, #-0x60]
    // 0xb631d8: add             x0, x3, #1
    // 0xb631dc: lsl             x1, x0, #1
    // 0xb631e0: StoreField: r2->field_b = r1
    //     0xb631e0: stur            w1, [x2, #0xb]
    // 0xb631e4: LoadField: r1 = r2->field_f
    //     0xb631e4: ldur            w1, [x2, #0xf]
    // 0xb631e8: DecompressPointer r1
    //     0xb631e8: add             x1, x1, HEAP, lsl #32
    // 0xb631ec: ldur            x0, [fp, #-0x10]
    // 0xb631f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb631f0: add             x25, x1, x3, lsl #2
    //     0xb631f4: add             x25, x25, #0xf
    //     0xb631f8: str             w0, [x25]
    //     0xb631fc: tbz             w0, #0, #0xb63218
    //     0xb63200: ldurb           w16, [x1, #-1]
    //     0xb63204: ldurb           w17, [x0, #-1]
    //     0xb63208: and             x16, x17, x16, lsr #2
    //     0xb6320c: tst             x16, HEAP, lsr #32
    //     0xb63210: b.eq            #0xb63218
    //     0xb63214: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb63218: LoadField: r0 = r4->field_b
    //     0xb63218: ldur            w0, [x4, #0xb]
    // 0xb6321c: DecompressPointer r0
    //     0xb6321c: add             x0, x0, HEAP, lsl #32
    // 0xb63220: cmp             w0, NULL
    // 0xb63224: b.eq            #0xb6342c
    // 0xb63228: LoadField: r1 = r0->field_3f
    //     0xb63228: ldur            w1, [x0, #0x3f]
    // 0xb6322c: DecompressPointer r1
    //     0xb6322c: add             x1, x1, HEAP, lsl #32
    // 0xb63230: r0 = LoadClassIdInstr(r1)
    //     0xb63230: ldur            x0, [x1, #-1]
    //     0xb63234: ubfx            x0, x0, #0xc, #0x14
    // 0xb63238: r16 = "search_page"
    //     0xb63238: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb6323c: ldr             x16, [x16, #0xe58]
    // 0xb63240: stp             x16, x1, [SP]
    // 0xb63244: mov             lr, x0
    // 0xb63248: ldr             lr, [x21, lr, lsl #3]
    // 0xb6324c: blr             lr
    // 0xb63250: tbnz            w0, #4, #0xb63260
    // 0xb63254: d0 = 0.740741
    //     0xb63254: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d68] IMM: double(0.7407407407407407) from 0x3fe7b425ed097b42
    //     0xb63258: ldr             d0, [x17, #0xd68]
    // 0xb6325c: b               #0xb63268
    // 0xb63260: d0 = 0.666667
    //     0xb63260: add             x17, PP, #0x50, lsl #12  ; [pp+0x50e98] IMM: double(0.6666666666666666) from 0x3fe5555555555555
    //     0xb63264: ldr             d0, [x17, #0xe98]
    // 0xb63268: ldur            x0, [fp, #-8]
    // 0xb6326c: ldur            x1, [fp, #-0x38]
    // 0xb63270: stur            d0, [fp, #-0x88]
    // 0xb63274: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xb63274: bl              #0xa4d630  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xb63278: mov             x1, x0
    // 0xb6327c: r0 = 2
    //     0xb6327c: movz            x0, #0x2
    // 0xb63280: stur            x1, [fp, #-0x10]
    // 0xb63284: StoreField: r1->field_7 = r0
    //     0xb63284: stur            x0, [x1, #7]
    // 0xb63288: d0 = 24.000000
    //     0xb63288: fmov            d0, #24.00000000
    // 0xb6328c: StoreField: r1->field_f = d0
    //     0xb6328c: stur            d0, [x1, #0xf]
    // 0xb63290: d0 = 8.000000
    //     0xb63290: fmov            d0, #8.00000000
    // 0xb63294: ArrayStore: r1[0] = d0  ; List_8
    //     0xb63294: stur            d0, [x1, #0x17]
    // 0xb63298: ldur            d0, [fp, #-0x88]
    // 0xb6329c: StoreField: r1->field_1f = d0
    //     0xb6329c: stur            d0, [x1, #0x1f]
    // 0xb632a0: ldur            x0, [fp, #-8]
    // 0xb632a4: LoadField: r2 = r0->field_b
    //     0xb632a4: ldur            w2, [x0, #0xb]
    // 0xb632a8: DecompressPointer r2
    //     0xb632a8: add             x2, x2, HEAP, lsl #32
    // 0xb632ac: cmp             w2, NULL
    // 0xb632b0: b.eq            #0xb63430
    // 0xb632b4: LoadField: r0 = r2->field_b
    //     0xb632b4: ldur            w0, [x2, #0xb]
    // 0xb632b8: DecompressPointer r0
    //     0xb632b8: add             x0, x0, HEAP, lsl #32
    // 0xb632bc: r2 = LoadClassIdInstr(r0)
    //     0xb632bc: ldur            x2, [x0, #-1]
    //     0xb632c0: ubfx            x2, x2, #0xc, #0x14
    // 0xb632c4: str             x0, [SP]
    // 0xb632c8: mov             x0, x2
    // 0xb632cc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb632cc: movz            x17, #0xc898
    //     0xb632d0: add             lr, x0, x17
    //     0xb632d4: ldr             lr, [x21, lr, lsl #3]
    //     0xb632d8: blr             lr
    // 0xb632dc: ldur            x2, [fp, #-0x20]
    // 0xb632e0: r1 = Function '<anonymous closure>':.
    //     0xb632e0: add             x1, PP, #0x56, lsl #12  ; [pp+0x562c8] AnonymousClosure: (0xb63434), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb632e4: ldr             x1, [x1, #0x2c8]
    // 0xb632e8: stur            x0, [fp, #-8]
    // 0xb632ec: r0 = AllocateClosure()
    //     0xb632ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb632f0: stur            x0, [fp, #-0x20]
    // 0xb632f4: r0 = GridView()
    //     0xb632f4: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xb632f8: mov             x1, x0
    // 0xb632fc: ldur            x2, [fp, #-0x10]
    // 0xb63300: ldur            x3, [fp, #-0x20]
    // 0xb63304: ldur            x5, [fp, #-8]
    // 0xb63308: stur            x0, [fp, #-8]
    // 0xb6330c: r0 = GridView.builder()
    //     0xb6330c: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xb63310: ldur            x0, [fp, #-0x38]
    // 0xb63314: LoadField: r1 = r0->field_b
    //     0xb63314: ldur            w1, [x0, #0xb]
    // 0xb63318: LoadField: r2 = r0->field_f
    //     0xb63318: ldur            w2, [x0, #0xf]
    // 0xb6331c: DecompressPointer r2
    //     0xb6331c: add             x2, x2, HEAP, lsl #32
    // 0xb63320: LoadField: r3 = r2->field_b
    //     0xb63320: ldur            w3, [x2, #0xb]
    // 0xb63324: r2 = LoadInt32Instr(r1)
    //     0xb63324: sbfx            x2, x1, #1, #0x1f
    // 0xb63328: stur            x2, [fp, #-0x60]
    // 0xb6332c: r1 = LoadInt32Instr(r3)
    //     0xb6332c: sbfx            x1, x3, #1, #0x1f
    // 0xb63330: cmp             x2, x1
    // 0xb63334: b.ne            #0xb63340
    // 0xb63338: mov             x1, x0
    // 0xb6333c: r0 = _growToNextCapacity()
    //     0xb6333c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb63340: ldur            x4, [fp, #-0x30]
    // 0xb63344: ldur            x5, [fp, #-0x28]
    // 0xb63348: ldur            x2, [fp, #-0x38]
    // 0xb6334c: ldur            x6, [fp, #-0x18]
    // 0xb63350: ldur            x3, [fp, #-0x60]
    // 0xb63354: add             x0, x3, #1
    // 0xb63358: lsl             x1, x0, #1
    // 0xb6335c: StoreField: r2->field_b = r1
    //     0xb6335c: stur            w1, [x2, #0xb]
    // 0xb63360: LoadField: r1 = r2->field_f
    //     0xb63360: ldur            w1, [x2, #0xf]
    // 0xb63364: DecompressPointer r1
    //     0xb63364: add             x1, x1, HEAP, lsl #32
    // 0xb63368: ldur            x0, [fp, #-8]
    // 0xb6336c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6336c: add             x25, x1, x3, lsl #2
    //     0xb63370: add             x25, x25, #0xf
    //     0xb63374: str             w0, [x25]
    //     0xb63378: tbz             w0, #0, #0xb63394
    //     0xb6337c: ldurb           w16, [x1, #-1]
    //     0xb63380: ldurb           w17, [x0, #-1]
    //     0xb63384: and             x16, x17, x16, lsr #2
    //     0xb63388: tst             x16, HEAP, lsr #32
    //     0xb6338c: b.eq            #0xb63394
    //     0xb63390: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb63394: r0 = Column()
    //     0xb63394: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb63398: mov             x1, x0
    // 0xb6339c: r0 = Instance_Axis
    //     0xb6339c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb633a0: stur            x1, [fp, #-8]
    // 0xb633a4: StoreField: r1->field_f = r0
    //     0xb633a4: stur            w0, [x1, #0xf]
    // 0xb633a8: ldur            x0, [fp, #-0x28]
    // 0xb633ac: StoreField: r1->field_13 = r0
    //     0xb633ac: stur            w0, [x1, #0x13]
    // 0xb633b0: r0 = Instance_MainAxisSize
    //     0xb633b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb633b4: ldr             x0, [x0, #0xa10]
    // 0xb633b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb633b8: stur            w0, [x1, #0x17]
    // 0xb633bc: ldur            x0, [fp, #-0x18]
    // 0xb633c0: StoreField: r1->field_1b = r0
    //     0xb633c0: stur            w0, [x1, #0x1b]
    // 0xb633c4: r0 = Instance_VerticalDirection
    //     0xb633c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb633c8: ldr             x0, [x0, #0xa20]
    // 0xb633cc: StoreField: r1->field_23 = r0
    //     0xb633cc: stur            w0, [x1, #0x23]
    // 0xb633d0: r0 = Instance_Clip
    //     0xb633d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb633d4: ldr             x0, [x0, #0x38]
    // 0xb633d8: StoreField: r1->field_2b = r0
    //     0xb633d8: stur            w0, [x1, #0x2b]
    // 0xb633dc: StoreField: r1->field_2f = rZR
    //     0xb633dc: stur            xzr, [x1, #0x2f]
    // 0xb633e0: ldur            x0, [fp, #-0x38]
    // 0xb633e4: StoreField: r1->field_b = r0
    //     0xb633e4: stur            w0, [x1, #0xb]
    // 0xb633e8: r0 = Padding()
    //     0xb633e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb633ec: ldur            x1, [fp, #-0x30]
    // 0xb633f0: StoreField: r0->field_f = r1
    //     0xb633f0: stur            w1, [x0, #0xf]
    // 0xb633f4: ldur            x1, [fp, #-8]
    // 0xb633f8: StoreField: r0->field_b = r1
    //     0xb633f8: stur            w1, [x0, #0xb]
    // 0xb633fc: LeaveFrame
    //     0xb633fc: mov             SP, fp
    //     0xb63400: ldp             fp, lr, [SP], #0x10
    // 0xb63404: ret
    //     0xb63404: ret             
    // 0xb63408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb63408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6340c: b               #0xb62230
    // 0xb63410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63410: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb63414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63414: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb63418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63418: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6341c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6341c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb63420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb63424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb63428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63428: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6342c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6342c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb63430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb63430: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb63434, size: 0x15bc
    // 0xb63434: EnterFrame
    //     0xb63434: stp             fp, lr, [SP, #-0x10]!
    //     0xb63438: mov             fp, SP
    // 0xb6343c: AllocStack(0x88)
    //     0xb6343c: sub             SP, SP, #0x88
    // 0xb63440: SetupParameters()
    //     0xb63440: ldr             x0, [fp, #0x20]
    //     0xb63444: ldur            w1, [x0, #0x17]
    //     0xb63448: add             x1, x1, HEAP, lsl #32
    //     0xb6344c: stur            x1, [fp, #-8]
    // 0xb63450: CheckStackOverflow
    //     0xb63450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb63454: cmp             SP, x16
    //     0xb63458: b.ls            #0xb6498c
    // 0xb6345c: r1 = 1
    //     0xb6345c: movz            x1, #0x1
    // 0xb63460: r0 = AllocateContext()
    //     0xb63460: bl              #0x16f6108  ; AllocateContextStub
    // 0xb63464: mov             x2, x0
    // 0xb63468: ldur            x1, [fp, #-8]
    // 0xb6346c: stur            x2, [fp, #-0x10]
    // 0xb63470: StoreField: r2->field_b = r1
    //     0xb63470: stur            w1, [x2, #0xb]
    // 0xb63474: LoadField: r0 = r1->field_f
    //     0xb63474: ldur            w0, [x1, #0xf]
    // 0xb63478: DecompressPointer r0
    //     0xb63478: add             x0, x0, HEAP, lsl #32
    // 0xb6347c: LoadField: r3 = r0->field_b
    //     0xb6347c: ldur            w3, [x0, #0xb]
    // 0xb63480: DecompressPointer r3
    //     0xb63480: add             x3, x3, HEAP, lsl #32
    // 0xb63484: cmp             w3, NULL
    // 0xb63488: b.eq            #0xb64994
    // 0xb6348c: LoadField: r0 = r3->field_b
    //     0xb6348c: ldur            w0, [x3, #0xb]
    // 0xb63490: DecompressPointer r0
    //     0xb63490: add             x0, x0, HEAP, lsl #32
    // 0xb63494: r3 = LoadClassIdInstr(r0)
    //     0xb63494: ldur            x3, [x0, #-1]
    //     0xb63498: ubfx            x3, x3, #0xc, #0x14
    // 0xb6349c: ldr             x16, [fp, #0x10]
    // 0xb634a0: stp             x16, x0, [SP]
    // 0xb634a4: mov             x0, x3
    // 0xb634a8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb634a8: sub             lr, x0, #0xb7
    //     0xb634ac: ldr             lr, [x21, lr, lsl #3]
    //     0xb634b0: blr             lr
    // 0xb634b4: mov             x4, x0
    // 0xb634b8: ldur            x3, [fp, #-0x10]
    // 0xb634bc: stur            x4, [fp, #-0x18]
    // 0xb634c0: StoreField: r3->field_f = r0
    //     0xb634c0: stur            w0, [x3, #0xf]
    //     0xb634c4: ldurb           w16, [x3, #-1]
    //     0xb634c8: ldurb           w17, [x0, #-1]
    //     0xb634cc: and             x16, x17, x16, lsr #2
    //     0xb634d0: tst             x16, HEAP, lsr #32
    //     0xb634d4: b.eq            #0xb634dc
    //     0xb634d8: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb634dc: ldur            x0, [fp, #-8]
    // 0xb634e0: LoadField: r1 = r0->field_f
    //     0xb634e0: ldur            w1, [x0, #0xf]
    // 0xb634e4: DecompressPointer r1
    //     0xb634e4: add             x1, x1, HEAP, lsl #32
    // 0xb634e8: mov             x2, x4
    // 0xb634ec: r0 = _shouldShowRating()
    //     0xb634ec: bl              #0xb649f0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::_shouldShowRating
    // 0xb634f0: stur            x0, [fp, #-0x20]
    // 0xb634f4: ldr             x16, [fp, #0x10]
    // 0xb634f8: str             x16, [SP]
    // 0xb634fc: r0 = _interpolateSingle()
    //     0xb634fc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb63500: r1 = <String>
    //     0xb63500: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb63504: stur            x0, [fp, #-0x28]
    // 0xb63508: r0 = ValueKey()
    //     0xb63508: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xb6350c: mov             x2, x0
    // 0xb63510: ldur            x0, [fp, #-0x28]
    // 0xb63514: stur            x2, [fp, #-0x30]
    // 0xb63518: StoreField: r2->field_b = r0
    //     0xb63518: stur            w0, [x2, #0xb]
    // 0xb6351c: ldr             x1, [fp, #0x18]
    // 0xb63520: r0 = of()
    //     0xb63520: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63524: LoadField: r1 = r0->field_5b
    //     0xb63524: ldur            w1, [x0, #0x5b]
    // 0xb63528: DecompressPointer r1
    //     0xb63528: add             x1, x1, HEAP, lsl #32
    // 0xb6352c: r0 = LoadClassIdInstr(r1)
    //     0xb6352c: ldur            x0, [x1, #-1]
    //     0xb63530: ubfx            x0, x0, #0xc, #0x14
    // 0xb63534: d0 = 0.030000
    //     0xb63534: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb63538: ldr             d0, [x17, #0x238]
    // 0xb6353c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb6353c: sub             lr, x0, #0xffa
    //     0xb63540: ldr             lr, [x21, lr, lsl #3]
    //     0xb63544: blr             lr
    // 0xb63548: stur            x0, [fp, #-0x28]
    // 0xb6354c: r0 = Radius()
    //     0xb6354c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb63550: d0 = 12.000000
    //     0xb63550: fmov            d0, #12.00000000
    // 0xb63554: stur            x0, [fp, #-0x38]
    // 0xb63558: StoreField: r0->field_7 = d0
    //     0xb63558: stur            d0, [x0, #7]
    // 0xb6355c: StoreField: r0->field_f = d0
    //     0xb6355c: stur            d0, [x0, #0xf]
    // 0xb63560: r0 = BorderRadius()
    //     0xb63560: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb63564: mov             x1, x0
    // 0xb63568: ldur            x0, [fp, #-0x38]
    // 0xb6356c: stur            x1, [fp, #-0x40]
    // 0xb63570: StoreField: r1->field_7 = r0
    //     0xb63570: stur            w0, [x1, #7]
    // 0xb63574: StoreField: r1->field_b = r0
    //     0xb63574: stur            w0, [x1, #0xb]
    // 0xb63578: StoreField: r1->field_f = r0
    //     0xb63578: stur            w0, [x1, #0xf]
    // 0xb6357c: StoreField: r1->field_13 = r0
    //     0xb6357c: stur            w0, [x1, #0x13]
    // 0xb63580: r0 = RoundedRectangleBorder()
    //     0xb63580: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb63584: mov             x3, x0
    // 0xb63588: ldur            x0, [fp, #-0x40]
    // 0xb6358c: stur            x3, [fp, #-0x48]
    // 0xb63590: StoreField: r3->field_b = r0
    //     0xb63590: stur            w0, [x3, #0xb]
    // 0xb63594: r0 = Instance_BorderSide
    //     0xb63594: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb63598: ldr             x0, [x0, #0xe20]
    // 0xb6359c: StoreField: r3->field_7 = r0
    //     0xb6359c: stur            w0, [x3, #7]
    // 0xb635a0: ldur            x0, [fp, #-0x18]
    // 0xb635a4: cmp             w0, NULL
    // 0xb635a8: b.ne            #0xb635b4
    // 0xb635ac: r1 = Null
    //     0xb635ac: mov             x1, NULL
    // 0xb635b0: b               #0xb635d4
    // 0xb635b4: LoadField: r1 = r0->field_37
    //     0xb635b4: ldur            w1, [x0, #0x37]
    // 0xb635b8: DecompressPointer r1
    //     0xb635b8: add             x1, x1, HEAP, lsl #32
    // 0xb635bc: cmp             w1, NULL
    // 0xb635c0: b.ne            #0xb635cc
    // 0xb635c4: r1 = Null
    //     0xb635c4: mov             x1, NULL
    // 0xb635c8: b               #0xb635d4
    // 0xb635cc: LoadField: r2 = r1->field_b
    //     0xb635cc: ldur            w2, [x1, #0xb]
    // 0xb635d0: mov             x1, x2
    // 0xb635d4: cmp             w1, NULL
    // 0xb635d8: b.ne            #0xb635e4
    // 0xb635dc: r1 = 0
    //     0xb635dc: movz            x1, #0
    // 0xb635e0: b               #0xb635ec
    // 0xb635e4: r2 = LoadInt32Instr(r1)
    //     0xb635e4: sbfx            x2, x1, #1, #0x1f
    // 0xb635e8: mov             x1, x2
    // 0xb635ec: ldur            x5, [fp, #-8]
    // 0xb635f0: ldur            x4, [fp, #-0x28]
    // 0xb635f4: LoadField: r2 = r5->field_f
    //     0xb635f4: ldur            w2, [x5, #0xf]
    // 0xb635f8: DecompressPointer r2
    //     0xb635f8: add             x2, x2, HEAP, lsl #32
    // 0xb635fc: LoadField: r6 = r2->field_1b
    //     0xb635fc: ldur            w6, [x2, #0x1b]
    // 0xb63600: DecompressPointer r6
    //     0xb63600: add             x6, x6, HEAP, lsl #32
    // 0xb63604: r16 = Sentinel
    //     0xb63604: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb63608: cmp             w6, w16
    // 0xb6360c: b.eq            #0xb64998
    // 0xb63610: stur            x6, [fp, #-0x40]
    // 0xb63614: lsl             x7, x1, #1
    // 0xb63618: ldur            x2, [fp, #-0x10]
    // 0xb6361c: stur            x7, [fp, #-0x38]
    // 0xb63620: r1 = Function '<anonymous closure>':.
    //     0xb63620: add             x1, PP, #0x56, lsl #12  ; [pp+0x562d0] AnonymousClosure: (0xb66464), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb63624: ldr             x1, [x1, #0x2d0]
    // 0xb63628: r0 = AllocateClosure()
    //     0xb63628: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6362c: ldur            x2, [fp, #-0x10]
    // 0xb63630: r1 = Function '<anonymous closure>':.
    //     0xb63630: add             x1, PP, #0x56, lsl #12  ; [pp+0x562d8] AnonymousClosure: (0xb64f3c), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb63634: ldr             x1, [x1, #0x2d8]
    // 0xb63638: stur            x0, [fp, #-0x50]
    // 0xb6363c: r0 = AllocateClosure()
    //     0xb6363c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb63640: stur            x0, [fp, #-0x58]
    // 0xb63644: r0 = PageView()
    //     0xb63644: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb63648: stur            x0, [fp, #-0x60]
    // 0xb6364c: ldur            x16, [fp, #-0x40]
    // 0xb63650: str             x16, [SP]
    // 0xb63654: mov             x1, x0
    // 0xb63658: ldur            x2, [fp, #-0x58]
    // 0xb6365c: ldur            x3, [fp, #-0x38]
    // 0xb63660: ldur            x5, [fp, #-0x50]
    // 0xb63664: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xb63664: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xb63668: ldr             x4, [x4, #0xd60]
    // 0xb6366c: r0 = PageView.builder()
    //     0xb6366c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb63670: r0 = AspectRatio()
    //     0xb63670: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb63674: d0 = 1.000000
    //     0xb63674: fmov            d0, #1.00000000
    // 0xb63678: stur            x0, [fp, #-0x38]
    // 0xb6367c: StoreField: r0->field_f = d0
    //     0xb6367c: stur            d0, [x0, #0xf]
    // 0xb63680: ldur            x1, [fp, #-0x60]
    // 0xb63684: StoreField: r0->field_b = r1
    //     0xb63684: stur            w1, [x0, #0xb]
    // 0xb63688: r0 = Card()
    //     0xb63688: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb6368c: mov             x2, x0
    // 0xb63690: ldur            x0, [fp, #-0x28]
    // 0xb63694: stur            x2, [fp, #-0x40]
    // 0xb63698: StoreField: r2->field_b = r0
    //     0xb63698: stur            w0, [x2, #0xb]
    // 0xb6369c: r0 = 0.000000
    //     0xb6369c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb636a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb636a0: stur            w0, [x2, #0x17]
    // 0xb636a4: ldur            x0, [fp, #-0x48]
    // 0xb636a8: StoreField: r2->field_1b = r0
    //     0xb636a8: stur            w0, [x2, #0x1b]
    // 0xb636ac: r0 = true
    //     0xb636ac: add             x0, NULL, #0x20  ; true
    // 0xb636b0: StoreField: r2->field_1f = r0
    //     0xb636b0: stur            w0, [x2, #0x1f]
    // 0xb636b4: ldur            x1, [fp, #-0x38]
    // 0xb636b8: StoreField: r2->field_2f = r1
    //     0xb636b8: stur            w1, [x2, #0x2f]
    // 0xb636bc: StoreField: r2->field_2b = r0
    //     0xb636bc: stur            w0, [x2, #0x2b]
    // 0xb636c0: r1 = Instance__CardVariant
    //     0xb636c0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb636c4: ldr             x1, [x1, #0xa68]
    // 0xb636c8: StoreField: r2->field_33 = r1
    //     0xb636c8: stur            w1, [x2, #0x33]
    // 0xb636cc: r1 = <FlexParentData>
    //     0xb636cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb636d0: ldr             x1, [x1, #0xe00]
    // 0xb636d4: r0 = Expanded()
    //     0xb636d4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb636d8: mov             x2, x0
    // 0xb636dc: r1 = 1
    //     0xb636dc: movz            x1, #0x1
    // 0xb636e0: stur            x2, [fp, #-0x28]
    // 0xb636e4: StoreField: r2->field_13 = r1
    //     0xb636e4: stur            x1, [x2, #0x13]
    // 0xb636e8: r3 = Instance_FlexFit
    //     0xb636e8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb636ec: ldr             x3, [x3, #0xe08]
    // 0xb636f0: StoreField: r2->field_1b = r3
    //     0xb636f0: stur            w3, [x2, #0x1b]
    // 0xb636f4: ldur            x0, [fp, #-0x40]
    // 0xb636f8: StoreField: r2->field_b = r0
    //     0xb636f8: stur            w0, [x2, #0xb]
    // 0xb636fc: ldur            x4, [fp, #-8]
    // 0xb63700: LoadField: r0 = r4->field_f
    //     0xb63700: ldur            w0, [x4, #0xf]
    // 0xb63704: DecompressPointer r0
    //     0xb63704: add             x0, x0, HEAP, lsl #32
    // 0xb63708: LoadField: r5 = r0->field_b
    //     0xb63708: ldur            w5, [x0, #0xb]
    // 0xb6370c: DecompressPointer r5
    //     0xb6370c: add             x5, x5, HEAP, lsl #32
    // 0xb63710: cmp             w5, NULL
    // 0xb63714: b.eq            #0xb649a4
    // 0xb63718: LoadField: r0 = r5->field_3f
    //     0xb63718: ldur            w0, [x5, #0x3f]
    // 0xb6371c: DecompressPointer r0
    //     0xb6371c: add             x0, x0, HEAP, lsl #32
    // 0xb63720: r5 = LoadClassIdInstr(r0)
    //     0xb63720: ldur            x5, [x0, #-1]
    //     0xb63724: ubfx            x5, x5, #0xc, #0x14
    // 0xb63728: r16 = "search_page"
    //     0xb63728: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb6372c: ldr             x16, [x16, #0xe58]
    // 0xb63730: stp             x16, x0, [SP]
    // 0xb63734: mov             x0, x5
    // 0xb63738: mov             lr, x0
    // 0xb6373c: ldr             lr, [x21, lr, lsl #3]
    // 0xb63740: blr             lr
    // 0xb63744: tbnz            w0, #4, #0xb63754
    // 0xb63748: d0 = 140.000000
    //     0xb63748: add             x17, PP, #0x56, lsl #12  ; [pp+0x562e0] IMM: double(140) from 0x4061800000000000
    //     0xb6374c: ldr             d0, [x17, #0x2e0]
    // 0xb63750: b               #0xb63758
    // 0xb63754: d0 = 100.000000
    //     0xb63754: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb63758: ldur            x0, [fp, #-0x18]
    // 0xb6375c: stur            d0, [fp, #-0x70]
    // 0xb63760: cmp             w0, NULL
    // 0xb63764: b.ne            #0xb63770
    // 0xb63768: r1 = Null
    //     0xb63768: mov             x1, NULL
    // 0xb6376c: b               #0xb63794
    // 0xb63770: LoadField: r1 = r0->field_33
    //     0xb63770: ldur            w1, [x0, #0x33]
    // 0xb63774: DecompressPointer r1
    //     0xb63774: add             x1, x1, HEAP, lsl #32
    // 0xb63778: cmp             w1, NULL
    // 0xb6377c: b.ne            #0xb63788
    // 0xb63780: r1 = Null
    //     0xb63780: mov             x1, NULL
    // 0xb63784: b               #0xb63794
    // 0xb63788: LoadField: r2 = r1->field_7
    //     0xb63788: ldur            w2, [x1, #7]
    // 0xb6378c: DecompressPointer r2
    //     0xb6378c: add             x2, x2, HEAP, lsl #32
    // 0xb63790: mov             x1, x2
    // 0xb63794: cmp             w1, NULL
    // 0xb63798: b.ne            #0xb637a0
    // 0xb6379c: r1 = ""
    //     0xb6379c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb637a0: ldur            x2, [fp, #-0x20]
    // 0xb637a4: r0 = capitalizeFirstWord()
    //     0xb637a4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb637a8: ldr             x1, [fp, #0x18]
    // 0xb637ac: stur            x0, [fp, #-0x38]
    // 0xb637b0: r0 = of()
    //     0xb637b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb637b4: LoadField: r1 = r0->field_87
    //     0xb637b4: ldur            w1, [x0, #0x87]
    // 0xb637b8: DecompressPointer r1
    //     0xb637b8: add             x1, x1, HEAP, lsl #32
    // 0xb637bc: LoadField: r0 = r1->field_2b
    //     0xb637bc: ldur            w0, [x1, #0x2b]
    // 0xb637c0: DecompressPointer r0
    //     0xb637c0: add             x0, x0, HEAP, lsl #32
    // 0xb637c4: stur            x0, [fp, #-0x40]
    // 0xb637c8: r1 = Instance_Color
    //     0xb637c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb637cc: d0 = 0.700000
    //     0xb637cc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb637d0: ldr             d0, [x17, #0xf48]
    // 0xb637d4: r0 = withOpacity()
    //     0xb637d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb637d8: r16 = 12.000000
    //     0xb637d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb637dc: ldr             x16, [x16, #0x9e8]
    // 0xb637e0: stp             x0, x16, [SP]
    // 0xb637e4: ldur            x1, [fp, #-0x40]
    // 0xb637e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb637e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb637ec: ldr             x4, [x4, #0xaa0]
    // 0xb637f0: r0 = copyWith()
    //     0xb637f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb637f4: stur            x0, [fp, #-0x40]
    // 0xb637f8: r0 = Text()
    //     0xb637f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb637fc: mov             x1, x0
    // 0xb63800: ldur            x0, [fp, #-0x38]
    // 0xb63804: stur            x1, [fp, #-0x48]
    // 0xb63808: StoreField: r1->field_b = r0
    //     0xb63808: stur            w0, [x1, #0xb]
    // 0xb6380c: ldur            x0, [fp, #-0x40]
    // 0xb63810: StoreField: r1->field_13 = r0
    //     0xb63810: stur            w0, [x1, #0x13]
    // 0xb63814: r0 = Instance_TextOverflow
    //     0xb63814: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb63818: ldr             x0, [x0, #0xe10]
    // 0xb6381c: StoreField: r1->field_2b = r0
    //     0xb6381c: stur            w0, [x1, #0x2b]
    // 0xb63820: r2 = 4
    //     0xb63820: movz            x2, #0x4
    // 0xb63824: StoreField: r1->field_37 = r2
    //     0xb63824: stur            w2, [x1, #0x37]
    // 0xb63828: ldur            d0, [fp, #-0x70]
    // 0xb6382c: r0 = inline_Allocate_Double()
    //     0xb6382c: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xb63830: add             x0, x0, #0x10
    //     0xb63834: cmp             x3, x0
    //     0xb63838: b.ls            #0xb649a8
    //     0xb6383c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb63840: sub             x0, x0, #0xf
    //     0xb63844: movz            x3, #0xe15c
    //     0xb63848: movk            x3, #0x3, lsl #16
    //     0xb6384c: stur            x3, [x0, #-1]
    // 0xb63850: StoreField: r0->field_7 = d0
    //     0xb63850: stur            d0, [x0, #7]
    // 0xb63854: stur            x0, [fp, #-0x38]
    // 0xb63858: r0 = Container()
    //     0xb63858: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6385c: stur            x0, [fp, #-0x40]
    // 0xb63860: ldur            x16, [fp, #-0x38]
    // 0xb63864: r30 = Instance_EdgeInsets
    //     0xb63864: add             lr, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb63868: ldr             lr, [lr, #0x930]
    // 0xb6386c: stp             lr, x16, [SP, #8]
    // 0xb63870: ldur            x16, [fp, #-0x48]
    // 0xb63874: str             x16, [SP]
    // 0xb63878: mov             x1, x0
    // 0xb6387c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb6387c: add             x4, PP, #0x44, lsl #12  ; [pp+0x44260] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb63880: ldr             x4, [x4, #0x260]
    // 0xb63884: r0 = Container()
    //     0xb63884: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb63888: r1 = Null
    //     0xb63888: mov             x1, NULL
    // 0xb6388c: r2 = 2
    //     0xb6388c: movz            x2, #0x2
    // 0xb63890: r0 = AllocateArray()
    //     0xb63890: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb63894: mov             x2, x0
    // 0xb63898: ldur            x0, [fp, #-0x40]
    // 0xb6389c: stur            x2, [fp, #-0x38]
    // 0xb638a0: StoreField: r2->field_f = r0
    //     0xb638a0: stur            w0, [x2, #0xf]
    // 0xb638a4: r1 = <Widget>
    //     0xb638a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb638a8: r0 = AllocateGrowableArray()
    //     0xb638a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb638ac: mov             x1, x0
    // 0xb638b0: ldur            x0, [fp, #-0x38]
    // 0xb638b4: stur            x1, [fp, #-0x40]
    // 0xb638b8: StoreField: r1->field_f = r0
    //     0xb638b8: stur            w0, [x1, #0xf]
    // 0xb638bc: r2 = 2
    //     0xb638bc: movz            x2, #0x2
    // 0xb638c0: StoreField: r1->field_b = r2
    //     0xb638c0: stur            w2, [x1, #0xb]
    // 0xb638c4: ldur            x0, [fp, #-0x20]
    // 0xb638c8: tbnz            w0, #4, #0xb640f4
    // 0xb638cc: ldur            x3, [fp, #-0x18]
    // 0xb638d0: cmp             w3, NULL
    // 0xb638d4: b.ne            #0xb638e0
    // 0xb638d8: r0 = Null
    //     0xb638d8: mov             x0, NULL
    // 0xb638dc: b               #0xb63904
    // 0xb638e0: LoadField: r0 = r3->field_7b
    //     0xb638e0: ldur            w0, [x3, #0x7b]
    // 0xb638e4: DecompressPointer r0
    //     0xb638e4: add             x0, x0, HEAP, lsl #32
    // 0xb638e8: cmp             w0, NULL
    // 0xb638ec: b.ne            #0xb638f8
    // 0xb638f0: r0 = Null
    //     0xb638f0: mov             x0, NULL
    // 0xb638f4: b               #0xb63904
    // 0xb638f8: LoadField: r4 = r0->field_f
    //     0xb638f8: ldur            w4, [x0, #0xf]
    // 0xb638fc: DecompressPointer r4
    //     0xb638fc: add             x4, x4, HEAP, lsl #32
    // 0xb63900: mov             x0, x4
    // 0xb63904: r4 = LoadClassIdInstr(r0)
    //     0xb63904: ldur            x4, [x0, #-1]
    //     0xb63908: ubfx            x4, x4, #0xc, #0x14
    // 0xb6390c: r16 = "product_rating"
    //     0xb6390c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xb63910: ldr             x16, [x16, #0xf20]
    // 0xb63914: stp             x16, x0, [SP]
    // 0xb63918: mov             x0, x4
    // 0xb6391c: mov             lr, x0
    // 0xb63920: ldr             lr, [x21, lr, lsl #3]
    // 0xb63924: blr             lr
    // 0xb63928: tbnz            w0, #4, #0xb63de4
    // 0xb6392c: ldur            x0, [fp, #-0x18]
    // 0xb63930: cmp             w0, NULL
    // 0xb63934: b.ne            #0xb63940
    // 0xb63938: r1 = Null
    //     0xb63938: mov             x1, NULL
    // 0xb6393c: b               #0xb63964
    // 0xb63940: LoadField: r1 = r0->field_7b
    //     0xb63940: ldur            w1, [x0, #0x7b]
    // 0xb63944: DecompressPointer r1
    //     0xb63944: add             x1, x1, HEAP, lsl #32
    // 0xb63948: cmp             w1, NULL
    // 0xb6394c: b.ne            #0xb63958
    // 0xb63950: r1 = Null
    //     0xb63950: mov             x1, NULL
    // 0xb63954: b               #0xb63964
    // 0xb63958: LoadField: r2 = r1->field_7
    //     0xb63958: ldur            w2, [x1, #7]
    // 0xb6395c: DecompressPointer r2
    //     0xb6395c: add             x2, x2, HEAP, lsl #32
    // 0xb63960: mov             x1, x2
    // 0xb63964: cmp             w1, NULL
    // 0xb63968: b.ne            #0xb63974
    // 0xb6396c: d1 = 0.000000
    //     0xb6396c: eor             v1.16b, v1.16b, v1.16b
    // 0xb63970: b               #0xb6397c
    // 0xb63974: LoadField: d0 = r1->field_7
    //     0xb63974: ldur            d0, [x1, #7]
    // 0xb63978: mov             v1.16b, v0.16b
    // 0xb6397c: d0 = 4.000000
    //     0xb6397c: fmov            d0, #4.00000000
    // 0xb63980: fcmp            d1, d0
    // 0xb63984: b.lt            #0xb63994
    // 0xb63988: r1 = Instance_Color
    //     0xb63988: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb6398c: ldr             x1, [x1, #0x858]
    // 0xb63990: b               #0xb63a78
    // 0xb63994: cmp             w0, NULL
    // 0xb63998: b.ne            #0xb639a4
    // 0xb6399c: r1 = Null
    //     0xb6399c: mov             x1, NULL
    // 0xb639a0: b               #0xb639c8
    // 0xb639a4: LoadField: r1 = r0->field_7b
    //     0xb639a4: ldur            w1, [x0, #0x7b]
    // 0xb639a8: DecompressPointer r1
    //     0xb639a8: add             x1, x1, HEAP, lsl #32
    // 0xb639ac: cmp             w1, NULL
    // 0xb639b0: b.ne            #0xb639bc
    // 0xb639b4: r1 = Null
    //     0xb639b4: mov             x1, NULL
    // 0xb639b8: b               #0xb639c8
    // 0xb639bc: LoadField: r2 = r1->field_7
    //     0xb639bc: ldur            w2, [x1, #7]
    // 0xb639c0: DecompressPointer r2
    //     0xb639c0: add             x2, x2, HEAP, lsl #32
    // 0xb639c4: mov             x1, x2
    // 0xb639c8: cmp             w1, NULL
    // 0xb639cc: b.ne            #0xb639d8
    // 0xb639d0: d1 = 0.000000
    //     0xb639d0: eor             v1.16b, v1.16b, v1.16b
    // 0xb639d4: b               #0xb639e0
    // 0xb639d8: LoadField: d0 = r1->field_7
    //     0xb639d8: ldur            d0, [x1, #7]
    // 0xb639dc: mov             v1.16b, v0.16b
    // 0xb639e0: d0 = 3.500000
    //     0xb639e0: fmov            d0, #3.50000000
    // 0xb639e4: fcmp            d1, d0
    // 0xb639e8: b.lt            #0xb63a0c
    // 0xb639ec: r1 = Instance_Color
    //     0xb639ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb639f0: ldr             x1, [x1, #0x858]
    // 0xb639f4: d0 = 0.700000
    //     0xb639f4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb639f8: ldr             d0, [x17, #0xf48]
    // 0xb639fc: r0 = withOpacity()
    //     0xb639fc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb63a00: mov             x1, x0
    // 0xb63a04: ldur            x0, [fp, #-0x18]
    // 0xb63a08: b               #0xb63a78
    // 0xb63a0c: cmp             w0, NULL
    // 0xb63a10: b.ne            #0xb63a1c
    // 0xb63a14: r1 = Null
    //     0xb63a14: mov             x1, NULL
    // 0xb63a18: b               #0xb63a40
    // 0xb63a1c: LoadField: r1 = r0->field_7b
    //     0xb63a1c: ldur            w1, [x0, #0x7b]
    // 0xb63a20: DecompressPointer r1
    //     0xb63a20: add             x1, x1, HEAP, lsl #32
    // 0xb63a24: cmp             w1, NULL
    // 0xb63a28: b.ne            #0xb63a34
    // 0xb63a2c: r1 = Null
    //     0xb63a2c: mov             x1, NULL
    // 0xb63a30: b               #0xb63a40
    // 0xb63a34: LoadField: r2 = r1->field_7
    //     0xb63a34: ldur            w2, [x1, #7]
    // 0xb63a38: DecompressPointer r2
    //     0xb63a38: add             x2, x2, HEAP, lsl #32
    // 0xb63a3c: mov             x1, x2
    // 0xb63a40: cmp             w1, NULL
    // 0xb63a44: b.ne            #0xb63a50
    // 0xb63a48: d1 = 0.000000
    //     0xb63a48: eor             v1.16b, v1.16b, v1.16b
    // 0xb63a4c: b               #0xb63a58
    // 0xb63a50: LoadField: d0 = r1->field_7
    //     0xb63a50: ldur            d0, [x1, #7]
    // 0xb63a54: mov             v1.16b, v0.16b
    // 0xb63a58: d0 = 2.000000
    //     0xb63a58: fmov            d0, #2.00000000
    // 0xb63a5c: fcmp            d1, d0
    // 0xb63a60: b.lt            #0xb63a70
    // 0xb63a64: r1 = Instance_Color
    //     0xb63a64: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb63a68: ldr             x1, [x1, #0x860]
    // 0xb63a6c: b               #0xb63a78
    // 0xb63a70: r1 = Instance_Color
    //     0xb63a70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb63a74: ldr             x1, [x1, #0x50]
    // 0xb63a78: stur            x1, [fp, #-0x20]
    // 0xb63a7c: r0 = ColorFilter()
    //     0xb63a7c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb63a80: mov             x1, x0
    // 0xb63a84: ldur            x0, [fp, #-0x20]
    // 0xb63a88: stur            x1, [fp, #-0x38]
    // 0xb63a8c: StoreField: r1->field_7 = r0
    //     0xb63a8c: stur            w0, [x1, #7]
    // 0xb63a90: r0 = Instance_BlendMode
    //     0xb63a90: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb63a94: ldr             x0, [x0, #0xb30]
    // 0xb63a98: StoreField: r1->field_b = r0
    //     0xb63a98: stur            w0, [x1, #0xb]
    // 0xb63a9c: r0 = 1
    //     0xb63a9c: movz            x0, #0x1
    // 0xb63aa0: StoreField: r1->field_13 = r0
    //     0xb63aa0: stur            x0, [x1, #0x13]
    // 0xb63aa4: r0 = SvgPicture()
    //     0xb63aa4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb63aa8: stur            x0, [fp, #-0x20]
    // 0xb63aac: ldur            x16, [fp, #-0x38]
    // 0xb63ab0: str             x16, [SP]
    // 0xb63ab4: mov             x1, x0
    // 0xb63ab8: r2 = "assets/images/green_star.svg"
    //     0xb63ab8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb63abc: ldr             x2, [x2, #0x9a0]
    // 0xb63ac0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb63ac0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb63ac4: ldr             x4, [x4, #0xa38]
    // 0xb63ac8: r0 = SvgPicture.asset()
    //     0xb63ac8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb63acc: ldur            x0, [fp, #-0x18]
    // 0xb63ad0: cmp             w0, NULL
    // 0xb63ad4: b.ne            #0xb63ae0
    // 0xb63ad8: r0 = Null
    //     0xb63ad8: mov             x0, NULL
    // 0xb63adc: b               #0xb63b18
    // 0xb63ae0: LoadField: r1 = r0->field_7b
    //     0xb63ae0: ldur            w1, [x0, #0x7b]
    // 0xb63ae4: DecompressPointer r1
    //     0xb63ae4: add             x1, x1, HEAP, lsl #32
    // 0xb63ae8: cmp             w1, NULL
    // 0xb63aec: b.ne            #0xb63af8
    // 0xb63af0: r0 = Null
    //     0xb63af0: mov             x0, NULL
    // 0xb63af4: b               #0xb63b18
    // 0xb63af8: LoadField: r2 = r1->field_7
    //     0xb63af8: ldur            w2, [x1, #7]
    // 0xb63afc: DecompressPointer r2
    //     0xb63afc: add             x2, x2, HEAP, lsl #32
    // 0xb63b00: cmp             w2, NULL
    // 0xb63b04: b.ne            #0xb63b10
    // 0xb63b08: r0 = Null
    //     0xb63b08: mov             x0, NULL
    // 0xb63b0c: b               #0xb63b18
    // 0xb63b10: str             x2, [SP]
    // 0xb63b14: r0 = toString()
    //     0xb63b14: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xb63b18: cmp             w0, NULL
    // 0xb63b1c: b.ne            #0xb63b28
    // 0xb63b20: r3 = ""
    //     0xb63b20: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb63b24: b               #0xb63b2c
    // 0xb63b28: mov             x3, x0
    // 0xb63b2c: ldur            x0, [fp, #-0x18]
    // 0xb63b30: ldur            x2, [fp, #-0x20]
    // 0xb63b34: ldr             x1, [fp, #0x18]
    // 0xb63b38: stur            x3, [fp, #-0x38]
    // 0xb63b3c: r0 = of()
    //     0xb63b3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63b40: LoadField: r1 = r0->field_87
    //     0xb63b40: ldur            w1, [x0, #0x87]
    // 0xb63b44: DecompressPointer r1
    //     0xb63b44: add             x1, x1, HEAP, lsl #32
    // 0xb63b48: LoadField: r0 = r1->field_7
    //     0xb63b48: ldur            w0, [x1, #7]
    // 0xb63b4c: DecompressPointer r0
    //     0xb63b4c: add             x0, x0, HEAP, lsl #32
    // 0xb63b50: r16 = 12.000000
    //     0xb63b50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb63b54: ldr             x16, [x16, #0x9e8]
    // 0xb63b58: r30 = Instance_Color
    //     0xb63b58: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb63b5c: stp             lr, x16, [SP]
    // 0xb63b60: mov             x1, x0
    // 0xb63b64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb63b64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb63b68: ldr             x4, [x4, #0xaa0]
    // 0xb63b6c: r0 = copyWith()
    //     0xb63b6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb63b70: stur            x0, [fp, #-0x48]
    // 0xb63b74: r0 = Text()
    //     0xb63b74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb63b78: mov             x3, x0
    // 0xb63b7c: ldur            x0, [fp, #-0x38]
    // 0xb63b80: stur            x3, [fp, #-0x50]
    // 0xb63b84: StoreField: r3->field_b = r0
    //     0xb63b84: stur            w0, [x3, #0xb]
    // 0xb63b88: ldur            x0, [fp, #-0x48]
    // 0xb63b8c: StoreField: r3->field_13 = r0
    //     0xb63b8c: stur            w0, [x3, #0x13]
    // 0xb63b90: r1 = Null
    //     0xb63b90: mov             x1, NULL
    // 0xb63b94: r2 = 4
    //     0xb63b94: movz            x2, #0x4
    // 0xb63b98: r0 = AllocateArray()
    //     0xb63b98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb63b9c: mov             x2, x0
    // 0xb63ba0: ldur            x0, [fp, #-0x20]
    // 0xb63ba4: stur            x2, [fp, #-0x38]
    // 0xb63ba8: StoreField: r2->field_f = r0
    //     0xb63ba8: stur            w0, [x2, #0xf]
    // 0xb63bac: ldur            x0, [fp, #-0x50]
    // 0xb63bb0: StoreField: r2->field_13 = r0
    //     0xb63bb0: stur            w0, [x2, #0x13]
    // 0xb63bb4: r1 = <Widget>
    //     0xb63bb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb63bb8: r0 = AllocateGrowableArray()
    //     0xb63bb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb63bbc: mov             x3, x0
    // 0xb63bc0: ldur            x0, [fp, #-0x38]
    // 0xb63bc4: stur            x3, [fp, #-0x48]
    // 0xb63bc8: StoreField: r3->field_f = r0
    //     0xb63bc8: stur            w0, [x3, #0xf]
    // 0xb63bcc: r0 = 4
    //     0xb63bcc: movz            x0, #0x4
    // 0xb63bd0: StoreField: r3->field_b = r0
    //     0xb63bd0: stur            w0, [x3, #0xb]
    // 0xb63bd4: ldur            x4, [fp, #-0x18]
    // 0xb63bd8: cmp             w4, NULL
    // 0xb63bdc: b.ne            #0xb63be8
    // 0xb63be0: mov             x2, x3
    // 0xb63be4: b               #0xb63d58
    // 0xb63be8: LoadField: r1 = r4->field_7b
    //     0xb63be8: ldur            w1, [x4, #0x7b]
    // 0xb63bec: DecompressPointer r1
    //     0xb63bec: add             x1, x1, HEAP, lsl #32
    // 0xb63bf0: cmp             w1, NULL
    // 0xb63bf4: b.ne            #0xb63c00
    // 0xb63bf8: mov             x2, x3
    // 0xb63bfc: b               #0xb63d58
    // 0xb63c00: LoadField: r5 = r1->field_b
    //     0xb63c00: ldur            w5, [x1, #0xb]
    // 0xb63c04: DecompressPointer r5
    //     0xb63c04: add             x5, x5, HEAP, lsl #32
    // 0xb63c08: stur            x5, [fp, #-0x20]
    // 0xb63c0c: cmp             w5, NULL
    // 0xb63c10: b.eq            #0xb63d54
    // 0xb63c14: r1 = Null
    //     0xb63c14: mov             x1, NULL
    // 0xb63c18: r2 = 6
    //     0xb63c18: movz            x2, #0x6
    // 0xb63c1c: r0 = AllocateArray()
    //     0xb63c1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb63c20: r16 = " | ("
    //     0xb63c20: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xb63c24: ldr             x16, [x16, #0xd70]
    // 0xb63c28: StoreField: r0->field_f = r16
    //     0xb63c28: stur            w16, [x0, #0xf]
    // 0xb63c2c: ldur            x1, [fp, #-0x20]
    // 0xb63c30: cmp             w1, NULL
    // 0xb63c34: b.ne            #0xb63c40
    // 0xb63c38: r2 = Null
    //     0xb63c38: mov             x2, NULL
    // 0xb63c3c: b               #0xb63c64
    // 0xb63c40: LoadField: d0 = r1->field_7
    //     0xb63c40: ldur            d0, [x1, #7]
    // 0xb63c44: fcmp            d0, d0
    // 0xb63c48: b.vs            #0xb649c0
    // 0xb63c4c: fcvtzs          x1, d0
    // 0xb63c50: asr             x16, x1, #0x1e
    // 0xb63c54: cmp             x16, x1, asr #63
    // 0xb63c58: b.ne            #0xb649c0
    // 0xb63c5c: lsl             x1, x1, #1
    // 0xb63c60: mov             x2, x1
    // 0xb63c64: ldur            x1, [fp, #-0x48]
    // 0xb63c68: StoreField: r0->field_13 = r2
    //     0xb63c68: stur            w2, [x0, #0x13]
    // 0xb63c6c: r16 = ")"
    //     0xb63c6c: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb63c70: ArrayStore: r0[0] = r16  ; List_4
    //     0xb63c70: stur            w16, [x0, #0x17]
    // 0xb63c74: str             x0, [SP]
    // 0xb63c78: r0 = _interpolate()
    //     0xb63c78: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb63c7c: ldr             x1, [fp, #0x18]
    // 0xb63c80: stur            x0, [fp, #-0x20]
    // 0xb63c84: r0 = of()
    //     0xb63c84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63c88: LoadField: r1 = r0->field_87
    //     0xb63c88: ldur            w1, [x0, #0x87]
    // 0xb63c8c: DecompressPointer r1
    //     0xb63c8c: add             x1, x1, HEAP, lsl #32
    // 0xb63c90: LoadField: r0 = r1->field_2b
    //     0xb63c90: ldur            w0, [x1, #0x2b]
    // 0xb63c94: DecompressPointer r0
    //     0xb63c94: add             x0, x0, HEAP, lsl #32
    // 0xb63c98: r16 = 12.000000
    //     0xb63c98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb63c9c: ldr             x16, [x16, #0x9e8]
    // 0xb63ca0: r30 = Instance_Color
    //     0xb63ca0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb63ca4: stp             lr, x16, [SP]
    // 0xb63ca8: mov             x1, x0
    // 0xb63cac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb63cac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb63cb0: ldr             x4, [x4, #0xaa0]
    // 0xb63cb4: r0 = copyWith()
    //     0xb63cb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb63cb8: stur            x0, [fp, #-0x38]
    // 0xb63cbc: r0 = Text()
    //     0xb63cbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb63cc0: mov             x2, x0
    // 0xb63cc4: ldur            x0, [fp, #-0x20]
    // 0xb63cc8: stur            x2, [fp, #-0x50]
    // 0xb63ccc: StoreField: r2->field_b = r0
    //     0xb63ccc: stur            w0, [x2, #0xb]
    // 0xb63cd0: ldur            x0, [fp, #-0x38]
    // 0xb63cd4: StoreField: r2->field_13 = r0
    //     0xb63cd4: stur            w0, [x2, #0x13]
    // 0xb63cd8: ldur            x0, [fp, #-0x48]
    // 0xb63cdc: LoadField: r1 = r0->field_b
    //     0xb63cdc: ldur            w1, [x0, #0xb]
    // 0xb63ce0: LoadField: r3 = r0->field_f
    //     0xb63ce0: ldur            w3, [x0, #0xf]
    // 0xb63ce4: DecompressPointer r3
    //     0xb63ce4: add             x3, x3, HEAP, lsl #32
    // 0xb63ce8: LoadField: r4 = r3->field_b
    //     0xb63ce8: ldur            w4, [x3, #0xb]
    // 0xb63cec: r3 = LoadInt32Instr(r1)
    //     0xb63cec: sbfx            x3, x1, #1, #0x1f
    // 0xb63cf0: stur            x3, [fp, #-0x68]
    // 0xb63cf4: r1 = LoadInt32Instr(r4)
    //     0xb63cf4: sbfx            x1, x4, #1, #0x1f
    // 0xb63cf8: cmp             x3, x1
    // 0xb63cfc: b.ne            #0xb63d08
    // 0xb63d00: mov             x1, x0
    // 0xb63d04: r0 = _growToNextCapacity()
    //     0xb63d04: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb63d08: ldur            x2, [fp, #-0x48]
    // 0xb63d0c: ldur            x3, [fp, #-0x68]
    // 0xb63d10: add             x0, x3, #1
    // 0xb63d14: lsl             x1, x0, #1
    // 0xb63d18: StoreField: r2->field_b = r1
    //     0xb63d18: stur            w1, [x2, #0xb]
    // 0xb63d1c: LoadField: r1 = r2->field_f
    //     0xb63d1c: ldur            w1, [x2, #0xf]
    // 0xb63d20: DecompressPointer r1
    //     0xb63d20: add             x1, x1, HEAP, lsl #32
    // 0xb63d24: ldur            x0, [fp, #-0x50]
    // 0xb63d28: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb63d28: add             x25, x1, x3, lsl #2
    //     0xb63d2c: add             x25, x25, #0xf
    //     0xb63d30: str             w0, [x25]
    //     0xb63d34: tbz             w0, #0, #0xb63d50
    //     0xb63d38: ldurb           w16, [x1, #-1]
    //     0xb63d3c: ldurb           w17, [x0, #-1]
    //     0xb63d40: and             x16, x17, x16, lsr #2
    //     0xb63d44: tst             x16, HEAP, lsr #32
    //     0xb63d48: b.eq            #0xb63d50
    //     0xb63d4c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb63d50: b               #0xb63d58
    // 0xb63d54: mov             x2, x3
    // 0xb63d58: r0 = Row()
    //     0xb63d58: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb63d5c: r2 = Instance_Axis
    //     0xb63d5c: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb63d60: StoreField: r0->field_f = r2
    //     0xb63d60: stur            w2, [x0, #0xf]
    // 0xb63d64: r3 = Instance_MainAxisAlignment
    //     0xb63d64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb63d68: ldr             x3, [x3, #0xa08]
    // 0xb63d6c: StoreField: r0->field_13 = r3
    //     0xb63d6c: stur            w3, [x0, #0x13]
    // 0xb63d70: r4 = Instance_MainAxisSize
    //     0xb63d70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb63d74: ldr             x4, [x4, #0xdd0]
    // 0xb63d78: ArrayStore: r0[0] = r4  ; List_4
    //     0xb63d78: stur            w4, [x0, #0x17]
    // 0xb63d7c: r5 = Instance_CrossAxisAlignment
    //     0xb63d7c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb63d80: ldr             x5, [x5, #0xa18]
    // 0xb63d84: StoreField: r0->field_1b = r5
    //     0xb63d84: stur            w5, [x0, #0x1b]
    // 0xb63d88: r6 = Instance_VerticalDirection
    //     0xb63d88: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb63d8c: ldr             x6, [x6, #0xa20]
    // 0xb63d90: StoreField: r0->field_23 = r6
    //     0xb63d90: stur            w6, [x0, #0x23]
    // 0xb63d94: r7 = Instance_Clip
    //     0xb63d94: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb63d98: ldr             x7, [x7, #0x38]
    // 0xb63d9c: StoreField: r0->field_2b = r7
    //     0xb63d9c: stur            w7, [x0, #0x2b]
    // 0xb63da0: StoreField: r0->field_2f = rZR
    //     0xb63da0: stur            xzr, [x0, #0x2f]
    // 0xb63da4: ldur            x1, [fp, #-0x48]
    // 0xb63da8: StoreField: r0->field_b = r1
    //     0xb63da8: stur            w1, [x0, #0xb]
    // 0xb63dac: mov             x16, x2
    // 0xb63db0: mov             x2, x0
    // 0xb63db4: mov             x0, x16
    // 0xb63db8: mov             x16, x3
    // 0xb63dbc: mov             x3, x2
    // 0xb63dc0: mov             x2, x16
    // 0xb63dc4: mov             x16, x4
    // 0xb63dc8: mov             x4, x3
    // 0xb63dcc: mov             x3, x16
    // 0xb63dd0: mov             x16, x6
    // 0xb63dd4: mov             x6, x4
    // 0xb63dd8: mov             x4, x16
    // 0xb63ddc: mov             x5, x7
    // 0xb63de0: b               #0xb64050
    // 0xb63de4: ldur            x8, [fp, #-0x18]
    // 0xb63de8: r4 = Instance_MainAxisSize
    //     0xb63de8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb63dec: ldr             x4, [x4, #0xdd0]
    // 0xb63df0: r2 = Instance_Axis
    //     0xb63df0: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb63df4: r6 = Instance_VerticalDirection
    //     0xb63df4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb63df8: ldr             x6, [x6, #0xa20]
    // 0xb63dfc: r3 = Instance_MainAxisAlignment
    //     0xb63dfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb63e00: ldr             x3, [x3, #0xa08]
    // 0xb63e04: r5 = Instance_CrossAxisAlignment
    //     0xb63e04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb63e08: ldr             x5, [x5, #0xa18]
    // 0xb63e0c: r0 = Instance_BlendMode
    //     0xb63e0c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb63e10: ldr             x0, [x0, #0xb30]
    // 0xb63e14: r7 = Instance_Clip
    //     0xb63e14: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb63e18: ldr             x7, [x7, #0x38]
    // 0xb63e1c: ldr             x1, [fp, #0x18]
    // 0xb63e20: r0 = of()
    //     0xb63e20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63e24: LoadField: r1 = r0->field_5b
    //     0xb63e24: ldur            w1, [x0, #0x5b]
    // 0xb63e28: DecompressPointer r1
    //     0xb63e28: add             x1, x1, HEAP, lsl #32
    // 0xb63e2c: stur            x1, [fp, #-0x20]
    // 0xb63e30: r0 = ColorFilter()
    //     0xb63e30: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb63e34: mov             x1, x0
    // 0xb63e38: ldur            x0, [fp, #-0x20]
    // 0xb63e3c: stur            x1, [fp, #-0x38]
    // 0xb63e40: StoreField: r1->field_7 = r0
    //     0xb63e40: stur            w0, [x1, #7]
    // 0xb63e44: r0 = Instance_BlendMode
    //     0xb63e44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb63e48: ldr             x0, [x0, #0xb30]
    // 0xb63e4c: StoreField: r1->field_b = r0
    //     0xb63e4c: stur            w0, [x1, #0xb]
    // 0xb63e50: r0 = 1
    //     0xb63e50: movz            x0, #0x1
    // 0xb63e54: StoreField: r1->field_13 = r0
    //     0xb63e54: stur            x0, [x1, #0x13]
    // 0xb63e58: r0 = SvgPicture()
    //     0xb63e58: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb63e5c: stur            x0, [fp, #-0x20]
    // 0xb63e60: ldur            x16, [fp, #-0x38]
    // 0xb63e64: str             x16, [SP]
    // 0xb63e68: mov             x1, x0
    // 0xb63e6c: r2 = "assets/images/green_star.svg"
    //     0xb63e6c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb63e70: ldr             x2, [x2, #0x9a0]
    // 0xb63e74: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb63e74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb63e78: ldr             x4, [x4, #0xa38]
    // 0xb63e7c: r0 = SvgPicture.asset()
    //     0xb63e7c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb63e80: ldur            x0, [fp, #-0x18]
    // 0xb63e84: cmp             w0, NULL
    // 0xb63e88: b.ne            #0xb63e94
    // 0xb63e8c: r0 = Null
    //     0xb63e8c: mov             x0, NULL
    // 0xb63e90: b               #0xb63ecc
    // 0xb63e94: LoadField: r1 = r0->field_7b
    //     0xb63e94: ldur            w1, [x0, #0x7b]
    // 0xb63e98: DecompressPointer r1
    //     0xb63e98: add             x1, x1, HEAP, lsl #32
    // 0xb63e9c: cmp             w1, NULL
    // 0xb63ea0: b.ne            #0xb63eac
    // 0xb63ea4: r0 = Null
    //     0xb63ea4: mov             x0, NULL
    // 0xb63ea8: b               #0xb63ecc
    // 0xb63eac: LoadField: r2 = r1->field_7
    //     0xb63eac: ldur            w2, [x1, #7]
    // 0xb63eb0: DecompressPointer r2
    //     0xb63eb0: add             x2, x2, HEAP, lsl #32
    // 0xb63eb4: cmp             w2, NULL
    // 0xb63eb8: b.ne            #0xb63ec4
    // 0xb63ebc: r0 = Null
    //     0xb63ebc: mov             x0, NULL
    // 0xb63ec0: b               #0xb63ecc
    // 0xb63ec4: str             x2, [SP]
    // 0xb63ec8: r0 = toString()
    //     0xb63ec8: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xb63ecc: cmp             w0, NULL
    // 0xb63ed0: b.ne            #0xb63edc
    // 0xb63ed4: r2 = ""
    //     0xb63ed4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb63ed8: b               #0xb63ee0
    // 0xb63edc: mov             x2, x0
    // 0xb63ee0: ldur            x0, [fp, #-0x20]
    // 0xb63ee4: ldr             x1, [fp, #0x18]
    // 0xb63ee8: stur            x2, [fp, #-0x38]
    // 0xb63eec: r0 = of()
    //     0xb63eec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63ef0: LoadField: r1 = r0->field_87
    //     0xb63ef0: ldur            w1, [x0, #0x87]
    // 0xb63ef4: DecompressPointer r1
    //     0xb63ef4: add             x1, x1, HEAP, lsl #32
    // 0xb63ef8: LoadField: r0 = r1->field_7
    //     0xb63ef8: ldur            w0, [x1, #7]
    // 0xb63efc: DecompressPointer r0
    //     0xb63efc: add             x0, x0, HEAP, lsl #32
    // 0xb63f00: r16 = 12.000000
    //     0xb63f00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb63f04: ldr             x16, [x16, #0x9e8]
    // 0xb63f08: r30 = Instance_Color
    //     0xb63f08: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb63f0c: stp             lr, x16, [SP]
    // 0xb63f10: mov             x1, x0
    // 0xb63f14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb63f14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb63f18: ldr             x4, [x4, #0xaa0]
    // 0xb63f1c: r0 = copyWith()
    //     0xb63f1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb63f20: stur            x0, [fp, #-0x48]
    // 0xb63f24: r0 = Text()
    //     0xb63f24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb63f28: mov             x2, x0
    // 0xb63f2c: ldur            x0, [fp, #-0x38]
    // 0xb63f30: stur            x2, [fp, #-0x50]
    // 0xb63f34: StoreField: r2->field_b = r0
    //     0xb63f34: stur            w0, [x2, #0xb]
    // 0xb63f38: ldur            x0, [fp, #-0x48]
    // 0xb63f3c: StoreField: r2->field_13 = r0
    //     0xb63f3c: stur            w0, [x2, #0x13]
    // 0xb63f40: ldr             x1, [fp, #0x18]
    // 0xb63f44: r0 = of()
    //     0xb63f44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63f48: LoadField: r1 = r0->field_87
    //     0xb63f48: ldur            w1, [x0, #0x87]
    // 0xb63f4c: DecompressPointer r1
    //     0xb63f4c: add             x1, x1, HEAP, lsl #32
    // 0xb63f50: LoadField: r0 = r1->field_2b
    //     0xb63f50: ldur            w0, [x1, #0x2b]
    // 0xb63f54: DecompressPointer r0
    //     0xb63f54: add             x0, x0, HEAP, lsl #32
    // 0xb63f58: ldr             x1, [fp, #0x18]
    // 0xb63f5c: stur            x0, [fp, #-0x38]
    // 0xb63f60: r0 = of()
    //     0xb63f60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb63f64: LoadField: r1 = r0->field_5b
    //     0xb63f64: ldur            w1, [x0, #0x5b]
    // 0xb63f68: DecompressPointer r1
    //     0xb63f68: add             x1, x1, HEAP, lsl #32
    // 0xb63f6c: r16 = 10.000000
    //     0xb63f6c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb63f70: stp             x1, x16, [SP]
    // 0xb63f74: ldur            x1, [fp, #-0x38]
    // 0xb63f78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb63f78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb63f7c: ldr             x4, [x4, #0xaa0]
    // 0xb63f80: r0 = copyWith()
    //     0xb63f80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb63f84: stur            x0, [fp, #-0x38]
    // 0xb63f88: r0 = Text()
    //     0xb63f88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb63f8c: mov             x3, x0
    // 0xb63f90: r0 = " Brand Rating"
    //     0xb63f90: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xb63f94: ldr             x0, [x0, #0xd78]
    // 0xb63f98: stur            x3, [fp, #-0x48]
    // 0xb63f9c: StoreField: r3->field_b = r0
    //     0xb63f9c: stur            w0, [x3, #0xb]
    // 0xb63fa0: ldur            x0, [fp, #-0x38]
    // 0xb63fa4: StoreField: r3->field_13 = r0
    //     0xb63fa4: stur            w0, [x3, #0x13]
    // 0xb63fa8: r1 = Null
    //     0xb63fa8: mov             x1, NULL
    // 0xb63fac: r2 = 6
    //     0xb63fac: movz            x2, #0x6
    // 0xb63fb0: r0 = AllocateArray()
    //     0xb63fb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb63fb4: mov             x2, x0
    // 0xb63fb8: ldur            x0, [fp, #-0x20]
    // 0xb63fbc: stur            x2, [fp, #-0x38]
    // 0xb63fc0: StoreField: r2->field_f = r0
    //     0xb63fc0: stur            w0, [x2, #0xf]
    // 0xb63fc4: ldur            x0, [fp, #-0x50]
    // 0xb63fc8: StoreField: r2->field_13 = r0
    //     0xb63fc8: stur            w0, [x2, #0x13]
    // 0xb63fcc: ldur            x0, [fp, #-0x48]
    // 0xb63fd0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb63fd0: stur            w0, [x2, #0x17]
    // 0xb63fd4: r1 = <Widget>
    //     0xb63fd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb63fd8: r0 = AllocateGrowableArray()
    //     0xb63fd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb63fdc: mov             x1, x0
    // 0xb63fe0: ldur            x0, [fp, #-0x38]
    // 0xb63fe4: stur            x1, [fp, #-0x20]
    // 0xb63fe8: StoreField: r1->field_f = r0
    //     0xb63fe8: stur            w0, [x1, #0xf]
    // 0xb63fec: r2 = 6
    //     0xb63fec: movz            x2, #0x6
    // 0xb63ff0: StoreField: r1->field_b = r2
    //     0xb63ff0: stur            w2, [x1, #0xb]
    // 0xb63ff4: r0 = Row()
    //     0xb63ff4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb63ff8: mov             x1, x0
    // 0xb63ffc: r0 = Instance_Axis
    //     0xb63ffc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb64000: StoreField: r1->field_f = r0
    //     0xb64000: stur            w0, [x1, #0xf]
    // 0xb64004: r2 = Instance_MainAxisAlignment
    //     0xb64004: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb64008: ldr             x2, [x2, #0xa08]
    // 0xb6400c: StoreField: r1->field_13 = r2
    //     0xb6400c: stur            w2, [x1, #0x13]
    // 0xb64010: r3 = Instance_MainAxisSize
    //     0xb64010: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb64014: ldr             x3, [x3, #0xdd0]
    // 0xb64018: ArrayStore: r1[0] = r3  ; List_4
    //     0xb64018: stur            w3, [x1, #0x17]
    // 0xb6401c: r4 = Instance_CrossAxisAlignment
    //     0xb6401c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb64020: ldr             x4, [x4, #0xa18]
    // 0xb64024: StoreField: r1->field_1b = r4
    //     0xb64024: stur            w4, [x1, #0x1b]
    // 0xb64028: r4 = Instance_VerticalDirection
    //     0xb64028: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6402c: ldr             x4, [x4, #0xa20]
    // 0xb64030: StoreField: r1->field_23 = r4
    //     0xb64030: stur            w4, [x1, #0x23]
    // 0xb64034: r5 = Instance_Clip
    //     0xb64034: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb64038: ldr             x5, [x5, #0x38]
    // 0xb6403c: StoreField: r1->field_2b = r5
    //     0xb6403c: stur            w5, [x1, #0x2b]
    // 0xb64040: StoreField: r1->field_2f = rZR
    //     0xb64040: stur            xzr, [x1, #0x2f]
    // 0xb64044: ldur            x6, [fp, #-0x20]
    // 0xb64048: StoreField: r1->field_b = r6
    //     0xb64048: stur            w6, [x1, #0xb]
    // 0xb6404c: mov             x6, x1
    // 0xb64050: ldur            x1, [fp, #-0x40]
    // 0xb64054: stur            x6, [fp, #-0x20]
    // 0xb64058: r0 = Padding()
    //     0xb64058: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6405c: mov             x2, x0
    // 0xb64060: r0 = Instance_EdgeInsets
    //     0xb64060: add             x0, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb64064: ldr             x0, [x0, #0xc98]
    // 0xb64068: stur            x2, [fp, #-0x38]
    // 0xb6406c: StoreField: r2->field_f = r0
    //     0xb6406c: stur            w0, [x2, #0xf]
    // 0xb64070: ldur            x1, [fp, #-0x20]
    // 0xb64074: StoreField: r2->field_b = r1
    //     0xb64074: stur            w1, [x2, #0xb]
    // 0xb64078: ldur            x3, [fp, #-0x40]
    // 0xb6407c: LoadField: r1 = r3->field_b
    //     0xb6407c: ldur            w1, [x3, #0xb]
    // 0xb64080: LoadField: r4 = r3->field_f
    //     0xb64080: ldur            w4, [x3, #0xf]
    // 0xb64084: DecompressPointer r4
    //     0xb64084: add             x4, x4, HEAP, lsl #32
    // 0xb64088: LoadField: r5 = r4->field_b
    //     0xb64088: ldur            w5, [x4, #0xb]
    // 0xb6408c: r4 = LoadInt32Instr(r1)
    //     0xb6408c: sbfx            x4, x1, #1, #0x1f
    // 0xb64090: stur            x4, [fp, #-0x68]
    // 0xb64094: r1 = LoadInt32Instr(r5)
    //     0xb64094: sbfx            x1, x5, #1, #0x1f
    // 0xb64098: cmp             x4, x1
    // 0xb6409c: b.ne            #0xb640a8
    // 0xb640a0: mov             x1, x3
    // 0xb640a4: r0 = _growToNextCapacity()
    //     0xb640a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb640a8: ldur            x3, [fp, #-0x40]
    // 0xb640ac: ldur            x2, [fp, #-0x68]
    // 0xb640b0: add             x0, x2, #1
    // 0xb640b4: lsl             x1, x0, #1
    // 0xb640b8: StoreField: r3->field_b = r1
    //     0xb640b8: stur            w1, [x3, #0xb]
    // 0xb640bc: LoadField: r1 = r3->field_f
    //     0xb640bc: ldur            w1, [x3, #0xf]
    // 0xb640c0: DecompressPointer r1
    //     0xb640c0: add             x1, x1, HEAP, lsl #32
    // 0xb640c4: ldur            x0, [fp, #-0x38]
    // 0xb640c8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb640c8: add             x25, x1, x2, lsl #2
    //     0xb640cc: add             x25, x25, #0xf
    //     0xb640d0: str             w0, [x25]
    //     0xb640d4: tbz             w0, #0, #0xb640f0
    //     0xb640d8: ldurb           w16, [x1, #-1]
    //     0xb640dc: ldurb           w17, [x0, #-1]
    //     0xb640e0: and             x16, x17, x16, lsr #2
    //     0xb640e4: tst             x16, HEAP, lsr #32
    //     0xb640e8: b.eq            #0xb640f0
    //     0xb640ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb640f0: b               #0xb640f8
    // 0xb640f4: mov             x3, x1
    // 0xb640f8: ldur            x0, [fp, #-0x18]
    // 0xb640fc: r1 = Null
    //     0xb640fc: mov             x1, NULL
    // 0xb64100: r2 = 6
    //     0xb64100: movz            x2, #0x6
    // 0xb64104: r0 = AllocateArray()
    //     0xb64104: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb64108: r16 = " "
    //     0xb64108: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb6410c: StoreField: r0->field_f = r16
    //     0xb6410c: stur            w16, [x0, #0xf]
    // 0xb64110: ldur            x1, [fp, #-0x18]
    // 0xb64114: cmp             w1, NULL
    // 0xb64118: b.ne            #0xb64124
    // 0xb6411c: r2 = Null
    //     0xb6411c: mov             x2, NULL
    // 0xb64120: b               #0xb6412c
    // 0xb64124: LoadField: r2 = r1->field_43
    //     0xb64124: ldur            w2, [x1, #0x43]
    // 0xb64128: DecompressPointer r2
    //     0xb64128: add             x2, x2, HEAP, lsl #32
    // 0xb6412c: StoreField: r0->field_13 = r2
    //     0xb6412c: stur            w2, [x0, #0x13]
    // 0xb64130: r16 = " "
    //     0xb64130: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb64134: ArrayStore: r0[0] = r16  ; List_4
    //     0xb64134: stur            w16, [x0, #0x17]
    // 0xb64138: str             x0, [SP]
    // 0xb6413c: r0 = _interpolate()
    //     0xb6413c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb64140: ldr             x1, [fp, #0x18]
    // 0xb64144: stur            x0, [fp, #-0x20]
    // 0xb64148: r0 = of()
    //     0xb64148: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6414c: LoadField: r1 = r0->field_87
    //     0xb6414c: ldur            w1, [x0, #0x87]
    // 0xb64150: DecompressPointer r1
    //     0xb64150: add             x1, x1, HEAP, lsl #32
    // 0xb64154: LoadField: r0 = r1->field_2b
    //     0xb64154: ldur            w0, [x1, #0x2b]
    // 0xb64158: DecompressPointer r0
    //     0xb64158: add             x0, x0, HEAP, lsl #32
    // 0xb6415c: stur            x0, [fp, #-0x38]
    // 0xb64160: r1 = Instance_Color
    //     0xb64160: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb64164: d0 = 0.700000
    //     0xb64164: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb64168: ldr             d0, [x17, #0xf48]
    // 0xb6416c: r0 = withOpacity()
    //     0xb6416c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb64170: r16 = 12.000000
    //     0xb64170: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb64174: ldr             x16, [x16, #0x9e8]
    // 0xb64178: stp             x0, x16, [SP]
    // 0xb6417c: ldur            x1, [fp, #-0x38]
    // 0xb64180: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb64180: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb64184: ldr             x4, [x4, #0xaa0]
    // 0xb64188: r0 = copyWith()
    //     0xb64188: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6418c: stur            x0, [fp, #-0x38]
    // 0xb64190: r0 = Text()
    //     0xb64190: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb64194: mov             x1, x0
    // 0xb64198: ldur            x0, [fp, #-0x20]
    // 0xb6419c: stur            x1, [fp, #-0x48]
    // 0xb641a0: StoreField: r1->field_b = r0
    //     0xb641a0: stur            w0, [x1, #0xb]
    // 0xb641a4: ldur            x0, [fp, #-0x38]
    // 0xb641a8: StoreField: r1->field_13 = r0
    //     0xb641a8: stur            w0, [x1, #0x13]
    // 0xb641ac: r0 = WidgetSpan()
    //     0xb641ac: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xb641b0: mov             x1, x0
    // 0xb641b4: ldur            x0, [fp, #-0x48]
    // 0xb641b8: stur            x1, [fp, #-0x20]
    // 0xb641bc: StoreField: r1->field_13 = r0
    //     0xb641bc: stur            w0, [x1, #0x13]
    // 0xb641c0: r0 = Instance_PlaceholderAlignment
    //     0xb641c0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xb641c4: ldr             x0, [x0, #0xa0]
    // 0xb641c8: StoreField: r1->field_b = r0
    //     0xb641c8: stur            w0, [x1, #0xb]
    // 0xb641cc: ldur            x0, [fp, #-0x18]
    // 0xb641d0: cmp             w0, NULL
    // 0xb641d4: b.ne            #0xb641e0
    // 0xb641d8: r3 = Null
    //     0xb641d8: mov             x3, NULL
    // 0xb641dc: b               #0xb641ec
    // 0xb641e0: LoadField: r2 = r0->field_4b
    //     0xb641e0: ldur            w2, [x0, #0x4b]
    // 0xb641e4: DecompressPointer r2
    //     0xb641e4: add             x2, x2, HEAP, lsl #32
    // 0xb641e8: mov             x3, x2
    // 0xb641ec: ldur            x2, [fp, #-0x40]
    // 0xb641f0: str             x3, [SP]
    // 0xb641f4: r0 = _interpolateSingle()
    //     0xb641f4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb641f8: ldr             x1, [fp, #0x18]
    // 0xb641fc: stur            x0, [fp, #-0x38]
    // 0xb64200: r0 = of()
    //     0xb64200: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb64204: LoadField: r1 = r0->field_87
    //     0xb64204: ldur            w1, [x0, #0x87]
    // 0xb64208: DecompressPointer r1
    //     0xb64208: add             x1, x1, HEAP, lsl #32
    // 0xb6420c: LoadField: r0 = r1->field_2b
    //     0xb6420c: ldur            w0, [x1, #0x2b]
    // 0xb64210: DecompressPointer r0
    //     0xb64210: add             x0, x0, HEAP, lsl #32
    // 0xb64214: stur            x0, [fp, #-0x48]
    // 0xb64218: r1 = Instance_Color
    //     0xb64218: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6421c: d0 = 0.250000
    //     0xb6421c: fmov            d0, #0.25000000
    // 0xb64220: r0 = withOpacity()
    //     0xb64220: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb64224: r16 = Instance_TextDecoration
    //     0xb64224: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb64228: ldr             x16, [x16, #0xe30]
    // 0xb6422c: r30 = 12.000000
    //     0xb6422c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb64230: ldr             lr, [lr, #0x9e8]
    // 0xb64234: stp             lr, x16, [SP, #8]
    // 0xb64238: str             x0, [SP]
    // 0xb6423c: ldur            x1, [fp, #-0x48]
    // 0xb64240: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb64240: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb64244: ldr             x4, [x4, #0xb60]
    // 0xb64248: r0 = copyWith()
    //     0xb64248: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6424c: stur            x0, [fp, #-0x48]
    // 0xb64250: r0 = TextSpan()
    //     0xb64250: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb64254: mov             x3, x0
    // 0xb64258: ldur            x0, [fp, #-0x38]
    // 0xb6425c: stur            x3, [fp, #-0x50]
    // 0xb64260: StoreField: r3->field_b = r0
    //     0xb64260: stur            w0, [x3, #0xb]
    // 0xb64264: r0 = Instance__DeferringMouseCursor
    //     0xb64264: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb64268: ArrayStore: r3[0] = r0  ; List_4
    //     0xb64268: stur            w0, [x3, #0x17]
    // 0xb6426c: ldur            x1, [fp, #-0x48]
    // 0xb64270: StoreField: r3->field_7 = r1
    //     0xb64270: stur            w1, [x3, #7]
    // 0xb64274: r1 = Null
    //     0xb64274: mov             x1, NULL
    // 0xb64278: r2 = 4
    //     0xb64278: movz            x2, #0x4
    // 0xb6427c: r0 = AllocateArray()
    //     0xb6427c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb64280: mov             x2, x0
    // 0xb64284: ldur            x0, [fp, #-0x20]
    // 0xb64288: stur            x2, [fp, #-0x38]
    // 0xb6428c: StoreField: r2->field_f = r0
    //     0xb6428c: stur            w0, [x2, #0xf]
    // 0xb64290: ldur            x0, [fp, #-0x50]
    // 0xb64294: StoreField: r2->field_13 = r0
    //     0xb64294: stur            w0, [x2, #0x13]
    // 0xb64298: r1 = <InlineSpan>
    //     0xb64298: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb6429c: ldr             x1, [x1, #0xe40]
    // 0xb642a0: r0 = AllocateGrowableArray()
    //     0xb642a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb642a4: mov             x1, x0
    // 0xb642a8: ldur            x0, [fp, #-0x38]
    // 0xb642ac: stur            x1, [fp, #-0x20]
    // 0xb642b0: StoreField: r1->field_f = r0
    //     0xb642b0: stur            w0, [x1, #0xf]
    // 0xb642b4: r2 = 4
    //     0xb642b4: movz            x2, #0x4
    // 0xb642b8: StoreField: r1->field_b = r2
    //     0xb642b8: stur            w2, [x1, #0xb]
    // 0xb642bc: r0 = TextSpan()
    //     0xb642bc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb642c0: mov             x1, x0
    // 0xb642c4: ldur            x0, [fp, #-0x20]
    // 0xb642c8: stur            x1, [fp, #-0x38]
    // 0xb642cc: StoreField: r1->field_f = r0
    //     0xb642cc: stur            w0, [x1, #0xf]
    // 0xb642d0: r0 = Instance__DeferringMouseCursor
    //     0xb642d0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb642d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb642d4: stur            w0, [x1, #0x17]
    // 0xb642d8: r0 = RichText()
    //     0xb642d8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb642dc: mov             x1, x0
    // 0xb642e0: ldur            x2, [fp, #-0x38]
    // 0xb642e4: stur            x0, [fp, #-0x20]
    // 0xb642e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb642e8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb642ec: r0 = RichText()
    //     0xb642ec: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb642f0: r0 = Padding()
    //     0xb642f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb642f4: mov             x2, x0
    // 0xb642f8: r0 = Instance_EdgeInsets
    //     0xb642f8: add             x0, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb642fc: ldr             x0, [x0, #0xc98]
    // 0xb64300: stur            x2, [fp, #-0x38]
    // 0xb64304: StoreField: r2->field_f = r0
    //     0xb64304: stur            w0, [x2, #0xf]
    // 0xb64308: ldur            x0, [fp, #-0x20]
    // 0xb6430c: StoreField: r2->field_b = r0
    //     0xb6430c: stur            w0, [x2, #0xb]
    // 0xb64310: ldur            x0, [fp, #-0x40]
    // 0xb64314: LoadField: r1 = r0->field_b
    //     0xb64314: ldur            w1, [x0, #0xb]
    // 0xb64318: LoadField: r3 = r0->field_f
    //     0xb64318: ldur            w3, [x0, #0xf]
    // 0xb6431c: DecompressPointer r3
    //     0xb6431c: add             x3, x3, HEAP, lsl #32
    // 0xb64320: LoadField: r4 = r3->field_b
    //     0xb64320: ldur            w4, [x3, #0xb]
    // 0xb64324: r3 = LoadInt32Instr(r1)
    //     0xb64324: sbfx            x3, x1, #1, #0x1f
    // 0xb64328: stur            x3, [fp, #-0x68]
    // 0xb6432c: r1 = LoadInt32Instr(r4)
    //     0xb6432c: sbfx            x1, x4, #1, #0x1f
    // 0xb64330: cmp             x3, x1
    // 0xb64334: b.ne            #0xb64340
    // 0xb64338: mov             x1, x0
    // 0xb6433c: r0 = _growToNextCapacity()
    //     0xb6433c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb64340: ldur            x4, [fp, #-8]
    // 0xb64344: ldur            x2, [fp, #-0x40]
    // 0xb64348: ldur            x3, [fp, #-0x68]
    // 0xb6434c: add             x0, x3, #1
    // 0xb64350: lsl             x1, x0, #1
    // 0xb64354: StoreField: r2->field_b = r1
    //     0xb64354: stur            w1, [x2, #0xb]
    // 0xb64358: LoadField: r1 = r2->field_f
    //     0xb64358: ldur            w1, [x2, #0xf]
    // 0xb6435c: DecompressPointer r1
    //     0xb6435c: add             x1, x1, HEAP, lsl #32
    // 0xb64360: ldur            x0, [fp, #-0x38]
    // 0xb64364: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb64364: add             x25, x1, x3, lsl #2
    //     0xb64368: add             x25, x25, #0xf
    //     0xb6436c: str             w0, [x25]
    //     0xb64370: tbz             w0, #0, #0xb6438c
    //     0xb64374: ldurb           w16, [x1, #-1]
    //     0xb64378: ldurb           w17, [x0, #-1]
    //     0xb6437c: and             x16, x17, x16, lsr #2
    //     0xb64380: tst             x16, HEAP, lsr #32
    //     0xb64384: b.eq            #0xb6438c
    //     0xb64388: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6438c: r0 = Column()
    //     0xb6438c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb64390: mov             x2, x0
    // 0xb64394: r0 = Instance_Axis
    //     0xb64394: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb64398: stur            x2, [fp, #-0x20]
    // 0xb6439c: StoreField: r2->field_f = r0
    //     0xb6439c: stur            w0, [x2, #0xf]
    // 0xb643a0: r3 = Instance_MainAxisAlignment
    //     0xb643a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb643a4: ldr             x3, [x3, #0xa08]
    // 0xb643a8: StoreField: r2->field_13 = r3
    //     0xb643a8: stur            w3, [x2, #0x13]
    // 0xb643ac: r4 = Instance_MainAxisSize
    //     0xb643ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb643b0: ldr             x4, [x4, #0xdd0]
    // 0xb643b4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb643b4: stur            w4, [x2, #0x17]
    // 0xb643b8: r5 = Instance_CrossAxisAlignment
    //     0xb643b8: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb643bc: ldr             x5, [x5, #0x890]
    // 0xb643c0: StoreField: r2->field_1b = r5
    //     0xb643c0: stur            w5, [x2, #0x1b]
    // 0xb643c4: r6 = Instance_VerticalDirection
    //     0xb643c4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb643c8: ldr             x6, [x6, #0xa20]
    // 0xb643cc: StoreField: r2->field_23 = r6
    //     0xb643cc: stur            w6, [x2, #0x23]
    // 0xb643d0: r7 = Instance_Clip
    //     0xb643d0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb643d4: ldr             x7, [x7, #0x38]
    // 0xb643d8: StoreField: r2->field_2b = r7
    //     0xb643d8: stur            w7, [x2, #0x2b]
    // 0xb643dc: StoreField: r2->field_2f = rZR
    //     0xb643dc: stur            xzr, [x2, #0x2f]
    // 0xb643e0: ldur            x1, [fp, #-0x40]
    // 0xb643e4: StoreField: r2->field_b = r1
    //     0xb643e4: stur            w1, [x2, #0xb]
    // 0xb643e8: r1 = <FlexParentData>
    //     0xb643e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb643ec: ldr             x1, [x1, #0xe00]
    // 0xb643f0: r0 = Expanded()
    //     0xb643f0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb643f4: mov             x3, x0
    // 0xb643f8: r0 = 1
    //     0xb643f8: movz            x0, #0x1
    // 0xb643fc: stur            x3, [fp, #-0x38]
    // 0xb64400: StoreField: r3->field_13 = r0
    //     0xb64400: stur            x0, [x3, #0x13]
    // 0xb64404: r0 = Instance_FlexFit
    //     0xb64404: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb64408: ldr             x0, [x0, #0xe08]
    // 0xb6440c: StoreField: r3->field_1b = r0
    //     0xb6440c: stur            w0, [x3, #0x1b]
    // 0xb64410: ldur            x0, [fp, #-0x20]
    // 0xb64414: StoreField: r3->field_b = r0
    //     0xb64414: stur            w0, [x3, #0xb]
    // 0xb64418: r1 = Null
    //     0xb64418: mov             x1, NULL
    // 0xb6441c: r2 = 2
    //     0xb6441c: movz            x2, #0x2
    // 0xb64420: r0 = AllocateArray()
    //     0xb64420: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb64424: mov             x2, x0
    // 0xb64428: ldur            x0, [fp, #-0x38]
    // 0xb6442c: stur            x2, [fp, #-0x20]
    // 0xb64430: StoreField: r2->field_f = r0
    //     0xb64430: stur            w0, [x2, #0xf]
    // 0xb64434: r1 = <Widget>
    //     0xb64434: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb64438: r0 = AllocateGrowableArray()
    //     0xb64438: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6443c: mov             x1, x0
    // 0xb64440: ldur            x0, [fp, #-0x20]
    // 0xb64444: stur            x1, [fp, #-0x38]
    // 0xb64448: StoreField: r1->field_f = r0
    //     0xb64448: stur            w0, [x1, #0xf]
    // 0xb6444c: r0 = 2
    //     0xb6444c: movz            x0, #0x2
    // 0xb64450: StoreField: r1->field_b = r0
    //     0xb64450: stur            w0, [x1, #0xb]
    // 0xb64454: ldur            x2, [fp, #-8]
    // 0xb64458: LoadField: r0 = r2->field_f
    //     0xb64458: ldur            w0, [x2, #0xf]
    // 0xb6445c: DecompressPointer r0
    //     0xb6445c: add             x0, x0, HEAP, lsl #32
    // 0xb64460: LoadField: r3 = r0->field_b
    //     0xb64460: ldur            w3, [x0, #0xb]
    // 0xb64464: DecompressPointer r3
    //     0xb64464: add             x3, x3, HEAP, lsl #32
    // 0xb64468: cmp             w3, NULL
    // 0xb6446c: b.eq            #0xb649e8
    // 0xb64470: LoadField: r0 = r3->field_1f
    //     0xb64470: ldur            w0, [x3, #0x1f]
    // 0xb64474: DecompressPointer r0
    //     0xb64474: add             x0, x0, HEAP, lsl #32
    // 0xb64478: LoadField: r4 = r0->field_1f
    //     0xb64478: ldur            w4, [x0, #0x1f]
    // 0xb6447c: DecompressPointer r4
    //     0xb6447c: add             x4, x4, HEAP, lsl #32
    // 0xb64480: cmp             w4, NULL
    // 0xb64484: b.ne            #0xb64490
    // 0xb64488: r0 = Null
    //     0xb64488: mov             x0, NULL
    // 0xb6448c: b               #0xb64498
    // 0xb64490: LoadField: r0 = r4->field_7
    //     0xb64490: ldur            w0, [x4, #7]
    // 0xb64494: DecompressPointer r0
    //     0xb64494: add             x0, x0, HEAP, lsl #32
    // 0xb64498: cmp             w0, NULL
    // 0xb6449c: b.ne            #0xb644a8
    // 0xb644a0: mov             x2, x1
    // 0xb644a4: b               #0xb647cc
    // 0xb644a8: tbnz            w0, #4, #0xb647c8
    // 0xb644ac: LoadField: r0 = r3->field_1f
    //     0xb644ac: ldur            w0, [x3, #0x1f]
    // 0xb644b0: DecompressPointer r0
    //     0xb644b0: add             x0, x0, HEAP, lsl #32
    // 0xb644b4: LoadField: r4 = r0->field_3f
    //     0xb644b4: ldur            w4, [x0, #0x3f]
    // 0xb644b8: DecompressPointer r4
    //     0xb644b8: add             x4, x4, HEAP, lsl #32
    // 0xb644bc: cmp             w4, NULL
    // 0xb644c0: b.ne            #0xb644cc
    // 0xb644c4: r0 = Null
    //     0xb644c4: mov             x0, NULL
    // 0xb644c8: b               #0xb644d4
    // 0xb644cc: LoadField: r0 = r4->field_23
    //     0xb644cc: ldur            w0, [x4, #0x23]
    // 0xb644d0: DecompressPointer r0
    //     0xb644d0: add             x0, x0, HEAP, lsl #32
    // 0xb644d4: cmp             w0, NULL
    // 0xb644d8: b.eq            #0xb64730
    // 0xb644dc: tbnz            w0, #4, #0xb64730
    // 0xb644e0: LoadField: r0 = r3->field_3f
    //     0xb644e0: ldur            w0, [x3, #0x3f]
    // 0xb644e4: DecompressPointer r0
    //     0xb644e4: add             x0, x0, HEAP, lsl #32
    // 0xb644e8: r3 = LoadClassIdInstr(r0)
    //     0xb644e8: ldur            x3, [x0, #-1]
    //     0xb644ec: ubfx            x3, x3, #0xc, #0x14
    // 0xb644f0: r16 = "search_page"
    //     0xb644f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb644f4: ldr             x16, [x16, #0xe58]
    // 0xb644f8: stp             x16, x0, [SP]
    // 0xb644fc: mov             x0, x3
    // 0xb64500: mov             lr, x0
    // 0xb64504: ldr             lr, [x21, lr, lsl #3]
    // 0xb64508: blr             lr
    // 0xb6450c: tbnz            w0, #4, #0xb64524
    // 0xb64510: r2 = Instance_BoxDecoration
    //     0xb64510: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e570] Obj!BoxDecoration@d647d1
    //     0xb64514: ldr             x2, [x2, #0x570]
    // 0xb64518: r0 = Instance_BoxShape
    //     0xb64518: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6451c: ldr             x0, [x0, #0x80]
    // 0xb64520: b               #0xb645cc
    // 0xb64524: ldur            x0, [fp, #-0x18]
    // 0xb64528: cmp             w0, NULL
    // 0xb6452c: b.ne            #0xb64538
    // 0xb64530: r1 = Null
    //     0xb64530: mov             x1, NULL
    // 0xb64534: b               #0xb64540
    // 0xb64538: LoadField: r1 = r0->field_4f
    //     0xb64538: ldur            w1, [x0, #0x4f]
    // 0xb6453c: DecompressPointer r1
    //     0xb6453c: add             x1, x1, HEAP, lsl #32
    // 0xb64540: cmp             w1, NULL
    // 0xb64544: b.eq            #0xb6454c
    // 0xb64548: tbnz            w1, #4, #0xb64558
    // 0xb6454c: r0 = Instance_Color
    //     0xb6454c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb64550: ldr             x0, [x0, #0xf88]
    // 0xb64554: b               #0xb6456c
    // 0xb64558: ldr             x1, [fp, #0x18]
    // 0xb6455c: r0 = of()
    //     0xb6455c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb64560: LoadField: r1 = r0->field_5b
    //     0xb64560: ldur            w1, [x0, #0x5b]
    // 0xb64564: DecompressPointer r1
    //     0xb64564: add             x1, x1, HEAP, lsl #32
    // 0xb64568: mov             x0, x1
    // 0xb6456c: stur            x0, [fp, #-0x20]
    // 0xb64570: r0 = Radius()
    //     0xb64570: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb64574: d0 = 100.000000
    //     0xb64574: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb64578: stur            x0, [fp, #-0x40]
    // 0xb6457c: StoreField: r0->field_7 = d0
    //     0xb6457c: stur            d0, [x0, #7]
    // 0xb64580: StoreField: r0->field_f = d0
    //     0xb64580: stur            d0, [x0, #0xf]
    // 0xb64584: r0 = BorderRadius()
    //     0xb64584: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb64588: mov             x1, x0
    // 0xb6458c: ldur            x0, [fp, #-0x40]
    // 0xb64590: stur            x1, [fp, #-0x48]
    // 0xb64594: StoreField: r1->field_7 = r0
    //     0xb64594: stur            w0, [x1, #7]
    // 0xb64598: StoreField: r1->field_b = r0
    //     0xb64598: stur            w0, [x1, #0xb]
    // 0xb6459c: StoreField: r1->field_f = r0
    //     0xb6459c: stur            w0, [x1, #0xf]
    // 0xb645a0: StoreField: r1->field_13 = r0
    //     0xb645a0: stur            w0, [x1, #0x13]
    // 0xb645a4: r0 = BoxDecoration()
    //     0xb645a4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb645a8: mov             x1, x0
    // 0xb645ac: ldur            x0, [fp, #-0x20]
    // 0xb645b0: StoreField: r1->field_7 = r0
    //     0xb645b0: stur            w0, [x1, #7]
    // 0xb645b4: ldur            x0, [fp, #-0x48]
    // 0xb645b8: StoreField: r1->field_13 = r0
    //     0xb645b8: stur            w0, [x1, #0x13]
    // 0xb645bc: r0 = Instance_BoxShape
    //     0xb645bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb645c0: ldr             x0, [x0, #0x80]
    // 0xb645c4: StoreField: r1->field_23 = r0
    //     0xb645c4: stur            w0, [x1, #0x23]
    // 0xb645c8: mov             x2, x1
    // 0xb645cc: ldur            x1, [fp, #-8]
    // 0xb645d0: stur            x2, [fp, #-0x20]
    // 0xb645d4: LoadField: r3 = r1->field_f
    //     0xb645d4: ldur            w3, [x1, #0xf]
    // 0xb645d8: DecompressPointer r3
    //     0xb645d8: add             x3, x3, HEAP, lsl #32
    // 0xb645dc: LoadField: r1 = r3->field_b
    //     0xb645dc: ldur            w1, [x3, #0xb]
    // 0xb645e0: DecompressPointer r1
    //     0xb645e0: add             x1, x1, HEAP, lsl #32
    // 0xb645e4: cmp             w1, NULL
    // 0xb645e8: b.eq            #0xb649ec
    // 0xb645ec: LoadField: r3 = r1->field_13
    //     0xb645ec: ldur            w3, [x1, #0x13]
    // 0xb645f0: DecompressPointer r3
    //     0xb645f0: add             x3, x3, HEAP, lsl #32
    // 0xb645f4: tbnz            w3, #4, #0xb646d0
    // 0xb645f8: ldur            x1, [fp, #-0x18]
    // 0xb645fc: cmp             w1, NULL
    // 0xb64600: b.ne            #0xb6460c
    // 0xb64604: r1 = Null
    //     0xb64604: mov             x1, NULL
    // 0xb64608: b               #0xb64618
    // 0xb6460c: LoadField: r3 = r1->field_4f
    //     0xb6460c: ldur            w3, [x1, #0x4f]
    // 0xb64610: DecompressPointer r3
    //     0xb64610: add             x3, x3, HEAP, lsl #32
    // 0xb64614: mov             x1, x3
    // 0xb64618: cmp             w1, NULL
    // 0xb6461c: b.eq            #0xb64624
    // 0xb64620: tbnz            w1, #4, #0xb64640
    // 0xb64624: r0 = Container()
    //     0xb64624: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb64628: mov             x1, x0
    // 0xb6462c: stur            x0, [fp, #-8]
    // 0xb64630: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb64630: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb64634: r0 = Container()
    //     0xb64634: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb64638: ldur            x0, [fp, #-8]
    // 0xb6463c: b               #0xb64670
    // 0xb64640: r0 = SvgPicture()
    //     0xb64640: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb64644: stur            x0, [fp, #-8]
    // 0xb64648: r16 = Instance_BoxFit
    //     0xb64648: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb6464c: ldr             x16, [x16, #0xb18]
    // 0xb64650: str             x16, [SP]
    // 0xb64654: mov             x1, x0
    // 0xb64658: r2 = "assets/images/add_to_bag_glass.svg"
    //     0xb64658: add             x2, PP, #0x55, lsl #12  ; [pp+0x55cf0] "assets/images/add_to_bag_glass.svg"
    //     0xb6465c: ldr             x2, [x2, #0xcf0]
    // 0xb64660: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb64660: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb64664: ldr             x4, [x4, #0xb0]
    // 0xb64668: r0 = SvgPicture.asset()
    //     0xb64668: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb6466c: ldur            x0, [fp, #-8]
    // 0xb64670: stur            x0, [fp, #-8]
    // 0xb64674: r0 = InkWell()
    //     0xb64674: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb64678: mov             x3, x0
    // 0xb6467c: ldur            x0, [fp, #-8]
    // 0xb64680: stur            x3, [fp, #-0x18]
    // 0xb64684: StoreField: r3->field_b = r0
    //     0xb64684: stur            w0, [x3, #0xb]
    // 0xb64688: ldur            x2, [fp, #-0x10]
    // 0xb6468c: r1 = Function '<anonymous closure>':.
    //     0xb6468c: add             x1, PP, #0x56, lsl #12  ; [pp+0x562e8] AnonymousClosure: (0xb64d54), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb64690: ldr             x1, [x1, #0x2e8]
    // 0xb64694: r0 = AllocateClosure()
    //     0xb64694: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb64698: mov             x1, x0
    // 0xb6469c: ldur            x0, [fp, #-0x18]
    // 0xb646a0: StoreField: r0->field_f = r1
    //     0xb646a0: stur            w1, [x0, #0xf]
    // 0xb646a4: r1 = true
    //     0xb646a4: add             x1, NULL, #0x20  ; true
    // 0xb646a8: StoreField: r0->field_43 = r1
    //     0xb646a8: stur            w1, [x0, #0x43]
    // 0xb646ac: r2 = Instance_BoxShape
    //     0xb646ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb646b0: ldr             x2, [x2, #0x80]
    // 0xb646b4: StoreField: r0->field_47 = r2
    //     0xb646b4: stur            w2, [x0, #0x47]
    // 0xb646b8: StoreField: r0->field_6f = r1
    //     0xb646b8: stur            w1, [x0, #0x6f]
    // 0xb646bc: r3 = false
    //     0xb646bc: add             x3, NULL, #0x30  ; false
    // 0xb646c0: StoreField: r0->field_73 = r3
    //     0xb646c0: stur            w3, [x0, #0x73]
    // 0xb646c4: StoreField: r0->field_83 = r1
    //     0xb646c4: stur            w1, [x0, #0x83]
    // 0xb646c8: StoreField: r0->field_7b = r3
    //     0xb646c8: stur            w3, [x0, #0x7b]
    // 0xb646cc: b               #0xb646f4
    // 0xb646d0: mov             x2, x0
    // 0xb646d4: r1 = true
    //     0xb646d4: add             x1, NULL, #0x20  ; true
    // 0xb646d8: r3 = false
    //     0xb646d8: add             x3, NULL, #0x30  ; false
    // 0xb646dc: r0 = Container()
    //     0xb646dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb646e0: mov             x1, x0
    // 0xb646e4: stur            x0, [fp, #-8]
    // 0xb646e8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb646e8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb646ec: r0 = Container()
    //     0xb646ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb646f0: ldur            x0, [fp, #-8]
    // 0xb646f4: stur            x0, [fp, #-8]
    // 0xb646f8: r0 = Container()
    //     0xb646f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb646fc: stur            x0, [fp, #-0x18]
    // 0xb64700: ldur            x16, [fp, #-0x20]
    // 0xb64704: r30 = Instance_EdgeInsets
    //     0xb64704: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb64708: ldr             lr, [lr, #0x980]
    // 0xb6470c: stp             lr, x16, [SP, #8]
    // 0xb64710: ldur            x16, [fp, #-8]
    // 0xb64714: str             x16, [SP]
    // 0xb64718: mov             x1, x0
    // 0xb6471c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb6471c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb64720: ldr             x4, [x4, #0xb40]
    // 0xb64724: r0 = Container()
    //     0xb64724: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb64728: ldur            x2, [fp, #-0x18]
    // 0xb6472c: b               #0xb64748
    // 0xb64730: r0 = Container()
    //     0xb64730: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb64734: mov             x1, x0
    // 0xb64738: stur            x0, [fp, #-8]
    // 0xb6473c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb6473c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb64740: r0 = Container()
    //     0xb64740: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb64744: ldur            x2, [fp, #-8]
    // 0xb64748: ldur            x0, [fp, #-0x38]
    // 0xb6474c: stur            x2, [fp, #-8]
    // 0xb64750: LoadField: r1 = r0->field_b
    //     0xb64750: ldur            w1, [x0, #0xb]
    // 0xb64754: LoadField: r3 = r0->field_f
    //     0xb64754: ldur            w3, [x0, #0xf]
    // 0xb64758: DecompressPointer r3
    //     0xb64758: add             x3, x3, HEAP, lsl #32
    // 0xb6475c: LoadField: r4 = r3->field_b
    //     0xb6475c: ldur            w4, [x3, #0xb]
    // 0xb64760: r3 = LoadInt32Instr(r1)
    //     0xb64760: sbfx            x3, x1, #1, #0x1f
    // 0xb64764: stur            x3, [fp, #-0x68]
    // 0xb64768: r1 = LoadInt32Instr(r4)
    //     0xb64768: sbfx            x1, x4, #1, #0x1f
    // 0xb6476c: cmp             x3, x1
    // 0xb64770: b.ne            #0xb6477c
    // 0xb64774: mov             x1, x0
    // 0xb64778: r0 = _growToNextCapacity()
    //     0xb64778: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6477c: ldur            x2, [fp, #-0x38]
    // 0xb64780: ldur            x3, [fp, #-0x68]
    // 0xb64784: add             x0, x3, #1
    // 0xb64788: lsl             x1, x0, #1
    // 0xb6478c: StoreField: r2->field_b = r1
    //     0xb6478c: stur            w1, [x2, #0xb]
    // 0xb64790: LoadField: r1 = r2->field_f
    //     0xb64790: ldur            w1, [x2, #0xf]
    // 0xb64794: DecompressPointer r1
    //     0xb64794: add             x1, x1, HEAP, lsl #32
    // 0xb64798: ldur            x0, [fp, #-8]
    // 0xb6479c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6479c: add             x25, x1, x3, lsl #2
    //     0xb647a0: add             x25, x25, #0xf
    //     0xb647a4: str             w0, [x25]
    //     0xb647a8: tbz             w0, #0, #0xb647c4
    //     0xb647ac: ldurb           w16, [x1, #-1]
    //     0xb647b0: ldurb           w17, [x0, #-1]
    //     0xb647b4: and             x16, x17, x16, lsr #2
    //     0xb647b8: tst             x16, HEAP, lsr #32
    //     0xb647bc: b.eq            #0xb647c4
    //     0xb647c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb647c4: b               #0xb647cc
    // 0xb647c8: mov             x2, x1
    // 0xb647cc: ldur            x1, [fp, #-0x30]
    // 0xb647d0: ldur            x0, [fp, #-0x28]
    // 0xb647d4: r0 = Row()
    //     0xb647d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb647d8: mov             x1, x0
    // 0xb647dc: r0 = Instance_Axis
    //     0xb647dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb647e0: stur            x1, [fp, #-8]
    // 0xb647e4: StoreField: r1->field_f = r0
    //     0xb647e4: stur            w0, [x1, #0xf]
    // 0xb647e8: r0 = Instance_MainAxisAlignment
    //     0xb647e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb647ec: ldr             x0, [x0, #0xa8]
    // 0xb647f0: StoreField: r1->field_13 = r0
    //     0xb647f0: stur            w0, [x1, #0x13]
    // 0xb647f4: r0 = Instance_MainAxisSize
    //     0xb647f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb647f8: ldr             x0, [x0, #0xa10]
    // 0xb647fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb647fc: stur            w0, [x1, #0x17]
    // 0xb64800: r0 = Instance_CrossAxisAlignment
    //     0xb64800: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb64804: ldr             x0, [x0, #0x890]
    // 0xb64808: StoreField: r1->field_1b = r0
    //     0xb64808: stur            w0, [x1, #0x1b]
    // 0xb6480c: r2 = Instance_VerticalDirection
    //     0xb6480c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb64810: ldr             x2, [x2, #0xa20]
    // 0xb64814: StoreField: r1->field_23 = r2
    //     0xb64814: stur            w2, [x1, #0x23]
    // 0xb64818: r3 = Instance_Clip
    //     0xb64818: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6481c: ldr             x3, [x3, #0x38]
    // 0xb64820: StoreField: r1->field_2b = r3
    //     0xb64820: stur            w3, [x1, #0x2b]
    // 0xb64824: StoreField: r1->field_2f = rZR
    //     0xb64824: stur            xzr, [x1, #0x2f]
    // 0xb64828: ldur            x4, [fp, #-0x38]
    // 0xb6482c: StoreField: r1->field_b = r4
    //     0xb6482c: stur            w4, [x1, #0xb]
    // 0xb64830: r0 = Padding()
    //     0xb64830: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb64834: mov             x3, x0
    // 0xb64838: r0 = Instance_EdgeInsets
    //     0xb64838: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb6483c: ldr             x0, [x0, #0x668]
    // 0xb64840: stur            x3, [fp, #-0x18]
    // 0xb64844: StoreField: r3->field_f = r0
    //     0xb64844: stur            w0, [x3, #0xf]
    // 0xb64848: ldur            x0, [fp, #-8]
    // 0xb6484c: StoreField: r3->field_b = r0
    //     0xb6484c: stur            w0, [x3, #0xb]
    // 0xb64850: r1 = Null
    //     0xb64850: mov             x1, NULL
    // 0xb64854: r2 = 4
    //     0xb64854: movz            x2, #0x4
    // 0xb64858: r0 = AllocateArray()
    //     0xb64858: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6485c: mov             x2, x0
    // 0xb64860: ldur            x0, [fp, #-0x28]
    // 0xb64864: stur            x2, [fp, #-8]
    // 0xb64868: StoreField: r2->field_f = r0
    //     0xb64868: stur            w0, [x2, #0xf]
    // 0xb6486c: ldur            x0, [fp, #-0x18]
    // 0xb64870: StoreField: r2->field_13 = r0
    //     0xb64870: stur            w0, [x2, #0x13]
    // 0xb64874: r1 = <Widget>
    //     0xb64874: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb64878: r0 = AllocateGrowableArray()
    //     0xb64878: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6487c: mov             x1, x0
    // 0xb64880: ldur            x0, [fp, #-8]
    // 0xb64884: stur            x1, [fp, #-0x18]
    // 0xb64888: StoreField: r1->field_f = r0
    //     0xb64888: stur            w0, [x1, #0xf]
    // 0xb6488c: r0 = 4
    //     0xb6488c: movz            x0, #0x4
    // 0xb64890: StoreField: r1->field_b = r0
    //     0xb64890: stur            w0, [x1, #0xb]
    // 0xb64894: r0 = Column()
    //     0xb64894: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb64898: mov             x3, x0
    // 0xb6489c: r0 = Instance_Axis
    //     0xb6489c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb648a0: stur            x3, [fp, #-8]
    // 0xb648a4: StoreField: r3->field_f = r0
    //     0xb648a4: stur            w0, [x3, #0xf]
    // 0xb648a8: r0 = Instance_MainAxisAlignment
    //     0xb648a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb648ac: ldr             x0, [x0, #0xa08]
    // 0xb648b0: StoreField: r3->field_13 = r0
    //     0xb648b0: stur            w0, [x3, #0x13]
    // 0xb648b4: r0 = Instance_MainAxisSize
    //     0xb648b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb648b8: ldr             x0, [x0, #0xdd0]
    // 0xb648bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb648bc: stur            w0, [x3, #0x17]
    // 0xb648c0: r0 = Instance_CrossAxisAlignment
    //     0xb648c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb648c4: ldr             x0, [x0, #0x890]
    // 0xb648c8: StoreField: r3->field_1b = r0
    //     0xb648c8: stur            w0, [x3, #0x1b]
    // 0xb648cc: r0 = Instance_VerticalDirection
    //     0xb648cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb648d0: ldr             x0, [x0, #0xa20]
    // 0xb648d4: StoreField: r3->field_23 = r0
    //     0xb648d4: stur            w0, [x3, #0x23]
    // 0xb648d8: r0 = Instance_Clip
    //     0xb648d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb648dc: ldr             x0, [x0, #0x38]
    // 0xb648e0: StoreField: r3->field_2b = r0
    //     0xb648e0: stur            w0, [x3, #0x2b]
    // 0xb648e4: StoreField: r3->field_2f = rZR
    //     0xb648e4: stur            xzr, [x3, #0x2f]
    // 0xb648e8: ldur            x0, [fp, #-0x18]
    // 0xb648ec: StoreField: r3->field_b = r0
    //     0xb648ec: stur            w0, [x3, #0xb]
    // 0xb648f0: ldur            x2, [fp, #-0x10]
    // 0xb648f4: r1 = Function '<anonymous closure>':.
    //     0xb648f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x562f0] AnonymousClosure: (0xb64bc8), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb648f8: ldr             x1, [x1, #0x2f0]
    // 0xb648fc: r0 = AllocateClosure()
    //     0xb648fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb64900: stur            x0, [fp, #-0x18]
    // 0xb64904: r0 = VisibilityDetector()
    //     0xb64904: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xb64908: mov             x1, x0
    // 0xb6490c: ldur            x0, [fp, #-0x18]
    // 0xb64910: stur            x1, [fp, #-0x20]
    // 0xb64914: StoreField: r1->field_f = r0
    //     0xb64914: stur            w0, [x1, #0xf]
    // 0xb64918: ldur            x0, [fp, #-8]
    // 0xb6491c: StoreField: r1->field_b = r0
    //     0xb6491c: stur            w0, [x1, #0xb]
    // 0xb64920: ldur            x0, [fp, #-0x30]
    // 0xb64924: StoreField: r1->field_7 = r0
    //     0xb64924: stur            w0, [x1, #7]
    // 0xb64928: r0 = InkWell()
    //     0xb64928: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb6492c: mov             x3, x0
    // 0xb64930: ldur            x0, [fp, #-0x20]
    // 0xb64934: stur            x3, [fp, #-8]
    // 0xb64938: StoreField: r3->field_b = r0
    //     0xb64938: stur            w0, [x3, #0xb]
    // 0xb6493c: ldur            x2, [fp, #-0x10]
    // 0xb64940: r1 = Function '<anonymous closure>':.
    //     0xb64940: add             x1, PP, #0x56, lsl #12  ; [pp+0x562f8] AnonymousClosure: (0xb64a60), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb64944: ldr             x1, [x1, #0x2f8]
    // 0xb64948: r0 = AllocateClosure()
    //     0xb64948: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6494c: mov             x1, x0
    // 0xb64950: ldur            x0, [fp, #-8]
    // 0xb64954: StoreField: r0->field_f = r1
    //     0xb64954: stur            w1, [x0, #0xf]
    // 0xb64958: r1 = true
    //     0xb64958: add             x1, NULL, #0x20  ; true
    // 0xb6495c: StoreField: r0->field_43 = r1
    //     0xb6495c: stur            w1, [x0, #0x43]
    // 0xb64960: r2 = Instance_BoxShape
    //     0xb64960: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb64964: ldr             x2, [x2, #0x80]
    // 0xb64968: StoreField: r0->field_47 = r2
    //     0xb64968: stur            w2, [x0, #0x47]
    // 0xb6496c: StoreField: r0->field_6f = r1
    //     0xb6496c: stur            w1, [x0, #0x6f]
    // 0xb64970: r2 = false
    //     0xb64970: add             x2, NULL, #0x30  ; false
    // 0xb64974: StoreField: r0->field_73 = r2
    //     0xb64974: stur            w2, [x0, #0x73]
    // 0xb64978: StoreField: r0->field_83 = r1
    //     0xb64978: stur            w1, [x0, #0x83]
    // 0xb6497c: StoreField: r0->field_7b = r2
    //     0xb6497c: stur            w2, [x0, #0x7b]
    // 0xb64980: LeaveFrame
    //     0xb64980: mov             SP, fp
    //     0xb64984: ldp             fp, lr, [SP], #0x10
    // 0xb64988: ret
    //     0xb64988: ret             
    // 0xb6498c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6498c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb64990: b               #0xb6345c
    // 0xb64994: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb64994: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb64998: r9 = _pageController
    //     0xb64998: add             x9, PP, #0x56, lsl #12  ; [pp+0x56300] Field <_ProductGridItemViewState@**********._pageController@**********>: late (offset: 0x1c)
    //     0xb6499c: ldr             x9, [x9, #0x300]
    // 0xb649a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb649a0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb649a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb649a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb649a8: SaveReg d0
    //     0xb649a8: str             q0, [SP, #-0x10]!
    // 0xb649ac: stp             x1, x2, [SP, #-0x10]!
    // 0xb649b0: r0 = AllocateDouble()
    //     0xb649b0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb649b4: ldp             x1, x2, [SP], #0x10
    // 0xb649b8: RestoreReg d0
    //     0xb649b8: ldr             q0, [SP], #0x10
    // 0xb649bc: b               #0xb63850
    // 0xb649c0: SaveReg d0
    //     0xb649c0: str             q0, [SP, #-0x10]!
    // 0xb649c4: SaveReg r0
    //     0xb649c4: str             x0, [SP, #-8]!
    // 0xb649c8: r0 = 74
    //     0xb649c8: movz            x0, #0x4a
    // 0xb649cc: r30 = DoubleToIntegerStub
    //     0xb649cc: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb649d0: LoadField: r30 = r30->field_7
    //     0xb649d0: ldur            lr, [lr, #7]
    // 0xb649d4: blr             lr
    // 0xb649d8: mov             x1, x0
    // 0xb649dc: RestoreReg r0
    //     0xb649dc: ldr             x0, [SP], #8
    // 0xb649e0: RestoreReg d0
    //     0xb649e0: ldr             q0, [SP], #0x10
    // 0xb649e4: b               #0xb63c60
    // 0xb649e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb649e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb649ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb649ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _shouldShowRating(/* No info */) {
    // ** addr: 0xb649f0, size: 0x70
    // 0xb649f0: EnterFrame
    //     0xb649f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb649f4: mov             fp, SP
    // 0xb649f8: AllocStack(0x10)
    //     0xb649f8: sub             SP, SP, #0x10
    // 0xb649fc: CheckStackOverflow
    //     0xb649fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb64a00: cmp             SP, x16
    //     0xb64a04: b.ls            #0xb64a58
    // 0xb64a08: cmp             w2, NULL
    // 0xb64a0c: b.eq            #0xb64a48
    // 0xb64a10: LoadField: r0 = r2->field_7b
    //     0xb64a10: ldur            w0, [x2, #0x7b]
    // 0xb64a14: DecompressPointer r0
    //     0xb64a14: add             x0, x0, HEAP, lsl #32
    // 0xb64a18: cmp             w0, NULL
    // 0xb64a1c: b.eq            #0xb64a48
    // 0xb64a20: LoadField: r1 = r0->field_7
    //     0xb64a20: ldur            w1, [x0, #7]
    // 0xb64a24: DecompressPointer r1
    //     0xb64a24: add             x1, x1, HEAP, lsl #32
    // 0xb64a28: cmp             w1, NULL
    // 0xb64a2c: b.eq            #0xb64a48
    // 0xb64a30: r16 = 0.000000
    //     0xb64a30: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb64a34: stp             x16, x1, [SP]
    // 0xb64a38: r0 = ==()
    //     0xb64a38: bl              #0x169ef54  ; [dart:core] _Double::==
    // 0xb64a3c: eor             x1, x0, #0x10
    // 0xb64a40: mov             x0, x1
    // 0xb64a44: b               #0xb64a4c
    // 0xb64a48: r0 = false
    //     0xb64a48: add             x0, NULL, #0x30  ; false
    // 0xb64a4c: LeaveFrame
    //     0xb64a4c: mov             SP, fp
    //     0xb64a50: ldp             fp, lr, [SP], #0x10
    // 0xb64a54: ret
    //     0xb64a54: ret             
    // 0xb64a58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb64a58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb64a5c: b               #0xb64a08
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb64a60, size: 0x168
    // 0xb64a60: EnterFrame
    //     0xb64a60: stp             fp, lr, [SP, #-0x10]!
    //     0xb64a64: mov             fp, SP
    // 0xb64a68: AllocStack(0x48)
    //     0xb64a68: sub             SP, SP, #0x48
    // 0xb64a6c: SetupParameters()
    //     0xb64a6c: ldr             x0, [fp, #0x10]
    //     0xb64a70: ldur            w1, [x0, #0x17]
    //     0xb64a74: add             x1, x1, HEAP, lsl #32
    // 0xb64a78: CheckStackOverflow
    //     0xb64a78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb64a7c: cmp             SP, x16
    //     0xb64a80: b.ls            #0xb64bbc
    // 0xb64a84: LoadField: r0 = r1->field_b
    //     0xb64a84: ldur            w0, [x1, #0xb]
    // 0xb64a88: DecompressPointer r0
    //     0xb64a88: add             x0, x0, HEAP, lsl #32
    // 0xb64a8c: LoadField: r2 = r0->field_f
    //     0xb64a8c: ldur            w2, [x0, #0xf]
    // 0xb64a90: DecompressPointer r2
    //     0xb64a90: add             x2, x2, HEAP, lsl #32
    // 0xb64a94: LoadField: r0 = r2->field_b
    //     0xb64a94: ldur            w0, [x2, #0xb]
    // 0xb64a98: DecompressPointer r0
    //     0xb64a98: add             x0, x0, HEAP, lsl #32
    // 0xb64a9c: cmp             w0, NULL
    // 0xb64aa0: b.eq            #0xb64bc4
    // 0xb64aa4: LoadField: r2 = r0->field_2f
    //     0xb64aa4: ldur            w2, [x0, #0x2f]
    // 0xb64aa8: DecompressPointer r2
    //     0xb64aa8: add             x2, x2, HEAP, lsl #32
    // 0xb64aac: cmp             w2, NULL
    // 0xb64ab0: b.ne            #0xb64ab8
    // 0xb64ab4: r2 = ""
    //     0xb64ab4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64ab8: LoadField: r3 = r0->field_2b
    //     0xb64ab8: ldur            w3, [x0, #0x2b]
    // 0xb64abc: DecompressPointer r3
    //     0xb64abc: add             x3, x3, HEAP, lsl #32
    // 0xb64ac0: cmp             w3, NULL
    // 0xb64ac4: b.ne            #0xb64acc
    // 0xb64ac8: r3 = ""
    //     0xb64ac8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64acc: LoadField: r4 = r0->field_37
    //     0xb64acc: ldur            w4, [x0, #0x37]
    // 0xb64ad0: DecompressPointer r4
    //     0xb64ad0: add             x4, x4, HEAP, lsl #32
    // 0xb64ad4: cmp             w4, NULL
    // 0xb64ad8: b.ne            #0xb64ae0
    // 0xb64adc: r4 = ""
    //     0xb64adc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64ae0: LoadField: r5 = r0->field_3b
    //     0xb64ae0: ldur            w5, [x0, #0x3b]
    // 0xb64ae4: DecompressPointer r5
    //     0xb64ae4: add             x5, x5, HEAP, lsl #32
    // 0xb64ae8: LoadField: r6 = r0->field_33
    //     0xb64ae8: ldur            w6, [x0, #0x33]
    // 0xb64aec: DecompressPointer r6
    //     0xb64aec: add             x6, x6, HEAP, lsl #32
    // 0xb64af0: cmp             w6, NULL
    // 0xb64af4: b.ne            #0xb64afc
    // 0xb64af8: r6 = ""
    //     0xb64af8: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64afc: LoadField: r7 = r0->field_f
    //     0xb64afc: ldur            w7, [x0, #0xf]
    // 0xb64b00: DecompressPointer r7
    //     0xb64b00: add             x7, x7, HEAP, lsl #32
    // 0xb64b04: cmp             w7, NULL
    // 0xb64b08: b.ne            #0xb64b10
    // 0xb64b0c: r7 = ""
    //     0xb64b0c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64b10: LoadField: r8 = r1->field_f
    //     0xb64b10: ldur            w8, [x1, #0xf]
    // 0xb64b14: DecompressPointer r8
    //     0xb64b14: add             x8, x8, HEAP, lsl #32
    // 0xb64b18: cmp             w8, NULL
    // 0xb64b1c: b.ne            #0xb64b28
    // 0xb64b20: r1 = Null
    //     0xb64b20: mov             x1, NULL
    // 0xb64b24: b               #0xb64b30
    // 0xb64b28: LoadField: r1 = r8->field_2b
    //     0xb64b28: ldur            w1, [x8, #0x2b]
    // 0xb64b2c: DecompressPointer r1
    //     0xb64b2c: add             x1, x1, HEAP, lsl #32
    // 0xb64b30: cmp             w1, NULL
    // 0xb64b34: b.ne            #0xb64b3c
    // 0xb64b38: r1 = ""
    //     0xb64b38: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64b3c: cmp             w8, NULL
    // 0xb64b40: b.ne            #0xb64b4c
    // 0xb64b44: r8 = Null
    //     0xb64b44: mov             x8, NULL
    // 0xb64b48: b               #0xb64b6c
    // 0xb64b4c: LoadField: r9 = r8->field_3b
    //     0xb64b4c: ldur            w9, [x8, #0x3b]
    // 0xb64b50: DecompressPointer r9
    //     0xb64b50: add             x9, x9, HEAP, lsl #32
    // 0xb64b54: cmp             w9, NULL
    // 0xb64b58: b.ne            #0xb64b64
    // 0xb64b5c: r8 = Null
    //     0xb64b5c: mov             x8, NULL
    // 0xb64b60: b               #0xb64b6c
    // 0xb64b64: LoadField: r8 = r9->field_b
    //     0xb64b64: ldur            w8, [x9, #0xb]
    // 0xb64b68: DecompressPointer r8
    //     0xb64b68: add             x8, x8, HEAP, lsl #32
    // 0xb64b6c: cmp             w8, NULL
    // 0xb64b70: b.ne            #0xb64b78
    // 0xb64b74: r8 = ""
    //     0xb64b74: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64b78: LoadField: r9 = r0->field_53
    //     0xb64b78: ldur            w9, [x0, #0x53]
    // 0xb64b7c: DecompressPointer r9
    //     0xb64b7c: add             x9, x9, HEAP, lsl #32
    // 0xb64b80: stp             x2, x9, [SP, #0x38]
    // 0xb64b84: stp             x4, x3, [SP, #0x28]
    // 0xb64b88: stp             x6, x5, [SP, #0x18]
    // 0xb64b8c: stp             x1, x7, [SP, #8]
    // 0xb64b90: str             x8, [SP]
    // 0xb64b94: r4 = 0
    //     0xb64b94: movz            x4, #0
    // 0xb64b98: ldr             x0, [SP, #0x40]
    // 0xb64b9c: r16 = UnlinkedCall_0x613b5c
    //     0xb64b9c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56308] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb64ba0: add             x16, x16, #0x308
    // 0xb64ba4: ldp             x5, lr, [x16]
    // 0xb64ba8: blr             lr
    // 0xb64bac: r0 = Null
    //     0xb64bac: mov             x0, NULL
    // 0xb64bb0: LeaveFrame
    //     0xb64bb0: mov             SP, fp
    //     0xb64bb4: ldp             fp, lr, [SP], #0x10
    // 0xb64bb8: ret
    //     0xb64bb8: ret             
    // 0xb64bbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb64bbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb64bc0: b               #0xb64a84
    // 0xb64bc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb64bc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xb64bc8, size: 0x18c
    // 0xb64bc8: EnterFrame
    //     0xb64bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb64bcc: mov             fp, SP
    // 0xb64bd0: AllocStack(0x58)
    //     0xb64bd0: sub             SP, SP, #0x58
    // 0xb64bd4: SetupParameters()
    //     0xb64bd4: ldr             x0, [fp, #0x18]
    //     0xb64bd8: ldur            w2, [x0, #0x17]
    //     0xb64bdc: add             x2, x2, HEAP, lsl #32
    //     0xb64be0: stur            x2, [fp, #-8]
    // 0xb64be4: CheckStackOverflow
    //     0xb64be4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb64be8: cmp             SP, x16
    //     0xb64bec: b.ls            #0xb64d48
    // 0xb64bf0: ldr             x1, [fp, #0x10]
    // 0xb64bf4: r0 = visibleFraction()
    //     0xb64bf4: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xb64bf8: mov             v1.16b, v0.16b
    // 0xb64bfc: d0 = 0.500000
    //     0xb64bfc: fmov            d0, #0.50000000
    // 0xb64c00: fcmp            d1, d0
    // 0xb64c04: b.le            #0xb64d38
    // 0xb64c08: ldur            x0, [fp, #-8]
    // 0xb64c0c: LoadField: r1 = r0->field_b
    //     0xb64c0c: ldur            w1, [x0, #0xb]
    // 0xb64c10: DecompressPointer r1
    //     0xb64c10: add             x1, x1, HEAP, lsl #32
    // 0xb64c14: LoadField: r2 = r1->field_f
    //     0xb64c14: ldur            w2, [x1, #0xf]
    // 0xb64c18: DecompressPointer r2
    //     0xb64c18: add             x2, x2, HEAP, lsl #32
    // 0xb64c1c: LoadField: r1 = r2->field_b
    //     0xb64c1c: ldur            w1, [x2, #0xb]
    // 0xb64c20: DecompressPointer r1
    //     0xb64c20: add             x1, x1, HEAP, lsl #32
    // 0xb64c24: stur            x1, [fp, #-0x28]
    // 0xb64c28: cmp             w1, NULL
    // 0xb64c2c: b.eq            #0xb64d50
    // 0xb64c30: LoadField: r2 = r0->field_f
    //     0xb64c30: ldur            w2, [x0, #0xf]
    // 0xb64c34: DecompressPointer r2
    //     0xb64c34: add             x2, x2, HEAP, lsl #32
    // 0xb64c38: cmp             w2, NULL
    // 0xb64c3c: b.ne            #0xb64c48
    // 0xb64c40: r0 = Null
    //     0xb64c40: mov             x0, NULL
    // 0xb64c44: b               #0xb64c50
    // 0xb64c48: LoadField: r0 = r2->field_53
    //     0xb64c48: ldur            w0, [x2, #0x53]
    // 0xb64c4c: DecompressPointer r0
    //     0xb64c4c: add             x0, x0, HEAP, lsl #32
    // 0xb64c50: stur            x0, [fp, #-0x20]
    // 0xb64c54: cmp             w2, NULL
    // 0xb64c58: b.ne            #0xb64c64
    // 0xb64c5c: r3 = Null
    //     0xb64c5c: mov             x3, NULL
    // 0xb64c60: b               #0xb64c88
    // 0xb64c64: LoadField: r3 = r2->field_3b
    //     0xb64c64: ldur            w3, [x2, #0x3b]
    // 0xb64c68: DecompressPointer r3
    //     0xb64c68: add             x3, x3, HEAP, lsl #32
    // 0xb64c6c: cmp             w3, NULL
    // 0xb64c70: b.ne            #0xb64c7c
    // 0xb64c74: r3 = Null
    //     0xb64c74: mov             x3, NULL
    // 0xb64c78: b               #0xb64c88
    // 0xb64c7c: LoadField: r4 = r3->field_b
    //     0xb64c7c: ldur            w4, [x3, #0xb]
    // 0xb64c80: DecompressPointer r4
    //     0xb64c80: add             x4, x4, HEAP, lsl #32
    // 0xb64c84: mov             x3, x4
    // 0xb64c88: stur            x3, [fp, #-0x18]
    // 0xb64c8c: cmp             w2, NULL
    // 0xb64c90: b.ne            #0xb64c9c
    // 0xb64c94: r4 = Null
    //     0xb64c94: mov             x4, NULL
    // 0xb64c98: b               #0xb64ca4
    // 0xb64c9c: LoadField: r4 = r2->field_2b
    //     0xb64c9c: ldur            w4, [x2, #0x2b]
    // 0xb64ca0: DecompressPointer r4
    //     0xb64ca0: add             x4, x4, HEAP, lsl #32
    // 0xb64ca4: stur            x4, [fp, #-0x10]
    // 0xb64ca8: cmp             w2, NULL
    // 0xb64cac: b.ne            #0xb64cb8
    // 0xb64cb0: r5 = Null
    //     0xb64cb0: mov             x5, NULL
    // 0xb64cb4: b               #0xb64cc0
    // 0xb64cb8: LoadField: r5 = r2->field_57
    //     0xb64cb8: ldur            w5, [x2, #0x57]
    // 0xb64cbc: DecompressPointer r5
    //     0xb64cbc: add             x5, x5, HEAP, lsl #32
    // 0xb64cc0: stur            x5, [fp, #-8]
    // 0xb64cc4: cmp             w2, NULL
    // 0xb64cc8: b.ne            #0xb64cd4
    // 0xb64ccc: r2 = Null
    //     0xb64ccc: mov             x2, NULL
    // 0xb64cd0: b               #0xb64ce0
    // 0xb64cd4: LoadField: r6 = r2->field_7b
    //     0xb64cd4: ldur            w6, [x2, #0x7b]
    // 0xb64cd8: DecompressPointer r6
    //     0xb64cd8: add             x6, x6, HEAP, lsl #32
    // 0xb64cdc: mov             x2, x6
    // 0xb64ce0: cmp             w2, NULL
    // 0xb64ce4: b.ne            #0xb64cf4
    // 0xb64ce8: r0 = ProductRating()
    //     0xb64ce8: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xb64cec: mov             x1, x0
    // 0xb64cf0: b               #0xb64cf8
    // 0xb64cf4: mov             x1, x2
    // 0xb64cf8: ldur            x0, [fp, #-0x28]
    // 0xb64cfc: LoadField: r2 = r0->field_4b
    //     0xb64cfc: ldur            w2, [x0, #0x4b]
    // 0xb64d00: DecompressPointer r2
    //     0xb64d00: add             x2, x2, HEAP, lsl #32
    // 0xb64d04: ldur            x16, [fp, #-0x20]
    // 0xb64d08: stp             x16, x2, [SP, #0x20]
    // 0xb64d0c: ldur            x16, [fp, #-0x18]
    // 0xb64d10: ldur            lr, [fp, #-0x10]
    // 0xb64d14: stp             lr, x16, [SP, #0x10]
    // 0xb64d18: ldur            x16, [fp, #-8]
    // 0xb64d1c: stp             x1, x16, [SP]
    // 0xb64d20: r4 = 0
    //     0xb64d20: movz            x4, #0
    // 0xb64d24: ldr             x0, [SP, #0x28]
    // 0xb64d28: r16 = UnlinkedCall_0x613b5c
    //     0xb64d28: add             x16, PP, #0x56, lsl #12  ; [pp+0x56318] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb64d2c: add             x16, x16, #0x318
    // 0xb64d30: ldp             x5, lr, [x16]
    // 0xb64d34: blr             lr
    // 0xb64d38: r0 = Null
    //     0xb64d38: mov             x0, NULL
    // 0xb64d3c: LeaveFrame
    //     0xb64d3c: mov             SP, fp
    //     0xb64d40: ldp             fp, lr, [SP], #0x10
    // 0xb64d44: ret
    //     0xb64d44: ret             
    // 0xb64d48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb64d48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb64d4c: b               #0xb64bf0
    // 0xb64d50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb64d50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb64d54, size: 0x1e8
    // 0xb64d54: EnterFrame
    //     0xb64d54: stp             fp, lr, [SP, #-0x10]!
    //     0xb64d58: mov             fp, SP
    // 0xb64d5c: AllocStack(0x30)
    //     0xb64d5c: sub             SP, SP, #0x30
    // 0xb64d60: SetupParameters()
    //     0xb64d60: ldr             x0, [fp, #0x10]
    //     0xb64d64: ldur            w1, [x0, #0x17]
    //     0xb64d68: add             x1, x1, HEAP, lsl #32
    // 0xb64d6c: CheckStackOverflow
    //     0xb64d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb64d70: cmp             SP, x16
    //     0xb64d74: b.ls            #0xb64f2c
    // 0xb64d78: LoadField: r0 = r1->field_f
    //     0xb64d78: ldur            w0, [x1, #0xf]
    // 0xb64d7c: DecompressPointer r0
    //     0xb64d7c: add             x0, x0, HEAP, lsl #32
    // 0xb64d80: stur            x0, [fp, #-0x10]
    // 0xb64d84: cmp             w0, NULL
    // 0xb64d88: b.ne            #0xb64d94
    // 0xb64d8c: r2 = Null
    //     0xb64d8c: mov             x2, NULL
    // 0xb64d90: b               #0xb64d9c
    // 0xb64d94: LoadField: r2 = r0->field_4f
    //     0xb64d94: ldur            w2, [x0, #0x4f]
    // 0xb64d98: DecompressPointer r2
    //     0xb64d98: add             x2, x2, HEAP, lsl #32
    // 0xb64d9c: cmp             w2, NULL
    // 0xb64da0: b.eq            #0xb64da8
    // 0xb64da4: tbz             w2, #4, #0xb64f1c
    // 0xb64da8: LoadField: r2 = r1->field_b
    //     0xb64da8: ldur            w2, [x1, #0xb]
    // 0xb64dac: DecompressPointer r2
    //     0xb64dac: add             x2, x2, HEAP, lsl #32
    // 0xb64db0: stur            x2, [fp, #-8]
    // 0xb64db4: LoadField: r1 = r2->field_f
    //     0xb64db4: ldur            w1, [x2, #0xf]
    // 0xb64db8: DecompressPointer r1
    //     0xb64db8: add             x1, x1, HEAP, lsl #32
    // 0xb64dbc: LoadField: r3 = r1->field_b
    //     0xb64dbc: ldur            w3, [x1, #0xb]
    // 0xb64dc0: DecompressPointer r3
    //     0xb64dc0: add             x3, x3, HEAP, lsl #32
    // 0xb64dc4: cmp             w3, NULL
    // 0xb64dc8: b.eq            #0xb64f34
    // 0xb64dcc: cmp             w0, NULL
    // 0xb64dd0: b.ne            #0xb64ddc
    // 0xb64dd4: r1 = Null
    //     0xb64dd4: mov             x1, NULL
    // 0xb64dd8: b               #0xb64de4
    // 0xb64ddc: LoadField: r1 = r0->field_2b
    //     0xb64ddc: ldur            w1, [x0, #0x2b]
    // 0xb64de0: DecompressPointer r1
    //     0xb64de0: add             x1, x1, HEAP, lsl #32
    // 0xb64de4: cmp             w0, NULL
    // 0xb64de8: b.ne            #0xb64df4
    // 0xb64dec: r4 = Null
    //     0xb64dec: mov             x4, NULL
    // 0xb64df0: b               #0xb64e18
    // 0xb64df4: LoadField: r4 = r0->field_3b
    //     0xb64df4: ldur            w4, [x0, #0x3b]
    // 0xb64df8: DecompressPointer r4
    //     0xb64df8: add             x4, x4, HEAP, lsl #32
    // 0xb64dfc: cmp             w4, NULL
    // 0xb64e00: b.ne            #0xb64e0c
    // 0xb64e04: r4 = Null
    //     0xb64e04: mov             x4, NULL
    // 0xb64e08: b               #0xb64e18
    // 0xb64e0c: LoadField: r5 = r4->field_7
    //     0xb64e0c: ldur            w5, [x4, #7]
    // 0xb64e10: DecompressPointer r5
    //     0xb64e10: add             x5, x5, HEAP, lsl #32
    // 0xb64e14: mov             x4, x5
    // 0xb64e18: cmp             w0, NULL
    // 0xb64e1c: b.ne            #0xb64e28
    // 0xb64e20: r5 = Null
    //     0xb64e20: mov             x5, NULL
    // 0xb64e24: b               #0xb64e4c
    // 0xb64e28: LoadField: r5 = r0->field_3b
    //     0xb64e28: ldur            w5, [x0, #0x3b]
    // 0xb64e2c: DecompressPointer r5
    //     0xb64e2c: add             x5, x5, HEAP, lsl #32
    // 0xb64e30: cmp             w5, NULL
    // 0xb64e34: b.ne            #0xb64e40
    // 0xb64e38: r5 = Null
    //     0xb64e38: mov             x5, NULL
    // 0xb64e3c: b               #0xb64e4c
    // 0xb64e40: LoadField: r6 = r5->field_b
    //     0xb64e40: ldur            w6, [x5, #0xb]
    // 0xb64e44: DecompressPointer r6
    //     0xb64e44: add             x6, x6, HEAP, lsl #32
    // 0xb64e48: mov             x5, x6
    // 0xb64e4c: LoadField: r6 = r3->field_47
    //     0xb64e4c: ldur            w6, [x3, #0x47]
    // 0xb64e50: DecompressPointer r6
    //     0xb64e50: add             x6, x6, HEAP, lsl #32
    // 0xb64e54: stp             x1, x6, [SP, #0x10]
    // 0xb64e58: stp             x5, x4, [SP]
    // 0xb64e5c: r4 = 0
    //     0xb64e5c: movz            x4, #0
    // 0xb64e60: ldr             x0, [SP, #0x18]
    // 0xb64e64: r16 = UnlinkedCall_0x613b5c
    //     0xb64e64: add             x16, PP, #0x56, lsl #12  ; [pp+0x56328] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb64e68: add             x16, x16, #0x328
    // 0xb64e6c: ldp             x5, lr, [x16]
    // 0xb64e70: blr             lr
    // 0xb64e74: ldur            x0, [fp, #-8]
    // 0xb64e78: LoadField: r1 = r0->field_f
    //     0xb64e78: ldur            w1, [x0, #0xf]
    // 0xb64e7c: DecompressPointer r1
    //     0xb64e7c: add             x1, x1, HEAP, lsl #32
    // 0xb64e80: LoadField: r0 = r1->field_b
    //     0xb64e80: ldur            w0, [x1, #0xb]
    // 0xb64e84: DecompressPointer r0
    //     0xb64e84: add             x0, x0, HEAP, lsl #32
    // 0xb64e88: stur            x0, [fp, #-8]
    // 0xb64e8c: cmp             w0, NULL
    // 0xb64e90: b.eq            #0xb64f38
    // 0xb64e94: ldur            x1, [fp, #-0x10]
    // 0xb64e98: cmp             w1, NULL
    // 0xb64e9c: b.ne            #0xb64eac
    // 0xb64ea0: r0 = Entity()
    //     0xb64ea0: bl              #0x9118a4  ; AllocateEntityStub -> Entity (size=0xc0)
    // 0xb64ea4: mov             x1, x0
    // 0xb64ea8: b               #0xb64eb0
    // 0xb64eac: ldur            x1, [fp, #-0x10]
    // 0xb64eb0: ldur            x0, [fp, #-8]
    // 0xb64eb4: LoadField: r2 = r0->field_27
    //     0xb64eb4: ldur            w2, [x0, #0x27]
    // 0xb64eb8: DecompressPointer r2
    //     0xb64eb8: add             x2, x2, HEAP, lsl #32
    // 0xb64ebc: cmp             w2, NULL
    // 0xb64ec0: b.ne            #0xb64ecc
    // 0xb64ec4: r3 = ""
    //     0xb64ec4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64ec8: b               #0xb64ed0
    // 0xb64ecc: mov             x3, x2
    // 0xb64ed0: ldur            x2, [fp, #-0x10]
    // 0xb64ed4: cmp             w2, NULL
    // 0xb64ed8: b.ne            #0xb64ee4
    // 0xb64edc: r2 = Null
    //     0xb64edc: mov             x2, NULL
    // 0xb64ee0: b               #0xb64ef0
    // 0xb64ee4: LoadField: r4 = r2->field_b3
    //     0xb64ee4: ldur            w4, [x2, #0xb3]
    // 0xb64ee8: DecompressPointer r4
    //     0xb64ee8: add             x4, x4, HEAP, lsl #32
    // 0xb64eec: mov             x2, x4
    // 0xb64ef0: cmp             w2, NULL
    // 0xb64ef4: b.ne            #0xb64efc
    // 0xb64ef8: r2 = ""
    //     0xb64ef8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb64efc: LoadField: r4 = r0->field_57
    //     0xb64efc: ldur            w4, [x0, #0x57]
    // 0xb64f00: DecompressPointer r4
    //     0xb64f00: add             x4, x4, HEAP, lsl #32
    // 0xb64f04: stp             x1, x4, [SP, #0x10]
    // 0xb64f08: stp             x2, x3, [SP]
    // 0xb64f0c: mov             x0, x4
    // 0xb64f10: ClosureCall
    //     0xb64f10: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xb64f14: ldur            x2, [x0, #0x1f]
    //     0xb64f18: blr             x2
    // 0xb64f1c: r0 = Null
    //     0xb64f1c: mov             x0, NULL
    // 0xb64f20: LeaveFrame
    //     0xb64f20: mov             SP, fp
    //     0xb64f24: ldp             fp, lr, [SP], #0x10
    // 0xb64f28: ret
    //     0xb64f28: ret             
    // 0xb64f2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb64f2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb64f30: b               #0xb64d78
    // 0xb64f34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb64f34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb64f38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb64f38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb64f3c, size: 0x1f4
    // 0xb64f3c: EnterFrame
    //     0xb64f3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb64f40: mov             fp, SP
    // 0xb64f44: AllocStack(0x20)
    //     0xb64f44: sub             SP, SP, #0x20
    // 0xb64f48: SetupParameters()
    //     0xb64f48: ldr             x0, [fp, #0x20]
    //     0xb64f4c: ldur            w1, [x0, #0x17]
    //     0xb64f50: add             x1, x1, HEAP, lsl #32
    // 0xb64f54: CheckStackOverflow
    //     0xb64f54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb64f58: cmp             SP, x16
    //     0xb64f5c: b.ls            #0xb65124
    // 0xb64f60: LoadField: r0 = r1->field_b
    //     0xb64f60: ldur            w0, [x1, #0xb]
    // 0xb64f64: DecompressPointer r0
    //     0xb64f64: add             x0, x0, HEAP, lsl #32
    // 0xb64f68: LoadField: r2 = r0->field_f
    //     0xb64f68: ldur            w2, [x0, #0xf]
    // 0xb64f6c: DecompressPointer r2
    //     0xb64f6c: add             x2, x2, HEAP, lsl #32
    // 0xb64f70: LoadField: r4 = r1->field_f
    //     0xb64f70: ldur            w4, [x1, #0xf]
    // 0xb64f74: DecompressPointer r4
    //     0xb64f74: add             x4, x4, HEAP, lsl #32
    // 0xb64f78: stur            x4, [fp, #-8]
    // 0xb64f7c: cmp             w4, NULL
    // 0xb64f80: b.ne            #0xb64f8c
    // 0xb64f84: r0 = Null
    //     0xb64f84: mov             x0, NULL
    // 0xb64f88: b               #0xb64fe0
    // 0xb64f8c: LoadField: r3 = r4->field_37
    //     0xb64f8c: ldur            w3, [x4, #0x37]
    // 0xb64f90: DecompressPointer r3
    //     0xb64f90: add             x3, x3, HEAP, lsl #32
    // 0xb64f94: cmp             w3, NULL
    // 0xb64f98: b.ne            #0xb64fa4
    // 0xb64f9c: r0 = Null
    //     0xb64f9c: mov             x0, NULL
    // 0xb64fa0: b               #0xb64fe0
    // 0xb64fa4: ldr             x0, [fp, #0x10]
    // 0xb64fa8: LoadField: r1 = r3->field_b
    //     0xb64fa8: ldur            w1, [x3, #0xb]
    // 0xb64fac: r5 = LoadInt32Instr(r0)
    //     0xb64fac: sbfx            x5, x0, #1, #0x1f
    //     0xb64fb0: tbz             w0, #0, #0xb64fb8
    //     0xb64fb4: ldur            x5, [x0, #7]
    // 0xb64fb8: r0 = LoadInt32Instr(r1)
    //     0xb64fb8: sbfx            x0, x1, #1, #0x1f
    // 0xb64fbc: mov             x1, x5
    // 0xb64fc0: cmp             x1, x0
    // 0xb64fc4: b.hs            #0xb6512c
    // 0xb64fc8: LoadField: r0 = r3->field_f
    //     0xb64fc8: ldur            w0, [x3, #0xf]
    // 0xb64fcc: DecompressPointer r0
    //     0xb64fcc: add             x0, x0, HEAP, lsl #32
    // 0xb64fd0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb64fd0: add             x16, x0, x5, lsl #2
    //     0xb64fd4: ldur            w1, [x16, #0xf]
    // 0xb64fd8: DecompressPointer r1
    //     0xb64fd8: add             x1, x1, HEAP, lsl #32
    // 0xb64fdc: mov             x0, x1
    // 0xb64fe0: mov             x1, x2
    // 0xb64fe4: mov             x2, x0
    // 0xb64fe8: mov             x3, x4
    // 0xb64fec: r0 = glassThemeSlider()
    //     0xb64fec: bl              #0xb65130  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::glassThemeSlider
    // 0xb64ff0: mov             x1, x0
    // 0xb64ff4: ldur            x0, [fp, #-8]
    // 0xb64ff8: stur            x1, [fp, #-0x10]
    // 0xb64ffc: cmp             w0, NULL
    // 0xb65000: b.ne            #0xb6500c
    // 0xb65004: r0 = Null
    //     0xb65004: mov             x0, NULL
    // 0xb65008: b               #0xb65018
    // 0xb6500c: LoadField: r2 = r0->field_bb
    //     0xb6500c: ldur            w2, [x0, #0xbb]
    // 0xb65010: DecompressPointer r2
    //     0xb65010: add             x2, x2, HEAP, lsl #32
    // 0xb65014: mov             x0, x2
    // 0xb65018: cmp             w0, NULL
    // 0xb6501c: b.ne            #0xb65024
    // 0xb65020: r0 = false
    //     0xb65020: add             x0, NULL, #0x30  ; false
    // 0xb65024: stur            x0, [fp, #-8]
    // 0xb65028: r0 = SvgPicture()
    //     0xb65028: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb6502c: mov             x1, x0
    // 0xb65030: r2 = "assets/images/free-gift-icon.svg"
    //     0xb65030: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xb65034: ldr             x2, [x2, #0xd40]
    // 0xb65038: stur            x0, [fp, #-0x18]
    // 0xb6503c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb6503c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb65040: r0 = SvgPicture.asset()
    //     0xb65040: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb65044: r0 = Padding()
    //     0xb65044: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb65048: mov             x1, x0
    // 0xb6504c: r0 = Instance_EdgeInsets
    //     0xb6504c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xb65050: ldr             x0, [x0, #0xd48]
    // 0xb65054: stur            x1, [fp, #-0x20]
    // 0xb65058: StoreField: r1->field_f = r0
    //     0xb65058: stur            w0, [x1, #0xf]
    // 0xb6505c: ldur            x0, [fp, #-0x18]
    // 0xb65060: StoreField: r1->field_b = r0
    //     0xb65060: stur            w0, [x1, #0xb]
    // 0xb65064: r0 = Visibility()
    //     0xb65064: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb65068: mov             x3, x0
    // 0xb6506c: ldur            x0, [fp, #-0x20]
    // 0xb65070: stur            x3, [fp, #-0x18]
    // 0xb65074: StoreField: r3->field_b = r0
    //     0xb65074: stur            w0, [x3, #0xb]
    // 0xb65078: r0 = Instance_SizedBox
    //     0xb65078: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb6507c: StoreField: r3->field_f = r0
    //     0xb6507c: stur            w0, [x3, #0xf]
    // 0xb65080: ldur            x0, [fp, #-8]
    // 0xb65084: StoreField: r3->field_13 = r0
    //     0xb65084: stur            w0, [x3, #0x13]
    // 0xb65088: r0 = false
    //     0xb65088: add             x0, NULL, #0x30  ; false
    // 0xb6508c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb6508c: stur            w0, [x3, #0x17]
    // 0xb65090: StoreField: r3->field_1b = r0
    //     0xb65090: stur            w0, [x3, #0x1b]
    // 0xb65094: StoreField: r3->field_1f = r0
    //     0xb65094: stur            w0, [x3, #0x1f]
    // 0xb65098: StoreField: r3->field_23 = r0
    //     0xb65098: stur            w0, [x3, #0x23]
    // 0xb6509c: StoreField: r3->field_27 = r0
    //     0xb6509c: stur            w0, [x3, #0x27]
    // 0xb650a0: StoreField: r3->field_2b = r0
    //     0xb650a0: stur            w0, [x3, #0x2b]
    // 0xb650a4: r1 = Null
    //     0xb650a4: mov             x1, NULL
    // 0xb650a8: r2 = 4
    //     0xb650a8: movz            x2, #0x4
    // 0xb650ac: r0 = AllocateArray()
    //     0xb650ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb650b0: mov             x2, x0
    // 0xb650b4: ldur            x0, [fp, #-0x10]
    // 0xb650b8: stur            x2, [fp, #-8]
    // 0xb650bc: StoreField: r2->field_f = r0
    //     0xb650bc: stur            w0, [x2, #0xf]
    // 0xb650c0: ldur            x0, [fp, #-0x18]
    // 0xb650c4: StoreField: r2->field_13 = r0
    //     0xb650c4: stur            w0, [x2, #0x13]
    // 0xb650c8: r1 = <Widget>
    //     0xb650c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb650cc: r0 = AllocateGrowableArray()
    //     0xb650cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb650d0: mov             x1, x0
    // 0xb650d4: ldur            x0, [fp, #-8]
    // 0xb650d8: stur            x1, [fp, #-0x10]
    // 0xb650dc: StoreField: r1->field_f = r0
    //     0xb650dc: stur            w0, [x1, #0xf]
    // 0xb650e0: r0 = 4
    //     0xb650e0: movz            x0, #0x4
    // 0xb650e4: StoreField: r1->field_b = r0
    //     0xb650e4: stur            w0, [x1, #0xb]
    // 0xb650e8: r0 = Stack()
    //     0xb650e8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb650ec: r1 = Instance_Alignment
    //     0xb650ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb650f0: ldr             x1, [x1, #0x950]
    // 0xb650f4: StoreField: r0->field_f = r1
    //     0xb650f4: stur            w1, [x0, #0xf]
    // 0xb650f8: r1 = Instance_StackFit
    //     0xb650f8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb650fc: ldr             x1, [x1, #0xfa8]
    // 0xb65100: ArrayStore: r0[0] = r1  ; List_4
    //     0xb65100: stur            w1, [x0, #0x17]
    // 0xb65104: r1 = Instance_Clip
    //     0xb65104: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb65108: ldr             x1, [x1, #0x7e0]
    // 0xb6510c: StoreField: r0->field_1b = r1
    //     0xb6510c: stur            w1, [x0, #0x1b]
    // 0xb65110: ldur            x1, [fp, #-0x10]
    // 0xb65114: StoreField: r0->field_b = r1
    //     0xb65114: stur            w1, [x0, #0xb]
    // 0xb65118: LeaveFrame
    //     0xb65118: mov             SP, fp
    //     0xb6511c: ldp             fp, lr, [SP], #0x10
    // 0xb65120: ret
    //     0xb65120: ret             
    // 0xb65124: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb65124: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb65128: b               #0xb64f60
    // 0xb6512c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6512c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ glassThemeSlider(/* No info */) {
    // ** addr: 0xb65130, size: 0x11f8
    // 0xb65130: EnterFrame
    //     0xb65130: stp             fp, lr, [SP, #-0x10]!
    //     0xb65134: mov             fp, SP
    // 0xb65138: AllocStack(0x90)
    //     0xb65138: sub             SP, SP, #0x90
    // 0xb6513c: SetupParameters(_ProductGridItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb6513c: stur            x1, [fp, #-8]
    //     0xb65140: stur            x2, [fp, #-0x10]
    //     0xb65144: stur            x3, [fp, #-0x18]
    // 0xb65148: CheckStackOverflow
    //     0xb65148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6514c: cmp             SP, x16
    //     0xb65150: b.ls            #0xb662e4
    // 0xb65154: r1 = 2
    //     0xb65154: movz            x1, #0x2
    // 0xb65158: r0 = AllocateContext()
    //     0xb65158: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6515c: mov             x2, x0
    // 0xb65160: ldur            x0, [fp, #-8]
    // 0xb65164: stur            x2, [fp, #-0x20]
    // 0xb65168: StoreField: r2->field_f = r0
    //     0xb65168: stur            w0, [x2, #0xf]
    // 0xb6516c: ldur            x1, [fp, #-0x18]
    // 0xb65170: StoreField: r2->field_13 = r1
    //     0xb65170: stur            w1, [x2, #0x13]
    // 0xb65174: LoadField: r1 = r0->field_f
    //     0xb65174: ldur            w1, [x0, #0xf]
    // 0xb65178: DecompressPointer r1
    //     0xb65178: add             x1, x1, HEAP, lsl #32
    // 0xb6517c: cmp             w1, NULL
    // 0xb65180: b.eq            #0xb662ec
    // 0xb65184: r0 = of()
    //     0xb65184: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb65188: LoadField: r1 = r0->field_5b
    //     0xb65188: ldur            w1, [x0, #0x5b]
    // 0xb6518c: DecompressPointer r1
    //     0xb6518c: add             x1, x1, HEAP, lsl #32
    // 0xb65190: r0 = LoadClassIdInstr(r1)
    //     0xb65190: ldur            x0, [x1, #-1]
    //     0xb65194: ubfx            x0, x0, #0xc, #0x14
    // 0xb65198: d0 = 0.030000
    //     0xb65198: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb6519c: ldr             d0, [x17, #0x238]
    // 0xb651a0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb651a0: sub             lr, x0, #0xffa
    //     0xb651a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb651a8: blr             lr
    // 0xb651ac: stur            x0, [fp, #-0x18]
    // 0xb651b0: r0 = Radius()
    //     0xb651b0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb651b4: d0 = 10.000000
    //     0xb651b4: fmov            d0, #10.00000000
    // 0xb651b8: stur            x0, [fp, #-0x28]
    // 0xb651bc: StoreField: r0->field_7 = d0
    //     0xb651bc: stur            d0, [x0, #7]
    // 0xb651c0: StoreField: r0->field_f = d0
    //     0xb651c0: stur            d0, [x0, #0xf]
    // 0xb651c4: r0 = BorderRadius()
    //     0xb651c4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb651c8: mov             x1, x0
    // 0xb651cc: ldur            x0, [fp, #-0x28]
    // 0xb651d0: stur            x1, [fp, #-0x30]
    // 0xb651d4: StoreField: r1->field_7 = r0
    //     0xb651d4: stur            w0, [x1, #7]
    // 0xb651d8: StoreField: r1->field_b = r0
    //     0xb651d8: stur            w0, [x1, #0xb]
    // 0xb651dc: StoreField: r1->field_f = r0
    //     0xb651dc: stur            w0, [x1, #0xf]
    // 0xb651e0: StoreField: r1->field_13 = r0
    //     0xb651e0: stur            w0, [x1, #0x13]
    // 0xb651e4: r0 = RoundedRectangleBorder()
    //     0xb651e4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb651e8: mov             x3, x0
    // 0xb651ec: ldur            x0, [fp, #-0x30]
    // 0xb651f0: stur            x3, [fp, #-0x28]
    // 0xb651f4: StoreField: r3->field_b = r0
    //     0xb651f4: stur            w0, [x3, #0xb]
    // 0xb651f8: r0 = Instance_BorderSide
    //     0xb651f8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb651fc: ldr             x0, [x0, #0xe20]
    // 0xb65200: StoreField: r3->field_7 = r0
    //     0xb65200: stur            w0, [x3, #7]
    // 0xb65204: ldur            x1, [fp, #-0x10]
    // 0xb65208: cmp             w1, NULL
    // 0xb6520c: b.ne            #0xb65218
    // 0xb65210: r1 = Null
    //     0xb65210: mov             x1, NULL
    // 0xb65214: b               #0xb65224
    // 0xb65218: LoadField: r2 = r1->field_b
    //     0xb65218: ldur            w2, [x1, #0xb]
    // 0xb6521c: DecompressPointer r2
    //     0xb6521c: add             x2, x2, HEAP, lsl #32
    // 0xb65220: mov             x1, x2
    // 0xb65224: cmp             w1, NULL
    // 0xb65228: b.ne            #0xb65234
    // 0xb6522c: r6 = ""
    //     0xb6522c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb65230: b               #0xb65238
    // 0xb65234: mov             x6, x1
    // 0xb65238: ldur            x5, [fp, #-0x20]
    // 0xb6523c: ldur            x4, [fp, #-0x18]
    // 0xb65240: stur            x6, [fp, #-0x10]
    // 0xb65244: r1 = Function '<anonymous closure>':.
    //     0xb65244: add             x1, PP, #0x56, lsl #12  ; [pp+0x56338] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb65248: ldr             x1, [x1, #0x338]
    // 0xb6524c: r2 = Null
    //     0xb6524c: mov             x2, NULL
    // 0xb65250: r0 = AllocateClosure()
    //     0xb65250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb65254: r1 = Function '<anonymous closure>':.
    //     0xb65254: add             x1, PP, #0x56, lsl #12  ; [pp+0x56340] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb65258: ldr             x1, [x1, #0x340]
    // 0xb6525c: r2 = Null
    //     0xb6525c: mov             x2, NULL
    // 0xb65260: stur            x0, [fp, #-0x30]
    // 0xb65264: r0 = AllocateClosure()
    //     0xb65264: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb65268: stur            x0, [fp, #-0x38]
    // 0xb6526c: r0 = CachedNetworkImage()
    //     0xb6526c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb65270: stur            x0, [fp, #-0x40]
    // 0xb65274: r16 = Instance_BoxFit
    //     0xb65274: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb65278: ldr             x16, [x16, #0xb18]
    // 0xb6527c: ldur            lr, [fp, #-0x30]
    // 0xb65280: stp             lr, x16, [SP, #8]
    // 0xb65284: ldur            x16, [fp, #-0x38]
    // 0xb65288: str             x16, [SP]
    // 0xb6528c: mov             x1, x0
    // 0xb65290: ldur            x2, [fp, #-0x10]
    // 0xb65294: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb65294: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb65298: ldr             x4, [x4, #0x638]
    // 0xb6529c: r0 = CachedNetworkImage()
    //     0xb6529c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb652a0: r0 = Card()
    //     0xb652a0: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb652a4: mov             x1, x0
    // 0xb652a8: ldur            x0, [fp, #-0x18]
    // 0xb652ac: stur            x1, [fp, #-0x10]
    // 0xb652b0: StoreField: r1->field_b = r0
    //     0xb652b0: stur            w0, [x1, #0xb]
    // 0xb652b4: r0 = 0.000000
    //     0xb652b4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb652b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb652b8: stur            w0, [x1, #0x17]
    // 0xb652bc: ldur            x0, [fp, #-0x28]
    // 0xb652c0: StoreField: r1->field_1b = r0
    //     0xb652c0: stur            w0, [x1, #0x1b]
    // 0xb652c4: r0 = true
    //     0xb652c4: add             x0, NULL, #0x20  ; true
    // 0xb652c8: StoreField: r1->field_1f = r0
    //     0xb652c8: stur            w0, [x1, #0x1f]
    // 0xb652cc: r2 = Instance_EdgeInsets
    //     0xb652cc: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb652d0: StoreField: r1->field_27 = r2
    //     0xb652d0: stur            w2, [x1, #0x27]
    // 0xb652d4: r2 = Instance_Clip
    //     0xb652d4: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb652d8: ldr             x2, [x2, #0xb50]
    // 0xb652dc: StoreField: r1->field_23 = r2
    //     0xb652dc: stur            w2, [x1, #0x23]
    // 0xb652e0: ldur            x2, [fp, #-0x40]
    // 0xb652e4: StoreField: r1->field_2f = r2
    //     0xb652e4: stur            w2, [x1, #0x2f]
    // 0xb652e8: StoreField: r1->field_2b = r0
    //     0xb652e8: stur            w0, [x1, #0x2b]
    // 0xb652ec: r2 = Instance__CardVariant
    //     0xb652ec: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb652f0: ldr             x2, [x2, #0xa68]
    // 0xb652f4: StoreField: r1->field_33 = r2
    //     0xb652f4: stur            w2, [x1, #0x33]
    // 0xb652f8: r0 = Center()
    //     0xb652f8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb652fc: mov             x1, x0
    // 0xb65300: r0 = Instance_Alignment
    //     0xb65300: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb65304: ldr             x0, [x0, #0xb10]
    // 0xb65308: stur            x1, [fp, #-0x28]
    // 0xb6530c: StoreField: r1->field_f = r0
    //     0xb6530c: stur            w0, [x1, #0xf]
    // 0xb65310: ldur            x0, [fp, #-0x10]
    // 0xb65314: StoreField: r1->field_b = r0
    //     0xb65314: stur            w0, [x1, #0xb]
    // 0xb65318: ldur            x2, [fp, #-0x20]
    // 0xb6531c: LoadField: r0 = r2->field_13
    //     0xb6531c: ldur            w0, [x2, #0x13]
    // 0xb65320: DecompressPointer r0
    //     0xb65320: add             x0, x0, HEAP, lsl #32
    // 0xb65324: stur            x0, [fp, #-0x18]
    // 0xb65328: cmp             w0, NULL
    // 0xb6532c: b.ne            #0xb65338
    // 0xb65330: r3 = Null
    //     0xb65330: mov             x3, NULL
    // 0xb65334: b               #0xb65364
    // 0xb65338: LoadField: r3 = r0->field_7f
    //     0xb65338: ldur            w3, [x0, #0x7f]
    // 0xb6533c: DecompressPointer r3
    //     0xb6533c: add             x3, x3, HEAP, lsl #32
    // 0xb65340: cmp             w3, NULL
    // 0xb65344: b.ne            #0xb65350
    // 0xb65348: r3 = Null
    //     0xb65348: mov             x3, NULL
    // 0xb6534c: b               #0xb65364
    // 0xb65350: LoadField: r4 = r3->field_7
    //     0xb65350: ldur            w4, [x3, #7]
    // 0xb65354: cbnz            w4, #0xb65360
    // 0xb65358: r3 = false
    //     0xb65358: add             x3, NULL, #0x30  ; false
    // 0xb6535c: b               #0xb65364
    // 0xb65360: r3 = true
    //     0xb65360: add             x3, NULL, #0x20  ; true
    // 0xb65364: cmp             w3, NULL
    // 0xb65368: b.ne            #0xb65370
    // 0xb6536c: r3 = false
    //     0xb6536c: add             x3, NULL, #0x30  ; false
    // 0xb65370: stur            x3, [fp, #-0x10]
    // 0xb65374: cmp             w0, NULL
    // 0xb65378: b.ne            #0xb65384
    // 0xb6537c: r4 = Null
    //     0xb6537c: mov             x4, NULL
    // 0xb65380: b               #0xb6538c
    // 0xb65384: LoadField: r4 = r0->field_93
    //     0xb65384: ldur            w4, [x0, #0x93]
    // 0xb65388: DecompressPointer r4
    //     0xb65388: add             x4, x4, HEAP, lsl #32
    // 0xb6538c: cmp             w4, NULL
    // 0xb65390: b.ne            #0xb653a8
    // 0xb65394: mov             x4, x2
    // 0xb65398: r0 = Instance_Alignment
    //     0xb65398: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb6539c: ldr             x0, [x0, #0xfa0]
    // 0xb653a0: r2 = 4
    //     0xb653a0: movz            x2, #0x4
    // 0xb653a4: b               #0xb65900
    // 0xb653a8: tbnz            w4, #4, #0xb658f0
    // 0xb653ac: ldur            x4, [fp, #-8]
    // 0xb653b0: r0 = Radius()
    //     0xb653b0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb653b4: d0 = 8.000000
    //     0xb653b4: fmov            d0, #8.00000000
    // 0xb653b8: stur            x0, [fp, #-0x30]
    // 0xb653bc: StoreField: r0->field_7 = d0
    //     0xb653bc: stur            d0, [x0, #7]
    // 0xb653c0: StoreField: r0->field_f = d0
    //     0xb653c0: stur            d0, [x0, #0xf]
    // 0xb653c4: r0 = BorderRadius()
    //     0xb653c4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb653c8: mov             x1, x0
    // 0xb653cc: ldur            x0, [fp, #-0x30]
    // 0xb653d0: stur            x1, [fp, #-0x38]
    // 0xb653d4: StoreField: r1->field_7 = r0
    //     0xb653d4: stur            w0, [x1, #7]
    // 0xb653d8: StoreField: r1->field_b = r0
    //     0xb653d8: stur            w0, [x1, #0xb]
    // 0xb653dc: StoreField: r1->field_f = r0
    //     0xb653dc: stur            w0, [x1, #0xf]
    // 0xb653e0: StoreField: r1->field_13 = r0
    //     0xb653e0: stur            w0, [x1, #0x13]
    // 0xb653e4: ldur            x0, [fp, #-8]
    // 0xb653e8: LoadField: r2 = r0->field_b
    //     0xb653e8: ldur            w2, [x0, #0xb]
    // 0xb653ec: DecompressPointer r2
    //     0xb653ec: add             x2, x2, HEAP, lsl #32
    // 0xb653f0: cmp             w2, NULL
    // 0xb653f4: b.eq            #0xb662f0
    // 0xb653f8: LoadField: r3 = r2->field_43
    //     0xb653f8: ldur            w3, [x2, #0x43]
    // 0xb653fc: DecompressPointer r3
    //     0xb653fc: add             x3, x3, HEAP, lsl #32
    // 0xb65400: stur            x3, [fp, #-0x30]
    // 0xb65404: cmp             w3, NULL
    // 0xb65408: b.ne            #0xb65414
    // 0xb6540c: r2 = Null
    //     0xb6540c: mov             x2, NULL
    // 0xb65410: b               #0xb65438
    // 0xb65414: LoadField: r2 = r3->field_13
    //     0xb65414: ldur            w2, [x3, #0x13]
    // 0xb65418: DecompressPointer r2
    //     0xb65418: add             x2, x2, HEAP, lsl #32
    // 0xb6541c: cmp             w2, NULL
    // 0xb65420: b.ne            #0xb6542c
    // 0xb65424: r2 = Null
    //     0xb65424: mov             x2, NULL
    // 0xb65428: b               #0xb65438
    // 0xb6542c: LoadField: r4 = r2->field_7
    //     0xb6542c: ldur            w4, [x2, #7]
    // 0xb65430: DecompressPointer r4
    //     0xb65430: add             x4, x4, HEAP, lsl #32
    // 0xb65434: mov             x2, x4
    // 0xb65438: cmp             w2, NULL
    // 0xb6543c: b.ne            #0xb65448
    // 0xb65440: r2 = 0
    //     0xb65440: movz            x2, #0
    // 0xb65444: b               #0xb65458
    // 0xb65448: r4 = LoadInt32Instr(r2)
    //     0xb65448: sbfx            x4, x2, #1, #0x1f
    //     0xb6544c: tbz             w2, #0, #0xb65454
    //     0xb65450: ldur            x4, [x2, #7]
    // 0xb65454: mov             x2, x4
    // 0xb65458: stur            x2, [fp, #-0x58]
    // 0xb6545c: cmp             w3, NULL
    // 0xb65460: b.ne            #0xb6546c
    // 0xb65464: r4 = Null
    //     0xb65464: mov             x4, NULL
    // 0xb65468: b               #0xb65490
    // 0xb6546c: LoadField: r4 = r3->field_13
    //     0xb6546c: ldur            w4, [x3, #0x13]
    // 0xb65470: DecompressPointer r4
    //     0xb65470: add             x4, x4, HEAP, lsl #32
    // 0xb65474: cmp             w4, NULL
    // 0xb65478: b.ne            #0xb65484
    // 0xb6547c: r4 = Null
    //     0xb6547c: mov             x4, NULL
    // 0xb65480: b               #0xb65490
    // 0xb65484: LoadField: r5 = r4->field_b
    //     0xb65484: ldur            w5, [x4, #0xb]
    // 0xb65488: DecompressPointer r5
    //     0xb65488: add             x5, x5, HEAP, lsl #32
    // 0xb6548c: mov             x4, x5
    // 0xb65490: cmp             w4, NULL
    // 0xb65494: b.ne            #0xb654a0
    // 0xb65498: r4 = 0
    //     0xb65498: movz            x4, #0
    // 0xb6549c: b               #0xb654b0
    // 0xb654a0: r5 = LoadInt32Instr(r4)
    //     0xb654a0: sbfx            x5, x4, #1, #0x1f
    //     0xb654a4: tbz             w4, #0, #0xb654ac
    //     0xb654a8: ldur            x5, [x4, #7]
    // 0xb654ac: mov             x4, x5
    // 0xb654b0: stur            x4, [fp, #-0x50]
    // 0xb654b4: cmp             w3, NULL
    // 0xb654b8: b.ne            #0xb654c4
    // 0xb654bc: r5 = Null
    //     0xb654bc: mov             x5, NULL
    // 0xb654c0: b               #0xb654e8
    // 0xb654c4: LoadField: r5 = r3->field_13
    //     0xb654c4: ldur            w5, [x3, #0x13]
    // 0xb654c8: DecompressPointer r5
    //     0xb654c8: add             x5, x5, HEAP, lsl #32
    // 0xb654cc: cmp             w5, NULL
    // 0xb654d0: b.ne            #0xb654dc
    // 0xb654d4: r5 = Null
    //     0xb654d4: mov             x5, NULL
    // 0xb654d8: b               #0xb654e8
    // 0xb654dc: LoadField: r6 = r5->field_f
    //     0xb654dc: ldur            w6, [x5, #0xf]
    // 0xb654e0: DecompressPointer r6
    //     0xb654e0: add             x6, x6, HEAP, lsl #32
    // 0xb654e4: mov             x5, x6
    // 0xb654e8: cmp             w5, NULL
    // 0xb654ec: b.ne            #0xb654f8
    // 0xb654f0: r5 = 0
    //     0xb654f0: movz            x5, #0
    // 0xb654f4: b               #0xb65508
    // 0xb654f8: r6 = LoadInt32Instr(r5)
    //     0xb654f8: sbfx            x6, x5, #1, #0x1f
    //     0xb654fc: tbz             w5, #0, #0xb65504
    //     0xb65500: ldur            x6, [x5, #7]
    // 0xb65504: mov             x5, x6
    // 0xb65508: stur            x5, [fp, #-0x48]
    // 0xb6550c: r0 = Color()
    //     0xb6550c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb65510: mov             x1, x0
    // 0xb65514: r0 = Instance_ColorSpace
    //     0xb65514: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb65518: stur            x1, [fp, #-0x40]
    // 0xb6551c: StoreField: r1->field_27 = r0
    //     0xb6551c: stur            w0, [x1, #0x27]
    // 0xb65520: d0 = 1.000000
    //     0xb65520: fmov            d0, #1.00000000
    // 0xb65524: StoreField: r1->field_7 = d0
    //     0xb65524: stur            d0, [x1, #7]
    // 0xb65528: ldur            x2, [fp, #-0x58]
    // 0xb6552c: ubfx            x2, x2, #0, #0x20
    // 0xb65530: and             w3, w2, #0xff
    // 0xb65534: ubfx            x3, x3, #0, #0x20
    // 0xb65538: scvtf           d0, x3
    // 0xb6553c: d1 = 255.000000
    //     0xb6553c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb65540: fdiv            d2, d0, d1
    // 0xb65544: StoreField: r1->field_f = d2
    //     0xb65544: stur            d2, [x1, #0xf]
    // 0xb65548: ldur            x2, [fp, #-0x50]
    // 0xb6554c: ubfx            x2, x2, #0, #0x20
    // 0xb65550: and             w3, w2, #0xff
    // 0xb65554: ubfx            x3, x3, #0, #0x20
    // 0xb65558: scvtf           d0, x3
    // 0xb6555c: fdiv            d2, d0, d1
    // 0xb65560: ArrayStore: r1[0] = d2  ; List_8
    //     0xb65560: stur            d2, [x1, #0x17]
    // 0xb65564: ldur            x2, [fp, #-0x48]
    // 0xb65568: ubfx            x2, x2, #0, #0x20
    // 0xb6556c: and             w3, w2, #0xff
    // 0xb65570: ubfx            x3, x3, #0, #0x20
    // 0xb65574: scvtf           d0, x3
    // 0xb65578: fdiv            d2, d0, d1
    // 0xb6557c: StoreField: r1->field_1f = d2
    //     0xb6557c: stur            d2, [x1, #0x1f]
    // 0xb65580: ldur            x2, [fp, #-0x30]
    // 0xb65584: cmp             w2, NULL
    // 0xb65588: b.ne            #0xb65594
    // 0xb6558c: r3 = Null
    //     0xb6558c: mov             x3, NULL
    // 0xb65590: b               #0xb655b8
    // 0xb65594: LoadField: r3 = r2->field_13
    //     0xb65594: ldur            w3, [x2, #0x13]
    // 0xb65598: DecompressPointer r3
    //     0xb65598: add             x3, x3, HEAP, lsl #32
    // 0xb6559c: cmp             w3, NULL
    // 0xb655a0: b.ne            #0xb655ac
    // 0xb655a4: r3 = Null
    //     0xb655a4: mov             x3, NULL
    // 0xb655a8: b               #0xb655b8
    // 0xb655ac: LoadField: r4 = r3->field_7
    //     0xb655ac: ldur            w4, [x3, #7]
    // 0xb655b0: DecompressPointer r4
    //     0xb655b0: add             x4, x4, HEAP, lsl #32
    // 0xb655b4: mov             x3, x4
    // 0xb655b8: cmp             w3, NULL
    // 0xb655bc: b.ne            #0xb655c8
    // 0xb655c0: r3 = 0
    //     0xb655c0: movz            x3, #0
    // 0xb655c4: b               #0xb655d8
    // 0xb655c8: r4 = LoadInt32Instr(r3)
    //     0xb655c8: sbfx            x4, x3, #1, #0x1f
    //     0xb655cc: tbz             w3, #0, #0xb655d4
    //     0xb655d0: ldur            x4, [x3, #7]
    // 0xb655d4: mov             x3, x4
    // 0xb655d8: stur            x3, [fp, #-0x58]
    // 0xb655dc: cmp             w2, NULL
    // 0xb655e0: b.ne            #0xb655ec
    // 0xb655e4: r4 = Null
    //     0xb655e4: mov             x4, NULL
    // 0xb655e8: b               #0xb65610
    // 0xb655ec: LoadField: r4 = r2->field_13
    //     0xb655ec: ldur            w4, [x2, #0x13]
    // 0xb655f0: DecompressPointer r4
    //     0xb655f0: add             x4, x4, HEAP, lsl #32
    // 0xb655f4: cmp             w4, NULL
    // 0xb655f8: b.ne            #0xb65604
    // 0xb655fc: r4 = Null
    //     0xb655fc: mov             x4, NULL
    // 0xb65600: b               #0xb65610
    // 0xb65604: LoadField: r5 = r4->field_b
    //     0xb65604: ldur            w5, [x4, #0xb]
    // 0xb65608: DecompressPointer r5
    //     0xb65608: add             x5, x5, HEAP, lsl #32
    // 0xb6560c: mov             x4, x5
    // 0xb65610: cmp             w4, NULL
    // 0xb65614: b.ne            #0xb65620
    // 0xb65618: r4 = 0
    //     0xb65618: movz            x4, #0
    // 0xb6561c: b               #0xb65630
    // 0xb65620: r5 = LoadInt32Instr(r4)
    //     0xb65620: sbfx            x5, x4, #1, #0x1f
    //     0xb65624: tbz             w4, #0, #0xb6562c
    //     0xb65628: ldur            x5, [x4, #7]
    // 0xb6562c: mov             x4, x5
    // 0xb65630: stur            x4, [fp, #-0x50]
    // 0xb65634: cmp             w2, NULL
    // 0xb65638: b.ne            #0xb65644
    // 0xb6563c: r2 = Null
    //     0xb6563c: mov             x2, NULL
    // 0xb65640: b               #0xb65664
    // 0xb65644: LoadField: r5 = r2->field_13
    //     0xb65644: ldur            w5, [x2, #0x13]
    // 0xb65648: DecompressPointer r5
    //     0xb65648: add             x5, x5, HEAP, lsl #32
    // 0xb6564c: cmp             w5, NULL
    // 0xb65650: b.ne            #0xb6565c
    // 0xb65654: r2 = Null
    //     0xb65654: mov             x2, NULL
    // 0xb65658: b               #0xb65664
    // 0xb6565c: LoadField: r2 = r5->field_f
    //     0xb6565c: ldur            w2, [x5, #0xf]
    // 0xb65660: DecompressPointer r2
    //     0xb65660: add             x2, x2, HEAP, lsl #32
    // 0xb65664: cmp             w2, NULL
    // 0xb65668: b.ne            #0xb65674
    // 0xb6566c: r6 = 0
    //     0xb6566c: movz            x6, #0
    // 0xb65670: b               #0xb65684
    // 0xb65674: r5 = LoadInt32Instr(r2)
    //     0xb65674: sbfx            x5, x2, #1, #0x1f
    //     0xb65678: tbz             w2, #0, #0xb65680
    //     0xb6567c: ldur            x5, [x2, #7]
    // 0xb65680: mov             x6, x5
    // 0xb65684: ldur            x5, [fp, #-0x18]
    // 0xb65688: ldur            x2, [fp, #-0x38]
    // 0xb6568c: stur            x6, [fp, #-0x48]
    // 0xb65690: r0 = Color()
    //     0xb65690: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb65694: mov             x3, x0
    // 0xb65698: r0 = Instance_ColorSpace
    //     0xb65698: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb6569c: stur            x3, [fp, #-0x30]
    // 0xb656a0: StoreField: r3->field_27 = r0
    //     0xb656a0: stur            w0, [x3, #0x27]
    // 0xb656a4: d0 = 0.700000
    //     0xb656a4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb656a8: ldr             d0, [x17, #0xf48]
    // 0xb656ac: StoreField: r3->field_7 = d0
    //     0xb656ac: stur            d0, [x3, #7]
    // 0xb656b0: ldur            x0, [fp, #-0x58]
    // 0xb656b4: ubfx            x0, x0, #0, #0x20
    // 0xb656b8: and             w1, w0, #0xff
    // 0xb656bc: ubfx            x1, x1, #0, #0x20
    // 0xb656c0: scvtf           d0, x1
    // 0xb656c4: d1 = 255.000000
    //     0xb656c4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb656c8: fdiv            d2, d0, d1
    // 0xb656cc: StoreField: r3->field_f = d2
    //     0xb656cc: stur            d2, [x3, #0xf]
    // 0xb656d0: ldur            x0, [fp, #-0x50]
    // 0xb656d4: ubfx            x0, x0, #0, #0x20
    // 0xb656d8: and             w1, w0, #0xff
    // 0xb656dc: ubfx            x1, x1, #0, #0x20
    // 0xb656e0: scvtf           d0, x1
    // 0xb656e4: fdiv            d2, d0, d1
    // 0xb656e8: ArrayStore: r3[0] = d2  ; List_8
    //     0xb656e8: stur            d2, [x3, #0x17]
    // 0xb656ec: ldur            x0, [fp, #-0x48]
    // 0xb656f0: ubfx            x0, x0, #0, #0x20
    // 0xb656f4: and             w1, w0, #0xff
    // 0xb656f8: ubfx            x1, x1, #0, #0x20
    // 0xb656fc: scvtf           d0, x1
    // 0xb65700: fdiv            d2, d0, d1
    // 0xb65704: StoreField: r3->field_1f = d2
    //     0xb65704: stur            d2, [x3, #0x1f]
    // 0xb65708: r1 = Null
    //     0xb65708: mov             x1, NULL
    // 0xb6570c: r2 = 4
    //     0xb6570c: movz            x2, #0x4
    // 0xb65710: r0 = AllocateArray()
    //     0xb65710: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb65714: mov             x2, x0
    // 0xb65718: ldur            x0, [fp, #-0x40]
    // 0xb6571c: stur            x2, [fp, #-0x60]
    // 0xb65720: StoreField: r2->field_f = r0
    //     0xb65720: stur            w0, [x2, #0xf]
    // 0xb65724: ldur            x0, [fp, #-0x30]
    // 0xb65728: StoreField: r2->field_13 = r0
    //     0xb65728: stur            w0, [x2, #0x13]
    // 0xb6572c: r1 = <Color>
    //     0xb6572c: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb65730: ldr             x1, [x1, #0xf80]
    // 0xb65734: r0 = AllocateGrowableArray()
    //     0xb65734: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb65738: mov             x1, x0
    // 0xb6573c: ldur            x0, [fp, #-0x60]
    // 0xb65740: stur            x1, [fp, #-0x30]
    // 0xb65744: StoreField: r1->field_f = r0
    //     0xb65744: stur            w0, [x1, #0xf]
    // 0xb65748: r2 = 4
    //     0xb65748: movz            x2, #0x4
    // 0xb6574c: StoreField: r1->field_b = r2
    //     0xb6574c: stur            w2, [x1, #0xb]
    // 0xb65750: r0 = LinearGradient()
    //     0xb65750: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb65754: mov             x1, x0
    // 0xb65758: r0 = Instance_Alignment
    //     0xb65758: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb6575c: ldr             x0, [x0, #0xce0]
    // 0xb65760: stur            x1, [fp, #-0x40]
    // 0xb65764: StoreField: r1->field_13 = r0
    //     0xb65764: stur            w0, [x1, #0x13]
    // 0xb65768: r0 = Instance_Alignment
    //     0xb65768: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb6576c: ldr             x0, [x0, #0xce8]
    // 0xb65770: ArrayStore: r1[0] = r0  ; List_4
    //     0xb65770: stur            w0, [x1, #0x17]
    // 0xb65774: r0 = Instance_TileMode
    //     0xb65774: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb65778: ldr             x0, [x0, #0xcf0]
    // 0xb6577c: StoreField: r1->field_1b = r0
    //     0xb6577c: stur            w0, [x1, #0x1b]
    // 0xb65780: ldur            x0, [fp, #-0x30]
    // 0xb65784: StoreField: r1->field_7 = r0
    //     0xb65784: stur            w0, [x1, #7]
    // 0xb65788: r0 = BoxDecoration()
    //     0xb65788: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6578c: mov             x2, x0
    // 0xb65790: ldur            x0, [fp, #-0x38]
    // 0xb65794: stur            x2, [fp, #-0x30]
    // 0xb65798: StoreField: r2->field_13 = r0
    //     0xb65798: stur            w0, [x2, #0x13]
    // 0xb6579c: ldur            x0, [fp, #-0x40]
    // 0xb657a0: StoreField: r2->field_1b = r0
    //     0xb657a0: stur            w0, [x2, #0x1b]
    // 0xb657a4: r0 = Instance_BoxShape
    //     0xb657a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb657a8: ldr             x0, [x0, #0x80]
    // 0xb657ac: StoreField: r2->field_23 = r0
    //     0xb657ac: stur            w0, [x2, #0x23]
    // 0xb657b0: ldur            x1, [fp, #-0x18]
    // 0xb657b4: cmp             w1, NULL
    // 0xb657b8: b.ne            #0xb657c4
    // 0xb657bc: r1 = Null
    //     0xb657bc: mov             x1, NULL
    // 0xb657c0: b               #0xb657d0
    // 0xb657c4: LoadField: r3 = r1->field_7f
    //     0xb657c4: ldur            w3, [x1, #0x7f]
    // 0xb657c8: DecompressPointer r3
    //     0xb657c8: add             x3, x3, HEAP, lsl #32
    // 0xb657cc: mov             x1, x3
    // 0xb657d0: cmp             w1, NULL
    // 0xb657d4: b.ne            #0xb657e0
    // 0xb657d8: r4 = ""
    //     0xb657d8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb657dc: b               #0xb657e4
    // 0xb657e0: mov             x4, x1
    // 0xb657e4: ldur            x3, [fp, #-8]
    // 0xb657e8: stur            x4, [fp, #-0x18]
    // 0xb657ec: LoadField: r1 = r3->field_f
    //     0xb657ec: ldur            w1, [x3, #0xf]
    // 0xb657f0: DecompressPointer r1
    //     0xb657f0: add             x1, x1, HEAP, lsl #32
    // 0xb657f4: cmp             w1, NULL
    // 0xb657f8: b.eq            #0xb662f4
    // 0xb657fc: r0 = of()
    //     0xb657fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb65800: LoadField: r1 = r0->field_87
    //     0xb65800: ldur            w1, [x0, #0x87]
    // 0xb65804: DecompressPointer r1
    //     0xb65804: add             x1, x1, HEAP, lsl #32
    // 0xb65808: LoadField: r0 = r1->field_7
    //     0xb65808: ldur            w0, [x1, #7]
    // 0xb6580c: DecompressPointer r0
    //     0xb6580c: add             x0, x0, HEAP, lsl #32
    // 0xb65810: r16 = 12.000000
    //     0xb65810: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb65814: ldr             x16, [x16, #0x9e8]
    // 0xb65818: r30 = Instance_Color
    //     0xb65818: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6581c: stp             lr, x16, [SP]
    // 0xb65820: mov             x1, x0
    // 0xb65824: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb65824: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb65828: ldr             x4, [x4, #0xaa0]
    // 0xb6582c: r0 = copyWith()
    //     0xb6582c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb65830: stur            x0, [fp, #-0x38]
    // 0xb65834: r0 = Text()
    //     0xb65834: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb65838: mov             x1, x0
    // 0xb6583c: ldur            x0, [fp, #-0x18]
    // 0xb65840: stur            x1, [fp, #-0x40]
    // 0xb65844: StoreField: r1->field_b = r0
    //     0xb65844: stur            w0, [x1, #0xb]
    // 0xb65848: ldur            x0, [fp, #-0x38]
    // 0xb6584c: StoreField: r1->field_13 = r0
    //     0xb6584c: stur            w0, [x1, #0x13]
    // 0xb65850: r0 = Instance_TextAlign
    //     0xb65850: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb65854: StoreField: r1->field_1b = r0
    //     0xb65854: stur            w0, [x1, #0x1b]
    // 0xb65858: r0 = Padding()
    //     0xb65858: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6585c: mov             x1, x0
    // 0xb65860: r0 = Instance_EdgeInsets
    //     0xb65860: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb65864: ldr             x0, [x0, #0x850]
    // 0xb65868: stur            x1, [fp, #-0x18]
    // 0xb6586c: StoreField: r1->field_f = r0
    //     0xb6586c: stur            w0, [x1, #0xf]
    // 0xb65870: ldur            x0, [fp, #-0x40]
    // 0xb65874: StoreField: r1->field_b = r0
    //     0xb65874: stur            w0, [x1, #0xb]
    // 0xb65878: r0 = Container()
    //     0xb65878: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6587c: stur            x0, [fp, #-0x38]
    // 0xb65880: r16 = 20.000000
    //     0xb65880: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb65884: ldr             x16, [x16, #0xac8]
    // 0xb65888: r30 = 120.000000
    //     0xb65888: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb6588c: ldr             lr, [lr, #0x3a0]
    // 0xb65890: stp             lr, x16, [SP, #0x10]
    // 0xb65894: ldur            x16, [fp, #-0x30]
    // 0xb65898: ldur            lr, [fp, #-0x18]
    // 0xb6589c: stp             lr, x16, [SP]
    // 0xb658a0: mov             x1, x0
    // 0xb658a4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb658a4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb658a8: ldr             x4, [x4, #0x8c0]
    // 0xb658ac: r0 = Container()
    //     0xb658ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb658b0: r0 = Align()
    //     0xb658b0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb658b4: mov             x1, x0
    // 0xb658b8: r0 = Instance_Alignment
    //     0xb658b8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb658bc: ldr             x0, [x0, #0xfa0]
    // 0xb658c0: stur            x1, [fp, #-0x18]
    // 0xb658c4: StoreField: r1->field_f = r0
    //     0xb658c4: stur            w0, [x1, #0xf]
    // 0xb658c8: ldur            x0, [fp, #-0x38]
    // 0xb658cc: StoreField: r1->field_b = r0
    //     0xb658cc: stur            w0, [x1, #0xb]
    // 0xb658d0: r0 = Padding()
    //     0xb658d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb658d4: mov             x1, x0
    // 0xb658d8: r0 = Instance_EdgeInsets
    //     0xb658d8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xb658dc: ldr             x0, [x0, #0xe50]
    // 0xb658e0: StoreField: r1->field_f = r0
    //     0xb658e0: stur            w0, [x1, #0xf]
    // 0xb658e4: ldur            x0, [fp, #-0x18]
    // 0xb658e8: StoreField: r1->field_b = r0
    //     0xb658e8: stur            w0, [x1, #0xb]
    // 0xb658ec: b               #0xb65c28
    // 0xb658f0: r0 = Instance_Alignment
    //     0xb658f0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb658f4: ldr             x0, [x0, #0xfa0]
    // 0xb658f8: r2 = 4
    //     0xb658f8: movz            x2, #0x4
    // 0xb658fc: ldur            x4, [fp, #-0x20]
    // 0xb65900: ldur            x3, [fp, #-8]
    // 0xb65904: LoadField: r1 = r3->field_f
    //     0xb65904: ldur            w1, [x3, #0xf]
    // 0xb65908: DecompressPointer r1
    //     0xb65908: add             x1, x1, HEAP, lsl #32
    // 0xb6590c: cmp             w1, NULL
    // 0xb65910: b.eq            #0xb662f8
    // 0xb65914: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb65914: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb65918: r0 = _of()
    //     0xb65918: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb6591c: LoadField: r1 = r0->field_7
    //     0xb6591c: ldur            w1, [x0, #7]
    // 0xb65920: DecompressPointer r1
    //     0xb65920: add             x1, x1, HEAP, lsl #32
    // 0xb65924: LoadField: d0 = r1->field_7
    //     0xb65924: ldur            d0, [x1, #7]
    // 0xb65928: d1 = 0.370000
    //     0xb65928: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xb6592c: ldr             d1, [x17, #0xe40]
    // 0xb65930: fmul            d2, d0, d1
    // 0xb65934: stur            d2, [fp, #-0x70]
    // 0xb65938: r1 = Instance_Color
    //     0xb65938: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6593c: d0 = 0.900000
    //     0xb6593c: ldr             d0, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xb65940: r0 = withOpacity()
    //     0xb65940: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb65944: stur            x0, [fp, #-0x18]
    // 0xb65948: r0 = Radius()
    //     0xb65948: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6594c: d0 = 4.000000
    //     0xb6594c: fmov            d0, #4.00000000
    // 0xb65950: stur            x0, [fp, #-0x30]
    // 0xb65954: StoreField: r0->field_7 = d0
    //     0xb65954: stur            d0, [x0, #7]
    // 0xb65958: StoreField: r0->field_f = d0
    //     0xb65958: stur            d0, [x0, #0xf]
    // 0xb6595c: r0 = BorderRadius()
    //     0xb6595c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb65960: mov             x1, x0
    // 0xb65964: ldur            x0, [fp, #-0x30]
    // 0xb65968: stur            x1, [fp, #-0x38]
    // 0xb6596c: StoreField: r1->field_7 = r0
    //     0xb6596c: stur            w0, [x1, #7]
    // 0xb65970: StoreField: r1->field_b = r0
    //     0xb65970: stur            w0, [x1, #0xb]
    // 0xb65974: StoreField: r1->field_f = r0
    //     0xb65974: stur            w0, [x1, #0xf]
    // 0xb65978: StoreField: r1->field_13 = r0
    //     0xb65978: stur            w0, [x1, #0x13]
    // 0xb6597c: r0 = BoxDecoration()
    //     0xb6597c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb65980: mov             x2, x0
    // 0xb65984: ldur            x0, [fp, #-0x18]
    // 0xb65988: stur            x2, [fp, #-0x30]
    // 0xb6598c: StoreField: r2->field_7 = r0
    //     0xb6598c: stur            w0, [x2, #7]
    // 0xb65990: ldur            x0, [fp, #-0x38]
    // 0xb65994: StoreField: r2->field_13 = r0
    //     0xb65994: stur            w0, [x2, #0x13]
    // 0xb65998: r0 = Instance_BoxShape
    //     0xb65998: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6599c: ldr             x0, [x0, #0x80]
    // 0xb659a0: StoreField: r2->field_23 = r0
    //     0xb659a0: stur            w0, [x2, #0x23]
    // 0xb659a4: ldur            x3, [fp, #-8]
    // 0xb659a8: LoadField: r1 = r3->field_f
    //     0xb659a8: ldur            w1, [x3, #0xf]
    // 0xb659ac: DecompressPointer r1
    //     0xb659ac: add             x1, x1, HEAP, lsl #32
    // 0xb659b0: cmp             w1, NULL
    // 0xb659b4: b.eq            #0xb662fc
    // 0xb659b8: r0 = of()
    //     0xb659b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb659bc: LoadField: r1 = r0->field_5b
    //     0xb659bc: ldur            w1, [x0, #0x5b]
    // 0xb659c0: DecompressPointer r1
    //     0xb659c0: add             x1, x1, HEAP, lsl #32
    // 0xb659c4: stur            x1, [fp, #-0x18]
    // 0xb659c8: r0 = ColorFilter()
    //     0xb659c8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb659cc: mov             x1, x0
    // 0xb659d0: ldur            x0, [fp, #-0x18]
    // 0xb659d4: stur            x1, [fp, #-0x38]
    // 0xb659d8: StoreField: r1->field_7 = r0
    //     0xb659d8: stur            w0, [x1, #7]
    // 0xb659dc: r0 = Instance_BlendMode
    //     0xb659dc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb659e0: ldr             x0, [x0, #0xb30]
    // 0xb659e4: StoreField: r1->field_b = r0
    //     0xb659e4: stur            w0, [x1, #0xb]
    // 0xb659e8: r0 = 1
    //     0xb659e8: movz            x0, #0x1
    // 0xb659ec: StoreField: r1->field_13 = r0
    //     0xb659ec: stur            x0, [x1, #0x13]
    // 0xb659f0: r0 = SvgPicture()
    //     0xb659f0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb659f4: stur            x0, [fp, #-0x18]
    // 0xb659f8: ldur            x16, [fp, #-0x38]
    // 0xb659fc: str             x16, [SP]
    // 0xb65a00: mov             x1, x0
    // 0xb65a04: r2 = "assets/images/bumper_coupon.svg"
    //     0xb65a04: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xb65a08: ldr             x2, [x2, #0xe48]
    // 0xb65a0c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb65a0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb65a10: ldr             x4, [x4, #0xa38]
    // 0xb65a14: r0 = SvgPicture.asset()
    //     0xb65a14: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb65a18: ldur            x2, [fp, #-0x20]
    // 0xb65a1c: LoadField: r0 = r2->field_13
    //     0xb65a1c: ldur            w0, [x2, #0x13]
    // 0xb65a20: DecompressPointer r0
    //     0xb65a20: add             x0, x0, HEAP, lsl #32
    // 0xb65a24: cmp             w0, NULL
    // 0xb65a28: b.ne            #0xb65a34
    // 0xb65a2c: r0 = Null
    //     0xb65a2c: mov             x0, NULL
    // 0xb65a30: b               #0xb65a40
    // 0xb65a34: LoadField: r1 = r0->field_7f
    //     0xb65a34: ldur            w1, [x0, #0x7f]
    // 0xb65a38: DecompressPointer r1
    //     0xb65a38: add             x1, x1, HEAP, lsl #32
    // 0xb65a3c: mov             x0, x1
    // 0xb65a40: cmp             w0, NULL
    // 0xb65a44: b.ne            #0xb65a50
    // 0xb65a48: r4 = ""
    //     0xb65a48: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb65a4c: b               #0xb65a54
    // 0xb65a50: mov             x4, x0
    // 0xb65a54: ldur            x3, [fp, #-8]
    // 0xb65a58: ldur            d0, [fp, #-0x70]
    // 0xb65a5c: ldur            x0, [fp, #-0x18]
    // 0xb65a60: stur            x4, [fp, #-0x38]
    // 0xb65a64: LoadField: r1 = r3->field_f
    //     0xb65a64: ldur            w1, [x3, #0xf]
    // 0xb65a68: DecompressPointer r1
    //     0xb65a68: add             x1, x1, HEAP, lsl #32
    // 0xb65a6c: cmp             w1, NULL
    // 0xb65a70: b.eq            #0xb66300
    // 0xb65a74: r0 = of()
    //     0xb65a74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb65a78: LoadField: r1 = r0->field_87
    //     0xb65a78: ldur            w1, [x0, #0x87]
    // 0xb65a7c: DecompressPointer r1
    //     0xb65a7c: add             x1, x1, HEAP, lsl #32
    // 0xb65a80: LoadField: r0 = r1->field_2b
    //     0xb65a80: ldur            w0, [x1, #0x2b]
    // 0xb65a84: DecompressPointer r0
    //     0xb65a84: add             x0, x0, HEAP, lsl #32
    // 0xb65a88: r16 = 12.000000
    //     0xb65a88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb65a8c: ldr             x16, [x16, #0x9e8]
    // 0xb65a90: r30 = Instance_Color
    //     0xb65a90: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb65a94: stp             lr, x16, [SP]
    // 0xb65a98: mov             x1, x0
    // 0xb65a9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb65a9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb65aa0: ldr             x4, [x4, #0xaa0]
    // 0xb65aa4: r0 = copyWith()
    //     0xb65aa4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb65aa8: stur            x0, [fp, #-0x40]
    // 0xb65aac: r0 = Text()
    //     0xb65aac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb65ab0: mov             x1, x0
    // 0xb65ab4: ldur            x0, [fp, #-0x38]
    // 0xb65ab8: stur            x1, [fp, #-0x60]
    // 0xb65abc: StoreField: r1->field_b = r0
    //     0xb65abc: stur            w0, [x1, #0xb]
    // 0xb65ac0: ldur            x0, [fp, #-0x40]
    // 0xb65ac4: StoreField: r1->field_13 = r0
    //     0xb65ac4: stur            w0, [x1, #0x13]
    // 0xb65ac8: r0 = Padding()
    //     0xb65ac8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb65acc: mov             x3, x0
    // 0xb65ad0: r0 = Instance_EdgeInsets
    //     0xb65ad0: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb65ad4: ldr             x0, [x0, #0xc40]
    // 0xb65ad8: stur            x3, [fp, #-0x38]
    // 0xb65adc: StoreField: r3->field_f = r0
    //     0xb65adc: stur            w0, [x3, #0xf]
    // 0xb65ae0: ldur            x0, [fp, #-0x60]
    // 0xb65ae4: StoreField: r3->field_b = r0
    //     0xb65ae4: stur            w0, [x3, #0xb]
    // 0xb65ae8: r1 = Null
    //     0xb65ae8: mov             x1, NULL
    // 0xb65aec: r2 = 4
    //     0xb65aec: movz            x2, #0x4
    // 0xb65af0: r0 = AllocateArray()
    //     0xb65af0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb65af4: mov             x2, x0
    // 0xb65af8: ldur            x0, [fp, #-0x18]
    // 0xb65afc: stur            x2, [fp, #-0x40]
    // 0xb65b00: StoreField: r2->field_f = r0
    //     0xb65b00: stur            w0, [x2, #0xf]
    // 0xb65b04: ldur            x0, [fp, #-0x38]
    // 0xb65b08: StoreField: r2->field_13 = r0
    //     0xb65b08: stur            w0, [x2, #0x13]
    // 0xb65b0c: r1 = <Widget>
    //     0xb65b0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb65b10: r0 = AllocateGrowableArray()
    //     0xb65b10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb65b14: mov             x1, x0
    // 0xb65b18: ldur            x0, [fp, #-0x40]
    // 0xb65b1c: stur            x1, [fp, #-0x18]
    // 0xb65b20: StoreField: r1->field_f = r0
    //     0xb65b20: stur            w0, [x1, #0xf]
    // 0xb65b24: r0 = 4
    //     0xb65b24: movz            x0, #0x4
    // 0xb65b28: StoreField: r1->field_b = r0
    //     0xb65b28: stur            w0, [x1, #0xb]
    // 0xb65b2c: r0 = Row()
    //     0xb65b2c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb65b30: mov             x1, x0
    // 0xb65b34: r0 = Instance_Axis
    //     0xb65b34: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb65b38: stur            x1, [fp, #-0x38]
    // 0xb65b3c: StoreField: r1->field_f = r0
    //     0xb65b3c: stur            w0, [x1, #0xf]
    // 0xb65b40: r0 = Instance_MainAxisAlignment
    //     0xb65b40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb65b44: ldr             x0, [x0, #0xa08]
    // 0xb65b48: StoreField: r1->field_13 = r0
    //     0xb65b48: stur            w0, [x1, #0x13]
    // 0xb65b4c: r0 = Instance_MainAxisSize
    //     0xb65b4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb65b50: ldr             x0, [x0, #0xa10]
    // 0xb65b54: ArrayStore: r1[0] = r0  ; List_4
    //     0xb65b54: stur            w0, [x1, #0x17]
    // 0xb65b58: r0 = Instance_CrossAxisAlignment
    //     0xb65b58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb65b5c: ldr             x0, [x0, #0xa18]
    // 0xb65b60: StoreField: r1->field_1b = r0
    //     0xb65b60: stur            w0, [x1, #0x1b]
    // 0xb65b64: r0 = Instance_VerticalDirection
    //     0xb65b64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb65b68: ldr             x0, [x0, #0xa20]
    // 0xb65b6c: StoreField: r1->field_23 = r0
    //     0xb65b6c: stur            w0, [x1, #0x23]
    // 0xb65b70: r0 = Instance_Clip
    //     0xb65b70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb65b74: ldr             x0, [x0, #0x38]
    // 0xb65b78: StoreField: r1->field_2b = r0
    //     0xb65b78: stur            w0, [x1, #0x2b]
    // 0xb65b7c: StoreField: r1->field_2f = rZR
    //     0xb65b7c: stur            xzr, [x1, #0x2f]
    // 0xb65b80: ldur            x0, [fp, #-0x18]
    // 0xb65b84: StoreField: r1->field_b = r0
    //     0xb65b84: stur            w0, [x1, #0xb]
    // 0xb65b88: ldur            d0, [fp, #-0x70]
    // 0xb65b8c: r0 = inline_Allocate_Double()
    //     0xb65b8c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb65b90: add             x0, x0, #0x10
    //     0xb65b94: cmp             x2, x0
    //     0xb65b98: b.ls            #0xb66304
    //     0xb65b9c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb65ba0: sub             x0, x0, #0xf
    //     0xb65ba4: movz            x2, #0xe15c
    //     0xb65ba8: movk            x2, #0x3, lsl #16
    //     0xb65bac: stur            x2, [x0, #-1]
    // 0xb65bb0: StoreField: r0->field_7 = d0
    //     0xb65bb0: stur            d0, [x0, #7]
    // 0xb65bb4: stur            x0, [fp, #-0x18]
    // 0xb65bb8: r0 = Container()
    //     0xb65bb8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb65bbc: stur            x0, [fp, #-0x40]
    // 0xb65bc0: r16 = 20.000000
    //     0xb65bc0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb65bc4: ldr             x16, [x16, #0xac8]
    // 0xb65bc8: ldur            lr, [fp, #-0x18]
    // 0xb65bcc: stp             lr, x16, [SP, #0x10]
    // 0xb65bd0: ldur            x16, [fp, #-0x30]
    // 0xb65bd4: ldur            lr, [fp, #-0x38]
    // 0xb65bd8: stp             lr, x16, [SP]
    // 0xb65bdc: mov             x1, x0
    // 0xb65be0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb65be0: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb65be4: ldr             x4, [x4, #0x8c0]
    // 0xb65be8: r0 = Container()
    //     0xb65be8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb65bec: r0 = Padding()
    //     0xb65bec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb65bf0: mov             x1, x0
    // 0xb65bf4: r0 = Instance_EdgeInsets
    //     0xb65bf4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb65bf8: ldr             x0, [x0, #0xf70]
    // 0xb65bfc: stur            x1, [fp, #-0x18]
    // 0xb65c00: StoreField: r1->field_f = r0
    //     0xb65c00: stur            w0, [x1, #0xf]
    // 0xb65c04: ldur            x0, [fp, #-0x40]
    // 0xb65c08: StoreField: r1->field_b = r0
    //     0xb65c08: stur            w0, [x1, #0xb]
    // 0xb65c0c: r0 = Align()
    //     0xb65c0c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb65c10: mov             x1, x0
    // 0xb65c14: r0 = Instance_Alignment
    //     0xb65c14: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb65c18: ldr             x0, [x0, #0xfa0]
    // 0xb65c1c: StoreField: r1->field_f = r0
    //     0xb65c1c: stur            w0, [x1, #0xf]
    // 0xb65c20: ldur            x0, [fp, #-0x18]
    // 0xb65c24: StoreField: r1->field_b = r0
    //     0xb65c24: stur            w0, [x1, #0xb]
    // 0xb65c28: ldur            x2, [fp, #-0x20]
    // 0xb65c2c: ldur            x0, [fp, #-0x10]
    // 0xb65c30: stur            x1, [fp, #-0x18]
    // 0xb65c34: r0 = Visibility()
    //     0xb65c34: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb65c38: mov             x1, x0
    // 0xb65c3c: ldur            x0, [fp, #-0x18]
    // 0xb65c40: stur            x1, [fp, #-0x30]
    // 0xb65c44: StoreField: r1->field_b = r0
    //     0xb65c44: stur            w0, [x1, #0xb]
    // 0xb65c48: r0 = Instance_SizedBox
    //     0xb65c48: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb65c4c: StoreField: r1->field_f = r0
    //     0xb65c4c: stur            w0, [x1, #0xf]
    // 0xb65c50: ldur            x2, [fp, #-0x10]
    // 0xb65c54: StoreField: r1->field_13 = r2
    //     0xb65c54: stur            w2, [x1, #0x13]
    // 0xb65c58: r2 = false
    //     0xb65c58: add             x2, NULL, #0x30  ; false
    // 0xb65c5c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb65c5c: stur            w2, [x1, #0x17]
    // 0xb65c60: StoreField: r1->field_1b = r2
    //     0xb65c60: stur            w2, [x1, #0x1b]
    // 0xb65c64: StoreField: r1->field_1f = r2
    //     0xb65c64: stur            w2, [x1, #0x1f]
    // 0xb65c68: StoreField: r1->field_23 = r2
    //     0xb65c68: stur            w2, [x1, #0x23]
    // 0xb65c6c: StoreField: r1->field_27 = r2
    //     0xb65c6c: stur            w2, [x1, #0x27]
    // 0xb65c70: StoreField: r1->field_2b = r2
    //     0xb65c70: stur            w2, [x1, #0x2b]
    // 0xb65c74: ldur            x3, [fp, #-0x20]
    // 0xb65c78: LoadField: r4 = r3->field_13
    //     0xb65c78: ldur            w4, [x3, #0x13]
    // 0xb65c7c: DecompressPointer r4
    //     0xb65c7c: add             x4, x4, HEAP, lsl #32
    // 0xb65c80: cmp             w4, NULL
    // 0xb65c84: b.ne            #0xb65c90
    // 0xb65c88: r5 = Null
    //     0xb65c88: mov             x5, NULL
    // 0xb65c8c: b               #0xb65cbc
    // 0xb65c90: LoadField: r5 = r4->field_b7
    //     0xb65c90: ldur            w5, [x4, #0xb7]
    // 0xb65c94: DecompressPointer r5
    //     0xb65c94: add             x5, x5, HEAP, lsl #32
    // 0xb65c98: cmp             w5, NULL
    // 0xb65c9c: b.ne            #0xb65ca8
    // 0xb65ca0: r5 = Null
    //     0xb65ca0: mov             x5, NULL
    // 0xb65ca4: b               #0xb65cbc
    // 0xb65ca8: LoadField: r6 = r5->field_7
    //     0xb65ca8: ldur            w6, [x5, #7]
    // 0xb65cac: cbnz            w6, #0xb65cb8
    // 0xb65cb0: r5 = false
    //     0xb65cb0: add             x5, NULL, #0x30  ; false
    // 0xb65cb4: b               #0xb65cbc
    // 0xb65cb8: r5 = true
    //     0xb65cb8: add             x5, NULL, #0x20  ; true
    // 0xb65cbc: cmp             w5, NULL
    // 0xb65cc0: b.ne            #0xb65cc8
    // 0xb65cc4: r5 = false
    //     0xb65cc4: add             x5, NULL, #0x30  ; false
    // 0xb65cc8: stur            x5, [fp, #-0x10]
    // 0xb65ccc: cmp             w4, NULL
    // 0xb65cd0: b.ne            #0xb65cdc
    // 0xb65cd4: r4 = Null
    //     0xb65cd4: mov             x4, NULL
    // 0xb65cd8: b               #0xb65ce8
    // 0xb65cdc: LoadField: r6 = r4->field_8b
    //     0xb65cdc: ldur            w6, [x4, #0x8b]
    // 0xb65ce0: DecompressPointer r6
    //     0xb65ce0: add             x6, x6, HEAP, lsl #32
    // 0xb65ce4: mov             x4, x6
    // 0xb65ce8: cmp             w4, NULL
    // 0xb65cec: b.eq            #0xb65d00
    // 0xb65cf0: tbnz            w4, #4, #0xb65d00
    // 0xb65cf4: d0 = 38.000000
    //     0xb65cf4: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xb65cf8: ldr             d0, [x17, #0xd10]
    // 0xb65cfc: b               #0xb65d04
    // 0xb65d00: d0 = 4.000000
    //     0xb65d00: fmov            d0, #4.00000000
    // 0xb65d04: stur            d0, [fp, #-0x70]
    // 0xb65d08: r0 = EdgeInsets()
    //     0xb65d08: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb65d0c: d0 = 8.000000
    //     0xb65d0c: fmov            d0, #8.00000000
    // 0xb65d10: stur            x0, [fp, #-0x18]
    // 0xb65d14: StoreField: r0->field_7 = d0
    //     0xb65d14: stur            d0, [x0, #7]
    // 0xb65d18: StoreField: r0->field_f = rZR
    //     0xb65d18: stur            xzr, [x0, #0xf]
    // 0xb65d1c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb65d1c: stur            xzr, [x0, #0x17]
    // 0xb65d20: ldur            d0, [fp, #-0x70]
    // 0xb65d24: StoreField: r0->field_1f = d0
    //     0xb65d24: stur            d0, [x0, #0x1f]
    // 0xb65d28: r16 = <EdgeInsets>
    //     0xb65d28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb65d2c: ldr             x16, [x16, #0xda0]
    // 0xb65d30: r30 = Instance_EdgeInsets
    //     0xb65d30: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb65d34: ldr             lr, [lr, #0x668]
    // 0xb65d38: stp             lr, x16, [SP]
    // 0xb65d3c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb65d3c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb65d40: r0 = all()
    //     0xb65d40: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb65d44: stur            x0, [fp, #-0x38]
    // 0xb65d48: r16 = <Color>
    //     0xb65d48: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb65d4c: ldr             x16, [x16, #0xf80]
    // 0xb65d50: r30 = Instance_Color
    //     0xb65d50: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb65d54: stp             lr, x16, [SP]
    // 0xb65d58: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb65d58: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb65d5c: r0 = all()
    //     0xb65d5c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb65d60: stur            x0, [fp, #-0x40]
    // 0xb65d64: r0 = Radius()
    //     0xb65d64: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb65d68: d0 = 5.000000
    //     0xb65d68: fmov            d0, #5.00000000
    // 0xb65d6c: stur            x0, [fp, #-0x60]
    // 0xb65d70: StoreField: r0->field_7 = d0
    //     0xb65d70: stur            d0, [x0, #7]
    // 0xb65d74: StoreField: r0->field_f = d0
    //     0xb65d74: stur            d0, [x0, #0xf]
    // 0xb65d78: r0 = BorderRadius()
    //     0xb65d78: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb65d7c: mov             x1, x0
    // 0xb65d80: ldur            x0, [fp, #-0x60]
    // 0xb65d84: stur            x1, [fp, #-0x68]
    // 0xb65d88: StoreField: r1->field_7 = r0
    //     0xb65d88: stur            w0, [x1, #7]
    // 0xb65d8c: StoreField: r1->field_b = r0
    //     0xb65d8c: stur            w0, [x1, #0xb]
    // 0xb65d90: StoreField: r1->field_f = r0
    //     0xb65d90: stur            w0, [x1, #0xf]
    // 0xb65d94: StoreField: r1->field_13 = r0
    //     0xb65d94: stur            w0, [x1, #0x13]
    // 0xb65d98: r0 = RoundedRectangleBorder()
    //     0xb65d98: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb65d9c: mov             x1, x0
    // 0xb65da0: ldur            x0, [fp, #-0x68]
    // 0xb65da4: StoreField: r1->field_b = r0
    //     0xb65da4: stur            w0, [x1, #0xb]
    // 0xb65da8: r0 = Instance_BorderSide
    //     0xb65da8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb65dac: ldr             x0, [x0, #0xe20]
    // 0xb65db0: StoreField: r1->field_7 = r0
    //     0xb65db0: stur            w0, [x1, #7]
    // 0xb65db4: r16 = <RoundedRectangleBorder>
    //     0xb65db4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb65db8: ldr             x16, [x16, #0xf78]
    // 0xb65dbc: stp             x1, x16, [SP]
    // 0xb65dc0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb65dc0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb65dc4: r0 = all()
    //     0xb65dc4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb65dc8: stur            x0, [fp, #-0x60]
    // 0xb65dcc: r0 = ButtonStyle()
    //     0xb65dcc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb65dd0: mov             x1, x0
    // 0xb65dd4: ldur            x0, [fp, #-0x40]
    // 0xb65dd8: stur            x1, [fp, #-0x68]
    // 0xb65ddc: StoreField: r1->field_b = r0
    //     0xb65ddc: stur            w0, [x1, #0xb]
    // 0xb65de0: ldur            x0, [fp, #-0x38]
    // 0xb65de4: StoreField: r1->field_23 = r0
    //     0xb65de4: stur            w0, [x1, #0x23]
    // 0xb65de8: ldur            x0, [fp, #-0x60]
    // 0xb65dec: StoreField: r1->field_43 = r0
    //     0xb65dec: stur            w0, [x1, #0x43]
    // 0xb65df0: r0 = TextButtonThemeData()
    //     0xb65df0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb65df4: mov             x1, x0
    // 0xb65df8: ldur            x0, [fp, #-0x68]
    // 0xb65dfc: stur            x1, [fp, #-0x38]
    // 0xb65e00: StoreField: r1->field_7 = r0
    //     0xb65e00: stur            w0, [x1, #7]
    // 0xb65e04: ldur            x2, [fp, #-0x20]
    // 0xb65e08: LoadField: r0 = r2->field_13
    //     0xb65e08: ldur            w0, [x2, #0x13]
    // 0xb65e0c: DecompressPointer r0
    //     0xb65e0c: add             x0, x0, HEAP, lsl #32
    // 0xb65e10: cmp             w0, NULL
    // 0xb65e14: b.ne            #0xb65e20
    // 0xb65e18: r5 = Null
    //     0xb65e18: mov             x5, NULL
    // 0xb65e1c: b               #0xb65e2c
    // 0xb65e20: LoadField: r3 = r0->field_b7
    //     0xb65e20: ldur            w3, [x0, #0xb7]
    // 0xb65e24: DecompressPointer r3
    //     0xb65e24: add             x3, x3, HEAP, lsl #32
    // 0xb65e28: mov             x5, x3
    // 0xb65e2c: ldur            x4, [fp, #-8]
    // 0xb65e30: ldur            x3, [fp, #-0x10]
    // 0xb65e34: ldur            x0, [fp, #-0x18]
    // 0xb65e38: str             x5, [SP]
    // 0xb65e3c: r0 = _interpolateSingle()
    //     0xb65e3c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb65e40: mov             x2, x0
    // 0xb65e44: ldur            x0, [fp, #-8]
    // 0xb65e48: stur            x2, [fp, #-0x40]
    // 0xb65e4c: LoadField: r1 = r0->field_f
    //     0xb65e4c: ldur            w1, [x0, #0xf]
    // 0xb65e50: DecompressPointer r1
    //     0xb65e50: add             x1, x1, HEAP, lsl #32
    // 0xb65e54: cmp             w1, NULL
    // 0xb65e58: b.eq            #0xb6631c
    // 0xb65e5c: r0 = of()
    //     0xb65e5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb65e60: LoadField: r1 = r0->field_87
    //     0xb65e60: ldur            w1, [x0, #0x87]
    // 0xb65e64: DecompressPointer r1
    //     0xb65e64: add             x1, x1, HEAP, lsl #32
    // 0xb65e68: LoadField: r0 = r1->field_2b
    //     0xb65e68: ldur            w0, [x1, #0x2b]
    // 0xb65e6c: DecompressPointer r0
    //     0xb65e6c: add             x0, x0, HEAP, lsl #32
    // 0xb65e70: r16 = 12.000000
    //     0xb65e70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb65e74: ldr             x16, [x16, #0x9e8]
    // 0xb65e78: r30 = Instance_Color
    //     0xb65e78: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb65e7c: stp             lr, x16, [SP]
    // 0xb65e80: mov             x1, x0
    // 0xb65e84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb65e84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb65e88: ldr             x4, [x4, #0xaa0]
    // 0xb65e8c: r0 = copyWith()
    //     0xb65e8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb65e90: stur            x0, [fp, #-0x60]
    // 0xb65e94: r0 = Text()
    //     0xb65e94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb65e98: mov             x3, x0
    // 0xb65e9c: ldur            x0, [fp, #-0x40]
    // 0xb65ea0: stur            x3, [fp, #-0x68]
    // 0xb65ea4: StoreField: r3->field_b = r0
    //     0xb65ea4: stur            w0, [x3, #0xb]
    // 0xb65ea8: ldur            x0, [fp, #-0x60]
    // 0xb65eac: StoreField: r3->field_13 = r0
    //     0xb65eac: stur            w0, [x3, #0x13]
    // 0xb65eb0: r1 = Function '<anonymous closure>':.
    //     0xb65eb0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56348] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb65eb4: ldr             x1, [x1, #0x348]
    // 0xb65eb8: r2 = Null
    //     0xb65eb8: mov             x2, NULL
    // 0xb65ebc: r0 = AllocateClosure()
    //     0xb65ebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb65ec0: stur            x0, [fp, #-0x40]
    // 0xb65ec4: r0 = TextButton()
    //     0xb65ec4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb65ec8: mov             x1, x0
    // 0xb65ecc: ldur            x0, [fp, #-0x40]
    // 0xb65ed0: stur            x1, [fp, #-0x60]
    // 0xb65ed4: StoreField: r1->field_b = r0
    //     0xb65ed4: stur            w0, [x1, #0xb]
    // 0xb65ed8: r0 = false
    //     0xb65ed8: add             x0, NULL, #0x30  ; false
    // 0xb65edc: StoreField: r1->field_27 = r0
    //     0xb65edc: stur            w0, [x1, #0x27]
    // 0xb65ee0: r2 = true
    //     0xb65ee0: add             x2, NULL, #0x20  ; true
    // 0xb65ee4: StoreField: r1->field_2f = r2
    //     0xb65ee4: stur            w2, [x1, #0x2f]
    // 0xb65ee8: ldur            x3, [fp, #-0x68]
    // 0xb65eec: StoreField: r1->field_37 = r3
    //     0xb65eec: stur            w3, [x1, #0x37]
    // 0xb65ef0: r0 = TextButtonTheme()
    //     0xb65ef0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb65ef4: mov             x1, x0
    // 0xb65ef8: ldur            x0, [fp, #-0x38]
    // 0xb65efc: stur            x1, [fp, #-0x40]
    // 0xb65f00: StoreField: r1->field_f = r0
    //     0xb65f00: stur            w0, [x1, #0xf]
    // 0xb65f04: ldur            x0, [fp, #-0x60]
    // 0xb65f08: StoreField: r1->field_b = r0
    //     0xb65f08: stur            w0, [x1, #0xb]
    // 0xb65f0c: r0 = Padding()
    //     0xb65f0c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb65f10: mov             x1, x0
    // 0xb65f14: ldur            x0, [fp, #-0x18]
    // 0xb65f18: stur            x1, [fp, #-0x38]
    // 0xb65f1c: StoreField: r1->field_f = r0
    //     0xb65f1c: stur            w0, [x1, #0xf]
    // 0xb65f20: ldur            x0, [fp, #-0x40]
    // 0xb65f24: StoreField: r1->field_b = r0
    //     0xb65f24: stur            w0, [x1, #0xb]
    // 0xb65f28: r0 = Visibility()
    //     0xb65f28: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb65f2c: mov             x2, x0
    // 0xb65f30: ldur            x0, [fp, #-0x38]
    // 0xb65f34: stur            x2, [fp, #-0x18]
    // 0xb65f38: StoreField: r2->field_b = r0
    //     0xb65f38: stur            w0, [x2, #0xb]
    // 0xb65f3c: r0 = Instance_SizedBox
    //     0xb65f3c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb65f40: StoreField: r2->field_f = r0
    //     0xb65f40: stur            w0, [x2, #0xf]
    // 0xb65f44: ldur            x1, [fp, #-0x10]
    // 0xb65f48: StoreField: r2->field_13 = r1
    //     0xb65f48: stur            w1, [x2, #0x13]
    // 0xb65f4c: r3 = false
    //     0xb65f4c: add             x3, NULL, #0x30  ; false
    // 0xb65f50: ArrayStore: r2[0] = r3  ; List_4
    //     0xb65f50: stur            w3, [x2, #0x17]
    // 0xb65f54: StoreField: r2->field_1b = r3
    //     0xb65f54: stur            w3, [x2, #0x1b]
    // 0xb65f58: StoreField: r2->field_1f = r3
    //     0xb65f58: stur            w3, [x2, #0x1f]
    // 0xb65f5c: StoreField: r2->field_23 = r3
    //     0xb65f5c: stur            w3, [x2, #0x23]
    // 0xb65f60: StoreField: r2->field_27 = r3
    //     0xb65f60: stur            w3, [x2, #0x27]
    // 0xb65f64: StoreField: r2->field_2b = r3
    //     0xb65f64: stur            w3, [x2, #0x2b]
    // 0xb65f68: ldur            x4, [fp, #-0x20]
    // 0xb65f6c: LoadField: r1 = r4->field_13
    //     0xb65f6c: ldur            w1, [x4, #0x13]
    // 0xb65f70: DecompressPointer r1
    //     0xb65f70: add             x1, x1, HEAP, lsl #32
    // 0xb65f74: cmp             w1, NULL
    // 0xb65f78: b.ne            #0xb65f84
    // 0xb65f7c: r1 = Null
    //     0xb65f7c: mov             x1, NULL
    // 0xb65f80: b               #0xb65f90
    // 0xb65f84: LoadField: r5 = r1->field_8b
    //     0xb65f84: ldur            w5, [x1, #0x8b]
    // 0xb65f88: DecompressPointer r5
    //     0xb65f88: add             x5, x5, HEAP, lsl #32
    // 0xb65f8c: mov             x1, x5
    // 0xb65f90: cmp             w1, NULL
    // 0xb65f94: b.ne            #0xb65fa0
    // 0xb65f98: r8 = false
    //     0xb65f98: add             x8, NULL, #0x30  ; false
    // 0xb65f9c: b               #0xb65fa4
    // 0xb65fa0: mov             x8, x1
    // 0xb65fa4: ldur            x5, [fp, #-8]
    // 0xb65fa8: ldur            x7, [fp, #-0x28]
    // 0xb65fac: ldur            x6, [fp, #-0x30]
    // 0xb65fb0: stur            x8, [fp, #-0x10]
    // 0xb65fb4: LoadField: r1 = r5->field_f
    //     0xb65fb4: ldur            w1, [x5, #0xf]
    // 0xb65fb8: DecompressPointer r1
    //     0xb65fb8: add             x1, x1, HEAP, lsl #32
    // 0xb65fbc: cmp             w1, NULL
    // 0xb65fc0: b.eq            #0xb66320
    // 0xb65fc4: r0 = of()
    //     0xb65fc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb65fc8: LoadField: r1 = r0->field_5b
    //     0xb65fc8: ldur            w1, [x0, #0x5b]
    // 0xb65fcc: DecompressPointer r1
    //     0xb65fcc: add             x1, x1, HEAP, lsl #32
    // 0xb65fd0: r16 = <Color>
    //     0xb65fd0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb65fd4: ldr             x16, [x16, #0xf80]
    // 0xb65fd8: stp             x1, x16, [SP]
    // 0xb65fdc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb65fdc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb65fe0: r0 = all()
    //     0xb65fe0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb65fe4: stur            x0, [fp, #-0x38]
    // 0xb65fe8: r0 = Radius()
    //     0xb65fe8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb65fec: d0 = 5.000000
    //     0xb65fec: fmov            d0, #5.00000000
    // 0xb65ff0: stur            x0, [fp, #-0x40]
    // 0xb65ff4: StoreField: r0->field_7 = d0
    //     0xb65ff4: stur            d0, [x0, #7]
    // 0xb65ff8: StoreField: r0->field_f = d0
    //     0xb65ff8: stur            d0, [x0, #0xf]
    // 0xb65ffc: r0 = BorderRadius()
    //     0xb65ffc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb66000: mov             x1, x0
    // 0xb66004: ldur            x0, [fp, #-0x40]
    // 0xb66008: stur            x1, [fp, #-0x60]
    // 0xb6600c: StoreField: r1->field_7 = r0
    //     0xb6600c: stur            w0, [x1, #7]
    // 0xb66010: StoreField: r1->field_b = r0
    //     0xb66010: stur            w0, [x1, #0xb]
    // 0xb66014: StoreField: r1->field_f = r0
    //     0xb66014: stur            w0, [x1, #0xf]
    // 0xb66018: StoreField: r1->field_13 = r0
    //     0xb66018: stur            w0, [x1, #0x13]
    // 0xb6601c: r0 = RoundedRectangleBorder()
    //     0xb6601c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb66020: mov             x1, x0
    // 0xb66024: ldur            x0, [fp, #-0x60]
    // 0xb66028: StoreField: r1->field_b = r0
    //     0xb66028: stur            w0, [x1, #0xb]
    // 0xb6602c: r0 = Instance_BorderSide
    //     0xb6602c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb66030: ldr             x0, [x0, #0xe20]
    // 0xb66034: StoreField: r1->field_7 = r0
    //     0xb66034: stur            w0, [x1, #7]
    // 0xb66038: r16 = <RoundedRectangleBorder>
    //     0xb66038: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb6603c: ldr             x16, [x16, #0xf78]
    // 0xb66040: stp             x1, x16, [SP]
    // 0xb66044: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb66044: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb66048: r0 = all()
    //     0xb66048: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb6604c: stur            x0, [fp, #-0x40]
    // 0xb66050: r0 = ButtonStyle()
    //     0xb66050: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb66054: mov             x1, x0
    // 0xb66058: ldur            x0, [fp, #-0x38]
    // 0xb6605c: stur            x1, [fp, #-0x60]
    // 0xb66060: StoreField: r1->field_b = r0
    //     0xb66060: stur            w0, [x1, #0xb]
    // 0xb66064: ldur            x0, [fp, #-0x40]
    // 0xb66068: StoreField: r1->field_43 = r0
    //     0xb66068: stur            w0, [x1, #0x43]
    // 0xb6606c: r0 = TextButtonThemeData()
    //     0xb6606c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb66070: mov             x2, x0
    // 0xb66074: ldur            x0, [fp, #-0x60]
    // 0xb66078: stur            x2, [fp, #-0x38]
    // 0xb6607c: StoreField: r2->field_7 = r0
    //     0xb6607c: stur            w0, [x2, #7]
    // 0xb66080: ldur            x0, [fp, #-8]
    // 0xb66084: LoadField: r1 = r0->field_f
    //     0xb66084: ldur            w1, [x0, #0xf]
    // 0xb66088: DecompressPointer r1
    //     0xb66088: add             x1, x1, HEAP, lsl #32
    // 0xb6608c: cmp             w1, NULL
    // 0xb66090: b.eq            #0xb66324
    // 0xb66094: r0 = of()
    //     0xb66094: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb66098: LoadField: r1 = r0->field_87
    //     0xb66098: ldur            w1, [x0, #0x87]
    // 0xb6609c: DecompressPointer r1
    //     0xb6609c: add             x1, x1, HEAP, lsl #32
    // 0xb660a0: LoadField: r0 = r1->field_2b
    //     0xb660a0: ldur            w0, [x1, #0x2b]
    // 0xb660a4: DecompressPointer r0
    //     0xb660a4: add             x0, x0, HEAP, lsl #32
    // 0xb660a8: r16 = 12.000000
    //     0xb660a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb660ac: ldr             x16, [x16, #0x9e8]
    // 0xb660b0: r30 = Instance_Color
    //     0xb660b0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb660b4: stp             lr, x16, [SP]
    // 0xb660b8: mov             x1, x0
    // 0xb660bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb660bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb660c0: ldr             x4, [x4, #0xaa0]
    // 0xb660c4: r0 = copyWith()
    //     0xb660c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb660c8: stur            x0, [fp, #-8]
    // 0xb660cc: r0 = Text()
    //     0xb660cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb660d0: mov             x3, x0
    // 0xb660d4: r0 = "Customisable"
    //     0xb660d4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb660d8: ldr             x0, [x0, #0x970]
    // 0xb660dc: stur            x3, [fp, #-0x40]
    // 0xb660e0: StoreField: r3->field_b = r0
    //     0xb660e0: stur            w0, [x3, #0xb]
    // 0xb660e4: ldur            x0, [fp, #-8]
    // 0xb660e8: StoreField: r3->field_13 = r0
    //     0xb660e8: stur            w0, [x3, #0x13]
    // 0xb660ec: r1 = Function '<anonymous closure>':.
    //     0xb660ec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56350] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb660f0: ldr             x1, [x1, #0x350]
    // 0xb660f4: r2 = Null
    //     0xb660f4: mov             x2, NULL
    // 0xb660f8: r0 = AllocateClosure()
    //     0xb660f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb660fc: stur            x0, [fp, #-8]
    // 0xb66100: r0 = TextButton()
    //     0xb66100: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb66104: mov             x1, x0
    // 0xb66108: ldur            x0, [fp, #-8]
    // 0xb6610c: stur            x1, [fp, #-0x60]
    // 0xb66110: StoreField: r1->field_b = r0
    //     0xb66110: stur            w0, [x1, #0xb]
    // 0xb66114: r0 = false
    //     0xb66114: add             x0, NULL, #0x30  ; false
    // 0xb66118: StoreField: r1->field_27 = r0
    //     0xb66118: stur            w0, [x1, #0x27]
    // 0xb6611c: r2 = true
    //     0xb6611c: add             x2, NULL, #0x20  ; true
    // 0xb66120: StoreField: r1->field_2f = r2
    //     0xb66120: stur            w2, [x1, #0x2f]
    // 0xb66124: ldur            x3, [fp, #-0x40]
    // 0xb66128: StoreField: r1->field_37 = r3
    //     0xb66128: stur            w3, [x1, #0x37]
    // 0xb6612c: r0 = TextButtonTheme()
    //     0xb6612c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb66130: mov             x1, x0
    // 0xb66134: ldur            x0, [fp, #-0x38]
    // 0xb66138: stur            x1, [fp, #-8]
    // 0xb6613c: StoreField: r1->field_f = r0
    //     0xb6613c: stur            w0, [x1, #0xf]
    // 0xb66140: ldur            x0, [fp, #-0x60]
    // 0xb66144: StoreField: r1->field_b = r0
    //     0xb66144: stur            w0, [x1, #0xb]
    // 0xb66148: r0 = SizedBox()
    //     0xb66148: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6614c: mov             x1, x0
    // 0xb66150: r0 = 30.000000
    //     0xb66150: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb66154: ldr             x0, [x0, #0x768]
    // 0xb66158: stur            x1, [fp, #-0x38]
    // 0xb6615c: StoreField: r1->field_13 = r0
    //     0xb6615c: stur            w0, [x1, #0x13]
    // 0xb66160: ldur            x0, [fp, #-8]
    // 0xb66164: StoreField: r1->field_b = r0
    //     0xb66164: stur            w0, [x1, #0xb]
    // 0xb66168: r0 = Padding()
    //     0xb66168: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6616c: mov             x1, x0
    // 0xb66170: r0 = Instance_EdgeInsets
    //     0xb66170: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb66174: ldr             x0, [x0, #0xe68]
    // 0xb66178: stur            x1, [fp, #-8]
    // 0xb6617c: StoreField: r1->field_f = r0
    //     0xb6617c: stur            w0, [x1, #0xf]
    // 0xb66180: ldur            x0, [fp, #-0x38]
    // 0xb66184: StoreField: r1->field_b = r0
    //     0xb66184: stur            w0, [x1, #0xb]
    // 0xb66188: r0 = Visibility()
    //     0xb66188: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb6618c: mov             x3, x0
    // 0xb66190: ldur            x0, [fp, #-8]
    // 0xb66194: stur            x3, [fp, #-0x38]
    // 0xb66198: StoreField: r3->field_b = r0
    //     0xb66198: stur            w0, [x3, #0xb]
    // 0xb6619c: r0 = Instance_SizedBox
    //     0xb6619c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb661a0: StoreField: r3->field_f = r0
    //     0xb661a0: stur            w0, [x3, #0xf]
    // 0xb661a4: ldur            x0, [fp, #-0x10]
    // 0xb661a8: StoreField: r3->field_13 = r0
    //     0xb661a8: stur            w0, [x3, #0x13]
    // 0xb661ac: r0 = false
    //     0xb661ac: add             x0, NULL, #0x30  ; false
    // 0xb661b0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb661b0: stur            w0, [x3, #0x17]
    // 0xb661b4: StoreField: r3->field_1b = r0
    //     0xb661b4: stur            w0, [x3, #0x1b]
    // 0xb661b8: StoreField: r3->field_1f = r0
    //     0xb661b8: stur            w0, [x3, #0x1f]
    // 0xb661bc: StoreField: r3->field_23 = r0
    //     0xb661bc: stur            w0, [x3, #0x23]
    // 0xb661c0: StoreField: r3->field_27 = r0
    //     0xb661c0: stur            w0, [x3, #0x27]
    // 0xb661c4: StoreField: r3->field_2b = r0
    //     0xb661c4: stur            w0, [x3, #0x2b]
    // 0xb661c8: r1 = Null
    //     0xb661c8: mov             x1, NULL
    // 0xb661cc: r2 = 8
    //     0xb661cc: movz            x2, #0x8
    // 0xb661d0: r0 = AllocateArray()
    //     0xb661d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb661d4: mov             x2, x0
    // 0xb661d8: ldur            x0, [fp, #-0x28]
    // 0xb661dc: stur            x2, [fp, #-8]
    // 0xb661e0: StoreField: r2->field_f = r0
    //     0xb661e0: stur            w0, [x2, #0xf]
    // 0xb661e4: ldur            x0, [fp, #-0x30]
    // 0xb661e8: StoreField: r2->field_13 = r0
    //     0xb661e8: stur            w0, [x2, #0x13]
    // 0xb661ec: ldur            x0, [fp, #-0x18]
    // 0xb661f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb661f0: stur            w0, [x2, #0x17]
    // 0xb661f4: ldur            x0, [fp, #-0x38]
    // 0xb661f8: StoreField: r2->field_1b = r0
    //     0xb661f8: stur            w0, [x2, #0x1b]
    // 0xb661fc: r1 = <Widget>
    //     0xb661fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb66200: r0 = AllocateGrowableArray()
    //     0xb66200: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb66204: mov             x1, x0
    // 0xb66208: ldur            x0, [fp, #-8]
    // 0xb6620c: stur            x1, [fp, #-0x10]
    // 0xb66210: StoreField: r1->field_f = r0
    //     0xb66210: stur            w0, [x1, #0xf]
    // 0xb66214: r0 = 8
    //     0xb66214: movz            x0, #0x8
    // 0xb66218: StoreField: r1->field_b = r0
    //     0xb66218: stur            w0, [x1, #0xb]
    // 0xb6621c: r0 = Stack()
    //     0xb6621c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb66220: mov             x1, x0
    // 0xb66224: r0 = Instance_Alignment
    //     0xb66224: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb66228: ldr             x0, [x0, #0x5b8]
    // 0xb6622c: stur            x1, [fp, #-8]
    // 0xb66230: StoreField: r1->field_f = r0
    //     0xb66230: stur            w0, [x1, #0xf]
    // 0xb66234: r0 = Instance_StackFit
    //     0xb66234: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb66238: ldr             x0, [x0, #0xfa8]
    // 0xb6623c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6623c: stur            w0, [x1, #0x17]
    // 0xb66240: r0 = Instance_Clip
    //     0xb66240: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb66244: ldr             x0, [x0, #0x7e0]
    // 0xb66248: StoreField: r1->field_1b = r0
    //     0xb66248: stur            w0, [x1, #0x1b]
    // 0xb6624c: ldur            x0, [fp, #-0x10]
    // 0xb66250: StoreField: r1->field_b = r0
    //     0xb66250: stur            w0, [x1, #0xb]
    // 0xb66254: r0 = InkWell()
    //     0xb66254: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb66258: mov             x3, x0
    // 0xb6625c: ldur            x0, [fp, #-8]
    // 0xb66260: stur            x3, [fp, #-0x10]
    // 0xb66264: StoreField: r3->field_b = r0
    //     0xb66264: stur            w0, [x3, #0xb]
    // 0xb66268: ldur            x2, [fp, #-0x20]
    // 0xb6626c: r1 = Function '<anonymous closure>':.
    //     0xb6626c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56358] AnonymousClosure: (0xb66328), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::glassThemeSlider (0xb65130)
    //     0xb66270: ldr             x1, [x1, #0x358]
    // 0xb66274: r0 = AllocateClosure()
    //     0xb66274: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb66278: ldur            x2, [fp, #-0x10]
    // 0xb6627c: StoreField: r2->field_f = r0
    //     0xb6627c: stur            w0, [x2, #0xf]
    // 0xb66280: r0 = true
    //     0xb66280: add             x0, NULL, #0x20  ; true
    // 0xb66284: StoreField: r2->field_43 = r0
    //     0xb66284: stur            w0, [x2, #0x43]
    // 0xb66288: r1 = Instance_BoxShape
    //     0xb66288: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6628c: ldr             x1, [x1, #0x80]
    // 0xb66290: StoreField: r2->field_47 = r1
    //     0xb66290: stur            w1, [x2, #0x47]
    // 0xb66294: StoreField: r2->field_6f = r0
    //     0xb66294: stur            w0, [x2, #0x6f]
    // 0xb66298: r1 = false
    //     0xb66298: add             x1, NULL, #0x30  ; false
    // 0xb6629c: StoreField: r2->field_73 = r1
    //     0xb6629c: stur            w1, [x2, #0x73]
    // 0xb662a0: StoreField: r2->field_83 = r0
    //     0xb662a0: stur            w0, [x2, #0x83]
    // 0xb662a4: StoreField: r2->field_7b = r1
    //     0xb662a4: stur            w1, [x2, #0x7b]
    // 0xb662a8: r0 = AnimatedContainer()
    //     0xb662a8: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb662ac: stur            x0, [fp, #-8]
    // 0xb662b0: r16 = Instance_Cubic
    //     0xb662b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb662b4: ldr             x16, [x16, #0xaf8]
    // 0xb662b8: str             x16, [SP]
    // 0xb662bc: mov             x1, x0
    // 0xb662c0: ldur            x2, [fp, #-0x10]
    // 0xb662c4: r3 = Instance_Duration
    //     0xb662c4: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb662c8: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb662c8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb662cc: ldr             x4, [x4, #0xbc8]
    // 0xb662d0: r0 = AnimatedContainer()
    //     0xb662d0: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb662d4: ldur            x0, [fp, #-8]
    // 0xb662d8: LeaveFrame
    //     0xb662d8: mov             SP, fp
    //     0xb662dc: ldp             fp, lr, [SP], #0x10
    // 0xb662e0: ret
    //     0xb662e0: ret             
    // 0xb662e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb662e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb662e8: b               #0xb65154
    // 0xb662ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb662ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb662f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb662f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb662f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb662f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb662f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb662f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb662fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb662fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66300: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb66300: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb66304: SaveReg d0
    //     0xb66304: str             q0, [SP, #-0x10]!
    // 0xb66308: SaveReg r1
    //     0xb66308: str             x1, [SP, #-8]!
    // 0xb6630c: r0 = AllocateDouble()
    //     0xb6630c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb66310: RestoreReg r1
    //     0xb66310: ldr             x1, [SP], #8
    // 0xb66314: RestoreReg d0
    //     0xb66314: ldr             q0, [SP], #0x10
    // 0xb66318: b               #0xb65bb0
    // 0xb6631c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6631c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66320: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66320: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66324: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66324: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb66328, size: 0x13c
    // 0xb66328: EnterFrame
    //     0xb66328: stp             fp, lr, [SP, #-0x10]!
    //     0xb6632c: mov             fp, SP
    // 0xb66330: AllocStack(0x48)
    //     0xb66330: sub             SP, SP, #0x48
    // 0xb66334: SetupParameters()
    //     0xb66334: ldr             x0, [fp, #0x10]
    //     0xb66338: ldur            w1, [x0, #0x17]
    //     0xb6633c: add             x1, x1, HEAP, lsl #32
    // 0xb66340: CheckStackOverflow
    //     0xb66340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66344: cmp             SP, x16
    //     0xb66348: b.ls            #0xb66458
    // 0xb6634c: LoadField: r0 = r1->field_f
    //     0xb6634c: ldur            w0, [x1, #0xf]
    // 0xb66350: DecompressPointer r0
    //     0xb66350: add             x0, x0, HEAP, lsl #32
    // 0xb66354: LoadField: r2 = r0->field_b
    //     0xb66354: ldur            w2, [x0, #0xb]
    // 0xb66358: DecompressPointer r2
    //     0xb66358: add             x2, x2, HEAP, lsl #32
    // 0xb6635c: cmp             w2, NULL
    // 0xb66360: b.eq            #0xb66460
    // 0xb66364: LoadField: r0 = r2->field_2f
    //     0xb66364: ldur            w0, [x2, #0x2f]
    // 0xb66368: DecompressPointer r0
    //     0xb66368: add             x0, x0, HEAP, lsl #32
    // 0xb6636c: cmp             w0, NULL
    // 0xb66370: b.ne            #0xb66378
    // 0xb66374: r0 = ""
    //     0xb66374: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb66378: LoadField: r3 = r2->field_2b
    //     0xb66378: ldur            w3, [x2, #0x2b]
    // 0xb6637c: DecompressPointer r3
    //     0xb6637c: add             x3, x3, HEAP, lsl #32
    // 0xb66380: cmp             w3, NULL
    // 0xb66384: b.ne            #0xb6638c
    // 0xb66388: r3 = ""
    //     0xb66388: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6638c: LoadField: r4 = r2->field_37
    //     0xb6638c: ldur            w4, [x2, #0x37]
    // 0xb66390: DecompressPointer r4
    //     0xb66390: add             x4, x4, HEAP, lsl #32
    // 0xb66394: LoadField: r5 = r2->field_3b
    //     0xb66394: ldur            w5, [x2, #0x3b]
    // 0xb66398: DecompressPointer r5
    //     0xb66398: add             x5, x5, HEAP, lsl #32
    // 0xb6639c: LoadField: r6 = r2->field_33
    //     0xb6639c: ldur            w6, [x2, #0x33]
    // 0xb663a0: DecompressPointer r6
    //     0xb663a0: add             x6, x6, HEAP, lsl #32
    // 0xb663a4: cmp             w6, NULL
    // 0xb663a8: b.ne            #0xb663b0
    // 0xb663ac: r6 = ""
    //     0xb663ac: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb663b0: LoadField: r7 = r2->field_f
    //     0xb663b0: ldur            w7, [x2, #0xf]
    // 0xb663b4: DecompressPointer r7
    //     0xb663b4: add             x7, x7, HEAP, lsl #32
    // 0xb663b8: cmp             w7, NULL
    // 0xb663bc: b.ne            #0xb663c4
    // 0xb663c0: r7 = ""
    //     0xb663c0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb663c4: LoadField: r8 = r1->field_13
    //     0xb663c4: ldur            w8, [x1, #0x13]
    // 0xb663c8: DecompressPointer r8
    //     0xb663c8: add             x8, x8, HEAP, lsl #32
    // 0xb663cc: cmp             w8, NULL
    // 0xb663d0: b.ne            #0xb663dc
    // 0xb663d4: r1 = Null
    //     0xb663d4: mov             x1, NULL
    // 0xb663d8: b               #0xb663e4
    // 0xb663dc: LoadField: r1 = r8->field_2b
    //     0xb663dc: ldur            w1, [x8, #0x2b]
    // 0xb663e0: DecompressPointer r1
    //     0xb663e0: add             x1, x1, HEAP, lsl #32
    // 0xb663e4: cmp             w8, NULL
    // 0xb663e8: b.ne            #0xb663f4
    // 0xb663ec: r8 = Null
    //     0xb663ec: mov             x8, NULL
    // 0xb663f0: b               #0xb66414
    // 0xb663f4: LoadField: r9 = r8->field_3b
    //     0xb663f4: ldur            w9, [x8, #0x3b]
    // 0xb663f8: DecompressPointer r9
    //     0xb663f8: add             x9, x9, HEAP, lsl #32
    // 0xb663fc: cmp             w9, NULL
    // 0xb66400: b.ne            #0xb6640c
    // 0xb66404: r8 = Null
    //     0xb66404: mov             x8, NULL
    // 0xb66408: b               #0xb66414
    // 0xb6640c: LoadField: r8 = r9->field_b
    //     0xb6640c: ldur            w8, [x9, #0xb]
    // 0xb66410: DecompressPointer r8
    //     0xb66410: add             x8, x8, HEAP, lsl #32
    // 0xb66414: LoadField: r9 = r2->field_53
    //     0xb66414: ldur            w9, [x2, #0x53]
    // 0xb66418: DecompressPointer r9
    //     0xb66418: add             x9, x9, HEAP, lsl #32
    // 0xb6641c: stp             x0, x9, [SP, #0x38]
    // 0xb66420: stp             x4, x3, [SP, #0x28]
    // 0xb66424: stp             x6, x5, [SP, #0x18]
    // 0xb66428: stp             x1, x7, [SP, #8]
    // 0xb6642c: str             x8, [SP]
    // 0xb66430: r4 = 0
    //     0xb66430: movz            x4, #0
    // 0xb66434: ldr             x0, [SP, #0x40]
    // 0xb66438: r16 = UnlinkedCall_0x613b5c
    //     0xb66438: add             x16, PP, #0x56, lsl #12  ; [pp+0x56360] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb6643c: add             x16, x16, #0x360
    // 0xb66440: ldp             x5, lr, [x16]
    // 0xb66444: blr             lr
    // 0xb66448: r0 = Null
    //     0xb66448: mov             x0, NULL
    // 0xb6644c: LeaveFrame
    //     0xb6644c: mov             SP, fp
    //     0xb66450: ldp             fp, lr, [SP], #0x10
    // 0xb66454: ret
    //     0xb66454: ret             
    // 0xb66458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb66458: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6645c: b               #0xb6634c
    // 0xb66460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66460: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb66464, size: 0x8c
    // 0xb66464: EnterFrame
    //     0xb66464: stp             fp, lr, [SP, #-0x10]!
    //     0xb66468: mov             fp, SP
    // 0xb6646c: AllocStack(0x8)
    //     0xb6646c: sub             SP, SP, #8
    // 0xb66470: SetupParameters()
    //     0xb66470: ldr             x0, [fp, #0x18]
    //     0xb66474: ldur            w1, [x0, #0x17]
    //     0xb66478: add             x1, x1, HEAP, lsl #32
    //     0xb6647c: stur            x1, [fp, #-8]
    // 0xb66480: CheckStackOverflow
    //     0xb66480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66484: cmp             SP, x16
    //     0xb66488: b.ls            #0xb664e8
    // 0xb6648c: r1 = 1
    //     0xb6648c: movz            x1, #0x1
    // 0xb66490: r0 = AllocateContext()
    //     0xb66490: bl              #0x16f6108  ; AllocateContextStub
    // 0xb66494: mov             x1, x0
    // 0xb66498: ldur            x0, [fp, #-8]
    // 0xb6649c: StoreField: r1->field_b = r0
    //     0xb6649c: stur            w0, [x1, #0xb]
    // 0xb664a0: ldr             x2, [fp, #0x10]
    // 0xb664a4: StoreField: r1->field_f = r2
    //     0xb664a4: stur            w2, [x1, #0xf]
    // 0xb664a8: LoadField: r2 = r0->field_b
    //     0xb664a8: ldur            w2, [x0, #0xb]
    // 0xb664ac: DecompressPointer r2
    //     0xb664ac: add             x2, x2, HEAP, lsl #32
    // 0xb664b0: LoadField: r0 = r2->field_f
    //     0xb664b0: ldur            w0, [x2, #0xf]
    // 0xb664b4: DecompressPointer r0
    //     0xb664b4: add             x0, x0, HEAP, lsl #32
    // 0xb664b8: mov             x2, x1
    // 0xb664bc: stur            x0, [fp, #-8]
    // 0xb664c0: r1 = Function '<anonymous closure>':.
    //     0xb664c0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56370] AnonymousClosure: (0xb664f0), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xb62208)
    //     0xb664c4: ldr             x1, [x1, #0x370]
    // 0xb664c8: r0 = AllocateClosure()
    //     0xb664c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb664cc: ldur            x1, [fp, #-8]
    // 0xb664d0: mov             x2, x0
    // 0xb664d4: r0 = setState()
    //     0xb664d4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb664d8: r0 = Null
    //     0xb664d8: mov             x0, NULL
    // 0xb664dc: LeaveFrame
    //     0xb664dc: mov             SP, fp
    //     0xb664e0: ldp             fp, lr, [SP], #0x10
    // 0xb664e4: ret
    //     0xb664e4: ret             
    // 0xb664e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb664e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb664ec: b               #0xb6648c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb664f0, size: 0x44
    // 0xb664f0: ldr             x1, [SP]
    // 0xb664f4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb664f4: ldur            w2, [x1, #0x17]
    // 0xb664f8: DecompressPointer r2
    //     0xb664f8: add             x2, x2, HEAP, lsl #32
    // 0xb664fc: LoadField: r1 = r2->field_b
    //     0xb664fc: ldur            w1, [x2, #0xb]
    // 0xb66500: DecompressPointer r1
    //     0xb66500: add             x1, x1, HEAP, lsl #32
    // 0xb66504: LoadField: r3 = r1->field_b
    //     0xb66504: ldur            w3, [x1, #0xb]
    // 0xb66508: DecompressPointer r3
    //     0xb66508: add             x3, x3, HEAP, lsl #32
    // 0xb6650c: LoadField: r1 = r3->field_f
    //     0xb6650c: ldur            w1, [x3, #0xf]
    // 0xb66510: DecompressPointer r1
    //     0xb66510: add             x1, x1, HEAP, lsl #32
    // 0xb66514: LoadField: r3 = r2->field_f
    //     0xb66514: ldur            w3, [x2, #0xf]
    // 0xb66518: DecompressPointer r3
    //     0xb66518: add             x3, x3, HEAP, lsl #32
    // 0xb6651c: r2 = LoadInt32Instr(r3)
    //     0xb6651c: sbfx            x2, x3, #1, #0x1f
    //     0xb66520: tbz             w3, #0, #0xb66528
    //     0xb66524: ldur            x2, [x3, #7]
    // 0xb66528: StoreField: r1->field_1f = r2
    //     0xb66528: stur            x2, [x1, #0x1f]
    // 0xb6652c: r0 = Null
    //     0xb6652c: mov             x0, NULL
    // 0xb66530: ret
    //     0xb66530: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb66534, size: 0xc8
    // 0xb66534: EnterFrame
    //     0xb66534: stp             fp, lr, [SP, #-0x10]!
    //     0xb66538: mov             fp, SP
    // 0xb6653c: AllocStack(0x30)
    //     0xb6653c: sub             SP, SP, #0x30
    // 0xb66540: SetupParameters()
    //     0xb66540: ldr             x0, [fp, #0x10]
    //     0xb66544: ldur            w1, [x0, #0x17]
    //     0xb66548: add             x1, x1, HEAP, lsl #32
    // 0xb6654c: CheckStackOverflow
    //     0xb6654c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66550: cmp             SP, x16
    //     0xb66554: b.ls            #0xb665f0
    // 0xb66558: LoadField: r0 = r1->field_f
    //     0xb66558: ldur            w0, [x1, #0xf]
    // 0xb6655c: DecompressPointer r0
    //     0xb6655c: add             x0, x0, HEAP, lsl #32
    // 0xb66560: LoadField: r1 = r0->field_b
    //     0xb66560: ldur            w1, [x0, #0xb]
    // 0xb66564: DecompressPointer r1
    //     0xb66564: add             x1, x1, HEAP, lsl #32
    // 0xb66568: cmp             w1, NULL
    // 0xb6656c: b.eq            #0xb665f8
    // 0xb66570: LoadField: r0 = r1->field_2f
    //     0xb66570: ldur            w0, [x1, #0x2f]
    // 0xb66574: DecompressPointer r0
    //     0xb66574: add             x0, x0, HEAP, lsl #32
    // 0xb66578: LoadField: r2 = r1->field_2b
    //     0xb66578: ldur            w2, [x1, #0x2b]
    // 0xb6657c: DecompressPointer r2
    //     0xb6657c: add             x2, x2, HEAP, lsl #32
    // 0xb66580: LoadField: r3 = r1->field_37
    //     0xb66580: ldur            w3, [x1, #0x37]
    // 0xb66584: DecompressPointer r3
    //     0xb66584: add             x3, x3, HEAP, lsl #32
    // 0xb66588: LoadField: r4 = r1->field_33
    //     0xb66588: ldur            w4, [x1, #0x33]
    // 0xb6658c: DecompressPointer r4
    //     0xb6658c: add             x4, x4, HEAP, lsl #32
    // 0xb66590: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb66590: ldur            w5, [x1, #0x17]
    // 0xb66594: DecompressPointer r5
    //     0xb66594: add             x5, x5, HEAP, lsl #32
    // 0xb66598: cmp             w5, NULL
    // 0xb6659c: b.ne            #0xb665a8
    // 0xb665a0: r5 = Null
    //     0xb665a0: mov             x5, NULL
    // 0xb665a4: b               #0xb665b4
    // 0xb665a8: LoadField: r6 = r5->field_b
    //     0xb665a8: ldur            w6, [x5, #0xb]
    // 0xb665ac: DecompressPointer r6
    //     0xb665ac: add             x6, x6, HEAP, lsl #32
    // 0xb665b0: mov             x5, x6
    // 0xb665b4: LoadField: r6 = r1->field_4f
    //     0xb665b4: ldur            w6, [x1, #0x4f]
    // 0xb665b8: DecompressPointer r6
    //     0xb665b8: add             x6, x6, HEAP, lsl #32
    // 0xb665bc: stp             x0, x6, [SP, #0x20]
    // 0xb665c0: stp             x3, x2, [SP, #0x10]
    // 0xb665c4: stp             x5, x4, [SP]
    // 0xb665c8: r4 = 0
    //     0xb665c8: movz            x4, #0
    // 0xb665cc: ldr             x0, [SP, #0x28]
    // 0xb665d0: r16 = UnlinkedCall_0x613b5c
    //     0xb665d0: add             x16, PP, #0x56, lsl #12  ; [pp+0x56378] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb665d4: add             x16, x16, #0x378
    // 0xb665d8: ldp             x5, lr, [x16]
    // 0xb665dc: blr             lr
    // 0xb665e0: r0 = Null
    //     0xb665e0: mov             x0, NULL
    // 0xb665e4: LeaveFrame
    //     0xb665e4: mov             SP, fp
    //     0xb665e8: ldp             fp, lr, [SP], #0x10
    // 0xb665ec: ret
    //     0xb665ec: ret             
    // 0xb665f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb665f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb665f4: b               #0xb66558
    // 0xb665f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb665f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc879cc, size: 0x64
    // 0xc879cc: EnterFrame
    //     0xc879cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc879d0: mov             fp, SP
    // 0xc879d4: AllocStack(0x8)
    //     0xc879d4: sub             SP, SP, #8
    // 0xc879d8: SetupParameters(_ProductGridItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc879d8: mov             x0, x1
    //     0xc879dc: stur            x1, [fp, #-8]
    // 0xc879e0: CheckStackOverflow
    //     0xc879e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc879e4: cmp             SP, x16
    //     0xc879e8: b.ls            #0xc87a1c
    // 0xc879ec: LoadField: r1 = r0->field_1b
    //     0xc879ec: ldur            w1, [x0, #0x1b]
    // 0xc879f0: DecompressPointer r1
    //     0xc879f0: add             x1, x1, HEAP, lsl #32
    // 0xc879f4: r16 = Sentinel
    //     0xc879f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc879f8: cmp             w1, w16
    // 0xc879fc: b.eq            #0xc87a24
    // 0xc87a00: r0 = dispose()
    //     0xc87a00: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87a04: ldur            x1, [fp, #-8]
    // 0xc87a08: r0 = dispose()
    //     0xc87a08: bl              #0xc87a30  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_grid_item_view.dart] __ProductGridItemViewState&State&SingleTickerProviderStateMixin::dispose
    // 0xc87a0c: r0 = Null
    //     0xc87a0c: mov             x0, NULL
    // 0xc87a10: LeaveFrame
    //     0xc87a10: mov             SP, fp
    //     0xc87a14: ldp             fp, lr, [SP], #0x10
    // 0xc87a18: ret
    //     0xc87a18: ret             
    // 0xc87a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87a1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87a20: b               #0xc879ec
    // 0xc87a24: r9 = _pageController
    //     0xc87a24: add             x9, PP, #0x56, lsl #12  ; [pp+0x56300] Field <_ProductGridItemViewState@**********._pageController@**********>: late (offset: 0x1c)
    //     0xc87a28: ldr             x9, [x9, #0x300]
    // 0xc87a2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87a2c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4076, size: 0x5c, field offset: 0xc
//   const constructor, 
class ProductGridItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f328, size: 0x30
    // 0xc7f328: EnterFrame
    //     0xc7f328: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f32c: mov             fp, SP
    // 0xc7f330: mov             x0, x1
    // 0xc7f334: r1 = <ProductGridItemView>
    //     0xc7f334: add             x1, PP, #0x48, lsl #12  ; [pp+0x48878] TypeArguments: <ProductGridItemView>
    //     0xc7f338: ldr             x1, [x1, #0x878]
    // 0xc7f33c: r0 = _ProductGridItemViewState()
    //     0xc7f33c: bl              #0xc7f358  ; Allocate_ProductGridItemViewStateStub -> _ProductGridItemViewState (size=0x28)
    // 0xc7f340: r1 = Sentinel
    //     0xc7f340: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f344: StoreField: r0->field_1b = r1
    //     0xc7f344: stur            w1, [x0, #0x1b]
    // 0xc7f348: StoreField: r0->field_1f = rZR
    //     0xc7f348: stur            xzr, [x0, #0x1f]
    // 0xc7f34c: LeaveFrame
    //     0xc7f34c: mov             SP, fp
    //     0xc7f350: ldp             fp, lr, [SP], #0x10
    // 0xc7f354: ret
    //     0xc7f354: ret             
  }
}
