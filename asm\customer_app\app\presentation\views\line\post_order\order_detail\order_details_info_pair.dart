// lib: , url: package:customer_app/app/presentation/views/line/post_order/order_detail/order_details_info_pair.dart

// class id: 1049544, size: 0x8
class :: {
}

// class id: 4485, size: 0x10, field offset: 0xc
//   const constructor, 
class OrderDetailsInfoPair extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x1297bc8, size: 0x260
    // 0x1297bc8: EnterFrame
    //     0x1297bc8: stp             fp, lr, [SP, #-0x10]!
    //     0x1297bcc: mov             fp, SP
    // 0x1297bd0: AllocStack(0x40)
    //     0x1297bd0: sub             SP, SP, #0x40
    // 0x1297bd4: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x1297bd4: mov             x0, x2
    //     0x1297bd8: stur            x2, [fp, #-0x18]
    // 0x1297bdc: CheckStackOverflow
    //     0x1297bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1297be0: cmp             SP, x16
    //     0x1297be4: b.ls            #0x1297e20
    // 0x1297be8: LoadField: r2 = r1->field_b
    //     0x1297be8: ldur            w2, [x1, #0xb]
    // 0x1297bec: DecompressPointer r2
    //     0x1297bec: add             x2, x2, HEAP, lsl #32
    // 0x1297bf0: stur            x2, [fp, #-0x10]
    // 0x1297bf4: LoadField: r1 = r2->field_7
    //     0x1297bf4: ldur            w1, [x2, #7]
    // 0x1297bf8: DecompressPointer r1
    //     0x1297bf8: add             x1, x1, HEAP, lsl #32
    // 0x1297bfc: cmp             w1, NULL
    // 0x1297c00: b.ne            #0x1297c0c
    // 0x1297c04: r3 = ""
    //     0x1297c04: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1297c08: b               #0x1297c10
    // 0x1297c0c: mov             x3, x1
    // 0x1297c10: mov             x1, x0
    // 0x1297c14: stur            x3, [fp, #-8]
    // 0x1297c18: r0 = of()
    //     0x1297c18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1297c1c: LoadField: r1 = r0->field_87
    //     0x1297c1c: ldur            w1, [x0, #0x87]
    // 0x1297c20: DecompressPointer r1
    //     0x1297c20: add             x1, x1, HEAP, lsl #32
    // 0x1297c24: LoadField: r0 = r1->field_7
    //     0x1297c24: ldur            w0, [x1, #7]
    // 0x1297c28: DecompressPointer r0
    //     0x1297c28: add             x0, x0, HEAP, lsl #32
    // 0x1297c2c: stur            x0, [fp, #-0x20]
    // 0x1297c30: r1 = Instance_Color
    //     0x1297c30: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1297c34: d0 = 0.700000
    //     0x1297c34: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1297c38: ldr             d0, [x17, #0xf48]
    // 0x1297c3c: r0 = withOpacity()
    //     0x1297c3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1297c40: r16 = 14.000000
    //     0x1297c40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1297c44: ldr             x16, [x16, #0x1d8]
    // 0x1297c48: stp             x0, x16, [SP]
    // 0x1297c4c: ldur            x1, [fp, #-0x20]
    // 0x1297c50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1297c50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1297c54: ldr             x4, [x4, #0xaa0]
    // 0x1297c58: r0 = copyWith()
    //     0x1297c58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1297c5c: stur            x0, [fp, #-0x20]
    // 0x1297c60: r0 = Text()
    //     0x1297c60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1297c64: mov             x1, x0
    // 0x1297c68: ldur            x0, [fp, #-8]
    // 0x1297c6c: stur            x1, [fp, #-0x28]
    // 0x1297c70: StoreField: r1->field_b = r0
    //     0x1297c70: stur            w0, [x1, #0xb]
    // 0x1297c74: ldur            x0, [fp, #-0x20]
    // 0x1297c78: StoreField: r1->field_13 = r0
    //     0x1297c78: stur            w0, [x1, #0x13]
    // 0x1297c7c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1297c7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1297c80: ldr             x0, [x0, #0x1c80]
    //     0x1297c84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1297c88: cmp             w0, w16
    //     0x1297c8c: b.ne            #0x1297c98
    //     0x1297c90: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1297c94: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1297c98: r0 = GetNavigation.size()
    //     0x1297c98: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1297c9c: LoadField: d0 = r0->field_7
    //     0x1297c9c: ldur            d0, [x0, #7]
    // 0x1297ca0: d1 = 0.900000
    //     0x1297ca0: ldr             d1, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0x1297ca4: fmul            d2, d0, d1
    // 0x1297ca8: stur            d2, [fp, #-0x30]
    // 0x1297cac: r0 = BoxConstraints()
    //     0x1297cac: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1297cb0: stur            x0, [fp, #-0x20]
    // 0x1297cb4: StoreField: r0->field_7 = rZR
    //     0x1297cb4: stur            xzr, [x0, #7]
    // 0x1297cb8: ldur            d0, [fp, #-0x30]
    // 0x1297cbc: StoreField: r0->field_f = d0
    //     0x1297cbc: stur            d0, [x0, #0xf]
    // 0x1297cc0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1297cc0: stur            xzr, [x0, #0x17]
    // 0x1297cc4: d0 = inf
    //     0x1297cc4: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1297cc8: StoreField: r0->field_1f = d0
    //     0x1297cc8: stur            d0, [x0, #0x1f]
    // 0x1297ccc: ldur            x1, [fp, #-0x10]
    // 0x1297cd0: LoadField: r2 = r1->field_b
    //     0x1297cd0: ldur            w2, [x1, #0xb]
    // 0x1297cd4: DecompressPointer r2
    //     0x1297cd4: add             x2, x2, HEAP, lsl #32
    // 0x1297cd8: cmp             w2, NULL
    // 0x1297cdc: b.ne            #0x1297ce8
    // 0x1297ce0: r3 = ""
    //     0x1297ce0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1297ce4: b               #0x1297cec
    // 0x1297ce8: mov             x3, x2
    // 0x1297cec: ldur            x2, [fp, #-0x28]
    // 0x1297cf0: ldur            x1, [fp, #-0x18]
    // 0x1297cf4: stur            x3, [fp, #-8]
    // 0x1297cf8: r0 = of()
    //     0x1297cf8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1297cfc: LoadField: r1 = r0->field_87
    //     0x1297cfc: ldur            w1, [x0, #0x87]
    // 0x1297d00: DecompressPointer r1
    //     0x1297d00: add             x1, x1, HEAP, lsl #32
    // 0x1297d04: LoadField: r0 = r1->field_2b
    //     0x1297d04: ldur            w0, [x1, #0x2b]
    // 0x1297d08: DecompressPointer r0
    //     0x1297d08: add             x0, x0, HEAP, lsl #32
    // 0x1297d0c: stur            x0, [fp, #-0x10]
    // 0x1297d10: r1 = Instance_Color
    //     0x1297d10: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1297d14: d0 = 0.400000
    //     0x1297d14: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1297d18: r0 = withOpacity()
    //     0x1297d18: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1297d1c: r16 = 12.000000
    //     0x1297d1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1297d20: ldr             x16, [x16, #0x9e8]
    // 0x1297d24: stp             x0, x16, [SP]
    // 0x1297d28: ldur            x1, [fp, #-0x10]
    // 0x1297d2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1297d2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1297d30: ldr             x4, [x4, #0xaa0]
    // 0x1297d34: r0 = copyWith()
    //     0x1297d34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1297d38: stur            x0, [fp, #-0x10]
    // 0x1297d3c: r0 = Text()
    //     0x1297d3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1297d40: mov             x1, x0
    // 0x1297d44: ldur            x0, [fp, #-8]
    // 0x1297d48: stur            x1, [fp, #-0x18]
    // 0x1297d4c: StoreField: r1->field_b = r0
    //     0x1297d4c: stur            w0, [x1, #0xb]
    // 0x1297d50: ldur            x0, [fp, #-0x10]
    // 0x1297d54: StoreField: r1->field_13 = r0
    //     0x1297d54: stur            w0, [x1, #0x13]
    // 0x1297d58: r0 = ConstrainedBox()
    //     0x1297d58: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1297d5c: mov             x3, x0
    // 0x1297d60: ldur            x0, [fp, #-0x20]
    // 0x1297d64: stur            x3, [fp, #-8]
    // 0x1297d68: StoreField: r3->field_f = r0
    //     0x1297d68: stur            w0, [x3, #0xf]
    // 0x1297d6c: ldur            x0, [fp, #-0x18]
    // 0x1297d70: StoreField: r3->field_b = r0
    //     0x1297d70: stur            w0, [x3, #0xb]
    // 0x1297d74: r1 = Null
    //     0x1297d74: mov             x1, NULL
    // 0x1297d78: r2 = 6
    //     0x1297d78: movz            x2, #0x6
    // 0x1297d7c: r0 = AllocateArray()
    //     0x1297d7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1297d80: mov             x2, x0
    // 0x1297d84: ldur            x0, [fp, #-0x28]
    // 0x1297d88: stur            x2, [fp, #-0x10]
    // 0x1297d8c: StoreField: r2->field_f = r0
    //     0x1297d8c: stur            w0, [x2, #0xf]
    // 0x1297d90: ldur            x0, [fp, #-8]
    // 0x1297d94: StoreField: r2->field_13 = r0
    //     0x1297d94: stur            w0, [x2, #0x13]
    // 0x1297d98: r16 = Instance_SizedBox
    //     0x1297d98: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x1297d9c: ldr             x16, [x16, #0x8b8]
    // 0x1297da0: ArrayStore: r2[0] = r16  ; List_4
    //     0x1297da0: stur            w16, [x2, #0x17]
    // 0x1297da4: r1 = <Widget>
    //     0x1297da4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1297da8: r0 = AllocateGrowableArray()
    //     0x1297da8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1297dac: mov             x1, x0
    // 0x1297db0: ldur            x0, [fp, #-0x10]
    // 0x1297db4: stur            x1, [fp, #-8]
    // 0x1297db8: StoreField: r1->field_f = r0
    //     0x1297db8: stur            w0, [x1, #0xf]
    // 0x1297dbc: r0 = 6
    //     0x1297dbc: movz            x0, #0x6
    // 0x1297dc0: StoreField: r1->field_b = r0
    //     0x1297dc0: stur            w0, [x1, #0xb]
    // 0x1297dc4: r0 = Wrap()
    //     0x1297dc4: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x1297dc8: r1 = Instance_Axis
    //     0x1297dc8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1297dcc: StoreField: r0->field_f = r1
    //     0x1297dcc: stur            w1, [x0, #0xf]
    // 0x1297dd0: r1 = Instance_WrapAlignment
    //     0x1297dd0: add             x1, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x1297dd4: ldr             x1, [x1, #0x6e8]
    // 0x1297dd8: StoreField: r0->field_13 = r1
    //     0x1297dd8: stur            w1, [x0, #0x13]
    // 0x1297ddc: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1297ddc: stur            xzr, [x0, #0x17]
    // 0x1297de0: StoreField: r0->field_1f = r1
    //     0x1297de0: stur            w1, [x0, #0x1f]
    // 0x1297de4: StoreField: r0->field_23 = rZR
    //     0x1297de4: stur            xzr, [x0, #0x23]
    // 0x1297de8: r1 = Instance_WrapCrossAlignment
    //     0x1297de8: add             x1, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x1297dec: ldr             x1, [x1, #0x6f0]
    // 0x1297df0: StoreField: r0->field_2b = r1
    //     0x1297df0: stur            w1, [x0, #0x2b]
    // 0x1297df4: r1 = Instance_VerticalDirection
    //     0x1297df4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1297df8: ldr             x1, [x1, #0xa20]
    // 0x1297dfc: StoreField: r0->field_33 = r1
    //     0x1297dfc: stur            w1, [x0, #0x33]
    // 0x1297e00: r1 = Instance_Clip
    //     0x1297e00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1297e04: ldr             x1, [x1, #0x38]
    // 0x1297e08: StoreField: r0->field_37 = r1
    //     0x1297e08: stur            w1, [x0, #0x37]
    // 0x1297e0c: ldur            x1, [fp, #-8]
    // 0x1297e10: StoreField: r0->field_b = r1
    //     0x1297e10: stur            w1, [x0, #0xb]
    // 0x1297e14: LeaveFrame
    //     0x1297e14: mov             SP, fp
    //     0x1297e18: ldp             fp, lr, [SP], #0x10
    // 0x1297e1c: ret
    //     0x1297e1c: ret             
    // 0x1297e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1297e20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1297e24: b               #0x1297be8
  }
}
