// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/know_more_bottom_sheet.dart

// class id: 1049310, size: 0x8
class :: {
}

// class id: 3408, size: 0x14, field offset: 0x14
class _KnowMoreBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb0454c, size: 0x115c
    // 0xb0454c: EnterFrame
    //     0xb0454c: stp             fp, lr, [SP, #-0x10]!
    //     0xb04550: mov             fp, SP
    // 0xb04554: AllocStack(0x78)
    //     0xb04554: sub             SP, SP, #0x78
    // 0xb04558: SetupParameters(_KnowMoreBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb04558: mov             x0, x1
    //     0xb0455c: stur            x1, [fp, #-8]
    //     0xb04560: mov             x1, x2
    //     0xb04564: stur            x2, [fp, #-0x10]
    // 0xb04568: CheckStackOverflow
    //     0xb04568: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb0456c: cmp             SP, x16
    //     0xb04570: b.ls            #0xb05650
    // 0xb04574: r1 = 1
    //     0xb04574: movz            x1, #0x1
    // 0xb04578: r0 = AllocateContext()
    //     0xb04578: bl              #0x16f6108  ; AllocateContextStub
    // 0xb0457c: mov             x2, x0
    // 0xb04580: ldur            x0, [fp, #-8]
    // 0xb04584: stur            x2, [fp, #-0x18]
    // 0xb04588: StoreField: r2->field_f = r0
    //     0xb04588: stur            w0, [x2, #0xf]
    // 0xb0458c: ldur            x1, [fp, #-0x10]
    // 0xb04590: r0 = of()
    //     0xb04590: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb04594: LoadField: r1 = r0->field_5b
    //     0xb04594: ldur            w1, [x0, #0x5b]
    // 0xb04598: DecompressPointer r1
    //     0xb04598: add             x1, x1, HEAP, lsl #32
    // 0xb0459c: stur            x1, [fp, #-0x20]
    // 0xb045a0: r0 = BoxDecoration()
    //     0xb045a0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb045a4: mov             x1, x0
    // 0xb045a8: ldur            x0, [fp, #-0x20]
    // 0xb045ac: stur            x1, [fp, #-0x28]
    // 0xb045b0: StoreField: r1->field_7 = r0
    //     0xb045b0: stur            w0, [x1, #7]
    // 0xb045b4: r0 = Instance_BoxShape
    //     0xb045b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb045b8: ldr             x0, [x0, #0x970]
    // 0xb045bc: StoreField: r1->field_23 = r0
    //     0xb045bc: stur            w0, [x1, #0x23]
    // 0xb045c0: r0 = SvgPicture()
    //     0xb045c0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb045c4: stur            x0, [fp, #-0x20]
    // 0xb045c8: r16 = Instance_BoxFit
    //     0xb045c8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb045cc: ldr             x16, [x16, #0xb18]
    // 0xb045d0: str             x16, [SP]
    // 0xb045d4: mov             x1, x0
    // 0xb045d8: r2 = "assets/images/return_order.svg"
    //     0xb045d8: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c80] "assets/images/return_order.svg"
    //     0xb045dc: ldr             x2, [x2, #0xc80]
    // 0xb045e0: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb045e0: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb045e4: ldr             x4, [x4, #0xb0]
    // 0xb045e8: r0 = SvgPicture.asset()
    //     0xb045e8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb045ec: r0 = Container()
    //     0xb045ec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb045f0: stur            x0, [fp, #-0x30]
    // 0xb045f4: r16 = 45.000000
    //     0xb045f4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb045f8: ldr             x16, [x16, #0xc88]
    // 0xb045fc: r30 = 45.000000
    //     0xb045fc: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb04600: ldr             lr, [lr, #0xc88]
    // 0xb04604: stp             lr, x16, [SP, #0x10]
    // 0xb04608: ldur            x16, [fp, #-0x28]
    // 0xb0460c: ldur            lr, [fp, #-0x20]
    // 0xb04610: stp             lr, x16, [SP]
    // 0xb04614: mov             x1, x0
    // 0xb04618: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb04618: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb0461c: ldr             x4, [x4, #0x870]
    // 0xb04620: r0 = Container()
    //     0xb04620: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb04624: ldur            x0, [fp, #-8]
    // 0xb04628: LoadField: r1 = r0->field_b
    //     0xb04628: ldur            w1, [x0, #0xb]
    // 0xb0462c: DecompressPointer r1
    //     0xb0462c: add             x1, x1, HEAP, lsl #32
    // 0xb04630: cmp             w1, NULL
    // 0xb04634: b.eq            #0xb05658
    // 0xb04638: LoadField: r2 = r1->field_b
    //     0xb04638: ldur            w2, [x1, #0xb]
    // 0xb0463c: DecompressPointer r2
    //     0xb0463c: add             x2, x2, HEAP, lsl #32
    // 0xb04640: LoadField: r1 = r2->field_b
    //     0xb04640: ldur            w1, [x2, #0xb]
    // 0xb04644: DecompressPointer r1
    //     0xb04644: add             x1, x1, HEAP, lsl #32
    // 0xb04648: cmp             w1, NULL
    // 0xb0464c: b.ne            #0xb04658
    // 0xb04650: r1 = Null
    //     0xb04650: mov             x1, NULL
    // 0xb04654: b               #0xb04674
    // 0xb04658: LoadField: r2 = r1->field_7
    //     0xb04658: ldur            w2, [x1, #7]
    // 0xb0465c: DecompressPointer r2
    //     0xb0465c: add             x2, x2, HEAP, lsl #32
    // 0xb04660: LoadField: r1 = r2->field_7
    //     0xb04660: ldur            w1, [x2, #7]
    // 0xb04664: DecompressPointer r1
    //     0xb04664: add             x1, x1, HEAP, lsl #32
    // 0xb04668: LoadField: r2 = r1->field_7
    //     0xb04668: ldur            w2, [x1, #7]
    // 0xb0466c: DecompressPointer r2
    //     0xb0466c: add             x2, x2, HEAP, lsl #32
    // 0xb04670: mov             x1, x2
    // 0xb04674: cmp             w1, NULL
    // 0xb04678: b.ne            #0xb04680
    // 0xb0467c: r1 = false
    //     0xb0467c: add             x1, NULL, #0x30  ; false
    // 0xb04680: stur            x1, [fp, #-0x20]
    // 0xb04684: r0 = SvgPicture()
    //     0xb04684: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb04688: stur            x0, [fp, #-0x28]
    // 0xb0468c: r16 = Instance_BoxFit
    //     0xb0468c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb04690: ldr             x16, [x16, #0xb18]
    // 0xb04694: str             x16, [SP]
    // 0xb04698: mov             x1, x0
    // 0xb0469c: r2 = "assets/images/replacement_check.svg"
    //     0xb0469c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xb046a0: ldr             x2, [x2, #0xc90]
    // 0xb046a4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb046a4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb046a8: ldr             x4, [x4, #0xb0]
    // 0xb046ac: r0 = SvgPicture.asset()
    //     0xb046ac: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb046b0: r0 = Visibility()
    //     0xb046b0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb046b4: mov             x1, x0
    // 0xb046b8: ldur            x0, [fp, #-0x28]
    // 0xb046bc: stur            x1, [fp, #-0x38]
    // 0xb046c0: StoreField: r1->field_b = r0
    //     0xb046c0: stur            w0, [x1, #0xb]
    // 0xb046c4: r0 = Instance_SizedBox
    //     0xb046c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb046c8: StoreField: r1->field_f = r0
    //     0xb046c8: stur            w0, [x1, #0xf]
    // 0xb046cc: ldur            x2, [fp, #-0x20]
    // 0xb046d0: StoreField: r1->field_13 = r2
    //     0xb046d0: stur            w2, [x1, #0x13]
    // 0xb046d4: r2 = false
    //     0xb046d4: add             x2, NULL, #0x30  ; false
    // 0xb046d8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb046d8: stur            w2, [x1, #0x17]
    // 0xb046dc: StoreField: r1->field_1b = r2
    //     0xb046dc: stur            w2, [x1, #0x1b]
    // 0xb046e0: StoreField: r1->field_1f = r2
    //     0xb046e0: stur            w2, [x1, #0x1f]
    // 0xb046e4: StoreField: r1->field_23 = r2
    //     0xb046e4: stur            w2, [x1, #0x23]
    // 0xb046e8: StoreField: r1->field_27 = r2
    //     0xb046e8: stur            w2, [x1, #0x27]
    // 0xb046ec: StoreField: r1->field_2b = r2
    //     0xb046ec: stur            w2, [x1, #0x2b]
    // 0xb046f0: ldur            x3, [fp, #-8]
    // 0xb046f4: LoadField: r4 = r3->field_b
    //     0xb046f4: ldur            w4, [x3, #0xb]
    // 0xb046f8: DecompressPointer r4
    //     0xb046f8: add             x4, x4, HEAP, lsl #32
    // 0xb046fc: cmp             w4, NULL
    // 0xb04700: b.eq            #0xb0565c
    // 0xb04704: LoadField: r5 = r4->field_b
    //     0xb04704: ldur            w5, [x4, #0xb]
    // 0xb04708: DecompressPointer r5
    //     0xb04708: add             x5, x5, HEAP, lsl #32
    // 0xb0470c: LoadField: r4 = r5->field_b
    //     0xb0470c: ldur            w4, [x5, #0xb]
    // 0xb04710: DecompressPointer r4
    //     0xb04710: add             x4, x4, HEAP, lsl #32
    // 0xb04714: cmp             w4, NULL
    // 0xb04718: b.ne            #0xb04724
    // 0xb0471c: r4 = Null
    //     0xb0471c: mov             x4, NULL
    // 0xb04720: b               #0xb04740
    // 0xb04724: LoadField: r5 = r4->field_7
    //     0xb04724: ldur            w5, [x4, #7]
    // 0xb04728: DecompressPointer r5
    //     0xb04728: add             x5, x5, HEAP, lsl #32
    // 0xb0472c: LoadField: r4 = r5->field_7
    //     0xb0472c: ldur            w4, [x5, #7]
    // 0xb04730: DecompressPointer r4
    //     0xb04730: add             x4, x4, HEAP, lsl #32
    // 0xb04734: LoadField: r5 = r4->field_7
    //     0xb04734: ldur            w5, [x4, #7]
    // 0xb04738: DecompressPointer r5
    //     0xb04738: add             x5, x5, HEAP, lsl #32
    // 0xb0473c: mov             x4, x5
    // 0xb04740: cmp             w4, NULL
    // 0xb04744: b.ne            #0xb04750
    // 0xb04748: r5 = false
    //     0xb04748: add             x5, NULL, #0x30  ; false
    // 0xb0474c: b               #0xb04754
    // 0xb04750: mov             x5, x4
    // 0xb04754: ldur            x4, [fp, #-0x30]
    // 0xb04758: eor             x6, x5, #0x10
    // 0xb0475c: stur            x6, [fp, #-0x20]
    // 0xb04760: r0 = SvgPicture()
    //     0xb04760: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb04764: stur            x0, [fp, #-0x28]
    // 0xb04768: r16 = Instance_BoxFit
    //     0xb04768: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb0476c: ldr             x16, [x16, #0xb18]
    // 0xb04770: str             x16, [SP]
    // 0xb04774: mov             x1, x0
    // 0xb04778: r2 = "assets/images/exchange_check.svg"
    //     0xb04778: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xb0477c: ldr             x2, [x2, #0xc98]
    // 0xb04780: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb04780: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb04784: ldr             x4, [x4, #0xb0]
    // 0xb04788: r0 = SvgPicture.asset()
    //     0xb04788: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb0478c: r0 = Visibility()
    //     0xb0478c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb04790: mov             x3, x0
    // 0xb04794: ldur            x0, [fp, #-0x28]
    // 0xb04798: stur            x3, [fp, #-0x40]
    // 0xb0479c: StoreField: r3->field_b = r0
    //     0xb0479c: stur            w0, [x3, #0xb]
    // 0xb047a0: r0 = Instance_SizedBox
    //     0xb047a0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb047a4: StoreField: r3->field_f = r0
    //     0xb047a4: stur            w0, [x3, #0xf]
    // 0xb047a8: ldur            x1, [fp, #-0x20]
    // 0xb047ac: StoreField: r3->field_13 = r1
    //     0xb047ac: stur            w1, [x3, #0x13]
    // 0xb047b0: r4 = false
    //     0xb047b0: add             x4, NULL, #0x30  ; false
    // 0xb047b4: ArrayStore: r3[0] = r4  ; List_4
    //     0xb047b4: stur            w4, [x3, #0x17]
    // 0xb047b8: StoreField: r3->field_1b = r4
    //     0xb047b8: stur            w4, [x3, #0x1b]
    // 0xb047bc: StoreField: r3->field_1f = r4
    //     0xb047bc: stur            w4, [x3, #0x1f]
    // 0xb047c0: StoreField: r3->field_23 = r4
    //     0xb047c0: stur            w4, [x3, #0x23]
    // 0xb047c4: StoreField: r3->field_27 = r4
    //     0xb047c4: stur            w4, [x3, #0x27]
    // 0xb047c8: StoreField: r3->field_2b = r4
    //     0xb047c8: stur            w4, [x3, #0x2b]
    // 0xb047cc: r1 = Null
    //     0xb047cc: mov             x1, NULL
    // 0xb047d0: r2 = 6
    //     0xb047d0: movz            x2, #0x6
    // 0xb047d4: r0 = AllocateArray()
    //     0xb047d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb047d8: mov             x2, x0
    // 0xb047dc: ldur            x0, [fp, #-0x30]
    // 0xb047e0: stur            x2, [fp, #-0x20]
    // 0xb047e4: StoreField: r2->field_f = r0
    //     0xb047e4: stur            w0, [x2, #0xf]
    // 0xb047e8: ldur            x0, [fp, #-0x38]
    // 0xb047ec: StoreField: r2->field_13 = r0
    //     0xb047ec: stur            w0, [x2, #0x13]
    // 0xb047f0: ldur            x0, [fp, #-0x40]
    // 0xb047f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb047f4: stur            w0, [x2, #0x17]
    // 0xb047f8: r1 = <Widget>
    //     0xb047f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb047fc: r0 = AllocateGrowableArray()
    //     0xb047fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb04800: mov             x1, x0
    // 0xb04804: ldur            x0, [fp, #-0x20]
    // 0xb04808: stur            x1, [fp, #-0x28]
    // 0xb0480c: StoreField: r1->field_f = r0
    //     0xb0480c: stur            w0, [x1, #0xf]
    // 0xb04810: r2 = 6
    //     0xb04810: movz            x2, #0x6
    // 0xb04814: StoreField: r1->field_b = r2
    //     0xb04814: stur            w2, [x1, #0xb]
    // 0xb04818: r0 = Stack()
    //     0xb04818: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb0481c: mov             x1, x0
    // 0xb04820: r0 = Instance_Alignment
    //     0xb04820: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb04824: ldr             x0, [x0, #0x950]
    // 0xb04828: stur            x1, [fp, #-0x20]
    // 0xb0482c: StoreField: r1->field_f = r0
    //     0xb0482c: stur            w0, [x1, #0xf]
    // 0xb04830: r2 = Instance_StackFit
    //     0xb04830: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb04834: ldr             x2, [x2, #0xfa8]
    // 0xb04838: ArrayStore: r1[0] = r2  ; List_4
    //     0xb04838: stur            w2, [x1, #0x17]
    // 0xb0483c: r3 = Instance_Clip
    //     0xb0483c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb04840: ldr             x3, [x3, #0x7e0]
    // 0xb04844: StoreField: r1->field_1b = r3
    //     0xb04844: stur            w3, [x1, #0x1b]
    // 0xb04848: ldur            x4, [fp, #-0x28]
    // 0xb0484c: StoreField: r1->field_b = r4
    //     0xb0484c: stur            w4, [x1, #0xb]
    // 0xb04850: ldur            x4, [fp, #-8]
    // 0xb04854: LoadField: r5 = r4->field_b
    //     0xb04854: ldur            w5, [x4, #0xb]
    // 0xb04858: DecompressPointer r5
    //     0xb04858: add             x5, x5, HEAP, lsl #32
    // 0xb0485c: cmp             w5, NULL
    // 0xb04860: b.eq            #0xb05660
    // 0xb04864: LoadField: r6 = r5->field_b
    //     0xb04864: ldur            w6, [x5, #0xb]
    // 0xb04868: DecompressPointer r6
    //     0xb04868: add             x6, x6, HEAP, lsl #32
    // 0xb0486c: LoadField: r5 = r6->field_b
    //     0xb0486c: ldur            w5, [x6, #0xb]
    // 0xb04870: DecompressPointer r5
    //     0xb04870: add             x5, x5, HEAP, lsl #32
    // 0xb04874: cmp             w5, NULL
    // 0xb04878: b.ne            #0xb04884
    // 0xb0487c: r5 = Null
    //     0xb0487c: mov             x5, NULL
    // 0xb04880: b               #0xb048a0
    // 0xb04884: LoadField: r6 = r5->field_7
    //     0xb04884: ldur            w6, [x5, #7]
    // 0xb04888: DecompressPointer r6
    //     0xb04888: add             x6, x6, HEAP, lsl #32
    // 0xb0488c: LoadField: r5 = r6->field_7
    //     0xb0488c: ldur            w5, [x6, #7]
    // 0xb04890: DecompressPointer r5
    //     0xb04890: add             x5, x5, HEAP, lsl #32
    // 0xb04894: LoadField: r6 = r5->field_b
    //     0xb04894: ldur            w6, [x5, #0xb]
    // 0xb04898: DecompressPointer r6
    //     0xb04898: add             x6, x6, HEAP, lsl #32
    // 0xb0489c: mov             x5, x6
    // 0xb048a0: str             x5, [SP]
    // 0xb048a4: r0 = _interpolateSingle()
    //     0xb048a4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb048a8: ldur            x1, [fp, #-0x10]
    // 0xb048ac: stur            x0, [fp, #-0x28]
    // 0xb048b0: r0 = of()
    //     0xb048b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb048b4: LoadField: r1 = r0->field_87
    //     0xb048b4: ldur            w1, [x0, #0x87]
    // 0xb048b8: DecompressPointer r1
    //     0xb048b8: add             x1, x1, HEAP, lsl #32
    // 0xb048bc: LoadField: r0 = r1->field_2b
    //     0xb048bc: ldur            w0, [x1, #0x2b]
    // 0xb048c0: DecompressPointer r0
    //     0xb048c0: add             x0, x0, HEAP, lsl #32
    // 0xb048c4: r16 = 12.000000
    //     0xb048c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb048c8: ldr             x16, [x16, #0x9e8]
    // 0xb048cc: r30 = Instance_Color
    //     0xb048cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb048d0: stp             lr, x16, [SP]
    // 0xb048d4: mov             x1, x0
    // 0xb048d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb048d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb048dc: ldr             x4, [x4, #0xaa0]
    // 0xb048e0: r0 = copyWith()
    //     0xb048e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb048e4: stur            x0, [fp, #-0x30]
    // 0xb048e8: r0 = Text()
    //     0xb048e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb048ec: mov             x1, x0
    // 0xb048f0: ldur            x0, [fp, #-0x28]
    // 0xb048f4: stur            x1, [fp, #-0x38]
    // 0xb048f8: StoreField: r1->field_b = r0
    //     0xb048f8: stur            w0, [x1, #0xb]
    // 0xb048fc: ldur            x0, [fp, #-0x30]
    // 0xb04900: StoreField: r1->field_13 = r0
    //     0xb04900: stur            w0, [x1, #0x13]
    // 0xb04904: r0 = Padding()
    //     0xb04904: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb04908: mov             x3, x0
    // 0xb0490c: r0 = Instance_EdgeInsets
    //     0xb0490c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb04910: ldr             x0, [x0, #0x770]
    // 0xb04914: stur            x3, [fp, #-0x28]
    // 0xb04918: StoreField: r3->field_f = r0
    //     0xb04918: stur            w0, [x3, #0xf]
    // 0xb0491c: ldur            x1, [fp, #-0x38]
    // 0xb04920: StoreField: r3->field_b = r1
    //     0xb04920: stur            w1, [x3, #0xb]
    // 0xb04924: r1 = Null
    //     0xb04924: mov             x1, NULL
    // 0xb04928: r2 = 4
    //     0xb04928: movz            x2, #0x4
    // 0xb0492c: r0 = AllocateArray()
    //     0xb0492c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb04930: mov             x2, x0
    // 0xb04934: ldur            x0, [fp, #-0x20]
    // 0xb04938: stur            x2, [fp, #-0x30]
    // 0xb0493c: StoreField: r2->field_f = r0
    //     0xb0493c: stur            w0, [x2, #0xf]
    // 0xb04940: ldur            x0, [fp, #-0x28]
    // 0xb04944: StoreField: r2->field_13 = r0
    //     0xb04944: stur            w0, [x2, #0x13]
    // 0xb04948: r1 = <Widget>
    //     0xb04948: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb0494c: r0 = AllocateGrowableArray()
    //     0xb0494c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb04950: mov             x1, x0
    // 0xb04954: ldur            x0, [fp, #-0x30]
    // 0xb04958: stur            x1, [fp, #-0x20]
    // 0xb0495c: StoreField: r1->field_f = r0
    //     0xb0495c: stur            w0, [x1, #0xf]
    // 0xb04960: r2 = 4
    //     0xb04960: movz            x2, #0x4
    // 0xb04964: StoreField: r1->field_b = r2
    //     0xb04964: stur            w2, [x1, #0xb]
    // 0xb04968: r0 = Column()
    //     0xb04968: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb0496c: mov             x2, x0
    // 0xb04970: r0 = Instance_Axis
    //     0xb04970: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb04974: stur            x2, [fp, #-0x28]
    // 0xb04978: StoreField: r2->field_f = r0
    //     0xb04978: stur            w0, [x2, #0xf]
    // 0xb0497c: r3 = Instance_MainAxisAlignment
    //     0xb0497c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb04980: ldr             x3, [x3, #0xa08]
    // 0xb04984: StoreField: r2->field_13 = r3
    //     0xb04984: stur            w3, [x2, #0x13]
    // 0xb04988: r4 = Instance_MainAxisSize
    //     0xb04988: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0498c: ldr             x4, [x4, #0xa10]
    // 0xb04990: ArrayStore: r2[0] = r4  ; List_4
    //     0xb04990: stur            w4, [x2, #0x17]
    // 0xb04994: r5 = Instance_CrossAxisAlignment
    //     0xb04994: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb04998: ldr             x5, [x5, #0xa18]
    // 0xb0499c: StoreField: r2->field_1b = r5
    //     0xb0499c: stur            w5, [x2, #0x1b]
    // 0xb049a0: r6 = Instance_VerticalDirection
    //     0xb049a0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb049a4: ldr             x6, [x6, #0xa20]
    // 0xb049a8: StoreField: r2->field_23 = r6
    //     0xb049a8: stur            w6, [x2, #0x23]
    // 0xb049ac: r7 = Instance_Clip
    //     0xb049ac: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb049b0: ldr             x7, [x7, #0x38]
    // 0xb049b4: StoreField: r2->field_2b = r7
    //     0xb049b4: stur            w7, [x2, #0x2b]
    // 0xb049b8: StoreField: r2->field_2f = rZR
    //     0xb049b8: stur            xzr, [x2, #0x2f]
    // 0xb049bc: ldur            x1, [fp, #-0x20]
    // 0xb049c0: StoreField: r2->field_b = r1
    //     0xb049c0: stur            w1, [x2, #0xb]
    // 0xb049c4: ldur            x1, [fp, #-0x10]
    // 0xb049c8: r0 = of()
    //     0xb049c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb049cc: LoadField: r1 = r0->field_5b
    //     0xb049cc: ldur            w1, [x0, #0x5b]
    // 0xb049d0: DecompressPointer r1
    //     0xb049d0: add             x1, x1, HEAP, lsl #32
    // 0xb049d4: stur            x1, [fp, #-0x20]
    // 0xb049d8: r0 = BoxDecoration()
    //     0xb049d8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb049dc: mov             x1, x0
    // 0xb049e0: ldur            x0, [fp, #-0x20]
    // 0xb049e4: stur            x1, [fp, #-0x30]
    // 0xb049e8: StoreField: r1->field_7 = r0
    //     0xb049e8: stur            w0, [x1, #7]
    // 0xb049ec: r0 = Instance_BoxShape
    //     0xb049ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb049f0: ldr             x0, [x0, #0x970]
    // 0xb049f4: StoreField: r1->field_23 = r0
    //     0xb049f4: stur            w0, [x1, #0x23]
    // 0xb049f8: r0 = SvgPicture()
    //     0xb049f8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb049fc: stur            x0, [fp, #-0x20]
    // 0xb04a00: r16 = Instance_BoxFit
    //     0xb04a00: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb04a04: ldr             x16, [x16, #0xb18]
    // 0xb04a08: str             x16, [SP]
    // 0xb04a0c: mov             x1, x0
    // 0xb04a10: r2 = "assets/images/exchange.svg"
    //     0xb04a10: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xb04a14: ldr             x2, [x2, #0xca0]
    // 0xb04a18: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb04a18: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb04a1c: ldr             x4, [x4, #0xb0]
    // 0xb04a20: r0 = SvgPicture.asset()
    //     0xb04a20: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb04a24: r0 = Container()
    //     0xb04a24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb04a28: stur            x0, [fp, #-0x38]
    // 0xb04a2c: r16 = 45.000000
    //     0xb04a2c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb04a30: ldr             x16, [x16, #0xc88]
    // 0xb04a34: r30 = 45.000000
    //     0xb04a34: add             lr, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xb04a38: ldr             lr, [lr, #0xc88]
    // 0xb04a3c: stp             lr, x16, [SP, #0x10]
    // 0xb04a40: ldur            x16, [fp, #-0x30]
    // 0xb04a44: ldur            lr, [fp, #-0x20]
    // 0xb04a48: stp             lr, x16, [SP]
    // 0xb04a4c: mov             x1, x0
    // 0xb04a50: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb04a50: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb04a54: ldr             x4, [x4, #0x870]
    // 0xb04a58: r0 = Container()
    //     0xb04a58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb04a5c: ldur            x0, [fp, #-8]
    // 0xb04a60: LoadField: r1 = r0->field_b
    //     0xb04a60: ldur            w1, [x0, #0xb]
    // 0xb04a64: DecompressPointer r1
    //     0xb04a64: add             x1, x1, HEAP, lsl #32
    // 0xb04a68: cmp             w1, NULL
    // 0xb04a6c: b.eq            #0xb05664
    // 0xb04a70: LoadField: r2 = r1->field_b
    //     0xb04a70: ldur            w2, [x1, #0xb]
    // 0xb04a74: DecompressPointer r2
    //     0xb04a74: add             x2, x2, HEAP, lsl #32
    // 0xb04a78: LoadField: r1 = r2->field_b
    //     0xb04a78: ldur            w1, [x2, #0xb]
    // 0xb04a7c: DecompressPointer r1
    //     0xb04a7c: add             x1, x1, HEAP, lsl #32
    // 0xb04a80: cmp             w1, NULL
    // 0xb04a84: b.ne            #0xb04a90
    // 0xb04a88: r1 = Null
    //     0xb04a88: mov             x1, NULL
    // 0xb04a8c: b               #0xb04aac
    // 0xb04a90: LoadField: r2 = r1->field_7
    //     0xb04a90: ldur            w2, [x1, #7]
    // 0xb04a94: DecompressPointer r2
    //     0xb04a94: add             x2, x2, HEAP, lsl #32
    // 0xb04a98: LoadField: r1 = r2->field_7
    //     0xb04a98: ldur            w1, [x2, #7]
    // 0xb04a9c: DecompressPointer r1
    //     0xb04a9c: add             x1, x1, HEAP, lsl #32
    // 0xb04aa0: LoadField: r2 = r1->field_7
    //     0xb04aa0: ldur            w2, [x1, #7]
    // 0xb04aa4: DecompressPointer r2
    //     0xb04aa4: add             x2, x2, HEAP, lsl #32
    // 0xb04aa8: mov             x1, x2
    // 0xb04aac: cmp             w1, NULL
    // 0xb04ab0: b.ne            #0xb04ab8
    // 0xb04ab4: r1 = false
    //     0xb04ab4: add             x1, NULL, #0x30  ; false
    // 0xb04ab8: stur            x1, [fp, #-0x20]
    // 0xb04abc: r0 = SvgPicture()
    //     0xb04abc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb04ac0: stur            x0, [fp, #-0x30]
    // 0xb04ac4: r16 = Instance_BoxFit
    //     0xb04ac4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb04ac8: ldr             x16, [x16, #0xb18]
    // 0xb04acc: str             x16, [SP]
    // 0xb04ad0: mov             x1, x0
    // 0xb04ad4: r2 = "assets/images/exchange_check.svg"
    //     0xb04ad4: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c98] "assets/images/exchange_check.svg"
    //     0xb04ad8: ldr             x2, [x2, #0xc98]
    // 0xb04adc: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb04adc: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb04ae0: ldr             x4, [x4, #0xb0]
    // 0xb04ae4: r0 = SvgPicture.asset()
    //     0xb04ae4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb04ae8: r0 = Visibility()
    //     0xb04ae8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb04aec: mov             x1, x0
    // 0xb04af0: ldur            x0, [fp, #-0x30]
    // 0xb04af4: stur            x1, [fp, #-0x40]
    // 0xb04af8: StoreField: r1->field_b = r0
    //     0xb04af8: stur            w0, [x1, #0xb]
    // 0xb04afc: r0 = Instance_SizedBox
    //     0xb04afc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb04b00: StoreField: r1->field_f = r0
    //     0xb04b00: stur            w0, [x1, #0xf]
    // 0xb04b04: ldur            x2, [fp, #-0x20]
    // 0xb04b08: StoreField: r1->field_13 = r2
    //     0xb04b08: stur            w2, [x1, #0x13]
    // 0xb04b0c: r2 = false
    //     0xb04b0c: add             x2, NULL, #0x30  ; false
    // 0xb04b10: ArrayStore: r1[0] = r2  ; List_4
    //     0xb04b10: stur            w2, [x1, #0x17]
    // 0xb04b14: StoreField: r1->field_1b = r2
    //     0xb04b14: stur            w2, [x1, #0x1b]
    // 0xb04b18: StoreField: r1->field_1f = r2
    //     0xb04b18: stur            w2, [x1, #0x1f]
    // 0xb04b1c: StoreField: r1->field_23 = r2
    //     0xb04b1c: stur            w2, [x1, #0x23]
    // 0xb04b20: StoreField: r1->field_27 = r2
    //     0xb04b20: stur            w2, [x1, #0x27]
    // 0xb04b24: StoreField: r1->field_2b = r2
    //     0xb04b24: stur            w2, [x1, #0x2b]
    // 0xb04b28: ldur            x3, [fp, #-8]
    // 0xb04b2c: LoadField: r4 = r3->field_b
    //     0xb04b2c: ldur            w4, [x3, #0xb]
    // 0xb04b30: DecompressPointer r4
    //     0xb04b30: add             x4, x4, HEAP, lsl #32
    // 0xb04b34: cmp             w4, NULL
    // 0xb04b38: b.eq            #0xb05668
    // 0xb04b3c: LoadField: r5 = r4->field_b
    //     0xb04b3c: ldur            w5, [x4, #0xb]
    // 0xb04b40: DecompressPointer r5
    //     0xb04b40: add             x5, x5, HEAP, lsl #32
    // 0xb04b44: LoadField: r4 = r5->field_b
    //     0xb04b44: ldur            w4, [x5, #0xb]
    // 0xb04b48: DecompressPointer r4
    //     0xb04b48: add             x4, x4, HEAP, lsl #32
    // 0xb04b4c: cmp             w4, NULL
    // 0xb04b50: b.ne            #0xb04b5c
    // 0xb04b54: r4 = Null
    //     0xb04b54: mov             x4, NULL
    // 0xb04b58: b               #0xb04b78
    // 0xb04b5c: LoadField: r5 = r4->field_7
    //     0xb04b5c: ldur            w5, [x4, #7]
    // 0xb04b60: DecompressPointer r5
    //     0xb04b60: add             x5, x5, HEAP, lsl #32
    // 0xb04b64: LoadField: r4 = r5->field_7
    //     0xb04b64: ldur            w4, [x5, #7]
    // 0xb04b68: DecompressPointer r4
    //     0xb04b68: add             x4, x4, HEAP, lsl #32
    // 0xb04b6c: LoadField: r5 = r4->field_7
    //     0xb04b6c: ldur            w5, [x4, #7]
    // 0xb04b70: DecompressPointer r5
    //     0xb04b70: add             x5, x5, HEAP, lsl #32
    // 0xb04b74: mov             x4, x5
    // 0xb04b78: cmp             w4, NULL
    // 0xb04b7c: b.ne            #0xb04b88
    // 0xb04b80: r5 = false
    //     0xb04b80: add             x5, NULL, #0x30  ; false
    // 0xb04b84: b               #0xb04b8c
    // 0xb04b88: mov             x5, x4
    // 0xb04b8c: ldur            x4, [fp, #-0x38]
    // 0xb04b90: eor             x6, x5, #0x10
    // 0xb04b94: stur            x6, [fp, #-0x20]
    // 0xb04b98: r0 = SvgPicture()
    //     0xb04b98: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb04b9c: stur            x0, [fp, #-0x30]
    // 0xb04ba0: r16 = Instance_BoxFit
    //     0xb04ba0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb04ba4: ldr             x16, [x16, #0xb18]
    // 0xb04ba8: str             x16, [SP]
    // 0xb04bac: mov             x1, x0
    // 0xb04bb0: r2 = "assets/images/replacement_check.svg"
    //     0xb04bb0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52c90] "assets/images/replacement_check.svg"
    //     0xb04bb4: ldr             x2, [x2, #0xc90]
    // 0xb04bb8: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb04bb8: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb04bbc: ldr             x4, [x4, #0xb0]
    // 0xb04bc0: r0 = SvgPicture.asset()
    //     0xb04bc0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb04bc4: r0 = Visibility()
    //     0xb04bc4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb04bc8: mov             x3, x0
    // 0xb04bcc: ldur            x0, [fp, #-0x30]
    // 0xb04bd0: stur            x3, [fp, #-0x48]
    // 0xb04bd4: StoreField: r3->field_b = r0
    //     0xb04bd4: stur            w0, [x3, #0xb]
    // 0xb04bd8: r0 = Instance_SizedBox
    //     0xb04bd8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb04bdc: StoreField: r3->field_f = r0
    //     0xb04bdc: stur            w0, [x3, #0xf]
    // 0xb04be0: ldur            x0, [fp, #-0x20]
    // 0xb04be4: StoreField: r3->field_13 = r0
    //     0xb04be4: stur            w0, [x3, #0x13]
    // 0xb04be8: r0 = false
    //     0xb04be8: add             x0, NULL, #0x30  ; false
    // 0xb04bec: ArrayStore: r3[0] = r0  ; List_4
    //     0xb04bec: stur            w0, [x3, #0x17]
    // 0xb04bf0: StoreField: r3->field_1b = r0
    //     0xb04bf0: stur            w0, [x3, #0x1b]
    // 0xb04bf4: StoreField: r3->field_1f = r0
    //     0xb04bf4: stur            w0, [x3, #0x1f]
    // 0xb04bf8: StoreField: r3->field_23 = r0
    //     0xb04bf8: stur            w0, [x3, #0x23]
    // 0xb04bfc: StoreField: r3->field_27 = r0
    //     0xb04bfc: stur            w0, [x3, #0x27]
    // 0xb04c00: StoreField: r3->field_2b = r0
    //     0xb04c00: stur            w0, [x3, #0x2b]
    // 0xb04c04: r1 = Null
    //     0xb04c04: mov             x1, NULL
    // 0xb04c08: r2 = 6
    //     0xb04c08: movz            x2, #0x6
    // 0xb04c0c: r0 = AllocateArray()
    //     0xb04c0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb04c10: mov             x2, x0
    // 0xb04c14: ldur            x0, [fp, #-0x38]
    // 0xb04c18: stur            x2, [fp, #-0x20]
    // 0xb04c1c: StoreField: r2->field_f = r0
    //     0xb04c1c: stur            w0, [x2, #0xf]
    // 0xb04c20: ldur            x0, [fp, #-0x40]
    // 0xb04c24: StoreField: r2->field_13 = r0
    //     0xb04c24: stur            w0, [x2, #0x13]
    // 0xb04c28: ldur            x0, [fp, #-0x48]
    // 0xb04c2c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb04c2c: stur            w0, [x2, #0x17]
    // 0xb04c30: r1 = <Widget>
    //     0xb04c30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb04c34: r0 = AllocateGrowableArray()
    //     0xb04c34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb04c38: mov             x1, x0
    // 0xb04c3c: ldur            x0, [fp, #-0x20]
    // 0xb04c40: stur            x1, [fp, #-0x30]
    // 0xb04c44: StoreField: r1->field_f = r0
    //     0xb04c44: stur            w0, [x1, #0xf]
    // 0xb04c48: r0 = 6
    //     0xb04c48: movz            x0, #0x6
    // 0xb04c4c: StoreField: r1->field_b = r0
    //     0xb04c4c: stur            w0, [x1, #0xb]
    // 0xb04c50: r0 = Stack()
    //     0xb04c50: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb04c54: mov             x1, x0
    // 0xb04c58: r0 = Instance_Alignment
    //     0xb04c58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb04c5c: ldr             x0, [x0, #0x950]
    // 0xb04c60: stur            x1, [fp, #-0x20]
    // 0xb04c64: StoreField: r1->field_f = r0
    //     0xb04c64: stur            w0, [x1, #0xf]
    // 0xb04c68: r0 = Instance_StackFit
    //     0xb04c68: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb04c6c: ldr             x0, [x0, #0xfa8]
    // 0xb04c70: ArrayStore: r1[0] = r0  ; List_4
    //     0xb04c70: stur            w0, [x1, #0x17]
    // 0xb04c74: r0 = Instance_Clip
    //     0xb04c74: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb04c78: ldr             x0, [x0, #0x7e0]
    // 0xb04c7c: StoreField: r1->field_1b = r0
    //     0xb04c7c: stur            w0, [x1, #0x1b]
    // 0xb04c80: ldur            x2, [fp, #-0x30]
    // 0xb04c84: StoreField: r1->field_b = r2
    //     0xb04c84: stur            w2, [x1, #0xb]
    // 0xb04c88: ldur            x2, [fp, #-8]
    // 0xb04c8c: LoadField: r3 = r2->field_b
    //     0xb04c8c: ldur            w3, [x2, #0xb]
    // 0xb04c90: DecompressPointer r3
    //     0xb04c90: add             x3, x3, HEAP, lsl #32
    // 0xb04c94: cmp             w3, NULL
    // 0xb04c98: b.eq            #0xb0566c
    // 0xb04c9c: LoadField: r4 = r3->field_b
    //     0xb04c9c: ldur            w4, [x3, #0xb]
    // 0xb04ca0: DecompressPointer r4
    //     0xb04ca0: add             x4, x4, HEAP, lsl #32
    // 0xb04ca4: LoadField: r3 = r4->field_b
    //     0xb04ca4: ldur            w3, [x4, #0xb]
    // 0xb04ca8: DecompressPointer r3
    //     0xb04ca8: add             x3, x3, HEAP, lsl #32
    // 0xb04cac: cmp             w3, NULL
    // 0xb04cb0: b.ne            #0xb04cbc
    // 0xb04cb4: r4 = Null
    //     0xb04cb4: mov             x4, NULL
    // 0xb04cb8: b               #0xb04cd4
    // 0xb04cbc: LoadField: r4 = r3->field_7
    //     0xb04cbc: ldur            w4, [x3, #7]
    // 0xb04cc0: DecompressPointer r4
    //     0xb04cc0: add             x4, x4, HEAP, lsl #32
    // 0xb04cc4: LoadField: r3 = r4->field_b
    //     0xb04cc4: ldur            w3, [x4, #0xb]
    // 0xb04cc8: DecompressPointer r3
    //     0xb04cc8: add             x3, x3, HEAP, lsl #32
    // 0xb04ccc: LoadField: r4 = r3->field_b
    //     0xb04ccc: ldur            w4, [x3, #0xb]
    // 0xb04cd0: DecompressPointer r4
    //     0xb04cd0: add             x4, x4, HEAP, lsl #32
    // 0xb04cd4: ldur            x3, [fp, #-0x28]
    // 0xb04cd8: str             x4, [SP]
    // 0xb04cdc: r0 = _interpolateSingle()
    //     0xb04cdc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb04ce0: ldur            x1, [fp, #-0x10]
    // 0xb04ce4: stur            x0, [fp, #-0x30]
    // 0xb04ce8: r0 = of()
    //     0xb04ce8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb04cec: LoadField: r1 = r0->field_87
    //     0xb04cec: ldur            w1, [x0, #0x87]
    // 0xb04cf0: DecompressPointer r1
    //     0xb04cf0: add             x1, x1, HEAP, lsl #32
    // 0xb04cf4: LoadField: r0 = r1->field_2b
    //     0xb04cf4: ldur            w0, [x1, #0x2b]
    // 0xb04cf8: DecompressPointer r0
    //     0xb04cf8: add             x0, x0, HEAP, lsl #32
    // 0xb04cfc: r16 = 12.000000
    //     0xb04cfc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb04d00: ldr             x16, [x16, #0x9e8]
    // 0xb04d04: r30 = Instance_Color
    //     0xb04d04: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb04d08: stp             lr, x16, [SP]
    // 0xb04d0c: mov             x1, x0
    // 0xb04d10: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb04d10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb04d14: ldr             x4, [x4, #0xaa0]
    // 0xb04d18: r0 = copyWith()
    //     0xb04d18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb04d1c: stur            x0, [fp, #-0x38]
    // 0xb04d20: r0 = Text()
    //     0xb04d20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb04d24: mov             x1, x0
    // 0xb04d28: ldur            x0, [fp, #-0x30]
    // 0xb04d2c: stur            x1, [fp, #-0x40]
    // 0xb04d30: StoreField: r1->field_b = r0
    //     0xb04d30: stur            w0, [x1, #0xb]
    // 0xb04d34: ldur            x0, [fp, #-0x38]
    // 0xb04d38: StoreField: r1->field_13 = r0
    //     0xb04d38: stur            w0, [x1, #0x13]
    // 0xb04d3c: r0 = Padding()
    //     0xb04d3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb04d40: mov             x3, x0
    // 0xb04d44: r0 = Instance_EdgeInsets
    //     0xb04d44: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xb04d48: ldr             x0, [x0, #0x770]
    // 0xb04d4c: stur            x3, [fp, #-0x30]
    // 0xb04d50: StoreField: r3->field_f = r0
    //     0xb04d50: stur            w0, [x3, #0xf]
    // 0xb04d54: ldur            x0, [fp, #-0x40]
    // 0xb04d58: StoreField: r3->field_b = r0
    //     0xb04d58: stur            w0, [x3, #0xb]
    // 0xb04d5c: r1 = Null
    //     0xb04d5c: mov             x1, NULL
    // 0xb04d60: r2 = 4
    //     0xb04d60: movz            x2, #0x4
    // 0xb04d64: r0 = AllocateArray()
    //     0xb04d64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb04d68: mov             x2, x0
    // 0xb04d6c: ldur            x0, [fp, #-0x20]
    // 0xb04d70: stur            x2, [fp, #-0x38]
    // 0xb04d74: StoreField: r2->field_f = r0
    //     0xb04d74: stur            w0, [x2, #0xf]
    // 0xb04d78: ldur            x0, [fp, #-0x30]
    // 0xb04d7c: StoreField: r2->field_13 = r0
    //     0xb04d7c: stur            w0, [x2, #0x13]
    // 0xb04d80: r1 = <Widget>
    //     0xb04d80: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb04d84: r0 = AllocateGrowableArray()
    //     0xb04d84: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb04d88: mov             x1, x0
    // 0xb04d8c: ldur            x0, [fp, #-0x38]
    // 0xb04d90: stur            x1, [fp, #-0x20]
    // 0xb04d94: StoreField: r1->field_f = r0
    //     0xb04d94: stur            w0, [x1, #0xf]
    // 0xb04d98: r2 = 4
    //     0xb04d98: movz            x2, #0x4
    // 0xb04d9c: StoreField: r1->field_b = r2
    //     0xb04d9c: stur            w2, [x1, #0xb]
    // 0xb04da0: r0 = Column()
    //     0xb04da0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb04da4: mov             x3, x0
    // 0xb04da8: r0 = Instance_Axis
    //     0xb04da8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb04dac: stur            x3, [fp, #-0x30]
    // 0xb04db0: StoreField: r3->field_f = r0
    //     0xb04db0: stur            w0, [x3, #0xf]
    // 0xb04db4: r4 = Instance_MainAxisAlignment
    //     0xb04db4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb04db8: ldr             x4, [x4, #0xa08]
    // 0xb04dbc: StoreField: r3->field_13 = r4
    //     0xb04dbc: stur            w4, [x3, #0x13]
    // 0xb04dc0: r5 = Instance_MainAxisSize
    //     0xb04dc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb04dc4: ldr             x5, [x5, #0xa10]
    // 0xb04dc8: ArrayStore: r3[0] = r5  ; List_4
    //     0xb04dc8: stur            w5, [x3, #0x17]
    // 0xb04dcc: r6 = Instance_CrossAxisAlignment
    //     0xb04dcc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb04dd0: ldr             x6, [x6, #0xa18]
    // 0xb04dd4: StoreField: r3->field_1b = r6
    //     0xb04dd4: stur            w6, [x3, #0x1b]
    // 0xb04dd8: r7 = Instance_VerticalDirection
    //     0xb04dd8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb04ddc: ldr             x7, [x7, #0xa20]
    // 0xb04de0: StoreField: r3->field_23 = r7
    //     0xb04de0: stur            w7, [x3, #0x23]
    // 0xb04de4: r8 = Instance_Clip
    //     0xb04de4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb04de8: ldr             x8, [x8, #0x38]
    // 0xb04dec: StoreField: r3->field_2b = r8
    //     0xb04dec: stur            w8, [x3, #0x2b]
    // 0xb04df0: StoreField: r3->field_2f = rZR
    //     0xb04df0: stur            xzr, [x3, #0x2f]
    // 0xb04df4: ldur            x1, [fp, #-0x20]
    // 0xb04df8: StoreField: r3->field_b = r1
    //     0xb04df8: stur            w1, [x3, #0xb]
    // 0xb04dfc: r1 = Null
    //     0xb04dfc: mov             x1, NULL
    // 0xb04e00: r2 = 4
    //     0xb04e00: movz            x2, #0x4
    // 0xb04e04: r0 = AllocateArray()
    //     0xb04e04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb04e08: mov             x2, x0
    // 0xb04e0c: ldur            x0, [fp, #-0x28]
    // 0xb04e10: stur            x2, [fp, #-0x20]
    // 0xb04e14: StoreField: r2->field_f = r0
    //     0xb04e14: stur            w0, [x2, #0xf]
    // 0xb04e18: ldur            x0, [fp, #-0x30]
    // 0xb04e1c: StoreField: r2->field_13 = r0
    //     0xb04e1c: stur            w0, [x2, #0x13]
    // 0xb04e20: r1 = <Widget>
    //     0xb04e20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb04e24: r0 = AllocateGrowableArray()
    //     0xb04e24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb04e28: mov             x1, x0
    // 0xb04e2c: ldur            x0, [fp, #-0x20]
    // 0xb04e30: stur            x1, [fp, #-0x28]
    // 0xb04e34: StoreField: r1->field_f = r0
    //     0xb04e34: stur            w0, [x1, #0xf]
    // 0xb04e38: r0 = 4
    //     0xb04e38: movz            x0, #0x4
    // 0xb04e3c: StoreField: r1->field_b = r0
    //     0xb04e3c: stur            w0, [x1, #0xb]
    // 0xb04e40: r0 = Row()
    //     0xb04e40: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb04e44: mov             x3, x0
    // 0xb04e48: r0 = Instance_Axis
    //     0xb04e48: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb04e4c: stur            x3, [fp, #-0x20]
    // 0xb04e50: StoreField: r3->field_f = r0
    //     0xb04e50: stur            w0, [x3, #0xf]
    // 0xb04e54: r0 = Instance_MainAxisAlignment
    //     0xb04e54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb04e58: ldr             x0, [x0, #0xd10]
    // 0xb04e5c: StoreField: r3->field_13 = r0
    //     0xb04e5c: stur            w0, [x3, #0x13]
    // 0xb04e60: r0 = Instance_MainAxisSize
    //     0xb04e60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb04e64: ldr             x0, [x0, #0xa10]
    // 0xb04e68: ArrayStore: r3[0] = r0  ; List_4
    //     0xb04e68: stur            w0, [x3, #0x17]
    // 0xb04e6c: r1 = Instance_CrossAxisAlignment
    //     0xb04e6c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb04e70: ldr             x1, [x1, #0xa18]
    // 0xb04e74: StoreField: r3->field_1b = r1
    //     0xb04e74: stur            w1, [x3, #0x1b]
    // 0xb04e78: r4 = Instance_VerticalDirection
    //     0xb04e78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb04e7c: ldr             x4, [x4, #0xa20]
    // 0xb04e80: StoreField: r3->field_23 = r4
    //     0xb04e80: stur            w4, [x3, #0x23]
    // 0xb04e84: r5 = Instance_Clip
    //     0xb04e84: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb04e88: ldr             x5, [x5, #0x38]
    // 0xb04e8c: StoreField: r3->field_2b = r5
    //     0xb04e8c: stur            w5, [x3, #0x2b]
    // 0xb04e90: StoreField: r3->field_2f = rZR
    //     0xb04e90: stur            xzr, [x3, #0x2f]
    // 0xb04e94: ldur            x1, [fp, #-0x28]
    // 0xb04e98: StoreField: r3->field_b = r1
    //     0xb04e98: stur            w1, [x3, #0xb]
    // 0xb04e9c: r1 = <Widget>
    //     0xb04e9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb04ea0: r2 = 20
    //     0xb04ea0: movz            x2, #0x14
    // 0xb04ea4: r0 = AllocateArray()
    //     0xb04ea4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb04ea8: mov             x1, x0
    // 0xb04eac: ldur            x0, [fp, #-0x20]
    // 0xb04eb0: stur            x1, [fp, #-0x28]
    // 0xb04eb4: StoreField: r1->field_f = r0
    //     0xb04eb4: stur            w0, [x1, #0xf]
    // 0xb04eb8: r16 = Instance_SizedBox
    //     0xb04eb8: add             x16, PP, #0x55, lsl #12  ; [pp+0x558b0] Obj!SizedBox@d68081
    //     0xb04ebc: ldr             x16, [x16, #0x8b0]
    // 0xb04ec0: StoreField: r1->field_13 = r16
    //     0xb04ec0: stur            w16, [x1, #0x13]
    // 0xb04ec4: ldur            x0, [fp, #-8]
    // 0xb04ec8: LoadField: r2 = r0->field_b
    //     0xb04ec8: ldur            w2, [x0, #0xb]
    // 0xb04ecc: DecompressPointer r2
    //     0xb04ecc: add             x2, x2, HEAP, lsl #32
    // 0xb04ed0: cmp             w2, NULL
    // 0xb04ed4: b.eq            #0xb05670
    // 0xb04ed8: LoadField: r3 = r2->field_b
    //     0xb04ed8: ldur            w3, [x2, #0xb]
    // 0xb04edc: DecompressPointer r3
    //     0xb04edc: add             x3, x3, HEAP, lsl #32
    // 0xb04ee0: LoadField: r2 = r3->field_b
    //     0xb04ee0: ldur            w2, [x3, #0xb]
    // 0xb04ee4: DecompressPointer r2
    //     0xb04ee4: add             x2, x2, HEAP, lsl #32
    // 0xb04ee8: cmp             w2, NULL
    // 0xb04eec: b.ne            #0xb04ef8
    // 0xb04ef0: r2 = Null
    //     0xb04ef0: mov             x2, NULL
    // 0xb04ef4: b               #0xb04f08
    // 0xb04ef8: LoadField: r3 = r2->field_b
    //     0xb04ef8: ldur            w3, [x2, #0xb]
    // 0xb04efc: DecompressPointer r3
    //     0xb04efc: add             x3, x3, HEAP, lsl #32
    // 0xb04f00: LoadField: r2 = r3->field_b
    //     0xb04f00: ldur            w2, [x3, #0xb]
    // 0xb04f04: DecompressPointer r2
    //     0xb04f04: add             x2, x2, HEAP, lsl #32
    // 0xb04f08: str             x2, [SP]
    // 0xb04f0c: r0 = _interpolateSingle()
    //     0xb04f0c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb04f10: ldur            x1, [fp, #-0x10]
    // 0xb04f14: stur            x0, [fp, #-0x20]
    // 0xb04f18: r0 = of()
    //     0xb04f18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb04f1c: LoadField: r1 = r0->field_87
    //     0xb04f1c: ldur            w1, [x0, #0x87]
    // 0xb04f20: DecompressPointer r1
    //     0xb04f20: add             x1, x1, HEAP, lsl #32
    // 0xb04f24: LoadField: r0 = r1->field_7
    //     0xb04f24: ldur            w0, [x1, #7]
    // 0xb04f28: DecompressPointer r0
    //     0xb04f28: add             x0, x0, HEAP, lsl #32
    // 0xb04f2c: r16 = 16.000000
    //     0xb04f2c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb04f30: ldr             x16, [x16, #0x188]
    // 0xb04f34: r30 = Instance_Color
    //     0xb04f34: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb04f38: stp             lr, x16, [SP]
    // 0xb04f3c: mov             x1, x0
    // 0xb04f40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb04f40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb04f44: ldr             x4, [x4, #0xaa0]
    // 0xb04f48: r0 = copyWith()
    //     0xb04f48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb04f4c: stur            x0, [fp, #-0x30]
    // 0xb04f50: r0 = Text()
    //     0xb04f50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb04f54: mov             x1, x0
    // 0xb04f58: ldur            x0, [fp, #-0x20]
    // 0xb04f5c: stur            x1, [fp, #-0x38]
    // 0xb04f60: StoreField: r1->field_b = r0
    //     0xb04f60: stur            w0, [x1, #0xb]
    // 0xb04f64: ldur            x0, [fp, #-0x30]
    // 0xb04f68: StoreField: r1->field_13 = r0
    //     0xb04f68: stur            w0, [x1, #0x13]
    // 0xb04f6c: r0 = SizedBox()
    //     0xb04f6c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb04f70: mov             x1, x0
    // 0xb04f74: r0 = inf
    //     0xb04f74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb04f78: ldr             x0, [x0, #0x9f8]
    // 0xb04f7c: StoreField: r1->field_f = r0
    //     0xb04f7c: stur            w0, [x1, #0xf]
    // 0xb04f80: ldur            x0, [fp, #-0x38]
    // 0xb04f84: StoreField: r1->field_b = r0
    //     0xb04f84: stur            w0, [x1, #0xb]
    // 0xb04f88: mov             x0, x1
    // 0xb04f8c: ldur            x1, [fp, #-0x28]
    // 0xb04f90: ArrayStore: r1[2] = r0  ; List_4
    //     0xb04f90: add             x25, x1, #0x17
    //     0xb04f94: str             w0, [x25]
    //     0xb04f98: tbz             w0, #0, #0xb04fb4
    //     0xb04f9c: ldurb           w16, [x1, #-1]
    //     0xb04fa0: ldurb           w17, [x0, #-1]
    //     0xb04fa4: and             x16, x17, x16, lsr #2
    //     0xb04fa8: tst             x16, HEAP, lsr #32
    //     0xb04fac: b.eq            #0xb04fb4
    //     0xb04fb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb04fb4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb04fb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb04fb8: ldr             x0, [x0, #0x1c80]
    //     0xb04fbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb04fc0: cmp             w0, w16
    //     0xb04fc4: b.ne            #0xb04fd0
    //     0xb04fc8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb04fcc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb04fd0: r0 = GetNavigation.width()
    //     0xb04fd0: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xb04fd4: mov             v1.16b, v0.16b
    // 0xb04fd8: d0 = 0.800000
    //     0xb04fd8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0xb04fdc: ldr             d0, [x17, #0xb28]
    // 0xb04fe0: fmul            d2, d1, d0
    // 0xb04fe4: ldur            x0, [fp, #-8]
    // 0xb04fe8: stur            d2, [fp, #-0x58]
    // 0xb04fec: LoadField: r1 = r0->field_b
    //     0xb04fec: ldur            w1, [x0, #0xb]
    // 0xb04ff0: DecompressPointer r1
    //     0xb04ff0: add             x1, x1, HEAP, lsl #32
    // 0xb04ff4: cmp             w1, NULL
    // 0xb04ff8: b.eq            #0xb05674
    // 0xb04ffc: LoadField: r2 = r1->field_b
    //     0xb04ffc: ldur            w2, [x1, #0xb]
    // 0xb05000: DecompressPointer r2
    //     0xb05000: add             x2, x2, HEAP, lsl #32
    // 0xb05004: LoadField: r1 = r2->field_b
    //     0xb05004: ldur            w1, [x2, #0xb]
    // 0xb05008: DecompressPointer r1
    //     0xb05008: add             x1, x1, HEAP, lsl #32
    // 0xb0500c: cmp             w1, NULL
    // 0xb05010: b.ne            #0xb0501c
    // 0xb05014: r4 = Null
    //     0xb05014: mov             x4, NULL
    // 0xb05018: b               #0xb05034
    // 0xb0501c: LoadField: r2 = r1->field_b
    //     0xb0501c: ldur            w2, [x1, #0xb]
    // 0xb05020: DecompressPointer r2
    //     0xb05020: add             x2, x2, HEAP, lsl #32
    // 0xb05024: LoadField: r1 = r2->field_7
    //     0xb05024: ldur            w1, [x2, #7]
    // 0xb05028: DecompressPointer r1
    //     0xb05028: add             x1, x1, HEAP, lsl #32
    // 0xb0502c: LoadField: r2 = r1->field_b
    //     0xb0502c: ldur            w2, [x1, #0xb]
    // 0xb05030: mov             x4, x2
    // 0xb05034: ldur            x3, [fp, #-0x28]
    // 0xb05038: ldur            x2, [fp, #-0x18]
    // 0xb0503c: stur            x4, [fp, #-0x20]
    // 0xb05040: r1 = Function '<anonymous closure>':.
    //     0xb05040: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e60] AnonymousClosure: (0xb0623c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xb7f450)
    //     0xb05044: ldr             x1, [x1, #0xe60]
    // 0xb05048: r0 = AllocateClosure()
    //     0xb05048: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0504c: stur            x0, [fp, #-0x30]
    // 0xb05050: r0 = ListView()
    //     0xb05050: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb05054: stur            x0, [fp, #-0x38]
    // 0xb05058: r16 = true
    //     0xb05058: add             x16, NULL, #0x20  ; true
    // 0xb0505c: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb0505c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb05060: ldr             lr, [lr, #0x1c8]
    // 0xb05064: stp             lr, x16, [SP]
    // 0xb05068: mov             x1, x0
    // 0xb0506c: ldur            x2, [fp, #-0x30]
    // 0xb05070: ldur            x3, [fp, #-0x20]
    // 0xb05074: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb05074: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb05078: ldr             x4, [x4, #8]
    // 0xb0507c: r0 = ListView.builder()
    //     0xb0507c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb05080: ldur            d0, [fp, #-0x58]
    // 0xb05084: r0 = inline_Allocate_Double()
    //     0xb05084: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb05088: add             x0, x0, #0x10
    //     0xb0508c: cmp             x1, x0
    //     0xb05090: b.ls            #0xb05678
    //     0xb05094: str             x0, [THR, #0x50]  ; THR::top
    //     0xb05098: sub             x0, x0, #0xf
    //     0xb0509c: movz            x1, #0xe15c
    //     0xb050a0: movk            x1, #0x3, lsl #16
    //     0xb050a4: stur            x1, [x0, #-1]
    // 0xb050a8: StoreField: r0->field_7 = d0
    //     0xb050a8: stur            d0, [x0, #7]
    // 0xb050ac: stur            x0, [fp, #-0x20]
    // 0xb050b0: r0 = Container()
    //     0xb050b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb050b4: stur            x0, [fp, #-0x30]
    // 0xb050b8: ldur            x16, [fp, #-0x20]
    // 0xb050bc: r30 = Instance_EdgeInsets
    //     0xb050bc: add             lr, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb050c0: ldr             lr, [lr, #0x858]
    // 0xb050c4: stp             lr, x16, [SP, #8]
    // 0xb050c8: ldur            x16, [fp, #-0x38]
    // 0xb050cc: str             x16, [SP]
    // 0xb050d0: mov             x1, x0
    // 0xb050d4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xb050d4: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xb050d8: ldr             x4, [x4, #0x628]
    // 0xb050dc: r0 = Container()
    //     0xb050dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb050e0: ldur            x1, [fp, #-0x28]
    // 0xb050e4: ldur            x0, [fp, #-0x30]
    // 0xb050e8: ArrayStore: r1[3] = r0  ; List_4
    //     0xb050e8: add             x25, x1, #0x1b
    //     0xb050ec: str             w0, [x25]
    //     0xb050f0: tbz             w0, #0, #0xb0510c
    //     0xb050f4: ldurb           w16, [x1, #-1]
    //     0xb050f8: ldurb           w17, [x0, #-1]
    //     0xb050fc: and             x16, x17, x16, lsr #2
    //     0xb05100: tst             x16, HEAP, lsr #32
    //     0xb05104: b.eq            #0xb0510c
    //     0xb05108: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb0510c: ldur            x1, [fp, #-0x28]
    // 0xb05110: r16 = Instance_SizedBox
    //     0xb05110: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xb05114: ldr             x16, [x16, #0x9f0]
    // 0xb05118: StoreField: r1->field_1f = r16
    //     0xb05118: stur            w16, [x1, #0x1f]
    // 0xb0511c: ldur            x0, [fp, #-8]
    // 0xb05120: LoadField: r2 = r0->field_b
    //     0xb05120: ldur            w2, [x0, #0xb]
    // 0xb05124: DecompressPointer r2
    //     0xb05124: add             x2, x2, HEAP, lsl #32
    // 0xb05128: cmp             w2, NULL
    // 0xb0512c: b.eq            #0xb05688
    // 0xb05130: LoadField: r3 = r2->field_b
    //     0xb05130: ldur            w3, [x2, #0xb]
    // 0xb05134: DecompressPointer r3
    //     0xb05134: add             x3, x3, HEAP, lsl #32
    // 0xb05138: LoadField: r2 = r3->field_b
    //     0xb05138: ldur            w2, [x3, #0xb]
    // 0xb0513c: DecompressPointer r2
    //     0xb0513c: add             x2, x2, HEAP, lsl #32
    // 0xb05140: cmp             w2, NULL
    // 0xb05144: b.ne            #0xb05150
    // 0xb05148: r2 = Null
    //     0xb05148: mov             x2, NULL
    // 0xb0514c: b               #0xb05160
    // 0xb05150: LoadField: r3 = r2->field_f
    //     0xb05150: ldur            w3, [x2, #0xf]
    // 0xb05154: DecompressPointer r3
    //     0xb05154: add             x3, x3, HEAP, lsl #32
    // 0xb05158: LoadField: r2 = r3->field_7
    //     0xb05158: ldur            w2, [x3, #7]
    // 0xb0515c: DecompressPointer r2
    //     0xb0515c: add             x2, x2, HEAP, lsl #32
    // 0xb05160: str             x2, [SP]
    // 0xb05164: r0 = _interpolateSingle()
    //     0xb05164: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb05168: ldur            x1, [fp, #-0x10]
    // 0xb0516c: stur            x0, [fp, #-0x20]
    // 0xb05170: r0 = of()
    //     0xb05170: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb05174: LoadField: r1 = r0->field_87
    //     0xb05174: ldur            w1, [x0, #0x87]
    // 0xb05178: DecompressPointer r1
    //     0xb05178: add             x1, x1, HEAP, lsl #32
    // 0xb0517c: LoadField: r0 = r1->field_7
    //     0xb0517c: ldur            w0, [x1, #7]
    // 0xb05180: DecompressPointer r0
    //     0xb05180: add             x0, x0, HEAP, lsl #32
    // 0xb05184: r16 = 16.000000
    //     0xb05184: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb05188: ldr             x16, [x16, #0x188]
    // 0xb0518c: r30 = Instance_Color
    //     0xb0518c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb05190: stp             lr, x16, [SP]
    // 0xb05194: mov             x1, x0
    // 0xb05198: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb05198: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0519c: ldr             x4, [x4, #0xaa0]
    // 0xb051a0: r0 = copyWith()
    //     0xb051a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb051a4: stur            x0, [fp, #-0x30]
    // 0xb051a8: r0 = Text()
    //     0xb051a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb051ac: mov             x1, x0
    // 0xb051b0: ldur            x0, [fp, #-0x20]
    // 0xb051b4: StoreField: r1->field_b = r0
    //     0xb051b4: stur            w0, [x1, #0xb]
    // 0xb051b8: ldur            x0, [fp, #-0x30]
    // 0xb051bc: StoreField: r1->field_13 = r0
    //     0xb051bc: stur            w0, [x1, #0x13]
    // 0xb051c0: mov             x0, x1
    // 0xb051c4: ldur            x1, [fp, #-0x28]
    // 0xb051c8: ArrayStore: r1[5] = r0  ; List_4
    //     0xb051c8: add             x25, x1, #0x23
    //     0xb051cc: str             w0, [x25]
    //     0xb051d0: tbz             w0, #0, #0xb051ec
    //     0xb051d4: ldurb           w16, [x1, #-1]
    //     0xb051d8: ldurb           w17, [x0, #-1]
    //     0xb051dc: and             x16, x17, x16, lsr #2
    //     0xb051e0: tst             x16, HEAP, lsr #32
    //     0xb051e4: b.eq            #0xb051ec
    //     0xb051e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb051ec: ldur            x0, [fp, #-0x28]
    // 0xb051f0: r16 = Instance_SizedBox
    //     0xb051f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb051f4: ldr             x16, [x16, #0x8b8]
    // 0xb051f8: StoreField: r0->field_27 = r16
    //     0xb051f8: stur            w16, [x0, #0x27]
    // 0xb051fc: r16 = 0.000000
    //     0xb051fc: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb05200: r30 = Instance_ConnectorThemeData
    //     0xb05200: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3fe88] Obj!ConnectorThemeData@d5bb71
    //     0xb05204: ldr             lr, [lr, #0xe88]
    // 0xb05208: stp             lr, x16, [SP]
    // 0xb0520c: r1 = Null
    //     0xb0520c: mov             x1, NULL
    // 0xb05210: r4 = const [0, 0x3, 0x2, 0x1, connectorTheme, 0x2, nodePosition, 0x1, null]
    //     0xb05210: add             x4, PP, #0x36, lsl #12  ; [pp+0x365b8] List(9) [0, 0x3, 0x2, 0x1, "connectorTheme", 0x2, "nodePosition", 0x1, Null]
    //     0xb05214: ldr             x4, [x4, #0x5b8]
    // 0xb05218: r0 = TimelineThemeData()
    //     0xb05218: bl              #0x9dffe8  ; [package:timelines_plus/src/timeline_theme.dart] TimelineThemeData::TimelineThemeData
    // 0xb0521c: mov             x3, x0
    // 0xb05220: ldur            x0, [fp, #-8]
    // 0xb05224: stur            x3, [fp, #-0x20]
    // 0xb05228: LoadField: r1 = r0->field_b
    //     0xb05228: ldur            w1, [x0, #0xb]
    // 0xb0522c: DecompressPointer r1
    //     0xb0522c: add             x1, x1, HEAP, lsl #32
    // 0xb05230: cmp             w1, NULL
    // 0xb05234: b.eq            #0xb0568c
    // 0xb05238: LoadField: r0 = r1->field_b
    //     0xb05238: ldur            w0, [x1, #0xb]
    // 0xb0523c: DecompressPointer r0
    //     0xb0523c: add             x0, x0, HEAP, lsl #32
    // 0xb05240: LoadField: r1 = r0->field_b
    //     0xb05240: ldur            w1, [x0, #0xb]
    // 0xb05244: DecompressPointer r1
    //     0xb05244: add             x1, x1, HEAP, lsl #32
    // 0xb05248: cmp             w1, NULL
    // 0xb0524c: b.ne            #0xb05258
    // 0xb05250: r0 = Null
    //     0xb05250: mov             x0, NULL
    // 0xb05254: b               #0xb0526c
    // 0xb05258: LoadField: r0 = r1->field_f
    //     0xb05258: ldur            w0, [x1, #0xf]
    // 0xb0525c: DecompressPointer r0
    //     0xb0525c: add             x0, x0, HEAP, lsl #32
    // 0xb05260: LoadField: r1 = r0->field_b
    //     0xb05260: ldur            w1, [x0, #0xb]
    // 0xb05264: DecompressPointer r1
    //     0xb05264: add             x1, x1, HEAP, lsl #32
    // 0xb05268: LoadField: r0 = r1->field_b
    //     0xb05268: ldur            w0, [x1, #0xb]
    // 0xb0526c: cmp             w0, NULL
    // 0xb05270: b.ne            #0xb0527c
    // 0xb05274: r6 = 0
    //     0xb05274: movz            x6, #0
    // 0xb05278: b               #0xb05284
    // 0xb0527c: r1 = LoadInt32Instr(r0)
    //     0xb0527c: sbfx            x1, x0, #1, #0x1f
    // 0xb05280: mov             x6, x1
    // 0xb05284: ldur            x0, [fp, #-0x28]
    // 0xb05288: ldur            x2, [fp, #-0x18]
    // 0xb0528c: stur            x6, [fp, #-0x50]
    // 0xb05290: r1 = Function '<anonymous closure>':.
    //     0xb05290: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e68] AnonymousClosure: (0xa7971c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xb05294: ldr             x1, [x1, #0xe68]
    // 0xb05298: r0 = AllocateClosure()
    //     0xb05298: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0529c: r1 = Function '<anonymous closure>':.
    //     0xb0529c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e70] AnonymousClosure: (0xa79710), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xb052a0: ldr             x1, [x1, #0xe70]
    // 0xb052a4: r2 = Null
    //     0xb052a4: mov             x2, NULL
    // 0xb052a8: stur            x0, [fp, #-8]
    // 0xb052ac: r0 = AllocateClosure()
    //     0xb052ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb052b0: r1 = Function '<anonymous closure>':.
    //     0xb052b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e78] AnonymousClosure: (0xa796c4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/know_more_bottom_sheet.dart] _KnowMoreBottomSheetState::build (0xbff404)
    //     0xb052b4: ldr             x1, [x1, #0xe78]
    // 0xb052b8: r2 = Null
    //     0xb052b8: mov             x2, NULL
    // 0xb052bc: stur            x0, [fp, #-0x18]
    // 0xb052c0: r0 = AllocateClosure()
    //     0xb052c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb052c4: mov             x2, x0
    // 0xb052c8: ldur            x3, [fp, #-8]
    // 0xb052cc: ldur            x5, [fp, #-0x18]
    // 0xb052d0: ldur            x6, [fp, #-0x50]
    // 0xb052d4: r1 = Null
    //     0xb052d4: mov             x1, NULL
    // 0xb052d8: r0 = TimelineTileBuilder.connected()
    //     0xb052d8: bl              #0x9dfab0  ; [package:timelines_plus/src/timeline_tile_builder.dart] TimelineTileBuilder::TimelineTileBuilder.connected
    // 0xb052dc: mov             x2, x0
    // 0xb052e0: ldur            x3, [fp, #-0x20]
    // 0xb052e4: r1 = Null
    //     0xb052e4: mov             x1, NULL
    // 0xb052e8: r0 = Timeline.tileBuilder()
    //     0xb052e8: bl              #0x9df894  ; [package:timelines_plus/src/timelines.dart] Timeline::Timeline.tileBuilder
    // 0xb052ec: ldur            x1, [fp, #-0x28]
    // 0xb052f0: ArrayStore: r1[7] = r0  ; List_4
    //     0xb052f0: add             x25, x1, #0x2b
    //     0xb052f4: str             w0, [x25]
    //     0xb052f8: tbz             w0, #0, #0xb05314
    //     0xb052fc: ldurb           w16, [x1, #-1]
    //     0xb05300: ldurb           w17, [x0, #-1]
    //     0xb05304: and             x16, x17, x16, lsr #2
    //     0xb05308: tst             x16, HEAP, lsr #32
    //     0xb0530c: b.eq            #0xb05314
    //     0xb05310: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb05314: ldur            x1, [fp, #-0x28]
    // 0xb05318: r16 = Instance_SizedBox
    //     0xb05318: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb0531c: ldr             x16, [x16, #0x8b8]
    // 0xb05320: StoreField: r1->field_2f = r16
    //     0xb05320: stur            w16, [x1, #0x2f]
    // 0xb05324: r0 = GetNavigation.size()
    //     0xb05324: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb05328: LoadField: d0 = r0->field_7
    //     0xb05328: ldur            d0, [x0, #7]
    // 0xb0532c: stur            d0, [fp, #-0x58]
    // 0xb05330: r16 = <EdgeInsets>
    //     0xb05330: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb05334: ldr             x16, [x16, #0xda0]
    // 0xb05338: r30 = Instance_EdgeInsets
    //     0xb05338: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb0533c: ldr             lr, [lr, #0x1f0]
    // 0xb05340: stp             lr, x16, [SP]
    // 0xb05344: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb05344: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb05348: r0 = all()
    //     0xb05348: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb0534c: ldur            x1, [fp, #-0x10]
    // 0xb05350: stur            x0, [fp, #-8]
    // 0xb05354: r0 = of()
    //     0xb05354: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb05358: LoadField: r1 = r0->field_5b
    //     0xb05358: ldur            w1, [x0, #0x5b]
    // 0xb0535c: DecompressPointer r1
    //     0xb0535c: add             x1, x1, HEAP, lsl #32
    // 0xb05360: r16 = <Color>
    //     0xb05360: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb05364: ldr             x16, [x16, #0xf80]
    // 0xb05368: stp             x1, x16, [SP]
    // 0xb0536c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0536c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb05370: r0 = all()
    //     0xb05370: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb05374: stur            x0, [fp, #-0x18]
    // 0xb05378: r16 = <RoundedRectangleBorder>
    //     0xb05378: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb0537c: ldr             x16, [x16, #0xf78]
    // 0xb05380: r30 = Instance_RoundedRectangleBorder
    //     0xb05380: add             lr, PP, #0x57, lsl #12  ; [pp+0x57e30] Obj!RoundedRectangleBorder@d5ac21
    //     0xb05384: ldr             lr, [lr, #0xe30]
    // 0xb05388: stp             lr, x16, [SP]
    // 0xb0538c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0538c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb05390: r0 = all()
    //     0xb05390: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb05394: stur            x0, [fp, #-0x20]
    // 0xb05398: r0 = ButtonStyle()
    //     0xb05398: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb0539c: mov             x1, x0
    // 0xb053a0: ldur            x0, [fp, #-0x18]
    // 0xb053a4: stur            x1, [fp, #-0x30]
    // 0xb053a8: StoreField: r1->field_b = r0
    //     0xb053a8: stur            w0, [x1, #0xb]
    // 0xb053ac: ldur            x0, [fp, #-8]
    // 0xb053b0: StoreField: r1->field_23 = r0
    //     0xb053b0: stur            w0, [x1, #0x23]
    // 0xb053b4: ldur            x0, [fp, #-0x20]
    // 0xb053b8: StoreField: r1->field_43 = r0
    //     0xb053b8: stur            w0, [x1, #0x43]
    // 0xb053bc: r0 = TextButtonThemeData()
    //     0xb053bc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb053c0: mov             x1, x0
    // 0xb053c4: ldur            x0, [fp, #-0x30]
    // 0xb053c8: stur            x1, [fp, #-8]
    // 0xb053cc: StoreField: r1->field_7 = r0
    //     0xb053cc: stur            w0, [x1, #7]
    // 0xb053d0: r0 = Radius()
    //     0xb053d0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb053d4: d0 = 18.000000
    //     0xb053d4: fmov            d0, #18.00000000
    // 0xb053d8: stur            x0, [fp, #-0x18]
    // 0xb053dc: StoreField: r0->field_7 = d0
    //     0xb053dc: stur            d0, [x0, #7]
    // 0xb053e0: StoreField: r0->field_f = d0
    //     0xb053e0: stur            d0, [x0, #0xf]
    // 0xb053e4: r0 = BorderRadius()
    //     0xb053e4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb053e8: mov             x1, x0
    // 0xb053ec: ldur            x0, [fp, #-0x18]
    // 0xb053f0: stur            x1, [fp, #-0x20]
    // 0xb053f4: StoreField: r1->field_7 = r0
    //     0xb053f4: stur            w0, [x1, #7]
    // 0xb053f8: StoreField: r1->field_b = r0
    //     0xb053f8: stur            w0, [x1, #0xb]
    // 0xb053fc: StoreField: r1->field_f = r0
    //     0xb053fc: stur            w0, [x1, #0xf]
    // 0xb05400: StoreField: r1->field_13 = r0
    //     0xb05400: stur            w0, [x1, #0x13]
    // 0xb05404: r0 = RoundedRectangleBorder()
    //     0xb05404: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb05408: mov             x1, x0
    // 0xb0540c: ldur            x0, [fp, #-0x20]
    // 0xb05410: StoreField: r1->field_b = r0
    //     0xb05410: stur            w0, [x1, #0xb]
    // 0xb05414: r0 = Instance_BorderSide
    //     0xb05414: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb05418: ldr             x0, [x0, #0xe20]
    // 0xb0541c: StoreField: r1->field_7 = r0
    //     0xb0541c: stur            w0, [x1, #7]
    // 0xb05420: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb05420: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb05424: r0 = styleFrom()
    //     0xb05424: bl              #0xb056cc  ; [package:flutter/src/material/text_button.dart] TextButton::styleFrom
    // 0xb05428: ldur            x1, [fp, #-0x10]
    // 0xb0542c: stur            x0, [fp, #-0x10]
    // 0xb05430: r0 = of()
    //     0xb05430: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb05434: LoadField: r1 = r0->field_87
    //     0xb05434: ldur            w1, [x0, #0x87]
    // 0xb05438: DecompressPointer r1
    //     0xb05438: add             x1, x1, HEAP, lsl #32
    // 0xb0543c: LoadField: r0 = r1->field_7
    //     0xb0543c: ldur            w0, [x1, #7]
    // 0xb05440: DecompressPointer r0
    //     0xb05440: add             x0, x0, HEAP, lsl #32
    // 0xb05444: r16 = 16.000000
    //     0xb05444: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb05448: ldr             x16, [x16, #0x188]
    // 0xb0544c: r30 = Instance_Color
    //     0xb0544c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb05450: stp             lr, x16, [SP]
    // 0xb05454: mov             x1, x0
    // 0xb05458: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb05458: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb0545c: ldr             x4, [x4, #0xaa0]
    // 0xb05460: r0 = copyWith()
    //     0xb05460: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb05464: stur            x0, [fp, #-0x18]
    // 0xb05468: r0 = Text()
    //     0xb05468: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0546c: mov             x3, x0
    // 0xb05470: r0 = "Ok, Go Back"
    //     0xb05470: add             x0, PP, #0x57, lsl #12  ; [pp+0x57e80] "Ok, Go Back"
    //     0xb05474: ldr             x0, [x0, #0xe80]
    // 0xb05478: stur            x3, [fp, #-0x20]
    // 0xb0547c: StoreField: r3->field_b = r0
    //     0xb0547c: stur            w0, [x3, #0xb]
    // 0xb05480: ldur            x0, [fp, #-0x18]
    // 0xb05484: StoreField: r3->field_13 = r0
    //     0xb05484: stur            w0, [x3, #0x13]
    // 0xb05488: r1 = Function '<anonymous closure>':.
    //     0xb05488: add             x1, PP, #0x57, lsl #12  ; [pp+0x57e88] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb0548c: ldr             x1, [x1, #0xe88]
    // 0xb05490: r2 = Null
    //     0xb05490: mov             x2, NULL
    // 0xb05494: r0 = AllocateClosure()
    //     0xb05494: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb05498: stur            x0, [fp, #-0x18]
    // 0xb0549c: r0 = TextButton()
    //     0xb0549c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb054a0: mov             x1, x0
    // 0xb054a4: ldur            x0, [fp, #-0x18]
    // 0xb054a8: stur            x1, [fp, #-0x30]
    // 0xb054ac: StoreField: r1->field_b = r0
    //     0xb054ac: stur            w0, [x1, #0xb]
    // 0xb054b0: ldur            x0, [fp, #-0x10]
    // 0xb054b4: StoreField: r1->field_1b = r0
    //     0xb054b4: stur            w0, [x1, #0x1b]
    // 0xb054b8: r0 = false
    //     0xb054b8: add             x0, NULL, #0x30  ; false
    // 0xb054bc: StoreField: r1->field_27 = r0
    //     0xb054bc: stur            w0, [x1, #0x27]
    // 0xb054c0: r2 = true
    //     0xb054c0: add             x2, NULL, #0x20  ; true
    // 0xb054c4: StoreField: r1->field_2f = r2
    //     0xb054c4: stur            w2, [x1, #0x2f]
    // 0xb054c8: ldur            x2, [fp, #-0x20]
    // 0xb054cc: StoreField: r1->field_37 = r2
    //     0xb054cc: stur            w2, [x1, #0x37]
    // 0xb054d0: r0 = TextButtonTheme()
    //     0xb054d0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb054d4: mov             x1, x0
    // 0xb054d8: ldur            x0, [fp, #-8]
    // 0xb054dc: stur            x1, [fp, #-0x10]
    // 0xb054e0: StoreField: r1->field_f = r0
    //     0xb054e0: stur            w0, [x1, #0xf]
    // 0xb054e4: ldur            x0, [fp, #-0x30]
    // 0xb054e8: StoreField: r1->field_b = r0
    //     0xb054e8: stur            w0, [x1, #0xb]
    // 0xb054ec: ldur            d0, [fp, #-0x58]
    // 0xb054f0: r0 = inline_Allocate_Double()
    //     0xb054f0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb054f4: add             x0, x0, #0x10
    //     0xb054f8: cmp             x2, x0
    //     0xb054fc: b.ls            #0xb05690
    //     0xb05500: str             x0, [THR, #0x50]  ; THR::top
    //     0xb05504: sub             x0, x0, #0xf
    //     0xb05508: movz            x2, #0xe15c
    //     0xb0550c: movk            x2, #0x3, lsl #16
    //     0xb05510: stur            x2, [x0, #-1]
    // 0xb05514: StoreField: r0->field_7 = d0
    //     0xb05514: stur            d0, [x0, #7]
    // 0xb05518: stur            x0, [fp, #-8]
    // 0xb0551c: r0 = SizedBox()
    //     0xb0551c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb05520: mov             x1, x0
    // 0xb05524: ldur            x0, [fp, #-8]
    // 0xb05528: StoreField: r1->field_f = r0
    //     0xb05528: stur            w0, [x1, #0xf]
    // 0xb0552c: ldur            x0, [fp, #-0x10]
    // 0xb05530: StoreField: r1->field_b = r0
    //     0xb05530: stur            w0, [x1, #0xb]
    // 0xb05534: mov             x0, x1
    // 0xb05538: ldur            x1, [fp, #-0x28]
    // 0xb0553c: ArrayStore: r1[9] = r0  ; List_4
    //     0xb0553c: add             x25, x1, #0x33
    //     0xb05540: str             w0, [x25]
    //     0xb05544: tbz             w0, #0, #0xb05560
    //     0xb05548: ldurb           w16, [x1, #-1]
    //     0xb0554c: ldurb           w17, [x0, #-1]
    //     0xb05550: and             x16, x17, x16, lsr #2
    //     0xb05554: tst             x16, HEAP, lsr #32
    //     0xb05558: b.eq            #0xb05560
    //     0xb0555c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb05560: r1 = <Widget>
    //     0xb05560: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb05564: r0 = AllocateGrowableArray()
    //     0xb05564: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb05568: mov             x1, x0
    // 0xb0556c: ldur            x0, [fp, #-0x28]
    // 0xb05570: stur            x1, [fp, #-8]
    // 0xb05574: StoreField: r1->field_f = r0
    //     0xb05574: stur            w0, [x1, #0xf]
    // 0xb05578: r0 = 20
    //     0xb05578: movz            x0, #0x14
    // 0xb0557c: StoreField: r1->field_b = r0
    //     0xb0557c: stur            w0, [x1, #0xb]
    // 0xb05580: r0 = Column()
    //     0xb05580: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb05584: mov             x1, x0
    // 0xb05588: r0 = Instance_Axis
    //     0xb05588: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb0558c: stur            x1, [fp, #-0x10]
    // 0xb05590: StoreField: r1->field_f = r0
    //     0xb05590: stur            w0, [x1, #0xf]
    // 0xb05594: r2 = Instance_MainAxisAlignment
    //     0xb05594: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb05598: ldr             x2, [x2, #0xa08]
    // 0xb0559c: StoreField: r1->field_13 = r2
    //     0xb0559c: stur            w2, [x1, #0x13]
    // 0xb055a0: r2 = Instance_MainAxisSize
    //     0xb055a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb055a4: ldr             x2, [x2, #0xa10]
    // 0xb055a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb055a8: stur            w2, [x1, #0x17]
    // 0xb055ac: r2 = Instance_CrossAxisAlignment
    //     0xb055ac: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb055b0: ldr             x2, [x2, #0x890]
    // 0xb055b4: StoreField: r1->field_1b = r2
    //     0xb055b4: stur            w2, [x1, #0x1b]
    // 0xb055b8: r2 = Instance_VerticalDirection
    //     0xb055b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb055bc: ldr             x2, [x2, #0xa20]
    // 0xb055c0: StoreField: r1->field_23 = r2
    //     0xb055c0: stur            w2, [x1, #0x23]
    // 0xb055c4: r2 = Instance_Clip
    //     0xb055c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb055c8: ldr             x2, [x2, #0x38]
    // 0xb055cc: StoreField: r1->field_2b = r2
    //     0xb055cc: stur            w2, [x1, #0x2b]
    // 0xb055d0: StoreField: r1->field_2f = rZR
    //     0xb055d0: stur            xzr, [x1, #0x2f]
    // 0xb055d4: ldur            x2, [fp, #-8]
    // 0xb055d8: StoreField: r1->field_b = r2
    //     0xb055d8: stur            w2, [x1, #0xb]
    // 0xb055dc: r0 = SingleChildScrollView()
    //     0xb055dc: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb055e0: mov             x1, x0
    // 0xb055e4: r0 = Instance_Axis
    //     0xb055e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb055e8: stur            x1, [fp, #-8]
    // 0xb055ec: StoreField: r1->field_b = r0
    //     0xb055ec: stur            w0, [x1, #0xb]
    // 0xb055f0: r0 = false
    //     0xb055f0: add             x0, NULL, #0x30  ; false
    // 0xb055f4: StoreField: r1->field_f = r0
    //     0xb055f4: stur            w0, [x1, #0xf]
    // 0xb055f8: r0 = Instance_EdgeInsets
    //     0xb055f8: add             x0, PP, #0x55, lsl #12  ; [pp+0x558e8] Obj!EdgeInsets@d58fa1
    //     0xb055fc: ldr             x0, [x0, #0x8e8]
    // 0xb05600: StoreField: r1->field_13 = r0
    //     0xb05600: stur            w0, [x1, #0x13]
    // 0xb05604: ldur            x0, [fp, #-0x10]
    // 0xb05608: StoreField: r1->field_23 = r0
    //     0xb05608: stur            w0, [x1, #0x23]
    // 0xb0560c: r0 = Instance_DragStartBehavior
    //     0xb0560c: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb05610: StoreField: r1->field_27 = r0
    //     0xb05610: stur            w0, [x1, #0x27]
    // 0xb05614: r0 = Instance_Clip
    //     0xb05614: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb05618: ldr             x0, [x0, #0x7e0]
    // 0xb0561c: StoreField: r1->field_2b = r0
    //     0xb0561c: stur            w0, [x1, #0x2b]
    // 0xb05620: r0 = Instance_HitTestBehavior
    //     0xb05620: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb05624: ldr             x0, [x0, #0x288]
    // 0xb05628: StoreField: r1->field_2f = r0
    //     0xb05628: stur            w0, [x1, #0x2f]
    // 0xb0562c: r0 = ColoredBox()
    //     0xb0562c: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb05630: r1 = Instance_Color
    //     0xb05630: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb05634: ldr             x1, [x1, #0x90]
    // 0xb05638: StoreField: r0->field_f = r1
    //     0xb05638: stur            w1, [x0, #0xf]
    // 0xb0563c: ldur            x1, [fp, #-8]
    // 0xb05640: StoreField: r0->field_b = r1
    //     0xb05640: stur            w1, [x0, #0xb]
    // 0xb05644: LeaveFrame
    //     0xb05644: mov             SP, fp
    //     0xb05648: ldp             fp, lr, [SP], #0x10
    // 0xb0564c: ret
    //     0xb0564c: ret             
    // 0xb05650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb05650: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb05654: b               #0xb04574
    // 0xb05658: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb05658: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0565c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0565c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb05660: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb05660: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb05664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb05664: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb05668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb05668: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0566c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0566c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb05670: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb05670: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb05674: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb05674: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb05678: SaveReg d0
    //     0xb05678: str             q0, [SP, #-0x10]!
    // 0xb0567c: r0 = AllocateDouble()
    //     0xb0567c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb05680: RestoreReg d0
    //     0xb05680: ldr             q0, [SP], #0x10
    // 0xb05684: b               #0xb050a8
    // 0xb05688: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb05688: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0568c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0568c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb05690: SaveReg d0
    //     0xb05690: str             q0, [SP, #-0x10]!
    // 0xb05694: SaveReg r1
    //     0xb05694: str             x1, [SP, #-8]!
    // 0xb05698: r0 = AllocateDouble()
    //     0xb05698: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb0569c: RestoreReg r1
    //     0xb0569c: ldr             x1, [SP], #8
    // 0xb056a0: RestoreReg d0
    //     0xb056a0: ldr             q0, [SP], #0x10
    // 0xb056a4: b               #0xb05514
  }
}

// class id: 4145, size: 0x10, field offset: 0xc
//   const constructor, 
class KnowMoreBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dee4, size: 0x24
    // 0xc7dee4: EnterFrame
    //     0xc7dee4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dee8: mov             fp, SP
    // 0xc7deec: mov             x0, x1
    // 0xc7def0: r1 = <KnowMoreBottomSheet>
    //     0xc7def0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b50] TypeArguments: <KnowMoreBottomSheet>
    //     0xc7def4: ldr             x1, [x1, #0xb50]
    // 0xc7def8: r0 = _KnowMoreBottomSheetState()
    //     0xc7def8: bl              #0xc7df08  ; Allocate_KnowMoreBottomSheetStateStub -> _KnowMoreBottomSheetState (size=0x14)
    // 0xc7defc: LeaveFrame
    //     0xc7defc: mov             SP, fp
    //     0xc7df00: ldp             fp, lr, [SP], #0x10
    // 0xc7df04: ret
    //     0xc7df04: ret             
  }
}
