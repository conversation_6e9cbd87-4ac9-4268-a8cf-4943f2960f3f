// lib: , url: package:customer_app/app/presentation/views/cosmetic/testimonials/testimonials_view.dart

// class id: 1049338, size: 0x8
class :: {
}

// class id: 4582, size: 0x14, field offset: 0x14
//   const constructor, 
class TestimonialsView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d2758, size: 0x10c
    // 0x14d2758: EnterFrame
    //     0x14d2758: stp             fp, lr, [SP, #-0x10]!
    //     0x14d275c: mov             fp, SP
    // 0x14d2760: AllocStack(0x18)
    //     0x14d2760: sub             SP, SP, #0x18
    // 0x14d2764: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d2764: stur            x1, [fp, #-8]
    //     0x14d2768: stur            x2, [fp, #-0x10]
    // 0x14d276c: CheckStackOverflow
    //     0x14d276c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d2770: cmp             SP, x16
    //     0x14d2774: b.ls            #0x14d285c
    // 0x14d2778: r1 = 2
    //     0x14d2778: movz            x1, #0x2
    // 0x14d277c: r0 = AllocateContext()
    //     0x14d277c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d2780: mov             x1, x0
    // 0x14d2784: ldur            x0, [fp, #-8]
    // 0x14d2788: stur            x1, [fp, #-0x18]
    // 0x14d278c: StoreField: r1->field_f = r0
    //     0x14d278c: stur            w0, [x1, #0xf]
    // 0x14d2790: ldur            x0, [fp, #-0x10]
    // 0x14d2794: StoreField: r1->field_13 = r0
    //     0x14d2794: stur            w0, [x1, #0x13]
    // 0x14d2798: r0 = Obx()
    //     0x14d2798: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d279c: ldur            x2, [fp, #-0x18]
    // 0x14d27a0: r1 = Function '<anonymous closure>':.
    //     0x14d27a0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b68] AnonymousClosure: (0x14d2864), in [package:customer_app/app/presentation/views/cosmetic/testimonials/testimonials_view.dart] TestimonialsView::body (0x14d2758)
    //     0x14d27a4: ldr             x1, [x1, #0xb68]
    // 0x14d27a8: stur            x0, [fp, #-8]
    // 0x14d27ac: r0 = AllocateClosure()
    //     0x14d27ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d27b0: mov             x1, x0
    // 0x14d27b4: ldur            x0, [fp, #-8]
    // 0x14d27b8: StoreField: r0->field_b = r1
    //     0x14d27b8: stur            w1, [x0, #0xb]
    // 0x14d27bc: r0 = Padding()
    //     0x14d27bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d27c0: mov             x3, x0
    // 0x14d27c4: r0 = Instance_EdgeInsets
    //     0x14d27c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14d27c8: ldr             x0, [x0, #0x980]
    // 0x14d27cc: stur            x3, [fp, #-0x10]
    // 0x14d27d0: StoreField: r3->field_f = r0
    //     0x14d27d0: stur            w0, [x3, #0xf]
    // 0x14d27d4: ldur            x0, [fp, #-8]
    // 0x14d27d8: StoreField: r3->field_b = r0
    //     0x14d27d8: stur            w0, [x3, #0xb]
    // 0x14d27dc: ldur            x2, [fp, #-0x18]
    // 0x14d27e0: r1 = Function '<anonymous closure>':.
    //     0x14d27e0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b70] AnonymousClosure: (0x147bdfc), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x14d27e4: ldr             x1, [x1, #0xb70]
    // 0x14d27e8: r0 = AllocateClosure()
    //     0x14d27e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d27ec: ldur            x2, [fp, #-0x18]
    // 0x14d27f0: r1 = Function '<anonymous closure>':.
    //     0x14d27f0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b78] AnonymousClosure: (0x147a04c), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x14d27f4: ldr             x1, [x1, #0xb78]
    // 0x14d27f8: stur            x0, [fp, #-8]
    // 0x14d27fc: r0 = AllocateClosure()
    //     0x14d27fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2800: stur            x0, [fp, #-0x18]
    // 0x14d2804: r0 = PagingView()
    //     0x14d2804: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14d2808: mov             x1, x0
    // 0x14d280c: ldur            x2, [fp, #-0x10]
    // 0x14d2810: ldur            x3, [fp, #-0x18]
    // 0x14d2814: ldur            x5, [fp, #-8]
    // 0x14d2818: stur            x0, [fp, #-8]
    // 0x14d281c: r0 = PagingView()
    //     0x14d281c: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14d2820: r0 = WillPopScope()
    //     0x14d2820: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d2824: mov             x3, x0
    // 0x14d2828: ldur            x0, [fp, #-8]
    // 0x14d282c: stur            x3, [fp, #-0x10]
    // 0x14d2830: StoreField: r3->field_b = r0
    //     0x14d2830: stur            w0, [x3, #0xb]
    // 0x14d2834: r1 = Function '<anonymous closure>':.
    //     0x14d2834: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b80] AnonymousClosure: (0x1479fd8), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x14d2838: ldr             x1, [x1, #0xb80]
    // 0x14d283c: r2 = Null
    //     0x14d283c: mov             x2, NULL
    // 0x14d2840: r0 = AllocateClosure()
    //     0x14d2840: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2844: mov             x1, x0
    // 0x14d2848: ldur            x0, [fp, #-0x10]
    // 0x14d284c: StoreField: r0->field_f = r1
    //     0x14d284c: stur            w1, [x0, #0xf]
    // 0x14d2850: LeaveFrame
    //     0x14d2850: mov             SP, fp
    //     0x14d2854: ldp             fp, lr, [SP], #0x10
    // 0x14d2858: ret
    //     0x14d2858: ret             
    // 0x14d285c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d285c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d2860: b               #0x14d2778
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14d2864, size: 0x5fc
    // 0x14d2864: EnterFrame
    //     0x14d2864: stp             fp, lr, [SP, #-0x10]!
    //     0x14d2868: mov             fp, SP
    // 0x14d286c: AllocStack(0x50)
    //     0x14d286c: sub             SP, SP, #0x50
    // 0x14d2870: SetupParameters()
    //     0x14d2870: ldr             x0, [fp, #0x10]
    //     0x14d2874: ldur            w2, [x0, #0x17]
    //     0x14d2878: add             x2, x2, HEAP, lsl #32
    //     0x14d287c: stur            x2, [fp, #-8]
    // 0x14d2880: CheckStackOverflow
    //     0x14d2880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d2884: cmp             SP, x16
    //     0x14d2888: b.ls            #0x14d2e58
    // 0x14d288c: LoadField: r1 = r2->field_f
    //     0x14d288c: ldur            w1, [x2, #0xf]
    // 0x14d2890: DecompressPointer r1
    //     0x14d2890: add             x1, x1, HEAP, lsl #32
    // 0x14d2894: r0 = controller()
    //     0x14d2894: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2898: LoadField: r1 = r0->field_53
    //     0x14d2898: ldur            w1, [x0, #0x53]
    // 0x14d289c: DecompressPointer r1
    //     0x14d289c: add             x1, x1, HEAP, lsl #32
    // 0x14d28a0: r0 = value()
    //     0x14d28a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d28a4: LoadField: r1 = r0->field_b
    //     0x14d28a4: ldur            w1, [x0, #0xb]
    // 0x14d28a8: DecompressPointer r1
    //     0x14d28a8: add             x1, x1, HEAP, lsl #32
    // 0x14d28ac: cmp             w1, NULL
    // 0x14d28b0: b.ne            #0x14d28c0
    // 0x14d28b4: r0 = Instance_Center
    //     0x14d28b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9a8] Obj!Center@d68361
    //     0x14d28b8: ldr             x0, [x0, #0x9a8]
    // 0x14d28bc: b               #0x14d2e4c
    // 0x14d28c0: ldur            x0, [fp, #-8]
    // 0x14d28c4: LoadField: r1 = r0->field_13
    //     0x14d28c4: ldur            w1, [x0, #0x13]
    // 0x14d28c8: DecompressPointer r1
    //     0x14d28c8: add             x1, x1, HEAP, lsl #32
    // 0x14d28cc: r0 = of()
    //     0x14d28cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d28d0: LoadField: r1 = r0->field_87
    //     0x14d28d0: ldur            w1, [x0, #0x87]
    // 0x14d28d4: DecompressPointer r1
    //     0x14d28d4: add             x1, x1, HEAP, lsl #32
    // 0x14d28d8: LoadField: r0 = r1->field_7
    //     0x14d28d8: ldur            w0, [x1, #7]
    // 0x14d28dc: DecompressPointer r0
    //     0x14d28dc: add             x0, x0, HEAP, lsl #32
    // 0x14d28e0: r16 = Instance_Color
    //     0x14d28e0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d28e4: r30 = 21.000000
    //     0x14d28e4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x14d28e8: ldr             lr, [lr, #0x9b0]
    // 0x14d28ec: stp             lr, x16, [SP]
    // 0x14d28f0: mov             x1, x0
    // 0x14d28f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d28f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d28f8: ldr             x4, [x4, #0x9b8]
    // 0x14d28fc: r0 = copyWith()
    //     0x14d28fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d2900: stur            x0, [fp, #-0x10]
    // 0x14d2904: r0 = Text()
    //     0x14d2904: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d2908: mov             x2, x0
    // 0x14d290c: r0 = "Customers Love Us"
    //     0x14d290c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c0] "Customers Love Us"
    //     0x14d2910: ldr             x0, [x0, #0x9c0]
    // 0x14d2914: stur            x2, [fp, #-0x18]
    // 0x14d2918: StoreField: r2->field_b = r0
    //     0x14d2918: stur            w0, [x2, #0xb]
    // 0x14d291c: ldur            x0, [fp, #-0x10]
    // 0x14d2920: StoreField: r2->field_13 = r0
    //     0x14d2920: stur            w0, [x2, #0x13]
    // 0x14d2924: ldur            x0, [fp, #-8]
    // 0x14d2928: LoadField: r1 = r0->field_13
    //     0x14d2928: ldur            w1, [x0, #0x13]
    // 0x14d292c: DecompressPointer r1
    //     0x14d292c: add             x1, x1, HEAP, lsl #32
    // 0x14d2930: r0 = of()
    //     0x14d2930: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d2934: LoadField: r1 = r0->field_87
    //     0x14d2934: ldur            w1, [x0, #0x87]
    // 0x14d2938: DecompressPointer r1
    //     0x14d2938: add             x1, x1, HEAP, lsl #32
    // 0x14d293c: LoadField: r0 = r1->field_7
    //     0x14d293c: ldur            w0, [x1, #7]
    // 0x14d2940: DecompressPointer r0
    //     0x14d2940: add             x0, x0, HEAP, lsl #32
    // 0x14d2944: ldur            x2, [fp, #-8]
    // 0x14d2948: stur            x0, [fp, #-0x10]
    // 0x14d294c: LoadField: r1 = r2->field_13
    //     0x14d294c: ldur            w1, [x2, #0x13]
    // 0x14d2950: DecompressPointer r1
    //     0x14d2950: add             x1, x1, HEAP, lsl #32
    // 0x14d2954: r0 = of()
    //     0x14d2954: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d2958: LoadField: r1 = r0->field_5b
    //     0x14d2958: ldur            w1, [x0, #0x5b]
    // 0x14d295c: DecompressPointer r1
    //     0x14d295c: add             x1, x1, HEAP, lsl #32
    // 0x14d2960: r16 = 21.000000
    //     0x14d2960: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x14d2964: ldr             x16, [x16, #0x9b0]
    // 0x14d2968: stp             x16, x1, [SP]
    // 0x14d296c: ldur            x1, [fp, #-0x10]
    // 0x14d2970: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d2970: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d2974: ldr             x4, [x4, #0x9b8]
    // 0x14d2978: r0 = copyWith()
    //     0x14d2978: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d297c: stur            x0, [fp, #-0x10]
    // 0x14d2980: r0 = Text()
    //     0x14d2980: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d2984: mov             x2, x0
    // 0x14d2988: r0 = "—"
    //     0x14d2988: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c8] "—"
    //     0x14d298c: ldr             x0, [x0, #0x9c8]
    // 0x14d2990: stur            x2, [fp, #-0x20]
    // 0x14d2994: StoreField: r2->field_b = r0
    //     0x14d2994: stur            w0, [x2, #0xb]
    // 0x14d2998: ldur            x0, [fp, #-0x10]
    // 0x14d299c: StoreField: r2->field_13 = r0
    //     0x14d299c: stur            w0, [x2, #0x13]
    // 0x14d29a0: r1 = Instance_Color
    //     0x14d29a0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d29a4: d0 = 0.100000
    //     0x14d29a4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14d29a8: r0 = withOpacity()
    //     0x14d29a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d29ac: stur            x0, [fp, #-0x10]
    // 0x14d29b0: r0 = Divider()
    //     0x14d29b0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x14d29b4: mov             x3, x0
    // 0x14d29b8: ldur            x0, [fp, #-0x10]
    // 0x14d29bc: stur            x3, [fp, #-0x28]
    // 0x14d29c0: StoreField: r3->field_1f = r0
    //     0x14d29c0: stur            w0, [x3, #0x1f]
    // 0x14d29c4: r1 = Null
    //     0x14d29c4: mov             x1, NULL
    // 0x14d29c8: r2 = 10
    //     0x14d29c8: movz            x2, #0xa
    // 0x14d29cc: r0 = AllocateArray()
    //     0x14d29cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d29d0: stur            x0, [fp, #-0x10]
    // 0x14d29d4: r16 = "Showing "
    //     0x14d29d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d0] "Showing "
    //     0x14d29d8: ldr             x16, [x16, #0x9d0]
    // 0x14d29dc: StoreField: r0->field_f = r16
    //     0x14d29dc: stur            w16, [x0, #0xf]
    // 0x14d29e0: ldur            x2, [fp, #-8]
    // 0x14d29e4: LoadField: r1 = r2->field_f
    //     0x14d29e4: ldur            w1, [x2, #0xf]
    // 0x14d29e8: DecompressPointer r1
    //     0x14d29e8: add             x1, x1, HEAP, lsl #32
    // 0x14d29ec: r0 = controller()
    //     0x14d29ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d29f0: LoadField: r1 = r0->field_57
    //     0x14d29f0: ldur            w1, [x0, #0x57]
    // 0x14d29f4: DecompressPointer r1
    //     0x14d29f4: add             x1, x1, HEAP, lsl #32
    // 0x14d29f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d29f8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d29fc: r0 = toList()
    //     0x14d29fc: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14d2a00: LoadField: r1 = r0->field_b
    //     0x14d2a00: ldur            w1, [x0, #0xb]
    // 0x14d2a04: ldur            x0, [fp, #-0x10]
    // 0x14d2a08: StoreField: r0->field_13 = r1
    //     0x14d2a08: stur            w1, [x0, #0x13]
    // 0x14d2a0c: r16 = " out of "
    //     0x14d2a0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d8] " out of "
    //     0x14d2a10: ldr             x16, [x16, #0x9d8]
    // 0x14d2a14: ArrayStore: r0[0] = r16  ; List_4
    //     0x14d2a14: stur            w16, [x0, #0x17]
    // 0x14d2a18: ldur            x2, [fp, #-8]
    // 0x14d2a1c: LoadField: r1 = r2->field_f
    //     0x14d2a1c: ldur            w1, [x2, #0xf]
    // 0x14d2a20: DecompressPointer r1
    //     0x14d2a20: add             x1, x1, HEAP, lsl #32
    // 0x14d2a24: r0 = controller()
    //     0x14d2a24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2a28: LoadField: r1 = r0->field_53
    //     0x14d2a28: ldur            w1, [x0, #0x53]
    // 0x14d2a2c: DecompressPointer r1
    //     0x14d2a2c: add             x1, x1, HEAP, lsl #32
    // 0x14d2a30: r0 = value()
    //     0x14d2a30: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d2a34: LoadField: r1 = r0->field_b
    //     0x14d2a34: ldur            w1, [x0, #0xb]
    // 0x14d2a38: DecompressPointer r1
    //     0x14d2a38: add             x1, x1, HEAP, lsl #32
    // 0x14d2a3c: cmp             w1, NULL
    // 0x14d2a40: b.ne            #0x14d2a4c
    // 0x14d2a44: r0 = Null
    //     0x14d2a44: mov             x0, NULL
    // 0x14d2a48: b               #0x14d2a70
    // 0x14d2a4c: LoadField: r0 = r1->field_b
    //     0x14d2a4c: ldur            w0, [x1, #0xb]
    // 0x14d2a50: DecompressPointer r0
    //     0x14d2a50: add             x0, x0, HEAP, lsl #32
    // 0x14d2a54: cmp             w0, NULL
    // 0x14d2a58: b.ne            #0x14d2a64
    // 0x14d2a5c: r0 = Null
    //     0x14d2a5c: mov             x0, NULL
    // 0x14d2a60: b               #0x14d2a70
    // 0x14d2a64: LoadField: r1 = r0->field_f
    //     0x14d2a64: ldur            w1, [x0, #0xf]
    // 0x14d2a68: DecompressPointer r1
    //     0x14d2a68: add             x1, x1, HEAP, lsl #32
    // 0x14d2a6c: mov             x0, x1
    // 0x14d2a70: ldur            x3, [fp, #-8]
    // 0x14d2a74: ldur            x6, [fp, #-0x18]
    // 0x14d2a78: ldur            x5, [fp, #-0x20]
    // 0x14d2a7c: ldur            x4, [fp, #-0x28]
    // 0x14d2a80: ldur            x2, [fp, #-0x10]
    // 0x14d2a84: mov             x1, x2
    // 0x14d2a88: ArrayStore: r1[3] = r0  ; List_4
    //     0x14d2a88: add             x25, x1, #0x1b
    //     0x14d2a8c: str             w0, [x25]
    //     0x14d2a90: tbz             w0, #0, #0x14d2aac
    //     0x14d2a94: ldurb           w16, [x1, #-1]
    //     0x14d2a98: ldurb           w17, [x0, #-1]
    //     0x14d2a9c: and             x16, x17, x16, lsr #2
    //     0x14d2aa0: tst             x16, HEAP, lsr #32
    //     0x14d2aa4: b.eq            #0x14d2aac
    //     0x14d2aa8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d2aac: r16 = " testimonials"
    //     0x14d2aac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e0] " testimonials"
    //     0x14d2ab0: ldr             x16, [x16, #0x9e0]
    // 0x14d2ab4: StoreField: r2->field_1f = r16
    //     0x14d2ab4: stur            w16, [x2, #0x1f]
    // 0x14d2ab8: str             x2, [SP]
    // 0x14d2abc: r0 = _interpolate()
    //     0x14d2abc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14d2ac0: mov             x2, x0
    // 0x14d2ac4: ldur            x0, [fp, #-8]
    // 0x14d2ac8: stur            x2, [fp, #-0x10]
    // 0x14d2acc: LoadField: r1 = r0->field_13
    //     0x14d2acc: ldur            w1, [x0, #0x13]
    // 0x14d2ad0: DecompressPointer r1
    //     0x14d2ad0: add             x1, x1, HEAP, lsl #32
    // 0x14d2ad4: r0 = of()
    //     0x14d2ad4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d2ad8: LoadField: r1 = r0->field_87
    //     0x14d2ad8: ldur            w1, [x0, #0x87]
    // 0x14d2adc: DecompressPointer r1
    //     0x14d2adc: add             x1, x1, HEAP, lsl #32
    // 0x14d2ae0: LoadField: r0 = r1->field_2b
    //     0x14d2ae0: ldur            w0, [x1, #0x2b]
    // 0x14d2ae4: DecompressPointer r0
    //     0x14d2ae4: add             x0, x0, HEAP, lsl #32
    // 0x14d2ae8: stur            x0, [fp, #-0x30]
    // 0x14d2aec: r1 = Instance_Color
    //     0x14d2aec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d2af0: d0 = 0.700000
    //     0x14d2af0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14d2af4: ldr             d0, [x17, #0xf48]
    // 0x14d2af8: r0 = withOpacity()
    //     0x14d2af8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d2afc: r16 = 14.000000
    //     0x14d2afc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d2b00: ldr             x16, [x16, #0x1d8]
    // 0x14d2b04: stp             x16, x0, [SP]
    // 0x14d2b08: ldur            x1, [fp, #-0x30]
    // 0x14d2b0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d2b0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d2b10: ldr             x4, [x4, #0x9b8]
    // 0x14d2b14: r0 = copyWith()
    //     0x14d2b14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d2b18: stur            x0, [fp, #-0x30]
    // 0x14d2b1c: r0 = Text()
    //     0x14d2b1c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d2b20: mov             x3, x0
    // 0x14d2b24: ldur            x0, [fp, #-0x10]
    // 0x14d2b28: stur            x3, [fp, #-0x38]
    // 0x14d2b2c: StoreField: r3->field_b = r0
    //     0x14d2b2c: stur            w0, [x3, #0xb]
    // 0x14d2b30: ldur            x0, [fp, #-0x30]
    // 0x14d2b34: StoreField: r3->field_13 = r0
    //     0x14d2b34: stur            w0, [x3, #0x13]
    // 0x14d2b38: r1 = Null
    //     0x14d2b38: mov             x1, NULL
    // 0x14d2b3c: r2 = 12
    //     0x14d2b3c: movz            x2, #0xc
    // 0x14d2b40: r0 = AllocateArray()
    //     0x14d2b40: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d2b44: mov             x2, x0
    // 0x14d2b48: ldur            x0, [fp, #-0x18]
    // 0x14d2b4c: stur            x2, [fp, #-0x10]
    // 0x14d2b50: StoreField: r2->field_f = r0
    //     0x14d2b50: stur            w0, [x2, #0xf]
    // 0x14d2b54: ldur            x0, [fp, #-0x20]
    // 0x14d2b58: StoreField: r2->field_13 = r0
    //     0x14d2b58: stur            w0, [x2, #0x13]
    // 0x14d2b5c: ldur            x0, [fp, #-0x28]
    // 0x14d2b60: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d2b60: stur            w0, [x2, #0x17]
    // 0x14d2b64: r16 = Instance_SizedBox
    //     0x14d2b64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x14d2b68: ldr             x16, [x16, #0x8f0]
    // 0x14d2b6c: StoreField: r2->field_1b = r16
    //     0x14d2b6c: stur            w16, [x2, #0x1b]
    // 0x14d2b70: ldur            x0, [fp, #-0x38]
    // 0x14d2b74: StoreField: r2->field_1f = r0
    //     0x14d2b74: stur            w0, [x2, #0x1f]
    // 0x14d2b78: r16 = Instance_SizedBox
    //     0x14d2b78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14d2b7c: ldr             x16, [x16, #0x9f0]
    // 0x14d2b80: StoreField: r2->field_23 = r16
    //     0x14d2b80: stur            w16, [x2, #0x23]
    // 0x14d2b84: r1 = <Widget>
    //     0x14d2b84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d2b88: r0 = AllocateGrowableArray()
    //     0x14d2b88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d2b8c: mov             x2, x0
    // 0x14d2b90: ldur            x0, [fp, #-0x10]
    // 0x14d2b94: stur            x2, [fp, #-0x18]
    // 0x14d2b98: StoreField: r2->field_f = r0
    //     0x14d2b98: stur            w0, [x2, #0xf]
    // 0x14d2b9c: r0 = 12
    //     0x14d2b9c: movz            x0, #0xc
    // 0x14d2ba0: StoreField: r2->field_b = r0
    //     0x14d2ba0: stur            w0, [x2, #0xb]
    // 0x14d2ba4: ldur            x0, [fp, #-8]
    // 0x14d2ba8: LoadField: r1 = r0->field_f
    //     0x14d2ba8: ldur            w1, [x0, #0xf]
    // 0x14d2bac: DecompressPointer r1
    //     0x14d2bac: add             x1, x1, HEAP, lsl #32
    // 0x14d2bb0: r0 = controller()
    //     0x14d2bb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2bb4: LoadField: r1 = r0->field_67
    //     0x14d2bb4: ldur            w1, [x0, #0x67]
    // 0x14d2bb8: DecompressPointer r1
    //     0x14d2bb8: add             x1, x1, HEAP, lsl #32
    // 0x14d2bbc: r0 = LoadClassIdInstr(r1)
    //     0x14d2bbc: ldur            x0, [x1, #-1]
    //     0x14d2bc0: ubfx            x0, x0, #0xc, #0x14
    // 0x14d2bc4: r16 = "testimonial"
    //     0x14d2bc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f8] "testimonial"
    //     0x14d2bc8: ldr             x16, [x16, #0x9f8]
    // 0x14d2bcc: stp             x16, x1, [SP]
    // 0x14d2bd0: mov             lr, x0
    // 0x14d2bd4: ldr             lr, [x21, lr, lsl #3]
    // 0x14d2bd8: blr             lr
    // 0x14d2bdc: tbnz            w0, #4, #0x14d2ce8
    // 0x14d2be0: ldur            x2, [fp, #-8]
    // 0x14d2be4: ldur            x0, [fp, #-0x18]
    // 0x14d2be8: LoadField: r1 = r2->field_f
    //     0x14d2be8: ldur            w1, [x2, #0xf]
    // 0x14d2bec: DecompressPointer r1
    //     0x14d2bec: add             x1, x1, HEAP, lsl #32
    // 0x14d2bf0: r0 = controller()
    //     0x14d2bf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2bf4: LoadField: r1 = r0->field_57
    //     0x14d2bf4: ldur            w1, [x0, #0x57]
    // 0x14d2bf8: DecompressPointer r1
    //     0x14d2bf8: add             x1, x1, HEAP, lsl #32
    // 0x14d2bfc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d2bfc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d2c00: r0 = toList()
    //     0x14d2c00: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14d2c04: stur            x0, [fp, #-0x10]
    // 0x14d2c08: r0 = Radius()
    //     0x14d2c08: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14d2c0c: d0 = 20.000000
    //     0x14d2c0c: fmov            d0, #20.00000000
    // 0x14d2c10: stur            x0, [fp, #-0x20]
    // 0x14d2c14: StoreField: r0->field_7 = d0
    //     0x14d2c14: stur            d0, [x0, #7]
    // 0x14d2c18: StoreField: r0->field_f = d0
    //     0x14d2c18: stur            d0, [x0, #0xf]
    // 0x14d2c1c: r0 = BorderRadius()
    //     0x14d2c1c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14d2c20: mov             x1, x0
    // 0x14d2c24: ldur            x0, [fp, #-0x20]
    // 0x14d2c28: stur            x1, [fp, #-0x28]
    // 0x14d2c2c: StoreField: r1->field_7 = r0
    //     0x14d2c2c: stur            w0, [x1, #7]
    // 0x14d2c30: StoreField: r1->field_b = r0
    //     0x14d2c30: stur            w0, [x1, #0xb]
    // 0x14d2c34: StoreField: r1->field_f = r0
    //     0x14d2c34: stur            w0, [x1, #0xf]
    // 0x14d2c38: StoreField: r1->field_13 = r0
    //     0x14d2c38: stur            w0, [x1, #0x13]
    // 0x14d2c3c: r0 = TestimonialViewAll()
    //     0x14d2c3c: bl              #0x147c4c8  ; AllocateTestimonialViewAllStub -> TestimonialViewAll (size=0x1c)
    // 0x14d2c40: mov             x2, x0
    // 0x14d2c44: ldur            x0, [fp, #-0x10]
    // 0x14d2c48: stur            x2, [fp, #-0x20]
    // 0x14d2c4c: StoreField: r2->field_b = r0
    //     0x14d2c4c: stur            w0, [x2, #0xb]
    // 0x14d2c50: ldur            x0, [fp, #-0x28]
    // 0x14d2c54: StoreField: r2->field_f = r0
    //     0x14d2c54: stur            w0, [x2, #0xf]
    // 0x14d2c58: r0 = Instance_Border
    //     0x14d2c58: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e868] Obj!Border@d5ab41
    //     0x14d2c5c: ldr             x0, [x0, #0x868]
    // 0x14d2c60: StoreField: r2->field_13 = r0
    //     0x14d2c60: stur            w0, [x2, #0x13]
    // 0x14d2c64: r0 = Instance_Color
    //     0x14d2c64: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14d2c68: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d2c68: stur            w0, [x2, #0x17]
    // 0x14d2c6c: ldur            x3, [fp, #-0x18]
    // 0x14d2c70: LoadField: r1 = r3->field_b
    //     0x14d2c70: ldur            w1, [x3, #0xb]
    // 0x14d2c74: LoadField: r4 = r3->field_f
    //     0x14d2c74: ldur            w4, [x3, #0xf]
    // 0x14d2c78: DecompressPointer r4
    //     0x14d2c78: add             x4, x4, HEAP, lsl #32
    // 0x14d2c7c: LoadField: r5 = r4->field_b
    //     0x14d2c7c: ldur            w5, [x4, #0xb]
    // 0x14d2c80: r4 = LoadInt32Instr(r1)
    //     0x14d2c80: sbfx            x4, x1, #1, #0x1f
    // 0x14d2c84: stur            x4, [fp, #-0x40]
    // 0x14d2c88: r1 = LoadInt32Instr(r5)
    //     0x14d2c88: sbfx            x1, x5, #1, #0x1f
    // 0x14d2c8c: cmp             x4, x1
    // 0x14d2c90: b.ne            #0x14d2c9c
    // 0x14d2c94: mov             x1, x3
    // 0x14d2c98: r0 = _growToNextCapacity()
    //     0x14d2c98: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14d2c9c: ldur            x2, [fp, #-0x18]
    // 0x14d2ca0: ldur            x3, [fp, #-0x40]
    // 0x14d2ca4: add             x0, x3, #1
    // 0x14d2ca8: lsl             x1, x0, #1
    // 0x14d2cac: StoreField: r2->field_b = r1
    //     0x14d2cac: stur            w1, [x2, #0xb]
    // 0x14d2cb0: LoadField: r1 = r2->field_f
    //     0x14d2cb0: ldur            w1, [x2, #0xf]
    // 0x14d2cb4: DecompressPointer r1
    //     0x14d2cb4: add             x1, x1, HEAP, lsl #32
    // 0x14d2cb8: ldur            x0, [fp, #-0x20]
    // 0x14d2cbc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14d2cbc: add             x25, x1, x3, lsl #2
    //     0x14d2cc0: add             x25, x25, #0xf
    //     0x14d2cc4: str             w0, [x25]
    //     0x14d2cc8: tbz             w0, #0, #0x14d2ce4
    //     0x14d2ccc: ldurb           w16, [x1, #-1]
    //     0x14d2cd0: ldurb           w17, [x0, #-1]
    //     0x14d2cd4: and             x16, x17, x16, lsr #2
    //     0x14d2cd8: tst             x16, HEAP, lsr #32
    //     0x14d2cdc: b.eq            #0x14d2ce4
    //     0x14d2ce0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d2ce4: b               #0x14d2cec
    // 0x14d2ce8: ldur            x2, [fp, #-0x18]
    // 0x14d2cec: ldur            x0, [fp, #-8]
    // 0x14d2cf0: LoadField: r1 = r0->field_f
    //     0x14d2cf0: ldur            w1, [x0, #0xf]
    // 0x14d2cf4: DecompressPointer r1
    //     0x14d2cf4: add             x1, x1, HEAP, lsl #32
    // 0x14d2cf8: r0 = controller()
    //     0x14d2cf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2cfc: LoadField: r1 = r0->field_67
    //     0x14d2cfc: ldur            w1, [x0, #0x67]
    // 0x14d2d00: DecompressPointer r1
    //     0x14d2d00: add             x1, x1, HEAP, lsl #32
    // 0x14d2d04: r0 = LoadClassIdInstr(r1)
    //     0x14d2d04: ldur            x0, [x1, #-1]
    //     0x14d2d08: ubfx            x0, x0, #0xc, #0x14
    // 0x14d2d0c: r16 = "product_testimonial"
    //     0x14d2d0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea00] "product_testimonial"
    //     0x14d2d10: ldr             x16, [x16, #0xa00]
    // 0x14d2d14: stp             x16, x1, [SP]
    // 0x14d2d18: mov             lr, x0
    // 0x14d2d1c: ldr             lr, [x21, lr, lsl #3]
    // 0x14d2d20: blr             lr
    // 0x14d2d24: tbnz            w0, #4, #0x14d2df4
    // 0x14d2d28: ldur            x1, [fp, #-8]
    // 0x14d2d2c: ldur            x0, [fp, #-0x18]
    // 0x14d2d30: LoadField: r2 = r1->field_f
    //     0x14d2d30: ldur            w2, [x1, #0xf]
    // 0x14d2d34: DecompressPointer r2
    //     0x14d2d34: add             x2, x2, HEAP, lsl #32
    // 0x14d2d38: mov             x1, x2
    // 0x14d2d3c: r0 = controller()
    //     0x14d2d3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2d40: LoadField: r1 = r0->field_57
    //     0x14d2d40: ldur            w1, [x0, #0x57]
    // 0x14d2d44: DecompressPointer r1
    //     0x14d2d44: add             x1, x1, HEAP, lsl #32
    // 0x14d2d48: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d2d48: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d2d4c: r0 = toList()
    //     0x14d2d4c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14d2d50: stur            x0, [fp, #-8]
    // 0x14d2d54: r0 = ProductTestimonialViewAll()
    //     0x14d2d54: bl              #0x147c4bc  ; AllocateProductTestimonialViewAllStub -> ProductTestimonialViewAll (size=0x1c)
    // 0x14d2d58: mov             x2, x0
    // 0x14d2d5c: ldur            x0, [fp, #-8]
    // 0x14d2d60: stur            x2, [fp, #-0x10]
    // 0x14d2d64: StoreField: r2->field_b = r0
    //     0x14d2d64: stur            w0, [x2, #0xb]
    // 0x14d2d68: d0 = 15.000000
    //     0x14d2d68: fmov            d0, #15.00000000
    // 0x14d2d6c: StoreField: r2->field_f = d0
    //     0x14d2d6c: stur            d0, [x2, #0xf]
    // 0x14d2d70: r0 = Instance_Color
    //     0x14d2d70: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14d2d74: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d2d74: stur            w0, [x2, #0x17]
    // 0x14d2d78: ldur            x0, [fp, #-0x18]
    // 0x14d2d7c: LoadField: r1 = r0->field_b
    //     0x14d2d7c: ldur            w1, [x0, #0xb]
    // 0x14d2d80: LoadField: r3 = r0->field_f
    //     0x14d2d80: ldur            w3, [x0, #0xf]
    // 0x14d2d84: DecompressPointer r3
    //     0x14d2d84: add             x3, x3, HEAP, lsl #32
    // 0x14d2d88: LoadField: r4 = r3->field_b
    //     0x14d2d88: ldur            w4, [x3, #0xb]
    // 0x14d2d8c: r3 = LoadInt32Instr(r1)
    //     0x14d2d8c: sbfx            x3, x1, #1, #0x1f
    // 0x14d2d90: stur            x3, [fp, #-0x40]
    // 0x14d2d94: r1 = LoadInt32Instr(r4)
    //     0x14d2d94: sbfx            x1, x4, #1, #0x1f
    // 0x14d2d98: cmp             x3, x1
    // 0x14d2d9c: b.ne            #0x14d2da8
    // 0x14d2da0: mov             x1, x0
    // 0x14d2da4: r0 = _growToNextCapacity()
    //     0x14d2da4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14d2da8: ldur            x2, [fp, #-0x18]
    // 0x14d2dac: ldur            x3, [fp, #-0x40]
    // 0x14d2db0: add             x0, x3, #1
    // 0x14d2db4: lsl             x1, x0, #1
    // 0x14d2db8: StoreField: r2->field_b = r1
    //     0x14d2db8: stur            w1, [x2, #0xb]
    // 0x14d2dbc: LoadField: r1 = r2->field_f
    //     0x14d2dbc: ldur            w1, [x2, #0xf]
    // 0x14d2dc0: DecompressPointer r1
    //     0x14d2dc0: add             x1, x1, HEAP, lsl #32
    // 0x14d2dc4: ldur            x0, [fp, #-0x10]
    // 0x14d2dc8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14d2dc8: add             x25, x1, x3, lsl #2
    //     0x14d2dcc: add             x25, x25, #0xf
    //     0x14d2dd0: str             w0, [x25]
    //     0x14d2dd4: tbz             w0, #0, #0x14d2df0
    //     0x14d2dd8: ldurb           w16, [x1, #-1]
    //     0x14d2ddc: ldurb           w17, [x0, #-1]
    //     0x14d2de0: and             x16, x17, x16, lsr #2
    //     0x14d2de4: tst             x16, HEAP, lsr #32
    //     0x14d2de8: b.eq            #0x14d2df0
    //     0x14d2dec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d2df0: b               #0x14d2df8
    // 0x14d2df4: ldur            x2, [fp, #-0x18]
    // 0x14d2df8: r0 = Column()
    //     0x14d2df8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d2dfc: r1 = Instance_Axis
    //     0x14d2dfc: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d2e00: StoreField: r0->field_f = r1
    //     0x14d2e00: stur            w1, [x0, #0xf]
    // 0x14d2e04: r1 = Instance_MainAxisAlignment
    //     0x14d2e04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d2e08: ldr             x1, [x1, #0xa08]
    // 0x14d2e0c: StoreField: r0->field_13 = r1
    //     0x14d2e0c: stur            w1, [x0, #0x13]
    // 0x14d2e10: r1 = Instance_MainAxisSize
    //     0x14d2e10: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d2e14: ldr             x1, [x1, #0xa10]
    // 0x14d2e18: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d2e18: stur            w1, [x0, #0x17]
    // 0x14d2e1c: r1 = Instance_CrossAxisAlignment
    //     0x14d2e1c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d2e20: ldr             x1, [x1, #0xa18]
    // 0x14d2e24: StoreField: r0->field_1b = r1
    //     0x14d2e24: stur            w1, [x0, #0x1b]
    // 0x14d2e28: r1 = Instance_VerticalDirection
    //     0x14d2e28: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d2e2c: ldr             x1, [x1, #0xa20]
    // 0x14d2e30: StoreField: r0->field_23 = r1
    //     0x14d2e30: stur            w1, [x0, #0x23]
    // 0x14d2e34: r1 = Instance_Clip
    //     0x14d2e34: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d2e38: ldr             x1, [x1, #0x38]
    // 0x14d2e3c: StoreField: r0->field_2b = r1
    //     0x14d2e3c: stur            w1, [x0, #0x2b]
    // 0x14d2e40: StoreField: r0->field_2f = rZR
    //     0x14d2e40: stur            xzr, [x0, #0x2f]
    // 0x14d2e44: ldur            x1, [fp, #-0x18]
    // 0x14d2e48: StoreField: r0->field_b = r1
    //     0x14d2e48: stur            w1, [x0, #0xb]
    // 0x14d2e4c: LeaveFrame
    //     0x14d2e4c: mov             SP, fp
    //     0x14d2e50: ldp             fp, lr, [SP], #0x10
    // 0x14d2e54: ret
    //     0x14d2e54: ret             
    // 0x14d2e58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d2e58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d2e5c: b               #0x14d288c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dee7c, size: 0x2b4
    // 0x15dee7c: EnterFrame
    //     0x15dee7c: stp             fp, lr, [SP, #-0x10]!
    //     0x15dee80: mov             fp, SP
    // 0x15dee84: AllocStack(0x30)
    //     0x15dee84: sub             SP, SP, #0x30
    // 0x15dee88: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15dee88: stur            x1, [fp, #-8]
    //     0x15dee8c: stur            x2, [fp, #-0x10]
    // 0x15dee90: CheckStackOverflow
    //     0x15dee90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dee94: cmp             SP, x16
    //     0x15dee98: b.ls            #0x15df128
    // 0x15dee9c: r1 = 2
    //     0x15dee9c: movz            x1, #0x2
    // 0x15deea0: r0 = AllocateContext()
    //     0x15deea0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15deea4: ldur            x1, [fp, #-8]
    // 0x15deea8: stur            x0, [fp, #-0x18]
    // 0x15deeac: StoreField: r0->field_f = r1
    //     0x15deeac: stur            w1, [x0, #0xf]
    // 0x15deeb0: ldur            x2, [fp, #-0x10]
    // 0x15deeb4: StoreField: r0->field_13 = r2
    //     0x15deeb4: stur            w2, [x0, #0x13]
    // 0x15deeb8: r0 = Obx()
    //     0x15deeb8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15deebc: ldur            x2, [fp, #-0x18]
    // 0x15deec0: r1 = Function '<anonymous closure>':.
    //     0x15deec0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b88] AnonymousClosure: (0x15cfe38), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_screen.dart] ExchangeCheckoutScreen::appBar (0x15e9d84)
    //     0x15deec4: ldr             x1, [x1, #0xb88]
    // 0x15deec8: stur            x0, [fp, #-0x10]
    // 0x15deecc: r0 = AllocateClosure()
    //     0x15deecc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15deed0: mov             x1, x0
    // 0x15deed4: ldur            x0, [fp, #-0x10]
    // 0x15deed8: StoreField: r0->field_b = r1
    //     0x15deed8: stur            w1, [x0, #0xb]
    // 0x15deedc: ldur            x1, [fp, #-8]
    // 0x15deee0: r0 = controller()
    //     0x15deee0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15deee4: LoadField: r1 = r0->field_6b
    //     0x15deee4: ldur            w1, [x0, #0x6b]
    // 0x15deee8: DecompressPointer r1
    //     0x15deee8: add             x1, x1, HEAP, lsl #32
    // 0x15deeec: r0 = value()
    //     0x15deeec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15deef0: tbnz            w0, #4, #0x15def88
    // 0x15deef4: ldur            x2, [fp, #-0x18]
    // 0x15deef8: LoadField: r1 = r2->field_13
    //     0x15deef8: ldur            w1, [x2, #0x13]
    // 0x15deefc: DecompressPointer r1
    //     0x15deefc: add             x1, x1, HEAP, lsl #32
    // 0x15def00: r0 = of()
    //     0x15def00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15def04: LoadField: r1 = r0->field_5b
    //     0x15def04: ldur            w1, [x0, #0x5b]
    // 0x15def08: DecompressPointer r1
    //     0x15def08: add             x1, x1, HEAP, lsl #32
    // 0x15def0c: stur            x1, [fp, #-8]
    // 0x15def10: r0 = ColorFilter()
    //     0x15def10: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15def14: mov             x1, x0
    // 0x15def18: ldur            x0, [fp, #-8]
    // 0x15def1c: stur            x1, [fp, #-0x20]
    // 0x15def20: StoreField: r1->field_7 = r0
    //     0x15def20: stur            w0, [x1, #7]
    // 0x15def24: r0 = Instance_BlendMode
    //     0x15def24: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15def28: ldr             x0, [x0, #0xb30]
    // 0x15def2c: StoreField: r1->field_b = r0
    //     0x15def2c: stur            w0, [x1, #0xb]
    // 0x15def30: r2 = 1
    //     0x15def30: movz            x2, #0x1
    // 0x15def34: StoreField: r1->field_13 = r2
    //     0x15def34: stur            x2, [x1, #0x13]
    // 0x15def38: r0 = SvgPicture()
    //     0x15def38: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15def3c: stur            x0, [fp, #-8]
    // 0x15def40: ldur            x16, [fp, #-0x20]
    // 0x15def44: str             x16, [SP]
    // 0x15def48: mov             x1, x0
    // 0x15def4c: r2 = "assets/images/search.svg"
    //     0x15def4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15def50: ldr             x2, [x2, #0xa30]
    // 0x15def54: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15def54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15def58: ldr             x4, [x4, #0xa38]
    // 0x15def5c: r0 = SvgPicture.asset()
    //     0x15def5c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15def60: r0 = Align()
    //     0x15def60: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15def64: r3 = Instance_Alignment
    //     0x15def64: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15def68: ldr             x3, [x3, #0xb10]
    // 0x15def6c: StoreField: r0->field_f = r3
    //     0x15def6c: stur            w3, [x0, #0xf]
    // 0x15def70: r4 = 1.000000
    //     0x15def70: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15def74: StoreField: r0->field_13 = r4
    //     0x15def74: stur            w4, [x0, #0x13]
    // 0x15def78: ArrayStore: r0[0] = r4  ; List_4
    //     0x15def78: stur            w4, [x0, #0x17]
    // 0x15def7c: ldur            x1, [fp, #-8]
    // 0x15def80: StoreField: r0->field_b = r1
    //     0x15def80: stur            w1, [x0, #0xb]
    // 0x15def84: b               #0x15df038
    // 0x15def88: ldur            x5, [fp, #-0x18]
    // 0x15def8c: r4 = 1.000000
    //     0x15def8c: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15def90: r0 = Instance_BlendMode
    //     0x15def90: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15def94: ldr             x0, [x0, #0xb30]
    // 0x15def98: r3 = Instance_Alignment
    //     0x15def98: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15def9c: ldr             x3, [x3, #0xb10]
    // 0x15defa0: r2 = 1
    //     0x15defa0: movz            x2, #0x1
    // 0x15defa4: LoadField: r1 = r5->field_13
    //     0x15defa4: ldur            w1, [x5, #0x13]
    // 0x15defa8: DecompressPointer r1
    //     0x15defa8: add             x1, x1, HEAP, lsl #32
    // 0x15defac: r0 = of()
    //     0x15defac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15defb0: LoadField: r1 = r0->field_5b
    //     0x15defb0: ldur            w1, [x0, #0x5b]
    // 0x15defb4: DecompressPointer r1
    //     0x15defb4: add             x1, x1, HEAP, lsl #32
    // 0x15defb8: stur            x1, [fp, #-8]
    // 0x15defbc: r0 = ColorFilter()
    //     0x15defbc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15defc0: mov             x1, x0
    // 0x15defc4: ldur            x0, [fp, #-8]
    // 0x15defc8: stur            x1, [fp, #-0x20]
    // 0x15defcc: StoreField: r1->field_7 = r0
    //     0x15defcc: stur            w0, [x1, #7]
    // 0x15defd0: r0 = Instance_BlendMode
    //     0x15defd0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15defd4: ldr             x0, [x0, #0xb30]
    // 0x15defd8: StoreField: r1->field_b = r0
    //     0x15defd8: stur            w0, [x1, #0xb]
    // 0x15defdc: r0 = 1
    //     0x15defdc: movz            x0, #0x1
    // 0x15defe0: StoreField: r1->field_13 = r0
    //     0x15defe0: stur            x0, [x1, #0x13]
    // 0x15defe4: r0 = SvgPicture()
    //     0x15defe4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15defe8: stur            x0, [fp, #-8]
    // 0x15defec: ldur            x16, [fp, #-0x20]
    // 0x15deff0: str             x16, [SP]
    // 0x15deff4: mov             x1, x0
    // 0x15deff8: r2 = "assets/images/appbar_arrow.svg"
    //     0x15deff8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15deffc: ldr             x2, [x2, #0xa40]
    // 0x15df000: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15df000: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15df004: ldr             x4, [x4, #0xa38]
    // 0x15df008: r0 = SvgPicture.asset()
    //     0x15df008: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15df00c: r0 = Align()
    //     0x15df00c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15df010: mov             x1, x0
    // 0x15df014: r0 = Instance_Alignment
    //     0x15df014: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15df018: ldr             x0, [x0, #0xb10]
    // 0x15df01c: StoreField: r1->field_f = r0
    //     0x15df01c: stur            w0, [x1, #0xf]
    // 0x15df020: r0 = 1.000000
    //     0x15df020: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15df024: StoreField: r1->field_13 = r0
    //     0x15df024: stur            w0, [x1, #0x13]
    // 0x15df028: ArrayStore: r1[0] = r0  ; List_4
    //     0x15df028: stur            w0, [x1, #0x17]
    // 0x15df02c: ldur            x0, [fp, #-8]
    // 0x15df030: StoreField: r1->field_b = r0
    //     0x15df030: stur            w0, [x1, #0xb]
    // 0x15df034: mov             x0, x1
    // 0x15df038: stur            x0, [fp, #-8]
    // 0x15df03c: r0 = InkWell()
    //     0x15df03c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15df040: mov             x3, x0
    // 0x15df044: ldur            x0, [fp, #-8]
    // 0x15df048: stur            x3, [fp, #-0x20]
    // 0x15df04c: StoreField: r3->field_b = r0
    //     0x15df04c: stur            w0, [x3, #0xb]
    // 0x15df050: ldur            x2, [fp, #-0x18]
    // 0x15df054: r1 = Function '<anonymous closure>':.
    //     0x15df054: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b90] AnonymousClosure: (0x15d8310), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15df058: ldr             x1, [x1, #0xb90]
    // 0x15df05c: r0 = AllocateClosure()
    //     0x15df05c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df060: ldur            x2, [fp, #-0x20]
    // 0x15df064: StoreField: r2->field_f = r0
    //     0x15df064: stur            w0, [x2, #0xf]
    // 0x15df068: r0 = true
    //     0x15df068: add             x0, NULL, #0x20  ; true
    // 0x15df06c: StoreField: r2->field_43 = r0
    //     0x15df06c: stur            w0, [x2, #0x43]
    // 0x15df070: r1 = Instance_BoxShape
    //     0x15df070: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15df074: ldr             x1, [x1, #0x80]
    // 0x15df078: StoreField: r2->field_47 = r1
    //     0x15df078: stur            w1, [x2, #0x47]
    // 0x15df07c: StoreField: r2->field_6f = r0
    //     0x15df07c: stur            w0, [x2, #0x6f]
    // 0x15df080: r1 = false
    //     0x15df080: add             x1, NULL, #0x30  ; false
    // 0x15df084: StoreField: r2->field_73 = r1
    //     0x15df084: stur            w1, [x2, #0x73]
    // 0x15df088: StoreField: r2->field_83 = r0
    //     0x15df088: stur            w0, [x2, #0x83]
    // 0x15df08c: StoreField: r2->field_7b = r1
    //     0x15df08c: stur            w1, [x2, #0x7b]
    // 0x15df090: r0 = Obx()
    //     0x15df090: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15df094: ldur            x2, [fp, #-0x18]
    // 0x15df098: r1 = Function '<anonymous closure>':.
    //     0x15df098: add             x1, PP, #0x41, lsl #12  ; [pp+0x41b98] AnonymousClosure: (0x15df130), in [package:customer_app/app/presentation/views/cosmetic/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15dee7c)
    //     0x15df09c: ldr             x1, [x1, #0xb98]
    // 0x15df0a0: stur            x0, [fp, #-8]
    // 0x15df0a4: r0 = AllocateClosure()
    //     0x15df0a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df0a8: mov             x1, x0
    // 0x15df0ac: ldur            x0, [fp, #-8]
    // 0x15df0b0: StoreField: r0->field_b = r1
    //     0x15df0b0: stur            w1, [x0, #0xb]
    // 0x15df0b4: r1 = Null
    //     0x15df0b4: mov             x1, NULL
    // 0x15df0b8: r2 = 2
    //     0x15df0b8: movz            x2, #0x2
    // 0x15df0bc: r0 = AllocateArray()
    //     0x15df0bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15df0c0: mov             x2, x0
    // 0x15df0c4: ldur            x0, [fp, #-8]
    // 0x15df0c8: stur            x2, [fp, #-0x18]
    // 0x15df0cc: StoreField: r2->field_f = r0
    //     0x15df0cc: stur            w0, [x2, #0xf]
    // 0x15df0d0: r1 = <Widget>
    //     0x15df0d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15df0d4: r0 = AllocateGrowableArray()
    //     0x15df0d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15df0d8: mov             x1, x0
    // 0x15df0dc: ldur            x0, [fp, #-0x18]
    // 0x15df0e0: stur            x1, [fp, #-8]
    // 0x15df0e4: StoreField: r1->field_f = r0
    //     0x15df0e4: stur            w0, [x1, #0xf]
    // 0x15df0e8: r0 = 2
    //     0x15df0e8: movz            x0, #0x2
    // 0x15df0ec: StoreField: r1->field_b = r0
    //     0x15df0ec: stur            w0, [x1, #0xb]
    // 0x15df0f0: r0 = AppBar()
    //     0x15df0f0: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15df0f4: stur            x0, [fp, #-0x18]
    // 0x15df0f8: ldur            x16, [fp, #-0x10]
    // 0x15df0fc: ldur            lr, [fp, #-8]
    // 0x15df100: stp             lr, x16, [SP]
    // 0x15df104: mov             x1, x0
    // 0x15df108: ldur            x2, [fp, #-0x20]
    // 0x15df10c: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15df10c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15df110: ldr             x4, [x4, #0xa58]
    // 0x15df114: r0 = AppBar()
    //     0x15df114: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15df118: ldur            x0, [fp, #-0x18]
    // 0x15df11c: LeaveFrame
    //     0x15df11c: mov             SP, fp
    //     0x15df120: ldp             fp, lr, [SP], #0x10
    // 0x15df124: ret
    //     0x15df124: ret             
    // 0x15df128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15df128: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15df12c: b               #0x15dee9c
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15df130, size: 0x304
    // 0x15df130: EnterFrame
    //     0x15df130: stp             fp, lr, [SP, #-0x10]!
    //     0x15df134: mov             fp, SP
    // 0x15df138: AllocStack(0x58)
    //     0x15df138: sub             SP, SP, #0x58
    // 0x15df13c: SetupParameters()
    //     0x15df13c: ldr             x0, [fp, #0x10]
    //     0x15df140: ldur            w2, [x0, #0x17]
    //     0x15df144: add             x2, x2, HEAP, lsl #32
    //     0x15df148: stur            x2, [fp, #-8]
    // 0x15df14c: CheckStackOverflow
    //     0x15df14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15df150: cmp             SP, x16
    //     0x15df154: b.ls            #0x15df42c
    // 0x15df158: LoadField: r1 = r2->field_f
    //     0x15df158: ldur            w1, [x2, #0xf]
    // 0x15df15c: DecompressPointer r1
    //     0x15df15c: add             x1, x1, HEAP, lsl #32
    // 0x15df160: r0 = controller()
    //     0x15df160: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df164: LoadField: r1 = r0->field_5b
    //     0x15df164: ldur            w1, [x0, #0x5b]
    // 0x15df168: DecompressPointer r1
    //     0x15df168: add             x1, x1, HEAP, lsl #32
    // 0x15df16c: r0 = value()
    //     0x15df16c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df170: LoadField: r1 = r0->field_1f
    //     0x15df170: ldur            w1, [x0, #0x1f]
    // 0x15df174: DecompressPointer r1
    //     0x15df174: add             x1, x1, HEAP, lsl #32
    // 0x15df178: cmp             w1, NULL
    // 0x15df17c: b.ne            #0x15df188
    // 0x15df180: r0 = Null
    //     0x15df180: mov             x0, NULL
    // 0x15df184: b               #0x15df190
    // 0x15df188: LoadField: r0 = r1->field_7
    //     0x15df188: ldur            w0, [x1, #7]
    // 0x15df18c: DecompressPointer r0
    //     0x15df18c: add             x0, x0, HEAP, lsl #32
    // 0x15df190: cmp             w0, NULL
    // 0x15df194: b.ne            #0x15df1a0
    // 0x15df198: r0 = false
    //     0x15df198: add             x0, NULL, #0x30  ; false
    // 0x15df19c: b               #0x15df394
    // 0x15df1a0: tbnz            w0, #4, #0x15df390
    // 0x15df1a4: ldur            x0, [fp, #-8]
    // 0x15df1a8: LoadField: r1 = r0->field_f
    //     0x15df1a8: ldur            w1, [x0, #0xf]
    // 0x15df1ac: DecompressPointer r1
    //     0x15df1ac: add             x1, x1, HEAP, lsl #32
    // 0x15df1b0: r0 = controller()
    //     0x15df1b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df1b4: LoadField: r1 = r0->field_6f
    //     0x15df1b4: ldur            w1, [x0, #0x6f]
    // 0x15df1b8: DecompressPointer r1
    //     0x15df1b8: add             x1, x1, HEAP, lsl #32
    // 0x15df1bc: r0 = value()
    //     0x15df1bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df1c0: mov             x2, x0
    // 0x15df1c4: ldur            x0, [fp, #-8]
    // 0x15df1c8: stur            x2, [fp, #-0x10]
    // 0x15df1cc: LoadField: r1 = r0->field_13
    //     0x15df1cc: ldur            w1, [x0, #0x13]
    // 0x15df1d0: DecompressPointer r1
    //     0x15df1d0: add             x1, x1, HEAP, lsl #32
    // 0x15df1d4: r0 = of()
    //     0x15df1d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df1d8: LoadField: r2 = r0->field_5b
    //     0x15df1d8: ldur            w2, [x0, #0x5b]
    // 0x15df1dc: DecompressPointer r2
    //     0x15df1dc: add             x2, x2, HEAP, lsl #32
    // 0x15df1e0: ldur            x0, [fp, #-8]
    // 0x15df1e4: stur            x2, [fp, #-0x18]
    // 0x15df1e8: LoadField: r1 = r0->field_f
    //     0x15df1e8: ldur            w1, [x0, #0xf]
    // 0x15df1ec: DecompressPointer r1
    //     0x15df1ec: add             x1, x1, HEAP, lsl #32
    // 0x15df1f0: r0 = controller()
    //     0x15df1f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df1f4: LoadField: r1 = r0->field_73
    //     0x15df1f4: ldur            w1, [x0, #0x73]
    // 0x15df1f8: DecompressPointer r1
    //     0x15df1f8: add             x1, x1, HEAP, lsl #32
    // 0x15df1fc: r0 = value()
    //     0x15df1fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df200: cmp             w0, NULL
    // 0x15df204: r16 = true
    //     0x15df204: add             x16, NULL, #0x20  ; true
    // 0x15df208: r17 = false
    //     0x15df208: add             x17, NULL, #0x30  ; false
    // 0x15df20c: csel            x2, x16, x17, ne
    // 0x15df210: ldur            x0, [fp, #-8]
    // 0x15df214: stur            x2, [fp, #-0x20]
    // 0x15df218: LoadField: r1 = r0->field_f
    //     0x15df218: ldur            w1, [x0, #0xf]
    // 0x15df21c: DecompressPointer r1
    //     0x15df21c: add             x1, x1, HEAP, lsl #32
    // 0x15df220: r0 = controller()
    //     0x15df220: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15df224: LoadField: r1 = r0->field_73
    //     0x15df224: ldur            w1, [x0, #0x73]
    // 0x15df228: DecompressPointer r1
    //     0x15df228: add             x1, x1, HEAP, lsl #32
    // 0x15df22c: r0 = value()
    //     0x15df22c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15df230: str             x0, [SP]
    // 0x15df234: r0 = _interpolateSingle()
    //     0x15df234: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15df238: mov             x2, x0
    // 0x15df23c: ldur            x0, [fp, #-8]
    // 0x15df240: stur            x2, [fp, #-0x28]
    // 0x15df244: LoadField: r1 = r0->field_13
    //     0x15df244: ldur            w1, [x0, #0x13]
    // 0x15df248: DecompressPointer r1
    //     0x15df248: add             x1, x1, HEAP, lsl #32
    // 0x15df24c: r0 = of()
    //     0x15df24c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df250: LoadField: r1 = r0->field_87
    //     0x15df250: ldur            w1, [x0, #0x87]
    // 0x15df254: DecompressPointer r1
    //     0x15df254: add             x1, x1, HEAP, lsl #32
    // 0x15df258: LoadField: r0 = r1->field_27
    //     0x15df258: ldur            w0, [x1, #0x27]
    // 0x15df25c: DecompressPointer r0
    //     0x15df25c: add             x0, x0, HEAP, lsl #32
    // 0x15df260: r16 = Instance_Color
    //     0x15df260: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15df264: str             x16, [SP]
    // 0x15df268: mov             x1, x0
    // 0x15df26c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15df26c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15df270: ldr             x4, [x4, #0xf40]
    // 0x15df274: r0 = copyWith()
    //     0x15df274: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15df278: stur            x0, [fp, #-0x30]
    // 0x15df27c: r0 = Text()
    //     0x15df27c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15df280: mov             x2, x0
    // 0x15df284: ldur            x0, [fp, #-0x28]
    // 0x15df288: stur            x2, [fp, #-0x38]
    // 0x15df28c: StoreField: r2->field_b = r0
    //     0x15df28c: stur            w0, [x2, #0xb]
    // 0x15df290: ldur            x0, [fp, #-0x30]
    // 0x15df294: StoreField: r2->field_13 = r0
    //     0x15df294: stur            w0, [x2, #0x13]
    // 0x15df298: ldur            x0, [fp, #-8]
    // 0x15df29c: LoadField: r1 = r0->field_13
    //     0x15df29c: ldur            w1, [x0, #0x13]
    // 0x15df2a0: DecompressPointer r1
    //     0x15df2a0: add             x1, x1, HEAP, lsl #32
    // 0x15df2a4: r0 = of()
    //     0x15df2a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15df2a8: LoadField: r1 = r0->field_5b
    //     0x15df2a8: ldur            w1, [x0, #0x5b]
    // 0x15df2ac: DecompressPointer r1
    //     0x15df2ac: add             x1, x1, HEAP, lsl #32
    // 0x15df2b0: stur            x1, [fp, #-8]
    // 0x15df2b4: r0 = ColorFilter()
    //     0x15df2b4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15df2b8: mov             x1, x0
    // 0x15df2bc: ldur            x0, [fp, #-8]
    // 0x15df2c0: stur            x1, [fp, #-0x28]
    // 0x15df2c4: StoreField: r1->field_7 = r0
    //     0x15df2c4: stur            w0, [x1, #7]
    // 0x15df2c8: r0 = Instance_BlendMode
    //     0x15df2c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15df2cc: ldr             x0, [x0, #0xb30]
    // 0x15df2d0: StoreField: r1->field_b = r0
    //     0x15df2d0: stur            w0, [x1, #0xb]
    // 0x15df2d4: r0 = 1
    //     0x15df2d4: movz            x0, #0x1
    // 0x15df2d8: StoreField: r1->field_13 = r0
    //     0x15df2d8: stur            x0, [x1, #0x13]
    // 0x15df2dc: r0 = SvgPicture()
    //     0x15df2dc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15df2e0: stur            x0, [fp, #-8]
    // 0x15df2e4: r16 = Instance_BoxFit
    //     0x15df2e4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15df2e8: ldr             x16, [x16, #0xb18]
    // 0x15df2ec: r30 = 24.000000
    //     0x15df2ec: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15df2f0: ldr             lr, [lr, #0xba8]
    // 0x15df2f4: stp             lr, x16, [SP, #0x10]
    // 0x15df2f8: r16 = 24.000000
    //     0x15df2f8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15df2fc: ldr             x16, [x16, #0xba8]
    // 0x15df300: ldur            lr, [fp, #-0x28]
    // 0x15df304: stp             lr, x16, [SP]
    // 0x15df308: mov             x1, x0
    // 0x15df30c: r2 = "assets/images/shopping_bag.svg"
    //     0x15df30c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15df310: ldr             x2, [x2, #0xa60]
    // 0x15df314: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15df314: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15df318: ldr             x4, [x4, #0xa68]
    // 0x15df31c: r0 = SvgPicture.asset()
    //     0x15df31c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15df320: r0 = Badge()
    //     0x15df320: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15df324: mov             x1, x0
    // 0x15df328: ldur            x0, [fp, #-0x18]
    // 0x15df32c: stur            x1, [fp, #-0x28]
    // 0x15df330: StoreField: r1->field_b = r0
    //     0x15df330: stur            w0, [x1, #0xb]
    // 0x15df334: ldur            x0, [fp, #-0x38]
    // 0x15df338: StoreField: r1->field_27 = r0
    //     0x15df338: stur            w0, [x1, #0x27]
    // 0x15df33c: ldur            x0, [fp, #-0x20]
    // 0x15df340: StoreField: r1->field_2b = r0
    //     0x15df340: stur            w0, [x1, #0x2b]
    // 0x15df344: ldur            x0, [fp, #-8]
    // 0x15df348: StoreField: r1->field_2f = r0
    //     0x15df348: stur            w0, [x1, #0x2f]
    // 0x15df34c: r0 = Visibility()
    //     0x15df34c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15df350: mov             x1, x0
    // 0x15df354: ldur            x0, [fp, #-0x28]
    // 0x15df358: StoreField: r1->field_b = r0
    //     0x15df358: stur            w0, [x1, #0xb]
    // 0x15df35c: r0 = Instance_SizedBox
    //     0x15df35c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15df360: StoreField: r1->field_f = r0
    //     0x15df360: stur            w0, [x1, #0xf]
    // 0x15df364: ldur            x0, [fp, #-0x10]
    // 0x15df368: StoreField: r1->field_13 = r0
    //     0x15df368: stur            w0, [x1, #0x13]
    // 0x15df36c: r0 = false
    //     0x15df36c: add             x0, NULL, #0x30  ; false
    // 0x15df370: ArrayStore: r1[0] = r0  ; List_4
    //     0x15df370: stur            w0, [x1, #0x17]
    // 0x15df374: StoreField: r1->field_1b = r0
    //     0x15df374: stur            w0, [x1, #0x1b]
    // 0x15df378: StoreField: r1->field_1f = r0
    //     0x15df378: stur            w0, [x1, #0x1f]
    // 0x15df37c: StoreField: r1->field_23 = r0
    //     0x15df37c: stur            w0, [x1, #0x23]
    // 0x15df380: StoreField: r1->field_27 = r0
    //     0x15df380: stur            w0, [x1, #0x27]
    // 0x15df384: StoreField: r1->field_2b = r0
    //     0x15df384: stur            w0, [x1, #0x2b]
    // 0x15df388: mov             x0, x1
    // 0x15df38c: b               #0x15df3ac
    // 0x15df390: r0 = false
    //     0x15df390: add             x0, NULL, #0x30  ; false
    // 0x15df394: r0 = Container()
    //     0x15df394: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15df398: mov             x1, x0
    // 0x15df39c: stur            x0, [fp, #-8]
    // 0x15df3a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15df3a0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15df3a4: r0 = Container()
    //     0x15df3a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15df3a8: ldur            x0, [fp, #-8]
    // 0x15df3ac: stur            x0, [fp, #-8]
    // 0x15df3b0: r0 = InkWell()
    //     0x15df3b0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15df3b4: mov             x3, x0
    // 0x15df3b8: ldur            x0, [fp, #-8]
    // 0x15df3bc: stur            x3, [fp, #-0x10]
    // 0x15df3c0: StoreField: r3->field_b = r0
    //     0x15df3c0: stur            w0, [x3, #0xb]
    // 0x15df3c4: r1 = Function '<anonymous closure>':.
    //     0x15df3c4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ba0] AnonymousClosure: (0x15d7f78), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15df3c8: ldr             x1, [x1, #0xba0]
    // 0x15df3cc: r2 = Null
    //     0x15df3cc: mov             x2, NULL
    // 0x15df3d0: r0 = AllocateClosure()
    //     0x15df3d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15df3d4: mov             x1, x0
    // 0x15df3d8: ldur            x0, [fp, #-0x10]
    // 0x15df3dc: StoreField: r0->field_f = r1
    //     0x15df3dc: stur            w1, [x0, #0xf]
    // 0x15df3e0: r1 = true
    //     0x15df3e0: add             x1, NULL, #0x20  ; true
    // 0x15df3e4: StoreField: r0->field_43 = r1
    //     0x15df3e4: stur            w1, [x0, #0x43]
    // 0x15df3e8: r2 = Instance_BoxShape
    //     0x15df3e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15df3ec: ldr             x2, [x2, #0x80]
    // 0x15df3f0: StoreField: r0->field_47 = r2
    //     0x15df3f0: stur            w2, [x0, #0x47]
    // 0x15df3f4: StoreField: r0->field_6f = r1
    //     0x15df3f4: stur            w1, [x0, #0x6f]
    // 0x15df3f8: r2 = false
    //     0x15df3f8: add             x2, NULL, #0x30  ; false
    // 0x15df3fc: StoreField: r0->field_73 = r2
    //     0x15df3fc: stur            w2, [x0, #0x73]
    // 0x15df400: StoreField: r0->field_83 = r1
    //     0x15df400: stur            w1, [x0, #0x83]
    // 0x15df404: StoreField: r0->field_7b = r2
    //     0x15df404: stur            w2, [x0, #0x7b]
    // 0x15df408: r0 = Padding()
    //     0x15df408: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15df40c: r1 = Instance_EdgeInsets
    //     0x15df40c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15df410: ldr             x1, [x1, #0xa78]
    // 0x15df414: StoreField: r0->field_f = r1
    //     0x15df414: stur            w1, [x0, #0xf]
    // 0x15df418: ldur            x1, [fp, #-0x10]
    // 0x15df41c: StoreField: r0->field_b = r1
    //     0x15df41c: stur            w1, [x0, #0xb]
    // 0x15df420: LeaveFrame
    //     0x15df420: mov             SP, fp
    //     0x15df424: ldp             fp, lr, [SP], #0x10
    // 0x15df428: ret
    //     0x15df428: ret             
    // 0x15df42c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15df42c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15df430: b               #0x15df158
  }
}
