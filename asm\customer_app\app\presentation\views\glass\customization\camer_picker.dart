// lib: , url: package:customer_app/app/presentation/views/glass/customization/camer_picker.dart

// class id: 1049383, size: 0x8
class :: {
}

// class id: 3349, size: 0x18, field offset: 0x14
class _CameraPickerState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb56520, size: 0x754
    // 0xb56520: EnterFrame
    //     0xb56520: stp             fp, lr, [SP, #-0x10]!
    //     0xb56524: mov             fp, SP
    // 0xb56528: AllocStack(0x58)
    //     0xb56528: sub             SP, SP, #0x58
    // 0xb5652c: SetupParameters(_CameraPickerState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb5652c: mov             x0, x1
    //     0xb56530: stur            x1, [fp, #-8]
    //     0xb56534: mov             x1, x2
    //     0xb56538: stur            x2, [fp, #-0x10]
    // 0xb5653c: CheckStackOverflow
    //     0xb5653c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56540: cmp             SP, x16
    //     0xb56544: b.ls            #0xb56c64
    // 0xb56548: r1 = 1
    //     0xb56548: movz            x1, #0x1
    // 0xb5654c: r0 = AllocateContext()
    //     0xb5654c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb56550: mov             x3, x0
    // 0xb56554: ldur            x0, [fp, #-8]
    // 0xb56558: stur            x3, [fp, #-0x20]
    // 0xb5655c: StoreField: r3->field_f = r0
    //     0xb5655c: stur            w0, [x3, #0xf]
    // 0xb56560: LoadField: r1 = r0->field_b
    //     0xb56560: ldur            w1, [x0, #0xb]
    // 0xb56564: DecompressPointer r1
    //     0xb56564: add             x1, x1, HEAP, lsl #32
    // 0xb56568: cmp             w1, NULL
    // 0xb5656c: b.eq            #0xb56c6c
    // 0xb56570: LoadField: r2 = r1->field_b
    //     0xb56570: ldur            w2, [x1, #0xb]
    // 0xb56574: DecompressPointer r2
    //     0xb56574: add             x2, x2, HEAP, lsl #32
    // 0xb56578: cmp             w2, NULL
    // 0xb5657c: b.ne            #0xb56588
    // 0xb56580: r1 = Null
    //     0xb56580: mov             x1, NULL
    // 0xb56584: b               #0xb56590
    // 0xb56588: LoadField: r1 = r2->field_2b
    //     0xb56588: ldur            w1, [x2, #0x2b]
    // 0xb5658c: DecompressPointer r1
    //     0xb5658c: add             x1, x1, HEAP, lsl #32
    // 0xb56590: cmp             w1, NULL
    // 0xb56594: b.eq            #0xb565f0
    // 0xb56598: tbnz            w1, #4, #0xb565f0
    // 0xb5659c: cmp             w2, NULL
    // 0xb565a0: b.ne            #0xb565ac
    // 0xb565a4: r4 = Null
    //     0xb565a4: mov             x4, NULL
    // 0xb565a8: b               #0xb565b8
    // 0xb565ac: LoadField: r1 = r2->field_1b
    //     0xb565ac: ldur            w1, [x2, #0x1b]
    // 0xb565b0: DecompressPointer r1
    //     0xb565b0: add             x1, x1, HEAP, lsl #32
    // 0xb565b4: mov             x4, x1
    // 0xb565b8: stur            x4, [fp, #-0x18]
    // 0xb565bc: r1 = Null
    //     0xb565bc: mov             x1, NULL
    // 0xb565c0: r2 = 4
    //     0xb565c0: movz            x2, #0x4
    // 0xb565c4: r0 = AllocateArray()
    //     0xb565c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb565c8: mov             x1, x0
    // 0xb565cc: ldur            x0, [fp, #-0x18]
    // 0xb565d0: StoreField: r1->field_f = r0
    //     0xb565d0: stur            w0, [x1, #0xf]
    // 0xb565d4: r16 = " *"
    //     0xb565d4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xb565d8: ldr             x16, [x16, #0xfc8]
    // 0xb565dc: StoreField: r1->field_13 = r16
    //     0xb565dc: stur            w16, [x1, #0x13]
    // 0xb565e0: str             x1, [SP]
    // 0xb565e4: r0 = _interpolate()
    //     0xb565e4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb565e8: mov             x2, x0
    // 0xb565ec: b               #0xb56614
    // 0xb565f0: cmp             w2, NULL
    // 0xb565f4: b.ne            #0xb56600
    // 0xb565f8: r0 = Null
    //     0xb565f8: mov             x0, NULL
    // 0xb565fc: b               #0xb56608
    // 0xb56600: LoadField: r0 = r2->field_1b
    //     0xb56600: ldur            w0, [x2, #0x1b]
    // 0xb56604: DecompressPointer r0
    //     0xb56604: add             x0, x0, HEAP, lsl #32
    // 0xb56608: str             x0, [SP]
    // 0xb5660c: r0 = _interpolateSingle()
    //     0xb5660c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb56610: mov             x2, x0
    // 0xb56614: ldur            x0, [fp, #-8]
    // 0xb56618: ldur            x1, [fp, #-0x10]
    // 0xb5661c: stur            x2, [fp, #-0x18]
    // 0xb56620: r0 = of()
    //     0xb56620: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb56624: LoadField: r1 = r0->field_87
    //     0xb56624: ldur            w1, [x0, #0x87]
    // 0xb56628: DecompressPointer r1
    //     0xb56628: add             x1, x1, HEAP, lsl #32
    // 0xb5662c: LoadField: r0 = r1->field_7
    //     0xb5662c: ldur            w0, [x1, #7]
    // 0xb56630: DecompressPointer r0
    //     0xb56630: add             x0, x0, HEAP, lsl #32
    // 0xb56634: r16 = Instance_Color
    //     0xb56634: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb56638: r30 = 14.000000
    //     0xb56638: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5663c: ldr             lr, [lr, #0x1d8]
    // 0xb56640: stp             lr, x16, [SP]
    // 0xb56644: mov             x1, x0
    // 0xb56648: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb56648: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5664c: ldr             x4, [x4, #0x9b8]
    // 0xb56650: r0 = copyWith()
    //     0xb56650: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb56654: stur            x0, [fp, #-0x28]
    // 0xb56658: r0 = Text()
    //     0xb56658: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5665c: mov             x3, x0
    // 0xb56660: ldur            x0, [fp, #-0x18]
    // 0xb56664: stur            x3, [fp, #-0x30]
    // 0xb56668: StoreField: r3->field_b = r0
    //     0xb56668: stur            w0, [x3, #0xb]
    // 0xb5666c: ldur            x0, [fp, #-0x28]
    // 0xb56670: StoreField: r3->field_13 = r0
    //     0xb56670: stur            w0, [x3, #0x13]
    // 0xb56674: ldur            x0, [fp, #-8]
    // 0xb56678: LoadField: r1 = r0->field_b
    //     0xb56678: ldur            w1, [x0, #0xb]
    // 0xb5667c: DecompressPointer r1
    //     0xb5667c: add             x1, x1, HEAP, lsl #32
    // 0xb56680: cmp             w1, NULL
    // 0xb56684: b.eq            #0xb56c70
    // 0xb56688: LoadField: r4 = r1->field_b
    //     0xb56688: ldur            w4, [x1, #0xb]
    // 0xb5668c: DecompressPointer r4
    //     0xb5668c: add             x4, x4, HEAP, lsl #32
    // 0xb56690: stur            x4, [fp, #-0x28]
    // 0xb56694: cmp             w4, NULL
    // 0xb56698: b.ne            #0xb566a4
    // 0xb5669c: r1 = Null
    //     0xb5669c: mov             x1, NULL
    // 0xb566a0: b               #0xb566ac
    // 0xb566a4: LoadField: r1 = r4->field_23
    //     0xb566a4: ldur            w1, [x4, #0x23]
    // 0xb566a8: DecompressPointer r1
    //     0xb566a8: add             x1, x1, HEAP, lsl #32
    // 0xb566ac: cbnz            w1, #0xb566b8
    // 0xb566b0: r5 = false
    //     0xb566b0: add             x5, NULL, #0x30  ; false
    // 0xb566b4: b               #0xb566bc
    // 0xb566b8: r5 = true
    //     0xb566b8: add             x5, NULL, #0x20  ; true
    // 0xb566bc: stur            x5, [fp, #-0x18]
    // 0xb566c0: r1 = Null
    //     0xb566c0: mov             x1, NULL
    // 0xb566c4: r2 = 4
    //     0xb566c4: movz            x2, #0x4
    // 0xb566c8: r0 = AllocateArray()
    //     0xb566c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb566cc: mov             x1, x0
    // 0xb566d0: stur            x1, [fp, #-0x38]
    // 0xb566d4: r16 = "+ "
    //     0xb566d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xb566d8: ldr             x16, [x16, #0xc30]
    // 0xb566dc: StoreField: r1->field_f = r16
    //     0xb566dc: stur            w16, [x1, #0xf]
    // 0xb566e0: ldur            x0, [fp, #-0x28]
    // 0xb566e4: cmp             w0, NULL
    // 0xb566e8: b.ne            #0xb566f4
    // 0xb566ec: r0 = Null
    //     0xb566ec: mov             x0, NULL
    // 0xb566f0: b               #0xb5671c
    // 0xb566f4: LoadField: r2 = r0->field_27
    //     0xb566f4: ldur            w2, [x0, #0x27]
    // 0xb566f8: DecompressPointer r2
    //     0xb566f8: add             x2, x2, HEAP, lsl #32
    // 0xb566fc: r0 = LoadClassIdInstr(r2)
    //     0xb566fc: ldur            x0, [x2, #-1]
    //     0xb56700: ubfx            x0, x0, #0xc, #0x14
    // 0xb56704: str             x2, [SP]
    // 0xb56708: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb56708: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb5670c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb5670c: movz            x17, #0x2700
    //     0xb56710: add             lr, x0, x17
    //     0xb56714: ldr             lr, [x21, lr, lsl #3]
    //     0xb56718: blr             lr
    // 0xb5671c: cmp             w0, NULL
    // 0xb56720: b.ne            #0xb56728
    // 0xb56724: r0 = ""
    //     0xb56724: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb56728: ldur            x3, [fp, #-8]
    // 0xb5672c: ldur            x2, [fp, #-0x30]
    // 0xb56730: ldur            x4, [fp, #-0x18]
    // 0xb56734: ldur            x1, [fp, #-0x38]
    // 0xb56738: ArrayStore: r1[1] = r0  ; List_4
    //     0xb56738: add             x25, x1, #0x13
    //     0xb5673c: str             w0, [x25]
    //     0xb56740: tbz             w0, #0, #0xb5675c
    //     0xb56744: ldurb           w16, [x1, #-1]
    //     0xb56748: ldurb           w17, [x0, #-1]
    //     0xb5674c: and             x16, x17, x16, lsr #2
    //     0xb56750: tst             x16, HEAP, lsr #32
    //     0xb56754: b.eq            #0xb5675c
    //     0xb56758: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5675c: ldur            x16, [fp, #-0x38]
    // 0xb56760: str             x16, [SP]
    // 0xb56764: r0 = _interpolate()
    //     0xb56764: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb56768: ldur            x1, [fp, #-0x10]
    // 0xb5676c: stur            x0, [fp, #-0x10]
    // 0xb56770: r0 = of()
    //     0xb56770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb56774: LoadField: r1 = r0->field_87
    //     0xb56774: ldur            w1, [x0, #0x87]
    // 0xb56778: DecompressPointer r1
    //     0xb56778: add             x1, x1, HEAP, lsl #32
    // 0xb5677c: LoadField: r0 = r1->field_2b
    //     0xb5677c: ldur            w0, [x1, #0x2b]
    // 0xb56780: DecompressPointer r0
    //     0xb56780: add             x0, x0, HEAP, lsl #32
    // 0xb56784: stur            x0, [fp, #-0x28]
    // 0xb56788: r1 = Instance_Color
    //     0xb56788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5678c: d0 = 0.700000
    //     0xb5678c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb56790: ldr             d0, [x17, #0xf48]
    // 0xb56794: r0 = withOpacity()
    //     0xb56794: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb56798: r16 = 14.000000
    //     0xb56798: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5679c: ldr             x16, [x16, #0x1d8]
    // 0xb567a0: stp             x16, x0, [SP]
    // 0xb567a4: ldur            x1, [fp, #-0x28]
    // 0xb567a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb567a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb567ac: ldr             x4, [x4, #0x9b8]
    // 0xb567b0: r0 = copyWith()
    //     0xb567b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb567b4: stur            x0, [fp, #-0x28]
    // 0xb567b8: r0 = Text()
    //     0xb567b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb567bc: mov             x1, x0
    // 0xb567c0: ldur            x0, [fp, #-0x10]
    // 0xb567c4: stur            x1, [fp, #-0x38]
    // 0xb567c8: StoreField: r1->field_b = r0
    //     0xb567c8: stur            w0, [x1, #0xb]
    // 0xb567cc: ldur            x0, [fp, #-0x28]
    // 0xb567d0: StoreField: r1->field_13 = r0
    //     0xb567d0: stur            w0, [x1, #0x13]
    // 0xb567d4: r0 = Visibility()
    //     0xb567d4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb567d8: mov             x3, x0
    // 0xb567dc: ldur            x0, [fp, #-0x38]
    // 0xb567e0: stur            x3, [fp, #-0x10]
    // 0xb567e4: StoreField: r3->field_b = r0
    //     0xb567e4: stur            w0, [x3, #0xb]
    // 0xb567e8: r0 = Instance_SizedBox
    //     0xb567e8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb567ec: StoreField: r3->field_f = r0
    //     0xb567ec: stur            w0, [x3, #0xf]
    // 0xb567f0: ldur            x0, [fp, #-0x18]
    // 0xb567f4: StoreField: r3->field_13 = r0
    //     0xb567f4: stur            w0, [x3, #0x13]
    // 0xb567f8: r0 = false
    //     0xb567f8: add             x0, NULL, #0x30  ; false
    // 0xb567fc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb567fc: stur            w0, [x3, #0x17]
    // 0xb56800: StoreField: r3->field_1b = r0
    //     0xb56800: stur            w0, [x3, #0x1b]
    // 0xb56804: StoreField: r3->field_1f = r0
    //     0xb56804: stur            w0, [x3, #0x1f]
    // 0xb56808: StoreField: r3->field_23 = r0
    //     0xb56808: stur            w0, [x3, #0x23]
    // 0xb5680c: StoreField: r3->field_27 = r0
    //     0xb5680c: stur            w0, [x3, #0x27]
    // 0xb56810: StoreField: r3->field_2b = r0
    //     0xb56810: stur            w0, [x3, #0x2b]
    // 0xb56814: r1 = Null
    //     0xb56814: mov             x1, NULL
    // 0xb56818: r2 = 6
    //     0xb56818: movz            x2, #0x6
    // 0xb5681c: r0 = AllocateArray()
    //     0xb5681c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb56820: mov             x2, x0
    // 0xb56824: ldur            x0, [fp, #-0x30]
    // 0xb56828: stur            x2, [fp, #-0x18]
    // 0xb5682c: StoreField: r2->field_f = r0
    //     0xb5682c: stur            w0, [x2, #0xf]
    // 0xb56830: r16 = Instance_Spacer
    //     0xb56830: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb56834: ldr             x16, [x16, #0xf0]
    // 0xb56838: StoreField: r2->field_13 = r16
    //     0xb56838: stur            w16, [x2, #0x13]
    // 0xb5683c: ldur            x0, [fp, #-0x10]
    // 0xb56840: ArrayStore: r2[0] = r0  ; List_4
    //     0xb56840: stur            w0, [x2, #0x17]
    // 0xb56844: r1 = <Widget>
    //     0xb56844: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb56848: r0 = AllocateGrowableArray()
    //     0xb56848: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5684c: mov             x1, x0
    // 0xb56850: ldur            x0, [fp, #-0x18]
    // 0xb56854: stur            x1, [fp, #-0x10]
    // 0xb56858: StoreField: r1->field_f = r0
    //     0xb56858: stur            w0, [x1, #0xf]
    // 0xb5685c: r0 = 6
    //     0xb5685c: movz            x0, #0x6
    // 0xb56860: StoreField: r1->field_b = r0
    //     0xb56860: stur            w0, [x1, #0xb]
    // 0xb56864: r0 = Row()
    //     0xb56864: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb56868: mov             x1, x0
    // 0xb5686c: r0 = Instance_Axis
    //     0xb5686c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb56870: stur            x1, [fp, #-0x18]
    // 0xb56874: StoreField: r1->field_f = r0
    //     0xb56874: stur            w0, [x1, #0xf]
    // 0xb56878: r0 = Instance_MainAxisAlignment
    //     0xb56878: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5687c: ldr             x0, [x0, #0xa08]
    // 0xb56880: StoreField: r1->field_13 = r0
    //     0xb56880: stur            w0, [x1, #0x13]
    // 0xb56884: r2 = Instance_MainAxisSize
    //     0xb56884: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb56888: ldr             x2, [x2, #0xa10]
    // 0xb5688c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb5688c: stur            w2, [x1, #0x17]
    // 0xb56890: r3 = Instance_CrossAxisAlignment
    //     0xb56890: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb56894: ldr             x3, [x3, #0xa18]
    // 0xb56898: StoreField: r1->field_1b = r3
    //     0xb56898: stur            w3, [x1, #0x1b]
    // 0xb5689c: r3 = Instance_VerticalDirection
    //     0xb5689c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb568a0: ldr             x3, [x3, #0xa20]
    // 0xb568a4: StoreField: r1->field_23 = r3
    //     0xb568a4: stur            w3, [x1, #0x23]
    // 0xb568a8: r4 = Instance_Clip
    //     0xb568a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb568ac: ldr             x4, [x4, #0x38]
    // 0xb568b0: StoreField: r1->field_2b = r4
    //     0xb568b0: stur            w4, [x1, #0x2b]
    // 0xb568b4: StoreField: r1->field_2f = rZR
    //     0xb568b4: stur            xzr, [x1, #0x2f]
    // 0xb568b8: ldur            x5, [fp, #-0x10]
    // 0xb568bc: StoreField: r1->field_b = r5
    //     0xb568bc: stur            w5, [x1, #0xb]
    // 0xb568c0: r0 = Padding()
    //     0xb568c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb568c4: mov             x2, x0
    // 0xb568c8: r0 = Instance_EdgeInsets
    //     0xb568c8: add             x0, PP, #0x33, lsl #12  ; [pp+0x33788] Obj!EdgeInsets@d58e21
    //     0xb568cc: ldr             x0, [x0, #0x788]
    // 0xb568d0: stur            x2, [fp, #-0x10]
    // 0xb568d4: StoreField: r2->field_f = r0
    //     0xb568d4: stur            w0, [x2, #0xf]
    // 0xb568d8: ldur            x0, [fp, #-0x18]
    // 0xb568dc: StoreField: r2->field_b = r0
    //     0xb568dc: stur            w0, [x2, #0xb]
    // 0xb568e0: ldur            x0, [fp, #-8]
    // 0xb568e4: LoadField: r1 = r0->field_13
    //     0xb568e4: ldur            w1, [x0, #0x13]
    // 0xb568e8: DecompressPointer r1
    //     0xb568e8: add             x1, x1, HEAP, lsl #32
    // 0xb568ec: stur            x1, [fp, #-0x30]
    // 0xb568f0: cmp             w1, NULL
    // 0xb568f4: b.ne            #0xb56a80
    // 0xb568f8: r1 = Instance_Color
    //     0xb568f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb568fc: d0 = 0.040000
    //     0xb568fc: ldr             d0, [PP, #0x54b0]  ; [pp+0x54b0] IMM: double(0.04) from 0x3fa47ae147ae147b
    // 0xb56900: r0 = withOpacity()
    //     0xb56900: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb56904: stur            x0, [fp, #-8]
    // 0xb56908: r0 = Radius()
    //     0xb56908: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb5690c: d0 = 15.000000
    //     0xb5690c: fmov            d0, #15.00000000
    // 0xb56910: stur            x0, [fp, #-0x18]
    // 0xb56914: StoreField: r0->field_7 = d0
    //     0xb56914: stur            d0, [x0, #7]
    // 0xb56918: StoreField: r0->field_f = d0
    //     0xb56918: stur            d0, [x0, #0xf]
    // 0xb5691c: r0 = BorderRadius()
    //     0xb5691c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb56920: mov             x1, x0
    // 0xb56924: ldur            x0, [fp, #-0x18]
    // 0xb56928: stur            x1, [fp, #-0x28]
    // 0xb5692c: StoreField: r1->field_7 = r0
    //     0xb5692c: stur            w0, [x1, #7]
    // 0xb56930: StoreField: r1->field_b = r0
    //     0xb56930: stur            w0, [x1, #0xb]
    // 0xb56934: StoreField: r1->field_f = r0
    //     0xb56934: stur            w0, [x1, #0xf]
    // 0xb56938: StoreField: r1->field_13 = r0
    //     0xb56938: stur            w0, [x1, #0x13]
    // 0xb5693c: r0 = BoxDecoration()
    //     0xb5693c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb56940: mov             x2, x0
    // 0xb56944: ldur            x0, [fp, #-8]
    // 0xb56948: stur            x2, [fp, #-0x18]
    // 0xb5694c: StoreField: r2->field_7 = r0
    //     0xb5694c: stur            w0, [x2, #7]
    // 0xb56950: ldur            x0, [fp, #-0x28]
    // 0xb56954: StoreField: r2->field_13 = r0
    //     0xb56954: stur            w0, [x2, #0x13]
    // 0xb56958: r0 = Instance_BoxShape
    //     0xb56958: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5695c: ldr             x0, [x0, #0x80]
    // 0xb56960: StoreField: r2->field_23 = r0
    //     0xb56960: stur            w0, [x2, #0x23]
    // 0xb56964: r1 = Instance_Color
    //     0xb56964: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb56968: d0 = 0.200000
    //     0xb56968: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb5696c: r0 = withOpacity()
    //     0xb5696c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb56970: stur            x0, [fp, #-8]
    // 0xb56974: r0 = Icon()
    //     0xb56974: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb56978: mov             x3, x0
    // 0xb5697c: r0 = Instance_IconData
    //     0xb5697c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36aa0] Obj!IconData@d555c1
    //     0xb56980: ldr             x0, [x0, #0xaa0]
    // 0xb56984: stur            x3, [fp, #-0x28]
    // 0xb56988: StoreField: r3->field_b = r0
    //     0xb56988: stur            w0, [x3, #0xb]
    // 0xb5698c: r0 = 25.000000
    //     0xb5698c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0xb56990: ldr             x0, [x0, #0x98]
    // 0xb56994: StoreField: r3->field_f = r0
    //     0xb56994: stur            w0, [x3, #0xf]
    // 0xb56998: ldur            x0, [fp, #-8]
    // 0xb5699c: StoreField: r3->field_23 = r0
    //     0xb5699c: stur            w0, [x3, #0x23]
    // 0xb569a0: r1 = Null
    //     0xb569a0: mov             x1, NULL
    // 0xb569a4: r2 = 4
    //     0xb569a4: movz            x2, #0x4
    // 0xb569a8: r0 = AllocateArray()
    //     0xb569a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb569ac: mov             x2, x0
    // 0xb569b0: ldur            x0, [fp, #-0x28]
    // 0xb569b4: stur            x2, [fp, #-8]
    // 0xb569b8: StoreField: r2->field_f = r0
    //     0xb569b8: stur            w0, [x2, #0xf]
    // 0xb569bc: r16 = Instance_Icon
    //     0xb569bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36aa8] Obj!Icon@d66331
    //     0xb569c0: ldr             x16, [x16, #0xaa8]
    // 0xb569c4: StoreField: r2->field_13 = r16
    //     0xb569c4: stur            w16, [x2, #0x13]
    // 0xb569c8: r1 = <Widget>
    //     0xb569c8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb569cc: r0 = AllocateGrowableArray()
    //     0xb569cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb569d0: mov             x1, x0
    // 0xb569d4: ldur            x0, [fp, #-8]
    // 0xb569d8: stur            x1, [fp, #-0x28]
    // 0xb569dc: StoreField: r1->field_f = r0
    //     0xb569dc: stur            w0, [x1, #0xf]
    // 0xb569e0: r2 = 4
    //     0xb569e0: movz            x2, #0x4
    // 0xb569e4: StoreField: r1->field_b = r2
    //     0xb569e4: stur            w2, [x1, #0xb]
    // 0xb569e8: r0 = Stack()
    //     0xb569e8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb569ec: mov             x1, x0
    // 0xb569f0: r0 = Instance_Alignment
    //     0xb569f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0xb569f4: ldr             x0, [x0, #0xa28]
    // 0xb569f8: stur            x1, [fp, #-8]
    // 0xb569fc: StoreField: r1->field_f = r0
    //     0xb569fc: stur            w0, [x1, #0xf]
    // 0xb56a00: r0 = Instance_StackFit
    //     0xb56a00: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb56a04: ldr             x0, [x0, #0xfa8]
    // 0xb56a08: ArrayStore: r1[0] = r0  ; List_4
    //     0xb56a08: stur            w0, [x1, #0x17]
    // 0xb56a0c: r0 = Instance_Clip
    //     0xb56a0c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb56a10: ldr             x0, [x0, #0x7e0]
    // 0xb56a14: StoreField: r1->field_1b = r0
    //     0xb56a14: stur            w0, [x1, #0x1b]
    // 0xb56a18: ldur            x0, [fp, #-0x28]
    // 0xb56a1c: StoreField: r1->field_b = r0
    //     0xb56a1c: stur            w0, [x1, #0xb]
    // 0xb56a20: r0 = Center()
    //     0xb56a20: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb56a24: mov             x1, x0
    // 0xb56a28: r0 = Instance_Alignment
    //     0xb56a28: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb56a2c: ldr             x0, [x0, #0xb10]
    // 0xb56a30: stur            x1, [fp, #-0x28]
    // 0xb56a34: StoreField: r1->field_f = r0
    //     0xb56a34: stur            w0, [x1, #0xf]
    // 0xb56a38: ldur            x0, [fp, #-8]
    // 0xb56a3c: StoreField: r1->field_b = r0
    //     0xb56a3c: stur            w0, [x1, #0xb]
    // 0xb56a40: r0 = Container()
    //     0xb56a40: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb56a44: stur            x0, [fp, #-8]
    // 0xb56a48: r16 = 60.000000
    //     0xb56a48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb56a4c: ldr             x16, [x16, #0x110]
    // 0xb56a50: r30 = 60.000000
    //     0xb56a50: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xb56a54: ldr             lr, [lr, #0x110]
    // 0xb56a58: stp             lr, x16, [SP, #0x10]
    // 0xb56a5c: ldur            x16, [fp, #-0x18]
    // 0xb56a60: ldur            lr, [fp, #-0x28]
    // 0xb56a64: stp             lr, x16, [SP]
    // 0xb56a68: mov             x1, x0
    // 0xb56a6c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb56a6c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb56a70: ldr             x4, [x4, #0x870]
    // 0xb56a74: r0 = Container()
    //     0xb56a74: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb56a78: ldur            x1, [fp, #-8]
    // 0xb56a7c: b               #0xb56b40
    // 0xb56a80: d0 = 15.000000
    //     0xb56a80: fmov            d0, #15.00000000
    // 0xb56a84: r0 = Radius()
    //     0xb56a84: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb56a88: d0 = 15.000000
    //     0xb56a88: fmov            d0, #15.00000000
    // 0xb56a8c: stur            x0, [fp, #-8]
    // 0xb56a90: StoreField: r0->field_7 = d0
    //     0xb56a90: stur            d0, [x0, #7]
    // 0xb56a94: StoreField: r0->field_f = d0
    //     0xb56a94: stur            d0, [x0, #0xf]
    // 0xb56a98: r0 = BorderRadius()
    //     0xb56a98: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb56a9c: mov             x1, x0
    // 0xb56aa0: ldur            x0, [fp, #-8]
    // 0xb56aa4: stur            x1, [fp, #-0x18]
    // 0xb56aa8: StoreField: r1->field_7 = r0
    //     0xb56aa8: stur            w0, [x1, #7]
    // 0xb56aac: StoreField: r1->field_b = r0
    //     0xb56aac: stur            w0, [x1, #0xb]
    // 0xb56ab0: StoreField: r1->field_f = r0
    //     0xb56ab0: stur            w0, [x1, #0xf]
    // 0xb56ab4: StoreField: r1->field_13 = r0
    //     0xb56ab4: stur            w0, [x1, #0x13]
    // 0xb56ab8: ldur            x0, [fp, #-0x30]
    // 0xb56abc: LoadField: r2 = r0->field_7
    //     0xb56abc: ldur            w2, [x0, #7]
    // 0xb56ac0: DecompressPointer r2
    //     0xb56ac0: add             x2, x2, HEAP, lsl #32
    // 0xb56ac4: stur            x2, [fp, #-8]
    // 0xb56ac8: r0 = current()
    //     0xb56ac8: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0xb56acc: r0 = _File()
    //     0xb56acc: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0xb56ad0: ldur            x1, [fp, #-8]
    // 0xb56ad4: stur            x0, [fp, #-0x28]
    // 0xb56ad8: StoreField: r0->field_7 = r1
    //     0xb56ad8: stur            w1, [x0, #7]
    // 0xb56adc: r0 = _toUtf8Array()
    //     0xb56adc: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0xb56ae0: ldur            x2, [fp, #-0x28]
    // 0xb56ae4: StoreField: r2->field_b = r0
    //     0xb56ae4: stur            w0, [x2, #0xb]
    //     0xb56ae8: ldurb           w16, [x2, #-1]
    //     0xb56aec: ldurb           w17, [x0, #-1]
    //     0xb56af0: and             x16, x17, x16, lsr #2
    //     0xb56af4: tst             x16, HEAP, lsr #32
    //     0xb56af8: b.eq            #0xb56b00
    //     0xb56afc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb56b00: r0 = Image()
    //     0xb56b00: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xb56b04: mov             x1, x0
    // 0xb56b08: ldur            x2, [fp, #-0x28]
    // 0xb56b0c: d0 = 60.000000
    //     0xb56b0c: ldr             d0, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0xb56b10: d1 = 60.000000
    //     0xb56b10: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0xb56b14: stur            x0, [fp, #-8]
    // 0xb56b18: r0 = Image.file()
    //     0xb56b18: bl              #0x9d34d0  ; [package:flutter/src/widgets/image.dart] Image::Image.file
    // 0xb56b1c: r0 = ClipRRect()
    //     0xb56b1c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb56b20: mov             x1, x0
    // 0xb56b24: ldur            x0, [fp, #-0x18]
    // 0xb56b28: StoreField: r1->field_f = r0
    //     0xb56b28: stur            w0, [x1, #0xf]
    // 0xb56b2c: r0 = Instance_Clip
    //     0xb56b2c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb56b30: ldr             x0, [x0, #0x138]
    // 0xb56b34: ArrayStore: r1[0] = r0  ; List_4
    //     0xb56b34: stur            w0, [x1, #0x17]
    // 0xb56b38: ldur            x0, [fp, #-8]
    // 0xb56b3c: StoreField: r1->field_b = r0
    //     0xb56b3c: stur            w0, [x1, #0xb]
    // 0xb56b40: ldur            x0, [fp, #-0x10]
    // 0xb56b44: stur            x1, [fp, #-8]
    // 0xb56b48: r0 = InkWell()
    //     0xb56b48: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb56b4c: mov             x3, x0
    // 0xb56b50: ldur            x0, [fp, #-8]
    // 0xb56b54: stur            x3, [fp, #-0x18]
    // 0xb56b58: StoreField: r3->field_b = r0
    //     0xb56b58: stur            w0, [x3, #0xb]
    // 0xb56b5c: ldur            x2, [fp, #-0x20]
    // 0xb56b60: r1 = Function '<anonymous closure>':.
    //     0xb56b60: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa40] AnonymousClosure: (0xb56c94), in [package:customer_app/app/presentation/views/glass/customization/camer_picker.dart] _CameraPickerState::build (0xb56520)
    //     0xb56b64: ldr             x1, [x1, #0xa40]
    // 0xb56b68: r0 = AllocateClosure()
    //     0xb56b68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb56b6c: mov             x1, x0
    // 0xb56b70: ldur            x0, [fp, #-0x18]
    // 0xb56b74: StoreField: r0->field_f = r1
    //     0xb56b74: stur            w1, [x0, #0xf]
    // 0xb56b78: r1 = true
    //     0xb56b78: add             x1, NULL, #0x20  ; true
    // 0xb56b7c: StoreField: r0->field_43 = r1
    //     0xb56b7c: stur            w1, [x0, #0x43]
    // 0xb56b80: r2 = Instance_BoxShape
    //     0xb56b80: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb56b84: ldr             x2, [x2, #0x80]
    // 0xb56b88: StoreField: r0->field_47 = r2
    //     0xb56b88: stur            w2, [x0, #0x47]
    // 0xb56b8c: StoreField: r0->field_6f = r1
    //     0xb56b8c: stur            w1, [x0, #0x6f]
    // 0xb56b90: r2 = false
    //     0xb56b90: add             x2, NULL, #0x30  ; false
    // 0xb56b94: StoreField: r0->field_73 = r2
    //     0xb56b94: stur            w2, [x0, #0x73]
    // 0xb56b98: StoreField: r0->field_83 = r1
    //     0xb56b98: stur            w1, [x0, #0x83]
    // 0xb56b9c: StoreField: r0->field_7b = r2
    //     0xb56b9c: stur            w2, [x0, #0x7b]
    // 0xb56ba0: r1 = Null
    //     0xb56ba0: mov             x1, NULL
    // 0xb56ba4: r2 = 4
    //     0xb56ba4: movz            x2, #0x4
    // 0xb56ba8: r0 = AllocateArray()
    //     0xb56ba8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb56bac: mov             x2, x0
    // 0xb56bb0: ldur            x0, [fp, #-0x10]
    // 0xb56bb4: stur            x2, [fp, #-8]
    // 0xb56bb8: StoreField: r2->field_f = r0
    //     0xb56bb8: stur            w0, [x2, #0xf]
    // 0xb56bbc: ldur            x0, [fp, #-0x18]
    // 0xb56bc0: StoreField: r2->field_13 = r0
    //     0xb56bc0: stur            w0, [x2, #0x13]
    // 0xb56bc4: r1 = <Widget>
    //     0xb56bc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb56bc8: r0 = AllocateGrowableArray()
    //     0xb56bc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb56bcc: mov             x1, x0
    // 0xb56bd0: ldur            x0, [fp, #-8]
    // 0xb56bd4: stur            x1, [fp, #-0x10]
    // 0xb56bd8: StoreField: r1->field_f = r0
    //     0xb56bd8: stur            w0, [x1, #0xf]
    // 0xb56bdc: r0 = 4
    //     0xb56bdc: movz            x0, #0x4
    // 0xb56be0: StoreField: r1->field_b = r0
    //     0xb56be0: stur            w0, [x1, #0xb]
    // 0xb56be4: r0 = Column()
    //     0xb56be4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb56be8: mov             x1, x0
    // 0xb56bec: r0 = Instance_Axis
    //     0xb56bec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb56bf0: stur            x1, [fp, #-8]
    // 0xb56bf4: StoreField: r1->field_f = r0
    //     0xb56bf4: stur            w0, [x1, #0xf]
    // 0xb56bf8: r0 = Instance_MainAxisAlignment
    //     0xb56bf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb56bfc: ldr             x0, [x0, #0xa08]
    // 0xb56c00: StoreField: r1->field_13 = r0
    //     0xb56c00: stur            w0, [x1, #0x13]
    // 0xb56c04: r0 = Instance_MainAxisSize
    //     0xb56c04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb56c08: ldr             x0, [x0, #0xa10]
    // 0xb56c0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb56c0c: stur            w0, [x1, #0x17]
    // 0xb56c10: r0 = Instance_CrossAxisAlignment
    //     0xb56c10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb56c14: ldr             x0, [x0, #0x890]
    // 0xb56c18: StoreField: r1->field_1b = r0
    //     0xb56c18: stur            w0, [x1, #0x1b]
    // 0xb56c1c: r0 = Instance_VerticalDirection
    //     0xb56c1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb56c20: ldr             x0, [x0, #0xa20]
    // 0xb56c24: StoreField: r1->field_23 = r0
    //     0xb56c24: stur            w0, [x1, #0x23]
    // 0xb56c28: r0 = Instance_Clip
    //     0xb56c28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb56c2c: ldr             x0, [x0, #0x38]
    // 0xb56c30: StoreField: r1->field_2b = r0
    //     0xb56c30: stur            w0, [x1, #0x2b]
    // 0xb56c34: StoreField: r1->field_2f = rZR
    //     0xb56c34: stur            xzr, [x1, #0x2f]
    // 0xb56c38: ldur            x0, [fp, #-0x10]
    // 0xb56c3c: StoreField: r1->field_b = r0
    //     0xb56c3c: stur            w0, [x1, #0xb]
    // 0xb56c40: r0 = Padding()
    //     0xb56c40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb56c44: r1 = Instance_EdgeInsets
    //     0xb56c44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb56c48: ldr             x1, [x1, #0x1f0]
    // 0xb56c4c: StoreField: r0->field_f = r1
    //     0xb56c4c: stur            w1, [x0, #0xf]
    // 0xb56c50: ldur            x1, [fp, #-8]
    // 0xb56c54: StoreField: r0->field_b = r1
    //     0xb56c54: stur            w1, [x0, #0xb]
    // 0xb56c58: LeaveFrame
    //     0xb56c58: mov             SP, fp
    //     0xb56c5c: ldp             fp, lr, [SP], #0x10
    // 0xb56c60: ret
    //     0xb56c60: ret             
    // 0xb56c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56c64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56c68: b               #0xb56548
    // 0xb56c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb56c6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb56c70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb56c70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb56c94, size: 0xac
    // 0xb56c94: EnterFrame
    //     0xb56c94: stp             fp, lr, [SP, #-0x10]!
    //     0xb56c98: mov             fp, SP
    // 0xb56c9c: AllocStack(0x38)
    //     0xb56c9c: sub             SP, SP, #0x38
    // 0xb56ca0: SetupParameters()
    //     0xb56ca0: ldr             x0, [fp, #0x10]
    //     0xb56ca4: ldur            w2, [x0, #0x17]
    //     0xb56ca8: add             x2, x2, HEAP, lsl #32
    //     0xb56cac: stur            x2, [fp, #-8]
    // 0xb56cb0: CheckStackOverflow
    //     0xb56cb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56cb4: cmp             SP, x16
    //     0xb56cb8: b.ls            #0xb56d38
    // 0xb56cbc: LoadField: r1 = r2->field_f
    //     0xb56cbc: ldur            w1, [x2, #0xf]
    // 0xb56cc0: DecompressPointer r1
    //     0xb56cc0: add             x1, x1, HEAP, lsl #32
    // 0xb56cc4: r0 = _getImageFromGallery()
    //     0xb56cc4: bl              #0xb56d40  ; [package:customer_app/app/presentation/views/glass/customization/camer_picker.dart] _CameraPickerState::_getImageFromGallery
    // 0xb56cc8: stur            x0, [fp, #-0x18]
    // 0xb56ccc: LoadField: r3 = r0->field_7
    //     0xb56ccc: ldur            w3, [x0, #7]
    // 0xb56cd0: DecompressPointer r3
    //     0xb56cd0: add             x3, x3, HEAP, lsl #32
    // 0xb56cd4: ldur            x2, [fp, #-8]
    // 0xb56cd8: stur            x3, [fp, #-0x10]
    // 0xb56cdc: r1 = Function '<anonymous closure>':.
    //     0xb56cdc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa48] AnonymousClosure: (0xb5747c), in [package:customer_app/app/presentation/views/glass/customization/camer_picker.dart] _CameraPickerState::build (0xb56520)
    //     0xb56ce0: ldr             x1, [x1, #0xa48]
    // 0xb56ce4: r0 = AllocateClosure()
    //     0xb56ce4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb56ce8: ldur            x2, [fp, #-0x10]
    // 0xb56cec: mov             x3, x0
    // 0xb56cf0: r1 = Null
    //     0xb56cf0: mov             x1, NULL
    // 0xb56cf4: stur            x3, [fp, #-8]
    // 0xb56cf8: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0xb56cf8: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0xb56cfc: ldr             x8, [x8, #0xce8]
    // 0xb56d00: LoadField: r9 = r8->field_7
    //     0xb56d00: ldur            x9, [x8, #7]
    // 0xb56d04: r3 = Null
    //     0xb56d04: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6aa50] Null
    //     0xb56d08: ldr             x3, [x3, #0xa50]
    // 0xb56d0c: blr             x9
    // 0xb56d10: ldur            x16, [fp, #-0x18]
    // 0xb56d14: stp             x16, NULL, [SP, #0x10]
    // 0xb56d18: ldur            x16, [fp, #-8]
    // 0xb56d1c: stp             NULL, x16, [SP]
    // 0xb56d20: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0xb56d20: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0xb56d24: r0 = then()
    //     0xb56d24: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb56d28: r0 = Null
    //     0xb56d28: mov             x0, NULL
    // 0xb56d2c: LeaveFrame
    //     0xb56d2c: mov             SP, fp
    //     0xb56d30: ldp             fp, lr, [SP], #0x10
    // 0xb56d34: ret
    //     0xb56d34: ret             
    // 0xb56d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56d38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56d3c: b               #0xb56cbc
  }
  _ _getImageFromGallery(/* No info */) async {
    // ** addr: 0xb56d40, size: 0x1e4
    // 0xb56d40: EnterFrame
    //     0xb56d40: stp             fp, lr, [SP, #-0x10]!
    //     0xb56d44: mov             fp, SP
    // 0xb56d48: AllocStack(0x38)
    //     0xb56d48: sub             SP, SP, #0x38
    // 0xb56d4c: SetupParameters(_CameraPickerState this /* r1 => r1, fp-0x10 */)
    //     0xb56d4c: stur            NULL, [fp, #-8]
    //     0xb56d50: stur            x1, [fp, #-0x10]
    // 0xb56d54: CheckStackOverflow
    //     0xb56d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56d58: cmp             SP, x16
    //     0xb56d5c: b.ls            #0xb56f18
    // 0xb56d60: r1 = 2
    //     0xb56d60: movz            x1, #0x2
    // 0xb56d64: r0 = AllocateContext()
    //     0xb56d64: bl              #0x16f6108  ; AllocateContextStub
    // 0xb56d68: mov             x1, x0
    // 0xb56d6c: ldur            x0, [fp, #-0x10]
    // 0xb56d70: stur            x1, [fp, #-0x18]
    // 0xb56d74: StoreField: r1->field_f = r0
    //     0xb56d74: stur            w0, [x1, #0xf]
    // 0xb56d78: InitAsync() -> Future
    //     0xb56d78: mov             x0, NULL
    //     0xb56d7c: bl              #0x6326e0  ; InitAsyncStub
    // 0xb56d80: r0 = ImagePicker()
    //     0xb56d80: bl              #0x9a787c  ; AllocateImagePickerStub -> ImagePicker (size=0x8)
    // 0xb56d84: r16 = 1800.000000
    //     0xb56d84: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a540] 1800
    //     0xb56d88: ldr             x16, [x16, #0x540]
    // 0xb56d8c: r30 = 1800.000000
    //     0xb56d8c: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6a540] 1800
    //     0xb56d90: ldr             lr, [lr, #0x540]
    // 0xb56d94: stp             lr, x16, [SP]
    // 0xb56d98: mov             x1, x0
    // 0xb56d9c: r4 = const [0, 0x3, 0x2, 0x1, maxHeight, 0x2, maxWidth, 0x1, null]
    //     0xb56d9c: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a548] List(9) [0, 0x3, 0x2, 0x1, "maxHeight", 0x2, "maxWidth", 0x1, Null]
    //     0xb56da0: ldr             x4, [x4, #0x548]
    // 0xb56da4: r0 = pickImage()
    //     0xb56da4: bl              #0xa302d8  ; [package:image_picker/image_picker.dart] ImagePicker::pickImage
    // 0xb56da8: mov             x1, x0
    // 0xb56dac: stur            x1, [fp, #-0x10]
    // 0xb56db0: r0 = Await()
    //     0xb56db0: bl              #0x63248c  ; AwaitStub
    // 0xb56db4: cmp             w0, NULL
    // 0xb56db8: b.eq            #0xb56f10
    // 0xb56dbc: ldur            x2, [fp, #-0x18]
    // 0xb56dc0: LoadField: r1 = r0->field_7
    //     0xb56dc0: ldur            w1, [x0, #7]
    // 0xb56dc4: DecompressPointer r1
    //     0xb56dc4: add             x1, x1, HEAP, lsl #32
    // 0xb56dc8: LoadField: r0 = r1->field_7
    //     0xb56dc8: ldur            w0, [x1, #7]
    // 0xb56dcc: DecompressPointer r0
    //     0xb56dcc: add             x0, x0, HEAP, lsl #32
    // 0xb56dd0: stur            x0, [fp, #-0x10]
    // 0xb56dd4: r0 = current()
    //     0xb56dd4: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0xb56dd8: r0 = _File()
    //     0xb56dd8: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0xb56ddc: ldur            x1, [fp, #-0x10]
    // 0xb56de0: stur            x0, [fp, #-0x20]
    // 0xb56de4: StoreField: r0->field_7 = r1
    //     0xb56de4: stur            w1, [x0, #7]
    // 0xb56de8: r0 = _toUtf8Array()
    //     0xb56de8: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0xb56dec: ldur            x1, [fp, #-0x20]
    // 0xb56df0: StoreField: r1->field_b = r0
    //     0xb56df0: stur            w0, [x1, #0xb]
    //     0xb56df4: ldurb           w16, [x1, #-1]
    //     0xb56df8: ldurb           w17, [x0, #-1]
    //     0xb56dfc: and             x16, x17, x16, lsr #2
    //     0xb56e00: tst             x16, HEAP, lsr #32
    //     0xb56e04: b.eq            #0xb56e0c
    //     0xb56e08: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb56e0c: mov             x0, x1
    // 0xb56e10: ldur            x2, [fp, #-0x18]
    // 0xb56e14: StoreField: r2->field_13 = r0
    //     0xb56e14: stur            w0, [x2, #0x13]
    //     0xb56e18: ldurb           w16, [x2, #-1]
    //     0xb56e1c: ldurb           w17, [x0, #-1]
    //     0xb56e20: and             x16, x17, x16, lsr #2
    //     0xb56e24: tst             x16, HEAP, lsr #32
    //     0xb56e28: b.eq            #0xb56e30
    //     0xb56e2c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb56e30: r0 = LoadStaticField(0x878)
    //     0xb56e30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb56e34: ldr             x0, [x0, #0x10f0]
    // 0xb56e38: cmp             w0, NULL
    // 0xb56e3c: b.eq            #0xb56f20
    // 0xb56e40: LoadField: r3 = r0->field_53
    //     0xb56e40: ldur            w3, [x0, #0x53]
    // 0xb56e44: DecompressPointer r3
    //     0xb56e44: add             x3, x3, HEAP, lsl #32
    // 0xb56e48: stur            x3, [fp, #-0x20]
    // 0xb56e4c: LoadField: r0 = r3->field_7
    //     0xb56e4c: ldur            w0, [x3, #7]
    // 0xb56e50: DecompressPointer r0
    //     0xb56e50: add             x0, x0, HEAP, lsl #32
    // 0xb56e54: stur            x0, [fp, #-0x10]
    // 0xb56e58: r1 = Function '<anonymous closure>':.
    //     0xb56e58: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa68] AnonymousClosure: (0xb56f24), in [package:customer_app/app/presentation/views/glass/customization/camer_picker.dart] _CameraPickerState::_getImageFromGallery (0xb56d40)
    //     0xb56e5c: ldr             x1, [x1, #0xa68]
    // 0xb56e60: r0 = AllocateClosure()
    //     0xb56e60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb56e64: ldur            x2, [fp, #-0x10]
    // 0xb56e68: mov             x3, x0
    // 0xb56e6c: r1 = Null
    //     0xb56e6c: mov             x1, NULL
    // 0xb56e70: stur            x3, [fp, #-0x10]
    // 0xb56e74: cmp             w2, NULL
    // 0xb56e78: b.eq            #0xb56e98
    // 0xb56e7c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xb56e7c: ldur            w4, [x2, #0x17]
    // 0xb56e80: DecompressPointer r4
    //     0xb56e80: add             x4, x4, HEAP, lsl #32
    // 0xb56e84: r8 = X0
    //     0xb56e84: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xb56e88: LoadField: r9 = r4->field_7
    //     0xb56e88: ldur            x9, [x4, #7]
    // 0xb56e8c: r3 = Null
    //     0xb56e8c: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6aa70] Null
    //     0xb56e90: ldr             x3, [x3, #0xa70]
    // 0xb56e94: blr             x9
    // 0xb56e98: ldur            x0, [fp, #-0x20]
    // 0xb56e9c: LoadField: r1 = r0->field_b
    //     0xb56e9c: ldur            w1, [x0, #0xb]
    // 0xb56ea0: LoadField: r2 = r0->field_f
    //     0xb56ea0: ldur            w2, [x0, #0xf]
    // 0xb56ea4: DecompressPointer r2
    //     0xb56ea4: add             x2, x2, HEAP, lsl #32
    // 0xb56ea8: LoadField: r3 = r2->field_b
    //     0xb56ea8: ldur            w3, [x2, #0xb]
    // 0xb56eac: r2 = LoadInt32Instr(r1)
    //     0xb56eac: sbfx            x2, x1, #1, #0x1f
    // 0xb56eb0: stur            x2, [fp, #-0x28]
    // 0xb56eb4: r1 = LoadInt32Instr(r3)
    //     0xb56eb4: sbfx            x1, x3, #1, #0x1f
    // 0xb56eb8: cmp             x2, x1
    // 0xb56ebc: b.ne            #0xb56ec8
    // 0xb56ec0: mov             x1, x0
    // 0xb56ec4: r0 = _growToNextCapacity()
    //     0xb56ec4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb56ec8: ldur            x2, [fp, #-0x20]
    // 0xb56ecc: ldur            x3, [fp, #-0x28]
    // 0xb56ed0: add             x4, x3, #1
    // 0xb56ed4: lsl             x5, x4, #1
    // 0xb56ed8: StoreField: r2->field_b = r5
    //     0xb56ed8: stur            w5, [x2, #0xb]
    // 0xb56edc: LoadField: r1 = r2->field_f
    //     0xb56edc: ldur            w1, [x2, #0xf]
    // 0xb56ee0: DecompressPointer r1
    //     0xb56ee0: add             x1, x1, HEAP, lsl #32
    // 0xb56ee4: ldur            x0, [fp, #-0x10]
    // 0xb56ee8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb56ee8: add             x25, x1, x3, lsl #2
    //     0xb56eec: add             x25, x25, #0xf
    //     0xb56ef0: str             w0, [x25]
    //     0xb56ef4: tbz             w0, #0, #0xb56f10
    //     0xb56ef8: ldurb           w16, [x1, #-1]
    //     0xb56efc: ldurb           w17, [x0, #-1]
    //     0xb56f00: and             x16, x17, x16, lsr #2
    //     0xb56f04: tst             x16, HEAP, lsr #32
    //     0xb56f08: b.eq            #0xb56f10
    //     0xb56f0c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb56f10: r0 = Null
    //     0xb56f10: mov             x0, NULL
    // 0xb56f14: r0 = ReturnAsyncNotFuture()
    //     0xb56f14: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xb56f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56f18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56f1c: b               #0xb56d60
    // 0xb56f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb56f20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xb56f24, size: 0x60
    // 0xb56f24: EnterFrame
    //     0xb56f24: stp             fp, lr, [SP, #-0x10]!
    //     0xb56f28: mov             fp, SP
    // 0xb56f2c: AllocStack(0x8)
    //     0xb56f2c: sub             SP, SP, #8
    // 0xb56f30: SetupParameters()
    //     0xb56f30: ldr             x0, [fp, #0x18]
    //     0xb56f34: ldur            w2, [x0, #0x17]
    //     0xb56f38: add             x2, x2, HEAP, lsl #32
    // 0xb56f3c: CheckStackOverflow
    //     0xb56f3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56f40: cmp             SP, x16
    //     0xb56f44: b.ls            #0xb56f7c
    // 0xb56f48: LoadField: r0 = r2->field_f
    //     0xb56f48: ldur            w0, [x2, #0xf]
    // 0xb56f4c: DecompressPointer r0
    //     0xb56f4c: add             x0, x0, HEAP, lsl #32
    // 0xb56f50: stur            x0, [fp, #-8]
    // 0xb56f54: r1 = Function '<anonymous closure>':.
    //     0xb56f54: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa80] AnonymousClosure: (0xb56f84), in [package:customer_app/app/presentation/views/glass/customization/camer_picker.dart] _CameraPickerState::_getImageFromGallery (0xb56d40)
    //     0xb56f58: ldr             x1, [x1, #0xa80]
    // 0xb56f5c: r0 = AllocateClosure()
    //     0xb56f5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb56f60: ldur            x1, [fp, #-8]
    // 0xb56f64: mov             x2, x0
    // 0xb56f68: r0 = setState()
    //     0xb56f68: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb56f6c: r0 = Null
    //     0xb56f6c: mov             x0, NULL
    // 0xb56f70: LeaveFrame
    //     0xb56f70: mov             SP, fp
    //     0xb56f74: ldp             fp, lr, [SP], #0x10
    // 0xb56f78: ret
    //     0xb56f78: ret             
    // 0xb56f7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb56f7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb56f80: b               #0xb56f48
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb56f84, size: 0x4f8
    // 0xb56f84: EnterFrame
    //     0xb56f84: stp             fp, lr, [SP, #-0x10]!
    //     0xb56f88: mov             fp, SP
    // 0xb56f8c: AllocStack(0x68)
    //     0xb56f8c: sub             SP, SP, #0x68
    // 0xb56f90: SetupParameters()
    //     0xb56f90: ldr             x0, [fp, #0x10]
    //     0xb56f94: ldur            w2, [x0, #0x17]
    //     0xb56f98: add             x2, x2, HEAP, lsl #32
    //     0xb56f9c: stur            x2, [fp, #-8]
    // 0xb56fa0: CheckStackOverflow
    //     0xb56fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb56fa4: cmp             SP, x16
    //     0xb56fa8: b.ls            #0xb5745c
    // 0xb56fac: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb56fac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb56fb0: ldr             x0, [x0]
    //     0xb56fb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb56fb8: cmp             w0, w16
    //     0xb56fbc: b.ne            #0xb56fc8
    //     0xb56fc0: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb56fc4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb56fc8: r1 = <CustomerResponse>
    //     0xb56fc8: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xb56fcc: ldr             x1, [x1, #0x5a8]
    // 0xb56fd0: stur            x0, [fp, #-0x10]
    // 0xb56fd4: r0 = AllocateGrowableArray()
    //     0xb56fd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb56fd8: mov             x2, x0
    // 0xb56fdc: ldur            x0, [fp, #-0x10]
    // 0xb56fe0: stur            x2, [fp, #-0x18]
    // 0xb56fe4: StoreField: r2->field_f = r0
    //     0xb56fe4: stur            w0, [x2, #0xf]
    // 0xb56fe8: StoreField: r2->field_b = rZR
    //     0xb56fe8: stur            wzr, [x2, #0xb]
    // 0xb56fec: r1 = <ProductCustomisation>
    //     0xb56fec: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xb56ff0: ldr             x1, [x1, #0x370]
    // 0xb56ff4: r0 = AllocateGrowableArray()
    //     0xb56ff4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb56ff8: mov             x3, x0
    // 0xb56ffc: ldur            x0, [fp, #-0x10]
    // 0xb57000: stur            x3, [fp, #-0x20]
    // 0xb57004: StoreField: r3->field_f = r0
    //     0xb57004: stur            w0, [x3, #0xf]
    // 0xb57008: StoreField: r3->field_b = rZR
    //     0xb57008: stur            wzr, [x3, #0xb]
    // 0xb5700c: ldur            x0, [fp, #-8]
    // 0xb57010: LoadField: r1 = r0->field_f
    //     0xb57010: ldur            w1, [x0, #0xf]
    // 0xb57014: DecompressPointer r1
    //     0xb57014: add             x1, x1, HEAP, lsl #32
    // 0xb57018: LoadField: r2 = r1->field_b
    //     0xb57018: ldur            w2, [x1, #0xb]
    // 0xb5701c: DecompressPointer r2
    //     0xb5701c: add             x2, x2, HEAP, lsl #32
    // 0xb57020: cmp             w2, NULL
    // 0xb57024: b.eq            #0xb57464
    // 0xb57028: LoadField: r4 = r2->field_f
    //     0xb57028: ldur            w4, [x2, #0xf]
    // 0xb5702c: DecompressPointer r4
    //     0xb5702c: add             x4, x4, HEAP, lsl #32
    // 0xb57030: stur            x4, [fp, #-0x10]
    // 0xb57034: LoadField: r1 = r4->field_b
    //     0xb57034: ldur            w1, [x4, #0xb]
    // 0xb57038: cbz             w1, #0xb570f0
    // 0xb5703c: mov             x2, x0
    // 0xb57040: r1 = Function '<anonymous closure>':.
    //     0xb57040: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa88] AnonymousClosure: (0xa3afc0), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xb57044: ldr             x1, [x1, #0xa88]
    // 0xb57048: r0 = AllocateClosure()
    //     0xb57048: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5704c: r1 = Function '<anonymous closure>':.
    //     0xb5704c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa90] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xb57050: ldr             x1, [x1, #0xa90]
    // 0xb57054: r2 = Null
    //     0xb57054: mov             x2, NULL
    // 0xb57058: stur            x0, [fp, #-0x28]
    // 0xb5705c: r0 = AllocateClosure()
    //     0xb5705c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb57060: str             x0, [SP]
    // 0xb57064: ldur            x1, [fp, #-0x10]
    // 0xb57068: ldur            x2, [fp, #-0x28]
    // 0xb5706c: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb5706c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb57070: ldr             x4, [x4, #0xb48]
    // 0xb57074: r0 = firstWhere()
    //     0xb57074: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xb57078: LoadField: r1 = r0->field_b
    //     0xb57078: ldur            w1, [x0, #0xb]
    // 0xb5707c: DecompressPointer r1
    //     0xb5707c: add             x1, x1, HEAP, lsl #32
    // 0xb57080: cmp             w1, NULL
    // 0xb57084: b.eq            #0xb570f0
    // 0xb57088: ldur            x1, [fp, #-8]
    // 0xb5708c: LoadField: r2 = r1->field_f
    //     0xb5708c: ldur            w2, [x1, #0xf]
    // 0xb57090: DecompressPointer r2
    //     0xb57090: add             x2, x2, HEAP, lsl #32
    // 0xb57094: LoadField: r3 = r2->field_b
    //     0xb57094: ldur            w3, [x2, #0xb]
    // 0xb57098: DecompressPointer r3
    //     0xb57098: add             x3, x3, HEAP, lsl #32
    // 0xb5709c: cmp             w3, NULL
    // 0xb570a0: b.eq            #0xb57468
    // 0xb570a4: LoadField: r2 = r3->field_b
    //     0xb570a4: ldur            w2, [x3, #0xb]
    // 0xb570a8: DecompressPointer r2
    //     0xb570a8: add             x2, x2, HEAP, lsl #32
    // 0xb570ac: cmp             w2, NULL
    // 0xb570b0: b.ne            #0xb570bc
    // 0xb570b4: r2 = Null
    //     0xb570b4: mov             x2, NULL
    // 0xb570b8: b               #0xb570c8
    // 0xb570bc: LoadField: r4 = r2->field_23
    //     0xb570bc: ldur            w4, [x2, #0x23]
    // 0xb570c0: DecompressPointer r4
    //     0xb570c0: add             x4, x4, HEAP, lsl #32
    // 0xb570c4: mov             x2, x4
    // 0xb570c8: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb570c8: ldur            w4, [x3, #0x17]
    // 0xb570cc: DecompressPointer r4
    //     0xb570cc: add             x4, x4, HEAP, lsl #32
    // 0xb570d0: stp             x2, x4, [SP, #8]
    // 0xb570d4: str             x0, [SP]
    // 0xb570d8: r4 = 0
    //     0xb570d8: movz            x4, #0
    // 0xb570dc: ldr             x0, [SP, #0x10]
    // 0xb570e0: r16 = UnlinkedCall_0x613b5c
    //     0xb570e0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aa98] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb570e4: add             x16, x16, #0xa98
    // 0xb570e8: ldp             x5, lr, [x16]
    // 0xb570ec: blr             lr
    // 0xb570f0: ldur            x3, [fp, #-8]
    // 0xb570f4: LoadField: r0 = r3->field_f
    //     0xb570f4: ldur            w0, [x3, #0xf]
    // 0xb570f8: DecompressPointer r0
    //     0xb570f8: add             x0, x0, HEAP, lsl #32
    // 0xb570fc: LoadField: r1 = r0->field_b
    //     0xb570fc: ldur            w1, [x0, #0xb]
    // 0xb57100: DecompressPointer r1
    //     0xb57100: add             x1, x1, HEAP, lsl #32
    // 0xb57104: cmp             w1, NULL
    // 0xb57108: b.eq            #0xb5746c
    // 0xb5710c: LoadField: r0 = r1->field_b
    //     0xb5710c: ldur            w0, [x1, #0xb]
    // 0xb57110: DecompressPointer r0
    //     0xb57110: add             x0, x0, HEAP, lsl #32
    // 0xb57114: cmp             w0, NULL
    // 0xb57118: b.ne            #0xb57124
    // 0xb5711c: r4 = Null
    //     0xb5711c: mov             x4, NULL
    // 0xb57120: b               #0xb57130
    // 0xb57124: LoadField: r1 = r0->field_1b
    //     0xb57124: ldur            w1, [x0, #0x1b]
    // 0xb57128: DecompressPointer r1
    //     0xb57128: add             x1, x1, HEAP, lsl #32
    // 0xb5712c: mov             x4, x1
    // 0xb57130: stur            x4, [fp, #-0x30]
    // 0xb57134: LoadField: r5 = r3->field_13
    //     0xb57134: ldur            w5, [x3, #0x13]
    // 0xb57138: DecompressPointer r5
    //     0xb57138: add             x5, x5, HEAP, lsl #32
    // 0xb5713c: stur            x5, [fp, #-0x28]
    // 0xb57140: LoadField: r6 = r5->field_7
    //     0xb57140: ldur            w6, [x5, #7]
    // 0xb57144: DecompressPointer r6
    //     0xb57144: add             x6, x6, HEAP, lsl #32
    // 0xb57148: stur            x6, [fp, #-0x10]
    // 0xb5714c: r0 = LoadClassIdInstr(r6)
    //     0xb5714c: ldur            x0, [x6, #-1]
    //     0xb57150: ubfx            x0, x0, #0xc, #0x14
    // 0xb57154: mov             x1, x6
    // 0xb57158: r2 = "/"
    //     0xb57158: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0xb5715c: r0 = GDT[cid_x0 + -0xffc]()
    //     0xb5715c: sub             lr, x0, #0xffc
    //     0xb57160: ldr             lr, [x21, lr, lsl #3]
    //     0xb57164: blr             lr
    // 0xb57168: mov             x1, x0
    // 0xb5716c: r0 = last()
    //     0xb5716c: bl              #0x79c9a8  ; [dart:core] _GrowableList::last
    // 0xb57170: mov             x1, x0
    // 0xb57174: ldur            x0, [fp, #-8]
    // 0xb57178: stur            x1, [fp, #-0x40]
    // 0xb5717c: LoadField: r2 = r0->field_f
    //     0xb5717c: ldur            w2, [x0, #0xf]
    // 0xb57180: DecompressPointer r2
    //     0xb57180: add             x2, x2, HEAP, lsl #32
    // 0xb57184: LoadField: r3 = r2->field_b
    //     0xb57184: ldur            w3, [x2, #0xb]
    // 0xb57188: DecompressPointer r3
    //     0xb57188: add             x3, x3, HEAP, lsl #32
    // 0xb5718c: cmp             w3, NULL
    // 0xb57190: b.eq            #0xb57470
    // 0xb57194: LoadField: r2 = r3->field_b
    //     0xb57194: ldur            w2, [x3, #0xb]
    // 0xb57198: DecompressPointer r2
    //     0xb57198: add             x2, x2, HEAP, lsl #32
    // 0xb5719c: cmp             w2, NULL
    // 0xb571a0: b.ne            #0xb571ac
    // 0xb571a4: r5 = Null
    //     0xb571a4: mov             x5, NULL
    // 0xb571a8: b               #0xb571b8
    // 0xb571ac: LoadField: r3 = r2->field_7
    //     0xb571ac: ldur            w3, [x2, #7]
    // 0xb571b0: DecompressPointer r3
    //     0xb571b0: add             x3, x3, HEAP, lsl #32
    // 0xb571b4: mov             x5, x3
    // 0xb571b8: ldur            x2, [fp, #-0x30]
    // 0xb571bc: ldur            x3, [fp, #-0x10]
    // 0xb571c0: ldur            x4, [fp, #-0x18]
    // 0xb571c4: stur            x5, [fp, #-0x38]
    // 0xb571c8: r0 = CustomerResponse()
    //     0xb571c8: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xb571cc: mov             x2, x0
    // 0xb571d0: ldur            x0, [fp, #-0x30]
    // 0xb571d4: stur            x2, [fp, #-0x50]
    // 0xb571d8: StoreField: r2->field_7 = r0
    //     0xb571d8: stur            w0, [x2, #7]
    // 0xb571dc: ldur            x0, [fp, #-0x40]
    // 0xb571e0: StoreField: r2->field_b = r0
    //     0xb571e0: stur            w0, [x2, #0xb]
    // 0xb571e4: ldur            x0, [fp, #-0x38]
    // 0xb571e8: StoreField: r2->field_f = r0
    //     0xb571e8: stur            w0, [x2, #0xf]
    // 0xb571ec: ldur            x0, [fp, #-0x10]
    // 0xb571f0: StoreField: r2->field_13 = r0
    //     0xb571f0: stur            w0, [x2, #0x13]
    // 0xb571f4: ldur            x0, [fp, #-0x18]
    // 0xb571f8: LoadField: r1 = r0->field_b
    //     0xb571f8: ldur            w1, [x0, #0xb]
    // 0xb571fc: LoadField: r3 = r0->field_f
    //     0xb571fc: ldur            w3, [x0, #0xf]
    // 0xb57200: DecompressPointer r3
    //     0xb57200: add             x3, x3, HEAP, lsl #32
    // 0xb57204: LoadField: r4 = r3->field_b
    //     0xb57204: ldur            w4, [x3, #0xb]
    // 0xb57208: r3 = LoadInt32Instr(r1)
    //     0xb57208: sbfx            x3, x1, #1, #0x1f
    // 0xb5720c: stur            x3, [fp, #-0x48]
    // 0xb57210: r1 = LoadInt32Instr(r4)
    //     0xb57210: sbfx            x1, x4, #1, #0x1f
    // 0xb57214: cmp             x3, x1
    // 0xb57218: b.ne            #0xb57224
    // 0xb5721c: mov             x1, x0
    // 0xb57220: r0 = _growToNextCapacity()
    //     0xb57220: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb57224: ldur            x4, [fp, #-8]
    // 0xb57228: ldur            x2, [fp, #-0x18]
    // 0xb5722c: ldur            x3, [fp, #-0x48]
    // 0xb57230: add             x0, x3, #1
    // 0xb57234: lsl             x1, x0, #1
    // 0xb57238: StoreField: r2->field_b = r1
    //     0xb57238: stur            w1, [x2, #0xb]
    // 0xb5723c: LoadField: r1 = r2->field_f
    //     0xb5723c: ldur            w1, [x2, #0xf]
    // 0xb57240: DecompressPointer r1
    //     0xb57240: add             x1, x1, HEAP, lsl #32
    // 0xb57244: ldur            x0, [fp, #-0x50]
    // 0xb57248: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb57248: add             x25, x1, x3, lsl #2
    //     0xb5724c: add             x25, x25, #0xf
    //     0xb57250: str             w0, [x25]
    //     0xb57254: tbz             w0, #0, #0xb57270
    //     0xb57258: ldurb           w16, [x1, #-1]
    //     0xb5725c: ldurb           w17, [x0, #-1]
    //     0xb57260: and             x16, x17, x16, lsr #2
    //     0xb57264: tst             x16, HEAP, lsr #32
    //     0xb57268: b.eq            #0xb57270
    //     0xb5726c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb57270: LoadField: r0 = r4->field_f
    //     0xb57270: ldur            w0, [x4, #0xf]
    // 0xb57274: DecompressPointer r0
    //     0xb57274: add             x0, x0, HEAP, lsl #32
    // 0xb57278: LoadField: r1 = r0->field_b
    //     0xb57278: ldur            w1, [x0, #0xb]
    // 0xb5727c: DecompressPointer r1
    //     0xb5727c: add             x1, x1, HEAP, lsl #32
    // 0xb57280: cmp             w1, NULL
    // 0xb57284: b.eq            #0xb57474
    // 0xb57288: LoadField: r0 = r1->field_b
    //     0xb57288: ldur            w0, [x1, #0xb]
    // 0xb5728c: DecompressPointer r0
    //     0xb5728c: add             x0, x0, HEAP, lsl #32
    // 0xb57290: cmp             w0, NULL
    // 0xb57294: b.ne            #0xb572a0
    // 0xb57298: r1 = Null
    //     0xb57298: mov             x1, NULL
    // 0xb5729c: b               #0xb572a8
    // 0xb572a0: LoadField: r1 = r0->field_7
    //     0xb572a0: ldur            w1, [x0, #7]
    // 0xb572a4: DecompressPointer r1
    //     0xb572a4: add             x1, x1, HEAP, lsl #32
    // 0xb572a8: stur            x1, [fp, #-0x40]
    // 0xb572ac: cmp             w0, NULL
    // 0xb572b0: b.ne            #0xb572bc
    // 0xb572b4: r3 = Null
    //     0xb572b4: mov             x3, NULL
    // 0xb572b8: b               #0xb572c4
    // 0xb572bc: LoadField: r3 = r0->field_b
    //     0xb572bc: ldur            w3, [x0, #0xb]
    // 0xb572c0: DecompressPointer r3
    //     0xb572c0: add             x3, x3, HEAP, lsl #32
    // 0xb572c4: stur            x3, [fp, #-0x38]
    // 0xb572c8: cmp             w0, NULL
    // 0xb572cc: b.ne            #0xb572d8
    // 0xb572d0: r5 = Null
    //     0xb572d0: mov             x5, NULL
    // 0xb572d4: b               #0xb572e0
    // 0xb572d8: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xb572d8: ldur            w5, [x0, #0x17]
    // 0xb572dc: DecompressPointer r5
    //     0xb572dc: add             x5, x5, HEAP, lsl #32
    // 0xb572e0: stur            x5, [fp, #-0x30]
    // 0xb572e4: cmp             w0, NULL
    // 0xb572e8: b.ne            #0xb572f4
    // 0xb572ec: r6 = Null
    //     0xb572ec: mov             x6, NULL
    // 0xb572f0: b               #0xb572fc
    // 0xb572f4: LoadField: r6 = r0->field_23
    //     0xb572f4: ldur            w6, [x0, #0x23]
    // 0xb572f8: DecompressPointer r6
    //     0xb572f8: add             x6, x6, HEAP, lsl #32
    // 0xb572fc: ldur            x0, [fp, #-0x20]
    // 0xb57300: stur            x6, [fp, #-0x10]
    // 0xb57304: r0 = ProductCustomisation()
    //     0xb57304: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xb57308: mov             x2, x0
    // 0xb5730c: ldur            x0, [fp, #-0x40]
    // 0xb57310: stur            x2, [fp, #-0x50]
    // 0xb57314: StoreField: r2->field_b = r0
    //     0xb57314: stur            w0, [x2, #0xb]
    // 0xb57318: ldur            x0, [fp, #-0x38]
    // 0xb5731c: StoreField: r2->field_f = r0
    //     0xb5731c: stur            w0, [x2, #0xf]
    // 0xb57320: ldur            x0, [fp, #-0x30]
    // 0xb57324: ArrayStore: r2[0] = r0  ; List_4
    //     0xb57324: stur            w0, [x2, #0x17]
    // 0xb57328: ldur            x0, [fp, #-0x18]
    // 0xb5732c: StoreField: r2->field_23 = r0
    //     0xb5732c: stur            w0, [x2, #0x23]
    // 0xb57330: ldur            x0, [fp, #-0x10]
    // 0xb57334: StoreField: r2->field_27 = r0
    //     0xb57334: stur            w0, [x2, #0x27]
    // 0xb57338: ldur            x1, [fp, #-0x20]
    // 0xb5733c: r0 = clear()
    //     0xb5733c: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb57340: ldur            x0, [fp, #-0x20]
    // 0xb57344: LoadField: r1 = r0->field_b
    //     0xb57344: ldur            w1, [x0, #0xb]
    // 0xb57348: LoadField: r2 = r0->field_f
    //     0xb57348: ldur            w2, [x0, #0xf]
    // 0xb5734c: DecompressPointer r2
    //     0xb5734c: add             x2, x2, HEAP, lsl #32
    // 0xb57350: LoadField: r3 = r2->field_b
    //     0xb57350: ldur            w3, [x2, #0xb]
    // 0xb57354: r2 = LoadInt32Instr(r1)
    //     0xb57354: sbfx            x2, x1, #1, #0x1f
    // 0xb57358: stur            x2, [fp, #-0x48]
    // 0xb5735c: r1 = LoadInt32Instr(r3)
    //     0xb5735c: sbfx            x1, x3, #1, #0x1f
    // 0xb57360: cmp             x2, x1
    // 0xb57364: b.ne            #0xb57370
    // 0xb57368: mov             x1, x0
    // 0xb5736c: r0 = _growToNextCapacity()
    //     0xb5736c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb57370: ldur            x4, [fp, #-8]
    // 0xb57374: ldur            x2, [fp, #-0x20]
    // 0xb57378: ldur            x3, [fp, #-0x48]
    // 0xb5737c: add             x0, x3, #1
    // 0xb57380: lsl             x1, x0, #1
    // 0xb57384: StoreField: r2->field_b = r1
    //     0xb57384: stur            w1, [x2, #0xb]
    // 0xb57388: LoadField: r1 = r2->field_f
    //     0xb57388: ldur            w1, [x2, #0xf]
    // 0xb5738c: DecompressPointer r1
    //     0xb5738c: add             x1, x1, HEAP, lsl #32
    // 0xb57390: ldur            x0, [fp, #-0x50]
    // 0xb57394: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb57394: add             x25, x1, x3, lsl #2
    //     0xb57398: add             x25, x25, #0xf
    //     0xb5739c: str             w0, [x25]
    //     0xb573a0: tbz             w0, #0, #0xb573bc
    //     0xb573a4: ldurb           w16, [x1, #-1]
    //     0xb573a8: ldurb           w17, [x0, #-1]
    //     0xb573ac: and             x16, x17, x16, lsr #2
    //     0xb573b0: tst             x16, HEAP, lsr #32
    //     0xb573b4: b.eq            #0xb573bc
    //     0xb573b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb573bc: LoadField: r0 = r4->field_f
    //     0xb573bc: ldur            w0, [x4, #0xf]
    // 0xb573c0: DecompressPointer r0
    //     0xb573c0: add             x0, x0, HEAP, lsl #32
    // 0xb573c4: LoadField: r1 = r0->field_b
    //     0xb573c4: ldur            w1, [x0, #0xb]
    // 0xb573c8: DecompressPointer r1
    //     0xb573c8: add             x1, x1, HEAP, lsl #32
    // 0xb573cc: cmp             w1, NULL
    // 0xb573d0: b.eq            #0xb57478
    // 0xb573d4: LoadField: r0 = r1->field_b
    //     0xb573d4: ldur            w0, [x1, #0xb]
    // 0xb573d8: DecompressPointer r0
    //     0xb573d8: add             x0, x0, HEAP, lsl #32
    // 0xb573dc: cmp             w0, NULL
    // 0xb573e0: b.ne            #0xb573ec
    // 0xb573e4: r0 = Null
    //     0xb573e4: mov             x0, NULL
    // 0xb573e8: b               #0xb573f8
    // 0xb573ec: LoadField: r3 = r0->field_23
    //     0xb573ec: ldur            w3, [x0, #0x23]
    // 0xb573f0: DecompressPointer r3
    //     0xb573f0: add             x3, x3, HEAP, lsl #32
    // 0xb573f4: mov             x0, x3
    // 0xb573f8: LoadField: r3 = r1->field_13
    //     0xb573f8: ldur            w3, [x1, #0x13]
    // 0xb573fc: DecompressPointer r3
    //     0xb573fc: add             x3, x3, HEAP, lsl #32
    // 0xb57400: stp             x0, x3, [SP, #8]
    // 0xb57404: str             x2, [SP]
    // 0xb57408: r4 = 0
    //     0xb57408: movz            x4, #0
    // 0xb5740c: ldr             x0, [SP, #0x10]
    // 0xb57410: r16 = UnlinkedCall_0x613b5c
    //     0xb57410: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aaa8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb57414: add             x16, x16, #0xaa8
    // 0xb57418: ldp             x5, lr, [x16]
    // 0xb5741c: blr             lr
    // 0xb57420: ldur            x1, [fp, #-8]
    // 0xb57424: LoadField: r2 = r1->field_f
    //     0xb57424: ldur            w2, [x1, #0xf]
    // 0xb57428: DecompressPointer r2
    //     0xb57428: add             x2, x2, HEAP, lsl #32
    // 0xb5742c: ldur            x0, [fp, #-0x28]
    // 0xb57430: StoreField: r2->field_13 = r0
    //     0xb57430: stur            w0, [x2, #0x13]
    //     0xb57434: ldurb           w16, [x2, #-1]
    //     0xb57438: ldurb           w17, [x0, #-1]
    //     0xb5743c: and             x16, x17, x16, lsr #2
    //     0xb57440: tst             x16, HEAP, lsr #32
    //     0xb57444: b.eq            #0xb5744c
    //     0xb57448: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb5744c: r0 = Null
    //     0xb5744c: mov             x0, NULL
    // 0xb57450: LeaveFrame
    //     0xb57450: mov             SP, fp
    //     0xb57454: ldp             fp, lr, [SP], #0x10
    // 0xb57458: ret
    //     0xb57458: ret             
    // 0xb5745c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5745c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb57460: b               #0xb56fac
    // 0xb57464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57464: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb57468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57468: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5746c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5746c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb57470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57470: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb57474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb57478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57478: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xb5747c, size: 0x64
    // 0xb5747c: EnterFrame
    //     0xb5747c: stp             fp, lr, [SP, #-0x10]!
    //     0xb57480: mov             fp, SP
    // 0xb57484: AllocStack(0x8)
    //     0xb57484: sub             SP, SP, #8
    // 0xb57488: SetupParameters()
    //     0xb57488: ldr             x0, [fp, #0x18]
    //     0xb5748c: ldur            w1, [x0, #0x17]
    //     0xb57490: add             x1, x1, HEAP, lsl #32
    // 0xb57494: CheckStackOverflow
    //     0xb57494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb57498: cmp             SP, x16
    //     0xb5749c: b.ls            #0xb574d8
    // 0xb574a0: LoadField: r0 = r1->field_f
    //     0xb574a0: ldur            w0, [x1, #0xf]
    // 0xb574a4: DecompressPointer r0
    //     0xb574a4: add             x0, x0, HEAP, lsl #32
    // 0xb574a8: stur            x0, [fp, #-8]
    // 0xb574ac: r1 = Function '<anonymous closure>':.
    //     0xb574ac: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa60] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb574b0: ldr             x1, [x1, #0xa60]
    // 0xb574b4: r2 = Null
    //     0xb574b4: mov             x2, NULL
    // 0xb574b8: r0 = AllocateClosure()
    //     0xb574b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb574bc: ldur            x1, [fp, #-8]
    // 0xb574c0: mov             x2, x0
    // 0xb574c4: r0 = setState()
    //     0xb574c4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb574c8: r0 = Null
    //     0xb574c8: mov             x0, NULL
    // 0xb574cc: LeaveFrame
    //     0xb574cc: mov             SP, fp
    //     0xb574d0: ldp             fp, lr, [SP], #0x10
    // 0xb574d4: ret
    //     0xb574d4: ret             
    // 0xb574d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb574d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb574dc: b               #0xb574a0
  }
}

// class id: 4089, size: 0x1c, field offset: 0xc
//   const constructor, 
class CameraPicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f01c, size: 0x24
    // 0xc7f01c: EnterFrame
    //     0xc7f01c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f020: mov             fp, SP
    // 0xc7f024: mov             x0, x1
    // 0xc7f028: r1 = <CameraPicker>
    //     0xc7f028: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e30] TypeArguments: <CameraPicker>
    //     0xc7f02c: ldr             x1, [x1, #0xe30]
    // 0xc7f030: r0 = _CameraPickerState()
    //     0xc7f030: bl              #0xc7f040  ; Allocate_CameraPickerStateStub -> _CameraPickerState (size=0x18)
    // 0xc7f034: LeaveFrame
    //     0xc7f034: mov             SP, fp
    //     0xc7f038: ldp             fp, lr, [SP], #0x10
    // 0xc7f03c: ret
    //     0xc7f03c: ret             
  }
}
