// lib: , url: package:customer_app/app/presentation/views/line/search/widgets/search_view.dart

// class id: 1049583, size: 0x8
class :: {
}

// class id: 3206, size: 0x24, field offset: 0x14
class _SearchViewState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaac0ac, size: 0x94
    // 0xaac0ac: EnterFrame
    //     0xaac0ac: stp             fp, lr, [SP, #-0x10]!
    //     0xaac0b0: mov             fp, SP
    // 0xaac0b4: r0 = false
    //     0xaac0b4: add             x0, NULL, #0x30  ; false
    // 0xaac0b8: ldr             x1, [fp, #0x10]
    // 0xaac0bc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaac0bc: ldur            w2, [x1, #0x17]
    // 0xaac0c0: DecompressPointer r2
    //     0xaac0c0: add             x2, x2, HEAP, lsl #32
    // 0xaac0c4: CheckStackOverflow
    //     0xaac0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaac0c8: cmp             SP, x16
    //     0xaac0cc: b.ls            #0xaac138
    // 0xaac0d0: LoadField: r1 = r2->field_b
    //     0xaac0d0: ldur            w1, [x2, #0xb]
    // 0xaac0d4: DecompressPointer r1
    //     0xaac0d4: add             x1, x1, HEAP, lsl #32
    // 0xaac0d8: LoadField: r3 = r1->field_f
    //     0xaac0d8: ldur            w3, [x1, #0xf]
    // 0xaac0dc: DecompressPointer r3
    //     0xaac0dc: add             x3, x3, HEAP, lsl #32
    // 0xaac0e0: StoreField: r3->field_1f = r0
    //     0xaac0e0: stur            w0, [x3, #0x1f]
    // 0xaac0e4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xaac0e4: ldur            w1, [x3, #0x17]
    // 0xaac0e8: DecompressPointer r1
    //     0xaac0e8: add             x1, x1, HEAP, lsl #32
    // 0xaac0ec: LoadField: r0 = r2->field_f
    //     0xaac0ec: ldur            w0, [x2, #0xf]
    // 0xaac0f0: DecompressPointer r0
    //     0xaac0f0: add             x0, x0, HEAP, lsl #32
    // 0xaac0f4: cmp             w0, NULL
    // 0xaac0f8: b.ne            #0xaac104
    // 0xaac0fc: r0 = Null
    //     0xaac0fc: mov             x0, NULL
    // 0xaac100: b               #0xaac110
    // 0xaac104: LoadField: r2 = r0->field_b
    //     0xaac104: ldur            w2, [x0, #0xb]
    // 0xaac108: DecompressPointer r2
    //     0xaac108: add             x2, x2, HEAP, lsl #32
    // 0xaac10c: mov             x0, x2
    // 0xaac110: cmp             w0, NULL
    // 0xaac114: b.ne            #0xaac120
    // 0xaac118: r2 = ""
    //     0xaac118: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaac11c: b               #0xaac124
    // 0xaac120: mov             x2, x0
    // 0xaac124: r0 = text=()
    //     0xaac124: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xaac128: r0 = Null
    //     0xaac128: mov             x0, NULL
    // 0xaac12c: LeaveFrame
    //     0xaac12c: mov             SP, fp
    //     0xaac130: ldp             fp, lr, [SP], #0x10
    // 0xaac134: ret
    //     0xaac134: ret             
    // 0xaac138: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaac138: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaac13c: b               #0xaac0d0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaac140, size: 0x104
    // 0xaac140: EnterFrame
    //     0xaac140: stp             fp, lr, [SP, #-0x10]!
    //     0xaac144: mov             fp, SP
    // 0xaac148: AllocStack(0x30)
    //     0xaac148: sub             SP, SP, #0x30
    // 0xaac14c: SetupParameters()
    //     0xaac14c: ldr             x0, [fp, #0x10]
    //     0xaac150: ldur            w2, [x0, #0x17]
    //     0xaac154: add             x2, x2, HEAP, lsl #32
    //     0xaac158: stur            x2, [fp, #-0x10]
    // 0xaac15c: CheckStackOverflow
    //     0xaac15c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaac160: cmp             SP, x16
    //     0xaac164: b.ls            #0xaac238
    // 0xaac168: LoadField: r0 = r2->field_b
    //     0xaac168: ldur            w0, [x2, #0xb]
    // 0xaac16c: DecompressPointer r0
    //     0xaac16c: add             x0, x0, HEAP, lsl #32
    // 0xaac170: stur            x0, [fp, #-8]
    // 0xaac174: LoadField: r1 = r0->field_f
    //     0xaac174: ldur            w1, [x0, #0xf]
    // 0xaac178: DecompressPointer r1
    //     0xaac178: add             x1, x1, HEAP, lsl #32
    // 0xaac17c: LoadField: r3 = r1->field_b
    //     0xaac17c: ldur            w3, [x1, #0xb]
    // 0xaac180: DecompressPointer r3
    //     0xaac180: add             x3, x3, HEAP, lsl #32
    // 0xaac184: cmp             w3, NULL
    // 0xaac188: b.eq            #0xaac240
    // 0xaac18c: LoadField: r4 = r2->field_f
    //     0xaac18c: ldur            w4, [x2, #0xf]
    // 0xaac190: DecompressPointer r4
    //     0xaac190: add             x4, x4, HEAP, lsl #32
    // 0xaac194: cmp             w4, NULL
    // 0xaac198: b.ne            #0xaac1a4
    // 0xaac19c: r4 = Null
    //     0xaac19c: mov             x4, NULL
    // 0xaac1a0: b               #0xaac1b0
    // 0xaac1a4: LoadField: r5 = r4->field_b
    //     0xaac1a4: ldur            w5, [x4, #0xb]
    // 0xaac1a8: DecompressPointer r5
    //     0xaac1a8: add             x5, x5, HEAP, lsl #32
    // 0xaac1ac: mov             x4, x5
    // 0xaac1b0: cmp             w4, NULL
    // 0xaac1b4: b.ne            #0xaac1bc
    // 0xaac1b8: r4 = " "
    //     0xaac1b8: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xaac1bc: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xaac1bc: ldur            w5, [x1, #0x17]
    // 0xaac1c0: DecompressPointer r5
    //     0xaac1c0: add             x5, x5, HEAP, lsl #32
    // 0xaac1c4: LoadField: r1 = r5->field_27
    //     0xaac1c4: ldur            w1, [x5, #0x27]
    // 0xaac1c8: DecompressPointer r1
    //     0xaac1c8: add             x1, x1, HEAP, lsl #32
    // 0xaac1cc: LoadField: r5 = r1->field_7
    //     0xaac1cc: ldur            w5, [x1, #7]
    // 0xaac1d0: DecompressPointer r5
    //     0xaac1d0: add             x5, x5, HEAP, lsl #32
    // 0xaac1d4: LoadField: r1 = r3->field_3b
    //     0xaac1d4: ldur            w1, [x3, #0x3b]
    // 0xaac1d8: DecompressPointer r1
    //     0xaac1d8: add             x1, x1, HEAP, lsl #32
    // 0xaac1dc: stp             x4, x1, [SP, #8]
    // 0xaac1e0: str             x5, [SP]
    // 0xaac1e4: r4 = 0
    //     0xaac1e4: movz            x4, #0
    // 0xaac1e8: ldr             x0, [SP, #0x10]
    // 0xaac1ec: r16 = UnlinkedCall_0x613b5c
    //     0xaac1ec: add             x16, PP, #0x51, lsl #12  ; [pp+0x51cc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaac1f0: add             x16, x16, #0xcc0
    // 0xaac1f4: ldp             x5, lr, [x16]
    // 0xaac1f8: blr             lr
    // 0xaac1fc: ldur            x0, [fp, #-8]
    // 0xaac200: LoadField: r3 = r0->field_f
    //     0xaac200: ldur            w3, [x0, #0xf]
    // 0xaac204: DecompressPointer r3
    //     0xaac204: add             x3, x3, HEAP, lsl #32
    // 0xaac208: ldur            x2, [fp, #-0x10]
    // 0xaac20c: stur            x3, [fp, #-0x18]
    // 0xaac210: r1 = Function '<anonymous closure>':.
    //     0xaac210: add             x1, PP, #0x51, lsl #12  ; [pp+0x51cd0] AnonymousClosure: (0xaac0ac), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaac214: ldr             x1, [x1, #0xcd0]
    // 0xaac218: r0 = AllocateClosure()
    //     0xaac218: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaac21c: ldur            x1, [fp, #-0x18]
    // 0xaac220: mov             x2, x0
    // 0xaac224: r0 = setState()
    //     0xaac224: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaac228: r0 = Null
    //     0xaac228: mov             x0, NULL
    // 0xaac22c: LeaveFrame
    //     0xaac22c: mov             SP, fp
    //     0xaac230: ldp             fp, lr, [SP], #0x10
    // 0xaac234: ret
    //     0xaac234: ret             
    // 0xaac238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaac238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaac23c: b               #0xaac168
    // 0xaac240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaac240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaac244, size: 0x48c
    // 0xaac244: EnterFrame
    //     0xaac244: stp             fp, lr, [SP, #-0x10]!
    //     0xaac248: mov             fp, SP
    // 0xaac24c: AllocStack(0x58)
    //     0xaac24c: sub             SP, SP, #0x58
    // 0xaac250: SetupParameters()
    //     0xaac250: ldr             x0, [fp, #0x20]
    //     0xaac254: ldur            w1, [x0, #0x17]
    //     0xaac258: add             x1, x1, HEAP, lsl #32
    //     0xaac25c: stur            x1, [fp, #-8]
    // 0xaac260: CheckStackOverflow
    //     0xaac260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaac264: cmp             SP, x16
    //     0xaac268: b.ls            #0xaac6c0
    // 0xaac26c: r1 = 1
    //     0xaac26c: movz            x1, #0x1
    // 0xaac270: r0 = AllocateContext()
    //     0xaac270: bl              #0x16f6108  ; AllocateContextStub
    // 0xaac274: mov             x2, x0
    // 0xaac278: ldur            x0, [fp, #-8]
    // 0xaac27c: stur            x2, [fp, #-0x10]
    // 0xaac280: StoreField: r2->field_b = r0
    //     0xaac280: stur            w0, [x2, #0xb]
    // 0xaac284: LoadField: r1 = r0->field_f
    //     0xaac284: ldur            w1, [x0, #0xf]
    // 0xaac288: DecompressPointer r1
    //     0xaac288: add             x1, x1, HEAP, lsl #32
    // 0xaac28c: LoadField: r0 = r1->field_b
    //     0xaac28c: ldur            w0, [x1, #0xb]
    // 0xaac290: DecompressPointer r0
    //     0xaac290: add             x0, x0, HEAP, lsl #32
    // 0xaac294: cmp             w0, NULL
    // 0xaac298: b.eq            #0xaac6c8
    // 0xaac29c: LoadField: r1 = r0->field_43
    //     0xaac29c: ldur            w1, [x0, #0x43]
    // 0xaac2a0: DecompressPointer r1
    //     0xaac2a0: add             x1, x1, HEAP, lsl #32
    // 0xaac2a4: LoadField: r3 = r1->field_b
    //     0xaac2a4: ldur            w3, [x1, #0xb]
    // 0xaac2a8: DecompressPointer r3
    //     0xaac2a8: add             x3, x3, HEAP, lsl #32
    // 0xaac2ac: cmp             w3, NULL
    // 0xaac2b0: b.ne            #0xaac2bc
    // 0xaac2b4: r0 = Null
    //     0xaac2b4: mov             x0, NULL
    // 0xaac2b8: b               #0xaac2f8
    // 0xaac2bc: ldr             x0, [fp, #0x10]
    // 0xaac2c0: LoadField: r1 = r3->field_b
    //     0xaac2c0: ldur            w1, [x3, #0xb]
    // 0xaac2c4: r4 = LoadInt32Instr(r0)
    //     0xaac2c4: sbfx            x4, x0, #1, #0x1f
    //     0xaac2c8: tbz             w0, #0, #0xaac2d0
    //     0xaac2cc: ldur            x4, [x0, #7]
    // 0xaac2d0: r0 = LoadInt32Instr(r1)
    //     0xaac2d0: sbfx            x0, x1, #1, #0x1f
    // 0xaac2d4: mov             x1, x4
    // 0xaac2d8: cmp             x1, x0
    // 0xaac2dc: b.hs            #0xaac6cc
    // 0xaac2e0: LoadField: r0 = r3->field_f
    //     0xaac2e0: ldur            w0, [x3, #0xf]
    // 0xaac2e4: DecompressPointer r0
    //     0xaac2e4: add             x0, x0, HEAP, lsl #32
    // 0xaac2e8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xaac2e8: add             x16, x0, x4, lsl #2
    //     0xaac2ec: ldur            w1, [x16, #0xf]
    // 0xaac2f0: DecompressPointer r1
    //     0xaac2f0: add             x1, x1, HEAP, lsl #32
    // 0xaac2f4: mov             x0, x1
    // 0xaac2f8: stur            x0, [fp, #-8]
    // 0xaac2fc: StoreField: r2->field_f = r0
    //     0xaac2fc: stur            w0, [x2, #0xf]
    // 0xaac300: r0 = ImageHeaders.forImages()
    //     0xaac300: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xaac304: mov             x3, x0
    // 0xaac308: ldur            x0, [fp, #-8]
    // 0xaac30c: stur            x3, [fp, #-0x20]
    // 0xaac310: cmp             w0, NULL
    // 0xaac314: b.ne            #0xaac320
    // 0xaac318: r1 = Null
    //     0xaac318: mov             x1, NULL
    // 0xaac31c: b               #0xaac328
    // 0xaac320: LoadField: r1 = r0->field_7
    //     0xaac320: ldur            w1, [x0, #7]
    // 0xaac324: DecompressPointer r1
    //     0xaac324: add             x1, x1, HEAP, lsl #32
    // 0xaac328: cmp             w1, NULL
    // 0xaac32c: b.ne            #0xaac338
    // 0xaac330: r4 = ""
    //     0xaac330: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaac334: b               #0xaac33c
    // 0xaac338: mov             x4, x1
    // 0xaac33c: stur            x4, [fp, #-0x18]
    // 0xaac340: r1 = Function '<anonymous closure>':.
    //     0xaac340: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ca8] AnonymousClosure: (0xaac6d0), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaac344: ldr             x1, [x1, #0xca8]
    // 0xaac348: r2 = Null
    //     0xaac348: mov             x2, NULL
    // 0xaac34c: r0 = AllocateClosure()
    //     0xaac34c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaac350: stur            x0, [fp, #-0x28]
    // 0xaac354: r0 = CachedNetworkImage()
    //     0xaac354: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaac358: stur            x0, [fp, #-0x30]
    // 0xaac35c: ldur            x16, [fp, #-0x20]
    // 0xaac360: r30 = 35.000000
    //     0xaac360: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xaac364: ldr             lr, [lr, #0x2b0]
    // 0xaac368: stp             lr, x16, [SP, #0x18]
    // 0xaac36c: r16 = 35.000000
    //     0xaac36c: add             x16, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xaac370: ldr             x16, [x16, #0x2b0]
    // 0xaac374: r30 = Instance_BoxFit
    //     0xaac374: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xaac378: ldr             lr, [lr, #0x118]
    // 0xaac37c: stp             lr, x16, [SP, #8]
    // 0xaac380: ldur            x16, [fp, #-0x28]
    // 0xaac384: str             x16, [SP]
    // 0xaac388: mov             x1, x0
    // 0xaac38c: ldur            x2, [fp, #-0x18]
    // 0xaac390: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x5, height, 0x4, httpHeaders, 0x2, width, 0x3, null]
    //     0xaac390: add             x4, PP, #0x51, lsl #12  ; [pp+0x51cb0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "width", 0x3, Null]
    //     0xaac394: ldr             x4, [x4, #0xcb0]
    // 0xaac398: r0 = CachedNetworkImage()
    //     0xaac398: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaac39c: ldur            x0, [fp, #-8]
    // 0xaac3a0: cmp             w0, NULL
    // 0xaac3a4: b.ne            #0xaac3b0
    // 0xaac3a8: r1 = Null
    //     0xaac3a8: mov             x1, NULL
    // 0xaac3ac: b               #0xaac3b8
    // 0xaac3b0: LoadField: r1 = r0->field_b
    //     0xaac3b0: ldur            w1, [x0, #0xb]
    // 0xaac3b4: DecompressPointer r1
    //     0xaac3b4: add             x1, x1, HEAP, lsl #32
    // 0xaac3b8: cmp             w1, NULL
    // 0xaac3bc: b.ne            #0xaac3c8
    // 0xaac3c0: r2 = ""
    //     0xaac3c0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaac3c4: b               #0xaac3cc
    // 0xaac3c8: mov             x2, x1
    // 0xaac3cc: ldr             x1, [fp, #0x18]
    // 0xaac3d0: stur            x2, [fp, #-0x18]
    // 0xaac3d4: r0 = of()
    //     0xaac3d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaac3d8: LoadField: r1 = r0->field_87
    //     0xaac3d8: ldur            w1, [x0, #0x87]
    // 0xaac3dc: DecompressPointer r1
    //     0xaac3dc: add             x1, x1, HEAP, lsl #32
    // 0xaac3e0: LoadField: r0 = r1->field_2b
    //     0xaac3e0: ldur            w0, [x1, #0x2b]
    // 0xaac3e4: DecompressPointer r0
    //     0xaac3e4: add             x0, x0, HEAP, lsl #32
    // 0xaac3e8: r16 = Instance_Color
    //     0xaac3e8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaac3ec: r30 = 12.000000
    //     0xaac3ec: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaac3f0: ldr             lr, [lr, #0x9e8]
    // 0xaac3f4: stp             lr, x16, [SP]
    // 0xaac3f8: mov             x1, x0
    // 0xaac3fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaac3fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaac400: ldr             x4, [x4, #0x9b8]
    // 0xaac404: r0 = copyWith()
    //     0xaac404: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaac408: stur            x0, [fp, #-0x20]
    // 0xaac40c: r0 = Text()
    //     0xaac40c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaac410: mov             x2, x0
    // 0xaac414: ldur            x0, [fp, #-0x18]
    // 0xaac418: stur            x2, [fp, #-0x28]
    // 0xaac41c: StoreField: r2->field_b = r0
    //     0xaac41c: stur            w0, [x2, #0xb]
    // 0xaac420: ldur            x0, [fp, #-0x20]
    // 0xaac424: StoreField: r2->field_13 = r0
    //     0xaac424: stur            w0, [x2, #0x13]
    // 0xaac428: ldur            x0, [fp, #-8]
    // 0xaac42c: cmp             w0, NULL
    // 0xaac430: b.ne            #0xaac43c
    // 0xaac434: r0 = Null
    //     0xaac434: mov             x0, NULL
    // 0xaac438: b               #0xaac448
    // 0xaac43c: LoadField: r1 = r0->field_f
    //     0xaac43c: ldur            w1, [x0, #0xf]
    // 0xaac440: DecompressPointer r1
    //     0xaac440: add             x1, x1, HEAP, lsl #32
    // 0xaac444: mov             x0, x1
    // 0xaac448: cmp             w0, NULL
    // 0xaac44c: b.ne            #0xaac458
    // 0xaac450: r3 = ""
    //     0xaac450: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaac454: b               #0xaac45c
    // 0xaac458: mov             x3, x0
    // 0xaac45c: ldur            x0, [fp, #-0x30]
    // 0xaac460: ldr             x1, [fp, #0x18]
    // 0xaac464: stur            x3, [fp, #-8]
    // 0xaac468: r0 = of()
    //     0xaac468: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaac46c: LoadField: r1 = r0->field_87
    //     0xaac46c: ldur            w1, [x0, #0x87]
    // 0xaac470: DecompressPointer r1
    //     0xaac470: add             x1, x1, HEAP, lsl #32
    // 0xaac474: LoadField: r0 = r1->field_2b
    //     0xaac474: ldur            w0, [x1, #0x2b]
    // 0xaac478: DecompressPointer r0
    //     0xaac478: add             x0, x0, HEAP, lsl #32
    // 0xaac47c: r16 = Instance_Color
    //     0xaac47c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaac480: r30 = 12.000000
    //     0xaac480: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaac484: ldr             lr, [lr, #0x9e8]
    // 0xaac488: stp             lr, x16, [SP]
    // 0xaac48c: mov             x1, x0
    // 0xaac490: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaac490: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaac494: ldr             x4, [x4, #0x9b8]
    // 0xaac498: r0 = copyWith()
    //     0xaac498: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaac49c: stur            x0, [fp, #-0x18]
    // 0xaac4a0: r0 = Text()
    //     0xaac4a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaac4a4: mov             x3, x0
    // 0xaac4a8: ldur            x0, [fp, #-8]
    // 0xaac4ac: stur            x3, [fp, #-0x20]
    // 0xaac4b0: StoreField: r3->field_b = r0
    //     0xaac4b0: stur            w0, [x3, #0xb]
    // 0xaac4b4: ldur            x0, [fp, #-0x18]
    // 0xaac4b8: StoreField: r3->field_13 = r0
    //     0xaac4b8: stur            w0, [x3, #0x13]
    // 0xaac4bc: r0 = Instance_TextOverflow
    //     0xaac4bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xaac4c0: ldr             x0, [x0, #0xe10]
    // 0xaac4c4: StoreField: r3->field_2b = r0
    //     0xaac4c4: stur            w0, [x3, #0x2b]
    // 0xaac4c8: r0 = 2
    //     0xaac4c8: movz            x0, #0x2
    // 0xaac4cc: StoreField: r3->field_37 = r0
    //     0xaac4cc: stur            w0, [x3, #0x37]
    // 0xaac4d0: r1 = Null
    //     0xaac4d0: mov             x1, NULL
    // 0xaac4d4: r2 = 6
    //     0xaac4d4: movz            x2, #0x6
    // 0xaac4d8: r0 = AllocateArray()
    //     0xaac4d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaac4dc: mov             x2, x0
    // 0xaac4e0: ldur            x0, [fp, #-0x28]
    // 0xaac4e4: stur            x2, [fp, #-8]
    // 0xaac4e8: StoreField: r2->field_f = r0
    //     0xaac4e8: stur            w0, [x2, #0xf]
    // 0xaac4ec: r16 = Instance_SizedBox
    //     0xaac4ec: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xaac4f0: ldr             x16, [x16, #0xc70]
    // 0xaac4f4: StoreField: r2->field_13 = r16
    //     0xaac4f4: stur            w16, [x2, #0x13]
    // 0xaac4f8: ldur            x0, [fp, #-0x20]
    // 0xaac4fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xaac4fc: stur            w0, [x2, #0x17]
    // 0xaac500: r1 = <Widget>
    //     0xaac500: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaac504: r0 = AllocateGrowableArray()
    //     0xaac504: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaac508: mov             x1, x0
    // 0xaac50c: ldur            x0, [fp, #-8]
    // 0xaac510: stur            x1, [fp, #-0x18]
    // 0xaac514: StoreField: r1->field_f = r0
    //     0xaac514: stur            w0, [x1, #0xf]
    // 0xaac518: r2 = 6
    //     0xaac518: movz            x2, #0x6
    // 0xaac51c: StoreField: r1->field_b = r2
    //     0xaac51c: stur            w2, [x1, #0xb]
    // 0xaac520: r0 = Column()
    //     0xaac520: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaac524: mov             x2, x0
    // 0xaac528: r0 = Instance_Axis
    //     0xaac528: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaac52c: stur            x2, [fp, #-8]
    // 0xaac530: StoreField: r2->field_f = r0
    //     0xaac530: stur            w0, [x2, #0xf]
    // 0xaac534: r0 = Instance_MainAxisAlignment
    //     0xaac534: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaac538: ldr             x0, [x0, #0xa08]
    // 0xaac53c: StoreField: r2->field_13 = r0
    //     0xaac53c: stur            w0, [x2, #0x13]
    // 0xaac540: r3 = Instance_MainAxisSize
    //     0xaac540: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaac544: ldr             x3, [x3, #0xa10]
    // 0xaac548: ArrayStore: r2[0] = r3  ; List_4
    //     0xaac548: stur            w3, [x2, #0x17]
    // 0xaac54c: r1 = Instance_CrossAxisAlignment
    //     0xaac54c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaac550: ldr             x1, [x1, #0x890]
    // 0xaac554: StoreField: r2->field_1b = r1
    //     0xaac554: stur            w1, [x2, #0x1b]
    // 0xaac558: r4 = Instance_VerticalDirection
    //     0xaac558: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaac55c: ldr             x4, [x4, #0xa20]
    // 0xaac560: StoreField: r2->field_23 = r4
    //     0xaac560: stur            w4, [x2, #0x23]
    // 0xaac564: r5 = Instance_Clip
    //     0xaac564: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaac568: ldr             x5, [x5, #0x38]
    // 0xaac56c: StoreField: r2->field_2b = r5
    //     0xaac56c: stur            w5, [x2, #0x2b]
    // 0xaac570: StoreField: r2->field_2f = rZR
    //     0xaac570: stur            xzr, [x2, #0x2f]
    // 0xaac574: ldur            x1, [fp, #-0x18]
    // 0xaac578: StoreField: r2->field_b = r1
    //     0xaac578: stur            w1, [x2, #0xb]
    // 0xaac57c: r1 = <FlexParentData>
    //     0xaac57c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaac580: ldr             x1, [x1, #0xe00]
    // 0xaac584: r0 = Expanded()
    //     0xaac584: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaac588: mov             x3, x0
    // 0xaac58c: r0 = 1
    //     0xaac58c: movz            x0, #0x1
    // 0xaac590: stur            x3, [fp, #-0x18]
    // 0xaac594: StoreField: r3->field_13 = r0
    //     0xaac594: stur            x0, [x3, #0x13]
    // 0xaac598: r0 = Instance_FlexFit
    //     0xaac598: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaac59c: ldr             x0, [x0, #0xe08]
    // 0xaac5a0: StoreField: r3->field_1b = r0
    //     0xaac5a0: stur            w0, [x3, #0x1b]
    // 0xaac5a4: ldur            x0, [fp, #-8]
    // 0xaac5a8: StoreField: r3->field_b = r0
    //     0xaac5a8: stur            w0, [x3, #0xb]
    // 0xaac5ac: r1 = Null
    //     0xaac5ac: mov             x1, NULL
    // 0xaac5b0: r2 = 6
    //     0xaac5b0: movz            x2, #0x6
    // 0xaac5b4: r0 = AllocateArray()
    //     0xaac5b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaac5b8: mov             x2, x0
    // 0xaac5bc: ldur            x0, [fp, #-0x30]
    // 0xaac5c0: stur            x2, [fp, #-8]
    // 0xaac5c4: StoreField: r2->field_f = r0
    //     0xaac5c4: stur            w0, [x2, #0xf]
    // 0xaac5c8: r16 = Instance_SizedBox
    //     0xaac5c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xaac5cc: ldr             x16, [x16, #0xb20]
    // 0xaac5d0: StoreField: r2->field_13 = r16
    //     0xaac5d0: stur            w16, [x2, #0x13]
    // 0xaac5d4: ldur            x0, [fp, #-0x18]
    // 0xaac5d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xaac5d8: stur            w0, [x2, #0x17]
    // 0xaac5dc: r1 = <Widget>
    //     0xaac5dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaac5e0: r0 = AllocateGrowableArray()
    //     0xaac5e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaac5e4: mov             x1, x0
    // 0xaac5e8: ldur            x0, [fp, #-8]
    // 0xaac5ec: stur            x1, [fp, #-0x18]
    // 0xaac5f0: StoreField: r1->field_f = r0
    //     0xaac5f0: stur            w0, [x1, #0xf]
    // 0xaac5f4: r0 = 6
    //     0xaac5f4: movz            x0, #0x6
    // 0xaac5f8: StoreField: r1->field_b = r0
    //     0xaac5f8: stur            w0, [x1, #0xb]
    // 0xaac5fc: r0 = Row()
    //     0xaac5fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaac600: mov             x1, x0
    // 0xaac604: r0 = Instance_Axis
    //     0xaac604: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaac608: stur            x1, [fp, #-8]
    // 0xaac60c: StoreField: r1->field_f = r0
    //     0xaac60c: stur            w0, [x1, #0xf]
    // 0xaac610: r0 = Instance_MainAxisAlignment
    //     0xaac610: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaac614: ldr             x0, [x0, #0xa08]
    // 0xaac618: StoreField: r1->field_13 = r0
    //     0xaac618: stur            w0, [x1, #0x13]
    // 0xaac61c: r0 = Instance_MainAxisSize
    //     0xaac61c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaac620: ldr             x0, [x0, #0xa10]
    // 0xaac624: ArrayStore: r1[0] = r0  ; List_4
    //     0xaac624: stur            w0, [x1, #0x17]
    // 0xaac628: r0 = Instance_CrossAxisAlignment
    //     0xaac628: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaac62c: ldr             x0, [x0, #0xa18]
    // 0xaac630: StoreField: r1->field_1b = r0
    //     0xaac630: stur            w0, [x1, #0x1b]
    // 0xaac634: r0 = Instance_VerticalDirection
    //     0xaac634: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaac638: ldr             x0, [x0, #0xa20]
    // 0xaac63c: StoreField: r1->field_23 = r0
    //     0xaac63c: stur            w0, [x1, #0x23]
    // 0xaac640: r0 = Instance_Clip
    //     0xaac640: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaac644: ldr             x0, [x0, #0x38]
    // 0xaac648: StoreField: r1->field_2b = r0
    //     0xaac648: stur            w0, [x1, #0x2b]
    // 0xaac64c: StoreField: r1->field_2f = rZR
    //     0xaac64c: stur            xzr, [x1, #0x2f]
    // 0xaac650: ldur            x0, [fp, #-0x18]
    // 0xaac654: StoreField: r1->field_b = r0
    //     0xaac654: stur            w0, [x1, #0xb]
    // 0xaac658: r0 = Container()
    //     0xaac658: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaac65c: stur            x0, [fp, #-0x18]
    // 0xaac660: r16 = Instance_EdgeInsets
    //     0xaac660: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaac664: ldr             x16, [x16, #0x980]
    // 0xaac668: ldur            lr, [fp, #-8]
    // 0xaac66c: stp             lr, x16, [SP]
    // 0xaac670: mov             x1, x0
    // 0xaac674: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xaac674: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xaac678: ldr             x4, [x4, #0x30]
    // 0xaac67c: r0 = Container()
    //     0xaac67c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaac680: r0 = GestureDetector()
    //     0xaac680: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaac684: ldur            x2, [fp, #-0x10]
    // 0xaac688: r1 = Function '<anonymous closure>':.
    //     0xaac688: add             x1, PP, #0x51, lsl #12  ; [pp+0x51cb8] AnonymousClosure: (0xaac140), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaac68c: ldr             x1, [x1, #0xcb8]
    // 0xaac690: stur            x0, [fp, #-8]
    // 0xaac694: r0 = AllocateClosure()
    //     0xaac694: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaac698: ldur            x16, [fp, #-0x18]
    // 0xaac69c: stp             x16, x0, [SP]
    // 0xaac6a0: ldur            x1, [fp, #-8]
    // 0xaac6a4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaac6a4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaac6a8: ldr             x4, [x4, #0xaf0]
    // 0xaac6ac: r0 = GestureDetector()
    //     0xaac6ac: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaac6b0: ldur            x0, [fp, #-8]
    // 0xaac6b4: LeaveFrame
    //     0xaac6b4: mov             SP, fp
    //     0xaac6b8: ldp             fp, lr, [SP], #0x10
    // 0xaac6bc: ret
    //     0xaac6bc: ret             
    // 0xaac6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaac6c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaac6c4: b               #0xaac26c
    // 0xaac6c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaac6c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaac6cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaac6cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Icon <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0xaac6d0, size: 0xc
    // 0xaac6d0: r0 = Instance_Icon
    //     0xaac6d0: add             x0, PP, #0x51, lsl #12  ; [pp+0x51cd8] Obj!Icon@d66631
    //     0xaac6d4: ldr             x0, [x0, #0xcd8]
    // 0xaac6d8: ret
    //     0xaac6d8: ret             
  }
  [closure] DropdownMenuItem<String> <anonymous closure>(dynamic, Filter) {
    // ** addr: 0xaacbec, size: 0xec
    // 0xaacbec: EnterFrame
    //     0xaacbec: stp             fp, lr, [SP, #-0x10]!
    //     0xaacbf0: mov             fp, SP
    // 0xaacbf4: AllocStack(0x30)
    //     0xaacbf4: sub             SP, SP, #0x30
    // 0xaacbf8: SetupParameters()
    //     0xaacbf8: ldr             x0, [fp, #0x18]
    //     0xaacbfc: ldur            w1, [x0, #0x17]
    //     0xaacc00: add             x1, x1, HEAP, lsl #32
    // 0xaacc04: CheckStackOverflow
    //     0xaacc04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaacc08: cmp             SP, x16
    //     0xaacc0c: b.ls            #0xaaccd0
    // 0xaacc10: ldr             x0, [fp, #0x10]
    // 0xaacc14: LoadField: r2 = r0->field_b
    //     0xaacc14: ldur            w2, [x0, #0xb]
    // 0xaacc18: DecompressPointer r2
    //     0xaacc18: add             x2, x2, HEAP, lsl #32
    // 0xaacc1c: stur            x2, [fp, #-0x10]
    // 0xaacc20: cmp             w2, NULL
    // 0xaacc24: b.ne            #0xaacc30
    // 0xaacc28: r0 = ""
    //     0xaacc28: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaacc2c: b               #0xaacc34
    // 0xaacc30: mov             x0, x2
    // 0xaacc34: stur            x0, [fp, #-8]
    // 0xaacc38: LoadField: r3 = r1->field_13
    //     0xaacc38: ldur            w3, [x1, #0x13]
    // 0xaacc3c: DecompressPointer r3
    //     0xaacc3c: add             x3, x3, HEAP, lsl #32
    // 0xaacc40: mov             x1, x3
    // 0xaacc44: r0 = of()
    //     0xaacc44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaacc48: LoadField: r1 = r0->field_87
    //     0xaacc48: ldur            w1, [x0, #0x87]
    // 0xaacc4c: DecompressPointer r1
    //     0xaacc4c: add             x1, x1, HEAP, lsl #32
    // 0xaacc50: LoadField: r0 = r1->field_2b
    //     0xaacc50: ldur            w0, [x1, #0x2b]
    // 0xaacc54: DecompressPointer r0
    //     0xaacc54: add             x0, x0, HEAP, lsl #32
    // 0xaacc58: r16 = 14.000000
    //     0xaacc58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaacc5c: ldr             x16, [x16, #0x1d8]
    // 0xaacc60: r30 = Instance_Color
    //     0xaacc60: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaacc64: stp             lr, x16, [SP]
    // 0xaacc68: mov             x1, x0
    // 0xaacc6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaacc6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaacc70: ldr             x4, [x4, #0xaa0]
    // 0xaacc74: r0 = copyWith()
    //     0xaacc74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaacc78: stur            x0, [fp, #-0x18]
    // 0xaacc7c: r0 = Text()
    //     0xaacc7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaacc80: mov             x2, x0
    // 0xaacc84: ldur            x0, [fp, #-8]
    // 0xaacc88: stur            x2, [fp, #-0x20]
    // 0xaacc8c: StoreField: r2->field_b = r0
    //     0xaacc8c: stur            w0, [x2, #0xb]
    // 0xaacc90: ldur            x0, [fp, #-0x18]
    // 0xaacc94: StoreField: r2->field_13 = r0
    //     0xaacc94: stur            w0, [x2, #0x13]
    // 0xaacc98: r1 = <String>
    //     0xaacc98: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xaacc9c: r0 = DropdownMenuItem()
    //     0xaacc9c: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0xaacca0: ldur            x1, [fp, #-0x10]
    // 0xaacca4: StoreField: r0->field_1b = r1
    //     0xaacca4: stur            w1, [x0, #0x1b]
    // 0xaacca8: r1 = true
    //     0xaacca8: add             x1, NULL, #0x20  ; true
    // 0xaaccac: StoreField: r0->field_1f = r1
    //     0xaaccac: stur            w1, [x0, #0x1f]
    // 0xaaccb0: r1 = Instance_AlignmentDirectional
    //     0xaaccb0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0xaaccb4: ldr             x1, [x1, #0xb70]
    // 0xaaccb8: StoreField: r0->field_f = r1
    //     0xaaccb8: stur            w1, [x0, #0xf]
    // 0xaaccbc: ldur            x1, [fp, #-0x20]
    // 0xaaccc0: StoreField: r0->field_b = r1
    //     0xaaccc0: stur            w1, [x0, #0xb]
    // 0xaaccc4: LeaveFrame
    //     0xaaccc4: mov             SP, fp
    //     0xaaccc8: ldp             fp, lr, [SP], #0x10
    // 0xaacccc: ret
    //     0xaacccc: ret             
    // 0xaaccd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaccd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaccd4: b               #0xaacc10
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaacdec, size: 0x70
    // 0xaacdec: EnterFrame
    //     0xaacdec: stp             fp, lr, [SP, #-0x10]!
    //     0xaacdf0: mov             fp, SP
    // 0xaacdf4: AllocStack(0x8)
    //     0xaacdf4: sub             SP, SP, #8
    // 0xaacdf8: SetupParameters()
    //     0xaacdf8: ldr             x0, [fp, #0x10]
    //     0xaacdfc: ldur            w3, [x0, #0x17]
    //     0xaace00: add             x3, x3, HEAP, lsl #32
    //     0xaace04: stur            x3, [fp, #-8]
    // 0xaace08: CheckStackOverflow
    //     0xaace08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaace0c: cmp             SP, x16
    //     0xaace10: b.ls            #0xaace54
    // 0xaace14: LoadField: r0 = r3->field_f
    //     0xaace14: ldur            w0, [x3, #0xf]
    // 0xaace18: DecompressPointer r0
    //     0xaace18: add             x0, x0, HEAP, lsl #32
    // 0xaace1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaace1c: ldur            w1, [x0, #0x17]
    // 0xaace20: DecompressPointer r1
    //     0xaace20: add             x1, x1, HEAP, lsl #32
    // 0xaace24: r2 = ""
    //     0xaace24: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaace28: r0 = text=()
    //     0xaace28: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0xaace2c: ldur            x1, [fp, #-8]
    // 0xaace30: LoadField: r2 = r1->field_f
    //     0xaace30: ldur            w2, [x1, #0xf]
    // 0xaace34: DecompressPointer r2
    //     0xaace34: add             x2, x2, HEAP, lsl #32
    // 0xaace38: r1 = false
    //     0xaace38: add             x1, NULL, #0x30  ; false
    // 0xaace3c: StoreField: r2->field_13 = r1
    //     0xaace3c: stur            w1, [x2, #0x13]
    // 0xaace40: StoreField: r2->field_1f = r1
    //     0xaace40: stur            w1, [x2, #0x1f]
    // 0xaace44: r0 = Null
    //     0xaace44: mov             x0, NULL
    // 0xaace48: LeaveFrame
    //     0xaace48: mov             SP, fp
    //     0xaace4c: ldp             fp, lr, [SP], #0x10
    // 0xaace50: ret
    //     0xaace50: ret             
    // 0xaace54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaace54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaace58: b               #0xaace14
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaace5c, size: 0xa8
    // 0xaace5c: EnterFrame
    //     0xaace5c: stp             fp, lr, [SP, #-0x10]!
    //     0xaace60: mov             fp, SP
    // 0xaace64: AllocStack(0x18)
    //     0xaace64: sub             SP, SP, #0x18
    // 0xaace68: SetupParameters()
    //     0xaace68: ldr             x0, [fp, #0x10]
    //     0xaace6c: ldur            w2, [x0, #0x17]
    //     0xaace70: add             x2, x2, HEAP, lsl #32
    //     0xaace74: stur            x2, [fp, #-8]
    // 0xaace78: CheckStackOverflow
    //     0xaace78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaace7c: cmp             SP, x16
    //     0xaace80: b.ls            #0xaacef8
    // 0xaace84: LoadField: r0 = r2->field_f
    //     0xaace84: ldur            w0, [x2, #0xf]
    // 0xaace88: DecompressPointer r0
    //     0xaace88: add             x0, x0, HEAP, lsl #32
    // 0xaace8c: LoadField: r1 = r0->field_b
    //     0xaace8c: ldur            w1, [x0, #0xb]
    // 0xaace90: DecompressPointer r1
    //     0xaace90: add             x1, x1, HEAP, lsl #32
    // 0xaace94: cmp             w1, NULL
    // 0xaace98: b.eq            #0xaacf00
    // 0xaace9c: LoadField: r0 = r1->field_23
    //     0xaace9c: ldur            w0, [x1, #0x23]
    // 0xaacea0: DecompressPointer r0
    //     0xaacea0: add             x0, x0, HEAP, lsl #32
    // 0xaacea4: str             x0, [SP]
    // 0xaacea8: r4 = 0
    //     0xaacea8: movz            x4, #0
    // 0xaaceac: ldr             x0, [SP]
    // 0xaaceb0: r16 = UnlinkedCall_0x613b5c
    //     0xaaceb0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51dd0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaaceb4: add             x16, x16, #0xdd0
    // 0xaaceb8: ldp             x5, lr, [x16]
    // 0xaacebc: blr             lr
    // 0xaacec0: ldur            x2, [fp, #-8]
    // 0xaacec4: LoadField: r0 = r2->field_f
    //     0xaacec4: ldur            w0, [x2, #0xf]
    // 0xaacec8: DecompressPointer r0
    //     0xaacec8: add             x0, x0, HEAP, lsl #32
    // 0xaacecc: stur            x0, [fp, #-0x10]
    // 0xaaced0: r1 = Function '<anonymous closure>':.
    //     0xaaced0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51de0] AnonymousClosure: (0xaacdec), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaaced4: ldr             x1, [x1, #0xde0]
    // 0xaaced8: r0 = AllocateClosure()
    //     0xaaced8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaacedc: ldur            x1, [fp, #-0x10]
    // 0xaacee0: mov             x2, x0
    // 0xaacee4: r0 = setState()
    //     0xaacee4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaacee8: r0 = Null
    //     0xaacee8: mov             x0, NULL
    // 0xaaceec: LeaveFrame
    //     0xaaceec: mov             SP, fp
    //     0xaacef0: ldp             fp, lr, [SP], #0x10
    // 0xaacef4: ret
    //     0xaacef4: ret             
    // 0xaacef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaacef8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaacefc: b               #0xaace84
    // 0xaacf00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaacf00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaad124, size: 0x2c
    // 0xaad124: r1 = false
    //     0xaad124: add             x1, NULL, #0x30  ; false
    // 0xaad128: ldr             x2, [SP]
    // 0xaad12c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xaad12c: ldur            w3, [x2, #0x17]
    // 0xaad130: DecompressPointer r3
    //     0xaad130: add             x3, x3, HEAP, lsl #32
    // 0xaad134: LoadField: r2 = r3->field_b
    //     0xaad134: ldur            w2, [x3, #0xb]
    // 0xaad138: DecompressPointer r2
    //     0xaad138: add             x2, x2, HEAP, lsl #32
    // 0xaad13c: LoadField: r3 = r2->field_f
    //     0xaad13c: ldur            w3, [x2, #0xf]
    // 0xaad140: DecompressPointer r3
    //     0xaad140: add             x3, x3, HEAP, lsl #32
    // 0xaad144: StoreField: r3->field_1f = r1
    //     0xaad144: stur            w1, [x3, #0x1f]
    // 0xaad148: r0 = Null
    //     0xaad148: mov             x0, NULL
    // 0xaad14c: ret
    //     0xaad14c: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xaad150, size: 0x188
    // 0xaad150: EnterFrame
    //     0xaad150: stp             fp, lr, [SP, #-0x10]!
    //     0xaad154: mov             fp, SP
    // 0xaad158: AllocStack(0x38)
    //     0xaad158: sub             SP, SP, #0x38
    // 0xaad15c: SetupParameters(_SearchViewState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xaad15c: stur            NULL, [fp, #-8]
    //     0xaad160: movz            x0, #0
    //     0xaad164: add             x1, fp, w0, sxtw #2
    //     0xaad168: ldr             x1, [x1, #0x18]
    //     0xaad16c: add             x2, fp, w0, sxtw #2
    //     0xaad170: ldr             x2, [x2, #0x10]
    //     0xaad174: stur            x2, [fp, #-0x18]
    //     0xaad178: ldur            w3, [x1, #0x17]
    //     0xaad17c: add             x3, x3, HEAP, lsl #32
    //     0xaad180: stur            x3, [fp, #-0x10]
    // 0xaad184: CheckStackOverflow
    //     0xaad184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaad188: cmp             SP, x16
    //     0xaad18c: b.ls            #0xaad2cc
    // 0xaad190: InitAsync() -> Future<void?>
    //     0xaad190: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0xaad194: bl              #0x6326e0  ; InitAsyncStub
    // 0xaad198: r1 = 1
    //     0xaad198: movz            x1, #0x1
    // 0xaad19c: r0 = AllocateContext()
    //     0xaad19c: bl              #0x16f6108  ; AllocateContextStub
    // 0xaad1a0: mov             x2, x0
    // 0xaad1a4: ldur            x0, [fp, #-0x10]
    // 0xaad1a8: stur            x2, [fp, #-0x20]
    // 0xaad1ac: StoreField: r2->field_b = r0
    //     0xaad1ac: stur            w0, [x2, #0xb]
    // 0xaad1b0: ldur            x1, [fp, #-0x18]
    // 0xaad1b4: r0 = trim()
    //     0xaad1b4: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xaad1b8: mov             x1, x0
    // 0xaad1bc: ldur            x2, [fp, #-0x20]
    // 0xaad1c0: StoreField: r2->field_f = r0
    //     0xaad1c0: stur            w0, [x2, #0xf]
    //     0xaad1c4: ldurb           w16, [x2, #-1]
    //     0xaad1c8: ldurb           w17, [x0, #-1]
    //     0xaad1cc: and             x16, x17, x16, lsr #2
    //     0xaad1d0: tst             x16, HEAP, lsr #32
    //     0xaad1d4: b.eq            #0xaad1dc
    //     0xaad1d8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xaad1dc: LoadField: r0 = r1->field_7
    //     0xaad1dc: ldur            w0, [x1, #7]
    // 0xaad1e0: cbz             w0, #0xaad298
    // 0xaad1e4: ldur            x3, [fp, #-0x10]
    // 0xaad1e8: r4 = true
    //     0xaad1e8: add             x4, NULL, #0x20  ; true
    // 0xaad1ec: LoadField: r5 = r3->field_f
    //     0xaad1ec: ldur            w5, [x3, #0xf]
    // 0xaad1f0: DecompressPointer r5
    //     0xaad1f0: add             x5, x5, HEAP, lsl #32
    // 0xaad1f4: stur            x5, [fp, #-0x28]
    // 0xaad1f8: StoreField: r5->field_13 = r4
    //     0xaad1f8: stur            w4, [x5, #0x13]
    // 0xaad1fc: r4 = LoadInt32Instr(r0)
    //     0xaad1fc: sbfx            x4, x0, #1, #0x1f
    // 0xaad200: cmp             x4, #3
    // 0xaad204: b.lt            #0xaad278
    // 0xaad208: LoadField: r0 = r5->field_b
    //     0xaad208: ldur            w0, [x5, #0xb]
    // 0xaad20c: DecompressPointer r0
    //     0xaad20c: add             x0, x0, HEAP, lsl #32
    // 0xaad210: cmp             w0, NULL
    // 0xaad214: b.eq            #0xaad2d4
    // 0xaad218: LoadField: r4 = r0->field_1b
    //     0xaad218: ldur            w4, [x0, #0x1b]
    // 0xaad21c: DecompressPointer r4
    //     0xaad21c: add             x4, x4, HEAP, lsl #32
    // 0xaad220: stp             x1, x4, [SP]
    // 0xaad224: r4 = 0
    //     0xaad224: movz            x4, #0
    // 0xaad228: ldr             x0, [SP, #8]
    // 0xaad22c: r16 = UnlinkedCall_0x613b5c
    //     0xaad22c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51de8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaad230: add             x16, x16, #0xde8
    // 0xaad234: ldp             x5, lr, [x16]
    // 0xaad238: blr             lr
    // 0xaad23c: mov             x1, x0
    // 0xaad240: stur            x1, [fp, #-0x18]
    // 0xaad244: r0 = Await()
    //     0xaad244: bl              #0x63248c  ; AwaitStub
    // 0xaad248: ldur            x0, [fp, #-0x10]
    // 0xaad24c: LoadField: r3 = r0->field_f
    //     0xaad24c: ldur            w3, [x0, #0xf]
    // 0xaad250: DecompressPointer r3
    //     0xaad250: add             x3, x3, HEAP, lsl #32
    // 0xaad254: ldur            x2, [fp, #-0x20]
    // 0xaad258: stur            x3, [fp, #-0x18]
    // 0xaad25c: r1 = Function '<anonymous closure>':.
    //     0xaad25c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51df8] AnonymousClosure: (0xaad370), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaad260: ldr             x1, [x1, #0xdf8]
    // 0xaad264: r0 = AllocateClosure()
    //     0xaad264: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaad268: ldur            x1, [fp, #-0x18]
    // 0xaad26c: mov             x2, x0
    // 0xaad270: r0 = setState()
    //     0xaad270: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaad274: b               #0xaad2c4
    // 0xaad278: ldur            x2, [fp, #-0x20]
    // 0xaad27c: r1 = Function '<anonymous closure>':.
    //     0xaad27c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e00] AnonymousClosure: (0xaad124), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaad280: ldr             x1, [x1, #0xe00]
    // 0xaad284: r0 = AllocateClosure()
    //     0xaad284: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaad288: ldur            x1, [fp, #-0x28]
    // 0xaad28c: mov             x2, x0
    // 0xaad290: r0 = setState()
    //     0xaad290: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaad294: b               #0xaad2c4
    // 0xaad298: ldur            x0, [fp, #-0x10]
    // 0xaad29c: LoadField: r3 = r0->field_f
    //     0xaad29c: ldur            w3, [x0, #0xf]
    // 0xaad2a0: DecompressPointer r3
    //     0xaad2a0: add             x3, x3, HEAP, lsl #32
    // 0xaad2a4: ldur            x2, [fp, #-0x20]
    // 0xaad2a8: stur            x3, [fp, #-0x18]
    // 0xaad2ac: r1 = Function '<anonymous closure>':.
    //     0xaad2ac: add             x1, PP, #0x51, lsl #12  ; [pp+0x51e08] AnonymousClosure: (0xaad2d8), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xaad2b0: ldr             x1, [x1, #0xe08]
    // 0xaad2b4: r0 = AllocateClosure()
    //     0xaad2b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaad2b8: ldur            x1, [fp, #-0x18]
    // 0xaad2bc: mov             x2, x0
    // 0xaad2c0: r0 = setState()
    //     0xaad2c0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaad2c4: r0 = Null
    //     0xaad2c4: mov             x0, NULL
    // 0xaad2c8: r0 = ReturnAsyncNotFuture()
    //     0xaad2c8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xaad2cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaad2cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaad2d0: b               #0xaad190
    // 0xaad2d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaad2d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaad2d8, size: 0x98
    // 0xaad2d8: EnterFrame
    //     0xaad2d8: stp             fp, lr, [SP, #-0x10]!
    //     0xaad2dc: mov             fp, SP
    // 0xaad2e0: AllocStack(0x10)
    //     0xaad2e0: sub             SP, SP, #0x10
    // 0xaad2e4: SetupParameters()
    //     0xaad2e4: add             x0, NULL, #0x30  ; false
    //     0xaad2e8: ldr             x1, [fp, #0x10]
    //     0xaad2ec: ldur            w2, [x1, #0x17]
    //     0xaad2f0: add             x2, x2, HEAP, lsl #32
    // 0xaad2e4: r0 = false
    // 0xaad2f4: CheckStackOverflow
    //     0xaad2f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaad2f8: cmp             SP, x16
    //     0xaad2fc: b.ls            #0xaad364
    // 0xaad300: LoadField: r1 = r2->field_b
    //     0xaad300: ldur            w1, [x2, #0xb]
    // 0xaad304: DecompressPointer r1
    //     0xaad304: add             x1, x1, HEAP, lsl #32
    // 0xaad308: LoadField: r3 = r1->field_f
    //     0xaad308: ldur            w3, [x1, #0xf]
    // 0xaad30c: DecompressPointer r3
    //     0xaad30c: add             x3, x3, HEAP, lsl #32
    // 0xaad310: StoreField: r3->field_13 = r0
    //     0xaad310: stur            w0, [x3, #0x13]
    // 0xaad314: StoreField: r3->field_1f = r0
    //     0xaad314: stur            w0, [x3, #0x1f]
    // 0xaad318: LoadField: r0 = r3->field_b
    //     0xaad318: ldur            w0, [x3, #0xb]
    // 0xaad31c: DecompressPointer r0
    //     0xaad31c: add             x0, x0, HEAP, lsl #32
    // 0xaad320: cmp             w0, NULL
    // 0xaad324: b.eq            #0xaad36c
    // 0xaad328: LoadField: r1 = r2->field_f
    //     0xaad328: ldur            w1, [x2, #0xf]
    // 0xaad32c: DecompressPointer r1
    //     0xaad32c: add             x1, x1, HEAP, lsl #32
    // 0xaad330: LoadField: r2 = r0->field_1f
    //     0xaad330: ldur            w2, [x0, #0x1f]
    // 0xaad334: DecompressPointer r2
    //     0xaad334: add             x2, x2, HEAP, lsl #32
    // 0xaad338: stp             x1, x2, [SP]
    // 0xaad33c: r4 = 0
    //     0xaad33c: movz            x4, #0
    // 0xaad340: ldr             x0, [SP, #8]
    // 0xaad344: r16 = UnlinkedCall_0x613b5c
    //     0xaad344: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaad348: add             x16, x16, #0xe10
    // 0xaad34c: ldp             x5, lr, [x16]
    // 0xaad350: blr             lr
    // 0xaad354: r0 = Null
    //     0xaad354: mov             x0, NULL
    // 0xaad358: LeaveFrame
    //     0xaad358: mov             SP, fp
    //     0xaad35c: ldp             fp, lr, [SP], #0x10
    // 0xaad360: ret
    //     0xaad360: ret             
    // 0xaad364: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaad364: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaad368: b               #0xaad300
    // 0xaad36c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaad36c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaad370, size: 0x48
    // 0xaad370: r1 = true
    //     0xaad370: add             x1, NULL, #0x20  ; true
    // 0xaad374: ldr             x2, [SP]
    // 0xaad378: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xaad378: ldur            w3, [x2, #0x17]
    // 0xaad37c: DecompressPointer r3
    //     0xaad37c: add             x3, x3, HEAP, lsl #32
    // 0xaad380: LoadField: r2 = r3->field_b
    //     0xaad380: ldur            w2, [x3, #0xb]
    // 0xaad384: DecompressPointer r2
    //     0xaad384: add             x2, x2, HEAP, lsl #32
    // 0xaad388: LoadField: r3 = r2->field_f
    //     0xaad388: ldur            w3, [x2, #0xf]
    // 0xaad38c: DecompressPointer r3
    //     0xaad38c: add             x3, x3, HEAP, lsl #32
    // 0xaad390: LoadField: r2 = r3->field_b
    //     0xaad390: ldur            w2, [x3, #0xb]
    // 0xaad394: DecompressPointer r2
    //     0xaad394: add             x2, x2, HEAP, lsl #32
    // 0xaad398: cmp             w2, NULL
    // 0xaad39c: b.eq            #0xaad3ac
    // 0xaad3a0: StoreField: r3->field_1f = r1
    //     0xaad3a0: stur            w1, [x3, #0x1f]
    // 0xaad3a4: r0 = Null
    //     0xaad3a4: mov             x0, NULL
    // 0xaad3a8: ret
    //     0xaad3a8: ret             
    // 0xaad3ac: EnterFrame
    //     0xaad3ac: stp             fp, lr, [SP, #-0x10]!
    //     0xaad3b0: mov             fp, SP
    // 0xaad3b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaad3b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc16ba0, size: 0x1190
    // 0xc16ba0: EnterFrame
    //     0xc16ba0: stp             fp, lr, [SP, #-0x10]!
    //     0xc16ba4: mov             fp, SP
    // 0xc16ba8: AllocStack(0x90)
    //     0xc16ba8: sub             SP, SP, #0x90
    // 0xc16bac: SetupParameters(_SearchViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc16bac: mov             x0, x1
    //     0xc16bb0: stur            x1, [fp, #-8]
    //     0xc16bb4: mov             x1, x2
    //     0xc16bb8: stur            x2, [fp, #-0x10]
    // 0xc16bbc: CheckStackOverflow
    //     0xc16bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc16bc0: cmp             SP, x16
    //     0xc16bc4: b.ls            #0xc17d0c
    // 0xc16bc8: r1 = 2
    //     0xc16bc8: movz            x1, #0x2
    // 0xc16bcc: r0 = AllocateContext()
    //     0xc16bcc: bl              #0x16f6108  ; AllocateContextStub
    // 0xc16bd0: mov             x2, x0
    // 0xc16bd4: ldur            x0, [fp, #-8]
    // 0xc16bd8: stur            x2, [fp, #-0x18]
    // 0xc16bdc: StoreField: r2->field_f = r0
    //     0xc16bdc: stur            w0, [x2, #0xf]
    // 0xc16be0: ldur            x1, [fp, #-0x10]
    // 0xc16be4: StoreField: r2->field_13 = r1
    //     0xc16be4: stur            w1, [x2, #0x13]
    // 0xc16be8: r0 = of()
    //     0xc16be8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc16bec: LoadField: r1 = r0->field_87
    //     0xc16bec: ldur            w1, [x0, #0x87]
    // 0xc16bf0: DecompressPointer r1
    //     0xc16bf0: add             x1, x1, HEAP, lsl #32
    // 0xc16bf4: LoadField: r0 = r1->field_2b
    //     0xc16bf4: ldur            w0, [x1, #0x2b]
    // 0xc16bf8: DecompressPointer r0
    //     0xc16bf8: add             x0, x0, HEAP, lsl #32
    // 0xc16bfc: stur            x0, [fp, #-0x10]
    // 0xc16c00: r1 = Instance_Color
    //     0xc16c00: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc16c04: d0 = 0.700000
    //     0xc16c04: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc16c08: ldr             d0, [x17, #0xf48]
    // 0xc16c0c: r0 = withOpacity()
    //     0xc16c0c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc16c10: r16 = 16.000000
    //     0xc16c10: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc16c14: ldr             x16, [x16, #0x188]
    // 0xc16c18: stp             x0, x16, [SP]
    // 0xc16c1c: ldur            x1, [fp, #-0x10]
    // 0xc16c20: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc16c20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc16c24: ldr             x4, [x4, #0xaa0]
    // 0xc16c28: r0 = copyWith()
    //     0xc16c28: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc16c2c: mov             x2, x0
    // 0xc16c30: ldur            x0, [fp, #-8]
    // 0xc16c34: stur            x2, [fp, #-0x20]
    // 0xc16c38: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc16c38: ldur            w3, [x0, #0x17]
    // 0xc16c3c: DecompressPointer r3
    //     0xc16c3c: add             x3, x3, HEAP, lsl #32
    // 0xc16c40: ldur            x4, [fp, #-0x18]
    // 0xc16c44: stur            x3, [fp, #-0x10]
    // 0xc16c48: LoadField: r1 = r4->field_13
    //     0xc16c48: ldur            w1, [x4, #0x13]
    // 0xc16c4c: DecompressPointer r1
    //     0xc16c4c: add             x1, x1, HEAP, lsl #32
    // 0xc16c50: r0 = of()
    //     0xc16c50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc16c54: LoadField: r1 = r0->field_87
    //     0xc16c54: ldur            w1, [x0, #0x87]
    // 0xc16c58: DecompressPointer r1
    //     0xc16c58: add             x1, x1, HEAP, lsl #32
    // 0xc16c5c: LoadField: r0 = r1->field_2b
    //     0xc16c5c: ldur            w0, [x1, #0x2b]
    // 0xc16c60: DecompressPointer r0
    //     0xc16c60: add             x0, x0, HEAP, lsl #32
    // 0xc16c64: stur            x0, [fp, #-0x28]
    // 0xc16c68: r1 = Instance_Color
    //     0xc16c68: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc16c6c: d0 = 0.400000
    //     0xc16c6c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xc16c70: r0 = withOpacity()
    //     0xc16c70: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc16c74: r16 = 16.000000
    //     0xc16c74: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc16c78: ldr             x16, [x16, #0x188]
    // 0xc16c7c: stp             x16, x0, [SP]
    // 0xc16c80: ldur            x1, [fp, #-0x28]
    // 0xc16c84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc16c84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc16c88: ldr             x4, [x4, #0x9b8]
    // 0xc16c8c: r0 = copyWith()
    //     0xc16c8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc16c90: ldur            x2, [fp, #-0x18]
    // 0xc16c94: stur            x0, [fp, #-0x28]
    // 0xc16c98: LoadField: r1 = r2->field_13
    //     0xc16c98: ldur            w1, [x2, #0x13]
    // 0xc16c9c: DecompressPointer r1
    //     0xc16c9c: add             x1, x1, HEAP, lsl #32
    // 0xc16ca0: r0 = of()
    //     0xc16ca0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc16ca4: LoadField: r1 = r0->field_5b
    //     0xc16ca4: ldur            w1, [x0, #0x5b]
    // 0xc16ca8: DecompressPointer r1
    //     0xc16ca8: add             x1, x1, HEAP, lsl #32
    // 0xc16cac: stur            x1, [fp, #-0x30]
    // 0xc16cb0: r0 = ColorFilter()
    //     0xc16cb0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc16cb4: mov             x1, x0
    // 0xc16cb8: ldur            x0, [fp, #-0x30]
    // 0xc16cbc: stur            x1, [fp, #-0x38]
    // 0xc16cc0: StoreField: r1->field_7 = r0
    //     0xc16cc0: stur            w0, [x1, #7]
    // 0xc16cc4: r0 = Instance_BlendMode
    //     0xc16cc4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc16cc8: ldr             x0, [x0, #0xb30]
    // 0xc16ccc: StoreField: r1->field_b = r0
    //     0xc16ccc: stur            w0, [x1, #0xb]
    // 0xc16cd0: r2 = 1
    //     0xc16cd0: movz            x2, #0x1
    // 0xc16cd4: StoreField: r1->field_13 = r2
    //     0xc16cd4: stur            x2, [x1, #0x13]
    // 0xc16cd8: r0 = SvgPicture()
    //     0xc16cd8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc16cdc: stur            x0, [fp, #-0x30]
    // 0xc16ce0: ldur            x16, [fp, #-0x38]
    // 0xc16ce4: str             x16, [SP]
    // 0xc16ce8: mov             x1, x0
    // 0xc16cec: r2 = "assets/images/search.svg"
    //     0xc16cec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0xc16cf0: ldr             x2, [x2, #0xa30]
    // 0xc16cf4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc16cf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc16cf8: ldr             x4, [x4, #0xa38]
    // 0xc16cfc: r0 = SvgPicture.asset()
    //     0xc16cfc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc16d00: r0 = Align()
    //     0xc16d00: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc16d04: mov             x1, x0
    // 0xc16d08: r0 = Instance_Alignment
    //     0xc16d08: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc16d0c: ldr             x0, [x0, #0xb10]
    // 0xc16d10: stur            x1, [fp, #-0x38]
    // 0xc16d14: StoreField: r1->field_f = r0
    //     0xc16d14: stur            w0, [x1, #0xf]
    // 0xc16d18: r0 = 1.000000
    //     0xc16d18: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc16d1c: StoreField: r1->field_13 = r0
    //     0xc16d1c: stur            w0, [x1, #0x13]
    // 0xc16d20: ArrayStore: r1[0] = r0  ; List_4
    //     0xc16d20: stur            w0, [x1, #0x17]
    // 0xc16d24: ldur            x2, [fp, #-0x30]
    // 0xc16d28: StoreField: r1->field_b = r2
    //     0xc16d28: stur            w2, [x1, #0xb]
    // 0xc16d2c: r0 = InputDecoration()
    //     0xc16d2c: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0xc16d30: mov             x3, x0
    // 0xc16d34: r0 = "Search"
    //     0xc16d34: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c08] "Search"
    //     0xc16d38: ldr             x0, [x0, #0xc08]
    // 0xc16d3c: stur            x3, [fp, #-0x30]
    // 0xc16d40: StoreField: r3->field_2f = r0
    //     0xc16d40: stur            w0, [x3, #0x2f]
    // 0xc16d44: ldur            x0, [fp, #-0x28]
    // 0xc16d48: StoreField: r3->field_37 = r0
    //     0xc16d48: stur            w0, [x3, #0x37]
    // 0xc16d4c: r0 = true
    //     0xc16d4c: add             x0, NULL, #0x20  ; true
    // 0xc16d50: StoreField: r3->field_47 = r0
    //     0xc16d50: stur            w0, [x3, #0x47]
    // 0xc16d54: StoreField: r3->field_4b = r0
    //     0xc16d54: stur            w0, [x3, #0x4b]
    // 0xc16d58: ldur            x1, [fp, #-0x38]
    // 0xc16d5c: StoreField: r3->field_73 = r1
    //     0xc16d5c: stur            w1, [x3, #0x73]
    // 0xc16d60: r1 = Instance__NoInputBorder
    //     0xc16d60: add             x1, PP, #0x33, lsl #12  ; [pp+0x33ee8] Obj!_NoInputBorder@d5ad71
    //     0xc16d64: ldr             x1, [x1, #0xee8]
    // 0xc16d68: StoreField: r3->field_d3 = r1
    //     0xc16d68: stur            w1, [x3, #0xd3]
    // 0xc16d6c: StoreField: r3->field_d7 = r0
    //     0xc16d6c: stur            w0, [x3, #0xd7]
    // 0xc16d70: ldur            x2, [fp, #-0x18]
    // 0xc16d74: r1 = Function '<anonymous closure>':.
    //     0xc16d74: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c10] AnonymousClosure: (0xc18218), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc16d78: ldr             x1, [x1, #0xc10]
    // 0xc16d7c: r0 = AllocateClosure()
    //     0xc16d7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc16d80: ldur            x2, [fp, #-0x18]
    // 0xc16d84: r1 = Function '<anonymous closure>':.
    //     0xc16d84: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c18] AnonymousClosure: (0xaad150), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc16d88: ldr             x1, [x1, #0xc18]
    // 0xc16d8c: stur            x0, [fp, #-0x28]
    // 0xc16d90: r0 = AllocateClosure()
    //     0xc16d90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc16d94: r1 = <String>
    //     0xc16d94: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xc16d98: stur            x0, [fp, #-0x38]
    // 0xc16d9c: r0 = TextFormField()
    //     0xc16d9c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xc16da0: stur            x0, [fp, #-0x40]
    // 0xc16da4: r16 = true
    //     0xc16da4: add             x16, NULL, #0x20  ; true
    // 0xc16da8: ldur            lr, [fp, #-0x20]
    // 0xc16dac: stp             lr, x16, [SP, #0x20]
    // 0xc16db0: ldur            x16, [fp, #-0x10]
    // 0xc16db4: r30 = Instance_TextInputAction
    //     0xc16db4: ldr             lr, [PP, #0x7460]  ; [pp+0x7460] Obj!TextInputAction@d72a01
    // 0xc16db8: stp             lr, x16, [SP, #0x10]
    // 0xc16dbc: ldur            x16, [fp, #-0x28]
    // 0xc16dc0: ldur            lr, [fp, #-0x38]
    // 0xc16dc4: stp             lr, x16, [SP]
    // 0xc16dc8: mov             x1, x0
    // 0xc16dcc: ldur            x2, [fp, #-0x30]
    // 0xc16dd0: r4 = const [0, 0x8, 0x6, 0x2, autofocus, 0x2, controller, 0x4, onChanged, 0x7, onFieldSubmitted, 0x6, style, 0x3, textInputAction, 0x5, null]
    //     0xc16dd0: add             x4, PP, #0x51, lsl #12  ; [pp+0x51c20] List(17) [0, 0x8, 0x6, 0x2, "autofocus", 0x2, "controller", 0x4, "onChanged", 0x7, "onFieldSubmitted", 0x6, "style", 0x3, "textInputAction", 0x5, Null]
    //     0xc16dd4: ldr             x4, [x4, #0xc20]
    // 0xc16dd8: r0 = TextFormField()
    //     0xc16dd8: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xc16ddc: r0 = Padding()
    //     0xc16ddc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc16de0: mov             x2, x0
    // 0xc16de4: r0 = Instance_EdgeInsets
    //     0xc16de4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f078] Obj!EdgeInsets@d571a1
    //     0xc16de8: ldr             x0, [x0, #0x78]
    // 0xc16dec: stur            x2, [fp, #-0x20]
    // 0xc16df0: StoreField: r2->field_f = r0
    //     0xc16df0: stur            w0, [x2, #0xf]
    // 0xc16df4: ldur            x0, [fp, #-0x40]
    // 0xc16df8: StoreField: r2->field_b = r0
    //     0xc16df8: stur            w0, [x2, #0xb]
    // 0xc16dfc: r1 = <FlexParentData>
    //     0xc16dfc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc16e00: ldr             x1, [x1, #0xe00]
    // 0xc16e04: r0 = Expanded()
    //     0xc16e04: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc16e08: mov             x2, x0
    // 0xc16e0c: r0 = 1
    //     0xc16e0c: movz            x0, #0x1
    // 0xc16e10: stur            x2, [fp, #-0x28]
    // 0xc16e14: StoreField: r2->field_13 = r0
    //     0xc16e14: stur            x0, [x2, #0x13]
    // 0xc16e18: r3 = Instance_FlexFit
    //     0xc16e18: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc16e1c: ldr             x3, [x3, #0xe08]
    // 0xc16e20: StoreField: r2->field_1b = r3
    //     0xc16e20: stur            w3, [x2, #0x1b]
    // 0xc16e24: ldur            x1, [fp, #-0x20]
    // 0xc16e28: StoreField: r2->field_b = r1
    //     0xc16e28: stur            w1, [x2, #0xb]
    // 0xc16e2c: ldur            x4, [fp, #-8]
    // 0xc16e30: LoadField: r5 = r4->field_13
    //     0xc16e30: ldur            w5, [x4, #0x13]
    // 0xc16e34: DecompressPointer r5
    //     0xc16e34: add             x5, x5, HEAP, lsl #32
    // 0xc16e38: ldur            x6, [fp, #-0x18]
    // 0xc16e3c: stur            x5, [fp, #-0x20]
    // 0xc16e40: LoadField: r1 = r6->field_13
    //     0xc16e40: ldur            w1, [x6, #0x13]
    // 0xc16e44: DecompressPointer r1
    //     0xc16e44: add             x1, x1, HEAP, lsl #32
    // 0xc16e48: r0 = of()
    //     0xc16e48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc16e4c: LoadField: r1 = r0->field_5b
    //     0xc16e4c: ldur            w1, [x0, #0x5b]
    // 0xc16e50: DecompressPointer r1
    //     0xc16e50: add             x1, x1, HEAP, lsl #32
    // 0xc16e54: stur            x1, [fp, #-0x30]
    // 0xc16e58: r0 = ColorFilter()
    //     0xc16e58: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc16e5c: mov             x1, x0
    // 0xc16e60: ldur            x0, [fp, #-0x30]
    // 0xc16e64: stur            x1, [fp, #-0x38]
    // 0xc16e68: StoreField: r1->field_7 = r0
    //     0xc16e68: stur            w0, [x1, #7]
    // 0xc16e6c: r0 = Instance_BlendMode
    //     0xc16e6c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc16e70: ldr             x0, [x0, #0xb30]
    // 0xc16e74: StoreField: r1->field_b = r0
    //     0xc16e74: stur            w0, [x1, #0xb]
    // 0xc16e78: r2 = 1
    //     0xc16e78: movz            x2, #0x1
    // 0xc16e7c: StoreField: r1->field_13 = r2
    //     0xc16e7c: stur            x2, [x1, #0x13]
    // 0xc16e80: r0 = SvgPicture()
    //     0xc16e80: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc16e84: stur            x0, [fp, #-0x30]
    // 0xc16e88: ldur            x16, [fp, #-0x38]
    // 0xc16e8c: str             x16, [SP]
    // 0xc16e90: mov             x1, x0
    // 0xc16e94: r2 = "assets/images/x.svg"
    //     0xc16e94: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xc16e98: ldr             x2, [x2, #0x5e8]
    // 0xc16e9c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc16e9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc16ea0: ldr             x4, [x4, #0xa38]
    // 0xc16ea4: r0 = SvgPicture.asset()
    //     0xc16ea4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc16ea8: r0 = InkWell()
    //     0xc16ea8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc16eac: mov             x3, x0
    // 0xc16eb0: ldur            x0, [fp, #-0x30]
    // 0xc16eb4: stur            x3, [fp, #-0x38]
    // 0xc16eb8: StoreField: r3->field_b = r0
    //     0xc16eb8: stur            w0, [x3, #0xb]
    // 0xc16ebc: ldur            x2, [fp, #-0x18]
    // 0xc16ec0: r1 = Function '<anonymous closure>':.
    //     0xc16ec0: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c28] AnonymousClosure: (0xaace5c), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc16ec4: ldr             x1, [x1, #0xc28]
    // 0xc16ec8: r0 = AllocateClosure()
    //     0xc16ec8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc16ecc: mov             x1, x0
    // 0xc16ed0: ldur            x0, [fp, #-0x38]
    // 0xc16ed4: StoreField: r0->field_f = r1
    //     0xc16ed4: stur            w1, [x0, #0xf]
    // 0xc16ed8: r1 = true
    //     0xc16ed8: add             x1, NULL, #0x20  ; true
    // 0xc16edc: StoreField: r0->field_43 = r1
    //     0xc16edc: stur            w1, [x0, #0x43]
    // 0xc16ee0: r2 = Instance_BoxShape
    //     0xc16ee0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc16ee4: ldr             x2, [x2, #0x80]
    // 0xc16ee8: StoreField: r0->field_47 = r2
    //     0xc16ee8: stur            w2, [x0, #0x47]
    // 0xc16eec: StoreField: r0->field_6f = r1
    //     0xc16eec: stur            w1, [x0, #0x6f]
    // 0xc16ef0: r2 = false
    //     0xc16ef0: add             x2, NULL, #0x30  ; false
    // 0xc16ef4: StoreField: r0->field_73 = r2
    //     0xc16ef4: stur            w2, [x0, #0x73]
    // 0xc16ef8: StoreField: r0->field_83 = r1
    //     0xc16ef8: stur            w1, [x0, #0x83]
    // 0xc16efc: StoreField: r0->field_7b = r2
    //     0xc16efc: stur            w2, [x0, #0x7b]
    // 0xc16f00: r0 = Visibility()
    //     0xc16f00: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc16f04: mov             x1, x0
    // 0xc16f08: ldur            x0, [fp, #-0x38]
    // 0xc16f0c: stur            x1, [fp, #-0x30]
    // 0xc16f10: StoreField: r1->field_b = r0
    //     0xc16f10: stur            w0, [x1, #0xb]
    // 0xc16f14: r0 = Instance_SizedBox
    //     0xc16f14: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc16f18: StoreField: r1->field_f = r0
    //     0xc16f18: stur            w0, [x1, #0xf]
    // 0xc16f1c: ldur            x2, [fp, #-0x20]
    // 0xc16f20: StoreField: r1->field_13 = r2
    //     0xc16f20: stur            w2, [x1, #0x13]
    // 0xc16f24: r2 = false
    //     0xc16f24: add             x2, NULL, #0x30  ; false
    // 0xc16f28: ArrayStore: r1[0] = r2  ; List_4
    //     0xc16f28: stur            w2, [x1, #0x17]
    // 0xc16f2c: StoreField: r1->field_1b = r2
    //     0xc16f2c: stur            w2, [x1, #0x1b]
    // 0xc16f30: StoreField: r1->field_1f = r2
    //     0xc16f30: stur            w2, [x1, #0x1f]
    // 0xc16f34: StoreField: r1->field_23 = r2
    //     0xc16f34: stur            w2, [x1, #0x23]
    // 0xc16f38: StoreField: r1->field_27 = r2
    //     0xc16f38: stur            w2, [x1, #0x27]
    // 0xc16f3c: StoreField: r1->field_2b = r2
    //     0xc16f3c: stur            w2, [x1, #0x2b]
    // 0xc16f40: r0 = Padding()
    //     0xc16f40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc16f44: mov             x3, x0
    // 0xc16f48: r0 = Instance_EdgeInsets
    //     0xc16f48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xc16f4c: ldr             x0, [x0, #0xd48]
    // 0xc16f50: stur            x3, [fp, #-0x20]
    // 0xc16f54: StoreField: r3->field_f = r0
    //     0xc16f54: stur            w0, [x3, #0xf]
    // 0xc16f58: ldur            x1, [fp, #-0x30]
    // 0xc16f5c: StoreField: r3->field_b = r1
    //     0xc16f5c: stur            w1, [x3, #0xb]
    // 0xc16f60: r1 = Null
    //     0xc16f60: mov             x1, NULL
    // 0xc16f64: r2 = 4
    //     0xc16f64: movz            x2, #0x4
    // 0xc16f68: r0 = AllocateArray()
    //     0xc16f68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc16f6c: mov             x2, x0
    // 0xc16f70: ldur            x0, [fp, #-0x28]
    // 0xc16f74: stur            x2, [fp, #-0x30]
    // 0xc16f78: StoreField: r2->field_f = r0
    //     0xc16f78: stur            w0, [x2, #0xf]
    // 0xc16f7c: ldur            x0, [fp, #-0x20]
    // 0xc16f80: StoreField: r2->field_13 = r0
    //     0xc16f80: stur            w0, [x2, #0x13]
    // 0xc16f84: r1 = <Widget>
    //     0xc16f84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc16f88: r0 = AllocateGrowableArray()
    //     0xc16f88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc16f8c: mov             x1, x0
    // 0xc16f90: ldur            x0, [fp, #-0x30]
    // 0xc16f94: stur            x1, [fp, #-0x20]
    // 0xc16f98: StoreField: r1->field_f = r0
    //     0xc16f98: stur            w0, [x1, #0xf]
    // 0xc16f9c: r2 = 4
    //     0xc16f9c: movz            x2, #0x4
    // 0xc16fa0: StoreField: r1->field_b = r2
    //     0xc16fa0: stur            w2, [x1, #0xb]
    // 0xc16fa4: r0 = Row()
    //     0xc16fa4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc16fa8: mov             x3, x0
    // 0xc16fac: r0 = Instance_Axis
    //     0xc16fac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc16fb0: stur            x3, [fp, #-0x28]
    // 0xc16fb4: StoreField: r3->field_f = r0
    //     0xc16fb4: stur            w0, [x3, #0xf]
    // 0xc16fb8: r4 = Instance_MainAxisAlignment
    //     0xc16fb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc16fbc: ldr             x4, [x4, #0xa08]
    // 0xc16fc0: StoreField: r3->field_13 = r4
    //     0xc16fc0: stur            w4, [x3, #0x13]
    // 0xc16fc4: r5 = Instance_MainAxisSize
    //     0xc16fc4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc16fc8: ldr             x5, [x5, #0xa10]
    // 0xc16fcc: ArrayStore: r3[0] = r5  ; List_4
    //     0xc16fcc: stur            w5, [x3, #0x17]
    // 0xc16fd0: r1 = Instance_CrossAxisAlignment
    //     0xc16fd0: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xc16fd4: ldr             x1, [x1, #0xc68]
    // 0xc16fd8: StoreField: r3->field_1b = r1
    //     0xc16fd8: stur            w1, [x3, #0x1b]
    // 0xc16fdc: r6 = Instance_VerticalDirection
    //     0xc16fdc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc16fe0: ldr             x6, [x6, #0xa20]
    // 0xc16fe4: StoreField: r3->field_23 = r6
    //     0xc16fe4: stur            w6, [x3, #0x23]
    // 0xc16fe8: r7 = Instance_Clip
    //     0xc16fe8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc16fec: ldr             x7, [x7, #0x38]
    // 0xc16ff0: StoreField: r3->field_2b = r7
    //     0xc16ff0: stur            w7, [x3, #0x2b]
    // 0xc16ff4: StoreField: r3->field_2f = rZR
    //     0xc16ff4: stur            xzr, [x3, #0x2f]
    // 0xc16ff8: ldur            x1, [fp, #-0x20]
    // 0xc16ffc: StoreField: r3->field_b = r1
    //     0xc16ffc: stur            w1, [x3, #0xb]
    // 0xc17000: ldur            x8, [fp, #-8]
    // 0xc17004: LoadField: r1 = r8->field_b
    //     0xc17004: ldur            w1, [x8, #0xb]
    // 0xc17008: DecompressPointer r1
    //     0xc17008: add             x1, x1, HEAP, lsl #32
    // 0xc1700c: cmp             w1, NULL
    // 0xc17010: b.eq            #0xc17d14
    // 0xc17014: LoadField: r2 = r1->field_b
    //     0xc17014: ldur            w2, [x1, #0xb]
    // 0xc17018: DecompressPointer r2
    //     0xc17018: add             x2, x2, HEAP, lsl #32
    // 0xc1701c: LoadField: r9 = r2->field_f
    //     0xc1701c: ldur            w9, [x2, #0xf]
    // 0xc17020: DecompressPointer r9
    //     0xc17020: add             x9, x9, HEAP, lsl #32
    // 0xc17024: cmp             w9, NULL
    // 0xc17028: b.eq            #0xc17050
    // 0xc1702c: LoadField: r2 = r1->field_3f
    //     0xc1702c: ldur            w2, [x1, #0x3f]
    // 0xc17030: DecompressPointer r2
    //     0xc17030: add             x2, x2, HEAP, lsl #32
    // 0xc17034: LoadField: r9 = r2->field_b
    //     0xc17034: ldur            w9, [x2, #0xb]
    // 0xc17038: cbz             w9, #0xc17044
    // 0xc1703c: r2 = false
    //     0xc1703c: add             x2, NULL, #0x30  ; false
    // 0xc17040: b               #0xc17048
    // 0xc17044: r2 = true
    //     0xc17044: add             x2, NULL, #0x20  ; true
    // 0xc17048: mov             x9, x2
    // 0xc1704c: b               #0xc17054
    // 0xc17050: r9 = false
    //     0xc17050: add             x9, NULL, #0x30  ; false
    // 0xc17054: stur            x9, [fp, #-0x20]
    // 0xc17058: LoadField: r2 = r1->field_3f
    //     0xc17058: ldur            w2, [x1, #0x3f]
    // 0xc1705c: DecompressPointer r2
    //     0xc1705c: add             x2, x2, HEAP, lsl #32
    // 0xc17060: LoadField: r1 = r2->field_b
    //     0xc17060: ldur            w1, [x2, #0xb]
    // 0xc17064: cbnz            w1, #0xc17148
    // 0xc17068: ldur            x10, [fp, #-0x18]
    // 0xc1706c: ldur            x11, [fp, #-0x10]
    // 0xc17070: r1 = Null
    //     0xc17070: mov             x1, NULL
    // 0xc17074: r2 = 6
    //     0xc17074: movz            x2, #0x6
    // 0xc17078: r0 = AllocateArray()
    //     0xc17078: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc1707c: r16 = "No results found for \""
    //     0xc1707c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c30] "No results found for \""
    //     0xc17080: ldr             x16, [x16, #0xc30]
    // 0xc17084: StoreField: r0->field_f = r16
    //     0xc17084: stur            w16, [x0, #0xf]
    // 0xc17088: ldur            x1, [fp, #-0x10]
    // 0xc1708c: LoadField: r2 = r1->field_27
    //     0xc1708c: ldur            w2, [x1, #0x27]
    // 0xc17090: DecompressPointer r2
    //     0xc17090: add             x2, x2, HEAP, lsl #32
    // 0xc17094: LoadField: r1 = r2->field_7
    //     0xc17094: ldur            w1, [x2, #7]
    // 0xc17098: DecompressPointer r1
    //     0xc17098: add             x1, x1, HEAP, lsl #32
    // 0xc1709c: StoreField: r0->field_13 = r1
    //     0xc1709c: stur            w1, [x0, #0x13]
    // 0xc170a0: r16 = "\""
    //     0xc170a0: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0xc170a4: ldr             x16, [x16, #0x550]
    // 0xc170a8: ArrayStore: r0[0] = r16  ; List_4
    //     0xc170a8: stur            w16, [x0, #0x17]
    // 0xc170ac: str             x0, [SP]
    // 0xc170b0: r0 = _interpolate()
    //     0xc170b0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc170b4: ldur            x2, [fp, #-0x18]
    // 0xc170b8: stur            x0, [fp, #-0x10]
    // 0xc170bc: LoadField: r1 = r2->field_13
    //     0xc170bc: ldur            w1, [x2, #0x13]
    // 0xc170c0: DecompressPointer r1
    //     0xc170c0: add             x1, x1, HEAP, lsl #32
    // 0xc170c4: r0 = of()
    //     0xc170c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc170c8: LoadField: r1 = r0->field_87
    //     0xc170c8: ldur            w1, [x0, #0x87]
    // 0xc170cc: DecompressPointer r1
    //     0xc170cc: add             x1, x1, HEAP, lsl #32
    // 0xc170d0: LoadField: r0 = r1->field_7
    //     0xc170d0: ldur            w0, [x1, #7]
    // 0xc170d4: DecompressPointer r0
    //     0xc170d4: add             x0, x0, HEAP, lsl #32
    // 0xc170d8: stur            x0, [fp, #-0x30]
    // 0xc170dc: r1 = Instance_Color
    //     0xc170dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc170e0: d0 = 0.400000
    //     0xc170e0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xc170e4: r0 = withOpacity()
    //     0xc170e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc170e8: r16 = 16.000000
    //     0xc170e8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc170ec: ldr             x16, [x16, #0x188]
    // 0xc170f0: stp             x16, x0, [SP]
    // 0xc170f4: ldur            x1, [fp, #-0x30]
    // 0xc170f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc170f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc170fc: ldr             x4, [x4, #0x9b8]
    // 0xc17100: r0 = copyWith()
    //     0xc17100: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc17104: stur            x0, [fp, #-0x30]
    // 0xc17108: r0 = Text()
    //     0xc17108: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc1710c: mov             x1, x0
    // 0xc17110: ldur            x0, [fp, #-0x10]
    // 0xc17114: stur            x1, [fp, #-0x38]
    // 0xc17118: StoreField: r1->field_b = r0
    //     0xc17118: stur            w0, [x1, #0xb]
    // 0xc1711c: ldur            x0, [fp, #-0x30]
    // 0xc17120: StoreField: r1->field_13 = r0
    //     0xc17120: stur            w0, [x1, #0x13]
    // 0xc17124: r0 = Padding()
    //     0xc17124: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc17128: mov             x1, x0
    // 0xc1712c: r0 = Instance_EdgeInsets
    //     0xc1712c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc17130: ldr             x0, [x0, #0x980]
    // 0xc17134: StoreField: r1->field_f = r0
    //     0xc17134: stur            w0, [x1, #0xf]
    // 0xc17138: ldur            x0, [fp, #-0x38]
    // 0xc1713c: StoreField: r1->field_b = r0
    //     0xc1713c: stur            w0, [x1, #0xb]
    // 0xc17140: mov             x3, x1
    // 0xc17144: b               #0xc17160
    // 0xc17148: r0 = Container()
    //     0xc17148: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1714c: mov             x1, x0
    // 0xc17150: stur            x0, [fp, #-0x10]
    // 0xc17154: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc17154: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc17158: r0 = Container()
    //     0xc17158: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc1715c: ldur            x3, [fp, #-0x10]
    // 0xc17160: ldur            x0, [fp, #-8]
    // 0xc17164: ldur            x2, [fp, #-0x18]
    // 0xc17168: ldur            x1, [fp, #-0x20]
    // 0xc1716c: stur            x3, [fp, #-0x10]
    // 0xc17170: r0 = Visibility()
    //     0xc17170: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc17174: mov             x2, x0
    // 0xc17178: ldur            x0, [fp, #-0x10]
    // 0xc1717c: stur            x2, [fp, #-0x30]
    // 0xc17180: StoreField: r2->field_b = r0
    //     0xc17180: stur            w0, [x2, #0xb]
    // 0xc17184: r0 = Instance_SizedBox
    //     0xc17184: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc17188: StoreField: r2->field_f = r0
    //     0xc17188: stur            w0, [x2, #0xf]
    // 0xc1718c: ldur            x1, [fp, #-0x20]
    // 0xc17190: StoreField: r2->field_13 = r1
    //     0xc17190: stur            w1, [x2, #0x13]
    // 0xc17194: r3 = false
    //     0xc17194: add             x3, NULL, #0x30  ; false
    // 0xc17198: ArrayStore: r2[0] = r3  ; List_4
    //     0xc17198: stur            w3, [x2, #0x17]
    // 0xc1719c: StoreField: r2->field_1b = r3
    //     0xc1719c: stur            w3, [x2, #0x1b]
    // 0xc171a0: StoreField: r2->field_1f = r3
    //     0xc171a0: stur            w3, [x2, #0x1f]
    // 0xc171a4: StoreField: r2->field_23 = r3
    //     0xc171a4: stur            w3, [x2, #0x23]
    // 0xc171a8: StoreField: r2->field_27 = r3
    //     0xc171a8: stur            w3, [x2, #0x27]
    // 0xc171ac: StoreField: r2->field_2b = r3
    //     0xc171ac: stur            w3, [x2, #0x2b]
    // 0xc171b0: ldur            x4, [fp, #-8]
    // 0xc171b4: LoadField: r1 = r4->field_b
    //     0xc171b4: ldur            w1, [x4, #0xb]
    // 0xc171b8: DecompressPointer r1
    //     0xc171b8: add             x1, x1, HEAP, lsl #32
    // 0xc171bc: cmp             w1, NULL
    // 0xc171c0: b.eq            #0xc17d18
    // 0xc171c4: LoadField: r5 = r1->field_3f
    //     0xc171c4: ldur            w5, [x1, #0x3f]
    // 0xc171c8: DecompressPointer r5
    //     0xc171c8: add             x5, x5, HEAP, lsl #32
    // 0xc171cc: LoadField: r1 = r5->field_b
    //     0xc171cc: ldur            w1, [x5, #0xb]
    // 0xc171d0: cbnz            w1, #0xc171dc
    // 0xc171d4: r5 = false
    //     0xc171d4: add             x5, NULL, #0x30  ; false
    // 0xc171d8: b               #0xc171e0
    // 0xc171dc: r5 = true
    //     0xc171dc: add             x5, NULL, #0x20  ; true
    // 0xc171e0: ldur            x6, [fp, #-0x18]
    // 0xc171e4: stur            x5, [fp, #-0x10]
    // 0xc171e8: LoadField: r1 = r6->field_13
    //     0xc171e8: ldur            w1, [x6, #0x13]
    // 0xc171ec: DecompressPointer r1
    //     0xc171ec: add             x1, x1, HEAP, lsl #32
    // 0xc171f0: r0 = of()
    //     0xc171f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc171f4: LoadField: r1 = r0->field_5b
    //     0xc171f4: ldur            w1, [x0, #0x5b]
    // 0xc171f8: DecompressPointer r1
    //     0xc171f8: add             x1, x1, HEAP, lsl #32
    // 0xc171fc: stur            x1, [fp, #-0x20]
    // 0xc17200: r0 = ColorFilter()
    //     0xc17200: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc17204: mov             x1, x0
    // 0xc17208: ldur            x0, [fp, #-0x20]
    // 0xc1720c: stur            x1, [fp, #-0x38]
    // 0xc17210: StoreField: r1->field_7 = r0
    //     0xc17210: stur            w0, [x1, #7]
    // 0xc17214: r0 = Instance_BlendMode
    //     0xc17214: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc17218: ldr             x0, [x0, #0xb30]
    // 0xc1721c: StoreField: r1->field_b = r0
    //     0xc1721c: stur            w0, [x1, #0xb]
    // 0xc17220: r0 = 1
    //     0xc17220: movz            x0, #0x1
    // 0xc17224: StoreField: r1->field_13 = r0
    //     0xc17224: stur            x0, [x1, #0x13]
    // 0xc17228: r0 = SvgPicture()
    //     0xc17228: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc1722c: stur            x0, [fp, #-0x20]
    // 0xc17230: ldur            x16, [fp, #-0x38]
    // 0xc17234: str             x16, [SP]
    // 0xc17238: mov             x1, x0
    // 0xc1723c: r2 = "assets/images/filter.svg"
    //     0xc1723c: add             x2, PP, #0x51, lsl #12  ; [pp+0x51c38] "assets/images/filter.svg"
    //     0xc17240: ldr             x2, [x2, #0xc38]
    // 0xc17244: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc17244: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc17248: ldr             x4, [x4, #0xa38]
    // 0xc1724c: r0 = SvgPicture.asset()
    //     0xc1724c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc17250: ldur            x0, [fp, #-8]
    // 0xc17254: LoadField: r1 = r0->field_b
    //     0xc17254: ldur            w1, [x0, #0xb]
    // 0xc17258: DecompressPointer r1
    //     0xc17258: add             x1, x1, HEAP, lsl #32
    // 0xc1725c: cmp             w1, NULL
    // 0xc17260: b.eq            #0xc17d1c
    // 0xc17264: LoadField: r2 = r1->field_f
    //     0xc17264: ldur            w2, [x1, #0xf]
    // 0xc17268: DecompressPointer r2
    //     0xc17268: add             x2, x2, HEAP, lsl #32
    // 0xc1726c: LoadField: r3 = r2->field_7
    //     0xc1726c: ldur            w3, [x2, #7]
    // 0xc17270: cbz             w3, #0xc1727c
    // 0xc17274: mov             x5, x2
    // 0xc17278: b               #0xc17280
    // 0xc1727c: r5 = Null
    //     0xc1727c: mov             x5, NULL
    // 0xc17280: stur            x5, [fp, #-0x40]
    // 0xc17284: LoadField: r2 = r1->field_b
    //     0xc17284: ldur            w2, [x1, #0xb]
    // 0xc17288: DecompressPointer r2
    //     0xc17288: add             x2, x2, HEAP, lsl #32
    // 0xc1728c: LoadField: r1 = r2->field_b
    //     0xc1728c: ldur            w1, [x2, #0xb]
    // 0xc17290: DecompressPointer r1
    //     0xc17290: add             x1, x1, HEAP, lsl #32
    // 0xc17294: cmp             w1, NULL
    // 0xc17298: b.ne            #0xc172a4
    // 0xc1729c: r3 = Null
    //     0xc1729c: mov             x3, NULL
    // 0xc172a0: b               #0xc172f0
    // 0xc172a4: LoadField: r3 = r1->field_b
    //     0xc172a4: ldur            w3, [x1, #0xb]
    // 0xc172a8: DecompressPointer r3
    //     0xc172a8: add             x3, x3, HEAP, lsl #32
    // 0xc172ac: ldur            x2, [fp, #-0x18]
    // 0xc172b0: stur            x3, [fp, #-0x38]
    // 0xc172b4: r1 = Function '<anonymous closure>':.
    //     0xc172b4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c40] AnonymousClosure: (0xaacbec), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc172b8: ldr             x1, [x1, #0xc40]
    // 0xc172bc: r0 = AllocateClosure()
    //     0xc172bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc172c0: r16 = <DropdownMenuItem<String>>
    //     0xc172c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f918] TypeArguments: <DropdownMenuItem<String>>
    //     0xc172c4: ldr             x16, [x16, #0x918]
    // 0xc172c8: ldur            lr, [fp, #-0x38]
    // 0xc172cc: stp             lr, x16, [SP, #8]
    // 0xc172d0: str             x0, [SP]
    // 0xc172d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc172d4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc172d8: r0 = map()
    //     0xc172d8: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xc172dc: mov             x1, x0
    // 0xc172e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc172e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc172e4: r0 = toList()
    //     0xc172e4: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xc172e8: mov             x3, x0
    // 0xc172ec: ldur            x0, [fp, #-8]
    // 0xc172f0: ldur            x2, [fp, #-0x18]
    // 0xc172f4: stur            x3, [fp, #-0x38]
    // 0xc172f8: r1 = Function '<anonymous closure>':.
    //     0xc172f8: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c48] AnonymousClosure: (0xc17fb8), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc172fc: ldr             x1, [x1, #0xc48]
    // 0xc17300: r0 = AllocateClosure()
    //     0xc17300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc17304: r1 = <String>
    //     0xc17304: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xc17308: stur            x0, [fp, #-0x48]
    // 0xc1730c: r0 = DropdownButton()
    //     0xc1730c: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xc17310: stur            x0, [fp, #-0x50]
    // 0xc17314: r16 = 0.000000
    //     0xc17314: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc17318: str             x16, [SP]
    // 0xc1731c: mov             x1, x0
    // 0xc17320: ldur            x2, [fp, #-0x38]
    // 0xc17324: ldur            x3, [fp, #-0x48]
    // 0xc17328: ldur            x5, [fp, #-0x40]
    // 0xc1732c: r4 = const [0, 0x5, 0x1, 0x4, iconSize, 0x4, null]
    //     0xc1732c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c0d8] List(7) [0, 0x5, 0x1, 0x4, "iconSize", 0x4, Null]
    //     0xc17330: ldr             x4, [x4, #0xd8]
    // 0xc17334: r0 = DropdownButton()
    //     0xc17334: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xc17338: r0 = DropdownButtonHideUnderline()
    //     0xc17338: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xc1733c: mov             x1, x0
    // 0xc17340: ldur            x0, [fp, #-0x50]
    // 0xc17344: stur            x1, [fp, #-0x38]
    // 0xc17348: StoreField: r1->field_b = r0
    //     0xc17348: stur            w0, [x1, #0xb]
    // 0xc1734c: r0 = Padding()
    //     0xc1734c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc17350: mov             x2, x0
    // 0xc17354: r0 = Instance_EdgeInsets
    //     0xc17354: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xc17358: ldr             x0, [x0, #0xc40]
    // 0xc1735c: stur            x2, [fp, #-0x40]
    // 0xc17360: StoreField: r2->field_f = r0
    //     0xc17360: stur            w0, [x2, #0xf]
    // 0xc17364: ldur            x0, [fp, #-0x38]
    // 0xc17368: StoreField: r2->field_b = r0
    //     0xc17368: stur            w0, [x2, #0xb]
    // 0xc1736c: r1 = <FlexParentData>
    //     0xc1736c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc17370: ldr             x1, [x1, #0xe00]
    // 0xc17374: r0 = Expanded()
    //     0xc17374: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc17378: stur            x0, [fp, #-0x48]
    // 0xc1737c: StoreField: r0->field_13 = rZR
    //     0xc1737c: stur            xzr, [x0, #0x13]
    // 0xc17380: r1 = Instance_FlexFit
    //     0xc17380: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc17384: ldr             x1, [x1, #0xe08]
    // 0xc17388: StoreField: r0->field_1b = r1
    //     0xc17388: stur            w1, [x0, #0x1b]
    // 0xc1738c: ldur            x1, [fp, #-0x40]
    // 0xc17390: StoreField: r0->field_b = r1
    //     0xc17390: stur            w1, [x0, #0xb]
    // 0xc17394: ldur            x3, [fp, #-8]
    // 0xc17398: LoadField: r1 = r3->field_b
    //     0xc17398: ldur            w1, [x3, #0xb]
    // 0xc1739c: DecompressPointer r1
    //     0xc1739c: add             x1, x1, HEAP, lsl #32
    // 0xc173a0: cmp             w1, NULL
    // 0xc173a4: b.eq            #0xc17d20
    // 0xc173a8: LoadField: r2 = r1->field_3f
    //     0xc173a8: ldur            w2, [x1, #0x3f]
    // 0xc173ac: DecompressPointer r2
    //     0xc173ac: add             x2, x2, HEAP, lsl #32
    // 0xc173b0: LoadField: r4 = r2->field_b
    //     0xc173b0: ldur            w4, [x2, #0xb]
    // 0xc173b4: cbnz            w4, #0xc173c0
    // 0xc173b8: r5 = false
    //     0xc173b8: add             x5, NULL, #0x30  ; false
    // 0xc173bc: b               #0xc173c4
    // 0xc173c0: r5 = true
    //     0xc173c0: add             x5, NULL, #0x20  ; true
    // 0xc173c4: stur            x5, [fp, #-0x40]
    // 0xc173c8: LoadField: r2 = r1->field_b
    //     0xc173c8: ldur            w2, [x1, #0xb]
    // 0xc173cc: DecompressPointer r2
    //     0xc173cc: add             x2, x2, HEAP, lsl #32
    // 0xc173d0: LoadField: r1 = r2->field_f
    //     0xc173d0: ldur            w1, [x2, #0xf]
    // 0xc173d4: DecompressPointer r1
    //     0xc173d4: add             x1, x1, HEAP, lsl #32
    // 0xc173d8: cmp             w1, NULL
    // 0xc173dc: b.ne            #0xc173e8
    // 0xc173e0: r10 = Null
    //     0xc173e0: mov             x10, NULL
    // 0xc173e4: b               #0xc173f4
    // 0xc173e8: LoadField: r2 = r1->field_13
    //     0xc173e8: ldur            w2, [x1, #0x13]
    // 0xc173ec: DecompressPointer r2
    //     0xc173ec: add             x2, x2, HEAP, lsl #32
    // 0xc173f0: mov             x10, x2
    // 0xc173f4: ldur            x8, [fp, #-0x18]
    // 0xc173f8: ldur            x9, [fp, #-0x28]
    // 0xc173fc: ldur            x6, [fp, #-0x30]
    // 0xc17400: ldur            x4, [fp, #-0x20]
    // 0xc17404: ldur            x7, [fp, #-0x10]
    // 0xc17408: stur            x10, [fp, #-0x38]
    // 0xc1740c: r1 = Null
    //     0xc1740c: mov             x1, NULL
    // 0xc17410: r2 = 4
    //     0xc17410: movz            x2, #0x4
    // 0xc17414: r0 = AllocateArray()
    //     0xc17414: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc17418: mov             x1, x0
    // 0xc1741c: ldur            x0, [fp, #-0x38]
    // 0xc17420: StoreField: r1->field_f = r0
    //     0xc17420: stur            w0, [x1, #0xf]
    // 0xc17424: r16 = " items"
    //     0xc17424: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c50] " items"
    //     0xc17428: ldr             x16, [x16, #0xc50]
    // 0xc1742c: StoreField: r1->field_13 = r16
    //     0xc1742c: stur            w16, [x1, #0x13]
    // 0xc17430: str             x1, [SP]
    // 0xc17434: r0 = _interpolate()
    //     0xc17434: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc17438: ldur            x2, [fp, #-0x18]
    // 0xc1743c: stur            x0, [fp, #-0x38]
    // 0xc17440: LoadField: r1 = r2->field_13
    //     0xc17440: ldur            w1, [x2, #0x13]
    // 0xc17444: DecompressPointer r1
    //     0xc17444: add             x1, x1, HEAP, lsl #32
    // 0xc17448: r0 = of()
    //     0xc17448: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc1744c: LoadField: r1 = r0->field_87
    //     0xc1744c: ldur            w1, [x0, #0x87]
    // 0xc17450: DecompressPointer r1
    //     0xc17450: add             x1, x1, HEAP, lsl #32
    // 0xc17454: LoadField: r0 = r1->field_2b
    //     0xc17454: ldur            w0, [x1, #0x2b]
    // 0xc17458: DecompressPointer r0
    //     0xc17458: add             x0, x0, HEAP, lsl #32
    // 0xc1745c: r16 = 14.000000
    //     0xc1745c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc17460: ldr             x16, [x16, #0x1d8]
    // 0xc17464: r30 = Instance_Color
    //     0xc17464: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc17468: stp             lr, x16, [SP]
    // 0xc1746c: mov             x1, x0
    // 0xc17470: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc17470: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc17474: ldr             x4, [x4, #0xaa0]
    // 0xc17478: r0 = copyWith()
    //     0xc17478: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc1747c: stur            x0, [fp, #-0x50]
    // 0xc17480: r0 = Text()
    //     0xc17480: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc17484: mov             x1, x0
    // 0xc17488: ldur            x0, [fp, #-0x38]
    // 0xc1748c: stur            x1, [fp, #-0x58]
    // 0xc17490: StoreField: r1->field_b = r0
    //     0xc17490: stur            w0, [x1, #0xb]
    // 0xc17494: ldur            x0, [fp, #-0x50]
    // 0xc17498: StoreField: r1->field_13 = r0
    //     0xc17498: stur            w0, [x1, #0x13]
    // 0xc1749c: r0 = Visibility()
    //     0xc1749c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc174a0: mov             x1, x0
    // 0xc174a4: ldur            x0, [fp, #-0x58]
    // 0xc174a8: stur            x1, [fp, #-0x38]
    // 0xc174ac: StoreField: r1->field_b = r0
    //     0xc174ac: stur            w0, [x1, #0xb]
    // 0xc174b0: r0 = Instance_SizedBox
    //     0xc174b0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc174b4: StoreField: r1->field_f = r0
    //     0xc174b4: stur            w0, [x1, #0xf]
    // 0xc174b8: ldur            x2, [fp, #-0x40]
    // 0xc174bc: StoreField: r1->field_13 = r2
    //     0xc174bc: stur            w2, [x1, #0x13]
    // 0xc174c0: r2 = false
    //     0xc174c0: add             x2, NULL, #0x30  ; false
    // 0xc174c4: ArrayStore: r1[0] = r2  ; List_4
    //     0xc174c4: stur            w2, [x1, #0x17]
    // 0xc174c8: StoreField: r1->field_1b = r2
    //     0xc174c8: stur            w2, [x1, #0x1b]
    // 0xc174cc: StoreField: r1->field_1f = r2
    //     0xc174cc: stur            w2, [x1, #0x1f]
    // 0xc174d0: StoreField: r1->field_23 = r2
    //     0xc174d0: stur            w2, [x1, #0x23]
    // 0xc174d4: StoreField: r1->field_27 = r2
    //     0xc174d4: stur            w2, [x1, #0x27]
    // 0xc174d8: StoreField: r1->field_2b = r2
    //     0xc174d8: stur            w2, [x1, #0x2b]
    // 0xc174dc: r0 = Padding()
    //     0xc174dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc174e0: mov             x3, x0
    // 0xc174e4: r0 = Instance_EdgeInsets
    //     0xc174e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xc174e8: ldr             x0, [x0, #0xd48]
    // 0xc174ec: stur            x3, [fp, #-0x40]
    // 0xc174f0: StoreField: r3->field_f = r0
    //     0xc174f0: stur            w0, [x3, #0xf]
    // 0xc174f4: ldur            x0, [fp, #-0x38]
    // 0xc174f8: StoreField: r3->field_b = r0
    //     0xc174f8: stur            w0, [x3, #0xb]
    // 0xc174fc: r1 = Null
    //     0xc174fc: mov             x1, NULL
    // 0xc17500: r2 = 8
    //     0xc17500: movz            x2, #0x8
    // 0xc17504: r0 = AllocateArray()
    //     0xc17504: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc17508: mov             x2, x0
    // 0xc1750c: ldur            x0, [fp, #-0x20]
    // 0xc17510: stur            x2, [fp, #-0x38]
    // 0xc17514: StoreField: r2->field_f = r0
    //     0xc17514: stur            w0, [x2, #0xf]
    // 0xc17518: ldur            x0, [fp, #-0x48]
    // 0xc1751c: StoreField: r2->field_13 = r0
    //     0xc1751c: stur            w0, [x2, #0x13]
    // 0xc17520: r16 = Instance_Spacer
    //     0xc17520: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xc17524: ldr             x16, [x16, #0xf0]
    // 0xc17528: ArrayStore: r2[0] = r16  ; List_4
    //     0xc17528: stur            w16, [x2, #0x17]
    // 0xc1752c: ldur            x0, [fp, #-0x40]
    // 0xc17530: StoreField: r2->field_1b = r0
    //     0xc17530: stur            w0, [x2, #0x1b]
    // 0xc17534: r1 = <Widget>
    //     0xc17534: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc17538: r0 = AllocateGrowableArray()
    //     0xc17538: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc1753c: mov             x1, x0
    // 0xc17540: ldur            x0, [fp, #-0x38]
    // 0xc17544: stur            x1, [fp, #-0x20]
    // 0xc17548: StoreField: r1->field_f = r0
    //     0xc17548: stur            w0, [x1, #0xf]
    // 0xc1754c: r0 = 8
    //     0xc1754c: movz            x0, #0x8
    // 0xc17550: StoreField: r1->field_b = r0
    //     0xc17550: stur            w0, [x1, #0xb]
    // 0xc17554: r0 = Row()
    //     0xc17554: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc17558: mov             x1, x0
    // 0xc1755c: r0 = Instance_Axis
    //     0xc1755c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc17560: stur            x1, [fp, #-0x38]
    // 0xc17564: StoreField: r1->field_f = r0
    //     0xc17564: stur            w0, [x1, #0xf]
    // 0xc17568: r0 = Instance_MainAxisAlignment
    //     0xc17568: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc1756c: ldr             x0, [x0, #0xa08]
    // 0xc17570: StoreField: r1->field_13 = r0
    //     0xc17570: stur            w0, [x1, #0x13]
    // 0xc17574: r2 = Instance_MainAxisSize
    //     0xc17574: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc17578: ldr             x2, [x2, #0xa10]
    // 0xc1757c: ArrayStore: r1[0] = r2  ; List_4
    //     0xc1757c: stur            w2, [x1, #0x17]
    // 0xc17580: r3 = Instance_CrossAxisAlignment
    //     0xc17580: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc17584: ldr             x3, [x3, #0xa18]
    // 0xc17588: StoreField: r1->field_1b = r3
    //     0xc17588: stur            w3, [x1, #0x1b]
    // 0xc1758c: r4 = Instance_VerticalDirection
    //     0xc1758c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc17590: ldr             x4, [x4, #0xa20]
    // 0xc17594: StoreField: r1->field_23 = r4
    //     0xc17594: stur            w4, [x1, #0x23]
    // 0xc17598: r5 = Instance_Clip
    //     0xc17598: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc1759c: ldr             x5, [x5, #0x38]
    // 0xc175a0: StoreField: r1->field_2b = r5
    //     0xc175a0: stur            w5, [x1, #0x2b]
    // 0xc175a4: StoreField: r1->field_2f = rZR
    //     0xc175a4: stur            xzr, [x1, #0x2f]
    // 0xc175a8: ldur            x6, [fp, #-0x20]
    // 0xc175ac: StoreField: r1->field_b = r6
    //     0xc175ac: stur            w6, [x1, #0xb]
    // 0xc175b0: r0 = Padding()
    //     0xc175b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc175b4: mov             x1, x0
    // 0xc175b8: r0 = Instance_EdgeInsets
    //     0xc175b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc175bc: ldr             x0, [x0, #0x668]
    // 0xc175c0: stur            x1, [fp, #-0x20]
    // 0xc175c4: StoreField: r1->field_f = r0
    //     0xc175c4: stur            w0, [x1, #0xf]
    // 0xc175c8: ldur            x0, [fp, #-0x38]
    // 0xc175cc: StoreField: r1->field_b = r0
    //     0xc175cc: stur            w0, [x1, #0xb]
    // 0xc175d0: r0 = Visibility()
    //     0xc175d0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc175d4: mov             x2, x0
    // 0xc175d8: ldur            x0, [fp, #-0x20]
    // 0xc175dc: stur            x2, [fp, #-0x38]
    // 0xc175e0: StoreField: r2->field_b = r0
    //     0xc175e0: stur            w0, [x2, #0xb]
    // 0xc175e4: r0 = Instance_SizedBox
    //     0xc175e4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc175e8: StoreField: r2->field_f = r0
    //     0xc175e8: stur            w0, [x2, #0xf]
    // 0xc175ec: ldur            x1, [fp, #-0x10]
    // 0xc175f0: StoreField: r2->field_13 = r1
    //     0xc175f0: stur            w1, [x2, #0x13]
    // 0xc175f4: r3 = false
    //     0xc175f4: add             x3, NULL, #0x30  ; false
    // 0xc175f8: ArrayStore: r2[0] = r3  ; List_4
    //     0xc175f8: stur            w3, [x2, #0x17]
    // 0xc175fc: StoreField: r2->field_1b = r3
    //     0xc175fc: stur            w3, [x2, #0x1b]
    // 0xc17600: StoreField: r2->field_1f = r3
    //     0xc17600: stur            w3, [x2, #0x1f]
    // 0xc17604: StoreField: r2->field_23 = r3
    //     0xc17604: stur            w3, [x2, #0x23]
    // 0xc17608: StoreField: r2->field_27 = r3
    //     0xc17608: stur            w3, [x2, #0x27]
    // 0xc1760c: StoreField: r2->field_2b = r3
    //     0xc1760c: stur            w3, [x2, #0x2b]
    // 0xc17610: ldur            x4, [fp, #-8]
    // 0xc17614: LoadField: r1 = r4->field_b
    //     0xc17614: ldur            w1, [x4, #0xb]
    // 0xc17618: DecompressPointer r1
    //     0xc17618: add             x1, x1, HEAP, lsl #32
    // 0xc1761c: cmp             w1, NULL
    // 0xc17620: b.eq            #0xc17d24
    // 0xc17624: LoadField: r5 = r1->field_3f
    //     0xc17624: ldur            w5, [x1, #0x3f]
    // 0xc17628: DecompressPointer r5
    //     0xc17628: add             x5, x5, HEAP, lsl #32
    // 0xc1762c: LoadField: r1 = r5->field_b
    //     0xc1762c: ldur            w1, [x5, #0xb]
    // 0xc17630: cbnz            w1, #0xc1763c
    // 0xc17634: r5 = false
    //     0xc17634: add             x5, NULL, #0x30  ; false
    // 0xc17638: b               #0xc17640
    // 0xc1763c: r5 = true
    //     0xc1763c: add             x5, NULL, #0x20  ; true
    // 0xc17640: stur            x5, [fp, #-0x10]
    // 0xc17644: r1 = Instance_Color
    //     0xc17644: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc17648: d0 = 0.100000
    //     0xc17648: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc1764c: r0 = withOpacity()
    //     0xc1764c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc17650: stur            x0, [fp, #-0x20]
    // 0xc17654: r0 = Divider()
    //     0xc17654: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xc17658: mov             x1, x0
    // 0xc1765c: r0 = 1.000000
    //     0xc1765c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc17660: stur            x1, [fp, #-0x40]
    // 0xc17664: StoreField: r1->field_f = r0
    //     0xc17664: stur            w0, [x1, #0xf]
    // 0xc17668: ldur            x0, [fp, #-0x20]
    // 0xc1766c: StoreField: r1->field_1f = r0
    //     0xc1766c: stur            w0, [x1, #0x1f]
    // 0xc17670: r0 = Visibility()
    //     0xc17670: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc17674: mov             x1, x0
    // 0xc17678: ldur            x0, [fp, #-0x40]
    // 0xc1767c: stur            x1, [fp, #-0x48]
    // 0xc17680: StoreField: r1->field_b = r0
    //     0xc17680: stur            w0, [x1, #0xb]
    // 0xc17684: r0 = Instance_SizedBox
    //     0xc17684: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc17688: StoreField: r1->field_f = r0
    //     0xc17688: stur            w0, [x1, #0xf]
    // 0xc1768c: ldur            x2, [fp, #-0x10]
    // 0xc17690: StoreField: r1->field_13 = r2
    //     0xc17690: stur            w2, [x1, #0x13]
    // 0xc17694: r2 = false
    //     0xc17694: add             x2, NULL, #0x30  ; false
    // 0xc17698: ArrayStore: r1[0] = r2  ; List_4
    //     0xc17698: stur            w2, [x1, #0x17]
    // 0xc1769c: StoreField: r1->field_1b = r2
    //     0xc1769c: stur            w2, [x1, #0x1b]
    // 0xc176a0: StoreField: r1->field_1f = r2
    //     0xc176a0: stur            w2, [x1, #0x1f]
    // 0xc176a4: StoreField: r1->field_23 = r2
    //     0xc176a4: stur            w2, [x1, #0x23]
    // 0xc176a8: StoreField: r1->field_27 = r2
    //     0xc176a8: stur            w2, [x1, #0x27]
    // 0xc176ac: StoreField: r1->field_2b = r2
    //     0xc176ac: stur            w2, [x1, #0x2b]
    // 0xc176b0: ldur            x3, [fp, #-8]
    // 0xc176b4: LoadField: r4 = r3->field_b
    //     0xc176b4: ldur            w4, [x3, #0xb]
    // 0xc176b8: DecompressPointer r4
    //     0xc176b8: add             x4, x4, HEAP, lsl #32
    // 0xc176bc: cmp             w4, NULL
    // 0xc176c0: b.eq            #0xc17d28
    // 0xc176c4: LoadField: r5 = r4->field_3f
    //     0xc176c4: ldur            w5, [x4, #0x3f]
    // 0xc176c8: DecompressPointer r5
    //     0xc176c8: add             x5, x5, HEAP, lsl #32
    // 0xc176cc: stur            x5, [fp, #-0x40]
    // 0xc176d0: LoadField: r6 = r5->field_b
    //     0xc176d0: ldur            w6, [x5, #0xb]
    // 0xc176d4: cbnz            w6, #0xc176e0
    // 0xc176d8: r7 = false
    //     0xc176d8: add             x7, NULL, #0x30  ; false
    // 0xc176dc: b               #0xc176e4
    // 0xc176e0: r7 = true
    //     0xc176e0: add             x7, NULL, #0x20  ; true
    // 0xc176e4: stur            x7, [fp, #-0x20]
    // 0xc176e8: LoadField: r6 = r4->field_13
    //     0xc176e8: ldur            w6, [x4, #0x13]
    // 0xc176ec: DecompressPointer r6
    //     0xc176ec: add             x6, x6, HEAP, lsl #32
    // 0xc176f0: stur            x6, [fp, #-0x10]
    // 0xc176f4: r0 = CustomStyle()
    //     0xc176f4: bl              #0x910168  ; AllocateCustomStyleStub -> CustomStyle (size=0x10)
    // 0xc176f8: mov             x1, x0
    // 0xc176fc: r0 = Instance_TitleAlignment
    //     0xc176fc: add             x0, PP, #0x24, lsl #12  ; [pp+0x24510] Obj!TitleAlignment@d75601
    //     0xc17700: ldr             x0, [x0, #0x510]
    // 0xc17704: stur            x1, [fp, #-0x50]
    // 0xc17708: StoreField: r1->field_7 = r0
    //     0xc17708: stur            w0, [x1, #7]
    // 0xc1770c: r0 = ProductGridItemView()
    //     0xc1770c: bl              #0x9c2a58  ; AllocateProductGridItemViewStub -> ProductGridItemView (size=0x5c)
    // 0xc17710: mov             x1, x0
    // 0xc17714: ldur            x0, [fp, #-0x40]
    // 0xc17718: stur            x1, [fp, #-0x58]
    // 0xc1771c: StoreField: r1->field_b = r0
    //     0xc1771c: stur            w0, [x1, #0xb]
    // 0xc17720: r0 = ""
    //     0xc17720: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc17724: StoreField: r1->field_f = r0
    //     0xc17724: stur            w0, [x1, #0xf]
    // 0xc17728: r0 = false
    //     0xc17728: add             x0, NULL, #0x30  ; false
    // 0xc1772c: StoreField: r1->field_13 = r0
    //     0xc1772c: stur            w0, [x1, #0x13]
    // 0xc17730: r0 = ViewAll()
    //     0xc17730: bl              #0x90ff98  ; AllocateViewAllStub -> ViewAll (size=0x10)
    // 0xc17734: mov             x1, x0
    // 0xc17738: ldur            x0, [fp, #-0x58]
    // 0xc1773c: ArrayStore: r0[0] = r1  ; List_4
    //     0xc1773c: stur            w1, [x0, #0x17]
    // 0xc17740: r3 = false
    //     0xc17740: add             x3, NULL, #0x30  ; false
    // 0xc17744: StoreField: r0->field_1b = r3
    //     0xc17744: stur            w3, [x0, #0x1b]
    // 0xc17748: ldur            x1, [fp, #-0x10]
    // 0xc1774c: StoreField: r0->field_1f = r1
    //     0xc1774c: stur            w1, [x0, #0x1f]
    // 0xc17750: ldur            x1, [fp, #-0x50]
    // 0xc17754: StoreField: r0->field_23 = r1
    //     0xc17754: stur            w1, [x0, #0x23]
    // 0xc17758: r4 = "search_page"
    //     0xc17758: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xc1775c: ldr             x4, [x4, #0xe58]
    // 0xc17760: StoreField: r0->field_37 = r4
    //     0xc17760: stur            w4, [x0, #0x37]
    // 0xc17764: StoreField: r0->field_3b = r4
    //     0xc17764: stur            w4, [x0, #0x3b]
    // 0xc17768: ldur            x2, [fp, #-0x18]
    // 0xc1776c: r1 = Function '<anonymous closure>':.
    //     0xc1776c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c58] AnonymousClosure: (0xc17f2c), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc17770: ldr             x1, [x1, #0xc58]
    // 0xc17774: r0 = AllocateClosure()
    //     0xc17774: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc17778: mov             x1, x0
    // 0xc1777c: ldur            x0, [fp, #-0x58]
    // 0xc17780: StoreField: r0->field_47 = r1
    //     0xc17780: stur            w1, [x0, #0x47]
    // 0xc17784: ldur            x2, [fp, #-0x18]
    // 0xc17788: r1 = Function '<anonymous closure>':.
    //     0xc17788: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c60] AnonymousClosure: (0xc17e94), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc1778c: ldr             x1, [x1, #0xc60]
    // 0xc17790: r0 = AllocateClosure()
    //     0xc17790: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc17794: mov             x1, x0
    // 0xc17798: ldur            x0, [fp, #-0x58]
    // 0xc1779c: StoreField: r0->field_4b = r1
    //     0xc1779c: stur            w1, [x0, #0x4b]
    // 0xc177a0: ldur            x2, [fp, #-0x18]
    // 0xc177a4: r1 = Function '<anonymous closure>':.
    //     0xc177a4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c68] AnonymousClosure: (0xc17de8), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc177a8: ldr             x1, [x1, #0xc68]
    // 0xc177ac: r0 = AllocateClosure()
    //     0xc177ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc177b0: mov             x1, x0
    // 0xc177b4: ldur            x0, [fp, #-0x58]
    // 0xc177b8: StoreField: r0->field_4f = r1
    //     0xc177b8: stur            w1, [x0, #0x4f]
    // 0xc177bc: r1 = "search_page"
    //     0xc177bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xc177c0: ldr             x1, [x1, #0xe58]
    // 0xc177c4: StoreField: r0->field_3f = r1
    //     0xc177c4: stur            w1, [x0, #0x3f]
    // 0xc177c8: ldur            x2, [fp, #-0x18]
    // 0xc177cc: r1 = Function '<anonymous closure>':.
    //     0xc177cc: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c70] AnonymousClosure: (0xc17d30), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc177d0: ldr             x1, [x1, #0xc70]
    // 0xc177d4: r0 = AllocateClosure()
    //     0xc177d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc177d8: mov             x1, x0
    // 0xc177dc: ldur            x0, [fp, #-0x58]
    // 0xc177e0: StoreField: r0->field_53 = r1
    //     0xc177e0: stur            w1, [x0, #0x53]
    // 0xc177e4: r1 = Function '<anonymous closure>':.
    //     0xc177e4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c78] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc177e8: ldr             x1, [x1, #0xc78]
    // 0xc177ec: r2 = Null
    //     0xc177ec: mov             x2, NULL
    // 0xc177f0: r0 = AllocateClosure()
    //     0xc177f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc177f4: mov             x1, x0
    // 0xc177f8: ldur            x0, [fp, #-0x58]
    // 0xc177fc: StoreField: r0->field_57 = r1
    //     0xc177fc: stur            w1, [x0, #0x57]
    // 0xc17800: r0 = Visibility()
    //     0xc17800: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc17804: mov             x3, x0
    // 0xc17808: ldur            x0, [fp, #-0x58]
    // 0xc1780c: stur            x3, [fp, #-0x10]
    // 0xc17810: StoreField: r3->field_b = r0
    //     0xc17810: stur            w0, [x3, #0xb]
    // 0xc17814: r0 = Instance_SizedBox
    //     0xc17814: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc17818: StoreField: r3->field_f = r0
    //     0xc17818: stur            w0, [x3, #0xf]
    // 0xc1781c: ldur            x0, [fp, #-0x20]
    // 0xc17820: StoreField: r3->field_13 = r0
    //     0xc17820: stur            w0, [x3, #0x13]
    // 0xc17824: r0 = false
    //     0xc17824: add             x0, NULL, #0x30  ; false
    // 0xc17828: ArrayStore: r3[0] = r0  ; List_4
    //     0xc17828: stur            w0, [x3, #0x17]
    // 0xc1782c: StoreField: r3->field_1b = r0
    //     0xc1782c: stur            w0, [x3, #0x1b]
    // 0xc17830: StoreField: r3->field_1f = r0
    //     0xc17830: stur            w0, [x3, #0x1f]
    // 0xc17834: StoreField: r3->field_23 = r0
    //     0xc17834: stur            w0, [x3, #0x23]
    // 0xc17838: StoreField: r3->field_27 = r0
    //     0xc17838: stur            w0, [x3, #0x27]
    // 0xc1783c: StoreField: r3->field_2b = r0
    //     0xc1783c: stur            w0, [x3, #0x2b]
    // 0xc17840: r1 = Null
    //     0xc17840: mov             x1, NULL
    // 0xc17844: r2 = 12
    //     0xc17844: movz            x2, #0xc
    // 0xc17848: r0 = AllocateArray()
    //     0xc17848: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc1784c: mov             x2, x0
    // 0xc17850: ldur            x0, [fp, #-0x28]
    // 0xc17854: stur            x2, [fp, #-0x20]
    // 0xc17858: StoreField: r2->field_f = r0
    //     0xc17858: stur            w0, [x2, #0xf]
    // 0xc1785c: r16 = Instance_Divider
    //     0xc1785c: add             x16, PP, #0x48, lsl #12  ; [pp+0x484d0] Obj!Divider@d66ca1
    //     0xc17860: ldr             x16, [x16, #0x4d0]
    // 0xc17864: StoreField: r2->field_13 = r16
    //     0xc17864: stur            w16, [x2, #0x13]
    // 0xc17868: ldur            x0, [fp, #-0x30]
    // 0xc1786c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc1786c: stur            w0, [x2, #0x17]
    // 0xc17870: ldur            x0, [fp, #-0x38]
    // 0xc17874: StoreField: r2->field_1b = r0
    //     0xc17874: stur            w0, [x2, #0x1b]
    // 0xc17878: ldur            x0, [fp, #-0x48]
    // 0xc1787c: StoreField: r2->field_1f = r0
    //     0xc1787c: stur            w0, [x2, #0x1f]
    // 0xc17880: ldur            x0, [fp, #-0x10]
    // 0xc17884: StoreField: r2->field_23 = r0
    //     0xc17884: stur            w0, [x2, #0x23]
    // 0xc17888: r1 = <Widget>
    //     0xc17888: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc1788c: r0 = AllocateGrowableArray()
    //     0xc1788c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc17890: mov             x1, x0
    // 0xc17894: ldur            x0, [fp, #-0x20]
    // 0xc17898: stur            x1, [fp, #-0x10]
    // 0xc1789c: StoreField: r1->field_f = r0
    //     0xc1789c: stur            w0, [x1, #0xf]
    // 0xc178a0: r0 = 12
    //     0xc178a0: movz            x0, #0xc
    // 0xc178a4: StoreField: r1->field_b = r0
    //     0xc178a4: stur            w0, [x1, #0xb]
    // 0xc178a8: r0 = Column()
    //     0xc178a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc178ac: mov             x3, x0
    // 0xc178b0: r0 = Instance_Axis
    //     0xc178b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc178b4: stur            x3, [fp, #-0x20]
    // 0xc178b8: StoreField: r3->field_f = r0
    //     0xc178b8: stur            w0, [x3, #0xf]
    // 0xc178bc: r4 = Instance_MainAxisAlignment
    //     0xc178bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc178c0: ldr             x4, [x4, #0xa08]
    // 0xc178c4: StoreField: r3->field_13 = r4
    //     0xc178c4: stur            w4, [x3, #0x13]
    // 0xc178c8: r5 = Instance_MainAxisSize
    //     0xc178c8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc178cc: ldr             x5, [x5, #0xa10]
    // 0xc178d0: ArrayStore: r3[0] = r5  ; List_4
    //     0xc178d0: stur            w5, [x3, #0x17]
    // 0xc178d4: r1 = Instance_CrossAxisAlignment
    //     0xc178d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc178d8: ldr             x1, [x1, #0xa18]
    // 0xc178dc: StoreField: r3->field_1b = r1
    //     0xc178dc: stur            w1, [x3, #0x1b]
    // 0xc178e0: r6 = Instance_VerticalDirection
    //     0xc178e0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc178e4: ldr             x6, [x6, #0xa20]
    // 0xc178e8: StoreField: r3->field_23 = r6
    //     0xc178e8: stur            w6, [x3, #0x23]
    // 0xc178ec: r7 = Instance_Clip
    //     0xc178ec: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc178f0: ldr             x7, [x7, #0x38]
    // 0xc178f4: StoreField: r3->field_2b = r7
    //     0xc178f4: stur            w7, [x3, #0x2b]
    // 0xc178f8: StoreField: r3->field_2f = rZR
    //     0xc178f8: stur            xzr, [x3, #0x2f]
    // 0xc178fc: ldur            x1, [fp, #-0x10]
    // 0xc17900: StoreField: r3->field_b = r1
    //     0xc17900: stur            w1, [x3, #0xb]
    // 0xc17904: r1 = Null
    //     0xc17904: mov             x1, NULL
    // 0xc17908: r2 = 2
    //     0xc17908: movz            x2, #0x2
    // 0xc1790c: r0 = AllocateArray()
    //     0xc1790c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc17910: mov             x2, x0
    // 0xc17914: ldur            x0, [fp, #-0x20]
    // 0xc17918: stur            x2, [fp, #-0x10]
    // 0xc1791c: StoreField: r2->field_f = r0
    //     0xc1791c: stur            w0, [x2, #0xf]
    // 0xc17920: r1 = <Widget>
    //     0xc17920: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc17924: r0 = AllocateGrowableArray()
    //     0xc17924: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc17928: mov             x2, x0
    // 0xc1792c: ldur            x0, [fp, #-0x10]
    // 0xc17930: stur            x2, [fp, #-0x20]
    // 0xc17934: StoreField: r2->field_f = r0
    //     0xc17934: stur            w0, [x2, #0xf]
    // 0xc17938: r0 = 2
    //     0xc17938: movz            x0, #0x2
    // 0xc1793c: StoreField: r2->field_b = r0
    //     0xc1793c: stur            w0, [x2, #0xb]
    // 0xc17940: ldur            x0, [fp, #-8]
    // 0xc17944: LoadField: r1 = r0->field_1f
    //     0xc17944: ldur            w1, [x0, #0x1f]
    // 0xc17948: DecompressPointer r1
    //     0xc17948: add             x1, x1, HEAP, lsl #32
    // 0xc1794c: tbnz            w1, #4, #0xc17c8c
    // 0xc17950: ldur            x3, [fp, #-0x18]
    // 0xc17954: LoadField: r1 = r3->field_13
    //     0xc17954: ldur            w1, [x3, #0x13]
    // 0xc17958: DecompressPointer r1
    //     0xc17958: add             x1, x1, HEAP, lsl #32
    // 0xc1795c: r0 = of()
    //     0xc1795c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc17960: LoadField: r1 = r0->field_87
    //     0xc17960: ldur            w1, [x0, #0x87]
    // 0xc17964: DecompressPointer r1
    //     0xc17964: add             x1, x1, HEAP, lsl #32
    // 0xc17968: LoadField: r0 = r1->field_2b
    //     0xc17968: ldur            w0, [x1, #0x2b]
    // 0xc1796c: DecompressPointer r0
    //     0xc1796c: add             x0, x0, HEAP, lsl #32
    // 0xc17970: r16 = Instance_Color
    //     0xc17970: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc17974: r30 = 12.000000
    //     0xc17974: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc17978: ldr             lr, [lr, #0x9e8]
    // 0xc1797c: stp             lr, x16, [SP]
    // 0xc17980: mov             x1, x0
    // 0xc17984: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc17984: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc17988: ldr             x4, [x4, #0x9b8]
    // 0xc1798c: r0 = copyWith()
    //     0xc1798c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc17990: stur            x0, [fp, #-0x10]
    // 0xc17994: r0 = Text()
    //     0xc17994: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc17998: mov             x1, x0
    // 0xc1799c: r0 = "Suggestions"
    //     0xc1799c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c80] "Suggestions"
    //     0xc179a0: ldr             x0, [x0, #0xc80]
    // 0xc179a4: stur            x1, [fp, #-0x28]
    // 0xc179a8: StoreField: r1->field_b = r0
    //     0xc179a8: stur            w0, [x1, #0xb]
    // 0xc179ac: ldur            x0, [fp, #-0x10]
    // 0xc179b0: StoreField: r1->field_13 = r0
    //     0xc179b0: stur            w0, [x1, #0x13]
    // 0xc179b4: r0 = Padding()
    //     0xc179b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc179b8: mov             x3, x0
    // 0xc179bc: r0 = Instance_EdgeInsets
    //     0xc179bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xc179c0: ldr             x0, [x0, #0xd0]
    // 0xc179c4: stur            x3, [fp, #-0x30]
    // 0xc179c8: StoreField: r3->field_f = r0
    //     0xc179c8: stur            w0, [x3, #0xf]
    // 0xc179cc: ldur            x0, [fp, #-0x28]
    // 0xc179d0: StoreField: r3->field_b = r0
    //     0xc179d0: stur            w0, [x3, #0xb]
    // 0xc179d4: ldur            x0, [fp, #-8]
    // 0xc179d8: LoadField: r1 = r0->field_b
    //     0xc179d8: ldur            w1, [x0, #0xb]
    // 0xc179dc: DecompressPointer r1
    //     0xc179dc: add             x1, x1, HEAP, lsl #32
    // 0xc179e0: cmp             w1, NULL
    // 0xc179e4: b.eq            #0xc17d2c
    // 0xc179e8: LoadField: r0 = r1->field_43
    //     0xc179e8: ldur            w0, [x1, #0x43]
    // 0xc179ec: DecompressPointer r0
    //     0xc179ec: add             x0, x0, HEAP, lsl #32
    // 0xc179f0: LoadField: r1 = r0->field_b
    //     0xc179f0: ldur            w1, [x0, #0xb]
    // 0xc179f4: DecompressPointer r1
    //     0xc179f4: add             x1, x1, HEAP, lsl #32
    // 0xc179f8: cmp             w1, NULL
    // 0xc179fc: b.ne            #0xc17a08
    // 0xc17a00: r0 = Null
    //     0xc17a00: mov             x0, NULL
    // 0xc17a04: b               #0xc17a0c
    // 0xc17a08: LoadField: r0 = r1->field_b
    //     0xc17a08: ldur            w0, [x1, #0xb]
    // 0xc17a0c: cmp             w0, NULL
    // 0xc17a10: b.ne            #0xc17a1c
    // 0xc17a14: r0 = 0
    //     0xc17a14: movz            x0, #0
    // 0xc17a18: b               #0xc17a24
    // 0xc17a1c: r2 = LoadInt32Instr(r0)
    //     0xc17a1c: sbfx            x2, x0, #1, #0x1f
    // 0xc17a20: mov             x0, x2
    // 0xc17a24: cmp             x0, #3
    // 0xc17a28: b.le            #0xc17a38
    // 0xc17a2c: r0 = 180.000000
    //     0xc17a2c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c88] 180
    //     0xc17a30: ldr             x0, [x0, #0xc88]
    // 0xc17a34: b               #0xc17a3c
    // 0xc17a38: r0 = Null
    //     0xc17a38: mov             x0, NULL
    // 0xc17a3c: stur            x0, [fp, #-0x10]
    // 0xc17a40: cmp             w1, NULL
    // 0xc17a44: b.ne            #0xc17a50
    // 0xc17a48: r1 = Null
    //     0xc17a48: mov             x1, NULL
    // 0xc17a4c: b               #0xc17a58
    // 0xc17a50: LoadField: r2 = r1->field_b
    //     0xc17a50: ldur            w2, [x1, #0xb]
    // 0xc17a54: mov             x1, x2
    // 0xc17a58: cmp             w1, NULL
    // 0xc17a5c: b.ne            #0xc17a68
    // 0xc17a60: r1 = 0
    //     0xc17a60: movz            x1, #0
    // 0xc17a64: b               #0xc17a70
    // 0xc17a68: r2 = LoadInt32Instr(r1)
    //     0xc17a68: sbfx            x2, x1, #1, #0x1f
    // 0xc17a6c: mov             x1, x2
    // 0xc17a70: ldur            x4, [fp, #-0x20]
    // 0xc17a74: lsl             x5, x1, #1
    // 0xc17a78: ldur            x2, [fp, #-0x18]
    // 0xc17a7c: stur            x5, [fp, #-8]
    // 0xc17a80: r1 = Function '<anonymous closure>':.
    //     0xc17a80: add             x1, PP, #0x51, lsl #12  ; [pp+0x51c90] AnonymousClosure: (0xaac244), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc17a84: ldr             x1, [x1, #0xc90]
    // 0xc17a88: r0 = AllocateClosure()
    //     0xc17a88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc17a8c: stur            x0, [fp, #-0x18]
    // 0xc17a90: r0 = ListView()
    //     0xc17a90: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc17a94: stur            x0, [fp, #-0x28]
    // 0xc17a98: r16 = Instance_EdgeInsets
    //     0xc17a98: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc17a9c: ldr             x16, [x16, #0x980]
    // 0xc17aa0: r30 = true
    //     0xc17aa0: add             lr, NULL, #0x20  ; true
    // 0xc17aa4: stp             lr, x16, [SP]
    // 0xc17aa8: mov             x1, x0
    // 0xc17aac: ldur            x2, [fp, #-0x18]
    // 0xc17ab0: ldur            x3, [fp, #-8]
    // 0xc17ab4: r4 = const [0, 0x5, 0x2, 0x3, padding, 0x3, shrinkWrap, 0x4, null]
    //     0xc17ab4: add             x4, PP, #0x51, lsl #12  ; [pp+0x51c98] List(9) [0, 0x5, 0x2, 0x3, "padding", 0x3, "shrinkWrap", 0x4, Null]
    //     0xc17ab8: ldr             x4, [x4, #0xc98]
    // 0xc17abc: r0 = ListView.builder()
    //     0xc17abc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xc17ac0: r0 = Scrollbar()
    //     0xc17ac0: bl              #0x997690  ; AllocateScrollbarStub -> Scrollbar (size=0x30)
    // 0xc17ac4: mov             x1, x0
    // 0xc17ac8: ldur            x0, [fp, #-0x28]
    // 0xc17acc: stur            x1, [fp, #-8]
    // 0xc17ad0: StoreField: r1->field_b = r0
    //     0xc17ad0: stur            w0, [x1, #0xb]
    // 0xc17ad4: r0 = SizedBox()
    //     0xc17ad4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc17ad8: mov             x3, x0
    // 0xc17adc: r0 = inf
    //     0xc17adc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc17ae0: ldr             x0, [x0, #0x9f8]
    // 0xc17ae4: stur            x3, [fp, #-0x18]
    // 0xc17ae8: StoreField: r3->field_f = r0
    //     0xc17ae8: stur            w0, [x3, #0xf]
    // 0xc17aec: ldur            x0, [fp, #-0x10]
    // 0xc17af0: StoreField: r3->field_13 = r0
    //     0xc17af0: stur            w0, [x3, #0x13]
    // 0xc17af4: ldur            x0, [fp, #-8]
    // 0xc17af8: StoreField: r3->field_b = r0
    //     0xc17af8: stur            w0, [x3, #0xb]
    // 0xc17afc: r1 = Null
    //     0xc17afc: mov             x1, NULL
    // 0xc17b00: r2 = 4
    //     0xc17b00: movz            x2, #0x4
    // 0xc17b04: r0 = AllocateArray()
    //     0xc17b04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc17b08: mov             x2, x0
    // 0xc17b0c: ldur            x0, [fp, #-0x30]
    // 0xc17b10: stur            x2, [fp, #-8]
    // 0xc17b14: StoreField: r2->field_f = r0
    //     0xc17b14: stur            w0, [x2, #0xf]
    // 0xc17b18: ldur            x0, [fp, #-0x18]
    // 0xc17b1c: StoreField: r2->field_13 = r0
    //     0xc17b1c: stur            w0, [x2, #0x13]
    // 0xc17b20: r1 = <Widget>
    //     0xc17b20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc17b24: r0 = AllocateGrowableArray()
    //     0xc17b24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc17b28: mov             x1, x0
    // 0xc17b2c: ldur            x0, [fp, #-8]
    // 0xc17b30: stur            x1, [fp, #-0x10]
    // 0xc17b34: StoreField: r1->field_f = r0
    //     0xc17b34: stur            w0, [x1, #0xf]
    // 0xc17b38: r0 = 4
    //     0xc17b38: movz            x0, #0x4
    // 0xc17b3c: StoreField: r1->field_b = r0
    //     0xc17b3c: stur            w0, [x1, #0xb]
    // 0xc17b40: r0 = Column()
    //     0xc17b40: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc17b44: mov             x1, x0
    // 0xc17b48: r0 = Instance_Axis
    //     0xc17b48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc17b4c: stur            x1, [fp, #-8]
    // 0xc17b50: StoreField: r1->field_f = r0
    //     0xc17b50: stur            w0, [x1, #0xf]
    // 0xc17b54: r2 = Instance_MainAxisAlignment
    //     0xc17b54: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc17b58: ldr             x2, [x2, #0xa08]
    // 0xc17b5c: StoreField: r1->field_13 = r2
    //     0xc17b5c: stur            w2, [x1, #0x13]
    // 0xc17b60: r2 = Instance_MainAxisSize
    //     0xc17b60: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc17b64: ldr             x2, [x2, #0xa10]
    // 0xc17b68: ArrayStore: r1[0] = r2  ; List_4
    //     0xc17b68: stur            w2, [x1, #0x17]
    // 0xc17b6c: r2 = Instance_CrossAxisAlignment
    //     0xc17b6c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc17b70: ldr             x2, [x2, #0x890]
    // 0xc17b74: StoreField: r1->field_1b = r2
    //     0xc17b74: stur            w2, [x1, #0x1b]
    // 0xc17b78: r2 = Instance_VerticalDirection
    //     0xc17b78: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc17b7c: ldr             x2, [x2, #0xa20]
    // 0xc17b80: StoreField: r1->field_23 = r2
    //     0xc17b80: stur            w2, [x1, #0x23]
    // 0xc17b84: r2 = Instance_Clip
    //     0xc17b84: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc17b88: ldr             x2, [x2, #0x38]
    // 0xc17b8c: StoreField: r1->field_2b = r2
    //     0xc17b8c: stur            w2, [x1, #0x2b]
    // 0xc17b90: StoreField: r1->field_2f = rZR
    //     0xc17b90: stur            xzr, [x1, #0x2f]
    // 0xc17b94: ldur            x3, [fp, #-0x10]
    // 0xc17b98: StoreField: r1->field_b = r3
    //     0xc17b98: stur            w3, [x1, #0xb]
    // 0xc17b9c: r0 = Material()
    //     0xc17b9c: bl              #0xaabb1c  ; AllocateMaterialStub -> Material (size=0x44)
    // 0xc17ba0: mov             x1, x0
    // 0xc17ba4: r0 = Instance_MaterialType
    //     0xc17ba4: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bd20] Obj!MaterialType@d741e1
    //     0xc17ba8: ldr             x0, [x0, #0xd20]
    // 0xc17bac: stur            x1, [fp, #-0x10]
    // 0xc17bb0: StoreField: r1->field_f = r0
    //     0xc17bb0: stur            w0, [x1, #0xf]
    // 0xc17bb4: d0 = 2.000000
    //     0xc17bb4: fmov            d0, #2.00000000
    // 0xc17bb8: ArrayStore: r1[0] = d0  ; List_8
    //     0xc17bb8: stur            d0, [x1, #0x17]
    // 0xc17bbc: r0 = Instance_Color
    //     0xc17bbc: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc17bc0: StoreField: r1->field_1f = r0
    //     0xc17bc0: stur            w0, [x1, #0x1f]
    // 0xc17bc4: r0 = true
    //     0xc17bc4: add             x0, NULL, #0x20  ; true
    // 0xc17bc8: StoreField: r1->field_33 = r0
    //     0xc17bc8: stur            w0, [x1, #0x33]
    // 0xc17bcc: r0 = Instance_Clip
    //     0xc17bcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc17bd0: ldr             x0, [x0, #0x38]
    // 0xc17bd4: StoreField: r1->field_37 = r0
    //     0xc17bd4: stur            w0, [x1, #0x37]
    // 0xc17bd8: r0 = Instance_Duration
    //     0xc17bd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e150] Obj!Duration@d77761
    //     0xc17bdc: ldr             x0, [x0, #0x150]
    // 0xc17be0: StoreField: r1->field_3b = r0
    //     0xc17be0: stur            w0, [x1, #0x3b]
    // 0xc17be4: ldur            x0, [fp, #-8]
    // 0xc17be8: StoreField: r1->field_b = r0
    //     0xc17be8: stur            w0, [x1, #0xb]
    // 0xc17bec: r0 = false
    //     0xc17bec: add             x0, NULL, #0x30  ; false
    // 0xc17bf0: StoreField: r1->field_13 = r0
    //     0xc17bf0: stur            w0, [x1, #0x13]
    // 0xc17bf4: r0 = Padding()
    //     0xc17bf4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc17bf8: mov             x2, x0
    // 0xc17bfc: r0 = Instance_EdgeInsets
    //     0xc17bfc: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ca0] Obj!EdgeInsets@d58b81
    //     0xc17c00: ldr             x0, [x0, #0xca0]
    // 0xc17c04: stur            x2, [fp, #-8]
    // 0xc17c08: StoreField: r2->field_f = r0
    //     0xc17c08: stur            w0, [x2, #0xf]
    // 0xc17c0c: ldur            x0, [fp, #-0x10]
    // 0xc17c10: StoreField: r2->field_b = r0
    //     0xc17c10: stur            w0, [x2, #0xb]
    // 0xc17c14: ldur            x0, [fp, #-0x20]
    // 0xc17c18: LoadField: r1 = r0->field_b
    //     0xc17c18: ldur            w1, [x0, #0xb]
    // 0xc17c1c: LoadField: r3 = r0->field_f
    //     0xc17c1c: ldur            w3, [x0, #0xf]
    // 0xc17c20: DecompressPointer r3
    //     0xc17c20: add             x3, x3, HEAP, lsl #32
    // 0xc17c24: LoadField: r4 = r3->field_b
    //     0xc17c24: ldur            w4, [x3, #0xb]
    // 0xc17c28: r3 = LoadInt32Instr(r1)
    //     0xc17c28: sbfx            x3, x1, #1, #0x1f
    // 0xc17c2c: stur            x3, [fp, #-0x60]
    // 0xc17c30: r1 = LoadInt32Instr(r4)
    //     0xc17c30: sbfx            x1, x4, #1, #0x1f
    // 0xc17c34: cmp             x3, x1
    // 0xc17c38: b.ne            #0xc17c44
    // 0xc17c3c: mov             x1, x0
    // 0xc17c40: r0 = _growToNextCapacity()
    //     0xc17c40: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc17c44: ldur            x2, [fp, #-0x20]
    // 0xc17c48: ldur            x3, [fp, #-0x60]
    // 0xc17c4c: add             x0, x3, #1
    // 0xc17c50: lsl             x1, x0, #1
    // 0xc17c54: StoreField: r2->field_b = r1
    //     0xc17c54: stur            w1, [x2, #0xb]
    // 0xc17c58: LoadField: r1 = r2->field_f
    //     0xc17c58: ldur            w1, [x2, #0xf]
    // 0xc17c5c: DecompressPointer r1
    //     0xc17c5c: add             x1, x1, HEAP, lsl #32
    // 0xc17c60: ldur            x0, [fp, #-8]
    // 0xc17c64: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc17c64: add             x25, x1, x3, lsl #2
    //     0xc17c68: add             x25, x25, #0xf
    //     0xc17c6c: str             w0, [x25]
    //     0xc17c70: tbz             w0, #0, #0xc17c8c
    //     0xc17c74: ldurb           w16, [x1, #-1]
    //     0xc17c78: ldurb           w17, [x0, #-1]
    //     0xc17c7c: and             x16, x17, x16, lsr #2
    //     0xc17c80: tst             x16, HEAP, lsr #32
    //     0xc17c84: b.eq            #0xc17c8c
    //     0xc17c88: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc17c8c: r0 = Stack()
    //     0xc17c8c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc17c90: mov             x1, x0
    // 0xc17c94: r0 = Instance_Alignment
    //     0xc17c94: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc17c98: ldr             x0, [x0, #0xfa0]
    // 0xc17c9c: stur            x1, [fp, #-8]
    // 0xc17ca0: StoreField: r1->field_f = r0
    //     0xc17ca0: stur            w0, [x1, #0xf]
    // 0xc17ca4: r0 = Instance_StackFit
    //     0xc17ca4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc17ca8: ldr             x0, [x0, #0xfa8]
    // 0xc17cac: ArrayStore: r1[0] = r0  ; List_4
    //     0xc17cac: stur            w0, [x1, #0x17]
    // 0xc17cb0: r0 = Instance_Clip
    //     0xc17cb0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc17cb4: ldr             x0, [x0, #0x7e0]
    // 0xc17cb8: StoreField: r1->field_1b = r0
    //     0xc17cb8: stur            w0, [x1, #0x1b]
    // 0xc17cbc: ldur            x2, [fp, #-0x20]
    // 0xc17cc0: StoreField: r1->field_b = r2
    //     0xc17cc0: stur            w2, [x1, #0xb]
    // 0xc17cc4: r0 = SingleChildScrollView()
    //     0xc17cc4: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xc17cc8: r1 = Instance_Axis
    //     0xc17cc8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc17ccc: StoreField: r0->field_b = r1
    //     0xc17ccc: stur            w1, [x0, #0xb]
    // 0xc17cd0: r1 = false
    //     0xc17cd0: add             x1, NULL, #0x30  ; false
    // 0xc17cd4: StoreField: r0->field_f = r1
    //     0xc17cd4: stur            w1, [x0, #0xf]
    // 0xc17cd8: ldur            x1, [fp, #-8]
    // 0xc17cdc: StoreField: r0->field_23 = r1
    //     0xc17cdc: stur            w1, [x0, #0x23]
    // 0xc17ce0: r1 = Instance_DragStartBehavior
    //     0xc17ce0: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xc17ce4: StoreField: r0->field_27 = r1
    //     0xc17ce4: stur            w1, [x0, #0x27]
    // 0xc17ce8: r1 = Instance_Clip
    //     0xc17ce8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc17cec: ldr             x1, [x1, #0x7e0]
    // 0xc17cf0: StoreField: r0->field_2b = r1
    //     0xc17cf0: stur            w1, [x0, #0x2b]
    // 0xc17cf4: r1 = Instance_HitTestBehavior
    //     0xc17cf4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xc17cf8: ldr             x1, [x1, #0x288]
    // 0xc17cfc: StoreField: r0->field_2f = r1
    //     0xc17cfc: stur            w1, [x0, #0x2f]
    // 0xc17d00: LeaveFrame
    //     0xc17d00: mov             SP, fp
    //     0xc17d04: ldp             fp, lr, [SP], #0x10
    // 0xc17d08: ret
    //     0xc17d08: ret             
    // 0xc17d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17d0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17d10: b               #0xc16bc8
    // 0xc17d14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc17d18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc17d1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc17d20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc17d24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc17d28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc17d2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17d2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0xc17d30, size: 0xb8
    // 0xc17d30: EnterFrame
    //     0xc17d30: stp             fp, lr, [SP, #-0x10]!
    //     0xc17d34: mov             fp, SP
    // 0xc17d38: AllocStack(0x38)
    //     0xc17d38: sub             SP, SP, #0x38
    // 0xc17d3c: SetupParameters()
    //     0xc17d3c: ldr             x0, [fp, #0x38]
    //     0xc17d40: ldur            w1, [x0, #0x17]
    //     0xc17d44: add             x1, x1, HEAP, lsl #32
    // 0xc17d48: CheckStackOverflow
    //     0xc17d48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17d4c: cmp             SP, x16
    //     0xc17d50: b.ls            #0xc17ddc
    // 0xc17d54: LoadField: r0 = r1->field_f
    //     0xc17d54: ldur            w0, [x1, #0xf]
    // 0xc17d58: DecompressPointer r0
    //     0xc17d58: add             x0, x0, HEAP, lsl #32
    // 0xc17d5c: LoadField: r1 = r0->field_b
    //     0xc17d5c: ldur            w1, [x0, #0xb]
    // 0xc17d60: DecompressPointer r1
    //     0xc17d60: add             x1, x1, HEAP, lsl #32
    // 0xc17d64: stur            x1, [fp, #-8]
    // 0xc17d68: cmp             w1, NULL
    // 0xc17d6c: b.eq            #0xc17de4
    // 0xc17d70: ldr             x0, [fp, #0x10]
    // 0xc17d74: cmp             w0, NULL
    // 0xc17d78: b.ne            #0xc17d88
    // 0xc17d7c: r0 = ProductRating()
    //     0xc17d7c: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xc17d80: mov             x1, x0
    // 0xc17d84: b               #0xc17d8c
    // 0xc17d88: mov             x1, x0
    // 0xc17d8c: ldur            x0, [fp, #-8]
    // 0xc17d90: LoadField: r2 = r0->field_2f
    //     0xc17d90: ldur            w2, [x0, #0x2f]
    // 0xc17d94: DecompressPointer r2
    //     0xc17d94: add             x2, x2, HEAP, lsl #32
    // 0xc17d98: ldr             x16, [fp, #0x30]
    // 0xc17d9c: stp             x16, x2, [SP, #0x20]
    // 0xc17da0: ldr             x16, [fp, #0x28]
    // 0xc17da4: ldr             lr, [fp, #0x20]
    // 0xc17da8: stp             lr, x16, [SP, #0x10]
    // 0xc17dac: ldr             x16, [fp, #0x18]
    // 0xc17db0: stp             x1, x16, [SP]
    // 0xc17db4: r4 = 0
    //     0xc17db4: movz            x4, #0
    // 0xc17db8: ldr             x0, [SP, #0x28]
    // 0xc17dbc: r16 = UnlinkedCall_0x613b5c
    //     0xc17dbc: add             x16, PP, #0x51, lsl #12  ; [pp+0x51ce0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc17dc0: add             x16, x16, #0xce0
    // 0xc17dc4: ldp             x5, lr, [x16]
    // 0xc17dc8: blr             lr
    // 0xc17dcc: r0 = Null
    //     0xc17dcc: mov             x0, NULL
    // 0xc17dd0: LeaveFrame
    //     0xc17dd0: mov             SP, fp
    //     0xc17dd4: ldp             fp, lr, [SP], #0x10
    // 0xc17dd8: ret
    //     0xc17dd8: ret             
    // 0xc17ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17ddc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17de0: b               #0xc17d54
    // 0xc17de4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17de4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String, String) {
    // ** addr: 0xc17de8, size: 0xac
    // 0xc17de8: EnterFrame
    //     0xc17de8: stp             fp, lr, [SP, #-0x10]!
    //     0xc17dec: mov             fp, SP
    // 0xc17df0: AllocStack(0x48)
    //     0xc17df0: sub             SP, SP, #0x48
    // 0xc17df4: SetupParameters()
    //     0xc17df4: ldr             x0, [fp, #0x50]
    //     0xc17df8: ldur            w1, [x0, #0x17]
    //     0xc17dfc: add             x1, x1, HEAP, lsl #32
    // 0xc17e00: CheckStackOverflow
    //     0xc17e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17e04: cmp             SP, x16
    //     0xc17e08: b.ls            #0xc17e88
    // 0xc17e0c: LoadField: r0 = r1->field_f
    //     0xc17e0c: ldur            w0, [x1, #0xf]
    // 0xc17e10: DecompressPointer r0
    //     0xc17e10: add             x0, x0, HEAP, lsl #32
    // 0xc17e14: LoadField: r1 = r0->field_b
    //     0xc17e14: ldur            w1, [x0, #0xb]
    // 0xc17e18: DecompressPointer r1
    //     0xc17e18: add             x1, x1, HEAP, lsl #32
    // 0xc17e1c: cmp             w1, NULL
    // 0xc17e20: b.eq            #0xc17e90
    // 0xc17e24: LoadField: r0 = r1->field_37
    //     0xc17e24: ldur            w0, [x1, #0x37]
    // 0xc17e28: DecompressPointer r0
    //     0xc17e28: add             x0, x0, HEAP, lsl #32
    // 0xc17e2c: ldr             x16, [fp, #0x48]
    // 0xc17e30: stp             x16, x0, [SP, #0x38]
    // 0xc17e34: ldr             x16, [fp, #0x40]
    // 0xc17e38: ldr             lr, [fp, #0x38]
    // 0xc17e3c: stp             lr, x16, [SP, #0x28]
    // 0xc17e40: ldr             x16, [fp, #0x30]
    // 0xc17e44: ldr             lr, [fp, #0x28]
    // 0xc17e48: stp             lr, x16, [SP, #0x18]
    // 0xc17e4c: ldr             x16, [fp, #0x20]
    // 0xc17e50: ldr             lr, [fp, #0x18]
    // 0xc17e54: stp             lr, x16, [SP, #8]
    // 0xc17e58: ldr             x16, [fp, #0x10]
    // 0xc17e5c: str             x16, [SP]
    // 0xc17e60: r4 = 0
    //     0xc17e60: movz            x4, #0
    // 0xc17e64: ldr             x0, [SP, #0x40]
    // 0xc17e68: r16 = UnlinkedCall_0x613b5c
    //     0xc17e68: add             x16, PP, #0x51, lsl #12  ; [pp+0x51cf0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc17e6c: add             x16, x16, #0xcf0
    // 0xc17e70: ldp             x5, lr, [x16]
    // 0xc17e74: blr             lr
    // 0xc17e78: r0 = Null
    //     0xc17e78: mov             x0, NULL
    // 0xc17e7c: LeaveFrame
    //     0xc17e7c: mov             SP, fp
    //     0xc17e80: ldp             fp, lr, [SP], #0x10
    // 0xc17e84: ret
    //     0xc17e84: ret             
    // 0xc17e88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17e88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17e8c: b               #0xc17e0c
    // 0xc17e90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17e90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String) {
    // ** addr: 0xc17e94, size: 0x98
    // 0xc17e94: EnterFrame
    //     0xc17e94: stp             fp, lr, [SP, #-0x10]!
    //     0xc17e98: mov             fp, SP
    // 0xc17e9c: AllocStack(0x30)
    //     0xc17e9c: sub             SP, SP, #0x30
    // 0xc17ea0: SetupParameters()
    //     0xc17ea0: ldr             x0, [fp, #0x38]
    //     0xc17ea4: ldur            w1, [x0, #0x17]
    //     0xc17ea8: add             x1, x1, HEAP, lsl #32
    // 0xc17eac: CheckStackOverflow
    //     0xc17eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17eb0: cmp             SP, x16
    //     0xc17eb4: b.ls            #0xc17f20
    // 0xc17eb8: LoadField: r0 = r1->field_f
    //     0xc17eb8: ldur            w0, [x1, #0xf]
    // 0xc17ebc: DecompressPointer r0
    //     0xc17ebc: add             x0, x0, HEAP, lsl #32
    // 0xc17ec0: LoadField: r1 = r0->field_b
    //     0xc17ec0: ldur            w1, [x0, #0xb]
    // 0xc17ec4: DecompressPointer r1
    //     0xc17ec4: add             x1, x1, HEAP, lsl #32
    // 0xc17ec8: cmp             w1, NULL
    // 0xc17ecc: b.eq            #0xc17f28
    // 0xc17ed0: LoadField: r0 = r1->field_33
    //     0xc17ed0: ldur            w0, [x1, #0x33]
    // 0xc17ed4: DecompressPointer r0
    //     0xc17ed4: add             x0, x0, HEAP, lsl #32
    // 0xc17ed8: ldr             x16, [fp, #0x30]
    // 0xc17edc: stp             x16, x0, [SP, #0x20]
    // 0xc17ee0: ldr             x16, [fp, #0x28]
    // 0xc17ee4: ldr             lr, [fp, #0x20]
    // 0xc17ee8: stp             lr, x16, [SP, #0x10]
    // 0xc17eec: ldr             x16, [fp, #0x18]
    // 0xc17ef0: ldr             lr, [fp, #0x10]
    // 0xc17ef4: stp             lr, x16, [SP]
    // 0xc17ef8: r4 = 0
    //     0xc17ef8: movz            x4, #0
    // 0xc17efc: ldr             x0, [SP, #0x28]
    // 0xc17f00: r16 = UnlinkedCall_0x613b5c
    //     0xc17f00: add             x16, PP, #0x51, lsl #12  ; [pp+0x51d80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc17f04: add             x16, x16, #0xd80
    // 0xc17f08: ldp             x5, lr, [x16]
    // 0xc17f0c: blr             lr
    // 0xc17f10: r0 = Null
    //     0xc17f10: mov             x0, NULL
    // 0xc17f14: LeaveFrame
    //     0xc17f14: mov             SP, fp
    //     0xc17f18: ldp             fp, lr, [SP], #0x10
    // 0xc17f1c: ret
    //     0xc17f1c: ret             
    // 0xc17f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17f20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17f24: b               #0xc17eb8
    // 0xc17f28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17f28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, String) {
    // ** addr: 0xc17f2c, size: 0x8c
    // 0xc17f2c: EnterFrame
    //     0xc17f2c: stp             fp, lr, [SP, #-0x10]!
    //     0xc17f30: mov             fp, SP
    // 0xc17f34: AllocStack(0x20)
    //     0xc17f34: sub             SP, SP, #0x20
    // 0xc17f38: SetupParameters()
    //     0xc17f38: ldr             x0, [fp, #0x28]
    //     0xc17f3c: ldur            w1, [x0, #0x17]
    //     0xc17f40: add             x1, x1, HEAP, lsl #32
    // 0xc17f44: CheckStackOverflow
    //     0xc17f44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17f48: cmp             SP, x16
    //     0xc17f4c: b.ls            #0xc17fac
    // 0xc17f50: LoadField: r0 = r1->field_f
    //     0xc17f50: ldur            w0, [x1, #0xf]
    // 0xc17f54: DecompressPointer r0
    //     0xc17f54: add             x0, x0, HEAP, lsl #32
    // 0xc17f58: LoadField: r1 = r0->field_b
    //     0xc17f58: ldur            w1, [x0, #0xb]
    // 0xc17f5c: DecompressPointer r1
    //     0xc17f5c: add             x1, x1, HEAP, lsl #32
    // 0xc17f60: cmp             w1, NULL
    // 0xc17f64: b.eq            #0xc17fb4
    // 0xc17f68: LoadField: r0 = r1->field_2b
    //     0xc17f68: ldur            w0, [x1, #0x2b]
    // 0xc17f6c: DecompressPointer r0
    //     0xc17f6c: add             x0, x0, HEAP, lsl #32
    // 0xc17f70: ldr             x16, [fp, #0x20]
    // 0xc17f74: stp             x16, x0, [SP, #0x10]
    // 0xc17f78: ldr             x16, [fp, #0x18]
    // 0xc17f7c: ldr             lr, [fp, #0x10]
    // 0xc17f80: stp             lr, x16, [SP]
    // 0xc17f84: r4 = 0
    //     0xc17f84: movz            x4, #0
    // 0xc17f88: ldr             x0, [SP, #0x18]
    // 0xc17f8c: r16 = UnlinkedCall_0x613b5c
    //     0xc17f8c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51d90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc17f90: add             x16, x16, #0xd90
    // 0xc17f94: ldp             x5, lr, [x16]
    // 0xc17f98: blr             lr
    // 0xc17f9c: r0 = Null
    //     0xc17f9c: mov             x0, NULL
    // 0xc17fa0: LeaveFrame
    //     0xc17fa0: mov             SP, fp
    //     0xc17fa4: ldp             fp, lr, [SP], #0x10
    // 0xc17fa8: ret
    //     0xc17fa8: ret             
    // 0xc17fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc17fac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc17fb0: b               #0xc17f50
    // 0xc17fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc17fb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xc17fb8, size: 0x84
    // 0xc17fb8: EnterFrame
    //     0xc17fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xc17fbc: mov             fp, SP
    // 0xc17fc0: AllocStack(0x10)
    //     0xc17fc0: sub             SP, SP, #0x10
    // 0xc17fc4: SetupParameters()
    //     0xc17fc4: ldr             x0, [fp, #0x18]
    //     0xc17fc8: ldur            w1, [x0, #0x17]
    //     0xc17fcc: add             x1, x1, HEAP, lsl #32
    //     0xc17fd0: stur            x1, [fp, #-8]
    // 0xc17fd4: CheckStackOverflow
    //     0xc17fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17fd8: cmp             SP, x16
    //     0xc17fdc: b.ls            #0xc18034
    // 0xc17fe0: r1 = 1
    //     0xc17fe0: movz            x1, #0x1
    // 0xc17fe4: r0 = AllocateContext()
    //     0xc17fe4: bl              #0x16f6108  ; AllocateContextStub
    // 0xc17fe8: mov             x1, x0
    // 0xc17fec: ldur            x0, [fp, #-8]
    // 0xc17ff0: StoreField: r1->field_b = r0
    //     0xc17ff0: stur            w0, [x1, #0xb]
    // 0xc17ff4: ldr             x2, [fp, #0x10]
    // 0xc17ff8: StoreField: r1->field_f = r2
    //     0xc17ff8: stur            w2, [x1, #0xf]
    // 0xc17ffc: LoadField: r3 = r0->field_f
    //     0xc17ffc: ldur            w3, [x0, #0xf]
    // 0xc18000: DecompressPointer r3
    //     0xc18000: add             x3, x3, HEAP, lsl #32
    // 0xc18004: mov             x2, x1
    // 0xc18008: stur            x3, [fp, #-0x10]
    // 0xc1800c: r1 = Function '<anonymous closure>':.
    //     0xc1800c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51da0] AnonymousClosure: (0xc1803c), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xc18010: ldr             x1, [x1, #0xda0]
    // 0xc18014: r0 = AllocateClosure()
    //     0xc18014: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc18018: ldur            x1, [fp, #-0x10]
    // 0xc1801c: mov             x2, x0
    // 0xc18020: r0 = setState()
    //     0xc18020: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc18024: r0 = Null
    //     0xc18024: mov             x0, NULL
    // 0xc18028: LeaveFrame
    //     0xc18028: mov             SP, fp
    //     0xc1802c: ldp             fp, lr, [SP], #0x10
    // 0xc18030: ret
    //     0xc18030: ret             
    // 0xc18034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18038: b               #0xc17fe0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc1803c, size: 0x1dc
    // 0xc1803c: EnterFrame
    //     0xc1803c: stp             fp, lr, [SP, #-0x10]!
    //     0xc18040: mov             fp, SP
    // 0xc18044: AllocStack(0x38)
    //     0xc18044: sub             SP, SP, #0x38
    // 0xc18048: SetupParameters()
    //     0xc18048: ldr             x0, [fp, #0x10]
    //     0xc1804c: ldur            w3, [x0, #0x17]
    //     0xc18050: add             x3, x3, HEAP, lsl #32
    //     0xc18054: stur            x3, [fp, #-0x18]
    // 0xc18058: CheckStackOverflow
    //     0xc18058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1805c: cmp             SP, x16
    //     0xc18060: b.ls            #0xc18204
    // 0xc18064: LoadField: r0 = r3->field_b
    //     0xc18064: ldur            w0, [x3, #0xb]
    // 0xc18068: DecompressPointer r0
    //     0xc18068: add             x0, x0, HEAP, lsl #32
    // 0xc1806c: stur            x0, [fp, #-0x10]
    // 0xc18070: LoadField: r4 = r0->field_f
    //     0xc18070: ldur            w4, [x0, #0xf]
    // 0xc18074: DecompressPointer r4
    //     0xc18074: add             x4, x4, HEAP, lsl #32
    // 0xc18078: stur            x4, [fp, #-8]
    // 0xc1807c: LoadField: r2 = r3->field_f
    //     0xc1807c: ldur            w2, [x3, #0xf]
    // 0xc18080: DecompressPointer r2
    //     0xc18080: add             x2, x2, HEAP, lsl #32
    // 0xc18084: r1 = _ConstMap len:5
    //     0xc18084: add             x1, PP, #0x51, lsl #12  ; [pp+0x51da8] Map<String, String>(5)
    //     0xc18088: ldr             x1, [x1, #0xda8]
    // 0xc1808c: r0 = []()
    //     0xc1808c: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xc18090: cmp             w0, NULL
    // 0xc18094: b.ne            #0xc180b4
    // 0xc18098: ldur            x1, [fp, #-0x10]
    // 0xc1809c: LoadField: r0 = r1->field_f
    //     0xc1809c: ldur            w0, [x1, #0xf]
    // 0xc180a0: DecompressPointer r0
    //     0xc180a0: add             x0, x0, HEAP, lsl #32
    // 0xc180a4: LoadField: r2 = r0->field_1b
    //     0xc180a4: ldur            w2, [x0, #0x1b]
    // 0xc180a8: DecompressPointer r2
    //     0xc180a8: add             x2, x2, HEAP, lsl #32
    // 0xc180ac: mov             x0, x2
    // 0xc180b0: b               #0xc180b8
    // 0xc180b4: ldur            x1, [fp, #-0x10]
    // 0xc180b8: ldur            x2, [fp, #-8]
    // 0xc180bc: StoreField: r2->field_1b = r0
    //     0xc180bc: stur            w0, [x2, #0x1b]
    //     0xc180c0: ldurb           w16, [x2, #-1]
    //     0xc180c4: ldurb           w17, [x0, #-1]
    //     0xc180c8: and             x16, x17, x16, lsr #2
    //     0xc180cc: tst             x16, HEAP, lsr #32
    //     0xc180d0: b.eq            #0xc180d8
    //     0xc180d4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc180d8: LoadField: r0 = r1->field_f
    //     0xc180d8: ldur            w0, [x1, #0xf]
    // 0xc180dc: DecompressPointer r0
    //     0xc180dc: add             x0, x0, HEAP, lsl #32
    // 0xc180e0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc180e0: ldur            w2, [x0, #0x17]
    // 0xc180e4: DecompressPointer r2
    //     0xc180e4: add             x2, x2, HEAP, lsl #32
    // 0xc180e8: LoadField: r3 = r2->field_27
    //     0xc180e8: ldur            w3, [x2, #0x27]
    // 0xc180ec: DecompressPointer r3
    //     0xc180ec: add             x3, x3, HEAP, lsl #32
    // 0xc180f0: LoadField: r2 = r3->field_7
    //     0xc180f0: ldur            w2, [x3, #7]
    // 0xc180f4: DecompressPointer r2
    //     0xc180f4: add             x2, x2, HEAP, lsl #32
    // 0xc180f8: LoadField: r4 = r2->field_7
    //     0xc180f8: ldur            w4, [x2, #7]
    // 0xc180fc: cbz             w4, #0xc18110
    // 0xc18100: LoadField: r2 = r3->field_7
    //     0xc18100: ldur            w2, [x3, #7]
    // 0xc18104: DecompressPointer r2
    //     0xc18104: add             x2, x2, HEAP, lsl #32
    // 0xc18108: mov             x4, x2
    // 0xc1810c: b               #0xc18114
    // 0xc18110: r4 = " "
    //     0xc18110: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xc18114: ldur            x2, [fp, #-0x18]
    // 0xc18118: LoadField: r5 = r0->field_b
    //     0xc18118: ldur            w5, [x0, #0xb]
    // 0xc1811c: DecompressPointer r5
    //     0xc1811c: add             x5, x5, HEAP, lsl #32
    // 0xc18120: cmp             w5, NULL
    // 0xc18124: b.eq            #0xc1820c
    // 0xc18128: LoadField: r6 = r0->field_1b
    //     0xc18128: ldur            w6, [x0, #0x1b]
    // 0xc1812c: DecompressPointer r6
    //     0xc1812c: add             x6, x6, HEAP, lsl #32
    // 0xc18130: LoadField: r0 = r3->field_7
    //     0xc18130: ldur            w0, [x3, #7]
    // 0xc18134: DecompressPointer r0
    //     0xc18134: add             x0, x0, HEAP, lsl #32
    // 0xc18138: LoadField: r3 = r5->field_27
    //     0xc18138: ldur            w3, [x5, #0x27]
    // 0xc1813c: DecompressPointer r3
    //     0xc1813c: add             x3, x3, HEAP, lsl #32
    // 0xc18140: stp             x4, x3, [SP, #0x10]
    // 0xc18144: stp             x0, x6, [SP]
    // 0xc18148: r4 = 0
    //     0xc18148: movz            x4, #0
    // 0xc1814c: ldr             x0, [SP, #0x18]
    // 0xc18150: r16 = UnlinkedCall_0x613b5c
    //     0xc18150: add             x16, PP, #0x51, lsl #12  ; [pp+0x51db0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc18154: add             x16, x16, #0xdb0
    // 0xc18158: ldp             x5, lr, [x16]
    // 0xc1815c: blr             lr
    // 0xc18160: ldur            x0, [fp, #-0x10]
    // 0xc18164: LoadField: r1 = r0->field_f
    //     0xc18164: ldur            w1, [x0, #0xf]
    // 0xc18168: DecompressPointer r1
    //     0xc18168: add             x1, x1, HEAP, lsl #32
    // 0xc1816c: LoadField: r3 = r1->field_b
    //     0xc1816c: ldur            w3, [x1, #0xb]
    // 0xc18170: DecompressPointer r3
    //     0xc18170: add             x3, x3, HEAP, lsl #32
    // 0xc18174: stur            x3, [fp, #-0x10]
    // 0xc18178: cmp             w3, NULL
    // 0xc1817c: b.eq            #0xc18210
    // 0xc18180: ldur            x0, [fp, #-0x18]
    // 0xc18184: LoadField: r4 = r0->field_f
    //     0xc18184: ldur            w4, [x0, #0xf]
    // 0xc18188: DecompressPointer r4
    //     0xc18188: add             x4, x4, HEAP, lsl #32
    // 0xc1818c: stur            x4, [fp, #-8]
    // 0xc18190: cmp             w4, NULL
    // 0xc18194: b.eq            #0xc18214
    // 0xc18198: mov             x0, x4
    // 0xc1819c: r2 = Null
    //     0xc1819c: mov             x2, NULL
    // 0xc181a0: r1 = Null
    //     0xc181a0: mov             x1, NULL
    // 0xc181a4: r4 = 60
    //     0xc181a4: movz            x4, #0x3c
    // 0xc181a8: branchIfSmi(r0, 0xc181b4)
    //     0xc181a8: tbz             w0, #0, #0xc181b4
    // 0xc181ac: r4 = LoadClassIdInstr(r0)
    //     0xc181ac: ldur            x4, [x0, #-1]
    //     0xc181b0: ubfx            x4, x4, #0xc, #0x14
    // 0xc181b4: sub             x4, x4, #0x5e
    // 0xc181b8: cmp             x4, #1
    // 0xc181bc: b.ls            #0xc181d0
    // 0xc181c0: r8 = String
    //     0xc181c0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xc181c4: r3 = Null
    //     0xc181c4: add             x3, PP, #0x51, lsl #12  ; [pp+0x51dc0] Null
    //     0xc181c8: ldr             x3, [x3, #0xdc0]
    // 0xc181cc: r0 = String()
    //     0xc181cc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xc181d0: ldur            x0, [fp, #-8]
    // 0xc181d4: ldur            x1, [fp, #-0x10]
    // 0xc181d8: StoreField: r1->field_f = r0
    //     0xc181d8: stur            w0, [x1, #0xf]
    //     0xc181dc: ldurb           w16, [x1, #-1]
    //     0xc181e0: ldurb           w17, [x0, #-1]
    //     0xc181e4: and             x16, x17, x16, lsr #2
    //     0xc181e8: tst             x16, HEAP, lsr #32
    //     0xc181ec: b.eq            #0xc181f4
    //     0xc181f0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc181f4: r0 = Null
    //     0xc181f4: mov             x0, NULL
    // 0xc181f8: LeaveFrame
    //     0xc181f8: mov             SP, fp
    //     0xc181fc: ldp             fp, lr, [SP], #0x10
    // 0xc18200: ret
    //     0xc18200: ret             
    // 0xc18204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18204: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc18208: b               #0xc18064
    // 0xc1820c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1820c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18210: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18210: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc18214: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc18214: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xc18218, size: 0xb0
    // 0xc18218: EnterFrame
    //     0xc18218: stp             fp, lr, [SP, #-0x10]!
    //     0xc1821c: mov             fp, SP
    // 0xc18220: AllocStack(0x20)
    //     0xc18220: sub             SP, SP, #0x20
    // 0xc18224: SetupParameters()
    //     0xc18224: ldr             x0, [fp, #0x18]
    //     0xc18228: ldur            w2, [x0, #0x17]
    //     0xc1822c: add             x2, x2, HEAP, lsl #32
    //     0xc18230: stur            x2, [fp, #-8]
    // 0xc18234: CheckStackOverflow
    //     0xc18234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc18238: cmp             SP, x16
    //     0xc1823c: b.ls            #0xc182bc
    // 0xc18240: ldr             x1, [fp, #0x10]
    // 0xc18244: r0 = trim()
    //     0xc18244: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xc18248: mov             x1, x0
    // 0xc1824c: ldur            x0, [fp, #-8]
    // 0xc18250: LoadField: r2 = r0->field_f
    //     0xc18250: ldur            w2, [x0, #0xf]
    // 0xc18254: DecompressPointer r2
    //     0xc18254: add             x2, x2, HEAP, lsl #32
    // 0xc18258: LoadField: r3 = r2->field_b
    //     0xc18258: ldur            w3, [x2, #0xb]
    // 0xc1825c: DecompressPointer r3
    //     0xc1825c: add             x3, x3, HEAP, lsl #32
    // 0xc18260: cmp             w3, NULL
    // 0xc18264: b.eq            #0xc182c4
    // 0xc18268: LoadField: r4 = r2->field_1b
    //     0xc18268: ldur            w4, [x2, #0x1b]
    // 0xc1826c: DecompressPointer r4
    //     0xc1826c: add             x4, x4, HEAP, lsl #32
    // 0xc18270: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xc18270: ldur            w2, [x3, #0x17]
    // 0xc18274: DecompressPointer r2
    //     0xc18274: add             x2, x2, HEAP, lsl #32
    // 0xc18278: stp             x1, x2, [SP, #8]
    // 0xc1827c: str             x4, [SP]
    // 0xc18280: r4 = 0
    //     0xc18280: movz            x4, #0
    // 0xc18284: ldr             x0, [SP, #0x10]
    // 0xc18288: r16 = UnlinkedCall_0x613b5c
    //     0xc18288: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc1828c: add             x16, x16, #0xe20
    // 0xc18290: ldp             x5, lr, [x16]
    // 0xc18294: blr             lr
    // 0xc18298: ldur            x1, [fp, #-8]
    // 0xc1829c: LoadField: r2 = r1->field_f
    //     0xc1829c: ldur            w2, [x1, #0xf]
    // 0xc182a0: DecompressPointer r2
    //     0xc182a0: add             x2, x2, HEAP, lsl #32
    // 0xc182a4: r1 = false
    //     0xc182a4: add             x1, NULL, #0x30  ; false
    // 0xc182a8: StoreField: r2->field_1f = r1
    //     0xc182a8: stur            w1, [x2, #0x1f]
    // 0xc182ac: r0 = Null
    //     0xc182ac: mov             x0, NULL
    // 0xc182b0: LeaveFrame
    //     0xc182b0: mov             SP, fp
    //     0xc182b4: ldp             fp, lr, [SP], #0x10
    // 0xc182b8: ret
    //     0xc182b8: ret             
    // 0xc182bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc182bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc182c0: b               #0xc18240
    // 0xc182c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc182c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3954, size: 0x48, field offset: 0xc
class SearchView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81558, size: 0x48
    // 0xc81558: EnterFrame
    //     0xc81558: stp             fp, lr, [SP, #-0x10]!
    //     0xc8155c: mov             fp, SP
    // 0xc81560: AllocStack(0x8)
    //     0xc81560: sub             SP, SP, #8
    // 0xc81564: CheckStackOverflow
    //     0xc81564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc81568: cmp             SP, x16
    //     0xc8156c: b.ls            #0xc81598
    // 0xc81570: r1 = <SearchView>
    //     0xc81570: add             x1, PP, #0x48, lsl #12  ; [pp+0x48238] TypeArguments: <SearchView>
    //     0xc81574: ldr             x1, [x1, #0x238]
    // 0xc81578: r0 = _SearchViewState()
    //     0xc81578: bl              #0xc815a0  ; Allocate_SearchViewStateStub -> _SearchViewState (size=0x24)
    // 0xc8157c: mov             x1, x0
    // 0xc81580: stur            x0, [fp, #-8]
    // 0xc81584: r0 = _SearchViewState()
    //     0xc81584: bl              #0xc7ccc4  ; [package:customer_app/app/presentation/views/basic/search/widgets/search_view.dart] _SearchViewState::_SearchViewState
    // 0xc81588: ldur            x0, [fp, #-8]
    // 0xc8158c: LeaveFrame
    //     0xc8158c: mov             SP, fp
    //     0xc81590: ldp             fp, lr, [SP], #0x10
    // 0xc81594: ret
    //     0xc81594: ret             
    // 0xc81598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81598: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8159c: b               #0xc81570
  }
  _ SearchView(/* No info */) {
    // ** addr: 0x13b0ecc, size: 0x210
    // 0x13b0ecc: EnterFrame
    //     0x13b0ecc: stp             fp, lr, [SP, #-0x10]!
    //     0x13b0ed0: mov             fp, SP
    // 0x13b0ed4: mov             x0, x2
    // 0x13b0ed8: mov             x4, x3
    // 0x13b0edc: mov             x3, x5
    // 0x13b0ee0: mov             x5, x2
    // 0x13b0ee4: mov             x2, x6
    // 0x13b0ee8: mov             x6, x1
    // 0x13b0eec: mov             x1, x7
    // 0x13b0ef0: StoreField: r6->field_b = r0
    //     0x13b0ef0: stur            w0, [x6, #0xb]
    //     0x13b0ef4: ldurb           w16, [x6, #-1]
    //     0x13b0ef8: ldurb           w17, [x0, #-1]
    //     0x13b0efc: and             x16, x17, x16, lsr #2
    //     0x13b0f00: tst             x16, HEAP, lsr #32
    //     0x13b0f04: b.eq            #0x13b0f0c
    //     0x13b0f08: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0f0c: mov             x0, x4
    // 0x13b0f10: StoreField: r6->field_f = r0
    //     0x13b0f10: stur            w0, [x6, #0xf]
    //     0x13b0f14: ldurb           w16, [x6, #-1]
    //     0x13b0f18: ldurb           w17, [x0, #-1]
    //     0x13b0f1c: and             x16, x17, x16, lsr #2
    //     0x13b0f20: tst             x16, HEAP, lsr #32
    //     0x13b0f24: b.eq            #0x13b0f2c
    //     0x13b0f28: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0f2c: mov             x0, x2
    // 0x13b0f30: StoreField: r6->field_13 = r0
    //     0x13b0f30: stur            w0, [x6, #0x13]
    //     0x13b0f34: ldurb           w16, [x6, #-1]
    //     0x13b0f38: ldurb           w17, [x0, #-1]
    //     0x13b0f3c: and             x16, x17, x16, lsr #2
    //     0x13b0f40: tst             x16, HEAP, lsr #32
    //     0x13b0f44: b.eq            #0x13b0f4c
    //     0x13b0f48: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0f4c: ldr             x0, [fp, #0x38]
    // 0x13b0f50: ArrayStore: r6[0] = r0  ; List_4
    //     0x13b0f50: stur            w0, [x6, #0x17]
    //     0x13b0f54: ldurb           w16, [x6, #-1]
    //     0x13b0f58: ldurb           w17, [x0, #-1]
    //     0x13b0f5c: and             x16, x17, x16, lsr #2
    //     0x13b0f60: tst             x16, HEAP, lsr #32
    //     0x13b0f64: b.eq            #0x13b0f6c
    //     0x13b0f68: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0f6c: ldr             x0, [fp, #0x40]
    // 0x13b0f70: StoreField: r6->field_1b = r0
    //     0x13b0f70: stur            w0, [x6, #0x1b]
    //     0x13b0f74: ldurb           w16, [x6, #-1]
    //     0x13b0f78: ldurb           w17, [x0, #-1]
    //     0x13b0f7c: and             x16, x17, x16, lsr #2
    //     0x13b0f80: tst             x16, HEAP, lsr #32
    //     0x13b0f84: b.eq            #0x13b0f8c
    //     0x13b0f88: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0f8c: ldr             x0, [fp, #0x48]
    // 0x13b0f90: StoreField: r6->field_1f = r0
    //     0x13b0f90: stur            w0, [x6, #0x1f]
    //     0x13b0f94: ldurb           w16, [x6, #-1]
    //     0x13b0f98: ldurb           w17, [x0, #-1]
    //     0x13b0f9c: and             x16, x17, x16, lsr #2
    //     0x13b0fa0: tst             x16, HEAP, lsr #32
    //     0x13b0fa4: b.eq            #0x13b0fac
    //     0x13b0fa8: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0fac: ldr             x0, [fp, #0x28]
    // 0x13b0fb0: StoreField: r6->field_23 = r0
    //     0x13b0fb0: stur            w0, [x6, #0x23]
    //     0x13b0fb4: ldurb           w16, [x6, #-1]
    //     0x13b0fb8: ldurb           w17, [x0, #-1]
    //     0x13b0fbc: and             x16, x17, x16, lsr #2
    //     0x13b0fc0: tst             x16, HEAP, lsr #32
    //     0x13b0fc4: b.eq            #0x13b0fcc
    //     0x13b0fc8: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0fcc: mov             x0, x1
    // 0x13b0fd0: StoreField: r6->field_27 = r0
    //     0x13b0fd0: stur            w0, [x6, #0x27]
    //     0x13b0fd4: ldurb           w16, [x6, #-1]
    //     0x13b0fd8: ldurb           w17, [x0, #-1]
    //     0x13b0fdc: and             x16, x17, x16, lsr #2
    //     0x13b0fe0: tst             x16, HEAP, lsr #32
    //     0x13b0fe4: b.eq            #0x13b0fec
    //     0x13b0fe8: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b0fec: ldr             x0, [fp, #0x10]
    // 0x13b0ff0: StoreField: r6->field_33 = r0
    //     0x13b0ff0: stur            w0, [x6, #0x33]
    //     0x13b0ff4: ldurb           w16, [x6, #-1]
    //     0x13b0ff8: ldurb           w17, [x0, #-1]
    //     0x13b0ffc: and             x16, x17, x16, lsr #2
    //     0x13b1000: tst             x16, HEAP, lsr #32
    //     0x13b1004: b.eq            #0x13b100c
    //     0x13b1008: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b100c: ldr             x0, [fp, #0x30]
    // 0x13b1010: StoreField: r6->field_37 = r0
    //     0x13b1010: stur            w0, [x6, #0x37]
    //     0x13b1014: ldurb           w16, [x6, #-1]
    //     0x13b1018: ldurb           w17, [x0, #-1]
    //     0x13b101c: and             x16, x17, x16, lsr #2
    //     0x13b1020: tst             x16, HEAP, lsr #32
    //     0x13b1024: b.eq            #0x13b102c
    //     0x13b1028: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b102c: mov             x0, x3
    // 0x13b1030: StoreField: r6->field_2b = r0
    //     0x13b1030: stur            w0, [x6, #0x2b]
    //     0x13b1034: ldurb           w16, [x6, #-1]
    //     0x13b1038: ldurb           w17, [x0, #-1]
    //     0x13b103c: and             x16, x17, x16, lsr #2
    //     0x13b1040: tst             x16, HEAP, lsr #32
    //     0x13b1044: b.eq            #0x13b104c
    //     0x13b1048: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b104c: ldr             x0, [fp, #0x20]
    // 0x13b1050: StoreField: r6->field_2f = r0
    //     0x13b1050: stur            w0, [x6, #0x2f]
    //     0x13b1054: ldurb           w16, [x6, #-1]
    //     0x13b1058: ldurb           w17, [x0, #-1]
    //     0x13b105c: and             x16, x17, x16, lsr #2
    //     0x13b1060: tst             x16, HEAP, lsr #32
    //     0x13b1064: b.eq            #0x13b106c
    //     0x13b1068: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b106c: ldr             x0, [fp, #0x58]
    // 0x13b1070: StoreField: r6->field_3f = r0
    //     0x13b1070: stur            w0, [x6, #0x3f]
    //     0x13b1074: ldurb           w16, [x6, #-1]
    //     0x13b1078: ldurb           w17, [x0, #-1]
    //     0x13b107c: and             x16, x17, x16, lsr #2
    //     0x13b1080: tst             x16, HEAP, lsr #32
    //     0x13b1084: b.eq            #0x13b108c
    //     0x13b1088: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b108c: ldr             x0, [fp, #0x50]
    // 0x13b1090: StoreField: r6->field_3b = r0
    //     0x13b1090: stur            w0, [x6, #0x3b]
    //     0x13b1094: ldurb           w16, [x6, #-1]
    //     0x13b1098: ldurb           w17, [x0, #-1]
    //     0x13b109c: and             x16, x17, x16, lsr #2
    //     0x13b10a0: tst             x16, HEAP, lsr #32
    //     0x13b10a4: b.eq            #0x13b10ac
    //     0x13b10a8: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b10ac: ldr             x0, [fp, #0x18]
    // 0x13b10b0: StoreField: r6->field_43 = r0
    //     0x13b10b0: stur            w0, [x6, #0x43]
    //     0x13b10b4: ldurb           w16, [x6, #-1]
    //     0x13b10b8: ldurb           w17, [x0, #-1]
    //     0x13b10bc: and             x16, x17, x16, lsr #2
    //     0x13b10c0: tst             x16, HEAP, lsr #32
    //     0x13b10c4: b.eq            #0x13b10cc
    //     0x13b10c8: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0x13b10cc: r0 = Null
    //     0x13b10cc: mov             x0, NULL
    // 0x13b10d0: LeaveFrame
    //     0x13b10d0: mov             SP, fp
    //     0x13b10d4: ldp             fp, lr, [SP], #0x10
    // 0x13b10d8: ret
    //     0x13b10d8: ret             
  }
}
