// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart

// class id: 1049530, size: 0x8
class :: {
}

// class id: 3241, size: 0x20, field offset: 0x14
class _TestimonialMoreImagesWidgetState extends State<dynamic> {

  late PageController _pageController; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x949cbc, size: 0xa8
    // 0x949cbc: EnterFrame
    //     0x949cbc: stp             fp, lr, [SP, #-0x10]!
    //     0x949cc0: mov             fp, SP
    // 0x949cc4: AllocStack(0x18)
    //     0x949cc4: sub             SP, SP, #0x18
    // 0x949cc8: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x10 */)
    //     0x949cc8: stur            x1, [fp, #-0x10]
    // 0x949ccc: CheckStackOverflow
    //     0x949ccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949cd0: cmp             SP, x16
    //     0x949cd4: b.ls            #0x949d58
    // 0x949cd8: LoadField: r0 = r1->field_b
    //     0x949cd8: ldur            w0, [x1, #0xb]
    // 0x949cdc: DecompressPointer r0
    //     0x949cdc: add             x0, x0, HEAP, lsl #32
    // 0x949ce0: cmp             w0, NULL
    // 0x949ce4: b.eq            #0x949d60
    // 0x949ce8: LoadField: r2 = r0->field_f
    //     0x949ce8: ldur            x2, [x0, #0xf]
    // 0x949cec: stur            x2, [fp, #-8]
    // 0x949cf0: StoreField: r1->field_13 = r2
    //     0x949cf0: stur            x2, [x1, #0x13]
    // 0x949cf4: r0 = PageController()
    //     0x949cf4: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x949cf8: mov             x2, x0
    // 0x949cfc: ldur            x0, [fp, #-8]
    // 0x949d00: stur            x2, [fp, #-0x18]
    // 0x949d04: StoreField: r2->field_3f = r0
    //     0x949d04: stur            x0, [x2, #0x3f]
    // 0x949d08: r0 = true
    //     0x949d08: add             x0, NULL, #0x20  ; true
    // 0x949d0c: StoreField: r2->field_47 = r0
    //     0x949d0c: stur            w0, [x2, #0x47]
    // 0x949d10: d0 = 1.000000
    //     0x949d10: fmov            d0, #1.00000000
    // 0x949d14: StoreField: r2->field_4b = d0
    //     0x949d14: stur            d0, [x2, #0x4b]
    // 0x949d18: mov             x1, x2
    // 0x949d1c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x949d1c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x949d20: r0 = ScrollController()
    //     0x949d20: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x949d24: ldur            x0, [fp, #-0x18]
    // 0x949d28: ldur            x1, [fp, #-0x10]
    // 0x949d2c: StoreField: r1->field_1b = r0
    //     0x949d2c: stur            w0, [x1, #0x1b]
    //     0x949d30: ldurb           w16, [x1, #-1]
    //     0x949d34: ldurb           w17, [x0, #-1]
    //     0x949d38: and             x16, x17, x16, lsr #2
    //     0x949d3c: tst             x16, HEAP, lsr #32
    //     0x949d40: b.eq            #0x949d48
    //     0x949d44: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x949d48: r0 = Null
    //     0x949d48: mov             x0, NULL
    // 0x949d4c: LeaveFrame
    //     0x949d4c: mov             SP, fp
    //     0x949d50: ldp             fp, lr, [SP], #0x10
    // 0x949d54: ret
    //     0x949d54: ret             
    // 0x949d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949d58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949d5c: b               #0x949cd8
    // 0x949d60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949d60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa66fc8, size: 0x34
    // 0xa66fc8: ldr             x1, [SP]
    // 0xa66fcc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa66fcc: ldur            w2, [x1, #0x17]
    // 0xa66fd0: DecompressPointer r2
    //     0xa66fd0: add             x2, x2, HEAP, lsl #32
    // 0xa66fd4: LoadField: r1 = r2->field_f
    //     0xa66fd4: ldur            w1, [x2, #0xf]
    // 0xa66fd8: DecompressPointer r1
    //     0xa66fd8: add             x1, x1, HEAP, lsl #32
    // 0xa66fdc: LoadField: r3 = r2->field_13
    //     0xa66fdc: ldur            w3, [x2, #0x13]
    // 0xa66fe0: DecompressPointer r3
    //     0xa66fe0: add             x3, x3, HEAP, lsl #32
    // 0xa66fe4: r2 = LoadInt32Instr(r3)
    //     0xa66fe4: sbfx            x2, x3, #1, #0x1f
    //     0xa66fe8: tbz             w3, #0, #0xa66ff0
    //     0xa66fec: ldur            x2, [x3, #7]
    // 0xa66ff0: StoreField: r1->field_13 = r2
    //     0xa66ff0: stur            x2, [x1, #0x13]
    // 0xa66ff4: r0 = Null
    //     0xa66ff4: mov             x0, NULL
    // 0xa66ff8: ret
    //     0xa66ff8: ret             
  }
  _ _onImageTapped(/* No info */) {
    // ** addr: 0xa66ffc, size: 0x80
    // 0xa66ffc: EnterFrame
    //     0xa66ffc: stp             fp, lr, [SP, #-0x10]!
    //     0xa67000: mov             fp, SP
    // 0xa67004: AllocStack(0x10)
    //     0xa67004: sub             SP, SP, #0x10
    // 0xa67008: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa67008: stur            x1, [fp, #-8]
    //     0xa6700c: stur            x2, [fp, #-0x10]
    // 0xa67010: CheckStackOverflow
    //     0xa67010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa67014: cmp             SP, x16
    //     0xa67018: b.ls            #0xa67074
    // 0xa6701c: r1 = 2
    //     0xa6701c: movz            x1, #0x2
    // 0xa67020: r0 = AllocateContext()
    //     0xa67020: bl              #0x16f6108  ; AllocateContextStub
    // 0xa67024: mov             x2, x0
    // 0xa67028: ldur            x3, [fp, #-8]
    // 0xa6702c: StoreField: r2->field_f = r3
    //     0xa6702c: stur            w3, [x2, #0xf]
    // 0xa67030: ldur            x4, [fp, #-0x10]
    // 0xa67034: r0 = BoxInt64Instr(r4)
    //     0xa67034: sbfiz           x0, x4, #1, #0x1f
    //     0xa67038: cmp             x4, x0, asr #1
    //     0xa6703c: b.eq            #0xa67048
    //     0xa67040: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa67044: stur            x4, [x0, #7]
    // 0xa67048: StoreField: r2->field_13 = r0
    //     0xa67048: stur            w0, [x2, #0x13]
    // 0xa6704c: r1 = Function '<anonymous closure>':.
    //     0xa6704c: add             x1, PP, #0x53, lsl #12  ; [pp+0x534f8] AnonymousClosure: (0xa66fc8), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped (0xa66ffc)
    //     0xa67050: ldr             x1, [x1, #0x4f8]
    // 0xa67054: r0 = AllocateClosure()
    //     0xa67054: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa67058: ldur            x1, [fp, #-8]
    // 0xa6705c: mov             x2, x0
    // 0xa67060: r0 = setState()
    //     0xa67060: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa67064: r0 = Null
    //     0xa67064: mov             x0, NULL
    // 0xa67068: LeaveFrame
    //     0xa67068: mov             SP, fp
    //     0xa6706c: ldp             fp, lr, [SP], #0x10
    // 0xa67070: ret
    //     0xa67070: ret             
    // 0xa67074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa67074: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa67078: b               #0xa6701c
  }
  _ build(/* No info */) {
    // ** addr: 0xbf2de4, size: 0x41c
    // 0xbf2de4: EnterFrame
    //     0xbf2de4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf2de8: mov             fp, SP
    // 0xbf2dec: AllocStack(0x60)
    //     0xbf2dec: sub             SP, SP, #0x60
    // 0xbf2df0: SetupParameters(_TestimonialMoreImagesWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbf2df0: stur            x1, [fp, #-8]
    //     0xbf2df4: stur            x2, [fp, #-0x10]
    // 0xbf2df8: CheckStackOverflow
    //     0xbf2df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf2dfc: cmp             SP, x16
    //     0xbf2e00: b.ls            #0xbf31e4
    // 0xbf2e04: r1 = 2
    //     0xbf2e04: movz            x1, #0x2
    // 0xbf2e08: r0 = AllocateContext()
    //     0xbf2e08: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf2e0c: mov             x3, x0
    // 0xbf2e10: ldur            x0, [fp, #-8]
    // 0xbf2e14: stur            x3, [fp, #-0x18]
    // 0xbf2e18: StoreField: r3->field_f = r0
    //     0xbf2e18: stur            w0, [x3, #0xf]
    // 0xbf2e1c: ldur            x1, [fp, #-0x10]
    // 0xbf2e20: StoreField: r3->field_13 = r1
    //     0xbf2e20: stur            w1, [x3, #0x13]
    // 0xbf2e24: mov             x2, x3
    // 0xbf2e28: r1 = Function '<anonymous closure>':.
    //     0xbf2e28: add             x1, PP, #0x53, lsl #12  ; [pp+0x53470] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xbf2e2c: ldr             x1, [x1, #0x470]
    // 0xbf2e30: r0 = AllocateClosure()
    //     0xbf2e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2e34: stur            x0, [fp, #-0x10]
    // 0xbf2e38: r0 = IconButton()
    //     0xbf2e38: bl              #0x9881fc  ; AllocateIconButtonStub -> IconButton (size=0x70)
    // 0xbf2e3c: mov             x1, x0
    // 0xbf2e40: ldur            x0, [fp, #-0x10]
    // 0xbf2e44: stur            x1, [fp, #-0x20]
    // 0xbf2e48: StoreField: r1->field_3b = r0
    //     0xbf2e48: stur            w0, [x1, #0x3b]
    // 0xbf2e4c: r0 = false
    //     0xbf2e4c: add             x0, NULL, #0x30  ; false
    // 0xbf2e50: StoreField: r1->field_4f = r0
    //     0xbf2e50: stur            w0, [x1, #0x4f]
    // 0xbf2e54: r2 = Instance_Icon
    //     0xbf2e54: add             x2, PP, #0x53, lsl #12  ; [pp+0x53478] Obj!Icon@d663b1
    //     0xbf2e58: ldr             x2, [x2, #0x478]
    // 0xbf2e5c: StoreField: r1->field_1f = r2
    //     0xbf2e5c: stur            w2, [x1, #0x1f]
    // 0xbf2e60: r2 = Instance__IconButtonVariant
    //     0xbf2e60: add             x2, PP, #0x52, lsl #12  ; [pp+0x52900] Obj!_IconButtonVariant@d745e1
    //     0xbf2e64: ldr             x2, [x2, #0x900]
    // 0xbf2e68: StoreField: r1->field_6b = r2
    //     0xbf2e68: stur            w2, [x1, #0x6b]
    // 0xbf2e6c: r0 = Padding()
    //     0xbf2e6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf2e70: mov             x1, x0
    // 0xbf2e74: r0 = Instance_EdgeInsets
    //     0xbf2e74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbf2e78: ldr             x0, [x0, #0x980]
    // 0xbf2e7c: stur            x1, [fp, #-0x10]
    // 0xbf2e80: StoreField: r1->field_f = r0
    //     0xbf2e80: stur            w0, [x1, #0xf]
    // 0xbf2e84: ldur            x0, [fp, #-0x20]
    // 0xbf2e88: StoreField: r1->field_b = r0
    //     0xbf2e88: stur            w0, [x1, #0xb]
    // 0xbf2e8c: r0 = Align()
    //     0xbf2e8c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbf2e90: mov             x1, x0
    // 0xbf2e94: r0 = Instance_Alignment
    //     0xbf2e94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbf2e98: ldr             x0, [x0, #0x950]
    // 0xbf2e9c: stur            x1, [fp, #-0x20]
    // 0xbf2ea0: StoreField: r1->field_f = r0
    //     0xbf2ea0: stur            w0, [x1, #0xf]
    // 0xbf2ea4: ldur            x0, [fp, #-0x10]
    // 0xbf2ea8: StoreField: r1->field_b = r0
    //     0xbf2ea8: stur            w0, [x1, #0xb]
    // 0xbf2eac: ldur            x2, [fp, #-8]
    // 0xbf2eb0: LoadField: r0 = r2->field_b
    //     0xbf2eb0: ldur            w0, [x2, #0xb]
    // 0xbf2eb4: DecompressPointer r0
    //     0xbf2eb4: add             x0, x0, HEAP, lsl #32
    // 0xbf2eb8: cmp             w0, NULL
    // 0xbf2ebc: b.eq            #0xbf31ec
    // 0xbf2ec0: LoadField: r3 = r0->field_b
    //     0xbf2ec0: ldur            w3, [x0, #0xb]
    // 0xbf2ec4: DecompressPointer r3
    //     0xbf2ec4: add             x3, x3, HEAP, lsl #32
    // 0xbf2ec8: cmp             w3, NULL
    // 0xbf2ecc: b.ne            #0xbf2ed8
    // 0xbf2ed0: r3 = Null
    //     0xbf2ed0: mov             x3, NULL
    // 0xbf2ed4: b               #0xbf2ee0
    // 0xbf2ed8: LoadField: r4 = r3->field_b
    //     0xbf2ed8: ldur            w4, [x3, #0xb]
    // 0xbf2edc: mov             x3, x4
    // 0xbf2ee0: cmp             w3, NULL
    // 0xbf2ee4: b.ne            #0xbf2f24
    // 0xbf2ee8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbf2ee8: ldur            w3, [x0, #0x17]
    // 0xbf2eec: DecompressPointer r3
    //     0xbf2eec: add             x3, x3, HEAP, lsl #32
    // 0xbf2ef0: cmp             w3, NULL
    // 0xbf2ef4: b.ne            #0xbf2f00
    // 0xbf2ef8: r0 = Null
    //     0xbf2ef8: mov             x0, NULL
    // 0xbf2efc: b               #0xbf2f1c
    // 0xbf2f00: r0 = LoadClassIdInstr(r3)
    //     0xbf2f00: ldur            x0, [x3, #-1]
    //     0xbf2f04: ubfx            x0, x0, #0xc, #0x14
    // 0xbf2f08: str             x3, [SP]
    // 0xbf2f0c: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbf2f0c: movz            x17, #0xc898
    //     0xbf2f10: add             lr, x0, x17
    //     0xbf2f14: ldr             lr, [x21, lr, lsl #3]
    //     0xbf2f18: blr             lr
    // 0xbf2f1c: mov             x4, x0
    // 0xbf2f20: b               #0xbf2f28
    // 0xbf2f24: mov             x4, x3
    // 0xbf2f28: ldur            x3, [fp, #-8]
    // 0xbf2f2c: ldur            x0, [fp, #-0x20]
    // 0xbf2f30: stur            x4, [fp, #-0x28]
    // 0xbf2f34: LoadField: r5 = r3->field_1b
    //     0xbf2f34: ldur            w5, [x3, #0x1b]
    // 0xbf2f38: DecompressPointer r5
    //     0xbf2f38: add             x5, x5, HEAP, lsl #32
    // 0xbf2f3c: r16 = Sentinel
    //     0xbf2f3c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf2f40: cmp             w5, w16
    // 0xbf2f44: b.eq            #0xbf31f0
    // 0xbf2f48: ldur            x2, [fp, #-0x18]
    // 0xbf2f4c: stur            x5, [fp, #-0x10]
    // 0xbf2f50: r1 = Function '<anonymous closure>':.
    //     0xbf2f50: add             x1, PP, #0x53, lsl #12  ; [pp+0x53480] AnonymousClosure: (0xbf3768), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xbf2de4)
    //     0xbf2f54: ldr             x1, [x1, #0x480]
    // 0xbf2f58: r0 = AllocateClosure()
    //     0xbf2f58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2f5c: ldur            x2, [fp, #-0x18]
    // 0xbf2f60: r1 = Function '<anonymous closure>':.
    //     0xbf2f60: add             x1, PP, #0x53, lsl #12  ; [pp+0x53488] AnonymousClosure: (0xbf3588), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xbf2de4)
    //     0xbf2f64: ldr             x1, [x1, #0x488]
    // 0xbf2f68: stur            x0, [fp, #-0x30]
    // 0xbf2f6c: r0 = AllocateClosure()
    //     0xbf2f6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf2f70: stur            x0, [fp, #-0x38]
    // 0xbf2f74: r0 = PageView()
    //     0xbf2f74: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbf2f78: stur            x0, [fp, #-0x40]
    // 0xbf2f7c: ldur            x16, [fp, #-0x10]
    // 0xbf2f80: str             x16, [SP]
    // 0xbf2f84: mov             x1, x0
    // 0xbf2f88: ldur            x2, [fp, #-0x38]
    // 0xbf2f8c: ldur            x3, [fp, #-0x28]
    // 0xbf2f90: ldur            x5, [fp, #-0x30]
    // 0xbf2f94: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xbf2f94: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xbf2f98: ldr             x4, [x4, #0xd60]
    // 0xbf2f9c: r0 = PageView.builder()
    //     0xbf2f9c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbf2fa0: r1 = <FlexParentData>
    //     0xbf2fa0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbf2fa4: ldr             x1, [x1, #0xe00]
    // 0xbf2fa8: r0 = Expanded()
    //     0xbf2fa8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbf2fac: mov             x3, x0
    // 0xbf2fb0: r0 = 1
    //     0xbf2fb0: movz            x0, #0x1
    // 0xbf2fb4: stur            x3, [fp, #-0x10]
    // 0xbf2fb8: StoreField: r3->field_13 = r0
    //     0xbf2fb8: stur            x0, [x3, #0x13]
    // 0xbf2fbc: r0 = Instance_FlexFit
    //     0xbf2fbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbf2fc0: ldr             x0, [x0, #0xe08]
    // 0xbf2fc4: StoreField: r3->field_1b = r0
    //     0xbf2fc4: stur            w0, [x3, #0x1b]
    // 0xbf2fc8: ldur            x0, [fp, #-0x40]
    // 0xbf2fcc: StoreField: r3->field_b = r0
    //     0xbf2fcc: stur            w0, [x3, #0xb]
    // 0xbf2fd0: r1 = Null
    //     0xbf2fd0: mov             x1, NULL
    // 0xbf2fd4: r2 = 6
    //     0xbf2fd4: movz            x2, #0x6
    // 0xbf2fd8: r0 = AllocateArray()
    //     0xbf2fd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf2fdc: mov             x2, x0
    // 0xbf2fe0: ldur            x0, [fp, #-0x20]
    // 0xbf2fe4: stur            x2, [fp, #-0x28]
    // 0xbf2fe8: StoreField: r2->field_f = r0
    //     0xbf2fe8: stur            w0, [x2, #0xf]
    // 0xbf2fec: r16 = Instance_SizedBox
    //     0xbf2fec: add             x16, PP, #0x53, lsl #12  ; [pp+0x53490] Obj!SizedBox@d68021
    //     0xbf2ff0: ldr             x16, [x16, #0x490]
    // 0xbf2ff4: StoreField: r2->field_13 = r16
    //     0xbf2ff4: stur            w16, [x2, #0x13]
    // 0xbf2ff8: ldur            x0, [fp, #-0x10]
    // 0xbf2ffc: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf2ffc: stur            w0, [x2, #0x17]
    // 0xbf3000: r1 = <Widget>
    //     0xbf3000: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf3004: r0 = AllocateGrowableArray()
    //     0xbf3004: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf3008: mov             x1, x0
    // 0xbf300c: ldur            x0, [fp, #-0x28]
    // 0xbf3010: stur            x1, [fp, #-0x10]
    // 0xbf3014: StoreField: r1->field_f = r0
    //     0xbf3014: stur            w0, [x1, #0xf]
    // 0xbf3018: r0 = 6
    //     0xbf3018: movz            x0, #0x6
    // 0xbf301c: StoreField: r1->field_b = r0
    //     0xbf301c: stur            w0, [x1, #0xb]
    // 0xbf3020: r0 = Column()
    //     0xbf3020: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf3024: mov             x1, x0
    // 0xbf3028: r0 = Instance_Axis
    //     0xbf3028: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf302c: stur            x1, [fp, #-0x20]
    // 0xbf3030: StoreField: r1->field_f = r0
    //     0xbf3030: stur            w0, [x1, #0xf]
    // 0xbf3034: r0 = Instance_MainAxisAlignment
    //     0xbf3034: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf3038: ldr             x0, [x0, #0xa08]
    // 0xbf303c: StoreField: r1->field_13 = r0
    //     0xbf303c: stur            w0, [x1, #0x13]
    // 0xbf3040: r0 = Instance_MainAxisSize
    //     0xbf3040: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf3044: ldr             x0, [x0, #0xa10]
    // 0xbf3048: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf3048: stur            w0, [x1, #0x17]
    // 0xbf304c: r0 = Instance_CrossAxisAlignment
    //     0xbf304c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf3050: ldr             x0, [x0, #0xa18]
    // 0xbf3054: StoreField: r1->field_1b = r0
    //     0xbf3054: stur            w0, [x1, #0x1b]
    // 0xbf3058: r0 = Instance_VerticalDirection
    //     0xbf3058: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf305c: ldr             x0, [x0, #0xa20]
    // 0xbf3060: StoreField: r1->field_23 = r0
    //     0xbf3060: stur            w0, [x1, #0x23]
    // 0xbf3064: r0 = Instance_Clip
    //     0xbf3064: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf3068: ldr             x0, [x0, #0x38]
    // 0xbf306c: StoreField: r1->field_2b = r0
    //     0xbf306c: stur            w0, [x1, #0x2b]
    // 0xbf3070: StoreField: r1->field_2f = rZR
    //     0xbf3070: stur            xzr, [x1, #0x2f]
    // 0xbf3074: ldur            x0, [fp, #-0x10]
    // 0xbf3078: StoreField: r1->field_b = r0
    //     0xbf3078: stur            w0, [x1, #0xb]
    // 0xbf307c: r0 = SafeArea()
    //     0xbf307c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xbf3080: mov             x2, x0
    // 0xbf3084: r1 = true
    //     0xbf3084: add             x1, NULL, #0x20  ; true
    // 0xbf3088: stur            x2, [fp, #-0x10]
    // 0xbf308c: StoreField: r2->field_b = r1
    //     0xbf308c: stur            w1, [x2, #0xb]
    // 0xbf3090: StoreField: r2->field_f = r1
    //     0xbf3090: stur            w1, [x2, #0xf]
    // 0xbf3094: StoreField: r2->field_13 = r1
    //     0xbf3094: stur            w1, [x2, #0x13]
    // 0xbf3098: ArrayStore: r2[0] = r1  ; List_4
    //     0xbf3098: stur            w1, [x2, #0x17]
    // 0xbf309c: r0 = Instance_EdgeInsets
    //     0xbf309c: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbf30a0: StoreField: r2->field_1b = r0
    //     0xbf30a0: stur            w0, [x2, #0x1b]
    // 0xbf30a4: r3 = false
    //     0xbf30a4: add             x3, NULL, #0x30  ; false
    // 0xbf30a8: StoreField: r2->field_1f = r3
    //     0xbf30a8: stur            w3, [x2, #0x1f]
    // 0xbf30ac: ldur            x0, [fp, #-0x20]
    // 0xbf30b0: StoreField: r2->field_23 = r0
    //     0xbf30b0: stur            w0, [x2, #0x23]
    // 0xbf30b4: ldur            x0, [fp, #-8]
    // 0xbf30b8: LoadField: r4 = r0->field_b
    //     0xbf30b8: ldur            w4, [x0, #0xb]
    // 0xbf30bc: DecompressPointer r4
    //     0xbf30bc: add             x4, x4, HEAP, lsl #32
    // 0xbf30c0: cmp             w4, NULL
    // 0xbf30c4: b.eq            #0xbf31fc
    // 0xbf30c8: LoadField: r0 = r4->field_b
    //     0xbf30c8: ldur            w0, [x4, #0xb]
    // 0xbf30cc: DecompressPointer r0
    //     0xbf30cc: add             x0, x0, HEAP, lsl #32
    // 0xbf30d0: cmp             w0, NULL
    // 0xbf30d4: b.ne            #0xbf30e0
    // 0xbf30d8: r0 = Null
    //     0xbf30d8: mov             x0, NULL
    // 0xbf30dc: b               #0xbf30e8
    // 0xbf30e0: LoadField: r5 = r0->field_b
    //     0xbf30e0: ldur            w5, [x0, #0xb]
    // 0xbf30e4: mov             x0, x5
    // 0xbf30e8: cmp             w0, NULL
    // 0xbf30ec: b.ne            #0xbf3130
    // 0xbf30f0: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xbf30f0: ldur            w0, [x4, #0x17]
    // 0xbf30f4: DecompressPointer r0
    //     0xbf30f4: add             x0, x0, HEAP, lsl #32
    // 0xbf30f8: cmp             w0, NULL
    // 0xbf30fc: b.ne            #0xbf3108
    // 0xbf3100: r0 = Null
    //     0xbf3100: mov             x0, NULL
    // 0xbf3104: b               #0xbf3128
    // 0xbf3108: r4 = LoadClassIdInstr(r0)
    //     0xbf3108: ldur            x4, [x0, #-1]
    //     0xbf310c: ubfx            x4, x4, #0xc, #0x14
    // 0xbf3110: str             x0, [SP]
    // 0xbf3114: mov             x0, x4
    // 0xbf3118: r0 = GDT[cid_x0 + 0xc898]()
    //     0xbf3118: movz            x17, #0xc898
    //     0xbf311c: add             lr, x0, x17
    //     0xbf3120: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3124: blr             lr
    // 0xbf3128: mov             x3, x0
    // 0xbf312c: b               #0xbf3134
    // 0xbf3130: mov             x3, x0
    // 0xbf3134: ldur            x0, [fp, #-0x10]
    // 0xbf3138: ldur            x2, [fp, #-0x18]
    // 0xbf313c: stur            x3, [fp, #-8]
    // 0xbf3140: r1 = Function '<anonymous closure>':.
    //     0xbf3140: add             x1, PP, #0x53, lsl #12  ; [pp+0x53498] AnonymousClosure: (0xbf3200), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xbf2de4)
    //     0xbf3144: ldr             x1, [x1, #0x498]
    // 0xbf3148: r0 = AllocateClosure()
    //     0xbf3148: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf314c: stur            x0, [fp, #-0x18]
    // 0xbf3150: r0 = ListView()
    //     0xbf3150: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbf3154: stur            x0, [fp, #-0x20]
    // 0xbf3158: r16 = Instance_Axis
    //     0xbf3158: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf315c: str             x16, [SP]
    // 0xbf3160: mov             x1, x0
    // 0xbf3164: ldur            x2, [fp, #-0x18]
    // 0xbf3168: ldur            x3, [fp, #-8]
    // 0xbf316c: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xbf316c: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xbf3170: ldr             x4, [x4, #0x4a0]
    // 0xbf3174: r0 = ListView.builder()
    //     0xbf3174: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbf3178: r0 = Container()
    //     0xbf3178: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf317c: stur            x0, [fp, #-8]
    // 0xbf3180: r16 = Instance_Color
    //     0xbf3180: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf3184: r30 = 170.000000
    //     0xbf3184: add             lr, PP, #0x53, lsl #12  ; [pp+0x534a8] 170
    //     0xbf3188: ldr             lr, [lr, #0x4a8]
    // 0xbf318c: stp             lr, x16, [SP, #0x10]
    // 0xbf3190: r16 = Instance_EdgeInsets
    //     0xbf3190: add             x16, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xbf3194: ldr             x16, [x16, #0x878]
    // 0xbf3198: ldur            lr, [fp, #-0x20]
    // 0xbf319c: stp             lr, x16, [SP]
    // 0xbf31a0: mov             x1, x0
    // 0xbf31a4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, height, 0x2, padding, 0x3, null]
    //     0xbf31a4: add             x4, PP, #0x53, lsl #12  ; [pp+0x534b0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "height", 0x2, "padding", 0x3, Null]
    //     0xbf31a8: ldr             x4, [x4, #0x4b0]
    // 0xbf31ac: r0 = Container()
    //     0xbf31ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf31b0: r0 = Scaffold()
    //     0xbf31b0: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xbf31b4: ldur            x1, [fp, #-0x10]
    // 0xbf31b8: ArrayStore: r0[0] = r1  ; List_4
    //     0xbf31b8: stur            w1, [x0, #0x17]
    // 0xbf31bc: ldur            x1, [fp, #-8]
    // 0xbf31c0: StoreField: r0->field_37 = r1
    //     0xbf31c0: stur            w1, [x0, #0x37]
    // 0xbf31c4: r1 = true
    //     0xbf31c4: add             x1, NULL, #0x20  ; true
    // 0xbf31c8: StoreField: r0->field_43 = r1
    //     0xbf31c8: stur            w1, [x0, #0x43]
    // 0xbf31cc: r1 = false
    //     0xbf31cc: add             x1, NULL, #0x30  ; false
    // 0xbf31d0: StoreField: r0->field_b = r1
    //     0xbf31d0: stur            w1, [x0, #0xb]
    // 0xbf31d4: StoreField: r0->field_f = r1
    //     0xbf31d4: stur            w1, [x0, #0xf]
    // 0xbf31d8: LeaveFrame
    //     0xbf31d8: mov             SP, fp
    //     0xbf31dc: ldp             fp, lr, [SP], #0x10
    // 0xbf31e0: ret
    //     0xbf31e0: ret             
    // 0xbf31e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf31e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf31e8: b               #0xbf2e04
    // 0xbf31ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf31ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf31f0: r9 = _pageController
    //     0xbf31f0: add             x9, PP, #0x53, lsl #12  ; [pp+0x534b8] Field <_TestimonialMoreImagesWidgetState@1709203245._pageController@1709203245>: late (offset: 0x1c)
    //     0xbf31f4: ldr             x9, [x9, #0x4b8]
    // 0xbf31f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbf31f8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbf31fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf31fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbf3200, size: 0x2b4
    // 0xbf3200: EnterFrame
    //     0xbf3200: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3204: mov             fp, SP
    // 0xbf3208: AllocStack(0x50)
    //     0xbf3208: sub             SP, SP, #0x50
    // 0xbf320c: SetupParameters()
    //     0xbf320c: ldr             x0, [fp, #0x20]
    //     0xbf3210: ldur            w1, [x0, #0x17]
    //     0xbf3214: add             x1, x1, HEAP, lsl #32
    //     0xbf3218: stur            x1, [fp, #-8]
    // 0xbf321c: CheckStackOverflow
    //     0xbf321c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3220: cmp             SP, x16
    //     0xbf3224: b.ls            #0xbf34a4
    // 0xbf3228: r1 = 1
    //     0xbf3228: movz            x1, #0x1
    // 0xbf322c: r0 = AllocateContext()
    //     0xbf322c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf3230: mov             x3, x0
    // 0xbf3234: ldur            x0, [fp, #-8]
    // 0xbf3238: stur            x3, [fp, #-0x10]
    // 0xbf323c: StoreField: r3->field_b = r0
    //     0xbf323c: stur            w0, [x3, #0xb]
    // 0xbf3240: ldr             x1, [fp, #0x10]
    // 0xbf3244: StoreField: r3->field_f = r1
    //     0xbf3244: stur            w1, [x3, #0xf]
    // 0xbf3248: LoadField: r2 = r0->field_f
    //     0xbf3248: ldur            w2, [x0, #0xf]
    // 0xbf324c: DecompressPointer r2
    //     0xbf324c: add             x2, x2, HEAP, lsl #32
    // 0xbf3250: LoadField: r4 = r2->field_13
    //     0xbf3250: ldur            x4, [x2, #0x13]
    // 0xbf3254: r2 = LoadInt32Instr(r1)
    //     0xbf3254: sbfx            x2, x1, #1, #0x1f
    //     0xbf3258: tbz             w1, #0, #0xbf3260
    //     0xbf325c: ldur            x2, [x1, #7]
    // 0xbf3260: cmp             x4, x2
    // 0xbf3264: b.ne            #0xbf3274
    // 0xbf3268: r2 = Instance_Color
    //     0xbf3268: add             x2, PP, #0x53, lsl #12  ; [pp+0x534c0] Obj!Color@d6b101
    //     0xbf326c: ldr             x2, [x2, #0x4c0]
    // 0xbf3270: b               #0xbf327c
    // 0xbf3274: r2 = Instance_Color
    //     0xbf3274: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xbf3278: ldr             x2, [x2, #0xf88]
    // 0xbf327c: r16 = 3.000000
    //     0xbf327c: add             x16, PP, #0x53, lsl #12  ; [pp+0x534c8] 3
    //     0xbf3280: ldr             x16, [x16, #0x4c8]
    // 0xbf3284: str             x16, [SP]
    // 0xbf3288: r1 = Null
    //     0xbf3288: mov             x1, NULL
    // 0xbf328c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbf328c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbf3290: ldr             x4, [x4, #0x108]
    // 0xbf3294: r0 = Border.all()
    //     0xbf3294: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbf3298: stur            x0, [fp, #-0x18]
    // 0xbf329c: r0 = BoxDecoration()
    //     0xbf329c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf32a0: mov             x2, x0
    // 0xbf32a4: ldur            x0, [fp, #-0x18]
    // 0xbf32a8: stur            x2, [fp, #-0x20]
    // 0xbf32ac: StoreField: r2->field_f = r0
    //     0xbf32ac: stur            w0, [x2, #0xf]
    // 0xbf32b0: r0 = Instance_BoxShape
    //     0xbf32b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf32b4: ldr             x0, [x0, #0x80]
    // 0xbf32b8: StoreField: r2->field_23 = r0
    //     0xbf32b8: stur            w0, [x2, #0x23]
    // 0xbf32bc: ldur            x0, [fp, #-8]
    // 0xbf32c0: LoadField: r1 = r0->field_f
    //     0xbf32c0: ldur            w1, [x0, #0xf]
    // 0xbf32c4: DecompressPointer r1
    //     0xbf32c4: add             x1, x1, HEAP, lsl #32
    // 0xbf32c8: LoadField: r3 = r1->field_b
    //     0xbf32c8: ldur            w3, [x1, #0xb]
    // 0xbf32cc: DecompressPointer r3
    //     0xbf32cc: add             x3, x3, HEAP, lsl #32
    // 0xbf32d0: cmp             w3, NULL
    // 0xbf32d4: b.eq            #0xbf34ac
    // 0xbf32d8: LoadField: r4 = r3->field_b
    //     0xbf32d8: ldur            w4, [x3, #0xb]
    // 0xbf32dc: DecompressPointer r4
    //     0xbf32dc: add             x4, x4, HEAP, lsl #32
    // 0xbf32e0: cmp             w4, NULL
    // 0xbf32e4: b.ne            #0xbf32f4
    // 0xbf32e8: ldur            x5, [fp, #-0x10]
    // 0xbf32ec: r0 = Null
    //     0xbf32ec: mov             x0, NULL
    // 0xbf32f0: b               #0xbf3358
    // 0xbf32f4: ldur            x5, [fp, #-0x10]
    // 0xbf32f8: LoadField: r0 = r5->field_f
    //     0xbf32f8: ldur            w0, [x5, #0xf]
    // 0xbf32fc: DecompressPointer r0
    //     0xbf32fc: add             x0, x0, HEAP, lsl #32
    // 0xbf3300: LoadField: r1 = r4->field_b
    //     0xbf3300: ldur            w1, [x4, #0xb]
    // 0xbf3304: r6 = LoadInt32Instr(r0)
    //     0xbf3304: sbfx            x6, x0, #1, #0x1f
    //     0xbf3308: tbz             w0, #0, #0xbf3310
    //     0xbf330c: ldur            x6, [x0, #7]
    // 0xbf3310: r0 = LoadInt32Instr(r1)
    //     0xbf3310: sbfx            x0, x1, #1, #0x1f
    // 0xbf3314: mov             x1, x6
    // 0xbf3318: cmp             x1, x0
    // 0xbf331c: b.hs            #0xbf34b0
    // 0xbf3320: LoadField: r0 = r4->field_f
    //     0xbf3320: ldur            w0, [x4, #0xf]
    // 0xbf3324: DecompressPointer r0
    //     0xbf3324: add             x0, x0, HEAP, lsl #32
    // 0xbf3328: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbf3328: add             x16, x0, x6, lsl #2
    //     0xbf332c: ldur            w1, [x16, #0xf]
    // 0xbf3330: DecompressPointer r1
    //     0xbf3330: add             x1, x1, HEAP, lsl #32
    // 0xbf3334: LoadField: r0 = r1->field_7
    //     0xbf3334: ldur            w0, [x1, #7]
    // 0xbf3338: DecompressPointer r0
    //     0xbf3338: add             x0, x0, HEAP, lsl #32
    // 0xbf333c: cmp             w0, NULL
    // 0xbf3340: b.ne            #0xbf334c
    // 0xbf3344: r0 = Null
    //     0xbf3344: mov             x0, NULL
    // 0xbf3348: b               #0xbf3358
    // 0xbf334c: LoadField: r1 = r0->field_b
    //     0xbf334c: ldur            w1, [x0, #0xb]
    // 0xbf3350: DecompressPointer r1
    //     0xbf3350: add             x1, x1, HEAP, lsl #32
    // 0xbf3354: mov             x0, x1
    // 0xbf3358: cmp             w0, NULL
    // 0xbf335c: b.ne            #0xbf33bc
    // 0xbf3360: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbf3360: ldur            w0, [x3, #0x17]
    // 0xbf3364: DecompressPointer r0
    //     0xbf3364: add             x0, x0, HEAP, lsl #32
    // 0xbf3368: cmp             w0, NULL
    // 0xbf336c: b.ne            #0xbf3378
    // 0xbf3370: r0 = Null
    //     0xbf3370: mov             x0, NULL
    // 0xbf3374: b               #0xbf33bc
    // 0xbf3378: LoadField: r1 = r5->field_f
    //     0xbf3378: ldur            w1, [x5, #0xf]
    // 0xbf337c: DecompressPointer r1
    //     0xbf337c: add             x1, x1, HEAP, lsl #32
    // 0xbf3380: r3 = LoadClassIdInstr(r0)
    //     0xbf3380: ldur            x3, [x0, #-1]
    //     0xbf3384: ubfx            x3, x3, #0xc, #0x14
    // 0xbf3388: stp             x1, x0, [SP]
    // 0xbf338c: mov             x0, x3
    // 0xbf3390: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbf3390: sub             lr, x0, #0xb7
    //     0xbf3394: ldr             lr, [x21, lr, lsl #3]
    //     0xbf3398: blr             lr
    // 0xbf339c: LoadField: r1 = r0->field_2b
    //     0xbf339c: ldur            w1, [x0, #0x2b]
    // 0xbf33a0: DecompressPointer r1
    //     0xbf33a0: add             x1, x1, HEAP, lsl #32
    // 0xbf33a4: cmp             w1, NULL
    // 0xbf33a8: b.ne            #0xbf33b4
    // 0xbf33ac: r0 = Null
    //     0xbf33ac: mov             x0, NULL
    // 0xbf33b0: b               #0xbf33bc
    // 0xbf33b4: LoadField: r0 = r1->field_b
    //     0xbf33b4: ldur            w0, [x1, #0xb]
    // 0xbf33b8: DecompressPointer r0
    //     0xbf33b8: add             x0, x0, HEAP, lsl #32
    // 0xbf33bc: cmp             w0, NULL
    // 0xbf33c0: b.ne            #0xbf33cc
    // 0xbf33c4: r2 = ""
    //     0xbf33c4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf33c8: b               #0xbf33d0
    // 0xbf33cc: mov             x2, x0
    // 0xbf33d0: stur            x2, [fp, #-8]
    // 0xbf33d4: r0 = ImageHeaders.forImages()
    //     0xbf33d4: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf33d8: r1 = Function '<anonymous closure>':.
    //     0xbf33d8: add             x1, PP, #0x53, lsl #12  ; [pp+0x534d0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf33dc: ldr             x1, [x1, #0x4d0]
    // 0xbf33e0: r2 = Null
    //     0xbf33e0: mov             x2, NULL
    // 0xbf33e4: stur            x0, [fp, #-0x18]
    // 0xbf33e8: r0 = AllocateClosure()
    //     0xbf33e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf33ec: r1 = Function '<anonymous closure>':.
    //     0xbf33ec: add             x1, PP, #0x53, lsl #12  ; [pp+0x534d8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf33f0: ldr             x1, [x1, #0x4d8]
    // 0xbf33f4: r2 = Null
    //     0xbf33f4: mov             x2, NULL
    // 0xbf33f8: stur            x0, [fp, #-0x28]
    // 0xbf33fc: r0 = AllocateClosure()
    //     0xbf33fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf3400: stur            x0, [fp, #-0x30]
    // 0xbf3404: r0 = CachedNetworkImage()
    //     0xbf3404: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf3408: stur            x0, [fp, #-0x38]
    // 0xbf340c: ldur            x16, [fp, #-0x18]
    // 0xbf3410: ldur            lr, [fp, #-0x28]
    // 0xbf3414: stp             lr, x16, [SP, #8]
    // 0xbf3418: ldur            x16, [fp, #-0x30]
    // 0xbf341c: str             x16, [SP]
    // 0xbf3420: mov             x1, x0
    // 0xbf3424: ldur            x2, [fp, #-8]
    // 0xbf3428: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xbf3428: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e0] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xbf342c: ldr             x4, [x4, #0x4e0]
    // 0xbf3430: r0 = CachedNetworkImage()
    //     0xbf3430: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf3434: r0 = Container()
    //     0xbf3434: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf3438: stur            x0, [fp, #-8]
    // 0xbf343c: ldur            x16, [fp, #-0x20]
    // 0xbf3440: r30 = Instance_EdgeInsets
    //     0xbf3440: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbf3444: ldr             lr, [lr, #0x980]
    // 0xbf3448: stp             lr, x16, [SP, #8]
    // 0xbf344c: ldur            x16, [fp, #-0x38]
    // 0xbf3450: str             x16, [SP]
    // 0xbf3454: mov             x1, x0
    // 0xbf3458: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, margin, 0x2, null]
    //     0xbf3458: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "margin", 0x2, Null]
    //     0xbf345c: ldr             x4, [x4, #0x4e8]
    // 0xbf3460: r0 = Container()
    //     0xbf3460: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf3464: r0 = GestureDetector()
    //     0xbf3464: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xbf3468: ldur            x2, [fp, #-0x10]
    // 0xbf346c: r1 = Function '<anonymous closure>':.
    //     0xbf346c: add             x1, PP, #0x53, lsl #12  ; [pp+0x534f0] AnonymousClosure: (0xbf34b4), in [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::build (0xbf2de4)
    //     0xbf3470: ldr             x1, [x1, #0x4f0]
    // 0xbf3474: stur            x0, [fp, #-0x10]
    // 0xbf3478: r0 = AllocateClosure()
    //     0xbf3478: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf347c: ldur            x16, [fp, #-8]
    // 0xbf3480: stp             x16, x0, [SP]
    // 0xbf3484: ldur            x1, [fp, #-0x10]
    // 0xbf3488: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xbf3488: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xbf348c: ldr             x4, [x4, #0xaf0]
    // 0xbf3490: r0 = GestureDetector()
    //     0xbf3490: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xbf3494: ldur            x0, [fp, #-0x10]
    // 0xbf3498: LeaveFrame
    //     0xbf3498: mov             SP, fp
    //     0xbf349c: ldp             fp, lr, [SP], #0x10
    // 0xbf34a0: ret
    //     0xbf34a0: ret             
    // 0xbf34a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf34a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf34a8: b               #0xbf3228
    // 0xbf34ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf34ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf34b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf34b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf34b4, size: 0xd4
    // 0xbf34b4: EnterFrame
    //     0xbf34b4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf34b8: mov             fp, SP
    // 0xbf34bc: AllocStack(0x10)
    //     0xbf34bc: sub             SP, SP, #0x10
    // 0xbf34c0: SetupParameters()
    //     0xbf34c0: ldr             x0, [fp, #0x10]
    //     0xbf34c4: ldur            w4, [x0, #0x17]
    //     0xbf34c8: add             x4, x4, HEAP, lsl #32
    //     0xbf34cc: stur            x4, [fp, #-0x10]
    // 0xbf34d0: CheckStackOverflow
    //     0xbf34d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf34d4: cmp             SP, x16
    //     0xbf34d8: b.ls            #0xbf3574
    // 0xbf34dc: LoadField: r0 = r4->field_b
    //     0xbf34dc: ldur            w0, [x4, #0xb]
    // 0xbf34e0: DecompressPointer r0
    //     0xbf34e0: add             x0, x0, HEAP, lsl #32
    // 0xbf34e4: stur            x0, [fp, #-8]
    // 0xbf34e8: LoadField: r1 = r0->field_f
    //     0xbf34e8: ldur            w1, [x0, #0xf]
    // 0xbf34ec: DecompressPointer r1
    //     0xbf34ec: add             x1, x1, HEAP, lsl #32
    // 0xbf34f0: LoadField: r2 = r4->field_f
    //     0xbf34f0: ldur            w2, [x4, #0xf]
    // 0xbf34f4: DecompressPointer r2
    //     0xbf34f4: add             x2, x2, HEAP, lsl #32
    // 0xbf34f8: r3 = LoadInt32Instr(r2)
    //     0xbf34f8: sbfx            x3, x2, #1, #0x1f
    //     0xbf34fc: tbz             w2, #0, #0xbf3504
    //     0xbf3500: ldur            x3, [x2, #7]
    // 0xbf3504: StoreField: r1->field_13 = r3
    //     0xbf3504: stur            x3, [x1, #0x13]
    // 0xbf3508: LoadField: r2 = r1->field_1b
    //     0xbf3508: ldur            w2, [x1, #0x1b]
    // 0xbf350c: DecompressPointer r2
    //     0xbf350c: add             x2, x2, HEAP, lsl #32
    // 0xbf3510: r16 = Sentinel
    //     0xbf3510: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbf3514: cmp             w2, w16
    // 0xbf3518: b.eq            #0xbf357c
    // 0xbf351c: mov             x1, x2
    // 0xbf3520: mov             x2, x3
    // 0xbf3524: r3 = Instance_Cubic
    //     0xbf3524: add             x3, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0xbf3528: ldr             x3, [x3, #0x2b0]
    // 0xbf352c: r5 = Instance_Duration
    //     0xbf352c: add             x5, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0xbf3530: ldr             x5, [x5, #0xf00]
    // 0xbf3534: r0 = animateToPage()
    //     0xbf3534: bl              #0x7f6cd0  ; [package:flutter/src/widgets/page_view.dart] PageController::animateToPage
    // 0xbf3538: ldur            x0, [fp, #-8]
    // 0xbf353c: LoadField: r1 = r0->field_f
    //     0xbf353c: ldur            w1, [x0, #0xf]
    // 0xbf3540: DecompressPointer r1
    //     0xbf3540: add             x1, x1, HEAP, lsl #32
    // 0xbf3544: ldur            x0, [fp, #-0x10]
    // 0xbf3548: LoadField: r2 = r0->field_f
    //     0xbf3548: ldur            w2, [x0, #0xf]
    // 0xbf354c: DecompressPointer r2
    //     0xbf354c: add             x2, x2, HEAP, lsl #32
    // 0xbf3550: r0 = LoadInt32Instr(r2)
    //     0xbf3550: sbfx            x0, x2, #1, #0x1f
    //     0xbf3554: tbz             w2, #0, #0xbf355c
    //     0xbf3558: ldur            x0, [x2, #7]
    // 0xbf355c: mov             x2, x0
    // 0xbf3560: r0 = _onImageTapped()
    //     0xbf3560: bl              #0xa66ffc  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped
    // 0xbf3564: r0 = Null
    //     0xbf3564: mov             x0, NULL
    // 0xbf3568: LeaveFrame
    //     0xbf3568: mov             SP, fp
    //     0xbf356c: ldp             fp, lr, [SP], #0x10
    // 0xbf3570: ret
    //     0xbf3570: ret             
    // 0xbf3574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3574: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3578: b               #0xbf34dc
    // 0xbf357c: r9 = _pageController
    //     0xbf357c: add             x9, PP, #0x53, lsl #12  ; [pp+0x534b8] Field <_TestimonialMoreImagesWidgetState@1709203245._pageController@1709203245>: late (offset: 0x1c)
    //     0xbf3580: ldr             x9, [x9, #0x4b8]
    // 0xbf3584: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbf3584: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbf3588, size: 0x1e0
    // 0xbf3588: EnterFrame
    //     0xbf3588: stp             fp, lr, [SP, #-0x10]!
    //     0xbf358c: mov             fp, SP
    // 0xbf3590: AllocStack(0x48)
    //     0xbf3590: sub             SP, SP, #0x48
    // 0xbf3594: SetupParameters()
    //     0xbf3594: ldr             x0, [fp, #0x20]
    //     0xbf3598: ldur            w1, [x0, #0x17]
    //     0xbf359c: add             x1, x1, HEAP, lsl #32
    //     0xbf35a0: stur            x1, [fp, #-8]
    // 0xbf35a4: CheckStackOverflow
    //     0xbf35a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf35a8: cmp             SP, x16
    //     0xbf35ac: b.ls            #0xbf3758
    // 0xbf35b0: r0 = ImageHeaders.forImages()
    //     0xbf35b0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf35b4: mov             x2, x0
    // 0xbf35b8: ldur            x0, [fp, #-8]
    // 0xbf35bc: stur            x2, [fp, #-0x10]
    // 0xbf35c0: LoadField: r1 = r0->field_f
    //     0xbf35c0: ldur            w1, [x0, #0xf]
    // 0xbf35c4: DecompressPointer r1
    //     0xbf35c4: add             x1, x1, HEAP, lsl #32
    // 0xbf35c8: LoadField: r3 = r1->field_b
    //     0xbf35c8: ldur            w3, [x1, #0xb]
    // 0xbf35cc: DecompressPointer r3
    //     0xbf35cc: add             x3, x3, HEAP, lsl #32
    // 0xbf35d0: cmp             w3, NULL
    // 0xbf35d4: b.eq            #0xbf3760
    // 0xbf35d8: LoadField: r4 = r3->field_b
    //     0xbf35d8: ldur            w4, [x3, #0xb]
    // 0xbf35dc: DecompressPointer r4
    //     0xbf35dc: add             x4, x4, HEAP, lsl #32
    // 0xbf35e0: cmp             w4, NULL
    // 0xbf35e4: b.ne            #0xbf35f4
    // 0xbf35e8: ldr             x5, [fp, #0x10]
    // 0xbf35ec: r0 = Null
    //     0xbf35ec: mov             x0, NULL
    // 0xbf35f0: b               #0xbf3654
    // 0xbf35f4: ldr             x5, [fp, #0x10]
    // 0xbf35f8: LoadField: r0 = r4->field_b
    //     0xbf35f8: ldur            w0, [x4, #0xb]
    // 0xbf35fc: r6 = LoadInt32Instr(r5)
    //     0xbf35fc: sbfx            x6, x5, #1, #0x1f
    //     0xbf3600: tbz             w5, #0, #0xbf3608
    //     0xbf3604: ldur            x6, [x5, #7]
    // 0xbf3608: r1 = LoadInt32Instr(r0)
    //     0xbf3608: sbfx            x1, x0, #1, #0x1f
    // 0xbf360c: mov             x0, x1
    // 0xbf3610: mov             x1, x6
    // 0xbf3614: cmp             x1, x0
    // 0xbf3618: b.hs            #0xbf3764
    // 0xbf361c: LoadField: r0 = r4->field_f
    //     0xbf361c: ldur            w0, [x4, #0xf]
    // 0xbf3620: DecompressPointer r0
    //     0xbf3620: add             x0, x0, HEAP, lsl #32
    // 0xbf3624: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbf3624: add             x16, x0, x6, lsl #2
    //     0xbf3628: ldur            w1, [x16, #0xf]
    // 0xbf362c: DecompressPointer r1
    //     0xbf362c: add             x1, x1, HEAP, lsl #32
    // 0xbf3630: LoadField: r0 = r1->field_7
    //     0xbf3630: ldur            w0, [x1, #7]
    // 0xbf3634: DecompressPointer r0
    //     0xbf3634: add             x0, x0, HEAP, lsl #32
    // 0xbf3638: cmp             w0, NULL
    // 0xbf363c: b.ne            #0xbf3648
    // 0xbf3640: r0 = Null
    //     0xbf3640: mov             x0, NULL
    // 0xbf3644: b               #0xbf3654
    // 0xbf3648: LoadField: r1 = r0->field_b
    //     0xbf3648: ldur            w1, [x0, #0xb]
    // 0xbf364c: DecompressPointer r1
    //     0xbf364c: add             x1, x1, HEAP, lsl #32
    // 0xbf3650: mov             x0, x1
    // 0xbf3654: cmp             w0, NULL
    // 0xbf3658: b.ne            #0xbf36b0
    // 0xbf365c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbf365c: ldur            w0, [x3, #0x17]
    // 0xbf3660: DecompressPointer r0
    //     0xbf3660: add             x0, x0, HEAP, lsl #32
    // 0xbf3664: cmp             w0, NULL
    // 0xbf3668: b.ne            #0xbf3674
    // 0xbf366c: r0 = Null
    //     0xbf366c: mov             x0, NULL
    // 0xbf3670: b               #0xbf36b0
    // 0xbf3674: r1 = LoadClassIdInstr(r0)
    //     0xbf3674: ldur            x1, [x0, #-1]
    //     0xbf3678: ubfx            x1, x1, #0xc, #0x14
    // 0xbf367c: stp             x5, x0, [SP]
    // 0xbf3680: mov             x0, x1
    // 0xbf3684: r0 = GDT[cid_x0 + -0xb7]()
    //     0xbf3684: sub             lr, x0, #0xb7
    //     0xbf3688: ldr             lr, [x21, lr, lsl #3]
    //     0xbf368c: blr             lr
    // 0xbf3690: LoadField: r1 = r0->field_2b
    //     0xbf3690: ldur            w1, [x0, #0x2b]
    // 0xbf3694: DecompressPointer r1
    //     0xbf3694: add             x1, x1, HEAP, lsl #32
    // 0xbf3698: cmp             w1, NULL
    // 0xbf369c: b.ne            #0xbf36a8
    // 0xbf36a0: r0 = Null
    //     0xbf36a0: mov             x0, NULL
    // 0xbf36a4: b               #0xbf36b0
    // 0xbf36a8: LoadField: r0 = r1->field_b
    //     0xbf36a8: ldur            w0, [x1, #0xb]
    // 0xbf36ac: DecompressPointer r0
    //     0xbf36ac: add             x0, x0, HEAP, lsl #32
    // 0xbf36b0: cmp             w0, NULL
    // 0xbf36b4: b.ne            #0xbf36bc
    // 0xbf36b8: r0 = ""
    //     0xbf36b8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf36bc: stur            x0, [fp, #-8]
    // 0xbf36c0: r1 = Function '<anonymous closure>':.
    //     0xbf36c0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53500] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf36c4: ldr             x1, [x1, #0x500]
    // 0xbf36c8: r2 = Null
    //     0xbf36c8: mov             x2, NULL
    // 0xbf36cc: r0 = AllocateClosure()
    //     0xbf36cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf36d0: r1 = Function '<anonymous closure>':.
    //     0xbf36d0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53508] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf36d4: ldr             x1, [x1, #0x508]
    // 0xbf36d8: r2 = Null
    //     0xbf36d8: mov             x2, NULL
    // 0xbf36dc: stur            x0, [fp, #-0x18]
    // 0xbf36e0: r0 = AllocateClosure()
    //     0xbf36e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf36e4: stur            x0, [fp, #-0x20]
    // 0xbf36e8: r0 = CachedNetworkImage()
    //     0xbf36e8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf36ec: stur            x0, [fp, #-0x28]
    // 0xbf36f0: r16 = inf
    //     0xbf36f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbf36f4: ldr             x16, [x16, #0x9f8]
    // 0xbf36f8: ldur            lr, [fp, #-0x10]
    // 0xbf36fc: stp             lr, x16, [SP, #0x10]
    // 0xbf3700: ldur            x16, [fp, #-0x18]
    // 0xbf3704: ldur            lr, [fp, #-0x20]
    // 0xbf3708: stp             lr, x16, [SP]
    // 0xbf370c: mov             x1, x0
    // 0xbf3710: ldur            x2, [fp, #-8]
    // 0xbf3714: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, httpHeaders, 0x3, progressIndicatorBuilder, 0x4, width, 0x2, null]
    //     0xbf3714: add             x4, PP, #0x53, lsl #12  ; [pp+0x53510] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "httpHeaders", 0x3, "progressIndicatorBuilder", 0x4, "width", 0x2, Null]
    //     0xbf3718: ldr             x4, [x4, #0x510]
    // 0xbf371c: r0 = CachedNetworkImage()
    //     0xbf371c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf3720: r0 = Container()
    //     0xbf3720: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf3724: stur            x0, [fp, #-8]
    // 0xbf3728: r16 = Instance_EdgeInsets
    //     0xbf3728: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbf372c: ldr             x16, [x16, #0x980]
    // 0xbf3730: ldur            lr, [fp, #-0x28]
    // 0xbf3734: stp             lr, x16, [SP]
    // 0xbf3738: mov             x1, x0
    // 0xbf373c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xbf373c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xbf3740: ldr             x4, [x4, #0x30]
    // 0xbf3744: r0 = Container()
    //     0xbf3744: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf3748: ldur            x0, [fp, #-8]
    // 0xbf374c: LeaveFrame
    //     0xbf374c: mov             SP, fp
    //     0xbf3750: ldp             fp, lr, [SP], #0x10
    // 0xbf3754: ret
    //     0xbf3754: ret             
    // 0xbf3758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3758: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf375c: b               #0xbf35b0
    // 0xbf3760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf3760: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf3764: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf3764: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbf3768, size: 0x5c
    // 0xbf3768: EnterFrame
    //     0xbf3768: stp             fp, lr, [SP, #-0x10]!
    //     0xbf376c: mov             fp, SP
    // 0xbf3770: ldr             x0, [fp, #0x18]
    // 0xbf3774: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf3774: ldur            w1, [x0, #0x17]
    // 0xbf3778: DecompressPointer r1
    //     0xbf3778: add             x1, x1, HEAP, lsl #32
    // 0xbf377c: CheckStackOverflow
    //     0xbf377c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3780: cmp             SP, x16
    //     0xbf3784: b.ls            #0xbf37bc
    // 0xbf3788: LoadField: r0 = r1->field_f
    //     0xbf3788: ldur            w0, [x1, #0xf]
    // 0xbf378c: DecompressPointer r0
    //     0xbf378c: add             x0, x0, HEAP, lsl #32
    // 0xbf3790: ldr             x1, [fp, #0x10]
    // 0xbf3794: r2 = LoadInt32Instr(r1)
    //     0xbf3794: sbfx            x2, x1, #1, #0x1f
    //     0xbf3798: tbz             w1, #0, #0xbf37a0
    //     0xbf379c: ldur            x2, [x1, #7]
    // 0xbf37a0: StoreField: r0->field_13 = r2
    //     0xbf37a0: stur            x2, [x0, #0x13]
    // 0xbf37a4: mov             x1, x0
    // 0xbf37a8: r0 = _onImageTapped()
    //     0xbf37a8: bl              #0xa66ffc  ; [package:customer_app/app/presentation/views/line/home/<USER>/testimonial_view_more_images_widget.dart] _TestimonialMoreImagesWidgetState::_onImageTapped
    // 0xbf37ac: r0 = Null
    //     0xbf37ac: mov             x0, NULL
    // 0xbf37b0: LeaveFrame
    //     0xbf37b0: mov             SP, fp
    //     0xbf37b4: ldp             fp, lr, [SP], #0x10
    // 0xbf37b8: ret
    //     0xbf37b8: ret             
    // 0xbf37bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf37bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf37c0: b               #0xbf3788
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88270, size: 0x54
    // 0xc88270: EnterFrame
    //     0xc88270: stp             fp, lr, [SP, #-0x10]!
    //     0xc88274: mov             fp, SP
    // 0xc88278: CheckStackOverflow
    //     0xc88278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc8827c: cmp             SP, x16
    //     0xc88280: b.ls            #0xc882b0
    // 0xc88284: LoadField: r0 = r1->field_1b
    //     0xc88284: ldur            w0, [x1, #0x1b]
    // 0xc88288: DecompressPointer r0
    //     0xc88288: add             x0, x0, HEAP, lsl #32
    // 0xc8828c: r16 = Sentinel
    //     0xc8828c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88290: cmp             w0, w16
    // 0xc88294: b.eq            #0xc882b8
    // 0xc88298: mov             x1, x0
    // 0xc8829c: r0 = dispose()
    //     0xc8829c: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc882a0: r0 = Null
    //     0xc882a0: mov             x0, NULL
    // 0xc882a4: LeaveFrame
    //     0xc882a4: mov             SP, fp
    //     0xc882a8: ldp             fp, lr, [SP], #0x10
    // 0xc882ac: ret
    //     0xc882ac: ret             
    // 0xc882b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc882b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc882b4: b               #0xc88284
    // 0xc882b8: r9 = _pageController
    //     0xc882b8: add             x9, PP, #0x53, lsl #12  ; [pp+0x534b8] Field <_TestimonialMoreImagesWidgetState@1709203245._pageController@1709203245>: late (offset: 0x1c)
    //     0xc882bc: ldr             x9, [x9, #0x4b8]
    // 0xc882c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc882c0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3987, size: 0x1c, field offset: 0xc
//   const constructor, 
class TestimonialMoreImagesWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80c84, size: 0x30
    // 0xc80c84: EnterFrame
    //     0xc80c84: stp             fp, lr, [SP, #-0x10]!
    //     0xc80c88: mov             fp, SP
    // 0xc80c8c: mov             x0, x1
    // 0xc80c90: r1 = <TestimonialMoreImagesWidget>
    //     0xc80c90: add             x1, PP, #0x48, lsl #12  ; [pp+0x483f8] TypeArguments: <TestimonialMoreImagesWidget>
    //     0xc80c94: ldr             x1, [x1, #0x3f8]
    // 0xc80c98: r0 = _TestimonialMoreImagesWidgetState()
    //     0xc80c98: bl              #0xc80cb4  ; Allocate_TestimonialMoreImagesWidgetStateStub -> _TestimonialMoreImagesWidgetState (size=0x20)
    // 0xc80c9c: StoreField: r0->field_13 = rZR
    //     0xc80c9c: stur            xzr, [x0, #0x13]
    // 0xc80ca0: r1 = Sentinel
    //     0xc80ca0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80ca4: StoreField: r0->field_1b = r1
    //     0xc80ca4: stur            w1, [x0, #0x1b]
    // 0xc80ca8: LeaveFrame
    //     0xc80ca8: mov             SP, fp
    //     0xc80cac: ldp             fp, lr, [SP], #0x10
    // 0xc80cb0: ret
    //     0xc80cb0: ret             
  }
}
