// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/order_item_card.dart

// class id: 1049293, size: 0x8
class :: {
}

// class id: 3413, size: 0x14, field offset: 0x14
class _OrderItemCardState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xafd7b8, size: 0x2f0
    // 0xafd7b8: EnterFrame
    //     0xafd7b8: stp             fp, lr, [SP, #-0x10]!
    //     0xafd7bc: mov             fp, SP
    // 0xafd7c0: AllocStack(0x48)
    //     0xafd7c0: sub             SP, SP, #0x48
    // 0xafd7c4: SetupParameters(_OrderItemCardState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xafd7c4: mov             x0, x1
    //     0xafd7c8: stur            x1, [fp, #-8]
    //     0xafd7cc: mov             x1, x2
    //     0xafd7d0: stur            x2, [fp, #-0x10]
    // 0xafd7d4: CheckStackOverflow
    //     0xafd7d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafd7d8: cmp             SP, x16
    //     0xafd7dc: b.ls            #0xafda98
    // 0xafd7e0: r1 = 1
    //     0xafd7e0: movz            x1, #0x1
    // 0xafd7e4: r0 = AllocateContext()
    //     0xafd7e4: bl              #0x16f6108  ; AllocateContextStub
    // 0xafd7e8: mov             x3, x0
    // 0xafd7ec: ldur            x0, [fp, #-8]
    // 0xafd7f0: stur            x3, [fp, #-0x18]
    // 0xafd7f4: StoreField: r3->field_f = r0
    //     0xafd7f4: stur            w0, [x3, #0xf]
    // 0xafd7f8: r1 = Null
    //     0xafd7f8: mov             x1, NULL
    // 0xafd7fc: r2 = 4
    //     0xafd7fc: movz            x2, #0x4
    // 0xafd800: r0 = AllocateArray()
    //     0xafd800: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafd804: r16 = "Ordered on "
    //     0xafd804: add             x16, PP, #0x53, lsl #12  ; [pp+0x533e8] "Ordered on "
    //     0xafd808: ldr             x16, [x16, #0x3e8]
    // 0xafd80c: StoreField: r0->field_f = r16
    //     0xafd80c: stur            w16, [x0, #0xf]
    // 0xafd810: ldur            x1, [fp, #-8]
    // 0xafd814: LoadField: r2 = r1->field_b
    //     0xafd814: ldur            w2, [x1, #0xb]
    // 0xafd818: DecompressPointer r2
    //     0xafd818: add             x2, x2, HEAP, lsl #32
    // 0xafd81c: cmp             w2, NULL
    // 0xafd820: b.eq            #0xafdaa0
    // 0xafd824: LoadField: r3 = r2->field_b
    //     0xafd824: ldur            w3, [x2, #0xb]
    // 0xafd828: DecompressPointer r3
    //     0xafd828: add             x3, x3, HEAP, lsl #32
    // 0xafd82c: LoadField: r2 = r3->field_7
    //     0xafd82c: ldur            w2, [x3, #7]
    // 0xafd830: DecompressPointer r2
    //     0xafd830: add             x2, x2, HEAP, lsl #32
    // 0xafd834: StoreField: r0->field_13 = r2
    //     0xafd834: stur            w2, [x0, #0x13]
    // 0xafd838: str             x0, [SP]
    // 0xafd83c: r0 = _interpolate()
    //     0xafd83c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xafd840: ldur            x1, [fp, #-0x10]
    // 0xafd844: stur            x0, [fp, #-0x10]
    // 0xafd848: r0 = of()
    //     0xafd848: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafd84c: LoadField: r1 = r0->field_87
    //     0xafd84c: ldur            w1, [x0, #0x87]
    // 0xafd850: DecompressPointer r1
    //     0xafd850: add             x1, x1, HEAP, lsl #32
    // 0xafd854: LoadField: r0 = r1->field_2b
    //     0xafd854: ldur            w0, [x1, #0x2b]
    // 0xafd858: DecompressPointer r0
    //     0xafd858: add             x0, x0, HEAP, lsl #32
    // 0xafd85c: r16 = 12.000000
    //     0xafd85c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafd860: ldr             x16, [x16, #0x9e8]
    // 0xafd864: r30 = Instance_Color
    //     0xafd864: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafd868: stp             lr, x16, [SP]
    // 0xafd86c: mov             x1, x0
    // 0xafd870: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafd870: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafd874: ldr             x4, [x4, #0xaa0]
    // 0xafd878: r0 = copyWith()
    //     0xafd878: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafd87c: stur            x0, [fp, #-0x20]
    // 0xafd880: r0 = Text()
    //     0xafd880: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafd884: mov             x1, x0
    // 0xafd888: ldur            x0, [fp, #-0x10]
    // 0xafd88c: stur            x1, [fp, #-0x28]
    // 0xafd890: StoreField: r1->field_b = r0
    //     0xafd890: stur            w0, [x1, #0xb]
    // 0xafd894: ldur            x0, [fp, #-0x20]
    // 0xafd898: StoreField: r1->field_13 = r0
    //     0xafd898: stur            w0, [x1, #0x13]
    // 0xafd89c: r0 = Align()
    //     0xafd89c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xafd8a0: mov             x1, x0
    // 0xafd8a4: r0 = Instance_Alignment
    //     0xafd8a4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xafd8a8: ldr             x0, [x0, #0xfa0]
    // 0xafd8ac: stur            x1, [fp, #-0x10]
    // 0xafd8b0: StoreField: r1->field_f = r0
    //     0xafd8b0: stur            w0, [x1, #0xf]
    // 0xafd8b4: ldur            x0, [fp, #-0x28]
    // 0xafd8b8: StoreField: r1->field_b = r0
    //     0xafd8b8: stur            w0, [x1, #0xb]
    // 0xafd8bc: r0 = Padding()
    //     0xafd8bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafd8c0: mov             x3, x0
    // 0xafd8c4: r0 = Instance_EdgeInsets
    //     0xafd8c4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xafd8c8: ldr             x0, [x0, #0x778]
    // 0xafd8cc: stur            x3, [fp, #-0x20]
    // 0xafd8d0: StoreField: r3->field_f = r0
    //     0xafd8d0: stur            w0, [x3, #0xf]
    // 0xafd8d4: ldur            x0, [fp, #-0x10]
    // 0xafd8d8: StoreField: r3->field_b = r0
    //     0xafd8d8: stur            w0, [x3, #0xb]
    // 0xafd8dc: ldur            x0, [fp, #-8]
    // 0xafd8e0: LoadField: r1 = r0->field_b
    //     0xafd8e0: ldur            w1, [x0, #0xb]
    // 0xafd8e4: DecompressPointer r1
    //     0xafd8e4: add             x1, x1, HEAP, lsl #32
    // 0xafd8e8: cmp             w1, NULL
    // 0xafd8ec: b.eq            #0xafdaa4
    // 0xafd8f0: LoadField: r0 = r1->field_b
    //     0xafd8f0: ldur            w0, [x1, #0xb]
    // 0xafd8f4: DecompressPointer r0
    //     0xafd8f4: add             x0, x0, HEAP, lsl #32
    // 0xafd8f8: LoadField: r1 = r0->field_b
    //     0xafd8f8: ldur            w1, [x0, #0xb]
    // 0xafd8fc: DecompressPointer r1
    //     0xafd8fc: add             x1, x1, HEAP, lsl #32
    // 0xafd900: cmp             w1, NULL
    // 0xafd904: b.ne            #0xafd910
    // 0xafd908: r0 = Null
    //     0xafd908: mov             x0, NULL
    // 0xafd90c: b               #0xafd914
    // 0xafd910: LoadField: r0 = r1->field_b
    //     0xafd910: ldur            w0, [x1, #0xb]
    // 0xafd914: cmp             w0, NULL
    // 0xafd918: b.ne            #0xafd924
    // 0xafd91c: r0 = 0
    //     0xafd91c: movz            x0, #0
    // 0xafd920: b               #0xafd92c
    // 0xafd924: r1 = LoadInt32Instr(r0)
    //     0xafd924: sbfx            x1, x0, #1, #0x1f
    // 0xafd928: mov             x0, x1
    // 0xafd92c: ldur            x2, [fp, #-0x18]
    // 0xafd930: stur            x0, [fp, #-0x30]
    // 0xafd934: r1 = Function '<anonymous closure>':.
    //     0xafd934: add             x1, PP, #0x57, lsl #12  ; [pp+0x57fa8] AnonymousClosure: (0xafdaa8), in [package:customer_app/app/presentation/views/cosmetic/orders/order_item_card.dart] _OrderItemCardState::build (0xafd7b8)
    //     0xafd938: ldr             x1, [x1, #0xfa8]
    // 0xafd93c: r0 = AllocateClosure()
    //     0xafd93c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafd940: r1 = Function '<anonymous closure>':.
    //     0xafd940: add             x1, PP, #0x57, lsl #12  ; [pp+0x57fb0] AnonymousClosure: (0x9bccb4), in [package:customer_app/app/presentation/views/line/post_order/order_success/order_success_widget.dart] OrderSuccessWidget::body (0x1506c94)
    //     0xafd944: ldr             x1, [x1, #0xfb0]
    // 0xafd948: r2 = Null
    //     0xafd948: mov             x2, NULL
    // 0xafd94c: stur            x0, [fp, #-8]
    // 0xafd950: r0 = AllocateClosure()
    //     0xafd950: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafd954: stur            x0, [fp, #-0x10]
    // 0xafd958: r0 = ListView()
    //     0xafd958: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xafd95c: stur            x0, [fp, #-0x18]
    // 0xafd960: r16 = Instance_Axis
    //     0xafd960: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafd964: r30 = true
    //     0xafd964: add             lr, NULL, #0x20  ; true
    // 0xafd968: stp             lr, x16, [SP, #8]
    // 0xafd96c: r16 = false
    //     0xafd96c: add             x16, NULL, #0x30  ; false
    // 0xafd970: str             x16, [SP]
    // 0xafd974: mov             x1, x0
    // 0xafd978: ldur            x2, [fp, #-8]
    // 0xafd97c: ldur            x3, [fp, #-0x30]
    // 0xafd980: ldur            x5, [fp, #-0x10]
    // 0xafd984: r4 = const [0, 0x7, 0x3, 0x4, primary, 0x6, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0xafd984: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e528] List(11) [0, 0x7, 0x3, 0x4, "primary", 0x6, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0xafd988: ldr             x4, [x4, #0x528]
    // 0xafd98c: r0 = ListView.separated()
    //     0xafd98c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xafd990: r1 = <FlexParentData>
    //     0xafd990: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xafd994: ldr             x1, [x1, #0xe00]
    // 0xafd998: r0 = Flexible()
    //     0xafd998: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xafd99c: mov             x3, x0
    // 0xafd9a0: r0 = 1
    //     0xafd9a0: movz            x0, #0x1
    // 0xafd9a4: stur            x3, [fp, #-8]
    // 0xafd9a8: StoreField: r3->field_13 = r0
    //     0xafd9a8: stur            x0, [x3, #0x13]
    // 0xafd9ac: r0 = Instance_FlexFit
    //     0xafd9ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xafd9b0: ldr             x0, [x0, #0xe20]
    // 0xafd9b4: StoreField: r3->field_1b = r0
    //     0xafd9b4: stur            w0, [x3, #0x1b]
    // 0xafd9b8: ldur            x0, [fp, #-0x18]
    // 0xafd9bc: StoreField: r3->field_b = r0
    //     0xafd9bc: stur            w0, [x3, #0xb]
    // 0xafd9c0: r1 = Null
    //     0xafd9c0: mov             x1, NULL
    // 0xafd9c4: r2 = 4
    //     0xafd9c4: movz            x2, #0x4
    // 0xafd9c8: r0 = AllocateArray()
    //     0xafd9c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafd9cc: mov             x2, x0
    // 0xafd9d0: ldur            x0, [fp, #-0x20]
    // 0xafd9d4: stur            x2, [fp, #-0x10]
    // 0xafd9d8: StoreField: r2->field_f = r0
    //     0xafd9d8: stur            w0, [x2, #0xf]
    // 0xafd9dc: ldur            x0, [fp, #-8]
    // 0xafd9e0: StoreField: r2->field_13 = r0
    //     0xafd9e0: stur            w0, [x2, #0x13]
    // 0xafd9e4: r1 = <Widget>
    //     0xafd9e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafd9e8: r0 = AllocateGrowableArray()
    //     0xafd9e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafd9ec: mov             x1, x0
    // 0xafd9f0: ldur            x0, [fp, #-0x10]
    // 0xafd9f4: stur            x1, [fp, #-8]
    // 0xafd9f8: StoreField: r1->field_f = r0
    //     0xafd9f8: stur            w0, [x1, #0xf]
    // 0xafd9fc: r0 = 4
    //     0xafd9fc: movz            x0, #0x4
    // 0xafda00: StoreField: r1->field_b = r0
    //     0xafda00: stur            w0, [x1, #0xb]
    // 0xafda04: r0 = Column()
    //     0xafda04: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafda08: mov             x1, x0
    // 0xafda0c: r0 = Instance_Axis
    //     0xafda0c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafda10: stur            x1, [fp, #-0x10]
    // 0xafda14: StoreField: r1->field_f = r0
    //     0xafda14: stur            w0, [x1, #0xf]
    // 0xafda18: r0 = Instance_MainAxisAlignment
    //     0xafda18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafda1c: ldr             x0, [x0, #0xa08]
    // 0xafda20: StoreField: r1->field_13 = r0
    //     0xafda20: stur            w0, [x1, #0x13]
    // 0xafda24: r0 = Instance_MainAxisSize
    //     0xafda24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xafda28: ldr             x0, [x0, #0xdd0]
    // 0xafda2c: ArrayStore: r1[0] = r0  ; List_4
    //     0xafda2c: stur            w0, [x1, #0x17]
    // 0xafda30: r0 = Instance_CrossAxisAlignment
    //     0xafda30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafda34: ldr             x0, [x0, #0xa18]
    // 0xafda38: StoreField: r1->field_1b = r0
    //     0xafda38: stur            w0, [x1, #0x1b]
    // 0xafda3c: r0 = Instance_VerticalDirection
    //     0xafda3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafda40: ldr             x0, [x0, #0xa20]
    // 0xafda44: StoreField: r1->field_23 = r0
    //     0xafda44: stur            w0, [x1, #0x23]
    // 0xafda48: r0 = Instance_Clip
    //     0xafda48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafda4c: ldr             x0, [x0, #0x38]
    // 0xafda50: StoreField: r1->field_2b = r0
    //     0xafda50: stur            w0, [x1, #0x2b]
    // 0xafda54: StoreField: r1->field_2f = rZR
    //     0xafda54: stur            xzr, [x1, #0x2f]
    // 0xafda58: ldur            x0, [fp, #-8]
    // 0xafda5c: StoreField: r1->field_b = r0
    //     0xafda5c: stur            w0, [x1, #0xb]
    // 0xafda60: r0 = Container()
    //     0xafda60: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafda64: stur            x0, [fp, #-8]
    // 0xafda68: r16 = Instance_EdgeInsets
    //     0xafda68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xafda6c: ldr             x16, [x16, #0xb0]
    // 0xafda70: ldur            lr, [fp, #-0x10]
    // 0xafda74: stp             lr, x16, [SP]
    // 0xafda78: mov             x1, x0
    // 0xafda7c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0xafda7c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0xafda80: ldr             x4, [x4, #0x400]
    // 0xafda84: r0 = Container()
    //     0xafda84: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafda88: ldur            x0, [fp, #-8]
    // 0xafda8c: LeaveFrame
    //     0xafda8c: mov             SP, fp
    //     0xafda90: ldp             fp, lr, [SP], #0x10
    // 0xafda94: ret
    //     0xafda94: ret             
    // 0xafda98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafda98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafda9c: b               #0xafd7e0
    // 0xafdaa0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafdaa0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafdaa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafdaa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xafdaa8, size: 0x13d8
    // 0xafdaa8: EnterFrame
    //     0xafdaa8: stp             fp, lr, [SP, #-0x10]!
    //     0xafdaac: mov             fp, SP
    // 0xafdab0: AllocStack(0x80)
    //     0xafdab0: sub             SP, SP, #0x80
    // 0xafdab4: SetupParameters()
    //     0xafdab4: ldr             x0, [fp, #0x20]
    //     0xafdab8: ldur            w2, [x0, #0x17]
    //     0xafdabc: add             x2, x2, HEAP, lsl #32
    //     0xafdac0: stur            x2, [fp, #-0x48]
    // 0xafdac4: CheckStackOverflow
    //     0xafdac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafdac8: cmp             SP, x16
    //     0xafdacc: b.ls            #0xafee34
    // 0xafdad0: LoadField: r0 = r2->field_f
    //     0xafdad0: ldur            w0, [x2, #0xf]
    // 0xafdad4: DecompressPointer r0
    //     0xafdad4: add             x0, x0, HEAP, lsl #32
    // 0xafdad8: LoadField: r3 = r0->field_b
    //     0xafdad8: ldur            w3, [x0, #0xb]
    // 0xafdadc: DecompressPointer r3
    //     0xafdadc: add             x3, x3, HEAP, lsl #32
    // 0xafdae0: cmp             w3, NULL
    // 0xafdae4: b.eq            #0xafee3c
    // 0xafdae8: LoadField: r4 = r3->field_1f
    //     0xafdae8: ldur            w4, [x3, #0x1f]
    // 0xafdaec: DecompressPointer r4
    //     0xafdaec: add             x4, x4, HEAP, lsl #32
    // 0xafdaf0: stur            x4, [fp, #-0x40]
    // 0xafdaf4: LoadField: r5 = r3->field_f
    //     0xafdaf4: ldur            w5, [x3, #0xf]
    // 0xafdaf8: DecompressPointer r5
    //     0xafdaf8: add             x5, x5, HEAP, lsl #32
    // 0xafdafc: stur            x5, [fp, #-0x38]
    // 0xafdb00: LoadField: r6 = r3->field_13
    //     0xafdb00: ldur            w6, [x3, #0x13]
    // 0xafdb04: DecompressPointer r6
    //     0xafdb04: add             x6, x6, HEAP, lsl #32
    // 0xafdb08: stur            x6, [fp, #-0x30]
    // 0xafdb0c: LoadField: r0 = r3->field_b
    //     0xafdb0c: ldur            w0, [x3, #0xb]
    // 0xafdb10: DecompressPointer r0
    //     0xafdb10: add             x0, x0, HEAP, lsl #32
    // 0xafdb14: LoadField: r7 = r0->field_b
    //     0xafdb14: ldur            w7, [x0, #0xb]
    // 0xafdb18: DecompressPointer r7
    //     0xafdb18: add             x7, x7, HEAP, lsl #32
    // 0xafdb1c: stur            x7, [fp, #-0x28]
    // 0xafdb20: cmp             w7, NULL
    // 0xafdb24: b.eq            #0xafee40
    // 0xafdb28: LoadField: r0 = r7->field_b
    //     0xafdb28: ldur            w0, [x7, #0xb]
    // 0xafdb2c: ldr             x1, [fp, #0x10]
    // 0xafdb30: r8 = LoadInt32Instr(r1)
    //     0xafdb30: sbfx            x8, x1, #1, #0x1f
    //     0xafdb34: tbz             w1, #0, #0xafdb3c
    //     0xafdb38: ldur            x8, [x1, #7]
    // 0xafdb3c: stur            x8, [fp, #-0x20]
    // 0xafdb40: r1 = LoadInt32Instr(r0)
    //     0xafdb40: sbfx            x1, x0, #1, #0x1f
    // 0xafdb44: mov             x0, x1
    // 0xafdb48: mov             x1, x8
    // 0xafdb4c: cmp             x1, x0
    // 0xafdb50: b.hs            #0xafee44
    // 0xafdb54: LoadField: r0 = r7->field_f
    //     0xafdb54: ldur            w0, [x7, #0xf]
    // 0xafdb58: DecompressPointer r0
    //     0xafdb58: add             x0, x0, HEAP, lsl #32
    // 0xafdb5c: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xafdb5c: add             x16, x0, x8, lsl #2
    //     0xafdb60: ldur            w1, [x16, #0xf]
    // 0xafdb64: DecompressPointer r1
    //     0xafdb64: add             x1, x1, HEAP, lsl #32
    // 0xafdb68: stur            x1, [fp, #-0x18]
    // 0xafdb6c: cmp             w1, NULL
    // 0xafdb70: b.eq            #0xafee48
    // 0xafdb74: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xafdb74: ldur            w0, [x3, #0x17]
    // 0xafdb78: DecompressPointer r0
    //     0xafdb78: add             x0, x0, HEAP, lsl #32
    // 0xafdb7c: stur            x0, [fp, #-0x10]
    // 0xafdb80: LoadField: r9 = r3->field_1b
    //     0xafdb80: ldur            w9, [x3, #0x1b]
    // 0xafdb84: DecompressPointer r9
    //     0xafdb84: add             x9, x9, HEAP, lsl #32
    // 0xafdb88: stur            x9, [fp, #-8]
    // 0xafdb8c: r0 = OrderCard()
    //     0xafdb8c: bl              #0xafee80  ; AllocateOrderCardStub -> OrderCard (size=0x30)
    // 0xafdb90: mov             x3, x0
    // 0xafdb94: ldur            x0, [fp, #-0x18]
    // 0xafdb98: stur            x3, [fp, #-0x50]
    // 0xafdb9c: StoreField: r3->field_b = r0
    //     0xafdb9c: stur            w0, [x3, #0xb]
    // 0xafdba0: r1 = "order_card"
    //     0xafdba0: add             x1, PP, #0x36, lsl #12  ; [pp+0x369a0] "order_card"
    //     0xafdba4: ldr             x1, [x1, #0x9a0]
    // 0xafdba8: StoreField: r3->field_f = r1
    //     0xafdba8: stur            w1, [x3, #0xf]
    // 0xafdbac: ldur            x1, [fp, #-0x38]
    // 0xafdbb0: StoreField: r3->field_13 = r1
    //     0xafdbb0: stur            w1, [x3, #0x13]
    // 0xafdbb4: ldur            x1, [fp, #-0x30]
    // 0xafdbb8: ArrayStore: r3[0] = r1  ; List_4
    //     0xafdbb8: stur            w1, [x3, #0x17]
    // 0xafdbbc: ldur            x1, [fp, #-0x10]
    // 0xafdbc0: StoreField: r3->field_1b = r1
    //     0xafdbc0: stur            w1, [x3, #0x1b]
    // 0xafdbc4: ldur            x1, [fp, #-8]
    // 0xafdbc8: StoreField: r3->field_1f = r1
    //     0xafdbc8: stur            w1, [x3, #0x1f]
    // 0xafdbcc: ldur            x2, [fp, #-0x48]
    // 0xafdbd0: r1 = Function '<anonymous closure>':.
    //     0xafdbd0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57fb8] AnonymousClosure: (0xafef08), in [package:customer_app/app/presentation/views/cosmetic/orders/order_item_card.dart] _OrderItemCardState::build (0xafd7b8)
    //     0xafdbd4: ldr             x1, [x1, #0xfb8]
    // 0xafdbd8: r0 = AllocateClosure()
    //     0xafdbd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafdbdc: mov             x1, x0
    // 0xafdbe0: ldur            x0, [fp, #-0x50]
    // 0xafdbe4: StoreField: r0->field_2b = r1
    //     0xafdbe4: stur            w1, [x0, #0x2b]
    // 0xafdbe8: ldur            x2, [fp, #-0x48]
    // 0xafdbec: r1 = Function '<anonymous closure>':.
    //     0xafdbec: add             x1, PP, #0x57, lsl #12  ; [pp+0x57fc0] AnonymousClosure: (0xafee8c), in [package:customer_app/app/presentation/views/cosmetic/orders/order_item_card.dart] _OrderItemCardState::build (0xafd7b8)
    //     0xafdbf0: ldr             x1, [x1, #0xfc0]
    // 0xafdbf4: r0 = AllocateClosure()
    //     0xafdbf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafdbf8: ldur            x2, [fp, #-0x50]
    // 0xafdbfc: StoreField: r2->field_23 = r0
    //     0xafdbfc: stur            w0, [x2, #0x23]
    // 0xafdc00: ldur            x0, [fp, #-0x40]
    // 0xafdc04: StoreField: r2->field_27 = r0
    //     0xafdc04: stur            w0, [x2, #0x27]
    // 0xafdc08: ldur            x0, [fp, #-0x18]
    // 0xafdc0c: cmp             w0, NULL
    // 0xafdc10: b.ne            #0xafdc1c
    // 0xafdc14: r0 = Null
    //     0xafdc14: mov             x0, NULL
    // 0xafdc18: b               #0xafdc28
    // 0xafdc1c: LoadField: r1 = r0->field_7f
    //     0xafdc1c: ldur            w1, [x0, #0x7f]
    // 0xafdc20: DecompressPointer r1
    //     0xafdc20: add             x1, x1, HEAP, lsl #32
    // 0xafdc24: mov             x0, x1
    // 0xafdc28: ldur            x3, [fp, #-0x28]
    // 0xafdc2c: cmp             w0, NULL
    // 0xafdc30: r16 = true
    //     0xafdc30: add             x16, NULL, #0x20  ; true
    // 0xafdc34: r17 = false
    //     0xafdc34: add             x17, NULL, #0x30  ; false
    // 0xafdc38: csel            x4, x16, x17, ne
    // 0xafdc3c: stur            x4, [fp, #-8]
    // 0xafdc40: cmp             w3, NULL
    // 0xafdc44: b.ne            #0xafdc54
    // 0xafdc48: ldur            x5, [fp, #-0x20]
    // 0xafdc4c: r0 = Null
    //     0xafdc4c: mov             x0, NULL
    // 0xafdc50: b               #0xafdcb8
    // 0xafdc54: ldur            x5, [fp, #-0x20]
    // 0xafdc58: LoadField: r0 = r3->field_b
    //     0xafdc58: ldur            w0, [x3, #0xb]
    // 0xafdc5c: r1 = LoadInt32Instr(r0)
    //     0xafdc5c: sbfx            x1, x0, #1, #0x1f
    // 0xafdc60: mov             x0, x1
    // 0xafdc64: mov             x1, x5
    // 0xafdc68: cmp             x1, x0
    // 0xafdc6c: b.hs            #0xafee4c
    // 0xafdc70: LoadField: r0 = r3->field_f
    //     0xafdc70: ldur            w0, [x3, #0xf]
    // 0xafdc74: DecompressPointer r0
    //     0xafdc74: add             x0, x0, HEAP, lsl #32
    // 0xafdc78: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xafdc78: add             x16, x0, x5, lsl #2
    //     0xafdc7c: ldur            w1, [x16, #0xf]
    // 0xafdc80: DecompressPointer r1
    //     0xafdc80: add             x1, x1, HEAP, lsl #32
    // 0xafdc84: cmp             w1, NULL
    // 0xafdc88: b.ne            #0xafdc94
    // 0xafdc8c: r0 = Null
    //     0xafdc8c: mov             x0, NULL
    // 0xafdc90: b               #0xafdcb8
    // 0xafdc94: LoadField: r0 = r1->field_7f
    //     0xafdc94: ldur            w0, [x1, #0x7f]
    // 0xafdc98: DecompressPointer r0
    //     0xafdc98: add             x0, x0, HEAP, lsl #32
    // 0xafdc9c: cmp             w0, NULL
    // 0xafdca0: b.ne            #0xafdcac
    // 0xafdca4: r0 = Null
    //     0xafdca4: mov             x0, NULL
    // 0xafdca8: b               #0xafdcb8
    // 0xafdcac: LoadField: r1 = r0->field_2b
    //     0xafdcac: ldur            w1, [x0, #0x2b]
    // 0xafdcb0: DecompressPointer r1
    //     0xafdcb0: add             x1, x1, HEAP, lsl #32
    // 0xafdcb4: mov             x0, x1
    // 0xafdcb8: cmp             w0, NULL
    // 0xafdcbc: b.ne            #0xafdd2c
    // 0xafdcc0: ldur            x7, [fp, #-0x48]
    // 0xafdcc4: mov             x8, x5
    // 0xafdcc8: r4 = 2
    //     0xafdcc8: movz            x4, #0x2
    // 0xafdccc: r6 = "Free"
    //     0xafdccc: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xafdcd0: ldr             x6, [x6, #0x668]
    // 0xafdcd4: r5 = 150.000000
    //     0xafdcd4: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xafdcd8: ldr             x5, [x5, #0x690]
    // 0xafdcdc: r3 = Instance_TextOverflow
    //     0xafdcdc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xafdce0: ldr             x3, [x3, #0xe10]
    // 0xafdce4: r9 = Instance_CrossAxisAlignment
    //     0xafdce4: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafdce8: ldr             x9, [x9, #0x890]
    // 0xafdcec: r10 = Instance_EdgeInsets
    //     0xafdcec: add             x10, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xafdcf0: ldr             x10, [x10, #0xa78]
    // 0xafdcf4: r19 = Instance_EdgeInsets
    //     0xafdcf4: add             x19, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xafdcf8: ldr             x19, [x19, #0x770]
    // 0xafdcfc: r13 = 6
    //     0xafdcfc: movz            x13, #0x6
    // 0xafdd00: r14 = Instance_Axis
    //     0xafdd00: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafdd04: r12 = Instance_FlexFit
    //     0xafdd04: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xafdd08: ldr             x12, [x12, #0xe08]
    // 0xafdd0c: r0 = Instance_BoxShape
    //     0xafdd0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafdd10: ldr             x0, [x0, #0x80]
    // 0xafdd14: r1 = Instance_Alignment
    //     0xafdd14: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xafdd18: ldr             x1, [x1, #0xb10]
    // 0xafdd1c: r2 = -1
    //     0xafdd1c: movn            x2, #0
    // 0xafdd20: d0 = 12.000000
    //     0xafdd20: fmov            d0, #12.00000000
    // 0xafdd24: r11 = 1
    //     0xafdd24: movz            x11, #0x1
    // 0xafdd28: b               #0xafe534
    // 0xafdd2c: tbnz            w0, #4, #0xafe4cc
    // 0xafdd30: ldur            x0, [fp, #-0x48]
    // 0xafdd34: r0 = Radius()
    //     0xafdd34: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafdd38: d0 = 12.000000
    //     0xafdd38: fmov            d0, #12.00000000
    // 0xafdd3c: stur            x0, [fp, #-0x10]
    // 0xafdd40: StoreField: r0->field_7 = d0
    //     0xafdd40: stur            d0, [x0, #7]
    // 0xafdd44: StoreField: r0->field_f = d0
    //     0xafdd44: stur            d0, [x0, #0xf]
    // 0xafdd48: r0 = BorderRadius()
    //     0xafdd48: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafdd4c: mov             x2, x0
    // 0xafdd50: ldur            x0, [fp, #-0x10]
    // 0xafdd54: stur            x2, [fp, #-0x18]
    // 0xafdd58: StoreField: r2->field_7 = r0
    //     0xafdd58: stur            w0, [x2, #7]
    // 0xafdd5c: StoreField: r2->field_b = r0
    //     0xafdd5c: stur            w0, [x2, #0xb]
    // 0xafdd60: StoreField: r2->field_f = r0
    //     0xafdd60: stur            w0, [x2, #0xf]
    // 0xafdd64: StoreField: r2->field_13 = r0
    //     0xafdd64: stur            w0, [x2, #0x13]
    // 0xafdd68: r1 = Instance_Color
    //     0xafdd68: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafdd6c: d0 = 0.070000
    //     0xafdd6c: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xafdd70: ldr             d0, [x17, #0x5f8]
    // 0xafdd74: r0 = withOpacity()
    //     0xafdd74: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xafdd78: r16 = 1.000000
    //     0xafdd78: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xafdd7c: str             x16, [SP]
    // 0xafdd80: mov             x2, x0
    // 0xafdd84: r1 = Null
    //     0xafdd84: mov             x1, NULL
    // 0xafdd88: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xafdd88: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xafdd8c: ldr             x4, [x4, #0x108]
    // 0xafdd90: r0 = Border.all()
    //     0xafdd90: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xafdd94: stur            x0, [fp, #-0x10]
    // 0xafdd98: r0 = BoxDecoration()
    //     0xafdd98: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xafdd9c: mov             x2, x0
    // 0xafdda0: ldur            x0, [fp, #-0x10]
    // 0xafdda4: stur            x2, [fp, #-0x28]
    // 0xafdda8: StoreField: r2->field_f = r0
    //     0xafdda8: stur            w0, [x2, #0xf]
    // 0xafddac: ldur            x0, [fp, #-0x18]
    // 0xafddb0: StoreField: r2->field_13 = r0
    //     0xafddb0: stur            w0, [x2, #0x13]
    // 0xafddb4: r0 = Instance_LinearGradient
    //     0xafddb4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xafddb8: ldr             x0, [x0, #0x660]
    // 0xafddbc: StoreField: r2->field_1b = r0
    //     0xafddbc: stur            w0, [x2, #0x1b]
    // 0xafddc0: r0 = Instance_BoxShape
    //     0xafddc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafddc4: ldr             x0, [x0, #0x80]
    // 0xafddc8: StoreField: r2->field_23 = r0
    //     0xafddc8: stur            w0, [x2, #0x23]
    // 0xafddcc: ldr             x1, [fp, #0x18]
    // 0xafddd0: r0 = of()
    //     0xafddd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafddd4: LoadField: r1 = r0->field_87
    //     0xafddd4: ldur            w1, [x0, #0x87]
    // 0xafddd8: DecompressPointer r1
    //     0xafddd8: add             x1, x1, HEAP, lsl #32
    // 0xafdddc: LoadField: r0 = r1->field_7
    //     0xafdddc: ldur            w0, [x1, #7]
    // 0xafdde0: DecompressPointer r0
    //     0xafdde0: add             x0, x0, HEAP, lsl #32
    // 0xafdde4: r16 = 12.000000
    //     0xafdde4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafdde8: ldr             x16, [x16, #0x9e8]
    // 0xafddec: r30 = Instance_Color
    //     0xafddec: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xafddf0: stp             lr, x16, [SP]
    // 0xafddf4: mov             x1, x0
    // 0xafddf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafddf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafddfc: ldr             x4, [x4, #0xaa0]
    // 0xafde00: r0 = copyWith()
    //     0xafde00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafde04: stur            x0, [fp, #-0x10]
    // 0xafde08: r0 = Text()
    //     0xafde08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafde0c: mov             x1, x0
    // 0xafde10: r0 = "Free"
    //     0xafde10: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xafde14: ldr             x0, [x0, #0x668]
    // 0xafde18: stur            x1, [fp, #-0x18]
    // 0xafde1c: StoreField: r1->field_b = r0
    //     0xafde1c: stur            w0, [x1, #0xb]
    // 0xafde20: ldur            x2, [fp, #-0x10]
    // 0xafde24: StoreField: r1->field_13 = r2
    //     0xafde24: stur            w2, [x1, #0x13]
    // 0xafde28: r0 = Center()
    //     0xafde28: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xafde2c: r1 = Instance_Alignment
    //     0xafde2c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xafde30: ldr             x1, [x1, #0xb10]
    // 0xafde34: stur            x0, [fp, #-0x10]
    // 0xafde38: StoreField: r0->field_f = r1
    //     0xafde38: stur            w1, [x0, #0xf]
    // 0xafde3c: ldur            x1, [fp, #-0x18]
    // 0xafde40: StoreField: r0->field_b = r1
    //     0xafde40: stur            w1, [x0, #0xb]
    // 0xafde44: r0 = RotatedBox()
    //     0xafde44: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xafde48: r2 = -1
    //     0xafde48: movn            x2, #0
    // 0xafde4c: stur            x0, [fp, #-0x18]
    // 0xafde50: StoreField: r0->field_f = r2
    //     0xafde50: stur            x2, [x0, #0xf]
    // 0xafde54: ldur            x1, [fp, #-0x10]
    // 0xafde58: StoreField: r0->field_b = r1
    //     0xafde58: stur            w1, [x0, #0xb]
    // 0xafde5c: r0 = Container()
    //     0xafde5c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafde60: stur            x0, [fp, #-0x10]
    // 0xafde64: r16 = 24.000000
    //     0xafde64: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xafde68: ldr             x16, [x16, #0xba8]
    // 0xafde6c: r30 = 56.000000
    //     0xafde6c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafde70: ldr             lr, [lr, #0xb78]
    // 0xafde74: stp             lr, x16, [SP, #0x10]
    // 0xafde78: r16 = Instance_BoxDecoration
    //     0xafde78: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0xafde7c: ldr             x16, [x16, #0xdb0]
    // 0xafde80: ldur            lr, [fp, #-0x18]
    // 0xafde84: stp             lr, x16, [SP]
    // 0xafde88: mov             x1, x0
    // 0xafde8c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xafde8c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xafde90: ldr             x4, [x4, #0x870]
    // 0xafde94: r0 = Container()
    //     0xafde94: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafde98: ldur            x2, [fp, #-0x48]
    // 0xafde9c: LoadField: r0 = r2->field_f
    //     0xafde9c: ldur            w0, [x2, #0xf]
    // 0xafdea0: DecompressPointer r0
    //     0xafdea0: add             x0, x0, HEAP, lsl #32
    // 0xafdea4: LoadField: r1 = r0->field_b
    //     0xafdea4: ldur            w1, [x0, #0xb]
    // 0xafdea8: DecompressPointer r1
    //     0xafdea8: add             x1, x1, HEAP, lsl #32
    // 0xafdeac: cmp             w1, NULL
    // 0xafdeb0: b.eq            #0xafee50
    // 0xafdeb4: LoadField: r0 = r1->field_b
    //     0xafdeb4: ldur            w0, [x1, #0xb]
    // 0xafdeb8: DecompressPointer r0
    //     0xafdeb8: add             x0, x0, HEAP, lsl #32
    // 0xafdebc: LoadField: r3 = r0->field_b
    //     0xafdebc: ldur            w3, [x0, #0xb]
    // 0xafdec0: DecompressPointer r3
    //     0xafdec0: add             x3, x3, HEAP, lsl #32
    // 0xafdec4: cmp             w3, NULL
    // 0xafdec8: b.ne            #0xafded8
    // 0xafdecc: ldur            x4, [fp, #-0x20]
    // 0xafded0: r0 = Null
    //     0xafded0: mov             x0, NULL
    // 0xafded4: b               #0xafdf3c
    // 0xafded8: ldur            x4, [fp, #-0x20]
    // 0xafdedc: LoadField: r0 = r3->field_b
    //     0xafdedc: ldur            w0, [x3, #0xb]
    // 0xafdee0: r1 = LoadInt32Instr(r0)
    //     0xafdee0: sbfx            x1, x0, #1, #0x1f
    // 0xafdee4: mov             x0, x1
    // 0xafdee8: mov             x1, x4
    // 0xafdeec: cmp             x1, x0
    // 0xafdef0: b.hs            #0xafee54
    // 0xafdef4: LoadField: r0 = r3->field_f
    //     0xafdef4: ldur            w0, [x3, #0xf]
    // 0xafdef8: DecompressPointer r0
    //     0xafdef8: add             x0, x0, HEAP, lsl #32
    // 0xafdefc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafdefc: add             x16, x0, x4, lsl #2
    //     0xafdf00: ldur            w1, [x16, #0xf]
    // 0xafdf04: DecompressPointer r1
    //     0xafdf04: add             x1, x1, HEAP, lsl #32
    // 0xafdf08: cmp             w1, NULL
    // 0xafdf0c: b.ne            #0xafdf18
    // 0xafdf10: r0 = Null
    //     0xafdf10: mov             x0, NULL
    // 0xafdf14: b               #0xafdf3c
    // 0xafdf18: LoadField: r0 = r1->field_7f
    //     0xafdf18: ldur            w0, [x1, #0x7f]
    // 0xafdf1c: DecompressPointer r0
    //     0xafdf1c: add             x0, x0, HEAP, lsl #32
    // 0xafdf20: cmp             w0, NULL
    // 0xafdf24: b.ne            #0xafdf30
    // 0xafdf28: r0 = Null
    //     0xafdf28: mov             x0, NULL
    // 0xafdf2c: b               #0xafdf3c
    // 0xafdf30: LoadField: r1 = r0->field_7
    //     0xafdf30: ldur            w1, [x0, #7]
    // 0xafdf34: DecompressPointer r1
    //     0xafdf34: add             x1, x1, HEAP, lsl #32
    // 0xafdf38: mov             x0, x1
    // 0xafdf3c: cmp             w0, NULL
    // 0xafdf40: b.ne            #0xafdf48
    // 0xafdf44: r0 = ""
    //     0xafdf44: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafdf48: stur            x0, [fp, #-0x18]
    // 0xafdf4c: r0 = CachedNetworkImage()
    //     0xafdf4c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xafdf50: stur            x0, [fp, #-0x30]
    // 0xafdf54: r16 = 56.000000
    //     0xafdf54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafdf58: ldr             x16, [x16, #0xb78]
    // 0xafdf5c: r30 = 56.000000
    //     0xafdf5c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafdf60: ldr             lr, [lr, #0xb78]
    // 0xafdf64: stp             lr, x16, [SP, #8]
    // 0xafdf68: r16 = Instance_BoxFit
    //     0xafdf68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xafdf6c: ldr             x16, [x16, #0x118]
    // 0xafdf70: str             x16, [SP]
    // 0xafdf74: mov             x1, x0
    // 0xafdf78: ldur            x2, [fp, #-0x18]
    // 0xafdf7c: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xafdf7c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xafdf80: ldr             x4, [x4, #0xb40]
    // 0xafdf84: r0 = CachedNetworkImage()
    //     0xafdf84: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xafdf88: ldur            x2, [fp, #-0x48]
    // 0xafdf8c: LoadField: r0 = r2->field_f
    //     0xafdf8c: ldur            w0, [x2, #0xf]
    // 0xafdf90: DecompressPointer r0
    //     0xafdf90: add             x0, x0, HEAP, lsl #32
    // 0xafdf94: LoadField: r1 = r0->field_b
    //     0xafdf94: ldur            w1, [x0, #0xb]
    // 0xafdf98: DecompressPointer r1
    //     0xafdf98: add             x1, x1, HEAP, lsl #32
    // 0xafdf9c: cmp             w1, NULL
    // 0xafdfa0: b.eq            #0xafee58
    // 0xafdfa4: LoadField: r0 = r1->field_b
    //     0xafdfa4: ldur            w0, [x1, #0xb]
    // 0xafdfa8: DecompressPointer r0
    //     0xafdfa8: add             x0, x0, HEAP, lsl #32
    // 0xafdfac: LoadField: r3 = r0->field_b
    //     0xafdfac: ldur            w3, [x0, #0xb]
    // 0xafdfb0: DecompressPointer r3
    //     0xafdfb0: add             x3, x3, HEAP, lsl #32
    // 0xafdfb4: cmp             w3, NULL
    // 0xafdfb8: b.ne            #0xafdfc8
    // 0xafdfbc: ldur            x4, [fp, #-0x20]
    // 0xafdfc0: r0 = Null
    //     0xafdfc0: mov             x0, NULL
    // 0xafdfc4: b               #0xafe02c
    // 0xafdfc8: ldur            x4, [fp, #-0x20]
    // 0xafdfcc: LoadField: r0 = r3->field_b
    //     0xafdfcc: ldur            w0, [x3, #0xb]
    // 0xafdfd0: r1 = LoadInt32Instr(r0)
    //     0xafdfd0: sbfx            x1, x0, #1, #0x1f
    // 0xafdfd4: mov             x0, x1
    // 0xafdfd8: mov             x1, x4
    // 0xafdfdc: cmp             x1, x0
    // 0xafdfe0: b.hs            #0xafee5c
    // 0xafdfe4: LoadField: r0 = r3->field_f
    //     0xafdfe4: ldur            w0, [x3, #0xf]
    // 0xafdfe8: DecompressPointer r0
    //     0xafdfe8: add             x0, x0, HEAP, lsl #32
    // 0xafdfec: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafdfec: add             x16, x0, x4, lsl #2
    //     0xafdff0: ldur            w1, [x16, #0xf]
    // 0xafdff4: DecompressPointer r1
    //     0xafdff4: add             x1, x1, HEAP, lsl #32
    // 0xafdff8: cmp             w1, NULL
    // 0xafdffc: b.ne            #0xafe008
    // 0xafe000: r0 = Null
    //     0xafe000: mov             x0, NULL
    // 0xafe004: b               #0xafe02c
    // 0xafe008: LoadField: r0 = r1->field_7f
    //     0xafe008: ldur            w0, [x1, #0x7f]
    // 0xafe00c: DecompressPointer r0
    //     0xafe00c: add             x0, x0, HEAP, lsl #32
    // 0xafe010: cmp             w0, NULL
    // 0xafe014: b.ne            #0xafe020
    // 0xafe018: r0 = Null
    //     0xafe018: mov             x0, NULL
    // 0xafe01c: b               #0xafe02c
    // 0xafe020: LoadField: r1 = r0->field_b
    //     0xafe020: ldur            w1, [x0, #0xb]
    // 0xafe024: DecompressPointer r1
    //     0xafe024: add             x1, x1, HEAP, lsl #32
    // 0xafe028: mov             x0, x1
    // 0xafe02c: cmp             w0, NULL
    // 0xafe030: b.ne            #0xafe038
    // 0xafe034: r0 = ""
    //     0xafe034: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafe038: ldr             x1, [fp, #0x18]
    // 0xafe03c: stur            x0, [fp, #-0x18]
    // 0xafe040: r0 = of()
    //     0xafe040: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe044: LoadField: r1 = r0->field_87
    //     0xafe044: ldur            w1, [x0, #0x87]
    // 0xafe048: DecompressPointer r1
    //     0xafe048: add             x1, x1, HEAP, lsl #32
    // 0xafe04c: LoadField: r0 = r1->field_7
    //     0xafe04c: ldur            w0, [x1, #7]
    // 0xafe050: DecompressPointer r0
    //     0xafe050: add             x0, x0, HEAP, lsl #32
    // 0xafe054: r16 = 12.000000
    //     0xafe054: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafe058: ldr             x16, [x16, #0x9e8]
    // 0xafe05c: r30 = Instance_Color
    //     0xafe05c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafe060: stp             lr, x16, [SP]
    // 0xafe064: mov             x1, x0
    // 0xafe068: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafe068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafe06c: ldr             x4, [x4, #0xaa0]
    // 0xafe070: r0 = copyWith()
    //     0xafe070: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafe074: stur            x0, [fp, #-0x38]
    // 0xafe078: r0 = Text()
    //     0xafe078: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafe07c: mov             x1, x0
    // 0xafe080: ldur            x0, [fp, #-0x18]
    // 0xafe084: stur            x1, [fp, #-0x40]
    // 0xafe088: StoreField: r1->field_b = r0
    //     0xafe088: stur            w0, [x1, #0xb]
    // 0xafe08c: ldur            x0, [fp, #-0x38]
    // 0xafe090: StoreField: r1->field_13 = r0
    //     0xafe090: stur            w0, [x1, #0x13]
    // 0xafe094: r3 = Instance_TextOverflow
    //     0xafe094: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xafe098: ldr             x3, [x3, #0xe10]
    // 0xafe09c: StoreField: r1->field_2b = r3
    //     0xafe09c: stur            w3, [x1, #0x2b]
    // 0xafe0a0: r4 = 2
    //     0xafe0a0: movz            x4, #0x2
    // 0xafe0a4: StoreField: r1->field_37 = r4
    //     0xafe0a4: stur            w4, [x1, #0x37]
    // 0xafe0a8: r0 = SizedBox()
    //     0xafe0a8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xafe0ac: r5 = 150.000000
    //     0xafe0ac: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xafe0b0: ldr             x5, [x5, #0x690]
    // 0xafe0b4: stur            x0, [fp, #-0x18]
    // 0xafe0b8: StoreField: r0->field_f = r5
    //     0xafe0b8: stur            w5, [x0, #0xf]
    // 0xafe0bc: ldur            x1, [fp, #-0x40]
    // 0xafe0c0: StoreField: r0->field_b = r1
    //     0xafe0c0: stur            w1, [x0, #0xb]
    // 0xafe0c4: ldr             x1, [fp, #0x18]
    // 0xafe0c8: r0 = of()
    //     0xafe0c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe0cc: LoadField: r1 = r0->field_87
    //     0xafe0cc: ldur            w1, [x0, #0x87]
    // 0xafe0d0: DecompressPointer r1
    //     0xafe0d0: add             x1, x1, HEAP, lsl #32
    // 0xafe0d4: LoadField: r0 = r1->field_2b
    //     0xafe0d4: ldur            w0, [x1, #0x2b]
    // 0xafe0d8: DecompressPointer r0
    //     0xafe0d8: add             x0, x0, HEAP, lsl #32
    // 0xafe0dc: r16 = 12.000000
    //     0xafe0dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafe0e0: ldr             x16, [x16, #0x9e8]
    // 0xafe0e4: r30 = Instance_Color
    //     0xafe0e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xafe0e8: ldr             lr, [lr, #0x858]
    // 0xafe0ec: stp             lr, x16, [SP]
    // 0xafe0f0: mov             x1, x0
    // 0xafe0f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafe0f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafe0f8: ldr             x4, [x4, #0xaa0]
    // 0xafe0fc: r0 = copyWith()
    //     0xafe0fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafe100: stur            x0, [fp, #-0x38]
    // 0xafe104: r0 = Text()
    //     0xafe104: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafe108: mov             x2, x0
    // 0xafe10c: r6 = "Free"
    //     0xafe10c: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xafe110: ldr             x6, [x6, #0x668]
    // 0xafe114: stur            x2, [fp, #-0x40]
    // 0xafe118: StoreField: r2->field_b = r6
    //     0xafe118: stur            w6, [x2, #0xb]
    // 0xafe11c: ldur            x0, [fp, #-0x38]
    // 0xafe120: StoreField: r2->field_13 = r0
    //     0xafe120: stur            w0, [x2, #0x13]
    // 0xafe124: ldur            x7, [fp, #-0x48]
    // 0xafe128: LoadField: r0 = r7->field_f
    //     0xafe128: ldur            w0, [x7, #0xf]
    // 0xafe12c: DecompressPointer r0
    //     0xafe12c: add             x0, x0, HEAP, lsl #32
    // 0xafe130: LoadField: r1 = r0->field_b
    //     0xafe130: ldur            w1, [x0, #0xb]
    // 0xafe134: DecompressPointer r1
    //     0xafe134: add             x1, x1, HEAP, lsl #32
    // 0xafe138: cmp             w1, NULL
    // 0xafe13c: b.eq            #0xafee60
    // 0xafe140: LoadField: r0 = r1->field_b
    //     0xafe140: ldur            w0, [x1, #0xb]
    // 0xafe144: DecompressPointer r0
    //     0xafe144: add             x0, x0, HEAP, lsl #32
    // 0xafe148: LoadField: r3 = r0->field_b
    //     0xafe148: ldur            w3, [x0, #0xb]
    // 0xafe14c: DecompressPointer r3
    //     0xafe14c: add             x3, x3, HEAP, lsl #32
    // 0xafe150: cmp             w3, NULL
    // 0xafe154: b.ne            #0xafe160
    // 0xafe158: r0 = Null
    //     0xafe158: mov             x0, NULL
    // 0xafe15c: b               #0xafe1c4
    // 0xafe160: ldur            x8, [fp, #-0x20]
    // 0xafe164: LoadField: r0 = r3->field_b
    //     0xafe164: ldur            w0, [x3, #0xb]
    // 0xafe168: r1 = LoadInt32Instr(r0)
    //     0xafe168: sbfx            x1, x0, #1, #0x1f
    // 0xafe16c: mov             x0, x1
    // 0xafe170: mov             x1, x8
    // 0xafe174: cmp             x1, x0
    // 0xafe178: b.hs            #0xafee64
    // 0xafe17c: LoadField: r0 = r3->field_f
    //     0xafe17c: ldur            w0, [x3, #0xf]
    // 0xafe180: DecompressPointer r0
    //     0xafe180: add             x0, x0, HEAP, lsl #32
    // 0xafe184: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xafe184: add             x16, x0, x8, lsl #2
    //     0xafe188: ldur            w1, [x16, #0xf]
    // 0xafe18c: DecompressPointer r1
    //     0xafe18c: add             x1, x1, HEAP, lsl #32
    // 0xafe190: cmp             w1, NULL
    // 0xafe194: b.ne            #0xafe1a0
    // 0xafe198: r0 = Null
    //     0xafe198: mov             x0, NULL
    // 0xafe19c: b               #0xafe1c4
    // 0xafe1a0: LoadField: r0 = r1->field_7f
    //     0xafe1a0: ldur            w0, [x1, #0x7f]
    // 0xafe1a4: DecompressPointer r0
    //     0xafe1a4: add             x0, x0, HEAP, lsl #32
    // 0xafe1a8: cmp             w0, NULL
    // 0xafe1ac: b.ne            #0xafe1b8
    // 0xafe1b0: r0 = Null
    //     0xafe1b0: mov             x0, NULL
    // 0xafe1b4: b               #0xafe1c4
    // 0xafe1b8: LoadField: r1 = r0->field_13
    //     0xafe1b8: ldur            w1, [x0, #0x13]
    // 0xafe1bc: DecompressPointer r1
    //     0xafe1bc: add             x1, x1, HEAP, lsl #32
    // 0xafe1c0: mov             x0, x1
    // 0xafe1c4: cmp             w0, NULL
    // 0xafe1c8: b.ne            #0xafe1d4
    // 0xafe1cc: r5 = ""
    //     0xafe1cc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafe1d0: b               #0xafe1d8
    // 0xafe1d4: mov             x5, x0
    // 0xafe1d8: ldur            x4, [fp, #-0x10]
    // 0xafe1dc: ldur            x3, [fp, #-0x30]
    // 0xafe1e0: ldur            x0, [fp, #-0x18]
    // 0xafe1e4: ldr             x1, [fp, #0x18]
    // 0xafe1e8: stur            x5, [fp, #-0x38]
    // 0xafe1ec: r0 = of()
    //     0xafe1ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe1f0: LoadField: r1 = r0->field_87
    //     0xafe1f0: ldur            w1, [x0, #0x87]
    // 0xafe1f4: DecompressPointer r1
    //     0xafe1f4: add             x1, x1, HEAP, lsl #32
    // 0xafe1f8: LoadField: r0 = r1->field_2b
    //     0xafe1f8: ldur            w0, [x1, #0x2b]
    // 0xafe1fc: DecompressPointer r0
    //     0xafe1fc: add             x0, x0, HEAP, lsl #32
    // 0xafe200: r16 = 12.000000
    //     0xafe200: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafe204: ldr             x16, [x16, #0x9e8]
    // 0xafe208: r30 = Instance_TextDecoration
    //     0xafe208: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xafe20c: ldr             lr, [lr, #0xe30]
    // 0xafe210: stp             lr, x16, [SP]
    // 0xafe214: mov             x1, x0
    // 0xafe218: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xafe218: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xafe21c: ldr             x4, [x4, #0x698]
    // 0xafe220: r0 = copyWith()
    //     0xafe220: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafe224: stur            x0, [fp, #-0x58]
    // 0xafe228: r0 = Text()
    //     0xafe228: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafe22c: mov             x3, x0
    // 0xafe230: ldur            x0, [fp, #-0x38]
    // 0xafe234: stur            x3, [fp, #-0x60]
    // 0xafe238: StoreField: r3->field_b = r0
    //     0xafe238: stur            w0, [x3, #0xb]
    // 0xafe23c: ldur            x0, [fp, #-0x58]
    // 0xafe240: StoreField: r3->field_13 = r0
    //     0xafe240: stur            w0, [x3, #0x13]
    // 0xafe244: r1 = Null
    //     0xafe244: mov             x1, NULL
    // 0xafe248: r2 = 6
    //     0xafe248: movz            x2, #0x6
    // 0xafe24c: r0 = AllocateArray()
    //     0xafe24c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafe250: mov             x2, x0
    // 0xafe254: ldur            x0, [fp, #-0x40]
    // 0xafe258: stur            x2, [fp, #-0x38]
    // 0xafe25c: StoreField: r2->field_f = r0
    //     0xafe25c: stur            w0, [x2, #0xf]
    // 0xafe260: r16 = Instance_SizedBox
    //     0xafe260: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xafe264: ldr             x16, [x16, #0xa50]
    // 0xafe268: StoreField: r2->field_13 = r16
    //     0xafe268: stur            w16, [x2, #0x13]
    // 0xafe26c: ldur            x0, [fp, #-0x60]
    // 0xafe270: ArrayStore: r2[0] = r0  ; List_4
    //     0xafe270: stur            w0, [x2, #0x17]
    // 0xafe274: r1 = <Widget>
    //     0xafe274: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafe278: r0 = AllocateGrowableArray()
    //     0xafe278: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafe27c: mov             x1, x0
    // 0xafe280: ldur            x0, [fp, #-0x38]
    // 0xafe284: stur            x1, [fp, #-0x40]
    // 0xafe288: StoreField: r1->field_f = r0
    //     0xafe288: stur            w0, [x1, #0xf]
    // 0xafe28c: r2 = 6
    //     0xafe28c: movz            x2, #0x6
    // 0xafe290: StoreField: r1->field_b = r2
    //     0xafe290: stur            w2, [x1, #0xb]
    // 0xafe294: r0 = Row()
    //     0xafe294: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafe298: mov             x3, x0
    // 0xafe29c: r0 = Instance_Axis
    //     0xafe29c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafe2a0: stur            x3, [fp, #-0x38]
    // 0xafe2a4: StoreField: r3->field_f = r0
    //     0xafe2a4: stur            w0, [x3, #0xf]
    // 0xafe2a8: r4 = Instance_MainAxisAlignment
    //     0xafe2a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafe2ac: ldr             x4, [x4, #0xa08]
    // 0xafe2b0: StoreField: r3->field_13 = r4
    //     0xafe2b0: stur            w4, [x3, #0x13]
    // 0xafe2b4: r5 = Instance_MainAxisSize
    //     0xafe2b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafe2b8: ldr             x5, [x5, #0xa10]
    // 0xafe2bc: ArrayStore: r3[0] = r5  ; List_4
    //     0xafe2bc: stur            w5, [x3, #0x17]
    // 0xafe2c0: r6 = Instance_CrossAxisAlignment
    //     0xafe2c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafe2c4: ldr             x6, [x6, #0xa18]
    // 0xafe2c8: StoreField: r3->field_1b = r6
    //     0xafe2c8: stur            w6, [x3, #0x1b]
    // 0xafe2cc: r7 = Instance_VerticalDirection
    //     0xafe2cc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafe2d0: ldr             x7, [x7, #0xa20]
    // 0xafe2d4: StoreField: r3->field_23 = r7
    //     0xafe2d4: stur            w7, [x3, #0x23]
    // 0xafe2d8: r8 = Instance_Clip
    //     0xafe2d8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafe2dc: ldr             x8, [x8, #0x38]
    // 0xafe2e0: StoreField: r3->field_2b = r8
    //     0xafe2e0: stur            w8, [x3, #0x2b]
    // 0xafe2e4: StoreField: r3->field_2f = rZR
    //     0xafe2e4: stur            xzr, [x3, #0x2f]
    // 0xafe2e8: ldur            x1, [fp, #-0x40]
    // 0xafe2ec: StoreField: r3->field_b = r1
    //     0xafe2ec: stur            w1, [x3, #0xb]
    // 0xafe2f0: r1 = Null
    //     0xafe2f0: mov             x1, NULL
    // 0xafe2f4: r2 = 6
    //     0xafe2f4: movz            x2, #0x6
    // 0xafe2f8: r0 = AllocateArray()
    //     0xafe2f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafe2fc: mov             x2, x0
    // 0xafe300: ldur            x0, [fp, #-0x18]
    // 0xafe304: stur            x2, [fp, #-0x40]
    // 0xafe308: StoreField: r2->field_f = r0
    //     0xafe308: stur            w0, [x2, #0xf]
    // 0xafe30c: r16 = Instance_SizedBox
    //     0xafe30c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xafe310: ldr             x16, [x16, #0xc70]
    // 0xafe314: StoreField: r2->field_13 = r16
    //     0xafe314: stur            w16, [x2, #0x13]
    // 0xafe318: ldur            x0, [fp, #-0x38]
    // 0xafe31c: ArrayStore: r2[0] = r0  ; List_4
    //     0xafe31c: stur            w0, [x2, #0x17]
    // 0xafe320: r1 = <Widget>
    //     0xafe320: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafe324: r0 = AllocateGrowableArray()
    //     0xafe324: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafe328: mov             x1, x0
    // 0xafe32c: ldur            x0, [fp, #-0x40]
    // 0xafe330: stur            x1, [fp, #-0x18]
    // 0xafe334: StoreField: r1->field_f = r0
    //     0xafe334: stur            w0, [x1, #0xf]
    // 0xafe338: r2 = 6
    //     0xafe338: movz            x2, #0x6
    // 0xafe33c: StoreField: r1->field_b = r2
    //     0xafe33c: stur            w2, [x1, #0xb]
    // 0xafe340: r0 = Column()
    //     0xafe340: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafe344: mov             x1, x0
    // 0xafe348: r0 = Instance_Axis
    //     0xafe348: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafe34c: stur            x1, [fp, #-0x38]
    // 0xafe350: StoreField: r1->field_f = r0
    //     0xafe350: stur            w0, [x1, #0xf]
    // 0xafe354: r2 = Instance_MainAxisAlignment
    //     0xafe354: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafe358: ldr             x2, [x2, #0xa08]
    // 0xafe35c: StoreField: r1->field_13 = r2
    //     0xafe35c: stur            w2, [x1, #0x13]
    // 0xafe360: r3 = Instance_MainAxisSize
    //     0xafe360: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafe364: ldr             x3, [x3, #0xa10]
    // 0xafe368: ArrayStore: r1[0] = r3  ; List_4
    //     0xafe368: stur            w3, [x1, #0x17]
    // 0xafe36c: r9 = Instance_CrossAxisAlignment
    //     0xafe36c: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafe370: ldr             x9, [x9, #0x890]
    // 0xafe374: StoreField: r1->field_1b = r9
    //     0xafe374: stur            w9, [x1, #0x1b]
    // 0xafe378: r4 = Instance_VerticalDirection
    //     0xafe378: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafe37c: ldr             x4, [x4, #0xa20]
    // 0xafe380: StoreField: r1->field_23 = r4
    //     0xafe380: stur            w4, [x1, #0x23]
    // 0xafe384: r5 = Instance_Clip
    //     0xafe384: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafe388: ldr             x5, [x5, #0x38]
    // 0xafe38c: StoreField: r1->field_2b = r5
    //     0xafe38c: stur            w5, [x1, #0x2b]
    // 0xafe390: StoreField: r1->field_2f = rZR
    //     0xafe390: stur            xzr, [x1, #0x2f]
    // 0xafe394: ldur            x6, [fp, #-0x18]
    // 0xafe398: StoreField: r1->field_b = r6
    //     0xafe398: stur            w6, [x1, #0xb]
    // 0xafe39c: r0 = Padding()
    //     0xafe39c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafe3a0: r10 = Instance_EdgeInsets
    //     0xafe3a0: add             x10, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xafe3a4: ldr             x10, [x10, #0xa78]
    // 0xafe3a8: stur            x0, [fp, #-0x18]
    // 0xafe3ac: StoreField: r0->field_f = r10
    //     0xafe3ac: stur            w10, [x0, #0xf]
    // 0xafe3b0: ldur            x1, [fp, #-0x38]
    // 0xafe3b4: StoreField: r0->field_b = r1
    //     0xafe3b4: stur            w1, [x0, #0xb]
    // 0xafe3b8: r1 = <FlexParentData>
    //     0xafe3b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xafe3bc: ldr             x1, [x1, #0xe00]
    // 0xafe3c0: r0 = Expanded()
    //     0xafe3c0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xafe3c4: r11 = 1
    //     0xafe3c4: movz            x11, #0x1
    // 0xafe3c8: stur            x0, [fp, #-0x38]
    // 0xafe3cc: StoreField: r0->field_13 = r11
    //     0xafe3cc: stur            x11, [x0, #0x13]
    // 0xafe3d0: r12 = Instance_FlexFit
    //     0xafe3d0: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xafe3d4: ldr             x12, [x12, #0xe08]
    // 0xafe3d8: StoreField: r0->field_1b = r12
    //     0xafe3d8: stur            w12, [x0, #0x1b]
    // 0xafe3dc: ldur            x1, [fp, #-0x18]
    // 0xafe3e0: StoreField: r0->field_b = r1
    //     0xafe3e0: stur            w1, [x0, #0xb]
    // 0xafe3e4: r1 = Null
    //     0xafe3e4: mov             x1, NULL
    // 0xafe3e8: r2 = 6
    //     0xafe3e8: movz            x2, #0x6
    // 0xafe3ec: r0 = AllocateArray()
    //     0xafe3ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafe3f0: mov             x2, x0
    // 0xafe3f4: ldur            x0, [fp, #-0x10]
    // 0xafe3f8: stur            x2, [fp, #-0x18]
    // 0xafe3fc: StoreField: r2->field_f = r0
    //     0xafe3fc: stur            w0, [x2, #0xf]
    // 0xafe400: ldur            x0, [fp, #-0x30]
    // 0xafe404: StoreField: r2->field_13 = r0
    //     0xafe404: stur            w0, [x2, #0x13]
    // 0xafe408: ldur            x0, [fp, #-0x38]
    // 0xafe40c: ArrayStore: r2[0] = r0  ; List_4
    //     0xafe40c: stur            w0, [x2, #0x17]
    // 0xafe410: r1 = <Widget>
    //     0xafe410: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafe414: r0 = AllocateGrowableArray()
    //     0xafe414: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafe418: mov             x1, x0
    // 0xafe41c: ldur            x0, [fp, #-0x18]
    // 0xafe420: stur            x1, [fp, #-0x10]
    // 0xafe424: StoreField: r1->field_f = r0
    //     0xafe424: stur            w0, [x1, #0xf]
    // 0xafe428: r13 = 6
    //     0xafe428: movz            x13, #0x6
    // 0xafe42c: StoreField: r1->field_b = r13
    //     0xafe42c: stur            w13, [x1, #0xb]
    // 0xafe430: r0 = Row()
    //     0xafe430: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafe434: r14 = Instance_Axis
    //     0xafe434: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafe438: stur            x0, [fp, #-0x18]
    // 0xafe43c: StoreField: r0->field_f = r14
    //     0xafe43c: stur            w14, [x0, #0xf]
    // 0xafe440: r1 = Instance_MainAxisAlignment
    //     0xafe440: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafe444: ldr             x1, [x1, #0xa08]
    // 0xafe448: StoreField: r0->field_13 = r1
    //     0xafe448: stur            w1, [x0, #0x13]
    // 0xafe44c: r2 = Instance_MainAxisSize
    //     0xafe44c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafe450: ldr             x2, [x2, #0xa10]
    // 0xafe454: ArrayStore: r0[0] = r2  ; List_4
    //     0xafe454: stur            w2, [x0, #0x17]
    // 0xafe458: r3 = Instance_CrossAxisAlignment
    //     0xafe458: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafe45c: ldr             x3, [x3, #0xa18]
    // 0xafe460: StoreField: r0->field_1b = r3
    //     0xafe460: stur            w3, [x0, #0x1b]
    // 0xafe464: r4 = Instance_VerticalDirection
    //     0xafe464: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafe468: ldr             x4, [x4, #0xa20]
    // 0xafe46c: StoreField: r0->field_23 = r4
    //     0xafe46c: stur            w4, [x0, #0x23]
    // 0xafe470: r5 = Instance_Clip
    //     0xafe470: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafe474: ldr             x5, [x5, #0x38]
    // 0xafe478: StoreField: r0->field_2b = r5
    //     0xafe478: stur            w5, [x0, #0x2b]
    // 0xafe47c: StoreField: r0->field_2f = rZR
    //     0xafe47c: stur            xzr, [x0, #0x2f]
    // 0xafe480: ldur            x6, [fp, #-0x10]
    // 0xafe484: StoreField: r0->field_b = r6
    //     0xafe484: stur            w6, [x0, #0xb]
    // 0xafe488: r0 = Container()
    //     0xafe488: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafe48c: stur            x0, [fp, #-0x10]
    // 0xafe490: ldur            x16, [fp, #-0x28]
    // 0xafe494: ldur            lr, [fp, #-0x18]
    // 0xafe498: stp             lr, x16, [SP]
    // 0xafe49c: mov             x1, x0
    // 0xafe4a0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xafe4a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xafe4a4: ldr             x4, [x4, #0x88]
    // 0xafe4a8: r0 = Container()
    //     0xafe4a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafe4ac: r0 = Padding()
    //     0xafe4ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafe4b0: r19 = Instance_EdgeInsets
    //     0xafe4b0: add             x19, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xafe4b4: ldr             x19, [x19, #0x770]
    // 0xafe4b8: StoreField: r0->field_f = r19
    //     0xafe4b8: stur            w19, [x0, #0xf]
    // 0xafe4bc: ldur            x1, [fp, #-0x10]
    // 0xafe4c0: StoreField: r0->field_b = r1
    //     0xafe4c0: stur            w1, [x0, #0xb]
    // 0xafe4c4: mov             x2, x0
    // 0xafe4c8: b               #0xafed44
    // 0xafe4cc: ldur            x7, [fp, #-0x48]
    // 0xafe4d0: mov             x8, x5
    // 0xafe4d4: r4 = 2
    //     0xafe4d4: movz            x4, #0x2
    // 0xafe4d8: r6 = "Free"
    //     0xafe4d8: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xafe4dc: ldr             x6, [x6, #0x668]
    // 0xafe4e0: r5 = 150.000000
    //     0xafe4e0: add             x5, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xafe4e4: ldr             x5, [x5, #0x690]
    // 0xafe4e8: r3 = Instance_TextOverflow
    //     0xafe4e8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xafe4ec: ldr             x3, [x3, #0xe10]
    // 0xafe4f0: r9 = Instance_CrossAxisAlignment
    //     0xafe4f0: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafe4f4: ldr             x9, [x9, #0x890]
    // 0xafe4f8: r10 = Instance_EdgeInsets
    //     0xafe4f8: add             x10, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xafe4fc: ldr             x10, [x10, #0xa78]
    // 0xafe500: r19 = Instance_EdgeInsets
    //     0xafe500: add             x19, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xafe504: ldr             x19, [x19, #0x770]
    // 0xafe508: r13 = 6
    //     0xafe508: movz            x13, #0x6
    // 0xafe50c: r14 = Instance_Axis
    //     0xafe50c: ldr             x14, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafe510: r12 = Instance_FlexFit
    //     0xafe510: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xafe514: ldr             x12, [x12, #0xe08]
    // 0xafe518: r0 = Instance_BoxShape
    //     0xafe518: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafe51c: ldr             x0, [x0, #0x80]
    // 0xafe520: r1 = Instance_Alignment
    //     0xafe520: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xafe524: ldr             x1, [x1, #0xb10]
    // 0xafe528: r2 = -1
    //     0xafe528: movn            x2, #0
    // 0xafe52c: d0 = 12.000000
    //     0xafe52c: fmov            d0, #12.00000000
    // 0xafe530: r11 = 1
    //     0xafe530: movz            x11, #0x1
    // 0xafe534: r0 = Radius()
    //     0xafe534: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafe538: d0 = 12.000000
    //     0xafe538: fmov            d0, #12.00000000
    // 0xafe53c: stur            x0, [fp, #-0x10]
    // 0xafe540: StoreField: r0->field_7 = d0
    //     0xafe540: stur            d0, [x0, #7]
    // 0xafe544: StoreField: r0->field_f = d0
    //     0xafe544: stur            d0, [x0, #0xf]
    // 0xafe548: r0 = BorderRadius()
    //     0xafe548: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafe54c: mov             x2, x0
    // 0xafe550: ldur            x0, [fp, #-0x10]
    // 0xafe554: stur            x2, [fp, #-0x18]
    // 0xafe558: StoreField: r2->field_7 = r0
    //     0xafe558: stur            w0, [x2, #7]
    // 0xafe55c: StoreField: r2->field_b = r0
    //     0xafe55c: stur            w0, [x2, #0xb]
    // 0xafe560: StoreField: r2->field_f = r0
    //     0xafe560: stur            w0, [x2, #0xf]
    // 0xafe564: StoreField: r2->field_13 = r0
    //     0xafe564: stur            w0, [x2, #0x13]
    // 0xafe568: ldr             x1, [fp, #0x18]
    // 0xafe56c: r0 = of()
    //     0xafe56c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe570: LoadField: r1 = r0->field_5b
    //     0xafe570: ldur            w1, [x0, #0x5b]
    // 0xafe574: DecompressPointer r1
    //     0xafe574: add             x1, x1, HEAP, lsl #32
    // 0xafe578: r0 = LoadClassIdInstr(r1)
    //     0xafe578: ldur            x0, [x1, #-1]
    //     0xafe57c: ubfx            x0, x0, #0xc, #0x14
    // 0xafe580: d0 = 0.070000
    //     0xafe580: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xafe584: ldr             d0, [x17, #0x5f8]
    // 0xafe588: r0 = GDT[cid_x0 + -0xffa]()
    //     0xafe588: sub             lr, x0, #0xffa
    //     0xafe58c: ldr             lr, [x21, lr, lsl #3]
    //     0xafe590: blr             lr
    // 0xafe594: r16 = 1.000000
    //     0xafe594: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xafe598: str             x16, [SP]
    // 0xafe59c: mov             x2, x0
    // 0xafe5a0: r1 = Null
    //     0xafe5a0: mov             x1, NULL
    // 0xafe5a4: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xafe5a4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xafe5a8: ldr             x4, [x4, #0x108]
    // 0xafe5ac: r0 = Border.all()
    //     0xafe5ac: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xafe5b0: stur            x0, [fp, #-0x10]
    // 0xafe5b4: r0 = BoxDecoration()
    //     0xafe5b4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xafe5b8: mov             x2, x0
    // 0xafe5bc: ldur            x0, [fp, #-0x10]
    // 0xafe5c0: stur            x2, [fp, #-0x28]
    // 0xafe5c4: StoreField: r2->field_f = r0
    //     0xafe5c4: stur            w0, [x2, #0xf]
    // 0xafe5c8: ldur            x0, [fp, #-0x18]
    // 0xafe5cc: StoreField: r2->field_13 = r0
    //     0xafe5cc: stur            w0, [x2, #0x13]
    // 0xafe5d0: r0 = Instance_BoxShape
    //     0xafe5d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafe5d4: ldr             x0, [x0, #0x80]
    // 0xafe5d8: StoreField: r2->field_23 = r0
    //     0xafe5d8: stur            w0, [x2, #0x23]
    // 0xafe5dc: ldr             x1, [fp, #0x18]
    // 0xafe5e0: r0 = of()
    //     0xafe5e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe5e4: LoadField: r1 = r0->field_5b
    //     0xafe5e4: ldur            w1, [x0, #0x5b]
    // 0xafe5e8: DecompressPointer r1
    //     0xafe5e8: add             x1, x1, HEAP, lsl #32
    // 0xafe5ec: r0 = LoadClassIdInstr(r1)
    //     0xafe5ec: ldur            x0, [x1, #-1]
    //     0xafe5f0: ubfx            x0, x0, #0xc, #0x14
    // 0xafe5f4: d0 = 0.400000
    //     0xafe5f4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xafe5f8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xafe5f8: sub             lr, x0, #0xffa
    //     0xafe5fc: ldr             lr, [x21, lr, lsl #3]
    //     0xafe600: blr             lr
    // 0xafe604: stur            x0, [fp, #-0x10]
    // 0xafe608: r0 = BoxDecoration()
    //     0xafe608: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xafe60c: mov             x2, x0
    // 0xafe610: ldur            x0, [fp, #-0x10]
    // 0xafe614: stur            x2, [fp, #-0x18]
    // 0xafe618: StoreField: r2->field_7 = r0
    //     0xafe618: stur            w0, [x2, #7]
    // 0xafe61c: r0 = Instance_BorderRadius
    //     0xafe61c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48990] Obj!BorderRadius@d5a281
    //     0xafe620: ldr             x0, [x0, #0x990]
    // 0xafe624: StoreField: r2->field_13 = r0
    //     0xafe624: stur            w0, [x2, #0x13]
    // 0xafe628: r0 = Instance_BoxShape
    //     0xafe628: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafe62c: ldr             x0, [x0, #0x80]
    // 0xafe630: StoreField: r2->field_23 = r0
    //     0xafe630: stur            w0, [x2, #0x23]
    // 0xafe634: ldr             x1, [fp, #0x18]
    // 0xafe638: r0 = of()
    //     0xafe638: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe63c: LoadField: r1 = r0->field_87
    //     0xafe63c: ldur            w1, [x0, #0x87]
    // 0xafe640: DecompressPointer r1
    //     0xafe640: add             x1, x1, HEAP, lsl #32
    // 0xafe644: LoadField: r0 = r1->field_7
    //     0xafe644: ldur            w0, [x1, #7]
    // 0xafe648: DecompressPointer r0
    //     0xafe648: add             x0, x0, HEAP, lsl #32
    // 0xafe64c: r16 = 12.000000
    //     0xafe64c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafe650: ldr             x16, [x16, #0x9e8]
    // 0xafe654: r30 = Instance_Color
    //     0xafe654: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xafe658: stp             lr, x16, [SP]
    // 0xafe65c: mov             x1, x0
    // 0xafe660: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafe660: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafe664: ldr             x4, [x4, #0xaa0]
    // 0xafe668: r0 = copyWith()
    //     0xafe668: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafe66c: stur            x0, [fp, #-0x10]
    // 0xafe670: r0 = Text()
    //     0xafe670: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafe674: mov             x1, x0
    // 0xafe678: r0 = "Free"
    //     0xafe678: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xafe67c: ldr             x0, [x0, #0x668]
    // 0xafe680: stur            x1, [fp, #-0x30]
    // 0xafe684: StoreField: r1->field_b = r0
    //     0xafe684: stur            w0, [x1, #0xb]
    // 0xafe688: ldur            x2, [fp, #-0x10]
    // 0xafe68c: StoreField: r1->field_13 = r2
    //     0xafe68c: stur            w2, [x1, #0x13]
    // 0xafe690: r0 = Center()
    //     0xafe690: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xafe694: mov             x1, x0
    // 0xafe698: r0 = Instance_Alignment
    //     0xafe698: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xafe69c: ldr             x0, [x0, #0xb10]
    // 0xafe6a0: stur            x1, [fp, #-0x10]
    // 0xafe6a4: StoreField: r1->field_f = r0
    //     0xafe6a4: stur            w0, [x1, #0xf]
    // 0xafe6a8: ldur            x0, [fp, #-0x30]
    // 0xafe6ac: StoreField: r1->field_b = r0
    //     0xafe6ac: stur            w0, [x1, #0xb]
    // 0xafe6b0: r0 = RotatedBox()
    //     0xafe6b0: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xafe6b4: mov             x1, x0
    // 0xafe6b8: r0 = -1
    //     0xafe6b8: movn            x0, #0
    // 0xafe6bc: stur            x1, [fp, #-0x30]
    // 0xafe6c0: StoreField: r1->field_f = r0
    //     0xafe6c0: stur            x0, [x1, #0xf]
    // 0xafe6c4: ldur            x0, [fp, #-0x10]
    // 0xafe6c8: StoreField: r1->field_b = r0
    //     0xafe6c8: stur            w0, [x1, #0xb]
    // 0xafe6cc: r0 = Container()
    //     0xafe6cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafe6d0: stur            x0, [fp, #-0x10]
    // 0xafe6d4: r16 = 24.000000
    //     0xafe6d4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xafe6d8: ldr             x16, [x16, #0xba8]
    // 0xafe6dc: r30 = 56.000000
    //     0xafe6dc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafe6e0: ldr             lr, [lr, #0xb78]
    // 0xafe6e4: stp             lr, x16, [SP, #0x10]
    // 0xafe6e8: ldur            x16, [fp, #-0x18]
    // 0xafe6ec: ldur            lr, [fp, #-0x30]
    // 0xafe6f0: stp             lr, x16, [SP]
    // 0xafe6f4: mov             x1, x0
    // 0xafe6f8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xafe6f8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xafe6fc: ldr             x4, [x4, #0x870]
    // 0xafe700: r0 = Container()
    //     0xafe700: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafe704: ldur            x2, [fp, #-0x48]
    // 0xafe708: LoadField: r0 = r2->field_f
    //     0xafe708: ldur            w0, [x2, #0xf]
    // 0xafe70c: DecompressPointer r0
    //     0xafe70c: add             x0, x0, HEAP, lsl #32
    // 0xafe710: LoadField: r1 = r0->field_b
    //     0xafe710: ldur            w1, [x0, #0xb]
    // 0xafe714: DecompressPointer r1
    //     0xafe714: add             x1, x1, HEAP, lsl #32
    // 0xafe718: cmp             w1, NULL
    // 0xafe71c: b.eq            #0xafee68
    // 0xafe720: LoadField: r0 = r1->field_b
    //     0xafe720: ldur            w0, [x1, #0xb]
    // 0xafe724: DecompressPointer r0
    //     0xafe724: add             x0, x0, HEAP, lsl #32
    // 0xafe728: LoadField: r3 = r0->field_b
    //     0xafe728: ldur            w3, [x0, #0xb]
    // 0xafe72c: DecompressPointer r3
    //     0xafe72c: add             x3, x3, HEAP, lsl #32
    // 0xafe730: cmp             w3, NULL
    // 0xafe734: b.ne            #0xafe744
    // 0xafe738: ldur            x4, [fp, #-0x20]
    // 0xafe73c: r0 = Null
    //     0xafe73c: mov             x0, NULL
    // 0xafe740: b               #0xafe7a8
    // 0xafe744: ldur            x4, [fp, #-0x20]
    // 0xafe748: LoadField: r0 = r3->field_b
    //     0xafe748: ldur            w0, [x3, #0xb]
    // 0xafe74c: r1 = LoadInt32Instr(r0)
    //     0xafe74c: sbfx            x1, x0, #1, #0x1f
    // 0xafe750: mov             x0, x1
    // 0xafe754: mov             x1, x4
    // 0xafe758: cmp             x1, x0
    // 0xafe75c: b.hs            #0xafee6c
    // 0xafe760: LoadField: r0 = r3->field_f
    //     0xafe760: ldur            w0, [x3, #0xf]
    // 0xafe764: DecompressPointer r0
    //     0xafe764: add             x0, x0, HEAP, lsl #32
    // 0xafe768: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafe768: add             x16, x0, x4, lsl #2
    //     0xafe76c: ldur            w1, [x16, #0xf]
    // 0xafe770: DecompressPointer r1
    //     0xafe770: add             x1, x1, HEAP, lsl #32
    // 0xafe774: cmp             w1, NULL
    // 0xafe778: b.ne            #0xafe784
    // 0xafe77c: r0 = Null
    //     0xafe77c: mov             x0, NULL
    // 0xafe780: b               #0xafe7a8
    // 0xafe784: LoadField: r0 = r1->field_7f
    //     0xafe784: ldur            w0, [x1, #0x7f]
    // 0xafe788: DecompressPointer r0
    //     0xafe788: add             x0, x0, HEAP, lsl #32
    // 0xafe78c: cmp             w0, NULL
    // 0xafe790: b.ne            #0xafe79c
    // 0xafe794: r0 = Null
    //     0xafe794: mov             x0, NULL
    // 0xafe798: b               #0xafe7a8
    // 0xafe79c: LoadField: r1 = r0->field_7
    //     0xafe79c: ldur            w1, [x0, #7]
    // 0xafe7a0: DecompressPointer r1
    //     0xafe7a0: add             x1, x1, HEAP, lsl #32
    // 0xafe7a4: mov             x0, x1
    // 0xafe7a8: cmp             w0, NULL
    // 0xafe7ac: b.ne            #0xafe7b4
    // 0xafe7b0: r0 = ""
    //     0xafe7b0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafe7b4: stur            x0, [fp, #-0x18]
    // 0xafe7b8: r0 = CachedNetworkImage()
    //     0xafe7b8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xafe7bc: stur            x0, [fp, #-0x30]
    // 0xafe7c0: r16 = 56.000000
    //     0xafe7c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafe7c4: ldr             x16, [x16, #0xb78]
    // 0xafe7c8: r30 = 56.000000
    //     0xafe7c8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafe7cc: ldr             lr, [lr, #0xb78]
    // 0xafe7d0: stp             lr, x16, [SP, #8]
    // 0xafe7d4: r16 = Instance_BoxFit
    //     0xafe7d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xafe7d8: ldr             x16, [x16, #0x118]
    // 0xafe7dc: str             x16, [SP]
    // 0xafe7e0: mov             x1, x0
    // 0xafe7e4: ldur            x2, [fp, #-0x18]
    // 0xafe7e8: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xafe7e8: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xafe7ec: ldr             x4, [x4, #0xb40]
    // 0xafe7f0: r0 = CachedNetworkImage()
    //     0xafe7f0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xafe7f4: ldur            x2, [fp, #-0x48]
    // 0xafe7f8: LoadField: r0 = r2->field_f
    //     0xafe7f8: ldur            w0, [x2, #0xf]
    // 0xafe7fc: DecompressPointer r0
    //     0xafe7fc: add             x0, x0, HEAP, lsl #32
    // 0xafe800: LoadField: r1 = r0->field_b
    //     0xafe800: ldur            w1, [x0, #0xb]
    // 0xafe804: DecompressPointer r1
    //     0xafe804: add             x1, x1, HEAP, lsl #32
    // 0xafe808: cmp             w1, NULL
    // 0xafe80c: b.eq            #0xafee70
    // 0xafe810: LoadField: r0 = r1->field_b
    //     0xafe810: ldur            w0, [x1, #0xb]
    // 0xafe814: DecompressPointer r0
    //     0xafe814: add             x0, x0, HEAP, lsl #32
    // 0xafe818: LoadField: r3 = r0->field_b
    //     0xafe818: ldur            w3, [x0, #0xb]
    // 0xafe81c: DecompressPointer r3
    //     0xafe81c: add             x3, x3, HEAP, lsl #32
    // 0xafe820: cmp             w3, NULL
    // 0xafe824: b.ne            #0xafe834
    // 0xafe828: ldur            x4, [fp, #-0x20]
    // 0xafe82c: r0 = Null
    //     0xafe82c: mov             x0, NULL
    // 0xafe830: b               #0xafe898
    // 0xafe834: ldur            x4, [fp, #-0x20]
    // 0xafe838: LoadField: r0 = r3->field_b
    //     0xafe838: ldur            w0, [x3, #0xb]
    // 0xafe83c: r1 = LoadInt32Instr(r0)
    //     0xafe83c: sbfx            x1, x0, #1, #0x1f
    // 0xafe840: mov             x0, x1
    // 0xafe844: mov             x1, x4
    // 0xafe848: cmp             x1, x0
    // 0xafe84c: b.hs            #0xafee74
    // 0xafe850: LoadField: r0 = r3->field_f
    //     0xafe850: ldur            w0, [x3, #0xf]
    // 0xafe854: DecompressPointer r0
    //     0xafe854: add             x0, x0, HEAP, lsl #32
    // 0xafe858: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafe858: add             x16, x0, x4, lsl #2
    //     0xafe85c: ldur            w1, [x16, #0xf]
    // 0xafe860: DecompressPointer r1
    //     0xafe860: add             x1, x1, HEAP, lsl #32
    // 0xafe864: cmp             w1, NULL
    // 0xafe868: b.ne            #0xafe874
    // 0xafe86c: r0 = Null
    //     0xafe86c: mov             x0, NULL
    // 0xafe870: b               #0xafe898
    // 0xafe874: LoadField: r0 = r1->field_7f
    //     0xafe874: ldur            w0, [x1, #0x7f]
    // 0xafe878: DecompressPointer r0
    //     0xafe878: add             x0, x0, HEAP, lsl #32
    // 0xafe87c: cmp             w0, NULL
    // 0xafe880: b.ne            #0xafe88c
    // 0xafe884: r0 = Null
    //     0xafe884: mov             x0, NULL
    // 0xafe888: b               #0xafe898
    // 0xafe88c: LoadField: r1 = r0->field_b
    //     0xafe88c: ldur            w1, [x0, #0xb]
    // 0xafe890: DecompressPointer r1
    //     0xafe890: add             x1, x1, HEAP, lsl #32
    // 0xafe894: mov             x0, x1
    // 0xafe898: cmp             w0, NULL
    // 0xafe89c: b.ne            #0xafe8a4
    // 0xafe8a0: r0 = ""
    //     0xafe8a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafe8a4: ldr             x1, [fp, #0x18]
    // 0xafe8a8: stur            x0, [fp, #-0x18]
    // 0xafe8ac: r0 = of()
    //     0xafe8ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe8b0: LoadField: r1 = r0->field_87
    //     0xafe8b0: ldur            w1, [x0, #0x87]
    // 0xafe8b4: DecompressPointer r1
    //     0xafe8b4: add             x1, x1, HEAP, lsl #32
    // 0xafe8b8: LoadField: r0 = r1->field_7
    //     0xafe8b8: ldur            w0, [x1, #7]
    // 0xafe8bc: DecompressPointer r0
    //     0xafe8bc: add             x0, x0, HEAP, lsl #32
    // 0xafe8c0: r16 = 12.000000
    //     0xafe8c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafe8c4: ldr             x16, [x16, #0x9e8]
    // 0xafe8c8: r30 = Instance_Color
    //     0xafe8c8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafe8cc: stp             lr, x16, [SP]
    // 0xafe8d0: mov             x1, x0
    // 0xafe8d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafe8d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafe8d8: ldr             x4, [x4, #0xaa0]
    // 0xafe8dc: r0 = copyWith()
    //     0xafe8dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafe8e0: stur            x0, [fp, #-0x38]
    // 0xafe8e4: r0 = Text()
    //     0xafe8e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafe8e8: mov             x1, x0
    // 0xafe8ec: ldur            x0, [fp, #-0x18]
    // 0xafe8f0: stur            x1, [fp, #-0x40]
    // 0xafe8f4: StoreField: r1->field_b = r0
    //     0xafe8f4: stur            w0, [x1, #0xb]
    // 0xafe8f8: ldur            x0, [fp, #-0x38]
    // 0xafe8fc: StoreField: r1->field_13 = r0
    //     0xafe8fc: stur            w0, [x1, #0x13]
    // 0xafe900: r0 = Instance_TextOverflow
    //     0xafe900: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xafe904: ldr             x0, [x0, #0xe10]
    // 0xafe908: StoreField: r1->field_2b = r0
    //     0xafe908: stur            w0, [x1, #0x2b]
    // 0xafe90c: r0 = 2
    //     0xafe90c: movz            x0, #0x2
    // 0xafe910: StoreField: r1->field_37 = r0
    //     0xafe910: stur            w0, [x1, #0x37]
    // 0xafe914: r0 = SizedBox()
    //     0xafe914: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xafe918: mov             x2, x0
    // 0xafe91c: r0 = 150.000000
    //     0xafe91c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xafe920: ldr             x0, [x0, #0x690]
    // 0xafe924: stur            x2, [fp, #-0x18]
    // 0xafe928: StoreField: r2->field_f = r0
    //     0xafe928: stur            w0, [x2, #0xf]
    // 0xafe92c: ldur            x0, [fp, #-0x40]
    // 0xafe930: StoreField: r2->field_b = r0
    //     0xafe930: stur            w0, [x2, #0xb]
    // 0xafe934: ldr             x1, [fp, #0x18]
    // 0xafe938: r0 = of()
    //     0xafe938: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafe93c: LoadField: r1 = r0->field_87
    //     0xafe93c: ldur            w1, [x0, #0x87]
    // 0xafe940: DecompressPointer r1
    //     0xafe940: add             x1, x1, HEAP, lsl #32
    // 0xafe944: LoadField: r0 = r1->field_2b
    //     0xafe944: ldur            w0, [x1, #0x2b]
    // 0xafe948: DecompressPointer r0
    //     0xafe948: add             x0, x0, HEAP, lsl #32
    // 0xafe94c: r16 = 12.000000
    //     0xafe94c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafe950: ldr             x16, [x16, #0x9e8]
    // 0xafe954: r30 = Instance_Color
    //     0xafe954: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafe958: stp             lr, x16, [SP]
    // 0xafe95c: mov             x1, x0
    // 0xafe960: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafe960: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafe964: ldr             x4, [x4, #0xaa0]
    // 0xafe968: r0 = copyWith()
    //     0xafe968: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafe96c: stur            x0, [fp, #-0x38]
    // 0xafe970: r0 = Text()
    //     0xafe970: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafe974: mov             x2, x0
    // 0xafe978: r0 = "Free"
    //     0xafe978: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xafe97c: ldr             x0, [x0, #0x668]
    // 0xafe980: stur            x2, [fp, #-0x40]
    // 0xafe984: StoreField: r2->field_b = r0
    //     0xafe984: stur            w0, [x2, #0xb]
    // 0xafe988: ldur            x0, [fp, #-0x38]
    // 0xafe98c: StoreField: r2->field_13 = r0
    //     0xafe98c: stur            w0, [x2, #0x13]
    // 0xafe990: ldur            x0, [fp, #-0x48]
    // 0xafe994: LoadField: r1 = r0->field_f
    //     0xafe994: ldur            w1, [x0, #0xf]
    // 0xafe998: DecompressPointer r1
    //     0xafe998: add             x1, x1, HEAP, lsl #32
    // 0xafe99c: LoadField: r0 = r1->field_b
    //     0xafe99c: ldur            w0, [x1, #0xb]
    // 0xafe9a0: DecompressPointer r0
    //     0xafe9a0: add             x0, x0, HEAP, lsl #32
    // 0xafe9a4: cmp             w0, NULL
    // 0xafe9a8: b.eq            #0xafee78
    // 0xafe9ac: LoadField: r1 = r0->field_b
    //     0xafe9ac: ldur            w1, [x0, #0xb]
    // 0xafe9b0: DecompressPointer r1
    //     0xafe9b0: add             x1, x1, HEAP, lsl #32
    // 0xafe9b4: LoadField: r3 = r1->field_b
    //     0xafe9b4: ldur            w3, [x1, #0xb]
    // 0xafe9b8: DecompressPointer r3
    //     0xafe9b8: add             x3, x3, HEAP, lsl #32
    // 0xafe9bc: cmp             w3, NULL
    // 0xafe9c0: b.ne            #0xafe9cc
    // 0xafe9c4: r0 = Null
    //     0xafe9c4: mov             x0, NULL
    // 0xafe9c8: b               #0xafea30
    // 0xafe9cc: ldur            x4, [fp, #-0x20]
    // 0xafe9d0: LoadField: r0 = r3->field_b
    //     0xafe9d0: ldur            w0, [x3, #0xb]
    // 0xafe9d4: r1 = LoadInt32Instr(r0)
    //     0xafe9d4: sbfx            x1, x0, #1, #0x1f
    // 0xafe9d8: mov             x0, x1
    // 0xafe9dc: mov             x1, x4
    // 0xafe9e0: cmp             x1, x0
    // 0xafe9e4: b.hs            #0xafee7c
    // 0xafe9e8: LoadField: r0 = r3->field_f
    //     0xafe9e8: ldur            w0, [x3, #0xf]
    // 0xafe9ec: DecompressPointer r0
    //     0xafe9ec: add             x0, x0, HEAP, lsl #32
    // 0xafe9f0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafe9f0: add             x16, x0, x4, lsl #2
    //     0xafe9f4: ldur            w1, [x16, #0xf]
    // 0xafe9f8: DecompressPointer r1
    //     0xafe9f8: add             x1, x1, HEAP, lsl #32
    // 0xafe9fc: cmp             w1, NULL
    // 0xafea00: b.ne            #0xafea0c
    // 0xafea04: r0 = Null
    //     0xafea04: mov             x0, NULL
    // 0xafea08: b               #0xafea30
    // 0xafea0c: LoadField: r0 = r1->field_7f
    //     0xafea0c: ldur            w0, [x1, #0x7f]
    // 0xafea10: DecompressPointer r0
    //     0xafea10: add             x0, x0, HEAP, lsl #32
    // 0xafea14: cmp             w0, NULL
    // 0xafea18: b.ne            #0xafea24
    // 0xafea1c: r0 = Null
    //     0xafea1c: mov             x0, NULL
    // 0xafea20: b               #0xafea30
    // 0xafea24: LoadField: r1 = r0->field_13
    //     0xafea24: ldur            w1, [x0, #0x13]
    // 0xafea28: DecompressPointer r1
    //     0xafea28: add             x1, x1, HEAP, lsl #32
    // 0xafea2c: mov             x0, x1
    // 0xafea30: cmp             w0, NULL
    // 0xafea34: b.ne            #0xafea40
    // 0xafea38: r5 = ""
    //     0xafea38: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafea3c: b               #0xafea44
    // 0xafea40: mov             x5, x0
    // 0xafea44: ldur            x4, [fp, #-0x10]
    // 0xafea48: ldur            x3, [fp, #-0x30]
    // 0xafea4c: ldur            x0, [fp, #-0x18]
    // 0xafea50: ldr             x1, [fp, #0x18]
    // 0xafea54: stur            x5, [fp, #-0x38]
    // 0xafea58: r0 = of()
    //     0xafea58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafea5c: LoadField: r1 = r0->field_87
    //     0xafea5c: ldur            w1, [x0, #0x87]
    // 0xafea60: DecompressPointer r1
    //     0xafea60: add             x1, x1, HEAP, lsl #32
    // 0xafea64: LoadField: r0 = r1->field_2b
    //     0xafea64: ldur            w0, [x1, #0x2b]
    // 0xafea68: DecompressPointer r0
    //     0xafea68: add             x0, x0, HEAP, lsl #32
    // 0xafea6c: r16 = 12.000000
    //     0xafea6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafea70: ldr             x16, [x16, #0x9e8]
    // 0xafea74: r30 = Instance_TextDecoration
    //     0xafea74: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xafea78: ldr             lr, [lr, #0xe30]
    // 0xafea7c: stp             lr, x16, [SP]
    // 0xafea80: mov             x1, x0
    // 0xafea84: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xafea84: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xafea88: ldr             x4, [x4, #0x698]
    // 0xafea8c: r0 = copyWith()
    //     0xafea8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafea90: stur            x0, [fp, #-0x48]
    // 0xafea94: r0 = Text()
    //     0xafea94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafea98: mov             x3, x0
    // 0xafea9c: ldur            x0, [fp, #-0x38]
    // 0xafeaa0: stur            x3, [fp, #-0x58]
    // 0xafeaa4: StoreField: r3->field_b = r0
    //     0xafeaa4: stur            w0, [x3, #0xb]
    // 0xafeaa8: ldur            x0, [fp, #-0x48]
    // 0xafeaac: StoreField: r3->field_13 = r0
    //     0xafeaac: stur            w0, [x3, #0x13]
    // 0xafeab0: r1 = Null
    //     0xafeab0: mov             x1, NULL
    // 0xafeab4: r2 = 6
    //     0xafeab4: movz            x2, #0x6
    // 0xafeab8: r0 = AllocateArray()
    //     0xafeab8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafeabc: mov             x2, x0
    // 0xafeac0: ldur            x0, [fp, #-0x40]
    // 0xafeac4: stur            x2, [fp, #-0x38]
    // 0xafeac8: StoreField: r2->field_f = r0
    //     0xafeac8: stur            w0, [x2, #0xf]
    // 0xafeacc: r16 = Instance_SizedBox
    //     0xafeacc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xafead0: ldr             x16, [x16, #0xa50]
    // 0xafead4: StoreField: r2->field_13 = r16
    //     0xafead4: stur            w16, [x2, #0x13]
    // 0xafead8: ldur            x0, [fp, #-0x58]
    // 0xafeadc: ArrayStore: r2[0] = r0  ; List_4
    //     0xafeadc: stur            w0, [x2, #0x17]
    // 0xafeae0: r1 = <Widget>
    //     0xafeae0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafeae4: r0 = AllocateGrowableArray()
    //     0xafeae4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafeae8: mov             x1, x0
    // 0xafeaec: ldur            x0, [fp, #-0x38]
    // 0xafeaf0: stur            x1, [fp, #-0x40]
    // 0xafeaf4: StoreField: r1->field_f = r0
    //     0xafeaf4: stur            w0, [x1, #0xf]
    // 0xafeaf8: r2 = 6
    //     0xafeaf8: movz            x2, #0x6
    // 0xafeafc: StoreField: r1->field_b = r2
    //     0xafeafc: stur            w2, [x1, #0xb]
    // 0xafeb00: r0 = Row()
    //     0xafeb00: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafeb04: mov             x3, x0
    // 0xafeb08: r0 = Instance_Axis
    //     0xafeb08: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafeb0c: stur            x3, [fp, #-0x38]
    // 0xafeb10: StoreField: r3->field_f = r0
    //     0xafeb10: stur            w0, [x3, #0xf]
    // 0xafeb14: r4 = Instance_MainAxisAlignment
    //     0xafeb14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafeb18: ldr             x4, [x4, #0xa08]
    // 0xafeb1c: StoreField: r3->field_13 = r4
    //     0xafeb1c: stur            w4, [x3, #0x13]
    // 0xafeb20: r5 = Instance_MainAxisSize
    //     0xafeb20: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafeb24: ldr             x5, [x5, #0xa10]
    // 0xafeb28: ArrayStore: r3[0] = r5  ; List_4
    //     0xafeb28: stur            w5, [x3, #0x17]
    // 0xafeb2c: r6 = Instance_CrossAxisAlignment
    //     0xafeb2c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafeb30: ldr             x6, [x6, #0xa18]
    // 0xafeb34: StoreField: r3->field_1b = r6
    //     0xafeb34: stur            w6, [x3, #0x1b]
    // 0xafeb38: r7 = Instance_VerticalDirection
    //     0xafeb38: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafeb3c: ldr             x7, [x7, #0xa20]
    // 0xafeb40: StoreField: r3->field_23 = r7
    //     0xafeb40: stur            w7, [x3, #0x23]
    // 0xafeb44: r8 = Instance_Clip
    //     0xafeb44: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafeb48: ldr             x8, [x8, #0x38]
    // 0xafeb4c: StoreField: r3->field_2b = r8
    //     0xafeb4c: stur            w8, [x3, #0x2b]
    // 0xafeb50: StoreField: r3->field_2f = rZR
    //     0xafeb50: stur            xzr, [x3, #0x2f]
    // 0xafeb54: ldur            x1, [fp, #-0x40]
    // 0xafeb58: StoreField: r3->field_b = r1
    //     0xafeb58: stur            w1, [x3, #0xb]
    // 0xafeb5c: r1 = Null
    //     0xafeb5c: mov             x1, NULL
    // 0xafeb60: r2 = 6
    //     0xafeb60: movz            x2, #0x6
    // 0xafeb64: r0 = AllocateArray()
    //     0xafeb64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafeb68: mov             x2, x0
    // 0xafeb6c: ldur            x0, [fp, #-0x18]
    // 0xafeb70: stur            x2, [fp, #-0x40]
    // 0xafeb74: StoreField: r2->field_f = r0
    //     0xafeb74: stur            w0, [x2, #0xf]
    // 0xafeb78: r16 = Instance_SizedBox
    //     0xafeb78: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xafeb7c: ldr             x16, [x16, #0xc70]
    // 0xafeb80: StoreField: r2->field_13 = r16
    //     0xafeb80: stur            w16, [x2, #0x13]
    // 0xafeb84: ldur            x0, [fp, #-0x38]
    // 0xafeb88: ArrayStore: r2[0] = r0  ; List_4
    //     0xafeb88: stur            w0, [x2, #0x17]
    // 0xafeb8c: r1 = <Widget>
    //     0xafeb8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafeb90: r0 = AllocateGrowableArray()
    //     0xafeb90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafeb94: mov             x1, x0
    // 0xafeb98: ldur            x0, [fp, #-0x40]
    // 0xafeb9c: stur            x1, [fp, #-0x18]
    // 0xafeba0: StoreField: r1->field_f = r0
    //     0xafeba0: stur            w0, [x1, #0xf]
    // 0xafeba4: r2 = 6
    //     0xafeba4: movz            x2, #0x6
    // 0xafeba8: StoreField: r1->field_b = r2
    //     0xafeba8: stur            w2, [x1, #0xb]
    // 0xafebac: r0 = Column()
    //     0xafebac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafebb0: mov             x1, x0
    // 0xafebb4: r0 = Instance_Axis
    //     0xafebb4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafebb8: stur            x1, [fp, #-0x38]
    // 0xafebbc: StoreField: r1->field_f = r0
    //     0xafebbc: stur            w0, [x1, #0xf]
    // 0xafebc0: r2 = Instance_MainAxisAlignment
    //     0xafebc0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafebc4: ldr             x2, [x2, #0xa08]
    // 0xafebc8: StoreField: r1->field_13 = r2
    //     0xafebc8: stur            w2, [x1, #0x13]
    // 0xafebcc: r3 = Instance_MainAxisSize
    //     0xafebcc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafebd0: ldr             x3, [x3, #0xa10]
    // 0xafebd4: ArrayStore: r1[0] = r3  ; List_4
    //     0xafebd4: stur            w3, [x1, #0x17]
    // 0xafebd8: r4 = Instance_CrossAxisAlignment
    //     0xafebd8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafebdc: ldr             x4, [x4, #0x890]
    // 0xafebe0: StoreField: r1->field_1b = r4
    //     0xafebe0: stur            w4, [x1, #0x1b]
    // 0xafebe4: r4 = Instance_VerticalDirection
    //     0xafebe4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafebe8: ldr             x4, [x4, #0xa20]
    // 0xafebec: StoreField: r1->field_23 = r4
    //     0xafebec: stur            w4, [x1, #0x23]
    // 0xafebf0: r5 = Instance_Clip
    //     0xafebf0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafebf4: ldr             x5, [x5, #0x38]
    // 0xafebf8: StoreField: r1->field_2b = r5
    //     0xafebf8: stur            w5, [x1, #0x2b]
    // 0xafebfc: StoreField: r1->field_2f = rZR
    //     0xafebfc: stur            xzr, [x1, #0x2f]
    // 0xafec00: ldur            x6, [fp, #-0x18]
    // 0xafec04: StoreField: r1->field_b = r6
    //     0xafec04: stur            w6, [x1, #0xb]
    // 0xafec08: r0 = Padding()
    //     0xafec08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafec0c: mov             x2, x0
    // 0xafec10: r0 = Instance_EdgeInsets
    //     0xafec10: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xafec14: ldr             x0, [x0, #0xa78]
    // 0xafec18: stur            x2, [fp, #-0x18]
    // 0xafec1c: StoreField: r2->field_f = r0
    //     0xafec1c: stur            w0, [x2, #0xf]
    // 0xafec20: ldur            x0, [fp, #-0x38]
    // 0xafec24: StoreField: r2->field_b = r0
    //     0xafec24: stur            w0, [x2, #0xb]
    // 0xafec28: r1 = <FlexParentData>
    //     0xafec28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xafec2c: ldr             x1, [x1, #0xe00]
    // 0xafec30: r0 = Expanded()
    //     0xafec30: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xafec34: mov             x3, x0
    // 0xafec38: r0 = 1
    //     0xafec38: movz            x0, #0x1
    // 0xafec3c: stur            x3, [fp, #-0x38]
    // 0xafec40: StoreField: r3->field_13 = r0
    //     0xafec40: stur            x0, [x3, #0x13]
    // 0xafec44: r0 = Instance_FlexFit
    //     0xafec44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xafec48: ldr             x0, [x0, #0xe08]
    // 0xafec4c: StoreField: r3->field_1b = r0
    //     0xafec4c: stur            w0, [x3, #0x1b]
    // 0xafec50: ldur            x0, [fp, #-0x18]
    // 0xafec54: StoreField: r3->field_b = r0
    //     0xafec54: stur            w0, [x3, #0xb]
    // 0xafec58: r1 = Null
    //     0xafec58: mov             x1, NULL
    // 0xafec5c: r2 = 6
    //     0xafec5c: movz            x2, #0x6
    // 0xafec60: r0 = AllocateArray()
    //     0xafec60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafec64: mov             x2, x0
    // 0xafec68: ldur            x0, [fp, #-0x10]
    // 0xafec6c: stur            x2, [fp, #-0x18]
    // 0xafec70: StoreField: r2->field_f = r0
    //     0xafec70: stur            w0, [x2, #0xf]
    // 0xafec74: ldur            x0, [fp, #-0x30]
    // 0xafec78: StoreField: r2->field_13 = r0
    //     0xafec78: stur            w0, [x2, #0x13]
    // 0xafec7c: ldur            x0, [fp, #-0x38]
    // 0xafec80: ArrayStore: r2[0] = r0  ; List_4
    //     0xafec80: stur            w0, [x2, #0x17]
    // 0xafec84: r1 = <Widget>
    //     0xafec84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafec88: r0 = AllocateGrowableArray()
    //     0xafec88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafec8c: mov             x1, x0
    // 0xafec90: ldur            x0, [fp, #-0x18]
    // 0xafec94: stur            x1, [fp, #-0x10]
    // 0xafec98: StoreField: r1->field_f = r0
    //     0xafec98: stur            w0, [x1, #0xf]
    // 0xafec9c: r0 = 6
    //     0xafec9c: movz            x0, #0x6
    // 0xafeca0: StoreField: r1->field_b = r0
    //     0xafeca0: stur            w0, [x1, #0xb]
    // 0xafeca4: r0 = Row()
    //     0xafeca4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafeca8: mov             x1, x0
    // 0xafecac: r0 = Instance_Axis
    //     0xafecac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafecb0: stur            x1, [fp, #-0x18]
    // 0xafecb4: StoreField: r1->field_f = r0
    //     0xafecb4: stur            w0, [x1, #0xf]
    // 0xafecb8: r0 = Instance_MainAxisAlignment
    //     0xafecb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafecbc: ldr             x0, [x0, #0xa08]
    // 0xafecc0: StoreField: r1->field_13 = r0
    //     0xafecc0: stur            w0, [x1, #0x13]
    // 0xafecc4: r2 = Instance_MainAxisSize
    //     0xafecc4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafecc8: ldr             x2, [x2, #0xa10]
    // 0xafeccc: ArrayStore: r1[0] = r2  ; List_4
    //     0xafeccc: stur            w2, [x1, #0x17]
    // 0xafecd0: r3 = Instance_CrossAxisAlignment
    //     0xafecd0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafecd4: ldr             x3, [x3, #0xa18]
    // 0xafecd8: StoreField: r1->field_1b = r3
    //     0xafecd8: stur            w3, [x1, #0x1b]
    // 0xafecdc: r4 = Instance_VerticalDirection
    //     0xafecdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafece0: ldr             x4, [x4, #0xa20]
    // 0xafece4: StoreField: r1->field_23 = r4
    //     0xafece4: stur            w4, [x1, #0x23]
    // 0xafece8: r5 = Instance_Clip
    //     0xafece8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafecec: ldr             x5, [x5, #0x38]
    // 0xafecf0: StoreField: r1->field_2b = r5
    //     0xafecf0: stur            w5, [x1, #0x2b]
    // 0xafecf4: StoreField: r1->field_2f = rZR
    //     0xafecf4: stur            xzr, [x1, #0x2f]
    // 0xafecf8: ldur            x6, [fp, #-0x10]
    // 0xafecfc: StoreField: r1->field_b = r6
    //     0xafecfc: stur            w6, [x1, #0xb]
    // 0xafed00: r0 = Container()
    //     0xafed00: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafed04: stur            x0, [fp, #-0x10]
    // 0xafed08: ldur            x16, [fp, #-0x28]
    // 0xafed0c: ldur            lr, [fp, #-0x18]
    // 0xafed10: stp             lr, x16, [SP]
    // 0xafed14: mov             x1, x0
    // 0xafed18: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xafed18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xafed1c: ldr             x4, [x4, #0x88]
    // 0xafed20: r0 = Container()
    //     0xafed20: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafed24: r0 = Padding()
    //     0xafed24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafed28: mov             x1, x0
    // 0xafed2c: r0 = Instance_EdgeInsets
    //     0xafed2c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xafed30: ldr             x0, [x0, #0x770]
    // 0xafed34: StoreField: r1->field_f = r0
    //     0xafed34: stur            w0, [x1, #0xf]
    // 0xafed38: ldur            x0, [fp, #-0x10]
    // 0xafed3c: StoreField: r1->field_b = r0
    //     0xafed3c: stur            w0, [x1, #0xb]
    // 0xafed40: mov             x2, x1
    // 0xafed44: ldur            x0, [fp, #-0x50]
    // 0xafed48: ldur            x1, [fp, #-8]
    // 0xafed4c: stur            x2, [fp, #-0x10]
    // 0xafed50: r0 = Visibility()
    //     0xafed50: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafed54: mov             x3, x0
    // 0xafed58: ldur            x0, [fp, #-0x10]
    // 0xafed5c: stur            x3, [fp, #-0x18]
    // 0xafed60: StoreField: r3->field_b = r0
    //     0xafed60: stur            w0, [x3, #0xb]
    // 0xafed64: r0 = Instance_SizedBox
    //     0xafed64: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafed68: StoreField: r3->field_f = r0
    //     0xafed68: stur            w0, [x3, #0xf]
    // 0xafed6c: ldur            x0, [fp, #-8]
    // 0xafed70: StoreField: r3->field_13 = r0
    //     0xafed70: stur            w0, [x3, #0x13]
    // 0xafed74: r0 = false
    //     0xafed74: add             x0, NULL, #0x30  ; false
    // 0xafed78: ArrayStore: r3[0] = r0  ; List_4
    //     0xafed78: stur            w0, [x3, #0x17]
    // 0xafed7c: StoreField: r3->field_1b = r0
    //     0xafed7c: stur            w0, [x3, #0x1b]
    // 0xafed80: StoreField: r3->field_1f = r0
    //     0xafed80: stur            w0, [x3, #0x1f]
    // 0xafed84: StoreField: r3->field_23 = r0
    //     0xafed84: stur            w0, [x3, #0x23]
    // 0xafed88: StoreField: r3->field_27 = r0
    //     0xafed88: stur            w0, [x3, #0x27]
    // 0xafed8c: StoreField: r3->field_2b = r0
    //     0xafed8c: stur            w0, [x3, #0x2b]
    // 0xafed90: r1 = Null
    //     0xafed90: mov             x1, NULL
    // 0xafed94: r2 = 4
    //     0xafed94: movz            x2, #0x4
    // 0xafed98: r0 = AllocateArray()
    //     0xafed98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafed9c: mov             x2, x0
    // 0xafeda0: ldur            x0, [fp, #-0x50]
    // 0xafeda4: stur            x2, [fp, #-8]
    // 0xafeda8: StoreField: r2->field_f = r0
    //     0xafeda8: stur            w0, [x2, #0xf]
    // 0xafedac: ldur            x0, [fp, #-0x18]
    // 0xafedb0: StoreField: r2->field_13 = r0
    //     0xafedb0: stur            w0, [x2, #0x13]
    // 0xafedb4: r1 = <Widget>
    //     0xafedb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafedb8: r0 = AllocateGrowableArray()
    //     0xafedb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafedbc: mov             x1, x0
    // 0xafedc0: ldur            x0, [fp, #-8]
    // 0xafedc4: stur            x1, [fp, #-0x10]
    // 0xafedc8: StoreField: r1->field_f = r0
    //     0xafedc8: stur            w0, [x1, #0xf]
    // 0xafedcc: r0 = 4
    //     0xafedcc: movz            x0, #0x4
    // 0xafedd0: StoreField: r1->field_b = r0
    //     0xafedd0: stur            w0, [x1, #0xb]
    // 0xafedd4: r0 = Column()
    //     0xafedd4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafedd8: r1 = Instance_Axis
    //     0xafedd8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafeddc: StoreField: r0->field_f = r1
    //     0xafeddc: stur            w1, [x0, #0xf]
    // 0xafede0: r1 = Instance_MainAxisAlignment
    //     0xafede0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafede4: ldr             x1, [x1, #0xa08]
    // 0xafede8: StoreField: r0->field_13 = r1
    //     0xafede8: stur            w1, [x0, #0x13]
    // 0xafedec: r1 = Instance_MainAxisSize
    //     0xafedec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafedf0: ldr             x1, [x1, #0xa10]
    // 0xafedf4: ArrayStore: r0[0] = r1  ; List_4
    //     0xafedf4: stur            w1, [x0, #0x17]
    // 0xafedf8: r1 = Instance_CrossAxisAlignment
    //     0xafedf8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafedfc: ldr             x1, [x1, #0xa18]
    // 0xafee00: StoreField: r0->field_1b = r1
    //     0xafee00: stur            w1, [x0, #0x1b]
    // 0xafee04: r1 = Instance_VerticalDirection
    //     0xafee04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafee08: ldr             x1, [x1, #0xa20]
    // 0xafee0c: StoreField: r0->field_23 = r1
    //     0xafee0c: stur            w1, [x0, #0x23]
    // 0xafee10: r1 = Instance_Clip
    //     0xafee10: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafee14: ldr             x1, [x1, #0x38]
    // 0xafee18: StoreField: r0->field_2b = r1
    //     0xafee18: stur            w1, [x0, #0x2b]
    // 0xafee1c: StoreField: r0->field_2f = rZR
    //     0xafee1c: stur            xzr, [x0, #0x2f]
    // 0xafee20: ldur            x1, [fp, #-0x10]
    // 0xafee24: StoreField: r0->field_b = r1
    //     0xafee24: stur            w1, [x0, #0xb]
    // 0xafee28: LeaveFrame
    //     0xafee28: mov             SP, fp
    //     0xafee2c: ldp             fp, lr, [SP], #0x10
    // 0xafee30: ret
    //     0xafee30: ret             
    // 0xafee34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafee34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafee38: b               #0xafdad0
    // 0xafee3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee44: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafee78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafee78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafee7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafee7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xafee8c, size: 0x7c
    // 0xafee8c: EnterFrame
    //     0xafee8c: stp             fp, lr, [SP, #-0x10]!
    //     0xafee90: mov             fp, SP
    // 0xafee94: AllocStack(0x8)
    //     0xafee94: sub             SP, SP, #8
    // 0xafee98: SetupParameters()
    //     0xafee98: ldr             x0, [fp, #0x10]
    //     0xafee9c: ldur            w1, [x0, #0x17]
    //     0xafeea0: add             x1, x1, HEAP, lsl #32
    // 0xafeea4: CheckStackOverflow
    //     0xafeea4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafeea8: cmp             SP, x16
    //     0xafeeac: b.ls            #0xafeefc
    // 0xafeeb0: LoadField: r0 = r1->field_f
    //     0xafeeb0: ldur            w0, [x1, #0xf]
    // 0xafeeb4: DecompressPointer r0
    //     0xafeeb4: add             x0, x0, HEAP, lsl #32
    // 0xafeeb8: LoadField: r1 = r0->field_b
    //     0xafeeb8: ldur            w1, [x0, #0xb]
    // 0xafeebc: DecompressPointer r1
    //     0xafeebc: add             x1, x1, HEAP, lsl #32
    // 0xafeec0: cmp             w1, NULL
    // 0xafeec4: b.eq            #0xafef04
    // 0xafeec8: LoadField: r0 = r1->field_23
    //     0xafeec8: ldur            w0, [x1, #0x23]
    // 0xafeecc: DecompressPointer r0
    //     0xafeecc: add             x0, x0, HEAP, lsl #32
    // 0xafeed0: str             x0, [SP]
    // 0xafeed4: r4 = 0
    //     0xafeed4: movz            x4, #0
    // 0xafeed8: ldr             x0, [SP]
    // 0xafeedc: r16 = UnlinkedCall_0x613b5c
    //     0xafeedc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57fc8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafeee0: add             x16, x16, #0xfc8
    // 0xafeee4: ldp             x5, lr, [x16]
    // 0xafeee8: blr             lr
    // 0xafeeec: r0 = Null
    //     0xafeeec: mov             x0, NULL
    // 0xafeef0: LeaveFrame
    //     0xafeef0: mov             SP, fp
    //     0xafeef4: ldp             fp, lr, [SP], #0x10
    // 0xafeef8: ret
    //     0xafeef8: ret             
    // 0xafeefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafeefc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafef00: b               #0xafeeb0
    // 0xafef04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafef04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, double, Items, String) {
    // ** addr: 0xafef08, size: 0x8c
    // 0xafef08: EnterFrame
    //     0xafef08: stp             fp, lr, [SP, #-0x10]!
    //     0xafef0c: mov             fp, SP
    // 0xafef10: AllocStack(0x20)
    //     0xafef10: sub             SP, SP, #0x20
    // 0xafef14: SetupParameters()
    //     0xafef14: ldr             x0, [fp, #0x28]
    //     0xafef18: ldur            w1, [x0, #0x17]
    //     0xafef1c: add             x1, x1, HEAP, lsl #32
    // 0xafef20: CheckStackOverflow
    //     0xafef20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafef24: cmp             SP, x16
    //     0xafef28: b.ls            #0xafef88
    // 0xafef2c: LoadField: r0 = r1->field_f
    //     0xafef2c: ldur            w0, [x1, #0xf]
    // 0xafef30: DecompressPointer r0
    //     0xafef30: add             x0, x0, HEAP, lsl #32
    // 0xafef34: LoadField: r1 = r0->field_b
    //     0xafef34: ldur            w1, [x0, #0xb]
    // 0xafef38: DecompressPointer r1
    //     0xafef38: add             x1, x1, HEAP, lsl #32
    // 0xafef3c: cmp             w1, NULL
    // 0xafef40: b.eq            #0xafef90
    // 0xafef44: LoadField: r0 = r1->field_27
    //     0xafef44: ldur            w0, [x1, #0x27]
    // 0xafef48: DecompressPointer r0
    //     0xafef48: add             x0, x0, HEAP, lsl #32
    // 0xafef4c: ldr             x16, [fp, #0x20]
    // 0xafef50: stp             x16, x0, [SP, #0x10]
    // 0xafef54: ldr             x16, [fp, #0x18]
    // 0xafef58: ldr             lr, [fp, #0x10]
    // 0xafef5c: stp             lr, x16, [SP]
    // 0xafef60: r4 = 0
    //     0xafef60: movz            x4, #0
    // 0xafef64: ldr             x0, [SP, #0x18]
    // 0xafef68: r16 = UnlinkedCall_0x613b5c
    //     0xafef68: add             x16, PP, #0x57, lsl #12  ; [pp+0x57fd8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafef6c: add             x16, x16, #0xfd8
    // 0xafef70: ldp             x5, lr, [x16]
    // 0xafef74: blr             lr
    // 0xafef78: r0 = Null
    //     0xafef78: mov             x0, NULL
    // 0xafef7c: LeaveFrame
    //     0xafef7c: mov             SP, fp
    //     0xafef80: ldp             fp, lr, [SP], #0x10
    // 0xafef84: ret
    //     0xafef84: ret             
    // 0xafef88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafef88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafef8c: b               #0xafef2c
    // 0xafef90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafef90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4150, size: 0x2c, field offset: 0xc
//   const constructor, 
class OrderItemCard extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ddd4, size: 0x24
    // 0xc7ddd4: EnterFrame
    //     0xc7ddd4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ddd8: mov             fp, SP
    // 0xc7dddc: mov             x0, x1
    // 0xc7dde0: r1 = <OrderItemCard>
    //     0xc7dde0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ba8] TypeArguments: <OrderItemCard>
    //     0xc7dde4: ldr             x1, [x1, #0xba8]
    // 0xc7dde8: r0 = _OrderItemCardState()
    //     0xc7dde8: bl              #0xc7ddf8  ; Allocate_OrderItemCardStateStub -> _OrderItemCardState (size=0x14)
    // 0xc7ddec: LeaveFrame
    //     0xc7ddec: mov             SP, fp
    //     0xc7ddf0: ldp             fp, lr, [SP], #0x10
    // 0xc7ddf4: ret
    //     0xc7ddf4: ret             
  }
}
