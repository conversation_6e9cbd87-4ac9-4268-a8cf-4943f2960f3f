// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bill_detail_widget.dart

// class id: 1049360, size: 0x8
class :: {
}

// class id: 3372, size: 0x14, field offset: 0x14
class _BillDetailWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93df7c, size: 0x130
    // 0x93df7c: EnterFrame
    //     0x93df7c: stp             fp, lr, [SP, #-0x10]!
    //     0x93df80: mov             fp, SP
    // 0x93df84: AllocStack(0x18)
    //     0x93df84: sub             SP, SP, #0x18
    // 0x93df88: SetupParameters(_BillDetailWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x93df88: stur            x1, [fp, #-8]
    // 0x93df8c: CheckStackOverflow
    //     0x93df8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93df90: cmp             SP, x16
    //     0x93df94: b.ls            #0x93e0a0
    // 0x93df98: r1 = 1
    //     0x93df98: movz            x1, #0x1
    // 0x93df9c: r0 = AllocateContext()
    //     0x93df9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x93dfa0: mov             x1, x0
    // 0x93dfa4: ldur            x0, [fp, #-8]
    // 0x93dfa8: StoreField: r1->field_f = r0
    //     0x93dfa8: stur            w0, [x1, #0xf]
    // 0x93dfac: r0 = LoadStaticField(0x878)
    //     0x93dfac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93dfb0: ldr             x0, [x0, #0x10f0]
    // 0x93dfb4: cmp             w0, NULL
    // 0x93dfb8: b.eq            #0x93e0a8
    // 0x93dfbc: LoadField: r3 = r0->field_53
    //     0x93dfbc: ldur            w3, [x0, #0x53]
    // 0x93dfc0: DecompressPointer r3
    //     0x93dfc0: add             x3, x3, HEAP, lsl #32
    // 0x93dfc4: stur            x3, [fp, #-0x10]
    // 0x93dfc8: LoadField: r0 = r3->field_7
    //     0x93dfc8: ldur            w0, [x3, #7]
    // 0x93dfcc: DecompressPointer r0
    //     0x93dfcc: add             x0, x0, HEAP, lsl #32
    // 0x93dfd0: mov             x2, x1
    // 0x93dfd4: stur            x0, [fp, #-8]
    // 0x93dfd8: r1 = Function '<anonymous closure>':.
    //     0x93dfd8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57090] AnonymousClosure: (0x902f44), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::initState (0x945a78)
    //     0x93dfdc: ldr             x1, [x1, #0x90]
    // 0x93dfe0: r0 = AllocateClosure()
    //     0x93dfe0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93dfe4: ldur            x2, [fp, #-8]
    // 0x93dfe8: mov             x3, x0
    // 0x93dfec: r1 = Null
    //     0x93dfec: mov             x1, NULL
    // 0x93dff0: stur            x3, [fp, #-8]
    // 0x93dff4: cmp             w2, NULL
    // 0x93dff8: b.eq            #0x93e018
    // 0x93dffc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93dffc: ldur            w4, [x2, #0x17]
    // 0x93e000: DecompressPointer r4
    //     0x93e000: add             x4, x4, HEAP, lsl #32
    // 0x93e004: r8 = X0
    //     0x93e004: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93e008: LoadField: r9 = r4->field_7
    //     0x93e008: ldur            x9, [x4, #7]
    // 0x93e00c: r3 = Null
    //     0x93e00c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57098] Null
    //     0x93e010: ldr             x3, [x3, #0x98]
    // 0x93e014: blr             x9
    // 0x93e018: ldur            x0, [fp, #-0x10]
    // 0x93e01c: LoadField: r1 = r0->field_b
    //     0x93e01c: ldur            w1, [x0, #0xb]
    // 0x93e020: LoadField: r2 = r0->field_f
    //     0x93e020: ldur            w2, [x0, #0xf]
    // 0x93e024: DecompressPointer r2
    //     0x93e024: add             x2, x2, HEAP, lsl #32
    // 0x93e028: LoadField: r3 = r2->field_b
    //     0x93e028: ldur            w3, [x2, #0xb]
    // 0x93e02c: r2 = LoadInt32Instr(r1)
    //     0x93e02c: sbfx            x2, x1, #1, #0x1f
    // 0x93e030: stur            x2, [fp, #-0x18]
    // 0x93e034: r1 = LoadInt32Instr(r3)
    //     0x93e034: sbfx            x1, x3, #1, #0x1f
    // 0x93e038: cmp             x2, x1
    // 0x93e03c: b.ne            #0x93e048
    // 0x93e040: mov             x1, x0
    // 0x93e044: r0 = _growToNextCapacity()
    //     0x93e044: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93e048: ldur            x2, [fp, #-0x10]
    // 0x93e04c: ldur            x3, [fp, #-0x18]
    // 0x93e050: add             x4, x3, #1
    // 0x93e054: lsl             x5, x4, #1
    // 0x93e058: StoreField: r2->field_b = r5
    //     0x93e058: stur            w5, [x2, #0xb]
    // 0x93e05c: LoadField: r1 = r2->field_f
    //     0x93e05c: ldur            w1, [x2, #0xf]
    // 0x93e060: DecompressPointer r1
    //     0x93e060: add             x1, x1, HEAP, lsl #32
    // 0x93e064: ldur            x0, [fp, #-8]
    // 0x93e068: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93e068: add             x25, x1, x3, lsl #2
    //     0x93e06c: add             x25, x25, #0xf
    //     0x93e070: str             w0, [x25]
    //     0x93e074: tbz             w0, #0, #0x93e090
    //     0x93e078: ldurb           w16, [x1, #-1]
    //     0x93e07c: ldurb           w17, [x0, #-1]
    //     0x93e080: and             x16, x17, x16, lsr #2
    //     0x93e084: tst             x16, HEAP, lsr #32
    //     0x93e088: b.eq            #0x93e090
    //     0x93e08c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93e090: r0 = Null
    //     0x93e090: mov             x0, NULL
    // 0x93e094: LeaveFrame
    //     0x93e094: mov             SP, fp
    //     0x93e098: ldp             fp, lr, [SP], #0x10
    // 0x93e09c: ret
    //     0x93e09c: ret             
    // 0x93e0a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93e0a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93e0a4: b               #0x93df98
    // 0x93e0a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93e0a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleChildRenderObjectWidget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9fd824, size: 0x158
    // 0x9fd824: EnterFrame
    //     0x9fd824: stp             fp, lr, [SP, #-0x10]!
    //     0x9fd828: mov             fp, SP
    // 0x9fd82c: AllocStack(0x18)
    //     0x9fd82c: sub             SP, SP, #0x18
    // 0x9fd830: SetupParameters()
    //     0x9fd830: ldr             x0, [fp, #0x20]
    //     0x9fd834: ldur            w1, [x0, #0x17]
    //     0x9fd838: add             x1, x1, HEAP, lsl #32
    // 0x9fd83c: CheckStackOverflow
    //     0x9fd83c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fd840: cmp             SP, x16
    //     0x9fd844: b.ls            #0x9fd970
    // 0x9fd848: LoadField: r0 = r1->field_f
    //     0x9fd848: ldur            w0, [x1, #0xf]
    // 0x9fd84c: DecompressPointer r0
    //     0x9fd84c: add             x0, x0, HEAP, lsl #32
    // 0x9fd850: LoadField: r1 = r0->field_b
    //     0x9fd850: ldur            w1, [x0, #0xb]
    // 0x9fd854: DecompressPointer r1
    //     0x9fd854: add             x1, x1, HEAP, lsl #32
    // 0x9fd858: cmp             w1, NULL
    // 0x9fd85c: b.eq            #0x9fd978
    // 0x9fd860: LoadField: r0 = r1->field_b
    //     0x9fd860: ldur            w0, [x1, #0xb]
    // 0x9fd864: DecompressPointer r0
    //     0x9fd864: add             x0, x0, HEAP, lsl #32
    // 0x9fd868: LoadField: r1 = r0->field_b
    //     0x9fd868: ldur            w1, [x0, #0xb]
    // 0x9fd86c: DecompressPointer r1
    //     0x9fd86c: add             x1, x1, HEAP, lsl #32
    // 0x9fd870: cmp             w1, NULL
    // 0x9fd874: b.ne            #0x9fd880
    // 0x9fd878: r0 = Null
    //     0x9fd878: mov             x0, NULL
    // 0x9fd87c: b               #0x9fd8a4
    // 0x9fd880: LoadField: r0 = r1->field_1b
    //     0x9fd880: ldur            w0, [x1, #0x1b]
    // 0x9fd884: DecompressPointer r0
    //     0x9fd884: add             x0, x0, HEAP, lsl #32
    // 0x9fd888: cmp             w0, NULL
    // 0x9fd88c: b.ne            #0x9fd898
    // 0x9fd890: r0 = Null
    //     0x9fd890: mov             x0, NULL
    // 0x9fd894: b               #0x9fd8a4
    // 0x9fd898: LoadField: r1 = r0->field_f
    //     0x9fd898: ldur            w1, [x0, #0xf]
    // 0x9fd89c: DecompressPointer r1
    //     0x9fd89c: add             x1, x1, HEAP, lsl #32
    // 0x9fd8a0: mov             x0, x1
    // 0x9fd8a4: cmp             w0, NULL
    // 0x9fd8a8: b.ne            #0x9fd8bc
    // 0x9fd8ac: r1 = <Entities>
    //     0x9fd8ac: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0x9fd8b0: ldr             x1, [x1, #0xea8]
    // 0x9fd8b4: r2 = 0
    //     0x9fd8b4: movz            x2, #0
    // 0x9fd8b8: r0 = AllocateArray()
    //     0x9fd8b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9fd8bc: ldr             x1, [fp, #0x10]
    // 0x9fd8c0: r2 = LoadClassIdInstr(r0)
    //     0x9fd8c0: ldur            x2, [x0, #-1]
    //     0x9fd8c4: ubfx            x2, x2, #0xc, #0x14
    // 0x9fd8c8: str             x0, [SP]
    // 0x9fd8cc: mov             x0, x2
    // 0x9fd8d0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x9fd8d0: movz            x17, #0xc898
    //     0x9fd8d4: add             lr, x0, x17
    //     0x9fd8d8: ldr             lr, [x21, lr, lsl #3]
    //     0x9fd8dc: blr             lr
    // 0x9fd8e0: r1 = LoadInt32Instr(r0)
    //     0x9fd8e0: sbfx            x1, x0, #1, #0x1f
    // 0x9fd8e4: sub             x0, x1, #2
    // 0x9fd8e8: ldr             x1, [fp, #0x10]
    // 0x9fd8ec: r2 = LoadInt32Instr(r1)
    //     0x9fd8ec: sbfx            x2, x1, #1, #0x1f
    //     0x9fd8f0: tbz             w1, #0, #0x9fd8f8
    //     0x9fd8f4: ldur            x2, [x1, #7]
    // 0x9fd8f8: cmp             x2, x0
    // 0x9fd8fc: b.ge            #0x9fd90c
    // 0x9fd900: r0 = Instance_SizedBox
    //     0x9fd900: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x9fd904: ldr             x0, [x0, #0x8f0]
    // 0x9fd908: b               #0x9fd964
    // 0x9fd90c: ldr             x1, [fp, #0x18]
    // 0x9fd910: r0 = of()
    //     0x9fd910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9fd914: LoadField: r1 = r0->field_5b
    //     0x9fd914: ldur            w1, [x0, #0x5b]
    // 0x9fd918: DecompressPointer r1
    //     0x9fd918: add             x1, x1, HEAP, lsl #32
    // 0x9fd91c: r0 = LoadClassIdInstr(r1)
    //     0x9fd91c: ldur            x0, [x1, #-1]
    //     0x9fd920: ubfx            x0, x0, #0xc, #0x14
    // 0x9fd924: d0 = 0.100000
    //     0x9fd924: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x9fd928: r0 = GDT[cid_x0 + -0xffa]()
    //     0x9fd928: sub             lr, x0, #0xffa
    //     0x9fd92c: ldr             lr, [x21, lr, lsl #3]
    //     0x9fd930: blr             lr
    // 0x9fd934: stur            x0, [fp, #-8]
    // 0x9fd938: r0 = Divider()
    //     0x9fd938: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x9fd93c: mov             x1, x0
    // 0x9fd940: ldur            x0, [fp, #-8]
    // 0x9fd944: stur            x1, [fp, #-0x10]
    // 0x9fd948: StoreField: r1->field_1f = r0
    //     0x9fd948: stur            w0, [x1, #0x1f]
    // 0x9fd94c: r0 = Padding()
    //     0x9fd94c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9fd950: r1 = Instance_EdgeInsets
    //     0x9fd950: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb28] Obj!EdgeInsets@d577d1
    //     0x9fd954: ldr             x1, [x1, #0xb28]
    // 0x9fd958: StoreField: r0->field_f = r1
    //     0x9fd958: stur            w1, [x0, #0xf]
    // 0x9fd95c: ldur            x1, [fp, #-0x10]
    // 0x9fd960: StoreField: r0->field_b = r1
    //     0x9fd960: stur            w1, [x0, #0xb]
    // 0x9fd964: LeaveFrame
    //     0x9fd964: mov             SP, fp
    //     0x9fd968: ldp             fp, lr, [SP], #0x10
    // 0x9fd96c: ret
    //     0x9fd96c: ret             
    // 0x9fd970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fd970: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fd974: b               #0x9fd848
    // 0x9fd978: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fd978: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x9fd97c, size: 0x918
    // 0x9fd97c: EnterFrame
    //     0x9fd97c: stp             fp, lr, [SP, #-0x10]!
    //     0x9fd980: mov             fp, SP
    // 0x9fd984: AllocStack(0x30)
    //     0x9fd984: sub             SP, SP, #0x30
    // 0x9fd988: SetupParameters()
    //     0x9fd988: ldr             x0, [fp, #0x20]
    //     0x9fd98c: ldur            w2, [x0, #0x17]
    //     0x9fd990: add             x2, x2, HEAP, lsl #32
    //     0x9fd994: stur            x2, [fp, #-0x10]
    // 0x9fd998: CheckStackOverflow
    //     0x9fd998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9fd99c: cmp             SP, x16
    //     0x9fd9a0: b.ls            #0x9fe254
    // 0x9fd9a4: LoadField: r0 = r2->field_f
    //     0x9fd9a4: ldur            w0, [x2, #0xf]
    // 0x9fd9a8: DecompressPointer r0
    //     0x9fd9a8: add             x0, x0, HEAP, lsl #32
    // 0x9fd9ac: LoadField: r1 = r0->field_b
    //     0x9fd9ac: ldur            w1, [x0, #0xb]
    // 0x9fd9b0: DecompressPointer r1
    //     0x9fd9b0: add             x1, x1, HEAP, lsl #32
    // 0x9fd9b4: cmp             w1, NULL
    // 0x9fd9b8: b.eq            #0x9fe25c
    // 0x9fd9bc: LoadField: r0 = r1->field_b
    //     0x9fd9bc: ldur            w0, [x1, #0xb]
    // 0x9fd9c0: DecompressPointer r0
    //     0x9fd9c0: add             x0, x0, HEAP, lsl #32
    // 0x9fd9c4: LoadField: r1 = r0->field_b
    //     0x9fd9c4: ldur            w1, [x0, #0xb]
    // 0x9fd9c8: DecompressPointer r1
    //     0x9fd9c8: add             x1, x1, HEAP, lsl #32
    // 0x9fd9cc: cmp             w1, NULL
    // 0x9fd9d0: b.ne            #0x9fd9e0
    // 0x9fd9d4: ldr             x4, [fp, #0x10]
    // 0x9fd9d8: r0 = Null
    //     0x9fd9d8: mov             x0, NULL
    // 0x9fd9dc: b               #0x9fda5c
    // 0x9fd9e0: LoadField: r0 = r1->field_1b
    //     0x9fd9e0: ldur            w0, [x1, #0x1b]
    // 0x9fd9e4: DecompressPointer r0
    //     0x9fd9e4: add             x0, x0, HEAP, lsl #32
    // 0x9fd9e8: cmp             w0, NULL
    // 0x9fd9ec: b.ne            #0x9fd9fc
    // 0x9fd9f0: ldr             x4, [fp, #0x10]
    // 0x9fd9f4: r0 = Null
    //     0x9fd9f4: mov             x0, NULL
    // 0x9fd9f8: b               #0x9fda5c
    // 0x9fd9fc: LoadField: r3 = r0->field_f
    //     0x9fd9fc: ldur            w3, [x0, #0xf]
    // 0x9fda00: DecompressPointer r3
    //     0x9fda00: add             x3, x3, HEAP, lsl #32
    // 0x9fda04: cmp             w3, NULL
    // 0x9fda08: b.ne            #0x9fda18
    // 0x9fda0c: ldr             x4, [fp, #0x10]
    // 0x9fda10: r0 = Null
    //     0x9fda10: mov             x0, NULL
    // 0x9fda14: b               #0x9fda5c
    // 0x9fda18: ldr             x4, [fp, #0x10]
    // 0x9fda1c: LoadField: r0 = r3->field_b
    //     0x9fda1c: ldur            w0, [x3, #0xb]
    // 0x9fda20: r5 = LoadInt32Instr(r4)
    //     0x9fda20: sbfx            x5, x4, #1, #0x1f
    //     0x9fda24: tbz             w4, #0, #0x9fda2c
    //     0x9fda28: ldur            x5, [x4, #7]
    // 0x9fda2c: r1 = LoadInt32Instr(r0)
    //     0x9fda2c: sbfx            x1, x0, #1, #0x1f
    // 0x9fda30: mov             x0, x1
    // 0x9fda34: mov             x1, x5
    // 0x9fda38: cmp             x1, x0
    // 0x9fda3c: b.hs            #0x9fe260
    // 0x9fda40: LoadField: r0 = r3->field_f
    //     0x9fda40: ldur            w0, [x3, #0xf]
    // 0x9fda44: DecompressPointer r0
    //     0x9fda44: add             x0, x0, HEAP, lsl #32
    // 0x9fda48: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9fda48: add             x16, x0, x5, lsl #2
    //     0x9fda4c: ldur            w1, [x16, #0xf]
    // 0x9fda50: DecompressPointer r1
    //     0x9fda50: add             x1, x1, HEAP, lsl #32
    // 0x9fda54: LoadField: r0 = r1->field_7
    //     0x9fda54: ldur            w0, [x1, #7]
    // 0x9fda58: DecompressPointer r0
    //     0x9fda58: add             x0, x0, HEAP, lsl #32
    // 0x9fda5c: cmp             w0, NULL
    // 0x9fda60: b.ne            #0x9fda68
    // 0x9fda64: r0 = ""
    //     0x9fda64: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9fda68: ldr             x1, [fp, #0x18]
    // 0x9fda6c: stur            x0, [fp, #-8]
    // 0x9fda70: r0 = of()
    //     0x9fda70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9fda74: LoadField: r1 = r0->field_87
    //     0x9fda74: ldur            w1, [x0, #0x87]
    // 0x9fda78: DecompressPointer r1
    //     0x9fda78: add             x1, x1, HEAP, lsl #32
    // 0x9fda7c: LoadField: r3 = r1->field_2b
    //     0x9fda7c: ldur            w3, [x1, #0x2b]
    // 0x9fda80: DecompressPointer r3
    //     0x9fda80: add             x3, x3, HEAP, lsl #32
    // 0x9fda84: ldur            x4, [fp, #-0x10]
    // 0x9fda88: stur            x3, [fp, #-0x18]
    // 0x9fda8c: LoadField: r0 = r4->field_f
    //     0x9fda8c: ldur            w0, [x4, #0xf]
    // 0x9fda90: DecompressPointer r0
    //     0x9fda90: add             x0, x0, HEAP, lsl #32
    // 0x9fda94: LoadField: r1 = r0->field_b
    //     0x9fda94: ldur            w1, [x0, #0xb]
    // 0x9fda98: DecompressPointer r1
    //     0x9fda98: add             x1, x1, HEAP, lsl #32
    // 0x9fda9c: cmp             w1, NULL
    // 0x9fdaa0: b.eq            #0x9fe264
    // 0x9fdaa4: LoadField: r0 = r1->field_b
    //     0x9fdaa4: ldur            w0, [x1, #0xb]
    // 0x9fdaa8: DecompressPointer r0
    //     0x9fdaa8: add             x0, x0, HEAP, lsl #32
    // 0x9fdaac: LoadField: r1 = r0->field_b
    //     0x9fdaac: ldur            w1, [x0, #0xb]
    // 0x9fdab0: DecompressPointer r1
    //     0x9fdab0: add             x1, x1, HEAP, lsl #32
    // 0x9fdab4: cmp             w1, NULL
    // 0x9fdab8: b.ne            #0x9fdac8
    // 0x9fdabc: ldr             x5, [fp, #0x10]
    // 0x9fdac0: r0 = Null
    //     0x9fdac0: mov             x0, NULL
    // 0x9fdac4: b               #0x9fdb44
    // 0x9fdac8: LoadField: r0 = r1->field_1b
    //     0x9fdac8: ldur            w0, [x1, #0x1b]
    // 0x9fdacc: DecompressPointer r0
    //     0x9fdacc: add             x0, x0, HEAP, lsl #32
    // 0x9fdad0: cmp             w0, NULL
    // 0x9fdad4: b.ne            #0x9fdae4
    // 0x9fdad8: ldr             x5, [fp, #0x10]
    // 0x9fdadc: r0 = Null
    //     0x9fdadc: mov             x0, NULL
    // 0x9fdae0: b               #0x9fdb44
    // 0x9fdae4: LoadField: r2 = r0->field_f
    //     0x9fdae4: ldur            w2, [x0, #0xf]
    // 0x9fdae8: DecompressPointer r2
    //     0x9fdae8: add             x2, x2, HEAP, lsl #32
    // 0x9fdaec: cmp             w2, NULL
    // 0x9fdaf0: b.ne            #0x9fdb00
    // 0x9fdaf4: ldr             x5, [fp, #0x10]
    // 0x9fdaf8: r0 = Null
    //     0x9fdaf8: mov             x0, NULL
    // 0x9fdafc: b               #0x9fdb44
    // 0x9fdb00: ldr             x5, [fp, #0x10]
    // 0x9fdb04: LoadField: r0 = r2->field_b
    //     0x9fdb04: ldur            w0, [x2, #0xb]
    // 0x9fdb08: r6 = LoadInt32Instr(r5)
    //     0x9fdb08: sbfx            x6, x5, #1, #0x1f
    //     0x9fdb0c: tbz             w5, #0, #0x9fdb14
    //     0x9fdb10: ldur            x6, [x5, #7]
    // 0x9fdb14: r1 = LoadInt32Instr(r0)
    //     0x9fdb14: sbfx            x1, x0, #1, #0x1f
    // 0x9fdb18: mov             x0, x1
    // 0x9fdb1c: mov             x1, x6
    // 0x9fdb20: cmp             x1, x0
    // 0x9fdb24: b.hs            #0x9fe268
    // 0x9fdb28: LoadField: r0 = r2->field_f
    //     0x9fdb28: ldur            w0, [x2, #0xf]
    // 0x9fdb2c: DecompressPointer r0
    //     0x9fdb2c: add             x0, x0, HEAP, lsl #32
    // 0x9fdb30: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x9fdb30: add             x16, x0, x6, lsl #2
    //     0x9fdb34: ldur            w1, [x16, #0xf]
    // 0x9fdb38: DecompressPointer r1
    //     0x9fdb38: add             x1, x1, HEAP, lsl #32
    // 0x9fdb3c: LoadField: r0 = r1->field_b
    //     0x9fdb3c: ldur            w0, [x1, #0xb]
    // 0x9fdb40: DecompressPointer r0
    //     0x9fdb40: add             x0, x0, HEAP, lsl #32
    // 0x9fdb44: cmp             w0, NULL
    // 0x9fdb48: b.ne            #0x9fdb54
    // 0x9fdb4c: r1 = ""
    //     0x9fdb4c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9fdb50: b               #0x9fdb58
    // 0x9fdb54: mov             x1, x0
    // 0x9fdb58: r0 = LoadClassIdInstr(r1)
    //     0x9fdb58: ldur            x0, [x1, #-1]
    //     0x9fdb5c: ubfx            x0, x0, #0xc, #0x14
    // 0x9fdb60: r2 = "-"
    //     0x9fdb60: ldr             x2, [PP, #0x31a8]  ; [pp+0x31a8] "-"
    // 0x9fdb64: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9fdb64: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9fdb68: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9fdb68: sub             lr, x0, #0xffe
    //     0x9fdb6c: ldr             lr, [x21, lr, lsl #3]
    //     0x9fdb70: blr             lr
    // 0x9fdb74: tbz             w0, #4, #0x9fdd5c
    // 0x9fdb78: ldur            x3, [fp, #-0x10]
    // 0x9fdb7c: LoadField: r0 = r3->field_f
    //     0x9fdb7c: ldur            w0, [x3, #0xf]
    // 0x9fdb80: DecompressPointer r0
    //     0x9fdb80: add             x0, x0, HEAP, lsl #32
    // 0x9fdb84: LoadField: r1 = r0->field_b
    //     0x9fdb84: ldur            w1, [x0, #0xb]
    // 0x9fdb88: DecompressPointer r1
    //     0x9fdb88: add             x1, x1, HEAP, lsl #32
    // 0x9fdb8c: cmp             w1, NULL
    // 0x9fdb90: b.eq            #0x9fe26c
    // 0x9fdb94: LoadField: r0 = r1->field_b
    //     0x9fdb94: ldur            w0, [x1, #0xb]
    // 0x9fdb98: DecompressPointer r0
    //     0x9fdb98: add             x0, x0, HEAP, lsl #32
    // 0x9fdb9c: LoadField: r1 = r0->field_b
    //     0x9fdb9c: ldur            w1, [x0, #0xb]
    // 0x9fdba0: DecompressPointer r1
    //     0x9fdba0: add             x1, x1, HEAP, lsl #32
    // 0x9fdba4: cmp             w1, NULL
    // 0x9fdba8: b.ne            #0x9fdbb8
    // 0x9fdbac: ldr             x4, [fp, #0x10]
    // 0x9fdbb0: r0 = Null
    //     0x9fdbb0: mov             x0, NULL
    // 0x9fdbb4: b               #0x9fdc34
    // 0x9fdbb8: LoadField: r0 = r1->field_1b
    //     0x9fdbb8: ldur            w0, [x1, #0x1b]
    // 0x9fdbbc: DecompressPointer r0
    //     0x9fdbbc: add             x0, x0, HEAP, lsl #32
    // 0x9fdbc0: cmp             w0, NULL
    // 0x9fdbc4: b.ne            #0x9fdbd4
    // 0x9fdbc8: ldr             x4, [fp, #0x10]
    // 0x9fdbcc: r0 = Null
    //     0x9fdbcc: mov             x0, NULL
    // 0x9fdbd0: b               #0x9fdc34
    // 0x9fdbd4: LoadField: r2 = r0->field_f
    //     0x9fdbd4: ldur            w2, [x0, #0xf]
    // 0x9fdbd8: DecompressPointer r2
    //     0x9fdbd8: add             x2, x2, HEAP, lsl #32
    // 0x9fdbdc: cmp             w2, NULL
    // 0x9fdbe0: b.ne            #0x9fdbf0
    // 0x9fdbe4: ldr             x4, [fp, #0x10]
    // 0x9fdbe8: r0 = Null
    //     0x9fdbe8: mov             x0, NULL
    // 0x9fdbec: b               #0x9fdc34
    // 0x9fdbf0: ldr             x4, [fp, #0x10]
    // 0x9fdbf4: LoadField: r0 = r2->field_b
    //     0x9fdbf4: ldur            w0, [x2, #0xb]
    // 0x9fdbf8: r5 = LoadInt32Instr(r4)
    //     0x9fdbf8: sbfx            x5, x4, #1, #0x1f
    //     0x9fdbfc: tbz             w4, #0, #0x9fdc04
    //     0x9fdc00: ldur            x5, [x4, #7]
    // 0x9fdc04: r1 = LoadInt32Instr(r0)
    //     0x9fdc04: sbfx            x1, x0, #1, #0x1f
    // 0x9fdc08: mov             x0, x1
    // 0x9fdc0c: mov             x1, x5
    // 0x9fdc10: cmp             x1, x0
    // 0x9fdc14: b.hs            #0x9fe270
    // 0x9fdc18: LoadField: r0 = r2->field_f
    //     0x9fdc18: ldur            w0, [x2, #0xf]
    // 0x9fdc1c: DecompressPointer r0
    //     0x9fdc1c: add             x0, x0, HEAP, lsl #32
    // 0x9fdc20: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9fdc20: add             x16, x0, x5, lsl #2
    //     0x9fdc24: ldur            w1, [x16, #0xf]
    // 0x9fdc28: DecompressPointer r1
    //     0x9fdc28: add             x1, x1, HEAP, lsl #32
    // 0x9fdc2c: LoadField: r0 = r1->field_7
    //     0x9fdc2c: ldur            w0, [x1, #7]
    // 0x9fdc30: DecompressPointer r0
    //     0x9fdc30: add             x0, x0, HEAP, lsl #32
    // 0x9fdc34: cmp             w0, NULL
    // 0x9fdc38: b.ne            #0x9fdc44
    // 0x9fdc3c: r1 = ""
    //     0x9fdc3c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9fdc40: b               #0x9fdc48
    // 0x9fdc44: mov             x1, x0
    // 0x9fdc48: r0 = LoadClassIdInstr(r1)
    //     0x9fdc48: ldur            x0, [x1, #-1]
    //     0x9fdc4c: ubfx            x0, x0, #0xc, #0x14
    // 0x9fdc50: r2 = "COD"
    //     0x9fdc50: add             x2, PP, #0x39, lsl #12  ; [pp+0x39838] "COD"
    //     0x9fdc54: ldr             x2, [x2, #0x838]
    // 0x9fdc58: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9fdc58: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9fdc5c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9fdc5c: sub             lr, x0, #0xffe
    //     0x9fdc60: ldr             lr, [x21, lr, lsl #3]
    //     0x9fdc64: blr             lr
    // 0x9fdc68: tbnz            w0, #4, #0x9fdc78
    // 0x9fdc6c: r0 = Instance_MaterialColor
    //     0x9fdc6c: add             x0, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x9fdc70: ldr             x0, [x0, #0x180]
    // 0x9fdc74: b               #0x9fdd54
    // 0x9fdc78: ldur            x0, [fp, #-0x10]
    // 0x9fdc7c: LoadField: r1 = r0->field_f
    //     0x9fdc7c: ldur            w1, [x0, #0xf]
    // 0x9fdc80: DecompressPointer r1
    //     0x9fdc80: add             x1, x1, HEAP, lsl #32
    // 0x9fdc84: LoadField: r2 = r1->field_b
    //     0x9fdc84: ldur            w2, [x1, #0xb]
    // 0x9fdc88: DecompressPointer r2
    //     0x9fdc88: add             x2, x2, HEAP, lsl #32
    // 0x9fdc8c: cmp             w2, NULL
    // 0x9fdc90: b.eq            #0x9fe274
    // 0x9fdc94: LoadField: r1 = r2->field_b
    //     0x9fdc94: ldur            w1, [x2, #0xb]
    // 0x9fdc98: DecompressPointer r1
    //     0x9fdc98: add             x1, x1, HEAP, lsl #32
    // 0x9fdc9c: LoadField: r2 = r1->field_b
    //     0x9fdc9c: ldur            w2, [x1, #0xb]
    // 0x9fdca0: DecompressPointer r2
    //     0x9fdca0: add             x2, x2, HEAP, lsl #32
    // 0x9fdca4: cmp             w2, NULL
    // 0x9fdca8: b.ne            #0x9fdcb4
    // 0x9fdcac: r1 = Null
    //     0x9fdcac: mov             x1, NULL
    // 0x9fdcb0: b               #0x9fdcd8
    // 0x9fdcb4: LoadField: r1 = r2->field_1b
    //     0x9fdcb4: ldur            w1, [x2, #0x1b]
    // 0x9fdcb8: DecompressPointer r1
    //     0x9fdcb8: add             x1, x1, HEAP, lsl #32
    // 0x9fdcbc: cmp             w1, NULL
    // 0x9fdcc0: b.ne            #0x9fdccc
    // 0x9fdcc4: r1 = Null
    //     0x9fdcc4: mov             x1, NULL
    // 0x9fdcc8: b               #0x9fdcd8
    // 0x9fdccc: LoadField: r2 = r1->field_f
    //     0x9fdccc: ldur            w2, [x1, #0xf]
    // 0x9fdcd0: DecompressPointer r2
    //     0x9fdcd0: add             x2, x2, HEAP, lsl #32
    // 0x9fdcd4: mov             x1, x2
    // 0x9fdcd8: cmp             w1, NULL
    // 0x9fdcdc: b.ne            #0x9fdcf4
    // 0x9fdce0: r1 = <Entities>
    //     0x9fdce0: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0x9fdce4: ldr             x1, [x1, #0xea8]
    // 0x9fdce8: r2 = 0
    //     0x9fdce8: movz            x2, #0
    // 0x9fdcec: r0 = AllocateArray()
    //     0x9fdcec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9fdcf0: b               #0x9fdcf8
    // 0x9fdcf4: mov             x0, x1
    // 0x9fdcf8: ldr             x1, [fp, #0x10]
    // 0x9fdcfc: r2 = LoadClassIdInstr(r0)
    //     0x9fdcfc: ldur            x2, [x0, #-1]
    //     0x9fdd00: ubfx            x2, x2, #0xc, #0x14
    // 0x9fdd04: str             x0, [SP]
    // 0x9fdd08: mov             x0, x2
    // 0x9fdd0c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x9fdd0c: movz            x17, #0xc898
    //     0x9fdd10: add             lr, x0, x17
    //     0x9fdd14: ldr             lr, [x21, lr, lsl #3]
    //     0x9fdd18: blr             lr
    // 0x9fdd1c: r1 = LoadInt32Instr(r0)
    //     0x9fdd1c: sbfx            x1, x0, #1, #0x1f
    // 0x9fdd20: sub             x0, x1, #1
    // 0x9fdd24: ldr             x2, [fp, #0x10]
    // 0x9fdd28: r1 = LoadInt32Instr(r2)
    //     0x9fdd28: sbfx            x1, x2, #1, #0x1f
    //     0x9fdd2c: tbz             w2, #0, #0x9fdd34
    //     0x9fdd30: ldur            x1, [x2, #7]
    // 0x9fdd34: cmp             x1, x0
    // 0x9fdd38: b.ge            #0x9fdd50
    // 0x9fdd3c: r1 = Instance_Color
    //     0x9fdd3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9fdd40: d0 = 0.700000
    //     0x9fdd40: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9fdd44: ldr             d0, [x17, #0xf48]
    // 0x9fdd48: r0 = withOpacity()
    //     0x9fdd48: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9fdd4c: b               #0x9fdd54
    // 0x9fdd50: r0 = Instance_Color
    //     0x9fdd50: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9fdd54: mov             x1, x0
    // 0x9fdd58: b               #0x9fdd64
    // 0x9fdd5c: r1 = Instance_Color
    //     0x9fdd5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9fdd60: ldr             x1, [x1, #0x858]
    // 0x9fdd64: ldur            x0, [fp, #-0x10]
    // 0x9fdd68: ldur            x2, [fp, #-8]
    // 0x9fdd6c: r16 = 12.000000
    //     0x9fdd6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9fdd70: ldr             x16, [x16, #0x9e8]
    // 0x9fdd74: stp             x16, x1, [SP]
    // 0x9fdd78: ldur            x1, [fp, #-0x18]
    // 0x9fdd7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9fdd7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9fdd80: ldr             x4, [x4, #0x9b8]
    // 0x9fdd84: r0 = copyWith()
    //     0x9fdd84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9fdd88: stur            x0, [fp, #-0x18]
    // 0x9fdd8c: r0 = Text()
    //     0x9fdd8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9fdd90: mov             x2, x0
    // 0x9fdd94: ldur            x0, [fp, #-8]
    // 0x9fdd98: stur            x2, [fp, #-0x20]
    // 0x9fdd9c: StoreField: r2->field_b = r0
    //     0x9fdd9c: stur            w0, [x2, #0xb]
    // 0x9fdda0: ldur            x0, [fp, #-0x18]
    // 0x9fdda4: StoreField: r2->field_13 = r0
    //     0x9fdda4: stur            w0, [x2, #0x13]
    // 0x9fdda8: ldur            x3, [fp, #-0x10]
    // 0x9fddac: LoadField: r0 = r3->field_f
    //     0x9fddac: ldur            w0, [x3, #0xf]
    // 0x9fddb0: DecompressPointer r0
    //     0x9fddb0: add             x0, x0, HEAP, lsl #32
    // 0x9fddb4: LoadField: r1 = r0->field_b
    //     0x9fddb4: ldur            w1, [x0, #0xb]
    // 0x9fddb8: DecompressPointer r1
    //     0x9fddb8: add             x1, x1, HEAP, lsl #32
    // 0x9fddbc: cmp             w1, NULL
    // 0x9fddc0: b.eq            #0x9fe278
    // 0x9fddc4: LoadField: r0 = r1->field_b
    //     0x9fddc4: ldur            w0, [x1, #0xb]
    // 0x9fddc8: DecompressPointer r0
    //     0x9fddc8: add             x0, x0, HEAP, lsl #32
    // 0x9fddcc: LoadField: r1 = r0->field_b
    //     0x9fddcc: ldur            w1, [x0, #0xb]
    // 0x9fddd0: DecompressPointer r1
    //     0x9fddd0: add             x1, x1, HEAP, lsl #32
    // 0x9fddd4: cmp             w1, NULL
    // 0x9fddd8: b.ne            #0x9fdde8
    // 0x9fdddc: ldr             x5, [fp, #0x10]
    // 0x9fdde0: r0 = Null
    //     0x9fdde0: mov             x0, NULL
    // 0x9fdde4: b               #0x9fde64
    // 0x9fdde8: LoadField: r0 = r1->field_1b
    //     0x9fdde8: ldur            w0, [x1, #0x1b]
    // 0x9fddec: DecompressPointer r0
    //     0x9fddec: add             x0, x0, HEAP, lsl #32
    // 0x9fddf0: cmp             w0, NULL
    // 0x9fddf4: b.ne            #0x9fde04
    // 0x9fddf8: ldr             x5, [fp, #0x10]
    // 0x9fddfc: r0 = Null
    //     0x9fddfc: mov             x0, NULL
    // 0x9fde00: b               #0x9fde64
    // 0x9fde04: LoadField: r4 = r0->field_f
    //     0x9fde04: ldur            w4, [x0, #0xf]
    // 0x9fde08: DecompressPointer r4
    //     0x9fde08: add             x4, x4, HEAP, lsl #32
    // 0x9fde0c: cmp             w4, NULL
    // 0x9fde10: b.ne            #0x9fde20
    // 0x9fde14: ldr             x5, [fp, #0x10]
    // 0x9fde18: r0 = Null
    //     0x9fde18: mov             x0, NULL
    // 0x9fde1c: b               #0x9fde64
    // 0x9fde20: ldr             x5, [fp, #0x10]
    // 0x9fde24: LoadField: r0 = r4->field_b
    //     0x9fde24: ldur            w0, [x4, #0xb]
    // 0x9fde28: r6 = LoadInt32Instr(r5)
    //     0x9fde28: sbfx            x6, x5, #1, #0x1f
    //     0x9fde2c: tbz             w5, #0, #0x9fde34
    //     0x9fde30: ldur            x6, [x5, #7]
    // 0x9fde34: r1 = LoadInt32Instr(r0)
    //     0x9fde34: sbfx            x1, x0, #1, #0x1f
    // 0x9fde38: mov             x0, x1
    // 0x9fde3c: mov             x1, x6
    // 0x9fde40: cmp             x1, x0
    // 0x9fde44: b.hs            #0x9fe27c
    // 0x9fde48: LoadField: r0 = r4->field_f
    //     0x9fde48: ldur            w0, [x4, #0xf]
    // 0x9fde4c: DecompressPointer r0
    //     0x9fde4c: add             x0, x0, HEAP, lsl #32
    // 0x9fde50: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x9fde50: add             x16, x0, x6, lsl #2
    //     0x9fde54: ldur            w1, [x16, #0xf]
    // 0x9fde58: DecompressPointer r1
    //     0x9fde58: add             x1, x1, HEAP, lsl #32
    // 0x9fde5c: LoadField: r0 = r1->field_b
    //     0x9fde5c: ldur            w0, [x1, #0xb]
    // 0x9fde60: DecompressPointer r0
    //     0x9fde60: add             x0, x0, HEAP, lsl #32
    // 0x9fde64: cmp             w0, NULL
    // 0x9fde68: b.ne            #0x9fde70
    // 0x9fde6c: r0 = ""
    //     0x9fde6c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9fde70: ldr             x1, [fp, #0x18]
    // 0x9fde74: stur            x0, [fp, #-8]
    // 0x9fde78: r0 = of()
    //     0x9fde78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9fde7c: LoadField: r1 = r0->field_87
    //     0x9fde7c: ldur            w1, [x0, #0x87]
    // 0x9fde80: DecompressPointer r1
    //     0x9fde80: add             x1, x1, HEAP, lsl #32
    // 0x9fde84: LoadField: r3 = r1->field_2b
    //     0x9fde84: ldur            w3, [x1, #0x2b]
    // 0x9fde88: DecompressPointer r3
    //     0x9fde88: add             x3, x3, HEAP, lsl #32
    // 0x9fde8c: ldur            x4, [fp, #-0x10]
    // 0x9fde90: stur            x3, [fp, #-0x18]
    // 0x9fde94: LoadField: r0 = r4->field_f
    //     0x9fde94: ldur            w0, [x4, #0xf]
    // 0x9fde98: DecompressPointer r0
    //     0x9fde98: add             x0, x0, HEAP, lsl #32
    // 0x9fde9c: LoadField: r1 = r0->field_b
    //     0x9fde9c: ldur            w1, [x0, #0xb]
    // 0x9fdea0: DecompressPointer r1
    //     0x9fdea0: add             x1, x1, HEAP, lsl #32
    // 0x9fdea4: cmp             w1, NULL
    // 0x9fdea8: b.eq            #0x9fe280
    // 0x9fdeac: LoadField: r0 = r1->field_b
    //     0x9fdeac: ldur            w0, [x1, #0xb]
    // 0x9fdeb0: DecompressPointer r0
    //     0x9fdeb0: add             x0, x0, HEAP, lsl #32
    // 0x9fdeb4: LoadField: r1 = r0->field_b
    //     0x9fdeb4: ldur            w1, [x0, #0xb]
    // 0x9fdeb8: DecompressPointer r1
    //     0x9fdeb8: add             x1, x1, HEAP, lsl #32
    // 0x9fdebc: cmp             w1, NULL
    // 0x9fdec0: b.ne            #0x9fded0
    // 0x9fdec4: ldr             x5, [fp, #0x10]
    // 0x9fdec8: r0 = Null
    //     0x9fdec8: mov             x0, NULL
    // 0x9fdecc: b               #0x9fdf4c
    // 0x9fded0: LoadField: r0 = r1->field_1b
    //     0x9fded0: ldur            w0, [x1, #0x1b]
    // 0x9fded4: DecompressPointer r0
    //     0x9fded4: add             x0, x0, HEAP, lsl #32
    // 0x9fded8: cmp             w0, NULL
    // 0x9fdedc: b.ne            #0x9fdeec
    // 0x9fdee0: ldr             x5, [fp, #0x10]
    // 0x9fdee4: r0 = Null
    //     0x9fdee4: mov             x0, NULL
    // 0x9fdee8: b               #0x9fdf4c
    // 0x9fdeec: LoadField: r2 = r0->field_f
    //     0x9fdeec: ldur            w2, [x0, #0xf]
    // 0x9fdef0: DecompressPointer r2
    //     0x9fdef0: add             x2, x2, HEAP, lsl #32
    // 0x9fdef4: cmp             w2, NULL
    // 0x9fdef8: b.ne            #0x9fdf08
    // 0x9fdefc: ldr             x5, [fp, #0x10]
    // 0x9fdf00: r0 = Null
    //     0x9fdf00: mov             x0, NULL
    // 0x9fdf04: b               #0x9fdf4c
    // 0x9fdf08: ldr             x5, [fp, #0x10]
    // 0x9fdf0c: LoadField: r0 = r2->field_b
    //     0x9fdf0c: ldur            w0, [x2, #0xb]
    // 0x9fdf10: r6 = LoadInt32Instr(r5)
    //     0x9fdf10: sbfx            x6, x5, #1, #0x1f
    //     0x9fdf14: tbz             w5, #0, #0x9fdf1c
    //     0x9fdf18: ldur            x6, [x5, #7]
    // 0x9fdf1c: r1 = LoadInt32Instr(r0)
    //     0x9fdf1c: sbfx            x1, x0, #1, #0x1f
    // 0x9fdf20: mov             x0, x1
    // 0x9fdf24: mov             x1, x6
    // 0x9fdf28: cmp             x1, x0
    // 0x9fdf2c: b.hs            #0x9fe284
    // 0x9fdf30: LoadField: r0 = r2->field_f
    //     0x9fdf30: ldur            w0, [x2, #0xf]
    // 0x9fdf34: DecompressPointer r0
    //     0x9fdf34: add             x0, x0, HEAP, lsl #32
    // 0x9fdf38: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x9fdf38: add             x16, x0, x6, lsl #2
    //     0x9fdf3c: ldur            w1, [x16, #0xf]
    // 0x9fdf40: DecompressPointer r1
    //     0x9fdf40: add             x1, x1, HEAP, lsl #32
    // 0x9fdf44: LoadField: r0 = r1->field_b
    //     0x9fdf44: ldur            w0, [x1, #0xb]
    // 0x9fdf48: DecompressPointer r0
    //     0x9fdf48: add             x0, x0, HEAP, lsl #32
    // 0x9fdf4c: cmp             w0, NULL
    // 0x9fdf50: b.ne            #0x9fdf5c
    // 0x9fdf54: r1 = ""
    //     0x9fdf54: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9fdf58: b               #0x9fdf60
    // 0x9fdf5c: mov             x1, x0
    // 0x9fdf60: r0 = LoadClassIdInstr(r1)
    //     0x9fdf60: ldur            x0, [x1, #-1]
    //     0x9fdf64: ubfx            x0, x0, #0xc, #0x14
    // 0x9fdf68: r2 = "-"
    //     0x9fdf68: ldr             x2, [PP, #0x31a8]  ; [pp+0x31a8] "-"
    // 0x9fdf6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9fdf6c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9fdf70: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9fdf70: sub             lr, x0, #0xffe
    //     0x9fdf74: ldr             lr, [x21, lr, lsl #3]
    //     0x9fdf78: blr             lr
    // 0x9fdf7c: tbz             w0, #4, #0x9fe158
    // 0x9fdf80: ldur            x3, [fp, #-0x10]
    // 0x9fdf84: LoadField: r0 = r3->field_f
    //     0x9fdf84: ldur            w0, [x3, #0xf]
    // 0x9fdf88: DecompressPointer r0
    //     0x9fdf88: add             x0, x0, HEAP, lsl #32
    // 0x9fdf8c: LoadField: r1 = r0->field_b
    //     0x9fdf8c: ldur            w1, [x0, #0xb]
    // 0x9fdf90: DecompressPointer r1
    //     0x9fdf90: add             x1, x1, HEAP, lsl #32
    // 0x9fdf94: cmp             w1, NULL
    // 0x9fdf98: b.eq            #0x9fe288
    // 0x9fdf9c: LoadField: r0 = r1->field_b
    //     0x9fdf9c: ldur            w0, [x1, #0xb]
    // 0x9fdfa0: DecompressPointer r0
    //     0x9fdfa0: add             x0, x0, HEAP, lsl #32
    // 0x9fdfa4: LoadField: r1 = r0->field_b
    //     0x9fdfa4: ldur            w1, [x0, #0xb]
    // 0x9fdfa8: DecompressPointer r1
    //     0x9fdfa8: add             x1, x1, HEAP, lsl #32
    // 0x9fdfac: cmp             w1, NULL
    // 0x9fdfb0: b.ne            #0x9fdfc0
    // 0x9fdfb4: ldr             x4, [fp, #0x10]
    // 0x9fdfb8: r0 = Null
    //     0x9fdfb8: mov             x0, NULL
    // 0x9fdfbc: b               #0x9fe03c
    // 0x9fdfc0: LoadField: r0 = r1->field_1b
    //     0x9fdfc0: ldur            w0, [x1, #0x1b]
    // 0x9fdfc4: DecompressPointer r0
    //     0x9fdfc4: add             x0, x0, HEAP, lsl #32
    // 0x9fdfc8: cmp             w0, NULL
    // 0x9fdfcc: b.ne            #0x9fdfdc
    // 0x9fdfd0: ldr             x4, [fp, #0x10]
    // 0x9fdfd4: r0 = Null
    //     0x9fdfd4: mov             x0, NULL
    // 0x9fdfd8: b               #0x9fe03c
    // 0x9fdfdc: LoadField: r2 = r0->field_f
    //     0x9fdfdc: ldur            w2, [x0, #0xf]
    // 0x9fdfe0: DecompressPointer r2
    //     0x9fdfe0: add             x2, x2, HEAP, lsl #32
    // 0x9fdfe4: cmp             w2, NULL
    // 0x9fdfe8: b.ne            #0x9fdff8
    // 0x9fdfec: ldr             x4, [fp, #0x10]
    // 0x9fdff0: r0 = Null
    //     0x9fdff0: mov             x0, NULL
    // 0x9fdff4: b               #0x9fe03c
    // 0x9fdff8: ldr             x4, [fp, #0x10]
    // 0x9fdffc: LoadField: r0 = r2->field_b
    //     0x9fdffc: ldur            w0, [x2, #0xb]
    // 0x9fe000: r5 = LoadInt32Instr(r4)
    //     0x9fe000: sbfx            x5, x4, #1, #0x1f
    //     0x9fe004: tbz             w4, #0, #0x9fe00c
    //     0x9fe008: ldur            x5, [x4, #7]
    // 0x9fe00c: r1 = LoadInt32Instr(r0)
    //     0x9fe00c: sbfx            x1, x0, #1, #0x1f
    // 0x9fe010: mov             x0, x1
    // 0x9fe014: mov             x1, x5
    // 0x9fe018: cmp             x1, x0
    // 0x9fe01c: b.hs            #0x9fe28c
    // 0x9fe020: LoadField: r0 = r2->field_f
    //     0x9fe020: ldur            w0, [x2, #0xf]
    // 0x9fe024: DecompressPointer r0
    //     0x9fe024: add             x0, x0, HEAP, lsl #32
    // 0x9fe028: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x9fe028: add             x16, x0, x5, lsl #2
    //     0x9fe02c: ldur            w1, [x16, #0xf]
    // 0x9fe030: DecompressPointer r1
    //     0x9fe030: add             x1, x1, HEAP, lsl #32
    // 0x9fe034: LoadField: r0 = r1->field_7
    //     0x9fe034: ldur            w0, [x1, #7]
    // 0x9fe038: DecompressPointer r0
    //     0x9fe038: add             x0, x0, HEAP, lsl #32
    // 0x9fe03c: cmp             w0, NULL
    // 0x9fe040: b.ne            #0x9fe04c
    // 0x9fe044: r1 = ""
    //     0x9fe044: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9fe048: b               #0x9fe050
    // 0x9fe04c: mov             x1, x0
    // 0x9fe050: r0 = LoadClassIdInstr(r1)
    //     0x9fe050: ldur            x0, [x1, #-1]
    //     0x9fe054: ubfx            x0, x0, #0xc, #0x14
    // 0x9fe058: r2 = "COD"
    //     0x9fe058: add             x2, PP, #0x39, lsl #12  ; [pp+0x39838] "COD"
    //     0x9fe05c: ldr             x2, [x2, #0x838]
    // 0x9fe060: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9fe060: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9fe064: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9fe064: sub             lr, x0, #0xffe
    //     0x9fe068: ldr             lr, [x21, lr, lsl #3]
    //     0x9fe06c: blr             lr
    // 0x9fe070: tbnz            w0, #4, #0x9fe080
    // 0x9fe074: r0 = Instance_MaterialColor
    //     0x9fe074: add             x0, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x9fe078: ldr             x0, [x0, #0x180]
    // 0x9fe07c: b               #0x9fe150
    // 0x9fe080: ldur            x0, [fp, #-0x10]
    // 0x9fe084: LoadField: r1 = r0->field_f
    //     0x9fe084: ldur            w1, [x0, #0xf]
    // 0x9fe088: DecompressPointer r1
    //     0x9fe088: add             x1, x1, HEAP, lsl #32
    // 0x9fe08c: LoadField: r0 = r1->field_b
    //     0x9fe08c: ldur            w0, [x1, #0xb]
    // 0x9fe090: DecompressPointer r0
    //     0x9fe090: add             x0, x0, HEAP, lsl #32
    // 0x9fe094: cmp             w0, NULL
    // 0x9fe098: b.eq            #0x9fe290
    // 0x9fe09c: LoadField: r1 = r0->field_b
    //     0x9fe09c: ldur            w1, [x0, #0xb]
    // 0x9fe0a0: DecompressPointer r1
    //     0x9fe0a0: add             x1, x1, HEAP, lsl #32
    // 0x9fe0a4: LoadField: r0 = r1->field_b
    //     0x9fe0a4: ldur            w0, [x1, #0xb]
    // 0x9fe0a8: DecompressPointer r0
    //     0x9fe0a8: add             x0, x0, HEAP, lsl #32
    // 0x9fe0ac: cmp             w0, NULL
    // 0x9fe0b0: b.ne            #0x9fe0bc
    // 0x9fe0b4: r0 = Null
    //     0x9fe0b4: mov             x0, NULL
    // 0x9fe0b8: b               #0x9fe0dc
    // 0x9fe0bc: LoadField: r1 = r0->field_1b
    //     0x9fe0bc: ldur            w1, [x0, #0x1b]
    // 0x9fe0c0: DecompressPointer r1
    //     0x9fe0c0: add             x1, x1, HEAP, lsl #32
    // 0x9fe0c4: cmp             w1, NULL
    // 0x9fe0c8: b.ne            #0x9fe0d4
    // 0x9fe0cc: r0 = Null
    //     0x9fe0cc: mov             x0, NULL
    // 0x9fe0d0: b               #0x9fe0dc
    // 0x9fe0d4: LoadField: r0 = r1->field_f
    //     0x9fe0d4: ldur            w0, [x1, #0xf]
    // 0x9fe0d8: DecompressPointer r0
    //     0x9fe0d8: add             x0, x0, HEAP, lsl #32
    // 0x9fe0dc: cmp             w0, NULL
    // 0x9fe0e0: b.ne            #0x9fe0f4
    // 0x9fe0e4: r1 = <Entities>
    //     0x9fe0e4: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0x9fe0e8: ldr             x1, [x1, #0xea8]
    // 0x9fe0ec: r2 = 0
    //     0x9fe0ec: movz            x2, #0
    // 0x9fe0f0: r0 = AllocateArray()
    //     0x9fe0f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9fe0f4: ldr             x1, [fp, #0x10]
    // 0x9fe0f8: r2 = LoadClassIdInstr(r0)
    //     0x9fe0f8: ldur            x2, [x0, #-1]
    //     0x9fe0fc: ubfx            x2, x2, #0xc, #0x14
    // 0x9fe100: str             x0, [SP]
    // 0x9fe104: mov             x0, x2
    // 0x9fe108: r0 = GDT[cid_x0 + 0xc898]()
    //     0x9fe108: movz            x17, #0xc898
    //     0x9fe10c: add             lr, x0, x17
    //     0x9fe110: ldr             lr, [x21, lr, lsl #3]
    //     0x9fe114: blr             lr
    // 0x9fe118: r1 = LoadInt32Instr(r0)
    //     0x9fe118: sbfx            x1, x0, #1, #0x1f
    // 0x9fe11c: sub             x0, x1, #1
    // 0x9fe120: ldr             x1, [fp, #0x10]
    // 0x9fe124: r2 = LoadInt32Instr(r1)
    //     0x9fe124: sbfx            x2, x1, #1, #0x1f
    //     0x9fe128: tbz             w1, #0, #0x9fe130
    //     0x9fe12c: ldur            x2, [x1, #7]
    // 0x9fe130: cmp             x2, x0
    // 0x9fe134: b.ge            #0x9fe14c
    // 0x9fe138: r1 = Instance_Color
    //     0x9fe138: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9fe13c: d0 = 0.700000
    //     0x9fe13c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x9fe140: ldr             d0, [x17, #0xf48]
    // 0x9fe144: r0 = withOpacity()
    //     0x9fe144: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9fe148: b               #0x9fe150
    // 0x9fe14c: r0 = Instance_Color
    //     0x9fe14c: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9fe150: mov             x1, x0
    // 0x9fe154: b               #0x9fe160
    // 0x9fe158: r1 = Instance_Color
    //     0x9fe158: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x9fe15c: ldr             x1, [x1, #0x858]
    // 0x9fe160: ldur            x0, [fp, #-0x20]
    // 0x9fe164: ldur            x2, [fp, #-8]
    // 0x9fe168: r16 = 12.000000
    //     0x9fe168: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9fe16c: ldr             x16, [x16, #0x9e8]
    // 0x9fe170: stp             x16, x1, [SP]
    // 0x9fe174: ldur            x1, [fp, #-0x18]
    // 0x9fe178: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9fe178: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9fe17c: ldr             x4, [x4, #0x9b8]
    // 0x9fe180: r0 = copyWith()
    //     0x9fe180: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9fe184: stur            x0, [fp, #-0x10]
    // 0x9fe188: r0 = Text()
    //     0x9fe188: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9fe18c: mov             x3, x0
    // 0x9fe190: ldur            x0, [fp, #-8]
    // 0x9fe194: stur            x3, [fp, #-0x18]
    // 0x9fe198: StoreField: r3->field_b = r0
    //     0x9fe198: stur            w0, [x3, #0xb]
    // 0x9fe19c: ldur            x0, [fp, #-0x10]
    // 0x9fe1a0: StoreField: r3->field_13 = r0
    //     0x9fe1a0: stur            w0, [x3, #0x13]
    // 0x9fe1a4: r1 = Null
    //     0x9fe1a4: mov             x1, NULL
    // 0x9fe1a8: r2 = 6
    //     0x9fe1a8: movz            x2, #0x6
    // 0x9fe1ac: r0 = AllocateArray()
    //     0x9fe1ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9fe1b0: mov             x2, x0
    // 0x9fe1b4: ldur            x0, [fp, #-0x20]
    // 0x9fe1b8: stur            x2, [fp, #-8]
    // 0x9fe1bc: StoreField: r2->field_f = r0
    //     0x9fe1bc: stur            w0, [x2, #0xf]
    // 0x9fe1c0: r16 = Instance_Spacer
    //     0x9fe1c0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x9fe1c4: ldr             x16, [x16, #0xf0]
    // 0x9fe1c8: StoreField: r2->field_13 = r16
    //     0x9fe1c8: stur            w16, [x2, #0x13]
    // 0x9fe1cc: ldur            x0, [fp, #-0x18]
    // 0x9fe1d0: ArrayStore: r2[0] = r0  ; List_4
    //     0x9fe1d0: stur            w0, [x2, #0x17]
    // 0x9fe1d4: r1 = <Widget>
    //     0x9fe1d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9fe1d8: r0 = AllocateGrowableArray()
    //     0x9fe1d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9fe1dc: mov             x1, x0
    // 0x9fe1e0: ldur            x0, [fp, #-8]
    // 0x9fe1e4: stur            x1, [fp, #-0x10]
    // 0x9fe1e8: StoreField: r1->field_f = r0
    //     0x9fe1e8: stur            w0, [x1, #0xf]
    // 0x9fe1ec: r0 = 6
    //     0x9fe1ec: movz            x0, #0x6
    // 0x9fe1f0: StoreField: r1->field_b = r0
    //     0x9fe1f0: stur            w0, [x1, #0xb]
    // 0x9fe1f4: r0 = Row()
    //     0x9fe1f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9fe1f8: r1 = Instance_Axis
    //     0x9fe1f8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9fe1fc: StoreField: r0->field_f = r1
    //     0x9fe1fc: stur            w1, [x0, #0xf]
    // 0x9fe200: r1 = Instance_MainAxisAlignment
    //     0x9fe200: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9fe204: ldr             x1, [x1, #0xa08]
    // 0x9fe208: StoreField: r0->field_13 = r1
    //     0x9fe208: stur            w1, [x0, #0x13]
    // 0x9fe20c: r1 = Instance_MainAxisSize
    //     0x9fe20c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9fe210: ldr             x1, [x1, #0xa10]
    // 0x9fe214: ArrayStore: r0[0] = r1  ; List_4
    //     0x9fe214: stur            w1, [x0, #0x17]
    // 0x9fe218: r1 = Instance_CrossAxisAlignment
    //     0x9fe218: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9fe21c: ldr             x1, [x1, #0xa18]
    // 0x9fe220: StoreField: r0->field_1b = r1
    //     0x9fe220: stur            w1, [x0, #0x1b]
    // 0x9fe224: r1 = Instance_VerticalDirection
    //     0x9fe224: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9fe228: ldr             x1, [x1, #0xa20]
    // 0x9fe22c: StoreField: r0->field_23 = r1
    //     0x9fe22c: stur            w1, [x0, #0x23]
    // 0x9fe230: r1 = Instance_Clip
    //     0x9fe230: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9fe234: ldr             x1, [x1, #0x38]
    // 0x9fe238: StoreField: r0->field_2b = r1
    //     0x9fe238: stur            w1, [x0, #0x2b]
    // 0x9fe23c: StoreField: r0->field_2f = rZR
    //     0x9fe23c: stur            xzr, [x0, #0x2f]
    // 0x9fe240: ldur            x1, [fp, #-0x10]
    // 0x9fe244: StoreField: r0->field_b = r1
    //     0x9fe244: stur            w1, [x0, #0xb]
    // 0x9fe248: LeaveFrame
    //     0x9fe248: mov             SP, fp
    //     0x9fe24c: ldp             fp, lr, [SP], #0x10
    // 0x9fe250: ret
    //     0x9fe250: ret             
    // 0x9fe254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9fe254: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9fe258: b               #0x9fd9a4
    // 0x9fe25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe260: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9fe260: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9fe264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe264: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe268: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9fe268: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9fe26c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe26c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe270: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9fe270: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9fe274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe274: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe278: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe27c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9fe27c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9fe280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe280: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe284: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9fe284: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9fe288: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe288: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9fe28c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9fe28c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9fe290: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9fe290: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb3c410, size: 0x5d0
    // 0xb3c410: EnterFrame
    //     0xb3c410: stp             fp, lr, [SP, #-0x10]!
    //     0xb3c414: mov             fp, SP
    // 0xb3c418: AllocStack(0x60)
    //     0xb3c418: sub             SP, SP, #0x60
    // 0xb3c41c: SetupParameters(_BillDetailWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb3c41c: mov             x0, x1
    //     0xb3c420: stur            x1, [fp, #-8]
    //     0xb3c424: mov             x1, x2
    //     0xb3c428: stur            x2, [fp, #-0x10]
    // 0xb3c42c: CheckStackOverflow
    //     0xb3c42c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3c430: cmp             SP, x16
    //     0xb3c434: b.ls            #0xb3c9c8
    // 0xb3c438: r1 = 1
    //     0xb3c438: movz            x1, #0x1
    // 0xb3c43c: r0 = AllocateContext()
    //     0xb3c43c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb3c440: mov             x2, x0
    // 0xb3c444: ldur            x0, [fp, #-8]
    // 0xb3c448: stur            x2, [fp, #-0x20]
    // 0xb3c44c: StoreField: r2->field_f = r0
    //     0xb3c44c: stur            w0, [x2, #0xf]
    // 0xb3c450: LoadField: r1 = r0->field_b
    //     0xb3c450: ldur            w1, [x0, #0xb]
    // 0xb3c454: DecompressPointer r1
    //     0xb3c454: add             x1, x1, HEAP, lsl #32
    // 0xb3c458: cmp             w1, NULL
    // 0xb3c45c: b.eq            #0xb3c9d0
    // 0xb3c460: LoadField: r3 = r1->field_b
    //     0xb3c460: ldur            w3, [x1, #0xb]
    // 0xb3c464: DecompressPointer r3
    //     0xb3c464: add             x3, x3, HEAP, lsl #32
    // 0xb3c468: LoadField: r1 = r3->field_b
    //     0xb3c468: ldur            w1, [x3, #0xb]
    // 0xb3c46c: DecompressPointer r1
    //     0xb3c46c: add             x1, x1, HEAP, lsl #32
    // 0xb3c470: cmp             w1, NULL
    // 0xb3c474: b.ne            #0xb3c480
    // 0xb3c478: r1 = Null
    //     0xb3c478: mov             x1, NULL
    // 0xb3c47c: b               #0xb3c4a0
    // 0xb3c480: LoadField: r3 = r1->field_1b
    //     0xb3c480: ldur            w3, [x1, #0x1b]
    // 0xb3c484: DecompressPointer r3
    //     0xb3c484: add             x3, x3, HEAP, lsl #32
    // 0xb3c488: cmp             w3, NULL
    // 0xb3c48c: b.ne            #0xb3c498
    // 0xb3c490: r1 = Null
    //     0xb3c490: mov             x1, NULL
    // 0xb3c494: b               #0xb3c4a0
    // 0xb3c498: LoadField: r1 = r3->field_7
    //     0xb3c498: ldur            w1, [x3, #7]
    // 0xb3c49c: DecompressPointer r1
    //     0xb3c49c: add             x1, x1, HEAP, lsl #32
    // 0xb3c4a0: cmp             w1, NULL
    // 0xb3c4a4: b.ne            #0xb3c4b0
    // 0xb3c4a8: r3 = ""
    //     0xb3c4a8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3c4ac: b               #0xb3c4b4
    // 0xb3c4b0: mov             x3, x1
    // 0xb3c4b4: ldur            x1, [fp, #-0x10]
    // 0xb3c4b8: stur            x3, [fp, #-0x18]
    // 0xb3c4bc: r0 = of()
    //     0xb3c4bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3c4c0: LoadField: r1 = r0->field_87
    //     0xb3c4c0: ldur            w1, [x0, #0x87]
    // 0xb3c4c4: DecompressPointer r1
    //     0xb3c4c4: add             x1, x1, HEAP, lsl #32
    // 0xb3c4c8: LoadField: r0 = r1->field_7
    //     0xb3c4c8: ldur            w0, [x1, #7]
    // 0xb3c4cc: DecompressPointer r0
    //     0xb3c4cc: add             x0, x0, HEAP, lsl #32
    // 0xb3c4d0: ldur            x1, [fp, #-0x10]
    // 0xb3c4d4: stur            x0, [fp, #-0x28]
    // 0xb3c4d8: r0 = of()
    //     0xb3c4d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3c4dc: LoadField: r1 = r0->field_5b
    //     0xb3c4dc: ldur            w1, [x0, #0x5b]
    // 0xb3c4e0: DecompressPointer r1
    //     0xb3c4e0: add             x1, x1, HEAP, lsl #32
    // 0xb3c4e4: r16 = 16.000000
    //     0xb3c4e4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb3c4e8: ldr             x16, [x16, #0x188]
    // 0xb3c4ec: stp             x1, x16, [SP]
    // 0xb3c4f0: ldur            x1, [fp, #-0x28]
    // 0xb3c4f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3c4f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3c4f8: ldr             x4, [x4, #0xaa0]
    // 0xb3c4fc: r0 = copyWith()
    //     0xb3c4fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3c500: stur            x0, [fp, #-0x28]
    // 0xb3c504: r0 = Text()
    //     0xb3c504: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3c508: mov             x2, x0
    // 0xb3c50c: ldur            x0, [fp, #-0x18]
    // 0xb3c510: stur            x2, [fp, #-0x30]
    // 0xb3c514: StoreField: r2->field_b = r0
    //     0xb3c514: stur            w0, [x2, #0xb]
    // 0xb3c518: ldur            x0, [fp, #-0x28]
    // 0xb3c51c: StoreField: r2->field_13 = r0
    //     0xb3c51c: stur            w0, [x2, #0x13]
    // 0xb3c520: ldur            x1, [fp, #-0x10]
    // 0xb3c524: r0 = of()
    //     0xb3c524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3c528: LoadField: r1 = r0->field_5b
    //     0xb3c528: ldur            w1, [x0, #0x5b]
    // 0xb3c52c: DecompressPointer r1
    //     0xb3c52c: add             x1, x1, HEAP, lsl #32
    // 0xb3c530: r0 = LoadClassIdInstr(r1)
    //     0xb3c530: ldur            x0, [x1, #-1]
    //     0xb3c534: ubfx            x0, x0, #0xc, #0x14
    // 0xb3c538: d0 = 0.070000
    //     0xb3c538: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb3c53c: ldr             d0, [x17, #0x5f8]
    // 0xb3c540: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb3c540: sub             lr, x0, #0xffa
    //     0xb3c544: ldr             lr, [x21, lr, lsl #3]
    //     0xb3c548: blr             lr
    // 0xb3c54c: stur            x0, [fp, #-0x18]
    // 0xb3c550: r0 = Divider()
    //     0xb3c550: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb3c554: mov             x2, x0
    // 0xb3c558: ldur            x0, [fp, #-0x18]
    // 0xb3c55c: stur            x2, [fp, #-0x28]
    // 0xb3c560: StoreField: r2->field_1f = r0
    //     0xb3c560: stur            w0, [x2, #0x1f]
    // 0xb3c564: ldur            x0, [fp, #-8]
    // 0xb3c568: LoadField: r1 = r0->field_b
    //     0xb3c568: ldur            w1, [x0, #0xb]
    // 0xb3c56c: DecompressPointer r1
    //     0xb3c56c: add             x1, x1, HEAP, lsl #32
    // 0xb3c570: cmp             w1, NULL
    // 0xb3c574: b.eq            #0xb3c9d4
    // 0xb3c578: LoadField: r3 = r1->field_b
    //     0xb3c578: ldur            w3, [x1, #0xb]
    // 0xb3c57c: DecompressPointer r3
    //     0xb3c57c: add             x3, x3, HEAP, lsl #32
    // 0xb3c580: LoadField: r1 = r3->field_b
    //     0xb3c580: ldur            w1, [x3, #0xb]
    // 0xb3c584: DecompressPointer r1
    //     0xb3c584: add             x1, x1, HEAP, lsl #32
    // 0xb3c588: cmp             w1, NULL
    // 0xb3c58c: b.ne            #0xb3c598
    // 0xb3c590: r1 = Null
    //     0xb3c590: mov             x1, NULL
    // 0xb3c594: b               #0xb3c5b8
    // 0xb3c598: LoadField: r3 = r1->field_1b
    //     0xb3c598: ldur            w3, [x1, #0x1b]
    // 0xb3c59c: DecompressPointer r3
    //     0xb3c59c: add             x3, x3, HEAP, lsl #32
    // 0xb3c5a0: cmp             w3, NULL
    // 0xb3c5a4: b.ne            #0xb3c5b0
    // 0xb3c5a8: r1 = Null
    //     0xb3c5a8: mov             x1, NULL
    // 0xb3c5ac: b               #0xb3c5b8
    // 0xb3c5b0: LoadField: r1 = r3->field_b
    //     0xb3c5b0: ldur            w1, [x3, #0xb]
    // 0xb3c5b4: DecompressPointer r1
    //     0xb3c5b4: add             x1, x1, HEAP, lsl #32
    // 0xb3c5b8: cmp             w1, NULL
    // 0xb3c5bc: b.ne            #0xb3c5c8
    // 0xb3c5c0: r3 = ""
    //     0xb3c5c0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb3c5c4: b               #0xb3c5cc
    // 0xb3c5c8: mov             x3, x1
    // 0xb3c5cc: ldur            x1, [fp, #-0x10]
    // 0xb3c5d0: stur            x3, [fp, #-0x18]
    // 0xb3c5d4: r0 = of()
    //     0xb3c5d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3c5d8: LoadField: r1 = r0->field_87
    //     0xb3c5d8: ldur            w1, [x0, #0x87]
    // 0xb3c5dc: DecompressPointer r1
    //     0xb3c5dc: add             x1, x1, HEAP, lsl #32
    // 0xb3c5e0: LoadField: r0 = r1->field_2b
    //     0xb3c5e0: ldur            w0, [x1, #0x2b]
    // 0xb3c5e4: DecompressPointer r0
    //     0xb3c5e4: add             x0, x0, HEAP, lsl #32
    // 0xb3c5e8: r16 = 12.000000
    //     0xb3c5e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb3c5ec: ldr             x16, [x16, #0x9e8]
    // 0xb3c5f0: r30 = Instance_Color
    //     0xb3c5f0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb3c5f4: ldr             lr, [lr, #0x858]
    // 0xb3c5f8: stp             lr, x16, [SP]
    // 0xb3c5fc: mov             x1, x0
    // 0xb3c600: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3c600: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3c604: ldr             x4, [x4, #0xaa0]
    // 0xb3c608: r0 = copyWith()
    //     0xb3c608: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3c60c: stur            x0, [fp, #-0x38]
    // 0xb3c610: r0 = Text()
    //     0xb3c610: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3c614: mov             x3, x0
    // 0xb3c618: ldur            x0, [fp, #-0x18]
    // 0xb3c61c: stur            x3, [fp, #-0x40]
    // 0xb3c620: StoreField: r3->field_b = r0
    //     0xb3c620: stur            w0, [x3, #0xb]
    // 0xb3c624: ldur            x0, [fp, #-0x38]
    // 0xb3c628: StoreField: r3->field_13 = r0
    //     0xb3c628: stur            w0, [x3, #0x13]
    // 0xb3c62c: ldur            x0, [fp, #-8]
    // 0xb3c630: LoadField: r1 = r0->field_b
    //     0xb3c630: ldur            w1, [x0, #0xb]
    // 0xb3c634: DecompressPointer r1
    //     0xb3c634: add             x1, x1, HEAP, lsl #32
    // 0xb3c638: cmp             w1, NULL
    // 0xb3c63c: b.eq            #0xb3c9d8
    // 0xb3c640: LoadField: r2 = r1->field_b
    //     0xb3c640: ldur            w2, [x1, #0xb]
    // 0xb3c644: DecompressPointer r2
    //     0xb3c644: add             x2, x2, HEAP, lsl #32
    // 0xb3c648: LoadField: r1 = r2->field_b
    //     0xb3c648: ldur            w1, [x2, #0xb]
    // 0xb3c64c: DecompressPointer r1
    //     0xb3c64c: add             x1, x1, HEAP, lsl #32
    // 0xb3c650: cmp             w1, NULL
    // 0xb3c654: b.ne            #0xb3c660
    // 0xb3c658: r1 = Null
    //     0xb3c658: mov             x1, NULL
    // 0xb3c65c: b               #0xb3c680
    // 0xb3c660: LoadField: r2 = r1->field_1b
    //     0xb3c660: ldur            w2, [x1, #0x1b]
    // 0xb3c664: DecompressPointer r2
    //     0xb3c664: add             x2, x2, HEAP, lsl #32
    // 0xb3c668: cmp             w2, NULL
    // 0xb3c66c: b.ne            #0xb3c678
    // 0xb3c670: r1 = Null
    //     0xb3c670: mov             x1, NULL
    // 0xb3c674: b               #0xb3c680
    // 0xb3c678: LoadField: r1 = r2->field_f
    //     0xb3c678: ldur            w1, [x2, #0xf]
    // 0xb3c67c: DecompressPointer r1
    //     0xb3c67c: add             x1, x1, HEAP, lsl #32
    // 0xb3c680: cmp             w1, NULL
    // 0xb3c684: b.ne            #0xb3c69c
    // 0xb3c688: r1 = <Entities>
    //     0xb3c688: add             x1, PP, #0x22, lsl #12  ; [pp+0x22ea8] TypeArguments: <Entities>
    //     0xb3c68c: ldr             x1, [x1, #0xea8]
    // 0xb3c690: r2 = 0
    //     0xb3c690: movz            x2, #0
    // 0xb3c694: r0 = AllocateArray()
    //     0xb3c694: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3c698: b               #0xb3c6a0
    // 0xb3c69c: mov             x0, x1
    // 0xb3c6a0: ldur            x2, [fp, #-8]
    // 0xb3c6a4: ldur            x3, [fp, #-0x28]
    // 0xb3c6a8: ldur            x1, [fp, #-0x40]
    // 0xb3c6ac: r4 = LoadClassIdInstr(r0)
    //     0xb3c6ac: ldur            x4, [x0, #-1]
    //     0xb3c6b0: ubfx            x4, x4, #0xc, #0x14
    // 0xb3c6b4: str             x0, [SP]
    // 0xb3c6b8: mov             x0, x4
    // 0xb3c6bc: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb3c6bc: movz            x17, #0xc898
    //     0xb3c6c0: add             lr, x0, x17
    //     0xb3c6c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb3c6c8: blr             lr
    // 0xb3c6cc: r3 = LoadInt32Instr(r0)
    //     0xb3c6cc: sbfx            x3, x0, #1, #0x1f
    // 0xb3c6d0: ldur            x2, [fp, #-0x20]
    // 0xb3c6d4: stur            x3, [fp, #-0x48]
    // 0xb3c6d8: r1 = Function '<anonymous closure>':.
    //     0xb3c6d8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57068] AnonymousClosure: (0x9fd97c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::build (0xb3c410)
    //     0xb3c6dc: ldr             x1, [x1, #0x68]
    // 0xb3c6e0: r0 = AllocateClosure()
    //     0xb3c6e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3c6e4: ldur            x2, [fp, #-0x20]
    // 0xb3c6e8: r1 = Function '<anonymous closure>':.
    //     0xb3c6e8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57070] AnonymousClosure: (0x9fd824), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::build (0xb3c410)
    //     0xb3c6ec: ldr             x1, [x1, #0x70]
    // 0xb3c6f0: stur            x0, [fp, #-0x18]
    // 0xb3c6f4: r0 = AllocateClosure()
    //     0xb3c6f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3c6f8: stur            x0, [fp, #-0x38]
    // 0xb3c6fc: r0 = ListView()
    //     0xb3c6fc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb3c700: stur            x0, [fp, #-0x50]
    // 0xb3c704: r16 = true
    //     0xb3c704: add             x16, NULL, #0x20  ; true
    // 0xb3c708: r30 = false
    //     0xb3c708: add             lr, NULL, #0x30  ; false
    // 0xb3c70c: stp             lr, x16, [SP]
    // 0xb3c710: mov             x1, x0
    // 0xb3c714: ldur            x2, [fp, #-0x18]
    // 0xb3c718: ldur            x3, [fp, #-0x48]
    // 0xb3c71c: ldur            x5, [fp, #-0x38]
    // 0xb3c720: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xb3c720: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xb3c724: ldr             x4, [x4, #0xc98]
    // 0xb3c728: r0 = ListView.separated()
    //     0xb3c728: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb3c72c: ldur            x0, [fp, #-8]
    // 0xb3c730: LoadField: r1 = r0->field_b
    //     0xb3c730: ldur            w1, [x0, #0xb]
    // 0xb3c734: DecompressPointer r1
    //     0xb3c734: add             x1, x1, HEAP, lsl #32
    // 0xb3c738: cmp             w1, NULL
    // 0xb3c73c: b.eq            #0xb3c9dc
    // 0xb3c740: LoadField: r0 = r1->field_b
    //     0xb3c740: ldur            w0, [x1, #0xb]
    // 0xb3c744: DecompressPointer r0
    //     0xb3c744: add             x0, x0, HEAP, lsl #32
    // 0xb3c748: stur            x0, [fp, #-8]
    // 0xb3c74c: r0 = DeliveryInfoWidget()
    //     0xb3c74c: bl              #0xb3c9e0  ; AllocateDeliveryInfoWidgetStub -> DeliveryInfoWidget (size=0x10)
    // 0xb3c750: mov             x3, x0
    // 0xb3c754: ldur            x0, [fp, #-8]
    // 0xb3c758: stur            x3, [fp, #-0x18]
    // 0xb3c75c: StoreField: r3->field_b = r0
    //     0xb3c75c: stur            w0, [x3, #0xb]
    // 0xb3c760: r1 = Null
    //     0xb3c760: mov             x1, NULL
    // 0xb3c764: r2 = 12
    //     0xb3c764: movz            x2, #0xc
    // 0xb3c768: r0 = AllocateArray()
    //     0xb3c768: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3c76c: mov             x2, x0
    // 0xb3c770: ldur            x0, [fp, #-0x28]
    // 0xb3c774: stur            x2, [fp, #-0x38]
    // 0xb3c778: StoreField: r2->field_f = r0
    //     0xb3c778: stur            w0, [x2, #0xf]
    // 0xb3c77c: ldur            x0, [fp, #-0x40]
    // 0xb3c780: StoreField: r2->field_13 = r0
    //     0xb3c780: stur            w0, [x2, #0x13]
    // 0xb3c784: r16 = Instance_SizedBox
    //     0xb3c784: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb3c788: ldr             x16, [x16, #0x328]
    // 0xb3c78c: ArrayStore: r2[0] = r16  ; List_4
    //     0xb3c78c: stur            w16, [x2, #0x17]
    // 0xb3c790: ldur            x0, [fp, #-0x50]
    // 0xb3c794: StoreField: r2->field_1b = r0
    //     0xb3c794: stur            w0, [x2, #0x1b]
    // 0xb3c798: r16 = Instance_SizedBox
    //     0xb3c798: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb3c79c: ldr             x16, [x16, #0x8f0]
    // 0xb3c7a0: StoreField: r2->field_1f = r16
    //     0xb3c7a0: stur            w16, [x2, #0x1f]
    // 0xb3c7a4: ldur            x0, [fp, #-0x18]
    // 0xb3c7a8: StoreField: r2->field_23 = r0
    //     0xb3c7a8: stur            w0, [x2, #0x23]
    // 0xb3c7ac: r1 = <Widget>
    //     0xb3c7ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3c7b0: r0 = AllocateGrowableArray()
    //     0xb3c7b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3c7b4: mov             x1, x0
    // 0xb3c7b8: ldur            x0, [fp, #-0x38]
    // 0xb3c7bc: stur            x1, [fp, #-0x18]
    // 0xb3c7c0: StoreField: r1->field_f = r0
    //     0xb3c7c0: stur            w0, [x1, #0xf]
    // 0xb3c7c4: r0 = 12
    //     0xb3c7c4: movz            x0, #0xc
    // 0xb3c7c8: StoreField: r1->field_b = r0
    //     0xb3c7c8: stur            w0, [x1, #0xb]
    // 0xb3c7cc: r0 = Column()
    //     0xb3c7cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3c7d0: mov             x2, x0
    // 0xb3c7d4: r0 = Instance_Axis
    //     0xb3c7d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3c7d8: stur            x2, [fp, #-0x28]
    // 0xb3c7dc: StoreField: r2->field_f = r0
    //     0xb3c7dc: stur            w0, [x2, #0xf]
    // 0xb3c7e0: r3 = Instance_MainAxisAlignment
    //     0xb3c7e0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3c7e4: ldr             x3, [x3, #0xa08]
    // 0xb3c7e8: StoreField: r2->field_13 = r3
    //     0xb3c7e8: stur            w3, [x2, #0x13]
    // 0xb3c7ec: r4 = Instance_MainAxisSize
    //     0xb3c7ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3c7f0: ldr             x4, [x4, #0xa10]
    // 0xb3c7f4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb3c7f4: stur            w4, [x2, #0x17]
    // 0xb3c7f8: r1 = Instance_CrossAxisAlignment
    //     0xb3c7f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb3c7fc: ldr             x1, [x1, #0x890]
    // 0xb3c800: StoreField: r2->field_1b = r1
    //     0xb3c800: stur            w1, [x2, #0x1b]
    // 0xb3c804: r5 = Instance_VerticalDirection
    //     0xb3c804: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3c808: ldr             x5, [x5, #0xa20]
    // 0xb3c80c: StoreField: r2->field_23 = r5
    //     0xb3c80c: stur            w5, [x2, #0x23]
    // 0xb3c810: r6 = Instance_Clip
    //     0xb3c810: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3c814: ldr             x6, [x6, #0x38]
    // 0xb3c818: StoreField: r2->field_2b = r6
    //     0xb3c818: stur            w6, [x2, #0x2b]
    // 0xb3c81c: StoreField: r2->field_2f = rZR
    //     0xb3c81c: stur            xzr, [x2, #0x2f]
    // 0xb3c820: ldur            x1, [fp, #-0x18]
    // 0xb3c824: StoreField: r2->field_b = r1
    //     0xb3c824: stur            w1, [x2, #0xb]
    // 0xb3c828: ldur            x1, [fp, #-8]
    // 0xb3c82c: LoadField: r7 = r1->field_b
    //     0xb3c82c: ldur            w7, [x1, #0xb]
    // 0xb3c830: DecompressPointer r7
    //     0xb3c830: add             x7, x7, HEAP, lsl #32
    // 0xb3c834: cmp             w7, NULL
    // 0xb3c838: b.ne            #0xb3c844
    // 0xb3c83c: r1 = Null
    //     0xb3c83c: mov             x1, NULL
    // 0xb3c840: b               #0xb3c868
    // 0xb3c844: LoadField: r1 = r7->field_1b
    //     0xb3c844: ldur            w1, [x7, #0x1b]
    // 0xb3c848: DecompressPointer r1
    //     0xb3c848: add             x1, x1, HEAP, lsl #32
    // 0xb3c84c: cmp             w1, NULL
    // 0xb3c850: b.ne            #0xb3c85c
    // 0xb3c854: r1 = Null
    //     0xb3c854: mov             x1, NULL
    // 0xb3c858: b               #0xb3c868
    // 0xb3c85c: LoadField: r7 = r1->field_7
    //     0xb3c85c: ldur            w7, [x1, #7]
    // 0xb3c860: DecompressPointer r7
    //     0xb3c860: add             x7, x7, HEAP, lsl #32
    // 0xb3c864: mov             x1, x7
    // 0xb3c868: cmp             w1, NULL
    // 0xb3c86c: b.eq            #0xb3c870
    // 0xb3c870: ldur            x7, [fp, #-0x30]
    // 0xb3c874: ldur            x1, [fp, #-0x10]
    // 0xb3c878: r0 = of()
    //     0xb3c878: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3c87c: LoadField: r1 = r0->field_87
    //     0xb3c87c: ldur            w1, [x0, #0x87]
    // 0xb3c880: DecompressPointer r1
    //     0xb3c880: add             x1, x1, HEAP, lsl #32
    // 0xb3c884: LoadField: r0 = r1->field_7
    //     0xb3c884: ldur            w0, [x1, #7]
    // 0xb3c888: DecompressPointer r0
    //     0xb3c888: add             x0, x0, HEAP, lsl #32
    // 0xb3c88c: ldur            x1, [fp, #-0x10]
    // 0xb3c890: stur            x0, [fp, #-8]
    // 0xb3c894: r0 = of()
    //     0xb3c894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3c898: LoadField: r1 = r0->field_5b
    //     0xb3c898: ldur            w1, [x0, #0x5b]
    // 0xb3c89c: DecompressPointer r1
    //     0xb3c89c: add             x1, x1, HEAP, lsl #32
    // 0xb3c8a0: r16 = 14.000000
    //     0xb3c8a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3c8a4: ldr             x16, [x16, #0x1d8]
    // 0xb3c8a8: stp             x1, x16, [SP]
    // 0xb3c8ac: ldur            x1, [fp, #-8]
    // 0xb3c8b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3c8b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3c8b4: ldr             x4, [x4, #0xaa0]
    // 0xb3c8b8: r0 = copyWith()
    //     0xb3c8b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3c8bc: r0 = Accordion()
    //     0xb3c8bc: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb3c8c0: mov             x3, x0
    // 0xb3c8c4: ldur            x0, [fp, #-0x30]
    // 0xb3c8c8: stur            x3, [fp, #-8]
    // 0xb3c8cc: StoreField: r3->field_b = r0
    //     0xb3c8cc: stur            w0, [x3, #0xb]
    // 0xb3c8d0: ldur            x0, [fp, #-0x28]
    // 0xb3c8d4: StoreField: r3->field_13 = r0
    //     0xb3c8d4: stur            w0, [x3, #0x13]
    // 0xb3c8d8: r0 = false
    //     0xb3c8d8: add             x0, NULL, #0x30  ; false
    // 0xb3c8dc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb3c8dc: stur            w0, [x3, #0x17]
    // 0xb3c8e0: d0 = 24.000000
    //     0xb3c8e0: fmov            d0, #24.00000000
    // 0xb3c8e4: StoreField: r3->field_1b = d0
    //     0xb3c8e4: stur            d0, [x3, #0x1b]
    // 0xb3c8e8: r0 = true
    //     0xb3c8e8: add             x0, NULL, #0x20  ; true
    // 0xb3c8ec: StoreField: r3->field_23 = r0
    //     0xb3c8ec: stur            w0, [x3, #0x23]
    // 0xb3c8f0: ldur            x2, [fp, #-0x20]
    // 0xb3c8f4: r1 = Function '<anonymous closure>':.
    //     0xb3c8f4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57078] AnonymousClosure: (0xb3ca10), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bill_detail_widget.dart] _BillDetailWidgetState::build (0xb3c410)
    //     0xb3c8f8: ldr             x1, [x1, #0x78]
    // 0xb3c8fc: r0 = AllocateClosure()
    //     0xb3c8fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb3c900: mov             x1, x0
    // 0xb3c904: ldur            x0, [fp, #-8]
    // 0xb3c908: StoreField: r0->field_3b = r1
    //     0xb3c908: stur            w1, [x0, #0x3b]
    // 0xb3c90c: r1 = Null
    //     0xb3c90c: mov             x1, NULL
    // 0xb3c910: r2 = 2
    //     0xb3c910: movz            x2, #0x2
    // 0xb3c914: r0 = AllocateArray()
    //     0xb3c914: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3c918: mov             x2, x0
    // 0xb3c91c: ldur            x0, [fp, #-8]
    // 0xb3c920: stur            x2, [fp, #-0x10]
    // 0xb3c924: StoreField: r2->field_f = r0
    //     0xb3c924: stur            w0, [x2, #0xf]
    // 0xb3c928: r1 = <Widget>
    //     0xb3c928: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3c92c: r0 = AllocateGrowableArray()
    //     0xb3c92c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3c930: mov             x1, x0
    // 0xb3c934: ldur            x0, [fp, #-0x10]
    // 0xb3c938: stur            x1, [fp, #-8]
    // 0xb3c93c: StoreField: r1->field_f = r0
    //     0xb3c93c: stur            w0, [x1, #0xf]
    // 0xb3c940: r0 = 2
    //     0xb3c940: movz            x0, #0x2
    // 0xb3c944: StoreField: r1->field_b = r0
    //     0xb3c944: stur            w0, [x1, #0xb]
    // 0xb3c948: r0 = Column()
    //     0xb3c948: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3c94c: mov             x1, x0
    // 0xb3c950: r0 = Instance_Axis
    //     0xb3c950: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3c954: stur            x1, [fp, #-0x10]
    // 0xb3c958: StoreField: r1->field_f = r0
    //     0xb3c958: stur            w0, [x1, #0xf]
    // 0xb3c95c: r0 = Instance_MainAxisAlignment
    //     0xb3c95c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3c960: ldr             x0, [x0, #0xa08]
    // 0xb3c964: StoreField: r1->field_13 = r0
    //     0xb3c964: stur            w0, [x1, #0x13]
    // 0xb3c968: r0 = Instance_MainAxisSize
    //     0xb3c968: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3c96c: ldr             x0, [x0, #0xa10]
    // 0xb3c970: ArrayStore: r1[0] = r0  ; List_4
    //     0xb3c970: stur            w0, [x1, #0x17]
    // 0xb3c974: r0 = Instance_CrossAxisAlignment
    //     0xb3c974: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb3c978: ldr             x0, [x0, #0xa18]
    // 0xb3c97c: StoreField: r1->field_1b = r0
    //     0xb3c97c: stur            w0, [x1, #0x1b]
    // 0xb3c980: r0 = Instance_VerticalDirection
    //     0xb3c980: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3c984: ldr             x0, [x0, #0xa20]
    // 0xb3c988: StoreField: r1->field_23 = r0
    //     0xb3c988: stur            w0, [x1, #0x23]
    // 0xb3c98c: r0 = Instance_Clip
    //     0xb3c98c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3c990: ldr             x0, [x0, #0x38]
    // 0xb3c994: StoreField: r1->field_2b = r0
    //     0xb3c994: stur            w0, [x1, #0x2b]
    // 0xb3c998: StoreField: r1->field_2f = rZR
    //     0xb3c998: stur            xzr, [x1, #0x2f]
    // 0xb3c99c: ldur            x0, [fp, #-8]
    // 0xb3c9a0: StoreField: r1->field_b = r0
    //     0xb3c9a0: stur            w0, [x1, #0xb]
    // 0xb3c9a4: r0 = Padding()
    //     0xb3c9a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb3c9a8: r1 = Instance_EdgeInsets
    //     0xb3c9a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb3c9ac: ldr             x1, [x1, #0x1f0]
    // 0xb3c9b0: StoreField: r0->field_f = r1
    //     0xb3c9b0: stur            w1, [x0, #0xf]
    // 0xb3c9b4: ldur            x1, [fp, #-0x10]
    // 0xb3c9b8: StoreField: r0->field_b = r1
    //     0xb3c9b8: stur            w1, [x0, #0xb]
    // 0xb3c9bc: LeaveFrame
    //     0xb3c9bc: mov             SP, fp
    //     0xb3c9c0: ldp             fp, lr, [SP], #0x10
    // 0xb3c9c4: ret
    //     0xb3c9c4: ret             
    // 0xb3c9c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3c9c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3c9cc: b               #0xb3c438
    // 0xb3c9d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c9d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3c9d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c9d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3c9d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c9d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3c9dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3c9dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb3ca10, size: 0x78
    // 0xb3ca10: EnterFrame
    //     0xb3ca10: stp             fp, lr, [SP, #-0x10]!
    //     0xb3ca14: mov             fp, SP
    // 0xb3ca18: AllocStack(0x8)
    //     0xb3ca18: sub             SP, SP, #8
    // 0xb3ca1c: SetupParameters()
    //     0xb3ca1c: ldr             x0, [fp, #0x10]
    //     0xb3ca20: ldur            w1, [x0, #0x17]
    //     0xb3ca24: add             x1, x1, HEAP, lsl #32
    // 0xb3ca28: CheckStackOverflow
    //     0xb3ca28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3ca2c: cmp             SP, x16
    //     0xb3ca30: b.ls            #0xb3ca7c
    // 0xb3ca34: LoadField: r0 = r1->field_f
    //     0xb3ca34: ldur            w0, [x1, #0xf]
    // 0xb3ca38: DecompressPointer r0
    //     0xb3ca38: add             x0, x0, HEAP, lsl #32
    // 0xb3ca3c: LoadField: r1 = r0->field_b
    //     0xb3ca3c: ldur            w1, [x0, #0xb]
    // 0xb3ca40: DecompressPointer r1
    //     0xb3ca40: add             x1, x1, HEAP, lsl #32
    // 0xb3ca44: cmp             w1, NULL
    // 0xb3ca48: b.eq            #0xb3ca84
    // 0xb3ca4c: LoadField: r0 = r1->field_13
    //     0xb3ca4c: ldur            w0, [x1, #0x13]
    // 0xb3ca50: DecompressPointer r0
    //     0xb3ca50: add             x0, x0, HEAP, lsl #32
    // 0xb3ca54: str             x0, [SP]
    // 0xb3ca58: r4 = 0
    //     0xb3ca58: movz            x4, #0
    // 0xb3ca5c: ldr             x0, [SP]
    // 0xb3ca60: r5 = UnlinkedCall_0x613b5c
    //     0xb3ca60: add             x16, PP, #0x57, lsl #12  ; [pp+0x57080] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb3ca64: ldp             x5, lr, [x16, #0x80]
    // 0xb3ca68: blr             lr
    // 0xb3ca6c: r0 = Null
    //     0xb3ca6c: mov             x0, NULL
    // 0xb3ca70: LeaveFrame
    //     0xb3ca70: mov             SP, fp
    //     0xb3ca74: ldp             fp, lr, [SP], #0x10
    // 0xb3ca78: ret
    //     0xb3ca78: ret             
    // 0xb3ca7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3ca7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb3ca80: b               #0xb3ca34
    // 0xb3ca84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3ca84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4110, size: 0x18, field offset: 0xc
class BillDetailWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e870, size: 0x24
    // 0xc7e870: EnterFrame
    //     0xc7e870: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e874: mov             fp, SP
    // 0xc7e878: mov             x0, x1
    // 0xc7e87c: r1 = <BillDetailWidget>
    //     0xc7e87c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a70] TypeArguments: <BillDetailWidget>
    //     0xc7e880: ldr             x1, [x1, #0xa70]
    // 0xc7e884: r0 = _BillDetailWidgetState()
    //     0xc7e884: bl              #0xc7e894  ; Allocate_BillDetailWidgetStateStub -> _BillDetailWidgetState (size=0x14)
    // 0xc7e888: LeaveFrame
    //     0xc7e888: mov             SP, fp
    //     0xc7e88c: ldp             fp, lr, [SP], #0x10
    // 0xc7e890: ret
    //     0xc7e890: ret             
  }
}
