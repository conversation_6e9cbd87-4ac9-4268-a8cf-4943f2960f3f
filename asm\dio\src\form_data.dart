// lib: , url: package:dio/src/form_data.dart

// class id: 1049605, size: 0x8
class :: {

  static late final Uint8List _rnU8; // offset: 0xd40
  static late final Random _random; // offset: 0xd44

  static Uint8List _rnU8() {
    // ** addr: 0x88405c, size: 0x94
    // 0x88405c: EnterFrame
    //     0x88405c: stp             fp, lr, [SP, #-0x10]!
    //     0x884060: mov             fp, SP
    // 0x884064: AllocStack(0x10)
    //     0x884064: sub             SP, SP, #0x10
    // 0x884068: r0 = 4
    //     0x884068: movz            x0, #0x4
    // 0x88406c: CheckStackOverflow
    //     0x88406c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x884070: cmp             SP, x16
    //     0x884074: b.ls            #0x8840e8
    // 0x884078: mov             x2, x0
    // 0x88407c: r1 = Null
    //     0x88407c: mov             x1, NULL
    // 0x884080: r0 = AllocateArray()
    //     0x884080: bl              #0x16f7198  ; AllocateArrayStub
    // 0x884084: stur            x0, [fp, #-8]
    // 0x884088: r16 = 26
    //     0x884088: movz            x16, #0x1a
    // 0x88408c: StoreField: r0->field_f = r16
    //     0x88408c: stur            w16, [x0, #0xf]
    // 0x884090: r16 = 20
    //     0x884090: movz            x16, #0x14
    // 0x884094: StoreField: r0->field_13 = r16
    //     0x884094: stur            w16, [x0, #0x13]
    // 0x884098: r1 = <int>
    //     0x884098: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0x88409c: r0 = AllocateGrowableArray()
    //     0x88409c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8840a0: mov             x1, x0
    // 0x8840a4: ldur            x0, [fp, #-8]
    // 0x8840a8: stur            x1, [fp, #-0x10]
    // 0x8840ac: StoreField: r1->field_f = r0
    //     0x8840ac: stur            w0, [x1, #0xf]
    // 0x8840b0: r4 = 4
    //     0x8840b0: movz            x4, #0x4
    // 0x8840b4: StoreField: r1->field_b = r4
    //     0x8840b4: stur            w4, [x1, #0xb]
    // 0x8840b8: r0 = AllocateUint8Array()
    //     0x8840b8: bl              #0x16f6e7c  ; AllocateUint8ArrayStub
    // 0x8840bc: mov             x1, x0
    // 0x8840c0: ldur            x5, [fp, #-0x10]
    // 0x8840c4: r2 = 0
    //     0x8840c4: movz            x2, #0
    // 0x8840c8: r3 = 2
    //     0x8840c8: movz            x3, #0x2
    // 0x8840cc: r6 = 0
    //     0x8840cc: movz            x6, #0
    // 0x8840d0: stur            x0, [fp, #-8]
    // 0x8840d4: r0 = _slowSetRange()
    //     0x8840d4: bl              #0x1535c5c  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x8840d8: ldur            x0, [fp, #-8]
    // 0x8840dc: LeaveFrame
    //     0x8840dc: mov             SP, fp
    //     0x8840e0: ldp             fp, lr, [SP], #0x10
    // 0x8840e4: ret
    //     0x8840e4: ret             
    // 0x8840e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8840e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8840ec: b               #0x884078
  }
  String _nextRandomId() {
    // ** addr: 0x8abb9c, size: 0xc4
    // 0x8abb9c: EnterFrame
    //     0x8abb9c: stp             fp, lr, [SP, #-0x10]!
    //     0x8abba0: mov             fp, SP
    // 0x8abba4: AllocStack(0x8)
    //     0x8abba4: sub             SP, SP, #8
    // 0x8abba8: CheckStackOverflow
    //     0x8abba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8abbac: cmp             SP, x16
    //     0x8abbb0: b.ls            #0x8abc58
    // 0x8abbb4: r0 = InitLateStaticField(0xd44) // [package:dio/src/form_data.dart] ::_random
    //     0x8abbb4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8abbb8: ldr             x0, [x0, #0x1a88]
    //     0x8abbbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8abbc0: cmp             w0, w16
    //     0x8abbc4: b.ne            #0x8abbd4
    //     0x8abbc8: add             x2, PP, #0x33, lsl #12  ; [pp+0x33270] Field <::._random@911426596>: static late final (offset: 0xd44)
    //     0x8abbcc: ldr             x2, [x2, #0x270]
    //     0x8abbd0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8abbd4: mov             x1, x0
    // 0x8abbd8: r2 = 4294967296
    //     0x8abbd8: orr             x2, xzr, #0x100000000
    // 0x8abbdc: r0 = nextInt()
    //     0x8abbdc: bl              #0x8abc60  ; [dart:math] _Random::nextInt
    // 0x8abbe0: mov             x2, x0
    // 0x8abbe4: r0 = BoxInt64Instr(r2)
    //     0x8abbe4: sbfiz           x0, x2, #1, #0x1f
    //     0x8abbe8: cmp             x2, x0, asr #1
    //     0x8abbec: b.eq            #0x8abbf8
    //     0x8abbf0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8abbf4: stur            x2, [x0, #7]
    // 0x8abbf8: r1 = 60
    //     0x8abbf8: movz            x1, #0x3c
    // 0x8abbfc: branchIfSmi(r0, 0x8abc08)
    //     0x8abbfc: tbz             w0, #0, #0x8abc08
    // 0x8abc00: r1 = LoadClassIdInstr(r0)
    //     0x8abc00: ldur            x1, [x0, #-1]
    //     0x8abc04: ubfx            x1, x1, #0xc, #0x14
    // 0x8abc08: str             x0, [SP]
    // 0x8abc0c: mov             x0, x1
    // 0x8abc10: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8abc10: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8abc14: r0 = GDT[cid_x0 + 0x2700]()
    //     0x8abc14: movz            x17, #0x2700
    //     0x8abc18: add             lr, x0, x17
    //     0x8abc1c: ldr             lr, [x21, lr, lsl #3]
    //     0x8abc20: blr             lr
    // 0x8abc24: r1 = LoadClassIdInstr(r0)
    //     0x8abc24: ldur            x1, [x0, #-1]
    //     0x8abc28: ubfx            x1, x1, #0xc, #0x14
    // 0x8abc2c: mov             x16, x0
    // 0x8abc30: mov             x0, x1
    // 0x8abc34: mov             x1, x16
    // 0x8abc38: r2 = 10
    //     0x8abc38: movz            x2, #0xa
    // 0x8abc3c: r3 = "0"
    //     0x8abc3c: ldr             x3, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x8abc40: r0 = GDT[cid_x0 + -0xff6]()
    //     0x8abc40: sub             lr, x0, #0xff6
    //     0x8abc44: ldr             lr, [x21, lr, lsl #3]
    //     0x8abc48: blr             lr
    // 0x8abc4c: LeaveFrame
    //     0x8abc4c: mov             SP, fp
    //     0x8abc50: ldp             fp, lr, [SP], #0x10
    // 0x8abc54: ret
    //     0x8abc54: ret             
    // 0x8abc58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8abc58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8abc5c: b               #0x8abbb4
  }
  static Random _random() {
    // ** addr: 0x8abe58, size: 0x30
    // 0x8abe58: EnterFrame
    //     0x8abe58: stp             fp, lr, [SP, #-0x10]!
    //     0x8abe5c: mov             fp, SP
    // 0x8abe60: CheckStackOverflow
    //     0x8abe60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8abe64: cmp             SP, x16
    //     0x8abe68: b.ls            #0x8abe80
    // 0x8abe6c: r1 = Null
    //     0x8abe6c: mov             x1, NULL
    // 0x8abe70: r0 = Random()
    //     0x8abe70: bl              #0x8abe88  ; [dart:math] Random::Random
    // 0x8abe74: LeaveFrame
    //     0x8abe74: mov             SP, fp
    //     0x8abe78: ldp             fp, lr, [SP], #0x10
    // 0x8abe7c: ret
    //     0x8abe7c: ret             
    // 0x8abe80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8abe80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8abe84: b               #0x8abe6c
  }
}

// class id: 4983, size: 0x20, field offset: 0x8
class FormData extends Object {

  late String _boundary; // offset: 0x10

  get _ length(/* No info */) {
    // ** addr: 0x882f1c, size: 0x36c
    // 0x882f1c: EnterFrame
    //     0x882f1c: stp             fp, lr, [SP, #-0x10]!
    //     0x882f20: mov             fp, SP
    // 0x882f24: AllocStack(0x58)
    //     0x882f24: sub             SP, SP, #0x58
    // 0x882f28: SetupParameters(FormData this /* r1 => r0, fp-0x40 */)
    //     0x882f28: mov             x0, x1
    //     0x882f2c: stur            x1, [fp, #-0x40]
    // 0x882f30: CheckStackOverflow
    //     0x882f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x882f34: cmp             SP, x16
    //     0x882f38: b.ls            #0x883248
    // 0x882f3c: LoadField: r3 = r0->field_13
    //     0x882f3c: ldur            w3, [x0, #0x13]
    // 0x882f40: DecompressPointer r3
    //     0x882f40: add             x3, x3, HEAP, lsl #32
    // 0x882f44: stur            x3, [fp, #-0x38]
    // 0x882f48: LoadField: r1 = r3->field_b
    //     0x882f48: ldur            w1, [x3, #0xb]
    // 0x882f4c: r4 = LoadInt32Instr(r1)
    //     0x882f4c: sbfx            x4, x1, #1, #0x1f
    // 0x882f50: stur            x4, [fp, #-0x30]
    // 0x882f54: r5 = 0
    //     0x882f54: movz            x5, #0
    // 0x882f58: r1 = 0
    //     0x882f58: movz            x1, #0
    // 0x882f5c: stur            x5, [fp, #-0x28]
    // 0x882f60: CheckStackOverflow
    //     0x882f60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x882f64: cmp             SP, x16
    //     0x882f68: b.ls            #0x883250
    // 0x882f6c: LoadField: r2 = r3->field_b
    //     0x882f6c: ldur            w2, [x3, #0xb]
    // 0x882f70: r6 = LoadInt32Instr(r2)
    //     0x882f70: sbfx            x6, x2, #1, #0x1f
    // 0x882f74: cmp             x4, x6
    // 0x882f78: b.ne            #0x883228
    // 0x882f7c: cmp             x1, x6
    // 0x882f80: b.ge            #0x8830d0
    // 0x882f84: LoadField: r2 = r3->field_f
    //     0x882f84: ldur            w2, [x3, #0xf]
    // 0x882f88: DecompressPointer r2
    //     0x882f88: add             x2, x2, HEAP, lsl #32
    // 0x882f8c: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0x882f8c: add             x16, x2, x1, lsl #2
    //     0x882f90: ldur            w6, [x16, #0xf]
    // 0x882f94: DecompressPointer r6
    //     0x882f94: add             x6, x6, HEAP, lsl #32
    // 0x882f98: stur            x6, [fp, #-0x20]
    // 0x882f9c: add             x7, x1, #1
    // 0x882fa0: stur            x7, [fp, #-0x18]
    // 0x882fa4: LoadField: r1 = r0->field_f
    //     0x882fa4: ldur            w1, [x0, #0xf]
    // 0x882fa8: DecompressPointer r1
    //     0x882fa8: add             x1, x1, HEAP, lsl #32
    // 0x882fac: r16 = Sentinel
    //     0x882fac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x882fb0: cmp             w1, w16
    // 0x882fb4: b.eq            #0x883258
    // 0x882fb8: LoadField: r2 = r1->field_7
    //     0x882fb8: ldur            w2, [x1, #7]
    // 0x882fbc: r1 = LoadInt32Instr(r2)
    //     0x882fbc: sbfx            x1, x2, #1, #0x1f
    // 0x882fc0: add             x2, x1, #2
    // 0x882fc4: add             x8, x2, #2
    // 0x882fc8: stur            x8, [fp, #-0x10]
    // 0x882fcc: LoadField: r9 = r6->field_b
    //     0x882fcc: ldur            w9, [x6, #0xb]
    // 0x882fd0: DecompressPointer r9
    //     0x882fd0: add             x9, x9, HEAP, lsl #32
    // 0x882fd4: stur            x9, [fp, #-8]
    // 0x882fd8: r1 = Null
    //     0x882fd8: mov             x1, NULL
    // 0x882fdc: r2 = 12
    //     0x882fdc: movz            x2, #0xc
    // 0x882fe0: r0 = AllocateArray()
    //     0x882fe0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x882fe4: stur            x0, [fp, #-0x48]
    // 0x882fe8: r16 = "content-disposition"
    //     0x882fe8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa638] "content-disposition"
    //     0x882fec: ldr             x16, [x16, #0x638]
    // 0x882ff0: StoreField: r0->field_f = r16
    //     0x882ff0: stur            w16, [x0, #0xf]
    // 0x882ff4: r16 = ": form-data; name=\""
    //     0x882ff4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa640] ": form-data; name=\""
    //     0x882ff8: ldr             x16, [x16, #0x640]
    // 0x882ffc: StoreField: r0->field_13 = r16
    //     0x882ffc: stur            w16, [x0, #0x13]
    // 0x883000: ldur            x1, [fp, #-0x40]
    // 0x883004: ldur            x2, [fp, #-8]
    // 0x883008: r0 = _browserEncode()
    //     0x883008: bl              #0x883674  ; [package:dio/src/form_data.dart] FormData::_browserEncode
    // 0x88300c: ldur            x1, [fp, #-0x48]
    // 0x883010: ArrayStore: r1[2] = r0  ; List_4
    //     0x883010: add             x25, x1, #0x17
    //     0x883014: str             w0, [x25]
    //     0x883018: tbz             w0, #0, #0x883034
    //     0x88301c: ldurb           w16, [x1, #-1]
    //     0x883020: ldurb           w17, [x0, #-1]
    //     0x883024: and             x16, x17, x16, lsr #2
    //     0x883028: tst             x16, HEAP, lsr #32
    //     0x88302c: b.eq            #0x883034
    //     0x883030: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x883034: ldur            x0, [fp, #-0x48]
    // 0x883038: r16 = "\""
    //     0x883038: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0x88303c: ldr             x16, [x16, #0x550]
    // 0x883040: StoreField: r0->field_1b = r16
    //     0x883040: stur            w16, [x0, #0x1b]
    // 0x883044: r16 = "\r\n"
    //     0x883044: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883048: ldr             x16, [x16, #0x648]
    // 0x88304c: StoreField: r0->field_1f = r16
    //     0x88304c: stur            w16, [x0, #0x1f]
    // 0x883050: r16 = "\r\n"
    //     0x883050: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883054: ldr             x16, [x16, #0x648]
    // 0x883058: StoreField: r0->field_23 = r16
    //     0x883058: stur            w16, [x0, #0x23]
    // 0x88305c: str             x0, [SP]
    // 0x883060: r0 = _interpolate()
    //     0x883060: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883064: mov             x2, x0
    // 0x883068: r1 = Instance_Utf8Encoder
    //     0x883068: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x88306c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x88306c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x883070: r0 = convert()
    //     0x883070: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883074: LoadField: r1 = r0->field_13
    //     0x883074: ldur            w1, [x0, #0x13]
    // 0x883078: r0 = LoadInt32Instr(r1)
    //     0x883078: sbfx            x0, x1, #1, #0x1f
    // 0x88307c: ldur            x1, [fp, #-0x10]
    // 0x883080: add             x3, x1, x0
    // 0x883084: ldur            x0, [fp, #-0x20]
    // 0x883088: stur            x3, [fp, #-0x50]
    // 0x88308c: LoadField: r2 = r0->field_f
    //     0x88308c: ldur            w2, [x0, #0xf]
    // 0x883090: DecompressPointer r2
    //     0x883090: add             x2, x2, HEAP, lsl #32
    // 0x883094: r1 = Instance_Utf8Encoder
    //     0x883094: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x883098: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x883098: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88309c: r0 = convert()
    //     0x88309c: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x8830a0: LoadField: r1 = r0->field_13
    //     0x8830a0: ldur            w1, [x0, #0x13]
    // 0x8830a4: r0 = LoadInt32Instr(r1)
    //     0x8830a4: sbfx            x0, x1, #1, #0x1f
    // 0x8830a8: ldur            x1, [fp, #-0x50]
    // 0x8830ac: add             x2, x1, x0
    // 0x8830b0: add             x0, x2, #2
    // 0x8830b4: ldur            x1, [fp, #-0x28]
    // 0x8830b8: add             x5, x1, x0
    // 0x8830bc: ldur            x1, [fp, #-0x18]
    // 0x8830c0: ldur            x0, [fp, #-0x40]
    // 0x8830c4: ldur            x3, [fp, #-0x38]
    // 0x8830c8: ldur            x4, [fp, #-0x30]
    // 0x8830cc: b               #0x882f5c
    // 0x8830d0: mov             x1, x5
    // 0x8830d4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8830d4: ldur            w3, [x0, #0x17]
    // 0x8830d8: DecompressPointer r3
    //     0x8830d8: add             x3, x3, HEAP, lsl #32
    // 0x8830dc: stur            x3, [fp, #-0x20]
    // 0x8830e0: LoadField: r2 = r3->field_b
    //     0x8830e0: ldur            w2, [x3, #0xb]
    // 0x8830e4: r4 = LoadInt32Instr(r2)
    //     0x8830e4: sbfx            x4, x2, #1, #0x1f
    // 0x8830e8: stur            x4, [fp, #-0x30]
    // 0x8830ec: mov             x5, x1
    // 0x8830f0: r1 = 0
    //     0x8830f0: movz            x1, #0
    // 0x8830f4: stur            x5, [fp, #-0x28]
    // 0x8830f8: CheckStackOverflow
    //     0x8830f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8830fc: cmp             SP, x16
    //     0x883100: b.ls            #0x883264
    // 0x883104: LoadField: r2 = r3->field_b
    //     0x883104: ldur            w2, [x3, #0xb]
    // 0x883108: r6 = LoadInt32Instr(r2)
    //     0x883108: sbfx            x6, x2, #1, #0x1f
    // 0x88310c: cmp             x4, x6
    // 0x883110: b.ne            #0x883208
    // 0x883114: cmp             x1, x6
    // 0x883118: b.ge            #0x8831cc
    // 0x88311c: LoadField: r2 = r3->field_f
    //     0x88311c: ldur            w2, [x3, #0xf]
    // 0x883120: DecompressPointer r2
    //     0x883120: add             x2, x2, HEAP, lsl #32
    // 0x883124: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0x883124: add             x16, x2, x1, lsl #2
    //     0x883128: ldur            w6, [x16, #0xf]
    // 0x88312c: DecompressPointer r6
    //     0x88312c: add             x6, x6, HEAP, lsl #32
    // 0x883130: stur            x6, [fp, #-8]
    // 0x883134: add             x7, x1, #1
    // 0x883138: stur            x7, [fp, #-0x18]
    // 0x88313c: LoadField: r1 = r0->field_f
    //     0x88313c: ldur            w1, [x0, #0xf]
    // 0x883140: DecompressPointer r1
    //     0x883140: add             x1, x1, HEAP, lsl #32
    // 0x883144: r16 = Sentinel
    //     0x883144: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x883148: cmp             w1, w16
    // 0x88314c: b.eq            #0x88326c
    // 0x883150: LoadField: r2 = r1->field_7
    //     0x883150: ldur            w2, [x1, #7]
    // 0x883154: r1 = LoadInt32Instr(r2)
    //     0x883154: sbfx            x1, x2, #1, #0x1f
    // 0x883158: add             x2, x1, #2
    // 0x88315c: add             x8, x2, #2
    // 0x883160: mov             x1, x0
    // 0x883164: mov             x2, x6
    // 0x883168: stur            x8, [fp, #-0x10]
    // 0x88316c: r0 = _headerForFile()
    //     0x88316c: bl              #0x883288  ; [package:dio/src/form_data.dart] FormData::_headerForFile
    // 0x883170: mov             x2, x0
    // 0x883174: r1 = Instance_Utf8Encoder
    //     0x883174: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x883178: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x883178: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88317c: r0 = convert()
    //     0x88317c: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883180: LoadField: r1 = r0->field_13
    //     0x883180: ldur            w1, [x0, #0x13]
    // 0x883184: r0 = LoadInt32Instr(r1)
    //     0x883184: sbfx            x0, x1, #1, #0x1f
    // 0x883188: ldur            x1, [fp, #-0x10]
    // 0x88318c: add             x2, x1, x0
    // 0x883190: ldur            x0, [fp, #-8]
    // 0x883194: LoadField: r1 = r0->field_f
    //     0x883194: ldur            w1, [x0, #0xf]
    // 0x883198: DecompressPointer r1
    //     0x883198: add             x1, x1, HEAP, lsl #32
    // 0x88319c: cmp             w1, NULL
    // 0x8831a0: b.eq            #0x883278
    // 0x8831a4: LoadField: r0 = r1->field_7
    //     0x8831a4: ldur            x0, [x1, #7]
    // 0x8831a8: add             x1, x2, x0
    // 0x8831ac: add             x0, x1, #2
    // 0x8831b0: ldur            x1, [fp, #-0x28]
    // 0x8831b4: add             x5, x1, x0
    // 0x8831b8: ldur            x1, [fp, #-0x18]
    // 0x8831bc: ldur            x0, [fp, #-0x40]
    // 0x8831c0: ldur            x3, [fp, #-0x20]
    // 0x8831c4: ldur            x4, [fp, #-0x30]
    // 0x8831c8: b               #0x8830f4
    // 0x8831cc: mov             x1, x5
    // 0x8831d0: add             x2, x1, #2
    // 0x8831d4: LoadField: r1 = r0->field_f
    //     0x8831d4: ldur            w1, [x0, #0xf]
    // 0x8831d8: DecompressPointer r1
    //     0x8831d8: add             x1, x1, HEAP, lsl #32
    // 0x8831dc: r16 = Sentinel
    //     0x8831dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8831e0: cmp             w1, w16
    // 0x8831e4: b.eq            #0x88327c
    // 0x8831e8: LoadField: r0 = r1->field_7
    //     0x8831e8: ldur            w0, [x1, #7]
    // 0x8831ec: r1 = LoadInt32Instr(r0)
    //     0x8831ec: sbfx            x1, x0, #1, #0x1f
    // 0x8831f0: add             x0, x2, x1
    // 0x8831f4: add             x1, x0, #4
    // 0x8831f8: mov             x0, x1
    // 0x8831fc: LeaveFrame
    //     0x8831fc: mov             SP, fp
    //     0x883200: ldp             fp, lr, [SP], #0x10
    // 0x883204: ret
    //     0x883204: ret             
    // 0x883208: mov             x0, x3
    // 0x88320c: r0 = ConcurrentModificationError()
    //     0x88320c: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x883210: mov             x1, x0
    // 0x883214: ldur            x0, [fp, #-0x20]
    // 0x883218: StoreField: r1->field_b = r0
    //     0x883218: stur            w0, [x1, #0xb]
    // 0x88321c: mov             x0, x1
    // 0x883220: r0 = Throw()
    //     0x883220: bl              #0x16f5420  ; ThrowStub
    // 0x883224: brk             #0
    // 0x883228: mov             x0, x3
    // 0x88322c: r0 = ConcurrentModificationError()
    //     0x88322c: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x883230: mov             x1, x0
    // 0x883234: ldur            x0, [fp, #-0x38]
    // 0x883238: StoreField: r1->field_b = r0
    //     0x883238: stur            w0, [x1, #0xb]
    // 0x88323c: mov             x0, x1
    // 0x883240: r0 = Throw()
    //     0x883240: bl              #0x16f5420  ; ThrowStub
    // 0x883244: brk             #0
    // 0x883248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883248: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88324c: b               #0x882f3c
    // 0x883250: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883250: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883254: b               #0x882f6c
    // 0x883258: r9 = _boundary
    //     0x883258: add             x9, PP, #0xa, lsl #12  ; [pp+0xa560] Field <FormData._boundary@911426596>: late (offset: 0x10)
    //     0x88325c: ldr             x9, [x9, #0x560]
    // 0x883260: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x883260: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x883264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883264: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883268: b               #0x883104
    // 0x88326c: r9 = _boundary
    //     0x88326c: add             x9, PP, #0xa, lsl #12  ; [pp+0xa560] Field <FormData._boundary@911426596>: late (offset: 0x10)
    //     0x883270: ldr             x9, [x9, #0x560]
    // 0x883274: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x883274: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x883278: r0 = NullErrorSharedWithoutFPURegs()
    //     0x883278: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x88327c: r9 = _boundary
    //     0x88327c: add             x9, PP, #0xa, lsl #12  ; [pp+0xa560] Field <FormData._boundary@911426596>: late (offset: 0x10)
    //     0x883280: ldr             x9, [x9, #0x560]
    // 0x883284: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x883284: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _headerForFile(/* No info */) {
    // ** addr: 0x883288, size: 0x2a4
    // 0x883288: EnterFrame
    //     0x883288: stp             fp, lr, [SP, #-0x10]!
    //     0x88328c: mov             fp, SP
    // 0x883290: AllocStack(0x38)
    //     0x883290: sub             SP, SP, #0x38
    // 0x883294: SetupParameters(FormData this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x883294: mov             x3, x1
    //     0x883298: mov             x0, x2
    //     0x88329c: stur            x1, [fp, #-0x10]
    //     0x8832a0: stur            x2, [fp, #-0x18]
    // 0x8832a4: CheckStackOverflow
    //     0x8832a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8832a8: cmp             SP, x16
    //     0x8832ac: b.ls            #0x883520
    // 0x8832b0: LoadField: r4 = r0->field_f
    //     0x8832b0: ldur            w4, [x0, #0xf]
    // 0x8832b4: DecompressPointer r4
    //     0x8832b4: add             x4, x4, HEAP, lsl #32
    // 0x8832b8: stur            x4, [fp, #-8]
    // 0x8832bc: r1 = Null
    //     0x8832bc: mov             x1, NULL
    // 0x8832c0: r2 = 8
    //     0x8832c0: movz            x2, #0x8
    // 0x8832c4: r0 = AllocateArray()
    //     0x8832c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8832c8: stur            x0, [fp, #-0x20]
    // 0x8832cc: r16 = "content-disposition"
    //     0x8832cc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa638] "content-disposition"
    //     0x8832d0: ldr             x16, [x16, #0x638]
    // 0x8832d4: StoreField: r0->field_f = r16
    //     0x8832d4: stur            w16, [x0, #0xf]
    // 0x8832d8: r16 = ": form-data; name=\""
    //     0x8832d8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa640] ": form-data; name=\""
    //     0x8832dc: ldr             x16, [x16, #0x640]
    // 0x8832e0: StoreField: r0->field_13 = r16
    //     0x8832e0: stur            w16, [x0, #0x13]
    // 0x8832e4: ldur            x1, [fp, #-0x18]
    // 0x8832e8: LoadField: r2 = r1->field_b
    //     0x8832e8: ldur            w2, [x1, #0xb]
    // 0x8832ec: DecompressPointer r2
    //     0x8832ec: add             x2, x2, HEAP, lsl #32
    // 0x8832f0: ldur            x1, [fp, #-0x10]
    // 0x8832f4: r0 = _browserEncode()
    //     0x8832f4: bl              #0x883674  ; [package:dio/src/form_data.dart] FormData::_browserEncode
    // 0x8832f8: ldur            x1, [fp, #-0x20]
    // 0x8832fc: ArrayStore: r1[2] = r0  ; List_4
    //     0x8832fc: add             x25, x1, #0x17
    //     0x883300: str             w0, [x25]
    //     0x883304: tbz             w0, #0, #0x883320
    //     0x883308: ldurb           w16, [x1, #-1]
    //     0x88330c: ldurb           w17, [x0, #-1]
    //     0x883310: and             x16, x17, x16, lsr #2
    //     0x883314: tst             x16, HEAP, lsr #32
    //     0x883318: b.eq            #0x883320
    //     0x88331c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x883320: ldur            x0, [fp, #-0x20]
    // 0x883324: r16 = "\""
    //     0x883324: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0x883328: ldr             x16, [x16, #0x550]
    // 0x88332c: StoreField: r0->field_1b = r16
    //     0x88332c: stur            w16, [x0, #0x1b]
    // 0x883330: str             x0, [SP]
    // 0x883334: r0 = _interpolate()
    //     0x883334: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883338: stur            x0, [fp, #-0x18]
    // 0x88333c: r1 = 1
    //     0x88333c: movz            x1, #0x1
    // 0x883340: r0 = AllocateContext()
    //     0x883340: bl              #0x16f6108  ; AllocateContextStub
    // 0x883344: mov             x3, x0
    // 0x883348: ldur            x0, [fp, #-0x18]
    // 0x88334c: stur            x3, [fp, #-0x28]
    // 0x883350: StoreField: r3->field_f = r0
    //     0x883350: stur            w0, [x3, #0xf]
    // 0x883354: ldur            x4, [fp, #-8]
    // 0x883358: cmp             w4, NULL
    // 0x88335c: b.eq            #0x883528
    // 0x883360: LoadField: r5 = r4->field_f
    //     0x883360: ldur            w5, [x4, #0xf]
    // 0x883364: DecompressPointer r5
    //     0x883364: add             x5, x5, HEAP, lsl #32
    // 0x883368: stur            x5, [fp, #-0x20]
    // 0x88336c: cmp             w5, NULL
    // 0x883370: b.eq            #0x883414
    // 0x883374: r1 = Null
    //     0x883374: mov             x1, NULL
    // 0x883378: r2 = 8
    //     0x883378: movz            x2, #0x8
    // 0x88337c: r0 = AllocateArray()
    //     0x88337c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x883380: mov             x3, x0
    // 0x883384: ldur            x0, [fp, #-0x18]
    // 0x883388: stur            x3, [fp, #-0x30]
    // 0x88338c: StoreField: r3->field_f = r0
    //     0x88338c: stur            w0, [x3, #0xf]
    // 0x883390: r16 = "; filename=\""
    //     0x883390: add             x16, PP, #0xa, lsl #12  ; [pp+0xa658] "; filename=\""
    //     0x883394: ldr             x16, [x16, #0x658]
    // 0x883398: StoreField: r3->field_13 = r16
    //     0x883398: stur            w16, [x3, #0x13]
    // 0x88339c: ldur            x1, [fp, #-0x10]
    // 0x8833a0: ldur            x2, [fp, #-0x20]
    // 0x8833a4: r0 = _browserEncode()
    //     0x8833a4: bl              #0x883674  ; [package:dio/src/form_data.dart] FormData::_browserEncode
    // 0x8833a8: ldur            x1, [fp, #-0x30]
    // 0x8833ac: ArrayStore: r1[2] = r0  ; List_4
    //     0x8833ac: add             x25, x1, #0x17
    //     0x8833b0: str             w0, [x25]
    //     0x8833b4: tbz             w0, #0, #0x8833d0
    //     0x8833b8: ldurb           w16, [x1, #-1]
    //     0x8833bc: ldurb           w17, [x0, #-1]
    //     0x8833c0: and             x16, x17, x16, lsr #2
    //     0x8833c4: tst             x16, HEAP, lsr #32
    //     0x8833c8: b.eq            #0x8833d0
    //     0x8833cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8833d0: ldur            x0, [fp, #-0x30]
    // 0x8833d4: r16 = "\""
    //     0x8833d4: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0x8833d8: ldr             x16, [x16, #0x550]
    // 0x8833dc: StoreField: r0->field_1b = r16
    //     0x8833dc: stur            w16, [x0, #0x1b]
    // 0x8833e0: str             x0, [SP]
    // 0x8833e4: r0 = _interpolate()
    //     0x8833e4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8833e8: mov             x1, x0
    // 0x8833ec: ldur            x3, [fp, #-0x28]
    // 0x8833f0: StoreField: r3->field_f = r0
    //     0x8833f0: stur            w0, [x3, #0xf]
    //     0x8833f4: ldurb           w16, [x3, #-1]
    //     0x8833f8: ldurb           w17, [x0, #-1]
    //     0x8833fc: and             x16, x17, x16, lsr #2
    //     0x883400: tst             x16, HEAP, lsr #32
    //     0x883404: b.eq            #0x88340c
    //     0x883408: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x88340c: mov             x4, x1
    // 0x883410: b               #0x883418
    // 0x883414: mov             x4, x0
    // 0x883418: ldur            x0, [fp, #-8]
    // 0x88341c: stur            x4, [fp, #-0x10]
    // 0x883420: r1 = Null
    //     0x883420: mov             x1, NULL
    // 0x883424: r2 = 8
    //     0x883424: movz            x2, #0x8
    // 0x883428: r0 = AllocateArray()
    //     0x883428: bl              #0x16f7198  ; AllocateArrayStub
    // 0x88342c: mov             x1, x0
    // 0x883430: ldur            x0, [fp, #-0x10]
    // 0x883434: StoreField: r1->field_f = r0
    //     0x883434: stur            w0, [x1, #0xf]
    // 0x883438: r16 = "\r\n"
    //     0x883438: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x88343c: ldr             x16, [x16, #0x648]
    // 0x883440: StoreField: r1->field_13 = r16
    //     0x883440: stur            w16, [x1, #0x13]
    // 0x883444: r16 = "content-type: "
    //     0x883444: add             x16, PP, #0xa, lsl #12  ; [pp+0xa660] "content-type: "
    //     0x883448: ldr             x16, [x16, #0x660]
    // 0x88344c: ArrayStore: r1[0] = r16  ; List_4
    //     0x88344c: stur            w16, [x1, #0x17]
    // 0x883450: ldur            x0, [fp, #-8]
    // 0x883454: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x883454: ldur            w2, [x0, #0x17]
    // 0x883458: DecompressPointer r2
    //     0x883458: add             x2, x2, HEAP, lsl #32
    // 0x88345c: StoreField: r1->field_1b = r2
    //     0x88345c: stur            w2, [x1, #0x1b]
    // 0x883460: str             x1, [SP]
    // 0x883464: r0 = _interpolate()
    //     0x883464: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883468: ldur            x3, [fp, #-0x28]
    // 0x88346c: StoreField: r3->field_f = r0
    //     0x88346c: stur            w0, [x3, #0xf]
    //     0x883470: ldurb           w16, [x3, #-1]
    //     0x883474: ldurb           w17, [x0, #-1]
    //     0x883478: and             x16, x17, x16, lsr #2
    //     0x88347c: tst             x16, HEAP, lsr #32
    //     0x883480: b.eq            #0x883488
    //     0x883484: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x883488: ldur            x0, [fp, #-8]
    // 0x88348c: LoadField: r4 = r0->field_13
    //     0x88348c: ldur            w4, [x0, #0x13]
    // 0x883490: DecompressPointer r4
    //     0x883490: add             x4, x4, HEAP, lsl #32
    // 0x883494: mov             x2, x3
    // 0x883498: stur            x4, [fp, #-0x10]
    // 0x88349c: r1 = Function '<anonymous closure>':.
    //     0x88349c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa668] AnonymousClosure: (0x88352c), in [package:dio/src/form_data.dart] FormData::_headerForFile (0x883288)
    //     0x8834a0: ldr             x1, [x1, #0x668]
    // 0x8834a4: r0 = AllocateClosure()
    //     0x8834a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8834a8: ldur            x1, [fp, #-0x10]
    // 0x8834ac: r2 = LoadClassIdInstr(r1)
    //     0x8834ac: ldur            x2, [x1, #-1]
    //     0x8834b0: ubfx            x2, x2, #0xc, #0x14
    // 0x8834b4: mov             x16, x0
    // 0x8834b8: mov             x0, x2
    // 0x8834bc: mov             x2, x16
    // 0x8834c0: r0 = GDT[cid_x0 + 0x6d3]()
    //     0x8834c0: add             lr, x0, #0x6d3
    //     0x8834c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8834c8: blr             lr
    // 0x8834cc: ldur            x0, [fp, #-0x28]
    // 0x8834d0: LoadField: r3 = r0->field_f
    //     0x8834d0: ldur            w3, [x0, #0xf]
    // 0x8834d4: DecompressPointer r3
    //     0x8834d4: add             x3, x3, HEAP, lsl #32
    // 0x8834d8: stur            x3, [fp, #-8]
    // 0x8834dc: r1 = Null
    //     0x8834dc: mov             x1, NULL
    // 0x8834e0: r2 = 6
    //     0x8834e0: movz            x2, #0x6
    // 0x8834e4: r0 = AllocateArray()
    //     0x8834e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8834e8: mov             x1, x0
    // 0x8834ec: ldur            x0, [fp, #-8]
    // 0x8834f0: StoreField: r1->field_f = r0
    //     0x8834f0: stur            w0, [x1, #0xf]
    // 0x8834f4: r16 = "\r\n"
    //     0x8834f4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x8834f8: ldr             x16, [x16, #0x648]
    // 0x8834fc: StoreField: r1->field_13 = r16
    //     0x8834fc: stur            w16, [x1, #0x13]
    // 0x883500: r16 = "\r\n"
    //     0x883500: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883504: ldr             x16, [x16, #0x648]
    // 0x883508: ArrayStore: r1[0] = r16  ; List_4
    //     0x883508: stur            w16, [x1, #0x17]
    // 0x88350c: str             x1, [SP]
    // 0x883510: r0 = _interpolate()
    //     0x883510: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883514: LeaveFrame
    //     0x883514: mov             SP, fp
    //     0x883518: ldp             fp, lr, [SP], #0x10
    // 0x88351c: ret
    //     0x88351c: ret             
    // 0x883520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883520: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883524: b               #0x8832b0
    // 0x883528: r0 = NullErrorSharedWithoutFPURegs()
    //     0x883528: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x88352c, size: 0x148
    // 0x88352c: EnterFrame
    //     0x88352c: stp             fp, lr, [SP, #-0x10]!
    //     0x883530: mov             fp, SP
    // 0x883534: AllocStack(0x28)
    //     0x883534: sub             SP, SP, #0x28
    // 0x883538: SetupParameters()
    //     0x883538: ldr             x0, [fp, #0x20]
    //     0x88353c: ldur            w2, [x0, #0x17]
    //     0x883540: add             x2, x2, HEAP, lsl #32
    //     0x883544: stur            x2, [fp, #-8]
    // 0x883548: CheckStackOverflow
    //     0x883548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88354c: cmp             SP, x16
    //     0x883550: b.ls            #0x883664
    // 0x883554: ldr             x1, [fp, #0x10]
    // 0x883558: r0 = LoadClassIdInstr(r1)
    //     0x883558: ldur            x0, [x1, #-1]
    //     0x88355c: ubfx            x0, x0, #0xc, #0x14
    // 0x883560: r0 = GDT[cid_x0 + 0xc907]()
    //     0x883560: movz            x17, #0xc907
    //     0x883564: add             lr, x0, x17
    //     0x883568: ldr             lr, [x21, lr, lsl #3]
    //     0x88356c: blr             lr
    // 0x883570: mov             x2, x0
    // 0x883574: stur            x2, [fp, #-0x10]
    // 0x883578: ldur            x3, [fp, #-8]
    // 0x88357c: ldr             x4, [fp, #0x18]
    // 0x883580: CheckStackOverflow
    //     0x883580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883584: cmp             SP, x16
    //     0x883588: b.ls            #0x88366c
    // 0x88358c: r0 = LoadClassIdInstr(r2)
    //     0x88358c: ldur            x0, [x2, #-1]
    //     0x883590: ubfx            x0, x0, #0xc, #0x14
    // 0x883594: mov             x1, x2
    // 0x883598: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x883598: add             lr, x0, #0x5ea
    //     0x88359c: ldr             lr, [x21, lr, lsl #3]
    //     0x8835a0: blr             lr
    // 0x8835a4: tbnz            w0, #4, #0x883654
    // 0x8835a8: ldr             x4, [fp, #0x18]
    // 0x8835ac: ldur            x3, [fp, #-8]
    // 0x8835b0: ldur            x2, [fp, #-0x10]
    // 0x8835b4: r0 = LoadClassIdInstr(r2)
    //     0x8835b4: ldur            x0, [x2, #-1]
    //     0x8835b8: ubfx            x0, x0, #0xc, #0x14
    // 0x8835bc: mov             x1, x2
    // 0x8835c0: r0 = GDT[cid_x0 + 0x655]()
    //     0x8835c0: add             lr, x0, #0x655
    //     0x8835c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8835c8: blr             lr
    // 0x8835cc: mov             x3, x0
    // 0x8835d0: ldur            x0, [fp, #-8]
    // 0x8835d4: stur            x3, [fp, #-0x20]
    // 0x8835d8: LoadField: r4 = r0->field_f
    //     0x8835d8: ldur            w4, [x0, #0xf]
    // 0x8835dc: DecompressPointer r4
    //     0x8835dc: add             x4, x4, HEAP, lsl #32
    // 0x8835e0: stur            x4, [fp, #-0x18]
    // 0x8835e4: r1 = Null
    //     0x8835e4: mov             x1, NULL
    // 0x8835e8: r2 = 10
    //     0x8835e8: movz            x2, #0xa
    // 0x8835ec: r0 = AllocateArray()
    //     0x8835ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8835f0: mov             x1, x0
    // 0x8835f4: ldur            x0, [fp, #-0x18]
    // 0x8835f8: StoreField: r1->field_f = r0
    //     0x8835f8: stur            w0, [x1, #0xf]
    // 0x8835fc: r16 = "\r\n"
    //     0x8835fc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883600: ldr             x16, [x16, #0x648]
    // 0x883604: StoreField: r1->field_13 = r16
    //     0x883604: stur            w16, [x1, #0x13]
    // 0x883608: ldr             x0, [fp, #0x18]
    // 0x88360c: ArrayStore: r1[0] = r0  ; List_4
    //     0x88360c: stur            w0, [x1, #0x17]
    // 0x883610: r16 = ": "
    //     0x883610: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] ": "
    // 0x883614: StoreField: r1->field_1b = r16
    //     0x883614: stur            w16, [x1, #0x1b]
    // 0x883618: ldur            x2, [fp, #-0x20]
    // 0x88361c: StoreField: r1->field_1f = r2
    //     0x88361c: stur            w2, [x1, #0x1f]
    // 0x883620: str             x1, [SP]
    // 0x883624: r0 = _interpolate()
    //     0x883624: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883628: ldur            x1, [fp, #-8]
    // 0x88362c: StoreField: r1->field_f = r0
    //     0x88362c: stur            w0, [x1, #0xf]
    //     0x883630: ldurb           w16, [x1, #-1]
    //     0x883634: ldurb           w17, [x0, #-1]
    //     0x883638: and             x16, x17, x16, lsr #2
    //     0x88363c: tst             x16, HEAP, lsr #32
    //     0x883640: b.eq            #0x883648
    //     0x883644: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x883648: mov             x3, x1
    // 0x88364c: ldur            x2, [fp, #-0x10]
    // 0x883650: b               #0x88357c
    // 0x883654: r0 = Null
    //     0x883654: mov             x0, NULL
    // 0x883658: LeaveFrame
    //     0x883658: mov             SP, fp
    //     0x88365c: ldp             fp, lr, [SP], #0x10
    // 0x883660: ret
    //     0x883660: ret             
    // 0x883664: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883664: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883668: b               #0x883554
    // 0x88366c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88366c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883670: b               #0x88358c
  }
  _ _browserEncode(/* No info */) {
    // ** addr: 0x883674, size: 0xa0
    // 0x883674: EnterFrame
    //     0x883674: stp             fp, lr, [SP, #-0x10]!
    //     0x883678: mov             fp, SP
    // 0x88367c: AllocStack(0x38)
    //     0x88367c: sub             SP, SP, #0x38
    // 0x883680: SetupParameters(FormData this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x883680: mov             x0, x1
    //     0x883684: mov             x1, x2
    //     0x883688: stur            x2, [fp, #-8]
    // 0x88368c: CheckStackOverflow
    //     0x88368c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883690: cmp             SP, x16
    //     0x883694: b.ls            #0x88370c
    // 0x883698: cmp             w1, NULL
    // 0x88369c: b.ne            #0x8836a8
    // 0x8836a0: r0 = Null
    //     0x8836a0: mov             x0, NULL
    // 0x8836a4: b               #0x883700
    // 0x8836a8: r16 = "\\r\\n|\\r|\\n"
    //     0x8836a8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa678] "\\r\\n|\\r|\\n"
    //     0x8836ac: ldr             x16, [x16, #0x678]
    // 0x8836b0: stp             x16, NULL, [SP, #0x20]
    // 0x8836b4: r16 = false
    //     0x8836b4: add             x16, NULL, #0x30  ; false
    // 0x8836b8: r30 = true
    //     0x8836b8: add             lr, NULL, #0x20  ; true
    // 0x8836bc: stp             lr, x16, [SP, #0x10]
    // 0x8836c0: r16 = false
    //     0x8836c0: add             x16, NULL, #0x30  ; false
    // 0x8836c4: r30 = false
    //     0x8836c4: add             lr, NULL, #0x30  ; false
    // 0x8836c8: stp             lr, x16, [SP]
    // 0x8836cc: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x8836cc: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x8836d0: r0 = _RegExp()
    //     0x8836d0: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x8836d4: ldur            x1, [fp, #-8]
    // 0x8836d8: mov             x2, x0
    // 0x8836dc: r3 = "%0D%0A"
    //     0x8836dc: add             x3, PP, #0xa, lsl #12  ; [pp+0xa680] "%0D%0A"
    //     0x8836e0: ldr             x3, [x3, #0x680]
    // 0x8836e4: r0 = replaceAll()
    //     0x8836e4: bl              #0x628c30  ; [dart:core] _StringBase::replaceAll
    // 0x8836e8: mov             x1, x0
    // 0x8836ec: r2 = "\""
    //     0x8836ec: add             x2, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0x8836f0: ldr             x2, [x2, #0x550]
    // 0x8836f4: r3 = "%22"
    //     0x8836f4: add             x3, PP, #0xa, lsl #12  ; [pp+0xa688] "%22"
    //     0x8836f8: ldr             x3, [x3, #0x688]
    // 0x8836fc: r0 = replaceAll()
    //     0x8836fc: bl              #0x628c30  ; [dart:core] _StringBase::replaceAll
    // 0x883700: LeaveFrame
    //     0x883700: mov             SP, fp
    //     0x883704: ldp             fp, lr, [SP], #0x10
    // 0x883708: ret
    //     0x883708: ret             
    // 0x88370c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88370c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883710: b               #0x883698
  }
  _ finalize(/* No info */) {
    // ** addr: 0x883714, size: 0x3c8
    // 0x883714: EnterFrame
    //     0x883714: stp             fp, lr, [SP, #-0x10]!
    //     0x883718: mov             fp, SP
    // 0x88371c: AllocStack(0x60)
    //     0x88371c: sub             SP, SP, #0x60
    // 0x883720: SetupParameters(FormData this /* r1 => r1, fp-0x8 */)
    //     0x883720: stur            x1, [fp, #-8]
    // 0x883724: CheckStackOverflow
    //     0x883724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883728: cmp             SP, x16
    //     0x88372c: b.ls            #0x883ac0
    // 0x883730: r1 = 4
    //     0x883730: movz            x1, #0x4
    // 0x883734: r0 = AllocateContext()
    //     0x883734: bl              #0x16f6108  ; AllocateContextStub
    // 0x883738: mov             x2, x0
    // 0x88373c: ldur            x0, [fp, #-8]
    // 0x883740: stur            x2, [fp, #-0x10]
    // 0x883744: StoreField: r2->field_f = r0
    //     0x883744: stur            w0, [x2, #0xf]
    // 0x883748: LoadField: r1 = r0->field_1b
    //     0x883748: ldur            w1, [x0, #0x1b]
    // 0x88374c: DecompressPointer r1
    //     0x88374c: add             x1, x1, HEAP, lsl #32
    // 0x883750: tbz             w1, #4, #0x883a80
    // 0x883754: r1 = true
    //     0x883754: add             x1, NULL, #0x20  ; true
    // 0x883758: StoreField: r0->field_1b = r1
    //     0x883758: stur            w1, [x0, #0x1b]
    // 0x88375c: r16 = false
    //     0x88375c: add             x16, NULL, #0x30  ; false
    // 0x883760: str             x16, [SP]
    // 0x883764: r1 = <Uint8List>
    //     0x883764: add             x1, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x883768: ldr             x1, [x1, #0x598]
    // 0x88376c: r4 = const [0, 0x2, 0x1, 0x1, sync, 0x1, null]
    //     0x88376c: add             x4, PP, #9, lsl #12  ; [pp+0x9100] List(7) [0, 0x2, 0x1, 0x1, "sync", 0x1, Null]
    //     0x883770: ldr             x4, [x4, #0x100]
    // 0x883774: r0 = StreamController()
    //     0x883774: bl              #0x657960  ; [dart:async] StreamController::StreamController
    // 0x883778: mov             x4, x0
    // 0x88377c: ldur            x3, [fp, #-0x10]
    // 0x883780: stur            x4, [fp, #-0x18]
    // 0x883784: StoreField: r3->field_13 = r0
    //     0x883784: stur            w0, [x3, #0x13]
    //     0x883788: ldurb           w16, [x3, #-1]
    //     0x88378c: ldurb           w17, [x0, #-1]
    //     0x883790: and             x16, x17, x16, lsr #2
    //     0x883794: tst             x16, HEAP, lsr #32
    //     0x883798: b.eq            #0x8837a0
    //     0x88379c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8837a0: mov             x2, x3
    // 0x8837a4: r1 = Function 'writeLine':.
    //     0x8837a4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa690] AnonymousClosure: (0x884154), in [package:dio/src/form_data.dart] FormData::finalize (0x883714)
    //     0x8837a8: ldr             x1, [x1, #0x690]
    // 0x8837ac: r0 = AllocateClosure()
    //     0x8837ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8837b0: ldur            x3, [fp, #-0x10]
    // 0x8837b4: ArrayStore: r3[0] = r0  ; List_4
    //     0x8837b4: stur            w0, [x3, #0x17]
    //     0x8837b8: ldurb           w16, [x3, #-1]
    //     0x8837bc: ldurb           w17, [x0, #-1]
    //     0x8837c0: and             x16, x17, x16, lsr #2
    //     0x8837c4: tst             x16, HEAP, lsr #32
    //     0x8837c8: b.eq            #0x8837d0
    //     0x8837cc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8837d0: mov             x2, x3
    // 0x8837d4: r1 = Function 'writeUtf8':.
    //     0x8837d4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa698] AnonymousClosure: (0x8840f0), in [package:dio/src/form_data.dart] FormData::finalize (0x883714)
    //     0x8837d8: ldr             x1, [x1, #0x698]
    // 0x8837dc: r0 = AllocateClosure()
    //     0x8837dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8837e0: ldur            x3, [fp, #-0x10]
    // 0x8837e4: StoreField: r3->field_1b = r0
    //     0x8837e4: stur            w0, [x3, #0x1b]
    //     0x8837e8: ldurb           w16, [x3, #-1]
    //     0x8837ec: ldurb           w17, [x0, #-1]
    //     0x8837f0: and             x16, x17, x16, lsr #2
    //     0x8837f4: tst             x16, HEAP, lsr #32
    //     0x8837f8: b.eq            #0x883800
    //     0x8837fc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x883800: ldur            x0, [fp, #-8]
    // 0x883804: LoadField: r4 = r0->field_13
    //     0x883804: ldur            w4, [x0, #0x13]
    // 0x883808: DecompressPointer r4
    //     0x883808: add             x4, x4, HEAP, lsl #32
    // 0x88380c: stur            x4, [fp, #-0x38]
    // 0x883810: LoadField: r1 = r4->field_b
    //     0x883810: ldur            w1, [x4, #0xb]
    // 0x883814: r5 = LoadInt32Instr(r1)
    //     0x883814: sbfx            x5, x1, #1, #0x1f
    // 0x883818: stur            x5, [fp, #-0x30]
    // 0x88381c: r1 = 0
    //     0x88381c: movz            x1, #0
    // 0x883820: CheckStackOverflow
    //     0x883820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883824: cmp             SP, x16
    //     0x883828: b.ls            #0x883ac8
    // 0x88382c: LoadField: r2 = r4->field_b
    //     0x88382c: ldur            w2, [x4, #0xb]
    // 0x883830: r6 = LoadInt32Instr(r2)
    //     0x883830: sbfx            x6, x2, #1, #0x1f
    // 0x883834: cmp             x5, x6
    // 0x883838: b.ne            #0x883aa0
    // 0x88383c: cmp             x1, x6
    // 0x883840: b.ge            #0x8839e8
    // 0x883844: LoadField: r2 = r4->field_f
    //     0x883844: ldur            w2, [x4, #0xf]
    // 0x883848: DecompressPointer r2
    //     0x883848: add             x2, x2, HEAP, lsl #32
    // 0x88384c: ArrayLoad: r6 = r2[r1]  ; Unknown_4
    //     0x88384c: add             x16, x2, x1, lsl #2
    //     0x883850: ldur            w6, [x16, #0xf]
    // 0x883854: DecompressPointer r6
    //     0x883854: add             x6, x6, HEAP, lsl #32
    // 0x883858: stur            x6, [fp, #-0x28]
    // 0x88385c: add             x7, x1, #1
    // 0x883860: stur            x7, [fp, #-0x20]
    // 0x883864: r1 = Null
    //     0x883864: mov             x1, NULL
    // 0x883868: r2 = 6
    //     0x883868: movz            x2, #0x6
    // 0x88386c: r0 = AllocateArray()
    //     0x88386c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x883870: r16 = "--"
    //     0x883870: add             x16, PP, #0xa, lsl #12  ; [pp+0xa6a0] "--"
    //     0x883874: ldr             x16, [x16, #0x6a0]
    // 0x883878: StoreField: r0->field_f = r16
    //     0x883878: stur            w16, [x0, #0xf]
    // 0x88387c: ldur            x1, [fp, #-8]
    // 0x883880: LoadField: r2 = r1->field_f
    //     0x883880: ldur            w2, [x1, #0xf]
    // 0x883884: DecompressPointer r2
    //     0x883884: add             x2, x2, HEAP, lsl #32
    // 0x883888: r16 = Sentinel
    //     0x883888: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x88388c: cmp             w2, w16
    // 0x883890: b.eq            #0x883ad0
    // 0x883894: StoreField: r0->field_13 = r2
    //     0x883894: stur            w2, [x0, #0x13]
    // 0x883898: r16 = "\r\n"
    //     0x883898: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x88389c: ldr             x16, [x16, #0x648]
    // 0x8838a0: ArrayStore: r0[0] = r16  ; List_4
    //     0x8838a0: stur            w16, [x0, #0x17]
    // 0x8838a4: str             x0, [SP]
    // 0x8838a8: r0 = _interpolate()
    //     0x8838a8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8838ac: mov             x2, x0
    // 0x8838b0: r1 = Instance_Utf8Encoder
    //     0x8838b0: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x8838b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8838b4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8838b8: r0 = convert()
    //     0x8838b8: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x8838bc: ldur            x1, [fp, #-0x18]
    // 0x8838c0: mov             x2, x0
    // 0x8838c4: r0 = add()
    //     0x8838c4: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x8838c8: ldur            x0, [fp, #-0x28]
    // 0x8838cc: LoadField: r3 = r0->field_b
    //     0x8838cc: ldur            w3, [x0, #0xb]
    // 0x8838d0: DecompressPointer r3
    //     0x8838d0: add             x3, x3, HEAP, lsl #32
    // 0x8838d4: stur            x3, [fp, #-0x40]
    // 0x8838d8: r1 = Null
    //     0x8838d8: mov             x1, NULL
    // 0x8838dc: r2 = 12
    //     0x8838dc: movz            x2, #0xc
    // 0x8838e0: r0 = AllocateArray()
    //     0x8838e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8838e4: stur            x0, [fp, #-0x48]
    // 0x8838e8: r16 = "content-disposition"
    //     0x8838e8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa638] "content-disposition"
    //     0x8838ec: ldr             x16, [x16, #0x638]
    // 0x8838f0: StoreField: r0->field_f = r16
    //     0x8838f0: stur            w16, [x0, #0xf]
    // 0x8838f4: r16 = ": form-data; name=\""
    //     0x8838f4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa640] ": form-data; name=\""
    //     0x8838f8: ldr             x16, [x16, #0x640]
    // 0x8838fc: StoreField: r0->field_13 = r16
    //     0x8838fc: stur            w16, [x0, #0x13]
    // 0x883900: ldur            x1, [fp, #-8]
    // 0x883904: ldur            x2, [fp, #-0x40]
    // 0x883908: r0 = _browserEncode()
    //     0x883908: bl              #0x883674  ; [package:dio/src/form_data.dart] FormData::_browserEncode
    // 0x88390c: ldur            x1, [fp, #-0x48]
    // 0x883910: ArrayStore: r1[2] = r0  ; List_4
    //     0x883910: add             x25, x1, #0x17
    //     0x883914: str             w0, [x25]
    //     0x883918: tbz             w0, #0, #0x883934
    //     0x88391c: ldurb           w16, [x1, #-1]
    //     0x883920: ldurb           w17, [x0, #-1]
    //     0x883924: and             x16, x17, x16, lsr #2
    //     0x883928: tst             x16, HEAP, lsr #32
    //     0x88392c: b.eq            #0x883934
    //     0x883930: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x883934: ldur            x0, [fp, #-0x48]
    // 0x883938: r16 = "\""
    //     0x883938: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0x88393c: ldr             x16, [x16, #0x550]
    // 0x883940: StoreField: r0->field_1b = r16
    //     0x883940: stur            w16, [x0, #0x1b]
    // 0x883944: r16 = "\r\n"
    //     0x883944: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883948: ldr             x16, [x16, #0x648]
    // 0x88394c: StoreField: r0->field_1f = r16
    //     0x88394c: stur            w16, [x0, #0x1f]
    // 0x883950: r16 = "\r\n"
    //     0x883950: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883954: ldr             x16, [x16, #0x648]
    // 0x883958: StoreField: r0->field_23 = r16
    //     0x883958: stur            w16, [x0, #0x23]
    // 0x88395c: str             x0, [SP]
    // 0x883960: r0 = _interpolate()
    //     0x883960: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883964: mov             x2, x0
    // 0x883968: r1 = Instance_Utf8Encoder
    //     0x883968: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x88396c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x88396c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x883970: r0 = convert()
    //     0x883970: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883974: ldur            x1, [fp, #-0x18]
    // 0x883978: mov             x2, x0
    // 0x88397c: r0 = add()
    //     0x88397c: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x883980: ldur            x0, [fp, #-0x28]
    // 0x883984: LoadField: r2 = r0->field_f
    //     0x883984: ldur            w2, [x0, #0xf]
    // 0x883988: DecompressPointer r2
    //     0x883988: add             x2, x2, HEAP, lsl #32
    // 0x88398c: r1 = Instance_Utf8Encoder
    //     0x88398c: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x883990: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x883990: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x883994: r0 = convert()
    //     0x883994: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883998: ldur            x1, [fp, #-0x18]
    // 0x88399c: mov             x2, x0
    // 0x8839a0: r0 = add()
    //     0x8839a0: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x8839a4: r0 = InitLateStaticField(0xd40) // [package:dio/src/form_data.dart] ::_rnU8
    //     0x8839a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8839a8: ldr             x0, [x0, #0x1a80]
    //     0x8839ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8839b0: cmp             w0, w16
    //     0x8839b4: b.ne            #0x8839c4
    //     0x8839b8: add             x2, PP, #0xa, lsl #12  ; [pp+0xa6a8] Field <::._rnU8@911426596>: static late final (offset: 0xd40)
    //     0x8839bc: ldr             x2, [x2, #0x6a8]
    //     0x8839c0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8839c4: ldur            x1, [fp, #-0x18]
    // 0x8839c8: mov             x2, x0
    // 0x8839cc: r0 = add()
    //     0x8839cc: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x8839d0: ldur            x1, [fp, #-0x20]
    // 0x8839d4: ldur            x0, [fp, #-8]
    // 0x8839d8: ldur            x3, [fp, #-0x10]
    // 0x8839dc: ldur            x4, [fp, #-0x38]
    // 0x8839e0: ldur            x5, [fp, #-0x30]
    // 0x8839e4: b               #0x883820
    // 0x8839e8: ldur            x0, [fp, #-0x18]
    // 0x8839ec: ldur            x2, [fp, #-0x10]
    // 0x8839f0: r1 = Function '<anonymous closure>':.
    //     0x8839f0: add             x1, PP, #0xa, lsl #12  ; [pp+0xa6b0] AnonymousClosure: (0x883c10), in [package:dio/src/form_data.dart] FormData::finalize (0x883714)
    //     0x8839f4: ldr             x1, [x1, #0x6b0]
    // 0x8839f8: r0 = AllocateClosure()
    //     0x8839f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8839fc: mov             x2, x0
    // 0x883a00: r1 = <void?>
    //     0x883a00: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x883a04: r0 = Future()
    //     0x883a04: bl              #0x6321e0  ; [dart:async] Future::Future
    // 0x883a08: ldur            x2, [fp, #-0x10]
    // 0x883a0c: r1 = Function '<anonymous closure>':.
    //     0x883a0c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa6b8] AnonymousClosure: (0x883b24), in [package:dio/src/form_data.dart] FormData::finalize (0x883714)
    //     0x883a10: ldr             x1, [x1, #0x6b8]
    // 0x883a14: stur            x0, [fp, #-8]
    // 0x883a18: r0 = AllocateClosure()
    //     0x883a18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x883a1c: r16 = <Null?>
    //     0x883a1c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x883a20: ldur            lr, [fp, #-8]
    // 0x883a24: stp             lr, x16, [SP, #8]
    // 0x883a28: str             x0, [SP]
    // 0x883a2c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x883a2c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x883a30: r0 = then()
    //     0x883a30: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x883a34: ldur            x2, [fp, #-0x10]
    // 0x883a38: r1 = Function '<anonymous closure>':.
    //     0x883a38: add             x1, PP, #0xa, lsl #12  ; [pp+0xa6c0] AnonymousClosure: (0x883adc), in [package:dio/src/form_data.dart] FormData::finalize (0x883714)
    //     0x883a3c: ldr             x1, [x1, #0x6c0]
    // 0x883a40: stur            x0, [fp, #-8]
    // 0x883a44: r0 = AllocateClosure()
    //     0x883a44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x883a48: ldur            x1, [fp, #-8]
    // 0x883a4c: mov             x2, x0
    // 0x883a50: r0 = whenComplete()
    //     0x883a50: bl              #0x167b0bc  ; [dart:async] _Future::whenComplete
    // 0x883a54: ldur            x0, [fp, #-0x18]
    // 0x883a58: LoadField: r1 = r0->field_7
    //     0x883a58: ldur            w1, [x0, #7]
    // 0x883a5c: DecompressPointer r1
    //     0x883a5c: add             x1, x1, HEAP, lsl #32
    // 0x883a60: r0 = _ControllerStream()
    //     0x883a60: bl              #0x698904  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0x883a64: mov             x1, x0
    // 0x883a68: ldur            x0, [fp, #-0x18]
    // 0x883a6c: StoreField: r1->field_b = r0
    //     0x883a6c: stur            w0, [x1, #0xb]
    // 0x883a70: mov             x0, x1
    // 0x883a74: LeaveFrame
    //     0x883a74: mov             SP, fp
    //     0x883a78: ldp             fp, lr, [SP], #0x10
    // 0x883a7c: ret
    //     0x883a7c: ret             
    // 0x883a80: r0 = StateError()
    //     0x883a80: bl              #0x622864  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x883a84: mov             x1, x0
    // 0x883a88: r0 = "The FormData has already been finalized. This typically means you are using the same FormData in repeated requests."
    //     0x883a88: add             x0, PP, #0xa, lsl #12  ; [pp+0xa6c8] "The FormData has already been finalized. This typically means you are using the same FormData in repeated requests."
    //     0x883a8c: ldr             x0, [x0, #0x6c8]
    // 0x883a90: StoreField: r1->field_b = r0
    //     0x883a90: stur            w0, [x1, #0xb]
    // 0x883a94: mov             x0, x1
    // 0x883a98: r0 = Throw()
    //     0x883a98: bl              #0x16f5420  ; ThrowStub
    // 0x883a9c: brk             #0
    // 0x883aa0: mov             x0, x4
    // 0x883aa4: r0 = ConcurrentModificationError()
    //     0x883aa4: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x883aa8: mov             x1, x0
    // 0x883aac: ldur            x0, [fp, #-0x38]
    // 0x883ab0: StoreField: r1->field_b = r0
    //     0x883ab0: stur            w0, [x1, #0xb]
    // 0x883ab4: mov             x0, x1
    // 0x883ab8: r0 = Throw()
    //     0x883ab8: bl              #0x16f5420  ; ThrowStub
    // 0x883abc: brk             #0
    // 0x883ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883ac0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883ac4: b               #0x883730
    // 0x883ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883ac8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883acc: b               #0x88382c
    // 0x883ad0: r9 = _boundary
    //     0x883ad0: add             x9, PP, #0xa, lsl #12  ; [pp+0xa560] Field <FormData._boundary@911426596>: late (offset: 0x10)
    //     0x883ad4: ldr             x9, [x9, #0x560]
    // 0x883ad8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x883ad8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x883adc, size: 0x48
    // 0x883adc: EnterFrame
    //     0x883adc: stp             fp, lr, [SP, #-0x10]!
    //     0x883ae0: mov             fp, SP
    // 0x883ae4: ldr             x0, [fp, #0x10]
    // 0x883ae8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x883ae8: ldur            w1, [x0, #0x17]
    // 0x883aec: DecompressPointer r1
    //     0x883aec: add             x1, x1, HEAP, lsl #32
    // 0x883af0: CheckStackOverflow
    //     0x883af0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883af4: cmp             SP, x16
    //     0x883af8: b.ls            #0x883b1c
    // 0x883afc: LoadField: r0 = r1->field_13
    //     0x883afc: ldur            w0, [x1, #0x13]
    // 0x883b00: DecompressPointer r0
    //     0x883b00: add             x0, x0, HEAP, lsl #32
    // 0x883b04: mov             x1, x0
    // 0x883b08: r0 = close()
    //     0x883b08: bl              #0x6f11fc  ; [dart:async] _StreamController::close
    // 0x883b0c: r0 = Null
    //     0x883b0c: mov             x0, NULL
    // 0x883b10: LeaveFrame
    //     0x883b10: mov             SP, fp
    //     0x883b14: ldp             fp, lr, [SP], #0x10
    // 0x883b18: ret
    //     0x883b18: ret             
    // 0x883b1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883b1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883b20: b               #0x883afc
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x883b24, size: 0xec
    // 0x883b24: EnterFrame
    //     0x883b24: stp             fp, lr, [SP, #-0x10]!
    //     0x883b28: mov             fp, SP
    // 0x883b2c: AllocStack(0x18)
    //     0x883b2c: sub             SP, SP, #0x18
    // 0x883b30: SetupParameters()
    //     0x883b30: ldr             x0, [fp, #0x18]
    //     0x883b34: ldur            w3, [x0, #0x17]
    //     0x883b38: add             x3, x3, HEAP, lsl #32
    //     0x883b3c: stur            x3, [fp, #-0x10]
    // 0x883b40: CheckStackOverflow
    //     0x883b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883b44: cmp             SP, x16
    //     0x883b48: b.ls            #0x883bfc
    // 0x883b4c: LoadField: r0 = r3->field_1b
    //     0x883b4c: ldur            w0, [x3, #0x1b]
    // 0x883b50: DecompressPointer r0
    //     0x883b50: add             x0, x0, HEAP, lsl #32
    // 0x883b54: stur            x0, [fp, #-8]
    // 0x883b58: r1 = Null
    //     0x883b58: mov             x1, NULL
    // 0x883b5c: r2 = 8
    //     0x883b5c: movz            x2, #0x8
    // 0x883b60: r0 = AllocateArray()
    //     0x883b60: bl              #0x16f7198  ; AllocateArrayStub
    // 0x883b64: r16 = "--"
    //     0x883b64: add             x16, PP, #0xa, lsl #12  ; [pp+0xa6a0] "--"
    //     0x883b68: ldr             x16, [x16, #0x6a0]
    // 0x883b6c: StoreField: r0->field_f = r16
    //     0x883b6c: stur            w16, [x0, #0xf]
    // 0x883b70: ldur            x1, [fp, #-0x10]
    // 0x883b74: LoadField: r2 = r1->field_f
    //     0x883b74: ldur            w2, [x1, #0xf]
    // 0x883b78: DecompressPointer r2
    //     0x883b78: add             x2, x2, HEAP, lsl #32
    // 0x883b7c: LoadField: r1 = r2->field_f
    //     0x883b7c: ldur            w1, [x2, #0xf]
    // 0x883b80: DecompressPointer r1
    //     0x883b80: add             x1, x1, HEAP, lsl #32
    // 0x883b84: r16 = Sentinel
    //     0x883b84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x883b88: cmp             w1, w16
    // 0x883b8c: b.eq            #0x883c04
    // 0x883b90: StoreField: r0->field_13 = r1
    //     0x883b90: stur            w1, [x0, #0x13]
    // 0x883b94: r16 = "--"
    //     0x883b94: add             x16, PP, #0xa, lsl #12  ; [pp+0xa6a0] "--"
    //     0x883b98: ldr             x16, [x16, #0x6a0]
    // 0x883b9c: ArrayStore: r0[0] = r16  ; List_4
    //     0x883b9c: stur            w16, [x0, #0x17]
    // 0x883ba0: r16 = "\r\n"
    //     0x883ba0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883ba4: ldr             x16, [x16, #0x648]
    // 0x883ba8: StoreField: r0->field_1b = r16
    //     0x883ba8: stur            w16, [x0, #0x1b]
    // 0x883bac: str             x0, [SP]
    // 0x883bb0: r0 = _interpolate()
    //     0x883bb0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883bb4: mov             x1, x0
    // 0x883bb8: ldur            x0, [fp, #-8]
    // 0x883bbc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x883bbc: ldur            w2, [x0, #0x17]
    // 0x883bc0: DecompressPointer r2
    //     0x883bc0: add             x2, x2, HEAP, lsl #32
    // 0x883bc4: LoadField: r0 = r2->field_13
    //     0x883bc4: ldur            w0, [x2, #0x13]
    // 0x883bc8: DecompressPointer r0
    //     0x883bc8: add             x0, x0, HEAP, lsl #32
    // 0x883bcc: mov             x2, x1
    // 0x883bd0: stur            x0, [fp, #-8]
    // 0x883bd4: r1 = Instance_Utf8Encoder
    //     0x883bd4: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x883bd8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x883bd8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x883bdc: r0 = convert()
    //     0x883bdc: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883be0: ldur            x1, [fp, #-8]
    // 0x883be4: mov             x2, x0
    // 0x883be8: r0 = add()
    //     0x883be8: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x883bec: r0 = Null
    //     0x883bec: mov             x0, NULL
    // 0x883bf0: LeaveFrame
    //     0x883bf0: mov             SP, fp
    //     0x883bf4: ldp             fp, lr, [SP], #0x10
    // 0x883bf8: ret
    //     0x883bf8: ret             
    // 0x883bfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883bfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883c00: b               #0x883b4c
    // 0x883c04: r9 = _boundary
    //     0x883c04: add             x9, PP, #0xa, lsl #12  ; [pp+0xa560] Field <FormData._boundary@911426596>: late (offset: 0x10)
    //     0x883c08: ldr             x9, [x9, #0x560]
    // 0x883c0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x883c0c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x883c10, size: 0x390
    // 0x883c10: EnterFrame
    //     0x883c10: stp             fp, lr, [SP, #-0x10]!
    //     0x883c14: mov             fp, SP
    // 0x883c18: AllocStack(0x88)
    //     0x883c18: sub             SP, SP, #0x88
    // 0x883c1c: SetupParameters(FormData this /* r1 */)
    //     0x883c1c: stur            NULL, [fp, #-8]
    //     0x883c20: movz            x0, #0
    //     0x883c24: add             x1, fp, w0, sxtw #2
    //     0x883c28: ldr             x1, [x1, #0x10]
    //     0x883c2c: ldur            w2, [x1, #0x17]
    //     0x883c30: add             x2, x2, HEAP, lsl #32
    //     0x883c34: stur            x2, [fp, #-0x10]
    // 0x883c38: CheckStackOverflow
    //     0x883c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883c3c: cmp             SP, x16
    //     0x883c40: b.ls            #0x883f80
    // 0x883c44: InitAsync() -> Future<void?>
    //     0x883c44: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x883c48: bl              #0x6326e0  ; InitAsyncStub
    // 0x883c4c: ldur            x0, [fp, #-0x10]
    // 0x883c50: LoadField: r1 = r0->field_f
    //     0x883c50: ldur            w1, [x0, #0xf]
    // 0x883c54: DecompressPointer r1
    //     0x883c54: add             x1, x1, HEAP, lsl #32
    // 0x883c58: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x883c58: ldur            w3, [x1, #0x17]
    // 0x883c5c: DecompressPointer r3
    //     0x883c5c: add             x3, x3, HEAP, lsl #32
    // 0x883c60: stur            x3, [fp, #-0x38]
    // 0x883c64: LoadField: r4 = r3->field_7
    //     0x883c64: ldur            w4, [x3, #7]
    // 0x883c68: DecompressPointer r4
    //     0x883c68: add             x4, x4, HEAP, lsl #32
    // 0x883c6c: stur            x4, [fp, #-0x30]
    // 0x883c70: LoadField: r1 = r3->field_b
    //     0x883c70: ldur            w1, [x3, #0xb]
    // 0x883c74: r5 = LoadInt32Instr(r1)
    //     0x883c74: sbfx            x5, x1, #1, #0x1f
    // 0x883c78: stur            x5, [fp, #-0x28]
    // 0x883c7c: LoadField: r1 = r0->field_1b
    //     0x883c7c: ldur            w1, [x0, #0x1b]
    // 0x883c80: DecompressPointer r1
    //     0x883c80: add             x1, x1, HEAP, lsl #32
    // 0x883c84: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x883c84: ldur            w2, [x1, #0x17]
    // 0x883c88: DecompressPointer r2
    //     0x883c88: add             x2, x2, HEAP, lsl #32
    // 0x883c8c: LoadField: r6 = r2->field_13
    //     0x883c8c: ldur            w6, [x2, #0x13]
    // 0x883c90: DecompressPointer r6
    //     0x883c90: add             x6, x6, HEAP, lsl #32
    // 0x883c94: stur            x6, [fp, #-0x20]
    // 0x883c98: LoadField: r7 = r0->field_13
    //     0x883c98: ldur            w7, [x0, #0x13]
    // 0x883c9c: DecompressPointer r7
    //     0x883c9c: add             x7, x7, HEAP, lsl #32
    // 0x883ca0: mov             x2, x7
    // 0x883ca4: stur            x7, [fp, #-0x18]
    // 0x883ca8: r1 = Function 'add':.
    //     0x883ca8: add             x1, PP, #9, lsl #12  ; [pp+0x9168] AnonymousClosure: (0x63611c), in [dart:async] _StreamController::add (0x65124c)
    //     0x883cac: ldr             x1, [x1, #0x168]
    // 0x883cb0: r0 = AllocateClosure()
    //     0x883cb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x883cb4: ldur            x2, [fp, #-0x18]
    // 0x883cb8: r1 = Function 'addError':.
    //     0x883cb8: add             x1, PP, #9, lsl #12  ; [pp+0x9110] AnonymousClosure: (0x679a58), in [dart:async] _StreamController::addError (0x6799c4)
    //     0x883cbc: ldr             x1, [x1, #0x110]
    // 0x883cc0: stur            x0, [fp, #-0x18]
    // 0x883cc4: r0 = AllocateClosure()
    //     0x883cc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x883cc8: mov             x3, x0
    // 0x883ccc: ldur            x0, [fp, #-0x10]
    // 0x883cd0: stur            x3, [fp, #-0x58]
    // 0x883cd4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x883cd4: ldur            w1, [x0, #0x17]
    // 0x883cd8: DecompressPointer r1
    //     0x883cd8: add             x1, x1, HEAP, lsl #32
    // 0x883cdc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x883cdc: ldur            w2, [x1, #0x17]
    // 0x883ce0: DecompressPointer r2
    //     0x883ce0: add             x2, x2, HEAP, lsl #32
    // 0x883ce4: LoadField: r4 = r2->field_13
    //     0x883ce4: ldur            w4, [x2, #0x13]
    // 0x883ce8: DecompressPointer r4
    //     0x883ce8: add             x4, x4, HEAP, lsl #32
    // 0x883cec: stur            x4, [fp, #-0x50]
    // 0x883cf0: r1 = 0
    //     0x883cf0: movz            x1, #0
    // 0x883cf4: ldur            x5, [fp, #-0x38]
    // 0x883cf8: ldur            x6, [fp, #-0x28]
    // 0x883cfc: CheckStackOverflow
    //     0x883cfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x883d00: cmp             SP, x16
    //     0x883d04: b.ls            #0x883f88
    // 0x883d08: LoadField: r2 = r5->field_b
    //     0x883d08: ldur            w2, [x5, #0xb]
    // 0x883d0c: r7 = LoadInt32Instr(r2)
    //     0x883d0c: sbfx            x7, x2, #1, #0x1f
    // 0x883d10: cmp             x6, x7
    // 0x883d14: b.ne            #0x883f60
    // 0x883d18: cmp             x1, x7
    // 0x883d1c: b.ge            #0x883f38
    // 0x883d20: LoadField: r2 = r5->field_f
    //     0x883d20: ldur            w2, [x5, #0xf]
    // 0x883d24: DecompressPointer r2
    //     0x883d24: add             x2, x2, HEAP, lsl #32
    // 0x883d28: ArrayLoad: r7 = r2[r1]  ; Unknown_4
    //     0x883d28: add             x16, x2, x1, lsl #2
    //     0x883d2c: ldur            w7, [x16, #0xf]
    // 0x883d30: DecompressPointer r7
    //     0x883d30: add             x7, x7, HEAP, lsl #32
    // 0x883d34: stur            x7, [fp, #-0x48]
    // 0x883d38: add             x8, x1, #1
    // 0x883d3c: stur            x8, [fp, #-0x40]
    // 0x883d40: r1 = Null
    //     0x883d40: mov             x1, NULL
    // 0x883d44: r2 = 6
    //     0x883d44: movz            x2, #0x6
    // 0x883d48: r0 = AllocateArray()
    //     0x883d48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x883d4c: r16 = "--"
    //     0x883d4c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa6a0] "--"
    //     0x883d50: ldr             x16, [x16, #0x6a0]
    // 0x883d54: StoreField: r0->field_f = r16
    //     0x883d54: stur            w16, [x0, #0xf]
    // 0x883d58: ldur            x1, [fp, #-0x10]
    // 0x883d5c: LoadField: r2 = r1->field_f
    //     0x883d5c: ldur            w2, [x1, #0xf]
    // 0x883d60: DecompressPointer r2
    //     0x883d60: add             x2, x2, HEAP, lsl #32
    // 0x883d64: LoadField: r3 = r2->field_f
    //     0x883d64: ldur            w3, [x2, #0xf]
    // 0x883d68: DecompressPointer r3
    //     0x883d68: add             x3, x3, HEAP, lsl #32
    // 0x883d6c: r16 = Sentinel
    //     0x883d6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x883d70: cmp             w3, w16
    // 0x883d74: b.eq            #0x883f90
    // 0x883d78: StoreField: r0->field_13 = r3
    //     0x883d78: stur            w3, [x0, #0x13]
    // 0x883d7c: r16 = "\r\n"
    //     0x883d7c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa648] "\r\n"
    //     0x883d80: ldr             x16, [x16, #0x648]
    // 0x883d84: ArrayStore: r0[0] = r16  ; List_4
    //     0x883d84: stur            w16, [x0, #0x17]
    // 0x883d88: str             x0, [SP]
    // 0x883d8c: r0 = _interpolate()
    //     0x883d8c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x883d90: mov             x2, x0
    // 0x883d94: r1 = Instance_Utf8Encoder
    //     0x883d94: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x883d98: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x883d98: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x883d9c: r0 = convert()
    //     0x883d9c: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883da0: ldur            x1, [fp, #-0x20]
    // 0x883da4: mov             x2, x0
    // 0x883da8: r0 = add()
    //     0x883da8: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x883dac: ldur            x0, [fp, #-0x10]
    // 0x883db0: LoadField: r1 = r0->field_f
    //     0x883db0: ldur            w1, [x0, #0xf]
    // 0x883db4: DecompressPointer r1
    //     0x883db4: add             x1, x1, HEAP, lsl #32
    // 0x883db8: ldur            x2, [fp, #-0x48]
    // 0x883dbc: r0 = _headerForFile()
    //     0x883dbc: bl              #0x883288  ; [package:dio/src/form_data.dart] FormData::_headerForFile
    // 0x883dc0: mov             x2, x0
    // 0x883dc4: r1 = Instance_Utf8Encoder
    //     0x883dc4: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x883dc8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x883dc8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x883dcc: r0 = convert()
    //     0x883dcc: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x883dd0: ldur            x1, [fp, #-0x20]
    // 0x883dd4: mov             x2, x0
    // 0x883dd8: r0 = add()
    //     0x883dd8: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x883ddc: ldur            x1, [fp, #-0x48]
    // 0x883de0: LoadField: r0 = r1->field_f
    //     0x883de0: ldur            w0, [x1, #0xf]
    // 0x883de4: DecompressPointer r0
    //     0x883de4: add             x0, x0, HEAP, lsl #32
    // 0x883de8: cmp             w0, NULL
    // 0x883dec: b.eq            #0x883f9c
    // 0x883df0: LoadField: r2 = r0->field_1f
    //     0x883df0: ldur            w2, [x0, #0x1f]
    // 0x883df4: DecompressPointer r2
    //     0x883df4: add             x2, x2, HEAP, lsl #32
    // 0x883df8: tbz             w2, #4, #0x883f40
    // 0x883dfc: r2 = true
    //     0x883dfc: add             x2, NULL, #0x20  ; true
    // 0x883e00: StoreField: r0->field_1f = r2
    //     0x883e00: stur            w2, [x0, #0x1f]
    // 0x883e04: LoadField: r3 = r0->field_1b
    //     0x883e04: ldur            w3, [x0, #0x1b]
    // 0x883e08: DecompressPointer r3
    //     0x883e08: add             x3, x3, HEAP, lsl #32
    // 0x883e0c: str             x3, [SP]
    // 0x883e10: mov             x0, x3
    // 0x883e14: ClosureCall
    //     0x883e14: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x883e18: ldur            x2, [x0, #0x1f]
    //     0x883e1c: blr             x2
    // 0x883e20: r1 = Function '<anonymous closure>':.
    //     0x883e20: add             x1, PP, #0xa, lsl #12  ; [pp+0xa6d0] AnonymousClosure: (0x884010), of [package:dio/src/multipart_file.dart] MultipartFile
    //     0x883e24: ldr             x1, [x1, #0x6d0]
    // 0x883e28: r2 = Null
    //     0x883e28: mov             x2, NULL
    // 0x883e2c: stur            x0, [fp, #-0x60]
    // 0x883e30: r0 = AllocateClosure()
    //     0x883e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x883e34: r16 = <Uint8List>
    //     0x883e34: add             x16, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x883e38: ldr             x16, [x16, #0x598]
    // 0x883e3c: ldur            lr, [fp, #-0x60]
    // 0x883e40: stp             lr, x16, [SP, #8]
    // 0x883e44: str             x0, [SP]
    // 0x883e48: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x883e48: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x883e4c: r0 = map()
    //     0x883e4c: bl              #0x6f8ef8  ; [dart:async] Stream::map
    // 0x883e50: r1 = <void?>
    //     0x883e50: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x883e54: stur            x0, [fp, #-0x60]
    // 0x883e58: r0 = _Future()
    //     0x883e58: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x883e5c: stur            x0, [fp, #-0x68]
    // 0x883e60: StoreField: r0->field_b = rZR
    //     0x883e60: stur            xzr, [x0, #0xb]
    // 0x883e64: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x883e64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x883e68: ldr             x0, [x0, #0x778]
    //     0x883e6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x883e70: cmp             w0, w16
    //     0x883e74: b.ne            #0x883e80
    //     0x883e78: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x883e7c: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x883e80: mov             x1, x0
    // 0x883e84: ldur            x0, [fp, #-0x68]
    // 0x883e88: StoreField: r0->field_13 = r1
    //     0x883e88: stur            w1, [x0, #0x13]
    // 0x883e8c: r1 = <void?>
    //     0x883e8c: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x883e90: r0 = _AsyncCompleter()
    //     0x883e90: bl              #0x632658  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x883e94: mov             x1, x0
    // 0x883e98: ldur            x0, [fp, #-0x68]
    // 0x883e9c: stur            x1, [fp, #-0x70]
    // 0x883ea0: StoreField: r1->field_b = r0
    //     0x883ea0: stur            w0, [x1, #0xb]
    // 0x883ea4: r1 = 1
    //     0x883ea4: movz            x1, #0x1
    // 0x883ea8: r0 = AllocateContext()
    //     0x883ea8: bl              #0x16f6108  ; AllocateContextStub
    // 0x883eac: mov             x1, x0
    // 0x883eb0: ldur            x0, [fp, #-0x70]
    // 0x883eb4: StoreField: r1->field_f = r0
    //     0x883eb4: stur            w0, [x1, #0xf]
    // 0x883eb8: mov             x2, x1
    // 0x883ebc: r1 = Function '<anonymous closure>': static.
    //     0x883ebc: add             x1, PP, #0xa, lsl #12  ; [pp+0xa6d8] AnonymousClosure: static (0x883fc4), of [package:dio/src/utils.dart] 
    //     0x883ec0: ldr             x1, [x1, #0x6d8]
    // 0x883ec4: r0 = AllocateClosure()
    //     0x883ec4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x883ec8: mov             x1, x0
    // 0x883ecc: r0 = <Uint8List>
    //     0x883ecc: add             x0, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x883ed0: ldr             x0, [x0, #0x598]
    // 0x883ed4: StoreField: r1->field_b = r0
    //     0x883ed4: stur            w0, [x1, #0xb]
    // 0x883ed8: ldur            x16, [fp, #-0x58]
    // 0x883edc: stp             x1, x16, [SP]
    // 0x883ee0: ldur            x1, [fp, #-0x60]
    // 0x883ee4: ldur            x2, [fp, #-0x18]
    // 0x883ee8: r4 = const [0, 0x4, 0x2, 0x2, onDone, 0x3, onError, 0x2, null]
    //     0x883ee8: ldr             x4, [PP, #0x268]  ; [pp+0x268] List(9) [0, 0x4, 0x2, 0x2, "onDone", 0x3, "onError", 0x2, Null]
    // 0x883eec: r0 = listen()
    //     0x883eec: bl              #0x1639844  ; [dart:async] _ForwardingStream::listen
    // 0x883ef0: ldur            x0, [fp, #-0x68]
    // 0x883ef4: r0 = Await()
    //     0x883ef4: bl              #0x63248c  ; AwaitStub
    // 0x883ef8: r0 = InitLateStaticField(0xd40) // [package:dio/src/form_data.dart] ::_rnU8
    //     0x883ef8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x883efc: ldr             x0, [x0, #0x1a80]
    //     0x883f00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x883f04: cmp             w0, w16
    //     0x883f08: b.ne            #0x883f18
    //     0x883f0c: add             x2, PP, #0xa, lsl #12  ; [pp+0xa6a8] Field <::._rnU8@911426596>: static late final (offset: 0xd40)
    //     0x883f10: ldr             x2, [x2, #0x6a8]
    //     0x883f14: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x883f18: ldur            x1, [fp, #-0x50]
    // 0x883f1c: mov             x2, x0
    // 0x883f20: r0 = add()
    //     0x883f20: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x883f24: ldur            x1, [fp, #-0x40]
    // 0x883f28: ldur            x0, [fp, #-0x10]
    // 0x883f2c: ldur            x4, [fp, #-0x50]
    // 0x883f30: ldur            x3, [fp, #-0x58]
    // 0x883f34: b               #0x883cf4
    // 0x883f38: r0 = Null
    //     0x883f38: mov             x0, NULL
    // 0x883f3c: r0 = ReturnAsyncNotFuture()
    //     0x883f3c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x883f40: r0 = StateError()
    //     0x883f40: bl              #0x622864  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x883f44: mov             x1, x0
    // 0x883f48: r0 = "The MultipartFile has already been finalized. This typically means you are using the same MultipartFile in repeated requests.\nUse MultipartFile.clone() or create a new MultipartFile for further usages."
    //     0x883f48: add             x0, PP, #0xa, lsl #12  ; [pp+0xa6e0] "The MultipartFile has already been finalized. This typically means you are using the same MultipartFile in repeated requests.\nUse MultipartFile.clone() or create a new MultipartFile for further usages."
    //     0x883f4c: ldr             x0, [x0, #0x6e0]
    // 0x883f50: StoreField: r1->field_b = r0
    //     0x883f50: stur            w0, [x1, #0xb]
    // 0x883f54: mov             x0, x1
    // 0x883f58: r0 = Throw()
    //     0x883f58: bl              #0x16f5420  ; ThrowStub
    // 0x883f5c: brk             #0
    // 0x883f60: mov             x0, x5
    // 0x883f64: r0 = ConcurrentModificationError()
    //     0x883f64: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x883f68: mov             x1, x0
    // 0x883f6c: ldur            x0, [fp, #-0x38]
    // 0x883f70: StoreField: r1->field_b = r0
    //     0x883f70: stur            w0, [x1, #0xb]
    // 0x883f74: mov             x0, x1
    // 0x883f78: r0 = Throw()
    //     0x883f78: bl              #0x16f5420  ; ThrowStub
    // 0x883f7c: brk             #0
    // 0x883f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883f80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883f84: b               #0x883c44
    // 0x883f88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x883f88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x883f8c: b               #0x883d08
    // 0x883f90: r9 = _boundary
    //     0x883f90: add             x9, PP, #0xa, lsl #12  ; [pp+0xa560] Field <FormData._boundary@911426596>: late (offset: 0x10)
    //     0x883f94: ldr             x9, [x9, #0x560]
    // 0x883f98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x883f98: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x883f9c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x883f9c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void writeUtf8(dynamic, String) {
    // ** addr: 0x8840f0, size: 0x64
    // 0x8840f0: EnterFrame
    //     0x8840f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8840f4: mov             fp, SP
    // 0x8840f8: AllocStack(0x8)
    //     0x8840f8: sub             SP, SP, #8
    // 0x8840fc: SetupParameters()
    //     0x8840fc: ldr             x0, [fp, #0x18]
    //     0x884100: ldur            w1, [x0, #0x17]
    //     0x884104: add             x1, x1, HEAP, lsl #32
    // 0x884108: CheckStackOverflow
    //     0x884108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88410c: cmp             SP, x16
    //     0x884110: b.ls            #0x88414c
    // 0x884114: LoadField: r0 = r1->field_13
    //     0x884114: ldur            w0, [x1, #0x13]
    // 0x884118: DecompressPointer r0
    //     0x884118: add             x0, x0, HEAP, lsl #32
    // 0x88411c: ldr             x2, [fp, #0x10]
    // 0x884120: stur            x0, [fp, #-8]
    // 0x884124: r1 = Instance_Utf8Encoder
    //     0x884124: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x884128: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x884128: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88412c: r0 = convert()
    //     0x88412c: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x884130: ldur            x1, [fp, #-8]
    // 0x884134: mov             x2, x0
    // 0x884138: r0 = add()
    //     0x884138: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x88413c: r0 = Null
    //     0x88413c: mov             x0, NULL
    // 0x884140: LeaveFrame
    //     0x884140: mov             SP, fp
    //     0x884144: ldp             fp, lr, [SP], #0x10
    // 0x884148: ret
    //     0x884148: ret             
    // 0x88414c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88414c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x884150: b               #0x884114
  }
  [closure] void writeLine(dynamic) {
    // ** addr: 0x884154, size: 0x74
    // 0x884154: EnterFrame
    //     0x884154: stp             fp, lr, [SP, #-0x10]!
    //     0x884158: mov             fp, SP
    // 0x88415c: AllocStack(0x8)
    //     0x88415c: sub             SP, SP, #8
    // 0x884160: SetupParameters()
    //     0x884160: ldr             x0, [fp, #0x10]
    //     0x884164: ldur            w1, [x0, #0x17]
    //     0x884168: add             x1, x1, HEAP, lsl #32
    // 0x88416c: CheckStackOverflow
    //     0x88416c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x884170: cmp             SP, x16
    //     0x884174: b.ls            #0x8841c0
    // 0x884178: LoadField: r0 = r1->field_13
    //     0x884178: ldur            w0, [x1, #0x13]
    // 0x88417c: DecompressPointer r0
    //     0x88417c: add             x0, x0, HEAP, lsl #32
    // 0x884180: stur            x0, [fp, #-8]
    // 0x884184: r0 = InitLateStaticField(0xd40) // [package:dio/src/form_data.dart] ::_rnU8
    //     0x884184: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x884188: ldr             x0, [x0, #0x1a80]
    //     0x88418c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x884190: cmp             w0, w16
    //     0x884194: b.ne            #0x8841a4
    //     0x884198: add             x2, PP, #0xa, lsl #12  ; [pp+0xa6a8] Field <::._rnU8@911426596>: static late final (offset: 0xd40)
    //     0x88419c: ldr             x2, [x2, #0x6a8]
    //     0x8841a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8841a4: ldur            x1, [fp, #-8]
    // 0x8841a8: mov             x2, x0
    // 0x8841ac: r0 = add()
    //     0x8841ac: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x8841b0: r0 = Null
    //     0x8841b0: mov             x0, NULL
    // 0x8841b4: LeaveFrame
    //     0x8841b4: mov             SP, fp
    //     0x8841b8: ldp             fp, lr, [SP], #0x10
    // 0x8841bc: ret
    //     0x8841bc: ret             
    // 0x8841c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8841c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8841c4: b               #0x884178
  }
  _ FormData(/* No info */) {
    // ** addr: 0x8ab990, size: 0xc4
    // 0x8ab990: EnterFrame
    //     0x8ab990: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab994: mov             fp, SP
    // 0x8ab998: AllocStack(0x8)
    //     0x8ab998: sub             SP, SP, #8
    // 0x8ab99c: r2 = Sentinel
    //     0x8ab99c: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ab9a0: r0 = false
    //     0x8ab9a0: add             x0, NULL, #0x30  ; false
    // 0x8ab9a4: mov             x3, x1
    // 0x8ab9a8: stur            x1, [fp, #-8]
    // 0x8ab9ac: CheckStackOverflow
    //     0x8ab9ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab9b0: cmp             SP, x16
    //     0x8ab9b4: b.ls            #0x8aba4c
    // 0x8ab9b8: StoreField: r3->field_f = r2
    //     0x8ab9b8: stur            w2, [x3, #0xf]
    // 0x8ab9bc: StoreField: r3->field_1b = r0
    //     0x8ab9bc: stur            w0, [x3, #0x1b]
    // 0x8ab9c0: r1 = <MapEntry<String, String>>
    //     0x8ab9c0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33220] TypeArguments: <MapEntry<String, String>>
    //     0x8ab9c4: ldr             x1, [x1, #0x220]
    // 0x8ab9c8: r2 = 0
    //     0x8ab9c8: movz            x2, #0
    // 0x8ab9cc: r0 = _GrowableList()
    //     0x8ab9cc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x8ab9d0: ldur            x3, [fp, #-8]
    // 0x8ab9d4: StoreField: r3->field_13 = r0
    //     0x8ab9d4: stur            w0, [x3, #0x13]
    //     0x8ab9d8: ldurb           w16, [x3, #-1]
    //     0x8ab9dc: ldurb           w17, [x0, #-1]
    //     0x8ab9e0: and             x16, x17, x16, lsr #2
    //     0x8ab9e4: tst             x16, HEAP, lsr #32
    //     0x8ab9e8: b.eq            #0x8ab9f0
    //     0x8ab9ec: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8ab9f0: r1 = <MapEntry<String, MultipartFile>>
    //     0x8ab9f0: add             x1, PP, #0x32, lsl #12  ; [pp+0x32d88] TypeArguments: <MapEntry<String, MultipartFile>>
    //     0x8ab9f4: ldr             x1, [x1, #0xd88]
    // 0x8ab9f8: r2 = 0
    //     0x8ab9f8: movz            x2, #0
    // 0x8ab9fc: r0 = _GrowableList()
    //     0x8ab9fc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x8aba00: ldur            x1, [fp, #-8]
    // 0x8aba04: ArrayStore: r1[0] = r0  ; List_4
    //     0x8aba04: stur            w0, [x1, #0x17]
    //     0x8aba08: ldurb           w16, [x1, #-1]
    //     0x8aba0c: ldurb           w17, [x0, #-1]
    //     0x8aba10: and             x16, x17, x16, lsr #2
    //     0x8aba14: tst             x16, HEAP, lsr #32
    //     0x8aba18: b.eq            #0x8aba20
    //     0x8aba1c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8aba20: r0 = "--dio-boundary"
    //     0x8aba20: add             x0, PP, #0x33, lsl #12  ; [pp+0x33228] "--dio-boundary"
    //     0x8aba24: ldr             x0, [x0, #0x228]
    // 0x8aba28: StoreField: r1->field_7 = r0
    //     0x8aba28: stur            w0, [x1, #7]
    // 0x8aba2c: r0 = false
    //     0x8aba2c: add             x0, NULL, #0x30  ; false
    // 0x8aba30: StoreField: r1->field_b = r0
    //     0x8aba30: stur            w0, [x1, #0xb]
    // 0x8aba34: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8aba34: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8aba38: r0 = _init()
    //     0x8aba38: bl              #0x8aba54  ; [package:dio/src/form_data.dart] FormData::_init
    // 0x8aba3c: r0 = Null
    //     0x8aba3c: mov             x0, NULL
    // 0x8aba40: LeaveFrame
    //     0x8aba40: mov             SP, fp
    //     0x8aba44: ldp             fp, lr, [SP], #0x10
    // 0x8aba48: ret
    //     0x8aba48: ret             
    // 0x8aba4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aba4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aba50: b               #0x8ab9b8
  }
  _ _init(/* No info */) {
    // ** addr: 0x8aba54, size: 0x148
    // 0x8aba54: EnterFrame
    //     0x8aba54: stp             fp, lr, [SP, #-0x10]!
    //     0x8aba58: mov             fp, SP
    // 0x8aba5c: AllocStack(0x30)
    //     0x8aba5c: sub             SP, SP, #0x30
    // 0x8aba60: SetupParameters(FormData this /* r1 => r1, fp-0x10 */, {dynamic fromMap = Null /* r0, fp-0x8 */})
    //     0x8aba60: stur            x1, [fp, #-0x10]
    //     0x8aba64: ldur            w0, [x4, #0x13]
    //     0x8aba68: ldur            w2, [x4, #0x1f]
    //     0x8aba6c: add             x2, x2, HEAP, lsl #32
    //     0x8aba70: add             x16, PP, #0x33, lsl #12  ; [pp+0x33238] "fromMap"
    //     0x8aba74: ldr             x16, [x16, #0x238]
    //     0x8aba78: cmp             w2, w16
    //     0x8aba7c: b.ne            #0x8aba98
    //     0x8aba80: ldur            w2, [x4, #0x23]
    //     0x8aba84: add             x2, x2, HEAP, lsl #32
    //     0x8aba88: sub             w3, w0, w2
    //     0x8aba8c: add             x0, fp, w3, sxtw #2
    //     0x8aba90: ldr             x0, [x0, #8]
    //     0x8aba94: b               #0x8aba9c
    //     0x8aba98: mov             x0, NULL
    //     0x8aba9c: stur            x0, [fp, #-8]
    // 0x8abaa0: CheckStackOverflow
    //     0x8abaa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8abaa4: cmp             SP, x16
    //     0x8abaa8: b.ls            #0x8abb94
    // 0x8abaac: r1 = 1
    //     0x8abaac: movz            x1, #0x1
    // 0x8abab0: r0 = AllocateContext()
    //     0x8abab0: bl              #0x16f6108  ; AllocateContextStub
    // 0x8abab4: mov             x3, x0
    // 0x8abab8: ldur            x0, [fp, #-0x10]
    // 0x8ababc: stur            x3, [fp, #-0x20]
    // 0x8abac0: StoreField: r3->field_f = r0
    //     0x8abac0: stur            w0, [x3, #0xf]
    // 0x8abac4: LoadField: r4 = r0->field_7
    //     0x8abac4: ldur            w4, [x0, #7]
    // 0x8abac8: DecompressPointer r4
    //     0x8abac8: add             x4, x4, HEAP, lsl #32
    // 0x8abacc: stur            x4, [fp, #-0x18]
    // 0x8abad0: r1 = Null
    //     0x8abad0: mov             x1, NULL
    // 0x8abad4: r2 = 6
    //     0x8abad4: movz            x2, #0x6
    // 0x8abad8: r0 = AllocateArray()
    //     0x8abad8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8abadc: mov             x1, x0
    // 0x8abae0: ldur            x0, [fp, #-0x18]
    // 0x8abae4: stur            x1, [fp, #-0x28]
    // 0x8abae8: StoreField: r1->field_f = r0
    //     0x8abae8: stur            w0, [x1, #0xf]
    // 0x8abaec: r16 = "-"
    //     0x8abaec: ldr             x16, [PP, #0x31a8]  ; [pp+0x31a8] "-"
    // 0x8abaf0: StoreField: r1->field_13 = r16
    //     0x8abaf0: stur            w16, [x1, #0x13]
    // 0x8abaf4: r0 = _nextRandomId()
    //     0x8abaf4: bl              #0x8abb9c  ; [package:dio/src/form_data.dart] ::_nextRandomId
    // 0x8abaf8: ldur            x1, [fp, #-0x28]
    // 0x8abafc: ArrayStore: r1[2] = r0  ; List_4
    //     0x8abafc: add             x25, x1, #0x17
    //     0x8abb00: str             w0, [x25]
    //     0x8abb04: tbz             w0, #0, #0x8abb20
    //     0x8abb08: ldurb           w16, [x1, #-1]
    //     0x8abb0c: ldurb           w17, [x0, #-1]
    //     0x8abb10: and             x16, x17, x16, lsr #2
    //     0x8abb14: tst             x16, HEAP, lsr #32
    //     0x8abb18: b.eq            #0x8abb20
    //     0x8abb1c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8abb20: ldur            x16, [fp, #-0x28]
    // 0x8abb24: str             x16, [SP]
    // 0x8abb28: r0 = _interpolate()
    //     0x8abb28: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8abb2c: ldur            x1, [fp, #-0x10]
    // 0x8abb30: StoreField: r1->field_f = r0
    //     0x8abb30: stur            w0, [x1, #0xf]
    //     0x8abb34: ldurb           w16, [x1, #-1]
    //     0x8abb38: ldurb           w17, [x0, #-1]
    //     0x8abb3c: and             x16, x17, x16, lsr #2
    //     0x8abb40: tst             x16, HEAP, lsr #32
    //     0x8abb44: b.eq            #0x8abb4c
    //     0x8abb48: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8abb4c: ldur            x0, [fp, #-8]
    // 0x8abb50: cmp             w0, NULL
    // 0x8abb54: b.eq            #0x8abb84
    // 0x8abb58: ldur            x2, [fp, #-0x20]
    // 0x8abb5c: r1 = Function '<anonymous closure>':.
    //     0x8abb5c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33240] AnonymousClosure: (0x8ac080), in [package:dio/src/form_data.dart] FormData::_init (0x8aba54)
    //     0x8abb60: ldr             x1, [x1, #0x240]
    // 0x8abb64: r0 = AllocateClosure()
    //     0x8abb64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8abb68: r16 = false
    //     0x8abb68: add             x16, NULL, #0x30  ; false
    // 0x8abb6c: str             x16, [SP]
    // 0x8abb70: ldur            x1, [fp, #-8]
    // 0x8abb74: mov             x2, x0
    // 0x8abb78: r4 = const [0, 0x3, 0x1, 0x2, encode, 0x2, null]
    //     0x8abb78: add             x4, PP, #0x33, lsl #12  ; [pp+0x33248] List(7) [0, 0x3, 0x1, 0x2, "encode", 0x2, Null]
    //     0x8abb7c: ldr             x4, [x4, #0x248]
    // 0x8abb80: r0 = encodeMap()
    //     0x8abb80: bl              #0x8803cc  ; [package:dio/src/utils.dart] ::encodeMap
    // 0x8abb84: r0 = Null
    //     0x8abb84: mov             x0, NULL
    // 0x8abb88: LeaveFrame
    //     0x8abb88: mov             SP, fp
    //     0x8abb8c: ldp             fp, lr, [SP], #0x10
    // 0x8abb90: ret
    //     0x8abb90: ret             
    // 0x8abb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8abb94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8abb98: b               #0x8abaac
  }
  [closure] Null <anonymous closure>(dynamic, String, Object?) {
    // ** addr: 0x8ac080, size: 0x294
    // 0x8ac080: EnterFrame
    //     0x8ac080: stp             fp, lr, [SP, #-0x10]!
    //     0x8ac084: mov             fp, SP
    // 0x8ac088: AllocStack(0x28)
    //     0x8ac088: sub             SP, SP, #0x28
    // 0x8ac08c: SetupParameters()
    //     0x8ac08c: ldr             x0, [fp, #0x20]
    //     0x8ac090: ldur            w1, [x0, #0x17]
    //     0x8ac094: add             x1, x1, HEAP, lsl #32
    // 0x8ac098: CheckStackOverflow
    //     0x8ac098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ac09c: cmp             SP, x16
    //     0x8ac0a0: b.ls            #0x8ac30c
    // 0x8ac0a4: ldr             x0, [fp, #0x10]
    // 0x8ac0a8: r2 = 60
    //     0x8ac0a8: movz            x2, #0x3c
    // 0x8ac0ac: branchIfSmi(r0, 0x8ac0b8)
    //     0x8ac0ac: tbz             w0, #0, #0x8ac0b8
    // 0x8ac0b0: r2 = LoadClassIdInstr(r0)
    //     0x8ac0b0: ldur            x2, [x0, #-1]
    //     0x8ac0b4: ubfx            x2, x2, #0xc, #0x14
    // 0x8ac0b8: r17 = 4981
    //     0x8ac0b8: movz            x17, #0x1375
    // 0x8ac0bc: cmp             x2, x17
    // 0x8ac0c0: b.ne            #0x8ac1b4
    // 0x8ac0c4: ldr             x2, [fp, #0x18]
    // 0x8ac0c8: LoadField: r3 = r1->field_f
    //     0x8ac0c8: ldur            w3, [x1, #0xf]
    // 0x8ac0cc: DecompressPointer r3
    //     0x8ac0cc: add             x3, x3, HEAP, lsl #32
    // 0x8ac0d0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x8ac0d0: ldur            w4, [x3, #0x17]
    // 0x8ac0d4: DecompressPointer r4
    //     0x8ac0d4: add             x4, x4, HEAP, lsl #32
    // 0x8ac0d8: stur            x4, [fp, #-8]
    // 0x8ac0dc: r1 = <String, MultipartFile>
    //     0x8ac0dc: add             x1, PP, #0x32, lsl #12  ; [pp+0x32d80] TypeArguments: <String, MultipartFile>
    //     0x8ac0e0: ldr             x1, [x1, #0xd80]
    // 0x8ac0e4: r0 = MapEntry()
    //     0x8ac0e4: bl              #0x68c07c  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x8ac0e8: mov             x3, x0
    // 0x8ac0ec: ldr             x2, [fp, #0x18]
    // 0x8ac0f0: stur            x3, [fp, #-0x10]
    // 0x8ac0f4: StoreField: r3->field_b = r2
    //     0x8ac0f4: stur            w2, [x3, #0xb]
    // 0x8ac0f8: ldr             x0, [fp, #0x10]
    // 0x8ac0fc: StoreField: r3->field_f = r0
    //     0x8ac0fc: stur            w0, [x3, #0xf]
    // 0x8ac100: ldur            x4, [fp, #-8]
    // 0x8ac104: LoadField: r2 = r4->field_7
    //     0x8ac104: ldur            w2, [x4, #7]
    // 0x8ac108: DecompressPointer r2
    //     0x8ac108: add             x2, x2, HEAP, lsl #32
    // 0x8ac10c: mov             x0, x3
    // 0x8ac110: r1 = Null
    //     0x8ac110: mov             x1, NULL
    // 0x8ac114: cmp             w2, NULL
    // 0x8ac118: b.eq            #0x8ac138
    // 0x8ac11c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8ac11c: ldur            w4, [x2, #0x17]
    // 0x8ac120: DecompressPointer r4
    //     0x8ac120: add             x4, x4, HEAP, lsl #32
    // 0x8ac124: r8 = X0
    //     0x8ac124: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8ac128: LoadField: r9 = r4->field_7
    //     0x8ac128: ldur            x9, [x4, #7]
    // 0x8ac12c: r3 = Null
    //     0x8ac12c: add             x3, PP, #0x33, lsl #12  ; [pp+0x33250] Null
    //     0x8ac130: ldr             x3, [x3, #0x250]
    // 0x8ac134: blr             x9
    // 0x8ac138: ldur            x0, [fp, #-8]
    // 0x8ac13c: LoadField: r1 = r0->field_b
    //     0x8ac13c: ldur            w1, [x0, #0xb]
    // 0x8ac140: LoadField: r2 = r0->field_f
    //     0x8ac140: ldur            w2, [x0, #0xf]
    // 0x8ac144: DecompressPointer r2
    //     0x8ac144: add             x2, x2, HEAP, lsl #32
    // 0x8ac148: LoadField: r3 = r2->field_b
    //     0x8ac148: ldur            w3, [x2, #0xb]
    // 0x8ac14c: r2 = LoadInt32Instr(r1)
    //     0x8ac14c: sbfx            x2, x1, #1, #0x1f
    // 0x8ac150: stur            x2, [fp, #-0x18]
    // 0x8ac154: r1 = LoadInt32Instr(r3)
    //     0x8ac154: sbfx            x1, x3, #1, #0x1f
    // 0x8ac158: cmp             x2, x1
    // 0x8ac15c: b.ne            #0x8ac168
    // 0x8ac160: mov             x1, x0
    // 0x8ac164: r0 = _growToNextCapacity()
    //     0x8ac164: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8ac168: ldur            x0, [fp, #-8]
    // 0x8ac16c: ldur            x2, [fp, #-0x18]
    // 0x8ac170: add             x1, x2, #1
    // 0x8ac174: lsl             x3, x1, #1
    // 0x8ac178: StoreField: r0->field_b = r3
    //     0x8ac178: stur            w3, [x0, #0xb]
    // 0x8ac17c: LoadField: r1 = r0->field_f
    //     0x8ac17c: ldur            w1, [x0, #0xf]
    // 0x8ac180: DecompressPointer r1
    //     0x8ac180: add             x1, x1, HEAP, lsl #32
    // 0x8ac184: ldur            x0, [fp, #-0x10]
    // 0x8ac188: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8ac188: add             x25, x1, x2, lsl #2
    //     0x8ac18c: add             x25, x25, #0xf
    //     0x8ac190: str             w0, [x25]
    //     0x8ac194: tbz             w0, #0, #0x8ac1b0
    //     0x8ac198: ldurb           w16, [x1, #-1]
    //     0x8ac19c: ldurb           w17, [x0, #-1]
    //     0x8ac1a0: and             x16, x17, x16, lsr #2
    //     0x8ac1a4: tst             x16, HEAP, lsr #32
    //     0x8ac1a8: b.eq            #0x8ac1b0
    //     0x8ac1ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8ac1b0: b               #0x8ac2fc
    // 0x8ac1b4: ldr             x2, [fp, #0x18]
    // 0x8ac1b8: LoadField: r3 = r1->field_f
    //     0x8ac1b8: ldur            w3, [x1, #0xf]
    // 0x8ac1bc: DecompressPointer r3
    //     0x8ac1bc: add             x3, x3, HEAP, lsl #32
    // 0x8ac1c0: LoadField: r1 = r3->field_13
    //     0x8ac1c0: ldur            w1, [x3, #0x13]
    // 0x8ac1c4: DecompressPointer r1
    //     0x8ac1c4: add             x1, x1, HEAP, lsl #32
    // 0x8ac1c8: stur            x1, [fp, #-8]
    // 0x8ac1cc: cmp             w0, NULL
    // 0x8ac1d0: b.ne            #0x8ac1dc
    // 0x8ac1d4: r0 = Null
    //     0x8ac1d4: mov             x0, NULL
    // 0x8ac1d8: b               #0x8ac208
    // 0x8ac1dc: r3 = 60
    //     0x8ac1dc: movz            x3, #0x3c
    // 0x8ac1e0: branchIfSmi(r0, 0x8ac1ec)
    //     0x8ac1e0: tbz             w0, #0, #0x8ac1ec
    // 0x8ac1e4: r3 = LoadClassIdInstr(r0)
    //     0x8ac1e4: ldur            x3, [x0, #-1]
    //     0x8ac1e8: ubfx            x3, x3, #0xc, #0x14
    // 0x8ac1ec: str             x0, [SP]
    // 0x8ac1f0: mov             x0, x3
    // 0x8ac1f4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x8ac1f4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x8ac1f8: r0 = GDT[cid_x0 + 0x2700]()
    //     0x8ac1f8: movz            x17, #0x2700
    //     0x8ac1fc: add             lr, x0, x17
    //     0x8ac200: ldr             lr, [x21, lr, lsl #3]
    //     0x8ac204: blr             lr
    // 0x8ac208: cmp             w0, NULL
    // 0x8ac20c: b.ne            #0x8ac218
    // 0x8ac210: r3 = ""
    //     0x8ac210: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8ac214: b               #0x8ac21c
    // 0x8ac218: mov             x3, x0
    // 0x8ac21c: ldr             x0, [fp, #0x18]
    // 0x8ac220: ldur            x2, [fp, #-8]
    // 0x8ac224: stur            x3, [fp, #-0x10]
    // 0x8ac228: r1 = <String, String>
    //     0x8ac228: add             x1, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x8ac22c: ldr             x1, [x1, #0x788]
    // 0x8ac230: r0 = MapEntry()
    //     0x8ac230: bl              #0x68c07c  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x8ac234: mov             x3, x0
    // 0x8ac238: ldr             x0, [fp, #0x18]
    // 0x8ac23c: stur            x3, [fp, #-0x20]
    // 0x8ac240: StoreField: r3->field_b = r0
    //     0x8ac240: stur            w0, [x3, #0xb]
    // 0x8ac244: ldur            x0, [fp, #-0x10]
    // 0x8ac248: StoreField: r3->field_f = r0
    //     0x8ac248: stur            w0, [x3, #0xf]
    // 0x8ac24c: ldur            x4, [fp, #-8]
    // 0x8ac250: LoadField: r2 = r4->field_7
    //     0x8ac250: ldur            w2, [x4, #7]
    // 0x8ac254: DecompressPointer r2
    //     0x8ac254: add             x2, x2, HEAP, lsl #32
    // 0x8ac258: mov             x0, x3
    // 0x8ac25c: r1 = Null
    //     0x8ac25c: mov             x1, NULL
    // 0x8ac260: cmp             w2, NULL
    // 0x8ac264: b.eq            #0x8ac284
    // 0x8ac268: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8ac268: ldur            w4, [x2, #0x17]
    // 0x8ac26c: DecompressPointer r4
    //     0x8ac26c: add             x4, x4, HEAP, lsl #32
    // 0x8ac270: r8 = X0
    //     0x8ac270: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8ac274: LoadField: r9 = r4->field_7
    //     0x8ac274: ldur            x9, [x4, #7]
    // 0x8ac278: r3 = Null
    //     0x8ac278: add             x3, PP, #0x33, lsl #12  ; [pp+0x33260] Null
    //     0x8ac27c: ldr             x3, [x3, #0x260]
    // 0x8ac280: blr             x9
    // 0x8ac284: ldur            x0, [fp, #-8]
    // 0x8ac288: LoadField: r1 = r0->field_b
    //     0x8ac288: ldur            w1, [x0, #0xb]
    // 0x8ac28c: LoadField: r2 = r0->field_f
    //     0x8ac28c: ldur            w2, [x0, #0xf]
    // 0x8ac290: DecompressPointer r2
    //     0x8ac290: add             x2, x2, HEAP, lsl #32
    // 0x8ac294: LoadField: r3 = r2->field_b
    //     0x8ac294: ldur            w3, [x2, #0xb]
    // 0x8ac298: r2 = LoadInt32Instr(r1)
    //     0x8ac298: sbfx            x2, x1, #1, #0x1f
    // 0x8ac29c: stur            x2, [fp, #-0x18]
    // 0x8ac2a0: r1 = LoadInt32Instr(r3)
    //     0x8ac2a0: sbfx            x1, x3, #1, #0x1f
    // 0x8ac2a4: cmp             x2, x1
    // 0x8ac2a8: b.ne            #0x8ac2b4
    // 0x8ac2ac: mov             x1, x0
    // 0x8ac2b0: r0 = _growToNextCapacity()
    //     0x8ac2b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x8ac2b4: ldur            x2, [fp, #-8]
    // 0x8ac2b8: ldur            x3, [fp, #-0x18]
    // 0x8ac2bc: add             x4, x3, #1
    // 0x8ac2c0: lsl             x5, x4, #1
    // 0x8ac2c4: StoreField: r2->field_b = r5
    //     0x8ac2c4: stur            w5, [x2, #0xb]
    // 0x8ac2c8: LoadField: r1 = r2->field_f
    //     0x8ac2c8: ldur            w1, [x2, #0xf]
    // 0x8ac2cc: DecompressPointer r1
    //     0x8ac2cc: add             x1, x1, HEAP, lsl #32
    // 0x8ac2d0: ldur            x0, [fp, #-0x20]
    // 0x8ac2d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x8ac2d4: add             x25, x1, x3, lsl #2
    //     0x8ac2d8: add             x25, x25, #0xf
    //     0x8ac2dc: str             w0, [x25]
    //     0x8ac2e0: tbz             w0, #0, #0x8ac2fc
    //     0x8ac2e4: ldurb           w16, [x1, #-1]
    //     0x8ac2e8: ldurb           w17, [x0, #-1]
    //     0x8ac2ec: and             x16, x17, x16, lsr #2
    //     0x8ac2f0: tst             x16, HEAP, lsr #32
    //     0x8ac2f4: b.eq            #0x8ac2fc
    //     0x8ac2f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8ac2fc: r0 = Null
    //     0x8ac2fc: mov             x0, NULL
    // 0x8ac300: LeaveFrame
    //     0x8ac300: mov             SP, fp
    //     0x8ac304: ldp             fp, lr, [SP], #0x10
    // 0x8ac308: ret
    //     0x8ac308: ret             
    // 0x8ac30c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ac30c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ac310: b               #0x8ac0a4
  }
  _ FormData.fromMap(/* No info */) {
    // ** addr: 0x12c9830, size: 0xd8
    // 0x12c9830: EnterFrame
    //     0x12c9830: stp             fp, lr, [SP, #-0x10]!
    //     0x12c9834: mov             fp, SP
    // 0x12c9838: AllocStack(0x18)
    //     0x12c9838: sub             SP, SP, #0x18
    // 0x12c983c: r3 = Sentinel
    //     0x12c983c: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x12c9840: r0 = false
    //     0x12c9840: add             x0, NULL, #0x30  ; false
    // 0x12c9844: mov             x5, x1
    // 0x12c9848: mov             x4, x2
    // 0x12c984c: stur            x1, [fp, #-8]
    // 0x12c9850: stur            x2, [fp, #-0x10]
    // 0x12c9854: CheckStackOverflow
    //     0x12c9854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c9858: cmp             SP, x16
    //     0x12c985c: b.ls            #0x12c9900
    // 0x12c9860: StoreField: r5->field_f = r3
    //     0x12c9860: stur            w3, [x5, #0xf]
    // 0x12c9864: StoreField: r5->field_1b = r0
    //     0x12c9864: stur            w0, [x5, #0x1b]
    // 0x12c9868: r1 = <MapEntry<String, String>>
    //     0x12c9868: add             x1, PP, #0x33, lsl #12  ; [pp+0x33220] TypeArguments: <MapEntry<String, String>>
    //     0x12c986c: ldr             x1, [x1, #0x220]
    // 0x12c9870: r2 = 0
    //     0x12c9870: movz            x2, #0
    // 0x12c9874: r0 = _GrowableList()
    //     0x12c9874: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x12c9878: ldur            x3, [fp, #-8]
    // 0x12c987c: StoreField: r3->field_13 = r0
    //     0x12c987c: stur            w0, [x3, #0x13]
    //     0x12c9880: ldurb           w16, [x3, #-1]
    //     0x12c9884: ldurb           w17, [x0, #-1]
    //     0x12c9888: and             x16, x17, x16, lsr #2
    //     0x12c988c: tst             x16, HEAP, lsr #32
    //     0x12c9890: b.eq            #0x12c9898
    //     0x12c9894: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x12c9898: r1 = <MapEntry<String, MultipartFile>>
    //     0x12c9898: add             x1, PP, #0x32, lsl #12  ; [pp+0x32d88] TypeArguments: <MapEntry<String, MultipartFile>>
    //     0x12c989c: ldr             x1, [x1, #0xd88]
    // 0x12c98a0: r2 = 0
    //     0x12c98a0: movz            x2, #0
    // 0x12c98a4: r0 = _GrowableList()
    //     0x12c98a4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x12c98a8: ldur            x1, [fp, #-8]
    // 0x12c98ac: ArrayStore: r1[0] = r0  ; List_4
    //     0x12c98ac: stur            w0, [x1, #0x17]
    //     0x12c98b0: ldurb           w16, [x1, #-1]
    //     0x12c98b4: ldurb           w17, [x0, #-1]
    //     0x12c98b8: and             x16, x17, x16, lsr #2
    //     0x12c98bc: tst             x16, HEAP, lsr #32
    //     0x12c98c0: b.eq            #0x12c98c8
    //     0x12c98c4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x12c98c8: r0 = false
    //     0x12c98c8: add             x0, NULL, #0x30  ; false
    // 0x12c98cc: StoreField: r1->field_b = r0
    //     0x12c98cc: stur            w0, [x1, #0xb]
    // 0x12c98d0: r0 = "--dio-boundary"
    //     0x12c98d0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33228] "--dio-boundary"
    //     0x12c98d4: ldr             x0, [x0, #0x228]
    // 0x12c98d8: StoreField: r1->field_7 = r0
    //     0x12c98d8: stur            w0, [x1, #7]
    // 0x12c98dc: ldur            x16, [fp, #-0x10]
    // 0x12c98e0: str             x16, [SP]
    // 0x12c98e4: r4 = const [0, 0x2, 0x1, 0x1, fromMap, 0x1, null]
    //     0x12c98e4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33230] List(7) [0, 0x2, 0x1, 0x1, "fromMap", 0x1, Null]
    //     0x12c98e8: ldr             x4, [x4, #0x230]
    // 0x12c98ec: r0 = _init()
    //     0x12c98ec: bl              #0x8aba54  ; [package:dio/src/form_data.dart] FormData::_init
    // 0x12c98f0: r0 = Null
    //     0x12c98f0: mov             x0, NULL
    // 0x12c98f4: LeaveFrame
    //     0x12c98f4: mov             SP, fp
    //     0x12c98f8: ldp             fp, lr, [SP], #0x10
    // 0x12c98fc: ret
    //     0x12c98fc: ret             
    // 0x12c9900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c9900: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c9904: b               #0x12c9860
  }
}
