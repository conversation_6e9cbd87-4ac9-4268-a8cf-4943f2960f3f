// lib: , url: package:customer_app/app/presentation/views/line/orders/orders_view.dart

// class id: 1049538, size: 0x8
class :: {
}

// class id: 4530, size: 0x14, field offset: 0x14
//   const constructor, 
class OrdersView extends BaseView<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x8a3de0, size: 0x50
    // 0x8a3de0: EnterFrame
    //     0x8a3de0: stp             fp, lr, [SP, #-0x10]!
    //     0x8a3de4: mov             fp, SP
    // 0x8a3de8: ldr             x0, [fp, #0x10]
    // 0x8a3dec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8a3dec: ldur            w1, [x0, #0x17]
    // 0x8a3df0: DecompressPointer r1
    //     0x8a3df0: add             x1, x1, HEAP, lsl #32
    // 0x8a3df4: CheckStackOverflow
    //     0x8a3df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a3df8: cmp             SP, x16
    //     0x8a3dfc: b.ls            #0x8a3e28
    // 0x8a3e00: LoadField: r0 = r1->field_f
    //     0x8a3e00: ldur            w0, [x1, #0xf]
    // 0x8a3e04: DecompressPointer r0
    //     0x8a3e04: add             x0, x0, HEAP, lsl #32
    // 0x8a3e08: mov             x1, x0
    // 0x8a3e0c: r0 = controller()
    //     0x8a3e0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8a3e10: mov             x1, x0
    // 0x8a3e14: r0 = onLoadNextPage()
    //     0x8a3e14: bl              #0x8b57cc  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::onLoadNextPage
    // 0x8a3e18: r0 = Null
    //     0x8a3e18: mov             x0, NULL
    // 0x8a3e1c: LeaveFrame
    //     0x8a3e1c: mov             SP, fp
    //     0x8a3e20: ldp             fp, lr, [SP], #0x10
    // 0x8a3e24: ret
    //     0x8a3e24: ret             
    // 0x8a3e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a3e28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a3e2c: b               #0x8a3e00
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x8a3e30, size: 0x3cc
    // 0x8a3e30: EnterFrame
    //     0x8a3e30: stp             fp, lr, [SP, #-0x10]!
    //     0x8a3e34: mov             fp, SP
    // 0x8a3e38: AllocStack(0x50)
    //     0x8a3e38: sub             SP, SP, #0x50
    // 0x8a3e3c: SetupParameters()
    //     0x8a3e3c: ldr             x0, [fp, #0x10]
    //     0x8a3e40: ldur            w2, [x0, #0x17]
    //     0x8a3e44: add             x2, x2, HEAP, lsl #32
    //     0x8a3e48: stur            x2, [fp, #-8]
    // 0x8a3e4c: CheckStackOverflow
    //     0x8a3e4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a3e50: cmp             SP, x16
    //     0x8a3e54: b.ls            #0x8a41f4
    // 0x8a3e58: LoadField: r1 = r2->field_f
    //     0x8a3e58: ldur            w1, [x2, #0xf]
    // 0x8a3e5c: DecompressPointer r1
    //     0x8a3e5c: add             x1, x1, HEAP, lsl #32
    // 0x8a3e60: r0 = controller()
    //     0x8a3e60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8a3e64: LoadField: r1 = r0->field_53
    //     0x8a3e64: ldur            w1, [x0, #0x53]
    // 0x8a3e68: DecompressPointer r1
    //     0x8a3e68: add             x1, x1, HEAP, lsl #32
    // 0x8a3e6c: r0 = value()
    //     0x8a3e6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8a3e70: LoadField: r1 = r0->field_b
    //     0x8a3e70: ldur            w1, [x0, #0xb]
    // 0x8a3e74: DecompressPointer r1
    //     0x8a3e74: add             x1, x1, HEAP, lsl #32
    // 0x8a3e78: cmp             w1, NULL
    // 0x8a3e7c: b.eq            #0x8a3ea8
    // 0x8a3e80: ldur            x2, [fp, #-8]
    // 0x8a3e84: LoadField: r1 = r2->field_f
    //     0x8a3e84: ldur            w1, [x2, #0xf]
    // 0x8a3e88: DecompressPointer r1
    //     0x8a3e88: add             x1, x1, HEAP, lsl #32
    // 0x8a3e8c: r0 = controller()
    //     0x8a3e8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8a3e90: LoadField: r1 = r0->field_63
    //     0x8a3e90: ldur            w1, [x0, #0x63]
    // 0x8a3e94: DecompressPointer r1
    //     0x8a3e94: add             x1, x1, HEAP, lsl #32
    // 0x8a3e98: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8a3e98: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8a3e9c: r0 = toList()
    //     0x8a3e9c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x8a3ea0: LoadField: r1 = r0->field_b
    //     0x8a3ea0: ldur            w1, [x0, #0xb]
    // 0x8a3ea4: cbnz            w1, #0x8a3f1c
    // 0x8a3ea8: ldur            x2, [fp, #-8]
    // 0x8a3eac: LoadField: r1 = r2->field_f
    //     0x8a3eac: ldur            w1, [x2, #0xf]
    // 0x8a3eb0: DecompressPointer r1
    //     0x8a3eb0: add             x1, x1, HEAP, lsl #32
    // 0x8a3eb4: r0 = controller()
    //     0x8a3eb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8a3eb8: mov             x1, x0
    // 0x8a3ebc: r0 = headerConfigData()
    //     0x8a3ebc: bl              #0x8a3724  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData
    // 0x8a3ec0: LoadField: r1 = r0->field_b
    //     0x8a3ec0: ldur            w1, [x0, #0xb]
    // 0x8a3ec4: DecompressPointer r1
    //     0x8a3ec4: add             x1, x1, HEAP, lsl #32
    // 0x8a3ec8: cmp             w1, NULL
    // 0x8a3ecc: b.ne            #0x8a3eec
    // 0x8a3ed0: r0 = Container()
    //     0x8a3ed0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x8a3ed4: mov             x1, x0
    // 0x8a3ed8: stur            x0, [fp, #-0x10]
    // 0x8a3edc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8a3edc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8a3ee0: r0 = Container()
    //     0x8a3ee0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x8a3ee4: ldur            x0, [fp, #-0x10]
    // 0x8a3ee8: b               #0x8a3ef4
    // 0x8a3eec: r0 = Instance_EmptyBagWidget
    //     0x8a3eec: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d38] Obj!EmptyBagWidget@d66d71
    //     0x8a3ef0: ldr             x0, [x0, #0xd38]
    // 0x8a3ef4: stur            x0, [fp, #-0x10]
    // 0x8a3ef8: r0 = Center()
    //     0x8a3ef8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x8a3efc: mov             x1, x0
    // 0x8a3f00: r0 = Instance_Alignment
    //     0x8a3f00: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x8a3f04: ldr             x0, [x0, #0xb10]
    // 0x8a3f08: StoreField: r1->field_f = r0
    //     0x8a3f08: stur            w0, [x1, #0xf]
    // 0x8a3f0c: ldur            x0, [fp, #-0x10]
    // 0x8a3f10: StoreField: r1->field_b = r0
    //     0x8a3f10: stur            w0, [x1, #0xb]
    // 0x8a3f14: mov             x0, x1
    // 0x8a3f18: b               #0x8a41e8
    // 0x8a3f1c: ldur            x2, [fp, #-8]
    // 0x8a3f20: LoadField: r1 = r2->field_13
    //     0x8a3f20: ldur            w1, [x2, #0x13]
    // 0x8a3f24: DecompressPointer r1
    //     0x8a3f24: add             x1, x1, HEAP, lsl #32
    // 0x8a3f28: r0 = of()
    //     0x8a3f28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x8a3f2c: LoadField: r1 = r0->field_87
    //     0x8a3f2c: ldur            w1, [x0, #0x87]
    // 0x8a3f30: DecompressPointer r1
    //     0x8a3f30: add             x1, x1, HEAP, lsl #32
    // 0x8a3f34: LoadField: r0 = r1->field_7
    //     0x8a3f34: ldur            w0, [x1, #7]
    // 0x8a3f38: DecompressPointer r0
    //     0x8a3f38: add             x0, x0, HEAP, lsl #32
    // 0x8a3f3c: stur            x0, [fp, #-0x10]
    // 0x8a3f40: r1 = Instance_Color
    //     0x8a3f40: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8a3f44: d0 = 0.700000
    //     0x8a3f44: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x8a3f48: ldr             d0, [x17, #0xf48]
    // 0x8a3f4c: r0 = withOpacity()
    //     0x8a3f4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8a3f50: r16 = 14.000000
    //     0x8a3f50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x8a3f54: ldr             x16, [x16, #0x1d8]
    // 0x8a3f58: stp             x16, x0, [SP]
    // 0x8a3f5c: ldur            x1, [fp, #-0x10]
    // 0x8a3f60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x8a3f60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x8a3f64: ldr             x4, [x4, #0x9b8]
    // 0x8a3f68: r0 = copyWith()
    //     0x8a3f68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8a3f6c: stur            x0, [fp, #-0x10]
    // 0x8a3f70: r0 = Text()
    //     0x8a3f70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x8a3f74: mov             x3, x0
    // 0x8a3f78: r0 = "My Orders"
    //     0x8a3f78: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d40] "My Orders"
    //     0x8a3f7c: ldr             x0, [x0, #0xd40]
    // 0x8a3f80: stur            x3, [fp, #-0x18]
    // 0x8a3f84: StoreField: r3->field_b = r0
    //     0x8a3f84: stur            w0, [x3, #0xb]
    // 0x8a3f88: ldur            x0, [fp, #-0x10]
    // 0x8a3f8c: StoreField: r3->field_13 = r0
    //     0x8a3f8c: stur            w0, [x3, #0x13]
    // 0x8a3f90: r1 = Null
    //     0x8a3f90: mov             x1, NULL
    // 0x8a3f94: r2 = 2
    //     0x8a3f94: movz            x2, #0x2
    // 0x8a3f98: r0 = AllocateArray()
    //     0x8a3f98: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8a3f9c: mov             x2, x0
    // 0x8a3fa0: ldur            x0, [fp, #-0x18]
    // 0x8a3fa4: stur            x2, [fp, #-0x10]
    // 0x8a3fa8: StoreField: r2->field_f = r0
    //     0x8a3fa8: stur            w0, [x2, #0xf]
    // 0x8a3fac: r1 = <Widget>
    //     0x8a3fac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8a3fb0: r0 = AllocateGrowableArray()
    //     0x8a3fb0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8a3fb4: mov             x1, x0
    // 0x8a3fb8: ldur            x0, [fp, #-0x10]
    // 0x8a3fbc: stur            x1, [fp, #-0x18]
    // 0x8a3fc0: StoreField: r1->field_f = r0
    //     0x8a3fc0: stur            w0, [x1, #0xf]
    // 0x8a3fc4: r0 = 2
    //     0x8a3fc4: movz            x0, #0x2
    // 0x8a3fc8: StoreField: r1->field_b = r0
    //     0x8a3fc8: stur            w0, [x1, #0xb]
    // 0x8a3fcc: r0 = Column()
    //     0x8a3fcc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x8a3fd0: mov             x1, x0
    // 0x8a3fd4: r0 = Instance_Axis
    //     0x8a3fd4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x8a3fd8: stur            x1, [fp, #-0x10]
    // 0x8a3fdc: StoreField: r1->field_f = r0
    //     0x8a3fdc: stur            w0, [x1, #0xf]
    // 0x8a3fe0: r2 = Instance_MainAxisAlignment
    //     0x8a3fe0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x8a3fe4: ldr             x2, [x2, #0xa08]
    // 0x8a3fe8: StoreField: r1->field_13 = r2
    //     0x8a3fe8: stur            w2, [x1, #0x13]
    // 0x8a3fec: r3 = Instance_MainAxisSize
    //     0x8a3fec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x8a3ff0: ldr             x3, [x3, #0xa10]
    // 0x8a3ff4: ArrayStore: r1[0] = r3  ; List_4
    //     0x8a3ff4: stur            w3, [x1, #0x17]
    // 0x8a3ff8: r4 = Instance_CrossAxisAlignment
    //     0x8a3ff8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x8a3ffc: ldr             x4, [x4, #0x890]
    // 0x8a4000: StoreField: r1->field_1b = r4
    //     0x8a4000: stur            w4, [x1, #0x1b]
    // 0x8a4004: r4 = Instance_VerticalDirection
    //     0x8a4004: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x8a4008: ldr             x4, [x4, #0xa20]
    // 0x8a400c: StoreField: r1->field_23 = r4
    //     0x8a400c: stur            w4, [x1, #0x23]
    // 0x8a4010: r5 = Instance_Clip
    //     0x8a4010: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8a4014: ldr             x5, [x5, #0x38]
    // 0x8a4018: StoreField: r1->field_2b = r5
    //     0x8a4018: stur            w5, [x1, #0x2b]
    // 0x8a401c: StoreField: r1->field_2f = rZR
    //     0x8a401c: stur            xzr, [x1, #0x2f]
    // 0x8a4020: ldur            x6, [fp, #-0x18]
    // 0x8a4024: StoreField: r1->field_b = r6
    //     0x8a4024: stur            w6, [x1, #0xb]
    // 0x8a4028: r0 = Align()
    //     0x8a4028: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x8a402c: mov             x2, x0
    // 0x8a4030: r0 = Instance_Alignment
    //     0x8a4030: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0x8a4034: ldr             x0, [x0, #0xf98]
    // 0x8a4038: stur            x2, [fp, #-0x18]
    // 0x8a403c: StoreField: r2->field_f = r0
    //     0x8a403c: stur            w0, [x2, #0xf]
    // 0x8a4040: ldur            x0, [fp, #-0x10]
    // 0x8a4044: StoreField: r2->field_b = r0
    //     0x8a4044: stur            w0, [x2, #0xb]
    // 0x8a4048: ldur            x0, [fp, #-8]
    // 0x8a404c: LoadField: r1 = r0->field_f
    //     0x8a404c: ldur            w1, [x0, #0xf]
    // 0x8a4050: DecompressPointer r1
    //     0x8a4050: add             x1, x1, HEAP, lsl #32
    // 0x8a4054: r0 = controller()
    //     0x8a4054: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8a4058: mov             x1, x0
    // 0x8a405c: r0 = widgetsList()
    //     0x8a405c: bl              #0x860358  ; [package:customer_app/app/presentation/controllers/home/<USER>
    // 0x8a4060: LoadField: r1 = r0->field_b
    //     0x8a4060: ldur            w1, [x0, #0xb]
    // 0x8a4064: r3 = LoadInt32Instr(r1)
    //     0x8a4064: sbfx            x3, x1, #1, #0x1f
    // 0x8a4068: ldur            x2, [fp, #-8]
    // 0x8a406c: stur            x3, [fp, #-0x20]
    // 0x8a4070: r1 = Function '<anonymous closure>':.
    //     0x8a4070: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d48] AnonymousClosure: (0x8aa0cc), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8a4074: ldr             x1, [x1, #0xd48]
    // 0x8a4078: r0 = AllocateClosure()
    //     0x8a4078: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8a407c: r1 = Function '<anonymous closure>':.
    //     0x8a407c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d50] AnonymousClosure: (0x8aa0c0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8a4080: ldr             x1, [x1, #0xd50]
    // 0x8a4084: r2 = Null
    //     0x8a4084: mov             x2, NULL
    // 0x8a4088: stur            x0, [fp, #-0x10]
    // 0x8a408c: r0 = AllocateClosure()
    //     0x8a408c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8a4090: stur            x0, [fp, #-0x28]
    // 0x8a4094: r0 = ListView()
    //     0x8a4094: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x8a4098: stur            x0, [fp, #-0x30]
    // 0x8a409c: r16 = Instance_Axis
    //     0x8a409c: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x8a40a0: r30 = true
    //     0x8a40a0: add             lr, NULL, #0x20  ; true
    // 0x8a40a4: stp             lr, x16, [SP, #0x10]
    // 0x8a40a8: r16 = Instance_ClampingScrollPhysics
    //     0x8a40a8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d58] Obj!ClampingScrollPhysics@d558d1
    //     0x8a40ac: ldr             x16, [x16, #0xd58]
    // 0x8a40b0: r30 = true
    //     0x8a40b0: add             lr, NULL, #0x20  ; true
    // 0x8a40b4: stp             lr, x16, [SP]
    // 0x8a40b8: mov             x1, x0
    // 0x8a40bc: ldur            x2, [fp, #-0x10]
    // 0x8a40c0: ldur            x3, [fp, #-0x20]
    // 0x8a40c4: ldur            x5, [fp, #-0x28]
    // 0x8a40c8: r4 = const [0, 0x8, 0x4, 0x4, physics, 0x6, primary, 0x7, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0x8a40c8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36d60] List(13) [0, 0x8, 0x4, 0x4, "physics", 0x6, "primary", 0x7, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0x8a40cc: ldr             x4, [x4, #0xd60]
    // 0x8a40d0: r0 = ListView.separated()
    //     0x8a40d0: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x8a40d4: r1 = Null
    //     0x8a40d4: mov             x1, NULL
    // 0x8a40d8: r2 = 6
    //     0x8a40d8: movz            x2, #0x6
    // 0x8a40dc: r0 = AllocateArray()
    //     0x8a40dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8a40e0: mov             x2, x0
    // 0x8a40e4: ldur            x0, [fp, #-0x18]
    // 0x8a40e8: stur            x2, [fp, #-0x10]
    // 0x8a40ec: StoreField: r2->field_f = r0
    //     0x8a40ec: stur            w0, [x2, #0xf]
    // 0x8a40f0: r16 = Instance_SizedBox
    //     0x8a40f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x8a40f4: ldr             x16, [x16, #0xd68]
    // 0x8a40f8: StoreField: r2->field_13 = r16
    //     0x8a40f8: stur            w16, [x2, #0x13]
    // 0x8a40fc: ldur            x0, [fp, #-0x30]
    // 0x8a4100: ArrayStore: r2[0] = r0  ; List_4
    //     0x8a4100: stur            w0, [x2, #0x17]
    // 0x8a4104: r1 = <Widget>
    //     0x8a4104: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8a4108: r0 = AllocateGrowableArray()
    //     0x8a4108: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8a410c: mov             x1, x0
    // 0x8a4110: ldur            x0, [fp, #-0x10]
    // 0x8a4114: stur            x1, [fp, #-0x18]
    // 0x8a4118: StoreField: r1->field_f = r0
    //     0x8a4118: stur            w0, [x1, #0xf]
    // 0x8a411c: r0 = 6
    //     0x8a411c: movz            x0, #0x6
    // 0x8a4120: StoreField: r1->field_b = r0
    //     0x8a4120: stur            w0, [x1, #0xb]
    // 0x8a4124: r0 = Column()
    //     0x8a4124: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x8a4128: mov             x1, x0
    // 0x8a412c: r0 = Instance_Axis
    //     0x8a412c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x8a4130: stur            x1, [fp, #-0x10]
    // 0x8a4134: StoreField: r1->field_f = r0
    //     0x8a4134: stur            w0, [x1, #0xf]
    // 0x8a4138: r0 = Instance_MainAxisAlignment
    //     0x8a4138: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x8a413c: ldr             x0, [x0, #0xa08]
    // 0x8a4140: StoreField: r1->field_13 = r0
    //     0x8a4140: stur            w0, [x1, #0x13]
    // 0x8a4144: r0 = Instance_MainAxisSize
    //     0x8a4144: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x8a4148: ldr             x0, [x0, #0xa10]
    // 0x8a414c: ArrayStore: r1[0] = r0  ; List_4
    //     0x8a414c: stur            w0, [x1, #0x17]
    // 0x8a4150: r0 = Instance_CrossAxisAlignment
    //     0x8a4150: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x8a4154: ldr             x0, [x0, #0xa18]
    // 0x8a4158: StoreField: r1->field_1b = r0
    //     0x8a4158: stur            w0, [x1, #0x1b]
    // 0x8a415c: r0 = Instance_VerticalDirection
    //     0x8a415c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x8a4160: ldr             x0, [x0, #0xa20]
    // 0x8a4164: StoreField: r1->field_23 = r0
    //     0x8a4164: stur            w0, [x1, #0x23]
    // 0x8a4168: r0 = Instance_Clip
    //     0x8a4168: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8a416c: ldr             x0, [x0, #0x38]
    // 0x8a4170: StoreField: r1->field_2b = r0
    //     0x8a4170: stur            w0, [x1, #0x2b]
    // 0x8a4174: StoreField: r1->field_2f = rZR
    //     0x8a4174: stur            xzr, [x1, #0x2f]
    // 0x8a4178: ldur            x0, [fp, #-0x18]
    // 0x8a417c: StoreField: r1->field_b = r0
    //     0x8a417c: stur            w0, [x1, #0xb]
    // 0x8a4180: r0 = Padding()
    //     0x8a4180: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x8a4184: mov             x3, x0
    // 0x8a4188: r0 = Instance_EdgeInsets
    //     0x8a4188: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d70] Obj!EdgeInsets@d56e11
    //     0x8a418c: ldr             x0, [x0, #0xd70]
    // 0x8a4190: stur            x3, [fp, #-0x18]
    // 0x8a4194: StoreField: r3->field_f = r0
    //     0x8a4194: stur            w0, [x3, #0xf]
    // 0x8a4198: ldur            x0, [fp, #-0x10]
    // 0x8a419c: StoreField: r3->field_b = r0
    //     0x8a419c: stur            w0, [x3, #0xb]
    // 0x8a41a0: ldur            x2, [fp, #-8]
    // 0x8a41a4: r1 = Function '<anonymous closure>':.
    //     0x8a41a4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d78] AnonymousClosure: (0x8a41fc), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8a41a8: ldr             x1, [x1, #0xd78]
    // 0x8a41ac: r0 = AllocateClosure()
    //     0x8a41ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8a41b0: ldur            x2, [fp, #-8]
    // 0x8a41b4: r1 = Function '<anonymous closure>':.
    //     0x8a41b4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d80] AnonymousClosure: (0x8a3de0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8a41b8: ldr             x1, [x1, #0xd80]
    // 0x8a41bc: stur            x0, [fp, #-8]
    // 0x8a41c0: r0 = AllocateClosure()
    //     0x8a41c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8a41c4: stur            x0, [fp, #-0x10]
    // 0x8a41c8: r0 = PagingView()
    //     0x8a41c8: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x8a41cc: mov             x1, x0
    // 0x8a41d0: ldur            x2, [fp, #-0x18]
    // 0x8a41d4: ldur            x3, [fp, #-0x10]
    // 0x8a41d8: ldur            x5, [fp, #-8]
    // 0x8a41dc: stur            x0, [fp, #-8]
    // 0x8a41e0: r0 = PagingView()
    //     0x8a41e0: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x8a41e4: ldur            x0, [fp, #-8]
    // 0x8a41e8: LeaveFrame
    //     0x8a41e8: mov             SP, fp
    //     0x8a41ec: ldp             fp, lr, [SP], #0x10
    // 0x8a41f0: ret
    //     0x8a41f0: ret             
    // 0x8a41f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a41f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a41f8: b               #0x8a3e58
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8a41fc, size: 0x64
    // 0x8a41fc: EnterFrame
    //     0x8a41fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8a4200: mov             fp, SP
    // 0x8a4204: AllocStack(0x10)
    //     0x8a4204: sub             SP, SP, #0x10
    // 0x8a4208: SetupParameters(OrdersView this /* r1 */)
    //     0x8a4208: stur            NULL, [fp, #-8]
    //     0x8a420c: movz            x0, #0
    //     0x8a4210: add             x1, fp, w0, sxtw #2
    //     0x8a4214: ldr             x1, [x1, #0x10]
    //     0x8a4218: ldur            w2, [x1, #0x17]
    //     0x8a421c: add             x2, x2, HEAP, lsl #32
    //     0x8a4220: stur            x2, [fp, #-0x10]
    // 0x8a4224: CheckStackOverflow
    //     0x8a4224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8a4228: cmp             SP, x16
    //     0x8a422c: b.ls            #0x8a4258
    // 0x8a4230: InitAsync() -> Future<void?>
    //     0x8a4230: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x8a4234: bl              #0x6326e0  ; InitAsyncStub
    // 0x8a4238: ldur            x0, [fp, #-0x10]
    // 0x8a423c: LoadField: r1 = r0->field_f
    //     0x8a423c: ldur            w1, [x0, #0xf]
    // 0x8a4240: DecompressPointer r1
    //     0x8a4240: add             x1, x1, HEAP, lsl #32
    // 0x8a4244: r0 = controller()
    //     0x8a4244: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8a4248: mov             x1, x0
    // 0x8a424c: r0 = onRefreshPage()
    //     0x8a424c: bl              #0x8a4260  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::onRefreshPage
    // 0x8a4250: r0 = Null
    //     0x8a4250: mov             x0, NULL
    // 0x8a4254: r0 = ReturnAsyncNotFuture()
    //     0x8a4254: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x8a4258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8a4258: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8a425c: b               #0x8a4230
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x8aa0c0, size: 0xc
    // 0x8aa0c0: r0 = Instance_Padding
    //     0x8aa0c0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d90] Obj!Padding@d683a1
    //     0x8aa0c4: ldr             x0, [x0, #0xd90]
    // 0x8aa0c8: ret
    //     0x8aa0c8: ret             
  }
  [closure] OrderItemCard <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x8aa0cc, size: 0x1c4
    // 0x8aa0cc: EnterFrame
    //     0x8aa0cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa0d0: mov             fp, SP
    // 0x8aa0d4: AllocStack(0x28)
    //     0x8aa0d4: sub             SP, SP, #0x28
    // 0x8aa0d8: SetupParameters()
    //     0x8aa0d8: ldr             x0, [fp, #0x20]
    //     0x8aa0dc: ldur            w2, [x0, #0x17]
    //     0x8aa0e0: add             x2, x2, HEAP, lsl #32
    //     0x8aa0e4: stur            x2, [fp, #-0x10]
    // 0x8aa0e8: CheckStackOverflow
    //     0x8aa0e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa0ec: cmp             SP, x16
    //     0x8aa0f0: b.ls            #0x8aa284
    // 0x8aa0f4: LoadField: r0 = r2->field_f
    //     0x8aa0f4: ldur            w0, [x2, #0xf]
    // 0x8aa0f8: DecompressPointer r0
    //     0x8aa0f8: add             x0, x0, HEAP, lsl #32
    // 0x8aa0fc: mov             x1, x0
    // 0x8aa100: stur            x0, [fp, #-8]
    // 0x8aa104: r0 = controller()
    //     0x8aa104: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8aa108: LoadField: r1 = r0->field_63
    //     0x8aa108: ldur            w1, [x0, #0x63]
    // 0x8aa10c: DecompressPointer r1
    //     0x8aa10c: add             x1, x1, HEAP, lsl #32
    // 0x8aa110: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8aa110: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8aa114: r0 = toList()
    //     0x8aa114: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x8aa118: mov             x2, x0
    // 0x8aa11c: LoadField: r0 = r2->field_b
    //     0x8aa11c: ldur            w0, [x2, #0xb]
    // 0x8aa120: ldr             x1, [fp, #0x10]
    // 0x8aa124: r3 = LoadInt32Instr(r1)
    //     0x8aa124: sbfx            x3, x1, #1, #0x1f
    //     0x8aa128: tbz             w1, #0, #0x8aa130
    //     0x8aa12c: ldur            x3, [x1, #7]
    // 0x8aa130: r1 = LoadInt32Instr(r0)
    //     0x8aa130: sbfx            x1, x0, #1, #0x1f
    // 0x8aa134: mov             x0, x1
    // 0x8aa138: mov             x1, x3
    // 0x8aa13c: cmp             x1, x0
    // 0x8aa140: b.hs            #0x8aa28c
    // 0x8aa144: LoadField: r0 = r2->field_f
    //     0x8aa144: ldur            w0, [x2, #0xf]
    // 0x8aa148: DecompressPointer r0
    //     0x8aa148: add             x0, x0, HEAP, lsl #32
    // 0x8aa14c: ArrayLoad: r4 = r0[r3]  ; Unknown_4
    //     0x8aa14c: add             x16, x0, x3, lsl #2
    //     0x8aa150: ldur            w4, [x16, #0xf]
    // 0x8aa154: DecompressPointer r4
    //     0x8aa154: add             x4, x4, HEAP, lsl #32
    // 0x8aa158: mov             x0, x4
    // 0x8aa15c: stur            x4, [fp, #-0x18]
    // 0x8aa160: r2 = Null
    //     0x8aa160: mov             x2, NULL
    // 0x8aa164: r1 = Null
    //     0x8aa164: mov             x1, NULL
    // 0x8aa168: r4 = LoadClassIdInstr(r0)
    //     0x8aa168: ldur            x4, [x0, #-1]
    //     0x8aa16c: ubfx            x4, x4, #0xc, #0x14
    // 0x8aa170: r17 = 5137
    //     0x8aa170: movz            x17, #0x1411
    // 0x8aa174: cmp             x4, x17
    // 0x8aa178: b.eq            #0x8aa190
    // 0x8aa17c: r8 = OrderData
    //     0x8aa17c: add             x8, PP, #0x36, lsl #12  ; [pp+0x36d98] Type: OrderData
    //     0x8aa180: ldr             x8, [x8, #0xd98]
    // 0x8aa184: r3 = Null
    //     0x8aa184: add             x3, PP, #0x36, lsl #12  ; [pp+0x36da0] Null
    //     0x8aa188: ldr             x3, [x3, #0xda0]
    // 0x8aa18c: r0 = DefaultTypeTest()
    //     0x8aa18c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x8aa190: ldur            x2, [fp, #-0x10]
    // 0x8aa194: LoadField: r0 = r2->field_f
    //     0x8aa194: ldur            w0, [x2, #0xf]
    // 0x8aa198: DecompressPointer r0
    //     0x8aa198: add             x0, x0, HEAP, lsl #32
    // 0x8aa19c: stur            x0, [fp, #-0x20]
    // 0x8aa1a0: r0 = OrderItemCard()
    //     0x8aa1a0: bl              #0x8aa290  ; AllocateOrderItemCardStub -> OrderItemCard (size=0x2c)
    // 0x8aa1a4: mov             x3, x0
    // 0x8aa1a8: ldur            x0, [fp, #-0x18]
    // 0x8aa1ac: stur            x3, [fp, #-0x28]
    // 0x8aa1b0: StoreField: r3->field_b = r0
    //     0x8aa1b0: stur            w0, [x3, #0xb]
    // 0x8aa1b4: ldur            x2, [fp, #-8]
    // 0x8aa1b8: r1 = Function 'cancelOrder':.
    //     0x8aa1b8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36db0] AnonymousClosure: (0x8b5784), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder (0x8b12cc)
    //     0x8aa1bc: ldr             x1, [x1, #0xdb0]
    // 0x8aa1c0: r0 = AllocateClosure()
    //     0x8aa1c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa1c4: mov             x1, x0
    // 0x8aa1c8: ldur            x0, [fp, #-0x28]
    // 0x8aa1cc: StoreField: r0->field_f = r1
    //     0x8aa1cc: stur            w1, [x0, #0xf]
    // 0x8aa1d0: ldur            x2, [fp, #-8]
    // 0x8aa1d4: r1 = Function 'cancelOrderWithFreeProduct':.
    //     0x8aa1d4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36db8] AnonymousClosure: (0x8b0d08), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x8b0d4c)
    //     0x8aa1d8: ldr             x1, [x1, #0xdb8]
    // 0x8aa1dc: r0 = AllocateClosure()
    //     0x8aa1dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa1e0: mov             x1, x0
    // 0x8aa1e4: ldur            x0, [fp, #-0x28]
    // 0x8aa1e8: StoreField: r0->field_13 = r1
    //     0x8aa1e8: stur            w1, [x0, #0x13]
    // 0x8aa1ec: ldur            x2, [fp, #-8]
    // 0x8aa1f0: r1 = Function 'openProductDetail':.
    //     0x8aa1f0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36dc0] AnonymousClosure: (0x8b0a9c), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::openProductDetail (0x8b0ae8)
    //     0x8aa1f4: ldr             x1, [x1, #0xdc0]
    // 0x8aa1f8: r0 = AllocateClosure()
    //     0x8aa1f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa1fc: mov             x1, x0
    // 0x8aa200: ldur            x0, [fp, #-0x28]
    // 0x8aa204: StoreField: r0->field_1f = r1
    //     0x8aa204: stur            w1, [x0, #0x1f]
    // 0x8aa208: ldur            x2, [fp, #-0x20]
    // 0x8aa20c: r1 = Function 'cancelReturn':.
    //     0x8aa20c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36dc8] AnonymousClosure: (0x8b0614), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelReturn (0x8b0658)
    //     0x8aa210: ldr             x1, [x1, #0xdc8]
    // 0x8aa214: r0 = AllocateClosure()
    //     0x8aa214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa218: mov             x1, x0
    // 0x8aa21c: ldur            x0, [fp, #-0x28]
    // 0x8aa220: ArrayStore: r0[0] = r1  ; List_4
    //     0x8aa220: stur            w1, [x0, #0x17]
    // 0x8aa224: ldur            x2, [fp, #-0x20]
    // 0x8aa228: r1 = Function 'cancelExchange':.
    //     0x8aa228: add             x1, PP, #0x36, lsl #12  ; [pp+0x36dd0] AnonymousClosure: (0x8adcb4), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelExchange (0x8add1c)
    //     0x8aa22c: ldr             x1, [x1, #0xdd0]
    // 0x8aa230: r0 = AllocateClosure()
    //     0x8aa230: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa234: mov             x1, x0
    // 0x8aa238: ldur            x0, [fp, #-0x28]
    // 0x8aa23c: StoreField: r0->field_1b = r1
    //     0x8aa23c: stur            w1, [x0, #0x1b]
    // 0x8aa240: ldur            x2, [fp, #-0x10]
    // 0x8aa244: r1 = Function '<anonymous closure>':.
    //     0x8aa244: add             x1, PP, #0x36, lsl #12  ; [pp+0x36dd8] AnonymousClosure: (0x8adc64), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8aa248: ldr             x1, [x1, #0xdd8]
    // 0x8aa24c: r0 = AllocateClosure()
    //     0x8aa24c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa250: mov             x1, x0
    // 0x8aa254: ldur            x0, [fp, #-0x28]
    // 0x8aa258: StoreField: r0->field_23 = r1
    //     0x8aa258: stur            w1, [x0, #0x23]
    // 0x8aa25c: ldur            x2, [fp, #-0x10]
    // 0x8aa260: r1 = Function '<anonymous closure>':.
    //     0x8aa260: add             x1, PP, #0x36, lsl #12  ; [pp+0x36de0] AnonymousClosure: (0x8aa2bc), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8aa264: ldr             x1, [x1, #0xde0]
    // 0x8aa268: r0 = AllocateClosure()
    //     0x8aa268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa26c: mov             x1, x0
    // 0x8aa270: ldur            x0, [fp, #-0x28]
    // 0x8aa274: StoreField: r0->field_27 = r1
    //     0x8aa274: stur            w1, [x0, #0x27]
    // 0x8aa278: LeaveFrame
    //     0x8aa278: mov             SP, fp
    //     0x8aa27c: ldp             fp, lr, [SP], #0x10
    // 0x8aa280: ret
    //     0x8aa280: ret             
    // 0x8aa284: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa284: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa288: b               #0x8aa0f4
    // 0x8aa28c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8aa28c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, double, Items, String) {
    // ** addr: 0x8aa2bc, size: 0x1d0
    // 0x8aa2bc: EnterFrame
    //     0x8aa2bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8aa2c0: mov             fp, SP
    // 0x8aa2c4: AllocStack(0x28)
    //     0x8aa2c4: sub             SP, SP, #0x28
    // 0x8aa2c8: SetupParameters()
    //     0x8aa2c8: ldr             x0, [fp, #0x28]
    //     0x8aa2cc: ldur            w2, [x0, #0x17]
    //     0x8aa2d0: add             x2, x2, HEAP, lsl #32
    //     0x8aa2d4: stur            x2, [fp, #-8]
    // 0x8aa2d8: CheckStackOverflow
    //     0x8aa2d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8aa2dc: cmp             SP, x16
    //     0x8aa2e0: b.ls            #0x8aa484
    // 0x8aa2e4: LoadField: r1 = r2->field_f
    //     0x8aa2e4: ldur            w1, [x2, #0xf]
    // 0x8aa2e8: DecompressPointer r1
    //     0x8aa2e8: add             x1, x1, HEAP, lsl #32
    // 0x8aa2ec: r0 = controller()
    //     0x8aa2ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8aa2f0: mov             x1, x0
    // 0x8aa2f4: ldr             x0, [fp, #0x10]
    // 0x8aa2f8: StoreField: r1->field_77 = r0
    //     0x8aa2f8: stur            w0, [x1, #0x77]
    //     0x8aa2fc: ldurb           w16, [x1, #-1]
    //     0x8aa300: ldurb           w17, [x0, #-1]
    //     0x8aa304: and             x16, x17, x16, lsr #2
    //     0x8aa308: tst             x16, HEAP, lsr #32
    //     0x8aa30c: b.eq            #0x8aa314
    //     0x8aa310: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8aa314: ldr             x1, [fp, #0x10]
    // 0x8aa318: r0 = LoadClassIdInstr(r1)
    //     0x8aa318: ldur            x0, [x1, #-1]
    //     0x8aa31c: ubfx            x0, x0, #0xc, #0x14
    // 0x8aa320: r16 = "stars"
    //     0x8aa320: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb28] "stars"
    //     0x8aa324: ldr             x16, [x16, #0xb28]
    // 0x8aa328: stp             x16, x1, [SP]
    // 0x8aa32c: mov             lr, x0
    // 0x8aa330: ldr             lr, [x21, lr, lsl #3]
    // 0x8aa334: blr             lr
    // 0x8aa338: tbz             w0, #4, #0x8aa400
    // 0x8aa33c: ldr             x1, [fp, #0x18]
    // 0x8aa340: ldr             x0, [fp, #0x10]
    // 0x8aa344: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8aa344: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8aa348: ldr             x0, [x0, #0x1c80]
    //     0x8aa34c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8aa350: cmp             w0, w16
    //     0x8aa354: b.ne            #0x8aa360
    //     0x8aa358: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8aa35c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8aa360: r1 = Null
    //     0x8aa360: mov             x1, NULL
    // 0x8aa364: r2 = 8
    //     0x8aa364: movz            x2, #0x8
    // 0x8aa368: r0 = AllocateArray()
    //     0x8aa368: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8aa36c: r16 = "order_id"
    //     0x8aa36c: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x8aa370: ldr             x16, [x16, #0xa38]
    // 0x8aa374: StoreField: r0->field_f = r16
    //     0x8aa374: stur            w16, [x0, #0xf]
    // 0x8aa378: ldr             x2, [fp, #0x18]
    // 0x8aa37c: LoadField: r1 = r2->field_7
    //     0x8aa37c: ldur            w1, [x2, #7]
    // 0x8aa380: DecompressPointer r1
    //     0x8aa380: add             x1, x1, HEAP, lsl #32
    // 0x8aa384: StoreField: r0->field_13 = r1
    //     0x8aa384: stur            w1, [x0, #0x13]
    // 0x8aa388: r16 = "coming_from"
    //     0x8aa388: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x8aa38c: ldr             x16, [x16, #0x328]
    // 0x8aa390: ArrayStore: r0[0] = r16  ; List_4
    //     0x8aa390: stur            w16, [x0, #0x17]
    // 0x8aa394: ldr             x1, [fp, #0x10]
    // 0x8aa398: StoreField: r0->field_1b = r1
    //     0x8aa398: stur            w1, [x0, #0x1b]
    // 0x8aa39c: r16 = <String, String?>
    //     0x8aa39c: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x8aa3a0: ldr             x16, [x16, #0x3c8]
    // 0x8aa3a4: stp             x0, x16, [SP]
    // 0x8aa3a8: r0 = Map._fromLiteral()
    //     0x8aa3a8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8aa3ac: r16 = "/rating_review_for_order"
    //     0x8aa3ac: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9b8] "/rating_review_for_order"
    //     0x8aa3b0: ldr             x16, [x16, #0x9b8]
    // 0x8aa3b4: stp             x16, NULL, [SP, #8]
    // 0x8aa3b8: str             x0, [SP]
    // 0x8aa3bc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x8aa3bc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x8aa3c0: ldr             x4, [x4, #0x438]
    // 0x8aa3c4: r0 = GetNavigation.toNamed()
    //     0x8aa3c4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x8aa3c8: stur            x0, [fp, #-0x10]
    // 0x8aa3cc: cmp             w0, NULL
    // 0x8aa3d0: b.eq            #0x8aa474
    // 0x8aa3d4: ldur            x2, [fp, #-8]
    // 0x8aa3d8: r1 = Function '<anonymous closure>':.
    //     0x8aa3d8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36de8] AnonymousClosure: (0x8adbf4), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8aa3dc: ldr             x1, [x1, #0xde8]
    // 0x8aa3e0: r0 = AllocateClosure()
    //     0x8aa3e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8aa3e4: r16 = <Null?>
    //     0x8aa3e4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x8aa3e8: ldur            lr, [fp, #-0x10]
    // 0x8aa3ec: stp             lr, x16, [SP, #8]
    // 0x8aa3f0: str             x0, [SP]
    // 0x8aa3f4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8aa3f4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8aa3f8: r0 = then()
    //     0x8aa3f8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x8aa3fc: b               #0x8aa474
    // 0x8aa400: ldr             x3, [fp, #0x20]
    // 0x8aa404: ldr             x2, [fp, #0x18]
    // 0x8aa408: ldur            x0, [fp, #-8]
    // 0x8aa40c: LoadField: r1 = r0->field_f
    //     0x8aa40c: ldur            w1, [x0, #0xf]
    // 0x8aa410: DecompressPointer r1
    //     0x8aa410: add             x1, x1, HEAP, lsl #32
    // 0x8aa414: r0 = controller()
    //     0x8aa414: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8aa418: mov             x1, x0
    // 0x8aa41c: ldr             x0, [fp, #0x18]
    // 0x8aa420: StoreField: r1->field_8b = r0
    //     0x8aa420: stur            w0, [x1, #0x8b]
    //     0x8aa424: ldurb           w16, [x1, #-1]
    //     0x8aa428: ldurb           w17, [x0, #-1]
    //     0x8aa42c: and             x16, x17, x16, lsr #2
    //     0x8aa430: tst             x16, HEAP, lsr #32
    //     0x8aa434: b.eq            #0x8aa43c
    //     0x8aa438: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8aa43c: ldur            x0, [fp, #-8]
    // 0x8aa440: LoadField: r1 = r0->field_f
    //     0x8aa440: ldur            w1, [x0, #0xf]
    // 0x8aa444: DecompressPointer r1
    //     0x8aa444: add             x1, x1, HEAP, lsl #32
    // 0x8aa448: r0 = controller()
    //     0x8aa448: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8aa44c: mov             x1, x0
    // 0x8aa450: ldr             x0, [fp, #0x20]
    // 0x8aa454: LoadField: d0 = r0->field_7
    //     0x8aa454: ldur            d0, [x0, #7]
    // 0x8aa458: StoreField: r1->field_8f = d0
    //     0x8aa458: stur            d0, [x1, #0x8f]
    // 0x8aa45c: ldur            x0, [fp, #-8]
    // 0x8aa460: LoadField: r1 = r0->field_f
    //     0x8aa460: ldur            w1, [x0, #0xf]
    // 0x8aa464: DecompressPointer r1
    //     0x8aa464: add             x1, x1, HEAP, lsl #32
    // 0x8aa468: r0 = controller()
    //     0x8aa468: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8aa46c: mov             x1, x0
    // 0x8aa470: r0 = createReview()
    //     0x8aa470: bl              #0x8aa48c  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::createReview
    // 0x8aa474: r0 = Null
    //     0x8aa474: mov             x0, NULL
    // 0x8aa478: LeaveFrame
    //     0x8aa478: mov             SP, fp
    //     0x8aa47c: ldp             fp, lr, [SP], #0x10
    // 0x8aa480: ret
    //     0x8aa480: ret             
    // 0x8aa484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8aa484: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8aa488: b               #0x8aa2e4
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x8adbf4, size: 0x70
    // 0x8adbf4: EnterFrame
    //     0x8adbf4: stp             fp, lr, [SP, #-0x10]!
    //     0x8adbf8: mov             fp, SP
    // 0x8adbfc: AllocStack(0x8)
    //     0x8adbfc: sub             SP, SP, #8
    // 0x8adc00: SetupParameters()
    //     0x8adc00: ldr             x0, [fp, #0x18]
    //     0x8adc04: ldur            w2, [x0, #0x17]
    //     0x8adc08: add             x2, x2, HEAP, lsl #32
    //     0x8adc0c: stur            x2, [fp, #-8]
    // 0x8adc10: CheckStackOverflow
    //     0x8adc10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8adc14: cmp             SP, x16
    //     0x8adc18: b.ls            #0x8adc5c
    // 0x8adc1c: LoadField: r1 = r2->field_f
    //     0x8adc1c: ldur            w1, [x2, #0xf]
    // 0x8adc20: DecompressPointer r1
    //     0x8adc20: add             x1, x1, HEAP, lsl #32
    // 0x8adc24: r0 = controller()
    //     0x8adc24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8adc28: LoadField: r1 = r0->field_4f
    //     0x8adc28: ldur            w1, [x0, #0x4f]
    // 0x8adc2c: DecompressPointer r1
    //     0x8adc2c: add             x1, x1, HEAP, lsl #32
    // 0x8adc30: r0 = initRefresh()
    //     0x8adc30: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x8adc34: ldur            x0, [fp, #-8]
    // 0x8adc38: LoadField: r1 = r0->field_f
    //     0x8adc38: ldur            w1, [x0, #0xf]
    // 0x8adc3c: DecompressPointer r1
    //     0x8adc3c: add             x1, x1, HEAP, lsl #32
    // 0x8adc40: r0 = controller()
    //     0x8adc40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8adc44: mov             x1, x0
    // 0x8adc48: r0 = getOrders()
    //     0x8adc48: bl              #0x8a5968  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getOrders
    // 0x8adc4c: r0 = Null
    //     0x8adc4c: mov             x0, NULL
    // 0x8adc50: LeaveFrame
    //     0x8adc50: mov             SP, fp
    //     0x8adc54: ldp             fp, lr, [SP], #0x10
    // 0x8adc58: ret
    //     0x8adc58: ret             
    // 0x8adc5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8adc5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8adc60: b               #0x8adc1c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x8adc64, size: 0x50
    // 0x8adc64: EnterFrame
    //     0x8adc64: stp             fp, lr, [SP, #-0x10]!
    //     0x8adc68: mov             fp, SP
    // 0x8adc6c: ldr             x0, [fp, #0x10]
    // 0x8adc70: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8adc70: ldur            w1, [x0, #0x17]
    // 0x8adc74: DecompressPointer r1
    //     0x8adc74: add             x1, x1, HEAP, lsl #32
    // 0x8adc78: CheckStackOverflow
    //     0x8adc78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8adc7c: cmp             SP, x16
    //     0x8adc80: b.ls            #0x8adcac
    // 0x8adc84: LoadField: r0 = r1->field_f
    //     0x8adc84: ldur            w0, [x1, #0xf]
    // 0x8adc88: DecompressPointer r0
    //     0x8adc88: add             x0, x0, HEAP, lsl #32
    // 0x8adc8c: mov             x1, x0
    // 0x8adc90: r0 = controller()
    //     0x8adc90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8adc94: mov             x1, x0
    // 0x8adc98: r0 = onRefreshPage()
    //     0x8adc98: bl              #0x8a4260  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::onRefreshPage
    // 0x8adc9c: r0 = Null
    //     0x8adc9c: mov             x0, NULL
    // 0x8adca0: LeaveFrame
    //     0x8adca0: mov             SP, fp
    //     0x8adca4: ldp             fp, lr, [SP], #0x10
    // 0x8adca8: ret
    //     0x8adca8: ret             
    // 0x8adcac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8adcac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8adcb0: b               #0x8adc84
  }
  [closure] void cancelExchange(dynamic, BuildContext, CancelExchangeRequestWidget, String, bool, bool, bool, String, String, String) {
    // ** addr: 0x8adcb4, size: 0x68
    // 0x8adcb4: EnterFrame
    //     0x8adcb4: stp             fp, lr, [SP, #-0x10]!
    //     0x8adcb8: mov             fp, SP
    // 0x8adcbc: AllocStack(0x20)
    //     0x8adcbc: sub             SP, SP, #0x20
    // 0x8adcc0: SetupParameters()
    //     0x8adcc0: ldr             x0, [fp, #0x58]
    //     0x8adcc4: ldur            w1, [x0, #0x17]
    //     0x8adcc8: add             x1, x1, HEAP, lsl #32
    // 0x8adccc: CheckStackOverflow
    //     0x8adccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8adcd0: cmp             SP, x16
    //     0x8adcd4: b.ls            #0x8add14
    // 0x8adcd8: ldr             x16, [fp, #0x28]
    // 0x8adcdc: ldr             lr, [fp, #0x20]
    // 0x8adce0: stp             lr, x16, [SP, #0x10]
    // 0x8adce4: ldr             x16, [fp, #0x18]
    // 0x8adce8: ldr             lr, [fp, #0x10]
    // 0x8adcec: stp             lr, x16, [SP]
    // 0x8adcf0: ldr             x2, [fp, #0x50]
    // 0x8adcf4: ldr             x3, [fp, #0x48]
    // 0x8adcf8: ldr             x5, [fp, #0x40]
    // 0x8adcfc: ldr             x6, [fp, #0x38]
    // 0x8add00: ldr             x7, [fp, #0x30]
    // 0x8add04: r0 = cancelExchange()
    //     0x8add04: bl              #0x8add1c  ; [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelExchange
    // 0x8add08: LeaveFrame
    //     0x8add08: mov             SP, fp
    //     0x8add0c: ldp             fp, lr, [SP], #0x10
    // 0x8add10: ret
    //     0x8add10: ret             
    // 0x8add14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8add14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8add18: b               #0x8adcd8
  }
  _ cancelExchange(/* No info */) {
    // ** addr: 0x8add1c, size: 0xe4
    // 0x8add1c: EnterFrame
    //     0x8add1c: stp             fp, lr, [SP, #-0x10]!
    //     0x8add20: mov             fp, SP
    // 0x8add24: AllocStack(0x60)
    //     0x8add24: sub             SP, SP, #0x60
    // 0x8add28: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r7, fp-0x28 */, dynamic _ /* r7 => r6, fp-0x30 */)
    //     0x8add28: stur            x6, [fp, #-0x28]
    //     0x8add2c: mov             x16, x7
    //     0x8add30: mov             x7, x6
    //     0x8add34: mov             x6, x16
    //     0x8add38: stur            x1, [fp, #-8]
    //     0x8add3c: stur            x2, [fp, #-0x10]
    //     0x8add40: stur            x3, [fp, #-0x18]
    //     0x8add44: stur            x5, [fp, #-0x20]
    //     0x8add48: stur            x6, [fp, #-0x30]
    // 0x8add4c: CheckStackOverflow
    //     0x8add4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8add50: cmp             SP, x16
    //     0x8add54: b.ls            #0x8addf8
    // 0x8add58: r1 = 3
    //     0x8add58: movz            x1, #0x3
    // 0x8add5c: r0 = AllocateContext()
    //     0x8add5c: bl              #0x16f6108  ; AllocateContextStub
    // 0x8add60: ldur            x1, [fp, #-8]
    // 0x8add64: stur            x0, [fp, #-0x38]
    // 0x8add68: StoreField: r0->field_f = r1
    //     0x8add68: stur            w1, [x0, #0xf]
    // 0x8add6c: ldur            x2, [fp, #-0x18]
    // 0x8add70: StoreField: r0->field_13 = r2
    //     0x8add70: stur            w2, [x0, #0x13]
    // 0x8add74: ldur            x2, [fp, #-0x20]
    // 0x8add78: ArrayStore: r0[0] = r2  ; List_4
    //     0x8add78: stur            w2, [x0, #0x17]
    // 0x8add7c: r0 = controller()
    //     0x8add7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8add80: mov             x1, x0
    // 0x8add84: ldur            x0, [fp, #-0x38]
    // 0x8add88: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8add88: ldur            w2, [x0, #0x17]
    // 0x8add8c: DecompressPointer r2
    //     0x8add8c: add             x2, x2, HEAP, lsl #32
    // 0x8add90: ldr             x16, [fp, #0x20]
    // 0x8add94: stp             x2, x16, [SP]
    // 0x8add98: ldr             x2, [fp, #0x18]
    // 0x8add9c: ldr             x3, [fp, #0x10]
    // 0x8adda0: ldr             x5, [fp, #0x28]
    // 0x8adda4: ldur            x6, [fp, #-0x30]
    // 0x8adda8: ldur            x7, [fp, #-0x28]
    // 0x8addac: r0 = exchangeCancelRequestCTAPostEvent()
    //     0x8addac: bl              #0x8ae250  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::exchangeCancelRequestCTAPostEvent
    // 0x8addb0: ldur            x2, [fp, #-0x38]
    // 0x8addb4: r1 = Function '<anonymous closure>':.
    //     0x8addb4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e10] AnonymousClosure: (0x8afcfc), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelExchange (0x8add1c)
    //     0x8addb8: ldr             x1, [x1, #0xe10]
    // 0x8addbc: r0 = AllocateClosure()
    //     0x8addbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8addc0: stp             x0, NULL, [SP, #0x18]
    // 0x8addc4: ldur            x16, [fp, #-0x10]
    // 0x8addc8: r30 = true
    //     0x8addc8: add             lr, NULL, #0x20  ; true
    // 0x8addcc: stp             lr, x16, [SP, #8]
    // 0x8addd0: r16 = Instance_RoundedRectangleBorder
    //     0x8addd0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x8addd4: ldr             x16, [x16, #0xd68]
    // 0x8addd8: str             x16, [SP]
    // 0x8adddc: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x8adddc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x8adde0: ldr             x4, [x4, #0xb20]
    // 0x8adde4: r0 = showModalBottomSheet()
    //     0x8adde4: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8adde8: r0 = Null
    //     0x8adde8: mov             x0, NULL
    // 0x8addec: LeaveFrame
    //     0x8addec: mov             SP, fp
    //     0x8addf0: ldp             fp, lr, [SP], #0x10
    // 0x8addf4: ret
    //     0x8addf4: ret             
    // 0x8addf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8addf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8addfc: b               #0x8add58
  }
  [closure] CancelExchangeBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8afcfc, size: 0x9c
    // 0x8afcfc: EnterFrame
    //     0x8afcfc: stp             fp, lr, [SP, #-0x10]!
    //     0x8afd00: mov             fp, SP
    // 0x8afd04: AllocStack(0x20)
    //     0x8afd04: sub             SP, SP, #0x20
    // 0x8afd08: SetupParameters()
    //     0x8afd08: ldr             x0, [fp, #0x18]
    //     0x8afd0c: ldur            w1, [x0, #0x17]
    //     0x8afd10: add             x1, x1, HEAP, lsl #32
    // 0x8afd14: CheckStackOverflow
    //     0x8afd14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8afd18: cmp             SP, x16
    //     0x8afd1c: b.ls            #0x8afd90
    // 0x8afd20: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8afd20: ldur            w0, [x1, #0x17]
    // 0x8afd24: DecompressPointer r0
    //     0x8afd24: add             x0, x0, HEAP, lsl #32
    // 0x8afd28: stur            x0, [fp, #-0x10]
    // 0x8afd2c: LoadField: r2 = r1->field_13
    //     0x8afd2c: ldur            w2, [x1, #0x13]
    // 0x8afd30: DecompressPointer r2
    //     0x8afd30: add             x2, x2, HEAP, lsl #32
    // 0x8afd34: stur            x2, [fp, #-8]
    // 0x8afd38: LoadField: r3 = r1->field_f
    //     0x8afd38: ldur            w3, [x1, #0xf]
    // 0x8afd3c: DecompressPointer r3
    //     0x8afd3c: add             x3, x3, HEAP, lsl #32
    // 0x8afd40: mov             x1, x3
    // 0x8afd44: r0 = controller()
    //     0x8afd44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8afd48: stur            x0, [fp, #-0x18]
    // 0x8afd4c: r0 = CancelExchangeBottomSheet()
    //     0x8afd4c: bl              #0x8afd98  ; AllocateCancelExchangeBottomSheetStub -> CancelExchangeBottomSheet (size=0x18)
    // 0x8afd50: mov             x3, x0
    // 0x8afd54: ldur            x0, [fp, #-8]
    // 0x8afd58: stur            x3, [fp, #-0x20]
    // 0x8afd5c: StoreField: r3->field_b = r0
    //     0x8afd5c: stur            w0, [x3, #0xb]
    // 0x8afd60: ldur            x2, [fp, #-0x18]
    // 0x8afd64: r1 = Function 'cancelExchange':.
    //     0x8afd64: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e18] AnonymousClosure: (0x8afdc4), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelExchange (0x8afe00)
    //     0x8afd68: ldr             x1, [x1, #0xe18]
    // 0x8afd6c: r0 = AllocateClosure()
    //     0x8afd6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8afd70: mov             x1, x0
    // 0x8afd74: ldur            x0, [fp, #-0x20]
    // 0x8afd78: StoreField: r0->field_f = r1
    //     0x8afd78: stur            w1, [x0, #0xf]
    // 0x8afd7c: ldur            x1, [fp, #-0x10]
    // 0x8afd80: StoreField: r0->field_13 = r1
    //     0x8afd80: stur            w1, [x0, #0x13]
    // 0x8afd84: LeaveFrame
    //     0x8afd84: mov             SP, fp
    //     0x8afd88: ldp             fp, lr, [SP], #0x10
    // 0x8afd8c: ret
    //     0x8afd8c: ret             
    // 0x8afd90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8afd90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8afd94: b               #0x8afd20
  }
  [closure] void cancelReturn(dynamic, BuildContext, CancelReturnRequestWidget?, String) {
    // ** addr: 0x8b0614, size: 0x44
    // 0x8b0614: EnterFrame
    //     0x8b0614: stp             fp, lr, [SP, #-0x10]!
    //     0x8b0618: mov             fp, SP
    // 0x8b061c: ldr             x0, [fp, #0x28]
    // 0x8b0620: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b0620: ldur            w1, [x0, #0x17]
    // 0x8b0624: DecompressPointer r1
    //     0x8b0624: add             x1, x1, HEAP, lsl #32
    // 0x8b0628: CheckStackOverflow
    //     0x8b0628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b062c: cmp             SP, x16
    //     0x8b0630: b.ls            #0x8b0650
    // 0x8b0634: ldr             x2, [fp, #0x20]
    // 0x8b0638: ldr             x3, [fp, #0x18]
    // 0x8b063c: ldr             x5, [fp, #0x10]
    // 0x8b0640: r0 = cancelReturn()
    //     0x8b0640: bl              #0x8b0658  ; [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelReturn
    // 0x8b0644: LeaveFrame
    //     0x8b0644: mov             SP, fp
    //     0x8b0648: ldp             fp, lr, [SP], #0x10
    // 0x8b064c: ret
    //     0x8b064c: ret             
    // 0x8b0650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b0650: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b0654: b               #0x8b0634
  }
  _ cancelReturn(/* No info */) {
    // ** addr: 0x8b0658, size: 0x9c
    // 0x8b0658: EnterFrame
    //     0x8b0658: stp             fp, lr, [SP, #-0x10]!
    //     0x8b065c: mov             fp, SP
    // 0x8b0660: AllocStack(0x48)
    //     0x8b0660: sub             SP, SP, #0x48
    // 0x8b0664: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x8b0664: stur            x1, [fp, #-8]
    //     0x8b0668: stur            x2, [fp, #-0x10]
    //     0x8b066c: stur            x3, [fp, #-0x18]
    //     0x8b0670: stur            x5, [fp, #-0x20]
    // 0x8b0674: CheckStackOverflow
    //     0x8b0674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0678: cmp             SP, x16
    //     0x8b067c: b.ls            #0x8b06ec
    // 0x8b0680: r1 = 3
    //     0x8b0680: movz            x1, #0x3
    // 0x8b0684: r0 = AllocateContext()
    //     0x8b0684: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b0688: mov             x1, x0
    // 0x8b068c: ldur            x0, [fp, #-8]
    // 0x8b0690: StoreField: r1->field_f = r0
    //     0x8b0690: stur            w0, [x1, #0xf]
    // 0x8b0694: ldur            x0, [fp, #-0x18]
    // 0x8b0698: StoreField: r1->field_13 = r0
    //     0x8b0698: stur            w0, [x1, #0x13]
    // 0x8b069c: ldur            x0, [fp, #-0x20]
    // 0x8b06a0: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b06a0: stur            w0, [x1, #0x17]
    // 0x8b06a4: mov             x2, x1
    // 0x8b06a8: r1 = Function '<anonymous closure>':.
    //     0x8b06a8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e40] AnonymousClosure: (0x8b06f4), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelReturn (0x8b0658)
    //     0x8b06ac: ldr             x1, [x1, #0xe40]
    // 0x8b06b0: r0 = AllocateClosure()
    //     0x8b06b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b06b4: stp             x0, NULL, [SP, #0x18]
    // 0x8b06b8: ldur            x16, [fp, #-0x10]
    // 0x8b06bc: r30 = true
    //     0x8b06bc: add             lr, NULL, #0x20  ; true
    // 0x8b06c0: stp             lr, x16, [SP, #8]
    // 0x8b06c4: r16 = Instance_RoundedRectangleBorder
    //     0x8b06c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x8b06c8: ldr             x16, [x16, #0xd68]
    // 0x8b06cc: str             x16, [SP]
    // 0x8b06d0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x8b06d0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x8b06d4: ldr             x4, [x4, #0xb20]
    // 0x8b06d8: r0 = showModalBottomSheet()
    //     0x8b06d8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b06dc: r0 = Null
    //     0x8b06dc: mov             x0, NULL
    // 0x8b06e0: LeaveFrame
    //     0x8b06e0: mov             SP, fp
    //     0x8b06e4: ldp             fp, lr, [SP], #0x10
    // 0x8b06e8: ret
    //     0x8b06e8: ret             
    // 0x8b06ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b06ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b06f0: b               #0x8b0680
  }
  [closure] CancelReturnOrderBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b06f4, size: 0x9c
    // 0x8b06f4: EnterFrame
    //     0x8b06f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b06f8: mov             fp, SP
    // 0x8b06fc: AllocStack(0x20)
    //     0x8b06fc: sub             SP, SP, #0x20
    // 0x8b0700: SetupParameters()
    //     0x8b0700: ldr             x0, [fp, #0x18]
    //     0x8b0704: ldur            w1, [x0, #0x17]
    //     0x8b0708: add             x1, x1, HEAP, lsl #32
    // 0x8b070c: CheckStackOverflow
    //     0x8b070c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0710: cmp             SP, x16
    //     0x8b0714: b.ls            #0x8b0788
    // 0x8b0718: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8b0718: ldur            w0, [x1, #0x17]
    // 0x8b071c: DecompressPointer r0
    //     0x8b071c: add             x0, x0, HEAP, lsl #32
    // 0x8b0720: stur            x0, [fp, #-0x10]
    // 0x8b0724: LoadField: r2 = r1->field_13
    //     0x8b0724: ldur            w2, [x1, #0x13]
    // 0x8b0728: DecompressPointer r2
    //     0x8b0728: add             x2, x2, HEAP, lsl #32
    // 0x8b072c: stur            x2, [fp, #-8]
    // 0x8b0730: LoadField: r3 = r1->field_f
    //     0x8b0730: ldur            w3, [x1, #0xf]
    // 0x8b0734: DecompressPointer r3
    //     0x8b0734: add             x3, x3, HEAP, lsl #32
    // 0x8b0738: mov             x1, x3
    // 0x8b073c: r0 = controller()
    //     0x8b073c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b0740: stur            x0, [fp, #-0x18]
    // 0x8b0744: r0 = CancelReturnOrderBottomSheet()
    //     0x8b0744: bl              #0x8b0790  ; AllocateCancelReturnOrderBottomSheetStub -> CancelReturnOrderBottomSheet (size=0x18)
    // 0x8b0748: mov             x3, x0
    // 0x8b074c: ldur            x0, [fp, #-8]
    // 0x8b0750: stur            x3, [fp, #-0x20]
    // 0x8b0754: StoreField: r3->field_b = r0
    //     0x8b0754: stur            w0, [x3, #0xb]
    // 0x8b0758: ldur            x2, [fp, #-0x18]
    // 0x8b075c: r1 = Function 'cancelReturn':.
    //     0x8b075c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e48] AnonymousClosure: (0x8b07bc), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelReturn (0x8b07f8)
    //     0x8b0760: ldr             x1, [x1, #0xe48]
    // 0x8b0764: r0 = AllocateClosure()
    //     0x8b0764: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b0768: mov             x1, x0
    // 0x8b076c: ldur            x0, [fp, #-0x20]
    // 0x8b0770: StoreField: r0->field_f = r1
    //     0x8b0770: stur            w1, [x0, #0xf]
    // 0x8b0774: ldur            x1, [fp, #-0x10]
    // 0x8b0778: StoreField: r0->field_13 = r1
    //     0x8b0778: stur            w1, [x0, #0x13]
    // 0x8b077c: LeaveFrame
    //     0x8b077c: mov             SP, fp
    //     0x8b0780: ldp             fp, lr, [SP], #0x10
    // 0x8b0784: ret
    //     0x8b0784: ret             
    // 0x8b0788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b0788: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b078c: b               #0x8b0718
  }
  [closure] void openProductDetail(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x8b0a9c, size: 0x4c
    // 0x8b0a9c: EnterFrame
    //     0x8b0a9c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b0aa0: mov             fp, SP
    // 0x8b0aa4: ldr             x0, [fp, #0x38]
    // 0x8b0aa8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b0aa8: ldur            w1, [x0, #0x17]
    // 0x8b0aac: DecompressPointer r1
    //     0x8b0aac: add             x1, x1, HEAP, lsl #32
    // 0x8b0ab0: CheckStackOverflow
    //     0x8b0ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0ab4: cmp             SP, x16
    //     0x8b0ab8: b.ls            #0x8b0ae0
    // 0x8b0abc: ldr             x2, [fp, #0x30]
    // 0x8b0ac0: ldr             x3, [fp, #0x28]
    // 0x8b0ac4: ldr             x5, [fp, #0x20]
    // 0x8b0ac8: ldr             x6, [fp, #0x18]
    // 0x8b0acc: ldr             x7, [fp, #0x10]
    // 0x8b0ad0: r0 = openProductDetail()
    //     0x8b0ad0: bl              #0x8b0ae8  ; [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::openProductDetail
    // 0x8b0ad4: LeaveFrame
    //     0x8b0ad4: mov             SP, fp
    //     0x8b0ad8: ldp             fp, lr, [SP], #0x10
    // 0x8b0adc: ret
    //     0x8b0adc: ret             
    // 0x8b0ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b0ae0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b0ae4: b               #0x8b0abc
  }
  _ openProductDetail(/* No info */) {
    // ** addr: 0x8b0ae8, size: 0x220
    // 0x8b0ae8: EnterFrame
    //     0x8b0ae8: stp             fp, lr, [SP, #-0x10]!
    //     0x8b0aec: mov             fp, SP
    // 0x8b0af0: AllocStack(0x48)
    //     0x8b0af0: sub             SP, SP, #0x48
    // 0x8b0af4: SetupParameters(OrdersView this /* r1 => r8, fp-0x8 */, dynamic _ /* r2 => r7, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r4, fp-0x28 */, dynamic _ /* r7 => r3, fp-0x30 */)
    //     0x8b0af4: mov             x8, x1
    //     0x8b0af8: mov             x4, x6
    //     0x8b0afc: stur            x6, [fp, #-0x28]
    //     0x8b0b00: mov             x6, x3
    //     0x8b0b04: stur            x3, [fp, #-0x18]
    //     0x8b0b08: mov             x3, x7
    //     0x8b0b0c: stur            x7, [fp, #-0x30]
    //     0x8b0b10: mov             x7, x2
    //     0x8b0b14: stur            x1, [fp, #-8]
    //     0x8b0b18: stur            x2, [fp, #-0x10]
    //     0x8b0b1c: stur            x5, [fp, #-0x20]
    // 0x8b0b20: CheckStackOverflow
    //     0x8b0b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0b24: cmp             SP, x16
    //     0x8b0b28: b.ls            #0x8b0d00
    // 0x8b0b2c: mov             x0, x5
    // 0x8b0b30: r2 = Null
    //     0x8b0b30: mov             x2, NULL
    // 0x8b0b34: r1 = Null
    //     0x8b0b34: mov             x1, NULL
    // 0x8b0b38: r4 = 60
    //     0x8b0b38: movz            x4, #0x3c
    // 0x8b0b3c: branchIfSmi(r0, 0x8b0b48)
    //     0x8b0b3c: tbz             w0, #0, #0x8b0b48
    // 0x8b0b40: r4 = LoadClassIdInstr(r0)
    //     0x8b0b40: ldur            x4, [x0, #-1]
    //     0x8b0b44: ubfx            x4, x4, #0xc, #0x14
    // 0x8b0b48: cmp             x4, #0x3f
    // 0x8b0b4c: b.eq            #0x8b0b60
    // 0x8b0b50: r8 = bool
    //     0x8b0b50: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x8b0b54: r3 = Null
    //     0x8b0b54: add             x3, PP, #0x36, lsl #12  ; [pp+0x36e68] Null
    //     0x8b0b58: ldr             x3, [x3, #0xe68]
    // 0x8b0b5c: r0 = bool()
    //     0x8b0b5c: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x8b0b60: ldur            x0, [fp, #-0x20]
    // 0x8b0b64: tbz             w0, #4, #0x8b0ba8
    // 0x8b0b68: ldur            x3, [fp, #-0x28]
    // 0x8b0b6c: mov             x0, x3
    // 0x8b0b70: r2 = Null
    //     0x8b0b70: mov             x2, NULL
    // 0x8b0b74: r1 = Null
    //     0x8b0b74: mov             x1, NULL
    // 0x8b0b78: r4 = 60
    //     0x8b0b78: movz            x4, #0x3c
    // 0x8b0b7c: branchIfSmi(r0, 0x8b0b88)
    //     0x8b0b7c: tbz             w0, #0, #0x8b0b88
    // 0x8b0b80: r4 = LoadClassIdInstr(r0)
    //     0x8b0b80: ldur            x4, [x0, #-1]
    //     0x8b0b84: ubfx            x4, x4, #0xc, #0x14
    // 0x8b0b88: cmp             x4, #0x3f
    // 0x8b0b8c: b.eq            #0x8b0ba0
    // 0x8b0b90: r8 = bool
    //     0x8b0b90: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x8b0b94: r3 = Null
    //     0x8b0b94: add             x3, PP, #0x36, lsl #12  ; [pp+0x36e78] Null
    //     0x8b0b98: ldr             x3, [x3, #0xe78]
    // 0x8b0b9c: r0 = bool()
    //     0x8b0b9c: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x8b0ba0: ldur            x0, [fp, #-0x28]
    // 0x8b0ba4: tbnz            w0, #4, #0x8b0c48
    // 0x8b0ba8: ldur            x0, [fp, #-0x30]
    // 0x8b0bac: ldur            x1, [fp, #-8]
    // 0x8b0bb0: r0 = controller()
    //     0x8b0bb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b0bb4: mov             x3, x0
    // 0x8b0bb8: ldur            x0, [fp, #-0x30]
    // 0x8b0bbc: r2 = Null
    //     0x8b0bbc: mov             x2, NULL
    // 0x8b0bc0: r1 = Null
    //     0x8b0bc0: mov             x1, NULL
    // 0x8b0bc4: stur            x3, [fp, #-8]
    // 0x8b0bc8: r4 = 60
    //     0x8b0bc8: movz            x4, #0x3c
    // 0x8b0bcc: branchIfSmi(r0, 0x8b0bd8)
    //     0x8b0bcc: tbz             w0, #0, #0x8b0bd8
    // 0x8b0bd0: r4 = LoadClassIdInstr(r0)
    //     0x8b0bd0: ldur            x4, [x0, #-1]
    //     0x8b0bd4: ubfx            x4, x4, #0xc, #0x14
    // 0x8b0bd8: sub             x4, x4, #0x5e
    // 0x8b0bdc: cmp             x4, #1
    // 0x8b0be0: b.ls            #0x8b0bf4
    // 0x8b0be4: r8 = String?
    //     0x8b0be4: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x8b0be8: r3 = Null
    //     0x8b0be8: add             x3, PP, #0x36, lsl #12  ; [pp+0x36e88] Null
    //     0x8b0bec: ldr             x3, [x3, #0xe88]
    // 0x8b0bf0: r0 = String?()
    //     0x8b0bf0: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x8b0bf4: r0 = EventData()
    //     0x8b0bf4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b0bf8: mov             x1, x0
    // 0x8b0bfc: r0 = "home_page"
    //     0x8b0bfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x8b0c00: ldr             x0, [x0, #0xe60]
    // 0x8b0c04: stur            x1, [fp, #-0x20]
    // 0x8b0c08: StoreField: r1->field_13 = r0
    //     0x8b0c08: stur            w0, [x1, #0x13]
    // 0x8b0c0c: ldur            x0, [fp, #-0x30]
    // 0x8b0c10: StoreField: r1->field_77 = r0
    //     0x8b0c10: stur            w0, [x1, #0x77]
    // 0x8b0c14: r0 = "order_list"
    //     0x8b0c14: add             x0, PP, #0x36, lsl #12  ; [pp+0x36e98] "order_list"
    //     0x8b0c18: ldr             x0, [x0, #0xe98]
    // 0x8b0c1c: StoreField: r1->field_87 = r0
    //     0x8b0c1c: stur            w0, [x1, #0x87]
    // 0x8b0c20: r0 = EventsRequest()
    //     0x8b0c20: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b0c24: mov             x1, x0
    // 0x8b0c28: r0 = "return_exchange_order_again"
    //     0x8b0c28: add             x0, PP, #0x36, lsl #12  ; [pp+0x362a0] "return_exchange_order_again"
    //     0x8b0c2c: ldr             x0, [x0, #0x2a0]
    // 0x8b0c30: StoreField: r1->field_7 = r0
    //     0x8b0c30: stur            w0, [x1, #7]
    // 0x8b0c34: ldur            x0, [fp, #-0x20]
    // 0x8b0c38: StoreField: r1->field_b = r0
    //     0x8b0c38: stur            w0, [x1, #0xb]
    // 0x8b0c3c: mov             x2, x1
    // 0x8b0c40: ldur            x1, [fp, #-8]
    // 0x8b0c44: r0 = postEvents()
    //     0x8b0c44: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b0c48: ldur            x1, [fp, #-0x10]
    // 0x8b0c4c: ldur            x0, [fp, #-0x18]
    // 0x8b0c50: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b0c50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b0c54: ldr             x0, [x0, #0x1c80]
    //     0x8b0c58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b0c5c: cmp             w0, w16
    //     0x8b0c60: b.ne            #0x8b0c6c
    //     0x8b0c64: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b0c68: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b0c6c: r1 = Null
    //     0x8b0c6c: mov             x1, NULL
    // 0x8b0c70: r2 = 16
    //     0x8b0c70: movz            x2, #0x10
    // 0x8b0c74: r0 = AllocateArray()
    //     0x8b0c74: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b0c78: r16 = "short_id"
    //     0x8b0c78: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x8b0c7c: ldr             x16, [x16, #0x488]
    // 0x8b0c80: StoreField: r0->field_f = r16
    //     0x8b0c80: stur            w16, [x0, #0xf]
    // 0x8b0c84: ldur            x1, [fp, #-0x10]
    // 0x8b0c88: StoreField: r0->field_13 = r1
    //     0x8b0c88: stur            w1, [x0, #0x13]
    // 0x8b0c8c: r16 = "sku_id"
    //     0x8b0c8c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x8b0c90: ldr             x16, [x16, #0x498]
    // 0x8b0c94: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b0c94: stur            w16, [x0, #0x17]
    // 0x8b0c98: ldur            x1, [fp, #-0x18]
    // 0x8b0c9c: StoreField: r0->field_1b = r1
    //     0x8b0c9c: stur            w1, [x0, #0x1b]
    // 0x8b0ca0: r16 = "screenSource"
    //     0x8b0ca0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x8b0ca4: ldr             x16, [x16, #0x450]
    // 0x8b0ca8: StoreField: r0->field_1f = r16
    //     0x8b0ca8: stur            w16, [x0, #0x1f]
    // 0x8b0cac: StoreField: r0->field_23 = rNULL
    //     0x8b0cac: stur            NULL, [x0, #0x23]
    // 0x8b0cb0: r16 = "previousScreenSource"
    //     0x8b0cb0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x8b0cb4: ldr             x16, [x16, #0x448]
    // 0x8b0cb8: StoreField: r0->field_27 = r16
    //     0x8b0cb8: stur            w16, [x0, #0x27]
    // 0x8b0cbc: r16 = "order_page"
    //     0x8b0cbc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b0cc0: ldr             x16, [x16, #0x710]
    // 0x8b0cc4: StoreField: r0->field_2b = r16
    //     0x8b0cc4: stur            w16, [x0, #0x2b]
    // 0x8b0cc8: r16 = <String, dynamic>
    //     0x8b0cc8: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x8b0ccc: stp             x0, x16, [SP]
    // 0x8b0cd0: r0 = Map._fromLiteral()
    //     0x8b0cd0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8b0cd4: r16 = "/product-detail"
    //     0x8b0cd4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x8b0cd8: ldr             x16, [x16, #0x4a8]
    // 0x8b0cdc: stp             x16, NULL, [SP, #8]
    // 0x8b0ce0: str             x0, [SP]
    // 0x8b0ce4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x8b0ce4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x8b0ce8: ldr             x4, [x4, #0x438]
    // 0x8b0cec: r0 = GetNavigation.toNamed()
    //     0x8b0cec: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x8b0cf0: r0 = Null
    //     0x8b0cf0: mov             x0, NULL
    // 0x8b0cf4: LeaveFrame
    //     0x8b0cf4: mov             SP, fp
    //     0x8b0cf8: ldp             fp, lr, [SP], #0x10
    // 0x8b0cfc: ret
    //     0x8b0cfc: ret             
    // 0x8b0d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b0d00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b0d04: b               #0x8b0b2c
  }
  [closure] void cancelOrderWithFreeProduct(dynamic, Items?, String, BuildContext) {
    // ** addr: 0x8b0d08, size: 0x44
    // 0x8b0d08: EnterFrame
    //     0x8b0d08: stp             fp, lr, [SP, #-0x10]!
    //     0x8b0d0c: mov             fp, SP
    // 0x8b0d10: ldr             x0, [fp, #0x28]
    // 0x8b0d14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b0d14: ldur            w1, [x0, #0x17]
    // 0x8b0d18: DecompressPointer r1
    //     0x8b0d18: add             x1, x1, HEAP, lsl #32
    // 0x8b0d1c: CheckStackOverflow
    //     0x8b0d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0d20: cmp             SP, x16
    //     0x8b0d24: b.ls            #0x8b0d44
    // 0x8b0d28: ldr             x2, [fp, #0x20]
    // 0x8b0d2c: ldr             x3, [fp, #0x18]
    // 0x8b0d30: ldr             x5, [fp, #0x10]
    // 0x8b0d34: r0 = cancelOrderWithFreeProduct()
    //     0x8b0d34: bl              #0x8b0d4c  ; [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct
    // 0x8b0d38: LeaveFrame
    //     0x8b0d38: mov             SP, fp
    //     0x8b0d3c: ldp             fp, lr, [SP], #0x10
    // 0x8b0d40: ret
    //     0x8b0d40: ret             
    // 0x8b0d44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b0d44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b0d48: b               #0x8b0d28
  }
  _ cancelOrderWithFreeProduct(/* No info */) {
    // ** addr: 0x8b0d4c, size: 0x144
    // 0x8b0d4c: EnterFrame
    //     0x8b0d4c: stp             fp, lr, [SP, #-0x10]!
    //     0x8b0d50: mov             fp, SP
    // 0x8b0d54: AllocStack(0x60)
    //     0x8b0d54: sub             SP, SP, #0x60
    // 0x8b0d58: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x8b0d58: stur            x1, [fp, #-8]
    //     0x8b0d5c: stur            x2, [fp, #-0x10]
    //     0x8b0d60: stur            x3, [fp, #-0x18]
    //     0x8b0d64: stur            x5, [fp, #-0x20]
    // 0x8b0d68: CheckStackOverflow
    //     0x8b0d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0d6c: cmp             SP, x16
    //     0x8b0d70: b.ls            #0x8b0e88
    // 0x8b0d74: r1 = 2
    //     0x8b0d74: movz            x1, #0x2
    // 0x8b0d78: r0 = AllocateContext()
    //     0x8b0d78: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b0d7c: mov             x2, x0
    // 0x8b0d80: ldur            x0, [fp, #-8]
    // 0x8b0d84: stur            x2, [fp, #-0x28]
    // 0x8b0d88: StoreField: r2->field_f = r0
    //     0x8b0d88: stur            w0, [x2, #0xf]
    // 0x8b0d8c: ldur            x1, [fp, #-0x10]
    // 0x8b0d90: StoreField: r2->field_13 = r1
    //     0x8b0d90: stur            w1, [x2, #0x13]
    // 0x8b0d94: mov             x1, x0
    // 0x8b0d98: r0 = controller()
    //     0x8b0d98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b0d9c: stur            x0, [fp, #-0x10]
    // 0x8b0da0: r0 = EventData()
    //     0x8b0da0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b0da4: mov             x1, x0
    // 0x8b0da8: r0 = "order_page"
    //     0x8b0da8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b0dac: ldr             x0, [x0, #0x710]
    // 0x8b0db0: stur            x1, [fp, #-0x30]
    // 0x8b0db4: StoreField: r1->field_13 = r0
    //     0x8b0db4: stur            w0, [x1, #0x13]
    // 0x8b0db8: ldur            x2, [fp, #-0x18]
    // 0x8b0dbc: StoreField: r1->field_77 = r2
    //     0x8b0dbc: stur            w2, [x1, #0x77]
    // 0x8b0dc0: r0 = EventsRequest()
    //     0x8b0dc0: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b0dc4: mov             x1, x0
    // 0x8b0dc8: r0 = "cancel_order_clicked"
    //     0x8b0dc8: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e8] "cancel_order_clicked"
    //     0x8b0dcc: ldr             x0, [x0, #0xe8]
    // 0x8b0dd0: StoreField: r1->field_7 = r0
    //     0x8b0dd0: stur            w0, [x1, #7]
    // 0x8b0dd4: ldur            x0, [fp, #-0x30]
    // 0x8b0dd8: StoreField: r1->field_b = r0
    //     0x8b0dd8: stur            w0, [x1, #0xb]
    // 0x8b0ddc: mov             x2, x1
    // 0x8b0de0: ldur            x1, [fp, #-0x10]
    // 0x8b0de4: r0 = postEvents()
    //     0x8b0de4: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b0de8: ldur            x1, [fp, #-8]
    // 0x8b0dec: r0 = controller()
    //     0x8b0dec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b0df0: stur            x0, [fp, #-8]
    // 0x8b0df4: r0 = EventData()
    //     0x8b0df4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b0df8: mov             x1, x0
    // 0x8b0dfc: r0 = "order_page"
    //     0x8b0dfc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b0e00: ldr             x0, [x0, #0x710]
    // 0x8b0e04: stur            x1, [fp, #-0x10]
    // 0x8b0e08: StoreField: r1->field_13 = r0
    //     0x8b0e08: stur            w0, [x1, #0x13]
    // 0x8b0e0c: ldur            x0, [fp, #-0x18]
    // 0x8b0e10: StoreField: r1->field_77 = r0
    //     0x8b0e10: stur            w0, [x1, #0x77]
    // 0x8b0e14: r0 = EventsRequest()
    //     0x8b0e14: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b0e18: mov             x1, x0
    // 0x8b0e1c: r0 = "cancel_free_gift_popup_opened"
    //     0x8b0e1c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ea0] "cancel_free_gift_popup_opened"
    //     0x8b0e20: ldr             x0, [x0, #0xea0]
    // 0x8b0e24: StoreField: r1->field_7 = r0
    //     0x8b0e24: stur            w0, [x1, #7]
    // 0x8b0e28: ldur            x0, [fp, #-0x10]
    // 0x8b0e2c: StoreField: r1->field_b = r0
    //     0x8b0e2c: stur            w0, [x1, #0xb]
    // 0x8b0e30: mov             x2, x1
    // 0x8b0e34: ldur            x1, [fp, #-8]
    // 0x8b0e38: r0 = postEvents()
    //     0x8b0e38: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b0e3c: ldur            x2, [fp, #-0x28]
    // 0x8b0e40: r1 = Function '<anonymous closure>':.
    //     0x8b0e40: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ea8] AnonymousClosure: (0x8b0e90), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x8b0d4c)
    //     0x8b0e44: ldr             x1, [x1, #0xea8]
    // 0x8b0e48: r0 = AllocateClosure()
    //     0x8b0e48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b0e4c: stp             x0, NULL, [SP, #0x20]
    // 0x8b0e50: ldur            x16, [fp, #-0x20]
    // 0x8b0e54: r30 = Instance_Color
    //     0x8b0e54: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x8b0e58: stp             lr, x16, [SP, #0x10]
    // 0x8b0e5c: r16 = Instance_RoundedRectangleBorder
    //     0x8b0e5c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x8b0e60: ldr             x16, [x16, #0xd68]
    // 0x8b0e64: r30 = true
    //     0x8b0e64: add             lr, NULL, #0x20  ; true
    // 0x8b0e68: stp             lr, x16, [SP]
    // 0x8b0e6c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x2, isScrollControlled, 0x4, shape, 0x3, null]
    //     0x8b0e6c: add             x4, PP, #0x34, lsl #12  ; [pp+0x345f0] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x4, "shape", 0x3, Null]
    //     0x8b0e70: ldr             x4, [x4, #0x5f0]
    // 0x8b0e74: r0 = showModalBottomSheet()
    //     0x8b0e74: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b0e78: r0 = Null
    //     0x8b0e78: mov             x0, NULL
    // 0x8b0e7c: LeaveFrame
    //     0x8b0e7c: mov             SP, fp
    //     0x8b0e80: ldp             fp, lr, [SP], #0x10
    // 0x8b0e84: ret
    //     0x8b0e84: ret             
    // 0x8b0e88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b0e88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b0e8c: b               #0x8b0d74
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b0e90, size: 0x1b0
    // 0x8b0e90: EnterFrame
    //     0x8b0e90: stp             fp, lr, [SP, #-0x10]!
    //     0x8b0e94: mov             fp, SP
    // 0x8b0e98: AllocStack(0x28)
    //     0x8b0e98: sub             SP, SP, #0x28
    // 0x8b0e9c: SetupParameters()
    //     0x8b0e9c: ldr             x0, [fp, #0x18]
    //     0x8b0ea0: ldur            w1, [x0, #0x17]
    //     0x8b0ea4: add             x1, x1, HEAP, lsl #32
    //     0x8b0ea8: stur            x1, [fp, #-8]
    // 0x8b0eac: CheckStackOverflow
    //     0x8b0eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b0eb0: cmp             SP, x16
    //     0x8b0eb4: b.ls            #0x8b1038
    // 0x8b0eb8: r1 = 1
    //     0x8b0eb8: movz            x1, #0x1
    // 0x8b0ebc: r0 = AllocateContext()
    //     0x8b0ebc: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b0ec0: mov             x1, x0
    // 0x8b0ec4: ldur            x0, [fp, #-8]
    // 0x8b0ec8: stur            x1, [fp, #-0x10]
    // 0x8b0ecc: StoreField: r1->field_b = r0
    //     0x8b0ecc: stur            w0, [x1, #0xb]
    // 0x8b0ed0: ldr             x2, [fp, #0x10]
    // 0x8b0ed4: StoreField: r1->field_f = r2
    //     0x8b0ed4: stur            w2, [x1, #0xf]
    // 0x8b0ed8: LoadField: r2 = r0->field_13
    //     0x8b0ed8: ldur            w2, [x0, #0x13]
    // 0x8b0edc: DecompressPointer r2
    //     0x8b0edc: add             x2, x2, HEAP, lsl #32
    // 0x8b0ee0: cmp             w2, NULL
    // 0x8b0ee4: b.ne            #0x8b0ef0
    // 0x8b0ee8: d0 = inf
    //     0x8b0ee8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b0eec: b               #0x8b0f5c
    // 0x8b0ef0: LoadField: r3 = r2->field_7f
    //     0x8b0ef0: ldur            w3, [x2, #0x7f]
    // 0x8b0ef4: DecompressPointer r3
    //     0x8b0ef4: add             x3, x3, HEAP, lsl #32
    // 0x8b0ef8: cmp             w3, NULL
    // 0x8b0efc: b.eq            #0x8b0f58
    // 0x8b0f00: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b0f00: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b0f04: ldr             x0, [x0, #0x1c80]
    //     0x8b0f08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b0f0c: cmp             w0, w16
    //     0x8b0f10: b.ne            #0x8b0f1c
    //     0x8b0f14: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b0f18: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b0f1c: r0 = GetNavigation.size()
    //     0x8b0f1c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b0f20: LoadField: d0 = r0->field_f
    //     0x8b0f20: ldur            d0, [x0, #0xf]
    // 0x8b0f24: d1 = 0.430000
    //     0x8b0f24: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x8b0f28: ldr             d1, [x17, #0xb8]
    // 0x8b0f2c: fmul            d2, d0, d1
    // 0x8b0f30: stur            d2, [fp, #-0x28]
    // 0x8b0f34: r0 = BoxConstraints()
    //     0x8b0f34: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b0f38: StoreField: r0->field_7 = rZR
    //     0x8b0f38: stur            xzr, [x0, #7]
    // 0x8b0f3c: d0 = inf
    //     0x8b0f3c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b0f40: StoreField: r0->field_f = d0
    //     0x8b0f40: stur            d0, [x0, #0xf]
    // 0x8b0f44: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b0f44: stur            xzr, [x0, #0x17]
    // 0x8b0f48: ldur            d0, [fp, #-0x28]
    // 0x8b0f4c: StoreField: r0->field_1f = d0
    //     0x8b0f4c: stur            d0, [x0, #0x1f]
    // 0x8b0f50: mov             x3, x0
    // 0x8b0f54: b               #0x8b0fb0
    // 0x8b0f58: d0 = inf
    //     0x8b0f58: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b0f5c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b0f5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b0f60: ldr             x0, [x0, #0x1c80]
    //     0x8b0f64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b0f68: cmp             w0, w16
    //     0x8b0f6c: b.ne            #0x8b0f78
    //     0x8b0f70: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b0f74: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b0f78: r0 = GetNavigation.size()
    //     0x8b0f78: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b0f7c: LoadField: d0 = r0->field_f
    //     0x8b0f7c: ldur            d0, [x0, #0xf]
    // 0x8b0f80: d1 = 0.300000
    //     0x8b0f80: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x8b0f84: ldr             d1, [x17, #0x658]
    // 0x8b0f88: fmul            d2, d0, d1
    // 0x8b0f8c: stur            d2, [fp, #-0x28]
    // 0x8b0f90: r0 = BoxConstraints()
    //     0x8b0f90: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b0f94: StoreField: r0->field_7 = rZR
    //     0x8b0f94: stur            xzr, [x0, #7]
    // 0x8b0f98: d0 = inf
    //     0x8b0f98: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b0f9c: StoreField: r0->field_f = d0
    //     0x8b0f9c: stur            d0, [x0, #0xf]
    // 0x8b0fa0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b0fa0: stur            xzr, [x0, #0x17]
    // 0x8b0fa4: ldur            d0, [fp, #-0x28]
    // 0x8b0fa8: StoreField: r0->field_1f = d0
    //     0x8b0fa8: stur            d0, [x0, #0x1f]
    // 0x8b0fac: mov             x3, x0
    // 0x8b0fb0: ldur            x0, [fp, #-8]
    // 0x8b0fb4: stur            x3, [fp, #-0x20]
    // 0x8b0fb8: LoadField: r4 = r0->field_13
    //     0x8b0fb8: ldur            w4, [x0, #0x13]
    // 0x8b0fbc: DecompressPointer r4
    //     0x8b0fbc: add             x4, x4, HEAP, lsl #32
    // 0x8b0fc0: ldur            x2, [fp, #-0x10]
    // 0x8b0fc4: stur            x4, [fp, #-0x18]
    // 0x8b0fc8: r1 = Function '<anonymous closure>':.
    //     0x8b0fc8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36eb0] AnonymousClosure: (0x8b1140), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x8b0d4c)
    //     0x8b0fcc: ldr             x1, [x1, #0xeb0]
    // 0x8b0fd0: r0 = AllocateClosure()
    //     0x8b0fd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b0fd4: stur            x0, [fp, #-8]
    // 0x8b0fd8: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x8b0fd8: bl              #0x8b104c  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x8b0fdc: mov             x1, x0
    // 0x8b0fe0: ldur            x0, [fp, #-8]
    // 0x8b0fe4: stur            x1, [fp, #-0x10]
    // 0x8b0fe8: StoreField: r1->field_b = r0
    //     0x8b0fe8: stur            w0, [x1, #0xb]
    // 0x8b0fec: ldur            x0, [fp, #-0x18]
    // 0x8b0ff0: StoreField: r1->field_1b = r0
    //     0x8b0ff0: stur            w0, [x1, #0x1b]
    // 0x8b0ff4: r0 = "Confirm Cancellation"
    //     0x8b0ff4: add             x0, PP, #0x36, lsl #12  ; [pp+0x360c8] "Confirm Cancellation"
    //     0x8b0ff8: ldr             x0, [x0, #0xc8]
    // 0x8b0ffc: StoreField: r1->field_13 = r0
    //     0x8b0ffc: stur            w0, [x1, #0x13]
    // 0x8b1000: r0 = "On cancellation of this product the free gift will also be cancelled"
    //     0x8b1000: add             x0, PP, #0x36, lsl #12  ; [pp+0x360d0] "On cancellation of this product the free gift will also be cancelled"
    //     0x8b1004: ldr             x0, [x0, #0xd0]
    // 0x8b1008: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b1008: stur            w0, [x1, #0x17]
    // 0x8b100c: r0 = "cancel_order"
    //     0x8b100c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0x8b1010: ldr             x0, [x0, #0x98]
    // 0x8b1014: StoreField: r1->field_f = r0
    //     0x8b1014: stur            w0, [x1, #0xf]
    // 0x8b1018: r0 = ConstrainedBox()
    //     0x8b1018: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x8b101c: ldur            x1, [fp, #-0x20]
    // 0x8b1020: StoreField: r0->field_f = r1
    //     0x8b1020: stur            w1, [x0, #0xf]
    // 0x8b1024: ldur            x1, [fp, #-0x10]
    // 0x8b1028: StoreField: r0->field_b = r1
    //     0x8b1028: stur            w1, [x0, #0xb]
    // 0x8b102c: LeaveFrame
    //     0x8b102c: mov             SP, fp
    //     0x8b1030: ldp             fp, lr, [SP], #0x10
    // 0x8b1034: ret
    //     0x8b1034: ret             
    // 0x8b1038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b1038: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b103c: b               #0x8b0eb8
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x8b1140, size: 0x18c
    // 0x8b1140: EnterFrame
    //     0x8b1140: stp             fp, lr, [SP, #-0x10]!
    //     0x8b1144: mov             fp, SP
    // 0x8b1148: AllocStack(0x20)
    //     0x8b1148: sub             SP, SP, #0x20
    // 0x8b114c: SetupParameters()
    //     0x8b114c: ldr             x0, [fp, #0x10]
    //     0x8b1150: ldur            w1, [x0, #0x17]
    //     0x8b1154: add             x1, x1, HEAP, lsl #32
    //     0x8b1158: stur            x1, [fp, #-8]
    // 0x8b115c: CheckStackOverflow
    //     0x8b115c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b1160: cmp             SP, x16
    //     0x8b1164: b.ls            #0x8b12c4
    // 0x8b1168: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b1168: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b116c: ldr             x0, [x0, #0x1c80]
    //     0x8b1170: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b1174: cmp             w0, w16
    //     0x8b1178: b.ne            #0x8b1184
    //     0x8b117c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b1180: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b1184: str             NULL, [SP]
    // 0x8b1188: r4 = const [0x1, 0, 0, 0, null]
    //     0x8b1188: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x8b118c: r0 = GetNavigation.back()
    //     0x8b118c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x8b1190: ldur            x0, [fp, #-8]
    // 0x8b1194: LoadField: r4 = r0->field_b
    //     0x8b1194: ldur            w4, [x0, #0xb]
    // 0x8b1198: DecompressPointer r4
    //     0x8b1198: add             x4, x4, HEAP, lsl #32
    // 0x8b119c: stur            x4, [fp, #-0x10]
    // 0x8b11a0: LoadField: r1 = r4->field_f
    //     0x8b11a0: ldur            w1, [x4, #0xf]
    // 0x8b11a4: DecompressPointer r1
    //     0x8b11a4: add             x1, x1, HEAP, lsl #32
    // 0x8b11a8: LoadField: r2 = r4->field_13
    //     0x8b11a8: ldur            w2, [x4, #0x13]
    // 0x8b11ac: DecompressPointer r2
    //     0x8b11ac: add             x2, x2, HEAP, lsl #32
    // 0x8b11b0: cmp             w2, NULL
    // 0x8b11b4: b.ne            #0x8b11c0
    // 0x8b11b8: r3 = Null
    //     0x8b11b8: mov             x3, NULL
    // 0x8b11bc: b               #0x8b11c8
    // 0x8b11c0: LoadField: r3 = r2->field_37
    //     0x8b11c0: ldur            w3, [x2, #0x37]
    // 0x8b11c4: DecompressPointer r3
    //     0x8b11c4: add             x3, x3, HEAP, lsl #32
    // 0x8b11c8: cmp             w2, NULL
    // 0x8b11cc: b.ne            #0x8b11d8
    // 0x8b11d0: r5 = Null
    //     0x8b11d0: mov             x5, NULL
    // 0x8b11d4: b               #0x8b11e0
    // 0x8b11d8: LoadField: r5 = r2->field_7
    //     0x8b11d8: ldur            w5, [x2, #7]
    // 0x8b11dc: DecompressPointer r5
    //     0x8b11dc: add             x5, x5, HEAP, lsl #32
    // 0x8b11e0: cmp             w5, NULL
    // 0x8b11e4: b.ne            #0x8b11ec
    // 0x8b11e8: r5 = ""
    //     0x8b11e8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b11ec: LoadField: r6 = r0->field_f
    //     0x8b11ec: ldur            w6, [x0, #0xf]
    // 0x8b11f0: DecompressPointer r6
    //     0x8b11f0: add             x6, x6, HEAP, lsl #32
    // 0x8b11f4: cmp             w2, NULL
    // 0x8b11f8: b.ne            #0x8b1204
    // 0x8b11fc: r0 = Null
    //     0x8b11fc: mov             x0, NULL
    // 0x8b1200: b               #0x8b120c
    // 0x8b1204: LoadField: r0 = r2->field_2f
    //     0x8b1204: ldur            w0, [x2, #0x2f]
    // 0x8b1208: DecompressPointer r0
    //     0x8b1208: add             x0, x0, HEAP, lsl #32
    // 0x8b120c: cmp             w0, NULL
    // 0x8b1210: b.ne            #0x8b1218
    // 0x8b1214: r0 = ""
    //     0x8b1214: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b1218: mov             x2, x3
    // 0x8b121c: mov             x3, x5
    // 0x8b1220: mov             x5, x6
    // 0x8b1224: mov             x6, x0
    // 0x8b1228: r0 = cancelOrder()
    //     0x8b1228: bl              #0x8b12cc  ; [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder
    // 0x8b122c: ldur            x0, [fp, #-0x10]
    // 0x8b1230: LoadField: r1 = r0->field_f
    //     0x8b1230: ldur            w1, [x0, #0xf]
    // 0x8b1234: DecompressPointer r1
    //     0x8b1234: add             x1, x1, HEAP, lsl #32
    // 0x8b1238: r0 = controller()
    //     0x8b1238: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b123c: mov             x1, x0
    // 0x8b1240: ldur            x0, [fp, #-0x10]
    // 0x8b1244: stur            x1, [fp, #-0x18]
    // 0x8b1248: LoadField: r2 = r0->field_13
    //     0x8b1248: ldur            w2, [x0, #0x13]
    // 0x8b124c: DecompressPointer r2
    //     0x8b124c: add             x2, x2, HEAP, lsl #32
    // 0x8b1250: cmp             w2, NULL
    // 0x8b1254: b.ne            #0x8b1260
    // 0x8b1258: r0 = Null
    //     0x8b1258: mov             x0, NULL
    // 0x8b125c: b               #0x8b1268
    // 0x8b1260: LoadField: r0 = r2->field_7
    //     0x8b1260: ldur            w0, [x2, #7]
    // 0x8b1264: DecompressPointer r0
    //     0x8b1264: add             x0, x0, HEAP, lsl #32
    // 0x8b1268: cmp             w0, NULL
    // 0x8b126c: b.ne            #0x8b1274
    // 0x8b1270: r0 = ""
    //     0x8b1270: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b1274: stur            x0, [fp, #-8]
    // 0x8b1278: r0 = EventData()
    //     0x8b1278: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b127c: mov             x1, x0
    // 0x8b1280: ldur            x0, [fp, #-8]
    // 0x8b1284: stur            x1, [fp, #-0x10]
    // 0x8b1288: StoreField: r1->field_77 = r0
    //     0x8b1288: stur            w0, [x1, #0x77]
    // 0x8b128c: r0 = EventsRequest()
    //     0x8b128c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b1290: mov             x1, x0
    // 0x8b1294: r0 = "cancel_free_gift_continue_clicked"
    //     0x8b1294: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e0] "cancel_free_gift_continue_clicked"
    //     0x8b1298: ldr             x0, [x0, #0xe0]
    // 0x8b129c: StoreField: r1->field_7 = r0
    //     0x8b129c: stur            w0, [x1, #7]
    // 0x8b12a0: ldur            x0, [fp, #-0x10]
    // 0x8b12a4: StoreField: r1->field_b = r0
    //     0x8b12a4: stur            w0, [x1, #0xb]
    // 0x8b12a8: mov             x2, x1
    // 0x8b12ac: ldur            x1, [fp, #-0x18]
    // 0x8b12b0: r0 = postEvents()
    //     0x8b12b0: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b12b4: r0 = Null
    //     0x8b12b4: mov             x0, NULL
    // 0x8b12b8: LeaveFrame
    //     0x8b12b8: mov             SP, fp
    //     0x8b12bc: ldp             fp, lr, [SP], #0x10
    // 0x8b12c0: ret
    //     0x8b12c0: ret             
    // 0x8b12c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b12c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b12c8: b               #0x8b1168
  }
  _ cancelOrder(/* No info */) {
    // ** addr: 0x8b12cc, size: 0x108
    // 0x8b12cc: EnterFrame
    //     0x8b12cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b12d0: mov             fp, SP
    // 0x8b12d4: AllocStack(0x58)
    //     0x8b12d4: sub             SP, SP, #0x58
    // 0x8b12d8: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x8b12d8: stur            x1, [fp, #-8]
    //     0x8b12dc: stur            x2, [fp, #-0x10]
    //     0x8b12e0: stur            x3, [fp, #-0x18]
    //     0x8b12e4: stur            x5, [fp, #-0x20]
    //     0x8b12e8: stur            x6, [fp, #-0x28]
    // 0x8b12ec: CheckStackOverflow
    //     0x8b12ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b12f0: cmp             SP, x16
    //     0x8b12f4: b.ls            #0x8b13cc
    // 0x8b12f8: r1 = 4
    //     0x8b12f8: movz            x1, #0x4
    // 0x8b12fc: r0 = AllocateContext()
    //     0x8b12fc: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b1300: ldur            x1, [fp, #-8]
    // 0x8b1304: stur            x0, [fp, #-0x30]
    // 0x8b1308: StoreField: r0->field_f = r1
    //     0x8b1308: stur            w1, [x0, #0xf]
    // 0x8b130c: ldur            x2, [fp, #-0x10]
    // 0x8b1310: StoreField: r0->field_13 = r2
    //     0x8b1310: stur            w2, [x0, #0x13]
    // 0x8b1314: ldur            x2, [fp, #-0x18]
    // 0x8b1318: ArrayStore: r0[0] = r2  ; List_4
    //     0x8b1318: stur            w2, [x0, #0x17]
    // 0x8b131c: ldur            x2, [fp, #-0x28]
    // 0x8b1320: StoreField: r0->field_1b = r2
    //     0x8b1320: stur            w2, [x0, #0x1b]
    // 0x8b1324: r0 = controller()
    //     0x8b1324: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b1328: ldur            x2, [fp, #-0x30]
    // 0x8b132c: stur            x0, [fp, #-0x10]
    // 0x8b1330: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x8b1330: ldur            w1, [x2, #0x17]
    // 0x8b1334: DecompressPointer r1
    //     0x8b1334: add             x1, x1, HEAP, lsl #32
    // 0x8b1338: stur            x1, [fp, #-8]
    // 0x8b133c: r0 = EventData()
    //     0x8b133c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b1340: mov             x1, x0
    // 0x8b1344: r0 = "order_page"
    //     0x8b1344: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b1348: ldr             x0, [x0, #0x710]
    // 0x8b134c: stur            x1, [fp, #-0x18]
    // 0x8b1350: StoreField: r1->field_13 = r0
    //     0x8b1350: stur            w0, [x1, #0x13]
    // 0x8b1354: ldur            x0, [fp, #-8]
    // 0x8b1358: StoreField: r1->field_77 = r0
    //     0x8b1358: stur            w0, [x1, #0x77]
    // 0x8b135c: r0 = EventsRequest()
    //     0x8b135c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b1360: mov             x1, x0
    // 0x8b1364: r0 = "cancel_order_clicked"
    //     0x8b1364: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e8] "cancel_order_clicked"
    //     0x8b1368: ldr             x0, [x0, #0xe8]
    // 0x8b136c: StoreField: r1->field_7 = r0
    //     0x8b136c: stur            w0, [x1, #7]
    // 0x8b1370: ldur            x0, [fp, #-0x18]
    // 0x8b1374: StoreField: r1->field_b = r0
    //     0x8b1374: stur            w0, [x1, #0xb]
    // 0x8b1378: mov             x2, x1
    // 0x8b137c: ldur            x1, [fp, #-0x10]
    // 0x8b1380: r0 = postEvents()
    //     0x8b1380: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b1384: ldur            x2, [fp, #-0x30]
    // 0x8b1388: r1 = Function '<anonymous closure>':.
    //     0x8b1388: add             x1, PP, #0x36, lsl #12  ; [pp+0x36eb8] AnonymousClosure: (0x8b13d4), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder (0x8b12cc)
    //     0x8b138c: ldr             x1, [x1, #0xeb8]
    // 0x8b1390: r0 = AllocateClosure()
    //     0x8b1390: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b1394: stp             x0, NULL, [SP, #0x18]
    // 0x8b1398: ldur            x16, [fp, #-0x20]
    // 0x8b139c: r30 = Instance_RoundedRectangleBorder
    //     0x8b139c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x8b13a0: ldr             lr, [lr, #0xd68]
    // 0x8b13a4: stp             lr, x16, [SP, #8]
    // 0x8b13a8: r16 = true
    //     0x8b13a8: add             x16, NULL, #0x20  ; true
    // 0x8b13ac: str             x16, [SP]
    // 0x8b13b0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x8b13b0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x8b13b4: ldr             x4, [x4, #0xd70]
    // 0x8b13b8: r0 = showModalBottomSheet()
    //     0x8b13b8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b13bc: r0 = Null
    //     0x8b13bc: mov             x0, NULL
    // 0x8b13c0: LeaveFrame
    //     0x8b13c0: mov             SP, fp
    //     0x8b13c4: ldp             fp, lr, [SP], #0x10
    // 0x8b13c8: ret
    //     0x8b13c8: ret             
    // 0x8b13cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b13cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b13d0: b               #0x8b12f8
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b13d4, size: 0x4d4
    // 0x8b13d4: EnterFrame
    //     0x8b13d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b13d8: mov             fp, SP
    // 0x8b13dc: AllocStack(0x68)
    //     0x8b13dc: sub             SP, SP, #0x68
    // 0x8b13e0: SetupParameters()
    //     0x8b13e0: ldr             x0, [fp, #0x18]
    //     0x8b13e4: ldur            w2, [x0, #0x17]
    //     0x8b13e8: add             x2, x2, HEAP, lsl #32
    //     0x8b13ec: stur            x2, [fp, #-8]
    // 0x8b13f0: CheckStackOverflow
    //     0x8b13f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b13f4: cmp             SP, x16
    //     0x8b13f8: b.ls            #0x8b18a0
    // 0x8b13fc: LoadField: r0 = r2->field_13
    //     0x8b13fc: ldur            w0, [x2, #0x13]
    // 0x8b1400: DecompressPointer r0
    //     0x8b1400: add             x0, x0, HEAP, lsl #32
    // 0x8b1404: cmp             w0, NULL
    // 0x8b1408: b.eq            #0x8b1518
    // 0x8b140c: LoadField: r1 = r0->field_13
    //     0x8b140c: ldur            w1, [x0, #0x13]
    // 0x8b1410: DecompressPointer r1
    //     0x8b1410: add             x1, x1, HEAP, lsl #32
    // 0x8b1414: cmp             w1, NULL
    // 0x8b1418: b.ne            #0x8b1468
    // 0x8b141c: LoadField: r1 = r0->field_7
    //     0x8b141c: ldur            w1, [x0, #7]
    // 0x8b1420: DecompressPointer r1
    //     0x8b1420: add             x1, x1, HEAP, lsl #32
    // 0x8b1424: cmp             w1, NULL
    // 0x8b1428: b.eq            #0x8b1460
    // 0x8b142c: LoadField: r0 = r2->field_1b
    //     0x8b142c: ldur            w0, [x2, #0x1b]
    // 0x8b1430: DecompressPointer r0
    //     0x8b1430: add             x0, x0, HEAP, lsl #32
    // 0x8b1434: r1 = LoadClassIdInstr(r0)
    //     0x8b1434: ldur            x1, [x0, #-1]
    //     0x8b1438: ubfx            x1, x1, #0xc, #0x14
    // 0x8b143c: r16 = "cod"
    //     0x8b143c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0x8b1440: ldr             x16, [x16, #0xa28]
    // 0x8b1444: stp             x16, x0, [SP]
    // 0x8b1448: mov             x0, x1
    // 0x8b144c: mov             lr, x0
    // 0x8b1450: ldr             lr, [x21, lr, lsl #3]
    // 0x8b1454: blr             lr
    // 0x8b1458: eor             x1, x0, #0x10
    // 0x8b145c: tbz             w1, #4, #0x8b146c
    // 0x8b1460: d0 = inf
    //     0x8b1460: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b1464: b               #0x8b14c4
    // 0x8b1468: tbnz            w1, #4, #0x8b14c0
    // 0x8b146c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b146c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b1470: ldr             x0, [x0, #0x1c80]
    //     0x8b1474: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b1478: cmp             w0, w16
    //     0x8b147c: b.ne            #0x8b1488
    //     0x8b1480: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b1484: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b1488: r0 = GetNavigation.size()
    //     0x8b1488: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b148c: LoadField: d0 = r0->field_f
    //     0x8b148c: ldur            d0, [x0, #0xf]
    // 0x8b1490: d1 = 0.800000
    //     0x8b1490: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x8b1494: ldr             d1, [x17, #0xb28]
    // 0x8b1498: fmul            d2, d0, d1
    // 0x8b149c: stur            d2, [fp, #-0x58]
    // 0x8b14a0: r0 = BoxConstraints()
    //     0x8b14a0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b14a4: StoreField: r0->field_7 = rZR
    //     0x8b14a4: stur            xzr, [x0, #7]
    // 0x8b14a8: d0 = inf
    //     0x8b14a8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b14ac: StoreField: r0->field_f = d0
    //     0x8b14ac: stur            d0, [x0, #0xf]
    // 0x8b14b0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b14b0: stur            xzr, [x0, #0x17]
    // 0x8b14b4: ldur            d0, [fp, #-0x58]
    // 0x8b14b8: StoreField: r0->field_1f = d0
    //     0x8b14b8: stur            d0, [x0, #0x1f]
    // 0x8b14bc: b               #0x8b156c
    // 0x8b14c0: d0 = inf
    //     0x8b14c0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b14c4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b14c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b14c8: ldr             x0, [x0, #0x1c80]
    //     0x8b14cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b14d0: cmp             w0, w16
    //     0x8b14d4: b.ne            #0x8b14e0
    //     0x8b14d8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b14dc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b14e0: r0 = GetNavigation.size()
    //     0x8b14e0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b14e4: LoadField: d0 = r0->field_f
    //     0x8b14e4: ldur            d0, [x0, #0xf]
    // 0x8b14e8: d1 = 0.430000
    //     0x8b14e8: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x8b14ec: ldr             d1, [x17, #0xb8]
    // 0x8b14f0: fmul            d2, d0, d1
    // 0x8b14f4: stur            d2, [fp, #-0x58]
    // 0x8b14f8: r0 = BoxConstraints()
    //     0x8b14f8: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b14fc: StoreField: r0->field_7 = rZR
    //     0x8b14fc: stur            xzr, [x0, #7]
    // 0x8b1500: d0 = inf
    //     0x8b1500: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b1504: StoreField: r0->field_f = d0
    //     0x8b1504: stur            d0, [x0, #0xf]
    // 0x8b1508: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b1508: stur            xzr, [x0, #0x17]
    // 0x8b150c: ldur            d0, [fp, #-0x58]
    // 0x8b1510: StoreField: r0->field_1f = d0
    //     0x8b1510: stur            d0, [x0, #0x1f]
    // 0x8b1514: b               #0x8b156c
    // 0x8b1518: d0 = inf
    //     0x8b1518: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b151c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b151c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b1520: ldr             x0, [x0, #0x1c80]
    //     0x8b1524: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b1528: cmp             w0, w16
    //     0x8b152c: b.ne            #0x8b1538
    //     0x8b1530: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b1534: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b1538: r0 = GetNavigation.size()
    //     0x8b1538: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b153c: LoadField: d0 = r0->field_f
    //     0x8b153c: ldur            d0, [x0, #0xf]
    // 0x8b1540: d1 = 0.300000
    //     0x8b1540: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x8b1544: ldr             d1, [x17, #0x658]
    // 0x8b1548: fmul            d2, d0, d1
    // 0x8b154c: stur            d2, [fp, #-0x58]
    // 0x8b1550: r0 = BoxConstraints()
    //     0x8b1550: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b1554: StoreField: r0->field_7 = rZR
    //     0x8b1554: stur            xzr, [x0, #7]
    // 0x8b1558: d0 = inf
    //     0x8b1558: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b155c: StoreField: r0->field_f = d0
    //     0x8b155c: stur            d0, [x0, #0xf]
    // 0x8b1560: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b1560: stur            xzr, [x0, #0x17]
    // 0x8b1564: ldur            d0, [fp, #-0x58]
    // 0x8b1568: StoreField: r0->field_1f = d0
    //     0x8b1568: stur            d0, [x0, #0x1f]
    // 0x8b156c: ldur            x2, [fp, #-8]
    // 0x8b1570: stur            x0, [fp, #-0x10]
    // 0x8b1574: LoadField: r1 = r2->field_f
    //     0x8b1574: ldur            w1, [x2, #0xf]
    // 0x8b1578: DecompressPointer r1
    //     0x8b1578: add             x1, x1, HEAP, lsl #32
    // 0x8b157c: r0 = controller()
    //     0x8b157c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b1580: LoadField: r1 = r0->field_67
    //     0x8b1580: ldur            w1, [x0, #0x67]
    // 0x8b1584: DecompressPointer r1
    //     0x8b1584: add             x1, x1, HEAP, lsl #32
    // 0x8b1588: r0 = value()
    //     0x8b1588: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8b158c: cmp             w0, NULL
    // 0x8b1590: b.ne            #0x8b159c
    // 0x8b1594: r3 = Null
    //     0x8b1594: mov             x3, NULL
    // 0x8b1598: b               #0x8b15a8
    // 0x8b159c: LoadField: r1 = r0->field_2b
    //     0x8b159c: ldur            w1, [x0, #0x2b]
    // 0x8b15a0: DecompressPointer r1
    //     0x8b15a0: add             x1, x1, HEAP, lsl #32
    // 0x8b15a4: mov             x3, x1
    // 0x8b15a8: ldur            x0, [fp, #-8]
    // 0x8b15ac: stur            x3, [fp, #-0x18]
    // 0x8b15b0: r1 = Null
    //     0x8b15b0: mov             x1, NULL
    // 0x8b15b4: r2 = 4
    //     0x8b15b4: movz            x2, #0x4
    // 0x8b15b8: r0 = AllocateArray()
    //     0x8b15b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b15bc: mov             x1, x0
    // 0x8b15c0: ldur            x0, [fp, #-0x18]
    // 0x8b15c4: StoreField: r1->field_f = r0
    //     0x8b15c4: stur            w0, [x1, #0xf]
    // 0x8b15c8: r16 = " has accepted your order, it is already under process"
    //     0x8b15c8: add             x16, PP, #0x36, lsl #12  ; [pp+0x360f8] " has accepted your order, it is already under process"
    //     0x8b15cc: ldr             x16, [x16, #0xf8]
    // 0x8b15d0: StoreField: r1->field_13 = r16
    //     0x8b15d0: stur            w16, [x1, #0x13]
    // 0x8b15d4: str             x1, [SP]
    // 0x8b15d8: r0 = _interpolate()
    //     0x8b15d8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8b15dc: mov             x1, x0
    // 0x8b15e0: ldur            x2, [fp, #-8]
    // 0x8b15e4: stur            x1, [fp, #-0x18]
    // 0x8b15e8: LoadField: r0 = r2->field_13
    //     0x8b15e8: ldur            w0, [x2, #0x13]
    // 0x8b15ec: DecompressPointer r0
    //     0x8b15ec: add             x0, x0, HEAP, lsl #32
    // 0x8b15f0: cmp             w0, NULL
    // 0x8b15f4: b.eq            #0x8b169c
    // 0x8b15f8: LoadField: r3 = r0->field_b
    //     0x8b15f8: ldur            w3, [x0, #0xb]
    // 0x8b15fc: DecompressPointer r3
    //     0x8b15fc: add             x3, x3, HEAP, lsl #32
    // 0x8b1600: cmp             w3, NULL
    // 0x8b1604: b.eq            #0x8b169c
    // 0x8b1608: r0 = 60
    //     0x8b1608: movz            x0, #0x3c
    // 0x8b160c: branchIfSmi(r3, 0x8b1618)
    //     0x8b160c: tbz             w3, #0, #0x8b1618
    // 0x8b1610: r0 = LoadClassIdInstr(r3)
    //     0x8b1610: ldur            x0, [x3, #-1]
    //     0x8b1614: ubfx            x0, x0, #0xc, #0x14
    // 0x8b1618: stp             xzr, x3, [SP]
    // 0x8b161c: mov             lr, x0
    // 0x8b1620: ldr             lr, [x21, lr, lsl #3]
    // 0x8b1624: blr             lr
    // 0x8b1628: tbz             w0, #4, #0x8b169c
    // 0x8b162c: ldur            x0, [fp, #-8]
    // 0x8b1630: r1 = Null
    //     0x8b1630: mov             x1, NULL
    // 0x8b1634: r2 = 6
    //     0x8b1634: movz            x2, #0x6
    // 0x8b1638: r0 = AllocateArray()
    //     0x8b1638: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b163c: r16 = "CANCEL ("
    //     0x8b163c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36100] "CANCEL ("
    //     0x8b1640: ldr             x16, [x16, #0x100]
    // 0x8b1644: StoreField: r0->field_f = r16
    //     0x8b1644: stur            w16, [x0, #0xf]
    // 0x8b1648: ldur            x2, [fp, #-8]
    // 0x8b164c: LoadField: r1 = r2->field_13
    //     0x8b164c: ldur            w1, [x2, #0x13]
    // 0x8b1650: DecompressPointer r1
    //     0x8b1650: add             x1, x1, HEAP, lsl #32
    // 0x8b1654: cmp             w1, NULL
    // 0x8b1658: b.ne            #0x8b1664
    // 0x8b165c: r1 = Null
    //     0x8b165c: mov             x1, NULL
    // 0x8b1660: b               #0x8b1670
    // 0x8b1664: LoadField: r3 = r1->field_23
    //     0x8b1664: ldur            w3, [x1, #0x23]
    // 0x8b1668: DecompressPointer r3
    //     0x8b1668: add             x3, x3, HEAP, lsl #32
    // 0x8b166c: mov             x1, x3
    // 0x8b1670: cmp             w1, NULL
    // 0x8b1674: b.ne            #0x8b167c
    // 0x8b1678: r1 = ""
    //     0x8b1678: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b167c: StoreField: r0->field_13 = r1
    //     0x8b167c: stur            w1, [x0, #0x13]
    // 0x8b1680: r16 = " CHARGE)"
    //     0x8b1680: add             x16, PP, #0x36, lsl #12  ; [pp+0x36108] " CHARGE)"
    //     0x8b1684: ldr             x16, [x16, #0x108]
    // 0x8b1688: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b1688: stur            w16, [x0, #0x17]
    // 0x8b168c: str             x0, [SP]
    // 0x8b1690: r0 = _interpolate()
    //     0x8b1690: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8b1694: mov             x4, x0
    // 0x8b1698: b               #0x8b16a4
    // 0x8b169c: r4 = "CANCEL ORDER"
    //     0x8b169c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36110] "CANCEL ORDER"
    //     0x8b16a0: ldr             x4, [x4, #0x110]
    // 0x8b16a4: ldur            x2, [fp, #-8]
    // 0x8b16a8: ldur            x3, [fp, #-0x10]
    // 0x8b16ac: ldur            x0, [fp, #-0x18]
    // 0x8b16b0: stur            x4, [fp, #-0x30]
    // 0x8b16b4: LoadField: r5 = r2->field_13
    //     0x8b16b4: ldur            w5, [x2, #0x13]
    // 0x8b16b8: DecompressPointer r5
    //     0x8b16b8: add             x5, x5, HEAP, lsl #32
    // 0x8b16bc: stur            x5, [fp, #-0x28]
    // 0x8b16c0: LoadField: r6 = r2->field_1b
    //     0x8b16c0: ldur            w6, [x2, #0x1b]
    // 0x8b16c4: DecompressPointer r6
    //     0x8b16c4: add             x6, x6, HEAP, lsl #32
    // 0x8b16c8: ldr             x1, [fp, #0x10]
    // 0x8b16cc: stur            x6, [fp, #-0x20]
    // 0x8b16d0: r0 = of()
    //     0x8b16d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x8b16d4: LoadField: r1 = r0->field_87
    //     0x8b16d4: ldur            w1, [x0, #0x87]
    // 0x8b16d8: DecompressPointer r1
    //     0x8b16d8: add             x1, x1, HEAP, lsl #32
    // 0x8b16dc: LoadField: r0 = r1->field_2b
    //     0x8b16dc: ldur            w0, [x1, #0x2b]
    // 0x8b16e0: DecompressPointer r0
    //     0x8b16e0: add             x0, x0, HEAP, lsl #32
    // 0x8b16e4: stur            x0, [fp, #-0x38]
    // 0x8b16e8: r1 = Instance_Color
    //     0x8b16e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8b16ec: d0 = 0.400000
    //     0x8b16ec: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x8b16f0: r0 = withOpacity()
    //     0x8b16f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8b16f4: r16 = 14.000000
    //     0x8b16f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x8b16f8: ldr             x16, [x16, #0x1d8]
    // 0x8b16fc: stp             x0, x16, [SP]
    // 0x8b1700: ldur            x1, [fp, #-0x38]
    // 0x8b1704: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x8b1704: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x8b1708: ldr             x4, [x4, #0xaa0]
    // 0x8b170c: r0 = copyWith()
    //     0x8b170c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8b1710: stur            x0, [fp, #-0x38]
    // 0x8b1714: r0 = Radius()
    //     0x8b1714: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8b1718: d0 = 4.000000
    //     0x8b1718: fmov            d0, #4.00000000
    // 0x8b171c: stur            x0, [fp, #-0x40]
    // 0x8b1720: StoreField: r0->field_7 = d0
    //     0x8b1720: stur            d0, [x0, #7]
    // 0x8b1724: StoreField: r0->field_f = d0
    //     0x8b1724: stur            d0, [x0, #0xf]
    // 0x8b1728: r0 = BorderRadius()
    //     0x8b1728: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8b172c: mov             x1, x0
    // 0x8b1730: ldur            x0, [fp, #-0x40]
    // 0x8b1734: stur            x1, [fp, #-0x48]
    // 0x8b1738: StoreField: r1->field_7 = r0
    //     0x8b1738: stur            w0, [x1, #7]
    // 0x8b173c: StoreField: r1->field_b = r0
    //     0x8b173c: stur            w0, [x1, #0xb]
    // 0x8b1740: StoreField: r1->field_f = r0
    //     0x8b1740: stur            w0, [x1, #0xf]
    // 0x8b1744: StoreField: r1->field_13 = r0
    //     0x8b1744: stur            w0, [x1, #0x13]
    // 0x8b1748: r0 = OutlineInputBorder()
    //     0x8b1748: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x8b174c: mov             x1, x0
    // 0x8b1750: ldur            x0, [fp, #-0x48]
    // 0x8b1754: stur            x1, [fp, #-0x40]
    // 0x8b1758: StoreField: r1->field_13 = r0
    //     0x8b1758: stur            w0, [x1, #0x13]
    // 0x8b175c: d0 = 4.000000
    //     0x8b175c: fmov            d0, #4.00000000
    // 0x8b1760: StoreField: r1->field_b = d0
    //     0x8b1760: stur            d0, [x1, #0xb]
    // 0x8b1764: r0 = Instance_BorderSide
    //     0x8b1764: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x8b1768: ldr             x0, [x0, #0x118]
    // 0x8b176c: StoreField: r1->field_7 = r0
    //     0x8b176c: stur            w0, [x1, #7]
    // 0x8b1770: r0 = Radius()
    //     0x8b1770: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8b1774: d0 = 4.000000
    //     0x8b1774: fmov            d0, #4.00000000
    // 0x8b1778: stur            x0, [fp, #-0x48]
    // 0x8b177c: StoreField: r0->field_7 = d0
    //     0x8b177c: stur            d0, [x0, #7]
    // 0x8b1780: StoreField: r0->field_f = d0
    //     0x8b1780: stur            d0, [x0, #0xf]
    // 0x8b1784: r0 = BorderRadius()
    //     0x8b1784: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8b1788: mov             x1, x0
    // 0x8b178c: ldur            x0, [fp, #-0x48]
    // 0x8b1790: stur            x1, [fp, #-0x50]
    // 0x8b1794: StoreField: r1->field_7 = r0
    //     0x8b1794: stur            w0, [x1, #7]
    // 0x8b1798: StoreField: r1->field_b = r0
    //     0x8b1798: stur            w0, [x1, #0xb]
    // 0x8b179c: StoreField: r1->field_f = r0
    //     0x8b179c: stur            w0, [x1, #0xf]
    // 0x8b17a0: StoreField: r1->field_13 = r0
    //     0x8b17a0: stur            w0, [x1, #0x13]
    // 0x8b17a4: r0 = OutlineInputBorder()
    //     0x8b17a4: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x8b17a8: mov             x1, x0
    // 0x8b17ac: ldur            x0, [fp, #-0x50]
    // 0x8b17b0: stur            x1, [fp, #-0x48]
    // 0x8b17b4: StoreField: r1->field_13 = r0
    //     0x8b17b4: stur            w0, [x1, #0x13]
    // 0x8b17b8: d0 = 4.000000
    //     0x8b17b8: fmov            d0, #4.00000000
    // 0x8b17bc: StoreField: r1->field_b = d0
    //     0x8b17bc: stur            d0, [x1, #0xb]
    // 0x8b17c0: r0 = Instance_BorderSide
    //     0x8b17c0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x8b17c4: ldr             x0, [x0, #0x118]
    // 0x8b17c8: StoreField: r1->field_7 = r0
    //     0x8b17c8: stur            w0, [x1, #7]
    // 0x8b17cc: r0 = InputDecoration()
    //     0x8b17cc: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0x8b17d0: mov             x1, x0
    // 0x8b17d4: r0 = "Mention the reason"
    //     0x8b17d4: add             x0, PP, #0x36, lsl #12  ; [pp+0x36120] "Mention the reason"
    //     0x8b17d8: ldr             x0, [x0, #0x120]
    // 0x8b17dc: stur            x1, [fp, #-0x50]
    // 0x8b17e0: StoreField: r1->field_13 = r0
    //     0x8b17e0: stur            w0, [x1, #0x13]
    // 0x8b17e4: ldur            x0, [fp, #-0x38]
    // 0x8b17e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b17e8: stur            w0, [x1, #0x17]
    // 0x8b17ec: r0 = true
    //     0x8b17ec: add             x0, NULL, #0x20  ; true
    // 0x8b17f0: StoreField: r1->field_47 = r0
    //     0x8b17f0: stur            w0, [x1, #0x47]
    // 0x8b17f4: StoreField: r1->field_4b = r0
    //     0x8b17f4: stur            w0, [x1, #0x4b]
    // 0x8b17f8: ldur            x2, [fp, #-0x48]
    // 0x8b17fc: StoreField: r1->field_c3 = r2
    //     0x8b17fc: stur            w2, [x1, #0xc3]
    // 0x8b1800: ldur            x2, [fp, #-0x40]
    // 0x8b1804: StoreField: r1->field_cf = r2
    //     0x8b1804: stur            w2, [x1, #0xcf]
    // 0x8b1808: StoreField: r1->field_d7 = r0
    //     0x8b1808: stur            w0, [x1, #0xd7]
    // 0x8b180c: r0 = CancelOrderConfirmBottomSheet()
    //     0x8b180c: bl              #0x8b1964  ; AllocateCancelOrderConfirmBottomSheetStub -> CancelOrderConfirmBottomSheet (size=0x34)
    // 0x8b1810: mov             x3, x0
    // 0x8b1814: ldur            x0, [fp, #-0x28]
    // 0x8b1818: stur            x3, [fp, #-0x38]
    // 0x8b181c: StoreField: r3->field_b = r0
    //     0x8b181c: stur            w0, [x3, #0xb]
    // 0x8b1820: ldur            x0, [fp, #-0x18]
    // 0x8b1824: StoreField: r3->field_f = r0
    //     0x8b1824: stur            w0, [x3, #0xf]
    // 0x8b1828: r0 = "Do you want to cancel this order\?"
    //     0x8b1828: add             x0, PP, #0x36, lsl #12  ; [pp+0x36128] "Do you want to cancel this order\?"
    //     0x8b182c: ldr             x0, [x0, #0x128]
    // 0x8b1830: StoreField: r3->field_13 = r0
    //     0x8b1830: stur            w0, [x3, #0x13]
    // 0x8b1834: ldur            x0, [fp, #-0x30]
    // 0x8b1838: ArrayStore: r3[0] = r0  ; List_4
    //     0x8b1838: stur            w0, [x3, #0x17]
    // 0x8b183c: ldur            x2, [fp, #-8]
    // 0x8b1840: r1 = Function '<anonymous closure>':.
    //     0x8b1840: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ec0] AnonymousClosure: (0x8b19a0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder (0x8b12cc)
    //     0x8b1844: ldr             x1, [x1, #0xec0]
    // 0x8b1848: r0 = AllocateClosure()
    //     0x8b1848: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b184c: mov             x1, x0
    // 0x8b1850: ldur            x0, [fp, #-0x38]
    // 0x8b1854: StoreField: r0->field_1b = r1
    //     0x8b1854: stur            w1, [x0, #0x1b]
    // 0x8b1858: r1 = Instance_BorderRadius
    //     0x8b1858: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x8b185c: ldr             x1, [x1, #0xf70]
    // 0x8b1860: StoreField: r0->field_1f = r1
    //     0x8b1860: stur            w1, [x0, #0x1f]
    // 0x8b1864: ldur            x1, [fp, #-0x50]
    // 0x8b1868: StoreField: r0->field_23 = r1
    //     0x8b1868: stur            w1, [x0, #0x23]
    // 0x8b186c: r1 = "GO BACK"
    //     0x8b186c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36138] "GO BACK"
    //     0x8b1870: ldr             x1, [x1, #0x138]
    // 0x8b1874: StoreField: r0->field_2b = r1
    //     0x8b1874: stur            w1, [x0, #0x2b]
    // 0x8b1878: ldur            x1, [fp, #-0x20]
    // 0x8b187c: StoreField: r0->field_2f = r1
    //     0x8b187c: stur            w1, [x0, #0x2f]
    // 0x8b1880: r0 = ConstrainedBox()
    //     0x8b1880: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x8b1884: ldur            x1, [fp, #-0x10]
    // 0x8b1888: StoreField: r0->field_f = r1
    //     0x8b1888: stur            w1, [x0, #0xf]
    // 0x8b188c: ldur            x1, [fp, #-0x38]
    // 0x8b1890: StoreField: r0->field_b = r1
    //     0x8b1890: stur            w1, [x0, #0xb]
    // 0x8b1894: LeaveFrame
    //     0x8b1894: mov             SP, fp
    //     0x8b1898: ldp             fp, lr, [SP], #0x10
    // 0x8b189c: ret
    //     0x8b189c: ret             
    // 0x8b18a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b18a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b18a4: b               #0x8b13fc
  }
  [closure] Null <anonymous closure>(dynamic, String?, String?) {
    // ** addr: 0x8b19a0, size: 0x8c
    // 0x8b19a0: EnterFrame
    //     0x8b19a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8b19a4: mov             fp, SP
    // 0x8b19a8: AllocStack(0x18)
    //     0x8b19a8: sub             SP, SP, #0x18
    // 0x8b19ac: SetupParameters()
    //     0x8b19ac: ldr             x0, [fp, #0x20]
    //     0x8b19b0: ldur            w2, [x0, #0x17]
    //     0x8b19b4: add             x2, x2, HEAP, lsl #32
    //     0x8b19b8: stur            x2, [fp, #-8]
    // 0x8b19bc: CheckStackOverflow
    //     0x8b19bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b19c0: cmp             SP, x16
    //     0x8b19c4: b.ls            #0x8b1a24
    // 0x8b19c8: LoadField: r1 = r2->field_f
    //     0x8b19c8: ldur            w1, [x2, #0xf]
    // 0x8b19cc: DecompressPointer r1
    //     0x8b19cc: add             x1, x1, HEAP, lsl #32
    // 0x8b19d0: r0 = controller()
    //     0x8b19d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b19d4: mov             x1, x0
    // 0x8b19d8: ldur            x0, [fp, #-8]
    // 0x8b19dc: stur            x1, [fp, #-0x18]
    // 0x8b19e0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8b19e0: ldur            w2, [x0, #0x17]
    // 0x8b19e4: DecompressPointer r2
    //     0x8b19e4: add             x2, x2, HEAP, lsl #32
    // 0x8b19e8: stur            x2, [fp, #-0x10]
    // 0x8b19ec: r0 = CancelOrderRequest()
    //     0x8b19ec: bl              #0x8b5238  ; AllocateCancelOrderRequestStub -> CancelOrderRequest (size=0x10)
    // 0x8b19f0: mov             x1, x0
    // 0x8b19f4: ldr             x0, [fp, #0x18]
    // 0x8b19f8: StoreField: r1->field_7 = r0
    //     0x8b19f8: stur            w0, [x1, #7]
    // 0x8b19fc: ldr             x0, [fp, #0x10]
    // 0x8b1a00: StoreField: r1->field_b = r0
    //     0x8b1a00: stur            w0, [x1, #0xb]
    // 0x8b1a04: mov             x3, x1
    // 0x8b1a08: ldur            x1, [fp, #-0x18]
    // 0x8b1a0c: ldur            x2, [fp, #-0x10]
    // 0x8b1a10: r0 = cancelOrder()
    //     0x8b1a10: bl              #0x8b1a2c  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelOrder
    // 0x8b1a14: r0 = Null
    //     0x8b1a14: mov             x0, NULL
    // 0x8b1a18: LeaveFrame
    //     0x8b1a18: mov             SP, fp
    //     0x8b1a1c: ldp             fp, lr, [SP], #0x10
    // 0x8b1a20: ret
    //     0x8b1a20: ret             
    // 0x8b1a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b1a24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b1a28: b               #0x8b19c8
  }
  [closure] void cancelOrder(dynamic, OrderCancellationPopup?, String, BuildContext, String) {
    // ** addr: 0x8b5784, size: 0x48
    // 0x8b5784: EnterFrame
    //     0x8b5784: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5788: mov             fp, SP
    // 0x8b578c: ldr             x0, [fp, #0x30]
    // 0x8b5790: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b5790: ldur            w1, [x0, #0x17]
    // 0x8b5794: DecompressPointer r1
    //     0x8b5794: add             x1, x1, HEAP, lsl #32
    // 0x8b5798: CheckStackOverflow
    //     0x8b5798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b579c: cmp             SP, x16
    //     0x8b57a0: b.ls            #0x8b57c4
    // 0x8b57a4: ldr             x2, [fp, #0x28]
    // 0x8b57a8: ldr             x3, [fp, #0x20]
    // 0x8b57ac: ldr             x5, [fp, #0x18]
    // 0x8b57b0: ldr             x6, [fp, #0x10]
    // 0x8b57b4: r0 = cancelOrder()
    //     0x8b57b4: bl              #0x8b12cc  ; [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder
    // 0x8b57b8: LeaveFrame
    //     0x8b57b8: mov             SP, fp
    //     0x8b57bc: ldp             fp, lr, [SP], #0x10
    // 0x8b57c0: ret
    //     0x8b57c0: ret             
    // 0x8b57c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b57c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b57c8: b               #0x8b57a4
  }
  _ body(/* No info */) {
    // ** addr: 0x15069cc, size: 0x94
    // 0x15069cc: EnterFrame
    //     0x15069cc: stp             fp, lr, [SP, #-0x10]!
    //     0x15069d0: mov             fp, SP
    // 0x15069d4: AllocStack(0x18)
    //     0x15069d4: sub             SP, SP, #0x18
    // 0x15069d8: SetupParameters(OrdersView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15069d8: mov             x0, x1
    //     0x15069dc: stur            x1, [fp, #-8]
    //     0x15069e0: stur            x2, [fp, #-0x10]
    // 0x15069e4: r1 = 2
    //     0x15069e4: movz            x1, #0x2
    // 0x15069e8: r0 = AllocateContext()
    //     0x15069e8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15069ec: ldur            x2, [fp, #-8]
    // 0x15069f0: stur            x0, [fp, #-0x18]
    // 0x15069f4: StoreField: r0->field_f = r2
    //     0x15069f4: stur            w2, [x0, #0xf]
    // 0x15069f8: ldur            x1, [fp, #-0x10]
    // 0x15069fc: StoreField: r0->field_13 = r1
    //     0x15069fc: stur            w1, [x0, #0x13]
    // 0x1506a00: r0 = Obx()
    //     0x1506a00: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1506a04: ldur            x2, [fp, #-0x18]
    // 0x1506a08: r1 = Function '<anonymous closure>':.
    //     0x1506a08: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d28] AnonymousClosure: (0x8a3e30), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x1506a0c: ldr             x1, [x1, #0xd28]
    // 0x1506a10: stur            x0, [fp, #-0x10]
    // 0x1506a14: r0 = AllocateClosure()
    //     0x1506a14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506a18: mov             x1, x0
    // 0x1506a1c: ldur            x0, [fp, #-0x10]
    // 0x1506a20: StoreField: r0->field_b = r1
    //     0x1506a20: stur            w1, [x0, #0xb]
    // 0x1506a24: r0 = WillPopScope()
    //     0x1506a24: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1506a28: mov             x3, x0
    // 0x1506a2c: ldur            x0, [fp, #-0x10]
    // 0x1506a30: stur            x3, [fp, #-0x18]
    // 0x1506a34: StoreField: r3->field_b = r0
    //     0x1506a34: stur            w0, [x3, #0xb]
    // 0x1506a38: ldur            x2, [fp, #-8]
    // 0x1506a3c: r1 = Function '_onBackPress@1713465688':.
    //     0x1506a3c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d30] AnonymousClosure: (0x1506a60), in [package:customer_app/app/presentation/views/basic/orders/orders_view.dart] OrdersView::_onBackPress (0x14057f0)
    //     0x1506a40: ldr             x1, [x1, #0xd30]
    // 0x1506a44: r0 = AllocateClosure()
    //     0x1506a44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1506a48: mov             x1, x0
    // 0x1506a4c: ldur            x0, [fp, #-0x18]
    // 0x1506a50: StoreField: r0->field_f = r1
    //     0x1506a50: stur            w1, [x0, #0xf]
    // 0x1506a54: LeaveFrame
    //     0x1506a54: mov             SP, fp
    //     0x1506a58: ldp             fp, lr, [SP], #0x10
    // 0x1506a5c: ret
    //     0x1506a5c: ret             
  }
  [closure] Future<bool> _onBackPress(dynamic) {
    // ** addr: 0x1506a60, size: 0x38
    // 0x1506a60: EnterFrame
    //     0x1506a60: stp             fp, lr, [SP, #-0x10]!
    //     0x1506a64: mov             fp, SP
    // 0x1506a68: ldr             x0, [fp, #0x10]
    // 0x1506a6c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1506a6c: ldur            w1, [x0, #0x17]
    // 0x1506a70: DecompressPointer r1
    //     0x1506a70: add             x1, x1, HEAP, lsl #32
    // 0x1506a74: CheckStackOverflow
    //     0x1506a74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1506a78: cmp             SP, x16
    //     0x1506a7c: b.ls            #0x1506a90
    // 0x1506a80: r0 = _onBackPress()
    //     0x1506a80: bl              #0x14057f0  ; [package:customer_app/app/presentation/views/basic/orders/orders_view.dart] OrdersView::_onBackPress
    // 0x1506a84: LeaveFrame
    //     0x1506a84: mov             SP, fp
    //     0x1506a88: ldp             fp, lr, [SP], #0x10
    // 0x1506a8c: ret
    //     0x1506a8c: ret             
    // 0x1506a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1506a90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1506a94: b               #0x1506a80
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x15d2d68, size: 0x50
    // 0x15d2d68: EnterFrame
    //     0x15d2d68: stp             fp, lr, [SP, #-0x10]!
    //     0x15d2d6c: mov             fp, SP
    // 0x15d2d70: ldr             x0, [fp, #0x18]
    // 0x15d2d74: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15d2d74: ldur            w1, [x0, #0x17]
    // 0x15d2d78: DecompressPointer r1
    //     0x15d2d78: add             x1, x1, HEAP, lsl #32
    // 0x15d2d7c: CheckStackOverflow
    //     0x15d2d7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d2d80: cmp             SP, x16
    //     0x15d2d84: b.ls            #0x15d2db0
    // 0x15d2d88: LoadField: r0 = r1->field_f
    //     0x15d2d88: ldur            w0, [x1, #0xf]
    // 0x15d2d8c: DecompressPointer r0
    //     0x15d2d8c: add             x0, x0, HEAP, lsl #32
    // 0x15d2d90: mov             x1, x0
    // 0x15d2d94: r0 = controller()
    //     0x15d2d94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2d98: mov             x1, x0
    // 0x15d2d9c: r0 = getBagCount()
    //     0x15d2d9c: bl              #0x8a92ac  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getBagCount
    // 0x15d2da0: r0 = Null
    //     0x15d2da0: mov             x0, NULL
    // 0x15d2da4: LeaveFrame
    //     0x15d2da4: mov             SP, fp
    //     0x15d2da8: ldp             fp, lr, [SP], #0x10
    // 0x15d2dac: ret
    //     0x15d2dac: ret             
    // 0x15d2db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d2db0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d2db4: b               #0x15d2d88
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d2db8, size: 0xdc
    // 0x15d2db8: EnterFrame
    //     0x15d2db8: stp             fp, lr, [SP, #-0x10]!
    //     0x15d2dbc: mov             fp, SP
    // 0x15d2dc0: AllocStack(0x28)
    //     0x15d2dc0: sub             SP, SP, #0x28
    // 0x15d2dc4: SetupParameters()
    //     0x15d2dc4: ldr             x0, [fp, #0x10]
    //     0x15d2dc8: ldur            w2, [x0, #0x17]
    //     0x15d2dcc: add             x2, x2, HEAP, lsl #32
    //     0x15d2dd0: stur            x2, [fp, #-8]
    // 0x15d2dd4: CheckStackOverflow
    //     0x15d2dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d2dd8: cmp             SP, x16
    //     0x15d2ddc: b.ls            #0x15d2e8c
    // 0x15d2de0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d2de0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d2de4: ldr             x0, [x0, #0x1c80]
    //     0x15d2de8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d2dec: cmp             w0, w16
    //     0x15d2df0: b.ne            #0x15d2dfc
    //     0x15d2df4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d2df8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d2dfc: r1 = Null
    //     0x15d2dfc: mov             x1, NULL
    // 0x15d2e00: r2 = 4
    //     0x15d2e00: movz            x2, #0x4
    // 0x15d2e04: r0 = AllocateArray()
    //     0x15d2e04: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d2e08: r16 = "previousScreenSource"
    //     0x15d2e08: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15d2e0c: ldr             x16, [x16, #0x448]
    // 0x15d2e10: StoreField: r0->field_f = r16
    //     0x15d2e10: stur            w16, [x0, #0xf]
    // 0x15d2e14: r16 = "order_page"
    //     0x15d2e14: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x15d2e18: ldr             x16, [x16, #0x710]
    // 0x15d2e1c: StoreField: r0->field_13 = r16
    //     0x15d2e1c: stur            w16, [x0, #0x13]
    // 0x15d2e20: r16 = <String, String>
    //     0x15d2e20: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15d2e24: ldr             x16, [x16, #0x788]
    // 0x15d2e28: stp             x0, x16, [SP]
    // 0x15d2e2c: r0 = Map._fromLiteral()
    //     0x15d2e2c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15d2e30: r16 = "/bag"
    //     0x15d2e30: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15d2e34: ldr             x16, [x16, #0x468]
    // 0x15d2e38: stp             x16, NULL, [SP, #8]
    // 0x15d2e3c: str             x0, [SP]
    // 0x15d2e40: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15d2e40: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15d2e44: ldr             x4, [x4, #0x438]
    // 0x15d2e48: r0 = GetNavigation.toNamed()
    //     0x15d2e48: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d2e4c: stur            x0, [fp, #-0x10]
    // 0x15d2e50: cmp             w0, NULL
    // 0x15d2e54: b.eq            #0x15d2e7c
    // 0x15d2e58: ldur            x2, [fp, #-8]
    // 0x15d2e5c: r1 = Function '<anonymous closure>':.
    //     0x15d2e5c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f18] AnonymousClosure: (0x15d2d68), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15d2e60: ldr             x1, [x1, #0xf18]
    // 0x15d2e64: r0 = AllocateClosure()
    //     0x15d2e64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d2e68: ldur            x16, [fp, #-0x10]
    // 0x15d2e6c: stp             x16, NULL, [SP, #8]
    // 0x15d2e70: str             x0, [SP]
    // 0x15d2e74: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15d2e74: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15d2e78: r0 = then()
    //     0x15d2e78: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15d2e7c: r0 = Null
    //     0x15d2e7c: mov             x0, NULL
    // 0x15d2e80: LeaveFrame
    //     0x15d2e80: mov             SP, fp
    //     0x15d2e84: ldp             fp, lr, [SP], #0x10
    // 0x15d2e88: ret
    //     0x15d2e88: ret             
    // 0x15d2e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d2e8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d2e90: b               #0x15d2de0
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15d2e94, size: 0x2f4
    // 0x15d2e94: EnterFrame
    //     0x15d2e94: stp             fp, lr, [SP, #-0x10]!
    //     0x15d2e98: mov             fp, SP
    // 0x15d2e9c: AllocStack(0x58)
    //     0x15d2e9c: sub             SP, SP, #0x58
    // 0x15d2ea0: SetupParameters()
    //     0x15d2ea0: ldr             x0, [fp, #0x10]
    //     0x15d2ea4: ldur            w2, [x0, #0x17]
    //     0x15d2ea8: add             x2, x2, HEAP, lsl #32
    //     0x15d2eac: stur            x2, [fp, #-8]
    // 0x15d2eb0: CheckStackOverflow
    //     0x15d2eb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d2eb4: cmp             SP, x16
    //     0x15d2eb8: b.ls            #0x15d3180
    // 0x15d2ebc: LoadField: r1 = r2->field_f
    //     0x15d2ebc: ldur            w1, [x2, #0xf]
    // 0x15d2ec0: DecompressPointer r1
    //     0x15d2ec0: add             x1, x1, HEAP, lsl #32
    // 0x15d2ec4: r0 = controller()
    //     0x15d2ec4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2ec8: mov             x1, x0
    // 0x15d2ecc: r0 = appliedCoupon()
    //     0x15d2ecc: bl              #0x913a0c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::appliedCoupon
    // 0x15d2ed0: LoadField: r1 = r0->field_1f
    //     0x15d2ed0: ldur            w1, [x0, #0x1f]
    // 0x15d2ed4: DecompressPointer r1
    //     0x15d2ed4: add             x1, x1, HEAP, lsl #32
    // 0x15d2ed8: cmp             w1, NULL
    // 0x15d2edc: b.ne            #0x15d2ee8
    // 0x15d2ee0: r0 = Null
    //     0x15d2ee0: mov             x0, NULL
    // 0x15d2ee4: b               #0x15d2ef0
    // 0x15d2ee8: LoadField: r0 = r1->field_7
    //     0x15d2ee8: ldur            w0, [x1, #7]
    // 0x15d2eec: DecompressPointer r0
    //     0x15d2eec: add             x0, x0, HEAP, lsl #32
    // 0x15d2ef0: cmp             w0, NULL
    // 0x15d2ef4: b.ne            #0x15d2f00
    // 0x15d2ef8: r0 = false
    //     0x15d2ef8: add             x0, NULL, #0x30  ; false
    // 0x15d2efc: b               #0x15d30e8
    // 0x15d2f00: tbnz            w0, #4, #0x15d30e4
    // 0x15d2f04: ldur            x2, [fp, #-8]
    // 0x15d2f08: LoadField: r1 = r2->field_f
    //     0x15d2f08: ldur            w1, [x2, #0xf]
    // 0x15d2f0c: DecompressPointer r1
    //     0x15d2f0c: add             x1, x1, HEAP, lsl #32
    // 0x15d2f10: r0 = controller()
    //     0x15d2f10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2f14: mov             x1, x0
    // 0x15d2f18: r0 = bumperCouponData()
    //     0x15d2f18: bl              #0x8a2a70  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bumperCouponData
    // 0x15d2f1c: ldur            x2, [fp, #-8]
    // 0x15d2f20: stur            x0, [fp, #-0x10]
    // 0x15d2f24: LoadField: r1 = r2->field_13
    //     0x15d2f24: ldur            w1, [x2, #0x13]
    // 0x15d2f28: DecompressPointer r1
    //     0x15d2f28: add             x1, x1, HEAP, lsl #32
    // 0x15d2f2c: r0 = of()
    //     0x15d2f2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d2f30: LoadField: r2 = r0->field_5b
    //     0x15d2f30: ldur            w2, [x0, #0x5b]
    // 0x15d2f34: DecompressPointer r2
    //     0x15d2f34: add             x2, x2, HEAP, lsl #32
    // 0x15d2f38: ldur            x0, [fp, #-8]
    // 0x15d2f3c: stur            x2, [fp, #-0x18]
    // 0x15d2f40: LoadField: r1 = r0->field_f
    //     0x15d2f40: ldur            w1, [x0, #0xf]
    // 0x15d2f44: DecompressPointer r1
    //     0x15d2f44: add             x1, x1, HEAP, lsl #32
    // 0x15d2f48: r0 = controller()
    //     0x15d2f48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2f4c: LoadField: r1 = r0->field_87
    //     0x15d2f4c: ldur            w1, [x0, #0x87]
    // 0x15d2f50: DecompressPointer r1
    //     0x15d2f50: add             x1, x1, HEAP, lsl #32
    // 0x15d2f54: r0 = value()
    //     0x15d2f54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d2f58: cmp             w0, NULL
    // 0x15d2f5c: r16 = true
    //     0x15d2f5c: add             x16, NULL, #0x20  ; true
    // 0x15d2f60: r17 = false
    //     0x15d2f60: add             x17, NULL, #0x30  ; false
    // 0x15d2f64: csel            x2, x16, x17, ne
    // 0x15d2f68: ldur            x0, [fp, #-8]
    // 0x15d2f6c: stur            x2, [fp, #-0x20]
    // 0x15d2f70: LoadField: r1 = r0->field_f
    //     0x15d2f70: ldur            w1, [x0, #0xf]
    // 0x15d2f74: DecompressPointer r1
    //     0x15d2f74: add             x1, x1, HEAP, lsl #32
    // 0x15d2f78: r0 = controller()
    //     0x15d2f78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d2f7c: LoadField: r1 = r0->field_87
    //     0x15d2f7c: ldur            w1, [x0, #0x87]
    // 0x15d2f80: DecompressPointer r1
    //     0x15d2f80: add             x1, x1, HEAP, lsl #32
    // 0x15d2f84: r0 = value()
    //     0x15d2f84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d2f88: str             x0, [SP]
    // 0x15d2f8c: r0 = _interpolateSingle()
    //     0x15d2f8c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15d2f90: ldur            x2, [fp, #-8]
    // 0x15d2f94: stur            x0, [fp, #-0x28]
    // 0x15d2f98: LoadField: r1 = r2->field_13
    //     0x15d2f98: ldur            w1, [x2, #0x13]
    // 0x15d2f9c: DecompressPointer r1
    //     0x15d2f9c: add             x1, x1, HEAP, lsl #32
    // 0x15d2fa0: r0 = of()
    //     0x15d2fa0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d2fa4: LoadField: r1 = r0->field_87
    //     0x15d2fa4: ldur            w1, [x0, #0x87]
    // 0x15d2fa8: DecompressPointer r1
    //     0x15d2fa8: add             x1, x1, HEAP, lsl #32
    // 0x15d2fac: LoadField: r0 = r1->field_27
    //     0x15d2fac: ldur            w0, [x1, #0x27]
    // 0x15d2fb0: DecompressPointer r0
    //     0x15d2fb0: add             x0, x0, HEAP, lsl #32
    // 0x15d2fb4: r16 = Instance_Color
    //     0x15d2fb4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15d2fb8: str             x16, [SP]
    // 0x15d2fbc: mov             x1, x0
    // 0x15d2fc0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15d2fc0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15d2fc4: ldr             x4, [x4, #0xf40]
    // 0x15d2fc8: r0 = copyWith()
    //     0x15d2fc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d2fcc: stur            x0, [fp, #-0x30]
    // 0x15d2fd0: r0 = Text()
    //     0x15d2fd0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d2fd4: mov             x2, x0
    // 0x15d2fd8: ldur            x0, [fp, #-0x28]
    // 0x15d2fdc: stur            x2, [fp, #-0x38]
    // 0x15d2fe0: StoreField: r2->field_b = r0
    //     0x15d2fe0: stur            w0, [x2, #0xb]
    // 0x15d2fe4: ldur            x0, [fp, #-0x30]
    // 0x15d2fe8: StoreField: r2->field_13 = r0
    //     0x15d2fe8: stur            w0, [x2, #0x13]
    // 0x15d2fec: ldur            x0, [fp, #-8]
    // 0x15d2ff0: LoadField: r1 = r0->field_13
    //     0x15d2ff0: ldur            w1, [x0, #0x13]
    // 0x15d2ff4: DecompressPointer r1
    //     0x15d2ff4: add             x1, x1, HEAP, lsl #32
    // 0x15d2ff8: r0 = of()
    //     0x15d2ff8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d2ffc: LoadField: r1 = r0->field_5b
    //     0x15d2ffc: ldur            w1, [x0, #0x5b]
    // 0x15d3000: DecompressPointer r1
    //     0x15d3000: add             x1, x1, HEAP, lsl #32
    // 0x15d3004: stur            x1, [fp, #-0x28]
    // 0x15d3008: r0 = ColorFilter()
    //     0x15d3008: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d300c: mov             x1, x0
    // 0x15d3010: ldur            x0, [fp, #-0x28]
    // 0x15d3014: stur            x1, [fp, #-0x30]
    // 0x15d3018: StoreField: r1->field_7 = r0
    //     0x15d3018: stur            w0, [x1, #7]
    // 0x15d301c: r0 = Instance_BlendMode
    //     0x15d301c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d3020: ldr             x0, [x0, #0xb30]
    // 0x15d3024: StoreField: r1->field_b = r0
    //     0x15d3024: stur            w0, [x1, #0xb]
    // 0x15d3028: r0 = 1
    //     0x15d3028: movz            x0, #0x1
    // 0x15d302c: StoreField: r1->field_13 = r0
    //     0x15d302c: stur            x0, [x1, #0x13]
    // 0x15d3030: r0 = SvgPicture()
    //     0x15d3030: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d3034: stur            x0, [fp, #-0x28]
    // 0x15d3038: r16 = Instance_BoxFit
    //     0x15d3038: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d303c: ldr             x16, [x16, #0xb18]
    // 0x15d3040: r30 = 24.000000
    //     0x15d3040: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d3044: ldr             lr, [lr, #0xba8]
    // 0x15d3048: stp             lr, x16, [SP, #0x10]
    // 0x15d304c: r16 = 24.000000
    //     0x15d304c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d3050: ldr             x16, [x16, #0xba8]
    // 0x15d3054: ldur            lr, [fp, #-0x30]
    // 0x15d3058: stp             lr, x16, [SP]
    // 0x15d305c: mov             x1, x0
    // 0x15d3060: r2 = "assets/images/shopping_bag.svg"
    //     0x15d3060: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15d3064: ldr             x2, [x2, #0xa60]
    // 0x15d3068: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d3068: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d306c: ldr             x4, [x4, #0xa68]
    // 0x15d3070: r0 = SvgPicture.asset()
    //     0x15d3070: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d3074: r0 = Badge()
    //     0x15d3074: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15d3078: mov             x1, x0
    // 0x15d307c: ldur            x0, [fp, #-0x18]
    // 0x15d3080: stur            x1, [fp, #-0x30]
    // 0x15d3084: StoreField: r1->field_b = r0
    //     0x15d3084: stur            w0, [x1, #0xb]
    // 0x15d3088: ldur            x0, [fp, #-0x38]
    // 0x15d308c: StoreField: r1->field_27 = r0
    //     0x15d308c: stur            w0, [x1, #0x27]
    // 0x15d3090: ldur            x0, [fp, #-0x20]
    // 0x15d3094: StoreField: r1->field_2b = r0
    //     0x15d3094: stur            w0, [x1, #0x2b]
    // 0x15d3098: ldur            x0, [fp, #-0x28]
    // 0x15d309c: StoreField: r1->field_2f = r0
    //     0x15d309c: stur            w0, [x1, #0x2f]
    // 0x15d30a0: r0 = Visibility()
    //     0x15d30a0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d30a4: mov             x1, x0
    // 0x15d30a8: ldur            x0, [fp, #-0x30]
    // 0x15d30ac: StoreField: r1->field_b = r0
    //     0x15d30ac: stur            w0, [x1, #0xb]
    // 0x15d30b0: r0 = Instance_SizedBox
    //     0x15d30b0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d30b4: StoreField: r1->field_f = r0
    //     0x15d30b4: stur            w0, [x1, #0xf]
    // 0x15d30b8: ldur            x0, [fp, #-0x10]
    // 0x15d30bc: StoreField: r1->field_13 = r0
    //     0x15d30bc: stur            w0, [x1, #0x13]
    // 0x15d30c0: r0 = false
    //     0x15d30c0: add             x0, NULL, #0x30  ; false
    // 0x15d30c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d30c4: stur            w0, [x1, #0x17]
    // 0x15d30c8: StoreField: r1->field_1b = r0
    //     0x15d30c8: stur            w0, [x1, #0x1b]
    // 0x15d30cc: StoreField: r1->field_1f = r0
    //     0x15d30cc: stur            w0, [x1, #0x1f]
    // 0x15d30d0: StoreField: r1->field_23 = r0
    //     0x15d30d0: stur            w0, [x1, #0x23]
    // 0x15d30d4: StoreField: r1->field_27 = r0
    //     0x15d30d4: stur            w0, [x1, #0x27]
    // 0x15d30d8: StoreField: r1->field_2b = r0
    //     0x15d30d8: stur            w0, [x1, #0x2b]
    // 0x15d30dc: mov             x0, x1
    // 0x15d30e0: b               #0x15d3100
    // 0x15d30e4: r0 = false
    //     0x15d30e4: add             x0, NULL, #0x30  ; false
    // 0x15d30e8: r0 = Container()
    //     0x15d30e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15d30ec: mov             x1, x0
    // 0x15d30f0: stur            x0, [fp, #-0x10]
    // 0x15d30f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15d30f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15d30f8: r0 = Container()
    //     0x15d30f8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15d30fc: ldur            x0, [fp, #-0x10]
    // 0x15d3100: stur            x0, [fp, #-0x10]
    // 0x15d3104: r0 = InkWell()
    //     0x15d3104: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d3108: mov             x3, x0
    // 0x15d310c: ldur            x0, [fp, #-0x10]
    // 0x15d3110: stur            x3, [fp, #-0x18]
    // 0x15d3114: StoreField: r3->field_b = r0
    //     0x15d3114: stur            w0, [x3, #0xb]
    // 0x15d3118: ldur            x2, [fp, #-8]
    // 0x15d311c: r1 = Function '<anonymous closure>':.
    //     0x15d311c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f10] AnonymousClosure: (0x15d2db8), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15d3120: ldr             x1, [x1, #0xf10]
    // 0x15d3124: r0 = AllocateClosure()
    //     0x15d3124: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d3128: mov             x1, x0
    // 0x15d312c: ldur            x0, [fp, #-0x18]
    // 0x15d3130: StoreField: r0->field_f = r1
    //     0x15d3130: stur            w1, [x0, #0xf]
    // 0x15d3134: r1 = true
    //     0x15d3134: add             x1, NULL, #0x20  ; true
    // 0x15d3138: StoreField: r0->field_43 = r1
    //     0x15d3138: stur            w1, [x0, #0x43]
    // 0x15d313c: r2 = Instance_BoxShape
    //     0x15d313c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d3140: ldr             x2, [x2, #0x80]
    // 0x15d3144: StoreField: r0->field_47 = r2
    //     0x15d3144: stur            w2, [x0, #0x47]
    // 0x15d3148: StoreField: r0->field_6f = r1
    //     0x15d3148: stur            w1, [x0, #0x6f]
    // 0x15d314c: r2 = false
    //     0x15d314c: add             x2, NULL, #0x30  ; false
    // 0x15d3150: StoreField: r0->field_73 = r2
    //     0x15d3150: stur            w2, [x0, #0x73]
    // 0x15d3154: StoreField: r0->field_83 = r1
    //     0x15d3154: stur            w1, [x0, #0x83]
    // 0x15d3158: StoreField: r0->field_7b = r2
    //     0x15d3158: stur            w2, [x0, #0x7b]
    // 0x15d315c: r0 = Padding()
    //     0x15d315c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15d3160: r1 = Instance_EdgeInsets
    //     0x15d3160: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15d3164: ldr             x1, [x1, #0xa78]
    // 0x15d3168: StoreField: r0->field_f = r1
    //     0x15d3168: stur            w1, [x0, #0xf]
    // 0x15d316c: ldur            x1, [fp, #-0x18]
    // 0x15d3170: StoreField: r0->field_b = r1
    //     0x15d3170: stur            w1, [x0, #0xb]
    // 0x15d3174: LeaveFrame
    //     0x15d3174: mov             SP, fp
    //     0x15d3178: ldp             fp, lr, [SP], #0x10
    // 0x15d317c: ret
    //     0x15d317c: ret             
    // 0x15d3180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d3180: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d3184: b               #0x15d2ebc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d3188, size: 0xcc
    // 0x15d3188: EnterFrame
    //     0x15d3188: stp             fp, lr, [SP, #-0x10]!
    //     0x15d318c: mov             fp, SP
    // 0x15d3190: AllocStack(0x18)
    //     0x15d3190: sub             SP, SP, #0x18
    // 0x15d3194: SetupParameters()
    //     0x15d3194: ldr             x0, [fp, #0x10]
    //     0x15d3198: ldur            w3, [x0, #0x17]
    //     0x15d319c: add             x3, x3, HEAP, lsl #32
    //     0x15d31a0: stur            x3, [fp, #-8]
    // 0x15d31a4: CheckStackOverflow
    //     0x15d31a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d31a8: cmp             SP, x16
    //     0x15d31ac: b.ls            #0x15d324c
    // 0x15d31b0: LoadField: r1 = r3->field_f
    //     0x15d31b0: ldur            w1, [x3, #0xf]
    // 0x15d31b4: DecompressPointer r1
    //     0x15d31b4: add             x1, x1, HEAP, lsl #32
    // 0x15d31b8: r2 = false
    //     0x15d31b8: add             x2, NULL, #0x30  ; false
    // 0x15d31bc: r0 = showLoading()
    //     0x15d31bc: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d31c0: ldur            x0, [fp, #-8]
    // 0x15d31c4: LoadField: r1 = r0->field_f
    //     0x15d31c4: ldur            w1, [x0, #0xf]
    // 0x15d31c8: DecompressPointer r1
    //     0x15d31c8: add             x1, x1, HEAP, lsl #32
    // 0x15d31cc: r0 = controller()
    //     0x15d31cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d31d0: LoadField: r1 = r0->field_7f
    //     0x15d31d0: ldur            w1, [x0, #0x7f]
    // 0x15d31d4: DecompressPointer r1
    //     0x15d31d4: add             x1, x1, HEAP, lsl #32
    // 0x15d31d8: r0 = value()
    //     0x15d31d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d31dc: tbnz            w0, #4, #0x15d3214
    // 0x15d31e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d31e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d31e4: ldr             x0, [x0, #0x1c80]
    //     0x15d31e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d31ec: cmp             w0, w16
    //     0x15d31f0: b.ne            #0x15d31fc
    //     0x15d31f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d31f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d31fc: r16 = "/search"
    //     0x15d31fc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15d3200: ldr             x16, [x16, #0x838]
    // 0x15d3204: stp             x16, NULL, [SP]
    // 0x15d3208: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15d3208: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15d320c: r0 = GetNavigation.toNamed()
    //     0x15d320c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d3210: b               #0x15d323c
    // 0x15d3214: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d3214: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d3218: ldr             x0, [x0, #0x1c80]
    //     0x15d321c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d3220: cmp             w0, w16
    //     0x15d3224: b.ne            #0x15d3230
    //     0x15d3228: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d322c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d3230: str             NULL, [SP]
    // 0x15d3234: r4 = const [0x1, 0, 0, 0, null]
    //     0x15d3234: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15d3238: r0 = GetNavigation.back()
    //     0x15d3238: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15d323c: r0 = Null
    //     0x15d323c: mov             x0, NULL
    // 0x15d3240: LeaveFrame
    //     0x15d3240: mov             SP, fp
    //     0x15d3244: ldp             fp, lr, [SP], #0x10
    // 0x15d3248: ret
    //     0x15d3248: ret             
    // 0x15d324c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d324c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d3250: b               #0x15d31b0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15eaca0, size: 0x2b0
    // 0x15eaca0: EnterFrame
    //     0x15eaca0: stp             fp, lr, [SP, #-0x10]!
    //     0x15eaca4: mov             fp, SP
    // 0x15eaca8: AllocStack(0x30)
    //     0x15eaca8: sub             SP, SP, #0x30
    // 0x15eacac: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15eacac: stur            x1, [fp, #-8]
    //     0x15eacb0: stur            x2, [fp, #-0x10]
    // 0x15eacb4: CheckStackOverflow
    //     0x15eacb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eacb8: cmp             SP, x16
    //     0x15eacbc: b.ls            #0x15eaf48
    // 0x15eacc0: r1 = 2
    //     0x15eacc0: movz            x1, #0x2
    // 0x15eacc4: r0 = AllocateContext()
    //     0x15eacc4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15eacc8: ldur            x1, [fp, #-8]
    // 0x15eaccc: stur            x0, [fp, #-0x18]
    // 0x15eacd0: StoreField: r0->field_f = r1
    //     0x15eacd0: stur            w1, [x0, #0xf]
    // 0x15eacd4: ldur            x2, [fp, #-0x10]
    // 0x15eacd8: StoreField: r0->field_13 = r2
    //     0x15eacd8: stur            w2, [x0, #0x13]
    // 0x15eacdc: r0 = Obx()
    //     0x15eacdc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15eace0: ldur            x2, [fp, #-0x18]
    // 0x15eace4: r1 = Function '<anonymous closure>':.
    //     0x15eace4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ef8] AnonymousClosure: (0x15eaf50), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15eace8: ldr             x1, [x1, #0xef8]
    // 0x15eacec: stur            x0, [fp, #-0x10]
    // 0x15eacf0: r0 = AllocateClosure()
    //     0x15eacf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eacf4: mov             x1, x0
    // 0x15eacf8: ldur            x0, [fp, #-0x10]
    // 0x15eacfc: StoreField: r0->field_b = r1
    //     0x15eacfc: stur            w1, [x0, #0xb]
    // 0x15ead00: ldur            x1, [fp, #-8]
    // 0x15ead04: r0 = controller()
    //     0x15ead04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ead08: mov             x1, x0
    // 0x15ead0c: r0 = couponType()
    //     0x15ead0c: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x15ead10: tbnz            w0, #4, #0x15eada8
    // 0x15ead14: ldur            x2, [fp, #-0x18]
    // 0x15ead18: LoadField: r1 = r2->field_13
    //     0x15ead18: ldur            w1, [x2, #0x13]
    // 0x15ead1c: DecompressPointer r1
    //     0x15ead1c: add             x1, x1, HEAP, lsl #32
    // 0x15ead20: r0 = of()
    //     0x15ead20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ead24: LoadField: r1 = r0->field_5b
    //     0x15ead24: ldur            w1, [x0, #0x5b]
    // 0x15ead28: DecompressPointer r1
    //     0x15ead28: add             x1, x1, HEAP, lsl #32
    // 0x15ead2c: stur            x1, [fp, #-8]
    // 0x15ead30: r0 = ColorFilter()
    //     0x15ead30: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ead34: mov             x1, x0
    // 0x15ead38: ldur            x0, [fp, #-8]
    // 0x15ead3c: stur            x1, [fp, #-0x20]
    // 0x15ead40: StoreField: r1->field_7 = r0
    //     0x15ead40: stur            w0, [x1, #7]
    // 0x15ead44: r0 = Instance_BlendMode
    //     0x15ead44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ead48: ldr             x0, [x0, #0xb30]
    // 0x15ead4c: StoreField: r1->field_b = r0
    //     0x15ead4c: stur            w0, [x1, #0xb]
    // 0x15ead50: r2 = 1
    //     0x15ead50: movz            x2, #0x1
    // 0x15ead54: StoreField: r1->field_13 = r2
    //     0x15ead54: stur            x2, [x1, #0x13]
    // 0x15ead58: r0 = SvgPicture()
    //     0x15ead58: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ead5c: stur            x0, [fp, #-8]
    // 0x15ead60: ldur            x16, [fp, #-0x20]
    // 0x15ead64: str             x16, [SP]
    // 0x15ead68: mov             x1, x0
    // 0x15ead6c: r2 = "assets/images/search.svg"
    //     0x15ead6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15ead70: ldr             x2, [x2, #0xa30]
    // 0x15ead74: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ead74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ead78: ldr             x4, [x4, #0xa38]
    // 0x15ead7c: r0 = SvgPicture.asset()
    //     0x15ead7c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ead80: r0 = Align()
    //     0x15ead80: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ead84: r3 = Instance_Alignment
    //     0x15ead84: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ead88: ldr             x3, [x3, #0xb10]
    // 0x15ead8c: StoreField: r0->field_f = r3
    //     0x15ead8c: stur            w3, [x0, #0xf]
    // 0x15ead90: r4 = 1.000000
    //     0x15ead90: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ead94: StoreField: r0->field_13 = r4
    //     0x15ead94: stur            w4, [x0, #0x13]
    // 0x15ead98: ArrayStore: r0[0] = r4  ; List_4
    //     0x15ead98: stur            w4, [x0, #0x17]
    // 0x15ead9c: ldur            x1, [fp, #-8]
    // 0x15eada0: StoreField: r0->field_b = r1
    //     0x15eada0: stur            w1, [x0, #0xb]
    // 0x15eada4: b               #0x15eae58
    // 0x15eada8: ldur            x5, [fp, #-0x18]
    // 0x15eadac: r4 = 1.000000
    //     0x15eadac: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eadb0: r0 = Instance_BlendMode
    //     0x15eadb0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eadb4: ldr             x0, [x0, #0xb30]
    // 0x15eadb8: r3 = Instance_Alignment
    //     0x15eadb8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eadbc: ldr             x3, [x3, #0xb10]
    // 0x15eadc0: r2 = 1
    //     0x15eadc0: movz            x2, #0x1
    // 0x15eadc4: LoadField: r1 = r5->field_13
    //     0x15eadc4: ldur            w1, [x5, #0x13]
    // 0x15eadc8: DecompressPointer r1
    //     0x15eadc8: add             x1, x1, HEAP, lsl #32
    // 0x15eadcc: r0 = of()
    //     0x15eadcc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eadd0: LoadField: r1 = r0->field_5b
    //     0x15eadd0: ldur            w1, [x0, #0x5b]
    // 0x15eadd4: DecompressPointer r1
    //     0x15eadd4: add             x1, x1, HEAP, lsl #32
    // 0x15eadd8: stur            x1, [fp, #-8]
    // 0x15eaddc: r0 = ColorFilter()
    //     0x15eaddc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15eade0: mov             x1, x0
    // 0x15eade4: ldur            x0, [fp, #-8]
    // 0x15eade8: stur            x1, [fp, #-0x20]
    // 0x15eadec: StoreField: r1->field_7 = r0
    //     0x15eadec: stur            w0, [x1, #7]
    // 0x15eadf0: r0 = Instance_BlendMode
    //     0x15eadf0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15eadf4: ldr             x0, [x0, #0xb30]
    // 0x15eadf8: StoreField: r1->field_b = r0
    //     0x15eadf8: stur            w0, [x1, #0xb]
    // 0x15eadfc: r0 = 1
    //     0x15eadfc: movz            x0, #0x1
    // 0x15eae00: StoreField: r1->field_13 = r0
    //     0x15eae00: stur            x0, [x1, #0x13]
    // 0x15eae04: r0 = SvgPicture()
    //     0x15eae04: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15eae08: stur            x0, [fp, #-8]
    // 0x15eae0c: ldur            x16, [fp, #-0x20]
    // 0x15eae10: str             x16, [SP]
    // 0x15eae14: mov             x1, x0
    // 0x15eae18: r2 = "assets/images/appbar_arrow.svg"
    //     0x15eae18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15eae1c: ldr             x2, [x2, #0xa40]
    // 0x15eae20: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15eae20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15eae24: ldr             x4, [x4, #0xa38]
    // 0x15eae28: r0 = SvgPicture.asset()
    //     0x15eae28: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15eae2c: r0 = Align()
    //     0x15eae2c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15eae30: mov             x1, x0
    // 0x15eae34: r0 = Instance_Alignment
    //     0x15eae34: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15eae38: ldr             x0, [x0, #0xb10]
    // 0x15eae3c: StoreField: r1->field_f = r0
    //     0x15eae3c: stur            w0, [x1, #0xf]
    // 0x15eae40: r0 = 1.000000
    //     0x15eae40: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15eae44: StoreField: r1->field_13 = r0
    //     0x15eae44: stur            w0, [x1, #0x13]
    // 0x15eae48: ArrayStore: r1[0] = r0  ; List_4
    //     0x15eae48: stur            w0, [x1, #0x17]
    // 0x15eae4c: ldur            x0, [fp, #-8]
    // 0x15eae50: StoreField: r1->field_b = r0
    //     0x15eae50: stur            w0, [x1, #0xb]
    // 0x15eae54: mov             x0, x1
    // 0x15eae58: stur            x0, [fp, #-8]
    // 0x15eae5c: r0 = InkWell()
    //     0x15eae5c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15eae60: mov             x3, x0
    // 0x15eae64: ldur            x0, [fp, #-8]
    // 0x15eae68: stur            x3, [fp, #-0x20]
    // 0x15eae6c: StoreField: r3->field_b = r0
    //     0x15eae6c: stur            w0, [x3, #0xb]
    // 0x15eae70: ldur            x2, [fp, #-0x18]
    // 0x15eae74: r1 = Function '<anonymous closure>':.
    //     0x15eae74: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f00] AnonymousClosure: (0x15d3188), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15eae78: ldr             x1, [x1, #0xf00]
    // 0x15eae7c: r0 = AllocateClosure()
    //     0x15eae7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eae80: ldur            x2, [fp, #-0x20]
    // 0x15eae84: StoreField: r2->field_f = r0
    //     0x15eae84: stur            w0, [x2, #0xf]
    // 0x15eae88: r0 = true
    //     0x15eae88: add             x0, NULL, #0x20  ; true
    // 0x15eae8c: StoreField: r2->field_43 = r0
    //     0x15eae8c: stur            w0, [x2, #0x43]
    // 0x15eae90: r1 = Instance_BoxShape
    //     0x15eae90: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15eae94: ldr             x1, [x1, #0x80]
    // 0x15eae98: StoreField: r2->field_47 = r1
    //     0x15eae98: stur            w1, [x2, #0x47]
    // 0x15eae9c: StoreField: r2->field_6f = r0
    //     0x15eae9c: stur            w0, [x2, #0x6f]
    // 0x15eaea0: r1 = false
    //     0x15eaea0: add             x1, NULL, #0x30  ; false
    // 0x15eaea4: StoreField: r2->field_73 = r1
    //     0x15eaea4: stur            w1, [x2, #0x73]
    // 0x15eaea8: StoreField: r2->field_83 = r0
    //     0x15eaea8: stur            w0, [x2, #0x83]
    // 0x15eaeac: StoreField: r2->field_7b = r1
    //     0x15eaeac: stur            w1, [x2, #0x7b]
    // 0x15eaeb0: r0 = Obx()
    //     0x15eaeb0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15eaeb4: ldur            x2, [fp, #-0x18]
    // 0x15eaeb8: r1 = Function '<anonymous closure>':.
    //     0x15eaeb8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f08] AnonymousClosure: (0x15d2e94), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15eaebc: ldr             x1, [x1, #0xf08]
    // 0x15eaec0: stur            x0, [fp, #-8]
    // 0x15eaec4: r0 = AllocateClosure()
    //     0x15eaec4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15eaec8: mov             x1, x0
    // 0x15eaecc: ldur            x0, [fp, #-8]
    // 0x15eaed0: StoreField: r0->field_b = r1
    //     0x15eaed0: stur            w1, [x0, #0xb]
    // 0x15eaed4: r1 = Null
    //     0x15eaed4: mov             x1, NULL
    // 0x15eaed8: r2 = 2
    //     0x15eaed8: movz            x2, #0x2
    // 0x15eaedc: r0 = AllocateArray()
    //     0x15eaedc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15eaee0: mov             x2, x0
    // 0x15eaee4: ldur            x0, [fp, #-8]
    // 0x15eaee8: stur            x2, [fp, #-0x18]
    // 0x15eaeec: StoreField: r2->field_f = r0
    //     0x15eaeec: stur            w0, [x2, #0xf]
    // 0x15eaef0: r1 = <Widget>
    //     0x15eaef0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15eaef4: r0 = AllocateGrowableArray()
    //     0x15eaef4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15eaef8: mov             x1, x0
    // 0x15eaefc: ldur            x0, [fp, #-0x18]
    // 0x15eaf00: stur            x1, [fp, #-8]
    // 0x15eaf04: StoreField: r1->field_f = r0
    //     0x15eaf04: stur            w0, [x1, #0xf]
    // 0x15eaf08: r0 = 2
    //     0x15eaf08: movz            x0, #0x2
    // 0x15eaf0c: StoreField: r1->field_b = r0
    //     0x15eaf0c: stur            w0, [x1, #0xb]
    // 0x15eaf10: r0 = AppBar()
    //     0x15eaf10: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15eaf14: stur            x0, [fp, #-0x18]
    // 0x15eaf18: ldur            x16, [fp, #-0x10]
    // 0x15eaf1c: ldur            lr, [fp, #-8]
    // 0x15eaf20: stp             lr, x16, [SP]
    // 0x15eaf24: mov             x1, x0
    // 0x15eaf28: ldur            x2, [fp, #-0x20]
    // 0x15eaf2c: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15eaf2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15eaf30: ldr             x4, [x4, #0xa58]
    // 0x15eaf34: r0 = AppBar()
    //     0x15eaf34: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15eaf38: ldur            x0, [fp, #-0x18]
    // 0x15eaf3c: LeaveFrame
    //     0x15eaf3c: mov             SP, fp
    //     0x15eaf40: ldp             fp, lr, [SP], #0x10
    // 0x15eaf44: ret
    //     0x15eaf44: ret             
    // 0x15eaf48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eaf48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eaf4c: b               #0x15eacc0
  }
}
