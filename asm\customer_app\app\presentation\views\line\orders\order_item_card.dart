// lib: , url: package:customer_app/app/presentation/views/line/orders/order_item_card.dart

// class id: 1049537, size: 0x8
class :: {
}

// class id: 3235, size: 0x14, field offset: 0x14
class _OrderItemCardState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbf9f74, size: 0x2f0
    // 0xbf9f74: EnterFrame
    //     0xbf9f74: stp             fp, lr, [SP, #-0x10]!
    //     0xbf9f78: mov             fp, SP
    // 0xbf9f7c: AllocStack(0x48)
    //     0xbf9f7c: sub             SP, SP, #0x48
    // 0xbf9f80: SetupParameters(_OrderItemCardState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbf9f80: mov             x0, x1
    //     0xbf9f84: stur            x1, [fp, #-8]
    //     0xbf9f88: mov             x1, x2
    //     0xbf9f8c: stur            x2, [fp, #-0x10]
    // 0xbf9f90: CheckStackOverflow
    //     0xbf9f90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf9f94: cmp             SP, x16
    //     0xbf9f98: b.ls            #0xbfa254
    // 0xbf9f9c: r1 = 1
    //     0xbf9f9c: movz            x1, #0x1
    // 0xbf9fa0: r0 = AllocateContext()
    //     0xbf9fa0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf9fa4: mov             x3, x0
    // 0xbf9fa8: ldur            x0, [fp, #-8]
    // 0xbf9fac: stur            x3, [fp, #-0x18]
    // 0xbf9fb0: StoreField: r3->field_f = r0
    //     0xbf9fb0: stur            w0, [x3, #0xf]
    // 0xbf9fb4: r1 = Null
    //     0xbf9fb4: mov             x1, NULL
    // 0xbf9fb8: r2 = 4
    //     0xbf9fb8: movz            x2, #0x4
    // 0xbf9fbc: r0 = AllocateArray()
    //     0xbf9fbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf9fc0: r16 = "Ordered on "
    //     0xbf9fc0: add             x16, PP, #0x53, lsl #12  ; [pp+0x533e8] "Ordered on "
    //     0xbf9fc4: ldr             x16, [x16, #0x3e8]
    // 0xbf9fc8: StoreField: r0->field_f = r16
    //     0xbf9fc8: stur            w16, [x0, #0xf]
    // 0xbf9fcc: ldur            x1, [fp, #-8]
    // 0xbf9fd0: LoadField: r2 = r1->field_b
    //     0xbf9fd0: ldur            w2, [x1, #0xb]
    // 0xbf9fd4: DecompressPointer r2
    //     0xbf9fd4: add             x2, x2, HEAP, lsl #32
    // 0xbf9fd8: cmp             w2, NULL
    // 0xbf9fdc: b.eq            #0xbfa25c
    // 0xbf9fe0: LoadField: r3 = r2->field_b
    //     0xbf9fe0: ldur            w3, [x2, #0xb]
    // 0xbf9fe4: DecompressPointer r3
    //     0xbf9fe4: add             x3, x3, HEAP, lsl #32
    // 0xbf9fe8: LoadField: r2 = r3->field_7
    //     0xbf9fe8: ldur            w2, [x3, #7]
    // 0xbf9fec: DecompressPointer r2
    //     0xbf9fec: add             x2, x2, HEAP, lsl #32
    // 0xbf9ff0: StoreField: r0->field_13 = r2
    //     0xbf9ff0: stur            w2, [x0, #0x13]
    // 0xbf9ff4: str             x0, [SP]
    // 0xbf9ff8: r0 = _interpolate()
    //     0xbf9ff8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf9ffc: ldur            x1, [fp, #-0x10]
    // 0xbfa000: stur            x0, [fp, #-0x10]
    // 0xbfa004: r0 = of()
    //     0xbfa004: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfa008: LoadField: r1 = r0->field_87
    //     0xbfa008: ldur            w1, [x0, #0x87]
    // 0xbfa00c: DecompressPointer r1
    //     0xbfa00c: add             x1, x1, HEAP, lsl #32
    // 0xbfa010: LoadField: r0 = r1->field_2b
    //     0xbfa010: ldur            w0, [x1, #0x2b]
    // 0xbfa014: DecompressPointer r0
    //     0xbfa014: add             x0, x0, HEAP, lsl #32
    // 0xbfa018: r16 = 12.000000
    //     0xbfa018: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfa01c: ldr             x16, [x16, #0x9e8]
    // 0xbfa020: r30 = Instance_Color
    //     0xbfa020: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfa024: stp             lr, x16, [SP]
    // 0xbfa028: mov             x1, x0
    // 0xbfa02c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfa02c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfa030: ldr             x4, [x4, #0xaa0]
    // 0xbfa034: r0 = copyWith()
    //     0xbfa034: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfa038: stur            x0, [fp, #-0x20]
    // 0xbfa03c: r0 = Text()
    //     0xbfa03c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfa040: mov             x1, x0
    // 0xbfa044: ldur            x0, [fp, #-0x10]
    // 0xbfa048: stur            x1, [fp, #-0x28]
    // 0xbfa04c: StoreField: r1->field_b = r0
    //     0xbfa04c: stur            w0, [x1, #0xb]
    // 0xbfa050: ldur            x0, [fp, #-0x20]
    // 0xbfa054: StoreField: r1->field_13 = r0
    //     0xbfa054: stur            w0, [x1, #0x13]
    // 0xbfa058: r0 = Align()
    //     0xbfa058: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbfa05c: mov             x1, x0
    // 0xbfa060: r0 = Instance_Alignment
    //     0xbfa060: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbfa064: ldr             x0, [x0, #0xfa0]
    // 0xbfa068: stur            x1, [fp, #-0x10]
    // 0xbfa06c: StoreField: r1->field_f = r0
    //     0xbfa06c: stur            w0, [x1, #0xf]
    // 0xbfa070: ldur            x0, [fp, #-0x28]
    // 0xbfa074: StoreField: r1->field_b = r0
    //     0xbfa074: stur            w0, [x1, #0xb]
    // 0xbfa078: r0 = Padding()
    //     0xbfa078: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfa07c: mov             x3, x0
    // 0xbfa080: r0 = Instance_EdgeInsets
    //     0xbfa080: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xbfa084: ldr             x0, [x0, #0x868]
    // 0xbfa088: stur            x3, [fp, #-0x20]
    // 0xbfa08c: StoreField: r3->field_f = r0
    //     0xbfa08c: stur            w0, [x3, #0xf]
    // 0xbfa090: ldur            x0, [fp, #-0x10]
    // 0xbfa094: StoreField: r3->field_b = r0
    //     0xbfa094: stur            w0, [x3, #0xb]
    // 0xbfa098: ldur            x0, [fp, #-8]
    // 0xbfa09c: LoadField: r1 = r0->field_b
    //     0xbfa09c: ldur            w1, [x0, #0xb]
    // 0xbfa0a0: DecompressPointer r1
    //     0xbfa0a0: add             x1, x1, HEAP, lsl #32
    // 0xbfa0a4: cmp             w1, NULL
    // 0xbfa0a8: b.eq            #0xbfa260
    // 0xbfa0ac: LoadField: r0 = r1->field_b
    //     0xbfa0ac: ldur            w0, [x1, #0xb]
    // 0xbfa0b0: DecompressPointer r0
    //     0xbfa0b0: add             x0, x0, HEAP, lsl #32
    // 0xbfa0b4: LoadField: r1 = r0->field_b
    //     0xbfa0b4: ldur            w1, [x0, #0xb]
    // 0xbfa0b8: DecompressPointer r1
    //     0xbfa0b8: add             x1, x1, HEAP, lsl #32
    // 0xbfa0bc: cmp             w1, NULL
    // 0xbfa0c0: b.ne            #0xbfa0cc
    // 0xbfa0c4: r0 = Null
    //     0xbfa0c4: mov             x0, NULL
    // 0xbfa0c8: b               #0xbfa0d0
    // 0xbfa0cc: LoadField: r0 = r1->field_b
    //     0xbfa0cc: ldur            w0, [x1, #0xb]
    // 0xbfa0d0: cmp             w0, NULL
    // 0xbfa0d4: b.ne            #0xbfa0e0
    // 0xbfa0d8: r0 = 0
    //     0xbfa0d8: movz            x0, #0
    // 0xbfa0dc: b               #0xbfa0e8
    // 0xbfa0e0: r1 = LoadInt32Instr(r0)
    //     0xbfa0e0: sbfx            x1, x0, #1, #0x1f
    // 0xbfa0e4: mov             x0, x1
    // 0xbfa0e8: ldur            x2, [fp, #-0x18]
    // 0xbfa0ec: stur            x0, [fp, #-0x30]
    // 0xbfa0f0: r1 = Function '<anonymous closure>':.
    //     0xbfa0f0: add             x1, PP, #0x53, lsl #12  ; [pp+0x533f0] AnonymousClosure: (0xbfa264), in [package:customer_app/app/presentation/views/line/orders/order_item_card.dart] _OrderItemCardState::build (0xbf9f74)
    //     0xbfa0f4: ldr             x1, [x1, #0x3f0]
    // 0xbfa0f8: r0 = AllocateClosure()
    //     0xbfa0f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfa0fc: r1 = Function '<anonymous closure>':.
    //     0xbfa0fc: add             x1, PP, #0x53, lsl #12  ; [pp+0x533f8] AnonymousClosure: (0x9bccb4), in [package:customer_app/app/presentation/views/line/post_order/order_success/order_success_widget.dart] OrderSuccessWidget::body (0x1506c94)
    //     0xbfa100: ldr             x1, [x1, #0x3f8]
    // 0xbfa104: r2 = Null
    //     0xbfa104: mov             x2, NULL
    // 0xbfa108: stur            x0, [fp, #-8]
    // 0xbfa10c: r0 = AllocateClosure()
    //     0xbfa10c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfa110: stur            x0, [fp, #-0x10]
    // 0xbfa114: r0 = ListView()
    //     0xbfa114: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbfa118: stur            x0, [fp, #-0x18]
    // 0xbfa11c: r16 = Instance_Axis
    //     0xbfa11c: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfa120: r30 = true
    //     0xbfa120: add             lr, NULL, #0x20  ; true
    // 0xbfa124: stp             lr, x16, [SP, #8]
    // 0xbfa128: r16 = false
    //     0xbfa128: add             x16, NULL, #0x30  ; false
    // 0xbfa12c: str             x16, [SP]
    // 0xbfa130: mov             x1, x0
    // 0xbfa134: ldur            x2, [fp, #-8]
    // 0xbfa138: ldur            x3, [fp, #-0x30]
    // 0xbfa13c: ldur            x5, [fp, #-0x10]
    // 0xbfa140: r4 = const [0, 0x7, 0x3, 0x4, primary, 0x6, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0xbfa140: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e528] List(11) [0, 0x7, 0x3, 0x4, "primary", 0x6, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0xbfa144: ldr             x4, [x4, #0x528]
    // 0xbfa148: r0 = ListView.separated()
    //     0xbfa148: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbfa14c: r1 = <FlexParentData>
    //     0xbfa14c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbfa150: ldr             x1, [x1, #0xe00]
    // 0xbfa154: r0 = Flexible()
    //     0xbfa154: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbfa158: mov             x3, x0
    // 0xbfa15c: r0 = 1
    //     0xbfa15c: movz            x0, #0x1
    // 0xbfa160: stur            x3, [fp, #-8]
    // 0xbfa164: StoreField: r3->field_13 = r0
    //     0xbfa164: stur            x0, [x3, #0x13]
    // 0xbfa168: r0 = Instance_FlexFit
    //     0xbfa168: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xbfa16c: ldr             x0, [x0, #0xe20]
    // 0xbfa170: StoreField: r3->field_1b = r0
    //     0xbfa170: stur            w0, [x3, #0x1b]
    // 0xbfa174: ldur            x0, [fp, #-0x18]
    // 0xbfa178: StoreField: r3->field_b = r0
    //     0xbfa178: stur            w0, [x3, #0xb]
    // 0xbfa17c: r1 = Null
    //     0xbfa17c: mov             x1, NULL
    // 0xbfa180: r2 = 4
    //     0xbfa180: movz            x2, #0x4
    // 0xbfa184: r0 = AllocateArray()
    //     0xbfa184: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfa188: mov             x2, x0
    // 0xbfa18c: ldur            x0, [fp, #-0x20]
    // 0xbfa190: stur            x2, [fp, #-0x10]
    // 0xbfa194: StoreField: r2->field_f = r0
    //     0xbfa194: stur            w0, [x2, #0xf]
    // 0xbfa198: ldur            x0, [fp, #-8]
    // 0xbfa19c: StoreField: r2->field_13 = r0
    //     0xbfa19c: stur            w0, [x2, #0x13]
    // 0xbfa1a0: r1 = <Widget>
    //     0xbfa1a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfa1a4: r0 = AllocateGrowableArray()
    //     0xbfa1a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfa1a8: mov             x1, x0
    // 0xbfa1ac: ldur            x0, [fp, #-0x10]
    // 0xbfa1b0: stur            x1, [fp, #-8]
    // 0xbfa1b4: StoreField: r1->field_f = r0
    //     0xbfa1b4: stur            w0, [x1, #0xf]
    // 0xbfa1b8: r0 = 4
    //     0xbfa1b8: movz            x0, #0x4
    // 0xbfa1bc: StoreField: r1->field_b = r0
    //     0xbfa1bc: stur            w0, [x1, #0xb]
    // 0xbfa1c0: r0 = Column()
    //     0xbfa1c0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfa1c4: mov             x1, x0
    // 0xbfa1c8: r0 = Instance_Axis
    //     0xbfa1c8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfa1cc: stur            x1, [fp, #-0x10]
    // 0xbfa1d0: StoreField: r1->field_f = r0
    //     0xbfa1d0: stur            w0, [x1, #0xf]
    // 0xbfa1d4: r0 = Instance_MainAxisAlignment
    //     0xbfa1d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfa1d8: ldr             x0, [x0, #0xa08]
    // 0xbfa1dc: StoreField: r1->field_13 = r0
    //     0xbfa1dc: stur            w0, [x1, #0x13]
    // 0xbfa1e0: r0 = Instance_MainAxisSize
    //     0xbfa1e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbfa1e4: ldr             x0, [x0, #0xdd0]
    // 0xbfa1e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfa1e8: stur            w0, [x1, #0x17]
    // 0xbfa1ec: r0 = Instance_CrossAxisAlignment
    //     0xbfa1ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfa1f0: ldr             x0, [x0, #0xa18]
    // 0xbfa1f4: StoreField: r1->field_1b = r0
    //     0xbfa1f4: stur            w0, [x1, #0x1b]
    // 0xbfa1f8: r0 = Instance_VerticalDirection
    //     0xbfa1f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfa1fc: ldr             x0, [x0, #0xa20]
    // 0xbfa200: StoreField: r1->field_23 = r0
    //     0xbfa200: stur            w0, [x1, #0x23]
    // 0xbfa204: r0 = Instance_Clip
    //     0xbfa204: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfa208: ldr             x0, [x0, #0x38]
    // 0xbfa20c: StoreField: r1->field_2b = r0
    //     0xbfa20c: stur            w0, [x1, #0x2b]
    // 0xbfa210: StoreField: r1->field_2f = rZR
    //     0xbfa210: stur            xzr, [x1, #0x2f]
    // 0xbfa214: ldur            x0, [fp, #-8]
    // 0xbfa218: StoreField: r1->field_b = r0
    //     0xbfa218: stur            w0, [x1, #0xb]
    // 0xbfa21c: r0 = Container()
    //     0xbfa21c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfa220: stur            x0, [fp, #-8]
    // 0xbfa224: r16 = Instance_EdgeInsets
    //     0xbfa224: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0xbfa228: ldr             x16, [x16, #0xb0]
    // 0xbfa22c: ldur            lr, [fp, #-0x10]
    // 0xbfa230: stp             lr, x16, [SP]
    // 0xbfa234: mov             x1, x0
    // 0xbfa238: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, margin, 0x1, null]
    //     0xbfa238: add             x4, PP, #0x53, lsl #12  ; [pp+0x53400] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "margin", 0x1, Null]
    //     0xbfa23c: ldr             x4, [x4, #0x400]
    // 0xbfa240: r0 = Container()
    //     0xbfa240: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfa244: ldur            x0, [fp, #-8]
    // 0xbfa248: LeaveFrame
    //     0xbfa248: mov             SP, fp
    //     0xbfa24c: ldp             fp, lr, [SP], #0x10
    // 0xbfa250: ret
    //     0xbfa250: ret             
    // 0xbfa254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfa254: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfa258: b               #0xbf9f9c
    // 0xbfa25c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa25c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfa260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfa260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Column <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbfa264, size: 0x1348
    // 0xbfa264: EnterFrame
    //     0xbfa264: stp             fp, lr, [SP, #-0x10]!
    //     0xbfa268: mov             fp, SP
    // 0xbfa26c: AllocStack(0x80)
    //     0xbfa26c: sub             SP, SP, #0x80
    // 0xbfa270: SetupParameters()
    //     0xbfa270: ldr             x0, [fp, #0x20]
    //     0xbfa274: ldur            w2, [x0, #0x17]
    //     0xbfa278: add             x2, x2, HEAP, lsl #32
    //     0xbfa27c: stur            x2, [fp, #-0x48]
    // 0xbfa280: CheckStackOverflow
    //     0xbfa280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfa284: cmp             SP, x16
    //     0xbfa288: b.ls            #0xbfb560
    // 0xbfa28c: LoadField: r0 = r2->field_f
    //     0xbfa28c: ldur            w0, [x2, #0xf]
    // 0xbfa290: DecompressPointer r0
    //     0xbfa290: add             x0, x0, HEAP, lsl #32
    // 0xbfa294: LoadField: r3 = r0->field_b
    //     0xbfa294: ldur            w3, [x0, #0xb]
    // 0xbfa298: DecompressPointer r3
    //     0xbfa298: add             x3, x3, HEAP, lsl #32
    // 0xbfa29c: cmp             w3, NULL
    // 0xbfa2a0: b.eq            #0xbfb568
    // 0xbfa2a4: LoadField: r4 = r3->field_1f
    //     0xbfa2a4: ldur            w4, [x3, #0x1f]
    // 0xbfa2a8: DecompressPointer r4
    //     0xbfa2a8: add             x4, x4, HEAP, lsl #32
    // 0xbfa2ac: stur            x4, [fp, #-0x40]
    // 0xbfa2b0: LoadField: r5 = r3->field_f
    //     0xbfa2b0: ldur            w5, [x3, #0xf]
    // 0xbfa2b4: DecompressPointer r5
    //     0xbfa2b4: add             x5, x5, HEAP, lsl #32
    // 0xbfa2b8: stur            x5, [fp, #-0x38]
    // 0xbfa2bc: LoadField: r6 = r3->field_13
    //     0xbfa2bc: ldur            w6, [x3, #0x13]
    // 0xbfa2c0: DecompressPointer r6
    //     0xbfa2c0: add             x6, x6, HEAP, lsl #32
    // 0xbfa2c4: stur            x6, [fp, #-0x30]
    // 0xbfa2c8: LoadField: r0 = r3->field_b
    //     0xbfa2c8: ldur            w0, [x3, #0xb]
    // 0xbfa2cc: DecompressPointer r0
    //     0xbfa2cc: add             x0, x0, HEAP, lsl #32
    // 0xbfa2d0: LoadField: r7 = r0->field_b
    //     0xbfa2d0: ldur            w7, [x0, #0xb]
    // 0xbfa2d4: DecompressPointer r7
    //     0xbfa2d4: add             x7, x7, HEAP, lsl #32
    // 0xbfa2d8: stur            x7, [fp, #-0x28]
    // 0xbfa2dc: cmp             w7, NULL
    // 0xbfa2e0: b.eq            #0xbfb56c
    // 0xbfa2e4: LoadField: r0 = r7->field_b
    //     0xbfa2e4: ldur            w0, [x7, #0xb]
    // 0xbfa2e8: ldr             x1, [fp, #0x10]
    // 0xbfa2ec: r8 = LoadInt32Instr(r1)
    //     0xbfa2ec: sbfx            x8, x1, #1, #0x1f
    //     0xbfa2f0: tbz             w1, #0, #0xbfa2f8
    //     0xbfa2f4: ldur            x8, [x1, #7]
    // 0xbfa2f8: stur            x8, [fp, #-0x20]
    // 0xbfa2fc: r1 = LoadInt32Instr(r0)
    //     0xbfa2fc: sbfx            x1, x0, #1, #0x1f
    // 0xbfa300: mov             x0, x1
    // 0xbfa304: mov             x1, x8
    // 0xbfa308: cmp             x1, x0
    // 0xbfa30c: b.hs            #0xbfb570
    // 0xbfa310: LoadField: r0 = r7->field_f
    //     0xbfa310: ldur            w0, [x7, #0xf]
    // 0xbfa314: DecompressPointer r0
    //     0xbfa314: add             x0, x0, HEAP, lsl #32
    // 0xbfa318: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbfa318: add             x16, x0, x8, lsl #2
    //     0xbfa31c: ldur            w1, [x16, #0xf]
    // 0xbfa320: DecompressPointer r1
    //     0xbfa320: add             x1, x1, HEAP, lsl #32
    // 0xbfa324: stur            x1, [fp, #-0x18]
    // 0xbfa328: cmp             w1, NULL
    // 0xbfa32c: b.eq            #0xbfb574
    // 0xbfa330: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbfa330: ldur            w0, [x3, #0x17]
    // 0xbfa334: DecompressPointer r0
    //     0xbfa334: add             x0, x0, HEAP, lsl #32
    // 0xbfa338: stur            x0, [fp, #-0x10]
    // 0xbfa33c: LoadField: r9 = r3->field_1b
    //     0xbfa33c: ldur            w9, [x3, #0x1b]
    // 0xbfa340: DecompressPointer r9
    //     0xbfa340: add             x9, x9, HEAP, lsl #32
    // 0xbfa344: stur            x9, [fp, #-8]
    // 0xbfa348: r0 = OrderCard()
    //     0xbfa348: bl              #0xbfb5ac  ; AllocateOrderCardStub -> OrderCard (size=0x30)
    // 0xbfa34c: mov             x3, x0
    // 0xbfa350: ldur            x0, [fp, #-0x18]
    // 0xbfa354: stur            x3, [fp, #-0x50]
    // 0xbfa358: StoreField: r3->field_b = r0
    //     0xbfa358: stur            w0, [x3, #0xb]
    // 0xbfa35c: r1 = "order_card"
    //     0xbfa35c: add             x1, PP, #0x36, lsl #12  ; [pp+0x369a0] "order_card"
    //     0xbfa360: ldr             x1, [x1, #0x9a0]
    // 0xbfa364: StoreField: r3->field_f = r1
    //     0xbfa364: stur            w1, [x3, #0xf]
    // 0xbfa368: ldur            x1, [fp, #-0x38]
    // 0xbfa36c: StoreField: r3->field_13 = r1
    //     0xbfa36c: stur            w1, [x3, #0x13]
    // 0xbfa370: ldur            x1, [fp, #-0x30]
    // 0xbfa374: ArrayStore: r3[0] = r1  ; List_4
    //     0xbfa374: stur            w1, [x3, #0x17]
    // 0xbfa378: ldur            x1, [fp, #-0x10]
    // 0xbfa37c: StoreField: r3->field_1b = r1
    //     0xbfa37c: stur            w1, [x3, #0x1b]
    // 0xbfa380: ldur            x1, [fp, #-8]
    // 0xbfa384: StoreField: r3->field_1f = r1
    //     0xbfa384: stur            w1, [x3, #0x1f]
    // 0xbfa388: ldur            x2, [fp, #-0x48]
    // 0xbfa38c: r1 = Function '<anonymous closure>':.
    //     0xbfa38c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53408] AnonymousClosure: (0xbfb634), in [package:customer_app/app/presentation/views/line/orders/order_item_card.dart] _OrderItemCardState::build (0xbf9f74)
    //     0xbfa390: ldr             x1, [x1, #0x408]
    // 0xbfa394: r0 = AllocateClosure()
    //     0xbfa394: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfa398: mov             x1, x0
    // 0xbfa39c: ldur            x0, [fp, #-0x50]
    // 0xbfa3a0: StoreField: r0->field_2b = r1
    //     0xbfa3a0: stur            w1, [x0, #0x2b]
    // 0xbfa3a4: ldur            x2, [fp, #-0x48]
    // 0xbfa3a8: r1 = Function '<anonymous closure>':.
    //     0xbfa3a8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53410] AnonymousClosure: (0xbfb5b8), in [package:customer_app/app/presentation/views/line/orders/order_item_card.dart] _OrderItemCardState::build (0xbf9f74)
    //     0xbfa3ac: ldr             x1, [x1, #0x410]
    // 0xbfa3b0: r0 = AllocateClosure()
    //     0xbfa3b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbfa3b4: ldur            x2, [fp, #-0x50]
    // 0xbfa3b8: StoreField: r2->field_23 = r0
    //     0xbfa3b8: stur            w0, [x2, #0x23]
    // 0xbfa3bc: ldur            x0, [fp, #-0x40]
    // 0xbfa3c0: StoreField: r2->field_27 = r0
    //     0xbfa3c0: stur            w0, [x2, #0x27]
    // 0xbfa3c4: ldur            x0, [fp, #-0x18]
    // 0xbfa3c8: cmp             w0, NULL
    // 0xbfa3cc: b.ne            #0xbfa3d8
    // 0xbfa3d0: r0 = Null
    //     0xbfa3d0: mov             x0, NULL
    // 0xbfa3d4: b               #0xbfa3e4
    // 0xbfa3d8: LoadField: r1 = r0->field_7f
    //     0xbfa3d8: ldur            w1, [x0, #0x7f]
    // 0xbfa3dc: DecompressPointer r1
    //     0xbfa3dc: add             x1, x1, HEAP, lsl #32
    // 0xbfa3e0: mov             x0, x1
    // 0xbfa3e4: ldur            x3, [fp, #-0x28]
    // 0xbfa3e8: cmp             w0, NULL
    // 0xbfa3ec: r16 = true
    //     0xbfa3ec: add             x16, NULL, #0x20  ; true
    // 0xbfa3f0: r17 = false
    //     0xbfa3f0: add             x17, NULL, #0x30  ; false
    // 0xbfa3f4: csel            x4, x16, x17, ne
    // 0xbfa3f8: stur            x4, [fp, #-8]
    // 0xbfa3fc: cmp             w3, NULL
    // 0xbfa400: b.ne            #0xbfa410
    // 0xbfa404: ldur            x5, [fp, #-0x20]
    // 0xbfa408: r0 = Null
    //     0xbfa408: mov             x0, NULL
    // 0xbfa40c: b               #0xbfa474
    // 0xbfa410: ldur            x5, [fp, #-0x20]
    // 0xbfa414: LoadField: r0 = r3->field_b
    //     0xbfa414: ldur            w0, [x3, #0xb]
    // 0xbfa418: r1 = LoadInt32Instr(r0)
    //     0xbfa418: sbfx            x1, x0, #1, #0x1f
    // 0xbfa41c: mov             x0, x1
    // 0xbfa420: mov             x1, x5
    // 0xbfa424: cmp             x1, x0
    // 0xbfa428: b.hs            #0xbfb578
    // 0xbfa42c: LoadField: r0 = r3->field_f
    //     0xbfa42c: ldur            w0, [x3, #0xf]
    // 0xbfa430: DecompressPointer r0
    //     0xbfa430: add             x0, x0, HEAP, lsl #32
    // 0xbfa434: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbfa434: add             x16, x0, x5, lsl #2
    //     0xbfa438: ldur            w1, [x16, #0xf]
    // 0xbfa43c: DecompressPointer r1
    //     0xbfa43c: add             x1, x1, HEAP, lsl #32
    // 0xbfa440: cmp             w1, NULL
    // 0xbfa444: b.ne            #0xbfa450
    // 0xbfa448: r0 = Null
    //     0xbfa448: mov             x0, NULL
    // 0xbfa44c: b               #0xbfa474
    // 0xbfa450: LoadField: r0 = r1->field_7f
    //     0xbfa450: ldur            w0, [x1, #0x7f]
    // 0xbfa454: DecompressPointer r0
    //     0xbfa454: add             x0, x0, HEAP, lsl #32
    // 0xbfa458: cmp             w0, NULL
    // 0xbfa45c: b.ne            #0xbfa468
    // 0xbfa460: r0 = Null
    //     0xbfa460: mov             x0, NULL
    // 0xbfa464: b               #0xbfa474
    // 0xbfa468: LoadField: r1 = r0->field_2b
    //     0xbfa468: ldur            w1, [x0, #0x2b]
    // 0xbfa46c: DecompressPointer r1
    //     0xbfa46c: add             x1, x1, HEAP, lsl #32
    // 0xbfa470: mov             x0, x1
    // 0xbfa474: cmp             w0, NULL
    // 0xbfa478: b.ne            #0xbfa4e4
    // 0xbfa47c: ldur            x8, [fp, #-0x48]
    // 0xbfa480: mov             x9, x5
    // 0xbfa484: r5 = 2
    //     0xbfa484: movz            x5, #0x2
    // 0xbfa488: r7 = "Free"
    //     0xbfa488: add             x7, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbfa48c: ldr             x7, [x7, #0x668]
    // 0xbfa490: r6 = 150.000000
    //     0xbfa490: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbfa494: ldr             x6, [x6, #0x690]
    // 0xbfa498: r4 = Instance_TextOverflow
    //     0xbfa498: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbfa49c: ldr             x4, [x4, #0xe10]
    // 0xbfa4a0: r10 = Instance_CrossAxisAlignment
    //     0xbfa4a0: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfa4a4: ldr             x10, [x10, #0x890]
    // 0xbfa4a8: r11 = Instance_EdgeInsets
    //     0xbfa4a8: add             x11, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbfa4ac: ldr             x11, [x11, #0xa78]
    // 0xbfa4b0: r20 = Instance_EdgeInsets
    //     0xbfa4b0: add             x20, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbfa4b4: ldr             x20, [x20, #0x770]
    // 0xbfa4b8: r14 = 6
    //     0xbfa4b8: movz            x14, #0x6
    // 0xbfa4bc: r19 = Instance_Axis
    //     0xbfa4bc: ldr             x19, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfa4c0: r13 = Instance_FlexFit
    //     0xbfa4c0: add             x13, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbfa4c4: ldr             x13, [x13, #0xe08]
    // 0xbfa4c8: r0 = Instance_BoxShape
    //     0xbfa4c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfa4cc: ldr             x0, [x0, #0x80]
    // 0xbfa4d0: r2 = Instance_Alignment
    //     0xbfa4d0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfa4d4: ldr             x2, [x2, #0xb10]
    // 0xbfa4d8: r3 = -1
    //     0xbfa4d8: movn            x3, #0
    // 0xbfa4dc: r12 = 1
    //     0xbfa4dc: movz            x12, #0x1
    // 0xbfa4e0: b               #0xbfacb8
    // 0xbfa4e4: tbnz            w0, #4, #0xbfac54
    // 0xbfa4e8: ldur            x0, [fp, #-0x48]
    // 0xbfa4ec: r1 = Instance_Color
    //     0xbfa4ec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfa4f0: d0 = 0.070000
    //     0xbfa4f0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbfa4f4: ldr             d0, [x17, #0x5f8]
    // 0xbfa4f8: r0 = withOpacity()
    //     0xbfa4f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbfa4fc: r16 = 1.000000
    //     0xbfa4fc: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbfa500: str             x16, [SP]
    // 0xbfa504: mov             x2, x0
    // 0xbfa508: r1 = Null
    //     0xbfa508: mov             x1, NULL
    // 0xbfa50c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbfa50c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbfa510: ldr             x4, [x4, #0x108]
    // 0xbfa514: r0 = Border.all()
    //     0xbfa514: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbfa518: stur            x0, [fp, #-0x10]
    // 0xbfa51c: r0 = BoxDecoration()
    //     0xbfa51c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbfa520: mov             x2, x0
    // 0xbfa524: ldur            x0, [fp, #-0x10]
    // 0xbfa528: stur            x2, [fp, #-0x18]
    // 0xbfa52c: StoreField: r2->field_f = r0
    //     0xbfa52c: stur            w0, [x2, #0xf]
    // 0xbfa530: r0 = Instance_LinearGradient
    //     0xbfa530: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xbfa534: ldr             x0, [x0, #0x660]
    // 0xbfa538: StoreField: r2->field_1b = r0
    //     0xbfa538: stur            w0, [x2, #0x1b]
    // 0xbfa53c: r0 = Instance_BoxShape
    //     0xbfa53c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfa540: ldr             x0, [x0, #0x80]
    // 0xbfa544: StoreField: r2->field_23 = r0
    //     0xbfa544: stur            w0, [x2, #0x23]
    // 0xbfa548: ldr             x1, [fp, #0x18]
    // 0xbfa54c: r0 = of()
    //     0xbfa54c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfa550: LoadField: r1 = r0->field_87
    //     0xbfa550: ldur            w1, [x0, #0x87]
    // 0xbfa554: DecompressPointer r1
    //     0xbfa554: add             x1, x1, HEAP, lsl #32
    // 0xbfa558: LoadField: r0 = r1->field_7
    //     0xbfa558: ldur            w0, [x1, #7]
    // 0xbfa55c: DecompressPointer r0
    //     0xbfa55c: add             x0, x0, HEAP, lsl #32
    // 0xbfa560: r16 = 12.000000
    //     0xbfa560: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfa564: ldr             x16, [x16, #0x9e8]
    // 0xbfa568: r30 = Instance_Color
    //     0xbfa568: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfa56c: stp             lr, x16, [SP]
    // 0xbfa570: mov             x1, x0
    // 0xbfa574: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfa574: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfa578: ldr             x4, [x4, #0xaa0]
    // 0xbfa57c: r0 = copyWith()
    //     0xbfa57c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfa580: stur            x0, [fp, #-0x10]
    // 0xbfa584: r0 = Text()
    //     0xbfa584: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfa588: mov             x1, x0
    // 0xbfa58c: r0 = "Free"
    //     0xbfa58c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbfa590: ldr             x0, [x0, #0x668]
    // 0xbfa594: stur            x1, [fp, #-0x28]
    // 0xbfa598: StoreField: r1->field_b = r0
    //     0xbfa598: stur            w0, [x1, #0xb]
    // 0xbfa59c: ldur            x2, [fp, #-0x10]
    // 0xbfa5a0: StoreField: r1->field_13 = r2
    //     0xbfa5a0: stur            w2, [x1, #0x13]
    // 0xbfa5a4: r0 = Center()
    //     0xbfa5a4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbfa5a8: r2 = Instance_Alignment
    //     0xbfa5a8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfa5ac: ldr             x2, [x2, #0xb10]
    // 0xbfa5b0: stur            x0, [fp, #-0x10]
    // 0xbfa5b4: StoreField: r0->field_f = r2
    //     0xbfa5b4: stur            w2, [x0, #0xf]
    // 0xbfa5b8: ldur            x1, [fp, #-0x28]
    // 0xbfa5bc: StoreField: r0->field_b = r1
    //     0xbfa5bc: stur            w1, [x0, #0xb]
    // 0xbfa5c0: r0 = RotatedBox()
    //     0xbfa5c0: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbfa5c4: r3 = -1
    //     0xbfa5c4: movn            x3, #0
    // 0xbfa5c8: stur            x0, [fp, #-0x28]
    // 0xbfa5cc: StoreField: r0->field_f = r3
    //     0xbfa5cc: stur            x3, [x0, #0xf]
    // 0xbfa5d0: ldur            x1, [fp, #-0x10]
    // 0xbfa5d4: StoreField: r0->field_b = r1
    //     0xbfa5d4: stur            w1, [x0, #0xb]
    // 0xbfa5d8: r0 = Container()
    //     0xbfa5d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfa5dc: stur            x0, [fp, #-0x10]
    // 0xbfa5e0: r16 = 24.000000
    //     0xbfa5e0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbfa5e4: ldr             x16, [x16, #0xba8]
    // 0xbfa5e8: r30 = 56.000000
    //     0xbfa5e8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbfa5ec: ldr             lr, [lr, #0xb78]
    // 0xbfa5f0: stp             lr, x16, [SP, #0x10]
    // 0xbfa5f4: r16 = Instance_Color
    //     0xbfa5f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbfa5f8: ldr             x16, [x16, #0x858]
    // 0xbfa5fc: ldur            lr, [fp, #-0x28]
    // 0xbfa600: stp             lr, x16, [SP]
    // 0xbfa604: mov             x1, x0
    // 0xbfa608: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbfa608: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbfa60c: ldr             x4, [x4, #0x670]
    // 0xbfa610: r0 = Container()
    //     0xbfa610: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfa614: ldur            x2, [fp, #-0x48]
    // 0xbfa618: LoadField: r0 = r2->field_f
    //     0xbfa618: ldur            w0, [x2, #0xf]
    // 0xbfa61c: DecompressPointer r0
    //     0xbfa61c: add             x0, x0, HEAP, lsl #32
    // 0xbfa620: LoadField: r1 = r0->field_b
    //     0xbfa620: ldur            w1, [x0, #0xb]
    // 0xbfa624: DecompressPointer r1
    //     0xbfa624: add             x1, x1, HEAP, lsl #32
    // 0xbfa628: cmp             w1, NULL
    // 0xbfa62c: b.eq            #0xbfb57c
    // 0xbfa630: LoadField: r0 = r1->field_b
    //     0xbfa630: ldur            w0, [x1, #0xb]
    // 0xbfa634: DecompressPointer r0
    //     0xbfa634: add             x0, x0, HEAP, lsl #32
    // 0xbfa638: LoadField: r3 = r0->field_b
    //     0xbfa638: ldur            w3, [x0, #0xb]
    // 0xbfa63c: DecompressPointer r3
    //     0xbfa63c: add             x3, x3, HEAP, lsl #32
    // 0xbfa640: cmp             w3, NULL
    // 0xbfa644: b.ne            #0xbfa654
    // 0xbfa648: ldur            x4, [fp, #-0x20]
    // 0xbfa64c: r0 = Null
    //     0xbfa64c: mov             x0, NULL
    // 0xbfa650: b               #0xbfa6b8
    // 0xbfa654: ldur            x4, [fp, #-0x20]
    // 0xbfa658: LoadField: r0 = r3->field_b
    //     0xbfa658: ldur            w0, [x3, #0xb]
    // 0xbfa65c: r1 = LoadInt32Instr(r0)
    //     0xbfa65c: sbfx            x1, x0, #1, #0x1f
    // 0xbfa660: mov             x0, x1
    // 0xbfa664: mov             x1, x4
    // 0xbfa668: cmp             x1, x0
    // 0xbfa66c: b.hs            #0xbfb580
    // 0xbfa670: LoadField: r0 = r3->field_f
    //     0xbfa670: ldur            w0, [x3, #0xf]
    // 0xbfa674: DecompressPointer r0
    //     0xbfa674: add             x0, x0, HEAP, lsl #32
    // 0xbfa678: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbfa678: add             x16, x0, x4, lsl #2
    //     0xbfa67c: ldur            w1, [x16, #0xf]
    // 0xbfa680: DecompressPointer r1
    //     0xbfa680: add             x1, x1, HEAP, lsl #32
    // 0xbfa684: cmp             w1, NULL
    // 0xbfa688: b.ne            #0xbfa694
    // 0xbfa68c: r0 = Null
    //     0xbfa68c: mov             x0, NULL
    // 0xbfa690: b               #0xbfa6b8
    // 0xbfa694: LoadField: r0 = r1->field_7f
    //     0xbfa694: ldur            w0, [x1, #0x7f]
    // 0xbfa698: DecompressPointer r0
    //     0xbfa698: add             x0, x0, HEAP, lsl #32
    // 0xbfa69c: cmp             w0, NULL
    // 0xbfa6a0: b.ne            #0xbfa6ac
    // 0xbfa6a4: r0 = Null
    //     0xbfa6a4: mov             x0, NULL
    // 0xbfa6a8: b               #0xbfa6b8
    // 0xbfa6ac: LoadField: r1 = r0->field_7
    //     0xbfa6ac: ldur            w1, [x0, #7]
    // 0xbfa6b0: DecompressPointer r1
    //     0xbfa6b0: add             x1, x1, HEAP, lsl #32
    // 0xbfa6b4: mov             x0, x1
    // 0xbfa6b8: cmp             w0, NULL
    // 0xbfa6bc: b.ne            #0xbfa6c4
    // 0xbfa6c0: r0 = ""
    //     0xbfa6c0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfa6c4: stur            x0, [fp, #-0x28]
    // 0xbfa6c8: r0 = ImageHeaders.forImages()
    //     0xbfa6c8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbfa6cc: stur            x0, [fp, #-0x30]
    // 0xbfa6d0: r0 = CachedNetworkImage()
    //     0xbfa6d0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbfa6d4: stur            x0, [fp, #-0x38]
    // 0xbfa6d8: ldur            x16, [fp, #-0x30]
    // 0xbfa6dc: r30 = 56.000000
    //     0xbfa6dc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbfa6e0: ldr             lr, [lr, #0xb78]
    // 0xbfa6e4: stp             lr, x16, [SP, #0x10]
    // 0xbfa6e8: r16 = 56.000000
    //     0xbfa6e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbfa6ec: ldr             x16, [x16, #0xb78]
    // 0xbfa6f0: r30 = Instance_BoxFit
    //     0xbfa6f0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbfa6f4: ldr             lr, [lr, #0x118]
    // 0xbfa6f8: stp             lr, x16, [SP]
    // 0xbfa6fc: mov             x1, x0
    // 0xbfa700: ldur            x2, [fp, #-0x28]
    // 0xbfa704: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x5, height, 0x4, httpHeaders, 0x2, width, 0x3, null]
    //     0xbfa704: add             x4, PP, #0x53, lsl #12  ; [pp+0x53418] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "width", 0x3, Null]
    //     0xbfa708: ldr             x4, [x4, #0x418]
    // 0xbfa70c: r0 = CachedNetworkImage()
    //     0xbfa70c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbfa710: ldur            x2, [fp, #-0x48]
    // 0xbfa714: LoadField: r0 = r2->field_f
    //     0xbfa714: ldur            w0, [x2, #0xf]
    // 0xbfa718: DecompressPointer r0
    //     0xbfa718: add             x0, x0, HEAP, lsl #32
    // 0xbfa71c: LoadField: r1 = r0->field_b
    //     0xbfa71c: ldur            w1, [x0, #0xb]
    // 0xbfa720: DecompressPointer r1
    //     0xbfa720: add             x1, x1, HEAP, lsl #32
    // 0xbfa724: cmp             w1, NULL
    // 0xbfa728: b.eq            #0xbfb584
    // 0xbfa72c: LoadField: r0 = r1->field_b
    //     0xbfa72c: ldur            w0, [x1, #0xb]
    // 0xbfa730: DecompressPointer r0
    //     0xbfa730: add             x0, x0, HEAP, lsl #32
    // 0xbfa734: LoadField: r3 = r0->field_b
    //     0xbfa734: ldur            w3, [x0, #0xb]
    // 0xbfa738: DecompressPointer r3
    //     0xbfa738: add             x3, x3, HEAP, lsl #32
    // 0xbfa73c: cmp             w3, NULL
    // 0xbfa740: b.ne            #0xbfa750
    // 0xbfa744: ldur            x4, [fp, #-0x20]
    // 0xbfa748: r0 = Null
    //     0xbfa748: mov             x0, NULL
    // 0xbfa74c: b               #0xbfa7b4
    // 0xbfa750: ldur            x4, [fp, #-0x20]
    // 0xbfa754: LoadField: r0 = r3->field_b
    //     0xbfa754: ldur            w0, [x3, #0xb]
    // 0xbfa758: r1 = LoadInt32Instr(r0)
    //     0xbfa758: sbfx            x1, x0, #1, #0x1f
    // 0xbfa75c: mov             x0, x1
    // 0xbfa760: mov             x1, x4
    // 0xbfa764: cmp             x1, x0
    // 0xbfa768: b.hs            #0xbfb588
    // 0xbfa76c: LoadField: r0 = r3->field_f
    //     0xbfa76c: ldur            w0, [x3, #0xf]
    // 0xbfa770: DecompressPointer r0
    //     0xbfa770: add             x0, x0, HEAP, lsl #32
    // 0xbfa774: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbfa774: add             x16, x0, x4, lsl #2
    //     0xbfa778: ldur            w1, [x16, #0xf]
    // 0xbfa77c: DecompressPointer r1
    //     0xbfa77c: add             x1, x1, HEAP, lsl #32
    // 0xbfa780: cmp             w1, NULL
    // 0xbfa784: b.ne            #0xbfa790
    // 0xbfa788: r0 = Null
    //     0xbfa788: mov             x0, NULL
    // 0xbfa78c: b               #0xbfa7b4
    // 0xbfa790: LoadField: r0 = r1->field_7f
    //     0xbfa790: ldur            w0, [x1, #0x7f]
    // 0xbfa794: DecompressPointer r0
    //     0xbfa794: add             x0, x0, HEAP, lsl #32
    // 0xbfa798: cmp             w0, NULL
    // 0xbfa79c: b.ne            #0xbfa7a8
    // 0xbfa7a0: r0 = Null
    //     0xbfa7a0: mov             x0, NULL
    // 0xbfa7a4: b               #0xbfa7b4
    // 0xbfa7a8: LoadField: r1 = r0->field_b
    //     0xbfa7a8: ldur            w1, [x0, #0xb]
    // 0xbfa7ac: DecompressPointer r1
    //     0xbfa7ac: add             x1, x1, HEAP, lsl #32
    // 0xbfa7b0: mov             x0, x1
    // 0xbfa7b4: cmp             w0, NULL
    // 0xbfa7b8: b.ne            #0xbfa7c0
    // 0xbfa7bc: r0 = ""
    //     0xbfa7bc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfa7c0: ldr             x1, [fp, #0x18]
    // 0xbfa7c4: stur            x0, [fp, #-0x28]
    // 0xbfa7c8: r0 = of()
    //     0xbfa7c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfa7cc: LoadField: r1 = r0->field_87
    //     0xbfa7cc: ldur            w1, [x0, #0x87]
    // 0xbfa7d0: DecompressPointer r1
    //     0xbfa7d0: add             x1, x1, HEAP, lsl #32
    // 0xbfa7d4: LoadField: r0 = r1->field_7
    //     0xbfa7d4: ldur            w0, [x1, #7]
    // 0xbfa7d8: DecompressPointer r0
    //     0xbfa7d8: add             x0, x0, HEAP, lsl #32
    // 0xbfa7dc: r16 = 12.000000
    //     0xbfa7dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfa7e0: ldr             x16, [x16, #0x9e8]
    // 0xbfa7e4: r30 = Instance_Color
    //     0xbfa7e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfa7e8: stp             lr, x16, [SP]
    // 0xbfa7ec: mov             x1, x0
    // 0xbfa7f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfa7f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfa7f4: ldr             x4, [x4, #0xaa0]
    // 0xbfa7f8: r0 = copyWith()
    //     0xbfa7f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfa7fc: stur            x0, [fp, #-0x30]
    // 0xbfa800: r0 = Text()
    //     0xbfa800: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfa804: mov             x1, x0
    // 0xbfa808: ldur            x0, [fp, #-0x28]
    // 0xbfa80c: stur            x1, [fp, #-0x40]
    // 0xbfa810: StoreField: r1->field_b = r0
    //     0xbfa810: stur            w0, [x1, #0xb]
    // 0xbfa814: ldur            x0, [fp, #-0x30]
    // 0xbfa818: StoreField: r1->field_13 = r0
    //     0xbfa818: stur            w0, [x1, #0x13]
    // 0xbfa81c: r4 = Instance_TextOverflow
    //     0xbfa81c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbfa820: ldr             x4, [x4, #0xe10]
    // 0xbfa824: StoreField: r1->field_2b = r4
    //     0xbfa824: stur            w4, [x1, #0x2b]
    // 0xbfa828: r5 = 2
    //     0xbfa828: movz            x5, #0x2
    // 0xbfa82c: StoreField: r1->field_37 = r5
    //     0xbfa82c: stur            w5, [x1, #0x37]
    // 0xbfa830: r0 = SizedBox()
    //     0xbfa830: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfa834: r6 = 150.000000
    //     0xbfa834: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbfa838: ldr             x6, [x6, #0x690]
    // 0xbfa83c: stur            x0, [fp, #-0x28]
    // 0xbfa840: StoreField: r0->field_f = r6
    //     0xbfa840: stur            w6, [x0, #0xf]
    // 0xbfa844: ldur            x1, [fp, #-0x40]
    // 0xbfa848: StoreField: r0->field_b = r1
    //     0xbfa848: stur            w1, [x0, #0xb]
    // 0xbfa84c: ldr             x1, [fp, #0x18]
    // 0xbfa850: r0 = of()
    //     0xbfa850: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfa854: LoadField: r1 = r0->field_87
    //     0xbfa854: ldur            w1, [x0, #0x87]
    // 0xbfa858: DecompressPointer r1
    //     0xbfa858: add             x1, x1, HEAP, lsl #32
    // 0xbfa85c: LoadField: r0 = r1->field_2b
    //     0xbfa85c: ldur            w0, [x1, #0x2b]
    // 0xbfa860: DecompressPointer r0
    //     0xbfa860: add             x0, x0, HEAP, lsl #32
    // 0xbfa864: r16 = 12.000000
    //     0xbfa864: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfa868: ldr             x16, [x16, #0x9e8]
    // 0xbfa86c: r30 = Instance_Color
    //     0xbfa86c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbfa870: ldr             lr, [lr, #0x858]
    // 0xbfa874: stp             lr, x16, [SP]
    // 0xbfa878: mov             x1, x0
    // 0xbfa87c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfa87c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfa880: ldr             x4, [x4, #0xaa0]
    // 0xbfa884: r0 = copyWith()
    //     0xbfa884: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfa888: stur            x0, [fp, #-0x30]
    // 0xbfa88c: r0 = Text()
    //     0xbfa88c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfa890: mov             x2, x0
    // 0xbfa894: r7 = "Free"
    //     0xbfa894: add             x7, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbfa898: ldr             x7, [x7, #0x668]
    // 0xbfa89c: stur            x2, [fp, #-0x40]
    // 0xbfa8a0: StoreField: r2->field_b = r7
    //     0xbfa8a0: stur            w7, [x2, #0xb]
    // 0xbfa8a4: ldur            x0, [fp, #-0x30]
    // 0xbfa8a8: StoreField: r2->field_13 = r0
    //     0xbfa8a8: stur            w0, [x2, #0x13]
    // 0xbfa8ac: ldur            x8, [fp, #-0x48]
    // 0xbfa8b0: LoadField: r0 = r8->field_f
    //     0xbfa8b0: ldur            w0, [x8, #0xf]
    // 0xbfa8b4: DecompressPointer r0
    //     0xbfa8b4: add             x0, x0, HEAP, lsl #32
    // 0xbfa8b8: LoadField: r1 = r0->field_b
    //     0xbfa8b8: ldur            w1, [x0, #0xb]
    // 0xbfa8bc: DecompressPointer r1
    //     0xbfa8bc: add             x1, x1, HEAP, lsl #32
    // 0xbfa8c0: cmp             w1, NULL
    // 0xbfa8c4: b.eq            #0xbfb58c
    // 0xbfa8c8: LoadField: r0 = r1->field_b
    //     0xbfa8c8: ldur            w0, [x1, #0xb]
    // 0xbfa8cc: DecompressPointer r0
    //     0xbfa8cc: add             x0, x0, HEAP, lsl #32
    // 0xbfa8d0: LoadField: r3 = r0->field_b
    //     0xbfa8d0: ldur            w3, [x0, #0xb]
    // 0xbfa8d4: DecompressPointer r3
    //     0xbfa8d4: add             x3, x3, HEAP, lsl #32
    // 0xbfa8d8: cmp             w3, NULL
    // 0xbfa8dc: b.ne            #0xbfa8e8
    // 0xbfa8e0: r0 = Null
    //     0xbfa8e0: mov             x0, NULL
    // 0xbfa8e4: b               #0xbfa94c
    // 0xbfa8e8: ldur            x9, [fp, #-0x20]
    // 0xbfa8ec: LoadField: r0 = r3->field_b
    //     0xbfa8ec: ldur            w0, [x3, #0xb]
    // 0xbfa8f0: r1 = LoadInt32Instr(r0)
    //     0xbfa8f0: sbfx            x1, x0, #1, #0x1f
    // 0xbfa8f4: mov             x0, x1
    // 0xbfa8f8: mov             x1, x9
    // 0xbfa8fc: cmp             x1, x0
    // 0xbfa900: b.hs            #0xbfb590
    // 0xbfa904: LoadField: r0 = r3->field_f
    //     0xbfa904: ldur            w0, [x3, #0xf]
    // 0xbfa908: DecompressPointer r0
    //     0xbfa908: add             x0, x0, HEAP, lsl #32
    // 0xbfa90c: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xbfa90c: add             x16, x0, x9, lsl #2
    //     0xbfa910: ldur            w1, [x16, #0xf]
    // 0xbfa914: DecompressPointer r1
    //     0xbfa914: add             x1, x1, HEAP, lsl #32
    // 0xbfa918: cmp             w1, NULL
    // 0xbfa91c: b.ne            #0xbfa928
    // 0xbfa920: r0 = Null
    //     0xbfa920: mov             x0, NULL
    // 0xbfa924: b               #0xbfa94c
    // 0xbfa928: LoadField: r0 = r1->field_7f
    //     0xbfa928: ldur            w0, [x1, #0x7f]
    // 0xbfa92c: DecompressPointer r0
    //     0xbfa92c: add             x0, x0, HEAP, lsl #32
    // 0xbfa930: cmp             w0, NULL
    // 0xbfa934: b.ne            #0xbfa940
    // 0xbfa938: r0 = Null
    //     0xbfa938: mov             x0, NULL
    // 0xbfa93c: b               #0xbfa94c
    // 0xbfa940: LoadField: r1 = r0->field_13
    //     0xbfa940: ldur            w1, [x0, #0x13]
    // 0xbfa944: DecompressPointer r1
    //     0xbfa944: add             x1, x1, HEAP, lsl #32
    // 0xbfa948: mov             x0, x1
    // 0xbfa94c: cmp             w0, NULL
    // 0xbfa950: b.ne            #0xbfa95c
    // 0xbfa954: r5 = ""
    //     0xbfa954: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfa958: b               #0xbfa960
    // 0xbfa95c: mov             x5, x0
    // 0xbfa960: ldur            x4, [fp, #-0x10]
    // 0xbfa964: ldur            x3, [fp, #-0x38]
    // 0xbfa968: ldur            x0, [fp, #-0x28]
    // 0xbfa96c: ldr             x1, [fp, #0x18]
    // 0xbfa970: stur            x5, [fp, #-0x30]
    // 0xbfa974: r0 = of()
    //     0xbfa974: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfa978: LoadField: r1 = r0->field_87
    //     0xbfa978: ldur            w1, [x0, #0x87]
    // 0xbfa97c: DecompressPointer r1
    //     0xbfa97c: add             x1, x1, HEAP, lsl #32
    // 0xbfa980: LoadField: r0 = r1->field_2b
    //     0xbfa980: ldur            w0, [x1, #0x2b]
    // 0xbfa984: DecompressPointer r0
    //     0xbfa984: add             x0, x0, HEAP, lsl #32
    // 0xbfa988: r16 = 12.000000
    //     0xbfa988: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfa98c: ldr             x16, [x16, #0x9e8]
    // 0xbfa990: r30 = Instance_TextDecoration
    //     0xbfa990: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbfa994: ldr             lr, [lr, #0xe30]
    // 0xbfa998: stp             lr, x16, [SP]
    // 0xbfa99c: mov             x1, x0
    // 0xbfa9a0: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbfa9a0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbfa9a4: ldr             x4, [x4, #0x698]
    // 0xbfa9a8: r0 = copyWith()
    //     0xbfa9a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfa9ac: stur            x0, [fp, #-0x58]
    // 0xbfa9b0: r0 = Text()
    //     0xbfa9b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfa9b4: mov             x3, x0
    // 0xbfa9b8: ldur            x0, [fp, #-0x30]
    // 0xbfa9bc: stur            x3, [fp, #-0x60]
    // 0xbfa9c0: StoreField: r3->field_b = r0
    //     0xbfa9c0: stur            w0, [x3, #0xb]
    // 0xbfa9c4: ldur            x0, [fp, #-0x58]
    // 0xbfa9c8: StoreField: r3->field_13 = r0
    //     0xbfa9c8: stur            w0, [x3, #0x13]
    // 0xbfa9cc: r1 = Null
    //     0xbfa9cc: mov             x1, NULL
    // 0xbfa9d0: r2 = 6
    //     0xbfa9d0: movz            x2, #0x6
    // 0xbfa9d4: r0 = AllocateArray()
    //     0xbfa9d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfa9d8: mov             x2, x0
    // 0xbfa9dc: ldur            x0, [fp, #-0x40]
    // 0xbfa9e0: stur            x2, [fp, #-0x30]
    // 0xbfa9e4: StoreField: r2->field_f = r0
    //     0xbfa9e4: stur            w0, [x2, #0xf]
    // 0xbfa9e8: r16 = Instance_SizedBox
    //     0xbfa9e8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbfa9ec: ldr             x16, [x16, #0xa50]
    // 0xbfa9f0: StoreField: r2->field_13 = r16
    //     0xbfa9f0: stur            w16, [x2, #0x13]
    // 0xbfa9f4: ldur            x0, [fp, #-0x60]
    // 0xbfa9f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfa9f8: stur            w0, [x2, #0x17]
    // 0xbfa9fc: r1 = <Widget>
    //     0xbfa9fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfaa00: r0 = AllocateGrowableArray()
    //     0xbfaa00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfaa04: mov             x1, x0
    // 0xbfaa08: ldur            x0, [fp, #-0x30]
    // 0xbfaa0c: stur            x1, [fp, #-0x40]
    // 0xbfaa10: StoreField: r1->field_f = r0
    //     0xbfaa10: stur            w0, [x1, #0xf]
    // 0xbfaa14: r2 = 6
    //     0xbfaa14: movz            x2, #0x6
    // 0xbfaa18: StoreField: r1->field_b = r2
    //     0xbfaa18: stur            w2, [x1, #0xb]
    // 0xbfaa1c: r0 = Row()
    //     0xbfaa1c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfaa20: mov             x3, x0
    // 0xbfaa24: r0 = Instance_Axis
    //     0xbfaa24: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfaa28: stur            x3, [fp, #-0x30]
    // 0xbfaa2c: StoreField: r3->field_f = r0
    //     0xbfaa2c: stur            w0, [x3, #0xf]
    // 0xbfaa30: r4 = Instance_MainAxisAlignment
    //     0xbfaa30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfaa34: ldr             x4, [x4, #0xa08]
    // 0xbfaa38: StoreField: r3->field_13 = r4
    //     0xbfaa38: stur            w4, [x3, #0x13]
    // 0xbfaa3c: r5 = Instance_MainAxisSize
    //     0xbfaa3c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfaa40: ldr             x5, [x5, #0xa10]
    // 0xbfaa44: ArrayStore: r3[0] = r5  ; List_4
    //     0xbfaa44: stur            w5, [x3, #0x17]
    // 0xbfaa48: r6 = Instance_CrossAxisAlignment
    //     0xbfaa48: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfaa4c: ldr             x6, [x6, #0xa18]
    // 0xbfaa50: StoreField: r3->field_1b = r6
    //     0xbfaa50: stur            w6, [x3, #0x1b]
    // 0xbfaa54: r7 = Instance_VerticalDirection
    //     0xbfaa54: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfaa58: ldr             x7, [x7, #0xa20]
    // 0xbfaa5c: StoreField: r3->field_23 = r7
    //     0xbfaa5c: stur            w7, [x3, #0x23]
    // 0xbfaa60: r8 = Instance_Clip
    //     0xbfaa60: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfaa64: ldr             x8, [x8, #0x38]
    // 0xbfaa68: StoreField: r3->field_2b = r8
    //     0xbfaa68: stur            w8, [x3, #0x2b]
    // 0xbfaa6c: StoreField: r3->field_2f = rZR
    //     0xbfaa6c: stur            xzr, [x3, #0x2f]
    // 0xbfaa70: ldur            x1, [fp, #-0x40]
    // 0xbfaa74: StoreField: r3->field_b = r1
    //     0xbfaa74: stur            w1, [x3, #0xb]
    // 0xbfaa78: r1 = Null
    //     0xbfaa78: mov             x1, NULL
    // 0xbfaa7c: r2 = 6
    //     0xbfaa7c: movz            x2, #0x6
    // 0xbfaa80: r0 = AllocateArray()
    //     0xbfaa80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfaa84: mov             x2, x0
    // 0xbfaa88: ldur            x0, [fp, #-0x28]
    // 0xbfaa8c: stur            x2, [fp, #-0x40]
    // 0xbfaa90: StoreField: r2->field_f = r0
    //     0xbfaa90: stur            w0, [x2, #0xf]
    // 0xbfaa94: r16 = Instance_SizedBox
    //     0xbfaa94: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbfaa98: ldr             x16, [x16, #0xc70]
    // 0xbfaa9c: StoreField: r2->field_13 = r16
    //     0xbfaa9c: stur            w16, [x2, #0x13]
    // 0xbfaaa0: ldur            x0, [fp, #-0x30]
    // 0xbfaaa4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfaaa4: stur            w0, [x2, #0x17]
    // 0xbfaaa8: r1 = <Widget>
    //     0xbfaaa8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfaaac: r0 = AllocateGrowableArray()
    //     0xbfaaac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfaab0: mov             x1, x0
    // 0xbfaab4: ldur            x0, [fp, #-0x40]
    // 0xbfaab8: stur            x1, [fp, #-0x28]
    // 0xbfaabc: StoreField: r1->field_f = r0
    //     0xbfaabc: stur            w0, [x1, #0xf]
    // 0xbfaac0: r2 = 6
    //     0xbfaac0: movz            x2, #0x6
    // 0xbfaac4: StoreField: r1->field_b = r2
    //     0xbfaac4: stur            w2, [x1, #0xb]
    // 0xbfaac8: r0 = Column()
    //     0xbfaac8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfaacc: mov             x1, x0
    // 0xbfaad0: r0 = Instance_Axis
    //     0xbfaad0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfaad4: stur            x1, [fp, #-0x30]
    // 0xbfaad8: StoreField: r1->field_f = r0
    //     0xbfaad8: stur            w0, [x1, #0xf]
    // 0xbfaadc: r2 = Instance_MainAxisAlignment
    //     0xbfaadc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfaae0: ldr             x2, [x2, #0xa08]
    // 0xbfaae4: StoreField: r1->field_13 = r2
    //     0xbfaae4: stur            w2, [x1, #0x13]
    // 0xbfaae8: r3 = Instance_MainAxisSize
    //     0xbfaae8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfaaec: ldr             x3, [x3, #0xa10]
    // 0xbfaaf0: ArrayStore: r1[0] = r3  ; List_4
    //     0xbfaaf0: stur            w3, [x1, #0x17]
    // 0xbfaaf4: r10 = Instance_CrossAxisAlignment
    //     0xbfaaf4: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfaaf8: ldr             x10, [x10, #0x890]
    // 0xbfaafc: StoreField: r1->field_1b = r10
    //     0xbfaafc: stur            w10, [x1, #0x1b]
    // 0xbfab00: r4 = Instance_VerticalDirection
    //     0xbfab00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfab04: ldr             x4, [x4, #0xa20]
    // 0xbfab08: StoreField: r1->field_23 = r4
    //     0xbfab08: stur            w4, [x1, #0x23]
    // 0xbfab0c: r5 = Instance_Clip
    //     0xbfab0c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfab10: ldr             x5, [x5, #0x38]
    // 0xbfab14: StoreField: r1->field_2b = r5
    //     0xbfab14: stur            w5, [x1, #0x2b]
    // 0xbfab18: StoreField: r1->field_2f = rZR
    //     0xbfab18: stur            xzr, [x1, #0x2f]
    // 0xbfab1c: ldur            x6, [fp, #-0x28]
    // 0xbfab20: StoreField: r1->field_b = r6
    //     0xbfab20: stur            w6, [x1, #0xb]
    // 0xbfab24: r0 = Padding()
    //     0xbfab24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfab28: r11 = Instance_EdgeInsets
    //     0xbfab28: add             x11, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbfab2c: ldr             x11, [x11, #0xa78]
    // 0xbfab30: stur            x0, [fp, #-0x28]
    // 0xbfab34: StoreField: r0->field_f = r11
    //     0xbfab34: stur            w11, [x0, #0xf]
    // 0xbfab38: ldur            x1, [fp, #-0x30]
    // 0xbfab3c: StoreField: r0->field_b = r1
    //     0xbfab3c: stur            w1, [x0, #0xb]
    // 0xbfab40: r1 = <FlexParentData>
    //     0xbfab40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbfab44: ldr             x1, [x1, #0xe00]
    // 0xbfab48: r0 = Expanded()
    //     0xbfab48: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbfab4c: r12 = 1
    //     0xbfab4c: movz            x12, #0x1
    // 0xbfab50: stur            x0, [fp, #-0x30]
    // 0xbfab54: StoreField: r0->field_13 = r12
    //     0xbfab54: stur            x12, [x0, #0x13]
    // 0xbfab58: r13 = Instance_FlexFit
    //     0xbfab58: add             x13, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbfab5c: ldr             x13, [x13, #0xe08]
    // 0xbfab60: StoreField: r0->field_1b = r13
    //     0xbfab60: stur            w13, [x0, #0x1b]
    // 0xbfab64: ldur            x1, [fp, #-0x28]
    // 0xbfab68: StoreField: r0->field_b = r1
    //     0xbfab68: stur            w1, [x0, #0xb]
    // 0xbfab6c: r1 = Null
    //     0xbfab6c: mov             x1, NULL
    // 0xbfab70: r2 = 6
    //     0xbfab70: movz            x2, #0x6
    // 0xbfab74: r0 = AllocateArray()
    //     0xbfab74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfab78: mov             x2, x0
    // 0xbfab7c: ldur            x0, [fp, #-0x10]
    // 0xbfab80: stur            x2, [fp, #-0x28]
    // 0xbfab84: StoreField: r2->field_f = r0
    //     0xbfab84: stur            w0, [x2, #0xf]
    // 0xbfab88: ldur            x0, [fp, #-0x38]
    // 0xbfab8c: StoreField: r2->field_13 = r0
    //     0xbfab8c: stur            w0, [x2, #0x13]
    // 0xbfab90: ldur            x0, [fp, #-0x30]
    // 0xbfab94: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfab94: stur            w0, [x2, #0x17]
    // 0xbfab98: r1 = <Widget>
    //     0xbfab98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfab9c: r0 = AllocateGrowableArray()
    //     0xbfab9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfaba0: mov             x1, x0
    // 0xbfaba4: ldur            x0, [fp, #-0x28]
    // 0xbfaba8: stur            x1, [fp, #-0x10]
    // 0xbfabac: StoreField: r1->field_f = r0
    //     0xbfabac: stur            w0, [x1, #0xf]
    // 0xbfabb0: r14 = 6
    //     0xbfabb0: movz            x14, #0x6
    // 0xbfabb4: StoreField: r1->field_b = r14
    //     0xbfabb4: stur            w14, [x1, #0xb]
    // 0xbfabb8: r0 = Row()
    //     0xbfabb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfabbc: r19 = Instance_Axis
    //     0xbfabbc: ldr             x19, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfabc0: stur            x0, [fp, #-0x28]
    // 0xbfabc4: StoreField: r0->field_f = r19
    //     0xbfabc4: stur            w19, [x0, #0xf]
    // 0xbfabc8: r1 = Instance_MainAxisAlignment
    //     0xbfabc8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfabcc: ldr             x1, [x1, #0xa08]
    // 0xbfabd0: StoreField: r0->field_13 = r1
    //     0xbfabd0: stur            w1, [x0, #0x13]
    // 0xbfabd4: r2 = Instance_MainAxisSize
    //     0xbfabd4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfabd8: ldr             x2, [x2, #0xa10]
    // 0xbfabdc: ArrayStore: r0[0] = r2  ; List_4
    //     0xbfabdc: stur            w2, [x0, #0x17]
    // 0xbfabe0: r3 = Instance_CrossAxisAlignment
    //     0xbfabe0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfabe4: ldr             x3, [x3, #0xa18]
    // 0xbfabe8: StoreField: r0->field_1b = r3
    //     0xbfabe8: stur            w3, [x0, #0x1b]
    // 0xbfabec: r4 = Instance_VerticalDirection
    //     0xbfabec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfabf0: ldr             x4, [x4, #0xa20]
    // 0xbfabf4: StoreField: r0->field_23 = r4
    //     0xbfabf4: stur            w4, [x0, #0x23]
    // 0xbfabf8: r5 = Instance_Clip
    //     0xbfabf8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfabfc: ldr             x5, [x5, #0x38]
    // 0xbfac00: StoreField: r0->field_2b = r5
    //     0xbfac00: stur            w5, [x0, #0x2b]
    // 0xbfac04: StoreField: r0->field_2f = rZR
    //     0xbfac04: stur            xzr, [x0, #0x2f]
    // 0xbfac08: ldur            x6, [fp, #-0x10]
    // 0xbfac0c: StoreField: r0->field_b = r6
    //     0xbfac0c: stur            w6, [x0, #0xb]
    // 0xbfac10: r0 = Container()
    //     0xbfac10: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfac14: stur            x0, [fp, #-0x10]
    // 0xbfac18: ldur            x16, [fp, #-0x18]
    // 0xbfac1c: ldur            lr, [fp, #-0x28]
    // 0xbfac20: stp             lr, x16, [SP]
    // 0xbfac24: mov             x1, x0
    // 0xbfac28: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbfac28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbfac2c: ldr             x4, [x4, #0x88]
    // 0xbfac30: r0 = Container()
    //     0xbfac30: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfac34: r0 = Padding()
    //     0xbfac34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfac38: r20 = Instance_EdgeInsets
    //     0xbfac38: add             x20, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbfac3c: ldr             x20, [x20, #0x770]
    // 0xbfac40: StoreField: r0->field_f = r20
    //     0xbfac40: stur            w20, [x0, #0xf]
    // 0xbfac44: ldur            x1, [fp, #-0x10]
    // 0xbfac48: StoreField: r0->field_b = r1
    //     0xbfac48: stur            w1, [x0, #0xb]
    // 0xbfac4c: mov             x2, x0
    // 0xbfac50: b               #0xbfb470
    // 0xbfac54: ldur            x8, [fp, #-0x48]
    // 0xbfac58: mov             x9, x5
    // 0xbfac5c: r5 = 2
    //     0xbfac5c: movz            x5, #0x2
    // 0xbfac60: r7 = "Free"
    //     0xbfac60: add             x7, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbfac64: ldr             x7, [x7, #0x668]
    // 0xbfac68: r6 = 150.000000
    //     0xbfac68: add             x6, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbfac6c: ldr             x6, [x6, #0x690]
    // 0xbfac70: r4 = Instance_TextOverflow
    //     0xbfac70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbfac74: ldr             x4, [x4, #0xe10]
    // 0xbfac78: r10 = Instance_CrossAxisAlignment
    //     0xbfac78: add             x10, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfac7c: ldr             x10, [x10, #0x890]
    // 0xbfac80: r11 = Instance_EdgeInsets
    //     0xbfac80: add             x11, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbfac84: ldr             x11, [x11, #0xa78]
    // 0xbfac88: r20 = Instance_EdgeInsets
    //     0xbfac88: add             x20, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbfac8c: ldr             x20, [x20, #0x770]
    // 0xbfac90: r14 = 6
    //     0xbfac90: movz            x14, #0x6
    // 0xbfac94: r19 = Instance_Axis
    //     0xbfac94: ldr             x19, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfac98: r13 = Instance_FlexFit
    //     0xbfac98: add             x13, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbfac9c: ldr             x13, [x13, #0xe08]
    // 0xbfaca0: r0 = Instance_BoxShape
    //     0xbfaca0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfaca4: ldr             x0, [x0, #0x80]
    // 0xbfaca8: r2 = Instance_Alignment
    //     0xbfaca8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfacac: ldr             x2, [x2, #0xb10]
    // 0xbfacb0: r3 = -1
    //     0xbfacb0: movn            x3, #0
    // 0xbfacb4: r12 = 1
    //     0xbfacb4: movz            x12, #0x1
    // 0xbfacb8: ldr             x1, [fp, #0x18]
    // 0xbfacbc: r0 = of()
    //     0xbfacbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfacc0: LoadField: r1 = r0->field_5b
    //     0xbfacc0: ldur            w1, [x0, #0x5b]
    // 0xbfacc4: DecompressPointer r1
    //     0xbfacc4: add             x1, x1, HEAP, lsl #32
    // 0xbfacc8: r0 = LoadClassIdInstr(r1)
    //     0xbfacc8: ldur            x0, [x1, #-1]
    //     0xbfaccc: ubfx            x0, x0, #0xc, #0x14
    // 0xbfacd0: d0 = 0.070000
    //     0xbfacd0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbfacd4: ldr             d0, [x17, #0x5f8]
    // 0xbfacd8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbfacd8: sub             lr, x0, #0xffa
    //     0xbfacdc: ldr             lr, [x21, lr, lsl #3]
    //     0xbface0: blr             lr
    // 0xbface4: r16 = 1.000000
    //     0xbface4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbface8: str             x16, [SP]
    // 0xbfacec: mov             x2, x0
    // 0xbfacf0: r1 = Null
    //     0xbfacf0: mov             x1, NULL
    // 0xbfacf4: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbfacf4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbfacf8: ldr             x4, [x4, #0x108]
    // 0xbfacfc: r0 = Border.all()
    //     0xbfacfc: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbfad00: stur            x0, [fp, #-0x10]
    // 0xbfad04: r0 = BoxDecoration()
    //     0xbfad04: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbfad08: mov             x2, x0
    // 0xbfad0c: ldur            x0, [fp, #-0x10]
    // 0xbfad10: stur            x2, [fp, #-0x18]
    // 0xbfad14: StoreField: r2->field_f = r0
    //     0xbfad14: stur            w0, [x2, #0xf]
    // 0xbfad18: r0 = Instance_BoxShape
    //     0xbfad18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbfad1c: ldr             x0, [x0, #0x80]
    // 0xbfad20: StoreField: r2->field_23 = r0
    //     0xbfad20: stur            w0, [x2, #0x23]
    // 0xbfad24: ldr             x1, [fp, #0x18]
    // 0xbfad28: r0 = of()
    //     0xbfad28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfad2c: LoadField: r1 = r0->field_5b
    //     0xbfad2c: ldur            w1, [x0, #0x5b]
    // 0xbfad30: DecompressPointer r1
    //     0xbfad30: add             x1, x1, HEAP, lsl #32
    // 0xbfad34: r0 = LoadClassIdInstr(r1)
    //     0xbfad34: ldur            x0, [x1, #-1]
    //     0xbfad38: ubfx            x0, x0, #0xc, #0x14
    // 0xbfad3c: d0 = 0.400000
    //     0xbfad3c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbfad40: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbfad40: sub             lr, x0, #0xffa
    //     0xbfad44: ldr             lr, [x21, lr, lsl #3]
    //     0xbfad48: blr             lr
    // 0xbfad4c: ldr             x1, [fp, #0x18]
    // 0xbfad50: stur            x0, [fp, #-0x10]
    // 0xbfad54: r0 = of()
    //     0xbfad54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfad58: LoadField: r1 = r0->field_87
    //     0xbfad58: ldur            w1, [x0, #0x87]
    // 0xbfad5c: DecompressPointer r1
    //     0xbfad5c: add             x1, x1, HEAP, lsl #32
    // 0xbfad60: LoadField: r0 = r1->field_7
    //     0xbfad60: ldur            w0, [x1, #7]
    // 0xbfad64: DecompressPointer r0
    //     0xbfad64: add             x0, x0, HEAP, lsl #32
    // 0xbfad68: r16 = 12.000000
    //     0xbfad68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfad6c: ldr             x16, [x16, #0x9e8]
    // 0xbfad70: r30 = Instance_Color
    //     0xbfad70: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbfad74: stp             lr, x16, [SP]
    // 0xbfad78: mov             x1, x0
    // 0xbfad7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfad7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfad80: ldr             x4, [x4, #0xaa0]
    // 0xbfad84: r0 = copyWith()
    //     0xbfad84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfad88: stur            x0, [fp, #-0x28]
    // 0xbfad8c: r0 = Text()
    //     0xbfad8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfad90: mov             x1, x0
    // 0xbfad94: r0 = "Free"
    //     0xbfad94: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbfad98: ldr             x0, [x0, #0x668]
    // 0xbfad9c: stur            x1, [fp, #-0x30]
    // 0xbfada0: StoreField: r1->field_b = r0
    //     0xbfada0: stur            w0, [x1, #0xb]
    // 0xbfada4: ldur            x2, [fp, #-0x28]
    // 0xbfada8: StoreField: r1->field_13 = r2
    //     0xbfada8: stur            w2, [x1, #0x13]
    // 0xbfadac: r0 = Center()
    //     0xbfadac: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbfadb0: mov             x1, x0
    // 0xbfadb4: r0 = Instance_Alignment
    //     0xbfadb4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbfadb8: ldr             x0, [x0, #0xb10]
    // 0xbfadbc: stur            x1, [fp, #-0x28]
    // 0xbfadc0: StoreField: r1->field_f = r0
    //     0xbfadc0: stur            w0, [x1, #0xf]
    // 0xbfadc4: ldur            x0, [fp, #-0x30]
    // 0xbfadc8: StoreField: r1->field_b = r0
    //     0xbfadc8: stur            w0, [x1, #0xb]
    // 0xbfadcc: r0 = RotatedBox()
    //     0xbfadcc: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbfadd0: mov             x1, x0
    // 0xbfadd4: r0 = -1
    //     0xbfadd4: movn            x0, #0
    // 0xbfadd8: stur            x1, [fp, #-0x30]
    // 0xbfaddc: StoreField: r1->field_f = r0
    //     0xbfaddc: stur            x0, [x1, #0xf]
    // 0xbfade0: ldur            x0, [fp, #-0x28]
    // 0xbfade4: StoreField: r1->field_b = r0
    //     0xbfade4: stur            w0, [x1, #0xb]
    // 0xbfade8: r0 = Container()
    //     0xbfade8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfadec: stur            x0, [fp, #-0x28]
    // 0xbfadf0: r16 = 24.000000
    //     0xbfadf0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbfadf4: ldr             x16, [x16, #0xba8]
    // 0xbfadf8: r30 = 56.000000
    //     0xbfadf8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbfadfc: ldr             lr, [lr, #0xb78]
    // 0xbfae00: stp             lr, x16, [SP, #0x10]
    // 0xbfae04: ldur            x16, [fp, #-0x10]
    // 0xbfae08: ldur            lr, [fp, #-0x30]
    // 0xbfae0c: stp             lr, x16, [SP]
    // 0xbfae10: mov             x1, x0
    // 0xbfae14: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbfae14: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbfae18: ldr             x4, [x4, #0x670]
    // 0xbfae1c: r0 = Container()
    //     0xbfae1c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfae20: r0 = ImageHeaders.forImages()
    //     0xbfae20: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbfae24: mov             x3, x0
    // 0xbfae28: ldur            x2, [fp, #-0x48]
    // 0xbfae2c: stur            x3, [fp, #-0x30]
    // 0xbfae30: LoadField: r0 = r2->field_f
    //     0xbfae30: ldur            w0, [x2, #0xf]
    // 0xbfae34: DecompressPointer r0
    //     0xbfae34: add             x0, x0, HEAP, lsl #32
    // 0xbfae38: LoadField: r1 = r0->field_b
    //     0xbfae38: ldur            w1, [x0, #0xb]
    // 0xbfae3c: DecompressPointer r1
    //     0xbfae3c: add             x1, x1, HEAP, lsl #32
    // 0xbfae40: cmp             w1, NULL
    // 0xbfae44: b.eq            #0xbfb594
    // 0xbfae48: LoadField: r0 = r1->field_b
    //     0xbfae48: ldur            w0, [x1, #0xb]
    // 0xbfae4c: DecompressPointer r0
    //     0xbfae4c: add             x0, x0, HEAP, lsl #32
    // 0xbfae50: LoadField: r4 = r0->field_b
    //     0xbfae50: ldur            w4, [x0, #0xb]
    // 0xbfae54: DecompressPointer r4
    //     0xbfae54: add             x4, x4, HEAP, lsl #32
    // 0xbfae58: cmp             w4, NULL
    // 0xbfae5c: b.ne            #0xbfae6c
    // 0xbfae60: ldur            x5, [fp, #-0x20]
    // 0xbfae64: r0 = Null
    //     0xbfae64: mov             x0, NULL
    // 0xbfae68: b               #0xbfaed0
    // 0xbfae6c: ldur            x5, [fp, #-0x20]
    // 0xbfae70: LoadField: r0 = r4->field_b
    //     0xbfae70: ldur            w0, [x4, #0xb]
    // 0xbfae74: r1 = LoadInt32Instr(r0)
    //     0xbfae74: sbfx            x1, x0, #1, #0x1f
    // 0xbfae78: mov             x0, x1
    // 0xbfae7c: mov             x1, x5
    // 0xbfae80: cmp             x1, x0
    // 0xbfae84: b.hs            #0xbfb598
    // 0xbfae88: LoadField: r0 = r4->field_f
    //     0xbfae88: ldur            w0, [x4, #0xf]
    // 0xbfae8c: DecompressPointer r0
    //     0xbfae8c: add             x0, x0, HEAP, lsl #32
    // 0xbfae90: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbfae90: add             x16, x0, x5, lsl #2
    //     0xbfae94: ldur            w1, [x16, #0xf]
    // 0xbfae98: DecompressPointer r1
    //     0xbfae98: add             x1, x1, HEAP, lsl #32
    // 0xbfae9c: cmp             w1, NULL
    // 0xbfaea0: b.ne            #0xbfaeac
    // 0xbfaea4: r0 = Null
    //     0xbfaea4: mov             x0, NULL
    // 0xbfaea8: b               #0xbfaed0
    // 0xbfaeac: LoadField: r0 = r1->field_7f
    //     0xbfaeac: ldur            w0, [x1, #0x7f]
    // 0xbfaeb0: DecompressPointer r0
    //     0xbfaeb0: add             x0, x0, HEAP, lsl #32
    // 0xbfaeb4: cmp             w0, NULL
    // 0xbfaeb8: b.ne            #0xbfaec4
    // 0xbfaebc: r0 = Null
    //     0xbfaebc: mov             x0, NULL
    // 0xbfaec0: b               #0xbfaed0
    // 0xbfaec4: LoadField: r1 = r0->field_7
    //     0xbfaec4: ldur            w1, [x0, #7]
    // 0xbfaec8: DecompressPointer r1
    //     0xbfaec8: add             x1, x1, HEAP, lsl #32
    // 0xbfaecc: mov             x0, x1
    // 0xbfaed0: cmp             w0, NULL
    // 0xbfaed4: b.ne            #0xbfaedc
    // 0xbfaed8: r0 = ""
    //     0xbfaed8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfaedc: stur            x0, [fp, #-0x10]
    // 0xbfaee0: r0 = CachedNetworkImage()
    //     0xbfaee0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbfaee4: stur            x0, [fp, #-0x38]
    // 0xbfaee8: ldur            x16, [fp, #-0x30]
    // 0xbfaeec: r30 = 56.000000
    //     0xbfaeec: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbfaef0: ldr             lr, [lr, #0xb78]
    // 0xbfaef4: stp             lr, x16, [SP, #0x10]
    // 0xbfaef8: r16 = 56.000000
    //     0xbfaef8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbfaefc: ldr             x16, [x16, #0xb78]
    // 0xbfaf00: r30 = Instance_BoxFit
    //     0xbfaf00: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbfaf04: ldr             lr, [lr, #0x118]
    // 0xbfaf08: stp             lr, x16, [SP]
    // 0xbfaf0c: mov             x1, x0
    // 0xbfaf10: ldur            x2, [fp, #-0x10]
    // 0xbfaf14: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x5, height, 0x4, httpHeaders, 0x2, width, 0x3, null]
    //     0xbfaf14: add             x4, PP, #0x53, lsl #12  ; [pp+0x53418] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "width", 0x3, Null]
    //     0xbfaf18: ldr             x4, [x4, #0x418]
    // 0xbfaf1c: r0 = CachedNetworkImage()
    //     0xbfaf1c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbfaf20: ldur            x2, [fp, #-0x48]
    // 0xbfaf24: LoadField: r0 = r2->field_f
    //     0xbfaf24: ldur            w0, [x2, #0xf]
    // 0xbfaf28: DecompressPointer r0
    //     0xbfaf28: add             x0, x0, HEAP, lsl #32
    // 0xbfaf2c: LoadField: r1 = r0->field_b
    //     0xbfaf2c: ldur            w1, [x0, #0xb]
    // 0xbfaf30: DecompressPointer r1
    //     0xbfaf30: add             x1, x1, HEAP, lsl #32
    // 0xbfaf34: cmp             w1, NULL
    // 0xbfaf38: b.eq            #0xbfb59c
    // 0xbfaf3c: LoadField: r0 = r1->field_b
    //     0xbfaf3c: ldur            w0, [x1, #0xb]
    // 0xbfaf40: DecompressPointer r0
    //     0xbfaf40: add             x0, x0, HEAP, lsl #32
    // 0xbfaf44: LoadField: r3 = r0->field_b
    //     0xbfaf44: ldur            w3, [x0, #0xb]
    // 0xbfaf48: DecompressPointer r3
    //     0xbfaf48: add             x3, x3, HEAP, lsl #32
    // 0xbfaf4c: cmp             w3, NULL
    // 0xbfaf50: b.ne            #0xbfaf60
    // 0xbfaf54: ldur            x4, [fp, #-0x20]
    // 0xbfaf58: r0 = Null
    //     0xbfaf58: mov             x0, NULL
    // 0xbfaf5c: b               #0xbfafc4
    // 0xbfaf60: ldur            x4, [fp, #-0x20]
    // 0xbfaf64: LoadField: r0 = r3->field_b
    //     0xbfaf64: ldur            w0, [x3, #0xb]
    // 0xbfaf68: r1 = LoadInt32Instr(r0)
    //     0xbfaf68: sbfx            x1, x0, #1, #0x1f
    // 0xbfaf6c: mov             x0, x1
    // 0xbfaf70: mov             x1, x4
    // 0xbfaf74: cmp             x1, x0
    // 0xbfaf78: b.hs            #0xbfb5a0
    // 0xbfaf7c: LoadField: r0 = r3->field_f
    //     0xbfaf7c: ldur            w0, [x3, #0xf]
    // 0xbfaf80: DecompressPointer r0
    //     0xbfaf80: add             x0, x0, HEAP, lsl #32
    // 0xbfaf84: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbfaf84: add             x16, x0, x4, lsl #2
    //     0xbfaf88: ldur            w1, [x16, #0xf]
    // 0xbfaf8c: DecompressPointer r1
    //     0xbfaf8c: add             x1, x1, HEAP, lsl #32
    // 0xbfaf90: cmp             w1, NULL
    // 0xbfaf94: b.ne            #0xbfafa0
    // 0xbfaf98: r0 = Null
    //     0xbfaf98: mov             x0, NULL
    // 0xbfaf9c: b               #0xbfafc4
    // 0xbfafa0: LoadField: r0 = r1->field_7f
    //     0xbfafa0: ldur            w0, [x1, #0x7f]
    // 0xbfafa4: DecompressPointer r0
    //     0xbfafa4: add             x0, x0, HEAP, lsl #32
    // 0xbfafa8: cmp             w0, NULL
    // 0xbfafac: b.ne            #0xbfafb8
    // 0xbfafb0: r0 = Null
    //     0xbfafb0: mov             x0, NULL
    // 0xbfafb4: b               #0xbfafc4
    // 0xbfafb8: LoadField: r1 = r0->field_b
    //     0xbfafb8: ldur            w1, [x0, #0xb]
    // 0xbfafbc: DecompressPointer r1
    //     0xbfafbc: add             x1, x1, HEAP, lsl #32
    // 0xbfafc0: mov             x0, x1
    // 0xbfafc4: cmp             w0, NULL
    // 0xbfafc8: b.ne            #0xbfafd0
    // 0xbfafcc: r0 = ""
    //     0xbfafcc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfafd0: ldr             x1, [fp, #0x18]
    // 0xbfafd4: stur            x0, [fp, #-0x10]
    // 0xbfafd8: r0 = of()
    //     0xbfafd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfafdc: LoadField: r1 = r0->field_87
    //     0xbfafdc: ldur            w1, [x0, #0x87]
    // 0xbfafe0: DecompressPointer r1
    //     0xbfafe0: add             x1, x1, HEAP, lsl #32
    // 0xbfafe4: LoadField: r0 = r1->field_7
    //     0xbfafe4: ldur            w0, [x1, #7]
    // 0xbfafe8: DecompressPointer r0
    //     0xbfafe8: add             x0, x0, HEAP, lsl #32
    // 0xbfafec: r16 = 12.000000
    //     0xbfafec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfaff0: ldr             x16, [x16, #0x9e8]
    // 0xbfaff4: r30 = Instance_Color
    //     0xbfaff4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfaff8: stp             lr, x16, [SP]
    // 0xbfaffc: mov             x1, x0
    // 0xbfb000: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfb000: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfb004: ldr             x4, [x4, #0xaa0]
    // 0xbfb008: r0 = copyWith()
    //     0xbfb008: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfb00c: stur            x0, [fp, #-0x30]
    // 0xbfb010: r0 = Text()
    //     0xbfb010: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfb014: mov             x1, x0
    // 0xbfb018: ldur            x0, [fp, #-0x10]
    // 0xbfb01c: stur            x1, [fp, #-0x40]
    // 0xbfb020: StoreField: r1->field_b = r0
    //     0xbfb020: stur            w0, [x1, #0xb]
    // 0xbfb024: ldur            x0, [fp, #-0x30]
    // 0xbfb028: StoreField: r1->field_13 = r0
    //     0xbfb028: stur            w0, [x1, #0x13]
    // 0xbfb02c: r0 = Instance_TextOverflow
    //     0xbfb02c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbfb030: ldr             x0, [x0, #0xe10]
    // 0xbfb034: StoreField: r1->field_2b = r0
    //     0xbfb034: stur            w0, [x1, #0x2b]
    // 0xbfb038: r0 = 2
    //     0xbfb038: movz            x0, #0x2
    // 0xbfb03c: StoreField: r1->field_37 = r0
    //     0xbfb03c: stur            w0, [x1, #0x37]
    // 0xbfb040: r0 = SizedBox()
    //     0xbfb040: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbfb044: mov             x2, x0
    // 0xbfb048: r0 = 150.000000
    //     0xbfb048: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbfb04c: ldr             x0, [x0, #0x690]
    // 0xbfb050: stur            x2, [fp, #-0x10]
    // 0xbfb054: StoreField: r2->field_f = r0
    //     0xbfb054: stur            w0, [x2, #0xf]
    // 0xbfb058: ldur            x0, [fp, #-0x40]
    // 0xbfb05c: StoreField: r2->field_b = r0
    //     0xbfb05c: stur            w0, [x2, #0xb]
    // 0xbfb060: ldr             x1, [fp, #0x18]
    // 0xbfb064: r0 = of()
    //     0xbfb064: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfb068: LoadField: r1 = r0->field_87
    //     0xbfb068: ldur            w1, [x0, #0x87]
    // 0xbfb06c: DecompressPointer r1
    //     0xbfb06c: add             x1, x1, HEAP, lsl #32
    // 0xbfb070: LoadField: r0 = r1->field_2b
    //     0xbfb070: ldur            w0, [x1, #0x2b]
    // 0xbfb074: DecompressPointer r0
    //     0xbfb074: add             x0, x0, HEAP, lsl #32
    // 0xbfb078: r16 = 12.000000
    //     0xbfb078: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfb07c: ldr             x16, [x16, #0x9e8]
    // 0xbfb080: r30 = Instance_Color
    //     0xbfb080: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbfb084: stp             lr, x16, [SP]
    // 0xbfb088: mov             x1, x0
    // 0xbfb08c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbfb08c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbfb090: ldr             x4, [x4, #0xaa0]
    // 0xbfb094: r0 = copyWith()
    //     0xbfb094: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfb098: stur            x0, [fp, #-0x30]
    // 0xbfb09c: r0 = Text()
    //     0xbfb09c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfb0a0: mov             x2, x0
    // 0xbfb0a4: r0 = "Free"
    //     0xbfb0a4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbfb0a8: ldr             x0, [x0, #0x668]
    // 0xbfb0ac: stur            x2, [fp, #-0x40]
    // 0xbfb0b0: StoreField: r2->field_b = r0
    //     0xbfb0b0: stur            w0, [x2, #0xb]
    // 0xbfb0b4: ldur            x0, [fp, #-0x30]
    // 0xbfb0b8: StoreField: r2->field_13 = r0
    //     0xbfb0b8: stur            w0, [x2, #0x13]
    // 0xbfb0bc: ldur            x0, [fp, #-0x48]
    // 0xbfb0c0: LoadField: r1 = r0->field_f
    //     0xbfb0c0: ldur            w1, [x0, #0xf]
    // 0xbfb0c4: DecompressPointer r1
    //     0xbfb0c4: add             x1, x1, HEAP, lsl #32
    // 0xbfb0c8: LoadField: r0 = r1->field_b
    //     0xbfb0c8: ldur            w0, [x1, #0xb]
    // 0xbfb0cc: DecompressPointer r0
    //     0xbfb0cc: add             x0, x0, HEAP, lsl #32
    // 0xbfb0d0: cmp             w0, NULL
    // 0xbfb0d4: b.eq            #0xbfb5a4
    // 0xbfb0d8: LoadField: r1 = r0->field_b
    //     0xbfb0d8: ldur            w1, [x0, #0xb]
    // 0xbfb0dc: DecompressPointer r1
    //     0xbfb0dc: add             x1, x1, HEAP, lsl #32
    // 0xbfb0e0: LoadField: r3 = r1->field_b
    //     0xbfb0e0: ldur            w3, [x1, #0xb]
    // 0xbfb0e4: DecompressPointer r3
    //     0xbfb0e4: add             x3, x3, HEAP, lsl #32
    // 0xbfb0e8: cmp             w3, NULL
    // 0xbfb0ec: b.ne            #0xbfb0f8
    // 0xbfb0f0: r0 = Null
    //     0xbfb0f0: mov             x0, NULL
    // 0xbfb0f4: b               #0xbfb15c
    // 0xbfb0f8: ldur            x4, [fp, #-0x20]
    // 0xbfb0fc: LoadField: r0 = r3->field_b
    //     0xbfb0fc: ldur            w0, [x3, #0xb]
    // 0xbfb100: r1 = LoadInt32Instr(r0)
    //     0xbfb100: sbfx            x1, x0, #1, #0x1f
    // 0xbfb104: mov             x0, x1
    // 0xbfb108: mov             x1, x4
    // 0xbfb10c: cmp             x1, x0
    // 0xbfb110: b.hs            #0xbfb5a8
    // 0xbfb114: LoadField: r0 = r3->field_f
    //     0xbfb114: ldur            w0, [x3, #0xf]
    // 0xbfb118: DecompressPointer r0
    //     0xbfb118: add             x0, x0, HEAP, lsl #32
    // 0xbfb11c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbfb11c: add             x16, x0, x4, lsl #2
    //     0xbfb120: ldur            w1, [x16, #0xf]
    // 0xbfb124: DecompressPointer r1
    //     0xbfb124: add             x1, x1, HEAP, lsl #32
    // 0xbfb128: cmp             w1, NULL
    // 0xbfb12c: b.ne            #0xbfb138
    // 0xbfb130: r0 = Null
    //     0xbfb130: mov             x0, NULL
    // 0xbfb134: b               #0xbfb15c
    // 0xbfb138: LoadField: r0 = r1->field_7f
    //     0xbfb138: ldur            w0, [x1, #0x7f]
    // 0xbfb13c: DecompressPointer r0
    //     0xbfb13c: add             x0, x0, HEAP, lsl #32
    // 0xbfb140: cmp             w0, NULL
    // 0xbfb144: b.ne            #0xbfb150
    // 0xbfb148: r0 = Null
    //     0xbfb148: mov             x0, NULL
    // 0xbfb14c: b               #0xbfb15c
    // 0xbfb150: LoadField: r1 = r0->field_13
    //     0xbfb150: ldur            w1, [x0, #0x13]
    // 0xbfb154: DecompressPointer r1
    //     0xbfb154: add             x1, x1, HEAP, lsl #32
    // 0xbfb158: mov             x0, x1
    // 0xbfb15c: cmp             w0, NULL
    // 0xbfb160: b.ne            #0xbfb16c
    // 0xbfb164: r5 = ""
    //     0xbfb164: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbfb168: b               #0xbfb170
    // 0xbfb16c: mov             x5, x0
    // 0xbfb170: ldur            x4, [fp, #-0x28]
    // 0xbfb174: ldur            x3, [fp, #-0x38]
    // 0xbfb178: ldur            x0, [fp, #-0x10]
    // 0xbfb17c: ldr             x1, [fp, #0x18]
    // 0xbfb180: stur            x5, [fp, #-0x30]
    // 0xbfb184: r0 = of()
    //     0xbfb184: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbfb188: LoadField: r1 = r0->field_87
    //     0xbfb188: ldur            w1, [x0, #0x87]
    // 0xbfb18c: DecompressPointer r1
    //     0xbfb18c: add             x1, x1, HEAP, lsl #32
    // 0xbfb190: LoadField: r0 = r1->field_2b
    //     0xbfb190: ldur            w0, [x1, #0x2b]
    // 0xbfb194: DecompressPointer r0
    //     0xbfb194: add             x0, x0, HEAP, lsl #32
    // 0xbfb198: r16 = 12.000000
    //     0xbfb198: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbfb19c: ldr             x16, [x16, #0x9e8]
    // 0xbfb1a0: r30 = Instance_TextDecoration
    //     0xbfb1a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbfb1a4: ldr             lr, [lr, #0xe30]
    // 0xbfb1a8: stp             lr, x16, [SP]
    // 0xbfb1ac: mov             x1, x0
    // 0xbfb1b0: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbfb1b0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbfb1b4: ldr             x4, [x4, #0x698]
    // 0xbfb1b8: r0 = copyWith()
    //     0xbfb1b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbfb1bc: stur            x0, [fp, #-0x48]
    // 0xbfb1c0: r0 = Text()
    //     0xbfb1c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbfb1c4: mov             x3, x0
    // 0xbfb1c8: ldur            x0, [fp, #-0x30]
    // 0xbfb1cc: stur            x3, [fp, #-0x58]
    // 0xbfb1d0: StoreField: r3->field_b = r0
    //     0xbfb1d0: stur            w0, [x3, #0xb]
    // 0xbfb1d4: ldur            x0, [fp, #-0x48]
    // 0xbfb1d8: StoreField: r3->field_13 = r0
    //     0xbfb1d8: stur            w0, [x3, #0x13]
    // 0xbfb1dc: r1 = Null
    //     0xbfb1dc: mov             x1, NULL
    // 0xbfb1e0: r2 = 6
    //     0xbfb1e0: movz            x2, #0x6
    // 0xbfb1e4: r0 = AllocateArray()
    //     0xbfb1e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfb1e8: mov             x2, x0
    // 0xbfb1ec: ldur            x0, [fp, #-0x40]
    // 0xbfb1f0: stur            x2, [fp, #-0x30]
    // 0xbfb1f4: StoreField: r2->field_f = r0
    //     0xbfb1f4: stur            w0, [x2, #0xf]
    // 0xbfb1f8: r16 = Instance_SizedBox
    //     0xbfb1f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbfb1fc: ldr             x16, [x16, #0xa50]
    // 0xbfb200: StoreField: r2->field_13 = r16
    //     0xbfb200: stur            w16, [x2, #0x13]
    // 0xbfb204: ldur            x0, [fp, #-0x58]
    // 0xbfb208: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfb208: stur            w0, [x2, #0x17]
    // 0xbfb20c: r1 = <Widget>
    //     0xbfb20c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfb210: r0 = AllocateGrowableArray()
    //     0xbfb210: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfb214: mov             x1, x0
    // 0xbfb218: ldur            x0, [fp, #-0x30]
    // 0xbfb21c: stur            x1, [fp, #-0x40]
    // 0xbfb220: StoreField: r1->field_f = r0
    //     0xbfb220: stur            w0, [x1, #0xf]
    // 0xbfb224: r2 = 6
    //     0xbfb224: movz            x2, #0x6
    // 0xbfb228: StoreField: r1->field_b = r2
    //     0xbfb228: stur            w2, [x1, #0xb]
    // 0xbfb22c: r0 = Row()
    //     0xbfb22c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfb230: mov             x3, x0
    // 0xbfb234: r0 = Instance_Axis
    //     0xbfb234: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfb238: stur            x3, [fp, #-0x30]
    // 0xbfb23c: StoreField: r3->field_f = r0
    //     0xbfb23c: stur            w0, [x3, #0xf]
    // 0xbfb240: r4 = Instance_MainAxisAlignment
    //     0xbfb240: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfb244: ldr             x4, [x4, #0xa08]
    // 0xbfb248: StoreField: r3->field_13 = r4
    //     0xbfb248: stur            w4, [x3, #0x13]
    // 0xbfb24c: r5 = Instance_MainAxisSize
    //     0xbfb24c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfb250: ldr             x5, [x5, #0xa10]
    // 0xbfb254: ArrayStore: r3[0] = r5  ; List_4
    //     0xbfb254: stur            w5, [x3, #0x17]
    // 0xbfb258: r6 = Instance_CrossAxisAlignment
    //     0xbfb258: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfb25c: ldr             x6, [x6, #0xa18]
    // 0xbfb260: StoreField: r3->field_1b = r6
    //     0xbfb260: stur            w6, [x3, #0x1b]
    // 0xbfb264: r7 = Instance_VerticalDirection
    //     0xbfb264: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfb268: ldr             x7, [x7, #0xa20]
    // 0xbfb26c: StoreField: r3->field_23 = r7
    //     0xbfb26c: stur            w7, [x3, #0x23]
    // 0xbfb270: r8 = Instance_Clip
    //     0xbfb270: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfb274: ldr             x8, [x8, #0x38]
    // 0xbfb278: StoreField: r3->field_2b = r8
    //     0xbfb278: stur            w8, [x3, #0x2b]
    // 0xbfb27c: StoreField: r3->field_2f = rZR
    //     0xbfb27c: stur            xzr, [x3, #0x2f]
    // 0xbfb280: ldur            x1, [fp, #-0x40]
    // 0xbfb284: StoreField: r3->field_b = r1
    //     0xbfb284: stur            w1, [x3, #0xb]
    // 0xbfb288: r1 = Null
    //     0xbfb288: mov             x1, NULL
    // 0xbfb28c: r2 = 6
    //     0xbfb28c: movz            x2, #0x6
    // 0xbfb290: r0 = AllocateArray()
    //     0xbfb290: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfb294: mov             x2, x0
    // 0xbfb298: ldur            x0, [fp, #-0x10]
    // 0xbfb29c: stur            x2, [fp, #-0x40]
    // 0xbfb2a0: StoreField: r2->field_f = r0
    //     0xbfb2a0: stur            w0, [x2, #0xf]
    // 0xbfb2a4: r16 = Instance_SizedBox
    //     0xbfb2a4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbfb2a8: ldr             x16, [x16, #0xc70]
    // 0xbfb2ac: StoreField: r2->field_13 = r16
    //     0xbfb2ac: stur            w16, [x2, #0x13]
    // 0xbfb2b0: ldur            x0, [fp, #-0x30]
    // 0xbfb2b4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfb2b4: stur            w0, [x2, #0x17]
    // 0xbfb2b8: r1 = <Widget>
    //     0xbfb2b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfb2bc: r0 = AllocateGrowableArray()
    //     0xbfb2bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfb2c0: mov             x1, x0
    // 0xbfb2c4: ldur            x0, [fp, #-0x40]
    // 0xbfb2c8: stur            x1, [fp, #-0x10]
    // 0xbfb2cc: StoreField: r1->field_f = r0
    //     0xbfb2cc: stur            w0, [x1, #0xf]
    // 0xbfb2d0: r2 = 6
    //     0xbfb2d0: movz            x2, #0x6
    // 0xbfb2d4: StoreField: r1->field_b = r2
    //     0xbfb2d4: stur            w2, [x1, #0xb]
    // 0xbfb2d8: r0 = Column()
    //     0xbfb2d8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfb2dc: mov             x1, x0
    // 0xbfb2e0: r0 = Instance_Axis
    //     0xbfb2e0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfb2e4: stur            x1, [fp, #-0x30]
    // 0xbfb2e8: StoreField: r1->field_f = r0
    //     0xbfb2e8: stur            w0, [x1, #0xf]
    // 0xbfb2ec: r2 = Instance_MainAxisAlignment
    //     0xbfb2ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfb2f0: ldr             x2, [x2, #0xa08]
    // 0xbfb2f4: StoreField: r1->field_13 = r2
    //     0xbfb2f4: stur            w2, [x1, #0x13]
    // 0xbfb2f8: r3 = Instance_MainAxisSize
    //     0xbfb2f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfb2fc: ldr             x3, [x3, #0xa10]
    // 0xbfb300: ArrayStore: r1[0] = r3  ; List_4
    //     0xbfb300: stur            w3, [x1, #0x17]
    // 0xbfb304: r4 = Instance_CrossAxisAlignment
    //     0xbfb304: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbfb308: ldr             x4, [x4, #0x890]
    // 0xbfb30c: StoreField: r1->field_1b = r4
    //     0xbfb30c: stur            w4, [x1, #0x1b]
    // 0xbfb310: r4 = Instance_VerticalDirection
    //     0xbfb310: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfb314: ldr             x4, [x4, #0xa20]
    // 0xbfb318: StoreField: r1->field_23 = r4
    //     0xbfb318: stur            w4, [x1, #0x23]
    // 0xbfb31c: r5 = Instance_Clip
    //     0xbfb31c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfb320: ldr             x5, [x5, #0x38]
    // 0xbfb324: StoreField: r1->field_2b = r5
    //     0xbfb324: stur            w5, [x1, #0x2b]
    // 0xbfb328: StoreField: r1->field_2f = rZR
    //     0xbfb328: stur            xzr, [x1, #0x2f]
    // 0xbfb32c: ldur            x6, [fp, #-0x10]
    // 0xbfb330: StoreField: r1->field_b = r6
    //     0xbfb330: stur            w6, [x1, #0xb]
    // 0xbfb334: r0 = Padding()
    //     0xbfb334: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfb338: mov             x2, x0
    // 0xbfb33c: r0 = Instance_EdgeInsets
    //     0xbfb33c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbfb340: ldr             x0, [x0, #0xa78]
    // 0xbfb344: stur            x2, [fp, #-0x10]
    // 0xbfb348: StoreField: r2->field_f = r0
    //     0xbfb348: stur            w0, [x2, #0xf]
    // 0xbfb34c: ldur            x0, [fp, #-0x30]
    // 0xbfb350: StoreField: r2->field_b = r0
    //     0xbfb350: stur            w0, [x2, #0xb]
    // 0xbfb354: r1 = <FlexParentData>
    //     0xbfb354: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbfb358: ldr             x1, [x1, #0xe00]
    // 0xbfb35c: r0 = Expanded()
    //     0xbfb35c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbfb360: mov             x3, x0
    // 0xbfb364: r0 = 1
    //     0xbfb364: movz            x0, #0x1
    // 0xbfb368: stur            x3, [fp, #-0x30]
    // 0xbfb36c: StoreField: r3->field_13 = r0
    //     0xbfb36c: stur            x0, [x3, #0x13]
    // 0xbfb370: r0 = Instance_FlexFit
    //     0xbfb370: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbfb374: ldr             x0, [x0, #0xe08]
    // 0xbfb378: StoreField: r3->field_1b = r0
    //     0xbfb378: stur            w0, [x3, #0x1b]
    // 0xbfb37c: ldur            x0, [fp, #-0x10]
    // 0xbfb380: StoreField: r3->field_b = r0
    //     0xbfb380: stur            w0, [x3, #0xb]
    // 0xbfb384: r1 = Null
    //     0xbfb384: mov             x1, NULL
    // 0xbfb388: r2 = 6
    //     0xbfb388: movz            x2, #0x6
    // 0xbfb38c: r0 = AllocateArray()
    //     0xbfb38c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfb390: mov             x2, x0
    // 0xbfb394: ldur            x0, [fp, #-0x28]
    // 0xbfb398: stur            x2, [fp, #-0x10]
    // 0xbfb39c: StoreField: r2->field_f = r0
    //     0xbfb39c: stur            w0, [x2, #0xf]
    // 0xbfb3a0: ldur            x0, [fp, #-0x38]
    // 0xbfb3a4: StoreField: r2->field_13 = r0
    //     0xbfb3a4: stur            w0, [x2, #0x13]
    // 0xbfb3a8: ldur            x0, [fp, #-0x30]
    // 0xbfb3ac: ArrayStore: r2[0] = r0  ; List_4
    //     0xbfb3ac: stur            w0, [x2, #0x17]
    // 0xbfb3b0: r1 = <Widget>
    //     0xbfb3b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfb3b4: r0 = AllocateGrowableArray()
    //     0xbfb3b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfb3b8: mov             x1, x0
    // 0xbfb3bc: ldur            x0, [fp, #-0x10]
    // 0xbfb3c0: stur            x1, [fp, #-0x28]
    // 0xbfb3c4: StoreField: r1->field_f = r0
    //     0xbfb3c4: stur            w0, [x1, #0xf]
    // 0xbfb3c8: r0 = 6
    //     0xbfb3c8: movz            x0, #0x6
    // 0xbfb3cc: StoreField: r1->field_b = r0
    //     0xbfb3cc: stur            w0, [x1, #0xb]
    // 0xbfb3d0: r0 = Row()
    //     0xbfb3d0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbfb3d4: mov             x1, x0
    // 0xbfb3d8: r0 = Instance_Axis
    //     0xbfb3d8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbfb3dc: stur            x1, [fp, #-0x10]
    // 0xbfb3e0: StoreField: r1->field_f = r0
    //     0xbfb3e0: stur            w0, [x1, #0xf]
    // 0xbfb3e4: r0 = Instance_MainAxisAlignment
    //     0xbfb3e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfb3e8: ldr             x0, [x0, #0xa08]
    // 0xbfb3ec: StoreField: r1->field_13 = r0
    //     0xbfb3ec: stur            w0, [x1, #0x13]
    // 0xbfb3f0: r2 = Instance_MainAxisSize
    //     0xbfb3f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfb3f4: ldr             x2, [x2, #0xa10]
    // 0xbfb3f8: ArrayStore: r1[0] = r2  ; List_4
    //     0xbfb3f8: stur            w2, [x1, #0x17]
    // 0xbfb3fc: r3 = Instance_CrossAxisAlignment
    //     0xbfb3fc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfb400: ldr             x3, [x3, #0xa18]
    // 0xbfb404: StoreField: r1->field_1b = r3
    //     0xbfb404: stur            w3, [x1, #0x1b]
    // 0xbfb408: r4 = Instance_VerticalDirection
    //     0xbfb408: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfb40c: ldr             x4, [x4, #0xa20]
    // 0xbfb410: StoreField: r1->field_23 = r4
    //     0xbfb410: stur            w4, [x1, #0x23]
    // 0xbfb414: r5 = Instance_Clip
    //     0xbfb414: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfb418: ldr             x5, [x5, #0x38]
    // 0xbfb41c: StoreField: r1->field_2b = r5
    //     0xbfb41c: stur            w5, [x1, #0x2b]
    // 0xbfb420: StoreField: r1->field_2f = rZR
    //     0xbfb420: stur            xzr, [x1, #0x2f]
    // 0xbfb424: ldur            x6, [fp, #-0x28]
    // 0xbfb428: StoreField: r1->field_b = r6
    //     0xbfb428: stur            w6, [x1, #0xb]
    // 0xbfb42c: r0 = Container()
    //     0xbfb42c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbfb430: stur            x0, [fp, #-0x28]
    // 0xbfb434: ldur            x16, [fp, #-0x18]
    // 0xbfb438: ldur            lr, [fp, #-0x10]
    // 0xbfb43c: stp             lr, x16, [SP]
    // 0xbfb440: mov             x1, x0
    // 0xbfb444: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbfb444: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbfb448: ldr             x4, [x4, #0x88]
    // 0xbfb44c: r0 = Container()
    //     0xbfb44c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbfb450: r0 = Padding()
    //     0xbfb450: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbfb454: mov             x1, x0
    // 0xbfb458: r0 = Instance_EdgeInsets
    //     0xbfb458: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbfb45c: ldr             x0, [x0, #0x770]
    // 0xbfb460: StoreField: r1->field_f = r0
    //     0xbfb460: stur            w0, [x1, #0xf]
    // 0xbfb464: ldur            x0, [fp, #-0x28]
    // 0xbfb468: StoreField: r1->field_b = r0
    //     0xbfb468: stur            w0, [x1, #0xb]
    // 0xbfb46c: mov             x2, x1
    // 0xbfb470: ldur            x0, [fp, #-0x50]
    // 0xbfb474: ldur            x1, [fp, #-8]
    // 0xbfb478: stur            x2, [fp, #-0x10]
    // 0xbfb47c: r0 = Visibility()
    //     0xbfb47c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbfb480: mov             x3, x0
    // 0xbfb484: ldur            x0, [fp, #-0x10]
    // 0xbfb488: stur            x3, [fp, #-0x18]
    // 0xbfb48c: StoreField: r3->field_b = r0
    //     0xbfb48c: stur            w0, [x3, #0xb]
    // 0xbfb490: r0 = Instance_SizedBox
    //     0xbfb490: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbfb494: StoreField: r3->field_f = r0
    //     0xbfb494: stur            w0, [x3, #0xf]
    // 0xbfb498: ldur            x0, [fp, #-8]
    // 0xbfb49c: StoreField: r3->field_13 = r0
    //     0xbfb49c: stur            w0, [x3, #0x13]
    // 0xbfb4a0: r0 = false
    //     0xbfb4a0: add             x0, NULL, #0x30  ; false
    // 0xbfb4a4: ArrayStore: r3[0] = r0  ; List_4
    //     0xbfb4a4: stur            w0, [x3, #0x17]
    // 0xbfb4a8: StoreField: r3->field_1b = r0
    //     0xbfb4a8: stur            w0, [x3, #0x1b]
    // 0xbfb4ac: StoreField: r3->field_1f = r0
    //     0xbfb4ac: stur            w0, [x3, #0x1f]
    // 0xbfb4b0: StoreField: r3->field_23 = r0
    //     0xbfb4b0: stur            w0, [x3, #0x23]
    // 0xbfb4b4: StoreField: r3->field_27 = r0
    //     0xbfb4b4: stur            w0, [x3, #0x27]
    // 0xbfb4b8: StoreField: r3->field_2b = r0
    //     0xbfb4b8: stur            w0, [x3, #0x2b]
    // 0xbfb4bc: r1 = Null
    //     0xbfb4bc: mov             x1, NULL
    // 0xbfb4c0: r2 = 4
    //     0xbfb4c0: movz            x2, #0x4
    // 0xbfb4c4: r0 = AllocateArray()
    //     0xbfb4c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbfb4c8: mov             x2, x0
    // 0xbfb4cc: ldur            x0, [fp, #-0x50]
    // 0xbfb4d0: stur            x2, [fp, #-8]
    // 0xbfb4d4: StoreField: r2->field_f = r0
    //     0xbfb4d4: stur            w0, [x2, #0xf]
    // 0xbfb4d8: ldur            x0, [fp, #-0x18]
    // 0xbfb4dc: StoreField: r2->field_13 = r0
    //     0xbfb4dc: stur            w0, [x2, #0x13]
    // 0xbfb4e0: r1 = <Widget>
    //     0xbfb4e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbfb4e4: r0 = AllocateGrowableArray()
    //     0xbfb4e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbfb4e8: mov             x1, x0
    // 0xbfb4ec: ldur            x0, [fp, #-8]
    // 0xbfb4f0: stur            x1, [fp, #-0x10]
    // 0xbfb4f4: StoreField: r1->field_f = r0
    //     0xbfb4f4: stur            w0, [x1, #0xf]
    // 0xbfb4f8: r0 = 4
    //     0xbfb4f8: movz            x0, #0x4
    // 0xbfb4fc: StoreField: r1->field_b = r0
    //     0xbfb4fc: stur            w0, [x1, #0xb]
    // 0xbfb500: r0 = Column()
    //     0xbfb500: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbfb504: r1 = Instance_Axis
    //     0xbfb504: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbfb508: StoreField: r0->field_f = r1
    //     0xbfb508: stur            w1, [x0, #0xf]
    // 0xbfb50c: r1 = Instance_MainAxisAlignment
    //     0xbfb50c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbfb510: ldr             x1, [x1, #0xa08]
    // 0xbfb514: StoreField: r0->field_13 = r1
    //     0xbfb514: stur            w1, [x0, #0x13]
    // 0xbfb518: r1 = Instance_MainAxisSize
    //     0xbfb518: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbfb51c: ldr             x1, [x1, #0xa10]
    // 0xbfb520: ArrayStore: r0[0] = r1  ; List_4
    //     0xbfb520: stur            w1, [x0, #0x17]
    // 0xbfb524: r1 = Instance_CrossAxisAlignment
    //     0xbfb524: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbfb528: ldr             x1, [x1, #0xa18]
    // 0xbfb52c: StoreField: r0->field_1b = r1
    //     0xbfb52c: stur            w1, [x0, #0x1b]
    // 0xbfb530: r1 = Instance_VerticalDirection
    //     0xbfb530: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbfb534: ldr             x1, [x1, #0xa20]
    // 0xbfb538: StoreField: r0->field_23 = r1
    //     0xbfb538: stur            w1, [x0, #0x23]
    // 0xbfb53c: r1 = Instance_Clip
    //     0xbfb53c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbfb540: ldr             x1, [x1, #0x38]
    // 0xbfb544: StoreField: r0->field_2b = r1
    //     0xbfb544: stur            w1, [x0, #0x2b]
    // 0xbfb548: StoreField: r0->field_2f = rZR
    //     0xbfb548: stur            xzr, [x0, #0x2f]
    // 0xbfb54c: ldur            x1, [fp, #-0x10]
    // 0xbfb550: StoreField: r0->field_b = r1
    //     0xbfb550: stur            w1, [x0, #0xb]
    // 0xbfb554: LeaveFrame
    //     0xbfb554: mov             SP, fp
    //     0xbfb558: ldp             fp, lr, [SP], #0x10
    // 0xbfb55c: ret
    //     0xbfb55c: ret             
    // 0xbfb560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfb560: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfb564: b               #0xbfa28c
    // 0xbfb568: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb568: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb56c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb56c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb570: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb570: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb574: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb574: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb578: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb578: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb57c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb57c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb580: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb580: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb584: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb584: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb588: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb588: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb58c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb58c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb590: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb590: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb598: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb598: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb59c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb59c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb5a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb5a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbfb5a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb5a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbfb5a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbfb5a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xbfb5b8, size: 0x7c
    // 0xbfb5b8: EnterFrame
    //     0xbfb5b8: stp             fp, lr, [SP, #-0x10]!
    //     0xbfb5bc: mov             fp, SP
    // 0xbfb5c0: AllocStack(0x8)
    //     0xbfb5c0: sub             SP, SP, #8
    // 0xbfb5c4: SetupParameters()
    //     0xbfb5c4: ldr             x0, [fp, #0x10]
    //     0xbfb5c8: ldur            w1, [x0, #0x17]
    //     0xbfb5cc: add             x1, x1, HEAP, lsl #32
    // 0xbfb5d0: CheckStackOverflow
    //     0xbfb5d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfb5d4: cmp             SP, x16
    //     0xbfb5d8: b.ls            #0xbfb628
    // 0xbfb5dc: LoadField: r0 = r1->field_f
    //     0xbfb5dc: ldur            w0, [x1, #0xf]
    // 0xbfb5e0: DecompressPointer r0
    //     0xbfb5e0: add             x0, x0, HEAP, lsl #32
    // 0xbfb5e4: LoadField: r1 = r0->field_b
    //     0xbfb5e4: ldur            w1, [x0, #0xb]
    // 0xbfb5e8: DecompressPointer r1
    //     0xbfb5e8: add             x1, x1, HEAP, lsl #32
    // 0xbfb5ec: cmp             w1, NULL
    // 0xbfb5f0: b.eq            #0xbfb630
    // 0xbfb5f4: LoadField: r0 = r1->field_23
    //     0xbfb5f4: ldur            w0, [x1, #0x23]
    // 0xbfb5f8: DecompressPointer r0
    //     0xbfb5f8: add             x0, x0, HEAP, lsl #32
    // 0xbfb5fc: str             x0, [SP]
    // 0xbfb600: r4 = 0
    //     0xbfb600: movz            x4, #0
    // 0xbfb604: ldr             x0, [SP]
    // 0xbfb608: r16 = UnlinkedCall_0x613b5c
    //     0xbfb608: add             x16, PP, #0x53, lsl #12  ; [pp+0x53420] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbfb60c: add             x16, x16, #0x420
    // 0xbfb610: ldp             x5, lr, [x16]
    // 0xbfb614: blr             lr
    // 0xbfb618: r0 = Null
    //     0xbfb618: mov             x0, NULL
    // 0xbfb61c: LeaveFrame
    //     0xbfb61c: mov             SP, fp
    //     0xbfb620: ldp             fp, lr, [SP], #0x10
    // 0xbfb624: ret
    //     0xbfb624: ret             
    // 0xbfb628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfb628: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfb62c: b               #0xbfb5dc
    // 0xbfb630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb630: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, double, Items, String) {
    // ** addr: 0xbfb634, size: 0x8c
    // 0xbfb634: EnterFrame
    //     0xbfb634: stp             fp, lr, [SP, #-0x10]!
    //     0xbfb638: mov             fp, SP
    // 0xbfb63c: AllocStack(0x20)
    //     0xbfb63c: sub             SP, SP, #0x20
    // 0xbfb640: SetupParameters()
    //     0xbfb640: ldr             x0, [fp, #0x28]
    //     0xbfb644: ldur            w1, [x0, #0x17]
    //     0xbfb648: add             x1, x1, HEAP, lsl #32
    // 0xbfb64c: CheckStackOverflow
    //     0xbfb64c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfb650: cmp             SP, x16
    //     0xbfb654: b.ls            #0xbfb6b4
    // 0xbfb658: LoadField: r0 = r1->field_f
    //     0xbfb658: ldur            w0, [x1, #0xf]
    // 0xbfb65c: DecompressPointer r0
    //     0xbfb65c: add             x0, x0, HEAP, lsl #32
    // 0xbfb660: LoadField: r1 = r0->field_b
    //     0xbfb660: ldur            w1, [x0, #0xb]
    // 0xbfb664: DecompressPointer r1
    //     0xbfb664: add             x1, x1, HEAP, lsl #32
    // 0xbfb668: cmp             w1, NULL
    // 0xbfb66c: b.eq            #0xbfb6bc
    // 0xbfb670: LoadField: r0 = r1->field_27
    //     0xbfb670: ldur            w0, [x1, #0x27]
    // 0xbfb674: DecompressPointer r0
    //     0xbfb674: add             x0, x0, HEAP, lsl #32
    // 0xbfb678: ldr             x16, [fp, #0x20]
    // 0xbfb67c: stp             x16, x0, [SP, #0x10]
    // 0xbfb680: ldr             x16, [fp, #0x18]
    // 0xbfb684: ldr             lr, [fp, #0x10]
    // 0xbfb688: stp             lr, x16, [SP]
    // 0xbfb68c: r4 = 0
    //     0xbfb68c: movz            x4, #0
    // 0xbfb690: ldr             x0, [SP, #0x18]
    // 0xbfb694: r16 = UnlinkedCall_0x613b5c
    //     0xbfb694: add             x16, PP, #0x53, lsl #12  ; [pp+0x53430] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbfb698: add             x16, x16, #0x430
    // 0xbfb69c: ldp             x5, lr, [x16]
    // 0xbfb6a0: blr             lr
    // 0xbfb6a4: r0 = Null
    //     0xbfb6a4: mov             x0, NULL
    // 0xbfb6a8: LeaveFrame
    //     0xbfb6a8: mov             SP, fp
    //     0xbfb6ac: ldp             fp, lr, [SP], #0x10
    // 0xbfb6b0: ret
    //     0xbfb6b0: ret             
    // 0xbfb6b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfb6b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfb6b8: b               #0xbfb658
    // 0xbfb6bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfb6bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3982, size: 0x2c, field offset: 0xc
//   const constructor, 
class OrderItemCard extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80db0, size: 0x24
    // 0xc80db0: EnterFrame
    //     0xc80db0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80db4: mov             fp, SP
    // 0xc80db8: mov             x0, x1
    // 0xc80dbc: r1 = <OrderItemCard>
    //     0xc80dbc: add             x1, PP, #0x48, lsl #12  ; [pp+0x483e8] TypeArguments: <OrderItemCard>
    //     0xc80dc0: ldr             x1, [x1, #0x3e8]
    // 0xc80dc4: r0 = _OrderItemCardState()
    //     0xc80dc4: bl              #0xc80dd4  ; Allocate_OrderItemCardStateStub -> _OrderItemCardState (size=0x14)
    // 0xc80dc8: LeaveFrame
    //     0xc80dc8: mov             SP, fp
    //     0xc80dcc: ldp             fp, lr, [SP], #0x10
    // 0xc80dd0: ret
    //     0xc80dd0: ret             
  }
}
