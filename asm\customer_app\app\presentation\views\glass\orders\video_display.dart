// lib: , url: package:customer_app/app/presentation/views/glass/orders/video_display.dart

// class id: 1049418, size: 0x8
class :: {
}

// class id: 3323, size: 0x18, field offset: 0x14
class VideoPlayerWidgetState extends State<dynamic> {

  late VideoPlayerController _controller; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x943e44, size: 0x14c
    // 0x943e44: EnterFrame
    //     0x943e44: stp             fp, lr, [SP, #-0x10]!
    //     0x943e48: mov             fp, SP
    // 0x943e4c: AllocStack(0x40)
    //     0x943e4c: sub             SP, SP, #0x40
    // 0x943e50: SetupParameters(VideoPlayerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x943e50: stur            x1, [fp, #-8]
    // 0x943e54: CheckStackOverflow
    //     0x943e54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943e58: cmp             SP, x16
    //     0x943e5c: b.ls            #0x943f80
    // 0x943e60: r1 = 1
    //     0x943e60: movz            x1, #0x1
    // 0x943e64: r0 = AllocateContext()
    //     0x943e64: bl              #0x16f6108  ; AllocateContextStub
    // 0x943e68: mov             x1, x0
    // 0x943e6c: ldur            x0, [fp, #-8]
    // 0x943e70: stur            x1, [fp, #-0x18]
    // 0x943e74: StoreField: r1->field_f = r0
    //     0x943e74: stur            w0, [x1, #0xf]
    // 0x943e78: LoadField: r2 = r0->field_b
    //     0x943e78: ldur            w2, [x0, #0xb]
    // 0x943e7c: DecompressPointer r2
    //     0x943e7c: add             x2, x2, HEAP, lsl #32
    // 0x943e80: cmp             w2, NULL
    // 0x943e84: b.eq            #0x943f88
    // 0x943e88: LoadField: r3 = r2->field_b
    //     0x943e88: ldur            w3, [x2, #0xb]
    // 0x943e8c: DecompressPointer r3
    //     0x943e8c: add             x3, x3, HEAP, lsl #32
    // 0x943e90: stur            x3, [fp, #-0x10]
    // 0x943e94: r0 = VideoPlayerOptions()
    //     0x943e94: bl              #0x93437c  ; AllocateVideoPlayerOptionsStub -> VideoPlayerOptions (size=0x10)
    // 0x943e98: mov             x2, x0
    // 0x943e9c: r0 = false
    //     0x943e9c: add             x0, NULL, #0x30  ; false
    // 0x943ea0: stur            x2, [fp, #-0x20]
    // 0x943ea4: StoreField: r2->field_b = r0
    //     0x943ea4: stur            w0, [x2, #0xb]
    // 0x943ea8: StoreField: r2->field_7 = r0
    //     0x943ea8: stur            w0, [x2, #7]
    // 0x943eac: r1 = <VideoPlayerValue>
    //     0x943eac: add             x1, PP, #0x52, lsl #12  ; [pp+0x52f90] TypeArguments: <VideoPlayerValue>
    //     0x943eb0: ldr             x1, [x1, #0xf90]
    // 0x943eb4: r0 = VideoPlayerController()
    //     0x943eb4: bl              #0x8f9c94  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x68)
    // 0x943eb8: stur            x0, [fp, #-0x28]
    // 0x943ebc: ldur            x16, [fp, #-0x20]
    // 0x943ec0: str             x16, [SP]
    // 0x943ec4: mov             x1, x0
    // 0x943ec8: ldur            x2, [fp, #-0x10]
    // 0x943ecc: r4 = const [0, 0x3, 0x1, 0x2, videoPlayerOptions, 0x2, null]
    //     0x943ecc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52f98] List(7) [0, 0x3, 0x1, 0x2, "videoPlayerOptions", 0x2, Null]
    //     0x943ed0: ldr             x4, [x4, #0xf98]
    // 0x943ed4: r0 = VideoPlayerController.network()
    //     0x943ed4: bl              #0x934250  ; [package:video_player/video_player.dart] VideoPlayerController::VideoPlayerController.network
    // 0x943ed8: ldur            x1, [fp, #-0x28]
    // 0x943edc: r0 = initialize()
    //     0x943edc: bl              #0x8f78ec  ; [package:video_player/video_player.dart] VideoPlayerController::initialize
    // 0x943ee0: ldur            x2, [fp, #-0x18]
    // 0x943ee4: r1 = Function '<anonymous closure>':.
    //     0x943ee4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a10] AnonymousClosure: (0x943fb0), in [package:customer_app/app/presentation/views/glass/orders/video_display.dart] VideoPlayerWidgetState::initState (0x943e44)
    //     0x943ee8: ldr             x1, [x1, #0xa10]
    // 0x943eec: stur            x0, [fp, #-0x10]
    // 0x943ef0: r0 = AllocateClosure()
    //     0x943ef0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x943ef4: r16 = <Null?>
    //     0x943ef4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x943ef8: ldur            lr, [fp, #-0x10]
    // 0x943efc: stp             lr, x16, [SP, #8]
    // 0x943f00: str             x0, [SP]
    // 0x943f04: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x943f04: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x943f08: r0 = then()
    //     0x943f08: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x943f0c: ldur            x0, [fp, #-0x28]
    // 0x943f10: ldur            x3, [fp, #-8]
    // 0x943f14: StoreField: r3->field_13 = r0
    //     0x943f14: stur            w0, [x3, #0x13]
    //     0x943f18: ldurb           w16, [x3, #-1]
    //     0x943f1c: ldurb           w17, [x0, #-1]
    //     0x943f20: and             x16, x17, x16, lsr #2
    //     0x943f24: tst             x16, HEAP, lsr #32
    //     0x943f28: b.eq            #0x943f30
    //     0x943f2c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x943f30: LoadField: r0 = r3->field_b
    //     0x943f30: ldur            w0, [x3, #0xb]
    // 0x943f34: DecompressPointer r0
    //     0x943f34: add             x0, x0, HEAP, lsl #32
    // 0x943f38: cmp             w0, NULL
    // 0x943f3c: b.eq            #0x943f8c
    // 0x943f40: LoadField: r1 = r0->field_f
    //     0x943f40: ldur            w1, [x0, #0xf]
    // 0x943f44: DecompressPointer r1
    //     0x943f44: add             x1, x1, HEAP, lsl #32
    // 0x943f48: cmp             w1, NULL
    // 0x943f4c: b.eq            #0x943f70
    // 0x943f50: tbnz            w1, #4, #0x943f70
    // 0x943f54: ldur            x1, [fp, #-0x28]
    // 0x943f58: r2 = true
    //     0x943f58: add             x2, NULL, #0x20  ; true
    // 0x943f5c: r0 = setLooping()
    //     0x943f5c: bl              #0x8faa24  ; [package:video_player/video_player.dart] VideoPlayerController::setLooping
    // 0x943f60: ldur            x0, [fp, #-8]
    // 0x943f64: LoadField: r1 = r0->field_13
    //     0x943f64: ldur            w1, [x0, #0x13]
    // 0x943f68: DecompressPointer r1
    //     0x943f68: add             x1, x1, HEAP, lsl #32
    // 0x943f6c: r0 = play()
    //     0x943f6c: bl              #0x6ec380  ; [package:video_player/video_player.dart] VideoPlayerController::play
    // 0x943f70: r0 = Null
    //     0x943f70: mov             x0, NULL
    // 0x943f74: LeaveFrame
    //     0x943f74: mov             SP, fp
    //     0x943f78: ldp             fp, lr, [SP], #0x10
    // 0x943f7c: ret
    //     0x943f7c: ret             
    // 0x943f80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943f80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943f84: b               #0x943e60
    // 0x943f88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x943f88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x943f8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x943f8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x943fb0, size: 0x64
    // 0x943fb0: EnterFrame
    //     0x943fb0: stp             fp, lr, [SP, #-0x10]!
    //     0x943fb4: mov             fp, SP
    // 0x943fb8: AllocStack(0x8)
    //     0x943fb8: sub             SP, SP, #8
    // 0x943fbc: SetupParameters()
    //     0x943fbc: ldr             x0, [fp, #0x18]
    //     0x943fc0: ldur            w1, [x0, #0x17]
    //     0x943fc4: add             x1, x1, HEAP, lsl #32
    // 0x943fc8: CheckStackOverflow
    //     0x943fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943fcc: cmp             SP, x16
    //     0x943fd0: b.ls            #0x94400c
    // 0x943fd4: LoadField: r0 = r1->field_f
    //     0x943fd4: ldur            w0, [x1, #0xf]
    // 0x943fd8: DecompressPointer r0
    //     0x943fd8: add             x0, x0, HEAP, lsl #32
    // 0x943fdc: stur            x0, [fp, #-8]
    // 0x943fe0: r1 = Function '<anonymous closure>':.
    //     0x943fe0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a18] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x943fe4: ldr             x1, [x1, #0xa18]
    // 0x943fe8: r2 = Null
    //     0x943fe8: mov             x2, NULL
    // 0x943fec: r0 = AllocateClosure()
    //     0x943fec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x943ff0: ldur            x1, [fp, #-8]
    // 0x943ff4: mov             x2, x0
    // 0x943ff8: r0 = setState()
    //     0x943ff8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x943ffc: r0 = Null
    //     0x943ffc: mov             x0, NULL
    // 0x944000: LeaveFrame
    //     0x944000: mov             SP, fp
    //     0x944004: ldp             fp, lr, [SP], #0x10
    // 0x944008: ret
    //     0x944008: ret             
    // 0x94400c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94400c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944010: b               #0x943fd4
  }
  _ build(/* No info */) {
    // ** addr: 0xb79e78, size: 0x34c
    // 0xb79e78: EnterFrame
    //     0xb79e78: stp             fp, lr, [SP, #-0x10]!
    //     0xb79e7c: mov             fp, SP
    // 0xb79e80: AllocStack(0x40)
    //     0xb79e80: sub             SP, SP, #0x40
    // 0xb79e84: SetupParameters(VideoPlayerWidgetState this /* r1 => r0, fp-0x10 */, dynamic _ /* r2 => r1 */)
    //     0xb79e84: mov             x0, x1
    //     0xb79e88: stur            x1, [fp, #-0x10]
    //     0xb79e8c: mov             x1, x2
    // 0xb79e90: CheckStackOverflow
    //     0xb79e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb79e94: cmp             SP, x16
    //     0xb79e98: b.ls            #0xb7a1ac
    // 0xb79e9c: LoadField: r2 = r0->field_13
    //     0xb79e9c: ldur            w2, [x0, #0x13]
    // 0xb79ea0: DecompressPointer r2
    //     0xb79ea0: add             x2, x2, HEAP, lsl #32
    // 0xb79ea4: r16 = Sentinel
    //     0xb79ea4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb79ea8: cmp             w2, w16
    // 0xb79eac: b.eq            #0xb7a1b4
    // 0xb79eb0: stur            x2, [fp, #-8]
    // 0xb79eb4: LoadField: r3 = r2->field_27
    //     0xb79eb4: ldur            w3, [x2, #0x27]
    // 0xb79eb8: DecompressPointer r3
    //     0xb79eb8: add             x3, x3, HEAP, lsl #32
    // 0xb79ebc: LoadField: r4 = r3->field_4b
    //     0xb79ebc: ldur            w4, [x3, #0x4b]
    // 0xb79ec0: DecompressPointer r4
    //     0xb79ec0: add             x4, x4, HEAP, lsl #32
    // 0xb79ec4: tbnz            w4, #4, #0xb7a0ec
    // 0xb79ec8: r0 = Radius()
    //     0xb79ec8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb79ecc: d0 = 15.000000
    //     0xb79ecc: fmov            d0, #15.00000000
    // 0xb79ed0: stur            x0, [fp, #-0x18]
    // 0xb79ed4: StoreField: r0->field_7 = d0
    //     0xb79ed4: stur            d0, [x0, #7]
    // 0xb79ed8: StoreField: r0->field_f = d0
    //     0xb79ed8: stur            d0, [x0, #0xf]
    // 0xb79edc: r0 = BorderRadius()
    //     0xb79edc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb79ee0: mov             x1, x0
    // 0xb79ee4: ldur            x0, [fp, #-0x18]
    // 0xb79ee8: stur            x1, [fp, #-0x20]
    // 0xb79eec: StoreField: r1->field_7 = r0
    //     0xb79eec: stur            w0, [x1, #7]
    // 0xb79ef0: StoreField: r1->field_b = r0
    //     0xb79ef0: stur            w0, [x1, #0xb]
    // 0xb79ef4: StoreField: r1->field_f = r0
    //     0xb79ef4: stur            w0, [x1, #0xf]
    // 0xb79ef8: StoreField: r1->field_13 = r0
    //     0xb79ef8: stur            w0, [x1, #0x13]
    // 0xb79efc: r0 = VideoPlayer()
    //     0xb79efc: bl              #0xa6fccc  ; AllocateVideoPlayerStub -> VideoPlayer (size=0x10)
    // 0xb79f00: mov             x3, x0
    // 0xb79f04: ldur            x0, [fp, #-8]
    // 0xb79f08: stur            x3, [fp, #-0x18]
    // 0xb79f0c: StoreField: r3->field_b = r0
    //     0xb79f0c: stur            w0, [x3, #0xb]
    // 0xb79f10: r1 = Null
    //     0xb79f10: mov             x1, NULL
    // 0xb79f14: r2 = 2
    //     0xb79f14: movz            x2, #0x2
    // 0xb79f18: r0 = AllocateArray()
    //     0xb79f18: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb79f1c: mov             x2, x0
    // 0xb79f20: ldur            x0, [fp, #-0x18]
    // 0xb79f24: stur            x2, [fp, #-8]
    // 0xb79f28: StoreField: r2->field_f = r0
    //     0xb79f28: stur            w0, [x2, #0xf]
    // 0xb79f2c: r1 = <Widget>
    //     0xb79f2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb79f30: r0 = AllocateGrowableArray()
    //     0xb79f30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb79f34: mov             x2, x0
    // 0xb79f38: ldur            x0, [fp, #-8]
    // 0xb79f3c: stur            x2, [fp, #-0x18]
    // 0xb79f40: StoreField: r2->field_f = r0
    //     0xb79f40: stur            w0, [x2, #0xf]
    // 0xb79f44: r0 = 2
    //     0xb79f44: movz            x0, #0x2
    // 0xb79f48: StoreField: r2->field_b = r0
    //     0xb79f48: stur            w0, [x2, #0xb]
    // 0xb79f4c: ldur            x0, [fp, #-0x10]
    // 0xb79f50: LoadField: r1 = r0->field_b
    //     0xb79f50: ldur            w1, [x0, #0xb]
    // 0xb79f54: DecompressPointer r1
    //     0xb79f54: add             x1, x1, HEAP, lsl #32
    // 0xb79f58: cmp             w1, NULL
    // 0xb79f5c: b.eq            #0xb7a1c0
    // 0xb79f60: LoadField: r0 = r1->field_f
    //     0xb79f60: ldur            w0, [x1, #0xf]
    // 0xb79f64: DecompressPointer r0
    //     0xb79f64: add             x0, x0, HEAP, lsl #32
    // 0xb79f68: cmp             w0, NULL
    // 0xb79f6c: b.eq            #0xb79f74
    // 0xb79f70: tbz             w0, #4, #0xb7a070
    // 0xb79f74: r1 = Instance_Color
    //     0xb79f74: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb79f78: d0 = 0.300000
    //     0xb79f78: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb79f7c: ldr             d0, [x17, #0x658]
    // 0xb79f80: r0 = withOpacity()
    //     0xb79f80: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb79f84: stur            x0, [fp, #-8]
    // 0xb79f88: r0 = BoxDecoration()
    //     0xb79f88: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb79f8c: mov             x1, x0
    // 0xb79f90: ldur            x0, [fp, #-8]
    // 0xb79f94: stur            x1, [fp, #-0x10]
    // 0xb79f98: StoreField: r1->field_7 = r0
    //     0xb79f98: stur            w0, [x1, #7]
    // 0xb79f9c: r0 = Instance_BoxShape
    //     0xb79f9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb79fa0: ldr             x0, [x0, #0x970]
    // 0xb79fa4: StoreField: r1->field_23 = r0
    //     0xb79fa4: stur            w0, [x1, #0x23]
    // 0xb79fa8: r0 = Container()
    //     0xb79fa8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb79fac: stur            x0, [fp, #-8]
    // 0xb79fb0: ldur            x16, [fp, #-0x10]
    // 0xb79fb4: r30 = Instance_EdgeInsets
    //     0xb79fb4: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0xb79fb8: ldr             lr, [lr, #0xb30]
    // 0xb79fbc: stp             lr, x16, [SP, #8]
    // 0xb79fc0: r16 = Instance_Icon
    //     0xb79fc0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b38] Obj!Icon@d66431
    //     0xb79fc4: ldr             x16, [x16, #0xb38]
    // 0xb79fc8: str             x16, [SP]
    // 0xb79fcc: mov             x1, x0
    // 0xb79fd0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb79fd0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb79fd4: ldr             x4, [x4, #0xb40]
    // 0xb79fd8: r0 = Container()
    //     0xb79fd8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb79fdc: r1 = <StackParentData>
    //     0xb79fdc: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb79fe0: ldr             x1, [x1, #0x8e0]
    // 0xb79fe4: r0 = Positioned()
    //     0xb79fe4: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb79fe8: mov             x2, x0
    // 0xb79fec: ldur            x0, [fp, #-8]
    // 0xb79ff0: stur            x2, [fp, #-0x10]
    // 0xb79ff4: StoreField: r2->field_b = r0
    //     0xb79ff4: stur            w0, [x2, #0xb]
    // 0xb79ff8: ldur            x0, [fp, #-0x18]
    // 0xb79ffc: LoadField: r1 = r0->field_b
    //     0xb79ffc: ldur            w1, [x0, #0xb]
    // 0xb7a000: LoadField: r3 = r0->field_f
    //     0xb7a000: ldur            w3, [x0, #0xf]
    // 0xb7a004: DecompressPointer r3
    //     0xb7a004: add             x3, x3, HEAP, lsl #32
    // 0xb7a008: LoadField: r4 = r3->field_b
    //     0xb7a008: ldur            w4, [x3, #0xb]
    // 0xb7a00c: r3 = LoadInt32Instr(r1)
    //     0xb7a00c: sbfx            x3, x1, #1, #0x1f
    // 0xb7a010: stur            x3, [fp, #-0x28]
    // 0xb7a014: r1 = LoadInt32Instr(r4)
    //     0xb7a014: sbfx            x1, x4, #1, #0x1f
    // 0xb7a018: cmp             x3, x1
    // 0xb7a01c: b.ne            #0xb7a028
    // 0xb7a020: mov             x1, x0
    // 0xb7a024: r0 = _growToNextCapacity()
    //     0xb7a024: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7a028: ldur            x2, [fp, #-0x18]
    // 0xb7a02c: ldur            x3, [fp, #-0x28]
    // 0xb7a030: add             x0, x3, #1
    // 0xb7a034: lsl             x1, x0, #1
    // 0xb7a038: StoreField: r2->field_b = r1
    //     0xb7a038: stur            w1, [x2, #0xb]
    // 0xb7a03c: LoadField: r1 = r2->field_f
    //     0xb7a03c: ldur            w1, [x2, #0xf]
    // 0xb7a040: DecompressPointer r1
    //     0xb7a040: add             x1, x1, HEAP, lsl #32
    // 0xb7a044: ldur            x0, [fp, #-0x10]
    // 0xb7a048: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7a048: add             x25, x1, x3, lsl #2
    //     0xb7a04c: add             x25, x25, #0xf
    //     0xb7a050: str             w0, [x25]
    //     0xb7a054: tbz             w0, #0, #0xb7a070
    //     0xb7a058: ldurb           w16, [x1, #-1]
    //     0xb7a05c: ldurb           w17, [x0, #-1]
    //     0xb7a060: and             x16, x17, x16, lsr #2
    //     0xb7a064: tst             x16, HEAP, lsr #32
    //     0xb7a068: b.eq            #0xb7a070
    //     0xb7a06c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7a070: ldur            x0, [fp, #-0x20]
    // 0xb7a074: r0 = Stack()
    //     0xb7a074: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb7a078: mov             x1, x0
    // 0xb7a07c: r0 = Instance_Alignment
    //     0xb7a07c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb7a080: ldr             x0, [x0, #0xb10]
    // 0xb7a084: stur            x1, [fp, #-8]
    // 0xb7a088: StoreField: r1->field_f = r0
    //     0xb7a088: stur            w0, [x1, #0xf]
    // 0xb7a08c: r0 = Instance_StackFit
    //     0xb7a08c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb7a090: ldr             x0, [x0, #0xfa8]
    // 0xb7a094: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7a094: stur            w0, [x1, #0x17]
    // 0xb7a098: r0 = Instance_Clip
    //     0xb7a098: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb7a09c: ldr             x0, [x0, #0x7e0]
    // 0xb7a0a0: StoreField: r1->field_1b = r0
    //     0xb7a0a0: stur            w0, [x1, #0x1b]
    // 0xb7a0a4: ldur            x0, [fp, #-0x18]
    // 0xb7a0a8: StoreField: r1->field_b = r0
    //     0xb7a0a8: stur            w0, [x1, #0xb]
    // 0xb7a0ac: r0 = ClipRRect()
    //     0xb7a0ac: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb7a0b0: mov             x1, x0
    // 0xb7a0b4: ldur            x0, [fp, #-0x20]
    // 0xb7a0b8: stur            x1, [fp, #-0x10]
    // 0xb7a0bc: StoreField: r1->field_f = r0
    //     0xb7a0bc: stur            w0, [x1, #0xf]
    // 0xb7a0c0: r0 = Instance_Clip
    //     0xb7a0c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb7a0c4: ldr             x0, [x0, #0x138]
    // 0xb7a0c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7a0c8: stur            w0, [x1, #0x17]
    // 0xb7a0cc: ldur            x0, [fp, #-8]
    // 0xb7a0d0: StoreField: r1->field_b = r0
    //     0xb7a0d0: stur            w0, [x1, #0xb]
    // 0xb7a0d4: r0 = AspectRatio()
    //     0xb7a0d4: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb7a0d8: d0 = 1.000000
    //     0xb7a0d8: fmov            d0, #1.00000000
    // 0xb7a0dc: StoreField: r0->field_f = d0
    //     0xb7a0dc: stur            d0, [x0, #0xf]
    // 0xb7a0e0: ldur            x1, [fp, #-0x10]
    // 0xb7a0e4: StoreField: r0->field_b = r1
    //     0xb7a0e4: stur            w1, [x0, #0xb]
    // 0xb7a0e8: b               #0xb7a1a0
    // 0xb7a0ec: r0 = Instance_Alignment
    //     0xb7a0ec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb7a0f0: ldr             x0, [x0, #0xb10]
    // 0xb7a0f4: r0 = of()
    //     0xb7a0f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7a0f8: LoadField: r1 = r0->field_5b
    //     0xb7a0f8: ldur            w1, [x0, #0x5b]
    // 0xb7a0fc: DecompressPointer r1
    //     0xb7a0fc: add             x1, x1, HEAP, lsl #32
    // 0xb7a100: r0 = LoadClassIdInstr(r1)
    //     0xb7a100: ldur            x0, [x1, #-1]
    //     0xb7a104: ubfx            x0, x0, #0xc, #0x14
    // 0xb7a108: d0 = 0.300000
    //     0xb7a108: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb7a10c: ldr             d0, [x17, #0x658]
    // 0xb7a110: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb7a110: sub             lr, x0, #0xffa
    //     0xb7a114: ldr             lr, [x21, lr, lsl #3]
    //     0xb7a118: blr             lr
    // 0xb7a11c: stur            x0, [fp, #-8]
    // 0xb7a120: r0 = CircularProgressIndicator()
    //     0xb7a120: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xb7a124: mov             x1, x0
    // 0xb7a128: r0 = Instance__ActivityIndicatorType
    //     0xb7a128: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xb7a12c: ldr             x0, [x0, #0x1b0]
    // 0xb7a130: stur            x1, [fp, #-0x10]
    // 0xb7a134: StoreField: r1->field_23 = r0
    //     0xb7a134: stur            w0, [x1, #0x23]
    // 0xb7a138: ldur            x0, [fp, #-8]
    // 0xb7a13c: StoreField: r1->field_13 = r0
    //     0xb7a13c: stur            w0, [x1, #0x13]
    // 0xb7a140: r0 = SizedBox()
    //     0xb7a140: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb7a144: mov             x1, x0
    // 0xb7a148: r0 = 30.000000
    //     0xb7a148: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb7a14c: ldr             x0, [x0, #0x768]
    // 0xb7a150: stur            x1, [fp, #-8]
    // 0xb7a154: StoreField: r1->field_f = r0
    //     0xb7a154: stur            w0, [x1, #0xf]
    // 0xb7a158: StoreField: r1->field_13 = r0
    //     0xb7a158: stur            w0, [x1, #0x13]
    // 0xb7a15c: ldur            x0, [fp, #-0x10]
    // 0xb7a160: StoreField: r1->field_b = r0
    //     0xb7a160: stur            w0, [x1, #0xb]
    // 0xb7a164: r0 = Center()
    //     0xb7a164: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb7a168: mov             x1, x0
    // 0xb7a16c: r0 = Instance_Alignment
    //     0xb7a16c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb7a170: ldr             x0, [x0, #0xb10]
    // 0xb7a174: stur            x1, [fp, #-0x10]
    // 0xb7a178: StoreField: r1->field_f = r0
    //     0xb7a178: stur            w0, [x1, #0xf]
    // 0xb7a17c: ldur            x0, [fp, #-8]
    // 0xb7a180: StoreField: r1->field_b = r0
    //     0xb7a180: stur            w0, [x1, #0xb]
    // 0xb7a184: r0 = SizedBox()
    //     0xb7a184: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb7a188: r1 = 57.000000
    //     0xb7a188: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0xb7a18c: ldr             x1, [x1, #0xb18]
    // 0xb7a190: StoreField: r0->field_f = r1
    //     0xb7a190: stur            w1, [x0, #0xf]
    // 0xb7a194: StoreField: r0->field_13 = r1
    //     0xb7a194: stur            w1, [x0, #0x13]
    // 0xb7a198: ldur            x1, [fp, #-0x10]
    // 0xb7a19c: StoreField: r0->field_b = r1
    //     0xb7a19c: stur            w1, [x0, #0xb]
    // 0xb7a1a0: LeaveFrame
    //     0xb7a1a0: mov             SP, fp
    //     0xb7a1a4: ldp             fp, lr, [SP], #0x10
    // 0xb7a1a8: ret
    //     0xb7a1a8: ret             
    // 0xb7a1ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7a1ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7a1b0: b               #0xb79e9c
    // 0xb7a1b4: r9 = _controller
    //     0xb7a1b4: add             x9, PP, #0x55, lsl #12  ; [pp+0x55a08] Field <VideoPlayerWidgetState._controller@1604292336>: late (offset: 0x14)
    //     0xb7a1b8: ldr             x9, [x9, #0xa08]
    // 0xb7a1bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb7a1bc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb7a1c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7a1c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87d0c, size: 0x54
    // 0xc87d0c: EnterFrame
    //     0xc87d0c: stp             fp, lr, [SP, #-0x10]!
    //     0xc87d10: mov             fp, SP
    // 0xc87d14: CheckStackOverflow
    //     0xc87d14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87d18: cmp             SP, x16
    //     0xc87d1c: b.ls            #0xc87d4c
    // 0xc87d20: LoadField: r0 = r1->field_13
    //     0xc87d20: ldur            w0, [x1, #0x13]
    // 0xc87d24: DecompressPointer r0
    //     0xc87d24: add             x0, x0, HEAP, lsl #32
    // 0xc87d28: r16 = Sentinel
    //     0xc87d28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87d2c: cmp             w0, w16
    // 0xc87d30: b.eq            #0xc87d54
    // 0xc87d34: mov             x1, x0
    // 0xc87d38: r0 = dispose()
    //     0xc87d38: bl              #0xc75c5c  ; [package:video_player/video_player.dart] VideoPlayerController::dispose
    // 0xc87d3c: r0 = Null
    //     0xc87d3c: mov             x0, NULL
    // 0xc87d40: LeaveFrame
    //     0xc87d40: mov             SP, fp
    //     0xc87d44: ldp             fp, lr, [SP], #0x10
    // 0xc87d48: ret
    //     0xc87d48: ret             
    // 0xc87d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87d4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87d50: b               #0xc87d20
    // 0xc87d54: r9 = _controller
    //     0xc87d54: add             x9, PP, #0x55, lsl #12  ; [pp+0x55a08] Field <VideoPlayerWidgetState._controller@1604292336>: late (offset: 0x14)
    //     0xc87d58: ldr             x9, [x9, #0xa08]
    // 0xc87d5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87d5c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4065, size: 0x14, field offset: 0xc
class VideoPlayerWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f674, size: 0x2c
    // 0xc7f674: EnterFrame
    //     0xc7f674: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f678: mov             fp, SP
    // 0xc7f67c: mov             x0, x1
    // 0xc7f680: r1 = <VideoPlayerWidget>
    //     0xc7f680: add             x1, PP, #0x48, lsl #12  ; [pp+0x487e8] TypeArguments: <VideoPlayerWidget>
    //     0xc7f684: ldr             x1, [x1, #0x7e8]
    // 0xc7f688: r0 = VideoPlayerWidgetState()
    //     0xc7f688: bl              #0xc7f6a0  ; AllocateVideoPlayerWidgetStateStub -> VideoPlayerWidgetState (size=0x18)
    // 0xc7f68c: r1 = Sentinel
    //     0xc7f68c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f690: StoreField: r0->field_13 = r1
    //     0xc7f690: stur            w1, [x0, #0x13]
    // 0xc7f694: LeaveFrame
    //     0xc7f694: mov             SP, fp
    //     0xc7f698: ldp             fp, lr, [SP], #0x10
    // 0xc7f69c: ret
    //     0xc7f69c: ret             
  }
}
