// lib: , url: package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart

// class id: 1049477, size: 0x8
class :: {
}

// class id: 4544, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestNumberPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1365ae0, size: 0x98
    // 0x1365ae0: EnterFrame
    //     0x1365ae0: stp             fp, lr, [SP, #-0x10]!
    //     0x1365ae4: mov             fp, SP
    // 0x1365ae8: AllocStack(0x18)
    //     0x1365ae8: sub             SP, SP, #0x18
    // 0x1365aec: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1365aec: stur            x1, [fp, #-8]
    //     0x1365af0: stur            x2, [fp, #-0x10]
    // 0x1365af4: CheckStackOverflow
    //     0x1365af4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1365af8: cmp             SP, x16
    //     0x1365afc: b.ls            #0x1365b70
    // 0x1365b00: r1 = 2
    //     0x1365b00: movz            x1, #0x2
    // 0x1365b04: r0 = AllocateContext()
    //     0x1365b04: bl              #0x16f6108  ; AllocateContextStub
    // 0x1365b08: ldur            x1, [fp, #-8]
    // 0x1365b0c: stur            x0, [fp, #-0x18]
    // 0x1365b10: StoreField: r0->field_f = r1
    //     0x1365b10: stur            w1, [x0, #0xf]
    // 0x1365b14: ldur            x2, [fp, #-0x10]
    // 0x1365b18: StoreField: r0->field_13 = r2
    //     0x1365b18: stur            w2, [x0, #0x13]
    // 0x1365b1c: r0 = controller()
    //     0x1365b1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1365b20: LoadField: r1 = r0->field_d7
    //     0x1365b20: ldur            w1, [x0, #0xd7]
    // 0x1365b24: DecompressPointer r1
    //     0x1365b24: add             x1, x1, HEAP, lsl #32
    // 0x1365b28: r0 = value()
    //     0x1365b28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1365b2c: tbnz            w0, #4, #0x1365b40
    // 0x1365b30: r0 = Instance_SizedBox
    //     0x1365b30: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1365b34: LeaveFrame
    //     0x1365b34: mov             SP, fp
    //     0x1365b38: ldp             fp, lr, [SP], #0x10
    // 0x1365b3c: ret
    //     0x1365b3c: ret             
    // 0x1365b40: r0 = Obx()
    //     0x1365b40: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1365b44: ldur            x2, [fp, #-0x18]
    // 0x1365b48: r1 = Function '<anonymous closure>':.
    //     0x1365b48: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8e8] AnonymousClosure: (0x1365b78), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::bottomNavigationBar (0x1365ae0)
    //     0x1365b4c: ldr             x1, [x1, #0x8e8]
    // 0x1365b50: stur            x0, [fp, #-8]
    // 0x1365b54: r0 = AllocateClosure()
    //     0x1365b54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1365b58: mov             x1, x0
    // 0x1365b5c: ldur            x0, [fp, #-8]
    // 0x1365b60: StoreField: r0->field_b = r1
    //     0x1365b60: stur            w1, [x0, #0xb]
    // 0x1365b64: LeaveFrame
    //     0x1365b64: mov             SP, fp
    //     0x1365b68: ldp             fp, lr, [SP], #0x10
    // 0x1365b6c: ret
    //     0x1365b6c: ret             
    // 0x1365b70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1365b70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1365b74: b               #0x1365b00
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x1365b78, size: 0x638
    // 0x1365b78: EnterFrame
    //     0x1365b78: stp             fp, lr, [SP, #-0x10]!
    //     0x1365b7c: mov             fp, SP
    // 0x1365b80: AllocStack(0x60)
    //     0x1365b80: sub             SP, SP, #0x60
    // 0x1365b84: SetupParameters()
    //     0x1365b84: ldr             x0, [fp, #0x10]
    //     0x1365b88: ldur            w2, [x0, #0x17]
    //     0x1365b8c: add             x2, x2, HEAP, lsl #32
    //     0x1365b90: stur            x2, [fp, #-8]
    // 0x1365b94: CheckStackOverflow
    //     0x1365b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1365b98: cmp             SP, x16
    //     0x1365b9c: b.ls            #0x1366164
    // 0x1365ba0: LoadField: r1 = r2->field_13
    //     0x1365ba0: ldur            w1, [x2, #0x13]
    // 0x1365ba4: DecompressPointer r1
    //     0x1365ba4: add             x1, x1, HEAP, lsl #32
    // 0x1365ba8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1365ba8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1365bac: r0 = _of()
    //     0x1365bac: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1365bb0: LoadField: r1 = r0->field_23
    //     0x1365bb0: ldur            w1, [x0, #0x23]
    // 0x1365bb4: DecompressPointer r1
    //     0x1365bb4: add             x1, x1, HEAP, lsl #32
    // 0x1365bb8: LoadField: d0 = r1->field_1f
    //     0x1365bb8: ldur            d0, [x1, #0x1f]
    // 0x1365bbc: stur            d0, [fp, #-0x40]
    // 0x1365bc0: r0 = EdgeInsets()
    //     0x1365bc0: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1365bc4: stur            x0, [fp, #-0x10]
    // 0x1365bc8: StoreField: r0->field_7 = rZR
    //     0x1365bc8: stur            xzr, [x0, #7]
    // 0x1365bcc: StoreField: r0->field_f = rZR
    //     0x1365bcc: stur            xzr, [x0, #0xf]
    // 0x1365bd0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1365bd0: stur            xzr, [x0, #0x17]
    // 0x1365bd4: ldur            d0, [fp, #-0x40]
    // 0x1365bd8: StoreField: r0->field_1f = d0
    //     0x1365bd8: stur            d0, [x0, #0x1f]
    // 0x1365bdc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1365bdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1365be0: ldr             x0, [x0, #0x1c80]
    //     0x1365be4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1365be8: cmp             w0, w16
    //     0x1365bec: b.ne            #0x1365bf8
    //     0x1365bf0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1365bf4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1365bf8: r0 = GetNavigation.width()
    //     0x1365bf8: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x1365bfc: ldur            x2, [fp, #-8]
    // 0x1365c00: stur            d0, [fp, #-0x40]
    // 0x1365c04: LoadField: r1 = r2->field_13
    //     0x1365c04: ldur            w1, [x2, #0x13]
    // 0x1365c08: DecompressPointer r1
    //     0x1365c08: add             x1, x1, HEAP, lsl #32
    // 0x1365c0c: r0 = of()
    //     0x1365c0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1365c10: LoadField: r1 = r0->field_5b
    //     0x1365c10: ldur            w1, [x0, #0x5b]
    // 0x1365c14: DecompressPointer r1
    //     0x1365c14: add             x1, x1, HEAP, lsl #32
    // 0x1365c18: r0 = LoadClassIdInstr(r1)
    //     0x1365c18: ldur            x0, [x1, #-1]
    //     0x1365c1c: ubfx            x0, x0, #0xc, #0x14
    // 0x1365c20: r2 = 40
    //     0x1365c20: movz            x2, #0x28
    // 0x1365c24: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x1365c24: sub             lr, x0, #0xfe7
    //     0x1365c28: ldr             lr, [x21, lr, lsl #3]
    //     0x1365c2c: blr             lr
    // 0x1365c30: stur            x0, [fp, #-0x18]
    // 0x1365c34: r0 = BorderSide()
    //     0x1365c34: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1365c38: mov             x1, x0
    // 0x1365c3c: ldur            x0, [fp, #-0x18]
    // 0x1365c40: stur            x1, [fp, #-0x20]
    // 0x1365c44: StoreField: r1->field_7 = r0
    //     0x1365c44: stur            w0, [x1, #7]
    // 0x1365c48: d0 = 2.000000
    //     0x1365c48: fmov            d0, #2.00000000
    // 0x1365c4c: StoreField: r1->field_b = d0
    //     0x1365c4c: stur            d0, [x1, #0xb]
    // 0x1365c50: r0 = Instance_BorderStyle
    //     0x1365c50: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1365c54: ldr             x0, [x0, #0xf68]
    // 0x1365c58: StoreField: r1->field_13 = r0
    //     0x1365c58: stur            w0, [x1, #0x13]
    // 0x1365c5c: d0 = -1.000000
    //     0x1365c5c: fmov            d0, #-1.00000000
    // 0x1365c60: ArrayStore: r1[0] = d0  ; List_8
    //     0x1365c60: stur            d0, [x1, #0x17]
    // 0x1365c64: r0 = Border()
    //     0x1365c64: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x1365c68: mov             x1, x0
    // 0x1365c6c: ldur            x0, [fp, #-0x20]
    // 0x1365c70: stur            x1, [fp, #-0x18]
    // 0x1365c74: StoreField: r1->field_7 = r0
    //     0x1365c74: stur            w0, [x1, #7]
    // 0x1365c78: r0 = Instance_BorderSide
    //     0x1365c78: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1365c7c: ldr             x0, [x0, #0xe20]
    // 0x1365c80: StoreField: r1->field_b = r0
    //     0x1365c80: stur            w0, [x1, #0xb]
    // 0x1365c84: StoreField: r1->field_f = r0
    //     0x1365c84: stur            w0, [x1, #0xf]
    // 0x1365c88: StoreField: r1->field_13 = r0
    //     0x1365c88: stur            w0, [x1, #0x13]
    // 0x1365c8c: r0 = BoxDecoration()
    //     0x1365c8c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1365c90: mov             x2, x0
    // 0x1365c94: ldur            x0, [fp, #-0x18]
    // 0x1365c98: stur            x2, [fp, #-0x20]
    // 0x1365c9c: StoreField: r2->field_f = r0
    //     0x1365c9c: stur            w0, [x2, #0xf]
    // 0x1365ca0: r0 = Instance_BoxShape
    //     0x1365ca0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1365ca4: ldr             x0, [x0, #0x80]
    // 0x1365ca8: StoreField: r2->field_23 = r0
    //     0x1365ca8: stur            w0, [x2, #0x23]
    // 0x1365cac: ldur            x0, [fp, #-8]
    // 0x1365cb0: LoadField: r1 = r0->field_f
    //     0x1365cb0: ldur            w1, [x0, #0xf]
    // 0x1365cb4: DecompressPointer r1
    //     0x1365cb4: add             x1, x1, HEAP, lsl #32
    // 0x1365cb8: r0 = controller()
    //     0x1365cb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1365cbc: LoadField: r1 = r0->field_a7
    //     0x1365cbc: ldur            w1, [x0, #0xa7]
    // 0x1365cc0: DecompressPointer r1
    //     0x1365cc0: add             x1, x1, HEAP, lsl #32
    // 0x1365cc4: r0 = value()
    //     0x1365cc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1365cc8: tbnz            w0, #4, #0x1365cec
    // 0x1365ccc: ldur            x2, [fp, #-8]
    // 0x1365cd0: LoadField: r1 = r2->field_13
    //     0x1365cd0: ldur            w1, [x2, #0x13]
    // 0x1365cd4: DecompressPointer r1
    //     0x1365cd4: add             x1, x1, HEAP, lsl #32
    // 0x1365cd8: r0 = of()
    //     0x1365cd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1365cdc: LoadField: r1 = r0->field_5b
    //     0x1365cdc: ldur            w1, [x0, #0x5b]
    // 0x1365ce0: DecompressPointer r1
    //     0x1365ce0: add             x1, x1, HEAP, lsl #32
    // 0x1365ce4: mov             x0, x1
    // 0x1365ce8: b               #0x1365cf4
    // 0x1365cec: r0 = Instance_MaterialColor
    //     0x1365cec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x1365cf0: ldr             x0, [x0, #0xdc0]
    // 0x1365cf4: ldur            x2, [fp, #-8]
    // 0x1365cf8: r16 = <Color>
    //     0x1365cf8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1365cfc: ldr             x16, [x16, #0xf80]
    // 0x1365d00: stp             x0, x16, [SP]
    // 0x1365d04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1365d04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1365d08: r0 = all()
    //     0x1365d08: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1365d0c: stur            x0, [fp, #-0x18]
    // 0x1365d10: r16 = <RoundedRectangleBorder>
    //     0x1365d10: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1365d14: ldr             x16, [x16, #0xf78]
    // 0x1365d18: r30 = Instance_RoundedRectangleBorder
    //     0x1365d18: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1365d1c: ldr             lr, [lr, #0xd68]
    // 0x1365d20: stp             lr, x16, [SP]
    // 0x1365d24: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1365d24: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1365d28: r0 = all()
    //     0x1365d28: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1365d2c: stur            x0, [fp, #-0x28]
    // 0x1365d30: r0 = ButtonStyle()
    //     0x1365d30: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1365d34: mov             x1, x0
    // 0x1365d38: ldur            x0, [fp, #-0x18]
    // 0x1365d3c: stur            x1, [fp, #-0x30]
    // 0x1365d40: StoreField: r1->field_b = r0
    //     0x1365d40: stur            w0, [x1, #0xb]
    // 0x1365d44: ldur            x0, [fp, #-0x28]
    // 0x1365d48: StoreField: r1->field_43 = r0
    //     0x1365d48: stur            w0, [x1, #0x43]
    // 0x1365d4c: r0 = TextButtonThemeData()
    //     0x1365d4c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1365d50: mov             x2, x0
    // 0x1365d54: ldur            x0, [fp, #-0x30]
    // 0x1365d58: stur            x2, [fp, #-0x18]
    // 0x1365d5c: StoreField: r2->field_7 = r0
    //     0x1365d5c: stur            w0, [x2, #7]
    // 0x1365d60: ldur            x0, [fp, #-8]
    // 0x1365d64: LoadField: r1 = r0->field_f
    //     0x1365d64: ldur            w1, [x0, #0xf]
    // 0x1365d68: DecompressPointer r1
    //     0x1365d68: add             x1, x1, HEAP, lsl #32
    // 0x1365d6c: r0 = controller()
    //     0x1365d6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1365d70: LoadField: r1 = r0->field_a7
    //     0x1365d70: ldur            w1, [x0, #0xa7]
    // 0x1365d74: DecompressPointer r1
    //     0x1365d74: add             x1, x1, HEAP, lsl #32
    // 0x1365d78: r0 = value()
    //     0x1365d78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1365d7c: tbnz            w0, #4, #0x1365d98
    // 0x1365d80: ldur            x2, [fp, #-8]
    // 0x1365d84: r1 = Function '<anonymous closure>':.
    //     0x1365d84: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d8f0] AnonymousClosure: (0x13661b0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::bottomNavigationBar (0x1365ae0)
    //     0x1365d88: ldr             x1, [x1, #0x8f0]
    // 0x1365d8c: r0 = AllocateClosure()
    //     0x1365d8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1365d90: mov             x4, x0
    // 0x1365d94: b               #0x1365d9c
    // 0x1365d98: r4 = Null
    //     0x1365d98: mov             x4, NULL
    // 0x1365d9c: ldur            x2, [fp, #-8]
    // 0x1365da0: ldur            x3, [fp, #-0x10]
    // 0x1365da4: ldur            d0, [fp, #-0x40]
    // 0x1365da8: ldur            x0, [fp, #-0x18]
    // 0x1365dac: stur            x4, [fp, #-0x28]
    // 0x1365db0: LoadField: r1 = r2->field_13
    //     0x1365db0: ldur            w1, [x2, #0x13]
    // 0x1365db4: DecompressPointer r1
    //     0x1365db4: add             x1, x1, HEAP, lsl #32
    // 0x1365db8: r0 = of()
    //     0x1365db8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1365dbc: LoadField: r1 = r0->field_87
    //     0x1365dbc: ldur            w1, [x0, #0x87]
    // 0x1365dc0: DecompressPointer r1
    //     0x1365dc0: add             x1, x1, HEAP, lsl #32
    // 0x1365dc4: LoadField: r0 = r1->field_7
    //     0x1365dc4: ldur            w0, [x1, #7]
    // 0x1365dc8: DecompressPointer r0
    //     0x1365dc8: add             x0, x0, HEAP, lsl #32
    // 0x1365dcc: r16 = 14.000000
    //     0x1365dcc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1365dd0: ldr             x16, [x16, #0x1d8]
    // 0x1365dd4: r30 = Instance_Color
    //     0x1365dd4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1365dd8: stp             lr, x16, [SP]
    // 0x1365ddc: mov             x1, x0
    // 0x1365de0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1365de0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1365de4: ldr             x4, [x4, #0xaa0]
    // 0x1365de8: r0 = copyWith()
    //     0x1365de8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1365dec: stur            x0, [fp, #-0x30]
    // 0x1365df0: r0 = Text()
    //     0x1365df0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1365df4: mov             x1, x0
    // 0x1365df8: r0 = "CONTINUE"
    //     0x1365df8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x1365dfc: ldr             x0, [x0, #0xac8]
    // 0x1365e00: stur            x1, [fp, #-0x38]
    // 0x1365e04: StoreField: r1->field_b = r0
    //     0x1365e04: stur            w0, [x1, #0xb]
    // 0x1365e08: ldur            x0, [fp, #-0x30]
    // 0x1365e0c: StoreField: r1->field_13 = r0
    //     0x1365e0c: stur            w0, [x1, #0x13]
    // 0x1365e10: r0 = TextButton()
    //     0x1365e10: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1365e14: mov             x1, x0
    // 0x1365e18: ldur            x0, [fp, #-0x28]
    // 0x1365e1c: stur            x1, [fp, #-0x30]
    // 0x1365e20: StoreField: r1->field_b = r0
    //     0x1365e20: stur            w0, [x1, #0xb]
    // 0x1365e24: r0 = false
    //     0x1365e24: add             x0, NULL, #0x30  ; false
    // 0x1365e28: StoreField: r1->field_27 = r0
    //     0x1365e28: stur            w0, [x1, #0x27]
    // 0x1365e2c: r2 = true
    //     0x1365e2c: add             x2, NULL, #0x20  ; true
    // 0x1365e30: StoreField: r1->field_2f = r2
    //     0x1365e30: stur            w2, [x1, #0x2f]
    // 0x1365e34: ldur            x3, [fp, #-0x38]
    // 0x1365e38: StoreField: r1->field_37 = r3
    //     0x1365e38: stur            w3, [x1, #0x37]
    // 0x1365e3c: r0 = TextButtonTheme()
    //     0x1365e3c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1365e40: mov             x1, x0
    // 0x1365e44: ldur            x0, [fp, #-0x18]
    // 0x1365e48: stur            x1, [fp, #-0x28]
    // 0x1365e4c: StoreField: r1->field_f = r0
    //     0x1365e4c: stur            w0, [x1, #0xf]
    // 0x1365e50: ldur            x0, [fp, #-0x30]
    // 0x1365e54: StoreField: r1->field_b = r0
    //     0x1365e54: stur            w0, [x1, #0xb]
    // 0x1365e58: ldur            d0, [fp, #-0x40]
    // 0x1365e5c: r0 = inline_Allocate_Double()
    //     0x1365e5c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x1365e60: add             x0, x0, #0x10
    //     0x1365e64: cmp             x2, x0
    //     0x1365e68: b.ls            #0x136616c
    //     0x1365e6c: str             x0, [THR, #0x50]  ; THR::top
    //     0x1365e70: sub             x0, x0, #0xf
    //     0x1365e74: movz            x2, #0xe15c
    //     0x1365e78: movk            x2, #0x3, lsl #16
    //     0x1365e7c: stur            x2, [x0, #-1]
    // 0x1365e80: StoreField: r0->field_7 = d0
    //     0x1365e80: stur            d0, [x0, #7]
    // 0x1365e84: stur            x0, [fp, #-0x18]
    // 0x1365e88: r0 = Container()
    //     0x1365e88: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1365e8c: stur            x0, [fp, #-0x30]
    // 0x1365e90: r16 = Instance_EdgeInsets
    //     0x1365e90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x1365e94: ldr             x16, [x16, #0xe48]
    // 0x1365e98: ldur            lr, [fp, #-0x18]
    // 0x1365e9c: stp             lr, x16, [SP, #0x10]
    // 0x1365ea0: ldur            x16, [fp, #-0x20]
    // 0x1365ea4: ldur            lr, [fp, #-0x28]
    // 0x1365ea8: stp             lr, x16, [SP]
    // 0x1365eac: mov             x1, x0
    // 0x1365eb0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x1365eb0: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x1365eb4: ldr             x4, [x4, #0x18]
    // 0x1365eb8: r0 = Container()
    //     0x1365eb8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1365ebc: r0 = GetNavigation.size()
    //     0x1365ebc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1365ec0: LoadField: d0 = r0->field_7
    //     0x1365ec0: ldur            d0, [x0, #7]
    // 0x1365ec4: ldur            x0, [fp, #-8]
    // 0x1365ec8: stur            d0, [fp, #-0x40]
    // 0x1365ecc: LoadField: r1 = r0->field_13
    //     0x1365ecc: ldur            w1, [x0, #0x13]
    // 0x1365ed0: DecompressPointer r1
    //     0x1365ed0: add             x1, x1, HEAP, lsl #32
    // 0x1365ed4: r0 = of()
    //     0x1365ed4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1365ed8: LoadField: r1 = r0->field_87
    //     0x1365ed8: ldur            w1, [x0, #0x87]
    // 0x1365edc: DecompressPointer r1
    //     0x1365edc: add             x1, x1, HEAP, lsl #32
    // 0x1365ee0: LoadField: r0 = r1->field_2b
    //     0x1365ee0: ldur            w0, [x1, #0x2b]
    // 0x1365ee4: DecompressPointer r0
    //     0x1365ee4: add             x0, x0, HEAP, lsl #32
    // 0x1365ee8: stur            x0, [fp, #-8]
    // 0x1365eec: r1 = Instance_Color
    //     0x1365eec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1365ef0: d0 = 0.700000
    //     0x1365ef0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1365ef4: ldr             d0, [x17, #0xf48]
    // 0x1365ef8: r0 = withOpacity()
    //     0x1365ef8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1365efc: r16 = 10.000000
    //     0x1365efc: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x1365f00: stp             x0, x16, [SP]
    // 0x1365f04: ldur            x1, [fp, #-8]
    // 0x1365f08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1365f08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1365f0c: ldr             x4, [x4, #0xaa0]
    // 0x1365f10: r0 = copyWith()
    //     0x1365f10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1365f14: stur            x0, [fp, #-8]
    // 0x1365f18: r0 = Text()
    //     0x1365f18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1365f1c: mov             x1, x0
    // 0x1365f20: r0 = "Powered By"
    //     0x1365f20: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x1365f24: ldr             x0, [x0, #0x750]
    // 0x1365f28: stur            x1, [fp, #-0x18]
    // 0x1365f2c: StoreField: r1->field_b = r0
    //     0x1365f2c: stur            w0, [x1, #0xb]
    // 0x1365f30: ldur            x0, [fp, #-8]
    // 0x1365f34: StoreField: r1->field_13 = r0
    //     0x1365f34: stur            w0, [x1, #0x13]
    // 0x1365f38: r0 = SvgPicture()
    //     0x1365f38: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1365f3c: stur            x0, [fp, #-8]
    // 0x1365f40: r16 = 20.000000
    //     0x1365f40: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x1365f44: ldr             x16, [x16, #0xac8]
    // 0x1365f48: str             x16, [SP]
    // 0x1365f4c: mov             x1, x0
    // 0x1365f50: r2 = "assets/images/shopdeck.svg"
    //     0x1365f50: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x1365f54: ldr             x2, [x2, #0x758]
    // 0x1365f58: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x1365f58: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x1365f5c: ldr             x4, [x4, #0x760]
    // 0x1365f60: r0 = SvgPicture.asset()
    //     0x1365f60: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1365f64: r1 = Null
    //     0x1365f64: mov             x1, NULL
    // 0x1365f68: r2 = 4
    //     0x1365f68: movz            x2, #0x4
    // 0x1365f6c: r0 = AllocateArray()
    //     0x1365f6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1365f70: mov             x2, x0
    // 0x1365f74: ldur            x0, [fp, #-0x18]
    // 0x1365f78: stur            x2, [fp, #-0x20]
    // 0x1365f7c: StoreField: r2->field_f = r0
    //     0x1365f7c: stur            w0, [x2, #0xf]
    // 0x1365f80: ldur            x0, [fp, #-8]
    // 0x1365f84: StoreField: r2->field_13 = r0
    //     0x1365f84: stur            w0, [x2, #0x13]
    // 0x1365f88: r1 = <Widget>
    //     0x1365f88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1365f8c: r0 = AllocateGrowableArray()
    //     0x1365f8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1365f90: mov             x1, x0
    // 0x1365f94: ldur            x0, [fp, #-0x20]
    // 0x1365f98: stur            x1, [fp, #-8]
    // 0x1365f9c: StoreField: r1->field_f = r0
    //     0x1365f9c: stur            w0, [x1, #0xf]
    // 0x1365fa0: r2 = 4
    //     0x1365fa0: movz            x2, #0x4
    // 0x1365fa4: StoreField: r1->field_b = r2
    //     0x1365fa4: stur            w2, [x1, #0xb]
    // 0x1365fa8: r0 = Row()
    //     0x1365fa8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1365fac: mov             x1, x0
    // 0x1365fb0: r0 = Instance_Axis
    //     0x1365fb0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1365fb4: stur            x1, [fp, #-0x18]
    // 0x1365fb8: StoreField: r1->field_f = r0
    //     0x1365fb8: stur            w0, [x1, #0xf]
    // 0x1365fbc: r0 = Instance_MainAxisAlignment
    //     0x1365fbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1365fc0: ldr             x0, [x0, #0xa08]
    // 0x1365fc4: StoreField: r1->field_13 = r0
    //     0x1365fc4: stur            w0, [x1, #0x13]
    // 0x1365fc8: r2 = Instance_MainAxisSize
    //     0x1365fc8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1365fcc: ldr             x2, [x2, #0xdd0]
    // 0x1365fd0: ArrayStore: r1[0] = r2  ; List_4
    //     0x1365fd0: stur            w2, [x1, #0x17]
    // 0x1365fd4: r3 = Instance_CrossAxisAlignment
    //     0x1365fd4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1365fd8: ldr             x3, [x3, #0xa18]
    // 0x1365fdc: StoreField: r1->field_1b = r3
    //     0x1365fdc: stur            w3, [x1, #0x1b]
    // 0x1365fe0: r4 = Instance_VerticalDirection
    //     0x1365fe0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1365fe4: ldr             x4, [x4, #0xa20]
    // 0x1365fe8: StoreField: r1->field_23 = r4
    //     0x1365fe8: stur            w4, [x1, #0x23]
    // 0x1365fec: r5 = Instance_Clip
    //     0x1365fec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1365ff0: ldr             x5, [x5, #0x38]
    // 0x1365ff4: StoreField: r1->field_2b = r5
    //     0x1365ff4: stur            w5, [x1, #0x2b]
    // 0x1365ff8: StoreField: r1->field_2f = rZR
    //     0x1365ff8: stur            xzr, [x1, #0x2f]
    // 0x1365ffc: ldur            x6, [fp, #-8]
    // 0x1366000: StoreField: r1->field_b = r6
    //     0x1366000: stur            w6, [x1, #0xb]
    // 0x1366004: ldur            d0, [fp, #-0x40]
    // 0x1366008: r6 = inline_Allocate_Double()
    //     0x1366008: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x136600c: add             x6, x6, #0x10
    //     0x1366010: cmp             x7, x6
    //     0x1366014: b.ls            #0x1366184
    //     0x1366018: str             x6, [THR, #0x50]  ; THR::top
    //     0x136601c: sub             x6, x6, #0xf
    //     0x1366020: movz            x7, #0xe15c
    //     0x1366024: movk            x7, #0x3, lsl #16
    //     0x1366028: stur            x7, [x6, #-1]
    // 0x136602c: StoreField: r6->field_7 = d0
    //     0x136602c: stur            d0, [x6, #7]
    // 0x1366030: stur            x6, [fp, #-8]
    // 0x1366034: r0 = Container()
    //     0x1366034: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1366038: stur            x0, [fp, #-0x20]
    // 0x136603c: r16 = Instance_Alignment
    //     0x136603c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1366040: ldr             x16, [x16, #0xb10]
    // 0x1366044: ldur            lr, [fp, #-8]
    // 0x1366048: stp             lr, x16, [SP, #0x10]
    // 0x136604c: r16 = Instance_BoxDecoration
    //     0x136604c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x1366050: ldr             x16, [x16, #0x768]
    // 0x1366054: ldur            lr, [fp, #-0x18]
    // 0x1366058: stp             lr, x16, [SP]
    // 0x136605c: mov             x1, x0
    // 0x1366060: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x1366060: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x1366064: ldr             x4, [x4, #0x770]
    // 0x1366068: r0 = Container()
    //     0x1366068: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x136606c: r1 = Null
    //     0x136606c: mov             x1, NULL
    // 0x1366070: r2 = 4
    //     0x1366070: movz            x2, #0x4
    // 0x1366074: r0 = AllocateArray()
    //     0x1366074: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1366078: mov             x2, x0
    // 0x136607c: ldur            x0, [fp, #-0x30]
    // 0x1366080: stur            x2, [fp, #-8]
    // 0x1366084: StoreField: r2->field_f = r0
    //     0x1366084: stur            w0, [x2, #0xf]
    // 0x1366088: ldur            x0, [fp, #-0x20]
    // 0x136608c: StoreField: r2->field_13 = r0
    //     0x136608c: stur            w0, [x2, #0x13]
    // 0x1366090: r1 = <Widget>
    //     0x1366090: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1366094: r0 = AllocateGrowableArray()
    //     0x1366094: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1366098: mov             x1, x0
    // 0x136609c: ldur            x0, [fp, #-8]
    // 0x13660a0: stur            x1, [fp, #-0x18]
    // 0x13660a4: StoreField: r1->field_f = r0
    //     0x13660a4: stur            w0, [x1, #0xf]
    // 0x13660a8: r0 = 4
    //     0x13660a8: movz            x0, #0x4
    // 0x13660ac: StoreField: r1->field_b = r0
    //     0x13660ac: stur            w0, [x1, #0xb]
    // 0x13660b0: r0 = Column()
    //     0x13660b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13660b4: mov             x1, x0
    // 0x13660b8: r0 = Instance_Axis
    //     0x13660b8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13660bc: stur            x1, [fp, #-8]
    // 0x13660c0: StoreField: r1->field_f = r0
    //     0x13660c0: stur            w0, [x1, #0xf]
    // 0x13660c4: r0 = Instance_MainAxisAlignment
    //     0x13660c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13660c8: ldr             x0, [x0, #0xa08]
    // 0x13660cc: StoreField: r1->field_13 = r0
    //     0x13660cc: stur            w0, [x1, #0x13]
    // 0x13660d0: r0 = Instance_MainAxisSize
    //     0x13660d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13660d4: ldr             x0, [x0, #0xdd0]
    // 0x13660d8: ArrayStore: r1[0] = r0  ; List_4
    //     0x13660d8: stur            w0, [x1, #0x17]
    // 0x13660dc: r0 = Instance_CrossAxisAlignment
    //     0x13660dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13660e0: ldr             x0, [x0, #0xa18]
    // 0x13660e4: StoreField: r1->field_1b = r0
    //     0x13660e4: stur            w0, [x1, #0x1b]
    // 0x13660e8: r0 = Instance_VerticalDirection
    //     0x13660e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13660ec: ldr             x0, [x0, #0xa20]
    // 0x13660f0: StoreField: r1->field_23 = r0
    //     0x13660f0: stur            w0, [x1, #0x23]
    // 0x13660f4: r0 = Instance_Clip
    //     0x13660f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13660f8: ldr             x0, [x0, #0x38]
    // 0x13660fc: StoreField: r1->field_2b = r0
    //     0x13660fc: stur            w0, [x1, #0x2b]
    // 0x1366100: StoreField: r1->field_2f = rZR
    //     0x1366100: stur            xzr, [x1, #0x2f]
    // 0x1366104: ldur            x0, [fp, #-0x18]
    // 0x1366108: StoreField: r1->field_b = r0
    //     0x1366108: stur            w0, [x1, #0xb]
    // 0x136610c: r0 = Padding()
    //     0x136610c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1366110: mov             x1, x0
    // 0x1366114: ldur            x0, [fp, #-0x10]
    // 0x1366118: stur            x1, [fp, #-0x18]
    // 0x136611c: StoreField: r1->field_f = r0
    //     0x136611c: stur            w0, [x1, #0xf]
    // 0x1366120: ldur            x0, [fp, #-8]
    // 0x1366124: StoreField: r1->field_b = r0
    //     0x1366124: stur            w0, [x1, #0xb]
    // 0x1366128: r0 = SafeArea()
    //     0x1366128: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x136612c: r1 = true
    //     0x136612c: add             x1, NULL, #0x20  ; true
    // 0x1366130: StoreField: r0->field_b = r1
    //     0x1366130: stur            w1, [x0, #0xb]
    // 0x1366134: StoreField: r0->field_f = r1
    //     0x1366134: stur            w1, [x0, #0xf]
    // 0x1366138: StoreField: r0->field_13 = r1
    //     0x1366138: stur            w1, [x0, #0x13]
    // 0x136613c: ArrayStore: r0[0] = r1  ; List_4
    //     0x136613c: stur            w1, [x0, #0x17]
    // 0x1366140: r1 = Instance_EdgeInsets
    //     0x1366140: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1366144: StoreField: r0->field_1b = r1
    //     0x1366144: stur            w1, [x0, #0x1b]
    // 0x1366148: r1 = false
    //     0x1366148: add             x1, NULL, #0x30  ; false
    // 0x136614c: StoreField: r0->field_1f = r1
    //     0x136614c: stur            w1, [x0, #0x1f]
    // 0x1366150: ldur            x1, [fp, #-0x18]
    // 0x1366154: StoreField: r0->field_23 = r1
    //     0x1366154: stur            w1, [x0, #0x23]
    // 0x1366158: LeaveFrame
    //     0x1366158: mov             SP, fp
    //     0x136615c: ldp             fp, lr, [SP], #0x10
    // 0x1366160: ret
    //     0x1366160: ret             
    // 0x1366164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1366164: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1366168: b               #0x1365ba0
    // 0x136616c: SaveReg d0
    //     0x136616c: str             q0, [SP, #-0x10]!
    // 0x1366170: SaveReg r1
    //     0x1366170: str             x1, [SP, #-8]!
    // 0x1366174: r0 = AllocateDouble()
    //     0x1366174: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1366178: RestoreReg r1
    //     0x1366178: ldr             x1, [SP], #8
    // 0x136617c: RestoreReg d0
    //     0x136617c: ldr             q0, [SP], #0x10
    // 0x1366180: b               #0x1365e80
    // 0x1366184: SaveReg d0
    //     0x1366184: str             q0, [SP, #-0x10]!
    // 0x1366188: stp             x4, x5, [SP, #-0x10]!
    // 0x136618c: stp             x2, x3, [SP, #-0x10]!
    // 0x1366190: stp             x0, x1, [SP, #-0x10]!
    // 0x1366194: r0 = AllocateDouble()
    //     0x1366194: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1366198: mov             x6, x0
    // 0x136619c: ldp             x0, x1, [SP], #0x10
    // 0x13661a0: ldp             x2, x3, [SP], #0x10
    // 0x13661a4: ldp             x4, x5, [SP], #0x10
    // 0x13661a8: RestoreReg d0
    //     0x13661a8: ldr             q0, [SP], #0x10
    // 0x13661ac: b               #0x136602c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13661b0, size: 0x150
    // 0x13661b0: EnterFrame
    //     0x13661b0: stp             fp, lr, [SP, #-0x10]!
    //     0x13661b4: mov             fp, SP
    // 0x13661b8: AllocStack(0x38)
    //     0x13661b8: sub             SP, SP, #0x38
    // 0x13661bc: SetupParameters()
    //     0x13661bc: ldr             x0, [fp, #0x10]
    //     0x13661c0: ldur            w2, [x0, #0x17]
    //     0x13661c4: add             x2, x2, HEAP, lsl #32
    //     0x13661c8: stur            x2, [fp, #-8]
    // 0x13661cc: CheckStackOverflow
    //     0x13661cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13661d0: cmp             SP, x16
    //     0x13661d4: b.ls            #0x13662f8
    // 0x13661d8: LoadField: r1 = r2->field_f
    //     0x13661d8: ldur            w1, [x2, #0xf]
    // 0x13661dc: DecompressPointer r1
    //     0x13661dc: add             x1, x1, HEAP, lsl #32
    // 0x13661e0: r0 = controller()
    //     0x13661e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13661e4: mov             x1, x0
    // 0x13661e8: r0 = headerConfigData()
    //     0x13661e8: bl              #0x8a3724  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData
    // 0x13661ec: LoadField: r1 = r0->field_3f
    //     0x13661ec: ldur            w1, [x0, #0x3f]
    // 0x13661f0: DecompressPointer r1
    //     0x13661f0: add             x1, x1, HEAP, lsl #32
    // 0x13661f4: cmp             w1, NULL
    // 0x13661f8: b.ne            #0x1366204
    // 0x13661fc: r0 = Null
    //     0x13661fc: mov             x0, NULL
    // 0x1366200: b               #0x136620c
    // 0x1366204: LoadField: r0 = r1->field_37
    //     0x1366204: ldur            w0, [x1, #0x37]
    // 0x1366208: DecompressPointer r0
    //     0x1366208: add             x0, x0, HEAP, lsl #32
    // 0x136620c: r1 = LoadClassIdInstr(r0)
    //     0x136620c: ldur            x1, [x0, #-1]
    //     0x1366210: ubfx            x1, x1, #0xc, #0x14
    // 0x1366214: r16 = "IQHEcgdx"
    //     0x1366214: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d8f8] "IQHEcgdx"
    //     0x1366218: ldr             x16, [x16, #0x8f8]
    // 0x136621c: stp             x16, x0, [SP]
    // 0x1366220: mov             x0, x1
    // 0x1366224: mov             lr, x0
    // 0x1366228: ldr             lr, [x21, lr, lsl #3]
    // 0x136622c: blr             lr
    // 0x1366230: tbnz            w0, #4, #0x1366250
    // 0x1366234: ldur            x0, [fp, #-8]
    // 0x1366238: LoadField: r1 = r0->field_f
    //     0x1366238: ldur            w1, [x0, #0xf]
    // 0x136623c: DecompressPointer r1
    //     0x136623c: add             x1, x1, HEAP, lsl #32
    // 0x1366240: r0 = controller()
    //     0x1366240: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366244: mov             x1, x0
    // 0x1366248: r0 = openAddressPage()
    //     0x1366248: bl              #0x12ea18c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::openAddressPage
    // 0x136624c: b               #0x13662e8
    // 0x1366250: ldur            x0, [fp, #-8]
    // 0x1366254: LoadField: r1 = r0->field_f
    //     0x1366254: ldur            w1, [x0, #0xf]
    // 0x1366258: DecompressPointer r1
    //     0x1366258: add             x1, x1, HEAP, lsl #32
    // 0x136625c: r0 = controller()
    //     0x136625c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366260: mov             x2, x0
    // 0x1366264: ldur            x0, [fp, #-8]
    // 0x1366268: stur            x2, [fp, #-0x10]
    // 0x136626c: LoadField: r1 = r0->field_f
    //     0x136626c: ldur            w1, [x0, #0xf]
    // 0x1366270: DecompressPointer r1
    //     0x1366270: add             x1, x1, HEAP, lsl #32
    // 0x1366274: r0 = controller()
    //     0x1366274: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1366278: mov             x1, x0
    // 0x136627c: r0 = userData()
    //     0x136627c: bl              #0x8a9718  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::userData
    // 0x1366280: ldur            x1, [fp, #-0x10]
    // 0x1366284: mov             x2, x0
    // 0x1366288: r0 = checkAddress()
    //     0x1366288: bl              #0x12e8118  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkAddress
    // 0x136628c: ldur            x0, [fp, #-8]
    // 0x1366290: LoadField: r1 = r0->field_f
    //     0x1366290: ldur            w1, [x0, #0xf]
    // 0x1366294: DecompressPointer r1
    //     0x1366294: add             x1, x1, HEAP, lsl #32
    // 0x1366298: r0 = controller()
    //     0x1366298: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136629c: r16 = "landing_page"
    //     0x136629c: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x13662a0: ldr             x16, [x16, #0x638]
    // 0x13662a4: r30 = "request_number"
    //     0x13662a4: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x13662a8: ldr             lr, [lr, #0x900]
    // 0x13662ac: stp             lr, x16, [SP, #0x18]
    // 0x13662b0: r16 = "page_cta"
    //     0x13662b0: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c780] "page_cta"
    //     0x13662b4: ldr             x16, [x16, #0x780]
    // 0x13662b8: r30 = "CONTINUE"
    //     0x13662b8: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x13662bc: ldr             lr, [lr, #0xac8]
    // 0x13662c0: stp             lr, x16, [SP, #8]
    // 0x13662c4: r16 = "continue_cta"
    //     0x13662c4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d908] "continue_cta"
    //     0x13662c8: ldr             x16, [x16, #0x908]
    // 0x13662cc: str             x16, [SP]
    // 0x13662d0: mov             x1, x0
    // 0x13662d4: r2 = "checkout_cta_clicked"
    //     0x13662d4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x13662d8: ldr             x2, [x2, #0x7b0]
    // 0x13662dc: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, pageId, 0x3, pageType, 0x2, widgetType, 0x6, null]
    //     0x13662dc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d910] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "pageId", 0x3, "pageType", 0x2, "widgetType", 0x6, Null]
    //     0x13662e0: ldr             x4, [x4, #0x910]
    // 0x13662e4: r0 = checkoutPostEvent()
    //     0x13662e4: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x13662e8: r0 = Null
    //     0x13662e8: mov             x0, NULL
    // 0x13662ec: LeaveFrame
    //     0x13662ec: mov             SP, fp
    //     0x13662f0: ldp             fp, lr, [SP], #0x10
    // 0x13662f4: ret
    //     0x13662f4: ret             
    // 0x13662f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13662f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13662fc: b               #0x13661d8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13913a8, size: 0x98
    // 0x13913a8: EnterFrame
    //     0x13913a8: stp             fp, lr, [SP, #-0x10]!
    //     0x13913ac: mov             fp, SP
    // 0x13913b0: AllocStack(0x28)
    //     0x13913b0: sub             SP, SP, #0x28
    // 0x13913b4: SetupParameters()
    //     0x13913b4: ldr             x0, [fp, #0x10]
    //     0x13913b8: ldur            w1, [x0, #0x17]
    //     0x13913bc: add             x1, x1, HEAP, lsl #32
    // 0x13913c0: CheckStackOverflow
    //     0x13913c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13913c4: cmp             SP, x16
    //     0x13913c8: b.ls            #0x1391438
    // 0x13913cc: LoadField: r0 = r1->field_f
    //     0x13913cc: ldur            w0, [x1, #0xf]
    // 0x13913d0: DecompressPointer r0
    //     0x13913d0: add             x0, x0, HEAP, lsl #32
    // 0x13913d4: mov             x1, x0
    // 0x13913d8: r0 = controller()
    //     0x13913d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13913dc: r16 = "landing_page"
    //     0x13913dc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x13913e0: ldr             x16, [x16, #0x638]
    // 0x13913e4: r30 = "request_number"
    //     0x13913e4: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x13913e8: ldr             lr, [lr, #0x900]
    // 0x13913ec: stp             lr, x16, [SP, #0x18]
    // 0x13913f0: r16 = "contact_details"
    //     0x13913f0: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3caf8] "contact_details"
    //     0x13913f4: ldr             x16, [x16, #0xaf8]
    // 0x13913f8: r30 = "contact_number"
    //     0x13913f8: add             lr, PP, #0xf, lsl #12  ; [pp+0xfce8] "contact_number"
    //     0x13913fc: ldr             lr, [lr, #0xce8]
    // 0x1391400: stp             lr, x16, [SP, #8]
    // 0x1391404: r16 = "contactNumber"
    //     0x1391404: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dad8] "contactNumber"
    //     0x1391408: ldr             x16, [x16, #0xad8]
    // 0x139140c: str             x16, [SP]
    // 0x1391410: mov             x1, x0
    // 0x1391414: r2 = "checkout_widget_fill"
    //     0x1391414: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d5c8] "checkout_widget_fill"
    //     0x1391418: ldr             x2, [x2, #0x5c8]
    // 0x139141c: r4 = const [0, 0x7, 0x5, 0x2, pageId, 0x3, pageType, 0x2, widgetField, 0x6, widgetId, 0x5, widgetType, 0x4, null]
    //     0x139141c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3dae0] List(15) [0, 0x7, 0x5, 0x2, "pageId", 0x3, "pageType", 0x2, "widgetField", 0x6, "widgetId", 0x5, "widgetType", 0x4, Null]
    //     0x1391420: ldr             x4, [x4, #0xae0]
    // 0x1391424: r0 = checkoutPostEvent()
    //     0x1391424: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x1391428: r0 = Null
    //     0x1391428: mov             x0, NULL
    // 0x139142c: LeaveFrame
    //     0x139142c: mov             SP, fp
    //     0x1391430: ldp             fp, lr, [SP], #0x10
    // 0x1391434: ret
    //     0x1391434: ret             
    // 0x1391438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1391438: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x139143c: b               #0x13913cc
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x1391440, size: 0x414
    // 0x1391440: EnterFrame
    //     0x1391440: stp             fp, lr, [SP, #-0x10]!
    //     0x1391444: mov             fp, SP
    // 0x1391448: AllocStack(0x50)
    //     0x1391448: sub             SP, SP, #0x50
    // 0x139144c: SetupParameters()
    //     0x139144c: ldr             x0, [fp, #0x10]
    //     0x1391450: ldur            w2, [x0, #0x17]
    //     0x1391454: add             x2, x2, HEAP, lsl #32
    //     0x1391458: stur            x2, [fp, #-8]
    // 0x139145c: CheckStackOverflow
    //     0x139145c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1391460: cmp             SP, x16
    //     0x1391464: b.ls            #0x139184c
    // 0x1391468: LoadField: r1 = r2->field_f
    //     0x1391468: ldur            w1, [x2, #0xf]
    // 0x139146c: DecompressPointer r1
    //     0x139146c: add             x1, x1, HEAP, lsl #32
    // 0x1391470: r0 = controller()
    //     0x1391470: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391474: LoadField: r1 = r0->field_d7
    //     0x1391474: ldur            w1, [x0, #0xd7]
    // 0x1391478: DecompressPointer r1
    //     0x1391478: add             x1, x1, HEAP, lsl #32
    // 0x139147c: r0 = value()
    //     0x139147c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1391480: tbnz            w0, #4, #0x13915b8
    // 0x1391484: ldur            x2, [fp, #-8]
    // 0x1391488: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x1391488: ldur            w0, [x2, #0x17]
    // 0x139148c: DecompressPointer r0
    //     0x139148c: add             x0, x0, HEAP, lsl #32
    // 0x1391490: LoadField: r1 = r0->field_2b
    //     0x1391490: ldur            w1, [x0, #0x2b]
    // 0x1391494: DecompressPointer r1
    //     0x1391494: add             x1, x1, HEAP, lsl #32
    // 0x1391498: r16 = 12.000000
    //     0x1391498: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x139149c: ldr             x16, [x16, #0x9e8]
    // 0x13914a0: r30 = Instance_Color
    //     0x13914a0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13914a4: stp             lr, x16, [SP]
    // 0x13914a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13914a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13914ac: ldr             x4, [x4, #0xaa0]
    // 0x13914b0: r0 = copyWith()
    //     0x13914b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13914b4: stur            x0, [fp, #-0x10]
    // 0x13914b8: r0 = Text()
    //     0x13914b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13914bc: mov             x3, x0
    // 0x13914c0: r0 = "Fetching your saved addresses from ShopDeck"
    //     0x13914c0: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3daa0] "Fetching your saved addresses from ShopDeck"
    //     0x13914c4: ldr             x0, [x0, #0xaa0]
    // 0x13914c8: stur            x3, [fp, #-0x18]
    // 0x13914cc: StoreField: r3->field_b = r0
    //     0x13914cc: stur            w0, [x3, #0xb]
    // 0x13914d0: ldur            x0, [fp, #-0x10]
    // 0x13914d4: StoreField: r3->field_13 = r0
    //     0x13914d4: stur            w0, [x3, #0x13]
    // 0x13914d8: r0 = Instance_TextAlign
    //     0x13914d8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x13914dc: StoreField: r3->field_1b = r0
    //     0x13914dc: stur            w0, [x3, #0x1b]
    // 0x13914e0: r1 = Null
    //     0x13914e0: mov             x1, NULL
    // 0x13914e4: r2 = 6
    //     0x13914e4: movz            x2, #0x6
    // 0x13914e8: r0 = AllocateArray()
    //     0x13914e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13914ec: stur            x0, [fp, #-0x10]
    // 0x13914f0: r16 = Instance_CircularProgressIndicator
    //     0x13914f0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3daa8] Obj!CircularProgressIndicator@d65741
    //     0x13914f4: ldr             x16, [x16, #0xaa8]
    // 0x13914f8: StoreField: r0->field_f = r16
    //     0x13914f8: stur            w16, [x0, #0xf]
    // 0x13914fc: r16 = Instance_SizedBox
    //     0x13914fc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x1391500: ldr             x16, [x16, #0xd68]
    // 0x1391504: StoreField: r0->field_13 = r16
    //     0x1391504: stur            w16, [x0, #0x13]
    // 0x1391508: ldur            x1, [fp, #-0x18]
    // 0x139150c: ArrayStore: r0[0] = r1  ; List_4
    //     0x139150c: stur            w1, [x0, #0x17]
    // 0x1391510: r1 = <Widget>
    //     0x1391510: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1391514: r0 = AllocateGrowableArray()
    //     0x1391514: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1391518: mov             x1, x0
    // 0x139151c: ldur            x0, [fp, #-0x10]
    // 0x1391520: stur            x1, [fp, #-0x18]
    // 0x1391524: StoreField: r1->field_f = r0
    //     0x1391524: stur            w0, [x1, #0xf]
    // 0x1391528: r0 = 6
    //     0x1391528: movz            x0, #0x6
    // 0x139152c: StoreField: r1->field_b = r0
    //     0x139152c: stur            w0, [x1, #0xb]
    // 0x1391530: r0 = Column()
    //     0x1391530: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1391534: mov             x1, x0
    // 0x1391538: r0 = Instance_Axis
    //     0x1391538: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x139153c: stur            x1, [fp, #-0x10]
    // 0x1391540: StoreField: r1->field_f = r0
    //     0x1391540: stur            w0, [x1, #0xf]
    // 0x1391544: r0 = Instance_MainAxisAlignment
    //     0x1391544: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x1391548: ldr             x0, [x0, #0xab0]
    // 0x139154c: StoreField: r1->field_13 = r0
    //     0x139154c: stur            w0, [x1, #0x13]
    // 0x1391550: r3 = Instance_MainAxisSize
    //     0x1391550: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1391554: ldr             x3, [x3, #0xa10]
    // 0x1391558: ArrayStore: r1[0] = r3  ; List_4
    //     0x1391558: stur            w3, [x1, #0x17]
    // 0x139155c: r4 = Instance_CrossAxisAlignment
    //     0x139155c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1391560: ldr             x4, [x4, #0xa18]
    // 0x1391564: StoreField: r1->field_1b = r4
    //     0x1391564: stur            w4, [x1, #0x1b]
    // 0x1391568: r5 = Instance_VerticalDirection
    //     0x1391568: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x139156c: ldr             x5, [x5, #0xa20]
    // 0x1391570: StoreField: r1->field_23 = r5
    //     0x1391570: stur            w5, [x1, #0x23]
    // 0x1391574: r6 = Instance_Clip
    //     0x1391574: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1391578: ldr             x6, [x6, #0x38]
    // 0x139157c: StoreField: r1->field_2b = r6
    //     0x139157c: stur            w6, [x1, #0x2b]
    // 0x1391580: StoreField: r1->field_2f = rZR
    //     0x1391580: stur            xzr, [x1, #0x2f]
    // 0x1391584: ldur            x0, [fp, #-0x18]
    // 0x1391588: StoreField: r1->field_b = r0
    //     0x1391588: stur            w0, [x1, #0xb]
    // 0x139158c: r0 = Center()
    //     0x139158c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1391590: mov             x1, x0
    // 0x1391594: r0 = Instance_Alignment
    //     0x1391594: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1391598: ldr             x0, [x0, #0xb10]
    // 0x139159c: StoreField: r1->field_f = r0
    //     0x139159c: stur            w0, [x1, #0xf]
    // 0x13915a0: ldur            x0, [fp, #-0x10]
    // 0x13915a4: StoreField: r1->field_b = r0
    //     0x13915a4: stur            w0, [x1, #0xb]
    // 0x13915a8: mov             x0, x1
    // 0x13915ac: LeaveFrame
    //     0x13915ac: mov             SP, fp
    //     0x13915b0: ldp             fp, lr, [SP], #0x10
    // 0x13915b4: ret
    //     0x13915b4: ret             
    // 0x13915b8: ldur            x2, [fp, #-8]
    // 0x13915bc: r4 = Instance_CrossAxisAlignment
    //     0x13915bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13915c0: ldr             x4, [x4, #0xa18]
    // 0x13915c4: r3 = Instance_MainAxisSize
    //     0x13915c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13915c8: ldr             x3, [x3, #0xa10]
    // 0x13915cc: r5 = Instance_VerticalDirection
    //     0x13915cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13915d0: ldr             x5, [x5, #0xa20]
    // 0x13915d4: r0 = Instance_Axis
    //     0x13915d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13915d8: r6 = Instance_Clip
    //     0x13915d8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13915dc: ldr             x6, [x6, #0x38]
    // 0x13915e0: r0 = Obx()
    //     0x13915e0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13915e4: ldur            x2, [fp, #-8]
    // 0x13915e8: r1 = Function '<anonymous closure>':.
    //     0x13915e8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dab0] AnonymousClosure: (0x1391ea0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x13915ec: ldr             x1, [x1, #0xab0]
    // 0x13915f0: stur            x0, [fp, #-0x10]
    // 0x13915f4: r0 = AllocateClosure()
    //     0x13915f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13915f8: mov             x1, x0
    // 0x13915fc: ldur            x0, [fp, #-0x10]
    // 0x1391600: StoreField: r0->field_b = r1
    //     0x1391600: stur            w1, [x0, #0xb]
    // 0x1391604: r0 = Obx()
    //     0x1391604: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1391608: ldur            x2, [fp, #-8]
    // 0x139160c: r1 = Function '<anonymous closure>':.
    //     0x139160c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dab8] AnonymousClosure: (0x1391e30), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1391610: ldr             x1, [x1, #0xab8]
    // 0x1391614: stur            x0, [fp, #-0x18]
    // 0x1391618: r0 = AllocateClosure()
    //     0x1391618: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x139161c: mov             x1, x0
    // 0x1391620: ldur            x0, [fp, #-0x18]
    // 0x1391624: StoreField: r0->field_b = r1
    //     0x1391624: stur            w1, [x0, #0xb]
    // 0x1391628: ldur            x2, [fp, #-8]
    // 0x139162c: LoadField: r1 = r2->field_f
    //     0x139162c: ldur            w1, [x2, #0xf]
    // 0x1391630: DecompressPointer r1
    //     0x1391630: add             x1, x1, HEAP, lsl #32
    // 0x1391634: r0 = controller()
    //     0x1391634: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391638: LoadField: r1 = r0->field_5f
    //     0x1391638: ldur            w1, [x0, #0x5f]
    // 0x139163c: DecompressPointer r1
    //     0x139163c: add             x1, x1, HEAP, lsl #32
    // 0x1391640: r0 = value()
    //     0x1391640: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1391644: ldur            x2, [fp, #-8]
    // 0x1391648: stur            x0, [fp, #-0x20]
    // 0x139164c: LoadField: r1 = r2->field_f
    //     0x139164c: ldur            w1, [x2, #0xf]
    // 0x1391650: DecompressPointer r1
    //     0x1391650: add             x1, x1, HEAP, lsl #32
    // 0x1391654: r0 = controller()
    //     0x1391654: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391658: LoadField: r1 = r0->field_5b
    //     0x1391658: ldur            w1, [x0, #0x5b]
    // 0x139165c: DecompressPointer r1
    //     0x139165c: add             x1, x1, HEAP, lsl #32
    // 0x1391660: r0 = value()
    //     0x1391660: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1391664: ldur            x2, [fp, #-8]
    // 0x1391668: stur            x0, [fp, #-0x28]
    // 0x139166c: LoadField: r1 = r2->field_f
    //     0x139166c: ldur            w1, [x2, #0xf]
    // 0x1391670: DecompressPointer r1
    //     0x1391670: add             x1, x1, HEAP, lsl #32
    // 0x1391674: r0 = controller()
    //     0x1391674: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391678: LoadField: r1 = r0->field_53
    //     0x1391678: ldur            w1, [x0, #0x53]
    // 0x139167c: DecompressPointer r1
    //     0x139167c: add             x1, x1, HEAP, lsl #32
    // 0x1391680: r0 = value()
    //     0x1391680: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1391684: ldur            x2, [fp, #-8]
    // 0x1391688: LoadField: r1 = r2->field_f
    //     0x1391688: ldur            w1, [x2, #0xf]
    // 0x139168c: DecompressPointer r1
    //     0x139168c: add             x1, x1, HEAP, lsl #32
    // 0x1391690: r0 = controller()
    //     0x1391690: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391694: LoadField: r1 = r0->field_93
    //     0x1391694: ldur            w1, [x0, #0x93]
    // 0x1391698: DecompressPointer r1
    //     0x1391698: add             x1, x1, HEAP, lsl #32
    // 0x139169c: stur            x1, [fp, #-0x30]
    // 0x13916a0: r0 = CheckoutBagAccordion()
    //     0x13916a0: bl              #0x1390ab8  ; AllocateCheckoutBagAccordionStub -> CheckoutBagAccordion (size=0x1c)
    // 0x13916a4: mov             x3, x0
    // 0x13916a8: ldur            x0, [fp, #-0x20]
    // 0x13916ac: stur            x3, [fp, #-0x38]
    // 0x13916b0: StoreField: r3->field_b = r0
    //     0x13916b0: stur            w0, [x3, #0xb]
    // 0x13916b4: ldur            x0, [fp, #-0x28]
    // 0x13916b8: StoreField: r3->field_f = r0
    //     0x13916b8: stur            w0, [x3, #0xf]
    // 0x13916bc: ldur            x0, [fp, #-0x30]
    // 0x13916c0: StoreField: r3->field_13 = r0
    //     0x13916c0: stur            w0, [x3, #0x13]
    // 0x13916c4: ldur            x2, [fp, #-8]
    // 0x13916c8: r1 = Function '<anonymous closure>':.
    //     0x13916c8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dac0] AnonymousClosure: (0x1391d98), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x13916cc: ldr             x1, [x1, #0xac0]
    // 0x13916d0: r0 = AllocateClosure()
    //     0x13916d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13916d4: mov             x1, x0
    // 0x13916d8: ldur            x0, [fp, #-0x38]
    // 0x13916dc: ArrayStore: r0[0] = r1  ; List_4
    //     0x13916dc: stur            w1, [x0, #0x17]
    // 0x13916e0: ldur            x2, [fp, #-8]
    // 0x13916e4: LoadField: r1 = r2->field_f
    //     0x13916e4: ldur            w1, [x2, #0xf]
    // 0x13916e8: DecompressPointer r1
    //     0x13916e8: add             x1, x1, HEAP, lsl #32
    // 0x13916ec: r0 = controller()
    //     0x13916ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13916f0: LoadField: r1 = r0->field_7b
    //     0x13916f0: ldur            w1, [x0, #0x7b]
    // 0x13916f4: DecompressPointer r1
    //     0x13916f4: add             x1, x1, HEAP, lsl #32
    // 0x13916f8: r0 = value()
    //     0x13916f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13916fc: ldur            x2, [fp, #-8]
    // 0x1391700: stur            x0, [fp, #-0x20]
    // 0x1391704: LoadField: r1 = r2->field_f
    //     0x1391704: ldur            w1, [x2, #0xf]
    // 0x1391708: DecompressPointer r1
    //     0x1391708: add             x1, x1, HEAP, lsl #32
    // 0x139170c: r0 = controller()
    //     0x139170c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391710: LoadField: r1 = r0->field_cb
    //     0x1391710: ldur            w1, [x0, #0xcb]
    // 0x1391714: DecompressPointer r1
    //     0x1391714: add             x1, x1, HEAP, lsl #32
    // 0x1391718: r0 = value()
    //     0x1391718: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x139171c: cmp             w0, NULL
    // 0x1391720: b.ne            #0x139172c
    // 0x1391724: r6 = false
    //     0x1391724: add             x6, NULL, #0x30  ; false
    // 0x1391728: b               #0x1391730
    // 0x139172c: mov             x6, x0
    // 0x1391730: ldur            x5, [fp, #-0x10]
    // 0x1391734: ldur            x4, [fp, #-0x18]
    // 0x1391738: ldur            x3, [fp, #-0x38]
    // 0x139173c: ldur            x0, [fp, #-0x20]
    // 0x1391740: ldur            x2, [fp, #-8]
    // 0x1391744: stur            x6, [fp, #-0x28]
    // 0x1391748: r1 = Function '<anonymous closure>':.
    //     0x1391748: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dac8] AnonymousClosure: (0x1391860), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x139174c: ldr             x1, [x1, #0xac8]
    // 0x1391750: r0 = AllocateClosure()
    //     0x1391750: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1391754: stur            x0, [fp, #-0x30]
    // 0x1391758: r0 = CheckoutNumberWidget()
    //     0x1391758: bl              #0x1391854  ; AllocateCheckoutNumberWidgetStub -> CheckoutNumberWidget (size=0x1c)
    // 0x139175c: mov             x3, x0
    // 0x1391760: ldur            x0, [fp, #-0x30]
    // 0x1391764: stur            x3, [fp, #-0x40]
    // 0x1391768: StoreField: r3->field_b = r0
    //     0x1391768: stur            w0, [x3, #0xb]
    // 0x139176c: ldur            x2, [fp, #-8]
    // 0x1391770: r1 = Function '<anonymous closure>':.
    //     0x1391770: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dad0] AnonymousClosure: (0x13913a8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1391774: ldr             x1, [x1, #0xad0]
    // 0x1391778: r0 = AllocateClosure()
    //     0x1391778: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x139177c: mov             x1, x0
    // 0x1391780: ldur            x0, [fp, #-0x40]
    // 0x1391784: StoreField: r0->field_f = r1
    //     0x1391784: stur            w1, [x0, #0xf]
    // 0x1391788: ldur            x1, [fp, #-0x20]
    // 0x139178c: StoreField: r0->field_13 = r1
    //     0x139178c: stur            w1, [x0, #0x13]
    // 0x1391790: ldur            x1, [fp, #-0x28]
    // 0x1391794: ArrayStore: r0[0] = r1  ; List_4
    //     0x1391794: stur            w1, [x0, #0x17]
    // 0x1391798: r1 = Null
    //     0x1391798: mov             x1, NULL
    // 0x139179c: r2 = 8
    //     0x139179c: movz            x2, #0x8
    // 0x13917a0: r0 = AllocateArray()
    //     0x13917a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13917a4: mov             x2, x0
    // 0x13917a8: ldur            x0, [fp, #-0x10]
    // 0x13917ac: stur            x2, [fp, #-8]
    // 0x13917b0: StoreField: r2->field_f = r0
    //     0x13917b0: stur            w0, [x2, #0xf]
    // 0x13917b4: ldur            x0, [fp, #-0x18]
    // 0x13917b8: StoreField: r2->field_13 = r0
    //     0x13917b8: stur            w0, [x2, #0x13]
    // 0x13917bc: ldur            x0, [fp, #-0x38]
    // 0x13917c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13917c0: stur            w0, [x2, #0x17]
    // 0x13917c4: ldur            x0, [fp, #-0x40]
    // 0x13917c8: StoreField: r2->field_1b = r0
    //     0x13917c8: stur            w0, [x2, #0x1b]
    // 0x13917cc: r1 = <Widget>
    //     0x13917cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13917d0: r0 = AllocateGrowableArray()
    //     0x13917d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13917d4: mov             x1, x0
    // 0x13917d8: ldur            x0, [fp, #-8]
    // 0x13917dc: stur            x1, [fp, #-0x10]
    // 0x13917e0: StoreField: r1->field_f = r0
    //     0x13917e0: stur            w0, [x1, #0xf]
    // 0x13917e4: r0 = 8
    //     0x13917e4: movz            x0, #0x8
    // 0x13917e8: StoreField: r1->field_b = r0
    //     0x13917e8: stur            w0, [x1, #0xb]
    // 0x13917ec: r0 = Column()
    //     0x13917ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13917f0: r1 = Instance_Axis
    //     0x13917f0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13917f4: StoreField: r0->field_f = r1
    //     0x13917f4: stur            w1, [x0, #0xf]
    // 0x13917f8: r1 = Instance_MainAxisAlignment
    //     0x13917f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13917fc: ldr             x1, [x1, #0xa08]
    // 0x1391800: StoreField: r0->field_13 = r1
    //     0x1391800: stur            w1, [x0, #0x13]
    // 0x1391804: r1 = Instance_MainAxisSize
    //     0x1391804: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1391808: ldr             x1, [x1, #0xa10]
    // 0x139180c: ArrayStore: r0[0] = r1  ; List_4
    //     0x139180c: stur            w1, [x0, #0x17]
    // 0x1391810: r1 = Instance_CrossAxisAlignment
    //     0x1391810: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1391814: ldr             x1, [x1, #0xa18]
    // 0x1391818: StoreField: r0->field_1b = r1
    //     0x1391818: stur            w1, [x0, #0x1b]
    // 0x139181c: r1 = Instance_VerticalDirection
    //     0x139181c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1391820: ldr             x1, [x1, #0xa20]
    // 0x1391824: StoreField: r0->field_23 = r1
    //     0x1391824: stur            w1, [x0, #0x23]
    // 0x1391828: r1 = Instance_Clip
    //     0x1391828: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x139182c: ldr             x1, [x1, #0x38]
    // 0x1391830: StoreField: r0->field_2b = r1
    //     0x1391830: stur            w1, [x0, #0x2b]
    // 0x1391834: StoreField: r0->field_2f = rZR
    //     0x1391834: stur            xzr, [x0, #0x2f]
    // 0x1391838: ldur            x1, [fp, #-0x10]
    // 0x139183c: StoreField: r0->field_b = r1
    //     0x139183c: stur            w1, [x0, #0xb]
    // 0x1391840: LeaveFrame
    //     0x1391840: mov             SP, fp
    //     0x1391844: ldp             fp, lr, [SP], #0x10
    // 0x1391848: ret
    //     0x1391848: ret             
    // 0x139184c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x139184c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1391850: b               #0x1391468
  }
  [closure] void <anonymous closure>(dynamic, String, bool) {
    // ** addr: 0x1391860, size: 0x14c
    // 0x1391860: EnterFrame
    //     0x1391860: stp             fp, lr, [SP, #-0x10]!
    //     0x1391864: mov             fp, SP
    // 0x1391868: AllocStack(0x18)
    //     0x1391868: sub             SP, SP, #0x18
    // 0x139186c: SetupParameters()
    //     0x139186c: ldr             x0, [fp, #0x20]
    //     0x1391870: ldur            w1, [x0, #0x17]
    //     0x1391874: add             x1, x1, HEAP, lsl #32
    //     0x1391878: stur            x1, [fp, #-8]
    // 0x139187c: CheckStackOverflow
    //     0x139187c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1391880: cmp             SP, x16
    //     0x1391884: b.ls            #0x13919a0
    // 0x1391888: r1 = 2
    //     0x1391888: movz            x1, #0x2
    // 0x139188c: r0 = AllocateContext()
    //     0x139188c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1391890: mov             x1, x0
    // 0x1391894: ldur            x0, [fp, #-8]
    // 0x1391898: StoreField: r1->field_b = r0
    //     0x1391898: stur            w0, [x1, #0xb]
    // 0x139189c: ldr             x0, [fp, #0x18]
    // 0x13918a0: StoreField: r1->field_f = r0
    //     0x13918a0: stur            w0, [x1, #0xf]
    // 0x13918a4: ldr             x0, [fp, #0x10]
    // 0x13918a8: StoreField: r1->field_13 = r0
    //     0x13918a8: stur            w0, [x1, #0x13]
    // 0x13918ac: r0 = LoadStaticField(0x878)
    //     0x13918ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13918b0: ldr             x0, [x0, #0x10f0]
    // 0x13918b4: cmp             w0, NULL
    // 0x13918b8: b.eq            #0x13919a8
    // 0x13918bc: LoadField: r3 = r0->field_53
    //     0x13918bc: ldur            w3, [x0, #0x53]
    // 0x13918c0: DecompressPointer r3
    //     0x13918c0: add             x3, x3, HEAP, lsl #32
    // 0x13918c4: stur            x3, [fp, #-0x10]
    // 0x13918c8: LoadField: r0 = r3->field_7
    //     0x13918c8: ldur            w0, [x3, #7]
    // 0x13918cc: DecompressPointer r0
    //     0x13918cc: add             x0, x0, HEAP, lsl #32
    // 0x13918d0: mov             x2, x1
    // 0x13918d4: stur            x0, [fp, #-8]
    // 0x13918d8: r1 = Function '<anonymous closure>':.
    //     0x13918d8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dae8] AnonymousClosure: (0x13919ac), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x13918dc: ldr             x1, [x1, #0xae8]
    // 0x13918e0: r0 = AllocateClosure()
    //     0x13918e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13918e4: ldur            x2, [fp, #-8]
    // 0x13918e8: mov             x3, x0
    // 0x13918ec: r1 = Null
    //     0x13918ec: mov             x1, NULL
    // 0x13918f0: stur            x3, [fp, #-8]
    // 0x13918f4: cmp             w2, NULL
    // 0x13918f8: b.eq            #0x1391918
    // 0x13918fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13918fc: ldur            w4, [x2, #0x17]
    // 0x1391900: DecompressPointer r4
    //     0x1391900: add             x4, x4, HEAP, lsl #32
    // 0x1391904: r8 = X0
    //     0x1391904: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x1391908: LoadField: r9 = r4->field_7
    //     0x1391908: ldur            x9, [x4, #7]
    // 0x139190c: r3 = Null
    //     0x139190c: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3daf0] Null
    //     0x1391910: ldr             x3, [x3, #0xaf0]
    // 0x1391914: blr             x9
    // 0x1391918: ldur            x0, [fp, #-0x10]
    // 0x139191c: LoadField: r1 = r0->field_b
    //     0x139191c: ldur            w1, [x0, #0xb]
    // 0x1391920: LoadField: r2 = r0->field_f
    //     0x1391920: ldur            w2, [x0, #0xf]
    // 0x1391924: DecompressPointer r2
    //     0x1391924: add             x2, x2, HEAP, lsl #32
    // 0x1391928: LoadField: r3 = r2->field_b
    //     0x1391928: ldur            w3, [x2, #0xb]
    // 0x139192c: r2 = LoadInt32Instr(r1)
    //     0x139192c: sbfx            x2, x1, #1, #0x1f
    // 0x1391930: stur            x2, [fp, #-0x18]
    // 0x1391934: r1 = LoadInt32Instr(r3)
    //     0x1391934: sbfx            x1, x3, #1, #0x1f
    // 0x1391938: cmp             x2, x1
    // 0x139193c: b.ne            #0x1391948
    // 0x1391940: mov             x1, x0
    // 0x1391944: r0 = _growToNextCapacity()
    //     0x1391944: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1391948: ldur            x2, [fp, #-0x10]
    // 0x139194c: ldur            x3, [fp, #-0x18]
    // 0x1391950: add             x4, x3, #1
    // 0x1391954: lsl             x5, x4, #1
    // 0x1391958: StoreField: r2->field_b = r5
    //     0x1391958: stur            w5, [x2, #0xb]
    // 0x139195c: LoadField: r1 = r2->field_f
    //     0x139195c: ldur            w1, [x2, #0xf]
    // 0x1391960: DecompressPointer r1
    //     0x1391960: add             x1, x1, HEAP, lsl #32
    // 0x1391964: ldur            x0, [fp, #-8]
    // 0x1391968: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1391968: add             x25, x1, x3, lsl #2
    //     0x139196c: add             x25, x25, #0xf
    //     0x1391970: str             w0, [x25]
    //     0x1391974: tbz             w0, #0, #0x1391990
    //     0x1391978: ldurb           w16, [x1, #-1]
    //     0x139197c: ldurb           w17, [x0, #-1]
    //     0x1391980: and             x16, x17, x16, lsr #2
    //     0x1391984: tst             x16, HEAP, lsr #32
    //     0x1391988: b.eq            #0x1391990
    //     0x139198c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1391990: r0 = Null
    //     0x1391990: mov             x0, NULL
    // 0x1391994: LeaveFrame
    //     0x1391994: mov             SP, fp
    //     0x1391998: ldp             fp, lr, [SP], #0x10
    // 0x139199c: ret
    //     0x139199c: ret             
    // 0x13919a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13919a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13919a4: b               #0x1391888
    // 0x13919a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x13919a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x13919ac, size: 0xe8
    // 0x13919ac: EnterFrame
    //     0x13919ac: stp             fp, lr, [SP, #-0x10]!
    //     0x13919b0: mov             fp, SP
    // 0x13919b4: AllocStack(0x10)
    //     0x13919b4: sub             SP, SP, #0x10
    // 0x13919b8: SetupParameters()
    //     0x13919b8: ldr             x0, [fp, #0x18]
    //     0x13919bc: ldur            w2, [x0, #0x17]
    //     0x13919c0: add             x2, x2, HEAP, lsl #32
    //     0x13919c4: stur            x2, [fp, #-0x10]
    // 0x13919c8: CheckStackOverflow
    //     0x13919c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13919cc: cmp             SP, x16
    //     0x13919d0: b.ls            #0x1391a8c
    // 0x13919d4: LoadField: r0 = r2->field_b
    //     0x13919d4: ldur            w0, [x2, #0xb]
    // 0x13919d8: DecompressPointer r0
    //     0x13919d8: add             x0, x0, HEAP, lsl #32
    // 0x13919dc: stur            x0, [fp, #-8]
    // 0x13919e0: LoadField: r1 = r0->field_f
    //     0x13919e0: ldur            w1, [x0, #0xf]
    // 0x13919e4: DecompressPointer r1
    //     0x13919e4: add             x1, x1, HEAP, lsl #32
    // 0x13919e8: r0 = controller()
    //     0x13919e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13919ec: mov             x1, x0
    // 0x13919f0: ldur            x0, [fp, #-0x10]
    // 0x13919f4: LoadField: r2 = r0->field_f
    //     0x13919f4: ldur            w2, [x0, #0xf]
    // 0x13919f8: DecompressPointer r2
    //     0x13919f8: add             x2, x2, HEAP, lsl #32
    // 0x13919fc: r0 = phoneNumber=()
    //     0x13919fc: bl              #0x1391d4c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::phoneNumber=
    // 0x1391a00: ldur            x0, [fp, #-0x10]
    // 0x1391a04: LoadField: r1 = r0->field_f
    //     0x1391a04: ldur            w1, [x0, #0xf]
    // 0x1391a08: DecompressPointer r1
    //     0x1391a08: add             x1, x1, HEAP, lsl #32
    // 0x1391a0c: LoadField: r2 = r1->field_7
    //     0x1391a0c: ldur            w2, [x1, #7]
    // 0x1391a10: cbz             w2, #0x1391a3c
    // 0x1391a14: ldur            x2, [fp, #-8]
    // 0x1391a18: LoadField: r1 = r2->field_f
    //     0x1391a18: ldur            w1, [x2, #0xf]
    // 0x1391a1c: DecompressPointer r1
    //     0x1391a1c: add             x1, x1, HEAP, lsl #32
    // 0x1391a20: r0 = controller()
    //     0x1391a20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391a24: LoadField: r1 = r0->field_a7
    //     0x1391a24: ldur            w1, [x0, #0xa7]
    // 0x1391a28: DecompressPointer r1
    //     0x1391a28: add             x1, x1, HEAP, lsl #32
    // 0x1391a2c: ldur            x0, [fp, #-0x10]
    // 0x1391a30: LoadField: r2 = r0->field_13
    //     0x1391a30: ldur            w2, [x0, #0x13]
    // 0x1391a34: DecompressPointer r2
    //     0x1391a34: add             x2, x2, HEAP, lsl #32
    // 0x1391a38: r0 = value=()
    //     0x1391a38: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1391a3c: ldur            x0, [fp, #-8]
    // 0x1391a40: LoadField: r1 = r0->field_f
    //     0x1391a40: ldur            w1, [x0, #0xf]
    // 0x1391a44: DecompressPointer r1
    //     0x1391a44: add             x1, x1, HEAP, lsl #32
    // 0x1391a48: r0 = controller()
    //     0x1391a48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391a4c: LoadField: r1 = r0->field_7b
    //     0x1391a4c: ldur            w1, [x0, #0x7b]
    // 0x1391a50: DecompressPointer r1
    //     0x1391a50: add             x1, x1, HEAP, lsl #32
    // 0x1391a54: r0 = value()
    //     0x1391a54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1391a58: LoadField: r1 = r0->field_7
    //     0x1391a58: ldur            w1, [x0, #7]
    // 0x1391a5c: cmp             w1, #0x14
    // 0x1391a60: b.ne            #0x1391a7c
    // 0x1391a64: ldur            x0, [fp, #-8]
    // 0x1391a68: LoadField: r1 = r0->field_f
    //     0x1391a68: ldur            w1, [x0, #0xf]
    // 0x1391a6c: DecompressPointer r1
    //     0x1391a6c: add             x1, x1, HEAP, lsl #32
    // 0x1391a70: r0 = controller()
    //     0x1391a70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391a74: mov             x1, x0
    // 0x1391a78: r0 = updateVisitorContact()
    //     0x1391a78: bl              #0x1391a94  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::updateVisitorContact
    // 0x1391a7c: r0 = Null
    //     0x1391a7c: mov             x0, NULL
    // 0x1391a80: LeaveFrame
    //     0x1391a80: mov             SP, fp
    //     0x1391a84: ldp             fp, lr, [SP], #0x10
    // 0x1391a88: ret
    //     0x1391a88: ret             
    // 0x1391a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1391a8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1391a90: b               #0x13919d4
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1391d98, size: 0x98
    // 0x1391d98: EnterFrame
    //     0x1391d98: stp             fp, lr, [SP, #-0x10]!
    //     0x1391d9c: mov             fp, SP
    // 0x1391da0: AllocStack(0x28)
    //     0x1391da0: sub             SP, SP, #0x28
    // 0x1391da4: SetupParameters()
    //     0x1391da4: ldr             x0, [fp, #0x10]
    //     0x1391da8: ldur            w1, [x0, #0x17]
    //     0x1391dac: add             x1, x1, HEAP, lsl #32
    // 0x1391db0: CheckStackOverflow
    //     0x1391db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1391db4: cmp             SP, x16
    //     0x1391db8: b.ls            #0x1391e28
    // 0x1391dbc: LoadField: r0 = r1->field_f
    //     0x1391dbc: ldur            w0, [x1, #0xf]
    // 0x1391dc0: DecompressPointer r0
    //     0x1391dc0: add             x0, x0, HEAP, lsl #32
    // 0x1391dc4: mov             x1, x0
    // 0x1391dc8: r0 = controller()
    //     0x1391dc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391dcc: r16 = "landing_page"
    //     0x1391dcc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x1391dd0: ldr             x16, [x16, #0x638]
    // 0x1391dd4: r30 = "request_number"
    //     0x1391dd4: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x1391dd8: ldr             lr, [lr, #0x900]
    // 0x1391ddc: stp             lr, x16, [SP, #0x18]
    // 0x1391de0: r16 = "accordion_click"
    //     0x1391de0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d5e8] "accordion_click"
    //     0x1391de4: ldr             x16, [x16, #0x5e8]
    // 0x1391de8: r30 = "Accordion Click"
    //     0x1391de8: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d5f0] "Accordion Click"
    //     0x1391dec: ldr             lr, [lr, #0x5f0]
    // 0x1391df0: stp             lr, x16, [SP, #8]
    // 0x1391df4: r16 = "bag_accordion"
    //     0x1391df4: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d5f8] "bag_accordion"
    //     0x1391df8: ldr             x16, [x16, #0x5f8]
    // 0x1391dfc: str             x16, [SP]
    // 0x1391e00: mov             x1, x0
    // 0x1391e04: r2 = "checkout_cta_clicked"
    //     0x1391e04: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x1391e08: ldr             x2, [x2, #0x7b0]
    // 0x1391e0c: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, pageId, 0x3, pageType, 0x2, widgetType, 0x6, null]
    //     0x1391e0c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d910] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "pageId", 0x3, "pageType", 0x2, "widgetType", 0x6, Null]
    //     0x1391e10: ldr             x4, [x4, #0x910]
    // 0x1391e14: r0 = checkoutPostEvent()
    //     0x1391e14: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x1391e18: r0 = Null
    //     0x1391e18: mov             x0, NULL
    // 0x1391e1c: LeaveFrame
    //     0x1391e1c: mov             SP, fp
    //     0x1391e20: ldp             fp, lr, [SP], #0x10
    // 0x1391e24: ret
    //     0x1391e24: ret             
    // 0x1391e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1391e28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1391e2c: b               #0x1391dbc
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x1391e30, size: 0x64
    // 0x1391e30: EnterFrame
    //     0x1391e30: stp             fp, lr, [SP, #-0x10]!
    //     0x1391e34: mov             fp, SP
    // 0x1391e38: AllocStack(0x8)
    //     0x1391e38: sub             SP, SP, #8
    // 0x1391e3c: SetupParameters()
    //     0x1391e3c: ldr             x0, [fp, #0x10]
    //     0x1391e40: ldur            w1, [x0, #0x17]
    //     0x1391e44: add             x1, x1, HEAP, lsl #32
    // 0x1391e48: CheckStackOverflow
    //     0x1391e48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1391e4c: cmp             SP, x16
    //     0x1391e50: b.ls            #0x1391e8c
    // 0x1391e54: LoadField: r0 = r1->field_f
    //     0x1391e54: ldur            w0, [x1, #0xf]
    // 0x1391e58: DecompressPointer r0
    //     0x1391e58: add             x0, x0, HEAP, lsl #32
    // 0x1391e5c: mov             x1, x0
    // 0x1391e60: r0 = controller()
    //     0x1391e60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391e64: mov             x1, x0
    // 0x1391e68: r0 = bumperCouponData()
    //     0x1391e68: bl              #0x8a2a70  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bumperCouponData
    // 0x1391e6c: stur            x0, [fp, #-8]
    // 0x1391e70: r0 = CheckoutBreadCrumb()
    //     0x1391e70: bl              #0x1391e94  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x1391e74: ldur            x1, [fp, #-8]
    // 0x1391e78: StoreField: r0->field_b = r1
    //     0x1391e78: stur            w1, [x0, #0xb]
    // 0x1391e7c: StoreField: r0->field_f = rZR
    //     0x1391e7c: stur            xzr, [x0, #0xf]
    // 0x1391e80: LeaveFrame
    //     0x1391e80: mov             SP, fp
    //     0x1391e84: ldp             fp, lr, [SP], #0x10
    // 0x1391e88: ret
    //     0x1391e88: ret             
    // 0x1391e8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1391e8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1391e90: b               #0x1391e54
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x1391ea0, size: 0x144
    // 0x1391ea0: EnterFrame
    //     0x1391ea0: stp             fp, lr, [SP, #-0x10]!
    //     0x1391ea4: mov             fp, SP
    // 0x1391ea8: AllocStack(0x20)
    //     0x1391ea8: sub             SP, SP, #0x20
    // 0x1391eac: SetupParameters()
    //     0x1391eac: ldr             x0, [fp, #0x10]
    //     0x1391eb0: ldur            w2, [x0, #0x17]
    //     0x1391eb4: add             x2, x2, HEAP, lsl #32
    //     0x1391eb8: stur            x2, [fp, #-8]
    // 0x1391ebc: CheckStackOverflow
    //     0x1391ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1391ec0: cmp             SP, x16
    //     0x1391ec4: b.ls            #0x1391fd8
    // 0x1391ec8: LoadField: r1 = r2->field_f
    //     0x1391ec8: ldur            w1, [x2, #0xf]
    // 0x1391ecc: DecompressPointer r1
    //     0x1391ecc: add             x1, x1, HEAP, lsl #32
    // 0x1391ed0: r0 = controller()
    //     0x1391ed0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1391ed4: LoadField: r1 = r0->field_ab
    //     0x1391ed4: ldur            w1, [x0, #0xab]
    // 0x1391ed8: DecompressPointer r1
    //     0x1391ed8: add             x1, x1, HEAP, lsl #32
    // 0x1391edc: r0 = value()
    //     0x1391edc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1391ee0: tbnz            w0, #4, #0x1391fc8
    // 0x1391ee4: r0 = LoadStaticField(0x878)
    //     0x1391ee4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1391ee8: ldr             x0, [x0, #0x10f0]
    // 0x1391eec: cmp             w0, NULL
    // 0x1391ef0: b.eq            #0x1391fe0
    // 0x1391ef4: LoadField: r3 = r0->field_53
    //     0x1391ef4: ldur            w3, [x0, #0x53]
    // 0x1391ef8: DecompressPointer r3
    //     0x1391ef8: add             x3, x3, HEAP, lsl #32
    // 0x1391efc: stur            x3, [fp, #-0x18]
    // 0x1391f00: LoadField: r0 = r3->field_7
    //     0x1391f00: ldur            w0, [x3, #7]
    // 0x1391f04: DecompressPointer r0
    //     0x1391f04: add             x0, x0, HEAP, lsl #32
    // 0x1391f08: ldur            x2, [fp, #-8]
    // 0x1391f0c: stur            x0, [fp, #-0x10]
    // 0x1391f10: r1 = Function '<anonymous closure>':.
    //     0x1391f10: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db00] AnonymousClosure: (0x1391fe4), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1391f14: ldr             x1, [x1, #0xb00]
    // 0x1391f18: r0 = AllocateClosure()
    //     0x1391f18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1391f1c: ldur            x2, [fp, #-0x10]
    // 0x1391f20: mov             x3, x0
    // 0x1391f24: r1 = Null
    //     0x1391f24: mov             x1, NULL
    // 0x1391f28: stur            x3, [fp, #-8]
    // 0x1391f2c: cmp             w2, NULL
    // 0x1391f30: b.eq            #0x1391f50
    // 0x1391f34: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x1391f34: ldur            w4, [x2, #0x17]
    // 0x1391f38: DecompressPointer r4
    //     0x1391f38: add             x4, x4, HEAP, lsl #32
    // 0x1391f3c: r8 = X0
    //     0x1391f3c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x1391f40: LoadField: r9 = r4->field_7
    //     0x1391f40: ldur            x9, [x4, #7]
    // 0x1391f44: r3 = Null
    //     0x1391f44: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3db08] Null
    //     0x1391f48: ldr             x3, [x3, #0xb08]
    // 0x1391f4c: blr             x9
    // 0x1391f50: ldur            x0, [fp, #-0x18]
    // 0x1391f54: LoadField: r1 = r0->field_b
    //     0x1391f54: ldur            w1, [x0, #0xb]
    // 0x1391f58: LoadField: r2 = r0->field_f
    //     0x1391f58: ldur            w2, [x0, #0xf]
    // 0x1391f5c: DecompressPointer r2
    //     0x1391f5c: add             x2, x2, HEAP, lsl #32
    // 0x1391f60: LoadField: r3 = r2->field_b
    //     0x1391f60: ldur            w3, [x2, #0xb]
    // 0x1391f64: r2 = LoadInt32Instr(r1)
    //     0x1391f64: sbfx            x2, x1, #1, #0x1f
    // 0x1391f68: stur            x2, [fp, #-0x20]
    // 0x1391f6c: r1 = LoadInt32Instr(r3)
    //     0x1391f6c: sbfx            x1, x3, #1, #0x1f
    // 0x1391f70: cmp             x2, x1
    // 0x1391f74: b.ne            #0x1391f80
    // 0x1391f78: mov             x1, x0
    // 0x1391f7c: r0 = _growToNextCapacity()
    //     0x1391f7c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1391f80: ldur            x2, [fp, #-0x18]
    // 0x1391f84: ldur            x3, [fp, #-0x20]
    // 0x1391f88: add             x4, x3, #1
    // 0x1391f8c: lsl             x5, x4, #1
    // 0x1391f90: StoreField: r2->field_b = r5
    //     0x1391f90: stur            w5, [x2, #0xb]
    // 0x1391f94: LoadField: r1 = r2->field_f
    //     0x1391f94: ldur            w1, [x2, #0xf]
    // 0x1391f98: DecompressPointer r1
    //     0x1391f98: add             x1, x1, HEAP, lsl #32
    // 0x1391f9c: ldur            x0, [fp, #-8]
    // 0x1391fa0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1391fa0: add             x25, x1, x3, lsl #2
    //     0x1391fa4: add             x25, x25, #0xf
    //     0x1391fa8: str             w0, [x25]
    //     0x1391fac: tbz             w0, #0, #0x1391fc8
    //     0x1391fb0: ldurb           w16, [x1, #-1]
    //     0x1391fb4: ldurb           w17, [x0, #-1]
    //     0x1391fb8: and             x16, x17, x16, lsr #2
    //     0x1391fbc: tst             x16, HEAP, lsr #32
    //     0x1391fc0: b.eq            #0x1391fc8
    //     0x1391fc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1391fc8: r0 = Instance_SizedBox
    //     0x1391fc8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1391fcc: LeaveFrame
    //     0x1391fcc: mov             SP, fp
    //     0x1391fd0: ldp             fp, lr, [SP], #0x10
    // 0x1391fd4: ret
    //     0x1391fd4: ret             
    // 0x1391fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1391fd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1391fdc: b               #0x1391ec8
    // 0x1391fe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x1391fe0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x1391fe4, size: 0x74
    // 0x1391fe4: EnterFrame
    //     0x1391fe4: stp             fp, lr, [SP, #-0x10]!
    //     0x1391fe8: mov             fp, SP
    // 0x1391fec: AllocStack(0x8)
    //     0x1391fec: sub             SP, SP, #8
    // 0x1391ff0: SetupParameters()
    //     0x1391ff0: ldr             x0, [fp, #0x18]
    //     0x1391ff4: ldur            w2, [x0, #0x17]
    //     0x1391ff8: add             x2, x2, HEAP, lsl #32
    //     0x1391ffc: stur            x2, [fp, #-8]
    // 0x1392000: CheckStackOverflow
    //     0x1392000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1392004: cmp             SP, x16
    //     0x1392008: b.ls            #0x1392050
    // 0x139200c: LoadField: r1 = r2->field_f
    //     0x139200c: ldur            w1, [x2, #0xf]
    // 0x1392010: DecompressPointer r1
    //     0x1392010: add             x1, x1, HEAP, lsl #32
    // 0x1392014: r0 = controller()
    //     0x1392014: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1392018: LoadField: r1 = r0->field_ab
    //     0x1392018: ldur            w1, [x0, #0xab]
    // 0x139201c: DecompressPointer r1
    //     0x139201c: add             x1, x1, HEAP, lsl #32
    // 0x1392020: r2 = false
    //     0x1392020: add             x2, NULL, #0x30  ; false
    // 0x1392024: r0 = value=()
    //     0x1392024: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1392028: ldur            x0, [fp, #-8]
    // 0x139202c: LoadField: r1 = r0->field_f
    //     0x139202c: ldur            w1, [x0, #0xf]
    // 0x1392030: DecompressPointer r1
    //     0x1392030: add             x1, x1, HEAP, lsl #32
    // 0x1392034: LoadField: r2 = r0->field_13
    //     0x1392034: ldur            w2, [x0, #0x13]
    // 0x1392038: DecompressPointer r2
    //     0x1392038: add             x2, x2, HEAP, lsl #32
    // 0x139203c: r0 = _showOtpBottomSheet()
    //     0x139203c: bl              #0x1392058  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet
    // 0x1392040: r0 = Null
    //     0x1392040: mov             x0, NULL
    // 0x1392044: LeaveFrame
    //     0x1392044: mov             SP, fp
    //     0x1392048: ldp             fp, lr, [SP], #0x10
    // 0x139204c: ret
    //     0x139204c: ret             
    // 0x1392050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1392050: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1392054: b               #0x139200c
  }
  _ _showOtpBottomSheet(/* No info */) {
    // ** addr: 0x1392058, size: 0xb4
    // 0x1392058: EnterFrame
    //     0x1392058: stp             fp, lr, [SP, #-0x10]!
    //     0x139205c: mov             fp, SP
    // 0x1392060: AllocStack(0x40)
    //     0x1392060: sub             SP, SP, #0x40
    // 0x1392064: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1392064: stur            x1, [fp, #-8]
    //     0x1392068: stur            x2, [fp, #-0x10]
    // 0x139206c: CheckStackOverflow
    //     0x139206c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1392070: cmp             SP, x16
    //     0x1392074: b.ls            #0x1392104
    // 0x1392078: r1 = 1
    //     0x1392078: movz            x1, #0x1
    // 0x139207c: r0 = AllocateContext()
    //     0x139207c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1392080: mov             x3, x0
    // 0x1392084: ldur            x0, [fp, #-8]
    // 0x1392088: stur            x3, [fp, #-0x18]
    // 0x139208c: StoreField: r3->field_f = r0
    //     0x139208c: stur            w0, [x3, #0xf]
    // 0x1392090: mov             x2, x3
    // 0x1392094: r1 = Function '<anonymous closure>':.
    //     0x1392094: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db18] AnonymousClosure: (0x1392170), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1392098: ldr             x1, [x1, #0xb18]
    // 0x139209c: r0 = AllocateClosure()
    //     0x139209c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13920a0: stp             x0, NULL, [SP, #0x18]
    // 0x13920a4: ldur            x16, [fp, #-0x10]
    // 0x13920a8: r30 = true
    //     0x13920a8: add             lr, NULL, #0x20  ; true
    // 0x13920ac: stp             lr, x16, [SP, #8]
    // 0x13920b0: r16 = Instance_RoundedRectangleBorder
    //     0x13920b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x13920b4: ldr             x16, [x16, #0xd68]
    // 0x13920b8: str             x16, [SP]
    // 0x13920bc: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x13920bc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x13920c0: ldr             x4, [x4, #0xb20]
    // 0x13920c4: r0 = showModalBottomSheet()
    //     0x13920c4: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x13920c8: ldur            x2, [fp, #-0x18]
    // 0x13920cc: r1 = Function '<anonymous closure>':.
    //     0x13920cc: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db20] AnonymousClosure: (0x139210c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x13920d0: ldr             x1, [x1, #0xb20]
    // 0x13920d4: stur            x0, [fp, #-8]
    // 0x13920d8: r0 = AllocateClosure()
    //     0x13920d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13920dc: r16 = <Null?>
    //     0x13920dc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x13920e0: ldur            lr, [fp, #-8]
    // 0x13920e4: stp             lr, x16, [SP, #8]
    // 0x13920e8: str             x0, [SP]
    // 0x13920ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x13920ec: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x13920f0: r0 = then()
    //     0x13920f0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x13920f4: r0 = Null
    //     0x13920f4: mov             x0, NULL
    // 0x13920f8: LeaveFrame
    //     0x13920f8: mov             SP, fp
    //     0x13920fc: ldp             fp, lr, [SP], #0x10
    // 0x1392100: ret
    //     0x1392100: ret             
    // 0x1392104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1392104: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1392108: b               #0x1392078
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x139210c, size: 0x64
    // 0x139210c: EnterFrame
    //     0x139210c: stp             fp, lr, [SP, #-0x10]!
    //     0x1392110: mov             fp, SP
    // 0x1392114: ldr             x0, [fp, #0x18]
    // 0x1392118: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1392118: ldur            w1, [x0, #0x17]
    // 0x139211c: DecompressPointer r1
    //     0x139211c: add             x1, x1, HEAP, lsl #32
    // 0x1392120: CheckStackOverflow
    //     0x1392120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1392124: cmp             SP, x16
    //     0x1392128: b.ls            #0x1392168
    // 0x139212c: ldr             x0, [fp, #0x10]
    // 0x1392130: cmp             w0, NULL
    // 0x1392134: b.ne            #0x1392158
    // 0x1392138: LoadField: r0 = r1->field_f
    //     0x1392138: ldur            w0, [x1, #0xf]
    // 0x139213c: DecompressPointer r0
    //     0x139213c: add             x0, x0, HEAP, lsl #32
    // 0x1392140: mov             x1, x0
    // 0x1392144: r0 = controller()
    //     0x1392144: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1392148: LoadField: r1 = r0->field_cb
    //     0x1392148: ldur            w1, [x0, #0xcb]
    // 0x139214c: DecompressPointer r1
    //     0x139214c: add             x1, x1, HEAP, lsl #32
    // 0x1392150: r2 = false
    //     0x1392150: add             x2, NULL, #0x30  ; false
    // 0x1392154: r0 = value=()
    //     0x1392154: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1392158: r0 = Null
    //     0x1392158: mov             x0, NULL
    // 0x139215c: LeaveFrame
    //     0x139215c: mov             SP, fp
    //     0x1392160: ldp             fp, lr, [SP], #0x10
    // 0x1392164: ret
    //     0x1392164: ret             
    // 0x1392168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1392168: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x139216c: b               #0x139212c
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1392170, size: 0x130
    // 0x1392170: EnterFrame
    //     0x1392170: stp             fp, lr, [SP, #-0x10]!
    //     0x1392174: mov             fp, SP
    // 0x1392178: AllocStack(0x30)
    //     0x1392178: sub             SP, SP, #0x30
    // 0x139217c: SetupParameters()
    //     0x139217c: ldr             x0, [fp, #0x18]
    //     0x1392180: ldur            w1, [x0, #0x17]
    //     0x1392184: add             x1, x1, HEAP, lsl #32
    //     0x1392188: stur            x1, [fp, #-8]
    // 0x139218c: CheckStackOverflow
    //     0x139218c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1392190: cmp             SP, x16
    //     0x1392194: b.ls            #0x1392298
    // 0x1392198: r1 = 1
    //     0x1392198: movz            x1, #0x1
    // 0x139219c: r0 = AllocateContext()
    //     0x139219c: bl              #0x16f6108  ; AllocateContextStub
    // 0x13921a0: mov             x2, x0
    // 0x13921a4: ldur            x0, [fp, #-8]
    // 0x13921a8: stur            x2, [fp, #-0x10]
    // 0x13921ac: StoreField: r2->field_b = r0
    //     0x13921ac: stur            w0, [x2, #0xb]
    // 0x13921b0: ldr             x1, [fp, #0x10]
    // 0x13921b4: StoreField: r2->field_f = r1
    //     0x13921b4: stur            w1, [x2, #0xf]
    // 0x13921b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13921b8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13921bc: r0 = _of()
    //     0x13921bc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13921c0: LoadField: r1 = r0->field_23
    //     0x13921c0: ldur            w1, [x0, #0x23]
    // 0x13921c4: DecompressPointer r1
    //     0x13921c4: add             x1, x1, HEAP, lsl #32
    // 0x13921c8: LoadField: d0 = r1->field_1f
    //     0x13921c8: ldur            d0, [x1, #0x1f]
    // 0x13921cc: stur            d0, [fp, #-0x30]
    // 0x13921d0: r0 = EdgeInsets()
    //     0x13921d0: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x13921d4: stur            x0, [fp, #-0x18]
    // 0x13921d8: StoreField: r0->field_7 = rZR
    //     0x13921d8: stur            xzr, [x0, #7]
    // 0x13921dc: StoreField: r0->field_f = rZR
    //     0x13921dc: stur            xzr, [x0, #0xf]
    // 0x13921e0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13921e0: stur            xzr, [x0, #0x17]
    // 0x13921e4: ldur            d0, [fp, #-0x30]
    // 0x13921e8: StoreField: r0->field_1f = d0
    //     0x13921e8: stur            d0, [x0, #0x1f]
    // 0x13921ec: ldur            x1, [fp, #-8]
    // 0x13921f0: LoadField: r2 = r1->field_f
    //     0x13921f0: ldur            w2, [x1, #0xf]
    // 0x13921f4: DecompressPointer r2
    //     0x13921f4: add             x2, x2, HEAP, lsl #32
    // 0x13921f8: mov             x1, x2
    // 0x13921fc: r0 = controller()
    //     0x13921fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1392200: LoadField: r1 = r0->field_7b
    //     0x1392200: ldur            w1, [x0, #0x7b]
    // 0x1392204: DecompressPointer r1
    //     0x1392204: add             x1, x1, HEAP, lsl #32
    // 0x1392208: r0 = value()
    //     0x1392208: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x139220c: ldur            x2, [fp, #-0x10]
    // 0x1392210: r1 = Function '<anonymous closure>':.
    //     0x1392210: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db28] AnonymousClosure: (0x1392654), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1392214: ldr             x1, [x1, #0xb28]
    // 0x1392218: stur            x0, [fp, #-8]
    // 0x139221c: r0 = AllocateClosure()
    //     0x139221c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1392220: stur            x0, [fp, #-0x20]
    // 0x1392224: r0 = OtpBottomSheet()
    //     0x1392224: bl              #0x13022a8  ; AllocateOtpBottomSheetStub -> OtpBottomSheet (size=0x1c)
    // 0x1392228: mov             x3, x0
    // 0x139222c: ldur            x0, [fp, #-0x20]
    // 0x1392230: stur            x3, [fp, #-0x28]
    // 0x1392234: StoreField: r3->field_b = r0
    //     0x1392234: stur            w0, [x3, #0xb]
    // 0x1392238: ldur            x2, [fp, #-0x10]
    // 0x139223c: r1 = Function '<anonymous closure>':.
    //     0x139223c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db30] AnonymousClosure: (0x1392328), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1392240: ldr             x1, [x1, #0xb30]
    // 0x1392244: r0 = AllocateClosure()
    //     0x1392244: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1392248: mov             x1, x0
    // 0x139224c: ldur            x0, [fp, #-0x28]
    // 0x1392250: StoreField: r0->field_f = r1
    //     0x1392250: stur            w1, [x0, #0xf]
    // 0x1392254: ldur            x1, [fp, #-8]
    // 0x1392258: StoreField: r0->field_13 = r1
    //     0x1392258: stur            w1, [x0, #0x13]
    // 0x139225c: ldur            x2, [fp, #-0x10]
    // 0x1392260: r1 = Function '<anonymous closure>':.
    //     0x1392260: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3db38] AnonymousClosure: (0x13922a0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1392264: ldr             x1, [x1, #0xb38]
    // 0x1392268: r0 = AllocateClosure()
    //     0x1392268: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x139226c: mov             x1, x0
    // 0x1392270: ldur            x0, [fp, #-0x28]
    // 0x1392274: ArrayStore: r0[0] = r1  ; List_4
    //     0x1392274: stur            w1, [x0, #0x17]
    // 0x1392278: r0 = Padding()
    //     0x1392278: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x139227c: ldur            x1, [fp, #-0x18]
    // 0x1392280: StoreField: r0->field_f = r1
    //     0x1392280: stur            w1, [x0, #0xf]
    // 0x1392284: ldur            x1, [fp, #-0x28]
    // 0x1392288: StoreField: r0->field_b = r1
    //     0x1392288: stur            w1, [x0, #0xb]
    // 0x139228c: LeaveFrame
    //     0x139228c: mov             SP, fp
    //     0x1392290: ldp             fp, lr, [SP], #0x10
    // 0x1392294: ret
    //     0x1392294: ret             
    // 0x1392298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1392298: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x139229c: b               #0x1392198
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13922a0, size: 0x88
    // 0x13922a0: EnterFrame
    //     0x13922a0: stp             fp, lr, [SP, #-0x10]!
    //     0x13922a4: mov             fp, SP
    // 0x13922a8: AllocStack(0x18)
    //     0x13922a8: sub             SP, SP, #0x18
    // 0x13922ac: SetupParameters()
    //     0x13922ac: ldr             x0, [fp, #0x10]
    //     0x13922b0: ldur            w2, [x0, #0x17]
    //     0x13922b4: add             x2, x2, HEAP, lsl #32
    //     0x13922b8: stur            x2, [fp, #-8]
    // 0x13922bc: CheckStackOverflow
    //     0x13922bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13922c0: cmp             SP, x16
    //     0x13922c4: b.ls            #0x1392320
    // 0x13922c8: LoadField: r0 = r2->field_b
    //     0x13922c8: ldur            w0, [x2, #0xb]
    // 0x13922cc: DecompressPointer r0
    //     0x13922cc: add             x0, x0, HEAP, lsl #32
    // 0x13922d0: LoadField: r1 = r0->field_f
    //     0x13922d0: ldur            w1, [x0, #0xf]
    // 0x13922d4: DecompressPointer r1
    //     0x13922d4: add             x1, x1, HEAP, lsl #32
    // 0x13922d8: r0 = controller()
    //     0x13922d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13922dc: LoadField: r1 = r0->field_cb
    //     0x13922dc: ldur            w1, [x0, #0xcb]
    // 0x13922e0: DecompressPointer r1
    //     0x13922e0: add             x1, x1, HEAP, lsl #32
    // 0x13922e4: r2 = false
    //     0x13922e4: add             x2, NULL, #0x30  ; false
    // 0x13922e8: r0 = value=()
    //     0x13922e8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13922ec: ldur            x0, [fp, #-8]
    // 0x13922f0: LoadField: r1 = r0->field_f
    //     0x13922f0: ldur            w1, [x0, #0xf]
    // 0x13922f4: DecompressPointer r1
    //     0x13922f4: add             x1, x1, HEAP, lsl #32
    // 0x13922f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13922f8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13922fc: r0 = of()
    //     0x13922fc: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x1392300: r16 = <Object?>
    //     0x1392300: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0x1392304: stp             x0, x16, [SP]
    // 0x1392308: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1392308: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x139230c: r0 = pop()
    //     0x139230c: bl              #0x67c754  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0x1392310: r0 = Null
    //     0x1392310: mov             x0, NULL
    // 0x1392314: LeaveFrame
    //     0x1392314: mov             SP, fp
    //     0x1392318: ldp             fp, lr, [SP], #0x10
    // 0x139231c: ret
    //     0x139231c: ret             
    // 0x1392320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1392320: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1392324: b               #0x13922c8
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1392328, size: 0xa4
    // 0x1392328: EnterFrame
    //     0x1392328: stp             fp, lr, [SP, #-0x10]!
    //     0x139232c: mov             fp, SP
    // 0x1392330: AllocStack(0x18)
    //     0x1392330: sub             SP, SP, #0x18
    // 0x1392334: SetupParameters()
    //     0x1392334: ldr             x0, [fp, #0x10]
    //     0x1392338: ldur            w1, [x0, #0x17]
    //     0x139233c: add             x1, x1, HEAP, lsl #32
    // 0x1392340: CheckStackOverflow
    //     0x1392340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1392344: cmp             SP, x16
    //     0x1392348: b.ls            #0x13923c4
    // 0x139234c: LoadField: r0 = r1->field_b
    //     0x139234c: ldur            w0, [x1, #0xb]
    // 0x1392350: DecompressPointer r0
    //     0x1392350: add             x0, x0, HEAP, lsl #32
    // 0x1392354: stur            x0, [fp, #-8]
    // 0x1392358: LoadField: r1 = r0->field_f
    //     0x1392358: ldur            w1, [x0, #0xf]
    // 0x139235c: DecompressPointer r1
    //     0x139235c: add             x1, x1, HEAP, lsl #32
    // 0x1392360: r0 = controller()
    //     0x1392360: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1392364: mov             x2, x0
    // 0x1392368: ldur            x0, [fp, #-8]
    // 0x139236c: stur            x2, [fp, #-0x10]
    // 0x1392370: LoadField: r1 = r0->field_f
    //     0x1392370: ldur            w1, [x0, #0xf]
    // 0x1392374: DecompressPointer r1
    //     0x1392374: add             x1, x1, HEAP, lsl #32
    // 0x1392378: r0 = controller()
    //     0x1392378: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x139237c: LoadField: r1 = r0->field_7b
    //     0x139237c: ldur            w1, [x0, #0x7b]
    // 0x1392380: DecompressPointer r1
    //     0x1392380: add             x1, x1, HEAP, lsl #32
    // 0x1392384: r0 = value()
    //     0x1392384: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1392388: mov             x2, x0
    // 0x139238c: ldur            x0, [fp, #-8]
    // 0x1392390: stur            x2, [fp, #-0x18]
    // 0x1392394: LoadField: r1 = r0->field_f
    //     0x1392394: ldur            w1, [x0, #0xf]
    // 0x1392398: DecompressPointer r1
    //     0x1392398: add             x1, x1, HEAP, lsl #32
    // 0x139239c: r0 = controller()
    //     0x139239c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13923a0: LoadField: r3 = r0->field_8b
    //     0x13923a0: ldur            w3, [x0, #0x8b]
    // 0x13923a4: DecompressPointer r3
    //     0x13923a4: add             x3, x3, HEAP, lsl #32
    // 0x13923a8: ldur            x1, [fp, #-0x10]
    // 0x13923ac: ldur            x2, [fp, #-0x18]
    // 0x13923b0: r0 = resendOtp()
    //     0x13923b0: bl              #0x13923cc  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::resendOtp
    // 0x13923b4: r0 = Null
    //     0x13923b4: mov             x0, NULL
    // 0x13923b8: LeaveFrame
    //     0x13923b8: mov             SP, fp
    //     0x13923bc: ldp             fp, lr, [SP], #0x10
    // 0x13923c0: ret
    //     0x13923c0: ret             
    // 0x13923c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13923c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13923c8: b               #0x139234c
  }
  [closure] Future<Null> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x1392654, size: 0x1a0
    // 0x1392654: EnterFrame
    //     0x1392654: stp             fp, lr, [SP, #-0x10]!
    //     0x1392658: mov             fp, SP
    // 0x139265c: AllocStack(0x58)
    //     0x139265c: sub             SP, SP, #0x58
    // 0x1392660: SetupParameters(CheckoutRequestNumberPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1392660: stur            NULL, [fp, #-8]
    //     0x1392664: movz            x0, #0
    //     0x1392668: add             x1, fp, w0, sxtw #2
    //     0x139266c: ldr             x1, [x1, #0x18]
    //     0x1392670: add             x2, fp, w0, sxtw #2
    //     0x1392674: ldr             x2, [x2, #0x10]
    //     0x1392678: stur            x2, [fp, #-0x18]
    //     0x139267c: ldur            w3, [x1, #0x17]
    //     0x1392680: add             x3, x3, HEAP, lsl #32
    //     0x1392684: stur            x3, [fp, #-0x10]
    // 0x1392688: CheckStackOverflow
    //     0x1392688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x139268c: cmp             SP, x16
    //     0x1392690: b.ls            #0x13927ec
    // 0x1392694: InitAsync() -> Future<Null?>
    //     0x1392694: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x1392698: bl              #0x6326e0  ; InitAsyncStub
    // 0x139269c: ldur            x0, [fp, #-0x10]
    // 0x13926a0: LoadField: r2 = r0->field_b
    //     0x13926a0: ldur            w2, [x0, #0xb]
    // 0x13926a4: DecompressPointer r2
    //     0x13926a4: add             x2, x2, HEAP, lsl #32
    // 0x13926a8: stur            x2, [fp, #-0x20]
    // 0x13926ac: LoadField: r1 = r2->field_f
    //     0x13926ac: ldur            w1, [x2, #0xf]
    // 0x13926b0: DecompressPointer r1
    //     0x13926b0: add             x1, x1, HEAP, lsl #32
    // 0x13926b4: r0 = controller()
    //     0x13926b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13926b8: mov             x2, x0
    // 0x13926bc: ldur            x0, [fp, #-0x20]
    // 0x13926c0: stur            x2, [fp, #-0x28]
    // 0x13926c4: LoadField: r1 = r0->field_f
    //     0x13926c4: ldur            w1, [x0, #0xf]
    // 0x13926c8: DecompressPointer r1
    //     0x13926c8: add             x1, x1, HEAP, lsl #32
    // 0x13926cc: r0 = controller()
    //     0x13926cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13926d0: LoadField: r1 = r0->field_7b
    //     0x13926d0: ldur            w1, [x0, #0x7b]
    // 0x13926d4: DecompressPointer r1
    //     0x13926d4: add             x1, x1, HEAP, lsl #32
    // 0x13926d8: r0 = value()
    //     0x13926d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13926dc: mov             x3, x0
    // 0x13926e0: ldur            x0, [fp, #-0x18]
    // 0x13926e4: r2 = Null
    //     0x13926e4: mov             x2, NULL
    // 0x13926e8: r1 = Null
    //     0x13926e8: mov             x1, NULL
    // 0x13926ec: stur            x3, [fp, #-0x30]
    // 0x13926f0: r4 = 60
    //     0x13926f0: movz            x4, #0x3c
    // 0x13926f4: branchIfSmi(r0, 0x1392700)
    //     0x13926f4: tbz             w0, #0, #0x1392700
    // 0x13926f8: r4 = LoadClassIdInstr(r0)
    //     0x13926f8: ldur            x4, [x0, #-1]
    //     0x13926fc: ubfx            x4, x4, #0xc, #0x14
    // 0x1392700: sub             x4, x4, #0x5e
    // 0x1392704: cmp             x4, #1
    // 0x1392708: b.ls            #0x139271c
    // 0x139270c: r8 = String
    //     0x139270c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x1392710: r3 = Null
    //     0x1392710: add             x3, PP, #0x3d, lsl #12  ; [pp+0x3db70] Null
    //     0x1392714: ldr             x3, [x3, #0xb70]
    // 0x1392718: r0 = String()
    //     0x1392718: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x139271c: ldur            x1, [fp, #-0x28]
    // 0x1392720: ldur            x2, [fp, #-0x30]
    // 0x1392724: ldur            x3, [fp, #-0x18]
    // 0x1392728: r0 = verifyOtp()
    //     0x1392728: bl              #0x13927f4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::verifyOtp
    // 0x139272c: mov             x1, x0
    // 0x1392730: stur            x1, [fp, #-0x18]
    // 0x1392734: r0 = Await()
    //     0x1392734: bl              #0x63248c  ; AwaitStub
    // 0x1392738: r16 = true
    //     0x1392738: add             x16, NULL, #0x20  ; true
    // 0x139273c: cmp             w0, w16
    // 0x1392740: b.ne            #0x1392788
    // 0x1392744: ldur            x0, [fp, #-0x20]
    // 0x1392748: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1392748: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x139274c: ldr             x0, [x0, #0x1c80]
    //     0x1392750: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1392754: cmp             w0, w16
    //     0x1392758: b.ne            #0x1392764
    //     0x139275c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1392760: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1392764: str             NULL, [SP]
    // 0x1392768: r4 = const [0x1, 0, 0, 0, null]
    //     0x1392768: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x139276c: r0 = GetNavigation.back()
    //     0x139276c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1392770: ldur            x0, [fp, #-0x20]
    // 0x1392774: LoadField: r1 = r0->field_f
    //     0x1392774: ldur            w1, [x0, #0xf]
    // 0x1392778: DecompressPointer r1
    //     0x1392778: add             x1, x1, HEAP, lsl #32
    // 0x139277c: r0 = controller()
    //     0x139277c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1392780: mov             x1, x0
    // 0x1392784: r0 = navigateToOrderSummaryPage()
    //     0x1392784: bl              #0x12e8d30  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::navigateToOrderSummaryPage
    // 0x1392788: ldur            x0, [fp, #-0x20]
    // 0x139278c: LoadField: r1 = r0->field_f
    //     0x139278c: ldur            w1, [x0, #0xf]
    // 0x1392790: DecompressPointer r1
    //     0x1392790: add             x1, x1, HEAP, lsl #32
    // 0x1392794: r0 = controller()
    //     0x1392794: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1392798: r16 = "landing_page"
    //     0x1392798: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x139279c: ldr             x16, [x16, #0x638]
    // 0x13927a0: r30 = "request_number"
    //     0x13927a0: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x13927a4: ldr             lr, [lr, #0x900]
    // 0x13927a8: stp             lr, x16, [SP, #0x18]
    // 0x13927ac: r16 = "confirm_cta"
    //     0x13927ac: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3db80] "confirm_cta"
    //     0x13927b0: ldr             x16, [x16, #0xb80]
    // 0x13927b4: r30 = "Confirm"
    //     0x13927b4: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3cec0] "Confirm"
    //     0x13927b8: ldr             lr, [lr, #0xec0]
    // 0x13927bc: stp             lr, x16, [SP, #8]
    // 0x13927c0: r16 = "otp_popup"
    //     0x13927c0: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c818] "otp_popup"
    //     0x13927c4: ldr             x16, [x16, #0x818]
    // 0x13927c8: str             x16, [SP]
    // 0x13927cc: mov             x1, x0
    // 0x13927d0: r2 = "checkout_cta_clicked"
    //     0x13927d0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x13927d4: ldr             x2, [x2, #0x7b0]
    // 0x13927d8: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, pageId, 0x3, pageType, 0x2, widgetType, 0x6, null]
    //     0x13927d8: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d910] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "pageId", 0x3, "pageType", 0x2, "widgetType", 0x6, Null]
    //     0x13927dc: ldr             x4, [x4, #0x910]
    // 0x13927e0: r0 = checkoutPostEvent()
    //     0x13927e0: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x13927e4: r0 = Null
    //     0x13927e4: mov             x0, NULL
    // 0x13927e8: r0 = ReturnAsyncNotFuture()
    //     0x13927e8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x13927ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13927ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13927f0: b               #0x1392694
  }
  _ body(/* No info */) {
    // ** addr: 0x1500b5c, size: 0x118
    // 0x1500b5c: EnterFrame
    //     0x1500b5c: stp             fp, lr, [SP, #-0x10]!
    //     0x1500b60: mov             fp, SP
    // 0x1500b64: AllocStack(0x18)
    //     0x1500b64: sub             SP, SP, #0x18
    // 0x1500b68: SetupParameters(CheckoutRequestNumberPage this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x1500b68: stur            x1, [fp, #-8]
    //     0x1500b6c: mov             x16, x2
    //     0x1500b70: mov             x2, x1
    //     0x1500b74: mov             x1, x16
    //     0x1500b78: stur            x1, [fp, #-0x10]
    // 0x1500b7c: CheckStackOverflow
    //     0x1500b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1500b80: cmp             SP, x16
    //     0x1500b84: b.ls            #0x1500c6c
    // 0x1500b88: r1 = 3
    //     0x1500b88: movz            x1, #0x3
    // 0x1500b8c: r0 = AllocateContext()
    //     0x1500b8c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1500b90: ldur            x2, [fp, #-8]
    // 0x1500b94: stur            x0, [fp, #-0x18]
    // 0x1500b98: StoreField: r0->field_f = r2
    //     0x1500b98: stur            w2, [x0, #0xf]
    // 0x1500b9c: ldur            x1, [fp, #-0x10]
    // 0x1500ba0: StoreField: r0->field_13 = r1
    //     0x1500ba0: stur            w1, [x0, #0x13]
    // 0x1500ba4: r0 = of()
    //     0x1500ba4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1500ba8: LoadField: r1 = r0->field_87
    //     0x1500ba8: ldur            w1, [x0, #0x87]
    // 0x1500bac: DecompressPointer r1
    //     0x1500bac: add             x1, x1, HEAP, lsl #32
    // 0x1500bb0: mov             x0, x1
    // 0x1500bb4: ldur            x2, [fp, #-0x18]
    // 0x1500bb8: ArrayStore: r2[0] = r0  ; List_4
    //     0x1500bb8: stur            w0, [x2, #0x17]
    //     0x1500bbc: ldurb           w16, [x2, #-1]
    //     0x1500bc0: ldurb           w17, [x0, #-1]
    //     0x1500bc4: and             x16, x17, x16, lsr #2
    //     0x1500bc8: tst             x16, HEAP, lsr #32
    //     0x1500bcc: b.eq            #0x1500bd4
    //     0x1500bd0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1500bd4: r0 = Obx()
    //     0x1500bd4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1500bd8: ldur            x2, [fp, #-0x18]
    // 0x1500bdc: r1 = Function '<anonymous closure>':.
    //     0x1500bdc: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3da88] AnonymousClosure: (0x1391440), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1500be0: ldr             x1, [x1, #0xa88]
    // 0x1500be4: stur            x0, [fp, #-0x10]
    // 0x1500be8: r0 = AllocateClosure()
    //     0x1500be8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500bec: mov             x1, x0
    // 0x1500bf0: ldur            x0, [fp, #-0x10]
    // 0x1500bf4: StoreField: r0->field_b = r1
    //     0x1500bf4: stur            w1, [x0, #0xb]
    // 0x1500bf8: r0 = SafeArea()
    //     0x1500bf8: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1500bfc: mov             x1, x0
    // 0x1500c00: r0 = true
    //     0x1500c00: add             x0, NULL, #0x20  ; true
    // 0x1500c04: stur            x1, [fp, #-0x18]
    // 0x1500c08: StoreField: r1->field_b = r0
    //     0x1500c08: stur            w0, [x1, #0xb]
    // 0x1500c0c: StoreField: r1->field_f = r0
    //     0x1500c0c: stur            w0, [x1, #0xf]
    // 0x1500c10: StoreField: r1->field_13 = r0
    //     0x1500c10: stur            w0, [x1, #0x13]
    // 0x1500c14: ArrayStore: r1[0] = r0  ; List_4
    //     0x1500c14: stur            w0, [x1, #0x17]
    // 0x1500c18: r0 = Instance_EdgeInsets
    //     0x1500c18: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1500c1c: StoreField: r1->field_1b = r0
    //     0x1500c1c: stur            w0, [x1, #0x1b]
    // 0x1500c20: r0 = false
    //     0x1500c20: add             x0, NULL, #0x30  ; false
    // 0x1500c24: StoreField: r1->field_1f = r0
    //     0x1500c24: stur            w0, [x1, #0x1f]
    // 0x1500c28: ldur            x0, [fp, #-0x10]
    // 0x1500c2c: StoreField: r1->field_23 = r0
    //     0x1500c2c: stur            w0, [x1, #0x23]
    // 0x1500c30: r0 = WillPopScope()
    //     0x1500c30: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1500c34: mov             x3, x0
    // 0x1500c38: ldur            x0, [fp, #-0x18]
    // 0x1500c3c: stur            x3, [fp, #-0x10]
    // 0x1500c40: StoreField: r3->field_b = r0
    //     0x1500c40: stur            w0, [x3, #0xb]
    // 0x1500c44: ldur            x2, [fp, #-8]
    // 0x1500c48: r1 = Function 'getBack':.
    //     0x1500c48: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3da90] AnonymousClosure: (0x1500c74), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack (0x1500cac)
    //     0x1500c4c: ldr             x1, [x1, #0xa90]
    // 0x1500c50: r0 = AllocateClosure()
    //     0x1500c50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500c54: mov             x1, x0
    // 0x1500c58: ldur            x0, [fp, #-0x10]
    // 0x1500c5c: StoreField: r0->field_f = r1
    //     0x1500c5c: stur            w1, [x0, #0xf]
    // 0x1500c60: LeaveFrame
    //     0x1500c60: mov             SP, fp
    //     0x1500c64: ldp             fp, lr, [SP], #0x10
    // 0x1500c68: ret
    //     0x1500c68: ret             
    // 0x1500c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1500c6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1500c70: b               #0x1500b88
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x1500c74, size: 0x38
    // 0x1500c74: EnterFrame
    //     0x1500c74: stp             fp, lr, [SP, #-0x10]!
    //     0x1500c78: mov             fp, SP
    // 0x1500c7c: ldr             x0, [fp, #0x10]
    // 0x1500c80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1500c80: ldur            w1, [x0, #0x17]
    // 0x1500c84: DecompressPointer r1
    //     0x1500c84: add             x1, x1, HEAP, lsl #32
    // 0x1500c88: CheckStackOverflow
    //     0x1500c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1500c8c: cmp             SP, x16
    //     0x1500c90: b.ls            #0x1500ca4
    // 0x1500c94: r0 = getBack()
    //     0x1500c94: bl              #0x1500cac  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack
    // 0x1500c98: LeaveFrame
    //     0x1500c98: mov             SP, fp
    //     0x1500c9c: ldp             fp, lr, [SP], #0x10
    // 0x1500ca0: ret
    //     0x1500ca0: ret             
    // 0x1500ca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1500ca4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1500ca8: b               #0x1500c94
  }
  _ getBack(/* No info */) {
    // ** addr: 0x1500cac, size: 0xb0
    // 0x1500cac: EnterFrame
    //     0x1500cac: stp             fp, lr, [SP, #-0x10]!
    //     0x1500cb0: mov             fp, SP
    // 0x1500cb4: AllocStack(0x8)
    //     0x1500cb4: sub             SP, SP, #8
    // 0x1500cb8: CheckStackOverflow
    //     0x1500cb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1500cbc: cmp             SP, x16
    //     0x1500cc0: b.ls            #0x1500d54
    // 0x1500cc4: r2 = false
    //     0x1500cc4: add             x2, NULL, #0x30  ; false
    // 0x1500cc8: r0 = showLoading()
    //     0x1500cc8: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x1500ccc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1500ccc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1500cd0: ldr             x0, [x0, #0x1c80]
    //     0x1500cd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1500cd8: cmp             w0, w16
    //     0x1500cdc: b.ne            #0x1500ce8
    //     0x1500ce0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1500ce4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1500ce8: r1 = Function '<anonymous closure>':.
    //     0x1500ce8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3da98] AnonymousClosure: (0x1390d2c), in [package:customer_app/app/presentation/views/line/checkout_variants/checkout_variants_view.dart] CheckoutVariantsView::getBack (0x1390ea0)
    //     0x1500cec: ldr             x1, [x1, #0xa98]
    // 0x1500cf0: r2 = Null
    //     0x1500cf0: mov             x2, NULL
    // 0x1500cf4: r0 = AllocateClosure()
    //     0x1500cf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500cf8: mov             x1, x0
    // 0x1500cfc: r0 = GetNavigation.until()
    //     0x1500cfc: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x1500d00: r1 = <bool>
    //     0x1500d00: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x1500d04: r0 = _Future()
    //     0x1500d04: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x1500d08: stur            x0, [fp, #-8]
    // 0x1500d0c: StoreField: r0->field_b = rZR
    //     0x1500d0c: stur            xzr, [x0, #0xb]
    // 0x1500d10: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x1500d10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1500d14: ldr             x0, [x0, #0x778]
    //     0x1500d18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1500d1c: cmp             w0, w16
    //     0x1500d20: b.ne            #0x1500d2c
    //     0x1500d24: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x1500d28: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x1500d2c: mov             x1, x0
    // 0x1500d30: ldur            x0, [fp, #-8]
    // 0x1500d34: StoreField: r0->field_13 = r1
    //     0x1500d34: stur            w1, [x0, #0x13]
    // 0x1500d38: mov             x1, x0
    // 0x1500d3c: r2 = false
    //     0x1500d3c: add             x2, NULL, #0x30  ; false
    // 0x1500d40: r0 = _asyncComplete()
    //     0x1500d40: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x1500d44: ldur            x0, [fp, #-8]
    // 0x1500d48: LeaveFrame
    //     0x1500d48: mov             SP, fp
    //     0x1500d4c: ldp             fp, lr, [SP], #0x10
    // 0x1500d50: ret
    //     0x1500d50: ret             
    // 0x1500d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1500d54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1500d58: b               #0x1500cc4
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15ccba8, size: 0x420
    // 0x15ccba8: EnterFrame
    //     0x15ccba8: stp             fp, lr, [SP, #-0x10]!
    //     0x15ccbac: mov             fp, SP
    // 0x15ccbb0: AllocStack(0x40)
    //     0x15ccbb0: sub             SP, SP, #0x40
    // 0x15ccbb4: SetupParameters()
    //     0x15ccbb4: ldr             x0, [fp, #0x10]
    //     0x15ccbb8: ldur            w2, [x0, #0x17]
    //     0x15ccbbc: add             x2, x2, HEAP, lsl #32
    //     0x15ccbc0: stur            x2, [fp, #-8]
    // 0x15ccbc4: CheckStackOverflow
    //     0x15ccbc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ccbc8: cmp             SP, x16
    //     0x15ccbcc: b.ls            #0x15ccfc0
    // 0x15ccbd0: LoadField: r1 = r2->field_f
    //     0x15ccbd0: ldur            w1, [x2, #0xf]
    // 0x15ccbd4: DecompressPointer r1
    //     0x15ccbd4: add             x1, x1, HEAP, lsl #32
    // 0x15ccbd8: r0 = controller()
    //     0x15ccbd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ccbdc: LoadField: r1 = r0->field_53
    //     0x15ccbdc: ldur            w1, [x0, #0x53]
    // 0x15ccbe0: DecompressPointer r1
    //     0x15ccbe0: add             x1, x1, HEAP, lsl #32
    // 0x15ccbe4: r0 = value()
    //     0x15ccbe4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ccbe8: LoadField: r1 = r0->field_3f
    //     0x15ccbe8: ldur            w1, [x0, #0x3f]
    // 0x15ccbec: DecompressPointer r1
    //     0x15ccbec: add             x1, x1, HEAP, lsl #32
    // 0x15ccbf0: cmp             w1, NULL
    // 0x15ccbf4: b.ne            #0x15ccc00
    // 0x15ccbf8: r0 = Null
    //     0x15ccbf8: mov             x0, NULL
    // 0x15ccbfc: b               #0x15ccc08
    // 0x15ccc00: LoadField: r0 = r1->field_f
    //     0x15ccc00: ldur            w0, [x1, #0xf]
    // 0x15ccc04: DecompressPointer r0
    //     0x15ccc04: add             x0, x0, HEAP, lsl #32
    // 0x15ccc08: r1 = LoadClassIdInstr(r0)
    //     0x15ccc08: ldur            x1, [x0, #-1]
    //     0x15ccc0c: ubfx            x1, x1, #0xc, #0x14
    // 0x15ccc10: r16 = "image_text"
    //     0x15ccc10: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15ccc14: ldr             x16, [x16, #0xa88]
    // 0x15ccc18: stp             x16, x0, [SP]
    // 0x15ccc1c: mov             x0, x1
    // 0x15ccc20: mov             lr, x0
    // 0x15ccc24: ldr             lr, [x21, lr, lsl #3]
    // 0x15ccc28: blr             lr
    // 0x15ccc2c: tbnz            w0, #4, #0x15ccc38
    // 0x15ccc30: r2 = true
    //     0x15ccc30: add             x2, NULL, #0x20  ; true
    // 0x15ccc34: b               #0x15ccc98
    // 0x15ccc38: ldur            x0, [fp, #-8]
    // 0x15ccc3c: LoadField: r1 = r0->field_f
    //     0x15ccc3c: ldur            w1, [x0, #0xf]
    // 0x15ccc40: DecompressPointer r1
    //     0x15ccc40: add             x1, x1, HEAP, lsl #32
    // 0x15ccc44: r0 = controller()
    //     0x15ccc44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ccc48: LoadField: r1 = r0->field_53
    //     0x15ccc48: ldur            w1, [x0, #0x53]
    // 0x15ccc4c: DecompressPointer r1
    //     0x15ccc4c: add             x1, x1, HEAP, lsl #32
    // 0x15ccc50: r0 = value()
    //     0x15ccc50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ccc54: LoadField: r1 = r0->field_3f
    //     0x15ccc54: ldur            w1, [x0, #0x3f]
    // 0x15ccc58: DecompressPointer r1
    //     0x15ccc58: add             x1, x1, HEAP, lsl #32
    // 0x15ccc5c: cmp             w1, NULL
    // 0x15ccc60: b.ne            #0x15ccc6c
    // 0x15ccc64: r0 = Null
    //     0x15ccc64: mov             x0, NULL
    // 0x15ccc68: b               #0x15ccc74
    // 0x15ccc6c: LoadField: r0 = r1->field_f
    //     0x15ccc6c: ldur            w0, [x1, #0xf]
    // 0x15ccc70: DecompressPointer r0
    //     0x15ccc70: add             x0, x0, HEAP, lsl #32
    // 0x15ccc74: r1 = LoadClassIdInstr(r0)
    //     0x15ccc74: ldur            x1, [x0, #-1]
    //     0x15ccc78: ubfx            x1, x1, #0xc, #0x14
    // 0x15ccc7c: r16 = "image"
    //     0x15ccc7c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15ccc80: stp             x16, x0, [SP]
    // 0x15ccc84: mov             x0, x1
    // 0x15ccc88: mov             lr, x0
    // 0x15ccc8c: ldr             lr, [x21, lr, lsl #3]
    // 0x15ccc90: blr             lr
    // 0x15ccc94: mov             x2, x0
    // 0x15ccc98: ldur            x0, [fp, #-8]
    // 0x15ccc9c: stur            x2, [fp, #-0x10]
    // 0x15ccca0: LoadField: r1 = r0->field_f
    //     0x15ccca0: ldur            w1, [x0, #0xf]
    // 0x15ccca4: DecompressPointer r1
    //     0x15ccca4: add             x1, x1, HEAP, lsl #32
    // 0x15ccca8: r0 = controller()
    //     0x15ccca8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cccac: LoadField: r1 = r0->field_53
    //     0x15cccac: ldur            w1, [x0, #0x53]
    // 0x15cccb0: DecompressPointer r1
    //     0x15cccb0: add             x1, x1, HEAP, lsl #32
    // 0x15cccb4: r0 = value()
    //     0x15cccb4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cccb8: LoadField: r1 = r0->field_27
    //     0x15cccb8: ldur            w1, [x0, #0x27]
    // 0x15cccbc: DecompressPointer r1
    //     0x15cccbc: add             x1, x1, HEAP, lsl #32
    // 0x15cccc0: cmp             w1, NULL
    // 0x15cccc4: b.ne            #0x15cccd0
    // 0x15cccc8: r2 = ""
    //     0x15cccc8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15ccccc: b               #0x15cccd4
    // 0x15cccd0: mov             x2, x1
    // 0x15cccd4: ldur            x0, [fp, #-8]
    // 0x15cccd8: ldur            x1, [fp, #-0x10]
    // 0x15cccdc: stur            x2, [fp, #-0x18]
    // 0x15ccce0: r0 = CachedNetworkImage()
    //     0x15ccce0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15ccce4: stur            x0, [fp, #-0x20]
    // 0x15ccce8: r16 = Instance_BoxFit
    //     0x15ccce8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cccec: ldr             x16, [x16, #0xb18]
    // 0x15cccf0: r30 = 50.000000
    //     0x15cccf0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cccf4: ldr             lr, [lr, #0xa90]
    // 0x15cccf8: stp             lr, x16, [SP, #8]
    // 0x15cccfc: r16 = 50.000000
    //     0x15cccfc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15ccd00: ldr             x16, [x16, #0xa90]
    // 0x15ccd04: str             x16, [SP]
    // 0x15ccd08: mov             x1, x0
    // 0x15ccd0c: ldur            x2, [fp, #-0x18]
    // 0x15ccd10: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15ccd10: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d8e0] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15ccd14: ldr             x4, [x4, #0x8e0]
    // 0x15ccd18: r0 = CachedNetworkImage()
    //     0x15ccd18: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15ccd1c: r0 = Visibility()
    //     0x15ccd1c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15ccd20: mov             x2, x0
    // 0x15ccd24: ldur            x0, [fp, #-0x20]
    // 0x15ccd28: stur            x2, [fp, #-0x18]
    // 0x15ccd2c: StoreField: r2->field_b = r0
    //     0x15ccd2c: stur            w0, [x2, #0xb]
    // 0x15ccd30: r0 = Instance_SizedBox
    //     0x15ccd30: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15ccd34: StoreField: r2->field_f = r0
    //     0x15ccd34: stur            w0, [x2, #0xf]
    // 0x15ccd38: ldur            x1, [fp, #-0x10]
    // 0x15ccd3c: StoreField: r2->field_13 = r1
    //     0x15ccd3c: stur            w1, [x2, #0x13]
    // 0x15ccd40: r3 = false
    //     0x15ccd40: add             x3, NULL, #0x30  ; false
    // 0x15ccd44: ArrayStore: r2[0] = r3  ; List_4
    //     0x15ccd44: stur            w3, [x2, #0x17]
    // 0x15ccd48: StoreField: r2->field_1b = r3
    //     0x15ccd48: stur            w3, [x2, #0x1b]
    // 0x15ccd4c: StoreField: r2->field_1f = r3
    //     0x15ccd4c: stur            w3, [x2, #0x1f]
    // 0x15ccd50: StoreField: r2->field_23 = r3
    //     0x15ccd50: stur            w3, [x2, #0x23]
    // 0x15ccd54: StoreField: r2->field_27 = r3
    //     0x15ccd54: stur            w3, [x2, #0x27]
    // 0x15ccd58: StoreField: r2->field_2b = r3
    //     0x15ccd58: stur            w3, [x2, #0x2b]
    // 0x15ccd5c: ldur            x4, [fp, #-8]
    // 0x15ccd60: LoadField: r1 = r4->field_f
    //     0x15ccd60: ldur            w1, [x4, #0xf]
    // 0x15ccd64: DecompressPointer r1
    //     0x15ccd64: add             x1, x1, HEAP, lsl #32
    // 0x15ccd68: r0 = controller()
    //     0x15ccd68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ccd6c: LoadField: r1 = r0->field_53
    //     0x15ccd6c: ldur            w1, [x0, #0x53]
    // 0x15ccd70: DecompressPointer r1
    //     0x15ccd70: add             x1, x1, HEAP, lsl #32
    // 0x15ccd74: r0 = value()
    //     0x15ccd74: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ccd78: LoadField: r1 = r0->field_3f
    //     0x15ccd78: ldur            w1, [x0, #0x3f]
    // 0x15ccd7c: DecompressPointer r1
    //     0x15ccd7c: add             x1, x1, HEAP, lsl #32
    // 0x15ccd80: cmp             w1, NULL
    // 0x15ccd84: b.ne            #0x15ccd90
    // 0x15ccd88: r0 = Null
    //     0x15ccd88: mov             x0, NULL
    // 0x15ccd8c: b               #0x15ccd98
    // 0x15ccd90: LoadField: r0 = r1->field_f
    //     0x15ccd90: ldur            w0, [x1, #0xf]
    // 0x15ccd94: DecompressPointer r0
    //     0x15ccd94: add             x0, x0, HEAP, lsl #32
    // 0x15ccd98: r1 = LoadClassIdInstr(r0)
    //     0x15ccd98: ldur            x1, [x0, #-1]
    //     0x15ccd9c: ubfx            x1, x1, #0xc, #0x14
    // 0x15ccda0: r16 = "image_text"
    //     0x15ccda0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15ccda4: ldr             x16, [x16, #0xa88]
    // 0x15ccda8: stp             x16, x0, [SP]
    // 0x15ccdac: mov             x0, x1
    // 0x15ccdb0: mov             lr, x0
    // 0x15ccdb4: ldr             lr, [x21, lr, lsl #3]
    // 0x15ccdb8: blr             lr
    // 0x15ccdbc: tbnz            w0, #4, #0x15ccdc8
    // 0x15ccdc0: r2 = true
    //     0x15ccdc0: add             x2, NULL, #0x20  ; true
    // 0x15ccdc4: b               #0x15cce28
    // 0x15ccdc8: ldur            x0, [fp, #-8]
    // 0x15ccdcc: LoadField: r1 = r0->field_f
    //     0x15ccdcc: ldur            w1, [x0, #0xf]
    // 0x15ccdd0: DecompressPointer r1
    //     0x15ccdd0: add             x1, x1, HEAP, lsl #32
    // 0x15ccdd4: r0 = controller()
    //     0x15ccdd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ccdd8: LoadField: r1 = r0->field_53
    //     0x15ccdd8: ldur            w1, [x0, #0x53]
    // 0x15ccddc: DecompressPointer r1
    //     0x15ccddc: add             x1, x1, HEAP, lsl #32
    // 0x15ccde0: r0 = value()
    //     0x15ccde0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ccde4: LoadField: r1 = r0->field_3f
    //     0x15ccde4: ldur            w1, [x0, #0x3f]
    // 0x15ccde8: DecompressPointer r1
    //     0x15ccde8: add             x1, x1, HEAP, lsl #32
    // 0x15ccdec: cmp             w1, NULL
    // 0x15ccdf0: b.ne            #0x15ccdfc
    // 0x15ccdf4: r0 = Null
    //     0x15ccdf4: mov             x0, NULL
    // 0x15ccdf8: b               #0x15cce04
    // 0x15ccdfc: LoadField: r0 = r1->field_f
    //     0x15ccdfc: ldur            w0, [x1, #0xf]
    // 0x15cce00: DecompressPointer r0
    //     0x15cce00: add             x0, x0, HEAP, lsl #32
    // 0x15cce04: r1 = LoadClassIdInstr(r0)
    //     0x15cce04: ldur            x1, [x0, #-1]
    //     0x15cce08: ubfx            x1, x1, #0xc, #0x14
    // 0x15cce0c: r16 = "text"
    //     0x15cce0c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15cce10: stp             x16, x0, [SP]
    // 0x15cce14: mov             x0, x1
    // 0x15cce18: mov             lr, x0
    // 0x15cce1c: ldr             lr, [x21, lr, lsl #3]
    // 0x15cce20: blr             lr
    // 0x15cce24: mov             x2, x0
    // 0x15cce28: ldur            x0, [fp, #-8]
    // 0x15cce2c: stur            x2, [fp, #-0x10]
    // 0x15cce30: LoadField: r1 = r0->field_f
    //     0x15cce30: ldur            w1, [x0, #0xf]
    // 0x15cce34: DecompressPointer r1
    //     0x15cce34: add             x1, x1, HEAP, lsl #32
    // 0x15cce38: r0 = controller()
    //     0x15cce38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cce3c: LoadField: r1 = r0->field_53
    //     0x15cce3c: ldur            w1, [x0, #0x53]
    // 0x15cce40: DecompressPointer r1
    //     0x15cce40: add             x1, x1, HEAP, lsl #32
    // 0x15cce44: r0 = value()
    //     0x15cce44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cce48: LoadField: r1 = r0->field_2b
    //     0x15cce48: ldur            w1, [x0, #0x2b]
    // 0x15cce4c: DecompressPointer r1
    //     0x15cce4c: add             x1, x1, HEAP, lsl #32
    // 0x15cce50: cmp             w1, NULL
    // 0x15cce54: b.ne            #0x15cce60
    // 0x15cce58: r4 = ""
    //     0x15cce58: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cce5c: b               #0x15cce64
    // 0x15cce60: mov             x4, x1
    // 0x15cce64: ldur            x0, [fp, #-8]
    // 0x15cce68: ldur            x3, [fp, #-0x18]
    // 0x15cce6c: ldur            x2, [fp, #-0x10]
    // 0x15cce70: stur            x4, [fp, #-0x20]
    // 0x15cce74: LoadField: r1 = r0->field_13
    //     0x15cce74: ldur            w1, [x0, #0x13]
    // 0x15cce78: DecompressPointer r1
    //     0x15cce78: add             x1, x1, HEAP, lsl #32
    // 0x15cce7c: r0 = of()
    //     0x15cce7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cce80: LoadField: r1 = r0->field_87
    //     0x15cce80: ldur            w1, [x0, #0x87]
    // 0x15cce84: DecompressPointer r1
    //     0x15cce84: add             x1, x1, HEAP, lsl #32
    // 0x15cce88: LoadField: r0 = r1->field_7
    //     0x15cce88: ldur            w0, [x1, #7]
    // 0x15cce8c: DecompressPointer r0
    //     0x15cce8c: add             x0, x0, HEAP, lsl #32
    // 0x15cce90: r16 = 14.000000
    //     0x15cce90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x15cce94: ldr             x16, [x16, #0x1d8]
    // 0x15cce98: r30 = Instance_Color
    //     0x15cce98: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15cce9c: stp             lr, x16, [SP]
    // 0x15ccea0: mov             x1, x0
    // 0x15ccea4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15ccea4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15ccea8: ldr             x4, [x4, #0xaa0]
    // 0x15cceac: r0 = copyWith()
    //     0x15cceac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cceb0: stur            x0, [fp, #-8]
    // 0x15cceb4: r0 = Text()
    //     0x15cceb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cceb8: mov             x1, x0
    // 0x15ccebc: ldur            x0, [fp, #-0x20]
    // 0x15ccec0: stur            x1, [fp, #-0x28]
    // 0x15ccec4: StoreField: r1->field_b = r0
    //     0x15ccec4: stur            w0, [x1, #0xb]
    // 0x15ccec8: ldur            x0, [fp, #-8]
    // 0x15ccecc: StoreField: r1->field_13 = r0
    //     0x15ccecc: stur            w0, [x1, #0x13]
    // 0x15cced0: r0 = Visibility()
    //     0x15cced0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cced4: mov             x3, x0
    // 0x15cced8: ldur            x0, [fp, #-0x28]
    // 0x15ccedc: stur            x3, [fp, #-8]
    // 0x15ccee0: StoreField: r3->field_b = r0
    //     0x15ccee0: stur            w0, [x3, #0xb]
    // 0x15ccee4: r0 = Instance_SizedBox
    //     0x15ccee4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15ccee8: StoreField: r3->field_f = r0
    //     0x15ccee8: stur            w0, [x3, #0xf]
    // 0x15cceec: ldur            x0, [fp, #-0x10]
    // 0x15ccef0: StoreField: r3->field_13 = r0
    //     0x15ccef0: stur            w0, [x3, #0x13]
    // 0x15ccef4: r0 = false
    //     0x15ccef4: add             x0, NULL, #0x30  ; false
    // 0x15ccef8: ArrayStore: r3[0] = r0  ; List_4
    //     0x15ccef8: stur            w0, [x3, #0x17]
    // 0x15ccefc: StoreField: r3->field_1b = r0
    //     0x15ccefc: stur            w0, [x3, #0x1b]
    // 0x15ccf00: StoreField: r3->field_1f = r0
    //     0x15ccf00: stur            w0, [x3, #0x1f]
    // 0x15ccf04: StoreField: r3->field_23 = r0
    //     0x15ccf04: stur            w0, [x3, #0x23]
    // 0x15ccf08: StoreField: r3->field_27 = r0
    //     0x15ccf08: stur            w0, [x3, #0x27]
    // 0x15ccf0c: StoreField: r3->field_2b = r0
    //     0x15ccf0c: stur            w0, [x3, #0x2b]
    // 0x15ccf10: r1 = Null
    //     0x15ccf10: mov             x1, NULL
    // 0x15ccf14: r2 = 6
    //     0x15ccf14: movz            x2, #0x6
    // 0x15ccf18: r0 = AllocateArray()
    //     0x15ccf18: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15ccf1c: mov             x2, x0
    // 0x15ccf20: ldur            x0, [fp, #-0x18]
    // 0x15ccf24: stur            x2, [fp, #-0x10]
    // 0x15ccf28: StoreField: r2->field_f = r0
    //     0x15ccf28: stur            w0, [x2, #0xf]
    // 0x15ccf2c: r16 = Instance_SizedBox
    //     0x15ccf2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15ccf30: ldr             x16, [x16, #0xaa8]
    // 0x15ccf34: StoreField: r2->field_13 = r16
    //     0x15ccf34: stur            w16, [x2, #0x13]
    // 0x15ccf38: ldur            x0, [fp, #-8]
    // 0x15ccf3c: ArrayStore: r2[0] = r0  ; List_4
    //     0x15ccf3c: stur            w0, [x2, #0x17]
    // 0x15ccf40: r1 = <Widget>
    //     0x15ccf40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15ccf44: r0 = AllocateGrowableArray()
    //     0x15ccf44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15ccf48: mov             x1, x0
    // 0x15ccf4c: ldur            x0, [fp, #-0x10]
    // 0x15ccf50: stur            x1, [fp, #-8]
    // 0x15ccf54: StoreField: r1->field_f = r0
    //     0x15ccf54: stur            w0, [x1, #0xf]
    // 0x15ccf58: r0 = 6
    //     0x15ccf58: movz            x0, #0x6
    // 0x15ccf5c: StoreField: r1->field_b = r0
    //     0x15ccf5c: stur            w0, [x1, #0xb]
    // 0x15ccf60: r0 = Row()
    //     0x15ccf60: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15ccf64: r1 = Instance_Axis
    //     0x15ccf64: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15ccf68: StoreField: r0->field_f = r1
    //     0x15ccf68: stur            w1, [x0, #0xf]
    // 0x15ccf6c: r1 = Instance_MainAxisAlignment
    //     0x15ccf6c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15ccf70: ldr             x1, [x1, #0xab0]
    // 0x15ccf74: StoreField: r0->field_13 = r1
    //     0x15ccf74: stur            w1, [x0, #0x13]
    // 0x15ccf78: r1 = Instance_MainAxisSize
    //     0x15ccf78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15ccf7c: ldr             x1, [x1, #0xa10]
    // 0x15ccf80: ArrayStore: r0[0] = r1  ; List_4
    //     0x15ccf80: stur            w1, [x0, #0x17]
    // 0x15ccf84: r1 = Instance_CrossAxisAlignment
    //     0x15ccf84: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15ccf88: ldr             x1, [x1, #0xa18]
    // 0x15ccf8c: StoreField: r0->field_1b = r1
    //     0x15ccf8c: stur            w1, [x0, #0x1b]
    // 0x15ccf90: r1 = Instance_VerticalDirection
    //     0x15ccf90: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15ccf94: ldr             x1, [x1, #0xa20]
    // 0x15ccf98: StoreField: r0->field_23 = r1
    //     0x15ccf98: stur            w1, [x0, #0x23]
    // 0x15ccf9c: r1 = Instance_Clip
    //     0x15ccf9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15ccfa0: ldr             x1, [x1, #0x38]
    // 0x15ccfa4: StoreField: r0->field_2b = r1
    //     0x15ccfa4: stur            w1, [x0, #0x2b]
    // 0x15ccfa8: StoreField: r0->field_2f = rZR
    //     0x15ccfa8: stur            xzr, [x0, #0x2f]
    // 0x15ccfac: ldur            x1, [fp, #-8]
    // 0x15ccfb0: StoreField: r0->field_b = r1
    //     0x15ccfb0: stur            w1, [x0, #0xb]
    // 0x15ccfb4: LeaveFrame
    //     0x15ccfb4: mov             SP, fp
    //     0x15ccfb8: ldp             fp, lr, [SP], #0x10
    // 0x15ccfbc: ret
    //     0x15ccfbc: ret             
    // 0x15ccfc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ccfc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ccfc4: b               #0x15ccbd0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e8384, size: 0x24c
    // 0x15e8384: EnterFrame
    //     0x15e8384: stp             fp, lr, [SP, #-0x10]!
    //     0x15e8388: mov             fp, SP
    // 0x15e838c: AllocStack(0x28)
    //     0x15e838c: sub             SP, SP, #0x28
    // 0x15e8390: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e8390: stur            x1, [fp, #-8]
    //     0x15e8394: stur            x2, [fp, #-0x10]
    // 0x15e8398: CheckStackOverflow
    //     0x15e8398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e839c: cmp             SP, x16
    //     0x15e83a0: b.ls            #0x15e85c8
    // 0x15e83a4: r1 = 2
    //     0x15e83a4: movz            x1, #0x2
    // 0x15e83a8: r0 = AllocateContext()
    //     0x15e83a8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e83ac: ldur            x1, [fp, #-8]
    // 0x15e83b0: stur            x0, [fp, #-0x18]
    // 0x15e83b4: StoreField: r0->field_f = r1
    //     0x15e83b4: stur            w1, [x0, #0xf]
    // 0x15e83b8: ldur            x2, [fp, #-0x10]
    // 0x15e83bc: StoreField: r0->field_13 = r2
    //     0x15e83bc: stur            w2, [x0, #0x13]
    // 0x15e83c0: r0 = Obx()
    //     0x15e83c0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e83c4: ldur            x2, [fp, #-0x18]
    // 0x15e83c8: r1 = Function '<anonymous closure>':.
    //     0x15e83c8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbb8] AnonymousClosure: (0x15ccba8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15e83cc: ldr             x1, [x1, #0xbb8]
    // 0x15e83d0: stur            x0, [fp, #-0x10]
    // 0x15e83d4: r0 = AllocateClosure()
    //     0x15e83d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e83d8: mov             x1, x0
    // 0x15e83dc: ldur            x0, [fp, #-0x10]
    // 0x15e83e0: StoreField: r0->field_b = r1
    //     0x15e83e0: stur            w1, [x0, #0xb]
    // 0x15e83e4: ldur            x1, [fp, #-8]
    // 0x15e83e8: r0 = controller()
    //     0x15e83e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e83ec: mov             x1, x0
    // 0x15e83f0: r0 = configData()
    //     0x15e83f0: bl              #0x8a3140  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::configData
    // 0x15e83f4: tbnz            w0, #4, #0x15e848c
    // 0x15e83f8: ldur            x2, [fp, #-0x18]
    // 0x15e83fc: LoadField: r1 = r2->field_13
    //     0x15e83fc: ldur            w1, [x2, #0x13]
    // 0x15e8400: DecompressPointer r1
    //     0x15e8400: add             x1, x1, HEAP, lsl #32
    // 0x15e8404: r0 = of()
    //     0x15e8404: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e8408: LoadField: r1 = r0->field_5b
    //     0x15e8408: ldur            w1, [x0, #0x5b]
    // 0x15e840c: DecompressPointer r1
    //     0x15e840c: add             x1, x1, HEAP, lsl #32
    // 0x15e8410: stur            x1, [fp, #-8]
    // 0x15e8414: r0 = ColorFilter()
    //     0x15e8414: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e8418: mov             x1, x0
    // 0x15e841c: ldur            x0, [fp, #-8]
    // 0x15e8420: stur            x1, [fp, #-0x20]
    // 0x15e8424: StoreField: r1->field_7 = r0
    //     0x15e8424: stur            w0, [x1, #7]
    // 0x15e8428: r0 = Instance_BlendMode
    //     0x15e8428: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e842c: ldr             x0, [x0, #0xb30]
    // 0x15e8430: StoreField: r1->field_b = r0
    //     0x15e8430: stur            w0, [x1, #0xb]
    // 0x15e8434: r2 = 1
    //     0x15e8434: movz            x2, #0x1
    // 0x15e8438: StoreField: r1->field_13 = r2
    //     0x15e8438: stur            x2, [x1, #0x13]
    // 0x15e843c: r0 = SvgPicture()
    //     0x15e843c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e8440: stur            x0, [fp, #-8]
    // 0x15e8444: ldur            x16, [fp, #-0x20]
    // 0x15e8448: str             x16, [SP]
    // 0x15e844c: mov             x1, x0
    // 0x15e8450: r2 = "assets/images/search.svg"
    //     0x15e8450: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e8454: ldr             x2, [x2, #0xa30]
    // 0x15e8458: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e8458: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e845c: ldr             x4, [x4, #0xa38]
    // 0x15e8460: r0 = SvgPicture.asset()
    //     0x15e8460: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e8464: r0 = Align()
    //     0x15e8464: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e8468: r3 = Instance_Alignment
    //     0x15e8468: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e846c: ldr             x3, [x3, #0xb10]
    // 0x15e8470: StoreField: r0->field_f = r3
    //     0x15e8470: stur            w3, [x0, #0xf]
    // 0x15e8474: r4 = 1.000000
    //     0x15e8474: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e8478: StoreField: r0->field_13 = r4
    //     0x15e8478: stur            w4, [x0, #0x13]
    // 0x15e847c: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e847c: stur            w4, [x0, #0x17]
    // 0x15e8480: ldur            x1, [fp, #-8]
    // 0x15e8484: StoreField: r0->field_b = r1
    //     0x15e8484: stur            w1, [x0, #0xb]
    // 0x15e8488: b               #0x15e853c
    // 0x15e848c: ldur            x5, [fp, #-0x18]
    // 0x15e8490: r4 = 1.000000
    //     0x15e8490: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e8494: r0 = Instance_BlendMode
    //     0x15e8494: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e8498: ldr             x0, [x0, #0xb30]
    // 0x15e849c: r3 = Instance_Alignment
    //     0x15e849c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e84a0: ldr             x3, [x3, #0xb10]
    // 0x15e84a4: r2 = 1
    //     0x15e84a4: movz            x2, #0x1
    // 0x15e84a8: LoadField: r1 = r5->field_13
    //     0x15e84a8: ldur            w1, [x5, #0x13]
    // 0x15e84ac: DecompressPointer r1
    //     0x15e84ac: add             x1, x1, HEAP, lsl #32
    // 0x15e84b0: r0 = of()
    //     0x15e84b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e84b4: LoadField: r1 = r0->field_5b
    //     0x15e84b4: ldur            w1, [x0, #0x5b]
    // 0x15e84b8: DecompressPointer r1
    //     0x15e84b8: add             x1, x1, HEAP, lsl #32
    // 0x15e84bc: stur            x1, [fp, #-8]
    // 0x15e84c0: r0 = ColorFilter()
    //     0x15e84c0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e84c4: mov             x1, x0
    // 0x15e84c8: ldur            x0, [fp, #-8]
    // 0x15e84cc: stur            x1, [fp, #-0x20]
    // 0x15e84d0: StoreField: r1->field_7 = r0
    //     0x15e84d0: stur            w0, [x1, #7]
    // 0x15e84d4: r0 = Instance_BlendMode
    //     0x15e84d4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e84d8: ldr             x0, [x0, #0xb30]
    // 0x15e84dc: StoreField: r1->field_b = r0
    //     0x15e84dc: stur            w0, [x1, #0xb]
    // 0x15e84e0: r0 = 1
    //     0x15e84e0: movz            x0, #0x1
    // 0x15e84e4: StoreField: r1->field_13 = r0
    //     0x15e84e4: stur            x0, [x1, #0x13]
    // 0x15e84e8: r0 = SvgPicture()
    //     0x15e84e8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e84ec: stur            x0, [fp, #-8]
    // 0x15e84f0: ldur            x16, [fp, #-0x20]
    // 0x15e84f4: str             x16, [SP]
    // 0x15e84f8: mov             x1, x0
    // 0x15e84fc: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e84fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e8500: ldr             x2, [x2, #0xa40]
    // 0x15e8504: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e8504: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e8508: ldr             x4, [x4, #0xa38]
    // 0x15e850c: r0 = SvgPicture.asset()
    //     0x15e850c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e8510: r0 = Align()
    //     0x15e8510: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e8514: mov             x1, x0
    // 0x15e8518: r0 = Instance_Alignment
    //     0x15e8518: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e851c: ldr             x0, [x0, #0xb10]
    // 0x15e8520: StoreField: r1->field_f = r0
    //     0x15e8520: stur            w0, [x1, #0xf]
    // 0x15e8524: r0 = 1.000000
    //     0x15e8524: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e8528: StoreField: r1->field_13 = r0
    //     0x15e8528: stur            w0, [x1, #0x13]
    // 0x15e852c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e852c: stur            w0, [x1, #0x17]
    // 0x15e8530: ldur            x0, [fp, #-8]
    // 0x15e8534: StoreField: r1->field_b = r0
    //     0x15e8534: stur            w0, [x1, #0xb]
    // 0x15e8538: mov             x0, x1
    // 0x15e853c: stur            x0, [fp, #-8]
    // 0x15e8540: r0 = InkWell()
    //     0x15e8540: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e8544: mov             x3, x0
    // 0x15e8548: ldur            x0, [fp, #-8]
    // 0x15e854c: stur            x3, [fp, #-0x20]
    // 0x15e8550: StoreField: r3->field_b = r0
    //     0x15e8550: stur            w0, [x3, #0xb]
    // 0x15e8554: ldur            x2, [fp, #-0x18]
    // 0x15e8558: r1 = Function '<anonymous closure>':.
    //     0x15e8558: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbc0] AnonymousClosure: (0x15e85d0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15e855c: ldr             x1, [x1, #0xbc0]
    // 0x15e8560: r0 = AllocateClosure()
    //     0x15e8560: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e8564: ldur            x2, [fp, #-0x20]
    // 0x15e8568: StoreField: r2->field_f = r0
    //     0x15e8568: stur            w0, [x2, #0xf]
    // 0x15e856c: r0 = true
    //     0x15e856c: add             x0, NULL, #0x20  ; true
    // 0x15e8570: StoreField: r2->field_43 = r0
    //     0x15e8570: stur            w0, [x2, #0x43]
    // 0x15e8574: r1 = Instance_BoxShape
    //     0x15e8574: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e8578: ldr             x1, [x1, #0x80]
    // 0x15e857c: StoreField: r2->field_47 = r1
    //     0x15e857c: stur            w1, [x2, #0x47]
    // 0x15e8580: StoreField: r2->field_6f = r0
    //     0x15e8580: stur            w0, [x2, #0x6f]
    // 0x15e8584: r1 = false
    //     0x15e8584: add             x1, NULL, #0x30  ; false
    // 0x15e8588: StoreField: r2->field_73 = r1
    //     0x15e8588: stur            w1, [x2, #0x73]
    // 0x15e858c: StoreField: r2->field_83 = r0
    //     0x15e858c: stur            w0, [x2, #0x83]
    // 0x15e8590: StoreField: r2->field_7b = r1
    //     0x15e8590: stur            w1, [x2, #0x7b]
    // 0x15e8594: r0 = AppBar()
    //     0x15e8594: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e8598: stur            x0, [fp, #-8]
    // 0x15e859c: ldur            x16, [fp, #-0x10]
    // 0x15e85a0: str             x16, [SP]
    // 0x15e85a4: mov             x1, x0
    // 0x15e85a8: ldur            x2, [fp, #-0x20]
    // 0x15e85ac: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e85ac: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e85b0: ldr             x4, [x4, #0xf00]
    // 0x15e85b4: r0 = AppBar()
    //     0x15e85b4: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e85b8: ldur            x0, [fp, #-8]
    // 0x15e85bc: LeaveFrame
    //     0x15e85bc: mov             SP, fp
    //     0x15e85c0: ldp             fp, lr, [SP], #0x10
    // 0x15e85c4: ret
    //     0x15e85c4: ret             
    // 0x15e85c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e85c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e85cc: b               #0x15e83a4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e85d0, size: 0xc8
    // 0x15e85d0: EnterFrame
    //     0x15e85d0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e85d4: mov             fp, SP
    // 0x15e85d8: AllocStack(0x18)
    //     0x15e85d8: sub             SP, SP, #0x18
    // 0x15e85dc: SetupParameters()
    //     0x15e85dc: ldr             x0, [fp, #0x10]
    //     0x15e85e0: ldur            w3, [x0, #0x17]
    //     0x15e85e4: add             x3, x3, HEAP, lsl #32
    //     0x15e85e8: stur            x3, [fp, #-8]
    // 0x15e85ec: CheckStackOverflow
    //     0x15e85ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e85f0: cmp             SP, x16
    //     0x15e85f4: b.ls            #0x15e8690
    // 0x15e85f8: LoadField: r1 = r3->field_f
    //     0x15e85f8: ldur            w1, [x3, #0xf]
    // 0x15e85fc: DecompressPointer r1
    //     0x15e85fc: add             x1, x1, HEAP, lsl #32
    // 0x15e8600: r2 = false
    //     0x15e8600: add             x2, NULL, #0x30  ; false
    // 0x15e8604: r0 = showLoading()
    //     0x15e8604: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e8608: ldur            x0, [fp, #-8]
    // 0x15e860c: LoadField: r1 = r0->field_f
    //     0x15e860c: ldur            w1, [x0, #0xf]
    // 0x15e8610: DecompressPointer r1
    //     0x15e8610: add             x1, x1, HEAP, lsl #32
    // 0x15e8614: r0 = controller()
    //     0x15e8614: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e8618: LoadField: r1 = r0->field_73
    //     0x15e8618: ldur            w1, [x0, #0x73]
    // 0x15e861c: DecompressPointer r1
    //     0x15e861c: add             x1, x1, HEAP, lsl #32
    // 0x15e8620: r0 = value()
    //     0x15e8620: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e8624: tbnz            w0, #4, #0x15e865c
    // 0x15e8628: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e8628: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e862c: ldr             x0, [x0, #0x1c80]
    //     0x15e8630: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e8634: cmp             w0, w16
    //     0x15e8638: b.ne            #0x15e8644
    //     0x15e863c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e8640: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e8644: r16 = "/search"
    //     0x15e8644: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e8648: ldr             x16, [x16, #0x838]
    // 0x15e864c: stp             x16, NULL, [SP]
    // 0x15e8650: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e8650: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e8654: r0 = GetNavigation.toNamed()
    //     0x15e8654: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e8658: b               #0x15e8680
    // 0x15e865c: ldur            x0, [fp, #-8]
    // 0x15e8660: LoadField: r1 = r0->field_f
    //     0x15e8660: ldur            w1, [x0, #0xf]
    // 0x15e8664: DecompressPointer r1
    //     0x15e8664: add             x1, x1, HEAP, lsl #32
    // 0x15e8668: r2 = false
    //     0x15e8668: add             x2, NULL, #0x30  ; false
    // 0x15e866c: r0 = showLoading()
    //     0x15e866c: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e8670: ldur            x0, [fp, #-8]
    // 0x15e8674: LoadField: r1 = r0->field_f
    //     0x15e8674: ldur            w1, [x0, #0xf]
    // 0x15e8678: DecompressPointer r1
    //     0x15e8678: add             x1, x1, HEAP, lsl #32
    // 0x15e867c: r0 = getBack()
    //     0x15e867c: bl              #0x1500cac  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack
    // 0x15e8680: r0 = Null
    //     0x15e8680: mov             x0, NULL
    // 0x15e8684: LeaveFrame
    //     0x15e8684: mov             SP, fp
    //     0x15e8688: ldp             fp, lr, [SP], #0x10
    // 0x15e868c: ret
    //     0x15e868c: ret             
    // 0x15e8690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e8690: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e8694: b               #0x15e85f8
  }
}
