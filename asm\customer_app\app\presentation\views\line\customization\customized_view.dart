// lib: , url: package:customer_app/app/presentation/views/line/customization/customized_view.dart

// class id: 1049511, size: 0x8
class :: {
}

// class id: 3254, size: 0x14, field offset: 0x14
class _CustomizedViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbda6f8, size: 0x5a4
    // 0xbda6f8: EnterFrame
    //     0xbda6f8: stp             fp, lr, [SP, #-0x10]!
    //     0xbda6fc: mov             fp, SP
    // 0xbda700: AllocStack(0x70)
    //     0xbda700: sub             SP, SP, #0x70
    // 0xbda704: SetupParameters(_CustomizedViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbda704: mov             x0, x1
    //     0xbda708: stur            x1, [fp, #-8]
    //     0xbda70c: mov             x1, x2
    //     0xbda710: stur            x2, [fp, #-0x10]
    // 0xbda714: CheckStackOverflow
    //     0xbda714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbda718: cmp             SP, x16
    //     0xbda71c: b.ls            #0xbdac88
    // 0xbda720: r1 = 1
    //     0xbda720: movz            x1, #0x1
    // 0xbda724: r0 = AllocateContext()
    //     0xbda724: bl              #0x16f6108  ; AllocateContextStub
    // 0xbda728: mov             x2, x0
    // 0xbda72c: ldur            x0, [fp, #-8]
    // 0xbda730: stur            x2, [fp, #-0x18]
    // 0xbda734: StoreField: r2->field_f = r0
    //     0xbda734: stur            w0, [x2, #0xf]
    // 0xbda738: ldur            x1, [fp, #-0x10]
    // 0xbda73c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbda73c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbda740: r0 = _of()
    //     0xbda740: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbda744: LoadField: r1 = r0->field_23
    //     0xbda744: ldur            w1, [x0, #0x23]
    // 0xbda748: DecompressPointer r1
    //     0xbda748: add             x1, x1, HEAP, lsl #32
    // 0xbda74c: LoadField: d0 = r1->field_1f
    //     0xbda74c: ldur            d0, [x1, #0x1f]
    // 0xbda750: stur            d0, [fp, #-0x58]
    // 0xbda754: r0 = EdgeInsets()
    //     0xbda754: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xbda758: stur            x0, [fp, #-0x20]
    // 0xbda75c: StoreField: r0->field_7 = rZR
    //     0xbda75c: stur            xzr, [x0, #7]
    // 0xbda760: StoreField: r0->field_f = rZR
    //     0xbda760: stur            xzr, [x0, #0xf]
    // 0xbda764: ArrayStore: r0[0] = rZR  ; List_8
    //     0xbda764: stur            xzr, [x0, #0x17]
    // 0xbda768: ldur            d0, [fp, #-0x58]
    // 0xbda76c: StoreField: r0->field_1f = d0
    //     0xbda76c: stur            d0, [x0, #0x1f]
    // 0xbda770: ldur            x1, [fp, #-8]
    // 0xbda774: LoadField: r2 = r1->field_b
    //     0xbda774: ldur            w2, [x1, #0xb]
    // 0xbda778: DecompressPointer r2
    //     0xbda778: add             x2, x2, HEAP, lsl #32
    // 0xbda77c: cmp             w2, NULL
    // 0xbda780: b.eq            #0xbdac90
    // 0xbda784: LoadField: r3 = r2->field_b
    //     0xbda784: ldur            w3, [x2, #0xb]
    // 0xbda788: DecompressPointer r3
    //     0xbda788: add             x3, x3, HEAP, lsl #32
    // 0xbda78c: LoadField: r2 = r3->field_b
    //     0xbda78c: ldur            w2, [x3, #0xb]
    // 0xbda790: DecompressPointer r2
    //     0xbda790: add             x2, x2, HEAP, lsl #32
    // 0xbda794: cmp             w2, NULL
    // 0xbda798: b.ne            #0xbda7a4
    // 0xbda79c: r2 = Null
    //     0xbda79c: mov             x2, NULL
    // 0xbda7a0: b               #0xbda7b0
    // 0xbda7a4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbda7a4: ldur            w3, [x2, #0x17]
    // 0xbda7a8: DecompressPointer r3
    //     0xbda7a8: add             x3, x3, HEAP, lsl #32
    // 0xbda7ac: mov             x2, x3
    // 0xbda7b0: str             x2, [SP]
    // 0xbda7b4: r0 = _interpolateSingle()
    //     0xbda7b4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbda7b8: ldur            x1, [fp, #-0x10]
    // 0xbda7bc: stur            x0, [fp, #-0x10]
    // 0xbda7c0: r0 = of()
    //     0xbda7c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbda7c4: LoadField: r1 = r0->field_87
    //     0xbda7c4: ldur            w1, [x0, #0x87]
    // 0xbda7c8: DecompressPointer r1
    //     0xbda7c8: add             x1, x1, HEAP, lsl #32
    // 0xbda7cc: LoadField: r0 = r1->field_2b
    //     0xbda7cc: ldur            w0, [x1, #0x2b]
    // 0xbda7d0: DecompressPointer r0
    //     0xbda7d0: add             x0, x0, HEAP, lsl #32
    // 0xbda7d4: stur            x0, [fp, #-0x28]
    // 0xbda7d8: r1 = Instance_Color
    //     0xbda7d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbda7dc: d0 = 0.400000
    //     0xbda7dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbda7e0: r0 = withOpacity()
    //     0xbda7e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbda7e4: r16 = Instance_FontWeight
    //     0xbda7e4: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xbda7e8: ldr             x16, [x16, #0x20]
    // 0xbda7ec: r30 = 14.000000
    //     0xbda7ec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbda7f0: ldr             lr, [lr, #0x1d8]
    // 0xbda7f4: stp             lr, x16, [SP, #8]
    // 0xbda7f8: str             x0, [SP]
    // 0xbda7fc: ldur            x1, [fp, #-0x28]
    // 0xbda800: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x2, fontWeight, 0x1, null]
    //     0xbda800: add             x4, PP, #0x53, lsl #12  ; [pp+0x53a58] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x2, "fontWeight", 0x1, Null]
    //     0xbda804: ldr             x4, [x4, #0xa58]
    // 0xbda808: r0 = copyWith()
    //     0xbda808: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbda80c: stur            x0, [fp, #-0x28]
    // 0xbda810: r0 = Text()
    //     0xbda810: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbda814: mov             x2, x0
    // 0xbda818: ldur            x0, [fp, #-0x10]
    // 0xbda81c: stur            x2, [fp, #-0x30]
    // 0xbda820: StoreField: r2->field_b = r0
    //     0xbda820: stur            w0, [x2, #0xb]
    // 0xbda824: ldur            x0, [fp, #-0x28]
    // 0xbda828: StoreField: r2->field_13 = r0
    //     0xbda828: stur            w0, [x2, #0x13]
    // 0xbda82c: r1 = <FlexParentData>
    //     0xbda82c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbda830: ldr             x1, [x1, #0xe00]
    // 0xbda834: r0 = Expanded()
    //     0xbda834: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbda838: mov             x1, x0
    // 0xbda83c: r0 = 1
    //     0xbda83c: movz            x0, #0x1
    // 0xbda840: stur            x1, [fp, #-0x10]
    // 0xbda844: StoreField: r1->field_13 = r0
    //     0xbda844: stur            x0, [x1, #0x13]
    // 0xbda848: r0 = Instance_FlexFit
    //     0xbda848: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbda84c: ldr             x0, [x0, #0xe08]
    // 0xbda850: StoreField: r1->field_1b = r0
    //     0xbda850: stur            w0, [x1, #0x1b]
    // 0xbda854: ldur            x0, [fp, #-0x30]
    // 0xbda858: StoreField: r1->field_b = r0
    //     0xbda858: stur            w0, [x1, #0xb]
    // 0xbda85c: r0 = InkWell()
    //     0xbda85c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbda860: mov             x3, x0
    // 0xbda864: r0 = Instance_Icon
    //     0xbda864: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xbda868: ldr             x0, [x0, #0x2b8]
    // 0xbda86c: stur            x3, [fp, #-0x28]
    // 0xbda870: StoreField: r3->field_b = r0
    //     0xbda870: stur            w0, [x3, #0xb]
    // 0xbda874: r1 = Function '<anonymous closure>':.
    //     0xbda874: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a60] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbda878: ldr             x1, [x1, #0xa60]
    // 0xbda87c: r2 = Null
    //     0xbda87c: mov             x2, NULL
    // 0xbda880: r0 = AllocateClosure()
    //     0xbda880: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbda884: mov             x1, x0
    // 0xbda888: ldur            x0, [fp, #-0x28]
    // 0xbda88c: StoreField: r0->field_f = r1
    //     0xbda88c: stur            w1, [x0, #0xf]
    // 0xbda890: r1 = true
    //     0xbda890: add             x1, NULL, #0x20  ; true
    // 0xbda894: StoreField: r0->field_43 = r1
    //     0xbda894: stur            w1, [x0, #0x43]
    // 0xbda898: r2 = Instance_BoxShape
    //     0xbda898: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbda89c: ldr             x2, [x2, #0x80]
    // 0xbda8a0: StoreField: r0->field_47 = r2
    //     0xbda8a0: stur            w2, [x0, #0x47]
    // 0xbda8a4: StoreField: r0->field_6f = r1
    //     0xbda8a4: stur            w1, [x0, #0x6f]
    // 0xbda8a8: r3 = false
    //     0xbda8a8: add             x3, NULL, #0x30  ; false
    // 0xbda8ac: StoreField: r0->field_73 = r3
    //     0xbda8ac: stur            w3, [x0, #0x73]
    // 0xbda8b0: StoreField: r0->field_83 = r1
    //     0xbda8b0: stur            w1, [x0, #0x83]
    // 0xbda8b4: StoreField: r0->field_7b = r3
    //     0xbda8b4: stur            w3, [x0, #0x7b]
    // 0xbda8b8: r1 = Null
    //     0xbda8b8: mov             x1, NULL
    // 0xbda8bc: r2 = 6
    //     0xbda8bc: movz            x2, #0x6
    // 0xbda8c0: r0 = AllocateArray()
    //     0xbda8c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbda8c4: mov             x2, x0
    // 0xbda8c8: ldur            x0, [fp, #-0x10]
    // 0xbda8cc: stur            x2, [fp, #-0x30]
    // 0xbda8d0: StoreField: r2->field_f = r0
    //     0xbda8d0: stur            w0, [x2, #0xf]
    // 0xbda8d4: r16 = Instance_SizedBox
    //     0xbda8d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbda8d8: ldr             x16, [x16, #0xb20]
    // 0xbda8dc: StoreField: r2->field_13 = r16
    //     0xbda8dc: stur            w16, [x2, #0x13]
    // 0xbda8e0: ldur            x0, [fp, #-0x28]
    // 0xbda8e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbda8e4: stur            w0, [x2, #0x17]
    // 0xbda8e8: r1 = <Widget>
    //     0xbda8e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbda8ec: r0 = AllocateGrowableArray()
    //     0xbda8ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbda8f0: mov             x1, x0
    // 0xbda8f4: ldur            x0, [fp, #-0x30]
    // 0xbda8f8: stur            x1, [fp, #-0x10]
    // 0xbda8fc: StoreField: r1->field_f = r0
    //     0xbda8fc: stur            w0, [x1, #0xf]
    // 0xbda900: r2 = 6
    //     0xbda900: movz            x2, #0x6
    // 0xbda904: StoreField: r1->field_b = r2
    //     0xbda904: stur            w2, [x1, #0xb]
    // 0xbda908: r0 = Row()
    //     0xbda908: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbda90c: mov             x1, x0
    // 0xbda910: r0 = Instance_Axis
    //     0xbda910: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbda914: stur            x1, [fp, #-0x28]
    // 0xbda918: StoreField: r1->field_f = r0
    //     0xbda918: stur            w0, [x1, #0xf]
    // 0xbda91c: r0 = Instance_MainAxisAlignment
    //     0xbda91c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbda920: ldr             x0, [x0, #0xa08]
    // 0xbda924: StoreField: r1->field_13 = r0
    //     0xbda924: stur            w0, [x1, #0x13]
    // 0xbda928: r2 = Instance_MainAxisSize
    //     0xbda928: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbda92c: ldr             x2, [x2, #0xa10]
    // 0xbda930: ArrayStore: r1[0] = r2  ; List_4
    //     0xbda930: stur            w2, [x1, #0x17]
    // 0xbda934: r3 = Instance_CrossAxisAlignment
    //     0xbda934: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbda938: ldr             x3, [x3, #0x890]
    // 0xbda93c: StoreField: r1->field_1b = r3
    //     0xbda93c: stur            w3, [x1, #0x1b]
    // 0xbda940: r4 = Instance_VerticalDirection
    //     0xbda940: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbda944: ldr             x4, [x4, #0xa20]
    // 0xbda948: StoreField: r1->field_23 = r4
    //     0xbda948: stur            w4, [x1, #0x23]
    // 0xbda94c: r5 = Instance_Clip
    //     0xbda94c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbda950: ldr             x5, [x5, #0x38]
    // 0xbda954: StoreField: r1->field_2b = r5
    //     0xbda954: stur            w5, [x1, #0x2b]
    // 0xbda958: StoreField: r1->field_2f = rZR
    //     0xbda958: stur            xzr, [x1, #0x2f]
    // 0xbda95c: ldur            x6, [fp, #-0x10]
    // 0xbda960: StoreField: r1->field_b = r6
    //     0xbda960: stur            w6, [x1, #0xb]
    // 0xbda964: r0 = Padding()
    //     0xbda964: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbda968: mov             x1, x0
    // 0xbda96c: r0 = Instance_EdgeInsets
    //     0xbda96c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xbda970: ldr             x0, [x0, #0x560]
    // 0xbda974: stur            x1, [fp, #-0x30]
    // 0xbda978: StoreField: r1->field_f = r0
    //     0xbda978: stur            w0, [x1, #0xf]
    // 0xbda97c: ldur            x0, [fp, #-0x28]
    // 0xbda980: StoreField: r1->field_b = r0
    //     0xbda980: stur            w0, [x1, #0xb]
    // 0xbda984: ldur            x0, [fp, #-8]
    // 0xbda988: LoadField: r2 = r0->field_b
    //     0xbda988: ldur            w2, [x0, #0xb]
    // 0xbda98c: DecompressPointer r2
    //     0xbda98c: add             x2, x2, HEAP, lsl #32
    // 0xbda990: cmp             w2, NULL
    // 0xbda994: b.eq            #0xbdac94
    // 0xbda998: LoadField: r3 = r2->field_b
    //     0xbda998: ldur            w3, [x2, #0xb]
    // 0xbda99c: DecompressPointer r3
    //     0xbda99c: add             x3, x3, HEAP, lsl #32
    // 0xbda9a0: LoadField: r2 = r3->field_b
    //     0xbda9a0: ldur            w2, [x3, #0xb]
    // 0xbda9a4: DecompressPointer r2
    //     0xbda9a4: add             x2, x2, HEAP, lsl #32
    // 0xbda9a8: cmp             w2, NULL
    // 0xbda9ac: b.ne            #0xbda9b8
    // 0xbda9b0: r3 = Null
    //     0xbda9b0: mov             x3, NULL
    // 0xbda9b4: b               #0xbdaa00
    // 0xbda9b8: LoadField: r3 = r2->field_1b
    //     0xbda9b8: ldur            w3, [x2, #0x1b]
    // 0xbda9bc: DecompressPointer r3
    //     0xbda9bc: add             x3, x3, HEAP, lsl #32
    // 0xbda9c0: cmp             w3, NULL
    // 0xbda9c4: b.ne            #0xbda9d0
    // 0xbda9c8: r3 = Null
    //     0xbda9c8: mov             x3, NULL
    // 0xbda9cc: b               #0xbdaa00
    // 0xbda9d0: LoadField: r4 = r3->field_7
    //     0xbda9d0: ldur            w4, [x3, #7]
    // 0xbda9d4: DecompressPointer r4
    //     0xbda9d4: add             x4, x4, HEAP, lsl #32
    // 0xbda9d8: cmp             w4, NULL
    // 0xbda9dc: b.ne            #0xbda9e8
    // 0xbda9e0: r3 = Null
    //     0xbda9e0: mov             x3, NULL
    // 0xbda9e4: b               #0xbdaa00
    // 0xbda9e8: LoadField: r3 = r4->field_7
    //     0xbda9e8: ldur            w3, [x4, #7]
    // 0xbda9ec: cbnz            w3, #0xbda9f8
    // 0xbda9f0: r4 = false
    //     0xbda9f0: add             x4, NULL, #0x30  ; false
    // 0xbda9f4: b               #0xbda9fc
    // 0xbda9f8: r4 = true
    //     0xbda9f8: add             x4, NULL, #0x20  ; true
    // 0xbda9fc: mov             x3, x4
    // 0xbdaa00: cmp             w3, NULL
    // 0xbdaa04: b.ne            #0xbdaa0c
    // 0xbdaa08: r3 = false
    //     0xbdaa08: add             x3, NULL, #0x30  ; false
    // 0xbdaa0c: stur            x3, [fp, #-0x28]
    // 0xbdaa10: cmp             w2, NULL
    // 0xbdaa14: b.ne            #0xbdaa20
    // 0xbdaa18: r2 = Null
    //     0xbdaa18: mov             x2, NULL
    // 0xbdaa1c: b               #0xbdaa40
    // 0xbdaa20: LoadField: r4 = r2->field_1b
    //     0xbdaa20: ldur            w4, [x2, #0x1b]
    // 0xbdaa24: DecompressPointer r4
    //     0xbdaa24: add             x4, x4, HEAP, lsl #32
    // 0xbdaa28: cmp             w4, NULL
    // 0xbdaa2c: b.ne            #0xbdaa38
    // 0xbdaa30: r2 = Null
    //     0xbdaa30: mov             x2, NULL
    // 0xbdaa34: b               #0xbdaa40
    // 0xbdaa38: LoadField: r2 = r4->field_7
    //     0xbdaa38: ldur            w2, [x4, #7]
    // 0xbdaa3c: DecompressPointer r2
    //     0xbdaa3c: add             x2, x2, HEAP, lsl #32
    // 0xbdaa40: cmp             w2, NULL
    // 0xbdaa44: b.ne            #0xbdaa4c
    // 0xbdaa48: r2 = ""
    //     0xbdaa48: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdaa4c: stur            x2, [fp, #-0x10]
    // 0xbdaa50: r0 = ImageHeaders.forImages()
    //     0xbdaa50: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbdaa54: r1 = Function '<anonymous closure>':.
    //     0xbdaa54: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a68] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xbdaa58: ldr             x1, [x1, #0xa68]
    // 0xbdaa5c: r2 = Null
    //     0xbdaa5c: mov             x2, NULL
    // 0xbdaa60: stur            x0, [fp, #-0x38]
    // 0xbdaa64: r0 = AllocateClosure()
    //     0xbdaa64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdaa68: r1 = Function '<anonymous closure>':.
    //     0xbdaa68: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a70] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdaa6c: ldr             x1, [x1, #0xa70]
    // 0xbdaa70: r2 = Null
    //     0xbdaa70: mov             x2, NULL
    // 0xbdaa74: stur            x0, [fp, #-0x40]
    // 0xbdaa78: r0 = AllocateClosure()
    //     0xbdaa78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdaa7c: stur            x0, [fp, #-0x48]
    // 0xbdaa80: r0 = CachedNetworkImage()
    //     0xbdaa80: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbdaa84: stur            x0, [fp, #-0x50]
    // 0xbdaa88: ldur            x16, [fp, #-0x38]
    // 0xbdaa8c: ldur            lr, [fp, #-0x40]
    // 0xbdaa90: stp             lr, x16, [SP, #8]
    // 0xbdaa94: ldur            x16, [fp, #-0x48]
    // 0xbdaa98: str             x16, [SP]
    // 0xbdaa9c: mov             x1, x0
    // 0xbdaaa0: ldur            x2, [fp, #-0x10]
    // 0xbdaaa4: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xbdaaa4: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e0] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xbdaaa8: ldr             x4, [x4, #0x4e0]
    // 0xbdaaac: r0 = CachedNetworkImage()
    //     0xbdaaac: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbdaab0: r0 = Padding()
    //     0xbdaab0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdaab4: mov             x1, x0
    // 0xbdaab8: r0 = Instance_EdgeInsets
    //     0xbdaab8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdaabc: ldr             x0, [x0, #0x1f0]
    // 0xbdaac0: stur            x1, [fp, #-0x10]
    // 0xbdaac4: StoreField: r1->field_f = r0
    //     0xbdaac4: stur            w0, [x1, #0xf]
    // 0xbdaac8: ldur            x0, [fp, #-0x50]
    // 0xbdaacc: StoreField: r1->field_b = r0
    //     0xbdaacc: stur            w0, [x1, #0xb]
    // 0xbdaad0: r0 = Visibility()
    //     0xbdaad0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbdaad4: mov             x3, x0
    // 0xbdaad8: ldur            x0, [fp, #-0x10]
    // 0xbdaadc: stur            x3, [fp, #-0x38]
    // 0xbdaae0: StoreField: r3->field_b = r0
    //     0xbdaae0: stur            w0, [x3, #0xb]
    // 0xbdaae4: r0 = Instance_SizedBox
    //     0xbdaae4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbdaae8: StoreField: r3->field_f = r0
    //     0xbdaae8: stur            w0, [x3, #0xf]
    // 0xbdaaec: ldur            x0, [fp, #-0x28]
    // 0xbdaaf0: StoreField: r3->field_13 = r0
    //     0xbdaaf0: stur            w0, [x3, #0x13]
    // 0xbdaaf4: r0 = false
    //     0xbdaaf4: add             x0, NULL, #0x30  ; false
    // 0xbdaaf8: ArrayStore: r3[0] = r0  ; List_4
    //     0xbdaaf8: stur            w0, [x3, #0x17]
    // 0xbdaafc: StoreField: r3->field_1b = r0
    //     0xbdaafc: stur            w0, [x3, #0x1b]
    // 0xbdab00: StoreField: r3->field_1f = r0
    //     0xbdab00: stur            w0, [x3, #0x1f]
    // 0xbdab04: StoreField: r3->field_23 = r0
    //     0xbdab04: stur            w0, [x3, #0x23]
    // 0xbdab08: StoreField: r3->field_27 = r0
    //     0xbdab08: stur            w0, [x3, #0x27]
    // 0xbdab0c: StoreField: r3->field_2b = r0
    //     0xbdab0c: stur            w0, [x3, #0x2b]
    // 0xbdab10: ldur            x0, [fp, #-8]
    // 0xbdab14: LoadField: r1 = r0->field_b
    //     0xbdab14: ldur            w1, [x0, #0xb]
    // 0xbdab18: DecompressPointer r1
    //     0xbdab18: add             x1, x1, HEAP, lsl #32
    // 0xbdab1c: cmp             w1, NULL
    // 0xbdab20: b.eq            #0xbdac98
    // 0xbdab24: LoadField: r0 = r1->field_b
    //     0xbdab24: ldur            w0, [x1, #0xb]
    // 0xbdab28: DecompressPointer r0
    //     0xbdab28: add             x0, x0, HEAP, lsl #32
    // 0xbdab2c: LoadField: r1 = r0->field_b
    //     0xbdab2c: ldur            w1, [x0, #0xb]
    // 0xbdab30: DecompressPointer r1
    //     0xbdab30: add             x1, x1, HEAP, lsl #32
    // 0xbdab34: cmp             w1, NULL
    // 0xbdab38: b.ne            #0xbdab44
    // 0xbdab3c: r5 = Null
    //     0xbdab3c: mov             x5, NULL
    // 0xbdab40: b               #0xbdab68
    // 0xbdab44: LoadField: r0 = r1->field_f
    //     0xbdab44: ldur            w0, [x1, #0xf]
    // 0xbdab48: DecompressPointer r0
    //     0xbdab48: add             x0, x0, HEAP, lsl #32
    // 0xbdab4c: cmp             w0, NULL
    // 0xbdab50: b.ne            #0xbdab5c
    // 0xbdab54: r0 = Null
    //     0xbdab54: mov             x0, NULL
    // 0xbdab58: b               #0xbdab64
    // 0xbdab5c: LoadField: r1 = r0->field_b
    //     0xbdab5c: ldur            w1, [x0, #0xb]
    // 0xbdab60: mov             x0, x1
    // 0xbdab64: mov             x5, x0
    // 0xbdab68: ldur            x4, [fp, #-0x20]
    // 0xbdab6c: ldur            x0, [fp, #-0x30]
    // 0xbdab70: ldur            x2, [fp, #-0x18]
    // 0xbdab74: stur            x5, [fp, #-8]
    // 0xbdab78: r1 = Function '<anonymous closure>':.
    //     0xbdab78: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a78] AnonymousClosure: (0xbdacbc), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdab7c: ldr             x1, [x1, #0xa78]
    // 0xbdab80: r0 = AllocateClosure()
    //     0xbdab80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdab84: stur            x0, [fp, #-0x10]
    // 0xbdab88: r0 = ListView()
    //     0xbdab88: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbdab8c: stur            x0, [fp, #-0x18]
    // 0xbdab90: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbdab90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbdab94: ldr             x16, [x16, #0x1c8]
    // 0xbdab98: r30 = Instance_Axis
    //     0xbdab98: ldr             lr, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdab9c: stp             lr, x16, [SP, #8]
    // 0xbdaba0: r16 = true
    //     0xbdaba0: add             x16, NULL, #0x20  ; true
    // 0xbdaba4: str             x16, [SP]
    // 0xbdaba8: mov             x1, x0
    // 0xbdabac: ldur            x2, [fp, #-0x10]
    // 0xbdabb0: ldur            x3, [fp, #-8]
    // 0xbdabb4: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x3, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0xbdabb4: add             x4, PP, #0x34, lsl #12  ; [pp+0x346d8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0xbdabb8: ldr             x4, [x4, #0x6d8]
    // 0xbdabbc: r0 = ListView.builder()
    //     0xbdabbc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbdabc0: r1 = Null
    //     0xbdabc0: mov             x1, NULL
    // 0xbdabc4: r2 = 6
    //     0xbdabc4: movz            x2, #0x6
    // 0xbdabc8: r0 = AllocateArray()
    //     0xbdabc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdabcc: mov             x2, x0
    // 0xbdabd0: ldur            x0, [fp, #-0x30]
    // 0xbdabd4: stur            x2, [fp, #-8]
    // 0xbdabd8: StoreField: r2->field_f = r0
    //     0xbdabd8: stur            w0, [x2, #0xf]
    // 0xbdabdc: ldur            x0, [fp, #-0x38]
    // 0xbdabe0: StoreField: r2->field_13 = r0
    //     0xbdabe0: stur            w0, [x2, #0x13]
    // 0xbdabe4: ldur            x0, [fp, #-0x18]
    // 0xbdabe8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdabe8: stur            w0, [x2, #0x17]
    // 0xbdabec: r1 = <Widget>
    //     0xbdabec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdabf0: r0 = AllocateGrowableArray()
    //     0xbdabf0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdabf4: mov             x1, x0
    // 0xbdabf8: ldur            x0, [fp, #-8]
    // 0xbdabfc: stur            x1, [fp, #-0x10]
    // 0xbdac00: StoreField: r1->field_f = r0
    //     0xbdac00: stur            w0, [x1, #0xf]
    // 0xbdac04: r0 = 6
    //     0xbdac04: movz            x0, #0x6
    // 0xbdac08: StoreField: r1->field_b = r0
    //     0xbdac08: stur            w0, [x1, #0xb]
    // 0xbdac0c: r0 = Column()
    //     0xbdac0c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdac10: mov             x1, x0
    // 0xbdac14: r0 = Instance_Axis
    //     0xbdac14: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdac18: stur            x1, [fp, #-8]
    // 0xbdac1c: StoreField: r1->field_f = r0
    //     0xbdac1c: stur            w0, [x1, #0xf]
    // 0xbdac20: r0 = Instance_MainAxisAlignment
    //     0xbdac20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdac24: ldr             x0, [x0, #0xa08]
    // 0xbdac28: StoreField: r1->field_13 = r0
    //     0xbdac28: stur            w0, [x1, #0x13]
    // 0xbdac2c: r0 = Instance_MainAxisSize
    //     0xbdac2c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdac30: ldr             x0, [x0, #0xa10]
    // 0xbdac34: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdac34: stur            w0, [x1, #0x17]
    // 0xbdac38: r0 = Instance_CrossAxisAlignment
    //     0xbdac38: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbdac3c: ldr             x0, [x0, #0x890]
    // 0xbdac40: StoreField: r1->field_1b = r0
    //     0xbdac40: stur            w0, [x1, #0x1b]
    // 0xbdac44: r0 = Instance_VerticalDirection
    //     0xbdac44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdac48: ldr             x0, [x0, #0xa20]
    // 0xbdac4c: StoreField: r1->field_23 = r0
    //     0xbdac4c: stur            w0, [x1, #0x23]
    // 0xbdac50: r0 = Instance_Clip
    //     0xbdac50: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdac54: ldr             x0, [x0, #0x38]
    // 0xbdac58: StoreField: r1->field_2b = r0
    //     0xbdac58: stur            w0, [x1, #0x2b]
    // 0xbdac5c: StoreField: r1->field_2f = rZR
    //     0xbdac5c: stur            xzr, [x1, #0x2f]
    // 0xbdac60: ldur            x0, [fp, #-0x10]
    // 0xbdac64: StoreField: r1->field_b = r0
    //     0xbdac64: stur            w0, [x1, #0xb]
    // 0xbdac68: r0 = Padding()
    //     0xbdac68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdac6c: ldur            x1, [fp, #-0x20]
    // 0xbdac70: StoreField: r0->field_f = r1
    //     0xbdac70: stur            w1, [x0, #0xf]
    // 0xbdac74: ldur            x1, [fp, #-8]
    // 0xbdac78: StoreField: r0->field_b = r1
    //     0xbdac78: stur            w1, [x0, #0xb]
    // 0xbdac7c: LeaveFrame
    //     0xbdac7c: mov             SP, fp
    //     0xbdac80: ldp             fp, lr, [SP], #0x10
    // 0xbdac84: ret
    //     0xbdac84: ret             
    // 0xbdac88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdac88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdac8c: b               #0xbda720
    // 0xbdac90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdac90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdac94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdac94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdac98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdac98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbdacbc, size: 0x79c
    // 0xbdacbc: EnterFrame
    //     0xbdacbc: stp             fp, lr, [SP, #-0x10]!
    //     0xbdacc0: mov             fp, SP
    // 0xbdacc4: AllocStack(0x28)
    //     0xbdacc4: sub             SP, SP, #0x28
    // 0xbdacc8: SetupParameters()
    //     0xbdacc8: ldr             x0, [fp, #0x20]
    //     0xbdaccc: ldur            w2, [x0, #0x17]
    //     0xbdacd0: add             x2, x2, HEAP, lsl #32
    //     0xbdacd4: stur            x2, [fp, #-8]
    // 0xbdacd8: CheckStackOverflow
    //     0xbdacd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdacdc: cmp             SP, x16
    //     0xbdace0: b.ls            #0xbdb420
    // 0xbdace4: r0 = Container()
    //     0xbdace4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdace8: mov             x1, x0
    // 0xbdacec: stur            x0, [fp, #-0x10]
    // 0xbdacf0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdacf0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdacf4: r0 = Container()
    //     0xbdacf4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdacf8: ldur            x2, [fp, #-8]
    // 0xbdacfc: LoadField: r0 = r2->field_f
    //     0xbdacfc: ldur            w0, [x2, #0xf]
    // 0xbdad00: DecompressPointer r0
    //     0xbdad00: add             x0, x0, HEAP, lsl #32
    // 0xbdad04: LoadField: r3 = r0->field_b
    //     0xbdad04: ldur            w3, [x0, #0xb]
    // 0xbdad08: DecompressPointer r3
    //     0xbdad08: add             x3, x3, HEAP, lsl #32
    // 0xbdad0c: cmp             w3, NULL
    // 0xbdad10: b.eq            #0xbdb428
    // 0xbdad14: LoadField: r0 = r3->field_b
    //     0xbdad14: ldur            w0, [x3, #0xb]
    // 0xbdad18: DecompressPointer r0
    //     0xbdad18: add             x0, x0, HEAP, lsl #32
    // 0xbdad1c: LoadField: r4 = r0->field_b
    //     0xbdad1c: ldur            w4, [x0, #0xb]
    // 0xbdad20: DecompressPointer r4
    //     0xbdad20: add             x4, x4, HEAP, lsl #32
    // 0xbdad24: cmp             w4, NULL
    // 0xbdad28: b.ne            #0xbdad34
    // 0xbdad2c: ldr             x6, [fp, #0x10]
    // 0xbdad30: b               #0xbdae7c
    // 0xbdad34: LoadField: r5 = r4->field_f
    //     0xbdad34: ldur            w5, [x4, #0xf]
    // 0xbdad38: DecompressPointer r5
    //     0xbdad38: add             x5, x5, HEAP, lsl #32
    // 0xbdad3c: cmp             w5, NULL
    // 0xbdad40: b.ne            #0xbdad4c
    // 0xbdad44: ldr             x6, [fp, #0x10]
    // 0xbdad48: b               #0xbdae7c
    // 0xbdad4c: ldr             x6, [fp, #0x10]
    // 0xbdad50: LoadField: r0 = r5->field_b
    //     0xbdad50: ldur            w0, [x5, #0xb]
    // 0xbdad54: r7 = LoadInt32Instr(r6)
    //     0xbdad54: sbfx            x7, x6, #1, #0x1f
    //     0xbdad58: tbz             w6, #0, #0xbdad60
    //     0xbdad5c: ldur            x7, [x6, #7]
    // 0xbdad60: r1 = LoadInt32Instr(r0)
    //     0xbdad60: sbfx            x1, x0, #1, #0x1f
    // 0xbdad64: mov             x0, x1
    // 0xbdad68: mov             x1, x7
    // 0xbdad6c: cmp             x1, x0
    // 0xbdad70: b.hs            #0xbdb42c
    // 0xbdad74: LoadField: r0 = r5->field_f
    //     0xbdad74: ldur            w0, [x5, #0xf]
    // 0xbdad78: DecompressPointer r0
    //     0xbdad78: add             x0, x0, HEAP, lsl #32
    // 0xbdad7c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbdad7c: add             x16, x0, x7, lsl #2
    //     0xbdad80: ldur            w1, [x16, #0xf]
    // 0xbdad84: DecompressPointer r1
    //     0xbdad84: add             x1, x1, HEAP, lsl #32
    // 0xbdad88: LoadField: r0 = r1->field_b
    //     0xbdad88: ldur            w0, [x1, #0xb]
    // 0xbdad8c: DecompressPointer r0
    //     0xbdad8c: add             x0, x0, HEAP, lsl #32
    // 0xbdad90: r16 = Instance_CustomisationType
    //     0xbdad90: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xbdad94: ldr             x16, [x16, #0x680]
    // 0xbdad98: cmp             w0, w16
    // 0xbdad9c: b.ne            #0xbdae7c
    // 0xbdada0: cmp             w4, NULL
    // 0xbdada4: b.ne            #0xbdadb0
    // 0xbdada8: r0 = Null
    //     0xbdada8: mov             x0, NULL
    // 0xbdadac: b               #0xbdadf8
    // 0xbdadb0: LoadField: r5 = r4->field_f
    //     0xbdadb0: ldur            w5, [x4, #0xf]
    // 0xbdadb4: DecompressPointer r5
    //     0xbdadb4: add             x5, x5, HEAP, lsl #32
    // 0xbdadb8: cmp             w5, NULL
    // 0xbdadbc: b.ne            #0xbdadc8
    // 0xbdadc0: r0 = Null
    //     0xbdadc0: mov             x0, NULL
    // 0xbdadc4: b               #0xbdadf8
    // 0xbdadc8: LoadField: r0 = r5->field_b
    //     0xbdadc8: ldur            w0, [x5, #0xb]
    // 0xbdadcc: r1 = LoadInt32Instr(r0)
    //     0xbdadcc: sbfx            x1, x0, #1, #0x1f
    // 0xbdadd0: mov             x0, x1
    // 0xbdadd4: mov             x1, x7
    // 0xbdadd8: cmp             x1, x0
    // 0xbdaddc: b.hs            #0xbdb430
    // 0xbdade0: LoadField: r0 = r5->field_f
    //     0xbdade0: ldur            w0, [x5, #0xf]
    // 0xbdade4: DecompressPointer r0
    //     0xbdade4: add             x0, x0, HEAP, lsl #32
    // 0xbdade8: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbdade8: add             x16, x0, x7, lsl #2
    //     0xbdadec: ldur            w1, [x16, #0xf]
    // 0xbdadf0: DecompressPointer r1
    //     0xbdadf0: add             x1, x1, HEAP, lsl #32
    // 0xbdadf4: mov             x0, x1
    // 0xbdadf8: stur            x0, [fp, #-0x20]
    // 0xbdadfc: LoadField: r1 = r3->field_f
    //     0xbdadfc: ldur            w1, [x3, #0xf]
    // 0xbdae00: DecompressPointer r1
    //     0xbdae00: add             x1, x1, HEAP, lsl #32
    // 0xbdae04: stur            x1, [fp, #-0x18]
    // 0xbdae08: r0 = CustomisationText()
    //     0xbdae08: bl              #0xbdb488  ; AllocateCustomisationTextStub -> CustomisationText (size=0x20)
    // 0xbdae0c: mov             x3, x0
    // 0xbdae10: ldur            x0, [fp, #-0x20]
    // 0xbdae14: stur            x3, [fp, #-0x28]
    // 0xbdae18: StoreField: r3->field_b = r0
    //     0xbdae18: stur            w0, [x3, #0xb]
    // 0xbdae1c: ldur            x0, [fp, #-0x18]
    // 0xbdae20: StoreField: r3->field_f = r0
    //     0xbdae20: stur            w0, [x3, #0xf]
    // 0xbdae24: ldur            x2, [fp, #-8]
    // 0xbdae28: r1 = Function '<anonymous closure>':.
    //     0xbdae28: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a80] AnonymousClosure: (0xbdbb9c), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdae2c: ldr             x1, [x1, #0xa80]
    // 0xbdae30: r0 = AllocateClosure()
    //     0xbdae30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdae34: mov             x1, x0
    // 0xbdae38: ldur            x0, [fp, #-0x28]
    // 0xbdae3c: StoreField: r0->field_13 = r1
    //     0xbdae3c: stur            w1, [x0, #0x13]
    // 0xbdae40: ldur            x2, [fp, #-8]
    // 0xbdae44: r1 = Function '<anonymous closure>':.
    //     0xbdae44: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a88] AnonymousClosure: (0xbdbb08), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdae48: ldr             x1, [x1, #0xa88]
    // 0xbdae4c: r0 = AllocateClosure()
    //     0xbdae4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdae50: mov             x1, x0
    // 0xbdae54: ldur            x0, [fp, #-0x28]
    // 0xbdae58: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdae58: stur            w1, [x0, #0x17]
    // 0xbdae5c: ldur            x2, [fp, #-8]
    // 0xbdae60: r1 = Function '<anonymous closure>':.
    //     0xbdae60: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a90] AnonymousClosure: (0xbdba7c), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdae64: ldr             x1, [x1, #0xa90]
    // 0xbdae68: r0 = AllocateClosure()
    //     0xbdae68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdae6c: mov             x1, x0
    // 0xbdae70: ldur            x0, [fp, #-0x28]
    // 0xbdae74: StoreField: r0->field_1b = r1
    //     0xbdae74: stur            w1, [x0, #0x1b]
    // 0xbdae78: b               #0xbdb414
    // 0xbdae7c: cmp             w4, NULL
    // 0xbdae80: b.eq            #0xbdafc4
    // 0xbdae84: LoadField: r2 = r4->field_f
    //     0xbdae84: ldur            w2, [x4, #0xf]
    // 0xbdae88: DecompressPointer r2
    //     0xbdae88: add             x2, x2, HEAP, lsl #32
    // 0xbdae8c: cmp             w2, NULL
    // 0xbdae90: b.eq            #0xbdafc4
    // 0xbdae94: LoadField: r0 = r2->field_b
    //     0xbdae94: ldur            w0, [x2, #0xb]
    // 0xbdae98: r5 = LoadInt32Instr(r6)
    //     0xbdae98: sbfx            x5, x6, #1, #0x1f
    //     0xbdae9c: tbz             w6, #0, #0xbdaea4
    //     0xbdaea0: ldur            x5, [x6, #7]
    // 0xbdaea4: r1 = LoadInt32Instr(r0)
    //     0xbdaea4: sbfx            x1, x0, #1, #0x1f
    // 0xbdaea8: mov             x0, x1
    // 0xbdaeac: mov             x1, x5
    // 0xbdaeb0: cmp             x1, x0
    // 0xbdaeb4: b.hs            #0xbdb434
    // 0xbdaeb8: LoadField: r0 = r2->field_f
    //     0xbdaeb8: ldur            w0, [x2, #0xf]
    // 0xbdaebc: DecompressPointer r0
    //     0xbdaebc: add             x0, x0, HEAP, lsl #32
    // 0xbdaec0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdaec0: add             x16, x0, x5, lsl #2
    //     0xbdaec4: ldur            w1, [x16, #0xf]
    // 0xbdaec8: DecompressPointer r1
    //     0xbdaec8: add             x1, x1, HEAP, lsl #32
    // 0xbdaecc: LoadField: r0 = r1->field_b
    //     0xbdaecc: ldur            w0, [x1, #0xb]
    // 0xbdaed0: DecompressPointer r0
    //     0xbdaed0: add             x0, x0, HEAP, lsl #32
    // 0xbdaed4: r16 = Instance_CustomisationType
    //     0xbdaed4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xbdaed8: ldr             x16, [x16, #0x690]
    // 0xbdaedc: cmp             w0, w16
    // 0xbdaee0: b.ne            #0xbdafc4
    // 0xbdaee4: cmp             w4, NULL
    // 0xbdaee8: b.ne            #0xbdaef4
    // 0xbdaeec: r0 = Null
    //     0xbdaeec: mov             x0, NULL
    // 0xbdaef0: b               #0xbdaf3c
    // 0xbdaef4: LoadField: r2 = r4->field_f
    //     0xbdaef4: ldur            w2, [x4, #0xf]
    // 0xbdaef8: DecompressPointer r2
    //     0xbdaef8: add             x2, x2, HEAP, lsl #32
    // 0xbdaefc: cmp             w2, NULL
    // 0xbdaf00: b.ne            #0xbdaf0c
    // 0xbdaf04: r0 = Null
    //     0xbdaf04: mov             x0, NULL
    // 0xbdaf08: b               #0xbdaf3c
    // 0xbdaf0c: LoadField: r0 = r2->field_b
    //     0xbdaf0c: ldur            w0, [x2, #0xb]
    // 0xbdaf10: r1 = LoadInt32Instr(r0)
    //     0xbdaf10: sbfx            x1, x0, #1, #0x1f
    // 0xbdaf14: mov             x0, x1
    // 0xbdaf18: mov             x1, x5
    // 0xbdaf1c: cmp             x1, x0
    // 0xbdaf20: b.hs            #0xbdb438
    // 0xbdaf24: LoadField: r0 = r2->field_f
    //     0xbdaf24: ldur            w0, [x2, #0xf]
    // 0xbdaf28: DecompressPointer r0
    //     0xbdaf28: add             x0, x0, HEAP, lsl #32
    // 0xbdaf2c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdaf2c: add             x16, x0, x5, lsl #2
    //     0xbdaf30: ldur            w1, [x16, #0xf]
    // 0xbdaf34: DecompressPointer r1
    //     0xbdaf34: add             x1, x1, HEAP, lsl #32
    // 0xbdaf38: mov             x0, x1
    // 0xbdaf3c: stur            x0, [fp, #-0x20]
    // 0xbdaf40: LoadField: r1 = r3->field_f
    //     0xbdaf40: ldur            w1, [x3, #0xf]
    // 0xbdaf44: DecompressPointer r1
    //     0xbdaf44: add             x1, x1, HEAP, lsl #32
    // 0xbdaf48: stur            x1, [fp, #-0x18]
    // 0xbdaf4c: r0 = CustomisationNumber()
    //     0xbdaf4c: bl              #0xbdb47c  ; AllocateCustomisationNumberStub -> CustomisationNumber (size=0x20)
    // 0xbdaf50: mov             x3, x0
    // 0xbdaf54: ldur            x0, [fp, #-0x20]
    // 0xbdaf58: stur            x3, [fp, #-0x28]
    // 0xbdaf5c: StoreField: r3->field_b = r0
    //     0xbdaf5c: stur            w0, [x3, #0xb]
    // 0xbdaf60: ldur            x0, [fp, #-0x18]
    // 0xbdaf64: StoreField: r3->field_f = r0
    //     0xbdaf64: stur            w0, [x3, #0xf]
    // 0xbdaf68: ldur            x2, [fp, #-8]
    // 0xbdaf6c: r1 = Function '<anonymous closure>':.
    //     0xbdaf6c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a98] AnonymousClosure: (0xbdb9f4), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdaf70: ldr             x1, [x1, #0xa98]
    // 0xbdaf74: r0 = AllocateClosure()
    //     0xbdaf74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdaf78: mov             x1, x0
    // 0xbdaf7c: ldur            x0, [fp, #-0x28]
    // 0xbdaf80: StoreField: r0->field_13 = r1
    //     0xbdaf80: stur            w1, [x0, #0x13]
    // 0xbdaf84: ldur            x2, [fp, #-8]
    // 0xbdaf88: r1 = Function '<anonymous closure>':.
    //     0xbdaf88: add             x1, PP, #0x53, lsl #12  ; [pp+0x53aa0] AnonymousClosure: (0xbdb960), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdaf8c: ldr             x1, [x1, #0xaa0]
    // 0xbdaf90: r0 = AllocateClosure()
    //     0xbdaf90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdaf94: mov             x1, x0
    // 0xbdaf98: ldur            x0, [fp, #-0x28]
    // 0xbdaf9c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdaf9c: stur            w1, [x0, #0x17]
    // 0xbdafa0: ldur            x2, [fp, #-8]
    // 0xbdafa4: r1 = Function '<anonymous closure>':.
    //     0xbdafa4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53aa8] AnonymousClosure: (0xbdb8d4), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdafa8: ldr             x1, [x1, #0xaa8]
    // 0xbdafac: r0 = AllocateClosure()
    //     0xbdafac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdafb0: mov             x1, x0
    // 0xbdafb4: ldur            x0, [fp, #-0x28]
    // 0xbdafb8: StoreField: r0->field_1b = r1
    //     0xbdafb8: stur            w1, [x0, #0x1b]
    // 0xbdafbc: mov             x1, x0
    // 0xbdafc0: b               #0xbdb410
    // 0xbdafc4: cmp             w4, NULL
    // 0xbdafc8: b.eq            #0xbdb1a0
    // 0xbdafcc: LoadField: r2 = r4->field_f
    //     0xbdafcc: ldur            w2, [x4, #0xf]
    // 0xbdafd0: DecompressPointer r2
    //     0xbdafd0: add             x2, x2, HEAP, lsl #32
    // 0xbdafd4: cmp             w2, NULL
    // 0xbdafd8: b.eq            #0xbdb1a0
    // 0xbdafdc: LoadField: r0 = r2->field_b
    //     0xbdafdc: ldur            w0, [x2, #0xb]
    // 0xbdafe0: r5 = LoadInt32Instr(r6)
    //     0xbdafe0: sbfx            x5, x6, #1, #0x1f
    //     0xbdafe4: tbz             w6, #0, #0xbdafec
    //     0xbdafe8: ldur            x5, [x6, #7]
    // 0xbdafec: r1 = LoadInt32Instr(r0)
    //     0xbdafec: sbfx            x1, x0, #1, #0x1f
    // 0xbdaff0: mov             x0, x1
    // 0xbdaff4: mov             x1, x5
    // 0xbdaff8: cmp             x1, x0
    // 0xbdaffc: b.hs            #0xbdb43c
    // 0xbdb000: LoadField: r0 = r2->field_f
    //     0xbdb000: ldur            w0, [x2, #0xf]
    // 0xbdb004: DecompressPointer r0
    //     0xbdb004: add             x0, x0, HEAP, lsl #32
    // 0xbdb008: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb008: add             x16, x0, x5, lsl #2
    //     0xbdb00c: ldur            w1, [x16, #0xf]
    // 0xbdb010: DecompressPointer r1
    //     0xbdb010: add             x1, x1, HEAP, lsl #32
    // 0xbdb014: LoadField: r0 = r1->field_b
    //     0xbdb014: ldur            w0, [x1, #0xb]
    // 0xbdb018: DecompressPointer r0
    //     0xbdb018: add             x0, x0, HEAP, lsl #32
    // 0xbdb01c: r16 = Instance_CustomisationType
    //     0xbdb01c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xbdb020: ldr             x16, [x16, #0x660]
    // 0xbdb024: cmp             w0, w16
    // 0xbdb028: b.ne            #0xbdb1a0
    // 0xbdb02c: cmp             w4, NULL
    // 0xbdb030: b.ne            #0xbdb03c
    // 0xbdb034: r0 = Null
    //     0xbdb034: mov             x0, NULL
    // 0xbdb038: b               #0xbdb0ac
    // 0xbdb03c: LoadField: r2 = r4->field_f
    //     0xbdb03c: ldur            w2, [x4, #0xf]
    // 0xbdb040: DecompressPointer r2
    //     0xbdb040: add             x2, x2, HEAP, lsl #32
    // 0xbdb044: cmp             w2, NULL
    // 0xbdb048: b.ne            #0xbdb054
    // 0xbdb04c: r0 = Null
    //     0xbdb04c: mov             x0, NULL
    // 0xbdb050: b               #0xbdb0ac
    // 0xbdb054: LoadField: r0 = r2->field_b
    //     0xbdb054: ldur            w0, [x2, #0xb]
    // 0xbdb058: r1 = LoadInt32Instr(r0)
    //     0xbdb058: sbfx            x1, x0, #1, #0x1f
    // 0xbdb05c: mov             x0, x1
    // 0xbdb060: mov             x1, x5
    // 0xbdb064: cmp             x1, x0
    // 0xbdb068: b.hs            #0xbdb440
    // 0xbdb06c: LoadField: r0 = r2->field_f
    //     0xbdb06c: ldur            w0, [x2, #0xf]
    // 0xbdb070: DecompressPointer r0
    //     0xbdb070: add             x0, x0, HEAP, lsl #32
    // 0xbdb074: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb074: add             x16, x0, x5, lsl #2
    //     0xbdb078: ldur            w1, [x16, #0xf]
    // 0xbdb07c: DecompressPointer r1
    //     0xbdb07c: add             x1, x1, HEAP, lsl #32
    // 0xbdb080: LoadField: r0 = r1->field_2f
    //     0xbdb080: ldur            w0, [x1, #0x2f]
    // 0xbdb084: DecompressPointer r0
    //     0xbdb084: add             x0, x0, HEAP, lsl #32
    // 0xbdb088: cmp             w0, NULL
    // 0xbdb08c: b.ne            #0xbdb098
    // 0xbdb090: r0 = Null
    //     0xbdb090: mov             x0, NULL
    // 0xbdb094: b               #0xbdb0ac
    // 0xbdb098: LoadField: r1 = r0->field_b
    //     0xbdb098: ldur            w1, [x0, #0xb]
    // 0xbdb09c: cbnz            w1, #0xbdb0a8
    // 0xbdb0a0: r0 = false
    //     0xbdb0a0: add             x0, NULL, #0x30  ; false
    // 0xbdb0a4: b               #0xbdb0ac
    // 0xbdb0a8: r0 = true
    //     0xbdb0a8: add             x0, NULL, #0x20  ; true
    // 0xbdb0ac: cmp             w0, NULL
    // 0xbdb0b0: b.eq            #0xbdb180
    // 0xbdb0b4: tbnz            w0, #4, #0xbdb180
    // 0xbdb0b8: cmp             w4, NULL
    // 0xbdb0bc: b.ne            #0xbdb0c8
    // 0xbdb0c0: r0 = Null
    //     0xbdb0c0: mov             x0, NULL
    // 0xbdb0c4: b               #0xbdb110
    // 0xbdb0c8: LoadField: r2 = r4->field_f
    //     0xbdb0c8: ldur            w2, [x4, #0xf]
    // 0xbdb0cc: DecompressPointer r2
    //     0xbdb0cc: add             x2, x2, HEAP, lsl #32
    // 0xbdb0d0: cmp             w2, NULL
    // 0xbdb0d4: b.ne            #0xbdb0e0
    // 0xbdb0d8: r0 = Null
    //     0xbdb0d8: mov             x0, NULL
    // 0xbdb0dc: b               #0xbdb110
    // 0xbdb0e0: LoadField: r0 = r2->field_b
    //     0xbdb0e0: ldur            w0, [x2, #0xb]
    // 0xbdb0e4: r1 = LoadInt32Instr(r0)
    //     0xbdb0e4: sbfx            x1, x0, #1, #0x1f
    // 0xbdb0e8: mov             x0, x1
    // 0xbdb0ec: mov             x1, x5
    // 0xbdb0f0: cmp             x1, x0
    // 0xbdb0f4: b.hs            #0xbdb444
    // 0xbdb0f8: LoadField: r0 = r2->field_f
    //     0xbdb0f8: ldur            w0, [x2, #0xf]
    // 0xbdb0fc: DecompressPointer r0
    //     0xbdb0fc: add             x0, x0, HEAP, lsl #32
    // 0xbdb100: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb100: add             x16, x0, x5, lsl #2
    //     0xbdb104: ldur            w1, [x16, #0xf]
    // 0xbdb108: DecompressPointer r1
    //     0xbdb108: add             x1, x1, HEAP, lsl #32
    // 0xbdb10c: mov             x0, x1
    // 0xbdb110: stur            x0, [fp, #-0x18]
    // 0xbdb114: r0 = CustomizationMultiSelect()
    //     0xbdb114: bl              #0xbdb470  ; AllocateCustomizationMultiSelectStub -> CustomizationMultiSelect (size=0x1c)
    // 0xbdb118: mov             x3, x0
    // 0xbdb11c: ldur            x0, [fp, #-0x18]
    // 0xbdb120: stur            x3, [fp, #-0x20]
    // 0xbdb124: StoreField: r3->field_b = r0
    //     0xbdb124: stur            w0, [x3, #0xb]
    // 0xbdb128: ldur            x2, [fp, #-8]
    // 0xbdb12c: r1 = Function '<anonymous closure>':.
    //     0xbdb12c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ab0] AnonymousClosure: (0xbdb854), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb130: ldr             x1, [x1, #0xab0]
    // 0xbdb134: r0 = AllocateClosure()
    //     0xbdb134: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb138: mov             x1, x0
    // 0xbdb13c: ldur            x0, [fp, #-0x20]
    // 0xbdb140: StoreField: r0->field_f = r1
    //     0xbdb140: stur            w1, [x0, #0xf]
    // 0xbdb144: ldur            x2, [fp, #-8]
    // 0xbdb148: r1 = Function '<anonymous closure>':.
    //     0xbdb148: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ab8] AnonymousClosure: (0xbdb7cc), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb14c: ldr             x1, [x1, #0xab8]
    // 0xbdb150: r0 = AllocateClosure()
    //     0xbdb150: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb154: mov             x1, x0
    // 0xbdb158: ldur            x0, [fp, #-0x20]
    // 0xbdb15c: StoreField: r0->field_13 = r1
    //     0xbdb15c: stur            w1, [x0, #0x13]
    // 0xbdb160: ldur            x2, [fp, #-8]
    // 0xbdb164: r1 = Function '<anonymous closure>':.
    //     0xbdb164: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ac0] AnonymousClosure: (0xbdb738), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb168: ldr             x1, [x1, #0xac0]
    // 0xbdb16c: r0 = AllocateClosure()
    //     0xbdb16c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb170: mov             x1, x0
    // 0xbdb174: ldur            x0, [fp, #-0x20]
    // 0xbdb178: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdb178: stur            w1, [x0, #0x17]
    // 0xbdb17c: b               #0xbdb198
    // 0xbdb180: r0 = Container()
    //     0xbdb180: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdb184: mov             x1, x0
    // 0xbdb188: stur            x0, [fp, #-0x18]
    // 0xbdb18c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdb18c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdb190: r0 = Container()
    //     0xbdb190: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdb194: ldur            x0, [fp, #-0x18]
    // 0xbdb198: mov             x1, x0
    // 0xbdb19c: b               #0xbdb410
    // 0xbdb1a0: cmp             w4, NULL
    // 0xbdb1a4: b.eq            #0xbdb2e8
    // 0xbdb1a8: LoadField: r2 = r4->field_f
    //     0xbdb1a8: ldur            w2, [x4, #0xf]
    // 0xbdb1ac: DecompressPointer r2
    //     0xbdb1ac: add             x2, x2, HEAP, lsl #32
    // 0xbdb1b0: cmp             w2, NULL
    // 0xbdb1b4: b.eq            #0xbdb2e8
    // 0xbdb1b8: LoadField: r0 = r2->field_b
    //     0xbdb1b8: ldur            w0, [x2, #0xb]
    // 0xbdb1bc: r5 = LoadInt32Instr(r6)
    //     0xbdb1bc: sbfx            x5, x6, #1, #0x1f
    //     0xbdb1c0: tbz             w6, #0, #0xbdb1c8
    //     0xbdb1c4: ldur            x5, [x6, #7]
    // 0xbdb1c8: r1 = LoadInt32Instr(r0)
    //     0xbdb1c8: sbfx            x1, x0, #1, #0x1f
    // 0xbdb1cc: mov             x0, x1
    // 0xbdb1d0: mov             x1, x5
    // 0xbdb1d4: cmp             x1, x0
    // 0xbdb1d8: b.hs            #0xbdb448
    // 0xbdb1dc: LoadField: r0 = r2->field_f
    //     0xbdb1dc: ldur            w0, [x2, #0xf]
    // 0xbdb1e0: DecompressPointer r0
    //     0xbdb1e0: add             x0, x0, HEAP, lsl #32
    // 0xbdb1e4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb1e4: add             x16, x0, x5, lsl #2
    //     0xbdb1e8: ldur            w1, [x16, #0xf]
    // 0xbdb1ec: DecompressPointer r1
    //     0xbdb1ec: add             x1, x1, HEAP, lsl #32
    // 0xbdb1f0: LoadField: r0 = r1->field_b
    //     0xbdb1f0: ldur            w0, [x1, #0xb]
    // 0xbdb1f4: DecompressPointer r0
    //     0xbdb1f4: add             x0, x0, HEAP, lsl #32
    // 0xbdb1f8: r16 = Instance_CustomisationType
    //     0xbdb1f8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xbdb1fc: ldr             x16, [x16, #0x670]
    // 0xbdb200: cmp             w0, w16
    // 0xbdb204: b.ne            #0xbdb2e8
    // 0xbdb208: cmp             w4, NULL
    // 0xbdb20c: b.ne            #0xbdb218
    // 0xbdb210: r0 = Null
    //     0xbdb210: mov             x0, NULL
    // 0xbdb214: b               #0xbdb260
    // 0xbdb218: LoadField: r2 = r4->field_f
    //     0xbdb218: ldur            w2, [x4, #0xf]
    // 0xbdb21c: DecompressPointer r2
    //     0xbdb21c: add             x2, x2, HEAP, lsl #32
    // 0xbdb220: cmp             w2, NULL
    // 0xbdb224: b.ne            #0xbdb230
    // 0xbdb228: r0 = Null
    //     0xbdb228: mov             x0, NULL
    // 0xbdb22c: b               #0xbdb260
    // 0xbdb230: LoadField: r0 = r2->field_b
    //     0xbdb230: ldur            w0, [x2, #0xb]
    // 0xbdb234: r1 = LoadInt32Instr(r0)
    //     0xbdb234: sbfx            x1, x0, #1, #0x1f
    // 0xbdb238: mov             x0, x1
    // 0xbdb23c: mov             x1, x5
    // 0xbdb240: cmp             x1, x0
    // 0xbdb244: b.hs            #0xbdb44c
    // 0xbdb248: LoadField: r0 = r2->field_f
    //     0xbdb248: ldur            w0, [x2, #0xf]
    // 0xbdb24c: DecompressPointer r0
    //     0xbdb24c: add             x0, x0, HEAP, lsl #32
    // 0xbdb250: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb250: add             x16, x0, x5, lsl #2
    //     0xbdb254: ldur            w1, [x16, #0xf]
    // 0xbdb258: DecompressPointer r1
    //     0xbdb258: add             x1, x1, HEAP, lsl #32
    // 0xbdb25c: mov             x0, x1
    // 0xbdb260: stur            x0, [fp, #-0x20]
    // 0xbdb264: LoadField: r1 = r3->field_f
    //     0xbdb264: ldur            w1, [x3, #0xf]
    // 0xbdb268: DecompressPointer r1
    //     0xbdb268: add             x1, x1, HEAP, lsl #32
    // 0xbdb26c: stur            x1, [fp, #-0x18]
    // 0xbdb270: r0 = CustomizationSingleSelect()
    //     0xbdb270: bl              #0xbdb464  ; AllocateCustomizationSingleSelectStub -> CustomizationSingleSelect (size=0x20)
    // 0xbdb274: mov             x3, x0
    // 0xbdb278: ldur            x0, [fp, #-0x20]
    // 0xbdb27c: stur            x3, [fp, #-0x28]
    // 0xbdb280: StoreField: r3->field_b = r0
    //     0xbdb280: stur            w0, [x3, #0xb]
    // 0xbdb284: ldur            x0, [fp, #-0x18]
    // 0xbdb288: StoreField: r3->field_f = r0
    //     0xbdb288: stur            w0, [x3, #0xf]
    // 0xbdb28c: ldur            x2, [fp, #-8]
    // 0xbdb290: r1 = Function '<anonymous closure>':.
    //     0xbdb290: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ac8] AnonymousClosure: (0xbdb6b0), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb294: ldr             x1, [x1, #0xac8]
    // 0xbdb298: r0 = AllocateClosure()
    //     0xbdb298: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb29c: mov             x1, x0
    // 0xbdb2a0: ldur            x0, [fp, #-0x28]
    // 0xbdb2a4: StoreField: r0->field_13 = r1
    //     0xbdb2a4: stur            w1, [x0, #0x13]
    // 0xbdb2a8: ldur            x2, [fp, #-8]
    // 0xbdb2ac: r1 = Function '<anonymous closure>':.
    //     0xbdb2ac: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ad0] AnonymousClosure: (0xbdb630), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb2b0: ldr             x1, [x1, #0xad0]
    // 0xbdb2b4: r0 = AllocateClosure()
    //     0xbdb2b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb2b8: mov             x1, x0
    // 0xbdb2bc: ldur            x0, [fp, #-0x28]
    // 0xbdb2c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdb2c0: stur            w1, [x0, #0x17]
    // 0xbdb2c4: ldur            x2, [fp, #-8]
    // 0xbdb2c8: r1 = Function '<anonymous closure>':.
    //     0xbdb2c8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ad8] AnonymousClosure: (0xbdb5a4), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb2cc: ldr             x1, [x1, #0xad8]
    // 0xbdb2d0: r0 = AllocateClosure()
    //     0xbdb2d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb2d4: mov             x1, x0
    // 0xbdb2d8: ldur            x0, [fp, #-0x28]
    // 0xbdb2dc: StoreField: r0->field_1b = r1
    //     0xbdb2dc: stur            w1, [x0, #0x1b]
    // 0xbdb2e0: mov             x1, x0
    // 0xbdb2e4: b               #0xbdb410
    // 0xbdb2e8: cmp             w4, NULL
    // 0xbdb2ec: b.eq            #0xbdb40c
    // 0xbdb2f0: LoadField: r2 = r4->field_f
    //     0xbdb2f0: ldur            w2, [x4, #0xf]
    // 0xbdb2f4: DecompressPointer r2
    //     0xbdb2f4: add             x2, x2, HEAP, lsl #32
    // 0xbdb2f8: cmp             w2, NULL
    // 0xbdb2fc: b.eq            #0xbdb40c
    // 0xbdb300: LoadField: r0 = r2->field_b
    //     0xbdb300: ldur            w0, [x2, #0xb]
    // 0xbdb304: r5 = LoadInt32Instr(r6)
    //     0xbdb304: sbfx            x5, x6, #1, #0x1f
    //     0xbdb308: tbz             w6, #0, #0xbdb310
    //     0xbdb30c: ldur            x5, [x6, #7]
    // 0xbdb310: r1 = LoadInt32Instr(r0)
    //     0xbdb310: sbfx            x1, x0, #1, #0x1f
    // 0xbdb314: mov             x0, x1
    // 0xbdb318: mov             x1, x5
    // 0xbdb31c: cmp             x1, x0
    // 0xbdb320: b.hs            #0xbdb450
    // 0xbdb324: LoadField: r0 = r2->field_f
    //     0xbdb324: ldur            w0, [x2, #0xf]
    // 0xbdb328: DecompressPointer r0
    //     0xbdb328: add             x0, x0, HEAP, lsl #32
    // 0xbdb32c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb32c: add             x16, x0, x5, lsl #2
    //     0xbdb330: ldur            w1, [x16, #0xf]
    // 0xbdb334: DecompressPointer r1
    //     0xbdb334: add             x1, x1, HEAP, lsl #32
    // 0xbdb338: LoadField: r0 = r1->field_b
    //     0xbdb338: ldur            w0, [x1, #0xb]
    // 0xbdb33c: DecompressPointer r0
    //     0xbdb33c: add             x0, x0, HEAP, lsl #32
    // 0xbdb340: r16 = Instance_CustomisationType
    //     0xbdb340: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xbdb344: ldr             x16, [x16, #0x650]
    // 0xbdb348: cmp             w0, w16
    // 0xbdb34c: b.ne            #0xbdb40c
    // 0xbdb350: cmp             w4, NULL
    // 0xbdb354: b.ne            #0xbdb360
    // 0xbdb358: r0 = Null
    //     0xbdb358: mov             x0, NULL
    // 0xbdb35c: b               #0xbdb3a8
    // 0xbdb360: LoadField: r2 = r4->field_f
    //     0xbdb360: ldur            w2, [x4, #0xf]
    // 0xbdb364: DecompressPointer r2
    //     0xbdb364: add             x2, x2, HEAP, lsl #32
    // 0xbdb368: cmp             w2, NULL
    // 0xbdb36c: b.ne            #0xbdb378
    // 0xbdb370: r0 = Null
    //     0xbdb370: mov             x0, NULL
    // 0xbdb374: b               #0xbdb3a8
    // 0xbdb378: LoadField: r0 = r2->field_b
    //     0xbdb378: ldur            w0, [x2, #0xb]
    // 0xbdb37c: r1 = LoadInt32Instr(r0)
    //     0xbdb37c: sbfx            x1, x0, #1, #0x1f
    // 0xbdb380: mov             x0, x1
    // 0xbdb384: mov             x1, x5
    // 0xbdb388: cmp             x1, x0
    // 0xbdb38c: b.hs            #0xbdb454
    // 0xbdb390: LoadField: r0 = r2->field_f
    //     0xbdb390: ldur            w0, [x2, #0xf]
    // 0xbdb394: DecompressPointer r0
    //     0xbdb394: add             x0, x0, HEAP, lsl #32
    // 0xbdb398: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbdb398: add             x16, x0, x5, lsl #2
    //     0xbdb39c: ldur            w1, [x16, #0xf]
    // 0xbdb3a0: DecompressPointer r1
    //     0xbdb3a0: add             x1, x1, HEAP, lsl #32
    // 0xbdb3a4: mov             x0, x1
    // 0xbdb3a8: stur            x0, [fp, #-0x20]
    // 0xbdb3ac: LoadField: r1 = r3->field_f
    //     0xbdb3ac: ldur            w1, [x3, #0xf]
    // 0xbdb3b0: DecompressPointer r1
    //     0xbdb3b0: add             x1, x1, HEAP, lsl #32
    // 0xbdb3b4: stur            x1, [fp, #-0x18]
    // 0xbdb3b8: r0 = CameraPicker()
    //     0xbdb3b8: bl              #0xbdb458  ; AllocateCameraPickerStub -> CameraPicker (size=0x1c)
    // 0xbdb3bc: mov             x3, x0
    // 0xbdb3c0: ldur            x0, [fp, #-0x20]
    // 0xbdb3c4: stur            x3, [fp, #-0x28]
    // 0xbdb3c8: StoreField: r3->field_b = r0
    //     0xbdb3c8: stur            w0, [x3, #0xb]
    // 0xbdb3cc: ldur            x0, [fp, #-0x18]
    // 0xbdb3d0: StoreField: r3->field_f = r0
    //     0xbdb3d0: stur            w0, [x3, #0xf]
    // 0xbdb3d4: ldur            x2, [fp, #-8]
    // 0xbdb3d8: r1 = Function '<anonymous closure>':.
    //     0xbdb3d8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ae0] AnonymousClosure: (0xbdb51c), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb3dc: ldr             x1, [x1, #0xae0]
    // 0xbdb3e0: r0 = AllocateClosure()
    //     0xbdb3e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb3e4: mov             x1, x0
    // 0xbdb3e8: ldur            x0, [fp, #-0x28]
    // 0xbdb3ec: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdb3ec: stur            w1, [x0, #0x17]
    // 0xbdb3f0: ldur            x2, [fp, #-8]
    // 0xbdb3f4: r1 = Function '<anonymous closure>':.
    //     0xbdb3f4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ae8] AnonymousClosure: (0xbdb494), in [package:customer_app/app/presentation/views/line/customization/customized_view.dart] _CustomizedViewState::build (0xbda6f8)
    //     0xbdb3f8: ldr             x1, [x1, #0xae8]
    // 0xbdb3fc: r0 = AllocateClosure()
    //     0xbdb3fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdb400: ldur            x1, [fp, #-0x28]
    // 0xbdb404: StoreField: r1->field_13 = r0
    //     0xbdb404: stur            w0, [x1, #0x13]
    // 0xbdb408: b               #0xbdb410
    // 0xbdb40c: ldur            x1, [fp, #-0x10]
    // 0xbdb410: mov             x0, x1
    // 0xbdb414: LeaveFrame
    //     0xbdb414: mov             SP, fp
    //     0xbdb418: ldp             fp, lr, [SP], #0x10
    // 0xbdb41c: ret
    //     0xbdb41c: ret             
    // 0xbdb420: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb420: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb424: b               #0xbdace4
    // 0xbdb428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb428: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdb42c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb42c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb430: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb430: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb434: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb434: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb438: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb438: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb43c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb43c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb440: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb440: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb444: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb444: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb448: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb448: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb44c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb44c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb450: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb450: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbdb454: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbdb454: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>) {
    // ** addr: 0xbdb494, size: 0x88
    // 0xbdb494: EnterFrame
    //     0xbdb494: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb498: mov             fp, SP
    // 0xbdb49c: AllocStack(0x18)
    //     0xbdb49c: sub             SP, SP, #0x18
    // 0xbdb4a0: SetupParameters()
    //     0xbdb4a0: ldr             x0, [fp, #0x20]
    //     0xbdb4a4: ldur            w1, [x0, #0x17]
    //     0xbdb4a8: add             x1, x1, HEAP, lsl #32
    // 0xbdb4ac: CheckStackOverflow
    //     0xbdb4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb4b0: cmp             SP, x16
    //     0xbdb4b4: b.ls            #0xbdb510
    // 0xbdb4b8: LoadField: r0 = r1->field_f
    //     0xbdb4b8: ldur            w0, [x1, #0xf]
    // 0xbdb4bc: DecompressPointer r0
    //     0xbdb4bc: add             x0, x0, HEAP, lsl #32
    // 0xbdb4c0: LoadField: r1 = r0->field_b
    //     0xbdb4c0: ldur            w1, [x0, #0xb]
    // 0xbdb4c4: DecompressPointer r1
    //     0xbdb4c4: add             x1, x1, HEAP, lsl #32
    // 0xbdb4c8: cmp             w1, NULL
    // 0xbdb4cc: b.eq            #0xbdb518
    // 0xbdb4d0: LoadField: r0 = r1->field_13
    //     0xbdb4d0: ldur            w0, [x1, #0x13]
    // 0xbdb4d4: DecompressPointer r0
    //     0xbdb4d4: add             x0, x0, HEAP, lsl #32
    // 0xbdb4d8: ldr             x16, [fp, #0x18]
    // 0xbdb4dc: stp             x16, x0, [SP, #8]
    // 0xbdb4e0: ldr             x16, [fp, #0x10]
    // 0xbdb4e4: str             x16, [SP]
    // 0xbdb4e8: r4 = 0
    //     0xbdb4e8: movz            x4, #0
    // 0xbdb4ec: ldr             x0, [SP, #0x10]
    // 0xbdb4f0: r16 = UnlinkedCall_0x613b5c
    //     0xbdb4f0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53af0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb4f4: add             x16, x16, #0xaf0
    // 0xbdb4f8: ldp             x5, lr, [x16]
    // 0xbdb4fc: blr             lr
    // 0xbdb500: r0 = Null
    //     0xbdb500: mov             x0, NULL
    // 0xbdb504: LeaveFrame
    //     0xbdb504: mov             SP, fp
    //     0xbdb508: ldp             fp, lr, [SP], #0x10
    // 0xbdb50c: ret
    //     0xbdb50c: ret             
    // 0xbdb510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb510: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb514: b               #0xbdb4b8
    // 0xbdb518: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb518: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0xbdb51c, size: 0x88
    // 0xbdb51c: EnterFrame
    //     0xbdb51c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb520: mov             fp, SP
    // 0xbdb524: AllocStack(0x18)
    //     0xbdb524: sub             SP, SP, #0x18
    // 0xbdb528: SetupParameters()
    //     0xbdb528: ldr             x0, [fp, #0x20]
    //     0xbdb52c: ldur            w1, [x0, #0x17]
    //     0xbdb530: add             x1, x1, HEAP, lsl #32
    // 0xbdb534: CheckStackOverflow
    //     0xbdb534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb538: cmp             SP, x16
    //     0xbdb53c: b.ls            #0xbdb598
    // 0xbdb540: LoadField: r0 = r1->field_f
    //     0xbdb540: ldur            w0, [x1, #0xf]
    // 0xbdb544: DecompressPointer r0
    //     0xbdb544: add             x0, x0, HEAP, lsl #32
    // 0xbdb548: LoadField: r1 = r0->field_b
    //     0xbdb548: ldur            w1, [x0, #0xb]
    // 0xbdb54c: DecompressPointer r1
    //     0xbdb54c: add             x1, x1, HEAP, lsl #32
    // 0xbdb550: cmp             w1, NULL
    // 0xbdb554: b.eq            #0xbdb5a0
    // 0xbdb558: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbdb558: ldur            w0, [x1, #0x17]
    // 0xbdb55c: DecompressPointer r0
    //     0xbdb55c: add             x0, x0, HEAP, lsl #32
    // 0xbdb560: ldr             x16, [fp, #0x18]
    // 0xbdb564: stp             x16, x0, [SP, #8]
    // 0xbdb568: ldr             x16, [fp, #0x10]
    // 0xbdb56c: str             x16, [SP]
    // 0xbdb570: r4 = 0
    //     0xbdb570: movz            x4, #0
    // 0xbdb574: ldr             x0, [SP, #0x10]
    // 0xbdb578: r16 = UnlinkedCall_0x613b5c
    //     0xbdb578: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb57c: add             x16, x16, #0xb00
    // 0xbdb580: ldp             x5, lr, [x16]
    // 0xbdb584: blr             lr
    // 0xbdb588: r0 = Null
    //     0xbdb588: mov             x0, NULL
    // 0xbdb58c: LeaveFrame
    //     0xbdb58c: mov             SP, fp
    //     0xbdb590: ldp             fp, lr, [SP], #0x10
    // 0xbdb594: ret
    //     0xbdb594: ret             
    // 0xbdb598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb598: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb59c: b               #0xbdb540
    // 0xbdb5a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb5a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, String, List<ProductCustomisation>) {
    // ** addr: 0xbdb5a4, size: 0x8c
    // 0xbdb5a4: EnterFrame
    //     0xbdb5a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb5a8: mov             fp, SP
    // 0xbdb5ac: AllocStack(0x20)
    //     0xbdb5ac: sub             SP, SP, #0x20
    // 0xbdb5b0: SetupParameters()
    //     0xbdb5b0: ldr             x0, [fp, #0x28]
    //     0xbdb5b4: ldur            w1, [x0, #0x17]
    //     0xbdb5b8: add             x1, x1, HEAP, lsl #32
    // 0xbdb5bc: CheckStackOverflow
    //     0xbdb5bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb5c0: cmp             SP, x16
    //     0xbdb5c4: b.ls            #0xbdb624
    // 0xbdb5c8: LoadField: r0 = r1->field_f
    //     0xbdb5c8: ldur            w0, [x1, #0xf]
    // 0xbdb5cc: DecompressPointer r0
    //     0xbdb5cc: add             x0, x0, HEAP, lsl #32
    // 0xbdb5d0: LoadField: r1 = r0->field_b
    //     0xbdb5d0: ldur            w1, [x0, #0xb]
    // 0xbdb5d4: DecompressPointer r1
    //     0xbdb5d4: add             x1, x1, HEAP, lsl #32
    // 0xbdb5d8: cmp             w1, NULL
    // 0xbdb5dc: b.eq            #0xbdb62c
    // 0xbdb5e0: LoadField: r0 = r1->field_47
    //     0xbdb5e0: ldur            w0, [x1, #0x47]
    // 0xbdb5e4: DecompressPointer r0
    //     0xbdb5e4: add             x0, x0, HEAP, lsl #32
    // 0xbdb5e8: ldr             x16, [fp, #0x20]
    // 0xbdb5ec: stp             x16, x0, [SP, #0x10]
    // 0xbdb5f0: ldr             x16, [fp, #0x18]
    // 0xbdb5f4: ldr             lr, [fp, #0x10]
    // 0xbdb5f8: stp             lr, x16, [SP]
    // 0xbdb5fc: r4 = 0
    //     0xbdb5fc: movz            x4, #0
    // 0xbdb600: ldr             x0, [SP, #0x18]
    // 0xbdb604: r16 = UnlinkedCall_0x613b5c
    //     0xbdb604: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb608: add             x16, x16, #0xb10
    // 0xbdb60c: ldp             x5, lr, [x16]
    // 0xbdb610: blr             lr
    // 0xbdb614: r0 = Null
    //     0xbdb614: mov             x0, NULL
    // 0xbdb618: LeaveFrame
    //     0xbdb618: mov             SP, fp
    //     0xbdb61c: ldp             fp, lr, [SP], #0x10
    // 0xbdb620: ret
    //     0xbdb620: ret             
    // 0xbdb624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb624: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb628: b               #0xbdb5c8
    // 0xbdb62c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb62c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0xbdb630, size: 0x80
    // 0xbdb630: EnterFrame
    //     0xbdb630: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb634: mov             fp, SP
    // 0xbdb638: AllocStack(0x10)
    //     0xbdb638: sub             SP, SP, #0x10
    // 0xbdb63c: SetupParameters()
    //     0xbdb63c: ldr             x0, [fp, #0x18]
    //     0xbdb640: ldur            w1, [x0, #0x17]
    //     0xbdb644: add             x1, x1, HEAP, lsl #32
    // 0xbdb648: CheckStackOverflow
    //     0xbdb648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb64c: cmp             SP, x16
    //     0xbdb650: b.ls            #0xbdb6a4
    // 0xbdb654: LoadField: r0 = r1->field_f
    //     0xbdb654: ldur            w0, [x1, #0xf]
    // 0xbdb658: DecompressPointer r0
    //     0xbdb658: add             x0, x0, HEAP, lsl #32
    // 0xbdb65c: LoadField: r1 = r0->field_b
    //     0xbdb65c: ldur            w1, [x0, #0xb]
    // 0xbdb660: DecompressPointer r1
    //     0xbdb660: add             x1, x1, HEAP, lsl #32
    // 0xbdb664: cmp             w1, NULL
    // 0xbdb668: b.eq            #0xbdb6ac
    // 0xbdb66c: LoadField: r0 = r1->field_43
    //     0xbdb66c: ldur            w0, [x1, #0x43]
    // 0xbdb670: DecompressPointer r0
    //     0xbdb670: add             x0, x0, HEAP, lsl #32
    // 0xbdb674: ldr             x16, [fp, #0x10]
    // 0xbdb678: stp             x16, x0, [SP]
    // 0xbdb67c: r4 = 0
    //     0xbdb67c: movz            x4, #0
    // 0xbdb680: ldr             x0, [SP, #8]
    // 0xbdb684: r16 = UnlinkedCall_0x613b5c
    //     0xbdb684: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb688: add             x16, x16, #0xb20
    // 0xbdb68c: ldp             x5, lr, [x16]
    // 0xbdb690: blr             lr
    // 0xbdb694: r0 = Null
    //     0xbdb694: mov             x0, NULL
    // 0xbdb698: LeaveFrame
    //     0xbdb698: mov             SP, fp
    //     0xbdb69c: ldp             fp, lr, [SP], #0x10
    // 0xbdb6a0: ret
    //     0xbdb6a0: ret             
    // 0xbdb6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb6a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb6a8: b               #0xbdb654
    // 0xbdb6ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb6ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool, int) {
    // ** addr: 0xbdb6b0, size: 0x88
    // 0xbdb6b0: EnterFrame
    //     0xbdb6b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb6b4: mov             fp, SP
    // 0xbdb6b8: AllocStack(0x18)
    //     0xbdb6b8: sub             SP, SP, #0x18
    // 0xbdb6bc: SetupParameters()
    //     0xbdb6bc: ldr             x0, [fp, #0x20]
    //     0xbdb6c0: ldur            w1, [x0, #0x17]
    //     0xbdb6c4: add             x1, x1, HEAP, lsl #32
    // 0xbdb6c8: CheckStackOverflow
    //     0xbdb6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb6cc: cmp             SP, x16
    //     0xbdb6d0: b.ls            #0xbdb72c
    // 0xbdb6d4: LoadField: r0 = r1->field_f
    //     0xbdb6d4: ldur            w0, [x1, #0xf]
    // 0xbdb6d8: DecompressPointer r0
    //     0xbdb6d8: add             x0, x0, HEAP, lsl #32
    // 0xbdb6dc: LoadField: r1 = r0->field_b
    //     0xbdb6dc: ldur            w1, [x0, #0xb]
    // 0xbdb6e0: DecompressPointer r1
    //     0xbdb6e0: add             x1, x1, HEAP, lsl #32
    // 0xbdb6e4: cmp             w1, NULL
    // 0xbdb6e8: b.eq            #0xbdb734
    // 0xbdb6ec: LoadField: r0 = r1->field_3f
    //     0xbdb6ec: ldur            w0, [x1, #0x3f]
    // 0xbdb6f0: DecompressPointer r0
    //     0xbdb6f0: add             x0, x0, HEAP, lsl #32
    // 0xbdb6f4: ldr             x16, [fp, #0x18]
    // 0xbdb6f8: stp             x16, x0, [SP, #8]
    // 0xbdb6fc: ldr             x16, [fp, #0x10]
    // 0xbdb700: str             x16, [SP]
    // 0xbdb704: r4 = 0
    //     0xbdb704: movz            x4, #0
    // 0xbdb708: ldr             x0, [SP, #0x10]
    // 0xbdb70c: r16 = UnlinkedCall_0x613b5c
    //     0xbdb70c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb710: add             x16, x16, #0xb30
    // 0xbdb714: ldp             x5, lr, [x16]
    // 0xbdb718: blr             lr
    // 0xbdb71c: r0 = Null
    //     0xbdb71c: mov             x0, NULL
    // 0xbdb720: LeaveFrame
    //     0xbdb720: mov             SP, fp
    //     0xbdb724: ldp             fp, lr, [SP], #0x10
    // 0xbdb728: ret
    //     0xbdb728: ret             
    // 0xbdb72c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb72c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb730: b               #0xbdb6d4
    // 0xbdb734: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb734: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, bool, bool, CustomerResponse) {
    // ** addr: 0xbdb738, size: 0x94
    // 0xbdb738: EnterFrame
    //     0xbdb738: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb73c: mov             fp, SP
    // 0xbdb740: AllocStack(0x28)
    //     0xbdb740: sub             SP, SP, #0x28
    // 0xbdb744: SetupParameters()
    //     0xbdb744: ldr             x0, [fp, #0x30]
    //     0xbdb748: ldur            w1, [x0, #0x17]
    //     0xbdb74c: add             x1, x1, HEAP, lsl #32
    // 0xbdb750: CheckStackOverflow
    //     0xbdb750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb754: cmp             SP, x16
    //     0xbdb758: b.ls            #0xbdb7c0
    // 0xbdb75c: LoadField: r0 = r1->field_f
    //     0xbdb75c: ldur            w0, [x1, #0xf]
    // 0xbdb760: DecompressPointer r0
    //     0xbdb760: add             x0, x0, HEAP, lsl #32
    // 0xbdb764: LoadField: r1 = r0->field_b
    //     0xbdb764: ldur            w1, [x0, #0xb]
    // 0xbdb768: DecompressPointer r1
    //     0xbdb768: add             x1, x1, HEAP, lsl #32
    // 0xbdb76c: cmp             w1, NULL
    // 0xbdb770: b.eq            #0xbdb7c8
    // 0xbdb774: LoadField: r0 = r1->field_3b
    //     0xbdb774: ldur            w0, [x1, #0x3b]
    // 0xbdb778: DecompressPointer r0
    //     0xbdb778: add             x0, x0, HEAP, lsl #32
    // 0xbdb77c: ldr             x16, [fp, #0x28]
    // 0xbdb780: stp             x16, x0, [SP, #0x18]
    // 0xbdb784: ldr             x16, [fp, #0x20]
    // 0xbdb788: ldr             lr, [fp, #0x18]
    // 0xbdb78c: stp             lr, x16, [SP, #8]
    // 0xbdb790: ldr             x16, [fp, #0x10]
    // 0xbdb794: str             x16, [SP]
    // 0xbdb798: r4 = 0
    //     0xbdb798: movz            x4, #0
    // 0xbdb79c: ldr             x0, [SP, #0x20]
    // 0xbdb7a0: r16 = UnlinkedCall_0x613b5c
    //     0xbdb7a0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb7a4: add             x16, x16, #0xb40
    // 0xbdb7a8: ldp             x5, lr, [x16]
    // 0xbdb7ac: blr             lr
    // 0xbdb7b0: r0 = Null
    //     0xbdb7b0: mov             x0, NULL
    // 0xbdb7b4: LeaveFrame
    //     0xbdb7b4: mov             SP, fp
    //     0xbdb7b8: ldp             fp, lr, [SP], #0x10
    // 0xbdb7bc: ret
    //     0xbdb7bc: ret             
    // 0xbdb7c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb7c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb7c4: b               #0xbdb75c
    // 0xbdb7c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb7c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, CustomerResponse) {
    // ** addr: 0xbdb7cc, size: 0x88
    // 0xbdb7cc: EnterFrame
    //     0xbdb7cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb7d0: mov             fp, SP
    // 0xbdb7d4: AllocStack(0x18)
    //     0xbdb7d4: sub             SP, SP, #0x18
    // 0xbdb7d8: SetupParameters()
    //     0xbdb7d8: ldr             x0, [fp, #0x20]
    //     0xbdb7dc: ldur            w1, [x0, #0x17]
    //     0xbdb7e0: add             x1, x1, HEAP, lsl #32
    // 0xbdb7e4: CheckStackOverflow
    //     0xbdb7e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb7e8: cmp             SP, x16
    //     0xbdb7ec: b.ls            #0xbdb848
    // 0xbdb7f0: LoadField: r0 = r1->field_f
    //     0xbdb7f0: ldur            w0, [x1, #0xf]
    // 0xbdb7f4: DecompressPointer r0
    //     0xbdb7f4: add             x0, x0, HEAP, lsl #32
    // 0xbdb7f8: LoadField: r1 = r0->field_b
    //     0xbdb7f8: ldur            w1, [x0, #0xb]
    // 0xbdb7fc: DecompressPointer r1
    //     0xbdb7fc: add             x1, x1, HEAP, lsl #32
    // 0xbdb800: cmp             w1, NULL
    // 0xbdb804: b.eq            #0xbdb850
    // 0xbdb808: LoadField: r0 = r1->field_37
    //     0xbdb808: ldur            w0, [x1, #0x37]
    // 0xbdb80c: DecompressPointer r0
    //     0xbdb80c: add             x0, x0, HEAP, lsl #32
    // 0xbdb810: ldr             x16, [fp, #0x18]
    // 0xbdb814: stp             x16, x0, [SP, #8]
    // 0xbdb818: ldr             x16, [fp, #0x10]
    // 0xbdb81c: str             x16, [SP]
    // 0xbdb820: r4 = 0
    //     0xbdb820: movz            x4, #0
    // 0xbdb824: ldr             x0, [SP, #0x10]
    // 0xbdb828: r16 = UnlinkedCall_0x613b5c
    //     0xbdb828: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b50] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb82c: add             x16, x16, #0xb50
    // 0xbdb830: ldp             x5, lr, [x16]
    // 0xbdb834: blr             lr
    // 0xbdb838: r0 = Null
    //     0xbdb838: mov             x0, NULL
    // 0xbdb83c: LeaveFrame
    //     0xbdb83c: mov             SP, fp
    //     0xbdb840: ldp             fp, lr, [SP], #0x10
    // 0xbdb844: ret
    //     0xbdb844: ret             
    // 0xbdb848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb848: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb84c: b               #0xbdb7f0
    // 0xbdb850: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb850: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0xbdb854, size: 0x80
    // 0xbdb854: EnterFrame
    //     0xbdb854: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb858: mov             fp, SP
    // 0xbdb85c: AllocStack(0x10)
    //     0xbdb85c: sub             SP, SP, #0x10
    // 0xbdb860: SetupParameters()
    //     0xbdb860: ldr             x0, [fp, #0x18]
    //     0xbdb864: ldur            w1, [x0, #0x17]
    //     0xbdb868: add             x1, x1, HEAP, lsl #32
    // 0xbdb86c: CheckStackOverflow
    //     0xbdb86c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb870: cmp             SP, x16
    //     0xbdb874: b.ls            #0xbdb8c8
    // 0xbdb878: LoadField: r0 = r1->field_f
    //     0xbdb878: ldur            w0, [x1, #0xf]
    // 0xbdb87c: DecompressPointer r0
    //     0xbdb87c: add             x0, x0, HEAP, lsl #32
    // 0xbdb880: LoadField: r1 = r0->field_b
    //     0xbdb880: ldur            w1, [x0, #0xb]
    // 0xbdb884: DecompressPointer r1
    //     0xbdb884: add             x1, x1, HEAP, lsl #32
    // 0xbdb888: cmp             w1, NULL
    // 0xbdb88c: b.eq            #0xbdb8d0
    // 0xbdb890: LoadField: r0 = r1->field_33
    //     0xbdb890: ldur            w0, [x1, #0x33]
    // 0xbdb894: DecompressPointer r0
    //     0xbdb894: add             x0, x0, HEAP, lsl #32
    // 0xbdb898: ldr             x16, [fp, #0x10]
    // 0xbdb89c: stp             x16, x0, [SP]
    // 0xbdb8a0: r4 = 0
    //     0xbdb8a0: movz            x4, #0
    // 0xbdb8a4: ldr             x0, [SP, #8]
    // 0xbdb8a8: r16 = UnlinkedCall_0x613b5c
    //     0xbdb8a8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb8ac: add             x16, x16, #0xb60
    // 0xbdb8b0: ldp             x5, lr, [x16]
    // 0xbdb8b4: blr             lr
    // 0xbdb8b8: r0 = Null
    //     0xbdb8b8: mov             x0, NULL
    // 0xbdb8bc: LeaveFrame
    //     0xbdb8bc: mov             SP, fp
    //     0xbdb8c0: ldp             fp, lr, [SP], #0x10
    // 0xbdb8c4: ret
    //     0xbdb8c4: ret             
    // 0xbdb8c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb8c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb8cc: b               #0xbdb878
    // 0xbdb8d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb8d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xbdb8d4, size: 0x8c
    // 0xbdb8d4: EnterFrame
    //     0xbdb8d4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb8d8: mov             fp, SP
    // 0xbdb8dc: AllocStack(0x20)
    //     0xbdb8dc: sub             SP, SP, #0x20
    // 0xbdb8e0: SetupParameters()
    //     0xbdb8e0: ldr             x0, [fp, #0x28]
    //     0xbdb8e4: ldur            w1, [x0, #0x17]
    //     0xbdb8e8: add             x1, x1, HEAP, lsl #32
    // 0xbdb8ec: CheckStackOverflow
    //     0xbdb8ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb8f0: cmp             SP, x16
    //     0xbdb8f4: b.ls            #0xbdb954
    // 0xbdb8f8: LoadField: r0 = r1->field_f
    //     0xbdb8f8: ldur            w0, [x1, #0xf]
    // 0xbdb8fc: DecompressPointer r0
    //     0xbdb8fc: add             x0, x0, HEAP, lsl #32
    // 0xbdb900: LoadField: r1 = r0->field_b
    //     0xbdb900: ldur            w1, [x0, #0xb]
    // 0xbdb904: DecompressPointer r1
    //     0xbdb904: add             x1, x1, HEAP, lsl #32
    // 0xbdb908: cmp             w1, NULL
    // 0xbdb90c: b.eq            #0xbdb95c
    // 0xbdb910: LoadField: r0 = r1->field_2f
    //     0xbdb910: ldur            w0, [x1, #0x2f]
    // 0xbdb914: DecompressPointer r0
    //     0xbdb914: add             x0, x0, HEAP, lsl #32
    // 0xbdb918: ldr             x16, [fp, #0x20]
    // 0xbdb91c: stp             x16, x0, [SP, #0x10]
    // 0xbdb920: ldr             x16, [fp, #0x18]
    // 0xbdb924: ldr             lr, [fp, #0x10]
    // 0xbdb928: stp             lr, x16, [SP]
    // 0xbdb92c: r4 = 0
    //     0xbdb92c: movz            x4, #0
    // 0xbdb930: ldr             x0, [SP, #0x18]
    // 0xbdb934: r16 = UnlinkedCall_0x613b5c
    //     0xbdb934: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb938: add             x16, x16, #0xb70
    // 0xbdb93c: ldp             x5, lr, [x16]
    // 0xbdb940: blr             lr
    // 0xbdb944: r0 = Null
    //     0xbdb944: mov             x0, NULL
    // 0xbdb948: LeaveFrame
    //     0xbdb948: mov             SP, fp
    //     0xbdb94c: ldp             fp, lr, [SP], #0x10
    // 0xbdb950: ret
    //     0xbdb950: ret             
    // 0xbdb954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb954: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb958: b               #0xbdb8f8
    // 0xbdb95c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb95c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xbdb960, size: 0x94
    // 0xbdb960: EnterFrame
    //     0xbdb960: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb964: mov             fp, SP
    // 0xbdb968: AllocStack(0x28)
    //     0xbdb968: sub             SP, SP, #0x28
    // 0xbdb96c: SetupParameters()
    //     0xbdb96c: ldr             x0, [fp, #0x30]
    //     0xbdb970: ldur            w1, [x0, #0x17]
    //     0xbdb974: add             x1, x1, HEAP, lsl #32
    // 0xbdb978: CheckStackOverflow
    //     0xbdb978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdb97c: cmp             SP, x16
    //     0xbdb980: b.ls            #0xbdb9e8
    // 0xbdb984: LoadField: r0 = r1->field_f
    //     0xbdb984: ldur            w0, [x1, #0xf]
    // 0xbdb988: DecompressPointer r0
    //     0xbdb988: add             x0, x0, HEAP, lsl #32
    // 0xbdb98c: LoadField: r1 = r0->field_b
    //     0xbdb98c: ldur            w1, [x0, #0xb]
    // 0xbdb990: DecompressPointer r1
    //     0xbdb990: add             x1, x1, HEAP, lsl #32
    // 0xbdb994: cmp             w1, NULL
    // 0xbdb998: b.eq            #0xbdb9f0
    // 0xbdb99c: LoadField: r0 = r1->field_2b
    //     0xbdb99c: ldur            w0, [x1, #0x2b]
    // 0xbdb9a0: DecompressPointer r0
    //     0xbdb9a0: add             x0, x0, HEAP, lsl #32
    // 0xbdb9a4: ldr             x16, [fp, #0x28]
    // 0xbdb9a8: stp             x16, x0, [SP, #0x18]
    // 0xbdb9ac: ldr             x16, [fp, #0x20]
    // 0xbdb9b0: ldr             lr, [fp, #0x18]
    // 0xbdb9b4: stp             lr, x16, [SP, #8]
    // 0xbdb9b8: ldr             x16, [fp, #0x10]
    // 0xbdb9bc: str             x16, [SP]
    // 0xbdb9c0: r4 = 0
    //     0xbdb9c0: movz            x4, #0
    // 0xbdb9c4: ldr             x0, [SP, #0x20]
    // 0xbdb9c8: r16 = UnlinkedCall_0x613b5c
    //     0xbdb9c8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdb9cc: add             x16, x16, #0xb80
    // 0xbdb9d0: ldp             x5, lr, [x16]
    // 0xbdb9d4: blr             lr
    // 0xbdb9d8: r0 = Null
    //     0xbdb9d8: mov             x0, NULL
    // 0xbdb9dc: LeaveFrame
    //     0xbdb9dc: mov             SP, fp
    //     0xbdb9e0: ldp             fp, lr, [SP], #0x10
    // 0xbdb9e4: ret
    //     0xbdb9e4: ret             
    // 0xbdb9e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdb9e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdb9ec: b               #0xbdb984
    // 0xbdb9f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdb9f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0xbdb9f4, size: 0x88
    // 0xbdb9f4: EnterFrame
    //     0xbdb9f4: stp             fp, lr, [SP, #-0x10]!
    //     0xbdb9f8: mov             fp, SP
    // 0xbdb9fc: AllocStack(0x18)
    //     0xbdb9fc: sub             SP, SP, #0x18
    // 0xbdba00: SetupParameters()
    //     0xbdba00: ldr             x0, [fp, #0x20]
    //     0xbdba04: ldur            w1, [x0, #0x17]
    //     0xbdba08: add             x1, x1, HEAP, lsl #32
    // 0xbdba0c: CheckStackOverflow
    //     0xbdba0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdba10: cmp             SP, x16
    //     0xbdba14: b.ls            #0xbdba70
    // 0xbdba18: LoadField: r0 = r1->field_f
    //     0xbdba18: ldur            w0, [x1, #0xf]
    // 0xbdba1c: DecompressPointer r0
    //     0xbdba1c: add             x0, x0, HEAP, lsl #32
    // 0xbdba20: LoadField: r1 = r0->field_b
    //     0xbdba20: ldur            w1, [x0, #0xb]
    // 0xbdba24: DecompressPointer r1
    //     0xbdba24: add             x1, x1, HEAP, lsl #32
    // 0xbdba28: cmp             w1, NULL
    // 0xbdba2c: b.eq            #0xbdba78
    // 0xbdba30: LoadField: r0 = r1->field_27
    //     0xbdba30: ldur            w0, [x1, #0x27]
    // 0xbdba34: DecompressPointer r0
    //     0xbdba34: add             x0, x0, HEAP, lsl #32
    // 0xbdba38: ldr             x16, [fp, #0x18]
    // 0xbdba3c: stp             x16, x0, [SP, #8]
    // 0xbdba40: ldr             x16, [fp, #0x10]
    // 0xbdba44: str             x16, [SP]
    // 0xbdba48: r4 = 0
    //     0xbdba48: movz            x4, #0
    // 0xbdba4c: ldr             x0, [SP, #0x10]
    // 0xbdba50: r16 = UnlinkedCall_0x613b5c
    //     0xbdba50: add             x16, PP, #0x53, lsl #12  ; [pp+0x53b90] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdba54: add             x16, x16, #0xb90
    // 0xbdba58: ldp             x5, lr, [x16]
    // 0xbdba5c: blr             lr
    // 0xbdba60: r0 = Null
    //     0xbdba60: mov             x0, NULL
    // 0xbdba64: LeaveFrame
    //     0xbdba64: mov             SP, fp
    //     0xbdba68: ldp             fp, lr, [SP], #0x10
    // 0xbdba6c: ret
    //     0xbdba6c: ret             
    // 0xbdba70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdba70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdba74: b               #0xbdba18
    // 0xbdba78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdba78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xbdba7c, size: 0x8c
    // 0xbdba7c: EnterFrame
    //     0xbdba7c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdba80: mov             fp, SP
    // 0xbdba84: AllocStack(0x20)
    //     0xbdba84: sub             SP, SP, #0x20
    // 0xbdba88: SetupParameters()
    //     0xbdba88: ldr             x0, [fp, #0x28]
    //     0xbdba8c: ldur            w1, [x0, #0x17]
    //     0xbdba90: add             x1, x1, HEAP, lsl #32
    // 0xbdba94: CheckStackOverflow
    //     0xbdba94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdba98: cmp             SP, x16
    //     0xbdba9c: b.ls            #0xbdbafc
    // 0xbdbaa0: LoadField: r0 = r1->field_f
    //     0xbdbaa0: ldur            w0, [x1, #0xf]
    // 0xbdbaa4: DecompressPointer r0
    //     0xbdbaa4: add             x0, x0, HEAP, lsl #32
    // 0xbdbaa8: LoadField: r1 = r0->field_b
    //     0xbdbaa8: ldur            w1, [x0, #0xb]
    // 0xbdbaac: DecompressPointer r1
    //     0xbdbaac: add             x1, x1, HEAP, lsl #32
    // 0xbdbab0: cmp             w1, NULL
    // 0xbdbab4: b.eq            #0xbdbb04
    // 0xbdbab8: LoadField: r0 = r1->field_23
    //     0xbdbab8: ldur            w0, [x1, #0x23]
    // 0xbdbabc: DecompressPointer r0
    //     0xbdbabc: add             x0, x0, HEAP, lsl #32
    // 0xbdbac0: ldr             x16, [fp, #0x20]
    // 0xbdbac4: stp             x16, x0, [SP, #0x10]
    // 0xbdbac8: ldr             x16, [fp, #0x18]
    // 0xbdbacc: ldr             lr, [fp, #0x10]
    // 0xbdbad0: stp             lr, x16, [SP]
    // 0xbdbad4: r4 = 0
    //     0xbdbad4: movz            x4, #0
    // 0xbdbad8: ldr             x0, [SP, #0x18]
    // 0xbdbadc: r16 = UnlinkedCall_0x613b5c
    //     0xbdbadc: add             x16, PP, #0x53, lsl #12  ; [pp+0x53ba0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdbae0: add             x16, x16, #0xba0
    // 0xbdbae4: ldp             x5, lr, [x16]
    // 0xbdbae8: blr             lr
    // 0xbdbaec: r0 = Null
    //     0xbdbaec: mov             x0, NULL
    // 0xbdbaf0: LeaveFrame
    //     0xbdbaf0: mov             SP, fp
    //     0xbdbaf4: ldp             fp, lr, [SP], #0x10
    // 0xbdbaf8: ret
    //     0xbdbaf8: ret             
    // 0xbdbafc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdbafc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdbb00: b               #0xbdbaa0
    // 0xbdbb04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdbb04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, List<ProductCustomisation>, bool) {
    // ** addr: 0xbdbb08, size: 0x94
    // 0xbdbb08: EnterFrame
    //     0xbdbb08: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbb0c: mov             fp, SP
    // 0xbdbb10: AllocStack(0x28)
    //     0xbdbb10: sub             SP, SP, #0x28
    // 0xbdbb14: SetupParameters()
    //     0xbdbb14: ldr             x0, [fp, #0x30]
    //     0xbdbb18: ldur            w1, [x0, #0x17]
    //     0xbdbb1c: add             x1, x1, HEAP, lsl #32
    // 0xbdbb20: CheckStackOverflow
    //     0xbdbb20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdbb24: cmp             SP, x16
    //     0xbdbb28: b.ls            #0xbdbb90
    // 0xbdbb2c: LoadField: r0 = r1->field_f
    //     0xbdbb2c: ldur            w0, [x1, #0xf]
    // 0xbdbb30: DecompressPointer r0
    //     0xbdbb30: add             x0, x0, HEAP, lsl #32
    // 0xbdbb34: LoadField: r1 = r0->field_b
    //     0xbdbb34: ldur            w1, [x0, #0xb]
    // 0xbdbb38: DecompressPointer r1
    //     0xbdbb38: add             x1, x1, HEAP, lsl #32
    // 0xbdbb3c: cmp             w1, NULL
    // 0xbdbb40: b.eq            #0xbdbb98
    // 0xbdbb44: LoadField: r0 = r1->field_1f
    //     0xbdbb44: ldur            w0, [x1, #0x1f]
    // 0xbdbb48: DecompressPointer r0
    //     0xbdbb48: add             x0, x0, HEAP, lsl #32
    // 0xbdbb4c: ldr             x16, [fp, #0x28]
    // 0xbdbb50: stp             x16, x0, [SP, #0x18]
    // 0xbdbb54: ldr             x16, [fp, #0x20]
    // 0xbdbb58: ldr             lr, [fp, #0x18]
    // 0xbdbb5c: stp             lr, x16, [SP, #8]
    // 0xbdbb60: ldr             x16, [fp, #0x10]
    // 0xbdbb64: str             x16, [SP]
    // 0xbdbb68: r4 = 0
    //     0xbdbb68: movz            x4, #0
    // 0xbdbb6c: ldr             x0, [SP, #0x20]
    // 0xbdbb70: r16 = UnlinkedCall_0x613b5c
    //     0xbdbb70: add             x16, PP, #0x53, lsl #12  ; [pp+0x53bb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdbb74: add             x16, x16, #0xbb0
    // 0xbdbb78: ldp             x5, lr, [x16]
    // 0xbdbb7c: blr             lr
    // 0xbdbb80: r0 = Null
    //     0xbdbb80: mov             x0, NULL
    // 0xbdbb84: LeaveFrame
    //     0xbdbb84: mov             SP, fp
    //     0xbdbb88: ldp             fp, lr, [SP], #0x10
    // 0xbdbb8c: ret
    //     0xbdbb8c: ret             
    // 0xbdbb90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdbb90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdbb94: b               #0xbdbb2c
    // 0xbdbb98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdbb98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0xbdbb9c, size: 0x88
    // 0xbdbb9c: EnterFrame
    //     0xbdbb9c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbba0: mov             fp, SP
    // 0xbdbba4: AllocStack(0x18)
    //     0xbdbba4: sub             SP, SP, #0x18
    // 0xbdbba8: SetupParameters()
    //     0xbdbba8: ldr             x0, [fp, #0x20]
    //     0xbdbbac: ldur            w1, [x0, #0x17]
    //     0xbdbbb0: add             x1, x1, HEAP, lsl #32
    // 0xbdbbb4: CheckStackOverflow
    //     0xbdbbb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdbbb8: cmp             SP, x16
    //     0xbdbbbc: b.ls            #0xbdbc18
    // 0xbdbbc0: LoadField: r0 = r1->field_f
    //     0xbdbbc0: ldur            w0, [x1, #0xf]
    // 0xbdbbc4: DecompressPointer r0
    //     0xbdbbc4: add             x0, x0, HEAP, lsl #32
    // 0xbdbbc8: LoadField: r1 = r0->field_b
    //     0xbdbbc8: ldur            w1, [x0, #0xb]
    // 0xbdbbcc: DecompressPointer r1
    //     0xbdbbcc: add             x1, x1, HEAP, lsl #32
    // 0xbdbbd0: cmp             w1, NULL
    // 0xbdbbd4: b.eq            #0xbdbc20
    // 0xbdbbd8: LoadField: r0 = r1->field_1b
    //     0xbdbbd8: ldur            w0, [x1, #0x1b]
    // 0xbdbbdc: DecompressPointer r0
    //     0xbdbbdc: add             x0, x0, HEAP, lsl #32
    // 0xbdbbe0: ldr             x16, [fp, #0x18]
    // 0xbdbbe4: stp             x16, x0, [SP, #8]
    // 0xbdbbe8: ldr             x16, [fp, #0x10]
    // 0xbdbbec: str             x16, [SP]
    // 0xbdbbf0: r4 = 0
    //     0xbdbbf0: movz            x4, #0
    // 0xbdbbf4: ldr             x0, [SP, #0x10]
    // 0xbdbbf8: r16 = UnlinkedCall_0x613b5c
    //     0xbdbbf8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53bc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdbbfc: add             x16, x16, #0xbc0
    // 0xbdbc00: ldp             x5, lr, [x16]
    // 0xbdbc04: blr             lr
    // 0xbdbc08: r0 = Null
    //     0xbdbc08: mov             x0, NULL
    // 0xbdbc0c: LeaveFrame
    //     0xbdbc0c: mov             SP, fp
    //     0xbdbc10: ldp             fp, lr, [SP], #0x10
    // 0xbdbc14: ret
    //     0xbdbc14: ret             
    // 0xbdbc18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdbc18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdbc1c: b               #0xbdbbc0
    // 0xbdbc20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdbc20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4000, size: 0x4c, field offset: 0xc
//   const constructor, 
class CustomizedView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc808fc, size: 0x24
    // 0xc808fc: EnterFrame
    //     0xc808fc: stp             fp, lr, [SP, #-0x10]!
    //     0xc80900: mov             fp, SP
    // 0xc80904: mov             x0, x1
    // 0xc80908: r1 = <CustomizedView>
    //     0xc80908: add             x1, PP, #0x48, lsl #12  ; [pp+0x48450] TypeArguments: <CustomizedView>
    //     0xc8090c: ldr             x1, [x1, #0x450]
    // 0xc80910: r0 = _CustomizedViewState()
    //     0xc80910: bl              #0xc80920  ; Allocate_CustomizedViewStateStub -> _CustomizedViewState (size=0x14)
    // 0xc80914: LeaveFrame
    //     0xc80914: mov             SP, fp
    //     0xc80918: ldp             fp, lr, [SP], #0x10
    // 0xc8091c: ret
    //     0xc8091c: ret             
  }
}
