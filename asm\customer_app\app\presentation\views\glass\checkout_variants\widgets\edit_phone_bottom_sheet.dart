// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_phone_bottom_sheet.dart

// class id: 1049372, size: 0x8
class :: {
}

// class id: 3359, size: 0x24, field offset: 0x14
class _EditPhoneBottomSheetState extends State<dynamic> {

  late TextEditingController _phoneController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x940b18, size: 0xdc
    // 0x940b18: EnterFrame
    //     0x940b18: stp             fp, lr, [SP, #-0x10]!
    //     0x940b1c: mov             fp, SP
    // 0x940b20: AllocStack(0x20)
    //     0x940b20: sub             SP, SP, #0x20
    // 0x940b24: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r0, fp-0x10 */)
    //     0x940b24: mov             x0, x1
    //     0x940b28: stur            x1, [fp, #-0x10]
    // 0x940b2c: CheckStackOverflow
    //     0x940b2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940b30: cmp             SP, x16
    //     0x940b34: b.ls            #0x940be4
    // 0x940b38: LoadField: r1 = r0->field_b
    //     0x940b38: ldur            w1, [x0, #0xb]
    // 0x940b3c: DecompressPointer r1
    //     0x940b3c: add             x1, x1, HEAP, lsl #32
    // 0x940b40: cmp             w1, NULL
    // 0x940b44: b.eq            #0x940bec
    // 0x940b48: LoadField: r2 = r1->field_b
    //     0x940b48: ldur            w2, [x1, #0xb]
    // 0x940b4c: DecompressPointer r2
    //     0x940b4c: add             x2, x2, HEAP, lsl #32
    // 0x940b50: stur            x2, [fp, #-8]
    // 0x940b54: r1 = <TextEditingValue>
    //     0x940b54: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0x940b58: r0 = TextEditingController()
    //     0x940b58: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x940b5c: stur            x0, [fp, #-0x18]
    // 0x940b60: ldur            x16, [fp, #-8]
    // 0x940b64: str             x16, [SP]
    // 0x940b68: mov             x1, x0
    // 0x940b6c: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0x940b6c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0x940b70: ldr             x4, [x4, #0xc40]
    // 0x940b74: r0 = TextEditingController()
    //     0x940b74: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x940b78: ldur            x0, [fp, #-0x18]
    // 0x940b7c: ldur            x3, [fp, #-0x10]
    // 0x940b80: StoreField: r3->field_13 = r0
    //     0x940b80: stur            w0, [x3, #0x13]
    //     0x940b84: ldurb           w16, [x3, #-1]
    //     0x940b88: ldurb           w17, [x0, #-1]
    //     0x940b8c: and             x16, x17, x16, lsr #2
    //     0x940b90: tst             x16, HEAP, lsr #32
    //     0x940b94: b.eq            #0x940b9c
    //     0x940b98: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x940b9c: LoadField: r0 = r3->field_b
    //     0x940b9c: ldur            w0, [x3, #0xb]
    // 0x940ba0: DecompressPointer r0
    //     0x940ba0: add             x0, x0, HEAP, lsl #32
    // 0x940ba4: cmp             w0, NULL
    // 0x940ba8: b.eq            #0x940bf0
    // 0x940bac: LoadField: r2 = r0->field_b
    //     0x940bac: ldur            w2, [x0, #0xb]
    // 0x940bb0: DecompressPointer r2
    //     0x940bb0: add             x2, x2, HEAP, lsl #32
    // 0x940bb4: LoadField: r0 = r2->field_7
    //     0x940bb4: ldur            w0, [x2, #7]
    // 0x940bb8: cbz             w0, #0x940bd4
    // 0x940bbc: r0 = true
    //     0x940bbc: add             x0, NULL, #0x20  ; true
    // 0x940bc0: StoreField: r3->field_1b = r0
    //     0x940bc0: stur            w0, [x3, #0x1b]
    // 0x940bc4: mov             x1, x3
    // 0x940bc8: r0 = _isValidPhoneNumber()
    //     0x940bc8: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x940bcc: ldur            x1, [fp, #-0x10]
    // 0x940bd0: ArrayStore: r1[0] = r0  ; List_4
    //     0x940bd0: stur            w0, [x1, #0x17]
    // 0x940bd4: r0 = Null
    //     0x940bd4: mov             x0, NULL
    // 0x940bd8: LeaveFrame
    //     0x940bd8: mov             SP, fp
    //     0x940bdc: ldp             fp, lr, [SP], #0x10
    // 0x940be0: ret
    //     0x940be0: ret             
    // 0x940be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x940be4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x940be8: b               #0x940b38
    // 0x940bec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x940bec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x940bf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x940bf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb499e4, size: 0x810
    // 0xb499e4: EnterFrame
    //     0xb499e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb499e8: mov             fp, SP
    // 0xb499ec: AllocStack(0xc0)
    //     0xb499ec: sub             SP, SP, #0xc0
    // 0xb499f0: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb499f0: mov             x0, x1
    //     0xb499f4: stur            x1, [fp, #-8]
    //     0xb499f8: mov             x1, x2
    //     0xb499fc: stur            x2, [fp, #-0x10]
    // 0xb49a00: CheckStackOverflow
    //     0xb49a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb49a04: cmp             SP, x16
    //     0xb49a08: b.ls            #0xb4a1dc
    // 0xb49a0c: r1 = 2
    //     0xb49a0c: movz            x1, #0x2
    // 0xb49a10: r0 = AllocateContext()
    //     0xb49a10: bl              #0x16f6108  ; AllocateContextStub
    // 0xb49a14: mov             x2, x0
    // 0xb49a18: ldur            x0, [fp, #-8]
    // 0xb49a1c: stur            x2, [fp, #-0x18]
    // 0xb49a20: StoreField: r2->field_f = r0
    //     0xb49a20: stur            w0, [x2, #0xf]
    // 0xb49a24: ldur            x1, [fp, #-0x10]
    // 0xb49a28: StoreField: r2->field_13 = r1
    //     0xb49a28: stur            w1, [x2, #0x13]
    // 0xb49a2c: r0 = of()
    //     0xb49a2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49a30: stur            x0, [fp, #-0x20]
    // 0xb49a34: LoadField: r2 = r0->field_87
    //     0xb49a34: ldur            w2, [x0, #0x87]
    // 0xb49a38: DecompressPointer r2
    //     0xb49a38: add             x2, x2, HEAP, lsl #32
    // 0xb49a3c: ldur            x3, [fp, #-0x18]
    // 0xb49a40: stur            x2, [fp, #-0x10]
    // 0xb49a44: LoadField: r1 = r3->field_13
    //     0xb49a44: ldur            w1, [x3, #0x13]
    // 0xb49a48: DecompressPointer r1
    //     0xb49a48: add             x1, x1, HEAP, lsl #32
    // 0xb49a4c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb49a4c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb49a50: r0 = _of()
    //     0xb49a50: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb49a54: LoadField: r1 = r0->field_7
    //     0xb49a54: ldur            w1, [x0, #7]
    // 0xb49a58: DecompressPointer r1
    //     0xb49a58: add             x1, x1, HEAP, lsl #32
    // 0xb49a5c: LoadField: d0 = r1->field_f
    //     0xb49a5c: ldur            d0, [x1, #0xf]
    // 0xb49a60: d1 = 0.280000
    //     0xb49a60: add             x17, PP, #0x53, lsl #12  ; [pp+0x53e50] IMM: double(0.28) from 0x3fd1eb851eb851ec
    //     0xb49a64: ldr             d1, [x17, #0xe50]
    // 0xb49a68: fmul            d2, d0, d1
    // 0xb49a6c: stur            d2, [fp, #-0x70]
    // 0xb49a70: r0 = BoxConstraints()
    //     0xb49a70: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb49a74: stur            x0, [fp, #-0x28]
    // 0xb49a78: StoreField: r0->field_7 = rZR
    //     0xb49a78: stur            xzr, [x0, #7]
    // 0xb49a7c: d0 = inf
    //     0xb49a7c: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb49a80: StoreField: r0->field_f = d0
    //     0xb49a80: stur            d0, [x0, #0xf]
    // 0xb49a84: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb49a84: stur            xzr, [x0, #0x17]
    // 0xb49a88: ldur            d0, [fp, #-0x70]
    // 0xb49a8c: StoreField: r0->field_1f = d0
    //     0xb49a8c: stur            d0, [x0, #0x1f]
    // 0xb49a90: ldur            x2, [fp, #-0x18]
    // 0xb49a94: LoadField: r1 = r2->field_13
    //     0xb49a94: ldur            w1, [x2, #0x13]
    // 0xb49a98: DecompressPointer r1
    //     0xb49a98: add             x1, x1, HEAP, lsl #32
    // 0xb49a9c: r0 = of()
    //     0xb49a9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49aa0: LoadField: r1 = r0->field_87
    //     0xb49aa0: ldur            w1, [x0, #0x87]
    // 0xb49aa4: DecompressPointer r1
    //     0xb49aa4: add             x1, x1, HEAP, lsl #32
    // 0xb49aa8: LoadField: r0 = r1->field_7
    //     0xb49aa8: ldur            w0, [x1, #7]
    // 0xb49aac: DecompressPointer r0
    //     0xb49aac: add             x0, x0, HEAP, lsl #32
    // 0xb49ab0: r16 = Instance_Color
    //     0xb49ab0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb49ab4: r30 = 14.000000
    //     0xb49ab4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb49ab8: ldr             lr, [lr, #0x1d8]
    // 0xb49abc: stp             lr, x16, [SP]
    // 0xb49ac0: mov             x1, x0
    // 0xb49ac4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb49ac4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb49ac8: ldr             x4, [x4, #0x9b8]
    // 0xb49acc: r0 = copyWith()
    //     0xb49acc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb49ad0: stur            x0, [fp, #-0x30]
    // 0xb49ad4: r0 = Text()
    //     0xb49ad4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb49ad8: mov             x1, x0
    // 0xb49adc: r0 = "Edit Phone Number"
    //     0xb49adc: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e58] "Edit Phone Number"
    //     0xb49ae0: ldr             x0, [x0, #0xe58]
    // 0xb49ae4: stur            x1, [fp, #-0x38]
    // 0xb49ae8: StoreField: r1->field_b = r0
    //     0xb49ae8: stur            w0, [x1, #0xb]
    // 0xb49aec: ldur            x0, [fp, #-0x30]
    // 0xb49af0: StoreField: r1->field_13 = r0
    //     0xb49af0: stur            w0, [x1, #0x13]
    // 0xb49af4: r0 = SvgPicture()
    //     0xb49af4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb49af8: mov             x1, x0
    // 0xb49afc: r2 = "assets/images/x.svg"
    //     0xb49afc: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb49b00: ldr             x2, [x2, #0x5e8]
    // 0xb49b04: stur            x0, [fp, #-0x30]
    // 0xb49b08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb49b08: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb49b0c: r0 = SvgPicture.asset()
    //     0xb49b0c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb49b10: r0 = InkWell()
    //     0xb49b10: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb49b14: mov             x3, x0
    // 0xb49b18: ldur            x0, [fp, #-0x30]
    // 0xb49b1c: stur            x3, [fp, #-0x40]
    // 0xb49b20: StoreField: r3->field_b = r0
    //     0xb49b20: stur            w0, [x3, #0xb]
    // 0xb49b24: ldur            x2, [fp, #-0x18]
    // 0xb49b28: r1 = Function '<anonymous closure>':.
    //     0xb49b28: add             x1, PP, #0x56, lsl #12  ; [pp+0x565e8] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb49b2c: ldr             x1, [x1, #0x5e8]
    // 0xb49b30: r0 = AllocateClosure()
    //     0xb49b30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb49b34: mov             x1, x0
    // 0xb49b38: ldur            x0, [fp, #-0x40]
    // 0xb49b3c: StoreField: r0->field_f = r1
    //     0xb49b3c: stur            w1, [x0, #0xf]
    // 0xb49b40: r3 = true
    //     0xb49b40: add             x3, NULL, #0x20  ; true
    // 0xb49b44: StoreField: r0->field_43 = r3
    //     0xb49b44: stur            w3, [x0, #0x43]
    // 0xb49b48: r4 = Instance_BoxShape
    //     0xb49b48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb49b4c: ldr             x4, [x4, #0x80]
    // 0xb49b50: StoreField: r0->field_47 = r4
    //     0xb49b50: stur            w4, [x0, #0x47]
    // 0xb49b54: StoreField: r0->field_6f = r3
    //     0xb49b54: stur            w3, [x0, #0x6f]
    // 0xb49b58: r5 = false
    //     0xb49b58: add             x5, NULL, #0x30  ; false
    // 0xb49b5c: StoreField: r0->field_73 = r5
    //     0xb49b5c: stur            w5, [x0, #0x73]
    // 0xb49b60: StoreField: r0->field_83 = r3
    //     0xb49b60: stur            w3, [x0, #0x83]
    // 0xb49b64: StoreField: r0->field_7b = r5
    //     0xb49b64: stur            w5, [x0, #0x7b]
    // 0xb49b68: r1 = Null
    //     0xb49b68: mov             x1, NULL
    // 0xb49b6c: r2 = 4
    //     0xb49b6c: movz            x2, #0x4
    // 0xb49b70: r0 = AllocateArray()
    //     0xb49b70: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb49b74: mov             x2, x0
    // 0xb49b78: ldur            x0, [fp, #-0x38]
    // 0xb49b7c: stur            x2, [fp, #-0x30]
    // 0xb49b80: StoreField: r2->field_f = r0
    //     0xb49b80: stur            w0, [x2, #0xf]
    // 0xb49b84: ldur            x0, [fp, #-0x40]
    // 0xb49b88: StoreField: r2->field_13 = r0
    //     0xb49b88: stur            w0, [x2, #0x13]
    // 0xb49b8c: r1 = <Widget>
    //     0xb49b8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb49b90: r0 = AllocateGrowableArray()
    //     0xb49b90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb49b94: mov             x1, x0
    // 0xb49b98: ldur            x0, [fp, #-0x30]
    // 0xb49b9c: stur            x1, [fp, #-0x38]
    // 0xb49ba0: StoreField: r1->field_f = r0
    //     0xb49ba0: stur            w0, [x1, #0xf]
    // 0xb49ba4: r2 = 4
    //     0xb49ba4: movz            x2, #0x4
    // 0xb49ba8: StoreField: r1->field_b = r2
    //     0xb49ba8: stur            w2, [x1, #0xb]
    // 0xb49bac: r0 = Row()
    //     0xb49bac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb49bb0: mov             x1, x0
    // 0xb49bb4: r0 = Instance_Axis
    //     0xb49bb4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb49bb8: stur            x1, [fp, #-0x30]
    // 0xb49bbc: StoreField: r1->field_f = r0
    //     0xb49bbc: stur            w0, [x1, #0xf]
    // 0xb49bc0: r0 = Instance_MainAxisAlignment
    //     0xb49bc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb49bc4: ldr             x0, [x0, #0xa8]
    // 0xb49bc8: StoreField: r1->field_13 = r0
    //     0xb49bc8: stur            w0, [x1, #0x13]
    // 0xb49bcc: r0 = Instance_MainAxisSize
    //     0xb49bcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb49bd0: ldr             x0, [x0, #0xa10]
    // 0xb49bd4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb49bd4: stur            w0, [x1, #0x17]
    // 0xb49bd8: r0 = Instance_CrossAxisAlignment
    //     0xb49bd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb49bdc: ldr             x0, [x0, #0xa18]
    // 0xb49be0: StoreField: r1->field_1b = r0
    //     0xb49be0: stur            w0, [x1, #0x1b]
    // 0xb49be4: r2 = Instance_VerticalDirection
    //     0xb49be4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb49be8: ldr             x2, [x2, #0xa20]
    // 0xb49bec: StoreField: r1->field_23 = r2
    //     0xb49bec: stur            w2, [x1, #0x23]
    // 0xb49bf0: r3 = Instance_Clip
    //     0xb49bf0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb49bf4: ldr             x3, [x3, #0x38]
    // 0xb49bf8: StoreField: r1->field_2b = r3
    //     0xb49bf8: stur            w3, [x1, #0x2b]
    // 0xb49bfc: StoreField: r1->field_2f = rZR
    //     0xb49bfc: stur            xzr, [x1, #0x2f]
    // 0xb49c00: ldur            x4, [fp, #-0x38]
    // 0xb49c04: StoreField: r1->field_b = r4
    //     0xb49c04: stur            w4, [x1, #0xb]
    // 0xb49c08: r0 = Padding()
    //     0xb49c08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb49c0c: mov             x1, x0
    // 0xb49c10: r0 = Instance_EdgeInsets
    //     0xb49c10: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e68] Obj!EdgeInsets@d580a1
    //     0xb49c14: ldr             x0, [x0, #0xe68]
    // 0xb49c18: stur            x1, [fp, #-0x40]
    // 0xb49c1c: StoreField: r1->field_f = r0
    //     0xb49c1c: stur            w0, [x1, #0xf]
    // 0xb49c20: ldur            x0, [fp, #-0x30]
    // 0xb49c24: StoreField: r1->field_b = r0
    //     0xb49c24: stur            w0, [x1, #0xb]
    // 0xb49c28: ldur            x0, [fp, #-8]
    // 0xb49c2c: LoadField: r2 = r0->field_1f
    //     0xb49c2c: ldur            w2, [x0, #0x1f]
    // 0xb49c30: DecompressPointer r2
    //     0xb49c30: add             x2, x2, HEAP, lsl #32
    // 0xb49c34: stur            x2, [fp, #-0x38]
    // 0xb49c38: LoadField: r3 = r0->field_b
    //     0xb49c38: ldur            w3, [x0, #0xb]
    // 0xb49c3c: DecompressPointer r3
    //     0xb49c3c: add             x3, x3, HEAP, lsl #32
    // 0xb49c40: cmp             w3, NULL
    // 0xb49c44: b.eq            #0xb4a1e4
    // 0xb49c48: LoadField: r4 = r3->field_b
    //     0xb49c48: ldur            w4, [x3, #0xb]
    // 0xb49c4c: DecompressPointer r4
    //     0xb49c4c: add             x4, x4, HEAP, lsl #32
    // 0xb49c50: LoadField: r3 = r4->field_7
    //     0xb49c50: ldur            w3, [x4, #7]
    // 0xb49c54: cbnz            w3, #0xb49c60
    // 0xb49c58: r4 = false
    //     0xb49c58: add             x4, NULL, #0x30  ; false
    // 0xb49c5c: b               #0xb49c64
    // 0xb49c60: r4 = true
    //     0xb49c60: add             x4, NULL, #0x20  ; true
    // 0xb49c64: stur            x4, [fp, #-0x30]
    // 0xb49c68: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb49c68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb49c6c: ldr             x0, [x0, #0x1530]
    //     0xb49c70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb49c74: cmp             w0, w16
    //     0xb49c78: b.ne            #0xb49c88
    //     0xb49c7c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb49c80: ldr             x2, [x2, #0x120]
    //     0xb49c84: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb49c88: stur            x0, [fp, #-0x48]
    // 0xb49c8c: r0 = LengthLimitingTextInputFormatter()
    //     0xb49c8c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb49c90: mov             x3, x0
    // 0xb49c94: r0 = 20
    //     0xb49c94: movz            x0, #0x14
    // 0xb49c98: stur            x3, [fp, #-0x50]
    // 0xb49c9c: StoreField: r3->field_7 = r0
    //     0xb49c9c: stur            w0, [x3, #7]
    // 0xb49ca0: r1 = Null
    //     0xb49ca0: mov             x1, NULL
    // 0xb49ca4: r2 = 4
    //     0xb49ca4: movz            x2, #0x4
    // 0xb49ca8: r0 = AllocateArray()
    //     0xb49ca8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb49cac: mov             x2, x0
    // 0xb49cb0: ldur            x0, [fp, #-0x48]
    // 0xb49cb4: stur            x2, [fp, #-0x58]
    // 0xb49cb8: StoreField: r2->field_f = r0
    //     0xb49cb8: stur            w0, [x2, #0xf]
    // 0xb49cbc: ldur            x0, [fp, #-0x50]
    // 0xb49cc0: StoreField: r2->field_13 = r0
    //     0xb49cc0: stur            w0, [x2, #0x13]
    // 0xb49cc4: r1 = <TextInputFormatter>
    //     0xb49cc4: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb49cc8: ldr             x1, [x1, #0x7b0]
    // 0xb49ccc: r0 = AllocateGrowableArray()
    //     0xb49ccc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb49cd0: mov             x2, x0
    // 0xb49cd4: ldur            x0, [fp, #-0x58]
    // 0xb49cd8: stur            x2, [fp, #-0x48]
    // 0xb49cdc: StoreField: r2->field_f = r0
    //     0xb49cdc: stur            w0, [x2, #0xf]
    // 0xb49ce0: r0 = 4
    //     0xb49ce0: movz            x0, #0x4
    // 0xb49ce4: StoreField: r2->field_b = r0
    //     0xb49ce4: stur            w0, [x2, #0xb]
    // 0xb49ce8: ldur            x0, [fp, #-0x10]
    // 0xb49cec: LoadField: r1 = r0->field_2b
    //     0xb49cec: ldur            w1, [x0, #0x2b]
    // 0xb49cf0: DecompressPointer r1
    //     0xb49cf0: add             x1, x1, HEAP, lsl #32
    // 0xb49cf4: ldur            x0, [fp, #-0x20]
    // 0xb49cf8: LoadField: r3 = r0->field_5b
    //     0xb49cf8: ldur            w3, [x0, #0x5b]
    // 0xb49cfc: DecompressPointer r3
    //     0xb49cfc: add             x3, x3, HEAP, lsl #32
    // 0xb49d00: r16 = 14.000000
    //     0xb49d00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb49d04: ldr             x16, [x16, #0x1d8]
    // 0xb49d08: stp             x3, x16, [SP]
    // 0xb49d0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb49d0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb49d10: ldr             x4, [x4, #0xaa0]
    // 0xb49d14: r0 = copyWith()
    //     0xb49d14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb49d18: mov             x3, x0
    // 0xb49d1c: ldur            x0, [fp, #-8]
    // 0xb49d20: stur            x3, [fp, #-0x20]
    // 0xb49d24: LoadField: r4 = r0->field_13
    //     0xb49d24: ldur            w4, [x0, #0x13]
    // 0xb49d28: DecompressPointer r4
    //     0xb49d28: add             x4, x4, HEAP, lsl #32
    // 0xb49d2c: r16 = Sentinel
    //     0xb49d2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb49d30: cmp             w4, w16
    // 0xb49d34: b.eq            #0xb4a1e8
    // 0xb49d38: ldur            x5, [fp, #-0x18]
    // 0xb49d3c: stur            x4, [fp, #-0x10]
    // 0xb49d40: LoadField: r2 = r5->field_13
    //     0xb49d40: ldur            w2, [x5, #0x13]
    // 0xb49d44: DecompressPointer r2
    //     0xb49d44: add             x2, x2, HEAP, lsl #32
    // 0xb49d48: mov             x1, x0
    // 0xb49d4c: r0 = _buildInputDecoration()
    //     0xb49d4c: bl              #0xb4a1f4  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_buildInputDecoration
    // 0xb49d50: ldur            x2, [fp, #-8]
    // 0xb49d54: r1 = Function '_validatePhoneNumber@1556118804':.
    //     0xb49d54: add             x1, PP, #0x56, lsl #12  ; [pp+0x565f0] AnonymousClosure: (0xb4a528), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber (0xa08f3c)
    //     0xb49d58: ldr             x1, [x1, #0x5f0]
    // 0xb49d5c: stur            x0, [fp, #-0x50]
    // 0xb49d60: r0 = AllocateClosure()
    //     0xb49d60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb49d64: ldur            x2, [fp, #-8]
    // 0xb49d68: r1 = Function '_handlePhoneNumberChanged@1556118804':.
    //     0xb49d68: add             x1, PP, #0x56, lsl #12  ; [pp+0x565f8] AnonymousClosure: (0xb4a454), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_handlePhoneNumberChanged (0xb4a490)
    //     0xb49d6c: ldr             x1, [x1, #0x5f8]
    // 0xb49d70: stur            x0, [fp, #-0x58]
    // 0xb49d74: r0 = AllocateClosure()
    //     0xb49d74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb49d78: r1 = <String>
    //     0xb49d78: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb49d7c: stur            x0, [fp, #-0x60]
    // 0xb49d80: r0 = TextFormField()
    //     0xb49d80: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb49d84: stur            x0, [fp, #-0x68]
    // 0xb49d88: ldur            x16, [fp, #-0x58]
    // 0xb49d8c: r30 = true
    //     0xb49d8c: add             lr, NULL, #0x20  ; true
    // 0xb49d90: stp             lr, x16, [SP, #0x40]
    // 0xb49d94: ldur            x16, [fp, #-0x30]
    // 0xb49d98: r30 = Instance_AutovalidateMode
    //     0xb49d98: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb49d9c: ldr             lr, [lr, #0x7e8]
    // 0xb49da0: stp             lr, x16, [SP, #0x30]
    // 0xb49da4: ldur            x16, [fp, #-0x48]
    // 0xb49da8: r30 = Instance_TextInputType
    //     0xb49da8: add             lr, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb49dac: ldr             lr, [lr, #0x1a0]
    // 0xb49db0: stp             lr, x16, [SP, #0x20]
    // 0xb49db4: ldur            x16, [fp, #-0x20]
    // 0xb49db8: r30 = 2
    //     0xb49db8: movz            lr, #0x2
    // 0xb49dbc: stp             lr, x16, [SP, #0x10]
    // 0xb49dc0: ldur            x16, [fp, #-0x10]
    // 0xb49dc4: ldur            lr, [fp, #-0x60]
    // 0xb49dc8: stp             lr, x16, [SP]
    // 0xb49dcc: mov             x1, x0
    // 0xb49dd0: ldur            x2, [fp, #-0x50]
    // 0xb49dd4: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, onChanged, 0xb, style, 0x8, validator, 0x2, null]
    //     0xb49dd4: add             x4, PP, #0x53, lsl #12  ; [pp+0x53e80] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "onChanged", 0xb, "style", 0x8, "validator", 0x2, Null]
    //     0xb49dd8: ldr             x4, [x4, #0xe80]
    // 0xb49ddc: r0 = TextFormField()
    //     0xb49ddc: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb49de0: r0 = Form()
    //     0xb49de0: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb49de4: mov             x1, x0
    // 0xb49de8: ldur            x0, [fp, #-0x68]
    // 0xb49dec: stur            x1, [fp, #-0x10]
    // 0xb49df0: StoreField: r1->field_b = r0
    //     0xb49df0: stur            w0, [x1, #0xb]
    // 0xb49df4: r0 = Instance_AutovalidateMode
    //     0xb49df4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb49df8: ldr             x0, [x0, #0x800]
    // 0xb49dfc: StoreField: r1->field_23 = r0
    //     0xb49dfc: stur            w0, [x1, #0x23]
    // 0xb49e00: ldur            x0, [fp, #-0x38]
    // 0xb49e04: StoreField: r1->field_7 = r0
    //     0xb49e04: stur            w0, [x1, #7]
    // 0xb49e08: r0 = Padding()
    //     0xb49e08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb49e0c: mov             x2, x0
    // 0xb49e10: r0 = Instance_EdgeInsets
    //     0xb49e10: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e88] Obj!EdgeInsets@d57231
    //     0xb49e14: ldr             x0, [x0, #0xe88]
    // 0xb49e18: stur            x2, [fp, #-0x20]
    // 0xb49e1c: StoreField: r2->field_f = r0
    //     0xb49e1c: stur            w0, [x2, #0xf]
    // 0xb49e20: ldur            x0, [fp, #-0x10]
    // 0xb49e24: StoreField: r2->field_b = r0
    //     0xb49e24: stur            w0, [x2, #0xb]
    // 0xb49e28: r1 = <FlexParentData>
    //     0xb49e28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb49e2c: ldr             x1, [x1, #0xe00]
    // 0xb49e30: r0 = Expanded()
    //     0xb49e30: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb49e34: mov             x2, x0
    // 0xb49e38: r0 = 1
    //     0xb49e38: movz            x0, #0x1
    // 0xb49e3c: stur            x2, [fp, #-0x10]
    // 0xb49e40: StoreField: r2->field_13 = r0
    //     0xb49e40: stur            x0, [x2, #0x13]
    // 0xb49e44: r0 = Instance_FlexFit
    //     0xb49e44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb49e48: ldr             x0, [x0, #0xe08]
    // 0xb49e4c: StoreField: r2->field_1b = r0
    //     0xb49e4c: stur            w0, [x2, #0x1b]
    // 0xb49e50: ldur            x0, [fp, #-0x20]
    // 0xb49e54: StoreField: r2->field_b = r0
    //     0xb49e54: stur            w0, [x2, #0xb]
    // 0xb49e58: ldur            x0, [fp, #-0x18]
    // 0xb49e5c: LoadField: r1 = r0->field_13
    //     0xb49e5c: ldur            w1, [x0, #0x13]
    // 0xb49e60: DecompressPointer r1
    //     0xb49e60: add             x1, x1, HEAP, lsl #32
    // 0xb49e64: r0 = of()
    //     0xb49e64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49e68: LoadField: r1 = r0->field_5b
    //     0xb49e68: ldur            w1, [x0, #0x5b]
    // 0xb49e6c: DecompressPointer r1
    //     0xb49e6c: add             x1, x1, HEAP, lsl #32
    // 0xb49e70: r0 = LoadClassIdInstr(r1)
    //     0xb49e70: ldur            x0, [x1, #-1]
    //     0xb49e74: ubfx            x0, x0, #0xc, #0x14
    // 0xb49e78: r2 = 40
    //     0xb49e78: movz            x2, #0x28
    // 0xb49e7c: r0 = GDT[cid_x0 + -0xfe7]()
    //     0xb49e7c: sub             lr, x0, #0xfe7
    //     0xb49e80: ldr             lr, [x21, lr, lsl #3]
    //     0xb49e84: blr             lr
    // 0xb49e88: stur            x0, [fp, #-0x20]
    // 0xb49e8c: r0 = BorderSide()
    //     0xb49e8c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb49e90: mov             x1, x0
    // 0xb49e94: ldur            x0, [fp, #-0x20]
    // 0xb49e98: stur            x1, [fp, #-0x30]
    // 0xb49e9c: StoreField: r1->field_7 = r0
    //     0xb49e9c: stur            w0, [x1, #7]
    // 0xb49ea0: d0 = 2.000000
    //     0xb49ea0: fmov            d0, #2.00000000
    // 0xb49ea4: StoreField: r1->field_b = d0
    //     0xb49ea4: stur            d0, [x1, #0xb]
    // 0xb49ea8: r0 = Instance_BorderStyle
    //     0xb49ea8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb49eac: ldr             x0, [x0, #0xf68]
    // 0xb49eb0: StoreField: r1->field_13 = r0
    //     0xb49eb0: stur            w0, [x1, #0x13]
    // 0xb49eb4: d0 = -1.000000
    //     0xb49eb4: fmov            d0, #-1.00000000
    // 0xb49eb8: ArrayStore: r1[0] = d0  ; List_8
    //     0xb49eb8: stur            d0, [x1, #0x17]
    // 0xb49ebc: r0 = Border()
    //     0xb49ebc: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0xb49ec0: mov             x1, x0
    // 0xb49ec4: ldur            x0, [fp, #-0x30]
    // 0xb49ec8: stur            x1, [fp, #-0x20]
    // 0xb49ecc: StoreField: r1->field_7 = r0
    //     0xb49ecc: stur            w0, [x1, #7]
    // 0xb49ed0: r0 = Instance_BorderSide
    //     0xb49ed0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb49ed4: ldr             x0, [x0, #0xe20]
    // 0xb49ed8: StoreField: r1->field_b = r0
    //     0xb49ed8: stur            w0, [x1, #0xb]
    // 0xb49edc: StoreField: r1->field_f = r0
    //     0xb49edc: stur            w0, [x1, #0xf]
    // 0xb49ee0: StoreField: r1->field_13 = r0
    //     0xb49ee0: stur            w0, [x1, #0x13]
    // 0xb49ee4: r0 = BoxDecoration()
    //     0xb49ee4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb49ee8: mov             x2, x0
    // 0xb49eec: ldur            x0, [fp, #-0x20]
    // 0xb49ef0: stur            x2, [fp, #-0x30]
    // 0xb49ef4: StoreField: r2->field_f = r0
    //     0xb49ef4: stur            w0, [x2, #0xf]
    // 0xb49ef8: r0 = Instance_BoxShape
    //     0xb49ef8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb49efc: ldr             x0, [x0, #0x80]
    // 0xb49f00: StoreField: r2->field_23 = r0
    //     0xb49f00: stur            w0, [x2, #0x23]
    // 0xb49f04: ldur            x0, [fp, #-8]
    // 0xb49f08: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb49f08: ldur            w1, [x0, #0x17]
    // 0xb49f0c: DecompressPointer r1
    //     0xb49f0c: add             x1, x1, HEAP, lsl #32
    // 0xb49f10: tbnz            w1, #4, #0xb49f30
    // 0xb49f14: ldur            x3, [fp, #-0x18]
    // 0xb49f18: LoadField: r1 = r3->field_13
    //     0xb49f18: ldur            w1, [x3, #0x13]
    // 0xb49f1c: DecompressPointer r1
    //     0xb49f1c: add             x1, x1, HEAP, lsl #32
    // 0xb49f20: r0 = of()
    //     0xb49f20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49f24: LoadField: r1 = r0->field_5b
    //     0xb49f24: ldur            w1, [x0, #0x5b]
    // 0xb49f28: DecompressPointer r1
    //     0xb49f28: add             x1, x1, HEAP, lsl #32
    // 0xb49f2c: b               #0xb49f38
    // 0xb49f30: r1 = Instance_MaterialColor
    //     0xb49f30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb49f34: ldr             x1, [x1, #0xdc0]
    // 0xb49f38: ldur            x0, [fp, #-8]
    // 0xb49f3c: r16 = <Color>
    //     0xb49f3c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb49f40: ldr             x16, [x16, #0xf80]
    // 0xb49f44: stp             x1, x16, [SP]
    // 0xb49f48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb49f48: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb49f4c: r0 = all()
    //     0xb49f4c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb49f50: stur            x0, [fp, #-0x20]
    // 0xb49f54: r16 = <RoundedRectangleBorder>
    //     0xb49f54: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb49f58: ldr             x16, [x16, #0xf78]
    // 0xb49f5c: r30 = Instance_RoundedRectangleBorder
    //     0xb49f5c: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb49f60: ldr             lr, [lr, #0x888]
    // 0xb49f64: stp             lr, x16, [SP]
    // 0xb49f68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb49f68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb49f6c: r0 = all()
    //     0xb49f6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb49f70: stur            x0, [fp, #-0x38]
    // 0xb49f74: r0 = ButtonStyle()
    //     0xb49f74: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb49f78: mov             x1, x0
    // 0xb49f7c: ldur            x0, [fp, #-0x20]
    // 0xb49f80: stur            x1, [fp, #-0x48]
    // 0xb49f84: StoreField: r1->field_b = r0
    //     0xb49f84: stur            w0, [x1, #0xb]
    // 0xb49f88: ldur            x0, [fp, #-0x38]
    // 0xb49f8c: StoreField: r1->field_43 = r0
    //     0xb49f8c: stur            w0, [x1, #0x43]
    // 0xb49f90: r0 = TextButtonThemeData()
    //     0xb49f90: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb49f94: mov             x3, x0
    // 0xb49f98: ldur            x0, [fp, #-0x48]
    // 0xb49f9c: stur            x3, [fp, #-0x20]
    // 0xb49fa0: StoreField: r3->field_7 = r0
    //     0xb49fa0: stur            w0, [x3, #7]
    // 0xb49fa4: ldur            x0, [fp, #-8]
    // 0xb49fa8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb49fa8: ldur            w1, [x0, #0x17]
    // 0xb49fac: DecompressPointer r1
    //     0xb49fac: add             x1, x1, HEAP, lsl #32
    // 0xb49fb0: tbnz            w1, #4, #0xb49fcc
    // 0xb49fb4: ldur            x2, [fp, #-0x18]
    // 0xb49fb8: r1 = Function '<anonymous closure>':.
    //     0xb49fb8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56600] AnonymousClosure: (0xb4a348), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::build (0xb499e4)
    //     0xb49fbc: ldr             x1, [x1, #0x600]
    // 0xb49fc0: r0 = AllocateClosure()
    //     0xb49fc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb49fc4: mov             x4, x0
    // 0xb49fc8: b               #0xb49fd0
    // 0xb49fcc: r4 = Null
    //     0xb49fcc: mov             x4, NULL
    // 0xb49fd0: ldur            x1, [fp, #-0x18]
    // 0xb49fd4: ldur            x3, [fp, #-0x40]
    // 0xb49fd8: ldur            x2, [fp, #-0x10]
    // 0xb49fdc: ldur            x0, [fp, #-0x20]
    // 0xb49fe0: stur            x4, [fp, #-8]
    // 0xb49fe4: LoadField: r5 = r1->field_13
    //     0xb49fe4: ldur            w5, [x1, #0x13]
    // 0xb49fe8: DecompressPointer r5
    //     0xb49fe8: add             x5, x5, HEAP, lsl #32
    // 0xb49fec: mov             x1, x5
    // 0xb49ff0: r0 = of()
    //     0xb49ff0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb49ff4: LoadField: r1 = r0->field_87
    //     0xb49ff4: ldur            w1, [x0, #0x87]
    // 0xb49ff8: DecompressPointer r1
    //     0xb49ff8: add             x1, x1, HEAP, lsl #32
    // 0xb49ffc: LoadField: r0 = r1->field_7
    //     0xb49ffc: ldur            w0, [x1, #7]
    // 0xb4a000: DecompressPointer r0
    //     0xb4a000: add             x0, x0, HEAP, lsl #32
    // 0xb4a004: r16 = 14.000000
    //     0xb4a004: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4a008: ldr             x16, [x16, #0x1d8]
    // 0xb4a00c: r30 = Instance_Color
    //     0xb4a00c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4a010: stp             lr, x16, [SP]
    // 0xb4a014: mov             x1, x0
    // 0xb4a018: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4a018: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4a01c: ldr             x4, [x4, #0xaa0]
    // 0xb4a020: r0 = copyWith()
    //     0xb4a020: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4a024: stur            x0, [fp, #-0x18]
    // 0xb4a028: r0 = Text()
    //     0xb4a028: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4a02c: mov             x1, x0
    // 0xb4a030: r0 = "Confirm"
    //     0xb4a030: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cec0] "Confirm"
    //     0xb4a034: ldr             x0, [x0, #0xec0]
    // 0xb4a038: stur            x1, [fp, #-0x38]
    // 0xb4a03c: StoreField: r1->field_b = r0
    //     0xb4a03c: stur            w0, [x1, #0xb]
    // 0xb4a040: ldur            x0, [fp, #-0x18]
    // 0xb4a044: StoreField: r1->field_13 = r0
    //     0xb4a044: stur            w0, [x1, #0x13]
    // 0xb4a048: r0 = TextButton()
    //     0xb4a048: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb4a04c: mov             x1, x0
    // 0xb4a050: ldur            x0, [fp, #-8]
    // 0xb4a054: stur            x1, [fp, #-0x18]
    // 0xb4a058: StoreField: r1->field_b = r0
    //     0xb4a058: stur            w0, [x1, #0xb]
    // 0xb4a05c: r0 = false
    //     0xb4a05c: add             x0, NULL, #0x30  ; false
    // 0xb4a060: StoreField: r1->field_27 = r0
    //     0xb4a060: stur            w0, [x1, #0x27]
    // 0xb4a064: r2 = true
    //     0xb4a064: add             x2, NULL, #0x20  ; true
    // 0xb4a068: StoreField: r1->field_2f = r2
    //     0xb4a068: stur            w2, [x1, #0x2f]
    // 0xb4a06c: ldur            x3, [fp, #-0x38]
    // 0xb4a070: StoreField: r1->field_37 = r3
    //     0xb4a070: stur            w3, [x1, #0x37]
    // 0xb4a074: r0 = TextButtonTheme()
    //     0xb4a074: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb4a078: mov             x1, x0
    // 0xb4a07c: ldur            x0, [fp, #-0x20]
    // 0xb4a080: stur            x1, [fp, #-8]
    // 0xb4a084: StoreField: r1->field_f = r0
    //     0xb4a084: stur            w0, [x1, #0xf]
    // 0xb4a088: ldur            x0, [fp, #-0x18]
    // 0xb4a08c: StoreField: r1->field_b = r0
    //     0xb4a08c: stur            w0, [x1, #0xb]
    // 0xb4a090: r0 = Container()
    //     0xb4a090: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4a094: stur            x0, [fp, #-0x18]
    // 0xb4a098: r16 = Instance_EdgeInsets
    //     0xb4a098: add             x16, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xb4a09c: ldr             x16, [x16, #0x50]
    // 0xb4a0a0: r30 = inf
    //     0xb4a0a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb4a0a4: ldr             lr, [lr, #0x9f8]
    // 0xb4a0a8: stp             lr, x16, [SP, #0x10]
    // 0xb4a0ac: ldur            x16, [fp, #-0x30]
    // 0xb4a0b0: ldur            lr, [fp, #-8]
    // 0xb4a0b4: stp             lr, x16, [SP]
    // 0xb4a0b8: mov             x1, x0
    // 0xb4a0bc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0xb4a0bc: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0xb4a0c0: ldr             x4, [x4, #0x18]
    // 0xb4a0c4: r0 = Container()
    //     0xb4a0c4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4a0c8: r1 = Null
    //     0xb4a0c8: mov             x1, NULL
    // 0xb4a0cc: r2 = 8
    //     0xb4a0cc: movz            x2, #0x8
    // 0xb4a0d0: r0 = AllocateArray()
    //     0xb4a0d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4a0d4: mov             x2, x0
    // 0xb4a0d8: ldur            x0, [fp, #-0x40]
    // 0xb4a0dc: stur            x2, [fp, #-8]
    // 0xb4a0e0: StoreField: r2->field_f = r0
    //     0xb4a0e0: stur            w0, [x2, #0xf]
    // 0xb4a0e4: ldur            x0, [fp, #-0x10]
    // 0xb4a0e8: StoreField: r2->field_13 = r0
    //     0xb4a0e8: stur            w0, [x2, #0x13]
    // 0xb4a0ec: r16 = Instance_SizedBox
    //     0xb4a0ec: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb4a0f0: ldr             x16, [x16, #0x578]
    // 0xb4a0f4: ArrayStore: r2[0] = r16  ; List_4
    //     0xb4a0f4: stur            w16, [x2, #0x17]
    // 0xb4a0f8: ldur            x0, [fp, #-0x18]
    // 0xb4a0fc: StoreField: r2->field_1b = r0
    //     0xb4a0fc: stur            w0, [x2, #0x1b]
    // 0xb4a100: r1 = <Widget>
    //     0xb4a100: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4a104: r0 = AllocateGrowableArray()
    //     0xb4a104: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4a108: mov             x1, x0
    // 0xb4a10c: ldur            x0, [fp, #-8]
    // 0xb4a110: stur            x1, [fp, #-0x10]
    // 0xb4a114: StoreField: r1->field_f = r0
    //     0xb4a114: stur            w0, [x1, #0xf]
    // 0xb4a118: r0 = 8
    //     0xb4a118: movz            x0, #0x8
    // 0xb4a11c: StoreField: r1->field_b = r0
    //     0xb4a11c: stur            w0, [x1, #0xb]
    // 0xb4a120: r0 = Column()
    //     0xb4a120: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4a124: mov             x1, x0
    // 0xb4a128: r0 = Instance_Axis
    //     0xb4a128: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4a12c: stur            x1, [fp, #-8]
    // 0xb4a130: StoreField: r1->field_f = r0
    //     0xb4a130: stur            w0, [x1, #0xf]
    // 0xb4a134: r0 = Instance_MainAxisAlignment
    //     0xb4a134: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4a138: ldr             x0, [x0, #0xa08]
    // 0xb4a13c: StoreField: r1->field_13 = r0
    //     0xb4a13c: stur            w0, [x1, #0x13]
    // 0xb4a140: r0 = Instance_MainAxisSize
    //     0xb4a140: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb4a144: ldr             x0, [x0, #0xdd0]
    // 0xb4a148: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4a148: stur            w0, [x1, #0x17]
    // 0xb4a14c: r0 = Instance_CrossAxisAlignment
    //     0xb4a14c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4a150: ldr             x0, [x0, #0xa18]
    // 0xb4a154: StoreField: r1->field_1b = r0
    //     0xb4a154: stur            w0, [x1, #0x1b]
    // 0xb4a158: r0 = Instance_VerticalDirection
    //     0xb4a158: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4a15c: ldr             x0, [x0, #0xa20]
    // 0xb4a160: StoreField: r1->field_23 = r0
    //     0xb4a160: stur            w0, [x1, #0x23]
    // 0xb4a164: r0 = Instance_Clip
    //     0xb4a164: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4a168: ldr             x0, [x0, #0x38]
    // 0xb4a16c: StoreField: r1->field_2b = r0
    //     0xb4a16c: stur            w0, [x1, #0x2b]
    // 0xb4a170: StoreField: r1->field_2f = rZR
    //     0xb4a170: stur            xzr, [x1, #0x2f]
    // 0xb4a174: ldur            x0, [fp, #-0x10]
    // 0xb4a178: StoreField: r1->field_b = r0
    //     0xb4a178: stur            w0, [x1, #0xb]
    // 0xb4a17c: r0 = Container()
    //     0xb4a17c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4a180: stur            x0, [fp, #-0x10]
    // 0xb4a184: ldur            x16, [fp, #-0x28]
    // 0xb4a188: ldur            lr, [fp, #-8]
    // 0xb4a18c: stp             lr, x16, [SP]
    // 0xb4a190: mov             x1, x0
    // 0xb4a194: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, constraints, 0x1, null]
    //     0xb4a194: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b30] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "constraints", 0x1, Null]
    //     0xb4a198: ldr             x4, [x4, #0xb30]
    // 0xb4a19c: r0 = Container()
    //     0xb4a19c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4a1a0: r0 = SafeArea()
    //     0xb4a1a0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb4a1a4: r1 = true
    //     0xb4a1a4: add             x1, NULL, #0x20  ; true
    // 0xb4a1a8: StoreField: r0->field_b = r1
    //     0xb4a1a8: stur            w1, [x0, #0xb]
    // 0xb4a1ac: StoreField: r0->field_f = r1
    //     0xb4a1ac: stur            w1, [x0, #0xf]
    // 0xb4a1b0: StoreField: r0->field_13 = r1
    //     0xb4a1b0: stur            w1, [x0, #0x13]
    // 0xb4a1b4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb4a1b4: stur            w1, [x0, #0x17]
    // 0xb4a1b8: r1 = Instance_EdgeInsets
    //     0xb4a1b8: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb4a1bc: StoreField: r0->field_1b = r1
    //     0xb4a1bc: stur            w1, [x0, #0x1b]
    // 0xb4a1c0: r1 = false
    //     0xb4a1c0: add             x1, NULL, #0x30  ; false
    // 0xb4a1c4: StoreField: r0->field_1f = r1
    //     0xb4a1c4: stur            w1, [x0, #0x1f]
    // 0xb4a1c8: ldur            x1, [fp, #-0x10]
    // 0xb4a1cc: StoreField: r0->field_23 = r1
    //     0xb4a1cc: stur            w1, [x0, #0x23]
    // 0xb4a1d0: LeaveFrame
    //     0xb4a1d0: mov             SP, fp
    //     0xb4a1d4: ldp             fp, lr, [SP], #0x10
    // 0xb4a1d8: ret
    //     0xb4a1d8: ret             
    // 0xb4a1dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a1dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a1e0: b               #0xb49a0c
    // 0xb4a1e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4a1e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4a1e8: r9 = _phoneController
    //     0xb4a1e8: add             x9, PP, #0x56, lsl #12  ; [pp+0x56608] Field <_EditPhoneBottomSheetState@1556118804._phoneController@1556118804>: late (offset: 0x14)
    //     0xb4a1ec: ldr             x9, [x9, #0x608]
    // 0xb4a1f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb4a1f0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildInputDecoration(/* No info */) {
    // ** addr: 0xb4a1f4, size: 0x154
    // 0xb4a1f4: EnterFrame
    //     0xb4a1f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a1f8: mov             fp, SP
    // 0xb4a1fc: AllocStack(0x50)
    //     0xb4a1fc: sub             SP, SP, #0x50
    // 0xb4a200: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb4a200: mov             x0, x2
    //     0xb4a204: stur            x2, [fp, #-0x10]
    //     0xb4a208: mov             x2, x1
    //     0xb4a20c: stur            x1, [fp, #-8]
    // 0xb4a210: CheckStackOverflow
    //     0xb4a210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a214: cmp             SP, x16
    //     0xb4a218: b.ls            #0xb4a340
    // 0xb4a21c: mov             x1, x0
    // 0xb4a220: r0 = of()
    //     0xb4a220: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4a224: ldur            x1, [fp, #-0x10]
    // 0xb4a228: stur            x0, [fp, #-0x10]
    // 0xb4a22c: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb4a22c: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb4a230: ldur            x1, [fp, #-8]
    // 0xb4a234: ldur            x2, [fp, #-0x10]
    // 0xb4a238: stur            x0, [fp, #-0x18]
    // 0xb4a23c: r0 = _buildPrefixIcon()
    //     0xb4a23c: bl              #0xa08aec  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_buildPrefixIcon
    // 0xb4a240: mov             x1, x0
    // 0xb4a244: ldur            x0, [fp, #-8]
    // 0xb4a248: stur            x1, [fp, #-0x28]
    // 0xb4a24c: LoadField: r2 = r0->field_1b
    //     0xb4a24c: ldur            w2, [x0, #0x1b]
    // 0xb4a250: DecompressPointer r2
    //     0xb4a250: add             x2, x2, HEAP, lsl #32
    // 0xb4a254: tbnz            w2, #4, #0xb4a2b8
    // 0xb4a258: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb4a258: ldur            w2, [x0, #0x17]
    // 0xb4a25c: DecompressPointer r2
    //     0xb4a25c: add             x2, x2, HEAP, lsl #32
    // 0xb4a260: tbnz            w2, #4, #0xb4a270
    // 0xb4a264: r0 = Instance_IconData
    //     0xb4a264: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb4a268: ldr             x0, [x0, #0x130]
    // 0xb4a26c: b               #0xb4a278
    // 0xb4a270: r0 = Instance_IconData
    //     0xb4a270: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb4a274: ldr             x0, [x0, #0x138]
    // 0xb4a278: stur            x0, [fp, #-0x20]
    // 0xb4a27c: tbnz            w2, #4, #0xb4a28c
    // 0xb4a280: r2 = Instance_Color
    //     0xb4a280: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4a284: ldr             x2, [x2, #0x858]
    // 0xb4a288: b               #0xb4a294
    // 0xb4a28c: r2 = Instance_Color
    //     0xb4a28c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb4a290: ldr             x2, [x2, #0x50]
    // 0xb4a294: stur            x2, [fp, #-8]
    // 0xb4a298: r0 = Icon()
    //     0xb4a298: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb4a29c: mov             x1, x0
    // 0xb4a2a0: ldur            x0, [fp, #-0x20]
    // 0xb4a2a4: StoreField: r1->field_b = r0
    //     0xb4a2a4: stur            w0, [x1, #0xb]
    // 0xb4a2a8: ldur            x0, [fp, #-8]
    // 0xb4a2ac: StoreField: r1->field_23 = r0
    //     0xb4a2ac: stur            w0, [x1, #0x23]
    // 0xb4a2b0: mov             x2, x1
    // 0xb4a2b4: b               #0xb4a2bc
    // 0xb4a2b8: r2 = Null
    //     0xb4a2b8: mov             x2, NULL
    // 0xb4a2bc: ldur            x0, [fp, #-0x10]
    // 0xb4a2c0: stur            x2, [fp, #-0x20]
    // 0xb4a2c4: LoadField: r1 = r0->field_87
    //     0xb4a2c4: ldur            w1, [x0, #0x87]
    // 0xb4a2c8: DecompressPointer r1
    //     0xb4a2c8: add             x1, x1, HEAP, lsl #32
    // 0xb4a2cc: LoadField: r0 = r1->field_2b
    //     0xb4a2cc: ldur            w0, [x1, #0x2b]
    // 0xb4a2d0: DecompressPointer r0
    //     0xb4a2d0: add             x0, x0, HEAP, lsl #32
    // 0xb4a2d4: stur            x0, [fp, #-8]
    // 0xb4a2d8: r1 = Instance_Color
    //     0xb4a2d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4a2dc: d0 = 0.400000
    //     0xb4a2dc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4a2e0: r0 = withOpacity()
    //     0xb4a2e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4a2e4: r16 = 14.000000
    //     0xb4a2e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4a2e8: ldr             x16, [x16, #0x1d8]
    // 0xb4a2ec: stp             x0, x16, [SP]
    // 0xb4a2f0: ldur            x1, [fp, #-8]
    // 0xb4a2f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4a2f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4a2f8: ldr             x4, [x4, #0xaa0]
    // 0xb4a2fc: r0 = copyWith()
    //     0xb4a2fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4a300: ldur            x16, [fp, #-0x28]
    // 0xb4a304: ldur            lr, [fp, #-0x20]
    // 0xb4a308: stp             lr, x16, [SP, #0x18]
    // 0xb4a30c: r16 = Instance_EdgeInsets
    //     0xb4a30c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb4a310: ldr             x16, [x16, #0xc40]
    // 0xb4a314: r30 = "Enter WhatsApp no."
    //     0xb4a314: add             lr, PP, #0x53, lsl #12  ; [pp+0x53ec8] "Enter WhatsApp no."
    //     0xb4a318: ldr             lr, [lr, #0xec8]
    // 0xb4a31c: stp             lr, x16, [SP, #8]
    // 0xb4a320: str             x0, [SP]
    // 0xb4a324: ldur            x1, [fp, #-0x18]
    // 0xb4a328: r4 = const [0, 0x6, 0x5, 0x1, contentPadding, 0x3, hintStyle, 0x5, hintText, 0x4, prefixIcon, 0x1, suffixIcon, 0x2, null]
    //     0xb4a328: add             x4, PP, #0x56, lsl #12  ; [pp+0x56618] List(15) [0, 0x6, 0x5, 0x1, "contentPadding", 0x3, "hintStyle", 0x5, "hintText", 0x4, "prefixIcon", 0x1, "suffixIcon", 0x2, Null]
    //     0xb4a32c: ldr             x4, [x4, #0x618]
    // 0xb4a330: r0 = copyWith()
    //     0xb4a330: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb4a334: LeaveFrame
    //     0xb4a334: mov             SP, fp
    //     0xb4a338: ldp             fp, lr, [SP], #0x10
    // 0xb4a33c: ret
    //     0xb4a33c: ret             
    // 0xb4a340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a340: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a344: b               #0xb4a21c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4a348, size: 0x10c
    // 0xb4a348: EnterFrame
    //     0xb4a348: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a34c: mov             fp, SP
    // 0xb4a350: AllocStack(0x20)
    //     0xb4a350: sub             SP, SP, #0x20
    // 0xb4a354: SetupParameters()
    //     0xb4a354: ldr             x0, [fp, #0x10]
    //     0xb4a358: ldur            w1, [x0, #0x17]
    //     0xb4a35c: add             x1, x1, HEAP, lsl #32
    //     0xb4a360: stur            x1, [fp, #-8]
    // 0xb4a364: CheckStackOverflow
    //     0xb4a364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a368: cmp             SP, x16
    //     0xb4a36c: b.ls            #0xb4a42c
    // 0xb4a370: LoadField: r0 = r1->field_f
    //     0xb4a370: ldur            w0, [x1, #0xf]
    // 0xb4a374: DecompressPointer r0
    //     0xb4a374: add             x0, x0, HEAP, lsl #32
    // 0xb4a378: LoadField: r2 = r0->field_b
    //     0xb4a378: ldur            w2, [x0, #0xb]
    // 0xb4a37c: DecompressPointer r2
    //     0xb4a37c: add             x2, x2, HEAP, lsl #32
    // 0xb4a380: cmp             w2, NULL
    // 0xb4a384: b.eq            #0xb4a434
    // 0xb4a388: LoadField: r3 = r2->field_f
    //     0xb4a388: ldur            w3, [x2, #0xf]
    // 0xb4a38c: DecompressPointer r3
    //     0xb4a38c: add             x3, x3, HEAP, lsl #32
    // 0xb4a390: LoadField: r2 = r0->field_13
    //     0xb4a390: ldur            w2, [x0, #0x13]
    // 0xb4a394: DecompressPointer r2
    //     0xb4a394: add             x2, x2, HEAP, lsl #32
    // 0xb4a398: r16 = Sentinel
    //     0xb4a398: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb4a39c: cmp             w2, w16
    // 0xb4a3a0: b.eq            #0xb4a438
    // 0xb4a3a4: LoadField: r0 = r2->field_27
    //     0xb4a3a4: ldur            w0, [x2, #0x27]
    // 0xb4a3a8: DecompressPointer r0
    //     0xb4a3a8: add             x0, x0, HEAP, lsl #32
    // 0xb4a3ac: LoadField: r2 = r0->field_7
    //     0xb4a3ac: ldur            w2, [x0, #7]
    // 0xb4a3b0: DecompressPointer r2
    //     0xb4a3b0: add             x2, x2, HEAP, lsl #32
    // 0xb4a3b4: cmp             w3, NULL
    // 0xb4a3b8: b.eq            #0xb4a444
    // 0xb4a3bc: stp             x2, x3, [SP]
    // 0xb4a3c0: mov             x0, x3
    // 0xb4a3c4: ClosureCall
    //     0xb4a3c4: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb4a3c8: ldur            x2, [x0, #0x1f]
    //     0xb4a3cc: blr             x2
    // 0xb4a3d0: ldur            x0, [fp, #-8]
    // 0xb4a3d4: LoadField: r1 = r0->field_13
    //     0xb4a3d4: ldur            w1, [x0, #0x13]
    // 0xb4a3d8: DecompressPointer r1
    //     0xb4a3d8: add             x1, x1, HEAP, lsl #32
    // 0xb4a3dc: LoadField: r2 = r0->field_f
    //     0xb4a3dc: ldur            w2, [x0, #0xf]
    // 0xb4a3e0: DecompressPointer r2
    //     0xb4a3e0: add             x2, x2, HEAP, lsl #32
    // 0xb4a3e4: LoadField: r0 = r2->field_13
    //     0xb4a3e4: ldur            w0, [x2, #0x13]
    // 0xb4a3e8: DecompressPointer r0
    //     0xb4a3e8: add             x0, x0, HEAP, lsl #32
    // 0xb4a3ec: r16 = Sentinel
    //     0xb4a3ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb4a3f0: cmp             w0, w16
    // 0xb4a3f4: b.eq            #0xb4a448
    // 0xb4a3f8: LoadField: r2 = r0->field_27
    //     0xb4a3f8: ldur            w2, [x0, #0x27]
    // 0xb4a3fc: DecompressPointer r2
    //     0xb4a3fc: add             x2, x2, HEAP, lsl #32
    // 0xb4a400: LoadField: r0 = r2->field_7
    //     0xb4a400: ldur            w0, [x2, #7]
    // 0xb4a404: DecompressPointer r0
    //     0xb4a404: add             x0, x0, HEAP, lsl #32
    // 0xb4a408: r16 = <String>
    //     0xb4a408: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb4a40c: stp             x1, x16, [SP, #8]
    // 0xb4a410: str             x0, [SP]
    // 0xb4a414: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb4a414: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb4a418: r0 = pop()
    //     0xb4a418: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb4a41c: r0 = Null
    //     0xb4a41c: mov             x0, NULL
    // 0xb4a420: LeaveFrame
    //     0xb4a420: mov             SP, fp
    //     0xb4a424: ldp             fp, lr, [SP], #0x10
    // 0xb4a428: ret
    //     0xb4a428: ret             
    // 0xb4a42c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a42c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a430: b               #0xb4a370
    // 0xb4a434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4a434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4a438: r9 = _phoneController
    //     0xb4a438: add             x9, PP, #0x56, lsl #12  ; [pp+0x56608] Field <_EditPhoneBottomSheetState@1556118804._phoneController@1556118804>: late (offset: 0x14)
    //     0xb4a43c: ldr             x9, [x9, #0x608]
    // 0xb4a440: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb4a440: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb4a444: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb4a444: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb4a448: r9 = _phoneController
    //     0xb4a448: add             x9, PP, #0x56, lsl #12  ; [pp+0x56608] Field <_EditPhoneBottomSheetState@1556118804._phoneController@1556118804>: late (offset: 0x14)
    //     0xb4a44c: ldr             x9, [x9, #0x608]
    // 0xb4a450: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb4a450: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _handlePhoneNumberChanged(dynamic, String) {
    // ** addr: 0xb4a454, size: 0x3c
    // 0xb4a454: EnterFrame
    //     0xb4a454: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a458: mov             fp, SP
    // 0xb4a45c: ldr             x0, [fp, #0x18]
    // 0xb4a460: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb4a460: ldur            w1, [x0, #0x17]
    // 0xb4a464: DecompressPointer r1
    //     0xb4a464: add             x1, x1, HEAP, lsl #32
    // 0xb4a468: CheckStackOverflow
    //     0xb4a468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a46c: cmp             SP, x16
    //     0xb4a470: b.ls            #0xb4a488
    // 0xb4a474: ldr             x2, [fp, #0x10]
    // 0xb4a478: r0 = _handlePhoneNumberChanged()
    //     0xb4a478: bl              #0xb4a490  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_handlePhoneNumberChanged
    // 0xb4a47c: LeaveFrame
    //     0xb4a47c: mov             SP, fp
    //     0xb4a480: ldp             fp, lr, [SP], #0x10
    // 0xb4a484: ret
    //     0xb4a484: ret             
    // 0xb4a488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a488: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a48c: b               #0xb4a474
  }
  _ _handlePhoneNumberChanged(/* No info */) {
    // ** addr: 0xb4a490, size: 0x98
    // 0xb4a490: EnterFrame
    //     0xb4a490: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a494: mov             fp, SP
    // 0xb4a498: AllocStack(0x18)
    //     0xb4a498: sub             SP, SP, #0x18
    // 0xb4a49c: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb4a49c: stur            x1, [fp, #-8]
    //     0xb4a4a0: stur            x2, [fp, #-0x10]
    // 0xb4a4a4: CheckStackOverflow
    //     0xb4a4a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a4a8: cmp             SP, x16
    //     0xb4a4ac: b.ls            #0xb4a520
    // 0xb4a4b0: r1 = 2
    //     0xb4a4b0: movz            x1, #0x2
    // 0xb4a4b4: r0 = AllocateContext()
    //     0xb4a4b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4a4b8: mov             x3, x0
    // 0xb4a4bc: ldur            x0, [fp, #-8]
    // 0xb4a4c0: stur            x3, [fp, #-0x18]
    // 0xb4a4c4: StoreField: r3->field_f = r0
    //     0xb4a4c4: stur            w0, [x3, #0xf]
    // 0xb4a4c8: r1 = true
    //     0xb4a4c8: add             x1, NULL, #0x20  ; true
    // 0xb4a4cc: StoreField: r0->field_1b = r1
    //     0xb4a4cc: stur            w1, [x0, #0x1b]
    // 0xb4a4d0: mov             x1, x0
    // 0xb4a4d4: ldur            x2, [fp, #-0x10]
    // 0xb4a4d8: r0 = _isValidPhoneNumber()
    //     0xb4a4d8: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0xb4a4dc: ldur            x2, [fp, #-0x18]
    // 0xb4a4e0: StoreField: r2->field_13 = r0
    //     0xb4a4e0: stur            w0, [x2, #0x13]
    // 0xb4a4e4: ldur            x3, [fp, #-8]
    // 0xb4a4e8: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb4a4e8: ldur            w1, [x3, #0x17]
    // 0xb4a4ec: DecompressPointer r1
    //     0xb4a4ec: add             x1, x1, HEAP, lsl #32
    // 0xb4a4f0: cmp             w1, w0
    // 0xb4a4f4: b.eq            #0xb4a510
    // 0xb4a4f8: r1 = Function '<anonymous closure>':.
    //     0xb4a4f8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56610] AnonymousClosure: (0xa1f004), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_handlePhoneNumberChanged (0xa1f028)
    //     0xb4a4fc: ldr             x1, [x1, #0x610]
    // 0xb4a500: r0 = AllocateClosure()
    //     0xb4a500: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4a504: ldur            x1, [fp, #-8]
    // 0xb4a508: mov             x2, x0
    // 0xb4a50c: r0 = setState()
    //     0xb4a50c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb4a510: r0 = Null
    //     0xb4a510: mov             x0, NULL
    // 0xb4a514: LeaveFrame
    //     0xb4a514: mov             SP, fp
    //     0xb4a518: ldp             fp, lr, [SP], #0x10
    // 0xb4a51c: ret
    //     0xb4a51c: ret             
    // 0xb4a520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a520: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a524: b               #0xb4a4b0
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0xb4a528, size: 0x3c
    // 0xb4a528: EnterFrame
    //     0xb4a528: stp             fp, lr, [SP, #-0x10]!
    //     0xb4a52c: mov             fp, SP
    // 0xb4a530: ldr             x0, [fp, #0x18]
    // 0xb4a534: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb4a534: ldur            w1, [x0, #0x17]
    // 0xb4a538: DecompressPointer r1
    //     0xb4a538: add             x1, x1, HEAP, lsl #32
    // 0xb4a53c: CheckStackOverflow
    //     0xb4a53c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4a540: cmp             SP, x16
    //     0xb4a544: b.ls            #0xb4a55c
    // 0xb4a548: ldr             x2, [fp, #0x10]
    // 0xb4a54c: r0 = _validatePhoneNumber()
    //     0xb4a54c: bl              #0xa08f3c  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber
    // 0xb4a550: LeaveFrame
    //     0xb4a550: mov             SP, fp
    //     0xb4a554: ldp             fp, lr, [SP], #0x10
    // 0xb4a558: ret
    //     0xb4a558: ret             
    // 0xb4a55c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4a55c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4a560: b               #0xb4a548
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87924, size: 0x54
    // 0xc87924: EnterFrame
    //     0xc87924: stp             fp, lr, [SP, #-0x10]!
    //     0xc87928: mov             fp, SP
    // 0xc8792c: CheckStackOverflow
    //     0xc8792c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87930: cmp             SP, x16
    //     0xc87934: b.ls            #0xc87964
    // 0xc87938: LoadField: r0 = r1->field_13
    //     0xc87938: ldur            w0, [x1, #0x13]
    // 0xc8793c: DecompressPointer r0
    //     0xc8793c: add             x0, x0, HEAP, lsl #32
    // 0xc87940: r16 = Sentinel
    //     0xc87940: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87944: cmp             w0, w16
    // 0xc87948: b.eq            #0xc8796c
    // 0xc8794c: mov             x1, x0
    // 0xc87950: r0 = dispose()
    //     0xc87950: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87954: r0 = Null
    //     0xc87954: mov             x0, NULL
    // 0xc87958: LeaveFrame
    //     0xc87958: mov             SP, fp
    //     0xc8795c: ldp             fp, lr, [SP], #0x10
    // 0xc87960: ret
    //     0xc87960: ret             
    // 0xc87964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87964: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87968: b               #0xc87938
    // 0xc8796c: r9 = _phoneController
    //     0xc8796c: add             x9, PP, #0x56, lsl #12  ; [pp+0x56608] Field <_EditPhoneBottomSheetState@1556118804._phoneController@1556118804>: late (offset: 0x14)
    //     0xc87970: ldr             x9, [x9, #0x608]
    // 0xc87974: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87974: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4098, size: 0x14, field offset: 0xc
//   const constructor, 
class EditPhoneBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ecf8, size: 0x5c
    // 0xc7ecf8: EnterFrame
    //     0xc7ecf8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ecfc: mov             fp, SP
    // 0xc7ed00: AllocStack(0x8)
    //     0xc7ed00: sub             SP, SP, #8
    // 0xc7ed04: SetupParameters(EditPhoneBottomSheet this /* r1 => r0 */)
    //     0xc7ed04: mov             x0, x1
    // 0xc7ed08: r1 = <EditPhoneBottomSheet>
    //     0xc7ed08: add             x1, PP, #0x48, lsl #12  ; [pp+0x489d8] TypeArguments: <EditPhoneBottomSheet>
    //     0xc7ed0c: ldr             x1, [x1, #0x9d8]
    // 0xc7ed10: r0 = _EditPhoneBottomSheetState()
    //     0xc7ed10: bl              #0xc7ed54  ; Allocate_EditPhoneBottomSheetStateStub -> _EditPhoneBottomSheetState (size=0x24)
    // 0xc7ed14: mov             x2, x0
    // 0xc7ed18: r0 = Sentinel
    //     0xc7ed18: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7ed1c: stur            x2, [fp, #-8]
    // 0xc7ed20: StoreField: r2->field_13 = r0
    //     0xc7ed20: stur            w0, [x2, #0x13]
    // 0xc7ed24: r0 = false
    //     0xc7ed24: add             x0, NULL, #0x30  ; false
    // 0xc7ed28: ArrayStore: r2[0] = r0  ; List_4
    //     0xc7ed28: stur            w0, [x2, #0x17]
    // 0xc7ed2c: StoreField: r2->field_1b = r0
    //     0xc7ed2c: stur            w0, [x2, #0x1b]
    // 0xc7ed30: r1 = <FormState>
    //     0xc7ed30: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0xc7ed34: ldr             x1, [x1, #0xad8]
    // 0xc7ed38: r0 = LabeledGlobalKey()
    //     0xc7ed38: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xc7ed3c: mov             x1, x0
    // 0xc7ed40: ldur            x0, [fp, #-8]
    // 0xc7ed44: StoreField: r0->field_1f = r1
    //     0xc7ed44: stur            w1, [x0, #0x1f]
    // 0xc7ed48: LeaveFrame
    //     0xc7ed48: mov             SP, fp
    //     0xc7ed4c: ldp             fp, lr, [SP], #0x10
    // 0xc7ed50: ret
    //     0xc7ed50: ret             
  }
}
