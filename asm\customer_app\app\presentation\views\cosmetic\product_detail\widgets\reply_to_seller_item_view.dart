// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/reply_to_seller_item_view.dart

// class id: 1049321, size: 0x8
class :: {
}

// class id: 3396, size: 0x14, field offset: 0x14
class _ReplyToSellerItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb15dec, size: 0x77c
    // 0xb15dec: EnterFrame
    //     0xb15dec: stp             fp, lr, [SP, #-0x10]!
    //     0xb15df0: mov             fp, SP
    // 0xb15df4: AllocStack(0x80)
    //     0xb15df4: sub             SP, SP, #0x80
    // 0xb15df8: SetupParameters(_ReplyToSellerItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb15df8: mov             x0, x1
    //     0xb15dfc: stur            x1, [fp, #-8]
    //     0xb15e00: mov             x1, x2
    //     0xb15e04: stur            x2, [fp, #-0x10]
    // 0xb15e08: CheckStackOverflow
    //     0xb15e08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb15e0c: cmp             SP, x16
    //     0xb15e10: b.ls            #0xb16528
    // 0xb15e14: r1 = 1
    //     0xb15e14: movz            x1, #0x1
    // 0xb15e18: r0 = AllocateContext()
    //     0xb15e18: bl              #0x16f6108  ; AllocateContextStub
    // 0xb15e1c: mov             x1, x0
    // 0xb15e20: ldur            x0, [fp, #-8]
    // 0xb15e24: stur            x1, [fp, #-0x28]
    // 0xb15e28: StoreField: r1->field_f = r0
    //     0xb15e28: stur            w0, [x1, #0xf]
    // 0xb15e2c: LoadField: r2 = r0->field_b
    //     0xb15e2c: ldur            w2, [x0, #0xb]
    // 0xb15e30: DecompressPointer r2
    //     0xb15e30: add             x2, x2, HEAP, lsl #32
    // 0xb15e34: stur            x2, [fp, #-0x20]
    // 0xb15e38: cmp             w2, NULL
    // 0xb15e3c: b.eq            #0xb16530
    // 0xb15e40: LoadField: r3 = r2->field_f
    //     0xb15e40: ldur            w3, [x2, #0xf]
    // 0xb15e44: DecompressPointer r3
    //     0xb15e44: add             x3, x3, HEAP, lsl #32
    // 0xb15e48: cmp             w3, NULL
    // 0xb15e4c: b.ne            #0xb15e58
    // 0xb15e50: r3 = Null
    //     0xb15e50: mov             x3, NULL
    // 0xb15e54: b               #0xb15e64
    // 0xb15e58: LoadField: r4 = r3->field_6f
    //     0xb15e58: ldur            w4, [x3, #0x6f]
    // 0xb15e5c: DecompressPointer r4
    //     0xb15e5c: add             x4, x4, HEAP, lsl #32
    // 0xb15e60: mov             x3, x4
    // 0xb15e64: cmp             w3, NULL
    // 0xb15e68: b.eq            #0xb15eb8
    // 0xb15e6c: tbnz            w3, #4, #0xb15eb8
    // 0xb15e70: LoadField: r3 = r2->field_b
    //     0xb15e70: ldur            w3, [x2, #0xb]
    // 0xb15e74: DecompressPointer r3
    //     0xb15e74: add             x3, x3, HEAP, lsl #32
    // 0xb15e78: LoadField: r4 = r3->field_f
    //     0xb15e78: ldur            w4, [x3, #0xf]
    // 0xb15e7c: DecompressPointer r4
    //     0xb15e7c: add             x4, x4, HEAP, lsl #32
    // 0xb15e80: cmp             w4, NULL
    // 0xb15e84: b.ne            #0xb15e90
    // 0xb15e88: r3 = Null
    //     0xb15e88: mov             x3, NULL
    // 0xb15e8c: b               #0xb15ea8
    // 0xb15e90: LoadField: r3 = r4->field_7
    //     0xb15e90: ldur            w3, [x4, #7]
    // 0xb15e94: cbnz            w3, #0xb15ea0
    // 0xb15e98: r4 = false
    //     0xb15e98: add             x4, NULL, #0x30  ; false
    // 0xb15e9c: b               #0xb15ea4
    // 0xb15ea0: r4 = true
    //     0xb15ea0: add             x4, NULL, #0x20  ; true
    // 0xb15ea4: mov             x3, x4
    // 0xb15ea8: cmp             w3, NULL
    // 0xb15eac: b.ne            #0xb15ebc
    // 0xb15eb0: r3 = false
    //     0xb15eb0: add             x3, NULL, #0x30  ; false
    // 0xb15eb4: b               #0xb15ebc
    // 0xb15eb8: r3 = false
    //     0xb15eb8: add             x3, NULL, #0x30  ; false
    // 0xb15ebc: stur            x3, [fp, #-0x18]
    // 0xb15ec0: r0 = Radius()
    //     0xb15ec0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb15ec4: d0 = 12.000000
    //     0xb15ec4: fmov            d0, #12.00000000
    // 0xb15ec8: stur            x0, [fp, #-0x30]
    // 0xb15ecc: StoreField: r0->field_7 = d0
    //     0xb15ecc: stur            d0, [x0, #7]
    // 0xb15ed0: StoreField: r0->field_f = d0
    //     0xb15ed0: stur            d0, [x0, #0xf]
    // 0xb15ed4: r0 = BorderRadius()
    //     0xb15ed4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb15ed8: mov             x1, x0
    // 0xb15edc: ldur            x0, [fp, #-0x30]
    // 0xb15ee0: stur            x1, [fp, #-0x38]
    // 0xb15ee4: StoreField: r1->field_7 = r0
    //     0xb15ee4: stur            w0, [x1, #7]
    // 0xb15ee8: StoreField: r1->field_b = r0
    //     0xb15ee8: stur            w0, [x1, #0xb]
    // 0xb15eec: StoreField: r1->field_f = r0
    //     0xb15eec: stur            w0, [x1, #0xf]
    // 0xb15ef0: StoreField: r1->field_13 = r0
    //     0xb15ef0: stur            w0, [x1, #0x13]
    // 0xb15ef4: r0 = BoxDecoration()
    //     0xb15ef4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb15ef8: mov             x2, x0
    // 0xb15efc: r0 = Instance_DecorationImage
    //     0xb15efc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52ea0] Obj!DecorationImage@d5a141
    //     0xb15f00: ldr             x0, [x0, #0xea0]
    // 0xb15f04: stur            x2, [fp, #-0x30]
    // 0xb15f08: StoreField: r2->field_b = r0
    //     0xb15f08: stur            w0, [x2, #0xb]
    // 0xb15f0c: ldur            x0, [fp, #-0x38]
    // 0xb15f10: StoreField: r2->field_13 = r0
    //     0xb15f10: stur            w0, [x2, #0x13]
    // 0xb15f14: r0 = Instance_BoxShape
    //     0xb15f14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb15f18: ldr             x0, [x0, #0x80]
    // 0xb15f1c: StoreField: r2->field_23 = r0
    //     0xb15f1c: stur            w0, [x2, #0x23]
    // 0xb15f20: ldur            x1, [fp, #-0x20]
    // 0xb15f24: LoadField: r3 = r1->field_b
    //     0xb15f24: ldur            w3, [x1, #0xb]
    // 0xb15f28: DecompressPointer r3
    //     0xb15f28: add             x3, x3, HEAP, lsl #32
    // 0xb15f2c: LoadField: r1 = r3->field_f
    //     0xb15f2c: ldur            w1, [x3, #0xf]
    // 0xb15f30: DecompressPointer r1
    //     0xb15f30: add             x1, x1, HEAP, lsl #32
    // 0xb15f34: cmp             w1, NULL
    // 0xb15f38: b.ne            #0xb15f44
    // 0xb15f3c: r4 = ""
    //     0xb15f3c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb15f40: b               #0xb15f48
    // 0xb15f44: mov             x4, x1
    // 0xb15f48: ldur            x3, [fp, #-8]
    // 0xb15f4c: ldur            x1, [fp, #-0x10]
    // 0xb15f50: stur            x4, [fp, #-0x20]
    // 0xb15f54: r0 = of()
    //     0xb15f54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb15f58: LoadField: r1 = r0->field_87
    //     0xb15f58: ldur            w1, [x0, #0x87]
    // 0xb15f5c: DecompressPointer r1
    //     0xb15f5c: add             x1, x1, HEAP, lsl #32
    // 0xb15f60: LoadField: r0 = r1->field_7
    //     0xb15f60: ldur            w0, [x1, #7]
    // 0xb15f64: DecompressPointer r0
    //     0xb15f64: add             x0, x0, HEAP, lsl #32
    // 0xb15f68: r16 = 16.000000
    //     0xb15f68: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb15f6c: ldr             x16, [x16, #0x188]
    // 0xb15f70: r30 = Instance_Color
    //     0xb15f70: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb15f74: stp             lr, x16, [SP]
    // 0xb15f78: mov             x1, x0
    // 0xb15f7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb15f7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb15f80: ldr             x4, [x4, #0xaa0]
    // 0xb15f84: r0 = copyWith()
    //     0xb15f84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb15f88: stur            x0, [fp, #-0x38]
    // 0xb15f8c: r0 = Text()
    //     0xb15f8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb15f90: mov             x1, x0
    // 0xb15f94: ldur            x0, [fp, #-0x20]
    // 0xb15f98: stur            x1, [fp, #-0x40]
    // 0xb15f9c: StoreField: r1->field_b = r0
    //     0xb15f9c: stur            w0, [x1, #0xb]
    // 0xb15fa0: ldur            x0, [fp, #-0x38]
    // 0xb15fa4: StoreField: r1->field_13 = r0
    //     0xb15fa4: stur            w0, [x1, #0x13]
    // 0xb15fa8: r0 = Padding()
    //     0xb15fa8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb15fac: mov             x2, x0
    // 0xb15fb0: r0 = Instance_EdgeInsets
    //     0xb15fb0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb15fb4: ldr             x0, [x0, #0x858]
    // 0xb15fb8: stur            x2, [fp, #-0x38]
    // 0xb15fbc: StoreField: r2->field_f = r0
    //     0xb15fbc: stur            w0, [x2, #0xf]
    // 0xb15fc0: ldur            x0, [fp, #-0x40]
    // 0xb15fc4: StoreField: r2->field_b = r0
    //     0xb15fc4: stur            w0, [x2, #0xb]
    // 0xb15fc8: ldur            x0, [fp, #-8]
    // 0xb15fcc: LoadField: r1 = r0->field_b
    //     0xb15fcc: ldur            w1, [x0, #0xb]
    // 0xb15fd0: DecompressPointer r1
    //     0xb15fd0: add             x1, x1, HEAP, lsl #32
    // 0xb15fd4: cmp             w1, NULL
    // 0xb15fd8: b.eq            #0xb16534
    // 0xb15fdc: LoadField: r3 = r1->field_b
    //     0xb15fdc: ldur            w3, [x1, #0xb]
    // 0xb15fe0: DecompressPointer r3
    //     0xb15fe0: add             x3, x3, HEAP, lsl #32
    // 0xb15fe4: LoadField: r1 = r3->field_73
    //     0xb15fe4: ldur            w1, [x3, #0x73]
    // 0xb15fe8: DecompressPointer r1
    //     0xb15fe8: add             x1, x1, HEAP, lsl #32
    // 0xb15fec: cmp             w1, NULL
    // 0xb15ff0: b.ne            #0xb15ffc
    // 0xb15ff4: r3 = ""
    //     0xb15ff4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb15ff8: b               #0xb16000
    // 0xb15ffc: mov             x3, x1
    // 0xb16000: ldur            x1, [fp, #-0x10]
    // 0xb16004: stur            x3, [fp, #-0x20]
    // 0xb16008: r0 = of()
    //     0xb16008: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1600c: LoadField: r1 = r0->field_87
    //     0xb1600c: ldur            w1, [x0, #0x87]
    // 0xb16010: DecompressPointer r1
    //     0xb16010: add             x1, x1, HEAP, lsl #32
    // 0xb16014: LoadField: r0 = r1->field_7
    //     0xb16014: ldur            w0, [x1, #7]
    // 0xb16018: DecompressPointer r0
    //     0xb16018: add             x0, x0, HEAP, lsl #32
    // 0xb1601c: stur            x0, [fp, #-0x40]
    // 0xb16020: r1 = Instance_Color
    //     0xb16020: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb16024: d0 = 0.300000
    //     0xb16024: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb16028: ldr             d0, [x17, #0x658]
    // 0xb1602c: r0 = withOpacity()
    //     0xb1602c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb16030: r16 = 12.000000
    //     0xb16030: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb16034: ldr             x16, [x16, #0x9e8]
    // 0xb16038: stp             x0, x16, [SP]
    // 0xb1603c: ldur            x1, [fp, #-0x40]
    // 0xb16040: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb16040: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb16044: ldr             x4, [x4, #0xaa0]
    // 0xb16048: r0 = copyWith()
    //     0xb16048: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb1604c: stur            x0, [fp, #-0x40]
    // 0xb16050: r0 = Text()
    //     0xb16050: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb16054: mov             x1, x0
    // 0xb16058: ldur            x0, [fp, #-0x20]
    // 0xb1605c: stur            x1, [fp, #-0x48]
    // 0xb16060: StoreField: r1->field_b = r0
    //     0xb16060: stur            w0, [x1, #0xb]
    // 0xb16064: ldur            x0, [fp, #-0x40]
    // 0xb16068: StoreField: r1->field_13 = r0
    //     0xb16068: stur            w0, [x1, #0x13]
    // 0xb1606c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb1606c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb16070: ldr             x0, [x0, #0x1c80]
    //     0xb16074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb16078: cmp             w0, w16
    //     0xb1607c: b.ne            #0xb16088
    //     0xb16080: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb16084: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb16088: r0 = GetNavigation.size()
    //     0xb16088: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb1608c: LoadField: d0 = r0->field_7
    //     0xb1608c: ldur            d0, [x0, #7]
    // 0xb16090: stur            d0, [fp, #-0x58]
    // 0xb16094: r0 = Radius()
    //     0xb16094: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb16098: d0 = 24.000000
    //     0xb16098: fmov            d0, #24.00000000
    // 0xb1609c: stur            x0, [fp, #-0x20]
    // 0xb160a0: StoreField: r0->field_7 = d0
    //     0xb160a0: stur            d0, [x0, #7]
    // 0xb160a4: StoreField: r0->field_f = d0
    //     0xb160a4: stur            d0, [x0, #0xf]
    // 0xb160a8: r0 = BorderRadius()
    //     0xb160a8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb160ac: mov             x1, x0
    // 0xb160b0: ldur            x0, [fp, #-0x20]
    // 0xb160b4: stur            x1, [fp, #-0x40]
    // 0xb160b8: StoreField: r1->field_7 = r0
    //     0xb160b8: stur            w0, [x1, #7]
    // 0xb160bc: StoreField: r1->field_b = r0
    //     0xb160bc: stur            w0, [x1, #0xb]
    // 0xb160c0: StoreField: r1->field_f = r0
    //     0xb160c0: stur            w0, [x1, #0xf]
    // 0xb160c4: StoreField: r1->field_13 = r0
    //     0xb160c4: stur            w0, [x1, #0x13]
    // 0xb160c8: r0 = BoxDecoration()
    //     0xb160c8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb160cc: mov             x1, x0
    // 0xb160d0: r0 = Instance_Color
    //     0xb160d0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb160d4: stur            x1, [fp, #-0x20]
    // 0xb160d8: StoreField: r1->field_7 = r0
    //     0xb160d8: stur            w0, [x1, #7]
    // 0xb160dc: ldur            x0, [fp, #-0x40]
    // 0xb160e0: StoreField: r1->field_13 = r0
    //     0xb160e0: stur            w0, [x1, #0x13]
    // 0xb160e4: r0 = Instance_BoxShape
    //     0xb160e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb160e8: ldr             x0, [x0, #0x80]
    // 0xb160ec: StoreField: r1->field_23 = r0
    //     0xb160ec: stur            w0, [x1, #0x23]
    // 0xb160f0: r0 = SvgPicture()
    //     0xb160f0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb160f4: mov             x1, x0
    // 0xb160f8: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xb160f8: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xb160fc: ldr             x2, [x2, #0x140]
    // 0xb16100: stur            x0, [fp, #-0x40]
    // 0xb16104: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb16104: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb16108: r0 = SvgPicture.asset()
    //     0xb16108: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb1610c: r0 = Align()
    //     0xb1610c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb16110: mov             x2, x0
    // 0xb16114: r0 = Instance_Alignment
    //     0xb16114: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb16118: ldr             x0, [x0, #0xb10]
    // 0xb1611c: stur            x2, [fp, #-0x50]
    // 0xb16120: StoreField: r2->field_f = r0
    //     0xb16120: stur            w0, [x2, #0xf]
    // 0xb16124: r0 = 1.000000
    //     0xb16124: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb16128: StoreField: r2->field_13 = r0
    //     0xb16128: stur            w0, [x2, #0x13]
    // 0xb1612c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb1612c: stur            w0, [x2, #0x17]
    // 0xb16130: ldur            x0, [fp, #-0x40]
    // 0xb16134: StoreField: r2->field_b = r0
    //     0xb16134: stur            w0, [x2, #0xb]
    // 0xb16138: ldur            x0, [fp, #-8]
    // 0xb1613c: LoadField: r1 = r0->field_b
    //     0xb1613c: ldur            w1, [x0, #0xb]
    // 0xb16140: DecompressPointer r1
    //     0xb16140: add             x1, x1, HEAP, lsl #32
    // 0xb16144: cmp             w1, NULL
    // 0xb16148: b.eq            #0xb16538
    // 0xb1614c: LoadField: r0 = r1->field_b
    //     0xb1614c: ldur            w0, [x1, #0xb]
    // 0xb16150: DecompressPointer r0
    //     0xb16150: add             x0, x0, HEAP, lsl #32
    // 0xb16154: LoadField: r1 = r0->field_77
    //     0xb16154: ldur            w1, [x0, #0x77]
    // 0xb16158: DecompressPointer r1
    //     0xb16158: add             x1, x1, HEAP, lsl #32
    // 0xb1615c: cmp             w1, NULL
    // 0xb16160: b.ne            #0xb1616c
    // 0xb16164: r5 = ""
    //     0xb16164: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb16168: b               #0xb16170
    // 0xb1616c: mov             x5, x1
    // 0xb16170: ldur            x4, [fp, #-0x18]
    // 0xb16174: ldur            x3, [fp, #-0x38]
    // 0xb16178: ldur            x0, [fp, #-0x48]
    // 0xb1617c: ldur            d0, [fp, #-0x58]
    // 0xb16180: ldur            x1, [fp, #-0x10]
    // 0xb16184: stur            x5, [fp, #-8]
    // 0xb16188: r0 = of()
    //     0xb16188: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb1618c: LoadField: r1 = r0->field_87
    //     0xb1618c: ldur            w1, [x0, #0x87]
    // 0xb16190: DecompressPointer r1
    //     0xb16190: add             x1, x1, HEAP, lsl #32
    // 0xb16194: LoadField: r0 = r1->field_7
    //     0xb16194: ldur            w0, [x1, #7]
    // 0xb16198: DecompressPointer r0
    //     0xb16198: add             x0, x0, HEAP, lsl #32
    // 0xb1619c: ldur            x1, [fp, #-0x10]
    // 0xb161a0: stur            x0, [fp, #-0x40]
    // 0xb161a4: r0 = of()
    //     0xb161a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb161a8: LoadField: r1 = r0->field_5b
    //     0xb161a8: ldur            w1, [x0, #0x5b]
    // 0xb161ac: DecompressPointer r1
    //     0xb161ac: add             x1, x1, HEAP, lsl #32
    // 0xb161b0: r16 = 16.000000
    //     0xb161b0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb161b4: ldr             x16, [x16, #0x188]
    // 0xb161b8: stp             x1, x16, [SP]
    // 0xb161bc: ldur            x1, [fp, #-0x40]
    // 0xb161c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb161c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb161c4: ldr             x4, [x4, #0xaa0]
    // 0xb161c8: r0 = copyWith()
    //     0xb161c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb161cc: stur            x0, [fp, #-0x10]
    // 0xb161d0: r0 = Text()
    //     0xb161d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb161d4: mov             x1, x0
    // 0xb161d8: ldur            x0, [fp, #-8]
    // 0xb161dc: stur            x1, [fp, #-0x40]
    // 0xb161e0: StoreField: r1->field_b = r0
    //     0xb161e0: stur            w0, [x1, #0xb]
    // 0xb161e4: ldur            x0, [fp, #-0x10]
    // 0xb161e8: StoreField: r1->field_13 = r0
    //     0xb161e8: stur            w0, [x1, #0x13]
    // 0xb161ec: r0 = InkWell()
    //     0xb161ec: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb161f0: mov             x3, x0
    // 0xb161f4: ldur            x0, [fp, #-0x40]
    // 0xb161f8: stur            x3, [fp, #-8]
    // 0xb161fc: StoreField: r3->field_b = r0
    //     0xb161fc: stur            w0, [x3, #0xb]
    // 0xb16200: ldur            x2, [fp, #-0x28]
    // 0xb16204: r1 = Function '<anonymous closure>':.
    //     0xb16204: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f50] AnonymousClosure: (0xa8e78c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart] _ReplyToSellerItemViewState::build (0xc0b9f8)
    //     0xb16208: ldr             x1, [x1, #0xf50]
    // 0xb1620c: r0 = AllocateClosure()
    //     0xb1620c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb16210: mov             x1, x0
    // 0xb16214: ldur            x0, [fp, #-8]
    // 0xb16218: StoreField: r0->field_f = r1
    //     0xb16218: stur            w1, [x0, #0xf]
    // 0xb1621c: r3 = true
    //     0xb1621c: add             x3, NULL, #0x20  ; true
    // 0xb16220: StoreField: r0->field_43 = r3
    //     0xb16220: stur            w3, [x0, #0x43]
    // 0xb16224: r4 = Instance_BoxShape
    //     0xb16224: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb16228: ldr             x4, [x4, #0x80]
    // 0xb1622c: StoreField: r0->field_47 = r4
    //     0xb1622c: stur            w4, [x0, #0x47]
    // 0xb16230: StoreField: r0->field_6f = r3
    //     0xb16230: stur            w3, [x0, #0x6f]
    // 0xb16234: r5 = false
    //     0xb16234: add             x5, NULL, #0x30  ; false
    // 0xb16238: StoreField: r0->field_73 = r5
    //     0xb16238: stur            w5, [x0, #0x73]
    // 0xb1623c: StoreField: r0->field_83 = r3
    //     0xb1623c: stur            w3, [x0, #0x83]
    // 0xb16240: StoreField: r0->field_7b = r5
    //     0xb16240: stur            w5, [x0, #0x7b]
    // 0xb16244: r1 = Null
    //     0xb16244: mov             x1, NULL
    // 0xb16248: r2 = 6
    //     0xb16248: movz            x2, #0x6
    // 0xb1624c: r0 = AllocateArray()
    //     0xb1624c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb16250: mov             x2, x0
    // 0xb16254: ldur            x0, [fp, #-0x50]
    // 0xb16258: stur            x2, [fp, #-0x10]
    // 0xb1625c: StoreField: r2->field_f = r0
    //     0xb1625c: stur            w0, [x2, #0xf]
    // 0xb16260: r16 = Instance_SizedBox
    //     0xb16260: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb16264: ldr             x16, [x16, #0x998]
    // 0xb16268: StoreField: r2->field_13 = r16
    //     0xb16268: stur            w16, [x2, #0x13]
    // 0xb1626c: ldur            x0, [fp, #-8]
    // 0xb16270: ArrayStore: r2[0] = r0  ; List_4
    //     0xb16270: stur            w0, [x2, #0x17]
    // 0xb16274: r1 = <Widget>
    //     0xb16274: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16278: r0 = AllocateGrowableArray()
    //     0xb16278: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb1627c: mov             x1, x0
    // 0xb16280: ldur            x0, [fp, #-0x10]
    // 0xb16284: stur            x1, [fp, #-8]
    // 0xb16288: StoreField: r1->field_f = r0
    //     0xb16288: stur            w0, [x1, #0xf]
    // 0xb1628c: r0 = 6
    //     0xb1628c: movz            x0, #0x6
    // 0xb16290: StoreField: r1->field_b = r0
    //     0xb16290: stur            w0, [x1, #0xb]
    // 0xb16294: r0 = Row()
    //     0xb16294: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb16298: mov             x1, x0
    // 0xb1629c: r0 = Instance_Axis
    //     0xb1629c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb162a0: stur            x1, [fp, #-0x10]
    // 0xb162a4: StoreField: r1->field_f = r0
    //     0xb162a4: stur            w0, [x1, #0xf]
    // 0xb162a8: r0 = Instance_MainAxisAlignment
    //     0xb162a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb162ac: ldr             x0, [x0, #0xab0]
    // 0xb162b0: StoreField: r1->field_13 = r0
    //     0xb162b0: stur            w0, [x1, #0x13]
    // 0xb162b4: r0 = Instance_MainAxisSize
    //     0xb162b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb162b8: ldr             x0, [x0, #0xa10]
    // 0xb162bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb162bc: stur            w0, [x1, #0x17]
    // 0xb162c0: r2 = Instance_CrossAxisAlignment
    //     0xb162c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb162c4: ldr             x2, [x2, #0xa18]
    // 0xb162c8: StoreField: r1->field_1b = r2
    //     0xb162c8: stur            w2, [x1, #0x1b]
    // 0xb162cc: r3 = Instance_VerticalDirection
    //     0xb162cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb162d0: ldr             x3, [x3, #0xa20]
    // 0xb162d4: StoreField: r1->field_23 = r3
    //     0xb162d4: stur            w3, [x1, #0x23]
    // 0xb162d8: r4 = Instance_Clip
    //     0xb162d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb162dc: ldr             x4, [x4, #0x38]
    // 0xb162e0: StoreField: r1->field_2b = r4
    //     0xb162e0: stur            w4, [x1, #0x2b]
    // 0xb162e4: StoreField: r1->field_2f = rZR
    //     0xb162e4: stur            xzr, [x1, #0x2f]
    // 0xb162e8: ldur            x5, [fp, #-8]
    // 0xb162ec: StoreField: r1->field_b = r5
    //     0xb162ec: stur            w5, [x1, #0xb]
    // 0xb162f0: ldur            d0, [fp, #-0x58]
    // 0xb162f4: r5 = inline_Allocate_Double()
    //     0xb162f4: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xb162f8: add             x5, x5, #0x10
    //     0xb162fc: cmp             x6, x5
    //     0xb16300: b.ls            #0xb1653c
    //     0xb16304: str             x5, [THR, #0x50]  ; THR::top
    //     0xb16308: sub             x5, x5, #0xf
    //     0xb1630c: movz            x6, #0xe15c
    //     0xb16310: movk            x6, #0x3, lsl #16
    //     0xb16314: stur            x6, [x5, #-1]
    // 0xb16318: StoreField: r5->field_7 = d0
    //     0xb16318: stur            d0, [x5, #7]
    // 0xb1631c: stur            x5, [fp, #-8]
    // 0xb16320: r0 = Container()
    //     0xb16320: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb16324: stur            x0, [fp, #-0x40]
    // 0xb16328: ldur            x16, [fp, #-8]
    // 0xb1632c: r30 = 48.000000
    //     0xb1632c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb16330: ldr             lr, [lr, #0xad8]
    // 0xb16334: stp             lr, x16, [SP, #0x18]
    // 0xb16338: ldur            x16, [fp, #-0x20]
    // 0xb1633c: r30 = Instance_Alignment
    //     0xb1633c: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb16340: ldr             lr, [lr, #0xb10]
    // 0xb16344: stp             lr, x16, [SP, #8]
    // 0xb16348: ldur            x16, [fp, #-0x10]
    // 0xb1634c: str             x16, [SP]
    // 0xb16350: mov             x1, x0
    // 0xb16354: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb16354: add             x4, PP, #0x55, lsl #12  ; [pp+0x559e8] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb16358: ldr             x4, [x4, #0x9e8]
    // 0xb1635c: r0 = Container()
    //     0xb1635c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb16360: r0 = InkWell()
    //     0xb16360: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb16364: mov             x3, x0
    // 0xb16368: ldur            x0, [fp, #-0x40]
    // 0xb1636c: stur            x3, [fp, #-8]
    // 0xb16370: StoreField: r3->field_b = r0
    //     0xb16370: stur            w0, [x3, #0xb]
    // 0xb16374: ldur            x2, [fp, #-0x28]
    // 0xb16378: r1 = Function '<anonymous closure>':.
    //     0xb16378: add             x1, PP, #0x57, lsl #12  ; [pp+0x57f58] AnonymousClosure: (0xa8e78c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart] _ReplyToSellerItemViewState::build (0xc0b9f8)
    //     0xb1637c: ldr             x1, [x1, #0xf58]
    // 0xb16380: r0 = AllocateClosure()
    //     0xb16380: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb16384: mov             x1, x0
    // 0xb16388: ldur            x0, [fp, #-8]
    // 0xb1638c: StoreField: r0->field_f = r1
    //     0xb1638c: stur            w1, [x0, #0xf]
    // 0xb16390: r1 = true
    //     0xb16390: add             x1, NULL, #0x20  ; true
    // 0xb16394: StoreField: r0->field_43 = r1
    //     0xb16394: stur            w1, [x0, #0x43]
    // 0xb16398: r2 = Instance_BoxShape
    //     0xb16398: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb1639c: ldr             x2, [x2, #0x80]
    // 0xb163a0: StoreField: r0->field_47 = r2
    //     0xb163a0: stur            w2, [x0, #0x47]
    // 0xb163a4: StoreField: r0->field_6f = r1
    //     0xb163a4: stur            w1, [x0, #0x6f]
    // 0xb163a8: r2 = false
    //     0xb163a8: add             x2, NULL, #0x30  ; false
    // 0xb163ac: StoreField: r0->field_73 = r2
    //     0xb163ac: stur            w2, [x0, #0x73]
    // 0xb163b0: StoreField: r0->field_83 = r1
    //     0xb163b0: stur            w1, [x0, #0x83]
    // 0xb163b4: StoreField: r0->field_7b = r2
    //     0xb163b4: stur            w2, [x0, #0x7b]
    // 0xb163b8: r0 = Padding()
    //     0xb163b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb163bc: mov             x3, x0
    // 0xb163c0: r0 = Instance_EdgeInsets
    //     0xb163c0: add             x0, PP, #0x55, lsl #12  ; [pp+0x559f8] Obj!EdgeInsets@d59121
    //     0xb163c4: ldr             x0, [x0, #0x9f8]
    // 0xb163c8: stur            x3, [fp, #-0x10]
    // 0xb163cc: StoreField: r3->field_f = r0
    //     0xb163cc: stur            w0, [x3, #0xf]
    // 0xb163d0: ldur            x0, [fp, #-8]
    // 0xb163d4: StoreField: r3->field_b = r0
    //     0xb163d4: stur            w0, [x3, #0xb]
    // 0xb163d8: r1 = Null
    //     0xb163d8: mov             x1, NULL
    // 0xb163dc: r2 = 8
    //     0xb163dc: movz            x2, #0x8
    // 0xb163e0: r0 = AllocateArray()
    //     0xb163e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb163e4: mov             x2, x0
    // 0xb163e8: ldur            x0, [fp, #-0x38]
    // 0xb163ec: stur            x2, [fp, #-8]
    // 0xb163f0: StoreField: r2->field_f = r0
    //     0xb163f0: stur            w0, [x2, #0xf]
    // 0xb163f4: r16 = Instance_SizedBox
    //     0xb163f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb163f8: ldr             x16, [x16, #0x8b8]
    // 0xb163fc: StoreField: r2->field_13 = r16
    //     0xb163fc: stur            w16, [x2, #0x13]
    // 0xb16400: ldur            x0, [fp, #-0x48]
    // 0xb16404: ArrayStore: r2[0] = r0  ; List_4
    //     0xb16404: stur            w0, [x2, #0x17]
    // 0xb16408: ldur            x0, [fp, #-0x10]
    // 0xb1640c: StoreField: r2->field_1b = r0
    //     0xb1640c: stur            w0, [x2, #0x1b]
    // 0xb16410: r1 = <Widget>
    //     0xb16410: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb16414: r0 = AllocateGrowableArray()
    //     0xb16414: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb16418: mov             x1, x0
    // 0xb1641c: ldur            x0, [fp, #-8]
    // 0xb16420: stur            x1, [fp, #-0x10]
    // 0xb16424: StoreField: r1->field_f = r0
    //     0xb16424: stur            w0, [x1, #0xf]
    // 0xb16428: r0 = 8
    //     0xb16428: movz            x0, #0x8
    // 0xb1642c: StoreField: r1->field_b = r0
    //     0xb1642c: stur            w0, [x1, #0xb]
    // 0xb16430: r0 = Column()
    //     0xb16430: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb16434: mov             x1, x0
    // 0xb16438: r0 = Instance_Axis
    //     0xb16438: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb1643c: stur            x1, [fp, #-8]
    // 0xb16440: StoreField: r1->field_f = r0
    //     0xb16440: stur            w0, [x1, #0xf]
    // 0xb16444: r0 = Instance_MainAxisAlignment
    //     0xb16444: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb16448: ldr             x0, [x0, #0xa08]
    // 0xb1644c: StoreField: r1->field_13 = r0
    //     0xb1644c: stur            w0, [x1, #0x13]
    // 0xb16450: r0 = Instance_MainAxisSize
    //     0xb16450: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb16454: ldr             x0, [x0, #0xa10]
    // 0xb16458: ArrayStore: r1[0] = r0  ; List_4
    //     0xb16458: stur            w0, [x1, #0x17]
    // 0xb1645c: r0 = Instance_CrossAxisAlignment
    //     0xb1645c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb16460: ldr             x0, [x0, #0xa18]
    // 0xb16464: StoreField: r1->field_1b = r0
    //     0xb16464: stur            w0, [x1, #0x1b]
    // 0xb16468: r0 = Instance_VerticalDirection
    //     0xb16468: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb1646c: ldr             x0, [x0, #0xa20]
    // 0xb16470: StoreField: r1->field_23 = r0
    //     0xb16470: stur            w0, [x1, #0x23]
    // 0xb16474: r0 = Instance_Clip
    //     0xb16474: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb16478: ldr             x0, [x0, #0x38]
    // 0xb1647c: StoreField: r1->field_2b = r0
    //     0xb1647c: stur            w0, [x1, #0x2b]
    // 0xb16480: StoreField: r1->field_2f = rZR
    //     0xb16480: stur            xzr, [x1, #0x2f]
    // 0xb16484: ldur            x0, [fp, #-0x10]
    // 0xb16488: StoreField: r1->field_b = r0
    //     0xb16488: stur            w0, [x1, #0xb]
    // 0xb1648c: r0 = Container()
    //     0xb1648c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb16490: stur            x0, [fp, #-0x10]
    // 0xb16494: r16 = inf
    //     0xb16494: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb16498: ldr             x16, [x16, #0x9f8]
    // 0xb1649c: r30 = 152.000000
    //     0xb1649c: add             lr, PP, #0x55, lsl #12  ; [pp+0x55a00] 152
    //     0xb164a0: ldr             lr, [lr, #0xa00]
    // 0xb164a4: stp             lr, x16, [SP, #0x10]
    // 0xb164a8: ldur            x16, [fp, #-0x30]
    // 0xb164ac: ldur            lr, [fp, #-8]
    // 0xb164b0: stp             lr, x16, [SP]
    // 0xb164b4: mov             x1, x0
    // 0xb164b8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb164b8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb164bc: ldr             x4, [x4, #0x870]
    // 0xb164c0: r0 = Container()
    //     0xb164c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb164c4: r0 = Visibility()
    //     0xb164c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb164c8: mov             x1, x0
    // 0xb164cc: ldur            x0, [fp, #-0x10]
    // 0xb164d0: stur            x1, [fp, #-8]
    // 0xb164d4: StoreField: r1->field_b = r0
    //     0xb164d4: stur            w0, [x1, #0xb]
    // 0xb164d8: r0 = Instance_SizedBox
    //     0xb164d8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb164dc: StoreField: r1->field_f = r0
    //     0xb164dc: stur            w0, [x1, #0xf]
    // 0xb164e0: ldur            x0, [fp, #-0x18]
    // 0xb164e4: StoreField: r1->field_13 = r0
    //     0xb164e4: stur            w0, [x1, #0x13]
    // 0xb164e8: r0 = false
    //     0xb164e8: add             x0, NULL, #0x30  ; false
    // 0xb164ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xb164ec: stur            w0, [x1, #0x17]
    // 0xb164f0: StoreField: r1->field_1b = r0
    //     0xb164f0: stur            w0, [x1, #0x1b]
    // 0xb164f4: StoreField: r1->field_1f = r0
    //     0xb164f4: stur            w0, [x1, #0x1f]
    // 0xb164f8: StoreField: r1->field_23 = r0
    //     0xb164f8: stur            w0, [x1, #0x23]
    // 0xb164fc: StoreField: r1->field_27 = r0
    //     0xb164fc: stur            w0, [x1, #0x27]
    // 0xb16500: StoreField: r1->field_2b = r0
    //     0xb16500: stur            w0, [x1, #0x2b]
    // 0xb16504: r0 = Padding()
    //     0xb16504: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb16508: r1 = Instance_EdgeInsets
    //     0xb16508: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb1650c: ldr             x1, [x1, #0x110]
    // 0xb16510: StoreField: r0->field_f = r1
    //     0xb16510: stur            w1, [x0, #0xf]
    // 0xb16514: ldur            x1, [fp, #-8]
    // 0xb16518: StoreField: r0->field_b = r1
    //     0xb16518: stur            w1, [x0, #0xb]
    // 0xb1651c: LeaveFrame
    //     0xb1651c: mov             SP, fp
    //     0xb16520: ldp             fp, lr, [SP], #0x10
    // 0xb16524: ret
    //     0xb16524: ret             
    // 0xb16528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb16528: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb1652c: b               #0xb15e14
    // 0xb16530: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb16530: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb16534: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb16534: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb16538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb16538: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb1653c: SaveReg d0
    //     0xb1653c: str             q0, [SP, #-0x10]!
    // 0xb16540: stp             x3, x4, [SP, #-0x10]!
    // 0xb16544: stp             x1, x2, [SP, #-0x10]!
    // 0xb16548: SaveReg r0
    //     0xb16548: str             x0, [SP, #-8]!
    // 0xb1654c: r0 = AllocateDouble()
    //     0xb1654c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb16550: mov             x5, x0
    // 0xb16554: RestoreReg r0
    //     0xb16554: ldr             x0, [SP], #8
    // 0xb16558: ldp             x1, x2, [SP], #0x10
    // 0xb1655c: ldp             x3, x4, [SP], #0x10
    // 0xb16560: RestoreReg d0
    //     0xb16560: ldr             q0, [SP], #0x10
    // 0xb16564: b               #0xb16318
  }
}

// class id: 4134, size: 0x14, field offset: 0xc
//   const constructor, 
class ReplyToSellerItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e1b8, size: 0x24
    // 0xc7e1b8: EnterFrame
    //     0xc7e1b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e1bc: mov             fp, SP
    // 0xc7e1c0: mov             x0, x1
    // 0xc7e1c4: r1 = <ReplyToSellerItemView>
    //     0xc7e1c4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b90] TypeArguments: <ReplyToSellerItemView>
    //     0xc7e1c8: ldr             x1, [x1, #0xb90]
    // 0xc7e1cc: r0 = _ReplyToSellerItemViewState()
    //     0xc7e1cc: bl              #0xc7e1dc  ; Allocate_ReplyToSellerItemViewStateStub -> _ReplyToSellerItemViewState (size=0x14)
    // 0xc7e1d0: LeaveFrame
    //     0xc7e1d0: mov             SP, fp
    //     0xc7e1d4: ldp             fp, lr, [SP], #0x10
    // 0xc7e1d8: ret
    //     0xc7e1d8: ret             
  }
}
