// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/view_size_widget.dart

// class id: 1049576, size: 0x8
class :: {
}

// class id: 3209, size: 0x14, field offset: 0x14
class _ViewSizeWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc11f64, size: 0x1b0
    // 0xc11f64: EnterFrame
    //     0xc11f64: stp             fp, lr, [SP, #-0x10]!
    //     0xc11f68: mov             fp, SP
    // 0xc11f6c: AllocStack(0x30)
    //     0xc11f6c: sub             SP, SP, #0x30
    // 0xc11f70: SetupParameters(_ViewSizeWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc11f70: mov             x0, x1
    //     0xc11f74: stur            x1, [fp, #-8]
    //     0xc11f78: mov             x1, x2
    //     0xc11f7c: stur            x2, [fp, #-0x10]
    // 0xc11f80: CheckStackOverflow
    //     0xc11f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc11f84: cmp             SP, x16
    //     0xc11f88: b.ls            #0xc12108
    // 0xc11f8c: r1 = 1
    //     0xc11f8c: movz            x1, #0x1
    // 0xc11f90: r0 = AllocateContext()
    //     0xc11f90: bl              #0x16f6108  ; AllocateContextStub
    // 0xc11f94: mov             x2, x0
    // 0xc11f98: ldur            x0, [fp, #-8]
    // 0xc11f9c: stur            x2, [fp, #-0x18]
    // 0xc11fa0: StoreField: r2->field_f = r0
    //     0xc11fa0: stur            w0, [x2, #0xf]
    // 0xc11fa4: LoadField: r1 = r0->field_b
    //     0xc11fa4: ldur            w1, [x0, #0xb]
    // 0xc11fa8: DecompressPointer r1
    //     0xc11fa8: add             x1, x1, HEAP, lsl #32
    // 0xc11fac: cmp             w1, NULL
    // 0xc11fb0: b.eq            #0xc12110
    // 0xc11fb4: LoadField: r0 = r1->field_b
    //     0xc11fb4: ldur            w0, [x1, #0xb]
    // 0xc11fb8: DecompressPointer r0
    //     0xc11fb8: add             x0, x0, HEAP, lsl #32
    // 0xc11fbc: LoadField: r1 = r0->field_7
    //     0xc11fbc: ldur            w1, [x0, #7]
    // 0xc11fc0: cbz             w1, #0xc12088
    // 0xc11fc4: ldur            x1, [fp, #-0x10]
    // 0xc11fc8: r0 = of()
    //     0xc11fc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc11fcc: LoadField: r1 = r0->field_87
    //     0xc11fcc: ldur            w1, [x0, #0x87]
    // 0xc11fd0: DecompressPointer r1
    //     0xc11fd0: add             x1, x1, HEAP, lsl #32
    // 0xc11fd4: LoadField: r0 = r1->field_7
    //     0xc11fd4: ldur            w0, [x1, #7]
    // 0xc11fd8: DecompressPointer r0
    //     0xc11fd8: add             x0, x0, HEAP, lsl #32
    // 0xc11fdc: stur            x0, [fp, #-8]
    // 0xc11fe0: r1 = Instance_Color
    //     0xc11fe0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc11fe4: d0 = 0.700000
    //     0xc11fe4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc11fe8: ldr             d0, [x17, #0xf48]
    // 0xc11fec: r0 = withOpacity()
    //     0xc11fec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc11ff0: r16 = 12.000000
    //     0xc11ff0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc11ff4: ldr             x16, [x16, #0x9e8]
    // 0xc11ff8: stp             x16, x0, [SP, #8]
    // 0xc11ffc: r16 = Instance_TextDecoration
    //     0xc11ffc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xc12000: ldr             x16, [x16, #0x10]
    // 0xc12004: str             x16, [SP]
    // 0xc12008: ldur            x1, [fp, #-8]
    // 0xc1200c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xc1200c: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xc12010: ldr             x4, [x4, #0x7c8]
    // 0xc12014: r0 = copyWith()
    //     0xc12014: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc12018: stur            x0, [fp, #-8]
    // 0xc1201c: r0 = Text()
    //     0xc1201c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc12020: mov             x1, x0
    // 0xc12024: r0 = "View Chart"
    //     0xc12024: add             x0, PP, #0x43, lsl #12  ; [pp+0x436d0] "View Chart"
    //     0xc12028: ldr             x0, [x0, #0x6d0]
    // 0xc1202c: stur            x1, [fp, #-0x10]
    // 0xc12030: StoreField: r1->field_b = r0
    //     0xc12030: stur            w0, [x1, #0xb]
    // 0xc12034: ldur            x0, [fp, #-8]
    // 0xc12038: StoreField: r1->field_13 = r0
    //     0xc12038: stur            w0, [x1, #0x13]
    // 0xc1203c: r0 = Instance_TextAlign
    //     0xc1203c: ldr             x0, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xc12040: StoreField: r1->field_1b = r0
    //     0xc12040: stur            w0, [x1, #0x1b]
    // 0xc12044: r0 = Align()
    //     0xc12044: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc12048: mov             x1, x0
    // 0xc1204c: r0 = Instance_Alignment
    //     0xc1204c: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xc12050: ldr             x0, [x0, #0xa78]
    // 0xc12054: stur            x1, [fp, #-8]
    // 0xc12058: StoreField: r1->field_f = r0
    //     0xc12058: stur            w0, [x1, #0xf]
    // 0xc1205c: ldur            x0, [fp, #-0x10]
    // 0xc12060: StoreField: r1->field_b = r0
    //     0xc12060: stur            w0, [x1, #0xb]
    // 0xc12064: r0 = Padding()
    //     0xc12064: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc12068: mov             x1, x0
    // 0xc1206c: r0 = Instance_EdgeInsets
    //     0xc1206c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52088] Obj!EdgeInsets@d58ac1
    //     0xc12070: ldr             x0, [x0, #0x88]
    // 0xc12074: StoreField: r1->field_f = r0
    //     0xc12074: stur            w0, [x1, #0xf]
    // 0xc12078: ldur            x0, [fp, #-8]
    // 0xc1207c: StoreField: r1->field_b = r0
    //     0xc1207c: stur            w0, [x1, #0xb]
    // 0xc12080: mov             x0, x1
    // 0xc12084: b               #0xc120a0
    // 0xc12088: r0 = Container()
    //     0xc12088: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1208c: mov             x1, x0
    // 0xc12090: stur            x0, [fp, #-8]
    // 0xc12094: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc12094: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc12098: r0 = Container()
    //     0xc12098: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc1209c: ldur            x0, [fp, #-8]
    // 0xc120a0: stur            x0, [fp, #-8]
    // 0xc120a4: r0 = InkWell()
    //     0xc120a4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc120a8: mov             x3, x0
    // 0xc120ac: ldur            x0, [fp, #-8]
    // 0xc120b0: stur            x3, [fp, #-0x10]
    // 0xc120b4: StoreField: r3->field_b = r0
    //     0xc120b4: stur            w0, [x3, #0xb]
    // 0xc120b8: ldur            x2, [fp, #-0x18]
    // 0xc120bc: r1 = Function '<anonymous closure>':.
    //     0xc120bc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52090] AnonymousClosure: (0xc12134), in [package:customer_app/app/presentation/views/line/product_detail/widgets/view_size_widget.dart] _ViewSizeWidgetState::build (0xc11f64)
    //     0xc120c0: ldr             x1, [x1, #0x90]
    // 0xc120c4: r0 = AllocateClosure()
    //     0xc120c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc120c8: mov             x1, x0
    // 0xc120cc: ldur            x0, [fp, #-0x10]
    // 0xc120d0: StoreField: r0->field_f = r1
    //     0xc120d0: stur            w1, [x0, #0xf]
    // 0xc120d4: r1 = true
    //     0xc120d4: add             x1, NULL, #0x20  ; true
    // 0xc120d8: StoreField: r0->field_43 = r1
    //     0xc120d8: stur            w1, [x0, #0x43]
    // 0xc120dc: r2 = Instance_BoxShape
    //     0xc120dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc120e0: ldr             x2, [x2, #0x80]
    // 0xc120e4: StoreField: r0->field_47 = r2
    //     0xc120e4: stur            w2, [x0, #0x47]
    // 0xc120e8: StoreField: r0->field_6f = r1
    //     0xc120e8: stur            w1, [x0, #0x6f]
    // 0xc120ec: r2 = false
    //     0xc120ec: add             x2, NULL, #0x30  ; false
    // 0xc120f0: StoreField: r0->field_73 = r2
    //     0xc120f0: stur            w2, [x0, #0x73]
    // 0xc120f4: StoreField: r0->field_83 = r1
    //     0xc120f4: stur            w1, [x0, #0x83]
    // 0xc120f8: StoreField: r0->field_7b = r2
    //     0xc120f8: stur            w2, [x0, #0x7b]
    // 0xc120fc: LeaveFrame
    //     0xc120fc: mov             SP, fp
    //     0xc12100: ldp             fp, lr, [SP], #0x10
    // 0xc12104: ret
    //     0xc12104: ret             
    // 0xc12108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc12108: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1210c: b               #0xc11f8c
    // 0xc12110: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc12110: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc12134, size: 0x78
    // 0xc12134: EnterFrame
    //     0xc12134: stp             fp, lr, [SP, #-0x10]!
    //     0xc12138: mov             fp, SP
    // 0xc1213c: AllocStack(0x18)
    //     0xc1213c: sub             SP, SP, #0x18
    // 0xc12140: SetupParameters()
    //     0xc12140: ldr             x0, [fp, #0x10]
    //     0xc12144: ldur            w2, [x0, #0x17]
    //     0xc12148: add             x2, x2, HEAP, lsl #32
    //     0xc1214c: stur            x2, [fp, #-8]
    // 0xc12150: CheckStackOverflow
    //     0xc12150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc12154: cmp             SP, x16
    //     0xc12158: b.ls            #0xc121a4
    // 0xc1215c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc1215c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc12160: ldr             x0, [x0, #0x1c80]
    //     0xc12164: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc12168: cmp             w0, w16
    //     0xc1216c: b.ne            #0xc12178
    //     0xc12170: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc12174: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc12178: ldur            x2, [fp, #-8]
    // 0xc1217c: r1 = Function '<anonymous closure>':.
    //     0xc1217c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52098] AnonymousClosure: (0xc121ac), in [package:customer_app/app/presentation/views/line/product_detail/widgets/view_size_widget.dart] _ViewSizeWidgetState::build (0xc11f64)
    //     0xc12180: ldr             x1, [x1, #0x98]
    // 0xc12184: r0 = AllocateClosure()
    //     0xc12184: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc12188: stp             x0, NULL, [SP]
    // 0xc1218c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc1218c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc12190: r0 = GetNavigation.to()
    //     0xc12190: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xc12194: r0 = Null
    //     0xc12194: mov             x0, NULL
    // 0xc12198: LeaveFrame
    //     0xc12198: mov             SP, fp
    //     0xc1219c: ldp             fp, lr, [SP], #0x10
    // 0xc121a0: ret
    //     0xc121a0: ret             
    // 0xc121a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc121a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc121a8: b               #0xc1215c
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0xc121ac, size: 0x58
    // 0xc121ac: EnterFrame
    //     0xc121ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc121b0: mov             fp, SP
    // 0xc121b4: AllocStack(0x8)
    //     0xc121b4: sub             SP, SP, #8
    // 0xc121b8: SetupParameters()
    //     0xc121b8: ldr             x0, [fp, #0x10]
    //     0xc121bc: ldur            w1, [x0, #0x17]
    //     0xc121c0: add             x1, x1, HEAP, lsl #32
    // 0xc121c4: LoadField: r0 = r1->field_f
    //     0xc121c4: ldur            w0, [x1, #0xf]
    // 0xc121c8: DecompressPointer r0
    //     0xc121c8: add             x0, x0, HEAP, lsl #32
    // 0xc121cc: LoadField: r1 = r0->field_b
    //     0xc121cc: ldur            w1, [x0, #0xb]
    // 0xc121d0: DecompressPointer r1
    //     0xc121d0: add             x1, x1, HEAP, lsl #32
    // 0xc121d4: cmp             w1, NULL
    // 0xc121d8: b.eq            #0xc12200
    // 0xc121dc: LoadField: r0 = r1->field_b
    //     0xc121dc: ldur            w0, [x1, #0xb]
    // 0xc121e0: DecompressPointer r0
    //     0xc121e0: add             x0, x0, HEAP, lsl #32
    // 0xc121e4: stur            x0, [fp, #-8]
    // 0xc121e8: r0 = ViewSizeChart()
    //     0xc121e8: bl              #0xbbb748  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0xc121ec: ldur            x1, [fp, #-8]
    // 0xc121f0: StoreField: r0->field_b = r1
    //     0xc121f0: stur            w1, [x0, #0xb]
    // 0xc121f4: LeaveFrame
    //     0xc121f4: mov             SP, fp
    //     0xc121f8: ldp             fp, lr, [SP], #0x10
    // 0xc121fc: ret
    //     0xc121fc: ret             
    // 0xc12200: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc12200: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3957, size: 0x10, field offset: 0xc
//   const constructor, 
class ViewSizeWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81480, size: 0x24
    // 0xc81480: EnterFrame
    //     0xc81480: stp             fp, lr, [SP, #-0x10]!
    //     0xc81484: mov             fp, SP
    // 0xc81488: mov             x0, x1
    // 0xc8148c: r1 = <ViewSizeWidget>
    //     0xc8148c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48260] TypeArguments: <ViewSizeWidget>
    //     0xc81490: ldr             x1, [x1, #0x260]
    // 0xc81494: r0 = _ViewSizeWidgetState()
    //     0xc81494: bl              #0xc814a4  ; Allocate_ViewSizeWidgetStateStub -> _ViewSizeWidgetState (size=0x14)
    // 0xc81498: LeaveFrame
    //     0xc81498: mov             SP, fp
    //     0xc8149c: ldp             fp, lr, [SP], #0x10
    // 0xc814a0: ret
    //     0xc814a0: ret             
  }
}
