// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart

// class id: 1049291, size: 0x8
class :: {
}

// class id: 3415, size: 0x1c, field offset: 0x14
class _OrderCardState extends State<dynamic> {

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x80376c, size: 0x1f8
    // 0x80376c: EnterFrame
    //     0x80376c: stp             fp, lr, [SP, #-0x10]!
    //     0x803770: mov             fp, SP
    // 0x803774: AllocStack(0x20)
    //     0x803774: sub             SP, SP, #0x20
    // 0x803778: SetupParameters(_OrderCardState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x803778: mov             x4, x1
    //     0x80377c: mov             x3, x2
    //     0x803780: stur            x1, [fp, #-8]
    //     0x803784: stur            x2, [fp, #-0x10]
    // 0x803788: CheckStackOverflow
    //     0x803788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x80378c: cmp             SP, x16
    //     0x803790: b.ls            #0x803958
    // 0x803794: mov             x0, x3
    // 0x803798: r2 = Null
    //     0x803798: mov             x2, NULL
    // 0x80379c: r1 = Null
    //     0x80379c: mov             x1, NULL
    // 0x8037a0: r4 = 60
    //     0x8037a0: movz            x4, #0x3c
    // 0x8037a4: branchIfSmi(r0, 0x8037b0)
    //     0x8037a4: tbz             w0, #0, #0x8037b0
    // 0x8037a8: r4 = LoadClassIdInstr(r0)
    //     0x8037a8: ldur            x4, [x0, #-1]
    //     0x8037ac: ubfx            x4, x4, #0xc, #0x14
    // 0x8037b0: r17 = 4152
    //     0x8037b0: movz            x17, #0x1038
    // 0x8037b4: cmp             x4, x17
    // 0x8037b8: b.eq            #0x8037d0
    // 0x8037bc: r8 = OrderCard
    //     0x8037bc: add             x8, PP, #0x6a, lsl #12  ; [pp+0x6ad70] Type: OrderCard
    //     0x8037c0: ldr             x8, [x8, #0xd70]
    // 0x8037c4: r3 = Null
    //     0x8037c4: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6ad78] Null
    //     0x8037c8: ldr             x3, [x3, #0xd78]
    // 0x8037cc: r0 = OrderCard()
    //     0x8037cc: bl              #0x803964  ; IsType_OrderCard_Stub
    // 0x8037d0: ldur            x3, [fp, #-8]
    // 0x8037d4: LoadField: r2 = r3->field_7
    //     0x8037d4: ldur            w2, [x3, #7]
    // 0x8037d8: DecompressPointer r2
    //     0x8037d8: add             x2, x2, HEAP, lsl #32
    // 0x8037dc: ldur            x0, [fp, #-0x10]
    // 0x8037e0: r1 = Null
    //     0x8037e0: mov             x1, NULL
    // 0x8037e4: cmp             w2, NULL
    // 0x8037e8: b.eq            #0x80380c
    // 0x8037ec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8037ec: ldur            w4, [x2, #0x17]
    // 0x8037f0: DecompressPointer r4
    //     0x8037f0: add             x4, x4, HEAP, lsl #32
    // 0x8037f4: r8 = X0 bound StatefulWidget
    //     0x8037f4: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x8037f8: ldr             x8, [x8, #0x7a0]
    // 0x8037fc: LoadField: r9 = r4->field_7
    //     0x8037fc: ldur            x9, [x4, #7]
    // 0x803800: r3 = Null
    //     0x803800: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6ad88] Null
    //     0x803804: ldr             x3, [x3, #0xd88]
    // 0x803808: blr             x9
    // 0x80380c: ldur            x2, [fp, #-8]
    // 0x803810: LoadField: r0 = r2->field_b
    //     0x803810: ldur            w0, [x2, #0xb]
    // 0x803814: DecompressPointer r0
    //     0x803814: add             x0, x0, HEAP, lsl #32
    // 0x803818: cmp             w0, NULL
    // 0x80381c: b.eq            #0x803960
    // 0x803820: LoadField: r1 = r0->field_b
    //     0x803820: ldur            w1, [x0, #0xb]
    // 0x803824: DecompressPointer r1
    //     0x803824: add             x1, x1, HEAP, lsl #32
    // 0x803828: LoadField: r3 = r1->field_8b
    //     0x803828: ldur            w3, [x1, #0x8b]
    // 0x80382c: DecompressPointer r3
    //     0x80382c: add             x3, x3, HEAP, lsl #32
    // 0x803830: cmp             w3, NULL
    // 0x803834: b.ne            #0x803840
    // 0x803838: r4 = Null
    //     0x803838: mov             x4, NULL
    // 0x80383c: b               #0x80385c
    // 0x803840: LoadField: r4 = r3->field_f
    //     0x803840: ldur            x4, [x3, #0xf]
    // 0x803844: r0 = BoxInt64Instr(r4)
    //     0x803844: sbfiz           x0, x4, #1, #0x1f
    //     0x803848: cmp             x4, x0, asr #1
    //     0x80384c: b.eq            #0x803858
    //     0x803850: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x803854: stur            x4, [x0, #7]
    // 0x803858: mov             x4, x0
    // 0x80385c: ldur            x0, [fp, #-0x10]
    // 0x803860: LoadField: r1 = r0->field_b
    //     0x803860: ldur            w1, [x0, #0xb]
    // 0x803864: DecompressPointer r1
    //     0x803864: add             x1, x1, HEAP, lsl #32
    // 0x803868: LoadField: r0 = r1->field_8b
    //     0x803868: ldur            w0, [x1, #0x8b]
    // 0x80386c: DecompressPointer r0
    //     0x80386c: add             x0, x0, HEAP, lsl #32
    // 0x803870: cmp             w0, NULL
    // 0x803874: b.ne            #0x803880
    // 0x803878: r0 = Null
    //     0x803878: mov             x0, NULL
    // 0x80387c: b               #0x803898
    // 0x803880: LoadField: r5 = r0->field_f
    //     0x803880: ldur            x5, [x0, #0xf]
    // 0x803884: r0 = BoxInt64Instr(r5)
    //     0x803884: sbfiz           x0, x5, #1, #0x1f
    //     0x803888: cmp             x5, x0, asr #1
    //     0x80388c: b.eq            #0x803898
    //     0x803890: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x803894: stur            x5, [x0, #7]
    // 0x803898: cmp             w4, w0
    // 0x80389c: b.eq            #0x803948
    // 0x8038a0: and             w16, w4, w0
    // 0x8038a4: branchIfSmi(r16, 0x8038d8)
    //     0x8038a4: tbz             w16, #0, #0x8038d8
    // 0x8038a8: r16 = LoadClassIdInstr(r4)
    //     0x8038a8: ldur            x16, [x4, #-1]
    //     0x8038ac: ubfx            x16, x16, #0xc, #0x14
    // 0x8038b0: cmp             x16, #0x3d
    // 0x8038b4: b.ne            #0x8038d8
    // 0x8038b8: r16 = LoadClassIdInstr(r0)
    //     0x8038b8: ldur            x16, [x0, #-1]
    //     0x8038bc: ubfx            x16, x16, #0xc, #0x14
    // 0x8038c0: cmp             x16, #0x3d
    // 0x8038c4: b.ne            #0x8038d8
    // 0x8038c8: LoadField: r16 = r4->field_7
    //     0x8038c8: ldur            x16, [x4, #7]
    // 0x8038cc: LoadField: r17 = r0->field_7
    //     0x8038cc: ldur            x17, [x0, #7]
    // 0x8038d0: cmp             x16, x17
    // 0x8038d4: b.eq            #0x803948
    // 0x8038d8: cmp             w3, NULL
    // 0x8038dc: b.ne            #0x8038e8
    // 0x8038e0: r0 = Null
    //     0x8038e0: mov             x0, NULL
    // 0x8038e4: b               #0x803900
    // 0x8038e8: LoadField: r4 = r3->field_f
    //     0x8038e8: ldur            x4, [x3, #0xf]
    // 0x8038ec: r0 = BoxInt64Instr(r4)
    //     0x8038ec: sbfiz           x0, x4, #1, #0x1f
    //     0x8038f0: cmp             x4, x0, asr #1
    //     0x8038f4: b.eq            #0x803900
    //     0x8038f8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8038fc: stur            x4, [x0, #7]
    // 0x803900: cmp             w0, NULL
    // 0x803904: b.ne            #0x803910
    // 0x803908: r3 = 0
    //     0x803908: movz            x3, #0
    // 0x80390c: b               #0x803920
    // 0x803910: r1 = LoadInt32Instr(r0)
    //     0x803910: sbfx            x1, x0, #1, #0x1f
    //     0x803914: tbz             w0, #0, #0x80391c
    //     0x803918: ldur            x1, [x0, #7]
    // 0x80391c: mov             x3, x1
    // 0x803920: r0 = BoxInt64Instr(r3)
    //     0x803920: sbfiz           x0, x3, #1, #0x1f
    //     0x803924: cmp             x3, x0, asr #1
    //     0x803928: b.eq            #0x803934
    //     0x80392c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x803930: stur            x3, [x0, #7]
    // 0x803934: stp             x0, NULL, [SP]
    // 0x803938: r0 = _Double.fromInteger()
    //     0x803938: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x80393c: LoadField: d0 = r0->field_7
    //     0x80393c: ldur            d0, [x0, #7]
    // 0x803940: ldur            x1, [fp, #-8]
    // 0x803944: StoreField: r1->field_13 = d0
    //     0x803944: stur            d0, [x1, #0x13]
    // 0x803948: r0 = Null
    //     0x803948: mov             x0, NULL
    // 0x80394c: LeaveFrame
    //     0x80394c: mov             SP, fp
    //     0x803950: ldp             fp, lr, [SP], #0x10
    // 0x803954: ret
    //     0x803954: ret             
    // 0x803958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x803958: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x80395c: b               #0x803794
    // 0x803960: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x803960: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xafa414, size: 0x1970
    // 0xafa414: EnterFrame
    //     0xafa414: stp             fp, lr, [SP, #-0x10]!
    //     0xafa418: mov             fp, SP
    // 0xafa41c: AllocStack(0xa8)
    //     0xafa41c: sub             SP, SP, #0xa8
    // 0xafa420: SetupParameters(_OrderCardState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xafa420: mov             x0, x1
    //     0xafa424: stur            x1, [fp, #-8]
    //     0xafa428: mov             x1, x2
    //     0xafa42c: stur            x2, [fp, #-0x10]
    // 0xafa430: CheckStackOverflow
    //     0xafa430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafa434: cmp             SP, x16
    //     0xafa438: b.ls            #0xafbd18
    // 0xafa43c: r1 = 1
    //     0xafa43c: movz            x1, #0x1
    // 0xafa440: r0 = AllocateContext()
    //     0xafa440: bl              #0x16f6108  ; AllocateContextStub
    // 0xafa444: mov             x1, x0
    // 0xafa448: ldur            x0, [fp, #-8]
    // 0xafa44c: stur            x1, [fp, #-0x18]
    // 0xafa450: StoreField: r1->field_f = r0
    //     0xafa450: stur            w0, [x1, #0xf]
    // 0xafa454: r0 = Radius()
    //     0xafa454: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafa458: d0 = 15.000000
    //     0xafa458: fmov            d0, #15.00000000
    // 0xafa45c: stur            x0, [fp, #-0x20]
    // 0xafa460: StoreField: r0->field_7 = d0
    //     0xafa460: stur            d0, [x0, #7]
    // 0xafa464: StoreField: r0->field_f = d0
    //     0xafa464: stur            d0, [x0, #0xf]
    // 0xafa468: r0 = BorderRadius()
    //     0xafa468: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafa46c: mov             x1, x0
    // 0xafa470: ldur            x0, [fp, #-0x20]
    // 0xafa474: stur            x1, [fp, #-0x28]
    // 0xafa478: StoreField: r1->field_7 = r0
    //     0xafa478: stur            w0, [x1, #7]
    // 0xafa47c: StoreField: r1->field_b = r0
    //     0xafa47c: stur            w0, [x1, #0xb]
    // 0xafa480: StoreField: r1->field_f = r0
    //     0xafa480: stur            w0, [x1, #0xf]
    // 0xafa484: StoreField: r1->field_13 = r0
    //     0xafa484: stur            w0, [x1, #0x13]
    // 0xafa488: r0 = RoundedRectangleBorder()
    //     0xafa488: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xafa48c: mov             x2, x0
    // 0xafa490: ldur            x0, [fp, #-0x28]
    // 0xafa494: stur            x2, [fp, #-0x30]
    // 0xafa498: StoreField: r2->field_b = r0
    //     0xafa498: stur            w0, [x2, #0xb]
    // 0xafa49c: r0 = Instance_BorderSide
    //     0xafa49c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xafa4a0: ldr             x0, [x0, #0xe20]
    // 0xafa4a4: StoreField: r2->field_7 = r0
    //     0xafa4a4: stur            w0, [x2, #7]
    // 0xafa4a8: ldur            x0, [fp, #-8]
    // 0xafa4ac: LoadField: r1 = r0->field_b
    //     0xafa4ac: ldur            w1, [x0, #0xb]
    // 0xafa4b0: DecompressPointer r1
    //     0xafa4b0: add             x1, x1, HEAP, lsl #32
    // 0xafa4b4: cmp             w1, NULL
    // 0xafa4b8: b.eq            #0xafbd20
    // 0xafa4bc: LoadField: r3 = r1->field_b
    //     0xafa4bc: ldur            w3, [x1, #0xb]
    // 0xafa4c0: DecompressPointer r3
    //     0xafa4c0: add             x3, x3, HEAP, lsl #32
    // 0xafa4c4: LoadField: r1 = r3->field_5b
    //     0xafa4c4: ldur            w1, [x3, #0x5b]
    // 0xafa4c8: DecompressPointer r1
    //     0xafa4c8: add             x1, x1, HEAP, lsl #32
    // 0xafa4cc: cmp             w1, NULL
    // 0xafa4d0: b.ne            #0xafa4dc
    // 0xafa4d4: r3 = false
    //     0xafa4d4: add             x3, NULL, #0x30  ; false
    // 0xafa4d8: b               #0xafa4e0
    // 0xafa4dc: mov             x3, x1
    // 0xafa4e0: ldur            x1, [fp, #-0x10]
    // 0xafa4e4: stur            x3, [fp, #-0x20]
    // 0xafa4e8: r0 = of()
    //     0xafa4e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafa4ec: LoadField: r1 = r0->field_5b
    //     0xafa4ec: ldur            w1, [x0, #0x5b]
    // 0xafa4f0: DecompressPointer r1
    //     0xafa4f0: add             x1, x1, HEAP, lsl #32
    // 0xafa4f4: stur            x1, [fp, #-0x28]
    // 0xafa4f8: r0 = Radius()
    //     0xafa4f8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafa4fc: d0 = 12.000000
    //     0xafa4fc: fmov            d0, #12.00000000
    // 0xafa500: stur            x0, [fp, #-0x38]
    // 0xafa504: StoreField: r0->field_7 = d0
    //     0xafa504: stur            d0, [x0, #7]
    // 0xafa508: StoreField: r0->field_f = d0
    //     0xafa508: stur            d0, [x0, #0xf]
    // 0xafa50c: r0 = BorderRadius()
    //     0xafa50c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafa510: mov             x1, x0
    // 0xafa514: ldur            x0, [fp, #-0x38]
    // 0xafa518: stur            x1, [fp, #-0x40]
    // 0xafa51c: StoreField: r1->field_7 = r0
    //     0xafa51c: stur            w0, [x1, #7]
    // 0xafa520: StoreField: r1->field_b = r0
    //     0xafa520: stur            w0, [x1, #0xb]
    // 0xafa524: StoreField: r1->field_f = r0
    //     0xafa524: stur            w0, [x1, #0xf]
    // 0xafa528: StoreField: r1->field_13 = r0
    //     0xafa528: stur            w0, [x1, #0x13]
    // 0xafa52c: r0 = BoxDecoration()
    //     0xafa52c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xafa530: mov             x2, x0
    // 0xafa534: ldur            x0, [fp, #-0x28]
    // 0xafa538: stur            x2, [fp, #-0x38]
    // 0xafa53c: StoreField: r2->field_7 = r0
    //     0xafa53c: stur            w0, [x2, #7]
    // 0xafa540: ldur            x0, [fp, #-0x40]
    // 0xafa544: StoreField: r2->field_13 = r0
    //     0xafa544: stur            w0, [x2, #0x13]
    // 0xafa548: r0 = Instance_BoxShape
    //     0xafa548: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafa54c: ldr             x0, [x0, #0x80]
    // 0xafa550: StoreField: r2->field_23 = r0
    //     0xafa550: stur            w0, [x2, #0x23]
    // 0xafa554: ldur            x1, [fp, #-0x10]
    // 0xafa558: r0 = of()
    //     0xafa558: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafa55c: LoadField: r1 = r0->field_87
    //     0xafa55c: ldur            w1, [x0, #0x87]
    // 0xafa560: DecompressPointer r1
    //     0xafa560: add             x1, x1, HEAP, lsl #32
    // 0xafa564: LoadField: r0 = r1->field_7
    //     0xafa564: ldur            w0, [x1, #7]
    // 0xafa568: DecompressPointer r0
    //     0xafa568: add             x0, x0, HEAP, lsl #32
    // 0xafa56c: r16 = 12.000000
    //     0xafa56c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafa570: ldr             x16, [x16, #0x9e8]
    // 0xafa574: r30 = Instance_Color
    //     0xafa574: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xafa578: stp             lr, x16, [SP]
    // 0xafa57c: mov             x1, x0
    // 0xafa580: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafa580: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafa584: ldr             x4, [x4, #0xaa0]
    // 0xafa588: r0 = copyWith()
    //     0xafa588: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafa58c: stur            x0, [fp, #-0x28]
    // 0xafa590: r0 = Text()
    //     0xafa590: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafa594: mov             x1, x0
    // 0xafa598: r0 = "Exchange"
    //     0xafa598: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ee8] "Exchange"
    //     0xafa59c: ldr             x0, [x0, #0xee8]
    // 0xafa5a0: stur            x1, [fp, #-0x40]
    // 0xafa5a4: StoreField: r1->field_b = r0
    //     0xafa5a4: stur            w0, [x1, #0xb]
    // 0xafa5a8: ldur            x0, [fp, #-0x28]
    // 0xafa5ac: StoreField: r1->field_13 = r0
    //     0xafa5ac: stur            w0, [x1, #0x13]
    // 0xafa5b0: r0 = Container()
    //     0xafa5b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafa5b4: stur            x0, [fp, #-0x28]
    // 0xafa5b8: r16 = Instance_EdgeInsets
    //     0xafa5b8: add             x16, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xafa5bc: ldr             x16, [x16, #0xdb0]
    // 0xafa5c0: ldur            lr, [fp, #-0x38]
    // 0xafa5c4: stp             lr, x16, [SP, #8]
    // 0xafa5c8: ldur            x16, [fp, #-0x40]
    // 0xafa5cc: str             x16, [SP]
    // 0xafa5d0: mov             x1, x0
    // 0xafa5d4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xafa5d4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xafa5d8: ldr             x4, [x4, #0x610]
    // 0xafa5dc: r0 = Container()
    //     0xafa5dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafa5e0: r0 = Visibility()
    //     0xafa5e0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafa5e4: mov             x1, x0
    // 0xafa5e8: ldur            x0, [fp, #-0x28]
    // 0xafa5ec: stur            x1, [fp, #-0x38]
    // 0xafa5f0: StoreField: r1->field_b = r0
    //     0xafa5f0: stur            w0, [x1, #0xb]
    // 0xafa5f4: r0 = Instance_SizedBox
    //     0xafa5f4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafa5f8: StoreField: r1->field_f = r0
    //     0xafa5f8: stur            w0, [x1, #0xf]
    // 0xafa5fc: ldur            x2, [fp, #-0x20]
    // 0xafa600: StoreField: r1->field_13 = r2
    //     0xafa600: stur            w2, [x1, #0x13]
    // 0xafa604: r2 = false
    //     0xafa604: add             x2, NULL, #0x30  ; false
    // 0xafa608: ArrayStore: r1[0] = r2  ; List_4
    //     0xafa608: stur            w2, [x1, #0x17]
    // 0xafa60c: StoreField: r1->field_1b = r2
    //     0xafa60c: stur            w2, [x1, #0x1b]
    // 0xafa610: StoreField: r1->field_1f = r2
    //     0xafa610: stur            w2, [x1, #0x1f]
    // 0xafa614: StoreField: r1->field_23 = r2
    //     0xafa614: stur            w2, [x1, #0x23]
    // 0xafa618: StoreField: r1->field_27 = r2
    //     0xafa618: stur            w2, [x1, #0x27]
    // 0xafa61c: StoreField: r1->field_2b = r2
    //     0xafa61c: stur            w2, [x1, #0x2b]
    // 0xafa620: r0 = Radius()
    //     0xafa620: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafa624: d0 = 12.000000
    //     0xafa624: fmov            d0, #12.00000000
    // 0xafa628: stur            x0, [fp, #-0x20]
    // 0xafa62c: StoreField: r0->field_7 = d0
    //     0xafa62c: stur            d0, [x0, #7]
    // 0xafa630: StoreField: r0->field_f = d0
    //     0xafa630: stur            d0, [x0, #0xf]
    // 0xafa634: r0 = BorderRadius()
    //     0xafa634: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafa638: mov             x3, x0
    // 0xafa63c: ldur            x0, [fp, #-0x20]
    // 0xafa640: stur            x3, [fp, #-0x28]
    // 0xafa644: StoreField: r3->field_7 = r0
    //     0xafa644: stur            w0, [x3, #7]
    // 0xafa648: StoreField: r3->field_b = r0
    //     0xafa648: stur            w0, [x3, #0xb]
    // 0xafa64c: StoreField: r3->field_f = r0
    //     0xafa64c: stur            w0, [x3, #0xf]
    // 0xafa650: StoreField: r3->field_13 = r0
    //     0xafa650: stur            w0, [x3, #0x13]
    // 0xafa654: ldur            x0, [fp, #-8]
    // 0xafa658: LoadField: r1 = r0->field_b
    //     0xafa658: ldur            w1, [x0, #0xb]
    // 0xafa65c: DecompressPointer r1
    //     0xafa65c: add             x1, x1, HEAP, lsl #32
    // 0xafa660: cmp             w1, NULL
    // 0xafa664: b.eq            #0xafbd24
    // 0xafa668: LoadField: r2 = r1->field_b
    //     0xafa668: ldur            w2, [x1, #0xb]
    // 0xafa66c: DecompressPointer r2
    //     0xafa66c: add             x2, x2, HEAP, lsl #32
    // 0xafa670: LoadField: r1 = r2->field_b
    //     0xafa670: ldur            w1, [x2, #0xb]
    // 0xafa674: DecompressPointer r1
    //     0xafa674: add             x1, x1, HEAP, lsl #32
    // 0xafa678: cmp             w1, NULL
    // 0xafa67c: b.ne            #0xafa688
    // 0xafa680: r4 = ""
    //     0xafa680: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafa684: b               #0xafa68c
    // 0xafa688: mov             x4, x1
    // 0xafa68c: stur            x4, [fp, #-0x20]
    // 0xafa690: r1 = Function '<anonymous closure>':.
    //     0xafa690: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac90] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xafa694: ldr             x1, [x1, #0xc90]
    // 0xafa698: r2 = Null
    //     0xafa698: mov             x2, NULL
    // 0xafa69c: r0 = AllocateClosure()
    //     0xafa69c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafa6a0: r1 = Function '<anonymous closure>':.
    //     0xafa6a0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ac98] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xafa6a4: ldr             x1, [x1, #0xc98]
    // 0xafa6a8: r2 = Null
    //     0xafa6a8: mov             x2, NULL
    // 0xafa6ac: stur            x0, [fp, #-0x40]
    // 0xafa6b0: r0 = AllocateClosure()
    //     0xafa6b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafa6b4: stur            x0, [fp, #-0x48]
    // 0xafa6b8: r0 = CachedNetworkImage()
    //     0xafa6b8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xafa6bc: stur            x0, [fp, #-0x50]
    // 0xafa6c0: r16 = 56.000000
    //     0xafa6c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafa6c4: ldr             x16, [x16, #0xb78]
    // 0xafa6c8: r30 = 56.000000
    //     0xafa6c8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xafa6cc: ldr             lr, [lr, #0xb78]
    // 0xafa6d0: stp             lr, x16, [SP, #0x18]
    // 0xafa6d4: r16 = Instance_BoxFit
    //     0xafa6d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xafa6d8: ldr             x16, [x16, #0x118]
    // 0xafa6dc: ldur            lr, [fp, #-0x40]
    // 0xafa6e0: stp             lr, x16, [SP, #8]
    // 0xafa6e4: ldur            x16, [fp, #-0x48]
    // 0xafa6e8: str             x16, [SP]
    // 0xafa6ec: mov             x1, x0
    // 0xafa6f0: ldur            x2, [fp, #-0x20]
    // 0xafa6f4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xafa6f4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xafa6f8: ldr             x4, [x4, #0xc28]
    // 0xafa6fc: r0 = CachedNetworkImage()
    //     0xafa6fc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xafa700: r0 = ClipRRect()
    //     0xafa700: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xafa704: mov             x2, x0
    // 0xafa708: ldur            x0, [fp, #-0x28]
    // 0xafa70c: stur            x2, [fp, #-0x20]
    // 0xafa710: StoreField: r2->field_f = r0
    //     0xafa710: stur            w0, [x2, #0xf]
    // 0xafa714: r0 = Instance_Clip
    //     0xafa714: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xafa718: ldr             x0, [x0, #0x138]
    // 0xafa71c: ArrayStore: r2[0] = r0  ; List_4
    //     0xafa71c: stur            w0, [x2, #0x17]
    // 0xafa720: ldur            x0, [fp, #-0x50]
    // 0xafa724: StoreField: r2->field_b = r0
    //     0xafa724: stur            w0, [x2, #0xb]
    // 0xafa728: ldur            x0, [fp, #-8]
    // 0xafa72c: LoadField: r1 = r0->field_b
    //     0xafa72c: ldur            w1, [x0, #0xb]
    // 0xafa730: DecompressPointer r1
    //     0xafa730: add             x1, x1, HEAP, lsl #32
    // 0xafa734: cmp             w1, NULL
    // 0xafa738: b.eq            #0xafbd28
    // 0xafa73c: LoadField: r3 = r1->field_b
    //     0xafa73c: ldur            w3, [x1, #0xb]
    // 0xafa740: DecompressPointer r3
    //     0xafa740: add             x3, x3, HEAP, lsl #32
    // 0xafa744: LoadField: r1 = r3->field_f
    //     0xafa744: ldur            w1, [x3, #0xf]
    // 0xafa748: DecompressPointer r1
    //     0xafa748: add             x1, x1, HEAP, lsl #32
    // 0xafa74c: cmp             w1, NULL
    // 0xafa750: b.ne            #0xafa758
    // 0xafa754: r1 = ""
    //     0xafa754: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafa758: r0 = capitalizeFirstWord()
    //     0xafa758: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xafa75c: ldur            x1, [fp, #-0x10]
    // 0xafa760: stur            x0, [fp, #-0x28]
    // 0xafa764: r0 = of()
    //     0xafa764: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafa768: LoadField: r1 = r0->field_87
    //     0xafa768: ldur            w1, [x0, #0x87]
    // 0xafa76c: DecompressPointer r1
    //     0xafa76c: add             x1, x1, HEAP, lsl #32
    // 0xafa770: LoadField: r0 = r1->field_7
    //     0xafa770: ldur            w0, [x1, #7]
    // 0xafa774: DecompressPointer r0
    //     0xafa774: add             x0, x0, HEAP, lsl #32
    // 0xafa778: stur            x0, [fp, #-0x40]
    // 0xafa77c: r1 = Instance_Color
    //     0xafa77c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafa780: d0 = 0.700000
    //     0xafa780: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xafa784: ldr             d0, [x17, #0xf48]
    // 0xafa788: r0 = withOpacity()
    //     0xafa788: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xafa78c: r16 = 12.000000
    //     0xafa78c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafa790: ldr             x16, [x16, #0x9e8]
    // 0xafa794: stp             x16, x0, [SP]
    // 0xafa798: ldur            x1, [fp, #-0x40]
    // 0xafa79c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xafa79c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xafa7a0: ldr             x4, [x4, #0x9b8]
    // 0xafa7a4: r0 = copyWith()
    //     0xafa7a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafa7a8: stur            x0, [fp, #-0x40]
    // 0xafa7ac: r0 = Text()
    //     0xafa7ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafa7b0: mov             x1, x0
    // 0xafa7b4: ldur            x0, [fp, #-0x28]
    // 0xafa7b8: stur            x1, [fp, #-0x48]
    // 0xafa7bc: StoreField: r1->field_b = r0
    //     0xafa7bc: stur            w0, [x1, #0xb]
    // 0xafa7c0: ldur            x0, [fp, #-0x40]
    // 0xafa7c4: StoreField: r1->field_13 = r0
    //     0xafa7c4: stur            w0, [x1, #0x13]
    // 0xafa7c8: r0 = Instance_TextOverflow
    //     0xafa7c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xafa7cc: ldr             x0, [x0, #0xe10]
    // 0xafa7d0: StoreField: r1->field_2b = r0
    //     0xafa7d0: stur            w0, [x1, #0x2b]
    // 0xafa7d4: r0 = 2
    //     0xafa7d4: movz            x0, #0x2
    // 0xafa7d8: StoreField: r1->field_37 = r0
    //     0xafa7d8: stur            w0, [x1, #0x37]
    // 0xafa7dc: ldur            x2, [fp, #-8]
    // 0xafa7e0: LoadField: r0 = r2->field_b
    //     0xafa7e0: ldur            w0, [x2, #0xb]
    // 0xafa7e4: DecompressPointer r0
    //     0xafa7e4: add             x0, x0, HEAP, lsl #32
    // 0xafa7e8: cmp             w0, NULL
    // 0xafa7ec: b.eq            #0xafbd2c
    // 0xafa7f0: LoadField: r3 = r0->field_b
    //     0xafa7f0: ldur            w3, [x0, #0xb]
    // 0xafa7f4: DecompressPointer r3
    //     0xafa7f4: add             x3, x3, HEAP, lsl #32
    // 0xafa7f8: LoadField: r0 = r3->field_47
    //     0xafa7f8: ldur            w0, [x3, #0x47]
    // 0xafa7fc: DecompressPointer r0
    //     0xafa7fc: add             x0, x0, HEAP, lsl #32
    // 0xafa800: r3 = LoadClassIdInstr(r0)
    //     0xafa800: ldur            x3, [x0, #-1]
    //     0xafa804: ubfx            x3, x3, #0xc, #0x14
    // 0xafa808: r16 = "size"
    //     0xafa808: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xafa80c: ldr             x16, [x16, #0x9c0]
    // 0xafa810: stp             x16, x0, [SP]
    // 0xafa814: mov             x0, x3
    // 0xafa818: mov             lr, x0
    // 0xafa81c: ldr             lr, [x21, lr, lsl #3]
    // 0xafa820: blr             lr
    // 0xafa824: tbnz            w0, #4, #0xafa87c
    // 0xafa828: ldur            x0, [fp, #-8]
    // 0xafa82c: r1 = Null
    //     0xafa82c: mov             x1, NULL
    // 0xafa830: r2 = 4
    //     0xafa830: movz            x2, #0x4
    // 0xafa834: r0 = AllocateArray()
    //     0xafa834: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafa838: r16 = "Size :  "
    //     0xafa838: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0xafa83c: ldr             x16, [x16, #0x758]
    // 0xafa840: StoreField: r0->field_f = r16
    //     0xafa840: stur            w16, [x0, #0xf]
    // 0xafa844: ldur            x1, [fp, #-8]
    // 0xafa848: LoadField: r2 = r1->field_b
    //     0xafa848: ldur            w2, [x1, #0xb]
    // 0xafa84c: DecompressPointer r2
    //     0xafa84c: add             x2, x2, HEAP, lsl #32
    // 0xafa850: cmp             w2, NULL
    // 0xafa854: b.eq            #0xafbd30
    // 0xafa858: LoadField: r3 = r2->field_b
    //     0xafa858: ldur            w3, [x2, #0xb]
    // 0xafa85c: DecompressPointer r3
    //     0xafa85c: add             x3, x3, HEAP, lsl #32
    // 0xafa860: LoadField: r2 = r3->field_1f
    //     0xafa860: ldur            w2, [x3, #0x1f]
    // 0xafa864: DecompressPointer r2
    //     0xafa864: add             x2, x2, HEAP, lsl #32
    // 0xafa868: StoreField: r0->field_13 = r2
    //     0xafa868: stur            w2, [x0, #0x13]
    // 0xafa86c: str             x0, [SP]
    // 0xafa870: r0 = _interpolate()
    //     0xafa870: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xafa874: mov             x2, x0
    // 0xafa878: b               #0xafa8cc
    // 0xafa87c: ldur            x0, [fp, #-8]
    // 0xafa880: r1 = Null
    //     0xafa880: mov             x1, NULL
    // 0xafa884: r2 = 4
    //     0xafa884: movz            x2, #0x4
    // 0xafa888: r0 = AllocateArray()
    //     0xafa888: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafa88c: r16 = "Variant : "
    //     0xafa88c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0xafa890: ldr             x16, [x16, #0x768]
    // 0xafa894: StoreField: r0->field_f = r16
    //     0xafa894: stur            w16, [x0, #0xf]
    // 0xafa898: ldur            x1, [fp, #-8]
    // 0xafa89c: LoadField: r2 = r1->field_b
    //     0xafa89c: ldur            w2, [x1, #0xb]
    // 0xafa8a0: DecompressPointer r2
    //     0xafa8a0: add             x2, x2, HEAP, lsl #32
    // 0xafa8a4: cmp             w2, NULL
    // 0xafa8a8: b.eq            #0xafbd34
    // 0xafa8ac: LoadField: r3 = r2->field_b
    //     0xafa8ac: ldur            w3, [x2, #0xb]
    // 0xafa8b0: DecompressPointer r3
    //     0xafa8b0: add             x3, x3, HEAP, lsl #32
    // 0xafa8b4: LoadField: r2 = r3->field_1f
    //     0xafa8b4: ldur            w2, [x3, #0x1f]
    // 0xafa8b8: DecompressPointer r2
    //     0xafa8b8: add             x2, x2, HEAP, lsl #32
    // 0xafa8bc: StoreField: r0->field_13 = r2
    //     0xafa8bc: stur            w2, [x0, #0x13]
    // 0xafa8c0: str             x0, [SP]
    // 0xafa8c4: r0 = _interpolate()
    //     0xafa8c4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xafa8c8: mov             x2, x0
    // 0xafa8cc: ldur            x0, [fp, #-8]
    // 0xafa8d0: ldur            x1, [fp, #-0x10]
    // 0xafa8d4: stur            x2, [fp, #-0x28]
    // 0xafa8d8: r0 = of()
    //     0xafa8d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafa8dc: LoadField: r1 = r0->field_87
    //     0xafa8dc: ldur            w1, [x0, #0x87]
    // 0xafa8e0: DecompressPointer r1
    //     0xafa8e0: add             x1, x1, HEAP, lsl #32
    // 0xafa8e4: LoadField: r0 = r1->field_2b
    //     0xafa8e4: ldur            w0, [x1, #0x2b]
    // 0xafa8e8: DecompressPointer r0
    //     0xafa8e8: add             x0, x0, HEAP, lsl #32
    // 0xafa8ec: stur            x0, [fp, #-0x40]
    // 0xafa8f0: r1 = Instance_Color
    //     0xafa8f0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafa8f4: d0 = 0.700000
    //     0xafa8f4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xafa8f8: ldr             d0, [x17, #0xf48]
    // 0xafa8fc: r0 = withOpacity()
    //     0xafa8fc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xafa900: r16 = 12.000000
    //     0xafa900: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafa904: ldr             x16, [x16, #0x9e8]
    // 0xafa908: stp             x0, x16, [SP]
    // 0xafa90c: ldur            x1, [fp, #-0x40]
    // 0xafa910: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafa910: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafa914: ldr             x4, [x4, #0xaa0]
    // 0xafa918: r0 = copyWith()
    //     0xafa918: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafa91c: stur            x0, [fp, #-0x40]
    // 0xafa920: r0 = Text()
    //     0xafa920: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafa924: mov             x1, x0
    // 0xafa928: ldur            x0, [fp, #-0x28]
    // 0xafa92c: stur            x1, [fp, #-0x50]
    // 0xafa930: StoreField: r1->field_b = r0
    //     0xafa930: stur            w0, [x1, #0xb]
    // 0xafa934: ldur            x0, [fp, #-0x40]
    // 0xafa938: StoreField: r1->field_13 = r0
    //     0xafa938: stur            w0, [x1, #0x13]
    // 0xafa93c: r0 = Padding()
    //     0xafa93c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafa940: mov             x1, x0
    // 0xafa944: r0 = Instance_EdgeInsets
    //     0xafa944: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xafa948: ldr             x0, [x0, #0x770]
    // 0xafa94c: stur            x1, [fp, #-0x28]
    // 0xafa950: StoreField: r1->field_f = r0
    //     0xafa950: stur            w0, [x1, #0xf]
    // 0xafa954: ldur            x2, [fp, #-0x50]
    // 0xafa958: StoreField: r1->field_b = r2
    //     0xafa958: stur            w2, [x1, #0xb]
    // 0xafa95c: r0 = Visibility()
    //     0xafa95c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafa960: mov             x2, x0
    // 0xafa964: ldur            x0, [fp, #-0x28]
    // 0xafa968: stur            x2, [fp, #-0x40]
    // 0xafa96c: StoreField: r2->field_b = r0
    //     0xafa96c: stur            w0, [x2, #0xb]
    // 0xafa970: r0 = Instance_SizedBox
    //     0xafa970: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafa974: StoreField: r2->field_f = r0
    //     0xafa974: stur            w0, [x2, #0xf]
    // 0xafa978: r3 = true
    //     0xafa978: add             x3, NULL, #0x20  ; true
    // 0xafa97c: StoreField: r2->field_13 = r3
    //     0xafa97c: stur            w3, [x2, #0x13]
    // 0xafa980: r4 = false
    //     0xafa980: add             x4, NULL, #0x30  ; false
    // 0xafa984: ArrayStore: r2[0] = r4  ; List_4
    //     0xafa984: stur            w4, [x2, #0x17]
    // 0xafa988: StoreField: r2->field_1b = r4
    //     0xafa988: stur            w4, [x2, #0x1b]
    // 0xafa98c: StoreField: r2->field_1f = r4
    //     0xafa98c: stur            w4, [x2, #0x1f]
    // 0xafa990: StoreField: r2->field_23 = r4
    //     0xafa990: stur            w4, [x2, #0x23]
    // 0xafa994: StoreField: r2->field_27 = r4
    //     0xafa994: stur            w4, [x2, #0x27]
    // 0xafa998: StoreField: r2->field_2b = r4
    //     0xafa998: stur            w4, [x2, #0x2b]
    // 0xafa99c: ldur            x5, [fp, #-8]
    // 0xafa9a0: LoadField: r1 = r5->field_b
    //     0xafa9a0: ldur            w1, [x5, #0xb]
    // 0xafa9a4: DecompressPointer r1
    //     0xafa9a4: add             x1, x1, HEAP, lsl #32
    // 0xafa9a8: cmp             w1, NULL
    // 0xafa9ac: b.eq            #0xafbd38
    // 0xafa9b0: LoadField: r6 = r1->field_b
    //     0xafa9b0: ldur            w6, [x1, #0xb]
    // 0xafa9b4: DecompressPointer r6
    //     0xafa9b4: add             x6, x6, HEAP, lsl #32
    // 0xafa9b8: LoadField: r1 = r6->field_13
    //     0xafa9b8: ldur            w1, [x6, #0x13]
    // 0xafa9bc: DecompressPointer r1
    //     0xafa9bc: add             x1, x1, HEAP, lsl #32
    // 0xafa9c0: cmp             w1, NULL
    // 0xafa9c4: b.ne            #0xafa9d0
    // 0xafa9c8: r6 = ""
    //     0xafa9c8: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafa9cc: b               #0xafa9d4
    // 0xafa9d0: mov             x6, x1
    // 0xafa9d4: ldur            x1, [fp, #-0x10]
    // 0xafa9d8: stur            x6, [fp, #-0x28]
    // 0xafa9dc: r0 = of()
    //     0xafa9dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafa9e0: LoadField: r1 = r0->field_87
    //     0xafa9e0: ldur            w1, [x0, #0x87]
    // 0xafa9e4: DecompressPointer r1
    //     0xafa9e4: add             x1, x1, HEAP, lsl #32
    // 0xafa9e8: LoadField: r2 = r1->field_7
    //     0xafa9e8: ldur            w2, [x1, #7]
    // 0xafa9ec: DecompressPointer r2
    //     0xafa9ec: add             x2, x2, HEAP, lsl #32
    // 0xafa9f0: ldur            x1, [fp, #-8]
    // 0xafa9f4: stur            x2, [fp, #-0x50]
    // 0xafa9f8: LoadField: r0 = r1->field_b
    //     0xafa9f8: ldur            w0, [x1, #0xb]
    // 0xafa9fc: DecompressPointer r0
    //     0xafa9fc: add             x0, x0, HEAP, lsl #32
    // 0xafaa00: cmp             w0, NULL
    // 0xafaa04: b.eq            #0xafbd3c
    // 0xafaa08: LoadField: r3 = r0->field_b
    //     0xafaa08: ldur            w3, [x0, #0xb]
    // 0xafaa0c: DecompressPointer r3
    //     0xafaa0c: add             x3, x3, HEAP, lsl #32
    // 0xafaa10: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xafaa10: ldur            w0, [x3, #0x17]
    // 0xafaa14: DecompressPointer r0
    //     0xafaa14: add             x0, x0, HEAP, lsl #32
    // 0xafaa18: r3 = LoadClassIdInstr(r0)
    //     0xafaa18: ldur            x3, [x0, #-1]
    //     0xafaa1c: ubfx            x3, x3, #0xc, #0x14
    // 0xafaa20: r16 = "pending"
    //     0xafaa20: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec0] "pending"
    //     0xafaa24: ldr             x16, [x16, #0xec0]
    // 0xafaa28: stp             x16, x0, [SP]
    // 0xafaa2c: mov             x0, x3
    // 0xafaa30: mov             lr, x0
    // 0xafaa34: ldr             lr, [x21, lr, lsl #3]
    // 0xafaa38: blr             lr
    // 0xafaa3c: tbnz            w0, #4, #0xafaa4c
    // 0xafaa40: r1 = Instance_MaterialColor
    //     0xafaa40: add             x1, PP, #0x35, lsl #12  ; [pp+0x35ec8] Obj!MaterialColor@d6bda1
    //     0xafaa44: ldr             x1, [x1, #0xec8]
    // 0xafaa48: b               #0xafaafc
    // 0xafaa4c: ldur            x1, [fp, #-8]
    // 0xafaa50: LoadField: r0 = r1->field_b
    //     0xafaa50: ldur            w0, [x1, #0xb]
    // 0xafaa54: DecompressPointer r0
    //     0xafaa54: add             x0, x0, HEAP, lsl #32
    // 0xafaa58: cmp             w0, NULL
    // 0xafaa5c: b.eq            #0xafbd40
    // 0xafaa60: LoadField: r2 = r0->field_b
    //     0xafaa60: ldur            w2, [x0, #0xb]
    // 0xafaa64: DecompressPointer r2
    //     0xafaa64: add             x2, x2, HEAP, lsl #32
    // 0xafaa68: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xafaa68: ldur            w0, [x2, #0x17]
    // 0xafaa6c: DecompressPointer r0
    //     0xafaa6c: add             x0, x0, HEAP, lsl #32
    // 0xafaa70: r2 = LoadClassIdInstr(r0)
    //     0xafaa70: ldur            x2, [x0, #-1]
    //     0xafaa74: ubfx            x2, x2, #0xc, #0x14
    // 0xafaa78: r16 = "cancel_initiated"
    //     0xafaa78: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ed0] "cancel_initiated"
    //     0xafaa7c: ldr             x16, [x16, #0xed0]
    // 0xafaa80: stp             x16, x0, [SP]
    // 0xafaa84: mov             x0, x2
    // 0xafaa88: mov             lr, x0
    // 0xafaa8c: ldr             lr, [x21, lr, lsl #3]
    // 0xafaa90: blr             lr
    // 0xafaa94: tbz             w0, #4, #0xafaae4
    // 0xafaa98: ldur            x1, [fp, #-8]
    // 0xafaa9c: LoadField: r0 = r1->field_b
    //     0xafaa9c: ldur            w0, [x1, #0xb]
    // 0xafaaa0: DecompressPointer r0
    //     0xafaaa0: add             x0, x0, HEAP, lsl #32
    // 0xafaaa4: cmp             w0, NULL
    // 0xafaaa8: b.eq            #0xafbd44
    // 0xafaaac: LoadField: r2 = r0->field_b
    //     0xafaaac: ldur            w2, [x0, #0xb]
    // 0xafaab0: DecompressPointer r2
    //     0xafaab0: add             x2, x2, HEAP, lsl #32
    // 0xafaab4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xafaab4: ldur            w0, [x2, #0x17]
    // 0xafaab8: DecompressPointer r0
    //     0xafaab8: add             x0, x0, HEAP, lsl #32
    // 0xafaabc: r2 = LoadClassIdInstr(r0)
    //     0xafaabc: ldur            x2, [x0, #-1]
    //     0xafaac0: ubfx            x2, x2, #0xc, #0x14
    // 0xafaac4: r16 = "failure"
    //     0xafaac4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ebe8] "failure"
    //     0xafaac8: ldr             x16, [x16, #0xbe8]
    // 0xafaacc: stp             x16, x0, [SP]
    // 0xafaad0: mov             x0, x2
    // 0xafaad4: mov             lr, x0
    // 0xafaad8: ldr             lr, [x21, lr, lsl #3]
    // 0xafaadc: blr             lr
    // 0xafaae0: tbnz            w0, #4, #0xafaaf0
    // 0xafaae4: r0 = Instance_MaterialAccentColor
    //     0xafaae4: add             x0, PP, #0x35, lsl #12  ; [pp+0x35ed8] Obj!MaterialAccentColor@d6bca1
    //     0xafaae8: ldr             x0, [x0, #0xed8]
    // 0xafaaec: b               #0xafaaf8
    // 0xafaaf0: r0 = Instance_Color
    //     0xafaaf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xafaaf4: ldr             x0, [x0, #0x858]
    // 0xafaaf8: mov             x1, x0
    // 0xafaafc: ldur            x0, [fp, #-8]
    // 0xafab00: ldur            x2, [fp, #-0x28]
    // 0xafab04: r16 = 12.000000
    //     0xafab04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafab08: ldr             x16, [x16, #0x9e8]
    // 0xafab0c: stp             x1, x16, [SP]
    // 0xafab10: ldur            x1, [fp, #-0x50]
    // 0xafab14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafab14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafab18: ldr             x4, [x4, #0xaa0]
    // 0xafab1c: r0 = copyWith()
    //     0xafab1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafab20: stur            x0, [fp, #-0x50]
    // 0xafab24: r0 = Text()
    //     0xafab24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafab28: mov             x1, x0
    // 0xafab2c: ldur            x0, [fp, #-0x28]
    // 0xafab30: stur            x1, [fp, #-0x58]
    // 0xafab34: StoreField: r1->field_b = r0
    //     0xafab34: stur            w0, [x1, #0xb]
    // 0xafab38: ldur            x0, [fp, #-0x50]
    // 0xafab3c: StoreField: r1->field_13 = r0
    //     0xafab3c: stur            w0, [x1, #0x13]
    // 0xafab40: r0 = Padding()
    //     0xafab40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafab44: mov             x1, x0
    // 0xafab48: r0 = Instance_EdgeInsets
    //     0xafab48: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xafab4c: ldr             x0, [x0, #0x770]
    // 0xafab50: stur            x1, [fp, #-0x28]
    // 0xafab54: StoreField: r1->field_f = r0
    //     0xafab54: stur            w0, [x1, #0xf]
    // 0xafab58: ldur            x0, [fp, #-0x58]
    // 0xafab5c: StoreField: r1->field_b = r0
    //     0xafab5c: stur            w0, [x1, #0xb]
    // 0xafab60: r0 = Visibility()
    //     0xafab60: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafab64: mov             x3, x0
    // 0xafab68: ldur            x0, [fp, #-0x28]
    // 0xafab6c: stur            x3, [fp, #-0x50]
    // 0xafab70: StoreField: r3->field_b = r0
    //     0xafab70: stur            w0, [x3, #0xb]
    // 0xafab74: r0 = Instance_SizedBox
    //     0xafab74: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafab78: StoreField: r3->field_f = r0
    //     0xafab78: stur            w0, [x3, #0xf]
    // 0xafab7c: r4 = true
    //     0xafab7c: add             x4, NULL, #0x20  ; true
    // 0xafab80: StoreField: r3->field_13 = r4
    //     0xafab80: stur            w4, [x3, #0x13]
    // 0xafab84: r5 = false
    //     0xafab84: add             x5, NULL, #0x30  ; false
    // 0xafab88: ArrayStore: r3[0] = r5  ; List_4
    //     0xafab88: stur            w5, [x3, #0x17]
    // 0xafab8c: StoreField: r3->field_1b = r5
    //     0xafab8c: stur            w5, [x3, #0x1b]
    // 0xafab90: StoreField: r3->field_1f = r5
    //     0xafab90: stur            w5, [x3, #0x1f]
    // 0xafab94: StoreField: r3->field_23 = r5
    //     0xafab94: stur            w5, [x3, #0x23]
    // 0xafab98: StoreField: r3->field_27 = r5
    //     0xafab98: stur            w5, [x3, #0x27]
    // 0xafab9c: StoreField: r3->field_2b = r5
    //     0xafab9c: stur            w5, [x3, #0x2b]
    // 0xafaba0: ldur            x6, [fp, #-8]
    // 0xafaba4: LoadField: r1 = r6->field_b
    //     0xafaba4: ldur            w1, [x6, #0xb]
    // 0xafaba8: DecompressPointer r1
    //     0xafaba8: add             x1, x1, HEAP, lsl #32
    // 0xafabac: cmp             w1, NULL
    // 0xafabb0: b.eq            #0xafbd48
    // 0xafabb4: LoadField: r2 = r1->field_b
    //     0xafabb4: ldur            w2, [x1, #0xb]
    // 0xafabb8: DecompressPointer r2
    //     0xafabb8: add             x2, x2, HEAP, lsl #32
    // 0xafabbc: LoadField: r1 = r2->field_3b
    //     0xafabbc: ldur            w1, [x2, #0x3b]
    // 0xafabc0: DecompressPointer r1
    //     0xafabc0: add             x1, x1, HEAP, lsl #32
    // 0xafabc4: cmp             w1, NULL
    // 0xafabc8: b.ne            #0xafabe0
    // 0xafabcc: r1 = <ProductCustomisation>
    //     0xafabcc: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xafabd0: ldr             x1, [x1, #0x370]
    // 0xafabd4: r2 = 0
    //     0xafabd4: movz            x2, #0
    // 0xafabd8: r0 = AllocateArray()
    //     0xafabd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafabdc: mov             x1, x0
    // 0xafabe0: ldur            x2, [fp, #-8]
    // 0xafabe4: r0 = LoadClassIdInstr(r1)
    //     0xafabe4: ldur            x0, [x1, #-1]
    //     0xafabe8: ubfx            x0, x0, #0xc, #0x14
    // 0xafabec: r0 = GDT[cid_x0 + 0xe517]()
    //     0xafabec: movz            x17, #0xe517
    //     0xafabf0: add             lr, x0, x17
    //     0xafabf4: ldr             lr, [x21, lr, lsl #3]
    //     0xafabf8: blr             lr
    // 0xafabfc: ldur            x1, [fp, #-0x10]
    // 0xafac00: stur            x0, [fp, #-0x28]
    // 0xafac04: r0 = of()
    //     0xafac04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafac08: LoadField: r1 = r0->field_87
    //     0xafac08: ldur            w1, [x0, #0x87]
    // 0xafac0c: DecompressPointer r1
    //     0xafac0c: add             x1, x1, HEAP, lsl #32
    // 0xafac10: LoadField: r0 = r1->field_2b
    //     0xafac10: ldur            w0, [x1, #0x2b]
    // 0xafac14: DecompressPointer r0
    //     0xafac14: add             x0, x0, HEAP, lsl #32
    // 0xafac18: r16 = 14.000000
    //     0xafac18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xafac1c: ldr             x16, [x16, #0x1d8]
    // 0xafac20: r30 = Instance_Color
    //     0xafac20: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafac24: stp             lr, x16, [SP]
    // 0xafac28: mov             x1, x0
    // 0xafac2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafac2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafac30: ldr             x4, [x4, #0xaa0]
    // 0xafac34: r0 = copyWith()
    //     0xafac34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafac38: stur            x0, [fp, #-0x58]
    // 0xafac3c: r0 = Text()
    //     0xafac3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafac40: mov             x1, x0
    // 0xafac44: r0 = "Customised"
    //     0xafac44: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0xafac48: ldr             x0, [x0, #0xd88]
    // 0xafac4c: stur            x1, [fp, #-0x60]
    // 0xafac50: StoreField: r1->field_b = r0
    //     0xafac50: stur            w0, [x1, #0xb]
    // 0xafac54: ldur            x0, [fp, #-0x58]
    // 0xafac58: StoreField: r1->field_13 = r0
    //     0xafac58: stur            w0, [x1, #0x13]
    // 0xafac5c: r0 = Padding()
    //     0xafac5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafac60: mov             x1, x0
    // 0xafac64: r0 = Instance_EdgeInsets
    //     0xafac64: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xafac68: ldr             x0, [x0, #0x668]
    // 0xafac6c: stur            x1, [fp, #-0x58]
    // 0xafac70: StoreField: r1->field_f = r0
    //     0xafac70: stur            w0, [x1, #0xf]
    // 0xafac74: ldur            x2, [fp, #-0x60]
    // 0xafac78: StoreField: r1->field_b = r2
    //     0xafac78: stur            w2, [x1, #0xb]
    // 0xafac7c: r0 = Visibility()
    //     0xafac7c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafac80: mov             x2, x0
    // 0xafac84: ldur            x0, [fp, #-0x58]
    // 0xafac88: stur            x2, [fp, #-0x60]
    // 0xafac8c: StoreField: r2->field_b = r0
    //     0xafac8c: stur            w0, [x2, #0xb]
    // 0xafac90: r0 = Instance_SizedBox
    //     0xafac90: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafac94: StoreField: r2->field_f = r0
    //     0xafac94: stur            w0, [x2, #0xf]
    // 0xafac98: ldur            x1, [fp, #-0x28]
    // 0xafac9c: StoreField: r2->field_13 = r1
    //     0xafac9c: stur            w1, [x2, #0x13]
    // 0xafaca0: r3 = false
    //     0xafaca0: add             x3, NULL, #0x30  ; false
    // 0xafaca4: ArrayStore: r2[0] = r3  ; List_4
    //     0xafaca4: stur            w3, [x2, #0x17]
    // 0xafaca8: StoreField: r2->field_1b = r3
    //     0xafaca8: stur            w3, [x2, #0x1b]
    // 0xafacac: StoreField: r2->field_1f = r3
    //     0xafacac: stur            w3, [x2, #0x1f]
    // 0xafacb0: StoreField: r2->field_23 = r3
    //     0xafacb0: stur            w3, [x2, #0x23]
    // 0xafacb4: StoreField: r2->field_27 = r3
    //     0xafacb4: stur            w3, [x2, #0x27]
    // 0xafacb8: StoreField: r2->field_2b = r3
    //     0xafacb8: stur            w3, [x2, #0x2b]
    // 0xafacbc: ldur            x4, [fp, #-8]
    // 0xafacc0: LoadField: r1 = r4->field_b
    //     0xafacc0: ldur            w1, [x4, #0xb]
    // 0xafacc4: DecompressPointer r1
    //     0xafacc4: add             x1, x1, HEAP, lsl #32
    // 0xafacc8: cmp             w1, NULL
    // 0xafaccc: b.eq            #0xafbd4c
    // 0xafacd0: LoadField: r5 = r1->field_b
    //     0xafacd0: ldur            w5, [x1, #0xb]
    // 0xafacd4: DecompressPointer r5
    //     0xafacd4: add             x5, x5, HEAP, lsl #32
    // 0xafacd8: LoadField: r1 = r5->field_23
    //     0xafacd8: ldur            w1, [x5, #0x23]
    // 0xafacdc: DecompressPointer r1
    //     0xafacdc: add             x1, x1, HEAP, lsl #32
    // 0xaface0: cmp             w1, NULL
    // 0xaface4: b.ne            #0xafacf0
    // 0xaface8: r5 = ""
    //     0xaface8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafacec: b               #0xafacf4
    // 0xafacf0: mov             x5, x1
    // 0xafacf4: ldur            x1, [fp, #-0x10]
    // 0xafacf8: stur            x5, [fp, #-0x28]
    // 0xafacfc: r0 = of()
    //     0xafacfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafad00: LoadField: r1 = r0->field_87
    //     0xafad00: ldur            w1, [x0, #0x87]
    // 0xafad04: DecompressPointer r1
    //     0xafad04: add             x1, x1, HEAP, lsl #32
    // 0xafad08: LoadField: r0 = r1->field_2b
    //     0xafad08: ldur            w0, [x1, #0x2b]
    // 0xafad0c: DecompressPointer r0
    //     0xafad0c: add             x0, x0, HEAP, lsl #32
    // 0xafad10: r16 = Instance_Color
    //     0xafad10: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafad14: r30 = 12.000000
    //     0xafad14: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafad18: ldr             lr, [lr, #0x9e8]
    // 0xafad1c: stp             lr, x16, [SP]
    // 0xafad20: mov             x1, x0
    // 0xafad24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xafad24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xafad28: ldr             x4, [x4, #0x9b8]
    // 0xafad2c: r0 = copyWith()
    //     0xafad2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafad30: stur            x0, [fp, #-0x58]
    // 0xafad34: r0 = Text()
    //     0xafad34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafad38: mov             x1, x0
    // 0xafad3c: ldur            x0, [fp, #-0x28]
    // 0xafad40: stur            x1, [fp, #-0x68]
    // 0xafad44: StoreField: r1->field_b = r0
    //     0xafad44: stur            w0, [x1, #0xb]
    // 0xafad48: ldur            x0, [fp, #-0x58]
    // 0xafad4c: StoreField: r1->field_13 = r0
    //     0xafad4c: stur            w0, [x1, #0x13]
    // 0xafad50: r0 = Container()
    //     0xafad50: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafad54: stur            x0, [fp, #-0x28]
    // 0xafad58: r16 = Instance_BoxDecoration
    //     0xafad58: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aca0] Obj!BoxDecoration@d64b01
    //     0xafad5c: ldr             x16, [x16, #0xca0]
    // 0xafad60: ldur            lr, [fp, #-0x68]
    // 0xafad64: stp             lr, x16, [SP]
    // 0xafad68: mov             x1, x0
    // 0xafad6c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xafad6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xafad70: ldr             x4, [x4, #0x88]
    // 0xafad74: r0 = Container()
    //     0xafad74: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafad78: ldur            x0, [fp, #-8]
    // 0xafad7c: LoadField: r1 = r0->field_b
    //     0xafad7c: ldur            w1, [x0, #0xb]
    // 0xafad80: DecompressPointer r1
    //     0xafad80: add             x1, x1, HEAP, lsl #32
    // 0xafad84: cmp             w1, NULL
    // 0xafad88: b.eq            #0xafbd50
    // 0xafad8c: LoadField: r2 = r1->field_b
    //     0xafad8c: ldur            w2, [x1, #0xb]
    // 0xafad90: DecompressPointer r2
    //     0xafad90: add             x2, x2, HEAP, lsl #32
    // 0xafad94: LoadField: r1 = r2->field_27
    //     0xafad94: ldur            w1, [x2, #0x27]
    // 0xafad98: DecompressPointer r1
    //     0xafad98: add             x1, x1, HEAP, lsl #32
    // 0xafad9c: cmp             w1, NULL
    // 0xafada0: b.ne            #0xafadac
    // 0xafada4: r9 = ""
    //     0xafada4: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafada8: b               #0xafadb0
    // 0xafadac: mov             x9, x1
    // 0xafadb0: ldur            x8, [fp, #-0x38]
    // 0xafadb4: ldur            x7, [fp, #-0x20]
    // 0xafadb8: ldur            x6, [fp, #-0x48]
    // 0xafadbc: ldur            x5, [fp, #-0x40]
    // 0xafadc0: ldur            x4, [fp, #-0x50]
    // 0xafadc4: ldur            x3, [fp, #-0x60]
    // 0xafadc8: ldur            x2, [fp, #-0x28]
    // 0xafadcc: ldur            x1, [fp, #-0x10]
    // 0xafadd0: stur            x9, [fp, #-0x58]
    // 0xafadd4: r0 = of()
    //     0xafadd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafadd8: LoadField: r1 = r0->field_87
    //     0xafadd8: ldur            w1, [x0, #0x87]
    // 0xafaddc: DecompressPointer r1
    //     0xafaddc: add             x1, x1, HEAP, lsl #32
    // 0xafade0: LoadField: r0 = r1->field_2b
    //     0xafade0: ldur            w0, [x1, #0x2b]
    // 0xafade4: DecompressPointer r0
    //     0xafade4: add             x0, x0, HEAP, lsl #32
    // 0xafade8: r16 = Instance_Color
    //     0xafade8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafadec: r30 = 12.000000
    //     0xafadec: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafadf0: ldr             lr, [lr, #0x9e8]
    // 0xafadf4: stp             lr, x16, [SP]
    // 0xafadf8: mov             x1, x0
    // 0xafadfc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xafadfc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xafae00: ldr             x4, [x4, #0x9b8]
    // 0xafae04: r0 = copyWith()
    //     0xafae04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafae08: stur            x0, [fp, #-0x68]
    // 0xafae0c: r0 = Text()
    //     0xafae0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafae10: mov             x3, x0
    // 0xafae14: ldur            x0, [fp, #-0x58]
    // 0xafae18: stur            x3, [fp, #-0x70]
    // 0xafae1c: StoreField: r3->field_b = r0
    //     0xafae1c: stur            w0, [x3, #0xb]
    // 0xafae20: ldur            x0, [fp, #-0x68]
    // 0xafae24: StoreField: r3->field_13 = r0
    //     0xafae24: stur            w0, [x3, #0x13]
    // 0xafae28: r1 = Null
    //     0xafae28: mov             x1, NULL
    // 0xafae2c: r2 = 4
    //     0xafae2c: movz            x2, #0x4
    // 0xafae30: r0 = AllocateArray()
    //     0xafae30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafae34: mov             x2, x0
    // 0xafae38: ldur            x0, [fp, #-0x28]
    // 0xafae3c: stur            x2, [fp, #-0x58]
    // 0xafae40: StoreField: r2->field_f = r0
    //     0xafae40: stur            w0, [x2, #0xf]
    // 0xafae44: ldur            x0, [fp, #-0x70]
    // 0xafae48: StoreField: r2->field_13 = r0
    //     0xafae48: stur            w0, [x2, #0x13]
    // 0xafae4c: r1 = <Widget>
    //     0xafae4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafae50: r0 = AllocateGrowableArray()
    //     0xafae50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafae54: mov             x1, x0
    // 0xafae58: ldur            x0, [fp, #-0x58]
    // 0xafae5c: stur            x1, [fp, #-0x28]
    // 0xafae60: StoreField: r1->field_f = r0
    //     0xafae60: stur            w0, [x1, #0xf]
    // 0xafae64: r2 = 4
    //     0xafae64: movz            x2, #0x4
    // 0xafae68: StoreField: r1->field_b = r2
    //     0xafae68: stur            w2, [x1, #0xb]
    // 0xafae6c: r0 = Row()
    //     0xafae6c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafae70: mov             x1, x0
    // 0xafae74: r0 = Instance_Axis
    //     0xafae74: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafae78: stur            x1, [fp, #-0x58]
    // 0xafae7c: StoreField: r1->field_f = r0
    //     0xafae7c: stur            w0, [x1, #0xf]
    // 0xafae80: r2 = Instance_MainAxisAlignment
    //     0xafae80: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xafae84: ldr             x2, [x2, #0xa8]
    // 0xafae88: StoreField: r1->field_13 = r2
    //     0xafae88: stur            w2, [x1, #0x13]
    // 0xafae8c: r3 = Instance_MainAxisSize
    //     0xafae8c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafae90: ldr             x3, [x3, #0xa10]
    // 0xafae94: ArrayStore: r1[0] = r3  ; List_4
    //     0xafae94: stur            w3, [x1, #0x17]
    // 0xafae98: r4 = Instance_CrossAxisAlignment
    //     0xafae98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafae9c: ldr             x4, [x4, #0xa18]
    // 0xafaea0: StoreField: r1->field_1b = r4
    //     0xafaea0: stur            w4, [x1, #0x1b]
    // 0xafaea4: r5 = Instance_VerticalDirection
    //     0xafaea4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafaea8: ldr             x5, [x5, #0xa20]
    // 0xafaeac: StoreField: r1->field_23 = r5
    //     0xafaeac: stur            w5, [x1, #0x23]
    // 0xafaeb0: r6 = Instance_Clip
    //     0xafaeb0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafaeb4: ldr             x6, [x6, #0x38]
    // 0xafaeb8: StoreField: r1->field_2b = r6
    //     0xafaeb8: stur            w6, [x1, #0x2b]
    // 0xafaebc: StoreField: r1->field_2f = rZR
    //     0xafaebc: stur            xzr, [x1, #0x2f]
    // 0xafaec0: ldur            x7, [fp, #-0x28]
    // 0xafaec4: StoreField: r1->field_b = r7
    //     0xafaec4: stur            w7, [x1, #0xb]
    // 0xafaec8: r0 = Visibility()
    //     0xafaec8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafaecc: mov             x3, x0
    // 0xafaed0: ldur            x0, [fp, #-0x58]
    // 0xafaed4: stur            x3, [fp, #-0x28]
    // 0xafaed8: StoreField: r3->field_b = r0
    //     0xafaed8: stur            w0, [x3, #0xb]
    // 0xafaedc: r0 = Instance_SizedBox
    //     0xafaedc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafaee0: StoreField: r3->field_f = r0
    //     0xafaee0: stur            w0, [x3, #0xf]
    // 0xafaee4: r4 = false
    //     0xafaee4: add             x4, NULL, #0x30  ; false
    // 0xafaee8: StoreField: r3->field_13 = r4
    //     0xafaee8: stur            w4, [x3, #0x13]
    // 0xafaeec: ArrayStore: r3[0] = r4  ; List_4
    //     0xafaeec: stur            w4, [x3, #0x17]
    // 0xafaef0: StoreField: r3->field_1b = r4
    //     0xafaef0: stur            w4, [x3, #0x1b]
    // 0xafaef4: StoreField: r3->field_1f = r4
    //     0xafaef4: stur            w4, [x3, #0x1f]
    // 0xafaef8: StoreField: r3->field_23 = r4
    //     0xafaef8: stur            w4, [x3, #0x23]
    // 0xafaefc: StoreField: r3->field_27 = r4
    //     0xafaefc: stur            w4, [x3, #0x27]
    // 0xafaf00: StoreField: r3->field_2b = r4
    //     0xafaf00: stur            w4, [x3, #0x2b]
    // 0xafaf04: r1 = Null
    //     0xafaf04: mov             x1, NULL
    // 0xafaf08: r2 = 12
    //     0xafaf08: movz            x2, #0xc
    // 0xafaf0c: r0 = AllocateArray()
    //     0xafaf0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafaf10: mov             x2, x0
    // 0xafaf14: ldur            x0, [fp, #-0x48]
    // 0xafaf18: stur            x2, [fp, #-0x58]
    // 0xafaf1c: StoreField: r2->field_f = r0
    //     0xafaf1c: stur            w0, [x2, #0xf]
    // 0xafaf20: ldur            x0, [fp, #-0x40]
    // 0xafaf24: StoreField: r2->field_13 = r0
    //     0xafaf24: stur            w0, [x2, #0x13]
    // 0xafaf28: ldur            x0, [fp, #-0x50]
    // 0xafaf2c: ArrayStore: r2[0] = r0  ; List_4
    //     0xafaf2c: stur            w0, [x2, #0x17]
    // 0xafaf30: ldur            x0, [fp, #-0x60]
    // 0xafaf34: StoreField: r2->field_1b = r0
    //     0xafaf34: stur            w0, [x2, #0x1b]
    // 0xafaf38: r16 = Instance_SizedBox
    //     0xafaf38: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xafaf3c: ldr             x16, [x16, #0x578]
    // 0xafaf40: StoreField: r2->field_1f = r16
    //     0xafaf40: stur            w16, [x2, #0x1f]
    // 0xafaf44: ldur            x0, [fp, #-0x28]
    // 0xafaf48: StoreField: r2->field_23 = r0
    //     0xafaf48: stur            w0, [x2, #0x23]
    // 0xafaf4c: r1 = <Widget>
    //     0xafaf4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafaf50: r0 = AllocateGrowableArray()
    //     0xafaf50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafaf54: mov             x1, x0
    // 0xafaf58: ldur            x0, [fp, #-0x58]
    // 0xafaf5c: stur            x1, [fp, #-0x28]
    // 0xafaf60: StoreField: r1->field_f = r0
    //     0xafaf60: stur            w0, [x1, #0xf]
    // 0xafaf64: r0 = 12
    //     0xafaf64: movz            x0, #0xc
    // 0xafaf68: StoreField: r1->field_b = r0
    //     0xafaf68: stur            w0, [x1, #0xb]
    // 0xafaf6c: r0 = Column()
    //     0xafaf6c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafaf70: mov             x2, x0
    // 0xafaf74: r0 = Instance_Axis
    //     0xafaf74: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafaf78: stur            x2, [fp, #-0x40]
    // 0xafaf7c: StoreField: r2->field_f = r0
    //     0xafaf7c: stur            w0, [x2, #0xf]
    // 0xafaf80: r3 = Instance_MainAxisAlignment
    //     0xafaf80: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafaf84: ldr             x3, [x3, #0xa08]
    // 0xafaf88: StoreField: r2->field_13 = r3
    //     0xafaf88: stur            w3, [x2, #0x13]
    // 0xafaf8c: r1 = Instance_MainAxisSize
    //     0xafaf8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xafaf90: ldr             x1, [x1, #0xdd0]
    // 0xafaf94: ArrayStore: r2[0] = r1  ; List_4
    //     0xafaf94: stur            w1, [x2, #0x17]
    // 0xafaf98: r4 = Instance_CrossAxisAlignment
    //     0xafaf98: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafaf9c: ldr             x4, [x4, #0x890]
    // 0xafafa0: StoreField: r2->field_1b = r4
    //     0xafafa0: stur            w4, [x2, #0x1b]
    // 0xafafa4: r5 = Instance_VerticalDirection
    //     0xafafa4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafafa8: ldr             x5, [x5, #0xa20]
    // 0xafafac: StoreField: r2->field_23 = r5
    //     0xafafac: stur            w5, [x2, #0x23]
    // 0xafafb0: r6 = Instance_Clip
    //     0xafafb0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafafb4: ldr             x6, [x6, #0x38]
    // 0xafafb8: StoreField: r2->field_2b = r6
    //     0xafafb8: stur            w6, [x2, #0x2b]
    // 0xafafbc: StoreField: r2->field_2f = rZR
    //     0xafafbc: stur            xzr, [x2, #0x2f]
    // 0xafafc0: ldur            x1, [fp, #-0x28]
    // 0xafafc4: StoreField: r2->field_b = r1
    //     0xafafc4: stur            w1, [x2, #0xb]
    // 0xafafc8: r1 = <FlexParentData>
    //     0xafafc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xafafcc: ldr             x1, [x1, #0xe00]
    // 0xafafd0: r0 = Expanded()
    //     0xafafd0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xafafd4: mov             x2, x0
    // 0xafafd8: r0 = 1
    //     0xafafd8: movz            x0, #0x1
    // 0xafafdc: stur            x2, [fp, #-0x28]
    // 0xafafe0: StoreField: r2->field_13 = r0
    //     0xafafe0: stur            x0, [x2, #0x13]
    // 0xafafe4: r1 = Instance_FlexFit
    //     0xafafe4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xafafe8: ldr             x1, [x1, #0xe08]
    // 0xafafec: StoreField: r2->field_1b = r1
    //     0xafafec: stur            w1, [x2, #0x1b]
    // 0xafaff0: ldur            x1, [fp, #-0x40]
    // 0xafaff4: StoreField: r2->field_b = r1
    //     0xafaff4: stur            w1, [x2, #0xb]
    // 0xafaff8: ldur            x1, [fp, #-0x10]
    // 0xafaffc: r0 = of()
    //     0xafaffc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafb000: LoadField: r1 = r0->field_5b
    //     0xafb000: ldur            w1, [x0, #0x5b]
    // 0xafb004: DecompressPointer r1
    //     0xafb004: add             x1, x1, HEAP, lsl #32
    // 0xafb008: stur            x1, [fp, #-0x40]
    // 0xafb00c: r0 = ColorFilter()
    //     0xafb00c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xafb010: mov             x1, x0
    // 0xafb014: ldur            x0, [fp, #-0x40]
    // 0xafb018: stur            x1, [fp, #-0x48]
    // 0xafb01c: StoreField: r1->field_7 = r0
    //     0xafb01c: stur            w0, [x1, #7]
    // 0xafb020: r0 = Instance_BlendMode
    //     0xafb020: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xafb024: ldr             x0, [x0, #0xb30]
    // 0xafb028: StoreField: r1->field_b = r0
    //     0xafb028: stur            w0, [x1, #0xb]
    // 0xafb02c: r0 = 1
    //     0xafb02c: movz            x0, #0x1
    // 0xafb030: StoreField: r1->field_13 = r0
    //     0xafb030: stur            x0, [x1, #0x13]
    // 0xafb034: r0 = SvgPicture()
    //     0xafb034: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xafb038: stur            x0, [fp, #-0x40]
    // 0xafb03c: ldur            x16, [fp, #-0x48]
    // 0xafb040: str             x16, [SP]
    // 0xafb044: mov             x1, x0
    // 0xafb048: r2 = "assets/images/small_right.svg"
    //     0xafb048: add             x2, PP, #0x46, lsl #12  ; [pp+0x46a70] "assets/images/small_right.svg"
    //     0xafb04c: ldr             x2, [x2, #0xa70]
    // 0xafb050: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xafb050: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xafb054: ldr             x4, [x4, #0xa38]
    // 0xafb058: r0 = SvgPicture.asset()
    //     0xafb058: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xafb05c: r1 = Null
    //     0xafb05c: mov             x1, NULL
    // 0xafb060: r2 = 8
    //     0xafb060: movz            x2, #0x8
    // 0xafb064: r0 = AllocateArray()
    //     0xafb064: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafb068: mov             x2, x0
    // 0xafb06c: ldur            x0, [fp, #-0x20]
    // 0xafb070: stur            x2, [fp, #-0x48]
    // 0xafb074: StoreField: r2->field_f = r0
    //     0xafb074: stur            w0, [x2, #0xf]
    // 0xafb078: r16 = Instance_SizedBox
    //     0xafb078: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xafb07c: ldr             x16, [x16, #0xb20]
    // 0xafb080: StoreField: r2->field_13 = r16
    //     0xafb080: stur            w16, [x2, #0x13]
    // 0xafb084: ldur            x0, [fp, #-0x28]
    // 0xafb088: ArrayStore: r2[0] = r0  ; List_4
    //     0xafb088: stur            w0, [x2, #0x17]
    // 0xafb08c: ldur            x0, [fp, #-0x40]
    // 0xafb090: StoreField: r2->field_1b = r0
    //     0xafb090: stur            w0, [x2, #0x1b]
    // 0xafb094: r1 = <Widget>
    //     0xafb094: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafb098: r0 = AllocateGrowableArray()
    //     0xafb098: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafb09c: mov             x1, x0
    // 0xafb0a0: ldur            x0, [fp, #-0x48]
    // 0xafb0a4: stur            x1, [fp, #-0x20]
    // 0xafb0a8: StoreField: r1->field_f = r0
    //     0xafb0a8: stur            w0, [x1, #0xf]
    // 0xafb0ac: r2 = 8
    //     0xafb0ac: movz            x2, #0x8
    // 0xafb0b0: StoreField: r1->field_b = r2
    //     0xafb0b0: stur            w2, [x1, #0xb]
    // 0xafb0b4: r0 = Row()
    //     0xafb0b4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafb0b8: mov             x1, x0
    // 0xafb0bc: r0 = Instance_Axis
    //     0xafb0bc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafb0c0: stur            x1, [fp, #-0x48]
    // 0xafb0c4: StoreField: r1->field_f = r0
    //     0xafb0c4: stur            w0, [x1, #0xf]
    // 0xafb0c8: r2 = Instance_MainAxisAlignment
    //     0xafb0c8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xafb0cc: ldr             x2, [x2, #0xab0]
    // 0xafb0d0: StoreField: r1->field_13 = r2
    //     0xafb0d0: stur            w2, [x1, #0x13]
    // 0xafb0d4: r2 = Instance_MainAxisSize
    //     0xafb0d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafb0d8: ldr             x2, [x2, #0xa10]
    // 0xafb0dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xafb0dc: stur            w2, [x1, #0x17]
    // 0xafb0e0: r3 = Instance_CrossAxisAlignment
    //     0xafb0e0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafb0e4: ldr             x3, [x3, #0xa18]
    // 0xafb0e8: StoreField: r1->field_1b = r3
    //     0xafb0e8: stur            w3, [x1, #0x1b]
    // 0xafb0ec: r4 = Instance_VerticalDirection
    //     0xafb0ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafb0f0: ldr             x4, [x4, #0xa20]
    // 0xafb0f4: StoreField: r1->field_23 = r4
    //     0xafb0f4: stur            w4, [x1, #0x23]
    // 0xafb0f8: r5 = Instance_Clip
    //     0xafb0f8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafb0fc: ldr             x5, [x5, #0x38]
    // 0xafb100: StoreField: r1->field_2b = r5
    //     0xafb100: stur            w5, [x1, #0x2b]
    // 0xafb104: StoreField: r1->field_2f = rZR
    //     0xafb104: stur            xzr, [x1, #0x2f]
    // 0xafb108: ldur            x6, [fp, #-0x20]
    // 0xafb10c: StoreField: r1->field_b = r6
    //     0xafb10c: stur            w6, [x1, #0xb]
    // 0xafb110: ldur            x6, [fp, #-8]
    // 0xafb114: LoadField: r7 = r6->field_b
    //     0xafb114: ldur            w7, [x6, #0xb]
    // 0xafb118: DecompressPointer r7
    //     0xafb118: add             x7, x7, HEAP, lsl #32
    // 0xafb11c: cmp             w7, NULL
    // 0xafb120: b.eq            #0xafbd54
    // 0xafb124: LoadField: r8 = r7->field_b
    //     0xafb124: ldur            w8, [x7, #0xb]
    // 0xafb128: DecompressPointer r8
    //     0xafb128: add             x8, x8, HEAP, lsl #32
    // 0xafb12c: stur            x8, [fp, #-0x40]
    // 0xafb130: LoadField: r7 = r8->field_3b
    //     0xafb130: ldur            w7, [x8, #0x3b]
    // 0xafb134: DecompressPointer r7
    //     0xafb134: add             x7, x7, HEAP, lsl #32
    // 0xafb138: stur            x7, [fp, #-0x28]
    // 0xafb13c: LoadField: r9 = r8->field_43
    //     0xafb13c: ldur            w9, [x8, #0x43]
    // 0xafb140: DecompressPointer r9
    //     0xafb140: add             x9, x9, HEAP, lsl #32
    // 0xafb144: stur            x9, [fp, #-0x20]
    // 0xafb148: r0 = CustomisedStrip()
    //     0xafb148: bl              #0xa078f0  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xafb14c: mov             x1, x0
    // 0xafb150: ldur            x0, [fp, #-0x28]
    // 0xafb154: stur            x1, [fp, #-0x50]
    // 0xafb158: StoreField: r1->field_b = r0
    //     0xafb158: stur            w0, [x1, #0xb]
    // 0xafb15c: ldur            x0, [fp, #-0x20]
    // 0xafb160: StoreField: r1->field_13 = r0
    //     0xafb160: stur            w0, [x1, #0x13]
    // 0xafb164: r0 = Visibility()
    //     0xafb164: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafb168: mov             x3, x0
    // 0xafb16c: ldur            x0, [fp, #-0x50]
    // 0xafb170: stur            x3, [fp, #-0x20]
    // 0xafb174: StoreField: r3->field_b = r0
    //     0xafb174: stur            w0, [x3, #0xb]
    // 0xafb178: r0 = Instance_SizedBox
    //     0xafb178: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafb17c: StoreField: r3->field_f = r0
    //     0xafb17c: stur            w0, [x3, #0xf]
    // 0xafb180: r4 = false
    //     0xafb180: add             x4, NULL, #0x30  ; false
    // 0xafb184: StoreField: r3->field_13 = r4
    //     0xafb184: stur            w4, [x3, #0x13]
    // 0xafb188: ArrayStore: r3[0] = r4  ; List_4
    //     0xafb188: stur            w4, [x3, #0x17]
    // 0xafb18c: StoreField: r3->field_1b = r4
    //     0xafb18c: stur            w4, [x3, #0x1b]
    // 0xafb190: StoreField: r3->field_1f = r4
    //     0xafb190: stur            w4, [x3, #0x1f]
    // 0xafb194: StoreField: r3->field_23 = r4
    //     0xafb194: stur            w4, [x3, #0x23]
    // 0xafb198: StoreField: r3->field_27 = r4
    //     0xafb198: stur            w4, [x3, #0x27]
    // 0xafb19c: StoreField: r3->field_2b = r4
    //     0xafb19c: stur            w4, [x3, #0x2b]
    // 0xafb1a0: r1 = Null
    //     0xafb1a0: mov             x1, NULL
    // 0xafb1a4: r2 = 8
    //     0xafb1a4: movz            x2, #0x8
    // 0xafb1a8: r0 = AllocateArray()
    //     0xafb1a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafb1ac: mov             x2, x0
    // 0xafb1b0: ldur            x0, [fp, #-0x38]
    // 0xafb1b4: stur            x2, [fp, #-0x28]
    // 0xafb1b8: StoreField: r2->field_f = r0
    //     0xafb1b8: stur            w0, [x2, #0xf]
    // 0xafb1bc: r16 = Instance_SizedBox
    //     0xafb1bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xafb1c0: ldr             x16, [x16, #0x8b8]
    // 0xafb1c4: StoreField: r2->field_13 = r16
    //     0xafb1c4: stur            w16, [x2, #0x13]
    // 0xafb1c8: ldur            x0, [fp, #-0x48]
    // 0xafb1cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xafb1cc: stur            w0, [x2, #0x17]
    // 0xafb1d0: ldur            x0, [fp, #-0x20]
    // 0xafb1d4: StoreField: r2->field_1b = r0
    //     0xafb1d4: stur            w0, [x2, #0x1b]
    // 0xafb1d8: r1 = <Widget>
    //     0xafb1d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafb1dc: r0 = AllocateGrowableArray()
    //     0xafb1dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafb1e0: mov             x1, x0
    // 0xafb1e4: ldur            x0, [fp, #-0x28]
    // 0xafb1e8: stur            x1, [fp, #-0x20]
    // 0xafb1ec: StoreField: r1->field_f = r0
    //     0xafb1ec: stur            w0, [x1, #0xf]
    // 0xafb1f0: r2 = 8
    //     0xafb1f0: movz            x2, #0x8
    // 0xafb1f4: StoreField: r1->field_b = r2
    //     0xafb1f4: stur            w2, [x1, #0xb]
    // 0xafb1f8: r0 = Column()
    //     0xafb1f8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafb1fc: mov             x1, x0
    // 0xafb200: r0 = Instance_Axis
    //     0xafb200: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafb204: stur            x1, [fp, #-0x28]
    // 0xafb208: StoreField: r1->field_f = r0
    //     0xafb208: stur            w0, [x1, #0xf]
    // 0xafb20c: r2 = Instance_MainAxisAlignment
    //     0xafb20c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafb210: ldr             x2, [x2, #0xa08]
    // 0xafb214: StoreField: r1->field_13 = r2
    //     0xafb214: stur            w2, [x1, #0x13]
    // 0xafb218: r3 = Instance_MainAxisSize
    //     0xafb218: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafb21c: ldr             x3, [x3, #0xa10]
    // 0xafb220: ArrayStore: r1[0] = r3  ; List_4
    //     0xafb220: stur            w3, [x1, #0x17]
    // 0xafb224: r4 = Instance_CrossAxisAlignment
    //     0xafb224: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafb228: ldr             x4, [x4, #0x890]
    // 0xafb22c: StoreField: r1->field_1b = r4
    //     0xafb22c: stur            w4, [x1, #0x1b]
    // 0xafb230: r5 = Instance_VerticalDirection
    //     0xafb230: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafb234: ldr             x5, [x5, #0xa20]
    // 0xafb238: StoreField: r1->field_23 = r5
    //     0xafb238: stur            w5, [x1, #0x23]
    // 0xafb23c: r6 = Instance_Clip
    //     0xafb23c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafb240: ldr             x6, [x6, #0x38]
    // 0xafb244: StoreField: r1->field_2b = r6
    //     0xafb244: stur            w6, [x1, #0x2b]
    // 0xafb248: StoreField: r1->field_2f = rZR
    //     0xafb248: stur            xzr, [x1, #0x2f]
    // 0xafb24c: ldur            x7, [fp, #-0x20]
    // 0xafb250: StoreField: r1->field_b = r7
    //     0xafb250: stur            w7, [x1, #0xb]
    // 0xafb254: r0 = InkWell()
    //     0xafb254: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xafb258: mov             x3, x0
    // 0xafb25c: ldur            x0, [fp, #-0x28]
    // 0xafb260: stur            x3, [fp, #-0x20]
    // 0xafb264: StoreField: r3->field_b = r0
    //     0xafb264: stur            w0, [x3, #0xb]
    // 0xafb268: ldur            x2, [fp, #-0x18]
    // 0xafb26c: r1 = Function '<anonymous closure>':.
    //     0xafb26c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aca8] AnonymousClosure: (0xafd094), in [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::build (0xafa414)
    //     0xafb270: ldr             x1, [x1, #0xca8]
    // 0xafb274: r0 = AllocateClosure()
    //     0xafb274: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafb278: mov             x1, x0
    // 0xafb27c: ldur            x0, [fp, #-0x20]
    // 0xafb280: StoreField: r0->field_f = r1
    //     0xafb280: stur            w1, [x0, #0xf]
    // 0xafb284: r2 = true
    //     0xafb284: add             x2, NULL, #0x20  ; true
    // 0xafb288: StoreField: r0->field_43 = r2
    //     0xafb288: stur            w2, [x0, #0x43]
    // 0xafb28c: r3 = Instance_BoxShape
    //     0xafb28c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafb290: ldr             x3, [x3, #0x80]
    // 0xafb294: StoreField: r0->field_47 = r3
    //     0xafb294: stur            w3, [x0, #0x47]
    // 0xafb298: StoreField: r0->field_6f = r2
    //     0xafb298: stur            w2, [x0, #0x6f]
    // 0xafb29c: r4 = false
    //     0xafb29c: add             x4, NULL, #0x30  ; false
    // 0xafb2a0: StoreField: r0->field_73 = r4
    //     0xafb2a0: stur            w4, [x0, #0x73]
    // 0xafb2a4: StoreField: r0->field_83 = r2
    //     0xafb2a4: stur            w2, [x0, #0x83]
    // 0xafb2a8: StoreField: r0->field_7b = r4
    //     0xafb2a8: stur            w4, [x0, #0x7b]
    // 0xafb2ac: ldur            x1, [fp, #-0x40]
    // 0xafb2b0: LoadField: r5 = r1->field_1b
    //     0xafb2b0: ldur            w5, [x1, #0x1b]
    // 0xafb2b4: DecompressPointer r5
    //     0xafb2b4: add             x5, x5, HEAP, lsl #32
    // 0xafb2b8: cmp             w5, NULL
    // 0xafb2bc: b.ne            #0xafb2c8
    // 0xafb2c0: r5 = Null
    //     0xafb2c0: mov             x5, NULL
    // 0xafb2c4: b               #0xafb2dc
    // 0xafb2c8: LoadField: r6 = r5->field_7
    //     0xafb2c8: ldur            w6, [x5, #7]
    // 0xafb2cc: cbnz            w6, #0xafb2d8
    // 0xafb2d0: r5 = false
    //     0xafb2d0: add             x5, NULL, #0x30  ; false
    // 0xafb2d4: b               #0xafb2dc
    // 0xafb2d8: r5 = true
    //     0xafb2d8: add             x5, NULL, #0x20  ; true
    // 0xafb2dc: cmp             w5, NULL
    // 0xafb2e0: b.ne            #0xafb2e8
    // 0xafb2e4: r5 = false
    //     0xafb2e4: add             x5, NULL, #0x30  ; false
    // 0xafb2e8: stur            x5, [fp, #-0x38]
    // 0xafb2ec: LoadField: r6 = r1->field_13
    //     0xafb2ec: ldur            w6, [x1, #0x13]
    // 0xafb2f0: DecompressPointer r6
    //     0xafb2f0: add             x6, x6, HEAP, lsl #32
    // 0xafb2f4: cmp             w6, NULL
    // 0xafb2f8: b.ne            #0xafb304
    // 0xafb2fc: r7 = ""
    //     0xafb2fc: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafb300: b               #0xafb308
    // 0xafb304: mov             x7, x6
    // 0xafb308: ldur            x6, [fp, #-8]
    // 0xafb30c: ldur            x1, [fp, #-0x10]
    // 0xafb310: stur            x7, [fp, #-0x28]
    // 0xafb314: r0 = of()
    //     0xafb314: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafb318: LoadField: r1 = r0->field_87
    //     0xafb318: ldur            w1, [x0, #0x87]
    // 0xafb31c: DecompressPointer r1
    //     0xafb31c: add             x1, x1, HEAP, lsl #32
    // 0xafb320: LoadField: r0 = r1->field_7
    //     0xafb320: ldur            w0, [x1, #7]
    // 0xafb324: DecompressPointer r0
    //     0xafb324: add             x0, x0, HEAP, lsl #32
    // 0xafb328: r16 = Instance_Color
    //     0xafb328: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafb32c: r30 = 12.000000
    //     0xafb32c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafb330: ldr             lr, [lr, #0x9e8]
    // 0xafb334: stp             lr, x16, [SP]
    // 0xafb338: mov             x1, x0
    // 0xafb33c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xafb33c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xafb340: ldr             x4, [x4, #0x9b8]
    // 0xafb344: r0 = copyWith()
    //     0xafb344: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafb348: stur            x0, [fp, #-0x40]
    // 0xafb34c: r0 = Text()
    //     0xafb34c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafb350: mov             x2, x0
    // 0xafb354: ldur            x0, [fp, #-0x28]
    // 0xafb358: stur            x2, [fp, #-0x48]
    // 0xafb35c: StoreField: r2->field_b = r0
    //     0xafb35c: stur            w0, [x2, #0xb]
    // 0xafb360: ldur            x0, [fp, #-0x40]
    // 0xafb364: StoreField: r2->field_13 = r0
    //     0xafb364: stur            w0, [x2, #0x13]
    // 0xafb368: ldur            x0, [fp, #-8]
    // 0xafb36c: LoadField: r1 = r0->field_b
    //     0xafb36c: ldur            w1, [x0, #0xb]
    // 0xafb370: DecompressPointer r1
    //     0xafb370: add             x1, x1, HEAP, lsl #32
    // 0xafb374: cmp             w1, NULL
    // 0xafb378: b.eq            #0xafbd58
    // 0xafb37c: LoadField: r3 = r1->field_b
    //     0xafb37c: ldur            w3, [x1, #0xb]
    // 0xafb380: DecompressPointer r3
    //     0xafb380: add             x3, x3, HEAP, lsl #32
    // 0xafb384: LoadField: r1 = r3->field_1b
    //     0xafb384: ldur            w1, [x3, #0x1b]
    // 0xafb388: DecompressPointer r1
    //     0xafb388: add             x1, x1, HEAP, lsl #32
    // 0xafb38c: cmp             w1, NULL
    // 0xafb390: b.ne            #0xafb39c
    // 0xafb394: r4 = ""
    //     0xafb394: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafb398: b               #0xafb3a0
    // 0xafb39c: mov             x4, x1
    // 0xafb3a0: ldur            x3, [fp, #-0x38]
    // 0xafb3a4: ldur            x1, [fp, #-0x10]
    // 0xafb3a8: stur            x4, [fp, #-0x28]
    // 0xafb3ac: r0 = of()
    //     0xafb3ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafb3b0: LoadField: r1 = r0->field_87
    //     0xafb3b0: ldur            w1, [x0, #0x87]
    // 0xafb3b4: DecompressPointer r1
    //     0xafb3b4: add             x1, x1, HEAP, lsl #32
    // 0xafb3b8: LoadField: r0 = r1->field_2b
    //     0xafb3b8: ldur            w0, [x1, #0x2b]
    // 0xafb3bc: DecompressPointer r0
    //     0xafb3bc: add             x0, x0, HEAP, lsl #32
    // 0xafb3c0: stur            x0, [fp, #-0x40]
    // 0xafb3c4: r1 = Instance_Color
    //     0xafb3c4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafb3c8: d0 = 0.400000
    //     0xafb3c8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xafb3cc: r0 = withOpacity()
    //     0xafb3cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xafb3d0: r16 = 12.000000
    //     0xafb3d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafb3d4: ldr             x16, [x16, #0x9e8]
    // 0xafb3d8: stp             x16, x0, [SP]
    // 0xafb3dc: ldur            x1, [fp, #-0x40]
    // 0xafb3e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xafb3e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xafb3e4: ldr             x4, [x4, #0x9b8]
    // 0xafb3e8: r0 = copyWith()
    //     0xafb3e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafb3ec: stur            x0, [fp, #-0x40]
    // 0xafb3f0: r0 = Text()
    //     0xafb3f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafb3f4: mov             x3, x0
    // 0xafb3f8: ldur            x0, [fp, #-0x28]
    // 0xafb3fc: stur            x3, [fp, #-0x50]
    // 0xafb400: StoreField: r3->field_b = r0
    //     0xafb400: stur            w0, [x3, #0xb]
    // 0xafb404: ldur            x0, [fp, #-0x40]
    // 0xafb408: StoreField: r3->field_13 = r0
    //     0xafb408: stur            w0, [x3, #0x13]
    // 0xafb40c: r1 = Null
    //     0xafb40c: mov             x1, NULL
    // 0xafb410: r2 = 6
    //     0xafb410: movz            x2, #0x6
    // 0xafb414: r0 = AllocateArray()
    //     0xafb414: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafb418: mov             x2, x0
    // 0xafb41c: ldur            x0, [fp, #-0x48]
    // 0xafb420: stur            x2, [fp, #-0x28]
    // 0xafb424: StoreField: r2->field_f = r0
    //     0xafb424: stur            w0, [x2, #0xf]
    // 0xafb428: r16 = Instance_SizedBox
    //     0xafb428: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xafb42c: ldr             x16, [x16, #0x328]
    // 0xafb430: StoreField: r2->field_13 = r16
    //     0xafb430: stur            w16, [x2, #0x13]
    // 0xafb434: ldur            x0, [fp, #-0x50]
    // 0xafb438: ArrayStore: r2[0] = r0  ; List_4
    //     0xafb438: stur            w0, [x2, #0x17]
    // 0xafb43c: r1 = <Widget>
    //     0xafb43c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafb440: r0 = AllocateGrowableArray()
    //     0xafb440: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafb444: mov             x1, x0
    // 0xafb448: ldur            x0, [fp, #-0x28]
    // 0xafb44c: stur            x1, [fp, #-0x40]
    // 0xafb450: StoreField: r1->field_f = r0
    //     0xafb450: stur            w0, [x1, #0xf]
    // 0xafb454: r0 = 6
    //     0xafb454: movz            x0, #0x6
    // 0xafb458: StoreField: r1->field_b = r0
    //     0xafb458: stur            w0, [x1, #0xb]
    // 0xafb45c: r0 = Column()
    //     0xafb45c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafb460: mov             x1, x0
    // 0xafb464: r0 = Instance_Axis
    //     0xafb464: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafb468: stur            x1, [fp, #-0x28]
    // 0xafb46c: StoreField: r1->field_f = r0
    //     0xafb46c: stur            w0, [x1, #0xf]
    // 0xafb470: r2 = Instance_MainAxisAlignment
    //     0xafb470: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafb474: ldr             x2, [x2, #0xa08]
    // 0xafb478: StoreField: r1->field_13 = r2
    //     0xafb478: stur            w2, [x1, #0x13]
    // 0xafb47c: r3 = Instance_MainAxisSize
    //     0xafb47c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafb480: ldr             x3, [x3, #0xa10]
    // 0xafb484: ArrayStore: r1[0] = r3  ; List_4
    //     0xafb484: stur            w3, [x1, #0x17]
    // 0xafb488: r4 = Instance_CrossAxisAlignment
    //     0xafb488: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafb48c: ldr             x4, [x4, #0x890]
    // 0xafb490: StoreField: r1->field_1b = r4
    //     0xafb490: stur            w4, [x1, #0x1b]
    // 0xafb494: r5 = Instance_VerticalDirection
    //     0xafb494: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafb498: ldr             x5, [x5, #0xa20]
    // 0xafb49c: StoreField: r1->field_23 = r5
    //     0xafb49c: stur            w5, [x1, #0x23]
    // 0xafb4a0: r6 = Instance_Clip
    //     0xafb4a0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafb4a4: ldr             x6, [x6, #0x38]
    // 0xafb4a8: StoreField: r1->field_2b = r6
    //     0xafb4a8: stur            w6, [x1, #0x2b]
    // 0xafb4ac: StoreField: r1->field_2f = rZR
    //     0xafb4ac: stur            xzr, [x1, #0x2f]
    // 0xafb4b0: ldur            x7, [fp, #-0x40]
    // 0xafb4b4: StoreField: r1->field_b = r7
    //     0xafb4b4: stur            w7, [x1, #0xb]
    // 0xafb4b8: r0 = Padding()
    //     0xafb4b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafb4bc: mov             x1, x0
    // 0xafb4c0: r0 = Instance_EdgeInsets
    //     0xafb4c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xafb4c4: ldr             x0, [x0, #0x980]
    // 0xafb4c8: stur            x1, [fp, #-0x40]
    // 0xafb4cc: StoreField: r1->field_f = r0
    //     0xafb4cc: stur            w0, [x1, #0xf]
    // 0xafb4d0: ldur            x2, [fp, #-0x28]
    // 0xafb4d4: StoreField: r1->field_b = r2
    //     0xafb4d4: stur            w2, [x1, #0xb]
    // 0xafb4d8: r0 = Container()
    //     0xafb4d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafb4dc: stur            x0, [fp, #-0x28]
    // 0xafb4e0: r16 = Instance_BoxDecoration
    //     0xafb4e0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a7f8] Obj!BoxDecoration@d64b31
    //     0xafb4e4: ldr             x16, [x16, #0x7f8]
    // 0xafb4e8: ldur            lr, [fp, #-0x40]
    // 0xafb4ec: stp             lr, x16, [SP]
    // 0xafb4f0: mov             x1, x0
    // 0xafb4f4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xafb4f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xafb4f8: ldr             x4, [x4, #0x88]
    // 0xafb4fc: r0 = Container()
    //     0xafb4fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafb500: r0 = Padding()
    //     0xafb500: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafb504: mov             x1, x0
    // 0xafb508: r0 = Instance_EdgeInsets
    //     0xafb508: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xafb50c: ldr             x0, [x0, #0x980]
    // 0xafb510: stur            x1, [fp, #-0x40]
    // 0xafb514: StoreField: r1->field_f = r0
    //     0xafb514: stur            w0, [x1, #0xf]
    // 0xafb518: ldur            x0, [fp, #-0x28]
    // 0xafb51c: StoreField: r1->field_b = r0
    //     0xafb51c: stur            w0, [x1, #0xb]
    // 0xafb520: r0 = Visibility()
    //     0xafb520: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafb524: mov             x2, x0
    // 0xafb528: ldur            x0, [fp, #-0x40]
    // 0xafb52c: stur            x2, [fp, #-0x48]
    // 0xafb530: StoreField: r2->field_b = r0
    //     0xafb530: stur            w0, [x2, #0xb]
    // 0xafb534: r3 = Instance_SizedBox
    //     0xafb534: ldr             x3, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafb538: StoreField: r2->field_f = r3
    //     0xafb538: stur            w3, [x2, #0xf]
    // 0xafb53c: ldur            x0, [fp, #-0x38]
    // 0xafb540: StoreField: r2->field_13 = r0
    //     0xafb540: stur            w0, [x2, #0x13]
    // 0xafb544: r4 = false
    //     0xafb544: add             x4, NULL, #0x30  ; false
    // 0xafb548: ArrayStore: r2[0] = r4  ; List_4
    //     0xafb548: stur            w4, [x2, #0x17]
    // 0xafb54c: StoreField: r2->field_1b = r4
    //     0xafb54c: stur            w4, [x2, #0x1b]
    // 0xafb550: StoreField: r2->field_1f = r4
    //     0xafb550: stur            w4, [x2, #0x1f]
    // 0xafb554: StoreField: r2->field_23 = r4
    //     0xafb554: stur            w4, [x2, #0x23]
    // 0xafb558: StoreField: r2->field_27 = r4
    //     0xafb558: stur            w4, [x2, #0x27]
    // 0xafb55c: StoreField: r2->field_2b = r4
    //     0xafb55c: stur            w4, [x2, #0x2b]
    // 0xafb560: ldur            x5, [fp, #-8]
    // 0xafb564: LoadField: r0 = r5->field_b
    //     0xafb564: ldur            w0, [x5, #0xb]
    // 0xafb568: DecompressPointer r0
    //     0xafb568: add             x0, x0, HEAP, lsl #32
    // 0xafb56c: cmp             w0, NULL
    // 0xafb570: b.eq            #0xafbd5c
    // 0xafb574: LoadField: r1 = r0->field_b
    //     0xafb574: ldur            w1, [x0, #0xb]
    // 0xafb578: DecompressPointer r1
    //     0xafb578: add             x1, x1, HEAP, lsl #32
    // 0xafb57c: LoadField: r0 = r1->field_83
    //     0xafb57c: ldur            w0, [x1, #0x83]
    // 0xafb580: DecompressPointer r0
    //     0xafb580: add             x0, x0, HEAP, lsl #32
    // 0xafb584: cmp             w0, NULL
    // 0xafb588: b.ne            #0xafb594
    // 0xafb58c: r6 = false
    //     0xafb58c: add             x6, NULL, #0x30  ; false
    // 0xafb590: b               #0xafb598
    // 0xafb594: mov             x6, x0
    // 0xafb598: stur            x6, [fp, #-0x28]
    // 0xafb59c: LoadField: r0 = r1->field_8b
    //     0xafb59c: ldur            w0, [x1, #0x8b]
    // 0xafb5a0: DecompressPointer r0
    //     0xafb5a0: add             x0, x0, HEAP, lsl #32
    // 0xafb5a4: cmp             w0, NULL
    // 0xafb5a8: b.ne            #0xafb5b4
    // 0xafb5ac: r0 = Null
    //     0xafb5ac: mov             x0, NULL
    // 0xafb5b0: b               #0xafb5cc
    // 0xafb5b4: LoadField: r7 = r0->field_f
    //     0xafb5b4: ldur            x7, [x0, #0xf]
    // 0xafb5b8: r0 = BoxInt64Instr(r7)
    //     0xafb5b8: sbfiz           x0, x7, #1, #0x1f
    //     0xafb5bc: cmp             x7, x0, asr #1
    //     0xafb5c0: b.eq            #0xafb5cc
    //     0xafb5c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xafb5c8: stur            x7, [x0, #7]
    // 0xafb5cc: cmp             w0, NULL
    // 0xafb5d0: b.ne            #0xafb5dc
    // 0xafb5d4: r7 = 0
    //     0xafb5d4: movz            x7, #0
    // 0xafb5d8: b               #0xafb5ec
    // 0xafb5dc: r1 = LoadInt32Instr(r0)
    //     0xafb5dc: sbfx            x1, x0, #1, #0x1f
    //     0xafb5e0: tbz             w0, #0, #0xafb5e8
    //     0xafb5e4: ldur            x1, [x0, #7]
    // 0xafb5e8: mov             x7, x1
    // 0xafb5ec: r0 = BoxInt64Instr(r7)
    //     0xafb5ec: sbfiz           x0, x7, #1, #0x1f
    //     0xafb5f0: cmp             x7, x0, asr #1
    //     0xafb5f4: b.eq            #0xafb600
    //     0xafb5f8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xafb5fc: stur            x7, [x0, #7]
    // 0xafb600: stp             x0, NULL, [SP]
    // 0xafb604: r0 = _Double.fromInteger()
    //     0xafb604: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xafb608: ldur            x2, [fp, #-0x18]
    // 0xafb60c: r1 = Function '<anonymous closure>':.
    //     0xafb60c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6acb0] AnonymousClosure: (0xafcf74), in [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::build (0xafa414)
    //     0xafb610: ldr             x1, [x1, #0xcb0]
    // 0xafb614: stur            x0, [fp, #-0x38]
    // 0xafb618: r0 = AllocateClosure()
    //     0xafb618: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafb61c: stur            x0, [fp, #-0x40]
    // 0xafb620: r0 = RatingBar()
    //     0xafb620: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xafb624: mov             x3, x0
    // 0xafb628: ldur            x0, [fp, #-0x40]
    // 0xafb62c: stur            x3, [fp, #-0x50]
    // 0xafb630: StoreField: r3->field_b = r0
    //     0xafb630: stur            w0, [x3, #0xb]
    // 0xafb634: r0 = false
    //     0xafb634: add             x0, NULL, #0x30  ; false
    // 0xafb638: StoreField: r3->field_1f = r0
    //     0xafb638: stur            w0, [x3, #0x1f]
    // 0xafb63c: r4 = Instance_Axis
    //     0xafb63c: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafb640: StoreField: r3->field_23 = r4
    //     0xafb640: stur            w4, [x3, #0x23]
    // 0xafb644: r5 = true
    //     0xafb644: add             x5, NULL, #0x20  ; true
    // 0xafb648: StoreField: r3->field_27 = r5
    //     0xafb648: stur            w5, [x3, #0x27]
    // 0xafb64c: d0 = 2.000000
    //     0xafb64c: fmov            d0, #2.00000000
    // 0xafb650: StoreField: r3->field_2b = d0
    //     0xafb650: stur            d0, [x3, #0x2b]
    // 0xafb654: StoreField: r3->field_33 = r0
    //     0xafb654: stur            w0, [x3, #0x33]
    // 0xafb658: ldur            x1, [fp, #-0x38]
    // 0xafb65c: LoadField: d0 = r1->field_7
    //     0xafb65c: ldur            d0, [x1, #7]
    // 0xafb660: StoreField: r3->field_37 = d0
    //     0xafb660: stur            d0, [x3, #0x37]
    // 0xafb664: r1 = 5
    //     0xafb664: movz            x1, #0x5
    // 0xafb668: StoreField: r3->field_3f = r1
    //     0xafb668: stur            x1, [x3, #0x3f]
    // 0xafb66c: r1 = Instance_EdgeInsets
    //     0xafb66c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xafb670: ldr             x1, [x1, #0xa68]
    // 0xafb674: StoreField: r3->field_47 = r1
    //     0xafb674: stur            w1, [x3, #0x47]
    // 0xafb678: d0 = 20.000000
    //     0xafb678: fmov            d0, #20.00000000
    // 0xafb67c: StoreField: r3->field_4b = d0
    //     0xafb67c: stur            d0, [x3, #0x4b]
    // 0xafb680: d0 = 1.000000
    //     0xafb680: fmov            d0, #1.00000000
    // 0xafb684: StoreField: r3->field_53 = d0
    //     0xafb684: stur            d0, [x3, #0x53]
    // 0xafb688: StoreField: r3->field_5b = r0
    //     0xafb688: stur            w0, [x3, #0x5b]
    // 0xafb68c: StoreField: r3->field_5f = r0
    //     0xafb68c: stur            w0, [x3, #0x5f]
    // 0xafb690: ldur            x2, [fp, #-0x18]
    // 0xafb694: r1 = Function '<anonymous closure>':.
    //     0xafb694: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6acb8] AnonymousClosure: (0xafce60), in [package:customer_app/app/presentation/views/glass/orders/order_card.dart] _OrderCardState::build (0xb753b0)
    //     0xafb698: ldr             x1, [x1, #0xcb8]
    // 0xafb69c: r0 = AllocateClosure()
    //     0xafb69c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafb6a0: mov             x1, x0
    // 0xafb6a4: ldur            x0, [fp, #-0x50]
    // 0xafb6a8: StoreField: r0->field_63 = r1
    //     0xafb6a8: stur            w1, [x0, #0x63]
    // 0xafb6ac: ldur            x1, [fp, #-0x10]
    // 0xafb6b0: r0 = of()
    //     0xafb6b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafb6b4: LoadField: r1 = r0->field_87
    //     0xafb6b4: ldur            w1, [x0, #0x87]
    // 0xafb6b8: DecompressPointer r1
    //     0xafb6b8: add             x1, x1, HEAP, lsl #32
    // 0xafb6bc: LoadField: r0 = r1->field_2b
    //     0xafb6bc: ldur            w0, [x1, #0x2b]
    // 0xafb6c0: DecompressPointer r0
    //     0xafb6c0: add             x0, x0, HEAP, lsl #32
    // 0xafb6c4: stur            x0, [fp, #-0x38]
    // 0xafb6c8: r1 = Instance_Color
    //     0xafb6c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafb6cc: d0 = 0.300000
    //     0xafb6cc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xafb6d0: ldr             d0, [x17, #0x658]
    // 0xafb6d4: r0 = withOpacity()
    //     0xafb6d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xafb6d8: r16 = 12.000000
    //     0xafb6d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafb6dc: ldr             x16, [x16, #0x9e8]
    // 0xafb6e0: stp             x0, x16, [SP]
    // 0xafb6e4: ldur            x1, [fp, #-0x38]
    // 0xafb6e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafb6e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafb6ec: ldr             x4, [x4, #0xaa0]
    // 0xafb6f0: r0 = copyWith()
    //     0xafb6f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafb6f4: stur            x0, [fp, #-0x38]
    // 0xafb6f8: r0 = Text()
    //     0xafb6f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafb6fc: mov             x1, x0
    // 0xafb700: r0 = "Rate this product"
    //     0xafb700: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fcb0] "Rate this product"
    //     0xafb704: ldr             x0, [x0, #0xcb0]
    // 0xafb708: stur            x1, [fp, #-0x40]
    // 0xafb70c: StoreField: r1->field_b = r0
    //     0xafb70c: stur            w0, [x1, #0xb]
    // 0xafb710: ldur            x0, [fp, #-0x38]
    // 0xafb714: StoreField: r1->field_13 = r0
    //     0xafb714: stur            w0, [x1, #0x13]
    // 0xafb718: r0 = Padding()
    //     0xafb718: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafb71c: mov             x3, x0
    // 0xafb720: r0 = Instance_EdgeInsets
    //     0xafb720: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xafb724: ldr             x0, [x0, #0x668]
    // 0xafb728: stur            x3, [fp, #-0x38]
    // 0xafb72c: StoreField: r3->field_f = r0
    //     0xafb72c: stur            w0, [x3, #0xf]
    // 0xafb730: ldur            x0, [fp, #-0x40]
    // 0xafb734: StoreField: r3->field_b = r0
    //     0xafb734: stur            w0, [x3, #0xb]
    // 0xafb738: r1 = Null
    //     0xafb738: mov             x1, NULL
    // 0xafb73c: r2 = 4
    //     0xafb73c: movz            x2, #0x4
    // 0xafb740: r0 = AllocateArray()
    //     0xafb740: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafb744: mov             x2, x0
    // 0xafb748: ldur            x0, [fp, #-0x50]
    // 0xafb74c: stur            x2, [fp, #-0x40]
    // 0xafb750: StoreField: r2->field_f = r0
    //     0xafb750: stur            w0, [x2, #0xf]
    // 0xafb754: ldur            x0, [fp, #-0x38]
    // 0xafb758: StoreField: r2->field_13 = r0
    //     0xafb758: stur            w0, [x2, #0x13]
    // 0xafb75c: r1 = <Widget>
    //     0xafb75c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafb760: r0 = AllocateGrowableArray()
    //     0xafb760: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafb764: mov             x1, x0
    // 0xafb768: ldur            x0, [fp, #-0x40]
    // 0xafb76c: stur            x1, [fp, #-0x38]
    // 0xafb770: StoreField: r1->field_f = r0
    //     0xafb770: stur            w0, [x1, #0xf]
    // 0xafb774: r2 = 4
    //     0xafb774: movz            x2, #0x4
    // 0xafb778: StoreField: r1->field_b = r2
    //     0xafb778: stur            w2, [x1, #0xb]
    // 0xafb77c: r0 = Column()
    //     0xafb77c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafb780: mov             x2, x0
    // 0xafb784: r0 = Instance_Axis
    //     0xafb784: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafb788: stur            x2, [fp, #-0x40]
    // 0xafb78c: StoreField: r2->field_f = r0
    //     0xafb78c: stur            w0, [x2, #0xf]
    // 0xafb790: r3 = Instance_MainAxisAlignment
    //     0xafb790: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafb794: ldr             x3, [x3, #0xa08]
    // 0xafb798: StoreField: r2->field_13 = r3
    //     0xafb798: stur            w3, [x2, #0x13]
    // 0xafb79c: r4 = Instance_MainAxisSize
    //     0xafb79c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafb7a0: ldr             x4, [x4, #0xa10]
    // 0xafb7a4: ArrayStore: r2[0] = r4  ; List_4
    //     0xafb7a4: stur            w4, [x2, #0x17]
    // 0xafb7a8: r1 = Instance_CrossAxisAlignment
    //     0xafb7a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafb7ac: ldr             x1, [x1, #0x890]
    // 0xafb7b0: StoreField: r2->field_1b = r1
    //     0xafb7b0: stur            w1, [x2, #0x1b]
    // 0xafb7b4: r5 = Instance_VerticalDirection
    //     0xafb7b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafb7b8: ldr             x5, [x5, #0xa20]
    // 0xafb7bc: StoreField: r2->field_23 = r5
    //     0xafb7bc: stur            w5, [x2, #0x23]
    // 0xafb7c0: r6 = Instance_Clip
    //     0xafb7c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafb7c4: ldr             x6, [x6, #0x38]
    // 0xafb7c8: StoreField: r2->field_2b = r6
    //     0xafb7c8: stur            w6, [x2, #0x2b]
    // 0xafb7cc: StoreField: r2->field_2f = rZR
    //     0xafb7cc: stur            xzr, [x2, #0x2f]
    // 0xafb7d0: ldur            x1, [fp, #-0x38]
    // 0xafb7d4: StoreField: r2->field_b = r1
    //     0xafb7d4: stur            w1, [x2, #0xb]
    // 0xafb7d8: ldur            x7, [fp, #-8]
    // 0xafb7dc: LoadField: r1 = r7->field_b
    //     0xafb7dc: ldur            w1, [x7, #0xb]
    // 0xafb7e0: DecompressPointer r1
    //     0xafb7e0: add             x1, x1, HEAP, lsl #32
    // 0xafb7e4: cmp             w1, NULL
    // 0xafb7e8: b.eq            #0xafbd60
    // 0xafb7ec: LoadField: r8 = r1->field_b
    //     0xafb7ec: ldur            w8, [x1, #0xb]
    // 0xafb7f0: DecompressPointer r8
    //     0xafb7f0: add             x8, x8, HEAP, lsl #32
    // 0xafb7f4: LoadField: r1 = r8->field_8f
    //     0xafb7f4: ldur            w1, [x8, #0x8f]
    // 0xafb7f8: DecompressPointer r1
    //     0xafb7f8: add             x1, x1, HEAP, lsl #32
    // 0xafb7fc: cmp             w1, NULL
    // 0xafb800: b.ne            #0xafb80c
    // 0xafb804: r8 = ""
    //     0xafb804: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafb808: b               #0xafb810
    // 0xafb80c: mov             x8, x1
    // 0xafb810: ldur            x1, [fp, #-0x10]
    // 0xafb814: stur            x8, [fp, #-0x38]
    // 0xafb818: r0 = of()
    //     0xafb818: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafb81c: LoadField: r1 = r0->field_87
    //     0xafb81c: ldur            w1, [x0, #0x87]
    // 0xafb820: DecompressPointer r1
    //     0xafb820: add             x1, x1, HEAP, lsl #32
    // 0xafb824: LoadField: r0 = r1->field_f
    //     0xafb824: ldur            w0, [x1, #0xf]
    // 0xafb828: DecompressPointer r0
    //     0xafb828: add             x0, x0, HEAP, lsl #32
    // 0xafb82c: cmp             w0, NULL
    // 0xafb830: b.ne            #0xafb83c
    // 0xafb834: r4 = Null
    //     0xafb834: mov             x4, NULL
    // 0xafb838: b               #0xafb864
    // 0xafb83c: r16 = 12.000000
    //     0xafb83c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafb840: ldr             x16, [x16, #0x9e8]
    // 0xafb844: r30 = Instance_Color
    //     0xafb844: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xafb848: ldr             lr, [lr, #0x858]
    // 0xafb84c: stp             lr, x16, [SP]
    // 0xafb850: mov             x1, x0
    // 0xafb854: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafb854: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafb858: ldr             x4, [x4, #0xaa0]
    // 0xafb85c: r0 = copyWith()
    //     0xafb85c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafb860: mov             x4, x0
    // 0xafb864: ldur            x1, [fp, #-8]
    // 0xafb868: ldur            x3, [fp, #-0x28]
    // 0xafb86c: ldur            x0, [fp, #-0x40]
    // 0xafb870: ldur            x2, [fp, #-0x38]
    // 0xafb874: stur            x4, [fp, #-0x10]
    // 0xafb878: r0 = Text()
    //     0xafb878: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafb87c: mov             x1, x0
    // 0xafb880: ldur            x0, [fp, #-0x38]
    // 0xafb884: stur            x1, [fp, #-0x50]
    // 0xafb888: StoreField: r1->field_b = r0
    //     0xafb888: stur            w0, [x1, #0xb]
    // 0xafb88c: ldur            x0, [fp, #-0x10]
    // 0xafb890: StoreField: r1->field_13 = r0
    //     0xafb890: stur            w0, [x1, #0x13]
    // 0xafb894: r0 = InkWell()
    //     0xafb894: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xafb898: mov             x3, x0
    // 0xafb89c: ldur            x0, [fp, #-0x50]
    // 0xafb8a0: stur            x3, [fp, #-0x10]
    // 0xafb8a4: StoreField: r3->field_b = r0
    //     0xafb8a4: stur            w0, [x3, #0xb]
    // 0xafb8a8: ldur            x2, [fp, #-0x18]
    // 0xafb8ac: r1 = Function '<anonymous closure>':.
    //     0xafb8ac: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6acc0] AnonymousClosure: (0xafcd80), in [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::build (0xafa414)
    //     0xafb8b0: ldr             x1, [x1, #0xcc0]
    // 0xafb8b4: r0 = AllocateClosure()
    //     0xafb8b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafb8b8: mov             x1, x0
    // 0xafb8bc: ldur            x0, [fp, #-0x10]
    // 0xafb8c0: StoreField: r0->field_f = r1
    //     0xafb8c0: stur            w1, [x0, #0xf]
    // 0xafb8c4: r3 = true
    //     0xafb8c4: add             x3, NULL, #0x20  ; true
    // 0xafb8c8: StoreField: r0->field_43 = r3
    //     0xafb8c8: stur            w3, [x0, #0x43]
    // 0xafb8cc: r1 = Instance_BoxShape
    //     0xafb8cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafb8d0: ldr             x1, [x1, #0x80]
    // 0xafb8d4: StoreField: r0->field_47 = r1
    //     0xafb8d4: stur            w1, [x0, #0x47]
    // 0xafb8d8: StoreField: r0->field_6f = r3
    //     0xafb8d8: stur            w3, [x0, #0x6f]
    // 0xafb8dc: r4 = false
    //     0xafb8dc: add             x4, NULL, #0x30  ; false
    // 0xafb8e0: StoreField: r0->field_73 = r4
    //     0xafb8e0: stur            w4, [x0, #0x73]
    // 0xafb8e4: StoreField: r0->field_83 = r3
    //     0xafb8e4: stur            w3, [x0, #0x83]
    // 0xafb8e8: StoreField: r0->field_7b = r4
    //     0xafb8e8: stur            w4, [x0, #0x7b]
    // 0xafb8ec: r1 = Null
    //     0xafb8ec: mov             x1, NULL
    // 0xafb8f0: r2 = 4
    //     0xafb8f0: movz            x2, #0x4
    // 0xafb8f4: r0 = AllocateArray()
    //     0xafb8f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafb8f8: mov             x2, x0
    // 0xafb8fc: ldur            x0, [fp, #-0x40]
    // 0xafb900: stur            x2, [fp, #-0x38]
    // 0xafb904: StoreField: r2->field_f = r0
    //     0xafb904: stur            w0, [x2, #0xf]
    // 0xafb908: ldur            x0, [fp, #-0x10]
    // 0xafb90c: StoreField: r2->field_13 = r0
    //     0xafb90c: stur            w0, [x2, #0x13]
    // 0xafb910: r1 = <Widget>
    //     0xafb910: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafb914: r0 = AllocateGrowableArray()
    //     0xafb914: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafb918: mov             x1, x0
    // 0xafb91c: ldur            x0, [fp, #-0x38]
    // 0xafb920: stur            x1, [fp, #-0x10]
    // 0xafb924: StoreField: r1->field_f = r0
    //     0xafb924: stur            w0, [x1, #0xf]
    // 0xafb928: r0 = 4
    //     0xafb928: movz            x0, #0x4
    // 0xafb92c: StoreField: r1->field_b = r0
    //     0xafb92c: stur            w0, [x1, #0xb]
    // 0xafb930: r0 = Row()
    //     0xafb930: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafb934: mov             x1, x0
    // 0xafb938: r0 = Instance_Axis
    //     0xafb938: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafb93c: stur            x1, [fp, #-0x38]
    // 0xafb940: StoreField: r1->field_f = r0
    //     0xafb940: stur            w0, [x1, #0xf]
    // 0xafb944: r0 = Instance_MainAxisAlignment
    //     0xafb944: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xafb948: ldr             x0, [x0, #0xa8]
    // 0xafb94c: StoreField: r1->field_13 = r0
    //     0xafb94c: stur            w0, [x1, #0x13]
    // 0xafb950: r0 = Instance_MainAxisSize
    //     0xafb950: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafb954: ldr             x0, [x0, #0xa10]
    // 0xafb958: ArrayStore: r1[0] = r0  ; List_4
    //     0xafb958: stur            w0, [x1, #0x17]
    // 0xafb95c: r2 = Instance_CrossAxisAlignment
    //     0xafb95c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafb960: ldr             x2, [x2, #0xa18]
    // 0xafb964: StoreField: r1->field_1b = r2
    //     0xafb964: stur            w2, [x1, #0x1b]
    // 0xafb968: r3 = Instance_VerticalDirection
    //     0xafb968: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafb96c: ldr             x3, [x3, #0xa20]
    // 0xafb970: StoreField: r1->field_23 = r3
    //     0xafb970: stur            w3, [x1, #0x23]
    // 0xafb974: r4 = Instance_Clip
    //     0xafb974: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafb978: ldr             x4, [x4, #0x38]
    // 0xafb97c: StoreField: r1->field_2b = r4
    //     0xafb97c: stur            w4, [x1, #0x2b]
    // 0xafb980: StoreField: r1->field_2f = rZR
    //     0xafb980: stur            xzr, [x1, #0x2f]
    // 0xafb984: ldur            x5, [fp, #-0x10]
    // 0xafb988: StoreField: r1->field_b = r5
    //     0xafb988: stur            w5, [x1, #0xb]
    // 0xafb98c: r0 = Padding()
    //     0xafb98c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafb990: mov             x1, x0
    // 0xafb994: r0 = Instance_EdgeInsets
    //     0xafb994: add             x0, PP, #0x54, lsl #12  ; [pp+0x54a40] Obj!EdgeInsets@d576e1
    //     0xafb998: ldr             x0, [x0, #0xa40]
    // 0xafb99c: stur            x1, [fp, #-0x10]
    // 0xafb9a0: StoreField: r1->field_f = r0
    //     0xafb9a0: stur            w0, [x1, #0xf]
    // 0xafb9a4: ldur            x0, [fp, #-0x38]
    // 0xafb9a8: StoreField: r1->field_b = r0
    //     0xafb9a8: stur            w0, [x1, #0xb]
    // 0xafb9ac: r0 = Visibility()
    //     0xafb9ac: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafb9b0: mov             x1, x0
    // 0xafb9b4: ldur            x0, [fp, #-0x10]
    // 0xafb9b8: stur            x1, [fp, #-0x38]
    // 0xafb9bc: StoreField: r1->field_b = r0
    //     0xafb9bc: stur            w0, [x1, #0xb]
    // 0xafb9c0: r2 = Instance_SizedBox
    //     0xafb9c0: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafb9c4: StoreField: r1->field_f = r2
    //     0xafb9c4: stur            w2, [x1, #0xf]
    // 0xafb9c8: ldur            x0, [fp, #-0x28]
    // 0xafb9cc: StoreField: r1->field_13 = r0
    //     0xafb9cc: stur            w0, [x1, #0x13]
    // 0xafb9d0: r3 = false
    //     0xafb9d0: add             x3, NULL, #0x30  ; false
    // 0xafb9d4: ArrayStore: r1[0] = r3  ; List_4
    //     0xafb9d4: stur            w3, [x1, #0x17]
    // 0xafb9d8: StoreField: r1->field_1b = r3
    //     0xafb9d8: stur            w3, [x1, #0x1b]
    // 0xafb9dc: StoreField: r1->field_1f = r3
    //     0xafb9dc: stur            w3, [x1, #0x1f]
    // 0xafb9e0: StoreField: r1->field_23 = r3
    //     0xafb9e0: stur            w3, [x1, #0x23]
    // 0xafb9e4: StoreField: r1->field_27 = r3
    //     0xafb9e4: stur            w3, [x1, #0x27]
    // 0xafb9e8: StoreField: r1->field_2b = r3
    //     0xafb9e8: stur            w3, [x1, #0x2b]
    // 0xafb9ec: ldur            x4, [fp, #-8]
    // 0xafb9f0: LoadField: r0 = r4->field_b
    //     0xafb9f0: ldur            w0, [x4, #0xb]
    // 0xafb9f4: DecompressPointer r0
    //     0xafb9f4: add             x0, x0, HEAP, lsl #32
    // 0xafb9f8: cmp             w0, NULL
    // 0xafb9fc: b.eq            #0xafbd64
    // 0xafba00: LoadField: r5 = r0->field_b
    //     0xafba00: ldur            w5, [x0, #0xb]
    // 0xafba04: DecompressPointer r5
    //     0xafba04: add             x5, x5, HEAP, lsl #32
    // 0xafba08: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xafba08: ldur            w0, [x5, #0x17]
    // 0xafba0c: DecompressPointer r0
    //     0xafba0c: add             x0, x0, HEAP, lsl #32
    // 0xafba10: r5 = LoadClassIdInstr(r0)
    //     0xafba10: ldur            x5, [x0, #-1]
    //     0xafba14: ubfx            x5, x5, #0xc, #0x14
    // 0xafba18: r16 = "pending"
    //     0xafba18: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ec0] "pending"
    //     0xafba1c: ldr             x16, [x16, #0xec0]
    // 0xafba20: stp             x16, x0, [SP]
    // 0xafba24: mov             x0, x5
    // 0xafba28: mov             lr, x0
    // 0xafba2c: ldr             lr, [x21, lr, lsl #3]
    // 0xafba30: blr             lr
    // 0xafba34: eor             x1, x0, #0x10
    // 0xafba38: stur            x1, [fp, #-0x10]
    // 0xafba3c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafba3c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafba40: ldr             x0, [x0, #0x1c80]
    //     0xafba44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafba48: cmp             w0, w16
    //     0xafba4c: b.ne            #0xafba58
    //     0xafba50: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xafba54: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xafba58: r0 = GetNavigation.size()
    //     0xafba58: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xafba5c: LoadField: d0 = r0->field_7
    //     0xafba5c: ldur            d0, [x0, #7]
    // 0xafba60: ldur            x0, [fp, #-8]
    // 0xafba64: stur            d0, [fp, #-0x80]
    // 0xafba68: LoadField: r1 = r0->field_b
    //     0xafba68: ldur            w1, [x0, #0xb]
    // 0xafba6c: DecompressPointer r1
    //     0xafba6c: add             x1, x1, HEAP, lsl #32
    // 0xafba70: cmp             w1, NULL
    // 0xafba74: b.eq            #0xafbd68
    // 0xafba78: LoadField: r0 = r1->field_b
    //     0xafba78: ldur            w0, [x1, #0xb]
    // 0xafba7c: DecompressPointer r0
    //     0xafba7c: add             x0, x0, HEAP, lsl #32
    // 0xafba80: LoadField: r1 = r0->field_57
    //     0xafba80: ldur            w1, [x0, #0x57]
    // 0xafba84: DecompressPointer r1
    //     0xafba84: add             x1, x1, HEAP, lsl #32
    // 0xafba88: cmp             w1, NULL
    // 0xafba8c: b.ne            #0xafba98
    // 0xafba90: r0 = Null
    //     0xafba90: mov             x0, NULL
    // 0xafba94: b               #0xafba9c
    // 0xafba98: LoadField: r0 = r1->field_b
    //     0xafba98: ldur            w0, [x1, #0xb]
    // 0xafba9c: cmp             w0, NULL
    // 0xafbaa0: b.ne            #0xafbaac
    // 0xafbaa4: r7 = 0
    //     0xafbaa4: movz            x7, #0
    // 0xafbaa8: b               #0xafbab4
    // 0xafbaac: r1 = LoadInt32Instr(r0)
    //     0xafbaac: sbfx            x1, x0, #1, #0x1f
    // 0xafbab0: mov             x7, x1
    // 0xafbab4: ldur            x6, [fp, #-0x30]
    // 0xafbab8: ldur            x5, [fp, #-0x20]
    // 0xafbabc: ldur            x4, [fp, #-0x48]
    // 0xafbac0: ldur            x3, [fp, #-0x38]
    // 0xafbac4: ldur            x0, [fp, #-0x10]
    // 0xafbac8: stur            x7, [fp, #-0x78]
    // 0xafbacc: r1 = Function '<anonymous closure>':.
    //     0xafbacc: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6acc8] AnonymousClosure: (0xa6d658), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xafbad0: ldr             x1, [x1, #0xcc8]
    // 0xafbad4: r2 = Null
    //     0xafbad4: mov             x2, NULL
    // 0xafbad8: r0 = AllocateClosure()
    //     0xafbad8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafbadc: ldur            x2, [fp, #-0x18]
    // 0xafbae0: r1 = Function '<anonymous closure>':.
    //     0xafbae0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6acd0] AnonymousClosure: (0xafbd84), in [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::build (0xafa414)
    //     0xafbae4: ldr             x1, [x1, #0xcd0]
    // 0xafbae8: stur            x0, [fp, #-8]
    // 0xafbaec: r0 = AllocateClosure()
    //     0xafbaec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafbaf0: stur            x0, [fp, #-0x18]
    // 0xafbaf4: r0 = ListView()
    //     0xafbaf4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xafbaf8: stur            x0, [fp, #-0x28]
    // 0xafbafc: r16 = Instance_NeverScrollableScrollPhysics
    //     0xafbafc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xafbb00: ldr             x16, [x16, #0x1c8]
    // 0xafbb04: r30 = Instance_Axis
    //     0xafbb04: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafbb08: stp             lr, x16, [SP, #8]
    // 0xafbb0c: r16 = true
    //     0xafbb0c: add             x16, NULL, #0x20  ; true
    // 0xafbb10: str             x16, [SP]
    // 0xafbb14: mov             x1, x0
    // 0xafbb18: ldur            x2, [fp, #-0x18]
    // 0xafbb1c: ldur            x3, [fp, #-0x78]
    // 0xafbb20: ldur            x5, [fp, #-8]
    // 0xafbb24: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x5, shrinkWrap, 0x6, null]
    //     0xafbb24: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a228] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x6, Null]
    //     0xafbb28: ldr             x4, [x4, #0x228]
    // 0xafbb2c: r0 = ListView.separated()
    //     0xafbb2c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xafbb30: r0 = Align()
    //     0xafbb30: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xafbb34: mov             x1, x0
    // 0xafbb38: r0 = Instance_Alignment
    //     0xafbb38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xafbb3c: ldr             x0, [x0, #0xb10]
    // 0xafbb40: stur            x1, [fp, #-0x18]
    // 0xafbb44: StoreField: r1->field_f = r0
    //     0xafbb44: stur            w0, [x1, #0xf]
    // 0xafbb48: ldur            x0, [fp, #-0x28]
    // 0xafbb4c: StoreField: r1->field_b = r0
    //     0xafbb4c: stur            w0, [x1, #0xb]
    // 0xafbb50: ldur            d0, [fp, #-0x80]
    // 0xafbb54: r0 = inline_Allocate_Double()
    //     0xafbb54: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xafbb58: add             x0, x0, #0x10
    //     0xafbb5c: cmp             x2, x0
    //     0xafbb60: b.ls            #0xafbd6c
    //     0xafbb64: str             x0, [THR, #0x50]  ; THR::top
    //     0xafbb68: sub             x0, x0, #0xf
    //     0xafbb6c: movz            x2, #0xe15c
    //     0xafbb70: movk            x2, #0x3, lsl #16
    //     0xafbb74: stur            x2, [x0, #-1]
    // 0xafbb78: StoreField: r0->field_7 = d0
    //     0xafbb78: stur            d0, [x0, #7]
    // 0xafbb7c: stur            x0, [fp, #-8]
    // 0xafbb80: r0 = SizedBox()
    //     0xafbb80: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xafbb84: mov             x1, x0
    // 0xafbb88: ldur            x0, [fp, #-8]
    // 0xafbb8c: stur            x1, [fp, #-0x28]
    // 0xafbb90: StoreField: r1->field_f = r0
    //     0xafbb90: stur            w0, [x1, #0xf]
    // 0xafbb94: r0 = 25.000000
    //     0xafbb94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0xafbb98: ldr             x0, [x0, #0x98]
    // 0xafbb9c: StoreField: r1->field_13 = r0
    //     0xafbb9c: stur            w0, [x1, #0x13]
    // 0xafbba0: ldur            x0, [fp, #-0x18]
    // 0xafbba4: StoreField: r1->field_b = r0
    //     0xafbba4: stur            w0, [x1, #0xb]
    // 0xafbba8: r0 = Padding()
    //     0xafbba8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafbbac: mov             x1, x0
    // 0xafbbb0: r0 = Instance_EdgeInsets
    //     0xafbbb0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0xafbbb4: ldr             x0, [x0, #0x868]
    // 0xafbbb8: stur            x1, [fp, #-8]
    // 0xafbbbc: StoreField: r1->field_f = r0
    //     0xafbbbc: stur            w0, [x1, #0xf]
    // 0xafbbc0: ldur            x0, [fp, #-0x28]
    // 0xafbbc4: StoreField: r1->field_b = r0
    //     0xafbbc4: stur            w0, [x1, #0xb]
    // 0xafbbc8: r0 = Visibility()
    //     0xafbbc8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xafbbcc: mov             x3, x0
    // 0xafbbd0: ldur            x0, [fp, #-8]
    // 0xafbbd4: stur            x3, [fp, #-0x18]
    // 0xafbbd8: StoreField: r3->field_b = r0
    //     0xafbbd8: stur            w0, [x3, #0xb]
    // 0xafbbdc: r0 = Instance_SizedBox
    //     0xafbbdc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xafbbe0: StoreField: r3->field_f = r0
    //     0xafbbe0: stur            w0, [x3, #0xf]
    // 0xafbbe4: ldur            x0, [fp, #-0x10]
    // 0xafbbe8: StoreField: r3->field_13 = r0
    //     0xafbbe8: stur            w0, [x3, #0x13]
    // 0xafbbec: r0 = false
    //     0xafbbec: add             x0, NULL, #0x30  ; false
    // 0xafbbf0: ArrayStore: r3[0] = r0  ; List_4
    //     0xafbbf0: stur            w0, [x3, #0x17]
    // 0xafbbf4: StoreField: r3->field_1b = r0
    //     0xafbbf4: stur            w0, [x3, #0x1b]
    // 0xafbbf8: StoreField: r3->field_1f = r0
    //     0xafbbf8: stur            w0, [x3, #0x1f]
    // 0xafbbfc: StoreField: r3->field_23 = r0
    //     0xafbbfc: stur            w0, [x3, #0x23]
    // 0xafbc00: StoreField: r3->field_27 = r0
    //     0xafbc00: stur            w0, [x3, #0x27]
    // 0xafbc04: StoreField: r3->field_2b = r0
    //     0xafbc04: stur            w0, [x3, #0x2b]
    // 0xafbc08: r1 = Null
    //     0xafbc08: mov             x1, NULL
    // 0xafbc0c: r2 = 8
    //     0xafbc0c: movz            x2, #0x8
    // 0xafbc10: r0 = AllocateArray()
    //     0xafbc10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafbc14: mov             x2, x0
    // 0xafbc18: ldur            x0, [fp, #-0x20]
    // 0xafbc1c: stur            x2, [fp, #-8]
    // 0xafbc20: StoreField: r2->field_f = r0
    //     0xafbc20: stur            w0, [x2, #0xf]
    // 0xafbc24: ldur            x0, [fp, #-0x48]
    // 0xafbc28: StoreField: r2->field_13 = r0
    //     0xafbc28: stur            w0, [x2, #0x13]
    // 0xafbc2c: ldur            x0, [fp, #-0x38]
    // 0xafbc30: ArrayStore: r2[0] = r0  ; List_4
    //     0xafbc30: stur            w0, [x2, #0x17]
    // 0xafbc34: ldur            x0, [fp, #-0x18]
    // 0xafbc38: StoreField: r2->field_1b = r0
    //     0xafbc38: stur            w0, [x2, #0x1b]
    // 0xafbc3c: r1 = <Widget>
    //     0xafbc3c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafbc40: r0 = AllocateGrowableArray()
    //     0xafbc40: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafbc44: mov             x1, x0
    // 0xafbc48: ldur            x0, [fp, #-8]
    // 0xafbc4c: stur            x1, [fp, #-0x10]
    // 0xafbc50: StoreField: r1->field_f = r0
    //     0xafbc50: stur            w0, [x1, #0xf]
    // 0xafbc54: r0 = 8
    //     0xafbc54: movz            x0, #0x8
    // 0xafbc58: StoreField: r1->field_b = r0
    //     0xafbc58: stur            w0, [x1, #0xb]
    // 0xafbc5c: r0 = Column()
    //     0xafbc5c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafbc60: mov             x1, x0
    // 0xafbc64: r0 = Instance_Axis
    //     0xafbc64: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafbc68: stur            x1, [fp, #-8]
    // 0xafbc6c: StoreField: r1->field_f = r0
    //     0xafbc6c: stur            w0, [x1, #0xf]
    // 0xafbc70: r0 = Instance_MainAxisAlignment
    //     0xafbc70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafbc74: ldr             x0, [x0, #0xa08]
    // 0xafbc78: StoreField: r1->field_13 = r0
    //     0xafbc78: stur            w0, [x1, #0x13]
    // 0xafbc7c: r0 = Instance_MainAxisSize
    //     0xafbc7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafbc80: ldr             x0, [x0, #0xa10]
    // 0xafbc84: ArrayStore: r1[0] = r0  ; List_4
    //     0xafbc84: stur            w0, [x1, #0x17]
    // 0xafbc88: r0 = Instance_CrossAxisAlignment
    //     0xafbc88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafbc8c: ldr             x0, [x0, #0xa18]
    // 0xafbc90: StoreField: r1->field_1b = r0
    //     0xafbc90: stur            w0, [x1, #0x1b]
    // 0xafbc94: r0 = Instance_VerticalDirection
    //     0xafbc94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafbc98: ldr             x0, [x0, #0xa20]
    // 0xafbc9c: StoreField: r1->field_23 = r0
    //     0xafbc9c: stur            w0, [x1, #0x23]
    // 0xafbca0: r0 = Instance_Clip
    //     0xafbca0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafbca4: ldr             x0, [x0, #0x38]
    // 0xafbca8: StoreField: r1->field_2b = r0
    //     0xafbca8: stur            w0, [x1, #0x2b]
    // 0xafbcac: StoreField: r1->field_2f = rZR
    //     0xafbcac: stur            xzr, [x1, #0x2f]
    // 0xafbcb0: ldur            x0, [fp, #-0x10]
    // 0xafbcb4: StoreField: r1->field_b = r0
    //     0xafbcb4: stur            w0, [x1, #0xb]
    // 0xafbcb8: r0 = Padding()
    //     0xafbcb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafbcbc: mov             x1, x0
    // 0xafbcc0: r0 = Instance_EdgeInsets
    //     0xafbcc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xafbcc4: ldr             x0, [x0, #0x1f0]
    // 0xafbcc8: stur            x1, [fp, #-0x10]
    // 0xafbccc: StoreField: r1->field_f = r0
    //     0xafbccc: stur            w0, [x1, #0xf]
    // 0xafbcd0: ldur            x0, [fp, #-8]
    // 0xafbcd4: StoreField: r1->field_b = r0
    //     0xafbcd4: stur            w0, [x1, #0xb]
    // 0xafbcd8: r0 = Card()
    //     0xafbcd8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xafbcdc: r1 = 0.000000
    //     0xafbcdc: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xafbce0: ArrayStore: r0[0] = r1  ; List_4
    //     0xafbce0: stur            w1, [x0, #0x17]
    // 0xafbce4: ldur            x1, [fp, #-0x30]
    // 0xafbce8: StoreField: r0->field_1b = r1
    //     0xafbce8: stur            w1, [x0, #0x1b]
    // 0xafbcec: r1 = true
    //     0xafbcec: add             x1, NULL, #0x20  ; true
    // 0xafbcf0: StoreField: r0->field_1f = r1
    //     0xafbcf0: stur            w1, [x0, #0x1f]
    // 0xafbcf4: ldur            x2, [fp, #-0x10]
    // 0xafbcf8: StoreField: r0->field_2f = r2
    //     0xafbcf8: stur            w2, [x0, #0x2f]
    // 0xafbcfc: StoreField: r0->field_2b = r1
    //     0xafbcfc: stur            w1, [x0, #0x2b]
    // 0xafbd00: r1 = Instance__CardVariant
    //     0xafbd00: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xafbd04: ldr             x1, [x1, #0xa68]
    // 0xafbd08: StoreField: r0->field_33 = r1
    //     0xafbd08: stur            w1, [x0, #0x33]
    // 0xafbd0c: LeaveFrame
    //     0xafbd0c: mov             SP, fp
    //     0xafbd10: ldp             fp, lr, [SP], #0x10
    // 0xafbd14: ret
    //     0xafbd14: ret             
    // 0xafbd18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafbd18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafbd1c: b               #0xafa43c
    // 0xafbd20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafbd64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafbd68: r0 = NullCastErrorSharedWithFPURegs()
    //     0xafbd68: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xafbd6c: SaveReg d0
    //     0xafbd6c: str             q0, [SP, #-0x10]!
    // 0xafbd70: SaveReg r1
    //     0xafbd70: str             x1, [SP, #-8]!
    // 0xafbd74: r0 = AllocateDouble()
    //     0xafbd74: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xafbd78: RestoreReg r1
    //     0xafbd78: ldr             x1, [SP], #8
    // 0xafbd7c: RestoreReg d0
    //     0xafbd7c: ldr             q0, [SP], #0x10
    // 0xafbd80: b               #0xafbb78
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xafbd84, size: 0x83c
    // 0xafbd84: EnterFrame
    //     0xafbd84: stp             fp, lr, [SP, #-0x10]!
    //     0xafbd88: mov             fp, SP
    // 0xafbd8c: AllocStack(0x50)
    //     0xafbd8c: sub             SP, SP, #0x50
    // 0xafbd90: SetupParameters()
    //     0xafbd90: ldr             x0, [fp, #0x20]
    //     0xafbd94: ldur            w1, [x0, #0x17]
    //     0xafbd98: add             x1, x1, HEAP, lsl #32
    //     0xafbd9c: stur            x1, [fp, #-8]
    // 0xafbda0: CheckStackOverflow
    //     0xafbda0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafbda4: cmp             SP, x16
    //     0xafbda8: b.ls            #0xafc58c
    // 0xafbdac: r1 = 2
    //     0xafbdac: movz            x1, #0x2
    // 0xafbdb0: r0 = AllocateContext()
    //     0xafbdb0: bl              #0x16f6108  ; AllocateContextStub
    // 0xafbdb4: mov             x3, x0
    // 0xafbdb8: ldur            x0, [fp, #-8]
    // 0xafbdbc: stur            x3, [fp, #-0x10]
    // 0xafbdc0: StoreField: r3->field_b = r0
    //     0xafbdc0: stur            w0, [x3, #0xb]
    // 0xafbdc4: ldr             x4, [fp, #0x18]
    // 0xafbdc8: StoreField: r3->field_f = r4
    //     0xafbdc8: stur            w4, [x3, #0xf]
    // 0xafbdcc: ldr             x5, [fp, #0x10]
    // 0xafbdd0: StoreField: r3->field_13 = r5
    //     0xafbdd0: stur            w5, [x3, #0x13]
    // 0xafbdd4: r1 = <Widget>
    //     0xafbdd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafbdd8: r2 = 0
    //     0xafbdd8: movz            x2, #0
    // 0xafbddc: r0 = _GrowableList()
    //     0xafbddc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xafbde0: mov             x3, x0
    // 0xafbde4: ldur            x2, [fp, #-8]
    // 0xafbde8: stur            x3, [fp, #-0x18]
    // 0xafbdec: LoadField: r0 = r2->field_f
    //     0xafbdec: ldur            w0, [x2, #0xf]
    // 0xafbdf0: DecompressPointer r0
    //     0xafbdf0: add             x0, x0, HEAP, lsl #32
    // 0xafbdf4: LoadField: r1 = r0->field_b
    //     0xafbdf4: ldur            w1, [x0, #0xb]
    // 0xafbdf8: DecompressPointer r1
    //     0xafbdf8: add             x1, x1, HEAP, lsl #32
    // 0xafbdfc: cmp             w1, NULL
    // 0xafbe00: b.eq            #0xafc594
    // 0xafbe04: LoadField: r0 = r1->field_b
    //     0xafbe04: ldur            w0, [x1, #0xb]
    // 0xafbe08: DecompressPointer r0
    //     0xafbe08: add             x0, x0, HEAP, lsl #32
    // 0xafbe0c: LoadField: r4 = r0->field_57
    //     0xafbe0c: ldur            w4, [x0, #0x57]
    // 0xafbe10: DecompressPointer r4
    //     0xafbe10: add             x4, x4, HEAP, lsl #32
    // 0xafbe14: cmp             w4, NULL
    // 0xafbe18: b.ne            #0xafbe28
    // 0xafbe1c: ldr             x5, [fp, #0x10]
    // 0xafbe20: r0 = Null
    //     0xafbe20: mov             x0, NULL
    // 0xafbe24: b               #0xafbe7c
    // 0xafbe28: ldr             x5, [fp, #0x10]
    // 0xafbe2c: LoadField: r0 = r4->field_b
    //     0xafbe2c: ldur            w0, [x4, #0xb]
    // 0xafbe30: r6 = LoadInt32Instr(r5)
    //     0xafbe30: sbfx            x6, x5, #1, #0x1f
    //     0xafbe34: tbz             w5, #0, #0xafbe3c
    //     0xafbe38: ldur            x6, [x5, #7]
    // 0xafbe3c: r1 = LoadInt32Instr(r0)
    //     0xafbe3c: sbfx            x1, x0, #1, #0x1f
    // 0xafbe40: mov             x0, x1
    // 0xafbe44: mov             x1, x6
    // 0xafbe48: cmp             x1, x0
    // 0xafbe4c: b.hs            #0xafc598
    // 0xafbe50: LoadField: r0 = r4->field_f
    //     0xafbe50: ldur            w0, [x4, #0xf]
    // 0xafbe54: DecompressPointer r0
    //     0xafbe54: add             x0, x0, HEAP, lsl #32
    // 0xafbe58: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xafbe58: add             x16, x0, x6, lsl #2
    //     0xafbe5c: ldur            w1, [x16, #0xf]
    // 0xafbe60: DecompressPointer r1
    //     0xafbe60: add             x1, x1, HEAP, lsl #32
    // 0xafbe64: cmp             w1, NULL
    // 0xafbe68: b.ne            #0xafbe74
    // 0xafbe6c: r0 = Null
    //     0xafbe6c: mov             x0, NULL
    // 0xafbe70: b               #0xafbe7c
    // 0xafbe74: LoadField: r0 = r1->field_7
    //     0xafbe74: ldur            w0, [x1, #7]
    // 0xafbe78: DecompressPointer r0
    //     0xafbe78: add             x0, x0, HEAP, lsl #32
    // 0xafbe7c: r1 = LoadClassIdInstr(r0)
    //     0xafbe7c: ldur            x1, [x0, #-1]
    //     0xafbe80: ubfx            x1, x1, #0xc, #0x14
    // 0xafbe84: r16 = "cancel_order"
    //     0xafbe84: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xafbe88: ldr             x16, [x16, #0x98]
    // 0xafbe8c: stp             x16, x0, [SP]
    // 0xafbe90: mov             x0, x1
    // 0xafbe94: mov             lr, x0
    // 0xafbe98: ldr             lr, [x21, lr, lsl #3]
    // 0xafbe9c: blr             lr
    // 0xafbea0: tbz             w0, #4, #0xafc01c
    // 0xafbea4: ldur            x2, [fp, #-8]
    // 0xafbea8: LoadField: r0 = r2->field_f
    //     0xafbea8: ldur            w0, [x2, #0xf]
    // 0xafbeac: DecompressPointer r0
    //     0xafbeac: add             x0, x0, HEAP, lsl #32
    // 0xafbeb0: LoadField: r1 = r0->field_b
    //     0xafbeb0: ldur            w1, [x0, #0xb]
    // 0xafbeb4: DecompressPointer r1
    //     0xafbeb4: add             x1, x1, HEAP, lsl #32
    // 0xafbeb8: cmp             w1, NULL
    // 0xafbebc: b.eq            #0xafc59c
    // 0xafbec0: LoadField: r0 = r1->field_b
    //     0xafbec0: ldur            w0, [x1, #0xb]
    // 0xafbec4: DecompressPointer r0
    //     0xafbec4: add             x0, x0, HEAP, lsl #32
    // 0xafbec8: LoadField: r3 = r0->field_57
    //     0xafbec8: ldur            w3, [x0, #0x57]
    // 0xafbecc: DecompressPointer r3
    //     0xafbecc: add             x3, x3, HEAP, lsl #32
    // 0xafbed0: cmp             w3, NULL
    // 0xafbed4: b.ne            #0xafbee4
    // 0xafbed8: ldr             x4, [fp, #0x10]
    // 0xafbedc: r0 = Null
    //     0xafbedc: mov             x0, NULL
    // 0xafbee0: b               #0xafbf38
    // 0xafbee4: ldr             x4, [fp, #0x10]
    // 0xafbee8: LoadField: r0 = r3->field_b
    //     0xafbee8: ldur            w0, [x3, #0xb]
    // 0xafbeec: r5 = LoadInt32Instr(r4)
    //     0xafbeec: sbfx            x5, x4, #1, #0x1f
    //     0xafbef0: tbz             w4, #0, #0xafbef8
    //     0xafbef4: ldur            x5, [x4, #7]
    // 0xafbef8: r1 = LoadInt32Instr(r0)
    //     0xafbef8: sbfx            x1, x0, #1, #0x1f
    // 0xafbefc: mov             x0, x1
    // 0xafbf00: mov             x1, x5
    // 0xafbf04: cmp             x1, x0
    // 0xafbf08: b.hs            #0xafc5a0
    // 0xafbf0c: LoadField: r0 = r3->field_f
    //     0xafbf0c: ldur            w0, [x3, #0xf]
    // 0xafbf10: DecompressPointer r0
    //     0xafbf10: add             x0, x0, HEAP, lsl #32
    // 0xafbf14: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xafbf14: add             x16, x0, x5, lsl #2
    //     0xafbf18: ldur            w1, [x16, #0xf]
    // 0xafbf1c: DecompressPointer r1
    //     0xafbf1c: add             x1, x1, HEAP, lsl #32
    // 0xafbf20: cmp             w1, NULL
    // 0xafbf24: b.ne            #0xafbf30
    // 0xafbf28: r0 = Null
    //     0xafbf28: mov             x0, NULL
    // 0xafbf2c: b               #0xafbf38
    // 0xafbf30: LoadField: r0 = r1->field_7
    //     0xafbf30: ldur            w0, [x1, #7]
    // 0xafbf34: DecompressPointer r0
    //     0xafbf34: add             x0, x0, HEAP, lsl #32
    // 0xafbf38: r1 = LoadClassIdInstr(r0)
    //     0xafbf38: ldur            x1, [x0, #-1]
    //     0xafbf3c: ubfx            x1, x1, #0xc, #0x14
    // 0xafbf40: r16 = "exchange_cancel"
    //     0xafbf40: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] "exchange_cancel"
    //     0xafbf44: ldr             x16, [x16, #0xdf8]
    // 0xafbf48: stp             x16, x0, [SP]
    // 0xafbf4c: mov             x0, x1
    // 0xafbf50: mov             lr, x0
    // 0xafbf54: ldr             lr, [x21, lr, lsl #3]
    // 0xafbf58: blr             lr
    // 0xafbf5c: tbz             w0, #4, #0xafc01c
    // 0xafbf60: ldur            x2, [fp, #-8]
    // 0xafbf64: LoadField: r0 = r2->field_f
    //     0xafbf64: ldur            w0, [x2, #0xf]
    // 0xafbf68: DecompressPointer r0
    //     0xafbf68: add             x0, x0, HEAP, lsl #32
    // 0xafbf6c: LoadField: r1 = r0->field_b
    //     0xafbf6c: ldur            w1, [x0, #0xb]
    // 0xafbf70: DecompressPointer r1
    //     0xafbf70: add             x1, x1, HEAP, lsl #32
    // 0xafbf74: cmp             w1, NULL
    // 0xafbf78: b.eq            #0xafc5a4
    // 0xafbf7c: LoadField: r0 = r1->field_b
    //     0xafbf7c: ldur            w0, [x1, #0xb]
    // 0xafbf80: DecompressPointer r0
    //     0xafbf80: add             x0, x0, HEAP, lsl #32
    // 0xafbf84: LoadField: r3 = r0->field_57
    //     0xafbf84: ldur            w3, [x0, #0x57]
    // 0xafbf88: DecompressPointer r3
    //     0xafbf88: add             x3, x3, HEAP, lsl #32
    // 0xafbf8c: cmp             w3, NULL
    // 0xafbf90: b.ne            #0xafbfa0
    // 0xafbf94: ldr             x4, [fp, #0x10]
    // 0xafbf98: r0 = Null
    //     0xafbf98: mov             x0, NULL
    // 0xafbf9c: b               #0xafbff4
    // 0xafbfa0: ldr             x4, [fp, #0x10]
    // 0xafbfa4: LoadField: r0 = r3->field_b
    //     0xafbfa4: ldur            w0, [x3, #0xb]
    // 0xafbfa8: r5 = LoadInt32Instr(r4)
    //     0xafbfa8: sbfx            x5, x4, #1, #0x1f
    //     0xafbfac: tbz             w4, #0, #0xafbfb4
    //     0xafbfb0: ldur            x5, [x4, #7]
    // 0xafbfb4: r1 = LoadInt32Instr(r0)
    //     0xafbfb4: sbfx            x1, x0, #1, #0x1f
    // 0xafbfb8: mov             x0, x1
    // 0xafbfbc: mov             x1, x5
    // 0xafbfc0: cmp             x1, x0
    // 0xafbfc4: b.hs            #0xafc5a8
    // 0xafbfc8: LoadField: r0 = r3->field_f
    //     0xafbfc8: ldur            w0, [x3, #0xf]
    // 0xafbfcc: DecompressPointer r0
    //     0xafbfcc: add             x0, x0, HEAP, lsl #32
    // 0xafbfd0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xafbfd0: add             x16, x0, x5, lsl #2
    //     0xafbfd4: ldur            w1, [x16, #0xf]
    // 0xafbfd8: DecompressPointer r1
    //     0xafbfd8: add             x1, x1, HEAP, lsl #32
    // 0xafbfdc: cmp             w1, NULL
    // 0xafbfe0: b.ne            #0xafbfec
    // 0xafbfe4: r0 = Null
    //     0xafbfe4: mov             x0, NULL
    // 0xafbfe8: b               #0xafbff4
    // 0xafbfec: LoadField: r0 = r1->field_7
    //     0xafbfec: ldur            w0, [x1, #7]
    // 0xafbff0: DecompressPointer r0
    //     0xafbff0: add             x0, x0, HEAP, lsl #32
    // 0xafbff4: r1 = LoadClassIdInstr(r0)
    //     0xafbff4: ldur            x1, [x0, #-1]
    //     0xafbff8: ubfx            x1, x1, #0xc, #0x14
    // 0xafbffc: r16 = "cancel_return"
    //     0xafbffc: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df0] "cancel_return"
    //     0xafc000: ldr             x16, [x16, #0xdf0]
    // 0xafc004: stp             x16, x0, [SP]
    // 0xafc008: mov             x0, x1
    // 0xafc00c: mov             lr, x0
    // 0xafc010: ldr             lr, [x21, lr, lsl #3]
    // 0xafc014: blr             lr
    // 0xafc018: tbnz            w0, #4, #0xafc128
    // 0xafc01c: ldur            x0, [fp, #-0x18]
    // 0xafc020: ldr             x1, [fp, #0x18]
    // 0xafc024: r0 = of()
    //     0xafc024: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafc028: LoadField: r1 = r0->field_5b
    //     0xafc028: ldur            w1, [x0, #0x5b]
    // 0xafc02c: DecompressPointer r1
    //     0xafc02c: add             x1, x1, HEAP, lsl #32
    // 0xafc030: stur            x1, [fp, #-0x20]
    // 0xafc034: r0 = ColorFilter()
    //     0xafc034: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xafc038: mov             x1, x0
    // 0xafc03c: ldur            x0, [fp, #-0x20]
    // 0xafc040: stur            x1, [fp, #-0x28]
    // 0xafc044: StoreField: r1->field_7 = r0
    //     0xafc044: stur            w0, [x1, #7]
    // 0xafc048: r2 = Instance_BlendMode
    //     0xafc048: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xafc04c: ldr             x2, [x2, #0xb30]
    // 0xafc050: StoreField: r1->field_b = r2
    //     0xafc050: stur            w2, [x1, #0xb]
    // 0xafc054: r3 = 1
    //     0xafc054: movz            x3, #0x1
    // 0xafc058: StoreField: r1->field_13 = r3
    //     0xafc058: stur            x3, [x1, #0x13]
    // 0xafc05c: r0 = SvgPicture()
    //     0xafc05c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xafc060: stur            x0, [fp, #-0x20]
    // 0xafc064: r16 = 16.000000
    //     0xafc064: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xafc068: ldr             x16, [x16, #0x188]
    // 0xafc06c: r30 = 16.000000
    //     0xafc06c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xafc070: ldr             lr, [lr, #0x188]
    // 0xafc074: stp             lr, x16, [SP, #0x10]
    // 0xafc078: r16 = Instance_BoxFit
    //     0xafc078: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xafc07c: ldr             x16, [x16, #0xb18]
    // 0xafc080: ldur            lr, [fp, #-0x28]
    // 0xafc084: stp             lr, x16, [SP]
    // 0xafc088: mov             x1, x0
    // 0xafc08c: r2 = "assets/images/x-circle.svg"
    //     0xafc08c: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a230] "assets/images/x-circle.svg"
    //     0xafc090: ldr             x2, [x2, #0x230]
    // 0xafc094: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xafc094: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a238] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xafc098: ldr             x4, [x4, #0x238]
    // 0xafc09c: r0 = SvgPicture.asset()
    //     0xafc09c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xafc0a0: ldur            x0, [fp, #-0x18]
    // 0xafc0a4: LoadField: r1 = r0->field_b
    //     0xafc0a4: ldur            w1, [x0, #0xb]
    // 0xafc0a8: LoadField: r2 = r0->field_f
    //     0xafc0a8: ldur            w2, [x0, #0xf]
    // 0xafc0ac: DecompressPointer r2
    //     0xafc0ac: add             x2, x2, HEAP, lsl #32
    // 0xafc0b0: LoadField: r3 = r2->field_b
    //     0xafc0b0: ldur            w3, [x2, #0xb]
    // 0xafc0b4: r2 = LoadInt32Instr(r1)
    //     0xafc0b4: sbfx            x2, x1, #1, #0x1f
    // 0xafc0b8: stur            x2, [fp, #-0x30]
    // 0xafc0bc: r1 = LoadInt32Instr(r3)
    //     0xafc0bc: sbfx            x1, x3, #1, #0x1f
    // 0xafc0c0: cmp             x2, x1
    // 0xafc0c4: b.ne            #0xafc0d0
    // 0xafc0c8: mov             x1, x0
    // 0xafc0cc: r0 = _growToNextCapacity()
    //     0xafc0cc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafc0d0: ldur            x4, [fp, #-0x18]
    // 0xafc0d4: ldur            x2, [fp, #-0x30]
    // 0xafc0d8: add             x3, x2, #1
    // 0xafc0dc: lsl             x0, x3, #1
    // 0xafc0e0: StoreField: r4->field_b = r0
    //     0xafc0e0: stur            w0, [x4, #0xb]
    // 0xafc0e4: LoadField: r5 = r4->field_f
    //     0xafc0e4: ldur            w5, [x4, #0xf]
    // 0xafc0e8: DecompressPointer r5
    //     0xafc0e8: add             x5, x5, HEAP, lsl #32
    // 0xafc0ec: mov             x1, x5
    // 0xafc0f0: ldur            x0, [fp, #-0x20]
    // 0xafc0f4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xafc0f4: add             x25, x1, x2, lsl #2
    //     0xafc0f8: add             x25, x25, #0xf
    //     0xafc0fc: str             w0, [x25]
    //     0xafc100: tbz             w0, #0, #0xafc11c
    //     0xafc104: ldurb           w16, [x1, #-1]
    //     0xafc108: ldurb           w17, [x0, #-1]
    //     0xafc10c: and             x16, x17, x16, lsr #2
    //     0xafc110: tst             x16, HEAP, lsr #32
    //     0xafc114: b.eq            #0xafc11c
    //     0xafc118: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafc11c: mov             x0, x5
    // 0xafc120: mov             x2, x4
    // 0xafc124: b               #0xafc30c
    // 0xafc128: ldur            x5, [fp, #-8]
    // 0xafc12c: ldur            x4, [fp, #-0x18]
    // 0xafc130: r2 = Instance_BlendMode
    //     0xafc130: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xafc134: ldr             x2, [x2, #0xb30]
    // 0xafc138: r3 = 1
    //     0xafc138: movz            x3, #0x1
    // 0xafc13c: LoadField: r0 = r5->field_f
    //     0xafc13c: ldur            w0, [x5, #0xf]
    // 0xafc140: DecompressPointer r0
    //     0xafc140: add             x0, x0, HEAP, lsl #32
    // 0xafc144: LoadField: r1 = r0->field_b
    //     0xafc144: ldur            w1, [x0, #0xb]
    // 0xafc148: DecompressPointer r1
    //     0xafc148: add             x1, x1, HEAP, lsl #32
    // 0xafc14c: cmp             w1, NULL
    // 0xafc150: b.eq            #0xafc5ac
    // 0xafc154: LoadField: r0 = r1->field_b
    //     0xafc154: ldur            w0, [x1, #0xb]
    // 0xafc158: DecompressPointer r0
    //     0xafc158: add             x0, x0, HEAP, lsl #32
    // 0xafc15c: LoadField: r6 = r0->field_57
    //     0xafc15c: ldur            w6, [x0, #0x57]
    // 0xafc160: DecompressPointer r6
    //     0xafc160: add             x6, x6, HEAP, lsl #32
    // 0xafc164: cmp             w6, NULL
    // 0xafc168: b.ne            #0xafc178
    // 0xafc16c: ldr             x7, [fp, #0x10]
    // 0xafc170: r0 = Null
    //     0xafc170: mov             x0, NULL
    // 0xafc174: b               #0xafc1cc
    // 0xafc178: ldr             x7, [fp, #0x10]
    // 0xafc17c: LoadField: r0 = r6->field_b
    //     0xafc17c: ldur            w0, [x6, #0xb]
    // 0xafc180: r8 = LoadInt32Instr(r7)
    //     0xafc180: sbfx            x8, x7, #1, #0x1f
    //     0xafc184: tbz             w7, #0, #0xafc18c
    //     0xafc188: ldur            x8, [x7, #7]
    // 0xafc18c: r1 = LoadInt32Instr(r0)
    //     0xafc18c: sbfx            x1, x0, #1, #0x1f
    // 0xafc190: mov             x0, x1
    // 0xafc194: mov             x1, x8
    // 0xafc198: cmp             x1, x0
    // 0xafc19c: b.hs            #0xafc5b0
    // 0xafc1a0: LoadField: r0 = r6->field_f
    //     0xafc1a0: ldur            w0, [x6, #0xf]
    // 0xafc1a4: DecompressPointer r0
    //     0xafc1a4: add             x0, x0, HEAP, lsl #32
    // 0xafc1a8: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xafc1a8: add             x16, x0, x8, lsl #2
    //     0xafc1ac: ldur            w1, [x16, #0xf]
    // 0xafc1b0: DecompressPointer r1
    //     0xafc1b0: add             x1, x1, HEAP, lsl #32
    // 0xafc1b4: cmp             w1, NULL
    // 0xafc1b8: b.ne            #0xafc1c4
    // 0xafc1bc: r0 = Null
    //     0xafc1bc: mov             x0, NULL
    // 0xafc1c0: b               #0xafc1cc
    // 0xafc1c4: LoadField: r0 = r1->field_7
    //     0xafc1c4: ldur            w0, [x1, #7]
    // 0xafc1c8: DecompressPointer r0
    //     0xafc1c8: add             x0, x0, HEAP, lsl #32
    // 0xafc1cc: r1 = LoadClassIdInstr(r0)
    //     0xafc1cc: ldur            x1, [x0, #-1]
    //     0xafc1d0: ubfx            x1, x1, #0xc, #0x14
    // 0xafc1d4: r16 = "track_order"
    //     0xafc1d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36080] "track_order"
    //     0xafc1d8: ldr             x16, [x16, #0x80]
    // 0xafc1dc: stp             x16, x0, [SP]
    // 0xafc1e0: mov             x0, x1
    // 0xafc1e4: mov             lr, x0
    // 0xafc1e8: ldr             lr, [x21, lr, lsl #3]
    // 0xafc1ec: blr             lr
    // 0xafc1f0: tbnz            w0, #4, #0xafc200
    // 0xafc1f4: r2 = Instance_Icon
    //     0xafc1f4: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a240] Obj!Icon@d663f1
    //     0xafc1f8: ldr             x2, [x2, #0x240]
    // 0xafc1fc: b               #0xafc284
    // 0xafc200: ldr             x1, [fp, #0x18]
    // 0xafc204: r0 = of()
    //     0xafc204: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafc208: LoadField: r1 = r0->field_5b
    //     0xafc208: ldur            w1, [x0, #0x5b]
    // 0xafc20c: DecompressPointer r1
    //     0xafc20c: add             x1, x1, HEAP, lsl #32
    // 0xafc210: stur            x1, [fp, #-0x20]
    // 0xafc214: r0 = ColorFilter()
    //     0xafc214: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xafc218: mov             x1, x0
    // 0xafc21c: ldur            x0, [fp, #-0x20]
    // 0xafc220: stur            x1, [fp, #-0x28]
    // 0xafc224: StoreField: r1->field_7 = r0
    //     0xafc224: stur            w0, [x1, #7]
    // 0xafc228: r0 = Instance_BlendMode
    //     0xafc228: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xafc22c: ldr             x0, [x0, #0xb30]
    // 0xafc230: StoreField: r1->field_b = r0
    //     0xafc230: stur            w0, [x1, #0xb]
    // 0xafc234: r0 = 1
    //     0xafc234: movz            x0, #0x1
    // 0xafc238: StoreField: r1->field_13 = r0
    //     0xafc238: stur            x0, [x1, #0x13]
    // 0xafc23c: r0 = SvgPicture()
    //     0xafc23c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xafc240: stur            x0, [fp, #-0x20]
    // 0xafc244: r16 = 16.000000
    //     0xafc244: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xafc248: ldr             x16, [x16, #0x188]
    // 0xafc24c: r30 = 16.000000
    //     0xafc24c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xafc250: ldr             lr, [lr, #0x188]
    // 0xafc254: stp             lr, x16, [SP, #0x10]
    // 0xafc258: r16 = Instance_BoxFit
    //     0xafc258: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xafc25c: ldr             x16, [x16, #0xb18]
    // 0xafc260: ldur            lr, [fp, #-0x28]
    // 0xafc264: stp             lr, x16, [SP]
    // 0xafc268: mov             x1, x0
    // 0xafc26c: r2 = "assets/images/exchange.svg"
    //     0xafc26c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52ca0] "assets/images/exchange.svg"
    //     0xafc270: ldr             x2, [x2, #0xca0]
    // 0xafc274: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0xafc274: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a238] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xafc278: ldr             x4, [x4, #0x238]
    // 0xafc27c: r0 = SvgPicture.asset()
    //     0xafc27c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xafc280: ldur            x2, [fp, #-0x20]
    // 0xafc284: ldur            x0, [fp, #-0x18]
    // 0xafc288: stur            x2, [fp, #-0x20]
    // 0xafc28c: LoadField: r1 = r0->field_b
    //     0xafc28c: ldur            w1, [x0, #0xb]
    // 0xafc290: LoadField: r3 = r0->field_f
    //     0xafc290: ldur            w3, [x0, #0xf]
    // 0xafc294: DecompressPointer r3
    //     0xafc294: add             x3, x3, HEAP, lsl #32
    // 0xafc298: LoadField: r4 = r3->field_b
    //     0xafc298: ldur            w4, [x3, #0xb]
    // 0xafc29c: r3 = LoadInt32Instr(r1)
    //     0xafc29c: sbfx            x3, x1, #1, #0x1f
    // 0xafc2a0: stur            x3, [fp, #-0x30]
    // 0xafc2a4: r1 = LoadInt32Instr(r4)
    //     0xafc2a4: sbfx            x1, x4, #1, #0x1f
    // 0xafc2a8: cmp             x3, x1
    // 0xafc2ac: b.ne            #0xafc2b8
    // 0xafc2b0: mov             x1, x0
    // 0xafc2b4: r0 = _growToNextCapacity()
    //     0xafc2b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafc2b8: ldur            x2, [fp, #-0x18]
    // 0xafc2bc: ldur            x3, [fp, #-0x30]
    // 0xafc2c0: add             x4, x3, #1
    // 0xafc2c4: lsl             x0, x4, #1
    // 0xafc2c8: StoreField: r2->field_b = r0
    //     0xafc2c8: stur            w0, [x2, #0xb]
    // 0xafc2cc: LoadField: r5 = r2->field_f
    //     0xafc2cc: ldur            w5, [x2, #0xf]
    // 0xafc2d0: DecompressPointer r5
    //     0xafc2d0: add             x5, x5, HEAP, lsl #32
    // 0xafc2d4: mov             x1, x5
    // 0xafc2d8: ldur            x0, [fp, #-0x20]
    // 0xafc2dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafc2dc: add             x25, x1, x3, lsl #2
    //     0xafc2e0: add             x25, x25, #0xf
    //     0xafc2e4: str             w0, [x25]
    //     0xafc2e8: tbz             w0, #0, #0xafc304
    //     0xafc2ec: ldurb           w16, [x1, #-1]
    //     0xafc2f0: ldurb           w17, [x0, #-1]
    //     0xafc2f4: and             x16, x17, x16, lsr #2
    //     0xafc2f8: tst             x16, HEAP, lsr #32
    //     0xafc2fc: b.eq            #0xafc304
    //     0xafc300: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafc304: mov             x3, x4
    // 0xafc308: mov             x0, x5
    // 0xafc30c: stur            x3, [fp, #-0x30]
    // 0xafc310: LoadField: r1 = r0->field_b
    //     0xafc310: ldur            w1, [x0, #0xb]
    // 0xafc314: r0 = LoadInt32Instr(r1)
    //     0xafc314: sbfx            x0, x1, #1, #0x1f
    // 0xafc318: cmp             x3, x0
    // 0xafc31c: b.ne            #0xafc328
    // 0xafc320: mov             x1, x2
    // 0xafc324: r0 = _growToNextCapacity()
    //     0xafc324: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafc328: ldur            x4, [fp, #-8]
    // 0xafc32c: ldur            x2, [fp, #-0x18]
    // 0xafc330: ldur            x3, [fp, #-0x30]
    // 0xafc334: add             x0, x3, #1
    // 0xafc338: lsl             x1, x0, #1
    // 0xafc33c: StoreField: r2->field_b = r1
    //     0xafc33c: stur            w1, [x2, #0xb]
    // 0xafc340: mov             x1, x3
    // 0xafc344: cmp             x1, x0
    // 0xafc348: b.hs            #0xafc5b4
    // 0xafc34c: LoadField: r0 = r2->field_f
    //     0xafc34c: ldur            w0, [x2, #0xf]
    // 0xafc350: DecompressPointer r0
    //     0xafc350: add             x0, x0, HEAP, lsl #32
    // 0xafc354: add             x1, x0, x3, lsl #2
    // 0xafc358: r16 = Instance_SizedBox
    //     0xafc358: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xafc35c: ldr             x16, [x16, #0x998]
    // 0xafc360: StoreField: r1->field_f = r16
    //     0xafc360: stur            w16, [x1, #0xf]
    // 0xafc364: LoadField: r0 = r4->field_f
    //     0xafc364: ldur            w0, [x4, #0xf]
    // 0xafc368: DecompressPointer r0
    //     0xafc368: add             x0, x0, HEAP, lsl #32
    // 0xafc36c: LoadField: r1 = r0->field_b
    //     0xafc36c: ldur            w1, [x0, #0xb]
    // 0xafc370: DecompressPointer r1
    //     0xafc370: add             x1, x1, HEAP, lsl #32
    // 0xafc374: cmp             w1, NULL
    // 0xafc378: b.eq            #0xafc5b8
    // 0xafc37c: LoadField: r0 = r1->field_b
    //     0xafc37c: ldur            w0, [x1, #0xb]
    // 0xafc380: DecompressPointer r0
    //     0xafc380: add             x0, x0, HEAP, lsl #32
    // 0xafc384: LoadField: r3 = r0->field_57
    //     0xafc384: ldur            w3, [x0, #0x57]
    // 0xafc388: DecompressPointer r3
    //     0xafc388: add             x3, x3, HEAP, lsl #32
    // 0xafc38c: cmp             w3, NULL
    // 0xafc390: b.ne            #0xafc39c
    // 0xafc394: r0 = Null
    //     0xafc394: mov             x0, NULL
    // 0xafc398: b               #0xafc3ec
    // 0xafc39c: ldr             x0, [fp, #0x10]
    // 0xafc3a0: LoadField: r1 = r3->field_b
    //     0xafc3a0: ldur            w1, [x3, #0xb]
    // 0xafc3a4: r4 = LoadInt32Instr(r0)
    //     0xafc3a4: sbfx            x4, x0, #1, #0x1f
    //     0xafc3a8: tbz             w0, #0, #0xafc3b0
    //     0xafc3ac: ldur            x4, [x0, #7]
    // 0xafc3b0: r0 = LoadInt32Instr(r1)
    //     0xafc3b0: sbfx            x0, x1, #1, #0x1f
    // 0xafc3b4: mov             x1, x4
    // 0xafc3b8: cmp             x1, x0
    // 0xafc3bc: b.hs            #0xafc5bc
    // 0xafc3c0: LoadField: r0 = r3->field_f
    //     0xafc3c0: ldur            w0, [x3, #0xf]
    // 0xafc3c4: DecompressPointer r0
    //     0xafc3c4: add             x0, x0, HEAP, lsl #32
    // 0xafc3c8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafc3c8: add             x16, x0, x4, lsl #2
    //     0xafc3cc: ldur            w1, [x16, #0xf]
    // 0xafc3d0: DecompressPointer r1
    //     0xafc3d0: add             x1, x1, HEAP, lsl #32
    // 0xafc3d4: cmp             w1, NULL
    // 0xafc3d8: b.ne            #0xafc3e4
    // 0xafc3dc: r0 = Null
    //     0xafc3dc: mov             x0, NULL
    // 0xafc3e0: b               #0xafc3ec
    // 0xafc3e4: LoadField: r0 = r1->field_b
    //     0xafc3e4: ldur            w0, [x1, #0xb]
    // 0xafc3e8: DecompressPointer r0
    //     0xafc3e8: add             x0, x0, HEAP, lsl #32
    // 0xafc3ec: cmp             w0, NULL
    // 0xafc3f0: b.ne            #0xafc3f8
    // 0xafc3f4: r0 = ""
    //     0xafc3f4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafc3f8: ldr             x1, [fp, #0x18]
    // 0xafc3fc: stur            x0, [fp, #-8]
    // 0xafc400: r0 = of()
    //     0xafc400: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafc404: LoadField: r1 = r0->field_87
    //     0xafc404: ldur            w1, [x0, #0x87]
    // 0xafc408: DecompressPointer r1
    //     0xafc408: add             x1, x1, HEAP, lsl #32
    // 0xafc40c: LoadField: r0 = r1->field_2b
    //     0xafc40c: ldur            w0, [x1, #0x2b]
    // 0xafc410: DecompressPointer r0
    //     0xafc410: add             x0, x0, HEAP, lsl #32
    // 0xafc414: r16 = 12.000000
    //     0xafc414: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xafc418: ldr             x16, [x16, #0x9e8]
    // 0xafc41c: r30 = Instance_Color
    //     0xafc41c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xafc420: stp             lr, x16, [SP]
    // 0xafc424: mov             x1, x0
    // 0xafc428: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xafc428: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xafc42c: ldr             x4, [x4, #0xaa0]
    // 0xafc430: r0 = copyWith()
    //     0xafc430: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xafc434: stur            x0, [fp, #-0x20]
    // 0xafc438: r0 = Text()
    //     0xafc438: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xafc43c: mov             x2, x0
    // 0xafc440: ldur            x0, [fp, #-8]
    // 0xafc444: stur            x2, [fp, #-0x28]
    // 0xafc448: StoreField: r2->field_b = r0
    //     0xafc448: stur            w0, [x2, #0xb]
    // 0xafc44c: ldur            x0, [fp, #-0x20]
    // 0xafc450: StoreField: r2->field_13 = r0
    //     0xafc450: stur            w0, [x2, #0x13]
    // 0xafc454: ldur            x0, [fp, #-0x18]
    // 0xafc458: LoadField: r1 = r0->field_b
    //     0xafc458: ldur            w1, [x0, #0xb]
    // 0xafc45c: LoadField: r3 = r0->field_f
    //     0xafc45c: ldur            w3, [x0, #0xf]
    // 0xafc460: DecompressPointer r3
    //     0xafc460: add             x3, x3, HEAP, lsl #32
    // 0xafc464: LoadField: r4 = r3->field_b
    //     0xafc464: ldur            w4, [x3, #0xb]
    // 0xafc468: r3 = LoadInt32Instr(r1)
    //     0xafc468: sbfx            x3, x1, #1, #0x1f
    // 0xafc46c: stur            x3, [fp, #-0x30]
    // 0xafc470: r1 = LoadInt32Instr(r4)
    //     0xafc470: sbfx            x1, x4, #1, #0x1f
    // 0xafc474: cmp             x3, x1
    // 0xafc478: b.ne            #0xafc484
    // 0xafc47c: mov             x1, x0
    // 0xafc480: r0 = _growToNextCapacity()
    //     0xafc480: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafc484: ldur            x2, [fp, #-0x18]
    // 0xafc488: ldur            x3, [fp, #-0x30]
    // 0xafc48c: add             x0, x3, #1
    // 0xafc490: lsl             x1, x0, #1
    // 0xafc494: StoreField: r2->field_b = r1
    //     0xafc494: stur            w1, [x2, #0xb]
    // 0xafc498: LoadField: r1 = r2->field_f
    //     0xafc498: ldur            w1, [x2, #0xf]
    // 0xafc49c: DecompressPointer r1
    //     0xafc49c: add             x1, x1, HEAP, lsl #32
    // 0xafc4a0: ldur            x0, [fp, #-0x28]
    // 0xafc4a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafc4a4: add             x25, x1, x3, lsl #2
    //     0xafc4a8: add             x25, x25, #0xf
    //     0xafc4ac: str             w0, [x25]
    //     0xafc4b0: tbz             w0, #0, #0xafc4cc
    //     0xafc4b4: ldurb           w16, [x1, #-1]
    //     0xafc4b8: ldurb           w17, [x0, #-1]
    //     0xafc4bc: and             x16, x17, x16, lsr #2
    //     0xafc4c0: tst             x16, HEAP, lsr #32
    //     0xafc4c4: b.eq            #0xafc4cc
    //     0xafc4c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafc4cc: r0 = Row()
    //     0xafc4cc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafc4d0: mov             x1, x0
    // 0xafc4d4: r0 = Instance_Axis
    //     0xafc4d4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafc4d8: stur            x1, [fp, #-8]
    // 0xafc4dc: StoreField: r1->field_f = r0
    //     0xafc4dc: stur            w0, [x1, #0xf]
    // 0xafc4e0: r0 = Instance_MainAxisAlignment
    //     0xafc4e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafc4e4: ldr             x0, [x0, #0xa08]
    // 0xafc4e8: StoreField: r1->field_13 = r0
    //     0xafc4e8: stur            w0, [x1, #0x13]
    // 0xafc4ec: r0 = Instance_MainAxisSize
    //     0xafc4ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafc4f0: ldr             x0, [x0, #0xa10]
    // 0xafc4f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xafc4f4: stur            w0, [x1, #0x17]
    // 0xafc4f8: r0 = Instance_CrossAxisAlignment
    //     0xafc4f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafc4fc: ldr             x0, [x0, #0xa18]
    // 0xafc500: StoreField: r1->field_1b = r0
    //     0xafc500: stur            w0, [x1, #0x1b]
    // 0xafc504: r0 = Instance_VerticalDirection
    //     0xafc504: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafc508: ldr             x0, [x0, #0xa20]
    // 0xafc50c: StoreField: r1->field_23 = r0
    //     0xafc50c: stur            w0, [x1, #0x23]
    // 0xafc510: r0 = Instance_Clip
    //     0xafc510: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafc514: ldr             x0, [x0, #0x38]
    // 0xafc518: StoreField: r1->field_2b = r0
    //     0xafc518: stur            w0, [x1, #0x2b]
    // 0xafc51c: StoreField: r1->field_2f = rZR
    //     0xafc51c: stur            xzr, [x1, #0x2f]
    // 0xafc520: ldur            x0, [fp, #-0x18]
    // 0xafc524: StoreField: r1->field_b = r0
    //     0xafc524: stur            w0, [x1, #0xb]
    // 0xafc528: r0 = InkWell()
    //     0xafc528: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xafc52c: mov             x3, x0
    // 0xafc530: ldur            x0, [fp, #-8]
    // 0xafc534: stur            x3, [fp, #-0x18]
    // 0xafc538: StoreField: r3->field_b = r0
    //     0xafc538: stur            w0, [x3, #0xb]
    // 0xafc53c: ldur            x2, [fp, #-0x10]
    // 0xafc540: r1 = Function '<anonymous closure>':.
    //     0xafc540: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6acd8] AnonymousClosure: (0xafc5c0), in [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::build (0xafa414)
    //     0xafc544: ldr             x1, [x1, #0xcd8]
    // 0xafc548: r0 = AllocateClosure()
    //     0xafc548: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafc54c: mov             x1, x0
    // 0xafc550: ldur            x0, [fp, #-0x18]
    // 0xafc554: StoreField: r0->field_f = r1
    //     0xafc554: stur            w1, [x0, #0xf]
    // 0xafc558: r1 = true
    //     0xafc558: add             x1, NULL, #0x20  ; true
    // 0xafc55c: StoreField: r0->field_43 = r1
    //     0xafc55c: stur            w1, [x0, #0x43]
    // 0xafc560: r2 = Instance_BoxShape
    //     0xafc560: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xafc564: ldr             x2, [x2, #0x80]
    // 0xafc568: StoreField: r0->field_47 = r2
    //     0xafc568: stur            w2, [x0, #0x47]
    // 0xafc56c: StoreField: r0->field_6f = r1
    //     0xafc56c: stur            w1, [x0, #0x6f]
    // 0xafc570: r2 = false
    //     0xafc570: add             x2, NULL, #0x30  ; false
    // 0xafc574: StoreField: r0->field_73 = r2
    //     0xafc574: stur            w2, [x0, #0x73]
    // 0xafc578: StoreField: r0->field_83 = r1
    //     0xafc578: stur            w1, [x0, #0x83]
    // 0xafc57c: StoreField: r0->field_7b = r2
    //     0xafc57c: stur            w2, [x0, #0x7b]
    // 0xafc580: LeaveFrame
    //     0xafc580: mov             SP, fp
    //     0xafc584: ldp             fp, lr, [SP], #0x10
    // 0xafc588: ret
    //     0xafc588: ret             
    // 0xafc58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafc58c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafc590: b               #0xafbdac
    // 0xafc594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafc594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafc598: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafc598: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafc59c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafc59c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafc5a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafc5a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafc5a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafc5a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafc5a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafc5a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafc5ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafc5ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafc5b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafc5b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafc5b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafc5b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafc5b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafc5b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafc5bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafc5bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xafc5c0, size: 0x68c
    // 0xafc5c0: EnterFrame
    //     0xafc5c0: stp             fp, lr, [SP, #-0x10]!
    //     0xafc5c4: mov             fp, SP
    // 0xafc5c8: AllocStack(0x60)
    //     0xafc5c8: sub             SP, SP, #0x60
    // 0xafc5cc: SetupParameters()
    //     0xafc5cc: ldr             x0, [fp, #0x10]
    //     0xafc5d0: ldur            w2, [x0, #0x17]
    //     0xafc5d4: add             x2, x2, HEAP, lsl #32
    //     0xafc5d8: stur            x2, [fp, #-0x10]
    // 0xafc5dc: CheckStackOverflow
    //     0xafc5dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafc5e0: cmp             SP, x16
    //     0xafc5e4: b.ls            #0xafcc08
    // 0xafc5e8: LoadField: r3 = r2->field_b
    //     0xafc5e8: ldur            w3, [x2, #0xb]
    // 0xafc5ec: DecompressPointer r3
    //     0xafc5ec: add             x3, x3, HEAP, lsl #32
    // 0xafc5f0: stur            x3, [fp, #-8]
    // 0xafc5f4: LoadField: r0 = r3->field_f
    //     0xafc5f4: ldur            w0, [x3, #0xf]
    // 0xafc5f8: DecompressPointer r0
    //     0xafc5f8: add             x0, x0, HEAP, lsl #32
    // 0xafc5fc: LoadField: r1 = r0->field_b
    //     0xafc5fc: ldur            w1, [x0, #0xb]
    // 0xafc600: DecompressPointer r1
    //     0xafc600: add             x1, x1, HEAP, lsl #32
    // 0xafc604: cmp             w1, NULL
    // 0xafc608: b.eq            #0xafcc10
    // 0xafc60c: LoadField: r0 = r1->field_b
    //     0xafc60c: ldur            w0, [x1, #0xb]
    // 0xafc610: DecompressPointer r0
    //     0xafc610: add             x0, x0, HEAP, lsl #32
    // 0xafc614: LoadField: r4 = r0->field_57
    //     0xafc614: ldur            w4, [x0, #0x57]
    // 0xafc618: DecompressPointer r4
    //     0xafc618: add             x4, x4, HEAP, lsl #32
    // 0xafc61c: cmp             w4, NULL
    // 0xafc620: b.ne            #0xafc62c
    // 0xafc624: r0 = Null
    //     0xafc624: mov             x0, NULL
    // 0xafc628: b               #0xafc680
    // 0xafc62c: LoadField: r0 = r2->field_13
    //     0xafc62c: ldur            w0, [x2, #0x13]
    // 0xafc630: DecompressPointer r0
    //     0xafc630: add             x0, x0, HEAP, lsl #32
    // 0xafc634: LoadField: r1 = r4->field_b
    //     0xafc634: ldur            w1, [x4, #0xb]
    // 0xafc638: r5 = LoadInt32Instr(r0)
    //     0xafc638: sbfx            x5, x0, #1, #0x1f
    //     0xafc63c: tbz             w0, #0, #0xafc644
    //     0xafc640: ldur            x5, [x0, #7]
    // 0xafc644: r0 = LoadInt32Instr(r1)
    //     0xafc644: sbfx            x0, x1, #1, #0x1f
    // 0xafc648: mov             x1, x5
    // 0xafc64c: cmp             x1, x0
    // 0xafc650: b.hs            #0xafcc14
    // 0xafc654: LoadField: r0 = r4->field_f
    //     0xafc654: ldur            w0, [x4, #0xf]
    // 0xafc658: DecompressPointer r0
    //     0xafc658: add             x0, x0, HEAP, lsl #32
    // 0xafc65c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xafc65c: add             x16, x0, x5, lsl #2
    //     0xafc660: ldur            w1, [x16, #0xf]
    // 0xafc664: DecompressPointer r1
    //     0xafc664: add             x1, x1, HEAP, lsl #32
    // 0xafc668: cmp             w1, NULL
    // 0xafc66c: b.ne            #0xafc678
    // 0xafc670: r0 = Null
    //     0xafc670: mov             x0, NULL
    // 0xafc674: b               #0xafc680
    // 0xafc678: LoadField: r0 = r1->field_7
    //     0xafc678: ldur            w0, [x1, #7]
    // 0xafc67c: DecompressPointer r0
    //     0xafc67c: add             x0, x0, HEAP, lsl #32
    // 0xafc680: r1 = LoadClassIdInstr(r0)
    //     0xafc680: ldur            x1, [x0, #-1]
    //     0xafc684: ubfx            x1, x1, #0xc, #0x14
    // 0xafc688: r16 = "cancel_order"
    //     0xafc688: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xafc68c: ldr             x16, [x16, #0x98]
    // 0xafc690: stp             x16, x0, [SP]
    // 0xafc694: mov             x0, x1
    // 0xafc698: mov             lr, x0
    // 0xafc69c: ldr             lr, [x21, lr, lsl #3]
    // 0xafc6a0: blr             lr
    // 0xafc6a4: tbnz            w0, #4, #0xafc7a4
    // 0xafc6a8: ldur            x2, [fp, #-8]
    // 0xafc6ac: LoadField: r0 = r2->field_f
    //     0xafc6ac: ldur            w0, [x2, #0xf]
    // 0xafc6b0: DecompressPointer r0
    //     0xafc6b0: add             x0, x0, HEAP, lsl #32
    // 0xafc6b4: LoadField: r1 = r0->field_b
    //     0xafc6b4: ldur            w1, [x0, #0xb]
    // 0xafc6b8: DecompressPointer r1
    //     0xafc6b8: add             x1, x1, HEAP, lsl #32
    // 0xafc6bc: cmp             w1, NULL
    // 0xafc6c0: b.eq            #0xafcc18
    // 0xafc6c4: LoadField: r0 = r1->field_b
    //     0xafc6c4: ldur            w0, [x1, #0xb]
    // 0xafc6c8: DecompressPointer r0
    //     0xafc6c8: add             x0, x0, HEAP, lsl #32
    // 0xafc6cc: LoadField: r2 = r0->field_7f
    //     0xafc6cc: ldur            w2, [x0, #0x7f]
    // 0xafc6d0: DecompressPointer r2
    //     0xafc6d0: add             x2, x2, HEAP, lsl #32
    // 0xafc6d4: cmp             w2, NULL
    // 0xafc6d8: b.ne            #0xafc6e4
    // 0xafc6dc: r0 = Null
    //     0xafc6dc: mov             x0, NULL
    // 0xafc6e0: b               #0xafc6ec
    // 0xafc6e4: LoadField: r0 = r2->field_2b
    //     0xafc6e4: ldur            w0, [x2, #0x2b]
    // 0xafc6e8: DecompressPointer r0
    //     0xafc6e8: add             x0, x0, HEAP, lsl #32
    // 0xafc6ec: cmp             w0, NULL
    // 0xafc6f0: b.ne            #0xafc6fc
    // 0xafc6f4: ldur            x3, [fp, #-0x10]
    // 0xafc6f8: b               #0xafc74c
    // 0xafc6fc: tbnz            w0, #4, #0xafc748
    // 0xafc700: ldur            x3, [fp, #-0x10]
    // 0xafc704: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xafc704: ldur            w0, [x1, #0x17]
    // 0xafc708: DecompressPointer r0
    //     0xafc708: add             x0, x0, HEAP, lsl #32
    // 0xafc70c: LoadField: r2 = r1->field_b
    //     0xafc70c: ldur            w2, [x1, #0xb]
    // 0xafc710: DecompressPointer r2
    //     0xafc710: add             x2, x2, HEAP, lsl #32
    // 0xafc714: LoadField: r1 = r2->field_7
    //     0xafc714: ldur            w1, [x2, #7]
    // 0xafc718: DecompressPointer r1
    //     0xafc718: add             x1, x1, HEAP, lsl #32
    // 0xafc71c: LoadField: r4 = r3->field_f
    //     0xafc71c: ldur            w4, [x3, #0xf]
    // 0xafc720: DecompressPointer r4
    //     0xafc720: add             x4, x4, HEAP, lsl #32
    // 0xafc724: stp             x2, x0, [SP, #0x10]
    // 0xafc728: stp             x4, x1, [SP]
    // 0xafc72c: r4 = 0
    //     0xafc72c: movz            x4, #0
    // 0xafc730: ldr             x0, [SP, #0x18]
    // 0xafc734: r16 = UnlinkedCall_0x613b5c
    //     0xafc734: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ace0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafc738: add             x16, x16, #0xce0
    // 0xafc73c: ldp             x5, lr, [x16]
    // 0xafc740: blr             lr
    // 0xafc744: b               #0xafcbf8
    // 0xafc748: ldur            x3, [fp, #-0x10]
    // 0xafc74c: LoadField: r0 = r1->field_13
    //     0xafc74c: ldur            w0, [x1, #0x13]
    // 0xafc750: DecompressPointer r0
    //     0xafc750: add             x0, x0, HEAP, lsl #32
    // 0xafc754: LoadField: r2 = r1->field_b
    //     0xafc754: ldur            w2, [x1, #0xb]
    // 0xafc758: DecompressPointer r2
    //     0xafc758: add             x2, x2, HEAP, lsl #32
    // 0xafc75c: LoadField: r1 = r2->field_37
    //     0xafc75c: ldur            w1, [x2, #0x37]
    // 0xafc760: DecompressPointer r1
    //     0xafc760: add             x1, x1, HEAP, lsl #32
    // 0xafc764: LoadField: r4 = r2->field_7
    //     0xafc764: ldur            w4, [x2, #7]
    // 0xafc768: DecompressPointer r4
    //     0xafc768: add             x4, x4, HEAP, lsl #32
    // 0xafc76c: LoadField: r5 = r3->field_f
    //     0xafc76c: ldur            w5, [x3, #0xf]
    // 0xafc770: DecompressPointer r5
    //     0xafc770: add             x5, x5, HEAP, lsl #32
    // 0xafc774: LoadField: r3 = r2->field_2f
    //     0xafc774: ldur            w3, [x2, #0x2f]
    // 0xafc778: DecompressPointer r3
    //     0xafc778: add             x3, x3, HEAP, lsl #32
    // 0xafc77c: stp             x1, x0, [SP, #0x18]
    // 0xafc780: stp             x5, x4, [SP, #8]
    // 0xafc784: str             x3, [SP]
    // 0xafc788: r4 = 0
    //     0xafc788: movz            x4, #0
    // 0xafc78c: ldr             x0, [SP, #0x20]
    // 0xafc790: r16 = UnlinkedCall_0x613b5c
    //     0xafc790: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6acf0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafc794: add             x16, x16, #0xcf0
    // 0xafc798: ldp             x5, lr, [x16]
    // 0xafc79c: blr             lr
    // 0xafc7a0: b               #0xafcbf8
    // 0xafc7a4: ldur            x3, [fp, #-0x10]
    // 0xafc7a8: ldur            x2, [fp, #-8]
    // 0xafc7ac: LoadField: r0 = r2->field_f
    //     0xafc7ac: ldur            w0, [x2, #0xf]
    // 0xafc7b0: DecompressPointer r0
    //     0xafc7b0: add             x0, x0, HEAP, lsl #32
    // 0xafc7b4: LoadField: r1 = r0->field_b
    //     0xafc7b4: ldur            w1, [x0, #0xb]
    // 0xafc7b8: DecompressPointer r1
    //     0xafc7b8: add             x1, x1, HEAP, lsl #32
    // 0xafc7bc: cmp             w1, NULL
    // 0xafc7c0: b.eq            #0xafcc1c
    // 0xafc7c4: LoadField: r0 = r1->field_b
    //     0xafc7c4: ldur            w0, [x1, #0xb]
    // 0xafc7c8: DecompressPointer r0
    //     0xafc7c8: add             x0, x0, HEAP, lsl #32
    // 0xafc7cc: LoadField: r4 = r0->field_57
    //     0xafc7cc: ldur            w4, [x0, #0x57]
    // 0xafc7d0: DecompressPointer r4
    //     0xafc7d0: add             x4, x4, HEAP, lsl #32
    // 0xafc7d4: cmp             w4, NULL
    // 0xafc7d8: b.ne            #0xafc7e4
    // 0xafc7dc: r0 = Null
    //     0xafc7dc: mov             x0, NULL
    // 0xafc7e0: b               #0xafc838
    // 0xafc7e4: LoadField: r0 = r3->field_13
    //     0xafc7e4: ldur            w0, [x3, #0x13]
    // 0xafc7e8: DecompressPointer r0
    //     0xafc7e8: add             x0, x0, HEAP, lsl #32
    // 0xafc7ec: LoadField: r1 = r4->field_b
    //     0xafc7ec: ldur            w1, [x4, #0xb]
    // 0xafc7f0: r5 = LoadInt32Instr(r0)
    //     0xafc7f0: sbfx            x5, x0, #1, #0x1f
    //     0xafc7f4: tbz             w0, #0, #0xafc7fc
    //     0xafc7f8: ldur            x5, [x0, #7]
    // 0xafc7fc: r0 = LoadInt32Instr(r1)
    //     0xafc7fc: sbfx            x0, x1, #1, #0x1f
    // 0xafc800: mov             x1, x5
    // 0xafc804: cmp             x1, x0
    // 0xafc808: b.hs            #0xafcc20
    // 0xafc80c: LoadField: r0 = r4->field_f
    //     0xafc80c: ldur            w0, [x4, #0xf]
    // 0xafc810: DecompressPointer r0
    //     0xafc810: add             x0, x0, HEAP, lsl #32
    // 0xafc814: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xafc814: add             x16, x0, x5, lsl #2
    //     0xafc818: ldur            w1, [x16, #0xf]
    // 0xafc81c: DecompressPointer r1
    //     0xafc81c: add             x1, x1, HEAP, lsl #32
    // 0xafc820: cmp             w1, NULL
    // 0xafc824: b.ne            #0xafc830
    // 0xafc828: r0 = Null
    //     0xafc828: mov             x0, NULL
    // 0xafc82c: b               #0xafc838
    // 0xafc830: LoadField: r0 = r1->field_7
    //     0xafc830: ldur            w0, [x1, #7]
    // 0xafc834: DecompressPointer r0
    //     0xafc834: add             x0, x0, HEAP, lsl #32
    // 0xafc838: r1 = LoadClassIdInstr(r0)
    //     0xafc838: ldur            x1, [x0, #-1]
    //     0xafc83c: ubfx            x1, x1, #0xc, #0x14
    // 0xafc840: r16 = "exchange_cancel"
    //     0xafc840: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df8] "exchange_cancel"
    //     0xafc844: ldr             x16, [x16, #0xdf8]
    // 0xafc848: stp             x16, x0, [SP]
    // 0xafc84c: mov             x0, x1
    // 0xafc850: mov             lr, x0
    // 0xafc854: ldr             lr, [x21, lr, lsl #3]
    // 0xafc858: blr             lr
    // 0xafc85c: tbnz            w0, #4, #0xafc980
    // 0xafc860: ldur            x3, [fp, #-0x10]
    // 0xafc864: ldur            x2, [fp, #-8]
    // 0xafc868: LoadField: r0 = r2->field_f
    //     0xafc868: ldur            w0, [x2, #0xf]
    // 0xafc86c: DecompressPointer r0
    //     0xafc86c: add             x0, x0, HEAP, lsl #32
    // 0xafc870: LoadField: r2 = r0->field_b
    //     0xafc870: ldur            w2, [x0, #0xb]
    // 0xafc874: DecompressPointer r2
    //     0xafc874: add             x2, x2, HEAP, lsl #32
    // 0xafc878: cmp             w2, NULL
    // 0xafc87c: b.eq            #0xafcc24
    // 0xafc880: LoadField: r4 = r2->field_1f
    //     0xafc880: ldur            w4, [x2, #0x1f]
    // 0xafc884: DecompressPointer r4
    //     0xafc884: add             x4, x4, HEAP, lsl #32
    // 0xafc888: LoadField: r5 = r3->field_f
    //     0xafc888: ldur            w5, [x3, #0xf]
    // 0xafc88c: DecompressPointer r5
    //     0xafc88c: add             x5, x5, HEAP, lsl #32
    // 0xafc890: LoadField: r0 = r2->field_b
    //     0xafc890: ldur            w0, [x2, #0xb]
    // 0xafc894: DecompressPointer r0
    //     0xafc894: add             x0, x0, HEAP, lsl #32
    // 0xafc898: LoadField: r6 = r0->field_63
    //     0xafc898: ldur            w6, [x0, #0x63]
    // 0xafc89c: DecompressPointer r6
    //     0xafc89c: add             x6, x6, HEAP, lsl #32
    // 0xafc8a0: LoadField: r7 = r0->field_67
    //     0xafc8a0: ldur            w7, [x0, #0x67]
    // 0xafc8a4: DecompressPointer r7
    //     0xafc8a4: add             x7, x7, HEAP, lsl #32
    // 0xafc8a8: LoadField: r8 = r0->field_73
    //     0xafc8a8: ldur            w8, [x0, #0x73]
    // 0xafc8ac: DecompressPointer r8
    //     0xafc8ac: add             x8, x8, HEAP, lsl #32
    // 0xafc8b0: LoadField: r9 = r0->field_6f
    //     0xafc8b0: ldur            w9, [x0, #0x6f]
    // 0xafc8b4: DecompressPointer r9
    //     0xafc8b4: add             x9, x9, HEAP, lsl #32
    // 0xafc8b8: LoadField: r10 = r0->field_6b
    //     0xafc8b8: ldur            w10, [x0, #0x6b]
    // 0xafc8bc: DecompressPointer r10
    //     0xafc8bc: add             x10, x10, HEAP, lsl #32
    // 0xafc8c0: LoadField: r11 = r0->field_77
    //     0xafc8c0: ldur            w11, [x0, #0x77]
    // 0xafc8c4: DecompressPointer r11
    //     0xafc8c4: add             x11, x11, HEAP, lsl #32
    // 0xafc8c8: LoadField: r12 = r0->field_57
    //     0xafc8c8: ldur            w12, [x0, #0x57]
    // 0xafc8cc: DecompressPointer r12
    //     0xafc8cc: add             x12, x12, HEAP, lsl #32
    // 0xafc8d0: cmp             w12, NULL
    // 0xafc8d4: b.ne            #0xafc8e0
    // 0xafc8d8: r0 = Null
    //     0xafc8d8: mov             x0, NULL
    // 0xafc8dc: b               #0xafc934
    // 0xafc8e0: LoadField: r0 = r3->field_13
    //     0xafc8e0: ldur            w0, [x3, #0x13]
    // 0xafc8e4: DecompressPointer r0
    //     0xafc8e4: add             x0, x0, HEAP, lsl #32
    // 0xafc8e8: LoadField: r1 = r12->field_b
    //     0xafc8e8: ldur            w1, [x12, #0xb]
    // 0xafc8ec: r3 = LoadInt32Instr(r0)
    //     0xafc8ec: sbfx            x3, x0, #1, #0x1f
    //     0xafc8f0: tbz             w0, #0, #0xafc8f8
    //     0xafc8f4: ldur            x3, [x0, #7]
    // 0xafc8f8: r0 = LoadInt32Instr(r1)
    //     0xafc8f8: sbfx            x0, x1, #1, #0x1f
    // 0xafc8fc: mov             x1, x3
    // 0xafc900: cmp             x1, x0
    // 0xafc904: b.hs            #0xafcc28
    // 0xafc908: LoadField: r0 = r12->field_f
    //     0xafc908: ldur            w0, [x12, #0xf]
    // 0xafc90c: DecompressPointer r0
    //     0xafc90c: add             x0, x0, HEAP, lsl #32
    // 0xafc910: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xafc910: add             x16, x0, x3, lsl #2
    //     0xafc914: ldur            w1, [x16, #0xf]
    // 0xafc918: DecompressPointer r1
    //     0xafc918: add             x1, x1, HEAP, lsl #32
    // 0xafc91c: cmp             w1, NULL
    // 0xafc920: b.ne            #0xafc92c
    // 0xafc924: r0 = Null
    //     0xafc924: mov             x0, NULL
    // 0xafc928: b               #0xafc934
    // 0xafc92c: LoadField: r0 = r1->field_b
    //     0xafc92c: ldur            w0, [x1, #0xb]
    // 0xafc930: DecompressPointer r0
    //     0xafc930: add             x0, x0, HEAP, lsl #32
    // 0xafc934: cmp             w0, NULL
    // 0xafc938: b.ne            #0xafc940
    // 0xafc93c: r0 = ""
    //     0xafc93c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafc940: LoadField: r1 = r2->field_b
    //     0xafc940: ldur            w1, [x2, #0xb]
    // 0xafc944: DecompressPointer r1
    //     0xafc944: add             x1, x1, HEAP, lsl #32
    // 0xafc948: LoadField: r2 = r1->field_7
    //     0xafc948: ldur            w2, [x1, #7]
    // 0xafc94c: DecompressPointer r2
    //     0xafc94c: add             x2, x2, HEAP, lsl #32
    // 0xafc950: stp             x5, x4, [SP, #0x40]
    // 0xafc954: stp             x7, x6, [SP, #0x30]
    // 0xafc958: stp             x9, x8, [SP, #0x20]
    // 0xafc95c: stp             x11, x10, [SP, #0x10]
    // 0xafc960: stp             x2, x0, [SP]
    // 0xafc964: r4 = 0
    //     0xafc964: movz            x4, #0
    // 0xafc968: ldr             x0, [SP, #0x48]
    // 0xafc96c: r16 = UnlinkedCall_0x613b5c
    //     0xafc96c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ad00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafc970: add             x16, x16, #0xd00
    // 0xafc974: ldp             x5, lr, [x16]
    // 0xafc978: blr             lr
    // 0xafc97c: b               #0xafcbf8
    // 0xafc980: ldur            x3, [fp, #-0x10]
    // 0xafc984: ldur            x2, [fp, #-8]
    // 0xafc988: LoadField: r0 = r2->field_f
    //     0xafc988: ldur            w0, [x2, #0xf]
    // 0xafc98c: DecompressPointer r0
    //     0xafc98c: add             x0, x0, HEAP, lsl #32
    // 0xafc990: LoadField: r1 = r0->field_b
    //     0xafc990: ldur            w1, [x0, #0xb]
    // 0xafc994: DecompressPointer r1
    //     0xafc994: add             x1, x1, HEAP, lsl #32
    // 0xafc998: cmp             w1, NULL
    // 0xafc99c: b.eq            #0xafcc2c
    // 0xafc9a0: LoadField: r0 = r1->field_b
    //     0xafc9a0: ldur            w0, [x1, #0xb]
    // 0xafc9a4: DecompressPointer r0
    //     0xafc9a4: add             x0, x0, HEAP, lsl #32
    // 0xafc9a8: LoadField: r4 = r0->field_57
    //     0xafc9a8: ldur            w4, [x0, #0x57]
    // 0xafc9ac: DecompressPointer r4
    //     0xafc9ac: add             x4, x4, HEAP, lsl #32
    // 0xafc9b0: cmp             w4, NULL
    // 0xafc9b4: b.ne            #0xafc9c0
    // 0xafc9b8: r0 = Null
    //     0xafc9b8: mov             x0, NULL
    // 0xafc9bc: b               #0xafca14
    // 0xafc9c0: LoadField: r0 = r3->field_13
    //     0xafc9c0: ldur            w0, [x3, #0x13]
    // 0xafc9c4: DecompressPointer r0
    //     0xafc9c4: add             x0, x0, HEAP, lsl #32
    // 0xafc9c8: LoadField: r1 = r4->field_b
    //     0xafc9c8: ldur            w1, [x4, #0xb]
    // 0xafc9cc: r5 = LoadInt32Instr(r0)
    //     0xafc9cc: sbfx            x5, x0, #1, #0x1f
    //     0xafc9d0: tbz             w0, #0, #0xafc9d8
    //     0xafc9d4: ldur            x5, [x0, #7]
    // 0xafc9d8: r0 = LoadInt32Instr(r1)
    //     0xafc9d8: sbfx            x0, x1, #1, #0x1f
    // 0xafc9dc: mov             x1, x5
    // 0xafc9e0: cmp             x1, x0
    // 0xafc9e4: b.hs            #0xafcc30
    // 0xafc9e8: LoadField: r0 = r4->field_f
    //     0xafc9e8: ldur            w0, [x4, #0xf]
    // 0xafc9ec: DecompressPointer r0
    //     0xafc9ec: add             x0, x0, HEAP, lsl #32
    // 0xafc9f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xafc9f0: add             x16, x0, x5, lsl #2
    //     0xafc9f4: ldur            w1, [x16, #0xf]
    // 0xafc9f8: DecompressPointer r1
    //     0xafc9f8: add             x1, x1, HEAP, lsl #32
    // 0xafc9fc: cmp             w1, NULL
    // 0xafca00: b.ne            #0xafca0c
    // 0xafca04: r0 = Null
    //     0xafca04: mov             x0, NULL
    // 0xafca08: b               #0xafca14
    // 0xafca0c: LoadField: r0 = r1->field_7
    //     0xafca0c: ldur            w0, [x1, #7]
    // 0xafca10: DecompressPointer r0
    //     0xafca10: add             x0, x0, HEAP, lsl #32
    // 0xafca14: r1 = LoadClassIdInstr(r0)
    //     0xafca14: ldur            x1, [x0, #-1]
    //     0xafca18: ubfx            x1, x1, #0xc, #0x14
    // 0xafca1c: r16 = "cancel_return"
    //     0xafca1c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34df0] "cancel_return"
    //     0xafca20: ldr             x16, [x16, #0xdf0]
    // 0xafca24: stp             x16, x0, [SP]
    // 0xafca28: mov             x0, x1
    // 0xafca2c: mov             lr, x0
    // 0xafca30: ldr             lr, [x21, lr, lsl #3]
    // 0xafca34: blr             lr
    // 0xafca38: tbnz            w0, #4, #0xafcaa8
    // 0xafca3c: ldur            x0, [fp, #-0x10]
    // 0xafca40: ldur            x2, [fp, #-8]
    // 0xafca44: LoadField: r1 = r2->field_f
    //     0xafca44: ldur            w1, [x2, #0xf]
    // 0xafca48: DecompressPointer r1
    //     0xafca48: add             x1, x1, HEAP, lsl #32
    // 0xafca4c: LoadField: r2 = r1->field_b
    //     0xafca4c: ldur            w2, [x1, #0xb]
    // 0xafca50: DecompressPointer r2
    //     0xafca50: add             x2, x2, HEAP, lsl #32
    // 0xafca54: cmp             w2, NULL
    // 0xafca58: b.eq            #0xafcc34
    // 0xafca5c: LoadField: r1 = r2->field_1b
    //     0xafca5c: ldur            w1, [x2, #0x1b]
    // 0xafca60: DecompressPointer r1
    //     0xafca60: add             x1, x1, HEAP, lsl #32
    // 0xafca64: LoadField: r3 = r0->field_f
    //     0xafca64: ldur            w3, [x0, #0xf]
    // 0xafca68: DecompressPointer r3
    //     0xafca68: add             x3, x3, HEAP, lsl #32
    // 0xafca6c: LoadField: r0 = r2->field_b
    //     0xafca6c: ldur            w0, [x2, #0xb]
    // 0xafca70: DecompressPointer r0
    //     0xafca70: add             x0, x0, HEAP, lsl #32
    // 0xafca74: LoadField: r2 = r0->field_5f
    //     0xafca74: ldur            w2, [x0, #0x5f]
    // 0xafca78: DecompressPointer r2
    //     0xafca78: add             x2, x2, HEAP, lsl #32
    // 0xafca7c: LoadField: r4 = r0->field_67
    //     0xafca7c: ldur            w4, [x0, #0x67]
    // 0xafca80: DecompressPointer r4
    //     0xafca80: add             x4, x4, HEAP, lsl #32
    // 0xafca84: stp             x3, x1, [SP, #0x10]
    // 0xafca88: stp             x4, x2, [SP]
    // 0xafca8c: r4 = 0
    //     0xafca8c: movz            x4, #0
    // 0xafca90: ldr             x0, [SP, #0x18]
    // 0xafca94: r16 = UnlinkedCall_0x613b5c
    //     0xafca94: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ad10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafca98: add             x16, x16, #0xd10
    // 0xafca9c: ldp             x5, lr, [x16]
    // 0xafcaa0: blr             lr
    // 0xafcaa4: b               #0xafcbf8
    // 0xafcaa8: ldur            x0, [fp, #-0x10]
    // 0xafcaac: ldur            x2, [fp, #-8]
    // 0xafcab0: LoadField: r1 = r2->field_f
    //     0xafcab0: ldur            w1, [x2, #0xf]
    // 0xafcab4: DecompressPointer r1
    //     0xafcab4: add             x1, x1, HEAP, lsl #32
    // 0xafcab8: LoadField: r3 = r1->field_b
    //     0xafcab8: ldur            w3, [x1, #0xb]
    // 0xafcabc: DecompressPointer r3
    //     0xafcabc: add             x3, x3, HEAP, lsl #32
    // 0xafcac0: cmp             w3, NULL
    // 0xafcac4: b.eq            #0xafcc38
    // 0xafcac8: LoadField: r1 = r3->field_b
    //     0xafcac8: ldur            w1, [x3, #0xb]
    // 0xafcacc: DecompressPointer r1
    //     0xafcacc: add             x1, x1, HEAP, lsl #32
    // 0xafcad0: LoadField: r3 = r1->field_57
    //     0xafcad0: ldur            w3, [x1, #0x57]
    // 0xafcad4: DecompressPointer r3
    //     0xafcad4: add             x3, x3, HEAP, lsl #32
    // 0xafcad8: cmp             w3, NULL
    // 0xafcadc: b.ne            #0xafcae8
    // 0xafcae0: r0 = Null
    //     0xafcae0: mov             x0, NULL
    // 0xafcae4: b               #0xafcb40
    // 0xafcae8: LoadField: r1 = r0->field_13
    //     0xafcae8: ldur            w1, [x0, #0x13]
    // 0xafcaec: DecompressPointer r1
    //     0xafcaec: add             x1, x1, HEAP, lsl #32
    // 0xafcaf0: LoadField: r0 = r3->field_b
    //     0xafcaf0: ldur            w0, [x3, #0xb]
    // 0xafcaf4: r4 = LoadInt32Instr(r1)
    //     0xafcaf4: sbfx            x4, x1, #1, #0x1f
    //     0xafcaf8: tbz             w1, #0, #0xafcb00
    //     0xafcafc: ldur            x4, [x1, #7]
    // 0xafcb00: r1 = LoadInt32Instr(r0)
    //     0xafcb00: sbfx            x1, x0, #1, #0x1f
    // 0xafcb04: mov             x0, x1
    // 0xafcb08: mov             x1, x4
    // 0xafcb0c: cmp             x1, x0
    // 0xafcb10: b.hs            #0xafcc3c
    // 0xafcb14: LoadField: r0 = r3->field_f
    //     0xafcb14: ldur            w0, [x3, #0xf]
    // 0xafcb18: DecompressPointer r0
    //     0xafcb18: add             x0, x0, HEAP, lsl #32
    // 0xafcb1c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xafcb1c: add             x16, x0, x4, lsl #2
    //     0xafcb20: ldur            w1, [x16, #0xf]
    // 0xafcb24: DecompressPointer r1
    //     0xafcb24: add             x1, x1, HEAP, lsl #32
    // 0xafcb28: cmp             w1, NULL
    // 0xafcb2c: b.ne            #0xafcb38
    // 0xafcb30: r0 = Null
    //     0xafcb30: mov             x0, NULL
    // 0xafcb34: b               #0xafcb40
    // 0xafcb38: LoadField: r0 = r1->field_7
    //     0xafcb38: ldur            w0, [x1, #7]
    // 0xafcb3c: DecompressPointer r0
    //     0xafcb3c: add             x0, x0, HEAP, lsl #32
    // 0xafcb40: r1 = LoadClassIdInstr(r0)
    //     0xafcb40: ldur            x1, [x0, #-1]
    //     0xafcb44: ubfx            x1, x1, #0xc, #0x14
    // 0xafcb48: r16 = "track_order"
    //     0xafcb48: add             x16, PP, #0x36, lsl #12  ; [pp+0x36080] "track_order"
    //     0xafcb4c: ldr             x16, [x16, #0x80]
    // 0xafcb50: stp             x16, x0, [SP]
    // 0xafcb54: mov             x0, x1
    // 0xafcb58: mov             lr, x0
    // 0xafcb5c: ldr             lr, [x21, lr, lsl #3]
    // 0xafcb60: blr             lr
    // 0xafcb64: tbnz            w0, #4, #0xafcbd0
    // 0xafcb68: ldur            x0, [fp, #-8]
    // 0xafcb6c: LoadField: r1 = r0->field_f
    //     0xafcb6c: ldur            w1, [x0, #0xf]
    // 0xafcb70: DecompressPointer r1
    //     0xafcb70: add             x1, x1, HEAP, lsl #32
    // 0xafcb74: LoadField: r0 = r1->field_b
    //     0xafcb74: ldur            w0, [x1, #0xb]
    // 0xafcb78: DecompressPointer r0
    //     0xafcb78: add             x0, x0, HEAP, lsl #32
    // 0xafcb7c: cmp             w0, NULL
    // 0xafcb80: b.eq            #0xafcc40
    // 0xafcb84: LoadField: r1 = r0->field_b
    //     0xafcb84: ldur            w1, [x0, #0xb]
    // 0xafcb88: DecompressPointer r1
    //     0xafcb88: add             x1, x1, HEAP, lsl #32
    // 0xafcb8c: LoadField: r2 = r1->field_33
    //     0xafcb8c: ldur            w2, [x1, #0x33]
    // 0xafcb90: DecompressPointer r2
    //     0xafcb90: add             x2, x2, HEAP, lsl #32
    // 0xafcb94: cmp             w2, NULL
    // 0xafcb98: b.eq            #0xafcbf8
    // 0xafcb9c: LoadField: r1 = r0->field_b
    //     0xafcb9c: ldur            w1, [x0, #0xb]
    // 0xafcba0: DecompressPointer r1
    //     0xafcba0: add             x1, x1, HEAP, lsl #32
    // 0xafcba4: LoadField: r0 = r1->field_33
    //     0xafcba4: ldur            w0, [x1, #0x33]
    // 0xafcba8: DecompressPointer r0
    //     0xafcba8: add             x0, x0, HEAP, lsl #32
    // 0xafcbac: cmp             w0, NULL
    // 0xafcbb0: b.eq            #0xafcc44
    // 0xafcbb4: mov             x1, x0
    // 0xafcbb8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xafcbb8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xafcbbc: r0 = parse()
    //     0xafcbbc: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0xafcbc0: mov             x1, x0
    // 0xafcbc4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xafcbc4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xafcbc8: r0 = launchUrl()
    //     0xafcbc8: bl              #0x85cb94  ; [package:url_launcher/src/url_launcher_uri.dart] ::launchUrl
    // 0xafcbcc: b               #0xafcbf8
    // 0xafcbd0: ldur            x0, [fp, #-8]
    // 0xafcbd4: LoadField: r1 = r0->field_f
    //     0xafcbd4: ldur            w1, [x0, #0xf]
    // 0xafcbd8: DecompressPointer r1
    //     0xafcbd8: add             x1, x1, HEAP, lsl #32
    // 0xafcbdc: LoadField: r0 = r1->field_b
    //     0xafcbdc: ldur            w0, [x1, #0xb]
    // 0xafcbe0: DecompressPointer r0
    //     0xafcbe0: add             x0, x0, HEAP, lsl #32
    // 0xafcbe4: cmp             w0, NULL
    // 0xafcbe8: b.eq            #0xafcc48
    // 0xafcbec: LoadField: r2 = r0->field_b
    //     0xafcbec: ldur            w2, [x0, #0xb]
    // 0xafcbf0: DecompressPointer r2
    //     0xafcbf0: add             x2, x2, HEAP, lsl #32
    // 0xafcbf4: r0 = findProductIds()
    //     0xafcbf4: bl              #0xafcc4c  ; [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::findProductIds
    // 0xafcbf8: r0 = Null
    //     0xafcbf8: mov             x0, NULL
    // 0xafcbfc: LeaveFrame
    //     0xafcbfc: mov             SP, fp
    //     0xafcc00: ldp             fp, lr, [SP], #0x10
    // 0xafcc04: ret
    //     0xafcc04: ret             
    // 0xafcc08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafcc08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafcc0c: b               #0xafc5e8
    // 0xafcc10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcc14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcc18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcc20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcc24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcc28: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcc2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcc30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcc34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcc3c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcc40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafcc48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcc48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ findProductIds(/* No info */) {
    // ** addr: 0xafcc4c, size: 0x134
    // 0xafcc4c: EnterFrame
    //     0xafcc4c: stp             fp, lr, [SP, #-0x10]!
    //     0xafcc50: mov             fp, SP
    // 0xafcc54: AllocStack(0x40)
    //     0xafcc54: sub             SP, SP, #0x40
    // 0xafcc58: SetupParameters(_OrderCardState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xafcc58: mov             x4, x1
    //     0xafcc5c: mov             x3, x2
    //     0xafcc60: stur            x1, [fp, #-8]
    //     0xafcc64: stur            x2, [fp, #-0x10]
    // 0xafcc68: CheckStackOverflow
    //     0xafcc68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafcc6c: cmp             SP, x16
    //     0xafcc70: b.ls            #0xafcd6c
    // 0xafcc74: LoadField: r1 = r3->field_2b
    //     0xafcc74: ldur            w1, [x3, #0x2b]
    // 0xafcc78: DecompressPointer r1
    //     0xafcc78: add             x1, x1, HEAP, lsl #32
    // 0xafcc7c: cmp             w1, NULL
    // 0xafcc80: b.ne            #0xafcc8c
    // 0xafcc84: r2 = Null
    //     0xafcc84: mov             x2, NULL
    // 0xafcc88: b               #0xafcca8
    // 0xafcc8c: r0 = LoadClassIdInstr(r1)
    //     0xafcc8c: ldur            x0, [x1, #-1]
    //     0xafcc90: ubfx            x0, x0, #0xc, #0x14
    // 0xafcc94: r2 = "/"
    //     0xafcc94: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0xafcc98: r0 = GDT[cid_x0 + -0xffc]()
    //     0xafcc98: sub             lr, x0, #0xffc
    //     0xafcc9c: ldr             lr, [x21, lr, lsl #3]
    //     0xafcca0: blr             lr
    // 0xafcca4: mov             x2, x0
    // 0xafcca8: cmp             w2, NULL
    // 0xafccac: b.eq            #0xafcd5c
    // 0xafccb0: ldur            x4, [fp, #-8]
    // 0xafccb4: ldur            x3, [fp, #-0x10]
    // 0xafccb8: LoadField: r0 = r2->field_b
    //     0xafccb8: ldur            w0, [x2, #0xb]
    // 0xafccbc: r5 = LoadInt32Instr(r0)
    //     0xafccbc: sbfx            x5, x0, #1, #0x1f
    // 0xafccc0: sub             x6, x5, #2
    // 0xafccc4: mov             x0, x5
    // 0xafccc8: mov             x1, x6
    // 0xafcccc: cmp             x1, x0
    // 0xafccd0: b.hs            #0xafcd74
    // 0xafccd4: LoadField: r7 = r2->field_f
    //     0xafccd4: ldur            w7, [x2, #0xf]
    // 0xafccd8: DecompressPointer r7
    //     0xafccd8: add             x7, x7, HEAP, lsl #32
    // 0xafccdc: ArrayLoad: r2 = r7[r6]  ; Unknown_4
    //     0xafccdc: add             x16, x7, x6, lsl #2
    //     0xafcce0: ldur            w2, [x16, #0xf]
    // 0xafcce4: DecompressPointer r2
    //     0xafcce4: add             x2, x2, HEAP, lsl #32
    // 0xafcce8: sub             x6, x5, #1
    // 0xafccec: mov             x0, x5
    // 0xafccf0: mov             x1, x6
    // 0xafccf4: cmp             x1, x0
    // 0xafccf8: b.hs            #0xafcd78
    // 0xafccfc: ArrayLoad: r0 = r7[r6]  ; Unknown_4
    //     0xafccfc: add             x16, x7, x6, lsl #2
    //     0xafcd00: ldur            w0, [x16, #0xf]
    // 0xafcd04: DecompressPointer r0
    //     0xafcd04: add             x0, x0, HEAP, lsl #32
    // 0xafcd08: LoadField: r1 = r4->field_b
    //     0xafcd08: ldur            w1, [x4, #0xb]
    // 0xafcd0c: DecompressPointer r1
    //     0xafcd0c: add             x1, x1, HEAP, lsl #32
    // 0xafcd10: cmp             w1, NULL
    // 0xafcd14: b.eq            #0xafcd7c
    // 0xafcd18: LoadField: r4 = r1->field_27
    //     0xafcd18: ldur            w4, [x1, #0x27]
    // 0xafcd1c: DecompressPointer r4
    //     0xafcd1c: add             x4, x4, HEAP, lsl #32
    // 0xafcd20: LoadField: r1 = r3->field_6b
    //     0xafcd20: ldur            w1, [x3, #0x6b]
    // 0xafcd24: DecompressPointer r1
    //     0xafcd24: add             x1, x1, HEAP, lsl #32
    // 0xafcd28: LoadField: r5 = r3->field_6f
    //     0xafcd28: ldur            w5, [x3, #0x6f]
    // 0xafcd2c: DecompressPointer r5
    //     0xafcd2c: add             x5, x5, HEAP, lsl #32
    // 0xafcd30: LoadField: r6 = r3->field_7
    //     0xafcd30: ldur            w6, [x3, #7]
    // 0xafcd34: DecompressPointer r6
    //     0xafcd34: add             x6, x6, HEAP, lsl #32
    // 0xafcd38: stp             x2, x4, [SP, #0x20]
    // 0xafcd3c: stp             x1, x0, [SP, #0x10]
    // 0xafcd40: stp             x6, x5, [SP]
    // 0xafcd44: r4 = 0
    //     0xafcd44: movz            x4, #0
    // 0xafcd48: ldr             x0, [SP, #0x28]
    // 0xafcd4c: r16 = UnlinkedCall_0x613b5c
    //     0xafcd4c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ad20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafcd50: add             x16, x16, #0xd20
    // 0xafcd54: ldp             x5, lr, [x16]
    // 0xafcd58: blr             lr
    // 0xafcd5c: r0 = Null
    //     0xafcd5c: mov             x0, NULL
    // 0xafcd60: LeaveFrame
    //     0xafcd60: mov             SP, fp
    //     0xafcd64: ldp             fp, lr, [SP], #0x10
    // 0xafcd68: ret
    //     0xafcd68: ret             
    // 0xafcd6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafcd6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafcd70: b               #0xafcc74
    // 0xafcd74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcd74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcd78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xafcd78: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xafcd7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafcd7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xafcd80, size: 0xe0
    // 0xafcd80: EnterFrame
    //     0xafcd80: stp             fp, lr, [SP, #-0x10]!
    //     0xafcd84: mov             fp, SP
    // 0xafcd88: AllocStack(0x20)
    //     0xafcd88: sub             SP, SP, #0x20
    // 0xafcd8c: SetupParameters()
    //     0xafcd8c: ldr             x0, [fp, #0x10]
    //     0xafcd90: ldur            w1, [x0, #0x17]
    //     0xafcd94: add             x1, x1, HEAP, lsl #32
    // 0xafcd98: CheckStackOverflow
    //     0xafcd98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafcd9c: cmp             SP, x16
    //     0xafcda0: b.ls            #0xafce30
    // 0xafcda4: LoadField: r0 = r1->field_f
    //     0xafcda4: ldur            w0, [x1, #0xf]
    // 0xafcda8: DecompressPointer r0
    //     0xafcda8: add             x0, x0, HEAP, lsl #32
    // 0xafcdac: LoadField: r1 = r0->field_b
    //     0xafcdac: ldur            w1, [x0, #0xb]
    // 0xafcdb0: DecompressPointer r1
    //     0xafcdb0: add             x1, x1, HEAP, lsl #32
    // 0xafcdb4: cmp             w1, NULL
    // 0xafcdb8: b.eq            #0xafce38
    // 0xafcdbc: LoadField: d0 = r0->field_13
    //     0xafcdbc: ldur            d0, [x0, #0x13]
    // 0xafcdc0: LoadField: r0 = r1->field_b
    //     0xafcdc0: ldur            w0, [x1, #0xb]
    // 0xafcdc4: DecompressPointer r0
    //     0xafcdc4: add             x0, x0, HEAP, lsl #32
    // 0xafcdc8: LoadField: r2 = r0->field_8f
    //     0xafcdc8: ldur            w2, [x0, #0x8f]
    // 0xafcdcc: DecompressPointer r2
    //     0xafcdcc: add             x2, x2, HEAP, lsl #32
    // 0xafcdd0: LoadField: r3 = r1->field_2b
    //     0xafcdd0: ldur            w3, [x1, #0x2b]
    // 0xafcdd4: DecompressPointer r3
    //     0xafcdd4: add             x3, x3, HEAP, lsl #32
    // 0xafcdd8: r1 = inline_Allocate_Double()
    //     0xafcdd8: ldp             x1, x4, [THR, #0x50]  ; THR::top
    //     0xafcddc: add             x1, x1, #0x10
    //     0xafcde0: cmp             x4, x1
    //     0xafcde4: b.ls            #0xafce3c
    //     0xafcde8: str             x1, [THR, #0x50]  ; THR::top
    //     0xafcdec: sub             x1, x1, #0xf
    //     0xafcdf0: movz            x4, #0xe15c
    //     0xafcdf4: movk            x4, #0x3, lsl #16
    //     0xafcdf8: stur            x4, [x1, #-1]
    // 0xafcdfc: StoreField: r1->field_7 = d0
    //     0xafcdfc: stur            d0, [x1, #7]
    // 0xafce00: stp             x1, x3, [SP, #0x10]
    // 0xafce04: stp             x2, x0, [SP]
    // 0xafce08: r4 = 0
    //     0xafce08: movz            x4, #0
    // 0xafce0c: ldr             x0, [SP, #0x18]
    // 0xafce10: r16 = UnlinkedCall_0x613b5c
    //     0xafce10: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ad30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafce14: add             x16, x16, #0xd30
    // 0xafce18: ldp             x5, lr, [x16]
    // 0xafce1c: blr             lr
    // 0xafce20: r0 = Null
    //     0xafce20: mov             x0, NULL
    // 0xafce24: LeaveFrame
    //     0xafce24: mov             SP, fp
    //     0xafce28: ldp             fp, lr, [SP], #0x10
    // 0xafce2c: ret
    //     0xafce2c: ret             
    // 0xafce30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafce30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafce34: b               #0xafcda4
    // 0xafce38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafce38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafce3c: SaveReg d0
    //     0xafce3c: str             q0, [SP, #-0x10]!
    // 0xafce40: stp             x2, x3, [SP, #-0x10]!
    // 0xafce44: SaveReg r0
    //     0xafce44: str             x0, [SP, #-8]!
    // 0xafce48: r0 = AllocateDouble()
    //     0xafce48: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xafce4c: mov             x1, x0
    // 0xafce50: RestoreReg r0
    //     0xafce50: ldr             x0, [SP], #8
    // 0xafce54: ldp             x2, x3, [SP], #0x10
    // 0xafce58: RestoreReg d0
    //     0xafce58: ldr             q0, [SP], #0x10
    // 0xafce5c: b               #0xafcdfc
  }
  [closure] void <anonymous closure>(dynamic, double) {
    // ** addr: 0xafcf74, size: 0x120
    // 0xafcf74: EnterFrame
    //     0xafcf74: stp             fp, lr, [SP, #-0x10]!
    //     0xafcf78: mov             fp, SP
    // 0xafcf7c: AllocStack(0x30)
    //     0xafcf7c: sub             SP, SP, #0x30
    // 0xafcf80: SetupParameters()
    //     0xafcf80: ldr             x0, [fp, #0x18]
    //     0xafcf84: ldur            w1, [x0, #0x17]
    //     0xafcf88: add             x1, x1, HEAP, lsl #32
    //     0xafcf8c: stur            x1, [fp, #-8]
    // 0xafcf90: CheckStackOverflow
    //     0xafcf90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafcf94: cmp             SP, x16
    //     0xafcf98: b.ls            #0xafd070
    // 0xafcf9c: r1 = 1
    //     0xafcf9c: movz            x1, #0x1
    // 0xafcfa0: r0 = AllocateContext()
    //     0xafcfa0: bl              #0x16f6108  ; AllocateContextStub
    // 0xafcfa4: mov             x1, x0
    // 0xafcfa8: ldur            x0, [fp, #-8]
    // 0xafcfac: StoreField: r1->field_b = r0
    //     0xafcfac: stur            w0, [x1, #0xb]
    // 0xafcfb0: ldr             x2, [fp, #0x10]
    // 0xafcfb4: StoreField: r1->field_f = r2
    //     0xafcfb4: stur            w2, [x1, #0xf]
    // 0xafcfb8: LoadField: r3 = r0->field_f
    //     0xafcfb8: ldur            w3, [x0, #0xf]
    // 0xafcfbc: DecompressPointer r3
    //     0xafcfbc: add             x3, x3, HEAP, lsl #32
    // 0xafcfc0: mov             x2, x1
    // 0xafcfc4: stur            x3, [fp, #-0x10]
    // 0xafcfc8: r1 = Function '<anonymous closure>':.
    //     0xafcfc8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ad40] AnonymousClosure: (0xa6d968), in [package:customer_app/app/presentation/views/line/orders/order_card.dart] _OrderCardState::build (0xbf6f24)
    //     0xafcfcc: ldr             x1, [x1, #0xd40]
    // 0xafcfd0: r0 = AllocateClosure()
    //     0xafcfd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafcfd4: ldur            x1, [fp, #-0x10]
    // 0xafcfd8: mov             x2, x0
    // 0xafcfdc: r0 = setState()
    //     0xafcfdc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xafcfe0: ldur            x0, [fp, #-8]
    // 0xafcfe4: LoadField: r1 = r0->field_f
    //     0xafcfe4: ldur            w1, [x0, #0xf]
    // 0xafcfe8: DecompressPointer r1
    //     0xafcfe8: add             x1, x1, HEAP, lsl #32
    // 0xafcfec: LoadField: r0 = r1->field_b
    //     0xafcfec: ldur            w0, [x1, #0xb]
    // 0xafcff0: DecompressPointer r0
    //     0xafcff0: add             x0, x0, HEAP, lsl #32
    // 0xafcff4: cmp             w0, NULL
    // 0xafcff8: b.eq            #0xafd078
    // 0xafcffc: LoadField: d0 = r1->field_13
    //     0xafcffc: ldur            d0, [x1, #0x13]
    // 0xafd000: LoadField: r1 = r0->field_b
    //     0xafd000: ldur            w1, [x0, #0xb]
    // 0xafd004: DecompressPointer r1
    //     0xafd004: add             x1, x1, HEAP, lsl #32
    // 0xafd008: LoadField: r2 = r0->field_2b
    //     0xafd008: ldur            w2, [x0, #0x2b]
    // 0xafd00c: DecompressPointer r2
    //     0xafd00c: add             x2, x2, HEAP, lsl #32
    // 0xafd010: r0 = inline_Allocate_Double()
    //     0xafd010: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xafd014: add             x0, x0, #0x10
    //     0xafd018: cmp             x3, x0
    //     0xafd01c: b.ls            #0xafd07c
    //     0xafd020: str             x0, [THR, #0x50]  ; THR::top
    //     0xafd024: sub             x0, x0, #0xf
    //     0xafd028: movz            x3, #0xe15c
    //     0xafd02c: movk            x3, #0x3, lsl #16
    //     0xafd030: stur            x3, [x0, #-1]
    // 0xafd034: StoreField: r0->field_7 = d0
    //     0xafd034: stur            d0, [x0, #7]
    // 0xafd038: stp             x0, x2, [SP, #0x10]
    // 0xafd03c: r16 = "stars"
    //     0xafd03c: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb28] "stars"
    //     0xafd040: ldr             x16, [x16, #0xb28]
    // 0xafd044: stp             x16, x1, [SP]
    // 0xafd048: r4 = 0
    //     0xafd048: movz            x4, #0
    // 0xafd04c: ldr             x0, [SP, #0x18]
    // 0xafd050: r16 = UnlinkedCall_0x613b5c
    //     0xafd050: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ad48] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafd054: add             x16, x16, #0xd48
    // 0xafd058: ldp             x5, lr, [x16]
    // 0xafd05c: blr             lr
    // 0xafd060: r0 = Null
    //     0xafd060: mov             x0, NULL
    // 0xafd064: LeaveFrame
    //     0xafd064: mov             SP, fp
    //     0xafd068: ldp             fp, lr, [SP], #0x10
    // 0xafd06c: ret
    //     0xafd06c: ret             
    // 0xafd070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafd070: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafd074: b               #0xafcf9c
    // 0xafd078: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd078: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafd07c: SaveReg d0
    //     0xafd07c: str             q0, [SP, #-0x10]!
    // 0xafd080: stp             x1, x2, [SP, #-0x10]!
    // 0xafd084: r0 = AllocateDouble()
    //     0xafd084: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xafd088: ldp             x1, x2, [SP], #0x10
    // 0xafd08c: RestoreReg d0
    //     0xafd08c: ldr             q0, [SP], #0x10
    // 0xafd090: b               #0xafd034
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xafd094, size: 0x154
    // 0xafd094: EnterFrame
    //     0xafd094: stp             fp, lr, [SP, #-0x10]!
    //     0xafd098: mov             fp, SP
    // 0xafd09c: AllocStack(0x38)
    //     0xafd09c: sub             SP, SP, #0x38
    // 0xafd0a0: SetupParameters()
    //     0xafd0a0: ldr             x0, [fp, #0x10]
    //     0xafd0a4: ldur            w2, [x0, #0x17]
    //     0xafd0a8: add             x2, x2, HEAP, lsl #32
    //     0xafd0ac: stur            x2, [fp, #-8]
    // 0xafd0b0: CheckStackOverflow
    //     0xafd0b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafd0b4: cmp             SP, x16
    //     0xafd0b8: b.ls            #0xafd1cc
    // 0xafd0bc: LoadField: r0 = r2->field_f
    //     0xafd0bc: ldur            w0, [x2, #0xf]
    // 0xafd0c0: DecompressPointer r0
    //     0xafd0c0: add             x0, x0, HEAP, lsl #32
    // 0xafd0c4: LoadField: r1 = r0->field_b
    //     0xafd0c4: ldur            w1, [x0, #0xb]
    // 0xafd0c8: DecompressPointer r1
    //     0xafd0c8: add             x1, x1, HEAP, lsl #32
    // 0xafd0cc: cmp             w1, NULL
    // 0xafd0d0: b.eq            #0xafd1d4
    // 0xafd0d4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xafd0d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xafd0d8: ldr             x0, [x0, #0x1c80]
    //     0xafd0dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xafd0e0: cmp             w0, w16
    //     0xafd0e4: b.ne            #0xafd0f0
    //     0xafd0e8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xafd0ec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xafd0f0: ldur            x2, [fp, #-8]
    // 0xafd0f4: LoadField: r0 = r2->field_f
    //     0xafd0f4: ldur            w0, [x2, #0xf]
    // 0xafd0f8: DecompressPointer r0
    //     0xafd0f8: add             x0, x0, HEAP, lsl #32
    // 0xafd0fc: LoadField: r1 = r0->field_b
    //     0xafd0fc: ldur            w1, [x0, #0xb]
    // 0xafd100: DecompressPointer r1
    //     0xafd100: add             x1, x1, HEAP, lsl #32
    // 0xafd104: cmp             w1, NULL
    // 0xafd108: b.eq            #0xafd1d8
    // 0xafd10c: LoadField: r0 = r1->field_b
    //     0xafd10c: ldur            w0, [x1, #0xb]
    // 0xafd110: DecompressPointer r0
    //     0xafd110: add             x0, x0, HEAP, lsl #32
    // 0xafd114: LoadField: r1 = r0->field_7
    //     0xafd114: ldur            w1, [x0, #7]
    // 0xafd118: DecompressPointer r1
    //     0xafd118: add             x1, x1, HEAP, lsl #32
    // 0xafd11c: stur            x1, [fp, #-0x20]
    // 0xafd120: cmp             w1, NULL
    // 0xafd124: b.eq            #0xafd1dc
    // 0xafd128: LoadField: r3 = r0->field_f
    //     0xafd128: ldur            w3, [x0, #0xf]
    // 0xafd12c: DecompressPointer r3
    //     0xafd12c: add             x3, x3, HEAP, lsl #32
    // 0xafd130: stur            x3, [fp, #-0x18]
    // 0xafd134: cmp             w3, NULL
    // 0xafd138: b.eq            #0xafd1e0
    // 0xafd13c: LoadField: r4 = r0->field_23
    //     0xafd13c: ldur            w4, [x0, #0x23]
    // 0xafd140: DecompressPointer r4
    //     0xafd140: add             x4, x4, HEAP, lsl #32
    // 0xafd144: stur            x4, [fp, #-0x10]
    // 0xafd148: cmp             w4, NULL
    // 0xafd14c: b.eq            #0xafd1e4
    // 0xafd150: r0 = OrderItemModel()
    //     0xafd150: bl              #0x925de8  ; AllocateOrderItemModelStub -> OrderItemModel (size=0x14)
    // 0xafd154: mov             x1, x0
    // 0xafd158: ldur            x0, [fp, #-0x20]
    // 0xafd15c: StoreField: r1->field_7 = r0
    //     0xafd15c: stur            w0, [x1, #7]
    // 0xafd160: ldur            x0, [fp, #-0x18]
    // 0xafd164: StoreField: r1->field_b = r0
    //     0xafd164: stur            w0, [x1, #0xb]
    // 0xafd168: ldur            x0, [fp, #-0x10]
    // 0xafd16c: StoreField: r1->field_f = r0
    //     0xafd16c: stur            w0, [x1, #0xf]
    // 0xafd170: r16 = "/order"
    //     0xafd170: add             x16, PP, #0xb, lsl #12  ; [pp+0xb430] "/order"
    //     0xafd174: ldr             x16, [x16, #0x430]
    // 0xafd178: stp             x16, NULL, [SP, #8]
    // 0xafd17c: str             x1, [SP]
    // 0xafd180: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xafd180: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xafd184: ldr             x4, [x4, #0x438]
    // 0xafd188: r0 = GetNavigation.toNamed()
    //     0xafd188: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xafd18c: stur            x0, [fp, #-0x10]
    // 0xafd190: cmp             w0, NULL
    // 0xafd194: b.eq            #0xafd1bc
    // 0xafd198: ldur            x2, [fp, #-8]
    // 0xafd19c: r1 = Function '<anonymous closure>':.
    //     0xafd19c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ad58] AnonymousClosure: (0xafd1e8), in [package:customer_app/app/presentation/views/cosmetic/orders/order_card.dart] _OrderCardState::build (0xafa414)
    //     0xafd1a0: ldr             x1, [x1, #0xd58]
    // 0xafd1a4: r0 = AllocateClosure()
    //     0xafd1a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafd1a8: ldur            x16, [fp, #-0x10]
    // 0xafd1ac: stp             x16, NULL, [SP, #8]
    // 0xafd1b0: str             x0, [SP]
    // 0xafd1b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xafd1b4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xafd1b8: r0 = then()
    //     0xafd1b8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xafd1bc: r0 = Null
    //     0xafd1bc: mov             x0, NULL
    // 0xafd1c0: LeaveFrame
    //     0xafd1c0: mov             SP, fp
    //     0xafd1c4: ldp             fp, lr, [SP], #0x10
    // 0xafd1c8: ret
    //     0xafd1c8: ret             
    // 0xafd1cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafd1cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafd1d0: b               #0xafd0bc
    // 0xafd1d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd1d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafd1d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd1d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafd1dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd1dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafd1e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd1e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafd1e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd1e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xafd1e8, size: 0x7c
    // 0xafd1e8: EnterFrame
    //     0xafd1e8: stp             fp, lr, [SP, #-0x10]!
    //     0xafd1ec: mov             fp, SP
    // 0xafd1f0: AllocStack(0x8)
    //     0xafd1f0: sub             SP, SP, #8
    // 0xafd1f4: SetupParameters()
    //     0xafd1f4: ldr             x0, [fp, #0x18]
    //     0xafd1f8: ldur            w1, [x0, #0x17]
    //     0xafd1fc: add             x1, x1, HEAP, lsl #32
    // 0xafd200: CheckStackOverflow
    //     0xafd200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafd204: cmp             SP, x16
    //     0xafd208: b.ls            #0xafd258
    // 0xafd20c: LoadField: r0 = r1->field_f
    //     0xafd20c: ldur            w0, [x1, #0xf]
    // 0xafd210: DecompressPointer r0
    //     0xafd210: add             x0, x0, HEAP, lsl #32
    // 0xafd214: LoadField: r1 = r0->field_b
    //     0xafd214: ldur            w1, [x0, #0xb]
    // 0xafd218: DecompressPointer r1
    //     0xafd218: add             x1, x1, HEAP, lsl #32
    // 0xafd21c: cmp             w1, NULL
    // 0xafd220: b.eq            #0xafd260
    // 0xafd224: LoadField: r0 = r1->field_23
    //     0xafd224: ldur            w0, [x1, #0x23]
    // 0xafd228: DecompressPointer r0
    //     0xafd228: add             x0, x0, HEAP, lsl #32
    // 0xafd22c: str             x0, [SP]
    // 0xafd230: r4 = 0
    //     0xafd230: movz            x4, #0
    // 0xafd234: ldr             x0, [SP]
    // 0xafd238: r16 = UnlinkedCall_0x613b5c
    //     0xafd238: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ad60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafd23c: add             x16, x16, #0xd60
    // 0xafd240: ldp             x5, lr, [x16]
    // 0xafd244: blr             lr
    // 0xafd248: r0 = Null
    //     0xafd248: mov             x0, NULL
    // 0xafd24c: LeaveFrame
    //     0xafd24c: mov             SP, fp
    //     0xafd250: ldp             fp, lr, [SP], #0x10
    // 0xafd254: ret
    //     0xafd254: ret             
    // 0xafd258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafd258: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafd25c: b               #0xafd20c
    // 0xafd260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4152, size: 0x30, field offset: 0xc
//   const constructor, 
class OrderCard extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dd68, size: 0x28
    // 0xc7dd68: EnterFrame
    //     0xc7dd68: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dd6c: mov             fp, SP
    // 0xc7dd70: mov             x0, x1
    // 0xc7dd74: r1 = <OrderCard>
    //     0xc7dd74: add             x1, PP, #0x61, lsl #12  ; [pp+0x61f30] TypeArguments: <OrderCard>
    //     0xc7dd78: ldr             x1, [x1, #0xf30]
    // 0xc7dd7c: r0 = _OrderCardState()
    //     0xc7dd7c: bl              #0xc7dd90  ; Allocate_OrderCardStateStub -> _OrderCardState (size=0x1c)
    // 0xc7dd80: StoreField: r0->field_13 = rZR
    //     0xc7dd80: stur            xzr, [x0, #0x13]
    // 0xc7dd84: LeaveFrame
    //     0xc7dd84: mov             SP, fp
    //     0xc7dd88: ldp             fp, lr, [SP], #0x10
    // 0xc7dd8c: ret
    //     0xc7dd8c: ret             
  }
}
