// lib: , url: package:dio/src/adapter.dart

// class id: 1049597, size: 0x8
class :: {
}

// class id: 4997, size: 0x2c, field offset: 0x8
class ResponseBody extends Object {

  _ ResponseBody(/* No info */) {
    // ** addr: 0x869fc0, size: 0x114
    // 0x869fc0: EnterFrame
    //     0x869fc0: stp             fp, lr, [SP, #-0x10]!
    //     0x869fc4: mov             fp, SP
    // 0x869fc8: AllocStack(0x40)
    //     0x869fc8: sub             SP, SP, #0x40
    // 0x869fcc: SetupParameters(ResponseBody this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r0, fp-0x30 */)
    //     0x869fcc: mov             x4, x1
    //     0x869fd0: stur            x1, [fp, #-8]
    //     0x869fd4: mov             x1, x5
    //     0x869fd8: mov             x0, x7
    //     0x869fdc: stur            x2, [fp, #-0x10]
    //     0x869fe0: stur            x3, [fp, #-0x18]
    //     0x869fe4: stur            x5, [fp, #-0x20]
    //     0x869fe8: stur            x6, [fp, #-0x28]
    //     0x869fec: stur            x7, [fp, #-0x30]
    // 0x869ff0: CheckStackOverflow
    //     0x869ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x869ff4: cmp             SP, x16
    //     0x869ff8: b.ls            #0x86a0cc
    // 0x869ffc: r16 = <String, dynamic>
    //     0x869ffc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x86a000: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x86a004: stp             lr, x16, [SP]
    // 0x86a008: r0 = Map._fromLiteral()
    //     0x86a008: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x86a00c: ldur            x1, [fp, #-8]
    // 0x86a010: StoreField: r1->field_23 = r0
    //     0x86a010: stur            w0, [x1, #0x23]
    //     0x86a014: ldurb           w16, [x1, #-1]
    //     0x86a018: ldurb           w17, [x0, #-1]
    //     0x86a01c: and             x16, x17, x16, lsr #2
    //     0x86a020: tst             x16, HEAP, lsr #32
    //     0x86a024: b.eq            #0x86a02c
    //     0x86a028: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x86a02c: ldur            x0, [fp, #-0x10]
    // 0x86a030: StoreField: r1->field_b = r0
    //     0x86a030: stur            w0, [x1, #0xb]
    //     0x86a034: ldurb           w16, [x1, #-1]
    //     0x86a038: ldurb           w17, [x0, #-1]
    //     0x86a03c: and             x16, x17, x16, lsr #2
    //     0x86a040: tst             x16, HEAP, lsr #32
    //     0x86a044: b.eq            #0x86a04c
    //     0x86a048: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x86a04c: ldur            x2, [fp, #-0x18]
    // 0x86a050: StoreField: r1->field_f = r2
    //     0x86a050: stur            x2, [x1, #0xf]
    // 0x86a054: ldr             x0, [fp, #0x10]
    // 0x86a058: ArrayStore: r1[0] = r0  ; List_4
    //     0x86a058: stur            w0, [x1, #0x17]
    //     0x86a05c: ldurb           w16, [x1, #-1]
    //     0x86a060: ldurb           w17, [x0, #-1]
    //     0x86a064: and             x16, x17, x16, lsr #2
    //     0x86a068: tst             x16, HEAP, lsr #32
    //     0x86a06c: b.eq            #0x86a074
    //     0x86a070: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x86a074: ldur            x2, [fp, #-0x28]
    // 0x86a078: StoreField: r1->field_7 = r2
    //     0x86a078: stur            w2, [x1, #7]
    // 0x86a07c: ldur            x0, [fp, #-0x30]
    // 0x86a080: StoreField: r1->field_1b = r0
    //     0x86a080: stur            w0, [x1, #0x1b]
    //     0x86a084: ldurb           w16, [x1, #-1]
    //     0x86a088: ldurb           w17, [x0, #-1]
    //     0x86a08c: and             x16, x17, x16, lsr #2
    //     0x86a090: tst             x16, HEAP, lsr #32
    //     0x86a094: b.eq            #0x86a09c
    //     0x86a098: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x86a09c: ldur            x0, [fp, #-0x20]
    // 0x86a0a0: StoreField: r1->field_1f = r0
    //     0x86a0a0: stur            w0, [x1, #0x1f]
    //     0x86a0a4: ldurb           w16, [x1, #-1]
    //     0x86a0a8: ldurb           w17, [x0, #-1]
    //     0x86a0ac: and             x16, x17, x16, lsr #2
    //     0x86a0b0: tst             x16, HEAP, lsr #32
    //     0x86a0b4: b.eq            #0x86a0bc
    //     0x86a0b8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x86a0bc: r0 = Null
    //     0x86a0bc: mov             x0, NULL
    // 0x86a0c0: LeaveFrame
    //     0x86a0c0: mov             SP, fp
    //     0x86a0c4: ldp             fp, lr, [SP], #0x10
    // 0x86a0c8: ret
    //     0x86a0c8: ret             
    // 0x86a0cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86a0cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86a0d0: b               #0x869ffc
  }
}

// class id: 4998, size: 0x8, field offset: 0x8
abstract class HttpClientAdapter extends Object {
}
