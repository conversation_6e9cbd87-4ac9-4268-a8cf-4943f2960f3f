// lib: , url: package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart

// class id: 1049580, size: 0x8
class :: {
}

// class id: 3208, size: 0x40, field offset: 0x14
class _RatingReviewAllMediaOnTapImageState extends State<dynamic> {

  late PageController _pageController; // offset: 0x24

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9357e0, size: 0x34
    // 0x9357e0: ldr             x1, [SP]
    // 0x9357e4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9357e4: ldur            w2, [x1, #0x17]
    // 0x9357e8: DecompressPointer r2
    //     0x9357e8: add             x2, x2, HEAP, lsl #32
    // 0x9357ec: LoadField: r1 = r2->field_f
    //     0x9357ec: ldur            w1, [x2, #0xf]
    // 0x9357f0: DecompressPointer r1
    //     0x9357f0: add             x1, x1, HEAP, lsl #32
    // 0x9357f4: LoadField: r2 = r1->field_27
    //     0x9357f4: ldur            w2, [x1, #0x27]
    // 0x9357f8: DecompressPointer r2
    //     0x9357f8: add             x2, x2, HEAP, lsl #32
    // 0x9357fc: LoadField: r3 = r2->field_27
    //     0x9357fc: ldur            w3, [x2, #0x27]
    // 0x935800: DecompressPointer r3
    //     0x935800: add             x3, x3, HEAP, lsl #32
    // 0x935804: eor             x2, x3, #0x10
    // 0x935808: StoreField: r1->field_2b = r2
    //     0x935808: stur            w2, [x1, #0x2b]
    // 0x93580c: r0 = Null
    //     0x93580c: mov             x0, NULL
    // 0x935810: ret
    //     0x935810: ret             
  }
  _ _onCollapseChanged(/* No info */) {
    // ** addr: 0x935814, size: 0x64
    // 0x935814: EnterFrame
    //     0x935814: stp             fp, lr, [SP, #-0x10]!
    //     0x935818: mov             fp, SP
    // 0x93581c: AllocStack(0x8)
    //     0x93581c: sub             SP, SP, #8
    // 0x935820: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r1, fp-0x8 */)
    //     0x935820: stur            x1, [fp, #-8]
    // 0x935824: CheckStackOverflow
    //     0x935824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x935828: cmp             SP, x16
    //     0x93582c: b.ls            #0x935870
    // 0x935830: r1 = 1
    //     0x935830: movz            x1, #0x1
    // 0x935834: r0 = AllocateContext()
    //     0x935834: bl              #0x16f6108  ; AllocateContextStub
    // 0x935838: mov             x1, x0
    // 0x93583c: ldur            x0, [fp, #-8]
    // 0x935840: StoreField: r1->field_f = r0
    //     0x935840: stur            w0, [x1, #0xf]
    // 0x935844: mov             x2, x1
    // 0x935848: r1 = Function '<anonymous closure>':.
    //     0x935848: add             x1, PP, #0x52, lsl #12  ; [pp+0x52060] AnonymousClosure: (0x9357e0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x935814)
    //     0x93584c: ldr             x1, [x1, #0x60]
    // 0x935850: r0 = AllocateClosure()
    //     0x935850: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x935854: ldur            x1, [fp, #-8]
    // 0x935858: mov             x2, x0
    // 0x93585c: r0 = setState()
    //     0x93585c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x935860: r0 = Null
    //     0x935860: mov             x0, NULL
    // 0x935864: LeaveFrame
    //     0x935864: mov             SP, fp
    //     0x935868: ldp             fp, lr, [SP], #0x10
    // 0x93586c: ret
    //     0x93586c: ret             
    // 0x935870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x935870: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x935874: b               #0x935830
  }
  [closure] void _onCollapseChanged(dynamic) {
    // ** addr: 0x935878, size: 0x38
    // 0x935878: EnterFrame
    //     0x935878: stp             fp, lr, [SP, #-0x10]!
    //     0x93587c: mov             fp, SP
    // 0x935880: ldr             x0, [fp, #0x10]
    // 0x935884: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x935884: ldur            w1, [x0, #0x17]
    // 0x935888: DecompressPointer r1
    //     0x935888: add             x1, x1, HEAP, lsl #32
    // 0x93588c: CheckStackOverflow
    //     0x93588c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x935890: cmp             SP, x16
    //     0x935894: b.ls            #0x9358a8
    // 0x935898: r0 = _onCollapseChanged()
    //     0x935898: bl              #0x935814  ; [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged
    // 0x93589c: LeaveFrame
    //     0x93589c: mov             SP, fp
    //     0x9358a0: ldp             fp, lr, [SP], #0x10
    // 0x9358a4: ret
    //     0x9358a4: ret             
    // 0x9358a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9358a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9358ac: b               #0x935898
  }
  _ initState(/* No info */) {
    // ** addr: 0x94b2d8, size: 0x18c
    // 0x94b2d8: EnterFrame
    //     0x94b2d8: stp             fp, lr, [SP, #-0x10]!
    //     0x94b2dc: mov             fp, SP
    // 0x94b2e0: AllocStack(0x18)
    //     0x94b2e0: sub             SP, SP, #0x18
    // 0x94b2e4: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */)
    //     0x94b2e4: mov             x0, x1
    //     0x94b2e8: stur            x1, [fp, #-8]
    // 0x94b2ec: CheckStackOverflow
    //     0x94b2ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b2f0: cmp             SP, x16
    //     0x94b2f4: b.ls            #0x94b450
    // 0x94b2f8: LoadField: r1 = r0->field_b
    //     0x94b2f8: ldur            w1, [x0, #0xb]
    // 0x94b2fc: DecompressPointer r1
    //     0x94b2fc: add             x1, x1, HEAP, lsl #32
    // 0x94b300: cmp             w1, NULL
    // 0x94b304: b.eq            #0x94b458
    // 0x94b308: LoadField: r2 = r1->field_13
    //     0x94b308: ldur            x2, [x1, #0x13]
    // 0x94b30c: StoreField: r0->field_13 = r2
    //     0x94b30c: stur            x2, [x0, #0x13]
    // 0x94b310: LoadField: r2 = r1->field_1b
    //     0x94b310: ldur            w2, [x1, #0x1b]
    // 0x94b314: DecompressPointer r2
    //     0x94b314: add             x2, x2, HEAP, lsl #32
    // 0x94b318: cmp             w2, NULL
    // 0x94b31c: b.ne            #0x94b328
    // 0x94b320: r1 = 0
    //     0x94b320: movz            x1, #0
    // 0x94b324: b               #0x94b334
    // 0x94b328: r1 = LoadInt32Instr(r2)
    //     0x94b328: sbfx            x1, x2, #1, #0x1f
    //     0x94b32c: tbz             w2, #0, #0x94b334
    //     0x94b330: ldur            x1, [x2, #7]
    // 0x94b334: StoreField: r0->field_1b = r1
    //     0x94b334: stur            x1, [x0, #0x1b]
    // 0x94b338: mov             x1, x0
    // 0x94b33c: r0 = _flattenMediaList()
    //     0x94b33c: bl              #0x94b464  ; [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_flattenMediaList
    // 0x94b340: ldur            x0, [fp, #-8]
    // 0x94b344: LoadField: r1 = r0->field_b
    //     0x94b344: ldur            w1, [x0, #0xb]
    // 0x94b348: DecompressPointer r1
    //     0x94b348: add             x1, x1, HEAP, lsl #32
    // 0x94b34c: cmp             w1, NULL
    // 0x94b350: b.eq            #0x94b45c
    // 0x94b354: LoadField: r2 = r1->field_13
    //     0x94b354: ldur            x2, [x1, #0x13]
    // 0x94b358: LoadField: r3 = r1->field_1b
    //     0x94b358: ldur            w3, [x1, #0x1b]
    // 0x94b35c: DecompressPointer r3
    //     0x94b35c: add             x3, x3, HEAP, lsl #32
    // 0x94b360: cmp             w3, NULL
    // 0x94b364: b.ne            #0x94b370
    // 0x94b368: r3 = 0
    //     0x94b368: movz            x3, #0
    // 0x94b36c: b               #0x94b380
    // 0x94b370: r1 = LoadInt32Instr(r3)
    //     0x94b370: sbfx            x1, x3, #1, #0x1f
    //     0x94b374: tbz             w3, #0, #0x94b37c
    //     0x94b378: ldur            x1, [x3, #7]
    // 0x94b37c: mov             x3, x1
    // 0x94b380: mov             x1, x0
    // 0x94b384: r0 = _getGlobalIndex()
    //     0x94b384: bl              #0x935398  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_getGlobalIndex
    // 0x94b388: stur            x0, [fp, #-0x10]
    // 0x94b38c: r0 = PageController()
    //     0x94b38c: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x94b390: mov             x2, x0
    // 0x94b394: ldur            x0, [fp, #-0x10]
    // 0x94b398: stur            x2, [fp, #-0x18]
    // 0x94b39c: StoreField: r2->field_3f = r0
    //     0x94b39c: stur            x0, [x2, #0x3f]
    // 0x94b3a0: r0 = true
    //     0x94b3a0: add             x0, NULL, #0x20  ; true
    // 0x94b3a4: StoreField: r2->field_47 = r0
    //     0x94b3a4: stur            w0, [x2, #0x47]
    // 0x94b3a8: d0 = 1.000000
    //     0x94b3a8: fmov            d0, #1.00000000
    // 0x94b3ac: StoreField: r2->field_4b = d0
    //     0x94b3ac: stur            d0, [x2, #0x4b]
    // 0x94b3b0: mov             x1, x2
    // 0x94b3b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x94b3b4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94b3b8: r0 = ScrollController()
    //     0x94b3b8: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94b3bc: ldur            x0, [fp, #-0x18]
    // 0x94b3c0: ldur            x3, [fp, #-8]
    // 0x94b3c4: StoreField: r3->field_23 = r0
    //     0x94b3c4: stur            w0, [x3, #0x23]
    //     0x94b3c8: ldurb           w16, [x3, #-1]
    //     0x94b3cc: ldurb           w17, [x0, #-1]
    //     0x94b3d0: and             x16, x17, x16, lsr #2
    //     0x94b3d4: tst             x16, HEAP, lsr #32
    //     0x94b3d8: b.eq            #0x94b3e0
    //     0x94b3dc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x94b3e0: LoadField: r0 = r3->field_27
    //     0x94b3e0: ldur            w0, [x3, #0x27]
    // 0x94b3e4: DecompressPointer r0
    //     0x94b3e4: add             x0, x0, HEAP, lsl #32
    // 0x94b3e8: mov             x2, x3
    // 0x94b3ec: stur            x0, [fp, #-0x18]
    // 0x94b3f0: r1 = Function '_onCollapseChanged@1749268759':.
    //     0x94b3f0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52058] AnonymousClosure: (0x935878), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x935814)
    //     0x94b3f4: ldr             x1, [x1, #0x58]
    // 0x94b3f8: r0 = AllocateClosure()
    //     0x94b3f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94b3fc: ldur            x1, [fp, #-0x18]
    // 0x94b400: mov             x2, x0
    // 0x94b404: r0 = addListener()
    //     0x94b404: bl              #0x7b8dac  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x94b408: ldur            x1, [fp, #-8]
    // 0x94b40c: LoadField: r2 = r1->field_b
    //     0x94b40c: ldur            w2, [x1, #0xb]
    // 0x94b410: DecompressPointer r2
    //     0x94b410: add             x2, x2, HEAP, lsl #32
    // 0x94b414: cmp             w2, NULL
    // 0x94b418: b.eq            #0x94b460
    // 0x94b41c: LoadField: r0 = r2->field_23
    //     0x94b41c: ldur            w0, [x2, #0x23]
    // 0x94b420: DecompressPointer r0
    //     0x94b420: add             x0, x0, HEAP, lsl #32
    // 0x94b424: StoreField: r1->field_2f = r0
    //     0x94b424: stur            w0, [x1, #0x2f]
    //     0x94b428: ldurb           w16, [x1, #-1]
    //     0x94b42c: ldurb           w17, [x0, #-1]
    //     0x94b430: and             x16, x17, x16, lsr #2
    //     0x94b434: tst             x16, HEAP, lsr #32
    //     0x94b438: b.eq            #0x94b440
    //     0x94b43c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94b440: r0 = Null
    //     0x94b440: mov             x0, NULL
    // 0x94b444: LeaveFrame
    //     0x94b444: mov             SP, fp
    //     0x94b448: ldp             fp, lr, [SP], #0x10
    // 0x94b44c: ret
    //     0x94b44c: ret             
    // 0x94b450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b450: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b454: b               #0x94b2f8
    // 0x94b458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b458: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94b45c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b45c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94b460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b460: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _flattenMediaList(/* No info */) {
    // ** addr: 0x94b464, size: 0x304
    // 0x94b464: EnterFrame
    //     0x94b464: stp             fp, lr, [SP, #-0x10]!
    //     0x94b468: mov             fp, SP
    // 0x94b46c: AllocStack(0x58)
    //     0x94b46c: sub             SP, SP, #0x58
    // 0x94b470: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */)
    //     0x94b470: mov             x0, x1
    //     0x94b474: stur            x1, [fp, #-8]
    // 0x94b478: CheckStackOverflow
    //     0x94b478: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b47c: cmp             SP, x16
    //     0x94b480: b.ls            #0x94b74c
    // 0x94b484: LoadField: r1 = r0->field_37
    //     0x94b484: ldur            w1, [x0, #0x37]
    // 0x94b488: DecompressPointer r1
    //     0x94b488: add             x1, x1, HEAP, lsl #32
    // 0x94b48c: r0 = clear()
    //     0x94b48c: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x94b490: ldur            x0, [fp, #-8]
    // 0x94b494: LoadField: r1 = r0->field_3b
    //     0x94b494: ldur            w1, [x0, #0x3b]
    // 0x94b498: DecompressPointer r1
    //     0x94b498: add             x1, x1, HEAP, lsl #32
    // 0x94b49c: r0 = clear()
    //     0x94b49c: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x94b4a0: r4 = 0
    //     0x94b4a0: movz            x4, #0
    // 0x94b4a4: ldur            x3, [fp, #-8]
    // 0x94b4a8: stur            x4, [fp, #-0x40]
    // 0x94b4ac: CheckStackOverflow
    //     0x94b4ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b4b0: cmp             SP, x16
    //     0x94b4b4: b.ls            #0x94b754
    // 0x94b4b8: LoadField: r0 = r3->field_b
    //     0x94b4b8: ldur            w0, [x3, #0xb]
    // 0x94b4bc: DecompressPointer r0
    //     0x94b4bc: add             x0, x0, HEAP, lsl #32
    // 0x94b4c0: cmp             w0, NULL
    // 0x94b4c4: b.eq            #0x94b75c
    // 0x94b4c8: LoadField: r1 = r0->field_f
    //     0x94b4c8: ldur            w1, [x0, #0xf]
    // 0x94b4cc: DecompressPointer r1
    //     0x94b4cc: add             x1, x1, HEAP, lsl #32
    // 0x94b4d0: LoadField: r0 = r1->field_b
    //     0x94b4d0: ldur            w0, [x1, #0xb]
    // 0x94b4d4: r2 = LoadInt32Instr(r0)
    //     0x94b4d4: sbfx            x2, x0, #1, #0x1f
    // 0x94b4d8: cmp             x4, x2
    // 0x94b4dc: b.ge            #0x94b73c
    // 0x94b4e0: LoadField: r0 = r1->field_f
    //     0x94b4e0: ldur            w0, [x1, #0xf]
    // 0x94b4e4: DecompressPointer r0
    //     0x94b4e4: add             x0, x0, HEAP, lsl #32
    // 0x94b4e8: lsl             x5, x4, #1
    // 0x94b4ec: stur            x5, [fp, #-0x38]
    // 0x94b4f0: ArrayLoad: r6 = r0[r4]  ; Unknown_4
    //     0x94b4f0: add             x16, x0, x4, lsl #2
    //     0x94b4f4: ldur            w6, [x16, #0xf]
    // 0x94b4f8: DecompressPointer r6
    //     0x94b4f8: add             x6, x6, HEAP, lsl #32
    // 0x94b4fc: stur            x6, [fp, #-0x30]
    // 0x94b500: r7 = 0
    //     0x94b500: movz            x7, #0
    // 0x94b504: stur            x7, [fp, #-0x28]
    // 0x94b508: CheckStackOverflow
    //     0x94b508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b50c: cmp             SP, x16
    //     0x94b510: b.ls            #0x94b760
    // 0x94b514: LoadField: r0 = r6->field_1b
    //     0x94b514: ldur            w0, [x6, #0x1b]
    // 0x94b518: DecompressPointer r0
    //     0x94b518: add             x0, x0, HEAP, lsl #32
    // 0x94b51c: LoadField: r1 = r0->field_b
    //     0x94b51c: ldur            w1, [x0, #0xb]
    // 0x94b520: r2 = LoadInt32Instr(r1)
    //     0x94b520: sbfx            x2, x1, #1, #0x1f
    // 0x94b524: cmp             x7, x2
    // 0x94b528: b.ge            #0x94b730
    // 0x94b52c: LoadField: r8 = r3->field_37
    //     0x94b52c: ldur            w8, [x3, #0x37]
    // 0x94b530: DecompressPointer r8
    //     0x94b530: add             x8, x8, HEAP, lsl #32
    // 0x94b534: stur            x8, [fp, #-0x20]
    // 0x94b538: LoadField: r1 = r0->field_f
    //     0x94b538: ldur            w1, [x0, #0xf]
    // 0x94b53c: DecompressPointer r1
    //     0x94b53c: add             x1, x1, HEAP, lsl #32
    // 0x94b540: lsl             x9, x7, #1
    // 0x94b544: stur            x9, [fp, #-0x18]
    // 0x94b548: ArrayLoad: r10 = r1[r7]  ; Unknown_4
    //     0x94b548: add             x16, x1, x7, lsl #2
    //     0x94b54c: ldur            w10, [x16, #0xf]
    // 0x94b550: DecompressPointer r10
    //     0x94b550: add             x10, x10, HEAP, lsl #32
    // 0x94b554: stur            x10, [fp, #-0x10]
    // 0x94b558: LoadField: r2 = r8->field_7
    //     0x94b558: ldur            w2, [x8, #7]
    // 0x94b55c: DecompressPointer r2
    //     0x94b55c: add             x2, x2, HEAP, lsl #32
    // 0x94b560: mov             x0, x10
    // 0x94b564: r1 = Null
    //     0x94b564: mov             x1, NULL
    // 0x94b568: cmp             w2, NULL
    // 0x94b56c: b.eq            #0x94b58c
    // 0x94b570: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x94b570: ldur            w4, [x2, #0x17]
    // 0x94b574: DecompressPointer r4
    //     0x94b574: add             x4, x4, HEAP, lsl #32
    // 0x94b578: r8 = X0
    //     0x94b578: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x94b57c: LoadField: r9 = r4->field_7
    //     0x94b57c: ldur            x9, [x4, #7]
    // 0x94b580: r3 = Null
    //     0x94b580: add             x3, PP, #0x52, lsl #12  ; [pp+0x52068] Null
    //     0x94b584: ldr             x3, [x3, #0x68]
    // 0x94b588: blr             x9
    // 0x94b58c: ldur            x0, [fp, #-0x20]
    // 0x94b590: LoadField: r1 = r0->field_b
    //     0x94b590: ldur            w1, [x0, #0xb]
    // 0x94b594: LoadField: r2 = r0->field_f
    //     0x94b594: ldur            w2, [x0, #0xf]
    // 0x94b598: DecompressPointer r2
    //     0x94b598: add             x2, x2, HEAP, lsl #32
    // 0x94b59c: LoadField: r3 = r2->field_b
    //     0x94b59c: ldur            w3, [x2, #0xb]
    // 0x94b5a0: r2 = LoadInt32Instr(r1)
    //     0x94b5a0: sbfx            x2, x1, #1, #0x1f
    // 0x94b5a4: stur            x2, [fp, #-0x48]
    // 0x94b5a8: r1 = LoadInt32Instr(r3)
    //     0x94b5a8: sbfx            x1, x3, #1, #0x1f
    // 0x94b5ac: cmp             x2, x1
    // 0x94b5b0: b.ne            #0x94b5bc
    // 0x94b5b4: mov             x1, x0
    // 0x94b5b8: r0 = _growToNextCapacity()
    //     0x94b5b8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x94b5bc: ldur            x3, [fp, #-8]
    // 0x94b5c0: ldur            x0, [fp, #-0x20]
    // 0x94b5c4: ldur            x4, [fp, #-0x38]
    // 0x94b5c8: ldur            x5, [fp, #-0x18]
    // 0x94b5cc: ldur            x2, [fp, #-0x48]
    // 0x94b5d0: add             x1, x2, #1
    // 0x94b5d4: lsl             x6, x1, #1
    // 0x94b5d8: StoreField: r0->field_b = r6
    //     0x94b5d8: stur            w6, [x0, #0xb]
    // 0x94b5dc: LoadField: r1 = r0->field_f
    //     0x94b5dc: ldur            w1, [x0, #0xf]
    // 0x94b5e0: DecompressPointer r1
    //     0x94b5e0: add             x1, x1, HEAP, lsl #32
    // 0x94b5e4: ldur            x0, [fp, #-0x10]
    // 0x94b5e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x94b5e8: add             x25, x1, x2, lsl #2
    //     0x94b5ec: add             x25, x25, #0xf
    //     0x94b5f0: str             w0, [x25]
    //     0x94b5f4: tbz             w0, #0, #0x94b610
    //     0x94b5f8: ldurb           w16, [x1, #-1]
    //     0x94b5fc: ldurb           w17, [x0, #-1]
    //     0x94b600: and             x16, x17, x16, lsr #2
    //     0x94b604: tst             x16, HEAP, lsr #32
    //     0x94b608: b.eq            #0x94b610
    //     0x94b60c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94b610: LoadField: r0 = r3->field_3b
    //     0x94b610: ldur            w0, [x3, #0x3b]
    // 0x94b614: DecompressPointer r0
    //     0x94b614: add             x0, x0, HEAP, lsl #32
    // 0x94b618: stur            x0, [fp, #-0x10]
    // 0x94b61c: r1 = Null
    //     0x94b61c: mov             x1, NULL
    // 0x94b620: r2 = 8
    //     0x94b620: movz            x2, #0x8
    // 0x94b624: r0 = AllocateArray()
    //     0x94b624: bl              #0x16f7198  ; AllocateArrayStub
    // 0x94b628: r16 = "parentIndex"
    //     0x94b628: add             x16, PP, #0x52, lsl #12  ; [pp+0x52048] "parentIndex"
    //     0x94b62c: ldr             x16, [x16, #0x48]
    // 0x94b630: StoreField: r0->field_f = r16
    //     0x94b630: stur            w16, [x0, #0xf]
    // 0x94b634: ldur            x1, [fp, #-0x38]
    // 0x94b638: StoreField: r0->field_13 = r1
    //     0x94b638: stur            w1, [x0, #0x13]
    // 0x94b63c: r16 = "childIndex"
    //     0x94b63c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52050] "childIndex"
    //     0x94b640: ldr             x16, [x16, #0x50]
    // 0x94b644: ArrayStore: r0[0] = r16  ; List_4
    //     0x94b644: stur            w16, [x0, #0x17]
    // 0x94b648: ldur            x2, [fp, #-0x18]
    // 0x94b64c: StoreField: r0->field_1b = r2
    //     0x94b64c: stur            w2, [x0, #0x1b]
    // 0x94b650: r16 = <String, int>
    //     0x94b650: ldr             x16, [PP, #0xd40]  ; [pp+0xd40] TypeArguments: <String, int>
    // 0x94b654: stp             x0, x16, [SP]
    // 0x94b658: r0 = Map._fromLiteral()
    //     0x94b658: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x94b65c: mov             x4, x0
    // 0x94b660: ldur            x3, [fp, #-0x10]
    // 0x94b664: stur            x4, [fp, #-0x18]
    // 0x94b668: LoadField: r2 = r3->field_7
    //     0x94b668: ldur            w2, [x3, #7]
    // 0x94b66c: DecompressPointer r2
    //     0x94b66c: add             x2, x2, HEAP, lsl #32
    // 0x94b670: mov             x0, x4
    // 0x94b674: r1 = Null
    //     0x94b674: mov             x1, NULL
    // 0x94b678: cmp             w2, NULL
    // 0x94b67c: b.eq            #0x94b69c
    // 0x94b680: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x94b680: ldur            w4, [x2, #0x17]
    // 0x94b684: DecompressPointer r4
    //     0x94b684: add             x4, x4, HEAP, lsl #32
    // 0x94b688: r8 = X0
    //     0x94b688: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x94b68c: LoadField: r9 = r4->field_7
    //     0x94b68c: ldur            x9, [x4, #7]
    // 0x94b690: r3 = Null
    //     0x94b690: add             x3, PP, #0x52, lsl #12  ; [pp+0x52078] Null
    //     0x94b694: ldr             x3, [x3, #0x78]
    // 0x94b698: blr             x9
    // 0x94b69c: ldur            x0, [fp, #-0x10]
    // 0x94b6a0: LoadField: r1 = r0->field_b
    //     0x94b6a0: ldur            w1, [x0, #0xb]
    // 0x94b6a4: LoadField: r2 = r0->field_f
    //     0x94b6a4: ldur            w2, [x0, #0xf]
    // 0x94b6a8: DecompressPointer r2
    //     0x94b6a8: add             x2, x2, HEAP, lsl #32
    // 0x94b6ac: LoadField: r3 = r2->field_b
    //     0x94b6ac: ldur            w3, [x2, #0xb]
    // 0x94b6b0: r2 = LoadInt32Instr(r1)
    //     0x94b6b0: sbfx            x2, x1, #1, #0x1f
    // 0x94b6b4: stur            x2, [fp, #-0x48]
    // 0x94b6b8: r1 = LoadInt32Instr(r3)
    //     0x94b6b8: sbfx            x1, x3, #1, #0x1f
    // 0x94b6bc: cmp             x2, x1
    // 0x94b6c0: b.ne            #0x94b6cc
    // 0x94b6c4: mov             x1, x0
    // 0x94b6c8: r0 = _growToNextCapacity()
    //     0x94b6c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x94b6cc: ldur            x4, [fp, #-0x28]
    // 0x94b6d0: ldur            x2, [fp, #-0x10]
    // 0x94b6d4: ldur            x3, [fp, #-0x48]
    // 0x94b6d8: add             x5, x3, #1
    // 0x94b6dc: lsl             x6, x5, #1
    // 0x94b6e0: StoreField: r2->field_b = r6
    //     0x94b6e0: stur            w6, [x2, #0xb]
    // 0x94b6e4: LoadField: r1 = r2->field_f
    //     0x94b6e4: ldur            w1, [x2, #0xf]
    // 0x94b6e8: DecompressPointer r1
    //     0x94b6e8: add             x1, x1, HEAP, lsl #32
    // 0x94b6ec: ldur            x0, [fp, #-0x18]
    // 0x94b6f0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x94b6f0: add             x25, x1, x3, lsl #2
    //     0x94b6f4: add             x25, x25, #0xf
    //     0x94b6f8: str             w0, [x25]
    //     0x94b6fc: tbz             w0, #0, #0x94b718
    //     0x94b700: ldurb           w16, [x1, #-1]
    //     0x94b704: ldurb           w17, [x0, #-1]
    //     0x94b708: and             x16, x17, x16, lsr #2
    //     0x94b70c: tst             x16, HEAP, lsr #32
    //     0x94b710: b.eq            #0x94b718
    //     0x94b714: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94b718: add             x7, x4, #1
    // 0x94b71c: ldur            x3, [fp, #-8]
    // 0x94b720: ldur            x4, [fp, #-0x40]
    // 0x94b724: ldur            x5, [fp, #-0x38]
    // 0x94b728: ldur            x6, [fp, #-0x30]
    // 0x94b72c: b               #0x94b504
    // 0x94b730: mov             x1, x4
    // 0x94b734: add             x4, x1, #1
    // 0x94b738: b               #0x94b4a4
    // 0x94b73c: r0 = Null
    //     0x94b73c: mov             x0, NULL
    // 0x94b740: LeaveFrame
    //     0x94b740: mov             SP, fp
    //     0x94b744: ldp             fp, lr, [SP], #0x10
    // 0x94b748: ret
    //     0x94b748: ret             
    // 0x94b74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b74c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b750: b               #0x94b484
    // 0x94b754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b754: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b758: b               #0x94b4b8
    // 0x94b75c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94b75c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94b760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b760: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b764: b               #0x94b514
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa4b44, size: 0xb4
    // 0xaa4b44: EnterFrame
    //     0xaa4b44: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4b48: mov             fp, SP
    // 0xaa4b4c: AllocStack(0x18)
    //     0xaa4b4c: sub             SP, SP, #0x18
    // 0xaa4b50: SetupParameters()
    //     0xaa4b50: ldr             x0, [fp, #0x10]
    //     0xaa4b54: ldur            w4, [x0, #0x17]
    //     0xaa4b58: add             x4, x4, HEAP, lsl #32
    //     0xaa4b5c: stur            x4, [fp, #-8]
    // 0xaa4b60: CheckStackOverflow
    //     0xaa4b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4b64: cmp             SP, x16
    //     0xaa4b68: b.ls            #0xaa4bec
    // 0xaa4b6c: LoadField: r0 = r4->field_f
    //     0xaa4b6c: ldur            w0, [x4, #0xf]
    // 0xaa4b70: DecompressPointer r0
    //     0xaa4b70: add             x0, x0, HEAP, lsl #32
    // 0xaa4b74: LoadField: r1 = r0->field_2f
    //     0xaa4b74: ldur            w1, [x0, #0x2f]
    // 0xaa4b78: DecompressPointer r1
    //     0xaa4b78: add             x1, x1, HEAP, lsl #32
    // 0xaa4b7c: LoadField: r2 = r4->field_13
    //     0xaa4b7c: ldur            w2, [x4, #0x13]
    // 0xaa4b80: DecompressPointer r2
    //     0xaa4b80: add             x2, x2, HEAP, lsl #32
    // 0xaa4b84: r0 = LoadClassIdInstr(r1)
    //     0xaa4b84: ldur            x0, [x1, #-1]
    //     0xaa4b88: ubfx            x0, x0, #0xc, #0x14
    // 0xaa4b8c: r3 = true
    //     0xaa4b8c: add             x3, NULL, #0x20  ; true
    // 0xaa4b90: r0 = GDT[cid_x0 + 0x35a]()
    //     0xaa4b90: add             lr, x0, #0x35a
    //     0xaa4b94: ldr             lr, [x21, lr, lsl #3]
    //     0xaa4b98: blr             lr
    // 0xaa4b9c: ldur            x0, [fp, #-8]
    // 0xaa4ba0: LoadField: r1 = r0->field_f
    //     0xaa4ba0: ldur            w1, [x0, #0xf]
    // 0xaa4ba4: DecompressPointer r1
    //     0xaa4ba4: add             x1, x1, HEAP, lsl #32
    // 0xaa4ba8: LoadField: r0 = r1->field_b
    //     0xaa4ba8: ldur            w0, [x1, #0xb]
    // 0xaa4bac: DecompressPointer r0
    //     0xaa4bac: add             x0, x0, HEAP, lsl #32
    // 0xaa4bb0: cmp             w0, NULL
    // 0xaa4bb4: b.eq            #0xaa4bf4
    // 0xaa4bb8: LoadField: r2 = r1->field_2f
    //     0xaa4bb8: ldur            w2, [x1, #0x2f]
    // 0xaa4bbc: DecompressPointer r2
    //     0xaa4bbc: add             x2, x2, HEAP, lsl #32
    // 0xaa4bc0: LoadField: r1 = r0->field_1f
    //     0xaa4bc0: ldur            w1, [x0, #0x1f]
    // 0xaa4bc4: DecompressPointer r1
    //     0xaa4bc4: add             x1, x1, HEAP, lsl #32
    // 0xaa4bc8: stp             x2, x1, [SP]
    // 0xaa4bcc: mov             x0, x1
    // 0xaa4bd0: ClosureCall
    //     0xaa4bd0: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xaa4bd4: ldur            x2, [x0, #0x1f]
    //     0xaa4bd8: blr             x2
    // 0xaa4bdc: r0 = Null
    //     0xaa4bdc: mov             x0, NULL
    // 0xaa4be0: LeaveFrame
    //     0xaa4be0: mov             SP, fp
    //     0xaa4be4: ldp             fp, lr, [SP], #0x10
    // 0xaa4be8: ret
    //     0xaa4be8: ret             
    // 0xaa4bec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa4bec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa4bf0: b               #0xaa4b6c
    // 0xaa4bf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa4bf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xaa4bf8, size: 0x94
    // 0xaa4bf8: EnterFrame
    //     0xaa4bf8: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4bfc: mov             fp, SP
    // 0xaa4c00: AllocStack(0x20)
    //     0xaa4c00: sub             SP, SP, #0x20
    // 0xaa4c04: SetupParameters()
    //     0xaa4c04: ldr             x0, [fp, #0x18]
    //     0xaa4c08: ldur            w2, [x0, #0x17]
    //     0xaa4c0c: add             x2, x2, HEAP, lsl #32
    //     0xaa4c10: stur            x2, [fp, #-8]
    // 0xaa4c14: CheckStackOverflow
    //     0xaa4c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4c18: cmp             SP, x16
    //     0xaa4c1c: b.ls            #0xaa4c84
    // 0xaa4c20: ldr             x0, [fp, #0x10]
    // 0xaa4c24: r1 = LoadClassIdInstr(r0)
    //     0xaa4c24: ldur            x1, [x0, #-1]
    //     0xaa4c28: ubfx            x1, x1, #0xc, #0x14
    // 0xaa4c2c: r16 = "flag"
    //     0xaa4c2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xaa4c30: ldr             x16, [x16, #0xa68]
    // 0xaa4c34: stp             x16, x0, [SP]
    // 0xaa4c38: mov             x0, x1
    // 0xaa4c3c: mov             lr, x0
    // 0xaa4c40: ldr             lr, [x21, lr, lsl #3]
    // 0xaa4c44: blr             lr
    // 0xaa4c48: tbnz            w0, #4, #0xaa4c74
    // 0xaa4c4c: ldur            x2, [fp, #-8]
    // 0xaa4c50: LoadField: r0 = r2->field_f
    //     0xaa4c50: ldur            w0, [x2, #0xf]
    // 0xaa4c54: DecompressPointer r0
    //     0xaa4c54: add             x0, x0, HEAP, lsl #32
    // 0xaa4c58: stur            x0, [fp, #-0x10]
    // 0xaa4c5c: r1 = Function '<anonymous closure>':.
    //     0xaa4c5c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52000] AnonymousClosure: (0xaa4b44), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xaa4c60: ldr             x1, [x1]
    // 0xaa4c64: r0 = AllocateClosure()
    //     0xaa4c64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa4c68: ldur            x1, [fp, #-0x10]
    // 0xaa4c6c: mov             x2, x0
    // 0xaa4c70: r0 = setState()
    //     0xaa4c70: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa4c74: r0 = Null
    //     0xaa4c74: mov             x0, NULL
    // 0xaa4c78: LeaveFrame
    //     0xaa4c78: mov             SP, fp
    //     0xaa4c7c: ldp             fp, lr, [SP], #0x10
    // 0xaa4c80: ret
    //     0xaa4c80: ret             
    // 0xaa4c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa4c84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa4c88: b               #0xaa4c20
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xaa4c8c, size: 0x744
    // 0xaa4c8c: EnterFrame
    //     0xaa4c8c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa4c90: mov             fp, SP
    // 0xaa4c94: AllocStack(0xa0)
    //     0xaa4c94: sub             SP, SP, #0xa0
    // 0xaa4c98: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xaa4c98: mov             x0, x1
    //     0xaa4c9c: stur            x1, [fp, #-8]
    //     0xaa4ca0: mov             x1, x2
    //     0xaa4ca4: stur            x2, [fp, #-0x10]
    //     0xaa4ca8: mov             x2, x5
    //     0xaa4cac: stur            x3, [fp, #-0x18]
    //     0xaa4cb0: stur            x5, [fp, #-0x20]
    // 0xaa4cb4: CheckStackOverflow
    //     0xaa4cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa4cb8: cmp             SP, x16
    //     0xaa4cbc: b.ls            #0xaa5348
    // 0xaa4cc0: r1 = 2
    //     0xaa4cc0: movz            x1, #0x2
    // 0xaa4cc4: r0 = AllocateContext()
    //     0xaa4cc4: bl              #0x16f6108  ; AllocateContextStub
    // 0xaa4cc8: mov             x4, x0
    // 0xaa4ccc: ldur            x3, [fp, #-8]
    // 0xaa4cd0: stur            x4, [fp, #-0x28]
    // 0xaa4cd4: StoreField: r4->field_f = r3
    //     0xaa4cd4: stur            w3, [x4, #0xf]
    // 0xaa4cd8: ldur            x2, [fp, #-0x20]
    // 0xaa4cdc: StoreField: r4->field_13 = r2
    //     0xaa4cdc: stur            w2, [x4, #0x13]
    // 0xaa4ce0: LoadField: r1 = r3->field_2f
    //     0xaa4ce0: ldur            w1, [x3, #0x2f]
    // 0xaa4ce4: DecompressPointer r1
    //     0xaa4ce4: add             x1, x1, HEAP, lsl #32
    // 0xaa4ce8: r0 = LoadClassIdInstr(r1)
    //     0xaa4ce8: ldur            x0, [x1, #-1]
    //     0xaa4cec: ubfx            x0, x0, #0xc, #0x14
    // 0xaa4cf0: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa4cf0: sub             lr, x0, #0xfe
    //     0xaa4cf4: ldr             lr, [x21, lr, lsl #3]
    //     0xaa4cf8: blr             lr
    // 0xaa4cfc: r1 = 60
    //     0xaa4cfc: movz            x1, #0x3c
    // 0xaa4d00: branchIfSmi(r0, 0xaa4d0c)
    //     0xaa4d00: tbz             w0, #0, #0xaa4d0c
    // 0xaa4d04: r1 = LoadClassIdInstr(r0)
    //     0xaa4d04: ldur            x1, [x0, #-1]
    //     0xaa4d08: ubfx            x1, x1, #0xc, #0x14
    // 0xaa4d0c: r16 = true
    //     0xaa4d0c: add             x16, NULL, #0x20  ; true
    // 0xaa4d10: stp             x16, x0, [SP]
    // 0xaa4d14: mov             x0, x1
    // 0xaa4d18: mov             lr, x0
    // 0xaa4d1c: ldr             lr, [x21, lr, lsl #3]
    // 0xaa4d20: blr             lr
    // 0xaa4d24: tbnz            w0, #4, #0xaa4d30
    // 0xaa4d28: d0 = 100.000000
    //     0xaa4d28: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xaa4d2c: b               #0xaa4d38
    // 0xaa4d30: d0 = 120.000000
    //     0xaa4d30: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xaa4d34: ldr             d0, [x17, #0xa38]
    // 0xaa4d38: ldur            x0, [fp, #-0x18]
    // 0xaa4d3c: stur            d0, [fp, #-0x58]
    // 0xaa4d40: r0 = BoxConstraints()
    //     0xaa4d40: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xaa4d44: stur            x0, [fp, #-0x20]
    // 0xaa4d48: StoreField: r0->field_7 = rZR
    //     0xaa4d48: stur            xzr, [x0, #7]
    // 0xaa4d4c: ldur            d0, [fp, #-0x58]
    // 0xaa4d50: StoreField: r0->field_f = d0
    //     0xaa4d50: stur            d0, [x0, #0xf]
    // 0xaa4d54: ArrayStore: r0[0] = rZR  ; List_8
    //     0xaa4d54: stur            xzr, [x0, #0x17]
    // 0xaa4d58: d0 = inf
    //     0xaa4d58: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xaa4d5c: StoreField: r0->field_1f = d0
    //     0xaa4d5c: stur            d0, [x0, #0x1f]
    // 0xaa4d60: ldur            x1, [fp, #-0x18]
    // 0xaa4d64: cmp             w1, NULL
    // 0xaa4d68: b.ne            #0xaa4d74
    // 0xaa4d6c: r2 = Null
    //     0xaa4d6c: mov             x2, NULL
    // 0xaa4d70: b               #0xaa4da0
    // 0xaa4d74: LoadField: d0 = r1->field_7
    //     0xaa4d74: ldur            d0, [x1, #7]
    // 0xaa4d78: r2 = inline_Allocate_Double()
    //     0xaa4d78: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaa4d7c: add             x2, x2, #0x10
    //     0xaa4d80: cmp             x3, x2
    //     0xaa4d84: b.ls            #0xaa5350
    //     0xaa4d88: str             x2, [THR, #0x50]  ; THR::top
    //     0xaa4d8c: sub             x2, x2, #0xf
    //     0xaa4d90: movz            x3, #0xe15c
    //     0xaa4d94: movk            x3, #0x3, lsl #16
    //     0xaa4d98: stur            x3, [x2, #-1]
    // 0xaa4d9c: StoreField: r2->field_7 = d0
    //     0xaa4d9c: stur            d0, [x2, #7]
    // 0xaa4da0: cmp             w2, NULL
    // 0xaa4da4: b.ne            #0xaa4db0
    // 0xaa4da8: d0 = 0.000000
    //     0xaa4da8: eor             v0.16b, v0.16b, v0.16b
    // 0xaa4dac: b               #0xaa4db4
    // 0xaa4db0: LoadField: d0 = r2->field_7
    //     0xaa4db0: ldur            d0, [x2, #7]
    // 0xaa4db4: stur            d0, [fp, #-0x70]
    // 0xaa4db8: cmp             w1, NULL
    // 0xaa4dbc: b.ne            #0xaa4dc8
    // 0xaa4dc0: r2 = Null
    //     0xaa4dc0: mov             x2, NULL
    // 0xaa4dc4: b               #0xaa4df4
    // 0xaa4dc8: LoadField: d1 = r1->field_f
    //     0xaa4dc8: ldur            d1, [x1, #0xf]
    // 0xaa4dcc: r2 = inline_Allocate_Double()
    //     0xaa4dcc: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaa4dd0: add             x2, x2, #0x10
    //     0xaa4dd4: cmp             x3, x2
    //     0xaa4dd8: b.ls            #0xaa536c
    //     0xaa4ddc: str             x2, [THR, #0x50]  ; THR::top
    //     0xaa4de0: sub             x2, x2, #0xf
    //     0xaa4de4: movz            x3, #0xe15c
    //     0xaa4de8: movk            x3, #0x3, lsl #16
    //     0xaa4dec: stur            x3, [x2, #-1]
    // 0xaa4df0: StoreField: r2->field_7 = d1
    //     0xaa4df0: stur            d1, [x2, #7]
    // 0xaa4df4: cmp             w2, NULL
    // 0xaa4df8: b.ne            #0xaa4e04
    // 0xaa4dfc: d2 = 0.000000
    //     0xaa4dfc: eor             v2.16b, v2.16b, v2.16b
    // 0xaa4e00: b               #0xaa4e0c
    // 0xaa4e04: LoadField: d1 = r2->field_7
    //     0xaa4e04: ldur            d1, [x2, #7]
    // 0xaa4e08: mov             v2.16b, v1.16b
    // 0xaa4e0c: d1 = 50.000000
    //     0xaa4e0c: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xaa4e10: fsub            d3, d2, d1
    // 0xaa4e14: stur            d3, [fp, #-0x68]
    // 0xaa4e18: cmp             w1, NULL
    // 0xaa4e1c: b.ne            #0xaa4e28
    // 0xaa4e20: r2 = Null
    //     0xaa4e20: mov             x2, NULL
    // 0xaa4e24: b               #0xaa4e54
    // 0xaa4e28: LoadField: d2 = r1->field_7
    //     0xaa4e28: ldur            d2, [x1, #7]
    // 0xaa4e2c: r2 = inline_Allocate_Double()
    //     0xaa4e2c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xaa4e30: add             x2, x2, #0x10
    //     0xaa4e34: cmp             x3, x2
    //     0xaa4e38: b.ls            #0xaa5388
    //     0xaa4e3c: str             x2, [THR, #0x50]  ; THR::top
    //     0xaa4e40: sub             x2, x2, #0xf
    //     0xaa4e44: movz            x3, #0xe15c
    //     0xaa4e48: movk            x3, #0x3, lsl #16
    //     0xaa4e4c: stur            x3, [x2, #-1]
    // 0xaa4e50: StoreField: r2->field_7 = d2
    //     0xaa4e50: stur            d2, [x2, #7]
    // 0xaa4e54: cmp             w2, NULL
    // 0xaa4e58: b.ne            #0xaa4e64
    // 0xaa4e5c: d2 = 0.000000
    //     0xaa4e5c: eor             v2.16b, v2.16b, v2.16b
    // 0xaa4e60: b               #0xaa4e68
    // 0xaa4e64: LoadField: d2 = r2->field_7
    //     0xaa4e64: ldur            d2, [x2, #7]
    // 0xaa4e68: fadd            d4, d2, d1
    // 0xaa4e6c: stur            d4, [fp, #-0x60]
    // 0xaa4e70: cmp             w1, NULL
    // 0xaa4e74: b.ne            #0xaa4e80
    // 0xaa4e78: r1 = Null
    //     0xaa4e78: mov             x1, NULL
    // 0xaa4e7c: b               #0xaa4eac
    // 0xaa4e80: LoadField: d1 = r1->field_f
    //     0xaa4e80: ldur            d1, [x1, #0xf]
    // 0xaa4e84: r1 = inline_Allocate_Double()
    //     0xaa4e84: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xaa4e88: add             x1, x1, #0x10
    //     0xaa4e8c: cmp             x2, x1
    //     0xaa4e90: b.ls            #0xaa53ac
    //     0xaa4e94: str             x1, [THR, #0x50]  ; THR::top
    //     0xaa4e98: sub             x1, x1, #0xf
    //     0xaa4e9c: movz            x2, #0xe15c
    //     0xaa4ea0: movk            x2, #0x3, lsl #16
    //     0xaa4ea4: stur            x2, [x1, #-1]
    // 0xaa4ea8: StoreField: r1->field_7 = d1
    //     0xaa4ea8: stur            d1, [x1, #7]
    // 0xaa4eac: cmp             w1, NULL
    // 0xaa4eb0: b.ne            #0xaa4ebc
    // 0xaa4eb4: d1 = 0.000000
    //     0xaa4eb4: eor             v1.16b, v1.16b, v1.16b
    // 0xaa4eb8: b               #0xaa4ec0
    // 0xaa4ebc: LoadField: d1 = r1->field_7
    //     0xaa4ebc: ldur            d1, [x1, #7]
    // 0xaa4ec0: ldur            x1, [fp, #-8]
    // 0xaa4ec4: ldur            x2, [fp, #-0x28]
    // 0xaa4ec8: stur            d1, [fp, #-0x58]
    // 0xaa4ecc: r0 = RelativeRect()
    //     0xaa4ecc: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xaa4ed0: mov             x3, x0
    // 0xaa4ed4: ldur            d0, [fp, #-0x70]
    // 0xaa4ed8: stur            x3, [fp, #-0x18]
    // 0xaa4edc: StoreField: r3->field_7 = d0
    //     0xaa4edc: stur            d0, [x3, #7]
    // 0xaa4ee0: ldur            d0, [fp, #-0x68]
    // 0xaa4ee4: StoreField: r3->field_f = d0
    //     0xaa4ee4: stur            d0, [x3, #0xf]
    // 0xaa4ee8: ldur            d0, [fp, #-0x60]
    // 0xaa4eec: ArrayStore: r3[0] = d0  ; List_8
    //     0xaa4eec: stur            d0, [x3, #0x17]
    // 0xaa4ef0: ldur            d0, [fp, #-0x58]
    // 0xaa4ef4: StoreField: r3->field_1f = d0
    //     0xaa4ef4: stur            d0, [x3, #0x1f]
    // 0xaa4ef8: ldur            x4, [fp, #-8]
    // 0xaa4efc: LoadField: r1 = r4->field_2f
    //     0xaa4efc: ldur            w1, [x4, #0x2f]
    // 0xaa4f00: DecompressPointer r1
    //     0xaa4f00: add             x1, x1, HEAP, lsl #32
    // 0xaa4f04: ldur            x5, [fp, #-0x28]
    // 0xaa4f08: LoadField: r2 = r5->field_13
    //     0xaa4f08: ldur            w2, [x5, #0x13]
    // 0xaa4f0c: DecompressPointer r2
    //     0xaa4f0c: add             x2, x2, HEAP, lsl #32
    // 0xaa4f10: r0 = LoadClassIdInstr(r1)
    //     0xaa4f10: ldur            x0, [x1, #-1]
    //     0xaa4f14: ubfx            x0, x0, #0xc, #0x14
    // 0xaa4f18: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa4f18: sub             lr, x0, #0xfe
    //     0xaa4f1c: ldr             lr, [x21, lr, lsl #3]
    //     0xaa4f20: blr             lr
    // 0xaa4f24: r1 = 60
    //     0xaa4f24: movz            x1, #0x3c
    // 0xaa4f28: branchIfSmi(r0, 0xaa4f34)
    //     0xaa4f28: tbz             w0, #0, #0xaa4f34
    // 0xaa4f2c: r1 = LoadClassIdInstr(r0)
    //     0xaa4f2c: ldur            x1, [x0, #-1]
    //     0xaa4f30: ubfx            x1, x1, #0xc, #0x14
    // 0xaa4f34: r16 = true
    //     0xaa4f34: add             x16, NULL, #0x20  ; true
    // 0xaa4f38: stp             x16, x0, [SP]
    // 0xaa4f3c: mov             x0, x1
    // 0xaa4f40: mov             lr, x0
    // 0xaa4f44: ldr             lr, [x21, lr, lsl #3]
    // 0xaa4f48: blr             lr
    // 0xaa4f4c: tbnz            w0, #4, #0xaa4f58
    // 0xaa4f50: r4 = Null
    //     0xaa4f50: mov             x4, NULL
    // 0xaa4f54: b               #0xaa4f6c
    // 0xaa4f58: ldur            x2, [fp, #-0x28]
    // 0xaa4f5c: r1 = Function '<anonymous closure>':.
    //     0xaa4f5c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ff0] AnonymousClosure: (0xaa53d0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xaa4f60: ldr             x1, [x1, #0xff0]
    // 0xaa4f64: r0 = AllocateClosure()
    //     0xaa4f64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa4f68: mov             x4, x0
    // 0xaa4f6c: ldur            x0, [fp, #-8]
    // 0xaa4f70: ldur            x3, [fp, #-0x28]
    // 0xaa4f74: stur            x4, [fp, #-0x30]
    // 0xaa4f78: r1 = <Widget>
    //     0xaa4f78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaa4f7c: r2 = 0
    //     0xaa4f7c: movz            x2, #0
    // 0xaa4f80: r0 = _GrowableList()
    //     0xaa4f80: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaa4f84: mov             x4, x0
    // 0xaa4f88: ldur            x3, [fp, #-8]
    // 0xaa4f8c: stur            x4, [fp, #-0x38]
    // 0xaa4f90: LoadField: r1 = r3->field_2f
    //     0xaa4f90: ldur            w1, [x3, #0x2f]
    // 0xaa4f94: DecompressPointer r1
    //     0xaa4f94: add             x1, x1, HEAP, lsl #32
    // 0xaa4f98: ldur            x5, [fp, #-0x28]
    // 0xaa4f9c: LoadField: r2 = r5->field_13
    //     0xaa4f9c: ldur            w2, [x5, #0x13]
    // 0xaa4fa0: DecompressPointer r2
    //     0xaa4fa0: add             x2, x2, HEAP, lsl #32
    // 0xaa4fa4: r0 = LoadClassIdInstr(r1)
    //     0xaa4fa4: ldur            x0, [x1, #-1]
    //     0xaa4fa8: ubfx            x0, x0, #0xc, #0x14
    // 0xaa4fac: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa4fac: sub             lr, x0, #0xfe
    //     0xaa4fb0: ldr             lr, [x21, lr, lsl #3]
    //     0xaa4fb4: blr             lr
    // 0xaa4fb8: r1 = 60
    //     0xaa4fb8: movz            x1, #0x3c
    // 0xaa4fbc: branchIfSmi(r0, 0xaa4fc8)
    //     0xaa4fbc: tbz             w0, #0, #0xaa4fc8
    // 0xaa4fc0: r1 = LoadClassIdInstr(r0)
    //     0xaa4fc0: ldur            x1, [x0, #-1]
    //     0xaa4fc4: ubfx            x1, x1, #0xc, #0x14
    // 0xaa4fc8: r16 = true
    //     0xaa4fc8: add             x16, NULL, #0x20  ; true
    // 0xaa4fcc: stp             x16, x0, [SP]
    // 0xaa4fd0: mov             x0, x1
    // 0xaa4fd4: mov             lr, x0
    // 0xaa4fd8: ldr             lr, [x21, lr, lsl #3]
    // 0xaa4fdc: blr             lr
    // 0xaa4fe0: tbnz            w0, #4, #0xaa5044
    // 0xaa4fe4: ldur            x0, [fp, #-0x38]
    // 0xaa4fe8: LoadField: r1 = r0->field_b
    //     0xaa4fe8: ldur            w1, [x0, #0xb]
    // 0xaa4fec: LoadField: r2 = r0->field_f
    //     0xaa4fec: ldur            w2, [x0, #0xf]
    // 0xaa4ff0: DecompressPointer r2
    //     0xaa4ff0: add             x2, x2, HEAP, lsl #32
    // 0xaa4ff4: LoadField: r3 = r2->field_b
    //     0xaa4ff4: ldur            w3, [x2, #0xb]
    // 0xaa4ff8: r2 = LoadInt32Instr(r1)
    //     0xaa4ff8: sbfx            x2, x1, #1, #0x1f
    // 0xaa4ffc: stur            x2, [fp, #-0x40]
    // 0xaa5000: r1 = LoadInt32Instr(r3)
    //     0xaa5000: sbfx            x1, x3, #1, #0x1f
    // 0xaa5004: cmp             x2, x1
    // 0xaa5008: b.ne            #0xaa5014
    // 0xaa500c: mov             x1, x0
    // 0xaa5010: r0 = _growToNextCapacity()
    //     0xaa5010: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa5014: ldur            x0, [fp, #-0x38]
    // 0xaa5018: ldur            x1, [fp, #-0x40]
    // 0xaa501c: add             x2, x1, #1
    // 0xaa5020: lsl             x3, x2, #1
    // 0xaa5024: StoreField: r0->field_b = r3
    //     0xaa5024: stur            w3, [x0, #0xb]
    // 0xaa5028: LoadField: r2 = r0->field_f
    //     0xaa5028: ldur            w2, [x0, #0xf]
    // 0xaa502c: DecompressPointer r2
    //     0xaa502c: add             x2, x2, HEAP, lsl #32
    // 0xaa5030: add             x3, x2, x1, lsl #2
    // 0xaa5034: r16 = Instance_Icon
    //     0xaa5034: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xaa5038: ldr             x16, [x16, #0xa48]
    // 0xaa503c: StoreField: r3->field_f = r16
    //     0xaa503c: stur            w16, [x3, #0xf]
    // 0xaa5040: b               #0xaa5048
    // 0xaa5044: ldur            x0, [fp, #-0x38]
    // 0xaa5048: LoadField: r1 = r0->field_b
    //     0xaa5048: ldur            w1, [x0, #0xb]
    // 0xaa504c: LoadField: r2 = r0->field_f
    //     0xaa504c: ldur            w2, [x0, #0xf]
    // 0xaa5050: DecompressPointer r2
    //     0xaa5050: add             x2, x2, HEAP, lsl #32
    // 0xaa5054: LoadField: r3 = r2->field_b
    //     0xaa5054: ldur            w3, [x2, #0xb]
    // 0xaa5058: r2 = LoadInt32Instr(r1)
    //     0xaa5058: sbfx            x2, x1, #1, #0x1f
    // 0xaa505c: stur            x2, [fp, #-0x40]
    // 0xaa5060: r1 = LoadInt32Instr(r3)
    //     0xaa5060: sbfx            x1, x3, #1, #0x1f
    // 0xaa5064: cmp             x2, x1
    // 0xaa5068: b.ne            #0xaa5074
    // 0xaa506c: mov             x1, x0
    // 0xaa5070: r0 = _growToNextCapacity()
    //     0xaa5070: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa5074: ldur            x1, [fp, #-8]
    // 0xaa5078: ldur            x4, [fp, #-0x28]
    // 0xaa507c: ldur            x3, [fp, #-0x38]
    // 0xaa5080: ldur            x0, [fp, #-0x40]
    // 0xaa5084: add             x2, x0, #1
    // 0xaa5088: lsl             x5, x2, #1
    // 0xaa508c: StoreField: r3->field_b = r5
    //     0xaa508c: stur            w5, [x3, #0xb]
    // 0xaa5090: LoadField: r2 = r3->field_f
    //     0xaa5090: ldur            w2, [x3, #0xf]
    // 0xaa5094: DecompressPointer r2
    //     0xaa5094: add             x2, x2, HEAP, lsl #32
    // 0xaa5098: add             x5, x2, x0, lsl #2
    // 0xaa509c: r16 = Instance_SizedBox
    //     0xaa509c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xaa50a0: ldr             x16, [x16, #0xa50]
    // 0xaa50a4: StoreField: r5->field_f = r16
    //     0xaa50a4: stur            w16, [x5, #0xf]
    // 0xaa50a8: LoadField: r0 = r1->field_2f
    //     0xaa50a8: ldur            w0, [x1, #0x2f]
    // 0xaa50ac: DecompressPointer r0
    //     0xaa50ac: add             x0, x0, HEAP, lsl #32
    // 0xaa50b0: LoadField: r2 = r4->field_13
    //     0xaa50b0: ldur            w2, [x4, #0x13]
    // 0xaa50b4: DecompressPointer r2
    //     0xaa50b4: add             x2, x2, HEAP, lsl #32
    // 0xaa50b8: r1 = LoadClassIdInstr(r0)
    //     0xaa50b8: ldur            x1, [x0, #-1]
    //     0xaa50bc: ubfx            x1, x1, #0xc, #0x14
    // 0xaa50c0: mov             x16, x0
    // 0xaa50c4: mov             x0, x1
    // 0xaa50c8: mov             x1, x16
    // 0xaa50cc: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa50cc: sub             lr, x0, #0xfe
    //     0xaa50d0: ldr             lr, [x21, lr, lsl #3]
    //     0xaa50d4: blr             lr
    // 0xaa50d8: r1 = 60
    //     0xaa50d8: movz            x1, #0x3c
    // 0xaa50dc: branchIfSmi(r0, 0xaa50e8)
    //     0xaa50dc: tbz             w0, #0, #0xaa50e8
    // 0xaa50e0: r1 = LoadClassIdInstr(r0)
    //     0xaa50e0: ldur            x1, [x0, #-1]
    //     0xaa50e4: ubfx            x1, x1, #0xc, #0x14
    // 0xaa50e8: r16 = true
    //     0xaa50e8: add             x16, NULL, #0x20  ; true
    // 0xaa50ec: stp             x16, x0, [SP]
    // 0xaa50f0: mov             x0, x1
    // 0xaa50f4: mov             lr, x0
    // 0xaa50f8: ldr             lr, [x21, lr, lsl #3]
    // 0xaa50fc: blr             lr
    // 0xaa5100: tbnz            w0, #4, #0xaa5110
    // 0xaa5104: r0 = "Flagged"
    //     0xaa5104: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xaa5108: ldr             x0, [x0, #0xa58]
    // 0xaa510c: b               #0xaa5118
    // 0xaa5110: r0 = "Flag as abusive"
    //     0xaa5110: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xaa5114: ldr             x0, [x0, #0xa60]
    // 0xaa5118: ldur            x1, [fp, #-0x10]
    // 0xaa511c: stur            x0, [fp, #-8]
    // 0xaa5120: r0 = of()
    //     0xaa5120: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa5124: LoadField: r1 = r0->field_87
    //     0xaa5124: ldur            w1, [x0, #0x87]
    // 0xaa5128: DecompressPointer r1
    //     0xaa5128: add             x1, x1, HEAP, lsl #32
    // 0xaa512c: LoadField: r0 = r1->field_33
    //     0xaa512c: ldur            w0, [x1, #0x33]
    // 0xaa5130: DecompressPointer r0
    //     0xaa5130: add             x0, x0, HEAP, lsl #32
    // 0xaa5134: cmp             w0, NULL
    // 0xaa5138: b.ne            #0xaa5144
    // 0xaa513c: r2 = Null
    //     0xaa513c: mov             x2, NULL
    // 0xaa5140: b               #0xaa5168
    // 0xaa5144: r16 = 12.000000
    //     0xaa5144: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaa5148: ldr             x16, [x16, #0x9e8]
    // 0xaa514c: r30 = Instance_Color
    //     0xaa514c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaa5150: stp             lr, x16, [SP]
    // 0xaa5154: mov             x1, x0
    // 0xaa5158: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaa5158: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaa515c: ldr             x4, [x4, #0xaa0]
    // 0xaa5160: r0 = copyWith()
    //     0xaa5160: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaa5164: mov             x2, x0
    // 0xaa5168: ldur            x1, [fp, #-0x38]
    // 0xaa516c: ldur            x0, [fp, #-8]
    // 0xaa5170: stur            x2, [fp, #-0x48]
    // 0xaa5174: r0 = Text()
    //     0xaa5174: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaa5178: mov             x2, x0
    // 0xaa517c: ldur            x0, [fp, #-8]
    // 0xaa5180: stur            x2, [fp, #-0x50]
    // 0xaa5184: StoreField: r2->field_b = r0
    //     0xaa5184: stur            w0, [x2, #0xb]
    // 0xaa5188: ldur            x0, [fp, #-0x48]
    // 0xaa518c: StoreField: r2->field_13 = r0
    //     0xaa518c: stur            w0, [x2, #0x13]
    // 0xaa5190: ldur            x0, [fp, #-0x38]
    // 0xaa5194: LoadField: r1 = r0->field_b
    //     0xaa5194: ldur            w1, [x0, #0xb]
    // 0xaa5198: LoadField: r3 = r0->field_f
    //     0xaa5198: ldur            w3, [x0, #0xf]
    // 0xaa519c: DecompressPointer r3
    //     0xaa519c: add             x3, x3, HEAP, lsl #32
    // 0xaa51a0: LoadField: r4 = r3->field_b
    //     0xaa51a0: ldur            w4, [x3, #0xb]
    // 0xaa51a4: r3 = LoadInt32Instr(r1)
    //     0xaa51a4: sbfx            x3, x1, #1, #0x1f
    // 0xaa51a8: stur            x3, [fp, #-0x40]
    // 0xaa51ac: r1 = LoadInt32Instr(r4)
    //     0xaa51ac: sbfx            x1, x4, #1, #0x1f
    // 0xaa51b0: cmp             x3, x1
    // 0xaa51b4: b.ne            #0xaa51c0
    // 0xaa51b8: mov             x1, x0
    // 0xaa51bc: r0 = _growToNextCapacity()
    //     0xaa51bc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa51c0: ldur            x4, [fp, #-0x30]
    // 0xaa51c4: ldur            x2, [fp, #-0x38]
    // 0xaa51c8: ldur            x3, [fp, #-0x40]
    // 0xaa51cc: add             x0, x3, #1
    // 0xaa51d0: lsl             x1, x0, #1
    // 0xaa51d4: StoreField: r2->field_b = r1
    //     0xaa51d4: stur            w1, [x2, #0xb]
    // 0xaa51d8: LoadField: r1 = r2->field_f
    //     0xaa51d8: ldur            w1, [x2, #0xf]
    // 0xaa51dc: DecompressPointer r1
    //     0xaa51dc: add             x1, x1, HEAP, lsl #32
    // 0xaa51e0: ldur            x0, [fp, #-0x50]
    // 0xaa51e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaa51e4: add             x25, x1, x3, lsl #2
    //     0xaa51e8: add             x25, x25, #0xf
    //     0xaa51ec: str             w0, [x25]
    //     0xaa51f0: tbz             w0, #0, #0xaa520c
    //     0xaa51f4: ldurb           w16, [x1, #-1]
    //     0xaa51f8: ldurb           w17, [x0, #-1]
    //     0xaa51fc: and             x16, x17, x16, lsr #2
    //     0xaa5200: tst             x16, HEAP, lsr #32
    //     0xaa5204: b.eq            #0xaa520c
    //     0xaa5208: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaa520c: r0 = Row()
    //     0xaa520c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaa5210: mov             x2, x0
    // 0xaa5214: r0 = Instance_Axis
    //     0xaa5214: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaa5218: stur            x2, [fp, #-8]
    // 0xaa521c: StoreField: r2->field_f = r0
    //     0xaa521c: stur            w0, [x2, #0xf]
    // 0xaa5220: r0 = Instance_MainAxisAlignment
    //     0xaa5220: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaa5224: ldr             x0, [x0, #0xa08]
    // 0xaa5228: StoreField: r2->field_13 = r0
    //     0xaa5228: stur            w0, [x2, #0x13]
    // 0xaa522c: r0 = Instance_MainAxisSize
    //     0xaa522c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaa5230: ldr             x0, [x0, #0xa10]
    // 0xaa5234: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa5234: stur            w0, [x2, #0x17]
    // 0xaa5238: r0 = Instance_CrossAxisAlignment
    //     0xaa5238: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaa523c: ldr             x0, [x0, #0xa18]
    // 0xaa5240: StoreField: r2->field_1b = r0
    //     0xaa5240: stur            w0, [x2, #0x1b]
    // 0xaa5244: r0 = Instance_VerticalDirection
    //     0xaa5244: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaa5248: ldr             x0, [x0, #0xa20]
    // 0xaa524c: StoreField: r2->field_23 = r0
    //     0xaa524c: stur            w0, [x2, #0x23]
    // 0xaa5250: r0 = Instance_Clip
    //     0xaa5250: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaa5254: ldr             x0, [x0, #0x38]
    // 0xaa5258: StoreField: r2->field_2b = r0
    //     0xaa5258: stur            w0, [x2, #0x2b]
    // 0xaa525c: StoreField: r2->field_2f = rZR
    //     0xaa525c: stur            xzr, [x2, #0x2f]
    // 0xaa5260: ldur            x0, [fp, #-0x38]
    // 0xaa5264: StoreField: r2->field_b = r0
    //     0xaa5264: stur            w0, [x2, #0xb]
    // 0xaa5268: r1 = <String>
    //     0xaa5268: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xaa526c: r0 = PopupMenuItem()
    //     0xaa526c: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xaa5270: mov             x3, x0
    // 0xaa5274: r0 = "flag"
    //     0xaa5274: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xaa5278: ldr             x0, [x0, #0xa68]
    // 0xaa527c: stur            x3, [fp, #-0x38]
    // 0xaa5280: StoreField: r3->field_f = r0
    //     0xaa5280: stur            w0, [x3, #0xf]
    // 0xaa5284: ldur            x0, [fp, #-0x30]
    // 0xaa5288: StoreField: r3->field_13 = r0
    //     0xaa5288: stur            w0, [x3, #0x13]
    // 0xaa528c: r0 = true
    //     0xaa528c: add             x0, NULL, #0x20  ; true
    // 0xaa5290: ArrayStore: r3[0] = r0  ; List_4
    //     0xaa5290: stur            w0, [x3, #0x17]
    // 0xaa5294: d0 = 25.000000
    //     0xaa5294: fmov            d0, #25.00000000
    // 0xaa5298: StoreField: r3->field_1b = d0
    //     0xaa5298: stur            d0, [x3, #0x1b]
    // 0xaa529c: ldur            x0, [fp, #-8]
    // 0xaa52a0: StoreField: r3->field_33 = r0
    //     0xaa52a0: stur            w0, [x3, #0x33]
    // 0xaa52a4: r1 = Null
    //     0xaa52a4: mov             x1, NULL
    // 0xaa52a8: r2 = 2
    //     0xaa52a8: movz            x2, #0x2
    // 0xaa52ac: r0 = AllocateArray()
    //     0xaa52ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaa52b0: mov             x2, x0
    // 0xaa52b4: ldur            x0, [fp, #-0x38]
    // 0xaa52b8: stur            x2, [fp, #-8]
    // 0xaa52bc: StoreField: r2->field_f = r0
    //     0xaa52bc: stur            w0, [x2, #0xf]
    // 0xaa52c0: r1 = <PopupMenuEntry<String>>
    //     0xaa52c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xaa52c4: ldr             x1, [x1, #0xa70]
    // 0xaa52c8: r0 = AllocateGrowableArray()
    //     0xaa52c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaa52cc: mov             x1, x0
    // 0xaa52d0: ldur            x0, [fp, #-8]
    // 0xaa52d4: StoreField: r1->field_f = r0
    //     0xaa52d4: stur            w0, [x1, #0xf]
    // 0xaa52d8: r0 = 2
    //     0xaa52d8: movz            x0, #0x2
    // 0xaa52dc: StoreField: r1->field_b = r0
    //     0xaa52dc: stur            w0, [x1, #0xb]
    // 0xaa52e0: r16 = <String>
    //     0xaa52e0: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xaa52e4: ldur            lr, [fp, #-0x10]
    // 0xaa52e8: stp             lr, x16, [SP, #0x20]
    // 0xaa52ec: ldur            x16, [fp, #-0x18]
    // 0xaa52f0: stp             x16, x1, [SP, #0x10]
    // 0xaa52f4: r16 = Instance_Color
    //     0xaa52f4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaa52f8: ldur            lr, [fp, #-0x20]
    // 0xaa52fc: stp             lr, x16, [SP]
    // 0xaa5300: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xaa5300: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xaa5304: ldr             x4, [x4, #0xa78]
    // 0xaa5308: r0 = showMenu()
    //     0xaa5308: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xaa530c: ldur            x2, [fp, #-0x28]
    // 0xaa5310: r1 = Function '<anonymous closure>':.
    //     0xaa5310: add             x1, PP, #0x51, lsl #12  ; [pp+0x51ff8] AnonymousClosure: (0xaa4bf8), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xaa5314: ldr             x1, [x1, #0xff8]
    // 0xaa5318: stur            x0, [fp, #-8]
    // 0xaa531c: r0 = AllocateClosure()
    //     0xaa531c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa5320: r16 = <Null?>
    //     0xaa5320: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xaa5324: ldur            lr, [fp, #-8]
    // 0xaa5328: stp             lr, x16, [SP, #8]
    // 0xaa532c: str             x0, [SP]
    // 0xaa5330: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaa5330: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaa5334: r0 = then()
    //     0xaa5334: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xaa5338: r0 = Null
    //     0xaa5338: mov             x0, NULL
    // 0xaa533c: LeaveFrame
    //     0xaa533c: mov             SP, fp
    //     0xaa5340: ldp             fp, lr, [SP], #0x10
    // 0xaa5344: ret
    //     0xaa5344: ret             
    // 0xaa5348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5348: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa534c: b               #0xaa4cc0
    // 0xaa5350: SaveReg d0
    //     0xaa5350: str             q0, [SP, #-0x10]!
    // 0xaa5354: stp             x0, x1, [SP, #-0x10]!
    // 0xaa5358: r0 = AllocateDouble()
    //     0xaa5358: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa535c: mov             x2, x0
    // 0xaa5360: ldp             x0, x1, [SP], #0x10
    // 0xaa5364: RestoreReg d0
    //     0xaa5364: ldr             q0, [SP], #0x10
    // 0xaa5368: b               #0xaa4d9c
    // 0xaa536c: stp             q0, q1, [SP, #-0x20]!
    // 0xaa5370: stp             x0, x1, [SP, #-0x10]!
    // 0xaa5374: r0 = AllocateDouble()
    //     0xaa5374: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa5378: mov             x2, x0
    // 0xaa537c: ldp             x0, x1, [SP], #0x10
    // 0xaa5380: ldp             q0, q1, [SP], #0x20
    // 0xaa5384: b               #0xaa4df0
    // 0xaa5388: stp             q2, q3, [SP, #-0x20]!
    // 0xaa538c: stp             q0, q1, [SP, #-0x20]!
    // 0xaa5390: stp             x0, x1, [SP, #-0x10]!
    // 0xaa5394: r0 = AllocateDouble()
    //     0xaa5394: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa5398: mov             x2, x0
    // 0xaa539c: ldp             x0, x1, [SP], #0x10
    // 0xaa53a0: ldp             q0, q1, [SP], #0x20
    // 0xaa53a4: ldp             q2, q3, [SP], #0x20
    // 0xaa53a8: b               #0xaa4e50
    // 0xaa53ac: stp             q3, q4, [SP, #-0x20]!
    // 0xaa53b0: stp             q0, q1, [SP, #-0x20]!
    // 0xaa53b4: SaveReg r0
    //     0xaa53b4: str             x0, [SP, #-8]!
    // 0xaa53b8: r0 = AllocateDouble()
    //     0xaa53b8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa53bc: mov             x1, x0
    // 0xaa53c0: RestoreReg r0
    //     0xaa53c0: ldr             x0, [SP], #8
    // 0xaa53c4: ldp             q0, q1, [SP], #0x20
    // 0xaa53c8: ldp             q3, q4, [SP], #0x20
    // 0xaa53cc: b               #0xaa4ea8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa53d0, size: 0x60
    // 0xaa53d0: EnterFrame
    //     0xaa53d0: stp             fp, lr, [SP, #-0x10]!
    //     0xaa53d4: mov             fp, SP
    // 0xaa53d8: AllocStack(0x8)
    //     0xaa53d8: sub             SP, SP, #8
    // 0xaa53dc: SetupParameters()
    //     0xaa53dc: ldr             x0, [fp, #0x10]
    //     0xaa53e0: ldur            w2, [x0, #0x17]
    //     0xaa53e4: add             x2, x2, HEAP, lsl #32
    // 0xaa53e8: CheckStackOverflow
    //     0xaa53e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa53ec: cmp             SP, x16
    //     0xaa53f0: b.ls            #0xaa5428
    // 0xaa53f4: LoadField: r0 = r2->field_f
    //     0xaa53f4: ldur            w0, [x2, #0xf]
    // 0xaa53f8: DecompressPointer r0
    //     0xaa53f8: add             x0, x0, HEAP, lsl #32
    // 0xaa53fc: stur            x0, [fp, #-8]
    // 0xaa5400: r1 = Function '<anonymous closure>':.
    //     0xaa5400: add             x1, PP, #0x52, lsl #12  ; [pp+0x52008] AnonymousClosure: (0xaa4b44), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xaa5404: ldr             x1, [x1, #8]
    // 0xaa5408: r0 = AllocateClosure()
    //     0xaa5408: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa540c: ldur            x1, [fp, #-8]
    // 0xaa5410: mov             x2, x0
    // 0xaa5414: r0 = setState()
    //     0xaa5414: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa5418: r0 = Null
    //     0xaa5418: mov             x0, NULL
    // 0xaa541c: LeaveFrame
    //     0xaa541c: mov             SP, fp
    //     0xaa5420: ldp             fp, lr, [SP], #0x10
    // 0xaa5424: ret
    //     0xaa5424: ret             
    // 0xaa5428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa5428: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa542c: b               #0xaa53f4
  }
  [closure] void <anonymous closure>(dynamic, TapDownDetails) {
    // ** addr: 0xaa5490, size: 0x4c
    // 0xaa5490: ldr             x1, [SP, #8]
    // 0xaa5494: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaa5494: ldur            w2, [x1, #0x17]
    // 0xaa5498: DecompressPointer r2
    //     0xaa5498: add             x2, x2, HEAP, lsl #32
    // 0xaa549c: LoadField: r1 = r2->field_f
    //     0xaa549c: ldur            w1, [x2, #0xf]
    // 0xaa54a0: DecompressPointer r1
    //     0xaa54a0: add             x1, x1, HEAP, lsl #32
    // 0xaa54a4: ldr             x2, [SP]
    // 0xaa54a8: LoadField: r0 = r2->field_7
    //     0xaa54a8: ldur            w0, [x2, #7]
    // 0xaa54ac: DecompressPointer r0
    //     0xaa54ac: add             x0, x0, HEAP, lsl #32
    // 0xaa54b0: StoreField: r1->field_33 = r0
    //     0xaa54b0: stur            w0, [x1, #0x33]
    //     0xaa54b4: ldurb           w16, [x1, #-1]
    //     0xaa54b8: ldurb           w17, [x0, #-1]
    //     0xaa54bc: and             x16, x17, x16, lsr #2
    //     0xaa54c0: tst             x16, HEAP, lsr #32
    //     0xaa54c4: b.eq            #0xaa54d4
    //     0xaa54c8: str             lr, [SP, #-8]!
    //     0xaa54cc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    //     0xaa54d0: ldr             lr, [SP], #8
    // 0xaa54d4: r0 = Null
    //     0xaa54d4: mov             x0, NULL
    // 0xaa54d8: ret
    //     0xaa54d8: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa54dc, size: 0xe8
    // 0xaa54dc: EnterFrame
    //     0xaa54dc: stp             fp, lr, [SP, #-0x10]!
    //     0xaa54e0: mov             fp, SP
    // 0xaa54e4: AllocStack(0x10)
    //     0xaa54e4: sub             SP, SP, #0x10
    // 0xaa54e8: SetupParameters()
    //     0xaa54e8: ldr             x0, [fp, #0x10]
    //     0xaa54ec: ldur            w1, [x0, #0x17]
    //     0xaa54f0: add             x1, x1, HEAP, lsl #32
    // 0xaa54f4: CheckStackOverflow
    //     0xaa54f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa54f8: cmp             SP, x16
    //     0xaa54fc: b.ls            #0xaa55b8
    // 0xaa5500: LoadField: r0 = r1->field_f
    //     0xaa5500: ldur            w0, [x1, #0xf]
    // 0xaa5504: DecompressPointer r0
    //     0xaa5504: add             x0, x0, HEAP, lsl #32
    // 0xaa5508: LoadField: r1 = r0->field_b
    //     0xaa5508: ldur            w1, [x0, #0xb]
    // 0xaa550c: DecompressPointer r1
    //     0xaa550c: add             x1, x1, HEAP, lsl #32
    // 0xaa5510: cmp             w1, NULL
    // 0xaa5514: b.eq            #0xaa55c0
    // 0xaa5518: LoadField: r0 = r1->field_b
    //     0xaa5518: ldur            w0, [x1, #0xb]
    // 0xaa551c: DecompressPointer r0
    //     0xaa551c: add             x0, x0, HEAP, lsl #32
    // 0xaa5520: r1 = LoadClassIdInstr(r0)
    //     0xaa5520: ldur            x1, [x0, #-1]
    //     0xaa5524: ubfx            x1, x1, #0xc, #0x14
    // 0xaa5528: r16 = "direct_image"
    //     0xaa5528: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xaa552c: ldr             x16, [x16, #0xc98]
    // 0xaa5530: stp             x16, x0, [SP]
    // 0xaa5534: mov             x0, x1
    // 0xaa5538: mov             lr, x0
    // 0xaa553c: ldr             lr, [x21, lr, lsl #3]
    // 0xaa5540: blr             lr
    // 0xaa5544: tbnz            w0, #4, #0xaa5574
    // 0xaa5548: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaa5548: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaa554c: ldr             x0, [x0, #0x1c80]
    //     0xaa5550: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaa5554: cmp             w0, w16
    //     0xaa5558: b.ne            #0xaa5564
    //     0xaa555c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaa5560: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaa5564: str             NULL, [SP]
    // 0xaa5568: r4 = const [0x1, 0, 0, 0, null]
    //     0xaa5568: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaa556c: r0 = GetNavigation.back()
    //     0xaa556c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xaa5570: b               #0xaa55a8
    // 0xaa5574: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaa5574: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaa5578: ldr             x0, [x0, #0x1c80]
    //     0xaa557c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaa5580: cmp             w0, w16
    //     0xaa5584: b.ne            #0xaa5590
    //     0xaa5588: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaa558c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaa5590: str             NULL, [SP]
    // 0xaa5594: r4 = const [0x1, 0, 0, 0, null]
    //     0xaa5594: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaa5598: r0 = GetNavigation.back()
    //     0xaa5598: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xaa559c: str             NULL, [SP]
    // 0xaa55a0: r4 = const [0x1, 0, 0, 0, null]
    //     0xaa55a0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xaa55a4: r0 = GetNavigation.back()
    //     0xaa55a4: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xaa55a8: r0 = Null
    //     0xaa55a8: mov             x0, NULL
    // 0xaa55ac: LeaveFrame
    //     0xaa55ac: mov             SP, fp
    //     0xaa55b0: ldp             fp, lr, [SP], #0x10
    // 0xaa55b4: ret
    //     0xaa55b4: ret             
    // 0xaa55b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa55b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa55bc: b               #0xaa5500
    // 0xaa55c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaa55c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, int) {
    // ** addr: 0xaa55c4, size: 0x1d4
    // 0xaa55c4: EnterFrame
    //     0xaa55c4: stp             fp, lr, [SP, #-0x10]!
    //     0xaa55c8: mov             fp, SP
    // 0xaa55cc: AllocStack(0x40)
    //     0xaa55cc: sub             SP, SP, #0x40
    // 0xaa55d0: SetupParameters()
    //     0xaa55d0: ldr             x0, [fp, #0x18]
    //     0xaa55d4: ldur            w2, [x0, #0x17]
    //     0xaa55d8: add             x2, x2, HEAP, lsl #32
    //     0xaa55dc: stur            x2, [fp, #-8]
    // 0xaa55e0: CheckStackOverflow
    //     0xaa55e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa55e4: cmp             SP, x16
    //     0xaa55e8: b.ls            #0xaa576c
    // 0xaa55ec: LoadField: r1 = r2->field_13
    //     0xaa55ec: ldur            w1, [x2, #0x13]
    // 0xaa55f0: DecompressPointer r1
    //     0xaa55f0: add             x1, x1, HEAP, lsl #32
    // 0xaa55f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa55f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa55f8: r0 = _of()
    //     0xaa55f8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaa55fc: LoadField: r1 = r0->field_7
    //     0xaa55fc: ldur            w1, [x0, #7]
    // 0xaa5600: DecompressPointer r1
    //     0xaa5600: add             x1, x1, HEAP, lsl #32
    // 0xaa5604: LoadField: d0 = r1->field_7
    //     0xaa5604: ldur            d0, [x1, #7]
    // 0xaa5608: d1 = 0.920000
    //     0xaa5608: add             x17, PP, #0x52, lsl #12  ; [pp+0x52010] IMM: double(0.92) from 0x3fed70a3d70a3d71
    //     0xaa560c: ldr             d1, [x17, #0x10]
    // 0xaa5610: fmul            d2, d0, d1
    // 0xaa5614: ldur            x0, [fp, #-8]
    // 0xaa5618: LoadField: r2 = r0->field_f
    //     0xaa5618: ldur            w2, [x0, #0xf]
    // 0xaa561c: DecompressPointer r2
    //     0xaa561c: add             x2, x2, HEAP, lsl #32
    // 0xaa5620: LoadField: r0 = r2->field_b
    //     0xaa5620: ldur            w0, [x2, #0xb]
    // 0xaa5624: DecompressPointer r0
    //     0xaa5624: add             x0, x0, HEAP, lsl #32
    // 0xaa5628: cmp             w0, NULL
    // 0xaa562c: b.eq            #0xaa5774
    // 0xaa5630: LoadField: r3 = r0->field_f
    //     0xaa5630: ldur            w3, [x0, #0xf]
    // 0xaa5634: DecompressPointer r3
    //     0xaa5634: add             x3, x3, HEAP, lsl #32
    // 0xaa5638: LoadField: r4 = r2->field_13
    //     0xaa5638: ldur            x4, [x2, #0x13]
    // 0xaa563c: LoadField: r0 = r3->field_b
    //     0xaa563c: ldur            w0, [x3, #0xb]
    // 0xaa5640: r1 = LoadInt32Instr(r0)
    //     0xaa5640: sbfx            x1, x0, #1, #0x1f
    // 0xaa5644: mov             x0, x1
    // 0xaa5648: mov             x1, x4
    // 0xaa564c: cmp             x1, x0
    // 0xaa5650: b.hs            #0xaa5778
    // 0xaa5654: LoadField: r0 = r3->field_f
    //     0xaa5654: ldur            w0, [x3, #0xf]
    // 0xaa5658: DecompressPointer r0
    //     0xaa5658: add             x0, x0, HEAP, lsl #32
    // 0xaa565c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xaa565c: add             x16, x0, x4, lsl #2
    //     0xaa5660: ldur            w1, [x16, #0xf]
    // 0xaa5664: DecompressPointer r1
    //     0xaa5664: add             x1, x1, HEAP, lsl #32
    // 0xaa5668: LoadField: r0 = r1->field_1b
    //     0xaa5668: ldur            w0, [x1, #0x1b]
    // 0xaa566c: DecompressPointer r0
    //     0xaa566c: add             x0, x0, HEAP, lsl #32
    // 0xaa5670: LoadField: r1 = r0->field_b
    //     0xaa5670: ldur            w1, [x0, #0xb]
    // 0xaa5674: r16 = LoadInt32Instr(r1)
    //     0xaa5674: sbfx            x16, x1, #1, #0x1f
    // 0xaa5678: scvtf           d0, w16
    // 0xaa567c: fdiv            d1, d2, d0
    // 0xaa5680: d0 = 2.000000
    //     0xaa5680: fmov            d0, #2.00000000
    // 0xaa5684: fsub            d2, d1, d0
    // 0xaa5688: stur            d2, [fp, #-0x20]
    // 0xaa568c: LoadField: r0 = r2->field_1b
    //     0xaa568c: ldur            x0, [x2, #0x1b]
    // 0xaa5690: ldr             x1, [fp, #0x10]
    // 0xaa5694: r2 = LoadInt32Instr(r1)
    //     0xaa5694: sbfx            x2, x1, #1, #0x1f
    //     0xaa5698: tbz             w1, #0, #0xaa56a0
    //     0xaa569c: ldur            x2, [x1, #7]
    // 0xaa56a0: cmp             x0, x2
    // 0xaa56a4: b.ne            #0xaa56b4
    // 0xaa56a8: mov             v0.16b, v2.16b
    // 0xaa56ac: r0 = Instance_Color
    //     0xaa56ac: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaa56b0: b               #0xaa56c4
    // 0xaa56b4: r1 = Instance_Color
    //     0xaa56b4: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaa56b8: d0 = 0.400000
    //     0xaa56b8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xaa56bc: r0 = withOpacity()
    //     0xaa56bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaa56c0: ldur            d0, [fp, #-0x20]
    // 0xaa56c4: stur            x0, [fp, #-0x10]
    // 0xaa56c8: r1 = inline_Allocate_Double()
    //     0xaa56c8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xaa56cc: add             x1, x1, #0x10
    //     0xaa56d0: cmp             x2, x1
    //     0xaa56d4: b.ls            #0xaa577c
    //     0xaa56d8: str             x1, [THR, #0x50]  ; THR::top
    //     0xaa56dc: sub             x1, x1, #0xf
    //     0xaa56e0: movz            x2, #0xe15c
    //     0xaa56e4: movk            x2, #0x3, lsl #16
    //     0xaa56e8: stur            x2, [x1, #-1]
    // 0xaa56ec: StoreField: r1->field_7 = d0
    //     0xaa56ec: stur            d0, [x1, #7]
    // 0xaa56f0: stur            x1, [fp, #-8]
    // 0xaa56f4: r0 = Container()
    //     0xaa56f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaa56f8: stur            x0, [fp, #-0x18]
    // 0xaa56fc: r16 = 2.000000
    //     0xaa56fc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xaa5700: ldr             x16, [x16, #0xdf8]
    // 0xaa5704: ldur            lr, [fp, #-8]
    // 0xaa5708: stp             lr, x16, [SP, #0x10]
    // 0xaa570c: ldur            x16, [fp, #-0x10]
    // 0xaa5710: r30 = Instance_EdgeInsets
    //     0xaa5710: add             lr, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xaa5714: ldr             lr, [lr, #0x18]
    // 0xaa5718: stp             lr, x16, [SP]
    // 0xaa571c: mov             x1, x0
    // 0xaa5720: r4 = const [0, 0x5, 0x4, 0x1, color, 0x3, height, 0x1, margin, 0x4, width, 0x2, null]
    //     0xaa5720: add             x4, PP, #0x51, lsl #12  ; [pp+0x51ef8] List(13) [0, 0x5, 0x4, 0x1, "color", 0x3, "height", 0x1, "margin", 0x4, "width", 0x2, Null]
    //     0xaa5724: ldr             x4, [x4, #0xef8]
    // 0xaa5728: r0 = Container()
    //     0xaa5728: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaa572c: r0 = InkWell()
    //     0xaa572c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaa5730: ldur            x1, [fp, #-0x18]
    // 0xaa5734: StoreField: r0->field_b = r1
    //     0xaa5734: stur            w1, [x0, #0xb]
    // 0xaa5738: r1 = true
    //     0xaa5738: add             x1, NULL, #0x20  ; true
    // 0xaa573c: StoreField: r0->field_43 = r1
    //     0xaa573c: stur            w1, [x0, #0x43]
    // 0xaa5740: r2 = Instance_BoxShape
    //     0xaa5740: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaa5744: ldr             x2, [x2, #0x80]
    // 0xaa5748: StoreField: r0->field_47 = r2
    //     0xaa5748: stur            w2, [x0, #0x47]
    // 0xaa574c: StoreField: r0->field_6f = r1
    //     0xaa574c: stur            w1, [x0, #0x6f]
    // 0xaa5750: r2 = false
    //     0xaa5750: add             x2, NULL, #0x30  ; false
    // 0xaa5754: StoreField: r0->field_73 = r2
    //     0xaa5754: stur            w2, [x0, #0x73]
    // 0xaa5758: StoreField: r0->field_83 = r1
    //     0xaa5758: stur            w1, [x0, #0x83]
    // 0xaa575c: StoreField: r0->field_7b = r2
    //     0xaa575c: stur            w2, [x0, #0x7b]
    // 0xaa5760: LeaveFrame
    //     0xaa5760: mov             SP, fp
    //     0xaa5764: ldp             fp, lr, [SP], #0x10
    // 0xaa5768: ret
    //     0xaa5768: ret             
    // 0xaa576c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa576c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa5770: b               #0xaa55ec
    // 0xaa5774: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaa5774: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xaa5778: r0 = RangeErrorSharedWithFPURegs()
    //     0xaa5778: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xaa577c: SaveReg d0
    //     0xaa577c: str             q0, [SP, #-0x10]!
    // 0xaa5780: SaveReg r0
    //     0xaa5780: str             x0, [SP, #-8]!
    // 0xaa5784: r0 = AllocateDouble()
    //     0xaa5784: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa5788: mov             x1, x0
    // 0xaa578c: RestoreReg r0
    //     0xaa578c: ldr             x0, [SP], #8
    // 0xaa5790: RestoreReg d0
    //     0xaa5790: ldr             q0, [SP], #0x10
    // 0xaa5794: b               #0xaa56ec
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0xaa613c, size: 0x118
    // 0xaa613c: EnterFrame
    //     0xaa613c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6140: mov             fp, SP
    // 0xaa6144: AllocStack(0x18)
    //     0xaa6144: sub             SP, SP, #0x18
    // 0xaa6148: CheckStackOverflow
    //     0xaa6148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa614c: cmp             SP, x16
    //     0xaa6150: b.ls            #0xaa623c
    // 0xaa6154: ldr             x0, [fp, #0x10]
    // 0xaa6158: LoadField: r1 = r0->field_b
    //     0xaa6158: ldur            w1, [x0, #0xb]
    // 0xaa615c: DecompressPointer r1
    //     0xaa615c: add             x1, x1, HEAP, lsl #32
    // 0xaa6160: cmp             w1, NULL
    // 0xaa6164: b.eq            #0xaa6180
    // 0xaa6168: LoadField: r2 = r0->field_f
    //     0xaa6168: ldur            x2, [x0, #0xf]
    // 0xaa616c: r0 = LoadInt32Instr(r1)
    //     0xaa616c: sbfx            x0, x1, #1, #0x1f
    //     0xaa6170: tbz             w1, #0, #0xaa6178
    //     0xaa6174: ldur            x0, [x1, #7]
    // 0xaa6178: cmp             x2, x0
    // 0xaa617c: b.le            #0xaa6188
    // 0xaa6180: r0 = Null
    //     0xaa6180: mov             x0, NULL
    // 0xaa6184: b               #0xaa61bc
    // 0xaa6188: scvtf           d0, x2
    // 0xaa618c: scvtf           d1, x0
    // 0xaa6190: fdiv            d2, d0, d1
    // 0xaa6194: r0 = inline_Allocate_Double()
    //     0xaa6194: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaa6198: add             x0, x0, #0x10
    //     0xaa619c: cmp             x1, x0
    //     0xaa61a0: b.ls            #0xaa6244
    //     0xaa61a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa61a8: sub             x0, x0, #0xf
    //     0xaa61ac: movz            x1, #0xe15c
    //     0xaa61b0: movk            x1, #0x3, lsl #16
    //     0xaa61b4: stur            x1, [x0, #-1]
    // 0xaa61b8: StoreField: r0->field_7 = d2
    //     0xaa61b8: stur            d2, [x0, #7]
    // 0xaa61bc: ldr             x1, [fp, #0x20]
    // 0xaa61c0: stur            x0, [fp, #-8]
    // 0xaa61c4: r0 = of()
    //     0xaa61c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaa61c8: LoadField: r1 = r0->field_5b
    //     0xaa61c8: ldur            w1, [x0, #0x5b]
    // 0xaa61cc: DecompressPointer r1
    //     0xaa61cc: add             x1, x1, HEAP, lsl #32
    // 0xaa61d0: r0 = LoadClassIdInstr(r1)
    //     0xaa61d0: ldur            x0, [x1, #-1]
    //     0xaa61d4: ubfx            x0, x0, #0xc, #0x14
    // 0xaa61d8: d0 = 0.300000
    //     0xaa61d8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xaa61dc: ldr             d0, [x17, #0x658]
    // 0xaa61e0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xaa61e0: sub             lr, x0, #0xffa
    //     0xaa61e4: ldr             lr, [x21, lr, lsl #3]
    //     0xaa61e8: blr             lr
    // 0xaa61ec: stur            x0, [fp, #-0x10]
    // 0xaa61f0: r0 = CircularProgressIndicator()
    //     0xaa61f0: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xaa61f4: mov             x1, x0
    // 0xaa61f8: r0 = Instance__ActivityIndicatorType
    //     0xaa61f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xaa61fc: ldr             x0, [x0, #0x1b0]
    // 0xaa6200: stur            x1, [fp, #-0x18]
    // 0xaa6204: StoreField: r1->field_23 = r0
    //     0xaa6204: stur            w0, [x1, #0x23]
    // 0xaa6208: ldur            x0, [fp, #-8]
    // 0xaa620c: StoreField: r1->field_b = r0
    //     0xaa620c: stur            w0, [x1, #0xb]
    // 0xaa6210: ldur            x0, [fp, #-0x10]
    // 0xaa6214: StoreField: r1->field_13 = r0
    //     0xaa6214: stur            w0, [x1, #0x13]
    // 0xaa6218: r0 = Center()
    //     0xaa6218: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaa621c: r1 = Instance_Alignment
    //     0xaa621c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaa6220: ldr             x1, [x1, #0xb10]
    // 0xaa6224: StoreField: r0->field_f = r1
    //     0xaa6224: stur            w1, [x0, #0xf]
    // 0xaa6228: ldur            x1, [fp, #-0x18]
    // 0xaa622c: StoreField: r0->field_b = r1
    //     0xaa622c: stur            w1, [x0, #0xb]
    // 0xaa6230: LeaveFrame
    //     0xaa6230: mov             SP, fp
    //     0xaa6234: ldp             fp, lr, [SP], #0x10
    // 0xaa6238: ret
    //     0xaa6238: ret             
    // 0xaa623c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa623c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6240: b               #0xaa6154
    // 0xaa6244: SaveReg d2
    //     0xaa6244: str             q2, [SP, #-0x10]!
    // 0xaa6248: r0 = AllocateDouble()
    //     0xaa6248: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa624c: RestoreReg d2
    //     0xaa624c: ldr             q2, [SP], #0x10
    // 0xaa6250: b               #0xaa61b8
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaa6254, size: 0x6dc
    // 0xaa6254: EnterFrame
    //     0xaa6254: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6258: mov             fp, SP
    // 0xaa625c: AllocStack(0x88)
    //     0xaa625c: sub             SP, SP, #0x88
    // 0xaa6260: SetupParameters()
    //     0xaa6260: ldr             x0, [fp, #0x20]
    //     0xaa6264: ldur            w3, [x0, #0x17]
    //     0xaa6268: add             x3, x3, HEAP, lsl #32
    //     0xaa626c: stur            x3, [fp, #-0x18]
    // 0xaa6270: CheckStackOverflow
    //     0xaa6270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6274: cmp             SP, x16
    //     0xaa6278: b.ls            #0xaa68f0
    // 0xaa627c: LoadField: r0 = r3->field_f
    //     0xaa627c: ldur            w0, [x3, #0xf]
    // 0xaa6280: DecompressPointer r0
    //     0xaa6280: add             x0, x0, HEAP, lsl #32
    // 0xaa6284: LoadField: r2 = r0->field_37
    //     0xaa6284: ldur            w2, [x0, #0x37]
    // 0xaa6288: DecompressPointer r2
    //     0xaa6288: add             x2, x2, HEAP, lsl #32
    // 0xaa628c: LoadField: r0 = r2->field_b
    //     0xaa628c: ldur            w0, [x2, #0xb]
    // 0xaa6290: ldr             x1, [fp, #0x10]
    // 0xaa6294: r4 = LoadInt32Instr(r1)
    //     0xaa6294: sbfx            x4, x1, #1, #0x1f
    //     0xaa6298: tbz             w1, #0, #0xaa62a0
    //     0xaa629c: ldur            x4, [x1, #7]
    // 0xaa62a0: stur            x4, [fp, #-0x10]
    // 0xaa62a4: r1 = LoadInt32Instr(r0)
    //     0xaa62a4: sbfx            x1, x0, #1, #0x1f
    // 0xaa62a8: mov             x0, x1
    // 0xaa62ac: mov             x1, x4
    // 0xaa62b0: cmp             x1, x0
    // 0xaa62b4: b.hs            #0xaa68f8
    // 0xaa62b8: LoadField: r0 = r2->field_f
    //     0xaa62b8: ldur            w0, [x2, #0xf]
    // 0xaa62bc: DecompressPointer r0
    //     0xaa62bc: add             x0, x0, HEAP, lsl #32
    // 0xaa62c0: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0xaa62c0: add             x16, x0, x4, lsl #2
    //     0xaa62c4: ldur            w5, [x16, #0xf]
    // 0xaa62c8: DecompressPointer r5
    //     0xaa62c8: add             x5, x5, HEAP, lsl #32
    // 0xaa62cc: stur            x5, [fp, #-8]
    // 0xaa62d0: r1 = <Widget>
    //     0xaa62d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaa62d4: r2 = 0
    //     0xaa62d4: movz            x2, #0
    // 0xaa62d8: r0 = _GrowableList()
    //     0xaa62d8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaa62dc: mov             x2, x0
    // 0xaa62e0: ldur            x1, [fp, #-8]
    // 0xaa62e4: stur            x2, [fp, #-0x20]
    // 0xaa62e8: LoadField: r0 = r1->field_f
    //     0xaa62e8: ldur            w0, [x1, #0xf]
    // 0xaa62ec: DecompressPointer r0
    //     0xaa62ec: add             x0, x0, HEAP, lsl #32
    // 0xaa62f0: r3 = LoadClassIdInstr(r0)
    //     0xaa62f0: ldur            x3, [x0, #-1]
    //     0xaa62f4: ubfx            x3, x3, #0xc, #0x14
    // 0xaa62f8: r16 = "image"
    //     0xaa62f8: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xaa62fc: stp             x16, x0, [SP]
    // 0xaa6300: mov             x0, x3
    // 0xaa6304: mov             lr, x0
    // 0xaa6308: ldr             lr, [x21, lr, lsl #3]
    // 0xaa630c: blr             lr
    // 0xaa6310: tbnz            w0, #4, #0xaa6500
    // 0xaa6314: ldur            x2, [fp, #-0x18]
    // 0xaa6318: LoadField: r0 = r2->field_f
    //     0xaa6318: ldur            w0, [x2, #0xf]
    // 0xaa631c: DecompressPointer r0
    //     0xaa631c: add             x0, x0, HEAP, lsl #32
    // 0xaa6320: LoadField: r1 = r0->field_2b
    //     0xaa6320: ldur            w1, [x0, #0x2b]
    // 0xaa6324: DecompressPointer r1
    //     0xaa6324: add             x1, x1, HEAP, lsl #32
    // 0xaa6328: tbnz            w1, #4, #0xaa6338
    // 0xaa632c: r1 = Instance_BoxFit
    //     0xaa632c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xaa6330: ldr             x1, [x1, #0x118]
    // 0xaa6334: b               #0xaa6340
    // 0xaa6338: r1 = Instance_BoxFit
    //     0xaa6338: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xaa633c: ldr             x1, [x1, #0xf38]
    // 0xaa6340: ldur            x0, [fp, #-8]
    // 0xaa6344: stur            x1, [fp, #-0x30]
    // 0xaa6348: LoadField: r3 = r0->field_13
    //     0xaa6348: ldur            w3, [x0, #0x13]
    // 0xaa634c: DecompressPointer r3
    //     0xaa634c: add             x3, x3, HEAP, lsl #32
    // 0xaa6350: cmp             w3, NULL
    // 0xaa6354: b.ne            #0xaa635c
    // 0xaa6358: r3 = ""
    //     0xaa6358: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaa635c: ldur            x0, [fp, #-0x20]
    // 0xaa6360: stur            x3, [fp, #-0x28]
    // 0xaa6364: r0 = ImageHeaders.forImages()
    //     0xaa6364: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xaa6368: r1 = Function '<anonymous closure>':.
    //     0xaa6368: add             x1, PP, #0x52, lsl #12  ; [pp+0x52020] AnonymousClosure: (0xaa613c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xaa636c: ldr             x1, [x1, #0x20]
    // 0xaa6370: r2 = Null
    //     0xaa6370: mov             x2, NULL
    // 0xaa6374: stur            x0, [fp, #-0x38]
    // 0xaa6378: r0 = AllocateClosure()
    //     0xaa6378: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa637c: r1 = Function '<anonymous closure>':.
    //     0xaa637c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52028] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaa6380: ldr             x1, [x1, #0x28]
    // 0xaa6384: r2 = Null
    //     0xaa6384: mov             x2, NULL
    // 0xaa6388: stur            x0, [fp, #-0x40]
    // 0xaa638c: r0 = AllocateClosure()
    //     0xaa638c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa6390: stur            x0, [fp, #-0x48]
    // 0xaa6394: r0 = CachedNetworkImage()
    //     0xaa6394: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaa6398: stur            x0, [fp, #-0x50]
    // 0xaa639c: ldur            x16, [fp, #-0x30]
    // 0xaa63a0: r30 = inf
    //     0xaa63a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xaa63a4: ldr             lr, [lr, #0x9f8]
    // 0xaa63a8: stp             lr, x16, [SP, #0x18]
    // 0xaa63ac: ldur            x16, [fp, #-0x38]
    // 0xaa63b0: ldur            lr, [fp, #-0x40]
    // 0xaa63b4: stp             lr, x16, [SP, #8]
    // 0xaa63b8: ldur            x16, [fp, #-0x48]
    // 0xaa63bc: str             x16, [SP]
    // 0xaa63c0: mov             x1, x0
    // 0xaa63c4: ldur            x2, [fp, #-0x28]
    // 0xaa63c8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xaa63c8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51f50] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xaa63cc: ldr             x4, [x4, #0xf50]
    // 0xaa63d0: r0 = CachedNetworkImage()
    //     0xaa63d0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaa63d4: r1 = Null
    //     0xaa63d4: mov             x1, NULL
    // 0xaa63d8: r2 = 2
    //     0xaa63d8: movz            x2, #0x2
    // 0xaa63dc: r0 = AllocateArray()
    //     0xaa63dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaa63e0: mov             x2, x0
    // 0xaa63e4: ldur            x0, [fp, #-0x50]
    // 0xaa63e8: stur            x2, [fp, #-0x28]
    // 0xaa63ec: StoreField: r2->field_f = r0
    //     0xaa63ec: stur            w0, [x2, #0xf]
    // 0xaa63f0: r1 = <Widget>
    //     0xaa63f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaa63f4: r0 = AllocateGrowableArray()
    //     0xaa63f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaa63f8: mov             x1, x0
    // 0xaa63fc: ldur            x0, [fp, #-0x28]
    // 0xaa6400: stur            x1, [fp, #-0x30]
    // 0xaa6404: StoreField: r1->field_f = r0
    //     0xaa6404: stur            w0, [x1, #0xf]
    // 0xaa6408: r0 = 2
    //     0xaa6408: movz            x0, #0x2
    // 0xaa640c: StoreField: r1->field_b = r0
    //     0xaa640c: stur            w0, [x1, #0xb]
    // 0xaa6410: r0 = Stack()
    //     0xaa6410: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xaa6414: mov             x1, x0
    // 0xaa6418: r0 = Instance_AlignmentDirectional
    //     0xaa6418: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xaa641c: ldr             x0, [x0, #0xd08]
    // 0xaa6420: stur            x1, [fp, #-0x28]
    // 0xaa6424: StoreField: r1->field_f = r0
    //     0xaa6424: stur            w0, [x1, #0xf]
    // 0xaa6428: r2 = Instance_StackFit
    //     0xaa6428: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xaa642c: ldr             x2, [x2, #0xfa8]
    // 0xaa6430: ArrayStore: r1[0] = r2  ; List_4
    //     0xaa6430: stur            w2, [x1, #0x17]
    // 0xaa6434: r3 = Instance_Clip
    //     0xaa6434: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xaa6438: ldr             x3, [x3, #0x7e0]
    // 0xaa643c: StoreField: r1->field_1b = r3
    //     0xaa643c: stur            w3, [x1, #0x1b]
    // 0xaa6440: ldur            x4, [fp, #-0x30]
    // 0xaa6444: StoreField: r1->field_b = r4
    //     0xaa6444: stur            w4, [x1, #0xb]
    // 0xaa6448: r0 = GestureDetector()
    //     0xaa6448: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaa644c: stur            x0, [fp, #-0x30]
    // 0xaa6450: ldur            x16, [fp, #-0x28]
    // 0xaa6454: str             x16, [SP]
    // 0xaa6458: mov             x1, x0
    // 0xaa645c: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xaa645c: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfb0] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xaa6460: ldr             x4, [x4, #0xfb0]
    // 0xaa6464: r0 = GestureDetector()
    //     0xaa6464: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaa6468: r0 = Center()
    //     0xaa6468: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaa646c: r1 = Instance_Alignment
    //     0xaa646c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaa6470: ldr             x1, [x1, #0xb10]
    // 0xaa6474: stur            x0, [fp, #-0x28]
    // 0xaa6478: StoreField: r0->field_f = r1
    //     0xaa6478: stur            w1, [x0, #0xf]
    // 0xaa647c: ldur            x1, [fp, #-0x30]
    // 0xaa6480: StoreField: r0->field_b = r1
    //     0xaa6480: stur            w1, [x0, #0xb]
    // 0xaa6484: ldur            x2, [fp, #-0x20]
    // 0xaa6488: LoadField: r1 = r2->field_b
    //     0xaa6488: ldur            w1, [x2, #0xb]
    // 0xaa648c: LoadField: r3 = r2->field_f
    //     0xaa648c: ldur            w3, [x2, #0xf]
    // 0xaa6490: DecompressPointer r3
    //     0xaa6490: add             x3, x3, HEAP, lsl #32
    // 0xaa6494: LoadField: r4 = r3->field_b
    //     0xaa6494: ldur            w4, [x3, #0xb]
    // 0xaa6498: r3 = LoadInt32Instr(r1)
    //     0xaa6498: sbfx            x3, x1, #1, #0x1f
    // 0xaa649c: stur            x3, [fp, #-0x58]
    // 0xaa64a0: r1 = LoadInt32Instr(r4)
    //     0xaa64a0: sbfx            x1, x4, #1, #0x1f
    // 0xaa64a4: cmp             x3, x1
    // 0xaa64a8: b.ne            #0xaa64b4
    // 0xaa64ac: mov             x1, x2
    // 0xaa64b0: r0 = _growToNextCapacity()
    //     0xaa64b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa64b4: ldur            x2, [fp, #-0x20]
    // 0xaa64b8: ldur            x3, [fp, #-0x58]
    // 0xaa64bc: add             x0, x3, #1
    // 0xaa64c0: lsl             x1, x0, #1
    // 0xaa64c4: StoreField: r2->field_b = r1
    //     0xaa64c4: stur            w1, [x2, #0xb]
    // 0xaa64c8: LoadField: r1 = r2->field_f
    //     0xaa64c8: ldur            w1, [x2, #0xf]
    // 0xaa64cc: DecompressPointer r1
    //     0xaa64cc: add             x1, x1, HEAP, lsl #32
    // 0xaa64d0: ldur            x0, [fp, #-0x28]
    // 0xaa64d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaa64d4: add             x25, x1, x3, lsl #2
    //     0xaa64d8: add             x25, x25, #0xf
    //     0xaa64dc: str             w0, [x25]
    //     0xaa64e0: tbz             w0, #0, #0xaa64fc
    //     0xaa64e4: ldurb           w16, [x1, #-1]
    //     0xaa64e8: ldurb           w17, [x0, #-1]
    //     0xaa64ec: and             x16, x17, x16, lsr #2
    //     0xaa64f0: tst             x16, HEAP, lsr #32
    //     0xaa64f4: b.eq            #0xaa64fc
    //     0xaa64f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaa64fc: b               #0xaa65e4
    // 0xaa6500: ldur            x2, [fp, #-0x20]
    // 0xaa6504: ldur            x0, [fp, #-8]
    // 0xaa6508: r1 = Instance_Alignment
    //     0xaa6508: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaa650c: ldr             x1, [x1, #0xb10]
    // 0xaa6510: LoadField: r3 = r0->field_13
    //     0xaa6510: ldur            w3, [x0, #0x13]
    // 0xaa6514: DecompressPointer r3
    //     0xaa6514: add             x3, x3, HEAP, lsl #32
    // 0xaa6518: cmp             w3, NULL
    // 0xaa651c: b.ne            #0xaa6528
    // 0xaa6520: r0 = ""
    //     0xaa6520: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaa6524: b               #0xaa652c
    // 0xaa6528: mov             x0, x3
    // 0xaa652c: stur            x0, [fp, #-8]
    // 0xaa6530: r0 = VideoPlayerWidget()
    //     0xaa6530: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xaa6534: mov             x1, x0
    // 0xaa6538: ldur            x0, [fp, #-8]
    // 0xaa653c: stur            x1, [fp, #-0x28]
    // 0xaa6540: StoreField: r1->field_b = r0
    //     0xaa6540: stur            w0, [x1, #0xb]
    // 0xaa6544: r0 = true
    //     0xaa6544: add             x0, NULL, #0x20  ; true
    // 0xaa6548: StoreField: r1->field_f = r0
    //     0xaa6548: stur            w0, [x1, #0xf]
    // 0xaa654c: r0 = Center()
    //     0xaa654c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaa6550: mov             x2, x0
    // 0xaa6554: r0 = Instance_Alignment
    //     0xaa6554: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaa6558: ldr             x0, [x0, #0xb10]
    // 0xaa655c: stur            x2, [fp, #-8]
    // 0xaa6560: StoreField: r2->field_f = r0
    //     0xaa6560: stur            w0, [x2, #0xf]
    // 0xaa6564: ldur            x0, [fp, #-0x28]
    // 0xaa6568: StoreField: r2->field_b = r0
    //     0xaa6568: stur            w0, [x2, #0xb]
    // 0xaa656c: ldur            x0, [fp, #-0x20]
    // 0xaa6570: LoadField: r1 = r0->field_b
    //     0xaa6570: ldur            w1, [x0, #0xb]
    // 0xaa6574: LoadField: r3 = r0->field_f
    //     0xaa6574: ldur            w3, [x0, #0xf]
    // 0xaa6578: DecompressPointer r3
    //     0xaa6578: add             x3, x3, HEAP, lsl #32
    // 0xaa657c: LoadField: r4 = r3->field_b
    //     0xaa657c: ldur            w4, [x3, #0xb]
    // 0xaa6580: r3 = LoadInt32Instr(r1)
    //     0xaa6580: sbfx            x3, x1, #1, #0x1f
    // 0xaa6584: stur            x3, [fp, #-0x58]
    // 0xaa6588: r1 = LoadInt32Instr(r4)
    //     0xaa6588: sbfx            x1, x4, #1, #0x1f
    // 0xaa658c: cmp             x3, x1
    // 0xaa6590: b.ne            #0xaa659c
    // 0xaa6594: mov             x1, x0
    // 0xaa6598: r0 = _growToNextCapacity()
    //     0xaa6598: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa659c: ldur            x2, [fp, #-0x20]
    // 0xaa65a0: ldur            x3, [fp, #-0x58]
    // 0xaa65a4: add             x0, x3, #1
    // 0xaa65a8: lsl             x1, x0, #1
    // 0xaa65ac: StoreField: r2->field_b = r1
    //     0xaa65ac: stur            w1, [x2, #0xb]
    // 0xaa65b0: LoadField: r1 = r2->field_f
    //     0xaa65b0: ldur            w1, [x2, #0xf]
    // 0xaa65b4: DecompressPointer r1
    //     0xaa65b4: add             x1, x1, HEAP, lsl #32
    // 0xaa65b8: ldur            x0, [fp, #-8]
    // 0xaa65bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaa65bc: add             x25, x1, x3, lsl #2
    //     0xaa65c0: add             x25, x25, #0xf
    //     0xaa65c4: str             w0, [x25]
    //     0xaa65c8: tbz             w0, #0, #0xaa65e4
    //     0xaa65cc: ldurb           w16, [x1, #-1]
    //     0xaa65d0: ldurb           w17, [x0, #-1]
    //     0xaa65d4: and             x16, x17, x16, lsr #2
    //     0xaa65d8: tst             x16, HEAP, lsr #32
    //     0xaa65dc: b.eq            #0xaa65e4
    //     0xaa65e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaa65e4: ldur            x0, [fp, #-0x10]
    // 0xaa65e8: cmp             x0, #0
    // 0xaa65ec: b.le            #0xaa673c
    // 0xaa65f0: ldr             x1, [fp, #0x18]
    // 0xaa65f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa65f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa65f8: r0 = _of()
    //     0xaa65f8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaa65fc: LoadField: r1 = r0->field_7
    //     0xaa65fc: ldur            w1, [x0, #7]
    // 0xaa6600: DecompressPointer r1
    //     0xaa6600: add             x1, x1, HEAP, lsl #32
    // 0xaa6604: LoadField: d0 = r1->field_7
    //     0xaa6604: ldur            d0, [x1, #7]
    // 0xaa6608: d1 = 0.200000
    //     0xaa6608: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xaa660c: fmul            d2, d0, d1
    // 0xaa6610: stur            d2, [fp, #-0x60]
    // 0xaa6614: r0 = Container()
    //     0xaa6614: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaa6618: stur            x0, [fp, #-8]
    // 0xaa661c: r16 = Instance_Color
    //     0xaa661c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xaa6620: ldr             x16, [x16, #0xf88]
    // 0xaa6624: str             x16, [SP]
    // 0xaa6628: mov             x1, x0
    // 0xaa662c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaa662c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaa6630: ldr             x4, [x4, #0xf40]
    // 0xaa6634: r0 = Container()
    //     0xaa6634: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaa6638: r0 = GestureDetector()
    //     0xaa6638: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaa663c: ldur            x2, [fp, #-0x18]
    // 0xaa6640: r1 = Function '<anonymous closure>':.
    //     0xaa6640: add             x1, PP, #0x52, lsl #12  ; [pp+0x52030] AnonymousClosure: (0xaa6990), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xaa6644: ldr             x1, [x1, #0x30]
    // 0xaa6648: stur            x0, [fp, #-0x28]
    // 0xaa664c: r0 = AllocateClosure()
    //     0xaa664c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa6650: ldur            x16, [fp, #-8]
    // 0xaa6654: stp             x16, x0, [SP]
    // 0xaa6658: ldur            x1, [fp, #-0x28]
    // 0xaa665c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaa665c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaa6660: ldr             x4, [x4, #0xaf0]
    // 0xaa6664: r0 = GestureDetector()
    //     0xaa6664: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaa6668: r1 = <StackParentData>
    //     0xaa6668: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xaa666c: ldr             x1, [x1, #0x8e0]
    // 0xaa6670: r0 = Positioned()
    //     0xaa6670: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xaa6674: mov             x2, x0
    // 0xaa6678: r0 = 0.000000
    //     0xaa6678: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaa667c: stur            x2, [fp, #-8]
    // 0xaa6680: StoreField: r2->field_13 = r0
    //     0xaa6680: stur            w0, [x2, #0x13]
    // 0xaa6684: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa6684: stur            w0, [x2, #0x17]
    // 0xaa6688: StoreField: r2->field_1f = r0
    //     0xaa6688: stur            w0, [x2, #0x1f]
    // 0xaa668c: ldur            d0, [fp, #-0x60]
    // 0xaa6690: r1 = inline_Allocate_Double()
    //     0xaa6690: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xaa6694: add             x1, x1, #0x10
    //     0xaa6698: cmp             x3, x1
    //     0xaa669c: b.ls            #0xaa68fc
    //     0xaa66a0: str             x1, [THR, #0x50]  ; THR::top
    //     0xaa66a4: sub             x1, x1, #0xf
    //     0xaa66a8: movz            x3, #0xe15c
    //     0xaa66ac: movk            x3, #0x3, lsl #16
    //     0xaa66b0: stur            x3, [x1, #-1]
    // 0xaa66b4: StoreField: r1->field_7 = d0
    //     0xaa66b4: stur            d0, [x1, #7]
    // 0xaa66b8: StoreField: r2->field_23 = r1
    //     0xaa66b8: stur            w1, [x2, #0x23]
    // 0xaa66bc: ldur            x1, [fp, #-0x28]
    // 0xaa66c0: StoreField: r2->field_b = r1
    //     0xaa66c0: stur            w1, [x2, #0xb]
    // 0xaa66c4: ldur            x3, [fp, #-0x20]
    // 0xaa66c8: LoadField: r1 = r3->field_b
    //     0xaa66c8: ldur            w1, [x3, #0xb]
    // 0xaa66cc: LoadField: r4 = r3->field_f
    //     0xaa66cc: ldur            w4, [x3, #0xf]
    // 0xaa66d0: DecompressPointer r4
    //     0xaa66d0: add             x4, x4, HEAP, lsl #32
    // 0xaa66d4: LoadField: r5 = r4->field_b
    //     0xaa66d4: ldur            w5, [x4, #0xb]
    // 0xaa66d8: r4 = LoadInt32Instr(r1)
    //     0xaa66d8: sbfx            x4, x1, #1, #0x1f
    // 0xaa66dc: stur            x4, [fp, #-0x58]
    // 0xaa66e0: r1 = LoadInt32Instr(r5)
    //     0xaa66e0: sbfx            x1, x5, #1, #0x1f
    // 0xaa66e4: cmp             x4, x1
    // 0xaa66e8: b.ne            #0xaa66f4
    // 0xaa66ec: mov             x1, x3
    // 0xaa66f0: r0 = _growToNextCapacity()
    //     0xaa66f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa66f4: ldur            x2, [fp, #-0x20]
    // 0xaa66f8: ldur            x3, [fp, #-0x58]
    // 0xaa66fc: add             x0, x3, #1
    // 0xaa6700: lsl             x1, x0, #1
    // 0xaa6704: StoreField: r2->field_b = r1
    //     0xaa6704: stur            w1, [x2, #0xb]
    // 0xaa6708: LoadField: r1 = r2->field_f
    //     0xaa6708: ldur            w1, [x2, #0xf]
    // 0xaa670c: DecompressPointer r1
    //     0xaa670c: add             x1, x1, HEAP, lsl #32
    // 0xaa6710: ldur            x0, [fp, #-8]
    // 0xaa6714: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaa6714: add             x25, x1, x3, lsl #2
    //     0xaa6718: add             x25, x25, #0xf
    //     0xaa671c: str             w0, [x25]
    //     0xaa6720: tbz             w0, #0, #0xaa673c
    //     0xaa6724: ldurb           w16, [x1, #-1]
    //     0xaa6728: ldurb           w17, [x0, #-1]
    //     0xaa672c: and             x16, x17, x16, lsr #2
    //     0xaa6730: tst             x16, HEAP, lsr #32
    //     0xaa6734: b.eq            #0xaa673c
    //     0xaa6738: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaa673c: ldur            x3, [fp, #-0x18]
    // 0xaa6740: ldur            x0, [fp, #-0x10]
    // 0xaa6744: LoadField: r1 = r3->field_f
    //     0xaa6744: ldur            w1, [x3, #0xf]
    // 0xaa6748: DecompressPointer r1
    //     0xaa6748: add             x1, x1, HEAP, lsl #32
    // 0xaa674c: LoadField: r4 = r1->field_37
    //     0xaa674c: ldur            w4, [x1, #0x37]
    // 0xaa6750: DecompressPointer r4
    //     0xaa6750: add             x4, x4, HEAP, lsl #32
    // 0xaa6754: LoadField: r1 = r4->field_b
    //     0xaa6754: ldur            w1, [x4, #0xb]
    // 0xaa6758: r4 = LoadInt32Instr(r1)
    //     0xaa6758: sbfx            x4, x1, #1, #0x1f
    // 0xaa675c: sub             x1, x4, #1
    // 0xaa6760: cmp             x0, x1
    // 0xaa6764: b.ge            #0xaa68b4
    // 0xaa6768: ldr             x1, [fp, #0x18]
    // 0xaa676c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaa676c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaa6770: r0 = _of()
    //     0xaa6770: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xaa6774: LoadField: r1 = r0->field_7
    //     0xaa6774: ldur            w1, [x0, #7]
    // 0xaa6778: DecompressPointer r1
    //     0xaa6778: add             x1, x1, HEAP, lsl #32
    // 0xaa677c: LoadField: d0 = r1->field_7
    //     0xaa677c: ldur            d0, [x1, #7]
    // 0xaa6780: d1 = 0.200000
    //     0xaa6780: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xaa6784: fmul            d2, d0, d1
    // 0xaa6788: stur            d2, [fp, #-0x60]
    // 0xaa678c: r0 = Container()
    //     0xaa678c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaa6790: stur            x0, [fp, #-8]
    // 0xaa6794: r16 = Instance_Color
    //     0xaa6794: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xaa6798: ldr             x16, [x16, #0xf88]
    // 0xaa679c: str             x16, [SP]
    // 0xaa67a0: mov             x1, x0
    // 0xaa67a4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaa67a4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaa67a8: ldr             x4, [x4, #0xf40]
    // 0xaa67ac: r0 = Container()
    //     0xaa67ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaa67b0: r0 = GestureDetector()
    //     0xaa67b0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaa67b4: ldur            x2, [fp, #-0x18]
    // 0xaa67b8: r1 = Function '<anonymous closure>':.
    //     0xaa67b8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52038] AnonymousClosure: (0xaa6930), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xaa67bc: ldr             x1, [x1, #0x38]
    // 0xaa67c0: stur            x0, [fp, #-0x18]
    // 0xaa67c4: r0 = AllocateClosure()
    //     0xaa67c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa67c8: ldur            x16, [fp, #-8]
    // 0xaa67cc: stp             x16, x0, [SP]
    // 0xaa67d0: ldur            x1, [fp, #-0x18]
    // 0xaa67d4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaa67d4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaa67d8: ldr             x4, [x4, #0xaf0]
    // 0xaa67dc: r0 = GestureDetector()
    //     0xaa67dc: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaa67e0: r1 = <StackParentData>
    //     0xaa67e0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xaa67e4: ldr             x1, [x1, #0x8e0]
    // 0xaa67e8: r0 = Positioned()
    //     0xaa67e8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xaa67ec: mov             x2, x0
    // 0xaa67f0: r0 = 0.000000
    //     0xaa67f0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaa67f4: stur            x2, [fp, #-8]
    // 0xaa67f8: ArrayStore: r2[0] = r0  ; List_4
    //     0xaa67f8: stur            w0, [x2, #0x17]
    // 0xaa67fc: StoreField: r2->field_1b = r0
    //     0xaa67fc: stur            w0, [x2, #0x1b]
    // 0xaa6800: StoreField: r2->field_1f = r0
    //     0xaa6800: stur            w0, [x2, #0x1f]
    // 0xaa6804: ldur            d0, [fp, #-0x60]
    // 0xaa6808: r0 = inline_Allocate_Double()
    //     0xaa6808: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaa680c: add             x0, x0, #0x10
    //     0xaa6810: cmp             x1, x0
    //     0xaa6814: b.ls            #0xaa6918
    //     0xaa6818: str             x0, [THR, #0x50]  ; THR::top
    //     0xaa681c: sub             x0, x0, #0xf
    //     0xaa6820: movz            x1, #0xe15c
    //     0xaa6824: movk            x1, #0x3, lsl #16
    //     0xaa6828: stur            x1, [x0, #-1]
    // 0xaa682c: StoreField: r0->field_7 = d0
    //     0xaa682c: stur            d0, [x0, #7]
    // 0xaa6830: StoreField: r2->field_23 = r0
    //     0xaa6830: stur            w0, [x2, #0x23]
    // 0xaa6834: ldur            x0, [fp, #-0x18]
    // 0xaa6838: StoreField: r2->field_b = r0
    //     0xaa6838: stur            w0, [x2, #0xb]
    // 0xaa683c: ldur            x0, [fp, #-0x20]
    // 0xaa6840: LoadField: r1 = r0->field_b
    //     0xaa6840: ldur            w1, [x0, #0xb]
    // 0xaa6844: LoadField: r3 = r0->field_f
    //     0xaa6844: ldur            w3, [x0, #0xf]
    // 0xaa6848: DecompressPointer r3
    //     0xaa6848: add             x3, x3, HEAP, lsl #32
    // 0xaa684c: LoadField: r4 = r3->field_b
    //     0xaa684c: ldur            w4, [x3, #0xb]
    // 0xaa6850: r3 = LoadInt32Instr(r1)
    //     0xaa6850: sbfx            x3, x1, #1, #0x1f
    // 0xaa6854: stur            x3, [fp, #-0x10]
    // 0xaa6858: r1 = LoadInt32Instr(r4)
    //     0xaa6858: sbfx            x1, x4, #1, #0x1f
    // 0xaa685c: cmp             x3, x1
    // 0xaa6860: b.ne            #0xaa686c
    // 0xaa6864: mov             x1, x0
    // 0xaa6868: r0 = _growToNextCapacity()
    //     0xaa6868: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaa686c: ldur            x2, [fp, #-0x20]
    // 0xaa6870: ldur            x3, [fp, #-0x10]
    // 0xaa6874: add             x0, x3, #1
    // 0xaa6878: lsl             x1, x0, #1
    // 0xaa687c: StoreField: r2->field_b = r1
    //     0xaa687c: stur            w1, [x2, #0xb]
    // 0xaa6880: LoadField: r1 = r2->field_f
    //     0xaa6880: ldur            w1, [x2, #0xf]
    // 0xaa6884: DecompressPointer r1
    //     0xaa6884: add             x1, x1, HEAP, lsl #32
    // 0xaa6888: ldur            x0, [fp, #-8]
    // 0xaa688c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaa688c: add             x25, x1, x3, lsl #2
    //     0xaa6890: add             x25, x25, #0xf
    //     0xaa6894: str             w0, [x25]
    //     0xaa6898: tbz             w0, #0, #0xaa68b4
    //     0xaa689c: ldurb           w16, [x1, #-1]
    //     0xaa68a0: ldurb           w17, [x0, #-1]
    //     0xaa68a4: and             x16, x17, x16, lsr #2
    //     0xaa68a8: tst             x16, HEAP, lsr #32
    //     0xaa68ac: b.eq            #0xaa68b4
    //     0xaa68b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaa68b4: r0 = Stack()
    //     0xaa68b4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xaa68b8: r1 = Instance_AlignmentDirectional
    //     0xaa68b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xaa68bc: ldr             x1, [x1, #0xd08]
    // 0xaa68c0: StoreField: r0->field_f = r1
    //     0xaa68c0: stur            w1, [x0, #0xf]
    // 0xaa68c4: r1 = Instance_StackFit
    //     0xaa68c4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xaa68c8: ldr             x1, [x1, #0xfa8]
    // 0xaa68cc: ArrayStore: r0[0] = r1  ; List_4
    //     0xaa68cc: stur            w1, [x0, #0x17]
    // 0xaa68d0: r1 = Instance_Clip
    //     0xaa68d0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xaa68d4: ldr             x1, [x1, #0x7e0]
    // 0xaa68d8: StoreField: r0->field_1b = r1
    //     0xaa68d8: stur            w1, [x0, #0x1b]
    // 0xaa68dc: ldur            x1, [fp, #-0x20]
    // 0xaa68e0: StoreField: r0->field_b = r1
    //     0xaa68e0: stur            w1, [x0, #0xb]
    // 0xaa68e4: LeaveFrame
    //     0xaa68e4: mov             SP, fp
    //     0xaa68e8: ldp             fp, lr, [SP], #0x10
    // 0xaa68ec: ret
    //     0xaa68ec: ret             
    // 0xaa68f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa68f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa68f4: b               #0xaa627c
    // 0xaa68f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaa68f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaa68fc: SaveReg d0
    //     0xaa68fc: str             q0, [SP, #-0x10]!
    // 0xaa6900: stp             x0, x2, [SP, #-0x10]!
    // 0xaa6904: r0 = AllocateDouble()
    //     0xaa6904: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa6908: mov             x1, x0
    // 0xaa690c: ldp             x0, x2, [SP], #0x10
    // 0xaa6910: RestoreReg d0
    //     0xaa6910: ldr             q0, [SP], #0x10
    // 0xaa6914: b               #0xaa66b4
    // 0xaa6918: SaveReg d0
    //     0xaa6918: str             q0, [SP, #-0x10]!
    // 0xaa691c: SaveReg r2
    //     0xaa691c: str             x2, [SP, #-8]!
    // 0xaa6920: r0 = AllocateDouble()
    //     0xaa6920: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaa6924: RestoreReg r2
    //     0xaa6924: ldr             x2, [SP], #8
    // 0xaa6928: RestoreReg d0
    //     0xaa6928: ldr             q0, [SP], #0x10
    // 0xaa692c: b               #0xaa682c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa6930, size: 0x60
    // 0xaa6930: EnterFrame
    //     0xaa6930: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6934: mov             fp, SP
    // 0xaa6938: ldr             x0, [fp, #0x10]
    // 0xaa693c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa693c: ldur            w1, [x0, #0x17]
    // 0xaa6940: DecompressPointer r1
    //     0xaa6940: add             x1, x1, HEAP, lsl #32
    // 0xaa6944: CheckStackOverflow
    //     0xaa6944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6948: cmp             SP, x16
    //     0xaa694c: b.ls            #0xaa697c
    // 0xaa6950: LoadField: r0 = r1->field_f
    //     0xaa6950: ldur            w0, [x1, #0xf]
    // 0xaa6954: DecompressPointer r0
    //     0xaa6954: add             x0, x0, HEAP, lsl #32
    // 0xaa6958: LoadField: r1 = r0->field_23
    //     0xaa6958: ldur            w1, [x0, #0x23]
    // 0xaa695c: DecompressPointer r1
    //     0xaa695c: add             x1, x1, HEAP, lsl #32
    // 0xaa6960: r16 = Sentinel
    //     0xaa6960: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa6964: cmp             w1, w16
    // 0xaa6968: b.eq            #0xaa6984
    // 0xaa696c: r0 = nextPage()
    //     0xaa696c: bl              #0xaa5ed0  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xaa6970: LeaveFrame
    //     0xaa6970: mov             SP, fp
    //     0xaa6974: ldp             fp, lr, [SP], #0x10
    // 0xaa6978: ret
    //     0xaa6978: ret             
    // 0xaa697c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa697c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6980: b               #0xaa6950
    // 0xaa6984: r9 = _pageController
    //     0xaa6984: add             x9, PP, #0x51, lsl #12  ; [pp+0x51fe8] Field <_RatingReviewAllMediaOnTapImageState@1749268759._pageController@1749268759>: late (offset: 0x24)
    //     0xaa6988: ldr             x9, [x9, #0xfe8]
    // 0xaa698c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa698c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa6990, size: 0x60
    // 0xaa6990: EnterFrame
    //     0xaa6990: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6994: mov             fp, SP
    // 0xaa6998: ldr             x0, [fp, #0x10]
    // 0xaa699c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaa699c: ldur            w1, [x0, #0x17]
    // 0xaa69a0: DecompressPointer r1
    //     0xaa69a0: add             x1, x1, HEAP, lsl #32
    // 0xaa69a4: CheckStackOverflow
    //     0xaa69a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa69a8: cmp             SP, x16
    //     0xaa69ac: b.ls            #0xaa69dc
    // 0xaa69b0: LoadField: r0 = r1->field_f
    //     0xaa69b0: ldur            w0, [x1, #0xf]
    // 0xaa69b4: DecompressPointer r0
    //     0xaa69b4: add             x0, x0, HEAP, lsl #32
    // 0xaa69b8: LoadField: r1 = r0->field_23
    //     0xaa69b8: ldur            w1, [x0, #0x23]
    // 0xaa69bc: DecompressPointer r1
    //     0xaa69bc: add             x1, x1, HEAP, lsl #32
    // 0xaa69c0: r16 = Sentinel
    //     0xaa69c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaa69c4: cmp             w1, w16
    // 0xaa69c8: b.eq            #0xaa69e4
    // 0xaa69cc: r0 = previousPage()
    //     0xaa69cc: bl              #0xaa6010  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xaa69d0: LeaveFrame
    //     0xaa69d0: mov             SP, fp
    //     0xaa69d4: ldp             fp, lr, [SP], #0x10
    // 0xaa69d8: ret
    //     0xaa69d8: ret             
    // 0xaa69dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa69dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa69e0: b               #0xaa69b0
    // 0xaa69e4: r9 = _pageController
    //     0xaa69e4: add             x9, PP, #0x51, lsl #12  ; [pp+0x51fe8] Field <_RatingReviewAllMediaOnTapImageState@1749268759._pageController@1749268759>: late (offset: 0x24)
    //     0xaa69e8: ldr             x9, [x9, #0xfe8]
    // 0xaa69ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaa69ec: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaa6a74, size: 0x1a8
    // 0xaa6a74: EnterFrame
    //     0xaa6a74: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6a78: mov             fp, SP
    // 0xaa6a7c: AllocStack(0x18)
    //     0xaa6a7c: sub             SP, SP, #0x18
    // 0xaa6a80: SetupParameters()
    //     0xaa6a80: ldr             x0, [fp, #0x10]
    //     0xaa6a84: ldur            w3, [x0, #0x17]
    //     0xaa6a88: add             x3, x3, HEAP, lsl #32
    //     0xaa6a8c: stur            x3, [fp, #-0x18]
    // 0xaa6a90: CheckStackOverflow
    //     0xaa6a90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6a94: cmp             SP, x16
    //     0xaa6a98: b.ls            #0xaa6c0c
    // 0xaa6a9c: LoadField: r4 = r3->field_b
    //     0xaa6a9c: ldur            w4, [x3, #0xb]
    // 0xaa6aa0: DecompressPointer r4
    //     0xaa6aa0: add             x4, x4, HEAP, lsl #32
    // 0xaa6aa4: stur            x4, [fp, #-0x10]
    // 0xaa6aa8: LoadField: r5 = r4->field_f
    //     0xaa6aa8: ldur            w5, [x4, #0xf]
    // 0xaa6aac: DecompressPointer r5
    //     0xaa6aac: add             x5, x5, HEAP, lsl #32
    // 0xaa6ab0: stur            x5, [fp, #-8]
    // 0xaa6ab4: LoadField: r2 = r5->field_3b
    //     0xaa6ab4: ldur            w2, [x5, #0x3b]
    // 0xaa6ab8: DecompressPointer r2
    //     0xaa6ab8: add             x2, x2, HEAP, lsl #32
    // 0xaa6abc: LoadField: r0 = r3->field_f
    //     0xaa6abc: ldur            w0, [x3, #0xf]
    // 0xaa6ac0: DecompressPointer r0
    //     0xaa6ac0: add             x0, x0, HEAP, lsl #32
    // 0xaa6ac4: LoadField: r1 = r2->field_b
    //     0xaa6ac4: ldur            w1, [x2, #0xb]
    // 0xaa6ac8: r6 = LoadInt32Instr(r0)
    //     0xaa6ac8: sbfx            x6, x0, #1, #0x1f
    //     0xaa6acc: tbz             w0, #0, #0xaa6ad4
    //     0xaa6ad0: ldur            x6, [x0, #7]
    // 0xaa6ad4: r0 = LoadInt32Instr(r1)
    //     0xaa6ad4: sbfx            x0, x1, #1, #0x1f
    // 0xaa6ad8: mov             x1, x6
    // 0xaa6adc: cmp             x1, x0
    // 0xaa6ae0: b.hs            #0xaa6c14
    // 0xaa6ae4: LoadField: r0 = r2->field_f
    //     0xaa6ae4: ldur            w0, [x2, #0xf]
    // 0xaa6ae8: DecompressPointer r0
    //     0xaa6ae8: add             x0, x0, HEAP, lsl #32
    // 0xaa6aec: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xaa6aec: add             x16, x0, x6, lsl #2
    //     0xaa6af0: ldur            w1, [x16, #0xf]
    // 0xaa6af4: DecompressPointer r1
    //     0xaa6af4: add             x1, x1, HEAP, lsl #32
    // 0xaa6af8: r0 = LoadClassIdInstr(r1)
    //     0xaa6af8: ldur            x0, [x1, #-1]
    //     0xaa6afc: ubfx            x0, x0, #0xc, #0x14
    // 0xaa6b00: r2 = "parentIndex"
    //     0xaa6b00: add             x2, PP, #0x52, lsl #12  ; [pp+0x52048] "parentIndex"
    //     0xaa6b04: ldr             x2, [x2, #0x48]
    // 0xaa6b08: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa6b08: sub             lr, x0, #0xfe
    //     0xaa6b0c: ldr             lr, [x21, lr, lsl #3]
    //     0xaa6b10: blr             lr
    // 0xaa6b14: cmp             w0, NULL
    // 0xaa6b18: b.ne            #0xaa6b24
    // 0xaa6b1c: r2 = 0
    //     0xaa6b1c: movz            x2, #0
    // 0xaa6b20: b               #0xaa6b34
    // 0xaa6b24: r1 = LoadInt32Instr(r0)
    //     0xaa6b24: sbfx            x1, x0, #1, #0x1f
    //     0xaa6b28: tbz             w0, #0, #0xaa6b30
    //     0xaa6b2c: ldur            x1, [x0, #7]
    // 0xaa6b30: mov             x2, x1
    // 0xaa6b34: ldur            x0, [fp, #-0x18]
    // 0xaa6b38: ldur            x3, [fp, #-0x10]
    // 0xaa6b3c: ldur            x1, [fp, #-8]
    // 0xaa6b40: StoreField: r1->field_13 = r2
    //     0xaa6b40: stur            x2, [x1, #0x13]
    // 0xaa6b44: LoadField: r4 = r3->field_f
    //     0xaa6b44: ldur            w4, [x3, #0xf]
    // 0xaa6b48: DecompressPointer r4
    //     0xaa6b48: add             x4, x4, HEAP, lsl #32
    // 0xaa6b4c: stur            x4, [fp, #-8]
    // 0xaa6b50: LoadField: r2 = r4->field_3b
    //     0xaa6b50: ldur            w2, [x4, #0x3b]
    // 0xaa6b54: DecompressPointer r2
    //     0xaa6b54: add             x2, x2, HEAP, lsl #32
    // 0xaa6b58: LoadField: r1 = r0->field_f
    //     0xaa6b58: ldur            w1, [x0, #0xf]
    // 0xaa6b5c: DecompressPointer r1
    //     0xaa6b5c: add             x1, x1, HEAP, lsl #32
    // 0xaa6b60: LoadField: r0 = r2->field_b
    //     0xaa6b60: ldur            w0, [x2, #0xb]
    // 0xaa6b64: r5 = LoadInt32Instr(r1)
    //     0xaa6b64: sbfx            x5, x1, #1, #0x1f
    //     0xaa6b68: tbz             w1, #0, #0xaa6b70
    //     0xaa6b6c: ldur            x5, [x1, #7]
    // 0xaa6b70: r1 = LoadInt32Instr(r0)
    //     0xaa6b70: sbfx            x1, x0, #1, #0x1f
    // 0xaa6b74: mov             x0, x1
    // 0xaa6b78: mov             x1, x5
    // 0xaa6b7c: cmp             x1, x0
    // 0xaa6b80: b.hs            #0xaa6c18
    // 0xaa6b84: LoadField: r0 = r2->field_f
    //     0xaa6b84: ldur            w0, [x2, #0xf]
    // 0xaa6b88: DecompressPointer r0
    //     0xaa6b88: add             x0, x0, HEAP, lsl #32
    // 0xaa6b8c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xaa6b8c: add             x16, x0, x5, lsl #2
    //     0xaa6b90: ldur            w1, [x16, #0xf]
    // 0xaa6b94: DecompressPointer r1
    //     0xaa6b94: add             x1, x1, HEAP, lsl #32
    // 0xaa6b98: r0 = LoadClassIdInstr(r1)
    //     0xaa6b98: ldur            x0, [x1, #-1]
    //     0xaa6b9c: ubfx            x0, x0, #0xc, #0x14
    // 0xaa6ba0: r2 = "childIndex"
    //     0xaa6ba0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52050] "childIndex"
    //     0xaa6ba4: ldr             x2, [x2, #0x50]
    // 0xaa6ba8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xaa6ba8: sub             lr, x0, #0xfe
    //     0xaa6bac: ldr             lr, [x21, lr, lsl #3]
    //     0xaa6bb0: blr             lr
    // 0xaa6bb4: cmp             w0, NULL
    // 0xaa6bb8: b.ne            #0xaa6bc4
    // 0xaa6bbc: r2 = 0
    //     0xaa6bbc: movz            x2, #0
    // 0xaa6bc0: b               #0xaa6bd4
    // 0xaa6bc4: r1 = LoadInt32Instr(r0)
    //     0xaa6bc4: sbfx            x1, x0, #1, #0x1f
    //     0xaa6bc8: tbz             w0, #0, #0xaa6bd0
    //     0xaa6bcc: ldur            x1, [x0, #7]
    // 0xaa6bd0: mov             x2, x1
    // 0xaa6bd4: ldur            x0, [fp, #-0x10]
    // 0xaa6bd8: ldur            x1, [fp, #-8]
    // 0xaa6bdc: StoreField: r1->field_1b = r2
    //     0xaa6bdc: stur            x2, [x1, #0x1b]
    // 0xaa6be0: LoadField: r1 = r0->field_f
    //     0xaa6be0: ldur            w1, [x0, #0xf]
    // 0xaa6be4: DecompressPointer r1
    //     0xaa6be4: add             x1, x1, HEAP, lsl #32
    // 0xaa6be8: LoadField: r0 = r1->field_27
    //     0xaa6be8: ldur            w0, [x1, #0x27]
    // 0xaa6bec: DecompressPointer r0
    //     0xaa6bec: add             x0, x0, HEAP, lsl #32
    // 0xaa6bf0: mov             x1, x0
    // 0xaa6bf4: r2 = true
    //     0xaa6bf4: add             x2, NULL, #0x20  ; true
    // 0xaa6bf8: r0 = value=()
    //     0xaa6bf8: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xaa6bfc: r0 = Null
    //     0xaa6bfc: mov             x0, NULL
    // 0xaa6c00: LeaveFrame
    //     0xaa6c00: mov             SP, fp
    //     0xaa6c04: ldp             fp, lr, [SP], #0x10
    // 0xaa6c08: ret
    //     0xaa6c08: ret             
    // 0xaa6c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6c0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6c10: b               #0xaa6a9c
    // 0xaa6c14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaa6c14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaa6c18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaa6c18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xaa6c1c, size: 0x84
    // 0xaa6c1c: EnterFrame
    //     0xaa6c1c: stp             fp, lr, [SP, #-0x10]!
    //     0xaa6c20: mov             fp, SP
    // 0xaa6c24: AllocStack(0x10)
    //     0xaa6c24: sub             SP, SP, #0x10
    // 0xaa6c28: SetupParameters()
    //     0xaa6c28: ldr             x0, [fp, #0x18]
    //     0xaa6c2c: ldur            w1, [x0, #0x17]
    //     0xaa6c30: add             x1, x1, HEAP, lsl #32
    //     0xaa6c34: stur            x1, [fp, #-8]
    // 0xaa6c38: CheckStackOverflow
    //     0xaa6c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaa6c3c: cmp             SP, x16
    //     0xaa6c40: b.ls            #0xaa6c98
    // 0xaa6c44: r1 = 1
    //     0xaa6c44: movz            x1, #0x1
    // 0xaa6c48: r0 = AllocateContext()
    //     0xaa6c48: bl              #0x16f6108  ; AllocateContextStub
    // 0xaa6c4c: mov             x1, x0
    // 0xaa6c50: ldur            x0, [fp, #-8]
    // 0xaa6c54: StoreField: r1->field_b = r0
    //     0xaa6c54: stur            w0, [x1, #0xb]
    // 0xaa6c58: ldr             x2, [fp, #0x10]
    // 0xaa6c5c: StoreField: r1->field_f = r2
    //     0xaa6c5c: stur            w2, [x1, #0xf]
    // 0xaa6c60: LoadField: r3 = r0->field_f
    //     0xaa6c60: ldur            w3, [x0, #0xf]
    // 0xaa6c64: DecompressPointer r3
    //     0xaa6c64: add             x3, x3, HEAP, lsl #32
    // 0xaa6c68: mov             x2, x1
    // 0xaa6c6c: stur            x3, [fp, #-0x10]
    // 0xaa6c70: r1 = Function '<anonymous closure>':.
    //     0xaa6c70: add             x1, PP, #0x52, lsl #12  ; [pp+0x52040] AnonymousClosure: (0xaa6a74), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xaa6c74: ldr             x1, [x1, #0x40]
    // 0xaa6c78: r0 = AllocateClosure()
    //     0xaa6c78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaa6c7c: ldur            x1, [fp, #-0x10]
    // 0xaa6c80: mov             x2, x0
    // 0xaa6c84: r0 = setState()
    //     0xaa6c84: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaa6c88: r0 = Null
    //     0xaa6c88: mov             x0, NULL
    // 0xaa6c8c: LeaveFrame
    //     0xaa6c8c: mov             SP, fp
    //     0xaa6c90: ldp             fp, lr, [SP], #0x10
    // 0xaa6c94: ret
    //     0xaa6c94: ret             
    // 0xaa6c98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaa6c98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaa6c9c: b               #0xaa6c44
  }
  _ build(/* No info */) {
    // ** addr: 0xc12204, size: 0x2134
    // 0xc12204: EnterFrame
    //     0xc12204: stp             fp, lr, [SP, #-0x10]!
    //     0xc12208: mov             fp, SP
    // 0xc1220c: AllocStack(0x80)
    //     0xc1220c: sub             SP, SP, #0x80
    // 0xc12210: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc12210: stur            x1, [fp, #-8]
    //     0xc12214: stur            x2, [fp, #-0x10]
    // 0xc12218: CheckStackOverflow
    //     0xc12218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1221c: cmp             SP, x16
    //     0xc12220: b.ls            #0xc14284
    // 0xc12224: r1 = 2
    //     0xc12224: movz            x1, #0x2
    // 0xc12228: r0 = AllocateContext()
    //     0xc12228: bl              #0x16f6108  ; AllocateContextStub
    // 0xc1222c: mov             x3, x0
    // 0xc12230: ldur            x0, [fp, #-8]
    // 0xc12234: stur            x3, [fp, #-0x20]
    // 0xc12238: StoreField: r3->field_f = r0
    //     0xc12238: stur            w0, [x3, #0xf]
    // 0xc1223c: ldur            x1, [fp, #-0x10]
    // 0xc12240: StoreField: r3->field_13 = r1
    //     0xc12240: stur            w1, [x3, #0x13]
    // 0xc12244: LoadField: r4 = r0->field_23
    //     0xc12244: ldur            w4, [x0, #0x23]
    // 0xc12248: DecompressPointer r4
    //     0xc12248: add             x4, x4, HEAP, lsl #32
    // 0xc1224c: r16 = Sentinel
    //     0xc1224c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc12250: cmp             w4, w16
    // 0xc12254: b.eq            #0xc1428c
    // 0xc12258: stur            x4, [fp, #-0x18]
    // 0xc1225c: LoadField: r1 = r0->field_37
    //     0xc1225c: ldur            w1, [x0, #0x37]
    // 0xc12260: DecompressPointer r1
    //     0xc12260: add             x1, x1, HEAP, lsl #32
    // 0xc12264: LoadField: r5 = r1->field_b
    //     0xc12264: ldur            w5, [x1, #0xb]
    // 0xc12268: mov             x2, x3
    // 0xc1226c: stur            x5, [fp, #-0x10]
    // 0xc12270: r1 = Function '<anonymous closure>':.
    //     0xc12270: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f88] AnonymousClosure: (0xaa6c1c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xc12274: ldr             x1, [x1, #0xf88]
    // 0xc12278: r0 = AllocateClosure()
    //     0xc12278: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1227c: ldur            x2, [fp, #-0x20]
    // 0xc12280: r1 = Function '<anonymous closure>':.
    //     0xc12280: add             x1, PP, #0x51, lsl #12  ; [pp+0x51f90] AnonymousClosure: (0xaa6254), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xc12284: ldr             x1, [x1, #0xf90]
    // 0xc12288: stur            x0, [fp, #-0x28]
    // 0xc1228c: r0 = AllocateClosure()
    //     0xc1228c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc12290: stur            x0, [fp, #-0x30]
    // 0xc12294: r0 = PageView()
    //     0xc12294: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xc12298: stur            x0, [fp, #-0x38]
    // 0xc1229c: ldur            x16, [fp, #-0x18]
    // 0xc122a0: r30 = Instance_NeverScrollableScrollPhysics
    //     0xc122a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xc122a4: ldr             lr, [lr, #0x1c8]
    // 0xc122a8: stp             lr, x16, [SP]
    // 0xc122ac: mov             x1, x0
    // 0xc122b0: ldur            x2, [fp, #-0x30]
    // 0xc122b4: ldur            x3, [fp, #-0x10]
    // 0xc122b8: ldur            x5, [fp, #-0x28]
    // 0xc122bc: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x4, physics, 0x5, null]
    //     0xc122bc: add             x4, PP, #0x51, lsl #12  ; [pp+0x51f98] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x4, "physics", 0x5, Null]
    //     0xc122c0: ldr             x4, [x4, #0xf98]
    // 0xc122c4: r0 = PageView.builder()
    //     0xc122c4: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xc122c8: ldur            x3, [fp, #-8]
    // 0xc122cc: LoadField: r0 = r3->field_b
    //     0xc122cc: ldur            w0, [x3, #0xb]
    // 0xc122d0: DecompressPointer r0
    //     0xc122d0: add             x0, x0, HEAP, lsl #32
    // 0xc122d4: cmp             w0, NULL
    // 0xc122d8: b.eq            #0xc14298
    // 0xc122dc: LoadField: r2 = r0->field_f
    //     0xc122dc: ldur            w2, [x0, #0xf]
    // 0xc122e0: DecompressPointer r2
    //     0xc122e0: add             x2, x2, HEAP, lsl #32
    // 0xc122e4: LoadField: r4 = r3->field_13
    //     0xc122e4: ldur            x4, [x3, #0x13]
    // 0xc122e8: LoadField: r0 = r2->field_b
    //     0xc122e8: ldur            w0, [x2, #0xb]
    // 0xc122ec: r1 = LoadInt32Instr(r0)
    //     0xc122ec: sbfx            x1, x0, #1, #0x1f
    // 0xc122f0: mov             x0, x1
    // 0xc122f4: mov             x1, x4
    // 0xc122f8: cmp             x1, x0
    // 0xc122fc: b.hs            #0xc1429c
    // 0xc12300: LoadField: r0 = r2->field_f
    //     0xc12300: ldur            w0, [x2, #0xf]
    // 0xc12304: DecompressPointer r0
    //     0xc12304: add             x0, x0, HEAP, lsl #32
    // 0xc12308: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc12308: add             x16, x0, x4, lsl #2
    //     0xc1230c: ldur            w1, [x16, #0xf]
    // 0xc12310: DecompressPointer r1
    //     0xc12310: add             x1, x1, HEAP, lsl #32
    // 0xc12314: LoadField: r0 = r1->field_1b
    //     0xc12314: ldur            w0, [x1, #0x1b]
    // 0xc12318: DecompressPointer r0
    //     0xc12318: add             x0, x0, HEAP, lsl #32
    // 0xc1231c: LoadField: r4 = r0->field_b
    //     0xc1231c: ldur            w4, [x0, #0xb]
    // 0xc12320: ldur            x2, [fp, #-0x20]
    // 0xc12324: stur            x4, [fp, #-0x10]
    // 0xc12328: r1 = Function '<anonymous closure>':.
    //     0xc12328: add             x1, PP, #0x51, lsl #12  ; [pp+0x51fa0] AnonymousClosure: (0xaa55c4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xc1232c: ldr             x1, [x1, #0xfa0]
    // 0xc12330: r0 = AllocateClosure()
    //     0xc12330: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc12334: mov             x3, x0
    // 0xc12338: ldur            x0, [fp, #-0x10]
    // 0xc1233c: stur            x3, [fp, #-0x18]
    // 0xc12340: r2 = LoadInt32Instr(r0)
    //     0xc12340: sbfx            x2, x0, #1, #0x1f
    // 0xc12344: r1 = <Widget>
    //     0xc12344: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc12348: r0 = _GrowableList()
    //     0xc12348: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc1234c: mov             x1, x0
    // 0xc12350: stur            x1, [fp, #-0x10]
    // 0xc12354: r2 = 0
    //     0xc12354: movz            x2, #0
    // 0xc12358: stur            x2, [fp, #-0x40]
    // 0xc1235c: CheckStackOverflow
    //     0xc1235c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc12360: cmp             SP, x16
    //     0xc12364: b.ls            #0xc142a0
    // 0xc12368: LoadField: r0 = r1->field_b
    //     0xc12368: ldur            w0, [x1, #0xb]
    // 0xc1236c: r3 = LoadInt32Instr(r0)
    //     0xc1236c: sbfx            x3, x0, #1, #0x1f
    // 0xc12370: cmp             x2, x3
    // 0xc12374: b.ge            #0xc12438
    // 0xc12378: lsl             x0, x2, #1
    // 0xc1237c: ldur            x16, [fp, #-0x18]
    // 0xc12380: stp             x0, x16, [SP]
    // 0xc12384: ldur            x0, [fp, #-0x18]
    // 0xc12388: ClosureCall
    //     0xc12388: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xc1238c: ldur            x2, [x0, #0x1f]
    //     0xc12390: blr             x2
    // 0xc12394: mov             x3, x0
    // 0xc12398: r2 = Null
    //     0xc12398: mov             x2, NULL
    // 0xc1239c: r1 = Null
    //     0xc1239c: mov             x1, NULL
    // 0xc123a0: stur            x3, [fp, #-0x28]
    // 0xc123a4: r4 = 60
    //     0xc123a4: movz            x4, #0x3c
    // 0xc123a8: branchIfSmi(r0, 0xc123b4)
    //     0xc123a8: tbz             w0, #0, #0xc123b4
    // 0xc123ac: r4 = LoadClassIdInstr(r0)
    //     0xc123ac: ldur            x4, [x0, #-1]
    //     0xc123b0: ubfx            x4, x4, #0xc, #0x14
    // 0xc123b4: sub             x4, x4, #0xe60
    // 0xc123b8: cmp             x4, #0x464
    // 0xc123bc: b.ls            #0xc123d4
    // 0xc123c0: r8 = Widget
    //     0xc123c0: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xc123c4: ldr             x8, [x8, #0xe68]
    // 0xc123c8: r3 = Null
    //     0xc123c8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51fa8] Null
    //     0xc123cc: ldr             x3, [x3, #0xfa8]
    // 0xc123d0: r0 = Widget()
    //     0xc123d0: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xc123d4: ldur            x3, [fp, #-0x10]
    // 0xc123d8: LoadField: r0 = r3->field_b
    //     0xc123d8: ldur            w0, [x3, #0xb]
    // 0xc123dc: r1 = LoadInt32Instr(r0)
    //     0xc123dc: sbfx            x1, x0, #1, #0x1f
    // 0xc123e0: mov             x0, x1
    // 0xc123e4: ldur            x1, [fp, #-0x40]
    // 0xc123e8: cmp             x1, x0
    // 0xc123ec: b.hs            #0xc142a8
    // 0xc123f0: LoadField: r1 = r3->field_f
    //     0xc123f0: ldur            w1, [x3, #0xf]
    // 0xc123f4: DecompressPointer r1
    //     0xc123f4: add             x1, x1, HEAP, lsl #32
    // 0xc123f8: ldur            x0, [fp, #-0x28]
    // 0xc123fc: ldur            x2, [fp, #-0x40]
    // 0xc12400: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc12400: add             x25, x1, x2, lsl #2
    //     0xc12404: add             x25, x25, #0xf
    //     0xc12408: str             w0, [x25]
    //     0xc1240c: tbz             w0, #0, #0xc12428
    //     0xc12410: ldurb           w16, [x1, #-1]
    //     0xc12414: ldurb           w17, [x0, #-1]
    //     0xc12418: and             x16, x17, x16, lsr #2
    //     0xc1241c: tst             x16, HEAP, lsr #32
    //     0xc12420: b.eq            #0xc12428
    //     0xc12424: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc12428: add             x0, x2, #1
    // 0xc1242c: mov             x2, x0
    // 0xc12430: mov             x1, x3
    // 0xc12434: b               #0xc12358
    // 0xc12438: ldur            x0, [fp, #-8]
    // 0xc1243c: mov             x3, x1
    // 0xc12440: ldur            x1, [fp, #-0x38]
    // 0xc12444: r0 = Row()
    //     0xc12444: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc12448: mov             x2, x0
    // 0xc1244c: r0 = Instance_Axis
    //     0xc1244c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc12450: stur            x2, [fp, #-0x18]
    // 0xc12454: StoreField: r2->field_f = r0
    //     0xc12454: stur            w0, [x2, #0xf]
    // 0xc12458: r1 = Instance_MainAxisAlignment
    //     0xc12458: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc1245c: ldr             x1, [x1, #0xab0]
    // 0xc12460: StoreField: r2->field_13 = r1
    //     0xc12460: stur            w1, [x2, #0x13]
    // 0xc12464: r3 = Instance_MainAxisSize
    //     0xc12464: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc12468: ldr             x3, [x3, #0xa10]
    // 0xc1246c: ArrayStore: r2[0] = r3  ; List_4
    //     0xc1246c: stur            w3, [x2, #0x17]
    // 0xc12470: r4 = Instance_CrossAxisAlignment
    //     0xc12470: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc12474: ldr             x4, [x4, #0xa18]
    // 0xc12478: StoreField: r2->field_1b = r4
    //     0xc12478: stur            w4, [x2, #0x1b]
    // 0xc1247c: r5 = Instance_VerticalDirection
    //     0xc1247c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc12480: ldr             x5, [x5, #0xa20]
    // 0xc12484: StoreField: r2->field_23 = r5
    //     0xc12484: stur            w5, [x2, #0x23]
    // 0xc12488: r6 = Instance_Clip
    //     0xc12488: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc1248c: ldr             x6, [x6, #0x38]
    // 0xc12490: StoreField: r2->field_2b = r6
    //     0xc12490: stur            w6, [x2, #0x2b]
    // 0xc12494: StoreField: r2->field_2f = rZR
    //     0xc12494: stur            xzr, [x2, #0x2f]
    // 0xc12498: ldur            x1, [fp, #-0x10]
    // 0xc1249c: StoreField: r2->field_b = r1
    //     0xc1249c: stur            w1, [x2, #0xb]
    // 0xc124a0: r1 = <StackParentData>
    //     0xc124a0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc124a4: ldr             x1, [x1, #0x8e0]
    // 0xc124a8: r0 = Positioned()
    //     0xc124a8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc124ac: mov             x3, x0
    // 0xc124b0: r0 = 12.000000
    //     0xc124b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc124b4: ldr             x0, [x0, #0x9e8]
    // 0xc124b8: stur            x3, [fp, #-0x10]
    // 0xc124bc: StoreField: r3->field_13 = r0
    //     0xc124bc: stur            w0, [x3, #0x13]
    // 0xc124c0: ArrayStore: r3[0] = r0  ; List_4
    //     0xc124c0: stur            w0, [x3, #0x17]
    // 0xc124c4: ldur            x1, [fp, #-0x18]
    // 0xc124c8: StoreField: r3->field_b = r1
    //     0xc124c8: stur            w1, [x3, #0xb]
    // 0xc124cc: r1 = Null
    //     0xc124cc: mov             x1, NULL
    // 0xc124d0: r2 = 4
    //     0xc124d0: movz            x2, #0x4
    // 0xc124d4: r0 = AllocateArray()
    //     0xc124d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc124d8: mov             x2, x0
    // 0xc124dc: ldur            x0, [fp, #-0x38]
    // 0xc124e0: stur            x2, [fp, #-0x18]
    // 0xc124e4: StoreField: r2->field_f = r0
    //     0xc124e4: stur            w0, [x2, #0xf]
    // 0xc124e8: ldur            x0, [fp, #-0x10]
    // 0xc124ec: StoreField: r2->field_13 = r0
    //     0xc124ec: stur            w0, [x2, #0x13]
    // 0xc124f0: r1 = <Widget>
    //     0xc124f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc124f4: r0 = AllocateGrowableArray()
    //     0xc124f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc124f8: mov             x1, x0
    // 0xc124fc: ldur            x0, [fp, #-0x18]
    // 0xc12500: stur            x1, [fp, #-0x10]
    // 0xc12504: StoreField: r1->field_f = r0
    //     0xc12504: stur            w0, [x1, #0xf]
    // 0xc12508: r2 = 4
    //     0xc12508: movz            x2, #0x4
    // 0xc1250c: StoreField: r1->field_b = r2
    //     0xc1250c: stur            w2, [x1, #0xb]
    // 0xc12510: ldur            x3, [fp, #-8]
    // 0xc12514: LoadField: r0 = r3->field_b
    //     0xc12514: ldur            w0, [x3, #0xb]
    // 0xc12518: DecompressPointer r0
    //     0xc12518: add             x0, x0, HEAP, lsl #32
    // 0xc1251c: cmp             w0, NULL
    // 0xc12520: b.eq            #0xc142ac
    // 0xc12524: LoadField: r4 = r0->field_b
    //     0xc12524: ldur            w4, [x0, #0xb]
    // 0xc12528: DecompressPointer r4
    //     0xc12528: add             x4, x4, HEAP, lsl #32
    // 0xc1252c: r0 = LoadClassIdInstr(r4)
    //     0xc1252c: ldur            x0, [x4, #-1]
    //     0xc12530: ubfx            x0, x0, #0xc, #0x14
    // 0xc12534: r16 = "direct_image"
    //     0xc12534: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xc12538: ldr             x16, [x16, #0xc98]
    // 0xc1253c: stp             x16, x4, [SP]
    // 0xc12540: mov             lr, x0
    // 0xc12544: ldr             lr, [x21, lr, lsl #3]
    // 0xc12548: blr             lr
    // 0xc1254c: tbz             w0, #4, #0xc12660
    // 0xc12550: ldur            x1, [fp, #-0x10]
    // 0xc12554: r0 = InkWell()
    //     0xc12554: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc12558: mov             x3, x0
    // 0xc1255c: r0 = Instance_Icon
    //     0xc1255c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51fb8] Obj!Icon@d665b1
    //     0xc12560: ldr             x0, [x0, #0xfb8]
    // 0xc12564: stur            x3, [fp, #-0x18]
    // 0xc12568: StoreField: r3->field_b = r0
    //     0xc12568: stur            w0, [x3, #0xb]
    // 0xc1256c: r1 = Function '<anonymous closure>':.
    //     0xc1256c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51fc0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xc12570: ldr             x1, [x1, #0xfc0]
    // 0xc12574: r2 = Null
    //     0xc12574: mov             x2, NULL
    // 0xc12578: r0 = AllocateClosure()
    //     0xc12578: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1257c: mov             x1, x0
    // 0xc12580: ldur            x0, [fp, #-0x18]
    // 0xc12584: StoreField: r0->field_f = r1
    //     0xc12584: stur            w1, [x0, #0xf]
    // 0xc12588: r2 = true
    //     0xc12588: add             x2, NULL, #0x20  ; true
    // 0xc1258c: StoreField: r0->field_43 = r2
    //     0xc1258c: stur            w2, [x0, #0x43]
    // 0xc12590: r3 = Instance_BoxShape
    //     0xc12590: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc12594: ldr             x3, [x3, #0x80]
    // 0xc12598: StoreField: r0->field_47 = r3
    //     0xc12598: stur            w3, [x0, #0x47]
    // 0xc1259c: StoreField: r0->field_6f = r2
    //     0xc1259c: stur            w2, [x0, #0x6f]
    // 0xc125a0: r4 = false
    //     0xc125a0: add             x4, NULL, #0x30  ; false
    // 0xc125a4: StoreField: r0->field_73 = r4
    //     0xc125a4: stur            w4, [x0, #0x73]
    // 0xc125a8: StoreField: r0->field_83 = r2
    //     0xc125a8: stur            w2, [x0, #0x83]
    // 0xc125ac: StoreField: r0->field_7b = r4
    //     0xc125ac: stur            w4, [x0, #0x7b]
    // 0xc125b0: r1 = <StackParentData>
    //     0xc125b0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc125b4: ldr             x1, [x1, #0x8e0]
    // 0xc125b8: r0 = Positioned()
    //     0xc125b8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc125bc: mov             x2, x0
    // 0xc125c0: r0 = 12.000000
    //     0xc125c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc125c4: ldr             x0, [x0, #0x9e8]
    // 0xc125c8: stur            x2, [fp, #-0x28]
    // 0xc125cc: StoreField: r2->field_13 = r0
    //     0xc125cc: stur            w0, [x2, #0x13]
    // 0xc125d0: r3 = 24.000000
    //     0xc125d0: add             x3, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xc125d4: ldr             x3, [x3, #0xba8]
    // 0xc125d8: ArrayStore: r2[0] = r3  ; List_4
    //     0xc125d8: stur            w3, [x2, #0x17]
    // 0xc125dc: ldur            x1, [fp, #-0x18]
    // 0xc125e0: StoreField: r2->field_b = r1
    //     0xc125e0: stur            w1, [x2, #0xb]
    // 0xc125e4: ldur            x4, [fp, #-0x10]
    // 0xc125e8: LoadField: r1 = r4->field_b
    //     0xc125e8: ldur            w1, [x4, #0xb]
    // 0xc125ec: LoadField: r5 = r4->field_f
    //     0xc125ec: ldur            w5, [x4, #0xf]
    // 0xc125f0: DecompressPointer r5
    //     0xc125f0: add             x5, x5, HEAP, lsl #32
    // 0xc125f4: LoadField: r6 = r5->field_b
    //     0xc125f4: ldur            w6, [x5, #0xb]
    // 0xc125f8: r5 = LoadInt32Instr(r1)
    //     0xc125f8: sbfx            x5, x1, #1, #0x1f
    // 0xc125fc: stur            x5, [fp, #-0x40]
    // 0xc12600: r1 = LoadInt32Instr(r6)
    //     0xc12600: sbfx            x1, x6, #1, #0x1f
    // 0xc12604: cmp             x5, x1
    // 0xc12608: b.ne            #0xc12614
    // 0xc1260c: mov             x1, x4
    // 0xc12610: r0 = _growToNextCapacity()
    //     0xc12610: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc12614: ldur            x2, [fp, #-0x10]
    // 0xc12618: ldur            x3, [fp, #-0x40]
    // 0xc1261c: add             x0, x3, #1
    // 0xc12620: lsl             x1, x0, #1
    // 0xc12624: StoreField: r2->field_b = r1
    //     0xc12624: stur            w1, [x2, #0xb]
    // 0xc12628: LoadField: r1 = r2->field_f
    //     0xc12628: ldur            w1, [x2, #0xf]
    // 0xc1262c: DecompressPointer r1
    //     0xc1262c: add             x1, x1, HEAP, lsl #32
    // 0xc12630: ldur            x0, [fp, #-0x28]
    // 0xc12634: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc12634: add             x25, x1, x3, lsl #2
    //     0xc12638: add             x25, x25, #0xf
    //     0xc1263c: str             w0, [x25]
    //     0xc12640: tbz             w0, #0, #0xc1265c
    //     0xc12644: ldurb           w16, [x1, #-1]
    //     0xc12648: ldurb           w17, [x0, #-1]
    //     0xc1264c: and             x16, x17, x16, lsr #2
    //     0xc12650: tst             x16, HEAP, lsr #32
    //     0xc12654: b.eq            #0xc1265c
    //     0xc12658: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc1265c: b               #0xc12664
    // 0xc12660: ldur            x2, [fp, #-0x10]
    // 0xc12664: r0 = InkWell()
    //     0xc12664: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc12668: mov             x3, x0
    // 0xc1266c: r0 = Instance_Icon
    //     0xc1266c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51fc8] Obj!Icon@d66571
    //     0xc12670: ldr             x0, [x0, #0xfc8]
    // 0xc12674: stur            x3, [fp, #-0x18]
    // 0xc12678: StoreField: r3->field_b = r0
    //     0xc12678: stur            w0, [x3, #0xb]
    // 0xc1267c: ldur            x2, [fp, #-0x20]
    // 0xc12680: r1 = Function '<anonymous closure>':.
    //     0xc12680: add             x1, PP, #0x51, lsl #12  ; [pp+0x51fd0] AnonymousClosure: (0xaa54dc), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xc12684: ldr             x1, [x1, #0xfd0]
    // 0xc12688: r0 = AllocateClosure()
    //     0xc12688: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1268c: mov             x1, x0
    // 0xc12690: ldur            x0, [fp, #-0x18]
    // 0xc12694: StoreField: r0->field_f = r1
    //     0xc12694: stur            w1, [x0, #0xf]
    // 0xc12698: r2 = true
    //     0xc12698: add             x2, NULL, #0x20  ; true
    // 0xc1269c: StoreField: r0->field_43 = r2
    //     0xc1269c: stur            w2, [x0, #0x43]
    // 0xc126a0: r3 = Instance_BoxShape
    //     0xc126a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc126a4: ldr             x3, [x3, #0x80]
    // 0xc126a8: StoreField: r0->field_47 = r3
    //     0xc126a8: stur            w3, [x0, #0x47]
    // 0xc126ac: StoreField: r0->field_6f = r2
    //     0xc126ac: stur            w2, [x0, #0x6f]
    // 0xc126b0: r4 = false
    //     0xc126b0: add             x4, NULL, #0x30  ; false
    // 0xc126b4: StoreField: r0->field_73 = r4
    //     0xc126b4: stur            w4, [x0, #0x73]
    // 0xc126b8: StoreField: r0->field_83 = r2
    //     0xc126b8: stur            w2, [x0, #0x83]
    // 0xc126bc: StoreField: r0->field_7b = r4
    //     0xc126bc: stur            w4, [x0, #0x7b]
    // 0xc126c0: r1 = <StackParentData>
    //     0xc126c0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc126c4: ldr             x1, [x1, #0x8e0]
    // 0xc126c8: r0 = Positioned()
    //     0xc126c8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc126cc: mov             x2, x0
    // 0xc126d0: r0 = 24.000000
    //     0xc126d0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xc126d4: ldr             x0, [x0, #0xba8]
    // 0xc126d8: stur            x2, [fp, #-0x28]
    // 0xc126dc: ArrayStore: r2[0] = r0  ; List_4
    //     0xc126dc: stur            w0, [x2, #0x17]
    // 0xc126e0: r0 = 12.000000
    //     0xc126e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc126e4: ldr             x0, [x0, #0x9e8]
    // 0xc126e8: StoreField: r2->field_1b = r0
    //     0xc126e8: stur            w0, [x2, #0x1b]
    // 0xc126ec: ldur            x0, [fp, #-0x18]
    // 0xc126f0: StoreField: r2->field_b = r0
    //     0xc126f0: stur            w0, [x2, #0xb]
    // 0xc126f4: ldur            x0, [fp, #-0x10]
    // 0xc126f8: LoadField: r1 = r0->field_b
    //     0xc126f8: ldur            w1, [x0, #0xb]
    // 0xc126fc: LoadField: r3 = r0->field_f
    //     0xc126fc: ldur            w3, [x0, #0xf]
    // 0xc12700: DecompressPointer r3
    //     0xc12700: add             x3, x3, HEAP, lsl #32
    // 0xc12704: LoadField: r4 = r3->field_b
    //     0xc12704: ldur            w4, [x3, #0xb]
    // 0xc12708: r3 = LoadInt32Instr(r1)
    //     0xc12708: sbfx            x3, x1, #1, #0x1f
    // 0xc1270c: stur            x3, [fp, #-0x40]
    // 0xc12710: r1 = LoadInt32Instr(r4)
    //     0xc12710: sbfx            x1, x4, #1, #0x1f
    // 0xc12714: cmp             x3, x1
    // 0xc12718: b.ne            #0xc12724
    // 0xc1271c: mov             x1, x0
    // 0xc12720: r0 = _growToNextCapacity()
    //     0xc12720: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc12724: ldur            x4, [fp, #-8]
    // 0xc12728: ldur            x2, [fp, #-0x10]
    // 0xc1272c: ldur            x3, [fp, #-0x40]
    // 0xc12730: add             x0, x3, #1
    // 0xc12734: lsl             x1, x0, #1
    // 0xc12738: StoreField: r2->field_b = r1
    //     0xc12738: stur            w1, [x2, #0xb]
    // 0xc1273c: LoadField: r1 = r2->field_f
    //     0xc1273c: ldur            w1, [x2, #0xf]
    // 0xc12740: DecompressPointer r1
    //     0xc12740: add             x1, x1, HEAP, lsl #32
    // 0xc12744: ldur            x0, [fp, #-0x28]
    // 0xc12748: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc12748: add             x25, x1, x3, lsl #2
    //     0xc1274c: add             x25, x25, #0xf
    //     0xc12750: str             w0, [x25]
    //     0xc12754: tbz             w0, #0, #0xc12770
    //     0xc12758: ldurb           w16, [x1, #-1]
    //     0xc1275c: ldurb           w17, [x0, #-1]
    //     0xc12760: and             x16, x17, x16, lsr #2
    //     0xc12764: tst             x16, HEAP, lsr #32
    //     0xc12768: b.eq            #0xc12770
    //     0xc1276c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc12770: LoadField: r0 = r4->field_2b
    //     0xc12770: ldur            w0, [x4, #0x2b]
    // 0xc12774: DecompressPointer r0
    //     0xc12774: add             x0, x0, HEAP, lsl #32
    // 0xc12778: tbnz            w0, #4, #0xc13294
    // 0xc1277c: ldur            x0, [fp, #-0x20]
    // 0xc12780: LoadField: r1 = r0->field_13
    //     0xc12780: ldur            w1, [x0, #0x13]
    // 0xc12784: DecompressPointer r1
    //     0xc12784: add             x1, x1, HEAP, lsl #32
    // 0xc12788: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc12788: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc1278c: r0 = _of()
    //     0xc1278c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xc12790: LoadField: r1 = r0->field_7
    //     0xc12790: ldur            w1, [x0, #7]
    // 0xc12794: DecompressPointer r1
    //     0xc12794: add             x1, x1, HEAP, lsl #32
    // 0xc12798: LoadField: d1 = r1->field_7
    //     0xc12798: ldur            d1, [x1, #7]
    // 0xc1279c: stur            d1, [fp, #-0x58]
    // 0xc127a0: r1 = Instance_Color
    //     0xc127a0: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc127a4: d0 = 0.700000
    //     0xc127a4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc127a8: ldr             d0, [x17, #0xf48]
    // 0xc127ac: r0 = withOpacity()
    //     0xc127ac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc127b0: stur            x0, [fp, #-0x18]
    // 0xc127b4: r0 = BoxDecoration()
    //     0xc127b4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc127b8: mov             x2, x0
    // 0xc127bc: ldur            x0, [fp, #-0x18]
    // 0xc127c0: stur            x2, [fp, #-0x28]
    // 0xc127c4: StoreField: r2->field_7 = r0
    //     0xc127c4: stur            w0, [x2, #7]
    // 0xc127c8: r0 = Instance_BorderRadius
    //     0xc127c8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xc127cc: ldr             x0, [x0, #0xf70]
    // 0xc127d0: StoreField: r2->field_13 = r0
    //     0xc127d0: stur            w0, [x2, #0x13]
    // 0xc127d4: r0 = Instance_BoxShape
    //     0xc127d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc127d8: ldr             x0, [x0, #0x80]
    // 0xc127dc: StoreField: r2->field_23 = r0
    //     0xc127dc: stur            w0, [x2, #0x23]
    // 0xc127e0: r1 = Instance_Color
    //     0xc127e0: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc127e4: d0 = 0.500000
    //     0xc127e4: fmov            d0, #0.50000000
    // 0xc127e8: r0 = withOpacity()
    //     0xc127e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc127ec: stur            x0, [fp, #-0x18]
    // 0xc127f0: r0 = BoxDecoration()
    //     0xc127f0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc127f4: mov             x2, x0
    // 0xc127f8: ldur            x0, [fp, #-0x18]
    // 0xc127fc: stur            x2, [fp, #-0x30]
    // 0xc12800: StoreField: r2->field_7 = r0
    //     0xc12800: stur            w0, [x2, #7]
    // 0xc12804: r3 = Instance_BoxShape
    //     0xc12804: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xc12808: ldr             x3, [x3, #0x970]
    // 0xc1280c: StoreField: r2->field_23 = r3
    //     0xc1280c: stur            w3, [x2, #0x23]
    // 0xc12810: ldur            x4, [fp, #-8]
    // 0xc12814: LoadField: r0 = r4->field_b
    //     0xc12814: ldur            w0, [x4, #0xb]
    // 0xc12818: DecompressPointer r0
    //     0xc12818: add             x0, x0, HEAP, lsl #32
    // 0xc1281c: cmp             w0, NULL
    // 0xc12820: b.eq            #0xc142b0
    // 0xc12824: LoadField: r5 = r0->field_f
    //     0xc12824: ldur            w5, [x0, #0xf]
    // 0xc12828: DecompressPointer r5
    //     0xc12828: add             x5, x5, HEAP, lsl #32
    // 0xc1282c: LoadField: r6 = r4->field_13
    //     0xc1282c: ldur            x6, [x4, #0x13]
    // 0xc12830: LoadField: r0 = r5->field_b
    //     0xc12830: ldur            w0, [x5, #0xb]
    // 0xc12834: r1 = LoadInt32Instr(r0)
    //     0xc12834: sbfx            x1, x0, #1, #0x1f
    // 0xc12838: mov             x0, x1
    // 0xc1283c: mov             x1, x6
    // 0xc12840: cmp             x1, x0
    // 0xc12844: b.hs            #0xc142b4
    // 0xc12848: LoadField: r0 = r5->field_f
    //     0xc12848: ldur            w0, [x5, #0xf]
    // 0xc1284c: DecompressPointer r0
    //     0xc1284c: add             x0, x0, HEAP, lsl #32
    // 0xc12850: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xc12850: add             x16, x0, x6, lsl #2
    //     0xc12854: ldur            w1, [x16, #0xf]
    // 0xc12858: DecompressPointer r1
    //     0xc12858: add             x1, x1, HEAP, lsl #32
    // 0xc1285c: LoadField: r0 = r1->field_7
    //     0xc1285c: ldur            w0, [x1, #7]
    // 0xc12860: DecompressPointer r0
    //     0xc12860: add             x0, x0, HEAP, lsl #32
    // 0xc12864: cmp             w0, NULL
    // 0xc12868: b.ne            #0xc12874
    // 0xc1286c: r0 = Null
    //     0xc1286c: mov             x0, NULL
    // 0xc12870: b               #0xc12898
    // 0xc12874: stp             xzr, x0, [SP]
    // 0xc12878: r0 = []()
    //     0xc12878: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xc1287c: r1 = LoadClassIdInstr(r0)
    //     0xc1287c: ldur            x1, [x0, #-1]
    //     0xc12880: ubfx            x1, x1, #0xc, #0x14
    // 0xc12884: str             x0, [SP]
    // 0xc12888: mov             x0, x1
    // 0xc1288c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc1288c: sub             lr, x0, #1, lsl #12
    //     0xc12890: ldr             lr, [x21, lr, lsl #3]
    //     0xc12894: blr             lr
    // 0xc12898: cmp             w0, NULL
    // 0xc1289c: b.ne            #0xc128a8
    // 0xc128a0: r3 = ""
    //     0xc128a0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc128a4: b               #0xc128ac
    // 0xc128a8: mov             x3, x0
    // 0xc128ac: ldur            x0, [fp, #-8]
    // 0xc128b0: ldur            x2, [fp, #-0x20]
    // 0xc128b4: stur            x3, [fp, #-0x18]
    // 0xc128b8: LoadField: r1 = r2->field_13
    //     0xc128b8: ldur            w1, [x2, #0x13]
    // 0xc128bc: DecompressPointer r1
    //     0xc128bc: add             x1, x1, HEAP, lsl #32
    // 0xc128c0: r0 = of()
    //     0xc128c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc128c4: LoadField: r1 = r0->field_87
    //     0xc128c4: ldur            w1, [x0, #0x87]
    // 0xc128c8: DecompressPointer r1
    //     0xc128c8: add             x1, x1, HEAP, lsl #32
    // 0xc128cc: LoadField: r0 = r1->field_7
    //     0xc128cc: ldur            w0, [x1, #7]
    // 0xc128d0: DecompressPointer r0
    //     0xc128d0: add             x0, x0, HEAP, lsl #32
    // 0xc128d4: r16 = 16.000000
    //     0xc128d4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc128d8: ldr             x16, [x16, #0x188]
    // 0xc128dc: r30 = Instance_Color
    //     0xc128dc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc128e0: stp             lr, x16, [SP]
    // 0xc128e4: mov             x1, x0
    // 0xc128e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc128e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc128ec: ldr             x4, [x4, #0xaa0]
    // 0xc128f0: r0 = copyWith()
    //     0xc128f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc128f4: stur            x0, [fp, #-0x38]
    // 0xc128f8: r0 = Text()
    //     0xc128f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc128fc: mov             x1, x0
    // 0xc12900: ldur            x0, [fp, #-0x18]
    // 0xc12904: stur            x1, [fp, #-0x48]
    // 0xc12908: StoreField: r1->field_b = r0
    //     0xc12908: stur            w0, [x1, #0xb]
    // 0xc1290c: ldur            x0, [fp, #-0x38]
    // 0xc12910: StoreField: r1->field_13 = r0
    //     0xc12910: stur            w0, [x1, #0x13]
    // 0xc12914: r0 = Center()
    //     0xc12914: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc12918: mov             x1, x0
    // 0xc1291c: r0 = Instance_Alignment
    //     0xc1291c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc12920: ldr             x0, [x0, #0xb10]
    // 0xc12924: stur            x1, [fp, #-0x18]
    // 0xc12928: StoreField: r1->field_f = r0
    //     0xc12928: stur            w0, [x1, #0xf]
    // 0xc1292c: ldur            x2, [fp, #-0x48]
    // 0xc12930: StoreField: r1->field_b = r2
    //     0xc12930: stur            w2, [x1, #0xb]
    // 0xc12934: r0 = Container()
    //     0xc12934: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc12938: stur            x0, [fp, #-0x38]
    // 0xc1293c: r16 = 34.000000
    //     0xc1293c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc12940: ldr             x16, [x16, #0x978]
    // 0xc12944: r30 = 34.000000
    //     0xc12944: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc12948: ldr             lr, [lr, #0x978]
    // 0xc1294c: stp             lr, x16, [SP, #0x18]
    // 0xc12950: r16 = Instance_EdgeInsets
    //     0xc12950: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc12954: ldr             x16, [x16, #0x980]
    // 0xc12958: ldur            lr, [fp, #-0x30]
    // 0xc1295c: stp             lr, x16, [SP, #8]
    // 0xc12960: ldur            x16, [fp, #-0x18]
    // 0xc12964: str             x16, [SP]
    // 0xc12968: mov             x1, x0
    // 0xc1296c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc1296c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc12970: ldr             x4, [x4, #0x988]
    // 0xc12974: r0 = Container()
    //     0xc12974: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc12978: ldur            x2, [fp, #-8]
    // 0xc1297c: LoadField: r0 = r2->field_b
    //     0xc1297c: ldur            w0, [x2, #0xb]
    // 0xc12980: DecompressPointer r0
    //     0xc12980: add             x0, x0, HEAP, lsl #32
    // 0xc12984: cmp             w0, NULL
    // 0xc12988: b.eq            #0xc142b8
    // 0xc1298c: LoadField: r3 = r0->field_f
    //     0xc1298c: ldur            w3, [x0, #0xf]
    // 0xc12990: DecompressPointer r3
    //     0xc12990: add             x3, x3, HEAP, lsl #32
    // 0xc12994: LoadField: r4 = r2->field_13
    //     0xc12994: ldur            x4, [x2, #0x13]
    // 0xc12998: LoadField: r0 = r3->field_b
    //     0xc12998: ldur            w0, [x3, #0xb]
    // 0xc1299c: r1 = LoadInt32Instr(r0)
    //     0xc1299c: sbfx            x1, x0, #1, #0x1f
    // 0xc129a0: mov             x0, x1
    // 0xc129a4: mov             x1, x4
    // 0xc129a8: cmp             x1, x0
    // 0xc129ac: b.hs            #0xc142bc
    // 0xc129b0: LoadField: r0 = r3->field_f
    //     0xc129b0: ldur            w0, [x3, #0xf]
    // 0xc129b4: DecompressPointer r0
    //     0xc129b4: add             x0, x0, HEAP, lsl #32
    // 0xc129b8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc129b8: add             x16, x0, x4, lsl #2
    //     0xc129bc: ldur            w1, [x16, #0xf]
    // 0xc129c0: DecompressPointer r1
    //     0xc129c0: add             x1, x1, HEAP, lsl #32
    // 0xc129c4: LoadField: r0 = r1->field_7
    //     0xc129c4: ldur            w0, [x1, #7]
    // 0xc129c8: DecompressPointer r0
    //     0xc129c8: add             x0, x0, HEAP, lsl #32
    // 0xc129cc: cmp             w0, NULL
    // 0xc129d0: b.ne            #0xc129dc
    // 0xc129d4: r3 = ""
    //     0xc129d4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc129d8: b               #0xc129e0
    // 0xc129dc: mov             x3, x0
    // 0xc129e0: ldur            x0, [fp, #-0x20]
    // 0xc129e4: stur            x3, [fp, #-0x18]
    // 0xc129e8: LoadField: r1 = r0->field_13
    //     0xc129e8: ldur            w1, [x0, #0x13]
    // 0xc129ec: DecompressPointer r1
    //     0xc129ec: add             x1, x1, HEAP, lsl #32
    // 0xc129f0: r0 = of()
    //     0xc129f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc129f4: LoadField: r1 = r0->field_87
    //     0xc129f4: ldur            w1, [x0, #0x87]
    // 0xc129f8: DecompressPointer r1
    //     0xc129f8: add             x1, x1, HEAP, lsl #32
    // 0xc129fc: LoadField: r0 = r1->field_7
    //     0xc129fc: ldur            w0, [x1, #7]
    // 0xc12a00: DecompressPointer r0
    //     0xc12a00: add             x0, x0, HEAP, lsl #32
    // 0xc12a04: r16 = 14.000000
    //     0xc12a04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc12a08: ldr             x16, [x16, #0x1d8]
    // 0xc12a0c: r30 = Instance_Color
    //     0xc12a0c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc12a10: stp             lr, x16, [SP]
    // 0xc12a14: mov             x1, x0
    // 0xc12a18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc12a18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc12a1c: ldr             x4, [x4, #0xaa0]
    // 0xc12a20: r0 = copyWith()
    //     0xc12a20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc12a24: stur            x0, [fp, #-0x30]
    // 0xc12a28: r0 = Text()
    //     0xc12a28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc12a2c: mov             x2, x0
    // 0xc12a30: ldur            x0, [fp, #-0x18]
    // 0xc12a34: stur            x2, [fp, #-0x48]
    // 0xc12a38: StoreField: r2->field_b = r0
    //     0xc12a38: stur            w0, [x2, #0xb]
    // 0xc12a3c: ldur            x0, [fp, #-0x30]
    // 0xc12a40: StoreField: r2->field_13 = r0
    //     0xc12a40: stur            w0, [x2, #0x13]
    // 0xc12a44: ldur            x3, [fp, #-8]
    // 0xc12a48: LoadField: r0 = r3->field_b
    //     0xc12a48: ldur            w0, [x3, #0xb]
    // 0xc12a4c: DecompressPointer r0
    //     0xc12a4c: add             x0, x0, HEAP, lsl #32
    // 0xc12a50: cmp             w0, NULL
    // 0xc12a54: b.eq            #0xc142c0
    // 0xc12a58: LoadField: r4 = r0->field_f
    //     0xc12a58: ldur            w4, [x0, #0xf]
    // 0xc12a5c: DecompressPointer r4
    //     0xc12a5c: add             x4, x4, HEAP, lsl #32
    // 0xc12a60: LoadField: r5 = r3->field_13
    //     0xc12a60: ldur            x5, [x3, #0x13]
    // 0xc12a64: LoadField: r0 = r4->field_b
    //     0xc12a64: ldur            w0, [x4, #0xb]
    // 0xc12a68: r1 = LoadInt32Instr(r0)
    //     0xc12a68: sbfx            x1, x0, #1, #0x1f
    // 0xc12a6c: mov             x0, x1
    // 0xc12a70: mov             x1, x5
    // 0xc12a74: cmp             x1, x0
    // 0xc12a78: b.hs            #0xc142c4
    // 0xc12a7c: LoadField: r0 = r4->field_f
    //     0xc12a7c: ldur            w0, [x4, #0xf]
    // 0xc12a80: DecompressPointer r0
    //     0xc12a80: add             x0, x0, HEAP, lsl #32
    // 0xc12a84: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc12a84: add             x16, x0, x5, lsl #2
    //     0xc12a88: ldur            w1, [x16, #0xf]
    // 0xc12a8c: DecompressPointer r1
    //     0xc12a8c: add             x1, x1, HEAP, lsl #32
    // 0xc12a90: LoadField: r0 = r1->field_1f
    //     0xc12a90: ldur            w0, [x1, #0x1f]
    // 0xc12a94: DecompressPointer r0
    //     0xc12a94: add             x0, x0, HEAP, lsl #32
    // 0xc12a98: cmp             w0, NULL
    // 0xc12a9c: b.ne            #0xc12aa8
    // 0xc12aa0: r4 = ""
    //     0xc12aa0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc12aa4: b               #0xc12aac
    // 0xc12aa8: mov             x4, x0
    // 0xc12aac: ldur            x0, [fp, #-0x20]
    // 0xc12ab0: stur            x4, [fp, #-0x18]
    // 0xc12ab4: LoadField: r1 = r0->field_13
    //     0xc12ab4: ldur            w1, [x0, #0x13]
    // 0xc12ab8: DecompressPointer r1
    //     0xc12ab8: add             x1, x1, HEAP, lsl #32
    // 0xc12abc: r0 = of()
    //     0xc12abc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc12ac0: LoadField: r1 = r0->field_87
    //     0xc12ac0: ldur            w1, [x0, #0x87]
    // 0xc12ac4: DecompressPointer r1
    //     0xc12ac4: add             x1, x1, HEAP, lsl #32
    // 0xc12ac8: LoadField: r0 = r1->field_33
    //     0xc12ac8: ldur            w0, [x1, #0x33]
    // 0xc12acc: DecompressPointer r0
    //     0xc12acc: add             x0, x0, HEAP, lsl #32
    // 0xc12ad0: stur            x0, [fp, #-0x30]
    // 0xc12ad4: cmp             w0, NULL
    // 0xc12ad8: b.ne            #0xc12ae4
    // 0xc12adc: r4 = Null
    //     0xc12adc: mov             x4, NULL
    // 0xc12ae0: b               #0xc12b0c
    // 0xc12ae4: r1 = Instance_Color
    //     0xc12ae4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc12ae8: d0 = 0.500000
    //     0xc12ae8: fmov            d0, #0.50000000
    // 0xc12aec: r0 = withOpacity()
    //     0xc12aec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc12af0: r16 = 10.000000
    //     0xc12af0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc12af4: stp             x0, x16, [SP]
    // 0xc12af8: ldur            x1, [fp, #-0x30]
    // 0xc12afc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc12afc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc12b00: ldr             x4, [x4, #0xaa0]
    // 0xc12b04: r0 = copyWith()
    //     0xc12b04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc12b08: mov             x4, x0
    // 0xc12b0c: ldur            x1, [fp, #-8]
    // 0xc12b10: ldur            x3, [fp, #-0x38]
    // 0xc12b14: ldur            x0, [fp, #-0x48]
    // 0xc12b18: ldur            x2, [fp, #-0x18]
    // 0xc12b1c: stur            x4, [fp, #-0x30]
    // 0xc12b20: r0 = Text()
    //     0xc12b20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc12b24: mov             x1, x0
    // 0xc12b28: ldur            x0, [fp, #-0x18]
    // 0xc12b2c: stur            x1, [fp, #-0x50]
    // 0xc12b30: StoreField: r1->field_b = r0
    //     0xc12b30: stur            w0, [x1, #0xb]
    // 0xc12b34: ldur            x0, [fp, #-0x30]
    // 0xc12b38: StoreField: r1->field_13 = r0
    //     0xc12b38: stur            w0, [x1, #0x13]
    // 0xc12b3c: r0 = Padding()
    //     0xc12b3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc12b40: mov             x3, x0
    // 0xc12b44: r0 = Instance_EdgeInsets
    //     0xc12b44: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xc12b48: ldr             x0, [x0, #0xe90]
    // 0xc12b4c: stur            x3, [fp, #-0x18]
    // 0xc12b50: StoreField: r3->field_f = r0
    //     0xc12b50: stur            w0, [x3, #0xf]
    // 0xc12b54: ldur            x1, [fp, #-0x50]
    // 0xc12b58: StoreField: r3->field_b = r1
    //     0xc12b58: stur            w1, [x3, #0xb]
    // 0xc12b5c: r1 = Null
    //     0xc12b5c: mov             x1, NULL
    // 0xc12b60: r2 = 4
    //     0xc12b60: movz            x2, #0x4
    // 0xc12b64: r0 = AllocateArray()
    //     0xc12b64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc12b68: mov             x2, x0
    // 0xc12b6c: ldur            x0, [fp, #-0x48]
    // 0xc12b70: stur            x2, [fp, #-0x30]
    // 0xc12b74: StoreField: r2->field_f = r0
    //     0xc12b74: stur            w0, [x2, #0xf]
    // 0xc12b78: ldur            x0, [fp, #-0x18]
    // 0xc12b7c: StoreField: r2->field_13 = r0
    //     0xc12b7c: stur            w0, [x2, #0x13]
    // 0xc12b80: r1 = <Widget>
    //     0xc12b80: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc12b84: r0 = AllocateGrowableArray()
    //     0xc12b84: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc12b88: mov             x1, x0
    // 0xc12b8c: ldur            x0, [fp, #-0x30]
    // 0xc12b90: stur            x1, [fp, #-0x18]
    // 0xc12b94: StoreField: r1->field_f = r0
    //     0xc12b94: stur            w0, [x1, #0xf]
    // 0xc12b98: r2 = 4
    //     0xc12b98: movz            x2, #0x4
    // 0xc12b9c: StoreField: r1->field_b = r2
    //     0xc12b9c: stur            w2, [x1, #0xb]
    // 0xc12ba0: r0 = Column()
    //     0xc12ba0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc12ba4: mov             x3, x0
    // 0xc12ba8: r0 = Instance_Axis
    //     0xc12ba8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc12bac: stur            x3, [fp, #-0x30]
    // 0xc12bb0: StoreField: r3->field_f = r0
    //     0xc12bb0: stur            w0, [x3, #0xf]
    // 0xc12bb4: r4 = Instance_MainAxisAlignment
    //     0xc12bb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc12bb8: ldr             x4, [x4, #0xa08]
    // 0xc12bbc: StoreField: r3->field_13 = r4
    //     0xc12bbc: stur            w4, [x3, #0x13]
    // 0xc12bc0: r5 = Instance_MainAxisSize
    //     0xc12bc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc12bc4: ldr             x5, [x5, #0xa10]
    // 0xc12bc8: ArrayStore: r3[0] = r5  ; List_4
    //     0xc12bc8: stur            w5, [x3, #0x17]
    // 0xc12bcc: r6 = Instance_CrossAxisAlignment
    //     0xc12bcc: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc12bd0: ldr             x6, [x6, #0x890]
    // 0xc12bd4: StoreField: r3->field_1b = r6
    //     0xc12bd4: stur            w6, [x3, #0x1b]
    // 0xc12bd8: r7 = Instance_VerticalDirection
    //     0xc12bd8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc12bdc: ldr             x7, [x7, #0xa20]
    // 0xc12be0: StoreField: r3->field_23 = r7
    //     0xc12be0: stur            w7, [x3, #0x23]
    // 0xc12be4: r8 = Instance_Clip
    //     0xc12be4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc12be8: ldr             x8, [x8, #0x38]
    // 0xc12bec: StoreField: r3->field_2b = r8
    //     0xc12bec: stur            w8, [x3, #0x2b]
    // 0xc12bf0: StoreField: r3->field_2f = rZR
    //     0xc12bf0: stur            xzr, [x3, #0x2f]
    // 0xc12bf4: ldur            x1, [fp, #-0x18]
    // 0xc12bf8: StoreField: r3->field_b = r1
    //     0xc12bf8: stur            w1, [x3, #0xb]
    // 0xc12bfc: r1 = Null
    //     0xc12bfc: mov             x1, NULL
    // 0xc12c00: r2 = 6
    //     0xc12c00: movz            x2, #0x6
    // 0xc12c04: r0 = AllocateArray()
    //     0xc12c04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc12c08: mov             x2, x0
    // 0xc12c0c: ldur            x0, [fp, #-0x38]
    // 0xc12c10: stur            x2, [fp, #-0x18]
    // 0xc12c14: StoreField: r2->field_f = r0
    //     0xc12c14: stur            w0, [x2, #0xf]
    // 0xc12c18: r16 = Instance_SizedBox
    //     0xc12c18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xc12c1c: ldr             x16, [x16, #0x998]
    // 0xc12c20: StoreField: r2->field_13 = r16
    //     0xc12c20: stur            w16, [x2, #0x13]
    // 0xc12c24: ldur            x0, [fp, #-0x30]
    // 0xc12c28: ArrayStore: r2[0] = r0  ; List_4
    //     0xc12c28: stur            w0, [x2, #0x17]
    // 0xc12c2c: r1 = <Widget>
    //     0xc12c2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc12c30: r0 = AllocateGrowableArray()
    //     0xc12c30: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc12c34: mov             x1, x0
    // 0xc12c38: ldur            x0, [fp, #-0x18]
    // 0xc12c3c: stur            x1, [fp, #-0x30]
    // 0xc12c40: StoreField: r1->field_f = r0
    //     0xc12c40: stur            w0, [x1, #0xf]
    // 0xc12c44: r2 = 6
    //     0xc12c44: movz            x2, #0x6
    // 0xc12c48: StoreField: r1->field_b = r2
    //     0xc12c48: stur            w2, [x1, #0xb]
    // 0xc12c4c: r0 = Row()
    //     0xc12c4c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc12c50: mov             x3, x0
    // 0xc12c54: r2 = Instance_Axis
    //     0xc12c54: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc12c58: stur            x3, [fp, #-0x18]
    // 0xc12c5c: StoreField: r3->field_f = r2
    //     0xc12c5c: stur            w2, [x3, #0xf]
    // 0xc12c60: r4 = Instance_MainAxisAlignment
    //     0xc12c60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc12c64: ldr             x4, [x4, #0xa08]
    // 0xc12c68: StoreField: r3->field_13 = r4
    //     0xc12c68: stur            w4, [x3, #0x13]
    // 0xc12c6c: r5 = Instance_MainAxisSize
    //     0xc12c6c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc12c70: ldr             x5, [x5, #0xa10]
    // 0xc12c74: ArrayStore: r3[0] = r5  ; List_4
    //     0xc12c74: stur            w5, [x3, #0x17]
    // 0xc12c78: r6 = Instance_CrossAxisAlignment
    //     0xc12c78: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc12c7c: ldr             x6, [x6, #0xa18]
    // 0xc12c80: StoreField: r3->field_1b = r6
    //     0xc12c80: stur            w6, [x3, #0x1b]
    // 0xc12c84: r7 = Instance_VerticalDirection
    //     0xc12c84: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc12c88: ldr             x7, [x7, #0xa20]
    // 0xc12c8c: StoreField: r3->field_23 = r7
    //     0xc12c8c: stur            w7, [x3, #0x23]
    // 0xc12c90: r8 = Instance_Clip
    //     0xc12c90: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc12c94: ldr             x8, [x8, #0x38]
    // 0xc12c98: StoreField: r3->field_2b = r8
    //     0xc12c98: stur            w8, [x3, #0x2b]
    // 0xc12c9c: StoreField: r3->field_2f = rZR
    //     0xc12c9c: stur            xzr, [x3, #0x2f]
    // 0xc12ca0: ldur            x0, [fp, #-0x30]
    // 0xc12ca4: StoreField: r3->field_b = r0
    //     0xc12ca4: stur            w0, [x3, #0xb]
    // 0xc12ca8: ldur            x9, [fp, #-8]
    // 0xc12cac: LoadField: r0 = r9->field_b
    //     0xc12cac: ldur            w0, [x9, #0xb]
    // 0xc12cb0: DecompressPointer r0
    //     0xc12cb0: add             x0, x0, HEAP, lsl #32
    // 0xc12cb4: cmp             w0, NULL
    // 0xc12cb8: b.eq            #0xc142c8
    // 0xc12cbc: LoadField: r10 = r0->field_f
    //     0xc12cbc: ldur            w10, [x0, #0xf]
    // 0xc12cc0: DecompressPointer r10
    //     0xc12cc0: add             x10, x10, HEAP, lsl #32
    // 0xc12cc4: LoadField: r11 = r9->field_13
    //     0xc12cc4: ldur            x11, [x9, #0x13]
    // 0xc12cc8: LoadField: r0 = r10->field_b
    //     0xc12cc8: ldur            w0, [x10, #0xb]
    // 0xc12ccc: r1 = LoadInt32Instr(r0)
    //     0xc12ccc: sbfx            x1, x0, #1, #0x1f
    // 0xc12cd0: mov             x0, x1
    // 0xc12cd4: mov             x1, x11
    // 0xc12cd8: cmp             x1, x0
    // 0xc12cdc: b.hs            #0xc142cc
    // 0xc12ce0: LoadField: r0 = r10->field_f
    //     0xc12ce0: ldur            w0, [x10, #0xf]
    // 0xc12ce4: DecompressPointer r0
    //     0xc12ce4: add             x0, x0, HEAP, lsl #32
    // 0xc12ce8: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xc12ce8: add             x16, x0, x11, lsl #2
    //     0xc12cec: ldur            w1, [x16, #0xf]
    // 0xc12cf0: DecompressPointer r1
    //     0xc12cf0: add             x1, x1, HEAP, lsl #32
    // 0xc12cf4: LoadField: r0 = r1->field_f
    //     0xc12cf4: ldur            w0, [x1, #0xf]
    // 0xc12cf8: DecompressPointer r0
    //     0xc12cf8: add             x0, x0, HEAP, lsl #32
    // 0xc12cfc: r1 = 60
    //     0xc12cfc: movz            x1, #0x3c
    // 0xc12d00: branchIfSmi(r0, 0xc12d0c)
    //     0xc12d00: tbz             w0, #0, #0xc12d0c
    // 0xc12d04: r1 = LoadClassIdInstr(r0)
    //     0xc12d04: ldur            x1, [x0, #-1]
    //     0xc12d08: ubfx            x1, x1, #0xc, #0x14
    // 0xc12d0c: str             x0, [SP]
    // 0xc12d10: mov             x0, x1
    // 0xc12d14: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc12d14: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc12d18: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc12d18: movz            x17, #0x2700
    //     0xc12d1c: add             lr, x0, x17
    //     0xc12d20: ldr             lr, [x21, lr, lsl #3]
    //     0xc12d24: blr             lr
    // 0xc12d28: mov             x1, x0
    // 0xc12d2c: r0 = parse()
    //     0xc12d2c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc12d30: mov             v1.16b, v0.16b
    // 0xc12d34: d0 = 4.000000
    //     0xc12d34: fmov            d0, #4.00000000
    // 0xc12d38: fcmp            d1, d0
    // 0xc12d3c: b.lt            #0xc12d50
    // 0xc12d40: r4 = Instance_Color
    //     0xc12d40: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc12d44: ldr             x4, [x4, #0x858]
    // 0xc12d48: d0 = 2.000000
    //     0xc12d48: fmov            d0, #2.00000000
    // 0xc12d4c: b               #0xc12eb0
    // 0xc12d50: ldur            x2, [fp, #-8]
    // 0xc12d54: LoadField: r0 = r2->field_b
    //     0xc12d54: ldur            w0, [x2, #0xb]
    // 0xc12d58: DecompressPointer r0
    //     0xc12d58: add             x0, x0, HEAP, lsl #32
    // 0xc12d5c: cmp             w0, NULL
    // 0xc12d60: b.eq            #0xc142d0
    // 0xc12d64: LoadField: r3 = r0->field_f
    //     0xc12d64: ldur            w3, [x0, #0xf]
    // 0xc12d68: DecompressPointer r3
    //     0xc12d68: add             x3, x3, HEAP, lsl #32
    // 0xc12d6c: LoadField: r4 = r2->field_13
    //     0xc12d6c: ldur            x4, [x2, #0x13]
    // 0xc12d70: LoadField: r0 = r3->field_b
    //     0xc12d70: ldur            w0, [x3, #0xb]
    // 0xc12d74: r1 = LoadInt32Instr(r0)
    //     0xc12d74: sbfx            x1, x0, #1, #0x1f
    // 0xc12d78: mov             x0, x1
    // 0xc12d7c: mov             x1, x4
    // 0xc12d80: cmp             x1, x0
    // 0xc12d84: b.hs            #0xc142d4
    // 0xc12d88: LoadField: r0 = r3->field_f
    //     0xc12d88: ldur            w0, [x3, #0xf]
    // 0xc12d8c: DecompressPointer r0
    //     0xc12d8c: add             x0, x0, HEAP, lsl #32
    // 0xc12d90: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc12d90: add             x16, x0, x4, lsl #2
    //     0xc12d94: ldur            w1, [x16, #0xf]
    // 0xc12d98: DecompressPointer r1
    //     0xc12d98: add             x1, x1, HEAP, lsl #32
    // 0xc12d9c: LoadField: r0 = r1->field_f
    //     0xc12d9c: ldur            w0, [x1, #0xf]
    // 0xc12da0: DecompressPointer r0
    //     0xc12da0: add             x0, x0, HEAP, lsl #32
    // 0xc12da4: r1 = 60
    //     0xc12da4: movz            x1, #0x3c
    // 0xc12da8: branchIfSmi(r0, 0xc12db4)
    //     0xc12da8: tbz             w0, #0, #0xc12db4
    // 0xc12dac: r1 = LoadClassIdInstr(r0)
    //     0xc12dac: ldur            x1, [x0, #-1]
    //     0xc12db0: ubfx            x1, x1, #0xc, #0x14
    // 0xc12db4: str             x0, [SP]
    // 0xc12db8: mov             x0, x1
    // 0xc12dbc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc12dbc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc12dc0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc12dc0: movz            x17, #0x2700
    //     0xc12dc4: add             lr, x0, x17
    //     0xc12dc8: ldr             lr, [x21, lr, lsl #3]
    //     0xc12dcc: blr             lr
    // 0xc12dd0: mov             x1, x0
    // 0xc12dd4: r0 = parse()
    //     0xc12dd4: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc12dd8: d1 = 3.500000
    //     0xc12dd8: fmov            d1, #3.50000000
    // 0xc12ddc: fcmp            d0, d1
    // 0xc12de0: b.lt            #0xc12e00
    // 0xc12de4: r1 = Instance_Color
    //     0xc12de4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc12de8: ldr             x1, [x1, #0x858]
    // 0xc12dec: d0 = 0.700000
    //     0xc12dec: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc12df0: ldr             d0, [x17, #0xf48]
    // 0xc12df4: r0 = withOpacity()
    //     0xc12df4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc12df8: d0 = 2.000000
    //     0xc12df8: fmov            d0, #2.00000000
    // 0xc12dfc: b               #0xc12eac
    // 0xc12e00: ldur            x2, [fp, #-8]
    // 0xc12e04: LoadField: r0 = r2->field_b
    //     0xc12e04: ldur            w0, [x2, #0xb]
    // 0xc12e08: DecompressPointer r0
    //     0xc12e08: add             x0, x0, HEAP, lsl #32
    // 0xc12e0c: cmp             w0, NULL
    // 0xc12e10: b.eq            #0xc142d8
    // 0xc12e14: LoadField: r3 = r0->field_f
    //     0xc12e14: ldur            w3, [x0, #0xf]
    // 0xc12e18: DecompressPointer r3
    //     0xc12e18: add             x3, x3, HEAP, lsl #32
    // 0xc12e1c: LoadField: r4 = r2->field_13
    //     0xc12e1c: ldur            x4, [x2, #0x13]
    // 0xc12e20: LoadField: r0 = r3->field_b
    //     0xc12e20: ldur            w0, [x3, #0xb]
    // 0xc12e24: r1 = LoadInt32Instr(r0)
    //     0xc12e24: sbfx            x1, x0, #1, #0x1f
    // 0xc12e28: mov             x0, x1
    // 0xc12e2c: mov             x1, x4
    // 0xc12e30: cmp             x1, x0
    // 0xc12e34: b.hs            #0xc142dc
    // 0xc12e38: LoadField: r0 = r3->field_f
    //     0xc12e38: ldur            w0, [x3, #0xf]
    // 0xc12e3c: DecompressPointer r0
    //     0xc12e3c: add             x0, x0, HEAP, lsl #32
    // 0xc12e40: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc12e40: add             x16, x0, x4, lsl #2
    //     0xc12e44: ldur            w1, [x16, #0xf]
    // 0xc12e48: DecompressPointer r1
    //     0xc12e48: add             x1, x1, HEAP, lsl #32
    // 0xc12e4c: LoadField: r0 = r1->field_f
    //     0xc12e4c: ldur            w0, [x1, #0xf]
    // 0xc12e50: DecompressPointer r0
    //     0xc12e50: add             x0, x0, HEAP, lsl #32
    // 0xc12e54: r1 = 60
    //     0xc12e54: movz            x1, #0x3c
    // 0xc12e58: branchIfSmi(r0, 0xc12e64)
    //     0xc12e58: tbz             w0, #0, #0xc12e64
    // 0xc12e5c: r1 = LoadClassIdInstr(r0)
    //     0xc12e5c: ldur            x1, [x0, #-1]
    //     0xc12e60: ubfx            x1, x1, #0xc, #0x14
    // 0xc12e64: str             x0, [SP]
    // 0xc12e68: mov             x0, x1
    // 0xc12e6c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc12e6c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc12e70: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc12e70: movz            x17, #0x2700
    //     0xc12e74: add             lr, x0, x17
    //     0xc12e78: ldr             lr, [x21, lr, lsl #3]
    //     0xc12e7c: blr             lr
    // 0xc12e80: mov             x1, x0
    // 0xc12e84: r0 = parse()
    //     0xc12e84: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc12e88: mov             v1.16b, v0.16b
    // 0xc12e8c: d0 = 2.000000
    //     0xc12e8c: fmov            d0, #2.00000000
    // 0xc12e90: fcmp            d1, d0
    // 0xc12e94: b.lt            #0xc12ea4
    // 0xc12e98: r0 = Instance_Color
    //     0xc12e98: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xc12e9c: ldr             x0, [x0, #0x860]
    // 0xc12ea0: b               #0xc12eac
    // 0xc12ea4: r0 = Instance_Color
    //     0xc12ea4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xc12ea8: ldr             x0, [x0, #0x50]
    // 0xc12eac: mov             x4, x0
    // 0xc12eb0: ldur            x0, [fp, #-8]
    // 0xc12eb4: ldur            x2, [fp, #-0x20]
    // 0xc12eb8: ldur            x1, [fp, #-0x18]
    // 0xc12ebc: ldur            d1, [fp, #-0x58]
    // 0xc12ec0: ldur            x3, [fp, #-0x10]
    // 0xc12ec4: stur            x4, [fp, #-0x30]
    // 0xc12ec8: r0 = ColorFilter()
    //     0xc12ec8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc12ecc: mov             x1, x0
    // 0xc12ed0: ldur            x0, [fp, #-0x30]
    // 0xc12ed4: stur            x1, [fp, #-0x38]
    // 0xc12ed8: StoreField: r1->field_7 = r0
    //     0xc12ed8: stur            w0, [x1, #7]
    // 0xc12edc: r0 = Instance_BlendMode
    //     0xc12edc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc12ee0: ldr             x0, [x0, #0xb30]
    // 0xc12ee4: StoreField: r1->field_b = r0
    //     0xc12ee4: stur            w0, [x1, #0xb]
    // 0xc12ee8: r2 = 1
    //     0xc12ee8: movz            x2, #0x1
    // 0xc12eec: StoreField: r1->field_13 = r2
    //     0xc12eec: stur            x2, [x1, #0x13]
    // 0xc12ef0: r0 = SvgPicture()
    //     0xc12ef0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc12ef4: stur            x0, [fp, #-0x30]
    // 0xc12ef8: ldur            x16, [fp, #-0x38]
    // 0xc12efc: str             x16, [SP]
    // 0xc12f00: mov             x1, x0
    // 0xc12f04: r2 = "assets/images/green_star.svg"
    //     0xc12f04: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xc12f08: ldr             x2, [x2, #0x9a0]
    // 0xc12f0c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc12f0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc12f10: ldr             x4, [x4, #0xa38]
    // 0xc12f14: r0 = SvgPicture.asset()
    //     0xc12f14: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc12f18: ldur            x2, [fp, #-8]
    // 0xc12f1c: LoadField: r0 = r2->field_b
    //     0xc12f1c: ldur            w0, [x2, #0xb]
    // 0xc12f20: DecompressPointer r0
    //     0xc12f20: add             x0, x0, HEAP, lsl #32
    // 0xc12f24: cmp             w0, NULL
    // 0xc12f28: b.eq            #0xc142e0
    // 0xc12f2c: LoadField: r3 = r0->field_f
    //     0xc12f2c: ldur            w3, [x0, #0xf]
    // 0xc12f30: DecompressPointer r3
    //     0xc12f30: add             x3, x3, HEAP, lsl #32
    // 0xc12f34: LoadField: r4 = r2->field_13
    //     0xc12f34: ldur            x4, [x2, #0x13]
    // 0xc12f38: LoadField: r0 = r3->field_b
    //     0xc12f38: ldur            w0, [x3, #0xb]
    // 0xc12f3c: r1 = LoadInt32Instr(r0)
    //     0xc12f3c: sbfx            x1, x0, #1, #0x1f
    // 0xc12f40: mov             x0, x1
    // 0xc12f44: mov             x1, x4
    // 0xc12f48: cmp             x1, x0
    // 0xc12f4c: b.hs            #0xc142e4
    // 0xc12f50: LoadField: r0 = r3->field_f
    //     0xc12f50: ldur            w0, [x3, #0xf]
    // 0xc12f54: DecompressPointer r0
    //     0xc12f54: add             x0, x0, HEAP, lsl #32
    // 0xc12f58: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc12f58: add             x16, x0, x4, lsl #2
    //     0xc12f5c: ldur            w1, [x16, #0xf]
    // 0xc12f60: DecompressPointer r1
    //     0xc12f60: add             x1, x1, HEAP, lsl #32
    // 0xc12f64: LoadField: r0 = r1->field_f
    //     0xc12f64: ldur            w0, [x1, #0xf]
    // 0xc12f68: DecompressPointer r0
    //     0xc12f68: add             x0, x0, HEAP, lsl #32
    // 0xc12f6c: r1 = 60
    //     0xc12f6c: movz            x1, #0x3c
    // 0xc12f70: branchIfSmi(r0, 0xc12f7c)
    //     0xc12f70: tbz             w0, #0, #0xc12f7c
    // 0xc12f74: r1 = LoadClassIdInstr(r0)
    //     0xc12f74: ldur            x1, [x0, #-1]
    //     0xc12f78: ubfx            x1, x1, #0xc, #0x14
    // 0xc12f7c: str             x0, [SP]
    // 0xc12f80: mov             x0, x1
    // 0xc12f84: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc12f84: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc12f88: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc12f88: movz            x17, #0x2700
    //     0xc12f8c: add             lr, x0, x17
    //     0xc12f90: ldr             lr, [x21, lr, lsl #3]
    //     0xc12f94: blr             lr
    // 0xc12f98: ldur            x2, [fp, #-0x20]
    // 0xc12f9c: stur            x0, [fp, #-0x38]
    // 0xc12fa0: LoadField: r1 = r2->field_13
    //     0xc12fa0: ldur            w1, [x2, #0x13]
    // 0xc12fa4: DecompressPointer r1
    //     0xc12fa4: add             x1, x1, HEAP, lsl #32
    // 0xc12fa8: r0 = of()
    //     0xc12fa8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc12fac: LoadField: r1 = r0->field_87
    //     0xc12fac: ldur            w1, [x0, #0x87]
    // 0xc12fb0: DecompressPointer r1
    //     0xc12fb0: add             x1, x1, HEAP, lsl #32
    // 0xc12fb4: LoadField: r0 = r1->field_7
    //     0xc12fb4: ldur            w0, [x1, #7]
    // 0xc12fb8: DecompressPointer r0
    //     0xc12fb8: add             x0, x0, HEAP, lsl #32
    // 0xc12fbc: r16 = 12.000000
    //     0xc12fbc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc12fc0: ldr             x16, [x16, #0x9e8]
    // 0xc12fc4: r30 = Instance_Color
    //     0xc12fc4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc12fc8: stp             lr, x16, [SP]
    // 0xc12fcc: mov             x1, x0
    // 0xc12fd0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc12fd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc12fd4: ldr             x4, [x4, #0xaa0]
    // 0xc12fd8: r0 = copyWith()
    //     0xc12fd8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc12fdc: stur            x0, [fp, #-0x48]
    // 0xc12fe0: r0 = Text()
    //     0xc12fe0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc12fe4: mov             x3, x0
    // 0xc12fe8: ldur            x0, [fp, #-0x38]
    // 0xc12fec: stur            x3, [fp, #-0x50]
    // 0xc12ff0: StoreField: r3->field_b = r0
    //     0xc12ff0: stur            w0, [x3, #0xb]
    // 0xc12ff4: ldur            x0, [fp, #-0x48]
    // 0xc12ff8: StoreField: r3->field_13 = r0
    //     0xc12ff8: stur            w0, [x3, #0x13]
    // 0xc12ffc: r1 = Null
    //     0xc12ffc: mov             x1, NULL
    // 0xc13000: r2 = 6
    //     0xc13000: movz            x2, #0x6
    // 0xc13004: r0 = AllocateArray()
    //     0xc13004: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc13008: mov             x2, x0
    // 0xc1300c: ldur            x0, [fp, #-0x30]
    // 0xc13010: stur            x2, [fp, #-0x38]
    // 0xc13014: StoreField: r2->field_f = r0
    //     0xc13014: stur            w0, [x2, #0xf]
    // 0xc13018: r16 = Instance_SizedBox
    //     0xc13018: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xc1301c: ldr             x16, [x16, #0xe98]
    // 0xc13020: StoreField: r2->field_13 = r16
    //     0xc13020: stur            w16, [x2, #0x13]
    // 0xc13024: ldur            x0, [fp, #-0x50]
    // 0xc13028: ArrayStore: r2[0] = r0  ; List_4
    //     0xc13028: stur            w0, [x2, #0x17]
    // 0xc1302c: r1 = <Widget>
    //     0xc1302c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc13030: r0 = AllocateGrowableArray()
    //     0xc13030: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc13034: mov             x1, x0
    // 0xc13038: ldur            x0, [fp, #-0x38]
    // 0xc1303c: stur            x1, [fp, #-0x30]
    // 0xc13040: StoreField: r1->field_f = r0
    //     0xc13040: stur            w0, [x1, #0xf]
    // 0xc13044: r2 = 6
    //     0xc13044: movz            x2, #0x6
    // 0xc13048: StoreField: r1->field_b = r2
    //     0xc13048: stur            w2, [x1, #0xb]
    // 0xc1304c: r0 = Row()
    //     0xc1304c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc13050: mov             x1, x0
    // 0xc13054: r0 = Instance_Axis
    //     0xc13054: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc13058: stur            x1, [fp, #-0x38]
    // 0xc1305c: StoreField: r1->field_f = r0
    //     0xc1305c: stur            w0, [x1, #0xf]
    // 0xc13060: r2 = Instance_MainAxisAlignment
    //     0xc13060: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc13064: ldr             x2, [x2, #0xa08]
    // 0xc13068: StoreField: r1->field_13 = r2
    //     0xc13068: stur            w2, [x1, #0x13]
    // 0xc1306c: r3 = Instance_MainAxisSize
    //     0xc1306c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc13070: ldr             x3, [x3, #0xa10]
    // 0xc13074: ArrayStore: r1[0] = r3  ; List_4
    //     0xc13074: stur            w3, [x1, #0x17]
    // 0xc13078: r4 = Instance_CrossAxisAlignment
    //     0xc13078: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc1307c: ldr             x4, [x4, #0xa18]
    // 0xc13080: StoreField: r1->field_1b = r4
    //     0xc13080: stur            w4, [x1, #0x1b]
    // 0xc13084: r5 = Instance_VerticalDirection
    //     0xc13084: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc13088: ldr             x5, [x5, #0xa20]
    // 0xc1308c: StoreField: r1->field_23 = r5
    //     0xc1308c: stur            w5, [x1, #0x23]
    // 0xc13090: r6 = Instance_Clip
    //     0xc13090: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc13094: ldr             x6, [x6, #0x38]
    // 0xc13098: StoreField: r1->field_2b = r6
    //     0xc13098: stur            w6, [x1, #0x2b]
    // 0xc1309c: StoreField: r1->field_2f = rZR
    //     0xc1309c: stur            xzr, [x1, #0x2f]
    // 0xc130a0: ldur            x7, [fp, #-0x30]
    // 0xc130a4: StoreField: r1->field_b = r7
    //     0xc130a4: stur            w7, [x1, #0xb]
    // 0xc130a8: r0 = Align()
    //     0xc130a8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc130ac: mov             x3, x0
    // 0xc130b0: r0 = Instance_Alignment
    //     0xc130b0: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xc130b4: ldr             x0, [x0, #0xa78]
    // 0xc130b8: stur            x3, [fp, #-0x30]
    // 0xc130bc: StoreField: r3->field_f = r0
    //     0xc130bc: stur            w0, [x3, #0xf]
    // 0xc130c0: ldur            x1, [fp, #-0x38]
    // 0xc130c4: StoreField: r3->field_b = r1
    //     0xc130c4: stur            w1, [x3, #0xb]
    // 0xc130c8: r1 = Null
    //     0xc130c8: mov             x1, NULL
    // 0xc130cc: r2 = 4
    //     0xc130cc: movz            x2, #0x4
    // 0xc130d0: r0 = AllocateArray()
    //     0xc130d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc130d4: mov             x2, x0
    // 0xc130d8: ldur            x0, [fp, #-0x18]
    // 0xc130dc: stur            x2, [fp, #-0x38]
    // 0xc130e0: StoreField: r2->field_f = r0
    //     0xc130e0: stur            w0, [x2, #0xf]
    // 0xc130e4: ldur            x0, [fp, #-0x30]
    // 0xc130e8: StoreField: r2->field_13 = r0
    //     0xc130e8: stur            w0, [x2, #0x13]
    // 0xc130ec: r1 = <Widget>
    //     0xc130ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc130f0: r0 = AllocateGrowableArray()
    //     0xc130f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc130f4: mov             x1, x0
    // 0xc130f8: ldur            x0, [fp, #-0x38]
    // 0xc130fc: stur            x1, [fp, #-0x18]
    // 0xc13100: StoreField: r1->field_f = r0
    //     0xc13100: stur            w0, [x1, #0xf]
    // 0xc13104: r2 = 4
    //     0xc13104: movz            x2, #0x4
    // 0xc13108: StoreField: r1->field_b = r2
    //     0xc13108: stur            w2, [x1, #0xb]
    // 0xc1310c: r0 = Row()
    //     0xc1310c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc13110: mov             x1, x0
    // 0xc13114: r0 = Instance_Axis
    //     0xc13114: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc13118: stur            x1, [fp, #-0x30]
    // 0xc1311c: StoreField: r1->field_f = r0
    //     0xc1311c: stur            w0, [x1, #0xf]
    // 0xc13120: r2 = Instance_MainAxisAlignment
    //     0xc13120: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc13124: ldr             x2, [x2, #0xa8]
    // 0xc13128: StoreField: r1->field_13 = r2
    //     0xc13128: stur            w2, [x1, #0x13]
    // 0xc1312c: r3 = Instance_MainAxisSize
    //     0xc1312c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc13130: ldr             x3, [x3, #0xa10]
    // 0xc13134: ArrayStore: r1[0] = r3  ; List_4
    //     0xc13134: stur            w3, [x1, #0x17]
    // 0xc13138: r4 = Instance_CrossAxisAlignment
    //     0xc13138: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc1313c: ldr             x4, [x4, #0xa18]
    // 0xc13140: StoreField: r1->field_1b = r4
    //     0xc13140: stur            w4, [x1, #0x1b]
    // 0xc13144: r5 = Instance_VerticalDirection
    //     0xc13144: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc13148: ldr             x5, [x5, #0xa20]
    // 0xc1314c: StoreField: r1->field_23 = r5
    //     0xc1314c: stur            w5, [x1, #0x23]
    // 0xc13150: r6 = Instance_Clip
    //     0xc13150: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc13154: ldr             x6, [x6, #0x38]
    // 0xc13158: StoreField: r1->field_2b = r6
    //     0xc13158: stur            w6, [x1, #0x2b]
    // 0xc1315c: StoreField: r1->field_2f = rZR
    //     0xc1315c: stur            xzr, [x1, #0x2f]
    // 0xc13160: ldur            x7, [fp, #-0x18]
    // 0xc13164: StoreField: r1->field_b = r7
    //     0xc13164: stur            w7, [x1, #0xb]
    // 0xc13168: r0 = Padding()
    //     0xc13168: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc1316c: mov             x1, x0
    // 0xc13170: r0 = Instance_EdgeInsets
    //     0xc13170: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xc13174: ldr             x0, [x0, #0xd0]
    // 0xc13178: stur            x1, [fp, #-0x18]
    // 0xc1317c: StoreField: r1->field_f = r0
    //     0xc1317c: stur            w0, [x1, #0xf]
    // 0xc13180: ldur            x0, [fp, #-0x30]
    // 0xc13184: StoreField: r1->field_b = r0
    //     0xc13184: stur            w0, [x1, #0xb]
    // 0xc13188: r0 = Container()
    //     0xc13188: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc1318c: stur            x0, [fp, #-0x30]
    // 0xc13190: ldur            x16, [fp, #-0x28]
    // 0xc13194: ldur            lr, [fp, #-0x18]
    // 0xc13198: stp             lr, x16, [SP]
    // 0xc1319c: mov             x1, x0
    // 0xc131a0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xc131a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xc131a4: ldr             x4, [x4, #0x88]
    // 0xc131a8: r0 = Container()
    //     0xc131a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc131ac: ldur            d0, [fp, #-0x58]
    // 0xc131b0: r0 = inline_Allocate_Double()
    //     0xc131b0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xc131b4: add             x0, x0, #0x10
    //     0xc131b8: cmp             x1, x0
    //     0xc131bc: b.ls            #0xc142e8
    //     0xc131c0: str             x0, [THR, #0x50]  ; THR::top
    //     0xc131c4: sub             x0, x0, #0xf
    //     0xc131c8: movz            x1, #0xe15c
    //     0xc131cc: movk            x1, #0x3, lsl #16
    //     0xc131d0: stur            x1, [x0, #-1]
    // 0xc131d4: StoreField: r0->field_7 = d0
    //     0xc131d4: stur            d0, [x0, #7]
    // 0xc131d8: stur            x0, [fp, #-0x18]
    // 0xc131dc: r0 = SizedBox()
    //     0xc131dc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc131e0: mov             x2, x0
    // 0xc131e4: ldur            x0, [fp, #-0x18]
    // 0xc131e8: stur            x2, [fp, #-0x28]
    // 0xc131ec: StoreField: r2->field_f = r0
    //     0xc131ec: stur            w0, [x2, #0xf]
    // 0xc131f0: ldur            x0, [fp, #-0x30]
    // 0xc131f4: StoreField: r2->field_b = r0
    //     0xc131f4: stur            w0, [x2, #0xb]
    // 0xc131f8: r1 = <StackParentData>
    //     0xc131f8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xc131fc: ldr             x1, [x1, #0x8e0]
    // 0xc13200: r0 = Positioned()
    //     0xc13200: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xc13204: mov             x2, x0
    // 0xc13208: r0 = 0.000000
    //     0xc13208: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xc1320c: stur            x2, [fp, #-0x18]
    // 0xc13210: StoreField: r2->field_1f = r0
    //     0xc13210: stur            w0, [x2, #0x1f]
    // 0xc13214: ldur            x0, [fp, #-0x28]
    // 0xc13218: StoreField: r2->field_b = r0
    //     0xc13218: stur            w0, [x2, #0xb]
    // 0xc1321c: ldur            x0, [fp, #-0x10]
    // 0xc13220: LoadField: r1 = r0->field_b
    //     0xc13220: ldur            w1, [x0, #0xb]
    // 0xc13224: LoadField: r3 = r0->field_f
    //     0xc13224: ldur            w3, [x0, #0xf]
    // 0xc13228: DecompressPointer r3
    //     0xc13228: add             x3, x3, HEAP, lsl #32
    // 0xc1322c: LoadField: r4 = r3->field_b
    //     0xc1322c: ldur            w4, [x3, #0xb]
    // 0xc13230: r3 = LoadInt32Instr(r1)
    //     0xc13230: sbfx            x3, x1, #1, #0x1f
    // 0xc13234: stur            x3, [fp, #-0x40]
    // 0xc13238: r1 = LoadInt32Instr(r4)
    //     0xc13238: sbfx            x1, x4, #1, #0x1f
    // 0xc1323c: cmp             x3, x1
    // 0xc13240: b.ne            #0xc1324c
    // 0xc13244: mov             x1, x0
    // 0xc13248: r0 = _growToNextCapacity()
    //     0xc13248: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc1324c: ldur            x2, [fp, #-0x10]
    // 0xc13250: ldur            x3, [fp, #-0x40]
    // 0xc13254: add             x0, x3, #1
    // 0xc13258: lsl             x1, x0, #1
    // 0xc1325c: StoreField: r2->field_b = r1
    //     0xc1325c: stur            w1, [x2, #0xb]
    // 0xc13260: LoadField: r1 = r2->field_f
    //     0xc13260: ldur            w1, [x2, #0xf]
    // 0xc13264: DecompressPointer r1
    //     0xc13264: add             x1, x1, HEAP, lsl #32
    // 0xc13268: ldur            x0, [fp, #-0x18]
    // 0xc1326c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc1326c: add             x25, x1, x3, lsl #2
    //     0xc13270: add             x25, x25, #0xf
    //     0xc13274: str             w0, [x25]
    //     0xc13278: tbz             w0, #0, #0xc13294
    //     0xc1327c: ldurb           w16, [x1, #-1]
    //     0xc13280: ldurb           w17, [x0, #-1]
    //     0xc13284: and             x16, x17, x16, lsr #2
    //     0xc13288: tst             x16, HEAP, lsr #32
    //     0xc1328c: b.eq            #0xc13294
    //     0xc13290: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc13294: ldur            x0, [fp, #-8]
    // 0xc13298: r0 = Stack()
    //     0xc13298: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc1329c: mov             x1, x0
    // 0xc132a0: r0 = Instance_AlignmentDirectional
    //     0xc132a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xc132a4: ldr             x0, [x0, #0xd08]
    // 0xc132a8: stur            x1, [fp, #-0x18]
    // 0xc132ac: StoreField: r1->field_f = r0
    //     0xc132ac: stur            w0, [x1, #0xf]
    // 0xc132b0: r0 = Instance_StackFit
    //     0xc132b0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xc132b4: ldr             x0, [x0, #0xfa8]
    // 0xc132b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xc132b8: stur            w0, [x1, #0x17]
    // 0xc132bc: r0 = Instance_Clip
    //     0xc132bc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xc132c0: ldr             x0, [x0, #0x7e0]
    // 0xc132c4: StoreField: r1->field_1b = r0
    //     0xc132c4: stur            w0, [x1, #0x1b]
    // 0xc132c8: ldur            x0, [fp, #-0x10]
    // 0xc132cc: StoreField: r1->field_b = r0
    //     0xc132cc: stur            w0, [x1, #0xb]
    // 0xc132d0: r0 = ColoredBox()
    //     0xc132d0: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xc132d4: mov             x2, x0
    // 0xc132d8: r0 = Instance_Color
    //     0xc132d8: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc132dc: stur            x2, [fp, #-0x10]
    // 0xc132e0: StoreField: r2->field_f = r0
    //     0xc132e0: stur            w0, [x2, #0xf]
    // 0xc132e4: ldur            x1, [fp, #-0x18]
    // 0xc132e8: StoreField: r2->field_b = r1
    //     0xc132e8: stur            w1, [x2, #0xb]
    // 0xc132ec: r1 = <FlexParentData>
    //     0xc132ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xc132f0: ldr             x1, [x1, #0xe00]
    // 0xc132f4: r0 = Expanded()
    //     0xc132f4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xc132f8: mov             x3, x0
    // 0xc132fc: r0 = 1
    //     0xc132fc: movz            x0, #0x1
    // 0xc13300: stur            x3, [fp, #-0x18]
    // 0xc13304: StoreField: r3->field_13 = r0
    //     0xc13304: stur            x0, [x3, #0x13]
    // 0xc13308: r1 = Instance_FlexFit
    //     0xc13308: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xc1330c: ldr             x1, [x1, #0xe08]
    // 0xc13310: StoreField: r3->field_1b = r1
    //     0xc13310: stur            w1, [x3, #0x1b]
    // 0xc13314: ldur            x1, [fp, #-0x10]
    // 0xc13318: StoreField: r3->field_b = r1
    //     0xc13318: stur            w1, [x3, #0xb]
    // 0xc1331c: r1 = <Widget>
    //     0xc1331c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc13320: r2 = 0
    //     0xc13320: movz            x2, #0
    // 0xc13324: r0 = _GrowableList()
    //     0xc13324: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc13328: mov             x2, x0
    // 0xc1332c: ldur            x0, [fp, #-8]
    // 0xc13330: stur            x2, [fp, #-0x10]
    // 0xc13334: LoadField: r1 = r0->field_2b
    //     0xc13334: ldur            w1, [x0, #0x2b]
    // 0xc13338: DecompressPointer r1
    //     0xc13338: add             x1, x1, HEAP, lsl #32
    // 0xc1333c: tbz             w1, #4, #0xc13d58
    // 0xc13340: r1 = Instance_Color
    //     0xc13340: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc13344: d0 = 0.050000
    //     0xc13344: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xc13348: r0 = withOpacity()
    //     0xc13348: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc1334c: stur            x0, [fp, #-0x28]
    // 0xc13350: r0 = BoxDecoration()
    //     0xc13350: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc13354: mov             x2, x0
    // 0xc13358: ldur            x0, [fp, #-0x28]
    // 0xc1335c: stur            x2, [fp, #-0x30]
    // 0xc13360: StoreField: r2->field_7 = r0
    //     0xc13360: stur            w0, [x2, #7]
    // 0xc13364: r0 = Instance_BoxShape
    //     0xc13364: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xc13368: ldr             x0, [x0, #0x970]
    // 0xc1336c: StoreField: r2->field_23 = r0
    //     0xc1336c: stur            w0, [x2, #0x23]
    // 0xc13370: ldur            x3, [fp, #-8]
    // 0xc13374: LoadField: r0 = r3->field_b
    //     0xc13374: ldur            w0, [x3, #0xb]
    // 0xc13378: DecompressPointer r0
    //     0xc13378: add             x0, x0, HEAP, lsl #32
    // 0xc1337c: cmp             w0, NULL
    // 0xc13380: b.eq            #0xc142f8
    // 0xc13384: LoadField: r4 = r0->field_f
    //     0xc13384: ldur            w4, [x0, #0xf]
    // 0xc13388: DecompressPointer r4
    //     0xc13388: add             x4, x4, HEAP, lsl #32
    // 0xc1338c: LoadField: r5 = r3->field_13
    //     0xc1338c: ldur            x5, [x3, #0x13]
    // 0xc13390: LoadField: r0 = r4->field_b
    //     0xc13390: ldur            w0, [x4, #0xb]
    // 0xc13394: r1 = LoadInt32Instr(r0)
    //     0xc13394: sbfx            x1, x0, #1, #0x1f
    // 0xc13398: mov             x0, x1
    // 0xc1339c: mov             x1, x5
    // 0xc133a0: cmp             x1, x0
    // 0xc133a4: b.hs            #0xc142fc
    // 0xc133a8: LoadField: r0 = r4->field_f
    //     0xc133a8: ldur            w0, [x4, #0xf]
    // 0xc133ac: DecompressPointer r0
    //     0xc133ac: add             x0, x0, HEAP, lsl #32
    // 0xc133b0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc133b0: add             x16, x0, x5, lsl #2
    //     0xc133b4: ldur            w1, [x16, #0xf]
    // 0xc133b8: DecompressPointer r1
    //     0xc133b8: add             x1, x1, HEAP, lsl #32
    // 0xc133bc: LoadField: r0 = r1->field_7
    //     0xc133bc: ldur            w0, [x1, #7]
    // 0xc133c0: DecompressPointer r0
    //     0xc133c0: add             x0, x0, HEAP, lsl #32
    // 0xc133c4: cmp             w0, NULL
    // 0xc133c8: b.ne            #0xc133d4
    // 0xc133cc: r0 = Null
    //     0xc133cc: mov             x0, NULL
    // 0xc133d0: b               #0xc133f8
    // 0xc133d4: stp             xzr, x0, [SP]
    // 0xc133d8: r0 = []()
    //     0xc133d8: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xc133dc: r1 = LoadClassIdInstr(r0)
    //     0xc133dc: ldur            x1, [x0, #-1]
    //     0xc133e0: ubfx            x1, x1, #0xc, #0x14
    // 0xc133e4: str             x0, [SP]
    // 0xc133e8: mov             x0, x1
    // 0xc133ec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc133ec: sub             lr, x0, #1, lsl #12
    //     0xc133f0: ldr             lr, [x21, lr, lsl #3]
    //     0xc133f4: blr             lr
    // 0xc133f8: cmp             w0, NULL
    // 0xc133fc: b.ne            #0xc13408
    // 0xc13400: r3 = ""
    //     0xc13400: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc13404: b               #0xc1340c
    // 0xc13408: mov             x3, x0
    // 0xc1340c: ldur            x0, [fp, #-8]
    // 0xc13410: ldur            x2, [fp, #-0x20]
    // 0xc13414: stur            x3, [fp, #-0x28]
    // 0xc13418: LoadField: r1 = r2->field_13
    //     0xc13418: ldur            w1, [x2, #0x13]
    // 0xc1341c: DecompressPointer r1
    //     0xc1341c: add             x1, x1, HEAP, lsl #32
    // 0xc13420: r0 = of()
    //     0xc13420: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc13424: LoadField: r1 = r0->field_87
    //     0xc13424: ldur            w1, [x0, #0x87]
    // 0xc13428: DecompressPointer r1
    //     0xc13428: add             x1, x1, HEAP, lsl #32
    // 0xc1342c: LoadField: r0 = r1->field_7
    //     0xc1342c: ldur            w0, [x1, #7]
    // 0xc13430: DecompressPointer r0
    //     0xc13430: add             x0, x0, HEAP, lsl #32
    // 0xc13434: stur            x0, [fp, #-0x38]
    // 0xc13438: r1 = Instance_Color
    //     0xc13438: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc1343c: d0 = 0.500000
    //     0xc1343c: fmov            d0, #0.50000000
    // 0xc13440: r0 = withOpacity()
    //     0xc13440: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc13444: r16 = 16.000000
    //     0xc13444: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc13448: ldr             x16, [x16, #0x188]
    // 0xc1344c: stp             x0, x16, [SP]
    // 0xc13450: ldur            x1, [fp, #-0x38]
    // 0xc13454: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc13454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc13458: ldr             x4, [x4, #0xaa0]
    // 0xc1345c: r0 = copyWith()
    //     0xc1345c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13460: stur            x0, [fp, #-0x38]
    // 0xc13464: r0 = Text()
    //     0xc13464: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc13468: mov             x1, x0
    // 0xc1346c: ldur            x0, [fp, #-0x28]
    // 0xc13470: stur            x1, [fp, #-0x48]
    // 0xc13474: StoreField: r1->field_b = r0
    //     0xc13474: stur            w0, [x1, #0xb]
    // 0xc13478: ldur            x0, [fp, #-0x38]
    // 0xc1347c: StoreField: r1->field_13 = r0
    //     0xc1347c: stur            w0, [x1, #0x13]
    // 0xc13480: r0 = Center()
    //     0xc13480: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc13484: mov             x1, x0
    // 0xc13488: r0 = Instance_Alignment
    //     0xc13488: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc1348c: ldr             x0, [x0, #0xb10]
    // 0xc13490: stur            x1, [fp, #-0x28]
    // 0xc13494: StoreField: r1->field_f = r0
    //     0xc13494: stur            w0, [x1, #0xf]
    // 0xc13498: ldur            x0, [fp, #-0x48]
    // 0xc1349c: StoreField: r1->field_b = r0
    //     0xc1349c: stur            w0, [x1, #0xb]
    // 0xc134a0: r0 = Container()
    //     0xc134a0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc134a4: stur            x0, [fp, #-0x38]
    // 0xc134a8: r16 = 34.000000
    //     0xc134a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc134ac: ldr             x16, [x16, #0x978]
    // 0xc134b0: r30 = 34.000000
    //     0xc134b0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xc134b4: ldr             lr, [lr, #0x978]
    // 0xc134b8: stp             lr, x16, [SP, #0x18]
    // 0xc134bc: r16 = Instance_EdgeInsets
    //     0xc134bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xc134c0: ldr             x16, [x16, #0x980]
    // 0xc134c4: ldur            lr, [fp, #-0x30]
    // 0xc134c8: stp             lr, x16, [SP, #8]
    // 0xc134cc: ldur            x16, [fp, #-0x28]
    // 0xc134d0: str             x16, [SP]
    // 0xc134d4: mov             x1, x0
    // 0xc134d8: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xc134d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xc134dc: ldr             x4, [x4, #0x988]
    // 0xc134e0: r0 = Container()
    //     0xc134e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc134e4: ldur            x2, [fp, #-8]
    // 0xc134e8: LoadField: r0 = r2->field_b
    //     0xc134e8: ldur            w0, [x2, #0xb]
    // 0xc134ec: DecompressPointer r0
    //     0xc134ec: add             x0, x0, HEAP, lsl #32
    // 0xc134f0: cmp             w0, NULL
    // 0xc134f4: b.eq            #0xc14300
    // 0xc134f8: LoadField: r3 = r0->field_f
    //     0xc134f8: ldur            w3, [x0, #0xf]
    // 0xc134fc: DecompressPointer r3
    //     0xc134fc: add             x3, x3, HEAP, lsl #32
    // 0xc13500: LoadField: r4 = r2->field_13
    //     0xc13500: ldur            x4, [x2, #0x13]
    // 0xc13504: LoadField: r0 = r3->field_b
    //     0xc13504: ldur            w0, [x3, #0xb]
    // 0xc13508: r1 = LoadInt32Instr(r0)
    //     0xc13508: sbfx            x1, x0, #1, #0x1f
    // 0xc1350c: mov             x0, x1
    // 0xc13510: mov             x1, x4
    // 0xc13514: cmp             x1, x0
    // 0xc13518: b.hs            #0xc14304
    // 0xc1351c: LoadField: r0 = r3->field_f
    //     0xc1351c: ldur            w0, [x3, #0xf]
    // 0xc13520: DecompressPointer r0
    //     0xc13520: add             x0, x0, HEAP, lsl #32
    // 0xc13524: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc13524: add             x16, x0, x4, lsl #2
    //     0xc13528: ldur            w1, [x16, #0xf]
    // 0xc1352c: DecompressPointer r1
    //     0xc1352c: add             x1, x1, HEAP, lsl #32
    // 0xc13530: LoadField: r0 = r1->field_7
    //     0xc13530: ldur            w0, [x1, #7]
    // 0xc13534: DecompressPointer r0
    //     0xc13534: add             x0, x0, HEAP, lsl #32
    // 0xc13538: cmp             w0, NULL
    // 0xc1353c: b.ne            #0xc13548
    // 0xc13540: r3 = ""
    //     0xc13540: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc13544: b               #0xc1354c
    // 0xc13548: mov             x3, x0
    // 0xc1354c: ldur            x0, [fp, #-0x20]
    // 0xc13550: stur            x3, [fp, #-0x28]
    // 0xc13554: LoadField: r1 = r0->field_13
    //     0xc13554: ldur            w1, [x0, #0x13]
    // 0xc13558: DecompressPointer r1
    //     0xc13558: add             x1, x1, HEAP, lsl #32
    // 0xc1355c: r0 = of()
    //     0xc1355c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc13560: LoadField: r1 = r0->field_87
    //     0xc13560: ldur            w1, [x0, #0x87]
    // 0xc13564: DecompressPointer r1
    //     0xc13564: add             x1, x1, HEAP, lsl #32
    // 0xc13568: LoadField: r0 = r1->field_7
    //     0xc13568: ldur            w0, [x1, #7]
    // 0xc1356c: DecompressPointer r0
    //     0xc1356c: add             x0, x0, HEAP, lsl #32
    // 0xc13570: r16 = 14.000000
    //     0xc13570: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc13574: ldr             x16, [x16, #0x1d8]
    // 0xc13578: r30 = Instance_Color
    //     0xc13578: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc1357c: stp             lr, x16, [SP]
    // 0xc13580: mov             x1, x0
    // 0xc13584: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc13584: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc13588: ldr             x4, [x4, #0xaa0]
    // 0xc1358c: r0 = copyWith()
    //     0xc1358c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13590: stur            x0, [fp, #-0x30]
    // 0xc13594: r0 = Text()
    //     0xc13594: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc13598: mov             x2, x0
    // 0xc1359c: ldur            x0, [fp, #-0x28]
    // 0xc135a0: stur            x2, [fp, #-0x48]
    // 0xc135a4: StoreField: r2->field_b = r0
    //     0xc135a4: stur            w0, [x2, #0xb]
    // 0xc135a8: ldur            x0, [fp, #-0x30]
    // 0xc135ac: StoreField: r2->field_13 = r0
    //     0xc135ac: stur            w0, [x2, #0x13]
    // 0xc135b0: ldur            x3, [fp, #-8]
    // 0xc135b4: LoadField: r0 = r3->field_b
    //     0xc135b4: ldur            w0, [x3, #0xb]
    // 0xc135b8: DecompressPointer r0
    //     0xc135b8: add             x0, x0, HEAP, lsl #32
    // 0xc135bc: cmp             w0, NULL
    // 0xc135c0: b.eq            #0xc14308
    // 0xc135c4: LoadField: r4 = r0->field_f
    //     0xc135c4: ldur            w4, [x0, #0xf]
    // 0xc135c8: DecompressPointer r4
    //     0xc135c8: add             x4, x4, HEAP, lsl #32
    // 0xc135cc: LoadField: r5 = r3->field_13
    //     0xc135cc: ldur            x5, [x3, #0x13]
    // 0xc135d0: LoadField: r0 = r4->field_b
    //     0xc135d0: ldur            w0, [x4, #0xb]
    // 0xc135d4: r1 = LoadInt32Instr(r0)
    //     0xc135d4: sbfx            x1, x0, #1, #0x1f
    // 0xc135d8: mov             x0, x1
    // 0xc135dc: mov             x1, x5
    // 0xc135e0: cmp             x1, x0
    // 0xc135e4: b.hs            #0xc1430c
    // 0xc135e8: LoadField: r0 = r4->field_f
    //     0xc135e8: ldur            w0, [x4, #0xf]
    // 0xc135ec: DecompressPointer r0
    //     0xc135ec: add             x0, x0, HEAP, lsl #32
    // 0xc135f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc135f0: add             x16, x0, x5, lsl #2
    //     0xc135f4: ldur            w1, [x16, #0xf]
    // 0xc135f8: DecompressPointer r1
    //     0xc135f8: add             x1, x1, HEAP, lsl #32
    // 0xc135fc: LoadField: r0 = r1->field_1f
    //     0xc135fc: ldur            w0, [x1, #0x1f]
    // 0xc13600: DecompressPointer r0
    //     0xc13600: add             x0, x0, HEAP, lsl #32
    // 0xc13604: cmp             w0, NULL
    // 0xc13608: b.ne            #0xc13614
    // 0xc1360c: r4 = ""
    //     0xc1360c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc13610: b               #0xc13618
    // 0xc13614: mov             x4, x0
    // 0xc13618: ldur            x0, [fp, #-0x20]
    // 0xc1361c: stur            x4, [fp, #-0x28]
    // 0xc13620: LoadField: r1 = r0->field_13
    //     0xc13620: ldur            w1, [x0, #0x13]
    // 0xc13624: DecompressPointer r1
    //     0xc13624: add             x1, x1, HEAP, lsl #32
    // 0xc13628: r0 = of()
    //     0xc13628: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc1362c: LoadField: r1 = r0->field_87
    //     0xc1362c: ldur            w1, [x0, #0x87]
    // 0xc13630: DecompressPointer r1
    //     0xc13630: add             x1, x1, HEAP, lsl #32
    // 0xc13634: LoadField: r0 = r1->field_33
    //     0xc13634: ldur            w0, [x1, #0x33]
    // 0xc13638: DecompressPointer r0
    //     0xc13638: add             x0, x0, HEAP, lsl #32
    // 0xc1363c: stur            x0, [fp, #-0x30]
    // 0xc13640: cmp             w0, NULL
    // 0xc13644: b.ne            #0xc13650
    // 0xc13648: r4 = Null
    //     0xc13648: mov             x4, NULL
    // 0xc1364c: b               #0xc13678
    // 0xc13650: r1 = Instance_Color
    //     0xc13650: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc13654: d0 = 0.500000
    //     0xc13654: fmov            d0, #0.50000000
    // 0xc13658: r0 = withOpacity()
    //     0xc13658: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc1365c: r16 = 10.000000
    //     0xc1365c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc13660: stp             x0, x16, [SP]
    // 0xc13664: ldur            x1, [fp, #-0x30]
    // 0xc13668: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc13668: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc1366c: ldr             x4, [x4, #0xaa0]
    // 0xc13670: r0 = copyWith()
    //     0xc13670: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13674: mov             x4, x0
    // 0xc13678: ldur            x1, [fp, #-8]
    // 0xc1367c: ldur            x3, [fp, #-0x38]
    // 0xc13680: ldur            x0, [fp, #-0x48]
    // 0xc13684: ldur            x2, [fp, #-0x28]
    // 0xc13688: stur            x4, [fp, #-0x30]
    // 0xc1368c: r0 = Text()
    //     0xc1368c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc13690: mov             x1, x0
    // 0xc13694: ldur            x0, [fp, #-0x28]
    // 0xc13698: stur            x1, [fp, #-0x50]
    // 0xc1369c: StoreField: r1->field_b = r0
    //     0xc1369c: stur            w0, [x1, #0xb]
    // 0xc136a0: ldur            x0, [fp, #-0x30]
    // 0xc136a4: StoreField: r1->field_13 = r0
    //     0xc136a4: stur            w0, [x1, #0x13]
    // 0xc136a8: r0 = Padding()
    //     0xc136a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc136ac: mov             x3, x0
    // 0xc136b0: r0 = Instance_EdgeInsets
    //     0xc136b0: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xc136b4: ldr             x0, [x0, #0xe90]
    // 0xc136b8: stur            x3, [fp, #-0x28]
    // 0xc136bc: StoreField: r3->field_f = r0
    //     0xc136bc: stur            w0, [x3, #0xf]
    // 0xc136c0: ldur            x0, [fp, #-0x50]
    // 0xc136c4: StoreField: r3->field_b = r0
    //     0xc136c4: stur            w0, [x3, #0xb]
    // 0xc136c8: r1 = Null
    //     0xc136c8: mov             x1, NULL
    // 0xc136cc: r2 = 4
    //     0xc136cc: movz            x2, #0x4
    // 0xc136d0: r0 = AllocateArray()
    //     0xc136d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc136d4: mov             x2, x0
    // 0xc136d8: ldur            x0, [fp, #-0x48]
    // 0xc136dc: stur            x2, [fp, #-0x30]
    // 0xc136e0: StoreField: r2->field_f = r0
    //     0xc136e0: stur            w0, [x2, #0xf]
    // 0xc136e4: ldur            x0, [fp, #-0x28]
    // 0xc136e8: StoreField: r2->field_13 = r0
    //     0xc136e8: stur            w0, [x2, #0x13]
    // 0xc136ec: r1 = <Widget>
    //     0xc136ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc136f0: r0 = AllocateGrowableArray()
    //     0xc136f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc136f4: mov             x1, x0
    // 0xc136f8: ldur            x0, [fp, #-0x30]
    // 0xc136fc: stur            x1, [fp, #-0x28]
    // 0xc13700: StoreField: r1->field_f = r0
    //     0xc13700: stur            w0, [x1, #0xf]
    // 0xc13704: r2 = 4
    //     0xc13704: movz            x2, #0x4
    // 0xc13708: StoreField: r1->field_b = r2
    //     0xc13708: stur            w2, [x1, #0xb]
    // 0xc1370c: r0 = Column()
    //     0xc1370c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc13710: mov             x3, x0
    // 0xc13714: r0 = Instance_Axis
    //     0xc13714: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc13718: stur            x3, [fp, #-0x30]
    // 0xc1371c: StoreField: r3->field_f = r0
    //     0xc1371c: stur            w0, [x3, #0xf]
    // 0xc13720: r4 = Instance_MainAxisAlignment
    //     0xc13720: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc13724: ldr             x4, [x4, #0xa08]
    // 0xc13728: StoreField: r3->field_13 = r4
    //     0xc13728: stur            w4, [x3, #0x13]
    // 0xc1372c: r5 = Instance_MainAxisSize
    //     0xc1372c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc13730: ldr             x5, [x5, #0xa10]
    // 0xc13734: ArrayStore: r3[0] = r5  ; List_4
    //     0xc13734: stur            w5, [x3, #0x17]
    // 0xc13738: r6 = Instance_CrossAxisAlignment
    //     0xc13738: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc1373c: ldr             x6, [x6, #0x890]
    // 0xc13740: StoreField: r3->field_1b = r6
    //     0xc13740: stur            w6, [x3, #0x1b]
    // 0xc13744: r7 = Instance_VerticalDirection
    //     0xc13744: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc13748: ldr             x7, [x7, #0xa20]
    // 0xc1374c: StoreField: r3->field_23 = r7
    //     0xc1374c: stur            w7, [x3, #0x23]
    // 0xc13750: r8 = Instance_Clip
    //     0xc13750: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc13754: ldr             x8, [x8, #0x38]
    // 0xc13758: StoreField: r3->field_2b = r8
    //     0xc13758: stur            w8, [x3, #0x2b]
    // 0xc1375c: StoreField: r3->field_2f = rZR
    //     0xc1375c: stur            xzr, [x3, #0x2f]
    // 0xc13760: ldur            x1, [fp, #-0x28]
    // 0xc13764: StoreField: r3->field_b = r1
    //     0xc13764: stur            w1, [x3, #0xb]
    // 0xc13768: r1 = Null
    //     0xc13768: mov             x1, NULL
    // 0xc1376c: r2 = 6
    //     0xc1376c: movz            x2, #0x6
    // 0xc13770: r0 = AllocateArray()
    //     0xc13770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc13774: mov             x2, x0
    // 0xc13778: ldur            x0, [fp, #-0x38]
    // 0xc1377c: stur            x2, [fp, #-0x28]
    // 0xc13780: StoreField: r2->field_f = r0
    //     0xc13780: stur            w0, [x2, #0xf]
    // 0xc13784: r16 = Instance_SizedBox
    //     0xc13784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xc13788: ldr             x16, [x16, #0x998]
    // 0xc1378c: StoreField: r2->field_13 = r16
    //     0xc1378c: stur            w16, [x2, #0x13]
    // 0xc13790: ldur            x0, [fp, #-0x30]
    // 0xc13794: ArrayStore: r2[0] = r0  ; List_4
    //     0xc13794: stur            w0, [x2, #0x17]
    // 0xc13798: r1 = <Widget>
    //     0xc13798: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc1379c: r0 = AllocateGrowableArray()
    //     0xc1379c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc137a0: mov             x1, x0
    // 0xc137a4: ldur            x0, [fp, #-0x28]
    // 0xc137a8: stur            x1, [fp, #-0x30]
    // 0xc137ac: StoreField: r1->field_f = r0
    //     0xc137ac: stur            w0, [x1, #0xf]
    // 0xc137b0: r2 = 6
    //     0xc137b0: movz            x2, #0x6
    // 0xc137b4: StoreField: r1->field_b = r2
    //     0xc137b4: stur            w2, [x1, #0xb]
    // 0xc137b8: r0 = Row()
    //     0xc137b8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc137bc: mov             x3, x0
    // 0xc137c0: r2 = Instance_Axis
    //     0xc137c0: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc137c4: stur            x3, [fp, #-0x28]
    // 0xc137c8: StoreField: r3->field_f = r2
    //     0xc137c8: stur            w2, [x3, #0xf]
    // 0xc137cc: r4 = Instance_MainAxisAlignment
    //     0xc137cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc137d0: ldr             x4, [x4, #0xa08]
    // 0xc137d4: StoreField: r3->field_13 = r4
    //     0xc137d4: stur            w4, [x3, #0x13]
    // 0xc137d8: r5 = Instance_MainAxisSize
    //     0xc137d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc137dc: ldr             x5, [x5, #0xa10]
    // 0xc137e0: ArrayStore: r3[0] = r5  ; List_4
    //     0xc137e0: stur            w5, [x3, #0x17]
    // 0xc137e4: r6 = Instance_CrossAxisAlignment
    //     0xc137e4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc137e8: ldr             x6, [x6, #0xa18]
    // 0xc137ec: StoreField: r3->field_1b = r6
    //     0xc137ec: stur            w6, [x3, #0x1b]
    // 0xc137f0: r7 = Instance_VerticalDirection
    //     0xc137f0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc137f4: ldr             x7, [x7, #0xa20]
    // 0xc137f8: StoreField: r3->field_23 = r7
    //     0xc137f8: stur            w7, [x3, #0x23]
    // 0xc137fc: r8 = Instance_Clip
    //     0xc137fc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc13800: ldr             x8, [x8, #0x38]
    // 0xc13804: StoreField: r3->field_2b = r8
    //     0xc13804: stur            w8, [x3, #0x2b]
    // 0xc13808: StoreField: r3->field_2f = rZR
    //     0xc13808: stur            xzr, [x3, #0x2f]
    // 0xc1380c: ldur            x0, [fp, #-0x30]
    // 0xc13810: StoreField: r3->field_b = r0
    //     0xc13810: stur            w0, [x3, #0xb]
    // 0xc13814: ldur            x9, [fp, #-8]
    // 0xc13818: LoadField: r0 = r9->field_b
    //     0xc13818: ldur            w0, [x9, #0xb]
    // 0xc1381c: DecompressPointer r0
    //     0xc1381c: add             x0, x0, HEAP, lsl #32
    // 0xc13820: cmp             w0, NULL
    // 0xc13824: b.eq            #0xc14310
    // 0xc13828: LoadField: r10 = r0->field_f
    //     0xc13828: ldur            w10, [x0, #0xf]
    // 0xc1382c: DecompressPointer r10
    //     0xc1382c: add             x10, x10, HEAP, lsl #32
    // 0xc13830: LoadField: r11 = r9->field_13
    //     0xc13830: ldur            x11, [x9, #0x13]
    // 0xc13834: LoadField: r0 = r10->field_b
    //     0xc13834: ldur            w0, [x10, #0xb]
    // 0xc13838: r1 = LoadInt32Instr(r0)
    //     0xc13838: sbfx            x1, x0, #1, #0x1f
    // 0xc1383c: mov             x0, x1
    // 0xc13840: mov             x1, x11
    // 0xc13844: cmp             x1, x0
    // 0xc13848: b.hs            #0xc14314
    // 0xc1384c: LoadField: r0 = r10->field_f
    //     0xc1384c: ldur            w0, [x10, #0xf]
    // 0xc13850: DecompressPointer r0
    //     0xc13850: add             x0, x0, HEAP, lsl #32
    // 0xc13854: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xc13854: add             x16, x0, x11, lsl #2
    //     0xc13858: ldur            w1, [x16, #0xf]
    // 0xc1385c: DecompressPointer r1
    //     0xc1385c: add             x1, x1, HEAP, lsl #32
    // 0xc13860: LoadField: r0 = r1->field_f
    //     0xc13860: ldur            w0, [x1, #0xf]
    // 0xc13864: DecompressPointer r0
    //     0xc13864: add             x0, x0, HEAP, lsl #32
    // 0xc13868: r1 = 60
    //     0xc13868: movz            x1, #0x3c
    // 0xc1386c: branchIfSmi(r0, 0xc13878)
    //     0xc1386c: tbz             w0, #0, #0xc13878
    // 0xc13870: r1 = LoadClassIdInstr(r0)
    //     0xc13870: ldur            x1, [x0, #-1]
    //     0xc13874: ubfx            x1, x1, #0xc, #0x14
    // 0xc13878: str             x0, [SP]
    // 0xc1387c: mov             x0, x1
    // 0xc13880: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc13880: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc13884: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc13884: movz            x17, #0x2700
    //     0xc13888: add             lr, x0, x17
    //     0xc1388c: ldr             lr, [x21, lr, lsl #3]
    //     0xc13890: blr             lr
    // 0xc13894: mov             x1, x0
    // 0xc13898: r0 = parse()
    //     0xc13898: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc1389c: mov             v1.16b, v0.16b
    // 0xc138a0: d0 = 4.000000
    //     0xc138a0: fmov            d0, #4.00000000
    // 0xc138a4: fcmp            d1, d0
    // 0xc138a8: b.lt            #0xc138b8
    // 0xc138ac: r4 = Instance_Color
    //     0xc138ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc138b0: ldr             x4, [x4, #0x858]
    // 0xc138b4: b               #0xc13a18
    // 0xc138b8: ldur            x2, [fp, #-8]
    // 0xc138bc: LoadField: r0 = r2->field_b
    //     0xc138bc: ldur            w0, [x2, #0xb]
    // 0xc138c0: DecompressPointer r0
    //     0xc138c0: add             x0, x0, HEAP, lsl #32
    // 0xc138c4: cmp             w0, NULL
    // 0xc138c8: b.eq            #0xc14318
    // 0xc138cc: LoadField: r3 = r0->field_f
    //     0xc138cc: ldur            w3, [x0, #0xf]
    // 0xc138d0: DecompressPointer r3
    //     0xc138d0: add             x3, x3, HEAP, lsl #32
    // 0xc138d4: LoadField: r4 = r2->field_13
    //     0xc138d4: ldur            x4, [x2, #0x13]
    // 0xc138d8: LoadField: r0 = r3->field_b
    //     0xc138d8: ldur            w0, [x3, #0xb]
    // 0xc138dc: r1 = LoadInt32Instr(r0)
    //     0xc138dc: sbfx            x1, x0, #1, #0x1f
    // 0xc138e0: mov             x0, x1
    // 0xc138e4: mov             x1, x4
    // 0xc138e8: cmp             x1, x0
    // 0xc138ec: b.hs            #0xc1431c
    // 0xc138f0: LoadField: r0 = r3->field_f
    //     0xc138f0: ldur            w0, [x3, #0xf]
    // 0xc138f4: DecompressPointer r0
    //     0xc138f4: add             x0, x0, HEAP, lsl #32
    // 0xc138f8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc138f8: add             x16, x0, x4, lsl #2
    //     0xc138fc: ldur            w1, [x16, #0xf]
    // 0xc13900: DecompressPointer r1
    //     0xc13900: add             x1, x1, HEAP, lsl #32
    // 0xc13904: LoadField: r0 = r1->field_f
    //     0xc13904: ldur            w0, [x1, #0xf]
    // 0xc13908: DecompressPointer r0
    //     0xc13908: add             x0, x0, HEAP, lsl #32
    // 0xc1390c: r1 = 60
    //     0xc1390c: movz            x1, #0x3c
    // 0xc13910: branchIfSmi(r0, 0xc1391c)
    //     0xc13910: tbz             w0, #0, #0xc1391c
    // 0xc13914: r1 = LoadClassIdInstr(r0)
    //     0xc13914: ldur            x1, [x0, #-1]
    //     0xc13918: ubfx            x1, x1, #0xc, #0x14
    // 0xc1391c: str             x0, [SP]
    // 0xc13920: mov             x0, x1
    // 0xc13924: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc13924: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc13928: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc13928: movz            x17, #0x2700
    //     0xc1392c: add             lr, x0, x17
    //     0xc13930: ldr             lr, [x21, lr, lsl #3]
    //     0xc13934: blr             lr
    // 0xc13938: mov             x1, x0
    // 0xc1393c: r0 = parse()
    //     0xc1393c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc13940: mov             v1.16b, v0.16b
    // 0xc13944: d0 = 3.500000
    //     0xc13944: fmov            d0, #3.50000000
    // 0xc13948: fcmp            d1, d0
    // 0xc1394c: b.lt            #0xc13968
    // 0xc13950: r1 = Instance_Color
    //     0xc13950: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc13954: ldr             x1, [x1, #0x858]
    // 0xc13958: d0 = 0.700000
    //     0xc13958: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc1395c: ldr             d0, [x17, #0xf48]
    // 0xc13960: r0 = withOpacity()
    //     0xc13960: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc13964: b               #0xc13a14
    // 0xc13968: ldur            x2, [fp, #-8]
    // 0xc1396c: LoadField: r0 = r2->field_b
    //     0xc1396c: ldur            w0, [x2, #0xb]
    // 0xc13970: DecompressPointer r0
    //     0xc13970: add             x0, x0, HEAP, lsl #32
    // 0xc13974: cmp             w0, NULL
    // 0xc13978: b.eq            #0xc14320
    // 0xc1397c: LoadField: r3 = r0->field_f
    //     0xc1397c: ldur            w3, [x0, #0xf]
    // 0xc13980: DecompressPointer r3
    //     0xc13980: add             x3, x3, HEAP, lsl #32
    // 0xc13984: LoadField: r4 = r2->field_13
    //     0xc13984: ldur            x4, [x2, #0x13]
    // 0xc13988: LoadField: r0 = r3->field_b
    //     0xc13988: ldur            w0, [x3, #0xb]
    // 0xc1398c: r1 = LoadInt32Instr(r0)
    //     0xc1398c: sbfx            x1, x0, #1, #0x1f
    // 0xc13990: mov             x0, x1
    // 0xc13994: mov             x1, x4
    // 0xc13998: cmp             x1, x0
    // 0xc1399c: b.hs            #0xc14324
    // 0xc139a0: LoadField: r0 = r3->field_f
    //     0xc139a0: ldur            w0, [x3, #0xf]
    // 0xc139a4: DecompressPointer r0
    //     0xc139a4: add             x0, x0, HEAP, lsl #32
    // 0xc139a8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc139a8: add             x16, x0, x4, lsl #2
    //     0xc139ac: ldur            w1, [x16, #0xf]
    // 0xc139b0: DecompressPointer r1
    //     0xc139b0: add             x1, x1, HEAP, lsl #32
    // 0xc139b4: LoadField: r0 = r1->field_f
    //     0xc139b4: ldur            w0, [x1, #0xf]
    // 0xc139b8: DecompressPointer r0
    //     0xc139b8: add             x0, x0, HEAP, lsl #32
    // 0xc139bc: r1 = 60
    //     0xc139bc: movz            x1, #0x3c
    // 0xc139c0: branchIfSmi(r0, 0xc139cc)
    //     0xc139c0: tbz             w0, #0, #0xc139cc
    // 0xc139c4: r1 = LoadClassIdInstr(r0)
    //     0xc139c4: ldur            x1, [x0, #-1]
    //     0xc139c8: ubfx            x1, x1, #0xc, #0x14
    // 0xc139cc: str             x0, [SP]
    // 0xc139d0: mov             x0, x1
    // 0xc139d4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc139d4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc139d8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc139d8: movz            x17, #0x2700
    //     0xc139dc: add             lr, x0, x17
    //     0xc139e0: ldr             lr, [x21, lr, lsl #3]
    //     0xc139e4: blr             lr
    // 0xc139e8: mov             x1, x0
    // 0xc139ec: r0 = parse()
    //     0xc139ec: bl              #0x64333c  ; [dart:core] double::parse
    // 0xc139f0: mov             v1.16b, v0.16b
    // 0xc139f4: d0 = 2.000000
    //     0xc139f4: fmov            d0, #2.00000000
    // 0xc139f8: fcmp            d1, d0
    // 0xc139fc: b.lt            #0xc13a0c
    // 0xc13a00: r0 = Instance_Color
    //     0xc13a00: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xc13a04: ldr             x0, [x0, #0x860]
    // 0xc13a08: b               #0xc13a14
    // 0xc13a0c: r0 = Instance_Color
    //     0xc13a0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xc13a10: ldr             x0, [x0, #0x50]
    // 0xc13a14: mov             x4, x0
    // 0xc13a18: ldur            x0, [fp, #-8]
    // 0xc13a1c: ldur            x2, [fp, #-0x20]
    // 0xc13a20: ldur            x3, [fp, #-0x10]
    // 0xc13a24: ldur            x1, [fp, #-0x28]
    // 0xc13a28: stur            x4, [fp, #-0x30]
    // 0xc13a2c: r0 = ColorFilter()
    //     0xc13a2c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc13a30: mov             x1, x0
    // 0xc13a34: ldur            x0, [fp, #-0x30]
    // 0xc13a38: stur            x1, [fp, #-0x38]
    // 0xc13a3c: StoreField: r1->field_7 = r0
    //     0xc13a3c: stur            w0, [x1, #7]
    // 0xc13a40: r0 = Instance_BlendMode
    //     0xc13a40: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc13a44: ldr             x0, [x0, #0xb30]
    // 0xc13a48: StoreField: r1->field_b = r0
    //     0xc13a48: stur            w0, [x1, #0xb]
    // 0xc13a4c: r0 = 1
    //     0xc13a4c: movz            x0, #0x1
    // 0xc13a50: StoreField: r1->field_13 = r0
    //     0xc13a50: stur            x0, [x1, #0x13]
    // 0xc13a54: r0 = SvgPicture()
    //     0xc13a54: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc13a58: stur            x0, [fp, #-0x30]
    // 0xc13a5c: ldur            x16, [fp, #-0x38]
    // 0xc13a60: str             x16, [SP]
    // 0xc13a64: mov             x1, x0
    // 0xc13a68: r2 = "assets/images/green_star.svg"
    //     0xc13a68: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xc13a6c: ldr             x2, [x2, #0x9a0]
    // 0xc13a70: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc13a70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc13a74: ldr             x4, [x4, #0xa38]
    // 0xc13a78: r0 = SvgPicture.asset()
    //     0xc13a78: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc13a7c: ldur            x2, [fp, #-8]
    // 0xc13a80: LoadField: r0 = r2->field_b
    //     0xc13a80: ldur            w0, [x2, #0xb]
    // 0xc13a84: DecompressPointer r0
    //     0xc13a84: add             x0, x0, HEAP, lsl #32
    // 0xc13a88: cmp             w0, NULL
    // 0xc13a8c: b.eq            #0xc14328
    // 0xc13a90: LoadField: r3 = r0->field_f
    //     0xc13a90: ldur            w3, [x0, #0xf]
    // 0xc13a94: DecompressPointer r3
    //     0xc13a94: add             x3, x3, HEAP, lsl #32
    // 0xc13a98: LoadField: r4 = r2->field_13
    //     0xc13a98: ldur            x4, [x2, #0x13]
    // 0xc13a9c: LoadField: r0 = r3->field_b
    //     0xc13a9c: ldur            w0, [x3, #0xb]
    // 0xc13aa0: r1 = LoadInt32Instr(r0)
    //     0xc13aa0: sbfx            x1, x0, #1, #0x1f
    // 0xc13aa4: mov             x0, x1
    // 0xc13aa8: mov             x1, x4
    // 0xc13aac: cmp             x1, x0
    // 0xc13ab0: b.hs            #0xc1432c
    // 0xc13ab4: LoadField: r0 = r3->field_f
    //     0xc13ab4: ldur            w0, [x3, #0xf]
    // 0xc13ab8: DecompressPointer r0
    //     0xc13ab8: add             x0, x0, HEAP, lsl #32
    // 0xc13abc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xc13abc: add             x16, x0, x4, lsl #2
    //     0xc13ac0: ldur            w1, [x16, #0xf]
    // 0xc13ac4: DecompressPointer r1
    //     0xc13ac4: add             x1, x1, HEAP, lsl #32
    // 0xc13ac8: LoadField: r0 = r1->field_f
    //     0xc13ac8: ldur            w0, [x1, #0xf]
    // 0xc13acc: DecompressPointer r0
    //     0xc13acc: add             x0, x0, HEAP, lsl #32
    // 0xc13ad0: r1 = 60
    //     0xc13ad0: movz            x1, #0x3c
    // 0xc13ad4: branchIfSmi(r0, 0xc13ae0)
    //     0xc13ad4: tbz             w0, #0, #0xc13ae0
    // 0xc13ad8: r1 = LoadClassIdInstr(r0)
    //     0xc13ad8: ldur            x1, [x0, #-1]
    //     0xc13adc: ubfx            x1, x1, #0xc, #0x14
    // 0xc13ae0: str             x0, [SP]
    // 0xc13ae4: mov             x0, x1
    // 0xc13ae8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc13ae8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc13aec: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc13aec: movz            x17, #0x2700
    //     0xc13af0: add             lr, x0, x17
    //     0xc13af4: ldr             lr, [x21, lr, lsl #3]
    //     0xc13af8: blr             lr
    // 0xc13afc: ldur            x2, [fp, #-0x20]
    // 0xc13b00: stur            x0, [fp, #-0x38]
    // 0xc13b04: LoadField: r1 = r2->field_13
    //     0xc13b04: ldur            w1, [x2, #0x13]
    // 0xc13b08: DecompressPointer r1
    //     0xc13b08: add             x1, x1, HEAP, lsl #32
    // 0xc13b0c: r0 = of()
    //     0xc13b0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc13b10: LoadField: r1 = r0->field_87
    //     0xc13b10: ldur            w1, [x0, #0x87]
    // 0xc13b14: DecompressPointer r1
    //     0xc13b14: add             x1, x1, HEAP, lsl #32
    // 0xc13b18: LoadField: r0 = r1->field_7
    //     0xc13b18: ldur            w0, [x1, #7]
    // 0xc13b1c: DecompressPointer r0
    //     0xc13b1c: add             x0, x0, HEAP, lsl #32
    // 0xc13b20: r16 = 12.000000
    //     0xc13b20: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc13b24: ldr             x16, [x16, #0x9e8]
    // 0xc13b28: r30 = Instance_Color
    //     0xc13b28: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc13b2c: stp             lr, x16, [SP]
    // 0xc13b30: mov             x1, x0
    // 0xc13b34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc13b34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc13b38: ldr             x4, [x4, #0xaa0]
    // 0xc13b3c: r0 = copyWith()
    //     0xc13b3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13b40: stur            x0, [fp, #-0x48]
    // 0xc13b44: r0 = Text()
    //     0xc13b44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc13b48: mov             x3, x0
    // 0xc13b4c: ldur            x0, [fp, #-0x38]
    // 0xc13b50: stur            x3, [fp, #-0x50]
    // 0xc13b54: StoreField: r3->field_b = r0
    //     0xc13b54: stur            w0, [x3, #0xb]
    // 0xc13b58: ldur            x0, [fp, #-0x48]
    // 0xc13b5c: StoreField: r3->field_13 = r0
    //     0xc13b5c: stur            w0, [x3, #0x13]
    // 0xc13b60: r1 = Null
    //     0xc13b60: mov             x1, NULL
    // 0xc13b64: r2 = 6
    //     0xc13b64: movz            x2, #0x6
    // 0xc13b68: r0 = AllocateArray()
    //     0xc13b68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc13b6c: mov             x2, x0
    // 0xc13b70: ldur            x0, [fp, #-0x30]
    // 0xc13b74: stur            x2, [fp, #-0x38]
    // 0xc13b78: StoreField: r2->field_f = r0
    //     0xc13b78: stur            w0, [x2, #0xf]
    // 0xc13b7c: r16 = Instance_SizedBox
    //     0xc13b7c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xc13b80: ldr             x16, [x16, #0xe98]
    // 0xc13b84: StoreField: r2->field_13 = r16
    //     0xc13b84: stur            w16, [x2, #0x13]
    // 0xc13b88: ldur            x0, [fp, #-0x50]
    // 0xc13b8c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc13b8c: stur            w0, [x2, #0x17]
    // 0xc13b90: r1 = <Widget>
    //     0xc13b90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc13b94: r0 = AllocateGrowableArray()
    //     0xc13b94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc13b98: mov             x1, x0
    // 0xc13b9c: ldur            x0, [fp, #-0x38]
    // 0xc13ba0: stur            x1, [fp, #-0x30]
    // 0xc13ba4: StoreField: r1->field_f = r0
    //     0xc13ba4: stur            w0, [x1, #0xf]
    // 0xc13ba8: r0 = 6
    //     0xc13ba8: movz            x0, #0x6
    // 0xc13bac: StoreField: r1->field_b = r0
    //     0xc13bac: stur            w0, [x1, #0xb]
    // 0xc13bb0: r0 = Row()
    //     0xc13bb0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc13bb4: mov             x3, x0
    // 0xc13bb8: r0 = Instance_Axis
    //     0xc13bb8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc13bbc: stur            x3, [fp, #-0x38]
    // 0xc13bc0: StoreField: r3->field_f = r0
    //     0xc13bc0: stur            w0, [x3, #0xf]
    // 0xc13bc4: r4 = Instance_MainAxisAlignment
    //     0xc13bc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc13bc8: ldr             x4, [x4, #0xa08]
    // 0xc13bcc: StoreField: r3->field_13 = r4
    //     0xc13bcc: stur            w4, [x3, #0x13]
    // 0xc13bd0: r5 = Instance_MainAxisSize
    //     0xc13bd0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc13bd4: ldr             x5, [x5, #0xa10]
    // 0xc13bd8: ArrayStore: r3[0] = r5  ; List_4
    //     0xc13bd8: stur            w5, [x3, #0x17]
    // 0xc13bdc: r6 = Instance_CrossAxisAlignment
    //     0xc13bdc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc13be0: ldr             x6, [x6, #0xa18]
    // 0xc13be4: StoreField: r3->field_1b = r6
    //     0xc13be4: stur            w6, [x3, #0x1b]
    // 0xc13be8: r7 = Instance_VerticalDirection
    //     0xc13be8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc13bec: ldr             x7, [x7, #0xa20]
    // 0xc13bf0: StoreField: r3->field_23 = r7
    //     0xc13bf0: stur            w7, [x3, #0x23]
    // 0xc13bf4: r8 = Instance_Clip
    //     0xc13bf4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc13bf8: ldr             x8, [x8, #0x38]
    // 0xc13bfc: StoreField: r3->field_2b = r8
    //     0xc13bfc: stur            w8, [x3, #0x2b]
    // 0xc13c00: StoreField: r3->field_2f = rZR
    //     0xc13c00: stur            xzr, [x3, #0x2f]
    // 0xc13c04: ldur            x1, [fp, #-0x30]
    // 0xc13c08: StoreField: r3->field_b = r1
    //     0xc13c08: stur            w1, [x3, #0xb]
    // 0xc13c0c: r1 = Null
    //     0xc13c0c: mov             x1, NULL
    // 0xc13c10: r2 = 4
    //     0xc13c10: movz            x2, #0x4
    // 0xc13c14: r0 = AllocateArray()
    //     0xc13c14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc13c18: mov             x2, x0
    // 0xc13c1c: ldur            x0, [fp, #-0x28]
    // 0xc13c20: stur            x2, [fp, #-0x30]
    // 0xc13c24: StoreField: r2->field_f = r0
    //     0xc13c24: stur            w0, [x2, #0xf]
    // 0xc13c28: ldur            x0, [fp, #-0x38]
    // 0xc13c2c: StoreField: r2->field_13 = r0
    //     0xc13c2c: stur            w0, [x2, #0x13]
    // 0xc13c30: r1 = <Widget>
    //     0xc13c30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc13c34: r0 = AllocateGrowableArray()
    //     0xc13c34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc13c38: mov             x1, x0
    // 0xc13c3c: ldur            x0, [fp, #-0x30]
    // 0xc13c40: stur            x1, [fp, #-0x28]
    // 0xc13c44: StoreField: r1->field_f = r0
    //     0xc13c44: stur            w0, [x1, #0xf]
    // 0xc13c48: r2 = 4
    //     0xc13c48: movz            x2, #0x4
    // 0xc13c4c: StoreField: r1->field_b = r2
    //     0xc13c4c: stur            w2, [x1, #0xb]
    // 0xc13c50: r0 = Row()
    //     0xc13c50: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc13c54: mov             x1, x0
    // 0xc13c58: r0 = Instance_Axis
    //     0xc13c58: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc13c5c: stur            x1, [fp, #-0x30]
    // 0xc13c60: StoreField: r1->field_f = r0
    //     0xc13c60: stur            w0, [x1, #0xf]
    // 0xc13c64: r0 = Instance_MainAxisAlignment
    //     0xc13c64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc13c68: ldr             x0, [x0, #0xa8]
    // 0xc13c6c: StoreField: r1->field_13 = r0
    //     0xc13c6c: stur            w0, [x1, #0x13]
    // 0xc13c70: r0 = Instance_MainAxisSize
    //     0xc13c70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc13c74: ldr             x0, [x0, #0xa10]
    // 0xc13c78: ArrayStore: r1[0] = r0  ; List_4
    //     0xc13c78: stur            w0, [x1, #0x17]
    // 0xc13c7c: r2 = Instance_CrossAxisAlignment
    //     0xc13c7c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc13c80: ldr             x2, [x2, #0xa18]
    // 0xc13c84: StoreField: r1->field_1b = r2
    //     0xc13c84: stur            w2, [x1, #0x1b]
    // 0xc13c88: r3 = Instance_VerticalDirection
    //     0xc13c88: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc13c8c: ldr             x3, [x3, #0xa20]
    // 0xc13c90: StoreField: r1->field_23 = r3
    //     0xc13c90: stur            w3, [x1, #0x23]
    // 0xc13c94: r4 = Instance_Clip
    //     0xc13c94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc13c98: ldr             x4, [x4, #0x38]
    // 0xc13c9c: StoreField: r1->field_2b = r4
    //     0xc13c9c: stur            w4, [x1, #0x2b]
    // 0xc13ca0: StoreField: r1->field_2f = rZR
    //     0xc13ca0: stur            xzr, [x1, #0x2f]
    // 0xc13ca4: ldur            x5, [fp, #-0x28]
    // 0xc13ca8: StoreField: r1->field_b = r5
    //     0xc13ca8: stur            w5, [x1, #0xb]
    // 0xc13cac: r0 = Container()
    //     0xc13cac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc13cb0: stur            x0, [fp, #-0x28]
    // 0xc13cb4: r16 = Instance_BoxDecoration
    //     0xc13cb4: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xc13cb8: ldr             x16, [x16, #0x5a8]
    // 0xc13cbc: r30 = Instance_EdgeInsets
    //     0xc13cbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xc13cc0: ldr             lr, [lr, #0xd0]
    // 0xc13cc4: stp             lr, x16, [SP, #8]
    // 0xc13cc8: ldur            x16, [fp, #-0x30]
    // 0xc13ccc: str             x16, [SP]
    // 0xc13cd0: mov             x1, x0
    // 0xc13cd4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xc13cd4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xc13cd8: ldr             x4, [x4, #0xb40]
    // 0xc13cdc: r0 = Container()
    //     0xc13cdc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc13ce0: ldur            x0, [fp, #-0x10]
    // 0xc13ce4: LoadField: r1 = r0->field_b
    //     0xc13ce4: ldur            w1, [x0, #0xb]
    // 0xc13ce8: LoadField: r2 = r0->field_f
    //     0xc13ce8: ldur            w2, [x0, #0xf]
    // 0xc13cec: DecompressPointer r2
    //     0xc13cec: add             x2, x2, HEAP, lsl #32
    // 0xc13cf0: LoadField: r3 = r2->field_b
    //     0xc13cf0: ldur            w3, [x2, #0xb]
    // 0xc13cf4: r2 = LoadInt32Instr(r1)
    //     0xc13cf4: sbfx            x2, x1, #1, #0x1f
    // 0xc13cf8: stur            x2, [fp, #-0x40]
    // 0xc13cfc: r1 = LoadInt32Instr(r3)
    //     0xc13cfc: sbfx            x1, x3, #1, #0x1f
    // 0xc13d00: cmp             x2, x1
    // 0xc13d04: b.ne            #0xc13d10
    // 0xc13d08: mov             x1, x0
    // 0xc13d0c: r0 = _growToNextCapacity()
    //     0xc13d0c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc13d10: ldur            x2, [fp, #-0x10]
    // 0xc13d14: ldur            x3, [fp, #-0x40]
    // 0xc13d18: add             x0, x3, #1
    // 0xc13d1c: lsl             x1, x0, #1
    // 0xc13d20: StoreField: r2->field_b = r1
    //     0xc13d20: stur            w1, [x2, #0xb]
    // 0xc13d24: LoadField: r1 = r2->field_f
    //     0xc13d24: ldur            w1, [x2, #0xf]
    // 0xc13d28: DecompressPointer r1
    //     0xc13d28: add             x1, x1, HEAP, lsl #32
    // 0xc13d2c: ldur            x0, [fp, #-0x28]
    // 0xc13d30: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc13d30: add             x25, x1, x3, lsl #2
    //     0xc13d34: add             x25, x25, #0xf
    //     0xc13d38: str             w0, [x25]
    //     0xc13d3c: tbz             w0, #0, #0xc13d58
    //     0xc13d40: ldurb           w16, [x1, #-1]
    //     0xc13d44: ldurb           w17, [x0, #-1]
    //     0xc13d48: and             x16, x17, x16, lsr #2
    //     0xc13d4c: tst             x16, HEAP, lsr #32
    //     0xc13d50: b.eq            #0xc13d58
    //     0xc13d54: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc13d58: ldur            x3, [fp, #-8]
    // 0xc13d5c: LoadField: r0 = r3->field_b
    //     0xc13d5c: ldur            w0, [x3, #0xb]
    // 0xc13d60: DecompressPointer r0
    //     0xc13d60: add             x0, x0, HEAP, lsl #32
    // 0xc13d64: cmp             w0, NULL
    // 0xc13d68: b.eq            #0xc14330
    // 0xc13d6c: LoadField: r4 = r0->field_f
    //     0xc13d6c: ldur            w4, [x0, #0xf]
    // 0xc13d70: DecompressPointer r4
    //     0xc13d70: add             x4, x4, HEAP, lsl #32
    // 0xc13d74: LoadField: r5 = r3->field_13
    //     0xc13d74: ldur            x5, [x3, #0x13]
    // 0xc13d78: LoadField: r0 = r4->field_b
    //     0xc13d78: ldur            w0, [x4, #0xb]
    // 0xc13d7c: r1 = LoadInt32Instr(r0)
    //     0xc13d7c: sbfx            x1, x0, #1, #0x1f
    // 0xc13d80: mov             x0, x1
    // 0xc13d84: mov             x1, x5
    // 0xc13d88: cmp             x1, x0
    // 0xc13d8c: b.hs            #0xc14334
    // 0xc13d90: LoadField: r0 = r4->field_f
    //     0xc13d90: ldur            w0, [x4, #0xf]
    // 0xc13d94: DecompressPointer r0
    //     0xc13d94: add             x0, x0, HEAP, lsl #32
    // 0xc13d98: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc13d98: add             x16, x0, x5, lsl #2
    //     0xc13d9c: ldur            w1, [x16, #0xf]
    // 0xc13da0: DecompressPointer r1
    //     0xc13da0: add             x1, x1, HEAP, lsl #32
    // 0xc13da4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc13da4: ldur            w0, [x1, #0x17]
    // 0xc13da8: DecompressPointer r0
    //     0xc13da8: add             x0, x0, HEAP, lsl #32
    // 0xc13dac: cmp             w0, NULL
    // 0xc13db0: b.ne            #0xc13dbc
    // 0xc13db4: r4 = ""
    //     0xc13db4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc13db8: b               #0xc13dc0
    // 0xc13dbc: mov             x4, x0
    // 0xc13dc0: ldur            x0, [fp, #-0x20]
    // 0xc13dc4: stur            x4, [fp, #-0x28]
    // 0xc13dc8: LoadField: r1 = r0->field_13
    //     0xc13dc8: ldur            w1, [x0, #0x13]
    // 0xc13dcc: DecompressPointer r1
    //     0xc13dcc: add             x1, x1, HEAP, lsl #32
    // 0xc13dd0: r0 = of()
    //     0xc13dd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc13dd4: LoadField: r1 = r0->field_87
    //     0xc13dd4: ldur            w1, [x0, #0x87]
    // 0xc13dd8: DecompressPointer r1
    //     0xc13dd8: add             x1, x1, HEAP, lsl #32
    // 0xc13ddc: LoadField: r0 = r1->field_2b
    //     0xc13ddc: ldur            w0, [x1, #0x2b]
    // 0xc13de0: DecompressPointer r0
    //     0xc13de0: add             x0, x0, HEAP, lsl #32
    // 0xc13de4: LoadField: r1 = r0->field_13
    //     0xc13de4: ldur            w1, [x0, #0x13]
    // 0xc13de8: DecompressPointer r1
    //     0xc13de8: add             x1, x1, HEAP, lsl #32
    // 0xc13dec: r16 = Instance_Color
    //     0xc13dec: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc13df0: stp             x16, x1, [SP]
    // 0xc13df4: r1 = Instance_TextStyle
    //     0xc13df4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xc13df8: ldr             x1, [x1, #0x9b0]
    // 0xc13dfc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xc13dfc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xc13e00: ldr             x4, [x4, #0x9b8]
    // 0xc13e04: r0 = copyWith()
    //     0xc13e04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13e08: ldur            x2, [fp, #-0x20]
    // 0xc13e0c: stur            x0, [fp, #-0x30]
    // 0xc13e10: LoadField: r1 = r2->field_13
    //     0xc13e10: ldur            w1, [x2, #0x13]
    // 0xc13e14: DecompressPointer r1
    //     0xc13e14: add             x1, x1, HEAP, lsl #32
    // 0xc13e18: r0 = of()
    //     0xc13e18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc13e1c: LoadField: r1 = r0->field_87
    //     0xc13e1c: ldur            w1, [x0, #0x87]
    // 0xc13e20: DecompressPointer r1
    //     0xc13e20: add             x1, x1, HEAP, lsl #32
    // 0xc13e24: LoadField: r0 = r1->field_7
    //     0xc13e24: ldur            w0, [x1, #7]
    // 0xc13e28: DecompressPointer r0
    //     0xc13e28: add             x0, x0, HEAP, lsl #32
    // 0xc13e2c: r16 = 12.000000
    //     0xc13e2c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc13e30: ldr             x16, [x16, #0x9e8]
    // 0xc13e34: r30 = Instance_Color
    //     0xc13e34: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc13e38: stp             lr, x16, [SP, #8]
    // 0xc13e3c: r16 = Instance_FontWeight
    //     0xc13e3c: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xc13e40: ldr             x16, [x16, #0x20]
    // 0xc13e44: str             x16, [SP]
    // 0xc13e48: mov             x1, x0
    // 0xc13e4c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xc13e4c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xc13e50: ldr             x4, [x4, #0xc48]
    // 0xc13e54: r0 = copyWith()
    //     0xc13e54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13e58: ldur            x2, [fp, #-0x20]
    // 0xc13e5c: stur            x0, [fp, #-0x38]
    // 0xc13e60: LoadField: r1 = r2->field_13
    //     0xc13e60: ldur            w1, [x2, #0x13]
    // 0xc13e64: DecompressPointer r1
    //     0xc13e64: add             x1, x1, HEAP, lsl #32
    // 0xc13e68: r0 = of()
    //     0xc13e68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc13e6c: LoadField: r1 = r0->field_87
    //     0xc13e6c: ldur            w1, [x0, #0x87]
    // 0xc13e70: DecompressPointer r1
    //     0xc13e70: add             x1, x1, HEAP, lsl #32
    // 0xc13e74: LoadField: r0 = r1->field_7
    //     0xc13e74: ldur            w0, [x1, #7]
    // 0xc13e78: DecompressPointer r0
    //     0xc13e78: add             x0, x0, HEAP, lsl #32
    // 0xc13e7c: r16 = 12.000000
    //     0xc13e7c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc13e80: ldr             x16, [x16, #0x9e8]
    // 0xc13e84: r30 = Instance_Color
    //     0xc13e84: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc13e88: stp             lr, x16, [SP, #8]
    // 0xc13e8c: r16 = Instance_FontWeight
    //     0xc13e8c: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xc13e90: ldr             x16, [x16, #0x20]
    // 0xc13e94: str             x16, [SP]
    // 0xc13e98: mov             x1, x0
    // 0xc13e9c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xc13e9c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xc13ea0: ldr             x4, [x4, #0xc48]
    // 0xc13ea4: r0 = copyWith()
    //     0xc13ea4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc13ea8: mov             x1, x0
    // 0xc13eac: ldur            x0, [fp, #-8]
    // 0xc13eb0: stur            x1, [fp, #-0x50]
    // 0xc13eb4: LoadField: r2 = r0->field_27
    //     0xc13eb4: ldur            w2, [x0, #0x27]
    // 0xc13eb8: DecompressPointer r2
    //     0xc13eb8: add             x2, x2, HEAP, lsl #32
    // 0xc13ebc: stur            x2, [fp, #-0x48]
    // 0xc13ec0: r0 = ReadMoreText()
    //     0xc13ec0: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xc13ec4: mov             x1, x0
    // 0xc13ec8: ldur            x0, [fp, #-0x28]
    // 0xc13ecc: stur            x1, [fp, #-8]
    // 0xc13ed0: StoreField: r1->field_3f = r0
    //     0xc13ed0: stur            w0, [x1, #0x3f]
    // 0xc13ed4: ldur            x0, [fp, #-0x48]
    // 0xc13ed8: StoreField: r1->field_b = r0
    //     0xc13ed8: stur            w0, [x1, #0xb]
    // 0xc13edc: r0 = " Read Less"
    //     0xc13edc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xc13ee0: ldr             x0, [x0, #0x9c0]
    // 0xc13ee4: StoreField: r1->field_43 = r0
    //     0xc13ee4: stur            w0, [x1, #0x43]
    // 0xc13ee8: r0 = "Read More"
    //     0xc13ee8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xc13eec: ldr             x0, [x0, #0x9c8]
    // 0xc13ef0: StoreField: r1->field_47 = r0
    //     0xc13ef0: stur            w0, [x1, #0x47]
    // 0xc13ef4: r0 = 240
    //     0xc13ef4: movz            x0, #0xf0
    // 0xc13ef8: StoreField: r1->field_f = r0
    //     0xc13ef8: stur            x0, [x1, #0xf]
    // 0xc13efc: r0 = 2
    //     0xc13efc: movz            x0, #0x2
    // 0xc13f00: ArrayStore: r1[0] = r0  ; List_8
    //     0xc13f00: stur            x0, [x1, #0x17]
    // 0xc13f04: r0 = Instance_TrimMode
    //     0xc13f04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xc13f08: ldr             x0, [x0, #0x9d0]
    // 0xc13f0c: StoreField: r1->field_1f = r0
    //     0xc13f0c: stur            w0, [x1, #0x1f]
    // 0xc13f10: ldur            x0, [fp, #-0x30]
    // 0xc13f14: StoreField: r1->field_4f = r0
    //     0xc13f14: stur            w0, [x1, #0x4f]
    // 0xc13f18: r0 = Instance_TextAlign
    //     0xc13f18: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xc13f1c: StoreField: r1->field_53 = r0
    //     0xc13f1c: stur            w0, [x1, #0x53]
    // 0xc13f20: ldur            x0, [fp, #-0x38]
    // 0xc13f24: StoreField: r1->field_23 = r0
    //     0xc13f24: stur            w0, [x1, #0x23]
    // 0xc13f28: ldur            x0, [fp, #-0x50]
    // 0xc13f2c: StoreField: r1->field_27 = r0
    //     0xc13f2c: stur            w0, [x1, #0x27]
    // 0xc13f30: r0 = "… "
    //     0xc13f30: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xc13f34: ldr             x0, [x0, #0x9d8]
    // 0xc13f38: StoreField: r1->field_3b = r0
    //     0xc13f38: stur            w0, [x1, #0x3b]
    // 0xc13f3c: r0 = true
    //     0xc13f3c: add             x0, NULL, #0x20  ; true
    // 0xc13f40: StoreField: r1->field_37 = r0
    //     0xc13f40: stur            w0, [x1, #0x37]
    // 0xc13f44: r0 = Container()
    //     0xc13f44: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc13f48: stur            x0, [fp, #-0x28]
    // 0xc13f4c: r16 = Instance_EdgeInsets
    //     0xc13f4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc13f50: ldr             x16, [x16, #0x668]
    // 0xc13f54: r30 = Instance_BoxDecoration
    //     0xc13f54: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xc13f58: ldr             lr, [lr, #0x5a8]
    // 0xc13f5c: stp             lr, x16, [SP, #8]
    // 0xc13f60: ldur            x16, [fp, #-8]
    // 0xc13f64: str             x16, [SP]
    // 0xc13f68: mov             x1, x0
    // 0xc13f6c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xc13f6c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xc13f70: ldr             x4, [x4, #0x610]
    // 0xc13f74: r0 = Container()
    //     0xc13f74: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc13f78: r0 = Padding()
    //     0xc13f78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc13f7c: mov             x2, x0
    // 0xc13f80: r0 = Instance_EdgeInsets
    //     0xc13f80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xc13f84: ldr             x0, [x0, #0xa00]
    // 0xc13f88: stur            x2, [fp, #-8]
    // 0xc13f8c: StoreField: r2->field_f = r0
    //     0xc13f8c: stur            w0, [x2, #0xf]
    // 0xc13f90: ldur            x0, [fp, #-0x28]
    // 0xc13f94: StoreField: r2->field_b = r0
    //     0xc13f94: stur            w0, [x2, #0xb]
    // 0xc13f98: ldur            x0, [fp, #-0x10]
    // 0xc13f9c: LoadField: r1 = r0->field_b
    //     0xc13f9c: ldur            w1, [x0, #0xb]
    // 0xc13fa0: LoadField: r3 = r0->field_f
    //     0xc13fa0: ldur            w3, [x0, #0xf]
    // 0xc13fa4: DecompressPointer r3
    //     0xc13fa4: add             x3, x3, HEAP, lsl #32
    // 0xc13fa8: LoadField: r4 = r3->field_b
    //     0xc13fa8: ldur            w4, [x3, #0xb]
    // 0xc13fac: r3 = LoadInt32Instr(r1)
    //     0xc13fac: sbfx            x3, x1, #1, #0x1f
    // 0xc13fb0: stur            x3, [fp, #-0x40]
    // 0xc13fb4: r1 = LoadInt32Instr(r4)
    //     0xc13fb4: sbfx            x1, x4, #1, #0x1f
    // 0xc13fb8: cmp             x3, x1
    // 0xc13fbc: b.ne            #0xc13fc8
    // 0xc13fc0: mov             x1, x0
    // 0xc13fc4: r0 = _growToNextCapacity()
    //     0xc13fc4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc13fc8: ldur            x2, [fp, #-0x10]
    // 0xc13fcc: ldur            x3, [fp, #-0x40]
    // 0xc13fd0: add             x0, x3, #1
    // 0xc13fd4: lsl             x1, x0, #1
    // 0xc13fd8: StoreField: r2->field_b = r1
    //     0xc13fd8: stur            w1, [x2, #0xb]
    // 0xc13fdc: LoadField: r1 = r2->field_f
    //     0xc13fdc: ldur            w1, [x2, #0xf]
    // 0xc13fe0: DecompressPointer r1
    //     0xc13fe0: add             x1, x1, HEAP, lsl #32
    // 0xc13fe4: ldur            x0, [fp, #-8]
    // 0xc13fe8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc13fe8: add             x25, x1, x3, lsl #2
    //     0xc13fec: add             x25, x25, #0xf
    //     0xc13ff0: str             w0, [x25]
    //     0xc13ff4: tbz             w0, #0, #0xc14010
    //     0xc13ff8: ldurb           w16, [x1, #-1]
    //     0xc13ffc: ldurb           w17, [x0, #-1]
    //     0xc14000: and             x16, x17, x16, lsr #2
    //     0xc14004: tst             x16, HEAP, lsr #32
    //     0xc14008: b.eq            #0xc14010
    //     0xc1400c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc14010: r0 = GestureDetector()
    //     0xc14010: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xc14014: ldur            x2, [fp, #-0x20]
    // 0xc14018: r1 = Function '<anonymous closure>':.
    //     0xc14018: add             x1, PP, #0x51, lsl #12  ; [pp+0x51fd8] AnonymousClosure: (0xaa5490), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xc1401c: ldr             x1, [x1, #0xfd8]
    // 0xc14020: stur            x0, [fp, #-8]
    // 0xc14024: r0 = AllocateClosure()
    //     0xc14024: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc14028: ldur            x2, [fp, #-0x20]
    // 0xc1402c: r1 = Function '<anonymous closure>':.
    //     0xc1402c: add             x1, PP, #0x51, lsl #12  ; [pp+0x51fe0] AnonymousClosure: (0xc14338), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xc14030: ldr             x1, [x1, #0xfe0]
    // 0xc14034: stur            x0, [fp, #-0x20]
    // 0xc14038: r0 = AllocateClosure()
    //     0xc14038: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc1403c: ldur            x16, [fp, #-0x20]
    // 0xc14040: stp             x0, x16, [SP, #8]
    // 0xc14044: r16 = Instance_Icon
    //     0xc14044: add             x16, PP, #0x51, lsl #12  ; [pp+0x51eb8] Obj!Icon@d66531
    //     0xc14048: ldr             x16, [x16, #0xeb8]
    // 0xc1404c: str             x16, [SP]
    // 0xc14050: ldur            x1, [fp, #-8]
    // 0xc14054: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xc14054: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xc14058: ldr             x4, [x4, #0xa20]
    // 0xc1405c: r0 = GestureDetector()
    //     0xc1405c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xc14060: r0 = Align()
    //     0xc14060: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc14064: mov             x1, x0
    // 0xc14068: r0 = Instance_Alignment
    //     0xc14068: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xc1406c: ldr             x0, [x0, #0xa78]
    // 0xc14070: stur            x1, [fp, #-0x20]
    // 0xc14074: StoreField: r1->field_f = r0
    //     0xc14074: stur            w0, [x1, #0xf]
    // 0xc14078: ldur            x0, [fp, #-8]
    // 0xc1407c: StoreField: r1->field_b = r0
    //     0xc1407c: stur            w0, [x1, #0xb]
    // 0xc14080: r0 = Padding()
    //     0xc14080: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc14084: mov             x2, x0
    // 0xc14088: r0 = Instance_EdgeInsets
    //     0xc14088: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ec0] Obj!EdgeInsets@d58af1
    //     0xc1408c: ldr             x0, [x0, #0xec0]
    // 0xc14090: stur            x2, [fp, #-8]
    // 0xc14094: StoreField: r2->field_f = r0
    //     0xc14094: stur            w0, [x2, #0xf]
    // 0xc14098: ldur            x0, [fp, #-0x20]
    // 0xc1409c: StoreField: r2->field_b = r0
    //     0xc1409c: stur            w0, [x2, #0xb]
    // 0xc140a0: ldur            x0, [fp, #-0x10]
    // 0xc140a4: LoadField: r1 = r0->field_b
    //     0xc140a4: ldur            w1, [x0, #0xb]
    // 0xc140a8: LoadField: r3 = r0->field_f
    //     0xc140a8: ldur            w3, [x0, #0xf]
    // 0xc140ac: DecompressPointer r3
    //     0xc140ac: add             x3, x3, HEAP, lsl #32
    // 0xc140b0: LoadField: r4 = r3->field_b
    //     0xc140b0: ldur            w4, [x3, #0xb]
    // 0xc140b4: r3 = LoadInt32Instr(r1)
    //     0xc140b4: sbfx            x3, x1, #1, #0x1f
    // 0xc140b8: stur            x3, [fp, #-0x40]
    // 0xc140bc: r1 = LoadInt32Instr(r4)
    //     0xc140bc: sbfx            x1, x4, #1, #0x1f
    // 0xc140c0: cmp             x3, x1
    // 0xc140c4: b.ne            #0xc140d0
    // 0xc140c8: mov             x1, x0
    // 0xc140cc: r0 = _growToNextCapacity()
    //     0xc140cc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc140d0: ldur            x4, [fp, #-0x18]
    // 0xc140d4: ldur            x2, [fp, #-0x10]
    // 0xc140d8: ldur            x3, [fp, #-0x40]
    // 0xc140dc: add             x0, x3, #1
    // 0xc140e0: lsl             x1, x0, #1
    // 0xc140e4: StoreField: r2->field_b = r1
    //     0xc140e4: stur            w1, [x2, #0xb]
    // 0xc140e8: LoadField: r1 = r2->field_f
    //     0xc140e8: ldur            w1, [x2, #0xf]
    // 0xc140ec: DecompressPointer r1
    //     0xc140ec: add             x1, x1, HEAP, lsl #32
    // 0xc140f0: ldur            x0, [fp, #-8]
    // 0xc140f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc140f4: add             x25, x1, x3, lsl #2
    //     0xc140f8: add             x25, x25, #0xf
    //     0xc140fc: str             w0, [x25]
    //     0xc14100: tbz             w0, #0, #0xc1411c
    //     0xc14104: ldurb           w16, [x1, #-1]
    //     0xc14108: ldurb           w17, [x0, #-1]
    //     0xc1410c: and             x16, x17, x16, lsr #2
    //     0xc14110: tst             x16, HEAP, lsr #32
    //     0xc14114: b.eq            #0xc1411c
    //     0xc14118: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc1411c: r0 = Column()
    //     0xc1411c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc14120: mov             x3, x0
    // 0xc14124: r0 = Instance_Axis
    //     0xc14124: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc14128: stur            x3, [fp, #-8]
    // 0xc1412c: StoreField: r3->field_f = r0
    //     0xc1412c: stur            w0, [x3, #0xf]
    // 0xc14130: r4 = Instance_MainAxisAlignment
    //     0xc14130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc14134: ldr             x4, [x4, #0xa08]
    // 0xc14138: StoreField: r3->field_13 = r4
    //     0xc14138: stur            w4, [x3, #0x13]
    // 0xc1413c: r5 = Instance_MainAxisSize
    //     0xc1413c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc14140: ldr             x5, [x5, #0xa10]
    // 0xc14144: ArrayStore: r3[0] = r5  ; List_4
    //     0xc14144: stur            w5, [x3, #0x17]
    // 0xc14148: r1 = Instance_CrossAxisAlignment
    //     0xc14148: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc1414c: ldr             x1, [x1, #0x890]
    // 0xc14150: StoreField: r3->field_1b = r1
    //     0xc14150: stur            w1, [x3, #0x1b]
    // 0xc14154: r6 = Instance_VerticalDirection
    //     0xc14154: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc14158: ldr             x6, [x6, #0xa20]
    // 0xc1415c: StoreField: r3->field_23 = r6
    //     0xc1415c: stur            w6, [x3, #0x23]
    // 0xc14160: r7 = Instance_Clip
    //     0xc14160: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc14164: ldr             x7, [x7, #0x38]
    // 0xc14168: StoreField: r3->field_2b = r7
    //     0xc14168: stur            w7, [x3, #0x2b]
    // 0xc1416c: StoreField: r3->field_2f = rZR
    //     0xc1416c: stur            xzr, [x3, #0x2f]
    // 0xc14170: ldur            x1, [fp, #-0x10]
    // 0xc14174: StoreField: r3->field_b = r1
    //     0xc14174: stur            w1, [x3, #0xb]
    // 0xc14178: r1 = Null
    //     0xc14178: mov             x1, NULL
    // 0xc1417c: r2 = 4
    //     0xc1417c: movz            x2, #0x4
    // 0xc14180: r0 = AllocateArray()
    //     0xc14180: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc14184: mov             x2, x0
    // 0xc14188: ldur            x0, [fp, #-0x18]
    // 0xc1418c: stur            x2, [fp, #-0x10]
    // 0xc14190: StoreField: r2->field_f = r0
    //     0xc14190: stur            w0, [x2, #0xf]
    // 0xc14194: ldur            x0, [fp, #-8]
    // 0xc14198: StoreField: r2->field_13 = r0
    //     0xc14198: stur            w0, [x2, #0x13]
    // 0xc1419c: r1 = <Widget>
    //     0xc1419c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc141a0: r0 = AllocateGrowableArray()
    //     0xc141a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc141a4: mov             x1, x0
    // 0xc141a8: ldur            x0, [fp, #-0x10]
    // 0xc141ac: stur            x1, [fp, #-8]
    // 0xc141b0: StoreField: r1->field_f = r0
    //     0xc141b0: stur            w0, [x1, #0xf]
    // 0xc141b4: r0 = 4
    //     0xc141b4: movz            x0, #0x4
    // 0xc141b8: StoreField: r1->field_b = r0
    //     0xc141b8: stur            w0, [x1, #0xb]
    // 0xc141bc: r0 = Column()
    //     0xc141bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc141c0: mov             x1, x0
    // 0xc141c4: r0 = Instance_Axis
    //     0xc141c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc141c8: stur            x1, [fp, #-0x10]
    // 0xc141cc: StoreField: r1->field_f = r0
    //     0xc141cc: stur            w0, [x1, #0xf]
    // 0xc141d0: r0 = Instance_MainAxisAlignment
    //     0xc141d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc141d4: ldr             x0, [x0, #0xa08]
    // 0xc141d8: StoreField: r1->field_13 = r0
    //     0xc141d8: stur            w0, [x1, #0x13]
    // 0xc141dc: r0 = Instance_MainAxisSize
    //     0xc141dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc141e0: ldr             x0, [x0, #0xa10]
    // 0xc141e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xc141e4: stur            w0, [x1, #0x17]
    // 0xc141e8: r0 = Instance_CrossAxisAlignment
    //     0xc141e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc141ec: ldr             x0, [x0, #0xa18]
    // 0xc141f0: StoreField: r1->field_1b = r0
    //     0xc141f0: stur            w0, [x1, #0x1b]
    // 0xc141f4: r0 = Instance_VerticalDirection
    //     0xc141f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc141f8: ldr             x0, [x0, #0xa20]
    // 0xc141fc: StoreField: r1->field_23 = r0
    //     0xc141fc: stur            w0, [x1, #0x23]
    // 0xc14200: r0 = Instance_Clip
    //     0xc14200: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc14204: ldr             x0, [x0, #0x38]
    // 0xc14208: StoreField: r1->field_2b = r0
    //     0xc14208: stur            w0, [x1, #0x2b]
    // 0xc1420c: StoreField: r1->field_2f = rZR
    //     0xc1420c: stur            xzr, [x1, #0x2f]
    // 0xc14210: ldur            x0, [fp, #-8]
    // 0xc14214: StoreField: r1->field_b = r0
    //     0xc14214: stur            w0, [x1, #0xb]
    // 0xc14218: r0 = SafeArea()
    //     0xc14218: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xc1421c: mov             x1, x0
    // 0xc14220: r0 = true
    //     0xc14220: add             x0, NULL, #0x20  ; true
    // 0xc14224: stur            x1, [fp, #-8]
    // 0xc14228: StoreField: r1->field_b = r0
    //     0xc14228: stur            w0, [x1, #0xb]
    // 0xc1422c: StoreField: r1->field_f = r0
    //     0xc1422c: stur            w0, [x1, #0xf]
    // 0xc14230: StoreField: r1->field_13 = r0
    //     0xc14230: stur            w0, [x1, #0x13]
    // 0xc14234: ArrayStore: r1[0] = r0  ; List_4
    //     0xc14234: stur            w0, [x1, #0x17]
    // 0xc14238: r2 = Instance_EdgeInsets
    //     0xc14238: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc1423c: StoreField: r1->field_1b = r2
    //     0xc1423c: stur            w2, [x1, #0x1b]
    // 0xc14240: r2 = false
    //     0xc14240: add             x2, NULL, #0x30  ; false
    // 0xc14244: StoreField: r1->field_1f = r2
    //     0xc14244: stur            w2, [x1, #0x1f]
    // 0xc14248: ldur            x3, [fp, #-0x10]
    // 0xc1424c: StoreField: r1->field_23 = r3
    //     0xc1424c: stur            w3, [x1, #0x23]
    // 0xc14250: r0 = Scaffold()
    //     0xc14250: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xc14254: ldur            x1, [fp, #-8]
    // 0xc14258: ArrayStore: r0[0] = r1  ; List_4
    //     0xc14258: stur            w1, [x0, #0x17]
    // 0xc1425c: r1 = Instance_Color
    //     0xc1425c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc14260: StoreField: r0->field_33 = r1
    //     0xc14260: stur            w1, [x0, #0x33]
    // 0xc14264: r1 = true
    //     0xc14264: add             x1, NULL, #0x20  ; true
    // 0xc14268: StoreField: r0->field_43 = r1
    //     0xc14268: stur            w1, [x0, #0x43]
    // 0xc1426c: r1 = false
    //     0xc1426c: add             x1, NULL, #0x30  ; false
    // 0xc14270: StoreField: r0->field_b = r1
    //     0xc14270: stur            w1, [x0, #0xb]
    // 0xc14274: StoreField: r0->field_f = r1
    //     0xc14274: stur            w1, [x0, #0xf]
    // 0xc14278: LeaveFrame
    //     0xc14278: mov             SP, fp
    //     0xc1427c: ldp             fp, lr, [SP], #0x10
    // 0xc14280: ret
    //     0xc14280: ret             
    // 0xc14284: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc14284: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc14288: b               #0xc12224
    // 0xc1428c: r9 = _pageController
    //     0xc1428c: add             x9, PP, #0x51, lsl #12  ; [pp+0x51fe8] Field <_RatingReviewAllMediaOnTapImageState@1749268759._pageController@1749268759>: late (offset: 0x24)
    //     0xc14290: ldr             x9, [x9, #0xfe8]
    // 0xc14294: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc14294: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc14298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14298: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1429c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1429c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc142a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc142a4: b               #0xc12368
    // 0xc142a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142d0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc142d0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xc142d4: r0 = RangeErrorSharedWithFPURegs()
    //     0xc142d4: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xc142d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc142e8: SaveReg d0
    //     0xc142e8: str             q0, [SP, #-0x10]!
    // 0xc142ec: r0 = AllocateDouble()
    //     0xc142ec: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc142f0: RestoreReg d0
    //     0xc142f0: ldr             q0, [SP], #0x10
    // 0xc142f4: b               #0xc131d4
    // 0xc142f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc142f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc142fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc142fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc14304: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc14304: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1430c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1430c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14310: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14310: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc14314: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc14314: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14318: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14318: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1431c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1431c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14320: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14320: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc14324: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc14324: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14328: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14328: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1432c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc1432c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc14330: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14330: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc14334: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc14334: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc14338, size: 0xd0
    // 0xc14338: EnterFrame
    //     0xc14338: stp             fp, lr, [SP, #-0x10]!
    //     0xc1433c: mov             fp, SP
    // 0xc14340: ldr             x0, [fp, #0x10]
    // 0xc14344: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc14344: ldur            w1, [x0, #0x17]
    // 0xc14348: DecompressPointer r1
    //     0xc14348: add             x1, x1, HEAP, lsl #32
    // 0xc1434c: CheckStackOverflow
    //     0xc1434c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc14350: cmp             SP, x16
    //     0xc14354: b.ls            #0xc143f8
    // 0xc14358: LoadField: r2 = r1->field_f
    //     0xc14358: ldur            w2, [x1, #0xf]
    // 0xc1435c: DecompressPointer r2
    //     0xc1435c: add             x2, x2, HEAP, lsl #32
    // 0xc14360: LoadField: r3 = r2->field_33
    //     0xc14360: ldur            w3, [x2, #0x33]
    // 0xc14364: DecompressPointer r3
    //     0xc14364: add             x3, x3, HEAP, lsl #32
    // 0xc14368: cmp             w3, NULL
    // 0xc1436c: b.eq            #0xc143e8
    // 0xc14370: LoadField: r4 = r1->field_13
    //     0xc14370: ldur            w4, [x1, #0x13]
    // 0xc14374: DecompressPointer r4
    //     0xc14374: add             x4, x4, HEAP, lsl #32
    // 0xc14378: LoadField: r0 = r2->field_b
    //     0xc14378: ldur            w0, [x2, #0xb]
    // 0xc1437c: DecompressPointer r0
    //     0xc1437c: add             x0, x0, HEAP, lsl #32
    // 0xc14380: cmp             w0, NULL
    // 0xc14384: b.eq            #0xc14400
    // 0xc14388: LoadField: r5 = r0->field_f
    //     0xc14388: ldur            w5, [x0, #0xf]
    // 0xc1438c: DecompressPointer r5
    //     0xc1438c: add             x5, x5, HEAP, lsl #32
    // 0xc14390: LoadField: r6 = r2->field_13
    //     0xc14390: ldur            x6, [x2, #0x13]
    // 0xc14394: LoadField: r0 = r5->field_b
    //     0xc14394: ldur            w0, [x5, #0xb]
    // 0xc14398: r1 = LoadInt32Instr(r0)
    //     0xc14398: sbfx            x1, x0, #1, #0x1f
    // 0xc1439c: mov             x0, x1
    // 0xc143a0: mov             x1, x6
    // 0xc143a4: cmp             x1, x0
    // 0xc143a8: b.hs            #0xc14404
    // 0xc143ac: LoadField: r0 = r5->field_f
    //     0xc143ac: ldur            w0, [x5, #0xf]
    // 0xc143b0: DecompressPointer r0
    //     0xc143b0: add             x0, x0, HEAP, lsl #32
    // 0xc143b4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xc143b4: add             x16, x0, x6, lsl #2
    //     0xc143b8: ldur            w1, [x16, #0xf]
    // 0xc143bc: DecompressPointer r1
    //     0xc143bc: add             x1, x1, HEAP, lsl #32
    // 0xc143c0: LoadField: r0 = r1->field_b
    //     0xc143c0: ldur            w0, [x1, #0xb]
    // 0xc143c4: DecompressPointer r0
    //     0xc143c4: add             x0, x0, HEAP, lsl #32
    // 0xc143c8: cmp             w0, NULL
    // 0xc143cc: b.ne            #0xc143d8
    // 0xc143d0: r5 = ""
    //     0xc143d0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc143d4: b               #0xc143dc
    // 0xc143d8: mov             x5, x0
    // 0xc143dc: mov             x1, x2
    // 0xc143e0: mov             x2, x4
    // 0xc143e4: r0 = showMenuItem()
    //     0xc143e4: bl              #0xaa4c8c  ; [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem
    // 0xc143e8: r0 = Null
    //     0xc143e8: mov             x0, NULL
    // 0xc143ec: LeaveFrame
    //     0xc143ec: mov             SP, fp
    //     0xc143f0: ldp             fp, lr, [SP], #0x10
    // 0xc143f4: ret
    //     0xc143f4: ret             
    // 0xc143f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc143f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc143fc: b               #0xc14358
    // 0xc14400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc14400: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc14404: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc14404: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc885dc, size: 0x8c
    // 0xc885dc: EnterFrame
    //     0xc885dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc885e0: mov             fp, SP
    // 0xc885e4: AllocStack(0x10)
    //     0xc885e4: sub             SP, SP, #0x10
    // 0xc885e8: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r2, fp-0x8 */)
    //     0xc885e8: mov             x2, x1
    //     0xc885ec: stur            x1, [fp, #-8]
    // 0xc885f0: CheckStackOverflow
    //     0xc885f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc885f4: cmp             SP, x16
    //     0xc885f8: b.ls            #0xc88654
    // 0xc885fc: LoadField: r1 = r2->field_23
    //     0xc885fc: ldur            w1, [x2, #0x23]
    // 0xc88600: DecompressPointer r1
    //     0xc88600: add             x1, x1, HEAP, lsl #32
    // 0xc88604: r16 = Sentinel
    //     0xc88604: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88608: cmp             w1, w16
    // 0xc8860c: b.eq            #0xc8865c
    // 0xc88610: r0 = dispose()
    //     0xc88610: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc88614: ldur            x2, [fp, #-8]
    // 0xc88618: LoadField: r0 = r2->field_27
    //     0xc88618: ldur            w0, [x2, #0x27]
    // 0xc8861c: DecompressPointer r0
    //     0xc8861c: add             x0, x0, HEAP, lsl #32
    // 0xc88620: stur            x0, [fp, #-0x10]
    // 0xc88624: r1 = Function '_onCollapseChanged@1749268759':.
    //     0xc88624: add             x1, PP, #0x52, lsl #12  ; [pp+0x52058] AnonymousClosure: (0x935878), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x935814)
    //     0xc88628: ldr             x1, [x1, #0x58]
    // 0xc8862c: r0 = AllocateClosure()
    //     0xc8862c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc88630: ldur            x1, [fp, #-0x10]
    // 0xc88634: mov             x2, x0
    // 0xc88638: r0 = removeListener()
    //     0xc88638: bl              #0x7b91b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc8863c: ldur            x1, [fp, #-0x10]
    // 0xc88640: r0 = dispose()
    //     0xc88640: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc88644: r0 = Null
    //     0xc88644: mov             x0, NULL
    // 0xc88648: LeaveFrame
    //     0xc88648: mov             SP, fp
    //     0xc8864c: ldp             fp, lr, [SP], #0x10
    // 0xc88650: ret
    //     0xc88650: ret             
    // 0xc88654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88654: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88658: b               #0xc885fc
    // 0xc8865c: r9 = _pageController
    //     0xc8865c: add             x9, PP, #0x51, lsl #12  ; [pp+0x51fe8] Field <_RatingReviewAllMediaOnTapImageState@1749268759._pageController@1749268759>: late (offset: 0x24)
    //     0xc88660: ldr             x9, [x9, #0xfe8]
    // 0xc88664: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc88664: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3956, size: 0x28, field offset: 0xc
class RatingReviewAllMediaOnTapImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc814b0, size: 0x48
    // 0xc814b0: EnterFrame
    //     0xc814b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc814b4: mov             fp, SP
    // 0xc814b8: AllocStack(0x8)
    //     0xc814b8: sub             SP, SP, #8
    // 0xc814bc: CheckStackOverflow
    //     0xc814bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc814c0: cmp             SP, x16
    //     0xc814c4: b.ls            #0xc814f0
    // 0xc814c8: r1 = <RatingReviewAllMediaOnTapImage>
    //     0xc814c8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48250] TypeArguments: <RatingReviewAllMediaOnTapImage>
    //     0xc814cc: ldr             x1, [x1, #0x250]
    // 0xc814d0: r0 = _RatingReviewAllMediaOnTapImageState()
    //     0xc814d0: bl              #0xc814f8  ; Allocate_RatingReviewAllMediaOnTapImageStateStub -> _RatingReviewAllMediaOnTapImageState (size=0x40)
    // 0xc814d4: mov             x1, x0
    // 0xc814d8: stur            x0, [fp, #-8]
    // 0xc814dc: r0 = _RatingReviewAllMediaOnTapImageState()
    //     0xc814dc: bl              #0xc7c8bc  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_RatingReviewAllMediaOnTapImageState
    // 0xc814e0: ldur            x0, [fp, #-8]
    // 0xc814e4: LeaveFrame
    //     0xc814e4: mov             SP, fp
    //     0xc814e8: ldp             fp, lr, [SP], #0x10
    // 0xc814ec: ret
    //     0xc814ec: ret             
    // 0xc814f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc814f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc814f4: b               #0xc814c8
  }
}
