// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart

// class id: 1049355, size: 0x8
class :: {
}

// class id: 4577, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestNumberPage extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x12e7054, size: 0x158
    // 0x12e7054: EnterFrame
    //     0x12e7054: stp             fp, lr, [SP, #-0x10]!
    //     0x12e7058: mov             fp, SP
    // 0x12e705c: AllocStack(0x38)
    //     0x12e705c: sub             SP, SP, #0x38
    // 0x12e7060: SetupParameters()
    //     0x12e7060: ldr             x0, [fp, #0x10]
    //     0x12e7064: ldur            w2, [x0, #0x17]
    //     0x12e7068: add             x2, x2, HEAP, lsl #32
    //     0x12e706c: stur            x2, [fp, #-8]
    // 0x12e7070: CheckStackOverflow
    //     0x12e7070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12e7074: cmp             SP, x16
    //     0x12e7078: b.ls            #0x12e71a4
    // 0x12e707c: LoadField: r1 = r2->field_f
    //     0x12e707c: ldur            w1, [x2, #0xf]
    // 0x12e7080: DecompressPointer r1
    //     0x12e7080: add             x1, x1, HEAP, lsl #32
    // 0x12e7084: r0 = controller()
    //     0x12e7084: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e7088: LoadField: r1 = r0->field_53
    //     0x12e7088: ldur            w1, [x0, #0x53]
    // 0x12e708c: DecompressPointer r1
    //     0x12e708c: add             x1, x1, HEAP, lsl #32
    // 0x12e7090: r0 = value()
    //     0x12e7090: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12e7094: LoadField: r1 = r0->field_3f
    //     0x12e7094: ldur            w1, [x0, #0x3f]
    // 0x12e7098: DecompressPointer r1
    //     0x12e7098: add             x1, x1, HEAP, lsl #32
    // 0x12e709c: cmp             w1, NULL
    // 0x12e70a0: b.ne            #0x12e70ac
    // 0x12e70a4: r0 = Null
    //     0x12e70a4: mov             x0, NULL
    // 0x12e70a8: b               #0x12e70b4
    // 0x12e70ac: LoadField: r0 = r1->field_37
    //     0x12e70ac: ldur            w0, [x1, #0x37]
    // 0x12e70b0: DecompressPointer r0
    //     0x12e70b0: add             x0, x0, HEAP, lsl #32
    // 0x12e70b4: r1 = LoadClassIdInstr(r0)
    //     0x12e70b4: ldur            x1, [x0, #-1]
    //     0x12e70b8: ubfx            x1, x1, #0xc, #0x14
    // 0x12e70bc: r16 = "IQHEcgdx"
    //     0x12e70bc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d8f8] "IQHEcgdx"
    //     0x12e70c0: ldr             x16, [x16, #0x8f8]
    // 0x12e70c4: stp             x16, x0, [SP]
    // 0x12e70c8: mov             x0, x1
    // 0x12e70cc: mov             lr, x0
    // 0x12e70d0: ldr             lr, [x21, lr, lsl #3]
    // 0x12e70d4: blr             lr
    // 0x12e70d8: tbnz            w0, #4, #0x12e70f8
    // 0x12e70dc: ldur            x0, [fp, #-8]
    // 0x12e70e0: LoadField: r1 = r0->field_f
    //     0x12e70e0: ldur            w1, [x0, #0xf]
    // 0x12e70e4: DecompressPointer r1
    //     0x12e70e4: add             x1, x1, HEAP, lsl #32
    // 0x12e70e8: r0 = controller()
    //     0x12e70e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e70ec: mov             x1, x0
    // 0x12e70f0: r0 = openAddressPage()
    //     0x12e70f0: bl              #0x12ea18c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::openAddressPage
    // 0x12e70f4: b               #0x12e7194
    // 0x12e70f8: ldur            x0, [fp, #-8]
    // 0x12e70fc: LoadField: r1 = r0->field_f
    //     0x12e70fc: ldur            w1, [x0, #0xf]
    // 0x12e7100: DecompressPointer r1
    //     0x12e7100: add             x1, x1, HEAP, lsl #32
    // 0x12e7104: r0 = controller()
    //     0x12e7104: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e7108: mov             x2, x0
    // 0x12e710c: ldur            x0, [fp, #-8]
    // 0x12e7110: stur            x2, [fp, #-0x10]
    // 0x12e7114: LoadField: r1 = r0->field_f
    //     0x12e7114: ldur            w1, [x0, #0xf]
    // 0x12e7118: DecompressPointer r1
    //     0x12e7118: add             x1, x1, HEAP, lsl #32
    // 0x12e711c: r0 = controller()
    //     0x12e711c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e7120: LoadField: r1 = r0->field_7b
    //     0x12e7120: ldur            w1, [x0, #0x7b]
    // 0x12e7124: DecompressPointer r1
    //     0x12e7124: add             x1, x1, HEAP, lsl #32
    // 0x12e7128: r0 = value()
    //     0x12e7128: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12e712c: ldur            x1, [fp, #-0x10]
    // 0x12e7130: mov             x2, x0
    // 0x12e7134: r0 = checkAddress()
    //     0x12e7134: bl              #0x12e8118  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkAddress
    // 0x12e7138: ldur            x0, [fp, #-8]
    // 0x12e713c: LoadField: r1 = r0->field_f
    //     0x12e713c: ldur            w1, [x0, #0xf]
    // 0x12e7140: DecompressPointer r1
    //     0x12e7140: add             x1, x1, HEAP, lsl #32
    // 0x12e7144: r0 = controller()
    //     0x12e7144: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e7148: r16 = "landing_page"
    //     0x12e7148: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x12e714c: ldr             x16, [x16, #0x638]
    // 0x12e7150: r30 = "request_number"
    //     0x12e7150: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x12e7154: ldr             lr, [lr, #0x900]
    // 0x12e7158: stp             lr, x16, [SP, #0x18]
    // 0x12e715c: r16 = "page_cta"
    //     0x12e715c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c780] "page_cta"
    //     0x12e7160: ldr             x16, [x16, #0x780]
    // 0x12e7164: r30 = "Continue"
    //     0x12e7164: add             lr, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x12e7168: ldr             lr, [lr, #0xfe0]
    // 0x12e716c: stp             lr, x16, [SP, #8]
    // 0x12e7170: r16 = "continue_cta"
    //     0x12e7170: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d908] "continue_cta"
    //     0x12e7174: ldr             x16, [x16, #0x908]
    // 0x12e7178: str             x16, [SP]
    // 0x12e717c: mov             x1, x0
    // 0x12e7180: r2 = "checkout_cta_clicked"
    //     0x12e7180: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x12e7184: ldr             x2, [x2, #0x7b0]
    // 0x12e7188: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, pageId, 0x3, pageType, 0x2, widgetType, 0x6, null]
    //     0x12e7188: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d910] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "pageId", 0x3, "pageType", 0x2, "widgetType", 0x6, Null]
    //     0x12e718c: ldr             x4, [x4, #0x910]
    // 0x12e7190: r0 = checkoutPostEvent()
    //     0x12e7190: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x12e7194: r0 = Null
    //     0x12e7194: mov             x0, NULL
    // 0x12e7198: LeaveFrame
    //     0x12e7198: mov             SP, fp
    //     0x12e719c: ldp             fp, lr, [SP], #0x10
    // 0x12e71a0: ret
    //     0x12e71a0: ret             
    // 0x12e71a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12e71a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12e71a8: b               #0x12e707c
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x12e71ac, size: 0x5f8
    // 0x12e71ac: EnterFrame
    //     0x12e71ac: stp             fp, lr, [SP, #-0x10]!
    //     0x12e71b0: mov             fp, SP
    // 0x12e71b4: AllocStack(0x58)
    //     0x12e71b4: sub             SP, SP, #0x58
    // 0x12e71b8: SetupParameters()
    //     0x12e71b8: ldr             x0, [fp, #0x10]
    //     0x12e71bc: ldur            w2, [x0, #0x17]
    //     0x12e71c0: add             x2, x2, HEAP, lsl #32
    //     0x12e71c4: stur            x2, [fp, #-8]
    // 0x12e71c8: CheckStackOverflow
    //     0x12e71c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12e71cc: cmp             SP, x16
    //     0x12e71d0: b.ls            #0x12e7758
    // 0x12e71d4: LoadField: r1 = r2->field_13
    //     0x12e71d4: ldur            w1, [x2, #0x13]
    // 0x12e71d8: DecompressPointer r1
    //     0x12e71d8: add             x1, x1, HEAP, lsl #32
    // 0x12e71dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12e71dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12e71e0: r0 = _of()
    //     0x12e71e0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x12e71e4: LoadField: r1 = r0->field_23
    //     0x12e71e4: ldur            w1, [x0, #0x23]
    // 0x12e71e8: DecompressPointer r1
    //     0x12e71e8: add             x1, x1, HEAP, lsl #32
    // 0x12e71ec: LoadField: d0 = r1->field_1f
    //     0x12e71ec: ldur            d0, [x1, #0x1f]
    // 0x12e71f0: stur            d0, [fp, #-0x38]
    // 0x12e71f4: r0 = EdgeInsets()
    //     0x12e71f4: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x12e71f8: stur            x0, [fp, #-0x10]
    // 0x12e71fc: StoreField: r0->field_7 = rZR
    //     0x12e71fc: stur            xzr, [x0, #7]
    // 0x12e7200: StoreField: r0->field_f = rZR
    //     0x12e7200: stur            xzr, [x0, #0xf]
    // 0x12e7204: ArrayStore: r0[0] = rZR  ; List_8
    //     0x12e7204: stur            xzr, [x0, #0x17]
    // 0x12e7208: ldur            d0, [fp, #-0x38]
    // 0x12e720c: StoreField: r0->field_1f = d0
    //     0x12e720c: stur            d0, [x0, #0x1f]
    // 0x12e7210: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x12e7210: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12e7214: ldr             x0, [x0, #0x1c80]
    //     0x12e7218: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12e721c: cmp             w0, w16
    //     0x12e7220: b.ne            #0x12e722c
    //     0x12e7224: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x12e7228: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12e722c: r0 = GetNavigation.width()
    //     0x12e722c: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x12e7230: stur            d0, [fp, #-0x38]
    // 0x12e7234: r16 = <EdgeInsets>
    //     0x12e7234: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x12e7238: ldr             x16, [x16, #0xda0]
    // 0x12e723c: r30 = Instance_EdgeInsets
    //     0x12e723c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x12e7240: ldr             lr, [lr, #0x1f0]
    // 0x12e7244: stp             lr, x16, [SP]
    // 0x12e7248: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12e7248: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12e724c: r0 = all()
    //     0x12e724c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12e7250: ldur            x2, [fp, #-8]
    // 0x12e7254: stur            x0, [fp, #-0x18]
    // 0x12e7258: LoadField: r1 = r2->field_f
    //     0x12e7258: ldur            w1, [x2, #0xf]
    // 0x12e725c: DecompressPointer r1
    //     0x12e725c: add             x1, x1, HEAP, lsl #32
    // 0x12e7260: r0 = controller()
    //     0x12e7260: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e7264: LoadField: r1 = r0->field_a7
    //     0x12e7264: ldur            w1, [x0, #0xa7]
    // 0x12e7268: DecompressPointer r1
    //     0x12e7268: add             x1, x1, HEAP, lsl #32
    // 0x12e726c: r0 = value()
    //     0x12e726c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12e7270: tbnz            w0, #4, #0x12e7290
    // 0x12e7274: ldur            x2, [fp, #-8]
    // 0x12e7278: LoadField: r1 = r2->field_13
    //     0x12e7278: ldur            w1, [x2, #0x13]
    // 0x12e727c: DecompressPointer r1
    //     0x12e727c: add             x1, x1, HEAP, lsl #32
    // 0x12e7280: r0 = of()
    //     0x12e7280: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e7284: LoadField: r1 = r0->field_5b
    //     0x12e7284: ldur            w1, [x0, #0x5b]
    // 0x12e7288: DecompressPointer r1
    //     0x12e7288: add             x1, x1, HEAP, lsl #32
    // 0x12e728c: b               #0x12e7298
    // 0x12e7290: r1 = Instance_MaterialColor
    //     0x12e7290: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x12e7294: ldr             x1, [x1, #0xdc0]
    // 0x12e7298: ldur            x2, [fp, #-8]
    // 0x12e729c: ldur            x0, [fp, #-0x18]
    // 0x12e72a0: r16 = <Color>
    //     0x12e72a0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x12e72a4: ldr             x16, [x16, #0xf80]
    // 0x12e72a8: stp             x1, x16, [SP]
    // 0x12e72ac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12e72ac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12e72b0: r0 = all()
    //     0x12e72b0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12e72b4: stur            x0, [fp, #-0x20]
    // 0x12e72b8: r0 = Radius()
    //     0x12e72b8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x12e72bc: d0 = 20.000000
    //     0x12e72bc: fmov            d0, #20.00000000
    // 0x12e72c0: stur            x0, [fp, #-0x28]
    // 0x12e72c4: StoreField: r0->field_7 = d0
    //     0x12e72c4: stur            d0, [x0, #7]
    // 0x12e72c8: StoreField: r0->field_f = d0
    //     0x12e72c8: stur            d0, [x0, #0xf]
    // 0x12e72cc: r0 = BorderRadius()
    //     0x12e72cc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x12e72d0: mov             x1, x0
    // 0x12e72d4: ldur            x0, [fp, #-0x28]
    // 0x12e72d8: stur            x1, [fp, #-0x30]
    // 0x12e72dc: StoreField: r1->field_7 = r0
    //     0x12e72dc: stur            w0, [x1, #7]
    // 0x12e72e0: StoreField: r1->field_b = r0
    //     0x12e72e0: stur            w0, [x1, #0xb]
    // 0x12e72e4: StoreField: r1->field_f = r0
    //     0x12e72e4: stur            w0, [x1, #0xf]
    // 0x12e72e8: StoreField: r1->field_13 = r0
    //     0x12e72e8: stur            w0, [x1, #0x13]
    // 0x12e72ec: r0 = RoundedRectangleBorder()
    //     0x12e72ec: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x12e72f0: mov             x1, x0
    // 0x12e72f4: ldur            x0, [fp, #-0x30]
    // 0x12e72f8: StoreField: r1->field_b = r0
    //     0x12e72f8: stur            w0, [x1, #0xb]
    // 0x12e72fc: r0 = Instance_BorderSide
    //     0x12e72fc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x12e7300: ldr             x0, [x0, #0xe20]
    // 0x12e7304: StoreField: r1->field_7 = r0
    //     0x12e7304: stur            w0, [x1, #7]
    // 0x12e7308: r16 = <RoundedRectangleBorder>
    //     0x12e7308: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x12e730c: ldr             x16, [x16, #0xf78]
    // 0x12e7310: stp             x1, x16, [SP]
    // 0x12e7314: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12e7314: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12e7318: r0 = all()
    //     0x12e7318: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12e731c: stur            x0, [fp, #-0x28]
    // 0x12e7320: r0 = ButtonStyle()
    //     0x12e7320: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x12e7324: mov             x1, x0
    // 0x12e7328: ldur            x0, [fp, #-0x20]
    // 0x12e732c: stur            x1, [fp, #-0x30]
    // 0x12e7330: StoreField: r1->field_b = r0
    //     0x12e7330: stur            w0, [x1, #0xb]
    // 0x12e7334: ldur            x0, [fp, #-0x18]
    // 0x12e7338: StoreField: r1->field_23 = r0
    //     0x12e7338: stur            w0, [x1, #0x23]
    // 0x12e733c: ldur            x0, [fp, #-0x28]
    // 0x12e7340: StoreField: r1->field_43 = r0
    //     0x12e7340: stur            w0, [x1, #0x43]
    // 0x12e7344: r0 = TextButtonThemeData()
    //     0x12e7344: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x12e7348: mov             x2, x0
    // 0x12e734c: ldur            x0, [fp, #-0x30]
    // 0x12e7350: stur            x2, [fp, #-0x18]
    // 0x12e7354: StoreField: r2->field_7 = r0
    //     0x12e7354: stur            w0, [x2, #7]
    // 0x12e7358: ldur            x0, [fp, #-8]
    // 0x12e735c: LoadField: r1 = r0->field_f
    //     0x12e735c: ldur            w1, [x0, #0xf]
    // 0x12e7360: DecompressPointer r1
    //     0x12e7360: add             x1, x1, HEAP, lsl #32
    // 0x12e7364: r0 = controller()
    //     0x12e7364: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e7368: LoadField: r1 = r0->field_a7
    //     0x12e7368: ldur            w1, [x0, #0xa7]
    // 0x12e736c: DecompressPointer r1
    //     0x12e736c: add             x1, x1, HEAP, lsl #32
    // 0x12e7370: r0 = value()
    //     0x12e7370: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12e7374: tbnz            w0, #4, #0x12e7390
    // 0x12e7378: ldur            x2, [fp, #-8]
    // 0x12e737c: r1 = Function '<anonymous closure>':.
    //     0x12e737c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41338] AnonymousClosure: (0x12e7054), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::bottomNavigationBar (0x135db28)
    //     0x12e7380: ldr             x1, [x1, #0x338]
    // 0x12e7384: r0 = AllocateClosure()
    //     0x12e7384: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12e7388: mov             x4, x0
    // 0x12e738c: b               #0x12e7394
    // 0x12e7390: r4 = Null
    //     0x12e7390: mov             x4, NULL
    // 0x12e7394: ldur            x2, [fp, #-8]
    // 0x12e7398: ldur            x3, [fp, #-0x10]
    // 0x12e739c: ldur            d0, [fp, #-0x38]
    // 0x12e73a0: ldur            x0, [fp, #-0x18]
    // 0x12e73a4: stur            x4, [fp, #-0x20]
    // 0x12e73a8: LoadField: r1 = r2->field_13
    //     0x12e73a8: ldur            w1, [x2, #0x13]
    // 0x12e73ac: DecompressPointer r1
    //     0x12e73ac: add             x1, x1, HEAP, lsl #32
    // 0x12e73b0: r0 = of()
    //     0x12e73b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e73b4: LoadField: r1 = r0->field_87
    //     0x12e73b4: ldur            w1, [x0, #0x87]
    // 0x12e73b8: DecompressPointer r1
    //     0x12e73b8: add             x1, x1, HEAP, lsl #32
    // 0x12e73bc: LoadField: r0 = r1->field_7
    //     0x12e73bc: ldur            w0, [x1, #7]
    // 0x12e73c0: DecompressPointer r0
    //     0x12e73c0: add             x0, x0, HEAP, lsl #32
    // 0x12e73c4: r16 = 14.000000
    //     0x12e73c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x12e73c8: ldr             x16, [x16, #0x1d8]
    // 0x12e73cc: r30 = Instance_Color
    //     0x12e73cc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12e73d0: stp             lr, x16, [SP]
    // 0x12e73d4: mov             x1, x0
    // 0x12e73d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12e73d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12e73dc: ldr             x4, [x4, #0xaa0]
    // 0x12e73e0: r0 = copyWith()
    //     0x12e73e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12e73e4: stur            x0, [fp, #-0x28]
    // 0x12e73e8: r0 = Text()
    //     0x12e73e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12e73ec: mov             x1, x0
    // 0x12e73f0: r0 = "Continue"
    //     0x12e73f0: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x12e73f4: ldr             x0, [x0, #0xfe0]
    // 0x12e73f8: stur            x1, [fp, #-0x30]
    // 0x12e73fc: StoreField: r1->field_b = r0
    //     0x12e73fc: stur            w0, [x1, #0xb]
    // 0x12e7400: ldur            x0, [fp, #-0x28]
    // 0x12e7404: StoreField: r1->field_13 = r0
    //     0x12e7404: stur            w0, [x1, #0x13]
    // 0x12e7408: r0 = TextButton()
    //     0x12e7408: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x12e740c: mov             x1, x0
    // 0x12e7410: ldur            x0, [fp, #-0x20]
    // 0x12e7414: stur            x1, [fp, #-0x28]
    // 0x12e7418: StoreField: r1->field_b = r0
    //     0x12e7418: stur            w0, [x1, #0xb]
    // 0x12e741c: r0 = false
    //     0x12e741c: add             x0, NULL, #0x30  ; false
    // 0x12e7420: StoreField: r1->field_27 = r0
    //     0x12e7420: stur            w0, [x1, #0x27]
    // 0x12e7424: r2 = true
    //     0x12e7424: add             x2, NULL, #0x20  ; true
    // 0x12e7428: StoreField: r1->field_2f = r2
    //     0x12e7428: stur            w2, [x1, #0x2f]
    // 0x12e742c: ldur            x3, [fp, #-0x30]
    // 0x12e7430: StoreField: r1->field_37 = r3
    //     0x12e7430: stur            w3, [x1, #0x37]
    // 0x12e7434: r0 = TextButtonTheme()
    //     0x12e7434: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x12e7438: mov             x1, x0
    // 0x12e743c: ldur            x0, [fp, #-0x18]
    // 0x12e7440: stur            x1, [fp, #-0x20]
    // 0x12e7444: StoreField: r1->field_f = r0
    //     0x12e7444: stur            w0, [x1, #0xf]
    // 0x12e7448: ldur            x0, [fp, #-0x28]
    // 0x12e744c: StoreField: r1->field_b = r0
    //     0x12e744c: stur            w0, [x1, #0xb]
    // 0x12e7450: ldur            d0, [fp, #-0x38]
    // 0x12e7454: r0 = inline_Allocate_Double()
    //     0x12e7454: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x12e7458: add             x0, x0, #0x10
    //     0x12e745c: cmp             x2, x0
    //     0x12e7460: b.ls            #0x12e7760
    //     0x12e7464: str             x0, [THR, #0x50]  ; THR::top
    //     0x12e7468: sub             x0, x0, #0xf
    //     0x12e746c: movz            x2, #0xe15c
    //     0x12e7470: movk            x2, #0x3, lsl #16
    //     0x12e7474: stur            x2, [x0, #-1]
    // 0x12e7478: StoreField: r0->field_7 = d0
    //     0x12e7478: stur            d0, [x0, #7]
    // 0x12e747c: stur            x0, [fp, #-0x18]
    // 0x12e7480: r0 = Container()
    //     0x12e7480: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12e7484: stur            x0, [fp, #-0x28]
    // 0x12e7488: r16 = Instance_EdgeInsets
    //     0x12e7488: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x12e748c: ldr             x16, [x16, #0xe48]
    // 0x12e7490: ldur            lr, [fp, #-0x18]
    // 0x12e7494: stp             lr, x16, [SP, #8]
    // 0x12e7498: ldur            x16, [fp, #-0x20]
    // 0x12e749c: str             x16, [SP]
    // 0x12e74a0: mov             x1, x0
    // 0x12e74a4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x1, width, 0x2, null]
    //     0x12e74a4: add             x4, PP, #0x41, lsl #12  ; [pp+0x41340] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x12e74a8: ldr             x4, [x4, #0x340]
    // 0x12e74ac: r0 = Container()
    //     0x12e74ac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12e74b0: r0 = GetNavigation.size()
    //     0x12e74b0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x12e74b4: LoadField: d0 = r0->field_7
    //     0x12e74b4: ldur            d0, [x0, #7]
    // 0x12e74b8: ldur            x0, [fp, #-8]
    // 0x12e74bc: stur            d0, [fp, #-0x38]
    // 0x12e74c0: LoadField: r1 = r0->field_13
    //     0x12e74c0: ldur            w1, [x0, #0x13]
    // 0x12e74c4: DecompressPointer r1
    //     0x12e74c4: add             x1, x1, HEAP, lsl #32
    // 0x12e74c8: r0 = of()
    //     0x12e74c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e74cc: LoadField: r1 = r0->field_87
    //     0x12e74cc: ldur            w1, [x0, #0x87]
    // 0x12e74d0: DecompressPointer r1
    //     0x12e74d0: add             x1, x1, HEAP, lsl #32
    // 0x12e74d4: LoadField: r0 = r1->field_2b
    //     0x12e74d4: ldur            w0, [x1, #0x2b]
    // 0x12e74d8: DecompressPointer r0
    //     0x12e74d8: add             x0, x0, HEAP, lsl #32
    // 0x12e74dc: stur            x0, [fp, #-8]
    // 0x12e74e0: r1 = Instance_Color
    //     0x12e74e0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12e74e4: d0 = 0.700000
    //     0x12e74e4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x12e74e8: ldr             d0, [x17, #0xf48]
    // 0x12e74ec: r0 = withOpacity()
    //     0x12e74ec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12e74f0: r16 = 10.000000
    //     0x12e74f0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x12e74f4: stp             x0, x16, [SP]
    // 0x12e74f8: ldur            x1, [fp, #-8]
    // 0x12e74fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12e74fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12e7500: ldr             x4, [x4, #0xaa0]
    // 0x12e7504: r0 = copyWith()
    //     0x12e7504: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12e7508: stur            x0, [fp, #-8]
    // 0x12e750c: r0 = Text()
    //     0x12e750c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12e7510: mov             x1, x0
    // 0x12e7514: r0 = "Powered By"
    //     0x12e7514: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x12e7518: ldr             x0, [x0, #0x750]
    // 0x12e751c: stur            x1, [fp, #-0x18]
    // 0x12e7520: StoreField: r1->field_b = r0
    //     0x12e7520: stur            w0, [x1, #0xb]
    // 0x12e7524: ldur            x0, [fp, #-8]
    // 0x12e7528: StoreField: r1->field_13 = r0
    //     0x12e7528: stur            w0, [x1, #0x13]
    // 0x12e752c: r0 = SvgPicture()
    //     0x12e752c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x12e7530: stur            x0, [fp, #-8]
    // 0x12e7534: r16 = 20.000000
    //     0x12e7534: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x12e7538: ldr             x16, [x16, #0xac8]
    // 0x12e753c: str             x16, [SP]
    // 0x12e7540: mov             x1, x0
    // 0x12e7544: r2 = "assets/images/shopdeck.svg"
    //     0x12e7544: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x12e7548: ldr             x2, [x2, #0x758]
    // 0x12e754c: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x12e754c: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x12e7550: ldr             x4, [x4, #0x760]
    // 0x12e7554: r0 = SvgPicture.asset()
    //     0x12e7554: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x12e7558: r1 = Null
    //     0x12e7558: mov             x1, NULL
    // 0x12e755c: r2 = 4
    //     0x12e755c: movz            x2, #0x4
    // 0x12e7560: r0 = AllocateArray()
    //     0x12e7560: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12e7564: mov             x2, x0
    // 0x12e7568: ldur            x0, [fp, #-0x18]
    // 0x12e756c: stur            x2, [fp, #-0x20]
    // 0x12e7570: StoreField: r2->field_f = r0
    //     0x12e7570: stur            w0, [x2, #0xf]
    // 0x12e7574: ldur            x0, [fp, #-8]
    // 0x12e7578: StoreField: r2->field_13 = r0
    //     0x12e7578: stur            w0, [x2, #0x13]
    // 0x12e757c: r1 = <Widget>
    //     0x12e757c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12e7580: r0 = AllocateGrowableArray()
    //     0x12e7580: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12e7584: mov             x1, x0
    // 0x12e7588: ldur            x0, [fp, #-0x20]
    // 0x12e758c: stur            x1, [fp, #-8]
    // 0x12e7590: StoreField: r1->field_f = r0
    //     0x12e7590: stur            w0, [x1, #0xf]
    // 0x12e7594: r2 = 4
    //     0x12e7594: movz            x2, #0x4
    // 0x12e7598: StoreField: r1->field_b = r2
    //     0x12e7598: stur            w2, [x1, #0xb]
    // 0x12e759c: r0 = Row()
    //     0x12e759c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12e75a0: mov             x1, x0
    // 0x12e75a4: r0 = Instance_Axis
    //     0x12e75a4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12e75a8: stur            x1, [fp, #-0x18]
    // 0x12e75ac: StoreField: r1->field_f = r0
    //     0x12e75ac: stur            w0, [x1, #0xf]
    // 0x12e75b0: r0 = Instance_MainAxisAlignment
    //     0x12e75b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12e75b4: ldr             x0, [x0, #0xa08]
    // 0x12e75b8: StoreField: r1->field_13 = r0
    //     0x12e75b8: stur            w0, [x1, #0x13]
    // 0x12e75bc: r2 = Instance_MainAxisSize
    //     0x12e75bc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x12e75c0: ldr             x2, [x2, #0xdd0]
    // 0x12e75c4: ArrayStore: r1[0] = r2  ; List_4
    //     0x12e75c4: stur            w2, [x1, #0x17]
    // 0x12e75c8: r3 = Instance_CrossAxisAlignment
    //     0x12e75c8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12e75cc: ldr             x3, [x3, #0xa18]
    // 0x12e75d0: StoreField: r1->field_1b = r3
    //     0x12e75d0: stur            w3, [x1, #0x1b]
    // 0x12e75d4: r4 = Instance_VerticalDirection
    //     0x12e75d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12e75d8: ldr             x4, [x4, #0xa20]
    // 0x12e75dc: StoreField: r1->field_23 = r4
    //     0x12e75dc: stur            w4, [x1, #0x23]
    // 0x12e75e0: r5 = Instance_Clip
    //     0x12e75e0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12e75e4: ldr             x5, [x5, #0x38]
    // 0x12e75e8: StoreField: r1->field_2b = r5
    //     0x12e75e8: stur            w5, [x1, #0x2b]
    // 0x12e75ec: StoreField: r1->field_2f = rZR
    //     0x12e75ec: stur            xzr, [x1, #0x2f]
    // 0x12e75f0: ldur            x6, [fp, #-8]
    // 0x12e75f4: StoreField: r1->field_b = r6
    //     0x12e75f4: stur            w6, [x1, #0xb]
    // 0x12e75f8: ldur            d0, [fp, #-0x38]
    // 0x12e75fc: r6 = inline_Allocate_Double()
    //     0x12e75fc: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x12e7600: add             x6, x6, #0x10
    //     0x12e7604: cmp             x7, x6
    //     0x12e7608: b.ls            #0x12e7778
    //     0x12e760c: str             x6, [THR, #0x50]  ; THR::top
    //     0x12e7610: sub             x6, x6, #0xf
    //     0x12e7614: movz            x7, #0xe15c
    //     0x12e7618: movk            x7, #0x3, lsl #16
    //     0x12e761c: stur            x7, [x6, #-1]
    // 0x12e7620: StoreField: r6->field_7 = d0
    //     0x12e7620: stur            d0, [x6, #7]
    // 0x12e7624: stur            x6, [fp, #-8]
    // 0x12e7628: r0 = Container()
    //     0x12e7628: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12e762c: stur            x0, [fp, #-0x20]
    // 0x12e7630: r16 = Instance_Alignment
    //     0x12e7630: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x12e7634: ldr             x16, [x16, #0xb10]
    // 0x12e7638: ldur            lr, [fp, #-8]
    // 0x12e763c: stp             lr, x16, [SP, #0x10]
    // 0x12e7640: r16 = Instance_BoxDecoration
    //     0x12e7640: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x12e7644: ldr             x16, [x16, #0x768]
    // 0x12e7648: ldur            lr, [fp, #-0x18]
    // 0x12e764c: stp             lr, x16, [SP]
    // 0x12e7650: mov             x1, x0
    // 0x12e7654: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x12e7654: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x12e7658: ldr             x4, [x4, #0x770]
    // 0x12e765c: r0 = Container()
    //     0x12e765c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12e7660: r1 = Null
    //     0x12e7660: mov             x1, NULL
    // 0x12e7664: r2 = 4
    //     0x12e7664: movz            x2, #0x4
    // 0x12e7668: r0 = AllocateArray()
    //     0x12e7668: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12e766c: mov             x2, x0
    // 0x12e7670: ldur            x0, [fp, #-0x28]
    // 0x12e7674: stur            x2, [fp, #-8]
    // 0x12e7678: StoreField: r2->field_f = r0
    //     0x12e7678: stur            w0, [x2, #0xf]
    // 0x12e767c: ldur            x0, [fp, #-0x20]
    // 0x12e7680: StoreField: r2->field_13 = r0
    //     0x12e7680: stur            w0, [x2, #0x13]
    // 0x12e7684: r1 = <Widget>
    //     0x12e7684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12e7688: r0 = AllocateGrowableArray()
    //     0x12e7688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12e768c: mov             x1, x0
    // 0x12e7690: ldur            x0, [fp, #-8]
    // 0x12e7694: stur            x1, [fp, #-0x18]
    // 0x12e7698: StoreField: r1->field_f = r0
    //     0x12e7698: stur            w0, [x1, #0xf]
    // 0x12e769c: r0 = 4
    //     0x12e769c: movz            x0, #0x4
    // 0x12e76a0: StoreField: r1->field_b = r0
    //     0x12e76a0: stur            w0, [x1, #0xb]
    // 0x12e76a4: r0 = Column()
    //     0x12e76a4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x12e76a8: mov             x1, x0
    // 0x12e76ac: r0 = Instance_Axis
    //     0x12e76ac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x12e76b0: stur            x1, [fp, #-8]
    // 0x12e76b4: StoreField: r1->field_f = r0
    //     0x12e76b4: stur            w0, [x1, #0xf]
    // 0x12e76b8: r0 = Instance_MainAxisAlignment
    //     0x12e76b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12e76bc: ldr             x0, [x0, #0xa08]
    // 0x12e76c0: StoreField: r1->field_13 = r0
    //     0x12e76c0: stur            w0, [x1, #0x13]
    // 0x12e76c4: r0 = Instance_MainAxisSize
    //     0x12e76c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x12e76c8: ldr             x0, [x0, #0xdd0]
    // 0x12e76cc: ArrayStore: r1[0] = r0  ; List_4
    //     0x12e76cc: stur            w0, [x1, #0x17]
    // 0x12e76d0: r0 = Instance_CrossAxisAlignment
    //     0x12e76d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12e76d4: ldr             x0, [x0, #0xa18]
    // 0x12e76d8: StoreField: r1->field_1b = r0
    //     0x12e76d8: stur            w0, [x1, #0x1b]
    // 0x12e76dc: r0 = Instance_VerticalDirection
    //     0x12e76dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12e76e0: ldr             x0, [x0, #0xa20]
    // 0x12e76e4: StoreField: r1->field_23 = r0
    //     0x12e76e4: stur            w0, [x1, #0x23]
    // 0x12e76e8: r0 = Instance_Clip
    //     0x12e76e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12e76ec: ldr             x0, [x0, #0x38]
    // 0x12e76f0: StoreField: r1->field_2b = r0
    //     0x12e76f0: stur            w0, [x1, #0x2b]
    // 0x12e76f4: StoreField: r1->field_2f = rZR
    //     0x12e76f4: stur            xzr, [x1, #0x2f]
    // 0x12e76f8: ldur            x0, [fp, #-0x18]
    // 0x12e76fc: StoreField: r1->field_b = r0
    //     0x12e76fc: stur            w0, [x1, #0xb]
    // 0x12e7700: r0 = Padding()
    //     0x12e7700: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12e7704: mov             x1, x0
    // 0x12e7708: ldur            x0, [fp, #-0x10]
    // 0x12e770c: stur            x1, [fp, #-0x18]
    // 0x12e7710: StoreField: r1->field_f = r0
    //     0x12e7710: stur            w0, [x1, #0xf]
    // 0x12e7714: ldur            x0, [fp, #-8]
    // 0x12e7718: StoreField: r1->field_b = r0
    //     0x12e7718: stur            w0, [x1, #0xb]
    // 0x12e771c: r0 = SafeArea()
    //     0x12e771c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x12e7720: r1 = true
    //     0x12e7720: add             x1, NULL, #0x20  ; true
    // 0x12e7724: StoreField: r0->field_b = r1
    //     0x12e7724: stur            w1, [x0, #0xb]
    // 0x12e7728: StoreField: r0->field_f = r1
    //     0x12e7728: stur            w1, [x0, #0xf]
    // 0x12e772c: StoreField: r0->field_13 = r1
    //     0x12e772c: stur            w1, [x0, #0x13]
    // 0x12e7730: ArrayStore: r0[0] = r1  ; List_4
    //     0x12e7730: stur            w1, [x0, #0x17]
    // 0x12e7734: r1 = Instance_EdgeInsets
    //     0x12e7734: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x12e7738: StoreField: r0->field_1b = r1
    //     0x12e7738: stur            w1, [x0, #0x1b]
    // 0x12e773c: r1 = false
    //     0x12e773c: add             x1, NULL, #0x30  ; false
    // 0x12e7740: StoreField: r0->field_1f = r1
    //     0x12e7740: stur            w1, [x0, #0x1f]
    // 0x12e7744: ldur            x1, [fp, #-0x18]
    // 0x12e7748: StoreField: r0->field_23 = r1
    //     0x12e7748: stur            w1, [x0, #0x23]
    // 0x12e774c: LeaveFrame
    //     0x12e774c: mov             SP, fp
    //     0x12e7750: ldp             fp, lr, [SP], #0x10
    // 0x12e7754: ret
    //     0x12e7754: ret             
    // 0x12e7758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12e7758: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12e775c: b               #0x12e71d4
    // 0x12e7760: SaveReg d0
    //     0x12e7760: str             q0, [SP, #-0x10]!
    // 0x12e7764: SaveReg r1
    //     0x12e7764: str             x1, [SP, #-8]!
    // 0x12e7768: r0 = AllocateDouble()
    //     0x12e7768: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12e776c: RestoreReg r1
    //     0x12e776c: ldr             x1, [SP], #8
    // 0x12e7770: RestoreReg d0
    //     0x12e7770: ldr             q0, [SP], #0x10
    // 0x12e7774: b               #0x12e7478
    // 0x12e7778: SaveReg d0
    //     0x12e7778: str             q0, [SP, #-0x10]!
    // 0x12e777c: stp             x4, x5, [SP, #-0x10]!
    // 0x12e7780: stp             x2, x3, [SP, #-0x10]!
    // 0x12e7784: stp             x0, x1, [SP, #-0x10]!
    // 0x12e7788: r0 = AllocateDouble()
    //     0x12e7788: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12e778c: mov             x6, x0
    // 0x12e7790: ldp             x0, x1, [SP], #0x10
    // 0x12e7794: ldp             x2, x3, [SP], #0x10
    // 0x12e7798: ldp             x4, x5, [SP], #0x10
    // 0x12e779c: RestoreReg d0
    //     0x12e779c: ldr             q0, [SP], #0x10
    // 0x12e77a0: b               #0x12e7620
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x135db28, size: 0x98
    // 0x135db28: EnterFrame
    //     0x135db28: stp             fp, lr, [SP, #-0x10]!
    //     0x135db2c: mov             fp, SP
    // 0x135db30: AllocStack(0x18)
    //     0x135db30: sub             SP, SP, #0x18
    // 0x135db34: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x135db34: stur            x1, [fp, #-8]
    //     0x135db38: stur            x2, [fp, #-0x10]
    // 0x135db3c: CheckStackOverflow
    //     0x135db3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135db40: cmp             SP, x16
    //     0x135db44: b.ls            #0x135dbb8
    // 0x135db48: r1 = 2
    //     0x135db48: movz            x1, #0x2
    // 0x135db4c: r0 = AllocateContext()
    //     0x135db4c: bl              #0x16f6108  ; AllocateContextStub
    // 0x135db50: ldur            x1, [fp, #-8]
    // 0x135db54: stur            x0, [fp, #-0x18]
    // 0x135db58: StoreField: r0->field_f = r1
    //     0x135db58: stur            w1, [x0, #0xf]
    // 0x135db5c: ldur            x2, [fp, #-0x10]
    // 0x135db60: StoreField: r0->field_13 = r2
    //     0x135db60: stur            w2, [x0, #0x13]
    // 0x135db64: r0 = controller()
    //     0x135db64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135db68: LoadField: r1 = r0->field_d7
    //     0x135db68: ldur            w1, [x0, #0xd7]
    // 0x135db6c: DecompressPointer r1
    //     0x135db6c: add             x1, x1, HEAP, lsl #32
    // 0x135db70: r0 = value()
    //     0x135db70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135db74: tbnz            w0, #4, #0x135db88
    // 0x135db78: r0 = Instance_SizedBox
    //     0x135db78: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x135db7c: LeaveFrame
    //     0x135db7c: mov             SP, fp
    //     0x135db80: ldp             fp, lr, [SP], #0x10
    // 0x135db84: ret
    //     0x135db84: ret             
    // 0x135db88: r0 = Obx()
    //     0x135db88: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x135db8c: ldur            x2, [fp, #-0x18]
    // 0x135db90: r1 = Function '<anonymous closure>':.
    //     0x135db90: add             x1, PP, #0x41, lsl #12  ; [pp+0x41330] AnonymousClosure: (0x12e71ac), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::bottomNavigationBar (0x135db28)
    //     0x135db94: ldr             x1, [x1, #0x330]
    // 0x135db98: stur            x0, [fp, #-8]
    // 0x135db9c: r0 = AllocateClosure()
    //     0x135db9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135dba0: mov             x1, x0
    // 0x135dba4: ldur            x0, [fp, #-8]
    // 0x135dba8: StoreField: r0->field_b = r1
    //     0x135dba8: stur            w1, [x0, #0xb]
    // 0x135dbac: LeaveFrame
    //     0x135dbac: mov             SP, fp
    //     0x135dbb0: ldp             fp, lr, [SP], #0x10
    // 0x135dbb4: ret
    //     0x135dbb4: ret             
    // 0x135dbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135dbb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135dbbc: b               #0x135db48
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x1393324, size: 0xf4
    // 0x1393324: EnterFrame
    //     0x1393324: stp             fp, lr, [SP, #-0x10]!
    //     0x1393328: mov             fp, SP
    // 0x139332c: AllocStack(0x10)
    //     0x139332c: sub             SP, SP, #0x10
    // 0x1393330: SetupParameters()
    //     0x1393330: ldr             x0, [fp, #0x18]
    //     0x1393334: ldur            w2, [x0, #0x17]
    //     0x1393338: add             x2, x2, HEAP, lsl #32
    //     0x139333c: stur            x2, [fp, #-0x10]
    // 0x1393340: CheckStackOverflow
    //     0x1393340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393344: cmp             SP, x16
    //     0x1393348: b.ls            #0x1393410
    // 0x139334c: LoadField: r0 = r2->field_b
    //     0x139334c: ldur            w0, [x2, #0xb]
    // 0x1393350: DecompressPointer r0
    //     0x1393350: add             x0, x0, HEAP, lsl #32
    // 0x1393354: stur            x0, [fp, #-8]
    // 0x1393358: LoadField: r1 = r0->field_f
    //     0x1393358: ldur            w1, [x0, #0xf]
    // 0x139335c: DecompressPointer r1
    //     0x139335c: add             x1, x1, HEAP, lsl #32
    // 0x1393360: r0 = controller()
    //     0x1393360: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393364: mov             x1, x0
    // 0x1393368: ldur            x0, [fp, #-0x10]
    // 0x139336c: LoadField: r2 = r0->field_f
    //     0x139336c: ldur            w2, [x0, #0xf]
    // 0x1393370: DecompressPointer r2
    //     0x1393370: add             x2, x2, HEAP, lsl #32
    // 0x1393374: LoadField: r3 = r1->field_7b
    //     0x1393374: ldur            w3, [x1, #0x7b]
    // 0x1393378: DecompressPointer r3
    //     0x1393378: add             x3, x3, HEAP, lsl #32
    // 0x139337c: mov             x1, x3
    // 0x1393380: r0 = value=()
    //     0x1393380: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1393384: ldur            x0, [fp, #-0x10]
    // 0x1393388: LoadField: r1 = r0->field_f
    //     0x1393388: ldur            w1, [x0, #0xf]
    // 0x139338c: DecompressPointer r1
    //     0x139338c: add             x1, x1, HEAP, lsl #32
    // 0x1393390: LoadField: r2 = r1->field_7
    //     0x1393390: ldur            w2, [x1, #7]
    // 0x1393394: cbz             w2, #0x13933c0
    // 0x1393398: ldur            x2, [fp, #-8]
    // 0x139339c: LoadField: r1 = r2->field_f
    //     0x139339c: ldur            w1, [x2, #0xf]
    // 0x13933a0: DecompressPointer r1
    //     0x13933a0: add             x1, x1, HEAP, lsl #32
    // 0x13933a4: r0 = controller()
    //     0x13933a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13933a8: LoadField: r1 = r0->field_a7
    //     0x13933a8: ldur            w1, [x0, #0xa7]
    // 0x13933ac: DecompressPointer r1
    //     0x13933ac: add             x1, x1, HEAP, lsl #32
    // 0x13933b0: ldur            x0, [fp, #-0x10]
    // 0x13933b4: LoadField: r2 = r0->field_13
    //     0x13933b4: ldur            w2, [x0, #0x13]
    // 0x13933b8: DecompressPointer r2
    //     0x13933b8: add             x2, x2, HEAP, lsl #32
    // 0x13933bc: r0 = value=()
    //     0x13933bc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13933c0: ldur            x0, [fp, #-8]
    // 0x13933c4: LoadField: r1 = r0->field_f
    //     0x13933c4: ldur            w1, [x0, #0xf]
    // 0x13933c8: DecompressPointer r1
    //     0x13933c8: add             x1, x1, HEAP, lsl #32
    // 0x13933cc: r0 = controller()
    //     0x13933cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13933d0: LoadField: r1 = r0->field_7b
    //     0x13933d0: ldur            w1, [x0, #0x7b]
    // 0x13933d4: DecompressPointer r1
    //     0x13933d4: add             x1, x1, HEAP, lsl #32
    // 0x13933d8: r0 = value()
    //     0x13933d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13933dc: LoadField: r1 = r0->field_7
    //     0x13933dc: ldur            w1, [x0, #7]
    // 0x13933e0: cmp             w1, #0x14
    // 0x13933e4: b.ne            #0x1393400
    // 0x13933e8: ldur            x0, [fp, #-8]
    // 0x13933ec: LoadField: r1 = r0->field_f
    //     0x13933ec: ldur            w1, [x0, #0xf]
    // 0x13933f0: DecompressPointer r1
    //     0x13933f0: add             x1, x1, HEAP, lsl #32
    // 0x13933f4: r0 = controller()
    //     0x13933f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13933f8: mov             x1, x0
    // 0x13933fc: r0 = updateVisitorContact()
    //     0x13933fc: bl              #0x1391a94  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::updateVisitorContact
    // 0x1393400: r0 = Null
    //     0x1393400: mov             x0, NULL
    // 0x1393404: LeaveFrame
    //     0x1393404: mov             SP, fp
    //     0x1393408: ldp             fp, lr, [SP], #0x10
    // 0x139340c: ret
    //     0x139340c: ret             
    // 0x1393410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393414: b               #0x139334c
  }
  [closure] void <anonymous closure>(dynamic, String, bool) {
    // ** addr: 0x1393418, size: 0x14c
    // 0x1393418: EnterFrame
    //     0x1393418: stp             fp, lr, [SP, #-0x10]!
    //     0x139341c: mov             fp, SP
    // 0x1393420: AllocStack(0x18)
    //     0x1393420: sub             SP, SP, #0x18
    // 0x1393424: SetupParameters()
    //     0x1393424: ldr             x0, [fp, #0x20]
    //     0x1393428: ldur            w1, [x0, #0x17]
    //     0x139342c: add             x1, x1, HEAP, lsl #32
    //     0x1393430: stur            x1, [fp, #-8]
    // 0x1393434: CheckStackOverflow
    //     0x1393434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393438: cmp             SP, x16
    //     0x139343c: b.ls            #0x1393558
    // 0x1393440: r1 = 2
    //     0x1393440: movz            x1, #0x2
    // 0x1393444: r0 = AllocateContext()
    //     0x1393444: bl              #0x16f6108  ; AllocateContextStub
    // 0x1393448: mov             x1, x0
    // 0x139344c: ldur            x0, [fp, #-8]
    // 0x1393450: StoreField: r1->field_b = r0
    //     0x1393450: stur            w0, [x1, #0xb]
    // 0x1393454: ldr             x0, [fp, #0x18]
    // 0x1393458: StoreField: r1->field_f = r0
    //     0x1393458: stur            w0, [x1, #0xf]
    // 0x139345c: ldr             x0, [fp, #0x10]
    // 0x1393460: StoreField: r1->field_13 = r0
    //     0x1393460: stur            w0, [x1, #0x13]
    // 0x1393464: r0 = LoadStaticField(0x878)
    //     0x1393464: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1393468: ldr             x0, [x0, #0x10f0]
    // 0x139346c: cmp             w0, NULL
    // 0x1393470: b.eq            #0x1393560
    // 0x1393474: LoadField: r3 = r0->field_53
    //     0x1393474: ldur            w3, [x0, #0x53]
    // 0x1393478: DecompressPointer r3
    //     0x1393478: add             x3, x3, HEAP, lsl #32
    // 0x139347c: stur            x3, [fp, #-0x10]
    // 0x1393480: LoadField: r0 = r3->field_7
    //     0x1393480: ldur            w0, [x3, #7]
    // 0x1393484: DecompressPointer r0
    //     0x1393484: add             x0, x0, HEAP, lsl #32
    // 0x1393488: mov             x2, x1
    // 0x139348c: stur            x0, [fp, #-8]
    // 0x1393490: r1 = Function '<anonymous closure>':.
    //     0x1393490: add             x1, PP, #0x41, lsl #12  ; [pp+0x41388] AnonymousClosure: (0x1393324), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x1393494: ldr             x1, [x1, #0x388]
    // 0x1393498: r0 = AllocateClosure()
    //     0x1393498: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x139349c: ldur            x2, [fp, #-8]
    // 0x13934a0: mov             x3, x0
    // 0x13934a4: r1 = Null
    //     0x13934a4: mov             x1, NULL
    // 0x13934a8: stur            x3, [fp, #-8]
    // 0x13934ac: cmp             w2, NULL
    // 0x13934b0: b.eq            #0x13934d0
    // 0x13934b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13934b4: ldur            w4, [x2, #0x17]
    // 0x13934b8: DecompressPointer r4
    //     0x13934b8: add             x4, x4, HEAP, lsl #32
    // 0x13934bc: r8 = X0
    //     0x13934bc: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x13934c0: LoadField: r9 = r4->field_7
    //     0x13934c0: ldur            x9, [x4, #7]
    // 0x13934c4: r3 = Null
    //     0x13934c4: add             x3, PP, #0x41, lsl #12  ; [pp+0x41390] Null
    //     0x13934c8: ldr             x3, [x3, #0x390]
    // 0x13934cc: blr             x9
    // 0x13934d0: ldur            x0, [fp, #-0x10]
    // 0x13934d4: LoadField: r1 = r0->field_b
    //     0x13934d4: ldur            w1, [x0, #0xb]
    // 0x13934d8: LoadField: r2 = r0->field_f
    //     0x13934d8: ldur            w2, [x0, #0xf]
    // 0x13934dc: DecompressPointer r2
    //     0x13934dc: add             x2, x2, HEAP, lsl #32
    // 0x13934e0: LoadField: r3 = r2->field_b
    //     0x13934e0: ldur            w3, [x2, #0xb]
    // 0x13934e4: r2 = LoadInt32Instr(r1)
    //     0x13934e4: sbfx            x2, x1, #1, #0x1f
    // 0x13934e8: stur            x2, [fp, #-0x18]
    // 0x13934ec: r1 = LoadInt32Instr(r3)
    //     0x13934ec: sbfx            x1, x3, #1, #0x1f
    // 0x13934f0: cmp             x2, x1
    // 0x13934f4: b.ne            #0x1393500
    // 0x13934f8: mov             x1, x0
    // 0x13934fc: r0 = _growToNextCapacity()
    //     0x13934fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1393500: ldur            x2, [fp, #-0x10]
    // 0x1393504: ldur            x3, [fp, #-0x18]
    // 0x1393508: add             x4, x3, #1
    // 0x139350c: lsl             x5, x4, #1
    // 0x1393510: StoreField: r2->field_b = r5
    //     0x1393510: stur            w5, [x2, #0xb]
    // 0x1393514: LoadField: r1 = r2->field_f
    //     0x1393514: ldur            w1, [x2, #0xf]
    // 0x1393518: DecompressPointer r1
    //     0x1393518: add             x1, x1, HEAP, lsl #32
    // 0x139351c: ldur            x0, [fp, #-8]
    // 0x1393520: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1393520: add             x25, x1, x3, lsl #2
    //     0x1393524: add             x25, x25, #0xf
    //     0x1393528: str             w0, [x25]
    //     0x139352c: tbz             w0, #0, #0x1393548
    //     0x1393530: ldurb           w16, [x1, #-1]
    //     0x1393534: ldurb           w17, [x0, #-1]
    //     0x1393538: and             x16, x17, x16, lsr #2
    //     0x139353c: tst             x16, HEAP, lsr #32
    //     0x1393540: b.eq            #0x1393548
    //     0x1393544: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1393548: r0 = Null
    //     0x1393548: mov             x0, NULL
    // 0x139354c: LeaveFrame
    //     0x139354c: mov             SP, fp
    //     0x1393550: ldp             fp, lr, [SP], #0x10
    // 0x1393554: ret
    //     0x1393554: ret             
    // 0x1393558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393558: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x139355c: b               #0x1393440
    // 0x1393560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x1393560: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x1393564, size: 0x414
    // 0x1393564: EnterFrame
    //     0x1393564: stp             fp, lr, [SP, #-0x10]!
    //     0x1393568: mov             fp, SP
    // 0x139356c: AllocStack(0x50)
    //     0x139356c: sub             SP, SP, #0x50
    // 0x1393570: SetupParameters()
    //     0x1393570: ldr             x0, [fp, #0x10]
    //     0x1393574: ldur            w2, [x0, #0x17]
    //     0x1393578: add             x2, x2, HEAP, lsl #32
    //     0x139357c: stur            x2, [fp, #-8]
    // 0x1393580: CheckStackOverflow
    //     0x1393580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393584: cmp             SP, x16
    //     0x1393588: b.ls            #0x1393970
    // 0x139358c: LoadField: r1 = r2->field_f
    //     0x139358c: ldur            w1, [x2, #0xf]
    // 0x1393590: DecompressPointer r1
    //     0x1393590: add             x1, x1, HEAP, lsl #32
    // 0x1393594: r0 = controller()
    //     0x1393594: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393598: LoadField: r1 = r0->field_d7
    //     0x1393598: ldur            w1, [x0, #0xd7]
    // 0x139359c: DecompressPointer r1
    //     0x139359c: add             x1, x1, HEAP, lsl #32
    // 0x13935a0: r0 = value()
    //     0x13935a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13935a4: tbnz            w0, #4, #0x13936dc
    // 0x13935a8: ldur            x2, [fp, #-8]
    // 0x13935ac: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x13935ac: ldur            w0, [x2, #0x17]
    // 0x13935b0: DecompressPointer r0
    //     0x13935b0: add             x0, x0, HEAP, lsl #32
    // 0x13935b4: LoadField: r1 = r0->field_2b
    //     0x13935b4: ldur            w1, [x0, #0x2b]
    // 0x13935b8: DecompressPointer r1
    //     0x13935b8: add             x1, x1, HEAP, lsl #32
    // 0x13935bc: r16 = 12.000000
    //     0x13935bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13935c0: ldr             x16, [x16, #0x9e8]
    // 0x13935c4: r30 = Instance_Color
    //     0x13935c4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13935c8: stp             lr, x16, [SP]
    // 0x13935cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13935cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13935d0: ldr             x4, [x4, #0xaa0]
    // 0x13935d4: r0 = copyWith()
    //     0x13935d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13935d8: stur            x0, [fp, #-0x10]
    // 0x13935dc: r0 = Text()
    //     0x13935dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13935e0: mov             x3, x0
    // 0x13935e4: r0 = "Fetching your saved addresses from ShopDeck"
    //     0x13935e4: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3daa0] "Fetching your saved addresses from ShopDeck"
    //     0x13935e8: ldr             x0, [x0, #0xaa0]
    // 0x13935ec: stur            x3, [fp, #-0x18]
    // 0x13935f0: StoreField: r3->field_b = r0
    //     0x13935f0: stur            w0, [x3, #0xb]
    // 0x13935f4: ldur            x0, [fp, #-0x10]
    // 0x13935f8: StoreField: r3->field_13 = r0
    //     0x13935f8: stur            w0, [x3, #0x13]
    // 0x13935fc: r0 = Instance_TextAlign
    //     0x13935fc: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1393600: StoreField: r3->field_1b = r0
    //     0x1393600: stur            w0, [x3, #0x1b]
    // 0x1393604: r1 = Null
    //     0x1393604: mov             x1, NULL
    // 0x1393608: r2 = 6
    //     0x1393608: movz            x2, #0x6
    // 0x139360c: r0 = AllocateArray()
    //     0x139360c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1393610: stur            x0, [fp, #-0x10]
    // 0x1393614: r16 = Instance_CircularProgressIndicator
    //     0x1393614: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3daa8] Obj!CircularProgressIndicator@d65741
    //     0x1393618: ldr             x16, [x16, #0xaa8]
    // 0x139361c: StoreField: r0->field_f = r16
    //     0x139361c: stur            w16, [x0, #0xf]
    // 0x1393620: r16 = Instance_SizedBox
    //     0x1393620: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x1393624: ldr             x16, [x16, #0xd68]
    // 0x1393628: StoreField: r0->field_13 = r16
    //     0x1393628: stur            w16, [x0, #0x13]
    // 0x139362c: ldur            x1, [fp, #-0x18]
    // 0x1393630: ArrayStore: r0[0] = r1  ; List_4
    //     0x1393630: stur            w1, [x0, #0x17]
    // 0x1393634: r1 = <Widget>
    //     0x1393634: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1393638: r0 = AllocateGrowableArray()
    //     0x1393638: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x139363c: mov             x1, x0
    // 0x1393640: ldur            x0, [fp, #-0x10]
    // 0x1393644: stur            x1, [fp, #-0x18]
    // 0x1393648: StoreField: r1->field_f = r0
    //     0x1393648: stur            w0, [x1, #0xf]
    // 0x139364c: r0 = 6
    //     0x139364c: movz            x0, #0x6
    // 0x1393650: StoreField: r1->field_b = r0
    //     0x1393650: stur            w0, [x1, #0xb]
    // 0x1393654: r0 = Column()
    //     0x1393654: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1393658: mov             x1, x0
    // 0x139365c: r0 = Instance_Axis
    //     0x139365c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1393660: stur            x1, [fp, #-0x10]
    // 0x1393664: StoreField: r1->field_f = r0
    //     0x1393664: stur            w0, [x1, #0xf]
    // 0x1393668: r0 = Instance_MainAxisAlignment
    //     0x1393668: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x139366c: ldr             x0, [x0, #0xab0]
    // 0x1393670: StoreField: r1->field_13 = r0
    //     0x1393670: stur            w0, [x1, #0x13]
    // 0x1393674: r3 = Instance_MainAxisSize
    //     0x1393674: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1393678: ldr             x3, [x3, #0xa10]
    // 0x139367c: ArrayStore: r1[0] = r3  ; List_4
    //     0x139367c: stur            w3, [x1, #0x17]
    // 0x1393680: r4 = Instance_CrossAxisAlignment
    //     0x1393680: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1393684: ldr             x4, [x4, #0xa18]
    // 0x1393688: StoreField: r1->field_1b = r4
    //     0x1393688: stur            w4, [x1, #0x1b]
    // 0x139368c: r5 = Instance_VerticalDirection
    //     0x139368c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1393690: ldr             x5, [x5, #0xa20]
    // 0x1393694: StoreField: r1->field_23 = r5
    //     0x1393694: stur            w5, [x1, #0x23]
    // 0x1393698: r6 = Instance_Clip
    //     0x1393698: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x139369c: ldr             x6, [x6, #0x38]
    // 0x13936a0: StoreField: r1->field_2b = r6
    //     0x13936a0: stur            w6, [x1, #0x2b]
    // 0x13936a4: StoreField: r1->field_2f = rZR
    //     0x13936a4: stur            xzr, [x1, #0x2f]
    // 0x13936a8: ldur            x0, [fp, #-0x18]
    // 0x13936ac: StoreField: r1->field_b = r0
    //     0x13936ac: stur            w0, [x1, #0xb]
    // 0x13936b0: r0 = Center()
    //     0x13936b0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13936b4: mov             x1, x0
    // 0x13936b8: r0 = Instance_Alignment
    //     0x13936b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13936bc: ldr             x0, [x0, #0xb10]
    // 0x13936c0: StoreField: r1->field_f = r0
    //     0x13936c0: stur            w0, [x1, #0xf]
    // 0x13936c4: ldur            x0, [fp, #-0x10]
    // 0x13936c8: StoreField: r1->field_b = r0
    //     0x13936c8: stur            w0, [x1, #0xb]
    // 0x13936cc: mov             x0, x1
    // 0x13936d0: LeaveFrame
    //     0x13936d0: mov             SP, fp
    //     0x13936d4: ldp             fp, lr, [SP], #0x10
    // 0x13936d8: ret
    //     0x13936d8: ret             
    // 0x13936dc: ldur            x2, [fp, #-8]
    // 0x13936e0: r4 = Instance_CrossAxisAlignment
    //     0x13936e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13936e4: ldr             x4, [x4, #0xa18]
    // 0x13936e8: r3 = Instance_MainAxisSize
    //     0x13936e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13936ec: ldr             x3, [x3, #0xa10]
    // 0x13936f0: r5 = Instance_VerticalDirection
    //     0x13936f0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13936f4: ldr             x5, [x5, #0xa20]
    // 0x13936f8: r0 = Instance_Axis
    //     0x13936f8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13936fc: r6 = Instance_Clip
    //     0x13936fc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1393700: ldr             x6, [x6, #0x38]
    // 0x1393704: r0 = Obx()
    //     0x1393704: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1393708: ldur            x2, [fp, #-8]
    // 0x139370c: r1 = Function '<anonymous closure>':.
    //     0x139370c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41360] AnonymousClosure: (0x1393a00), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x1393710: ldr             x1, [x1, #0x360]
    // 0x1393714: stur            x0, [fp, #-0x10]
    // 0x1393718: r0 = AllocateClosure()
    //     0x1393718: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x139371c: mov             x1, x0
    // 0x1393720: ldur            x0, [fp, #-0x10]
    // 0x1393724: StoreField: r0->field_b = r1
    //     0x1393724: stur            w1, [x0, #0xb]
    // 0x1393728: r0 = Obx()
    //     0x1393728: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x139372c: ldur            x2, [fp, #-8]
    // 0x1393730: r1 = Function '<anonymous closure>':.
    //     0x1393730: add             x1, PP, #0x41, lsl #12  ; [pp+0x41368] AnonymousClosure: (0x1393990), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x1393734: ldr             x1, [x1, #0x368]
    // 0x1393738: stur            x0, [fp, #-0x18]
    // 0x139373c: r0 = AllocateClosure()
    //     0x139373c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393740: mov             x1, x0
    // 0x1393744: ldur            x0, [fp, #-0x18]
    // 0x1393748: StoreField: r0->field_b = r1
    //     0x1393748: stur            w1, [x0, #0xb]
    // 0x139374c: ldur            x2, [fp, #-8]
    // 0x1393750: LoadField: r1 = r2->field_f
    //     0x1393750: ldur            w1, [x2, #0xf]
    // 0x1393754: DecompressPointer r1
    //     0x1393754: add             x1, x1, HEAP, lsl #32
    // 0x1393758: r0 = controller()
    //     0x1393758: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x139375c: LoadField: r1 = r0->field_5f
    //     0x139375c: ldur            w1, [x0, #0x5f]
    // 0x1393760: DecompressPointer r1
    //     0x1393760: add             x1, x1, HEAP, lsl #32
    // 0x1393764: r0 = value()
    //     0x1393764: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393768: ldur            x2, [fp, #-8]
    // 0x139376c: stur            x0, [fp, #-0x20]
    // 0x1393770: LoadField: r1 = r2->field_f
    //     0x1393770: ldur            w1, [x2, #0xf]
    // 0x1393774: DecompressPointer r1
    //     0x1393774: add             x1, x1, HEAP, lsl #32
    // 0x1393778: r0 = controller()
    //     0x1393778: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x139377c: LoadField: r1 = r0->field_5b
    //     0x139377c: ldur            w1, [x0, #0x5b]
    // 0x1393780: DecompressPointer r1
    //     0x1393780: add             x1, x1, HEAP, lsl #32
    // 0x1393784: r0 = value()
    //     0x1393784: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393788: ldur            x2, [fp, #-8]
    // 0x139378c: stur            x0, [fp, #-0x28]
    // 0x1393790: LoadField: r1 = r2->field_f
    //     0x1393790: ldur            w1, [x2, #0xf]
    // 0x1393794: DecompressPointer r1
    //     0x1393794: add             x1, x1, HEAP, lsl #32
    // 0x1393798: r0 = controller()
    //     0x1393798: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x139379c: LoadField: r1 = r0->field_53
    //     0x139379c: ldur            w1, [x0, #0x53]
    // 0x13937a0: DecompressPointer r1
    //     0x13937a0: add             x1, x1, HEAP, lsl #32
    // 0x13937a4: r0 = value()
    //     0x13937a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13937a8: ldur            x2, [fp, #-8]
    // 0x13937ac: LoadField: r1 = r2->field_f
    //     0x13937ac: ldur            w1, [x2, #0xf]
    // 0x13937b0: DecompressPointer r1
    //     0x13937b0: add             x1, x1, HEAP, lsl #32
    // 0x13937b4: r0 = controller()
    //     0x13937b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13937b8: LoadField: r1 = r0->field_93
    //     0x13937b8: ldur            w1, [x0, #0x93]
    // 0x13937bc: DecompressPointer r1
    //     0x13937bc: add             x1, x1, HEAP, lsl #32
    // 0x13937c0: stur            x1, [fp, #-0x30]
    // 0x13937c4: r0 = CheckoutBagAccordion()
    //     0x13937c4: bl              #0x1393984  ; AllocateCheckoutBagAccordionStub -> CheckoutBagAccordion (size=0x1c)
    // 0x13937c8: mov             x3, x0
    // 0x13937cc: ldur            x0, [fp, #-0x20]
    // 0x13937d0: stur            x3, [fp, #-0x38]
    // 0x13937d4: StoreField: r3->field_b = r0
    //     0x13937d4: stur            w0, [x3, #0xb]
    // 0x13937d8: ldur            x0, [fp, #-0x28]
    // 0x13937dc: StoreField: r3->field_f = r0
    //     0x13937dc: stur            w0, [x3, #0xf]
    // 0x13937e0: ldur            x0, [fp, #-0x30]
    // 0x13937e4: StoreField: r3->field_13 = r0
    //     0x13937e4: stur            w0, [x3, #0x13]
    // 0x13937e8: ldur            x2, [fp, #-8]
    // 0x13937ec: r1 = Function '<anonymous closure>':.
    //     0x13937ec: add             x1, PP, #0x41, lsl #12  ; [pp+0x41370] AnonymousClosure: (0x1391d98), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x13937f0: ldr             x1, [x1, #0x370]
    // 0x13937f4: r0 = AllocateClosure()
    //     0x13937f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13937f8: mov             x1, x0
    // 0x13937fc: ldur            x0, [fp, #-0x38]
    // 0x1393800: ArrayStore: r0[0] = r1  ; List_4
    //     0x1393800: stur            w1, [x0, #0x17]
    // 0x1393804: ldur            x2, [fp, #-8]
    // 0x1393808: LoadField: r1 = r2->field_f
    //     0x1393808: ldur            w1, [x2, #0xf]
    // 0x139380c: DecompressPointer r1
    //     0x139380c: add             x1, x1, HEAP, lsl #32
    // 0x1393810: r0 = controller()
    //     0x1393810: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393814: LoadField: r1 = r0->field_7b
    //     0x1393814: ldur            w1, [x0, #0x7b]
    // 0x1393818: DecompressPointer r1
    //     0x1393818: add             x1, x1, HEAP, lsl #32
    // 0x139381c: r0 = value()
    //     0x139381c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393820: ldur            x2, [fp, #-8]
    // 0x1393824: stur            x0, [fp, #-0x20]
    // 0x1393828: LoadField: r1 = r2->field_f
    //     0x1393828: ldur            w1, [x2, #0xf]
    // 0x139382c: DecompressPointer r1
    //     0x139382c: add             x1, x1, HEAP, lsl #32
    // 0x1393830: r0 = controller()
    //     0x1393830: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393834: LoadField: r1 = r0->field_cb
    //     0x1393834: ldur            w1, [x0, #0xcb]
    // 0x1393838: DecompressPointer r1
    //     0x1393838: add             x1, x1, HEAP, lsl #32
    // 0x139383c: r0 = value()
    //     0x139383c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393840: cmp             w0, NULL
    // 0x1393844: b.ne            #0x1393850
    // 0x1393848: r6 = false
    //     0x1393848: add             x6, NULL, #0x30  ; false
    // 0x139384c: b               #0x1393854
    // 0x1393850: mov             x6, x0
    // 0x1393854: ldur            x5, [fp, #-0x10]
    // 0x1393858: ldur            x4, [fp, #-0x18]
    // 0x139385c: ldur            x3, [fp, #-0x38]
    // 0x1393860: ldur            x0, [fp, #-0x20]
    // 0x1393864: ldur            x2, [fp, #-8]
    // 0x1393868: stur            x6, [fp, #-0x28]
    // 0x139386c: r1 = Function '<anonymous closure>':.
    //     0x139386c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41378] AnonymousClosure: (0x1393418), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x1393870: ldr             x1, [x1, #0x378]
    // 0x1393874: r0 = AllocateClosure()
    //     0x1393874: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393878: stur            x0, [fp, #-0x30]
    // 0x139387c: r0 = CheckoutNumberWidget()
    //     0x139387c: bl              #0x1393978  ; AllocateCheckoutNumberWidgetStub -> CheckoutNumberWidget (size=0x1c)
    // 0x1393880: mov             x3, x0
    // 0x1393884: ldur            x0, [fp, #-0x30]
    // 0x1393888: stur            x3, [fp, #-0x40]
    // 0x139388c: StoreField: r3->field_b = r0
    //     0x139388c: stur            w0, [x3, #0xb]
    // 0x1393890: ldur            x2, [fp, #-8]
    // 0x1393894: r1 = Function '<anonymous closure>':.
    //     0x1393894: add             x1, PP, #0x41, lsl #12  ; [pp+0x41380] AnonymousClosure: (0x13913a8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1393898: ldr             x1, [x1, #0x380]
    // 0x139389c: r0 = AllocateClosure()
    //     0x139389c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13938a0: mov             x1, x0
    // 0x13938a4: ldur            x0, [fp, #-0x40]
    // 0x13938a8: StoreField: r0->field_f = r1
    //     0x13938a8: stur            w1, [x0, #0xf]
    // 0x13938ac: ldur            x1, [fp, #-0x20]
    // 0x13938b0: StoreField: r0->field_13 = r1
    //     0x13938b0: stur            w1, [x0, #0x13]
    // 0x13938b4: ldur            x1, [fp, #-0x28]
    // 0x13938b8: ArrayStore: r0[0] = r1  ; List_4
    //     0x13938b8: stur            w1, [x0, #0x17]
    // 0x13938bc: r1 = Null
    //     0x13938bc: mov             x1, NULL
    // 0x13938c0: r2 = 8
    //     0x13938c0: movz            x2, #0x8
    // 0x13938c4: r0 = AllocateArray()
    //     0x13938c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13938c8: mov             x2, x0
    // 0x13938cc: ldur            x0, [fp, #-0x10]
    // 0x13938d0: stur            x2, [fp, #-8]
    // 0x13938d4: StoreField: r2->field_f = r0
    //     0x13938d4: stur            w0, [x2, #0xf]
    // 0x13938d8: ldur            x0, [fp, #-0x18]
    // 0x13938dc: StoreField: r2->field_13 = r0
    //     0x13938dc: stur            w0, [x2, #0x13]
    // 0x13938e0: ldur            x0, [fp, #-0x38]
    // 0x13938e4: ArrayStore: r2[0] = r0  ; List_4
    //     0x13938e4: stur            w0, [x2, #0x17]
    // 0x13938e8: ldur            x0, [fp, #-0x40]
    // 0x13938ec: StoreField: r2->field_1b = r0
    //     0x13938ec: stur            w0, [x2, #0x1b]
    // 0x13938f0: r1 = <Widget>
    //     0x13938f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13938f4: r0 = AllocateGrowableArray()
    //     0x13938f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13938f8: mov             x1, x0
    // 0x13938fc: ldur            x0, [fp, #-8]
    // 0x1393900: stur            x1, [fp, #-0x10]
    // 0x1393904: StoreField: r1->field_f = r0
    //     0x1393904: stur            w0, [x1, #0xf]
    // 0x1393908: r0 = 8
    //     0x1393908: movz            x0, #0x8
    // 0x139390c: StoreField: r1->field_b = r0
    //     0x139390c: stur            w0, [x1, #0xb]
    // 0x1393910: r0 = Column()
    //     0x1393910: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1393914: r1 = Instance_Axis
    //     0x1393914: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1393918: StoreField: r0->field_f = r1
    //     0x1393918: stur            w1, [x0, #0xf]
    // 0x139391c: r1 = Instance_MainAxisAlignment
    //     0x139391c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1393920: ldr             x1, [x1, #0xa08]
    // 0x1393924: StoreField: r0->field_13 = r1
    //     0x1393924: stur            w1, [x0, #0x13]
    // 0x1393928: r1 = Instance_MainAxisSize
    //     0x1393928: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x139392c: ldr             x1, [x1, #0xa10]
    // 0x1393930: ArrayStore: r0[0] = r1  ; List_4
    //     0x1393930: stur            w1, [x0, #0x17]
    // 0x1393934: r1 = Instance_CrossAxisAlignment
    //     0x1393934: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1393938: ldr             x1, [x1, #0xa18]
    // 0x139393c: StoreField: r0->field_1b = r1
    //     0x139393c: stur            w1, [x0, #0x1b]
    // 0x1393940: r1 = Instance_VerticalDirection
    //     0x1393940: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1393944: ldr             x1, [x1, #0xa20]
    // 0x1393948: StoreField: r0->field_23 = r1
    //     0x1393948: stur            w1, [x0, #0x23]
    // 0x139394c: r1 = Instance_Clip
    //     0x139394c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1393950: ldr             x1, [x1, #0x38]
    // 0x1393954: StoreField: r0->field_2b = r1
    //     0x1393954: stur            w1, [x0, #0x2b]
    // 0x1393958: StoreField: r0->field_2f = rZR
    //     0x1393958: stur            xzr, [x0, #0x2f]
    // 0x139395c: ldur            x1, [fp, #-0x10]
    // 0x1393960: StoreField: r0->field_b = r1
    //     0x1393960: stur            w1, [x0, #0xb]
    // 0x1393964: LeaveFrame
    //     0x1393964: mov             SP, fp
    //     0x1393968: ldp             fp, lr, [SP], #0x10
    // 0x139396c: ret
    //     0x139396c: ret             
    // 0x1393970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393970: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393974: b               #0x139358c
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x1393990, size: 0x64
    // 0x1393990: EnterFrame
    //     0x1393990: stp             fp, lr, [SP, #-0x10]!
    //     0x1393994: mov             fp, SP
    // 0x1393998: AllocStack(0x8)
    //     0x1393998: sub             SP, SP, #8
    // 0x139399c: SetupParameters()
    //     0x139399c: ldr             x0, [fp, #0x10]
    //     0x13939a0: ldur            w1, [x0, #0x17]
    //     0x13939a4: add             x1, x1, HEAP, lsl #32
    // 0x13939a8: CheckStackOverflow
    //     0x13939a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13939ac: cmp             SP, x16
    //     0x13939b0: b.ls            #0x13939ec
    // 0x13939b4: LoadField: r0 = r1->field_f
    //     0x13939b4: ldur            w0, [x1, #0xf]
    // 0x13939b8: DecompressPointer r0
    //     0x13939b8: add             x0, x0, HEAP, lsl #32
    // 0x13939bc: mov             x1, x0
    // 0x13939c0: r0 = controller()
    //     0x13939c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13939c4: LoadField: r1 = r0->field_83
    //     0x13939c4: ldur            w1, [x0, #0x83]
    // 0x13939c8: DecompressPointer r1
    //     0x13939c8: add             x1, x1, HEAP, lsl #32
    // 0x13939cc: r0 = value()
    //     0x13939cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13939d0: stur            x0, [fp, #-8]
    // 0x13939d4: r0 = CheckoutBreadCrumb()
    //     0x13939d4: bl              #0x13939f4  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x10)
    // 0x13939d8: ldur            x1, [fp, #-8]
    // 0x13939dc: StoreField: r0->field_b = r1
    //     0x13939dc: stur            w1, [x0, #0xb]
    // 0x13939e0: LeaveFrame
    //     0x13939e0: mov             SP, fp
    //     0x13939e4: ldp             fp, lr, [SP], #0x10
    // 0x13939e8: ret
    //     0x13939e8: ret             
    // 0x13939ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13939ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13939f0: b               #0x13939b4
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x1393a00, size: 0x144
    // 0x1393a00: EnterFrame
    //     0x1393a00: stp             fp, lr, [SP, #-0x10]!
    //     0x1393a04: mov             fp, SP
    // 0x1393a08: AllocStack(0x20)
    //     0x1393a08: sub             SP, SP, #0x20
    // 0x1393a0c: SetupParameters()
    //     0x1393a0c: ldr             x0, [fp, #0x10]
    //     0x1393a10: ldur            w2, [x0, #0x17]
    //     0x1393a14: add             x2, x2, HEAP, lsl #32
    //     0x1393a18: stur            x2, [fp, #-8]
    // 0x1393a1c: CheckStackOverflow
    //     0x1393a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393a20: cmp             SP, x16
    //     0x1393a24: b.ls            #0x1393b38
    // 0x1393a28: LoadField: r1 = r2->field_f
    //     0x1393a28: ldur            w1, [x2, #0xf]
    // 0x1393a2c: DecompressPointer r1
    //     0x1393a2c: add             x1, x1, HEAP, lsl #32
    // 0x1393a30: r0 = controller()
    //     0x1393a30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393a34: LoadField: r1 = r0->field_ab
    //     0x1393a34: ldur            w1, [x0, #0xab]
    // 0x1393a38: DecompressPointer r1
    //     0x1393a38: add             x1, x1, HEAP, lsl #32
    // 0x1393a3c: r0 = value()
    //     0x1393a3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393a40: tbnz            w0, #4, #0x1393b28
    // 0x1393a44: r0 = LoadStaticField(0x878)
    //     0x1393a44: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1393a48: ldr             x0, [x0, #0x10f0]
    // 0x1393a4c: cmp             w0, NULL
    // 0x1393a50: b.eq            #0x1393b40
    // 0x1393a54: LoadField: r3 = r0->field_53
    //     0x1393a54: ldur            w3, [x0, #0x53]
    // 0x1393a58: DecompressPointer r3
    //     0x1393a58: add             x3, x3, HEAP, lsl #32
    // 0x1393a5c: stur            x3, [fp, #-0x18]
    // 0x1393a60: LoadField: r0 = r3->field_7
    //     0x1393a60: ldur            w0, [x3, #7]
    // 0x1393a64: DecompressPointer r0
    //     0x1393a64: add             x0, x0, HEAP, lsl #32
    // 0x1393a68: ldur            x2, [fp, #-8]
    // 0x1393a6c: stur            x0, [fp, #-0x10]
    // 0x1393a70: r1 = Function '<anonymous closure>':.
    //     0x1393a70: add             x1, PP, #0x41, lsl #12  ; [pp+0x413a0] AnonymousClosure: (0x1393b44), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x1393a74: ldr             x1, [x1, #0x3a0]
    // 0x1393a78: r0 = AllocateClosure()
    //     0x1393a78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393a7c: ldur            x2, [fp, #-0x10]
    // 0x1393a80: mov             x3, x0
    // 0x1393a84: r1 = Null
    //     0x1393a84: mov             x1, NULL
    // 0x1393a88: stur            x3, [fp, #-8]
    // 0x1393a8c: cmp             w2, NULL
    // 0x1393a90: b.eq            #0x1393ab0
    // 0x1393a94: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x1393a94: ldur            w4, [x2, #0x17]
    // 0x1393a98: DecompressPointer r4
    //     0x1393a98: add             x4, x4, HEAP, lsl #32
    // 0x1393a9c: r8 = X0
    //     0x1393a9c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x1393aa0: LoadField: r9 = r4->field_7
    //     0x1393aa0: ldur            x9, [x4, #7]
    // 0x1393aa4: r3 = Null
    //     0x1393aa4: add             x3, PP, #0x41, lsl #12  ; [pp+0x413a8] Null
    //     0x1393aa8: ldr             x3, [x3, #0x3a8]
    // 0x1393aac: blr             x9
    // 0x1393ab0: ldur            x0, [fp, #-0x18]
    // 0x1393ab4: LoadField: r1 = r0->field_b
    //     0x1393ab4: ldur            w1, [x0, #0xb]
    // 0x1393ab8: LoadField: r2 = r0->field_f
    //     0x1393ab8: ldur            w2, [x0, #0xf]
    // 0x1393abc: DecompressPointer r2
    //     0x1393abc: add             x2, x2, HEAP, lsl #32
    // 0x1393ac0: LoadField: r3 = r2->field_b
    //     0x1393ac0: ldur            w3, [x2, #0xb]
    // 0x1393ac4: r2 = LoadInt32Instr(r1)
    //     0x1393ac4: sbfx            x2, x1, #1, #0x1f
    // 0x1393ac8: stur            x2, [fp, #-0x20]
    // 0x1393acc: r1 = LoadInt32Instr(r3)
    //     0x1393acc: sbfx            x1, x3, #1, #0x1f
    // 0x1393ad0: cmp             x2, x1
    // 0x1393ad4: b.ne            #0x1393ae0
    // 0x1393ad8: mov             x1, x0
    // 0x1393adc: r0 = _growToNextCapacity()
    //     0x1393adc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1393ae0: ldur            x2, [fp, #-0x18]
    // 0x1393ae4: ldur            x3, [fp, #-0x20]
    // 0x1393ae8: add             x4, x3, #1
    // 0x1393aec: lsl             x5, x4, #1
    // 0x1393af0: StoreField: r2->field_b = r5
    //     0x1393af0: stur            w5, [x2, #0xb]
    // 0x1393af4: LoadField: r1 = r2->field_f
    //     0x1393af4: ldur            w1, [x2, #0xf]
    // 0x1393af8: DecompressPointer r1
    //     0x1393af8: add             x1, x1, HEAP, lsl #32
    // 0x1393afc: ldur            x0, [fp, #-8]
    // 0x1393b00: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1393b00: add             x25, x1, x3, lsl #2
    //     0x1393b04: add             x25, x25, #0xf
    //     0x1393b08: str             w0, [x25]
    //     0x1393b0c: tbz             w0, #0, #0x1393b28
    //     0x1393b10: ldurb           w16, [x1, #-1]
    //     0x1393b14: ldurb           w17, [x0, #-1]
    //     0x1393b18: and             x16, x17, x16, lsr #2
    //     0x1393b1c: tst             x16, HEAP, lsr #32
    //     0x1393b20: b.eq            #0x1393b28
    //     0x1393b24: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1393b28: r0 = Instance_SizedBox
    //     0x1393b28: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1393b2c: LeaveFrame
    //     0x1393b2c: mov             SP, fp
    //     0x1393b30: ldp             fp, lr, [SP], #0x10
    // 0x1393b34: ret
    //     0x1393b34: ret             
    // 0x1393b38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393b38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393b3c: b               #0x1393a28
    // 0x1393b40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x1393b40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x1393b44, size: 0x74
    // 0x1393b44: EnterFrame
    //     0x1393b44: stp             fp, lr, [SP, #-0x10]!
    //     0x1393b48: mov             fp, SP
    // 0x1393b4c: AllocStack(0x8)
    //     0x1393b4c: sub             SP, SP, #8
    // 0x1393b50: SetupParameters()
    //     0x1393b50: ldr             x0, [fp, #0x18]
    //     0x1393b54: ldur            w2, [x0, #0x17]
    //     0x1393b58: add             x2, x2, HEAP, lsl #32
    //     0x1393b5c: stur            x2, [fp, #-8]
    // 0x1393b60: CheckStackOverflow
    //     0x1393b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393b64: cmp             SP, x16
    //     0x1393b68: b.ls            #0x1393bb0
    // 0x1393b6c: LoadField: r1 = r2->field_f
    //     0x1393b6c: ldur            w1, [x2, #0xf]
    // 0x1393b70: DecompressPointer r1
    //     0x1393b70: add             x1, x1, HEAP, lsl #32
    // 0x1393b74: r0 = controller()
    //     0x1393b74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393b78: LoadField: r1 = r0->field_ab
    //     0x1393b78: ldur            w1, [x0, #0xab]
    // 0x1393b7c: DecompressPointer r1
    //     0x1393b7c: add             x1, x1, HEAP, lsl #32
    // 0x1393b80: r2 = false
    //     0x1393b80: add             x2, NULL, #0x30  ; false
    // 0x1393b84: r0 = value=()
    //     0x1393b84: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1393b88: ldur            x0, [fp, #-8]
    // 0x1393b8c: LoadField: r1 = r0->field_f
    //     0x1393b8c: ldur            w1, [x0, #0xf]
    // 0x1393b90: DecompressPointer r1
    //     0x1393b90: add             x1, x1, HEAP, lsl #32
    // 0x1393b94: LoadField: r2 = r0->field_13
    //     0x1393b94: ldur            w2, [x0, #0x13]
    // 0x1393b98: DecompressPointer r2
    //     0x1393b98: add             x2, x2, HEAP, lsl #32
    // 0x1393b9c: r0 = _showOtpBottomSheet()
    //     0x1393b9c: bl              #0x1393bb8  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet
    // 0x1393ba0: r0 = Null
    //     0x1393ba0: mov             x0, NULL
    // 0x1393ba4: LeaveFrame
    //     0x1393ba4: mov             SP, fp
    //     0x1393ba8: ldp             fp, lr, [SP], #0x10
    // 0x1393bac: ret
    //     0x1393bac: ret             
    // 0x1393bb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393bb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393bb4: b               #0x1393b6c
  }
  _ _showOtpBottomSheet(/* No info */) {
    // ** addr: 0x1393bb8, size: 0xb4
    // 0x1393bb8: EnterFrame
    //     0x1393bb8: stp             fp, lr, [SP, #-0x10]!
    //     0x1393bbc: mov             fp, SP
    // 0x1393bc0: AllocStack(0x40)
    //     0x1393bc0: sub             SP, SP, #0x40
    // 0x1393bc4: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1393bc4: stur            x1, [fp, #-8]
    //     0x1393bc8: stur            x2, [fp, #-0x10]
    // 0x1393bcc: CheckStackOverflow
    //     0x1393bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393bd0: cmp             SP, x16
    //     0x1393bd4: b.ls            #0x1393c64
    // 0x1393bd8: r1 = 1
    //     0x1393bd8: movz            x1, #0x1
    // 0x1393bdc: r0 = AllocateContext()
    //     0x1393bdc: bl              #0x16f6108  ; AllocateContextStub
    // 0x1393be0: mov             x3, x0
    // 0x1393be4: ldur            x0, [fp, #-8]
    // 0x1393be8: stur            x3, [fp, #-0x18]
    // 0x1393bec: StoreField: r3->field_f = r0
    //     0x1393bec: stur            w0, [x3, #0xf]
    // 0x1393bf0: mov             x2, x3
    // 0x1393bf4: r1 = Function '<anonymous closure>':.
    //     0x1393bf4: add             x1, PP, #0x41, lsl #12  ; [pp+0x413b8] AnonymousClosure: (0x1393c6c), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1393bb8)
    //     0x1393bf8: ldr             x1, [x1, #0x3b8]
    // 0x1393bfc: r0 = AllocateClosure()
    //     0x1393bfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393c00: stp             x0, NULL, [SP, #0x18]
    // 0x1393c04: ldur            x16, [fp, #-0x10]
    // 0x1393c08: r30 = true
    //     0x1393c08: add             lr, NULL, #0x20  ; true
    // 0x1393c0c: stp             lr, x16, [SP, #8]
    // 0x1393c10: r16 = Instance_RoundedRectangleBorder
    //     0x1393c10: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1393c14: ldr             x16, [x16, #0xc78]
    // 0x1393c18: str             x16, [SP]
    // 0x1393c1c: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x1393c1c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x1393c20: ldr             x4, [x4, #0xb20]
    // 0x1393c24: r0 = showModalBottomSheet()
    //     0x1393c24: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1393c28: ldur            x2, [fp, #-0x18]
    // 0x1393c2c: r1 = Function '<anonymous closure>':.
    //     0x1393c2c: add             x1, PP, #0x41, lsl #12  ; [pp+0x413c0] AnonymousClosure: (0x139210c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1393c30: ldr             x1, [x1, #0x3c0]
    // 0x1393c34: stur            x0, [fp, #-8]
    // 0x1393c38: r0 = AllocateClosure()
    //     0x1393c38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393c3c: r16 = <Null?>
    //     0x1393c3c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x1393c40: ldur            lr, [fp, #-8]
    // 0x1393c44: stp             lr, x16, [SP, #8]
    // 0x1393c48: str             x0, [SP]
    // 0x1393c4c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x1393c4c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x1393c50: r0 = then()
    //     0x1393c50: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x1393c54: r0 = Null
    //     0x1393c54: mov             x0, NULL
    // 0x1393c58: LeaveFrame
    //     0x1393c58: mov             SP, fp
    //     0x1393c5c: ldp             fp, lr, [SP], #0x10
    // 0x1393c60: ret
    //     0x1393c60: ret             
    // 0x1393c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393c64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393c68: b               #0x1393bd8
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1393c6c, size: 0x130
    // 0x1393c6c: EnterFrame
    //     0x1393c6c: stp             fp, lr, [SP, #-0x10]!
    //     0x1393c70: mov             fp, SP
    // 0x1393c74: AllocStack(0x30)
    //     0x1393c74: sub             SP, SP, #0x30
    // 0x1393c78: SetupParameters()
    //     0x1393c78: ldr             x0, [fp, #0x18]
    //     0x1393c7c: ldur            w1, [x0, #0x17]
    //     0x1393c80: add             x1, x1, HEAP, lsl #32
    //     0x1393c84: stur            x1, [fp, #-8]
    // 0x1393c88: CheckStackOverflow
    //     0x1393c88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393c8c: cmp             SP, x16
    //     0x1393c90: b.ls            #0x1393d94
    // 0x1393c94: r1 = 1
    //     0x1393c94: movz            x1, #0x1
    // 0x1393c98: r0 = AllocateContext()
    //     0x1393c98: bl              #0x16f6108  ; AllocateContextStub
    // 0x1393c9c: mov             x2, x0
    // 0x1393ca0: ldur            x0, [fp, #-8]
    // 0x1393ca4: stur            x2, [fp, #-0x10]
    // 0x1393ca8: StoreField: r2->field_b = r0
    //     0x1393ca8: stur            w0, [x2, #0xb]
    // 0x1393cac: ldr             x1, [fp, #0x10]
    // 0x1393cb0: StoreField: r2->field_f = r1
    //     0x1393cb0: stur            w1, [x2, #0xf]
    // 0x1393cb4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1393cb4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1393cb8: r0 = _of()
    //     0x1393cb8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1393cbc: LoadField: r1 = r0->field_23
    //     0x1393cbc: ldur            w1, [x0, #0x23]
    // 0x1393cc0: DecompressPointer r1
    //     0x1393cc0: add             x1, x1, HEAP, lsl #32
    // 0x1393cc4: LoadField: d0 = r1->field_1f
    //     0x1393cc4: ldur            d0, [x1, #0x1f]
    // 0x1393cc8: stur            d0, [fp, #-0x30]
    // 0x1393ccc: r0 = EdgeInsets()
    //     0x1393ccc: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1393cd0: stur            x0, [fp, #-0x18]
    // 0x1393cd4: StoreField: r0->field_7 = rZR
    //     0x1393cd4: stur            xzr, [x0, #7]
    // 0x1393cd8: StoreField: r0->field_f = rZR
    //     0x1393cd8: stur            xzr, [x0, #0xf]
    // 0x1393cdc: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1393cdc: stur            xzr, [x0, #0x17]
    // 0x1393ce0: ldur            d0, [fp, #-0x30]
    // 0x1393ce4: StoreField: r0->field_1f = d0
    //     0x1393ce4: stur            d0, [x0, #0x1f]
    // 0x1393ce8: ldur            x1, [fp, #-8]
    // 0x1393cec: LoadField: r2 = r1->field_f
    //     0x1393cec: ldur            w2, [x1, #0xf]
    // 0x1393cf0: DecompressPointer r2
    //     0x1393cf0: add             x2, x2, HEAP, lsl #32
    // 0x1393cf4: mov             x1, x2
    // 0x1393cf8: r0 = controller()
    //     0x1393cf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393cfc: LoadField: r1 = r0->field_7b
    //     0x1393cfc: ldur            w1, [x0, #0x7b]
    // 0x1393d00: DecompressPointer r1
    //     0x1393d00: add             x1, x1, HEAP, lsl #32
    // 0x1393d04: r0 = value()
    //     0x1393d04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393d08: ldur            x2, [fp, #-0x10]
    // 0x1393d0c: r1 = Function '<anonymous closure>':.
    //     0x1393d0c: add             x1, PP, #0x41, lsl #12  ; [pp+0x413c8] AnonymousClosure: (0x1393da8), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1393bb8)
    //     0x1393d10: ldr             x1, [x1, #0x3c8]
    // 0x1393d14: stur            x0, [fp, #-8]
    // 0x1393d18: r0 = AllocateClosure()
    //     0x1393d18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393d1c: stur            x0, [fp, #-0x20]
    // 0x1393d20: r0 = OtpBottomSheet()
    //     0x1393d20: bl              #0x1393d9c  ; AllocateOtpBottomSheetStub -> OtpBottomSheet (size=0x1c)
    // 0x1393d24: mov             x3, x0
    // 0x1393d28: ldur            x0, [fp, #-0x20]
    // 0x1393d2c: stur            x3, [fp, #-0x28]
    // 0x1393d30: StoreField: r3->field_b = r0
    //     0x1393d30: stur            w0, [x3, #0xb]
    // 0x1393d34: ldur            x2, [fp, #-0x10]
    // 0x1393d38: r1 = Function '<anonymous closure>':.
    //     0x1393d38: add             x1, PP, #0x41, lsl #12  ; [pp+0x413d0] AnonymousClosure: (0x1392328), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1393d3c: ldr             x1, [x1, #0x3d0]
    // 0x1393d40: r0 = AllocateClosure()
    //     0x1393d40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393d44: mov             x1, x0
    // 0x1393d48: ldur            x0, [fp, #-0x28]
    // 0x1393d4c: StoreField: r0->field_f = r1
    //     0x1393d4c: stur            w1, [x0, #0xf]
    // 0x1393d50: ldur            x1, [fp, #-8]
    // 0x1393d54: StoreField: r0->field_13 = r1
    //     0x1393d54: stur            w1, [x0, #0x13]
    // 0x1393d58: ldur            x2, [fp, #-0x10]
    // 0x1393d5c: r1 = Function '<anonymous closure>':.
    //     0x1393d5c: add             x1, PP, #0x41, lsl #12  ; [pp+0x413d8] AnonymousClosure: (0x13922a0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1393d60: ldr             x1, [x1, #0x3d8]
    // 0x1393d64: r0 = AllocateClosure()
    //     0x1393d64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1393d68: mov             x1, x0
    // 0x1393d6c: ldur            x0, [fp, #-0x28]
    // 0x1393d70: ArrayStore: r0[0] = r1  ; List_4
    //     0x1393d70: stur            w1, [x0, #0x17]
    // 0x1393d74: r0 = Padding()
    //     0x1393d74: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1393d78: ldur            x1, [fp, #-0x18]
    // 0x1393d7c: StoreField: r0->field_f = r1
    //     0x1393d7c: stur            w1, [x0, #0xf]
    // 0x1393d80: ldur            x1, [fp, #-0x28]
    // 0x1393d84: StoreField: r0->field_b = r1
    //     0x1393d84: stur            w1, [x0, #0xb]
    // 0x1393d88: LeaveFrame
    //     0x1393d88: mov             SP, fp
    //     0x1393d8c: ldp             fp, lr, [SP], #0x10
    // 0x1393d90: ret
    //     0x1393d90: ret             
    // 0x1393d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393d94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393d98: b               #0x1393c94
  }
  [closure] Future<Null> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x1393da8, size: 0x1a0
    // 0x1393da8: EnterFrame
    //     0x1393da8: stp             fp, lr, [SP, #-0x10]!
    //     0x1393dac: mov             fp, SP
    // 0x1393db0: AllocStack(0x58)
    //     0x1393db0: sub             SP, SP, #0x58
    // 0x1393db4: SetupParameters(CheckoutRequestNumberPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1393db4: stur            NULL, [fp, #-8]
    //     0x1393db8: movz            x0, #0
    //     0x1393dbc: add             x1, fp, w0, sxtw #2
    //     0x1393dc0: ldr             x1, [x1, #0x18]
    //     0x1393dc4: add             x2, fp, w0, sxtw #2
    //     0x1393dc8: ldr             x2, [x2, #0x10]
    //     0x1393dcc: stur            x2, [fp, #-0x18]
    //     0x1393dd0: ldur            w3, [x1, #0x17]
    //     0x1393dd4: add             x3, x3, HEAP, lsl #32
    //     0x1393dd8: stur            x3, [fp, #-0x10]
    // 0x1393ddc: CheckStackOverflow
    //     0x1393ddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1393de0: cmp             SP, x16
    //     0x1393de4: b.ls            #0x1393f40
    // 0x1393de8: InitAsync() -> Future<Null?>
    //     0x1393de8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x1393dec: bl              #0x6326e0  ; InitAsyncStub
    // 0x1393df0: ldur            x0, [fp, #-0x10]
    // 0x1393df4: LoadField: r2 = r0->field_b
    //     0x1393df4: ldur            w2, [x0, #0xb]
    // 0x1393df8: DecompressPointer r2
    //     0x1393df8: add             x2, x2, HEAP, lsl #32
    // 0x1393dfc: stur            x2, [fp, #-0x20]
    // 0x1393e00: LoadField: r1 = r2->field_f
    //     0x1393e00: ldur            w1, [x2, #0xf]
    // 0x1393e04: DecompressPointer r1
    //     0x1393e04: add             x1, x1, HEAP, lsl #32
    // 0x1393e08: r0 = controller()
    //     0x1393e08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393e0c: mov             x2, x0
    // 0x1393e10: ldur            x0, [fp, #-0x20]
    // 0x1393e14: stur            x2, [fp, #-0x28]
    // 0x1393e18: LoadField: r1 = r0->field_f
    //     0x1393e18: ldur            w1, [x0, #0xf]
    // 0x1393e1c: DecompressPointer r1
    //     0x1393e1c: add             x1, x1, HEAP, lsl #32
    // 0x1393e20: r0 = controller()
    //     0x1393e20: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393e24: LoadField: r1 = r0->field_7b
    //     0x1393e24: ldur            w1, [x0, #0x7b]
    // 0x1393e28: DecompressPointer r1
    //     0x1393e28: add             x1, x1, HEAP, lsl #32
    // 0x1393e2c: r0 = value()
    //     0x1393e2c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1393e30: mov             x3, x0
    // 0x1393e34: ldur            x0, [fp, #-0x18]
    // 0x1393e38: r2 = Null
    //     0x1393e38: mov             x2, NULL
    // 0x1393e3c: r1 = Null
    //     0x1393e3c: mov             x1, NULL
    // 0x1393e40: stur            x3, [fp, #-0x30]
    // 0x1393e44: r4 = 60
    //     0x1393e44: movz            x4, #0x3c
    // 0x1393e48: branchIfSmi(r0, 0x1393e54)
    //     0x1393e48: tbz             w0, #0, #0x1393e54
    // 0x1393e4c: r4 = LoadClassIdInstr(r0)
    //     0x1393e4c: ldur            x4, [x0, #-1]
    //     0x1393e50: ubfx            x4, x4, #0xc, #0x14
    // 0x1393e54: sub             x4, x4, #0x5e
    // 0x1393e58: cmp             x4, #1
    // 0x1393e5c: b.ls            #0x1393e70
    // 0x1393e60: r8 = String
    //     0x1393e60: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x1393e64: r3 = Null
    //     0x1393e64: add             x3, PP, #0x41, lsl #12  ; [pp+0x413e0] Null
    //     0x1393e68: ldr             x3, [x3, #0x3e0]
    // 0x1393e6c: r0 = String()
    //     0x1393e6c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x1393e70: ldur            x1, [fp, #-0x28]
    // 0x1393e74: ldur            x2, [fp, #-0x30]
    // 0x1393e78: ldur            x3, [fp, #-0x18]
    // 0x1393e7c: r0 = verifyOtp()
    //     0x1393e7c: bl              #0x13927f4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::verifyOtp
    // 0x1393e80: mov             x1, x0
    // 0x1393e84: stur            x1, [fp, #-0x18]
    // 0x1393e88: r0 = Await()
    //     0x1393e88: bl              #0x63248c  ; AwaitStub
    // 0x1393e8c: r16 = true
    //     0x1393e8c: add             x16, NULL, #0x20  ; true
    // 0x1393e90: cmp             w0, w16
    // 0x1393e94: b.ne            #0x1393edc
    // 0x1393e98: ldur            x0, [fp, #-0x20]
    // 0x1393e9c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1393e9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1393ea0: ldr             x0, [x0, #0x1c80]
    //     0x1393ea4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1393ea8: cmp             w0, w16
    //     0x1393eac: b.ne            #0x1393eb8
    //     0x1393eb0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1393eb4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1393eb8: str             NULL, [SP]
    // 0x1393ebc: r4 = const [0x1, 0, 0, 0, null]
    //     0x1393ebc: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x1393ec0: r0 = GetNavigation.back()
    //     0x1393ec0: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1393ec4: ldur            x0, [fp, #-0x20]
    // 0x1393ec8: LoadField: r1 = r0->field_f
    //     0x1393ec8: ldur            w1, [x0, #0xf]
    // 0x1393ecc: DecompressPointer r1
    //     0x1393ecc: add             x1, x1, HEAP, lsl #32
    // 0x1393ed0: r0 = controller()
    //     0x1393ed0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393ed4: mov             x1, x0
    // 0x1393ed8: r0 = navigateToOrderSummaryPage()
    //     0x1393ed8: bl              #0x12e8d30  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::navigateToOrderSummaryPage
    // 0x1393edc: ldur            x0, [fp, #-0x20]
    // 0x1393ee0: LoadField: r1 = r0->field_f
    //     0x1393ee0: ldur            w1, [x0, #0xf]
    // 0x1393ee4: DecompressPointer r1
    //     0x1393ee4: add             x1, x1, HEAP, lsl #32
    // 0x1393ee8: r0 = controller()
    //     0x1393ee8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1393eec: r16 = "landing_page"
    //     0x1393eec: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x1393ef0: ldr             x16, [x16, #0x638]
    // 0x1393ef4: r30 = "request_number"
    //     0x1393ef4: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x1393ef8: ldr             lr, [lr, #0x900]
    // 0x1393efc: stp             lr, x16, [SP, #0x18]
    // 0x1393f00: r16 = "confirm_cta"
    //     0x1393f00: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3db80] "confirm_cta"
    //     0x1393f04: ldr             x16, [x16, #0xb80]
    // 0x1393f08: r30 = "Confirm"
    //     0x1393f08: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3cec0] "Confirm"
    //     0x1393f0c: ldr             lr, [lr, #0xec0]
    // 0x1393f10: stp             lr, x16, [SP, #8]
    // 0x1393f14: r16 = "otp_popup"
    //     0x1393f14: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c818] "otp_popup"
    //     0x1393f18: ldr             x16, [x16, #0x818]
    // 0x1393f1c: str             x16, [SP]
    // 0x1393f20: mov             x1, x0
    // 0x1393f24: r2 = "checkout_cta_clicked"
    //     0x1393f24: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x1393f28: ldr             x2, [x2, #0x7b0]
    // 0x1393f2c: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, pageId, 0x3, pageType, 0x2, widgetType, 0x6, null]
    //     0x1393f2c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d910] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "pageId", 0x3, "pageType", 0x2, "widgetType", 0x6, Null]
    //     0x1393f30: ldr             x4, [x4, #0x910]
    // 0x1393f34: r0 = checkoutPostEvent()
    //     0x1393f34: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x1393f38: r0 = Null
    //     0x1393f38: mov             x0, NULL
    // 0x1393f3c: r0 = ReturnAsyncNotFuture()
    //     0x1393f3c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1393f40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1393f40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1393f44: b               #0x1393de8
  }
  _ body(/* No info */) {
    // ** addr: 0x14d7a24, size: 0x118
    // 0x14d7a24: EnterFrame
    //     0x14d7a24: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7a28: mov             fp, SP
    // 0x14d7a2c: AllocStack(0x18)
    //     0x14d7a2c: sub             SP, SP, #0x18
    // 0x14d7a30: SetupParameters(CheckoutRequestNumberPage this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x14d7a30: stur            x1, [fp, #-8]
    //     0x14d7a34: mov             x16, x2
    //     0x14d7a38: mov             x2, x1
    //     0x14d7a3c: mov             x1, x16
    //     0x14d7a40: stur            x1, [fp, #-0x10]
    // 0x14d7a44: CheckStackOverflow
    //     0x14d7a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7a48: cmp             SP, x16
    //     0x14d7a4c: b.ls            #0x14d7b34
    // 0x14d7a50: r1 = 3
    //     0x14d7a50: movz            x1, #0x3
    // 0x14d7a54: r0 = AllocateContext()
    //     0x14d7a54: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d7a58: ldur            x2, [fp, #-8]
    // 0x14d7a5c: stur            x0, [fp, #-0x18]
    // 0x14d7a60: StoreField: r0->field_f = r2
    //     0x14d7a60: stur            w2, [x0, #0xf]
    // 0x14d7a64: ldur            x1, [fp, #-0x10]
    // 0x14d7a68: StoreField: r0->field_13 = r1
    //     0x14d7a68: stur            w1, [x0, #0x13]
    // 0x14d7a6c: r0 = of()
    //     0x14d7a6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d7a70: LoadField: r1 = r0->field_87
    //     0x14d7a70: ldur            w1, [x0, #0x87]
    // 0x14d7a74: DecompressPointer r1
    //     0x14d7a74: add             x1, x1, HEAP, lsl #32
    // 0x14d7a78: mov             x0, x1
    // 0x14d7a7c: ldur            x2, [fp, #-0x18]
    // 0x14d7a80: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d7a80: stur            w0, [x2, #0x17]
    //     0x14d7a84: ldurb           w16, [x2, #-1]
    //     0x14d7a88: ldurb           w17, [x0, #-1]
    //     0x14d7a8c: and             x16, x17, x16, lsr #2
    //     0x14d7a90: tst             x16, HEAP, lsr #32
    //     0x14d7a94: b.eq            #0x14d7a9c
    //     0x14d7a98: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x14d7a9c: r0 = Obx()
    //     0x14d7a9c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d7aa0: ldur            x2, [fp, #-0x18]
    // 0x14d7aa4: r1 = Function '<anonymous closure>':.
    //     0x14d7aa4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41348] AnonymousClosure: (0x1393564), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x14d7aa8: ldr             x1, [x1, #0x348]
    // 0x14d7aac: stur            x0, [fp, #-0x10]
    // 0x14d7ab0: r0 = AllocateClosure()
    //     0x14d7ab0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7ab4: mov             x1, x0
    // 0x14d7ab8: ldur            x0, [fp, #-0x10]
    // 0x14d7abc: StoreField: r0->field_b = r1
    //     0x14d7abc: stur            w1, [x0, #0xb]
    // 0x14d7ac0: r0 = SafeArea()
    //     0x14d7ac0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14d7ac4: mov             x1, x0
    // 0x14d7ac8: r0 = true
    //     0x14d7ac8: add             x0, NULL, #0x20  ; true
    // 0x14d7acc: stur            x1, [fp, #-0x18]
    // 0x14d7ad0: StoreField: r1->field_b = r0
    //     0x14d7ad0: stur            w0, [x1, #0xb]
    // 0x14d7ad4: StoreField: r1->field_f = r0
    //     0x14d7ad4: stur            w0, [x1, #0xf]
    // 0x14d7ad8: StoreField: r1->field_13 = r0
    //     0x14d7ad8: stur            w0, [x1, #0x13]
    // 0x14d7adc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d7adc: stur            w0, [x1, #0x17]
    // 0x14d7ae0: r0 = Instance_EdgeInsets
    //     0x14d7ae0: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14d7ae4: StoreField: r1->field_1b = r0
    //     0x14d7ae4: stur            w0, [x1, #0x1b]
    // 0x14d7ae8: r0 = false
    //     0x14d7ae8: add             x0, NULL, #0x30  ; false
    // 0x14d7aec: StoreField: r1->field_1f = r0
    //     0x14d7aec: stur            w0, [x1, #0x1f]
    // 0x14d7af0: ldur            x0, [fp, #-0x10]
    // 0x14d7af4: StoreField: r1->field_23 = r0
    //     0x14d7af4: stur            w0, [x1, #0x23]
    // 0x14d7af8: r0 = WillPopScope()
    //     0x14d7af8: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d7afc: mov             x3, x0
    // 0x14d7b00: ldur            x0, [fp, #-0x18]
    // 0x14d7b04: stur            x3, [fp, #-0x10]
    // 0x14d7b08: StoreField: r3->field_b = r0
    //     0x14d7b08: stur            w0, [x3, #0xb]
    // 0x14d7b0c: ldur            x2, [fp, #-8]
    // 0x14d7b10: r1 = Function 'getBack':.
    //     0x14d7b10: add             x1, PP, #0x41, lsl #12  ; [pp+0x41350] AnonymousClosure: (0x14d7b3c), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack (0x14d7b74)
    //     0x14d7b14: ldr             x1, [x1, #0x350]
    // 0x14d7b18: r0 = AllocateClosure()
    //     0x14d7b18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7b1c: mov             x1, x0
    // 0x14d7b20: ldur            x0, [fp, #-0x10]
    // 0x14d7b24: StoreField: r0->field_f = r1
    //     0x14d7b24: stur            w1, [x0, #0xf]
    // 0x14d7b28: LeaveFrame
    //     0x14d7b28: mov             SP, fp
    //     0x14d7b2c: ldp             fp, lr, [SP], #0x10
    // 0x14d7b30: ret
    //     0x14d7b30: ret             
    // 0x14d7b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7b34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7b38: b               #0x14d7a50
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x14d7b3c, size: 0x38
    // 0x14d7b3c: EnterFrame
    //     0x14d7b3c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7b40: mov             fp, SP
    // 0x14d7b44: ldr             x0, [fp, #0x10]
    // 0x14d7b48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d7b48: ldur            w1, [x0, #0x17]
    // 0x14d7b4c: DecompressPointer r1
    //     0x14d7b4c: add             x1, x1, HEAP, lsl #32
    // 0x14d7b50: CheckStackOverflow
    //     0x14d7b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7b54: cmp             SP, x16
    //     0x14d7b58: b.ls            #0x14d7b6c
    // 0x14d7b5c: r0 = getBack()
    //     0x14d7b5c: bl              #0x14d7b74  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack
    // 0x14d7b60: LeaveFrame
    //     0x14d7b60: mov             SP, fp
    //     0x14d7b64: ldp             fp, lr, [SP], #0x10
    // 0x14d7b68: ret
    //     0x14d7b68: ret             
    // 0x14d7b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7b6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7b70: b               #0x14d7b5c
  }
  _ getBack(/* No info */) {
    // ** addr: 0x14d7b74, size: 0xb0
    // 0x14d7b74: EnterFrame
    //     0x14d7b74: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7b78: mov             fp, SP
    // 0x14d7b7c: AllocStack(0x8)
    //     0x14d7b7c: sub             SP, SP, #8
    // 0x14d7b80: CheckStackOverflow
    //     0x14d7b80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7b84: cmp             SP, x16
    //     0x14d7b88: b.ls            #0x14d7c1c
    // 0x14d7b8c: r2 = false
    //     0x14d7b8c: add             x2, NULL, #0x30  ; false
    // 0x14d7b90: r0 = showLoading()
    //     0x14d7b90: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x14d7b94: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14d7b94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d7b98: ldr             x0, [x0, #0x1c80]
    //     0x14d7b9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d7ba0: cmp             w0, w16
    //     0x14d7ba4: b.ne            #0x14d7bb0
    //     0x14d7ba8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14d7bac: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14d7bb0: r1 = Function '<anonymous closure>':.
    //     0x14d7bb0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41358] AnonymousClosure: (0x1390d2c), in [package:customer_app/app/presentation/views/line/checkout_variants/checkout_variants_view.dart] CheckoutVariantsView::getBack (0x1390ea0)
    //     0x14d7bb4: ldr             x1, [x1, #0x358]
    // 0x14d7bb8: r2 = Null
    //     0x14d7bb8: mov             x2, NULL
    // 0x14d7bbc: r0 = AllocateClosure()
    //     0x14d7bbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7bc0: mov             x1, x0
    // 0x14d7bc4: r0 = GetNavigation.until()
    //     0x14d7bc4: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x14d7bc8: r1 = <bool>
    //     0x14d7bc8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x14d7bcc: r0 = _Future()
    //     0x14d7bcc: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x14d7bd0: stur            x0, [fp, #-8]
    // 0x14d7bd4: StoreField: r0->field_b = rZR
    //     0x14d7bd4: stur            xzr, [x0, #0xb]
    // 0x14d7bd8: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x14d7bd8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d7bdc: ldr             x0, [x0, #0x778]
    //     0x14d7be0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d7be4: cmp             w0, w16
    //     0x14d7be8: b.ne            #0x14d7bf4
    //     0x14d7bec: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x14d7bf0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x14d7bf4: mov             x1, x0
    // 0x14d7bf8: ldur            x0, [fp, #-8]
    // 0x14d7bfc: StoreField: r0->field_13 = r1
    //     0x14d7bfc: stur            w1, [x0, #0x13]
    // 0x14d7c00: mov             x1, x0
    // 0x14d7c04: r2 = false
    //     0x14d7c04: add             x2, NULL, #0x30  ; false
    // 0x14d7c08: r0 = _asyncComplete()
    //     0x14d7c08: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x14d7c0c: ldur            x0, [fp, #-8]
    // 0x14d7c10: LeaveFrame
    //     0x14d7c10: mov             SP, fp
    //     0x14d7c14: ldp             fp, lr, [SP], #0x10
    // 0x14d7c18: ret
    //     0x14d7c18: ret             
    // 0x14d7c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7c1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7c20: b               #0x14d7b8c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e060c, size: 0x250
    // 0x15e060c: EnterFrame
    //     0x15e060c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e0610: mov             fp, SP
    // 0x15e0614: AllocStack(0x28)
    //     0x15e0614: sub             SP, SP, #0x28
    // 0x15e0618: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e0618: stur            x1, [fp, #-8]
    //     0x15e061c: stur            x2, [fp, #-0x10]
    // 0x15e0620: CheckStackOverflow
    //     0x15e0620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e0624: cmp             SP, x16
    //     0x15e0628: b.ls            #0x15e0854
    // 0x15e062c: r1 = 2
    //     0x15e062c: movz            x1, #0x2
    // 0x15e0630: r0 = AllocateContext()
    //     0x15e0630: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e0634: ldur            x1, [fp, #-8]
    // 0x15e0638: stur            x0, [fp, #-0x18]
    // 0x15e063c: StoreField: r0->field_f = r1
    //     0x15e063c: stur            w1, [x0, #0xf]
    // 0x15e0640: ldur            x2, [fp, #-0x10]
    // 0x15e0644: StoreField: r0->field_13 = r2
    //     0x15e0644: stur            w2, [x0, #0x13]
    // 0x15e0648: r0 = Obx()
    //     0x15e0648: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e064c: ldur            x2, [fp, #-0x18]
    // 0x15e0650: r1 = Function '<anonymous closure>':.
    //     0x15e0650: add             x1, PP, #0x41, lsl #12  ; [pp+0x413f0] AnonymousClosure: (0x15ccba8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15e0654: ldr             x1, [x1, #0x3f0]
    // 0x15e0658: stur            x0, [fp, #-0x10]
    // 0x15e065c: r0 = AllocateClosure()
    //     0x15e065c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e0660: mov             x1, x0
    // 0x15e0664: ldur            x0, [fp, #-0x10]
    // 0x15e0668: StoreField: r0->field_b = r1
    //     0x15e0668: stur            w1, [x0, #0xb]
    // 0x15e066c: ldur            x1, [fp, #-8]
    // 0x15e0670: r0 = controller()
    //     0x15e0670: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e0674: LoadField: r1 = r0->field_73
    //     0x15e0674: ldur            w1, [x0, #0x73]
    // 0x15e0678: DecompressPointer r1
    //     0x15e0678: add             x1, x1, HEAP, lsl #32
    // 0x15e067c: r0 = value()
    //     0x15e067c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e0680: tbnz            w0, #4, #0x15e0718
    // 0x15e0684: ldur            x2, [fp, #-0x18]
    // 0x15e0688: LoadField: r1 = r2->field_13
    //     0x15e0688: ldur            w1, [x2, #0x13]
    // 0x15e068c: DecompressPointer r1
    //     0x15e068c: add             x1, x1, HEAP, lsl #32
    // 0x15e0690: r0 = of()
    //     0x15e0690: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e0694: LoadField: r1 = r0->field_5b
    //     0x15e0694: ldur            w1, [x0, #0x5b]
    // 0x15e0698: DecompressPointer r1
    //     0x15e0698: add             x1, x1, HEAP, lsl #32
    // 0x15e069c: stur            x1, [fp, #-8]
    // 0x15e06a0: r0 = ColorFilter()
    //     0x15e06a0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e06a4: mov             x1, x0
    // 0x15e06a8: ldur            x0, [fp, #-8]
    // 0x15e06ac: stur            x1, [fp, #-0x20]
    // 0x15e06b0: StoreField: r1->field_7 = r0
    //     0x15e06b0: stur            w0, [x1, #7]
    // 0x15e06b4: r0 = Instance_BlendMode
    //     0x15e06b4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e06b8: ldr             x0, [x0, #0xb30]
    // 0x15e06bc: StoreField: r1->field_b = r0
    //     0x15e06bc: stur            w0, [x1, #0xb]
    // 0x15e06c0: r2 = 1
    //     0x15e06c0: movz            x2, #0x1
    // 0x15e06c4: StoreField: r1->field_13 = r2
    //     0x15e06c4: stur            x2, [x1, #0x13]
    // 0x15e06c8: r0 = SvgPicture()
    //     0x15e06c8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e06cc: stur            x0, [fp, #-8]
    // 0x15e06d0: ldur            x16, [fp, #-0x20]
    // 0x15e06d4: str             x16, [SP]
    // 0x15e06d8: mov             x1, x0
    // 0x15e06dc: r2 = "assets/images/search.svg"
    //     0x15e06dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e06e0: ldr             x2, [x2, #0xa30]
    // 0x15e06e4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e06e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e06e8: ldr             x4, [x4, #0xa38]
    // 0x15e06ec: r0 = SvgPicture.asset()
    //     0x15e06ec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e06f0: r0 = Align()
    //     0x15e06f0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e06f4: r3 = Instance_Alignment
    //     0x15e06f4: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e06f8: ldr             x3, [x3, #0xb10]
    // 0x15e06fc: StoreField: r0->field_f = r3
    //     0x15e06fc: stur            w3, [x0, #0xf]
    // 0x15e0700: r4 = 1.000000
    //     0x15e0700: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e0704: StoreField: r0->field_13 = r4
    //     0x15e0704: stur            w4, [x0, #0x13]
    // 0x15e0708: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e0708: stur            w4, [x0, #0x17]
    // 0x15e070c: ldur            x1, [fp, #-8]
    // 0x15e0710: StoreField: r0->field_b = r1
    //     0x15e0710: stur            w1, [x0, #0xb]
    // 0x15e0714: b               #0x15e07c8
    // 0x15e0718: ldur            x5, [fp, #-0x18]
    // 0x15e071c: r4 = 1.000000
    //     0x15e071c: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e0720: r0 = Instance_BlendMode
    //     0x15e0720: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e0724: ldr             x0, [x0, #0xb30]
    // 0x15e0728: r3 = Instance_Alignment
    //     0x15e0728: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e072c: ldr             x3, [x3, #0xb10]
    // 0x15e0730: r2 = 1
    //     0x15e0730: movz            x2, #0x1
    // 0x15e0734: LoadField: r1 = r5->field_13
    //     0x15e0734: ldur            w1, [x5, #0x13]
    // 0x15e0738: DecompressPointer r1
    //     0x15e0738: add             x1, x1, HEAP, lsl #32
    // 0x15e073c: r0 = of()
    //     0x15e073c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e0740: LoadField: r1 = r0->field_5b
    //     0x15e0740: ldur            w1, [x0, #0x5b]
    // 0x15e0744: DecompressPointer r1
    //     0x15e0744: add             x1, x1, HEAP, lsl #32
    // 0x15e0748: stur            x1, [fp, #-8]
    // 0x15e074c: r0 = ColorFilter()
    //     0x15e074c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e0750: mov             x1, x0
    // 0x15e0754: ldur            x0, [fp, #-8]
    // 0x15e0758: stur            x1, [fp, #-0x20]
    // 0x15e075c: StoreField: r1->field_7 = r0
    //     0x15e075c: stur            w0, [x1, #7]
    // 0x15e0760: r0 = Instance_BlendMode
    //     0x15e0760: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e0764: ldr             x0, [x0, #0xb30]
    // 0x15e0768: StoreField: r1->field_b = r0
    //     0x15e0768: stur            w0, [x1, #0xb]
    // 0x15e076c: r0 = 1
    //     0x15e076c: movz            x0, #0x1
    // 0x15e0770: StoreField: r1->field_13 = r0
    //     0x15e0770: stur            x0, [x1, #0x13]
    // 0x15e0774: r0 = SvgPicture()
    //     0x15e0774: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e0778: stur            x0, [fp, #-8]
    // 0x15e077c: ldur            x16, [fp, #-0x20]
    // 0x15e0780: str             x16, [SP]
    // 0x15e0784: mov             x1, x0
    // 0x15e0788: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e0788: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e078c: ldr             x2, [x2, #0xa40]
    // 0x15e0790: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e0790: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e0794: ldr             x4, [x4, #0xa38]
    // 0x15e0798: r0 = SvgPicture.asset()
    //     0x15e0798: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e079c: r0 = Align()
    //     0x15e079c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e07a0: mov             x1, x0
    // 0x15e07a4: r0 = Instance_Alignment
    //     0x15e07a4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e07a8: ldr             x0, [x0, #0xb10]
    // 0x15e07ac: StoreField: r1->field_f = r0
    //     0x15e07ac: stur            w0, [x1, #0xf]
    // 0x15e07b0: r0 = 1.000000
    //     0x15e07b0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e07b4: StoreField: r1->field_13 = r0
    //     0x15e07b4: stur            w0, [x1, #0x13]
    // 0x15e07b8: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e07b8: stur            w0, [x1, #0x17]
    // 0x15e07bc: ldur            x0, [fp, #-8]
    // 0x15e07c0: StoreField: r1->field_b = r0
    //     0x15e07c0: stur            w0, [x1, #0xb]
    // 0x15e07c4: mov             x0, x1
    // 0x15e07c8: stur            x0, [fp, #-8]
    // 0x15e07cc: r0 = InkWell()
    //     0x15e07cc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e07d0: mov             x3, x0
    // 0x15e07d4: ldur            x0, [fp, #-8]
    // 0x15e07d8: stur            x3, [fp, #-0x20]
    // 0x15e07dc: StoreField: r3->field_b = r0
    //     0x15e07dc: stur            w0, [x3, #0xb]
    // 0x15e07e0: ldur            x2, [fp, #-0x18]
    // 0x15e07e4: r1 = Function '<anonymous closure>':.
    //     0x15e07e4: add             x1, PP, #0x41, lsl #12  ; [pp+0x413f8] AnonymousClosure: (0x15e085c), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e060c)
    //     0x15e07e8: ldr             x1, [x1, #0x3f8]
    // 0x15e07ec: r0 = AllocateClosure()
    //     0x15e07ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e07f0: ldur            x2, [fp, #-0x20]
    // 0x15e07f4: StoreField: r2->field_f = r0
    //     0x15e07f4: stur            w0, [x2, #0xf]
    // 0x15e07f8: r0 = true
    //     0x15e07f8: add             x0, NULL, #0x20  ; true
    // 0x15e07fc: StoreField: r2->field_43 = r0
    //     0x15e07fc: stur            w0, [x2, #0x43]
    // 0x15e0800: r1 = Instance_BoxShape
    //     0x15e0800: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e0804: ldr             x1, [x1, #0x80]
    // 0x15e0808: StoreField: r2->field_47 = r1
    //     0x15e0808: stur            w1, [x2, #0x47]
    // 0x15e080c: StoreField: r2->field_6f = r0
    //     0x15e080c: stur            w0, [x2, #0x6f]
    // 0x15e0810: r1 = false
    //     0x15e0810: add             x1, NULL, #0x30  ; false
    // 0x15e0814: StoreField: r2->field_73 = r1
    //     0x15e0814: stur            w1, [x2, #0x73]
    // 0x15e0818: StoreField: r2->field_83 = r0
    //     0x15e0818: stur            w0, [x2, #0x83]
    // 0x15e081c: StoreField: r2->field_7b = r1
    //     0x15e081c: stur            w1, [x2, #0x7b]
    // 0x15e0820: r0 = AppBar()
    //     0x15e0820: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e0824: stur            x0, [fp, #-8]
    // 0x15e0828: ldur            x16, [fp, #-0x10]
    // 0x15e082c: str             x16, [SP]
    // 0x15e0830: mov             x1, x0
    // 0x15e0834: ldur            x2, [fp, #-0x20]
    // 0x15e0838: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e0838: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e083c: ldr             x4, [x4, #0xf00]
    // 0x15e0840: r0 = AppBar()
    //     0x15e0840: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e0844: ldur            x0, [fp, #-8]
    // 0x15e0848: LeaveFrame
    //     0x15e0848: mov             SP, fp
    //     0x15e084c: ldp             fp, lr, [SP], #0x10
    // 0x15e0850: ret
    //     0x15e0850: ret             
    // 0x15e0854: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e0854: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e0858: b               #0x15e062c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e085c, size: 0xc8
    // 0x15e085c: EnterFrame
    //     0x15e085c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e0860: mov             fp, SP
    // 0x15e0864: AllocStack(0x18)
    //     0x15e0864: sub             SP, SP, #0x18
    // 0x15e0868: SetupParameters()
    //     0x15e0868: ldr             x0, [fp, #0x10]
    //     0x15e086c: ldur            w3, [x0, #0x17]
    //     0x15e0870: add             x3, x3, HEAP, lsl #32
    //     0x15e0874: stur            x3, [fp, #-8]
    // 0x15e0878: CheckStackOverflow
    //     0x15e0878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e087c: cmp             SP, x16
    //     0x15e0880: b.ls            #0x15e091c
    // 0x15e0884: LoadField: r1 = r3->field_f
    //     0x15e0884: ldur            w1, [x3, #0xf]
    // 0x15e0888: DecompressPointer r1
    //     0x15e0888: add             x1, x1, HEAP, lsl #32
    // 0x15e088c: r2 = false
    //     0x15e088c: add             x2, NULL, #0x30  ; false
    // 0x15e0890: r0 = showLoading()
    //     0x15e0890: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e0894: ldur            x0, [fp, #-8]
    // 0x15e0898: LoadField: r1 = r0->field_f
    //     0x15e0898: ldur            w1, [x0, #0xf]
    // 0x15e089c: DecompressPointer r1
    //     0x15e089c: add             x1, x1, HEAP, lsl #32
    // 0x15e08a0: r0 = controller()
    //     0x15e08a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e08a4: LoadField: r1 = r0->field_73
    //     0x15e08a4: ldur            w1, [x0, #0x73]
    // 0x15e08a8: DecompressPointer r1
    //     0x15e08a8: add             x1, x1, HEAP, lsl #32
    // 0x15e08ac: r0 = value()
    //     0x15e08ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e08b0: tbnz            w0, #4, #0x15e08e8
    // 0x15e08b4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e08b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e08b8: ldr             x0, [x0, #0x1c80]
    //     0x15e08bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e08c0: cmp             w0, w16
    //     0x15e08c4: b.ne            #0x15e08d0
    //     0x15e08c8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e08cc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e08d0: r16 = "/search"
    //     0x15e08d0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e08d4: ldr             x16, [x16, #0x838]
    // 0x15e08d8: stp             x16, NULL, [SP]
    // 0x15e08dc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e08dc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e08e0: r0 = GetNavigation.toNamed()
    //     0x15e08e0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e08e4: b               #0x15e090c
    // 0x15e08e8: ldur            x0, [fp, #-8]
    // 0x15e08ec: LoadField: r1 = r0->field_f
    //     0x15e08ec: ldur            w1, [x0, #0xf]
    // 0x15e08f0: DecompressPointer r1
    //     0x15e08f0: add             x1, x1, HEAP, lsl #32
    // 0x15e08f4: r2 = false
    //     0x15e08f4: add             x2, NULL, #0x30  ; false
    // 0x15e08f8: r0 = showLoading()
    //     0x15e08f8: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e08fc: ldur            x0, [fp, #-8]
    // 0x15e0900: LoadField: r1 = r0->field_f
    //     0x15e0900: ldur            w1, [x0, #0xf]
    // 0x15e0904: DecompressPointer r1
    //     0x15e0904: add             x1, x1, HEAP, lsl #32
    // 0x15e0908: r0 = getBack()
    //     0x15e0908: bl              #0x14d7b74  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack
    // 0x15e090c: r0 = Null
    //     0x15e090c: mov             x0, NULL
    // 0x15e0910: LeaveFrame
    //     0x15e0910: mov             SP, fp
    //     0x15e0914: ldp             fp, lr, [SP], #0x10
    // 0x15e0918: ret
    //     0x15e0918: ret             
    // 0x15e091c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e091c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e0920: b               #0x15e0884
  }
}
