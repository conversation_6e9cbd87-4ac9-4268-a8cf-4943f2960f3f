// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_video_media_carousel.dart

// class id: 1049441, size: 0x8
class :: {
}

// class id: 3309, size: 0x20, field offset: 0x14
class _ProductVideoMediaCarouselState extends State<dynamic> {

  late Future<void> _future; // offset: 0x1c
  late VideoPlayerController _controller; // offset: 0x14
  late ChewieController? _chewieController; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x9445d0, size: 0xf8
    // 0x9445d0: EnterFrame
    //     0x9445d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9445d4: mov             fp, SP
    // 0x9445d8: AllocStack(0x10)
    //     0x9445d8: sub             SP, SP, #0x10
    // 0x9445dc: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r0, fp-0x8 */)
    //     0x9445dc: mov             x0, x1
    //     0x9445e0: stur            x1, [fp, #-8]
    // 0x9445e4: CheckStackOverflow
    //     0x9445e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9445e8: cmp             SP, x16
    //     0x9445ec: b.ls            #0x9446bc
    // 0x9445f0: LoadField: r1 = r0->field_b
    //     0x9445f0: ldur            w1, [x0, #0xb]
    // 0x9445f4: DecompressPointer r1
    //     0x9445f4: add             x1, x1, HEAP, lsl #32
    // 0x9445f8: cmp             w1, NULL
    // 0x9445fc: b.eq            #0x9446c4
    // 0x944600: LoadField: r2 = r1->field_b
    //     0x944600: ldur            w2, [x1, #0xb]
    // 0x944604: DecompressPointer r2
    //     0x944604: add             x2, x2, HEAP, lsl #32
    // 0x944608: LoadField: r1 = r2->field_2f
    //     0x944608: ldur            w1, [x2, #0x2f]
    // 0x94460c: DecompressPointer r1
    //     0x94460c: add             x1, x1, HEAP, lsl #32
    // 0x944610: cmp             w1, NULL
    // 0x944614: b.ne            #0x944620
    // 0x944618: r1 = Null
    //     0x944618: mov             x1, NULL
    // 0x94461c: b               #0x94462c
    // 0x944620: LoadField: r2 = r1->field_b
    //     0x944620: ldur            w2, [x1, #0xb]
    // 0x944624: DecompressPointer r2
    //     0x944624: add             x2, x2, HEAP, lsl #32
    // 0x944628: mov             x1, x2
    // 0x94462c: cmp             w1, NULL
    // 0x944630: b.ne            #0x944638
    // 0x944634: r1 = ""
    //     0x944634: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x944638: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x944638: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x94463c: r0 = parse()
    //     0x94463c: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0x944640: r1 = <VideoPlayerValue>
    //     0x944640: add             x1, PP, #0x52, lsl #12  ; [pp+0x52f90] TypeArguments: <VideoPlayerValue>
    //     0x944644: ldr             x1, [x1, #0xf90]
    // 0x944648: stur            x0, [fp, #-0x10]
    // 0x94464c: r0 = VideoPlayerController()
    //     0x94464c: bl              #0x8f9c94  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x68)
    // 0x944650: mov             x1, x0
    // 0x944654: ldur            x2, [fp, #-0x10]
    // 0x944658: stur            x0, [fp, #-0x10]
    // 0x94465c: r0 = VideoPlayerController.networkUrl()
    //     0x94465c: bl              #0x8f9ba4  ; [package:video_player/video_player.dart] VideoPlayerController::VideoPlayerController.networkUrl
    // 0x944660: ldur            x0, [fp, #-0x10]
    // 0x944664: ldur            x2, [fp, #-8]
    // 0x944668: StoreField: r2->field_13 = r0
    //     0x944668: stur            w0, [x2, #0x13]
    //     0x94466c: ldurb           w16, [x2, #-1]
    //     0x944670: ldurb           w17, [x0, #-1]
    //     0x944674: and             x16, x17, x16, lsr #2
    //     0x944678: tst             x16, HEAP, lsr #32
    //     0x94467c: b.eq            #0x944684
    //     0x944680: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x944684: mov             x1, x2
    // 0x944688: r0 = initVideoPlayer()
    //     0x944688: bl              #0x9446e8  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer
    // 0x94468c: ldur            x1, [fp, #-8]
    // 0x944690: StoreField: r1->field_1b = r0
    //     0x944690: stur            w0, [x1, #0x1b]
    //     0x944694: ldurb           w16, [x1, #-1]
    //     0x944698: ldurb           w17, [x0, #-1]
    //     0x94469c: and             x16, x17, x16, lsr #2
    //     0x9446a0: tst             x16, HEAP, lsr #32
    //     0x9446a4: b.eq            #0x9446ac
    //     0x9446a8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9446ac: r0 = Null
    //     0x9446ac: mov             x0, NULL
    // 0x9446b0: LeaveFrame
    //     0x9446b0: mov             SP, fp
    //     0x9446b4: ldp             fp, lr, [SP], #0x10
    // 0x9446b8: ret
    //     0x9446b8: ret             
    // 0x9446bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9446bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9446c0: b               #0x9445f0
    // 0x9446c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9446c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initVideoPlayer(/* No info */) async {
    // ** addr: 0x9446e8, size: 0xa0
    // 0x9446e8: EnterFrame
    //     0x9446e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9446ec: mov             fp, SP
    // 0x9446f0: AllocStack(0x20)
    //     0x9446f0: sub             SP, SP, #0x20
    // 0x9446f4: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r1, fp-0x10 */)
    //     0x9446f4: stur            NULL, [fp, #-8]
    //     0x9446f8: stur            x1, [fp, #-0x10]
    // 0x9446fc: CheckStackOverflow
    //     0x9446fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944700: cmp             SP, x16
    //     0x944704: b.ls            #0x944774
    // 0x944708: r1 = 1
    //     0x944708: movz            x1, #0x1
    // 0x94470c: r0 = AllocateContext()
    //     0x94470c: bl              #0x16f6108  ; AllocateContextStub
    // 0x944710: mov             x2, x0
    // 0x944714: ldur            x1, [fp, #-0x10]
    // 0x944718: stur            x2, [fp, #-0x18]
    // 0x94471c: StoreField: r2->field_f = r1
    //     0x94471c: stur            w1, [x2, #0xf]
    // 0x944720: InitAsync() -> Future<void?>
    //     0x944720: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x944724: bl              #0x6326e0  ; InitAsyncStub
    // 0x944728: ldur            x0, [fp, #-0x10]
    // 0x94472c: LoadField: r1 = r0->field_13
    //     0x94472c: ldur            w1, [x0, #0x13]
    // 0x944730: DecompressPointer r1
    //     0x944730: add             x1, x1, HEAP, lsl #32
    // 0x944734: r16 = Sentinel
    //     0x944734: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x944738: cmp             w1, w16
    // 0x94473c: b.eq            #0x94477c
    // 0x944740: r0 = initialize()
    //     0x944740: bl              #0x8f78ec  ; [package:video_player/video_player.dart] VideoPlayerController::initialize
    // 0x944744: mov             x1, x0
    // 0x944748: stur            x1, [fp, #-0x20]
    // 0x94474c: r0 = Await()
    //     0x94474c: bl              #0x63248c  ; AwaitStub
    // 0x944750: ldur            x2, [fp, #-0x18]
    // 0x944754: r1 = Function '<anonymous closure>':.
    //     0x944754: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a7b8] AnonymousClosure: (0x944788), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer (0x9446e8)
    //     0x944758: ldr             x1, [x1, #0x7b8]
    // 0x94475c: r0 = AllocateClosure()
    //     0x94475c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944760: ldur            x1, [fp, #-0x10]
    // 0x944764: mov             x2, x0
    // 0x944768: r0 = setState()
    //     0x944768: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x94476c: r0 = Null
    //     0x94476c: mov             x0, NULL
    // 0x944770: r0 = ReturnAsyncNotFuture()
    //     0x944770: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x944774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944774: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944778: b               #0x944708
    // 0x94477c: r9 = _controller
    //     0x94477c: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7a8] Field <_ProductVideoMediaCarouselState@1631471210._controller@1631471210>: late (offset: 0x14)
    //     0x944780: ldr             x9, [x9, #0x7a8]
    // 0x944784: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x944784: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x944788, size: 0x168
    // 0x944788: EnterFrame
    //     0x944788: stp             fp, lr, [SP, #-0x10]!
    //     0x94478c: mov             fp, SP
    // 0x944790: AllocStack(0x58)
    //     0x944790: sub             SP, SP, #0x58
    // 0x944794: SetupParameters()
    //     0x944794: ldr             x0, [fp, #0x10]
    //     0x944798: ldur            w2, [x0, #0x17]
    //     0x94479c: add             x2, x2, HEAP, lsl #32
    //     0x9447a0: stur            x2, [fp, #-0x18]
    // 0x9447a4: CheckStackOverflow
    //     0x9447a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9447a8: cmp             SP, x16
    //     0x9447ac: b.ls            #0x9448c0
    // 0x9447b0: LoadField: r0 = r2->field_f
    //     0x9447b0: ldur            w0, [x2, #0xf]
    // 0x9447b4: DecompressPointer r0
    //     0x9447b4: add             x0, x0, HEAP, lsl #32
    // 0x9447b8: stur            x0, [fp, #-0x10]
    // 0x9447bc: LoadField: r5 = r0->field_13
    //     0x9447bc: ldur            w5, [x0, #0x13]
    // 0x9447c0: DecompressPointer r5
    //     0x9447c0: add             x5, x5, HEAP, lsl #32
    // 0x9447c4: r16 = Sentinel
    //     0x9447c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9447c8: cmp             w5, w16
    // 0x9447cc: b.eq            #0x9448c8
    // 0x9447d0: stur            x5, [fp, #-8]
    // 0x9447d4: LoadField: r1 = r5->field_27
    //     0x9447d4: ldur            w1, [x5, #0x27]
    // 0x9447d8: DecompressPointer r1
    //     0x9447d8: add             x1, x1, HEAP, lsl #32
    // 0x9447dc: r0 = aspectRatio()
    //     0x9447dc: bl              #0x8faac4  ; [package:video_player/video_player.dart] VideoPlayerValue::aspectRatio
    // 0x9447e0: r0 = inline_Allocate_Double()
    //     0x9447e0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x9447e4: add             x0, x0, #0x10
    //     0x9447e8: cmp             x1, x0
    //     0x9447ec: b.ls            #0x9448d4
    //     0x9447f0: str             x0, [THR, #0x50]  ; THR::top
    //     0x9447f4: sub             x0, x0, #0xf
    //     0x9447f8: movz            x1, #0xe15c
    //     0x9447fc: movk            x1, #0x3, lsl #16
    //     0x944800: stur            x1, [x0, #-1]
    // 0x944804: StoreField: r0->field_7 = d0
    //     0x944804: stur            d0, [x0, #7]
    // 0x944808: stur            x0, [fp, #-0x20]
    // 0x94480c: r1 = Function '<anonymous closure>':.
    //     0x94480c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a7c0] AnonymousClosure: (0x934ed0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::initVideoPlayer (0x9350dc)
    //     0x944810: ldr             x1, [x1, #0x7c0]
    // 0x944814: r2 = Null
    //     0x944814: mov             x2, NULL
    // 0x944818: r0 = AllocateClosure()
    //     0x944818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94481c: stur            x0, [fp, #-0x28]
    // 0x944820: r0 = ChewieController()
    //     0x944820: bl              #0x8faaa0  ; AllocateChewieControllerStub -> ChewieController (size=0xdc)
    // 0x944824: stur            x0, [fp, #-0x30]
    // 0x944828: ldur            x16, [fp, #-0x20]
    // 0x94482c: r30 = true
    //     0x94482c: add             lr, NULL, #0x20  ; true
    // 0x944830: stp             lr, x16, [SP, #0x18]
    // 0x944834: r16 = true
    //     0x944834: add             x16, NULL, #0x20  ; true
    // 0x944838: r30 = false
    //     0x944838: add             lr, NULL, #0x30  ; false
    // 0x94483c: stp             lr, x16, [SP, #8]
    // 0x944840: ldur            x16, [fp, #-0x28]
    // 0x944844: str             x16, [SP]
    // 0x944848: mov             x1, x0
    // 0x94484c: ldur            x5, [fp, #-8]
    // 0x944850: r2 = true
    //     0x944850: add             x2, NULL, #0x20  ; true
    // 0x944854: r3 = true
    //     0x944854: add             x3, NULL, #0x20  ; true
    // 0x944858: r4 = const [0, 0x9, 0x5, 0x4, allowFullScreen, 0x7, aspectRatio, 0x4, autoInitialize, 0x5, errorBuilder, 0x8, showOptions, 0x6, null]
    //     0x944858: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a1c0] List(15) [0, 0x9, 0x5, 0x4, "allowFullScreen", 0x7, "aspectRatio", 0x4, "autoInitialize", 0x5, "errorBuilder", 0x8, "showOptions", 0x6, Null]
    //     0x94485c: ldr             x4, [x4, #0x1c0]
    // 0x944860: r0 = ChewieController()
    //     0x944860: bl              #0x8fa550  ; [package:chewie/src/chewie_player.dart] ChewieController::ChewieController
    // 0x944864: ldur            x0, [fp, #-0x30]
    // 0x944868: ldur            x1, [fp, #-0x10]
    // 0x94486c: ArrayStore: r1[0] = r0  ; List_4
    //     0x94486c: stur            w0, [x1, #0x17]
    //     0x944870: ldurb           w16, [x1, #-1]
    //     0x944874: ldurb           w17, [x0, #-1]
    //     0x944878: and             x16, x17, x16, lsr #2
    //     0x94487c: tst             x16, HEAP, lsr #32
    //     0x944880: b.eq            #0x944888
    //     0x944884: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x944888: ldur            x0, [fp, #-0x18]
    // 0x94488c: LoadField: r1 = r0->field_f
    //     0x94488c: ldur            w1, [x0, #0xf]
    // 0x944890: DecompressPointer r1
    //     0x944890: add             x1, x1, HEAP, lsl #32
    // 0x944894: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x944894: ldur            w0, [x1, #0x17]
    // 0x944898: DecompressPointer r0
    //     0x944898: add             x0, x0, HEAP, lsl #32
    // 0x94489c: r16 = Sentinel
    //     0x94489c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9448a0: cmp             w0, w16
    // 0x9448a4: b.eq            #0x9448e4
    // 0x9448a8: mov             x1, x0
    // 0x9448ac: r0 = setVolume()
    //     0x9448ac: bl              #0x8fa424  ; [package:chewie/src/chewie_player.dart] ChewieController::setVolume
    // 0x9448b0: r0 = Null
    //     0x9448b0: mov             x0, NULL
    // 0x9448b4: LeaveFrame
    //     0x9448b4: mov             SP, fp
    //     0x9448b8: ldp             fp, lr, [SP], #0x10
    // 0x9448bc: ret
    //     0x9448bc: ret             
    // 0x9448c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9448c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9448c4: b               #0x9447b0
    // 0x9448c8: r9 = _controller
    //     0x9448c8: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7a8] Field <_ProductVideoMediaCarouselState@1631471210._controller@1631471210>: late (offset: 0x14)
    //     0x9448cc: ldr             x9, [x9, #0x7a8]
    // 0x9448d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9448d0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9448d4: SaveReg d0
    //     0x9448d4: str             q0, [SP, #-0x10]!
    // 0x9448d8: r0 = AllocateDouble()
    //     0x9448d8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9448dc: RestoreReg d0
    //     0x9448dc: ldr             q0, [SP], #0x10
    // 0x9448e0: b               #0x944804
    // 0x9448e4: r9 = _chewieController
    //     0x9448e4: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7b0] Field <_ProductVideoMediaCarouselState@1631471210._chewieController@1631471210>: late (offset: 0x18)
    //     0x9448e8: ldr             x9, [x9, #0x7b0]
    // 0x9448ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9448ec: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb8a8dc, size: 0xa4
    // 0xb8a8dc: EnterFrame
    //     0xb8a8dc: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a8e0: mov             fp, SP
    // 0xb8a8e4: AllocStack(0x18)
    //     0xb8a8e4: sub             SP, SP, #0x18
    // 0xb8a8e8: SetupParameters(_ProductVideoMediaCarouselState this /* r1 => r1, fp-0x8 */)
    //     0xb8a8e8: stur            x1, [fp, #-8]
    // 0xb8a8ec: r1 = 1
    //     0xb8a8ec: movz            x1, #0x1
    // 0xb8a8f0: r0 = AllocateContext()
    //     0xb8a8f0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb8a8f4: mov             x2, x0
    // 0xb8a8f8: ldur            x0, [fp, #-8]
    // 0xb8a8fc: stur            x2, [fp, #-0x18]
    // 0xb8a900: StoreField: r2->field_f = r0
    //     0xb8a900: stur            w0, [x2, #0xf]
    // 0xb8a904: LoadField: r3 = r0->field_1b
    //     0xb8a904: ldur            w3, [x0, #0x1b]
    // 0xb8a908: DecompressPointer r3
    //     0xb8a908: add             x3, x3, HEAP, lsl #32
    // 0xb8a90c: r16 = Sentinel
    //     0xb8a90c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb8a910: cmp             w3, w16
    // 0xb8a914: b.eq            #0xb8a974
    // 0xb8a918: stur            x3, [fp, #-0x10]
    // 0xb8a91c: r1 = <void?>
    //     0xb8a91c: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0xb8a920: r0 = FutureBuilder()
    //     0xb8a920: bl              #0xa89a78  ; AllocateFutureBuilderStub -> FutureBuilder<X0> (size=0x1c)
    // 0xb8a924: mov             x3, x0
    // 0xb8a928: ldur            x0, [fp, #-0x10]
    // 0xb8a92c: stur            x3, [fp, #-8]
    // 0xb8a930: StoreField: r3->field_f = r0
    //     0xb8a930: stur            w0, [x3, #0xf]
    // 0xb8a934: ldur            x2, [fp, #-0x18]
    // 0xb8a938: r1 = Function '<anonymous closure>':.
    //     0xb8a938: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a798] AnonymousClosure: (0xb8a980), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_video_media_carousel.dart] _ProductVideoMediaCarouselState::build (0xb8a8dc)
    //     0xb8a93c: ldr             x1, [x1, #0x798]
    // 0xb8a940: r0 = AllocateClosure()
    //     0xb8a940: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb8a944: mov             x1, x0
    // 0xb8a948: ldur            x0, [fp, #-8]
    // 0xb8a94c: StoreField: r0->field_13 = r1
    //     0xb8a94c: stur            w1, [x0, #0x13]
    // 0xb8a950: r0 = Padding()
    //     0xb8a950: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8a954: r1 = Instance_EdgeInsets
    //     0xb8a954: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb8a958: ldr             x1, [x1, #0x980]
    // 0xb8a95c: StoreField: r0->field_f = r1
    //     0xb8a95c: stur            w1, [x0, #0xf]
    // 0xb8a960: ldur            x1, [fp, #-8]
    // 0xb8a964: StoreField: r0->field_b = r1
    //     0xb8a964: stur            w1, [x0, #0xb]
    // 0xb8a968: LeaveFrame
    //     0xb8a968: mov             SP, fp
    //     0xb8a96c: ldp             fp, lr, [SP], #0x10
    // 0xb8a970: ret
    //     0xb8a970: ret             
    // 0xb8a974: r9 = _future
    //     0xb8a974: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7a0] Field <_ProductVideoMediaCarouselState@1631471210._future@1631471210>: late (offset: 0x1c)
    //     0xb8a978: ldr             x9, [x9, #0x7a0]
    // 0xb8a97c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb8a97c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<Object?>) {
    // ** addr: 0xb8a980, size: 0x104
    // 0xb8a980: EnterFrame
    //     0xb8a980: stp             fp, lr, [SP, #-0x10]!
    //     0xb8a984: mov             fp, SP
    // 0xb8a988: AllocStack(0x18)
    //     0xb8a988: sub             SP, SP, #0x18
    // 0xb8a98c: SetupParameters()
    //     0xb8a98c: ldr             x0, [fp, #0x20]
    //     0xb8a990: ldur            w2, [x0, #0x17]
    //     0xb8a994: add             x2, x2, HEAP, lsl #32
    //     0xb8a998: stur            x2, [fp, #-8]
    // 0xb8a99c: CheckStackOverflow
    //     0xb8a99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb8a9a0: cmp             SP, x16
    //     0xb8a9a4: b.ls            #0xb8aa64
    // 0xb8a9a8: LoadField: r0 = r2->field_f
    //     0xb8a9a8: ldur            w0, [x2, #0xf]
    // 0xb8a9ac: DecompressPointer r0
    //     0xb8a9ac: add             x0, x0, HEAP, lsl #32
    // 0xb8a9b0: LoadField: r1 = r0->field_13
    //     0xb8a9b0: ldur            w1, [x0, #0x13]
    // 0xb8a9b4: DecompressPointer r1
    //     0xb8a9b4: add             x1, x1, HEAP, lsl #32
    // 0xb8a9b8: r16 = Sentinel
    //     0xb8a9b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb8a9bc: cmp             w1, w16
    // 0xb8a9c0: b.eq            #0xb8aa6c
    // 0xb8a9c4: LoadField: r0 = r1->field_27
    //     0xb8a9c4: ldur            w0, [x1, #0x27]
    // 0xb8a9c8: DecompressPointer r0
    //     0xb8a9c8: add             x0, x0, HEAP, lsl #32
    // 0xb8a9cc: LoadField: r1 = r0->field_4b
    //     0xb8a9cc: ldur            w1, [x0, #0x4b]
    // 0xb8a9d0: DecompressPointer r1
    //     0xb8a9d0: add             x1, x1, HEAP, lsl #32
    // 0xb8a9d4: tbnz            w1, #4, #0xb8aa34
    // 0xb8a9d8: mov             x1, x0
    // 0xb8a9dc: r0 = aspectRatio()
    //     0xb8a9dc: bl              #0x8faac4  ; [package:video_player/video_player.dart] VideoPlayerValue::aspectRatio
    // 0xb8a9e0: ldur            x0, [fp, #-8]
    // 0xb8a9e4: stur            d0, [fp, #-0x18]
    // 0xb8a9e8: LoadField: r1 = r0->field_f
    //     0xb8a9e8: ldur            w1, [x0, #0xf]
    // 0xb8a9ec: DecompressPointer r1
    //     0xb8a9ec: add             x1, x1, HEAP, lsl #32
    // 0xb8a9f0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb8a9f0: ldur            w0, [x1, #0x17]
    // 0xb8a9f4: DecompressPointer r0
    //     0xb8a9f4: add             x0, x0, HEAP, lsl #32
    // 0xb8a9f8: r16 = Sentinel
    //     0xb8a9f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb8a9fc: cmp             w0, w16
    // 0xb8aa00: b.eq            #0xb8aa78
    // 0xb8aa04: stur            x0, [fp, #-8]
    // 0xb8aa08: r0 = Chewie()
    //     0xb8aa08: bl              #0x9d1c78  ; AllocateChewieStub -> Chewie (size=0x10)
    // 0xb8aa0c: mov             x1, x0
    // 0xb8aa10: ldur            x0, [fp, #-8]
    // 0xb8aa14: stur            x1, [fp, #-0x10]
    // 0xb8aa18: StoreField: r1->field_b = r0
    //     0xb8aa18: stur            w0, [x1, #0xb]
    // 0xb8aa1c: r0 = AspectRatio()
    //     0xb8aa1c: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb8aa20: ldur            d0, [fp, #-0x18]
    // 0xb8aa24: StoreField: r0->field_f = d0
    //     0xb8aa24: stur            d0, [x0, #0xf]
    // 0xb8aa28: ldur            x1, [fp, #-0x10]
    // 0xb8aa2c: StoreField: r0->field_b = r1
    //     0xb8aa2c: stur            w1, [x0, #0xb]
    // 0xb8aa30: b               #0xb8aa3c
    // 0xb8aa34: r0 = Instance_CircularProgressIndicator
    //     0xb8aa34: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3daa8] Obj!CircularProgressIndicator@d65741
    //     0xb8aa38: ldr             x0, [x0, #0xaa8]
    // 0xb8aa3c: stur            x0, [fp, #-8]
    // 0xb8aa40: r0 = Center()
    //     0xb8aa40: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb8aa44: r1 = Instance_Alignment
    //     0xb8aa44: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb8aa48: ldr             x1, [x1, #0xb10]
    // 0xb8aa4c: StoreField: r0->field_f = r1
    //     0xb8aa4c: stur            w1, [x0, #0xf]
    // 0xb8aa50: ldur            x1, [fp, #-8]
    // 0xb8aa54: StoreField: r0->field_b = r1
    //     0xb8aa54: stur            w1, [x0, #0xb]
    // 0xb8aa58: LeaveFrame
    //     0xb8aa58: mov             SP, fp
    //     0xb8aa5c: ldp             fp, lr, [SP], #0x10
    // 0xb8aa60: ret
    //     0xb8aa60: ret             
    // 0xb8aa64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8aa64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb8aa68: b               #0xb8a9a8
    // 0xb8aa6c: r9 = _controller
    //     0xb8aa6c: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7a8] Field <_ProductVideoMediaCarouselState@1631471210._controller@1631471210>: late (offset: 0x14)
    //     0xb8aa70: ldr             x9, [x9, #0x7a8]
    // 0xb8aa74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb8aa74: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb8aa78: r9 = _chewieController
    //     0xb8aa78: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7b0] Field <_ProductVideoMediaCarouselState@1631471210._chewieController@1631471210>: late (offset: 0x18)
    //     0xb8aa7c: ldr             x9, [x9, #0x7b0]
    // 0xb8aa80: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xb8aa80: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87e38, size: 0x54
    // 0xc87e38: EnterFrame
    //     0xc87e38: stp             fp, lr, [SP, #-0x10]!
    //     0xc87e3c: mov             fp, SP
    // 0xc87e40: CheckStackOverflow
    //     0xc87e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87e44: cmp             SP, x16
    //     0xc87e48: b.ls            #0xc87e78
    // 0xc87e4c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc87e4c: ldur            w0, [x1, #0x17]
    // 0xc87e50: DecompressPointer r0
    //     0xc87e50: add             x0, x0, HEAP, lsl #32
    // 0xc87e54: r16 = Sentinel
    //     0xc87e54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87e58: cmp             w0, w16
    // 0xc87e5c: b.eq            #0xc87e80
    // 0xc87e60: mov             x1, x0
    // 0xc87e64: r0 = dispose()
    //     0xc87e64: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87e68: r0 = Null
    //     0xc87e68: mov             x0, NULL
    // 0xc87e6c: LeaveFrame
    //     0xc87e6c: mov             SP, fp
    //     0xc87e70: ldp             fp, lr, [SP], #0x10
    // 0xc87e74: ret
    //     0xc87e74: ret             
    // 0xc87e78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87e78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87e7c: b               #0xc87e4c
    // 0xc87e80: r9 = _chewieController
    //     0xc87e80: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a7b0] Field <_ProductVideoMediaCarouselState@1631471210._chewieController@1631471210>: late (offset: 0x18)
    //     0xc87e84: ldr             x9, [x9, #0x7b0]
    // 0xc87e88: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87e88: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4052, size: 0x10, field offset: 0xc
//   const constructor, 
class ProductVideoMediaCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f9b8, size: 0x34
    // 0xc7f9b8: EnterFrame
    //     0xc7f9b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f9bc: mov             fp, SP
    // 0xc7f9c0: mov             x0, x1
    // 0xc7f9c4: r1 = <ProductVideoMediaCarousel>
    //     0xc7f9c4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d60] TypeArguments: <ProductVideoMediaCarousel>
    //     0xc7f9c8: ldr             x1, [x1, #0xd60]
    // 0xc7f9cc: r0 = _ProductVideoMediaCarouselState()
    //     0xc7f9cc: bl              #0xc7f9ec  ; Allocate_ProductVideoMediaCarouselStateStub -> _ProductVideoMediaCarouselState (size=0x20)
    // 0xc7f9d0: r1 = Sentinel
    //     0xc7f9d0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f9d4: StoreField: r0->field_13 = r1
    //     0xc7f9d4: stur            w1, [x0, #0x13]
    // 0xc7f9d8: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7f9d8: stur            w1, [x0, #0x17]
    // 0xc7f9dc: StoreField: r0->field_1b = r1
    //     0xc7f9dc: stur            w1, [x0, #0x1b]
    // 0xc7f9e0: LeaveFrame
    //     0xc7f9e0: mov             SP, fp
    //     0xc7f9e4: ldp             fp, lr, [SP], #0x10
    // 0xc7f9e8: ret
    //     0xc7f9e8: ret             
  }
}
