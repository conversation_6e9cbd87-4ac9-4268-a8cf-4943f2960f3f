// lib: , url: package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart

// class id: 1049456, size: 0x8
class :: {
}

// class id: 3299, size: 0x40, field offset: 0x14
class _RatingReviewAllMediaOnTapImageState extends State<dynamic> {

  late PageController _pageController; // offset: 0x24

  _ initState(/* No info */) {
    // ** addr: 0x9448f0, size: 0x18c
    // 0x9448f0: EnterFrame
    //     0x9448f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9448f4: mov             fp, SP
    // 0x9448f8: AllocStack(0x18)
    //     0x9448f8: sub             SP, SP, #0x18
    // 0x9448fc: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */)
    //     0x9448fc: mov             x0, x1
    //     0x944900: stur            x1, [fp, #-8]
    // 0x944904: CheckStackOverflow
    //     0x944904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944908: cmp             SP, x16
    //     0x94490c: b.ls            #0x944a68
    // 0x944910: LoadField: r1 = r0->field_b
    //     0x944910: ldur            w1, [x0, #0xb]
    // 0x944914: DecompressPointer r1
    //     0x944914: add             x1, x1, HEAP, lsl #32
    // 0x944918: cmp             w1, NULL
    // 0x94491c: b.eq            #0x944a70
    // 0x944920: LoadField: r2 = r1->field_13
    //     0x944920: ldur            x2, [x1, #0x13]
    // 0x944924: StoreField: r0->field_13 = r2
    //     0x944924: stur            x2, [x0, #0x13]
    // 0x944928: LoadField: r2 = r1->field_1b
    //     0x944928: ldur            w2, [x1, #0x1b]
    // 0x94492c: DecompressPointer r2
    //     0x94492c: add             x2, x2, HEAP, lsl #32
    // 0x944930: cmp             w2, NULL
    // 0x944934: b.ne            #0x944940
    // 0x944938: r1 = 0
    //     0x944938: movz            x1, #0
    // 0x94493c: b               #0x94494c
    // 0x944940: r1 = LoadInt32Instr(r2)
    //     0x944940: sbfx            x1, x2, #1, #0x1f
    //     0x944944: tbz             w2, #0, #0x94494c
    //     0x944948: ldur            x1, [x2, #7]
    // 0x94494c: StoreField: r0->field_1b = r1
    //     0x94494c: stur            x1, [x0, #0x1b]
    // 0x944950: mov             x1, x0
    // 0x944954: r0 = _flattenMediaList()
    //     0x944954: bl              #0x944a9c  ; [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_flattenMediaList
    // 0x944958: ldur            x0, [fp, #-8]
    // 0x94495c: LoadField: r1 = r0->field_b
    //     0x94495c: ldur            w1, [x0, #0xb]
    // 0x944960: DecompressPointer r1
    //     0x944960: add             x1, x1, HEAP, lsl #32
    // 0x944964: cmp             w1, NULL
    // 0x944968: b.eq            #0x944a74
    // 0x94496c: LoadField: r2 = r1->field_13
    //     0x94496c: ldur            x2, [x1, #0x13]
    // 0x944970: LoadField: r3 = r1->field_1b
    //     0x944970: ldur            w3, [x1, #0x1b]
    // 0x944974: DecompressPointer r3
    //     0x944974: add             x3, x3, HEAP, lsl #32
    // 0x944978: cmp             w3, NULL
    // 0x94497c: b.ne            #0x944988
    // 0x944980: r3 = 0
    //     0x944980: movz            x3, #0
    // 0x944984: b               #0x944998
    // 0x944988: r1 = LoadInt32Instr(r3)
    //     0x944988: sbfx            x1, x3, #1, #0x1f
    //     0x94498c: tbz             w3, #0, #0x944994
    //     0x944990: ldur            x1, [x3, #7]
    // 0x944994: mov             x3, x1
    // 0x944998: mov             x1, x0
    // 0x94499c: r0 = _getGlobalIndex()
    //     0x94499c: bl              #0x935398  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_getGlobalIndex
    // 0x9449a0: stur            x0, [fp, #-0x10]
    // 0x9449a4: r0 = PageController()
    //     0x9449a4: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x9449a8: mov             x2, x0
    // 0x9449ac: ldur            x0, [fp, #-0x10]
    // 0x9449b0: stur            x2, [fp, #-0x18]
    // 0x9449b4: StoreField: r2->field_3f = r0
    //     0x9449b4: stur            x0, [x2, #0x3f]
    // 0x9449b8: r0 = true
    //     0x9449b8: add             x0, NULL, #0x20  ; true
    // 0x9449bc: StoreField: r2->field_47 = r0
    //     0x9449bc: stur            w0, [x2, #0x47]
    // 0x9449c0: d0 = 1.000000
    //     0x9449c0: fmov            d0, #1.00000000
    // 0x9449c4: StoreField: r2->field_4b = d0
    //     0x9449c4: stur            d0, [x2, #0x4b]
    // 0x9449c8: mov             x1, x2
    // 0x9449cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9449cc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9449d0: r0 = ScrollController()
    //     0x9449d0: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x9449d4: ldur            x0, [fp, #-0x18]
    // 0x9449d8: ldur            x3, [fp, #-8]
    // 0x9449dc: StoreField: r3->field_23 = r0
    //     0x9449dc: stur            w0, [x3, #0x23]
    //     0x9449e0: ldurb           w16, [x3, #-1]
    //     0x9449e4: ldurb           w17, [x0, #-1]
    //     0x9449e8: and             x16, x17, x16, lsr #2
    //     0x9449ec: tst             x16, HEAP, lsr #32
    //     0x9449f0: b.eq            #0x9449f8
    //     0x9449f4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9449f8: LoadField: r0 = r3->field_27
    //     0x9449f8: ldur            w0, [x3, #0x27]
    // 0x9449fc: DecompressPointer r0
    //     0x9449fc: add             x0, x0, HEAP, lsl #32
    // 0x944a00: mov             x2, x3
    // 0x944a04: stur            x0, [fp, #-0x18]
    // 0x944a08: r1 = Function '_onCollapseChanged@1633116981':.
    //     0x944a08: add             x1, PP, #0x55, lsl #12  ; [pp+0x55030] AnonymousClosure: (0x944da0), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x944dd8)
    //     0x944a0c: ldr             x1, [x1, #0x30]
    // 0x944a10: r0 = AllocateClosure()
    //     0x944a10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944a14: ldur            x1, [fp, #-0x18]
    // 0x944a18: mov             x2, x0
    // 0x944a1c: r0 = addListener()
    //     0x944a1c: bl              #0x7b8dac  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x944a20: ldur            x1, [fp, #-8]
    // 0x944a24: LoadField: r2 = r1->field_b
    //     0x944a24: ldur            w2, [x1, #0xb]
    // 0x944a28: DecompressPointer r2
    //     0x944a28: add             x2, x2, HEAP, lsl #32
    // 0x944a2c: cmp             w2, NULL
    // 0x944a30: b.eq            #0x944a78
    // 0x944a34: LoadField: r0 = r2->field_23
    //     0x944a34: ldur            w0, [x2, #0x23]
    // 0x944a38: DecompressPointer r0
    //     0x944a38: add             x0, x0, HEAP, lsl #32
    // 0x944a3c: StoreField: r1->field_2f = r0
    //     0x944a3c: stur            w0, [x1, #0x2f]
    //     0x944a40: ldurb           w16, [x1, #-1]
    //     0x944a44: ldurb           w17, [x0, #-1]
    //     0x944a48: and             x16, x17, x16, lsr #2
    //     0x944a4c: tst             x16, HEAP, lsr #32
    //     0x944a50: b.eq            #0x944a58
    //     0x944a54: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x944a58: r0 = Null
    //     0x944a58: mov             x0, NULL
    // 0x944a5c: LeaveFrame
    //     0x944a5c: mov             SP, fp
    //     0x944a60: ldp             fp, lr, [SP], #0x10
    // 0x944a64: ret
    //     0x944a64: ret             
    // 0x944a68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944a68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944a6c: b               #0x944910
    // 0x944a70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944a70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944a74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944a74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944a78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944a78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _flattenMediaList(/* No info */) {
    // ** addr: 0x944a9c, size: 0x304
    // 0x944a9c: EnterFrame
    //     0x944a9c: stp             fp, lr, [SP, #-0x10]!
    //     0x944aa0: mov             fp, SP
    // 0x944aa4: AllocStack(0x58)
    //     0x944aa4: sub             SP, SP, #0x58
    // 0x944aa8: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */)
    //     0x944aa8: mov             x0, x1
    //     0x944aac: stur            x1, [fp, #-8]
    // 0x944ab0: CheckStackOverflow
    //     0x944ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944ab4: cmp             SP, x16
    //     0x944ab8: b.ls            #0x944d84
    // 0x944abc: LoadField: r1 = r0->field_37
    //     0x944abc: ldur            w1, [x0, #0x37]
    // 0x944ac0: DecompressPointer r1
    //     0x944ac0: add             x1, x1, HEAP, lsl #32
    // 0x944ac4: r0 = clear()
    //     0x944ac4: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x944ac8: ldur            x0, [fp, #-8]
    // 0x944acc: LoadField: r1 = r0->field_3b
    //     0x944acc: ldur            w1, [x0, #0x3b]
    // 0x944ad0: DecompressPointer r1
    //     0x944ad0: add             x1, x1, HEAP, lsl #32
    // 0x944ad4: r0 = clear()
    //     0x944ad4: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0x944ad8: r4 = 0
    //     0x944ad8: movz            x4, #0
    // 0x944adc: ldur            x3, [fp, #-8]
    // 0x944ae0: stur            x4, [fp, #-0x40]
    // 0x944ae4: CheckStackOverflow
    //     0x944ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944ae8: cmp             SP, x16
    //     0x944aec: b.ls            #0x944d8c
    // 0x944af0: LoadField: r0 = r3->field_b
    //     0x944af0: ldur            w0, [x3, #0xb]
    // 0x944af4: DecompressPointer r0
    //     0x944af4: add             x0, x0, HEAP, lsl #32
    // 0x944af8: cmp             w0, NULL
    // 0x944afc: b.eq            #0x944d94
    // 0x944b00: LoadField: r1 = r0->field_f
    //     0x944b00: ldur            w1, [x0, #0xf]
    // 0x944b04: DecompressPointer r1
    //     0x944b04: add             x1, x1, HEAP, lsl #32
    // 0x944b08: LoadField: r0 = r1->field_b
    //     0x944b08: ldur            w0, [x1, #0xb]
    // 0x944b0c: r2 = LoadInt32Instr(r0)
    //     0x944b0c: sbfx            x2, x0, #1, #0x1f
    // 0x944b10: cmp             x4, x2
    // 0x944b14: b.ge            #0x944d74
    // 0x944b18: LoadField: r0 = r1->field_f
    //     0x944b18: ldur            w0, [x1, #0xf]
    // 0x944b1c: DecompressPointer r0
    //     0x944b1c: add             x0, x0, HEAP, lsl #32
    // 0x944b20: lsl             x5, x4, #1
    // 0x944b24: stur            x5, [fp, #-0x38]
    // 0x944b28: ArrayLoad: r6 = r0[r4]  ; Unknown_4
    //     0x944b28: add             x16, x0, x4, lsl #2
    //     0x944b2c: ldur            w6, [x16, #0xf]
    // 0x944b30: DecompressPointer r6
    //     0x944b30: add             x6, x6, HEAP, lsl #32
    // 0x944b34: stur            x6, [fp, #-0x30]
    // 0x944b38: r7 = 0
    //     0x944b38: movz            x7, #0
    // 0x944b3c: stur            x7, [fp, #-0x28]
    // 0x944b40: CheckStackOverflow
    //     0x944b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944b44: cmp             SP, x16
    //     0x944b48: b.ls            #0x944d98
    // 0x944b4c: LoadField: r0 = r6->field_1b
    //     0x944b4c: ldur            w0, [x6, #0x1b]
    // 0x944b50: DecompressPointer r0
    //     0x944b50: add             x0, x0, HEAP, lsl #32
    // 0x944b54: LoadField: r1 = r0->field_b
    //     0x944b54: ldur            w1, [x0, #0xb]
    // 0x944b58: r2 = LoadInt32Instr(r1)
    //     0x944b58: sbfx            x2, x1, #1, #0x1f
    // 0x944b5c: cmp             x7, x2
    // 0x944b60: b.ge            #0x944d68
    // 0x944b64: LoadField: r8 = r3->field_37
    //     0x944b64: ldur            w8, [x3, #0x37]
    // 0x944b68: DecompressPointer r8
    //     0x944b68: add             x8, x8, HEAP, lsl #32
    // 0x944b6c: stur            x8, [fp, #-0x20]
    // 0x944b70: LoadField: r1 = r0->field_f
    //     0x944b70: ldur            w1, [x0, #0xf]
    // 0x944b74: DecompressPointer r1
    //     0x944b74: add             x1, x1, HEAP, lsl #32
    // 0x944b78: lsl             x9, x7, #1
    // 0x944b7c: stur            x9, [fp, #-0x18]
    // 0x944b80: ArrayLoad: r10 = r1[r7]  ; Unknown_4
    //     0x944b80: add             x16, x1, x7, lsl #2
    //     0x944b84: ldur            w10, [x16, #0xf]
    // 0x944b88: DecompressPointer r10
    //     0x944b88: add             x10, x10, HEAP, lsl #32
    // 0x944b8c: stur            x10, [fp, #-0x10]
    // 0x944b90: LoadField: r2 = r8->field_7
    //     0x944b90: ldur            w2, [x8, #7]
    // 0x944b94: DecompressPointer r2
    //     0x944b94: add             x2, x2, HEAP, lsl #32
    // 0x944b98: mov             x0, x10
    // 0x944b9c: r1 = Null
    //     0x944b9c: mov             x1, NULL
    // 0x944ba0: cmp             w2, NULL
    // 0x944ba4: b.eq            #0x944bc4
    // 0x944ba8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x944ba8: ldur            w4, [x2, #0x17]
    // 0x944bac: DecompressPointer r4
    //     0x944bac: add             x4, x4, HEAP, lsl #32
    // 0x944bb0: r8 = X0
    //     0x944bb0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x944bb4: LoadField: r9 = r4->field_7
    //     0x944bb4: ldur            x9, [x4, #7]
    // 0x944bb8: r3 = Null
    //     0x944bb8: add             x3, PP, #0x55, lsl #12  ; [pp+0x55040] Null
    //     0x944bbc: ldr             x3, [x3, #0x40]
    // 0x944bc0: blr             x9
    // 0x944bc4: ldur            x0, [fp, #-0x20]
    // 0x944bc8: LoadField: r1 = r0->field_b
    //     0x944bc8: ldur            w1, [x0, #0xb]
    // 0x944bcc: LoadField: r2 = r0->field_f
    //     0x944bcc: ldur            w2, [x0, #0xf]
    // 0x944bd0: DecompressPointer r2
    //     0x944bd0: add             x2, x2, HEAP, lsl #32
    // 0x944bd4: LoadField: r3 = r2->field_b
    //     0x944bd4: ldur            w3, [x2, #0xb]
    // 0x944bd8: r2 = LoadInt32Instr(r1)
    //     0x944bd8: sbfx            x2, x1, #1, #0x1f
    // 0x944bdc: stur            x2, [fp, #-0x48]
    // 0x944be0: r1 = LoadInt32Instr(r3)
    //     0x944be0: sbfx            x1, x3, #1, #0x1f
    // 0x944be4: cmp             x2, x1
    // 0x944be8: b.ne            #0x944bf4
    // 0x944bec: mov             x1, x0
    // 0x944bf0: r0 = _growToNextCapacity()
    //     0x944bf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x944bf4: ldur            x3, [fp, #-8]
    // 0x944bf8: ldur            x0, [fp, #-0x20]
    // 0x944bfc: ldur            x4, [fp, #-0x38]
    // 0x944c00: ldur            x5, [fp, #-0x18]
    // 0x944c04: ldur            x2, [fp, #-0x48]
    // 0x944c08: add             x1, x2, #1
    // 0x944c0c: lsl             x6, x1, #1
    // 0x944c10: StoreField: r0->field_b = r6
    //     0x944c10: stur            w6, [x0, #0xb]
    // 0x944c14: LoadField: r1 = r0->field_f
    //     0x944c14: ldur            w1, [x0, #0xf]
    // 0x944c18: DecompressPointer r1
    //     0x944c18: add             x1, x1, HEAP, lsl #32
    // 0x944c1c: ldur            x0, [fp, #-0x10]
    // 0x944c20: ArrayStore: r1[r2] = r0  ; List_4
    //     0x944c20: add             x25, x1, x2, lsl #2
    //     0x944c24: add             x25, x25, #0xf
    //     0x944c28: str             w0, [x25]
    //     0x944c2c: tbz             w0, #0, #0x944c48
    //     0x944c30: ldurb           w16, [x1, #-1]
    //     0x944c34: ldurb           w17, [x0, #-1]
    //     0x944c38: and             x16, x17, x16, lsr #2
    //     0x944c3c: tst             x16, HEAP, lsr #32
    //     0x944c40: b.eq            #0x944c48
    //     0x944c44: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x944c48: LoadField: r0 = r3->field_3b
    //     0x944c48: ldur            w0, [x3, #0x3b]
    // 0x944c4c: DecompressPointer r0
    //     0x944c4c: add             x0, x0, HEAP, lsl #32
    // 0x944c50: stur            x0, [fp, #-0x10]
    // 0x944c54: r1 = Null
    //     0x944c54: mov             x1, NULL
    // 0x944c58: r2 = 8
    //     0x944c58: movz            x2, #0x8
    // 0x944c5c: r0 = AllocateArray()
    //     0x944c5c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x944c60: r16 = "parentIndex"
    //     0x944c60: add             x16, PP, #0x52, lsl #12  ; [pp+0x52048] "parentIndex"
    //     0x944c64: ldr             x16, [x16, #0x48]
    // 0x944c68: StoreField: r0->field_f = r16
    //     0x944c68: stur            w16, [x0, #0xf]
    // 0x944c6c: ldur            x1, [fp, #-0x38]
    // 0x944c70: StoreField: r0->field_13 = r1
    //     0x944c70: stur            w1, [x0, #0x13]
    // 0x944c74: r16 = "childIndex"
    //     0x944c74: add             x16, PP, #0x52, lsl #12  ; [pp+0x52050] "childIndex"
    //     0x944c78: ldr             x16, [x16, #0x50]
    // 0x944c7c: ArrayStore: r0[0] = r16  ; List_4
    //     0x944c7c: stur            w16, [x0, #0x17]
    // 0x944c80: ldur            x2, [fp, #-0x18]
    // 0x944c84: StoreField: r0->field_1b = r2
    //     0x944c84: stur            w2, [x0, #0x1b]
    // 0x944c88: r16 = <String, int>
    //     0x944c88: ldr             x16, [PP, #0xd40]  ; [pp+0xd40] TypeArguments: <String, int>
    // 0x944c8c: stp             x0, x16, [SP]
    // 0x944c90: r0 = Map._fromLiteral()
    //     0x944c90: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x944c94: mov             x4, x0
    // 0x944c98: ldur            x3, [fp, #-0x10]
    // 0x944c9c: stur            x4, [fp, #-0x18]
    // 0x944ca0: LoadField: r2 = r3->field_7
    //     0x944ca0: ldur            w2, [x3, #7]
    // 0x944ca4: DecompressPointer r2
    //     0x944ca4: add             x2, x2, HEAP, lsl #32
    // 0x944ca8: mov             x0, x4
    // 0x944cac: r1 = Null
    //     0x944cac: mov             x1, NULL
    // 0x944cb0: cmp             w2, NULL
    // 0x944cb4: b.eq            #0x944cd4
    // 0x944cb8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x944cb8: ldur            w4, [x2, #0x17]
    // 0x944cbc: DecompressPointer r4
    //     0x944cbc: add             x4, x4, HEAP, lsl #32
    // 0x944cc0: r8 = X0
    //     0x944cc0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x944cc4: LoadField: r9 = r4->field_7
    //     0x944cc4: ldur            x9, [x4, #7]
    // 0x944cc8: r3 = Null
    //     0x944cc8: add             x3, PP, #0x55, lsl #12  ; [pp+0x55050] Null
    //     0x944ccc: ldr             x3, [x3, #0x50]
    // 0x944cd0: blr             x9
    // 0x944cd4: ldur            x0, [fp, #-0x10]
    // 0x944cd8: LoadField: r1 = r0->field_b
    //     0x944cd8: ldur            w1, [x0, #0xb]
    // 0x944cdc: LoadField: r2 = r0->field_f
    //     0x944cdc: ldur            w2, [x0, #0xf]
    // 0x944ce0: DecompressPointer r2
    //     0x944ce0: add             x2, x2, HEAP, lsl #32
    // 0x944ce4: LoadField: r3 = r2->field_b
    //     0x944ce4: ldur            w3, [x2, #0xb]
    // 0x944ce8: r2 = LoadInt32Instr(r1)
    //     0x944ce8: sbfx            x2, x1, #1, #0x1f
    // 0x944cec: stur            x2, [fp, #-0x48]
    // 0x944cf0: r1 = LoadInt32Instr(r3)
    //     0x944cf0: sbfx            x1, x3, #1, #0x1f
    // 0x944cf4: cmp             x2, x1
    // 0x944cf8: b.ne            #0x944d04
    // 0x944cfc: mov             x1, x0
    // 0x944d00: r0 = _growToNextCapacity()
    //     0x944d00: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x944d04: ldur            x4, [fp, #-0x28]
    // 0x944d08: ldur            x2, [fp, #-0x10]
    // 0x944d0c: ldur            x3, [fp, #-0x48]
    // 0x944d10: add             x5, x3, #1
    // 0x944d14: lsl             x6, x5, #1
    // 0x944d18: StoreField: r2->field_b = r6
    //     0x944d18: stur            w6, [x2, #0xb]
    // 0x944d1c: LoadField: r1 = r2->field_f
    //     0x944d1c: ldur            w1, [x2, #0xf]
    // 0x944d20: DecompressPointer r1
    //     0x944d20: add             x1, x1, HEAP, lsl #32
    // 0x944d24: ldur            x0, [fp, #-0x18]
    // 0x944d28: ArrayStore: r1[r3] = r0  ; List_4
    //     0x944d28: add             x25, x1, x3, lsl #2
    //     0x944d2c: add             x25, x25, #0xf
    //     0x944d30: str             w0, [x25]
    //     0x944d34: tbz             w0, #0, #0x944d50
    //     0x944d38: ldurb           w16, [x1, #-1]
    //     0x944d3c: ldurb           w17, [x0, #-1]
    //     0x944d40: and             x16, x17, x16, lsr #2
    //     0x944d44: tst             x16, HEAP, lsr #32
    //     0x944d48: b.eq            #0x944d50
    //     0x944d4c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x944d50: add             x7, x4, #1
    // 0x944d54: ldur            x3, [fp, #-8]
    // 0x944d58: ldur            x4, [fp, #-0x40]
    // 0x944d5c: ldur            x5, [fp, #-0x38]
    // 0x944d60: ldur            x6, [fp, #-0x30]
    // 0x944d64: b               #0x944b3c
    // 0x944d68: mov             x1, x4
    // 0x944d6c: add             x4, x1, #1
    // 0x944d70: b               #0x944adc
    // 0x944d74: r0 = Null
    //     0x944d74: mov             x0, NULL
    // 0x944d78: LeaveFrame
    //     0x944d78: mov             SP, fp
    //     0x944d7c: ldp             fp, lr, [SP], #0x10
    // 0x944d80: ret
    //     0x944d80: ret             
    // 0x944d84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944d84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944d88: b               #0x944abc
    // 0x944d8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944d8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944d90: b               #0x944af0
    // 0x944d94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944d94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944d98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944d9c: b               #0x944b4c
  }
  [closure] void _onCollapseChanged(dynamic) {
    // ** addr: 0x944da0, size: 0x38
    // 0x944da0: EnterFrame
    //     0x944da0: stp             fp, lr, [SP, #-0x10]!
    //     0x944da4: mov             fp, SP
    // 0x944da8: ldr             x0, [fp, #0x10]
    // 0x944dac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x944dac: ldur            w1, [x0, #0x17]
    // 0x944db0: DecompressPointer r1
    //     0x944db0: add             x1, x1, HEAP, lsl #32
    // 0x944db4: CheckStackOverflow
    //     0x944db4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944db8: cmp             SP, x16
    //     0x944dbc: b.ls            #0x944dd0
    // 0x944dc0: r0 = _onCollapseChanged()
    //     0x944dc0: bl              #0x944dd8  ; [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged
    // 0x944dc4: LeaveFrame
    //     0x944dc4: mov             SP, fp
    //     0x944dc8: ldp             fp, lr, [SP], #0x10
    // 0x944dcc: ret
    //     0x944dcc: ret             
    // 0x944dd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944dd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944dd4: b               #0x944dc0
  }
  _ _onCollapseChanged(/* No info */) {
    // ** addr: 0x944dd8, size: 0x64
    // 0x944dd8: EnterFrame
    //     0x944dd8: stp             fp, lr, [SP, #-0x10]!
    //     0x944ddc: mov             fp, SP
    // 0x944de0: AllocStack(0x8)
    //     0x944de0: sub             SP, SP, #8
    // 0x944de4: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r1, fp-0x8 */)
    //     0x944de4: stur            x1, [fp, #-8]
    // 0x944de8: CheckStackOverflow
    //     0x944de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944dec: cmp             SP, x16
    //     0x944df0: b.ls            #0x944e34
    // 0x944df4: r1 = 1
    //     0x944df4: movz            x1, #0x1
    // 0x944df8: r0 = AllocateContext()
    //     0x944df8: bl              #0x16f6108  ; AllocateContextStub
    // 0x944dfc: mov             x1, x0
    // 0x944e00: ldur            x0, [fp, #-8]
    // 0x944e04: StoreField: r1->field_f = r0
    //     0x944e04: stur            w0, [x1, #0xf]
    // 0x944e08: mov             x2, x1
    // 0x944e0c: r1 = Function '<anonymous closure>':.
    //     0x944e0c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55038] AnonymousClosure: (0x9357e0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x935814)
    //     0x944e10: ldr             x1, [x1, #0x38]
    // 0x944e14: r0 = AllocateClosure()
    //     0x944e14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944e18: ldur            x1, [fp, #-8]
    // 0x944e1c: mov             x2, x0
    // 0x944e20: r0 = setState()
    //     0x944e20: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x944e24: r0 = Null
    //     0x944e24: mov             x0, NULL
    // 0x944e28: LeaveFrame
    //     0x944e28: mov             SP, fp
    //     0x944e2c: ldp             fp, lr, [SP], #0x10
    // 0x944e30: ret
    //     0x944e30: ret             
    // 0x944e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944e34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944e38: b               #0x944df4
  }
  _ build(/* No info */) {
    // ** addr: 0xb94ab4, size: 0x2134
    // 0xb94ab4: EnterFrame
    //     0xb94ab4: stp             fp, lr, [SP, #-0x10]!
    //     0xb94ab8: mov             fp, SP
    // 0xb94abc: AllocStack(0x80)
    //     0xb94abc: sub             SP, SP, #0x80
    // 0xb94ac0: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb94ac0: stur            x1, [fp, #-8]
    //     0xb94ac4: stur            x2, [fp, #-0x10]
    // 0xb94ac8: CheckStackOverflow
    //     0xb94ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94acc: cmp             SP, x16
    //     0xb94ad0: b.ls            #0xb96b34
    // 0xb94ad4: r1 = 2
    //     0xb94ad4: movz            x1, #0x2
    // 0xb94ad8: r0 = AllocateContext()
    //     0xb94ad8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb94adc: mov             x3, x0
    // 0xb94ae0: ldur            x0, [fp, #-8]
    // 0xb94ae4: stur            x3, [fp, #-0x20]
    // 0xb94ae8: StoreField: r3->field_f = r0
    //     0xb94ae8: stur            w0, [x3, #0xf]
    // 0xb94aec: ldur            x1, [fp, #-0x10]
    // 0xb94af0: StoreField: r3->field_13 = r1
    //     0xb94af0: stur            w1, [x3, #0x13]
    // 0xb94af4: LoadField: r4 = r0->field_23
    //     0xb94af4: ldur            w4, [x0, #0x23]
    // 0xb94af8: DecompressPointer r4
    //     0xb94af8: add             x4, x4, HEAP, lsl #32
    // 0xb94afc: r16 = Sentinel
    //     0xb94afc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb94b00: cmp             w4, w16
    // 0xb94b04: b.eq            #0xb96b3c
    // 0xb94b08: stur            x4, [fp, #-0x18]
    // 0xb94b0c: LoadField: r1 = r0->field_37
    //     0xb94b0c: ldur            w1, [x0, #0x37]
    // 0xb94b10: DecompressPointer r1
    //     0xb94b10: add             x1, x1, HEAP, lsl #32
    // 0xb94b14: LoadField: r5 = r1->field_b
    //     0xb94b14: ldur            w5, [x1, #0xb]
    // 0xb94b18: mov             x2, x3
    // 0xb94b1c: stur            x5, [fp, #-0x10]
    // 0xb94b20: r1 = Function '<anonymous closure>':.
    //     0xb94b20: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f98] AnonymousClosure: (0xb97c88), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb94ab4)
    //     0xb94b24: ldr             x1, [x1, #0xf98]
    // 0xb94b28: r0 = AllocateClosure()
    //     0xb94b28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94b2c: ldur            x2, [fp, #-0x20]
    // 0xb94b30: r1 = Function '<anonymous closure>':.
    //     0xb94b30: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fa0] AnonymousClosure: (0xb974f0), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb94ab4)
    //     0xb94b34: ldr             x1, [x1, #0xfa0]
    // 0xb94b38: stur            x0, [fp, #-0x28]
    // 0xb94b3c: r0 = AllocateClosure()
    //     0xb94b3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94b40: stur            x0, [fp, #-0x30]
    // 0xb94b44: r0 = PageView()
    //     0xb94b44: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb94b48: stur            x0, [fp, #-0x38]
    // 0xb94b4c: ldur            x16, [fp, #-0x18]
    // 0xb94b50: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb94b50: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb94b54: ldr             lr, [lr, #0x1c8]
    // 0xb94b58: stp             lr, x16, [SP]
    // 0xb94b5c: mov             x1, x0
    // 0xb94b60: ldur            x2, [fp, #-0x30]
    // 0xb94b64: ldur            x3, [fp, #-0x10]
    // 0xb94b68: ldur            x5, [fp, #-0x28]
    // 0xb94b6c: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x4, physics, 0x5, null]
    //     0xb94b6c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51f98] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x4, "physics", 0x5, Null]
    //     0xb94b70: ldr             x4, [x4, #0xf98]
    // 0xb94b74: r0 = PageView.builder()
    //     0xb94b74: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb94b78: ldur            x3, [fp, #-8]
    // 0xb94b7c: LoadField: r0 = r3->field_b
    //     0xb94b7c: ldur            w0, [x3, #0xb]
    // 0xb94b80: DecompressPointer r0
    //     0xb94b80: add             x0, x0, HEAP, lsl #32
    // 0xb94b84: cmp             w0, NULL
    // 0xb94b88: b.eq            #0xb96b48
    // 0xb94b8c: LoadField: r2 = r0->field_f
    //     0xb94b8c: ldur            w2, [x0, #0xf]
    // 0xb94b90: DecompressPointer r2
    //     0xb94b90: add             x2, x2, HEAP, lsl #32
    // 0xb94b94: LoadField: r4 = r3->field_13
    //     0xb94b94: ldur            x4, [x3, #0x13]
    // 0xb94b98: LoadField: r0 = r2->field_b
    //     0xb94b98: ldur            w0, [x2, #0xb]
    // 0xb94b9c: r1 = LoadInt32Instr(r0)
    //     0xb94b9c: sbfx            x1, x0, #1, #0x1f
    // 0xb94ba0: mov             x0, x1
    // 0xb94ba4: mov             x1, x4
    // 0xb94ba8: cmp             x1, x0
    // 0xb94bac: b.hs            #0xb96b4c
    // 0xb94bb0: LoadField: r0 = r2->field_f
    //     0xb94bb0: ldur            w0, [x2, #0xf]
    // 0xb94bb4: DecompressPointer r0
    //     0xb94bb4: add             x0, x0, HEAP, lsl #32
    // 0xb94bb8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb94bb8: add             x16, x0, x4, lsl #2
    //     0xb94bbc: ldur            w1, [x16, #0xf]
    // 0xb94bc0: DecompressPointer r1
    //     0xb94bc0: add             x1, x1, HEAP, lsl #32
    // 0xb94bc4: LoadField: r0 = r1->field_1b
    //     0xb94bc4: ldur            w0, [x1, #0x1b]
    // 0xb94bc8: DecompressPointer r0
    //     0xb94bc8: add             x0, x0, HEAP, lsl #32
    // 0xb94bcc: LoadField: r4 = r0->field_b
    //     0xb94bcc: ldur            w4, [x0, #0xb]
    // 0xb94bd0: ldur            x2, [fp, #-0x20]
    // 0xb94bd4: stur            x4, [fp, #-0x10]
    // 0xb94bd8: r1 = Function '<anonymous closure>':.
    //     0xb94bd8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fa8] AnonymousClosure: (0xaa55c4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb94bdc: ldr             x1, [x1, #0xfa8]
    // 0xb94be0: r0 = AllocateClosure()
    //     0xb94be0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94be4: mov             x3, x0
    // 0xb94be8: ldur            x0, [fp, #-0x10]
    // 0xb94bec: stur            x3, [fp, #-0x18]
    // 0xb94bf0: r2 = LoadInt32Instr(r0)
    //     0xb94bf0: sbfx            x2, x0, #1, #0x1f
    // 0xb94bf4: r1 = <Widget>
    //     0xb94bf4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb94bf8: r0 = _GrowableList()
    //     0xb94bf8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb94bfc: mov             x1, x0
    // 0xb94c00: stur            x1, [fp, #-0x10]
    // 0xb94c04: r2 = 0
    //     0xb94c04: movz            x2, #0
    // 0xb94c08: stur            x2, [fp, #-0x40]
    // 0xb94c0c: CheckStackOverflow
    //     0xb94c0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94c10: cmp             SP, x16
    //     0xb94c14: b.ls            #0xb96b50
    // 0xb94c18: LoadField: r0 = r1->field_b
    //     0xb94c18: ldur            w0, [x1, #0xb]
    // 0xb94c1c: r3 = LoadInt32Instr(r0)
    //     0xb94c1c: sbfx            x3, x0, #1, #0x1f
    // 0xb94c20: cmp             x2, x3
    // 0xb94c24: b.ge            #0xb94ce8
    // 0xb94c28: lsl             x0, x2, #1
    // 0xb94c2c: ldur            x16, [fp, #-0x18]
    // 0xb94c30: stp             x0, x16, [SP]
    // 0xb94c34: ldur            x0, [fp, #-0x18]
    // 0xb94c38: ClosureCall
    //     0xb94c38: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb94c3c: ldur            x2, [x0, #0x1f]
    //     0xb94c40: blr             x2
    // 0xb94c44: mov             x3, x0
    // 0xb94c48: r2 = Null
    //     0xb94c48: mov             x2, NULL
    // 0xb94c4c: r1 = Null
    //     0xb94c4c: mov             x1, NULL
    // 0xb94c50: stur            x3, [fp, #-0x28]
    // 0xb94c54: r4 = 60
    //     0xb94c54: movz            x4, #0x3c
    // 0xb94c58: branchIfSmi(r0, 0xb94c64)
    //     0xb94c58: tbz             w0, #0, #0xb94c64
    // 0xb94c5c: r4 = LoadClassIdInstr(r0)
    //     0xb94c5c: ldur            x4, [x0, #-1]
    //     0xb94c60: ubfx            x4, x4, #0xc, #0x14
    // 0xb94c64: sub             x4, x4, #0xe60
    // 0xb94c68: cmp             x4, #0x464
    // 0xb94c6c: b.ls            #0xb94c84
    // 0xb94c70: r8 = Widget
    //     0xb94c70: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xb94c74: ldr             x8, [x8, #0xe68]
    // 0xb94c78: r3 = Null
    //     0xb94c78: add             x3, PP, #0x54, lsl #12  ; [pp+0x54fb0] Null
    //     0xb94c7c: ldr             x3, [x3, #0xfb0]
    // 0xb94c80: r0 = Widget()
    //     0xb94c80: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xb94c84: ldur            x3, [fp, #-0x10]
    // 0xb94c88: LoadField: r0 = r3->field_b
    //     0xb94c88: ldur            w0, [x3, #0xb]
    // 0xb94c8c: r1 = LoadInt32Instr(r0)
    //     0xb94c8c: sbfx            x1, x0, #1, #0x1f
    // 0xb94c90: mov             x0, x1
    // 0xb94c94: ldur            x1, [fp, #-0x40]
    // 0xb94c98: cmp             x1, x0
    // 0xb94c9c: b.hs            #0xb96b58
    // 0xb94ca0: LoadField: r1 = r3->field_f
    //     0xb94ca0: ldur            w1, [x3, #0xf]
    // 0xb94ca4: DecompressPointer r1
    //     0xb94ca4: add             x1, x1, HEAP, lsl #32
    // 0xb94ca8: ldur            x0, [fp, #-0x28]
    // 0xb94cac: ldur            x2, [fp, #-0x40]
    // 0xb94cb0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb94cb0: add             x25, x1, x2, lsl #2
    //     0xb94cb4: add             x25, x25, #0xf
    //     0xb94cb8: str             w0, [x25]
    //     0xb94cbc: tbz             w0, #0, #0xb94cd8
    //     0xb94cc0: ldurb           w16, [x1, #-1]
    //     0xb94cc4: ldurb           w17, [x0, #-1]
    //     0xb94cc8: and             x16, x17, x16, lsr #2
    //     0xb94ccc: tst             x16, HEAP, lsr #32
    //     0xb94cd0: b.eq            #0xb94cd8
    //     0xb94cd4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb94cd8: add             x0, x2, #1
    // 0xb94cdc: mov             x2, x0
    // 0xb94ce0: mov             x1, x3
    // 0xb94ce4: b               #0xb94c08
    // 0xb94ce8: ldur            x0, [fp, #-8]
    // 0xb94cec: mov             x3, x1
    // 0xb94cf0: ldur            x1, [fp, #-0x38]
    // 0xb94cf4: r0 = Row()
    //     0xb94cf4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb94cf8: mov             x2, x0
    // 0xb94cfc: r0 = Instance_Axis
    //     0xb94cfc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb94d00: stur            x2, [fp, #-0x18]
    // 0xb94d04: StoreField: r2->field_f = r0
    //     0xb94d04: stur            w0, [x2, #0xf]
    // 0xb94d08: r1 = Instance_MainAxisAlignment
    //     0xb94d08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb94d0c: ldr             x1, [x1, #0xab0]
    // 0xb94d10: StoreField: r2->field_13 = r1
    //     0xb94d10: stur            w1, [x2, #0x13]
    // 0xb94d14: r3 = Instance_MainAxisSize
    //     0xb94d14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb94d18: ldr             x3, [x3, #0xa10]
    // 0xb94d1c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb94d1c: stur            w3, [x2, #0x17]
    // 0xb94d20: r4 = Instance_CrossAxisAlignment
    //     0xb94d20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb94d24: ldr             x4, [x4, #0xa18]
    // 0xb94d28: StoreField: r2->field_1b = r4
    //     0xb94d28: stur            w4, [x2, #0x1b]
    // 0xb94d2c: r5 = Instance_VerticalDirection
    //     0xb94d2c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb94d30: ldr             x5, [x5, #0xa20]
    // 0xb94d34: StoreField: r2->field_23 = r5
    //     0xb94d34: stur            w5, [x2, #0x23]
    // 0xb94d38: r6 = Instance_Clip
    //     0xb94d38: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb94d3c: ldr             x6, [x6, #0x38]
    // 0xb94d40: StoreField: r2->field_2b = r6
    //     0xb94d40: stur            w6, [x2, #0x2b]
    // 0xb94d44: StoreField: r2->field_2f = rZR
    //     0xb94d44: stur            xzr, [x2, #0x2f]
    // 0xb94d48: ldur            x1, [fp, #-0x10]
    // 0xb94d4c: StoreField: r2->field_b = r1
    //     0xb94d4c: stur            w1, [x2, #0xb]
    // 0xb94d50: r1 = <StackParentData>
    //     0xb94d50: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb94d54: ldr             x1, [x1, #0x8e0]
    // 0xb94d58: r0 = Positioned()
    //     0xb94d58: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb94d5c: mov             x3, x0
    // 0xb94d60: r0 = 12.000000
    //     0xb94d60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb94d64: ldr             x0, [x0, #0x9e8]
    // 0xb94d68: stur            x3, [fp, #-0x10]
    // 0xb94d6c: StoreField: r3->field_13 = r0
    //     0xb94d6c: stur            w0, [x3, #0x13]
    // 0xb94d70: ArrayStore: r3[0] = r0  ; List_4
    //     0xb94d70: stur            w0, [x3, #0x17]
    // 0xb94d74: ldur            x1, [fp, #-0x18]
    // 0xb94d78: StoreField: r3->field_b = r1
    //     0xb94d78: stur            w1, [x3, #0xb]
    // 0xb94d7c: r1 = Null
    //     0xb94d7c: mov             x1, NULL
    // 0xb94d80: r2 = 4
    //     0xb94d80: movz            x2, #0x4
    // 0xb94d84: r0 = AllocateArray()
    //     0xb94d84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb94d88: mov             x2, x0
    // 0xb94d8c: ldur            x0, [fp, #-0x38]
    // 0xb94d90: stur            x2, [fp, #-0x18]
    // 0xb94d94: StoreField: r2->field_f = r0
    //     0xb94d94: stur            w0, [x2, #0xf]
    // 0xb94d98: ldur            x0, [fp, #-0x10]
    // 0xb94d9c: StoreField: r2->field_13 = r0
    //     0xb94d9c: stur            w0, [x2, #0x13]
    // 0xb94da0: r1 = <Widget>
    //     0xb94da0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb94da4: r0 = AllocateGrowableArray()
    //     0xb94da4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb94da8: mov             x1, x0
    // 0xb94dac: ldur            x0, [fp, #-0x18]
    // 0xb94db0: stur            x1, [fp, #-0x10]
    // 0xb94db4: StoreField: r1->field_f = r0
    //     0xb94db4: stur            w0, [x1, #0xf]
    // 0xb94db8: r2 = 4
    //     0xb94db8: movz            x2, #0x4
    // 0xb94dbc: StoreField: r1->field_b = r2
    //     0xb94dbc: stur            w2, [x1, #0xb]
    // 0xb94dc0: ldur            x3, [fp, #-8]
    // 0xb94dc4: LoadField: r0 = r3->field_b
    //     0xb94dc4: ldur            w0, [x3, #0xb]
    // 0xb94dc8: DecompressPointer r0
    //     0xb94dc8: add             x0, x0, HEAP, lsl #32
    // 0xb94dcc: cmp             w0, NULL
    // 0xb94dd0: b.eq            #0xb96b5c
    // 0xb94dd4: LoadField: r4 = r0->field_b
    //     0xb94dd4: ldur            w4, [x0, #0xb]
    // 0xb94dd8: DecompressPointer r4
    //     0xb94dd8: add             x4, x4, HEAP, lsl #32
    // 0xb94ddc: r0 = LoadClassIdInstr(r4)
    //     0xb94ddc: ldur            x0, [x4, #-1]
    //     0xb94de0: ubfx            x0, x0, #0xc, #0x14
    // 0xb94de4: r16 = "direct_image"
    //     0xb94de4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xb94de8: ldr             x16, [x16, #0xc98]
    // 0xb94dec: stp             x16, x4, [SP]
    // 0xb94df0: mov             lr, x0
    // 0xb94df4: ldr             lr, [x21, lr, lsl #3]
    // 0xb94df8: blr             lr
    // 0xb94dfc: tbz             w0, #4, #0xb94f10
    // 0xb94e00: ldur            x1, [fp, #-0x10]
    // 0xb94e04: r0 = InkWell()
    //     0xb94e04: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb94e08: mov             x3, x0
    // 0xb94e0c: r0 = Instance_Icon
    //     0xb94e0c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51fb8] Obj!Icon@d665b1
    //     0xb94e10: ldr             x0, [x0, #0xfb8]
    // 0xb94e14: stur            x3, [fp, #-0x18]
    // 0xb94e18: StoreField: r3->field_b = r0
    //     0xb94e18: stur            w0, [x3, #0xb]
    // 0xb94e1c: r1 = Function '<anonymous closure>':.
    //     0xb94e1c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fc0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb94e20: ldr             x1, [x1, #0xfc0]
    // 0xb94e24: r2 = Null
    //     0xb94e24: mov             x2, NULL
    // 0xb94e28: r0 = AllocateClosure()
    //     0xb94e28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94e2c: mov             x1, x0
    // 0xb94e30: ldur            x0, [fp, #-0x18]
    // 0xb94e34: StoreField: r0->field_f = r1
    //     0xb94e34: stur            w1, [x0, #0xf]
    // 0xb94e38: r2 = true
    //     0xb94e38: add             x2, NULL, #0x20  ; true
    // 0xb94e3c: StoreField: r0->field_43 = r2
    //     0xb94e3c: stur            w2, [x0, #0x43]
    // 0xb94e40: r3 = Instance_BoxShape
    //     0xb94e40: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb94e44: ldr             x3, [x3, #0x80]
    // 0xb94e48: StoreField: r0->field_47 = r3
    //     0xb94e48: stur            w3, [x0, #0x47]
    // 0xb94e4c: StoreField: r0->field_6f = r2
    //     0xb94e4c: stur            w2, [x0, #0x6f]
    // 0xb94e50: r4 = false
    //     0xb94e50: add             x4, NULL, #0x30  ; false
    // 0xb94e54: StoreField: r0->field_73 = r4
    //     0xb94e54: stur            w4, [x0, #0x73]
    // 0xb94e58: StoreField: r0->field_83 = r2
    //     0xb94e58: stur            w2, [x0, #0x83]
    // 0xb94e5c: StoreField: r0->field_7b = r4
    //     0xb94e5c: stur            w4, [x0, #0x7b]
    // 0xb94e60: r1 = <StackParentData>
    //     0xb94e60: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb94e64: ldr             x1, [x1, #0x8e0]
    // 0xb94e68: r0 = Positioned()
    //     0xb94e68: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb94e6c: mov             x2, x0
    // 0xb94e70: r0 = 12.000000
    //     0xb94e70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb94e74: ldr             x0, [x0, #0x9e8]
    // 0xb94e78: stur            x2, [fp, #-0x28]
    // 0xb94e7c: StoreField: r2->field_13 = r0
    //     0xb94e7c: stur            w0, [x2, #0x13]
    // 0xb94e80: r3 = 24.000000
    //     0xb94e80: add             x3, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb94e84: ldr             x3, [x3, #0xba8]
    // 0xb94e88: ArrayStore: r2[0] = r3  ; List_4
    //     0xb94e88: stur            w3, [x2, #0x17]
    // 0xb94e8c: ldur            x1, [fp, #-0x18]
    // 0xb94e90: StoreField: r2->field_b = r1
    //     0xb94e90: stur            w1, [x2, #0xb]
    // 0xb94e94: ldur            x4, [fp, #-0x10]
    // 0xb94e98: LoadField: r1 = r4->field_b
    //     0xb94e98: ldur            w1, [x4, #0xb]
    // 0xb94e9c: LoadField: r5 = r4->field_f
    //     0xb94e9c: ldur            w5, [x4, #0xf]
    // 0xb94ea0: DecompressPointer r5
    //     0xb94ea0: add             x5, x5, HEAP, lsl #32
    // 0xb94ea4: LoadField: r6 = r5->field_b
    //     0xb94ea4: ldur            w6, [x5, #0xb]
    // 0xb94ea8: r5 = LoadInt32Instr(r1)
    //     0xb94ea8: sbfx            x5, x1, #1, #0x1f
    // 0xb94eac: stur            x5, [fp, #-0x40]
    // 0xb94eb0: r1 = LoadInt32Instr(r6)
    //     0xb94eb0: sbfx            x1, x6, #1, #0x1f
    // 0xb94eb4: cmp             x5, x1
    // 0xb94eb8: b.ne            #0xb94ec4
    // 0xb94ebc: mov             x1, x4
    // 0xb94ec0: r0 = _growToNextCapacity()
    //     0xb94ec0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb94ec4: ldur            x2, [fp, #-0x10]
    // 0xb94ec8: ldur            x3, [fp, #-0x40]
    // 0xb94ecc: add             x0, x3, #1
    // 0xb94ed0: lsl             x1, x0, #1
    // 0xb94ed4: StoreField: r2->field_b = r1
    //     0xb94ed4: stur            w1, [x2, #0xb]
    // 0xb94ed8: LoadField: r1 = r2->field_f
    //     0xb94ed8: ldur            w1, [x2, #0xf]
    // 0xb94edc: DecompressPointer r1
    //     0xb94edc: add             x1, x1, HEAP, lsl #32
    // 0xb94ee0: ldur            x0, [fp, #-0x28]
    // 0xb94ee4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb94ee4: add             x25, x1, x3, lsl #2
    //     0xb94ee8: add             x25, x25, #0xf
    //     0xb94eec: str             w0, [x25]
    //     0xb94ef0: tbz             w0, #0, #0xb94f0c
    //     0xb94ef4: ldurb           w16, [x1, #-1]
    //     0xb94ef8: ldurb           w17, [x0, #-1]
    //     0xb94efc: and             x16, x17, x16, lsr #2
    //     0xb94f00: tst             x16, HEAP, lsr #32
    //     0xb94f04: b.eq            #0xb94f0c
    //     0xb94f08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb94f0c: b               #0xb94f14
    // 0xb94f10: ldur            x2, [fp, #-0x10]
    // 0xb94f14: r0 = InkWell()
    //     0xb94f14: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb94f18: mov             x3, x0
    // 0xb94f1c: r0 = Instance_Icon
    //     0xb94f1c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51fc8] Obj!Icon@d66571
    //     0xb94f20: ldr             x0, [x0, #0xfc8]
    // 0xb94f24: stur            x3, [fp, #-0x18]
    // 0xb94f28: StoreField: r3->field_b = r0
    //     0xb94f28: stur            w0, [x3, #0xb]
    // 0xb94f2c: ldur            x2, [fp, #-0x20]
    // 0xb94f30: r1 = Function '<anonymous closure>':.
    //     0xb94f30: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fc8] AnonymousClosure: (0xaa54dc), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb94f34: ldr             x1, [x1, #0xfc8]
    // 0xb94f38: r0 = AllocateClosure()
    //     0xb94f38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94f3c: mov             x1, x0
    // 0xb94f40: ldur            x0, [fp, #-0x18]
    // 0xb94f44: StoreField: r0->field_f = r1
    //     0xb94f44: stur            w1, [x0, #0xf]
    // 0xb94f48: r2 = true
    //     0xb94f48: add             x2, NULL, #0x20  ; true
    // 0xb94f4c: StoreField: r0->field_43 = r2
    //     0xb94f4c: stur            w2, [x0, #0x43]
    // 0xb94f50: r3 = Instance_BoxShape
    //     0xb94f50: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb94f54: ldr             x3, [x3, #0x80]
    // 0xb94f58: StoreField: r0->field_47 = r3
    //     0xb94f58: stur            w3, [x0, #0x47]
    // 0xb94f5c: StoreField: r0->field_6f = r2
    //     0xb94f5c: stur            w2, [x0, #0x6f]
    // 0xb94f60: r4 = false
    //     0xb94f60: add             x4, NULL, #0x30  ; false
    // 0xb94f64: StoreField: r0->field_73 = r4
    //     0xb94f64: stur            w4, [x0, #0x73]
    // 0xb94f68: StoreField: r0->field_83 = r2
    //     0xb94f68: stur            w2, [x0, #0x83]
    // 0xb94f6c: StoreField: r0->field_7b = r4
    //     0xb94f6c: stur            w4, [x0, #0x7b]
    // 0xb94f70: r1 = <StackParentData>
    //     0xb94f70: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb94f74: ldr             x1, [x1, #0x8e0]
    // 0xb94f78: r0 = Positioned()
    //     0xb94f78: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb94f7c: mov             x2, x0
    // 0xb94f80: r0 = 24.000000
    //     0xb94f80: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb94f84: ldr             x0, [x0, #0xba8]
    // 0xb94f88: stur            x2, [fp, #-0x28]
    // 0xb94f8c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb94f8c: stur            w0, [x2, #0x17]
    // 0xb94f90: r0 = 12.000000
    //     0xb94f90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb94f94: ldr             x0, [x0, #0x9e8]
    // 0xb94f98: StoreField: r2->field_1b = r0
    //     0xb94f98: stur            w0, [x2, #0x1b]
    // 0xb94f9c: ldur            x0, [fp, #-0x18]
    // 0xb94fa0: StoreField: r2->field_b = r0
    //     0xb94fa0: stur            w0, [x2, #0xb]
    // 0xb94fa4: ldur            x0, [fp, #-0x10]
    // 0xb94fa8: LoadField: r1 = r0->field_b
    //     0xb94fa8: ldur            w1, [x0, #0xb]
    // 0xb94fac: LoadField: r3 = r0->field_f
    //     0xb94fac: ldur            w3, [x0, #0xf]
    // 0xb94fb0: DecompressPointer r3
    //     0xb94fb0: add             x3, x3, HEAP, lsl #32
    // 0xb94fb4: LoadField: r4 = r3->field_b
    //     0xb94fb4: ldur            w4, [x3, #0xb]
    // 0xb94fb8: r3 = LoadInt32Instr(r1)
    //     0xb94fb8: sbfx            x3, x1, #1, #0x1f
    // 0xb94fbc: stur            x3, [fp, #-0x40]
    // 0xb94fc0: r1 = LoadInt32Instr(r4)
    //     0xb94fc0: sbfx            x1, x4, #1, #0x1f
    // 0xb94fc4: cmp             x3, x1
    // 0xb94fc8: b.ne            #0xb94fd4
    // 0xb94fcc: mov             x1, x0
    // 0xb94fd0: r0 = _growToNextCapacity()
    //     0xb94fd0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb94fd4: ldur            x4, [fp, #-8]
    // 0xb94fd8: ldur            x2, [fp, #-0x10]
    // 0xb94fdc: ldur            x3, [fp, #-0x40]
    // 0xb94fe0: add             x0, x3, #1
    // 0xb94fe4: lsl             x1, x0, #1
    // 0xb94fe8: StoreField: r2->field_b = r1
    //     0xb94fe8: stur            w1, [x2, #0xb]
    // 0xb94fec: LoadField: r1 = r2->field_f
    //     0xb94fec: ldur            w1, [x2, #0xf]
    // 0xb94ff0: DecompressPointer r1
    //     0xb94ff0: add             x1, x1, HEAP, lsl #32
    // 0xb94ff4: ldur            x0, [fp, #-0x28]
    // 0xb94ff8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb94ff8: add             x25, x1, x3, lsl #2
    //     0xb94ffc: add             x25, x25, #0xf
    //     0xb95000: str             w0, [x25]
    //     0xb95004: tbz             w0, #0, #0xb95020
    //     0xb95008: ldurb           w16, [x1, #-1]
    //     0xb9500c: ldurb           w17, [x0, #-1]
    //     0xb95010: and             x16, x17, x16, lsr #2
    //     0xb95014: tst             x16, HEAP, lsr #32
    //     0xb95018: b.eq            #0xb95020
    //     0xb9501c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb95020: LoadField: r0 = r4->field_2b
    //     0xb95020: ldur            w0, [x4, #0x2b]
    // 0xb95024: DecompressPointer r0
    //     0xb95024: add             x0, x0, HEAP, lsl #32
    // 0xb95028: tbnz            w0, #4, #0xb95b44
    // 0xb9502c: ldur            x0, [fp, #-0x20]
    // 0xb95030: LoadField: r1 = r0->field_13
    //     0xb95030: ldur            w1, [x0, #0x13]
    // 0xb95034: DecompressPointer r1
    //     0xb95034: add             x1, x1, HEAP, lsl #32
    // 0xb95038: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb95038: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb9503c: r0 = _of()
    //     0xb9503c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb95040: LoadField: r1 = r0->field_7
    //     0xb95040: ldur            w1, [x0, #7]
    // 0xb95044: DecompressPointer r1
    //     0xb95044: add             x1, x1, HEAP, lsl #32
    // 0xb95048: LoadField: d1 = r1->field_7
    //     0xb95048: ldur            d1, [x1, #7]
    // 0xb9504c: stur            d1, [fp, #-0x58]
    // 0xb95050: r1 = Instance_Color
    //     0xb95050: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb95054: d0 = 0.700000
    //     0xb95054: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb95058: ldr             d0, [x17, #0xf48]
    // 0xb9505c: r0 = withOpacity()
    //     0xb9505c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb95060: stur            x0, [fp, #-0x18]
    // 0xb95064: r0 = BoxDecoration()
    //     0xb95064: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb95068: mov             x2, x0
    // 0xb9506c: ldur            x0, [fp, #-0x18]
    // 0xb95070: stur            x2, [fp, #-0x28]
    // 0xb95074: StoreField: r2->field_7 = r0
    //     0xb95074: stur            w0, [x2, #7]
    // 0xb95078: r0 = Instance_BorderRadius
    //     0xb95078: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] Obj!BorderRadius@d5a201
    //     0xb9507c: ldr             x0, [x0, #0xbf8]
    // 0xb95080: StoreField: r2->field_13 = r0
    //     0xb95080: stur            w0, [x2, #0x13]
    // 0xb95084: r0 = Instance_BoxShape
    //     0xb95084: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb95088: ldr             x0, [x0, #0x80]
    // 0xb9508c: StoreField: r2->field_23 = r0
    //     0xb9508c: stur            w0, [x2, #0x23]
    // 0xb95090: r1 = Instance_Color
    //     0xb95090: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb95094: d0 = 0.500000
    //     0xb95094: fmov            d0, #0.50000000
    // 0xb95098: r0 = withOpacity()
    //     0xb95098: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9509c: stur            x0, [fp, #-0x18]
    // 0xb950a0: r0 = BoxDecoration()
    //     0xb950a0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb950a4: mov             x2, x0
    // 0xb950a8: ldur            x0, [fp, #-0x18]
    // 0xb950ac: stur            x2, [fp, #-0x30]
    // 0xb950b0: StoreField: r2->field_7 = r0
    //     0xb950b0: stur            w0, [x2, #7]
    // 0xb950b4: r3 = Instance_BoxShape
    //     0xb950b4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb950b8: ldr             x3, [x3, #0x970]
    // 0xb950bc: StoreField: r2->field_23 = r3
    //     0xb950bc: stur            w3, [x2, #0x23]
    // 0xb950c0: ldur            x4, [fp, #-8]
    // 0xb950c4: LoadField: r0 = r4->field_b
    //     0xb950c4: ldur            w0, [x4, #0xb]
    // 0xb950c8: DecompressPointer r0
    //     0xb950c8: add             x0, x0, HEAP, lsl #32
    // 0xb950cc: cmp             w0, NULL
    // 0xb950d0: b.eq            #0xb96b60
    // 0xb950d4: LoadField: r5 = r0->field_f
    //     0xb950d4: ldur            w5, [x0, #0xf]
    // 0xb950d8: DecompressPointer r5
    //     0xb950d8: add             x5, x5, HEAP, lsl #32
    // 0xb950dc: LoadField: r6 = r4->field_13
    //     0xb950dc: ldur            x6, [x4, #0x13]
    // 0xb950e0: LoadField: r0 = r5->field_b
    //     0xb950e0: ldur            w0, [x5, #0xb]
    // 0xb950e4: r1 = LoadInt32Instr(r0)
    //     0xb950e4: sbfx            x1, x0, #1, #0x1f
    // 0xb950e8: mov             x0, x1
    // 0xb950ec: mov             x1, x6
    // 0xb950f0: cmp             x1, x0
    // 0xb950f4: b.hs            #0xb96b64
    // 0xb950f8: LoadField: r0 = r5->field_f
    //     0xb950f8: ldur            w0, [x5, #0xf]
    // 0xb950fc: DecompressPointer r0
    //     0xb950fc: add             x0, x0, HEAP, lsl #32
    // 0xb95100: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb95100: add             x16, x0, x6, lsl #2
    //     0xb95104: ldur            w1, [x16, #0xf]
    // 0xb95108: DecompressPointer r1
    //     0xb95108: add             x1, x1, HEAP, lsl #32
    // 0xb9510c: LoadField: r0 = r1->field_7
    //     0xb9510c: ldur            w0, [x1, #7]
    // 0xb95110: DecompressPointer r0
    //     0xb95110: add             x0, x0, HEAP, lsl #32
    // 0xb95114: cmp             w0, NULL
    // 0xb95118: b.ne            #0xb95124
    // 0xb9511c: r0 = Null
    //     0xb9511c: mov             x0, NULL
    // 0xb95120: b               #0xb95148
    // 0xb95124: stp             xzr, x0, [SP]
    // 0xb95128: r0 = []()
    //     0xb95128: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb9512c: r1 = LoadClassIdInstr(r0)
    //     0xb9512c: ldur            x1, [x0, #-1]
    //     0xb95130: ubfx            x1, x1, #0xc, #0x14
    // 0xb95134: str             x0, [SP]
    // 0xb95138: mov             x0, x1
    // 0xb9513c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb9513c: sub             lr, x0, #1, lsl #12
    //     0xb95140: ldr             lr, [x21, lr, lsl #3]
    //     0xb95144: blr             lr
    // 0xb95148: cmp             w0, NULL
    // 0xb9514c: b.ne            #0xb95158
    // 0xb95150: r3 = ""
    //     0xb95150: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb95154: b               #0xb9515c
    // 0xb95158: mov             x3, x0
    // 0xb9515c: ldur            x0, [fp, #-8]
    // 0xb95160: ldur            x2, [fp, #-0x20]
    // 0xb95164: stur            x3, [fp, #-0x18]
    // 0xb95168: LoadField: r1 = r2->field_13
    //     0xb95168: ldur            w1, [x2, #0x13]
    // 0xb9516c: DecompressPointer r1
    //     0xb9516c: add             x1, x1, HEAP, lsl #32
    // 0xb95170: r0 = of()
    //     0xb95170: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb95174: LoadField: r1 = r0->field_87
    //     0xb95174: ldur            w1, [x0, #0x87]
    // 0xb95178: DecompressPointer r1
    //     0xb95178: add             x1, x1, HEAP, lsl #32
    // 0xb9517c: LoadField: r0 = r1->field_7
    //     0xb9517c: ldur            w0, [x1, #7]
    // 0xb95180: DecompressPointer r0
    //     0xb95180: add             x0, x0, HEAP, lsl #32
    // 0xb95184: r16 = 16.000000
    //     0xb95184: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb95188: ldr             x16, [x16, #0x188]
    // 0xb9518c: r30 = Instance_Color
    //     0xb9518c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95190: stp             lr, x16, [SP]
    // 0xb95194: mov             x1, x0
    // 0xb95198: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb95198: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9519c: ldr             x4, [x4, #0xaa0]
    // 0xb951a0: r0 = copyWith()
    //     0xb951a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb951a4: stur            x0, [fp, #-0x38]
    // 0xb951a8: r0 = Text()
    //     0xb951a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb951ac: mov             x1, x0
    // 0xb951b0: ldur            x0, [fp, #-0x18]
    // 0xb951b4: stur            x1, [fp, #-0x48]
    // 0xb951b8: StoreField: r1->field_b = r0
    //     0xb951b8: stur            w0, [x1, #0xb]
    // 0xb951bc: ldur            x0, [fp, #-0x38]
    // 0xb951c0: StoreField: r1->field_13 = r0
    //     0xb951c0: stur            w0, [x1, #0x13]
    // 0xb951c4: r0 = Center()
    //     0xb951c4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb951c8: mov             x1, x0
    // 0xb951cc: r0 = Instance_Alignment
    //     0xb951cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb951d0: ldr             x0, [x0, #0xb10]
    // 0xb951d4: stur            x1, [fp, #-0x18]
    // 0xb951d8: StoreField: r1->field_f = r0
    //     0xb951d8: stur            w0, [x1, #0xf]
    // 0xb951dc: ldur            x2, [fp, #-0x48]
    // 0xb951e0: StoreField: r1->field_b = r2
    //     0xb951e0: stur            w2, [x1, #0xb]
    // 0xb951e4: r0 = Container()
    //     0xb951e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb951e8: stur            x0, [fp, #-0x38]
    // 0xb951ec: r16 = 34.000000
    //     0xb951ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb951f0: ldr             x16, [x16, #0x978]
    // 0xb951f4: r30 = 34.000000
    //     0xb951f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb951f8: ldr             lr, [lr, #0x978]
    // 0xb951fc: stp             lr, x16, [SP, #0x18]
    // 0xb95200: r16 = Instance_EdgeInsets
    //     0xb95200: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb95204: ldr             x16, [x16, #0x980]
    // 0xb95208: ldur            lr, [fp, #-0x30]
    // 0xb9520c: stp             lr, x16, [SP, #8]
    // 0xb95210: ldur            x16, [fp, #-0x18]
    // 0xb95214: str             x16, [SP]
    // 0xb95218: mov             x1, x0
    // 0xb9521c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb9521c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb95220: ldr             x4, [x4, #0x988]
    // 0xb95224: r0 = Container()
    //     0xb95224: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb95228: ldur            x2, [fp, #-8]
    // 0xb9522c: LoadField: r0 = r2->field_b
    //     0xb9522c: ldur            w0, [x2, #0xb]
    // 0xb95230: DecompressPointer r0
    //     0xb95230: add             x0, x0, HEAP, lsl #32
    // 0xb95234: cmp             w0, NULL
    // 0xb95238: b.eq            #0xb96b68
    // 0xb9523c: LoadField: r3 = r0->field_f
    //     0xb9523c: ldur            w3, [x0, #0xf]
    // 0xb95240: DecompressPointer r3
    //     0xb95240: add             x3, x3, HEAP, lsl #32
    // 0xb95244: LoadField: r4 = r2->field_13
    //     0xb95244: ldur            x4, [x2, #0x13]
    // 0xb95248: LoadField: r0 = r3->field_b
    //     0xb95248: ldur            w0, [x3, #0xb]
    // 0xb9524c: r1 = LoadInt32Instr(r0)
    //     0xb9524c: sbfx            x1, x0, #1, #0x1f
    // 0xb95250: mov             x0, x1
    // 0xb95254: mov             x1, x4
    // 0xb95258: cmp             x1, x0
    // 0xb9525c: b.hs            #0xb96b6c
    // 0xb95260: LoadField: r0 = r3->field_f
    //     0xb95260: ldur            w0, [x3, #0xf]
    // 0xb95264: DecompressPointer r0
    //     0xb95264: add             x0, x0, HEAP, lsl #32
    // 0xb95268: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb95268: add             x16, x0, x4, lsl #2
    //     0xb9526c: ldur            w1, [x16, #0xf]
    // 0xb95270: DecompressPointer r1
    //     0xb95270: add             x1, x1, HEAP, lsl #32
    // 0xb95274: LoadField: r0 = r1->field_7
    //     0xb95274: ldur            w0, [x1, #7]
    // 0xb95278: DecompressPointer r0
    //     0xb95278: add             x0, x0, HEAP, lsl #32
    // 0xb9527c: cmp             w0, NULL
    // 0xb95280: b.ne            #0xb9528c
    // 0xb95284: r3 = ""
    //     0xb95284: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb95288: b               #0xb95290
    // 0xb9528c: mov             x3, x0
    // 0xb95290: ldur            x0, [fp, #-0x20]
    // 0xb95294: stur            x3, [fp, #-0x18]
    // 0xb95298: LoadField: r1 = r0->field_13
    //     0xb95298: ldur            w1, [x0, #0x13]
    // 0xb9529c: DecompressPointer r1
    //     0xb9529c: add             x1, x1, HEAP, lsl #32
    // 0xb952a0: r0 = of()
    //     0xb952a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb952a4: LoadField: r1 = r0->field_87
    //     0xb952a4: ldur            w1, [x0, #0x87]
    // 0xb952a8: DecompressPointer r1
    //     0xb952a8: add             x1, x1, HEAP, lsl #32
    // 0xb952ac: LoadField: r0 = r1->field_7
    //     0xb952ac: ldur            w0, [x1, #7]
    // 0xb952b0: DecompressPointer r0
    //     0xb952b0: add             x0, x0, HEAP, lsl #32
    // 0xb952b4: r16 = 14.000000
    //     0xb952b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb952b8: ldr             x16, [x16, #0x1d8]
    // 0xb952bc: r30 = Instance_Color
    //     0xb952bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb952c0: stp             lr, x16, [SP]
    // 0xb952c4: mov             x1, x0
    // 0xb952c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb952c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb952cc: ldr             x4, [x4, #0xaa0]
    // 0xb952d0: r0 = copyWith()
    //     0xb952d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb952d4: stur            x0, [fp, #-0x30]
    // 0xb952d8: r0 = Text()
    //     0xb952d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb952dc: mov             x2, x0
    // 0xb952e0: ldur            x0, [fp, #-0x18]
    // 0xb952e4: stur            x2, [fp, #-0x48]
    // 0xb952e8: StoreField: r2->field_b = r0
    //     0xb952e8: stur            w0, [x2, #0xb]
    // 0xb952ec: ldur            x0, [fp, #-0x30]
    // 0xb952f0: StoreField: r2->field_13 = r0
    //     0xb952f0: stur            w0, [x2, #0x13]
    // 0xb952f4: ldur            x3, [fp, #-8]
    // 0xb952f8: LoadField: r0 = r3->field_b
    //     0xb952f8: ldur            w0, [x3, #0xb]
    // 0xb952fc: DecompressPointer r0
    //     0xb952fc: add             x0, x0, HEAP, lsl #32
    // 0xb95300: cmp             w0, NULL
    // 0xb95304: b.eq            #0xb96b70
    // 0xb95308: LoadField: r4 = r0->field_f
    //     0xb95308: ldur            w4, [x0, #0xf]
    // 0xb9530c: DecompressPointer r4
    //     0xb9530c: add             x4, x4, HEAP, lsl #32
    // 0xb95310: LoadField: r5 = r3->field_13
    //     0xb95310: ldur            x5, [x3, #0x13]
    // 0xb95314: LoadField: r0 = r4->field_b
    //     0xb95314: ldur            w0, [x4, #0xb]
    // 0xb95318: r1 = LoadInt32Instr(r0)
    //     0xb95318: sbfx            x1, x0, #1, #0x1f
    // 0xb9531c: mov             x0, x1
    // 0xb95320: mov             x1, x5
    // 0xb95324: cmp             x1, x0
    // 0xb95328: b.hs            #0xb96b74
    // 0xb9532c: LoadField: r0 = r4->field_f
    //     0xb9532c: ldur            w0, [x4, #0xf]
    // 0xb95330: DecompressPointer r0
    //     0xb95330: add             x0, x0, HEAP, lsl #32
    // 0xb95334: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb95334: add             x16, x0, x5, lsl #2
    //     0xb95338: ldur            w1, [x16, #0xf]
    // 0xb9533c: DecompressPointer r1
    //     0xb9533c: add             x1, x1, HEAP, lsl #32
    // 0xb95340: LoadField: r0 = r1->field_1f
    //     0xb95340: ldur            w0, [x1, #0x1f]
    // 0xb95344: DecompressPointer r0
    //     0xb95344: add             x0, x0, HEAP, lsl #32
    // 0xb95348: cmp             w0, NULL
    // 0xb9534c: b.ne            #0xb95358
    // 0xb95350: r4 = ""
    //     0xb95350: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb95354: b               #0xb9535c
    // 0xb95358: mov             x4, x0
    // 0xb9535c: ldur            x0, [fp, #-0x20]
    // 0xb95360: stur            x4, [fp, #-0x18]
    // 0xb95364: LoadField: r1 = r0->field_13
    //     0xb95364: ldur            w1, [x0, #0x13]
    // 0xb95368: DecompressPointer r1
    //     0xb95368: add             x1, x1, HEAP, lsl #32
    // 0xb9536c: r0 = of()
    //     0xb9536c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb95370: LoadField: r1 = r0->field_87
    //     0xb95370: ldur            w1, [x0, #0x87]
    // 0xb95374: DecompressPointer r1
    //     0xb95374: add             x1, x1, HEAP, lsl #32
    // 0xb95378: LoadField: r0 = r1->field_33
    //     0xb95378: ldur            w0, [x1, #0x33]
    // 0xb9537c: DecompressPointer r0
    //     0xb9537c: add             x0, x0, HEAP, lsl #32
    // 0xb95380: stur            x0, [fp, #-0x30]
    // 0xb95384: cmp             w0, NULL
    // 0xb95388: b.ne            #0xb95394
    // 0xb9538c: r4 = Null
    //     0xb9538c: mov             x4, NULL
    // 0xb95390: b               #0xb953bc
    // 0xb95394: r1 = Instance_Color
    //     0xb95394: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95398: d0 = 0.500000
    //     0xb95398: fmov            d0, #0.50000000
    // 0xb9539c: r0 = withOpacity()
    //     0xb9539c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb953a0: r16 = 10.000000
    //     0xb953a0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb953a4: stp             x0, x16, [SP]
    // 0xb953a8: ldur            x1, [fp, #-0x30]
    // 0xb953ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb953ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb953b0: ldr             x4, [x4, #0xaa0]
    // 0xb953b4: r0 = copyWith()
    //     0xb953b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb953b8: mov             x4, x0
    // 0xb953bc: ldur            x1, [fp, #-8]
    // 0xb953c0: ldur            x3, [fp, #-0x38]
    // 0xb953c4: ldur            x0, [fp, #-0x48]
    // 0xb953c8: ldur            x2, [fp, #-0x18]
    // 0xb953cc: stur            x4, [fp, #-0x30]
    // 0xb953d0: r0 = Text()
    //     0xb953d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb953d4: mov             x1, x0
    // 0xb953d8: ldur            x0, [fp, #-0x18]
    // 0xb953dc: stur            x1, [fp, #-0x50]
    // 0xb953e0: StoreField: r1->field_b = r0
    //     0xb953e0: stur            w0, [x1, #0xb]
    // 0xb953e4: ldur            x0, [fp, #-0x30]
    // 0xb953e8: StoreField: r1->field_13 = r0
    //     0xb953e8: stur            w0, [x1, #0x13]
    // 0xb953ec: r0 = Padding()
    //     0xb953ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb953f0: mov             x3, x0
    // 0xb953f4: r0 = Instance_EdgeInsets
    //     0xb953f4: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb953f8: ldr             x0, [x0, #0xe90]
    // 0xb953fc: stur            x3, [fp, #-0x18]
    // 0xb95400: StoreField: r3->field_f = r0
    //     0xb95400: stur            w0, [x3, #0xf]
    // 0xb95404: ldur            x1, [fp, #-0x50]
    // 0xb95408: StoreField: r3->field_b = r1
    //     0xb95408: stur            w1, [x3, #0xb]
    // 0xb9540c: r1 = Null
    //     0xb9540c: mov             x1, NULL
    // 0xb95410: r2 = 4
    //     0xb95410: movz            x2, #0x4
    // 0xb95414: r0 = AllocateArray()
    //     0xb95414: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb95418: mov             x2, x0
    // 0xb9541c: ldur            x0, [fp, #-0x48]
    // 0xb95420: stur            x2, [fp, #-0x30]
    // 0xb95424: StoreField: r2->field_f = r0
    //     0xb95424: stur            w0, [x2, #0xf]
    // 0xb95428: ldur            x0, [fp, #-0x18]
    // 0xb9542c: StoreField: r2->field_13 = r0
    //     0xb9542c: stur            w0, [x2, #0x13]
    // 0xb95430: r1 = <Widget>
    //     0xb95430: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb95434: r0 = AllocateGrowableArray()
    //     0xb95434: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb95438: mov             x1, x0
    // 0xb9543c: ldur            x0, [fp, #-0x30]
    // 0xb95440: stur            x1, [fp, #-0x18]
    // 0xb95444: StoreField: r1->field_f = r0
    //     0xb95444: stur            w0, [x1, #0xf]
    // 0xb95448: r2 = 4
    //     0xb95448: movz            x2, #0x4
    // 0xb9544c: StoreField: r1->field_b = r2
    //     0xb9544c: stur            w2, [x1, #0xb]
    // 0xb95450: r0 = Column()
    //     0xb95450: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb95454: mov             x3, x0
    // 0xb95458: r0 = Instance_Axis
    //     0xb95458: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9545c: stur            x3, [fp, #-0x30]
    // 0xb95460: StoreField: r3->field_f = r0
    //     0xb95460: stur            w0, [x3, #0xf]
    // 0xb95464: r4 = Instance_MainAxisAlignment
    //     0xb95464: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb95468: ldr             x4, [x4, #0xa08]
    // 0xb9546c: StoreField: r3->field_13 = r4
    //     0xb9546c: stur            w4, [x3, #0x13]
    // 0xb95470: r5 = Instance_MainAxisSize
    //     0xb95470: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb95474: ldr             x5, [x5, #0xa10]
    // 0xb95478: ArrayStore: r3[0] = r5  ; List_4
    //     0xb95478: stur            w5, [x3, #0x17]
    // 0xb9547c: r6 = Instance_CrossAxisAlignment
    //     0xb9547c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb95480: ldr             x6, [x6, #0x890]
    // 0xb95484: StoreField: r3->field_1b = r6
    //     0xb95484: stur            w6, [x3, #0x1b]
    // 0xb95488: r7 = Instance_VerticalDirection
    //     0xb95488: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9548c: ldr             x7, [x7, #0xa20]
    // 0xb95490: StoreField: r3->field_23 = r7
    //     0xb95490: stur            w7, [x3, #0x23]
    // 0xb95494: r8 = Instance_Clip
    //     0xb95494: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb95498: ldr             x8, [x8, #0x38]
    // 0xb9549c: StoreField: r3->field_2b = r8
    //     0xb9549c: stur            w8, [x3, #0x2b]
    // 0xb954a0: StoreField: r3->field_2f = rZR
    //     0xb954a0: stur            xzr, [x3, #0x2f]
    // 0xb954a4: ldur            x1, [fp, #-0x18]
    // 0xb954a8: StoreField: r3->field_b = r1
    //     0xb954a8: stur            w1, [x3, #0xb]
    // 0xb954ac: r1 = Null
    //     0xb954ac: mov             x1, NULL
    // 0xb954b0: r2 = 6
    //     0xb954b0: movz            x2, #0x6
    // 0xb954b4: r0 = AllocateArray()
    //     0xb954b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb954b8: mov             x2, x0
    // 0xb954bc: ldur            x0, [fp, #-0x38]
    // 0xb954c0: stur            x2, [fp, #-0x18]
    // 0xb954c4: StoreField: r2->field_f = r0
    //     0xb954c4: stur            w0, [x2, #0xf]
    // 0xb954c8: r16 = Instance_SizedBox
    //     0xb954c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb954cc: ldr             x16, [x16, #0x998]
    // 0xb954d0: StoreField: r2->field_13 = r16
    //     0xb954d0: stur            w16, [x2, #0x13]
    // 0xb954d4: ldur            x0, [fp, #-0x30]
    // 0xb954d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb954d8: stur            w0, [x2, #0x17]
    // 0xb954dc: r1 = <Widget>
    //     0xb954dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb954e0: r0 = AllocateGrowableArray()
    //     0xb954e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb954e4: mov             x1, x0
    // 0xb954e8: ldur            x0, [fp, #-0x18]
    // 0xb954ec: stur            x1, [fp, #-0x30]
    // 0xb954f0: StoreField: r1->field_f = r0
    //     0xb954f0: stur            w0, [x1, #0xf]
    // 0xb954f4: r2 = 6
    //     0xb954f4: movz            x2, #0x6
    // 0xb954f8: StoreField: r1->field_b = r2
    //     0xb954f8: stur            w2, [x1, #0xb]
    // 0xb954fc: r0 = Row()
    //     0xb954fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb95500: mov             x3, x0
    // 0xb95504: r2 = Instance_Axis
    //     0xb95504: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb95508: stur            x3, [fp, #-0x18]
    // 0xb9550c: StoreField: r3->field_f = r2
    //     0xb9550c: stur            w2, [x3, #0xf]
    // 0xb95510: r4 = Instance_MainAxisAlignment
    //     0xb95510: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb95514: ldr             x4, [x4, #0xa08]
    // 0xb95518: StoreField: r3->field_13 = r4
    //     0xb95518: stur            w4, [x3, #0x13]
    // 0xb9551c: r5 = Instance_MainAxisSize
    //     0xb9551c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb95520: ldr             x5, [x5, #0xa10]
    // 0xb95524: ArrayStore: r3[0] = r5  ; List_4
    //     0xb95524: stur            w5, [x3, #0x17]
    // 0xb95528: r6 = Instance_CrossAxisAlignment
    //     0xb95528: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9552c: ldr             x6, [x6, #0xa18]
    // 0xb95530: StoreField: r3->field_1b = r6
    //     0xb95530: stur            w6, [x3, #0x1b]
    // 0xb95534: r7 = Instance_VerticalDirection
    //     0xb95534: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb95538: ldr             x7, [x7, #0xa20]
    // 0xb9553c: StoreField: r3->field_23 = r7
    //     0xb9553c: stur            w7, [x3, #0x23]
    // 0xb95540: r8 = Instance_Clip
    //     0xb95540: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb95544: ldr             x8, [x8, #0x38]
    // 0xb95548: StoreField: r3->field_2b = r8
    //     0xb95548: stur            w8, [x3, #0x2b]
    // 0xb9554c: StoreField: r3->field_2f = rZR
    //     0xb9554c: stur            xzr, [x3, #0x2f]
    // 0xb95550: ldur            x0, [fp, #-0x30]
    // 0xb95554: StoreField: r3->field_b = r0
    //     0xb95554: stur            w0, [x3, #0xb]
    // 0xb95558: ldur            x9, [fp, #-8]
    // 0xb9555c: LoadField: r0 = r9->field_b
    //     0xb9555c: ldur            w0, [x9, #0xb]
    // 0xb95560: DecompressPointer r0
    //     0xb95560: add             x0, x0, HEAP, lsl #32
    // 0xb95564: cmp             w0, NULL
    // 0xb95568: b.eq            #0xb96b78
    // 0xb9556c: LoadField: r10 = r0->field_f
    //     0xb9556c: ldur            w10, [x0, #0xf]
    // 0xb95570: DecompressPointer r10
    //     0xb95570: add             x10, x10, HEAP, lsl #32
    // 0xb95574: LoadField: r11 = r9->field_13
    //     0xb95574: ldur            x11, [x9, #0x13]
    // 0xb95578: LoadField: r0 = r10->field_b
    //     0xb95578: ldur            w0, [x10, #0xb]
    // 0xb9557c: r1 = LoadInt32Instr(r0)
    //     0xb9557c: sbfx            x1, x0, #1, #0x1f
    // 0xb95580: mov             x0, x1
    // 0xb95584: mov             x1, x11
    // 0xb95588: cmp             x1, x0
    // 0xb9558c: b.hs            #0xb96b7c
    // 0xb95590: LoadField: r0 = r10->field_f
    //     0xb95590: ldur            w0, [x10, #0xf]
    // 0xb95594: DecompressPointer r0
    //     0xb95594: add             x0, x0, HEAP, lsl #32
    // 0xb95598: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xb95598: add             x16, x0, x11, lsl #2
    //     0xb9559c: ldur            w1, [x16, #0xf]
    // 0xb955a0: DecompressPointer r1
    //     0xb955a0: add             x1, x1, HEAP, lsl #32
    // 0xb955a4: LoadField: r0 = r1->field_f
    //     0xb955a4: ldur            w0, [x1, #0xf]
    // 0xb955a8: DecompressPointer r0
    //     0xb955a8: add             x0, x0, HEAP, lsl #32
    // 0xb955ac: r1 = 60
    //     0xb955ac: movz            x1, #0x3c
    // 0xb955b0: branchIfSmi(r0, 0xb955bc)
    //     0xb955b0: tbz             w0, #0, #0xb955bc
    // 0xb955b4: r1 = LoadClassIdInstr(r0)
    //     0xb955b4: ldur            x1, [x0, #-1]
    //     0xb955b8: ubfx            x1, x1, #0xc, #0x14
    // 0xb955bc: str             x0, [SP]
    // 0xb955c0: mov             x0, x1
    // 0xb955c4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb955c4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb955c8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb955c8: movz            x17, #0x2700
    //     0xb955cc: add             lr, x0, x17
    //     0xb955d0: ldr             lr, [x21, lr, lsl #3]
    //     0xb955d4: blr             lr
    // 0xb955d8: mov             x1, x0
    // 0xb955dc: r0 = parse()
    //     0xb955dc: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb955e0: mov             v1.16b, v0.16b
    // 0xb955e4: d0 = 4.000000
    //     0xb955e4: fmov            d0, #4.00000000
    // 0xb955e8: fcmp            d1, d0
    // 0xb955ec: b.lt            #0xb95600
    // 0xb955f0: r4 = Instance_Color
    //     0xb955f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb955f4: ldr             x4, [x4, #0x858]
    // 0xb955f8: d0 = 2.000000
    //     0xb955f8: fmov            d0, #2.00000000
    // 0xb955fc: b               #0xb95760
    // 0xb95600: ldur            x2, [fp, #-8]
    // 0xb95604: LoadField: r0 = r2->field_b
    //     0xb95604: ldur            w0, [x2, #0xb]
    // 0xb95608: DecompressPointer r0
    //     0xb95608: add             x0, x0, HEAP, lsl #32
    // 0xb9560c: cmp             w0, NULL
    // 0xb95610: b.eq            #0xb96b80
    // 0xb95614: LoadField: r3 = r0->field_f
    //     0xb95614: ldur            w3, [x0, #0xf]
    // 0xb95618: DecompressPointer r3
    //     0xb95618: add             x3, x3, HEAP, lsl #32
    // 0xb9561c: LoadField: r4 = r2->field_13
    //     0xb9561c: ldur            x4, [x2, #0x13]
    // 0xb95620: LoadField: r0 = r3->field_b
    //     0xb95620: ldur            w0, [x3, #0xb]
    // 0xb95624: r1 = LoadInt32Instr(r0)
    //     0xb95624: sbfx            x1, x0, #1, #0x1f
    // 0xb95628: mov             x0, x1
    // 0xb9562c: mov             x1, x4
    // 0xb95630: cmp             x1, x0
    // 0xb95634: b.hs            #0xb96b84
    // 0xb95638: LoadField: r0 = r3->field_f
    //     0xb95638: ldur            w0, [x3, #0xf]
    // 0xb9563c: DecompressPointer r0
    //     0xb9563c: add             x0, x0, HEAP, lsl #32
    // 0xb95640: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb95640: add             x16, x0, x4, lsl #2
    //     0xb95644: ldur            w1, [x16, #0xf]
    // 0xb95648: DecompressPointer r1
    //     0xb95648: add             x1, x1, HEAP, lsl #32
    // 0xb9564c: LoadField: r0 = r1->field_f
    //     0xb9564c: ldur            w0, [x1, #0xf]
    // 0xb95650: DecompressPointer r0
    //     0xb95650: add             x0, x0, HEAP, lsl #32
    // 0xb95654: r1 = 60
    //     0xb95654: movz            x1, #0x3c
    // 0xb95658: branchIfSmi(r0, 0xb95664)
    //     0xb95658: tbz             w0, #0, #0xb95664
    // 0xb9565c: r1 = LoadClassIdInstr(r0)
    //     0xb9565c: ldur            x1, [x0, #-1]
    //     0xb95660: ubfx            x1, x1, #0xc, #0x14
    // 0xb95664: str             x0, [SP]
    // 0xb95668: mov             x0, x1
    // 0xb9566c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb9566c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb95670: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb95670: movz            x17, #0x2700
    //     0xb95674: add             lr, x0, x17
    //     0xb95678: ldr             lr, [x21, lr, lsl #3]
    //     0xb9567c: blr             lr
    // 0xb95680: mov             x1, x0
    // 0xb95684: r0 = parse()
    //     0xb95684: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb95688: d1 = 3.500000
    //     0xb95688: fmov            d1, #3.50000000
    // 0xb9568c: fcmp            d0, d1
    // 0xb95690: b.lt            #0xb956b0
    // 0xb95694: r1 = Instance_Color
    //     0xb95694: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb95698: ldr             x1, [x1, #0x858]
    // 0xb9569c: d0 = 0.700000
    //     0xb9569c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb956a0: ldr             d0, [x17, #0xf48]
    // 0xb956a4: r0 = withOpacity()
    //     0xb956a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb956a8: d0 = 2.000000
    //     0xb956a8: fmov            d0, #2.00000000
    // 0xb956ac: b               #0xb9575c
    // 0xb956b0: ldur            x2, [fp, #-8]
    // 0xb956b4: LoadField: r0 = r2->field_b
    //     0xb956b4: ldur            w0, [x2, #0xb]
    // 0xb956b8: DecompressPointer r0
    //     0xb956b8: add             x0, x0, HEAP, lsl #32
    // 0xb956bc: cmp             w0, NULL
    // 0xb956c0: b.eq            #0xb96b88
    // 0xb956c4: LoadField: r3 = r0->field_f
    //     0xb956c4: ldur            w3, [x0, #0xf]
    // 0xb956c8: DecompressPointer r3
    //     0xb956c8: add             x3, x3, HEAP, lsl #32
    // 0xb956cc: LoadField: r4 = r2->field_13
    //     0xb956cc: ldur            x4, [x2, #0x13]
    // 0xb956d0: LoadField: r0 = r3->field_b
    //     0xb956d0: ldur            w0, [x3, #0xb]
    // 0xb956d4: r1 = LoadInt32Instr(r0)
    //     0xb956d4: sbfx            x1, x0, #1, #0x1f
    // 0xb956d8: mov             x0, x1
    // 0xb956dc: mov             x1, x4
    // 0xb956e0: cmp             x1, x0
    // 0xb956e4: b.hs            #0xb96b8c
    // 0xb956e8: LoadField: r0 = r3->field_f
    //     0xb956e8: ldur            w0, [x3, #0xf]
    // 0xb956ec: DecompressPointer r0
    //     0xb956ec: add             x0, x0, HEAP, lsl #32
    // 0xb956f0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb956f0: add             x16, x0, x4, lsl #2
    //     0xb956f4: ldur            w1, [x16, #0xf]
    // 0xb956f8: DecompressPointer r1
    //     0xb956f8: add             x1, x1, HEAP, lsl #32
    // 0xb956fc: LoadField: r0 = r1->field_f
    //     0xb956fc: ldur            w0, [x1, #0xf]
    // 0xb95700: DecompressPointer r0
    //     0xb95700: add             x0, x0, HEAP, lsl #32
    // 0xb95704: r1 = 60
    //     0xb95704: movz            x1, #0x3c
    // 0xb95708: branchIfSmi(r0, 0xb95714)
    //     0xb95708: tbz             w0, #0, #0xb95714
    // 0xb9570c: r1 = LoadClassIdInstr(r0)
    //     0xb9570c: ldur            x1, [x0, #-1]
    //     0xb95710: ubfx            x1, x1, #0xc, #0x14
    // 0xb95714: str             x0, [SP]
    // 0xb95718: mov             x0, x1
    // 0xb9571c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb9571c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb95720: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb95720: movz            x17, #0x2700
    //     0xb95724: add             lr, x0, x17
    //     0xb95728: ldr             lr, [x21, lr, lsl #3]
    //     0xb9572c: blr             lr
    // 0xb95730: mov             x1, x0
    // 0xb95734: r0 = parse()
    //     0xb95734: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb95738: mov             v1.16b, v0.16b
    // 0xb9573c: d0 = 2.000000
    //     0xb9573c: fmov            d0, #2.00000000
    // 0xb95740: fcmp            d1, d0
    // 0xb95744: b.lt            #0xb95754
    // 0xb95748: r0 = Instance_Color
    //     0xb95748: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb9574c: ldr             x0, [x0, #0x860]
    // 0xb95750: b               #0xb9575c
    // 0xb95754: r0 = Instance_Color
    //     0xb95754: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb95758: ldr             x0, [x0, #0x50]
    // 0xb9575c: mov             x4, x0
    // 0xb95760: ldur            x0, [fp, #-8]
    // 0xb95764: ldur            x2, [fp, #-0x20]
    // 0xb95768: ldur            x1, [fp, #-0x18]
    // 0xb9576c: ldur            d1, [fp, #-0x58]
    // 0xb95770: ldur            x3, [fp, #-0x10]
    // 0xb95774: stur            x4, [fp, #-0x30]
    // 0xb95778: r0 = ColorFilter()
    //     0xb95778: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb9577c: mov             x1, x0
    // 0xb95780: ldur            x0, [fp, #-0x30]
    // 0xb95784: stur            x1, [fp, #-0x38]
    // 0xb95788: StoreField: r1->field_7 = r0
    //     0xb95788: stur            w0, [x1, #7]
    // 0xb9578c: r0 = Instance_BlendMode
    //     0xb9578c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb95790: ldr             x0, [x0, #0xb30]
    // 0xb95794: StoreField: r1->field_b = r0
    //     0xb95794: stur            w0, [x1, #0xb]
    // 0xb95798: r2 = 1
    //     0xb95798: movz            x2, #0x1
    // 0xb9579c: StoreField: r1->field_13 = r2
    //     0xb9579c: stur            x2, [x1, #0x13]
    // 0xb957a0: r0 = SvgPicture()
    //     0xb957a0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb957a4: stur            x0, [fp, #-0x30]
    // 0xb957a8: ldur            x16, [fp, #-0x38]
    // 0xb957ac: str             x16, [SP]
    // 0xb957b0: mov             x1, x0
    // 0xb957b4: r2 = "assets/images/green_star.svg"
    //     0xb957b4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb957b8: ldr             x2, [x2, #0x9a0]
    // 0xb957bc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb957bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb957c0: ldr             x4, [x4, #0xa38]
    // 0xb957c4: r0 = SvgPicture.asset()
    //     0xb957c4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb957c8: ldur            x2, [fp, #-8]
    // 0xb957cc: LoadField: r0 = r2->field_b
    //     0xb957cc: ldur            w0, [x2, #0xb]
    // 0xb957d0: DecompressPointer r0
    //     0xb957d0: add             x0, x0, HEAP, lsl #32
    // 0xb957d4: cmp             w0, NULL
    // 0xb957d8: b.eq            #0xb96b90
    // 0xb957dc: LoadField: r3 = r0->field_f
    //     0xb957dc: ldur            w3, [x0, #0xf]
    // 0xb957e0: DecompressPointer r3
    //     0xb957e0: add             x3, x3, HEAP, lsl #32
    // 0xb957e4: LoadField: r4 = r2->field_13
    //     0xb957e4: ldur            x4, [x2, #0x13]
    // 0xb957e8: LoadField: r0 = r3->field_b
    //     0xb957e8: ldur            w0, [x3, #0xb]
    // 0xb957ec: r1 = LoadInt32Instr(r0)
    //     0xb957ec: sbfx            x1, x0, #1, #0x1f
    // 0xb957f0: mov             x0, x1
    // 0xb957f4: mov             x1, x4
    // 0xb957f8: cmp             x1, x0
    // 0xb957fc: b.hs            #0xb96b94
    // 0xb95800: LoadField: r0 = r3->field_f
    //     0xb95800: ldur            w0, [x3, #0xf]
    // 0xb95804: DecompressPointer r0
    //     0xb95804: add             x0, x0, HEAP, lsl #32
    // 0xb95808: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb95808: add             x16, x0, x4, lsl #2
    //     0xb9580c: ldur            w1, [x16, #0xf]
    // 0xb95810: DecompressPointer r1
    //     0xb95810: add             x1, x1, HEAP, lsl #32
    // 0xb95814: LoadField: r0 = r1->field_f
    //     0xb95814: ldur            w0, [x1, #0xf]
    // 0xb95818: DecompressPointer r0
    //     0xb95818: add             x0, x0, HEAP, lsl #32
    // 0xb9581c: r1 = 60
    //     0xb9581c: movz            x1, #0x3c
    // 0xb95820: branchIfSmi(r0, 0xb9582c)
    //     0xb95820: tbz             w0, #0, #0xb9582c
    // 0xb95824: r1 = LoadClassIdInstr(r0)
    //     0xb95824: ldur            x1, [x0, #-1]
    //     0xb95828: ubfx            x1, x1, #0xc, #0x14
    // 0xb9582c: str             x0, [SP]
    // 0xb95830: mov             x0, x1
    // 0xb95834: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb95834: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb95838: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb95838: movz            x17, #0x2700
    //     0xb9583c: add             lr, x0, x17
    //     0xb95840: ldr             lr, [x21, lr, lsl #3]
    //     0xb95844: blr             lr
    // 0xb95848: ldur            x2, [fp, #-0x20]
    // 0xb9584c: stur            x0, [fp, #-0x38]
    // 0xb95850: LoadField: r1 = r2->field_13
    //     0xb95850: ldur            w1, [x2, #0x13]
    // 0xb95854: DecompressPointer r1
    //     0xb95854: add             x1, x1, HEAP, lsl #32
    // 0xb95858: r0 = of()
    //     0xb95858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9585c: LoadField: r1 = r0->field_87
    //     0xb9585c: ldur            w1, [x0, #0x87]
    // 0xb95860: DecompressPointer r1
    //     0xb95860: add             x1, x1, HEAP, lsl #32
    // 0xb95864: LoadField: r0 = r1->field_7
    //     0xb95864: ldur            w0, [x1, #7]
    // 0xb95868: DecompressPointer r0
    //     0xb95868: add             x0, x0, HEAP, lsl #32
    // 0xb9586c: r16 = 12.000000
    //     0xb9586c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb95870: ldr             x16, [x16, #0x9e8]
    // 0xb95874: r30 = Instance_Color
    //     0xb95874: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95878: stp             lr, x16, [SP]
    // 0xb9587c: mov             x1, x0
    // 0xb95880: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb95880: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb95884: ldr             x4, [x4, #0xaa0]
    // 0xb95888: r0 = copyWith()
    //     0xb95888: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9588c: stur            x0, [fp, #-0x48]
    // 0xb95890: r0 = Text()
    //     0xb95890: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb95894: mov             x3, x0
    // 0xb95898: ldur            x0, [fp, #-0x38]
    // 0xb9589c: stur            x3, [fp, #-0x50]
    // 0xb958a0: StoreField: r3->field_b = r0
    //     0xb958a0: stur            w0, [x3, #0xb]
    // 0xb958a4: ldur            x0, [fp, #-0x48]
    // 0xb958a8: StoreField: r3->field_13 = r0
    //     0xb958a8: stur            w0, [x3, #0x13]
    // 0xb958ac: r1 = Null
    //     0xb958ac: mov             x1, NULL
    // 0xb958b0: r2 = 6
    //     0xb958b0: movz            x2, #0x6
    // 0xb958b4: r0 = AllocateArray()
    //     0xb958b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb958b8: mov             x2, x0
    // 0xb958bc: ldur            x0, [fp, #-0x30]
    // 0xb958c0: stur            x2, [fp, #-0x38]
    // 0xb958c4: StoreField: r2->field_f = r0
    //     0xb958c4: stur            w0, [x2, #0xf]
    // 0xb958c8: r16 = Instance_SizedBox
    //     0xb958c8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb958cc: ldr             x16, [x16, #0xe98]
    // 0xb958d0: StoreField: r2->field_13 = r16
    //     0xb958d0: stur            w16, [x2, #0x13]
    // 0xb958d4: ldur            x0, [fp, #-0x50]
    // 0xb958d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb958d8: stur            w0, [x2, #0x17]
    // 0xb958dc: r1 = <Widget>
    //     0xb958dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb958e0: r0 = AllocateGrowableArray()
    //     0xb958e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb958e4: mov             x1, x0
    // 0xb958e8: ldur            x0, [fp, #-0x38]
    // 0xb958ec: stur            x1, [fp, #-0x30]
    // 0xb958f0: StoreField: r1->field_f = r0
    //     0xb958f0: stur            w0, [x1, #0xf]
    // 0xb958f4: r2 = 6
    //     0xb958f4: movz            x2, #0x6
    // 0xb958f8: StoreField: r1->field_b = r2
    //     0xb958f8: stur            w2, [x1, #0xb]
    // 0xb958fc: r0 = Row()
    //     0xb958fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb95900: mov             x1, x0
    // 0xb95904: r0 = Instance_Axis
    //     0xb95904: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb95908: stur            x1, [fp, #-0x38]
    // 0xb9590c: StoreField: r1->field_f = r0
    //     0xb9590c: stur            w0, [x1, #0xf]
    // 0xb95910: r2 = Instance_MainAxisAlignment
    //     0xb95910: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb95914: ldr             x2, [x2, #0xa08]
    // 0xb95918: StoreField: r1->field_13 = r2
    //     0xb95918: stur            w2, [x1, #0x13]
    // 0xb9591c: r3 = Instance_MainAxisSize
    //     0xb9591c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb95920: ldr             x3, [x3, #0xa10]
    // 0xb95924: ArrayStore: r1[0] = r3  ; List_4
    //     0xb95924: stur            w3, [x1, #0x17]
    // 0xb95928: r4 = Instance_CrossAxisAlignment
    //     0xb95928: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9592c: ldr             x4, [x4, #0xa18]
    // 0xb95930: StoreField: r1->field_1b = r4
    //     0xb95930: stur            w4, [x1, #0x1b]
    // 0xb95934: r5 = Instance_VerticalDirection
    //     0xb95934: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb95938: ldr             x5, [x5, #0xa20]
    // 0xb9593c: StoreField: r1->field_23 = r5
    //     0xb9593c: stur            w5, [x1, #0x23]
    // 0xb95940: r6 = Instance_Clip
    //     0xb95940: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb95944: ldr             x6, [x6, #0x38]
    // 0xb95948: StoreField: r1->field_2b = r6
    //     0xb95948: stur            w6, [x1, #0x2b]
    // 0xb9594c: StoreField: r1->field_2f = rZR
    //     0xb9594c: stur            xzr, [x1, #0x2f]
    // 0xb95950: ldur            x7, [fp, #-0x30]
    // 0xb95954: StoreField: r1->field_b = r7
    //     0xb95954: stur            w7, [x1, #0xb]
    // 0xb95958: r0 = Align()
    //     0xb95958: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb9595c: mov             x3, x0
    // 0xb95960: r0 = Instance_Alignment
    //     0xb95960: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb95964: ldr             x0, [x0, #0xa78]
    // 0xb95968: stur            x3, [fp, #-0x30]
    // 0xb9596c: StoreField: r3->field_f = r0
    //     0xb9596c: stur            w0, [x3, #0xf]
    // 0xb95970: ldur            x1, [fp, #-0x38]
    // 0xb95974: StoreField: r3->field_b = r1
    //     0xb95974: stur            w1, [x3, #0xb]
    // 0xb95978: r1 = Null
    //     0xb95978: mov             x1, NULL
    // 0xb9597c: r2 = 4
    //     0xb9597c: movz            x2, #0x4
    // 0xb95980: r0 = AllocateArray()
    //     0xb95980: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb95984: mov             x2, x0
    // 0xb95988: ldur            x0, [fp, #-0x18]
    // 0xb9598c: stur            x2, [fp, #-0x38]
    // 0xb95990: StoreField: r2->field_f = r0
    //     0xb95990: stur            w0, [x2, #0xf]
    // 0xb95994: ldur            x0, [fp, #-0x30]
    // 0xb95998: StoreField: r2->field_13 = r0
    //     0xb95998: stur            w0, [x2, #0x13]
    // 0xb9599c: r1 = <Widget>
    //     0xb9599c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb959a0: r0 = AllocateGrowableArray()
    //     0xb959a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb959a4: mov             x1, x0
    // 0xb959a8: ldur            x0, [fp, #-0x38]
    // 0xb959ac: stur            x1, [fp, #-0x18]
    // 0xb959b0: StoreField: r1->field_f = r0
    //     0xb959b0: stur            w0, [x1, #0xf]
    // 0xb959b4: r2 = 4
    //     0xb959b4: movz            x2, #0x4
    // 0xb959b8: StoreField: r1->field_b = r2
    //     0xb959b8: stur            w2, [x1, #0xb]
    // 0xb959bc: r0 = Row()
    //     0xb959bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb959c0: mov             x1, x0
    // 0xb959c4: r0 = Instance_Axis
    //     0xb959c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb959c8: stur            x1, [fp, #-0x30]
    // 0xb959cc: StoreField: r1->field_f = r0
    //     0xb959cc: stur            w0, [x1, #0xf]
    // 0xb959d0: r2 = Instance_MainAxisAlignment
    //     0xb959d0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb959d4: ldr             x2, [x2, #0xa8]
    // 0xb959d8: StoreField: r1->field_13 = r2
    //     0xb959d8: stur            w2, [x1, #0x13]
    // 0xb959dc: r3 = Instance_MainAxisSize
    //     0xb959dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb959e0: ldr             x3, [x3, #0xa10]
    // 0xb959e4: ArrayStore: r1[0] = r3  ; List_4
    //     0xb959e4: stur            w3, [x1, #0x17]
    // 0xb959e8: r4 = Instance_CrossAxisAlignment
    //     0xb959e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb959ec: ldr             x4, [x4, #0xa18]
    // 0xb959f0: StoreField: r1->field_1b = r4
    //     0xb959f0: stur            w4, [x1, #0x1b]
    // 0xb959f4: r5 = Instance_VerticalDirection
    //     0xb959f4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb959f8: ldr             x5, [x5, #0xa20]
    // 0xb959fc: StoreField: r1->field_23 = r5
    //     0xb959fc: stur            w5, [x1, #0x23]
    // 0xb95a00: r6 = Instance_Clip
    //     0xb95a00: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb95a04: ldr             x6, [x6, #0x38]
    // 0xb95a08: StoreField: r1->field_2b = r6
    //     0xb95a08: stur            w6, [x1, #0x2b]
    // 0xb95a0c: StoreField: r1->field_2f = rZR
    //     0xb95a0c: stur            xzr, [x1, #0x2f]
    // 0xb95a10: ldur            x7, [fp, #-0x18]
    // 0xb95a14: StoreField: r1->field_b = r7
    //     0xb95a14: stur            w7, [x1, #0xb]
    // 0xb95a18: r0 = Padding()
    //     0xb95a18: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb95a1c: mov             x1, x0
    // 0xb95a20: r0 = Instance_EdgeInsets
    //     0xb95a20: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb95a24: ldr             x0, [x0, #0xd0]
    // 0xb95a28: stur            x1, [fp, #-0x18]
    // 0xb95a2c: StoreField: r1->field_f = r0
    //     0xb95a2c: stur            w0, [x1, #0xf]
    // 0xb95a30: ldur            x0, [fp, #-0x30]
    // 0xb95a34: StoreField: r1->field_b = r0
    //     0xb95a34: stur            w0, [x1, #0xb]
    // 0xb95a38: r0 = Container()
    //     0xb95a38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb95a3c: stur            x0, [fp, #-0x30]
    // 0xb95a40: ldur            x16, [fp, #-0x28]
    // 0xb95a44: ldur            lr, [fp, #-0x18]
    // 0xb95a48: stp             lr, x16, [SP]
    // 0xb95a4c: mov             x1, x0
    // 0xb95a50: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb95a50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb95a54: ldr             x4, [x4, #0x88]
    // 0xb95a58: r0 = Container()
    //     0xb95a58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb95a5c: ldur            d0, [fp, #-0x58]
    // 0xb95a60: r0 = inline_Allocate_Double()
    //     0xb95a60: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb95a64: add             x0, x0, #0x10
    //     0xb95a68: cmp             x1, x0
    //     0xb95a6c: b.ls            #0xb96b98
    //     0xb95a70: str             x0, [THR, #0x50]  ; THR::top
    //     0xb95a74: sub             x0, x0, #0xf
    //     0xb95a78: movz            x1, #0xe15c
    //     0xb95a7c: movk            x1, #0x3, lsl #16
    //     0xb95a80: stur            x1, [x0, #-1]
    // 0xb95a84: StoreField: r0->field_7 = d0
    //     0xb95a84: stur            d0, [x0, #7]
    // 0xb95a88: stur            x0, [fp, #-0x18]
    // 0xb95a8c: r0 = SizedBox()
    //     0xb95a8c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb95a90: mov             x2, x0
    // 0xb95a94: ldur            x0, [fp, #-0x18]
    // 0xb95a98: stur            x2, [fp, #-0x28]
    // 0xb95a9c: StoreField: r2->field_f = r0
    //     0xb95a9c: stur            w0, [x2, #0xf]
    // 0xb95aa0: ldur            x0, [fp, #-0x30]
    // 0xb95aa4: StoreField: r2->field_b = r0
    //     0xb95aa4: stur            w0, [x2, #0xb]
    // 0xb95aa8: r1 = <StackParentData>
    //     0xb95aa8: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb95aac: ldr             x1, [x1, #0x8e0]
    // 0xb95ab0: r0 = Positioned()
    //     0xb95ab0: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb95ab4: mov             x2, x0
    // 0xb95ab8: r0 = 0.000000
    //     0xb95ab8: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb95abc: stur            x2, [fp, #-0x18]
    // 0xb95ac0: StoreField: r2->field_1f = r0
    //     0xb95ac0: stur            w0, [x2, #0x1f]
    // 0xb95ac4: ldur            x0, [fp, #-0x28]
    // 0xb95ac8: StoreField: r2->field_b = r0
    //     0xb95ac8: stur            w0, [x2, #0xb]
    // 0xb95acc: ldur            x0, [fp, #-0x10]
    // 0xb95ad0: LoadField: r1 = r0->field_b
    //     0xb95ad0: ldur            w1, [x0, #0xb]
    // 0xb95ad4: LoadField: r3 = r0->field_f
    //     0xb95ad4: ldur            w3, [x0, #0xf]
    // 0xb95ad8: DecompressPointer r3
    //     0xb95ad8: add             x3, x3, HEAP, lsl #32
    // 0xb95adc: LoadField: r4 = r3->field_b
    //     0xb95adc: ldur            w4, [x3, #0xb]
    // 0xb95ae0: r3 = LoadInt32Instr(r1)
    //     0xb95ae0: sbfx            x3, x1, #1, #0x1f
    // 0xb95ae4: stur            x3, [fp, #-0x40]
    // 0xb95ae8: r1 = LoadInt32Instr(r4)
    //     0xb95ae8: sbfx            x1, x4, #1, #0x1f
    // 0xb95aec: cmp             x3, x1
    // 0xb95af0: b.ne            #0xb95afc
    // 0xb95af4: mov             x1, x0
    // 0xb95af8: r0 = _growToNextCapacity()
    //     0xb95af8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb95afc: ldur            x2, [fp, #-0x10]
    // 0xb95b00: ldur            x3, [fp, #-0x40]
    // 0xb95b04: add             x0, x3, #1
    // 0xb95b08: lsl             x1, x0, #1
    // 0xb95b0c: StoreField: r2->field_b = r1
    //     0xb95b0c: stur            w1, [x2, #0xb]
    // 0xb95b10: LoadField: r1 = r2->field_f
    //     0xb95b10: ldur            w1, [x2, #0xf]
    // 0xb95b14: DecompressPointer r1
    //     0xb95b14: add             x1, x1, HEAP, lsl #32
    // 0xb95b18: ldur            x0, [fp, #-0x18]
    // 0xb95b1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb95b1c: add             x25, x1, x3, lsl #2
    //     0xb95b20: add             x25, x25, #0xf
    //     0xb95b24: str             w0, [x25]
    //     0xb95b28: tbz             w0, #0, #0xb95b44
    //     0xb95b2c: ldurb           w16, [x1, #-1]
    //     0xb95b30: ldurb           w17, [x0, #-1]
    //     0xb95b34: and             x16, x17, x16, lsr #2
    //     0xb95b38: tst             x16, HEAP, lsr #32
    //     0xb95b3c: b.eq            #0xb95b44
    //     0xb95b40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb95b44: ldur            x0, [fp, #-8]
    // 0xb95b48: r0 = Stack()
    //     0xb95b48: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb95b4c: mov             x1, x0
    // 0xb95b50: r0 = Instance_AlignmentDirectional
    //     0xb95b50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb95b54: ldr             x0, [x0, #0xd08]
    // 0xb95b58: stur            x1, [fp, #-0x18]
    // 0xb95b5c: StoreField: r1->field_f = r0
    //     0xb95b5c: stur            w0, [x1, #0xf]
    // 0xb95b60: r0 = Instance_StackFit
    //     0xb95b60: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb95b64: ldr             x0, [x0, #0xfa8]
    // 0xb95b68: ArrayStore: r1[0] = r0  ; List_4
    //     0xb95b68: stur            w0, [x1, #0x17]
    // 0xb95b6c: r0 = Instance_Clip
    //     0xb95b6c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb95b70: ldr             x0, [x0, #0x7e0]
    // 0xb95b74: StoreField: r1->field_1b = r0
    //     0xb95b74: stur            w0, [x1, #0x1b]
    // 0xb95b78: ldur            x0, [fp, #-0x10]
    // 0xb95b7c: StoreField: r1->field_b = r0
    //     0xb95b7c: stur            w0, [x1, #0xb]
    // 0xb95b80: r0 = ColoredBox()
    //     0xb95b80: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb95b84: mov             x2, x0
    // 0xb95b88: r0 = Instance_Color
    //     0xb95b88: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95b8c: stur            x2, [fp, #-0x10]
    // 0xb95b90: StoreField: r2->field_f = r0
    //     0xb95b90: stur            w0, [x2, #0xf]
    // 0xb95b94: ldur            x1, [fp, #-0x18]
    // 0xb95b98: StoreField: r2->field_b = r1
    //     0xb95b98: stur            w1, [x2, #0xb]
    // 0xb95b9c: r1 = <FlexParentData>
    //     0xb95b9c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb95ba0: ldr             x1, [x1, #0xe00]
    // 0xb95ba4: r0 = Expanded()
    //     0xb95ba4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb95ba8: mov             x3, x0
    // 0xb95bac: r0 = 1
    //     0xb95bac: movz            x0, #0x1
    // 0xb95bb0: stur            x3, [fp, #-0x18]
    // 0xb95bb4: StoreField: r3->field_13 = r0
    //     0xb95bb4: stur            x0, [x3, #0x13]
    // 0xb95bb8: r1 = Instance_FlexFit
    //     0xb95bb8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb95bbc: ldr             x1, [x1, #0xe08]
    // 0xb95bc0: StoreField: r3->field_1b = r1
    //     0xb95bc0: stur            w1, [x3, #0x1b]
    // 0xb95bc4: ldur            x1, [fp, #-0x10]
    // 0xb95bc8: StoreField: r3->field_b = r1
    //     0xb95bc8: stur            w1, [x3, #0xb]
    // 0xb95bcc: r1 = <Widget>
    //     0xb95bcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb95bd0: r2 = 0
    //     0xb95bd0: movz            x2, #0
    // 0xb95bd4: r0 = _GrowableList()
    //     0xb95bd4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb95bd8: mov             x2, x0
    // 0xb95bdc: ldur            x0, [fp, #-8]
    // 0xb95be0: stur            x2, [fp, #-0x10]
    // 0xb95be4: LoadField: r1 = r0->field_2b
    //     0xb95be4: ldur            w1, [x0, #0x2b]
    // 0xb95be8: DecompressPointer r1
    //     0xb95be8: add             x1, x1, HEAP, lsl #32
    // 0xb95bec: tbz             w1, #4, #0xb96608
    // 0xb95bf0: r1 = Instance_Color
    //     0xb95bf0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95bf4: d0 = 0.050000
    //     0xb95bf4: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb95bf8: r0 = withOpacity()
    //     0xb95bf8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb95bfc: stur            x0, [fp, #-0x28]
    // 0xb95c00: r0 = BoxDecoration()
    //     0xb95c00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb95c04: mov             x2, x0
    // 0xb95c08: ldur            x0, [fp, #-0x28]
    // 0xb95c0c: stur            x2, [fp, #-0x30]
    // 0xb95c10: StoreField: r2->field_7 = r0
    //     0xb95c10: stur            w0, [x2, #7]
    // 0xb95c14: r0 = Instance_BoxShape
    //     0xb95c14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb95c18: ldr             x0, [x0, #0x970]
    // 0xb95c1c: StoreField: r2->field_23 = r0
    //     0xb95c1c: stur            w0, [x2, #0x23]
    // 0xb95c20: ldur            x3, [fp, #-8]
    // 0xb95c24: LoadField: r0 = r3->field_b
    //     0xb95c24: ldur            w0, [x3, #0xb]
    // 0xb95c28: DecompressPointer r0
    //     0xb95c28: add             x0, x0, HEAP, lsl #32
    // 0xb95c2c: cmp             w0, NULL
    // 0xb95c30: b.eq            #0xb96ba8
    // 0xb95c34: LoadField: r4 = r0->field_f
    //     0xb95c34: ldur            w4, [x0, #0xf]
    // 0xb95c38: DecompressPointer r4
    //     0xb95c38: add             x4, x4, HEAP, lsl #32
    // 0xb95c3c: LoadField: r5 = r3->field_13
    //     0xb95c3c: ldur            x5, [x3, #0x13]
    // 0xb95c40: LoadField: r0 = r4->field_b
    //     0xb95c40: ldur            w0, [x4, #0xb]
    // 0xb95c44: r1 = LoadInt32Instr(r0)
    //     0xb95c44: sbfx            x1, x0, #1, #0x1f
    // 0xb95c48: mov             x0, x1
    // 0xb95c4c: mov             x1, x5
    // 0xb95c50: cmp             x1, x0
    // 0xb95c54: b.hs            #0xb96bac
    // 0xb95c58: LoadField: r0 = r4->field_f
    //     0xb95c58: ldur            w0, [x4, #0xf]
    // 0xb95c5c: DecompressPointer r0
    //     0xb95c5c: add             x0, x0, HEAP, lsl #32
    // 0xb95c60: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb95c60: add             x16, x0, x5, lsl #2
    //     0xb95c64: ldur            w1, [x16, #0xf]
    // 0xb95c68: DecompressPointer r1
    //     0xb95c68: add             x1, x1, HEAP, lsl #32
    // 0xb95c6c: LoadField: r0 = r1->field_7
    //     0xb95c6c: ldur            w0, [x1, #7]
    // 0xb95c70: DecompressPointer r0
    //     0xb95c70: add             x0, x0, HEAP, lsl #32
    // 0xb95c74: cmp             w0, NULL
    // 0xb95c78: b.ne            #0xb95c84
    // 0xb95c7c: r0 = Null
    //     0xb95c7c: mov             x0, NULL
    // 0xb95c80: b               #0xb95ca8
    // 0xb95c84: stp             xzr, x0, [SP]
    // 0xb95c88: r0 = []()
    //     0xb95c88: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb95c8c: r1 = LoadClassIdInstr(r0)
    //     0xb95c8c: ldur            x1, [x0, #-1]
    //     0xb95c90: ubfx            x1, x1, #0xc, #0x14
    // 0xb95c94: str             x0, [SP]
    // 0xb95c98: mov             x0, x1
    // 0xb95c9c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb95c9c: sub             lr, x0, #1, lsl #12
    //     0xb95ca0: ldr             lr, [x21, lr, lsl #3]
    //     0xb95ca4: blr             lr
    // 0xb95ca8: cmp             w0, NULL
    // 0xb95cac: b.ne            #0xb95cb8
    // 0xb95cb0: r3 = ""
    //     0xb95cb0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb95cb4: b               #0xb95cbc
    // 0xb95cb8: mov             x3, x0
    // 0xb95cbc: ldur            x0, [fp, #-8]
    // 0xb95cc0: ldur            x2, [fp, #-0x20]
    // 0xb95cc4: stur            x3, [fp, #-0x28]
    // 0xb95cc8: LoadField: r1 = r2->field_13
    //     0xb95cc8: ldur            w1, [x2, #0x13]
    // 0xb95ccc: DecompressPointer r1
    //     0xb95ccc: add             x1, x1, HEAP, lsl #32
    // 0xb95cd0: r0 = of()
    //     0xb95cd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb95cd4: LoadField: r1 = r0->field_87
    //     0xb95cd4: ldur            w1, [x0, #0x87]
    // 0xb95cd8: DecompressPointer r1
    //     0xb95cd8: add             x1, x1, HEAP, lsl #32
    // 0xb95cdc: LoadField: r0 = r1->field_7
    //     0xb95cdc: ldur            w0, [x1, #7]
    // 0xb95ce0: DecompressPointer r0
    //     0xb95ce0: add             x0, x0, HEAP, lsl #32
    // 0xb95ce4: stur            x0, [fp, #-0x38]
    // 0xb95ce8: r1 = Instance_Color
    //     0xb95ce8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95cec: d0 = 0.500000
    //     0xb95cec: fmov            d0, #0.50000000
    // 0xb95cf0: r0 = withOpacity()
    //     0xb95cf0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb95cf4: r16 = 16.000000
    //     0xb95cf4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb95cf8: ldr             x16, [x16, #0x188]
    // 0xb95cfc: stp             x0, x16, [SP]
    // 0xb95d00: ldur            x1, [fp, #-0x38]
    // 0xb95d04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb95d04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb95d08: ldr             x4, [x4, #0xaa0]
    // 0xb95d0c: r0 = copyWith()
    //     0xb95d0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95d10: stur            x0, [fp, #-0x38]
    // 0xb95d14: r0 = Text()
    //     0xb95d14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb95d18: mov             x1, x0
    // 0xb95d1c: ldur            x0, [fp, #-0x28]
    // 0xb95d20: stur            x1, [fp, #-0x48]
    // 0xb95d24: StoreField: r1->field_b = r0
    //     0xb95d24: stur            w0, [x1, #0xb]
    // 0xb95d28: ldur            x0, [fp, #-0x38]
    // 0xb95d2c: StoreField: r1->field_13 = r0
    //     0xb95d2c: stur            w0, [x1, #0x13]
    // 0xb95d30: r0 = Center()
    //     0xb95d30: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb95d34: mov             x1, x0
    // 0xb95d38: r0 = Instance_Alignment
    //     0xb95d38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb95d3c: ldr             x0, [x0, #0xb10]
    // 0xb95d40: stur            x1, [fp, #-0x28]
    // 0xb95d44: StoreField: r1->field_f = r0
    //     0xb95d44: stur            w0, [x1, #0xf]
    // 0xb95d48: ldur            x0, [fp, #-0x48]
    // 0xb95d4c: StoreField: r1->field_b = r0
    //     0xb95d4c: stur            w0, [x1, #0xb]
    // 0xb95d50: r0 = Container()
    //     0xb95d50: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb95d54: stur            x0, [fp, #-0x38]
    // 0xb95d58: r16 = 34.000000
    //     0xb95d58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb95d5c: ldr             x16, [x16, #0x978]
    // 0xb95d60: r30 = 34.000000
    //     0xb95d60: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb95d64: ldr             lr, [lr, #0x978]
    // 0xb95d68: stp             lr, x16, [SP, #0x18]
    // 0xb95d6c: r16 = Instance_EdgeInsets
    //     0xb95d6c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb95d70: ldr             x16, [x16, #0x980]
    // 0xb95d74: ldur            lr, [fp, #-0x30]
    // 0xb95d78: stp             lr, x16, [SP, #8]
    // 0xb95d7c: ldur            x16, [fp, #-0x28]
    // 0xb95d80: str             x16, [SP]
    // 0xb95d84: mov             x1, x0
    // 0xb95d88: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb95d88: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb95d8c: ldr             x4, [x4, #0x988]
    // 0xb95d90: r0 = Container()
    //     0xb95d90: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb95d94: ldur            x2, [fp, #-8]
    // 0xb95d98: LoadField: r0 = r2->field_b
    //     0xb95d98: ldur            w0, [x2, #0xb]
    // 0xb95d9c: DecompressPointer r0
    //     0xb95d9c: add             x0, x0, HEAP, lsl #32
    // 0xb95da0: cmp             w0, NULL
    // 0xb95da4: b.eq            #0xb96bb0
    // 0xb95da8: LoadField: r3 = r0->field_f
    //     0xb95da8: ldur            w3, [x0, #0xf]
    // 0xb95dac: DecompressPointer r3
    //     0xb95dac: add             x3, x3, HEAP, lsl #32
    // 0xb95db0: LoadField: r4 = r2->field_13
    //     0xb95db0: ldur            x4, [x2, #0x13]
    // 0xb95db4: LoadField: r0 = r3->field_b
    //     0xb95db4: ldur            w0, [x3, #0xb]
    // 0xb95db8: r1 = LoadInt32Instr(r0)
    //     0xb95db8: sbfx            x1, x0, #1, #0x1f
    // 0xb95dbc: mov             x0, x1
    // 0xb95dc0: mov             x1, x4
    // 0xb95dc4: cmp             x1, x0
    // 0xb95dc8: b.hs            #0xb96bb4
    // 0xb95dcc: LoadField: r0 = r3->field_f
    //     0xb95dcc: ldur            w0, [x3, #0xf]
    // 0xb95dd0: DecompressPointer r0
    //     0xb95dd0: add             x0, x0, HEAP, lsl #32
    // 0xb95dd4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb95dd4: add             x16, x0, x4, lsl #2
    //     0xb95dd8: ldur            w1, [x16, #0xf]
    // 0xb95ddc: DecompressPointer r1
    //     0xb95ddc: add             x1, x1, HEAP, lsl #32
    // 0xb95de0: LoadField: r0 = r1->field_7
    //     0xb95de0: ldur            w0, [x1, #7]
    // 0xb95de4: DecompressPointer r0
    //     0xb95de4: add             x0, x0, HEAP, lsl #32
    // 0xb95de8: cmp             w0, NULL
    // 0xb95dec: b.ne            #0xb95df8
    // 0xb95df0: r3 = ""
    //     0xb95df0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb95df4: b               #0xb95dfc
    // 0xb95df8: mov             x3, x0
    // 0xb95dfc: ldur            x0, [fp, #-0x20]
    // 0xb95e00: stur            x3, [fp, #-0x28]
    // 0xb95e04: LoadField: r1 = r0->field_13
    //     0xb95e04: ldur            w1, [x0, #0x13]
    // 0xb95e08: DecompressPointer r1
    //     0xb95e08: add             x1, x1, HEAP, lsl #32
    // 0xb95e0c: r0 = of()
    //     0xb95e0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb95e10: LoadField: r1 = r0->field_87
    //     0xb95e10: ldur            w1, [x0, #0x87]
    // 0xb95e14: DecompressPointer r1
    //     0xb95e14: add             x1, x1, HEAP, lsl #32
    // 0xb95e18: LoadField: r0 = r1->field_7
    //     0xb95e18: ldur            w0, [x1, #7]
    // 0xb95e1c: DecompressPointer r0
    //     0xb95e1c: add             x0, x0, HEAP, lsl #32
    // 0xb95e20: r16 = 14.000000
    //     0xb95e20: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb95e24: ldr             x16, [x16, #0x1d8]
    // 0xb95e28: r30 = Instance_Color
    //     0xb95e28: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95e2c: stp             lr, x16, [SP]
    // 0xb95e30: mov             x1, x0
    // 0xb95e34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb95e34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb95e38: ldr             x4, [x4, #0xaa0]
    // 0xb95e3c: r0 = copyWith()
    //     0xb95e3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95e40: stur            x0, [fp, #-0x30]
    // 0xb95e44: r0 = Text()
    //     0xb95e44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb95e48: mov             x2, x0
    // 0xb95e4c: ldur            x0, [fp, #-0x28]
    // 0xb95e50: stur            x2, [fp, #-0x48]
    // 0xb95e54: StoreField: r2->field_b = r0
    //     0xb95e54: stur            w0, [x2, #0xb]
    // 0xb95e58: ldur            x0, [fp, #-0x30]
    // 0xb95e5c: StoreField: r2->field_13 = r0
    //     0xb95e5c: stur            w0, [x2, #0x13]
    // 0xb95e60: ldur            x3, [fp, #-8]
    // 0xb95e64: LoadField: r0 = r3->field_b
    //     0xb95e64: ldur            w0, [x3, #0xb]
    // 0xb95e68: DecompressPointer r0
    //     0xb95e68: add             x0, x0, HEAP, lsl #32
    // 0xb95e6c: cmp             w0, NULL
    // 0xb95e70: b.eq            #0xb96bb8
    // 0xb95e74: LoadField: r4 = r0->field_f
    //     0xb95e74: ldur            w4, [x0, #0xf]
    // 0xb95e78: DecompressPointer r4
    //     0xb95e78: add             x4, x4, HEAP, lsl #32
    // 0xb95e7c: LoadField: r5 = r3->field_13
    //     0xb95e7c: ldur            x5, [x3, #0x13]
    // 0xb95e80: LoadField: r0 = r4->field_b
    //     0xb95e80: ldur            w0, [x4, #0xb]
    // 0xb95e84: r1 = LoadInt32Instr(r0)
    //     0xb95e84: sbfx            x1, x0, #1, #0x1f
    // 0xb95e88: mov             x0, x1
    // 0xb95e8c: mov             x1, x5
    // 0xb95e90: cmp             x1, x0
    // 0xb95e94: b.hs            #0xb96bbc
    // 0xb95e98: LoadField: r0 = r4->field_f
    //     0xb95e98: ldur            w0, [x4, #0xf]
    // 0xb95e9c: DecompressPointer r0
    //     0xb95e9c: add             x0, x0, HEAP, lsl #32
    // 0xb95ea0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb95ea0: add             x16, x0, x5, lsl #2
    //     0xb95ea4: ldur            w1, [x16, #0xf]
    // 0xb95ea8: DecompressPointer r1
    //     0xb95ea8: add             x1, x1, HEAP, lsl #32
    // 0xb95eac: LoadField: r0 = r1->field_1f
    //     0xb95eac: ldur            w0, [x1, #0x1f]
    // 0xb95eb0: DecompressPointer r0
    //     0xb95eb0: add             x0, x0, HEAP, lsl #32
    // 0xb95eb4: cmp             w0, NULL
    // 0xb95eb8: b.ne            #0xb95ec4
    // 0xb95ebc: r4 = ""
    //     0xb95ebc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb95ec0: b               #0xb95ec8
    // 0xb95ec4: mov             x4, x0
    // 0xb95ec8: ldur            x0, [fp, #-0x20]
    // 0xb95ecc: stur            x4, [fp, #-0x28]
    // 0xb95ed0: LoadField: r1 = r0->field_13
    //     0xb95ed0: ldur            w1, [x0, #0x13]
    // 0xb95ed4: DecompressPointer r1
    //     0xb95ed4: add             x1, x1, HEAP, lsl #32
    // 0xb95ed8: r0 = of()
    //     0xb95ed8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb95edc: LoadField: r1 = r0->field_87
    //     0xb95edc: ldur            w1, [x0, #0x87]
    // 0xb95ee0: DecompressPointer r1
    //     0xb95ee0: add             x1, x1, HEAP, lsl #32
    // 0xb95ee4: LoadField: r0 = r1->field_33
    //     0xb95ee4: ldur            w0, [x1, #0x33]
    // 0xb95ee8: DecompressPointer r0
    //     0xb95ee8: add             x0, x0, HEAP, lsl #32
    // 0xb95eec: stur            x0, [fp, #-0x30]
    // 0xb95ef0: cmp             w0, NULL
    // 0xb95ef4: b.ne            #0xb95f00
    // 0xb95ef8: r4 = Null
    //     0xb95ef8: mov             x4, NULL
    // 0xb95efc: b               #0xb95f28
    // 0xb95f00: r1 = Instance_Color
    //     0xb95f00: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb95f04: d0 = 0.500000
    //     0xb95f04: fmov            d0, #0.50000000
    // 0xb95f08: r0 = withOpacity()
    //     0xb95f08: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb95f0c: r16 = 10.000000
    //     0xb95f0c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb95f10: stp             x0, x16, [SP]
    // 0xb95f14: ldur            x1, [fp, #-0x30]
    // 0xb95f18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb95f18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb95f1c: ldr             x4, [x4, #0xaa0]
    // 0xb95f20: r0 = copyWith()
    //     0xb95f20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb95f24: mov             x4, x0
    // 0xb95f28: ldur            x1, [fp, #-8]
    // 0xb95f2c: ldur            x3, [fp, #-0x38]
    // 0xb95f30: ldur            x0, [fp, #-0x48]
    // 0xb95f34: ldur            x2, [fp, #-0x28]
    // 0xb95f38: stur            x4, [fp, #-0x30]
    // 0xb95f3c: r0 = Text()
    //     0xb95f3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb95f40: mov             x1, x0
    // 0xb95f44: ldur            x0, [fp, #-0x28]
    // 0xb95f48: stur            x1, [fp, #-0x50]
    // 0xb95f4c: StoreField: r1->field_b = r0
    //     0xb95f4c: stur            w0, [x1, #0xb]
    // 0xb95f50: ldur            x0, [fp, #-0x30]
    // 0xb95f54: StoreField: r1->field_13 = r0
    //     0xb95f54: stur            w0, [x1, #0x13]
    // 0xb95f58: r0 = Padding()
    //     0xb95f58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb95f5c: mov             x3, x0
    // 0xb95f60: r0 = Instance_EdgeInsets
    //     0xb95f60: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb95f64: ldr             x0, [x0, #0xe90]
    // 0xb95f68: stur            x3, [fp, #-0x28]
    // 0xb95f6c: StoreField: r3->field_f = r0
    //     0xb95f6c: stur            w0, [x3, #0xf]
    // 0xb95f70: ldur            x0, [fp, #-0x50]
    // 0xb95f74: StoreField: r3->field_b = r0
    //     0xb95f74: stur            w0, [x3, #0xb]
    // 0xb95f78: r1 = Null
    //     0xb95f78: mov             x1, NULL
    // 0xb95f7c: r2 = 4
    //     0xb95f7c: movz            x2, #0x4
    // 0xb95f80: r0 = AllocateArray()
    //     0xb95f80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb95f84: mov             x2, x0
    // 0xb95f88: ldur            x0, [fp, #-0x48]
    // 0xb95f8c: stur            x2, [fp, #-0x30]
    // 0xb95f90: StoreField: r2->field_f = r0
    //     0xb95f90: stur            w0, [x2, #0xf]
    // 0xb95f94: ldur            x0, [fp, #-0x28]
    // 0xb95f98: StoreField: r2->field_13 = r0
    //     0xb95f98: stur            w0, [x2, #0x13]
    // 0xb95f9c: r1 = <Widget>
    //     0xb95f9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb95fa0: r0 = AllocateGrowableArray()
    //     0xb95fa0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb95fa4: mov             x1, x0
    // 0xb95fa8: ldur            x0, [fp, #-0x30]
    // 0xb95fac: stur            x1, [fp, #-0x28]
    // 0xb95fb0: StoreField: r1->field_f = r0
    //     0xb95fb0: stur            w0, [x1, #0xf]
    // 0xb95fb4: r2 = 4
    //     0xb95fb4: movz            x2, #0x4
    // 0xb95fb8: StoreField: r1->field_b = r2
    //     0xb95fb8: stur            w2, [x1, #0xb]
    // 0xb95fbc: r0 = Column()
    //     0xb95fbc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb95fc0: mov             x3, x0
    // 0xb95fc4: r0 = Instance_Axis
    //     0xb95fc4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb95fc8: stur            x3, [fp, #-0x30]
    // 0xb95fcc: StoreField: r3->field_f = r0
    //     0xb95fcc: stur            w0, [x3, #0xf]
    // 0xb95fd0: r4 = Instance_MainAxisAlignment
    //     0xb95fd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb95fd4: ldr             x4, [x4, #0xa08]
    // 0xb95fd8: StoreField: r3->field_13 = r4
    //     0xb95fd8: stur            w4, [x3, #0x13]
    // 0xb95fdc: r5 = Instance_MainAxisSize
    //     0xb95fdc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb95fe0: ldr             x5, [x5, #0xa10]
    // 0xb95fe4: ArrayStore: r3[0] = r5  ; List_4
    //     0xb95fe4: stur            w5, [x3, #0x17]
    // 0xb95fe8: r6 = Instance_CrossAxisAlignment
    //     0xb95fe8: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb95fec: ldr             x6, [x6, #0x890]
    // 0xb95ff0: StoreField: r3->field_1b = r6
    //     0xb95ff0: stur            w6, [x3, #0x1b]
    // 0xb95ff4: r7 = Instance_VerticalDirection
    //     0xb95ff4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb95ff8: ldr             x7, [x7, #0xa20]
    // 0xb95ffc: StoreField: r3->field_23 = r7
    //     0xb95ffc: stur            w7, [x3, #0x23]
    // 0xb96000: r8 = Instance_Clip
    //     0xb96000: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb96004: ldr             x8, [x8, #0x38]
    // 0xb96008: StoreField: r3->field_2b = r8
    //     0xb96008: stur            w8, [x3, #0x2b]
    // 0xb9600c: StoreField: r3->field_2f = rZR
    //     0xb9600c: stur            xzr, [x3, #0x2f]
    // 0xb96010: ldur            x1, [fp, #-0x28]
    // 0xb96014: StoreField: r3->field_b = r1
    //     0xb96014: stur            w1, [x3, #0xb]
    // 0xb96018: r1 = Null
    //     0xb96018: mov             x1, NULL
    // 0xb9601c: r2 = 6
    //     0xb9601c: movz            x2, #0x6
    // 0xb96020: r0 = AllocateArray()
    //     0xb96020: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb96024: mov             x2, x0
    // 0xb96028: ldur            x0, [fp, #-0x38]
    // 0xb9602c: stur            x2, [fp, #-0x28]
    // 0xb96030: StoreField: r2->field_f = r0
    //     0xb96030: stur            w0, [x2, #0xf]
    // 0xb96034: r16 = Instance_SizedBox
    //     0xb96034: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb96038: ldr             x16, [x16, #0x998]
    // 0xb9603c: StoreField: r2->field_13 = r16
    //     0xb9603c: stur            w16, [x2, #0x13]
    // 0xb96040: ldur            x0, [fp, #-0x30]
    // 0xb96044: ArrayStore: r2[0] = r0  ; List_4
    //     0xb96044: stur            w0, [x2, #0x17]
    // 0xb96048: r1 = <Widget>
    //     0xb96048: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9604c: r0 = AllocateGrowableArray()
    //     0xb9604c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb96050: mov             x1, x0
    // 0xb96054: ldur            x0, [fp, #-0x28]
    // 0xb96058: stur            x1, [fp, #-0x30]
    // 0xb9605c: StoreField: r1->field_f = r0
    //     0xb9605c: stur            w0, [x1, #0xf]
    // 0xb96060: r2 = 6
    //     0xb96060: movz            x2, #0x6
    // 0xb96064: StoreField: r1->field_b = r2
    //     0xb96064: stur            w2, [x1, #0xb]
    // 0xb96068: r0 = Row()
    //     0xb96068: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9606c: mov             x3, x0
    // 0xb96070: r2 = Instance_Axis
    //     0xb96070: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb96074: stur            x3, [fp, #-0x28]
    // 0xb96078: StoreField: r3->field_f = r2
    //     0xb96078: stur            w2, [x3, #0xf]
    // 0xb9607c: r4 = Instance_MainAxisAlignment
    //     0xb9607c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb96080: ldr             x4, [x4, #0xa08]
    // 0xb96084: StoreField: r3->field_13 = r4
    //     0xb96084: stur            w4, [x3, #0x13]
    // 0xb96088: r5 = Instance_MainAxisSize
    //     0xb96088: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9608c: ldr             x5, [x5, #0xa10]
    // 0xb96090: ArrayStore: r3[0] = r5  ; List_4
    //     0xb96090: stur            w5, [x3, #0x17]
    // 0xb96094: r6 = Instance_CrossAxisAlignment
    //     0xb96094: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb96098: ldr             x6, [x6, #0xa18]
    // 0xb9609c: StoreField: r3->field_1b = r6
    //     0xb9609c: stur            w6, [x3, #0x1b]
    // 0xb960a0: r7 = Instance_VerticalDirection
    //     0xb960a0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb960a4: ldr             x7, [x7, #0xa20]
    // 0xb960a8: StoreField: r3->field_23 = r7
    //     0xb960a8: stur            w7, [x3, #0x23]
    // 0xb960ac: r8 = Instance_Clip
    //     0xb960ac: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb960b0: ldr             x8, [x8, #0x38]
    // 0xb960b4: StoreField: r3->field_2b = r8
    //     0xb960b4: stur            w8, [x3, #0x2b]
    // 0xb960b8: StoreField: r3->field_2f = rZR
    //     0xb960b8: stur            xzr, [x3, #0x2f]
    // 0xb960bc: ldur            x0, [fp, #-0x30]
    // 0xb960c0: StoreField: r3->field_b = r0
    //     0xb960c0: stur            w0, [x3, #0xb]
    // 0xb960c4: ldur            x9, [fp, #-8]
    // 0xb960c8: LoadField: r0 = r9->field_b
    //     0xb960c8: ldur            w0, [x9, #0xb]
    // 0xb960cc: DecompressPointer r0
    //     0xb960cc: add             x0, x0, HEAP, lsl #32
    // 0xb960d0: cmp             w0, NULL
    // 0xb960d4: b.eq            #0xb96bc0
    // 0xb960d8: LoadField: r10 = r0->field_f
    //     0xb960d8: ldur            w10, [x0, #0xf]
    // 0xb960dc: DecompressPointer r10
    //     0xb960dc: add             x10, x10, HEAP, lsl #32
    // 0xb960e0: LoadField: r11 = r9->field_13
    //     0xb960e0: ldur            x11, [x9, #0x13]
    // 0xb960e4: LoadField: r0 = r10->field_b
    //     0xb960e4: ldur            w0, [x10, #0xb]
    // 0xb960e8: r1 = LoadInt32Instr(r0)
    //     0xb960e8: sbfx            x1, x0, #1, #0x1f
    // 0xb960ec: mov             x0, x1
    // 0xb960f0: mov             x1, x11
    // 0xb960f4: cmp             x1, x0
    // 0xb960f8: b.hs            #0xb96bc4
    // 0xb960fc: LoadField: r0 = r10->field_f
    //     0xb960fc: ldur            w0, [x10, #0xf]
    // 0xb96100: DecompressPointer r0
    //     0xb96100: add             x0, x0, HEAP, lsl #32
    // 0xb96104: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xb96104: add             x16, x0, x11, lsl #2
    //     0xb96108: ldur            w1, [x16, #0xf]
    // 0xb9610c: DecompressPointer r1
    //     0xb9610c: add             x1, x1, HEAP, lsl #32
    // 0xb96110: LoadField: r0 = r1->field_f
    //     0xb96110: ldur            w0, [x1, #0xf]
    // 0xb96114: DecompressPointer r0
    //     0xb96114: add             x0, x0, HEAP, lsl #32
    // 0xb96118: r1 = 60
    //     0xb96118: movz            x1, #0x3c
    // 0xb9611c: branchIfSmi(r0, 0xb96128)
    //     0xb9611c: tbz             w0, #0, #0xb96128
    // 0xb96120: r1 = LoadClassIdInstr(r0)
    //     0xb96120: ldur            x1, [x0, #-1]
    //     0xb96124: ubfx            x1, x1, #0xc, #0x14
    // 0xb96128: str             x0, [SP]
    // 0xb9612c: mov             x0, x1
    // 0xb96130: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb96130: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb96134: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb96134: movz            x17, #0x2700
    //     0xb96138: add             lr, x0, x17
    //     0xb9613c: ldr             lr, [x21, lr, lsl #3]
    //     0xb96140: blr             lr
    // 0xb96144: mov             x1, x0
    // 0xb96148: r0 = parse()
    //     0xb96148: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb9614c: mov             v1.16b, v0.16b
    // 0xb96150: d0 = 4.000000
    //     0xb96150: fmov            d0, #4.00000000
    // 0xb96154: fcmp            d1, d0
    // 0xb96158: b.lt            #0xb96168
    // 0xb9615c: r4 = Instance_Color
    //     0xb9615c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb96160: ldr             x4, [x4, #0x858]
    // 0xb96164: b               #0xb962c8
    // 0xb96168: ldur            x2, [fp, #-8]
    // 0xb9616c: LoadField: r0 = r2->field_b
    //     0xb9616c: ldur            w0, [x2, #0xb]
    // 0xb96170: DecompressPointer r0
    //     0xb96170: add             x0, x0, HEAP, lsl #32
    // 0xb96174: cmp             w0, NULL
    // 0xb96178: b.eq            #0xb96bc8
    // 0xb9617c: LoadField: r3 = r0->field_f
    //     0xb9617c: ldur            w3, [x0, #0xf]
    // 0xb96180: DecompressPointer r3
    //     0xb96180: add             x3, x3, HEAP, lsl #32
    // 0xb96184: LoadField: r4 = r2->field_13
    //     0xb96184: ldur            x4, [x2, #0x13]
    // 0xb96188: LoadField: r0 = r3->field_b
    //     0xb96188: ldur            w0, [x3, #0xb]
    // 0xb9618c: r1 = LoadInt32Instr(r0)
    //     0xb9618c: sbfx            x1, x0, #1, #0x1f
    // 0xb96190: mov             x0, x1
    // 0xb96194: mov             x1, x4
    // 0xb96198: cmp             x1, x0
    // 0xb9619c: b.hs            #0xb96bcc
    // 0xb961a0: LoadField: r0 = r3->field_f
    //     0xb961a0: ldur            w0, [x3, #0xf]
    // 0xb961a4: DecompressPointer r0
    //     0xb961a4: add             x0, x0, HEAP, lsl #32
    // 0xb961a8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb961a8: add             x16, x0, x4, lsl #2
    //     0xb961ac: ldur            w1, [x16, #0xf]
    // 0xb961b0: DecompressPointer r1
    //     0xb961b0: add             x1, x1, HEAP, lsl #32
    // 0xb961b4: LoadField: r0 = r1->field_f
    //     0xb961b4: ldur            w0, [x1, #0xf]
    // 0xb961b8: DecompressPointer r0
    //     0xb961b8: add             x0, x0, HEAP, lsl #32
    // 0xb961bc: r1 = 60
    //     0xb961bc: movz            x1, #0x3c
    // 0xb961c0: branchIfSmi(r0, 0xb961cc)
    //     0xb961c0: tbz             w0, #0, #0xb961cc
    // 0xb961c4: r1 = LoadClassIdInstr(r0)
    //     0xb961c4: ldur            x1, [x0, #-1]
    //     0xb961c8: ubfx            x1, x1, #0xc, #0x14
    // 0xb961cc: str             x0, [SP]
    // 0xb961d0: mov             x0, x1
    // 0xb961d4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb961d4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb961d8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb961d8: movz            x17, #0x2700
    //     0xb961dc: add             lr, x0, x17
    //     0xb961e0: ldr             lr, [x21, lr, lsl #3]
    //     0xb961e4: blr             lr
    // 0xb961e8: mov             x1, x0
    // 0xb961ec: r0 = parse()
    //     0xb961ec: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb961f0: mov             v1.16b, v0.16b
    // 0xb961f4: d0 = 3.500000
    //     0xb961f4: fmov            d0, #3.50000000
    // 0xb961f8: fcmp            d1, d0
    // 0xb961fc: b.lt            #0xb96218
    // 0xb96200: r1 = Instance_Color
    //     0xb96200: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb96204: ldr             x1, [x1, #0x858]
    // 0xb96208: d0 = 0.700000
    //     0xb96208: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb9620c: ldr             d0, [x17, #0xf48]
    // 0xb96210: r0 = withOpacity()
    //     0xb96210: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb96214: b               #0xb962c4
    // 0xb96218: ldur            x2, [fp, #-8]
    // 0xb9621c: LoadField: r0 = r2->field_b
    //     0xb9621c: ldur            w0, [x2, #0xb]
    // 0xb96220: DecompressPointer r0
    //     0xb96220: add             x0, x0, HEAP, lsl #32
    // 0xb96224: cmp             w0, NULL
    // 0xb96228: b.eq            #0xb96bd0
    // 0xb9622c: LoadField: r3 = r0->field_f
    //     0xb9622c: ldur            w3, [x0, #0xf]
    // 0xb96230: DecompressPointer r3
    //     0xb96230: add             x3, x3, HEAP, lsl #32
    // 0xb96234: LoadField: r4 = r2->field_13
    //     0xb96234: ldur            x4, [x2, #0x13]
    // 0xb96238: LoadField: r0 = r3->field_b
    //     0xb96238: ldur            w0, [x3, #0xb]
    // 0xb9623c: r1 = LoadInt32Instr(r0)
    //     0xb9623c: sbfx            x1, x0, #1, #0x1f
    // 0xb96240: mov             x0, x1
    // 0xb96244: mov             x1, x4
    // 0xb96248: cmp             x1, x0
    // 0xb9624c: b.hs            #0xb96bd4
    // 0xb96250: LoadField: r0 = r3->field_f
    //     0xb96250: ldur            w0, [x3, #0xf]
    // 0xb96254: DecompressPointer r0
    //     0xb96254: add             x0, x0, HEAP, lsl #32
    // 0xb96258: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb96258: add             x16, x0, x4, lsl #2
    //     0xb9625c: ldur            w1, [x16, #0xf]
    // 0xb96260: DecompressPointer r1
    //     0xb96260: add             x1, x1, HEAP, lsl #32
    // 0xb96264: LoadField: r0 = r1->field_f
    //     0xb96264: ldur            w0, [x1, #0xf]
    // 0xb96268: DecompressPointer r0
    //     0xb96268: add             x0, x0, HEAP, lsl #32
    // 0xb9626c: r1 = 60
    //     0xb9626c: movz            x1, #0x3c
    // 0xb96270: branchIfSmi(r0, 0xb9627c)
    //     0xb96270: tbz             w0, #0, #0xb9627c
    // 0xb96274: r1 = LoadClassIdInstr(r0)
    //     0xb96274: ldur            x1, [x0, #-1]
    //     0xb96278: ubfx            x1, x1, #0xc, #0x14
    // 0xb9627c: str             x0, [SP]
    // 0xb96280: mov             x0, x1
    // 0xb96284: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb96284: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb96288: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb96288: movz            x17, #0x2700
    //     0xb9628c: add             lr, x0, x17
    //     0xb96290: ldr             lr, [x21, lr, lsl #3]
    //     0xb96294: blr             lr
    // 0xb96298: mov             x1, x0
    // 0xb9629c: r0 = parse()
    //     0xb9629c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb962a0: mov             v1.16b, v0.16b
    // 0xb962a4: d0 = 2.000000
    //     0xb962a4: fmov            d0, #2.00000000
    // 0xb962a8: fcmp            d1, d0
    // 0xb962ac: b.lt            #0xb962bc
    // 0xb962b0: r0 = Instance_Color
    //     0xb962b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb962b4: ldr             x0, [x0, #0x860]
    // 0xb962b8: b               #0xb962c4
    // 0xb962bc: r0 = Instance_Color
    //     0xb962bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb962c0: ldr             x0, [x0, #0x50]
    // 0xb962c4: mov             x4, x0
    // 0xb962c8: ldur            x0, [fp, #-8]
    // 0xb962cc: ldur            x2, [fp, #-0x20]
    // 0xb962d0: ldur            x3, [fp, #-0x10]
    // 0xb962d4: ldur            x1, [fp, #-0x28]
    // 0xb962d8: stur            x4, [fp, #-0x30]
    // 0xb962dc: r0 = ColorFilter()
    //     0xb962dc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb962e0: mov             x1, x0
    // 0xb962e4: ldur            x0, [fp, #-0x30]
    // 0xb962e8: stur            x1, [fp, #-0x38]
    // 0xb962ec: StoreField: r1->field_7 = r0
    //     0xb962ec: stur            w0, [x1, #7]
    // 0xb962f0: r0 = Instance_BlendMode
    //     0xb962f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb962f4: ldr             x0, [x0, #0xb30]
    // 0xb962f8: StoreField: r1->field_b = r0
    //     0xb962f8: stur            w0, [x1, #0xb]
    // 0xb962fc: r0 = 1
    //     0xb962fc: movz            x0, #0x1
    // 0xb96300: StoreField: r1->field_13 = r0
    //     0xb96300: stur            x0, [x1, #0x13]
    // 0xb96304: r0 = SvgPicture()
    //     0xb96304: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb96308: stur            x0, [fp, #-0x30]
    // 0xb9630c: ldur            x16, [fp, #-0x38]
    // 0xb96310: str             x16, [SP]
    // 0xb96314: mov             x1, x0
    // 0xb96318: r2 = "assets/images/green_star.svg"
    //     0xb96318: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb9631c: ldr             x2, [x2, #0x9a0]
    // 0xb96320: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb96320: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb96324: ldr             x4, [x4, #0xa38]
    // 0xb96328: r0 = SvgPicture.asset()
    //     0xb96328: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb9632c: ldur            x2, [fp, #-8]
    // 0xb96330: LoadField: r0 = r2->field_b
    //     0xb96330: ldur            w0, [x2, #0xb]
    // 0xb96334: DecompressPointer r0
    //     0xb96334: add             x0, x0, HEAP, lsl #32
    // 0xb96338: cmp             w0, NULL
    // 0xb9633c: b.eq            #0xb96bd8
    // 0xb96340: LoadField: r3 = r0->field_f
    //     0xb96340: ldur            w3, [x0, #0xf]
    // 0xb96344: DecompressPointer r3
    //     0xb96344: add             x3, x3, HEAP, lsl #32
    // 0xb96348: LoadField: r4 = r2->field_13
    //     0xb96348: ldur            x4, [x2, #0x13]
    // 0xb9634c: LoadField: r0 = r3->field_b
    //     0xb9634c: ldur            w0, [x3, #0xb]
    // 0xb96350: r1 = LoadInt32Instr(r0)
    //     0xb96350: sbfx            x1, x0, #1, #0x1f
    // 0xb96354: mov             x0, x1
    // 0xb96358: mov             x1, x4
    // 0xb9635c: cmp             x1, x0
    // 0xb96360: b.hs            #0xb96bdc
    // 0xb96364: LoadField: r0 = r3->field_f
    //     0xb96364: ldur            w0, [x3, #0xf]
    // 0xb96368: DecompressPointer r0
    //     0xb96368: add             x0, x0, HEAP, lsl #32
    // 0xb9636c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb9636c: add             x16, x0, x4, lsl #2
    //     0xb96370: ldur            w1, [x16, #0xf]
    // 0xb96374: DecompressPointer r1
    //     0xb96374: add             x1, x1, HEAP, lsl #32
    // 0xb96378: LoadField: r0 = r1->field_f
    //     0xb96378: ldur            w0, [x1, #0xf]
    // 0xb9637c: DecompressPointer r0
    //     0xb9637c: add             x0, x0, HEAP, lsl #32
    // 0xb96380: r1 = 60
    //     0xb96380: movz            x1, #0x3c
    // 0xb96384: branchIfSmi(r0, 0xb96390)
    //     0xb96384: tbz             w0, #0, #0xb96390
    // 0xb96388: r1 = LoadClassIdInstr(r0)
    //     0xb96388: ldur            x1, [x0, #-1]
    //     0xb9638c: ubfx            x1, x1, #0xc, #0x14
    // 0xb96390: str             x0, [SP]
    // 0xb96394: mov             x0, x1
    // 0xb96398: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb96398: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb9639c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb9639c: movz            x17, #0x2700
    //     0xb963a0: add             lr, x0, x17
    //     0xb963a4: ldr             lr, [x21, lr, lsl #3]
    //     0xb963a8: blr             lr
    // 0xb963ac: ldur            x2, [fp, #-0x20]
    // 0xb963b0: stur            x0, [fp, #-0x38]
    // 0xb963b4: LoadField: r1 = r2->field_13
    //     0xb963b4: ldur            w1, [x2, #0x13]
    // 0xb963b8: DecompressPointer r1
    //     0xb963b8: add             x1, x1, HEAP, lsl #32
    // 0xb963bc: r0 = of()
    //     0xb963bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb963c0: LoadField: r1 = r0->field_87
    //     0xb963c0: ldur            w1, [x0, #0x87]
    // 0xb963c4: DecompressPointer r1
    //     0xb963c4: add             x1, x1, HEAP, lsl #32
    // 0xb963c8: LoadField: r0 = r1->field_7
    //     0xb963c8: ldur            w0, [x1, #7]
    // 0xb963cc: DecompressPointer r0
    //     0xb963cc: add             x0, x0, HEAP, lsl #32
    // 0xb963d0: r16 = 12.000000
    //     0xb963d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb963d4: ldr             x16, [x16, #0x9e8]
    // 0xb963d8: r30 = Instance_Color
    //     0xb963d8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb963dc: stp             lr, x16, [SP]
    // 0xb963e0: mov             x1, x0
    // 0xb963e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb963e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb963e8: ldr             x4, [x4, #0xaa0]
    // 0xb963ec: r0 = copyWith()
    //     0xb963ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb963f0: stur            x0, [fp, #-0x48]
    // 0xb963f4: r0 = Text()
    //     0xb963f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb963f8: mov             x3, x0
    // 0xb963fc: ldur            x0, [fp, #-0x38]
    // 0xb96400: stur            x3, [fp, #-0x50]
    // 0xb96404: StoreField: r3->field_b = r0
    //     0xb96404: stur            w0, [x3, #0xb]
    // 0xb96408: ldur            x0, [fp, #-0x48]
    // 0xb9640c: StoreField: r3->field_13 = r0
    //     0xb9640c: stur            w0, [x3, #0x13]
    // 0xb96410: r1 = Null
    //     0xb96410: mov             x1, NULL
    // 0xb96414: r2 = 6
    //     0xb96414: movz            x2, #0x6
    // 0xb96418: r0 = AllocateArray()
    //     0xb96418: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9641c: mov             x2, x0
    // 0xb96420: ldur            x0, [fp, #-0x30]
    // 0xb96424: stur            x2, [fp, #-0x38]
    // 0xb96428: StoreField: r2->field_f = r0
    //     0xb96428: stur            w0, [x2, #0xf]
    // 0xb9642c: r16 = Instance_SizedBox
    //     0xb9642c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb96430: ldr             x16, [x16, #0xe98]
    // 0xb96434: StoreField: r2->field_13 = r16
    //     0xb96434: stur            w16, [x2, #0x13]
    // 0xb96438: ldur            x0, [fp, #-0x50]
    // 0xb9643c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9643c: stur            w0, [x2, #0x17]
    // 0xb96440: r1 = <Widget>
    //     0xb96440: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb96444: r0 = AllocateGrowableArray()
    //     0xb96444: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb96448: mov             x1, x0
    // 0xb9644c: ldur            x0, [fp, #-0x38]
    // 0xb96450: stur            x1, [fp, #-0x30]
    // 0xb96454: StoreField: r1->field_f = r0
    //     0xb96454: stur            w0, [x1, #0xf]
    // 0xb96458: r0 = 6
    //     0xb96458: movz            x0, #0x6
    // 0xb9645c: StoreField: r1->field_b = r0
    //     0xb9645c: stur            w0, [x1, #0xb]
    // 0xb96460: r0 = Row()
    //     0xb96460: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb96464: mov             x3, x0
    // 0xb96468: r0 = Instance_Axis
    //     0xb96468: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9646c: stur            x3, [fp, #-0x38]
    // 0xb96470: StoreField: r3->field_f = r0
    //     0xb96470: stur            w0, [x3, #0xf]
    // 0xb96474: r4 = Instance_MainAxisAlignment
    //     0xb96474: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb96478: ldr             x4, [x4, #0xa08]
    // 0xb9647c: StoreField: r3->field_13 = r4
    //     0xb9647c: stur            w4, [x3, #0x13]
    // 0xb96480: r5 = Instance_MainAxisSize
    //     0xb96480: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb96484: ldr             x5, [x5, #0xa10]
    // 0xb96488: ArrayStore: r3[0] = r5  ; List_4
    //     0xb96488: stur            w5, [x3, #0x17]
    // 0xb9648c: r6 = Instance_CrossAxisAlignment
    //     0xb9648c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb96490: ldr             x6, [x6, #0xa18]
    // 0xb96494: StoreField: r3->field_1b = r6
    //     0xb96494: stur            w6, [x3, #0x1b]
    // 0xb96498: r7 = Instance_VerticalDirection
    //     0xb96498: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9649c: ldr             x7, [x7, #0xa20]
    // 0xb964a0: StoreField: r3->field_23 = r7
    //     0xb964a0: stur            w7, [x3, #0x23]
    // 0xb964a4: r8 = Instance_Clip
    //     0xb964a4: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb964a8: ldr             x8, [x8, #0x38]
    // 0xb964ac: StoreField: r3->field_2b = r8
    //     0xb964ac: stur            w8, [x3, #0x2b]
    // 0xb964b0: StoreField: r3->field_2f = rZR
    //     0xb964b0: stur            xzr, [x3, #0x2f]
    // 0xb964b4: ldur            x1, [fp, #-0x30]
    // 0xb964b8: StoreField: r3->field_b = r1
    //     0xb964b8: stur            w1, [x3, #0xb]
    // 0xb964bc: r1 = Null
    //     0xb964bc: mov             x1, NULL
    // 0xb964c0: r2 = 4
    //     0xb964c0: movz            x2, #0x4
    // 0xb964c4: r0 = AllocateArray()
    //     0xb964c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb964c8: mov             x2, x0
    // 0xb964cc: ldur            x0, [fp, #-0x28]
    // 0xb964d0: stur            x2, [fp, #-0x30]
    // 0xb964d4: StoreField: r2->field_f = r0
    //     0xb964d4: stur            w0, [x2, #0xf]
    // 0xb964d8: ldur            x0, [fp, #-0x38]
    // 0xb964dc: StoreField: r2->field_13 = r0
    //     0xb964dc: stur            w0, [x2, #0x13]
    // 0xb964e0: r1 = <Widget>
    //     0xb964e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb964e4: r0 = AllocateGrowableArray()
    //     0xb964e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb964e8: mov             x1, x0
    // 0xb964ec: ldur            x0, [fp, #-0x30]
    // 0xb964f0: stur            x1, [fp, #-0x28]
    // 0xb964f4: StoreField: r1->field_f = r0
    //     0xb964f4: stur            w0, [x1, #0xf]
    // 0xb964f8: r2 = 4
    //     0xb964f8: movz            x2, #0x4
    // 0xb964fc: StoreField: r1->field_b = r2
    //     0xb964fc: stur            w2, [x1, #0xb]
    // 0xb96500: r0 = Row()
    //     0xb96500: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb96504: mov             x1, x0
    // 0xb96508: r0 = Instance_Axis
    //     0xb96508: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9650c: stur            x1, [fp, #-0x30]
    // 0xb96510: StoreField: r1->field_f = r0
    //     0xb96510: stur            w0, [x1, #0xf]
    // 0xb96514: r0 = Instance_MainAxisAlignment
    //     0xb96514: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb96518: ldr             x0, [x0, #0xa8]
    // 0xb9651c: StoreField: r1->field_13 = r0
    //     0xb9651c: stur            w0, [x1, #0x13]
    // 0xb96520: r0 = Instance_MainAxisSize
    //     0xb96520: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb96524: ldr             x0, [x0, #0xa10]
    // 0xb96528: ArrayStore: r1[0] = r0  ; List_4
    //     0xb96528: stur            w0, [x1, #0x17]
    // 0xb9652c: r2 = Instance_CrossAxisAlignment
    //     0xb9652c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb96530: ldr             x2, [x2, #0xa18]
    // 0xb96534: StoreField: r1->field_1b = r2
    //     0xb96534: stur            w2, [x1, #0x1b]
    // 0xb96538: r3 = Instance_VerticalDirection
    //     0xb96538: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9653c: ldr             x3, [x3, #0xa20]
    // 0xb96540: StoreField: r1->field_23 = r3
    //     0xb96540: stur            w3, [x1, #0x23]
    // 0xb96544: r4 = Instance_Clip
    //     0xb96544: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb96548: ldr             x4, [x4, #0x38]
    // 0xb9654c: StoreField: r1->field_2b = r4
    //     0xb9654c: stur            w4, [x1, #0x2b]
    // 0xb96550: StoreField: r1->field_2f = rZR
    //     0xb96550: stur            xzr, [x1, #0x2f]
    // 0xb96554: ldur            x5, [fp, #-0x28]
    // 0xb96558: StoreField: r1->field_b = r5
    //     0xb96558: stur            w5, [x1, #0xb]
    // 0xb9655c: r0 = Container()
    //     0xb9655c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb96560: stur            x0, [fp, #-0x28]
    // 0xb96564: r16 = Instance_BoxDecoration
    //     0xb96564: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb96568: ldr             x16, [x16, #0x5a8]
    // 0xb9656c: r30 = Instance_EdgeInsets
    //     0xb9656c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb96570: ldr             lr, [lr, #0xd0]
    // 0xb96574: stp             lr, x16, [SP, #8]
    // 0xb96578: ldur            x16, [fp, #-0x30]
    // 0xb9657c: str             x16, [SP]
    // 0xb96580: mov             x1, x0
    // 0xb96584: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb96584: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb96588: ldr             x4, [x4, #0xb40]
    // 0xb9658c: r0 = Container()
    //     0xb9658c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb96590: ldur            x0, [fp, #-0x10]
    // 0xb96594: LoadField: r1 = r0->field_b
    //     0xb96594: ldur            w1, [x0, #0xb]
    // 0xb96598: LoadField: r2 = r0->field_f
    //     0xb96598: ldur            w2, [x0, #0xf]
    // 0xb9659c: DecompressPointer r2
    //     0xb9659c: add             x2, x2, HEAP, lsl #32
    // 0xb965a0: LoadField: r3 = r2->field_b
    //     0xb965a0: ldur            w3, [x2, #0xb]
    // 0xb965a4: r2 = LoadInt32Instr(r1)
    //     0xb965a4: sbfx            x2, x1, #1, #0x1f
    // 0xb965a8: stur            x2, [fp, #-0x40]
    // 0xb965ac: r1 = LoadInt32Instr(r3)
    //     0xb965ac: sbfx            x1, x3, #1, #0x1f
    // 0xb965b0: cmp             x2, x1
    // 0xb965b4: b.ne            #0xb965c0
    // 0xb965b8: mov             x1, x0
    // 0xb965bc: r0 = _growToNextCapacity()
    //     0xb965bc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb965c0: ldur            x2, [fp, #-0x10]
    // 0xb965c4: ldur            x3, [fp, #-0x40]
    // 0xb965c8: add             x0, x3, #1
    // 0xb965cc: lsl             x1, x0, #1
    // 0xb965d0: StoreField: r2->field_b = r1
    //     0xb965d0: stur            w1, [x2, #0xb]
    // 0xb965d4: LoadField: r1 = r2->field_f
    //     0xb965d4: ldur            w1, [x2, #0xf]
    // 0xb965d8: DecompressPointer r1
    //     0xb965d8: add             x1, x1, HEAP, lsl #32
    // 0xb965dc: ldur            x0, [fp, #-0x28]
    // 0xb965e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb965e0: add             x25, x1, x3, lsl #2
    //     0xb965e4: add             x25, x25, #0xf
    //     0xb965e8: str             w0, [x25]
    //     0xb965ec: tbz             w0, #0, #0xb96608
    //     0xb965f0: ldurb           w16, [x1, #-1]
    //     0xb965f4: ldurb           w17, [x0, #-1]
    //     0xb965f8: and             x16, x17, x16, lsr #2
    //     0xb965fc: tst             x16, HEAP, lsr #32
    //     0xb96600: b.eq            #0xb96608
    //     0xb96604: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb96608: ldur            x3, [fp, #-8]
    // 0xb9660c: LoadField: r0 = r3->field_b
    //     0xb9660c: ldur            w0, [x3, #0xb]
    // 0xb96610: DecompressPointer r0
    //     0xb96610: add             x0, x0, HEAP, lsl #32
    // 0xb96614: cmp             w0, NULL
    // 0xb96618: b.eq            #0xb96be0
    // 0xb9661c: LoadField: r4 = r0->field_f
    //     0xb9661c: ldur            w4, [x0, #0xf]
    // 0xb96620: DecompressPointer r4
    //     0xb96620: add             x4, x4, HEAP, lsl #32
    // 0xb96624: LoadField: r5 = r3->field_13
    //     0xb96624: ldur            x5, [x3, #0x13]
    // 0xb96628: LoadField: r0 = r4->field_b
    //     0xb96628: ldur            w0, [x4, #0xb]
    // 0xb9662c: r1 = LoadInt32Instr(r0)
    //     0xb9662c: sbfx            x1, x0, #1, #0x1f
    // 0xb96630: mov             x0, x1
    // 0xb96634: mov             x1, x5
    // 0xb96638: cmp             x1, x0
    // 0xb9663c: b.hs            #0xb96be4
    // 0xb96640: LoadField: r0 = r4->field_f
    //     0xb96640: ldur            w0, [x4, #0xf]
    // 0xb96644: DecompressPointer r0
    //     0xb96644: add             x0, x0, HEAP, lsl #32
    // 0xb96648: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb96648: add             x16, x0, x5, lsl #2
    //     0xb9664c: ldur            w1, [x16, #0xf]
    // 0xb96650: DecompressPointer r1
    //     0xb96650: add             x1, x1, HEAP, lsl #32
    // 0xb96654: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb96654: ldur            w0, [x1, #0x17]
    // 0xb96658: DecompressPointer r0
    //     0xb96658: add             x0, x0, HEAP, lsl #32
    // 0xb9665c: cmp             w0, NULL
    // 0xb96660: b.ne            #0xb9666c
    // 0xb96664: r4 = ""
    //     0xb96664: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb96668: b               #0xb96670
    // 0xb9666c: mov             x4, x0
    // 0xb96670: ldur            x0, [fp, #-0x20]
    // 0xb96674: stur            x4, [fp, #-0x28]
    // 0xb96678: LoadField: r1 = r0->field_13
    //     0xb96678: ldur            w1, [x0, #0x13]
    // 0xb9667c: DecompressPointer r1
    //     0xb9667c: add             x1, x1, HEAP, lsl #32
    // 0xb96680: r0 = of()
    //     0xb96680: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb96684: LoadField: r1 = r0->field_87
    //     0xb96684: ldur            w1, [x0, #0x87]
    // 0xb96688: DecompressPointer r1
    //     0xb96688: add             x1, x1, HEAP, lsl #32
    // 0xb9668c: LoadField: r0 = r1->field_2b
    //     0xb9668c: ldur            w0, [x1, #0x2b]
    // 0xb96690: DecompressPointer r0
    //     0xb96690: add             x0, x0, HEAP, lsl #32
    // 0xb96694: LoadField: r1 = r0->field_13
    //     0xb96694: ldur            w1, [x0, #0x13]
    // 0xb96698: DecompressPointer r1
    //     0xb96698: add             x1, x1, HEAP, lsl #32
    // 0xb9669c: r16 = Instance_Color
    //     0xb9669c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb966a0: stp             x16, x1, [SP]
    // 0xb966a4: r1 = Instance_TextStyle
    //     0xb966a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xb966a8: ldr             x1, [x1, #0x9b0]
    // 0xb966ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb966ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb966b0: ldr             x4, [x4, #0x9b8]
    // 0xb966b4: r0 = copyWith()
    //     0xb966b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb966b8: ldur            x2, [fp, #-0x20]
    // 0xb966bc: stur            x0, [fp, #-0x30]
    // 0xb966c0: LoadField: r1 = r2->field_13
    //     0xb966c0: ldur            w1, [x2, #0x13]
    // 0xb966c4: DecompressPointer r1
    //     0xb966c4: add             x1, x1, HEAP, lsl #32
    // 0xb966c8: r0 = of()
    //     0xb966c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb966cc: LoadField: r1 = r0->field_87
    //     0xb966cc: ldur            w1, [x0, #0x87]
    // 0xb966d0: DecompressPointer r1
    //     0xb966d0: add             x1, x1, HEAP, lsl #32
    // 0xb966d4: LoadField: r0 = r1->field_7
    //     0xb966d4: ldur            w0, [x1, #7]
    // 0xb966d8: DecompressPointer r0
    //     0xb966d8: add             x0, x0, HEAP, lsl #32
    // 0xb966dc: r16 = 12.000000
    //     0xb966dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb966e0: ldr             x16, [x16, #0x9e8]
    // 0xb966e4: r30 = Instance_Color
    //     0xb966e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb966e8: stp             lr, x16, [SP, #8]
    // 0xb966ec: r16 = Instance_FontWeight
    //     0xb966ec: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb966f0: ldr             x16, [x16, #0x20]
    // 0xb966f4: str             x16, [SP]
    // 0xb966f8: mov             x1, x0
    // 0xb966fc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb966fc: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb96700: ldr             x4, [x4, #0xc48]
    // 0xb96704: r0 = copyWith()
    //     0xb96704: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb96708: ldur            x2, [fp, #-0x20]
    // 0xb9670c: stur            x0, [fp, #-0x38]
    // 0xb96710: LoadField: r1 = r2->field_13
    //     0xb96710: ldur            w1, [x2, #0x13]
    // 0xb96714: DecompressPointer r1
    //     0xb96714: add             x1, x1, HEAP, lsl #32
    // 0xb96718: r0 = of()
    //     0xb96718: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9671c: LoadField: r1 = r0->field_87
    //     0xb9671c: ldur            w1, [x0, #0x87]
    // 0xb96720: DecompressPointer r1
    //     0xb96720: add             x1, x1, HEAP, lsl #32
    // 0xb96724: LoadField: r0 = r1->field_7
    //     0xb96724: ldur            w0, [x1, #7]
    // 0xb96728: DecompressPointer r0
    //     0xb96728: add             x0, x0, HEAP, lsl #32
    // 0xb9672c: r16 = 12.000000
    //     0xb9672c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb96730: ldr             x16, [x16, #0x9e8]
    // 0xb96734: r30 = Instance_Color
    //     0xb96734: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb96738: stp             lr, x16, [SP, #8]
    // 0xb9673c: r16 = Instance_FontWeight
    //     0xb9673c: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb96740: ldr             x16, [x16, #0x20]
    // 0xb96744: str             x16, [SP]
    // 0xb96748: mov             x1, x0
    // 0xb9674c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb9674c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb96750: ldr             x4, [x4, #0xc48]
    // 0xb96754: r0 = copyWith()
    //     0xb96754: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb96758: mov             x1, x0
    // 0xb9675c: ldur            x0, [fp, #-8]
    // 0xb96760: stur            x1, [fp, #-0x50]
    // 0xb96764: LoadField: r2 = r0->field_27
    //     0xb96764: ldur            w2, [x0, #0x27]
    // 0xb96768: DecompressPointer r2
    //     0xb96768: add             x2, x2, HEAP, lsl #32
    // 0xb9676c: stur            x2, [fp, #-0x48]
    // 0xb96770: r0 = ReadMoreText()
    //     0xb96770: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb96774: mov             x1, x0
    // 0xb96778: ldur            x0, [fp, #-0x28]
    // 0xb9677c: stur            x1, [fp, #-8]
    // 0xb96780: StoreField: r1->field_3f = r0
    //     0xb96780: stur            w0, [x1, #0x3f]
    // 0xb96784: ldur            x0, [fp, #-0x48]
    // 0xb96788: StoreField: r1->field_b = r0
    //     0xb96788: stur            w0, [x1, #0xb]
    // 0xb9678c: r0 = " Read Less"
    //     0xb9678c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xb96790: ldr             x0, [x0, #0x9c0]
    // 0xb96794: StoreField: r1->field_43 = r0
    //     0xb96794: stur            w0, [x1, #0x43]
    // 0xb96798: r0 = "Read More"
    //     0xb96798: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xb9679c: ldr             x0, [x0, #0x9c8]
    // 0xb967a0: StoreField: r1->field_47 = r0
    //     0xb967a0: stur            w0, [x1, #0x47]
    // 0xb967a4: r0 = 240
    //     0xb967a4: movz            x0, #0xf0
    // 0xb967a8: StoreField: r1->field_f = r0
    //     0xb967a8: stur            x0, [x1, #0xf]
    // 0xb967ac: r0 = 2
    //     0xb967ac: movz            x0, #0x2
    // 0xb967b0: ArrayStore: r1[0] = r0  ; List_8
    //     0xb967b0: stur            x0, [x1, #0x17]
    // 0xb967b4: r0 = Instance_TrimMode
    //     0xb967b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb967b8: ldr             x0, [x0, #0x9d0]
    // 0xb967bc: StoreField: r1->field_1f = r0
    //     0xb967bc: stur            w0, [x1, #0x1f]
    // 0xb967c0: ldur            x0, [fp, #-0x30]
    // 0xb967c4: StoreField: r1->field_4f = r0
    //     0xb967c4: stur            w0, [x1, #0x4f]
    // 0xb967c8: r0 = Instance_TextAlign
    //     0xb967c8: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb967cc: StoreField: r1->field_53 = r0
    //     0xb967cc: stur            w0, [x1, #0x53]
    // 0xb967d0: ldur            x0, [fp, #-0x38]
    // 0xb967d4: StoreField: r1->field_23 = r0
    //     0xb967d4: stur            w0, [x1, #0x23]
    // 0xb967d8: ldur            x0, [fp, #-0x50]
    // 0xb967dc: StoreField: r1->field_27 = r0
    //     0xb967dc: stur            w0, [x1, #0x27]
    // 0xb967e0: r0 = "… "
    //     0xb967e0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb967e4: ldr             x0, [x0, #0x9d8]
    // 0xb967e8: StoreField: r1->field_3b = r0
    //     0xb967e8: stur            w0, [x1, #0x3b]
    // 0xb967ec: r0 = true
    //     0xb967ec: add             x0, NULL, #0x20  ; true
    // 0xb967f0: StoreField: r1->field_37 = r0
    //     0xb967f0: stur            w0, [x1, #0x37]
    // 0xb967f4: r0 = Container()
    //     0xb967f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb967f8: stur            x0, [fp, #-0x28]
    // 0xb967fc: r16 = Instance_EdgeInsets
    //     0xb967fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb96800: ldr             x16, [x16, #0x668]
    // 0xb96804: r30 = Instance_BoxDecoration
    //     0xb96804: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb96808: ldr             lr, [lr, #0x5a8]
    // 0xb9680c: stp             lr, x16, [SP, #8]
    // 0xb96810: ldur            x16, [fp, #-8]
    // 0xb96814: str             x16, [SP]
    // 0xb96818: mov             x1, x0
    // 0xb9681c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xb9681c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb96820: ldr             x4, [x4, #0x610]
    // 0xb96824: r0 = Container()
    //     0xb96824: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb96828: r0 = Padding()
    //     0xb96828: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9682c: mov             x2, x0
    // 0xb96830: r0 = Instance_EdgeInsets
    //     0xb96830: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb96834: ldr             x0, [x0, #0xa00]
    // 0xb96838: stur            x2, [fp, #-8]
    // 0xb9683c: StoreField: r2->field_f = r0
    //     0xb9683c: stur            w0, [x2, #0xf]
    // 0xb96840: ldur            x0, [fp, #-0x28]
    // 0xb96844: StoreField: r2->field_b = r0
    //     0xb96844: stur            w0, [x2, #0xb]
    // 0xb96848: ldur            x0, [fp, #-0x10]
    // 0xb9684c: LoadField: r1 = r0->field_b
    //     0xb9684c: ldur            w1, [x0, #0xb]
    // 0xb96850: LoadField: r3 = r0->field_f
    //     0xb96850: ldur            w3, [x0, #0xf]
    // 0xb96854: DecompressPointer r3
    //     0xb96854: add             x3, x3, HEAP, lsl #32
    // 0xb96858: LoadField: r4 = r3->field_b
    //     0xb96858: ldur            w4, [x3, #0xb]
    // 0xb9685c: r3 = LoadInt32Instr(r1)
    //     0xb9685c: sbfx            x3, x1, #1, #0x1f
    // 0xb96860: stur            x3, [fp, #-0x40]
    // 0xb96864: r1 = LoadInt32Instr(r4)
    //     0xb96864: sbfx            x1, x4, #1, #0x1f
    // 0xb96868: cmp             x3, x1
    // 0xb9686c: b.ne            #0xb96878
    // 0xb96870: mov             x1, x0
    // 0xb96874: r0 = _growToNextCapacity()
    //     0xb96874: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96878: ldur            x2, [fp, #-0x10]
    // 0xb9687c: ldur            x3, [fp, #-0x40]
    // 0xb96880: add             x0, x3, #1
    // 0xb96884: lsl             x1, x0, #1
    // 0xb96888: StoreField: r2->field_b = r1
    //     0xb96888: stur            w1, [x2, #0xb]
    // 0xb9688c: LoadField: r1 = r2->field_f
    //     0xb9688c: ldur            w1, [x2, #0xf]
    // 0xb96890: DecompressPointer r1
    //     0xb96890: add             x1, x1, HEAP, lsl #32
    // 0xb96894: ldur            x0, [fp, #-8]
    // 0xb96898: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb96898: add             x25, x1, x3, lsl #2
    //     0xb9689c: add             x25, x25, #0xf
    //     0xb968a0: str             w0, [x25]
    //     0xb968a4: tbz             w0, #0, #0xb968c0
    //     0xb968a8: ldurb           w16, [x1, #-1]
    //     0xb968ac: ldurb           w17, [x0, #-1]
    //     0xb968b0: and             x16, x17, x16, lsr #2
    //     0xb968b4: tst             x16, HEAP, lsr #32
    //     0xb968b8: b.eq            #0xb968c0
    //     0xb968bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb968c0: r0 = GestureDetector()
    //     0xb968c0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb968c4: ldur            x2, [fp, #-0x20]
    // 0xb968c8: r1 = Function '<anonymous closure>':.
    //     0xb968c8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fd0] AnonymousClosure: (0xaa5490), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb968cc: ldr             x1, [x1, #0xfd0]
    // 0xb968d0: stur            x0, [fp, #-8]
    // 0xb968d4: r0 = AllocateClosure()
    //     0xb968d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb968d8: ldur            x2, [fp, #-0x20]
    // 0xb968dc: r1 = Function '<anonymous closure>':.
    //     0xb968dc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fd8] AnonymousClosure: (0xb96be8), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb94ab4)
    //     0xb968e0: ldr             x1, [x1, #0xfd8]
    // 0xb968e4: stur            x0, [fp, #-0x20]
    // 0xb968e8: r0 = AllocateClosure()
    //     0xb968e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb968ec: ldur            x16, [fp, #-0x20]
    // 0xb968f0: stp             x0, x16, [SP, #8]
    // 0xb968f4: r16 = Instance_Icon
    //     0xb968f4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51eb8] Obj!Icon@d66531
    //     0xb968f8: ldr             x16, [x16, #0xeb8]
    // 0xb968fc: str             x16, [SP]
    // 0xb96900: ldur            x1, [fp, #-8]
    // 0xb96904: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xb96904: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xb96908: ldr             x4, [x4, #0xa20]
    // 0xb9690c: r0 = GestureDetector()
    //     0xb9690c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb96910: r0 = Align()
    //     0xb96910: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb96914: mov             x1, x0
    // 0xb96918: r0 = Instance_Alignment
    //     0xb96918: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb9691c: ldr             x0, [x0, #0xa78]
    // 0xb96920: stur            x1, [fp, #-0x20]
    // 0xb96924: StoreField: r1->field_f = r0
    //     0xb96924: stur            w0, [x1, #0xf]
    // 0xb96928: ldur            x0, [fp, #-8]
    // 0xb9692c: StoreField: r1->field_b = r0
    //     0xb9692c: stur            w0, [x1, #0xb]
    // 0xb96930: r0 = Padding()
    //     0xb96930: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb96934: mov             x2, x0
    // 0xb96938: r0 = Instance_EdgeInsets
    //     0xb96938: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ec0] Obj!EdgeInsets@d58af1
    //     0xb9693c: ldr             x0, [x0, #0xec0]
    // 0xb96940: stur            x2, [fp, #-8]
    // 0xb96944: StoreField: r2->field_f = r0
    //     0xb96944: stur            w0, [x2, #0xf]
    // 0xb96948: ldur            x0, [fp, #-0x20]
    // 0xb9694c: StoreField: r2->field_b = r0
    //     0xb9694c: stur            w0, [x2, #0xb]
    // 0xb96950: ldur            x0, [fp, #-0x10]
    // 0xb96954: LoadField: r1 = r0->field_b
    //     0xb96954: ldur            w1, [x0, #0xb]
    // 0xb96958: LoadField: r3 = r0->field_f
    //     0xb96958: ldur            w3, [x0, #0xf]
    // 0xb9695c: DecompressPointer r3
    //     0xb9695c: add             x3, x3, HEAP, lsl #32
    // 0xb96960: LoadField: r4 = r3->field_b
    //     0xb96960: ldur            w4, [x3, #0xb]
    // 0xb96964: r3 = LoadInt32Instr(r1)
    //     0xb96964: sbfx            x3, x1, #1, #0x1f
    // 0xb96968: stur            x3, [fp, #-0x40]
    // 0xb9696c: r1 = LoadInt32Instr(r4)
    //     0xb9696c: sbfx            x1, x4, #1, #0x1f
    // 0xb96970: cmp             x3, x1
    // 0xb96974: b.ne            #0xb96980
    // 0xb96978: mov             x1, x0
    // 0xb9697c: r0 = _growToNextCapacity()
    //     0xb9697c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb96980: ldur            x4, [fp, #-0x18]
    // 0xb96984: ldur            x2, [fp, #-0x10]
    // 0xb96988: ldur            x3, [fp, #-0x40]
    // 0xb9698c: add             x0, x3, #1
    // 0xb96990: lsl             x1, x0, #1
    // 0xb96994: StoreField: r2->field_b = r1
    //     0xb96994: stur            w1, [x2, #0xb]
    // 0xb96998: LoadField: r1 = r2->field_f
    //     0xb96998: ldur            w1, [x2, #0xf]
    // 0xb9699c: DecompressPointer r1
    //     0xb9699c: add             x1, x1, HEAP, lsl #32
    // 0xb969a0: ldur            x0, [fp, #-8]
    // 0xb969a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb969a4: add             x25, x1, x3, lsl #2
    //     0xb969a8: add             x25, x25, #0xf
    //     0xb969ac: str             w0, [x25]
    //     0xb969b0: tbz             w0, #0, #0xb969cc
    //     0xb969b4: ldurb           w16, [x1, #-1]
    //     0xb969b8: ldurb           w17, [x0, #-1]
    //     0xb969bc: and             x16, x17, x16, lsr #2
    //     0xb969c0: tst             x16, HEAP, lsr #32
    //     0xb969c4: b.eq            #0xb969cc
    //     0xb969c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb969cc: r0 = Column()
    //     0xb969cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb969d0: mov             x3, x0
    // 0xb969d4: r0 = Instance_Axis
    //     0xb969d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb969d8: stur            x3, [fp, #-8]
    // 0xb969dc: StoreField: r3->field_f = r0
    //     0xb969dc: stur            w0, [x3, #0xf]
    // 0xb969e0: r4 = Instance_MainAxisAlignment
    //     0xb969e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb969e4: ldr             x4, [x4, #0xa08]
    // 0xb969e8: StoreField: r3->field_13 = r4
    //     0xb969e8: stur            w4, [x3, #0x13]
    // 0xb969ec: r5 = Instance_MainAxisSize
    //     0xb969ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb969f0: ldr             x5, [x5, #0xa10]
    // 0xb969f4: ArrayStore: r3[0] = r5  ; List_4
    //     0xb969f4: stur            w5, [x3, #0x17]
    // 0xb969f8: r1 = Instance_CrossAxisAlignment
    //     0xb969f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb969fc: ldr             x1, [x1, #0x890]
    // 0xb96a00: StoreField: r3->field_1b = r1
    //     0xb96a00: stur            w1, [x3, #0x1b]
    // 0xb96a04: r6 = Instance_VerticalDirection
    //     0xb96a04: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb96a08: ldr             x6, [x6, #0xa20]
    // 0xb96a0c: StoreField: r3->field_23 = r6
    //     0xb96a0c: stur            w6, [x3, #0x23]
    // 0xb96a10: r7 = Instance_Clip
    //     0xb96a10: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb96a14: ldr             x7, [x7, #0x38]
    // 0xb96a18: StoreField: r3->field_2b = r7
    //     0xb96a18: stur            w7, [x3, #0x2b]
    // 0xb96a1c: StoreField: r3->field_2f = rZR
    //     0xb96a1c: stur            xzr, [x3, #0x2f]
    // 0xb96a20: ldur            x1, [fp, #-0x10]
    // 0xb96a24: StoreField: r3->field_b = r1
    //     0xb96a24: stur            w1, [x3, #0xb]
    // 0xb96a28: r1 = Null
    //     0xb96a28: mov             x1, NULL
    // 0xb96a2c: r2 = 4
    //     0xb96a2c: movz            x2, #0x4
    // 0xb96a30: r0 = AllocateArray()
    //     0xb96a30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb96a34: mov             x2, x0
    // 0xb96a38: ldur            x0, [fp, #-0x18]
    // 0xb96a3c: stur            x2, [fp, #-0x10]
    // 0xb96a40: StoreField: r2->field_f = r0
    //     0xb96a40: stur            w0, [x2, #0xf]
    // 0xb96a44: ldur            x0, [fp, #-8]
    // 0xb96a48: StoreField: r2->field_13 = r0
    //     0xb96a48: stur            w0, [x2, #0x13]
    // 0xb96a4c: r1 = <Widget>
    //     0xb96a4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb96a50: r0 = AllocateGrowableArray()
    //     0xb96a50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb96a54: mov             x1, x0
    // 0xb96a58: ldur            x0, [fp, #-0x10]
    // 0xb96a5c: stur            x1, [fp, #-8]
    // 0xb96a60: StoreField: r1->field_f = r0
    //     0xb96a60: stur            w0, [x1, #0xf]
    // 0xb96a64: r0 = 4
    //     0xb96a64: movz            x0, #0x4
    // 0xb96a68: StoreField: r1->field_b = r0
    //     0xb96a68: stur            w0, [x1, #0xb]
    // 0xb96a6c: r0 = Column()
    //     0xb96a6c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb96a70: mov             x1, x0
    // 0xb96a74: r0 = Instance_Axis
    //     0xb96a74: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb96a78: stur            x1, [fp, #-0x10]
    // 0xb96a7c: StoreField: r1->field_f = r0
    //     0xb96a7c: stur            w0, [x1, #0xf]
    // 0xb96a80: r0 = Instance_MainAxisAlignment
    //     0xb96a80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb96a84: ldr             x0, [x0, #0xa08]
    // 0xb96a88: StoreField: r1->field_13 = r0
    //     0xb96a88: stur            w0, [x1, #0x13]
    // 0xb96a8c: r0 = Instance_MainAxisSize
    //     0xb96a8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb96a90: ldr             x0, [x0, #0xa10]
    // 0xb96a94: ArrayStore: r1[0] = r0  ; List_4
    //     0xb96a94: stur            w0, [x1, #0x17]
    // 0xb96a98: r0 = Instance_CrossAxisAlignment
    //     0xb96a98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb96a9c: ldr             x0, [x0, #0xa18]
    // 0xb96aa0: StoreField: r1->field_1b = r0
    //     0xb96aa0: stur            w0, [x1, #0x1b]
    // 0xb96aa4: r0 = Instance_VerticalDirection
    //     0xb96aa4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb96aa8: ldr             x0, [x0, #0xa20]
    // 0xb96aac: StoreField: r1->field_23 = r0
    //     0xb96aac: stur            w0, [x1, #0x23]
    // 0xb96ab0: r0 = Instance_Clip
    //     0xb96ab0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb96ab4: ldr             x0, [x0, #0x38]
    // 0xb96ab8: StoreField: r1->field_2b = r0
    //     0xb96ab8: stur            w0, [x1, #0x2b]
    // 0xb96abc: StoreField: r1->field_2f = rZR
    //     0xb96abc: stur            xzr, [x1, #0x2f]
    // 0xb96ac0: ldur            x0, [fp, #-8]
    // 0xb96ac4: StoreField: r1->field_b = r0
    //     0xb96ac4: stur            w0, [x1, #0xb]
    // 0xb96ac8: r0 = SafeArea()
    //     0xb96ac8: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb96acc: mov             x1, x0
    // 0xb96ad0: r0 = true
    //     0xb96ad0: add             x0, NULL, #0x20  ; true
    // 0xb96ad4: stur            x1, [fp, #-8]
    // 0xb96ad8: StoreField: r1->field_b = r0
    //     0xb96ad8: stur            w0, [x1, #0xb]
    // 0xb96adc: StoreField: r1->field_f = r0
    //     0xb96adc: stur            w0, [x1, #0xf]
    // 0xb96ae0: StoreField: r1->field_13 = r0
    //     0xb96ae0: stur            w0, [x1, #0x13]
    // 0xb96ae4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb96ae4: stur            w0, [x1, #0x17]
    // 0xb96ae8: r2 = Instance_EdgeInsets
    //     0xb96ae8: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb96aec: StoreField: r1->field_1b = r2
    //     0xb96aec: stur            w2, [x1, #0x1b]
    // 0xb96af0: r2 = false
    //     0xb96af0: add             x2, NULL, #0x30  ; false
    // 0xb96af4: StoreField: r1->field_1f = r2
    //     0xb96af4: stur            w2, [x1, #0x1f]
    // 0xb96af8: ldur            x3, [fp, #-0x10]
    // 0xb96afc: StoreField: r1->field_23 = r3
    //     0xb96afc: stur            w3, [x1, #0x23]
    // 0xb96b00: r0 = Scaffold()
    //     0xb96b00: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb96b04: ldur            x1, [fp, #-8]
    // 0xb96b08: ArrayStore: r0[0] = r1  ; List_4
    //     0xb96b08: stur            w1, [x0, #0x17]
    // 0xb96b0c: r1 = Instance_Color
    //     0xb96b0c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb96b10: StoreField: r0->field_33 = r1
    //     0xb96b10: stur            w1, [x0, #0x33]
    // 0xb96b14: r1 = true
    //     0xb96b14: add             x1, NULL, #0x20  ; true
    // 0xb96b18: StoreField: r0->field_43 = r1
    //     0xb96b18: stur            w1, [x0, #0x43]
    // 0xb96b1c: r1 = false
    //     0xb96b1c: add             x1, NULL, #0x30  ; false
    // 0xb96b20: StoreField: r0->field_b = r1
    //     0xb96b20: stur            w1, [x0, #0xb]
    // 0xb96b24: StoreField: r0->field_f = r1
    //     0xb96b24: stur            w1, [x0, #0xf]
    // 0xb96b28: LeaveFrame
    //     0xb96b28: mov             SP, fp
    //     0xb96b2c: ldp             fp, lr, [SP], #0x10
    // 0xb96b30: ret
    //     0xb96b30: ret             
    // 0xb96b34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb96b34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb96b38: b               #0xb94ad4
    // 0xb96b3c: r9 = _pageController
    //     0xb96b3c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54fe0] Field <_RatingReviewAllMediaOnTapImageState@1633116981._pageController@1633116981>: late (offset: 0x24)
    //     0xb96b40: ldr             x9, [x9, #0xfe0]
    // 0xb96b44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb96b44: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb96b48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb96b50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb96b54: b               #0xb94c18
    // 0xb96b58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b80: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb96b80: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb96b84: r0 = RangeErrorSharedWithFPURegs()
    //     0xb96b84: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xb96b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b8c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96b90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96b94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96b94: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96b98: SaveReg d0
    //     0xb96b98: str             q0, [SP, #-0x10]!
    // 0xb96b9c: r0 = AllocateDouble()
    //     0xb96b9c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb96ba0: RestoreReg d0
    //     0xb96ba0: ldr             q0, [SP], #0x10
    // 0xb96ba4: b               #0xb95a84
    // 0xb96ba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96ba8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96bb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96bb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bb4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96bb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96bb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bbc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96bc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96bc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96bc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96bc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96bd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96bd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96bd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96bd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96bdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96bdc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb96be0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96be0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96be4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96be4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb96be8, size: 0xd0
    // 0xb96be8: EnterFrame
    //     0xb96be8: stp             fp, lr, [SP, #-0x10]!
    //     0xb96bec: mov             fp, SP
    // 0xb96bf0: ldr             x0, [fp, #0x10]
    // 0xb96bf4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb96bf4: ldur            w1, [x0, #0x17]
    // 0xb96bf8: DecompressPointer r1
    //     0xb96bf8: add             x1, x1, HEAP, lsl #32
    // 0xb96bfc: CheckStackOverflow
    //     0xb96bfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb96c00: cmp             SP, x16
    //     0xb96c04: b.ls            #0xb96ca8
    // 0xb96c08: LoadField: r2 = r1->field_f
    //     0xb96c08: ldur            w2, [x1, #0xf]
    // 0xb96c0c: DecompressPointer r2
    //     0xb96c0c: add             x2, x2, HEAP, lsl #32
    // 0xb96c10: LoadField: r3 = r2->field_33
    //     0xb96c10: ldur            w3, [x2, #0x33]
    // 0xb96c14: DecompressPointer r3
    //     0xb96c14: add             x3, x3, HEAP, lsl #32
    // 0xb96c18: cmp             w3, NULL
    // 0xb96c1c: b.eq            #0xb96c98
    // 0xb96c20: LoadField: r4 = r1->field_13
    //     0xb96c20: ldur            w4, [x1, #0x13]
    // 0xb96c24: DecompressPointer r4
    //     0xb96c24: add             x4, x4, HEAP, lsl #32
    // 0xb96c28: LoadField: r0 = r2->field_b
    //     0xb96c28: ldur            w0, [x2, #0xb]
    // 0xb96c2c: DecompressPointer r0
    //     0xb96c2c: add             x0, x0, HEAP, lsl #32
    // 0xb96c30: cmp             w0, NULL
    // 0xb96c34: b.eq            #0xb96cb0
    // 0xb96c38: LoadField: r5 = r0->field_f
    //     0xb96c38: ldur            w5, [x0, #0xf]
    // 0xb96c3c: DecompressPointer r5
    //     0xb96c3c: add             x5, x5, HEAP, lsl #32
    // 0xb96c40: LoadField: r6 = r2->field_13
    //     0xb96c40: ldur            x6, [x2, #0x13]
    // 0xb96c44: LoadField: r0 = r5->field_b
    //     0xb96c44: ldur            w0, [x5, #0xb]
    // 0xb96c48: r1 = LoadInt32Instr(r0)
    //     0xb96c48: sbfx            x1, x0, #1, #0x1f
    // 0xb96c4c: mov             x0, x1
    // 0xb96c50: mov             x1, x6
    // 0xb96c54: cmp             x1, x0
    // 0xb96c58: b.hs            #0xb96cb4
    // 0xb96c5c: LoadField: r0 = r5->field_f
    //     0xb96c5c: ldur            w0, [x5, #0xf]
    // 0xb96c60: DecompressPointer r0
    //     0xb96c60: add             x0, x0, HEAP, lsl #32
    // 0xb96c64: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb96c64: add             x16, x0, x6, lsl #2
    //     0xb96c68: ldur            w1, [x16, #0xf]
    // 0xb96c6c: DecompressPointer r1
    //     0xb96c6c: add             x1, x1, HEAP, lsl #32
    // 0xb96c70: LoadField: r0 = r1->field_b
    //     0xb96c70: ldur            w0, [x1, #0xb]
    // 0xb96c74: DecompressPointer r0
    //     0xb96c74: add             x0, x0, HEAP, lsl #32
    // 0xb96c78: cmp             w0, NULL
    // 0xb96c7c: b.ne            #0xb96c88
    // 0xb96c80: r5 = ""
    //     0xb96c80: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb96c84: b               #0xb96c8c
    // 0xb96c88: mov             x5, x0
    // 0xb96c8c: mov             x1, x2
    // 0xb96c90: mov             x2, x4
    // 0xb96c94: r0 = showMenuItem()
    //     0xb96c94: bl              #0xb96cb8  ; [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem
    // 0xb96c98: r0 = Null
    //     0xb96c98: mov             x0, NULL
    // 0xb96c9c: LeaveFrame
    //     0xb96c9c: mov             SP, fp
    //     0xb96ca0: ldp             fp, lr, [SP], #0x10
    // 0xb96ca4: ret
    //     0xb96ca4: ret             
    // 0xb96ca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb96ca8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb96cac: b               #0xb96c08
    // 0xb96cb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb96cb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb96cb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb96cb4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xb96cb8, size: 0x744
    // 0xb96cb8: EnterFrame
    //     0xb96cb8: stp             fp, lr, [SP, #-0x10]!
    //     0xb96cbc: mov             fp, SP
    // 0xb96cc0: AllocStack(0xa0)
    //     0xb96cc0: sub             SP, SP, #0xa0
    // 0xb96cc4: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xb96cc4: mov             x0, x1
    //     0xb96cc8: stur            x1, [fp, #-8]
    //     0xb96ccc: mov             x1, x2
    //     0xb96cd0: stur            x2, [fp, #-0x10]
    //     0xb96cd4: mov             x2, x5
    //     0xb96cd8: stur            x3, [fp, #-0x18]
    //     0xb96cdc: stur            x5, [fp, #-0x20]
    // 0xb96ce0: CheckStackOverflow
    //     0xb96ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb96ce4: cmp             SP, x16
    //     0xb96ce8: b.ls            #0xb97374
    // 0xb96cec: r1 = 2
    //     0xb96cec: movz            x1, #0x2
    // 0xb96cf0: r0 = AllocateContext()
    //     0xb96cf0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb96cf4: mov             x4, x0
    // 0xb96cf8: ldur            x3, [fp, #-8]
    // 0xb96cfc: stur            x4, [fp, #-0x28]
    // 0xb96d00: StoreField: r4->field_f = r3
    //     0xb96d00: stur            w3, [x4, #0xf]
    // 0xb96d04: ldur            x2, [fp, #-0x20]
    // 0xb96d08: StoreField: r4->field_13 = r2
    //     0xb96d08: stur            w2, [x4, #0x13]
    // 0xb96d0c: LoadField: r1 = r3->field_2f
    //     0xb96d0c: ldur            w1, [x3, #0x2f]
    // 0xb96d10: DecompressPointer r1
    //     0xb96d10: add             x1, x1, HEAP, lsl #32
    // 0xb96d14: r0 = LoadClassIdInstr(r1)
    //     0xb96d14: ldur            x0, [x1, #-1]
    //     0xb96d18: ubfx            x0, x0, #0xc, #0x14
    // 0xb96d1c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb96d1c: sub             lr, x0, #0xfe
    //     0xb96d20: ldr             lr, [x21, lr, lsl #3]
    //     0xb96d24: blr             lr
    // 0xb96d28: r1 = 60
    //     0xb96d28: movz            x1, #0x3c
    // 0xb96d2c: branchIfSmi(r0, 0xb96d38)
    //     0xb96d2c: tbz             w0, #0, #0xb96d38
    // 0xb96d30: r1 = LoadClassIdInstr(r0)
    //     0xb96d30: ldur            x1, [x0, #-1]
    //     0xb96d34: ubfx            x1, x1, #0xc, #0x14
    // 0xb96d38: r16 = true
    //     0xb96d38: add             x16, NULL, #0x20  ; true
    // 0xb96d3c: stp             x16, x0, [SP]
    // 0xb96d40: mov             x0, x1
    // 0xb96d44: mov             lr, x0
    // 0xb96d48: ldr             lr, [x21, lr, lsl #3]
    // 0xb96d4c: blr             lr
    // 0xb96d50: tbnz            w0, #4, #0xb96d5c
    // 0xb96d54: d0 = 100.000000
    //     0xb96d54: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb96d58: b               #0xb96d64
    // 0xb96d5c: d0 = 120.000000
    //     0xb96d5c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xb96d60: ldr             d0, [x17, #0xa38]
    // 0xb96d64: ldur            x0, [fp, #-0x18]
    // 0xb96d68: stur            d0, [fp, #-0x58]
    // 0xb96d6c: r0 = BoxConstraints()
    //     0xb96d6c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb96d70: stur            x0, [fp, #-0x20]
    // 0xb96d74: StoreField: r0->field_7 = rZR
    //     0xb96d74: stur            xzr, [x0, #7]
    // 0xb96d78: ldur            d0, [fp, #-0x58]
    // 0xb96d7c: StoreField: r0->field_f = d0
    //     0xb96d7c: stur            d0, [x0, #0xf]
    // 0xb96d80: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb96d80: stur            xzr, [x0, #0x17]
    // 0xb96d84: d0 = inf
    //     0xb96d84: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb96d88: StoreField: r0->field_1f = d0
    //     0xb96d88: stur            d0, [x0, #0x1f]
    // 0xb96d8c: ldur            x1, [fp, #-0x18]
    // 0xb96d90: cmp             w1, NULL
    // 0xb96d94: b.ne            #0xb96da0
    // 0xb96d98: r2 = Null
    //     0xb96d98: mov             x2, NULL
    // 0xb96d9c: b               #0xb96dcc
    // 0xb96da0: LoadField: d0 = r1->field_7
    //     0xb96da0: ldur            d0, [x1, #7]
    // 0xb96da4: r2 = inline_Allocate_Double()
    //     0xb96da4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb96da8: add             x2, x2, #0x10
    //     0xb96dac: cmp             x3, x2
    //     0xb96db0: b.ls            #0xb9737c
    //     0xb96db4: str             x2, [THR, #0x50]  ; THR::top
    //     0xb96db8: sub             x2, x2, #0xf
    //     0xb96dbc: movz            x3, #0xe15c
    //     0xb96dc0: movk            x3, #0x3, lsl #16
    //     0xb96dc4: stur            x3, [x2, #-1]
    // 0xb96dc8: StoreField: r2->field_7 = d0
    //     0xb96dc8: stur            d0, [x2, #7]
    // 0xb96dcc: cmp             w2, NULL
    // 0xb96dd0: b.ne            #0xb96ddc
    // 0xb96dd4: d0 = 0.000000
    //     0xb96dd4: eor             v0.16b, v0.16b, v0.16b
    // 0xb96dd8: b               #0xb96de0
    // 0xb96ddc: LoadField: d0 = r2->field_7
    //     0xb96ddc: ldur            d0, [x2, #7]
    // 0xb96de0: stur            d0, [fp, #-0x70]
    // 0xb96de4: cmp             w1, NULL
    // 0xb96de8: b.ne            #0xb96df4
    // 0xb96dec: r2 = Null
    //     0xb96dec: mov             x2, NULL
    // 0xb96df0: b               #0xb96e20
    // 0xb96df4: LoadField: d1 = r1->field_f
    //     0xb96df4: ldur            d1, [x1, #0xf]
    // 0xb96df8: r2 = inline_Allocate_Double()
    //     0xb96df8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb96dfc: add             x2, x2, #0x10
    //     0xb96e00: cmp             x3, x2
    //     0xb96e04: b.ls            #0xb97398
    //     0xb96e08: str             x2, [THR, #0x50]  ; THR::top
    //     0xb96e0c: sub             x2, x2, #0xf
    //     0xb96e10: movz            x3, #0xe15c
    //     0xb96e14: movk            x3, #0x3, lsl #16
    //     0xb96e18: stur            x3, [x2, #-1]
    // 0xb96e1c: StoreField: r2->field_7 = d1
    //     0xb96e1c: stur            d1, [x2, #7]
    // 0xb96e20: cmp             w2, NULL
    // 0xb96e24: b.ne            #0xb96e30
    // 0xb96e28: d2 = 0.000000
    //     0xb96e28: eor             v2.16b, v2.16b, v2.16b
    // 0xb96e2c: b               #0xb96e38
    // 0xb96e30: LoadField: d1 = r2->field_7
    //     0xb96e30: ldur            d1, [x2, #7]
    // 0xb96e34: mov             v2.16b, v1.16b
    // 0xb96e38: d1 = 50.000000
    //     0xb96e38: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb96e3c: fsub            d3, d2, d1
    // 0xb96e40: stur            d3, [fp, #-0x68]
    // 0xb96e44: cmp             w1, NULL
    // 0xb96e48: b.ne            #0xb96e54
    // 0xb96e4c: r2 = Null
    //     0xb96e4c: mov             x2, NULL
    // 0xb96e50: b               #0xb96e80
    // 0xb96e54: LoadField: d2 = r1->field_7
    //     0xb96e54: ldur            d2, [x1, #7]
    // 0xb96e58: r2 = inline_Allocate_Double()
    //     0xb96e58: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb96e5c: add             x2, x2, #0x10
    //     0xb96e60: cmp             x3, x2
    //     0xb96e64: b.ls            #0xb973b4
    //     0xb96e68: str             x2, [THR, #0x50]  ; THR::top
    //     0xb96e6c: sub             x2, x2, #0xf
    //     0xb96e70: movz            x3, #0xe15c
    //     0xb96e74: movk            x3, #0x3, lsl #16
    //     0xb96e78: stur            x3, [x2, #-1]
    // 0xb96e7c: StoreField: r2->field_7 = d2
    //     0xb96e7c: stur            d2, [x2, #7]
    // 0xb96e80: cmp             w2, NULL
    // 0xb96e84: b.ne            #0xb96e90
    // 0xb96e88: d2 = 0.000000
    //     0xb96e88: eor             v2.16b, v2.16b, v2.16b
    // 0xb96e8c: b               #0xb96e94
    // 0xb96e90: LoadField: d2 = r2->field_7
    //     0xb96e90: ldur            d2, [x2, #7]
    // 0xb96e94: fadd            d4, d2, d1
    // 0xb96e98: stur            d4, [fp, #-0x60]
    // 0xb96e9c: cmp             w1, NULL
    // 0xb96ea0: b.ne            #0xb96eac
    // 0xb96ea4: r1 = Null
    //     0xb96ea4: mov             x1, NULL
    // 0xb96ea8: b               #0xb96ed8
    // 0xb96eac: LoadField: d1 = r1->field_f
    //     0xb96eac: ldur            d1, [x1, #0xf]
    // 0xb96eb0: r1 = inline_Allocate_Double()
    //     0xb96eb0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb96eb4: add             x1, x1, #0x10
    //     0xb96eb8: cmp             x2, x1
    //     0xb96ebc: b.ls            #0xb973d8
    //     0xb96ec0: str             x1, [THR, #0x50]  ; THR::top
    //     0xb96ec4: sub             x1, x1, #0xf
    //     0xb96ec8: movz            x2, #0xe15c
    //     0xb96ecc: movk            x2, #0x3, lsl #16
    //     0xb96ed0: stur            x2, [x1, #-1]
    // 0xb96ed4: StoreField: r1->field_7 = d1
    //     0xb96ed4: stur            d1, [x1, #7]
    // 0xb96ed8: cmp             w1, NULL
    // 0xb96edc: b.ne            #0xb96ee8
    // 0xb96ee0: d1 = 0.000000
    //     0xb96ee0: eor             v1.16b, v1.16b, v1.16b
    // 0xb96ee4: b               #0xb96eec
    // 0xb96ee8: LoadField: d1 = r1->field_7
    //     0xb96ee8: ldur            d1, [x1, #7]
    // 0xb96eec: ldur            x1, [fp, #-8]
    // 0xb96ef0: ldur            x2, [fp, #-0x28]
    // 0xb96ef4: stur            d1, [fp, #-0x58]
    // 0xb96ef8: r0 = RelativeRect()
    //     0xb96ef8: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xb96efc: mov             x3, x0
    // 0xb96f00: ldur            d0, [fp, #-0x70]
    // 0xb96f04: stur            x3, [fp, #-0x18]
    // 0xb96f08: StoreField: r3->field_7 = d0
    //     0xb96f08: stur            d0, [x3, #7]
    // 0xb96f0c: ldur            d0, [fp, #-0x68]
    // 0xb96f10: StoreField: r3->field_f = d0
    //     0xb96f10: stur            d0, [x3, #0xf]
    // 0xb96f14: ldur            d0, [fp, #-0x60]
    // 0xb96f18: ArrayStore: r3[0] = d0  ; List_8
    //     0xb96f18: stur            d0, [x3, #0x17]
    // 0xb96f1c: ldur            d0, [fp, #-0x58]
    // 0xb96f20: StoreField: r3->field_1f = d0
    //     0xb96f20: stur            d0, [x3, #0x1f]
    // 0xb96f24: ldur            x4, [fp, #-8]
    // 0xb96f28: LoadField: r1 = r4->field_2f
    //     0xb96f28: ldur            w1, [x4, #0x2f]
    // 0xb96f2c: DecompressPointer r1
    //     0xb96f2c: add             x1, x1, HEAP, lsl #32
    // 0xb96f30: ldur            x5, [fp, #-0x28]
    // 0xb96f34: LoadField: r2 = r5->field_13
    //     0xb96f34: ldur            w2, [x5, #0x13]
    // 0xb96f38: DecompressPointer r2
    //     0xb96f38: add             x2, x2, HEAP, lsl #32
    // 0xb96f3c: r0 = LoadClassIdInstr(r1)
    //     0xb96f3c: ldur            x0, [x1, #-1]
    //     0xb96f40: ubfx            x0, x0, #0xc, #0x14
    // 0xb96f44: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb96f44: sub             lr, x0, #0xfe
    //     0xb96f48: ldr             lr, [x21, lr, lsl #3]
    //     0xb96f4c: blr             lr
    // 0xb96f50: r1 = 60
    //     0xb96f50: movz            x1, #0x3c
    // 0xb96f54: branchIfSmi(r0, 0xb96f60)
    //     0xb96f54: tbz             w0, #0, #0xb96f60
    // 0xb96f58: r1 = LoadClassIdInstr(r0)
    //     0xb96f58: ldur            x1, [x0, #-1]
    //     0xb96f5c: ubfx            x1, x1, #0xc, #0x14
    // 0xb96f60: r16 = true
    //     0xb96f60: add             x16, NULL, #0x20  ; true
    // 0xb96f64: stp             x16, x0, [SP]
    // 0xb96f68: mov             x0, x1
    // 0xb96f6c: mov             lr, x0
    // 0xb96f70: ldr             lr, [x21, lr, lsl #3]
    // 0xb96f74: blr             lr
    // 0xb96f78: tbnz            w0, #4, #0xb96f84
    // 0xb96f7c: r4 = Null
    //     0xb96f7c: mov             x4, NULL
    // 0xb96f80: b               #0xb96f98
    // 0xb96f84: ldur            x2, [fp, #-0x28]
    // 0xb96f88: r1 = Function '<anonymous closure>':.
    //     0xb96f88: add             x1, PP, #0x54, lsl #12  ; [pp+0x54fe8] AnonymousClosure: (0xb97490), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xb96cb8)
    //     0xb96f8c: ldr             x1, [x1, #0xfe8]
    // 0xb96f90: r0 = AllocateClosure()
    //     0xb96f90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb96f94: mov             x4, x0
    // 0xb96f98: ldur            x0, [fp, #-8]
    // 0xb96f9c: ldur            x3, [fp, #-0x28]
    // 0xb96fa0: stur            x4, [fp, #-0x30]
    // 0xb96fa4: r1 = <Widget>
    //     0xb96fa4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb96fa8: r2 = 0
    //     0xb96fa8: movz            x2, #0
    // 0xb96fac: r0 = _GrowableList()
    //     0xb96fac: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb96fb0: mov             x4, x0
    // 0xb96fb4: ldur            x3, [fp, #-8]
    // 0xb96fb8: stur            x4, [fp, #-0x38]
    // 0xb96fbc: LoadField: r1 = r3->field_2f
    //     0xb96fbc: ldur            w1, [x3, #0x2f]
    // 0xb96fc0: DecompressPointer r1
    //     0xb96fc0: add             x1, x1, HEAP, lsl #32
    // 0xb96fc4: ldur            x5, [fp, #-0x28]
    // 0xb96fc8: LoadField: r2 = r5->field_13
    //     0xb96fc8: ldur            w2, [x5, #0x13]
    // 0xb96fcc: DecompressPointer r2
    //     0xb96fcc: add             x2, x2, HEAP, lsl #32
    // 0xb96fd0: r0 = LoadClassIdInstr(r1)
    //     0xb96fd0: ldur            x0, [x1, #-1]
    //     0xb96fd4: ubfx            x0, x0, #0xc, #0x14
    // 0xb96fd8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb96fd8: sub             lr, x0, #0xfe
    //     0xb96fdc: ldr             lr, [x21, lr, lsl #3]
    //     0xb96fe0: blr             lr
    // 0xb96fe4: r1 = 60
    //     0xb96fe4: movz            x1, #0x3c
    // 0xb96fe8: branchIfSmi(r0, 0xb96ff4)
    //     0xb96fe8: tbz             w0, #0, #0xb96ff4
    // 0xb96fec: r1 = LoadClassIdInstr(r0)
    //     0xb96fec: ldur            x1, [x0, #-1]
    //     0xb96ff0: ubfx            x1, x1, #0xc, #0x14
    // 0xb96ff4: r16 = true
    //     0xb96ff4: add             x16, NULL, #0x20  ; true
    // 0xb96ff8: stp             x16, x0, [SP]
    // 0xb96ffc: mov             x0, x1
    // 0xb97000: mov             lr, x0
    // 0xb97004: ldr             lr, [x21, lr, lsl #3]
    // 0xb97008: blr             lr
    // 0xb9700c: tbnz            w0, #4, #0xb97070
    // 0xb97010: ldur            x0, [fp, #-0x38]
    // 0xb97014: LoadField: r1 = r0->field_b
    //     0xb97014: ldur            w1, [x0, #0xb]
    // 0xb97018: LoadField: r2 = r0->field_f
    //     0xb97018: ldur            w2, [x0, #0xf]
    // 0xb9701c: DecompressPointer r2
    //     0xb9701c: add             x2, x2, HEAP, lsl #32
    // 0xb97020: LoadField: r3 = r2->field_b
    //     0xb97020: ldur            w3, [x2, #0xb]
    // 0xb97024: r2 = LoadInt32Instr(r1)
    //     0xb97024: sbfx            x2, x1, #1, #0x1f
    // 0xb97028: stur            x2, [fp, #-0x40]
    // 0xb9702c: r1 = LoadInt32Instr(r3)
    //     0xb9702c: sbfx            x1, x3, #1, #0x1f
    // 0xb97030: cmp             x2, x1
    // 0xb97034: b.ne            #0xb97040
    // 0xb97038: mov             x1, x0
    // 0xb9703c: r0 = _growToNextCapacity()
    //     0xb9703c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97040: ldur            x0, [fp, #-0x38]
    // 0xb97044: ldur            x1, [fp, #-0x40]
    // 0xb97048: add             x2, x1, #1
    // 0xb9704c: lsl             x3, x2, #1
    // 0xb97050: StoreField: r0->field_b = r3
    //     0xb97050: stur            w3, [x0, #0xb]
    // 0xb97054: LoadField: r2 = r0->field_f
    //     0xb97054: ldur            w2, [x0, #0xf]
    // 0xb97058: DecompressPointer r2
    //     0xb97058: add             x2, x2, HEAP, lsl #32
    // 0xb9705c: add             x3, x2, x1, lsl #2
    // 0xb97060: r16 = Instance_Icon
    //     0xb97060: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xb97064: ldr             x16, [x16, #0xa48]
    // 0xb97068: StoreField: r3->field_f = r16
    //     0xb97068: stur            w16, [x3, #0xf]
    // 0xb9706c: b               #0xb97074
    // 0xb97070: ldur            x0, [fp, #-0x38]
    // 0xb97074: LoadField: r1 = r0->field_b
    //     0xb97074: ldur            w1, [x0, #0xb]
    // 0xb97078: LoadField: r2 = r0->field_f
    //     0xb97078: ldur            w2, [x0, #0xf]
    // 0xb9707c: DecompressPointer r2
    //     0xb9707c: add             x2, x2, HEAP, lsl #32
    // 0xb97080: LoadField: r3 = r2->field_b
    //     0xb97080: ldur            w3, [x2, #0xb]
    // 0xb97084: r2 = LoadInt32Instr(r1)
    //     0xb97084: sbfx            x2, x1, #1, #0x1f
    // 0xb97088: stur            x2, [fp, #-0x40]
    // 0xb9708c: r1 = LoadInt32Instr(r3)
    //     0xb9708c: sbfx            x1, x3, #1, #0x1f
    // 0xb97090: cmp             x2, x1
    // 0xb97094: b.ne            #0xb970a0
    // 0xb97098: mov             x1, x0
    // 0xb9709c: r0 = _growToNextCapacity()
    //     0xb9709c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb970a0: ldur            x1, [fp, #-8]
    // 0xb970a4: ldur            x4, [fp, #-0x28]
    // 0xb970a8: ldur            x3, [fp, #-0x38]
    // 0xb970ac: ldur            x0, [fp, #-0x40]
    // 0xb970b0: add             x2, x0, #1
    // 0xb970b4: lsl             x5, x2, #1
    // 0xb970b8: StoreField: r3->field_b = r5
    //     0xb970b8: stur            w5, [x3, #0xb]
    // 0xb970bc: LoadField: r2 = r3->field_f
    //     0xb970bc: ldur            w2, [x3, #0xf]
    // 0xb970c0: DecompressPointer r2
    //     0xb970c0: add             x2, x2, HEAP, lsl #32
    // 0xb970c4: add             x5, x2, x0, lsl #2
    // 0xb970c8: r16 = Instance_SizedBox
    //     0xb970c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb970cc: ldr             x16, [x16, #0xa50]
    // 0xb970d0: StoreField: r5->field_f = r16
    //     0xb970d0: stur            w16, [x5, #0xf]
    // 0xb970d4: LoadField: r0 = r1->field_2f
    //     0xb970d4: ldur            w0, [x1, #0x2f]
    // 0xb970d8: DecompressPointer r0
    //     0xb970d8: add             x0, x0, HEAP, lsl #32
    // 0xb970dc: LoadField: r2 = r4->field_13
    //     0xb970dc: ldur            w2, [x4, #0x13]
    // 0xb970e0: DecompressPointer r2
    //     0xb970e0: add             x2, x2, HEAP, lsl #32
    // 0xb970e4: r1 = LoadClassIdInstr(r0)
    //     0xb970e4: ldur            x1, [x0, #-1]
    //     0xb970e8: ubfx            x1, x1, #0xc, #0x14
    // 0xb970ec: mov             x16, x0
    // 0xb970f0: mov             x0, x1
    // 0xb970f4: mov             x1, x16
    // 0xb970f8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb970f8: sub             lr, x0, #0xfe
    //     0xb970fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb97100: blr             lr
    // 0xb97104: r1 = 60
    //     0xb97104: movz            x1, #0x3c
    // 0xb97108: branchIfSmi(r0, 0xb97114)
    //     0xb97108: tbz             w0, #0, #0xb97114
    // 0xb9710c: r1 = LoadClassIdInstr(r0)
    //     0xb9710c: ldur            x1, [x0, #-1]
    //     0xb97110: ubfx            x1, x1, #0xc, #0x14
    // 0xb97114: r16 = true
    //     0xb97114: add             x16, NULL, #0x20  ; true
    // 0xb97118: stp             x16, x0, [SP]
    // 0xb9711c: mov             x0, x1
    // 0xb97120: mov             lr, x0
    // 0xb97124: ldr             lr, [x21, lr, lsl #3]
    // 0xb97128: blr             lr
    // 0xb9712c: tbnz            w0, #4, #0xb9713c
    // 0xb97130: r0 = "Flagged"
    //     0xb97130: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xb97134: ldr             x0, [x0, #0xa58]
    // 0xb97138: b               #0xb97144
    // 0xb9713c: r0 = "Flag as abusive"
    //     0xb9713c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xb97140: ldr             x0, [x0, #0xa60]
    // 0xb97144: ldur            x1, [fp, #-0x10]
    // 0xb97148: stur            x0, [fp, #-8]
    // 0xb9714c: r0 = of()
    //     0xb9714c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb97150: LoadField: r1 = r0->field_87
    //     0xb97150: ldur            w1, [x0, #0x87]
    // 0xb97154: DecompressPointer r1
    //     0xb97154: add             x1, x1, HEAP, lsl #32
    // 0xb97158: LoadField: r0 = r1->field_33
    //     0xb97158: ldur            w0, [x1, #0x33]
    // 0xb9715c: DecompressPointer r0
    //     0xb9715c: add             x0, x0, HEAP, lsl #32
    // 0xb97160: cmp             w0, NULL
    // 0xb97164: b.ne            #0xb97170
    // 0xb97168: r2 = Null
    //     0xb97168: mov             x2, NULL
    // 0xb9716c: b               #0xb97194
    // 0xb97170: r16 = 12.000000
    //     0xb97170: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb97174: ldr             x16, [x16, #0x9e8]
    // 0xb97178: r30 = Instance_Color
    //     0xb97178: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9717c: stp             lr, x16, [SP]
    // 0xb97180: mov             x1, x0
    // 0xb97184: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb97184: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb97188: ldr             x4, [x4, #0xaa0]
    // 0xb9718c: r0 = copyWith()
    //     0xb9718c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb97190: mov             x2, x0
    // 0xb97194: ldur            x1, [fp, #-0x38]
    // 0xb97198: ldur            x0, [fp, #-8]
    // 0xb9719c: stur            x2, [fp, #-0x48]
    // 0xb971a0: r0 = Text()
    //     0xb971a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb971a4: mov             x2, x0
    // 0xb971a8: ldur            x0, [fp, #-8]
    // 0xb971ac: stur            x2, [fp, #-0x50]
    // 0xb971b0: StoreField: r2->field_b = r0
    //     0xb971b0: stur            w0, [x2, #0xb]
    // 0xb971b4: ldur            x0, [fp, #-0x48]
    // 0xb971b8: StoreField: r2->field_13 = r0
    //     0xb971b8: stur            w0, [x2, #0x13]
    // 0xb971bc: ldur            x0, [fp, #-0x38]
    // 0xb971c0: LoadField: r1 = r0->field_b
    //     0xb971c0: ldur            w1, [x0, #0xb]
    // 0xb971c4: LoadField: r3 = r0->field_f
    //     0xb971c4: ldur            w3, [x0, #0xf]
    // 0xb971c8: DecompressPointer r3
    //     0xb971c8: add             x3, x3, HEAP, lsl #32
    // 0xb971cc: LoadField: r4 = r3->field_b
    //     0xb971cc: ldur            w4, [x3, #0xb]
    // 0xb971d0: r3 = LoadInt32Instr(r1)
    //     0xb971d0: sbfx            x3, x1, #1, #0x1f
    // 0xb971d4: stur            x3, [fp, #-0x40]
    // 0xb971d8: r1 = LoadInt32Instr(r4)
    //     0xb971d8: sbfx            x1, x4, #1, #0x1f
    // 0xb971dc: cmp             x3, x1
    // 0xb971e0: b.ne            #0xb971ec
    // 0xb971e4: mov             x1, x0
    // 0xb971e8: r0 = _growToNextCapacity()
    //     0xb971e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb971ec: ldur            x4, [fp, #-0x30]
    // 0xb971f0: ldur            x2, [fp, #-0x38]
    // 0xb971f4: ldur            x3, [fp, #-0x40]
    // 0xb971f8: add             x0, x3, #1
    // 0xb971fc: lsl             x1, x0, #1
    // 0xb97200: StoreField: r2->field_b = r1
    //     0xb97200: stur            w1, [x2, #0xb]
    // 0xb97204: LoadField: r1 = r2->field_f
    //     0xb97204: ldur            w1, [x2, #0xf]
    // 0xb97208: DecompressPointer r1
    //     0xb97208: add             x1, x1, HEAP, lsl #32
    // 0xb9720c: ldur            x0, [fp, #-0x50]
    // 0xb97210: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97210: add             x25, x1, x3, lsl #2
    //     0xb97214: add             x25, x25, #0xf
    //     0xb97218: str             w0, [x25]
    //     0xb9721c: tbz             w0, #0, #0xb97238
    //     0xb97220: ldurb           w16, [x1, #-1]
    //     0xb97224: ldurb           w17, [x0, #-1]
    //     0xb97228: and             x16, x17, x16, lsr #2
    //     0xb9722c: tst             x16, HEAP, lsr #32
    //     0xb97230: b.eq            #0xb97238
    //     0xb97234: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb97238: r0 = Row()
    //     0xb97238: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9723c: mov             x2, x0
    // 0xb97240: r0 = Instance_Axis
    //     0xb97240: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb97244: stur            x2, [fp, #-8]
    // 0xb97248: StoreField: r2->field_f = r0
    //     0xb97248: stur            w0, [x2, #0xf]
    // 0xb9724c: r0 = Instance_MainAxisAlignment
    //     0xb9724c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb97250: ldr             x0, [x0, #0xa08]
    // 0xb97254: StoreField: r2->field_13 = r0
    //     0xb97254: stur            w0, [x2, #0x13]
    // 0xb97258: r0 = Instance_MainAxisSize
    //     0xb97258: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9725c: ldr             x0, [x0, #0xa10]
    // 0xb97260: ArrayStore: r2[0] = r0  ; List_4
    //     0xb97260: stur            w0, [x2, #0x17]
    // 0xb97264: r0 = Instance_CrossAxisAlignment
    //     0xb97264: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb97268: ldr             x0, [x0, #0xa18]
    // 0xb9726c: StoreField: r2->field_1b = r0
    //     0xb9726c: stur            w0, [x2, #0x1b]
    // 0xb97270: r0 = Instance_VerticalDirection
    //     0xb97270: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb97274: ldr             x0, [x0, #0xa20]
    // 0xb97278: StoreField: r2->field_23 = r0
    //     0xb97278: stur            w0, [x2, #0x23]
    // 0xb9727c: r0 = Instance_Clip
    //     0xb9727c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb97280: ldr             x0, [x0, #0x38]
    // 0xb97284: StoreField: r2->field_2b = r0
    //     0xb97284: stur            w0, [x2, #0x2b]
    // 0xb97288: StoreField: r2->field_2f = rZR
    //     0xb97288: stur            xzr, [x2, #0x2f]
    // 0xb9728c: ldur            x0, [fp, #-0x38]
    // 0xb97290: StoreField: r2->field_b = r0
    //     0xb97290: stur            w0, [x2, #0xb]
    // 0xb97294: r1 = <String>
    //     0xb97294: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb97298: r0 = PopupMenuItem()
    //     0xb97298: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xb9729c: mov             x3, x0
    // 0xb972a0: r0 = "flag"
    //     0xb972a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb972a4: ldr             x0, [x0, #0xa68]
    // 0xb972a8: stur            x3, [fp, #-0x38]
    // 0xb972ac: StoreField: r3->field_f = r0
    //     0xb972ac: stur            w0, [x3, #0xf]
    // 0xb972b0: ldur            x0, [fp, #-0x30]
    // 0xb972b4: StoreField: r3->field_13 = r0
    //     0xb972b4: stur            w0, [x3, #0x13]
    // 0xb972b8: r0 = true
    //     0xb972b8: add             x0, NULL, #0x20  ; true
    // 0xb972bc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb972bc: stur            w0, [x3, #0x17]
    // 0xb972c0: d0 = 25.000000
    //     0xb972c0: fmov            d0, #25.00000000
    // 0xb972c4: StoreField: r3->field_1b = d0
    //     0xb972c4: stur            d0, [x3, #0x1b]
    // 0xb972c8: ldur            x0, [fp, #-8]
    // 0xb972cc: StoreField: r3->field_33 = r0
    //     0xb972cc: stur            w0, [x3, #0x33]
    // 0xb972d0: r1 = Null
    //     0xb972d0: mov             x1, NULL
    // 0xb972d4: r2 = 2
    //     0xb972d4: movz            x2, #0x2
    // 0xb972d8: r0 = AllocateArray()
    //     0xb972d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb972dc: mov             x2, x0
    // 0xb972e0: ldur            x0, [fp, #-0x38]
    // 0xb972e4: stur            x2, [fp, #-8]
    // 0xb972e8: StoreField: r2->field_f = r0
    //     0xb972e8: stur            w0, [x2, #0xf]
    // 0xb972ec: r1 = <PopupMenuEntry<String>>
    //     0xb972ec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xb972f0: ldr             x1, [x1, #0xa70]
    // 0xb972f4: r0 = AllocateGrowableArray()
    //     0xb972f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb972f8: mov             x1, x0
    // 0xb972fc: ldur            x0, [fp, #-8]
    // 0xb97300: StoreField: r1->field_f = r0
    //     0xb97300: stur            w0, [x1, #0xf]
    // 0xb97304: r0 = 2
    //     0xb97304: movz            x0, #0x2
    // 0xb97308: StoreField: r1->field_b = r0
    //     0xb97308: stur            w0, [x1, #0xb]
    // 0xb9730c: r16 = <String>
    //     0xb9730c: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb97310: ldur            lr, [fp, #-0x10]
    // 0xb97314: stp             lr, x16, [SP, #0x20]
    // 0xb97318: ldur            x16, [fp, #-0x18]
    // 0xb9731c: stp             x16, x1, [SP, #0x10]
    // 0xb97320: r16 = Instance_Color
    //     0xb97320: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb97324: ldur            lr, [fp, #-0x20]
    // 0xb97328: stp             lr, x16, [SP]
    // 0xb9732c: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xb9732c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xb97330: ldr             x4, [x4, #0xa78]
    // 0xb97334: r0 = showMenu()
    //     0xb97334: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xb97338: ldur            x2, [fp, #-0x28]
    // 0xb9733c: r1 = Function '<anonymous closure>':.
    //     0xb9733c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ff0] AnonymousClosure: (0xb973fc), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xb96cb8)
    //     0xb97340: ldr             x1, [x1, #0xff0]
    // 0xb97344: stur            x0, [fp, #-8]
    // 0xb97348: r0 = AllocateClosure()
    //     0xb97348: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9734c: r16 = <Null?>
    //     0xb9734c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xb97350: ldur            lr, [fp, #-8]
    // 0xb97354: stp             lr, x16, [SP, #8]
    // 0xb97358: str             x0, [SP]
    // 0xb9735c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9735c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb97360: r0 = then()
    //     0xb97360: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb97364: r0 = Null
    //     0xb97364: mov             x0, NULL
    // 0xb97368: LeaveFrame
    //     0xb97368: mov             SP, fp
    //     0xb9736c: ldp             fp, lr, [SP], #0x10
    // 0xb97370: ret
    //     0xb97370: ret             
    // 0xb97374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97374: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97378: b               #0xb96cec
    // 0xb9737c: SaveReg d0
    //     0xb9737c: str             q0, [SP, #-0x10]!
    // 0xb97380: stp             x0, x1, [SP, #-0x10]!
    // 0xb97384: r0 = AllocateDouble()
    //     0xb97384: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb97388: mov             x2, x0
    // 0xb9738c: ldp             x0, x1, [SP], #0x10
    // 0xb97390: RestoreReg d0
    //     0xb97390: ldr             q0, [SP], #0x10
    // 0xb97394: b               #0xb96dc8
    // 0xb97398: stp             q0, q1, [SP, #-0x20]!
    // 0xb9739c: stp             x0, x1, [SP, #-0x10]!
    // 0xb973a0: r0 = AllocateDouble()
    //     0xb973a0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb973a4: mov             x2, x0
    // 0xb973a8: ldp             x0, x1, [SP], #0x10
    // 0xb973ac: ldp             q0, q1, [SP], #0x20
    // 0xb973b0: b               #0xb96e1c
    // 0xb973b4: stp             q2, q3, [SP, #-0x20]!
    // 0xb973b8: stp             q0, q1, [SP, #-0x20]!
    // 0xb973bc: stp             x0, x1, [SP, #-0x10]!
    // 0xb973c0: r0 = AllocateDouble()
    //     0xb973c0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb973c4: mov             x2, x0
    // 0xb973c8: ldp             x0, x1, [SP], #0x10
    // 0xb973cc: ldp             q0, q1, [SP], #0x20
    // 0xb973d0: ldp             q2, q3, [SP], #0x20
    // 0xb973d4: b               #0xb96e7c
    // 0xb973d8: stp             q3, q4, [SP, #-0x20]!
    // 0xb973dc: stp             q0, q1, [SP, #-0x20]!
    // 0xb973e0: SaveReg r0
    //     0xb973e0: str             x0, [SP, #-8]!
    // 0xb973e4: r0 = AllocateDouble()
    //     0xb973e4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb973e8: mov             x1, x0
    // 0xb973ec: RestoreReg r0
    //     0xb973ec: ldr             x0, [SP], #8
    // 0xb973f0: ldp             q0, q1, [SP], #0x20
    // 0xb973f4: ldp             q3, q4, [SP], #0x20
    // 0xb973f8: b               #0xb96ed4
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb973fc, size: 0x94
    // 0xb973fc: EnterFrame
    //     0xb973fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb97400: mov             fp, SP
    // 0xb97404: AllocStack(0x20)
    //     0xb97404: sub             SP, SP, #0x20
    // 0xb97408: SetupParameters()
    //     0xb97408: ldr             x0, [fp, #0x18]
    //     0xb9740c: ldur            w2, [x0, #0x17]
    //     0xb97410: add             x2, x2, HEAP, lsl #32
    //     0xb97414: stur            x2, [fp, #-8]
    // 0xb97418: CheckStackOverflow
    //     0xb97418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9741c: cmp             SP, x16
    //     0xb97420: b.ls            #0xb97488
    // 0xb97424: ldr             x0, [fp, #0x10]
    // 0xb97428: r1 = LoadClassIdInstr(r0)
    //     0xb97428: ldur            x1, [x0, #-1]
    //     0xb9742c: ubfx            x1, x1, #0xc, #0x14
    // 0xb97430: r16 = "flag"
    //     0xb97430: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb97434: ldr             x16, [x16, #0xa68]
    // 0xb97438: stp             x16, x0, [SP]
    // 0xb9743c: mov             x0, x1
    // 0xb97440: mov             lr, x0
    // 0xb97444: ldr             lr, [x21, lr, lsl #3]
    // 0xb97448: blr             lr
    // 0xb9744c: tbnz            w0, #4, #0xb97478
    // 0xb97450: ldur            x2, [fp, #-8]
    // 0xb97454: LoadField: r0 = r2->field_f
    //     0xb97454: ldur            w0, [x2, #0xf]
    // 0xb97458: DecompressPointer r0
    //     0xb97458: add             x0, x0, HEAP, lsl #32
    // 0xb9745c: stur            x0, [fp, #-0x10]
    // 0xb97460: r1 = Function '<anonymous closure>':.
    //     0xb97460: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ff8] AnonymousClosure: (0xaa4b44), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xb97464: ldr             x1, [x1, #0xff8]
    // 0xb97468: r0 = AllocateClosure()
    //     0xb97468: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9746c: ldur            x1, [fp, #-0x10]
    // 0xb97470: mov             x2, x0
    // 0xb97474: r0 = setState()
    //     0xb97474: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb97478: r0 = Null
    //     0xb97478: mov             x0, NULL
    // 0xb9747c: LeaveFrame
    //     0xb9747c: mov             SP, fp
    //     0xb97480: ldp             fp, lr, [SP], #0x10
    // 0xb97484: ret
    //     0xb97484: ret             
    // 0xb97488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97488: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9748c: b               #0xb97424
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb97490, size: 0x60
    // 0xb97490: EnterFrame
    //     0xb97490: stp             fp, lr, [SP, #-0x10]!
    //     0xb97494: mov             fp, SP
    // 0xb97498: AllocStack(0x8)
    //     0xb97498: sub             SP, SP, #8
    // 0xb9749c: SetupParameters()
    //     0xb9749c: ldr             x0, [fp, #0x10]
    //     0xb974a0: ldur            w2, [x0, #0x17]
    //     0xb974a4: add             x2, x2, HEAP, lsl #32
    // 0xb974a8: CheckStackOverflow
    //     0xb974a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb974ac: cmp             SP, x16
    //     0xb974b0: b.ls            #0xb974e8
    // 0xb974b4: LoadField: r0 = r2->field_f
    //     0xb974b4: ldur            w0, [x2, #0xf]
    // 0xb974b8: DecompressPointer r0
    //     0xb974b8: add             x0, x0, HEAP, lsl #32
    // 0xb974bc: stur            x0, [fp, #-8]
    // 0xb974c0: r1 = Function '<anonymous closure>':.
    //     0xb974c0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55000] AnonymousClosure: (0xaa4b44), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::showMenuItem (0xaa4c8c)
    //     0xb974c4: ldr             x1, [x1]
    // 0xb974c8: r0 = AllocateClosure()
    //     0xb974c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb974cc: ldur            x1, [fp, #-8]
    // 0xb974d0: mov             x2, x0
    // 0xb974d4: r0 = setState()
    //     0xb974d4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb974d8: r0 = Null
    //     0xb974d8: mov             x0, NULL
    // 0xb974dc: LeaveFrame
    //     0xb974dc: mov             SP, fp
    //     0xb974e0: ldp             fp, lr, [SP], #0x10
    // 0xb974e4: ret
    //     0xb974e4: ret             
    // 0xb974e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb974e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb974ec: b               #0xb974b4
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb974f0, size: 0x6d8
    // 0xb974f0: EnterFrame
    //     0xb974f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb974f4: mov             fp, SP
    // 0xb974f8: AllocStack(0x78)
    //     0xb974f8: sub             SP, SP, #0x78
    // 0xb974fc: SetupParameters()
    //     0xb974fc: ldr             x0, [fp, #0x20]
    //     0xb97500: ldur            w3, [x0, #0x17]
    //     0xb97504: add             x3, x3, HEAP, lsl #32
    //     0xb97508: stur            x3, [fp, #-0x18]
    // 0xb9750c: CheckStackOverflow
    //     0xb9750c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97510: cmp             SP, x16
    //     0xb97514: b.ls            #0xb97b88
    // 0xb97518: LoadField: r0 = r3->field_f
    //     0xb97518: ldur            w0, [x3, #0xf]
    // 0xb9751c: DecompressPointer r0
    //     0xb9751c: add             x0, x0, HEAP, lsl #32
    // 0xb97520: LoadField: r2 = r0->field_37
    //     0xb97520: ldur            w2, [x0, #0x37]
    // 0xb97524: DecompressPointer r2
    //     0xb97524: add             x2, x2, HEAP, lsl #32
    // 0xb97528: LoadField: r0 = r2->field_b
    //     0xb97528: ldur            w0, [x2, #0xb]
    // 0xb9752c: ldr             x1, [fp, #0x10]
    // 0xb97530: r4 = LoadInt32Instr(r1)
    //     0xb97530: sbfx            x4, x1, #1, #0x1f
    //     0xb97534: tbz             w1, #0, #0xb9753c
    //     0xb97538: ldur            x4, [x1, #7]
    // 0xb9753c: stur            x4, [fp, #-0x10]
    // 0xb97540: r1 = LoadInt32Instr(r0)
    //     0xb97540: sbfx            x1, x0, #1, #0x1f
    // 0xb97544: mov             x0, x1
    // 0xb97548: mov             x1, x4
    // 0xb9754c: cmp             x1, x0
    // 0xb97550: b.hs            #0xb97b90
    // 0xb97554: LoadField: r0 = r2->field_f
    //     0xb97554: ldur            w0, [x2, #0xf]
    // 0xb97558: DecompressPointer r0
    //     0xb97558: add             x0, x0, HEAP, lsl #32
    // 0xb9755c: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0xb9755c: add             x16, x0, x4, lsl #2
    //     0xb97560: ldur            w5, [x16, #0xf]
    // 0xb97564: DecompressPointer r5
    //     0xb97564: add             x5, x5, HEAP, lsl #32
    // 0xb97568: stur            x5, [fp, #-8]
    // 0xb9756c: r1 = <Widget>
    //     0xb9756c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb97570: r2 = 0
    //     0xb97570: movz            x2, #0
    // 0xb97574: r0 = _GrowableList()
    //     0xb97574: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb97578: mov             x2, x0
    // 0xb9757c: ldur            x1, [fp, #-8]
    // 0xb97580: stur            x2, [fp, #-0x20]
    // 0xb97584: LoadField: r0 = r1->field_f
    //     0xb97584: ldur            w0, [x1, #0xf]
    // 0xb97588: DecompressPointer r0
    //     0xb97588: add             x0, x0, HEAP, lsl #32
    // 0xb9758c: r3 = LoadClassIdInstr(r0)
    //     0xb9758c: ldur            x3, [x0, #-1]
    //     0xb97590: ubfx            x3, x3, #0xc, #0x14
    // 0xb97594: r16 = "image"
    //     0xb97594: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb97598: stp             x16, x0, [SP]
    // 0xb9759c: mov             x0, x3
    // 0xb975a0: mov             lr, x0
    // 0xb975a4: ldr             lr, [x21, lr, lsl #3]
    // 0xb975a8: blr             lr
    // 0xb975ac: tbnz            w0, #4, #0xb97798
    // 0xb975b0: ldur            x0, [fp, #-0x18]
    // 0xb975b4: LoadField: r1 = r0->field_f
    //     0xb975b4: ldur            w1, [x0, #0xf]
    // 0xb975b8: DecompressPointer r1
    //     0xb975b8: add             x1, x1, HEAP, lsl #32
    // 0xb975bc: LoadField: r2 = r1->field_2b
    //     0xb975bc: ldur            w2, [x1, #0x2b]
    // 0xb975c0: DecompressPointer r2
    //     0xb975c0: add             x2, x2, HEAP, lsl #32
    // 0xb975c4: tbnz            w2, #4, #0xb975d4
    // 0xb975c8: r3 = Instance_BoxFit
    //     0xb975c8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb975cc: ldr             x3, [x3, #0x118]
    // 0xb975d0: b               #0xb975dc
    // 0xb975d4: r3 = Instance_BoxFit
    //     0xb975d4: add             x3, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xb975d8: ldr             x3, [x3, #0xf38]
    // 0xb975dc: ldur            x1, [fp, #-8]
    // 0xb975e0: stur            x3, [fp, #-0x30]
    // 0xb975e4: LoadField: r2 = r1->field_13
    //     0xb975e4: ldur            w2, [x1, #0x13]
    // 0xb975e8: DecompressPointer r2
    //     0xb975e8: add             x2, x2, HEAP, lsl #32
    // 0xb975ec: cmp             w2, NULL
    // 0xb975f0: b.ne            #0xb975fc
    // 0xb975f4: r5 = ""
    //     0xb975f4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb975f8: b               #0xb97600
    // 0xb975fc: mov             x5, x2
    // 0xb97600: ldur            x4, [fp, #-0x20]
    // 0xb97604: stur            x5, [fp, #-0x28]
    // 0xb97608: r1 = Function '<anonymous closure>':.
    //     0xb97608: add             x1, PP, #0x55, lsl #12  ; [pp+0x55008] AnonymousClosure: (0xaa613c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb9760c: ldr             x1, [x1, #8]
    // 0xb97610: r2 = Null
    //     0xb97610: mov             x2, NULL
    // 0xb97614: r0 = AllocateClosure()
    //     0xb97614: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb97618: r1 = Function '<anonymous closure>':.
    //     0xb97618: add             x1, PP, #0x55, lsl #12  ; [pp+0x55010] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9761c: ldr             x1, [x1, #0x10]
    // 0xb97620: r2 = Null
    //     0xb97620: mov             x2, NULL
    // 0xb97624: stur            x0, [fp, #-0x38]
    // 0xb97628: r0 = AllocateClosure()
    //     0xb97628: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9762c: stur            x0, [fp, #-0x40]
    // 0xb97630: r0 = CachedNetworkImage()
    //     0xb97630: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb97634: stur            x0, [fp, #-0x48]
    // 0xb97638: ldur            x16, [fp, #-0x30]
    // 0xb9763c: r30 = inf
    //     0xb9763c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb97640: ldr             lr, [lr, #0x9f8]
    // 0xb97644: stp             lr, x16, [SP, #0x10]
    // 0xb97648: ldur            x16, [fp, #-0x38]
    // 0xb9764c: ldur            lr, [fp, #-0x40]
    // 0xb97650: stp             lr, x16, [SP]
    // 0xb97654: mov             x1, x0
    // 0xb97658: ldur            x2, [fp, #-0x28]
    // 0xb9765c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb9765c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54f70] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb97660: ldr             x4, [x4, #0xf70]
    // 0xb97664: r0 = CachedNetworkImage()
    //     0xb97664: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb97668: r1 = Null
    //     0xb97668: mov             x1, NULL
    // 0xb9766c: r2 = 2
    //     0xb9766c: movz            x2, #0x2
    // 0xb97670: r0 = AllocateArray()
    //     0xb97670: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb97674: mov             x2, x0
    // 0xb97678: ldur            x0, [fp, #-0x48]
    // 0xb9767c: stur            x2, [fp, #-0x28]
    // 0xb97680: StoreField: r2->field_f = r0
    //     0xb97680: stur            w0, [x2, #0xf]
    // 0xb97684: r1 = <Widget>
    //     0xb97684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb97688: r0 = AllocateGrowableArray()
    //     0xb97688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9768c: mov             x1, x0
    // 0xb97690: ldur            x0, [fp, #-0x28]
    // 0xb97694: stur            x1, [fp, #-0x30]
    // 0xb97698: StoreField: r1->field_f = r0
    //     0xb97698: stur            w0, [x1, #0xf]
    // 0xb9769c: r0 = 2
    //     0xb9769c: movz            x0, #0x2
    // 0xb976a0: StoreField: r1->field_b = r0
    //     0xb976a0: stur            w0, [x1, #0xb]
    // 0xb976a4: r0 = Stack()
    //     0xb976a4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb976a8: mov             x1, x0
    // 0xb976ac: r0 = Instance_AlignmentDirectional
    //     0xb976ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb976b0: ldr             x0, [x0, #0xd08]
    // 0xb976b4: stur            x1, [fp, #-0x28]
    // 0xb976b8: StoreField: r1->field_f = r0
    //     0xb976b8: stur            w0, [x1, #0xf]
    // 0xb976bc: r2 = Instance_StackFit
    //     0xb976bc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb976c0: ldr             x2, [x2, #0xfa8]
    // 0xb976c4: ArrayStore: r1[0] = r2  ; List_4
    //     0xb976c4: stur            w2, [x1, #0x17]
    // 0xb976c8: r3 = Instance_Clip
    //     0xb976c8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb976cc: ldr             x3, [x3, #0x7e0]
    // 0xb976d0: StoreField: r1->field_1b = r3
    //     0xb976d0: stur            w3, [x1, #0x1b]
    // 0xb976d4: ldur            x4, [fp, #-0x30]
    // 0xb976d8: StoreField: r1->field_b = r4
    //     0xb976d8: stur            w4, [x1, #0xb]
    // 0xb976dc: r0 = GestureDetector()
    //     0xb976dc: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb976e0: stur            x0, [fp, #-0x30]
    // 0xb976e4: ldur            x16, [fp, #-0x28]
    // 0xb976e8: str             x16, [SP]
    // 0xb976ec: mov             x1, x0
    // 0xb976f0: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xb976f0: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfb0] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xb976f4: ldr             x4, [x4, #0xfb0]
    // 0xb976f8: r0 = GestureDetector()
    //     0xb976f8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb976fc: r0 = Center()
    //     0xb976fc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb97700: mov             x2, x0
    // 0xb97704: r0 = Instance_Alignment
    //     0xb97704: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb97708: ldr             x0, [x0, #0xb10]
    // 0xb9770c: stur            x2, [fp, #-0x28]
    // 0xb97710: StoreField: r2->field_f = r0
    //     0xb97710: stur            w0, [x2, #0xf]
    // 0xb97714: ldur            x0, [fp, #-0x30]
    // 0xb97718: StoreField: r2->field_b = r0
    //     0xb97718: stur            w0, [x2, #0xb]
    // 0xb9771c: ldur            x0, [fp, #-0x20]
    // 0xb97720: LoadField: r1 = r0->field_b
    //     0xb97720: ldur            w1, [x0, #0xb]
    // 0xb97724: LoadField: r3 = r0->field_f
    //     0xb97724: ldur            w3, [x0, #0xf]
    // 0xb97728: DecompressPointer r3
    //     0xb97728: add             x3, x3, HEAP, lsl #32
    // 0xb9772c: LoadField: r4 = r3->field_b
    //     0xb9772c: ldur            w4, [x3, #0xb]
    // 0xb97730: r3 = LoadInt32Instr(r1)
    //     0xb97730: sbfx            x3, x1, #1, #0x1f
    // 0xb97734: stur            x3, [fp, #-0x50]
    // 0xb97738: r1 = LoadInt32Instr(r4)
    //     0xb97738: sbfx            x1, x4, #1, #0x1f
    // 0xb9773c: cmp             x3, x1
    // 0xb97740: b.ne            #0xb9774c
    // 0xb97744: mov             x1, x0
    // 0xb97748: r0 = _growToNextCapacity()
    //     0xb97748: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9774c: ldur            x2, [fp, #-0x20]
    // 0xb97750: ldur            x3, [fp, #-0x50]
    // 0xb97754: add             x0, x3, #1
    // 0xb97758: lsl             x1, x0, #1
    // 0xb9775c: StoreField: r2->field_b = r1
    //     0xb9775c: stur            w1, [x2, #0xb]
    // 0xb97760: LoadField: r1 = r2->field_f
    //     0xb97760: ldur            w1, [x2, #0xf]
    // 0xb97764: DecompressPointer r1
    //     0xb97764: add             x1, x1, HEAP, lsl #32
    // 0xb97768: ldur            x0, [fp, #-0x28]
    // 0xb9776c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb9776c: add             x25, x1, x3, lsl #2
    //     0xb97770: add             x25, x25, #0xf
    //     0xb97774: str             w0, [x25]
    //     0xb97778: tbz             w0, #0, #0xb97794
    //     0xb9777c: ldurb           w16, [x1, #-1]
    //     0xb97780: ldurb           w17, [x0, #-1]
    //     0xb97784: and             x16, x17, x16, lsr #2
    //     0xb97788: tst             x16, HEAP, lsr #32
    //     0xb9778c: b.eq            #0xb97794
    //     0xb97790: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb97794: b               #0xb9787c
    // 0xb97798: ldur            x2, [fp, #-0x20]
    // 0xb9779c: ldur            x1, [fp, #-8]
    // 0xb977a0: r0 = Instance_Alignment
    //     0xb977a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb977a4: ldr             x0, [x0, #0xb10]
    // 0xb977a8: LoadField: r3 = r1->field_13
    //     0xb977a8: ldur            w3, [x1, #0x13]
    // 0xb977ac: DecompressPointer r3
    //     0xb977ac: add             x3, x3, HEAP, lsl #32
    // 0xb977b0: cmp             w3, NULL
    // 0xb977b4: b.ne            #0xb977c0
    // 0xb977b8: r1 = ""
    //     0xb977b8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb977bc: b               #0xb977c4
    // 0xb977c0: mov             x1, x3
    // 0xb977c4: stur            x1, [fp, #-8]
    // 0xb977c8: r0 = VideoPlayerWidget()
    //     0xb977c8: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb977cc: mov             x1, x0
    // 0xb977d0: ldur            x0, [fp, #-8]
    // 0xb977d4: stur            x1, [fp, #-0x28]
    // 0xb977d8: StoreField: r1->field_b = r0
    //     0xb977d8: stur            w0, [x1, #0xb]
    // 0xb977dc: r0 = true
    //     0xb977dc: add             x0, NULL, #0x20  ; true
    // 0xb977e0: StoreField: r1->field_f = r0
    //     0xb977e0: stur            w0, [x1, #0xf]
    // 0xb977e4: r0 = Center()
    //     0xb977e4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb977e8: mov             x2, x0
    // 0xb977ec: r0 = Instance_Alignment
    //     0xb977ec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb977f0: ldr             x0, [x0, #0xb10]
    // 0xb977f4: stur            x2, [fp, #-8]
    // 0xb977f8: StoreField: r2->field_f = r0
    //     0xb977f8: stur            w0, [x2, #0xf]
    // 0xb977fc: ldur            x0, [fp, #-0x28]
    // 0xb97800: StoreField: r2->field_b = r0
    //     0xb97800: stur            w0, [x2, #0xb]
    // 0xb97804: ldur            x0, [fp, #-0x20]
    // 0xb97808: LoadField: r1 = r0->field_b
    //     0xb97808: ldur            w1, [x0, #0xb]
    // 0xb9780c: LoadField: r3 = r0->field_f
    //     0xb9780c: ldur            w3, [x0, #0xf]
    // 0xb97810: DecompressPointer r3
    //     0xb97810: add             x3, x3, HEAP, lsl #32
    // 0xb97814: LoadField: r4 = r3->field_b
    //     0xb97814: ldur            w4, [x3, #0xb]
    // 0xb97818: r3 = LoadInt32Instr(r1)
    //     0xb97818: sbfx            x3, x1, #1, #0x1f
    // 0xb9781c: stur            x3, [fp, #-0x50]
    // 0xb97820: r1 = LoadInt32Instr(r4)
    //     0xb97820: sbfx            x1, x4, #1, #0x1f
    // 0xb97824: cmp             x3, x1
    // 0xb97828: b.ne            #0xb97834
    // 0xb9782c: mov             x1, x0
    // 0xb97830: r0 = _growToNextCapacity()
    //     0xb97830: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97834: ldur            x2, [fp, #-0x20]
    // 0xb97838: ldur            x3, [fp, #-0x50]
    // 0xb9783c: add             x0, x3, #1
    // 0xb97840: lsl             x1, x0, #1
    // 0xb97844: StoreField: r2->field_b = r1
    //     0xb97844: stur            w1, [x2, #0xb]
    // 0xb97848: LoadField: r1 = r2->field_f
    //     0xb97848: ldur            w1, [x2, #0xf]
    // 0xb9784c: DecompressPointer r1
    //     0xb9784c: add             x1, x1, HEAP, lsl #32
    // 0xb97850: ldur            x0, [fp, #-8]
    // 0xb97854: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97854: add             x25, x1, x3, lsl #2
    //     0xb97858: add             x25, x25, #0xf
    //     0xb9785c: str             w0, [x25]
    //     0xb97860: tbz             w0, #0, #0xb9787c
    //     0xb97864: ldurb           w16, [x1, #-1]
    //     0xb97868: ldurb           w17, [x0, #-1]
    //     0xb9786c: and             x16, x17, x16, lsr #2
    //     0xb97870: tst             x16, HEAP, lsr #32
    //     0xb97874: b.eq            #0xb9787c
    //     0xb97878: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb9787c: ldur            x0, [fp, #-0x10]
    // 0xb97880: cmp             x0, #0
    // 0xb97884: b.le            #0xb979d4
    // 0xb97888: ldr             x1, [fp, #0x18]
    // 0xb9788c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb9788c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb97890: r0 = _of()
    //     0xb97890: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb97894: LoadField: r1 = r0->field_7
    //     0xb97894: ldur            w1, [x0, #7]
    // 0xb97898: DecompressPointer r1
    //     0xb97898: add             x1, x1, HEAP, lsl #32
    // 0xb9789c: LoadField: d0 = r1->field_7
    //     0xb9789c: ldur            d0, [x1, #7]
    // 0xb978a0: d1 = 0.200000
    //     0xb978a0: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb978a4: fmul            d2, d0, d1
    // 0xb978a8: stur            d2, [fp, #-0x58]
    // 0xb978ac: r0 = Container()
    //     0xb978ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb978b0: stur            x0, [fp, #-8]
    // 0xb978b4: r16 = Instance_Color
    //     0xb978b4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb978b8: ldr             x16, [x16, #0xf88]
    // 0xb978bc: str             x16, [SP]
    // 0xb978c0: mov             x1, x0
    // 0xb978c4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb978c4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb978c8: ldr             x4, [x4, #0xf40]
    // 0xb978cc: r0 = Container()
    //     0xb978cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb978d0: r0 = GestureDetector()
    //     0xb978d0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb978d4: ldur            x2, [fp, #-0x18]
    // 0xb978d8: r1 = Function '<anonymous closure>':.
    //     0xb978d8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55018] AnonymousClosure: (0xb97c28), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb94ab4)
    //     0xb978dc: ldr             x1, [x1, #0x18]
    // 0xb978e0: stur            x0, [fp, #-0x28]
    // 0xb978e4: r0 = AllocateClosure()
    //     0xb978e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb978e8: ldur            x16, [fp, #-8]
    // 0xb978ec: stp             x16, x0, [SP]
    // 0xb978f0: ldur            x1, [fp, #-0x28]
    // 0xb978f4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb978f4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb978f8: ldr             x4, [x4, #0xaf0]
    // 0xb978fc: r0 = GestureDetector()
    //     0xb978fc: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb97900: r1 = <StackParentData>
    //     0xb97900: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb97904: ldr             x1, [x1, #0x8e0]
    // 0xb97908: r0 = Positioned()
    //     0xb97908: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb9790c: mov             x2, x0
    // 0xb97910: r0 = 0.000000
    //     0xb97910: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb97914: stur            x2, [fp, #-8]
    // 0xb97918: StoreField: r2->field_13 = r0
    //     0xb97918: stur            w0, [x2, #0x13]
    // 0xb9791c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9791c: stur            w0, [x2, #0x17]
    // 0xb97920: StoreField: r2->field_1f = r0
    //     0xb97920: stur            w0, [x2, #0x1f]
    // 0xb97924: ldur            d0, [fp, #-0x58]
    // 0xb97928: r1 = inline_Allocate_Double()
    //     0xb97928: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xb9792c: add             x1, x1, #0x10
    //     0xb97930: cmp             x3, x1
    //     0xb97934: b.ls            #0xb97b94
    //     0xb97938: str             x1, [THR, #0x50]  ; THR::top
    //     0xb9793c: sub             x1, x1, #0xf
    //     0xb97940: movz            x3, #0xe15c
    //     0xb97944: movk            x3, #0x3, lsl #16
    //     0xb97948: stur            x3, [x1, #-1]
    // 0xb9794c: StoreField: r1->field_7 = d0
    //     0xb9794c: stur            d0, [x1, #7]
    // 0xb97950: StoreField: r2->field_23 = r1
    //     0xb97950: stur            w1, [x2, #0x23]
    // 0xb97954: ldur            x1, [fp, #-0x28]
    // 0xb97958: StoreField: r2->field_b = r1
    //     0xb97958: stur            w1, [x2, #0xb]
    // 0xb9795c: ldur            x3, [fp, #-0x20]
    // 0xb97960: LoadField: r1 = r3->field_b
    //     0xb97960: ldur            w1, [x3, #0xb]
    // 0xb97964: LoadField: r4 = r3->field_f
    //     0xb97964: ldur            w4, [x3, #0xf]
    // 0xb97968: DecompressPointer r4
    //     0xb97968: add             x4, x4, HEAP, lsl #32
    // 0xb9796c: LoadField: r5 = r4->field_b
    //     0xb9796c: ldur            w5, [x4, #0xb]
    // 0xb97970: r4 = LoadInt32Instr(r1)
    //     0xb97970: sbfx            x4, x1, #1, #0x1f
    // 0xb97974: stur            x4, [fp, #-0x50]
    // 0xb97978: r1 = LoadInt32Instr(r5)
    //     0xb97978: sbfx            x1, x5, #1, #0x1f
    // 0xb9797c: cmp             x4, x1
    // 0xb97980: b.ne            #0xb9798c
    // 0xb97984: mov             x1, x3
    // 0xb97988: r0 = _growToNextCapacity()
    //     0xb97988: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9798c: ldur            x2, [fp, #-0x20]
    // 0xb97990: ldur            x3, [fp, #-0x50]
    // 0xb97994: add             x0, x3, #1
    // 0xb97998: lsl             x1, x0, #1
    // 0xb9799c: StoreField: r2->field_b = r1
    //     0xb9799c: stur            w1, [x2, #0xb]
    // 0xb979a0: LoadField: r1 = r2->field_f
    //     0xb979a0: ldur            w1, [x2, #0xf]
    // 0xb979a4: DecompressPointer r1
    //     0xb979a4: add             x1, x1, HEAP, lsl #32
    // 0xb979a8: ldur            x0, [fp, #-8]
    // 0xb979ac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb979ac: add             x25, x1, x3, lsl #2
    //     0xb979b0: add             x25, x25, #0xf
    //     0xb979b4: str             w0, [x25]
    //     0xb979b8: tbz             w0, #0, #0xb979d4
    //     0xb979bc: ldurb           w16, [x1, #-1]
    //     0xb979c0: ldurb           w17, [x0, #-1]
    //     0xb979c4: and             x16, x17, x16, lsr #2
    //     0xb979c8: tst             x16, HEAP, lsr #32
    //     0xb979cc: b.eq            #0xb979d4
    //     0xb979d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb979d4: ldur            x3, [fp, #-0x18]
    // 0xb979d8: ldur            x0, [fp, #-0x10]
    // 0xb979dc: LoadField: r1 = r3->field_f
    //     0xb979dc: ldur            w1, [x3, #0xf]
    // 0xb979e0: DecompressPointer r1
    //     0xb979e0: add             x1, x1, HEAP, lsl #32
    // 0xb979e4: LoadField: r4 = r1->field_37
    //     0xb979e4: ldur            w4, [x1, #0x37]
    // 0xb979e8: DecompressPointer r4
    //     0xb979e8: add             x4, x4, HEAP, lsl #32
    // 0xb979ec: LoadField: r1 = r4->field_b
    //     0xb979ec: ldur            w1, [x4, #0xb]
    // 0xb979f0: r4 = LoadInt32Instr(r1)
    //     0xb979f0: sbfx            x4, x1, #1, #0x1f
    // 0xb979f4: sub             x1, x4, #1
    // 0xb979f8: cmp             x0, x1
    // 0xb979fc: b.ge            #0xb97b4c
    // 0xb97a00: ldr             x1, [fp, #0x18]
    // 0xb97a04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb97a04: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb97a08: r0 = _of()
    //     0xb97a08: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb97a0c: LoadField: r1 = r0->field_7
    //     0xb97a0c: ldur            w1, [x0, #7]
    // 0xb97a10: DecompressPointer r1
    //     0xb97a10: add             x1, x1, HEAP, lsl #32
    // 0xb97a14: LoadField: d0 = r1->field_7
    //     0xb97a14: ldur            d0, [x1, #7]
    // 0xb97a18: d1 = 0.200000
    //     0xb97a18: ldr             d1, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb97a1c: fmul            d2, d0, d1
    // 0xb97a20: stur            d2, [fp, #-0x58]
    // 0xb97a24: r0 = Container()
    //     0xb97a24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb97a28: stur            x0, [fp, #-8]
    // 0xb97a2c: r16 = Instance_Color
    //     0xb97a2c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb97a30: ldr             x16, [x16, #0xf88]
    // 0xb97a34: str             x16, [SP]
    // 0xb97a38: mov             x1, x0
    // 0xb97a3c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb97a3c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb97a40: ldr             x4, [x4, #0xf40]
    // 0xb97a44: r0 = Container()
    //     0xb97a44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb97a48: r0 = GestureDetector()
    //     0xb97a48: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb97a4c: ldur            x2, [fp, #-0x18]
    // 0xb97a50: r1 = Function '<anonymous closure>':.
    //     0xb97a50: add             x1, PP, #0x55, lsl #12  ; [pp+0x55020] AnonymousClosure: (0xb97bc8), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xb94ab4)
    //     0xb97a54: ldr             x1, [x1, #0x20]
    // 0xb97a58: stur            x0, [fp, #-0x18]
    // 0xb97a5c: r0 = AllocateClosure()
    //     0xb97a5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb97a60: ldur            x16, [fp, #-8]
    // 0xb97a64: stp             x16, x0, [SP]
    // 0xb97a68: ldur            x1, [fp, #-0x18]
    // 0xb97a6c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb97a6c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb97a70: ldr             x4, [x4, #0xaf0]
    // 0xb97a74: r0 = GestureDetector()
    //     0xb97a74: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb97a78: r1 = <StackParentData>
    //     0xb97a78: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb97a7c: ldr             x1, [x1, #0x8e0]
    // 0xb97a80: r0 = Positioned()
    //     0xb97a80: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb97a84: mov             x2, x0
    // 0xb97a88: r0 = 0.000000
    //     0xb97a88: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb97a8c: stur            x2, [fp, #-8]
    // 0xb97a90: ArrayStore: r2[0] = r0  ; List_4
    //     0xb97a90: stur            w0, [x2, #0x17]
    // 0xb97a94: StoreField: r2->field_1b = r0
    //     0xb97a94: stur            w0, [x2, #0x1b]
    // 0xb97a98: StoreField: r2->field_1f = r0
    //     0xb97a98: stur            w0, [x2, #0x1f]
    // 0xb97a9c: ldur            d0, [fp, #-0x58]
    // 0xb97aa0: r0 = inline_Allocate_Double()
    //     0xb97aa0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb97aa4: add             x0, x0, #0x10
    //     0xb97aa8: cmp             x1, x0
    //     0xb97aac: b.ls            #0xb97bb0
    //     0xb97ab0: str             x0, [THR, #0x50]  ; THR::top
    //     0xb97ab4: sub             x0, x0, #0xf
    //     0xb97ab8: movz            x1, #0xe15c
    //     0xb97abc: movk            x1, #0x3, lsl #16
    //     0xb97ac0: stur            x1, [x0, #-1]
    // 0xb97ac4: StoreField: r0->field_7 = d0
    //     0xb97ac4: stur            d0, [x0, #7]
    // 0xb97ac8: StoreField: r2->field_23 = r0
    //     0xb97ac8: stur            w0, [x2, #0x23]
    // 0xb97acc: ldur            x0, [fp, #-0x18]
    // 0xb97ad0: StoreField: r2->field_b = r0
    //     0xb97ad0: stur            w0, [x2, #0xb]
    // 0xb97ad4: ldur            x0, [fp, #-0x20]
    // 0xb97ad8: LoadField: r1 = r0->field_b
    //     0xb97ad8: ldur            w1, [x0, #0xb]
    // 0xb97adc: LoadField: r3 = r0->field_f
    //     0xb97adc: ldur            w3, [x0, #0xf]
    // 0xb97ae0: DecompressPointer r3
    //     0xb97ae0: add             x3, x3, HEAP, lsl #32
    // 0xb97ae4: LoadField: r4 = r3->field_b
    //     0xb97ae4: ldur            w4, [x3, #0xb]
    // 0xb97ae8: r3 = LoadInt32Instr(r1)
    //     0xb97ae8: sbfx            x3, x1, #1, #0x1f
    // 0xb97aec: stur            x3, [fp, #-0x10]
    // 0xb97af0: r1 = LoadInt32Instr(r4)
    //     0xb97af0: sbfx            x1, x4, #1, #0x1f
    // 0xb97af4: cmp             x3, x1
    // 0xb97af8: b.ne            #0xb97b04
    // 0xb97afc: mov             x1, x0
    // 0xb97b00: r0 = _growToNextCapacity()
    //     0xb97b00: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97b04: ldur            x2, [fp, #-0x20]
    // 0xb97b08: ldur            x3, [fp, #-0x10]
    // 0xb97b0c: add             x0, x3, #1
    // 0xb97b10: lsl             x1, x0, #1
    // 0xb97b14: StoreField: r2->field_b = r1
    //     0xb97b14: stur            w1, [x2, #0xb]
    // 0xb97b18: LoadField: r1 = r2->field_f
    //     0xb97b18: ldur            w1, [x2, #0xf]
    // 0xb97b1c: DecompressPointer r1
    //     0xb97b1c: add             x1, x1, HEAP, lsl #32
    // 0xb97b20: ldur            x0, [fp, #-8]
    // 0xb97b24: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97b24: add             x25, x1, x3, lsl #2
    //     0xb97b28: add             x25, x25, #0xf
    //     0xb97b2c: str             w0, [x25]
    //     0xb97b30: tbz             w0, #0, #0xb97b4c
    //     0xb97b34: ldurb           w16, [x1, #-1]
    //     0xb97b38: ldurb           w17, [x0, #-1]
    //     0xb97b3c: and             x16, x17, x16, lsr #2
    //     0xb97b40: tst             x16, HEAP, lsr #32
    //     0xb97b44: b.eq            #0xb97b4c
    //     0xb97b48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb97b4c: r0 = Stack()
    //     0xb97b4c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb97b50: r1 = Instance_AlignmentDirectional
    //     0xb97b50: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb97b54: ldr             x1, [x1, #0xd08]
    // 0xb97b58: StoreField: r0->field_f = r1
    //     0xb97b58: stur            w1, [x0, #0xf]
    // 0xb97b5c: r1 = Instance_StackFit
    //     0xb97b5c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb97b60: ldr             x1, [x1, #0xfa8]
    // 0xb97b64: ArrayStore: r0[0] = r1  ; List_4
    //     0xb97b64: stur            w1, [x0, #0x17]
    // 0xb97b68: r1 = Instance_Clip
    //     0xb97b68: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb97b6c: ldr             x1, [x1, #0x7e0]
    // 0xb97b70: StoreField: r0->field_1b = r1
    //     0xb97b70: stur            w1, [x0, #0x1b]
    // 0xb97b74: ldur            x1, [fp, #-0x20]
    // 0xb97b78: StoreField: r0->field_b = r1
    //     0xb97b78: stur            w1, [x0, #0xb]
    // 0xb97b7c: LeaveFrame
    //     0xb97b7c: mov             SP, fp
    //     0xb97b80: ldp             fp, lr, [SP], #0x10
    // 0xb97b84: ret
    //     0xb97b84: ret             
    // 0xb97b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97b88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97b8c: b               #0xb97518
    // 0xb97b90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb97b90: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb97b94: SaveReg d0
    //     0xb97b94: str             q0, [SP, #-0x10]!
    // 0xb97b98: stp             x0, x2, [SP, #-0x10]!
    // 0xb97b9c: r0 = AllocateDouble()
    //     0xb97b9c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb97ba0: mov             x1, x0
    // 0xb97ba4: ldp             x0, x2, [SP], #0x10
    // 0xb97ba8: RestoreReg d0
    //     0xb97ba8: ldr             q0, [SP], #0x10
    // 0xb97bac: b               #0xb9794c
    // 0xb97bb0: SaveReg d0
    //     0xb97bb0: str             q0, [SP, #-0x10]!
    // 0xb97bb4: SaveReg r2
    //     0xb97bb4: str             x2, [SP, #-8]!
    // 0xb97bb8: r0 = AllocateDouble()
    //     0xb97bb8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb97bbc: RestoreReg r2
    //     0xb97bbc: ldr             x2, [SP], #8
    // 0xb97bc0: RestoreReg d0
    //     0xb97bc0: ldr             q0, [SP], #0x10
    // 0xb97bc4: b               #0xb97ac4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb97bc8, size: 0x60
    // 0xb97bc8: EnterFrame
    //     0xb97bc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb97bcc: mov             fp, SP
    // 0xb97bd0: ldr             x0, [fp, #0x10]
    // 0xb97bd4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb97bd4: ldur            w1, [x0, #0x17]
    // 0xb97bd8: DecompressPointer r1
    //     0xb97bd8: add             x1, x1, HEAP, lsl #32
    // 0xb97bdc: CheckStackOverflow
    //     0xb97bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97be0: cmp             SP, x16
    //     0xb97be4: b.ls            #0xb97c14
    // 0xb97be8: LoadField: r0 = r1->field_f
    //     0xb97be8: ldur            w0, [x1, #0xf]
    // 0xb97bec: DecompressPointer r0
    //     0xb97bec: add             x0, x0, HEAP, lsl #32
    // 0xb97bf0: LoadField: r1 = r0->field_23
    //     0xb97bf0: ldur            w1, [x0, #0x23]
    // 0xb97bf4: DecompressPointer r1
    //     0xb97bf4: add             x1, x1, HEAP, lsl #32
    // 0xb97bf8: r16 = Sentinel
    //     0xb97bf8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb97bfc: cmp             w1, w16
    // 0xb97c00: b.eq            #0xb97c1c
    // 0xb97c04: r0 = nextPage()
    //     0xb97c04: bl              #0xaa5ed0  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xb97c08: LeaveFrame
    //     0xb97c08: mov             SP, fp
    //     0xb97c0c: ldp             fp, lr, [SP], #0x10
    // 0xb97c10: ret
    //     0xb97c10: ret             
    // 0xb97c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97c14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97c18: b               #0xb97be8
    // 0xb97c1c: r9 = _pageController
    //     0xb97c1c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54fe0] Field <_RatingReviewAllMediaOnTapImageState@1633116981._pageController@1633116981>: late (offset: 0x24)
    //     0xb97c20: ldr             x9, [x9, #0xfe0]
    // 0xb97c24: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb97c24: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb97c28, size: 0x60
    // 0xb97c28: EnterFrame
    //     0xb97c28: stp             fp, lr, [SP, #-0x10]!
    //     0xb97c2c: mov             fp, SP
    // 0xb97c30: ldr             x0, [fp, #0x10]
    // 0xb97c34: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb97c34: ldur            w1, [x0, #0x17]
    // 0xb97c38: DecompressPointer r1
    //     0xb97c38: add             x1, x1, HEAP, lsl #32
    // 0xb97c3c: CheckStackOverflow
    //     0xb97c3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97c40: cmp             SP, x16
    //     0xb97c44: b.ls            #0xb97c74
    // 0xb97c48: LoadField: r0 = r1->field_f
    //     0xb97c48: ldur            w0, [x1, #0xf]
    // 0xb97c4c: DecompressPointer r0
    //     0xb97c4c: add             x0, x0, HEAP, lsl #32
    // 0xb97c50: LoadField: r1 = r0->field_23
    //     0xb97c50: ldur            w1, [x0, #0x23]
    // 0xb97c54: DecompressPointer r1
    //     0xb97c54: add             x1, x1, HEAP, lsl #32
    // 0xb97c58: r16 = Sentinel
    //     0xb97c58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb97c5c: cmp             w1, w16
    // 0xb97c60: b.eq            #0xb97c7c
    // 0xb97c64: r0 = previousPage()
    //     0xb97c64: bl              #0xaa6010  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xb97c68: LeaveFrame
    //     0xb97c68: mov             SP, fp
    //     0xb97c6c: ldp             fp, lr, [SP], #0x10
    // 0xb97c70: ret
    //     0xb97c70: ret             
    // 0xb97c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97c74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97c78: b               #0xb97c48
    // 0xb97c7c: r9 = _pageController
    //     0xb97c7c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54fe0] Field <_RatingReviewAllMediaOnTapImageState@1633116981._pageController@1633116981>: late (offset: 0x24)
    //     0xb97c80: ldr             x9, [x9, #0xfe0]
    // 0xb97c84: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb97c84: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb97c88, size: 0x84
    // 0xb97c88: EnterFrame
    //     0xb97c88: stp             fp, lr, [SP, #-0x10]!
    //     0xb97c8c: mov             fp, SP
    // 0xb97c90: AllocStack(0x10)
    //     0xb97c90: sub             SP, SP, #0x10
    // 0xb97c94: SetupParameters()
    //     0xb97c94: ldr             x0, [fp, #0x18]
    //     0xb97c98: ldur            w1, [x0, #0x17]
    //     0xb97c9c: add             x1, x1, HEAP, lsl #32
    //     0xb97ca0: stur            x1, [fp, #-8]
    // 0xb97ca4: CheckStackOverflow
    //     0xb97ca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97ca8: cmp             SP, x16
    //     0xb97cac: b.ls            #0xb97d04
    // 0xb97cb0: r1 = 1
    //     0xb97cb0: movz            x1, #0x1
    // 0xb97cb4: r0 = AllocateContext()
    //     0xb97cb4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb97cb8: mov             x1, x0
    // 0xb97cbc: ldur            x0, [fp, #-8]
    // 0xb97cc0: StoreField: r1->field_b = r0
    //     0xb97cc0: stur            w0, [x1, #0xb]
    // 0xb97cc4: ldr             x2, [fp, #0x10]
    // 0xb97cc8: StoreField: r1->field_f = r2
    //     0xb97cc8: stur            w2, [x1, #0xf]
    // 0xb97ccc: LoadField: r3 = r0->field_f
    //     0xb97ccc: ldur            w3, [x0, #0xf]
    // 0xb97cd0: DecompressPointer r3
    //     0xb97cd0: add             x3, x3, HEAP, lsl #32
    // 0xb97cd4: mov             x2, x1
    // 0xb97cd8: stur            x3, [fp, #-0x10]
    // 0xb97cdc: r1 = Function '<anonymous closure>':.
    //     0xb97cdc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55028] AnonymousClosure: (0xaa6a74), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::build (0xc12204)
    //     0xb97ce0: ldr             x1, [x1, #0x28]
    // 0xb97ce4: r0 = AllocateClosure()
    //     0xb97ce4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb97ce8: ldur            x1, [fp, #-0x10]
    // 0xb97cec: mov             x2, x0
    // 0xb97cf0: r0 = setState()
    //     0xb97cf0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb97cf4: r0 = Null
    //     0xb97cf4: mov             x0, NULL
    // 0xb97cf8: LeaveFrame
    //     0xb97cf8: mov             SP, fp
    //     0xb97cfc: ldp             fp, lr, [SP], #0x10
    // 0xb97d00: ret
    //     0xb97d00: ret             
    // 0xb97d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb97d04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb97d08: b               #0xb97cb0
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87e8c, size: 0x8c
    // 0xc87e8c: EnterFrame
    //     0xc87e8c: stp             fp, lr, [SP, #-0x10]!
    //     0xc87e90: mov             fp, SP
    // 0xc87e94: AllocStack(0x10)
    //     0xc87e94: sub             SP, SP, #0x10
    // 0xc87e98: SetupParameters(_RatingReviewAllMediaOnTapImageState this /* r1 => r2, fp-0x8 */)
    //     0xc87e98: mov             x2, x1
    //     0xc87e9c: stur            x1, [fp, #-8]
    // 0xc87ea0: CheckStackOverflow
    //     0xc87ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87ea4: cmp             SP, x16
    //     0xc87ea8: b.ls            #0xc87f04
    // 0xc87eac: LoadField: r1 = r2->field_23
    //     0xc87eac: ldur            w1, [x2, #0x23]
    // 0xc87eb0: DecompressPointer r1
    //     0xc87eb0: add             x1, x1, HEAP, lsl #32
    // 0xc87eb4: r16 = Sentinel
    //     0xc87eb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87eb8: cmp             w1, w16
    // 0xc87ebc: b.eq            #0xc87f0c
    // 0xc87ec0: r0 = dispose()
    //     0xc87ec0: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87ec4: ldur            x2, [fp, #-8]
    // 0xc87ec8: LoadField: r0 = r2->field_27
    //     0xc87ec8: ldur            w0, [x2, #0x27]
    // 0xc87ecc: DecompressPointer r0
    //     0xc87ecc: add             x0, x0, HEAP, lsl #32
    // 0xc87ed0: stur            x0, [fp, #-0x10]
    // 0xc87ed4: r1 = Function '_onCollapseChanged@1633116981':.
    //     0xc87ed4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55030] AnonymousClosure: (0x944da0), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_onCollapseChanged (0x944dd8)
    //     0xc87ed8: ldr             x1, [x1, #0x30]
    // 0xc87edc: r0 = AllocateClosure()
    //     0xc87edc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc87ee0: ldur            x1, [fp, #-0x10]
    // 0xc87ee4: mov             x2, x0
    // 0xc87ee8: r0 = removeListener()
    //     0xc87ee8: bl              #0x7b91b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc87eec: ldur            x1, [fp, #-0x10]
    // 0xc87ef0: r0 = dispose()
    //     0xc87ef0: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87ef4: r0 = Null
    //     0xc87ef4: mov             x0, NULL
    // 0xc87ef8: LeaveFrame
    //     0xc87ef8: mov             SP, fp
    //     0xc87efc: ldp             fp, lr, [SP], #0x10
    // 0xc87f00: ret
    //     0xc87f00: ret             
    // 0xc87f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87f04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87f08: b               #0xc87eac
    // 0xc87f0c: r9 = _pageController
    //     0xc87f0c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54fe0] Field <_RatingReviewAllMediaOnTapImageState@1633116981._pageController@1633116981>: late (offset: 0x24)
    //     0xc87f10: ldr             x9, [x9, #0xfe0]
    // 0xc87f14: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87f14: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4042, size: 0x28, field offset: 0xc
class RatingReviewAllMediaOnTapImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fc7c, size: 0x48
    // 0xc7fc7c: EnterFrame
    //     0xc7fc7c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fc80: mov             fp, SP
    // 0xc7fc84: AllocStack(0x8)
    //     0xc7fc84: sub             SP, SP, #8
    // 0xc7fc88: CheckStackOverflow
    //     0xc7fc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fc8c: cmp             SP, x16
    //     0xc7fc90: b.ls            #0xc7fcbc
    // 0xc7fc94: r1 = <RatingReviewAllMediaOnTapImage>
    //     0xc7fc94: add             x1, PP, #0x48, lsl #12  ; [pp+0x48710] TypeArguments: <RatingReviewAllMediaOnTapImage>
    //     0xc7fc98: ldr             x1, [x1, #0x710]
    // 0xc7fc9c: r0 = _RatingReviewAllMediaOnTapImageState()
    //     0xc7fc9c: bl              #0xc7fcc4  ; Allocate_RatingReviewAllMediaOnTapImageStateStub -> _RatingReviewAllMediaOnTapImageState (size=0x40)
    // 0xc7fca0: mov             x1, x0
    // 0xc7fca4: stur            x0, [fp, #-8]
    // 0xc7fca8: r0 = _RatingReviewAllMediaOnTapImageState()
    //     0xc7fca8: bl              #0xc7c8bc  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_all_media_on_tap_image.dart] _RatingReviewAllMediaOnTapImageState::_RatingReviewAllMediaOnTapImageState
    // 0xc7fcac: ldur            x0, [fp, #-8]
    // 0xc7fcb0: LeaveFrame
    //     0xc7fcb0: mov             SP, fp
    //     0xc7fcb4: ldp             fp, lr, [SP], #0x10
    // 0xc7fcb8: ret
    //     0xc7fcb8: ret             
    // 0xc7fcbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fcbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fcc0: b               #0xc7fc94
  }
}
