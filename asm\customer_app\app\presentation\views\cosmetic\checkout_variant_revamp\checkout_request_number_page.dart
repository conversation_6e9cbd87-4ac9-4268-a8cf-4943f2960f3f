// lib: , url: package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart

// class id: 1049233, size: 0x8
class :: {
}

// class id: 4610, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestNumberPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1349924, size: 0x98
    // 0x1349924: EnterFrame
    //     0x1349924: stp             fp, lr, [SP, #-0x10]!
    //     0x1349928: mov             fp, SP
    // 0x134992c: AllocStack(0x18)
    //     0x134992c: sub             SP, SP, #0x18
    // 0x1349930: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1349930: stur            x1, [fp, #-8]
    //     0x1349934: stur            x2, [fp, #-0x10]
    // 0x1349938: CheckStackOverflow
    //     0x1349938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x134993c: cmp             SP, x16
    //     0x1349940: b.ls            #0x13499b4
    // 0x1349944: r1 = 2
    //     0x1349944: movz            x1, #0x2
    // 0x1349948: r0 = AllocateContext()
    //     0x1349948: bl              #0x16f6108  ; AllocateContextStub
    // 0x134994c: ldur            x1, [fp, #-8]
    // 0x1349950: stur            x0, [fp, #-0x18]
    // 0x1349954: StoreField: r0->field_f = r1
    //     0x1349954: stur            w1, [x0, #0xf]
    // 0x1349958: ldur            x2, [fp, #-0x10]
    // 0x134995c: StoreField: r0->field_13 = r2
    //     0x134995c: stur            w2, [x0, #0x13]
    // 0x1349960: r0 = controller()
    //     0x1349960: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1349964: LoadField: r1 = r0->field_d7
    //     0x1349964: ldur            w1, [x0, #0xd7]
    // 0x1349968: DecompressPointer r1
    //     0x1349968: add             x1, x1, HEAP, lsl #32
    // 0x134996c: r0 = value()
    //     0x134996c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1349970: tbnz            w0, #4, #0x1349984
    // 0x1349974: r0 = Instance_SizedBox
    //     0x1349974: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1349978: LeaveFrame
    //     0x1349978: mov             SP, fp
    //     0x134997c: ldp             fp, lr, [SP], #0x10
    // 0x1349980: ret
    //     0x1349980: ret             
    // 0x1349984: r0 = Obx()
    //     0x1349984: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1349988: ldur            x2, [fp, #-0x18]
    // 0x134998c: r1 = Function '<anonymous closure>':.
    //     0x134998c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44188] AnonymousClosure: (0x13499bc), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::bottomNavigationBar (0x1349924)
    //     0x1349990: ldr             x1, [x1, #0x188]
    // 0x1349994: stur            x0, [fp, #-8]
    // 0x1349998: r0 = AllocateClosure()
    //     0x1349998: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x134999c: mov             x1, x0
    // 0x13499a0: ldur            x0, [fp, #-8]
    // 0x13499a4: StoreField: r0->field_b = r1
    //     0x13499a4: stur            w1, [x0, #0xb]
    // 0x13499a8: LeaveFrame
    //     0x13499a8: mov             SP, fp
    //     0x13499ac: ldp             fp, lr, [SP], #0x10
    // 0x13499b0: ret
    //     0x13499b0: ret             
    // 0x13499b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13499b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13499b8: b               #0x1349944
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x13499bc, size: 0x660
    // 0x13499bc: EnterFrame
    //     0x13499bc: stp             fp, lr, [SP, #-0x10]!
    //     0x13499c0: mov             fp, SP
    // 0x13499c4: AllocStack(0x60)
    //     0x13499c4: sub             SP, SP, #0x60
    // 0x13499c8: SetupParameters()
    //     0x13499c8: ldr             x0, [fp, #0x10]
    //     0x13499cc: ldur            w2, [x0, #0x17]
    //     0x13499d0: add             x2, x2, HEAP, lsl #32
    //     0x13499d4: stur            x2, [fp, #-8]
    // 0x13499d8: CheckStackOverflow
    //     0x13499d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13499dc: cmp             SP, x16
    //     0x13499e0: b.ls            #0x1349fd0
    // 0x13499e4: LoadField: r1 = r2->field_13
    //     0x13499e4: ldur            w1, [x2, #0x13]
    // 0x13499e8: DecompressPointer r1
    //     0x13499e8: add             x1, x1, HEAP, lsl #32
    // 0x13499ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13499ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13499f0: r0 = _of()
    //     0x13499f0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13499f4: LoadField: r1 = r0->field_23
    //     0x13499f4: ldur            w1, [x0, #0x23]
    // 0x13499f8: DecompressPointer r1
    //     0x13499f8: add             x1, x1, HEAP, lsl #32
    // 0x13499fc: LoadField: d0 = r1->field_1f
    //     0x13499fc: ldur            d0, [x1, #0x1f]
    // 0x1349a00: stur            d0, [fp, #-0x40]
    // 0x1349a04: r0 = EdgeInsets()
    //     0x1349a04: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1349a08: stur            x0, [fp, #-0x10]
    // 0x1349a0c: StoreField: r0->field_7 = rZR
    //     0x1349a0c: stur            xzr, [x0, #7]
    // 0x1349a10: StoreField: r0->field_f = rZR
    //     0x1349a10: stur            xzr, [x0, #0xf]
    // 0x1349a14: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1349a14: stur            xzr, [x0, #0x17]
    // 0x1349a18: ldur            d0, [fp, #-0x40]
    // 0x1349a1c: StoreField: r0->field_1f = d0
    //     0x1349a1c: stur            d0, [x0, #0x1f]
    // 0x1349a20: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1349a20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1349a24: ldr             x0, [x0, #0x1c80]
    //     0x1349a28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1349a2c: cmp             w0, w16
    //     0x1349a30: b.ne            #0x1349a3c
    //     0x1349a34: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1349a38: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1349a3c: r0 = GetNavigation.width()
    //     0x1349a3c: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x1349a40: ldur            x2, [fp, #-8]
    // 0x1349a44: stur            d0, [fp, #-0x40]
    // 0x1349a48: LoadField: r1 = r2->field_13
    //     0x1349a48: ldur            w1, [x2, #0x13]
    // 0x1349a4c: DecompressPointer r1
    //     0x1349a4c: add             x1, x1, HEAP, lsl #32
    // 0x1349a50: r0 = of()
    //     0x1349a50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349a54: LoadField: r1 = r0->field_5b
    //     0x1349a54: ldur            w1, [x0, #0x5b]
    // 0x1349a58: DecompressPointer r1
    //     0x1349a58: add             x1, x1, HEAP, lsl #32
    // 0x1349a5c: r0 = LoadClassIdInstr(r1)
    //     0x1349a5c: ldur            x0, [x1, #-1]
    //     0x1349a60: ubfx            x0, x0, #0xc, #0x14
    // 0x1349a64: r2 = 40
    //     0x1349a64: movz            x2, #0x28
    // 0x1349a68: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x1349a68: sub             lr, x0, #0xfe7
    //     0x1349a6c: ldr             lr, [x21, lr, lsl #3]
    //     0x1349a70: blr             lr
    // 0x1349a74: stur            x0, [fp, #-0x18]
    // 0x1349a78: r0 = BorderSide()
    //     0x1349a78: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1349a7c: mov             x1, x0
    // 0x1349a80: ldur            x0, [fp, #-0x18]
    // 0x1349a84: stur            x1, [fp, #-0x20]
    // 0x1349a88: StoreField: r1->field_7 = r0
    //     0x1349a88: stur            w0, [x1, #7]
    // 0x1349a8c: d0 = 2.000000
    //     0x1349a8c: fmov            d0, #2.00000000
    // 0x1349a90: StoreField: r1->field_b = d0
    //     0x1349a90: stur            d0, [x1, #0xb]
    // 0x1349a94: r0 = Instance_BorderStyle
    //     0x1349a94: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1349a98: ldr             x0, [x0, #0xf68]
    // 0x1349a9c: StoreField: r1->field_13 = r0
    //     0x1349a9c: stur            w0, [x1, #0x13]
    // 0x1349aa0: d0 = -1.000000
    //     0x1349aa0: fmov            d0, #-1.00000000
    // 0x1349aa4: ArrayStore: r1[0] = d0  ; List_8
    //     0x1349aa4: stur            d0, [x1, #0x17]
    // 0x1349aa8: r0 = Border()
    //     0x1349aa8: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x1349aac: mov             x1, x0
    // 0x1349ab0: ldur            x0, [fp, #-0x20]
    // 0x1349ab4: stur            x1, [fp, #-0x18]
    // 0x1349ab8: StoreField: r1->field_7 = r0
    //     0x1349ab8: stur            w0, [x1, #7]
    // 0x1349abc: r0 = Instance_BorderSide
    //     0x1349abc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1349ac0: ldr             x0, [x0, #0xe20]
    // 0x1349ac4: StoreField: r1->field_b = r0
    //     0x1349ac4: stur            w0, [x1, #0xb]
    // 0x1349ac8: StoreField: r1->field_f = r0
    //     0x1349ac8: stur            w0, [x1, #0xf]
    // 0x1349acc: StoreField: r1->field_13 = r0
    //     0x1349acc: stur            w0, [x1, #0x13]
    // 0x1349ad0: r0 = BoxDecoration()
    //     0x1349ad0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1349ad4: mov             x1, x0
    // 0x1349ad8: ldur            x0, [fp, #-0x18]
    // 0x1349adc: stur            x1, [fp, #-0x20]
    // 0x1349ae0: StoreField: r1->field_f = r0
    //     0x1349ae0: stur            w0, [x1, #0xf]
    // 0x1349ae4: r0 = Instance_BoxShape
    //     0x1349ae4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1349ae8: ldr             x0, [x0, #0x80]
    // 0x1349aec: StoreField: r1->field_23 = r0
    //     0x1349aec: stur            w0, [x1, #0x23]
    // 0x1349af0: r16 = <EdgeInsets>
    //     0x1349af0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1349af4: ldr             x16, [x16, #0xda0]
    // 0x1349af8: r30 = Instance_EdgeInsets
    //     0x1349af8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1349afc: ldr             lr, [lr, #0x1f0]
    // 0x1349b00: stp             lr, x16, [SP]
    // 0x1349b04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1349b04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1349b08: r0 = all()
    //     0x1349b08: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1349b0c: ldur            x2, [fp, #-8]
    // 0x1349b10: stur            x0, [fp, #-0x18]
    // 0x1349b14: LoadField: r1 = r2->field_f
    //     0x1349b14: ldur            w1, [x2, #0xf]
    // 0x1349b18: DecompressPointer r1
    //     0x1349b18: add             x1, x1, HEAP, lsl #32
    // 0x1349b1c: r0 = controller()
    //     0x1349b1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1349b20: LoadField: r1 = r0->field_a7
    //     0x1349b20: ldur            w1, [x0, #0xa7]
    // 0x1349b24: DecompressPointer r1
    //     0x1349b24: add             x1, x1, HEAP, lsl #32
    // 0x1349b28: r0 = value()
    //     0x1349b28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1349b2c: tbnz            w0, #4, #0x1349b4c
    // 0x1349b30: ldur            x2, [fp, #-8]
    // 0x1349b34: LoadField: r1 = r2->field_13
    //     0x1349b34: ldur            w1, [x2, #0x13]
    // 0x1349b38: DecompressPointer r1
    //     0x1349b38: add             x1, x1, HEAP, lsl #32
    // 0x1349b3c: r0 = of()
    //     0x1349b3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349b40: LoadField: r1 = r0->field_5b
    //     0x1349b40: ldur            w1, [x0, #0x5b]
    // 0x1349b44: DecompressPointer r1
    //     0x1349b44: add             x1, x1, HEAP, lsl #32
    // 0x1349b48: b               #0x1349b54
    // 0x1349b4c: r1 = Instance_MaterialColor
    //     0x1349b4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x1349b50: ldr             x1, [x1, #0xdc0]
    // 0x1349b54: ldur            x2, [fp, #-8]
    // 0x1349b58: ldur            x0, [fp, #-0x18]
    // 0x1349b5c: r16 = <Color>
    //     0x1349b5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1349b60: ldr             x16, [x16, #0xf80]
    // 0x1349b64: stp             x1, x16, [SP]
    // 0x1349b68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1349b68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1349b6c: r0 = all()
    //     0x1349b6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1349b70: stur            x0, [fp, #-0x28]
    // 0x1349b74: r16 = <RoundedRectangleBorder>
    //     0x1349b74: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1349b78: ldr             x16, [x16, #0xf78]
    // 0x1349b7c: r30 = Instance_RoundedRectangleBorder
    //     0x1349b7c: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x1349b80: ldr             lr, [lr, #0x888]
    // 0x1349b84: stp             lr, x16, [SP]
    // 0x1349b88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1349b88: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1349b8c: r0 = all()
    //     0x1349b8c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1349b90: stur            x0, [fp, #-0x30]
    // 0x1349b94: r0 = ButtonStyle()
    //     0x1349b94: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1349b98: mov             x1, x0
    // 0x1349b9c: ldur            x0, [fp, #-0x28]
    // 0x1349ba0: stur            x1, [fp, #-0x38]
    // 0x1349ba4: StoreField: r1->field_b = r0
    //     0x1349ba4: stur            w0, [x1, #0xb]
    // 0x1349ba8: ldur            x0, [fp, #-0x18]
    // 0x1349bac: StoreField: r1->field_23 = r0
    //     0x1349bac: stur            w0, [x1, #0x23]
    // 0x1349bb0: ldur            x0, [fp, #-0x30]
    // 0x1349bb4: StoreField: r1->field_43 = r0
    //     0x1349bb4: stur            w0, [x1, #0x43]
    // 0x1349bb8: r0 = TextButtonThemeData()
    //     0x1349bb8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1349bbc: mov             x2, x0
    // 0x1349bc0: ldur            x0, [fp, #-0x38]
    // 0x1349bc4: stur            x2, [fp, #-0x18]
    // 0x1349bc8: StoreField: r2->field_7 = r0
    //     0x1349bc8: stur            w0, [x2, #7]
    // 0x1349bcc: ldur            x0, [fp, #-8]
    // 0x1349bd0: LoadField: r1 = r0->field_f
    //     0x1349bd0: ldur            w1, [x0, #0xf]
    // 0x1349bd4: DecompressPointer r1
    //     0x1349bd4: add             x1, x1, HEAP, lsl #32
    // 0x1349bd8: r0 = controller()
    //     0x1349bd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1349bdc: LoadField: r1 = r0->field_a7
    //     0x1349bdc: ldur            w1, [x0, #0xa7]
    // 0x1349be0: DecompressPointer r1
    //     0x1349be0: add             x1, x1, HEAP, lsl #32
    // 0x1349be4: r0 = value()
    //     0x1349be4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1349be8: tbnz            w0, #4, #0x1349c04
    // 0x1349bec: ldur            x2, [fp, #-8]
    // 0x1349bf0: r1 = Function '<anonymous closure>':.
    //     0x1349bf0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44190] AnonymousClosure: (0x12e7054), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::bottomNavigationBar (0x135db28)
    //     0x1349bf4: ldr             x1, [x1, #0x190]
    // 0x1349bf8: r0 = AllocateClosure()
    //     0x1349bf8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1349bfc: mov             x4, x0
    // 0x1349c00: b               #0x1349c08
    // 0x1349c04: r4 = Null
    //     0x1349c04: mov             x4, NULL
    // 0x1349c08: ldur            x2, [fp, #-8]
    // 0x1349c0c: ldur            x3, [fp, #-0x10]
    // 0x1349c10: ldur            d0, [fp, #-0x40]
    // 0x1349c14: ldur            x0, [fp, #-0x18]
    // 0x1349c18: stur            x4, [fp, #-0x28]
    // 0x1349c1c: LoadField: r1 = r2->field_13
    //     0x1349c1c: ldur            w1, [x2, #0x13]
    // 0x1349c20: DecompressPointer r1
    //     0x1349c20: add             x1, x1, HEAP, lsl #32
    // 0x1349c24: r0 = of()
    //     0x1349c24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349c28: LoadField: r1 = r0->field_87
    //     0x1349c28: ldur            w1, [x0, #0x87]
    // 0x1349c2c: DecompressPointer r1
    //     0x1349c2c: add             x1, x1, HEAP, lsl #32
    // 0x1349c30: LoadField: r0 = r1->field_7
    //     0x1349c30: ldur            w0, [x1, #7]
    // 0x1349c34: DecompressPointer r0
    //     0x1349c34: add             x0, x0, HEAP, lsl #32
    // 0x1349c38: r16 = 14.000000
    //     0x1349c38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1349c3c: ldr             x16, [x16, #0x1d8]
    // 0x1349c40: r30 = Instance_Color
    //     0x1349c40: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1349c44: stp             lr, x16, [SP]
    // 0x1349c48: mov             x1, x0
    // 0x1349c4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1349c4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1349c50: ldr             x4, [x4, #0xaa0]
    // 0x1349c54: r0 = copyWith()
    //     0x1349c54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1349c58: stur            x0, [fp, #-0x30]
    // 0x1349c5c: r0 = Text()
    //     0x1349c5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1349c60: mov             x1, x0
    // 0x1349c64: r0 = "Continue"
    //     0x1349c64: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x1349c68: ldr             x0, [x0, #0xfe0]
    // 0x1349c6c: stur            x1, [fp, #-0x38]
    // 0x1349c70: StoreField: r1->field_b = r0
    //     0x1349c70: stur            w0, [x1, #0xb]
    // 0x1349c74: ldur            x0, [fp, #-0x30]
    // 0x1349c78: StoreField: r1->field_13 = r0
    //     0x1349c78: stur            w0, [x1, #0x13]
    // 0x1349c7c: r0 = TextButton()
    //     0x1349c7c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1349c80: mov             x1, x0
    // 0x1349c84: ldur            x0, [fp, #-0x28]
    // 0x1349c88: stur            x1, [fp, #-0x30]
    // 0x1349c8c: StoreField: r1->field_b = r0
    //     0x1349c8c: stur            w0, [x1, #0xb]
    // 0x1349c90: r0 = false
    //     0x1349c90: add             x0, NULL, #0x30  ; false
    // 0x1349c94: StoreField: r1->field_27 = r0
    //     0x1349c94: stur            w0, [x1, #0x27]
    // 0x1349c98: r2 = true
    //     0x1349c98: add             x2, NULL, #0x20  ; true
    // 0x1349c9c: StoreField: r1->field_2f = r2
    //     0x1349c9c: stur            w2, [x1, #0x2f]
    // 0x1349ca0: ldur            x3, [fp, #-0x38]
    // 0x1349ca4: StoreField: r1->field_37 = r3
    //     0x1349ca4: stur            w3, [x1, #0x37]
    // 0x1349ca8: r0 = TextButtonTheme()
    //     0x1349ca8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1349cac: mov             x1, x0
    // 0x1349cb0: ldur            x0, [fp, #-0x18]
    // 0x1349cb4: stur            x1, [fp, #-0x28]
    // 0x1349cb8: StoreField: r1->field_f = r0
    //     0x1349cb8: stur            w0, [x1, #0xf]
    // 0x1349cbc: ldur            x0, [fp, #-0x30]
    // 0x1349cc0: StoreField: r1->field_b = r0
    //     0x1349cc0: stur            w0, [x1, #0xb]
    // 0x1349cc4: ldur            d0, [fp, #-0x40]
    // 0x1349cc8: r0 = inline_Allocate_Double()
    //     0x1349cc8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x1349ccc: add             x0, x0, #0x10
    //     0x1349cd0: cmp             x2, x0
    //     0x1349cd4: b.ls            #0x1349fd8
    //     0x1349cd8: str             x0, [THR, #0x50]  ; THR::top
    //     0x1349cdc: sub             x0, x0, #0xf
    //     0x1349ce0: movz            x2, #0xe15c
    //     0x1349ce4: movk            x2, #0x3, lsl #16
    //     0x1349ce8: stur            x2, [x0, #-1]
    // 0x1349cec: StoreField: r0->field_7 = d0
    //     0x1349cec: stur            d0, [x0, #7]
    // 0x1349cf0: stur            x0, [fp, #-0x18]
    // 0x1349cf4: r0 = Container()
    //     0x1349cf4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1349cf8: stur            x0, [fp, #-0x30]
    // 0x1349cfc: r16 = Instance_EdgeInsets
    //     0x1349cfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x1349d00: ldr             x16, [x16, #0xe48]
    // 0x1349d04: ldur            lr, [fp, #-0x18]
    // 0x1349d08: stp             lr, x16, [SP, #0x10]
    // 0x1349d0c: ldur            x16, [fp, #-0x20]
    // 0x1349d10: ldur            lr, [fp, #-0x28]
    // 0x1349d14: stp             lr, x16, [SP]
    // 0x1349d18: mov             x1, x0
    // 0x1349d1c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x1349d1c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x1349d20: ldr             x4, [x4, #0x18]
    // 0x1349d24: r0 = Container()
    //     0x1349d24: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1349d28: r0 = GetNavigation.size()
    //     0x1349d28: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1349d2c: LoadField: d0 = r0->field_7
    //     0x1349d2c: ldur            d0, [x0, #7]
    // 0x1349d30: ldur            x0, [fp, #-8]
    // 0x1349d34: stur            d0, [fp, #-0x40]
    // 0x1349d38: LoadField: r1 = r0->field_13
    //     0x1349d38: ldur            w1, [x0, #0x13]
    // 0x1349d3c: DecompressPointer r1
    //     0x1349d3c: add             x1, x1, HEAP, lsl #32
    // 0x1349d40: r0 = of()
    //     0x1349d40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349d44: LoadField: r1 = r0->field_87
    //     0x1349d44: ldur            w1, [x0, #0x87]
    // 0x1349d48: DecompressPointer r1
    //     0x1349d48: add             x1, x1, HEAP, lsl #32
    // 0x1349d4c: LoadField: r0 = r1->field_2b
    //     0x1349d4c: ldur            w0, [x1, #0x2b]
    // 0x1349d50: DecompressPointer r0
    //     0x1349d50: add             x0, x0, HEAP, lsl #32
    // 0x1349d54: stur            x0, [fp, #-8]
    // 0x1349d58: r1 = Instance_Color
    //     0x1349d58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1349d5c: d0 = 0.700000
    //     0x1349d5c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1349d60: ldr             d0, [x17, #0xf48]
    // 0x1349d64: r0 = withOpacity()
    //     0x1349d64: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1349d68: r16 = 10.000000
    //     0x1349d68: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x1349d6c: stp             x0, x16, [SP]
    // 0x1349d70: ldur            x1, [fp, #-8]
    // 0x1349d74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1349d74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1349d78: ldr             x4, [x4, #0xaa0]
    // 0x1349d7c: r0 = copyWith()
    //     0x1349d7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1349d80: stur            x0, [fp, #-8]
    // 0x1349d84: r0 = Text()
    //     0x1349d84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1349d88: mov             x1, x0
    // 0x1349d8c: r0 = "Powered By"
    //     0x1349d8c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x1349d90: ldr             x0, [x0, #0x750]
    // 0x1349d94: stur            x1, [fp, #-0x18]
    // 0x1349d98: StoreField: r1->field_b = r0
    //     0x1349d98: stur            w0, [x1, #0xb]
    // 0x1349d9c: ldur            x0, [fp, #-8]
    // 0x1349da0: StoreField: r1->field_13 = r0
    //     0x1349da0: stur            w0, [x1, #0x13]
    // 0x1349da4: r0 = SvgPicture()
    //     0x1349da4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1349da8: stur            x0, [fp, #-8]
    // 0x1349dac: r16 = 20.000000
    //     0x1349dac: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x1349db0: ldr             x16, [x16, #0xac8]
    // 0x1349db4: str             x16, [SP]
    // 0x1349db8: mov             x1, x0
    // 0x1349dbc: r2 = "assets/images/shopdeck.svg"
    //     0x1349dbc: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x1349dc0: ldr             x2, [x2, #0x758]
    // 0x1349dc4: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x1349dc4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x1349dc8: ldr             x4, [x4, #0x760]
    // 0x1349dcc: r0 = SvgPicture.asset()
    //     0x1349dcc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1349dd0: r1 = Null
    //     0x1349dd0: mov             x1, NULL
    // 0x1349dd4: r2 = 4
    //     0x1349dd4: movz            x2, #0x4
    // 0x1349dd8: r0 = AllocateArray()
    //     0x1349dd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1349ddc: mov             x2, x0
    // 0x1349de0: ldur            x0, [fp, #-0x18]
    // 0x1349de4: stur            x2, [fp, #-0x20]
    // 0x1349de8: StoreField: r2->field_f = r0
    //     0x1349de8: stur            w0, [x2, #0xf]
    // 0x1349dec: ldur            x0, [fp, #-8]
    // 0x1349df0: StoreField: r2->field_13 = r0
    //     0x1349df0: stur            w0, [x2, #0x13]
    // 0x1349df4: r1 = <Widget>
    //     0x1349df4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1349df8: r0 = AllocateGrowableArray()
    //     0x1349df8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1349dfc: mov             x1, x0
    // 0x1349e00: ldur            x0, [fp, #-0x20]
    // 0x1349e04: stur            x1, [fp, #-8]
    // 0x1349e08: StoreField: r1->field_f = r0
    //     0x1349e08: stur            w0, [x1, #0xf]
    // 0x1349e0c: r2 = 4
    //     0x1349e0c: movz            x2, #0x4
    // 0x1349e10: StoreField: r1->field_b = r2
    //     0x1349e10: stur            w2, [x1, #0xb]
    // 0x1349e14: r0 = Row()
    //     0x1349e14: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1349e18: mov             x1, x0
    // 0x1349e1c: r0 = Instance_Axis
    //     0x1349e1c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1349e20: stur            x1, [fp, #-0x18]
    // 0x1349e24: StoreField: r1->field_f = r0
    //     0x1349e24: stur            w0, [x1, #0xf]
    // 0x1349e28: r0 = Instance_MainAxisAlignment
    //     0x1349e28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1349e2c: ldr             x0, [x0, #0xa08]
    // 0x1349e30: StoreField: r1->field_13 = r0
    //     0x1349e30: stur            w0, [x1, #0x13]
    // 0x1349e34: r2 = Instance_MainAxisSize
    //     0x1349e34: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1349e38: ldr             x2, [x2, #0xdd0]
    // 0x1349e3c: ArrayStore: r1[0] = r2  ; List_4
    //     0x1349e3c: stur            w2, [x1, #0x17]
    // 0x1349e40: r3 = Instance_CrossAxisAlignment
    //     0x1349e40: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1349e44: ldr             x3, [x3, #0xa18]
    // 0x1349e48: StoreField: r1->field_1b = r3
    //     0x1349e48: stur            w3, [x1, #0x1b]
    // 0x1349e4c: r4 = Instance_VerticalDirection
    //     0x1349e4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1349e50: ldr             x4, [x4, #0xa20]
    // 0x1349e54: StoreField: r1->field_23 = r4
    //     0x1349e54: stur            w4, [x1, #0x23]
    // 0x1349e58: r5 = Instance_Clip
    //     0x1349e58: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1349e5c: ldr             x5, [x5, #0x38]
    // 0x1349e60: StoreField: r1->field_2b = r5
    //     0x1349e60: stur            w5, [x1, #0x2b]
    // 0x1349e64: StoreField: r1->field_2f = rZR
    //     0x1349e64: stur            xzr, [x1, #0x2f]
    // 0x1349e68: ldur            x6, [fp, #-8]
    // 0x1349e6c: StoreField: r1->field_b = r6
    //     0x1349e6c: stur            w6, [x1, #0xb]
    // 0x1349e70: ldur            d0, [fp, #-0x40]
    // 0x1349e74: r6 = inline_Allocate_Double()
    //     0x1349e74: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x1349e78: add             x6, x6, #0x10
    //     0x1349e7c: cmp             x7, x6
    //     0x1349e80: b.ls            #0x1349ff0
    //     0x1349e84: str             x6, [THR, #0x50]  ; THR::top
    //     0x1349e88: sub             x6, x6, #0xf
    //     0x1349e8c: movz            x7, #0xe15c
    //     0x1349e90: movk            x7, #0x3, lsl #16
    //     0x1349e94: stur            x7, [x6, #-1]
    // 0x1349e98: StoreField: r6->field_7 = d0
    //     0x1349e98: stur            d0, [x6, #7]
    // 0x1349e9c: stur            x6, [fp, #-8]
    // 0x1349ea0: r0 = Container()
    //     0x1349ea0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1349ea4: stur            x0, [fp, #-0x20]
    // 0x1349ea8: r16 = Instance_Alignment
    //     0x1349ea8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1349eac: ldr             x16, [x16, #0xb10]
    // 0x1349eb0: ldur            lr, [fp, #-8]
    // 0x1349eb4: stp             lr, x16, [SP, #0x10]
    // 0x1349eb8: r16 = Instance_BoxDecoration
    //     0x1349eb8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x1349ebc: ldr             x16, [x16, #0x768]
    // 0x1349ec0: ldur            lr, [fp, #-0x18]
    // 0x1349ec4: stp             lr, x16, [SP]
    // 0x1349ec8: mov             x1, x0
    // 0x1349ecc: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x1349ecc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x1349ed0: ldr             x4, [x4, #0x770]
    // 0x1349ed4: r0 = Container()
    //     0x1349ed4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1349ed8: r1 = Null
    //     0x1349ed8: mov             x1, NULL
    // 0x1349edc: r2 = 4
    //     0x1349edc: movz            x2, #0x4
    // 0x1349ee0: r0 = AllocateArray()
    //     0x1349ee0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1349ee4: mov             x2, x0
    // 0x1349ee8: ldur            x0, [fp, #-0x30]
    // 0x1349eec: stur            x2, [fp, #-8]
    // 0x1349ef0: StoreField: r2->field_f = r0
    //     0x1349ef0: stur            w0, [x2, #0xf]
    // 0x1349ef4: ldur            x0, [fp, #-0x20]
    // 0x1349ef8: StoreField: r2->field_13 = r0
    //     0x1349ef8: stur            w0, [x2, #0x13]
    // 0x1349efc: r1 = <Widget>
    //     0x1349efc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1349f00: r0 = AllocateGrowableArray()
    //     0x1349f00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1349f04: mov             x1, x0
    // 0x1349f08: ldur            x0, [fp, #-8]
    // 0x1349f0c: stur            x1, [fp, #-0x18]
    // 0x1349f10: StoreField: r1->field_f = r0
    //     0x1349f10: stur            w0, [x1, #0xf]
    // 0x1349f14: r0 = 4
    //     0x1349f14: movz            x0, #0x4
    // 0x1349f18: StoreField: r1->field_b = r0
    //     0x1349f18: stur            w0, [x1, #0xb]
    // 0x1349f1c: r0 = Column()
    //     0x1349f1c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1349f20: mov             x1, x0
    // 0x1349f24: r0 = Instance_Axis
    //     0x1349f24: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1349f28: stur            x1, [fp, #-8]
    // 0x1349f2c: StoreField: r1->field_f = r0
    //     0x1349f2c: stur            w0, [x1, #0xf]
    // 0x1349f30: r0 = Instance_MainAxisAlignment
    //     0x1349f30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1349f34: ldr             x0, [x0, #0xa08]
    // 0x1349f38: StoreField: r1->field_13 = r0
    //     0x1349f38: stur            w0, [x1, #0x13]
    // 0x1349f3c: r0 = Instance_MainAxisSize
    //     0x1349f3c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1349f40: ldr             x0, [x0, #0xdd0]
    // 0x1349f44: ArrayStore: r1[0] = r0  ; List_4
    //     0x1349f44: stur            w0, [x1, #0x17]
    // 0x1349f48: r0 = Instance_CrossAxisAlignment
    //     0x1349f48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1349f4c: ldr             x0, [x0, #0xa18]
    // 0x1349f50: StoreField: r1->field_1b = r0
    //     0x1349f50: stur            w0, [x1, #0x1b]
    // 0x1349f54: r0 = Instance_VerticalDirection
    //     0x1349f54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1349f58: ldr             x0, [x0, #0xa20]
    // 0x1349f5c: StoreField: r1->field_23 = r0
    //     0x1349f5c: stur            w0, [x1, #0x23]
    // 0x1349f60: r0 = Instance_Clip
    //     0x1349f60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1349f64: ldr             x0, [x0, #0x38]
    // 0x1349f68: StoreField: r1->field_2b = r0
    //     0x1349f68: stur            w0, [x1, #0x2b]
    // 0x1349f6c: StoreField: r1->field_2f = rZR
    //     0x1349f6c: stur            xzr, [x1, #0x2f]
    // 0x1349f70: ldur            x0, [fp, #-0x18]
    // 0x1349f74: StoreField: r1->field_b = r0
    //     0x1349f74: stur            w0, [x1, #0xb]
    // 0x1349f78: r0 = Padding()
    //     0x1349f78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1349f7c: mov             x1, x0
    // 0x1349f80: ldur            x0, [fp, #-0x10]
    // 0x1349f84: stur            x1, [fp, #-0x18]
    // 0x1349f88: StoreField: r1->field_f = r0
    //     0x1349f88: stur            w0, [x1, #0xf]
    // 0x1349f8c: ldur            x0, [fp, #-8]
    // 0x1349f90: StoreField: r1->field_b = r0
    //     0x1349f90: stur            w0, [x1, #0xb]
    // 0x1349f94: r0 = SafeArea()
    //     0x1349f94: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1349f98: r1 = true
    //     0x1349f98: add             x1, NULL, #0x20  ; true
    // 0x1349f9c: StoreField: r0->field_b = r1
    //     0x1349f9c: stur            w1, [x0, #0xb]
    // 0x1349fa0: StoreField: r0->field_f = r1
    //     0x1349fa0: stur            w1, [x0, #0xf]
    // 0x1349fa4: StoreField: r0->field_13 = r1
    //     0x1349fa4: stur            w1, [x0, #0x13]
    // 0x1349fa8: ArrayStore: r0[0] = r1  ; List_4
    //     0x1349fa8: stur            w1, [x0, #0x17]
    // 0x1349fac: r1 = Instance_EdgeInsets
    //     0x1349fac: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1349fb0: StoreField: r0->field_1b = r1
    //     0x1349fb0: stur            w1, [x0, #0x1b]
    // 0x1349fb4: r1 = false
    //     0x1349fb4: add             x1, NULL, #0x30  ; false
    // 0x1349fb8: StoreField: r0->field_1f = r1
    //     0x1349fb8: stur            w1, [x0, #0x1f]
    // 0x1349fbc: ldur            x1, [fp, #-0x18]
    // 0x1349fc0: StoreField: r0->field_23 = r1
    //     0x1349fc0: stur            w1, [x0, #0x23]
    // 0x1349fc4: LeaveFrame
    //     0x1349fc4: mov             SP, fp
    //     0x1349fc8: ldp             fp, lr, [SP], #0x10
    // 0x1349fcc: ret
    //     0x1349fcc: ret             
    // 0x1349fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1349fd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1349fd4: b               #0x13499e4
    // 0x1349fd8: SaveReg d0
    //     0x1349fd8: str             q0, [SP, #-0x10]!
    // 0x1349fdc: SaveReg r1
    //     0x1349fdc: str             x1, [SP, #-8]!
    // 0x1349fe0: r0 = AllocateDouble()
    //     0x1349fe0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1349fe4: RestoreReg r1
    //     0x1349fe4: ldr             x1, [SP], #8
    // 0x1349fe8: RestoreReg d0
    //     0x1349fe8: ldr             q0, [SP], #0x10
    // 0x1349fec: b               #0x1349cec
    // 0x1349ff0: SaveReg d0
    //     0x1349ff0: str             q0, [SP, #-0x10]!
    // 0x1349ff4: stp             x4, x5, [SP, #-0x10]!
    // 0x1349ff8: stp             x2, x3, [SP, #-0x10]!
    // 0x1349ffc: stp             x0, x1, [SP, #-0x10]!
    // 0x134a000: r0 = AllocateDouble()
    //     0x134a000: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x134a004: mov             x6, x0
    // 0x134a008: ldp             x0, x1, [SP], #0x10
    // 0x134a00c: ldp             x2, x3, [SP], #0x10
    // 0x134a010: ldp             x4, x5, [SP], #0x10
    // 0x134a014: RestoreReg d0
    //     0x134a014: ldr             q0, [SP], #0x10
    // 0x134a018: b               #0x1349e98
  }
  _ body(/* No info */) {
    // ** addr: 0x14855e4, size: 0x118
    // 0x14855e4: EnterFrame
    //     0x14855e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14855e8: mov             fp, SP
    // 0x14855ec: AllocStack(0x18)
    //     0x14855ec: sub             SP, SP, #0x18
    // 0x14855f0: SetupParameters(CheckoutRequestNumberPage this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x14855f0: stur            x1, [fp, #-8]
    //     0x14855f4: mov             x16, x2
    //     0x14855f8: mov             x2, x1
    //     0x14855fc: mov             x1, x16
    //     0x1485600: stur            x1, [fp, #-0x10]
    // 0x1485604: CheckStackOverflow
    //     0x1485604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485608: cmp             SP, x16
    //     0x148560c: b.ls            #0x14856f4
    // 0x1485610: r1 = 3
    //     0x1485610: movz            x1, #0x3
    // 0x1485614: r0 = AllocateContext()
    //     0x1485614: bl              #0x16f6108  ; AllocateContextStub
    // 0x1485618: ldur            x2, [fp, #-8]
    // 0x148561c: stur            x0, [fp, #-0x18]
    // 0x1485620: StoreField: r0->field_f = r2
    //     0x1485620: stur            w2, [x0, #0xf]
    // 0x1485624: ldur            x1, [fp, #-0x10]
    // 0x1485628: StoreField: r0->field_13 = r1
    //     0x1485628: stur            w1, [x0, #0x13]
    // 0x148562c: r0 = of()
    //     0x148562c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1485630: LoadField: r1 = r0->field_87
    //     0x1485630: ldur            w1, [x0, #0x87]
    // 0x1485634: DecompressPointer r1
    //     0x1485634: add             x1, x1, HEAP, lsl #32
    // 0x1485638: mov             x0, x1
    // 0x148563c: ldur            x2, [fp, #-0x18]
    // 0x1485640: ArrayStore: r2[0] = r0  ; List_4
    //     0x1485640: stur            w0, [x2, #0x17]
    //     0x1485644: ldurb           w16, [x2, #-1]
    //     0x1485648: ldurb           w17, [x0, #-1]
    //     0x148564c: and             x16, x17, x16, lsr #2
    //     0x1485650: tst             x16, HEAP, lsr #32
    //     0x1485654: b.eq            #0x148565c
    //     0x1485658: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x148565c: r0 = Obx()
    //     0x148565c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1485660: ldur            x2, [fp, #-0x18]
    // 0x1485664: r1 = Function '<anonymous closure>':.
    //     0x1485664: add             x1, PP, #0x44, lsl #12  ; [pp+0x44198] AnonymousClosure: (0x14857e4), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14855e4)
    //     0x1485668: ldr             x1, [x1, #0x198]
    // 0x148566c: stur            x0, [fp, #-0x10]
    // 0x1485670: r0 = AllocateClosure()
    //     0x1485670: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485674: mov             x1, x0
    // 0x1485678: ldur            x0, [fp, #-0x10]
    // 0x148567c: StoreField: r0->field_b = r1
    //     0x148567c: stur            w1, [x0, #0xb]
    // 0x1485680: r0 = SafeArea()
    //     0x1485680: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1485684: mov             x1, x0
    // 0x1485688: r0 = true
    //     0x1485688: add             x0, NULL, #0x20  ; true
    // 0x148568c: stur            x1, [fp, #-0x18]
    // 0x1485690: StoreField: r1->field_b = r0
    //     0x1485690: stur            w0, [x1, #0xb]
    // 0x1485694: StoreField: r1->field_f = r0
    //     0x1485694: stur            w0, [x1, #0xf]
    // 0x1485698: StoreField: r1->field_13 = r0
    //     0x1485698: stur            w0, [x1, #0x13]
    // 0x148569c: ArrayStore: r1[0] = r0  ; List_4
    //     0x148569c: stur            w0, [x1, #0x17]
    // 0x14856a0: r0 = Instance_EdgeInsets
    //     0x14856a0: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14856a4: StoreField: r1->field_1b = r0
    //     0x14856a4: stur            w0, [x1, #0x1b]
    // 0x14856a8: r0 = false
    //     0x14856a8: add             x0, NULL, #0x30  ; false
    // 0x14856ac: StoreField: r1->field_1f = r0
    //     0x14856ac: stur            w0, [x1, #0x1f]
    // 0x14856b0: ldur            x0, [fp, #-0x10]
    // 0x14856b4: StoreField: r1->field_23 = r0
    //     0x14856b4: stur            w0, [x1, #0x23]
    // 0x14856b8: r0 = WillPopScope()
    //     0x14856b8: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14856bc: mov             x3, x0
    // 0x14856c0: ldur            x0, [fp, #-0x18]
    // 0x14856c4: stur            x3, [fp, #-0x10]
    // 0x14856c8: StoreField: r3->field_b = r0
    //     0x14856c8: stur            w0, [x3, #0xb]
    // 0x14856cc: ldur            x2, [fp, #-8]
    // 0x14856d0: r1 = Function 'getBack':.
    //     0x14856d0: add             x1, PP, #0x44, lsl #12  ; [pp+0x441a0] AnonymousClosure: (0x14856fc), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack (0x1485734)
    //     0x14856d4: ldr             x1, [x1, #0x1a0]
    // 0x14856d8: r0 = AllocateClosure()
    //     0x14856d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14856dc: mov             x1, x0
    // 0x14856e0: ldur            x0, [fp, #-0x10]
    // 0x14856e4: StoreField: r0->field_f = r1
    //     0x14856e4: stur            w1, [x0, #0xf]
    // 0x14856e8: LeaveFrame
    //     0x14856e8: mov             SP, fp
    //     0x14856ec: ldp             fp, lr, [SP], #0x10
    // 0x14856f0: ret
    //     0x14856f0: ret             
    // 0x14856f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14856f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14856f8: b               #0x1485610
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x14856fc, size: 0x38
    // 0x14856fc: EnterFrame
    //     0x14856fc: stp             fp, lr, [SP, #-0x10]!
    //     0x1485700: mov             fp, SP
    // 0x1485704: ldr             x0, [fp, #0x10]
    // 0x1485708: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1485708: ldur            w1, [x0, #0x17]
    // 0x148570c: DecompressPointer r1
    //     0x148570c: add             x1, x1, HEAP, lsl #32
    // 0x1485710: CheckStackOverflow
    //     0x1485710: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485714: cmp             SP, x16
    //     0x1485718: b.ls            #0x148572c
    // 0x148571c: r0 = getBack()
    //     0x148571c: bl              #0x1485734  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack
    // 0x1485720: LeaveFrame
    //     0x1485720: mov             SP, fp
    //     0x1485724: ldp             fp, lr, [SP], #0x10
    // 0x1485728: ret
    //     0x1485728: ret             
    // 0x148572c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148572c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485730: b               #0x148571c
  }
  _ getBack(/* No info */) {
    // ** addr: 0x1485734, size: 0xb0
    // 0x1485734: EnterFrame
    //     0x1485734: stp             fp, lr, [SP, #-0x10]!
    //     0x1485738: mov             fp, SP
    // 0x148573c: AllocStack(0x8)
    //     0x148573c: sub             SP, SP, #8
    // 0x1485740: CheckStackOverflow
    //     0x1485740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485744: cmp             SP, x16
    //     0x1485748: b.ls            #0x14857dc
    // 0x148574c: r2 = false
    //     0x148574c: add             x2, NULL, #0x30  ; false
    // 0x1485750: r0 = showLoading()
    //     0x1485750: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x1485754: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1485754: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1485758: ldr             x0, [x0, #0x1c80]
    //     0x148575c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1485760: cmp             w0, w16
    //     0x1485764: b.ne            #0x1485770
    //     0x1485768: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x148576c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1485770: r1 = Function '<anonymous closure>':.
    //     0x1485770: add             x1, PP, #0x44, lsl #12  ; [pp+0x441a8] AnonymousClosure: (0x1390d2c), in [package:customer_app/app/presentation/views/line/checkout_variants/checkout_variants_view.dart] CheckoutVariantsView::getBack (0x1390ea0)
    //     0x1485774: ldr             x1, [x1, #0x1a8]
    // 0x1485778: r2 = Null
    //     0x1485778: mov             x2, NULL
    // 0x148577c: r0 = AllocateClosure()
    //     0x148577c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485780: mov             x1, x0
    // 0x1485784: r0 = GetNavigation.until()
    //     0x1485784: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x1485788: r1 = <bool>
    //     0x1485788: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x148578c: r0 = _Future()
    //     0x148578c: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x1485790: stur            x0, [fp, #-8]
    // 0x1485794: StoreField: r0->field_b = rZR
    //     0x1485794: stur            xzr, [x0, #0xb]
    // 0x1485798: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x1485798: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x148579c: ldr             x0, [x0, #0x778]
    //     0x14857a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14857a4: cmp             w0, w16
    //     0x14857a8: b.ne            #0x14857b4
    //     0x14857ac: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x14857b0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x14857b4: mov             x1, x0
    // 0x14857b8: ldur            x0, [fp, #-8]
    // 0x14857bc: StoreField: r0->field_13 = r1
    //     0x14857bc: stur            w1, [x0, #0x13]
    // 0x14857c0: mov             x1, x0
    // 0x14857c4: r2 = false
    //     0x14857c4: add             x2, NULL, #0x30  ; false
    // 0x14857c8: r0 = _asyncComplete()
    //     0x14857c8: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x14857cc: ldur            x0, [fp, #-8]
    // 0x14857d0: LeaveFrame
    //     0x14857d0: mov             SP, fp
    //     0x14857d4: ldp             fp, lr, [SP], #0x10
    // 0x14857d8: ret
    //     0x14857d8: ret             
    // 0x14857dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14857dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14857e0: b               #0x148574c
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14857e4, size: 0x414
    // 0x14857e4: EnterFrame
    //     0x14857e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14857e8: mov             fp, SP
    // 0x14857ec: AllocStack(0x50)
    //     0x14857ec: sub             SP, SP, #0x50
    // 0x14857f0: SetupParameters()
    //     0x14857f0: ldr             x0, [fp, #0x10]
    //     0x14857f4: ldur            w2, [x0, #0x17]
    //     0x14857f8: add             x2, x2, HEAP, lsl #32
    //     0x14857fc: stur            x2, [fp, #-8]
    // 0x1485800: CheckStackOverflow
    //     0x1485800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485804: cmp             SP, x16
    //     0x1485808: b.ls            #0x1485bf0
    // 0x148580c: LoadField: r1 = r2->field_f
    //     0x148580c: ldur            w1, [x2, #0xf]
    // 0x1485810: DecompressPointer r1
    //     0x1485810: add             x1, x1, HEAP, lsl #32
    // 0x1485814: r0 = controller()
    //     0x1485814: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485818: LoadField: r1 = r0->field_d7
    //     0x1485818: ldur            w1, [x0, #0xd7]
    // 0x148581c: DecompressPointer r1
    //     0x148581c: add             x1, x1, HEAP, lsl #32
    // 0x1485820: r0 = value()
    //     0x1485820: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485824: tbnz            w0, #4, #0x148595c
    // 0x1485828: ldur            x2, [fp, #-8]
    // 0x148582c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x148582c: ldur            w0, [x2, #0x17]
    // 0x1485830: DecompressPointer r0
    //     0x1485830: add             x0, x0, HEAP, lsl #32
    // 0x1485834: LoadField: r1 = r0->field_2b
    //     0x1485834: ldur            w1, [x0, #0x2b]
    // 0x1485838: DecompressPointer r1
    //     0x1485838: add             x1, x1, HEAP, lsl #32
    // 0x148583c: r16 = 12.000000
    //     0x148583c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1485840: ldr             x16, [x16, #0x9e8]
    // 0x1485844: r30 = Instance_Color
    //     0x1485844: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1485848: stp             lr, x16, [SP]
    // 0x148584c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x148584c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1485850: ldr             x4, [x4, #0xaa0]
    // 0x1485854: r0 = copyWith()
    //     0x1485854: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1485858: stur            x0, [fp, #-0x10]
    // 0x148585c: r0 = Text()
    //     0x148585c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1485860: mov             x3, x0
    // 0x1485864: r0 = "Fetching your saved addresses from ShopDeck"
    //     0x1485864: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3daa0] "Fetching your saved addresses from ShopDeck"
    //     0x1485868: ldr             x0, [x0, #0xaa0]
    // 0x148586c: stur            x3, [fp, #-0x18]
    // 0x1485870: StoreField: r3->field_b = r0
    //     0x1485870: stur            w0, [x3, #0xb]
    // 0x1485874: ldur            x0, [fp, #-0x10]
    // 0x1485878: StoreField: r3->field_13 = r0
    //     0x1485878: stur            w0, [x3, #0x13]
    // 0x148587c: r0 = Instance_TextAlign
    //     0x148587c: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x1485880: StoreField: r3->field_1b = r0
    //     0x1485880: stur            w0, [x3, #0x1b]
    // 0x1485884: r1 = Null
    //     0x1485884: mov             x1, NULL
    // 0x1485888: r2 = 6
    //     0x1485888: movz            x2, #0x6
    // 0x148588c: r0 = AllocateArray()
    //     0x148588c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1485890: stur            x0, [fp, #-0x10]
    // 0x1485894: r16 = Instance_CircularProgressIndicator
    //     0x1485894: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3daa8] Obj!CircularProgressIndicator@d65741
    //     0x1485898: ldr             x16, [x16, #0xaa8]
    // 0x148589c: StoreField: r0->field_f = r16
    //     0x148589c: stur            w16, [x0, #0xf]
    // 0x14858a0: r16 = Instance_SizedBox
    //     0x14858a0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x14858a4: ldr             x16, [x16, #0xd68]
    // 0x14858a8: StoreField: r0->field_13 = r16
    //     0x14858a8: stur            w16, [x0, #0x13]
    // 0x14858ac: ldur            x1, [fp, #-0x18]
    // 0x14858b0: ArrayStore: r0[0] = r1  ; List_4
    //     0x14858b0: stur            w1, [x0, #0x17]
    // 0x14858b4: r1 = <Widget>
    //     0x14858b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14858b8: r0 = AllocateGrowableArray()
    //     0x14858b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14858bc: mov             x1, x0
    // 0x14858c0: ldur            x0, [fp, #-0x10]
    // 0x14858c4: stur            x1, [fp, #-0x18]
    // 0x14858c8: StoreField: r1->field_f = r0
    //     0x14858c8: stur            w0, [x1, #0xf]
    // 0x14858cc: r0 = 6
    //     0x14858cc: movz            x0, #0x6
    // 0x14858d0: StoreField: r1->field_b = r0
    //     0x14858d0: stur            w0, [x1, #0xb]
    // 0x14858d4: r0 = Column()
    //     0x14858d4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14858d8: mov             x1, x0
    // 0x14858dc: r0 = Instance_Axis
    //     0x14858dc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14858e0: stur            x1, [fp, #-0x10]
    // 0x14858e4: StoreField: r1->field_f = r0
    //     0x14858e4: stur            w0, [x1, #0xf]
    // 0x14858e8: r0 = Instance_MainAxisAlignment
    //     0x14858e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x14858ec: ldr             x0, [x0, #0xab0]
    // 0x14858f0: StoreField: r1->field_13 = r0
    //     0x14858f0: stur            w0, [x1, #0x13]
    // 0x14858f4: r3 = Instance_MainAxisSize
    //     0x14858f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14858f8: ldr             x3, [x3, #0xa10]
    // 0x14858fc: ArrayStore: r1[0] = r3  ; List_4
    //     0x14858fc: stur            w3, [x1, #0x17]
    // 0x1485900: r4 = Instance_CrossAxisAlignment
    //     0x1485900: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1485904: ldr             x4, [x4, #0xa18]
    // 0x1485908: StoreField: r1->field_1b = r4
    //     0x1485908: stur            w4, [x1, #0x1b]
    // 0x148590c: r5 = Instance_VerticalDirection
    //     0x148590c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1485910: ldr             x5, [x5, #0xa20]
    // 0x1485914: StoreField: r1->field_23 = r5
    //     0x1485914: stur            w5, [x1, #0x23]
    // 0x1485918: r6 = Instance_Clip
    //     0x1485918: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148591c: ldr             x6, [x6, #0x38]
    // 0x1485920: StoreField: r1->field_2b = r6
    //     0x1485920: stur            w6, [x1, #0x2b]
    // 0x1485924: StoreField: r1->field_2f = rZR
    //     0x1485924: stur            xzr, [x1, #0x2f]
    // 0x1485928: ldur            x0, [fp, #-0x18]
    // 0x148592c: StoreField: r1->field_b = r0
    //     0x148592c: stur            w0, [x1, #0xb]
    // 0x1485930: r0 = Center()
    //     0x1485930: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1485934: mov             x1, x0
    // 0x1485938: r0 = Instance_Alignment
    //     0x1485938: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x148593c: ldr             x0, [x0, #0xb10]
    // 0x1485940: StoreField: r1->field_f = r0
    //     0x1485940: stur            w0, [x1, #0xf]
    // 0x1485944: ldur            x0, [fp, #-0x10]
    // 0x1485948: StoreField: r1->field_b = r0
    //     0x1485948: stur            w0, [x1, #0xb]
    // 0x148594c: mov             x0, x1
    // 0x1485950: LeaveFrame
    //     0x1485950: mov             SP, fp
    //     0x1485954: ldp             fp, lr, [SP], #0x10
    // 0x1485958: ret
    //     0x1485958: ret             
    // 0x148595c: ldur            x2, [fp, #-8]
    // 0x1485960: r4 = Instance_CrossAxisAlignment
    //     0x1485960: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1485964: ldr             x4, [x4, #0xa18]
    // 0x1485968: r3 = Instance_MainAxisSize
    //     0x1485968: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148596c: ldr             x3, [x3, #0xa10]
    // 0x1485970: r5 = Instance_VerticalDirection
    //     0x1485970: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1485974: ldr             x5, [x5, #0xa20]
    // 0x1485978: r0 = Instance_Axis
    //     0x1485978: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148597c: r6 = Instance_Clip
    //     0x148597c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1485980: ldr             x6, [x6, #0x38]
    // 0x1485984: r0 = Obx()
    //     0x1485984: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1485988: ldur            x2, [fp, #-8]
    // 0x148598c: r1 = Function '<anonymous closure>':.
    //     0x148598c: add             x1, PP, #0x44, lsl #12  ; [pp+0x441b0] AnonymousClosure: (0x1485db8), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14855e4)
    //     0x1485990: ldr             x1, [x1, #0x1b0]
    // 0x1485994: stur            x0, [fp, #-0x10]
    // 0x1485998: r0 = AllocateClosure()
    //     0x1485998: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148599c: mov             x1, x0
    // 0x14859a0: ldur            x0, [fp, #-0x10]
    // 0x14859a4: StoreField: r0->field_b = r1
    //     0x14859a4: stur            w1, [x0, #0xb]
    // 0x14859a8: r0 = Obx()
    //     0x14859a8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14859ac: ldur            x2, [fp, #-8]
    // 0x14859b0: r1 = Function '<anonymous closure>':.
    //     0x14859b0: add             x1, PP, #0x44, lsl #12  ; [pp+0x441b8] AnonymousClosure: (0x1485d50), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14855e4)
    //     0x14859b4: ldr             x1, [x1, #0x1b8]
    // 0x14859b8: stur            x0, [fp, #-0x18]
    // 0x14859bc: r0 = AllocateClosure()
    //     0x14859bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14859c0: mov             x1, x0
    // 0x14859c4: ldur            x0, [fp, #-0x18]
    // 0x14859c8: StoreField: r0->field_b = r1
    //     0x14859c8: stur            w1, [x0, #0xb]
    // 0x14859cc: ldur            x2, [fp, #-8]
    // 0x14859d0: LoadField: r1 = r2->field_f
    //     0x14859d0: ldur            w1, [x2, #0xf]
    // 0x14859d4: DecompressPointer r1
    //     0x14859d4: add             x1, x1, HEAP, lsl #32
    // 0x14859d8: r0 = controller()
    //     0x14859d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14859dc: LoadField: r1 = r0->field_5f
    //     0x14859dc: ldur            w1, [x0, #0x5f]
    // 0x14859e0: DecompressPointer r1
    //     0x14859e0: add             x1, x1, HEAP, lsl #32
    // 0x14859e4: r0 = value()
    //     0x14859e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14859e8: ldur            x2, [fp, #-8]
    // 0x14859ec: stur            x0, [fp, #-0x20]
    // 0x14859f0: LoadField: r1 = r2->field_f
    //     0x14859f0: ldur            w1, [x2, #0xf]
    // 0x14859f4: DecompressPointer r1
    //     0x14859f4: add             x1, x1, HEAP, lsl #32
    // 0x14859f8: r0 = controller()
    //     0x14859f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14859fc: LoadField: r1 = r0->field_5b
    //     0x14859fc: ldur            w1, [x0, #0x5b]
    // 0x1485a00: DecompressPointer r1
    //     0x1485a00: add             x1, x1, HEAP, lsl #32
    // 0x1485a04: r0 = value()
    //     0x1485a04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485a08: ldur            x2, [fp, #-8]
    // 0x1485a0c: stur            x0, [fp, #-0x28]
    // 0x1485a10: LoadField: r1 = r2->field_f
    //     0x1485a10: ldur            w1, [x2, #0xf]
    // 0x1485a14: DecompressPointer r1
    //     0x1485a14: add             x1, x1, HEAP, lsl #32
    // 0x1485a18: r0 = controller()
    //     0x1485a18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485a1c: LoadField: r1 = r0->field_53
    //     0x1485a1c: ldur            w1, [x0, #0x53]
    // 0x1485a20: DecompressPointer r1
    //     0x1485a20: add             x1, x1, HEAP, lsl #32
    // 0x1485a24: r0 = value()
    //     0x1485a24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485a28: ldur            x2, [fp, #-8]
    // 0x1485a2c: LoadField: r1 = r2->field_f
    //     0x1485a2c: ldur            w1, [x2, #0xf]
    // 0x1485a30: DecompressPointer r1
    //     0x1485a30: add             x1, x1, HEAP, lsl #32
    // 0x1485a34: r0 = controller()
    //     0x1485a34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485a38: LoadField: r1 = r0->field_93
    //     0x1485a38: ldur            w1, [x0, #0x93]
    // 0x1485a3c: DecompressPointer r1
    //     0x1485a3c: add             x1, x1, HEAP, lsl #32
    // 0x1485a40: stur            x1, [fp, #-0x30]
    // 0x1485a44: r0 = CheckoutBagAccordion()
    //     0x1485a44: bl              #0x1485570  ; AllocateCheckoutBagAccordionStub -> CheckoutBagAccordion (size=0x1c)
    // 0x1485a48: mov             x3, x0
    // 0x1485a4c: ldur            x0, [fp, #-0x20]
    // 0x1485a50: stur            x3, [fp, #-0x38]
    // 0x1485a54: StoreField: r3->field_b = r0
    //     0x1485a54: stur            w0, [x3, #0xb]
    // 0x1485a58: ldur            x0, [fp, #-0x28]
    // 0x1485a5c: StoreField: r3->field_f = r0
    //     0x1485a5c: stur            w0, [x3, #0xf]
    // 0x1485a60: ldur            x0, [fp, #-0x30]
    // 0x1485a64: StoreField: r3->field_13 = r0
    //     0x1485a64: stur            w0, [x3, #0x13]
    // 0x1485a68: ldur            x2, [fp, #-8]
    // 0x1485a6c: r1 = Function '<anonymous closure>':.
    //     0x1485a6c: add             x1, PP, #0x44, lsl #12  ; [pp+0x441c0] AnonymousClosure: (0x1391d98), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1485a70: ldr             x1, [x1, #0x1c0]
    // 0x1485a74: r0 = AllocateClosure()
    //     0x1485a74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485a78: mov             x1, x0
    // 0x1485a7c: ldur            x0, [fp, #-0x38]
    // 0x1485a80: ArrayStore: r0[0] = r1  ; List_4
    //     0x1485a80: stur            w1, [x0, #0x17]
    // 0x1485a84: ldur            x2, [fp, #-8]
    // 0x1485a88: LoadField: r1 = r2->field_f
    //     0x1485a88: ldur            w1, [x2, #0xf]
    // 0x1485a8c: DecompressPointer r1
    //     0x1485a8c: add             x1, x1, HEAP, lsl #32
    // 0x1485a90: r0 = controller()
    //     0x1485a90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485a94: LoadField: r1 = r0->field_7b
    //     0x1485a94: ldur            w1, [x0, #0x7b]
    // 0x1485a98: DecompressPointer r1
    //     0x1485a98: add             x1, x1, HEAP, lsl #32
    // 0x1485a9c: r0 = value()
    //     0x1485a9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485aa0: ldur            x2, [fp, #-8]
    // 0x1485aa4: stur            x0, [fp, #-0x20]
    // 0x1485aa8: LoadField: r1 = r2->field_f
    //     0x1485aa8: ldur            w1, [x2, #0xf]
    // 0x1485aac: DecompressPointer r1
    //     0x1485aac: add             x1, x1, HEAP, lsl #32
    // 0x1485ab0: r0 = controller()
    //     0x1485ab0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485ab4: LoadField: r1 = r0->field_cb
    //     0x1485ab4: ldur            w1, [x0, #0xcb]
    // 0x1485ab8: DecompressPointer r1
    //     0x1485ab8: add             x1, x1, HEAP, lsl #32
    // 0x1485abc: r0 = value()
    //     0x1485abc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485ac0: cmp             w0, NULL
    // 0x1485ac4: b.ne            #0x1485ad0
    // 0x1485ac8: r6 = false
    //     0x1485ac8: add             x6, NULL, #0x30  ; false
    // 0x1485acc: b               #0x1485ad4
    // 0x1485ad0: mov             x6, x0
    // 0x1485ad4: ldur            x5, [fp, #-0x10]
    // 0x1485ad8: ldur            x4, [fp, #-0x18]
    // 0x1485adc: ldur            x3, [fp, #-0x38]
    // 0x1485ae0: ldur            x0, [fp, #-0x20]
    // 0x1485ae4: ldur            x2, [fp, #-8]
    // 0x1485ae8: stur            x6, [fp, #-0x28]
    // 0x1485aec: r1 = Function '<anonymous closure>':.
    //     0x1485aec: add             x1, PP, #0x44, lsl #12  ; [pp+0x441c8] AnonymousClosure: (0x1485c04), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14855e4)
    //     0x1485af0: ldr             x1, [x1, #0x1c8]
    // 0x1485af4: r0 = AllocateClosure()
    //     0x1485af4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485af8: stur            x0, [fp, #-0x30]
    // 0x1485afc: r0 = CheckoutNumberWidget()
    //     0x1485afc: bl              #0x1485bf8  ; AllocateCheckoutNumberWidgetStub -> CheckoutNumberWidget (size=0x1c)
    // 0x1485b00: mov             x3, x0
    // 0x1485b04: ldur            x0, [fp, #-0x30]
    // 0x1485b08: stur            x3, [fp, #-0x40]
    // 0x1485b0c: StoreField: r3->field_b = r0
    //     0x1485b0c: stur            w0, [x3, #0xb]
    // 0x1485b10: ldur            x2, [fp, #-8]
    // 0x1485b14: r1 = Function '<anonymous closure>':.
    //     0x1485b14: add             x1, PP, #0x44, lsl #12  ; [pp+0x441d0] AnonymousClosure: (0x13913a8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x1500b5c)
    //     0x1485b18: ldr             x1, [x1, #0x1d0]
    // 0x1485b1c: r0 = AllocateClosure()
    //     0x1485b1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485b20: mov             x1, x0
    // 0x1485b24: ldur            x0, [fp, #-0x40]
    // 0x1485b28: StoreField: r0->field_f = r1
    //     0x1485b28: stur            w1, [x0, #0xf]
    // 0x1485b2c: ldur            x1, [fp, #-0x20]
    // 0x1485b30: StoreField: r0->field_13 = r1
    //     0x1485b30: stur            w1, [x0, #0x13]
    // 0x1485b34: ldur            x1, [fp, #-0x28]
    // 0x1485b38: ArrayStore: r0[0] = r1  ; List_4
    //     0x1485b38: stur            w1, [x0, #0x17]
    // 0x1485b3c: r1 = Null
    //     0x1485b3c: mov             x1, NULL
    // 0x1485b40: r2 = 8
    //     0x1485b40: movz            x2, #0x8
    // 0x1485b44: r0 = AllocateArray()
    //     0x1485b44: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1485b48: mov             x2, x0
    // 0x1485b4c: ldur            x0, [fp, #-0x10]
    // 0x1485b50: stur            x2, [fp, #-8]
    // 0x1485b54: StoreField: r2->field_f = r0
    //     0x1485b54: stur            w0, [x2, #0xf]
    // 0x1485b58: ldur            x0, [fp, #-0x18]
    // 0x1485b5c: StoreField: r2->field_13 = r0
    //     0x1485b5c: stur            w0, [x2, #0x13]
    // 0x1485b60: ldur            x0, [fp, #-0x38]
    // 0x1485b64: ArrayStore: r2[0] = r0  ; List_4
    //     0x1485b64: stur            w0, [x2, #0x17]
    // 0x1485b68: ldur            x0, [fp, #-0x40]
    // 0x1485b6c: StoreField: r2->field_1b = r0
    //     0x1485b6c: stur            w0, [x2, #0x1b]
    // 0x1485b70: r1 = <Widget>
    //     0x1485b70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1485b74: r0 = AllocateGrowableArray()
    //     0x1485b74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1485b78: mov             x1, x0
    // 0x1485b7c: ldur            x0, [fp, #-8]
    // 0x1485b80: stur            x1, [fp, #-0x10]
    // 0x1485b84: StoreField: r1->field_f = r0
    //     0x1485b84: stur            w0, [x1, #0xf]
    // 0x1485b88: r0 = 8
    //     0x1485b88: movz            x0, #0x8
    // 0x1485b8c: StoreField: r1->field_b = r0
    //     0x1485b8c: stur            w0, [x1, #0xb]
    // 0x1485b90: r0 = Column()
    //     0x1485b90: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1485b94: r1 = Instance_Axis
    //     0x1485b94: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1485b98: StoreField: r0->field_f = r1
    //     0x1485b98: stur            w1, [x0, #0xf]
    // 0x1485b9c: r1 = Instance_MainAxisAlignment
    //     0x1485b9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1485ba0: ldr             x1, [x1, #0xa08]
    // 0x1485ba4: StoreField: r0->field_13 = r1
    //     0x1485ba4: stur            w1, [x0, #0x13]
    // 0x1485ba8: r1 = Instance_MainAxisSize
    //     0x1485ba8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1485bac: ldr             x1, [x1, #0xa10]
    // 0x1485bb0: ArrayStore: r0[0] = r1  ; List_4
    //     0x1485bb0: stur            w1, [x0, #0x17]
    // 0x1485bb4: r1 = Instance_CrossAxisAlignment
    //     0x1485bb4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1485bb8: ldr             x1, [x1, #0xa18]
    // 0x1485bbc: StoreField: r0->field_1b = r1
    //     0x1485bbc: stur            w1, [x0, #0x1b]
    // 0x1485bc0: r1 = Instance_VerticalDirection
    //     0x1485bc0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1485bc4: ldr             x1, [x1, #0xa20]
    // 0x1485bc8: StoreField: r0->field_23 = r1
    //     0x1485bc8: stur            w1, [x0, #0x23]
    // 0x1485bcc: r1 = Instance_Clip
    //     0x1485bcc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1485bd0: ldr             x1, [x1, #0x38]
    // 0x1485bd4: StoreField: r0->field_2b = r1
    //     0x1485bd4: stur            w1, [x0, #0x2b]
    // 0x1485bd8: StoreField: r0->field_2f = rZR
    //     0x1485bd8: stur            xzr, [x0, #0x2f]
    // 0x1485bdc: ldur            x1, [fp, #-0x10]
    // 0x1485be0: StoreField: r0->field_b = r1
    //     0x1485be0: stur            w1, [x0, #0xb]
    // 0x1485be4: LeaveFrame
    //     0x1485be4: mov             SP, fp
    //     0x1485be8: ldp             fp, lr, [SP], #0x10
    // 0x1485bec: ret
    //     0x1485bec: ret             
    // 0x1485bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485bf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485bf4: b               #0x148580c
  }
  [closure] void <anonymous closure>(dynamic, String, bool) {
    // ** addr: 0x1485c04, size: 0x14c
    // 0x1485c04: EnterFrame
    //     0x1485c04: stp             fp, lr, [SP, #-0x10]!
    //     0x1485c08: mov             fp, SP
    // 0x1485c0c: AllocStack(0x18)
    //     0x1485c0c: sub             SP, SP, #0x18
    // 0x1485c10: SetupParameters()
    //     0x1485c10: ldr             x0, [fp, #0x20]
    //     0x1485c14: ldur            w1, [x0, #0x17]
    //     0x1485c18: add             x1, x1, HEAP, lsl #32
    //     0x1485c1c: stur            x1, [fp, #-8]
    // 0x1485c20: CheckStackOverflow
    //     0x1485c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485c24: cmp             SP, x16
    //     0x1485c28: b.ls            #0x1485d44
    // 0x1485c2c: r1 = 2
    //     0x1485c2c: movz            x1, #0x2
    // 0x1485c30: r0 = AllocateContext()
    //     0x1485c30: bl              #0x16f6108  ; AllocateContextStub
    // 0x1485c34: mov             x1, x0
    // 0x1485c38: ldur            x0, [fp, #-8]
    // 0x1485c3c: StoreField: r1->field_b = r0
    //     0x1485c3c: stur            w0, [x1, #0xb]
    // 0x1485c40: ldr             x0, [fp, #0x18]
    // 0x1485c44: StoreField: r1->field_f = r0
    //     0x1485c44: stur            w0, [x1, #0xf]
    // 0x1485c48: ldr             x0, [fp, #0x10]
    // 0x1485c4c: StoreField: r1->field_13 = r0
    //     0x1485c4c: stur            w0, [x1, #0x13]
    // 0x1485c50: r0 = LoadStaticField(0x878)
    //     0x1485c50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1485c54: ldr             x0, [x0, #0x10f0]
    // 0x1485c58: cmp             w0, NULL
    // 0x1485c5c: b.eq            #0x1485d4c
    // 0x1485c60: LoadField: r3 = r0->field_53
    //     0x1485c60: ldur            w3, [x0, #0x53]
    // 0x1485c64: DecompressPointer r3
    //     0x1485c64: add             x3, x3, HEAP, lsl #32
    // 0x1485c68: stur            x3, [fp, #-0x10]
    // 0x1485c6c: LoadField: r0 = r3->field_7
    //     0x1485c6c: ldur            w0, [x3, #7]
    // 0x1485c70: DecompressPointer r0
    //     0x1485c70: add             x0, x0, HEAP, lsl #32
    // 0x1485c74: mov             x2, x1
    // 0x1485c78: stur            x0, [fp, #-8]
    // 0x1485c7c: r1 = Function '<anonymous closure>':.
    //     0x1485c7c: add             x1, PP, #0x44, lsl #12  ; [pp+0x441d8] AnonymousClosure: (0x1393324), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14d7a24)
    //     0x1485c80: ldr             x1, [x1, #0x1d8]
    // 0x1485c84: r0 = AllocateClosure()
    //     0x1485c84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485c88: ldur            x2, [fp, #-8]
    // 0x1485c8c: mov             x3, x0
    // 0x1485c90: r1 = Null
    //     0x1485c90: mov             x1, NULL
    // 0x1485c94: stur            x3, [fp, #-8]
    // 0x1485c98: cmp             w2, NULL
    // 0x1485c9c: b.eq            #0x1485cbc
    // 0x1485ca0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x1485ca0: ldur            w4, [x2, #0x17]
    // 0x1485ca4: DecompressPointer r4
    //     0x1485ca4: add             x4, x4, HEAP, lsl #32
    // 0x1485ca8: r8 = X0
    //     0x1485ca8: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x1485cac: LoadField: r9 = r4->field_7
    //     0x1485cac: ldur            x9, [x4, #7]
    // 0x1485cb0: r3 = Null
    //     0x1485cb0: add             x3, PP, #0x44, lsl #12  ; [pp+0x441e0] Null
    //     0x1485cb4: ldr             x3, [x3, #0x1e0]
    // 0x1485cb8: blr             x9
    // 0x1485cbc: ldur            x0, [fp, #-0x10]
    // 0x1485cc0: LoadField: r1 = r0->field_b
    //     0x1485cc0: ldur            w1, [x0, #0xb]
    // 0x1485cc4: LoadField: r2 = r0->field_f
    //     0x1485cc4: ldur            w2, [x0, #0xf]
    // 0x1485cc8: DecompressPointer r2
    //     0x1485cc8: add             x2, x2, HEAP, lsl #32
    // 0x1485ccc: LoadField: r3 = r2->field_b
    //     0x1485ccc: ldur            w3, [x2, #0xb]
    // 0x1485cd0: r2 = LoadInt32Instr(r1)
    //     0x1485cd0: sbfx            x2, x1, #1, #0x1f
    // 0x1485cd4: stur            x2, [fp, #-0x18]
    // 0x1485cd8: r1 = LoadInt32Instr(r3)
    //     0x1485cd8: sbfx            x1, x3, #1, #0x1f
    // 0x1485cdc: cmp             x2, x1
    // 0x1485ce0: b.ne            #0x1485cec
    // 0x1485ce4: mov             x1, x0
    // 0x1485ce8: r0 = _growToNextCapacity()
    //     0x1485ce8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1485cec: ldur            x2, [fp, #-0x10]
    // 0x1485cf0: ldur            x3, [fp, #-0x18]
    // 0x1485cf4: add             x4, x3, #1
    // 0x1485cf8: lsl             x5, x4, #1
    // 0x1485cfc: StoreField: r2->field_b = r5
    //     0x1485cfc: stur            w5, [x2, #0xb]
    // 0x1485d00: LoadField: r1 = r2->field_f
    //     0x1485d00: ldur            w1, [x2, #0xf]
    // 0x1485d04: DecompressPointer r1
    //     0x1485d04: add             x1, x1, HEAP, lsl #32
    // 0x1485d08: ldur            x0, [fp, #-8]
    // 0x1485d0c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1485d0c: add             x25, x1, x3, lsl #2
    //     0x1485d10: add             x25, x25, #0xf
    //     0x1485d14: str             w0, [x25]
    //     0x1485d18: tbz             w0, #0, #0x1485d34
    //     0x1485d1c: ldurb           w16, [x1, #-1]
    //     0x1485d20: ldurb           w17, [x0, #-1]
    //     0x1485d24: and             x16, x17, x16, lsr #2
    //     0x1485d28: tst             x16, HEAP, lsr #32
    //     0x1485d2c: b.eq            #0x1485d34
    //     0x1485d30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1485d34: r0 = Null
    //     0x1485d34: mov             x0, NULL
    // 0x1485d38: LeaveFrame
    //     0x1485d38: mov             SP, fp
    //     0x1485d3c: ldp             fp, lr, [SP], #0x10
    // 0x1485d40: ret
    //     0x1485d40: ret             
    // 0x1485d44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485d44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485d48: b               #0x1485c2c
    // 0x1485d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x1485d4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x1485d50, size: 0x68
    // 0x1485d50: EnterFrame
    //     0x1485d50: stp             fp, lr, [SP, #-0x10]!
    //     0x1485d54: mov             fp, SP
    // 0x1485d58: AllocStack(0x8)
    //     0x1485d58: sub             SP, SP, #8
    // 0x1485d5c: SetupParameters()
    //     0x1485d5c: ldr             x0, [fp, #0x10]
    //     0x1485d60: ldur            w1, [x0, #0x17]
    //     0x1485d64: add             x1, x1, HEAP, lsl #32
    // 0x1485d68: CheckStackOverflow
    //     0x1485d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485d6c: cmp             SP, x16
    //     0x1485d70: b.ls            #0x1485db0
    // 0x1485d74: LoadField: r0 = r1->field_f
    //     0x1485d74: ldur            w0, [x1, #0xf]
    // 0x1485d78: DecompressPointer r0
    //     0x1485d78: add             x0, x0, HEAP, lsl #32
    // 0x1485d7c: mov             x1, x0
    // 0x1485d80: r0 = controller()
    //     0x1485d80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485d84: LoadField: r1 = r0->field_83
    //     0x1485d84: ldur            w1, [x0, #0x83]
    // 0x1485d88: DecompressPointer r1
    //     0x1485d88: add             x1, x1, HEAP, lsl #32
    // 0x1485d8c: r0 = value()
    //     0x1485d8c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485d90: stur            x0, [fp, #-8]
    // 0x1485d94: r0 = CheckoutBreadCrumb()
    //     0x1485d94: bl              #0x1484fc8  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x1485d98: ldur            x1, [fp, #-8]
    // 0x1485d9c: StoreField: r0->field_b = r1
    //     0x1485d9c: stur            w1, [x0, #0xb]
    // 0x1485da0: StoreField: r0->field_f = rZR
    //     0x1485da0: stur            xzr, [x0, #0xf]
    // 0x1485da4: LeaveFrame
    //     0x1485da4: mov             SP, fp
    //     0x1485da8: ldp             fp, lr, [SP], #0x10
    // 0x1485dac: ret
    //     0x1485dac: ret             
    // 0x1485db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485db0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485db4: b               #0x1485d74
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x1485db8, size: 0x144
    // 0x1485db8: EnterFrame
    //     0x1485db8: stp             fp, lr, [SP, #-0x10]!
    //     0x1485dbc: mov             fp, SP
    // 0x1485dc0: AllocStack(0x20)
    //     0x1485dc0: sub             SP, SP, #0x20
    // 0x1485dc4: SetupParameters()
    //     0x1485dc4: ldr             x0, [fp, #0x10]
    //     0x1485dc8: ldur            w2, [x0, #0x17]
    //     0x1485dcc: add             x2, x2, HEAP, lsl #32
    //     0x1485dd0: stur            x2, [fp, #-8]
    // 0x1485dd4: CheckStackOverflow
    //     0x1485dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485dd8: cmp             SP, x16
    //     0x1485ddc: b.ls            #0x1485ef0
    // 0x1485de0: LoadField: r1 = r2->field_f
    //     0x1485de0: ldur            w1, [x2, #0xf]
    // 0x1485de4: DecompressPointer r1
    //     0x1485de4: add             x1, x1, HEAP, lsl #32
    // 0x1485de8: r0 = controller()
    //     0x1485de8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485dec: LoadField: r1 = r0->field_ab
    //     0x1485dec: ldur            w1, [x0, #0xab]
    // 0x1485df0: DecompressPointer r1
    //     0x1485df0: add             x1, x1, HEAP, lsl #32
    // 0x1485df4: r0 = value()
    //     0x1485df4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485df8: tbnz            w0, #4, #0x1485ee0
    // 0x1485dfc: r0 = LoadStaticField(0x878)
    //     0x1485dfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1485e00: ldr             x0, [x0, #0x10f0]
    // 0x1485e04: cmp             w0, NULL
    // 0x1485e08: b.eq            #0x1485ef8
    // 0x1485e0c: LoadField: r3 = r0->field_53
    //     0x1485e0c: ldur            w3, [x0, #0x53]
    // 0x1485e10: DecompressPointer r3
    //     0x1485e10: add             x3, x3, HEAP, lsl #32
    // 0x1485e14: stur            x3, [fp, #-0x18]
    // 0x1485e18: LoadField: r0 = r3->field_7
    //     0x1485e18: ldur            w0, [x3, #7]
    // 0x1485e1c: DecompressPointer r0
    //     0x1485e1c: add             x0, x0, HEAP, lsl #32
    // 0x1485e20: ldur            x2, [fp, #-8]
    // 0x1485e24: stur            x0, [fp, #-0x10]
    // 0x1485e28: r1 = Function '<anonymous closure>':.
    //     0x1485e28: add             x1, PP, #0x44, lsl #12  ; [pp+0x441f0] AnonymousClosure: (0x1485efc), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::body (0x14855e4)
    //     0x1485e2c: ldr             x1, [x1, #0x1f0]
    // 0x1485e30: r0 = AllocateClosure()
    //     0x1485e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485e34: ldur            x2, [fp, #-0x10]
    // 0x1485e38: mov             x3, x0
    // 0x1485e3c: r1 = Null
    //     0x1485e3c: mov             x1, NULL
    // 0x1485e40: stur            x3, [fp, #-8]
    // 0x1485e44: cmp             w2, NULL
    // 0x1485e48: b.eq            #0x1485e68
    // 0x1485e4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x1485e4c: ldur            w4, [x2, #0x17]
    // 0x1485e50: DecompressPointer r4
    //     0x1485e50: add             x4, x4, HEAP, lsl #32
    // 0x1485e54: r8 = X0
    //     0x1485e54: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x1485e58: LoadField: r9 = r4->field_7
    //     0x1485e58: ldur            x9, [x4, #7]
    // 0x1485e5c: r3 = Null
    //     0x1485e5c: add             x3, PP, #0x44, lsl #12  ; [pp+0x441f8] Null
    //     0x1485e60: ldr             x3, [x3, #0x1f8]
    // 0x1485e64: blr             x9
    // 0x1485e68: ldur            x0, [fp, #-0x18]
    // 0x1485e6c: LoadField: r1 = r0->field_b
    //     0x1485e6c: ldur            w1, [x0, #0xb]
    // 0x1485e70: LoadField: r2 = r0->field_f
    //     0x1485e70: ldur            w2, [x0, #0xf]
    // 0x1485e74: DecompressPointer r2
    //     0x1485e74: add             x2, x2, HEAP, lsl #32
    // 0x1485e78: LoadField: r3 = r2->field_b
    //     0x1485e78: ldur            w3, [x2, #0xb]
    // 0x1485e7c: r2 = LoadInt32Instr(r1)
    //     0x1485e7c: sbfx            x2, x1, #1, #0x1f
    // 0x1485e80: stur            x2, [fp, #-0x20]
    // 0x1485e84: r1 = LoadInt32Instr(r3)
    //     0x1485e84: sbfx            x1, x3, #1, #0x1f
    // 0x1485e88: cmp             x2, x1
    // 0x1485e8c: b.ne            #0x1485e98
    // 0x1485e90: mov             x1, x0
    // 0x1485e94: r0 = _growToNextCapacity()
    //     0x1485e94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1485e98: ldur            x2, [fp, #-0x18]
    // 0x1485e9c: ldur            x3, [fp, #-0x20]
    // 0x1485ea0: add             x4, x3, #1
    // 0x1485ea4: lsl             x5, x4, #1
    // 0x1485ea8: StoreField: r2->field_b = r5
    //     0x1485ea8: stur            w5, [x2, #0xb]
    // 0x1485eac: LoadField: r1 = r2->field_f
    //     0x1485eac: ldur            w1, [x2, #0xf]
    // 0x1485eb0: DecompressPointer r1
    //     0x1485eb0: add             x1, x1, HEAP, lsl #32
    // 0x1485eb4: ldur            x0, [fp, #-8]
    // 0x1485eb8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x1485eb8: add             x25, x1, x3, lsl #2
    //     0x1485ebc: add             x25, x25, #0xf
    //     0x1485ec0: str             w0, [x25]
    //     0x1485ec4: tbz             w0, #0, #0x1485ee0
    //     0x1485ec8: ldurb           w16, [x1, #-1]
    //     0x1485ecc: ldurb           w17, [x0, #-1]
    //     0x1485ed0: and             x16, x17, x16, lsr #2
    //     0x1485ed4: tst             x16, HEAP, lsr #32
    //     0x1485ed8: b.eq            #0x1485ee0
    //     0x1485edc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1485ee0: r0 = Instance_SizedBox
    //     0x1485ee0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1485ee4: LeaveFrame
    //     0x1485ee4: mov             SP, fp
    //     0x1485ee8: ldp             fp, lr, [SP], #0x10
    // 0x1485eec: ret
    //     0x1485eec: ret             
    // 0x1485ef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485ef0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485ef4: b               #0x1485de0
    // 0x1485ef8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x1485ef8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x1485efc, size: 0x74
    // 0x1485efc: EnterFrame
    //     0x1485efc: stp             fp, lr, [SP, #-0x10]!
    //     0x1485f00: mov             fp, SP
    // 0x1485f04: AllocStack(0x8)
    //     0x1485f04: sub             SP, SP, #8
    // 0x1485f08: SetupParameters()
    //     0x1485f08: ldr             x0, [fp, #0x18]
    //     0x1485f0c: ldur            w2, [x0, #0x17]
    //     0x1485f10: add             x2, x2, HEAP, lsl #32
    //     0x1485f14: stur            x2, [fp, #-8]
    // 0x1485f18: CheckStackOverflow
    //     0x1485f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485f1c: cmp             SP, x16
    //     0x1485f20: b.ls            #0x1485f68
    // 0x1485f24: LoadField: r1 = r2->field_f
    //     0x1485f24: ldur            w1, [x2, #0xf]
    // 0x1485f28: DecompressPointer r1
    //     0x1485f28: add             x1, x1, HEAP, lsl #32
    // 0x1485f2c: r0 = controller()
    //     0x1485f2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485f30: LoadField: r1 = r0->field_ab
    //     0x1485f30: ldur            w1, [x0, #0xab]
    // 0x1485f34: DecompressPointer r1
    //     0x1485f34: add             x1, x1, HEAP, lsl #32
    // 0x1485f38: r2 = false
    //     0x1485f38: add             x2, NULL, #0x30  ; false
    // 0x1485f3c: r0 = value=()
    //     0x1485f3c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1485f40: ldur            x0, [fp, #-8]
    // 0x1485f44: LoadField: r1 = r0->field_f
    //     0x1485f44: ldur            w1, [x0, #0xf]
    // 0x1485f48: DecompressPointer r1
    //     0x1485f48: add             x1, x1, HEAP, lsl #32
    // 0x1485f4c: LoadField: r2 = r0->field_13
    //     0x1485f4c: ldur            w2, [x0, #0x13]
    // 0x1485f50: DecompressPointer r2
    //     0x1485f50: add             x2, x2, HEAP, lsl #32
    // 0x1485f54: r0 = _showOtpBottomSheet()
    //     0x1485f54: bl              #0x1485f70  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet
    // 0x1485f58: r0 = Null
    //     0x1485f58: mov             x0, NULL
    // 0x1485f5c: LeaveFrame
    //     0x1485f5c: mov             SP, fp
    //     0x1485f60: ldp             fp, lr, [SP], #0x10
    // 0x1485f64: ret
    //     0x1485f64: ret             
    // 0x1485f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485f68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485f6c: b               #0x1485f24
  }
  _ _showOtpBottomSheet(/* No info */) {
    // ** addr: 0x1485f70, size: 0xbc
    // 0x1485f70: EnterFrame
    //     0x1485f70: stp             fp, lr, [SP, #-0x10]!
    //     0x1485f74: mov             fp, SP
    // 0x1485f78: AllocStack(0x48)
    //     0x1485f78: sub             SP, SP, #0x48
    // 0x1485f7c: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1485f7c: stur            x1, [fp, #-8]
    //     0x1485f80: stur            x2, [fp, #-0x10]
    // 0x1485f84: CheckStackOverflow
    //     0x1485f84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485f88: cmp             SP, x16
    //     0x1485f8c: b.ls            #0x1486024
    // 0x1485f90: r1 = 1
    //     0x1485f90: movz            x1, #0x1
    // 0x1485f94: r0 = AllocateContext()
    //     0x1485f94: bl              #0x16f6108  ; AllocateContextStub
    // 0x1485f98: mov             x3, x0
    // 0x1485f9c: ldur            x0, [fp, #-8]
    // 0x1485fa0: stur            x3, [fp, #-0x18]
    // 0x1485fa4: StoreField: r3->field_f = r0
    //     0x1485fa4: stur            w0, [x3, #0xf]
    // 0x1485fa8: mov             x2, x3
    // 0x1485fac: r1 = Function '<anonymous closure>':.
    //     0x1485fac: add             x1, PP, #0x44, lsl #12  ; [pp+0x44208] AnonymousClosure: (0x148602c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1485f70)
    //     0x1485fb0: ldr             x1, [x1, #0x208]
    // 0x1485fb4: r0 = AllocateClosure()
    //     0x1485fb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485fb8: stp             x0, NULL, [SP, #0x20]
    // 0x1485fbc: ldur            x16, [fp, #-0x10]
    // 0x1485fc0: r30 = true
    //     0x1485fc0: add             lr, NULL, #0x20  ; true
    // 0x1485fc4: stp             lr, x16, [SP, #0x10]
    // 0x1485fc8: r16 = Instance_Color
    //     0x1485fc8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x1485fcc: ldr             x16, [x16, #0x90]
    // 0x1485fd0: r30 = Instance_RoundedRectangleBorder
    //     0x1485fd0: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1485fd4: ldr             lr, [lr, #0xc78]
    // 0x1485fd8: stp             lr, x16, [SP]
    // 0x1485fdc: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, shape, 0x4, null]
    //     0x1485fdc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d308] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, "shape", 0x4, Null]
    //     0x1485fe0: ldr             x4, [x4, #0x308]
    // 0x1485fe4: r0 = showModalBottomSheet()
    //     0x1485fe4: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1485fe8: ldur            x2, [fp, #-0x18]
    // 0x1485fec: r1 = Function '<anonymous closure>':.
    //     0x1485fec: add             x1, PP, #0x44, lsl #12  ; [pp+0x44210] AnonymousClosure: (0x139210c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1485ff0: ldr             x1, [x1, #0x210]
    // 0x1485ff4: stur            x0, [fp, #-8]
    // 0x1485ff8: r0 = AllocateClosure()
    //     0x1485ff8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485ffc: r16 = <Null?>
    //     0x1485ffc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x1486000: ldur            lr, [fp, #-8]
    // 0x1486004: stp             lr, x16, [SP, #8]
    // 0x1486008: str             x0, [SP]
    // 0x148600c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x148600c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x1486010: r0 = then()
    //     0x1486010: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x1486014: r0 = Null
    //     0x1486014: mov             x0, NULL
    // 0x1486018: LeaveFrame
    //     0x1486018: mov             SP, fp
    //     0x148601c: ldp             fp, lr, [SP], #0x10
    // 0x1486020: ret
    //     0x1486020: ret             
    // 0x1486024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486024: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486028: b               #0x1485f90
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x148602c, size: 0x130
    // 0x148602c: EnterFrame
    //     0x148602c: stp             fp, lr, [SP, #-0x10]!
    //     0x1486030: mov             fp, SP
    // 0x1486034: AllocStack(0x30)
    //     0x1486034: sub             SP, SP, #0x30
    // 0x1486038: SetupParameters()
    //     0x1486038: ldr             x0, [fp, #0x18]
    //     0x148603c: ldur            w1, [x0, #0x17]
    //     0x1486040: add             x1, x1, HEAP, lsl #32
    //     0x1486044: stur            x1, [fp, #-8]
    // 0x1486048: CheckStackOverflow
    //     0x1486048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148604c: cmp             SP, x16
    //     0x1486050: b.ls            #0x1486154
    // 0x1486054: r1 = 1
    //     0x1486054: movz            x1, #0x1
    // 0x1486058: r0 = AllocateContext()
    //     0x1486058: bl              #0x16f6108  ; AllocateContextStub
    // 0x148605c: mov             x2, x0
    // 0x1486060: ldur            x0, [fp, #-8]
    // 0x1486064: stur            x2, [fp, #-0x10]
    // 0x1486068: StoreField: r2->field_b = r0
    //     0x1486068: stur            w0, [x2, #0xb]
    // 0x148606c: ldr             x1, [fp, #0x10]
    // 0x1486070: StoreField: r2->field_f = r1
    //     0x1486070: stur            w1, [x2, #0xf]
    // 0x1486074: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1486074: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1486078: r0 = _of()
    //     0x1486078: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x148607c: LoadField: r1 = r0->field_23
    //     0x148607c: ldur            w1, [x0, #0x23]
    // 0x1486080: DecompressPointer r1
    //     0x1486080: add             x1, x1, HEAP, lsl #32
    // 0x1486084: LoadField: d0 = r1->field_1f
    //     0x1486084: ldur            d0, [x1, #0x1f]
    // 0x1486088: stur            d0, [fp, #-0x30]
    // 0x148608c: r0 = EdgeInsets()
    //     0x148608c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1486090: stur            x0, [fp, #-0x18]
    // 0x1486094: StoreField: r0->field_7 = rZR
    //     0x1486094: stur            xzr, [x0, #7]
    // 0x1486098: StoreField: r0->field_f = rZR
    //     0x1486098: stur            xzr, [x0, #0xf]
    // 0x148609c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x148609c: stur            xzr, [x0, #0x17]
    // 0x14860a0: ldur            d0, [fp, #-0x30]
    // 0x14860a4: StoreField: r0->field_1f = d0
    //     0x14860a4: stur            d0, [x0, #0x1f]
    // 0x14860a8: ldur            x1, [fp, #-8]
    // 0x14860ac: LoadField: r2 = r1->field_f
    //     0x14860ac: ldur            w2, [x1, #0xf]
    // 0x14860b0: DecompressPointer r2
    //     0x14860b0: add             x2, x2, HEAP, lsl #32
    // 0x14860b4: mov             x1, x2
    // 0x14860b8: r0 = controller()
    //     0x14860b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14860bc: LoadField: r1 = r0->field_7b
    //     0x14860bc: ldur            w1, [x0, #0x7b]
    // 0x14860c0: DecompressPointer r1
    //     0x14860c0: add             x1, x1, HEAP, lsl #32
    // 0x14860c4: r0 = value()
    //     0x14860c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14860c8: ldur            x2, [fp, #-0x10]
    // 0x14860cc: r1 = Function '<anonymous closure>':.
    //     0x14860cc: add             x1, PP, #0x44, lsl #12  ; [pp+0x44218] AnonymousClosure: (0x1486168), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1485f70)
    //     0x14860d0: ldr             x1, [x1, #0x218]
    // 0x14860d4: stur            x0, [fp, #-8]
    // 0x14860d8: r0 = AllocateClosure()
    //     0x14860d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14860dc: stur            x0, [fp, #-0x20]
    // 0x14860e0: r0 = OtpBottomSheet()
    //     0x14860e0: bl              #0x148615c  ; AllocateOtpBottomSheetStub -> OtpBottomSheet (size=0x1c)
    // 0x14860e4: mov             x3, x0
    // 0x14860e8: ldur            x0, [fp, #-0x20]
    // 0x14860ec: stur            x3, [fp, #-0x28]
    // 0x14860f0: StoreField: r3->field_b = r0
    //     0x14860f0: stur            w0, [x3, #0xb]
    // 0x14860f4: ldur            x2, [fp, #-0x10]
    // 0x14860f8: r1 = Function '<anonymous closure>':.
    //     0x14860f8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44220] AnonymousClosure: (0x1392328), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x14860fc: ldr             x1, [x1, #0x220]
    // 0x1486100: r0 = AllocateClosure()
    //     0x1486100: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1486104: mov             x1, x0
    // 0x1486108: ldur            x0, [fp, #-0x28]
    // 0x148610c: StoreField: r0->field_f = r1
    //     0x148610c: stur            w1, [x0, #0xf]
    // 0x1486110: ldur            x1, [fp, #-8]
    // 0x1486114: StoreField: r0->field_13 = r1
    //     0x1486114: stur            w1, [x0, #0x13]
    // 0x1486118: ldur            x2, [fp, #-0x10]
    // 0x148611c: r1 = Function '<anonymous closure>':.
    //     0x148611c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44228] AnonymousClosure: (0x13922a0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::_showOtpBottomSheet (0x1392058)
    //     0x1486120: ldr             x1, [x1, #0x228]
    // 0x1486124: r0 = AllocateClosure()
    //     0x1486124: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1486128: mov             x1, x0
    // 0x148612c: ldur            x0, [fp, #-0x28]
    // 0x1486130: ArrayStore: r0[0] = r1  ; List_4
    //     0x1486130: stur            w1, [x0, #0x17]
    // 0x1486134: r0 = Padding()
    //     0x1486134: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1486138: ldur            x1, [fp, #-0x18]
    // 0x148613c: StoreField: r0->field_f = r1
    //     0x148613c: stur            w1, [x0, #0xf]
    // 0x1486140: ldur            x1, [fp, #-0x28]
    // 0x1486144: StoreField: r0->field_b = r1
    //     0x1486144: stur            w1, [x0, #0xb]
    // 0x1486148: LeaveFrame
    //     0x1486148: mov             SP, fp
    //     0x148614c: ldp             fp, lr, [SP], #0x10
    // 0x1486150: ret
    //     0x1486150: ret             
    // 0x1486154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486154: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486158: b               #0x1486054
  }
  [closure] Future<Null> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x1486168, size: 0x1a0
    // 0x1486168: EnterFrame
    //     0x1486168: stp             fp, lr, [SP, #-0x10]!
    //     0x148616c: mov             fp, SP
    // 0x1486170: AllocStack(0x58)
    //     0x1486170: sub             SP, SP, #0x58
    // 0x1486174: SetupParameters(CheckoutRequestNumberPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1486174: stur            NULL, [fp, #-8]
    //     0x1486178: movz            x0, #0
    //     0x148617c: add             x1, fp, w0, sxtw #2
    //     0x1486180: ldr             x1, [x1, #0x18]
    //     0x1486184: add             x2, fp, w0, sxtw #2
    //     0x1486188: ldr             x2, [x2, #0x10]
    //     0x148618c: stur            x2, [fp, #-0x18]
    //     0x1486190: ldur            w3, [x1, #0x17]
    //     0x1486194: add             x3, x3, HEAP, lsl #32
    //     0x1486198: stur            x3, [fp, #-0x10]
    // 0x148619c: CheckStackOverflow
    //     0x148619c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14861a0: cmp             SP, x16
    //     0x14861a4: b.ls            #0x1486300
    // 0x14861a8: InitAsync() -> Future<Null?>
    //     0x14861a8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x14861ac: bl              #0x6326e0  ; InitAsyncStub
    // 0x14861b0: ldur            x0, [fp, #-0x10]
    // 0x14861b4: LoadField: r2 = r0->field_b
    //     0x14861b4: ldur            w2, [x0, #0xb]
    // 0x14861b8: DecompressPointer r2
    //     0x14861b8: add             x2, x2, HEAP, lsl #32
    // 0x14861bc: stur            x2, [fp, #-0x20]
    // 0x14861c0: LoadField: r1 = r2->field_f
    //     0x14861c0: ldur            w1, [x2, #0xf]
    // 0x14861c4: DecompressPointer r1
    //     0x14861c4: add             x1, x1, HEAP, lsl #32
    // 0x14861c8: r0 = controller()
    //     0x14861c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14861cc: mov             x2, x0
    // 0x14861d0: ldur            x0, [fp, #-0x20]
    // 0x14861d4: stur            x2, [fp, #-0x28]
    // 0x14861d8: LoadField: r1 = r0->field_f
    //     0x14861d8: ldur            w1, [x0, #0xf]
    // 0x14861dc: DecompressPointer r1
    //     0x14861dc: add             x1, x1, HEAP, lsl #32
    // 0x14861e0: r0 = controller()
    //     0x14861e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14861e4: LoadField: r1 = r0->field_7b
    //     0x14861e4: ldur            w1, [x0, #0x7b]
    // 0x14861e8: DecompressPointer r1
    //     0x14861e8: add             x1, x1, HEAP, lsl #32
    // 0x14861ec: r0 = value()
    //     0x14861ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14861f0: mov             x3, x0
    // 0x14861f4: ldur            x0, [fp, #-0x18]
    // 0x14861f8: r2 = Null
    //     0x14861f8: mov             x2, NULL
    // 0x14861fc: r1 = Null
    //     0x14861fc: mov             x1, NULL
    // 0x1486200: stur            x3, [fp, #-0x30]
    // 0x1486204: r4 = 60
    //     0x1486204: movz            x4, #0x3c
    // 0x1486208: branchIfSmi(r0, 0x1486214)
    //     0x1486208: tbz             w0, #0, #0x1486214
    // 0x148620c: r4 = LoadClassIdInstr(r0)
    //     0x148620c: ldur            x4, [x0, #-1]
    //     0x1486210: ubfx            x4, x4, #0xc, #0x14
    // 0x1486214: sub             x4, x4, #0x5e
    // 0x1486218: cmp             x4, #1
    // 0x148621c: b.ls            #0x1486230
    // 0x1486220: r8 = String
    //     0x1486220: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x1486224: r3 = Null
    //     0x1486224: add             x3, PP, #0x44, lsl #12  ; [pp+0x44230] Null
    //     0x1486228: ldr             x3, [x3, #0x230]
    // 0x148622c: r0 = String()
    //     0x148622c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x1486230: ldur            x1, [fp, #-0x28]
    // 0x1486234: ldur            x2, [fp, #-0x30]
    // 0x1486238: ldur            x3, [fp, #-0x18]
    // 0x148623c: r0 = verifyOtp()
    //     0x148623c: bl              #0x13927f4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::verifyOtp
    // 0x1486240: mov             x1, x0
    // 0x1486244: stur            x1, [fp, #-0x18]
    // 0x1486248: r0 = Await()
    //     0x1486248: bl              #0x63248c  ; AwaitStub
    // 0x148624c: r16 = true
    //     0x148624c: add             x16, NULL, #0x20  ; true
    // 0x1486250: cmp             w0, w16
    // 0x1486254: b.ne            #0x148629c
    // 0x1486258: ldur            x0, [fp, #-0x20]
    // 0x148625c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x148625c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1486260: ldr             x0, [x0, #0x1c80]
    //     0x1486264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1486268: cmp             w0, w16
    //     0x148626c: b.ne            #0x1486278
    //     0x1486270: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1486274: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1486278: str             NULL, [SP]
    // 0x148627c: r4 = const [0x1, 0, 0, 0, null]
    //     0x148627c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x1486280: r0 = GetNavigation.back()
    //     0x1486280: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1486284: ldur            x0, [fp, #-0x20]
    // 0x1486288: LoadField: r1 = r0->field_f
    //     0x1486288: ldur            w1, [x0, #0xf]
    // 0x148628c: DecompressPointer r1
    //     0x148628c: add             x1, x1, HEAP, lsl #32
    // 0x1486290: r0 = controller()
    //     0x1486290: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1486294: mov             x1, x0
    // 0x1486298: r0 = navigateToOrderSummaryPage()
    //     0x1486298: bl              #0x12e8d30  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::navigateToOrderSummaryPage
    // 0x148629c: ldur            x0, [fp, #-0x20]
    // 0x14862a0: LoadField: r1 = r0->field_f
    //     0x14862a0: ldur            w1, [x0, #0xf]
    // 0x14862a4: DecompressPointer r1
    //     0x14862a4: add             x1, x1, HEAP, lsl #32
    // 0x14862a8: r0 = controller()
    //     0x14862a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14862ac: r16 = "landing_page"
    //     0x14862ac: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x14862b0: ldr             x16, [x16, #0x638]
    // 0x14862b4: r30 = "request_number"
    //     0x14862b4: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d900] "request_number"
    //     0x14862b8: ldr             lr, [lr, #0x900]
    // 0x14862bc: stp             lr, x16, [SP, #0x18]
    // 0x14862c0: r16 = "confirm_cta"
    //     0x14862c0: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3db80] "confirm_cta"
    //     0x14862c4: ldr             x16, [x16, #0xb80]
    // 0x14862c8: r30 = "Confirm"
    //     0x14862c8: add             lr, PP, #0x3c, lsl #12  ; [pp+0x3cec0] "Confirm"
    //     0x14862cc: ldr             lr, [lr, #0xec0]
    // 0x14862d0: stp             lr, x16, [SP, #8]
    // 0x14862d4: r16 = "otp_popup"
    //     0x14862d4: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c818] "otp_popup"
    //     0x14862d8: ldr             x16, [x16, #0x818]
    // 0x14862dc: str             x16, [SP]
    // 0x14862e0: mov             x1, x0
    // 0x14862e4: r2 = "checkout_cta_clicked"
    //     0x14862e4: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x14862e8: ldr             x2, [x2, #0x7b0]
    // 0x14862ec: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, pageId, 0x3, pageType, 0x2, widgetType, 0x6, null]
    //     0x14862ec: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d910] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "pageId", 0x3, "pageType", 0x2, "widgetType", 0x6, Null]
    //     0x14862f0: ldr             x4, [x4, #0x910]
    // 0x14862f4: r0 = checkoutPostEvent()
    //     0x14862f4: bl              #0x12e77a4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::checkoutPostEvent
    // 0x14862f8: r0 = Null
    //     0x14862f8: mov             x0, NULL
    // 0x14862fc: r0 = ReturnAsyncNotFuture()
    //     0x14862fc: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1486300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486300: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486304: b               #0x14861a8
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d95a0, size: 0x250
    // 0x15d95a0: EnterFrame
    //     0x15d95a0: stp             fp, lr, [SP, #-0x10]!
    //     0x15d95a4: mov             fp, SP
    // 0x15d95a8: AllocStack(0x28)
    //     0x15d95a8: sub             SP, SP, #0x28
    // 0x15d95ac: SetupParameters(CheckoutRequestNumberPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15d95ac: stur            x1, [fp, #-8]
    //     0x15d95b0: stur            x2, [fp, #-0x10]
    // 0x15d95b4: CheckStackOverflow
    //     0x15d95b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d95b8: cmp             SP, x16
    //     0x15d95bc: b.ls            #0x15d97e8
    // 0x15d95c0: r1 = 2
    //     0x15d95c0: movz            x1, #0x2
    // 0x15d95c4: r0 = AllocateContext()
    //     0x15d95c4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d95c8: ldur            x1, [fp, #-8]
    // 0x15d95cc: stur            x0, [fp, #-0x18]
    // 0x15d95d0: StoreField: r0->field_f = r1
    //     0x15d95d0: stur            w1, [x0, #0xf]
    // 0x15d95d4: ldur            x2, [fp, #-0x10]
    // 0x15d95d8: StoreField: r0->field_13 = r2
    //     0x15d95d8: stur            w2, [x0, #0x13]
    // 0x15d95dc: r0 = Obx()
    //     0x15d95dc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d95e0: ldur            x2, [fp, #-0x18]
    // 0x15d95e4: r1 = Function '<anonymous closure>':.
    //     0x15d95e4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44240] AnonymousClosure: (0x15ccba8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15d95e8: ldr             x1, [x1, #0x240]
    // 0x15d95ec: stur            x0, [fp, #-0x10]
    // 0x15d95f0: r0 = AllocateClosure()
    //     0x15d95f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d95f4: mov             x1, x0
    // 0x15d95f8: ldur            x0, [fp, #-0x10]
    // 0x15d95fc: StoreField: r0->field_b = r1
    //     0x15d95fc: stur            w1, [x0, #0xb]
    // 0x15d9600: ldur            x1, [fp, #-8]
    // 0x15d9604: r0 = controller()
    //     0x15d9604: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d9608: LoadField: r1 = r0->field_73
    //     0x15d9608: ldur            w1, [x0, #0x73]
    // 0x15d960c: DecompressPointer r1
    //     0x15d960c: add             x1, x1, HEAP, lsl #32
    // 0x15d9610: r0 = value()
    //     0x15d9610: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d9614: tbnz            w0, #4, #0x15d96ac
    // 0x15d9618: ldur            x2, [fp, #-0x18]
    // 0x15d961c: LoadField: r1 = r2->field_13
    //     0x15d961c: ldur            w1, [x2, #0x13]
    // 0x15d9620: DecompressPointer r1
    //     0x15d9620: add             x1, x1, HEAP, lsl #32
    // 0x15d9624: r0 = of()
    //     0x15d9624: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d9628: LoadField: r1 = r0->field_5b
    //     0x15d9628: ldur            w1, [x0, #0x5b]
    // 0x15d962c: DecompressPointer r1
    //     0x15d962c: add             x1, x1, HEAP, lsl #32
    // 0x15d9630: stur            x1, [fp, #-8]
    // 0x15d9634: r0 = ColorFilter()
    //     0x15d9634: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d9638: mov             x1, x0
    // 0x15d963c: ldur            x0, [fp, #-8]
    // 0x15d9640: stur            x1, [fp, #-0x20]
    // 0x15d9644: StoreField: r1->field_7 = r0
    //     0x15d9644: stur            w0, [x1, #7]
    // 0x15d9648: r0 = Instance_BlendMode
    //     0x15d9648: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d964c: ldr             x0, [x0, #0xb30]
    // 0x15d9650: StoreField: r1->field_b = r0
    //     0x15d9650: stur            w0, [x1, #0xb]
    // 0x15d9654: r2 = 1
    //     0x15d9654: movz            x2, #0x1
    // 0x15d9658: StoreField: r1->field_13 = r2
    //     0x15d9658: stur            x2, [x1, #0x13]
    // 0x15d965c: r0 = SvgPicture()
    //     0x15d965c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d9660: stur            x0, [fp, #-8]
    // 0x15d9664: ldur            x16, [fp, #-0x20]
    // 0x15d9668: str             x16, [SP]
    // 0x15d966c: mov             x1, x0
    // 0x15d9670: r2 = "assets/images/search.svg"
    //     0x15d9670: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15d9674: ldr             x2, [x2, #0xa30]
    // 0x15d9678: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d9678: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d967c: ldr             x4, [x4, #0xa38]
    // 0x15d9680: r0 = SvgPicture.asset()
    //     0x15d9680: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d9684: r0 = Align()
    //     0x15d9684: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d9688: r3 = Instance_Alignment
    //     0x15d9688: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d968c: ldr             x3, [x3, #0xb10]
    // 0x15d9690: StoreField: r0->field_f = r3
    //     0x15d9690: stur            w3, [x0, #0xf]
    // 0x15d9694: r4 = 1.000000
    //     0x15d9694: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9698: StoreField: r0->field_13 = r4
    //     0x15d9698: stur            w4, [x0, #0x13]
    // 0x15d969c: ArrayStore: r0[0] = r4  ; List_4
    //     0x15d969c: stur            w4, [x0, #0x17]
    // 0x15d96a0: ldur            x1, [fp, #-8]
    // 0x15d96a4: StoreField: r0->field_b = r1
    //     0x15d96a4: stur            w1, [x0, #0xb]
    // 0x15d96a8: b               #0x15d975c
    // 0x15d96ac: ldur            x5, [fp, #-0x18]
    // 0x15d96b0: r4 = 1.000000
    //     0x15d96b0: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d96b4: r0 = Instance_BlendMode
    //     0x15d96b4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d96b8: ldr             x0, [x0, #0xb30]
    // 0x15d96bc: r3 = Instance_Alignment
    //     0x15d96bc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d96c0: ldr             x3, [x3, #0xb10]
    // 0x15d96c4: r2 = 1
    //     0x15d96c4: movz            x2, #0x1
    // 0x15d96c8: LoadField: r1 = r5->field_13
    //     0x15d96c8: ldur            w1, [x5, #0x13]
    // 0x15d96cc: DecompressPointer r1
    //     0x15d96cc: add             x1, x1, HEAP, lsl #32
    // 0x15d96d0: r0 = of()
    //     0x15d96d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d96d4: LoadField: r1 = r0->field_5b
    //     0x15d96d4: ldur            w1, [x0, #0x5b]
    // 0x15d96d8: DecompressPointer r1
    //     0x15d96d8: add             x1, x1, HEAP, lsl #32
    // 0x15d96dc: stur            x1, [fp, #-8]
    // 0x15d96e0: r0 = ColorFilter()
    //     0x15d96e0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d96e4: mov             x1, x0
    // 0x15d96e8: ldur            x0, [fp, #-8]
    // 0x15d96ec: stur            x1, [fp, #-0x20]
    // 0x15d96f0: StoreField: r1->field_7 = r0
    //     0x15d96f0: stur            w0, [x1, #7]
    // 0x15d96f4: r0 = Instance_BlendMode
    //     0x15d96f4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d96f8: ldr             x0, [x0, #0xb30]
    // 0x15d96fc: StoreField: r1->field_b = r0
    //     0x15d96fc: stur            w0, [x1, #0xb]
    // 0x15d9700: r0 = 1
    //     0x15d9700: movz            x0, #0x1
    // 0x15d9704: StoreField: r1->field_13 = r0
    //     0x15d9704: stur            x0, [x1, #0x13]
    // 0x15d9708: r0 = SvgPicture()
    //     0x15d9708: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d970c: stur            x0, [fp, #-8]
    // 0x15d9710: ldur            x16, [fp, #-0x20]
    // 0x15d9714: str             x16, [SP]
    // 0x15d9718: mov             x1, x0
    // 0x15d971c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d971c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d9720: ldr             x2, [x2, #0xa40]
    // 0x15d9724: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d9724: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d9728: ldr             x4, [x4, #0xa38]
    // 0x15d972c: r0 = SvgPicture.asset()
    //     0x15d972c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d9730: r0 = Align()
    //     0x15d9730: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d9734: mov             x1, x0
    // 0x15d9738: r0 = Instance_Alignment
    //     0x15d9738: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d973c: ldr             x0, [x0, #0xb10]
    // 0x15d9740: StoreField: r1->field_f = r0
    //     0x15d9740: stur            w0, [x1, #0xf]
    // 0x15d9744: r0 = 1.000000
    //     0x15d9744: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9748: StoreField: r1->field_13 = r0
    //     0x15d9748: stur            w0, [x1, #0x13]
    // 0x15d974c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d974c: stur            w0, [x1, #0x17]
    // 0x15d9750: ldur            x0, [fp, #-8]
    // 0x15d9754: StoreField: r1->field_b = r0
    //     0x15d9754: stur            w0, [x1, #0xb]
    // 0x15d9758: mov             x0, x1
    // 0x15d975c: stur            x0, [fp, #-8]
    // 0x15d9760: r0 = InkWell()
    //     0x15d9760: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d9764: mov             x3, x0
    // 0x15d9768: ldur            x0, [fp, #-8]
    // 0x15d976c: stur            x3, [fp, #-0x20]
    // 0x15d9770: StoreField: r3->field_b = r0
    //     0x15d9770: stur            w0, [x3, #0xb]
    // 0x15d9774: ldur            x2, [fp, #-0x18]
    // 0x15d9778: r1 = Function '<anonymous closure>':.
    //     0x15d9778: add             x1, PP, #0x44, lsl #12  ; [pp+0x44248] AnonymousClosure: (0x15d97f0), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15d95a0)
    //     0x15d977c: ldr             x1, [x1, #0x248]
    // 0x15d9780: r0 = AllocateClosure()
    //     0x15d9780: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d9784: ldur            x2, [fp, #-0x20]
    // 0x15d9788: StoreField: r2->field_f = r0
    //     0x15d9788: stur            w0, [x2, #0xf]
    // 0x15d978c: r0 = true
    //     0x15d978c: add             x0, NULL, #0x20  ; true
    // 0x15d9790: StoreField: r2->field_43 = r0
    //     0x15d9790: stur            w0, [x2, #0x43]
    // 0x15d9794: r1 = Instance_BoxShape
    //     0x15d9794: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d9798: ldr             x1, [x1, #0x80]
    // 0x15d979c: StoreField: r2->field_47 = r1
    //     0x15d979c: stur            w1, [x2, #0x47]
    // 0x15d97a0: StoreField: r2->field_6f = r0
    //     0x15d97a0: stur            w0, [x2, #0x6f]
    // 0x15d97a4: r1 = false
    //     0x15d97a4: add             x1, NULL, #0x30  ; false
    // 0x15d97a8: StoreField: r2->field_73 = r1
    //     0x15d97a8: stur            w1, [x2, #0x73]
    // 0x15d97ac: StoreField: r2->field_83 = r0
    //     0x15d97ac: stur            w0, [x2, #0x83]
    // 0x15d97b0: StoreField: r2->field_7b = r1
    //     0x15d97b0: stur            w1, [x2, #0x7b]
    // 0x15d97b4: r0 = AppBar()
    //     0x15d97b4: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d97b8: stur            x0, [fp, #-8]
    // 0x15d97bc: ldur            x16, [fp, #-0x10]
    // 0x15d97c0: str             x16, [SP]
    // 0x15d97c4: mov             x1, x0
    // 0x15d97c8: ldur            x2, [fp, #-0x20]
    // 0x15d97cc: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15d97cc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15d97d0: ldr             x4, [x4, #0xf00]
    // 0x15d97d4: r0 = AppBar()
    //     0x15d97d4: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d97d8: ldur            x0, [fp, #-8]
    // 0x15d97dc: LeaveFrame
    //     0x15d97dc: mov             SP, fp
    //     0x15d97e0: ldp             fp, lr, [SP], #0x10
    // 0x15d97e4: ret
    //     0x15d97e4: ret             
    // 0x15d97e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d97e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d97ec: b               #0x15d95c0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d97f0, size: 0xc8
    // 0x15d97f0: EnterFrame
    //     0x15d97f0: stp             fp, lr, [SP, #-0x10]!
    //     0x15d97f4: mov             fp, SP
    // 0x15d97f8: AllocStack(0x18)
    //     0x15d97f8: sub             SP, SP, #0x18
    // 0x15d97fc: SetupParameters()
    //     0x15d97fc: ldr             x0, [fp, #0x10]
    //     0x15d9800: ldur            w3, [x0, #0x17]
    //     0x15d9804: add             x3, x3, HEAP, lsl #32
    //     0x15d9808: stur            x3, [fp, #-8]
    // 0x15d980c: CheckStackOverflow
    //     0x15d980c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d9810: cmp             SP, x16
    //     0x15d9814: b.ls            #0x15d98b0
    // 0x15d9818: LoadField: r1 = r3->field_f
    //     0x15d9818: ldur            w1, [x3, #0xf]
    // 0x15d981c: DecompressPointer r1
    //     0x15d981c: add             x1, x1, HEAP, lsl #32
    // 0x15d9820: r2 = false
    //     0x15d9820: add             x2, NULL, #0x30  ; false
    // 0x15d9824: r0 = showLoading()
    //     0x15d9824: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d9828: ldur            x0, [fp, #-8]
    // 0x15d982c: LoadField: r1 = r0->field_f
    //     0x15d982c: ldur            w1, [x0, #0xf]
    // 0x15d9830: DecompressPointer r1
    //     0x15d9830: add             x1, x1, HEAP, lsl #32
    // 0x15d9834: r0 = controller()
    //     0x15d9834: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d9838: LoadField: r1 = r0->field_73
    //     0x15d9838: ldur            w1, [x0, #0x73]
    // 0x15d983c: DecompressPointer r1
    //     0x15d983c: add             x1, x1, HEAP, lsl #32
    // 0x15d9840: r0 = value()
    //     0x15d9840: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d9844: tbnz            w0, #4, #0x15d987c
    // 0x15d9848: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d9848: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d984c: ldr             x0, [x0, #0x1c80]
    //     0x15d9850: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d9854: cmp             w0, w16
    //     0x15d9858: b.ne            #0x15d9864
    //     0x15d985c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d9860: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d9864: r16 = "/search"
    //     0x15d9864: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15d9868: ldr             x16, [x16, #0x838]
    // 0x15d986c: stp             x16, NULL, [SP]
    // 0x15d9870: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15d9870: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15d9874: r0 = GetNavigation.toNamed()
    //     0x15d9874: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d9878: b               #0x15d98a0
    // 0x15d987c: ldur            x0, [fp, #-8]
    // 0x15d9880: LoadField: r1 = r0->field_f
    //     0x15d9880: ldur            w1, [x0, #0xf]
    // 0x15d9884: DecompressPointer r1
    //     0x15d9884: add             x1, x1, HEAP, lsl #32
    // 0x15d9888: r2 = false
    //     0x15d9888: add             x2, NULL, #0x30  ; false
    // 0x15d988c: r0 = showLoading()
    //     0x15d988c: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d9890: ldur            x0, [fp, #-8]
    // 0x15d9894: LoadField: r1 = r0->field_f
    //     0x15d9894: ldur            w1, [x0, #0xf]
    // 0x15d9898: DecompressPointer r1
    //     0x15d9898: add             x1, x1, HEAP, lsl #32
    // 0x15d989c: r0 = getBack()
    //     0x15d989c: bl              #0x1485734  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::getBack
    // 0x15d98a0: r0 = Null
    //     0x15d98a0: mov             x0, NULL
    // 0x15d98a4: LeaveFrame
    //     0x15d98a4: mov             SP, fp
    //     0x15d98a8: ldp             fp, lr, [SP], #0x10
    // 0x15d98ac: ret
    //     0x15d98ac: ret             
    // 0x15d98b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d98b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d98b4: b               #0x15d9818
  }
}
