// lib: , url: package:customer_app/app/presentation/views/line/bag/bag_view.dart

// class id: 1049467, size: 0x8
class :: {
}

// class id: 4548, size: 0x14, field offset: 0x14
//   const constructor, 
class BagView extends BaseView<dynamic> {

  [closure] Null <anonymous closure>(dynamic, int) {
    // ** addr: 0x12c00ec, size: 0x74
    // 0x12c00ec: EnterFrame
    //     0x12c00ec: stp             fp, lr, [SP, #-0x10]!
    //     0x12c00f0: mov             fp, SP
    // 0x12c00f4: ldr             x0, [fp, #0x18]
    // 0x12c00f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x12c00f8: ldur            w1, [x0, #0x17]
    // 0x12c00fc: DecompressPointer r1
    //     0x12c00fc: add             x1, x1, HEAP, lsl #32
    // 0x12c0100: CheckStackOverflow
    //     0x12c0100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c0104: cmp             SP, x16
    //     0x12c0108: b.ls            #0x12c0158
    // 0x12c010c: ldr             x0, [fp, #0x10]
    // 0x12c0110: cbz             w0, #0x12c0130
    // 0x12c0114: LoadField: r0 = r1->field_f
    //     0x12c0114: ldur            w0, [x1, #0xf]
    // 0x12c0118: DecompressPointer r0
    //     0x12c0118: add             x0, x0, HEAP, lsl #32
    // 0x12c011c: mov             x1, x0
    // 0x12c0120: r0 = controller()
    //     0x12c0120: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c0124: mov             x1, x0
    // 0x12c0128: r0 = checkoutVariant()
    //     0x12c0128: bl              #0x12c1bc8  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::checkoutVariant
    // 0x12c012c: b               #0x12c0148
    // 0x12c0130: LoadField: r0 = r1->field_f
    //     0x12c0130: ldur            w0, [x1, #0xf]
    // 0x12c0134: DecompressPointer r0
    //     0x12c0134: add             x0, x0, HEAP, lsl #32
    // 0x12c0138: mov             x1, x0
    // 0x12c013c: r0 = controller()
    //     0x12c013c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c0140: mov             x1, x0
    // 0x12c0144: r0 = clearBag()
    //     0x12c0144: bl              #0x12c05b0  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::clearBag
    // 0x12c0148: r0 = Null
    //     0x12c0148: mov             x0, NULL
    // 0x12c014c: LeaveFrame
    //     0x12c014c: mov             SP, fp
    //     0x12c0150: ldp             fp, lr, [SP], #0x10
    // 0x12c0154: ret
    //     0x12c0154: ret             
    // 0x12c0158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c0158: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c015c: b               #0x12c010c
  }
  [closure] Visibility <anonymous closure>(dynamic) {
    // ** addr: 0x12c0160, size: 0x408
    // 0x12c0160: EnterFrame
    //     0x12c0160: stp             fp, lr, [SP, #-0x10]!
    //     0x12c0164: mov             fp, SP
    // 0x12c0168: AllocStack(0x50)
    //     0x12c0168: sub             SP, SP, #0x50
    // 0x12c016c: SetupParameters()
    //     0x12c016c: ldr             x0, [fp, #0x10]
    //     0x12c0170: ldur            w2, [x0, #0x17]
    //     0x12c0174: add             x2, x2, HEAP, lsl #32
    //     0x12c0178: stur            x2, [fp, #-8]
    // 0x12c017c: CheckStackOverflow
    //     0x12c017c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c0180: cmp             SP, x16
    //     0x12c0184: b.ls            #0x12c0558
    // 0x12c0188: LoadField: r1 = r2->field_f
    //     0x12c0188: ldur            w1, [x2, #0xf]
    // 0x12c018c: DecompressPointer r1
    //     0x12c018c: add             x1, x1, HEAP, lsl #32
    // 0x12c0190: r0 = controller()
    //     0x12c0190: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c0194: LoadField: r1 = r0->field_5b
    //     0x12c0194: ldur            w1, [x0, #0x5b]
    // 0x12c0198: DecompressPointer r1
    //     0x12c0198: add             x1, x1, HEAP, lsl #32
    // 0x12c019c: r0 = value()
    //     0x12c019c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12c01a0: LoadField: r1 = r0->field_b
    //     0x12c01a0: ldur            w1, [x0, #0xb]
    // 0x12c01a4: DecompressPointer r1
    //     0x12c01a4: add             x1, x1, HEAP, lsl #32
    // 0x12c01a8: cmp             w1, NULL
    // 0x12c01ac: b.ne            #0x12c01b8
    // 0x12c01b0: r0 = Null
    //     0x12c01b0: mov             x0, NULL
    // 0x12c01b4: b               #0x12c0200
    // 0x12c01b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x12c01b8: ldur            w0, [x1, #0x17]
    // 0x12c01bc: DecompressPointer r0
    //     0x12c01bc: add             x0, x0, HEAP, lsl #32
    // 0x12c01c0: stur            x0, [fp, #-0x10]
    // 0x12c01c4: r1 = Function '<anonymous closure>':.
    //     0x12c01c4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5f8] AnonymousClosure: (0x12c058c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x12c01c8: ldr             x1, [x1, #0x5f8]
    // 0x12c01cc: r2 = Null
    //     0x12c01cc: mov             x2, NULL
    // 0x12c01d0: r0 = AllocateClosure()
    //     0x12c01d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12c01d4: r1 = Function '<anonymous closure>':.
    //     0x12c01d4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e600] AnonymousClosure: (0x12c0574), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x12c01d8: ldr             x1, [x1, #0x600]
    // 0x12c01dc: r2 = Null
    //     0x12c01dc: mov             x2, NULL
    // 0x12c01e0: stur            x0, [fp, #-0x18]
    // 0x12c01e4: r0 = AllocateClosure()
    //     0x12c01e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12c01e8: str             x0, [SP]
    // 0x12c01ec: ldur            x1, [fp, #-0x10]
    // 0x12c01f0: ldur            x2, [fp, #-0x18]
    // 0x12c01f4: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x12c01f4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x12c01f8: ldr             x4, [x4, #0xb48]
    // 0x12c01fc: r0 = firstWhere()
    //     0x12c01fc: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x12c0200: cmp             w0, NULL
    // 0x12c0204: b.eq            #0x12c026c
    // 0x12c0208: LoadField: r1 = r0->field_33
    //     0x12c0208: ldur            w1, [x0, #0x33]
    // 0x12c020c: DecompressPointer r1
    //     0x12c020c: add             x1, x1, HEAP, lsl #32
    // 0x12c0210: cmp             w1, NULL
    // 0x12c0214: b.eq            #0x12c026c
    // 0x12c0218: ldur            x2, [fp, #-8]
    // 0x12c021c: LoadField: r1 = r2->field_f
    //     0x12c021c: ldur            w1, [x2, #0xf]
    // 0x12c0220: DecompressPointer r1
    //     0x12c0220: add             x1, x1, HEAP, lsl #32
    // 0x12c0224: r0 = controller()
    //     0x12c0224: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c0228: LoadField: r1 = r0->field_5b
    //     0x12c0228: ldur            w1, [x0, #0x5b]
    // 0x12c022c: DecompressPointer r1
    //     0x12c022c: add             x1, x1, HEAP, lsl #32
    // 0x12c0230: r0 = value()
    //     0x12c0230: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12c0234: LoadField: r1 = r0->field_b
    //     0x12c0234: ldur            w1, [x0, #0xb]
    // 0x12c0238: DecompressPointer r1
    //     0x12c0238: add             x1, x1, HEAP, lsl #32
    // 0x12c023c: cmp             w1, NULL
    // 0x12c0240: b.ne            #0x12c024c
    // 0x12c0244: r0 = Null
    //     0x12c0244: mov             x0, NULL
    // 0x12c0248: b               #0x12c0254
    // 0x12c024c: LoadField: r0 = r1->field_b
    //     0x12c024c: ldur            w0, [x1, #0xb]
    // 0x12c0250: DecompressPointer r0
    //     0x12c0250: add             x0, x0, HEAP, lsl #32
    // 0x12c0254: cbnz            w0, #0x12c0260
    // 0x12c0258: r1 = false
    //     0x12c0258: add             x1, NULL, #0x30  ; false
    // 0x12c025c: b               #0x12c0264
    // 0x12c0260: r1 = true
    //     0x12c0260: add             x1, NULL, #0x20  ; true
    // 0x12c0264: mov             x0, x1
    // 0x12c0268: b               #0x12c0270
    // 0x12c026c: r0 = false
    //     0x12c026c: add             x0, NULL, #0x30  ; false
    // 0x12c0270: ldur            x2, [fp, #-8]
    // 0x12c0274: stur            x0, [fp, #-0x10]
    // 0x12c0278: LoadField: r1 = r2->field_f
    //     0x12c0278: ldur            w1, [x2, #0xf]
    // 0x12c027c: DecompressPointer r1
    //     0x12c027c: add             x1, x1, HEAP, lsl #32
    // 0x12c0280: r0 = controller()
    //     0x12c0280: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c0284: LoadField: r1 = r0->field_5b
    //     0x12c0284: ldur            w1, [x0, #0x5b]
    // 0x12c0288: DecompressPointer r1
    //     0x12c0288: add             x1, x1, HEAP, lsl #32
    // 0x12c028c: r0 = value()
    //     0x12c028c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12c0290: LoadField: r1 = r0->field_b
    //     0x12c0290: ldur            w1, [x0, #0xb]
    // 0x12c0294: DecompressPointer r1
    //     0x12c0294: add             x1, x1, HEAP, lsl #32
    // 0x12c0298: cmp             w1, NULL
    // 0x12c029c: b.ne            #0x12c02a8
    // 0x12c02a0: r0 = Null
    //     0x12c02a0: mov             x0, NULL
    // 0x12c02a4: b               #0x12c02b0
    // 0x12c02a8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x12c02a8: ldur            w0, [x1, #0x17]
    // 0x12c02ac: DecompressPointer r0
    //     0x12c02ac: add             x0, x0, HEAP, lsl #32
    // 0x12c02b0: cmp             w0, NULL
    // 0x12c02b4: b.ne            #0x12c02f8
    // 0x12c02b8: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x12c02b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12c02bc: ldr             x0, [x0]
    //     0x12c02c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12c02c4: cmp             w0, w16
    //     0x12c02c8: b.ne            #0x12c02d4
    //     0x12c02cc: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x12c02d0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12c02d4: r1 = <Catalogue>
    //     0x12c02d4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x12c02d8: ldr             x1, [x1, #0x418]
    // 0x12c02dc: stur            x0, [fp, #-0x18]
    // 0x12c02e0: r0 = AllocateGrowableArray()
    //     0x12c02e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12c02e4: mov             x1, x0
    // 0x12c02e8: ldur            x0, [fp, #-0x18]
    // 0x12c02ec: StoreField: r1->field_f = r0
    //     0x12c02ec: stur            w0, [x1, #0xf]
    // 0x12c02f0: StoreField: r1->field_b = rZR
    //     0x12c02f0: stur            wzr, [x1, #0xb]
    // 0x12c02f4: mov             x0, x1
    // 0x12c02f8: LoadField: r3 = r0->field_7
    //     0x12c02f8: ldur            w3, [x0, #7]
    // 0x12c02fc: DecompressPointer r3
    //     0x12c02fc: add             x3, x3, HEAP, lsl #32
    // 0x12c0300: stur            x3, [fp, #-0x38]
    // 0x12c0304: LoadField: r1 = r0->field_b
    //     0x12c0304: ldur            w1, [x0, #0xb]
    // 0x12c0308: r4 = LoadInt32Instr(r1)
    //     0x12c0308: sbfx            x4, x1, #1, #0x1f
    // 0x12c030c: stur            x4, [fp, #-0x30]
    // 0x12c0310: LoadField: r5 = r0->field_f
    //     0x12c0310: ldur            w5, [x0, #0xf]
    // 0x12c0314: DecompressPointer r5
    //     0x12c0314: add             x5, x5, HEAP, lsl #32
    // 0x12c0318: stur            x5, [fp, #-0x28]
    // 0x12c031c: r0 = 0
    //     0x12c031c: movz            x0, #0
    // 0x12c0320: CheckStackOverflow
    //     0x12c0320: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c0324: cmp             SP, x16
    //     0x12c0328: b.ls            #0x12c0560
    // 0x12c032c: cmp             x0, x4
    // 0x12c0330: b.ge            #0x12c03e4
    // 0x12c0334: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0x12c0334: add             x16, x5, x0, lsl #2
    //     0x12c0338: ldur            w6, [x16, #0xf]
    // 0x12c033c: DecompressPointer r6
    //     0x12c033c: add             x6, x6, HEAP, lsl #32
    // 0x12c0340: stur            x6, [fp, #-0x18]
    // 0x12c0344: add             x7, x0, #1
    // 0x12c0348: stur            x7, [fp, #-0x20]
    // 0x12c034c: cmp             w6, NULL
    // 0x12c0350: b.ne            #0x12c0384
    // 0x12c0354: mov             x0, x6
    // 0x12c0358: mov             x2, x3
    // 0x12c035c: r1 = Null
    //     0x12c035c: mov             x1, NULL
    // 0x12c0360: cmp             w2, NULL
    // 0x12c0364: b.eq            #0x12c0384
    // 0x12c0368: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x12c0368: ldur            w4, [x2, #0x17]
    // 0x12c036c: DecompressPointer r4
    //     0x12c036c: add             x4, x4, HEAP, lsl #32
    // 0x12c0370: r8 = X0
    //     0x12c0370: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x12c0374: LoadField: r9 = r4->field_7
    //     0x12c0374: ldur            x9, [x4, #7]
    // 0x12c0378: r3 = Null
    //     0x12c0378: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e608] Null
    //     0x12c037c: ldr             x3, [x3, #0x608]
    // 0x12c0380: blr             x9
    // 0x12c0384: ldur            x0, [fp, #-0x18]
    // 0x12c0388: LoadField: r1 = r0->field_63
    //     0x12c0388: ldur            w1, [x0, #0x63]
    // 0x12c038c: DecompressPointer r1
    //     0x12c038c: add             x1, x1, HEAP, lsl #32
    // 0x12c0390: cmp             w1, #2
    // 0x12c0394: b.ne            #0x12c03d0
    // 0x12c0398: LoadField: r1 = r0->field_5f
    //     0x12c0398: ldur            w1, [x0, #0x5f]
    // 0x12c039c: DecompressPointer r1
    //     0x12c039c: add             x1, x1, HEAP, lsl #32
    // 0x12c03a0: cmp             w1, NULL
    // 0x12c03a4: b.ne            #0x12c03b0
    // 0x12c03a8: r0 = 0
    //     0x12c03a8: movz            x0, #0
    // 0x12c03ac: b               #0x12c03c0
    // 0x12c03b0: r2 = LoadInt32Instr(r1)
    //     0x12c03b0: sbfx            x2, x1, #1, #0x1f
    //     0x12c03b4: tbz             w1, #0, #0x12c03bc
    //     0x12c03b8: ldur            x2, [x1, #7]
    // 0x12c03bc: mov             x0, x2
    // 0x12c03c0: cmp             x0, #1
    // 0x12c03c4: b.le            #0x12c03d0
    // 0x12c03c8: r0 = true
    //     0x12c03c8: add             x0, NULL, #0x20  ; true
    // 0x12c03cc: b               #0x12c03e8
    // 0x12c03d0: ldur            x0, [fp, #-0x20]
    // 0x12c03d4: ldur            x3, [fp, #-0x38]
    // 0x12c03d8: ldur            x5, [fp, #-0x28]
    // 0x12c03dc: ldur            x4, [fp, #-0x30]
    // 0x12c03e0: b               #0x12c0320
    // 0x12c03e4: r0 = false
    //     0x12c03e4: add             x0, NULL, #0x30  ; false
    // 0x12c03e8: ldur            x2, [fp, #-8]
    // 0x12c03ec: stur            x0, [fp, #-0x18]
    // 0x12c03f0: LoadField: r1 = r2->field_f
    //     0x12c03f0: ldur            w1, [x2, #0xf]
    // 0x12c03f4: DecompressPointer r1
    //     0x12c03f4: add             x1, x1, HEAP, lsl #32
    // 0x12c03f8: r0 = controller()
    //     0x12c03f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c03fc: LoadField: r1 = r0->field_5b
    //     0x12c03fc: ldur            w1, [x0, #0x5b]
    // 0x12c0400: DecompressPointer r1
    //     0x12c0400: add             x1, x1, HEAP, lsl #32
    // 0x12c0404: r0 = value()
    //     0x12c0404: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12c0408: LoadField: r1 = r0->field_b
    //     0x12c0408: ldur            w1, [x0, #0xb]
    // 0x12c040c: DecompressPointer r1
    //     0x12c040c: add             x1, x1, HEAP, lsl #32
    // 0x12c0410: cmp             w1, NULL
    // 0x12c0414: b.ne            #0x12c0420
    // 0x12c0418: r0 = Null
    //     0x12c0418: mov             x0, NULL
    // 0x12c041c: b               #0x12c043c
    // 0x12c0420: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x12c0420: ldur            w0, [x1, #0x17]
    // 0x12c0424: DecompressPointer r0
    //     0x12c0424: add             x0, x0, HEAP, lsl #32
    // 0x12c0428: LoadField: r1 = r0->field_b
    //     0x12c0428: ldur            w1, [x0, #0xb]
    // 0x12c042c: cbnz            w1, #0x12c0438
    // 0x12c0430: r0 = false
    //     0x12c0430: add             x0, NULL, #0x30  ; false
    // 0x12c0434: b               #0x12c043c
    // 0x12c0438: r0 = true
    //     0x12c0438: add             x0, NULL, #0x20  ; true
    // 0x12c043c: cmp             w0, NULL
    // 0x12c0440: b.ne            #0x12c0448
    // 0x12c0444: r0 = false
    //     0x12c0444: add             x0, NULL, #0x30  ; false
    // 0x12c0448: ldur            x2, [fp, #-8]
    // 0x12c044c: stur            x0, [fp, #-0x28]
    // 0x12c0450: LoadField: r1 = r2->field_f
    //     0x12c0450: ldur            w1, [x2, #0xf]
    // 0x12c0454: DecompressPointer r1
    //     0x12c0454: add             x1, x1, HEAP, lsl #32
    // 0x12c0458: r0 = controller()
    //     0x12c0458: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c045c: LoadField: r1 = r0->field_5b
    //     0x12c045c: ldur            w1, [x0, #0x5b]
    // 0x12c0460: DecompressPointer r1
    //     0x12c0460: add             x1, x1, HEAP, lsl #32
    // 0x12c0464: r0 = value()
    //     0x12c0464: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12c0468: LoadField: r2 = r0->field_b
    //     0x12c0468: ldur            w2, [x0, #0xb]
    // 0x12c046c: DecompressPointer r2
    //     0x12c046c: add             x2, x2, HEAP, lsl #32
    // 0x12c0470: ldur            x0, [fp, #-8]
    // 0x12c0474: stur            x2, [fp, #-0x38]
    // 0x12c0478: LoadField: r1 = r0->field_f
    //     0x12c0478: ldur            w1, [x0, #0xf]
    // 0x12c047c: DecompressPointer r1
    //     0x12c047c: add             x1, x1, HEAP, lsl #32
    // 0x12c0480: r0 = controller()
    //     0x12c0480: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c0484: mov             x1, x0
    // 0x12c0488: r0 = offersPost()
    //     0x12c0488: bl              #0x9eaa38  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::offersPost
    // 0x12c048c: ldur            x2, [fp, #-8]
    // 0x12c0490: LoadField: r1 = r2->field_f
    //     0x12c0490: ldur            w1, [x2, #0xf]
    // 0x12c0494: DecompressPointer r1
    //     0x12c0494: add             x1, x1, HEAP, lsl #32
    // 0x12c0498: r0 = controller()
    //     0x12c0498: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12c049c: LoadField: r1 = r0->field_83
    //     0x12c049c: ldur            w1, [x0, #0x83]
    // 0x12c04a0: DecompressPointer r1
    //     0x12c04a0: add             x1, x1, HEAP, lsl #32
    // 0x12c04a4: cmp             w1, NULL
    // 0x12c04a8: b.ne            #0x12c04b4
    // 0x12c04ac: r4 = ""
    //     0x12c04ac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x12c04b0: b               #0x12c04b8
    // 0x12c04b4: mov             x4, x1
    // 0x12c04b8: ldur            x3, [fp, #-0x10]
    // 0x12c04bc: ldur            x2, [fp, #-0x18]
    // 0x12c04c0: ldur            x1, [fp, #-0x28]
    // 0x12c04c4: ldur            x0, [fp, #-0x38]
    // 0x12c04c8: stur            x4, [fp, #-0x40]
    // 0x12c04cc: r0 = BottomView()
    //     0x12c04cc: bl              #0x12c0568  ; AllocateBottomViewStub -> BottomView (size=0x20)
    // 0x12c04d0: mov             x3, x0
    // 0x12c04d4: ldur            x0, [fp, #-0x38]
    // 0x12c04d8: stur            x3, [fp, #-0x48]
    // 0x12c04dc: StoreField: r3->field_b = r0
    //     0x12c04dc: stur            w0, [x3, #0xb]
    // 0x12c04e0: ldur            x0, [fp, #-0x10]
    // 0x12c04e4: StoreField: r3->field_f = r0
    //     0x12c04e4: stur            w0, [x3, #0xf]
    // 0x12c04e8: ldur            x0, [fp, #-0x18]
    // 0x12c04ec: StoreField: r3->field_13 = r0
    //     0x12c04ec: stur            w0, [x3, #0x13]
    // 0x12c04f0: ldur            x2, [fp, #-8]
    // 0x12c04f4: r1 = Function '<anonymous closure>':.
    //     0x12c04f4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e618] AnonymousClosure: (0x12c00ec), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x12c04f8: ldr             x1, [x1, #0x618]
    // 0x12c04fc: r0 = AllocateClosure()
    //     0x12c04fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12c0500: mov             x1, x0
    // 0x12c0504: ldur            x0, [fp, #-0x48]
    // 0x12c0508: ArrayStore: r0[0] = r1  ; List_4
    //     0x12c0508: stur            w1, [x0, #0x17]
    // 0x12c050c: ldur            x1, [fp, #-0x40]
    // 0x12c0510: StoreField: r0->field_1b = r1
    //     0x12c0510: stur            w1, [x0, #0x1b]
    // 0x12c0514: r0 = Visibility()
    //     0x12c0514: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x12c0518: ldur            x1, [fp, #-0x48]
    // 0x12c051c: StoreField: r0->field_b = r1
    //     0x12c051c: stur            w1, [x0, #0xb]
    // 0x12c0520: r1 = Instance_SizedBox
    //     0x12c0520: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x12c0524: StoreField: r0->field_f = r1
    //     0x12c0524: stur            w1, [x0, #0xf]
    // 0x12c0528: ldur            x1, [fp, #-0x28]
    // 0x12c052c: StoreField: r0->field_13 = r1
    //     0x12c052c: stur            w1, [x0, #0x13]
    // 0x12c0530: r1 = false
    //     0x12c0530: add             x1, NULL, #0x30  ; false
    // 0x12c0534: ArrayStore: r0[0] = r1  ; List_4
    //     0x12c0534: stur            w1, [x0, #0x17]
    // 0x12c0538: StoreField: r0->field_1b = r1
    //     0x12c0538: stur            w1, [x0, #0x1b]
    // 0x12c053c: StoreField: r0->field_1f = r1
    //     0x12c053c: stur            w1, [x0, #0x1f]
    // 0x12c0540: StoreField: r0->field_23 = r1
    //     0x12c0540: stur            w1, [x0, #0x23]
    // 0x12c0544: StoreField: r0->field_27 = r1
    //     0x12c0544: stur            w1, [x0, #0x27]
    // 0x12c0548: StoreField: r0->field_2b = r1
    //     0x12c0548: stur            w1, [x0, #0x2b]
    // 0x12c054c: LeaveFrame
    //     0x12c054c: mov             SP, fp
    //     0x12c0550: ldp             fp, lr, [SP], #0x10
    // 0x12c0554: ret
    //     0x12c0554: ret             
    // 0x12c0558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c0558: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c055c: b               #0x12c0188
    // 0x12c0560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c0560: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c0564: b               #0x12c032c
  }
  [closure] Catalogue <anonymous closure>(dynamic) {
    // ** addr: 0x12c0574, size: 0x18
    // 0x12c0574: EnterFrame
    //     0x12c0574: stp             fp, lr, [SP, #-0x10]!
    //     0x12c0578: mov             fp, SP
    // 0x12c057c: r0 = Catalogue()
    //     0x12c057c: bl              #0x8a1844  ; AllocateCatalogueStub -> Catalogue (size=0x8c)
    // 0x12c0580: LeaveFrame
    //     0x12c0580: mov             SP, fp
    //     0x12c0584: ldp             fp, lr, [SP], #0x10
    // 0x12c0588: ret
    //     0x12c0588: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Catalogue) {
    // ** addr: 0x12c058c, size: 0x24
    // 0x12c058c: ldr             x1, [SP]
    // 0x12c0590: LoadField: r2 = r1->field_33
    //     0x12c0590: ldur            w2, [x1, #0x33]
    // 0x12c0594: DecompressPointer r2
    //     0x12c0594: add             x2, x2, HEAP, lsl #32
    // 0x12c0598: cmp             w2, NULL
    // 0x12c059c: b.ne            #0x12c05a8
    // 0x12c05a0: r0 = true
    //     0x12c05a0: add             x0, NULL, #0x20  ; true
    // 0x12c05a4: b               #0x12c05ac
    // 0x12c05a8: mov             x0, x2
    // 0x12c05ac: ret
    //     0x12c05ac: ret             
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x1358c04, size: 0xe4
    // 0x1358c04: EnterFrame
    //     0x1358c04: stp             fp, lr, [SP, #-0x10]!
    //     0x1358c08: mov             fp, SP
    // 0x1358c0c: AllocStack(0x20)
    //     0x1358c0c: sub             SP, SP, #0x20
    // 0x1358c10: SetupParameters()
    //     0x1358c10: ldr             x0, [fp, #0x28]
    //     0x1358c14: ldur            w2, [x0, #0x17]
    //     0x1358c18: add             x2, x2, HEAP, lsl #32
    //     0x1358c1c: stur            x2, [fp, #-8]
    // 0x1358c20: CheckStackOverflow
    //     0x1358c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1358c24: cmp             SP, x16
    //     0x1358c28: b.ls            #0x1358ce0
    // 0x1358c2c: LoadField: r1 = r2->field_f
    //     0x1358c2c: ldur            w1, [x2, #0xf]
    // 0x1358c30: DecompressPointer r1
    //     0x1358c30: add             x1, x1, HEAP, lsl #32
    // 0x1358c34: r0 = controller()
    //     0x1358c34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358c38: LoadField: r1 = r0->field_7f
    //     0x1358c38: ldur            w1, [x0, #0x7f]
    // 0x1358c3c: DecompressPointer r1
    //     0x1358c3c: add             x1, x1, HEAP, lsl #32
    // 0x1358c40: ldr             x2, [fp, #0x18]
    // 0x1358c44: r0 = value=()
    //     0x1358c44: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1358c48: ldur            x0, [fp, #-8]
    // 0x1358c4c: LoadField: r1 = r0->field_f
    //     0x1358c4c: ldur            w1, [x0, #0xf]
    // 0x1358c50: DecompressPointer r1
    //     0x1358c50: add             x1, x1, HEAP, lsl #32
    // 0x1358c54: r0 = controller()
    //     0x1358c54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358c58: stur            x0, [fp, #-0x10]
    // 0x1358c5c: r0 = EventData()
    //     0x1358c5c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x1358c60: mov             x1, x0
    // 0x1358c64: r0 = "bag"
    //     0x1358c64: add             x0, PP, #0xb, lsl #12  ; [pp+0xb460] "bag"
    //     0x1358c68: ldr             x0, [x0, #0x460]
    // 0x1358c6c: stur            x1, [fp, #-0x18]
    // 0x1358c70: StoreField: r1->field_87 = r0
    //     0x1358c70: stur            w0, [x1, #0x87]
    // 0x1358c74: ldr             x2, [fp, #0x20]
    // 0x1358c78: StoreField: r1->field_a3 = r2
    //     0x1358c78: stur            w2, [x1, #0xa3]
    // 0x1358c7c: r0 = EventsRequest()
    //     0x1358c7c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x1358c80: mov             x1, x0
    // 0x1358c84: r0 = "offer_applied"
    //     0x1358c84: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3d478] "offer_applied"
    //     0x1358c88: ldr             x0, [x0, #0x478]
    // 0x1358c8c: StoreField: r1->field_7 = r0
    //     0x1358c8c: stur            w0, [x1, #7]
    // 0x1358c90: ldur            x0, [fp, #-0x18]
    // 0x1358c94: StoreField: r1->field_b = r0
    //     0x1358c94: stur            w0, [x1, #0xb]
    // 0x1358c98: mov             x2, x1
    // 0x1358c9c: ldur            x1, [fp, #-0x10]
    // 0x1358ca0: r0 = postEvents()
    //     0x1358ca0: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x1358ca4: ldur            x0, [fp, #-8]
    // 0x1358ca8: LoadField: r1 = r0->field_f
    //     0x1358ca8: ldur            w1, [x0, #0xf]
    // 0x1358cac: DecompressPointer r1
    //     0x1358cac: add             x1, x1, HEAP, lsl #32
    // 0x1358cb0: r0 = controller()
    //     0x1358cb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358cb4: ldr             x16, [fp, #0x10]
    // 0x1358cb8: str             x16, [SP]
    // 0x1358cbc: mov             x1, x0
    // 0x1358cc0: ldr             x2, [fp, #0x20]
    // 0x1358cc4: r4 = const [0, 0x3, 0x1, 0x2, type, 0x2, null]
    //     0x1358cc4: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e7b0] List(7) [0, 0x3, 0x1, 0x2, "type", 0x2, Null]
    //     0x1358cc8: ldr             x4, [x4, #0x7b0]
    // 0x1358ccc: r0 = getBag()
    //     0x1358ccc: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x1358cd0: r0 = Null
    //     0x1358cd0: mov             x0, NULL
    // 0x1358cd4: LeaveFrame
    //     0x1358cd4: mov             SP, fp
    //     0x1358cd8: ldp             fp, lr, [SP], #0x10
    // 0x1358cdc: ret
    //     0x1358cdc: ret             
    // 0x1358ce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1358ce0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1358ce4: b               #0x1358c2c
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1358ce8, size: 0x260
    // 0x1358ce8: EnterFrame
    //     0x1358ce8: stp             fp, lr, [SP, #-0x10]!
    //     0x1358cec: mov             fp, SP
    // 0x1358cf0: AllocStack(0x58)
    //     0x1358cf0: sub             SP, SP, #0x58
    // 0x1358cf4: SetupParameters()
    //     0x1358cf4: ldr             x0, [fp, #0x18]
    //     0x1358cf8: ldur            w2, [x0, #0x17]
    //     0x1358cfc: add             x2, x2, HEAP, lsl #32
    //     0x1358d00: stur            x2, [fp, #-8]
    // 0x1358d04: CheckStackOverflow
    //     0x1358d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1358d08: cmp             SP, x16
    //     0x1358d0c: b.ls            #0x1358f40
    // 0x1358d10: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1358d10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1358d14: ldr             x0, [x0, #0x1c80]
    //     0x1358d18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1358d1c: cmp             w0, w16
    //     0x1358d20: b.ne            #0x1358d2c
    //     0x1358d24: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1358d28: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1358d2c: r0 = GetNavigation.size()
    //     0x1358d2c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1358d30: LoadField: d0 = r0->field_f
    //     0x1358d30: ldur            d0, [x0, #0xf]
    // 0x1358d34: d1 = 0.800000
    //     0x1358d34: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x1358d38: ldr             d1, [x17, #0xb28]
    // 0x1358d3c: fmul            d2, d0, d1
    // 0x1358d40: stur            d2, [fp, #-0x48]
    // 0x1358d44: r0 = BoxConstraints()
    //     0x1358d44: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x1358d48: stur            x0, [fp, #-0x10]
    // 0x1358d4c: StoreField: r0->field_7 = rZR
    //     0x1358d4c: stur            xzr, [x0, #7]
    // 0x1358d50: d0 = inf
    //     0x1358d50: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x1358d54: StoreField: r0->field_f = d0
    //     0x1358d54: stur            d0, [x0, #0xf]
    // 0x1358d58: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1358d58: stur            xzr, [x0, #0x17]
    // 0x1358d5c: ldur            d0, [fp, #-0x48]
    // 0x1358d60: StoreField: r0->field_1f = d0
    //     0x1358d60: stur            d0, [x0, #0x1f]
    // 0x1358d64: ldur            x2, [fp, #-8]
    // 0x1358d68: LoadField: r1 = r2->field_f
    //     0x1358d68: ldur            w1, [x2, #0xf]
    // 0x1358d6c: DecompressPointer r1
    //     0x1358d6c: add             x1, x1, HEAP, lsl #32
    // 0x1358d70: r0 = controller()
    //     0x1358d70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358d74: LoadField: r1 = r0->field_63
    //     0x1358d74: ldur            w1, [x0, #0x63]
    // 0x1358d78: DecompressPointer r1
    //     0x1358d78: add             x1, x1, HEAP, lsl #32
    // 0x1358d7c: r0 = value()
    //     0x1358d7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358d80: LoadField: r2 = r0->field_b
    //     0x1358d80: ldur            w2, [x0, #0xb]
    // 0x1358d84: DecompressPointer r2
    //     0x1358d84: add             x2, x2, HEAP, lsl #32
    // 0x1358d88: ldur            x0, [fp, #-8]
    // 0x1358d8c: stur            x2, [fp, #-0x18]
    // 0x1358d90: LoadField: r1 = r0->field_f
    //     0x1358d90: ldur            w1, [x0, #0xf]
    // 0x1358d94: DecompressPointer r1
    //     0x1358d94: add             x1, x1, HEAP, lsl #32
    // 0x1358d98: r0 = controller()
    //     0x1358d98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358d9c: LoadField: r1 = r0->field_5b
    //     0x1358d9c: ldur            w1, [x0, #0x5b]
    // 0x1358da0: DecompressPointer r1
    //     0x1358da0: add             x1, x1, HEAP, lsl #32
    // 0x1358da4: r0 = value()
    //     0x1358da4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358da8: LoadField: r1 = r0->field_b
    //     0x1358da8: ldur            w1, [x0, #0xb]
    // 0x1358dac: DecompressPointer r1
    //     0x1358dac: add             x1, x1, HEAP, lsl #32
    // 0x1358db0: cmp             w1, NULL
    // 0x1358db4: b.ne            #0x1358dc0
    // 0x1358db8: r0 = Null
    //     0x1358db8: mov             x0, NULL
    // 0x1358dbc: b               #0x1358de4
    // 0x1358dc0: LoadField: r0 = r1->field_1b
    //     0x1358dc0: ldur            w0, [x1, #0x1b]
    // 0x1358dc4: DecompressPointer r0
    //     0x1358dc4: add             x0, x0, HEAP, lsl #32
    // 0x1358dc8: cmp             w0, NULL
    // 0x1358dcc: b.ne            #0x1358dd8
    // 0x1358dd0: r0 = Null
    //     0x1358dd0: mov             x0, NULL
    // 0x1358dd4: b               #0x1358de4
    // 0x1358dd8: LoadField: r1 = r0->field_7
    //     0x1358dd8: ldur            w1, [x0, #7]
    // 0x1358ddc: DecompressPointer r1
    //     0x1358ddc: add             x1, x1, HEAP, lsl #32
    // 0x1358de0: mov             x0, x1
    // 0x1358de4: ldur            x2, [fp, #-8]
    // 0x1358de8: stur            x0, [fp, #-0x20]
    // 0x1358dec: LoadField: r1 = r2->field_f
    //     0x1358dec: ldur            w1, [x2, #0xf]
    // 0x1358df0: DecompressPointer r1
    //     0x1358df0: add             x1, x1, HEAP, lsl #32
    // 0x1358df4: r0 = controller()
    //     0x1358df4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358df8: ldur            x2, [fp, #-8]
    // 0x1358dfc: stur            x0, [fp, #-0x28]
    // 0x1358e00: LoadField: r1 = r2->field_f
    //     0x1358e00: ldur            w1, [x2, #0xf]
    // 0x1358e04: DecompressPointer r1
    //     0x1358e04: add             x1, x1, HEAP, lsl #32
    // 0x1358e08: r0 = controller()
    //     0x1358e08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358e0c: LoadField: r1 = r0->field_77
    //     0x1358e0c: ldur            w1, [x0, #0x77]
    // 0x1358e10: DecompressPointer r1
    //     0x1358e10: add             x1, x1, HEAP, lsl #32
    // 0x1358e14: r0 = value()
    //     0x1358e14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358e18: ldur            x2, [fp, #-8]
    // 0x1358e1c: stur            x0, [fp, #-0x30]
    // 0x1358e20: LoadField: r1 = r2->field_f
    //     0x1358e20: ldur            w1, [x2, #0xf]
    // 0x1358e24: DecompressPointer r1
    //     0x1358e24: add             x1, x1, HEAP, lsl #32
    // 0x1358e28: r0 = controller()
    //     0x1358e28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358e2c: LoadField: r1 = r0->field_5b
    //     0x1358e2c: ldur            w1, [x0, #0x5b]
    // 0x1358e30: DecompressPointer r1
    //     0x1358e30: add             x1, x1, HEAP, lsl #32
    // 0x1358e34: r0 = value()
    //     0x1358e34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1358e38: LoadField: r1 = r0->field_b
    //     0x1358e38: ldur            w1, [x0, #0xb]
    // 0x1358e3c: DecompressPointer r1
    //     0x1358e3c: add             x1, x1, HEAP, lsl #32
    // 0x1358e40: cmp             w1, NULL
    // 0x1358e44: b.ne            #0x1358e50
    // 0x1358e48: r3 = Null
    //     0x1358e48: mov             x3, NULL
    // 0x1358e4c: b               #0x1358e5c
    // 0x1358e50: LoadField: r0 = r1->field_1b
    //     0x1358e50: ldur            w0, [x1, #0x1b]
    // 0x1358e54: DecompressPointer r0
    //     0x1358e54: add             x0, x0, HEAP, lsl #32
    // 0x1358e58: mov             x3, x0
    // 0x1358e5c: ldur            x2, [fp, #-0x18]
    // 0x1358e60: ldur            x1, [fp, #-0x20]
    // 0x1358e64: ldur            x0, [fp, #-0x30]
    // 0x1358e68: stur            x3, [fp, #-0x38]
    // 0x1358e6c: r0 = OffersListWidget()
    //     0x1358e6c: bl              #0xbc128c  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0x1358e70: mov             x3, x0
    // 0x1358e74: ldur            x0, [fp, #-0x18]
    // 0x1358e78: stur            x3, [fp, #-0x40]
    // 0x1358e7c: StoreField: r3->field_b = r0
    //     0x1358e7c: stur            w0, [x3, #0xb]
    // 0x1358e80: ldur            x0, [fp, #-0x20]
    // 0x1358e84: StoreField: r3->field_f = r0
    //     0x1358e84: stur            w0, [x3, #0xf]
    // 0x1358e88: ldur            x2, [fp, #-8]
    // 0x1358e8c: r1 = Function '<anonymous closure>':.
    //     0x1358e8c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e7a0] AnonymousClosure: (0x1358f48), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1358e90: ldr             x1, [x1, #0x7a0]
    // 0x1358e94: r0 = AllocateClosure()
    //     0x1358e94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358e98: mov             x1, x0
    // 0x1358e9c: ldur            x0, [fp, #-0x40]
    // 0x1358ea0: StoreField: r0->field_13 = r1
    //     0x1358ea0: stur            w1, [x0, #0x13]
    // 0x1358ea4: ldur            x2, [fp, #-8]
    // 0x1358ea8: r1 = Function '<anonymous closure>':.
    //     0x1358ea8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e7a8] AnonymousClosure: (0x1358c04), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1358eac: ldr             x1, [x1, #0x7a8]
    // 0x1358eb0: r0 = AllocateClosure()
    //     0x1358eb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358eb4: mov             x1, x0
    // 0x1358eb8: ldur            x0, [fp, #-0x40]
    // 0x1358ebc: ArrayStore: r0[0] = r1  ; List_4
    //     0x1358ebc: stur            w1, [x0, #0x17]
    // 0x1358ec0: r1 = true
    //     0x1358ec0: add             x1, NULL, #0x20  ; true
    // 0x1358ec4: StoreField: r0->field_1b = r1
    //     0x1358ec4: stur            w1, [x0, #0x1b]
    // 0x1358ec8: r1 = "bag"
    //     0x1358ec8: add             x1, PP, #0xb, lsl #12  ; [pp+0xb460] "bag"
    //     0x1358ecc: ldr             x1, [x1, #0x460]
    // 0x1358ed0: StoreField: r0->field_23 = r1
    //     0x1358ed0: stur            w1, [x0, #0x23]
    // 0x1358ed4: ldur            x2, [fp, #-0x28]
    // 0x1358ed8: r1 = Function 'postEvents':.
    //     0x1358ed8: add             x1, PP, #0x32, lsl #12  ; [pp+0x321b0] AnonymousClosure: (0x89c5cc), in [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents (0x8608dc)
    //     0x1358edc: ldr             x1, [x1, #0x1b0]
    // 0x1358ee0: r0 = AllocateClosure()
    //     0x1358ee0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1358ee4: mov             x1, x0
    // 0x1358ee8: ldur            x0, [fp, #-0x40]
    // 0x1358eec: StoreField: r0->field_1f = r1
    //     0x1358eec: stur            w1, [x0, #0x1f]
    // 0x1358ef0: r1 = "false"
    //     0x1358ef0: add             x1, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0x1358ef4: ldr             x1, [x1, #0xed8]
    // 0x1358ef8: StoreField: r0->field_27 = r1
    //     0x1358ef8: stur            w1, [x0, #0x27]
    // 0x1358efc: ldur            x1, [fp, #-0x30]
    // 0x1358f00: StoreField: r0->field_2b = r1
    //     0x1358f00: stur            w1, [x0, #0x2b]
    // 0x1358f04: ldur            x1, [fp, #-0x38]
    // 0x1358f08: StoreField: r0->field_2f = r1
    //     0x1358f08: stur            w1, [x0, #0x2f]
    // 0x1358f0c: r0 = Container()
    //     0x1358f0c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1358f10: stur            x0, [fp, #-8]
    // 0x1358f14: ldur            x16, [fp, #-0x10]
    // 0x1358f18: ldur            lr, [fp, #-0x40]
    // 0x1358f1c: stp             lr, x16, [SP]
    // 0x1358f20: mov             x1, x0
    // 0x1358f24: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, constraints, 0x1, null]
    //     0x1358f24: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b30] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "constraints", 0x1, Null]
    //     0x1358f28: ldr             x4, [x4, #0xb30]
    // 0x1358f2c: r0 = Container()
    //     0x1358f2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1358f30: ldur            x0, [fp, #-8]
    // 0x1358f34: LeaveFrame
    //     0x1358f34: mov             SP, fp
    //     0x1358f38: ldp             fp, lr, [SP], #0x10
    // 0x1358f3c: ret
    //     0x1358f3c: ret             
    // 0x1358f40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1358f40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1358f44: b               #0x1358d10
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x1358f48, size: 0x88
    // 0x1358f48: EnterFrame
    //     0x1358f48: stp             fp, lr, [SP, #-0x10]!
    //     0x1358f4c: mov             fp, SP
    // 0x1358f50: AllocStack(0x10)
    //     0x1358f50: sub             SP, SP, #0x10
    // 0x1358f54: SetupParameters()
    //     0x1358f54: ldr             x0, [fp, #0x28]
    //     0x1358f58: ldur            w2, [x0, #0x17]
    //     0x1358f5c: add             x2, x2, HEAP, lsl #32
    //     0x1358f60: stur            x2, [fp, #-8]
    // 0x1358f64: CheckStackOverflow
    //     0x1358f64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1358f68: cmp             SP, x16
    //     0x1358f6c: b.ls            #0x1358fc8
    // 0x1358f70: LoadField: r1 = r2->field_f
    //     0x1358f70: ldur            w1, [x2, #0xf]
    // 0x1358f74: DecompressPointer r1
    //     0x1358f74: add             x1, x1, HEAP, lsl #32
    // 0x1358f78: r0 = controller()
    //     0x1358f78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358f7c: LoadField: r1 = r0->field_7f
    //     0x1358f7c: ldur            w1, [x0, #0x7f]
    // 0x1358f80: DecompressPointer r1
    //     0x1358f80: add             x1, x1, HEAP, lsl #32
    // 0x1358f84: ldr             x2, [fp, #0x18]
    // 0x1358f88: r0 = value=()
    //     0x1358f88: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1358f8c: ldur            x0, [fp, #-8]
    // 0x1358f90: LoadField: r1 = r0->field_f
    //     0x1358f90: ldur            w1, [x0, #0xf]
    // 0x1358f94: DecompressPointer r1
    //     0x1358f94: add             x1, x1, HEAP, lsl #32
    // 0x1358f98: r0 = controller()
    //     0x1358f98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1358f9c: ldr             x16, [fp, #0x10]
    // 0x1358fa0: str             x16, [SP]
    // 0x1358fa4: mov             x1, x0
    // 0x1358fa8: ldr             x2, [fp, #0x20]
    // 0x1358fac: r4 = const [0, 0x3, 0x1, 0x2, type, 0x2, null]
    //     0x1358fac: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e7b0] List(7) [0, 0x3, 0x1, 0x2, "type", 0x2, Null]
    //     0x1358fb0: ldr             x4, [x4, #0x7b0]
    // 0x1358fb4: r0 = getBag()
    //     0x1358fb4: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x1358fb8: r0 = Null
    //     0x1358fb8: mov             x0, NULL
    // 0x1358fbc: LeaveFrame
    //     0x1358fbc: mov             SP, fp
    //     0x1358fc0: ldp             fp, lr, [SP], #0x10
    // 0x1358fc4: ret
    //     0x1358fc4: ret             
    // 0x1358fc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1358fc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1358fcc: b               #0x1358f70
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1358fd0, size: 0x7c
    // 0x1358fd0: EnterFrame
    //     0x1358fd0: stp             fp, lr, [SP, #-0x10]!
    //     0x1358fd4: mov             fp, SP
    // 0x1358fd8: AllocStack(0x30)
    //     0x1358fd8: sub             SP, SP, #0x30
    // 0x1358fdc: SetupParameters()
    //     0x1358fdc: ldr             x0, [fp, #0x10]
    //     0x1358fe0: ldur            w2, [x0, #0x17]
    //     0x1358fe4: add             x2, x2, HEAP, lsl #32
    // 0x1358fe8: CheckStackOverflow
    //     0x1358fe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1358fec: cmp             SP, x16
    //     0x1358ff0: b.ls            #0x1359044
    // 0x1358ff4: LoadField: r0 = r2->field_13
    //     0x1358ff4: ldur            w0, [x2, #0x13]
    // 0x1358ff8: DecompressPointer r0
    //     0x1358ff8: add             x0, x0, HEAP, lsl #32
    // 0x1358ffc: stur            x0, [fp, #-8]
    // 0x1359000: r1 = Function '<anonymous closure>':.
    //     0x1359000: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e798] AnonymousClosure: (0x1358ce8), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359004: ldr             x1, [x1, #0x798]
    // 0x1359008: r0 = AllocateClosure()
    //     0x1359008: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135900c: stp             x0, NULL, [SP, #0x18]
    // 0x1359010: ldur            x16, [fp, #-8]
    // 0x1359014: r30 = true
    //     0x1359014: add             lr, NULL, #0x20  ; true
    // 0x1359018: stp             lr, x16, [SP, #8]
    // 0x135901c: r16 = Instance_RoundedRectangleBorder
    //     0x135901c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1359020: ldr             x16, [x16, #0xd68]
    // 0x1359024: str             x16, [SP]
    // 0x1359028: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x1359028: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x135902c: ldr             x4, [x4, #0xb20]
    // 0x1359030: r0 = showModalBottomSheet()
    //     0x1359030: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1359034: r0 = Null
    //     0x1359034: mov             x0, NULL
    // 0x1359038: LeaveFrame
    //     0x1359038: mov             SP, fp
    //     0x135903c: ldp             fp, lr, [SP], #0x10
    // 0x1359040: ret
    //     0x1359040: ret             
    // 0x1359044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1359044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1359048: b               #0x1358ff4
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x135904c, size: 0x3c4
    // 0x135904c: EnterFrame
    //     0x135904c: stp             fp, lr, [SP, #-0x10]!
    //     0x1359050: mov             fp, SP
    // 0x1359054: AllocStack(0x50)
    //     0x1359054: sub             SP, SP, #0x50
    // 0x1359058: SetupParameters()
    //     0x1359058: ldr             x0, [fp, #0x10]
    //     0x135905c: ldur            w2, [x0, #0x17]
    //     0x1359060: add             x2, x2, HEAP, lsl #32
    //     0x1359064: stur            x2, [fp, #-8]
    // 0x1359068: CheckStackOverflow
    //     0x1359068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135906c: cmp             SP, x16
    //     0x1359070: b.ls            #0x1359408
    // 0x1359074: LoadField: r1 = r2->field_f
    //     0x1359074: ldur            w1, [x2, #0xf]
    // 0x1359078: DecompressPointer r1
    //     0x1359078: add             x1, x1, HEAP, lsl #32
    // 0x135907c: r0 = controller()
    //     0x135907c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359080: LoadField: r1 = r0->field_5b
    //     0x1359080: ldur            w1, [x0, #0x5b]
    // 0x1359084: DecompressPointer r1
    //     0x1359084: add             x1, x1, HEAP, lsl #32
    // 0x1359088: r0 = value()
    //     0x1359088: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135908c: LoadField: r1 = r0->field_b
    //     0x135908c: ldur            w1, [x0, #0xb]
    // 0x1359090: DecompressPointer r1
    //     0x1359090: add             x1, x1, HEAP, lsl #32
    // 0x1359094: cmp             w1, NULL
    // 0x1359098: b.ne            #0x13590a4
    // 0x135909c: r0 = Null
    //     0x135909c: mov             x0, NULL
    // 0x13590a0: b               #0x13590c0
    // 0x13590a4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13590a4: ldur            w0, [x1, #0x17]
    // 0x13590a8: DecompressPointer r0
    //     0x13590a8: add             x0, x0, HEAP, lsl #32
    // 0x13590ac: LoadField: r1 = r0->field_b
    //     0x13590ac: ldur            w1, [x0, #0xb]
    // 0x13590b0: cbnz            w1, #0x13590bc
    // 0x13590b4: r0 = false
    //     0x13590b4: add             x0, NULL, #0x30  ; false
    // 0x13590b8: b               #0x13590c0
    // 0x13590bc: r0 = true
    //     0x13590bc: add             x0, NULL, #0x20  ; true
    // 0x13590c0: cmp             w0, NULL
    // 0x13590c4: b.eq            #0x1359390
    // 0x13590c8: tbnz            w0, #4, #0x1359390
    // 0x13590cc: ldur            x2, [fp, #-8]
    // 0x13590d0: LoadField: r1 = r2->field_f
    //     0x13590d0: ldur            w1, [x2, #0xf]
    // 0x13590d4: DecompressPointer r1
    //     0x13590d4: add             x1, x1, HEAP, lsl #32
    // 0x13590d8: r0 = controller()
    //     0x13590d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13590dc: LoadField: r1 = r0->field_5b
    //     0x13590dc: ldur            w1, [x0, #0x5b]
    // 0x13590e0: DecompressPointer r1
    //     0x13590e0: add             x1, x1, HEAP, lsl #32
    // 0x13590e4: r0 = value()
    //     0x13590e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13590e8: LoadField: r2 = r0->field_b
    //     0x13590e8: ldur            w2, [x0, #0xb]
    // 0x13590ec: DecompressPointer r2
    //     0x13590ec: add             x2, x2, HEAP, lsl #32
    // 0x13590f0: ldur            x0, [fp, #-8]
    // 0x13590f4: stur            x2, [fp, #-0x10]
    // 0x13590f8: LoadField: r1 = r0->field_f
    //     0x13590f8: ldur            w1, [x0, #0xf]
    // 0x13590fc: DecompressPointer r1
    //     0x13590fc: add             x1, x1, HEAP, lsl #32
    // 0x1359100: r0 = controller()
    //     0x1359100: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359104: LoadField: r1 = r0->field_63
    //     0x1359104: ldur            w1, [x0, #0x63]
    // 0x1359108: DecompressPointer r1
    //     0x1359108: add             x1, x1, HEAP, lsl #32
    // 0x135910c: r0 = value()
    //     0x135910c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1359110: LoadField: r2 = r0->field_b
    //     0x1359110: ldur            w2, [x0, #0xb]
    // 0x1359114: DecompressPointer r2
    //     0x1359114: add             x2, x2, HEAP, lsl #32
    // 0x1359118: ldur            x0, [fp, #-8]
    // 0x135911c: stur            x2, [fp, #-0x18]
    // 0x1359120: LoadField: r1 = r0->field_f
    //     0x1359120: ldur            w1, [x0, #0xf]
    // 0x1359124: DecompressPointer r1
    //     0x1359124: add             x1, x1, HEAP, lsl #32
    // 0x1359128: r0 = controller()
    //     0x1359128: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135912c: mov             x1, x0
    // 0x1359130: r0 = bumperCouponData()
    //     0x1359130: bl              #0x9be348  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::bumperCouponData
    // 0x1359134: ldur            x2, [fp, #-8]
    // 0x1359138: stur            x0, [fp, #-0x20]
    // 0x135913c: LoadField: r1 = r2->field_f
    //     0x135913c: ldur            w1, [x2, #0xf]
    // 0x1359140: DecompressPointer r1
    //     0x1359140: add             x1, x1, HEAP, lsl #32
    // 0x1359144: r0 = controller()
    //     0x1359144: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359148: mov             x1, x0
    // 0x135914c: r0 = couponType()
    //     0x135914c: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x1359150: ldur            x2, [fp, #-8]
    // 0x1359154: stur            x0, [fp, #-0x28]
    // 0x1359158: LoadField: r1 = r2->field_f
    //     0x1359158: ldur            w1, [x2, #0xf]
    // 0x135915c: DecompressPointer r1
    //     0x135915c: add             x1, x1, HEAP, lsl #32
    // 0x1359160: r0 = controller()
    //     0x1359160: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359164: LoadField: r1 = r0->field_6f
    //     0x1359164: ldur            w1, [x0, #0x6f]
    // 0x1359168: DecompressPointer r1
    //     0x1359168: add             x1, x1, HEAP, lsl #32
    // 0x135916c: r0 = value()
    //     0x135916c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1359170: LoadField: r1 = r0->field_b
    //     0x1359170: ldur            w1, [x0, #0xb]
    // 0x1359174: DecompressPointer r1
    //     0x1359174: add             x1, x1, HEAP, lsl #32
    // 0x1359178: cmp             w1, NULL
    // 0x135917c: b.ne            #0x1359188
    // 0x1359180: r0 = Null
    //     0x1359180: mov             x0, NULL
    // 0x1359184: b               #0x1359190
    // 0x1359188: LoadField: r0 = r1->field_7
    //     0x1359188: ldur            w0, [x1, #7]
    // 0x135918c: DecompressPointer r0
    //     0x135918c: add             x0, x0, HEAP, lsl #32
    // 0x1359190: cmp             w0, NULL
    // 0x1359194: b.ne            #0x13591a0
    // 0x1359198: r4 = ""
    //     0x1359198: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135919c: b               #0x13591a4
    // 0x13591a0: mov             x4, x0
    // 0x13591a4: ldur            x3, [fp, #-8]
    // 0x13591a8: mov             x0, x4
    // 0x13591ac: stur            x4, [fp, #-0x30]
    // 0x13591b0: r2 = Null
    //     0x13591b0: mov             x2, NULL
    // 0x13591b4: r1 = Null
    //     0x13591b4: mov             x1, NULL
    // 0x13591b8: r4 = 60
    //     0x13591b8: movz            x4, #0x3c
    // 0x13591bc: branchIfSmi(r0, 0x13591c8)
    //     0x13591bc: tbz             w0, #0, #0x13591c8
    // 0x13591c0: r4 = LoadClassIdInstr(r0)
    //     0x13591c0: ldur            x4, [x0, #-1]
    //     0x13591c4: ubfx            x4, x4, #0xc, #0x14
    // 0x13591c8: sub             x4, x4, #0x5e
    // 0x13591cc: cmp             x4, #1
    // 0x13591d0: b.ls            #0x13591e4
    // 0x13591d4: r8 = String
    //     0x13591d4: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x13591d8: r3 = Null
    //     0x13591d8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e6c8] Null
    //     0x13591dc: ldr             x3, [x3, #0x6c8]
    // 0x13591e0: r0 = String()
    //     0x13591e0: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x13591e4: ldur            x2, [fp, #-8]
    // 0x13591e8: LoadField: r1 = r2->field_f
    //     0x13591e8: ldur            w1, [x2, #0xf]
    // 0x13591ec: DecompressPointer r1
    //     0x13591ec: add             x1, x1, HEAP, lsl #32
    // 0x13591f0: r0 = controller()
    //     0x13591f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13591f4: LoadField: r1 = r0->field_83
    //     0x13591f4: ldur            w1, [x0, #0x83]
    // 0x13591f8: DecompressPointer r1
    //     0x13591f8: add             x1, x1, HEAP, lsl #32
    // 0x13591fc: cmp             w1, NULL
    // 0x1359200: b.ne            #0x135920c
    // 0x1359204: r7 = ""
    //     0x1359204: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1359208: b               #0x1359210
    // 0x135920c: mov             x7, x1
    // 0x1359210: ldur            x2, [fp, #-8]
    // 0x1359214: ldur            x6, [fp, #-0x10]
    // 0x1359218: ldur            x5, [fp, #-0x18]
    // 0x135921c: ldur            x4, [fp, #-0x20]
    // 0x1359220: ldur            x3, [fp, #-0x28]
    // 0x1359224: ldur            x0, [fp, #-0x30]
    // 0x1359228: stur            x7, [fp, #-0x38]
    // 0x135922c: LoadField: r1 = r2->field_f
    //     0x135922c: ldur            w1, [x2, #0xf]
    // 0x1359230: DecompressPointer r1
    //     0x1359230: add             x1, x1, HEAP, lsl #32
    // 0x1359234: r0 = controller()
    //     0x1359234: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359238: LoadField: r2 = r0->field_6b
    //     0x1359238: ldur            w2, [x0, #0x6b]
    // 0x135923c: DecompressPointer r2
    //     0x135923c: add             x2, x2, HEAP, lsl #32
    // 0x1359240: ldur            x0, [fp, #-8]
    // 0x1359244: stur            x2, [fp, #-0x40]
    // 0x1359248: LoadField: r1 = r0->field_f
    //     0x1359248: ldur            w1, [x0, #0xf]
    // 0x135924c: DecompressPointer r1
    //     0x135924c: add             x1, x1, HEAP, lsl #32
    // 0x1359250: r0 = controller()
    //     0x1359250: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359254: mov             x1, x0
    // 0x1359258: r0 = freeGiftDetailResponse()
    //     0x1359258: bl              #0x12d6818  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::freeGiftDetailResponse
    // 0x135925c: stur            x0, [fp, #-0x48]
    // 0x1359260: r0 = BagItemViewWidget()
    //     0x1359260: bl              #0x1359410  ; AllocateBagItemViewWidgetStub -> BagItemViewWidget (size=0x4c)
    // 0x1359264: mov             x3, x0
    // 0x1359268: ldur            x0, [fp, #-0x10]
    // 0x135926c: stur            x3, [fp, #-0x50]
    // 0x1359270: StoreField: r3->field_b = r0
    //     0x1359270: stur            w0, [x3, #0xb]
    // 0x1359274: ldur            x0, [fp, #-0x18]
    // 0x1359278: StoreField: r3->field_f = r0
    //     0x1359278: stur            w0, [x3, #0xf]
    // 0x135927c: ldur            x2, [fp, #-8]
    // 0x1359280: r1 = Function '<anonymous closure>':.
    //     0x1359280: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6d8] AnonymousClosure: (0x135b00c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359284: ldr             x1, [x1, #0x6d8]
    // 0x1359288: r0 = AllocateClosure()
    //     0x1359288: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135928c: mov             x1, x0
    // 0x1359290: ldur            x0, [fp, #-0x50]
    // 0x1359294: StoreField: r0->field_13 = r1
    //     0x1359294: stur            w1, [x0, #0x13]
    // 0x1359298: ldur            x2, [fp, #-8]
    // 0x135929c: r1 = Function '<anonymous closure>':.
    //     0x135929c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6e0] AnonymousClosure: (0x135ac24), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x13592a0: ldr             x1, [x1, #0x6e0]
    // 0x13592a4: r0 = AllocateClosure()
    //     0x13592a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13592a8: mov             x1, x0
    // 0x13592ac: ldur            x0, [fp, #-0x50]
    // 0x13592b0: ArrayStore: r0[0] = r1  ; List_4
    //     0x13592b0: stur            w1, [x0, #0x17]
    // 0x13592b4: ldur            x1, [fp, #-0x48]
    // 0x13592b8: StoreField: r0->field_33 = r1
    //     0x13592b8: stur            w1, [x0, #0x33]
    // 0x13592bc: ldur            x2, [fp, #-8]
    // 0x13592c0: r1 = Function '<anonymous closure>':.
    //     0x13592c0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6e8] AnonymousClosure: (0x135a574), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x13592c4: ldr             x1, [x1, #0x6e8]
    // 0x13592c8: r0 = AllocateClosure()
    //     0x13592c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13592cc: mov             x1, x0
    // 0x13592d0: ldur            x0, [fp, #-0x50]
    // 0x13592d4: StoreField: r0->field_1b = r1
    //     0x13592d4: stur            w1, [x0, #0x1b]
    // 0x13592d8: ldur            x2, [fp, #-8]
    // 0x13592dc: r1 = Function '<anonymous closure>':.
    //     0x13592dc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6f0] AnonymousClosure: (0x1358fd0), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x13592e0: ldr             x1, [x1, #0x6f0]
    // 0x13592e4: r0 = AllocateClosure()
    //     0x13592e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13592e8: mov             x1, x0
    // 0x13592ec: ldur            x0, [fp, #-0x50]
    // 0x13592f0: StoreField: r0->field_1f = r1
    //     0x13592f0: stur            w1, [x0, #0x1f]
    // 0x13592f4: ldur            x2, [fp, #-8]
    // 0x13592f8: r1 = Function '<anonymous closure>':.
    //     0x13592f8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6f8] AnonymousClosure: (0x135a4e4), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x13592fc: ldr             x1, [x1, #0x6f8]
    // 0x1359300: r0 = AllocateClosure()
    //     0x1359300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1359304: mov             x1, x0
    // 0x1359308: ldur            x0, [fp, #-0x50]
    // 0x135930c: StoreField: r0->field_23 = r1
    //     0x135930c: stur            w1, [x0, #0x23]
    // 0x1359310: ldur            x2, [fp, #-8]
    // 0x1359314: r1 = Function '<anonymous closure>':.
    //     0x1359314: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e700] AnonymousClosure: (0x135a3e8), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359318: ldr             x1, [x1, #0x700]
    // 0x135931c: r0 = AllocateClosure()
    //     0x135931c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1359320: mov             x1, x0
    // 0x1359324: ldur            x0, [fp, #-0x50]
    // 0x1359328: StoreField: r0->field_27 = r1
    //     0x1359328: stur            w1, [x0, #0x27]
    // 0x135932c: ldur            x2, [fp, #-8]
    // 0x1359330: r1 = Function '<anonymous closure>':.
    //     0x1359330: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e708] AnonymousClosure: (0x1359bcc), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359334: ldr             x1, [x1, #0x708]
    // 0x1359338: r0 = AllocateClosure()
    //     0x1359338: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135933c: mov             x1, x0
    // 0x1359340: ldur            x0, [fp, #-0x50]
    // 0x1359344: StoreField: r0->field_2b = r1
    //     0x1359344: stur            w1, [x0, #0x2b]
    // 0x1359348: ldur            x2, [fp, #-8]
    // 0x135934c: r1 = Function '<anonymous closure>':.
    //     0x135934c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e710] AnonymousClosure: (0x135941c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359350: ldr             x1, [x1, #0x710]
    // 0x1359354: r0 = AllocateClosure()
    //     0x1359354: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1359358: mov             x1, x0
    // 0x135935c: ldur            x0, [fp, #-0x50]
    // 0x1359360: StoreField: r0->field_2f = r1
    //     0x1359360: stur            w1, [x0, #0x2f]
    // 0x1359364: ldur            x1, [fp, #-0x20]
    // 0x1359368: StoreField: r0->field_37 = r1
    //     0x1359368: stur            w1, [x0, #0x37]
    // 0x135936c: ldur            x1, [fp, #-0x28]
    // 0x1359370: StoreField: r0->field_3b = r1
    //     0x1359370: stur            w1, [x0, #0x3b]
    // 0x1359374: ldur            x1, [fp, #-0x30]
    // 0x1359378: StoreField: r0->field_3f = r1
    //     0x1359378: stur            w1, [x0, #0x3f]
    // 0x135937c: ldur            x1, [fp, #-0x38]
    // 0x1359380: StoreField: r0->field_43 = r1
    //     0x1359380: stur            w1, [x0, #0x43]
    // 0x1359384: ldur            x1, [fp, #-0x40]
    // 0x1359388: StoreField: r0->field_47 = r1
    //     0x1359388: stur            w1, [x0, #0x47]
    // 0x135938c: b               #0x13593fc
    // 0x1359390: ldur            x0, [fp, #-8]
    // 0x1359394: LoadField: r1 = r0->field_f
    //     0x1359394: ldur            w1, [x0, #0xf]
    // 0x1359398: DecompressPointer r1
    //     0x1359398: add             x1, x1, HEAP, lsl #32
    // 0x135939c: r0 = controller()
    //     0x135939c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13593a0: LoadField: r1 = r0->field_5b
    //     0x13593a0: ldur            w1, [x0, #0x5b]
    // 0x13593a4: DecompressPointer r1
    //     0x13593a4: add             x1, x1, HEAP, lsl #32
    // 0x13593a8: r0 = value()
    //     0x13593a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13593ac: LoadField: r1 = r0->field_b
    //     0x13593ac: ldur            w1, [x0, #0xb]
    // 0x13593b0: DecompressPointer r1
    //     0x13593b0: add             x1, x1, HEAP, lsl #32
    // 0x13593b4: cmp             w1, NULL
    // 0x13593b8: b.ne            #0x13593d8
    // 0x13593bc: r0 = Container()
    //     0x13593bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13593c0: mov             x1, x0
    // 0x13593c4: stur            x0, [fp, #-8]
    // 0x13593c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13593c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13593cc: r0 = Container()
    //     0x13593cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13593d0: ldur            x0, [fp, #-8]
    // 0x13593d4: b               #0x13593e0
    // 0x13593d8: r0 = Instance_EmptyBagWidget
    //     0x13593d8: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e718] Obj!EmptyBagWidget@d66d81
    //     0x13593dc: ldr             x0, [x0, #0x718]
    // 0x13593e0: stur            x0, [fp, #-8]
    // 0x13593e4: r0 = Center()
    //     0x13593e4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13593e8: r1 = Instance_Alignment
    //     0x13593e8: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13593ec: ldr             x1, [x1, #0xb10]
    // 0x13593f0: StoreField: r0->field_f = r1
    //     0x13593f0: stur            w1, [x0, #0xf]
    // 0x13593f4: ldur            x1, [fp, #-8]
    // 0x13593f8: StoreField: r0->field_b = r1
    //     0x13593f8: stur            w1, [x0, #0xb]
    // 0x13593fc: LeaveFrame
    //     0x13593fc: mov             SP, fp
    //     0x1359400: ldp             fp, lr, [SP], #0x10
    // 0x1359404: ret
    //     0x1359404: ret             
    // 0x1359408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1359408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135940c: b               #0x1359074
  }
  [closure] Null <anonymous closure>(dynamic, Catalogue?, int) {
    // ** addr: 0x135941c, size: 0x3d8
    // 0x135941c: EnterFrame
    //     0x135941c: stp             fp, lr, [SP, #-0x10]!
    //     0x1359420: mov             fp, SP
    // 0x1359424: AllocStack(0x58)
    //     0x1359424: sub             SP, SP, #0x58
    // 0x1359428: SetupParameters()
    //     0x1359428: ldr             x0, [fp, #0x20]
    //     0x135942c: ldur            w1, [x0, #0x17]
    //     0x1359430: add             x1, x1, HEAP, lsl #32
    //     0x1359434: stur            x1, [fp, #-0x18]
    // 0x1359438: CheckStackOverflow
    //     0x1359438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135943c: cmp             SP, x16
    //     0x1359440: b.ls            #0x13597c0
    // 0x1359444: ldr             x0, [fp, #0x18]
    // 0x1359448: cmp             w0, NULL
    // 0x135944c: b.ne            #0x1359458
    // 0x1359450: r2 = Null
    //     0x1359450: mov             x2, NULL
    // 0x1359454: b               #0x1359460
    // 0x1359458: LoadField: r2 = r0->field_b
    //     0x1359458: ldur            w2, [x0, #0xb]
    // 0x135945c: DecompressPointer r2
    //     0x135945c: add             x2, x2, HEAP, lsl #32
    // 0x1359460: stur            x2, [fp, #-0x10]
    // 0x1359464: cmp             w0, NULL
    // 0x1359468: b.ne            #0x1359474
    // 0x135946c: r4 = Null
    //     0x135946c: mov             x4, NULL
    // 0x1359470: b               #0x135949c
    // 0x1359474: LoadField: r3 = r0->field_1f
    //     0x1359474: ldur            w3, [x0, #0x1f]
    // 0x1359478: DecompressPointer r3
    //     0x1359478: add             x3, x3, HEAP, lsl #32
    // 0x135947c: cmp             w3, NULL
    // 0x1359480: b.ne            #0x135948c
    // 0x1359484: r3 = Null
    //     0x1359484: mov             x3, NULL
    // 0x1359488: b               #0x1359498
    // 0x135948c: LoadField: r4 = r3->field_b
    //     0x135948c: ldur            w4, [x3, #0xb]
    // 0x1359490: DecompressPointer r4
    //     0x1359490: add             x4, x4, HEAP, lsl #32
    // 0x1359494: mov             x3, x4
    // 0x1359498: mov             x4, x3
    // 0x135949c: ldr             x3, [fp, #0x10]
    // 0x13594a0: stur            x4, [fp, #-8]
    // 0x13594a4: r0 = AddToBagRequest()
    //     0x13594a4: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0x13594a8: mov             x2, x0
    // 0x13594ac: ldur            x0, [fp, #-0x10]
    // 0x13594b0: stur            x2, [fp, #-0x20]
    // 0x13594b4: StoreField: r2->field_7 = r0
    //     0x13594b4: stur            w0, [x2, #7]
    // 0x13594b8: ldur            x0, [fp, #-8]
    // 0x13594bc: StoreField: r2->field_b = r0
    //     0x13594bc: stur            w0, [x2, #0xb]
    // 0x13594c0: ldr             x0, [fp, #0x10]
    // 0x13594c4: r1 = LoadInt32Instr(r0)
    //     0x13594c4: sbfx            x1, x0, #1, #0x1f
    //     0x13594c8: tbz             w0, #0, #0x13594d0
    //     0x13594cc: ldur            x1, [x0, #7]
    // 0x13594d0: StoreField: r2->field_f = r1
    //     0x13594d0: stur            x1, [x2, #0xf]
    // 0x13594d4: ldur            x3, [fp, #-0x18]
    // 0x13594d8: LoadField: r1 = r3->field_f
    //     0x13594d8: ldur            w1, [x3, #0xf]
    // 0x13594dc: DecompressPointer r1
    //     0x13594dc: add             x1, x1, HEAP, lsl #32
    // 0x13594e0: r0 = controller()
    //     0x13594e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13594e4: LoadField: r1 = r0->field_73
    //     0x13594e4: ldur            w1, [x0, #0x73]
    // 0x13594e8: DecompressPointer r1
    //     0x13594e8: add             x1, x1, HEAP, lsl #32
    // 0x13594ec: r0 = value()
    //     0x13594ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13594f0: LoadField: r1 = r0->field_67
    //     0x13594f0: ldur            w1, [x0, #0x67]
    // 0x13594f4: DecompressPointer r1
    //     0x13594f4: add             x1, x1, HEAP, lsl #32
    // 0x13594f8: cmp             w1, NULL
    // 0x13594fc: b.ne            #0x1359508
    // 0x1359500: r0 = Null
    //     0x1359500: mov             x0, NULL
    // 0x1359504: b               #0x1359540
    // 0x1359508: LoadField: r0 = r1->field_7
    //     0x1359508: ldur            w0, [x1, #7]
    // 0x135950c: DecompressPointer r0
    //     0x135950c: add             x0, x0, HEAP, lsl #32
    // 0x1359510: cmp             w0, NULL
    // 0x1359514: b.ne            #0x1359520
    // 0x1359518: r0 = Null
    //     0x1359518: mov             x0, NULL
    // 0x135951c: b               #0x1359540
    // 0x1359520: LoadField: r1 = r0->field_b
    //     0x1359520: ldur            w1, [x0, #0xb]
    // 0x1359524: DecompressPointer r1
    //     0x1359524: add             x1, x1, HEAP, lsl #32
    // 0x1359528: cmp             w1, NULL
    // 0x135952c: b.ne            #0x1359538
    // 0x1359530: r0 = Null
    //     0x1359530: mov             x0, NULL
    // 0x1359534: b               #0x1359540
    // 0x1359538: LoadField: r0 = r1->field_7
    //     0x1359538: ldur            w0, [x1, #7]
    // 0x135953c: DecompressPointer r0
    //     0x135953c: add             x0, x0, HEAP, lsl #32
    // 0x1359540: cmp             w0, NULL
    // 0x1359544: b.eq            #0x1359794
    // 0x1359548: tbnz            w0, #4, #0x1359794
    // 0x135954c: ldr             x2, [fp, #0x18]
    // 0x1359550: ldur            x0, [fp, #-0x18]
    // 0x1359554: LoadField: r1 = r0->field_f
    //     0x1359554: ldur            w1, [x0, #0xf]
    // 0x1359558: DecompressPointer r1
    //     0x1359558: add             x1, x1, HEAP, lsl #32
    // 0x135955c: r0 = controller()
    //     0x135955c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359560: LoadField: r1 = r0->field_53
    //     0x1359560: ldur            w1, [x0, #0x53]
    // 0x1359564: DecompressPointer r1
    //     0x1359564: add             x1, x1, HEAP, lsl #32
    // 0x1359568: ldr             x0, [fp, #0x18]
    // 0x135956c: stur            x1, [fp, #-0x10]
    // 0x1359570: cmp             w0, NULL
    // 0x1359574: b.ne            #0x1359580
    // 0x1359578: r2 = Null
    //     0x1359578: mov             x2, NULL
    // 0x135957c: b               #0x13595a4
    // 0x1359580: LoadField: r2 = r0->field_1f
    //     0x1359580: ldur            w2, [x0, #0x1f]
    // 0x1359584: DecompressPointer r2
    //     0x1359584: add             x2, x2, HEAP, lsl #32
    // 0x1359588: cmp             w2, NULL
    // 0x135958c: b.ne            #0x1359598
    // 0x1359590: r2 = Null
    //     0x1359590: mov             x2, NULL
    // 0x1359594: b               #0x13595a4
    // 0x1359598: LoadField: r3 = r2->field_b
    //     0x1359598: ldur            w3, [x2, #0xb]
    // 0x135959c: DecompressPointer r3
    //     0x135959c: add             x3, x3, HEAP, lsl #32
    // 0x13595a0: mov             x2, x3
    // 0x13595a4: cmp             w2, NULL
    // 0x13595a8: b.ne            #0x13595b0
    // 0x13595ac: r2 = ""
    //     0x13595ac: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13595b0: stur            x2, [fp, #-8]
    // 0x13595b4: cmp             w0, NULL
    // 0x13595b8: b.ne            #0x13595c4
    // 0x13595bc: r0 = Null
    //     0x13595bc: mov             x0, NULL
    // 0x13595c0: b               #0x13595e4
    // 0x13595c4: LoadField: r3 = r0->field_43
    //     0x13595c4: ldur            w3, [x0, #0x43]
    // 0x13595c8: DecompressPointer r3
    //     0x13595c8: add             x3, x3, HEAP, lsl #32
    // 0x13595cc: cmp             w3, NULL
    // 0x13595d0: b.ne            #0x13595dc
    // 0x13595d4: r0 = Null
    //     0x13595d4: mov             x0, NULL
    // 0x13595d8: b               #0x13595e4
    // 0x13595dc: stp             x3, NULL, [SP]
    // 0x13595e0: r0 = _Double.fromInteger()
    //     0x13595e0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x13595e4: cmp             w0, NULL
    // 0x13595e8: b.ne            #0x13595f4
    // 0x13595ec: d0 = 0.000000
    //     0x13595ec: eor             v0.16b, v0.16b, v0.16b
    // 0x13595f0: b               #0x13595f8
    // 0x13595f4: LoadField: d0 = r0->field_7
    //     0x13595f4: ldur            d0, [x0, #7]
    // 0x13595f8: ldr             x0, [fp, #0x18]
    // 0x13595fc: stur            d0, [fp, #-0x30]
    // 0x1359600: r1 = Null
    //     0x1359600: mov             x1, NULL
    // 0x1359604: r2 = 12
    //     0x1359604: movz            x2, #0xc
    // 0x1359608: r0 = AllocateArray()
    //     0x1359608: bl              #0x16f7198  ; AllocateArrayStub
    // 0x135960c: stur            x0, [fp, #-0x28]
    // 0x1359610: r16 = "id"
    //     0x1359610: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0x1359614: ldr             x16, [x16, #0x400]
    // 0x1359618: StoreField: r0->field_f = r16
    //     0x1359618: stur            w16, [x0, #0xf]
    // 0x135961c: ldr             x1, [fp, #0x18]
    // 0x1359620: cmp             w1, NULL
    // 0x1359624: b.ne            #0x1359630
    // 0x1359628: r2 = Null
    //     0x1359628: mov             x2, NULL
    // 0x135962c: b               #0x1359654
    // 0x1359630: LoadField: r2 = r1->field_1f
    //     0x1359630: ldur            w2, [x1, #0x1f]
    // 0x1359634: DecompressPointer r2
    //     0x1359634: add             x2, x2, HEAP, lsl #32
    // 0x1359638: cmp             w2, NULL
    // 0x135963c: b.ne            #0x1359648
    // 0x1359640: r2 = Null
    //     0x1359640: mov             x2, NULL
    // 0x1359644: b               #0x1359654
    // 0x1359648: LoadField: r3 = r2->field_b
    //     0x1359648: ldur            w3, [x2, #0xb]
    // 0x135964c: DecompressPointer r3
    //     0x135964c: add             x3, x3, HEAP, lsl #32
    // 0x1359650: mov             x2, x3
    // 0x1359654: cmp             w2, NULL
    // 0x1359658: b.ne            #0x1359664
    // 0x135965c: r3 = ""
    //     0x135965c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1359660: b               #0x1359668
    // 0x1359664: mov             x3, x2
    // 0x1359668: ldr             x2, [fp, #0x10]
    // 0x135966c: StoreField: r0->field_13 = r3
    //     0x135966c: stur            w3, [x0, #0x13]
    // 0x1359670: r16 = "quantity"
    //     0x1359670: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x1359674: ldr             x16, [x16, #0x428]
    // 0x1359678: ArrayStore: r0[0] = r16  ; List_4
    //     0x1359678: stur            w16, [x0, #0x17]
    // 0x135967c: StoreField: r0->field_1b = r2
    //     0x135967c: stur            w2, [x0, #0x1b]
    // 0x1359680: r16 = "item_price"
    //     0x1359680: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0x1359684: ldr             x16, [x16, #0x498]
    // 0x1359688: StoreField: r0->field_1f = r16
    //     0x1359688: stur            w16, [x0, #0x1f]
    // 0x135968c: cmp             w1, NULL
    // 0x1359690: b.ne            #0x135969c
    // 0x1359694: r0 = Null
    //     0x1359694: mov             x0, NULL
    // 0x1359698: b               #0x13596bc
    // 0x135969c: LoadField: r2 = r1->field_43
    //     0x135969c: ldur            w2, [x1, #0x43]
    // 0x13596a0: DecompressPointer r2
    //     0x13596a0: add             x2, x2, HEAP, lsl #32
    // 0x13596a4: cmp             w2, NULL
    // 0x13596a8: b.ne            #0x13596b4
    // 0x13596ac: r0 = Null
    //     0x13596ac: mov             x0, NULL
    // 0x13596b0: b               #0x13596bc
    // 0x13596b4: stp             x2, NULL, [SP]
    // 0x13596b8: r0 = _Double.fromInteger()
    //     0x13596b8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x13596bc: cmp             w0, NULL
    // 0x13596c0: b.ne            #0x13596cc
    // 0x13596c4: d1 = 0.000000
    //     0x13596c4: eor             v1.16b, v1.16b, v1.16b
    // 0x13596c8: b               #0x13596d4
    // 0x13596cc: LoadField: d0 = r0->field_7
    //     0x13596cc: ldur            d0, [x0, #7]
    // 0x13596d0: mov             v1.16b, v0.16b
    // 0x13596d4: ldur            d0, [fp, #-0x30]
    // 0x13596d8: r0 = inline_Allocate_Double()
    //     0x13596d8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13596dc: add             x0, x0, #0x10
    //     0x13596e0: cmp             x1, x0
    //     0x13596e4: b.ls            #0x13597c8
    //     0x13596e8: str             x0, [THR, #0x50]  ; THR::top
    //     0x13596ec: sub             x0, x0, #0xf
    //     0x13596f0: movz            x1, #0xe15c
    //     0x13596f4: movk            x1, #0x3, lsl #16
    //     0x13596f8: stur            x1, [x0, #-1]
    // 0x13596fc: StoreField: r0->field_7 = d1
    //     0x13596fc: stur            d1, [x0, #7]
    // 0x1359700: ldur            x1, [fp, #-0x28]
    // 0x1359704: ArrayStore: r1[5] = r0  ; List_4
    //     0x1359704: add             x25, x1, #0x23
    //     0x1359708: str             w0, [x25]
    //     0x135970c: tbz             w0, #0, #0x1359728
    //     0x1359710: ldurb           w16, [x1, #-1]
    //     0x1359714: ldurb           w17, [x0, #-1]
    //     0x1359718: and             x16, x17, x16, lsr #2
    //     0x135971c: tst             x16, HEAP, lsr #32
    //     0x1359720: b.eq            #0x1359728
    //     0x1359724: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1359728: r16 = <String, dynamic>
    //     0x1359728: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x135972c: ldur            lr, [fp, #-0x28]
    // 0x1359730: stp             lr, x16, [SP]
    // 0x1359734: r0 = Map._fromLiteral()
    //     0x1359734: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1359738: ldur            d0, [fp, #-0x30]
    // 0x135973c: r1 = inline_Allocate_Double()
    //     0x135973c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x1359740: add             x1, x1, #0x10
    //     0x1359744: cmp             x2, x1
    //     0x1359748: b.ls            #0x13597d8
    //     0x135974c: str             x1, [THR, #0x50]  ; THR::top
    //     0x1359750: sub             x1, x1, #0xf
    //     0x1359754: movz            x2, #0xe15c
    //     0x1359758: movk            x2, #0x3, lsl #16
    //     0x135975c: stur            x2, [x1, #-1]
    // 0x1359760: StoreField: r1->field_7 = d0
    //     0x1359760: stur            d0, [x1, #7]
    // 0x1359764: ldur            x16, [fp, #-8]
    // 0x1359768: r30 = "product"
    //     0x1359768: add             lr, PP, #0x12, lsl #12  ; [pp+0x12240] "product"
    //     0x135976c: ldr             lr, [lr, #0x240]
    // 0x1359770: stp             lr, x16, [SP, #0x18]
    // 0x1359774: r16 = "INR"
    //     0x1359774: add             x16, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0x1359778: ldr             x16, [x16, #0x4c0]
    // 0x135977c: stp             x1, x16, [SP, #8]
    // 0x1359780: str             x0, [SP]
    // 0x1359784: ldur            x1, [fp, #-0x10]
    // 0x1359788: r4 = const [0, 0x6, 0x5, 0x1, content, 0x5, currency, 0x3, id, 0x1, price, 0x4, type, 0x2, null]
    //     0x1359788: add             x4, PP, #0x32, lsl #12  ; [pp+0x32318] List(15) [0, 0x6, 0x5, 0x1, "content", 0x5, "currency", 0x3, "id", 0x1, "price", 0x4, "type", 0x2, Null]
    //     0x135978c: ldr             x4, [x4, #0x318]
    // 0x1359790: r0 = logAddToCart()
    //     0x1359790: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0x1359794: ldur            x0, [fp, #-0x18]
    // 0x1359798: LoadField: r1 = r0->field_f
    //     0x1359798: ldur            w1, [x0, #0xf]
    // 0x135979c: DecompressPointer r1
    //     0x135979c: add             x1, x1, HEAP, lsl #32
    // 0x13597a0: r0 = controller()
    //     0x13597a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13597a4: mov             x1, x0
    // 0x13597a8: ldur            x2, [fp, #-0x20]
    // 0x13597ac: r0 = addToBag()
    //     0x13597ac: bl              #0x13597f4  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::addToBag
    // 0x13597b0: r0 = Null
    //     0x13597b0: mov             x0, NULL
    // 0x13597b4: LeaveFrame
    //     0x13597b4: mov             SP, fp
    //     0x13597b8: ldp             fp, lr, [SP], #0x10
    // 0x13597bc: ret
    //     0x13597bc: ret             
    // 0x13597c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13597c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13597c4: b               #0x1359444
    // 0x13597c8: stp             q0, q1, [SP, #-0x20]!
    // 0x13597cc: r0 = AllocateDouble()
    //     0x13597cc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13597d0: ldp             q0, q1, [SP], #0x20
    // 0x13597d4: b               #0x13596fc
    // 0x13597d8: SaveReg d0
    //     0x13597d8: str             q0, [SP, #-0x10]!
    // 0x13597dc: SaveReg r0
    //     0x13597dc: str             x0, [SP, #-8]!
    // 0x13597e0: r0 = AllocateDouble()
    //     0x13597e0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13597e4: mov             x1, x0
    // 0x13597e8: RestoreReg r0
    //     0x13597e8: ldr             x0, [SP], #8
    // 0x13597ec: RestoreReg d0
    //     0x13597ec: ldr             q0, [SP], #0x10
    // 0x13597f0: b               #0x1359760
  }
  [closure] Null <anonymous closure>(dynamic, int?, int?, Catalogue?) {
    // ** addr: 0x1359bcc, size: 0xb0
    // 0x1359bcc: EnterFrame
    //     0x1359bcc: stp             fp, lr, [SP, #-0x10]!
    //     0x1359bd0: mov             fp, SP
    // 0x1359bd4: AllocStack(0x38)
    //     0x1359bd4: sub             SP, SP, #0x38
    // 0x1359bd8: SetupParameters()
    //     0x1359bd8: ldr             x0, [fp, #0x28]
    //     0x1359bdc: ldur            w1, [x0, #0x17]
    //     0x1359be0: add             x1, x1, HEAP, lsl #32
    //     0x1359be4: stur            x1, [fp, #-8]
    // 0x1359be8: CheckStackOverflow
    //     0x1359be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1359bec: cmp             SP, x16
    //     0x1359bf0: b.ls            #0x1359c74
    // 0x1359bf4: r1 = 3
    //     0x1359bf4: movz            x1, #0x3
    // 0x1359bf8: r0 = AllocateContext()
    //     0x1359bf8: bl              #0x16f6108  ; AllocateContextStub
    // 0x1359bfc: mov             x1, x0
    // 0x1359c00: ldur            x0, [fp, #-8]
    // 0x1359c04: StoreField: r1->field_b = r0
    //     0x1359c04: stur            w0, [x1, #0xb]
    // 0x1359c08: ldr             x2, [fp, #0x20]
    // 0x1359c0c: StoreField: r1->field_f = r2
    //     0x1359c0c: stur            w2, [x1, #0xf]
    // 0x1359c10: ldr             x2, [fp, #0x18]
    // 0x1359c14: StoreField: r1->field_13 = r2
    //     0x1359c14: stur            w2, [x1, #0x13]
    // 0x1359c18: ldr             x2, [fp, #0x10]
    // 0x1359c1c: ArrayStore: r1[0] = r2  ; List_4
    //     0x1359c1c: stur            w2, [x1, #0x17]
    // 0x1359c20: LoadField: r3 = r0->field_13
    //     0x1359c20: ldur            w3, [x0, #0x13]
    // 0x1359c24: DecompressPointer r3
    //     0x1359c24: add             x3, x3, HEAP, lsl #32
    // 0x1359c28: mov             x2, x1
    // 0x1359c2c: stur            x3, [fp, #-0x10]
    // 0x1359c30: r1 = Function '<anonymous closure>':.
    //     0x1359c30: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e750] AnonymousClosure: (0x1359c7c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359c34: ldr             x1, [x1, #0x750]
    // 0x1359c38: r0 = AllocateClosure()
    //     0x1359c38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1359c3c: stp             x0, NULL, [SP, #0x18]
    // 0x1359c40: ldur            x16, [fp, #-0x10]
    // 0x1359c44: r30 = Instance_RoundedRectangleBorder
    //     0x1359c44: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x1359c48: ldr             lr, [lr, #0xd68]
    // 0x1359c4c: stp             lr, x16, [SP, #8]
    // 0x1359c50: r16 = true
    //     0x1359c50: add             x16, NULL, #0x20  ; true
    // 0x1359c54: str             x16, [SP]
    // 0x1359c58: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x1359c58: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x1359c5c: ldr             x4, [x4, #0xd70]
    // 0x1359c60: r0 = showModalBottomSheet()
    //     0x1359c60: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1359c64: r0 = Null
    //     0x1359c64: mov             x0, NULL
    // 0x1359c68: LeaveFrame
    //     0x1359c68: mov             SP, fp
    //     0x1359c6c: ldp             fp, lr, [SP], #0x10
    // 0x1359c70: ret
    //     0x1359c70: ret             
    // 0x1359c74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1359c74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1359c78: b               #0x1359bf4
  }
  [closure] BagBottomSheetToChoose <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1359c7c, size: 0xa8
    // 0x1359c7c: EnterFrame
    //     0x1359c7c: stp             fp, lr, [SP, #-0x10]!
    //     0x1359c80: mov             fp, SP
    // 0x1359c84: AllocStack(0x28)
    //     0x1359c84: sub             SP, SP, #0x28
    // 0x1359c88: SetupParameters()
    //     0x1359c88: ldr             x0, [fp, #0x18]
    //     0x1359c8c: ldur            w2, [x0, #0x17]
    //     0x1359c90: add             x2, x2, HEAP, lsl #32
    //     0x1359c94: stur            x2, [fp, #-0x20]
    // 0x1359c98: LoadField: r0 = r2->field_f
    //     0x1359c98: ldur            w0, [x2, #0xf]
    // 0x1359c9c: DecompressPointer r0
    //     0x1359c9c: add             x0, x0, HEAP, lsl #32
    // 0x1359ca0: stur            x0, [fp, #-0x18]
    // 0x1359ca4: LoadField: r1 = r2->field_13
    //     0x1359ca4: ldur            w1, [x2, #0x13]
    // 0x1359ca8: DecompressPointer r1
    //     0x1359ca8: add             x1, x1, HEAP, lsl #32
    // 0x1359cac: stur            x1, [fp, #-0x10]
    // 0x1359cb0: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x1359cb0: ldur            w3, [x2, #0x17]
    // 0x1359cb4: DecompressPointer r3
    //     0x1359cb4: add             x3, x3, HEAP, lsl #32
    // 0x1359cb8: stur            x3, [fp, #-8]
    // 0x1359cbc: r0 = BagBottomSheetToChoose()
    //     0x1359cbc: bl              #0x1359d24  ; AllocateBagBottomSheetToChooseStub -> BagBottomSheetToChoose (size=0x20)
    // 0x1359cc0: mov             x3, x0
    // 0x1359cc4: ldur            x0, [fp, #-0x18]
    // 0x1359cc8: stur            x3, [fp, #-0x28]
    // 0x1359ccc: StoreField: r3->field_b = r0
    //     0x1359ccc: stur            w0, [x3, #0xb]
    // 0x1359cd0: ldur            x0, [fp, #-0x10]
    // 0x1359cd4: StoreField: r3->field_f = r0
    //     0x1359cd4: stur            w0, [x3, #0xf]
    // 0x1359cd8: ldur            x0, [fp, #-8]
    // 0x1359cdc: StoreField: r3->field_13 = r0
    //     0x1359cdc: stur            w0, [x3, #0x13]
    // 0x1359ce0: ldur            x2, [fp, #-0x20]
    // 0x1359ce4: r1 = Function '<anonymous closure>':.
    //     0x1359ce4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e758] AnonymousClosure: (0x1359d88), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359ce8: ldr             x1, [x1, #0x758]
    // 0x1359cec: r0 = AllocateClosure()
    //     0x1359cec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1359cf0: mov             x1, x0
    // 0x1359cf4: ldur            x0, [fp, #-0x28]
    // 0x1359cf8: ArrayStore: r0[0] = r1  ; List_4
    //     0x1359cf8: stur            w1, [x0, #0x17]
    // 0x1359cfc: ldur            x2, [fp, #-0x20]
    // 0x1359d00: r1 = Function '<anonymous closure>':.
    //     0x1359d00: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e760] AnonymousClosure: (0x1359d30), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x1359d04: ldr             x1, [x1, #0x760]
    // 0x1359d08: r0 = AllocateClosure()
    //     0x1359d08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1359d0c: mov             x1, x0
    // 0x1359d10: ldur            x0, [fp, #-0x28]
    // 0x1359d14: StoreField: r0->field_1b = r1
    //     0x1359d14: stur            w1, [x0, #0x1b]
    // 0x1359d18: LeaveFrame
    //     0x1359d18: mov             SP, fp
    //     0x1359d1c: ldp             fp, lr, [SP], #0x10
    // 0x1359d20: ret
    //     0x1359d20: ret             
  }
  [closure] Null <anonymous closure>(dynamic, AddToBagRequest) {
    // ** addr: 0x1359d30, size: 0x58
    // 0x1359d30: EnterFrame
    //     0x1359d30: stp             fp, lr, [SP, #-0x10]!
    //     0x1359d34: mov             fp, SP
    // 0x1359d38: ldr             x0, [fp, #0x18]
    // 0x1359d3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1359d3c: ldur            w1, [x0, #0x17]
    // 0x1359d40: DecompressPointer r1
    //     0x1359d40: add             x1, x1, HEAP, lsl #32
    // 0x1359d44: CheckStackOverflow
    //     0x1359d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1359d48: cmp             SP, x16
    //     0x1359d4c: b.ls            #0x1359d80
    // 0x1359d50: LoadField: r0 = r1->field_b
    //     0x1359d50: ldur            w0, [x1, #0xb]
    // 0x1359d54: DecompressPointer r0
    //     0x1359d54: add             x0, x0, HEAP, lsl #32
    // 0x1359d58: LoadField: r1 = r0->field_f
    //     0x1359d58: ldur            w1, [x0, #0xf]
    // 0x1359d5c: DecompressPointer r1
    //     0x1359d5c: add             x1, x1, HEAP, lsl #32
    // 0x1359d60: r0 = controller()
    //     0x1359d60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359d64: mov             x1, x0
    // 0x1359d68: ldr             x2, [fp, #0x10]
    // 0x1359d6c: r0 = addToBag()
    //     0x1359d6c: bl              #0x13597f4  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::addToBag
    // 0x1359d70: r0 = Null
    //     0x1359d70: mov             x0, NULL
    // 0x1359d74: LeaveFrame
    //     0x1359d74: mov             SP, fp
    //     0x1359d78: ldp             fp, lr, [SP], #0x10
    // 0x1359d7c: ret
    //     0x1359d7c: ret             
    // 0x1359d80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1359d80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1359d84: b               #0x1359d50
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, int) {
    // ** addr: 0x1359d88, size: 0xac
    // 0x1359d88: EnterFrame
    //     0x1359d88: stp             fp, lr, [SP, #-0x10]!
    //     0x1359d8c: mov             fp, SP
    // 0x1359d90: AllocStack(0x10)
    //     0x1359d90: sub             SP, SP, #0x10
    // 0x1359d94: SetupParameters()
    //     0x1359d94: ldr             x0, [fp, #0x38]
    //     0x1359d98: ldur            w1, [x0, #0x17]
    //     0x1359d9c: add             x1, x1, HEAP, lsl #32
    //     0x1359da0: stur            x1, [fp, #-8]
    // 0x1359da4: CheckStackOverflow
    //     0x1359da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1359da8: cmp             SP, x16
    //     0x1359dac: b.ls            #0x1359e2c
    // 0x1359db0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1359db0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1359db4: ldr             x0, [x0, #0x1c80]
    //     0x1359db8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1359dbc: cmp             w0, w16
    //     0x1359dc0: b.ne            #0x1359dcc
    //     0x1359dc4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1359dc8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1359dcc: str             NULL, [SP]
    // 0x1359dd0: r4 = const [0x1, 0, 0, 0, null]
    //     0x1359dd0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x1359dd4: r0 = GetNavigation.back()
    //     0x1359dd4: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1359dd8: ldur            x0, [fp, #-8]
    // 0x1359ddc: LoadField: r1 = r0->field_b
    //     0x1359ddc: ldur            w1, [x0, #0xb]
    // 0x1359de0: DecompressPointer r1
    //     0x1359de0: add             x1, x1, HEAP, lsl #32
    // 0x1359de4: LoadField: r0 = r1->field_f
    //     0x1359de4: ldur            w0, [x1, #0xf]
    // 0x1359de8: DecompressPointer r0
    //     0x1359de8: add             x0, x0, HEAP, lsl #32
    // 0x1359dec: mov             x1, x0
    // 0x1359df0: r0 = controller()
    //     0x1359df0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1359df4: mov             x1, x0
    // 0x1359df8: ldr             x0, [fp, #0x10]
    // 0x1359dfc: r6 = LoadInt32Instr(r0)
    //     0x1359dfc: sbfx            x6, x0, #1, #0x1f
    //     0x1359e00: tbz             w0, #0, #0x1359e08
    //     0x1359e04: ldur            x6, [x0, #7]
    // 0x1359e08: ldr             x2, [fp, #0x18]
    // 0x1359e0c: ldr             x3, [fp, #0x30]
    // 0x1359e10: ldr             x5, [fp, #0x28]
    // 0x1359e14: ldr             x7, [fp, #0x20]
    // 0x1359e18: r0 = customisedResponse()
    //     0x1359e18: bl              #0x1359e34  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::customisedResponse
    // 0x1359e1c: r0 = Null
    //     0x1359e1c: mov             x0, NULL
    // 0x1359e20: LeaveFrame
    //     0x1359e20: mov             SP, fp
    //     0x1359e24: ldp             fp, lr, [SP], #0x10
    // 0x1359e28: ret
    //     0x1359e28: ret             
    // 0x1359e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1359e2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1359e30: b               #0x1359db0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x135a3e8, size: 0xfc
    // 0x135a3e8: EnterFrame
    //     0x135a3e8: stp             fp, lr, [SP, #-0x10]!
    //     0x135a3ec: mov             fp, SP
    // 0x135a3f0: AllocStack(0x8)
    //     0x135a3f0: sub             SP, SP, #8
    // 0x135a3f4: SetupParameters()
    //     0x135a3f4: ldr             x0, [fp, #0x10]
    //     0x135a3f8: ldur            w2, [x0, #0x17]
    //     0x135a3fc: add             x2, x2, HEAP, lsl #32
    //     0x135a400: stur            x2, [fp, #-8]
    // 0x135a404: CheckStackOverflow
    //     0x135a404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135a408: cmp             SP, x16
    //     0x135a40c: b.ls            #0x135a4dc
    // 0x135a410: LoadField: r1 = r2->field_f
    //     0x135a410: ldur            w1, [x2, #0xf]
    // 0x135a414: DecompressPointer r1
    //     0x135a414: add             x1, x1, HEAP, lsl #32
    // 0x135a418: r0 = controller()
    //     0x135a418: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a41c: mov             x1, x0
    // 0x135a420: r2 = "return_exchange_remove_from_bag"
    //     0x135a420: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e790] "return_exchange_remove_from_bag"
    //     0x135a424: ldr             x2, [x2, #0x790]
    // 0x135a428: r3 = ""
    //     0x135a428: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a42c: r0 = ctaExchangeCheckoutInitiatedPostEvent()
    //     0x135a42c: bl              #0x12c2c8c  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::ctaExchangeCheckoutInitiatedPostEvent
    // 0x135a430: ldur            x0, [fp, #-8]
    // 0x135a434: LoadField: r1 = r0->field_f
    //     0x135a434: ldur            w1, [x0, #0xf]
    // 0x135a438: DecompressPointer r1
    //     0x135a438: add             x1, x1, HEAP, lsl #32
    // 0x135a43c: r0 = controller()
    //     0x135a43c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a440: r2 = ""
    //     0x135a440: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a444: StoreField: r0->field_83 = r2
    //     0x135a444: stur            w2, [x0, #0x83]
    // 0x135a448: ldur            x0, [fp, #-8]
    // 0x135a44c: LoadField: r1 = r0->field_f
    //     0x135a44c: ldur            w1, [x0, #0xf]
    // 0x135a450: DecompressPointer r1
    //     0x135a450: add             x1, x1, HEAP, lsl #32
    // 0x135a454: r0 = controller()
    //     0x135a454: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a458: r2 = ""
    //     0x135a458: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a45c: StoreField: r0->field_6b = r2
    //     0x135a45c: stur            w2, [x0, #0x6b]
    // 0x135a460: ldur            x0, [fp, #-8]
    // 0x135a464: LoadField: r1 = r0->field_f
    //     0x135a464: ldur            w1, [x0, #0xf]
    // 0x135a468: DecompressPointer r1
    //     0x135a468: add             x1, x1, HEAP, lsl #32
    // 0x135a46c: r0 = controller()
    //     0x135a46c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a470: LoadField: r1 = r0->field_6f
    //     0x135a470: ldur            w1, [x0, #0x6f]
    // 0x135a474: DecompressPointer r1
    //     0x135a474: add             x1, x1, HEAP, lsl #32
    // 0x135a478: r0 = value()
    //     0x135a478: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135a47c: LoadField: r1 = r0->field_b
    //     0x135a47c: ldur            w1, [x0, #0xb]
    // 0x135a480: DecompressPointer r1
    //     0x135a480: add             x1, x1, HEAP, lsl #32
    // 0x135a484: cmp             w1, NULL
    // 0x135a488: b.ne            #0x135a494
    // 0x135a48c: r2 = ""
    //     0x135a48c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a490: b               #0x135a49c
    // 0x135a494: r2 = ""
    //     0x135a494: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a498: StoreField: r1->field_7 = r2
    //     0x135a498: stur            w2, [x1, #7]
    // 0x135a49c: ldur            x0, [fp, #-8]
    // 0x135a4a0: LoadField: r1 = r0->field_f
    //     0x135a4a0: ldur            w1, [x0, #0xf]
    // 0x135a4a4: DecompressPointer r1
    //     0x135a4a4: add             x1, x1, HEAP, lsl #32
    // 0x135a4a8: r0 = controller()
    //     0x135a4a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a4ac: ldur            x0, [fp, #-8]
    // 0x135a4b0: LoadField: r1 = r0->field_f
    //     0x135a4b0: ldur            w1, [x0, #0xf]
    // 0x135a4b4: DecompressPointer r1
    //     0x135a4b4: add             x1, x1, HEAP, lsl #32
    // 0x135a4b8: r0 = controller()
    //     0x135a4b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a4bc: mov             x1, x0
    // 0x135a4c0: r2 = ""
    //     0x135a4c0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a4c4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x135a4c4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x135a4c8: r0 = getBag()
    //     0x135a4c8: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x135a4cc: r0 = Null
    //     0x135a4cc: mov             x0, NULL
    // 0x135a4d0: LeaveFrame
    //     0x135a4d0: mov             SP, fp
    //     0x135a4d4: ldp             fp, lr, [SP], #0x10
    // 0x135a4d8: ret
    //     0x135a4d8: ret             
    // 0x135a4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135a4dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135a4e0: b               #0x135a410
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0x135a4e4, size: 0x90
    // 0x135a4e4: EnterFrame
    //     0x135a4e4: stp             fp, lr, [SP, #-0x10]!
    //     0x135a4e8: mov             fp, SP
    // 0x135a4ec: AllocStack(0x8)
    //     0x135a4ec: sub             SP, SP, #8
    // 0x135a4f0: SetupParameters()
    //     0x135a4f0: ldr             x0, [fp, #0x18]
    //     0x135a4f4: ldur            w2, [x0, #0x17]
    //     0x135a4f8: add             x2, x2, HEAP, lsl #32
    //     0x135a4fc: stur            x2, [fp, #-8]
    // 0x135a500: CheckStackOverflow
    //     0x135a500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135a504: cmp             SP, x16
    //     0x135a508: b.ls            #0x135a56c
    // 0x135a50c: LoadField: r1 = r2->field_f
    //     0x135a50c: ldur            w1, [x2, #0xf]
    // 0x135a510: DecompressPointer r1
    //     0x135a510: add             x1, x1, HEAP, lsl #32
    // 0x135a514: r0 = controller()
    //     0x135a514: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a518: mov             x1, x0
    // 0x135a51c: ldr             x0, [fp, #0x10]
    // 0x135a520: StoreField: r1->field_6b = r0
    //     0x135a520: stur            w0, [x1, #0x6b]
    //     0x135a524: ldurb           w16, [x1, #-1]
    //     0x135a528: ldurb           w17, [x0, #-1]
    //     0x135a52c: and             x16, x17, x16, lsr #2
    //     0x135a530: tst             x16, HEAP, lsr #32
    //     0x135a534: b.eq            #0x135a53c
    //     0x135a538: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x135a53c: ldur            x0, [fp, #-8]
    // 0x135a540: LoadField: r1 = r0->field_f
    //     0x135a540: ldur            w1, [x0, #0xf]
    // 0x135a544: DecompressPointer r1
    //     0x135a544: add             x1, x1, HEAP, lsl #32
    // 0x135a548: r0 = controller()
    //     0x135a548: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a54c: mov             x1, x0
    // 0x135a550: r2 = ""
    //     0x135a550: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135a554: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x135a554: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x135a558: r0 = getBag()
    //     0x135a558: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x135a55c: r0 = Null
    //     0x135a55c: mov             x0, NULL
    // 0x135a560: LeaveFrame
    //     0x135a560: mov             SP, fp
    //     0x135a564: ldp             fp, lr, [SP], #0x10
    // 0x135a568: ret
    //     0x135a568: ret             
    // 0x135a56c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135a56c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135a570: b               #0x135a50c
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String) {
    // ** addr: 0x135a574, size: 0xb8
    // 0x135a574: EnterFrame
    //     0x135a574: stp             fp, lr, [SP, #-0x10]!
    //     0x135a578: mov             fp, SP
    // 0x135a57c: AllocStack(0x38)
    //     0x135a57c: sub             SP, SP, #0x38
    // 0x135a580: SetupParameters()
    //     0x135a580: ldr             x0, [fp, #0x30]
    //     0x135a584: ldur            w1, [x0, #0x17]
    //     0x135a588: add             x1, x1, HEAP, lsl #32
    //     0x135a58c: stur            x1, [fp, #-8]
    // 0x135a590: CheckStackOverflow
    //     0x135a590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135a594: cmp             SP, x16
    //     0x135a598: b.ls            #0x135a624
    // 0x135a59c: r1 = 4
    //     0x135a59c: movz            x1, #0x4
    // 0x135a5a0: r0 = AllocateContext()
    //     0x135a5a0: bl              #0x16f6108  ; AllocateContextStub
    // 0x135a5a4: mov             x1, x0
    // 0x135a5a8: ldur            x0, [fp, #-8]
    // 0x135a5ac: StoreField: r1->field_b = r0
    //     0x135a5ac: stur            w0, [x1, #0xb]
    // 0x135a5b0: ldr             x2, [fp, #0x28]
    // 0x135a5b4: StoreField: r1->field_f = r2
    //     0x135a5b4: stur            w2, [x1, #0xf]
    // 0x135a5b8: ldr             x2, [fp, #0x20]
    // 0x135a5bc: StoreField: r1->field_13 = r2
    //     0x135a5bc: stur            w2, [x1, #0x13]
    // 0x135a5c0: ldr             x2, [fp, #0x18]
    // 0x135a5c4: ArrayStore: r1[0] = r2  ; List_4
    //     0x135a5c4: stur            w2, [x1, #0x17]
    // 0x135a5c8: ldr             x2, [fp, #0x10]
    // 0x135a5cc: StoreField: r1->field_1b = r2
    //     0x135a5cc: stur            w2, [x1, #0x1b]
    // 0x135a5d0: LoadField: r3 = r0->field_13
    //     0x135a5d0: ldur            w3, [x0, #0x13]
    // 0x135a5d4: DecompressPointer r3
    //     0x135a5d4: add             x3, x3, HEAP, lsl #32
    // 0x135a5d8: mov             x2, x1
    // 0x135a5dc: stur            x3, [fp, #-0x10]
    // 0x135a5e0: r1 = Function '<anonymous closure>':.
    //     0x135a5e0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e7b8] AnonymousClosure: (0x135a62c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x135a5e4: ldr             x1, [x1, #0x7b8]
    // 0x135a5e8: r0 = AllocateClosure()
    //     0x135a5e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135a5ec: stp             x0, NULL, [SP, #0x18]
    // 0x135a5f0: ldur            x16, [fp, #-0x10]
    // 0x135a5f4: r30 = Instance_RoundedRectangleBorder
    //     0x135a5f4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x135a5f8: ldr             lr, [lr, #0xd68]
    // 0x135a5fc: stp             lr, x16, [SP, #8]
    // 0x135a600: r16 = true
    //     0x135a600: add             x16, NULL, #0x20  ; true
    // 0x135a604: str             x16, [SP]
    // 0x135a608: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x135a608: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x135a60c: ldr             x4, [x4, #0xd70]
    // 0x135a610: r0 = showModalBottomSheet()
    //     0x135a610: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x135a614: r0 = Null
    //     0x135a614: mov             x0, NULL
    // 0x135a618: LeaveFrame
    //     0x135a618: mov             SP, fp
    //     0x135a61c: ldp             fp, lr, [SP], #0x10
    // 0x135a620: ret
    //     0x135a620: ret             
    // 0x135a624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135a624: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135a628: b               #0x135a59c
  }
  [closure] CancelOrderConfirmBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x135a62c, size: 0x1e8
    // 0x135a62c: EnterFrame
    //     0x135a62c: stp             fp, lr, [SP, #-0x10]!
    //     0x135a630: mov             fp, SP
    // 0x135a634: AllocStack(0x38)
    //     0x135a634: sub             SP, SP, #0x38
    // 0x135a638: SetupParameters()
    //     0x135a638: ldr             x0, [fp, #0x18]
    //     0x135a63c: ldur            w2, [x0, #0x17]
    //     0x135a640: add             x2, x2, HEAP, lsl #32
    //     0x135a644: stur            x2, [fp, #-8]
    // 0x135a648: CheckStackOverflow
    //     0x135a648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135a64c: cmp             SP, x16
    //     0x135a650: b.ls            #0x135a80c
    // 0x135a654: ldr             x1, [fp, #0x10]
    // 0x135a658: r0 = of()
    //     0x135a658: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135a65c: LoadField: r1 = r0->field_87
    //     0x135a65c: ldur            w1, [x0, #0x87]
    // 0x135a660: DecompressPointer r1
    //     0x135a660: add             x1, x1, HEAP, lsl #32
    // 0x135a664: LoadField: r0 = r1->field_2b
    //     0x135a664: ldur            w0, [x1, #0x2b]
    // 0x135a668: DecompressPointer r0
    //     0x135a668: add             x0, x0, HEAP, lsl #32
    // 0x135a66c: stur            x0, [fp, #-0x10]
    // 0x135a670: r1 = Instance_Color
    //     0x135a670: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x135a674: d0 = 0.400000
    //     0x135a674: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x135a678: r0 = withOpacity()
    //     0x135a678: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x135a67c: r16 = 14.000000
    //     0x135a67c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x135a680: ldr             x16, [x16, #0x1d8]
    // 0x135a684: stp             x0, x16, [SP]
    // 0x135a688: ldur            x1, [fp, #-0x10]
    // 0x135a68c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x135a68c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x135a690: ldr             x4, [x4, #0xaa0]
    // 0x135a694: r0 = copyWith()
    //     0x135a694: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x135a698: stur            x0, [fp, #-0x10]
    // 0x135a69c: r0 = Radius()
    //     0x135a69c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x135a6a0: d0 = 4.000000
    //     0x135a6a0: fmov            d0, #4.00000000
    // 0x135a6a4: stur            x0, [fp, #-0x18]
    // 0x135a6a8: StoreField: r0->field_7 = d0
    //     0x135a6a8: stur            d0, [x0, #7]
    // 0x135a6ac: StoreField: r0->field_f = d0
    //     0x135a6ac: stur            d0, [x0, #0xf]
    // 0x135a6b0: r0 = BorderRadius()
    //     0x135a6b0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x135a6b4: mov             x1, x0
    // 0x135a6b8: ldur            x0, [fp, #-0x18]
    // 0x135a6bc: stur            x1, [fp, #-0x20]
    // 0x135a6c0: StoreField: r1->field_7 = r0
    //     0x135a6c0: stur            w0, [x1, #7]
    // 0x135a6c4: StoreField: r1->field_b = r0
    //     0x135a6c4: stur            w0, [x1, #0xb]
    // 0x135a6c8: StoreField: r1->field_f = r0
    //     0x135a6c8: stur            w0, [x1, #0xf]
    // 0x135a6cc: StoreField: r1->field_13 = r0
    //     0x135a6cc: stur            w0, [x1, #0x13]
    // 0x135a6d0: r0 = OutlineInputBorder()
    //     0x135a6d0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x135a6d4: mov             x1, x0
    // 0x135a6d8: ldur            x0, [fp, #-0x20]
    // 0x135a6dc: stur            x1, [fp, #-0x18]
    // 0x135a6e0: StoreField: r1->field_13 = r0
    //     0x135a6e0: stur            w0, [x1, #0x13]
    // 0x135a6e4: d0 = 4.000000
    //     0x135a6e4: fmov            d0, #4.00000000
    // 0x135a6e8: StoreField: r1->field_b = d0
    //     0x135a6e8: stur            d0, [x1, #0xb]
    // 0x135a6ec: r0 = Instance_BorderSide
    //     0x135a6ec: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x135a6f0: ldr             x0, [x0, #0x118]
    // 0x135a6f4: StoreField: r1->field_7 = r0
    //     0x135a6f4: stur            w0, [x1, #7]
    // 0x135a6f8: r0 = Radius()
    //     0x135a6f8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x135a6fc: d0 = 4.000000
    //     0x135a6fc: fmov            d0, #4.00000000
    // 0x135a700: stur            x0, [fp, #-0x20]
    // 0x135a704: StoreField: r0->field_7 = d0
    //     0x135a704: stur            d0, [x0, #7]
    // 0x135a708: StoreField: r0->field_f = d0
    //     0x135a708: stur            d0, [x0, #0xf]
    // 0x135a70c: r0 = BorderRadius()
    //     0x135a70c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x135a710: mov             x1, x0
    // 0x135a714: ldur            x0, [fp, #-0x20]
    // 0x135a718: stur            x1, [fp, #-0x28]
    // 0x135a71c: StoreField: r1->field_7 = r0
    //     0x135a71c: stur            w0, [x1, #7]
    // 0x135a720: StoreField: r1->field_b = r0
    //     0x135a720: stur            w0, [x1, #0xb]
    // 0x135a724: StoreField: r1->field_f = r0
    //     0x135a724: stur            w0, [x1, #0xf]
    // 0x135a728: StoreField: r1->field_13 = r0
    //     0x135a728: stur            w0, [x1, #0x13]
    // 0x135a72c: r0 = OutlineInputBorder()
    //     0x135a72c: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x135a730: mov             x1, x0
    // 0x135a734: ldur            x0, [fp, #-0x28]
    // 0x135a738: stur            x1, [fp, #-0x20]
    // 0x135a73c: StoreField: r1->field_13 = r0
    //     0x135a73c: stur            w0, [x1, #0x13]
    // 0x135a740: d0 = 4.000000
    //     0x135a740: fmov            d0, #4.00000000
    // 0x135a744: StoreField: r1->field_b = d0
    //     0x135a744: stur            d0, [x1, #0xb]
    // 0x135a748: r0 = Instance_BorderSide
    //     0x135a748: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x135a74c: ldr             x0, [x0, #0x118]
    // 0x135a750: StoreField: r1->field_7 = r0
    //     0x135a750: stur            w0, [x1, #7]
    // 0x135a754: r0 = InputDecoration()
    //     0x135a754: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0x135a758: mov             x1, x0
    // 0x135a75c: r0 = "Mention the reason"
    //     0x135a75c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36120] "Mention the reason"
    //     0x135a760: ldr             x0, [x0, #0x120]
    // 0x135a764: stur            x1, [fp, #-0x28]
    // 0x135a768: StoreField: r1->field_13 = r0
    //     0x135a768: stur            w0, [x1, #0x13]
    // 0x135a76c: ldur            x0, [fp, #-0x10]
    // 0x135a770: ArrayStore: r1[0] = r0  ; List_4
    //     0x135a770: stur            w0, [x1, #0x17]
    // 0x135a774: r0 = true
    //     0x135a774: add             x0, NULL, #0x20  ; true
    // 0x135a778: StoreField: r1->field_47 = r0
    //     0x135a778: stur            w0, [x1, #0x47]
    // 0x135a77c: StoreField: r1->field_4b = r0
    //     0x135a77c: stur            w0, [x1, #0x4b]
    // 0x135a780: ldur            x2, [fp, #-0x20]
    // 0x135a784: StoreField: r1->field_c3 = r2
    //     0x135a784: stur            w2, [x1, #0xc3]
    // 0x135a788: ldur            x2, [fp, #-0x18]
    // 0x135a78c: StoreField: r1->field_cf = r2
    //     0x135a78c: stur            w2, [x1, #0xcf]
    // 0x135a790: StoreField: r1->field_d7 = r0
    //     0x135a790: stur            w0, [x1, #0xd7]
    // 0x135a794: r0 = CancelOrderConfirmBottomSheet()
    //     0x135a794: bl              #0x8b1964  ; AllocateCancelOrderConfirmBottomSheetStub -> CancelOrderConfirmBottomSheet (size=0x34)
    // 0x135a798: mov             x3, x0
    // 0x135a79c: r0 = "Are you sure want to remove this item\?"
    //     0x135a79c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7c0] "Are you sure want to remove this item\?"
    //     0x135a7a0: ldr             x0, [x0, #0x7c0]
    // 0x135a7a4: stur            x3, [fp, #-0x10]
    // 0x135a7a8: StoreField: r3->field_f = r0
    //     0x135a7a8: stur            w0, [x3, #0xf]
    // 0x135a7ac: r0 = "Remove item"
    //     0x135a7ac: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7c8] "Remove item"
    //     0x135a7b0: ldr             x0, [x0, #0x7c8]
    // 0x135a7b4: StoreField: r3->field_13 = r0
    //     0x135a7b4: stur            w0, [x3, #0x13]
    // 0x135a7b8: r0 = "Remove"
    //     0x135a7b8: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0x135a7bc: ldr             x0, [x0, #0x7d0]
    // 0x135a7c0: ArrayStore: r3[0] = r0  ; List_4
    //     0x135a7c0: stur            w0, [x3, #0x17]
    // 0x135a7c4: ldur            x2, [fp, #-8]
    // 0x135a7c8: r1 = Function '<anonymous closure>':.
    //     0x135a7c8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e7d8] AnonymousClosure: (0x135a814), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x135a7cc: ldr             x1, [x1, #0x7d8]
    // 0x135a7d0: r0 = AllocateClosure()
    //     0x135a7d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135a7d4: mov             x1, x0
    // 0x135a7d8: ldur            x0, [fp, #-0x10]
    // 0x135a7dc: StoreField: r0->field_1b = r1
    //     0x135a7dc: stur            w1, [x0, #0x1b]
    // 0x135a7e0: r1 = Instance_BorderRadius
    //     0x135a7e0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x135a7e4: ldr             x1, [x1, #0xf70]
    // 0x135a7e8: StoreField: r0->field_1f = r1
    //     0x135a7e8: stur            w1, [x0, #0x1f]
    // 0x135a7ec: ldur            x1, [fp, #-0x28]
    // 0x135a7f0: StoreField: r0->field_23 = r1
    //     0x135a7f0: stur            w1, [x0, #0x23]
    // 0x135a7f4: r1 = "GO BACK"
    //     0x135a7f4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36138] "GO BACK"
    //     0x135a7f8: ldr             x1, [x1, #0x138]
    // 0x135a7fc: StoreField: r0->field_2b = r1
    //     0x135a7fc: stur            w1, [x0, #0x2b]
    // 0x135a800: LeaveFrame
    //     0x135a800: mov             SP, fp
    //     0x135a804: ldp             fp, lr, [SP], #0x10
    // 0x135a808: ret
    //     0x135a808: ret             
    // 0x135a80c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135a80c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135a810: b               #0x135a654
  }
  [closure] Null <anonymous closure>(dynamic, String?, String?) {
    // ** addr: 0x135a814, size: 0x148
    // 0x135a814: EnterFrame
    //     0x135a814: stp             fp, lr, [SP, #-0x10]!
    //     0x135a818: mov             fp, SP
    // 0x135a81c: AllocStack(0x38)
    //     0x135a81c: sub             SP, SP, #0x38
    // 0x135a820: SetupParameters()
    //     0x135a820: ldr             x0, [fp, #0x20]
    //     0x135a824: ldur            w2, [x0, #0x17]
    //     0x135a828: add             x2, x2, HEAP, lsl #32
    //     0x135a82c: stur            x2, [fp, #-0x10]
    // 0x135a830: CheckStackOverflow
    //     0x135a830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135a834: cmp             SP, x16
    //     0x135a838: b.ls            #0x135a954
    // 0x135a83c: LoadField: r0 = r2->field_b
    //     0x135a83c: ldur            w0, [x2, #0xb]
    // 0x135a840: DecompressPointer r0
    //     0x135a840: add             x0, x0, HEAP, lsl #32
    // 0x135a844: stur            x0, [fp, #-8]
    // 0x135a848: LoadField: r1 = r0->field_f
    //     0x135a848: ldur            w1, [x0, #0xf]
    // 0x135a84c: DecompressPointer r1
    //     0x135a84c: add             x1, x1, HEAP, lsl #32
    // 0x135a850: r0 = controller()
    //     0x135a850: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a854: mov             x1, x0
    // 0x135a858: ldur            x0, [fp, #-0x10]
    // 0x135a85c: stur            x1, [fp, #-0x30]
    // 0x135a860: LoadField: r2 = r0->field_f
    //     0x135a860: ldur            w2, [x0, #0xf]
    // 0x135a864: DecompressPointer r2
    //     0x135a864: add             x2, x2, HEAP, lsl #32
    // 0x135a868: stur            x2, [fp, #-0x28]
    // 0x135a86c: LoadField: r3 = r0->field_13
    //     0x135a86c: ldur            w3, [x0, #0x13]
    // 0x135a870: DecompressPointer r3
    //     0x135a870: add             x3, x3, HEAP, lsl #32
    // 0x135a874: stur            x3, [fp, #-0x20]
    // 0x135a878: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x135a878: ldur            w4, [x0, #0x17]
    // 0x135a87c: DecompressPointer r4
    //     0x135a87c: add             x4, x4, HEAP, lsl #32
    // 0x135a880: stur            x4, [fp, #-0x18]
    // 0x135a884: r0 = EventData()
    //     0x135a884: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x135a888: mov             x1, x0
    // 0x135a88c: ldur            x0, [fp, #-0x28]
    // 0x135a890: stur            x1, [fp, #-0x38]
    // 0x135a894: StoreField: r1->field_33 = r0
    //     0x135a894: stur            w0, [x1, #0x33]
    // 0x135a898: ldur            x0, [fp, #-0x20]
    // 0x135a89c: StoreField: r1->field_8f = r0
    //     0x135a89c: stur            w0, [x1, #0x8f]
    // 0x135a8a0: ldur            x0, [fp, #-0x18]
    // 0x135a8a4: StoreField: r1->field_93 = r0
    //     0x135a8a4: stur            w0, [x1, #0x93]
    // 0x135a8a8: r0 = EventsRequest()
    //     0x135a8a8: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x135a8ac: mov             x1, x0
    // 0x135a8b0: r0 = "product_removed"
    //     0x135a8b0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7e0] "product_removed"
    //     0x135a8b4: ldr             x0, [x0, #0x7e0]
    // 0x135a8b8: StoreField: r1->field_7 = r0
    //     0x135a8b8: stur            w0, [x1, #7]
    // 0x135a8bc: ldur            x0, [fp, #-0x38]
    // 0x135a8c0: StoreField: r1->field_b = r0
    //     0x135a8c0: stur            w0, [x1, #0xb]
    // 0x135a8c4: mov             x2, x1
    // 0x135a8c8: ldur            x1, [fp, #-0x30]
    // 0x135a8cc: r0 = postEvents()
    //     0x135a8cc: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x135a8d0: ldur            x0, [fp, #-8]
    // 0x135a8d4: LoadField: r1 = r0->field_f
    //     0x135a8d4: ldur            w1, [x0, #0xf]
    // 0x135a8d8: DecompressPointer r1
    //     0x135a8d8: add             x1, x1, HEAP, lsl #32
    // 0x135a8dc: r0 = controller()
    //     0x135a8dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135a8e0: mov             x1, x0
    // 0x135a8e4: ldur            x0, [fp, #-0x10]
    // 0x135a8e8: stur            x1, [fp, #-0x28]
    // 0x135a8ec: LoadField: r2 = r0->field_1b
    //     0x135a8ec: ldur            w2, [x0, #0x1b]
    // 0x135a8f0: DecompressPointer r2
    //     0x135a8f0: add             x2, x2, HEAP, lsl #32
    // 0x135a8f4: stur            x2, [fp, #-0x20]
    // 0x135a8f8: LoadField: r3 = r0->field_f
    //     0x135a8f8: ldur            w3, [x0, #0xf]
    // 0x135a8fc: DecompressPointer r3
    //     0x135a8fc: add             x3, x3, HEAP, lsl #32
    // 0x135a900: stur            x3, [fp, #-0x18]
    // 0x135a904: LoadField: r4 = r0->field_13
    //     0x135a904: ldur            w4, [x0, #0x13]
    // 0x135a908: DecompressPointer r4
    //     0x135a908: add             x4, x4, HEAP, lsl #32
    // 0x135a90c: stur            x4, [fp, #-8]
    // 0x135a910: r0 = RemoveItemRequest()
    //     0x135a910: bl              #0x8fc238  ; AllocateRemoveItemRequestStub -> RemoveItemRequest (size=0x18)
    // 0x135a914: mov             x1, x0
    // 0x135a918: ldur            x0, [fp, #-0x18]
    // 0x135a91c: StoreField: r1->field_7 = r0
    //     0x135a91c: stur            w0, [x1, #7]
    // 0x135a920: ldur            x0, [fp, #-8]
    // 0x135a924: StoreField: r1->field_b = r0
    //     0x135a924: stur            w0, [x1, #0xb]
    // 0x135a928: r0 = true
    //     0x135a928: add             x0, NULL, #0x20  ; true
    // 0x135a92c: StoreField: r1->field_f = r0
    //     0x135a92c: stur            w0, [x1, #0xf]
    // 0x135a930: ldur            x0, [fp, #-0x20]
    // 0x135a934: StoreField: r1->field_13 = r0
    //     0x135a934: stur            w0, [x1, #0x13]
    // 0x135a938: mov             x2, x1
    // 0x135a93c: ldur            x1, [fp, #-0x28]
    // 0x135a940: r0 = removeItem()
    //     0x135a940: bl              #0x135a95c  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::removeItem
    // 0x135a944: r0 = Null
    //     0x135a944: mov             x0, NULL
    // 0x135a948: LeaveFrame
    //     0x135a948: mov             SP, fp
    //     0x135a94c: ldp             fp, lr, [SP], #0x10
    // 0x135a950: ret
    //     0x135a950: ret             
    // 0x135a954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135a954: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135a958: b               #0x135a83c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x135ac24, size: 0x3e8
    // 0x135ac24: EnterFrame
    //     0x135ac24: stp             fp, lr, [SP, #-0x10]!
    //     0x135ac28: mov             fp, SP
    // 0x135ac2c: AllocStack(0x48)
    //     0x135ac2c: sub             SP, SP, #0x48
    // 0x135ac30: SetupParameters()
    //     0x135ac30: ldr             x0, [fp, #0x10]
    //     0x135ac34: ldur            w2, [x0, #0x17]
    //     0x135ac38: add             x2, x2, HEAP, lsl #32
    //     0x135ac3c: stur            x2, [fp, #-8]
    // 0x135ac40: CheckStackOverflow
    //     0x135ac40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135ac44: cmp             SP, x16
    //     0x135ac48: b.ls            #0x135afec
    // 0x135ac4c: LoadField: r1 = r2->field_f
    //     0x135ac4c: ldur            w1, [x2, #0xf]
    // 0x135ac50: DecompressPointer r1
    //     0x135ac50: add             x1, x1, HEAP, lsl #32
    // 0x135ac54: r0 = controller()
    //     0x135ac54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ac58: mov             x2, x0
    // 0x135ac5c: ldur            x0, [fp, #-8]
    // 0x135ac60: stur            x2, [fp, #-0x10]
    // 0x135ac64: LoadField: r1 = r0->field_f
    //     0x135ac64: ldur            w1, [x0, #0xf]
    // 0x135ac68: DecompressPointer r1
    //     0x135ac68: add             x1, x1, HEAP, lsl #32
    // 0x135ac6c: r0 = controller()
    //     0x135ac6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ac70: LoadField: r1 = r0->field_5b
    //     0x135ac70: ldur            w1, [x0, #0x5b]
    // 0x135ac74: DecompressPointer r1
    //     0x135ac74: add             x1, x1, HEAP, lsl #32
    // 0x135ac78: r0 = value()
    //     0x135ac78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135ac7c: LoadField: r1 = r0->field_b
    //     0x135ac7c: ldur            w1, [x0, #0xb]
    // 0x135ac80: DecompressPointer r1
    //     0x135ac80: add             x1, x1, HEAP, lsl #32
    // 0x135ac84: cmp             w1, NULL
    // 0x135ac88: b.ne            #0x135ac94
    // 0x135ac8c: r0 = Null
    //     0x135ac8c: mov             x0, NULL
    // 0x135ac90: b               #0x135acb8
    // 0x135ac94: LoadField: r0 = r1->field_2f
    //     0x135ac94: ldur            w0, [x1, #0x2f]
    // 0x135ac98: DecompressPointer r0
    //     0x135ac98: add             x0, x0, HEAP, lsl #32
    // 0x135ac9c: cmp             w0, NULL
    // 0x135aca0: b.ne            #0x135acac
    // 0x135aca4: r0 = Null
    //     0x135aca4: mov             x0, NULL
    // 0x135aca8: b               #0x135acb8
    // 0x135acac: LoadField: r1 = r0->field_23
    //     0x135acac: ldur            w1, [x0, #0x23]
    // 0x135acb0: DecompressPointer r1
    //     0x135acb0: add             x1, x1, HEAP, lsl #32
    // 0x135acb4: mov             x0, x1
    // 0x135acb8: cmp             w0, NULL
    // 0x135acbc: b.ne            #0x135acc4
    // 0x135acc0: r0 = ""
    //     0x135acc0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135acc4: ldur            x2, [fp, #-8]
    // 0x135acc8: ldur            x1, [fp, #-0x10]
    // 0x135accc: StoreField: r1->field_67 = r0
    //     0x135accc: stur            w0, [x1, #0x67]
    //     0x135acd0: ldurb           w16, [x1, #-1]
    //     0x135acd4: ldurb           w17, [x0, #-1]
    //     0x135acd8: and             x16, x17, x16, lsr #2
    //     0x135acdc: tst             x16, HEAP, lsr #32
    //     0x135ace0: b.eq            #0x135ace8
    //     0x135ace4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x135ace8: LoadField: r1 = r2->field_f
    //     0x135ace8: ldur            w1, [x2, #0xf]
    // 0x135acec: DecompressPointer r1
    //     0x135acec: add             x1, x1, HEAP, lsl #32
    // 0x135acf0: r0 = controller()
    //     0x135acf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135acf4: mov             x1, x0
    // 0x135acf8: r0 = true
    //     0x135acf8: add             x0, NULL, #0x20  ; true
    // 0x135acfc: StoreField: r1->field_93 = r0
    //     0x135acfc: stur            w0, [x1, #0x93]
    // 0x135ad00: ldur            x0, [fp, #-8]
    // 0x135ad04: LoadField: r1 = r0->field_f
    //     0x135ad04: ldur            w1, [x0, #0xf]
    // 0x135ad08: DecompressPointer r1
    //     0x135ad08: add             x1, x1, HEAP, lsl #32
    // 0x135ad0c: r0 = controller()
    //     0x135ad0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ad10: mov             x1, x0
    // 0x135ad14: r0 = false
    //     0x135ad14: add             x0, NULL, #0x30  ; false
    // 0x135ad18: StoreField: r1->field_8f = r0
    //     0x135ad18: stur            w0, [x1, #0x8f]
    // 0x135ad1c: ldur            x0, [fp, #-8]
    // 0x135ad20: LoadField: r1 = r0->field_f
    //     0x135ad20: ldur            w1, [x0, #0xf]
    // 0x135ad24: DecompressPointer r1
    //     0x135ad24: add             x1, x1, HEAP, lsl #32
    // 0x135ad28: r0 = controller()
    //     0x135ad28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ad2c: mov             x2, x0
    // 0x135ad30: ldur            x0, [fp, #-8]
    // 0x135ad34: stur            x2, [fp, #-0x10]
    // 0x135ad38: LoadField: r1 = r0->field_f
    //     0x135ad38: ldur            w1, [x0, #0xf]
    // 0x135ad3c: DecompressPointer r1
    //     0x135ad3c: add             x1, x1, HEAP, lsl #32
    // 0x135ad40: r0 = controller()
    //     0x135ad40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ad44: LoadField: r2 = r0->field_67
    //     0x135ad44: ldur            w2, [x0, #0x67]
    // 0x135ad48: DecompressPointer r2
    //     0x135ad48: add             x2, x2, HEAP, lsl #32
    // 0x135ad4c: ldur            x1, [fp, #-0x10]
    // 0x135ad50: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x135ad50: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x135ad54: r0 = getBag()
    //     0x135ad54: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x135ad58: ldur            x0, [fp, #-8]
    // 0x135ad5c: LoadField: r1 = r0->field_f
    //     0x135ad5c: ldur            w1, [x0, #0xf]
    // 0x135ad60: DecompressPointer r1
    //     0x135ad60: add             x1, x1, HEAP, lsl #32
    // 0x135ad64: r0 = controller()
    //     0x135ad64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ad68: mov             x2, x0
    // 0x135ad6c: ldur            x0, [fp, #-8]
    // 0x135ad70: stur            x2, [fp, #-0x10]
    // 0x135ad74: LoadField: r1 = r0->field_f
    //     0x135ad74: ldur            w1, [x0, #0xf]
    // 0x135ad78: DecompressPointer r1
    //     0x135ad78: add             x1, x1, HEAP, lsl #32
    // 0x135ad7c: r0 = controller()
    //     0x135ad7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ad80: LoadField: r1 = r0->field_5b
    //     0x135ad80: ldur            w1, [x0, #0x5b]
    // 0x135ad84: DecompressPointer r1
    //     0x135ad84: add             x1, x1, HEAP, lsl #32
    // 0x135ad88: r0 = value()
    //     0x135ad88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135ad8c: LoadField: r1 = r0->field_b
    //     0x135ad8c: ldur            w1, [x0, #0xb]
    // 0x135ad90: DecompressPointer r1
    //     0x135ad90: add             x1, x1, HEAP, lsl #32
    // 0x135ad94: cmp             w1, NULL
    // 0x135ad98: b.ne            #0x135ada4
    // 0x135ad9c: r0 = Null
    //     0x135ad9c: mov             x0, NULL
    // 0x135ada0: b               #0x135adc4
    // 0x135ada4: LoadField: r0 = r1->field_7
    //     0x135ada4: ldur            w0, [x1, #7]
    // 0x135ada8: DecompressPointer r0
    //     0x135ada8: add             x0, x0, HEAP, lsl #32
    // 0x135adac: cmp             w0, NULL
    // 0x135adb0: b.ne            #0x135adbc
    // 0x135adb4: r0 = Null
    //     0x135adb4: mov             x0, NULL
    // 0x135adb8: b               #0x135adc4
    // 0x135adbc: stp             x0, NULL, [SP]
    // 0x135adc0: r0 = _Double.fromInteger()
    //     0x135adc0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x135adc4: cmp             w0, NULL
    // 0x135adc8: b.ne            #0x135add4
    // 0x135adcc: d0 = 0.000000
    //     0x135adcc: eor             v0.16b, v0.16b, v0.16b
    // 0x135add0: b               #0x135add8
    // 0x135add4: LoadField: d0 = r0->field_7
    //     0x135add4: ldur            d0, [x0, #7]
    // 0x135add8: ldur            x0, [fp, #-8]
    // 0x135addc: stur            d0, [fp, #-0x30]
    // 0x135ade0: LoadField: r1 = r0->field_f
    //     0x135ade0: ldur            w1, [x0, #0xf]
    // 0x135ade4: DecompressPointer r1
    //     0x135ade4: add             x1, x1, HEAP, lsl #32
    // 0x135ade8: r0 = controller()
    //     0x135ade8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135adec: LoadField: r1 = r0->field_5b
    //     0x135adec: ldur            w1, [x0, #0x5b]
    // 0x135adf0: DecompressPointer r1
    //     0x135adf0: add             x1, x1, HEAP, lsl #32
    // 0x135adf4: r0 = value()
    //     0x135adf4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135adf8: LoadField: r1 = r0->field_b
    //     0x135adf8: ldur            w1, [x0, #0xb]
    // 0x135adfc: DecompressPointer r1
    //     0x135adfc: add             x1, x1, HEAP, lsl #32
    // 0x135ae00: cmp             w1, NULL
    // 0x135ae04: b.ne            #0x135ae10
    // 0x135ae08: r0 = Null
    //     0x135ae08: mov             x0, NULL
    // 0x135ae0c: b               #0x135ae18
    // 0x135ae10: LoadField: r0 = r1->field_b
    //     0x135ae10: ldur            w0, [x1, #0xb]
    // 0x135ae14: DecompressPointer r0
    //     0x135ae14: add             x0, x0, HEAP, lsl #32
    // 0x135ae18: cmp             w0, NULL
    // 0x135ae1c: b.ne            #0x135ae28
    // 0x135ae20: r2 = 0
    //     0x135ae20: movz            x2, #0
    // 0x135ae24: b               #0x135ae38
    // 0x135ae28: r1 = LoadInt32Instr(r0)
    //     0x135ae28: sbfx            x1, x0, #1, #0x1f
    //     0x135ae2c: tbz             w0, #0, #0x135ae34
    //     0x135ae30: ldur            x1, [x0, #7]
    // 0x135ae34: mov             x2, x1
    // 0x135ae38: ldur            x0, [fp, #-8]
    // 0x135ae3c: stur            x2, [fp, #-0x18]
    // 0x135ae40: LoadField: r1 = r0->field_f
    //     0x135ae40: ldur            w1, [x0, #0xf]
    // 0x135ae44: DecompressPointer r1
    //     0x135ae44: add             x1, x1, HEAP, lsl #32
    // 0x135ae48: r0 = controller()
    //     0x135ae48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135ae4c: LoadField: r1 = r0->field_5b
    //     0x135ae4c: ldur            w1, [x0, #0x5b]
    // 0x135ae50: DecompressPointer r1
    //     0x135ae50: add             x1, x1, HEAP, lsl #32
    // 0x135ae54: r0 = value()
    //     0x135ae54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135ae58: LoadField: r1 = r0->field_b
    //     0x135ae58: ldur            w1, [x0, #0xb]
    // 0x135ae5c: DecompressPointer r1
    //     0x135ae5c: add             x1, x1, HEAP, lsl #32
    // 0x135ae60: cmp             w1, NULL
    // 0x135ae64: b.ne            #0x135ae70
    // 0x135ae68: r2 = Null
    //     0x135ae68: mov             x2, NULL
    // 0x135ae6c: b               #0x135ae98
    // 0x135ae70: LoadField: r0 = r1->field_2f
    //     0x135ae70: ldur            w0, [x1, #0x2f]
    // 0x135ae74: DecompressPointer r0
    //     0x135ae74: add             x0, x0, HEAP, lsl #32
    // 0x135ae78: cmp             w0, NULL
    // 0x135ae7c: b.ne            #0x135ae88
    // 0x135ae80: r0 = Null
    //     0x135ae80: mov             x0, NULL
    // 0x135ae84: b               #0x135ae94
    // 0x135ae88: LoadField: r1 = r0->field_27
    //     0x135ae88: ldur            w1, [x0, #0x27]
    // 0x135ae8c: DecompressPointer r1
    //     0x135ae8c: add             x1, x1, HEAP, lsl #32
    // 0x135ae90: mov             x0, x1
    // 0x135ae94: mov             x2, x0
    // 0x135ae98: ldur            x0, [fp, #-8]
    // 0x135ae9c: stur            x2, [fp, #-0x20]
    // 0x135aea0: LoadField: r1 = r0->field_f
    //     0x135aea0: ldur            w1, [x0, #0xf]
    // 0x135aea4: DecompressPointer r1
    //     0x135aea4: add             x1, x1, HEAP, lsl #32
    // 0x135aea8: r0 = controller()
    //     0x135aea8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135aeac: LoadField: r1 = r0->field_5b
    //     0x135aeac: ldur            w1, [x0, #0x5b]
    // 0x135aeb0: DecompressPointer r1
    //     0x135aeb0: add             x1, x1, HEAP, lsl #32
    // 0x135aeb4: r0 = value()
    //     0x135aeb4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135aeb8: LoadField: r1 = r0->field_b
    //     0x135aeb8: ldur            w1, [x0, #0xb]
    // 0x135aebc: DecompressPointer r1
    //     0x135aebc: add             x1, x1, HEAP, lsl #32
    // 0x135aec0: cmp             w1, NULL
    // 0x135aec4: b.ne            #0x135aed0
    // 0x135aec8: r0 = Null
    //     0x135aec8: mov             x0, NULL
    // 0x135aecc: b               #0x135af18
    // 0x135aed0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x135aed0: ldur            w0, [x1, #0x17]
    // 0x135aed4: DecompressPointer r0
    //     0x135aed4: add             x0, x0, HEAP, lsl #32
    // 0x135aed8: stur            x0, [fp, #-8]
    // 0x135aedc: r1 = Function '<anonymous closure>':.
    //     0x135aedc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e7f8] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x135aee0: ldr             x1, [x1, #0x7f8]
    // 0x135aee4: r2 = Null
    //     0x135aee4: mov             x2, NULL
    // 0x135aee8: r0 = AllocateClosure()
    //     0x135aee8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135aeec: r16 = <String?>
    //     0x135aeec: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x135aef0: ldur            lr, [fp, #-8]
    // 0x135aef4: stp             lr, x16, [SP, #8]
    // 0x135aef8: str             x0, [SP]
    // 0x135aefc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x135aefc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x135af00: r0 = map()
    //     0x135af00: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x135af04: r16 = ", "
    //     0x135af04: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x135af08: str             x16, [SP]
    // 0x135af0c: mov             x1, x0
    // 0x135af10: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x135af10: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x135af14: r0 = join()
    //     0x135af14: bl              #0x784778  ; [dart:_internal] ListIterable::join
    // 0x135af18: cmp             w0, NULL
    // 0x135af1c: b.ne            #0x135af28
    // 0x135af20: r2 = ""
    //     0x135af20: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135af24: b               #0x135af2c
    // 0x135af28: mov             x2, x0
    // 0x135af2c: ldur            d0, [fp, #-0x30]
    // 0x135af30: ldur            x1, [fp, #-0x18]
    // 0x135af34: ldur            x0, [fp, #-0x20]
    // 0x135af38: stur            x2, [fp, #-8]
    // 0x135af3c: r0 = EventData()
    //     0x135af3c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x135af40: mov             x2, x0
    // 0x135af44: r0 = "bag_page"
    //     0x135af44: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] "bag_page"
    //     0x135af48: ldr             x0, [x0, #0x68]
    // 0x135af4c: stur            x2, [fp, #-0x28]
    // 0x135af50: StoreField: r2->field_13 = r0
    //     0x135af50: stur            w0, [x2, #0x13]
    // 0x135af54: ldur            d0, [fp, #-0x30]
    // 0x135af58: r0 = inline_Allocate_Double()
    //     0x135af58: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x135af5c: add             x0, x0, #0x10
    //     0x135af60: cmp             x1, x0
    //     0x135af64: b.ls            #0x135aff4
    //     0x135af68: str             x0, [THR, #0x50]  ; THR::top
    //     0x135af6c: sub             x0, x0, #0xf
    //     0x135af70: movz            x1, #0xe15c
    //     0x135af74: movk            x1, #0x3, lsl #16
    //     0x135af78: stur            x1, [x0, #-1]
    // 0x135af7c: StoreField: r0->field_7 = d0
    //     0x135af7c: stur            d0, [x0, #7]
    // 0x135af80: StoreField: r2->field_3f = r0
    //     0x135af80: stur            w0, [x2, #0x3f]
    // 0x135af84: ldur            x3, [fp, #-0x18]
    // 0x135af88: r0 = BoxInt64Instr(r3)
    //     0x135af88: sbfiz           x0, x3, #1, #0x1f
    //     0x135af8c: cmp             x3, x0, asr #1
    //     0x135af90: b.eq            #0x135af9c
    //     0x135af94: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x135af98: stur            x3, [x0, #7]
    // 0x135af9c: StoreField: r2->field_43 = r0
    //     0x135af9c: stur            w0, [x2, #0x43]
    // 0x135afa0: ldur            x0, [fp, #-8]
    // 0x135afa4: StoreField: r2->field_47 = r0
    //     0x135afa4: stur            w0, [x2, #0x47]
    // 0x135afa8: ldur            x0, [fp, #-0x20]
    // 0x135afac: r17 = 283
    //     0x135afac: movz            x17, #0x11b
    // 0x135afb0: str             w0, [x2, x17]
    // 0x135afb4: r0 = EventsRequest()
    //     0x135afb4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x135afb8: mov             x1, x0
    // 0x135afbc: r0 = "free_gift_removed"
    //     0x135afbc: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e800] "free_gift_removed"
    //     0x135afc0: ldr             x0, [x0, #0x800]
    // 0x135afc4: StoreField: r1->field_7 = r0
    //     0x135afc4: stur            w0, [x1, #7]
    // 0x135afc8: ldur            x0, [fp, #-0x28]
    // 0x135afcc: StoreField: r1->field_b = r0
    //     0x135afcc: stur            w0, [x1, #0xb]
    // 0x135afd0: mov             x2, x1
    // 0x135afd4: ldur            x1, [fp, #-0x10]
    // 0x135afd8: r0 = postEvents()
    //     0x135afd8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x135afdc: r0 = Null
    //     0x135afdc: mov             x0, NULL
    // 0x135afe0: LeaveFrame
    //     0x135afe0: mov             SP, fp
    //     0x135afe4: ldp             fp, lr, [SP], #0x10
    // 0x135afe8: ret
    //     0x135afe8: ret             
    // 0x135afec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135afec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135aff0: b               #0x135ac4c
    // 0x135aff4: SaveReg d0
    //     0x135aff4: str             q0, [SP, #-0x10]!
    // 0x135aff8: SaveReg r2
    //     0x135aff8: str             x2, [SP, #-8]!
    // 0x135affc: r0 = AllocateDouble()
    //     0x135affc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x135b000: RestoreReg r2
    //     0x135b000: ldr             x2, [SP], #8
    // 0x135b004: RestoreReg d0
    //     0x135b004: ldr             q0, [SP], #0x10
    // 0x135b008: b               #0x135af7c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x135b00c, size: 0x3e8
    // 0x135b00c: EnterFrame
    //     0x135b00c: stp             fp, lr, [SP, #-0x10]!
    //     0x135b010: mov             fp, SP
    // 0x135b014: AllocStack(0x48)
    //     0x135b014: sub             SP, SP, #0x48
    // 0x135b018: SetupParameters()
    //     0x135b018: ldr             x0, [fp, #0x10]
    //     0x135b01c: ldur            w2, [x0, #0x17]
    //     0x135b020: add             x2, x2, HEAP, lsl #32
    //     0x135b024: stur            x2, [fp, #-8]
    // 0x135b028: CheckStackOverflow
    //     0x135b028: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x135b02c: cmp             SP, x16
    //     0x135b030: b.ls            #0x135b3d4
    // 0x135b034: LoadField: r1 = r2->field_f
    //     0x135b034: ldur            w1, [x2, #0xf]
    // 0x135b038: DecompressPointer r1
    //     0x135b038: add             x1, x1, HEAP, lsl #32
    // 0x135b03c: r0 = controller()
    //     0x135b03c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b040: mov             x2, x0
    // 0x135b044: ldur            x0, [fp, #-8]
    // 0x135b048: stur            x2, [fp, #-0x10]
    // 0x135b04c: LoadField: r1 = r0->field_f
    //     0x135b04c: ldur            w1, [x0, #0xf]
    // 0x135b050: DecompressPointer r1
    //     0x135b050: add             x1, x1, HEAP, lsl #32
    // 0x135b054: r0 = controller()
    //     0x135b054: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b058: LoadField: r1 = r0->field_5b
    //     0x135b058: ldur            w1, [x0, #0x5b]
    // 0x135b05c: DecompressPointer r1
    //     0x135b05c: add             x1, x1, HEAP, lsl #32
    // 0x135b060: r0 = value()
    //     0x135b060: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135b064: LoadField: r1 = r0->field_b
    //     0x135b064: ldur            w1, [x0, #0xb]
    // 0x135b068: DecompressPointer r1
    //     0x135b068: add             x1, x1, HEAP, lsl #32
    // 0x135b06c: cmp             w1, NULL
    // 0x135b070: b.ne            #0x135b07c
    // 0x135b074: r0 = Null
    //     0x135b074: mov             x0, NULL
    // 0x135b078: b               #0x135b0a0
    // 0x135b07c: LoadField: r0 = r1->field_2f
    //     0x135b07c: ldur            w0, [x1, #0x2f]
    // 0x135b080: DecompressPointer r0
    //     0x135b080: add             x0, x0, HEAP, lsl #32
    // 0x135b084: cmp             w0, NULL
    // 0x135b088: b.ne            #0x135b094
    // 0x135b08c: r0 = Null
    //     0x135b08c: mov             x0, NULL
    // 0x135b090: b               #0x135b0a0
    // 0x135b094: LoadField: r1 = r0->field_23
    //     0x135b094: ldur            w1, [x0, #0x23]
    // 0x135b098: DecompressPointer r1
    //     0x135b098: add             x1, x1, HEAP, lsl #32
    // 0x135b09c: mov             x0, x1
    // 0x135b0a0: cmp             w0, NULL
    // 0x135b0a4: b.ne            #0x135b0ac
    // 0x135b0a8: r0 = ""
    //     0x135b0a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135b0ac: ldur            x2, [fp, #-8]
    // 0x135b0b0: ldur            x1, [fp, #-0x10]
    // 0x135b0b4: StoreField: r1->field_67 = r0
    //     0x135b0b4: stur            w0, [x1, #0x67]
    //     0x135b0b8: ldurb           w16, [x1, #-1]
    //     0x135b0bc: ldurb           w17, [x0, #-1]
    //     0x135b0c0: and             x16, x17, x16, lsr #2
    //     0x135b0c4: tst             x16, HEAP, lsr #32
    //     0x135b0c8: b.eq            #0x135b0d0
    //     0x135b0cc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x135b0d0: LoadField: r1 = r2->field_f
    //     0x135b0d0: ldur            w1, [x2, #0xf]
    // 0x135b0d4: DecompressPointer r1
    //     0x135b0d4: add             x1, x1, HEAP, lsl #32
    // 0x135b0d8: r0 = controller()
    //     0x135b0d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b0dc: mov             x1, x0
    // 0x135b0e0: r0 = true
    //     0x135b0e0: add             x0, NULL, #0x20  ; true
    // 0x135b0e4: StoreField: r1->field_8f = r0
    //     0x135b0e4: stur            w0, [x1, #0x8f]
    // 0x135b0e8: ldur            x0, [fp, #-8]
    // 0x135b0ec: LoadField: r1 = r0->field_f
    //     0x135b0ec: ldur            w1, [x0, #0xf]
    // 0x135b0f0: DecompressPointer r1
    //     0x135b0f0: add             x1, x1, HEAP, lsl #32
    // 0x135b0f4: r0 = controller()
    //     0x135b0f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b0f8: mov             x1, x0
    // 0x135b0fc: r0 = false
    //     0x135b0fc: add             x0, NULL, #0x30  ; false
    // 0x135b100: StoreField: r1->field_93 = r0
    //     0x135b100: stur            w0, [x1, #0x93]
    // 0x135b104: ldur            x0, [fp, #-8]
    // 0x135b108: LoadField: r1 = r0->field_f
    //     0x135b108: ldur            w1, [x0, #0xf]
    // 0x135b10c: DecompressPointer r1
    //     0x135b10c: add             x1, x1, HEAP, lsl #32
    // 0x135b110: r0 = controller()
    //     0x135b110: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b114: mov             x2, x0
    // 0x135b118: ldur            x0, [fp, #-8]
    // 0x135b11c: stur            x2, [fp, #-0x10]
    // 0x135b120: LoadField: r1 = r0->field_f
    //     0x135b120: ldur            w1, [x0, #0xf]
    // 0x135b124: DecompressPointer r1
    //     0x135b124: add             x1, x1, HEAP, lsl #32
    // 0x135b128: r0 = controller()
    //     0x135b128: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b12c: LoadField: r2 = r0->field_67
    //     0x135b12c: ldur            w2, [x0, #0x67]
    // 0x135b130: DecompressPointer r2
    //     0x135b130: add             x2, x2, HEAP, lsl #32
    // 0x135b134: ldur            x1, [fp, #-0x10]
    // 0x135b138: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x135b138: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x135b13c: r0 = getBag()
    //     0x135b13c: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x135b140: ldur            x0, [fp, #-8]
    // 0x135b144: LoadField: r1 = r0->field_f
    //     0x135b144: ldur            w1, [x0, #0xf]
    // 0x135b148: DecompressPointer r1
    //     0x135b148: add             x1, x1, HEAP, lsl #32
    // 0x135b14c: r0 = controller()
    //     0x135b14c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b150: mov             x2, x0
    // 0x135b154: ldur            x0, [fp, #-8]
    // 0x135b158: stur            x2, [fp, #-0x10]
    // 0x135b15c: LoadField: r1 = r0->field_f
    //     0x135b15c: ldur            w1, [x0, #0xf]
    // 0x135b160: DecompressPointer r1
    //     0x135b160: add             x1, x1, HEAP, lsl #32
    // 0x135b164: r0 = controller()
    //     0x135b164: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b168: LoadField: r1 = r0->field_5b
    //     0x135b168: ldur            w1, [x0, #0x5b]
    // 0x135b16c: DecompressPointer r1
    //     0x135b16c: add             x1, x1, HEAP, lsl #32
    // 0x135b170: r0 = value()
    //     0x135b170: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135b174: LoadField: r1 = r0->field_b
    //     0x135b174: ldur            w1, [x0, #0xb]
    // 0x135b178: DecompressPointer r1
    //     0x135b178: add             x1, x1, HEAP, lsl #32
    // 0x135b17c: cmp             w1, NULL
    // 0x135b180: b.ne            #0x135b18c
    // 0x135b184: r0 = Null
    //     0x135b184: mov             x0, NULL
    // 0x135b188: b               #0x135b1ac
    // 0x135b18c: LoadField: r0 = r1->field_7
    //     0x135b18c: ldur            w0, [x1, #7]
    // 0x135b190: DecompressPointer r0
    //     0x135b190: add             x0, x0, HEAP, lsl #32
    // 0x135b194: cmp             w0, NULL
    // 0x135b198: b.ne            #0x135b1a4
    // 0x135b19c: r0 = Null
    //     0x135b19c: mov             x0, NULL
    // 0x135b1a0: b               #0x135b1ac
    // 0x135b1a4: stp             x0, NULL, [SP]
    // 0x135b1a8: r0 = _Double.fromInteger()
    //     0x135b1a8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x135b1ac: cmp             w0, NULL
    // 0x135b1b0: b.ne            #0x135b1bc
    // 0x135b1b4: d0 = 0.000000
    //     0x135b1b4: eor             v0.16b, v0.16b, v0.16b
    // 0x135b1b8: b               #0x135b1c0
    // 0x135b1bc: LoadField: d0 = r0->field_7
    //     0x135b1bc: ldur            d0, [x0, #7]
    // 0x135b1c0: ldur            x0, [fp, #-8]
    // 0x135b1c4: stur            d0, [fp, #-0x30]
    // 0x135b1c8: LoadField: r1 = r0->field_f
    //     0x135b1c8: ldur            w1, [x0, #0xf]
    // 0x135b1cc: DecompressPointer r1
    //     0x135b1cc: add             x1, x1, HEAP, lsl #32
    // 0x135b1d0: r0 = controller()
    //     0x135b1d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b1d4: LoadField: r1 = r0->field_5b
    //     0x135b1d4: ldur            w1, [x0, #0x5b]
    // 0x135b1d8: DecompressPointer r1
    //     0x135b1d8: add             x1, x1, HEAP, lsl #32
    // 0x135b1dc: r0 = value()
    //     0x135b1dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135b1e0: LoadField: r1 = r0->field_b
    //     0x135b1e0: ldur            w1, [x0, #0xb]
    // 0x135b1e4: DecompressPointer r1
    //     0x135b1e4: add             x1, x1, HEAP, lsl #32
    // 0x135b1e8: cmp             w1, NULL
    // 0x135b1ec: b.ne            #0x135b1f8
    // 0x135b1f0: r0 = Null
    //     0x135b1f0: mov             x0, NULL
    // 0x135b1f4: b               #0x135b200
    // 0x135b1f8: LoadField: r0 = r1->field_b
    //     0x135b1f8: ldur            w0, [x1, #0xb]
    // 0x135b1fc: DecompressPointer r0
    //     0x135b1fc: add             x0, x0, HEAP, lsl #32
    // 0x135b200: cmp             w0, NULL
    // 0x135b204: b.ne            #0x135b210
    // 0x135b208: r2 = 0
    //     0x135b208: movz            x2, #0
    // 0x135b20c: b               #0x135b220
    // 0x135b210: r1 = LoadInt32Instr(r0)
    //     0x135b210: sbfx            x1, x0, #1, #0x1f
    //     0x135b214: tbz             w0, #0, #0x135b21c
    //     0x135b218: ldur            x1, [x0, #7]
    // 0x135b21c: mov             x2, x1
    // 0x135b220: ldur            x0, [fp, #-8]
    // 0x135b224: stur            x2, [fp, #-0x18]
    // 0x135b228: LoadField: r1 = r0->field_f
    //     0x135b228: ldur            w1, [x0, #0xf]
    // 0x135b22c: DecompressPointer r1
    //     0x135b22c: add             x1, x1, HEAP, lsl #32
    // 0x135b230: r0 = controller()
    //     0x135b230: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b234: LoadField: r1 = r0->field_5b
    //     0x135b234: ldur            w1, [x0, #0x5b]
    // 0x135b238: DecompressPointer r1
    //     0x135b238: add             x1, x1, HEAP, lsl #32
    // 0x135b23c: r0 = value()
    //     0x135b23c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135b240: LoadField: r1 = r0->field_b
    //     0x135b240: ldur            w1, [x0, #0xb]
    // 0x135b244: DecompressPointer r1
    //     0x135b244: add             x1, x1, HEAP, lsl #32
    // 0x135b248: cmp             w1, NULL
    // 0x135b24c: b.ne            #0x135b258
    // 0x135b250: r2 = Null
    //     0x135b250: mov             x2, NULL
    // 0x135b254: b               #0x135b280
    // 0x135b258: LoadField: r0 = r1->field_2f
    //     0x135b258: ldur            w0, [x1, #0x2f]
    // 0x135b25c: DecompressPointer r0
    //     0x135b25c: add             x0, x0, HEAP, lsl #32
    // 0x135b260: cmp             w0, NULL
    // 0x135b264: b.ne            #0x135b270
    // 0x135b268: r0 = Null
    //     0x135b268: mov             x0, NULL
    // 0x135b26c: b               #0x135b27c
    // 0x135b270: LoadField: r1 = r0->field_27
    //     0x135b270: ldur            w1, [x0, #0x27]
    // 0x135b274: DecompressPointer r1
    //     0x135b274: add             x1, x1, HEAP, lsl #32
    // 0x135b278: mov             x0, x1
    // 0x135b27c: mov             x2, x0
    // 0x135b280: ldur            x0, [fp, #-8]
    // 0x135b284: stur            x2, [fp, #-0x20]
    // 0x135b288: LoadField: r1 = r0->field_f
    //     0x135b288: ldur            w1, [x0, #0xf]
    // 0x135b28c: DecompressPointer r1
    //     0x135b28c: add             x1, x1, HEAP, lsl #32
    // 0x135b290: r0 = controller()
    //     0x135b290: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x135b294: LoadField: r1 = r0->field_5b
    //     0x135b294: ldur            w1, [x0, #0x5b]
    // 0x135b298: DecompressPointer r1
    //     0x135b298: add             x1, x1, HEAP, lsl #32
    // 0x135b29c: r0 = value()
    //     0x135b29c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135b2a0: LoadField: r1 = r0->field_b
    //     0x135b2a0: ldur            w1, [x0, #0xb]
    // 0x135b2a4: DecompressPointer r1
    //     0x135b2a4: add             x1, x1, HEAP, lsl #32
    // 0x135b2a8: cmp             w1, NULL
    // 0x135b2ac: b.ne            #0x135b2b8
    // 0x135b2b0: r0 = Null
    //     0x135b2b0: mov             x0, NULL
    // 0x135b2b4: b               #0x135b300
    // 0x135b2b8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x135b2b8: ldur            w0, [x1, #0x17]
    // 0x135b2bc: DecompressPointer r0
    //     0x135b2bc: add             x0, x0, HEAP, lsl #32
    // 0x135b2c0: stur            x0, [fp, #-8]
    // 0x135b2c4: r1 = Function '<anonymous closure>':.
    //     0x135b2c4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e808] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x135b2c8: ldr             x1, [x1, #0x808]
    // 0x135b2cc: r2 = Null
    //     0x135b2cc: mov             x2, NULL
    // 0x135b2d0: r0 = AllocateClosure()
    //     0x135b2d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135b2d4: r16 = <String?>
    //     0x135b2d4: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x135b2d8: ldur            lr, [fp, #-8]
    // 0x135b2dc: stp             lr, x16, [SP, #8]
    // 0x135b2e0: str             x0, [SP]
    // 0x135b2e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x135b2e4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x135b2e8: r0 = map()
    //     0x135b2e8: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x135b2ec: r16 = ", "
    //     0x135b2ec: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x135b2f0: str             x16, [SP]
    // 0x135b2f4: mov             x1, x0
    // 0x135b2f8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x135b2f8: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x135b2fc: r0 = join()
    //     0x135b2fc: bl              #0x784778  ; [dart:_internal] ListIterable::join
    // 0x135b300: cmp             w0, NULL
    // 0x135b304: b.ne            #0x135b310
    // 0x135b308: r2 = ""
    //     0x135b308: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x135b30c: b               #0x135b314
    // 0x135b310: mov             x2, x0
    // 0x135b314: ldur            d0, [fp, #-0x30]
    // 0x135b318: ldur            x1, [fp, #-0x18]
    // 0x135b31c: ldur            x0, [fp, #-0x20]
    // 0x135b320: stur            x2, [fp, #-8]
    // 0x135b324: r0 = EventData()
    //     0x135b324: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x135b328: mov             x2, x0
    // 0x135b32c: r0 = "bag_page"
    //     0x135b32c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] "bag_page"
    //     0x135b330: ldr             x0, [x0, #0x68]
    // 0x135b334: stur            x2, [fp, #-0x28]
    // 0x135b338: StoreField: r2->field_13 = r0
    //     0x135b338: stur            w0, [x2, #0x13]
    // 0x135b33c: ldur            d0, [fp, #-0x30]
    // 0x135b340: r0 = inline_Allocate_Double()
    //     0x135b340: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x135b344: add             x0, x0, #0x10
    //     0x135b348: cmp             x1, x0
    //     0x135b34c: b.ls            #0x135b3dc
    //     0x135b350: str             x0, [THR, #0x50]  ; THR::top
    //     0x135b354: sub             x0, x0, #0xf
    //     0x135b358: movz            x1, #0xe15c
    //     0x135b35c: movk            x1, #0x3, lsl #16
    //     0x135b360: stur            x1, [x0, #-1]
    // 0x135b364: StoreField: r0->field_7 = d0
    //     0x135b364: stur            d0, [x0, #7]
    // 0x135b368: StoreField: r2->field_3f = r0
    //     0x135b368: stur            w0, [x2, #0x3f]
    // 0x135b36c: ldur            x3, [fp, #-0x18]
    // 0x135b370: r0 = BoxInt64Instr(r3)
    //     0x135b370: sbfiz           x0, x3, #1, #0x1f
    //     0x135b374: cmp             x3, x0, asr #1
    //     0x135b378: b.eq            #0x135b384
    //     0x135b37c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x135b380: stur            x3, [x0, #7]
    // 0x135b384: StoreField: r2->field_43 = r0
    //     0x135b384: stur            w0, [x2, #0x43]
    // 0x135b388: ldur            x0, [fp, #-8]
    // 0x135b38c: StoreField: r2->field_47 = r0
    //     0x135b38c: stur            w0, [x2, #0x47]
    // 0x135b390: ldur            x0, [fp, #-0x20]
    // 0x135b394: r17 = 283
    //     0x135b394: movz            x17, #0x11b
    // 0x135b398: str             w0, [x2, x17]
    // 0x135b39c: r0 = EventsRequest()
    //     0x135b39c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x135b3a0: mov             x1, x0
    // 0x135b3a4: r0 = "free_gift_added"
    //     0x135b3a4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e810] "free_gift_added"
    //     0x135b3a8: ldr             x0, [x0, #0x810]
    // 0x135b3ac: StoreField: r1->field_7 = r0
    //     0x135b3ac: stur            w0, [x1, #7]
    // 0x135b3b0: ldur            x0, [fp, #-0x28]
    // 0x135b3b4: StoreField: r1->field_b = r0
    //     0x135b3b4: stur            w0, [x1, #0xb]
    // 0x135b3b8: mov             x2, x1
    // 0x135b3bc: ldur            x1, [fp, #-0x10]
    // 0x135b3c0: r0 = postEvents()
    //     0x135b3c0: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x135b3c4: r0 = Null
    //     0x135b3c4: mov             x0, NULL
    // 0x135b3c8: LeaveFrame
    //     0x135b3c8: mov             SP, fp
    //     0x135b3cc: ldp             fp, lr, [SP], #0x10
    // 0x135b3d0: ret
    //     0x135b3d0: ret             
    // 0x135b3d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x135b3d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x135b3d8: b               #0x135b034
    // 0x135b3dc: SaveReg d0
    //     0x135b3dc: str             q0, [SP, #-0x10]!
    // 0x135b3e0: SaveReg r2
    //     0x135b3e0: str             x2, [SP, #-8]!
    // 0x135b3e4: r0 = AllocateDouble()
    //     0x135b3e4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x135b3e8: RestoreReg r2
    //     0x135b3e8: ldr             x2, [SP], #8
    // 0x135b3ec: RestoreReg d0
    //     0x135b3ec: ldr             q0, [SP], #0x10
    // 0x135b3f0: b               #0x135b364
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x13659c0, size: 0x58
    // 0x13659c0: EnterFrame
    //     0x13659c0: stp             fp, lr, [SP, #-0x10]!
    //     0x13659c4: mov             fp, SP
    // 0x13659c8: AllocStack(0x10)
    //     0x13659c8: sub             SP, SP, #0x10
    // 0x13659cc: SetupParameters(BagView this /* r1 => r1, fp-0x8 */)
    //     0x13659cc: stur            x1, [fp, #-8]
    // 0x13659d0: r1 = 1
    //     0x13659d0: movz            x1, #0x1
    // 0x13659d4: r0 = AllocateContext()
    //     0x13659d4: bl              #0x16f6108  ; AllocateContextStub
    // 0x13659d8: mov             x1, x0
    // 0x13659dc: ldur            x0, [fp, #-8]
    // 0x13659e0: stur            x1, [fp, #-0x10]
    // 0x13659e4: StoreField: r1->field_f = r0
    //     0x13659e4: stur            w0, [x1, #0xf]
    // 0x13659e8: r0 = Obx()
    //     0x13659e8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13659ec: ldur            x2, [fp, #-0x10]
    // 0x13659f0: r1 = Function '<anonymous closure>':.
    //     0x13659f0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e5f0] AnonymousClosure: (0x12c0160), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x13659f4: ldr             x1, [x1, #0x5f0]
    // 0x13659f8: stur            x0, [fp, #-8]
    // 0x13659fc: r0 = AllocateClosure()
    //     0x13659fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1365a00: mov             x1, x0
    // 0x1365a04: ldur            x0, [fp, #-8]
    // 0x1365a08: StoreField: r0->field_b = r1
    //     0x1365a08: stur            w1, [x0, #0xb]
    // 0x1365a0c: LeaveFrame
    //     0x1365a0c: mov             SP, fp
    //     0x1365a10: ldp             fp, lr, [SP], #0x10
    // 0x1365a14: ret
    //     0x1365a14: ret             
  }
  _ body(/* No info */) {
    // ** addr: 0x14fdaf8, size: 0x94
    // 0x14fdaf8: EnterFrame
    //     0x14fdaf8: stp             fp, lr, [SP, #-0x10]!
    //     0x14fdafc: mov             fp, SP
    // 0x14fdb00: AllocStack(0x18)
    //     0x14fdb00: sub             SP, SP, #0x18
    // 0x14fdb04: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14fdb04: stur            x1, [fp, #-8]
    //     0x14fdb08: stur            x2, [fp, #-0x10]
    // 0x14fdb0c: r1 = 2
    //     0x14fdb0c: movz            x1, #0x2
    // 0x14fdb10: r0 = AllocateContext()
    //     0x14fdb10: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fdb14: mov             x1, x0
    // 0x14fdb18: ldur            x0, [fp, #-8]
    // 0x14fdb1c: stur            x1, [fp, #-0x18]
    // 0x14fdb20: StoreField: r1->field_f = r0
    //     0x14fdb20: stur            w0, [x1, #0xf]
    // 0x14fdb24: ldur            x0, [fp, #-0x10]
    // 0x14fdb28: StoreField: r1->field_13 = r0
    //     0x14fdb28: stur            w0, [x1, #0x13]
    // 0x14fdb2c: r0 = Obx()
    //     0x14fdb2c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fdb30: ldur            x2, [fp, #-0x18]
    // 0x14fdb34: r1 = Function '<anonymous closure>':.
    //     0x14fdb34: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6b8] AnonymousClosure: (0x135904c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x14fdb38: ldr             x1, [x1, #0x6b8]
    // 0x14fdb3c: stur            x0, [fp, #-8]
    // 0x14fdb40: r0 = AllocateClosure()
    //     0x14fdb40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fdb44: mov             x1, x0
    // 0x14fdb48: ldur            x0, [fp, #-8]
    // 0x14fdb4c: StoreField: r0->field_b = r1
    //     0x14fdb4c: stur            w1, [x0, #0xb]
    // 0x14fdb50: r0 = WillPopScope()
    //     0x14fdb50: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14fdb54: mov             x3, x0
    // 0x14fdb58: ldur            x0, [fp, #-8]
    // 0x14fdb5c: stur            x3, [fp, #-0x10]
    // 0x14fdb60: StoreField: r3->field_b = r0
    //     0x14fdb60: stur            w0, [x3, #0xb]
    // 0x14fdb64: ldur            x2, [fp, #-0x18]
    // 0x14fdb68: r1 = Function '<anonymous closure>':.
    //     0x14fdb68: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6c0] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x14fdb6c: ldr             x1, [x1, #0x6c0]
    // 0x14fdb70: r0 = AllocateClosure()
    //     0x14fdb70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fdb74: mov             x1, x0
    // 0x14fdb78: ldur            x0, [fp, #-0x10]
    // 0x14fdb7c: StoreField: r0->field_f = r1
    //     0x14fdb7c: stur            w1, [x0, #0xf]
    // 0x14fdb80: LeaveFrame
    //     0x14fdb80: mov             SP, fp
    //     0x14fdb84: ldp             fp, lr, [SP], #0x10
    // 0x14fdb88: ret
    //     0x14fdb88: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15cb1c4, size: 0xcc
    // 0x15cb1c4: EnterFrame
    //     0x15cb1c4: stp             fp, lr, [SP, #-0x10]!
    //     0x15cb1c8: mov             fp, SP
    // 0x15cb1cc: AllocStack(0x18)
    //     0x15cb1cc: sub             SP, SP, #0x18
    // 0x15cb1d0: SetupParameters()
    //     0x15cb1d0: ldr             x0, [fp, #0x10]
    //     0x15cb1d4: ldur            w3, [x0, #0x17]
    //     0x15cb1d8: add             x3, x3, HEAP, lsl #32
    //     0x15cb1dc: stur            x3, [fp, #-8]
    // 0x15cb1e0: CheckStackOverflow
    //     0x15cb1e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cb1e4: cmp             SP, x16
    //     0x15cb1e8: b.ls            #0x15cb288
    // 0x15cb1ec: LoadField: r1 = r3->field_f
    //     0x15cb1ec: ldur            w1, [x3, #0xf]
    // 0x15cb1f0: DecompressPointer r1
    //     0x15cb1f0: add             x1, x1, HEAP, lsl #32
    // 0x15cb1f4: r2 = false
    //     0x15cb1f4: add             x2, NULL, #0x30  ; false
    // 0x15cb1f8: r0 = showLoading()
    //     0x15cb1f8: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15cb1fc: ldur            x0, [fp, #-8]
    // 0x15cb200: LoadField: r1 = r0->field_f
    //     0x15cb200: ldur            w1, [x0, #0xf]
    // 0x15cb204: DecompressPointer r1
    //     0x15cb204: add             x1, x1, HEAP, lsl #32
    // 0x15cb208: r0 = controller()
    //     0x15cb208: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cb20c: LoadField: r1 = r0->field_b3
    //     0x15cb20c: ldur            w1, [x0, #0xb3]
    // 0x15cb210: DecompressPointer r1
    //     0x15cb210: add             x1, x1, HEAP, lsl #32
    // 0x15cb214: r0 = value()
    //     0x15cb214: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cb218: tbnz            w0, #4, #0x15cb250
    // 0x15cb21c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cb21c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cb220: ldr             x0, [x0, #0x1c80]
    //     0x15cb224: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cb228: cmp             w0, w16
    //     0x15cb22c: b.ne            #0x15cb238
    //     0x15cb230: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cb234: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cb238: r16 = "/search"
    //     0x15cb238: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15cb23c: ldr             x16, [x16, #0x838]
    // 0x15cb240: stp             x16, NULL, [SP]
    // 0x15cb244: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15cb244: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15cb248: r0 = GetNavigation.toNamed()
    //     0x15cb248: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15cb24c: b               #0x15cb278
    // 0x15cb250: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cb250: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cb254: ldr             x0, [x0, #0x1c80]
    //     0x15cb258: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cb25c: cmp             w0, w16
    //     0x15cb260: b.ne            #0x15cb26c
    //     0x15cb264: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cb268: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cb26c: str             NULL, [SP]
    // 0x15cb270: r4 = const [0x1, 0, 0, 0, null]
    //     0x15cb270: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15cb274: r0 = GetNavigation.back()
    //     0x15cb274: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15cb278: r0 = Null
    //     0x15cb278: mov             x0, NULL
    // 0x15cb27c: LeaveFrame
    //     0x15cb27c: mov             SP, fp
    //     0x15cb280: ldp             fp, lr, [SP], #0x10
    // 0x15cb284: ret
    //     0x15cb284: ret             
    // 0x15cb288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cb288: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cb28c: b               #0x15cb1ec
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e64a0, size: 0x2b0
    // 0x15e64a0: EnterFrame
    //     0x15e64a0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e64a4: mov             fp, SP
    // 0x15e64a8: AllocStack(0x30)
    //     0x15e64a8: sub             SP, SP, #0x30
    // 0x15e64ac: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e64ac: stur            x1, [fp, #-8]
    //     0x15e64b0: stur            x2, [fp, #-0x10]
    // 0x15e64b4: CheckStackOverflow
    //     0x15e64b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e64b8: cmp             SP, x16
    //     0x15e64bc: b.ls            #0x15e6748
    // 0x15e64c0: r1 = 2
    //     0x15e64c0: movz            x1, #0x2
    // 0x15e64c4: r0 = AllocateContext()
    //     0x15e64c4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e64c8: ldur            x1, [fp, #-8]
    // 0x15e64cc: stur            x0, [fp, #-0x18]
    // 0x15e64d0: StoreField: r0->field_f = r1
    //     0x15e64d0: stur            w1, [x0, #0xf]
    // 0x15e64d4: ldur            x2, [fp, #-0x10]
    // 0x15e64d8: StoreField: r0->field_13 = r2
    //     0x15e64d8: stur            w2, [x0, #0x13]
    // 0x15e64dc: r0 = Obx()
    //     0x15e64dc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e64e0: ldur            x2, [fp, #-0x18]
    // 0x15e64e4: r1 = Function '<anonymous closure>':.
    //     0x15e64e4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e818] AnonymousClosure: (0x15e6a88), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::appBar (0x15e64a0)
    //     0x15e64e8: ldr             x1, [x1, #0x818]
    // 0x15e64ec: stur            x0, [fp, #-0x10]
    // 0x15e64f0: r0 = AllocateClosure()
    //     0x15e64f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e64f4: mov             x1, x0
    // 0x15e64f8: ldur            x0, [fp, #-0x10]
    // 0x15e64fc: StoreField: r0->field_b = r1
    //     0x15e64fc: stur            w1, [x0, #0xb]
    // 0x15e6500: ldur            x1, [fp, #-8]
    // 0x15e6504: r0 = controller()
    //     0x15e6504: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6508: mov             x1, x0
    // 0x15e650c: r0 = isShowSearch()
    //     0x15e650c: bl              #0x12d4d28  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::isShowSearch
    // 0x15e6510: tbnz            w0, #4, #0x15e65a8
    // 0x15e6514: ldur            x2, [fp, #-0x18]
    // 0x15e6518: LoadField: r1 = r2->field_13
    //     0x15e6518: ldur            w1, [x2, #0x13]
    // 0x15e651c: DecompressPointer r1
    //     0x15e651c: add             x1, x1, HEAP, lsl #32
    // 0x15e6520: r0 = of()
    //     0x15e6520: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e6524: LoadField: r1 = r0->field_5b
    //     0x15e6524: ldur            w1, [x0, #0x5b]
    // 0x15e6528: DecompressPointer r1
    //     0x15e6528: add             x1, x1, HEAP, lsl #32
    // 0x15e652c: stur            x1, [fp, #-8]
    // 0x15e6530: r0 = ColorFilter()
    //     0x15e6530: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e6534: mov             x1, x0
    // 0x15e6538: ldur            x0, [fp, #-8]
    // 0x15e653c: stur            x1, [fp, #-0x20]
    // 0x15e6540: StoreField: r1->field_7 = r0
    //     0x15e6540: stur            w0, [x1, #7]
    // 0x15e6544: r0 = Instance_BlendMode
    //     0x15e6544: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6548: ldr             x0, [x0, #0xb30]
    // 0x15e654c: StoreField: r1->field_b = r0
    //     0x15e654c: stur            w0, [x1, #0xb]
    // 0x15e6550: r2 = 1
    //     0x15e6550: movz            x2, #0x1
    // 0x15e6554: StoreField: r1->field_13 = r2
    //     0x15e6554: stur            x2, [x1, #0x13]
    // 0x15e6558: r0 = SvgPicture()
    //     0x15e6558: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e655c: stur            x0, [fp, #-8]
    // 0x15e6560: ldur            x16, [fp, #-0x20]
    // 0x15e6564: str             x16, [SP]
    // 0x15e6568: mov             x1, x0
    // 0x15e656c: r2 = "assets/images/search.svg"
    //     0x15e656c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e6570: ldr             x2, [x2, #0xa30]
    // 0x15e6574: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e6574: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e6578: ldr             x4, [x4, #0xa38]
    // 0x15e657c: r0 = SvgPicture.asset()
    //     0x15e657c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e6580: r0 = Align()
    //     0x15e6580: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e6584: r3 = Instance_Alignment
    //     0x15e6584: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e6588: ldr             x3, [x3, #0xb10]
    // 0x15e658c: StoreField: r0->field_f = r3
    //     0x15e658c: stur            w3, [x0, #0xf]
    // 0x15e6590: r4 = 1.000000
    //     0x15e6590: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e6594: StoreField: r0->field_13 = r4
    //     0x15e6594: stur            w4, [x0, #0x13]
    // 0x15e6598: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e6598: stur            w4, [x0, #0x17]
    // 0x15e659c: ldur            x1, [fp, #-8]
    // 0x15e65a0: StoreField: r0->field_b = r1
    //     0x15e65a0: stur            w1, [x0, #0xb]
    // 0x15e65a4: b               #0x15e6658
    // 0x15e65a8: ldur            x5, [fp, #-0x18]
    // 0x15e65ac: r4 = 1.000000
    //     0x15e65ac: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e65b0: r0 = Instance_BlendMode
    //     0x15e65b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e65b4: ldr             x0, [x0, #0xb30]
    // 0x15e65b8: r3 = Instance_Alignment
    //     0x15e65b8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e65bc: ldr             x3, [x3, #0xb10]
    // 0x15e65c0: r2 = 1
    //     0x15e65c0: movz            x2, #0x1
    // 0x15e65c4: LoadField: r1 = r5->field_13
    //     0x15e65c4: ldur            w1, [x5, #0x13]
    // 0x15e65c8: DecompressPointer r1
    //     0x15e65c8: add             x1, x1, HEAP, lsl #32
    // 0x15e65cc: r0 = of()
    //     0x15e65cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e65d0: LoadField: r1 = r0->field_5b
    //     0x15e65d0: ldur            w1, [x0, #0x5b]
    // 0x15e65d4: DecompressPointer r1
    //     0x15e65d4: add             x1, x1, HEAP, lsl #32
    // 0x15e65d8: stur            x1, [fp, #-8]
    // 0x15e65dc: r0 = ColorFilter()
    //     0x15e65dc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e65e0: mov             x1, x0
    // 0x15e65e4: ldur            x0, [fp, #-8]
    // 0x15e65e8: stur            x1, [fp, #-0x20]
    // 0x15e65ec: StoreField: r1->field_7 = r0
    //     0x15e65ec: stur            w0, [x1, #7]
    // 0x15e65f0: r0 = Instance_BlendMode
    //     0x15e65f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e65f4: ldr             x0, [x0, #0xb30]
    // 0x15e65f8: StoreField: r1->field_b = r0
    //     0x15e65f8: stur            w0, [x1, #0xb]
    // 0x15e65fc: r0 = 1
    //     0x15e65fc: movz            x0, #0x1
    // 0x15e6600: StoreField: r1->field_13 = r0
    //     0x15e6600: stur            x0, [x1, #0x13]
    // 0x15e6604: r0 = SvgPicture()
    //     0x15e6604: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e6608: stur            x0, [fp, #-8]
    // 0x15e660c: ldur            x16, [fp, #-0x20]
    // 0x15e6610: str             x16, [SP]
    // 0x15e6614: mov             x1, x0
    // 0x15e6618: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e6618: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e661c: ldr             x2, [x2, #0xa40]
    // 0x15e6620: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e6620: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e6624: ldr             x4, [x4, #0xa38]
    // 0x15e6628: r0 = SvgPicture.asset()
    //     0x15e6628: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e662c: r0 = Align()
    //     0x15e662c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e6630: mov             x1, x0
    // 0x15e6634: r0 = Instance_Alignment
    //     0x15e6634: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e6638: ldr             x0, [x0, #0xb10]
    // 0x15e663c: StoreField: r1->field_f = r0
    //     0x15e663c: stur            w0, [x1, #0xf]
    // 0x15e6640: r0 = 1.000000
    //     0x15e6640: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e6644: StoreField: r1->field_13 = r0
    //     0x15e6644: stur            w0, [x1, #0x13]
    // 0x15e6648: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e6648: stur            w0, [x1, #0x17]
    // 0x15e664c: ldur            x0, [fp, #-8]
    // 0x15e6650: StoreField: r1->field_b = r0
    //     0x15e6650: stur            w0, [x1, #0xb]
    // 0x15e6654: mov             x0, x1
    // 0x15e6658: stur            x0, [fp, #-8]
    // 0x15e665c: r0 = InkWell()
    //     0x15e665c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e6660: mov             x3, x0
    // 0x15e6664: ldur            x0, [fp, #-8]
    // 0x15e6668: stur            x3, [fp, #-0x20]
    // 0x15e666c: StoreField: r3->field_b = r0
    //     0x15e666c: stur            w0, [x3, #0xb]
    // 0x15e6670: ldur            x2, [fp, #-0x18]
    // 0x15e6674: r1 = Function '<anonymous closure>':.
    //     0x15e6674: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e820] AnonymousClosure: (0x15cb1c4), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::appBar (0x15e64a0)
    //     0x15e6678: ldr             x1, [x1, #0x820]
    // 0x15e667c: r0 = AllocateClosure()
    //     0x15e667c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6680: ldur            x2, [fp, #-0x20]
    // 0x15e6684: StoreField: r2->field_f = r0
    //     0x15e6684: stur            w0, [x2, #0xf]
    // 0x15e6688: r0 = true
    //     0x15e6688: add             x0, NULL, #0x20  ; true
    // 0x15e668c: StoreField: r2->field_43 = r0
    //     0x15e668c: stur            w0, [x2, #0x43]
    // 0x15e6690: r1 = Instance_BoxShape
    //     0x15e6690: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e6694: ldr             x1, [x1, #0x80]
    // 0x15e6698: StoreField: r2->field_47 = r1
    //     0x15e6698: stur            w1, [x2, #0x47]
    // 0x15e669c: StoreField: r2->field_6f = r0
    //     0x15e669c: stur            w0, [x2, #0x6f]
    // 0x15e66a0: r1 = false
    //     0x15e66a0: add             x1, NULL, #0x30  ; false
    // 0x15e66a4: StoreField: r2->field_73 = r1
    //     0x15e66a4: stur            w1, [x2, #0x73]
    // 0x15e66a8: StoreField: r2->field_83 = r0
    //     0x15e66a8: stur            w0, [x2, #0x83]
    // 0x15e66ac: StoreField: r2->field_7b = r1
    //     0x15e66ac: stur            w1, [x2, #0x7b]
    // 0x15e66b0: r0 = Obx()
    //     0x15e66b0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e66b4: ldur            x2, [fp, #-0x18]
    // 0x15e66b8: r1 = Function '<anonymous closure>':.
    //     0x15e66b8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e828] AnonymousClosure: (0x15e6750), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::appBar (0x15e64a0)
    //     0x15e66bc: ldr             x1, [x1, #0x828]
    // 0x15e66c0: stur            x0, [fp, #-8]
    // 0x15e66c4: r0 = AllocateClosure()
    //     0x15e66c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e66c8: mov             x1, x0
    // 0x15e66cc: ldur            x0, [fp, #-8]
    // 0x15e66d0: StoreField: r0->field_b = r1
    //     0x15e66d0: stur            w1, [x0, #0xb]
    // 0x15e66d4: r1 = Null
    //     0x15e66d4: mov             x1, NULL
    // 0x15e66d8: r2 = 2
    //     0x15e66d8: movz            x2, #0x2
    // 0x15e66dc: r0 = AllocateArray()
    //     0x15e66dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e66e0: mov             x2, x0
    // 0x15e66e4: ldur            x0, [fp, #-8]
    // 0x15e66e8: stur            x2, [fp, #-0x18]
    // 0x15e66ec: StoreField: r2->field_f = r0
    //     0x15e66ec: stur            w0, [x2, #0xf]
    // 0x15e66f0: r1 = <Widget>
    //     0x15e66f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e66f4: r0 = AllocateGrowableArray()
    //     0x15e66f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e66f8: mov             x1, x0
    // 0x15e66fc: ldur            x0, [fp, #-0x18]
    // 0x15e6700: stur            x1, [fp, #-8]
    // 0x15e6704: StoreField: r1->field_f = r0
    //     0x15e6704: stur            w0, [x1, #0xf]
    // 0x15e6708: r0 = 2
    //     0x15e6708: movz            x0, #0x2
    // 0x15e670c: StoreField: r1->field_b = r0
    //     0x15e670c: stur            w0, [x1, #0xb]
    // 0x15e6710: r0 = AppBar()
    //     0x15e6710: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e6714: stur            x0, [fp, #-0x18]
    // 0x15e6718: ldur            x16, [fp, #-0x10]
    // 0x15e671c: ldur            lr, [fp, #-8]
    // 0x15e6720: stp             lr, x16, [SP]
    // 0x15e6724: mov             x1, x0
    // 0x15e6728: ldur            x2, [fp, #-0x20]
    // 0x15e672c: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e672c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e6730: ldr             x4, [x4, #0xa58]
    // 0x15e6734: r0 = AppBar()
    //     0x15e6734: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e6738: ldur            x0, [fp, #-0x18]
    // 0x15e673c: LeaveFrame
    //     0x15e673c: mov             SP, fp
    //     0x15e6740: ldp             fp, lr, [SP], #0x10
    // 0x15e6744: ret
    //     0x15e6744: ret             
    // 0x15e6748: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e6748: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e674c: b               #0x15e64c0
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e6750, size: 0x338
    // 0x15e6750: EnterFrame
    //     0x15e6750: stp             fp, lr, [SP, #-0x10]!
    //     0x15e6754: mov             fp, SP
    // 0x15e6758: AllocStack(0x58)
    //     0x15e6758: sub             SP, SP, #0x58
    // 0x15e675c: SetupParameters()
    //     0x15e675c: ldr             x0, [fp, #0x10]
    //     0x15e6760: ldur            w2, [x0, #0x17]
    //     0x15e6764: add             x2, x2, HEAP, lsl #32
    //     0x15e6768: stur            x2, [fp, #-8]
    // 0x15e676c: CheckStackOverflow
    //     0x15e676c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e6770: cmp             SP, x16
    //     0x15e6774: b.ls            #0x15e6a80
    // 0x15e6778: LoadField: r1 = r2->field_f
    //     0x15e6778: ldur            w1, [x2, #0xf]
    // 0x15e677c: DecompressPointer r1
    //     0x15e677c: add             x1, x1, HEAP, lsl #32
    // 0x15e6780: r0 = controller()
    //     0x15e6780: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6784: LoadField: r1 = r0->field_73
    //     0x15e6784: ldur            w1, [x0, #0x73]
    // 0x15e6788: DecompressPointer r1
    //     0x15e6788: add             x1, x1, HEAP, lsl #32
    // 0x15e678c: r0 = value()
    //     0x15e678c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6790: LoadField: r1 = r0->field_1f
    //     0x15e6790: ldur            w1, [x0, #0x1f]
    // 0x15e6794: DecompressPointer r1
    //     0x15e6794: add             x1, x1, HEAP, lsl #32
    // 0x15e6798: cmp             w1, NULL
    // 0x15e679c: b.ne            #0x15e67a8
    // 0x15e67a0: r0 = Null
    //     0x15e67a0: mov             x0, NULL
    // 0x15e67a4: b               #0x15e67b0
    // 0x15e67a8: LoadField: r0 = r1->field_7
    //     0x15e67a8: ldur            w0, [x1, #7]
    // 0x15e67ac: DecompressPointer r0
    //     0x15e67ac: add             x0, x0, HEAP, lsl #32
    // 0x15e67b0: cmp             w0, NULL
    // 0x15e67b4: b.ne            #0x15e67c0
    // 0x15e67b8: r0 = false
    //     0x15e67b8: add             x0, NULL, #0x30  ; false
    // 0x15e67bc: b               #0x15e69e8
    // 0x15e67c0: tbnz            w0, #4, #0x15e69e4
    // 0x15e67c4: ldur            x0, [fp, #-8]
    // 0x15e67c8: LoadField: r1 = r0->field_f
    //     0x15e67c8: ldur            w1, [x0, #0xf]
    // 0x15e67cc: DecompressPointer r1
    //     0x15e67cc: add             x1, x1, HEAP, lsl #32
    // 0x15e67d0: r0 = controller()
    //     0x15e67d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e67d4: mov             x1, x0
    // 0x15e67d8: r0 = isShowBag()
    //     0x15e67d8: bl              #0x12d8848  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::isShowBag
    // 0x15e67dc: tbnz            w0, #4, #0x15e6814
    // 0x15e67e0: ldur            x0, [fp, #-8]
    // 0x15e67e4: LoadField: r1 = r0->field_f
    //     0x15e67e4: ldur            w1, [x0, #0xf]
    // 0x15e67e8: DecompressPointer r1
    //     0x15e67e8: add             x1, x1, HEAP, lsl #32
    // 0x15e67ec: r0 = controller()
    //     0x15e67ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e67f0: LoadField: r1 = r0->field_bb
    //     0x15e67f0: ldur            w1, [x0, #0xbb]
    // 0x15e67f4: DecompressPointer r1
    //     0x15e67f4: add             x1, x1, HEAP, lsl #32
    // 0x15e67f8: r0 = value()
    //     0x15e67f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e67fc: cmp             w0, NULL
    // 0x15e6800: r16 = true
    //     0x15e6800: add             x16, NULL, #0x20  ; true
    // 0x15e6804: r17 = false
    //     0x15e6804: add             x17, NULL, #0x30  ; false
    // 0x15e6808: csel            x1, x16, x17, ne
    // 0x15e680c: mov             x2, x1
    // 0x15e6810: b               #0x15e6818
    // 0x15e6814: r2 = false
    //     0x15e6814: add             x2, NULL, #0x30  ; false
    // 0x15e6818: ldur            x0, [fp, #-8]
    // 0x15e681c: stur            x2, [fp, #-0x10]
    // 0x15e6820: LoadField: r1 = r0->field_13
    //     0x15e6820: ldur            w1, [x0, #0x13]
    // 0x15e6824: DecompressPointer r1
    //     0x15e6824: add             x1, x1, HEAP, lsl #32
    // 0x15e6828: r0 = of()
    //     0x15e6828: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e682c: LoadField: r2 = r0->field_5b
    //     0x15e682c: ldur            w2, [x0, #0x5b]
    // 0x15e6830: DecompressPointer r2
    //     0x15e6830: add             x2, x2, HEAP, lsl #32
    // 0x15e6834: ldur            x0, [fp, #-8]
    // 0x15e6838: stur            x2, [fp, #-0x18]
    // 0x15e683c: LoadField: r1 = r0->field_f
    //     0x15e683c: ldur            w1, [x0, #0xf]
    // 0x15e6840: DecompressPointer r1
    //     0x15e6840: add             x1, x1, HEAP, lsl #32
    // 0x15e6844: r0 = controller()
    //     0x15e6844: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6848: LoadField: r1 = r0->field_bb
    //     0x15e6848: ldur            w1, [x0, #0xbb]
    // 0x15e684c: DecompressPointer r1
    //     0x15e684c: add             x1, x1, HEAP, lsl #32
    // 0x15e6850: r0 = value()
    //     0x15e6850: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6854: cmp             w0, NULL
    // 0x15e6858: r16 = true
    //     0x15e6858: add             x16, NULL, #0x20  ; true
    // 0x15e685c: r17 = false
    //     0x15e685c: add             x17, NULL, #0x30  ; false
    // 0x15e6860: csel            x2, x16, x17, ne
    // 0x15e6864: ldur            x0, [fp, #-8]
    // 0x15e6868: stur            x2, [fp, #-0x20]
    // 0x15e686c: LoadField: r1 = r0->field_f
    //     0x15e686c: ldur            w1, [x0, #0xf]
    // 0x15e6870: DecompressPointer r1
    //     0x15e6870: add             x1, x1, HEAP, lsl #32
    // 0x15e6874: r0 = controller()
    //     0x15e6874: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6878: LoadField: r1 = r0->field_bb
    //     0x15e6878: ldur            w1, [x0, #0xbb]
    // 0x15e687c: DecompressPointer r1
    //     0x15e687c: add             x1, x1, HEAP, lsl #32
    // 0x15e6880: r0 = value()
    //     0x15e6880: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6884: str             x0, [SP]
    // 0x15e6888: r0 = _interpolateSingle()
    //     0x15e6888: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e688c: mov             x2, x0
    // 0x15e6890: ldur            x0, [fp, #-8]
    // 0x15e6894: stur            x2, [fp, #-0x28]
    // 0x15e6898: LoadField: r1 = r0->field_13
    //     0x15e6898: ldur            w1, [x0, #0x13]
    // 0x15e689c: DecompressPointer r1
    //     0x15e689c: add             x1, x1, HEAP, lsl #32
    // 0x15e68a0: r0 = of()
    //     0x15e68a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e68a4: LoadField: r1 = r0->field_87
    //     0x15e68a4: ldur            w1, [x0, #0x87]
    // 0x15e68a8: DecompressPointer r1
    //     0x15e68a8: add             x1, x1, HEAP, lsl #32
    // 0x15e68ac: LoadField: r0 = r1->field_27
    //     0x15e68ac: ldur            w0, [x1, #0x27]
    // 0x15e68b0: DecompressPointer r0
    //     0x15e68b0: add             x0, x0, HEAP, lsl #32
    // 0x15e68b4: r16 = Instance_Color
    //     0x15e68b4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e68b8: str             x16, [SP]
    // 0x15e68bc: mov             x1, x0
    // 0x15e68c0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e68c0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e68c4: ldr             x4, [x4, #0xf40]
    // 0x15e68c8: r0 = copyWith()
    //     0x15e68c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e68cc: stur            x0, [fp, #-0x30]
    // 0x15e68d0: r0 = Text()
    //     0x15e68d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e68d4: mov             x2, x0
    // 0x15e68d8: ldur            x0, [fp, #-0x28]
    // 0x15e68dc: stur            x2, [fp, #-0x38]
    // 0x15e68e0: StoreField: r2->field_b = r0
    //     0x15e68e0: stur            w0, [x2, #0xb]
    // 0x15e68e4: ldur            x0, [fp, #-0x30]
    // 0x15e68e8: StoreField: r2->field_13 = r0
    //     0x15e68e8: stur            w0, [x2, #0x13]
    // 0x15e68ec: ldur            x0, [fp, #-8]
    // 0x15e68f0: LoadField: r1 = r0->field_13
    //     0x15e68f0: ldur            w1, [x0, #0x13]
    // 0x15e68f4: DecompressPointer r1
    //     0x15e68f4: add             x1, x1, HEAP, lsl #32
    // 0x15e68f8: r0 = of()
    //     0x15e68f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e68fc: LoadField: r1 = r0->field_5b
    //     0x15e68fc: ldur            w1, [x0, #0x5b]
    // 0x15e6900: DecompressPointer r1
    //     0x15e6900: add             x1, x1, HEAP, lsl #32
    // 0x15e6904: stur            x1, [fp, #-8]
    // 0x15e6908: r0 = ColorFilter()
    //     0x15e6908: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e690c: mov             x1, x0
    // 0x15e6910: ldur            x0, [fp, #-8]
    // 0x15e6914: stur            x1, [fp, #-0x28]
    // 0x15e6918: StoreField: r1->field_7 = r0
    //     0x15e6918: stur            w0, [x1, #7]
    // 0x15e691c: r0 = Instance_BlendMode
    //     0x15e691c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6920: ldr             x0, [x0, #0xb30]
    // 0x15e6924: StoreField: r1->field_b = r0
    //     0x15e6924: stur            w0, [x1, #0xb]
    // 0x15e6928: r0 = 1
    //     0x15e6928: movz            x0, #0x1
    // 0x15e692c: StoreField: r1->field_13 = r0
    //     0x15e692c: stur            x0, [x1, #0x13]
    // 0x15e6930: r0 = SvgPicture()
    //     0x15e6930: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e6934: stur            x0, [fp, #-8]
    // 0x15e6938: r16 = Instance_BoxFit
    //     0x15e6938: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e693c: ldr             x16, [x16, #0xb18]
    // 0x15e6940: r30 = 24.000000
    //     0x15e6940: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e6944: ldr             lr, [lr, #0xba8]
    // 0x15e6948: stp             lr, x16, [SP, #0x10]
    // 0x15e694c: r16 = 24.000000
    //     0x15e694c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e6950: ldr             x16, [x16, #0xba8]
    // 0x15e6954: ldur            lr, [fp, #-0x28]
    // 0x15e6958: stp             lr, x16, [SP]
    // 0x15e695c: mov             x1, x0
    // 0x15e6960: r2 = "assets/images/shopping_bag.svg"
    //     0x15e6960: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e6964: ldr             x2, [x2, #0xa60]
    // 0x15e6968: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e6968: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e696c: ldr             x4, [x4, #0xa68]
    // 0x15e6970: r0 = SvgPicture.asset()
    //     0x15e6970: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e6974: r0 = Badge()
    //     0x15e6974: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e6978: mov             x1, x0
    // 0x15e697c: ldur            x0, [fp, #-0x18]
    // 0x15e6980: stur            x1, [fp, #-0x28]
    // 0x15e6984: StoreField: r1->field_b = r0
    //     0x15e6984: stur            w0, [x1, #0xb]
    // 0x15e6988: ldur            x0, [fp, #-0x38]
    // 0x15e698c: StoreField: r1->field_27 = r0
    //     0x15e698c: stur            w0, [x1, #0x27]
    // 0x15e6990: ldur            x0, [fp, #-0x20]
    // 0x15e6994: StoreField: r1->field_2b = r0
    //     0x15e6994: stur            w0, [x1, #0x2b]
    // 0x15e6998: ldur            x0, [fp, #-8]
    // 0x15e699c: StoreField: r1->field_2f = r0
    //     0x15e699c: stur            w0, [x1, #0x2f]
    // 0x15e69a0: r0 = Visibility()
    //     0x15e69a0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e69a4: mov             x1, x0
    // 0x15e69a8: ldur            x0, [fp, #-0x28]
    // 0x15e69ac: StoreField: r1->field_b = r0
    //     0x15e69ac: stur            w0, [x1, #0xb]
    // 0x15e69b0: r0 = Instance_SizedBox
    //     0x15e69b0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e69b4: StoreField: r1->field_f = r0
    //     0x15e69b4: stur            w0, [x1, #0xf]
    // 0x15e69b8: ldur            x0, [fp, #-0x10]
    // 0x15e69bc: StoreField: r1->field_13 = r0
    //     0x15e69bc: stur            w0, [x1, #0x13]
    // 0x15e69c0: r0 = false
    //     0x15e69c0: add             x0, NULL, #0x30  ; false
    // 0x15e69c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e69c4: stur            w0, [x1, #0x17]
    // 0x15e69c8: StoreField: r1->field_1b = r0
    //     0x15e69c8: stur            w0, [x1, #0x1b]
    // 0x15e69cc: StoreField: r1->field_1f = r0
    //     0x15e69cc: stur            w0, [x1, #0x1f]
    // 0x15e69d0: StoreField: r1->field_23 = r0
    //     0x15e69d0: stur            w0, [x1, #0x23]
    // 0x15e69d4: StoreField: r1->field_27 = r0
    //     0x15e69d4: stur            w0, [x1, #0x27]
    // 0x15e69d8: StoreField: r1->field_2b = r0
    //     0x15e69d8: stur            w0, [x1, #0x2b]
    // 0x15e69dc: mov             x0, x1
    // 0x15e69e0: b               #0x15e6a00
    // 0x15e69e4: r0 = false
    //     0x15e69e4: add             x0, NULL, #0x30  ; false
    // 0x15e69e8: r0 = Container()
    //     0x15e69e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e69ec: mov             x1, x0
    // 0x15e69f0: stur            x0, [fp, #-8]
    // 0x15e69f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e69f4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e69f8: r0 = Container()
    //     0x15e69f8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e69fc: ldur            x0, [fp, #-8]
    // 0x15e6a00: stur            x0, [fp, #-8]
    // 0x15e6a04: r0 = InkWell()
    //     0x15e6a04: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e6a08: mov             x3, x0
    // 0x15e6a0c: ldur            x0, [fp, #-8]
    // 0x15e6a10: stur            x3, [fp, #-0x10]
    // 0x15e6a14: StoreField: r3->field_b = r0
    //     0x15e6a14: stur            w0, [x3, #0xb]
    // 0x15e6a18: r1 = Function '<anonymous closure>':.
    //     0x15e6a18: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e830] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e6a1c: ldr             x1, [x1, #0x830]
    // 0x15e6a20: r2 = Null
    //     0x15e6a20: mov             x2, NULL
    // 0x15e6a24: r0 = AllocateClosure()
    //     0x15e6a24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6a28: mov             x1, x0
    // 0x15e6a2c: ldur            x0, [fp, #-0x10]
    // 0x15e6a30: StoreField: r0->field_f = r1
    //     0x15e6a30: stur            w1, [x0, #0xf]
    // 0x15e6a34: r1 = true
    //     0x15e6a34: add             x1, NULL, #0x20  ; true
    // 0x15e6a38: StoreField: r0->field_43 = r1
    //     0x15e6a38: stur            w1, [x0, #0x43]
    // 0x15e6a3c: r2 = Instance_BoxShape
    //     0x15e6a3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e6a40: ldr             x2, [x2, #0x80]
    // 0x15e6a44: StoreField: r0->field_47 = r2
    //     0x15e6a44: stur            w2, [x0, #0x47]
    // 0x15e6a48: StoreField: r0->field_6f = r1
    //     0x15e6a48: stur            w1, [x0, #0x6f]
    // 0x15e6a4c: r2 = false
    //     0x15e6a4c: add             x2, NULL, #0x30  ; false
    // 0x15e6a50: StoreField: r0->field_73 = r2
    //     0x15e6a50: stur            w2, [x0, #0x73]
    // 0x15e6a54: StoreField: r0->field_83 = r1
    //     0x15e6a54: stur            w1, [x0, #0x83]
    // 0x15e6a58: StoreField: r0->field_7b = r2
    //     0x15e6a58: stur            w2, [x0, #0x7b]
    // 0x15e6a5c: r0 = Padding()
    //     0x15e6a5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e6a60: r1 = Instance_EdgeInsets
    //     0x15e6a60: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15e6a64: ldr             x1, [x1, #0xa78]
    // 0x15e6a68: StoreField: r0->field_f = r1
    //     0x15e6a68: stur            w1, [x0, #0xf]
    // 0x15e6a6c: ldur            x1, [fp, #-0x10]
    // 0x15e6a70: StoreField: r0->field_b = r1
    //     0x15e6a70: stur            w1, [x0, #0xb]
    // 0x15e6a74: LeaveFrame
    //     0x15e6a74: mov             SP, fp
    //     0x15e6a78: ldp             fp, lr, [SP], #0x10
    // 0x15e6a7c: ret
    //     0x15e6a7c: ret             
    // 0x15e6a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e6a80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e6a84: b               #0x15e6778
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15e6a88, size: 0x460
    // 0x15e6a88: EnterFrame
    //     0x15e6a88: stp             fp, lr, [SP, #-0x10]!
    //     0x15e6a8c: mov             fp, SP
    // 0x15e6a90: AllocStack(0x68)
    //     0x15e6a90: sub             SP, SP, #0x68
    // 0x15e6a94: SetupParameters()
    //     0x15e6a94: ldr             x0, [fp, #0x10]
    //     0x15e6a98: ldur            w2, [x0, #0x17]
    //     0x15e6a9c: add             x2, x2, HEAP, lsl #32
    //     0x15e6aa0: stur            x2, [fp, #-8]
    // 0x15e6aa4: CheckStackOverflow
    //     0x15e6aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e6aa8: cmp             SP, x16
    //     0x15e6aac: b.ls            #0x15e6ee0
    // 0x15e6ab0: LoadField: r1 = r2->field_f
    //     0x15e6ab0: ldur            w1, [x2, #0xf]
    // 0x15e6ab4: DecompressPointer r1
    //     0x15e6ab4: add             x1, x1, HEAP, lsl #32
    // 0x15e6ab8: r0 = controller()
    //     0x15e6ab8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6abc: LoadField: r1 = r0->field_73
    //     0x15e6abc: ldur            w1, [x0, #0x73]
    // 0x15e6ac0: DecompressPointer r1
    //     0x15e6ac0: add             x1, x1, HEAP, lsl #32
    // 0x15e6ac4: r0 = value()
    //     0x15e6ac4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6ac8: LoadField: r1 = r0->field_3f
    //     0x15e6ac8: ldur            w1, [x0, #0x3f]
    // 0x15e6acc: DecompressPointer r1
    //     0x15e6acc: add             x1, x1, HEAP, lsl #32
    // 0x15e6ad0: cmp             w1, NULL
    // 0x15e6ad4: b.ne            #0x15e6ae0
    // 0x15e6ad8: r0 = Null
    //     0x15e6ad8: mov             x0, NULL
    // 0x15e6adc: b               #0x15e6ae8
    // 0x15e6ae0: LoadField: r0 = r1->field_f
    //     0x15e6ae0: ldur            w0, [x1, #0xf]
    // 0x15e6ae4: DecompressPointer r0
    //     0x15e6ae4: add             x0, x0, HEAP, lsl #32
    // 0x15e6ae8: r1 = LoadClassIdInstr(r0)
    //     0x15e6ae8: ldur            x1, [x0, #-1]
    //     0x15e6aec: ubfx            x1, x1, #0xc, #0x14
    // 0x15e6af0: r16 = "image_text"
    //     0x15e6af0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15e6af4: ldr             x16, [x16, #0xa88]
    // 0x15e6af8: stp             x16, x0, [SP]
    // 0x15e6afc: mov             x0, x1
    // 0x15e6b00: mov             lr, x0
    // 0x15e6b04: ldr             lr, [x21, lr, lsl #3]
    // 0x15e6b08: blr             lr
    // 0x15e6b0c: tbnz            w0, #4, #0x15e6b18
    // 0x15e6b10: r2 = true
    //     0x15e6b10: add             x2, NULL, #0x20  ; true
    // 0x15e6b14: b               #0x15e6b78
    // 0x15e6b18: ldur            x0, [fp, #-8]
    // 0x15e6b1c: LoadField: r1 = r0->field_f
    //     0x15e6b1c: ldur            w1, [x0, #0xf]
    // 0x15e6b20: DecompressPointer r1
    //     0x15e6b20: add             x1, x1, HEAP, lsl #32
    // 0x15e6b24: r0 = controller()
    //     0x15e6b24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6b28: LoadField: r1 = r0->field_73
    //     0x15e6b28: ldur            w1, [x0, #0x73]
    // 0x15e6b2c: DecompressPointer r1
    //     0x15e6b2c: add             x1, x1, HEAP, lsl #32
    // 0x15e6b30: r0 = value()
    //     0x15e6b30: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6b34: LoadField: r1 = r0->field_3f
    //     0x15e6b34: ldur            w1, [x0, #0x3f]
    // 0x15e6b38: DecompressPointer r1
    //     0x15e6b38: add             x1, x1, HEAP, lsl #32
    // 0x15e6b3c: cmp             w1, NULL
    // 0x15e6b40: b.ne            #0x15e6b4c
    // 0x15e6b44: r0 = Null
    //     0x15e6b44: mov             x0, NULL
    // 0x15e6b48: b               #0x15e6b54
    // 0x15e6b4c: LoadField: r0 = r1->field_f
    //     0x15e6b4c: ldur            w0, [x1, #0xf]
    // 0x15e6b50: DecompressPointer r0
    //     0x15e6b50: add             x0, x0, HEAP, lsl #32
    // 0x15e6b54: r1 = LoadClassIdInstr(r0)
    //     0x15e6b54: ldur            x1, [x0, #-1]
    //     0x15e6b58: ubfx            x1, x1, #0xc, #0x14
    // 0x15e6b5c: r16 = "image"
    //     0x15e6b5c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15e6b60: stp             x16, x0, [SP]
    // 0x15e6b64: mov             x0, x1
    // 0x15e6b68: mov             lr, x0
    // 0x15e6b6c: ldr             lr, [x21, lr, lsl #3]
    // 0x15e6b70: blr             lr
    // 0x15e6b74: mov             x2, x0
    // 0x15e6b78: ldur            x0, [fp, #-8]
    // 0x15e6b7c: stur            x2, [fp, #-0x10]
    // 0x15e6b80: LoadField: r1 = r0->field_f
    //     0x15e6b80: ldur            w1, [x0, #0xf]
    // 0x15e6b84: DecompressPointer r1
    //     0x15e6b84: add             x1, x1, HEAP, lsl #32
    // 0x15e6b88: r0 = controller()
    //     0x15e6b88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6b8c: LoadField: r1 = r0->field_73
    //     0x15e6b8c: ldur            w1, [x0, #0x73]
    // 0x15e6b90: DecompressPointer r1
    //     0x15e6b90: add             x1, x1, HEAP, lsl #32
    // 0x15e6b94: r0 = value()
    //     0x15e6b94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6b98: LoadField: r1 = r0->field_27
    //     0x15e6b98: ldur            w1, [x0, #0x27]
    // 0x15e6b9c: DecompressPointer r1
    //     0x15e6b9c: add             x1, x1, HEAP, lsl #32
    // 0x15e6ba0: cmp             w1, NULL
    // 0x15e6ba4: b.ne            #0x15e6bb0
    // 0x15e6ba8: r2 = ""
    //     0x15e6ba8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e6bac: b               #0x15e6bb4
    // 0x15e6bb0: mov             x2, x1
    // 0x15e6bb4: ldur            x0, [fp, #-8]
    // 0x15e6bb8: ldur            x1, [fp, #-0x10]
    // 0x15e6bbc: stur            x2, [fp, #-0x18]
    // 0x15e6bc0: r0 = ImageHeaders.forImages()
    //     0x15e6bc0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15e6bc4: r1 = Function '<anonymous closure>':.
    //     0x15e6bc4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e838] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e6bc8: ldr             x1, [x1, #0x838]
    // 0x15e6bcc: r2 = Null
    //     0x15e6bcc: mov             x2, NULL
    // 0x15e6bd0: stur            x0, [fp, #-0x20]
    // 0x15e6bd4: r0 = AllocateClosure()
    //     0x15e6bd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6bd8: r1 = Function '<anonymous closure>':.
    //     0x15e6bd8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e840] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15e6bdc: ldr             x1, [x1, #0x840]
    // 0x15e6be0: r2 = Null
    //     0x15e6be0: mov             x2, NULL
    // 0x15e6be4: stur            x0, [fp, #-0x28]
    // 0x15e6be8: r0 = AllocateClosure()
    //     0x15e6be8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6bec: stur            x0, [fp, #-0x30]
    // 0x15e6bf0: r0 = CachedNetworkImage()
    //     0x15e6bf0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15e6bf4: stur            x0, [fp, #-0x38]
    // 0x15e6bf8: ldur            x16, [fp, #-0x20]
    // 0x15e6bfc: r30 = Instance_BoxFit
    //     0x15e6bfc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e6c00: ldr             lr, [lr, #0xb18]
    // 0x15e6c04: stp             lr, x16, [SP, #0x20]
    // 0x15e6c08: r16 = 50.000000
    //     0x15e6c08: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15e6c0c: ldr             x16, [x16, #0xa90]
    // 0x15e6c10: r30 = 50.000000
    //     0x15e6c10: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15e6c14: ldr             lr, [lr, #0xa90]
    // 0x15e6c18: stp             lr, x16, [SP, #0x10]
    // 0x15e6c1c: ldur            x16, [fp, #-0x28]
    // 0x15e6c20: ldur            lr, [fp, #-0x30]
    // 0x15e6c24: stp             lr, x16, [SP]
    // 0x15e6c28: mov             x1, x0
    // 0x15e6c2c: ldur            x2, [fp, #-0x18]
    // 0x15e6c30: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x3, height, 0x4, httpHeaders, 0x2, progressIndicatorBuilder, 0x6, width, 0x5, null]
    //     0x15e6c30: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e5e8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x6, "width", 0x5, Null]
    //     0x15e6c34: ldr             x4, [x4, #0x5e8]
    // 0x15e6c38: r0 = CachedNetworkImage()
    //     0x15e6c38: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15e6c3c: r0 = Visibility()
    //     0x15e6c3c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e6c40: mov             x2, x0
    // 0x15e6c44: ldur            x0, [fp, #-0x38]
    // 0x15e6c48: stur            x2, [fp, #-0x18]
    // 0x15e6c4c: StoreField: r2->field_b = r0
    //     0x15e6c4c: stur            w0, [x2, #0xb]
    // 0x15e6c50: r0 = Instance_SizedBox
    //     0x15e6c50: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e6c54: StoreField: r2->field_f = r0
    //     0x15e6c54: stur            w0, [x2, #0xf]
    // 0x15e6c58: ldur            x1, [fp, #-0x10]
    // 0x15e6c5c: StoreField: r2->field_13 = r1
    //     0x15e6c5c: stur            w1, [x2, #0x13]
    // 0x15e6c60: r3 = false
    //     0x15e6c60: add             x3, NULL, #0x30  ; false
    // 0x15e6c64: ArrayStore: r2[0] = r3  ; List_4
    //     0x15e6c64: stur            w3, [x2, #0x17]
    // 0x15e6c68: StoreField: r2->field_1b = r3
    //     0x15e6c68: stur            w3, [x2, #0x1b]
    // 0x15e6c6c: StoreField: r2->field_1f = r3
    //     0x15e6c6c: stur            w3, [x2, #0x1f]
    // 0x15e6c70: StoreField: r2->field_23 = r3
    //     0x15e6c70: stur            w3, [x2, #0x23]
    // 0x15e6c74: StoreField: r2->field_27 = r3
    //     0x15e6c74: stur            w3, [x2, #0x27]
    // 0x15e6c78: StoreField: r2->field_2b = r3
    //     0x15e6c78: stur            w3, [x2, #0x2b]
    // 0x15e6c7c: ldur            x4, [fp, #-8]
    // 0x15e6c80: LoadField: r1 = r4->field_f
    //     0x15e6c80: ldur            w1, [x4, #0xf]
    // 0x15e6c84: DecompressPointer r1
    //     0x15e6c84: add             x1, x1, HEAP, lsl #32
    // 0x15e6c88: r0 = controller()
    //     0x15e6c88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6c8c: LoadField: r1 = r0->field_73
    //     0x15e6c8c: ldur            w1, [x0, #0x73]
    // 0x15e6c90: DecompressPointer r1
    //     0x15e6c90: add             x1, x1, HEAP, lsl #32
    // 0x15e6c94: r0 = value()
    //     0x15e6c94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6c98: LoadField: r1 = r0->field_3f
    //     0x15e6c98: ldur            w1, [x0, #0x3f]
    // 0x15e6c9c: DecompressPointer r1
    //     0x15e6c9c: add             x1, x1, HEAP, lsl #32
    // 0x15e6ca0: cmp             w1, NULL
    // 0x15e6ca4: b.ne            #0x15e6cb0
    // 0x15e6ca8: r0 = Null
    //     0x15e6ca8: mov             x0, NULL
    // 0x15e6cac: b               #0x15e6cb8
    // 0x15e6cb0: LoadField: r0 = r1->field_f
    //     0x15e6cb0: ldur            w0, [x1, #0xf]
    // 0x15e6cb4: DecompressPointer r0
    //     0x15e6cb4: add             x0, x0, HEAP, lsl #32
    // 0x15e6cb8: r1 = LoadClassIdInstr(r0)
    //     0x15e6cb8: ldur            x1, [x0, #-1]
    //     0x15e6cbc: ubfx            x1, x1, #0xc, #0x14
    // 0x15e6cc0: r16 = "image_text"
    //     0x15e6cc0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15e6cc4: ldr             x16, [x16, #0xa88]
    // 0x15e6cc8: stp             x16, x0, [SP]
    // 0x15e6ccc: mov             x0, x1
    // 0x15e6cd0: mov             lr, x0
    // 0x15e6cd4: ldr             lr, [x21, lr, lsl #3]
    // 0x15e6cd8: blr             lr
    // 0x15e6cdc: tbnz            w0, #4, #0x15e6ce8
    // 0x15e6ce0: r2 = true
    //     0x15e6ce0: add             x2, NULL, #0x20  ; true
    // 0x15e6ce4: b               #0x15e6d48
    // 0x15e6ce8: ldur            x0, [fp, #-8]
    // 0x15e6cec: LoadField: r1 = r0->field_f
    //     0x15e6cec: ldur            w1, [x0, #0xf]
    // 0x15e6cf0: DecompressPointer r1
    //     0x15e6cf0: add             x1, x1, HEAP, lsl #32
    // 0x15e6cf4: r0 = controller()
    //     0x15e6cf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6cf8: LoadField: r1 = r0->field_73
    //     0x15e6cf8: ldur            w1, [x0, #0x73]
    // 0x15e6cfc: DecompressPointer r1
    //     0x15e6cfc: add             x1, x1, HEAP, lsl #32
    // 0x15e6d00: r0 = value()
    //     0x15e6d00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6d04: LoadField: r1 = r0->field_3f
    //     0x15e6d04: ldur            w1, [x0, #0x3f]
    // 0x15e6d08: DecompressPointer r1
    //     0x15e6d08: add             x1, x1, HEAP, lsl #32
    // 0x15e6d0c: cmp             w1, NULL
    // 0x15e6d10: b.ne            #0x15e6d1c
    // 0x15e6d14: r0 = Null
    //     0x15e6d14: mov             x0, NULL
    // 0x15e6d18: b               #0x15e6d24
    // 0x15e6d1c: LoadField: r0 = r1->field_f
    //     0x15e6d1c: ldur            w0, [x1, #0xf]
    // 0x15e6d20: DecompressPointer r0
    //     0x15e6d20: add             x0, x0, HEAP, lsl #32
    // 0x15e6d24: r1 = LoadClassIdInstr(r0)
    //     0x15e6d24: ldur            x1, [x0, #-1]
    //     0x15e6d28: ubfx            x1, x1, #0xc, #0x14
    // 0x15e6d2c: r16 = "text"
    //     0x15e6d2c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15e6d30: stp             x16, x0, [SP]
    // 0x15e6d34: mov             x0, x1
    // 0x15e6d38: mov             lr, x0
    // 0x15e6d3c: ldr             lr, [x21, lr, lsl #3]
    // 0x15e6d40: blr             lr
    // 0x15e6d44: mov             x2, x0
    // 0x15e6d48: ldur            x0, [fp, #-8]
    // 0x15e6d4c: stur            x2, [fp, #-0x10]
    // 0x15e6d50: LoadField: r1 = r0->field_f
    //     0x15e6d50: ldur            w1, [x0, #0xf]
    // 0x15e6d54: DecompressPointer r1
    //     0x15e6d54: add             x1, x1, HEAP, lsl #32
    // 0x15e6d58: r0 = controller()
    //     0x15e6d58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6d5c: LoadField: r1 = r0->field_73
    //     0x15e6d5c: ldur            w1, [x0, #0x73]
    // 0x15e6d60: DecompressPointer r1
    //     0x15e6d60: add             x1, x1, HEAP, lsl #32
    // 0x15e6d64: r0 = value()
    //     0x15e6d64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e6d68: LoadField: r1 = r0->field_2b
    //     0x15e6d68: ldur            w1, [x0, #0x2b]
    // 0x15e6d6c: DecompressPointer r1
    //     0x15e6d6c: add             x1, x1, HEAP, lsl #32
    // 0x15e6d70: cmp             w1, NULL
    // 0x15e6d74: b.ne            #0x15e6d80
    // 0x15e6d78: r4 = ""
    //     0x15e6d78: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15e6d7c: b               #0x15e6d84
    // 0x15e6d80: mov             x4, x1
    // 0x15e6d84: ldur            x0, [fp, #-8]
    // 0x15e6d88: ldur            x3, [fp, #-0x18]
    // 0x15e6d8c: ldur            x2, [fp, #-0x10]
    // 0x15e6d90: stur            x4, [fp, #-0x20]
    // 0x15e6d94: LoadField: r1 = r0->field_13
    //     0x15e6d94: ldur            w1, [x0, #0x13]
    // 0x15e6d98: DecompressPointer r1
    //     0x15e6d98: add             x1, x1, HEAP, lsl #32
    // 0x15e6d9c: r0 = of()
    //     0x15e6d9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e6da0: LoadField: r1 = r0->field_87
    //     0x15e6da0: ldur            w1, [x0, #0x87]
    // 0x15e6da4: DecompressPointer r1
    //     0x15e6da4: add             x1, x1, HEAP, lsl #32
    // 0x15e6da8: LoadField: r0 = r1->field_2b
    //     0x15e6da8: ldur            w0, [x1, #0x2b]
    // 0x15e6dac: DecompressPointer r0
    //     0x15e6dac: add             x0, x0, HEAP, lsl #32
    // 0x15e6db0: r16 = 16.000000
    //     0x15e6db0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15e6db4: ldr             x16, [x16, #0x188]
    // 0x15e6db8: r30 = Instance_Color
    //     0x15e6db8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e6dbc: stp             lr, x16, [SP]
    // 0x15e6dc0: mov             x1, x0
    // 0x15e6dc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15e6dc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15e6dc8: ldr             x4, [x4, #0xaa0]
    // 0x15e6dcc: r0 = copyWith()
    //     0x15e6dcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e6dd0: stur            x0, [fp, #-8]
    // 0x15e6dd4: r0 = Text()
    //     0x15e6dd4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e6dd8: mov             x1, x0
    // 0x15e6ddc: ldur            x0, [fp, #-0x20]
    // 0x15e6de0: stur            x1, [fp, #-0x28]
    // 0x15e6de4: StoreField: r1->field_b = r0
    //     0x15e6de4: stur            w0, [x1, #0xb]
    // 0x15e6de8: ldur            x0, [fp, #-8]
    // 0x15e6dec: StoreField: r1->field_13 = r0
    //     0x15e6dec: stur            w0, [x1, #0x13]
    // 0x15e6df0: r0 = Visibility()
    //     0x15e6df0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e6df4: mov             x3, x0
    // 0x15e6df8: ldur            x0, [fp, #-0x28]
    // 0x15e6dfc: stur            x3, [fp, #-8]
    // 0x15e6e00: StoreField: r3->field_b = r0
    //     0x15e6e00: stur            w0, [x3, #0xb]
    // 0x15e6e04: r0 = Instance_SizedBox
    //     0x15e6e04: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e6e08: StoreField: r3->field_f = r0
    //     0x15e6e08: stur            w0, [x3, #0xf]
    // 0x15e6e0c: ldur            x0, [fp, #-0x10]
    // 0x15e6e10: StoreField: r3->field_13 = r0
    //     0x15e6e10: stur            w0, [x3, #0x13]
    // 0x15e6e14: r0 = false
    //     0x15e6e14: add             x0, NULL, #0x30  ; false
    // 0x15e6e18: ArrayStore: r3[0] = r0  ; List_4
    //     0x15e6e18: stur            w0, [x3, #0x17]
    // 0x15e6e1c: StoreField: r3->field_1b = r0
    //     0x15e6e1c: stur            w0, [x3, #0x1b]
    // 0x15e6e20: StoreField: r3->field_1f = r0
    //     0x15e6e20: stur            w0, [x3, #0x1f]
    // 0x15e6e24: StoreField: r3->field_23 = r0
    //     0x15e6e24: stur            w0, [x3, #0x23]
    // 0x15e6e28: StoreField: r3->field_27 = r0
    //     0x15e6e28: stur            w0, [x3, #0x27]
    // 0x15e6e2c: StoreField: r3->field_2b = r0
    //     0x15e6e2c: stur            w0, [x3, #0x2b]
    // 0x15e6e30: r1 = Null
    //     0x15e6e30: mov             x1, NULL
    // 0x15e6e34: r2 = 6
    //     0x15e6e34: movz            x2, #0x6
    // 0x15e6e38: r0 = AllocateArray()
    //     0x15e6e38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e6e3c: mov             x2, x0
    // 0x15e6e40: ldur            x0, [fp, #-0x18]
    // 0x15e6e44: stur            x2, [fp, #-0x10]
    // 0x15e6e48: StoreField: r2->field_f = r0
    //     0x15e6e48: stur            w0, [x2, #0xf]
    // 0x15e6e4c: r16 = Instance_SizedBox
    //     0x15e6e4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15e6e50: ldr             x16, [x16, #0xaa8]
    // 0x15e6e54: StoreField: r2->field_13 = r16
    //     0x15e6e54: stur            w16, [x2, #0x13]
    // 0x15e6e58: ldur            x0, [fp, #-8]
    // 0x15e6e5c: ArrayStore: r2[0] = r0  ; List_4
    //     0x15e6e5c: stur            w0, [x2, #0x17]
    // 0x15e6e60: r1 = <Widget>
    //     0x15e6e60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e6e64: r0 = AllocateGrowableArray()
    //     0x15e6e64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e6e68: mov             x1, x0
    // 0x15e6e6c: ldur            x0, [fp, #-0x10]
    // 0x15e6e70: stur            x1, [fp, #-8]
    // 0x15e6e74: StoreField: r1->field_f = r0
    //     0x15e6e74: stur            w0, [x1, #0xf]
    // 0x15e6e78: r0 = 6
    //     0x15e6e78: movz            x0, #0x6
    // 0x15e6e7c: StoreField: r1->field_b = r0
    //     0x15e6e7c: stur            w0, [x1, #0xb]
    // 0x15e6e80: r0 = Row()
    //     0x15e6e80: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15e6e84: r1 = Instance_Axis
    //     0x15e6e84: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15e6e88: StoreField: r0->field_f = r1
    //     0x15e6e88: stur            w1, [x0, #0xf]
    // 0x15e6e8c: r1 = Instance_MainAxisAlignment
    //     0x15e6e8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15e6e90: ldr             x1, [x1, #0xab0]
    // 0x15e6e94: StoreField: r0->field_13 = r1
    //     0x15e6e94: stur            w1, [x0, #0x13]
    // 0x15e6e98: r1 = Instance_MainAxisSize
    //     0x15e6e98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15e6e9c: ldr             x1, [x1, #0xa10]
    // 0x15e6ea0: ArrayStore: r0[0] = r1  ; List_4
    //     0x15e6ea0: stur            w1, [x0, #0x17]
    // 0x15e6ea4: r1 = Instance_CrossAxisAlignment
    //     0x15e6ea4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15e6ea8: ldr             x1, [x1, #0xa18]
    // 0x15e6eac: StoreField: r0->field_1b = r1
    //     0x15e6eac: stur            w1, [x0, #0x1b]
    // 0x15e6eb0: r1 = Instance_VerticalDirection
    //     0x15e6eb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15e6eb4: ldr             x1, [x1, #0xa20]
    // 0x15e6eb8: StoreField: r0->field_23 = r1
    //     0x15e6eb8: stur            w1, [x0, #0x23]
    // 0x15e6ebc: r1 = Instance_Clip
    //     0x15e6ebc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15e6ec0: ldr             x1, [x1, #0x38]
    // 0x15e6ec4: StoreField: r0->field_2b = r1
    //     0x15e6ec4: stur            w1, [x0, #0x2b]
    // 0x15e6ec8: StoreField: r0->field_2f = rZR
    //     0x15e6ec8: stur            xzr, [x0, #0x2f]
    // 0x15e6ecc: ldur            x1, [fp, #-8]
    // 0x15e6ed0: StoreField: r0->field_b = r1
    //     0x15e6ed0: stur            w1, [x0, #0xb]
    // 0x15e6ed4: LeaveFrame
    //     0x15e6ed4: mov             SP, fp
    //     0x15e6ed8: ldp             fp, lr, [SP], #0x10
    // 0x15e6edc: ret
    //     0x15e6edc: ret             
    // 0x15e6ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e6ee0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e6ee4: b               #0x15e6ab0
  }
}
