// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/product_collection_poster_carousel.dart

// class id: 1049434, size: 0x8
class :: {
}

// class id: 3317, size: 0x20, field offset: 0x14
class _ProductCollectionPosterCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb80818, size: 0x67c
    // 0xb80818: EnterFrame
    //     0xb80818: stp             fp, lr, [SP, #-0x10]!
    //     0xb8081c: mov             fp, SP
    // 0xb80820: AllocStack(0x68)
    //     0xb80820: sub             SP, SP, #0x68
    // 0xb80824: SetupParameters(_ProductCollectionPosterCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb80824: mov             x0, x1
    //     0xb80828: stur            x1, [fp, #-8]
    //     0xb8082c: mov             x1, x2
    //     0xb80830: stur            x2, [fp, #-0x10]
    // 0xb80834: CheckStackOverflow
    //     0xb80834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb80838: cmp             SP, x16
    //     0xb8083c: b.ls            #0xb80e6c
    // 0xb80840: r1 = 1
    //     0xb80840: movz            x1, #0x1
    // 0xb80844: r0 = AllocateContext()
    //     0xb80844: bl              #0x16f6108  ; AllocateContextStub
    // 0xb80848: mov             x3, x0
    // 0xb8084c: ldur            x0, [fp, #-8]
    // 0xb80850: stur            x3, [fp, #-0x20]
    // 0xb80854: StoreField: r3->field_f = r0
    //     0xb80854: stur            w0, [x3, #0xf]
    // 0xb80858: LoadField: r1 = r0->field_b
    //     0xb80858: ldur            w1, [x0, #0xb]
    // 0xb8085c: DecompressPointer r1
    //     0xb8085c: add             x1, x1, HEAP, lsl #32
    // 0xb80860: cmp             w1, NULL
    // 0xb80864: b.eq            #0xb80e74
    // 0xb80868: LoadField: r2 = r1->field_13
    //     0xb80868: ldur            w2, [x1, #0x13]
    // 0xb8086c: DecompressPointer r2
    //     0xb8086c: add             x2, x2, HEAP, lsl #32
    // 0xb80870: LoadField: r1 = r2->field_7
    //     0xb80870: ldur            w1, [x2, #7]
    // 0xb80874: DecompressPointer r1
    //     0xb80874: add             x1, x1, HEAP, lsl #32
    // 0xb80878: cmp             w1, NULL
    // 0xb8087c: b.ne            #0xb80888
    // 0xb80880: r1 = Instance_TitleAlignment
    //     0xb80880: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb80884: ldr             x1, [x1, #0x518]
    // 0xb80888: r16 = Instance_TitleAlignment
    //     0xb80888: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb8088c: ldr             x16, [x16, #0x520]
    // 0xb80890: cmp             w1, w16
    // 0xb80894: b.ne            #0xb808a4
    // 0xb80898: r4 = Instance_CrossAxisAlignment
    //     0xb80898: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb8089c: ldr             x4, [x4, #0xc68]
    // 0xb808a0: b               #0xb808c8
    // 0xb808a4: r16 = Instance_TitleAlignment
    //     0xb808a4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb808a8: ldr             x16, [x16, #0x518]
    // 0xb808ac: cmp             w1, w16
    // 0xb808b0: b.ne            #0xb808c0
    // 0xb808b4: r4 = Instance_CrossAxisAlignment
    //     0xb808b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb808b8: ldr             x4, [x4, #0x890]
    // 0xb808bc: b               #0xb808c8
    // 0xb808c0: r4 = Instance_CrossAxisAlignment
    //     0xb808c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb808c4: ldr             x4, [x4, #0xa18]
    // 0xb808c8: stur            x4, [fp, #-0x18]
    // 0xb808cc: r1 = <Widget>
    //     0xb808cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb808d0: r2 = 0
    //     0xb808d0: movz            x2, #0
    // 0xb808d4: r0 = _GrowableList()
    //     0xb808d4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb808d8: mov             x2, x0
    // 0xb808dc: ldur            x0, [fp, #-8]
    // 0xb808e0: stur            x2, [fp, #-0x28]
    // 0xb808e4: LoadField: r1 = r0->field_b
    //     0xb808e4: ldur            w1, [x0, #0xb]
    // 0xb808e8: DecompressPointer r1
    //     0xb808e8: add             x1, x1, HEAP, lsl #32
    // 0xb808ec: cmp             w1, NULL
    // 0xb808f0: b.eq            #0xb80e78
    // 0xb808f4: LoadField: r3 = r1->field_f
    //     0xb808f4: ldur            w3, [x1, #0xf]
    // 0xb808f8: DecompressPointer r3
    //     0xb808f8: add             x3, x3, HEAP, lsl #32
    // 0xb808fc: LoadField: r1 = r3->field_7
    //     0xb808fc: ldur            w1, [x3, #7]
    // 0xb80900: cbz             w1, #0xb80a80
    // 0xb80904: mov             x1, x3
    // 0xb80908: r0 = capitalizeFirstWord()
    //     0xb80908: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb8090c: mov             x2, x0
    // 0xb80910: ldur            x0, [fp, #-8]
    // 0xb80914: stur            x2, [fp, #-0x38]
    // 0xb80918: LoadField: r1 = r0->field_b
    //     0xb80918: ldur            w1, [x0, #0xb]
    // 0xb8091c: DecompressPointer r1
    //     0xb8091c: add             x1, x1, HEAP, lsl #32
    // 0xb80920: cmp             w1, NULL
    // 0xb80924: b.eq            #0xb80e7c
    // 0xb80928: LoadField: r3 = r1->field_13
    //     0xb80928: ldur            w3, [x1, #0x13]
    // 0xb8092c: DecompressPointer r3
    //     0xb8092c: add             x3, x3, HEAP, lsl #32
    // 0xb80930: LoadField: r1 = r3->field_7
    //     0xb80930: ldur            w1, [x3, #7]
    // 0xb80934: DecompressPointer r1
    //     0xb80934: add             x1, x1, HEAP, lsl #32
    // 0xb80938: cmp             w1, NULL
    // 0xb8093c: b.ne            #0xb80948
    // 0xb80940: r1 = Instance_TitleAlignment
    //     0xb80940: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb80944: ldr             x1, [x1, #0x518]
    // 0xb80948: r16 = Instance_TitleAlignment
    //     0xb80948: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb8094c: ldr             x16, [x16, #0x520]
    // 0xb80950: cmp             w1, w16
    // 0xb80954: b.ne            #0xb80960
    // 0xb80958: r4 = Instance_TextAlign
    //     0xb80958: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb8095c: b               #0xb8097c
    // 0xb80960: r16 = Instance_TitleAlignment
    //     0xb80960: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb80964: ldr             x16, [x16, #0x518]
    // 0xb80968: cmp             w1, w16
    // 0xb8096c: b.ne            #0xb80978
    // 0xb80970: r4 = Instance_TextAlign
    //     0xb80970: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb80974: b               #0xb8097c
    // 0xb80978: r4 = Instance_TextAlign
    //     0xb80978: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb8097c: ldur            x3, [fp, #-0x28]
    // 0xb80980: ldur            x1, [fp, #-0x10]
    // 0xb80984: stur            x4, [fp, #-0x30]
    // 0xb80988: r0 = of()
    //     0xb80988: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb8098c: LoadField: r1 = r0->field_87
    //     0xb8098c: ldur            w1, [x0, #0x87]
    // 0xb80990: DecompressPointer r1
    //     0xb80990: add             x1, x1, HEAP, lsl #32
    // 0xb80994: LoadField: r0 = r1->field_7
    //     0xb80994: ldur            w0, [x1, #7]
    // 0xb80998: DecompressPointer r0
    //     0xb80998: add             x0, x0, HEAP, lsl #32
    // 0xb8099c: r16 = Instance_Color
    //     0xb8099c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb809a0: r30 = 32.000000
    //     0xb809a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb809a4: ldr             lr, [lr, #0x848]
    // 0xb809a8: stp             lr, x16, [SP]
    // 0xb809ac: mov             x1, x0
    // 0xb809b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb809b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb809b4: ldr             x4, [x4, #0x9b8]
    // 0xb809b8: r0 = copyWith()
    //     0xb809b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb809bc: stur            x0, [fp, #-0x40]
    // 0xb809c0: r0 = Text()
    //     0xb809c0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb809c4: mov             x1, x0
    // 0xb809c8: ldur            x0, [fp, #-0x38]
    // 0xb809cc: stur            x1, [fp, #-0x48]
    // 0xb809d0: StoreField: r1->field_b = r0
    //     0xb809d0: stur            w0, [x1, #0xb]
    // 0xb809d4: ldur            x0, [fp, #-0x40]
    // 0xb809d8: StoreField: r1->field_13 = r0
    //     0xb809d8: stur            w0, [x1, #0x13]
    // 0xb809dc: ldur            x0, [fp, #-0x30]
    // 0xb809e0: StoreField: r1->field_1b = r0
    //     0xb809e0: stur            w0, [x1, #0x1b]
    // 0xb809e4: r0 = Padding()
    //     0xb809e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb809e8: mov             x2, x0
    // 0xb809ec: r0 = Instance_EdgeInsets
    //     0xb809ec: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xb809f0: ldr             x0, [x0, #0xf30]
    // 0xb809f4: stur            x2, [fp, #-0x30]
    // 0xb809f8: StoreField: r2->field_f = r0
    //     0xb809f8: stur            w0, [x2, #0xf]
    // 0xb809fc: ldur            x0, [fp, #-0x48]
    // 0xb80a00: StoreField: r2->field_b = r0
    //     0xb80a00: stur            w0, [x2, #0xb]
    // 0xb80a04: ldur            x0, [fp, #-0x28]
    // 0xb80a08: LoadField: r1 = r0->field_b
    //     0xb80a08: ldur            w1, [x0, #0xb]
    // 0xb80a0c: LoadField: r3 = r0->field_f
    //     0xb80a0c: ldur            w3, [x0, #0xf]
    // 0xb80a10: DecompressPointer r3
    //     0xb80a10: add             x3, x3, HEAP, lsl #32
    // 0xb80a14: LoadField: r4 = r3->field_b
    //     0xb80a14: ldur            w4, [x3, #0xb]
    // 0xb80a18: r3 = LoadInt32Instr(r1)
    //     0xb80a18: sbfx            x3, x1, #1, #0x1f
    // 0xb80a1c: stur            x3, [fp, #-0x50]
    // 0xb80a20: r1 = LoadInt32Instr(r4)
    //     0xb80a20: sbfx            x1, x4, #1, #0x1f
    // 0xb80a24: cmp             x3, x1
    // 0xb80a28: b.ne            #0xb80a34
    // 0xb80a2c: mov             x1, x0
    // 0xb80a30: r0 = _growToNextCapacity()
    //     0xb80a30: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb80a34: ldur            x3, [fp, #-0x28]
    // 0xb80a38: ldur            x2, [fp, #-0x50]
    // 0xb80a3c: add             x0, x2, #1
    // 0xb80a40: lsl             x1, x0, #1
    // 0xb80a44: StoreField: r3->field_b = r1
    //     0xb80a44: stur            w1, [x3, #0xb]
    // 0xb80a48: LoadField: r1 = r3->field_f
    //     0xb80a48: ldur            w1, [x3, #0xf]
    // 0xb80a4c: DecompressPointer r1
    //     0xb80a4c: add             x1, x1, HEAP, lsl #32
    // 0xb80a50: ldur            x0, [fp, #-0x30]
    // 0xb80a54: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb80a54: add             x25, x1, x2, lsl #2
    //     0xb80a58: add             x25, x25, #0xf
    //     0xb80a5c: str             w0, [x25]
    //     0xb80a60: tbz             w0, #0, #0xb80a7c
    //     0xb80a64: ldurb           w16, [x1, #-1]
    //     0xb80a68: ldurb           w17, [x0, #-1]
    //     0xb80a6c: and             x16, x17, x16, lsr #2
    //     0xb80a70: tst             x16, HEAP, lsr #32
    //     0xb80a74: b.eq            #0xb80a7c
    //     0xb80a78: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80a7c: b               #0xb80a84
    // 0xb80a80: mov             x3, x2
    // 0xb80a84: ldur            x0, [fp, #-8]
    // 0xb80a88: LoadField: r1 = r0->field_b
    //     0xb80a88: ldur            w1, [x0, #0xb]
    // 0xb80a8c: DecompressPointer r1
    //     0xb80a8c: add             x1, x1, HEAP, lsl #32
    // 0xb80a90: cmp             w1, NULL
    // 0xb80a94: b.eq            #0xb80e80
    // 0xb80a98: LoadField: r2 = r1->field_b
    //     0xb80a98: ldur            w2, [x1, #0xb]
    // 0xb80a9c: DecompressPointer r2
    //     0xb80a9c: add             x2, x2, HEAP, lsl #32
    // 0xb80aa0: LoadField: r4 = r2->field_b
    //     0xb80aa0: ldur            w4, [x2, #0xb]
    // 0xb80aa4: stur            x4, [fp, #-0x38]
    // 0xb80aa8: LoadField: r5 = r0->field_13
    //     0xb80aa8: ldur            w5, [x0, #0x13]
    // 0xb80aac: DecompressPointer r5
    //     0xb80aac: add             x5, x5, HEAP, lsl #32
    // 0xb80ab0: r16 = Sentinel
    //     0xb80ab0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb80ab4: cmp             w5, w16
    // 0xb80ab8: b.eq            #0xb80e84
    // 0xb80abc: ldur            x2, [fp, #-0x20]
    // 0xb80ac0: stur            x5, [fp, #-0x30]
    // 0xb80ac4: r1 = Function '<anonymous closure>':.
    //     0xb80ac4: add             x1, PP, #0x55, lsl #12  ; [pp+0x557c8] AnonymousClosure: (0xb81764), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::build (0xb80818)
    //     0xb80ac8: ldr             x1, [x1, #0x7c8]
    // 0xb80acc: r0 = AllocateClosure()
    //     0xb80acc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb80ad0: ldur            x2, [fp, #-0x20]
    // 0xb80ad4: r1 = Function '<anonymous closure>':.
    //     0xb80ad4: add             x1, PP, #0x55, lsl #12  ; [pp+0x557d0] AnonymousClosure: (0xb80eb4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::build (0xb80818)
    //     0xb80ad8: ldr             x1, [x1, #0x7d0]
    // 0xb80adc: stur            x0, [fp, #-0x20]
    // 0xb80ae0: r0 = AllocateClosure()
    //     0xb80ae0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb80ae4: stur            x0, [fp, #-0x40]
    // 0xb80ae8: r0 = PageView()
    //     0xb80ae8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb80aec: stur            x0, [fp, #-0x48]
    // 0xb80af0: r16 = Instance_BouncingScrollPhysics
    //     0xb80af0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb80af4: ldr             x16, [x16, #0x890]
    // 0xb80af8: ldur            lr, [fp, #-0x30]
    // 0xb80afc: stp             lr, x16, [SP]
    // 0xb80b00: mov             x1, x0
    // 0xb80b04: ldur            x2, [fp, #-0x40]
    // 0xb80b08: ldur            x3, [fp, #-0x38]
    // 0xb80b0c: ldur            x5, [fp, #-0x20]
    // 0xb80b10: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb80b10: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb80b14: ldr             x4, [x4, #0xe40]
    // 0xb80b18: r0 = PageView.builder()
    //     0xb80b18: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb80b1c: r0 = AspectRatio()
    //     0xb80b1c: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb80b20: d0 = 0.833333
    //     0xb80b20: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4dd30] IMM: double(0.8333333333333334) from 0x3feaaaaaaaaaaaab
    //     0xb80b24: ldr             d0, [x17, #0xd30]
    // 0xb80b28: stur            x0, [fp, #-0x20]
    // 0xb80b2c: StoreField: r0->field_f = d0
    //     0xb80b2c: stur            d0, [x0, #0xf]
    // 0xb80b30: ldur            x1, [fp, #-0x48]
    // 0xb80b34: StoreField: r0->field_b = r1
    //     0xb80b34: stur            w1, [x0, #0xb]
    // 0xb80b38: ldur            x2, [fp, #-0x28]
    // 0xb80b3c: LoadField: r1 = r2->field_b
    //     0xb80b3c: ldur            w1, [x2, #0xb]
    // 0xb80b40: LoadField: r3 = r2->field_f
    //     0xb80b40: ldur            w3, [x2, #0xf]
    // 0xb80b44: DecompressPointer r3
    //     0xb80b44: add             x3, x3, HEAP, lsl #32
    // 0xb80b48: LoadField: r4 = r3->field_b
    //     0xb80b48: ldur            w4, [x3, #0xb]
    // 0xb80b4c: r3 = LoadInt32Instr(r1)
    //     0xb80b4c: sbfx            x3, x1, #1, #0x1f
    // 0xb80b50: stur            x3, [fp, #-0x50]
    // 0xb80b54: r1 = LoadInt32Instr(r4)
    //     0xb80b54: sbfx            x1, x4, #1, #0x1f
    // 0xb80b58: cmp             x3, x1
    // 0xb80b5c: b.ne            #0xb80b68
    // 0xb80b60: mov             x1, x2
    // 0xb80b64: r0 = _growToNextCapacity()
    //     0xb80b64: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb80b68: ldur            x4, [fp, #-8]
    // 0xb80b6c: ldur            x2, [fp, #-0x28]
    // 0xb80b70: ldur            x3, [fp, #-0x50]
    // 0xb80b74: add             x0, x3, #1
    // 0xb80b78: lsl             x1, x0, #1
    // 0xb80b7c: StoreField: r2->field_b = r1
    //     0xb80b7c: stur            w1, [x2, #0xb]
    // 0xb80b80: LoadField: r1 = r2->field_f
    //     0xb80b80: ldur            w1, [x2, #0xf]
    // 0xb80b84: DecompressPointer r1
    //     0xb80b84: add             x1, x1, HEAP, lsl #32
    // 0xb80b88: ldur            x0, [fp, #-0x20]
    // 0xb80b8c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb80b8c: add             x25, x1, x3, lsl #2
    //     0xb80b90: add             x25, x25, #0xf
    //     0xb80b94: str             w0, [x25]
    //     0xb80b98: tbz             w0, #0, #0xb80bb4
    //     0xb80b9c: ldurb           w16, [x1, #-1]
    //     0xb80ba0: ldurb           w17, [x0, #-1]
    //     0xb80ba4: and             x16, x17, x16, lsr #2
    //     0xb80ba8: tst             x16, HEAP, lsr #32
    //     0xb80bac: b.eq            #0xb80bb4
    //     0xb80bb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80bb4: LoadField: r0 = r4->field_b
    //     0xb80bb4: ldur            w0, [x4, #0xb]
    // 0xb80bb8: DecompressPointer r0
    //     0xb80bb8: add             x0, x0, HEAP, lsl #32
    // 0xb80bbc: cmp             w0, NULL
    // 0xb80bc0: b.eq            #0xb80e90
    // 0xb80bc4: LoadField: r1 = r0->field_b
    //     0xb80bc4: ldur            w1, [x0, #0xb]
    // 0xb80bc8: DecompressPointer r1
    //     0xb80bc8: add             x1, x1, HEAP, lsl #32
    // 0xb80bcc: LoadField: r0 = r1->field_b
    //     0xb80bcc: ldur            w0, [x1, #0xb]
    // 0xb80bd0: r3 = LoadInt32Instr(r0)
    //     0xb80bd0: sbfx            x3, x0, #1, #0x1f
    // 0xb80bd4: stur            x3, [fp, #-0x58]
    // 0xb80bd8: cmp             x3, #1
    // 0xb80bdc: b.le            #0xb80d60
    // 0xb80be0: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xb80be0: ldur            x0, [x4, #0x17]
    // 0xb80be4: ldur            x1, [fp, #-0x10]
    // 0xb80be8: stur            x0, [fp, #-0x50]
    // 0xb80bec: r0 = of()
    //     0xb80bec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb80bf0: LoadField: r1 = r0->field_5b
    //     0xb80bf0: ldur            w1, [x0, #0x5b]
    // 0xb80bf4: DecompressPointer r1
    //     0xb80bf4: add             x1, x1, HEAP, lsl #32
    // 0xb80bf8: stur            x1, [fp, #-8]
    // 0xb80bfc: r0 = CarouselIndicator()
    //     0xb80bfc: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb80c00: mov             x3, x0
    // 0xb80c04: ldur            x0, [fp, #-0x58]
    // 0xb80c08: stur            x3, [fp, #-0x10]
    // 0xb80c0c: StoreField: r3->field_b = r0
    //     0xb80c0c: stur            x0, [x3, #0xb]
    // 0xb80c10: ldur            x0, [fp, #-0x50]
    // 0xb80c14: StoreField: r3->field_13 = r0
    //     0xb80c14: stur            x0, [x3, #0x13]
    // 0xb80c18: ldur            x0, [fp, #-8]
    // 0xb80c1c: StoreField: r3->field_1b = r0
    //     0xb80c1c: stur            w0, [x3, #0x1b]
    // 0xb80c20: r0 = Instance_Color
    //     0xb80c20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb80c24: ldr             x0, [x0, #0x90]
    // 0xb80c28: StoreField: r3->field_1f = r0
    //     0xb80c28: stur            w0, [x3, #0x1f]
    // 0xb80c2c: r1 = Null
    //     0xb80c2c: mov             x1, NULL
    // 0xb80c30: r2 = 2
    //     0xb80c30: movz            x2, #0x2
    // 0xb80c34: r0 = AllocateArray()
    //     0xb80c34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb80c38: mov             x2, x0
    // 0xb80c3c: ldur            x0, [fp, #-0x10]
    // 0xb80c40: stur            x2, [fp, #-8]
    // 0xb80c44: StoreField: r2->field_f = r0
    //     0xb80c44: stur            w0, [x2, #0xf]
    // 0xb80c48: r1 = <Widget>
    //     0xb80c48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb80c4c: r0 = AllocateGrowableArray()
    //     0xb80c4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb80c50: mov             x1, x0
    // 0xb80c54: ldur            x0, [fp, #-8]
    // 0xb80c58: stur            x1, [fp, #-0x10]
    // 0xb80c5c: StoreField: r1->field_f = r0
    //     0xb80c5c: stur            w0, [x1, #0xf]
    // 0xb80c60: r0 = 2
    //     0xb80c60: movz            x0, #0x2
    // 0xb80c64: StoreField: r1->field_b = r0
    //     0xb80c64: stur            w0, [x1, #0xb]
    // 0xb80c68: r0 = Row()
    //     0xb80c68: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb80c6c: mov             x1, x0
    // 0xb80c70: r0 = Instance_Axis
    //     0xb80c70: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb80c74: stur            x1, [fp, #-8]
    // 0xb80c78: StoreField: r1->field_f = r0
    //     0xb80c78: stur            w0, [x1, #0xf]
    // 0xb80c7c: r0 = Instance_MainAxisAlignment
    //     0xb80c7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb80c80: ldr             x0, [x0, #0xab0]
    // 0xb80c84: StoreField: r1->field_13 = r0
    //     0xb80c84: stur            w0, [x1, #0x13]
    // 0xb80c88: r0 = Instance_MainAxisSize
    //     0xb80c88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb80c8c: ldr             x0, [x0, #0xa10]
    // 0xb80c90: ArrayStore: r1[0] = r0  ; List_4
    //     0xb80c90: stur            w0, [x1, #0x17]
    // 0xb80c94: r0 = Instance_CrossAxisAlignment
    //     0xb80c94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb80c98: ldr             x0, [x0, #0xa18]
    // 0xb80c9c: StoreField: r1->field_1b = r0
    //     0xb80c9c: stur            w0, [x1, #0x1b]
    // 0xb80ca0: r0 = Instance_VerticalDirection
    //     0xb80ca0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb80ca4: ldr             x0, [x0, #0xa20]
    // 0xb80ca8: StoreField: r1->field_23 = r0
    //     0xb80ca8: stur            w0, [x1, #0x23]
    // 0xb80cac: r2 = Instance_Clip
    //     0xb80cac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb80cb0: ldr             x2, [x2, #0x38]
    // 0xb80cb4: StoreField: r1->field_2b = r2
    //     0xb80cb4: stur            w2, [x1, #0x2b]
    // 0xb80cb8: StoreField: r1->field_2f = rZR
    //     0xb80cb8: stur            xzr, [x1, #0x2f]
    // 0xb80cbc: ldur            x3, [fp, #-0x10]
    // 0xb80cc0: StoreField: r1->field_b = r3
    //     0xb80cc0: stur            w3, [x1, #0xb]
    // 0xb80cc4: r0 = Padding()
    //     0xb80cc4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb80cc8: mov             x2, x0
    // 0xb80ccc: r0 = Instance_EdgeInsets
    //     0xb80ccc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb80cd0: ldr             x0, [x0, #0xa00]
    // 0xb80cd4: stur            x2, [fp, #-0x10]
    // 0xb80cd8: StoreField: r2->field_f = r0
    //     0xb80cd8: stur            w0, [x2, #0xf]
    // 0xb80cdc: ldur            x0, [fp, #-8]
    // 0xb80ce0: StoreField: r2->field_b = r0
    //     0xb80ce0: stur            w0, [x2, #0xb]
    // 0xb80ce4: ldur            x0, [fp, #-0x28]
    // 0xb80ce8: LoadField: r1 = r0->field_b
    //     0xb80ce8: ldur            w1, [x0, #0xb]
    // 0xb80cec: LoadField: r3 = r0->field_f
    //     0xb80cec: ldur            w3, [x0, #0xf]
    // 0xb80cf0: DecompressPointer r3
    //     0xb80cf0: add             x3, x3, HEAP, lsl #32
    // 0xb80cf4: LoadField: r4 = r3->field_b
    //     0xb80cf4: ldur            w4, [x3, #0xb]
    // 0xb80cf8: r3 = LoadInt32Instr(r1)
    //     0xb80cf8: sbfx            x3, x1, #1, #0x1f
    // 0xb80cfc: stur            x3, [fp, #-0x50]
    // 0xb80d00: r1 = LoadInt32Instr(r4)
    //     0xb80d00: sbfx            x1, x4, #1, #0x1f
    // 0xb80d04: cmp             x3, x1
    // 0xb80d08: b.ne            #0xb80d14
    // 0xb80d0c: mov             x1, x0
    // 0xb80d10: r0 = _growToNextCapacity()
    //     0xb80d10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb80d14: ldur            x2, [fp, #-0x28]
    // 0xb80d18: ldur            x3, [fp, #-0x50]
    // 0xb80d1c: add             x0, x3, #1
    // 0xb80d20: lsl             x1, x0, #1
    // 0xb80d24: StoreField: r2->field_b = r1
    //     0xb80d24: stur            w1, [x2, #0xb]
    // 0xb80d28: LoadField: r1 = r2->field_f
    //     0xb80d28: ldur            w1, [x2, #0xf]
    // 0xb80d2c: DecompressPointer r1
    //     0xb80d2c: add             x1, x1, HEAP, lsl #32
    // 0xb80d30: ldur            x0, [fp, #-0x10]
    // 0xb80d34: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb80d34: add             x25, x1, x3, lsl #2
    //     0xb80d38: add             x25, x25, #0xf
    //     0xb80d3c: str             w0, [x25]
    //     0xb80d40: tbz             w0, #0, #0xb80d5c
    //     0xb80d44: ldurb           w16, [x1, #-1]
    //     0xb80d48: ldurb           w17, [x0, #-1]
    //     0xb80d4c: and             x16, x17, x16, lsr #2
    //     0xb80d50: tst             x16, HEAP, lsr #32
    //     0xb80d54: b.eq            #0xb80d5c
    //     0xb80d58: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80d5c: b               #0xb80dec
    // 0xb80d60: r0 = Container()
    //     0xb80d60: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb80d64: mov             x1, x0
    // 0xb80d68: stur            x0, [fp, #-8]
    // 0xb80d6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb80d6c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb80d70: r0 = Container()
    //     0xb80d70: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb80d74: ldur            x0, [fp, #-0x28]
    // 0xb80d78: LoadField: r1 = r0->field_b
    //     0xb80d78: ldur            w1, [x0, #0xb]
    // 0xb80d7c: LoadField: r2 = r0->field_f
    //     0xb80d7c: ldur            w2, [x0, #0xf]
    // 0xb80d80: DecompressPointer r2
    //     0xb80d80: add             x2, x2, HEAP, lsl #32
    // 0xb80d84: LoadField: r3 = r2->field_b
    //     0xb80d84: ldur            w3, [x2, #0xb]
    // 0xb80d88: r2 = LoadInt32Instr(r1)
    //     0xb80d88: sbfx            x2, x1, #1, #0x1f
    // 0xb80d8c: stur            x2, [fp, #-0x50]
    // 0xb80d90: r1 = LoadInt32Instr(r3)
    //     0xb80d90: sbfx            x1, x3, #1, #0x1f
    // 0xb80d94: cmp             x2, x1
    // 0xb80d98: b.ne            #0xb80da4
    // 0xb80d9c: mov             x1, x0
    // 0xb80da0: r0 = _growToNextCapacity()
    //     0xb80da0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb80da4: ldur            x2, [fp, #-0x28]
    // 0xb80da8: ldur            x3, [fp, #-0x50]
    // 0xb80dac: add             x0, x3, #1
    // 0xb80db0: lsl             x1, x0, #1
    // 0xb80db4: StoreField: r2->field_b = r1
    //     0xb80db4: stur            w1, [x2, #0xb]
    // 0xb80db8: LoadField: r1 = r2->field_f
    //     0xb80db8: ldur            w1, [x2, #0xf]
    // 0xb80dbc: DecompressPointer r1
    //     0xb80dbc: add             x1, x1, HEAP, lsl #32
    // 0xb80dc0: ldur            x0, [fp, #-8]
    // 0xb80dc4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb80dc4: add             x25, x1, x3, lsl #2
    //     0xb80dc8: add             x25, x25, #0xf
    //     0xb80dcc: str             w0, [x25]
    //     0xb80dd0: tbz             w0, #0, #0xb80dec
    //     0xb80dd4: ldurb           w16, [x1, #-1]
    //     0xb80dd8: ldurb           w17, [x0, #-1]
    //     0xb80ddc: and             x16, x17, x16, lsr #2
    //     0xb80de0: tst             x16, HEAP, lsr #32
    //     0xb80de4: b.eq            #0xb80dec
    //     0xb80de8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb80dec: ldur            x0, [fp, #-0x18]
    // 0xb80df0: r0 = Column()
    //     0xb80df0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb80df4: mov             x1, x0
    // 0xb80df8: r0 = Instance_Axis
    //     0xb80df8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb80dfc: stur            x1, [fp, #-8]
    // 0xb80e00: StoreField: r1->field_f = r0
    //     0xb80e00: stur            w0, [x1, #0xf]
    // 0xb80e04: r0 = Instance_MainAxisAlignment
    //     0xb80e04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb80e08: ldr             x0, [x0, #0xa08]
    // 0xb80e0c: StoreField: r1->field_13 = r0
    //     0xb80e0c: stur            w0, [x1, #0x13]
    // 0xb80e10: r0 = Instance_MainAxisSize
    //     0xb80e10: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb80e14: ldr             x0, [x0, #0xdd0]
    // 0xb80e18: ArrayStore: r1[0] = r0  ; List_4
    //     0xb80e18: stur            w0, [x1, #0x17]
    // 0xb80e1c: ldur            x0, [fp, #-0x18]
    // 0xb80e20: StoreField: r1->field_1b = r0
    //     0xb80e20: stur            w0, [x1, #0x1b]
    // 0xb80e24: r0 = Instance_VerticalDirection
    //     0xb80e24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb80e28: ldr             x0, [x0, #0xa20]
    // 0xb80e2c: StoreField: r1->field_23 = r0
    //     0xb80e2c: stur            w0, [x1, #0x23]
    // 0xb80e30: r0 = Instance_Clip
    //     0xb80e30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb80e34: ldr             x0, [x0, #0x38]
    // 0xb80e38: StoreField: r1->field_2b = r0
    //     0xb80e38: stur            w0, [x1, #0x2b]
    // 0xb80e3c: StoreField: r1->field_2f = rZR
    //     0xb80e3c: stur            xzr, [x1, #0x2f]
    // 0xb80e40: ldur            x0, [fp, #-0x28]
    // 0xb80e44: StoreField: r1->field_b = r0
    //     0xb80e44: stur            w0, [x1, #0xb]
    // 0xb80e48: r0 = Padding()
    //     0xb80e48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb80e4c: r1 = Instance_EdgeInsets
    //     0xb80e4c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb80e50: ldr             x1, [x1, #0x240]
    // 0xb80e54: StoreField: r0->field_f = r1
    //     0xb80e54: stur            w1, [x0, #0xf]
    // 0xb80e58: ldur            x1, [fp, #-8]
    // 0xb80e5c: StoreField: r0->field_b = r1
    //     0xb80e5c: stur            w1, [x0, #0xb]
    // 0xb80e60: LeaveFrame
    //     0xb80e60: mov             SP, fp
    //     0xb80e64: ldp             fp, lr, [SP], #0x10
    // 0xb80e68: ret
    //     0xb80e68: ret             
    // 0xb80e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb80e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb80e70: b               #0xb80840
    // 0xb80e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80e74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80e78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80e7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80e7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80e80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb80e84: r9 = _pageController
    //     0xb80e84: add             x9, PP, #0x55, lsl #12  ; [pp+0x557d8] Field <_ProductCollectionPosterCarouselState@1619300699._pageController@1619300699>: late (offset: 0x14)
    //     0xb80e88: ldr             x9, [x9, #0x7d8]
    // 0xb80e8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb80e8c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb80e90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80e90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb80eb4, size: 0x90
    // 0xb80eb4: EnterFrame
    //     0xb80eb4: stp             fp, lr, [SP, #-0x10]!
    //     0xb80eb8: mov             fp, SP
    // 0xb80ebc: AllocStack(0x8)
    //     0xb80ebc: sub             SP, SP, #8
    // 0xb80ec0: SetupParameters()
    //     0xb80ec0: ldr             x0, [fp, #0x20]
    //     0xb80ec4: ldur            w1, [x0, #0x17]
    //     0xb80ec8: add             x1, x1, HEAP, lsl #32
    // 0xb80ecc: CheckStackOverflow
    //     0xb80ecc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb80ed0: cmp             SP, x16
    //     0xb80ed4: b.ls            #0xb80f38
    // 0xb80ed8: LoadField: r0 = r1->field_f
    //     0xb80ed8: ldur            w0, [x1, #0xf]
    // 0xb80edc: DecompressPointer r0
    //     0xb80edc: add             x0, x0, HEAP, lsl #32
    // 0xb80ee0: LoadField: r1 = r0->field_b
    //     0xb80ee0: ldur            w1, [x0, #0xb]
    // 0xb80ee4: DecompressPointer r1
    //     0xb80ee4: add             x1, x1, HEAP, lsl #32
    // 0xb80ee8: cmp             w1, NULL
    // 0xb80eec: b.eq            #0xb80f40
    // 0xb80ef0: LoadField: r2 = r1->field_b
    //     0xb80ef0: ldur            w2, [x1, #0xb]
    // 0xb80ef4: DecompressPointer r2
    //     0xb80ef4: add             x2, x2, HEAP, lsl #32
    // 0xb80ef8: ldr             x1, [fp, #0x10]
    // 0xb80efc: r3 = LoadInt32Instr(r1)
    //     0xb80efc: sbfx            x3, x1, #1, #0x1f
    //     0xb80f00: tbz             w1, #0, #0xb80f08
    //     0xb80f04: ldur            x3, [x1, #7]
    // 0xb80f08: mov             x1, x0
    // 0xb80f0c: r0 = glassThemeSlider()
    //     0xb80f0c: bl              #0xb80f44  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::glassThemeSlider
    // 0xb80f10: stur            x0, [fp, #-8]
    // 0xb80f14: r0 = Padding()
    //     0xb80f14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb80f18: r1 = Instance_EdgeInsets
    //     0xb80f18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb80f1c: ldr             x1, [x1, #0x668]
    // 0xb80f20: StoreField: r0->field_f = r1
    //     0xb80f20: stur            w1, [x0, #0xf]
    // 0xb80f24: ldur            x1, [fp, #-8]
    // 0xb80f28: StoreField: r0->field_b = r1
    //     0xb80f28: stur            w1, [x0, #0xb]
    // 0xb80f2c: LeaveFrame
    //     0xb80f2c: mov             SP, fp
    //     0xb80f30: ldp             fp, lr, [SP], #0x10
    // 0xb80f34: ret
    //     0xb80f34: ret             
    // 0xb80f38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb80f38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb80f3c: b               #0xb80ed8
    // 0xb80f40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb80f40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ glassThemeSlider(/* No info */) {
    // ** addr: 0xb80f44, size: 0x820
    // 0xb80f44: EnterFrame
    //     0xb80f44: stp             fp, lr, [SP, #-0x10]!
    //     0xb80f48: mov             fp, SP
    // 0xb80f4c: AllocStack(0x58)
    //     0xb80f4c: sub             SP, SP, #0x58
    // 0xb80f50: SetupParameters(_ProductCollectionPosterCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb80f50: stur            x1, [fp, #-8]
    //     0xb80f54: stur            x2, [fp, #-0x10]
    //     0xb80f58: stur            x3, [fp, #-0x18]
    // 0xb80f5c: CheckStackOverflow
    //     0xb80f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb80f60: cmp             SP, x16
    //     0xb80f64: b.ls            #0xb8173c
    // 0xb80f68: r1 = 3
    //     0xb80f68: movz            x1, #0x3
    // 0xb80f6c: r0 = AllocateContext()
    //     0xb80f6c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb80f70: mov             x3, x0
    // 0xb80f74: ldur            x2, [fp, #-8]
    // 0xb80f78: stur            x3, [fp, #-0x20]
    // 0xb80f7c: StoreField: r3->field_f = r2
    //     0xb80f7c: stur            w2, [x3, #0xf]
    // 0xb80f80: ldur            x0, [fp, #-0x10]
    // 0xb80f84: StoreField: r3->field_13 = r0
    //     0xb80f84: stur            w0, [x3, #0x13]
    // 0xb80f88: ldur            x4, [fp, #-0x18]
    // 0xb80f8c: r0 = BoxInt64Instr(r4)
    //     0xb80f8c: sbfiz           x0, x4, #1, #0x1f
    //     0xb80f90: cmp             x4, x0, asr #1
    //     0xb80f94: b.eq            #0xb80fa0
    //     0xb80f98: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb80f9c: stur            x4, [x0, #7]
    // 0xb80fa0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb80fa0: stur            w0, [x3, #0x17]
    // 0xb80fa4: LoadField: r1 = r2->field_f
    //     0xb80fa4: ldur            w1, [x2, #0xf]
    // 0xb80fa8: DecompressPointer r1
    //     0xb80fa8: add             x1, x1, HEAP, lsl #32
    // 0xb80fac: cmp             w1, NULL
    // 0xb80fb0: b.eq            #0xb81744
    // 0xb80fb4: r0 = of()
    //     0xb80fb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb80fb8: LoadField: r1 = r0->field_5b
    //     0xb80fb8: ldur            w1, [x0, #0x5b]
    // 0xb80fbc: DecompressPointer r1
    //     0xb80fbc: add             x1, x1, HEAP, lsl #32
    // 0xb80fc0: r0 = LoadClassIdInstr(r1)
    //     0xb80fc0: ldur            x0, [x1, #-1]
    //     0xb80fc4: ubfx            x0, x0, #0xc, #0x14
    // 0xb80fc8: d0 = 0.030000
    //     0xb80fc8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb80fcc: ldr             d0, [x17, #0x238]
    // 0xb80fd0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb80fd0: sub             lr, x0, #0xffa
    //     0xb80fd4: ldr             lr, [x21, lr, lsl #3]
    //     0xb80fd8: blr             lr
    // 0xb80fdc: stur            x0, [fp, #-0x10]
    // 0xb80fe0: r0 = Radius()
    //     0xb80fe0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb80fe4: d0 = 20.000000
    //     0xb80fe4: fmov            d0, #20.00000000
    // 0xb80fe8: stur            x0, [fp, #-0x28]
    // 0xb80fec: StoreField: r0->field_7 = d0
    //     0xb80fec: stur            d0, [x0, #7]
    // 0xb80ff0: StoreField: r0->field_f = d0
    //     0xb80ff0: stur            d0, [x0, #0xf]
    // 0xb80ff4: r0 = BorderRadius()
    //     0xb80ff4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb80ff8: mov             x1, x0
    // 0xb80ffc: ldur            x0, [fp, #-0x28]
    // 0xb81000: stur            x1, [fp, #-0x30]
    // 0xb81004: StoreField: r1->field_7 = r0
    //     0xb81004: stur            w0, [x1, #7]
    // 0xb81008: StoreField: r1->field_b = r0
    //     0xb81008: stur            w0, [x1, #0xb]
    // 0xb8100c: StoreField: r1->field_f = r0
    //     0xb8100c: stur            w0, [x1, #0xf]
    // 0xb81010: StoreField: r1->field_13 = r0
    //     0xb81010: stur            w0, [x1, #0x13]
    // 0xb81014: r0 = BoxDecoration()
    //     0xb81014: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb81018: mov             x3, x0
    // 0xb8101c: ldur            x0, [fp, #-0x10]
    // 0xb81020: stur            x3, [fp, #-0x28]
    // 0xb81024: StoreField: r3->field_7 = r0
    //     0xb81024: stur            w0, [x3, #7]
    // 0xb81028: ldur            x0, [fp, #-0x30]
    // 0xb8102c: StoreField: r3->field_13 = r0
    //     0xb8102c: stur            w0, [x3, #0x13]
    // 0xb81030: r4 = Instance_BoxShape
    //     0xb81030: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb81034: ldr             x4, [x4, #0x80]
    // 0xb81038: StoreField: r3->field_23 = r4
    //     0xb81038: stur            w4, [x3, #0x23]
    // 0xb8103c: ldur            x5, [fp, #-0x20]
    // 0xb81040: LoadField: r2 = r5->field_13
    //     0xb81040: ldur            w2, [x5, #0x13]
    // 0xb81044: DecompressPointer r2
    //     0xb81044: add             x2, x2, HEAP, lsl #32
    // 0xb81048: ArrayLoad: r0 = r5[0]  ; List_4
    //     0xb81048: ldur            w0, [x5, #0x17]
    // 0xb8104c: DecompressPointer r0
    //     0xb8104c: add             x0, x0, HEAP, lsl #32
    // 0xb81050: LoadField: r1 = r2->field_b
    //     0xb81050: ldur            w1, [x2, #0xb]
    // 0xb81054: r6 = LoadInt32Instr(r0)
    //     0xb81054: sbfx            x6, x0, #1, #0x1f
    //     0xb81058: tbz             w0, #0, #0xb81060
    //     0xb8105c: ldur            x6, [x0, #7]
    // 0xb81060: r0 = LoadInt32Instr(r1)
    //     0xb81060: sbfx            x0, x1, #1, #0x1f
    // 0xb81064: mov             x1, x6
    // 0xb81068: cmp             x1, x0
    // 0xb8106c: b.hs            #0xb81748
    // 0xb81070: LoadField: r0 = r2->field_f
    //     0xb81070: ldur            w0, [x2, #0xf]
    // 0xb81074: DecompressPointer r0
    //     0xb81074: add             x0, x0, HEAP, lsl #32
    // 0xb81078: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb81078: add             x16, x0, x6, lsl #2
    //     0xb8107c: ldur            w1, [x16, #0xf]
    // 0xb81080: DecompressPointer r1
    //     0xb81080: add             x1, x1, HEAP, lsl #32
    // 0xb81084: LoadField: r0 = r1->field_13
    //     0xb81084: ldur            w0, [x1, #0x13]
    // 0xb81088: DecompressPointer r0
    //     0xb81088: add             x0, x0, HEAP, lsl #32
    // 0xb8108c: stur            x0, [fp, #-0x10]
    // 0xb81090: cmp             w0, NULL
    // 0xb81094: b.eq            #0xb8174c
    // 0xb81098: r1 = Function '<anonymous closure>':.
    //     0xb81098: add             x1, PP, #0x55, lsl #12  ; [pp+0x557e0] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb8109c: ldr             x1, [x1, #0x7e0]
    // 0xb810a0: r2 = Null
    //     0xb810a0: mov             x2, NULL
    // 0xb810a4: r0 = AllocateClosure()
    //     0xb810a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb810a8: r1 = Function '<anonymous closure>':.
    //     0xb810a8: add             x1, PP, #0x55, lsl #12  ; [pp+0x557e8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb810ac: ldr             x1, [x1, #0x7e8]
    // 0xb810b0: r2 = Null
    //     0xb810b0: mov             x2, NULL
    // 0xb810b4: stur            x0, [fp, #-0x30]
    // 0xb810b8: r0 = AllocateClosure()
    //     0xb810b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb810bc: stur            x0, [fp, #-0x38]
    // 0xb810c0: r0 = CachedNetworkImage()
    //     0xb810c0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb810c4: stur            x0, [fp, #-0x40]
    // 0xb810c8: ldur            x16, [fp, #-0x30]
    // 0xb810cc: ldur            lr, [fp, #-0x38]
    // 0xb810d0: stp             lr, x16, [SP, #8]
    // 0xb810d4: r16 = Instance_BoxFit
    //     0xb810d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb810d8: ldr             x16, [x16, #0x118]
    // 0xb810dc: str             x16, [SP]
    // 0xb810e0: mov             x1, x0
    // 0xb810e4: ldur            x2, [fp, #-0x10]
    // 0xb810e8: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb810e8: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb810ec: ldr             x4, [x4, #0x790]
    // 0xb810f0: r0 = CachedNetworkImage()
    //     0xb810f0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb810f4: r0 = ClipRRect()
    //     0xb810f4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb810f8: mov             x1, x0
    // 0xb810fc: r0 = Instance_BorderRadius
    //     0xb810fc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e18] Obj!BorderRadius@d5a1e1
    //     0xb81100: ldr             x0, [x0, #0xe18]
    // 0xb81104: stur            x1, [fp, #-0x10]
    // 0xb81108: StoreField: r1->field_f = r0
    //     0xb81108: stur            w0, [x1, #0xf]
    // 0xb8110c: r0 = Instance_Clip
    //     0xb8110c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb81110: ldr             x0, [x0, #0x138]
    // 0xb81114: ArrayStore: r1[0] = r0  ; List_4
    //     0xb81114: stur            w0, [x1, #0x17]
    // 0xb81118: ldur            x0, [fp, #-0x40]
    // 0xb8111c: StoreField: r1->field_b = r0
    //     0xb8111c: stur            w0, [x1, #0xb]
    // 0xb81120: r0 = AspectRatio()
    //     0xb81120: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb81124: mov             x3, x0
    // 0xb81128: d0 = 1.000000
    //     0xb81128: fmov            d0, #1.00000000
    // 0xb8112c: stur            x3, [fp, #-0x30]
    // 0xb81130: StoreField: r3->field_f = d0
    //     0xb81130: stur            d0, [x3, #0xf]
    // 0xb81134: ldur            x0, [fp, #-0x10]
    // 0xb81138: StoreField: r3->field_b = r0
    //     0xb81138: stur            w0, [x3, #0xb]
    // 0xb8113c: ldur            x4, [fp, #-0x20]
    // 0xb81140: LoadField: r2 = r4->field_13
    //     0xb81140: ldur            w2, [x4, #0x13]
    // 0xb81144: DecompressPointer r2
    //     0xb81144: add             x2, x2, HEAP, lsl #32
    // 0xb81148: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb81148: ldur            w0, [x4, #0x17]
    // 0xb8114c: DecompressPointer r0
    //     0xb8114c: add             x0, x0, HEAP, lsl #32
    // 0xb81150: LoadField: r1 = r2->field_b
    //     0xb81150: ldur            w1, [x2, #0xb]
    // 0xb81154: r5 = LoadInt32Instr(r0)
    //     0xb81154: sbfx            x5, x0, #1, #0x1f
    //     0xb81158: tbz             w0, #0, #0xb81160
    //     0xb8115c: ldur            x5, [x0, #7]
    // 0xb81160: r0 = LoadInt32Instr(r1)
    //     0xb81160: sbfx            x0, x1, #1, #0x1f
    // 0xb81164: mov             x1, x5
    // 0xb81168: cmp             x1, x0
    // 0xb8116c: b.hs            #0xb81750
    // 0xb81170: LoadField: r0 = r2->field_f
    //     0xb81170: ldur            w0, [x2, #0xf]
    // 0xb81174: DecompressPointer r0
    //     0xb81174: add             x0, x0, HEAP, lsl #32
    // 0xb81178: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb81178: add             x16, x0, x5, lsl #2
    //     0xb8117c: ldur            w1, [x16, #0xf]
    // 0xb81180: DecompressPointer r1
    //     0xb81180: add             x1, x1, HEAP, lsl #32
    // 0xb81184: LoadField: r5 = r1->field_7
    //     0xb81184: ldur            w5, [x1, #7]
    // 0xb81188: DecompressPointer r5
    //     0xb81188: add             x5, x5, HEAP, lsl #32
    // 0xb8118c: mov             x0, x5
    // 0xb81190: stur            x5, [fp, #-0x10]
    // 0xb81194: r2 = Null
    //     0xb81194: mov             x2, NULL
    // 0xb81198: r1 = Null
    //     0xb81198: mov             x1, NULL
    // 0xb8119c: r4 = LoadClassIdInstr(r0)
    //     0xb8119c: ldur            x4, [x0, #-1]
    //     0xb811a0: ubfx            x4, x4, #0xc, #0x14
    // 0xb811a4: sub             x4, x4, #0x5e
    // 0xb811a8: cmp             x4, #1
    // 0xb811ac: b.ls            #0xb811c0
    // 0xb811b0: r8 = String
    //     0xb811b0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb811b4: r3 = Null
    //     0xb811b4: add             x3, PP, #0x55, lsl #12  ; [pp+0x557f0] Null
    //     0xb811b8: ldr             x3, [x3, #0x7f0]
    // 0xb811bc: r0 = String()
    //     0xb811bc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb811c0: ldur            x0, [fp, #-8]
    // 0xb811c4: LoadField: r1 = r0->field_f
    //     0xb811c4: ldur            w1, [x0, #0xf]
    // 0xb811c8: DecompressPointer r1
    //     0xb811c8: add             x1, x1, HEAP, lsl #32
    // 0xb811cc: cmp             w1, NULL
    // 0xb811d0: b.eq            #0xb81754
    // 0xb811d4: r0 = of()
    //     0xb811d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb811d8: LoadField: r1 = r0->field_87
    //     0xb811d8: ldur            w1, [x0, #0x87]
    // 0xb811dc: DecompressPointer r1
    //     0xb811dc: add             x1, x1, HEAP, lsl #32
    // 0xb811e0: LoadField: r0 = r1->field_7
    //     0xb811e0: ldur            w0, [x1, #7]
    // 0xb811e4: DecompressPointer r0
    //     0xb811e4: add             x0, x0, HEAP, lsl #32
    // 0xb811e8: r16 = Instance_Color
    //     0xb811e8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb811ec: r30 = 14.000000
    //     0xb811ec: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb811f0: ldr             lr, [lr, #0x1d8]
    // 0xb811f4: stp             lr, x16, [SP]
    // 0xb811f8: mov             x1, x0
    // 0xb811fc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb811fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb81200: ldr             x4, [x4, #0x9b8]
    // 0xb81204: r0 = copyWith()
    //     0xb81204: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb81208: stur            x0, [fp, #-0x38]
    // 0xb8120c: r0 = Text()
    //     0xb8120c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb81210: mov             x1, x0
    // 0xb81214: ldur            x0, [fp, #-0x10]
    // 0xb81218: stur            x1, [fp, #-0x40]
    // 0xb8121c: StoreField: r1->field_b = r0
    //     0xb8121c: stur            w0, [x1, #0xb]
    // 0xb81220: ldur            x0, [fp, #-0x38]
    // 0xb81224: StoreField: r1->field_13 = r0
    //     0xb81224: stur            w0, [x1, #0x13]
    // 0xb81228: r0 = Padding()
    //     0xb81228: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb8122c: mov             x3, x0
    // 0xb81230: r0 = Instance_EdgeInsets
    //     0xb81230: add             x0, PP, #0x55, lsl #12  ; [pp+0x55800] Obj!EdgeInsets@d59331
    //     0xb81234: ldr             x0, [x0, #0x800]
    // 0xb81238: stur            x3, [fp, #-0x10]
    // 0xb8123c: StoreField: r3->field_f = r0
    //     0xb8123c: stur            w0, [x3, #0xf]
    // 0xb81240: ldur            x0, [fp, #-0x40]
    // 0xb81244: StoreField: r3->field_b = r0
    //     0xb81244: stur            w0, [x3, #0xb]
    // 0xb81248: r1 = Null
    //     0xb81248: mov             x1, NULL
    // 0xb8124c: r2 = 4
    //     0xb8124c: movz            x2, #0x4
    // 0xb81250: r0 = AllocateArray()
    //     0xb81250: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb81254: mov             x2, x0
    // 0xb81258: ldur            x0, [fp, #-0x30]
    // 0xb8125c: stur            x2, [fp, #-0x38]
    // 0xb81260: StoreField: r2->field_f = r0
    //     0xb81260: stur            w0, [x2, #0xf]
    // 0xb81264: ldur            x0, [fp, #-0x10]
    // 0xb81268: StoreField: r2->field_13 = r0
    //     0xb81268: stur            w0, [x2, #0x13]
    // 0xb8126c: r1 = <Widget>
    //     0xb8126c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb81270: r0 = AllocateGrowableArray()
    //     0xb81270: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb81274: mov             x3, x0
    // 0xb81278: ldur            x0, [fp, #-0x38]
    // 0xb8127c: stur            x3, [fp, #-0x30]
    // 0xb81280: StoreField: r3->field_f = r0
    //     0xb81280: stur            w0, [x3, #0xf]
    // 0xb81284: r0 = 4
    //     0xb81284: movz            x0, #0x4
    // 0xb81288: StoreField: r3->field_b = r0
    //     0xb81288: stur            w0, [x3, #0xb]
    // 0xb8128c: ldur            x4, [fp, #-0x20]
    // 0xb81290: LoadField: r2 = r4->field_13
    //     0xb81290: ldur            w2, [x4, #0x13]
    // 0xb81294: DecompressPointer r2
    //     0xb81294: add             x2, x2, HEAP, lsl #32
    // 0xb81298: ArrayLoad: r0 = r4[0]  ; List_4
    //     0xb81298: ldur            w0, [x4, #0x17]
    // 0xb8129c: DecompressPointer r0
    //     0xb8129c: add             x0, x0, HEAP, lsl #32
    // 0xb812a0: LoadField: r1 = r2->field_b
    //     0xb812a0: ldur            w1, [x2, #0xb]
    // 0xb812a4: r5 = LoadInt32Instr(r0)
    //     0xb812a4: sbfx            x5, x0, #1, #0x1f
    //     0xb812a8: tbz             w0, #0, #0xb812b0
    //     0xb812ac: ldur            x5, [x0, #7]
    // 0xb812b0: r0 = LoadInt32Instr(r1)
    //     0xb812b0: sbfx            x0, x1, #1, #0x1f
    // 0xb812b4: mov             x1, x5
    // 0xb812b8: cmp             x1, x0
    // 0xb812bc: b.hs            #0xb81758
    // 0xb812c0: LoadField: r0 = r2->field_f
    //     0xb812c0: ldur            w0, [x2, #0xf]
    // 0xb812c4: DecompressPointer r0
    //     0xb812c4: add             x0, x0, HEAP, lsl #32
    // 0xb812c8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb812c8: add             x16, x0, x5, lsl #2
    //     0xb812cc: ldur            w1, [x16, #0xf]
    // 0xb812d0: DecompressPointer r1
    //     0xb812d0: add             x1, x1, HEAP, lsl #32
    // 0xb812d4: LoadField: r0 = r1->field_f
    //     0xb812d4: ldur            w0, [x1, #0xf]
    // 0xb812d8: DecompressPointer r0
    //     0xb812d8: add             x0, x0, HEAP, lsl #32
    // 0xb812dc: cmp             w0, NULL
    // 0xb812e0: b.eq            #0xb8175c
    // 0xb812e4: LoadField: r2 = r0->field_7
    //     0xb812e4: ldur            w2, [x0, #7]
    // 0xb812e8: cbz             w2, #0xb81554
    // 0xb812ec: ldur            x5, [fp, #-8]
    // 0xb812f0: LoadField: r6 = r1->field_f
    //     0xb812f0: ldur            w6, [x1, #0xf]
    // 0xb812f4: DecompressPointer r6
    //     0xb812f4: add             x6, x6, HEAP, lsl #32
    // 0xb812f8: mov             x0, x6
    // 0xb812fc: stur            x6, [fp, #-0x10]
    // 0xb81300: r2 = Null
    //     0xb81300: mov             x2, NULL
    // 0xb81304: r1 = Null
    //     0xb81304: mov             x1, NULL
    // 0xb81308: r4 = LoadClassIdInstr(r0)
    //     0xb81308: ldur            x4, [x0, #-1]
    //     0xb8130c: ubfx            x4, x4, #0xc, #0x14
    // 0xb81310: sub             x4, x4, #0x5e
    // 0xb81314: cmp             x4, #1
    // 0xb81318: b.ls            #0xb8132c
    // 0xb8131c: r8 = String
    //     0xb8131c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb81320: r3 = Null
    //     0xb81320: add             x3, PP, #0x55, lsl #12  ; [pp+0x55808] Null
    //     0xb81324: ldr             x3, [x3, #0x808]
    // 0xb81328: r0 = String()
    //     0xb81328: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb8132c: ldur            x0, [fp, #-8]
    // 0xb81330: LoadField: r1 = r0->field_f
    //     0xb81330: ldur            w1, [x0, #0xf]
    // 0xb81334: DecompressPointer r1
    //     0xb81334: add             x1, x1, HEAP, lsl #32
    // 0xb81338: cmp             w1, NULL
    // 0xb8133c: b.eq            #0xb81760
    // 0xb81340: r0 = of()
    //     0xb81340: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb81344: LoadField: r1 = r0->field_87
    //     0xb81344: ldur            w1, [x0, #0x87]
    // 0xb81348: DecompressPointer r1
    //     0xb81348: add             x1, x1, HEAP, lsl #32
    // 0xb8134c: LoadField: r0 = r1->field_7
    //     0xb8134c: ldur            w0, [x1, #7]
    // 0xb81350: DecompressPointer r0
    //     0xb81350: add             x0, x0, HEAP, lsl #32
    // 0xb81354: stur            x0, [fp, #-8]
    // 0xb81358: r1 = Instance_Color
    //     0xb81358: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb8135c: d0 = 0.700000
    //     0xb8135c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb81360: ldr             d0, [x17, #0xf48]
    // 0xb81364: r0 = withOpacity()
    //     0xb81364: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb81368: r16 = 14.000000
    //     0xb81368: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb8136c: ldr             x16, [x16, #0x1d8]
    // 0xb81370: stp             x16, x0, [SP, #8]
    // 0xb81374: r16 = Instance_TextDecoration
    //     0xb81374: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb81378: ldr             x16, [x16, #0x10]
    // 0xb8137c: str             x16, [SP]
    // 0xb81380: ldur            x1, [fp, #-8]
    // 0xb81384: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xb81384: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xb81388: ldr             x4, [x4, #0x7c8]
    // 0xb8138c: r0 = copyWith()
    //     0xb8138c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb81390: stur            x0, [fp, #-8]
    // 0xb81394: r0 = Text()
    //     0xb81394: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb81398: mov             x1, x0
    // 0xb8139c: ldur            x0, [fp, #-0x10]
    // 0xb813a0: stur            x1, [fp, #-0x38]
    // 0xb813a4: StoreField: r1->field_b = r0
    //     0xb813a4: stur            w0, [x1, #0xb]
    // 0xb813a8: ldur            x0, [fp, #-8]
    // 0xb813ac: StoreField: r1->field_13 = r0
    //     0xb813ac: stur            w0, [x1, #0x13]
    // 0xb813b0: r0 = InkWell()
    //     0xb813b0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb813b4: mov             x3, x0
    // 0xb813b8: ldur            x0, [fp, #-0x38]
    // 0xb813bc: stur            x3, [fp, #-8]
    // 0xb813c0: StoreField: r3->field_b = r0
    //     0xb813c0: stur            w0, [x3, #0xb]
    // 0xb813c4: ldur            x2, [fp, #-0x20]
    // 0xb813c8: r1 = Function '<anonymous closure>':.
    //     0xb813c8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55818] AnonymousClosure: (0xa7bc98), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::lineThemeSlider (0xa7bec0)
    //     0xb813cc: ldr             x1, [x1, #0x818]
    // 0xb813d0: r0 = AllocateClosure()
    //     0xb813d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb813d4: mov             x1, x0
    // 0xb813d8: ldur            x0, [fp, #-8]
    // 0xb813dc: StoreField: r0->field_f = r1
    //     0xb813dc: stur            w1, [x0, #0xf]
    // 0xb813e0: r1 = true
    //     0xb813e0: add             x1, NULL, #0x20  ; true
    // 0xb813e4: StoreField: r0->field_43 = r1
    //     0xb813e4: stur            w1, [x0, #0x43]
    // 0xb813e8: r2 = Instance_BoxShape
    //     0xb813e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb813ec: ldr             x2, [x2, #0x80]
    // 0xb813f0: StoreField: r0->field_47 = r2
    //     0xb813f0: stur            w2, [x0, #0x47]
    // 0xb813f4: StoreField: r0->field_6f = r1
    //     0xb813f4: stur            w1, [x0, #0x6f]
    // 0xb813f8: r2 = false
    //     0xb813f8: add             x2, NULL, #0x30  ; false
    // 0xb813fc: StoreField: r0->field_73 = r2
    //     0xb813fc: stur            w2, [x0, #0x73]
    // 0xb81400: StoreField: r0->field_83 = r1
    //     0xb81400: stur            w1, [x0, #0x83]
    // 0xb81404: StoreField: r0->field_7b = r2
    //     0xb81404: stur            w2, [x0, #0x7b]
    // 0xb81408: r1 = Null
    //     0xb81408: mov             x1, NULL
    // 0xb8140c: r2 = 6
    //     0xb8140c: movz            x2, #0x6
    // 0xb81410: r0 = AllocateArray()
    //     0xb81410: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb81414: mov             x2, x0
    // 0xb81418: ldur            x0, [fp, #-8]
    // 0xb8141c: stur            x2, [fp, #-0x10]
    // 0xb81420: StoreField: r2->field_f = r0
    //     0xb81420: stur            w0, [x2, #0xf]
    // 0xb81424: r16 = Instance_SizedBox
    //     0xb81424: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb81428: ldr             x16, [x16, #0xb20]
    // 0xb8142c: StoreField: r2->field_13 = r16
    //     0xb8142c: stur            w16, [x2, #0x13]
    // 0xb81430: r16 = Instance_Icon
    //     0xb81430: add             x16, PP, #0x55, lsl #12  ; [pp+0x55820] Obj!Icon@d66731
    //     0xb81434: ldr             x16, [x16, #0x820]
    // 0xb81438: ArrayStore: r2[0] = r16  ; List_4
    //     0xb81438: stur            w16, [x2, #0x17]
    // 0xb8143c: r1 = <Widget>
    //     0xb8143c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb81440: r0 = AllocateGrowableArray()
    //     0xb81440: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb81444: mov             x1, x0
    // 0xb81448: ldur            x0, [fp, #-0x10]
    // 0xb8144c: stur            x1, [fp, #-8]
    // 0xb81450: StoreField: r1->field_f = r0
    //     0xb81450: stur            w0, [x1, #0xf]
    // 0xb81454: r0 = 6
    //     0xb81454: movz            x0, #0x6
    // 0xb81458: StoreField: r1->field_b = r0
    //     0xb81458: stur            w0, [x1, #0xb]
    // 0xb8145c: r0 = Row()
    //     0xb8145c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb81460: mov             x1, x0
    // 0xb81464: r0 = Instance_Axis
    //     0xb81464: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb81468: stur            x1, [fp, #-0x10]
    // 0xb8146c: StoreField: r1->field_f = r0
    //     0xb8146c: stur            w0, [x1, #0xf]
    // 0xb81470: r2 = Instance_MainAxisAlignment
    //     0xb81470: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb81474: ldr             x2, [x2, #0xa08]
    // 0xb81478: StoreField: r1->field_13 = r2
    //     0xb81478: stur            w2, [x1, #0x13]
    // 0xb8147c: r3 = Instance_MainAxisSize
    //     0xb8147c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb81480: ldr             x3, [x3, #0xa10]
    // 0xb81484: ArrayStore: r1[0] = r3  ; List_4
    //     0xb81484: stur            w3, [x1, #0x17]
    // 0xb81488: r4 = Instance_CrossAxisAlignment
    //     0xb81488: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb8148c: ldr             x4, [x4, #0xa18]
    // 0xb81490: StoreField: r1->field_1b = r4
    //     0xb81490: stur            w4, [x1, #0x1b]
    // 0xb81494: r5 = Instance_VerticalDirection
    //     0xb81494: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb81498: ldr             x5, [x5, #0xa20]
    // 0xb8149c: StoreField: r1->field_23 = r5
    //     0xb8149c: stur            w5, [x1, #0x23]
    // 0xb814a0: r6 = Instance_Clip
    //     0xb814a0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb814a4: ldr             x6, [x6, #0x38]
    // 0xb814a8: StoreField: r1->field_2b = r6
    //     0xb814a8: stur            w6, [x1, #0x2b]
    // 0xb814ac: StoreField: r1->field_2f = rZR
    //     0xb814ac: stur            xzr, [x1, #0x2f]
    // 0xb814b0: ldur            x7, [fp, #-8]
    // 0xb814b4: StoreField: r1->field_b = r7
    //     0xb814b4: stur            w7, [x1, #0xb]
    // 0xb814b8: r0 = Padding()
    //     0xb814b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb814bc: mov             x2, x0
    // 0xb814c0: r0 = Instance_EdgeInsets
    //     0xb814c0: add             x0, PP, #0x55, lsl #12  ; [pp+0x55828] Obj!EdgeInsets@d59301
    //     0xb814c4: ldr             x0, [x0, #0x828]
    // 0xb814c8: stur            x2, [fp, #-8]
    // 0xb814cc: StoreField: r2->field_f = r0
    //     0xb814cc: stur            w0, [x2, #0xf]
    // 0xb814d0: ldur            x0, [fp, #-0x10]
    // 0xb814d4: StoreField: r2->field_b = r0
    //     0xb814d4: stur            w0, [x2, #0xb]
    // 0xb814d8: ldur            x0, [fp, #-0x30]
    // 0xb814dc: LoadField: r1 = r0->field_b
    //     0xb814dc: ldur            w1, [x0, #0xb]
    // 0xb814e0: LoadField: r3 = r0->field_f
    //     0xb814e0: ldur            w3, [x0, #0xf]
    // 0xb814e4: DecompressPointer r3
    //     0xb814e4: add             x3, x3, HEAP, lsl #32
    // 0xb814e8: LoadField: r4 = r3->field_b
    //     0xb814e8: ldur            w4, [x3, #0xb]
    // 0xb814ec: r3 = LoadInt32Instr(r1)
    //     0xb814ec: sbfx            x3, x1, #1, #0x1f
    // 0xb814f0: stur            x3, [fp, #-0x18]
    // 0xb814f4: r1 = LoadInt32Instr(r4)
    //     0xb814f4: sbfx            x1, x4, #1, #0x1f
    // 0xb814f8: cmp             x3, x1
    // 0xb814fc: b.ne            #0xb81508
    // 0xb81500: mov             x1, x0
    // 0xb81504: r0 = _growToNextCapacity()
    //     0xb81504: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb81508: ldur            x2, [fp, #-0x30]
    // 0xb8150c: ldur            x3, [fp, #-0x18]
    // 0xb81510: add             x0, x3, #1
    // 0xb81514: lsl             x1, x0, #1
    // 0xb81518: StoreField: r2->field_b = r1
    //     0xb81518: stur            w1, [x2, #0xb]
    // 0xb8151c: LoadField: r1 = r2->field_f
    //     0xb8151c: ldur            w1, [x2, #0xf]
    // 0xb81520: DecompressPointer r1
    //     0xb81520: add             x1, x1, HEAP, lsl #32
    // 0xb81524: ldur            x0, [fp, #-8]
    // 0xb81528: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb81528: add             x25, x1, x3, lsl #2
    //     0xb8152c: add             x25, x25, #0xf
    //     0xb81530: str             w0, [x25]
    //     0xb81534: tbz             w0, #0, #0xb81550
    //     0xb81538: ldurb           w16, [x1, #-1]
    //     0xb8153c: ldurb           w17, [x0, #-1]
    //     0xb81540: and             x16, x17, x16, lsr #2
    //     0xb81544: tst             x16, HEAP, lsr #32
    //     0xb81548: b.eq            #0xb81550
    //     0xb8154c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb81550: b               #0xb815e4
    // 0xb81554: mov             x2, x3
    // 0xb81558: r0 = Container()
    //     0xb81558: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb8155c: mov             x1, x0
    // 0xb81560: stur            x0, [fp, #-8]
    // 0xb81564: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb81564: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb81568: r0 = Container()
    //     0xb81568: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb8156c: ldur            x0, [fp, #-0x30]
    // 0xb81570: LoadField: r1 = r0->field_b
    //     0xb81570: ldur            w1, [x0, #0xb]
    // 0xb81574: LoadField: r2 = r0->field_f
    //     0xb81574: ldur            w2, [x0, #0xf]
    // 0xb81578: DecompressPointer r2
    //     0xb81578: add             x2, x2, HEAP, lsl #32
    // 0xb8157c: LoadField: r3 = r2->field_b
    //     0xb8157c: ldur            w3, [x2, #0xb]
    // 0xb81580: r2 = LoadInt32Instr(r1)
    //     0xb81580: sbfx            x2, x1, #1, #0x1f
    // 0xb81584: stur            x2, [fp, #-0x18]
    // 0xb81588: r1 = LoadInt32Instr(r3)
    //     0xb81588: sbfx            x1, x3, #1, #0x1f
    // 0xb8158c: cmp             x2, x1
    // 0xb81590: b.ne            #0xb8159c
    // 0xb81594: mov             x1, x0
    // 0xb81598: r0 = _growToNextCapacity()
    //     0xb81598: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb8159c: ldur            x2, [fp, #-0x30]
    // 0xb815a0: ldur            x3, [fp, #-0x18]
    // 0xb815a4: add             x0, x3, #1
    // 0xb815a8: lsl             x1, x0, #1
    // 0xb815ac: StoreField: r2->field_b = r1
    //     0xb815ac: stur            w1, [x2, #0xb]
    // 0xb815b0: LoadField: r1 = r2->field_f
    //     0xb815b0: ldur            w1, [x2, #0xf]
    // 0xb815b4: DecompressPointer r1
    //     0xb815b4: add             x1, x1, HEAP, lsl #32
    // 0xb815b8: ldur            x0, [fp, #-8]
    // 0xb815bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb815bc: add             x25, x1, x3, lsl #2
    //     0xb815c0: add             x25, x25, #0xf
    //     0xb815c4: str             w0, [x25]
    //     0xb815c8: tbz             w0, #0, #0xb815e4
    //     0xb815cc: ldurb           w16, [x1, #-1]
    //     0xb815d0: ldurb           w17, [x0, #-1]
    //     0xb815d4: and             x16, x17, x16, lsr #2
    //     0xb815d8: tst             x16, HEAP, lsr #32
    //     0xb815dc: b.eq            #0xb815e4
    //     0xb815e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb815e4: r0 = Column()
    //     0xb815e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb815e8: mov             x1, x0
    // 0xb815ec: r0 = Instance_Axis
    //     0xb815ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb815f0: stur            x1, [fp, #-8]
    // 0xb815f4: StoreField: r1->field_f = r0
    //     0xb815f4: stur            w0, [x1, #0xf]
    // 0xb815f8: r0 = Instance_MainAxisAlignment
    //     0xb815f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb815fc: ldr             x0, [x0, #0xa08]
    // 0xb81600: StoreField: r1->field_13 = r0
    //     0xb81600: stur            w0, [x1, #0x13]
    // 0xb81604: r2 = Instance_MainAxisSize
    //     0xb81604: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb81608: ldr             x2, [x2, #0xa10]
    // 0xb8160c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb8160c: stur            w2, [x1, #0x17]
    // 0xb81610: r3 = Instance_CrossAxisAlignment
    //     0xb81610: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb81614: ldr             x3, [x3, #0x890]
    // 0xb81618: StoreField: r1->field_1b = r3
    //     0xb81618: stur            w3, [x1, #0x1b]
    // 0xb8161c: r3 = Instance_VerticalDirection
    //     0xb8161c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb81620: ldr             x3, [x3, #0xa20]
    // 0xb81624: StoreField: r1->field_23 = r3
    //     0xb81624: stur            w3, [x1, #0x23]
    // 0xb81628: r4 = Instance_Clip
    //     0xb81628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8162c: ldr             x4, [x4, #0x38]
    // 0xb81630: StoreField: r1->field_2b = r4
    //     0xb81630: stur            w4, [x1, #0x2b]
    // 0xb81634: StoreField: r1->field_2f = rZR
    //     0xb81634: stur            xzr, [x1, #0x2f]
    // 0xb81638: ldur            x5, [fp, #-0x30]
    // 0xb8163c: StoreField: r1->field_b = r5
    //     0xb8163c: stur            w5, [x1, #0xb]
    // 0xb81640: r0 = AnimatedContainer()
    //     0xb81640: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb81644: stur            x0, [fp, #-0x10]
    // 0xb81648: r16 = Instance_Cubic
    //     0xb81648: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb8164c: ldr             x16, [x16, #0xaf8]
    // 0xb81650: ldur            lr, [fp, #-0x28]
    // 0xb81654: stp             lr, x16, [SP]
    // 0xb81658: mov             x1, x0
    // 0xb8165c: ldur            x2, [fp, #-8]
    // 0xb81660: r3 = Instance_Duration
    //     0xb81660: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb81664: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x3, decoration, 0x4, null]
    //     0xb81664: add             x4, PP, #0x55, lsl #12  ; [pp+0x55830] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x3, "decoration", 0x4, Null]
    //     0xb81668: ldr             x4, [x4, #0x830]
    // 0xb8166c: r0 = AnimatedContainer()
    //     0xb8166c: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb81670: r1 = <FlexParentData>
    //     0xb81670: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb81674: ldr             x1, [x1, #0xe00]
    // 0xb81678: r0 = Expanded()
    //     0xb81678: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb8167c: mov             x3, x0
    // 0xb81680: r0 = 1
    //     0xb81680: movz            x0, #0x1
    // 0xb81684: stur            x3, [fp, #-8]
    // 0xb81688: StoreField: r3->field_13 = r0
    //     0xb81688: stur            x0, [x3, #0x13]
    // 0xb8168c: r0 = Instance_FlexFit
    //     0xb8168c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb81690: ldr             x0, [x0, #0xe08]
    // 0xb81694: StoreField: r3->field_1b = r0
    //     0xb81694: stur            w0, [x3, #0x1b]
    // 0xb81698: ldur            x0, [fp, #-0x10]
    // 0xb8169c: StoreField: r3->field_b = r0
    //     0xb8169c: stur            w0, [x3, #0xb]
    // 0xb816a0: r1 = Null
    //     0xb816a0: mov             x1, NULL
    // 0xb816a4: r2 = 2
    //     0xb816a4: movz            x2, #0x2
    // 0xb816a8: r0 = AllocateArray()
    //     0xb816a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb816ac: mov             x2, x0
    // 0xb816b0: ldur            x0, [fp, #-8]
    // 0xb816b4: stur            x2, [fp, #-0x10]
    // 0xb816b8: StoreField: r2->field_f = r0
    //     0xb816b8: stur            w0, [x2, #0xf]
    // 0xb816bc: r1 = <Widget>
    //     0xb816bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb816c0: r0 = AllocateGrowableArray()
    //     0xb816c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb816c4: mov             x1, x0
    // 0xb816c8: ldur            x0, [fp, #-0x10]
    // 0xb816cc: stur            x1, [fp, #-8]
    // 0xb816d0: StoreField: r1->field_f = r0
    //     0xb816d0: stur            w0, [x1, #0xf]
    // 0xb816d4: r0 = 2
    //     0xb816d4: movz            x0, #0x2
    // 0xb816d8: StoreField: r1->field_b = r0
    //     0xb816d8: stur            w0, [x1, #0xb]
    // 0xb816dc: r0 = Row()
    //     0xb816dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb816e0: r1 = Instance_Axis
    //     0xb816e0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb816e4: StoreField: r0->field_f = r1
    //     0xb816e4: stur            w1, [x0, #0xf]
    // 0xb816e8: r1 = Instance_MainAxisAlignment
    //     0xb816e8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb816ec: ldr             x1, [x1, #0xa08]
    // 0xb816f0: StoreField: r0->field_13 = r1
    //     0xb816f0: stur            w1, [x0, #0x13]
    // 0xb816f4: r1 = Instance_MainAxisSize
    //     0xb816f4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb816f8: ldr             x1, [x1, #0xa10]
    // 0xb816fc: ArrayStore: r0[0] = r1  ; List_4
    //     0xb816fc: stur            w1, [x0, #0x17]
    // 0xb81700: r1 = Instance_CrossAxisAlignment
    //     0xb81700: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb81704: ldr             x1, [x1, #0xa18]
    // 0xb81708: StoreField: r0->field_1b = r1
    //     0xb81708: stur            w1, [x0, #0x1b]
    // 0xb8170c: r1 = Instance_VerticalDirection
    //     0xb8170c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb81710: ldr             x1, [x1, #0xa20]
    // 0xb81714: StoreField: r0->field_23 = r1
    //     0xb81714: stur            w1, [x0, #0x23]
    // 0xb81718: r1 = Instance_Clip
    //     0xb81718: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb8171c: ldr             x1, [x1, #0x38]
    // 0xb81720: StoreField: r0->field_2b = r1
    //     0xb81720: stur            w1, [x0, #0x2b]
    // 0xb81724: StoreField: r0->field_2f = rZR
    //     0xb81724: stur            xzr, [x0, #0x2f]
    // 0xb81728: ldur            x1, [fp, #-8]
    // 0xb8172c: StoreField: r0->field_b = r1
    //     0xb8172c: stur            w1, [x0, #0xb]
    // 0xb81730: LeaveFrame
    //     0xb81730: mov             SP, fp
    //     0xb81734: ldp             fp, lr, [SP], #0x10
    // 0xb81738: ret
    //     0xb81738: ret             
    // 0xb8173c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb8173c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb81740: b               #0xb80f68
    // 0xb81744: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb81744: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb81748: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb81748: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8174c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb8174c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb81750: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb81750: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb81754: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb81754: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb81758: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb81758: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb8175c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb8175c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb81760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb81760: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb81764, size: 0x84
    // 0xb81764: EnterFrame
    //     0xb81764: stp             fp, lr, [SP, #-0x10]!
    //     0xb81768: mov             fp, SP
    // 0xb8176c: AllocStack(0x10)
    //     0xb8176c: sub             SP, SP, #0x10
    // 0xb81770: SetupParameters()
    //     0xb81770: ldr             x0, [fp, #0x18]
    //     0xb81774: ldur            w1, [x0, #0x17]
    //     0xb81778: add             x1, x1, HEAP, lsl #32
    //     0xb8177c: stur            x1, [fp, #-8]
    // 0xb81780: CheckStackOverflow
    //     0xb81780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb81784: cmp             SP, x16
    //     0xb81788: b.ls            #0xb817e0
    // 0xb8178c: r1 = 1
    //     0xb8178c: movz            x1, #0x1
    // 0xb81790: r0 = AllocateContext()
    //     0xb81790: bl              #0x16f6108  ; AllocateContextStub
    // 0xb81794: mov             x1, x0
    // 0xb81798: ldur            x0, [fp, #-8]
    // 0xb8179c: StoreField: r1->field_b = r0
    //     0xb8179c: stur            w0, [x1, #0xb]
    // 0xb817a0: ldr             x2, [fp, #0x10]
    // 0xb817a4: StoreField: r1->field_f = r2
    //     0xb817a4: stur            w2, [x1, #0xf]
    // 0xb817a8: LoadField: r3 = r0->field_f
    //     0xb817a8: ldur            w3, [x0, #0xf]
    // 0xb817ac: DecompressPointer r3
    //     0xb817ac: add             x3, x3, HEAP, lsl #32
    // 0xb817b0: mov             x2, x1
    // 0xb817b4: stur            x3, [fp, #-0x10]
    // 0xb817b8: r1 = Function '<anonymous closure>':.
    //     0xb817b8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55838] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xb817bc: ldr             x1, [x1, #0x838]
    // 0xb817c0: r0 = AllocateClosure()
    //     0xb817c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb817c4: ldur            x1, [fp, #-0x10]
    // 0xb817c8: mov             x2, x0
    // 0xb817cc: r0 = setState()
    //     0xb817cc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb817d0: r0 = Null
    //     0xb817d0: mov             x0, NULL
    // 0xb817d4: LeaveFrame
    //     0xb817d4: mov             SP, fp
    //     0xb817d8: ldp             fp, lr, [SP], #0x10
    // 0xb817dc: ret
    //     0xb817dc: ret             
    // 0xb817e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb817e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb817e4: b               #0xb8178c
  }
}

// class id: 4059, size: 0x20, field offset: 0xc
//   const constructor, 
class ProductCollectionPosterCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f7b4, size: 0x30
    // 0xc7f7b4: EnterFrame
    //     0xc7f7b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f7b8: mov             fp, SP
    // 0xc7f7bc: mov             x0, x1
    // 0xc7f7c0: r1 = <ProductCollectionPosterCarousel>
    //     0xc7f7c0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48798] TypeArguments: <ProductCollectionPosterCarousel>
    //     0xc7f7c4: ldr             x1, [x1, #0x798]
    // 0xc7f7c8: r0 = _ProductCollectionPosterCarouselState()
    //     0xc7f7c8: bl              #0xc7f7e4  ; Allocate_ProductCollectionPosterCarouselStateStub -> _ProductCollectionPosterCarouselState (size=0x20)
    // 0xc7f7cc: r1 = Sentinel
    //     0xc7f7cc: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f7d0: StoreField: r0->field_13 = r1
    //     0xc7f7d0: stur            w1, [x0, #0x13]
    // 0xc7f7d4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7f7d4: stur            xzr, [x0, #0x17]
    // 0xc7f7d8: LeaveFrame
    //     0xc7f7d8: mov             SP, fp
    //     0xc7f7dc: ldp             fp, lr, [SP], #0x10
    // 0xc7f7e0: ret
    //     0xc7f7e0: ret             
  }
}
