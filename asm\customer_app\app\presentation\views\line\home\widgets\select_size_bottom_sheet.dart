// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart

// class id: 1049528, size: 0x8
class :: {
}

// class id: 3243, size: 0x30, field offset: 0x14
class _SelectSizeBottomSheetState extends State<dynamic> {

  late AllSkuDatum dropDownValue; // offset: 0x14

  [closure] bool <anonymous closure>(dynamic, AllSkuDatum?) {
    // ** addr: 0x9083cc, size: 0xbc
    // 0x9083cc: EnterFrame
    //     0x9083cc: stp             fp, lr, [SP, #-0x10]!
    //     0x9083d0: mov             fp, SP
    // 0x9083d4: AllocStack(0x10)
    //     0x9083d4: sub             SP, SP, #0x10
    // 0x9083d8: SetupParameters()
    //     0x9083d8: ldr             x0, [fp, #0x18]
    //     0x9083dc: ldur            w1, [x0, #0x17]
    //     0x9083e0: add             x1, x1, HEAP, lsl #32
    // 0x9083e4: CheckStackOverflow
    //     0x9083e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9083e8: cmp             SP, x16
    //     0x9083ec: b.ls            #0x90847c
    // 0x9083f0: ldr             x0, [fp, #0x10]
    // 0x9083f4: cmp             w0, NULL
    // 0x9083f8: b.ne            #0x908404
    // 0x9083fc: r0 = Null
    //     0x9083fc: mov             x0, NULL
    // 0x908400: b               #0x908410
    // 0x908404: LoadField: r2 = r0->field_b
    //     0x908404: ldur            w2, [x0, #0xb]
    // 0x908408: DecompressPointer r2
    //     0x908408: add             x2, x2, HEAP, lsl #32
    // 0x90840c: mov             x0, x2
    // 0x908410: LoadField: r2 = r1->field_f
    //     0x908410: ldur            w2, [x1, #0xf]
    // 0x908414: DecompressPointer r2
    //     0x908414: add             x2, x2, HEAP, lsl #32
    // 0x908418: LoadField: r1 = r2->field_b
    //     0x908418: ldur            w1, [x2, #0xb]
    // 0x90841c: DecompressPointer r1
    //     0x90841c: add             x1, x1, HEAP, lsl #32
    // 0x908420: cmp             w1, NULL
    // 0x908424: b.eq            #0x908484
    // 0x908428: LoadField: r2 = r1->field_b
    //     0x908428: ldur            w2, [x1, #0xb]
    // 0x90842c: DecompressPointer r2
    //     0x90842c: add             x2, x2, HEAP, lsl #32
    // 0x908430: LoadField: r1 = r2->field_3b
    //     0x908430: ldur            w1, [x2, #0x3b]
    // 0x908434: DecompressPointer r1
    //     0x908434: add             x1, x1, HEAP, lsl #32
    // 0x908438: cmp             w1, NULL
    // 0x90843c: b.ne            #0x908448
    // 0x908440: r1 = Null
    //     0x908440: mov             x1, NULL
    // 0x908444: b               #0x908454
    // 0x908448: LoadField: r2 = r1->field_b
    //     0x908448: ldur            w2, [x1, #0xb]
    // 0x90844c: DecompressPointer r2
    //     0x90844c: add             x2, x2, HEAP, lsl #32
    // 0x908450: mov             x1, x2
    // 0x908454: r2 = LoadClassIdInstr(r0)
    //     0x908454: ldur            x2, [x0, #-1]
    //     0x908458: ubfx            x2, x2, #0xc, #0x14
    // 0x90845c: stp             x1, x0, [SP]
    // 0x908460: mov             x0, x2
    // 0x908464: mov             lr, x0
    // 0x908468: ldr             lr, [x21, lr, lsl #3]
    // 0x90846c: blr             lr
    // 0x908470: LeaveFrame
    //     0x908470: mov             SP, fp
    //     0x908474: ldp             fp, lr, [SP], #0x10
    // 0x908478: ret
    //     0x908478: ret             
    // 0x90847c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90847c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x908480: b               #0x9083f0
    // 0x908484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x908484: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, AllGroupedSkusDatum?) {
    // ** addr: 0x9084a8, size: 0xbc
    // 0x9084a8: EnterFrame
    //     0x9084a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9084ac: mov             fp, SP
    // 0x9084b0: AllocStack(0x10)
    //     0x9084b0: sub             SP, SP, #0x10
    // 0x9084b4: SetupParameters()
    //     0x9084b4: ldr             x0, [fp, #0x18]
    //     0x9084b8: ldur            w1, [x0, #0x17]
    //     0x9084bc: add             x1, x1, HEAP, lsl #32
    // 0x9084c0: CheckStackOverflow
    //     0x9084c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9084c4: cmp             SP, x16
    //     0x9084c8: b.ls            #0x908558
    // 0x9084cc: ldr             x0, [fp, #0x10]
    // 0x9084d0: cmp             w0, NULL
    // 0x9084d4: b.ne            #0x9084e0
    // 0x9084d8: r0 = Null
    //     0x9084d8: mov             x0, NULL
    // 0x9084dc: b               #0x9084ec
    // 0x9084e0: LoadField: r2 = r0->field_7
    //     0x9084e0: ldur            w2, [x0, #7]
    // 0x9084e4: DecompressPointer r2
    //     0x9084e4: add             x2, x2, HEAP, lsl #32
    // 0x9084e8: mov             x0, x2
    // 0x9084ec: LoadField: r2 = r1->field_f
    //     0x9084ec: ldur            w2, [x1, #0xf]
    // 0x9084f0: DecompressPointer r2
    //     0x9084f0: add             x2, x2, HEAP, lsl #32
    // 0x9084f4: LoadField: r1 = r2->field_b
    //     0x9084f4: ldur            w1, [x2, #0xb]
    // 0x9084f8: DecompressPointer r1
    //     0x9084f8: add             x1, x1, HEAP, lsl #32
    // 0x9084fc: cmp             w1, NULL
    // 0x908500: b.eq            #0x908560
    // 0x908504: LoadField: r2 = r1->field_b
    //     0x908504: ldur            w2, [x1, #0xb]
    // 0x908508: DecompressPointer r2
    //     0x908508: add             x2, x2, HEAP, lsl #32
    // 0x90850c: LoadField: r1 = r2->field_3b
    //     0x90850c: ldur            w1, [x2, #0x3b]
    // 0x908510: DecompressPointer r1
    //     0x908510: add             x1, x1, HEAP, lsl #32
    // 0x908514: cmp             w1, NULL
    // 0x908518: b.ne            #0x908524
    // 0x90851c: r1 = Null
    //     0x90851c: mov             x1, NULL
    // 0x908520: b               #0x908530
    // 0x908524: LoadField: r2 = r1->field_b
    //     0x908524: ldur            w2, [x1, #0xb]
    // 0x908528: DecompressPointer r2
    //     0x908528: add             x2, x2, HEAP, lsl #32
    // 0x90852c: mov             x1, x2
    // 0x908530: r2 = LoadClassIdInstr(r0)
    //     0x908530: ldur            x2, [x0, #-1]
    //     0x908534: ubfx            x2, x2, #0xc, #0x14
    // 0x908538: stp             x1, x0, [SP]
    // 0x90853c: mov             x0, x2
    // 0x908540: mov             lr, x0
    // 0x908544: ldr             lr, [x21, lr, lsl #3]
    // 0x908548: blr             lr
    // 0x90854c: LeaveFrame
    //     0x90854c: mov             SP, fp
    //     0x908550: ldp             fp, lr, [SP], #0x10
    // 0x908554: ret
    //     0x908554: ret             
    // 0x908558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x908558: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90855c: b               #0x9084cc
    // 0x908560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x908560: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x949324, size: 0x574
    // 0x949324: EnterFrame
    //     0x949324: stp             fp, lr, [SP, #-0x10]!
    //     0x949328: mov             fp, SP
    // 0x94932c: AllocStack(0x38)
    //     0x94932c: sub             SP, SP, #0x38
    // 0x949330: SetupParameters(_SelectSizeBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x949330: stur            x1, [fp, #-8]
    // 0x949334: CheckStackOverflow
    //     0x949334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949338: cmp             SP, x16
    //     0x94933c: b.ls            #0x949868
    // 0x949340: r1 = 1
    //     0x949340: movz            x1, #0x1
    // 0x949344: r0 = AllocateContext()
    //     0x949344: bl              #0x16f6108  ; AllocateContextStub
    // 0x949348: mov             x2, x0
    // 0x94934c: ldur            x1, [fp, #-8]
    // 0x949350: stur            x2, [fp, #-0x10]
    // 0x949354: StoreField: r2->field_f = r1
    //     0x949354: stur            w1, [x2, #0xf]
    // 0x949358: LoadField: r0 = r1->field_b
    //     0x949358: ldur            w0, [x1, #0xb]
    // 0x94935c: DecompressPointer r0
    //     0x94935c: add             x0, x0, HEAP, lsl #32
    // 0x949360: cmp             w0, NULL
    // 0x949364: b.eq            #0x949870
    // 0x949368: LoadField: r3 = r0->field_f
    //     0x949368: ldur            w3, [x0, #0xf]
    // 0x94936c: DecompressPointer r3
    //     0x94936c: add             x3, x3, HEAP, lsl #32
    // 0x949370: r0 = LoadClassIdInstr(r3)
    //     0x949370: ldur            x0, [x3, #-1]
    //     0x949374: ubfx            x0, x0, #0xc, #0x14
    // 0x949378: r16 = "home_page"
    //     0x949378: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x94937c: ldr             x16, [x16, #0xe60]
    // 0x949380: stp             x16, x3, [SP]
    // 0x949384: mov             lr, x0
    // 0x949388: ldr             lr, [x21, lr, lsl #3]
    // 0x94938c: blr             lr
    // 0x949390: tbnz            w0, #4, #0x9493a8
    // 0x949394: ldur            x0, [fp, #-8]
    // 0x949398: r1 = "product_card"
    //     0x949398: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0x94939c: ldr             x1, [x1, #0xc28]
    // 0x9493a0: StoreField: r0->field_2b = r1
    //     0x9493a0: stur            w1, [x0, #0x2b]
    // 0x9493a4: b               #0x9493ac
    // 0x9493a8: ldur            x0, [fp, #-8]
    // 0x9493ac: LoadField: r1 = r0->field_b
    //     0x9493ac: ldur            w1, [x0, #0xb]
    // 0x9493b0: DecompressPointer r1
    //     0x9493b0: add             x1, x1, HEAP, lsl #32
    // 0x9493b4: cmp             w1, NULL
    // 0x9493b8: b.eq            #0x949874
    // 0x9493bc: LoadField: r2 = r1->field_b
    //     0x9493bc: ldur            w2, [x1, #0xb]
    // 0x9493c0: DecompressPointer r2
    //     0x9493c0: add             x2, x2, HEAP, lsl #32
    // 0x9493c4: LoadField: r1 = r2->field_73
    //     0x9493c4: ldur            w1, [x2, #0x73]
    // 0x9493c8: DecompressPointer r1
    //     0x9493c8: add             x1, x1, HEAP, lsl #32
    // 0x9493cc: cmp             w1, NULL
    // 0x9493d0: b.ne            #0x9493dc
    // 0x9493d4: r3 = Null
    //     0x9493d4: mov             x3, NULL
    // 0x9493d8: b               #0x9493f4
    // 0x9493dc: LoadField: r3 = r1->field_b
    //     0x9493dc: ldur            w3, [x1, #0xb]
    // 0x9493e0: cbnz            w3, #0x9493ec
    // 0x9493e4: r4 = false
    //     0x9493e4: add             x4, NULL, #0x30  ; false
    // 0x9493e8: b               #0x9493f0
    // 0x9493ec: r4 = true
    //     0x9493ec: add             x4, NULL, #0x20  ; true
    // 0x9493f0: mov             x3, x4
    // 0x9493f4: cmp             w3, NULL
    // 0x9493f8: b.ne            #0x949404
    // 0x9493fc: mov             x1, x0
    // 0x949400: b               #0x9496c4
    // 0x949404: tbnz            w3, #4, #0x9496c0
    // 0x949408: cmp             w1, NULL
    // 0x94940c: b.ne            #0x949418
    // 0x949410: r2 = Null
    //     0x949410: mov             x2, NULL
    // 0x949414: b               #0x949470
    // 0x949418: r16 = <AllGroupedSkusDatum?>
    //     0x949418: add             x16, PP, #0x53, lsl #12  ; [pp+0x53c98] TypeArguments: <AllGroupedSkusDatum?>
    //     0x94941c: ldr             x16, [x16, #0xc98]
    // 0x949420: stp             x1, x16, [SP]
    // 0x949424: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x949424: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x949428: r0 = cast()
    //     0x949428: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x94942c: ldur            x2, [fp, #-0x10]
    // 0x949430: r1 = Function '<anonymous closure>':.
    //     0x949430: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ca0] AnonymousClosure: (0x9084a8), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::initState (0x949324)
    //     0x949434: ldr             x1, [x1, #0xca0]
    // 0x949438: stur            x0, [fp, #-0x18]
    // 0x94943c: r0 = AllocateClosure()
    //     0x94943c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x949440: r1 = Function '<anonymous closure>':.
    //     0x949440: add             x1, PP, #0x53, lsl #12  ; [pp+0x53ca8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x949444: ldr             x1, [x1, #0xca8]
    // 0x949448: r2 = Null
    //     0x949448: mov             x2, NULL
    // 0x94944c: stur            x0, [fp, #-0x20]
    // 0x949450: r0 = AllocateClosure()
    //     0x949450: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x949454: str             x0, [SP]
    // 0x949458: ldur            x1, [fp, #-0x18]
    // 0x94945c: ldur            x2, [fp, #-0x20]
    // 0x949460: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x949460: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x949464: ldr             x4, [x4, #0xb48]
    // 0x949468: r0 = firstWhere()
    //     0x949468: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x94946c: mov             x2, x0
    // 0x949470: cmp             w2, NULL
    // 0x949474: b.eq            #0x9494fc
    // 0x949478: ldur            x0, [fp, #-8]
    // 0x94947c: LoadField: r1 = r0->field_b
    //     0x94947c: ldur            w1, [x0, #0xb]
    // 0x949480: DecompressPointer r1
    //     0x949480: add             x1, x1, HEAP, lsl #32
    // 0x949484: cmp             w1, NULL
    // 0x949488: b.eq            #0x949878
    // 0x94948c: LoadField: r3 = r1->field_b
    //     0x94948c: ldur            w3, [x1, #0xb]
    // 0x949490: DecompressPointer r3
    //     0x949490: add             x3, x3, HEAP, lsl #32
    // 0x949494: LoadField: r1 = r3->field_73
    //     0x949494: ldur            w1, [x3, #0x73]
    // 0x949498: DecompressPointer r1
    //     0x949498: add             x1, x1, HEAP, lsl #32
    // 0x94949c: cmp             w1, NULL
    // 0x9494a0: b.ne            #0x9494ac
    // 0x9494a4: r0 = Null
    //     0x9494a4: mov             x0, NULL
    // 0x9494a8: b               #0x9494cc
    // 0x9494ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9494ac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9494b0: r0 = indexOf()
    //     0x9494b0: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x9494b4: mov             x2, x0
    // 0x9494b8: r0 = BoxInt64Instr(r2)
    //     0x9494b8: sbfiz           x0, x2, #1, #0x1f
    //     0x9494bc: cmp             x2, x0, asr #1
    //     0x9494c0: b.eq            #0x9494cc
    //     0x9494c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9494c8: stur            x2, [x0, #7]
    // 0x9494cc: cmp             w0, NULL
    // 0x9494d0: b.ne            #0x9494dc
    // 0x9494d4: r0 = 0
    //     0x9494d4: movz            x0, #0
    // 0x9494d8: b               #0x9494ec
    // 0x9494dc: r1 = LoadInt32Instr(r0)
    //     0x9494dc: sbfx            x1, x0, #1, #0x1f
    //     0x9494e0: tbz             w0, #0, #0x9494e8
    //     0x9494e4: ldur            x1, [x0, #7]
    // 0x9494e8: mov             x0, x1
    // 0x9494ec: ldur            x2, [fp, #-8]
    // 0x9494f0: ArrayStore: r2[0] = r0  ; List_8
    //     0x9494f0: stur            x0, [x2, #0x17]
    // 0x9494f4: mov             x3, x0
    // 0x9494f8: b               #0x949508
    // 0x9494fc: ldur            x2, [fp, #-8]
    // 0x949500: ArrayStore: r2[0] = rZR  ; List_8
    //     0x949500: stur            xzr, [x2, #0x17]
    // 0x949504: r3 = 0
    //     0x949504: movz            x3, #0
    // 0x949508: stur            x3, [fp, #-0x28]
    // 0x94950c: LoadField: r0 = r2->field_b
    //     0x94950c: ldur            w0, [x2, #0xb]
    // 0x949510: DecompressPointer r0
    //     0x949510: add             x0, x0, HEAP, lsl #32
    // 0x949514: cmp             w0, NULL
    // 0x949518: b.eq            #0x94987c
    // 0x94951c: LoadField: r1 = r0->field_b
    //     0x94951c: ldur            w1, [x0, #0xb]
    // 0x949520: DecompressPointer r1
    //     0x949520: add             x1, x1, HEAP, lsl #32
    // 0x949524: LoadField: r4 = r1->field_73
    //     0x949524: ldur            w4, [x1, #0x73]
    // 0x949528: DecompressPointer r4
    //     0x949528: add             x4, x4, HEAP, lsl #32
    // 0x94952c: stur            x4, [fp, #-0x18]
    // 0x949530: cmp             w4, NULL
    // 0x949534: b.ne            #0x949540
    // 0x949538: r0 = Null
    //     0x949538: mov             x0, NULL
    // 0x94953c: b               #0x9495b4
    // 0x949540: LoadField: r0 = r4->field_b
    //     0x949540: ldur            w0, [x4, #0xb]
    // 0x949544: r1 = LoadInt32Instr(r0)
    //     0x949544: sbfx            x1, x0, #1, #0x1f
    // 0x949548: mov             x0, x1
    // 0x94954c: mov             x1, x3
    // 0x949550: cmp             x1, x0
    // 0x949554: b.hs            #0x949880
    // 0x949558: LoadField: r0 = r4->field_f
    //     0x949558: ldur            w0, [x4, #0xf]
    // 0x94955c: DecompressPointer r0
    //     0x94955c: add             x0, x0, HEAP, lsl #32
    // 0x949560: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x949560: add             x16, x0, x3, lsl #2
    //     0x949564: ldur            w1, [x16, #0xf]
    // 0x949568: DecompressPointer r1
    //     0x949568: add             x1, x1, HEAP, lsl #32
    // 0x94956c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x94956c: ldur            w5, [x1, #0x17]
    // 0x949570: DecompressPointer r5
    //     0x949570: add             x5, x5, HEAP, lsl #32
    // 0x949574: cmp             w5, NULL
    // 0x949578: b.ne            #0x949584
    // 0x94957c: r0 = Null
    //     0x94957c: mov             x0, NULL
    // 0x949580: b               #0x9495b4
    // 0x949584: LoadField: r0 = r5->field_b
    //     0x949584: ldur            w0, [x5, #0xb]
    // 0x949588: r1 = LoadInt32Instr(r0)
    //     0x949588: sbfx            x1, x0, #1, #0x1f
    // 0x94958c: mov             x0, x1
    // 0x949590: mov             x1, x3
    // 0x949594: cmp             x1, x0
    // 0x949598: b.hs            #0x949884
    // 0x94959c: LoadField: r0 = r5->field_f
    //     0x94959c: ldur            w0, [x5, #0xf]
    // 0x9495a0: DecompressPointer r0
    //     0x9495a0: add             x0, x0, HEAP, lsl #32
    // 0x9495a4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x9495a4: add             x16, x0, x3, lsl #2
    //     0x9495a8: ldur            w1, [x16, #0xf]
    // 0x9495ac: DecompressPointer r1
    //     0x9495ac: add             x1, x1, HEAP, lsl #32
    // 0x9495b0: mov             x0, x1
    // 0x9495b4: cmp             w0, NULL
    // 0x9495b8: b.ne            #0x9495c0
    // 0x9495bc: r0 = AllSkuDatum()
    //     0x9495bc: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x9495c0: ldur            x1, [fp, #-8]
    // 0x9495c4: ldur            x2, [fp, #-0x18]
    // 0x9495c8: StoreField: r1->field_13 = r0
    //     0x9495c8: stur            w0, [x1, #0x13]
    //     0x9495cc: ldurb           w16, [x1, #-1]
    //     0x9495d0: ldurb           w17, [x0, #-1]
    //     0x9495d4: and             x16, x17, x16, lsr #2
    //     0x9495d8: tst             x16, HEAP, lsr #32
    //     0x9495dc: b.eq            #0x9495e4
    //     0x9495e0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9495e4: LoadField: r3 = r1->field_27
    //     0x9495e4: ldur            w3, [x1, #0x27]
    // 0x9495e8: DecompressPointer r3
    //     0x9495e8: add             x3, x3, HEAP, lsl #32
    // 0x9495ec: stur            x3, [fp, #-0x20]
    // 0x9495f0: cmp             w2, NULL
    // 0x9495f4: b.ne            #0x949600
    // 0x9495f8: r0 = Null
    //     0x9495f8: mov             x0, NULL
    // 0x9495fc: b               #0x949638
    // 0x949600: ldur            x4, [fp, #-0x28]
    // 0x949604: LoadField: r0 = r2->field_b
    //     0x949604: ldur            w0, [x2, #0xb]
    // 0x949608: r1 = LoadInt32Instr(r0)
    //     0x949608: sbfx            x1, x0, #1, #0x1f
    // 0x94960c: mov             x0, x1
    // 0x949610: mov             x1, x4
    // 0x949614: cmp             x1, x0
    // 0x949618: b.hs            #0x949888
    // 0x94961c: LoadField: r0 = r2->field_f
    //     0x94961c: ldur            w0, [x2, #0xf]
    // 0x949620: DecompressPointer r0
    //     0x949620: add             x0, x0, HEAP, lsl #32
    // 0x949624: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x949624: add             x16, x0, x4, lsl #2
    //     0x949628: ldur            w1, [x16, #0xf]
    // 0x94962c: DecompressPointer r1
    //     0x94962c: add             x1, x1, HEAP, lsl #32
    // 0x949630: LoadField: r0 = r1->field_7
    //     0x949630: ldur            w0, [x1, #7]
    // 0x949634: DecompressPointer r0
    //     0x949634: add             x0, x0, HEAP, lsl #32
    // 0x949638: cmp             w0, NULL
    // 0x94963c: b.ne            #0x949644
    // 0x949640: r0 = ""
    //     0x949640: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x949644: stur            x0, [fp, #-0x18]
    // 0x949648: LoadField: r1 = r3->field_b
    //     0x949648: ldur            w1, [x3, #0xb]
    // 0x94964c: LoadField: r2 = r3->field_f
    //     0x94964c: ldur            w2, [x3, #0xf]
    // 0x949650: DecompressPointer r2
    //     0x949650: add             x2, x2, HEAP, lsl #32
    // 0x949654: LoadField: r4 = r2->field_b
    //     0x949654: ldur            w4, [x2, #0xb]
    // 0x949658: r2 = LoadInt32Instr(r1)
    //     0x949658: sbfx            x2, x1, #1, #0x1f
    // 0x94965c: stur            x2, [fp, #-0x28]
    // 0x949660: r1 = LoadInt32Instr(r4)
    //     0x949660: sbfx            x1, x4, #1, #0x1f
    // 0x949664: cmp             x2, x1
    // 0x949668: b.ne            #0x949674
    // 0x94966c: mov             x1, x3
    // 0x949670: r0 = _growToNextCapacity()
    //     0x949670: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x949674: ldur            x0, [fp, #-0x20]
    // 0x949678: ldur            x2, [fp, #-0x28]
    // 0x94967c: add             x1, x2, #1
    // 0x949680: lsl             x3, x1, #1
    // 0x949684: StoreField: r0->field_b = r3
    //     0x949684: stur            w3, [x0, #0xb]
    // 0x949688: LoadField: r1 = r0->field_f
    //     0x949688: ldur            w1, [x0, #0xf]
    // 0x94968c: DecompressPointer r1
    //     0x94968c: add             x1, x1, HEAP, lsl #32
    // 0x949690: ldur            x0, [fp, #-0x18]
    // 0x949694: ArrayStore: r1[r2] = r0  ; List_4
    //     0x949694: add             x25, x1, x2, lsl #2
    //     0x949698: add             x25, x25, #0xf
    //     0x94969c: str             w0, [x25]
    //     0x9496a0: tbz             w0, #0, #0x9496bc
    //     0x9496a4: ldurb           w16, [x1, #-1]
    //     0x9496a8: ldurb           w17, [x0, #-1]
    //     0x9496ac: and             x16, x17, x16, lsr #2
    //     0x9496b0: tst             x16, HEAP, lsr #32
    //     0x9496b4: b.eq            #0x9496bc
    //     0x9496b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9496bc: b               #0x949858
    // 0x9496c0: mov             x1, x0
    // 0x9496c4: LoadField: r0 = r2->field_6f
    //     0x9496c4: ldur            w0, [x2, #0x6f]
    // 0x9496c8: DecompressPointer r0
    //     0x9496c8: add             x0, x0, HEAP, lsl #32
    // 0x9496cc: cmp             w0, NULL
    // 0x9496d0: b.ne            #0x9496dc
    // 0x9496d4: r2 = Null
    //     0x9496d4: mov             x2, NULL
    // 0x9496d8: b               #0x949734
    // 0x9496dc: r16 = <AllSkuDatum?>
    //     0x9496dc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52868] TypeArguments: <AllSkuDatum?>
    //     0x9496e0: ldr             x16, [x16, #0x868]
    // 0x9496e4: stp             x0, x16, [SP]
    // 0x9496e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9496e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9496ec: r0 = cast()
    //     0x9496ec: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x9496f0: ldur            x2, [fp, #-0x10]
    // 0x9496f4: r1 = Function '<anonymous closure>':.
    //     0x9496f4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53cb0] AnonymousClosure: (0x9083cc), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::initState (0x949324)
    //     0x9496f8: ldr             x1, [x1, #0xcb0]
    // 0x9496fc: stur            x0, [fp, #-0x10]
    // 0x949700: r0 = AllocateClosure()
    //     0x949700: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x949704: r1 = Function '<anonymous closure>':.
    //     0x949704: add             x1, PP, #0x53, lsl #12  ; [pp+0x53cb8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x949708: ldr             x1, [x1, #0xcb8]
    // 0x94970c: r2 = Null
    //     0x94970c: mov             x2, NULL
    // 0x949710: stur            x0, [fp, #-0x18]
    // 0x949714: r0 = AllocateClosure()
    //     0x949714: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x949718: str             x0, [SP]
    // 0x94971c: ldur            x1, [fp, #-0x10]
    // 0x949720: ldur            x2, [fp, #-0x18]
    // 0x949724: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x949724: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x949728: ldr             x4, [x4, #0xb48]
    // 0x94972c: r0 = firstWhere()
    //     0x94972c: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x949730: mov             x2, x0
    // 0x949734: cmp             w2, NULL
    // 0x949738: b.eq            #0x9497c0
    // 0x94973c: ldur            x0, [fp, #-8]
    // 0x949740: LoadField: r1 = r0->field_b
    //     0x949740: ldur            w1, [x0, #0xb]
    // 0x949744: DecompressPointer r1
    //     0x949744: add             x1, x1, HEAP, lsl #32
    // 0x949748: cmp             w1, NULL
    // 0x94974c: b.eq            #0x94988c
    // 0x949750: LoadField: r3 = r1->field_b
    //     0x949750: ldur            w3, [x1, #0xb]
    // 0x949754: DecompressPointer r3
    //     0x949754: add             x3, x3, HEAP, lsl #32
    // 0x949758: LoadField: r1 = r3->field_6f
    //     0x949758: ldur            w1, [x3, #0x6f]
    // 0x94975c: DecompressPointer r1
    //     0x94975c: add             x1, x1, HEAP, lsl #32
    // 0x949760: cmp             w1, NULL
    // 0x949764: b.ne            #0x949770
    // 0x949768: r0 = Null
    //     0x949768: mov             x0, NULL
    // 0x94976c: b               #0x949790
    // 0x949770: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x949770: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x949774: r0 = indexOf()
    //     0x949774: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x949778: mov             x2, x0
    // 0x94977c: r0 = BoxInt64Instr(r2)
    //     0x94977c: sbfiz           x0, x2, #1, #0x1f
    //     0x949780: cmp             x2, x0, asr #1
    //     0x949784: b.eq            #0x949790
    //     0x949788: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94978c: stur            x2, [x0, #7]
    // 0x949790: cmp             w0, NULL
    // 0x949794: b.ne            #0x9497a0
    // 0x949798: r0 = 0
    //     0x949798: movz            x0, #0
    // 0x94979c: b               #0x9497b0
    // 0x9497a0: r1 = LoadInt32Instr(r0)
    //     0x9497a0: sbfx            x1, x0, #1, #0x1f
    //     0x9497a4: tbz             w0, #0, #0x9497ac
    //     0x9497a8: ldur            x1, [x0, #7]
    // 0x9497ac: mov             x0, x1
    // 0x9497b0: ldur            x2, [fp, #-8]
    // 0x9497b4: ArrayStore: r2[0] = r0  ; List_8
    //     0x9497b4: stur            x0, [x2, #0x17]
    // 0x9497b8: mov             x3, x0
    // 0x9497bc: b               #0x9497cc
    // 0x9497c0: ldur            x2, [fp, #-8]
    // 0x9497c4: ArrayStore: r2[0] = rZR  ; List_8
    //     0x9497c4: stur            xzr, [x2, #0x17]
    // 0x9497c8: r3 = 0
    //     0x9497c8: movz            x3, #0
    // 0x9497cc: LoadField: r0 = r2->field_b
    //     0x9497cc: ldur            w0, [x2, #0xb]
    // 0x9497d0: DecompressPointer r0
    //     0x9497d0: add             x0, x0, HEAP, lsl #32
    // 0x9497d4: cmp             w0, NULL
    // 0x9497d8: b.eq            #0x949890
    // 0x9497dc: LoadField: r1 = r0->field_b
    //     0x9497dc: ldur            w1, [x0, #0xb]
    // 0x9497e0: DecompressPointer r1
    //     0x9497e0: add             x1, x1, HEAP, lsl #32
    // 0x9497e4: LoadField: r4 = r1->field_6f
    //     0x9497e4: ldur            w4, [x1, #0x6f]
    // 0x9497e8: DecompressPointer r4
    //     0x9497e8: add             x4, x4, HEAP, lsl #32
    // 0x9497ec: cmp             w4, NULL
    // 0x9497f0: b.ne            #0x9497fc
    // 0x9497f4: r0 = Null
    //     0x9497f4: mov             x0, NULL
    // 0x9497f8: b               #0x94982c
    // 0x9497fc: LoadField: r0 = r4->field_b
    //     0x9497fc: ldur            w0, [x4, #0xb]
    // 0x949800: r1 = LoadInt32Instr(r0)
    //     0x949800: sbfx            x1, x0, #1, #0x1f
    // 0x949804: mov             x0, x1
    // 0x949808: mov             x1, x3
    // 0x94980c: cmp             x1, x0
    // 0x949810: b.hs            #0x949894
    // 0x949814: LoadField: r0 = r4->field_f
    //     0x949814: ldur            w0, [x4, #0xf]
    // 0x949818: DecompressPointer r0
    //     0x949818: add             x0, x0, HEAP, lsl #32
    // 0x94981c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x94981c: add             x16, x0, x3, lsl #2
    //     0x949820: ldur            w1, [x16, #0xf]
    // 0x949824: DecompressPointer r1
    //     0x949824: add             x1, x1, HEAP, lsl #32
    // 0x949828: mov             x0, x1
    // 0x94982c: cmp             w0, NULL
    // 0x949830: b.ne            #0x949838
    // 0x949834: r0 = AllSkuDatum()
    //     0x949834: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x949838: ldur            x1, [fp, #-8]
    // 0x94983c: StoreField: r1->field_13 = r0
    //     0x94983c: stur            w0, [x1, #0x13]
    //     0x949840: ldurb           w16, [x1, #-1]
    //     0x949844: ldurb           w17, [x0, #-1]
    //     0x949848: and             x16, x17, x16, lsr #2
    //     0x94984c: tst             x16, HEAP, lsr #32
    //     0x949850: b.eq            #0x949858
    //     0x949854: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x949858: r0 = Null
    //     0x949858: mov             x0, NULL
    // 0x94985c: LeaveFrame
    //     0x94985c: mov             SP, fp
    //     0x949860: ldp             fp, lr, [SP], #0x10
    // 0x949864: ret
    //     0x949864: ret             
    // 0x949868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949868: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94986c: b               #0x949340
    // 0x949870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949870: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949874: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949874: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949878: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94987c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94987c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949880: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x949880: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x949884: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x949884: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x949888: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x949888: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94988c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94988c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949894: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x949894: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa61dd4, size: 0x408
    // 0xa61dd4: EnterFrame
    //     0xa61dd4: stp             fp, lr, [SP, #-0x10]!
    //     0xa61dd8: mov             fp, SP
    // 0xa61ddc: AllocStack(0x28)
    //     0xa61ddc: sub             SP, SP, #0x28
    // 0xa61de0: SetupParameters()
    //     0xa61de0: ldr             x0, [fp, #0x10]
    //     0xa61de4: ldur            w2, [x0, #0x17]
    //     0xa61de8: add             x2, x2, HEAP, lsl #32
    //     0xa61dec: stur            x2, [fp, #-0x10]
    // 0xa61df0: CheckStackOverflow
    //     0xa61df0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa61df4: cmp             SP, x16
    //     0xa61df8: b.ls            #0xa621b0
    // 0xa61dfc: LoadField: r0 = r2->field_b
    //     0xa61dfc: ldur            w0, [x2, #0xb]
    // 0xa61e00: DecompressPointer r0
    //     0xa61e00: add             x0, x0, HEAP, lsl #32
    // 0xa61e04: stur            x0, [fp, #-8]
    // 0xa61e08: LoadField: r1 = r0->field_f
    //     0xa61e08: ldur            w1, [x0, #0xf]
    // 0xa61e0c: DecompressPointer r1
    //     0xa61e0c: add             x1, x1, HEAP, lsl #32
    // 0xa61e10: LoadField: r3 = r1->field_27
    //     0xa61e10: ldur            w3, [x1, #0x27]
    // 0xa61e14: DecompressPointer r3
    //     0xa61e14: add             x3, x3, HEAP, lsl #32
    // 0xa61e18: mov             x1, x3
    // 0xa61e1c: r0 = clear()
    //     0xa61e1c: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xa61e20: ldur            x3, [fp, #-8]
    // 0xa61e24: LoadField: r0 = r3->field_f
    //     0xa61e24: ldur            w0, [x3, #0xf]
    // 0xa61e28: DecompressPointer r0
    //     0xa61e28: add             x0, x0, HEAP, lsl #32
    // 0xa61e2c: LoadField: r2 = r0->field_27
    //     0xa61e2c: ldur            w2, [x0, #0x27]
    // 0xa61e30: DecompressPointer r2
    //     0xa61e30: add             x2, x2, HEAP, lsl #32
    // 0xa61e34: LoadField: r1 = r0->field_b
    //     0xa61e34: ldur            w1, [x0, #0xb]
    // 0xa61e38: DecompressPointer r1
    //     0xa61e38: add             x1, x1, HEAP, lsl #32
    // 0xa61e3c: cmp             w1, NULL
    // 0xa61e40: b.eq            #0xa621b8
    // 0xa61e44: LoadField: r0 = r1->field_b
    //     0xa61e44: ldur            w0, [x1, #0xb]
    // 0xa61e48: DecompressPointer r0
    //     0xa61e48: add             x0, x0, HEAP, lsl #32
    // 0xa61e4c: LoadField: r4 = r0->field_73
    //     0xa61e4c: ldur            w4, [x0, #0x73]
    // 0xa61e50: DecompressPointer r4
    //     0xa61e50: add             x4, x4, HEAP, lsl #32
    // 0xa61e54: cmp             w4, NULL
    // 0xa61e58: b.ne            #0xa61e68
    // 0xa61e5c: ldur            x5, [fp, #-0x10]
    // 0xa61e60: r0 = Null
    //     0xa61e60: mov             x0, NULL
    // 0xa61e64: b               #0xa61eb0
    // 0xa61e68: ldur            x5, [fp, #-0x10]
    // 0xa61e6c: LoadField: r0 = r5->field_f
    //     0xa61e6c: ldur            w0, [x5, #0xf]
    // 0xa61e70: DecompressPointer r0
    //     0xa61e70: add             x0, x0, HEAP, lsl #32
    // 0xa61e74: LoadField: r1 = r4->field_b
    //     0xa61e74: ldur            w1, [x4, #0xb]
    // 0xa61e78: r6 = LoadInt32Instr(r0)
    //     0xa61e78: sbfx            x6, x0, #1, #0x1f
    //     0xa61e7c: tbz             w0, #0, #0xa61e84
    //     0xa61e80: ldur            x6, [x0, #7]
    // 0xa61e84: r0 = LoadInt32Instr(r1)
    //     0xa61e84: sbfx            x0, x1, #1, #0x1f
    // 0xa61e88: mov             x1, x6
    // 0xa61e8c: cmp             x1, x0
    // 0xa61e90: b.hs            #0xa621bc
    // 0xa61e94: LoadField: r0 = r4->field_f
    //     0xa61e94: ldur            w0, [x4, #0xf]
    // 0xa61e98: DecompressPointer r0
    //     0xa61e98: add             x0, x0, HEAP, lsl #32
    // 0xa61e9c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa61e9c: add             x16, x0, x6, lsl #2
    //     0xa61ea0: ldur            w1, [x16, #0xf]
    // 0xa61ea4: DecompressPointer r1
    //     0xa61ea4: add             x1, x1, HEAP, lsl #32
    // 0xa61ea8: LoadField: r0 = r1->field_7
    //     0xa61ea8: ldur            w0, [x1, #7]
    // 0xa61eac: DecompressPointer r0
    //     0xa61eac: add             x0, x0, HEAP, lsl #32
    // 0xa61eb0: cmp             w0, NULL
    // 0xa61eb4: b.ne            #0xa61ebc
    // 0xa61eb8: r0 = ""
    //     0xa61eb8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa61ebc: mov             x1, x2
    // 0xa61ec0: mov             x2, x0
    // 0xa61ec4: r0 = contains()
    //     0xa61ec4: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa61ec8: tbnz            w0, #4, #0xa61f78
    // 0xa61ecc: ldur            x3, [fp, #-8]
    // 0xa61ed0: LoadField: r0 = r3->field_f
    //     0xa61ed0: ldur            w0, [x3, #0xf]
    // 0xa61ed4: DecompressPointer r0
    //     0xa61ed4: add             x0, x0, HEAP, lsl #32
    // 0xa61ed8: LoadField: r2 = r0->field_27
    //     0xa61ed8: ldur            w2, [x0, #0x27]
    // 0xa61edc: DecompressPointer r2
    //     0xa61edc: add             x2, x2, HEAP, lsl #32
    // 0xa61ee0: LoadField: r1 = r0->field_b
    //     0xa61ee0: ldur            w1, [x0, #0xb]
    // 0xa61ee4: DecompressPointer r1
    //     0xa61ee4: add             x1, x1, HEAP, lsl #32
    // 0xa61ee8: cmp             w1, NULL
    // 0xa61eec: b.eq            #0xa621c0
    // 0xa61ef0: LoadField: r0 = r1->field_b
    //     0xa61ef0: ldur            w0, [x1, #0xb]
    // 0xa61ef4: DecompressPointer r0
    //     0xa61ef4: add             x0, x0, HEAP, lsl #32
    // 0xa61ef8: LoadField: r4 = r0->field_73
    //     0xa61ef8: ldur            w4, [x0, #0x73]
    // 0xa61efc: DecompressPointer r4
    //     0xa61efc: add             x4, x4, HEAP, lsl #32
    // 0xa61f00: cmp             w4, NULL
    // 0xa61f04: b.ne            #0xa61f14
    // 0xa61f08: ldur            x5, [fp, #-0x10]
    // 0xa61f0c: r0 = Null
    //     0xa61f0c: mov             x0, NULL
    // 0xa61f10: b               #0xa61f5c
    // 0xa61f14: ldur            x5, [fp, #-0x10]
    // 0xa61f18: LoadField: r0 = r5->field_f
    //     0xa61f18: ldur            w0, [x5, #0xf]
    // 0xa61f1c: DecompressPointer r0
    //     0xa61f1c: add             x0, x0, HEAP, lsl #32
    // 0xa61f20: LoadField: r1 = r4->field_b
    //     0xa61f20: ldur            w1, [x4, #0xb]
    // 0xa61f24: r6 = LoadInt32Instr(r0)
    //     0xa61f24: sbfx            x6, x0, #1, #0x1f
    //     0xa61f28: tbz             w0, #0, #0xa61f30
    //     0xa61f2c: ldur            x6, [x0, #7]
    // 0xa61f30: r0 = LoadInt32Instr(r1)
    //     0xa61f30: sbfx            x0, x1, #1, #0x1f
    // 0xa61f34: mov             x1, x6
    // 0xa61f38: cmp             x1, x0
    // 0xa61f3c: b.hs            #0xa621c4
    // 0xa61f40: LoadField: r0 = r4->field_f
    //     0xa61f40: ldur            w0, [x4, #0xf]
    // 0xa61f44: DecompressPointer r0
    //     0xa61f44: add             x0, x0, HEAP, lsl #32
    // 0xa61f48: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa61f48: add             x16, x0, x6, lsl #2
    //     0xa61f4c: ldur            w1, [x16, #0xf]
    // 0xa61f50: DecompressPointer r1
    //     0xa61f50: add             x1, x1, HEAP, lsl #32
    // 0xa61f54: LoadField: r0 = r1->field_7
    //     0xa61f54: ldur            w0, [x1, #7]
    // 0xa61f58: DecompressPointer r0
    //     0xa61f58: add             x0, x0, HEAP, lsl #32
    // 0xa61f5c: cmp             w0, NULL
    // 0xa61f60: b.ne            #0xa61f68
    // 0xa61f64: r0 = ""
    //     0xa61f64: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa61f68: mov             x1, x2
    // 0xa61f6c: mov             x2, x0
    // 0xa61f70: r0 = remove()
    //     0xa61f70: bl              #0x71df18  ; [dart:core] _GrowableList::remove
    // 0xa61f74: b               #0xa62090
    // 0xa61f78: ldur            x2, [fp, #-8]
    // 0xa61f7c: LoadField: r0 = r2->field_f
    //     0xa61f7c: ldur            w0, [x2, #0xf]
    // 0xa61f80: DecompressPointer r0
    //     0xa61f80: add             x0, x0, HEAP, lsl #32
    // 0xa61f84: LoadField: r3 = r0->field_27
    //     0xa61f84: ldur            w3, [x0, #0x27]
    // 0xa61f88: DecompressPointer r3
    //     0xa61f88: add             x3, x3, HEAP, lsl #32
    // 0xa61f8c: stur            x3, [fp, #-0x28]
    // 0xa61f90: LoadField: r1 = r0->field_b
    //     0xa61f90: ldur            w1, [x0, #0xb]
    // 0xa61f94: DecompressPointer r1
    //     0xa61f94: add             x1, x1, HEAP, lsl #32
    // 0xa61f98: cmp             w1, NULL
    // 0xa61f9c: b.eq            #0xa621c8
    // 0xa61fa0: LoadField: r0 = r1->field_b
    //     0xa61fa0: ldur            w0, [x1, #0xb]
    // 0xa61fa4: DecompressPointer r0
    //     0xa61fa4: add             x0, x0, HEAP, lsl #32
    // 0xa61fa8: LoadField: r4 = r0->field_73
    //     0xa61fa8: ldur            w4, [x0, #0x73]
    // 0xa61fac: DecompressPointer r4
    //     0xa61fac: add             x4, x4, HEAP, lsl #32
    // 0xa61fb0: cmp             w4, NULL
    // 0xa61fb4: b.ne            #0xa61fc4
    // 0xa61fb8: ldur            x5, [fp, #-0x10]
    // 0xa61fbc: r0 = Null
    //     0xa61fbc: mov             x0, NULL
    // 0xa61fc0: b               #0xa6200c
    // 0xa61fc4: ldur            x5, [fp, #-0x10]
    // 0xa61fc8: LoadField: r0 = r5->field_f
    //     0xa61fc8: ldur            w0, [x5, #0xf]
    // 0xa61fcc: DecompressPointer r0
    //     0xa61fcc: add             x0, x0, HEAP, lsl #32
    // 0xa61fd0: LoadField: r1 = r4->field_b
    //     0xa61fd0: ldur            w1, [x4, #0xb]
    // 0xa61fd4: r6 = LoadInt32Instr(r0)
    //     0xa61fd4: sbfx            x6, x0, #1, #0x1f
    //     0xa61fd8: tbz             w0, #0, #0xa61fe0
    //     0xa61fdc: ldur            x6, [x0, #7]
    // 0xa61fe0: r0 = LoadInt32Instr(r1)
    //     0xa61fe0: sbfx            x0, x1, #1, #0x1f
    // 0xa61fe4: mov             x1, x6
    // 0xa61fe8: cmp             x1, x0
    // 0xa61fec: b.hs            #0xa621cc
    // 0xa61ff0: LoadField: r0 = r4->field_f
    //     0xa61ff0: ldur            w0, [x4, #0xf]
    // 0xa61ff4: DecompressPointer r0
    //     0xa61ff4: add             x0, x0, HEAP, lsl #32
    // 0xa61ff8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa61ff8: add             x16, x0, x6, lsl #2
    //     0xa61ffc: ldur            w1, [x16, #0xf]
    // 0xa62000: DecompressPointer r1
    //     0xa62000: add             x1, x1, HEAP, lsl #32
    // 0xa62004: LoadField: r0 = r1->field_7
    //     0xa62004: ldur            w0, [x1, #7]
    // 0xa62008: DecompressPointer r0
    //     0xa62008: add             x0, x0, HEAP, lsl #32
    // 0xa6200c: cmp             w0, NULL
    // 0xa62010: b.ne            #0xa62018
    // 0xa62014: r0 = ""
    //     0xa62014: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa62018: stur            x0, [fp, #-0x20]
    // 0xa6201c: LoadField: r1 = r3->field_b
    //     0xa6201c: ldur            w1, [x3, #0xb]
    // 0xa62020: LoadField: r4 = r3->field_f
    //     0xa62020: ldur            w4, [x3, #0xf]
    // 0xa62024: DecompressPointer r4
    //     0xa62024: add             x4, x4, HEAP, lsl #32
    // 0xa62028: LoadField: r6 = r4->field_b
    //     0xa62028: ldur            w6, [x4, #0xb]
    // 0xa6202c: r4 = LoadInt32Instr(r1)
    //     0xa6202c: sbfx            x4, x1, #1, #0x1f
    // 0xa62030: stur            x4, [fp, #-0x18]
    // 0xa62034: r1 = LoadInt32Instr(r6)
    //     0xa62034: sbfx            x1, x6, #1, #0x1f
    // 0xa62038: cmp             x4, x1
    // 0xa6203c: b.ne            #0xa62048
    // 0xa62040: mov             x1, x3
    // 0xa62044: r0 = _growToNextCapacity()
    //     0xa62044: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa62048: ldur            x0, [fp, #-0x28]
    // 0xa6204c: ldur            x2, [fp, #-0x18]
    // 0xa62050: add             x1, x2, #1
    // 0xa62054: lsl             x3, x1, #1
    // 0xa62058: StoreField: r0->field_b = r3
    //     0xa62058: stur            w3, [x0, #0xb]
    // 0xa6205c: LoadField: r1 = r0->field_f
    //     0xa6205c: ldur            w1, [x0, #0xf]
    // 0xa62060: DecompressPointer r1
    //     0xa62060: add             x1, x1, HEAP, lsl #32
    // 0xa62064: ldur            x0, [fp, #-0x20]
    // 0xa62068: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa62068: add             x25, x1, x2, lsl #2
    //     0xa6206c: add             x25, x25, #0xf
    //     0xa62070: str             w0, [x25]
    //     0xa62074: tbz             w0, #0, #0xa62090
    //     0xa62078: ldurb           w16, [x1, #-1]
    //     0xa6207c: ldurb           w17, [x0, #-1]
    //     0xa62080: and             x16, x17, x16, lsr #2
    //     0xa62084: tst             x16, HEAP, lsr #32
    //     0xa62088: b.eq            #0xa62090
    //     0xa6208c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa62090: ldur            x0, [fp, #-8]
    // 0xa62094: LoadField: r2 = r0->field_f
    //     0xa62094: ldur            w2, [x0, #0xf]
    // 0xa62098: DecompressPointer r2
    //     0xa62098: add             x2, x2, HEAP, lsl #32
    // 0xa6209c: stur            x2, [fp, #-0x20]
    // 0xa620a0: LoadField: r0 = r2->field_b
    //     0xa620a0: ldur            w0, [x2, #0xb]
    // 0xa620a4: DecompressPointer r0
    //     0xa620a4: add             x0, x0, HEAP, lsl #32
    // 0xa620a8: cmp             w0, NULL
    // 0xa620ac: b.eq            #0xa621d0
    // 0xa620b0: LoadField: r1 = r0->field_b
    //     0xa620b0: ldur            w1, [x0, #0xb]
    // 0xa620b4: DecompressPointer r1
    //     0xa620b4: add             x1, x1, HEAP, lsl #32
    // 0xa620b8: LoadField: r3 = r1->field_73
    //     0xa620b8: ldur            w3, [x1, #0x73]
    // 0xa620bc: DecompressPointer r3
    //     0xa620bc: add             x3, x3, HEAP, lsl #32
    // 0xa620c0: cmp             w3, NULL
    // 0xa620c4: b.ne            #0xa620d4
    // 0xa620c8: ldur            x4, [fp, #-0x10]
    // 0xa620cc: r0 = Null
    //     0xa620cc: mov             x0, NULL
    // 0xa620d0: b               #0xa62158
    // 0xa620d4: ldur            x4, [fp, #-0x10]
    // 0xa620d8: LoadField: r0 = r4->field_f
    //     0xa620d8: ldur            w0, [x4, #0xf]
    // 0xa620dc: DecompressPointer r0
    //     0xa620dc: add             x0, x0, HEAP, lsl #32
    // 0xa620e0: LoadField: r1 = r3->field_b
    //     0xa620e0: ldur            w1, [x3, #0xb]
    // 0xa620e4: r5 = LoadInt32Instr(r0)
    //     0xa620e4: sbfx            x5, x0, #1, #0x1f
    //     0xa620e8: tbz             w0, #0, #0xa620f0
    //     0xa620ec: ldur            x5, [x0, #7]
    // 0xa620f0: r0 = LoadInt32Instr(r1)
    //     0xa620f0: sbfx            x0, x1, #1, #0x1f
    // 0xa620f4: mov             x1, x5
    // 0xa620f8: cmp             x1, x0
    // 0xa620fc: b.hs            #0xa621d4
    // 0xa62100: LoadField: r0 = r3->field_f
    //     0xa62100: ldur            w0, [x3, #0xf]
    // 0xa62104: DecompressPointer r0
    //     0xa62104: add             x0, x0, HEAP, lsl #32
    // 0xa62108: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa62108: add             x16, x0, x5, lsl #2
    //     0xa6210c: ldur            w1, [x16, #0xf]
    // 0xa62110: DecompressPointer r1
    //     0xa62110: add             x1, x1, HEAP, lsl #32
    // 0xa62114: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xa62114: ldur            w3, [x1, #0x17]
    // 0xa62118: DecompressPointer r3
    //     0xa62118: add             x3, x3, HEAP, lsl #32
    // 0xa6211c: cmp             w3, NULL
    // 0xa62120: b.ne            #0xa6212c
    // 0xa62124: r0 = Null
    //     0xa62124: mov             x0, NULL
    // 0xa62128: b               #0xa62158
    // 0xa6212c: LoadField: r0 = r3->field_b
    //     0xa6212c: ldur            w0, [x3, #0xb]
    // 0xa62130: r1 = LoadInt32Instr(r0)
    //     0xa62130: sbfx            x1, x0, #1, #0x1f
    // 0xa62134: mov             x0, x1
    // 0xa62138: r1 = 0
    //     0xa62138: movz            x1, #0
    // 0xa6213c: cmp             x1, x0
    // 0xa62140: b.hs            #0xa621d8
    // 0xa62144: LoadField: r0 = r3->field_f
    //     0xa62144: ldur            w0, [x3, #0xf]
    // 0xa62148: DecompressPointer r0
    //     0xa62148: add             x0, x0, HEAP, lsl #32
    // 0xa6214c: LoadField: r1 = r0->field_f
    //     0xa6214c: ldur            w1, [x0, #0xf]
    // 0xa62150: DecompressPointer r1
    //     0xa62150: add             x1, x1, HEAP, lsl #32
    // 0xa62154: mov             x0, x1
    // 0xa62158: cmp             w0, NULL
    // 0xa6215c: b.ne            #0xa62164
    // 0xa62160: r0 = AllSkuDatum()
    //     0xa62160: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0xa62164: ldur            x2, [fp, #-0x10]
    // 0xa62168: ldur            x1, [fp, #-0x20]
    // 0xa6216c: StoreField: r1->field_13 = r0
    //     0xa6216c: stur            w0, [x1, #0x13]
    //     0xa62170: ldurb           w16, [x1, #-1]
    //     0xa62174: ldurb           w17, [x0, #-1]
    //     0xa62178: and             x16, x17, x16, lsr #2
    //     0xa6217c: tst             x16, HEAP, lsr #32
    //     0xa62180: b.eq            #0xa62188
    //     0xa62184: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa62188: LoadField: r3 = r2->field_f
    //     0xa62188: ldur            w3, [x2, #0xf]
    // 0xa6218c: DecompressPointer r3
    //     0xa6218c: add             x3, x3, HEAP, lsl #32
    // 0xa62190: r2 = LoadInt32Instr(r3)
    //     0xa62190: sbfx            x2, x3, #1, #0x1f
    //     0xa62194: tbz             w3, #0, #0xa6219c
    //     0xa62198: ldur            x2, [x3, #7]
    // 0xa6219c: StoreField: r1->field_1f = r2
    //     0xa6219c: stur            x2, [x1, #0x1f]
    // 0xa621a0: r0 = Null
    //     0xa621a0: mov             x0, NULL
    // 0xa621a4: LeaveFrame
    //     0xa621a4: mov             SP, fp
    //     0xa621a8: ldp             fp, lr, [SP], #0x10
    // 0xa621ac: ret
    //     0xa621ac: ret             
    // 0xa621b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa621b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa621b4: b               #0xa61dfc
    // 0xa621b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa621b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa621bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa621bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa621c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa621c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa621c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa621c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa621c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa621c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa621cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa621cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa621d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa621d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa621d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa621d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa621d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa621d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa621dc, size: 0x68
    // 0xa621dc: EnterFrame
    //     0xa621dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa621e0: mov             fp, SP
    // 0xa621e4: AllocStack(0x8)
    //     0xa621e4: sub             SP, SP, #8
    // 0xa621e8: SetupParameters()
    //     0xa621e8: ldr             x0, [fp, #0x10]
    //     0xa621ec: ldur            w2, [x0, #0x17]
    //     0xa621f0: add             x2, x2, HEAP, lsl #32
    // 0xa621f4: CheckStackOverflow
    //     0xa621f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa621f8: cmp             SP, x16
    //     0xa621fc: b.ls            #0xa6223c
    // 0xa62200: LoadField: r0 = r2->field_b
    //     0xa62200: ldur            w0, [x2, #0xb]
    // 0xa62204: DecompressPointer r0
    //     0xa62204: add             x0, x0, HEAP, lsl #32
    // 0xa62208: LoadField: r3 = r0->field_f
    //     0xa62208: ldur            w3, [x0, #0xf]
    // 0xa6220c: DecompressPointer r3
    //     0xa6220c: add             x3, x3, HEAP, lsl #32
    // 0xa62210: stur            x3, [fp, #-8]
    // 0xa62214: r1 = Function '<anonymous closure>':.
    //     0xa62214: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c90] AnonymousClosure: (0xa61dd4), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xa62218: ldr             x1, [x1, #0xc90]
    // 0xa6221c: r0 = AllocateClosure()
    //     0xa6221c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa62220: ldur            x1, [fp, #-8]
    // 0xa62224: mov             x2, x0
    // 0xa62228: r0 = setState()
    //     0xa62228: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa6222c: r0 = Null
    //     0xa6222c: mov             x0, NULL
    // 0xa62230: LeaveFrame
    //     0xa62230: mov             SP, fp
    //     0xa62234: ldp             fp, lr, [SP], #0x10
    // 0xa62238: ret
    //     0xa62238: ret             
    // 0xa6223c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6223c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62240: b               #0xa62200
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa62244, size: 0x78c
    // 0xa62244: EnterFrame
    //     0xa62244: stp             fp, lr, [SP, #-0x10]!
    //     0xa62248: mov             fp, SP
    // 0xa6224c: AllocStack(0x60)
    //     0xa6224c: sub             SP, SP, #0x60
    // 0xa62250: SetupParameters()
    //     0xa62250: ldr             x0, [fp, #0x20]
    //     0xa62254: ldur            w1, [x0, #0x17]
    //     0xa62258: add             x1, x1, HEAP, lsl #32
    //     0xa6225c: stur            x1, [fp, #-8]
    // 0xa62260: CheckStackOverflow
    //     0xa62260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa62264: cmp             SP, x16
    //     0xa62268: b.ls            #0xa629a0
    // 0xa6226c: r1 = 1
    //     0xa6226c: movz            x1, #0x1
    // 0xa62270: r0 = AllocateContext()
    //     0xa62270: bl              #0x16f6108  ; AllocateContextStub
    // 0xa62274: mov             x4, x0
    // 0xa62278: ldur            x3, [fp, #-8]
    // 0xa6227c: stur            x4, [fp, #-0x10]
    // 0xa62280: StoreField: r4->field_b = r3
    //     0xa62280: stur            w3, [x4, #0xb]
    // 0xa62284: ldr             x5, [fp, #0x10]
    // 0xa62288: StoreField: r4->field_f = r5
    //     0xa62288: stur            w5, [x4, #0xf]
    // 0xa6228c: LoadField: r0 = r3->field_f
    //     0xa6228c: ldur            w0, [x3, #0xf]
    // 0xa62290: DecompressPointer r0
    //     0xa62290: add             x0, x0, HEAP, lsl #32
    // 0xa62294: LoadField: r2 = r0->field_27
    //     0xa62294: ldur            w2, [x0, #0x27]
    // 0xa62298: DecompressPointer r2
    //     0xa62298: add             x2, x2, HEAP, lsl #32
    // 0xa6229c: LoadField: r1 = r0->field_b
    //     0xa6229c: ldur            w1, [x0, #0xb]
    // 0xa622a0: DecompressPointer r1
    //     0xa622a0: add             x1, x1, HEAP, lsl #32
    // 0xa622a4: cmp             w1, NULL
    // 0xa622a8: b.eq            #0xa629a8
    // 0xa622ac: LoadField: r0 = r1->field_b
    //     0xa622ac: ldur            w0, [x1, #0xb]
    // 0xa622b0: DecompressPointer r0
    //     0xa622b0: add             x0, x0, HEAP, lsl #32
    // 0xa622b4: LoadField: r6 = r0->field_73
    //     0xa622b4: ldur            w6, [x0, #0x73]
    // 0xa622b8: DecompressPointer r6
    //     0xa622b8: add             x6, x6, HEAP, lsl #32
    // 0xa622bc: cmp             w6, NULL
    // 0xa622c0: b.ne            #0xa622cc
    // 0xa622c4: r0 = Null
    //     0xa622c4: mov             x0, NULL
    // 0xa622c8: b               #0xa6230c
    // 0xa622cc: LoadField: r0 = r6->field_b
    //     0xa622cc: ldur            w0, [x6, #0xb]
    // 0xa622d0: r7 = LoadInt32Instr(r5)
    //     0xa622d0: sbfx            x7, x5, #1, #0x1f
    //     0xa622d4: tbz             w5, #0, #0xa622dc
    //     0xa622d8: ldur            x7, [x5, #7]
    // 0xa622dc: r1 = LoadInt32Instr(r0)
    //     0xa622dc: sbfx            x1, x0, #1, #0x1f
    // 0xa622e0: mov             x0, x1
    // 0xa622e4: mov             x1, x7
    // 0xa622e8: cmp             x1, x0
    // 0xa622ec: b.hs            #0xa629ac
    // 0xa622f0: LoadField: r0 = r6->field_f
    //     0xa622f0: ldur            w0, [x6, #0xf]
    // 0xa622f4: DecompressPointer r0
    //     0xa622f4: add             x0, x0, HEAP, lsl #32
    // 0xa622f8: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa622f8: add             x16, x0, x7, lsl #2
    //     0xa622fc: ldur            w1, [x16, #0xf]
    // 0xa62300: DecompressPointer r1
    //     0xa62300: add             x1, x1, HEAP, lsl #32
    // 0xa62304: LoadField: r0 = r1->field_7
    //     0xa62304: ldur            w0, [x1, #7]
    // 0xa62308: DecompressPointer r0
    //     0xa62308: add             x0, x0, HEAP, lsl #32
    // 0xa6230c: cmp             w0, NULL
    // 0xa62310: b.ne            #0xa62318
    // 0xa62314: r0 = ""
    //     0xa62314: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa62318: mov             x1, x2
    // 0xa6231c: mov             x2, x0
    // 0xa62320: r0 = contains()
    //     0xa62320: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa62324: tbnz            w0, #4, #0xa6237c
    // 0xa62328: ldr             x1, [fp, #0x18]
    // 0xa6232c: r0 = of()
    //     0xa6232c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa62330: LoadField: r2 = r0->field_5b
    //     0xa62330: ldur            w2, [x0, #0x5b]
    // 0xa62334: DecompressPointer r2
    //     0xa62334: add             x2, x2, HEAP, lsl #32
    // 0xa62338: r16 = 2.000000
    //     0xa62338: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xa6233c: ldr             x16, [x16, #0xdf8]
    // 0xa62340: str             x16, [SP]
    // 0xa62344: r1 = Null
    //     0xa62344: mov             x1, NULL
    // 0xa62348: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xa62348: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xa6234c: ldr             x4, [x4, #0x108]
    // 0xa62350: r0 = Border.all()
    //     0xa62350: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa62354: stur            x0, [fp, #-0x18]
    // 0xa62358: r0 = BoxDecoration()
    //     0xa62358: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa6235c: mov             x1, x0
    // 0xa62360: ldur            x0, [fp, #-0x18]
    // 0xa62364: StoreField: r1->field_f = r0
    //     0xa62364: stur            w0, [x1, #0xf]
    // 0xa62368: r0 = Instance_BoxShape
    //     0xa62368: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa6236c: ldr             x0, [x0, #0x80]
    // 0xa62370: StoreField: r1->field_23 = r0
    //     0xa62370: stur            w0, [x1, #0x23]
    // 0xa62374: mov             x2, x1
    // 0xa62378: b               #0xa623bc
    // 0xa6237c: r0 = Instance_BoxShape
    //     0xa6237c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa62380: ldr             x0, [x0, #0x80]
    // 0xa62384: r1 = Null
    //     0xa62384: mov             x1, NULL
    // 0xa62388: r2 = Instance_Color
    //     0xa62388: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xa6238c: ldr             x2, [x2, #0xf88]
    // 0xa62390: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa62390: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa62394: r0 = Border.all()
    //     0xa62394: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa62398: stur            x0, [fp, #-0x18]
    // 0xa6239c: r0 = BoxDecoration()
    //     0xa6239c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa623a0: mov             x1, x0
    // 0xa623a4: ldur            x0, [fp, #-0x18]
    // 0xa623a8: StoreField: r1->field_f = r0
    //     0xa623a8: stur            w0, [x1, #0xf]
    // 0xa623ac: r0 = Instance_BoxShape
    //     0xa623ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa623b0: ldr             x0, [x0, #0x80]
    // 0xa623b4: StoreField: r1->field_23 = r0
    //     0xa623b4: stur            w0, [x1, #0x23]
    // 0xa623b8: mov             x2, x1
    // 0xa623bc: ldur            x1, [fp, #-8]
    // 0xa623c0: stur            x2, [fp, #-0x18]
    // 0xa623c4: r0 = ImageHeaders.forImages()
    //     0xa623c4: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa623c8: mov             x4, x0
    // 0xa623cc: ldur            x3, [fp, #-8]
    // 0xa623d0: stur            x4, [fp, #-0x28]
    // 0xa623d4: LoadField: r0 = r3->field_f
    //     0xa623d4: ldur            w0, [x3, #0xf]
    // 0xa623d8: DecompressPointer r0
    //     0xa623d8: add             x0, x0, HEAP, lsl #32
    // 0xa623dc: LoadField: r1 = r0->field_b
    //     0xa623dc: ldur            w1, [x0, #0xb]
    // 0xa623e0: DecompressPointer r1
    //     0xa623e0: add             x1, x1, HEAP, lsl #32
    // 0xa623e4: cmp             w1, NULL
    // 0xa623e8: b.eq            #0xa629b0
    // 0xa623ec: LoadField: r0 = r1->field_b
    //     0xa623ec: ldur            w0, [x1, #0xb]
    // 0xa623f0: DecompressPointer r0
    //     0xa623f0: add             x0, x0, HEAP, lsl #32
    // 0xa623f4: LoadField: r2 = r0->field_73
    //     0xa623f4: ldur            w2, [x0, #0x73]
    // 0xa623f8: DecompressPointer r2
    //     0xa623f8: add             x2, x2, HEAP, lsl #32
    // 0xa623fc: cmp             w2, NULL
    // 0xa62400: b.ne            #0xa62410
    // 0xa62404: ldr             x5, [fp, #0x10]
    // 0xa62408: r0 = Null
    //     0xa62408: mov             x0, NULL
    // 0xa6240c: b               #0xa62454
    // 0xa62410: ldr             x5, [fp, #0x10]
    // 0xa62414: LoadField: r0 = r2->field_b
    //     0xa62414: ldur            w0, [x2, #0xb]
    // 0xa62418: r6 = LoadInt32Instr(r5)
    //     0xa62418: sbfx            x6, x5, #1, #0x1f
    //     0xa6241c: tbz             w5, #0, #0xa62424
    //     0xa62420: ldur            x6, [x5, #7]
    // 0xa62424: r1 = LoadInt32Instr(r0)
    //     0xa62424: sbfx            x1, x0, #1, #0x1f
    // 0xa62428: mov             x0, x1
    // 0xa6242c: mov             x1, x6
    // 0xa62430: cmp             x1, x0
    // 0xa62434: b.hs            #0xa629b4
    // 0xa62438: LoadField: r0 = r2->field_f
    //     0xa62438: ldur            w0, [x2, #0xf]
    // 0xa6243c: DecompressPointer r0
    //     0xa6243c: add             x0, x0, HEAP, lsl #32
    // 0xa62440: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa62440: add             x16, x0, x6, lsl #2
    //     0xa62444: ldur            w1, [x16, #0xf]
    // 0xa62448: DecompressPointer r1
    //     0xa62448: add             x1, x1, HEAP, lsl #32
    // 0xa6244c: LoadField: r0 = r1->field_f
    //     0xa6244c: ldur            w0, [x1, #0xf]
    // 0xa62450: DecompressPointer r0
    //     0xa62450: add             x0, x0, HEAP, lsl #32
    // 0xa62454: cmp             w0, NULL
    // 0xa62458: b.ne            #0xa62460
    // 0xa6245c: r0 = ""
    //     0xa6245c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa62460: stur            x0, [fp, #-0x20]
    // 0xa62464: r1 = Function '<anonymous closure>':.
    //     0xa62464: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c78] AnonymousClosure: (0xa629d0), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xa62468: ldr             x1, [x1, #0xc78]
    // 0xa6246c: r2 = Null
    //     0xa6246c: mov             x2, NULL
    // 0xa62470: r0 = AllocateClosure()
    //     0xa62470: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa62474: r1 = Function '<anonymous closure>':.
    //     0xa62474: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c80] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa62478: ldr             x1, [x1, #0xc80]
    // 0xa6247c: r2 = Null
    //     0xa6247c: mov             x2, NULL
    // 0xa62480: stur            x0, [fp, #-0x30]
    // 0xa62484: r0 = AllocateClosure()
    //     0xa62484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa62488: stur            x0, [fp, #-0x38]
    // 0xa6248c: r0 = CachedNetworkImage()
    //     0xa6248c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa62490: stur            x0, [fp, #-0x40]
    // 0xa62494: ldur            x16, [fp, #-0x28]
    // 0xa62498: r30 = Instance_BoxFit
    //     0xa62498: add             lr, PP, #0x4e, lsl #12  ; [pp+0x4ea20] Obj!BoxFit@d738a1
    //     0xa6249c: ldr             lr, [lr, #0xa20]
    // 0xa624a0: stp             lr, x16, [SP, #0x10]
    // 0xa624a4: ldur            x16, [fp, #-0x30]
    // 0xa624a8: ldur            lr, [fp, #-0x38]
    // 0xa624ac: stp             lr, x16, [SP]
    // 0xa624b0: mov             x1, x0
    // 0xa624b4: ldur            x2, [fp, #-0x20]
    // 0xa624b8: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xa624b8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xa624bc: ldr             x4, [x4, #0x828]
    // 0xa624c0: r0 = CachedNetworkImage()
    //     0xa624c0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa624c4: r0 = Container()
    //     0xa624c4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa624c8: stur            x0, [fp, #-0x20]
    // 0xa624cc: r16 = 46.000000
    //     0xa624cc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52830] 46
    //     0xa624d0: ldr             x16, [x16, #0x830]
    // 0xa624d4: r30 = 64.000000
    //     0xa624d4: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa624d8: ldr             lr, [lr, #0x838]
    // 0xa624dc: stp             lr, x16, [SP, #0x10]
    // 0xa624e0: ldur            x16, [fp, #-0x18]
    // 0xa624e4: ldur            lr, [fp, #-0x40]
    // 0xa624e8: stp             lr, x16, [SP]
    // 0xa624ec: mov             x1, x0
    // 0xa624f0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa624f0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa624f4: ldr             x4, [x4, #0x870]
    // 0xa624f8: r0 = Container()
    //     0xa624f8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa624fc: ldur            x2, [fp, #-8]
    // 0xa62500: LoadField: r0 = r2->field_f
    //     0xa62500: ldur            w0, [x2, #0xf]
    // 0xa62504: DecompressPointer r0
    //     0xa62504: add             x0, x0, HEAP, lsl #32
    // 0xa62508: LoadField: r3 = r0->field_b
    //     0xa62508: ldur            w3, [x0, #0xb]
    // 0xa6250c: DecompressPointer r3
    //     0xa6250c: add             x3, x3, HEAP, lsl #32
    // 0xa62510: cmp             w3, NULL
    // 0xa62514: b.eq            #0xa629b8
    // 0xa62518: LoadField: r0 = r3->field_b
    //     0xa62518: ldur            w0, [x3, #0xb]
    // 0xa6251c: DecompressPointer r0
    //     0xa6251c: add             x0, x0, HEAP, lsl #32
    // 0xa62520: LoadField: r4 = r0->field_73
    //     0xa62520: ldur            w4, [x0, #0x73]
    // 0xa62524: DecompressPointer r4
    //     0xa62524: add             x4, x4, HEAP, lsl #32
    // 0xa62528: cmp             w4, NULL
    // 0xa6252c: b.ne            #0xa6253c
    // 0xa62530: ldr             x5, [fp, #0x10]
    // 0xa62534: r0 = Null
    //     0xa62534: mov             x0, NULL
    // 0xa62538: b               #0xa62580
    // 0xa6253c: ldr             x5, [fp, #0x10]
    // 0xa62540: LoadField: r0 = r4->field_b
    //     0xa62540: ldur            w0, [x4, #0xb]
    // 0xa62544: r6 = LoadInt32Instr(r5)
    //     0xa62544: sbfx            x6, x5, #1, #0x1f
    //     0xa62548: tbz             w5, #0, #0xa62550
    //     0xa6254c: ldur            x6, [x5, #7]
    // 0xa62550: r1 = LoadInt32Instr(r0)
    //     0xa62550: sbfx            x1, x0, #1, #0x1f
    // 0xa62554: mov             x0, x1
    // 0xa62558: mov             x1, x6
    // 0xa6255c: cmp             x1, x0
    // 0xa62560: b.hs            #0xa629bc
    // 0xa62564: LoadField: r0 = r4->field_f
    //     0xa62564: ldur            w0, [x4, #0xf]
    // 0xa62568: DecompressPointer r0
    //     0xa62568: add             x0, x0, HEAP, lsl #32
    // 0xa6256c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa6256c: add             x16, x0, x6, lsl #2
    //     0xa62570: ldur            w1, [x16, #0xf]
    // 0xa62574: DecompressPointer r1
    //     0xa62574: add             x1, x1, HEAP, lsl #32
    // 0xa62578: LoadField: r0 = r1->field_13
    //     0xa62578: ldur            w0, [x1, #0x13]
    // 0xa6257c: DecompressPointer r0
    //     0xa6257c: add             x0, x0, HEAP, lsl #32
    // 0xa62580: cmp             w0, NULL
    // 0xa62584: r16 = true
    //     0xa62584: add             x16, NULL, #0x20  ; true
    // 0xa62588: r17 = false
    //     0xa62588: add             x17, NULL, #0x30  ; false
    // 0xa6258c: csel            x4, x16, x17, ne
    // 0xa62590: stur            x4, [fp, #-0x18]
    // 0xa62594: LoadField: r0 = r3->field_b
    //     0xa62594: ldur            w0, [x3, #0xb]
    // 0xa62598: DecompressPointer r0
    //     0xa62598: add             x0, x0, HEAP, lsl #32
    // 0xa6259c: LoadField: r6 = r0->field_73
    //     0xa6259c: ldur            w6, [x0, #0x73]
    // 0xa625a0: DecompressPointer r6
    //     0xa625a0: add             x6, x6, HEAP, lsl #32
    // 0xa625a4: cmp             w6, NULL
    // 0xa625a8: b.ne            #0xa625b4
    // 0xa625ac: r0 = Null
    //     0xa625ac: mov             x0, NULL
    // 0xa625b0: b               #0xa62618
    // 0xa625b4: LoadField: r0 = r6->field_b
    //     0xa625b4: ldur            w0, [x6, #0xb]
    // 0xa625b8: r7 = LoadInt32Instr(r5)
    //     0xa625b8: sbfx            x7, x5, #1, #0x1f
    //     0xa625bc: tbz             w5, #0, #0xa625c4
    //     0xa625c0: ldur            x7, [x5, #7]
    // 0xa625c4: r1 = LoadInt32Instr(r0)
    //     0xa625c4: sbfx            x1, x0, #1, #0x1f
    // 0xa625c8: mov             x0, x1
    // 0xa625cc: mov             x1, x7
    // 0xa625d0: cmp             x1, x0
    // 0xa625d4: b.hs            #0xa629c0
    // 0xa625d8: LoadField: r0 = r6->field_f
    //     0xa625d8: ldur            w0, [x6, #0xf]
    // 0xa625dc: DecompressPointer r0
    //     0xa625dc: add             x0, x0, HEAP, lsl #32
    // 0xa625e0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa625e0: add             x16, x0, x7, lsl #2
    //     0xa625e4: ldur            w1, [x16, #0xf]
    // 0xa625e8: DecompressPointer r1
    //     0xa625e8: add             x1, x1, HEAP, lsl #32
    // 0xa625ec: LoadField: r0 = r1->field_13
    //     0xa625ec: ldur            w0, [x1, #0x13]
    // 0xa625f0: DecompressPointer r0
    //     0xa625f0: add             x0, x0, HEAP, lsl #32
    // 0xa625f4: cmp             w0, NULL
    // 0xa625f8: b.ne            #0xa62604
    // 0xa625fc: r0 = Null
    //     0xa625fc: mov             x0, NULL
    // 0xa62600: b               #0xa62618
    // 0xa62604: LoadField: r1 = r0->field_7
    //     0xa62604: ldur            w1, [x0, #7]
    // 0xa62608: cbnz            w1, #0xa62614
    // 0xa6260c: r0 = false
    //     0xa6260c: add             x0, NULL, #0x30  ; false
    // 0xa62610: b               #0xa62618
    // 0xa62614: r0 = true
    //     0xa62614: add             x0, NULL, #0x20  ; true
    // 0xa62618: cmp             w0, NULL
    // 0xa6261c: b.eq            #0xa626ac
    // 0xa62620: tbnz            w0, #4, #0xa626ac
    // 0xa62624: LoadField: r0 = r3->field_b
    //     0xa62624: ldur            w0, [x3, #0xb]
    // 0xa62628: DecompressPointer r0
    //     0xa62628: add             x0, x0, HEAP, lsl #32
    // 0xa6262c: LoadField: r3 = r0->field_73
    //     0xa6262c: ldur            w3, [x0, #0x73]
    // 0xa62630: DecompressPointer r3
    //     0xa62630: add             x3, x3, HEAP, lsl #32
    // 0xa62634: cmp             w3, NULL
    // 0xa62638: b.ne            #0xa62644
    // 0xa6263c: r0 = Null
    //     0xa6263c: mov             x0, NULL
    // 0xa62640: b               #0xa6269c
    // 0xa62644: LoadField: r0 = r3->field_b
    //     0xa62644: ldur            w0, [x3, #0xb]
    // 0xa62648: r6 = LoadInt32Instr(r5)
    //     0xa62648: sbfx            x6, x5, #1, #0x1f
    //     0xa6264c: tbz             w5, #0, #0xa62654
    //     0xa62650: ldur            x6, [x5, #7]
    // 0xa62654: r1 = LoadInt32Instr(r0)
    //     0xa62654: sbfx            x1, x0, #1, #0x1f
    // 0xa62658: mov             x0, x1
    // 0xa6265c: mov             x1, x6
    // 0xa62660: cmp             x1, x0
    // 0xa62664: b.hs            #0xa629c4
    // 0xa62668: LoadField: r0 = r3->field_f
    //     0xa62668: ldur            w0, [x3, #0xf]
    // 0xa6266c: DecompressPointer r0
    //     0xa6266c: add             x0, x0, HEAP, lsl #32
    // 0xa62670: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa62670: add             x16, x0, x6, lsl #2
    //     0xa62674: ldur            w1, [x16, #0xf]
    // 0xa62678: DecompressPointer r1
    //     0xa62678: add             x1, x1, HEAP, lsl #32
    // 0xa6267c: LoadField: r0 = r1->field_13
    //     0xa6267c: ldur            w0, [x1, #0x13]
    // 0xa62680: DecompressPointer r0
    //     0xa62680: add             x0, x0, HEAP, lsl #32
    // 0xa62684: cmp             w0, NULL
    // 0xa62688: b.ne            #0xa62694
    // 0xa6268c: r0 = Null
    //     0xa6268c: mov             x0, NULL
    // 0xa62690: b               #0xa6269c
    // 0xa62694: mov             x1, x0
    // 0xa62698: r0 = StringExtension.toTitleCase()
    //     0xa62698: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xa6269c: str             x0, [SP]
    // 0xa626a0: r0 = _interpolateSingle()
    //     0xa626a0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa626a4: mov             x3, x0
    // 0xa626a8: b               #0xa626b0
    // 0xa626ac: r3 = ""
    //     0xa626ac: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa626b0: ldur            x0, [fp, #-8]
    // 0xa626b4: stur            x3, [fp, #-0x28]
    // 0xa626b8: LoadField: r1 = r0->field_f
    //     0xa626b8: ldur            w1, [x0, #0xf]
    // 0xa626bc: DecompressPointer r1
    //     0xa626bc: add             x1, x1, HEAP, lsl #32
    // 0xa626c0: LoadField: r2 = r1->field_27
    //     0xa626c0: ldur            w2, [x1, #0x27]
    // 0xa626c4: DecompressPointer r2
    //     0xa626c4: add             x2, x2, HEAP, lsl #32
    // 0xa626c8: LoadField: r0 = r1->field_b
    //     0xa626c8: ldur            w0, [x1, #0xb]
    // 0xa626cc: DecompressPointer r0
    //     0xa626cc: add             x0, x0, HEAP, lsl #32
    // 0xa626d0: cmp             w0, NULL
    // 0xa626d4: b.eq            #0xa629c8
    // 0xa626d8: LoadField: r1 = r0->field_b
    //     0xa626d8: ldur            w1, [x0, #0xb]
    // 0xa626dc: DecompressPointer r1
    //     0xa626dc: add             x1, x1, HEAP, lsl #32
    // 0xa626e0: LoadField: r4 = r1->field_73
    //     0xa626e0: ldur            w4, [x1, #0x73]
    // 0xa626e4: DecompressPointer r4
    //     0xa626e4: add             x4, x4, HEAP, lsl #32
    // 0xa626e8: cmp             w4, NULL
    // 0xa626ec: b.ne            #0xa626f8
    // 0xa626f0: r0 = Null
    //     0xa626f0: mov             x0, NULL
    // 0xa626f4: b               #0xa62738
    // 0xa626f8: ldr             x0, [fp, #0x10]
    // 0xa626fc: LoadField: r1 = r4->field_b
    //     0xa626fc: ldur            w1, [x4, #0xb]
    // 0xa62700: r5 = LoadInt32Instr(r0)
    //     0xa62700: sbfx            x5, x0, #1, #0x1f
    //     0xa62704: tbz             w0, #0, #0xa6270c
    //     0xa62708: ldur            x5, [x0, #7]
    // 0xa6270c: r0 = LoadInt32Instr(r1)
    //     0xa6270c: sbfx            x0, x1, #1, #0x1f
    // 0xa62710: mov             x1, x5
    // 0xa62714: cmp             x1, x0
    // 0xa62718: b.hs            #0xa629cc
    // 0xa6271c: LoadField: r0 = r4->field_f
    //     0xa6271c: ldur            w0, [x4, #0xf]
    // 0xa62720: DecompressPointer r0
    //     0xa62720: add             x0, x0, HEAP, lsl #32
    // 0xa62724: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa62724: add             x16, x0, x5, lsl #2
    //     0xa62728: ldur            w1, [x16, #0xf]
    // 0xa6272c: DecompressPointer r1
    //     0xa6272c: add             x1, x1, HEAP, lsl #32
    // 0xa62730: LoadField: r0 = r1->field_7
    //     0xa62730: ldur            w0, [x1, #7]
    // 0xa62734: DecompressPointer r0
    //     0xa62734: add             x0, x0, HEAP, lsl #32
    // 0xa62738: cmp             w0, NULL
    // 0xa6273c: b.ne            #0xa62744
    // 0xa62740: r0 = ""
    //     0xa62740: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa62744: mov             x1, x2
    // 0xa62748: mov             x2, x0
    // 0xa6274c: r0 = contains()
    //     0xa6274c: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa62750: tbnz            w0, #4, #0xa62794
    // 0xa62754: ldr             x1, [fp, #0x18]
    // 0xa62758: r0 = of()
    //     0xa62758: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa6275c: LoadField: r1 = r0->field_87
    //     0xa6275c: ldur            w1, [x0, #0x87]
    // 0xa62760: DecompressPointer r1
    //     0xa62760: add             x1, x1, HEAP, lsl #32
    // 0xa62764: LoadField: r0 = r1->field_7
    //     0xa62764: ldur            w0, [x1, #7]
    // 0xa62768: DecompressPointer r0
    //     0xa62768: add             x0, x0, HEAP, lsl #32
    // 0xa6276c: r16 = Instance_Color
    //     0xa6276c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa62770: r30 = 12.000000
    //     0xa62770: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa62774: ldr             lr, [lr, #0x9e8]
    // 0xa62778: stp             lr, x16, [SP]
    // 0xa6277c: mov             x1, x0
    // 0xa62780: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa62780: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa62784: ldr             x4, [x4, #0x9b8]
    // 0xa62788: r0 = copyWith()
    //     0xa62788: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa6278c: mov             x3, x0
    // 0xa62790: b               #0xa627e0
    // 0xa62794: ldr             x1, [fp, #0x18]
    // 0xa62798: r0 = of()
    //     0xa62798: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa6279c: LoadField: r1 = r0->field_87
    //     0xa6279c: ldur            w1, [x0, #0x87]
    // 0xa627a0: DecompressPointer r1
    //     0xa627a0: add             x1, x1, HEAP, lsl #32
    // 0xa627a4: LoadField: r0 = r1->field_2b
    //     0xa627a4: ldur            w0, [x1, #0x2b]
    // 0xa627a8: DecompressPointer r0
    //     0xa627a8: add             x0, x0, HEAP, lsl #32
    // 0xa627ac: stur            x0, [fp, #-8]
    // 0xa627b0: r1 = Instance_Color
    //     0xa627b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa627b4: d0 = 0.700000
    //     0xa627b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa627b8: ldr             d0, [x17, #0xf48]
    // 0xa627bc: r0 = withOpacity()
    //     0xa627bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa627c0: r16 = 12.000000
    //     0xa627c0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa627c4: ldr             x16, [x16, #0x9e8]
    // 0xa627c8: stp             x16, x0, [SP]
    // 0xa627cc: ldur            x1, [fp, #-8]
    // 0xa627d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa627d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa627d4: ldr             x4, [x4, #0x9b8]
    // 0xa627d8: r0 = copyWith()
    //     0xa627d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa627dc: mov             x3, x0
    // 0xa627e0: ldur            x2, [fp, #-0x20]
    // 0xa627e4: ldur            x1, [fp, #-0x18]
    // 0xa627e8: ldur            x0, [fp, #-0x28]
    // 0xa627ec: stur            x3, [fp, #-8]
    // 0xa627f0: r0 = Text()
    //     0xa627f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa627f4: mov             x2, x0
    // 0xa627f8: ldur            x0, [fp, #-0x28]
    // 0xa627fc: stur            x2, [fp, #-0x30]
    // 0xa62800: StoreField: r2->field_b = r0
    //     0xa62800: stur            w0, [x2, #0xb]
    // 0xa62804: ldur            x0, [fp, #-8]
    // 0xa62808: StoreField: r2->field_13 = r0
    //     0xa62808: stur            w0, [x2, #0x13]
    // 0xa6280c: r1 = <FlexParentData>
    //     0xa6280c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa62810: ldr             x1, [x1, #0xe00]
    // 0xa62814: r0 = Expanded()
    //     0xa62814: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa62818: mov             x1, x0
    // 0xa6281c: r0 = 1
    //     0xa6281c: movz            x0, #0x1
    // 0xa62820: stur            x1, [fp, #-8]
    // 0xa62824: StoreField: r1->field_13 = r0
    //     0xa62824: stur            x0, [x1, #0x13]
    // 0xa62828: r0 = Instance_FlexFit
    //     0xa62828: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa6282c: ldr             x0, [x0, #0xe08]
    // 0xa62830: StoreField: r1->field_1b = r0
    //     0xa62830: stur            w0, [x1, #0x1b]
    // 0xa62834: ldur            x0, [fp, #-0x30]
    // 0xa62838: StoreField: r1->field_b = r0
    //     0xa62838: stur            w0, [x1, #0xb]
    // 0xa6283c: r0 = Visibility()
    //     0xa6283c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa62840: mov             x3, x0
    // 0xa62844: ldur            x0, [fp, #-8]
    // 0xa62848: stur            x3, [fp, #-0x28]
    // 0xa6284c: StoreField: r3->field_b = r0
    //     0xa6284c: stur            w0, [x3, #0xb]
    // 0xa62850: r0 = Instance_SizedBox
    //     0xa62850: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa62854: StoreField: r3->field_f = r0
    //     0xa62854: stur            w0, [x3, #0xf]
    // 0xa62858: ldur            x0, [fp, #-0x18]
    // 0xa6285c: StoreField: r3->field_13 = r0
    //     0xa6285c: stur            w0, [x3, #0x13]
    // 0xa62860: r0 = false
    //     0xa62860: add             x0, NULL, #0x30  ; false
    // 0xa62864: ArrayStore: r3[0] = r0  ; List_4
    //     0xa62864: stur            w0, [x3, #0x17]
    // 0xa62868: StoreField: r3->field_1b = r0
    //     0xa62868: stur            w0, [x3, #0x1b]
    // 0xa6286c: StoreField: r3->field_1f = r0
    //     0xa6286c: stur            w0, [x3, #0x1f]
    // 0xa62870: StoreField: r3->field_23 = r0
    //     0xa62870: stur            w0, [x3, #0x23]
    // 0xa62874: StoreField: r3->field_27 = r0
    //     0xa62874: stur            w0, [x3, #0x27]
    // 0xa62878: StoreField: r3->field_2b = r0
    //     0xa62878: stur            w0, [x3, #0x2b]
    // 0xa6287c: r1 = Null
    //     0xa6287c: mov             x1, NULL
    // 0xa62880: r2 = 4
    //     0xa62880: movz            x2, #0x4
    // 0xa62884: r0 = AllocateArray()
    //     0xa62884: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa62888: mov             x2, x0
    // 0xa6288c: ldur            x0, [fp, #-0x20]
    // 0xa62890: stur            x2, [fp, #-8]
    // 0xa62894: StoreField: r2->field_f = r0
    //     0xa62894: stur            w0, [x2, #0xf]
    // 0xa62898: ldur            x0, [fp, #-0x28]
    // 0xa6289c: StoreField: r2->field_13 = r0
    //     0xa6289c: stur            w0, [x2, #0x13]
    // 0xa628a0: r1 = <Widget>
    //     0xa628a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa628a4: r0 = AllocateGrowableArray()
    //     0xa628a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa628a8: mov             x1, x0
    // 0xa628ac: ldur            x0, [fp, #-8]
    // 0xa628b0: stur            x1, [fp, #-0x18]
    // 0xa628b4: StoreField: r1->field_f = r0
    //     0xa628b4: stur            w0, [x1, #0xf]
    // 0xa628b8: r0 = 4
    //     0xa628b8: movz            x0, #0x4
    // 0xa628bc: StoreField: r1->field_b = r0
    //     0xa628bc: stur            w0, [x1, #0xb]
    // 0xa628c0: r0 = Column()
    //     0xa628c0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa628c4: mov             x1, x0
    // 0xa628c8: r0 = Instance_Axis
    //     0xa628c8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa628cc: stur            x1, [fp, #-8]
    // 0xa628d0: StoreField: r1->field_f = r0
    //     0xa628d0: stur            w0, [x1, #0xf]
    // 0xa628d4: r0 = Instance_MainAxisAlignment
    //     0xa628d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa628d8: ldr             x0, [x0, #0xa08]
    // 0xa628dc: StoreField: r1->field_13 = r0
    //     0xa628dc: stur            w0, [x1, #0x13]
    // 0xa628e0: r0 = Instance_MainAxisSize
    //     0xa628e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa628e4: ldr             x0, [x0, #0xa10]
    // 0xa628e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa628e8: stur            w0, [x1, #0x17]
    // 0xa628ec: r0 = Instance_CrossAxisAlignment
    //     0xa628ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa628f0: ldr             x0, [x0, #0xa18]
    // 0xa628f4: StoreField: r1->field_1b = r0
    //     0xa628f4: stur            w0, [x1, #0x1b]
    // 0xa628f8: r0 = Instance_VerticalDirection
    //     0xa628f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa628fc: ldr             x0, [x0, #0xa20]
    // 0xa62900: StoreField: r1->field_23 = r0
    //     0xa62900: stur            w0, [x1, #0x23]
    // 0xa62904: r0 = Instance_Clip
    //     0xa62904: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa62908: ldr             x0, [x0, #0x38]
    // 0xa6290c: StoreField: r1->field_2b = r0
    //     0xa6290c: stur            w0, [x1, #0x2b]
    // 0xa62910: StoreField: r1->field_2f = rZR
    //     0xa62910: stur            xzr, [x1, #0x2f]
    // 0xa62914: ldur            x0, [fp, #-0x18]
    // 0xa62918: StoreField: r1->field_b = r0
    //     0xa62918: stur            w0, [x1, #0xb]
    // 0xa6291c: r0 = Padding()
    //     0xa6291c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa62920: mov             x1, x0
    // 0xa62924: r0 = Instance_EdgeInsets
    //     0xa62924: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xa62928: ldr             x0, [x0, #0x268]
    // 0xa6292c: stur            x1, [fp, #-0x18]
    // 0xa62930: StoreField: r1->field_f = r0
    //     0xa62930: stur            w0, [x1, #0xf]
    // 0xa62934: ldur            x0, [fp, #-8]
    // 0xa62938: StoreField: r1->field_b = r0
    //     0xa62938: stur            w0, [x1, #0xb]
    // 0xa6293c: r0 = InkWell()
    //     0xa6293c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa62940: mov             x3, x0
    // 0xa62944: ldur            x0, [fp, #-0x18]
    // 0xa62948: stur            x3, [fp, #-8]
    // 0xa6294c: StoreField: r3->field_b = r0
    //     0xa6294c: stur            w0, [x3, #0xb]
    // 0xa62950: ldur            x2, [fp, #-0x10]
    // 0xa62954: r1 = Function '<anonymous closure>':.
    //     0xa62954: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c88] AnonymousClosure: (0xa621dc), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xa62958: ldr             x1, [x1, #0xc88]
    // 0xa6295c: r0 = AllocateClosure()
    //     0xa6295c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa62960: mov             x1, x0
    // 0xa62964: ldur            x0, [fp, #-8]
    // 0xa62968: StoreField: r0->field_f = r1
    //     0xa62968: stur            w1, [x0, #0xf]
    // 0xa6296c: r1 = true
    //     0xa6296c: add             x1, NULL, #0x20  ; true
    // 0xa62970: StoreField: r0->field_43 = r1
    //     0xa62970: stur            w1, [x0, #0x43]
    // 0xa62974: r2 = Instance_BoxShape
    //     0xa62974: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa62978: ldr             x2, [x2, #0x80]
    // 0xa6297c: StoreField: r0->field_47 = r2
    //     0xa6297c: stur            w2, [x0, #0x47]
    // 0xa62980: StoreField: r0->field_6f = r1
    //     0xa62980: stur            w1, [x0, #0x6f]
    // 0xa62984: r2 = false
    //     0xa62984: add             x2, NULL, #0x30  ; false
    // 0xa62988: StoreField: r0->field_73 = r2
    //     0xa62988: stur            w2, [x0, #0x73]
    // 0xa6298c: StoreField: r0->field_83 = r1
    //     0xa6298c: stur            w1, [x0, #0x83]
    // 0xa62990: StoreField: r0->field_7b = r2
    //     0xa62990: stur            w2, [x0, #0x7b]
    // 0xa62994: LeaveFrame
    //     0xa62994: mov             SP, fp
    //     0xa62998: ldp             fp, lr, [SP], #0x10
    // 0xa6299c: ret
    //     0xa6299c: ret             
    // 0xa629a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa629a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa629a4: b               #0xa6226c
    // 0xa629a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa629a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa629ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa629ac: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa629b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa629b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa629b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa629b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa629b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa629b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa629bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa629bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa629c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa629c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa629c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa629c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa629c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa629c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa629cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa629cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0xa629d0, size: 0x13c
    // 0xa629d0: EnterFrame
    //     0xa629d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa629d4: mov             fp, SP
    // 0xa629d8: AllocStack(0x18)
    //     0xa629d8: sub             SP, SP, #0x18
    // 0xa629dc: CheckStackOverflow
    //     0xa629dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa629e0: cmp             SP, x16
    //     0xa629e4: b.ls            #0xa62af4
    // 0xa629e8: ldr             x0, [fp, #0x10]
    // 0xa629ec: LoadField: r1 = r0->field_b
    //     0xa629ec: ldur            w1, [x0, #0xb]
    // 0xa629f0: DecompressPointer r1
    //     0xa629f0: add             x1, x1, HEAP, lsl #32
    // 0xa629f4: cmp             w1, NULL
    // 0xa629f8: b.eq            #0xa62a14
    // 0xa629fc: LoadField: r2 = r0->field_f
    //     0xa629fc: ldur            x2, [x0, #0xf]
    // 0xa62a00: r0 = LoadInt32Instr(r1)
    //     0xa62a00: sbfx            x0, x1, #1, #0x1f
    //     0xa62a04: tbz             w1, #0, #0xa62a0c
    //     0xa62a08: ldur            x0, [x1, #7]
    // 0xa62a0c: cmp             x2, x0
    // 0xa62a10: b.le            #0xa62a1c
    // 0xa62a14: r0 = Null
    //     0xa62a14: mov             x0, NULL
    // 0xa62a18: b               #0xa62a50
    // 0xa62a1c: scvtf           d0, x2
    // 0xa62a20: scvtf           d1, x0
    // 0xa62a24: fdiv            d2, d0, d1
    // 0xa62a28: r0 = inline_Allocate_Double()
    //     0xa62a28: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa62a2c: add             x0, x0, #0x10
    //     0xa62a30: cmp             x1, x0
    //     0xa62a34: b.ls            #0xa62afc
    //     0xa62a38: str             x0, [THR, #0x50]  ; THR::top
    //     0xa62a3c: sub             x0, x0, #0xf
    //     0xa62a40: movz            x1, #0xe15c
    //     0xa62a44: movk            x1, #0x3, lsl #16
    //     0xa62a48: stur            x1, [x0, #-1]
    // 0xa62a4c: StoreField: r0->field_7 = d2
    //     0xa62a4c: stur            d2, [x0, #7]
    // 0xa62a50: ldr             x1, [fp, #0x20]
    // 0xa62a54: stur            x0, [fp, #-8]
    // 0xa62a58: r0 = of()
    //     0xa62a58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa62a5c: LoadField: r1 = r0->field_5b
    //     0xa62a5c: ldur            w1, [x0, #0x5b]
    // 0xa62a60: DecompressPointer r1
    //     0xa62a60: add             x1, x1, HEAP, lsl #32
    // 0xa62a64: r0 = LoadClassIdInstr(r1)
    //     0xa62a64: ldur            x0, [x1, #-1]
    //     0xa62a68: ubfx            x0, x0, #0xc, #0x14
    // 0xa62a6c: d0 = 0.300000
    //     0xa62a6c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xa62a70: ldr             d0, [x17, #0x658]
    // 0xa62a74: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa62a74: sub             lr, x0, #0xffa
    //     0xa62a78: ldr             lr, [x21, lr, lsl #3]
    //     0xa62a7c: blr             lr
    // 0xa62a80: stur            x0, [fp, #-0x10]
    // 0xa62a84: r0 = CircularProgressIndicator()
    //     0xa62a84: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xa62a88: mov             x1, x0
    // 0xa62a8c: r0 = Instance__ActivityIndicatorType
    //     0xa62a8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xa62a90: ldr             x0, [x0, #0x1b0]
    // 0xa62a94: stur            x1, [fp, #-0x18]
    // 0xa62a98: StoreField: r1->field_23 = r0
    //     0xa62a98: stur            w0, [x1, #0x23]
    // 0xa62a9c: ldur            x0, [fp, #-8]
    // 0xa62aa0: StoreField: r1->field_b = r0
    //     0xa62aa0: stur            w0, [x1, #0xb]
    // 0xa62aa4: ldur            x0, [fp, #-0x10]
    // 0xa62aa8: StoreField: r1->field_13 = r0
    //     0xa62aa8: stur            w0, [x1, #0x13]
    // 0xa62aac: r0 = SizedBox()
    //     0xa62aac: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa62ab0: mov             x1, x0
    // 0xa62ab4: r0 = 20.000000
    //     0xa62ab4: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa62ab8: ldr             x0, [x0, #0xac8]
    // 0xa62abc: stur            x1, [fp, #-8]
    // 0xa62ac0: StoreField: r1->field_f = r0
    //     0xa62ac0: stur            w0, [x1, #0xf]
    // 0xa62ac4: StoreField: r1->field_13 = r0
    //     0xa62ac4: stur            w0, [x1, #0x13]
    // 0xa62ac8: ldur            x0, [fp, #-0x18]
    // 0xa62acc: StoreField: r1->field_b = r0
    //     0xa62acc: stur            w0, [x1, #0xb]
    // 0xa62ad0: r0 = Center()
    //     0xa62ad0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa62ad4: r1 = Instance_Alignment
    //     0xa62ad4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa62ad8: ldr             x1, [x1, #0xb10]
    // 0xa62adc: StoreField: r0->field_f = r1
    //     0xa62adc: stur            w1, [x0, #0xf]
    // 0xa62ae0: ldur            x1, [fp, #-8]
    // 0xa62ae4: StoreField: r0->field_b = r1
    //     0xa62ae4: stur            w1, [x0, #0xb]
    // 0xa62ae8: LeaveFrame
    //     0xa62ae8: mov             SP, fp
    //     0xa62aec: ldp             fp, lr, [SP], #0x10
    // 0xa62af0: ret
    //     0xa62af0: ret             
    // 0xa62af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa62af4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa62af8: b               #0xa629e8
    // 0xa62afc: SaveReg d2
    //     0xa62afc: str             q2, [SP, #-0x10]!
    // 0xa62b00: r0 = AllocateDouble()
    //     0xa62b00: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa62b04: RestoreReg d2
    //     0xa62b04: ldr             q2, [SP], #0x10
    // 0xa62b08: b               #0xa62a4c
  }
  _ build(/* No info */) {
    // ** addr: 0xbedbb0, size: 0xe60
    // 0xbedbb0: EnterFrame
    //     0xbedbb0: stp             fp, lr, [SP, #-0x10]!
    //     0xbedbb4: mov             fp, SP
    // 0xbedbb8: AllocStack(0x78)
    //     0xbedbb8: sub             SP, SP, #0x78
    // 0xbedbbc: SetupParameters(_SelectSizeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbedbbc: mov             x0, x1
    //     0xbedbc0: stur            x1, [fp, #-8]
    //     0xbedbc4: mov             x1, x2
    //     0xbedbc8: stur            x2, [fp, #-0x10]
    // 0xbedbcc: CheckStackOverflow
    //     0xbedbcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbedbd0: cmp             SP, x16
    //     0xbedbd4: b.ls            #0xbee9d0
    // 0xbedbd8: r1 = 2
    //     0xbedbd8: movz            x1, #0x2
    // 0xbedbdc: r0 = AllocateContext()
    //     0xbedbdc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbedbe0: mov             x2, x0
    // 0xbedbe4: ldur            x0, [fp, #-8]
    // 0xbedbe8: stur            x2, [fp, #-0x18]
    // 0xbedbec: StoreField: r2->field_f = r0
    //     0xbedbec: stur            w0, [x2, #0xf]
    // 0xbedbf0: ldur            x1, [fp, #-0x10]
    // 0xbedbf4: StoreField: r2->field_13 = r1
    //     0xbedbf4: stur            w1, [x2, #0x13]
    // 0xbedbf8: r0 = of()
    //     0xbedbf8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbedbfc: LoadField: r1 = r0->field_87
    //     0xbedbfc: ldur            w1, [x0, #0x87]
    // 0xbedc00: DecompressPointer r1
    //     0xbedc00: add             x1, x1, HEAP, lsl #32
    // 0xbedc04: LoadField: r0 = r1->field_27
    //     0xbedc04: ldur            w0, [x1, #0x27]
    // 0xbedc08: DecompressPointer r0
    //     0xbedc08: add             x0, x0, HEAP, lsl #32
    // 0xbedc0c: r16 = Instance_Color
    //     0xbedc0c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbedc10: r30 = 21.000000
    //     0xbedc10: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbedc14: ldr             lr, [lr, #0x9b0]
    // 0xbedc18: stp             lr, x16, [SP]
    // 0xbedc1c: mov             x1, x0
    // 0xbedc20: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbedc20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbedc24: ldr             x4, [x4, #0x9b8]
    // 0xbedc28: r0 = copyWith()
    //     0xbedc28: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbedc2c: stur            x0, [fp, #-0x10]
    // 0xbedc30: r0 = Text()
    //     0xbedc30: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbedc34: mov             x1, x0
    // 0xbedc38: r0 = "Select Preference"
    //     0xbedc38: add             x0, PP, #0x52, lsl #12  ; [pp+0x52710] "Select Preference"
    //     0xbedc3c: ldr             x0, [x0, #0x710]
    // 0xbedc40: stur            x1, [fp, #-0x20]
    // 0xbedc44: StoreField: r1->field_b = r0
    //     0xbedc44: stur            w0, [x1, #0xb]
    // 0xbedc48: ldur            x0, [fp, #-0x10]
    // 0xbedc4c: StoreField: r1->field_13 = r0
    //     0xbedc4c: stur            w0, [x1, #0x13]
    // 0xbedc50: r0 = SvgPicture()
    //     0xbedc50: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbedc54: mov             x1, x0
    // 0xbedc58: r2 = "assets/images/x.svg"
    //     0xbedc58: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbedc5c: ldr             x2, [x2, #0x5e8]
    // 0xbedc60: stur            x0, [fp, #-0x10]
    // 0xbedc64: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbedc64: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbedc68: r0 = SvgPicture.asset()
    //     0xbedc68: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbedc6c: r0 = InkWell()
    //     0xbedc6c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbedc70: mov             x3, x0
    // 0xbedc74: ldur            x0, [fp, #-0x10]
    // 0xbedc78: stur            x3, [fp, #-0x28]
    // 0xbedc7c: StoreField: r3->field_b = r0
    //     0xbedc7c: stur            w0, [x3, #0xb]
    // 0xbedc80: ldur            x2, [fp, #-0x18]
    // 0xbedc84: r1 = Function '<anonymous closure>':.
    //     0xbedc84: add             x1, PP, #0x53, lsl #12  ; [pp+0x53bd0] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbedc88: ldr             x1, [x1, #0xbd0]
    // 0xbedc8c: r0 = AllocateClosure()
    //     0xbedc8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbedc90: mov             x1, x0
    // 0xbedc94: ldur            x0, [fp, #-0x28]
    // 0xbedc98: StoreField: r0->field_f = r1
    //     0xbedc98: stur            w1, [x0, #0xf]
    // 0xbedc9c: r3 = true
    //     0xbedc9c: add             x3, NULL, #0x20  ; true
    // 0xbedca0: StoreField: r0->field_43 = r3
    //     0xbedca0: stur            w3, [x0, #0x43]
    // 0xbedca4: r4 = Instance_BoxShape
    //     0xbedca4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbedca8: ldr             x4, [x4, #0x80]
    // 0xbedcac: StoreField: r0->field_47 = r4
    //     0xbedcac: stur            w4, [x0, #0x47]
    // 0xbedcb0: StoreField: r0->field_6f = r3
    //     0xbedcb0: stur            w3, [x0, #0x6f]
    // 0xbedcb4: r5 = false
    //     0xbedcb4: add             x5, NULL, #0x30  ; false
    // 0xbedcb8: StoreField: r0->field_73 = r5
    //     0xbedcb8: stur            w5, [x0, #0x73]
    // 0xbedcbc: StoreField: r0->field_83 = r3
    //     0xbedcbc: stur            w3, [x0, #0x83]
    // 0xbedcc0: StoreField: r0->field_7b = r5
    //     0xbedcc0: stur            w5, [x0, #0x7b]
    // 0xbedcc4: r1 = Null
    //     0xbedcc4: mov             x1, NULL
    // 0xbedcc8: r2 = 6
    //     0xbedcc8: movz            x2, #0x6
    // 0xbedccc: r0 = AllocateArray()
    //     0xbedccc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbedcd0: mov             x2, x0
    // 0xbedcd4: ldur            x0, [fp, #-0x20]
    // 0xbedcd8: stur            x2, [fp, #-0x10]
    // 0xbedcdc: StoreField: r2->field_f = r0
    //     0xbedcdc: stur            w0, [x2, #0xf]
    // 0xbedce0: r16 = Instance_Spacer
    //     0xbedce0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbedce4: ldr             x16, [x16, #0xf0]
    // 0xbedce8: StoreField: r2->field_13 = r16
    //     0xbedce8: stur            w16, [x2, #0x13]
    // 0xbedcec: ldur            x0, [fp, #-0x28]
    // 0xbedcf0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbedcf0: stur            w0, [x2, #0x17]
    // 0xbedcf4: r1 = <Widget>
    //     0xbedcf4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbedcf8: r0 = AllocateGrowableArray()
    //     0xbedcf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbedcfc: mov             x1, x0
    // 0xbedd00: ldur            x0, [fp, #-0x10]
    // 0xbedd04: stur            x1, [fp, #-0x20]
    // 0xbedd08: StoreField: r1->field_f = r0
    //     0xbedd08: stur            w0, [x1, #0xf]
    // 0xbedd0c: r2 = 6
    //     0xbedd0c: movz            x2, #0x6
    // 0xbedd10: StoreField: r1->field_b = r2
    //     0xbedd10: stur            w2, [x1, #0xb]
    // 0xbedd14: r0 = Row()
    //     0xbedd14: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbedd18: mov             x1, x0
    // 0xbedd1c: r0 = Instance_Axis
    //     0xbedd1c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbedd20: stur            x1, [fp, #-0x28]
    // 0xbedd24: StoreField: r1->field_f = r0
    //     0xbedd24: stur            w0, [x1, #0xf]
    // 0xbedd28: r0 = Instance_MainAxisAlignment
    //     0xbedd28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbedd2c: ldr             x0, [x0, #0xa08]
    // 0xbedd30: StoreField: r1->field_13 = r0
    //     0xbedd30: stur            w0, [x1, #0x13]
    // 0xbedd34: r2 = Instance_MainAxisSize
    //     0xbedd34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbedd38: ldr             x2, [x2, #0xa10]
    // 0xbedd3c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbedd3c: stur            w2, [x1, #0x17]
    // 0xbedd40: r2 = Instance_CrossAxisAlignment
    //     0xbedd40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbedd44: ldr             x2, [x2, #0xa18]
    // 0xbedd48: StoreField: r1->field_1b = r2
    //     0xbedd48: stur            w2, [x1, #0x1b]
    // 0xbedd4c: r3 = Instance_VerticalDirection
    //     0xbedd4c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbedd50: ldr             x3, [x3, #0xa20]
    // 0xbedd54: StoreField: r1->field_23 = r3
    //     0xbedd54: stur            w3, [x1, #0x23]
    // 0xbedd58: r4 = Instance_Clip
    //     0xbedd58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbedd5c: ldr             x4, [x4, #0x38]
    // 0xbedd60: StoreField: r1->field_2b = r4
    //     0xbedd60: stur            w4, [x1, #0x2b]
    // 0xbedd64: StoreField: r1->field_2f = rZR
    //     0xbedd64: stur            xzr, [x1, #0x2f]
    // 0xbedd68: ldur            x5, [fp, #-0x20]
    // 0xbedd6c: StoreField: r1->field_b = r5
    //     0xbedd6c: stur            w5, [x1, #0xb]
    // 0xbedd70: ldur            x5, [fp, #-8]
    // 0xbedd74: LoadField: r6 = r5->field_b
    //     0xbedd74: ldur            w6, [x5, #0xb]
    // 0xbedd78: DecompressPointer r6
    //     0xbedd78: add             x6, x6, HEAP, lsl #32
    // 0xbedd7c: cmp             w6, NULL
    // 0xbedd80: b.eq            #0xbee9d8
    // 0xbedd84: LoadField: r7 = r6->field_b
    //     0xbedd84: ldur            w7, [x6, #0xb]
    // 0xbedd88: DecompressPointer r7
    //     0xbedd88: add             x7, x7, HEAP, lsl #32
    // 0xbedd8c: LoadField: r6 = r7->field_73
    //     0xbedd8c: ldur            w6, [x7, #0x73]
    // 0xbedd90: DecompressPointer r6
    //     0xbedd90: add             x6, x6, HEAP, lsl #32
    // 0xbedd94: cmp             w6, NULL
    // 0xbedd98: b.ne            #0xbedda4
    // 0xbedd9c: r6 = Null
    //     0xbedd9c: mov             x6, NULL
    // 0xbedda0: b               #0xbeddb8
    // 0xbedda4: LoadField: r7 = r6->field_b
    //     0xbedda4: ldur            w7, [x6, #0xb]
    // 0xbedda8: cbnz            w7, #0xbeddb4
    // 0xbeddac: r6 = false
    //     0xbeddac: add             x6, NULL, #0x30  ; false
    // 0xbeddb0: b               #0xbeddb8
    // 0xbeddb4: r6 = true
    //     0xbeddb4: add             x6, NULL, #0x20  ; true
    // 0xbeddb8: cmp             w6, NULL
    // 0xbeddbc: b.ne            #0xbeddc4
    // 0xbeddc0: r6 = false
    //     0xbeddc0: add             x6, NULL, #0x30  ; false
    // 0xbeddc4: stur            x6, [fp, #-0x10]
    // 0xbeddc8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbeddc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbeddcc: ldr             x0, [x0, #0x1c80]
    //     0xbeddd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbeddd4: cmp             w0, w16
    //     0xbeddd8: b.ne            #0xbedde4
    //     0xbedddc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbedde0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbedde4: r0 = GetNavigation.size()
    //     0xbedde4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbedde8: LoadField: d0 = r0->field_f
    //     0xbedde8: ldur            d0, [x0, #0xf]
    // 0xbeddec: d1 = 0.124000
    //     0xbeddec: add             x17, PP, #0x52, lsl #12  ; [pp+0x52720] IMM: double(0.124) from 0x3fbfbe76c8b43958
    //     0xbeddf0: ldr             d1, [x17, #0x720]
    // 0xbeddf4: fmul            d2, d0, d1
    // 0xbeddf8: stur            d2, [fp, #-0x58]
    // 0xbeddfc: r0 = BoxConstraints()
    //     0xbeddfc: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xbede00: stur            x0, [fp, #-0x30]
    // 0xbede04: StoreField: r0->field_7 = rZR
    //     0xbede04: stur            xzr, [x0, #7]
    // 0xbede08: d0 = inf
    //     0xbede08: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xbede0c: StoreField: r0->field_f = d0
    //     0xbede0c: stur            d0, [x0, #0xf]
    // 0xbede10: ArrayStore: r0[0] = rZR  ; List_8
    //     0xbede10: stur            xzr, [x0, #0x17]
    // 0xbede14: ldur            d0, [fp, #-0x58]
    // 0xbede18: StoreField: r0->field_1f = d0
    //     0xbede18: stur            d0, [x0, #0x1f]
    // 0xbede1c: ldur            x3, [fp, #-8]
    // 0xbede20: LoadField: r1 = r3->field_b
    //     0xbede20: ldur            w1, [x3, #0xb]
    // 0xbede24: DecompressPointer r1
    //     0xbede24: add             x1, x1, HEAP, lsl #32
    // 0xbede28: cmp             w1, NULL
    // 0xbede2c: b.eq            #0xbee9dc
    // 0xbede30: LoadField: r2 = r1->field_b
    //     0xbede30: ldur            w2, [x1, #0xb]
    // 0xbede34: DecompressPointer r2
    //     0xbede34: add             x2, x2, HEAP, lsl #32
    // 0xbede38: LoadField: r1 = r2->field_73
    //     0xbede38: ldur            w1, [x2, #0x73]
    // 0xbede3c: DecompressPointer r1
    //     0xbede3c: add             x1, x1, HEAP, lsl #32
    // 0xbede40: cmp             w1, NULL
    // 0xbede44: b.ne            #0xbede50
    // 0xbede48: r6 = Null
    //     0xbede48: mov             x6, NULL
    // 0xbede4c: b               #0xbede58
    // 0xbede50: LoadField: r2 = r1->field_b
    //     0xbede50: ldur            w2, [x1, #0xb]
    // 0xbede54: mov             x6, x2
    // 0xbede58: ldur            x4, [fp, #-0x28]
    // 0xbede5c: ldur            x5, [fp, #-0x10]
    // 0xbede60: ldur            x2, [fp, #-0x18]
    // 0xbede64: stur            x6, [fp, #-0x20]
    // 0xbede68: r1 = Function '<anonymous closure>':.
    //     0xbede68: add             x1, PP, #0x53, lsl #12  ; [pp+0x53bd8] AnonymousClosure: (0xa62244), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xbede6c: ldr             x1, [x1, #0xbd8]
    // 0xbede70: r0 = AllocateClosure()
    //     0xbede70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbede74: stur            x0, [fp, #-0x38]
    // 0xbede78: r0 = ListView()
    //     0xbede78: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbede7c: stur            x0, [fp, #-0x40]
    // 0xbede80: r16 = true
    //     0xbede80: add             x16, NULL, #0x20  ; true
    // 0xbede84: r30 = Instance_Axis
    //     0xbede84: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbede88: stp             lr, x16, [SP]
    // 0xbede8c: mov             x1, x0
    // 0xbede90: ldur            x2, [fp, #-0x38]
    // 0xbede94: ldur            x3, [fp, #-0x20]
    // 0xbede98: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xbede98: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xbede9c: ldr             x4, [x4, #0x2d0]
    // 0xbedea0: r0 = ListView.builder()
    //     0xbedea0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbedea4: r0 = ConstrainedBox()
    //     0xbedea4: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xbedea8: mov             x1, x0
    // 0xbedeac: ldur            x0, [fp, #-0x30]
    // 0xbedeb0: stur            x1, [fp, #-0x20]
    // 0xbedeb4: StoreField: r1->field_f = r0
    //     0xbedeb4: stur            w0, [x1, #0xf]
    // 0xbedeb8: ldur            x0, [fp, #-0x40]
    // 0xbedebc: StoreField: r1->field_b = r0
    //     0xbedebc: stur            w0, [x1, #0xb]
    // 0xbedec0: r0 = Align()
    //     0xbedec0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbedec4: mov             x1, x0
    // 0xbedec8: r0 = Instance_Alignment
    //     0xbedec8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbedecc: ldr             x0, [x0, #0xfa0]
    // 0xbeded0: stur            x1, [fp, #-0x30]
    // 0xbeded4: StoreField: r1->field_f = r0
    //     0xbeded4: stur            w0, [x1, #0xf]
    // 0xbeded8: ldur            x2, [fp, #-0x20]
    // 0xbededc: StoreField: r1->field_b = r2
    //     0xbededc: stur            w2, [x1, #0xb]
    // 0xbedee0: r0 = Visibility()
    //     0xbedee0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbedee4: mov             x3, x0
    // 0xbedee8: ldur            x0, [fp, #-0x30]
    // 0xbedeec: stur            x3, [fp, #-0x20]
    // 0xbedef0: StoreField: r3->field_b = r0
    //     0xbedef0: stur            w0, [x3, #0xb]
    // 0xbedef4: r0 = Instance_SizedBox
    //     0xbedef4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbedef8: StoreField: r3->field_f = r0
    //     0xbedef8: stur            w0, [x3, #0xf]
    // 0xbedefc: ldur            x0, [fp, #-0x10]
    // 0xbedf00: StoreField: r3->field_13 = r0
    //     0xbedf00: stur            w0, [x3, #0x13]
    // 0xbedf04: r0 = false
    //     0xbedf04: add             x0, NULL, #0x30  ; false
    // 0xbedf08: ArrayStore: r3[0] = r0  ; List_4
    //     0xbedf08: stur            w0, [x3, #0x17]
    // 0xbedf0c: StoreField: r3->field_1b = r0
    //     0xbedf0c: stur            w0, [x3, #0x1b]
    // 0xbedf10: StoreField: r3->field_1f = r0
    //     0xbedf10: stur            w0, [x3, #0x1f]
    // 0xbedf14: StoreField: r3->field_23 = r0
    //     0xbedf14: stur            w0, [x3, #0x23]
    // 0xbedf18: StoreField: r3->field_27 = r0
    //     0xbedf18: stur            w0, [x3, #0x27]
    // 0xbedf1c: StoreField: r3->field_2b = r0
    //     0xbedf1c: stur            w0, [x3, #0x2b]
    // 0xbedf20: r1 = Null
    //     0xbedf20: mov             x1, NULL
    // 0xbedf24: r2 = 6
    //     0xbedf24: movz            x2, #0x6
    // 0xbedf28: r0 = AllocateArray()
    //     0xbedf28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbedf2c: mov             x2, x0
    // 0xbedf30: ldur            x0, [fp, #-0x28]
    // 0xbedf34: stur            x2, [fp, #-0x10]
    // 0xbedf38: StoreField: r2->field_f = r0
    //     0xbedf38: stur            w0, [x2, #0xf]
    // 0xbedf3c: r16 = Instance_Divider
    //     0xbedf3c: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0xbedf40: ldr             x16, [x16, #0x2e0]
    // 0xbedf44: StoreField: r2->field_13 = r16
    //     0xbedf44: stur            w16, [x2, #0x13]
    // 0xbedf48: ldur            x0, [fp, #-0x20]
    // 0xbedf4c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbedf4c: stur            w0, [x2, #0x17]
    // 0xbedf50: r1 = <Widget>
    //     0xbedf50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbedf54: r0 = AllocateGrowableArray()
    //     0xbedf54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbedf58: mov             x1, x0
    // 0xbedf5c: ldur            x0, [fp, #-0x10]
    // 0xbedf60: stur            x1, [fp, #-0x20]
    // 0xbedf64: StoreField: r1->field_f = r0
    //     0xbedf64: stur            w0, [x1, #0xf]
    // 0xbedf68: r0 = 6
    //     0xbedf68: movz            x0, #0x6
    // 0xbedf6c: StoreField: r1->field_b = r0
    //     0xbedf6c: stur            w0, [x1, #0xb]
    // 0xbedf70: ldur            x2, [fp, #-8]
    // 0xbedf74: LoadField: r0 = r2->field_b
    //     0xbedf74: ldur            w0, [x2, #0xb]
    // 0xbedf78: DecompressPointer r0
    //     0xbedf78: add             x0, x0, HEAP, lsl #32
    // 0xbedf7c: cmp             w0, NULL
    // 0xbedf80: b.eq            #0xbee9e0
    // 0xbedf84: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbedf84: ldur            w3, [x0, #0x17]
    // 0xbedf88: DecompressPointer r3
    //     0xbedf88: add             x3, x3, HEAP, lsl #32
    // 0xbedf8c: r0 = LoadClassIdInstr(r3)
    //     0xbedf8c: ldur            x0, [x3, #-1]
    //     0xbedf90: ubfx            x0, x0, #0xc, #0x14
    // 0xbedf94: r16 = "size"
    //     0xbedf94: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbedf98: ldr             x16, [x16, #0x9c0]
    // 0xbedf9c: stp             x16, x3, [SP]
    // 0xbedfa0: mov             lr, x0
    // 0xbedfa4: ldr             lr, [x21, lr, lsl #3]
    // 0xbedfa8: blr             lr
    // 0xbedfac: tbnz            w0, #4, #0xbee0e4
    // 0xbedfb0: ldur            x2, [fp, #-0x18]
    // 0xbedfb4: ldur            x0, [fp, #-0x20]
    // 0xbedfb8: LoadField: r1 = r2->field_13
    //     0xbedfb8: ldur            w1, [x2, #0x13]
    // 0xbedfbc: DecompressPointer r1
    //     0xbedfbc: add             x1, x1, HEAP, lsl #32
    // 0xbedfc0: r0 = of()
    //     0xbedfc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbedfc4: LoadField: r1 = r0->field_87
    //     0xbedfc4: ldur            w1, [x0, #0x87]
    // 0xbedfc8: DecompressPointer r1
    //     0xbedfc8: add             x1, x1, HEAP, lsl #32
    // 0xbedfcc: LoadField: r0 = r1->field_7
    //     0xbedfcc: ldur            w0, [x1, #7]
    // 0xbedfd0: DecompressPointer r0
    //     0xbedfd0: add             x0, x0, HEAP, lsl #32
    // 0xbedfd4: stur            x0, [fp, #-0x10]
    // 0xbedfd8: r1 = Instance_Color
    //     0xbedfd8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbedfdc: d0 = 0.700000
    //     0xbedfdc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbedfe0: ldr             d0, [x17, #0xf48]
    // 0xbedfe4: r0 = withOpacity()
    //     0xbedfe4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbedfe8: r16 = 16.000000
    //     0xbedfe8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbedfec: ldr             x16, [x16, #0x188]
    // 0xbedff0: stp             x16, x0, [SP]
    // 0xbedff4: ldur            x1, [fp, #-0x10]
    // 0xbedff8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbedff8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbedffc: ldr             x4, [x4, #0x9b8]
    // 0xbee000: r0 = copyWith()
    //     0xbee000: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbee004: stur            x0, [fp, #-0x10]
    // 0xbee008: r0 = Text()
    //     0xbee008: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbee00c: mov             x1, x0
    // 0xbee010: r0 = "Size"
    //     0xbee010: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xbee014: ldr             x0, [x0, #0x730]
    // 0xbee018: stur            x1, [fp, #-0x28]
    // 0xbee01c: StoreField: r1->field_b = r0
    //     0xbee01c: stur            w0, [x1, #0xb]
    // 0xbee020: ldur            x0, [fp, #-0x10]
    // 0xbee024: StoreField: r1->field_13 = r0
    //     0xbee024: stur            w0, [x1, #0x13]
    // 0xbee028: r0 = Align()
    //     0xbee028: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbee02c: mov             x1, x0
    // 0xbee030: r0 = Instance_Alignment
    //     0xbee030: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbee034: ldr             x0, [x0, #0xfa0]
    // 0xbee038: stur            x1, [fp, #-0x10]
    // 0xbee03c: StoreField: r1->field_f = r0
    //     0xbee03c: stur            w0, [x1, #0xf]
    // 0xbee040: ldur            x0, [fp, #-0x28]
    // 0xbee044: StoreField: r1->field_b = r0
    //     0xbee044: stur            w0, [x1, #0xb]
    // 0xbee048: r0 = Padding()
    //     0xbee048: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbee04c: mov             x2, x0
    // 0xbee050: r0 = Instance_EdgeInsets
    //     0xbee050: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbee054: ldr             x0, [x0, #0x668]
    // 0xbee058: stur            x2, [fp, #-0x28]
    // 0xbee05c: StoreField: r2->field_f = r0
    //     0xbee05c: stur            w0, [x2, #0xf]
    // 0xbee060: ldur            x1, [fp, #-0x10]
    // 0xbee064: StoreField: r2->field_b = r1
    //     0xbee064: stur            w1, [x2, #0xb]
    // 0xbee068: ldur            x3, [fp, #-0x20]
    // 0xbee06c: LoadField: r1 = r3->field_b
    //     0xbee06c: ldur            w1, [x3, #0xb]
    // 0xbee070: LoadField: r4 = r3->field_f
    //     0xbee070: ldur            w4, [x3, #0xf]
    // 0xbee074: DecompressPointer r4
    //     0xbee074: add             x4, x4, HEAP, lsl #32
    // 0xbee078: LoadField: r5 = r4->field_b
    //     0xbee078: ldur            w5, [x4, #0xb]
    // 0xbee07c: r4 = LoadInt32Instr(r1)
    //     0xbee07c: sbfx            x4, x1, #1, #0x1f
    // 0xbee080: stur            x4, [fp, #-0x48]
    // 0xbee084: r1 = LoadInt32Instr(r5)
    //     0xbee084: sbfx            x1, x5, #1, #0x1f
    // 0xbee088: cmp             x4, x1
    // 0xbee08c: b.ne            #0xbee098
    // 0xbee090: mov             x1, x3
    // 0xbee094: r0 = _growToNextCapacity()
    //     0xbee094: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbee098: ldur            x2, [fp, #-0x20]
    // 0xbee09c: ldur            x3, [fp, #-0x48]
    // 0xbee0a0: add             x0, x3, #1
    // 0xbee0a4: lsl             x1, x0, #1
    // 0xbee0a8: StoreField: r2->field_b = r1
    //     0xbee0a8: stur            w1, [x2, #0xb]
    // 0xbee0ac: LoadField: r1 = r2->field_f
    //     0xbee0ac: ldur            w1, [x2, #0xf]
    // 0xbee0b0: DecompressPointer r1
    //     0xbee0b0: add             x1, x1, HEAP, lsl #32
    // 0xbee0b4: ldur            x0, [fp, #-0x28]
    // 0xbee0b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbee0b8: add             x25, x1, x3, lsl #2
    //     0xbee0bc: add             x25, x25, #0xf
    //     0xbee0c0: str             w0, [x25]
    //     0xbee0c4: tbz             w0, #0, #0xbee0e0
    //     0xbee0c8: ldurb           w16, [x1, #-1]
    //     0xbee0cc: ldurb           w17, [x0, #-1]
    //     0xbee0d0: and             x16, x17, x16, lsr #2
    //     0xbee0d4: tst             x16, HEAP, lsr #32
    //     0xbee0d8: b.eq            #0xbee0e0
    //     0xbee0dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbee0e0: b               #0xbee21c
    // 0xbee0e4: ldur            x3, [fp, #-0x18]
    // 0xbee0e8: ldur            x2, [fp, #-0x20]
    // 0xbee0ec: r0 = Instance_Alignment
    //     0xbee0ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbee0f0: ldr             x0, [x0, #0xfa0]
    // 0xbee0f4: LoadField: r1 = r3->field_13
    //     0xbee0f4: ldur            w1, [x3, #0x13]
    // 0xbee0f8: DecompressPointer r1
    //     0xbee0f8: add             x1, x1, HEAP, lsl #32
    // 0xbee0fc: r0 = of()
    //     0xbee0fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbee100: LoadField: r1 = r0->field_87
    //     0xbee100: ldur            w1, [x0, #0x87]
    // 0xbee104: DecompressPointer r1
    //     0xbee104: add             x1, x1, HEAP, lsl #32
    // 0xbee108: LoadField: r0 = r1->field_7
    //     0xbee108: ldur            w0, [x1, #7]
    // 0xbee10c: DecompressPointer r0
    //     0xbee10c: add             x0, x0, HEAP, lsl #32
    // 0xbee110: stur            x0, [fp, #-0x10]
    // 0xbee114: r1 = Instance_Color
    //     0xbee114: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbee118: d0 = 0.700000
    //     0xbee118: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbee11c: ldr             d0, [x17, #0xf48]
    // 0xbee120: r0 = withOpacity()
    //     0xbee120: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbee124: r16 = 16.000000
    //     0xbee124: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbee128: ldr             x16, [x16, #0x188]
    // 0xbee12c: stp             x16, x0, [SP]
    // 0xbee130: ldur            x1, [fp, #-0x10]
    // 0xbee134: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbee134: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbee138: ldr             x4, [x4, #0x9b8]
    // 0xbee13c: r0 = copyWith()
    //     0xbee13c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbee140: stur            x0, [fp, #-0x10]
    // 0xbee144: r0 = Text()
    //     0xbee144: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbee148: mov             x1, x0
    // 0xbee14c: r0 = "Variant"
    //     0xbee14c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52738] "Variant"
    //     0xbee150: ldr             x0, [x0, #0x738]
    // 0xbee154: stur            x1, [fp, #-0x28]
    // 0xbee158: StoreField: r1->field_b = r0
    //     0xbee158: stur            w0, [x1, #0xb]
    // 0xbee15c: ldur            x0, [fp, #-0x10]
    // 0xbee160: StoreField: r1->field_13 = r0
    //     0xbee160: stur            w0, [x1, #0x13]
    // 0xbee164: r0 = Align()
    //     0xbee164: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbee168: mov             x1, x0
    // 0xbee16c: r0 = Instance_Alignment
    //     0xbee16c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbee170: ldr             x0, [x0, #0xfa0]
    // 0xbee174: stur            x1, [fp, #-0x10]
    // 0xbee178: StoreField: r1->field_f = r0
    //     0xbee178: stur            w0, [x1, #0xf]
    // 0xbee17c: ldur            x0, [fp, #-0x28]
    // 0xbee180: StoreField: r1->field_b = r0
    //     0xbee180: stur            w0, [x1, #0xb]
    // 0xbee184: r0 = Padding()
    //     0xbee184: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbee188: mov             x2, x0
    // 0xbee18c: r0 = Instance_EdgeInsets
    //     0xbee18c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbee190: ldr             x0, [x0, #0x668]
    // 0xbee194: stur            x2, [fp, #-0x28]
    // 0xbee198: StoreField: r2->field_f = r0
    //     0xbee198: stur            w0, [x2, #0xf]
    // 0xbee19c: ldur            x1, [fp, #-0x10]
    // 0xbee1a0: StoreField: r2->field_b = r1
    //     0xbee1a0: stur            w1, [x2, #0xb]
    // 0xbee1a4: ldur            x3, [fp, #-0x20]
    // 0xbee1a8: LoadField: r1 = r3->field_b
    //     0xbee1a8: ldur            w1, [x3, #0xb]
    // 0xbee1ac: LoadField: r4 = r3->field_f
    //     0xbee1ac: ldur            w4, [x3, #0xf]
    // 0xbee1b0: DecompressPointer r4
    //     0xbee1b0: add             x4, x4, HEAP, lsl #32
    // 0xbee1b4: LoadField: r5 = r4->field_b
    //     0xbee1b4: ldur            w5, [x4, #0xb]
    // 0xbee1b8: r4 = LoadInt32Instr(r1)
    //     0xbee1b8: sbfx            x4, x1, #1, #0x1f
    // 0xbee1bc: stur            x4, [fp, #-0x48]
    // 0xbee1c0: r1 = LoadInt32Instr(r5)
    //     0xbee1c0: sbfx            x1, x5, #1, #0x1f
    // 0xbee1c4: cmp             x4, x1
    // 0xbee1c8: b.ne            #0xbee1d4
    // 0xbee1cc: mov             x1, x3
    // 0xbee1d0: r0 = _growToNextCapacity()
    //     0xbee1d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbee1d4: ldur            x2, [fp, #-0x20]
    // 0xbee1d8: ldur            x3, [fp, #-0x48]
    // 0xbee1dc: add             x0, x3, #1
    // 0xbee1e0: lsl             x1, x0, #1
    // 0xbee1e4: StoreField: r2->field_b = r1
    //     0xbee1e4: stur            w1, [x2, #0xb]
    // 0xbee1e8: LoadField: r1 = r2->field_f
    //     0xbee1e8: ldur            w1, [x2, #0xf]
    // 0xbee1ec: DecompressPointer r1
    //     0xbee1ec: add             x1, x1, HEAP, lsl #32
    // 0xbee1f0: ldur            x0, [fp, #-0x28]
    // 0xbee1f4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbee1f4: add             x25, x1, x3, lsl #2
    //     0xbee1f8: add             x25, x25, #0xf
    //     0xbee1fc: str             w0, [x25]
    //     0xbee200: tbz             w0, #0, #0xbee21c
    //     0xbee204: ldurb           w16, [x1, #-1]
    //     0xbee208: ldurb           w17, [x0, #-1]
    //     0xbee20c: and             x16, x17, x16, lsr #2
    //     0xbee210: tst             x16, HEAP, lsr #32
    //     0xbee214: b.eq            #0xbee21c
    //     0xbee218: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbee21c: ldur            x0, [fp, #-8]
    // 0xbee220: LoadField: r1 = r0->field_b
    //     0xbee220: ldur            w1, [x0, #0xb]
    // 0xbee224: DecompressPointer r1
    //     0xbee224: add             x1, x1, HEAP, lsl #32
    // 0xbee228: cmp             w1, NULL
    // 0xbee22c: b.eq            #0xbee9e4
    // 0xbee230: LoadField: r3 = r1->field_b
    //     0xbee230: ldur            w3, [x1, #0xb]
    // 0xbee234: DecompressPointer r3
    //     0xbee234: add             x3, x3, HEAP, lsl #32
    // 0xbee238: LoadField: r1 = r3->field_73
    //     0xbee238: ldur            w1, [x3, #0x73]
    // 0xbee23c: DecompressPointer r1
    //     0xbee23c: add             x1, x1, HEAP, lsl #32
    // 0xbee240: cmp             w1, NULL
    // 0xbee244: b.ne            #0xbee250
    // 0xbee248: r1 = Null
    //     0xbee248: mov             x1, NULL
    // 0xbee24c: b               #0xbee264
    // 0xbee250: LoadField: r3 = r1->field_b
    //     0xbee250: ldur            w3, [x1, #0xb]
    // 0xbee254: cbz             w3, #0xbee260
    // 0xbee258: r1 = false
    //     0xbee258: add             x1, NULL, #0x30  ; false
    // 0xbee25c: b               #0xbee264
    // 0xbee260: r1 = true
    //     0xbee260: add             x1, NULL, #0x20  ; true
    // 0xbee264: cmp             w1, NULL
    // 0xbee268: b.eq            #0xbee270
    // 0xbee26c: tbnz            w1, #4, #0xbee4a0
    // 0xbee270: r1 = Instance_Color
    //     0xbee270: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbee274: d0 = 0.100000
    //     0xbee274: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbee278: r0 = withOpacity()
    //     0xbee278: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbee27c: mov             x2, x0
    // 0xbee280: r1 = Null
    //     0xbee280: mov             x1, NULL
    // 0xbee284: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbee284: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbee288: r0 = Border.all()
    //     0xbee288: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbee28c: stur            x0, [fp, #-0x10]
    // 0xbee290: r0 = BoxDecoration()
    //     0xbee290: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbee294: mov             x1, x0
    // 0xbee298: r0 = Instance_Color
    //     0xbee298: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbee29c: stur            x1, [fp, #-0x28]
    // 0xbee2a0: StoreField: r1->field_7 = r0
    //     0xbee2a0: stur            w0, [x1, #7]
    // 0xbee2a4: ldur            x0, [fp, #-0x10]
    // 0xbee2a8: StoreField: r1->field_f = r0
    //     0xbee2a8: stur            w0, [x1, #0xf]
    // 0xbee2ac: r2 = Instance_BoxShape
    //     0xbee2ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbee2b0: ldr             x2, [x2, #0x80]
    // 0xbee2b4: StoreField: r1->field_23 = r2
    //     0xbee2b4: stur            w2, [x1, #0x23]
    // 0xbee2b8: r0 = SvgPicture()
    //     0xbee2b8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbee2bc: mov             x1, x0
    // 0xbee2c0: r2 = "assets/images/side.svg"
    //     0xbee2c0: add             x2, PP, #0x53, lsl #12  ; [pp+0x53be0] "assets/images/side.svg"
    //     0xbee2c4: ldr             x2, [x2, #0xbe0]
    // 0xbee2c8: stur            x0, [fp, #-0x10]
    // 0xbee2cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbee2cc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbee2d0: r0 = SvgPicture.asset()
    //     0xbee2d0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbee2d4: ldur            x0, [fp, #-8]
    // 0xbee2d8: LoadField: r5 = r0->field_13
    //     0xbee2d8: ldur            w5, [x0, #0x13]
    // 0xbee2dc: DecompressPointer r5
    //     0xbee2dc: add             x5, x5, HEAP, lsl #32
    // 0xbee2e0: r16 = Sentinel
    //     0xbee2e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbee2e4: cmp             w5, w16
    // 0xbee2e8: b.eq            #0xbee9e8
    // 0xbee2ec: stur            x5, [fp, #-0x38]
    // 0xbee2f0: LoadField: r1 = r0->field_b
    //     0xbee2f0: ldur            w1, [x0, #0xb]
    // 0xbee2f4: DecompressPointer r1
    //     0xbee2f4: add             x1, x1, HEAP, lsl #32
    // 0xbee2f8: cmp             w1, NULL
    // 0xbee2fc: b.eq            #0xbee9f4
    // 0xbee300: LoadField: r2 = r1->field_b
    //     0xbee300: ldur            w2, [x1, #0xb]
    // 0xbee304: DecompressPointer r2
    //     0xbee304: add             x2, x2, HEAP, lsl #32
    // 0xbee308: LoadField: r3 = r2->field_6f
    //     0xbee308: ldur            w3, [x2, #0x6f]
    // 0xbee30c: DecompressPointer r3
    //     0xbee30c: add             x3, x3, HEAP, lsl #32
    // 0xbee310: stur            x3, [fp, #-0x30]
    // 0xbee314: cmp             w3, NULL
    // 0xbee318: b.ne            #0xbee324
    // 0xbee31c: r3 = Null
    //     0xbee31c: mov             x3, NULL
    // 0xbee320: b               #0xbee360
    // 0xbee324: ldur            x2, [fp, #-0x18]
    // 0xbee328: r1 = Function '<anonymous closure>':.
    //     0xbee328: add             x1, PP, #0x53, lsl #12  ; [pp+0x53be8] AnonymousClosure: (0xbf0138), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbee32c: ldr             x1, [x1, #0xbe8]
    // 0xbee330: r0 = AllocateClosure()
    //     0xbee330: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbee334: r16 = <DropdownMenuItem<AllSkuDatum>>
    //     0xbee334: add             x16, PP, #0x52, lsl #12  ; [pp+0x52748] TypeArguments: <DropdownMenuItem<AllSkuDatum>>
    //     0xbee338: ldr             x16, [x16, #0x748]
    // 0xbee33c: ldur            lr, [fp, #-0x30]
    // 0xbee340: stp             lr, x16, [SP, #8]
    // 0xbee344: str             x0, [SP]
    // 0xbee348: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbee348: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbee34c: r0 = map()
    //     0xbee34c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xbee350: mov             x1, x0
    // 0xbee354: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbee354: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbee358: r0 = toList()
    //     0xbee358: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xbee35c: mov             x3, x0
    // 0xbee360: ldur            x0, [fp, #-0x20]
    // 0xbee364: ldur            x2, [fp, #-0x18]
    // 0xbee368: stur            x3, [fp, #-0x30]
    // 0xbee36c: r1 = Function '<anonymous closure>':.
    //     0xbee36c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53bf0] AnonymousClosure: (0xbf0244), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xbee370: ldr             x1, [x1, #0xbf0]
    // 0xbee374: r0 = AllocateClosure()
    //     0xbee374: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbee378: r1 = <AllSkuDatum>
    //     0xbee378: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xbee37c: ldr             x1, [x1, #0xea8]
    // 0xbee380: stur            x0, [fp, #-0x40]
    // 0xbee384: r0 = DropdownButton()
    //     0xbee384: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xbee388: stur            x0, [fp, #-0x50]
    // 0xbee38c: r16 = Instance_Alignment
    //     0xbee38c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbee390: ldr             x16, [x16, #0xb10]
    // 0xbee394: ldur            lr, [fp, #-0x10]
    // 0xbee398: stp             lr, x16, [SP]
    // 0xbee39c: mov             x1, x0
    // 0xbee3a0: ldur            x2, [fp, #-0x30]
    // 0xbee3a4: ldur            x3, [fp, #-0x40]
    // 0xbee3a8: ldur            x5, [fp, #-0x38]
    // 0xbee3ac: r4 = const [0, 0x6, 0x2, 0x4, alignment, 0x4, icon, 0x5, null]
    //     0xbee3ac: add             x4, PP, #0x53, lsl #12  ; [pp+0x53bf8] List(9) [0, 0x6, 0x2, 0x4, "alignment", 0x4, "icon", 0x5, Null]
    //     0xbee3b0: ldr             x4, [x4, #0xbf8]
    // 0xbee3b4: r0 = DropdownButton()
    //     0xbee3b4: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xbee3b8: r0 = DropdownButtonHideUnderline()
    //     0xbee3b8: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xbee3bc: mov             x1, x0
    // 0xbee3c0: ldur            x0, [fp, #-0x50]
    // 0xbee3c4: stur            x1, [fp, #-0x10]
    // 0xbee3c8: StoreField: r1->field_b = r0
    //     0xbee3c8: stur            w0, [x1, #0xb]
    // 0xbee3cc: r0 = Container()
    //     0xbee3cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbee3d0: stur            x0, [fp, #-0x30]
    // 0xbee3d4: r16 = inf
    //     0xbee3d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbee3d8: ldr             x16, [x16, #0x9f8]
    // 0xbee3dc: r30 = Instance_AlignmentDirectional
    //     0xbee3dc: add             lr, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xbee3e0: ldr             lr, [lr, #0x3a8]
    // 0xbee3e4: stp             lr, x16, [SP, #0x10]
    // 0xbee3e8: ldur            x16, [fp, #-0x28]
    // 0xbee3ec: ldur            lr, [fp, #-0x10]
    // 0xbee3f0: stp             lr, x16, [SP]
    // 0xbee3f4: mov             x1, x0
    // 0xbee3f8: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, child, 0x4, decoration, 0x3, width, 0x1, null]
    //     0xbee3f8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52758] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "child", 0x4, "decoration", 0x3, "width", 0x1, Null]
    //     0xbee3fc: ldr             x4, [x4, #0x758]
    // 0xbee400: r0 = Container()
    //     0xbee400: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbee404: r0 = Padding()
    //     0xbee404: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbee408: r3 = Instance_EdgeInsets
    //     0xbee408: add             x3, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbee40c: ldr             x3, [x3, #0x668]
    // 0xbee410: stur            x0, [fp, #-0x10]
    // 0xbee414: StoreField: r0->field_f = r3
    //     0xbee414: stur            w3, [x0, #0xf]
    // 0xbee418: ldur            x1, [fp, #-0x30]
    // 0xbee41c: StoreField: r0->field_b = r1
    //     0xbee41c: stur            w1, [x0, #0xb]
    // 0xbee420: ldur            x2, [fp, #-0x20]
    // 0xbee424: LoadField: r1 = r2->field_b
    //     0xbee424: ldur            w1, [x2, #0xb]
    // 0xbee428: LoadField: r3 = r2->field_f
    //     0xbee428: ldur            w3, [x2, #0xf]
    // 0xbee42c: DecompressPointer r3
    //     0xbee42c: add             x3, x3, HEAP, lsl #32
    // 0xbee430: LoadField: r4 = r3->field_b
    //     0xbee430: ldur            w4, [x3, #0xb]
    // 0xbee434: r3 = LoadInt32Instr(r1)
    //     0xbee434: sbfx            x3, x1, #1, #0x1f
    // 0xbee438: stur            x3, [fp, #-0x48]
    // 0xbee43c: r1 = LoadInt32Instr(r4)
    //     0xbee43c: sbfx            x1, x4, #1, #0x1f
    // 0xbee440: cmp             x3, x1
    // 0xbee444: b.ne            #0xbee450
    // 0xbee448: mov             x1, x2
    // 0xbee44c: r0 = _growToNextCapacity()
    //     0xbee44c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbee450: ldur            x4, [fp, #-0x20]
    // 0xbee454: ldur            x2, [fp, #-0x48]
    // 0xbee458: add             x0, x2, #1
    // 0xbee45c: lsl             x1, x0, #1
    // 0xbee460: StoreField: r4->field_b = r1
    //     0xbee460: stur            w1, [x4, #0xb]
    // 0xbee464: LoadField: r1 = r4->field_f
    //     0xbee464: ldur            w1, [x4, #0xf]
    // 0xbee468: DecompressPointer r1
    //     0xbee468: add             x1, x1, HEAP, lsl #32
    // 0xbee46c: ldur            x0, [fp, #-0x10]
    // 0xbee470: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbee470: add             x25, x1, x2, lsl #2
    //     0xbee474: add             x25, x25, #0xf
    //     0xbee478: str             w0, [x25]
    //     0xbee47c: tbz             w0, #0, #0xbee498
    //     0xbee480: ldurb           w16, [x1, #-1]
    //     0xbee484: ldurb           w17, [x0, #-1]
    //     0xbee488: and             x16, x17, x16, lsr #2
    //     0xbee48c: tst             x16, HEAP, lsr #32
    //     0xbee490: b.eq            #0xbee498
    //     0xbee494: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbee498: mov             x2, x4
    // 0xbee49c: b               #0xbee734
    // 0xbee4a0: mov             x5, x0
    // 0xbee4a4: mov             x4, x2
    // 0xbee4a8: r3 = Instance_EdgeInsets
    //     0xbee4a8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbee4ac: ldr             x3, [x3, #0x668]
    // 0xbee4b0: r0 = Instance_Color
    //     0xbee4b0: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbee4b4: r2 = Instance_BoxShape
    //     0xbee4b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbee4b8: ldr             x2, [x2, #0x80]
    // 0xbee4bc: r1 = Instance_Color
    //     0xbee4bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbee4c0: d0 = 0.100000
    //     0xbee4c0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbee4c4: r0 = withOpacity()
    //     0xbee4c4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbee4c8: mov             x2, x0
    // 0xbee4cc: r1 = Null
    //     0xbee4cc: mov             x1, NULL
    // 0xbee4d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbee4d0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbee4d4: r0 = Border.all()
    //     0xbee4d4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbee4d8: stur            x0, [fp, #-0x10]
    // 0xbee4dc: r0 = BoxDecoration()
    //     0xbee4dc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbee4e0: mov             x1, x0
    // 0xbee4e4: r0 = Instance_Color
    //     0xbee4e4: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbee4e8: stur            x1, [fp, #-0x28]
    // 0xbee4ec: StoreField: r1->field_7 = r0
    //     0xbee4ec: stur            w0, [x1, #7]
    // 0xbee4f0: ldur            x0, [fp, #-0x10]
    // 0xbee4f4: StoreField: r1->field_f = r0
    //     0xbee4f4: stur            w0, [x1, #0xf]
    // 0xbee4f8: r0 = Instance_BoxShape
    //     0xbee4f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbee4fc: ldr             x0, [x0, #0x80]
    // 0xbee500: StoreField: r1->field_23 = r0
    //     0xbee500: stur            w0, [x1, #0x23]
    // 0xbee504: ldur            x0, [fp, #-8]
    // 0xbee508: LoadField: r5 = r0->field_13
    //     0xbee508: ldur            w5, [x0, #0x13]
    // 0xbee50c: DecompressPointer r5
    //     0xbee50c: add             x5, x5, HEAP, lsl #32
    // 0xbee510: r16 = Sentinel
    //     0xbee510: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbee514: cmp             w5, w16
    // 0xbee518: b.eq            #0xbee9f8
    // 0xbee51c: stur            x5, [fp, #-0x10]
    // 0xbee520: r0 = SvgPicture()
    //     0xbee520: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbee524: mov             x1, x0
    // 0xbee528: r2 = "assets/images/side.svg"
    //     0xbee528: add             x2, PP, #0x53, lsl #12  ; [pp+0x53be0] "assets/images/side.svg"
    //     0xbee52c: ldr             x2, [x2, #0xbe0]
    // 0xbee530: stur            x0, [fp, #-0x30]
    // 0xbee534: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbee534: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbee538: r0 = SvgPicture.asset()
    //     0xbee538: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbee53c: ldur            x3, [fp, #-8]
    // 0xbee540: LoadField: r0 = r3->field_b
    //     0xbee540: ldur            w0, [x3, #0xb]
    // 0xbee544: DecompressPointer r0
    //     0xbee544: add             x0, x0, HEAP, lsl #32
    // 0xbee548: cmp             w0, NULL
    // 0xbee54c: b.eq            #0xbeea04
    // 0xbee550: LoadField: r1 = r0->field_b
    //     0xbee550: ldur            w1, [x0, #0xb]
    // 0xbee554: DecompressPointer r1
    //     0xbee554: add             x1, x1, HEAP, lsl #32
    // 0xbee558: LoadField: r2 = r1->field_73
    //     0xbee558: ldur            w2, [x1, #0x73]
    // 0xbee55c: DecompressPointer r2
    //     0xbee55c: add             x2, x2, HEAP, lsl #32
    // 0xbee560: cmp             w2, NULL
    // 0xbee564: b.ne            #0xbee570
    // 0xbee568: r3 = Null
    //     0xbee568: mov             x3, NULL
    // 0xbee56c: b               #0xbee5f8
    // 0xbee570: LoadField: r4 = r3->field_1f
    //     0xbee570: ldur            x4, [x3, #0x1f]
    // 0xbee574: LoadField: r0 = r2->field_b
    //     0xbee574: ldur            w0, [x2, #0xb]
    // 0xbee578: r1 = LoadInt32Instr(r0)
    //     0xbee578: sbfx            x1, x0, #1, #0x1f
    // 0xbee57c: mov             x0, x1
    // 0xbee580: mov             x1, x4
    // 0xbee584: cmp             x1, x0
    // 0xbee588: b.hs            #0xbeea08
    // 0xbee58c: LoadField: r0 = r2->field_f
    //     0xbee58c: ldur            w0, [x2, #0xf]
    // 0xbee590: DecompressPointer r0
    //     0xbee590: add             x0, x0, HEAP, lsl #32
    // 0xbee594: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbee594: add             x16, x0, x4, lsl #2
    //     0xbee598: ldur            w1, [x16, #0xf]
    // 0xbee59c: DecompressPointer r1
    //     0xbee59c: add             x1, x1, HEAP, lsl #32
    // 0xbee5a0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbee5a0: ldur            w0, [x1, #0x17]
    // 0xbee5a4: DecompressPointer r0
    //     0xbee5a4: add             x0, x0, HEAP, lsl #32
    // 0xbee5a8: stur            x0, [fp, #-0x38]
    // 0xbee5ac: cmp             w0, NULL
    // 0xbee5b0: b.ne            #0xbee5bc
    // 0xbee5b4: r0 = Null
    //     0xbee5b4: mov             x0, NULL
    // 0xbee5b8: b               #0xbee5f4
    // 0xbee5bc: ldur            x2, [fp, #-0x18]
    // 0xbee5c0: r1 = Function '<anonymous closure>':.
    //     0xbee5c0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c00] AnonymousClosure: (0xbf0138), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbee5c4: ldr             x1, [x1, #0xc00]
    // 0xbee5c8: r0 = AllocateClosure()
    //     0xbee5c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbee5cc: r16 = <DropdownMenuItem<AllSkuDatum>>
    //     0xbee5cc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52748] TypeArguments: <DropdownMenuItem<AllSkuDatum>>
    //     0xbee5d0: ldr             x16, [x16, #0x748]
    // 0xbee5d4: ldur            lr, [fp, #-0x38]
    // 0xbee5d8: stp             lr, x16, [SP, #8]
    // 0xbee5dc: str             x0, [SP]
    // 0xbee5e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbee5e0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbee5e4: r0 = map()
    //     0xbee5e4: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xbee5e8: mov             x1, x0
    // 0xbee5ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbee5ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbee5f0: r0 = toList()
    //     0xbee5f0: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xbee5f4: mov             x3, x0
    // 0xbee5f8: ldur            x0, [fp, #-0x20]
    // 0xbee5fc: ldur            x2, [fp, #-0x18]
    // 0xbee600: stur            x3, [fp, #-0x38]
    // 0xbee604: r1 = Function '<anonymous closure>':.
    //     0xbee604: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c08] AnonymousClosure: (0xbefe98), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xbee608: ldr             x1, [x1, #0xc08]
    // 0xbee60c: r0 = AllocateClosure()
    //     0xbee60c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbee610: r1 = <AllSkuDatum>
    //     0xbee610: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xbee614: ldr             x1, [x1, #0xea8]
    // 0xbee618: stur            x0, [fp, #-0x40]
    // 0xbee61c: r0 = DropdownButton()
    //     0xbee61c: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xbee620: stur            x0, [fp, #-0x50]
    // 0xbee624: r16 = Instance_Alignment
    //     0xbee624: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbee628: ldr             x16, [x16, #0xb10]
    // 0xbee62c: ldur            lr, [fp, #-0x30]
    // 0xbee630: stp             lr, x16, [SP]
    // 0xbee634: mov             x1, x0
    // 0xbee638: ldur            x2, [fp, #-0x38]
    // 0xbee63c: ldur            x3, [fp, #-0x40]
    // 0xbee640: ldur            x5, [fp, #-0x10]
    // 0xbee644: r4 = const [0, 0x6, 0x2, 0x4, alignment, 0x4, icon, 0x5, null]
    //     0xbee644: add             x4, PP, #0x53, lsl #12  ; [pp+0x53bf8] List(9) [0, 0x6, 0x2, 0x4, "alignment", 0x4, "icon", 0x5, Null]
    //     0xbee648: ldr             x4, [x4, #0xbf8]
    // 0xbee64c: r0 = DropdownButton()
    //     0xbee64c: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xbee650: r0 = DropdownButtonHideUnderline()
    //     0xbee650: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xbee654: mov             x1, x0
    // 0xbee658: ldur            x0, [fp, #-0x50]
    // 0xbee65c: stur            x1, [fp, #-0x10]
    // 0xbee660: StoreField: r1->field_b = r0
    //     0xbee660: stur            w0, [x1, #0xb]
    // 0xbee664: r0 = Container()
    //     0xbee664: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbee668: stur            x0, [fp, #-0x30]
    // 0xbee66c: r16 = inf
    //     0xbee66c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbee670: ldr             x16, [x16, #0x9f8]
    // 0xbee674: r30 = Instance_AlignmentDirectional
    //     0xbee674: add             lr, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xbee678: ldr             lr, [lr, #0x3a8]
    // 0xbee67c: stp             lr, x16, [SP, #0x10]
    // 0xbee680: ldur            x16, [fp, #-0x28]
    // 0xbee684: ldur            lr, [fp, #-0x10]
    // 0xbee688: stp             lr, x16, [SP]
    // 0xbee68c: mov             x1, x0
    // 0xbee690: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, child, 0x4, decoration, 0x3, width, 0x1, null]
    //     0xbee690: add             x4, PP, #0x52, lsl #12  ; [pp+0x52758] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "child", 0x4, "decoration", 0x3, "width", 0x1, Null]
    //     0xbee694: ldr             x4, [x4, #0x758]
    // 0xbee698: r0 = Container()
    //     0xbee698: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbee69c: r0 = Padding()
    //     0xbee69c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbee6a0: mov             x2, x0
    // 0xbee6a4: r0 = Instance_EdgeInsets
    //     0xbee6a4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbee6a8: ldr             x0, [x0, #0x668]
    // 0xbee6ac: stur            x2, [fp, #-0x10]
    // 0xbee6b0: StoreField: r2->field_f = r0
    //     0xbee6b0: stur            w0, [x2, #0xf]
    // 0xbee6b4: ldur            x0, [fp, #-0x30]
    // 0xbee6b8: StoreField: r2->field_b = r0
    //     0xbee6b8: stur            w0, [x2, #0xb]
    // 0xbee6bc: ldur            x0, [fp, #-0x20]
    // 0xbee6c0: LoadField: r1 = r0->field_b
    //     0xbee6c0: ldur            w1, [x0, #0xb]
    // 0xbee6c4: LoadField: r3 = r0->field_f
    //     0xbee6c4: ldur            w3, [x0, #0xf]
    // 0xbee6c8: DecompressPointer r3
    //     0xbee6c8: add             x3, x3, HEAP, lsl #32
    // 0xbee6cc: LoadField: r4 = r3->field_b
    //     0xbee6cc: ldur            w4, [x3, #0xb]
    // 0xbee6d0: r3 = LoadInt32Instr(r1)
    //     0xbee6d0: sbfx            x3, x1, #1, #0x1f
    // 0xbee6d4: stur            x3, [fp, #-0x48]
    // 0xbee6d8: r1 = LoadInt32Instr(r4)
    //     0xbee6d8: sbfx            x1, x4, #1, #0x1f
    // 0xbee6dc: cmp             x3, x1
    // 0xbee6e0: b.ne            #0xbee6ec
    // 0xbee6e4: mov             x1, x0
    // 0xbee6e8: r0 = _growToNextCapacity()
    //     0xbee6e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbee6ec: ldur            x2, [fp, #-0x20]
    // 0xbee6f0: ldur            x3, [fp, #-0x48]
    // 0xbee6f4: add             x0, x3, #1
    // 0xbee6f8: lsl             x1, x0, #1
    // 0xbee6fc: StoreField: r2->field_b = r1
    //     0xbee6fc: stur            w1, [x2, #0xb]
    // 0xbee700: LoadField: r1 = r2->field_f
    //     0xbee700: ldur            w1, [x2, #0xf]
    // 0xbee704: DecompressPointer r1
    //     0xbee704: add             x1, x1, HEAP, lsl #32
    // 0xbee708: ldur            x0, [fp, #-0x10]
    // 0xbee70c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbee70c: add             x25, x1, x3, lsl #2
    //     0xbee710: add             x25, x25, #0xf
    //     0xbee714: str             w0, [x25]
    //     0xbee718: tbz             w0, #0, #0xbee734
    //     0xbee71c: ldurb           w16, [x1, #-1]
    //     0xbee720: ldurb           w17, [x0, #-1]
    //     0xbee724: and             x16, x17, x16, lsr #2
    //     0xbee728: tst             x16, HEAP, lsr #32
    //     0xbee72c: b.eq            #0xbee734
    //     0xbee730: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbee734: ldur            x0, [fp, #-8]
    // 0xbee738: ldur            x3, [fp, #-0x18]
    // 0xbee73c: LoadField: r1 = r3->field_13
    //     0xbee73c: ldur            w1, [x3, #0x13]
    // 0xbee740: DecompressPointer r1
    //     0xbee740: add             x1, x1, HEAP, lsl #32
    // 0xbee744: r0 = of()
    //     0xbee744: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbee748: r17 = 307
    //     0xbee748: movz            x17, #0x133
    // 0xbee74c: ldr             w1, [x0, x17]
    // 0xbee750: DecompressPointer r1
    //     0xbee750: add             x1, x1, HEAP, lsl #32
    // 0xbee754: ldur            x0, [fp, #-8]
    // 0xbee758: stur            x1, [fp, #-0x10]
    // 0xbee75c: LoadField: r2 = r0->field_b
    //     0xbee75c: ldur            w2, [x0, #0xb]
    // 0xbee760: DecompressPointer r2
    //     0xbee760: add             x2, x2, HEAP, lsl #32
    // 0xbee764: cmp             w2, NULL
    // 0xbee768: b.eq            #0xbeea0c
    // 0xbee76c: LoadField: r0 = r2->field_b
    //     0xbee76c: ldur            w0, [x2, #0xb]
    // 0xbee770: DecompressPointer r0
    //     0xbee770: add             x0, x0, HEAP, lsl #32
    // 0xbee774: LoadField: r2 = r0->field_f
    //     0xbee774: ldur            w2, [x0, #0xf]
    // 0xbee778: DecompressPointer r2
    //     0xbee778: add             x2, x2, HEAP, lsl #32
    // 0xbee77c: cmp             w2, NULL
    // 0xbee780: b.ne            #0xbee78c
    // 0xbee784: r0 = Null
    //     0xbee784: mov             x0, NULL
    // 0xbee788: b               #0xbee7a4
    // 0xbee78c: r0 = LoadClassIdInstr(r2)
    //     0xbee78c: ldur            x0, [x2, #-1]
    //     0xbee790: ubfx            x0, x0, #0xc, #0x14
    // 0xbee794: str             x2, [SP]
    // 0xbee798: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbee798: sub             lr, x0, #1, lsl #12
    //     0xbee79c: ldr             lr, [x21, lr, lsl #3]
    //     0xbee7a0: blr             lr
    // 0xbee7a4: cmp             w0, NULL
    // 0xbee7a8: b.ne            #0xbee7b4
    // 0xbee7ac: r4 = ""
    //     0xbee7ac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbee7b0: b               #0xbee7b8
    // 0xbee7b4: mov             x4, x0
    // 0xbee7b8: ldur            x3, [fp, #-0x18]
    // 0xbee7bc: ldur            x0, [fp, #-0x10]
    // 0xbee7c0: ldur            x2, [fp, #-0x20]
    // 0xbee7c4: stur            x4, [fp, #-8]
    // 0xbee7c8: LoadField: r1 = r3->field_13
    //     0xbee7c8: ldur            w1, [x3, #0x13]
    // 0xbee7cc: DecompressPointer r1
    //     0xbee7cc: add             x1, x1, HEAP, lsl #32
    // 0xbee7d0: r0 = of()
    //     0xbee7d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbee7d4: LoadField: r1 = r0->field_87
    //     0xbee7d4: ldur            w1, [x0, #0x87]
    // 0xbee7d8: DecompressPointer r1
    //     0xbee7d8: add             x1, x1, HEAP, lsl #32
    // 0xbee7dc: LoadField: r0 = r1->field_7
    //     0xbee7dc: ldur            w0, [x1, #7]
    // 0xbee7e0: DecompressPointer r0
    //     0xbee7e0: add             x0, x0, HEAP, lsl #32
    // 0xbee7e4: r16 = 16.000000
    //     0xbee7e4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbee7e8: ldr             x16, [x16, #0x188]
    // 0xbee7ec: r30 = Instance_Color
    //     0xbee7ec: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbee7f0: stp             lr, x16, [SP]
    // 0xbee7f4: mov             x1, x0
    // 0xbee7f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbee7f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbee7fc: ldr             x4, [x4, #0xaa0]
    // 0xbee800: r0 = copyWith()
    //     0xbee800: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbee804: stur            x0, [fp, #-0x28]
    // 0xbee808: r0 = Text()
    //     0xbee808: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbee80c: mov             x3, x0
    // 0xbee810: ldur            x0, [fp, #-8]
    // 0xbee814: stur            x3, [fp, #-0x30]
    // 0xbee818: StoreField: r3->field_b = r0
    //     0xbee818: stur            w0, [x3, #0xb]
    // 0xbee81c: ldur            x0, [fp, #-0x28]
    // 0xbee820: StoreField: r3->field_13 = r0
    //     0xbee820: stur            w0, [x3, #0x13]
    // 0xbee824: ldur            x2, [fp, #-0x18]
    // 0xbee828: r1 = Function '<anonymous closure>':.
    //     0xbee828: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c10] AnonymousClosure: (0xbeea10), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xbee82c: ldr             x1, [x1, #0xc10]
    // 0xbee830: r0 = AllocateClosure()
    //     0xbee830: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbee834: stur            x0, [fp, #-8]
    // 0xbee838: r0 = TextButton()
    //     0xbee838: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbee83c: mov             x1, x0
    // 0xbee840: ldur            x0, [fp, #-8]
    // 0xbee844: stur            x1, [fp, #-0x18]
    // 0xbee848: StoreField: r1->field_b = r0
    //     0xbee848: stur            w0, [x1, #0xb]
    // 0xbee84c: r0 = false
    //     0xbee84c: add             x0, NULL, #0x30  ; false
    // 0xbee850: StoreField: r1->field_27 = r0
    //     0xbee850: stur            w0, [x1, #0x27]
    // 0xbee854: r0 = true
    //     0xbee854: add             x0, NULL, #0x20  ; true
    // 0xbee858: StoreField: r1->field_2f = r0
    //     0xbee858: stur            w0, [x1, #0x2f]
    // 0xbee85c: ldur            x0, [fp, #-0x30]
    // 0xbee860: StoreField: r1->field_37 = r0
    //     0xbee860: stur            w0, [x1, #0x37]
    // 0xbee864: r0 = Instance_ValueKey
    //     0xbee864: add             x0, PP, #0x53, lsl #12  ; [pp+0x53c18] Obj!ValueKey<String>@d5b2f1
    //     0xbee868: ldr             x0, [x0, #0xc18]
    // 0xbee86c: StoreField: r1->field_7 = r0
    //     0xbee86c: stur            w0, [x1, #7]
    // 0xbee870: r0 = TextButtonTheme()
    //     0xbee870: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbee874: mov             x1, x0
    // 0xbee878: ldur            x0, [fp, #-0x10]
    // 0xbee87c: stur            x1, [fp, #-8]
    // 0xbee880: StoreField: r1->field_f = r0
    //     0xbee880: stur            w0, [x1, #0xf]
    // 0xbee884: ldur            x0, [fp, #-0x18]
    // 0xbee888: StoreField: r1->field_b = r0
    //     0xbee888: stur            w0, [x1, #0xb]
    // 0xbee88c: r0 = SizedBox()
    //     0xbee88c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbee890: mov             x1, x0
    // 0xbee894: r0 = inf
    //     0xbee894: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbee898: ldr             x0, [x0, #0x9f8]
    // 0xbee89c: stur            x1, [fp, #-0x10]
    // 0xbee8a0: StoreField: r1->field_f = r0
    //     0xbee8a0: stur            w0, [x1, #0xf]
    // 0xbee8a4: r0 = 44.000000
    //     0xbee8a4: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbee8a8: ldr             x0, [x0, #0xad8]
    // 0xbee8ac: StoreField: r1->field_13 = r0
    //     0xbee8ac: stur            w0, [x1, #0x13]
    // 0xbee8b0: ldur            x0, [fp, #-8]
    // 0xbee8b4: StoreField: r1->field_b = r0
    //     0xbee8b4: stur            w0, [x1, #0xb]
    // 0xbee8b8: r0 = Padding()
    //     0xbee8b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbee8bc: mov             x2, x0
    // 0xbee8c0: r0 = Instance_EdgeInsets
    //     0xbee8c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbee8c4: ldr             x0, [x0, #0xa00]
    // 0xbee8c8: stur            x2, [fp, #-8]
    // 0xbee8cc: StoreField: r2->field_f = r0
    //     0xbee8cc: stur            w0, [x2, #0xf]
    // 0xbee8d0: ldur            x0, [fp, #-0x10]
    // 0xbee8d4: StoreField: r2->field_b = r0
    //     0xbee8d4: stur            w0, [x2, #0xb]
    // 0xbee8d8: ldur            x0, [fp, #-0x20]
    // 0xbee8dc: LoadField: r1 = r0->field_b
    //     0xbee8dc: ldur            w1, [x0, #0xb]
    // 0xbee8e0: LoadField: r3 = r0->field_f
    //     0xbee8e0: ldur            w3, [x0, #0xf]
    // 0xbee8e4: DecompressPointer r3
    //     0xbee8e4: add             x3, x3, HEAP, lsl #32
    // 0xbee8e8: LoadField: r4 = r3->field_b
    //     0xbee8e8: ldur            w4, [x3, #0xb]
    // 0xbee8ec: r3 = LoadInt32Instr(r1)
    //     0xbee8ec: sbfx            x3, x1, #1, #0x1f
    // 0xbee8f0: stur            x3, [fp, #-0x48]
    // 0xbee8f4: r1 = LoadInt32Instr(r4)
    //     0xbee8f4: sbfx            x1, x4, #1, #0x1f
    // 0xbee8f8: cmp             x3, x1
    // 0xbee8fc: b.ne            #0xbee908
    // 0xbee900: mov             x1, x0
    // 0xbee904: r0 = _growToNextCapacity()
    //     0xbee904: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbee908: ldur            x2, [fp, #-0x20]
    // 0xbee90c: ldur            x3, [fp, #-0x48]
    // 0xbee910: add             x0, x3, #1
    // 0xbee914: lsl             x1, x0, #1
    // 0xbee918: StoreField: r2->field_b = r1
    //     0xbee918: stur            w1, [x2, #0xb]
    // 0xbee91c: LoadField: r1 = r2->field_f
    //     0xbee91c: ldur            w1, [x2, #0xf]
    // 0xbee920: DecompressPointer r1
    //     0xbee920: add             x1, x1, HEAP, lsl #32
    // 0xbee924: ldur            x0, [fp, #-8]
    // 0xbee928: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbee928: add             x25, x1, x3, lsl #2
    //     0xbee92c: add             x25, x25, #0xf
    //     0xbee930: str             w0, [x25]
    //     0xbee934: tbz             w0, #0, #0xbee950
    //     0xbee938: ldurb           w16, [x1, #-1]
    //     0xbee93c: ldurb           w17, [x0, #-1]
    //     0xbee940: and             x16, x17, x16, lsr #2
    //     0xbee944: tst             x16, HEAP, lsr #32
    //     0xbee948: b.eq            #0xbee950
    //     0xbee94c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbee950: r0 = Column()
    //     0xbee950: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbee954: mov             x1, x0
    // 0xbee958: r0 = Instance_Axis
    //     0xbee958: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbee95c: stur            x1, [fp, #-8]
    // 0xbee960: StoreField: r1->field_f = r0
    //     0xbee960: stur            w0, [x1, #0xf]
    // 0xbee964: r0 = Instance_MainAxisAlignment
    //     0xbee964: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbee968: ldr             x0, [x0, #0xa08]
    // 0xbee96c: StoreField: r1->field_13 = r0
    //     0xbee96c: stur            w0, [x1, #0x13]
    // 0xbee970: r0 = Instance_MainAxisSize
    //     0xbee970: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbee974: ldr             x0, [x0, #0xdd0]
    // 0xbee978: ArrayStore: r1[0] = r0  ; List_4
    //     0xbee978: stur            w0, [x1, #0x17]
    // 0xbee97c: r0 = Instance_CrossAxisAlignment
    //     0xbee97c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbee980: ldr             x0, [x0, #0xa18]
    // 0xbee984: StoreField: r1->field_1b = r0
    //     0xbee984: stur            w0, [x1, #0x1b]
    // 0xbee988: r0 = Instance_VerticalDirection
    //     0xbee988: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbee98c: ldr             x0, [x0, #0xa20]
    // 0xbee990: StoreField: r1->field_23 = r0
    //     0xbee990: stur            w0, [x1, #0x23]
    // 0xbee994: r0 = Instance_Clip
    //     0xbee994: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbee998: ldr             x0, [x0, #0x38]
    // 0xbee99c: StoreField: r1->field_2b = r0
    //     0xbee99c: stur            w0, [x1, #0x2b]
    // 0xbee9a0: StoreField: r1->field_2f = rZR
    //     0xbee9a0: stur            xzr, [x1, #0x2f]
    // 0xbee9a4: ldur            x0, [fp, #-0x20]
    // 0xbee9a8: StoreField: r1->field_b = r0
    //     0xbee9a8: stur            w0, [x1, #0xb]
    // 0xbee9ac: r0 = Padding()
    //     0xbee9ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbee9b0: r1 = Instance_EdgeInsets
    //     0xbee9b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbee9b4: ldr             x1, [x1, #0x1f0]
    // 0xbee9b8: StoreField: r0->field_f = r1
    //     0xbee9b8: stur            w1, [x0, #0xf]
    // 0xbee9bc: ldur            x1, [fp, #-8]
    // 0xbee9c0: StoreField: r0->field_b = r1
    //     0xbee9c0: stur            w1, [x0, #0xb]
    // 0xbee9c4: LeaveFrame
    //     0xbee9c4: mov             SP, fp
    //     0xbee9c8: ldp             fp, lr, [SP], #0x10
    // 0xbee9cc: ret
    //     0xbee9cc: ret             
    // 0xbee9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbee9d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbee9d4: b               #0xbedbd8
    // 0xbee9d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbee9d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbee9dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbee9dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbee9e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbee9e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbee9e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbee9e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbee9e8: r9 = dropDownValue
    //     0xbee9e8: add             x9, PP, #0x53, lsl #12  ; [pp+0x53c20] Field <<EMAIL>>: late (offset: 0x14)
    //     0xbee9ec: ldr             x9, [x9, #0xc20]
    // 0xbee9f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbee9f0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbee9f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbee9f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbee9f8: r9 = dropDownValue
    //     0xbee9f8: add             x9, PP, #0x53, lsl #12  ; [pp+0x53c20] Field <<EMAIL>>: late (offset: 0x14)
    //     0xbee9fc: ldr             x9, [x9, #0xc20]
    // 0xbeea00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbeea00: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbeea04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeea04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbeea08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbeea08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbeea0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeea0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbeea10, size: 0x1488
    // 0xbeea10: EnterFrame
    //     0xbeea10: stp             fp, lr, [SP, #-0x10]!
    //     0xbeea14: mov             fp, SP
    // 0xbeea18: AllocStack(0x80)
    //     0xbeea18: sub             SP, SP, #0x80
    // 0xbeea1c: SetupParameters()
    //     0xbeea1c: ldr             x0, [fp, #0x10]
    //     0xbeea20: ldur            w1, [x0, #0x17]
    //     0xbeea24: add             x1, x1, HEAP, lsl #32
    //     0xbeea28: stur            x1, [fp, #-8]
    // 0xbeea2c: CheckStackOverflow
    //     0xbeea2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeea30: cmp             SP, x16
    //     0xbeea34: b.ls            #0xbefd9c
    // 0xbeea38: LoadField: r0 = r1->field_13
    //     0xbeea38: ldur            w0, [x1, #0x13]
    // 0xbeea3c: DecompressPointer r0
    //     0xbeea3c: add             x0, x0, HEAP, lsl #32
    // 0xbeea40: r16 = <Object?>
    //     0xbeea40: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xbeea44: stp             x0, x16, [SP]
    // 0xbeea48: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbeea48: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbeea4c: r0 = pop()
    //     0xbeea4c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xbeea50: ldur            x1, [fp, #-8]
    // 0xbeea54: LoadField: r0 = r1->field_f
    //     0xbeea54: ldur            w0, [x1, #0xf]
    // 0xbeea58: DecompressPointer r0
    //     0xbeea58: add             x0, x0, HEAP, lsl #32
    // 0xbeea5c: LoadField: r2 = r0->field_b
    //     0xbeea5c: ldur            w2, [x0, #0xb]
    // 0xbeea60: DecompressPointer r2
    //     0xbeea60: add             x2, x2, HEAP, lsl #32
    // 0xbeea64: cmp             w2, NULL
    // 0xbeea68: b.eq            #0xbefda4
    // 0xbeea6c: LoadField: r0 = r2->field_f
    //     0xbeea6c: ldur            w0, [x2, #0xf]
    // 0xbeea70: DecompressPointer r0
    //     0xbeea70: add             x0, x0, HEAP, lsl #32
    // 0xbeea74: r2 = LoadClassIdInstr(r0)
    //     0xbeea74: ldur            x2, [x0, #-1]
    //     0xbeea78: ubfx            x2, x2, #0xc, #0x14
    // 0xbeea7c: r16 = "home_page"
    //     0xbeea7c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xbeea80: ldr             x16, [x16, #0xe60]
    // 0xbeea84: stp             x16, x0, [SP]
    // 0xbeea88: mov             x0, x2
    // 0xbeea8c: mov             lr, x0
    // 0xbeea90: ldr             lr, [x21, lr, lsl #3]
    // 0xbeea94: blr             lr
    // 0xbeea98: tbnz            w0, #4, #0xbeead8
    // 0xbeea9c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbeea9c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbeeaa0: ldr             x0, [x0, #0x1c80]
    //     0xbeeaa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbeeaa8: cmp             w0, w16
    //     0xbeeaac: b.ne            #0xbeeab8
    //     0xbeeab0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbeeab4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbeeab8: r16 = <HomeController>
    //     0xbeeab8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xbeeabc: ldr             x16, [x16, #0xf98]
    // 0xbeeac0: str             x16, [SP]
    // 0xbeeac4: r4 = const [0x1, 0, 0, 0, null]
    //     0xbeeac4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbeeac8: r0 = Inst.find()
    //     0xbeeac8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbeeacc: r3 = "product_card"
    //     0xbeeacc: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xbeead0: ldr             x3, [x3, #0xc28]
    // 0xbeead4: b               #0xbeec6c
    // 0xbeead8: ldur            x1, [fp, #-8]
    // 0xbeeadc: LoadField: r0 = r1->field_f
    //     0xbeeadc: ldur            w0, [x1, #0xf]
    // 0xbeeae0: DecompressPointer r0
    //     0xbeeae0: add             x0, x0, HEAP, lsl #32
    // 0xbeeae4: LoadField: r2 = r0->field_b
    //     0xbeeae4: ldur            w2, [x0, #0xb]
    // 0xbeeae8: DecompressPointer r2
    //     0xbeeae8: add             x2, x2, HEAP, lsl #32
    // 0xbeeaec: cmp             w2, NULL
    // 0xbeeaf0: b.eq            #0xbefda8
    // 0xbeeaf4: LoadField: r0 = r2->field_f
    //     0xbeeaf4: ldur            w0, [x2, #0xf]
    // 0xbeeaf8: DecompressPointer r0
    //     0xbeeaf8: add             x0, x0, HEAP, lsl #32
    // 0xbeeafc: r2 = LoadClassIdInstr(r0)
    //     0xbeeafc: ldur            x2, [x0, #-1]
    //     0xbeeb00: ubfx            x2, x2, #0xc, #0x14
    // 0xbeeb04: r16 = "collection_page"
    //     0xbeeb04: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xbeeb08: ldr             x16, [x16, #0x118]
    // 0xbeeb0c: stp             x16, x0, [SP]
    // 0xbeeb10: mov             x0, x2
    // 0xbeeb14: mov             lr, x0
    // 0xbeeb18: ldr             lr, [x21, lr, lsl #3]
    // 0xbeeb1c: blr             lr
    // 0xbeeb20: tbnz            w0, #4, #0xbeeb60
    // 0xbeeb24: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbeeb24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbeeb28: ldr             x0, [x0, #0x1c80]
    //     0xbeeb2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbeeb30: cmp             w0, w16
    //     0xbeeb34: b.ne            #0xbeeb40
    //     0xbeeb38: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbeeb3c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbeeb40: r16 = <CollectionsController>
    //     0xbeeb40: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xbeeb44: ldr             x16, [x16, #0xb00]
    // 0xbeeb48: str             x16, [SP]
    // 0xbeeb4c: r4 = const [0x1, 0, 0, 0, null]
    //     0xbeeb4c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbeeb50: r0 = Inst.find()
    //     0xbeeb50: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbeeb54: r0 = "collection_page"
    //     0xbeeb54: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xbeeb58: ldr             x0, [x0, #0x118]
    // 0xbeeb5c: b               #0xbeec68
    // 0xbeeb60: ldur            x1, [fp, #-8]
    // 0xbeeb64: LoadField: r0 = r1->field_f
    //     0xbeeb64: ldur            w0, [x1, #0xf]
    // 0xbeeb68: DecompressPointer r0
    //     0xbeeb68: add             x0, x0, HEAP, lsl #32
    // 0xbeeb6c: LoadField: r2 = r0->field_b
    //     0xbeeb6c: ldur            w2, [x0, #0xb]
    // 0xbeeb70: DecompressPointer r2
    //     0xbeeb70: add             x2, x2, HEAP, lsl #32
    // 0xbeeb74: cmp             w2, NULL
    // 0xbeeb78: b.eq            #0xbefdac
    // 0xbeeb7c: LoadField: r0 = r2->field_f
    //     0xbeeb7c: ldur            w0, [x2, #0xf]
    // 0xbeeb80: DecompressPointer r0
    //     0xbeeb80: add             x0, x0, HEAP, lsl #32
    // 0xbeeb84: r2 = LoadClassIdInstr(r0)
    //     0xbeeb84: ldur            x2, [x0, #-1]
    //     0xbeeb88: ubfx            x2, x2, #0xc, #0x14
    // 0xbeeb8c: r16 = "order_success_page"
    //     0xbeeb8c: add             x16, PP, #0x34, lsl #12  ; [pp+0x341c0] "order_success_page"
    //     0xbeeb90: ldr             x16, [x16, #0x1c0]
    // 0xbeeb94: stp             x16, x0, [SP]
    // 0xbeeb98: mov             x0, x2
    // 0xbeeb9c: mov             lr, x0
    // 0xbeeba0: ldr             lr, [x21, lr, lsl #3]
    // 0xbeeba4: blr             lr
    // 0xbeeba8: tbnz            w0, #4, #0xbeec30
    // 0xbeebac: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbeebac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbeebb0: ldr             x0, [x0, #0x1c80]
    //     0xbeebb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbeebb8: cmp             w0, w16
    //     0xbeebbc: b.ne            #0xbeebc8
    //     0xbeebc0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbeebc4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbeebc8: r16 = <OrderSuccessController>
    //     0xbeebc8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c878] TypeArguments: <OrderSuccessController>
    //     0xbeebcc: ldr             x16, [x16, #0x878]
    // 0xbeebd0: str             x16, [SP]
    // 0xbeebd4: r4 = const [0x1, 0, 0, 0, null]
    //     0xbeebd4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbeebd8: r0 = Inst.find()
    //     0xbeebd8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbeebdc: stur            x0, [fp, #-0x10]
    // 0xbeebe0: r16 = <ProductDetailController>
    //     0xbeebe0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xbeebe4: ldr             x16, [x16, #0xde0]
    // 0xbeebe8: str             x16, [SP]
    // 0xbeebec: r4 = const [0x1, 0, 0, 0, null]
    //     0xbeebec: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbeebf0: r0 = Inst.delete()
    //     0xbeebf0: bl              #0x9c7d48  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.delete
    // 0xbeebf4: r16 = <Type>
    //     0xbeebf4: ldr             x16, [PP, #0x4b40]  ; [pp+0x4b40] TypeArguments: <Type>
    // 0xbeebf8: r30 = ProductDetailController
    //     0xbeebf8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ede8] Type: ProductDetailController
    //     0xbeebfc: ldr             lr, [lr, #0xde8]
    // 0xbeec00: stp             lr, x16, [SP]
    // 0xbeec04: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbeec04: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbeec08: r0 = Inst.put()
    //     0xbeec08: bl              #0xa5f404  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.put
    // 0xbeec0c: ldur            x0, [fp, #-0x10]
    // 0xbeec10: r17 = 283
    //     0xbeec10: movz            x17, #0x11b
    // 0xbeec14: ldr             w1, [x0, x17]
    // 0xbeec18: DecompressPointer r1
    //     0xbeec18: add             x1, x1, HEAP, lsl #32
    // 0xbeec1c: r2 = false
    //     0xbeec1c: add             x2, NULL, #0x30  ; false
    // 0xbeec20: r0 = value=()
    //     0xbeec20: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xbeec24: r0 = "order_success_page"
    //     0xbeec24: add             x0, PP, #0x34, lsl #12  ; [pp+0x341c0] "order_success_page"
    //     0xbeec28: ldr             x0, [x0, #0x1c0]
    // 0xbeec2c: b               #0xbeec68
    // 0xbeec30: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbeec30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbeec34: ldr             x0, [x0, #0x1c80]
    //     0xbeec38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbeec3c: cmp             w0, w16
    //     0xbeec40: b.ne            #0xbeec4c
    //     0xbeec44: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbeec48: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbeec4c: r16 = <HomeController>
    //     0xbeec4c: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xbeec50: ldr             x16, [x16, #0xf98]
    // 0xbeec54: str             x16, [SP]
    // 0xbeec58: r4 = const [0x1, 0, 0, 0, null]
    //     0xbeec58: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbeec5c: r0 = Inst.find()
    //     0xbeec5c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbeec60: r0 = "home_page"
    //     0xbeec60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xbeec64: ldr             x0, [x0, #0xe60]
    // 0xbeec68: mov             x3, x0
    // 0xbeec6c: ldur            x2, [fp, #-8]
    // 0xbeec70: stur            x3, [fp, #-0x30]
    // 0xbeec74: LoadField: r4 = r2->field_f
    //     0xbeec74: ldur            w4, [x2, #0xf]
    // 0xbeec78: DecompressPointer r4
    //     0xbeec78: add             x4, x4, HEAP, lsl #32
    // 0xbeec7c: LoadField: r5 = r4->field_b
    //     0xbeec7c: ldur            w5, [x4, #0xb]
    // 0xbeec80: DecompressPointer r5
    //     0xbeec80: add             x5, x5, HEAP, lsl #32
    // 0xbeec84: stur            x5, [fp, #-0x28]
    // 0xbeec88: cmp             w5, NULL
    // 0xbeec8c: b.eq            #0xbefdb0
    // 0xbeec90: LoadField: r6 = r5->field_f
    //     0xbeec90: ldur            w6, [x5, #0xf]
    // 0xbeec94: DecompressPointer r6
    //     0xbeec94: add             x6, x6, HEAP, lsl #32
    // 0xbeec98: stur            x6, [fp, #-0x20]
    // 0xbeec9c: LoadField: r0 = r5->field_b
    //     0xbeec9c: ldur            w0, [x5, #0xb]
    // 0xbeeca0: DecompressPointer r0
    //     0xbeeca0: add             x0, x0, HEAP, lsl #32
    // 0xbeeca4: LoadField: r7 = r0->field_6f
    //     0xbeeca4: ldur            w7, [x0, #0x6f]
    // 0xbeeca8: DecompressPointer r7
    //     0xbeeca8: add             x7, x7, HEAP, lsl #32
    // 0xbeecac: cmp             w7, NULL
    // 0xbeecb0: b.ne            #0xbeecbc
    // 0xbeecb4: r8 = Null
    //     0xbeecb4: mov             x8, NULL
    // 0xbeecb8: b               #0xbeecf8
    // 0xbeecbc: ArrayLoad: r8 = r4[0]  ; List_8
    //     0xbeecbc: ldur            x8, [x4, #0x17]
    // 0xbeecc0: LoadField: r0 = r7->field_b
    //     0xbeecc0: ldur            w0, [x7, #0xb]
    // 0xbeecc4: r1 = LoadInt32Instr(r0)
    //     0xbeecc4: sbfx            x1, x0, #1, #0x1f
    // 0xbeecc8: mov             x0, x1
    // 0xbeeccc: mov             x1, x8
    // 0xbeecd0: cmp             x1, x0
    // 0xbeecd4: b.hs            #0xbefdb4
    // 0xbeecd8: LoadField: r0 = r7->field_f
    //     0xbeecd8: ldur            w0, [x7, #0xf]
    // 0xbeecdc: DecompressPointer r0
    //     0xbeecdc: add             x0, x0, HEAP, lsl #32
    // 0xbeece0: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbeece0: add             x16, x0, x8, lsl #2
    //     0xbeece4: ldur            w1, [x16, #0xf]
    // 0xbeece8: DecompressPointer r1
    //     0xbeece8: add             x1, x1, HEAP, lsl #32
    // 0xbeecec: LoadField: r0 = r1->field_f
    //     0xbeecec: ldur            w0, [x1, #0xf]
    // 0xbeecf0: DecompressPointer r0
    //     0xbeecf0: add             x0, x0, HEAP, lsl #32
    // 0xbeecf4: mov             x8, x0
    // 0xbeecf8: stur            x8, [fp, #-0x18]
    // 0xbeecfc: cmp             w7, NULL
    // 0xbeed00: b.ne            #0xbeed0c
    // 0xbeed04: r9 = Null
    //     0xbeed04: mov             x9, NULL
    // 0xbeed08: b               #0xbeed48
    // 0xbeed0c: ArrayLoad: r9 = r4[0]  ; List_8
    //     0xbeed0c: ldur            x9, [x4, #0x17]
    // 0xbeed10: LoadField: r0 = r7->field_b
    //     0xbeed10: ldur            w0, [x7, #0xb]
    // 0xbeed14: r1 = LoadInt32Instr(r0)
    //     0xbeed14: sbfx            x1, x0, #1, #0x1f
    // 0xbeed18: mov             x0, x1
    // 0xbeed1c: mov             x1, x9
    // 0xbeed20: cmp             x1, x0
    // 0xbeed24: b.hs            #0xbefdb8
    // 0xbeed28: LoadField: r0 = r7->field_f
    //     0xbeed28: ldur            w0, [x7, #0xf]
    // 0xbeed2c: DecompressPointer r0
    //     0xbeed2c: add             x0, x0, HEAP, lsl #32
    // 0xbeed30: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xbeed30: add             x16, x0, x9, lsl #2
    //     0xbeed34: ldur            w1, [x16, #0xf]
    // 0xbeed38: DecompressPointer r1
    //     0xbeed38: add             x1, x1, HEAP, lsl #32
    // 0xbeed3c: LoadField: r0 = r1->field_b
    //     0xbeed3c: ldur            w0, [x1, #0xb]
    // 0xbeed40: DecompressPointer r0
    //     0xbeed40: add             x0, x0, HEAP, lsl #32
    // 0xbeed44: mov             x9, x0
    // 0xbeed48: stur            x9, [fp, #-0x10]
    // 0xbeed4c: cmp             w7, NULL
    // 0xbeed50: b.ne            #0xbeed74
    // 0xbeed54: mov             x0, x2
    // 0xbeed58: mov             x1, x3
    // 0xbeed5c: mov             x3, x6
    // 0xbeed60: mov             x4, x8
    // 0xbeed64: mov             x2, x5
    // 0xbeed68: mov             x5, x9
    // 0xbeed6c: r6 = Null
    //     0xbeed6c: mov             x6, NULL
    // 0xbeed70: b               #0xbeede0
    // 0xbeed74: ArrayLoad: r10 = r4[0]  ; List_8
    //     0xbeed74: ldur            x10, [x4, #0x17]
    // 0xbeed78: LoadField: r0 = r7->field_b
    //     0xbeed78: ldur            w0, [x7, #0xb]
    // 0xbeed7c: r1 = LoadInt32Instr(r0)
    //     0xbeed7c: sbfx            x1, x0, #1, #0x1f
    // 0xbeed80: mov             x0, x1
    // 0xbeed84: mov             x1, x10
    // 0xbeed88: cmp             x1, x0
    // 0xbeed8c: b.hs            #0xbefdbc
    // 0xbeed90: LoadField: r0 = r7->field_f
    //     0xbeed90: ldur            w0, [x7, #0xf]
    // 0xbeed94: DecompressPointer r0
    //     0xbeed94: add             x0, x0, HEAP, lsl #32
    // 0xbeed98: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0xbeed98: add             x16, x0, x10, lsl #2
    //     0xbeed9c: ldur            w1, [x16, #0xf]
    // 0xbeeda0: DecompressPointer r1
    //     0xbeeda0: add             x1, x1, HEAP, lsl #32
    // 0xbeeda4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbeeda4: ldur            w0, [x1, #0x17]
    // 0xbeeda8: DecompressPointer r0
    //     0xbeeda8: add             x0, x0, HEAP, lsl #32
    // 0xbeedac: cmp             w0, NULL
    // 0xbeedb0: b.ne            #0xbeedbc
    // 0xbeedb4: r0 = Null
    //     0xbeedb4: mov             x0, NULL
    // 0xbeedb8: b               #0xbeedc4
    // 0xbeedbc: stp             x0, NULL, [SP]
    // 0xbeedc0: r0 = _Double.fromInteger()
    //     0xbeedc0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbeedc4: mov             x6, x0
    // 0xbeedc8: ldur            x0, [fp, #-8]
    // 0xbeedcc: ldur            x1, [fp, #-0x30]
    // 0xbeedd0: ldur            x3, [fp, #-0x20]
    // 0xbeedd4: ldur            x4, [fp, #-0x18]
    // 0xbeedd8: ldur            x5, [fp, #-0x10]
    // 0xbeeddc: ldur            x2, [fp, #-0x28]
    // 0xbeede0: stur            x6, [fp, #-0x38]
    // 0xbeede4: r0 = EventData()
    //     0xbeede4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xbeede8: mov             x1, x0
    // 0xbeedec: ldur            x0, [fp, #-0x20]
    // 0xbeedf0: stur            x1, [fp, #-0x40]
    // 0xbeedf4: StoreField: r1->field_13 = r0
    //     0xbeedf4: stur            w0, [x1, #0x13]
    // 0xbeedf8: ldur            x0, [fp, #-0x38]
    // 0xbeedfc: StoreField: r1->field_2f = r0
    //     0xbeedfc: stur            w0, [x1, #0x2f]
    // 0xbeee00: ldur            x0, [fp, #-0x18]
    // 0xbeee04: StoreField: r1->field_33 = r0
    //     0xbeee04: stur            w0, [x1, #0x33]
    // 0xbeee08: ldur            x0, [fp, #-0x30]
    // 0xbeee0c: StoreField: r1->field_3b = r0
    //     0xbeee0c: stur            w0, [x1, #0x3b]
    // 0xbeee10: StoreField: r1->field_87 = r0
    //     0xbeee10: stur            w0, [x1, #0x87]
    // 0xbeee14: ldur            x0, [fp, #-0x10]
    // 0xbeee18: StoreField: r1->field_8f = r0
    //     0xbeee18: stur            w0, [x1, #0x8f]
    // 0xbeee1c: r0 = EventsRequest()
    //     0xbeee1c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xbeee20: mov             x1, x0
    // 0xbeee24: r0 = "add_to_bag_clicked"
    //     0xbeee24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xbeee28: ldr             x0, [x0, #0xec8]
    // 0xbeee2c: StoreField: r1->field_7 = r0
    //     0xbeee2c: stur            w0, [x1, #7]
    // 0xbeee30: ldur            x0, [fp, #-0x40]
    // 0xbeee34: StoreField: r1->field_b = r0
    //     0xbeee34: stur            w0, [x1, #0xb]
    // 0xbeee38: ldur            x0, [fp, #-0x28]
    // 0xbeee3c: LoadField: r2 = r0->field_13
    //     0xbeee3c: ldur            w2, [x0, #0x13]
    // 0xbeee40: DecompressPointer r2
    //     0xbeee40: add             x2, x2, HEAP, lsl #32
    // 0xbeee44: stp             x1, x2, [SP]
    // 0xbeee48: r4 = 0
    //     0xbeee48: movz            x4, #0
    // 0xbeee4c: ldr             x0, [SP, #8]
    // 0xbeee50: r16 = UnlinkedCall_0x613b5c
    //     0xbeee50: add             x16, PP, #0x53, lsl #12  ; [pp+0x53c30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbeee54: add             x16, x16, #0xc30
    // 0xbeee58: ldp             x5, lr, [x16]
    // 0xbeee5c: blr             lr
    // 0xbeee60: ldur            x2, [fp, #-8]
    // 0xbeee64: LoadField: r3 = r2->field_f
    //     0xbeee64: ldur            w3, [x2, #0xf]
    // 0xbeee68: DecompressPointer r3
    //     0xbeee68: add             x3, x3, HEAP, lsl #32
    // 0xbeee6c: LoadField: r4 = r3->field_b
    //     0xbeee6c: ldur            w4, [x3, #0xb]
    // 0xbeee70: DecompressPointer r4
    //     0xbeee70: add             x4, x4, HEAP, lsl #32
    // 0xbeee74: stur            x4, [fp, #-0x18]
    // 0xbeee78: cmp             w4, NULL
    // 0xbeee7c: b.eq            #0xbefdc0
    // 0xbeee80: LoadField: r0 = r4->field_b
    //     0xbeee80: ldur            w0, [x4, #0xb]
    // 0xbeee84: DecompressPointer r0
    //     0xbeee84: add             x0, x0, HEAP, lsl #32
    // 0xbeee88: LoadField: r1 = r0->field_8b
    //     0xbeee88: ldur            w1, [x0, #0x8b]
    // 0xbeee8c: DecompressPointer r1
    //     0xbeee8c: add             x1, x1, HEAP, lsl #32
    // 0xbeee90: cmp             w1, NULL
    // 0xbeee94: b.eq            #0xbef568
    // 0xbeee98: tbnz            w1, #4, #0xbef568
    // 0xbeee9c: LoadField: r1 = r0->field_87
    //     0xbeee9c: ldur            w1, [x0, #0x87]
    // 0xbeeea0: DecompressPointer r1
    //     0xbeeea0: add             x1, x1, HEAP, lsl #32
    // 0xbeeea4: cmp             w1, NULL
    // 0xbeeea8: b.eq            #0xbef20c
    // 0xbeeeac: tbnz            w1, #4, #0xbef20c
    // 0xbeeeb0: LoadField: r2 = r0->field_73
    //     0xbeeeb0: ldur            w2, [x0, #0x73]
    // 0xbeeeb4: DecompressPointer r2
    //     0xbeeeb4: add             x2, x2, HEAP, lsl #32
    // 0xbeeeb8: cmp             w2, NULL
    // 0xbeeebc: b.ne            #0xbeeec8
    // 0xbeeec0: r1 = Null
    //     0xbeeec0: mov             x1, NULL
    // 0xbeeec4: b               #0xbeeee0
    // 0xbeeec8: LoadField: r1 = r2->field_b
    //     0xbeeec8: ldur            w1, [x2, #0xb]
    // 0xbeeecc: cbz             w1, #0xbeeed8
    // 0xbeeed0: r5 = false
    //     0xbeeed0: add             x5, NULL, #0x30  ; false
    // 0xbeeed4: b               #0xbeeedc
    // 0xbeeed8: r5 = true
    //     0xbeeed8: add             x5, NULL, #0x20  ; true
    // 0xbeeedc: mov             x1, x5
    // 0xbeeee0: cmp             w1, NULL
    // 0xbeeee4: b.eq            #0xbeeeec
    // 0xbeeee8: tbnz            w1, #4, #0xbef080
    // 0xbeeeec: LoadField: r1 = r0->field_8f
    //     0xbeeeec: ldur            w1, [x0, #0x8f]
    // 0xbeeef0: DecompressPointer r1
    //     0xbeeef0: add             x1, x1, HEAP, lsl #32
    // 0xbeeef4: cmp             w1, NULL
    // 0xbeeef8: b.ne            #0xbeef04
    // 0xbeeefc: r2 = ""
    //     0xbeeefc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeef00: b               #0xbeef08
    // 0xbeef04: mov             x2, x1
    // 0xbeef08: LoadField: r5 = r0->field_6f
    //     0xbeef08: ldur            w5, [x0, #0x6f]
    // 0xbeef0c: DecompressPointer r5
    //     0xbeef0c: add             x5, x5, HEAP, lsl #32
    // 0xbeef10: cmp             w5, NULL
    // 0xbeef14: b.ne            #0xbeef20
    // 0xbeef18: r0 = Null
    //     0xbeef18: mov             x0, NULL
    // 0xbeef1c: b               #0xbeef58
    // 0xbeef20: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xbeef20: ldur            x6, [x3, #0x17]
    // 0xbeef24: LoadField: r0 = r5->field_b
    //     0xbeef24: ldur            w0, [x5, #0xb]
    // 0xbeef28: r1 = LoadInt32Instr(r0)
    //     0xbeef28: sbfx            x1, x0, #1, #0x1f
    // 0xbeef2c: mov             x0, x1
    // 0xbeef30: mov             x1, x6
    // 0xbeef34: cmp             x1, x0
    // 0xbeef38: b.hs            #0xbefdc4
    // 0xbeef3c: LoadField: r0 = r5->field_f
    //     0xbeef3c: ldur            w0, [x5, #0xf]
    // 0xbeef40: DecompressPointer r0
    //     0xbeef40: add             x0, x0, HEAP, lsl #32
    // 0xbeef44: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbeef44: add             x16, x0, x6, lsl #2
    //     0xbeef48: ldur            w1, [x16, #0xf]
    // 0xbeef4c: DecompressPointer r1
    //     0xbeef4c: add             x1, x1, HEAP, lsl #32
    // 0xbeef50: LoadField: r0 = r1->field_f
    //     0xbeef50: ldur            w0, [x1, #0xf]
    // 0xbeef54: DecompressPointer r0
    //     0xbeef54: add             x0, x0, HEAP, lsl #32
    // 0xbeef58: cmp             w0, NULL
    // 0xbeef5c: b.ne            #0xbeef68
    // 0xbeef60: r6 = ""
    //     0xbeef60: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeef64: b               #0xbeef6c
    // 0xbeef68: mov             x6, x0
    // 0xbeef6c: cmp             w5, NULL
    // 0xbeef70: b.ne            #0xbeef7c
    // 0xbeef74: r0 = Null
    //     0xbeef74: mov             x0, NULL
    // 0xbeef78: b               #0xbeefb4
    // 0xbeef7c: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xbeef7c: ldur            x7, [x3, #0x17]
    // 0xbeef80: LoadField: r0 = r5->field_b
    //     0xbeef80: ldur            w0, [x5, #0xb]
    // 0xbeef84: r1 = LoadInt32Instr(r0)
    //     0xbeef84: sbfx            x1, x0, #1, #0x1f
    // 0xbeef88: mov             x0, x1
    // 0xbeef8c: mov             x1, x7
    // 0xbeef90: cmp             x1, x0
    // 0xbeef94: b.hs            #0xbefdc8
    // 0xbeef98: LoadField: r0 = r5->field_f
    //     0xbeef98: ldur            w0, [x5, #0xf]
    // 0xbeef9c: DecompressPointer r0
    //     0xbeef9c: add             x0, x0, HEAP, lsl #32
    // 0xbeefa0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbeefa0: add             x16, x0, x7, lsl #2
    //     0xbeefa4: ldur            w1, [x16, #0xf]
    // 0xbeefa8: DecompressPointer r1
    //     0xbeefa8: add             x1, x1, HEAP, lsl #32
    // 0xbeefac: LoadField: r0 = r1->field_b
    //     0xbeefac: ldur            w0, [x1, #0xb]
    // 0xbeefb0: DecompressPointer r0
    //     0xbeefb0: add             x0, x0, HEAP, lsl #32
    // 0xbeefb4: cmp             w0, NULL
    // 0xbeefb8: b.ne            #0xbeefc4
    // 0xbeefbc: r7 = ""
    //     0xbeefbc: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeefc0: b               #0xbeefc8
    // 0xbeefc4: mov             x7, x0
    // 0xbeefc8: cmp             w5, NULL
    // 0xbeefcc: b.ne            #0xbeefd8
    // 0xbeefd0: r0 = Null
    //     0xbeefd0: mov             x0, NULL
    // 0xbeefd4: b               #0xbef010
    // 0xbeefd8: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xbeefd8: ldur            x8, [x3, #0x17]
    // 0xbeefdc: LoadField: r0 = r5->field_b
    //     0xbeefdc: ldur            w0, [x5, #0xb]
    // 0xbeefe0: r1 = LoadInt32Instr(r0)
    //     0xbeefe0: sbfx            x1, x0, #1, #0x1f
    // 0xbeefe4: mov             x0, x1
    // 0xbeefe8: mov             x1, x8
    // 0xbeefec: cmp             x1, x0
    // 0xbeeff0: b.hs            #0xbefdcc
    // 0xbeeff4: LoadField: r0 = r5->field_f
    //     0xbeeff4: ldur            w0, [x5, #0xf]
    // 0xbeeff8: DecompressPointer r0
    //     0xbeeff8: add             x0, x0, HEAP, lsl #32
    // 0xbeeffc: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbeeffc: add             x16, x0, x8, lsl #2
    //     0xbef000: ldur            w1, [x16, #0xf]
    // 0xbef004: DecompressPointer r1
    //     0xbef004: add             x1, x1, HEAP, lsl #32
    // 0xbef008: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbef008: ldur            w0, [x1, #0x17]
    // 0xbef00c: DecompressPointer r0
    //     0xbef00c: add             x0, x0, HEAP, lsl #32
    // 0xbef010: cmp             w0, NULL
    // 0xbef014: b.ne            #0xbef020
    // 0xbef018: r3 = 0
    //     0xbef018: movz            x3, #0
    // 0xbef01c: b               #0xbef030
    // 0xbef020: r1 = LoadInt32Instr(r0)
    //     0xbef020: sbfx            x1, x0, #1, #0x1f
    //     0xbef024: tbz             w0, #0, #0xbef02c
    //     0xbef028: ldur            x1, [x0, #7]
    // 0xbef02c: mov             x3, x1
    // 0xbef030: LoadField: r5 = r4->field_1f
    //     0xbef030: ldur            w5, [x4, #0x1f]
    // 0xbef034: DecompressPointer r5
    //     0xbef034: add             x5, x5, HEAP, lsl #32
    // 0xbef038: r0 = BoxInt64Instr(r3)
    //     0xbef038: sbfiz           x0, x3, #1, #0x1f
    //     0xbef03c: cmp             x3, x0, asr #1
    //     0xbef040: b.eq            #0xbef04c
    //     0xbef044: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbef048: stur            x3, [x0, #7]
    // 0xbef04c: stp             x2, x5, [SP, #0x28]
    // 0xbef050: r16 = true
    //     0xbef050: add             x16, NULL, #0x20  ; true
    // 0xbef054: stp             x6, x16, [SP, #0x18]
    // 0xbef058: stp             x0, x7, [SP, #8]
    // 0xbef05c: r16 = "add_to_bag"
    //     0xbef05c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xbef060: ldr             x16, [x16, #0xa38]
    // 0xbef064: str             x16, [SP]
    // 0xbef068: mov             x0, x5
    // 0xbef06c: ClosureCall
    //     0xbef06c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xbef070: ldr             x4, [x4, #0x330]
    //     0xbef074: ldur            x2, [x0, #0x1f]
    //     0xbef078: blr             x2
    // 0xbef07c: b               #0xbefd8c
    // 0xbef080: LoadField: r1 = r0->field_8f
    //     0xbef080: ldur            w1, [x0, #0x8f]
    // 0xbef084: DecompressPointer r1
    //     0xbef084: add             x1, x1, HEAP, lsl #32
    // 0xbef088: cmp             w1, NULL
    // 0xbef08c: b.ne            #0xbef098
    // 0xbef090: r5 = ""
    //     0xbef090: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef094: b               #0xbef09c
    // 0xbef098: mov             x5, x1
    // 0xbef09c: cmp             w2, NULL
    // 0xbef0a0: b.ne            #0xbef0ac
    // 0xbef0a4: r0 = Null
    //     0xbef0a4: mov             x0, NULL
    // 0xbef0a8: b               #0xbef0e4
    // 0xbef0ac: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xbef0ac: ldur            x6, [x3, #0x17]
    // 0xbef0b0: LoadField: r0 = r2->field_b
    //     0xbef0b0: ldur            w0, [x2, #0xb]
    // 0xbef0b4: r1 = LoadInt32Instr(r0)
    //     0xbef0b4: sbfx            x1, x0, #1, #0x1f
    // 0xbef0b8: mov             x0, x1
    // 0xbef0bc: mov             x1, x6
    // 0xbef0c0: cmp             x1, x0
    // 0xbef0c4: b.hs            #0xbefdd0
    // 0xbef0c8: LoadField: r0 = r2->field_f
    //     0xbef0c8: ldur            w0, [x2, #0xf]
    // 0xbef0cc: DecompressPointer r0
    //     0xbef0cc: add             x0, x0, HEAP, lsl #32
    // 0xbef0d0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef0d0: add             x16, x0, x6, lsl #2
    //     0xbef0d4: ldur            w1, [x16, #0xf]
    // 0xbef0d8: DecompressPointer r1
    //     0xbef0d8: add             x1, x1, HEAP, lsl #32
    // 0xbef0dc: LoadField: r0 = r1->field_b
    //     0xbef0dc: ldur            w0, [x1, #0xb]
    // 0xbef0e0: DecompressPointer r0
    //     0xbef0e0: add             x0, x0, HEAP, lsl #32
    // 0xbef0e4: cmp             w0, NULL
    // 0xbef0e8: b.ne            #0xbef0f4
    // 0xbef0ec: r6 = ""
    //     0xbef0ec: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef0f0: b               #0xbef0f8
    // 0xbef0f4: mov             x6, x0
    // 0xbef0f8: cmp             w2, NULL
    // 0xbef0fc: b.ne            #0xbef108
    // 0xbef100: r0 = Null
    //     0xbef100: mov             x0, NULL
    // 0xbef104: b               #0xbef140
    // 0xbef108: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xbef108: ldur            x7, [x3, #0x17]
    // 0xbef10c: LoadField: r0 = r2->field_b
    //     0xbef10c: ldur            w0, [x2, #0xb]
    // 0xbef110: r1 = LoadInt32Instr(r0)
    //     0xbef110: sbfx            x1, x0, #1, #0x1f
    // 0xbef114: mov             x0, x1
    // 0xbef118: mov             x1, x7
    // 0xbef11c: cmp             x1, x0
    // 0xbef120: b.hs            #0xbefdd4
    // 0xbef124: LoadField: r0 = r2->field_f
    //     0xbef124: ldur            w0, [x2, #0xf]
    // 0xbef128: DecompressPointer r0
    //     0xbef128: add             x0, x0, HEAP, lsl #32
    // 0xbef12c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbef12c: add             x16, x0, x7, lsl #2
    //     0xbef130: ldur            w1, [x16, #0xf]
    // 0xbef134: DecompressPointer r1
    //     0xbef134: add             x1, x1, HEAP, lsl #32
    // 0xbef138: LoadField: r0 = r1->field_7
    //     0xbef138: ldur            w0, [x1, #7]
    // 0xbef13c: DecompressPointer r0
    //     0xbef13c: add             x0, x0, HEAP, lsl #32
    // 0xbef140: cmp             w0, NULL
    // 0xbef144: b.ne            #0xbef150
    // 0xbef148: r7 = ""
    //     0xbef148: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef14c: b               #0xbef154
    // 0xbef150: mov             x7, x0
    // 0xbef154: cmp             w2, NULL
    // 0xbef158: b.ne            #0xbef164
    // 0xbef15c: r0 = Null
    //     0xbef15c: mov             x0, NULL
    // 0xbef160: b               #0xbef19c
    // 0xbef164: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xbef164: ldur            x8, [x3, #0x17]
    // 0xbef168: LoadField: r0 = r2->field_b
    //     0xbef168: ldur            w0, [x2, #0xb]
    // 0xbef16c: r1 = LoadInt32Instr(r0)
    //     0xbef16c: sbfx            x1, x0, #1, #0x1f
    // 0xbef170: mov             x0, x1
    // 0xbef174: mov             x1, x8
    // 0xbef178: cmp             x1, x0
    // 0xbef17c: b.hs            #0xbefdd8
    // 0xbef180: LoadField: r0 = r2->field_f
    //     0xbef180: ldur            w0, [x2, #0xf]
    // 0xbef184: DecompressPointer r0
    //     0xbef184: add             x0, x0, HEAP, lsl #32
    // 0xbef188: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbef188: add             x16, x0, x8, lsl #2
    //     0xbef18c: ldur            w1, [x16, #0xf]
    // 0xbef190: DecompressPointer r1
    //     0xbef190: add             x1, x1, HEAP, lsl #32
    // 0xbef194: LoadField: r0 = r1->field_1b
    //     0xbef194: ldur            w0, [x1, #0x1b]
    // 0xbef198: DecompressPointer r0
    //     0xbef198: add             x0, x0, HEAP, lsl #32
    // 0xbef19c: cmp             w0, NULL
    // 0xbef1a0: b.ne            #0xbef1ac
    // 0xbef1a4: r2 = 0
    //     0xbef1a4: movz            x2, #0
    // 0xbef1a8: b               #0xbef1bc
    // 0xbef1ac: r1 = LoadInt32Instr(r0)
    //     0xbef1ac: sbfx            x1, x0, #1, #0x1f
    //     0xbef1b0: tbz             w0, #0, #0xbef1b8
    //     0xbef1b4: ldur            x1, [x0, #7]
    // 0xbef1b8: mov             x2, x1
    // 0xbef1bc: LoadField: r3 = r4->field_1f
    //     0xbef1bc: ldur            w3, [x4, #0x1f]
    // 0xbef1c0: DecompressPointer r3
    //     0xbef1c0: add             x3, x3, HEAP, lsl #32
    // 0xbef1c4: r0 = BoxInt64Instr(r2)
    //     0xbef1c4: sbfiz           x0, x2, #1, #0x1f
    //     0xbef1c8: cmp             x2, x0, asr #1
    //     0xbef1cc: b.eq            #0xbef1d8
    //     0xbef1d0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbef1d4: stur            x2, [x0, #7]
    // 0xbef1d8: stp             x5, x3, [SP, #0x28]
    // 0xbef1dc: r16 = true
    //     0xbef1dc: add             x16, NULL, #0x20  ; true
    // 0xbef1e0: stp             x6, x16, [SP, #0x18]
    // 0xbef1e4: stp             x0, x7, [SP, #8]
    // 0xbef1e8: r16 = "add_to_bag"
    //     0xbef1e8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xbef1ec: ldr             x16, [x16, #0xa38]
    // 0xbef1f0: str             x16, [SP]
    // 0xbef1f4: mov             x0, x3
    // 0xbef1f8: ClosureCall
    //     0xbef1f8: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xbef1fc: ldr             x4, [x4, #0x330]
    //     0xbef200: ldur            x2, [x0, #0x1f]
    //     0xbef204: blr             x2
    // 0xbef208: b               #0xbefd8c
    // 0xbef20c: LoadField: r2 = r0->field_73
    //     0xbef20c: ldur            w2, [x0, #0x73]
    // 0xbef210: DecompressPointer r2
    //     0xbef210: add             x2, x2, HEAP, lsl #32
    // 0xbef214: cmp             w2, NULL
    // 0xbef218: b.ne            #0xbef224
    // 0xbef21c: r1 = Null
    //     0xbef21c: mov             x1, NULL
    // 0xbef220: b               #0xbef23c
    // 0xbef224: LoadField: r1 = r2->field_b
    //     0xbef224: ldur            w1, [x2, #0xb]
    // 0xbef228: cbz             w1, #0xbef234
    // 0xbef22c: r5 = false
    //     0xbef22c: add             x5, NULL, #0x30  ; false
    // 0xbef230: b               #0xbef238
    // 0xbef234: r5 = true
    //     0xbef234: add             x5, NULL, #0x20  ; true
    // 0xbef238: mov             x1, x5
    // 0xbef23c: cmp             w1, NULL
    // 0xbef240: b.eq            #0xbef248
    // 0xbef244: tbnz            w1, #4, #0xbef3dc
    // 0xbef248: LoadField: r1 = r0->field_8f
    //     0xbef248: ldur            w1, [x0, #0x8f]
    // 0xbef24c: DecompressPointer r1
    //     0xbef24c: add             x1, x1, HEAP, lsl #32
    // 0xbef250: cmp             w1, NULL
    // 0xbef254: b.ne            #0xbef260
    // 0xbef258: r2 = ""
    //     0xbef258: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef25c: b               #0xbef264
    // 0xbef260: mov             x2, x1
    // 0xbef264: LoadField: r5 = r0->field_6f
    //     0xbef264: ldur            w5, [x0, #0x6f]
    // 0xbef268: DecompressPointer r5
    //     0xbef268: add             x5, x5, HEAP, lsl #32
    // 0xbef26c: cmp             w5, NULL
    // 0xbef270: b.ne            #0xbef27c
    // 0xbef274: r0 = Null
    //     0xbef274: mov             x0, NULL
    // 0xbef278: b               #0xbef2b4
    // 0xbef27c: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xbef27c: ldur            x6, [x3, #0x17]
    // 0xbef280: LoadField: r0 = r5->field_b
    //     0xbef280: ldur            w0, [x5, #0xb]
    // 0xbef284: r1 = LoadInt32Instr(r0)
    //     0xbef284: sbfx            x1, x0, #1, #0x1f
    // 0xbef288: mov             x0, x1
    // 0xbef28c: mov             x1, x6
    // 0xbef290: cmp             x1, x0
    // 0xbef294: b.hs            #0xbefddc
    // 0xbef298: LoadField: r0 = r5->field_f
    //     0xbef298: ldur            w0, [x5, #0xf]
    // 0xbef29c: DecompressPointer r0
    //     0xbef29c: add             x0, x0, HEAP, lsl #32
    // 0xbef2a0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef2a0: add             x16, x0, x6, lsl #2
    //     0xbef2a4: ldur            w1, [x16, #0xf]
    // 0xbef2a8: DecompressPointer r1
    //     0xbef2a8: add             x1, x1, HEAP, lsl #32
    // 0xbef2ac: LoadField: r0 = r1->field_f
    //     0xbef2ac: ldur            w0, [x1, #0xf]
    // 0xbef2b0: DecompressPointer r0
    //     0xbef2b0: add             x0, x0, HEAP, lsl #32
    // 0xbef2b4: cmp             w0, NULL
    // 0xbef2b8: b.ne            #0xbef2c4
    // 0xbef2bc: r6 = ""
    //     0xbef2bc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef2c0: b               #0xbef2c8
    // 0xbef2c4: mov             x6, x0
    // 0xbef2c8: cmp             w5, NULL
    // 0xbef2cc: b.ne            #0xbef2d8
    // 0xbef2d0: r0 = Null
    //     0xbef2d0: mov             x0, NULL
    // 0xbef2d4: b               #0xbef310
    // 0xbef2d8: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xbef2d8: ldur            x7, [x3, #0x17]
    // 0xbef2dc: LoadField: r0 = r5->field_b
    //     0xbef2dc: ldur            w0, [x5, #0xb]
    // 0xbef2e0: r1 = LoadInt32Instr(r0)
    //     0xbef2e0: sbfx            x1, x0, #1, #0x1f
    // 0xbef2e4: mov             x0, x1
    // 0xbef2e8: mov             x1, x7
    // 0xbef2ec: cmp             x1, x0
    // 0xbef2f0: b.hs            #0xbefde0
    // 0xbef2f4: LoadField: r0 = r5->field_f
    //     0xbef2f4: ldur            w0, [x5, #0xf]
    // 0xbef2f8: DecompressPointer r0
    //     0xbef2f8: add             x0, x0, HEAP, lsl #32
    // 0xbef2fc: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbef2fc: add             x16, x0, x7, lsl #2
    //     0xbef300: ldur            w1, [x16, #0xf]
    // 0xbef304: DecompressPointer r1
    //     0xbef304: add             x1, x1, HEAP, lsl #32
    // 0xbef308: LoadField: r0 = r1->field_b
    //     0xbef308: ldur            w0, [x1, #0xb]
    // 0xbef30c: DecompressPointer r0
    //     0xbef30c: add             x0, x0, HEAP, lsl #32
    // 0xbef310: cmp             w0, NULL
    // 0xbef314: b.ne            #0xbef320
    // 0xbef318: r7 = ""
    //     0xbef318: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef31c: b               #0xbef324
    // 0xbef320: mov             x7, x0
    // 0xbef324: cmp             w5, NULL
    // 0xbef328: b.ne            #0xbef334
    // 0xbef32c: r0 = Null
    //     0xbef32c: mov             x0, NULL
    // 0xbef330: b               #0xbef36c
    // 0xbef334: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xbef334: ldur            x8, [x3, #0x17]
    // 0xbef338: LoadField: r0 = r5->field_b
    //     0xbef338: ldur            w0, [x5, #0xb]
    // 0xbef33c: r1 = LoadInt32Instr(r0)
    //     0xbef33c: sbfx            x1, x0, #1, #0x1f
    // 0xbef340: mov             x0, x1
    // 0xbef344: mov             x1, x8
    // 0xbef348: cmp             x1, x0
    // 0xbef34c: b.hs            #0xbefde4
    // 0xbef350: LoadField: r0 = r5->field_f
    //     0xbef350: ldur            w0, [x5, #0xf]
    // 0xbef354: DecompressPointer r0
    //     0xbef354: add             x0, x0, HEAP, lsl #32
    // 0xbef358: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbef358: add             x16, x0, x8, lsl #2
    //     0xbef35c: ldur            w1, [x16, #0xf]
    // 0xbef360: DecompressPointer r1
    //     0xbef360: add             x1, x1, HEAP, lsl #32
    // 0xbef364: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbef364: ldur            w0, [x1, #0x17]
    // 0xbef368: DecompressPointer r0
    //     0xbef368: add             x0, x0, HEAP, lsl #32
    // 0xbef36c: cmp             w0, NULL
    // 0xbef370: b.ne            #0xbef37c
    // 0xbef374: r3 = 0
    //     0xbef374: movz            x3, #0
    // 0xbef378: b               #0xbef38c
    // 0xbef37c: r1 = LoadInt32Instr(r0)
    //     0xbef37c: sbfx            x1, x0, #1, #0x1f
    //     0xbef380: tbz             w0, #0, #0xbef388
    //     0xbef384: ldur            x1, [x0, #7]
    // 0xbef388: mov             x3, x1
    // 0xbef38c: LoadField: r5 = r4->field_1f
    //     0xbef38c: ldur            w5, [x4, #0x1f]
    // 0xbef390: DecompressPointer r5
    //     0xbef390: add             x5, x5, HEAP, lsl #32
    // 0xbef394: r0 = BoxInt64Instr(r3)
    //     0xbef394: sbfiz           x0, x3, #1, #0x1f
    //     0xbef398: cmp             x3, x0, asr #1
    //     0xbef39c: b.eq            #0xbef3a8
    //     0xbef3a0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbef3a4: stur            x3, [x0, #7]
    // 0xbef3a8: stp             x2, x5, [SP, #0x28]
    // 0xbef3ac: r16 = true
    //     0xbef3ac: add             x16, NULL, #0x20  ; true
    // 0xbef3b0: stp             x6, x16, [SP, #0x18]
    // 0xbef3b4: stp             x0, x7, [SP, #8]
    // 0xbef3b8: r16 = "add_to_bag"
    //     0xbef3b8: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xbef3bc: ldr             x16, [x16, #0xa38]
    // 0xbef3c0: str             x16, [SP]
    // 0xbef3c4: mov             x0, x5
    // 0xbef3c8: ClosureCall
    //     0xbef3c8: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xbef3cc: ldr             x4, [x4, #0x330]
    //     0xbef3d0: ldur            x2, [x0, #0x1f]
    //     0xbef3d4: blr             x2
    // 0xbef3d8: b               #0xbefd8c
    // 0xbef3dc: LoadField: r1 = r0->field_8f
    //     0xbef3dc: ldur            w1, [x0, #0x8f]
    // 0xbef3e0: DecompressPointer r1
    //     0xbef3e0: add             x1, x1, HEAP, lsl #32
    // 0xbef3e4: cmp             w1, NULL
    // 0xbef3e8: b.ne            #0xbef3f4
    // 0xbef3ec: r5 = ""
    //     0xbef3ec: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef3f0: b               #0xbef3f8
    // 0xbef3f4: mov             x5, x1
    // 0xbef3f8: cmp             w2, NULL
    // 0xbef3fc: b.ne            #0xbef408
    // 0xbef400: r0 = Null
    //     0xbef400: mov             x0, NULL
    // 0xbef404: b               #0xbef440
    // 0xbef408: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xbef408: ldur            x6, [x3, #0x17]
    // 0xbef40c: LoadField: r0 = r2->field_b
    //     0xbef40c: ldur            w0, [x2, #0xb]
    // 0xbef410: r1 = LoadInt32Instr(r0)
    //     0xbef410: sbfx            x1, x0, #1, #0x1f
    // 0xbef414: mov             x0, x1
    // 0xbef418: mov             x1, x6
    // 0xbef41c: cmp             x1, x0
    // 0xbef420: b.hs            #0xbefde8
    // 0xbef424: LoadField: r0 = r2->field_f
    //     0xbef424: ldur            w0, [x2, #0xf]
    // 0xbef428: DecompressPointer r0
    //     0xbef428: add             x0, x0, HEAP, lsl #32
    // 0xbef42c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef42c: add             x16, x0, x6, lsl #2
    //     0xbef430: ldur            w1, [x16, #0xf]
    // 0xbef434: DecompressPointer r1
    //     0xbef434: add             x1, x1, HEAP, lsl #32
    // 0xbef438: LoadField: r0 = r1->field_b
    //     0xbef438: ldur            w0, [x1, #0xb]
    // 0xbef43c: DecompressPointer r0
    //     0xbef43c: add             x0, x0, HEAP, lsl #32
    // 0xbef440: cmp             w0, NULL
    // 0xbef444: b.ne            #0xbef450
    // 0xbef448: r6 = ""
    //     0xbef448: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef44c: b               #0xbef454
    // 0xbef450: mov             x6, x0
    // 0xbef454: cmp             w2, NULL
    // 0xbef458: b.ne            #0xbef464
    // 0xbef45c: r0 = Null
    //     0xbef45c: mov             x0, NULL
    // 0xbef460: b               #0xbef49c
    // 0xbef464: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xbef464: ldur            x7, [x3, #0x17]
    // 0xbef468: LoadField: r0 = r2->field_b
    //     0xbef468: ldur            w0, [x2, #0xb]
    // 0xbef46c: r1 = LoadInt32Instr(r0)
    //     0xbef46c: sbfx            x1, x0, #1, #0x1f
    // 0xbef470: mov             x0, x1
    // 0xbef474: mov             x1, x7
    // 0xbef478: cmp             x1, x0
    // 0xbef47c: b.hs            #0xbefdec
    // 0xbef480: LoadField: r0 = r2->field_f
    //     0xbef480: ldur            w0, [x2, #0xf]
    // 0xbef484: DecompressPointer r0
    //     0xbef484: add             x0, x0, HEAP, lsl #32
    // 0xbef488: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbef488: add             x16, x0, x7, lsl #2
    //     0xbef48c: ldur            w1, [x16, #0xf]
    // 0xbef490: DecompressPointer r1
    //     0xbef490: add             x1, x1, HEAP, lsl #32
    // 0xbef494: LoadField: r0 = r1->field_7
    //     0xbef494: ldur            w0, [x1, #7]
    // 0xbef498: DecompressPointer r0
    //     0xbef498: add             x0, x0, HEAP, lsl #32
    // 0xbef49c: cmp             w0, NULL
    // 0xbef4a0: b.ne            #0xbef4ac
    // 0xbef4a4: r7 = ""
    //     0xbef4a4: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef4a8: b               #0xbef4b0
    // 0xbef4ac: mov             x7, x0
    // 0xbef4b0: cmp             w2, NULL
    // 0xbef4b4: b.ne            #0xbef4c0
    // 0xbef4b8: r0 = Null
    //     0xbef4b8: mov             x0, NULL
    // 0xbef4bc: b               #0xbef4f8
    // 0xbef4c0: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xbef4c0: ldur            x8, [x3, #0x17]
    // 0xbef4c4: LoadField: r0 = r2->field_b
    //     0xbef4c4: ldur            w0, [x2, #0xb]
    // 0xbef4c8: r1 = LoadInt32Instr(r0)
    //     0xbef4c8: sbfx            x1, x0, #1, #0x1f
    // 0xbef4cc: mov             x0, x1
    // 0xbef4d0: mov             x1, x8
    // 0xbef4d4: cmp             x1, x0
    // 0xbef4d8: b.hs            #0xbefdf0
    // 0xbef4dc: LoadField: r0 = r2->field_f
    //     0xbef4dc: ldur            w0, [x2, #0xf]
    // 0xbef4e0: DecompressPointer r0
    //     0xbef4e0: add             x0, x0, HEAP, lsl #32
    // 0xbef4e4: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xbef4e4: add             x16, x0, x8, lsl #2
    //     0xbef4e8: ldur            w1, [x16, #0xf]
    // 0xbef4ec: DecompressPointer r1
    //     0xbef4ec: add             x1, x1, HEAP, lsl #32
    // 0xbef4f0: LoadField: r0 = r1->field_1b
    //     0xbef4f0: ldur            w0, [x1, #0x1b]
    // 0xbef4f4: DecompressPointer r0
    //     0xbef4f4: add             x0, x0, HEAP, lsl #32
    // 0xbef4f8: cmp             w0, NULL
    // 0xbef4fc: b.ne            #0xbef508
    // 0xbef500: r2 = 0
    //     0xbef500: movz            x2, #0
    // 0xbef504: b               #0xbef518
    // 0xbef508: r1 = LoadInt32Instr(r0)
    //     0xbef508: sbfx            x1, x0, #1, #0x1f
    //     0xbef50c: tbz             w0, #0, #0xbef514
    //     0xbef510: ldur            x1, [x0, #7]
    // 0xbef514: mov             x2, x1
    // 0xbef518: LoadField: r3 = r4->field_1f
    //     0xbef518: ldur            w3, [x4, #0x1f]
    // 0xbef51c: DecompressPointer r3
    //     0xbef51c: add             x3, x3, HEAP, lsl #32
    // 0xbef520: r0 = BoxInt64Instr(r2)
    //     0xbef520: sbfiz           x0, x2, #1, #0x1f
    //     0xbef524: cmp             x2, x0, asr #1
    //     0xbef528: b.eq            #0xbef534
    //     0xbef52c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbef530: stur            x2, [x0, #7]
    // 0xbef534: stp             x5, x3, [SP, #0x28]
    // 0xbef538: r16 = true
    //     0xbef538: add             x16, NULL, #0x20  ; true
    // 0xbef53c: stp             x6, x16, [SP, #0x18]
    // 0xbef540: stp             x0, x7, [SP, #8]
    // 0xbef544: r16 = "add_to_bag"
    //     0xbef544: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xbef548: ldr             x16, [x16, #0xa38]
    // 0xbef54c: str             x16, [SP]
    // 0xbef550: mov             x0, x3
    // 0xbef554: ClosureCall
    //     0xbef554: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xbef558: ldr             x4, [x4, #0x330]
    //     0xbef55c: ldur            x2, [x0, #0x1f]
    //     0xbef560: blr             x2
    // 0xbef564: b               #0xbefd8c
    // 0xbef568: LoadField: r5 = r0->field_73
    //     0xbef568: ldur            w5, [x0, #0x73]
    // 0xbef56c: DecompressPointer r5
    //     0xbef56c: add             x5, x5, HEAP, lsl #32
    // 0xbef570: cmp             w5, NULL
    // 0xbef574: b.ne            #0xbef580
    // 0xbef578: r1 = Null
    //     0xbef578: mov             x1, NULL
    // 0xbef57c: b               #0xbef598
    // 0xbef580: LoadField: r1 = r5->field_b
    //     0xbef580: ldur            w1, [x5, #0xb]
    // 0xbef584: cbz             w1, #0xbef590
    // 0xbef588: r6 = false
    //     0xbef588: add             x6, NULL, #0x30  ; false
    // 0xbef58c: b               #0xbef594
    // 0xbef590: r6 = true
    //     0xbef590: add             x6, NULL, #0x20  ; true
    // 0xbef594: mov             x1, x6
    // 0xbef598: cmp             w1, NULL
    // 0xbef59c: b.eq            #0xbef5a4
    // 0xbef5a0: tbnz            w1, #4, #0xbef990
    // 0xbef5a4: LoadField: r1 = r4->field_27
    //     0xbef5a4: ldur            w1, [x4, #0x27]
    // 0xbef5a8: DecompressPointer r1
    //     0xbef5a8: add             x1, x1, HEAP, lsl #32
    // 0xbef5ac: tbnz            w1, #4, #0xbef88c
    // 0xbef5b0: LoadField: r5 = r0->field_6f
    //     0xbef5b0: ldur            w5, [x0, #0x6f]
    // 0xbef5b4: DecompressPointer r5
    //     0xbef5b4: add             x5, x5, HEAP, lsl #32
    // 0xbef5b8: cmp             w5, NULL
    // 0xbef5bc: b.ne            #0xbef5c8
    // 0xbef5c0: r0 = Null
    //     0xbef5c0: mov             x0, NULL
    // 0xbef5c4: b               #0xbef600
    // 0xbef5c8: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xbef5c8: ldur            x6, [x3, #0x17]
    // 0xbef5cc: LoadField: r0 = r5->field_b
    //     0xbef5cc: ldur            w0, [x5, #0xb]
    // 0xbef5d0: r1 = LoadInt32Instr(r0)
    //     0xbef5d0: sbfx            x1, x0, #1, #0x1f
    // 0xbef5d4: mov             x0, x1
    // 0xbef5d8: mov             x1, x6
    // 0xbef5dc: cmp             x1, x0
    // 0xbef5e0: b.hs            #0xbefdf4
    // 0xbef5e4: LoadField: r0 = r5->field_f
    //     0xbef5e4: ldur            w0, [x5, #0xf]
    // 0xbef5e8: DecompressPointer r0
    //     0xbef5e8: add             x0, x0, HEAP, lsl #32
    // 0xbef5ec: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef5ec: add             x16, x0, x6, lsl #2
    //     0xbef5f0: ldur            w1, [x16, #0xf]
    // 0xbef5f4: DecompressPointer r1
    //     0xbef5f4: add             x1, x1, HEAP, lsl #32
    // 0xbef5f8: LoadField: r0 = r1->field_b
    //     0xbef5f8: ldur            w0, [x1, #0xb]
    // 0xbef5fc: DecompressPointer r0
    //     0xbef5fc: add             x0, x0, HEAP, lsl #32
    // 0xbef600: cmp             w0, NULL
    // 0xbef604: b.ne            #0xbef610
    // 0xbef608: r6 = ""
    //     0xbef608: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef60c: b               #0xbef614
    // 0xbef610: mov             x6, x0
    // 0xbef614: stur            x6, [fp, #-0x10]
    // 0xbef618: cmp             w5, NULL
    // 0xbef61c: b.ne            #0xbef628
    // 0xbef620: r0 = Null
    //     0xbef620: mov             x0, NULL
    // 0xbef624: b               #0xbef678
    // 0xbef628: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xbef628: ldur            x7, [x3, #0x17]
    // 0xbef62c: LoadField: r0 = r5->field_b
    //     0xbef62c: ldur            w0, [x5, #0xb]
    // 0xbef630: r1 = LoadInt32Instr(r0)
    //     0xbef630: sbfx            x1, x0, #1, #0x1f
    // 0xbef634: mov             x0, x1
    // 0xbef638: mov             x1, x7
    // 0xbef63c: cmp             x1, x0
    // 0xbef640: b.hs            #0xbefdf8
    // 0xbef644: LoadField: r0 = r5->field_f
    //     0xbef644: ldur            w0, [x5, #0xf]
    // 0xbef648: DecompressPointer r0
    //     0xbef648: add             x0, x0, HEAP, lsl #32
    // 0xbef64c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbef64c: add             x16, x0, x7, lsl #2
    //     0xbef650: ldur            w1, [x16, #0xf]
    // 0xbef654: DecompressPointer r1
    //     0xbef654: add             x1, x1, HEAP, lsl #32
    // 0xbef658: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbef658: ldur            w0, [x1, #0x17]
    // 0xbef65c: DecompressPointer r0
    //     0xbef65c: add             x0, x0, HEAP, lsl #32
    // 0xbef660: cmp             w0, NULL
    // 0xbef664: b.ne            #0xbef670
    // 0xbef668: r0 = Null
    //     0xbef668: mov             x0, NULL
    // 0xbef66c: b               #0xbef678
    // 0xbef670: stp             x0, NULL, [SP]
    // 0xbef674: r0 = _Double.fromInteger()
    //     0xbef674: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbef678: cmp             w0, NULL
    // 0xbef67c: b.ne            #0xbef688
    // 0xbef680: d0 = 0.000000
    //     0xbef680: eor             v0.16b, v0.16b, v0.16b
    // 0xbef684: b               #0xbef68c
    // 0xbef688: LoadField: d0 = r0->field_7
    //     0xbef688: ldur            d0, [x0, #7]
    // 0xbef68c: ldur            x0, [fp, #-8]
    // 0xbef690: stur            d0, [fp, #-0x48]
    // 0xbef694: r1 = Null
    //     0xbef694: mov             x1, NULL
    // 0xbef698: r2 = 12
    //     0xbef698: movz            x2, #0xc
    // 0xbef69c: r0 = AllocateArray()
    //     0xbef69c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbef6a0: mov             x2, x0
    // 0xbef6a4: stur            x2, [fp, #-0x20]
    // 0xbef6a8: r16 = "id"
    //     0xbef6a8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0xbef6ac: ldr             x16, [x16, #0x400]
    // 0xbef6b0: StoreField: r2->field_f = r16
    //     0xbef6b0: stur            w16, [x2, #0xf]
    // 0xbef6b4: ldur            x3, [fp, #-8]
    // 0xbef6b8: LoadField: r4 = r3->field_f
    //     0xbef6b8: ldur            w4, [x3, #0xf]
    // 0xbef6bc: DecompressPointer r4
    //     0xbef6bc: add             x4, x4, HEAP, lsl #32
    // 0xbef6c0: LoadField: r0 = r4->field_b
    //     0xbef6c0: ldur            w0, [x4, #0xb]
    // 0xbef6c4: DecompressPointer r0
    //     0xbef6c4: add             x0, x0, HEAP, lsl #32
    // 0xbef6c8: cmp             w0, NULL
    // 0xbef6cc: b.eq            #0xbefdfc
    // 0xbef6d0: LoadField: r1 = r0->field_b
    //     0xbef6d0: ldur            w1, [x0, #0xb]
    // 0xbef6d4: DecompressPointer r1
    //     0xbef6d4: add             x1, x1, HEAP, lsl #32
    // 0xbef6d8: LoadField: r5 = r1->field_6f
    //     0xbef6d8: ldur            w5, [x1, #0x6f]
    // 0xbef6dc: DecompressPointer r5
    //     0xbef6dc: add             x5, x5, HEAP, lsl #32
    // 0xbef6e0: cmp             w5, NULL
    // 0xbef6e4: b.ne            #0xbef6f0
    // 0xbef6e8: r0 = Null
    //     0xbef6e8: mov             x0, NULL
    // 0xbef6ec: b               #0xbef728
    // 0xbef6f0: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xbef6f0: ldur            x6, [x4, #0x17]
    // 0xbef6f4: LoadField: r0 = r5->field_b
    //     0xbef6f4: ldur            w0, [x5, #0xb]
    // 0xbef6f8: r1 = LoadInt32Instr(r0)
    //     0xbef6f8: sbfx            x1, x0, #1, #0x1f
    // 0xbef6fc: mov             x0, x1
    // 0xbef700: mov             x1, x6
    // 0xbef704: cmp             x1, x0
    // 0xbef708: b.hs            #0xbefe00
    // 0xbef70c: LoadField: r0 = r5->field_f
    //     0xbef70c: ldur            w0, [x5, #0xf]
    // 0xbef710: DecompressPointer r0
    //     0xbef710: add             x0, x0, HEAP, lsl #32
    // 0xbef714: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef714: add             x16, x0, x6, lsl #2
    //     0xbef718: ldur            w1, [x16, #0xf]
    // 0xbef71c: DecompressPointer r1
    //     0xbef71c: add             x1, x1, HEAP, lsl #32
    // 0xbef720: LoadField: r0 = r1->field_b
    //     0xbef720: ldur            w0, [x1, #0xb]
    // 0xbef724: DecompressPointer r0
    //     0xbef724: add             x0, x0, HEAP, lsl #32
    // 0xbef728: cmp             w0, NULL
    // 0xbef72c: b.ne            #0xbef734
    // 0xbef730: r0 = ""
    //     0xbef730: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef734: StoreField: r2->field_13 = r0
    //     0xbef734: stur            w0, [x2, #0x13]
    // 0xbef738: r16 = "quantity"
    //     0xbef738: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbef73c: ldr             x16, [x16, #0x428]
    // 0xbef740: ArrayStore: r2[0] = r16  ; List_4
    //     0xbef740: stur            w16, [x2, #0x17]
    // 0xbef744: r16 = 2
    //     0xbef744: movz            x16, #0x2
    // 0xbef748: StoreField: r2->field_1b = r16
    //     0xbef748: stur            w16, [x2, #0x1b]
    // 0xbef74c: r16 = "item_price"
    //     0xbef74c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0xbef750: ldr             x16, [x16, #0x498]
    // 0xbef754: StoreField: r2->field_1f = r16
    //     0xbef754: stur            w16, [x2, #0x1f]
    // 0xbef758: cmp             w5, NULL
    // 0xbef75c: b.ne            #0xbef768
    // 0xbef760: r0 = Null
    //     0xbef760: mov             x0, NULL
    // 0xbef764: b               #0xbef7b8
    // 0xbef768: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xbef768: ldur            x6, [x4, #0x17]
    // 0xbef76c: LoadField: r0 = r5->field_b
    //     0xbef76c: ldur            w0, [x5, #0xb]
    // 0xbef770: r1 = LoadInt32Instr(r0)
    //     0xbef770: sbfx            x1, x0, #1, #0x1f
    // 0xbef774: mov             x0, x1
    // 0xbef778: mov             x1, x6
    // 0xbef77c: cmp             x1, x0
    // 0xbef780: b.hs            #0xbefe04
    // 0xbef784: LoadField: r0 = r5->field_f
    //     0xbef784: ldur            w0, [x5, #0xf]
    // 0xbef788: DecompressPointer r0
    //     0xbef788: add             x0, x0, HEAP, lsl #32
    // 0xbef78c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef78c: add             x16, x0, x6, lsl #2
    //     0xbef790: ldur            w1, [x16, #0xf]
    // 0xbef794: DecompressPointer r1
    //     0xbef794: add             x1, x1, HEAP, lsl #32
    // 0xbef798: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbef798: ldur            w0, [x1, #0x17]
    // 0xbef79c: DecompressPointer r0
    //     0xbef79c: add             x0, x0, HEAP, lsl #32
    // 0xbef7a0: cmp             w0, NULL
    // 0xbef7a4: b.ne            #0xbef7b0
    // 0xbef7a8: r0 = Null
    //     0xbef7a8: mov             x0, NULL
    // 0xbef7ac: b               #0xbef7b8
    // 0xbef7b0: stp             x0, NULL, [SP]
    // 0xbef7b4: r0 = _Double.fromInteger()
    //     0xbef7b4: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbef7b8: cmp             w0, NULL
    // 0xbef7bc: b.ne            #0xbef7c8
    // 0xbef7c0: d1 = 0.000000
    //     0xbef7c0: eor             v1.16b, v1.16b, v1.16b
    // 0xbef7c4: b               #0xbef7d0
    // 0xbef7c8: LoadField: d0 = r0->field_7
    //     0xbef7c8: ldur            d0, [x0, #7]
    // 0xbef7cc: mov             v1.16b, v0.16b
    // 0xbef7d0: ldur            d0, [fp, #-0x48]
    // 0xbef7d4: ldur            x2, [fp, #-0x18]
    // 0xbef7d8: r0 = inline_Allocate_Double()
    //     0xbef7d8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbef7dc: add             x0, x0, #0x10
    //     0xbef7e0: cmp             x1, x0
    //     0xbef7e4: b.ls            #0xbefe08
    //     0xbef7e8: str             x0, [THR, #0x50]  ; THR::top
    //     0xbef7ec: sub             x0, x0, #0xf
    //     0xbef7f0: movz            x1, #0xe15c
    //     0xbef7f4: movk            x1, #0x3, lsl #16
    //     0xbef7f8: stur            x1, [x0, #-1]
    // 0xbef7fc: StoreField: r0->field_7 = d1
    //     0xbef7fc: stur            d1, [x0, #7]
    // 0xbef800: ldur            x1, [fp, #-0x20]
    // 0xbef804: ArrayStore: r1[5] = r0  ; List_4
    //     0xbef804: add             x25, x1, #0x23
    //     0xbef808: str             w0, [x25]
    //     0xbef80c: tbz             w0, #0, #0xbef828
    //     0xbef810: ldurb           w16, [x1, #-1]
    //     0xbef814: ldurb           w17, [x0, #-1]
    //     0xbef818: and             x16, x17, x16, lsr #2
    //     0xbef81c: tst             x16, HEAP, lsr #32
    //     0xbef820: b.eq            #0xbef828
    //     0xbef824: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbef828: r16 = <String, dynamic>
    //     0xbef828: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xbef82c: ldur            lr, [fp, #-0x20]
    // 0xbef830: stp             lr, x16, [SP]
    // 0xbef834: r0 = Map._fromLiteral()
    //     0xbef834: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbef838: ldur            x2, [fp, #-0x18]
    // 0xbef83c: LoadField: r1 = r2->field_23
    //     0xbef83c: ldur            w1, [x2, #0x23]
    // 0xbef840: DecompressPointer r1
    //     0xbef840: add             x1, x1, HEAP, lsl #32
    // 0xbef844: ldur            d0, [fp, #-0x48]
    // 0xbef848: r2 = inline_Allocate_Double()
    //     0xbef848: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xbef84c: add             x2, x2, #0x10
    //     0xbef850: cmp             x3, x2
    //     0xbef854: b.ls            #0xbefe20
    //     0xbef858: str             x2, [THR, #0x50]  ; THR::top
    //     0xbef85c: sub             x2, x2, #0xf
    //     0xbef860: movz            x3, #0xe15c
    //     0xbef864: movk            x3, #0x3, lsl #16
    //     0xbef868: stur            x3, [x2, #-1]
    // 0xbef86c: StoreField: r2->field_7 = d0
    //     0xbef86c: stur            d0, [x2, #7]
    // 0xbef870: ldur            x16, [fp, #-0x10]
    // 0xbef874: stp             x16, x1, [SP, #0x10]
    // 0xbef878: stp             x0, x2, [SP]
    // 0xbef87c: mov             x0, x1
    // 0xbef880: ClosureCall
    //     0xbef880: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbef884: ldur            x2, [x0, #0x1f]
    //     0xbef888: blr             x2
    // 0xbef88c: ldur            x4, [fp, #-8]
    // 0xbef890: LoadField: r2 = r4->field_f
    //     0xbef890: ldur            w2, [x4, #0xf]
    // 0xbef894: DecompressPointer r2
    //     0xbef894: add             x2, x2, HEAP, lsl #32
    // 0xbef898: LoadField: r3 = r2->field_b
    //     0xbef898: ldur            w3, [x2, #0xb]
    // 0xbef89c: DecompressPointer r3
    //     0xbef89c: add             x3, x3, HEAP, lsl #32
    // 0xbef8a0: cmp             w3, NULL
    // 0xbef8a4: b.eq            #0xbefe3c
    // 0xbef8a8: LoadField: r0 = r3->field_b
    //     0xbef8a8: ldur            w0, [x3, #0xb]
    // 0xbef8ac: DecompressPointer r0
    //     0xbef8ac: add             x0, x0, HEAP, lsl #32
    // 0xbef8b0: LoadField: r4 = r0->field_6f
    //     0xbef8b0: ldur            w4, [x0, #0x6f]
    // 0xbef8b4: DecompressPointer r4
    //     0xbef8b4: add             x4, x4, HEAP, lsl #32
    // 0xbef8b8: cmp             w4, NULL
    // 0xbef8bc: b.ne            #0xbef8c8
    // 0xbef8c0: r0 = Null
    //     0xbef8c0: mov             x0, NULL
    // 0xbef8c4: b               #0xbef900
    // 0xbef8c8: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xbef8c8: ldur            x5, [x2, #0x17]
    // 0xbef8cc: LoadField: r0 = r4->field_b
    //     0xbef8cc: ldur            w0, [x4, #0xb]
    // 0xbef8d0: r1 = LoadInt32Instr(r0)
    //     0xbef8d0: sbfx            x1, x0, #1, #0x1f
    // 0xbef8d4: mov             x0, x1
    // 0xbef8d8: mov             x1, x5
    // 0xbef8dc: cmp             x1, x0
    // 0xbef8e0: b.hs            #0xbefe40
    // 0xbef8e4: LoadField: r0 = r4->field_f
    //     0xbef8e4: ldur            w0, [x4, #0xf]
    // 0xbef8e8: DecompressPointer r0
    //     0xbef8e8: add             x0, x0, HEAP, lsl #32
    // 0xbef8ec: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbef8ec: add             x16, x0, x5, lsl #2
    //     0xbef8f0: ldur            w1, [x16, #0xf]
    // 0xbef8f4: DecompressPointer r1
    //     0xbef8f4: add             x1, x1, HEAP, lsl #32
    // 0xbef8f8: LoadField: r0 = r1->field_f
    //     0xbef8f8: ldur            w0, [x1, #0xf]
    // 0xbef8fc: DecompressPointer r0
    //     0xbef8fc: add             x0, x0, HEAP, lsl #32
    // 0xbef900: cmp             w0, NULL
    // 0xbef904: b.ne            #0xbef910
    // 0xbef908: r5 = ""
    //     0xbef908: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef90c: b               #0xbef914
    // 0xbef910: mov             x5, x0
    // 0xbef914: cmp             w4, NULL
    // 0xbef918: b.ne            #0xbef924
    // 0xbef91c: r0 = Null
    //     0xbef91c: mov             x0, NULL
    // 0xbef920: b               #0xbef95c
    // 0xbef924: ArrayLoad: r6 = r2[0]  ; List_8
    //     0xbef924: ldur            x6, [x2, #0x17]
    // 0xbef928: LoadField: r0 = r4->field_b
    //     0xbef928: ldur            w0, [x4, #0xb]
    // 0xbef92c: r1 = LoadInt32Instr(r0)
    //     0xbef92c: sbfx            x1, x0, #1, #0x1f
    // 0xbef930: mov             x0, x1
    // 0xbef934: mov             x1, x6
    // 0xbef938: cmp             x1, x0
    // 0xbef93c: b.hs            #0xbefe44
    // 0xbef940: LoadField: r0 = r4->field_f
    //     0xbef940: ldur            w0, [x4, #0xf]
    // 0xbef944: DecompressPointer r0
    //     0xbef944: add             x0, x0, HEAP, lsl #32
    // 0xbef948: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef948: add             x16, x0, x6, lsl #2
    //     0xbef94c: ldur            w1, [x16, #0xf]
    // 0xbef950: DecompressPointer r1
    //     0xbef950: add             x1, x1, HEAP, lsl #32
    // 0xbef954: LoadField: r0 = r1->field_b
    //     0xbef954: ldur            w0, [x1, #0xb]
    // 0xbef958: DecompressPointer r0
    //     0xbef958: add             x0, x0, HEAP, lsl #32
    // 0xbef95c: cmp             w0, NULL
    // 0xbef960: b.ne            #0xbef968
    // 0xbef964: r0 = ""
    //     0xbef964: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef968: LoadField: r1 = r3->field_1b
    //     0xbef968: ldur            w1, [x3, #0x1b]
    // 0xbef96c: DecompressPointer r1
    //     0xbef96c: add             x1, x1, HEAP, lsl #32
    // 0xbef970: stp             x5, x1, [SP, #0x10]
    // 0xbef974: r16 = 2
    //     0xbef974: movz            x16, #0x2
    // 0xbef978: stp             x16, x0, [SP]
    // 0xbef97c: mov             x0, x1
    // 0xbef980: ClosureCall
    //     0xbef980: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbef984: ldur            x2, [x0, #0x1f]
    //     0xbef988: blr             x2
    // 0xbef98c: b               #0xbefd8c
    // 0xbef990: mov             x16, x4
    // 0xbef994: mov             x4, x2
    // 0xbef998: mov             x2, x16
    // 0xbef99c: LoadField: r0 = r2->field_27
    //     0xbef99c: ldur            w0, [x2, #0x27]
    // 0xbef9a0: DecompressPointer r0
    //     0xbef9a0: add             x0, x0, HEAP, lsl #32
    // 0xbef9a4: tbnz            w0, #4, #0xbefc80
    // 0xbef9a8: cmp             w5, NULL
    // 0xbef9ac: b.ne            #0xbef9b8
    // 0xbef9b0: r0 = Null
    //     0xbef9b0: mov             x0, NULL
    // 0xbef9b4: b               #0xbef9f0
    // 0xbef9b8: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xbef9b8: ldur            x6, [x3, #0x17]
    // 0xbef9bc: LoadField: r0 = r5->field_b
    //     0xbef9bc: ldur            w0, [x5, #0xb]
    // 0xbef9c0: r1 = LoadInt32Instr(r0)
    //     0xbef9c0: sbfx            x1, x0, #1, #0x1f
    // 0xbef9c4: mov             x0, x1
    // 0xbef9c8: mov             x1, x6
    // 0xbef9cc: cmp             x1, x0
    // 0xbef9d0: b.hs            #0xbefe48
    // 0xbef9d4: LoadField: r0 = r5->field_f
    //     0xbef9d4: ldur            w0, [x5, #0xf]
    // 0xbef9d8: DecompressPointer r0
    //     0xbef9d8: add             x0, x0, HEAP, lsl #32
    // 0xbef9dc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbef9dc: add             x16, x0, x6, lsl #2
    //     0xbef9e0: ldur            w1, [x16, #0xf]
    // 0xbef9e4: DecompressPointer r1
    //     0xbef9e4: add             x1, x1, HEAP, lsl #32
    // 0xbef9e8: LoadField: r0 = r1->field_7
    //     0xbef9e8: ldur            w0, [x1, #7]
    // 0xbef9ec: DecompressPointer r0
    //     0xbef9ec: add             x0, x0, HEAP, lsl #32
    // 0xbef9f0: cmp             w0, NULL
    // 0xbef9f4: b.ne            #0xbefa00
    // 0xbef9f8: r6 = ""
    //     0xbef9f8: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbef9fc: b               #0xbefa04
    // 0xbefa00: mov             x6, x0
    // 0xbefa04: stur            x6, [fp, #-0x10]
    // 0xbefa08: cmp             w5, NULL
    // 0xbefa0c: b.ne            #0xbefa18
    // 0xbefa10: r0 = Null
    //     0xbefa10: mov             x0, NULL
    // 0xbefa14: b               #0xbefa68
    // 0xbefa18: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xbefa18: ldur            x7, [x3, #0x17]
    // 0xbefa1c: LoadField: r0 = r5->field_b
    //     0xbefa1c: ldur            w0, [x5, #0xb]
    // 0xbefa20: r1 = LoadInt32Instr(r0)
    //     0xbefa20: sbfx            x1, x0, #1, #0x1f
    // 0xbefa24: mov             x0, x1
    // 0xbefa28: mov             x1, x7
    // 0xbefa2c: cmp             x1, x0
    // 0xbefa30: b.hs            #0xbefe4c
    // 0xbefa34: LoadField: r0 = r5->field_f
    //     0xbefa34: ldur            w0, [x5, #0xf]
    // 0xbefa38: DecompressPointer r0
    //     0xbefa38: add             x0, x0, HEAP, lsl #32
    // 0xbefa3c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbefa3c: add             x16, x0, x7, lsl #2
    //     0xbefa40: ldur            w1, [x16, #0xf]
    // 0xbefa44: DecompressPointer r1
    //     0xbefa44: add             x1, x1, HEAP, lsl #32
    // 0xbefa48: LoadField: r0 = r1->field_1b
    //     0xbefa48: ldur            w0, [x1, #0x1b]
    // 0xbefa4c: DecompressPointer r0
    //     0xbefa4c: add             x0, x0, HEAP, lsl #32
    // 0xbefa50: cmp             w0, NULL
    // 0xbefa54: b.ne            #0xbefa60
    // 0xbefa58: r0 = Null
    //     0xbefa58: mov             x0, NULL
    // 0xbefa5c: b               #0xbefa68
    // 0xbefa60: stp             x0, NULL, [SP]
    // 0xbefa64: r0 = _Double.fromInteger()
    //     0xbefa64: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbefa68: cmp             w0, NULL
    // 0xbefa6c: b.ne            #0xbefa78
    // 0xbefa70: d0 = 0.000000
    //     0xbefa70: eor             v0.16b, v0.16b, v0.16b
    // 0xbefa74: b               #0xbefa7c
    // 0xbefa78: LoadField: d0 = r0->field_7
    //     0xbefa78: ldur            d0, [x0, #7]
    // 0xbefa7c: ldur            x0, [fp, #-8]
    // 0xbefa80: stur            d0, [fp, #-0x48]
    // 0xbefa84: r1 = Null
    //     0xbefa84: mov             x1, NULL
    // 0xbefa88: r2 = 12
    //     0xbefa88: movz            x2, #0xc
    // 0xbefa8c: r0 = AllocateArray()
    //     0xbefa8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbefa90: mov             x2, x0
    // 0xbefa94: stur            x2, [fp, #-0x20]
    // 0xbefa98: r16 = "id"
    //     0xbefa98: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0xbefa9c: ldr             x16, [x16, #0x400]
    // 0xbefaa0: StoreField: r2->field_f = r16
    //     0xbefaa0: stur            w16, [x2, #0xf]
    // 0xbefaa4: ldur            x3, [fp, #-8]
    // 0xbefaa8: LoadField: r4 = r3->field_f
    //     0xbefaa8: ldur            w4, [x3, #0xf]
    // 0xbefaac: DecompressPointer r4
    //     0xbefaac: add             x4, x4, HEAP, lsl #32
    // 0xbefab0: LoadField: r0 = r4->field_b
    //     0xbefab0: ldur            w0, [x4, #0xb]
    // 0xbefab4: DecompressPointer r0
    //     0xbefab4: add             x0, x0, HEAP, lsl #32
    // 0xbefab8: cmp             w0, NULL
    // 0xbefabc: b.eq            #0xbefe50
    // 0xbefac0: LoadField: r1 = r0->field_b
    //     0xbefac0: ldur            w1, [x0, #0xb]
    // 0xbefac4: DecompressPointer r1
    //     0xbefac4: add             x1, x1, HEAP, lsl #32
    // 0xbefac8: LoadField: r5 = r1->field_73
    //     0xbefac8: ldur            w5, [x1, #0x73]
    // 0xbefacc: DecompressPointer r5
    //     0xbefacc: add             x5, x5, HEAP, lsl #32
    // 0xbefad0: cmp             w5, NULL
    // 0xbefad4: b.ne            #0xbefae0
    // 0xbefad8: r0 = Null
    //     0xbefad8: mov             x0, NULL
    // 0xbefadc: b               #0xbefb18
    // 0xbefae0: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xbefae0: ldur            x6, [x4, #0x17]
    // 0xbefae4: LoadField: r0 = r5->field_b
    //     0xbefae4: ldur            w0, [x5, #0xb]
    // 0xbefae8: r1 = LoadInt32Instr(r0)
    //     0xbefae8: sbfx            x1, x0, #1, #0x1f
    // 0xbefaec: mov             x0, x1
    // 0xbefaf0: mov             x1, x6
    // 0xbefaf4: cmp             x1, x0
    // 0xbefaf8: b.hs            #0xbefe54
    // 0xbefafc: LoadField: r0 = r5->field_f
    //     0xbefafc: ldur            w0, [x5, #0xf]
    // 0xbefb00: DecompressPointer r0
    //     0xbefb00: add             x0, x0, HEAP, lsl #32
    // 0xbefb04: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbefb04: add             x16, x0, x6, lsl #2
    //     0xbefb08: ldur            w1, [x16, #0xf]
    // 0xbefb0c: DecompressPointer r1
    //     0xbefb0c: add             x1, x1, HEAP, lsl #32
    // 0xbefb10: LoadField: r0 = r1->field_7
    //     0xbefb10: ldur            w0, [x1, #7]
    // 0xbefb14: DecompressPointer r0
    //     0xbefb14: add             x0, x0, HEAP, lsl #32
    // 0xbefb18: cmp             w0, NULL
    // 0xbefb1c: b.ne            #0xbefb24
    // 0xbefb20: r0 = ""
    //     0xbefb20: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbefb24: StoreField: r2->field_13 = r0
    //     0xbefb24: stur            w0, [x2, #0x13]
    // 0xbefb28: r16 = "quantity"
    //     0xbefb28: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbefb2c: ldr             x16, [x16, #0x428]
    // 0xbefb30: ArrayStore: r2[0] = r16  ; List_4
    //     0xbefb30: stur            w16, [x2, #0x17]
    // 0xbefb34: r16 = 2
    //     0xbefb34: movz            x16, #0x2
    // 0xbefb38: StoreField: r2->field_1b = r16
    //     0xbefb38: stur            w16, [x2, #0x1b]
    // 0xbefb3c: r16 = "item_price"
    //     0xbefb3c: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0xbefb40: ldr             x16, [x16, #0x498]
    // 0xbefb44: StoreField: r2->field_1f = r16
    //     0xbefb44: stur            w16, [x2, #0x1f]
    // 0xbefb48: cmp             w5, NULL
    // 0xbefb4c: b.ne            #0xbefb58
    // 0xbefb50: r0 = Null
    //     0xbefb50: mov             x0, NULL
    // 0xbefb54: b               #0xbefba8
    // 0xbefb58: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xbefb58: ldur            x6, [x4, #0x17]
    // 0xbefb5c: LoadField: r0 = r5->field_b
    //     0xbefb5c: ldur            w0, [x5, #0xb]
    // 0xbefb60: r1 = LoadInt32Instr(r0)
    //     0xbefb60: sbfx            x1, x0, #1, #0x1f
    // 0xbefb64: mov             x0, x1
    // 0xbefb68: mov             x1, x6
    // 0xbefb6c: cmp             x1, x0
    // 0xbefb70: b.hs            #0xbefe58
    // 0xbefb74: LoadField: r0 = r5->field_f
    //     0xbefb74: ldur            w0, [x5, #0xf]
    // 0xbefb78: DecompressPointer r0
    //     0xbefb78: add             x0, x0, HEAP, lsl #32
    // 0xbefb7c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbefb7c: add             x16, x0, x6, lsl #2
    //     0xbefb80: ldur            w1, [x16, #0xf]
    // 0xbefb84: DecompressPointer r1
    //     0xbefb84: add             x1, x1, HEAP, lsl #32
    // 0xbefb88: LoadField: r0 = r1->field_1b
    //     0xbefb88: ldur            w0, [x1, #0x1b]
    // 0xbefb8c: DecompressPointer r0
    //     0xbefb8c: add             x0, x0, HEAP, lsl #32
    // 0xbefb90: cmp             w0, NULL
    // 0xbefb94: b.ne            #0xbefba0
    // 0xbefb98: r0 = Null
    //     0xbefb98: mov             x0, NULL
    // 0xbefb9c: b               #0xbefba8
    // 0xbefba0: stp             x0, NULL, [SP]
    // 0xbefba4: r0 = _Double.fromInteger()
    //     0xbefba4: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbefba8: cmp             w0, NULL
    // 0xbefbac: b.ne            #0xbefbb8
    // 0xbefbb0: d1 = 0.000000
    //     0xbefbb0: eor             v1.16b, v1.16b, v1.16b
    // 0xbefbb4: b               #0xbefbc0
    // 0xbefbb8: LoadField: d0 = r0->field_7
    //     0xbefbb8: ldur            d0, [x0, #7]
    // 0xbefbbc: mov             v1.16b, v0.16b
    // 0xbefbc0: ldur            d0, [fp, #-0x48]
    // 0xbefbc4: ldur            x2, [fp, #-0x18]
    // 0xbefbc8: r0 = inline_Allocate_Double()
    //     0xbefbc8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbefbcc: add             x0, x0, #0x10
    //     0xbefbd0: cmp             x1, x0
    //     0xbefbd4: b.ls            #0xbefe5c
    //     0xbefbd8: str             x0, [THR, #0x50]  ; THR::top
    //     0xbefbdc: sub             x0, x0, #0xf
    //     0xbefbe0: movz            x1, #0xe15c
    //     0xbefbe4: movk            x1, #0x3, lsl #16
    //     0xbefbe8: stur            x1, [x0, #-1]
    // 0xbefbec: StoreField: r0->field_7 = d1
    //     0xbefbec: stur            d1, [x0, #7]
    // 0xbefbf0: ldur            x1, [fp, #-0x20]
    // 0xbefbf4: ArrayStore: r1[5] = r0  ; List_4
    //     0xbefbf4: add             x25, x1, #0x23
    //     0xbefbf8: str             w0, [x25]
    //     0xbefbfc: tbz             w0, #0, #0xbefc18
    //     0xbefc00: ldurb           w16, [x1, #-1]
    //     0xbefc04: ldurb           w17, [x0, #-1]
    //     0xbefc08: and             x16, x17, x16, lsr #2
    //     0xbefc0c: tst             x16, HEAP, lsr #32
    //     0xbefc10: b.eq            #0xbefc18
    //     0xbefc14: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbefc18: r16 = <String, dynamic>
    //     0xbefc18: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xbefc1c: ldur            lr, [fp, #-0x20]
    // 0xbefc20: stp             lr, x16, [SP]
    // 0xbefc24: r0 = Map._fromLiteral()
    //     0xbefc24: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbefc28: mov             x1, x0
    // 0xbefc2c: ldur            x0, [fp, #-0x18]
    // 0xbefc30: LoadField: r2 = r0->field_23
    //     0xbefc30: ldur            w2, [x0, #0x23]
    // 0xbefc34: DecompressPointer r2
    //     0xbefc34: add             x2, x2, HEAP, lsl #32
    // 0xbefc38: ldur            d0, [fp, #-0x48]
    // 0xbefc3c: r0 = inline_Allocate_Double()
    //     0xbefc3c: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xbefc40: add             x0, x0, #0x10
    //     0xbefc44: cmp             x3, x0
    //     0xbefc48: b.ls            #0xbefe74
    //     0xbefc4c: str             x0, [THR, #0x50]  ; THR::top
    //     0xbefc50: sub             x0, x0, #0xf
    //     0xbefc54: movz            x3, #0xe15c
    //     0xbefc58: movk            x3, #0x3, lsl #16
    //     0xbefc5c: stur            x3, [x0, #-1]
    // 0xbefc60: StoreField: r0->field_7 = d0
    //     0xbefc60: stur            d0, [x0, #7]
    // 0xbefc64: ldur            x16, [fp, #-0x10]
    // 0xbefc68: stp             x16, x2, [SP, #0x10]
    // 0xbefc6c: stp             x1, x0, [SP]
    // 0xbefc70: mov             x0, x2
    // 0xbefc74: ClosureCall
    //     0xbefc74: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbefc78: ldur            x2, [x0, #0x1f]
    //     0xbefc7c: blr             x2
    // 0xbefc80: ldur            x0, [fp, #-8]
    // 0xbefc84: LoadField: r2 = r0->field_f
    //     0xbefc84: ldur            w2, [x0, #0xf]
    // 0xbefc88: DecompressPointer r2
    //     0xbefc88: add             x2, x2, HEAP, lsl #32
    // 0xbefc8c: LoadField: r3 = r2->field_b
    //     0xbefc8c: ldur            w3, [x2, #0xb]
    // 0xbefc90: DecompressPointer r3
    //     0xbefc90: add             x3, x3, HEAP, lsl #32
    // 0xbefc94: cmp             w3, NULL
    // 0xbefc98: b.eq            #0xbefe8c
    // 0xbefc9c: LoadField: r0 = r3->field_b
    //     0xbefc9c: ldur            w0, [x3, #0xb]
    // 0xbefca0: DecompressPointer r0
    //     0xbefca0: add             x0, x0, HEAP, lsl #32
    // 0xbefca4: LoadField: r4 = r0->field_73
    //     0xbefca4: ldur            w4, [x0, #0x73]
    // 0xbefca8: DecompressPointer r4
    //     0xbefca8: add             x4, x4, HEAP, lsl #32
    // 0xbefcac: cmp             w4, NULL
    // 0xbefcb0: b.ne            #0xbefcbc
    // 0xbefcb4: r0 = Null
    //     0xbefcb4: mov             x0, NULL
    // 0xbefcb8: b               #0xbefcf4
    // 0xbefcbc: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xbefcbc: ldur            x5, [x2, #0x17]
    // 0xbefcc0: LoadField: r0 = r4->field_b
    //     0xbefcc0: ldur            w0, [x4, #0xb]
    // 0xbefcc4: r1 = LoadInt32Instr(r0)
    //     0xbefcc4: sbfx            x1, x0, #1, #0x1f
    // 0xbefcc8: mov             x0, x1
    // 0xbefccc: mov             x1, x5
    // 0xbefcd0: cmp             x1, x0
    // 0xbefcd4: b.hs            #0xbefe90
    // 0xbefcd8: LoadField: r0 = r4->field_f
    //     0xbefcd8: ldur            w0, [x4, #0xf]
    // 0xbefcdc: DecompressPointer r0
    //     0xbefcdc: add             x0, x0, HEAP, lsl #32
    // 0xbefce0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbefce0: add             x16, x0, x5, lsl #2
    //     0xbefce4: ldur            w1, [x16, #0xf]
    // 0xbefce8: DecompressPointer r1
    //     0xbefce8: add             x1, x1, HEAP, lsl #32
    // 0xbefcec: LoadField: r0 = r1->field_b
    //     0xbefcec: ldur            w0, [x1, #0xb]
    // 0xbefcf0: DecompressPointer r0
    //     0xbefcf0: add             x0, x0, HEAP, lsl #32
    // 0xbefcf4: cmp             w0, NULL
    // 0xbefcf8: b.ne            #0xbefd04
    // 0xbefcfc: r5 = ""
    //     0xbefcfc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbefd00: b               #0xbefd08
    // 0xbefd04: mov             x5, x0
    // 0xbefd08: cmp             w4, NULL
    // 0xbefd0c: b.ne            #0xbefd18
    // 0xbefd10: r0 = Null
    //     0xbefd10: mov             x0, NULL
    // 0xbefd14: b               #0xbefd50
    // 0xbefd18: ArrayLoad: r6 = r2[0]  ; List_8
    //     0xbefd18: ldur            x6, [x2, #0x17]
    // 0xbefd1c: LoadField: r0 = r4->field_b
    //     0xbefd1c: ldur            w0, [x4, #0xb]
    // 0xbefd20: r1 = LoadInt32Instr(r0)
    //     0xbefd20: sbfx            x1, x0, #1, #0x1f
    // 0xbefd24: mov             x0, x1
    // 0xbefd28: mov             x1, x6
    // 0xbefd2c: cmp             x1, x0
    // 0xbefd30: b.hs            #0xbefe94
    // 0xbefd34: LoadField: r0 = r4->field_f
    //     0xbefd34: ldur            w0, [x4, #0xf]
    // 0xbefd38: DecompressPointer r0
    //     0xbefd38: add             x0, x0, HEAP, lsl #32
    // 0xbefd3c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbefd3c: add             x16, x0, x6, lsl #2
    //     0xbefd40: ldur            w1, [x16, #0xf]
    // 0xbefd44: DecompressPointer r1
    //     0xbefd44: add             x1, x1, HEAP, lsl #32
    // 0xbefd48: LoadField: r0 = r1->field_7
    //     0xbefd48: ldur            w0, [x1, #7]
    // 0xbefd4c: DecompressPointer r0
    //     0xbefd4c: add             x0, x0, HEAP, lsl #32
    // 0xbefd50: cmp             w0, NULL
    // 0xbefd54: b.ne            #0xbefd5c
    // 0xbefd58: r0 = ""
    //     0xbefd58: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbefd5c: LoadField: r1 = r3->field_1b
    //     0xbefd5c: ldur            w1, [x3, #0x1b]
    // 0xbefd60: DecompressPointer r1
    //     0xbefd60: add             x1, x1, HEAP, lsl #32
    // 0xbefd64: stp             x5, x1, [SP, #0x18]
    // 0xbefd68: r16 = 2
    //     0xbefd68: movz            x16, #0x2
    // 0xbefd6c: stp             x16, x0, [SP, #8]
    // 0xbefd70: r16 = ""
    //     0xbefd70: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbefd74: str             x16, [SP]
    // 0xbefd78: mov             x0, x1
    // 0xbefd7c: ClosureCall
    //     0xbefd7c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53c40] List(7) [0, 0x5, 0x5, 0x4, "customisationId", 0x4, Null]
    //     0xbefd80: ldr             x4, [x4, #0xc40]
    //     0xbefd84: ldur            x2, [x0, #0x1f]
    //     0xbefd88: blr             x2
    // 0xbefd8c: r0 = Null
    //     0xbefd8c: mov             x0, NULL
    // 0xbefd90: LeaveFrame
    //     0xbefd90: mov             SP, fp
    //     0xbefd94: ldp             fp, lr, [SP], #0x10
    // 0xbefd98: ret
    //     0xbefd98: ret             
    // 0xbefd9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbefd9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbefda0: b               #0xbeea38
    // 0xbefda4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefda4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefda8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefda8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefdac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefdac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefdb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefdb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefdb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdb4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdbc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefdc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefdc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdc4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdc8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdcc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdd8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefddc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefddc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefde0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefde0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefde4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefde4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefde8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefde8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdf0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdf4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdf8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefdf8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefdfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefdfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefe00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe04: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe04: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe08: stp             q0, q1, [SP, #-0x20]!
    // 0xbefe0c: SaveReg r2
    //     0xbefe0c: str             x2, [SP, #-8]!
    // 0xbefe10: r0 = AllocateDouble()
    //     0xbefe10: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbefe14: RestoreReg r2
    //     0xbefe14: ldr             x2, [SP], #8
    // 0xbefe18: ldp             q0, q1, [SP], #0x20
    // 0xbefe1c: b               #0xbef7fc
    // 0xbefe20: SaveReg d0
    //     0xbefe20: str             q0, [SP, #-0x10]!
    // 0xbefe24: stp             x0, x1, [SP, #-0x10]!
    // 0xbefe28: r0 = AllocateDouble()
    //     0xbefe28: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbefe2c: mov             x2, x0
    // 0xbefe30: ldp             x0, x1, [SP], #0x10
    // 0xbefe34: RestoreReg d0
    //     0xbefe34: ldr             q0, [SP], #0x10
    // 0xbefe38: b               #0xbef86c
    // 0xbefe3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefe3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefe40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe44: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe48: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefe50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefe54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe5c: stp             q0, q1, [SP, #-0x20]!
    // 0xbefe60: SaveReg r2
    //     0xbefe60: str             x2, [SP, #-8]!
    // 0xbefe64: r0 = AllocateDouble()
    //     0xbefe64: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbefe68: RestoreReg r2
    //     0xbefe68: ldr             x2, [SP], #8
    // 0xbefe6c: ldp             q0, q1, [SP], #0x20
    // 0xbefe70: b               #0xbefbec
    // 0xbefe74: SaveReg d0
    //     0xbefe74: str             q0, [SP, #-0x10]!
    // 0xbefe78: stp             x1, x2, [SP, #-0x10]!
    // 0xbefe7c: r0 = AllocateDouble()
    //     0xbefe7c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbefe80: ldp             x1, x2, [SP], #0x10
    // 0xbefe84: RestoreReg d0
    //     0xbefe84: ldr             q0, [SP], #0x10
    // 0xbefe88: b               #0xbefc60
    // 0xbefe8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbefe8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbefe90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe90: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbefe94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbefe94: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AllSkuDatum?) {
    // ** addr: 0xbefe98, size: 0x124
    // 0xbefe98: EnterFrame
    //     0xbefe98: stp             fp, lr, [SP, #-0x10]!
    //     0xbefe9c: mov             fp, SP
    // 0xbefea0: AllocStack(0x40)
    //     0xbefea0: sub             SP, SP, #0x40
    // 0xbefea4: SetupParameters()
    //     0xbefea4: ldr             x0, [fp, #0x18]
    //     0xbefea8: ldur            w1, [x0, #0x17]
    //     0xbefeac: add             x1, x1, HEAP, lsl #32
    //     0xbefeb0: stur            x1, [fp, #-8]
    // 0xbefeb4: CheckStackOverflow
    //     0xbefeb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbefeb8: cmp             SP, x16
    //     0xbefebc: b.ls            #0xbeffb0
    // 0xbefec0: r1 = 1
    //     0xbefec0: movz            x1, #0x1
    // 0xbefec4: r0 = AllocateContext()
    //     0xbefec4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbefec8: mov             x1, x0
    // 0xbefecc: ldur            x0, [fp, #-8]
    // 0xbefed0: stur            x1, [fp, #-0x28]
    // 0xbefed4: StoreField: r1->field_b = r0
    //     0xbefed4: stur            w0, [x1, #0xb]
    // 0xbefed8: ldr             x2, [fp, #0x10]
    // 0xbefedc: StoreField: r1->field_f = r2
    //     0xbefedc: stur            w2, [x1, #0xf]
    // 0xbefee0: LoadField: r2 = r0->field_f
    //     0xbefee0: ldur            w2, [x0, #0xf]
    // 0xbefee4: DecompressPointer r2
    //     0xbefee4: add             x2, x2, HEAP, lsl #32
    // 0xbefee8: LoadField: r3 = r2->field_b
    //     0xbefee8: ldur            w3, [x2, #0xb]
    // 0xbefeec: DecompressPointer r3
    //     0xbefeec: add             x3, x3, HEAP, lsl #32
    // 0xbefef0: stur            x3, [fp, #-0x20]
    // 0xbefef4: cmp             w3, NULL
    // 0xbefef8: b.eq            #0xbeffb8
    // 0xbefefc: LoadField: r4 = r2->field_2b
    //     0xbefefc: ldur            w4, [x2, #0x2b]
    // 0xbeff00: DecompressPointer r4
    //     0xbeff00: add             x4, x4, HEAP, lsl #32
    // 0xbeff04: stur            x4, [fp, #-0x18]
    // 0xbeff08: LoadField: r2 = r3->field_f
    //     0xbeff08: ldur            w2, [x3, #0xf]
    // 0xbeff0c: DecompressPointer r2
    //     0xbeff0c: add             x2, x2, HEAP, lsl #32
    // 0xbeff10: stur            x2, [fp, #-0x10]
    // 0xbeff14: r0 = EventData()
    //     0xbeff14: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xbeff18: mov             x1, x0
    // 0xbeff1c: ldur            x0, [fp, #-0x10]
    // 0xbeff20: stur            x1, [fp, #-0x30]
    // 0xbeff24: StoreField: r1->field_13 = r0
    //     0xbeff24: stur            w0, [x1, #0x13]
    // 0xbeff28: ldur            x0, [fp, #-0x18]
    // 0xbeff2c: StoreField: r1->field_3b = r0
    //     0xbeff2c: stur            w0, [x1, #0x3b]
    // 0xbeff30: r0 = EventsRequest()
    //     0xbeff30: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xbeff34: mov             x1, x0
    // 0xbeff38: r0 = "size_variation_clicked"
    //     0xbeff38: add             x0, PP, #0x32, lsl #12  ; [pp+0x32a28] "size_variation_clicked"
    //     0xbeff3c: ldr             x0, [x0, #0xa28]
    // 0xbeff40: StoreField: r1->field_7 = r0
    //     0xbeff40: stur            w0, [x1, #7]
    // 0xbeff44: ldur            x0, [fp, #-0x30]
    // 0xbeff48: StoreField: r1->field_b = r0
    //     0xbeff48: stur            w0, [x1, #0xb]
    // 0xbeff4c: ldur            x0, [fp, #-0x20]
    // 0xbeff50: LoadField: r2 = r0->field_13
    //     0xbeff50: ldur            w2, [x0, #0x13]
    // 0xbeff54: DecompressPointer r2
    //     0xbeff54: add             x2, x2, HEAP, lsl #32
    // 0xbeff58: stp             x1, x2, [SP]
    // 0xbeff5c: r4 = 0
    //     0xbeff5c: movz            x4, #0
    // 0xbeff60: ldr             x0, [SP, #8]
    // 0xbeff64: r16 = UnlinkedCall_0x613b5c
    //     0xbeff64: add             x16, PP, #0x53, lsl #12  ; [pp+0x53c48] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbeff68: add             x16, x16, #0xc48
    // 0xbeff6c: ldp             x5, lr, [x16]
    // 0xbeff70: blr             lr
    // 0xbeff74: ldur            x0, [fp, #-8]
    // 0xbeff78: LoadField: r3 = r0->field_f
    //     0xbeff78: ldur            w3, [x0, #0xf]
    // 0xbeff7c: DecompressPointer r3
    //     0xbeff7c: add             x3, x3, HEAP, lsl #32
    // 0xbeff80: ldur            x2, [fp, #-0x28]
    // 0xbeff84: stur            x3, [fp, #-0x10]
    // 0xbeff88: r1 = Function '<anonymous closure>':.
    //     0xbeff88: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c58] AnonymousClosure: (0xbeffbc), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xbeff8c: ldr             x1, [x1, #0xc58]
    // 0xbeff90: r0 = AllocateClosure()
    //     0xbeff90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeff94: ldur            x1, [fp, #-0x10]
    // 0xbeff98: mov             x2, x0
    // 0xbeff9c: r0 = setState()
    //     0xbeff9c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbeffa0: r0 = Null
    //     0xbeffa0: mov             x0, NULL
    // 0xbeffa4: LeaveFrame
    //     0xbeffa4: mov             SP, fp
    //     0xbeffa8: ldp             fp, lr, [SP], #0x10
    // 0xbeffac: ret
    //     0xbeffac: ret             
    // 0xbeffb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeffb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeffb4: b               #0xbefec0
    // 0xbeffb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeffb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbeffbc, size: 0x17c
    // 0xbeffbc: EnterFrame
    //     0xbeffbc: stp             fp, lr, [SP, #-0x10]!
    //     0xbeffc0: mov             fp, SP
    // 0xbeffc4: AllocStack(0x18)
    //     0xbeffc4: sub             SP, SP, #0x18
    // 0xbeffc8: SetupParameters()
    //     0xbeffc8: ldr             x0, [fp, #0x10]
    //     0xbeffcc: ldur            w3, [x0, #0x17]
    //     0xbeffd0: add             x3, x3, HEAP, lsl #32
    //     0xbeffd4: stur            x3, [fp, #-0x18]
    // 0xbeffd8: CheckStackOverflow
    //     0xbeffd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeffdc: cmp             SP, x16
    //     0xbeffe0: b.ls            #0xbf0120
    // 0xbeffe4: LoadField: r4 = r3->field_b
    //     0xbeffe4: ldur            w4, [x3, #0xb]
    // 0xbeffe8: DecompressPointer r4
    //     0xbeffe8: add             x4, x4, HEAP, lsl #32
    // 0xbeffec: stur            x4, [fp, #-0x10]
    // 0xbefff0: LoadField: r5 = r4->field_f
    //     0xbefff0: ldur            w5, [x4, #0xf]
    // 0xbefff4: DecompressPointer r5
    //     0xbefff4: add             x5, x5, HEAP, lsl #32
    // 0xbefff8: stur            x5, [fp, #-8]
    // 0xbefffc: LoadField: r0 = r5->field_b
    //     0xbefffc: ldur            w0, [x5, #0xb]
    // 0xbf0000: DecompressPointer r0
    //     0xbf0000: add             x0, x0, HEAP, lsl #32
    // 0xbf0004: cmp             w0, NULL
    // 0xbf0008: b.eq            #0xbf0128
    // 0xbf000c: LoadField: r1 = r0->field_b
    //     0xbf000c: ldur            w1, [x0, #0xb]
    // 0xbf0010: DecompressPointer r1
    //     0xbf0010: add             x1, x1, HEAP, lsl #32
    // 0xbf0014: LoadField: r2 = r1->field_73
    //     0xbf0014: ldur            w2, [x1, #0x73]
    // 0xbf0018: DecompressPointer r2
    //     0xbf0018: add             x2, x2, HEAP, lsl #32
    // 0xbf001c: cmp             w2, NULL
    // 0xbf0020: b.ne            #0xbf002c
    // 0xbf0024: r1 = Null
    //     0xbf0024: mov             x1, NULL
    // 0xbf0028: b               #0xbf00ac
    // 0xbf002c: LoadField: r6 = r5->field_1f
    //     0xbf002c: ldur            x6, [x5, #0x1f]
    // 0xbf0030: LoadField: r0 = r2->field_b
    //     0xbf0030: ldur            w0, [x2, #0xb]
    // 0xbf0034: r1 = LoadInt32Instr(r0)
    //     0xbf0034: sbfx            x1, x0, #1, #0x1f
    // 0xbf0038: mov             x0, x1
    // 0xbf003c: mov             x1, x6
    // 0xbf0040: cmp             x1, x0
    // 0xbf0044: b.hs            #0xbf012c
    // 0xbf0048: LoadField: r0 = r2->field_f
    //     0xbf0048: ldur            w0, [x2, #0xf]
    // 0xbf004c: DecompressPointer r0
    //     0xbf004c: add             x0, x0, HEAP, lsl #32
    // 0xbf0050: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbf0050: add             x16, x0, x6, lsl #2
    //     0xbf0054: ldur            w1, [x16, #0xf]
    // 0xbf0058: DecompressPointer r1
    //     0xbf0058: add             x1, x1, HEAP, lsl #32
    // 0xbf005c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbf005c: ldur            w0, [x1, #0x17]
    // 0xbf0060: DecompressPointer r0
    //     0xbf0060: add             x0, x0, HEAP, lsl #32
    // 0xbf0064: cmp             w0, NULL
    // 0xbf0068: b.ne            #0xbf0074
    // 0xbf006c: r1 = Null
    //     0xbf006c: mov             x1, NULL
    // 0xbf0070: b               #0xbf00ac
    // 0xbf0074: LoadField: r2 = r3->field_f
    //     0xbf0074: ldur            w2, [x3, #0xf]
    // 0xbf0078: DecompressPointer r2
    //     0xbf0078: add             x2, x2, HEAP, lsl #32
    // 0xbf007c: cmp             w2, NULL
    // 0xbf0080: b.eq            #0xbf0130
    // 0xbf0084: mov             x1, x0
    // 0xbf0088: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf0088: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf008c: r0 = indexOf()
    //     0xbf008c: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xbf0090: mov             x2, x0
    // 0xbf0094: r0 = BoxInt64Instr(r2)
    //     0xbf0094: sbfiz           x0, x2, #1, #0x1f
    //     0xbf0098: cmp             x2, x0, asr #1
    //     0xbf009c: b.eq            #0xbf00a8
    //     0xbf00a0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf00a4: stur            x2, [x0, #7]
    // 0xbf00a8: mov             x1, x0
    // 0xbf00ac: cmp             w1, NULL
    // 0xbf00b0: b.ne            #0xbf00bc
    // 0xbf00b4: r4 = 0
    //     0xbf00b4: movz            x4, #0
    // 0xbf00b8: b               #0xbf00cc
    // 0xbf00bc: r2 = LoadInt32Instr(r1)
    //     0xbf00bc: sbfx            x2, x1, #1, #0x1f
    //     0xbf00c0: tbz             w1, #0, #0xbf00c8
    //     0xbf00c4: ldur            x2, [x1, #7]
    // 0xbf00c8: mov             x4, x2
    // 0xbf00cc: ldur            x1, [fp, #-0x18]
    // 0xbf00d0: ldur            x2, [fp, #-0x10]
    // 0xbf00d4: ldur            x3, [fp, #-8]
    // 0xbf00d8: ArrayStore: r3[0] = r4  ; List_8
    //     0xbf00d8: stur            x4, [x3, #0x17]
    // 0xbf00dc: LoadField: r3 = r2->field_f
    //     0xbf00dc: ldur            w3, [x2, #0xf]
    // 0xbf00e0: DecompressPointer r3
    //     0xbf00e0: add             x3, x3, HEAP, lsl #32
    // 0xbf00e4: LoadField: r0 = r1->field_f
    //     0xbf00e4: ldur            w0, [x1, #0xf]
    // 0xbf00e8: DecompressPointer r0
    //     0xbf00e8: add             x0, x0, HEAP, lsl #32
    // 0xbf00ec: cmp             w0, NULL
    // 0xbf00f0: b.eq            #0xbf0134
    // 0xbf00f4: StoreField: r3->field_13 = r0
    //     0xbf00f4: stur            w0, [x3, #0x13]
    //     0xbf00f8: ldurb           w16, [x3, #-1]
    //     0xbf00fc: ldurb           w17, [x0, #-1]
    //     0xbf0100: and             x16, x17, x16, lsr #2
    //     0xbf0104: tst             x16, HEAP, lsr #32
    //     0xbf0108: b.eq            #0xbf0110
    //     0xbf010c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xbf0110: r0 = Null
    //     0xbf0110: mov             x0, NULL
    // 0xbf0114: LeaveFrame
    //     0xbf0114: mov             SP, fp
    //     0xbf0118: ldp             fp, lr, [SP], #0x10
    // 0xbf011c: ret
    //     0xbf011c: ret             
    // 0xbf0120: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf0120: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0124: b               #0xbeffe4
    // 0xbf0128: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0128: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf012c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf012c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf0130: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0130: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0134: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0134: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AllSkuDatum?) {
    // ** addr: 0xbf0244, size: 0x124
    // 0xbf0244: EnterFrame
    //     0xbf0244: stp             fp, lr, [SP, #-0x10]!
    //     0xbf0248: mov             fp, SP
    // 0xbf024c: AllocStack(0x40)
    //     0xbf024c: sub             SP, SP, #0x40
    // 0xbf0250: SetupParameters()
    //     0xbf0250: ldr             x0, [fp, #0x18]
    //     0xbf0254: ldur            w1, [x0, #0x17]
    //     0xbf0258: add             x1, x1, HEAP, lsl #32
    //     0xbf025c: stur            x1, [fp, #-8]
    // 0xbf0260: CheckStackOverflow
    //     0xbf0260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf0264: cmp             SP, x16
    //     0xbf0268: b.ls            #0xbf035c
    // 0xbf026c: r1 = 1
    //     0xbf026c: movz            x1, #0x1
    // 0xbf0270: r0 = AllocateContext()
    //     0xbf0270: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf0274: mov             x1, x0
    // 0xbf0278: ldur            x0, [fp, #-8]
    // 0xbf027c: stur            x1, [fp, #-0x28]
    // 0xbf0280: StoreField: r1->field_b = r0
    //     0xbf0280: stur            w0, [x1, #0xb]
    // 0xbf0284: ldr             x2, [fp, #0x10]
    // 0xbf0288: StoreField: r1->field_f = r2
    //     0xbf0288: stur            w2, [x1, #0xf]
    // 0xbf028c: LoadField: r2 = r0->field_f
    //     0xbf028c: ldur            w2, [x0, #0xf]
    // 0xbf0290: DecompressPointer r2
    //     0xbf0290: add             x2, x2, HEAP, lsl #32
    // 0xbf0294: LoadField: r3 = r2->field_b
    //     0xbf0294: ldur            w3, [x2, #0xb]
    // 0xbf0298: DecompressPointer r3
    //     0xbf0298: add             x3, x3, HEAP, lsl #32
    // 0xbf029c: stur            x3, [fp, #-0x20]
    // 0xbf02a0: cmp             w3, NULL
    // 0xbf02a4: b.eq            #0xbf0364
    // 0xbf02a8: LoadField: r4 = r2->field_2b
    //     0xbf02a8: ldur            w4, [x2, #0x2b]
    // 0xbf02ac: DecompressPointer r4
    //     0xbf02ac: add             x4, x4, HEAP, lsl #32
    // 0xbf02b0: stur            x4, [fp, #-0x18]
    // 0xbf02b4: LoadField: r2 = r3->field_f
    //     0xbf02b4: ldur            w2, [x3, #0xf]
    // 0xbf02b8: DecompressPointer r2
    //     0xbf02b8: add             x2, x2, HEAP, lsl #32
    // 0xbf02bc: stur            x2, [fp, #-0x10]
    // 0xbf02c0: r0 = EventData()
    //     0xbf02c0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xbf02c4: mov             x1, x0
    // 0xbf02c8: ldur            x0, [fp, #-0x10]
    // 0xbf02cc: stur            x1, [fp, #-0x30]
    // 0xbf02d0: StoreField: r1->field_13 = r0
    //     0xbf02d0: stur            w0, [x1, #0x13]
    // 0xbf02d4: ldur            x0, [fp, #-0x18]
    // 0xbf02d8: StoreField: r1->field_3b = r0
    //     0xbf02d8: stur            w0, [x1, #0x3b]
    // 0xbf02dc: r0 = EventsRequest()
    //     0xbf02dc: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xbf02e0: mov             x1, x0
    // 0xbf02e4: r0 = "size_variation_clicked"
    //     0xbf02e4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32a28] "size_variation_clicked"
    //     0xbf02e8: ldr             x0, [x0, #0xa28]
    // 0xbf02ec: StoreField: r1->field_7 = r0
    //     0xbf02ec: stur            w0, [x1, #7]
    // 0xbf02f0: ldur            x0, [fp, #-0x30]
    // 0xbf02f4: StoreField: r1->field_b = r0
    //     0xbf02f4: stur            w0, [x1, #0xb]
    // 0xbf02f8: ldur            x0, [fp, #-0x20]
    // 0xbf02fc: LoadField: r2 = r0->field_13
    //     0xbf02fc: ldur            w2, [x0, #0x13]
    // 0xbf0300: DecompressPointer r2
    //     0xbf0300: add             x2, x2, HEAP, lsl #32
    // 0xbf0304: stp             x1, x2, [SP]
    // 0xbf0308: r4 = 0
    //     0xbf0308: movz            x4, #0
    // 0xbf030c: ldr             x0, [SP, #8]
    // 0xbf0310: r16 = UnlinkedCall_0x613b5c
    //     0xbf0310: add             x16, PP, #0x53, lsl #12  ; [pp+0x53c60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf0314: add             x16, x16, #0xc60
    // 0xbf0318: ldp             x5, lr, [x16]
    // 0xbf031c: blr             lr
    // 0xbf0320: ldur            x0, [fp, #-8]
    // 0xbf0324: LoadField: r3 = r0->field_f
    //     0xbf0324: ldur            w3, [x0, #0xf]
    // 0xbf0328: DecompressPointer r3
    //     0xbf0328: add             x3, x3, HEAP, lsl #32
    // 0xbf032c: ldur            x2, [fp, #-0x28]
    // 0xbf0330: stur            x3, [fp, #-0x10]
    // 0xbf0334: r1 = Function '<anonymous closure>':.
    //     0xbf0334: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c70] AnonymousClosure: (0xbf0368), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xbf0338: ldr             x1, [x1, #0xc70]
    // 0xbf033c: r0 = AllocateClosure()
    //     0xbf033c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf0340: ldur            x1, [fp, #-0x10]
    // 0xbf0344: mov             x2, x0
    // 0xbf0348: r0 = setState()
    //     0xbf0348: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbf034c: r0 = Null
    //     0xbf034c: mov             x0, NULL
    // 0xbf0350: LeaveFrame
    //     0xbf0350: mov             SP, fp
    //     0xbf0354: ldp             fp, lr, [SP], #0x10
    // 0xbf0358: ret
    //     0xbf0358: ret             
    // 0xbf035c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf035c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0360: b               #0xbf026c
    // 0xbf0364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0364: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf0368, size: 0x12c
    // 0xbf0368: EnterFrame
    //     0xbf0368: stp             fp, lr, [SP, #-0x10]!
    //     0xbf036c: mov             fp, SP
    // 0xbf0370: AllocStack(0x18)
    //     0xbf0370: sub             SP, SP, #0x18
    // 0xbf0374: SetupParameters()
    //     0xbf0374: ldr             x0, [fp, #0x10]
    //     0xbf0378: ldur            w3, [x0, #0x17]
    //     0xbf037c: add             x3, x3, HEAP, lsl #32
    //     0xbf0380: stur            x3, [fp, #-0x18]
    // 0xbf0384: CheckStackOverflow
    //     0xbf0384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf0388: cmp             SP, x16
    //     0xbf038c: b.ls            #0xbf0480
    // 0xbf0390: LoadField: r0 = r3->field_b
    //     0xbf0390: ldur            w0, [x3, #0xb]
    // 0xbf0394: DecompressPointer r0
    //     0xbf0394: add             x0, x0, HEAP, lsl #32
    // 0xbf0398: stur            x0, [fp, #-0x10]
    // 0xbf039c: LoadField: r4 = r0->field_f
    //     0xbf039c: ldur            w4, [x0, #0xf]
    // 0xbf03a0: DecompressPointer r4
    //     0xbf03a0: add             x4, x4, HEAP, lsl #32
    // 0xbf03a4: stur            x4, [fp, #-8]
    // 0xbf03a8: LoadField: r1 = r4->field_b
    //     0xbf03a8: ldur            w1, [x4, #0xb]
    // 0xbf03ac: DecompressPointer r1
    //     0xbf03ac: add             x1, x1, HEAP, lsl #32
    // 0xbf03b0: cmp             w1, NULL
    // 0xbf03b4: b.eq            #0xbf0488
    // 0xbf03b8: LoadField: r2 = r1->field_b
    //     0xbf03b8: ldur            w2, [x1, #0xb]
    // 0xbf03bc: DecompressPointer r2
    //     0xbf03bc: add             x2, x2, HEAP, lsl #32
    // 0xbf03c0: LoadField: r1 = r2->field_6f
    //     0xbf03c0: ldur            w1, [x2, #0x6f]
    // 0xbf03c4: DecompressPointer r1
    //     0xbf03c4: add             x1, x1, HEAP, lsl #32
    // 0xbf03c8: cmp             w1, NULL
    // 0xbf03cc: b.ne            #0xbf03d8
    // 0xbf03d0: r1 = Null
    //     0xbf03d0: mov             x1, NULL
    // 0xbf03d4: b               #0xbf040c
    // 0xbf03d8: LoadField: r2 = r3->field_f
    //     0xbf03d8: ldur            w2, [x3, #0xf]
    // 0xbf03dc: DecompressPointer r2
    //     0xbf03dc: add             x2, x2, HEAP, lsl #32
    // 0xbf03e0: cmp             w2, NULL
    // 0xbf03e4: b.eq            #0xbf048c
    // 0xbf03e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf03e8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf03ec: r0 = indexOf()
    //     0xbf03ec: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xbf03f0: mov             x2, x0
    // 0xbf03f4: r0 = BoxInt64Instr(r2)
    //     0xbf03f4: sbfiz           x0, x2, #1, #0x1f
    //     0xbf03f8: cmp             x2, x0, asr #1
    //     0xbf03fc: b.eq            #0xbf0408
    //     0xbf0400: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbf0404: stur            x2, [x0, #7]
    // 0xbf0408: mov             x1, x0
    // 0xbf040c: cmp             w1, NULL
    // 0xbf0410: b.ne            #0xbf041c
    // 0xbf0414: r4 = 0
    //     0xbf0414: movz            x4, #0
    // 0xbf0418: b               #0xbf042c
    // 0xbf041c: r2 = LoadInt32Instr(r1)
    //     0xbf041c: sbfx            x2, x1, #1, #0x1f
    //     0xbf0420: tbz             w1, #0, #0xbf0428
    //     0xbf0424: ldur            x2, [x1, #7]
    // 0xbf0428: mov             x4, x2
    // 0xbf042c: ldur            x1, [fp, #-0x18]
    // 0xbf0430: ldur            x2, [fp, #-0x10]
    // 0xbf0434: ldur            x3, [fp, #-8]
    // 0xbf0438: ArrayStore: r3[0] = r4  ; List_8
    //     0xbf0438: stur            x4, [x3, #0x17]
    // 0xbf043c: LoadField: r3 = r2->field_f
    //     0xbf043c: ldur            w3, [x2, #0xf]
    // 0xbf0440: DecompressPointer r3
    //     0xbf0440: add             x3, x3, HEAP, lsl #32
    // 0xbf0444: LoadField: r0 = r1->field_f
    //     0xbf0444: ldur            w0, [x1, #0xf]
    // 0xbf0448: DecompressPointer r0
    //     0xbf0448: add             x0, x0, HEAP, lsl #32
    // 0xbf044c: cmp             w0, NULL
    // 0xbf0450: b.eq            #0xbf0490
    // 0xbf0454: StoreField: r3->field_13 = r0
    //     0xbf0454: stur            w0, [x3, #0x13]
    //     0xbf0458: ldurb           w16, [x3, #-1]
    //     0xbf045c: ldurb           w17, [x0, #-1]
    //     0xbf0460: and             x16, x17, x16, lsr #2
    //     0xbf0464: tst             x16, HEAP, lsr #32
    //     0xbf0468: b.eq            #0xbf0470
    //     0xbf046c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xbf0470: r0 = Null
    //     0xbf0470: mov             x0, NULL
    // 0xbf0474: LeaveFrame
    //     0xbf0474: mov             SP, fp
    //     0xbf0478: ldp             fp, lr, [SP], #0x10
    // 0xbf047c: ret
    //     0xbf047c: ret             
    // 0xbf0480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf0480: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0484: b               #0xbf0390
    // 0xbf0488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0488: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf048c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf048c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf0490: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf0490: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3989, size: 0x2c, field offset: 0xc
//   const constructor, 
class SelectSizeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80ba0, size: 0x48
    // 0xc80ba0: EnterFrame
    //     0xc80ba0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80ba4: mov             fp, SP
    // 0xc80ba8: AllocStack(0x8)
    //     0xc80ba8: sub             SP, SP, #8
    // 0xc80bac: CheckStackOverflow
    //     0xc80bac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80bb0: cmp             SP, x16
    //     0xc80bb4: b.ls            #0xc80be0
    // 0xc80bb8: r1 = <SelectSizeBottomSheet>
    //     0xc80bb8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48458] TypeArguments: <SelectSizeBottomSheet>
    //     0xc80bbc: ldr             x1, [x1, #0x458]
    // 0xc80bc0: r0 = _SelectSizeBottomSheetState()
    //     0xc80bc0: bl              #0xc80be8  ; Allocate_SelectSizeBottomSheetStateStub -> _SelectSizeBottomSheetState (size=0x30)
    // 0xc80bc4: mov             x1, x0
    // 0xc80bc8: stur            x0, [fp, #-8]
    // 0xc80bcc: r0 = _SelectSizeBottomSheetState()
    //     0xc80bcc: bl              #0xc7bc9c  ; [package:customer_app/app/presentation/views/basic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::_SelectSizeBottomSheetState
    // 0xc80bd0: ldur            x0, [fp, #-8]
    // 0xc80bd4: LeaveFrame
    //     0xc80bd4: mov             SP, fp
    //     0xc80bd8: ldp             fp, lr, [SP], #0x10
    // 0xc80bdc: ret
    //     0xc80bdc: ret             
    // 0xc80be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80be0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80be4: b               #0xc80bb8
  }
}
