// lib: , url: package:customer_app/app/presentation/controllers/login/login_controller.dart

// class id: 1049045, size: 0x8
class :: {
}

// class id: 1290, size: 0x48, field offset: 0x48
//   transformed mixin,
abstract class _LoginController&BaseController&CodeAutoFill extends BaseController
     with CodeAutoFill {
}

// class id: 1291, size: 0xcc, field offset: 0x48
class LoginController extends _LoginController&BaseController&CodeAutoFill {

  late LoginRepo loginRepo; // offset: 0x48

  _ postEventOtpVerification(/* No info */) {
    // ** addr: 0x9339f8, size: 0x35c
    // 0x9339f8: EnterFrame
    //     0x9339f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9339fc: mov             fp, SP
    // 0x933a00: AllocStack(0x58)
    //     0x933a00: sub             SP, SP, #0x58
    // 0x933a04: SetupParameters(LoginController this /* r1 => r2, fp-0x30 */)
    //     0x933a04: mov             x2, x1
    //     0x933a08: stur            x1, [fp, #-0x30]
    // 0x933a0c: CheckStackOverflow
    //     0x933a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x933a10: cmp             SP, x16
    //     0x933a14: b.ls            #0x933d40
    // 0x933a18: LoadField: r0 = r2->field_4f
    //     0x933a18: ldur            w0, [x2, #0x4f]
    // 0x933a1c: DecompressPointer r0
    //     0x933a1c: add             x0, x0, HEAP, lsl #32
    // 0x933a20: cmp             w0, NULL
    // 0x933a24: b.eq            #0x933a3c
    // 0x933a28: LoadField: r1 = r0->field_b
    //     0x933a28: ldur            w1, [x0, #0xb]
    // 0x933a2c: DecompressPointer r1
    //     0x933a2c: add             x1, x1, HEAP, lsl #32
    // 0x933a30: stur            x1, [fp, #-0x48]
    // 0x933a34: cmp             w1, NULL
    // 0x933a38: b.ne            #0x933c90
    // 0x933a3c: LoadField: r0 = r2->field_4b
    //     0x933a3c: ldur            w0, [x2, #0x4b]
    // 0x933a40: DecompressPointer r0
    //     0x933a40: add             x0, x0, HEAP, lsl #32
    // 0x933a44: cmp             w0, NULL
    // 0x933a48: b.ne            #0x933a58
    // 0x933a4c: mov             x1, x2
    // 0x933a50: r0 = ""
    //     0x933a50: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x933a54: b               #0x933b8c
    // 0x933a58: LoadField: r1 = r0->field_b
    //     0x933a58: ldur            w1, [x0, #0xb]
    // 0x933a5c: DecompressPointer r1
    //     0x933a5c: add             x1, x1, HEAP, lsl #32
    // 0x933a60: cmp             w1, NULL
    // 0x933a64: b.ne            #0x933a70
    // 0x933a68: r0 = ""
    //     0x933a68: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x933a6c: b               #0x933b88
    // 0x933a70: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x933a70: ldur            w3, [x1, #0x17]
    // 0x933a74: DecompressPointer r3
    //     0x933a74: add             x3, x3, HEAP, lsl #32
    // 0x933a78: stur            x3, [fp, #-0x28]
    // 0x933a7c: LoadField: r4 = r3->field_b
    //     0x933a7c: ldur            w4, [x3, #0xb]
    // 0x933a80: stur            x4, [fp, #-0x20]
    // 0x933a84: r0 = LoadInt32Instr(r4)
    //     0x933a84: sbfx            x0, x4, #1, #0x1f
    // 0x933a88: r6 = 0
    //     0x933a88: movz            x6, #0
    // 0x933a8c: r5 = ""
    //     0x933a8c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x933a90: stur            x6, [fp, #-0x10]
    // 0x933a94: stur            x5, [fp, #-0x18]
    // 0x933a98: CheckStackOverflow
    //     0x933a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x933a9c: cmp             SP, x16
    //     0x933aa0: b.ls            #0x933d48
    // 0x933aa4: cmp             x6, x0
    // 0x933aa8: b.ge            #0x933b84
    // 0x933aac: mov             x1, x6
    // 0x933ab0: cmp             x1, x0
    // 0x933ab4: b.hs            #0x933d50
    // 0x933ab8: LoadField: r0 = r3->field_f
    //     0x933ab8: ldur            w0, [x3, #0xf]
    // 0x933abc: DecompressPointer r0
    //     0x933abc: add             x0, x0, HEAP, lsl #32
    // 0x933ac0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x933ac0: add             x16, x0, x6, lsl #2
    //     0x933ac4: ldur            w1, [x16, #0xf]
    // 0x933ac8: DecompressPointer r1
    //     0x933ac8: add             x1, x1, HEAP, lsl #32
    // 0x933acc: stur            x1, [fp, #-8]
    // 0x933ad0: r0 = LoadClassIdInstr(r5)
    //     0x933ad0: ldur            x0, [x5, #-1]
    //     0x933ad4: ubfx            x0, x0, #0xc, #0x14
    // 0x933ad8: r16 = ""
    //     0x933ad8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x933adc: stp             x16, x5, [SP]
    // 0x933ae0: mov             lr, x0
    // 0x933ae4: ldr             lr, [x21, lr, lsl #3]
    // 0x933ae8: blr             lr
    // 0x933aec: tbnz            w0, #4, #0x933b04
    // 0x933af0: ldur            x0, [fp, #-8]
    // 0x933af4: LoadField: r1 = r0->field_b
    //     0x933af4: ldur            w1, [x0, #0xb]
    // 0x933af8: DecompressPointer r1
    //     0x933af8: add             x1, x1, HEAP, lsl #32
    // 0x933afc: mov             x5, x1
    // 0x933b00: b               #0x933b50
    // 0x933b04: ldur            x0, [fp, #-8]
    // 0x933b08: ldur            x3, [fp, #-0x18]
    // 0x933b0c: r1 = Null
    //     0x933b0c: mov             x1, NULL
    // 0x933b10: r2 = 6
    //     0x933b10: movz            x2, #0x6
    // 0x933b14: r0 = AllocateArray()
    //     0x933b14: bl              #0x16f7198  ; AllocateArrayStub
    // 0x933b18: mov             x1, x0
    // 0x933b1c: ldur            x0, [fp, #-0x18]
    // 0x933b20: StoreField: r1->field_f = r0
    //     0x933b20: stur            w0, [x1, #0xf]
    // 0x933b24: r16 = "\',\'"
    //     0x933b24: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d958] "\',\'"
    //     0x933b28: ldr             x16, [x16, #0x958]
    // 0x933b2c: StoreField: r1->field_13 = r16
    //     0x933b2c: stur            w16, [x1, #0x13]
    // 0x933b30: ldur            x0, [fp, #-8]
    // 0x933b34: LoadField: r2 = r0->field_b
    //     0x933b34: ldur            w2, [x0, #0xb]
    // 0x933b38: DecompressPointer r2
    //     0x933b38: add             x2, x2, HEAP, lsl #32
    // 0x933b3c: ArrayStore: r1[0] = r2  ; List_4
    //     0x933b3c: stur            w2, [x1, #0x17]
    // 0x933b40: str             x1, [SP]
    // 0x933b44: r0 = _interpolate()
    //     0x933b44: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x933b48: mov             x1, x0
    // 0x933b4c: mov             x5, x1
    // 0x933b50: ldur            x1, [fp, #-0x28]
    // 0x933b54: ldur            x2, [fp, #-0x20]
    // 0x933b58: LoadField: r0 = r1->field_b
    //     0x933b58: ldur            w0, [x1, #0xb]
    // 0x933b5c: cmp             w0, w2
    // 0x933b60: b.ne            #0x933d24
    // 0x933b64: ldur            x3, [fp, #-0x10]
    // 0x933b68: add             x6, x3, #1
    // 0x933b6c: r3 = LoadInt32Instr(r0)
    //     0x933b6c: sbfx            x3, x0, #1, #0x1f
    // 0x933b70: mov             x0, x3
    // 0x933b74: mov             x4, x2
    // 0x933b78: ldur            x2, [fp, #-0x30]
    // 0x933b7c: mov             x3, x1
    // 0x933b80: b               #0x933a90
    // 0x933b84: mov             x0, x5
    // 0x933b88: ldur            x1, [fp, #-0x30]
    // 0x933b8c: stur            x0, [fp, #-0x18]
    // 0x933b90: LoadField: r2 = r1->field_53
    //     0x933b90: ldur            w2, [x1, #0x53]
    // 0x933b94: DecompressPointer r2
    //     0x933b94: add             x2, x2, HEAP, lsl #32
    // 0x933b98: stur            x2, [fp, #-8]
    // 0x933b9c: LoadField: r3 = r1->field_4b
    //     0x933b9c: ldur            w3, [x1, #0x4b]
    // 0x933ba0: DecompressPointer r3
    //     0x933ba0: add             x3, x3, HEAP, lsl #32
    // 0x933ba4: cmp             w3, NULL
    // 0x933ba8: b.ne            #0x933bb4
    // 0x933bac: r0 = Null
    //     0x933bac: mov             x0, NULL
    // 0x933bb0: b               #0x933bf0
    // 0x933bb4: LoadField: r4 = r3->field_b
    //     0x933bb4: ldur            w4, [x3, #0xb]
    // 0x933bb8: DecompressPointer r4
    //     0x933bb8: add             x4, x4, HEAP, lsl #32
    // 0x933bbc: cmp             w4, NULL
    // 0x933bc0: b.ne            #0x933bcc
    // 0x933bc4: r0 = Null
    //     0x933bc4: mov             x0, NULL
    // 0x933bc8: b               #0x933bec
    // 0x933bcc: LoadField: r3 = r4->field_7
    //     0x933bcc: ldur            w3, [x4, #7]
    // 0x933bd0: DecompressPointer r3
    //     0x933bd0: add             x3, x3, HEAP, lsl #32
    // 0x933bd4: cmp             w3, NULL
    // 0x933bd8: b.ne            #0x933be4
    // 0x933bdc: r0 = Null
    //     0x933bdc: mov             x0, NULL
    // 0x933be0: b               #0x933bec
    // 0x933be4: stp             x3, NULL, [SP]
    // 0x933be8: r0 = _Double.fromInteger()
    //     0x933be8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x933bec: ldur            x1, [fp, #-0x30]
    // 0x933bf0: stur            x0, [fp, #-0x38]
    // 0x933bf4: LoadField: r2 = r1->field_4b
    //     0x933bf4: ldur            w2, [x1, #0x4b]
    // 0x933bf8: DecompressPointer r2
    //     0x933bf8: add             x2, x2, HEAP, lsl #32
    // 0x933bfc: cmp             w2, NULL
    // 0x933c00: b.ne            #0x933c0c
    // 0x933c04: r4 = Null
    //     0x933c04: mov             x4, NULL
    // 0x933c08: b               #0x933c30
    // 0x933c0c: LoadField: r3 = r2->field_b
    //     0x933c0c: ldur            w3, [x2, #0xb]
    // 0x933c10: DecompressPointer r3
    //     0x933c10: add             x3, x3, HEAP, lsl #32
    // 0x933c14: cmp             w3, NULL
    // 0x933c18: b.ne            #0x933c24
    // 0x933c1c: r2 = Null
    //     0x933c1c: mov             x2, NULL
    // 0x933c20: b               #0x933c2c
    // 0x933c24: LoadField: r2 = r3->field_b
    //     0x933c24: ldur            w2, [x3, #0xb]
    // 0x933c28: DecompressPointer r2
    //     0x933c28: add             x2, x2, HEAP, lsl #32
    // 0x933c2c: mov             x4, x2
    // 0x933c30: ldur            x3, [fp, #-8]
    // 0x933c34: ldur            x2, [fp, #-0x18]
    // 0x933c38: stur            x4, [fp, #-0x20]
    // 0x933c3c: r0 = EventData()
    //     0x933c3c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x933c40: mov             x1, x0
    // 0x933c44: ldur            x0, [fp, #-8]
    // 0x933c48: stur            x1, [fp, #-0x40]
    // 0x933c4c: StoreField: r1->field_13 = r0
    //     0x933c4c: stur            w0, [x1, #0x13]
    // 0x933c50: ldur            x0, [fp, #-0x38]
    // 0x933c54: StoreField: r1->field_3f = r0
    //     0x933c54: stur            w0, [x1, #0x3f]
    // 0x933c58: ldur            x0, [fp, #-0x20]
    // 0x933c5c: StoreField: r1->field_43 = r0
    //     0x933c5c: stur            w0, [x1, #0x43]
    // 0x933c60: ldur            x0, [fp, #-0x18]
    // 0x933c64: StoreField: r1->field_47 = r0
    //     0x933c64: stur            w0, [x1, #0x47]
    // 0x933c68: r0 = EventsRequest()
    //     0x933c68: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x933c6c: r2 = "otp_verification"
    //     0x933c6c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d960] "otp_verification"
    //     0x933c70: ldr             x2, [x2, #0x960]
    // 0x933c74: StoreField: r0->field_7 = r2
    //     0x933c74: stur            w2, [x0, #7]
    // 0x933c78: ldur            x1, [fp, #-0x40]
    // 0x933c7c: StoreField: r0->field_b = r1
    //     0x933c7c: stur            w1, [x0, #0xb]
    // 0x933c80: ldur            x1, [fp, #-0x30]
    // 0x933c84: mov             x2, x0
    // 0x933c88: r0 = postEvents()
    //     0x933c88: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x933c8c: b               #0x933d14
    // 0x933c90: mov             x3, x2
    // 0x933c94: r2 = "otp_verification"
    //     0x933c94: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d960] "otp_verification"
    //     0x933c98: ldr             x2, [x2, #0x960]
    // 0x933c9c: LoadField: r4 = r0->field_f
    //     0x933c9c: ldur            w4, [x0, #0xf]
    // 0x933ca0: DecompressPointer r4
    //     0x933ca0: add             x4, x4, HEAP, lsl #32
    // 0x933ca4: stur            x4, [fp, #-0x20]
    // 0x933ca8: LoadField: r5 = r3->field_53
    //     0x933ca8: ldur            w5, [x3, #0x53]
    // 0x933cac: DecompressPointer r5
    //     0x933cac: add             x5, x5, HEAP, lsl #32
    // 0x933cb0: stur            x5, [fp, #-0x18]
    // 0x933cb4: LoadField: r6 = r0->field_7
    //     0x933cb4: ldur            w6, [x0, #7]
    // 0x933cb8: DecompressPointer r6
    //     0x933cb8: add             x6, x6, HEAP, lsl #32
    // 0x933cbc: stur            x6, [fp, #-8]
    // 0x933cc0: r0 = EventData()
    //     0x933cc0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x933cc4: mov             x1, x0
    // 0x933cc8: ldur            x0, [fp, #-0x18]
    // 0x933ccc: stur            x1, [fp, #-0x38]
    // 0x933cd0: StoreField: r1->field_13 = r0
    //     0x933cd0: stur            w0, [x1, #0x13]
    // 0x933cd4: ldur            x0, [fp, #-8]
    // 0x933cd8: StoreField: r1->field_3f = r0
    //     0x933cd8: stur            w0, [x1, #0x3f]
    // 0x933cdc: ldur            x0, [fp, #-0x48]
    // 0x933ce0: StoreField: r1->field_43 = r0
    //     0x933ce0: stur            w0, [x1, #0x43]
    // 0x933ce4: ldur            x0, [fp, #-0x20]
    // 0x933ce8: StoreField: r1->field_47 = r0
    //     0x933ce8: stur            w0, [x1, #0x47]
    // 0x933cec: r0 = EventsRequest()
    //     0x933cec: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x933cf0: mov             x1, x0
    // 0x933cf4: r0 = "otp_verification"
    //     0x933cf4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d960] "otp_verification"
    //     0x933cf8: ldr             x0, [x0, #0x960]
    // 0x933cfc: StoreField: r1->field_7 = r0
    //     0x933cfc: stur            w0, [x1, #7]
    // 0x933d00: ldur            x0, [fp, #-0x38]
    // 0x933d04: StoreField: r1->field_b = r0
    //     0x933d04: stur            w0, [x1, #0xb]
    // 0x933d08: mov             x2, x1
    // 0x933d0c: ldur            x1, [fp, #-0x30]
    // 0x933d10: r0 = postEvents()
    //     0x933d10: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x933d14: r0 = Null
    //     0x933d14: mov             x0, NULL
    // 0x933d18: LeaveFrame
    //     0x933d18: mov             SP, fp
    //     0x933d1c: ldp             fp, lr, [SP], #0x10
    // 0x933d20: ret
    //     0x933d20: ret             
    // 0x933d24: r0 = ConcurrentModificationError()
    //     0x933d24: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x933d28: mov             x1, x0
    // 0x933d2c: ldur            x0, [fp, #-0x28]
    // 0x933d30: StoreField: r1->field_b = r0
    //     0x933d30: stur            w0, [x1, #0xb]
    // 0x933d34: mov             x0, x1
    // 0x933d38: r0 = Throw()
    //     0x933d38: bl              #0x16f5420  ; ThrowStub
    // 0x933d3c: brk             #0
    // 0x933d40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x933d40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x933d44: b               #0x933a18
    // 0x933d48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x933d48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x933d4c: b               #0x933aa4
    // 0x933d50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x933d50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getOtp(/* No info */) async {
    // ** addr: 0x131dccc, size: 0x100
    // 0x131dccc: EnterFrame
    //     0x131dccc: stp             fp, lr, [SP, #-0x10]!
    //     0x131dcd0: mov             fp, SP
    // 0x131dcd4: AllocStack(0x38)
    //     0x131dcd4: sub             SP, SP, #0x38
    // 0x131dcd8: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x131dcd8: stur            NULL, [fp, #-8]
    //     0x131dcdc: stur            x1, [fp, #-0x10]
    //     0x131dce0: stur            x2, [fp, #-0x18]
    // 0x131dce4: CheckStackOverflow
    //     0x131dce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131dce8: cmp             SP, x16
    //     0x131dcec: b.ls            #0x131ddc4
    // 0x131dcf0: r1 = 3
    //     0x131dcf0: movz            x1, #0x3
    // 0x131dcf4: r0 = AllocateContext()
    //     0x131dcf4: bl              #0x16f6108  ; AllocateContextStub
    // 0x131dcf8: mov             x2, x0
    // 0x131dcfc: ldur            x1, [fp, #-0x10]
    // 0x131dd00: stur            x2, [fp, #-0x20]
    // 0x131dd04: StoreField: r2->field_f = r1
    //     0x131dd04: stur            w1, [x2, #0xf]
    // 0x131dd08: ldur            x0, [fp, #-0x18]
    // 0x131dd0c: StoreField: r2->field_13 = r0
    //     0x131dd0c: stur            w0, [x2, #0x13]
    // 0x131dd10: InitAsync() -> Future
    //     0x131dd10: mov             x0, NULL
    //     0x131dd14: bl              #0x6326e0  ; InitAsyncStub
    // 0x131dd18: r1 = Null
    //     0x131dd18: mov             x1, NULL
    // 0x131dd1c: r0 = SmsAutoFill()
    //     0x131dd1c: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0x131dd20: mov             x1, x0
    // 0x131dd24: r0 = getAppSignature()
    //     0x131dd24: bl              #0x12c8600  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::getAppSignature
    // 0x131dd28: mov             x1, x0
    // 0x131dd2c: stur            x1, [fp, #-0x18]
    // 0x131dd30: r0 = Await()
    //     0x131dd30: bl              #0x63248c  ; AwaitStub
    // 0x131dd34: ldur            x2, [fp, #-0x20]
    // 0x131dd38: ArrayStore: r2[0] = r0  ; List_4
    //     0x131dd38: stur            w0, [x2, #0x17]
    //     0x131dd3c: tbz             w0, #0, #0x131dd58
    //     0x131dd40: ldurb           w16, [x2, #-1]
    //     0x131dd44: ldurb           w17, [x0, #-1]
    //     0x131dd48: and             x16, x17, x16, lsr #2
    //     0x131dd4c: tst             x16, HEAP, lsr #32
    //     0x131dd50: b.eq            #0x131dd58
    //     0x131dd54: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x131dd58: LoadField: r0 = r2->field_13
    //     0x131dd58: ldur            w0, [x2, #0x13]
    // 0x131dd5c: DecompressPointer r0
    //     0x131dd5c: add             x0, x0, HEAP, lsl #32
    // 0x131dd60: ldur            x1, [fp, #-0x10]
    // 0x131dd64: StoreField: r1->field_a3 = r0
    //     0x131dd64: stur            w0, [x1, #0xa3]
    //     0x131dd68: ldurb           w16, [x1, #-1]
    //     0x131dd6c: ldurb           w17, [x0, #-1]
    //     0x131dd70: and             x16, x17, x16, lsr #2
    //     0x131dd74: tst             x16, HEAP, lsr #32
    //     0x131dd78: b.eq            #0x131dd80
    //     0x131dd7c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x131dd80: LoadField: r0 = r1->field_a7
    //     0x131dd80: ldur            w0, [x1, #0xa7]
    // 0x131dd84: DecompressPointer r0
    //     0x131dd84: add             x0, x0, HEAP, lsl #32
    // 0x131dd88: mov             x1, x0
    // 0x131dd8c: r0 = getConnectivityType()
    //     0x131dd8c: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x131dd90: ldur            x2, [fp, #-0x20]
    // 0x131dd94: r1 = Function '<anonymous closure>':.
    //     0x131dd94: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f50] AnonymousClosure: (0x131ddcc), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::getOtp (0x131dccc)
    //     0x131dd98: ldr             x1, [x1, #0xf50]
    // 0x131dd9c: stur            x0, [fp, #-0x10]
    // 0x131dda0: r0 = AllocateClosure()
    //     0x131dda0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131dda4: r16 = <Null?>
    //     0x131dda4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x131dda8: ldur            lr, [fp, #-0x10]
    // 0x131ddac: stp             lr, x16, [SP, #8]
    // 0x131ddb0: str             x0, [SP]
    // 0x131ddb4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x131ddb4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x131ddb8: r0 = then()
    //     0x131ddb8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x131ddbc: r0 = Null
    //     0x131ddbc: mov             x0, NULL
    // 0x131ddc0: r0 = ReturnAsyncNotFuture()
    //     0x131ddc0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x131ddc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131ddc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131ddc8: b               #0x131dcf0
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x131ddcc, size: 0x114
    // 0x131ddcc: EnterFrame
    //     0x131ddcc: stp             fp, lr, [SP, #-0x10]!
    //     0x131ddd0: mov             fp, SP
    // 0x131ddd4: AllocStack(0x40)
    //     0x131ddd4: sub             SP, SP, #0x40
    // 0x131ddd8: SetupParameters()
    //     0x131ddd8: ldr             x0, [fp, #0x18]
    //     0x131dddc: ldur            w3, [x0, #0x17]
    //     0x131dde0: add             x3, x3, HEAP, lsl #32
    //     0x131dde4: stur            x3, [fp, #-8]
    // 0x131dde8: CheckStackOverflow
    //     0x131dde8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131ddec: cmp             SP, x16
    //     0x131ddf0: b.ls            #0x131decc
    // 0x131ddf4: LoadField: r1 = r3->field_f
    //     0x131ddf4: ldur            w1, [x3, #0xf]
    // 0x131ddf8: DecompressPointer r1
    //     0x131ddf8: add             x1, x1, HEAP, lsl #32
    // 0x131ddfc: ldr             x0, [fp, #0x10]
    // 0x131de00: StoreField: r1->field_ab = r0
    //     0x131de00: stur            w0, [x1, #0xab]
    // 0x131de04: tbnz            w0, #4, #0x131debc
    // 0x131de08: r2 = ""
    //     0x131de08: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x131de0c: r0 = showErrorMessage()
    //     0x131de0c: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x131de10: ldur            x0, [fp, #-8]
    // 0x131de14: LoadField: r1 = r0->field_f
    //     0x131de14: ldur            w1, [x0, #0xf]
    // 0x131de18: DecompressPointer r1
    //     0x131de18: add             x1, x1, HEAP, lsl #32
    // 0x131de1c: LoadField: r2 = r1->field_47
    //     0x131de1c: ldur            w2, [x1, #0x47]
    // 0x131de20: DecompressPointer r2
    //     0x131de20: add             x2, x2, HEAP, lsl #32
    // 0x131de24: r16 = Sentinel
    //     0x131de24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x131de28: cmp             w2, w16
    // 0x131de2c: b.eq            #0x131ded4
    // 0x131de30: LoadField: r3 = r0->field_13
    //     0x131de30: ldur            w3, [x0, #0x13]
    // 0x131de34: DecompressPointer r3
    //     0x131de34: add             x3, x3, HEAP, lsl #32
    // 0x131de38: LoadField: r4 = r1->field_9b
    //     0x131de38: ldur            w4, [x1, #0x9b]
    // 0x131de3c: DecompressPointer r4
    //     0x131de3c: add             x4, x4, HEAP, lsl #32
    // 0x131de40: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x131de40: ldur            w5, [x0, #0x17]
    // 0x131de44: DecompressPointer r5
    //     0x131de44: add             x5, x5, HEAP, lsl #32
    // 0x131de48: mov             x1, x2
    // 0x131de4c: mov             x2, x3
    // 0x131de50: mov             x3, x4
    // 0x131de54: r0 = sendOtpOnNumber()
    //     0x131de54: bl              #0x131dee0  ; [package:customer_app/app/data/repositories/login/login_repo_impl.dart] LoginRepoImpl::sendOtpOnNumber
    // 0x131de58: mov             x3, x0
    // 0x131de5c: ldur            x0, [fp, #-8]
    // 0x131de60: stur            x3, [fp, #-0x18]
    // 0x131de64: LoadField: r4 = r0->field_f
    //     0x131de64: ldur            w4, [x0, #0xf]
    // 0x131de68: DecompressPointer r4
    //     0x131de68: add             x4, x4, HEAP, lsl #32
    // 0x131de6c: mov             x2, x4
    // 0x131de70: stur            x4, [fp, #-0x10]
    // 0x131de74: r1 = Function '_handleGeneralResponse@1161085715':.
    //     0x131de74: add             x1, PP, #0x36, lsl #12  ; [pp+0x36f58] AnonymousClosure: (0x131e128), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleGeneralResponse (0x131e164)
    //     0x131de78: ldr             x1, [x1, #0xf58]
    // 0x131de7c: r0 = AllocateClosure()
    //     0x131de7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131de80: ldur            x2, [fp, #-0x10]
    // 0x131de84: r1 = Function '_handleError@1161085715':.
    //     0x131de84: add             x1, PP, #0x23, lsl #12  ; [pp+0x23710] AnonymousClosure: (0x131e244), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleError (0x131e280)
    //     0x131de88: ldr             x1, [x1, #0x710]
    // 0x131de8c: stur            x0, [fp, #-8]
    // 0x131de90: r0 = AllocateClosure()
    //     0x131de90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131de94: r16 = <GeneralResponse>
    //     0x131de94: add             x16, PP, #0xa, lsl #12  ; [pp+0xac10] TypeArguments: <GeneralResponse>
    //     0x131de98: ldr             x16, [x16, #0xc10]
    // 0x131de9c: ldur            lr, [fp, #-0x10]
    // 0x131dea0: stp             lr, x16, [SP, #0x18]
    // 0x131dea4: ldur            x16, [fp, #-0x18]
    // 0x131dea8: stp             x0, x16, [SP, #8]
    // 0x131deac: ldur            x16, [fp, #-8]
    // 0x131deb0: str             x16, [SP]
    // 0x131deb4: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x131deb4: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x131deb8: r0 = callDataService()
    //     0x131deb8: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x131debc: r0 = Null
    //     0x131debc: mov             x0, NULL
    // 0x131dec0: LeaveFrame
    //     0x131dec0: mov             SP, fp
    //     0x131dec4: ldp             fp, lr, [SP], #0x10
    // 0x131dec8: ret
    //     0x131dec8: ret             
    // 0x131decc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131decc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131ded0: b               #0x131ddf4
    // 0x131ded4: r9 = loginRepo
    //     0x131ded4: add             x9, PP, #0x22, lsl #12  ; [pp+0x22378] Field <LoginController.loginRepo>: late (offset: 0x48)
    //     0x131ded8: ldr             x9, [x9, #0x378]
    // 0x131dedc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x131dedc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleGeneralResponse(dynamic, dynamic) {
    // ** addr: 0x131e128, size: 0x3c
    // 0x131e128: EnterFrame
    //     0x131e128: stp             fp, lr, [SP, #-0x10]!
    //     0x131e12c: mov             fp, SP
    // 0x131e130: ldr             x0, [fp, #0x18]
    // 0x131e134: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x131e134: ldur            w1, [x0, #0x17]
    // 0x131e138: DecompressPointer r1
    //     0x131e138: add             x1, x1, HEAP, lsl #32
    // 0x131e13c: CheckStackOverflow
    //     0x131e13c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e140: cmp             SP, x16
    //     0x131e144: b.ls            #0x131e15c
    // 0x131e148: ldr             x2, [fp, #0x10]
    // 0x131e14c: r0 = _handleGeneralResponse()
    //     0x131e14c: bl              #0x131e164  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleGeneralResponse
    // 0x131e150: LeaveFrame
    //     0x131e150: mov             SP, fp
    //     0x131e154: ldp             fp, lr, [SP], #0x10
    // 0x131e158: ret
    //     0x131e158: ret             
    // 0x131e15c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e15c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e160: b               #0x131e148
  }
  _ _handleGeneralResponse(/* No info */) {
    // ** addr: 0x131e164, size: 0x54
    // 0x131e164: EnterFrame
    //     0x131e164: stp             fp, lr, [SP, #-0x10]!
    //     0x131e168: mov             fp, SP
    // 0x131e16c: AllocStack(0x8)
    //     0x131e16c: sub             SP, SP, #8
    // 0x131e170: SetupParameters(LoginController this /* r1 => r0, fp-0x8 */)
    //     0x131e170: mov             x0, x1
    //     0x131e174: stur            x1, [fp, #-8]
    // 0x131e178: CheckStackOverflow
    //     0x131e178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e17c: cmp             SP, x16
    //     0x131e180: b.ls            #0x131e1b0
    // 0x131e184: mov             x1, x0
    // 0x131e188: r0 = generalResponse=()
    //     0x131e188: bl              #0x131e1b8  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::generalResponse=
    // 0x131e18c: ldur            x0, [fp, #-8]
    // 0x131e190: LoadField: r1 = r0->field_8b
    //     0x131e190: ldur            w1, [x0, #0x8b]
    // 0x131e194: DecompressPointer r1
    //     0x131e194: add             x1, x1, HEAP, lsl #32
    // 0x131e198: r2 = true
    //     0x131e198: add             x2, NULL, #0x20  ; true
    // 0x131e19c: r0 = value=()
    //     0x131e19c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x131e1a0: r0 = Null
    //     0x131e1a0: mov             x0, NULL
    // 0x131e1a4: LeaveFrame
    //     0x131e1a4: mov             SP, fp
    //     0x131e1a8: ldp             fp, lr, [SP], #0x10
    // 0x131e1ac: ret
    //     0x131e1ac: ret             
    // 0x131e1b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e1b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e1b4: b               #0x131e184
  }
  set _ generalResponse=(/* No info */) {
    // ** addr: 0x131e1b8, size: 0x8c
    // 0x131e1b8: EnterFrame
    //     0x131e1b8: stp             fp, lr, [SP, #-0x10]!
    //     0x131e1bc: mov             fp, SP
    // 0x131e1c0: AllocStack(0x10)
    //     0x131e1c0: sub             SP, SP, #0x10
    // 0x131e1c4: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x131e1c4: mov             x3, x2
    //     0x131e1c8: stur            x2, [fp, #-0x10]
    // 0x131e1cc: CheckStackOverflow
    //     0x131e1cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e1d0: cmp             SP, x16
    //     0x131e1d4: b.ls            #0x131e23c
    // 0x131e1d8: LoadField: r4 = r1->field_67
    //     0x131e1d8: ldur            w4, [x1, #0x67]
    // 0x131e1dc: DecompressPointer r4
    //     0x131e1dc: add             x4, x4, HEAP, lsl #32
    // 0x131e1e0: mov             x0, x3
    // 0x131e1e4: stur            x4, [fp, #-8]
    // 0x131e1e8: r2 = Null
    //     0x131e1e8: mov             x2, NULL
    // 0x131e1ec: r1 = Null
    //     0x131e1ec: mov             x1, NULL
    // 0x131e1f0: r4 = 60
    //     0x131e1f0: movz            x4, #0x3c
    // 0x131e1f4: branchIfSmi(r0, 0x131e200)
    //     0x131e1f4: tbz             w0, #0, #0x131e200
    // 0x131e1f8: r4 = LoadClassIdInstr(r0)
    //     0x131e1f8: ldur            x4, [x0, #-1]
    //     0x131e1fc: ubfx            x4, x4, #0xc, #0x14
    // 0x131e200: r17 = 5085
    //     0x131e200: movz            x17, #0x13dd
    // 0x131e204: cmp             x4, x17
    // 0x131e208: b.eq            #0x131e220
    // 0x131e20c: r8 = GeneralResponse
    //     0x131e20c: add             x8, PP, #0x36, lsl #12  ; [pp+0x36160] Type: GeneralResponse
    //     0x131e210: ldr             x8, [x8, #0x160]
    // 0x131e214: r3 = Null
    //     0x131e214: add             x3, PP, #0x36, lsl #12  ; [pp+0x36f60] Null
    //     0x131e218: ldr             x3, [x3, #0xf60]
    // 0x131e21c: r0 = DefaultTypeTest()
    //     0x131e21c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x131e220: ldur            x1, [fp, #-8]
    // 0x131e224: ldur            x2, [fp, #-0x10]
    // 0x131e228: r0 = value=()
    //     0x131e228: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x131e22c: ldur            x0, [fp, #-0x10]
    // 0x131e230: LeaveFrame
    //     0x131e230: mov             SP, fp
    //     0x131e234: ldp             fp, lr, [SP], #0x10
    // 0x131e238: ret
    //     0x131e238: ret             
    // 0x131e23c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e23c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e240: b               #0x131e1d8
  }
  [closure] dynamic _handleError(dynamic, Exception) {
    // ** addr: 0x131e244, size: 0x3c
    // 0x131e244: EnterFrame
    //     0x131e244: stp             fp, lr, [SP, #-0x10]!
    //     0x131e248: mov             fp, SP
    // 0x131e24c: ldr             x0, [fp, #0x18]
    // 0x131e250: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x131e250: ldur            w1, [x0, #0x17]
    // 0x131e254: DecompressPointer r1
    //     0x131e254: add             x1, x1, HEAP, lsl #32
    // 0x131e258: CheckStackOverflow
    //     0x131e258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e25c: cmp             SP, x16
    //     0x131e260: b.ls            #0x131e278
    // 0x131e264: ldr             x2, [fp, #0x10]
    // 0x131e268: r0 = _handleError()
    //     0x131e268: bl              #0x131e280  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleError
    // 0x131e26c: LeaveFrame
    //     0x131e26c: mov             SP, fp
    //     0x131e270: ldp             fp, lr, [SP], #0x10
    // 0x131e274: ret
    //     0x131e274: ret             
    // 0x131e278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e278: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e27c: b               #0x131e264
  }
  _ _handleError(/* No info */) {
    // ** addr: 0x131e280, size: 0x88
    // 0x131e280: EnterFrame
    //     0x131e280: stp             fp, lr, [SP, #-0x10]!
    //     0x131e284: mov             fp, SP
    // 0x131e288: AllocStack(0x10)
    //     0x131e288: sub             SP, SP, #0x10
    // 0x131e28c: SetupParameters(LoginController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x131e28c: mov             x4, x1
    //     0x131e290: mov             x3, x2
    //     0x131e294: stur            x1, [fp, #-8]
    //     0x131e298: stur            x2, [fp, #-0x10]
    // 0x131e29c: CheckStackOverflow
    //     0x131e29c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e2a0: cmp             SP, x16
    //     0x131e2a4: b.ls            #0x131e300
    // 0x131e2a8: mov             x0, x3
    // 0x131e2ac: r2 = Null
    //     0x131e2ac: mov             x2, NULL
    // 0x131e2b0: r1 = Null
    //     0x131e2b0: mov             x1, NULL
    // 0x131e2b4: r4 = LoadClassIdInstr(r0)
    //     0x131e2b4: ldur            x4, [x0, #-1]
    //     0x131e2b8: ubfx            x4, x4, #0xc, #0x14
    // 0x131e2bc: r17 = -5018
    //     0x131e2bc: movn            x17, #0x1399
    // 0x131e2c0: add             x4, x4, x17
    // 0x131e2c4: cmp             x4, #8
    // 0x131e2c8: b.ls            #0x131e2dc
    // 0x131e2cc: r8 = BaseException
    //     0x131e2cc: ldr             x8, [PP, #0x7e90]  ; [pp+0x7e90] Type: BaseException
    // 0x131e2d0: r3 = Null
    //     0x131e2d0: add             x3, PP, #0x23, lsl #12  ; [pp+0x23718] Null
    //     0x131e2d4: ldr             x3, [x3, #0x718]
    // 0x131e2d8: r0 = DefaultTypeTest()
    //     0x131e2d8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x131e2dc: ldur            x0, [fp, #-0x10]
    // 0x131e2e0: LoadField: r2 = r0->field_7
    //     0x131e2e0: ldur            w2, [x0, #7]
    // 0x131e2e4: DecompressPointer r2
    //     0x131e2e4: add             x2, x2, HEAP, lsl #32
    // 0x131e2e8: ldur            x1, [fp, #-8]
    // 0x131e2ec: r0 = showErrorMessage()
    //     0x131e2ec: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x131e2f0: r0 = Null
    //     0x131e2f0: mov             x0, NULL
    // 0x131e2f4: LeaveFrame
    //     0x131e2f4: mov             SP, fp
    //     0x131e2f8: ldp             fp, lr, [SP], #0x10
    // 0x131e2fc: ret
    //     0x131e2fc: ret             
    // 0x131e300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e300: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e304: b               #0x131e2a8
  }
  _ postCheckoutMobileNumberFilledEvent(/* No info */) {
    // ** addr: 0x131e308, size: 0x52c
    // 0x131e308: EnterFrame
    //     0x131e308: stp             fp, lr, [SP, #-0x10]!
    //     0x131e30c: mov             fp, SP
    // 0x131e310: AllocStack(0x58)
    //     0x131e310: sub             SP, SP, #0x58
    // 0x131e314: SetupParameters(LoginController this /* r1 => r1, fp-0x8 */)
    //     0x131e314: stur            x1, [fp, #-8]
    // 0x131e318: CheckStackOverflow
    //     0x131e318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e31c: cmp             SP, x16
    //     0x131e320: b.ls            #0x131e820
    // 0x131e324: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x131e324: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x131e328: ldr             x0, [x0, #0x1c80]
    //     0x131e32c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131e330: cmp             w0, w16
    //     0x131e334: b.ne            #0x131e340
    //     0x131e338: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x131e33c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131e340: r0 = GetNavigation.arguments()
    //     0x131e340: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x131e344: stur            x0, [fp, #-0x10]
    // 0x131e348: cmp             w0, NULL
    // 0x131e34c: b.eq            #0x131e4f4
    // 0x131e350: ldur            x1, [fp, #-8]
    // 0x131e354: r16 = "bag_data"
    //     0x131e354: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d00] "bag_data"
    //     0x131e358: ldr             x16, [x16, #0xd00]
    // 0x131e35c: stp             x16, x0, [SP]
    // 0x131e360: r4 = 0
    //     0x131e360: movz            x4, #0
    // 0x131e364: ldr             x0, [SP, #8]
    // 0x131e368: r16 = UnlinkedCall_0x613b5c
    //     0x131e368: add             x16, PP, #0x36, lsl #12  ; [pp+0x36fa0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x131e36c: add             x16, x16, #0xfa0
    // 0x131e370: ldp             x5, lr, [x16]
    // 0x131e374: blr             lr
    // 0x131e378: mov             x3, x0
    // 0x131e37c: r2 = Null
    //     0x131e37c: mov             x2, NULL
    // 0x131e380: r1 = Null
    //     0x131e380: mov             x1, NULL
    // 0x131e384: stur            x3, [fp, #-0x18]
    // 0x131e388: r4 = 60
    //     0x131e388: movz            x4, #0x3c
    // 0x131e38c: branchIfSmi(r0, 0x131e398)
    //     0x131e38c: tbz             w0, #0, #0x131e398
    // 0x131e390: r4 = LoadClassIdInstr(r0)
    //     0x131e390: ldur            x4, [x0, #-1]
    //     0x131e394: ubfx            x4, x4, #0xc, #0x14
    // 0x131e398: r17 = 5419
    //     0x131e398: movz            x17, #0x152b
    // 0x131e39c: cmp             x4, x17
    // 0x131e3a0: b.eq            #0x131e3b8
    // 0x131e3a4: r8 = BagResponse?
    //     0x131e3a4: add             x8, PP, #0x11, lsl #12  ; [pp+0x11d18] Type: BagResponse?
    //     0x131e3a8: ldr             x8, [x8, #0xd18]
    // 0x131e3ac: r3 = Null
    //     0x131e3ac: add             x3, PP, #0x36, lsl #12  ; [pp+0x36fb0] Null
    //     0x131e3b0: ldr             x3, [x3, #0xfb0]
    // 0x131e3b4: r0 = DefaultNullableTypeTest()
    //     0x131e3b4: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x131e3b8: ldur            x0, [fp, #-0x18]
    // 0x131e3bc: ldur            x1, [fp, #-8]
    // 0x131e3c0: StoreField: r1->field_4b = r0
    //     0x131e3c0: stur            w0, [x1, #0x4b]
    //     0x131e3c4: ldurb           w16, [x1, #-1]
    //     0x131e3c8: ldurb           w17, [x0, #-1]
    //     0x131e3cc: and             x16, x17, x16, lsr #2
    //     0x131e3d0: tst             x16, HEAP, lsr #32
    //     0x131e3d4: b.eq            #0x131e3dc
    //     0x131e3d8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x131e3dc: ldur            x16, [fp, #-0x10]
    // 0x131e3e0: r30 = "previousScreenSource"
    //     0x131e3e0: add             lr, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x131e3e4: ldr             lr, [lr, #0x448]
    // 0x131e3e8: stp             lr, x16, [SP]
    // 0x131e3ec: r4 = 0
    //     0x131e3ec: movz            x4, #0
    // 0x131e3f0: ldr             x0, [SP, #8]
    // 0x131e3f4: r16 = UnlinkedCall_0x613b5c
    //     0x131e3f4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36fc0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x131e3f8: add             x16, x16, #0xfc0
    // 0x131e3fc: ldp             x5, lr, [x16]
    // 0x131e400: blr             lr
    // 0x131e404: mov             x3, x0
    // 0x131e408: r2 = Null
    //     0x131e408: mov             x2, NULL
    // 0x131e40c: r1 = Null
    //     0x131e40c: mov             x1, NULL
    // 0x131e410: stur            x3, [fp, #-0x18]
    // 0x131e414: r4 = 60
    //     0x131e414: movz            x4, #0x3c
    // 0x131e418: branchIfSmi(r0, 0x131e424)
    //     0x131e418: tbz             w0, #0, #0x131e424
    // 0x131e41c: r4 = LoadClassIdInstr(r0)
    //     0x131e41c: ldur            x4, [x0, #-1]
    //     0x131e420: ubfx            x4, x4, #0xc, #0x14
    // 0x131e424: sub             x4, x4, #0x5e
    // 0x131e428: cmp             x4, #1
    // 0x131e42c: b.ls            #0x131e440
    // 0x131e430: r8 = String?
    //     0x131e430: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x131e434: r3 = Null
    //     0x131e434: add             x3, PP, #0x36, lsl #12  ; [pp+0x36fd0] Null
    //     0x131e438: ldr             x3, [x3, #0xfd0]
    // 0x131e43c: r0 = String?()
    //     0x131e43c: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x131e440: ldur            x0, [fp, #-0x18]
    // 0x131e444: ldur            x1, [fp, #-8]
    // 0x131e448: StoreField: r1->field_53 = r0
    //     0x131e448: stur            w0, [x1, #0x53]
    //     0x131e44c: ldurb           w16, [x1, #-1]
    //     0x131e450: ldurb           w17, [x0, #-1]
    //     0x131e454: and             x16, x17, x16, lsr #2
    //     0x131e458: tst             x16, HEAP, lsr #32
    //     0x131e45c: b.eq            #0x131e464
    //     0x131e460: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x131e464: ldur            x16, [fp, #-0x10]
    // 0x131e468: r30 = "checkout_event_data"
    //     0x131e468: add             lr, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0x131e46c: ldr             lr, [lr, #0xd50]
    // 0x131e470: stp             lr, x16, [SP]
    // 0x131e474: r4 = 0
    //     0x131e474: movz            x4, #0
    // 0x131e478: ldr             x0, [SP, #8]
    // 0x131e47c: r16 = UnlinkedCall_0x613b5c
    //     0x131e47c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36fe0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x131e480: add             x16, x16, #0xfe0
    // 0x131e484: ldp             x5, lr, [x16]
    // 0x131e488: blr             lr
    // 0x131e48c: mov             x3, x0
    // 0x131e490: r2 = Null
    //     0x131e490: mov             x2, NULL
    // 0x131e494: r1 = Null
    //     0x131e494: mov             x1, NULL
    // 0x131e498: stur            x3, [fp, #-0x10]
    // 0x131e49c: r4 = 60
    //     0x131e49c: movz            x4, #0x3c
    // 0x131e4a0: branchIfSmi(r0, 0x131e4ac)
    //     0x131e4a0: tbz             w0, #0, #0x131e4ac
    // 0x131e4a4: r4 = LoadClassIdInstr(r0)
    //     0x131e4a4: ldur            x4, [x0, #-1]
    //     0x131e4a8: ubfx            x4, x4, #0xc, #0x14
    // 0x131e4ac: r17 = 5260
    //     0x131e4ac: movz            x17, #0x148c
    // 0x131e4b0: cmp             x4, x17
    // 0x131e4b4: b.eq            #0x131e4cc
    // 0x131e4b8: r8 = CheckoutEventData?
    //     0x131e4b8: add             x8, PP, #0x11, lsl #12  ; [pp+0x11d68] Type: CheckoutEventData?
    //     0x131e4bc: ldr             x8, [x8, #0xd68]
    // 0x131e4c0: r3 = Null
    //     0x131e4c0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36ff0] Null
    //     0x131e4c4: ldr             x3, [x3, #0xff0]
    // 0x131e4c8: r0 = DefaultNullableTypeTest()
    //     0x131e4c8: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x131e4cc: ldur            x0, [fp, #-0x10]
    // 0x131e4d0: ldur            x2, [fp, #-8]
    // 0x131e4d4: StoreField: r2->field_4f = r0
    //     0x131e4d4: stur            w0, [x2, #0x4f]
    //     0x131e4d8: ldurb           w16, [x2, #-1]
    //     0x131e4dc: ldurb           w17, [x0, #-1]
    //     0x131e4e0: and             x16, x17, x16, lsr #2
    //     0x131e4e4: tst             x16, HEAP, lsr #32
    //     0x131e4e8: b.eq            #0x131e4f0
    //     0x131e4ec: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x131e4f0: b               #0x131e4f8
    // 0x131e4f4: ldur            x2, [fp, #-8]
    // 0x131e4f8: LoadField: r0 = r2->field_4f
    //     0x131e4f8: ldur            w0, [x2, #0x4f]
    // 0x131e4fc: DecompressPointer r0
    //     0x131e4fc: add             x0, x0, HEAP, lsl #32
    // 0x131e500: cmp             w0, NULL
    // 0x131e504: b.eq            #0x131e51c
    // 0x131e508: LoadField: r1 = r0->field_b
    //     0x131e508: ldur            w1, [x0, #0xb]
    // 0x131e50c: DecompressPointer r1
    //     0x131e50c: add             x1, x1, HEAP, lsl #32
    // 0x131e510: stur            x1, [fp, #-0x48]
    // 0x131e514: cmp             w1, NULL
    // 0x131e518: b.ne            #0x131e770
    // 0x131e51c: LoadField: r0 = r2->field_4b
    //     0x131e51c: ldur            w0, [x2, #0x4b]
    // 0x131e520: DecompressPointer r0
    //     0x131e520: add             x0, x0, HEAP, lsl #32
    // 0x131e524: cmp             w0, NULL
    // 0x131e528: b.ne            #0x131e538
    // 0x131e52c: mov             x1, x2
    // 0x131e530: r0 = ""
    //     0x131e530: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x131e534: b               #0x131e66c
    // 0x131e538: LoadField: r1 = r0->field_b
    //     0x131e538: ldur            w1, [x0, #0xb]
    // 0x131e53c: DecompressPointer r1
    //     0x131e53c: add             x1, x1, HEAP, lsl #32
    // 0x131e540: cmp             w1, NULL
    // 0x131e544: b.ne            #0x131e550
    // 0x131e548: r0 = ""
    //     0x131e548: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x131e54c: b               #0x131e668
    // 0x131e550: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x131e550: ldur            w3, [x1, #0x17]
    // 0x131e554: DecompressPointer r3
    //     0x131e554: add             x3, x3, HEAP, lsl #32
    // 0x131e558: stur            x3, [fp, #-0x30]
    // 0x131e55c: LoadField: r4 = r3->field_b
    //     0x131e55c: ldur            w4, [x3, #0xb]
    // 0x131e560: stur            x4, [fp, #-0x28]
    // 0x131e564: r0 = LoadInt32Instr(r4)
    //     0x131e564: sbfx            x0, x4, #1, #0x1f
    // 0x131e568: r6 = 0
    //     0x131e568: movz            x6, #0
    // 0x131e56c: r5 = ""
    //     0x131e56c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x131e570: stur            x6, [fp, #-0x20]
    // 0x131e574: stur            x5, [fp, #-0x18]
    // 0x131e578: CheckStackOverflow
    //     0x131e578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131e57c: cmp             SP, x16
    //     0x131e580: b.ls            #0x131e828
    // 0x131e584: cmp             x6, x0
    // 0x131e588: b.ge            #0x131e664
    // 0x131e58c: mov             x1, x6
    // 0x131e590: cmp             x1, x0
    // 0x131e594: b.hs            #0x131e830
    // 0x131e598: LoadField: r0 = r3->field_f
    //     0x131e598: ldur            w0, [x3, #0xf]
    // 0x131e59c: DecompressPointer r0
    //     0x131e59c: add             x0, x0, HEAP, lsl #32
    // 0x131e5a0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0x131e5a0: add             x16, x0, x6, lsl #2
    //     0x131e5a4: ldur            w1, [x16, #0xf]
    // 0x131e5a8: DecompressPointer r1
    //     0x131e5a8: add             x1, x1, HEAP, lsl #32
    // 0x131e5ac: stur            x1, [fp, #-0x10]
    // 0x131e5b0: r0 = LoadClassIdInstr(r5)
    //     0x131e5b0: ldur            x0, [x5, #-1]
    //     0x131e5b4: ubfx            x0, x0, #0xc, #0x14
    // 0x131e5b8: r16 = ""
    //     0x131e5b8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x131e5bc: stp             x16, x5, [SP]
    // 0x131e5c0: mov             lr, x0
    // 0x131e5c4: ldr             lr, [x21, lr, lsl #3]
    // 0x131e5c8: blr             lr
    // 0x131e5cc: tbnz            w0, #4, #0x131e5e4
    // 0x131e5d0: ldur            x0, [fp, #-0x10]
    // 0x131e5d4: LoadField: r1 = r0->field_b
    //     0x131e5d4: ldur            w1, [x0, #0xb]
    // 0x131e5d8: DecompressPointer r1
    //     0x131e5d8: add             x1, x1, HEAP, lsl #32
    // 0x131e5dc: mov             x5, x1
    // 0x131e5e0: b               #0x131e630
    // 0x131e5e4: ldur            x0, [fp, #-0x10]
    // 0x131e5e8: ldur            x3, [fp, #-0x18]
    // 0x131e5ec: r1 = Null
    //     0x131e5ec: mov             x1, NULL
    // 0x131e5f0: r2 = 6
    //     0x131e5f0: movz            x2, #0x6
    // 0x131e5f4: r0 = AllocateArray()
    //     0x131e5f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x131e5f8: mov             x1, x0
    // 0x131e5fc: ldur            x0, [fp, #-0x18]
    // 0x131e600: StoreField: r1->field_f = r0
    //     0x131e600: stur            w0, [x1, #0xf]
    // 0x131e604: r16 = "\',\'"
    //     0x131e604: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d958] "\',\'"
    //     0x131e608: ldr             x16, [x16, #0x958]
    // 0x131e60c: StoreField: r1->field_13 = r16
    //     0x131e60c: stur            w16, [x1, #0x13]
    // 0x131e610: ldur            x0, [fp, #-0x10]
    // 0x131e614: LoadField: r2 = r0->field_b
    //     0x131e614: ldur            w2, [x0, #0xb]
    // 0x131e618: DecompressPointer r2
    //     0x131e618: add             x2, x2, HEAP, lsl #32
    // 0x131e61c: ArrayStore: r1[0] = r2  ; List_4
    //     0x131e61c: stur            w2, [x1, #0x17]
    // 0x131e620: str             x1, [SP]
    // 0x131e624: r0 = _interpolate()
    //     0x131e624: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x131e628: mov             x1, x0
    // 0x131e62c: mov             x5, x1
    // 0x131e630: ldur            x1, [fp, #-0x30]
    // 0x131e634: ldur            x2, [fp, #-0x28]
    // 0x131e638: LoadField: r0 = r1->field_b
    //     0x131e638: ldur            w0, [x1, #0xb]
    // 0x131e63c: cmp             w0, w2
    // 0x131e640: b.ne            #0x131e804
    // 0x131e644: ldur            x3, [fp, #-0x20]
    // 0x131e648: add             x6, x3, #1
    // 0x131e64c: r3 = LoadInt32Instr(r0)
    //     0x131e64c: sbfx            x3, x0, #1, #0x1f
    // 0x131e650: mov             x0, x3
    // 0x131e654: mov             x4, x2
    // 0x131e658: ldur            x2, [fp, #-8]
    // 0x131e65c: mov             x3, x1
    // 0x131e660: b               #0x131e570
    // 0x131e664: mov             x0, x5
    // 0x131e668: ldur            x1, [fp, #-8]
    // 0x131e66c: stur            x0, [fp, #-0x18]
    // 0x131e670: LoadField: r2 = r1->field_53
    //     0x131e670: ldur            w2, [x1, #0x53]
    // 0x131e674: DecompressPointer r2
    //     0x131e674: add             x2, x2, HEAP, lsl #32
    // 0x131e678: stur            x2, [fp, #-0x10]
    // 0x131e67c: LoadField: r3 = r1->field_4b
    //     0x131e67c: ldur            w3, [x1, #0x4b]
    // 0x131e680: DecompressPointer r3
    //     0x131e680: add             x3, x3, HEAP, lsl #32
    // 0x131e684: cmp             w3, NULL
    // 0x131e688: b.ne            #0x131e694
    // 0x131e68c: r0 = Null
    //     0x131e68c: mov             x0, NULL
    // 0x131e690: b               #0x131e6d0
    // 0x131e694: LoadField: r4 = r3->field_b
    //     0x131e694: ldur            w4, [x3, #0xb]
    // 0x131e698: DecompressPointer r4
    //     0x131e698: add             x4, x4, HEAP, lsl #32
    // 0x131e69c: cmp             w4, NULL
    // 0x131e6a0: b.ne            #0x131e6ac
    // 0x131e6a4: r0 = Null
    //     0x131e6a4: mov             x0, NULL
    // 0x131e6a8: b               #0x131e6cc
    // 0x131e6ac: LoadField: r3 = r4->field_7
    //     0x131e6ac: ldur            w3, [x4, #7]
    // 0x131e6b0: DecompressPointer r3
    //     0x131e6b0: add             x3, x3, HEAP, lsl #32
    // 0x131e6b4: cmp             w3, NULL
    // 0x131e6b8: b.ne            #0x131e6c4
    // 0x131e6bc: r0 = Null
    //     0x131e6bc: mov             x0, NULL
    // 0x131e6c0: b               #0x131e6cc
    // 0x131e6c4: stp             x3, NULL, [SP]
    // 0x131e6c8: r0 = _Double.fromInteger()
    //     0x131e6c8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x131e6cc: ldur            x1, [fp, #-8]
    // 0x131e6d0: stur            x0, [fp, #-0x38]
    // 0x131e6d4: LoadField: r2 = r1->field_4b
    //     0x131e6d4: ldur            w2, [x1, #0x4b]
    // 0x131e6d8: DecompressPointer r2
    //     0x131e6d8: add             x2, x2, HEAP, lsl #32
    // 0x131e6dc: cmp             w2, NULL
    // 0x131e6e0: b.ne            #0x131e6ec
    // 0x131e6e4: r4 = Null
    //     0x131e6e4: mov             x4, NULL
    // 0x131e6e8: b               #0x131e710
    // 0x131e6ec: LoadField: r3 = r2->field_b
    //     0x131e6ec: ldur            w3, [x2, #0xb]
    // 0x131e6f0: DecompressPointer r3
    //     0x131e6f0: add             x3, x3, HEAP, lsl #32
    // 0x131e6f4: cmp             w3, NULL
    // 0x131e6f8: b.ne            #0x131e704
    // 0x131e6fc: r2 = Null
    //     0x131e6fc: mov             x2, NULL
    // 0x131e700: b               #0x131e70c
    // 0x131e704: LoadField: r2 = r3->field_b
    //     0x131e704: ldur            w2, [x3, #0xb]
    // 0x131e708: DecompressPointer r2
    //     0x131e708: add             x2, x2, HEAP, lsl #32
    // 0x131e70c: mov             x4, x2
    // 0x131e710: ldur            x3, [fp, #-0x10]
    // 0x131e714: ldur            x2, [fp, #-0x18]
    // 0x131e718: stur            x4, [fp, #-0x28]
    // 0x131e71c: r0 = EventData()
    //     0x131e71c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x131e720: mov             x1, x0
    // 0x131e724: ldur            x0, [fp, #-0x10]
    // 0x131e728: stur            x1, [fp, #-0x40]
    // 0x131e72c: StoreField: r1->field_13 = r0
    //     0x131e72c: stur            w0, [x1, #0x13]
    // 0x131e730: ldur            x0, [fp, #-0x38]
    // 0x131e734: StoreField: r1->field_3f = r0
    //     0x131e734: stur            w0, [x1, #0x3f]
    // 0x131e738: ldur            x0, [fp, #-0x28]
    // 0x131e73c: StoreField: r1->field_43 = r0
    //     0x131e73c: stur            w0, [x1, #0x43]
    // 0x131e740: ldur            x0, [fp, #-0x18]
    // 0x131e744: StoreField: r1->field_47 = r0
    //     0x131e744: stur            w0, [x1, #0x47]
    // 0x131e748: r0 = EventsRequest()
    //     0x131e748: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x131e74c: r2 = "checkout_mobile_number_filled"
    //     0x131e74c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37000] "checkout_mobile_number_filled"
    //     0x131e750: ldr             x2, [x2]
    // 0x131e754: StoreField: r0->field_7 = r2
    //     0x131e754: stur            w2, [x0, #7]
    // 0x131e758: ldur            x1, [fp, #-0x40]
    // 0x131e75c: StoreField: r0->field_b = r1
    //     0x131e75c: stur            w1, [x0, #0xb]
    // 0x131e760: ldur            x1, [fp, #-8]
    // 0x131e764: mov             x2, x0
    // 0x131e768: r0 = postEvents()
    //     0x131e768: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x131e76c: b               #0x131e7f4
    // 0x131e770: mov             x3, x2
    // 0x131e774: r2 = "checkout_mobile_number_filled"
    //     0x131e774: add             x2, PP, #0x37, lsl #12  ; [pp+0x37000] "checkout_mobile_number_filled"
    //     0x131e778: ldr             x2, [x2]
    // 0x131e77c: LoadField: r4 = r0->field_f
    //     0x131e77c: ldur            w4, [x0, #0xf]
    // 0x131e780: DecompressPointer r4
    //     0x131e780: add             x4, x4, HEAP, lsl #32
    // 0x131e784: stur            x4, [fp, #-0x28]
    // 0x131e788: LoadField: r5 = r3->field_53
    //     0x131e788: ldur            w5, [x3, #0x53]
    // 0x131e78c: DecompressPointer r5
    //     0x131e78c: add             x5, x5, HEAP, lsl #32
    // 0x131e790: stur            x5, [fp, #-0x18]
    // 0x131e794: LoadField: r6 = r0->field_7
    //     0x131e794: ldur            w6, [x0, #7]
    // 0x131e798: DecompressPointer r6
    //     0x131e798: add             x6, x6, HEAP, lsl #32
    // 0x131e79c: stur            x6, [fp, #-0x10]
    // 0x131e7a0: r0 = EventData()
    //     0x131e7a0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x131e7a4: mov             x1, x0
    // 0x131e7a8: ldur            x0, [fp, #-0x18]
    // 0x131e7ac: stur            x1, [fp, #-0x38]
    // 0x131e7b0: StoreField: r1->field_13 = r0
    //     0x131e7b0: stur            w0, [x1, #0x13]
    // 0x131e7b4: ldur            x0, [fp, #-0x10]
    // 0x131e7b8: StoreField: r1->field_3f = r0
    //     0x131e7b8: stur            w0, [x1, #0x3f]
    // 0x131e7bc: ldur            x0, [fp, #-0x48]
    // 0x131e7c0: StoreField: r1->field_43 = r0
    //     0x131e7c0: stur            w0, [x1, #0x43]
    // 0x131e7c4: ldur            x0, [fp, #-0x28]
    // 0x131e7c8: StoreField: r1->field_47 = r0
    //     0x131e7c8: stur            w0, [x1, #0x47]
    // 0x131e7cc: r0 = EventsRequest()
    //     0x131e7cc: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x131e7d0: mov             x1, x0
    // 0x131e7d4: r0 = "checkout_mobile_number_filled"
    //     0x131e7d4: add             x0, PP, #0x37, lsl #12  ; [pp+0x37000] "checkout_mobile_number_filled"
    //     0x131e7d8: ldr             x0, [x0]
    // 0x131e7dc: StoreField: r1->field_7 = r0
    //     0x131e7dc: stur            w0, [x1, #7]
    // 0x131e7e0: ldur            x0, [fp, #-0x38]
    // 0x131e7e4: StoreField: r1->field_b = r0
    //     0x131e7e4: stur            w0, [x1, #0xb]
    // 0x131e7e8: mov             x2, x1
    // 0x131e7ec: ldur            x1, [fp, #-8]
    // 0x131e7f0: r0 = postEvents()
    //     0x131e7f0: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x131e7f4: r0 = Null
    //     0x131e7f4: mov             x0, NULL
    // 0x131e7f8: LeaveFrame
    //     0x131e7f8: mov             SP, fp
    //     0x131e7fc: ldp             fp, lr, [SP], #0x10
    // 0x131e800: ret
    //     0x131e800: ret             
    // 0x131e804: r0 = ConcurrentModificationError()
    //     0x131e804: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x131e808: mov             x1, x0
    // 0x131e80c: ldur            x0, [fp, #-0x30]
    // 0x131e810: StoreField: r1->field_b = r0
    //     0x131e810: stur            w0, [x1, #0xb]
    // 0x131e814: mov             x0, x1
    // 0x131e818: r0 = Throw()
    //     0x131e818: bl              #0x16f5420  ; ThrowStub
    // 0x131e81c: brk             #0
    // 0x131e820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e820: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e824: b               #0x131e324
    // 0x131e828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131e828: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131e82c: b               #0x131e584
    // 0x131e830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x131e830: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ startTimer(/* No info */) {
    // ** addr: 0x1402244, size: 0x70
    // 0x1402244: EnterFrame
    //     0x1402244: stp             fp, lr, [SP, #-0x10]!
    //     0x1402248: mov             fp, SP
    // 0x140224c: AllocStack(0x8)
    //     0x140224c: sub             SP, SP, #8
    // 0x1402250: SetupParameters(LoginController this /* r1 => r1, fp-0x8 */)
    //     0x1402250: stur            x1, [fp, #-8]
    // 0x1402254: CheckStackOverflow
    //     0x1402254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1402258: cmp             SP, x16
    //     0x140225c: b.ls            #0x14022ac
    // 0x1402260: r1 = 2
    //     0x1402260: movz            x1, #0x2
    // 0x1402264: r0 = AllocateContext()
    //     0x1402264: bl              #0x16f6108  ; AllocateContextStub
    // 0x1402268: mov             x1, x0
    // 0x140226c: ldur            x0, [fp, #-8]
    // 0x1402270: StoreField: r1->field_f = r0
    //     0x1402270: stur            w0, [x1, #0xf]
    // 0x1402274: r0 = 60
    //     0x1402274: movz            x0, #0x3c
    // 0x1402278: StoreField: r1->field_13 = r0
    //     0x1402278: stur            w0, [x1, #0x13]
    // 0x140227c: mov             x2, x1
    // 0x1402280: r1 = Function '<anonymous closure>':.
    //     0x1402280: add             x1, PP, #0x37, lsl #12  ; [pp+0x37268] AnonymousClosure: (0x14022b4), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::startTimer (0x1402244)
    //     0x1402284: ldr             x1, [x1, #0x268]
    // 0x1402288: r0 = AllocateClosure()
    //     0x1402288: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140228c: mov             x3, x0
    // 0x1402290: r1 = Null
    //     0x1402290: mov             x1, NULL
    // 0x1402294: r2 = Instance_Duration
    //     0x1402294: ldr             x2, [PP, #0xa68]  ; [pp+0xa68] Obj!Duration@d776d1
    // 0x1402298: r0 = Timer.periodic()
    //     0x1402298: bl              #0x6aa2cc  ; [dart:async] Timer::Timer.periodic
    // 0x140229c: r0 = Null
    //     0x140229c: mov             x0, NULL
    // 0x14022a0: LeaveFrame
    //     0x14022a0: mov             SP, fp
    //     0x14022a4: ldp             fp, lr, [SP], #0x10
    // 0x14022a8: ret
    //     0x14022a8: ret             
    // 0x14022ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14022ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14022b0: b               #0x1402260
  }
  [closure] void <anonymous closure>(dynamic, Timer) {
    // ** addr: 0x14022b4, size: 0xac
    // 0x14022b4: EnterFrame
    //     0x14022b4: stp             fp, lr, [SP, #-0x10]!
    //     0x14022b8: mov             fp, SP
    // 0x14022bc: ldr             x0, [fp, #0x18]
    // 0x14022c0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14022c0: ldur            w2, [x0, #0x17]
    // 0x14022c4: DecompressPointer r2
    //     0x14022c4: add             x2, x2, HEAP, lsl #32
    // 0x14022c8: CheckStackOverflow
    //     0x14022c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14022cc: cmp             SP, x16
    //     0x14022d0: b.ls            #0x1402358
    // 0x14022d4: LoadField: r0 = r2->field_13
    //     0x14022d4: ldur            w0, [x2, #0x13]
    // 0x14022d8: DecompressPointer r0
    //     0x14022d8: add             x0, x0, HEAP, lsl #32
    // 0x14022dc: r1 = LoadInt32Instr(r0)
    //     0x14022dc: sbfx            x1, x0, #1, #0x1f
    //     0x14022e0: tbz             w0, #0, #0x14022e8
    //     0x14022e4: ldur            x1, [x0, #7]
    // 0x14022e8: cbnz            x1, #0x1402310
    // 0x14022ec: LoadField: r0 = r2->field_f
    //     0x14022ec: ldur            w0, [x2, #0xf]
    // 0x14022f0: DecompressPointer r0
    //     0x14022f0: add             x0, x0, HEAP, lsl #32
    // 0x14022f4: LoadField: r1 = r0->field_97
    //     0x14022f4: ldur            w1, [x0, #0x97]
    // 0x14022f8: DecompressPointer r1
    //     0x14022f8: add             x1, x1, HEAP, lsl #32
    // 0x14022fc: r2 = true
    //     0x14022fc: add             x2, NULL, #0x20  ; true
    // 0x1402300: r0 = value=()
    //     0x1402300: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1402304: ldr             x1, [fp, #0x10]
    // 0x1402308: r0 = cancel()
    //     0x1402308: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x140230c: b               #0x1402348
    // 0x1402310: sub             x3, x1, #1
    // 0x1402314: r0 = BoxInt64Instr(r3)
    //     0x1402314: sbfiz           x0, x3, #1, #0x1f
    //     0x1402318: cmp             x3, x0, asr #1
    //     0x140231c: b.eq            #0x1402328
    //     0x1402320: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x1402324: stur            x3, [x0, #7]
    // 0x1402328: StoreField: r2->field_13 = r0
    //     0x1402328: stur            w0, [x2, #0x13]
    //     0x140232c: tbz             w0, #0, #0x1402348
    //     0x1402330: ldurb           w16, [x2, #-1]
    //     0x1402334: ldurb           w17, [x0, #-1]
    //     0x1402338: and             x16, x17, x16, lsr #2
    //     0x140233c: tst             x16, HEAP, lsr #32
    //     0x1402340: b.eq            #0x1402348
    //     0x1402344: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1402348: r0 = Null
    //     0x1402348: mov             x0, NULL
    // 0x140234c: LeaveFrame
    //     0x140234c: mov             SP, fp
    //     0x1402350: ldp             fp, lr, [SP], #0x10
    // 0x1402354: ret
    //     0x1402354: ret             
    // 0x1402358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1402358: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140235c: b               #0x14022d4
  }
  _ onCodeChanged(/* No info */) async {
    // ** addr: 0x1404230, size: 0x50
    // 0x1404230: EnterFrame
    //     0x1404230: stp             fp, lr, [SP, #-0x10]!
    //     0x1404234: mov             fp, SP
    // 0x1404238: AllocStack(0x18)
    //     0x1404238: sub             SP, SP, #0x18
    // 0x140423c: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x140423c: stur            NULL, [fp, #-8]
    //     0x1404240: stur            x1, [fp, #-0x10]
    //     0x1404244: stur            x2, [fp, #-0x18]
    // 0x1404248: CheckStackOverflow
    //     0x1404248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140424c: cmp             SP, x16
    //     0x1404250: b.ls            #0x1404278
    // 0x1404254: InitAsync() -> Future<void?>
    //     0x1404254: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x1404258: bl              #0x6326e0  ; InitAsyncStub
    // 0x140425c: ldur            x0, [fp, #-0x10]
    // 0x1404260: LoadField: r1 = r0->field_7f
    //     0x1404260: ldur            w1, [x0, #0x7f]
    // 0x1404264: DecompressPointer r1
    //     0x1404264: add             x1, x1, HEAP, lsl #32
    // 0x1404268: ldur            x2, [fp, #-0x18]
    // 0x140426c: r0 = value=()
    //     0x140426c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404270: ldur            x0, [fp, #-0x18]
    // 0x1404274: r0 = ReturnAsyncNotFuture()
    //     0x1404274: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1404278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404278: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140427c: b               #0x1404254
  }
  _ onCodeSubmitted(/* No info */) async {
    // ** addr: 0x1404344, size: 0x64
    // 0x1404344: EnterFrame
    //     0x1404344: stp             fp, lr, [SP, #-0x10]!
    //     0x1404348: mov             fp, SP
    // 0x140434c: AllocStack(0x18)
    //     0x140434c: sub             SP, SP, #0x18
    // 0x1404350: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x1404350: stur            NULL, [fp, #-8]
    //     0x1404354: stur            x1, [fp, #-0x10]
    //     0x1404358: stur            x2, [fp, #-0x18]
    // 0x140435c: CheckStackOverflow
    //     0x140435c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404360: cmp             SP, x16
    //     0x1404364: b.ls            #0x14043a0
    // 0x1404368: InitAsync() -> Future<void?>
    //     0x1404368: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x140436c: bl              #0x6326e0  ; InitAsyncStub
    // 0x1404370: ldur            x0, [fp, #-0x10]
    // 0x1404374: LoadField: r1 = r0->field_7f
    //     0x1404374: ldur            w1, [x0, #0x7f]
    // 0x1404378: DecompressPointer r1
    //     0x1404378: add             x1, x1, HEAP, lsl #32
    // 0x140437c: ldur            x2, [fp, #-0x18]
    // 0x1404380: r0 = value=()
    //     0x1404380: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404384: ldur            x1, [fp, #-0x10]
    // 0x1404388: LoadField: r2 = r1->field_a3
    //     0x1404388: ldur            w2, [x1, #0xa3]
    // 0x140438c: DecompressPointer r2
    //     0x140438c: add             x2, x2, HEAP, lsl #32
    // 0x1404390: ldur            x3, [fp, #-0x18]
    // 0x1404394: r0 = verifyOtp()
    //     0x1404394: bl              #0x14043a8  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::verifyOtp
    // 0x1404398: r0 = Null
    //     0x1404398: mov             x0, NULL
    // 0x140439c: r0 = ReturnAsyncNotFuture()
    //     0x140439c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14043a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14043a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14043a4: b               #0x1404368
  }
  _ verifyOtp(/* No info */) {
    // ** addr: 0x14043a8, size: 0xe4
    // 0x14043a8: EnterFrame
    //     0x14043a8: stp             fp, lr, [SP, #-0x10]!
    //     0x14043ac: mov             fp, SP
    // 0x14043b0: AllocStack(0x40)
    //     0x14043b0: sub             SP, SP, #0x40
    // 0x14043b4: SetupParameters(LoginController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x14043b4: mov             x4, x1
    //     0x14043b8: mov             x0, x2
    //     0x14043bc: stur            x1, [fp, #-8]
    //     0x14043c0: stur            x2, [fp, #-0x10]
    //     0x14043c4: stur            x3, [fp, #-0x18]
    // 0x14043c8: CheckStackOverflow
    //     0x14043c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14043cc: cmp             SP, x16
    //     0x14043d0: b.ls            #0x1404478
    // 0x14043d4: LoadField: r1 = r4->field_ab
    //     0x14043d4: ldur            w1, [x4, #0xab]
    // 0x14043d8: DecompressPointer r1
    //     0x14043d8: add             x1, x1, HEAP, lsl #32
    // 0x14043dc: tbnz            w1, #4, #0x1404468
    // 0x14043e0: mov             x1, x4
    // 0x14043e4: r2 = ""
    //     0x14043e4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14043e8: r0 = showErrorMessage()
    //     0x14043e8: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x14043ec: ldur            x0, [fp, #-8]
    // 0x14043f0: LoadField: r1 = r0->field_47
    //     0x14043f0: ldur            w1, [x0, #0x47]
    // 0x14043f4: DecompressPointer r1
    //     0x14043f4: add             x1, x1, HEAP, lsl #32
    // 0x14043f8: r16 = Sentinel
    //     0x14043f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x14043fc: cmp             w1, w16
    // 0x1404400: b.eq            #0x1404480
    // 0x1404404: LoadField: r5 = r0->field_9b
    //     0x1404404: ldur            w5, [x0, #0x9b]
    // 0x1404408: DecompressPointer r5
    //     0x1404408: add             x5, x5, HEAP, lsl #32
    // 0x140440c: ldur            x2, [fp, #-0x10]
    // 0x1404410: ldur            x3, [fp, #-0x18]
    // 0x1404414: r0 = verifyOtp()
    //     0x1404414: bl              #0x140448c  ; [package:customer_app/app/data/repositories/login/login_repo_impl.dart] LoginRepoImpl::verifyOtp
    // 0x1404418: ldur            x2, [fp, #-8]
    // 0x140441c: r1 = Function '_handleVerifyResponse@1161085715':.
    //     0x140441c: add             x1, PP, #0x37, lsl #12  ; [pp+0x370a0] AnonymousClosure: (0x14047a8), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleVerifyResponse (0x14047e4)
    //     0x1404420: ldr             x1, [x1, #0xa0]
    // 0x1404424: stur            x0, [fp, #-0x10]
    // 0x1404428: r0 = AllocateClosure()
    //     0x1404428: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140442c: ldur            x2, [fp, #-8]
    // 0x1404430: r1 = Function '_handleVerifyOtpError@1161085715':.
    //     0x1404430: add             x1, PP, #0x37, lsl #12  ; [pp+0x370a8] AnonymousClosure: (0x14046d4), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleVerifyOtpError (0x1404710)
    //     0x1404434: ldr             x1, [x1, #0xa8]
    // 0x1404438: stur            x0, [fp, #-0x18]
    // 0x140443c: r0 = AllocateClosure()
    //     0x140443c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1404440: r16 = <VerifyOtpResponse>
    //     0x1404440: add             x16, PP, #0xa, lsl #12  ; [pp+0xac18] TypeArguments: <VerifyOtpResponse>
    //     0x1404444: ldr             x16, [x16, #0xc18]
    // 0x1404448: ldur            lr, [fp, #-8]
    // 0x140444c: stp             lr, x16, [SP, #0x18]
    // 0x1404450: ldur            x16, [fp, #-0x10]
    // 0x1404454: stp             x0, x16, [SP, #8]
    // 0x1404458: ldur            x16, [fp, #-0x18]
    // 0x140445c: str             x16, [SP]
    // 0x1404460: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x1404460: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x1404464: r0 = callDataService()
    //     0x1404464: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x1404468: r0 = Null
    //     0x1404468: mov             x0, NULL
    // 0x140446c: LeaveFrame
    //     0x140446c: mov             SP, fp
    //     0x1404470: ldp             fp, lr, [SP], #0x10
    // 0x1404474: ret
    //     0x1404474: ret             
    // 0x1404478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404478: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140447c: b               #0x14043d4
    // 0x1404480: r9 = loginRepo
    //     0x1404480: add             x9, PP, #0x22, lsl #12  ; [pp+0x22378] Field <LoginController.loginRepo>: late (offset: 0x48)
    //     0x1404484: ldr             x9, [x9, #0x378]
    // 0x1404488: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x1404488: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleVerifyOtpError(dynamic, Exception) {
    // ** addr: 0x14046d4, size: 0x3c
    // 0x14046d4: EnterFrame
    //     0x14046d4: stp             fp, lr, [SP, #-0x10]!
    //     0x14046d8: mov             fp, SP
    // 0x14046dc: ldr             x0, [fp, #0x18]
    // 0x14046e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14046e0: ldur            w1, [x0, #0x17]
    // 0x14046e4: DecompressPointer r1
    //     0x14046e4: add             x1, x1, HEAP, lsl #32
    // 0x14046e8: CheckStackOverflow
    //     0x14046e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14046ec: cmp             SP, x16
    //     0x14046f0: b.ls            #0x1404708
    // 0x14046f4: ldr             x2, [fp, #0x10]
    // 0x14046f8: r0 = _handleVerifyOtpError()
    //     0x14046f8: bl              #0x1404710  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleVerifyOtpError
    // 0x14046fc: LeaveFrame
    //     0x14046fc: mov             SP, fp
    //     0x1404700: ldp             fp, lr, [SP], #0x10
    // 0x1404704: ret
    //     0x1404704: ret             
    // 0x1404708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404708: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140470c: b               #0x14046f4
  }
  _ _handleVerifyOtpError(/* No info */) {
    // ** addr: 0x1404710, size: 0x98
    // 0x1404710: EnterFrame
    //     0x1404710: stp             fp, lr, [SP, #-0x10]!
    //     0x1404714: mov             fp, SP
    // 0x1404718: AllocStack(0x10)
    //     0x1404718: sub             SP, SP, #0x10
    // 0x140471c: SetupParameters(LoginController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x140471c: mov             x3, x1
    //     0x1404720: mov             x0, x2
    //     0x1404724: stur            x1, [fp, #-8]
    //     0x1404728: stur            x2, [fp, #-0x10]
    // 0x140472c: CheckStackOverflow
    //     0x140472c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404730: cmp             SP, x16
    //     0x1404734: b.ls            #0x14047a0
    // 0x1404738: LoadField: r1 = r3->field_83
    //     0x1404738: ldur            w1, [x3, #0x83]
    // 0x140473c: DecompressPointer r1
    //     0x140473c: add             x1, x1, HEAP, lsl #32
    // 0x1404740: r2 = true
    //     0x1404740: add             x2, NULL, #0x20  ; true
    // 0x1404744: r0 = value=()
    //     0x1404744: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404748: ldur            x0, [fp, #-0x10]
    // 0x140474c: r2 = Null
    //     0x140474c: mov             x2, NULL
    // 0x1404750: r1 = Null
    //     0x1404750: mov             x1, NULL
    // 0x1404754: r4 = LoadClassIdInstr(r0)
    //     0x1404754: ldur            x4, [x0, #-1]
    //     0x1404758: ubfx            x4, x4, #0xc, #0x14
    // 0x140475c: r17 = -5018
    //     0x140475c: movn            x17, #0x1399
    // 0x1404760: add             x4, x4, x17
    // 0x1404764: cmp             x4, #8
    // 0x1404768: b.ls            #0x140477c
    // 0x140476c: r8 = BaseException
    //     0x140476c: ldr             x8, [PP, #0x7e90]  ; [pp+0x7e90] Type: BaseException
    // 0x1404770: r3 = Null
    //     0x1404770: add             x3, PP, #0x37, lsl #12  ; [pp+0x370b0] Null
    //     0x1404774: ldr             x3, [x3, #0xb0]
    // 0x1404778: r0 = DefaultTypeTest()
    //     0x1404778: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x140477c: ldur            x0, [fp, #-0x10]
    // 0x1404780: LoadField: r2 = r0->field_7
    //     0x1404780: ldur            w2, [x0, #7]
    // 0x1404784: DecompressPointer r2
    //     0x1404784: add             x2, x2, HEAP, lsl #32
    // 0x1404788: ldur            x1, [fp, #-8]
    // 0x140478c: r0 = showErrorMessage()
    //     0x140478c: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x1404790: r0 = Null
    //     0x1404790: mov             x0, NULL
    // 0x1404794: LeaveFrame
    //     0x1404794: mov             SP, fp
    //     0x1404798: ldp             fp, lr, [SP], #0x10
    // 0x140479c: ret
    //     0x140479c: ret             
    // 0x14047a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14047a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14047a4: b               #0x1404738
  }
  [closure] dynamic _handleVerifyResponse(dynamic, dynamic) {
    // ** addr: 0x14047a8, size: 0x3c
    // 0x14047a8: EnterFrame
    //     0x14047a8: stp             fp, lr, [SP, #-0x10]!
    //     0x14047ac: mov             fp, SP
    // 0x14047b0: ldr             x0, [fp, #0x18]
    // 0x14047b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14047b4: ldur            w1, [x0, #0x17]
    // 0x14047b8: DecompressPointer r1
    //     0x14047b8: add             x1, x1, HEAP, lsl #32
    // 0x14047bc: CheckStackOverflow
    //     0x14047bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14047c0: cmp             SP, x16
    //     0x14047c4: b.ls            #0x14047dc
    // 0x14047c8: ldr             x2, [fp, #0x10]
    // 0x14047cc: r0 = _handleVerifyResponse()
    //     0x14047cc: bl              #0x14047e4  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleVerifyResponse
    // 0x14047d0: LeaveFrame
    //     0x14047d0: mov             SP, fp
    //     0x14047d4: ldp             fp, lr, [SP], #0x10
    // 0x14047d8: ret
    //     0x14047d8: ret             
    // 0x14047dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14047dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14047e0: b               #0x14047c8
  }
  _ _handleVerifyResponse(/* No info */) {
    // ** addr: 0x14047e4, size: 0x48
    // 0x14047e4: EnterFrame
    //     0x14047e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14047e8: mov             fp, SP
    // 0x14047ec: AllocStack(0x8)
    //     0x14047ec: sub             SP, SP, #8
    // 0x14047f0: SetupParameters(LoginController this /* r1 => r0, fp-0x8 */)
    //     0x14047f0: mov             x0, x1
    //     0x14047f4: stur            x1, [fp, #-8]
    // 0x14047f8: CheckStackOverflow
    //     0x14047f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14047fc: cmp             SP, x16
    //     0x1404800: b.ls            #0x1404824
    // 0x1404804: mov             x1, x0
    // 0x1404808: r0 = verifyOtpResponse=()
    //     0x1404808: bl              #0x14049f8  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::verifyOtpResponse=
    // 0x140480c: ldur            x1, [fp, #-8]
    // 0x1404810: r0 = checkForOtpVerify()
    //     0x1404810: bl              #0x140482c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::checkForOtpVerify
    // 0x1404814: r0 = Null
    //     0x1404814: mov             x0, NULL
    // 0x1404818: LeaveFrame
    //     0x1404818: mov             SP, fp
    //     0x140481c: ldp             fp, lr, [SP], #0x10
    // 0x1404820: ret
    //     0x1404820: ret             
    // 0x1404824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404824: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404828: b               #0x1404804
  }
  _ checkForOtpVerify(/* No info */) async {
    // ** addr: 0x140482c, size: 0x1cc
    // 0x140482c: EnterFrame
    //     0x140482c: stp             fp, lr, [SP, #-0x10]!
    //     0x1404830: mov             fp, SP
    // 0x1404834: AllocStack(0x28)
    //     0x1404834: sub             SP, SP, #0x28
    // 0x1404838: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */)
    //     0x1404838: stur            NULL, [fp, #-8]
    //     0x140483c: stur            x1, [fp, #-0x10]
    // 0x1404840: CheckStackOverflow
    //     0x1404840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404844: cmp             SP, x16
    //     0x1404848: b.ls            #0x14049e8
    // 0x140484c: InitAsync() -> Future<void?>
    //     0x140484c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x1404850: bl              #0x6326e0  ; InitAsyncStub
    // 0x1404854: ldur            x0, [fp, #-0x10]
    // 0x1404858: LoadField: r2 = r0->field_6f
    //     0x1404858: ldur            w2, [x0, #0x6f]
    // 0x140485c: DecompressPointer r2
    //     0x140485c: add             x2, x2, HEAP, lsl #32
    // 0x1404860: mov             x1, x2
    // 0x1404864: stur            x2, [fp, #-0x18]
    // 0x1404868: r0 = value()
    //     0x1404868: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140486c: LoadField: r1 = r0->field_f
    //     0x140486c: ldur            w1, [x0, #0xf]
    // 0x1404870: DecompressPointer r1
    //     0x1404870: add             x1, x1, HEAP, lsl #32
    // 0x1404874: cmp             w1, NULL
    // 0x1404878: b.eq            #0x14049e0
    // 0x140487c: ldur            x0, [fp, #-0x10]
    // 0x1404880: LoadField: r2 = r0->field_63
    //     0x1404880: ldur            w2, [x0, #0x63]
    // 0x1404884: DecompressPointer r2
    //     0x1404884: add             x2, x2, HEAP, lsl #32
    // 0x1404888: ldur            x1, [fp, #-0x18]
    // 0x140488c: stur            x2, [fp, #-0x20]
    // 0x1404890: r0 = value()
    //     0x1404890: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1404894: LoadField: r1 = r0->field_f
    //     0x1404894: ldur            w1, [x0, #0xf]
    // 0x1404898: DecompressPointer r1
    //     0x1404898: add             x1, x1, HEAP, lsl #32
    // 0x140489c: cmp             w1, NULL
    // 0x14048a0: b.ne            #0x14048ac
    // 0x14048a4: r3 = ""
    //     0x14048a4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14048a8: b               #0x14048b0
    // 0x14048ac: mov             x3, x1
    // 0x14048b0: ldur            x1, [fp, #-0x20]
    // 0x14048b4: r2 = "user_id"
    //     0x14048b4: add             x2, PP, #0xe, lsl #12  ; [pp+0xe8a0] "user_id"
    //     0x14048b8: ldr             x2, [x2, #0x8a0]
    // 0x14048bc: r0 = setString()
    //     0x14048bc: bl              #0x889814  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setString
    // 0x14048c0: ldur            x1, [fp, #-0x18]
    // 0x14048c4: r0 = value()
    //     0x14048c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14048c8: LoadField: r1 = r0->field_13
    //     0x14048c8: ldur            w1, [x0, #0x13]
    // 0x14048cc: DecompressPointer r1
    //     0x14048cc: add             x1, x1, HEAP, lsl #32
    // 0x14048d0: cmp             w1, NULL
    // 0x14048d4: b.ne            #0x14048e0
    // 0x14048d8: r0 = Null
    //     0x14048d8: mov             x0, NULL
    // 0x14048dc: b               #0x14048e8
    // 0x14048e0: LoadField: r0 = r1->field_13
    //     0x14048e0: ldur            w0, [x1, #0x13]
    // 0x14048e4: DecompressPointer r0
    //     0x14048e4: add             x0, x0, HEAP, lsl #32
    // 0x14048e8: cmp             w0, NULL
    // 0x14048ec: b.ne            #0x14048f8
    // 0x14048f0: r3 = ""
    //     0x14048f0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14048f4: b               #0x14048fc
    // 0x14048f8: mov             x3, x0
    // 0x14048fc: ldur            x1, [fp, #-0x20]
    // 0x1404900: r2 = "number"
    //     0x1404900: add             x2, PP, #0xb, lsl #12  ; [pp+0xbd80] "number"
    //     0x1404904: ldr             x2, [x2, #0xd80]
    // 0x1404908: r0 = setString()
    //     0x1404908: bl              #0x889814  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setString
    // 0x140490c: ldur            x1, [fp, #-0x18]
    // 0x1404910: r0 = value()
    //     0x1404910: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1404914: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1404914: ldur            w1, [x0, #0x17]
    // 0x1404918: DecompressPointer r1
    //     0x1404918: add             x1, x1, HEAP, lsl #32
    // 0x140491c: cmp             w1, NULL
    // 0x1404920: b.ne            #0x140492c
    // 0x1404924: r3 = ""
    //     0x1404924: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1404928: b               #0x1404930
    // 0x140492c: mov             x3, x1
    // 0x1404930: ldur            x1, [fp, #-0x20]
    // 0x1404934: r2 = "token"
    //     0x1404934: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x1404938: ldr             x2, [x2, #0x958]
    // 0x140493c: r0 = setString()
    //     0x140493c: bl              #0x889814  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setString
    // 0x1404940: ldur            x1, [fp, #-0x10]
    // 0x1404944: r0 = postEventOtpVerification()
    //     0x1404944: bl              #0x9339f8  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::postEventOtpVerification
    // 0x1404948: r0 = app()
    //     0x1404948: bl              #0x88ab64  ; [package:firebase_core/firebase_core.dart] Firebase::app
    // 0x140494c: mov             x2, x0
    // 0x1404950: r1 = Null
    //     0x1404950: mov             x1, NULL
    // 0x1404954: r0 = FirebaseAnalytics.instanceFor()
    //     0x1404954: bl              #0x88a8bc  ; [package:firebase_analytics/firebase_analytics.dart] FirebaseAnalytics::FirebaseAnalytics.instanceFor
    // 0x1404958: ldur            x1, [fp, #-0x10]
    // 0x140495c: stur            x0, [fp, #-0x28]
    // 0x1404960: r0 = activeExchangeResponse()
    //     0x1404960: bl              #0x91ea4c  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::activeExchangeResponse
    // 0x1404964: LoadField: r2 = r0->field_f
    //     0x1404964: ldur            w2, [x0, #0xf]
    // 0x1404968: DecompressPointer r2
    //     0x1404968: add             x2, x2, HEAP, lsl #32
    // 0x140496c: ldur            x1, [fp, #-0x28]
    // 0x1404970: r0 = setUserId()
    //     0x1404970: bl              #0x933e30  ; [package:firebase_analytics/firebase_analytics.dart] FirebaseAnalytics::setUserId
    // 0x1404974: mov             x1, x0
    // 0x1404978: stur            x1, [fp, #-0x28]
    // 0x140497c: r0 = Await()
    //     0x140497c: bl              #0x63248c  ; AwaitStub
    // 0x1404980: ldur            x1, [fp, #-0x18]
    // 0x1404984: r0 = value()
    //     0x1404984: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1404988: LoadField: r2 = r0->field_13
    //     0x1404988: ldur            w2, [x0, #0x13]
    // 0x140498c: DecompressPointer r2
    //     0x140498c: add             x2, x2, HEAP, lsl #32
    // 0x1404990: cmp             w2, NULL
    // 0x1404994: b.eq            #0x14049f0
    // 0x1404998: ldur            x1, [fp, #-0x20]
    // 0x140499c: r0 = setUserData()
    //     0x140499c: bl              #0x933d54  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setUserData
    // 0x14049a0: mov             x1, x0
    // 0x14049a4: stur            x1, [fp, #-0x10]
    // 0x14049a8: r0 = Await()
    //     0x14049a8: bl              #0x63248c  ; AwaitStub
    // 0x14049ac: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14049ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14049b0: ldr             x0, [x0, #0x1c80]
    //     0x14049b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14049b8: cmp             w0, w16
    //     0x14049bc: b.ne            #0x14049c8
    //     0x14049c0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14049c4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14049c8: r0 = GetNavigation.context()
    //     0x14049c8: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x14049cc: cmp             w0, NULL
    // 0x14049d0: b.eq            #0x14049f4
    // 0x14049d4: mov             x1, x0
    // 0x14049d8: r0 = restartApp()
    //     0x14049d8: bl              #0x8a539c  ; [package:customer_app/app/presentation/custom_widgets/restart_widget.dart] RestartWidget::restartApp
    // 0x14049dc: r0 = GetResetExt.reset()
    //     0x14049dc: bl              #0x8a5030  ; [package:get/get_common/get_reset.dart] ::GetResetExt.reset
    // 0x14049e0: r0 = Null
    //     0x14049e0: mov             x0, NULL
    // 0x14049e4: r0 = ReturnAsyncNotFuture()
    //     0x14049e4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14049e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14049e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14049ec: b               #0x140484c
    // 0x14049f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14049f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x14049f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14049f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ verifyOtpResponse=(/* No info */) {
    // ** addr: 0x14049f8, size: 0x8c
    // 0x14049f8: EnterFrame
    //     0x14049f8: stp             fp, lr, [SP, #-0x10]!
    //     0x14049fc: mov             fp, SP
    // 0x1404a00: AllocStack(0x10)
    //     0x1404a00: sub             SP, SP, #0x10
    // 0x1404a04: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x1404a04: mov             x3, x2
    //     0x1404a08: stur            x2, [fp, #-0x10]
    // 0x1404a0c: CheckStackOverflow
    //     0x1404a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404a10: cmp             SP, x16
    //     0x1404a14: b.ls            #0x1404a7c
    // 0x1404a18: LoadField: r4 = r1->field_6f
    //     0x1404a18: ldur            w4, [x1, #0x6f]
    // 0x1404a1c: DecompressPointer r4
    //     0x1404a1c: add             x4, x4, HEAP, lsl #32
    // 0x1404a20: mov             x0, x3
    // 0x1404a24: stur            x4, [fp, #-8]
    // 0x1404a28: r2 = Null
    //     0x1404a28: mov             x2, NULL
    // 0x1404a2c: r1 = Null
    //     0x1404a2c: mov             x1, NULL
    // 0x1404a30: r4 = 60
    //     0x1404a30: movz            x4, #0x3c
    // 0x1404a34: branchIfSmi(r0, 0x1404a40)
    //     0x1404a34: tbz             w0, #0, #0x1404a40
    // 0x1404a38: r4 = LoadClassIdInstr(r0)
    //     0x1404a38: ldur            x4, [x0, #-1]
    //     0x1404a3c: ubfx            x4, x4, #0xc, #0x14
    // 0x1404a40: r17 = 5082
    //     0x1404a40: movz            x17, #0x13da
    // 0x1404a44: cmp             x4, x17
    // 0x1404a48: b.eq            #0x1404a60
    // 0x1404a4c: r8 = VerifyOtpResponse
    //     0x1404a4c: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2d980] Type: VerifyOtpResponse
    //     0x1404a50: ldr             x8, [x8, #0x980]
    // 0x1404a54: r3 = Null
    //     0x1404a54: add             x3, PP, #0x37, lsl #12  ; [pp+0x370c0] Null
    //     0x1404a58: ldr             x3, [x3, #0xc0]
    // 0x1404a5c: r0 = DefaultTypeTest()
    //     0x1404a5c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1404a60: ldur            x1, [fp, #-8]
    // 0x1404a64: ldur            x2, [fp, #-0x10]
    // 0x1404a68: r0 = value=()
    //     0x1404a68: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404a6c: ldur            x0, [fp, #-0x10]
    // 0x1404a70: LeaveFrame
    //     0x1404a70: mov             SP, fp
    //     0x1404a74: ldp             fp, lr, [SP], #0x10
    // 0x1404a78: ret
    //     0x1404a78: ret             
    // 0x1404a7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404a7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404a80: b               #0x1404a18
  }
  set _ isValid=(/* No info */) {
    // ** addr: 0x1404c00, size: 0x4c
    // 0x1404c00: EnterFrame
    //     0x1404c00: stp             fp, lr, [SP, #-0x10]!
    //     0x1404c04: mov             fp, SP
    // 0x1404c08: AllocStack(0x8)
    //     0x1404c08: sub             SP, SP, #8
    // 0x1404c0c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x1404c0c: mov             x0, x2
    //     0x1404c10: stur            x2, [fp, #-8]
    // 0x1404c14: CheckStackOverflow
    //     0x1404c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404c18: cmp             SP, x16
    //     0x1404c1c: b.ls            #0x1404c44
    // 0x1404c20: LoadField: r2 = r1->field_b7
    //     0x1404c20: ldur            w2, [x1, #0xb7]
    // 0x1404c24: DecompressPointer r2
    //     0x1404c24: add             x2, x2, HEAP, lsl #32
    // 0x1404c28: mov             x1, x2
    // 0x1404c2c: mov             x2, x0
    // 0x1404c30: r0 = value=()
    //     0x1404c30: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404c34: ldur            x0, [fp, #-8]
    // 0x1404c38: LeaveFrame
    //     0x1404c38: mov             SP, fp
    //     0x1404c3c: ldp             fp, lr, [SP], #0x10
    // 0x1404c40: ret
    //     0x1404c40: ret             
    // 0x1404c44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404c44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404c48: b               #0x1404c20
  }
  set _ onUserInteraction=(/* No info */) {
    // ** addr: 0x1404c4c, size: 0x40
    // 0x1404c4c: EnterFrame
    //     0x1404c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x1404c50: mov             fp, SP
    // 0x1404c54: CheckStackOverflow
    //     0x1404c54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404c58: cmp             SP, x16
    //     0x1404c5c: b.ls            #0x1404c84
    // 0x1404c60: LoadField: r0 = r1->field_bb
    //     0x1404c60: ldur            w0, [x1, #0xbb]
    // 0x1404c64: DecompressPointer r0
    //     0x1404c64: add             x0, x0, HEAP, lsl #32
    // 0x1404c68: mov             x1, x0
    // 0x1404c6c: r2 = true
    //     0x1404c6c: add             x2, NULL, #0x20  ; true
    // 0x1404c70: r0 = value=()
    //     0x1404c70: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1404c74: r0 = true
    //     0x1404c74: add             x0, NULL, #0x20  ; true
    // 0x1404c78: LeaveFrame
    //     0x1404c78: mov             SP, fp
    //     0x1404c7c: ldp             fp, lr, [SP], #0x10
    // 0x1404c80: ret
    //     0x1404c80: ret             
    // 0x1404c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1404c84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1404c88: b               #0x1404c60
  }
  _ resendOtp(/* No info */) {
    // ** addr: 0x1404f54, size: 0xdc
    // 0x1404f54: EnterFrame
    //     0x1404f54: stp             fp, lr, [SP, #-0x10]!
    //     0x1404f58: mov             fp, SP
    // 0x1404f5c: AllocStack(0x40)
    //     0x1404f5c: sub             SP, SP, #0x40
    // 0x1404f60: SetupParameters(LoginController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x1404f60: mov             x4, x1
    //     0x1404f64: mov             x0, x2
    //     0x1404f68: stur            x1, [fp, #-8]
    //     0x1404f6c: stur            x2, [fp, #-0x10]
    //     0x1404f70: stur            x3, [fp, #-0x18]
    // 0x1404f74: CheckStackOverflow
    //     0x1404f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1404f78: cmp             SP, x16
    //     0x1404f7c: b.ls            #0x140501c
    // 0x1404f80: LoadField: r1 = r4->field_ab
    //     0x1404f80: ldur            w1, [x4, #0xab]
    // 0x1404f84: DecompressPointer r1
    //     0x1404f84: add             x1, x1, HEAP, lsl #32
    // 0x1404f88: tbnz            w1, #4, #0x140500c
    // 0x1404f8c: mov             x1, x4
    // 0x1404f90: r2 = ""
    //     0x1404f90: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1404f94: r0 = showErrorMessage()
    //     0x1404f94: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x1404f98: ldur            x0, [fp, #-8]
    // 0x1404f9c: LoadField: r1 = r0->field_47
    //     0x1404f9c: ldur            w1, [x0, #0x47]
    // 0x1404fa0: DecompressPointer r1
    //     0x1404fa0: add             x1, x1, HEAP, lsl #32
    // 0x1404fa4: r16 = Sentinel
    //     0x1404fa4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1404fa8: cmp             w1, w16
    // 0x1404fac: b.eq            #0x1405024
    // 0x1404fb0: ldur            x2, [fp, #-0x10]
    // 0x1404fb4: ldur            x3, [fp, #-0x18]
    // 0x1404fb8: r0 = resendOtp()
    //     0x1404fb8: bl              #0x1405030  ; [package:customer_app/app/data/repositories/login/login_repo_impl.dart] LoginRepoImpl::resendOtp
    // 0x1404fbc: ldur            x2, [fp, #-8]
    // 0x1404fc0: r1 = Function '_handleResendOtpResponse@1161085715':.
    //     0x1404fc0: add             x1, PP, #0x37, lsl #12  ; [pp+0x371d0] AnonymousClosure: (0x1405334), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleResendOtpResponse (0x1405370)
    //     0x1404fc4: ldr             x1, [x1, #0x1d0]
    // 0x1404fc8: stur            x0, [fp, #-0x10]
    // 0x1404fcc: r0 = AllocateClosure()
    //     0x1404fcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1404fd0: ldur            x2, [fp, #-8]
    // 0x1404fd4: r1 = Function '_handleResendOtpError@1161085715':.
    //     0x1404fd4: add             x1, PP, #0x37, lsl #12  ; [pp+0x371d8] AnonymousClosure: (0x1405260), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleResendOtpError (0x140529c)
    //     0x1404fd8: ldr             x1, [x1, #0x1d8]
    // 0x1404fdc: stur            x0, [fp, #-0x18]
    // 0x1404fe0: r0 = AllocateClosure()
    //     0x1404fe0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1404fe4: r16 = <GeneralResponse>
    //     0x1404fe4: add             x16, PP, #0xa, lsl #12  ; [pp+0xac10] TypeArguments: <GeneralResponse>
    //     0x1404fe8: ldr             x16, [x16, #0xc10]
    // 0x1404fec: ldur            lr, [fp, #-8]
    // 0x1404ff0: stp             lr, x16, [SP, #0x18]
    // 0x1404ff4: ldur            x16, [fp, #-0x10]
    // 0x1404ff8: stp             x0, x16, [SP, #8]
    // 0x1404ffc: ldur            x16, [fp, #-0x18]
    // 0x1405000: str             x16, [SP]
    // 0x1405004: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x1405004: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x1405008: r0 = callDataService()
    //     0x1405008: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x140500c: r0 = Null
    //     0x140500c: mov             x0, NULL
    // 0x1405010: LeaveFrame
    //     0x1405010: mov             SP, fp
    //     0x1405014: ldp             fp, lr, [SP], #0x10
    // 0x1405018: ret
    //     0x1405018: ret             
    // 0x140501c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140501c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1405020: b               #0x1404f80
    // 0x1405024: r9 = loginRepo
    //     0x1405024: add             x9, PP, #0x22, lsl #12  ; [pp+0x22378] Field <LoginController.loginRepo>: late (offset: 0x48)
    //     0x1405028: ldr             x9, [x9, #0x378]
    // 0x140502c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x140502c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleResendOtpError(dynamic, Exception) {
    // ** addr: 0x1405260, size: 0x3c
    // 0x1405260: EnterFrame
    //     0x1405260: stp             fp, lr, [SP, #-0x10]!
    //     0x1405264: mov             fp, SP
    // 0x1405268: ldr             x0, [fp, #0x18]
    // 0x140526c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140526c: ldur            w1, [x0, #0x17]
    // 0x1405270: DecompressPointer r1
    //     0x1405270: add             x1, x1, HEAP, lsl #32
    // 0x1405274: CheckStackOverflow
    //     0x1405274: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1405278: cmp             SP, x16
    //     0x140527c: b.ls            #0x1405294
    // 0x1405280: ldr             x2, [fp, #0x10]
    // 0x1405284: r0 = _handleResendOtpError()
    //     0x1405284: bl              #0x140529c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleResendOtpError
    // 0x1405288: LeaveFrame
    //     0x1405288: mov             SP, fp
    //     0x140528c: ldp             fp, lr, [SP], #0x10
    // 0x1405290: ret
    //     0x1405290: ret             
    // 0x1405294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1405294: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1405298: b               #0x1405280
  }
  _ _handleResendOtpError(/* No info */) {
    // ** addr: 0x140529c, size: 0x98
    // 0x140529c: EnterFrame
    //     0x140529c: stp             fp, lr, [SP, #-0x10]!
    //     0x14052a0: mov             fp, SP
    // 0x14052a4: AllocStack(0x10)
    //     0x14052a4: sub             SP, SP, #0x10
    // 0x14052a8: SetupParameters(LoginController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x14052a8: mov             x3, x1
    //     0x14052ac: mov             x0, x2
    //     0x14052b0: stur            x1, [fp, #-8]
    //     0x14052b4: stur            x2, [fp, #-0x10]
    // 0x14052b8: CheckStackOverflow
    //     0x14052b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14052bc: cmp             SP, x16
    //     0x14052c0: b.ls            #0x140532c
    // 0x14052c4: LoadField: r1 = r3->field_87
    //     0x14052c4: ldur            w1, [x3, #0x87]
    // 0x14052c8: DecompressPointer r1
    //     0x14052c8: add             x1, x1, HEAP, lsl #32
    // 0x14052cc: r2 = true
    //     0x14052cc: add             x2, NULL, #0x20  ; true
    // 0x14052d0: r0 = value=()
    //     0x14052d0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14052d4: ldur            x0, [fp, #-0x10]
    // 0x14052d8: r2 = Null
    //     0x14052d8: mov             x2, NULL
    // 0x14052dc: r1 = Null
    //     0x14052dc: mov             x1, NULL
    // 0x14052e0: r4 = LoadClassIdInstr(r0)
    //     0x14052e0: ldur            x4, [x0, #-1]
    //     0x14052e4: ubfx            x4, x4, #0xc, #0x14
    // 0x14052e8: r17 = -5018
    //     0x14052e8: movn            x17, #0x1399
    // 0x14052ec: add             x4, x4, x17
    // 0x14052f0: cmp             x4, #8
    // 0x14052f4: b.ls            #0x1405308
    // 0x14052f8: r8 = BaseException
    //     0x14052f8: ldr             x8, [PP, #0x7e90]  ; [pp+0x7e90] Type: BaseException
    // 0x14052fc: r3 = Null
    //     0x14052fc: add             x3, PP, #0x37, lsl #12  ; [pp+0x371e0] Null
    //     0x1405300: ldr             x3, [x3, #0x1e0]
    // 0x1405304: r0 = DefaultTypeTest()
    //     0x1405304: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1405308: ldur            x0, [fp, #-0x10]
    // 0x140530c: LoadField: r2 = r0->field_7
    //     0x140530c: ldur            w2, [x0, #7]
    // 0x1405310: DecompressPointer r2
    //     0x1405310: add             x2, x2, HEAP, lsl #32
    // 0x1405314: ldur            x1, [fp, #-8]
    // 0x1405318: r0 = showErrorMessage()
    //     0x1405318: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x140531c: r0 = Null
    //     0x140531c: mov             x0, NULL
    // 0x1405320: LeaveFrame
    //     0x1405320: mov             SP, fp
    //     0x1405324: ldp             fp, lr, [SP], #0x10
    // 0x1405328: ret
    //     0x1405328: ret             
    // 0x140532c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140532c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1405330: b               #0x14052c4
  }
  [closure] dynamic _handleResendOtpResponse(dynamic, dynamic) {
    // ** addr: 0x1405334, size: 0x3c
    // 0x1405334: EnterFrame
    //     0x1405334: stp             fp, lr, [SP, #-0x10]!
    //     0x1405338: mov             fp, SP
    // 0x140533c: ldr             x0, [fp, #0x18]
    // 0x1405340: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1405340: ldur            w1, [x0, #0x17]
    // 0x1405344: DecompressPointer r1
    //     0x1405344: add             x1, x1, HEAP, lsl #32
    // 0x1405348: CheckStackOverflow
    //     0x1405348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140534c: cmp             SP, x16
    //     0x1405350: b.ls            #0x1405368
    // 0x1405354: ldr             x2, [fp, #0x10]
    // 0x1405358: r0 = _handleResendOtpResponse()
    //     0x1405358: bl              #0x1405370  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleResendOtpResponse
    // 0x140535c: LeaveFrame
    //     0x140535c: mov             SP, fp
    //     0x1405360: ldp             fp, lr, [SP], #0x10
    // 0x1405364: ret
    //     0x1405364: ret             
    // 0x1405368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1405368: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140536c: b               #0x1405354
  }
  _ _handleResendOtpResponse(/* No info */) {
    // ** addr: 0x1405370, size: 0xa8
    // 0x1405370: EnterFrame
    //     0x1405370: stp             fp, lr, [SP, #-0x10]!
    //     0x1405374: mov             fp, SP
    // 0x1405378: AllocStack(0x18)
    //     0x1405378: sub             SP, SP, #0x18
    // 0x140537c: SetupParameters(LoginController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x140537c: mov             x3, x1
    //     0x1405380: mov             x0, x2
    //     0x1405384: stur            x1, [fp, #-8]
    //     0x1405388: stur            x2, [fp, #-0x10]
    // 0x140538c: CheckStackOverflow
    //     0x140538c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1405390: cmp             SP, x16
    //     0x1405394: b.ls            #0x1405410
    // 0x1405398: mov             x1, x3
    // 0x140539c: r2 = "OTP Resend Successfully"
    //     0x140539c: add             x2, PP, #0x37, lsl #12  ; [pp+0x371f0] "OTP Resend Successfully"
    //     0x14053a0: ldr             x2, [x2, #0x1f0]
    // 0x14053a4: r0 = showErrorSnackBar()
    //     0x14053a4: bl              #0x9a6030  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorSnackBar
    // 0x14053a8: ldur            x0, [fp, #-8]
    // 0x14053ac: LoadField: r3 = r0->field_67
    //     0x14053ac: ldur            w3, [x0, #0x67]
    // 0x14053b0: DecompressPointer r3
    //     0x14053b0: add             x3, x3, HEAP, lsl #32
    // 0x14053b4: ldur            x0, [fp, #-0x10]
    // 0x14053b8: stur            x3, [fp, #-0x18]
    // 0x14053bc: r2 = Null
    //     0x14053bc: mov             x2, NULL
    // 0x14053c0: r1 = Null
    //     0x14053c0: mov             x1, NULL
    // 0x14053c4: r4 = 60
    //     0x14053c4: movz            x4, #0x3c
    // 0x14053c8: branchIfSmi(r0, 0x14053d4)
    //     0x14053c8: tbz             w0, #0, #0x14053d4
    // 0x14053cc: r4 = LoadClassIdInstr(r0)
    //     0x14053cc: ldur            x4, [x0, #-1]
    //     0x14053d0: ubfx            x4, x4, #0xc, #0x14
    // 0x14053d4: r17 = 5085
    //     0x14053d4: movz            x17, #0x13dd
    // 0x14053d8: cmp             x4, x17
    // 0x14053dc: b.eq            #0x14053f4
    // 0x14053e0: r8 = GeneralResponse
    //     0x14053e0: add             x8, PP, #0x36, lsl #12  ; [pp+0x36160] Type: GeneralResponse
    //     0x14053e4: ldr             x8, [x8, #0x160]
    // 0x14053e8: r3 = Null
    //     0x14053e8: add             x3, PP, #0x37, lsl #12  ; [pp+0x371f8] Null
    //     0x14053ec: ldr             x3, [x3, #0x1f8]
    // 0x14053f0: r0 = DefaultTypeTest()
    //     0x14053f0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x14053f4: ldur            x1, [fp, #-0x18]
    // 0x14053f8: ldur            x2, [fp, #-0x10]
    // 0x14053fc: r0 = value=()
    //     0x14053fc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1405400: r0 = Null
    //     0x1405400: mov             x0, NULL
    // 0x1405404: LeaveFrame
    //     0x1405404: mov             SP, fp
    //     0x1405408: ldp             fp, lr, [SP], #0x10
    // 0x140540c: ret
    //     0x140540c: ret             
    // 0x1405410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1405410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1405414: b               #0x1405398
  }
  [closure] Set<String> <anonymous closure>(dynamic, String) {
    // ** addr: 0x158d404, size: 0x68
    // 0x158d404: EnterFrame
    //     0x158d404: stp             fp, lr, [SP, #-0x10]!
    //     0x158d408: mov             fp, SP
    // 0x158d40c: AllocStack(0x8)
    //     0x158d40c: sub             SP, SP, #8
    // 0x158d410: CheckStackOverflow
    //     0x158d410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x158d414: cmp             SP, x16
    //     0x158d418: b.ls            #0x158d464
    // 0x158d41c: r1 = <String>
    //     0x158d41c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x158d420: r0 = _Set()
    //     0x158d420: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x158d424: mov             x3, x0
    // 0x158d428: r0 = _Uint32List
    //     0x158d428: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x158d42c: stur            x3, [fp, #-8]
    // 0x158d430: StoreField: r3->field_1b = r0
    //     0x158d430: stur            w0, [x3, #0x1b]
    // 0x158d434: StoreField: r3->field_b = rZR
    //     0x158d434: stur            wzr, [x3, #0xb]
    // 0x158d438: r0 = const []
    //     0x158d438: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x158d43c: StoreField: r3->field_f = r0
    //     0x158d43c: stur            w0, [x3, #0xf]
    // 0x158d440: StoreField: r3->field_13 = rZR
    //     0x158d440: stur            wzr, [x3, #0x13]
    // 0x158d444: ArrayStore: r3[0] = rZR  ; List_4
    //     0x158d444: stur            wzr, [x3, #0x17]
    // 0x158d448: mov             x1, x3
    // 0x158d44c: ldr             x2, [fp, #0x10]
    // 0x158d450: r0 = add()
    //     0x158d450: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x158d454: ldur            x0, [fp, #-8]
    // 0x158d458: LeaveFrame
    //     0x158d458: mov             SP, fp
    //     0x158d45c: ldp             fp, lr, [SP], #0x10
    // 0x158d460: ret
    //     0x158d460: ret             
    // 0x158d464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x158d464: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x158d468: b               #0x158d41c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x15a01a0, size: 0x66c
    // 0x15a01a0: EnterFrame
    //     0x15a01a0: stp             fp, lr, [SP, #-0x10]!
    //     0x15a01a4: mov             fp, SP
    // 0x15a01a8: AllocStack(0x38)
    //     0x15a01a8: sub             SP, SP, #0x38
    // 0x15a01ac: SetupParameters(LoginController this /* r1 => r1, fp-0x8 */)
    //     0x15a01ac: stur            x1, [fp, #-8]
    // 0x15a01b0: CheckStackOverflow
    //     0x15a01b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a01b4: cmp             SP, x16
    //     0x15a01b8: b.ls            #0x15a0804
    // 0x15a01bc: r1 = 1
    //     0x15a01bc: movz            x1, #0x1
    // 0x15a01c0: r0 = AllocateContext()
    //     0x15a01c0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a01c4: mov             x2, x0
    // 0x15a01c8: ldur            x0, [fp, #-8]
    // 0x15a01cc: stur            x2, [fp, #-0x10]
    // 0x15a01d0: StoreField: r2->field_f = r0
    //     0x15a01d0: stur            w0, [x2, #0xf]
    // 0x15a01d4: mov             x1, x0
    // 0x15a01d8: r0 = showSearchOrBag()
    //     0x15a01d8: bl              #0x15a1334  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::showSearchOrBag
    // 0x15a01dc: ldur            x1, [fp, #-8]
    // 0x15a01e0: r0 = checkLoginStatus()
    //     0x15a01e0: bl              #0x15a1164  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::checkLoginStatus
    // 0x15a01e4: ldur            x1, [fp, #-8]
    // 0x15a01e8: r0 = listenOtp()
    //     0x15a01e8: bl              #0x15a1120  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::listenOtp
    // 0x15a01ec: ldur            x1, [fp, #-8]
    // 0x15a01f0: r0 = getHeaderConfigData()
    //     0x15a01f0: bl              #0x15a0894  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::getHeaderConfigData
    // 0x15a01f4: ldur            x0, [fp, #-8]
    // 0x15a01f8: LoadField: r2 = r0->field_63
    //     0x15a01f8: ldur            w2, [x0, #0x63]
    // 0x15a01fc: DecompressPointer r2
    //     0x15a01fc: add             x2, x2, HEAP, lsl #32
    // 0x15a0200: mov             x1, x2
    // 0x15a0204: stur            x2, [fp, #-0x18]
    // 0x15a0208: r0 = getConfigData()
    //     0x15a0208: bl              #0x8897b0  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getConfigData
    // 0x15a020c: ldur            x2, [fp, #-0x10]
    // 0x15a0210: r1 = Function '<anonymous closure>':.
    //     0x15a0210: add             x1, PP, #0x22, lsl #12  ; [pp+0x221e8] AnonymousClosure: (0x15a219c), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onInit (0x15a01a0)
    //     0x15a0214: ldr             x1, [x1, #0x1e8]
    // 0x15a0218: stur            x0, [fp, #-0x20]
    // 0x15a021c: r0 = AllocateClosure()
    //     0x15a021c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a0220: r16 = <Set<LocalConfigData>>
    //     0x15a0220: add             x16, PP, #0xe, lsl #12  ; [pp+0xe850] TypeArguments: <Set<LocalConfigData>>
    //     0x15a0224: ldr             x16, [x16, #0x850]
    // 0x15a0228: ldur            lr, [fp, #-0x20]
    // 0x15a022c: stp             lr, x16, [SP, #8]
    // 0x15a0230: str             x0, [SP]
    // 0x15a0234: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a0234: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a0238: r0 = then()
    //     0x15a0238: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a023c: ldur            x1, [fp, #-0x18]
    // 0x15a0240: r2 = "session_id"
    //     0x15a0240: add             x2, PP, #0xe, lsl #12  ; [pp+0xe880] "session_id"
    //     0x15a0244: ldr             x2, [x2, #0x880]
    // 0x15a0248: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15a0248: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15a024c: r0 = getString()
    //     0x15a024c: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x15a0250: ldur            x2, [fp, #-0x10]
    // 0x15a0254: r1 = Function '<anonymous closure>':.
    //     0x15a0254: add             x1, PP, #0x22, lsl #12  ; [pp+0x221f0] AnonymousClosure: (0x158d404), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onInit (0x15a01a0)
    //     0x15a0258: ldr             x1, [x1, #0x1f0]
    // 0x15a025c: stur            x0, [fp, #-0x18]
    // 0x15a0260: r0 = AllocateClosure()
    //     0x15a0260: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a0264: r16 = <Set<String>>
    //     0x15a0264: add             x16, PP, #0xe, lsl #12  ; [pp+0xe878] TypeArguments: <Set<String>>
    //     0x15a0268: ldr             x16, [x16, #0x878]
    // 0x15a026c: ldur            lr, [fp, #-0x18]
    // 0x15a0270: stp             lr, x16, [SP, #8]
    // 0x15a0274: str             x0, [SP]
    // 0x15a0278: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a0278: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a027c: r0 = then()
    //     0x15a027c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a0280: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15a0280: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15a0284: ldr             x0, [x0, #0x1c80]
    //     0x15a0288: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15a028c: cmp             w0, w16
    //     0x15a0290: b.ne            #0x15a029c
    //     0x15a0294: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15a0298: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15a029c: r0 = GetNavigation.arguments()
    //     0x15a029c: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x15a02a0: stur            x0, [fp, #-0x18]
    // 0x15a02a4: cmp             w0, NULL
    // 0x15a02a8: b.eq            #0x15a07b0
    // 0x15a02ac: ldur            x1, [fp, #-8]
    // 0x15a02b0: r16 = "bag_data"
    //     0x15a02b0: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d00] "bag_data"
    //     0x15a02b4: ldr             x16, [x16, #0xd00]
    // 0x15a02b8: stp             x16, x0, [SP]
    // 0x15a02bc: r4 = 0
    //     0x15a02bc: movz            x4, #0
    // 0x15a02c0: ldr             x0, [SP, #8]
    // 0x15a02c4: r5 = UnlinkedCall_0x613b5c
    //     0x15a02c4: add             x16, PP, #0x22, lsl #12  ; [pp+0x221f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a02c8: ldp             x5, lr, [x16, #0x1f8]
    // 0x15a02cc: blr             lr
    // 0x15a02d0: mov             x3, x0
    // 0x15a02d4: r2 = Null
    //     0x15a02d4: mov             x2, NULL
    // 0x15a02d8: r1 = Null
    //     0x15a02d8: mov             x1, NULL
    // 0x15a02dc: stur            x3, [fp, #-0x20]
    // 0x15a02e0: r4 = 60
    //     0x15a02e0: movz            x4, #0x3c
    // 0x15a02e4: branchIfSmi(r0, 0x15a02f0)
    //     0x15a02e4: tbz             w0, #0, #0x15a02f0
    // 0x15a02e8: r4 = LoadClassIdInstr(r0)
    //     0x15a02e8: ldur            x4, [x0, #-1]
    //     0x15a02ec: ubfx            x4, x4, #0xc, #0x14
    // 0x15a02f0: r17 = 5419
    //     0x15a02f0: movz            x17, #0x152b
    // 0x15a02f4: cmp             x4, x17
    // 0x15a02f8: b.eq            #0x15a0310
    // 0x15a02fc: r8 = BagResponse?
    //     0x15a02fc: add             x8, PP, #0x11, lsl #12  ; [pp+0x11d18] Type: BagResponse?
    //     0x15a0300: ldr             x8, [x8, #0xd18]
    // 0x15a0304: r3 = Null
    //     0x15a0304: add             x3, PP, #0x22, lsl #12  ; [pp+0x22208] Null
    //     0x15a0308: ldr             x3, [x3, #0x208]
    // 0x15a030c: r0 = DefaultNullableTypeTest()
    //     0x15a030c: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x15a0310: ldur            x0, [fp, #-0x20]
    // 0x15a0314: ldur            x1, [fp, #-8]
    // 0x15a0318: StoreField: r1->field_4b = r0
    //     0x15a0318: stur            w0, [x1, #0x4b]
    //     0x15a031c: ldurb           w16, [x1, #-1]
    //     0x15a0320: ldurb           w17, [x0, #-1]
    //     0x15a0324: and             x16, x17, x16, lsr #2
    //     0x15a0328: tst             x16, HEAP, lsr #32
    //     0x15a032c: b.eq            #0x15a0334
    //     0x15a0330: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a0334: ldur            x16, [fp, #-0x18]
    // 0x15a0338: r30 = "previousScreenSource"
    //     0x15a0338: add             lr, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15a033c: ldr             lr, [lr, #0x448]
    // 0x15a0340: stp             lr, x16, [SP]
    // 0x15a0344: r4 = 0
    //     0x15a0344: movz            x4, #0
    // 0x15a0348: ldr             x0, [SP, #8]
    // 0x15a034c: r16 = UnlinkedCall_0x613b5c
    //     0x15a034c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22218] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a0350: add             x16, x16, #0x218
    // 0x15a0354: ldp             x5, lr, [x16]
    // 0x15a0358: blr             lr
    // 0x15a035c: mov             x3, x0
    // 0x15a0360: r2 = Null
    //     0x15a0360: mov             x2, NULL
    // 0x15a0364: r1 = Null
    //     0x15a0364: mov             x1, NULL
    // 0x15a0368: stur            x3, [fp, #-0x20]
    // 0x15a036c: r4 = 60
    //     0x15a036c: movz            x4, #0x3c
    // 0x15a0370: branchIfSmi(r0, 0x15a037c)
    //     0x15a0370: tbz             w0, #0, #0x15a037c
    // 0x15a0374: r4 = LoadClassIdInstr(r0)
    //     0x15a0374: ldur            x4, [x0, #-1]
    //     0x15a0378: ubfx            x4, x4, #0xc, #0x14
    // 0x15a037c: sub             x4, x4, #0x5e
    // 0x15a0380: cmp             x4, #1
    // 0x15a0384: b.ls            #0x15a0398
    // 0x15a0388: r8 = String?
    //     0x15a0388: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a038c: r3 = Null
    //     0x15a038c: add             x3, PP, #0x22, lsl #12  ; [pp+0x22228] Null
    //     0x15a0390: ldr             x3, [x3, #0x228]
    // 0x15a0394: r0 = String?()
    //     0x15a0394: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a0398: ldur            x0, [fp, #-0x20]
    // 0x15a039c: ldur            x1, [fp, #-8]
    // 0x15a03a0: StoreField: r1->field_53 = r0
    //     0x15a03a0: stur            w0, [x1, #0x53]
    //     0x15a03a4: ldurb           w16, [x1, #-1]
    //     0x15a03a8: ldurb           w17, [x0, #-1]
    //     0x15a03ac: and             x16, x17, x16, lsr #2
    //     0x15a03b0: tst             x16, HEAP, lsr #32
    //     0x15a03b4: b.eq            #0x15a03bc
    //     0x15a03b8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a03bc: ldur            x16, [fp, #-0x18]
    // 0x15a03c0: r30 = "checkout_event_data"
    //     0x15a03c0: add             lr, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0x15a03c4: ldr             lr, [lr, #0xd50]
    // 0x15a03c8: stp             lr, x16, [SP]
    // 0x15a03cc: r4 = 0
    //     0x15a03cc: movz            x4, #0
    // 0x15a03d0: ldr             x0, [SP, #8]
    // 0x15a03d4: r16 = UnlinkedCall_0x613b5c
    //     0x15a03d4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22238] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a03d8: add             x16, x16, #0x238
    // 0x15a03dc: ldp             x5, lr, [x16]
    // 0x15a03e0: blr             lr
    // 0x15a03e4: mov             x3, x0
    // 0x15a03e8: r2 = Null
    //     0x15a03e8: mov             x2, NULL
    // 0x15a03ec: r1 = Null
    //     0x15a03ec: mov             x1, NULL
    // 0x15a03f0: stur            x3, [fp, #-0x20]
    // 0x15a03f4: r4 = 60
    //     0x15a03f4: movz            x4, #0x3c
    // 0x15a03f8: branchIfSmi(r0, 0x15a0404)
    //     0x15a03f8: tbz             w0, #0, #0x15a0404
    // 0x15a03fc: r4 = LoadClassIdInstr(r0)
    //     0x15a03fc: ldur            x4, [x0, #-1]
    //     0x15a0400: ubfx            x4, x4, #0xc, #0x14
    // 0x15a0404: r17 = 5260
    //     0x15a0404: movz            x17, #0x148c
    // 0x15a0408: cmp             x4, x17
    // 0x15a040c: b.eq            #0x15a0424
    // 0x15a0410: r8 = CheckoutEventData?
    //     0x15a0410: add             x8, PP, #0x11, lsl #12  ; [pp+0x11d68] Type: CheckoutEventData?
    //     0x15a0414: ldr             x8, [x8, #0xd68]
    // 0x15a0418: r3 = Null
    //     0x15a0418: add             x3, PP, #0x22, lsl #12  ; [pp+0x22248] Null
    //     0x15a041c: ldr             x3, [x3, #0x248]
    // 0x15a0420: r0 = DefaultNullableTypeTest()
    //     0x15a0420: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x15a0424: ldur            x0, [fp, #-0x20]
    // 0x15a0428: ldur            x1, [fp, #-8]
    // 0x15a042c: StoreField: r1->field_4f = r0
    //     0x15a042c: stur            w0, [x1, #0x4f]
    //     0x15a0430: ldurb           w16, [x1, #-1]
    //     0x15a0434: ldurb           w17, [x0, #-1]
    //     0x15a0438: and             x16, x17, x16, lsr #2
    //     0x15a043c: tst             x16, HEAP, lsr #32
    //     0x15a0440: b.eq            #0x15a0448
    //     0x15a0444: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a0448: ldur            x16, [fp, #-0x18]
    // 0x15a044c: r30 = "product_id"
    //     0x15a044c: add             lr, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x15a0450: ldr             lr, [lr, #0x9b8]
    // 0x15a0454: stp             lr, x16, [SP]
    // 0x15a0458: r4 = 0
    //     0x15a0458: movz            x4, #0
    // 0x15a045c: ldr             x0, [SP, #8]
    // 0x15a0460: r16 = UnlinkedCall_0x613b5c
    //     0x15a0460: add             x16, PP, #0x22, lsl #12  ; [pp+0x22258] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a0464: add             x16, x16, #0x258
    // 0x15a0468: ldp             x5, lr, [x16]
    // 0x15a046c: blr             lr
    // 0x15a0470: mov             x3, x0
    // 0x15a0474: r2 = Null
    //     0x15a0474: mov             x2, NULL
    // 0x15a0478: r1 = Null
    //     0x15a0478: mov             x1, NULL
    // 0x15a047c: stur            x3, [fp, #-0x20]
    // 0x15a0480: r4 = 60
    //     0x15a0480: movz            x4, #0x3c
    // 0x15a0484: branchIfSmi(r0, 0x15a0490)
    //     0x15a0484: tbz             w0, #0, #0x15a0490
    // 0x15a0488: r4 = LoadClassIdInstr(r0)
    //     0x15a0488: ldur            x4, [x0, #-1]
    //     0x15a048c: ubfx            x4, x4, #0xc, #0x14
    // 0x15a0490: sub             x4, x4, #0x5e
    // 0x15a0494: cmp             x4, #1
    // 0x15a0498: b.ls            #0x15a04ac
    // 0x15a049c: r8 = String?
    //     0x15a049c: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a04a0: r3 = Null
    //     0x15a04a0: add             x3, PP, #0x22, lsl #12  ; [pp+0x22268] Null
    //     0x15a04a4: ldr             x3, [x3, #0x268]
    // 0x15a04a8: r0 = String?()
    //     0x15a04a8: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a04ac: ldur            x0, [fp, #-0x20]
    // 0x15a04b0: ldur            x1, [fp, #-8]
    // 0x15a04b4: StoreField: r1->field_57 = r0
    //     0x15a04b4: stur            w0, [x1, #0x57]
    //     0x15a04b8: ldurb           w16, [x1, #-1]
    //     0x15a04bc: ldurb           w17, [x0, #-1]
    //     0x15a04c0: and             x16, x17, x16, lsr #2
    //     0x15a04c4: tst             x16, HEAP, lsr #32
    //     0x15a04c8: b.eq            #0x15a04d0
    //     0x15a04cc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a04d0: ldur            x16, [fp, #-0x18]
    // 0x15a04d4: r30 = "sku_id"
    //     0x15a04d4: add             lr, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x15a04d8: ldr             lr, [lr, #0x498]
    // 0x15a04dc: stp             lr, x16, [SP]
    // 0x15a04e0: r4 = 0
    //     0x15a04e0: movz            x4, #0
    // 0x15a04e4: ldr             x0, [SP, #8]
    // 0x15a04e8: r16 = UnlinkedCall_0x613b5c
    //     0x15a04e8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22278] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a04ec: add             x16, x16, #0x278
    // 0x15a04f0: ldp             x5, lr, [x16]
    // 0x15a04f4: blr             lr
    // 0x15a04f8: mov             x3, x0
    // 0x15a04fc: r2 = Null
    //     0x15a04fc: mov             x2, NULL
    // 0x15a0500: r1 = Null
    //     0x15a0500: mov             x1, NULL
    // 0x15a0504: stur            x3, [fp, #-0x20]
    // 0x15a0508: r4 = 60
    //     0x15a0508: movz            x4, #0x3c
    // 0x15a050c: branchIfSmi(r0, 0x15a0518)
    //     0x15a050c: tbz             w0, #0, #0x15a0518
    // 0x15a0510: r4 = LoadClassIdInstr(r0)
    //     0x15a0510: ldur            x4, [x0, #-1]
    //     0x15a0514: ubfx            x4, x4, #0xc, #0x14
    // 0x15a0518: sub             x4, x4, #0x5e
    // 0x15a051c: cmp             x4, #1
    // 0x15a0520: b.ls            #0x15a0534
    // 0x15a0524: r8 = String?
    //     0x15a0524: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a0528: r3 = Null
    //     0x15a0528: add             x3, PP, #0x22, lsl #12  ; [pp+0x22288] Null
    //     0x15a052c: ldr             x3, [x3, #0x288]
    // 0x15a0530: r0 = String?()
    //     0x15a0530: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a0534: ldur            x0, [fp, #-0x20]
    // 0x15a0538: ldur            x1, [fp, #-8]
    // 0x15a053c: StoreField: r1->field_5b = r0
    //     0x15a053c: stur            w0, [x1, #0x5b]
    //     0x15a0540: ldurb           w16, [x1, #-1]
    //     0x15a0544: ldurb           w17, [x0, #-1]
    //     0x15a0548: and             x16, x17, x16, lsr #2
    //     0x15a054c: tst             x16, HEAP, lsr #32
    //     0x15a0550: b.eq            #0x15a0558
    //     0x15a0554: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a0558: ldur            x16, [fp, #-0x18]
    // 0x15a055c: r30 = "quantity"
    //     0x15a055c: add             lr, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x15a0560: ldr             lr, [lr, #0x428]
    // 0x15a0564: stp             lr, x16, [SP]
    // 0x15a0568: r4 = 0
    //     0x15a0568: movz            x4, #0
    // 0x15a056c: ldr             x0, [SP, #8]
    // 0x15a0570: r16 = UnlinkedCall_0x613b5c
    //     0x15a0570: add             x16, PP, #0x22, lsl #12  ; [pp+0x22298] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a0574: add             x16, x16, #0x298
    // 0x15a0578: ldp             x5, lr, [x16]
    // 0x15a057c: blr             lr
    // 0x15a0580: mov             x3, x0
    // 0x15a0584: r2 = Null
    //     0x15a0584: mov             x2, NULL
    // 0x15a0588: r1 = Null
    //     0x15a0588: mov             x1, NULL
    // 0x15a058c: stur            x3, [fp, #-0x20]
    // 0x15a0590: r4 = 60
    //     0x15a0590: movz            x4, #0x3c
    // 0x15a0594: branchIfSmi(r0, 0x15a05a0)
    //     0x15a0594: tbz             w0, #0, #0x15a05a0
    // 0x15a0598: r4 = LoadClassIdInstr(r0)
    //     0x15a0598: ldur            x4, [x0, #-1]
    //     0x15a059c: ubfx            x4, x4, #0xc, #0x14
    // 0x15a05a0: sub             x4, x4, #0x5e
    // 0x15a05a4: cmp             x4, #1
    // 0x15a05a8: b.ls            #0x15a05bc
    // 0x15a05ac: r8 = String?
    //     0x15a05ac: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a05b0: r3 = Null
    //     0x15a05b0: add             x3, PP, #0x22, lsl #12  ; [pp+0x222a8] Null
    //     0x15a05b4: ldr             x3, [x3, #0x2a8]
    // 0x15a05b8: r0 = String?()
    //     0x15a05b8: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a05bc: ldur            x0, [fp, #-0x20]
    // 0x15a05c0: ldur            x1, [fp, #-8]
    // 0x15a05c4: StoreField: r1->field_5f = r0
    //     0x15a05c4: stur            w0, [x1, #0x5f]
    //     0x15a05c8: ldurb           w16, [x1, #-1]
    //     0x15a05cc: ldurb           w17, [x0, #-1]
    //     0x15a05d0: and             x16, x17, x16, lsr #2
    //     0x15a05d4: tst             x16, HEAP, lsr #32
    //     0x15a05d8: b.eq            #0x15a05e0
    //     0x15a05dc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a05e0: ldur            x16, [fp, #-0x18]
    // 0x15a05e4: r30 = "customization_request"
    //     0x15a05e4: add             lr, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x15a05e8: ldr             lr, [lr, #0x2b8]
    // 0x15a05ec: stp             lr, x16, [SP]
    // 0x15a05f0: r4 = 0
    //     0x15a05f0: movz            x4, #0
    // 0x15a05f4: ldr             x0, [SP, #8]
    // 0x15a05f8: r16 = UnlinkedCall_0x613b5c
    //     0x15a05f8: add             x16, PP, #0x22, lsl #12  ; [pp+0x222c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a05fc: add             x16, x16, #0x2c0
    // 0x15a0600: ldp             x5, lr, [x16]
    // 0x15a0604: blr             lr
    // 0x15a0608: r2 = Null
    //     0x15a0608: mov             x2, NULL
    // 0x15a060c: r1 = Null
    //     0x15a060c: mov             x1, NULL
    // 0x15a0610: r4 = 60
    //     0x15a0610: movz            x4, #0x3c
    // 0x15a0614: branchIfSmi(r0, 0x15a0620)
    //     0x15a0614: tbz             w0, #0, #0x15a0620
    // 0x15a0618: r4 = LoadClassIdInstr(r0)
    //     0x15a0618: ldur            x4, [x0, #-1]
    //     0x15a061c: ubfx            x4, x4, #0xc, #0x14
    // 0x15a0620: r17 = 5445
    //     0x15a0620: movz            x17, #0x1545
    // 0x15a0624: cmp             x4, x17
    // 0x15a0628: b.eq            #0x15a0640
    // 0x15a062c: r8 = CustomizedRequest?
    //     0x15a062c: add             x8, PP, #0x22, lsl #12  ; [pp+0x222d0] Type: CustomizedRequest?
    //     0x15a0630: ldr             x8, [x8, #0x2d0]
    // 0x15a0634: r3 = Null
    //     0x15a0634: add             x3, PP, #0x22, lsl #12  ; [pp+0x222d8] Null
    //     0x15a0638: ldr             x3, [x3, #0x2d8]
    // 0x15a063c: r0 = DefaultNullableTypeTest()
    //     0x15a063c: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x15a0640: ldur            x16, [fp, #-0x18]
    // 0x15a0644: r30 = "customization_prize"
    //     0x15a0644: add             lr, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x15a0648: ldr             lr, [lr, #0x2e8]
    // 0x15a064c: stp             lr, x16, [SP]
    // 0x15a0650: r4 = 0
    //     0x15a0650: movz            x4, #0
    // 0x15a0654: ldr             x0, [SP, #8]
    // 0x15a0658: r16 = UnlinkedCall_0x613b5c
    //     0x15a0658: add             x16, PP, #0x22, lsl #12  ; [pp+0x222f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a065c: add             x16, x16, #0x2f0
    // 0x15a0660: ldp             x5, lr, [x16]
    // 0x15a0664: blr             lr
    // 0x15a0668: mov             x3, x0
    // 0x15a066c: r2 = Null
    //     0x15a066c: mov             x2, NULL
    // 0x15a0670: r1 = Null
    //     0x15a0670: mov             x1, NULL
    // 0x15a0674: stur            x3, [fp, #-0x20]
    // 0x15a0678: r4 = 60
    //     0x15a0678: movz            x4, #0x3c
    // 0x15a067c: branchIfSmi(r0, 0x15a0688)
    //     0x15a067c: tbz             w0, #0, #0x15a0688
    // 0x15a0680: r4 = LoadClassIdInstr(r0)
    //     0x15a0680: ldur            x4, [x0, #-1]
    //     0x15a0684: ubfx            x4, x4, #0xc, #0x14
    // 0x15a0688: sub             x4, x4, #0x5e
    // 0x15a068c: cmp             x4, #1
    // 0x15a0690: b.ls            #0x15a06a4
    // 0x15a0694: r8 = String?
    //     0x15a0694: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a0698: r3 = Null
    //     0x15a0698: add             x3, PP, #0x22, lsl #12  ; [pp+0x22300] Null
    //     0x15a069c: ldr             x3, [x3, #0x300]
    // 0x15a06a0: r0 = String?()
    //     0x15a06a0: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a06a4: ldur            x0, [fp, #-0x20]
    // 0x15a06a8: ldur            x1, [fp, #-8]
    // 0x15a06ac: StoreField: r1->field_af = r0
    //     0x15a06ac: stur            w0, [x1, #0xaf]
    //     0x15a06b0: ldurb           w16, [x1, #-1]
    //     0x15a06b4: ldurb           w17, [x0, #-1]
    //     0x15a06b8: and             x16, x17, x16, lsr #2
    //     0x15a06bc: tst             x16, HEAP, lsr #32
    //     0x15a06c0: b.eq            #0x15a06c8
    //     0x15a06c4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a06c8: ldur            x16, [fp, #-0x18]
    // 0x15a06cc: r30 = "couponCode"
    //     0x15a06cc: add             lr, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x15a06d0: ldr             lr, [lr, #0x310]
    // 0x15a06d4: stp             lr, x16, [SP]
    // 0x15a06d8: r4 = 0
    //     0x15a06d8: movz            x4, #0
    // 0x15a06dc: ldr             x0, [SP, #8]
    // 0x15a06e0: r16 = UnlinkedCall_0x613b5c
    //     0x15a06e0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22318] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a06e4: add             x16, x16, #0x318
    // 0x15a06e8: ldp             x5, lr, [x16]
    // 0x15a06ec: blr             lr
    // 0x15a06f0: cmp             w0, NULL
    // 0x15a06f4: b.ne            #0x15a0700
    // 0x15a06f8: r2 = ""
    //     0x15a06f8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a06fc: b               #0x15a0704
    // 0x15a0700: mov             x2, x0
    // 0x15a0704: ldur            x1, [fp, #-8]
    // 0x15a0708: r0 = appliedCoupon=()
    //     0x15a0708: bl              #0x15a080c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::appliedCoupon=
    // 0x15a070c: ldur            x16, [fp, #-0x18]
    // 0x15a0710: r30 = "coming_from"
    //     0x15a0710: add             lr, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x15a0714: ldr             lr, [lr, #0x328]
    // 0x15a0718: stp             lr, x16, [SP]
    // 0x15a071c: r4 = 0
    //     0x15a071c: movz            x4, #0
    // 0x15a0720: ldr             x0, [SP, #8]
    // 0x15a0724: r16 = UnlinkedCall_0x613b5c
    //     0x15a0724: add             x16, PP, #0x22, lsl #12  ; [pp+0x22330] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a0728: add             x16, x16, #0x330
    // 0x15a072c: ldp             x5, lr, [x16]
    // 0x15a0730: blr             lr
    // 0x15a0734: cmp             w0, NULL
    // 0x15a0738: b.ne            #0x15a0744
    // 0x15a073c: r4 = ""
    //     0x15a073c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a0740: b               #0x15a0748
    // 0x15a0744: mov             x4, x0
    // 0x15a0748: ldur            x3, [fp, #-8]
    // 0x15a074c: mov             x0, x4
    // 0x15a0750: stur            x4, [fp, #-0x18]
    // 0x15a0754: r2 = Null
    //     0x15a0754: mov             x2, NULL
    // 0x15a0758: r1 = Null
    //     0x15a0758: mov             x1, NULL
    // 0x15a075c: r4 = 60
    //     0x15a075c: movz            x4, #0x3c
    // 0x15a0760: branchIfSmi(r0, 0x15a076c)
    //     0x15a0760: tbz             w0, #0, #0x15a076c
    // 0x15a0764: r4 = LoadClassIdInstr(r0)
    //     0x15a0764: ldur            x4, [x0, #-1]
    //     0x15a0768: ubfx            x4, x4, #0xc, #0x14
    // 0x15a076c: sub             x4, x4, #0x5e
    // 0x15a0770: cmp             x4, #1
    // 0x15a0774: b.ls            #0x15a0788
    // 0x15a0778: r8 = String
    //     0x15a0778: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15a077c: r3 = Null
    //     0x15a077c: add             x3, PP, #0x22, lsl #12  ; [pp+0x22340] Null
    //     0x15a0780: ldr             x3, [x3, #0x340]
    // 0x15a0784: r0 = String()
    //     0x15a0784: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15a0788: ldur            x0, [fp, #-0x18]
    // 0x15a078c: ldur            x2, [fp, #-8]
    // 0x15a0790: StoreField: r2->field_9f = r0
    //     0x15a0790: stur            w0, [x2, #0x9f]
    //     0x15a0794: ldurb           w16, [x2, #-1]
    //     0x15a0798: ldurb           w17, [x0, #-1]
    //     0x15a079c: and             x16, x17, x16, lsr #2
    //     0x15a07a0: tst             x16, HEAP, lsr #32
    //     0x15a07a4: b.eq            #0x15a07ac
    //     0x15a07a8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a07ac: b               #0x15a07b4
    // 0x15a07b0: ldur            x2, [fp, #-8]
    // 0x15a07b4: LoadField: r1 = r2->field_a7
    //     0x15a07b4: ldur            w1, [x2, #0xa7]
    // 0x15a07b8: DecompressPointer r1
    //     0x15a07b8: add             x1, x1, HEAP, lsl #32
    // 0x15a07bc: r0 = getConnectivityType()
    //     0x15a07bc: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x15a07c0: ldur            x2, [fp, #-0x10]
    // 0x15a07c4: r1 = Function '<anonymous closure>':.
    //     0x15a07c4: add             x1, PP, #0x22, lsl #12  ; [pp+0x22350] AnonymousClosure: (0x15a1404), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::onInit (0x15a01a0)
    //     0x15a07c8: ldr             x1, [x1, #0x350]
    // 0x15a07cc: stur            x0, [fp, #-0x10]
    // 0x15a07d0: r0 = AllocateClosure()
    //     0x15a07d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a07d4: r16 = <Null?>
    //     0x15a07d4: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x15a07d8: ldur            lr, [fp, #-0x10]
    // 0x15a07dc: stp             lr, x16, [SP, #8]
    // 0x15a07e0: str             x0, [SP]
    // 0x15a07e4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a07e4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a07e8: r0 = then()
    //     0x15a07e8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a07ec: ldur            x1, [fp, #-8]
    // 0x15a07f0: r0 = onInit()
    //     0x15a07f0: bl              #0x158ae60  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::onInit
    // 0x15a07f4: r0 = Null
    //     0x15a07f4: mov             x0, NULL
    // 0x15a07f8: LeaveFrame
    //     0x15a07f8: mov             SP, fp
    //     0x15a07fc: ldp             fp, lr, [SP], #0x10
    // 0x15a0800: ret
    //     0x15a0800: ret             
    // 0x15a0804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a0804: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a0808: b               #0x15a01bc
  }
  set _ appliedCoupon=(/* No info */) {
    // ** addr: 0x15a080c, size: 0x88
    // 0x15a080c: EnterFrame
    //     0x15a080c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a0810: mov             fp, SP
    // 0x15a0814: AllocStack(0x10)
    //     0x15a0814: sub             SP, SP, #0x10
    // 0x15a0818: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x15a0818: mov             x3, x2
    //     0x15a081c: stur            x2, [fp, #-0x10]
    // 0x15a0820: CheckStackOverflow
    //     0x15a0820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a0824: cmp             SP, x16
    //     0x15a0828: b.ls            #0x15a088c
    // 0x15a082c: LoadField: r4 = r1->field_77
    //     0x15a082c: ldur            w4, [x1, #0x77]
    // 0x15a0830: DecompressPointer r4
    //     0x15a0830: add             x4, x4, HEAP, lsl #32
    // 0x15a0834: mov             x0, x3
    // 0x15a0838: stur            x4, [fp, #-8]
    // 0x15a083c: r2 = Null
    //     0x15a083c: mov             x2, NULL
    // 0x15a0840: r1 = Null
    //     0x15a0840: mov             x1, NULL
    // 0x15a0844: r4 = 60
    //     0x15a0844: movz            x4, #0x3c
    // 0x15a0848: branchIfSmi(r0, 0x15a0854)
    //     0x15a0848: tbz             w0, #0, #0x15a0854
    // 0x15a084c: r4 = LoadClassIdInstr(r0)
    //     0x15a084c: ldur            x4, [x0, #-1]
    //     0x15a0850: ubfx            x4, x4, #0xc, #0x14
    // 0x15a0854: sub             x4, x4, #0x5e
    // 0x15a0858: cmp             x4, #1
    // 0x15a085c: b.ls            #0x15a0870
    // 0x15a0860: r8 = String
    //     0x15a0860: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15a0864: r3 = Null
    //     0x15a0864: add             x3, PP, #0x23, lsl #12  ; [pp+0x23748] Null
    //     0x15a0868: ldr             x3, [x3, #0x748]
    // 0x15a086c: r0 = String()
    //     0x15a086c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15a0870: ldur            x1, [fp, #-8]
    // 0x15a0874: ldur            x2, [fp, #-0x10]
    // 0x15a0878: r0 = value=()
    //     0x15a0878: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a087c: ldur            x0, [fp, #-0x10]
    // 0x15a0880: LeaveFrame
    //     0x15a0880: mov             SP, fp
    //     0x15a0884: ldp             fp, lr, [SP], #0x10
    // 0x15a0888: ret
    //     0x15a0888: ret             
    // 0x15a088c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a088c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a0890: b               #0x15a082c
  }
  _ getHeaderConfigData(/* No info */) async {
    // ** addr: 0x15a0894, size: 0x120
    // 0x15a0894: EnterFrame
    //     0x15a0894: stp             fp, lr, [SP, #-0x10]!
    //     0x15a0898: mov             fp, SP
    // 0x15a089c: AllocStack(0x40)
    //     0x15a089c: sub             SP, SP, #0x40
    // 0x15a08a0: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */)
    //     0x15a08a0: stur            NULL, [fp, #-8]
    //     0x15a08a4: stur            x1, [fp, #-0x10]
    // 0x15a08a8: CheckStackOverflow
    //     0x15a08a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a08ac: cmp             SP, x16
    //     0x15a08b0: b.ls            #0x15a09ac
    // 0x15a08b4: r1 = 1
    //     0x15a08b4: movz            x1, #0x1
    // 0x15a08b8: r0 = AllocateContext()
    //     0x15a08b8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a08bc: mov             x2, x0
    // 0x15a08c0: ldur            x1, [fp, #-0x10]
    // 0x15a08c4: stur            x2, [fp, #-0x18]
    // 0x15a08c8: StoreField: r2->field_f = r1
    //     0x15a08c8: stur            w1, [x2, #0xf]
    // 0x15a08cc: InitAsync() -> Future<void?>
    //     0x15a08cc: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15a08d0: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a08d4: ldur            x0, [fp, #-0x10]
    // 0x15a08d8: LoadField: r2 = r0->field_63
    //     0x15a08d8: ldur            w2, [x0, #0x63]
    // 0x15a08dc: DecompressPointer r2
    //     0x15a08dc: add             x2, x2, HEAP, lsl #32
    // 0x15a08e0: mov             x1, x2
    // 0x15a08e4: stur            x2, [fp, #-0x20]
    // 0x15a08e8: r0 = getConfigData()
    //     0x15a08e8: bl              #0x8897b0  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getConfigData
    // 0x15a08ec: mov             x1, x0
    // 0x15a08f0: stur            x1, [fp, #-0x28]
    // 0x15a08f4: r0 = Await()
    //     0x15a08f4: bl              #0x63248c  ; AwaitStub
    // 0x15a08f8: ldur            x1, [fp, #-0x10]
    // 0x15a08fc: mov             x2, x0
    // 0x15a0900: r0 = bumperCouponData=()
    //     0x15a0900: bl              #0x8a9750  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::bumperCouponData=
    // 0x15a0904: ldur            x1, [fp, #-0x10]
    // 0x15a0908: r0 = userData()
    //     0x15a0908: bl              #0x8a9718  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::userData
    // 0x15a090c: LoadField: r1 = r0->field_4b
    //     0x15a090c: ldur            w1, [x0, #0x4b]
    // 0x15a0910: DecompressPointer r1
    //     0x15a0910: add             x1, x1, HEAP, lsl #32
    // 0x15a0914: cmp             w1, NULL
    // 0x15a0918: b.ne            #0x15a0924
    // 0x15a091c: r0 = Null
    //     0x15a091c: mov             x0, NULL
    // 0x15a0920: b               #0x15a092c
    // 0x15a0924: LoadField: r0 = r1->field_23
    //     0x15a0924: ldur            w0, [x1, #0x23]
    // 0x15a0928: DecompressPointer r0
    //     0x15a0928: add             x0, x0, HEAP, lsl #32
    // 0x15a092c: cmp             w0, NULL
    // 0x15a0930: b.ne            #0x15a0938
    // 0x15a0934: r0 = ""
    //     0x15a0934: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a0938: ldur            x2, [fp, #-0x10]
    // 0x15a093c: StoreField: r2->field_9b = r0
    //     0x15a093c: stur            w0, [x2, #0x9b]
    //     0x15a0940: ldurb           w16, [x2, #-1]
    //     0x15a0944: ldurb           w17, [x0, #-1]
    //     0x15a0948: and             x16, x17, x16, lsr #2
    //     0x15a094c: tst             x16, HEAP, lsr #32
    //     0x15a0950: b.eq            #0x15a0958
    //     0x15a0954: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a0958: ldur            x1, [fp, #-0x20]
    // 0x15a095c: r0 = getOfferParams()
    //     0x15a095c: bl              #0x90cff8  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getOfferParams
    // 0x15a0960: ldur            x2, [fp, #-0x18]
    // 0x15a0964: r1 = Function '<anonymous closure>':.
    //     0x15a0964: add             x1, PP, #0x23, lsl #12  ; [pp+0x23758] AnonymousClosure: (0x15a1044), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::getHeaderConfigData (0x15a0894)
    //     0x15a0968: ldr             x1, [x1, #0x758]
    // 0x15a096c: stur            x0, [fp, #-0x20]
    // 0x15a0970: r0 = AllocateClosure()
    //     0x15a0970: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a0974: r16 = <Set<OfferParams>>
    //     0x15a0974: add             x16, PP, #0x23, lsl #12  ; [pp+0x23760] TypeArguments: <Set<OfferParams>>
    //     0x15a0978: ldr             x16, [x16, #0x760]
    // 0x15a097c: ldur            lr, [fp, #-0x20]
    // 0x15a0980: stp             lr, x16, [SP, #8]
    // 0x15a0984: str             x0, [SP]
    // 0x15a0988: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a0988: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a098c: r0 = then()
    //     0x15a098c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a0990: mov             x1, x0
    // 0x15a0994: stur            x1, [fp, #-0x20]
    // 0x15a0998: r0 = Await()
    //     0x15a0998: bl              #0x63248c  ; AwaitStub
    // 0x15a099c: ldur            x1, [fp, #-0x10]
    // 0x15a09a0: r0 = createBreadcrumbData()
    //     0x15a09a0: bl              #0x15a09b4  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::createBreadcrumbData
    // 0x15a09a4: r0 = Null
    //     0x15a09a4: mov             x0, NULL
    // 0x15a09a8: r0 = ReturnAsyncNotFuture()
    //     0x15a09a8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a09ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a09ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a09b0: b               #0x15a08b4
  }
  _ createBreadcrumbData(/* No info */) {
    // ** addr: 0x15a09b4, size: 0xd8
    // 0x15a09b4: EnterFrame
    //     0x15a09b4: stp             fp, lr, [SP, #-0x10]!
    //     0x15a09b8: mov             fp, SP
    // 0x15a09bc: AllocStack(0x20)
    //     0x15a09bc: sub             SP, SP, #0x20
    // 0x15a09c0: SetupParameters(LoginController this /* r1 => r0, fp-0x8 */)
    //     0x15a09c0: mov             x0, x1
    //     0x15a09c4: stur            x1, [fp, #-8]
    // 0x15a09c8: CheckStackOverflow
    //     0x15a09c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a09cc: cmp             SP, x16
    //     0x15a09d0: b.ls            #0x15a0a84
    // 0x15a09d4: LoadField: r1 = r0->field_7b
    //     0x15a09d4: ldur            w1, [x0, #0x7b]
    // 0x15a09d8: DecompressPointer r1
    //     0x15a09d8: add             x1, x1, HEAP, lsl #32
    // 0x15a09dc: r0 = value()
    //     0x15a09dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15a09e0: LoadField: r1 = r0->field_37
    //     0x15a09e0: ldur            w1, [x0, #0x37]
    // 0x15a09e4: DecompressPointer r1
    //     0x15a09e4: add             x1, x1, HEAP, lsl #32
    // 0x15a09e8: stur            x1, [fp, #-0x10]
    // 0x15a09ec: r16 = "cosmetics"
    //     0x15a09ec: add             x16, PP, #0xd, lsl #12  ; [pp+0xd7e8] "cosmetics"
    //     0x15a09f0: ldr             x16, [x16, #0x7e8]
    // 0x15a09f4: stp             x1, x16, [SP]
    // 0x15a09f8: r0 = ==()
    //     0x15a09f8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0x15a09fc: tbnz            w0, #4, #0x15a0a0c
    // 0x15a0a00: ldur            x1, [fp, #-8]
    // 0x15a0a04: r0 = createCosmeticsBreadCrumbData()
    //     0x15a0a04: bl              #0x15a0e5c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::createCosmeticsBreadCrumbData
    // 0x15a0a08: b               #0x15a0a74
    // 0x15a0a0c: r16 = "line"
    //     0x15a0a0c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd4a0] "line"
    //     0x15a0a10: ldr             x16, [x16, #0x4a0]
    // 0x15a0a14: ldur            lr, [fp, #-0x10]
    // 0x15a0a18: stp             lr, x16, [SP]
    // 0x15a0a1c: r0 = ==()
    //     0x15a0a1c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0x15a0a20: tbnz            w0, #4, #0x15a0a30
    // 0x15a0a24: ldur            x1, [fp, #-8]
    // 0x15a0a28: r0 = createLineBreadCrumbData()
    //     0x15a0a28: bl              #0x15a0c74  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::createLineBreadCrumbData
    // 0x15a0a2c: b               #0x15a0a74
    // 0x15a0a30: r16 = "basic"
    //     0x15a0a30: add             x16, PP, #0xd, lsl #12  ; [pp+0xd7f8] "basic"
    //     0x15a0a34: ldr             x16, [x16, #0x7f8]
    // 0x15a0a38: ldur            lr, [fp, #-0x10]
    // 0x15a0a3c: stp             lr, x16, [SP]
    // 0x15a0a40: r0 = ==()
    //     0x15a0a40: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0x15a0a44: tbnz            w0, #4, #0x15a0a54
    // 0x15a0a48: ldur            x1, [fp, #-8]
    // 0x15a0a4c: r0 = createCosmeticsBreadCrumbData()
    //     0x15a0a4c: bl              #0x15a0e5c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::createCosmeticsBreadCrumbData
    // 0x15a0a50: b               #0x15a0a74
    // 0x15a0a54: r16 = "glasses"
    //     0x15a0a54: add             x16, PP, #0xd, lsl #12  ; [pp+0xd808] "glasses"
    //     0x15a0a58: ldr             x16, [x16, #0x808]
    // 0x15a0a5c: ldur            lr, [fp, #-0x10]
    // 0x15a0a60: stp             lr, x16, [SP]
    // 0x15a0a64: r0 = ==()
    //     0x15a0a64: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0x15a0a68: tbnz            w0, #4, #0x15a0a74
    // 0x15a0a6c: ldur            x1, [fp, #-8]
    // 0x15a0a70: r0 = createGlassBreadCrumbData()
    //     0x15a0a70: bl              #0x15a0a8c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::createGlassBreadCrumbData
    // 0x15a0a74: r0 = Null
    //     0x15a0a74: mov             x0, NULL
    // 0x15a0a78: LeaveFrame
    //     0x15a0a78: mov             SP, fp
    //     0x15a0a7c: ldp             fp, lr, [SP], #0x10
    // 0x15a0a80: ret
    //     0x15a0a80: ret             
    // 0x15a0a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a0a84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a0a88: b               #0x15a09d4
  }
  _ createGlassBreadCrumbData(/* No info */) {
    // ** addr: 0x15a0a8c, size: 0x1e8
    // 0x15a0a8c: EnterFrame
    //     0x15a0a8c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a0a90: mov             fp, SP
    // 0x15a0a94: AllocStack(0x28)
    //     0x15a0a94: sub             SP, SP, #0x28
    // 0x15a0a98: CheckStackOverflow
    //     0x15a0a98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a0a9c: cmp             SP, x16
    //     0x15a0aa0: b.ls            #0x15a0c6c
    // 0x15a0aa4: r0 = BreadCrumbData()
    //     0x15a0aa4: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0aa8: mov             x1, x0
    // 0x15a0aac: r0 = "assets/images/check_circle.svg"
    //     0x15a0aac: add             x0, PP, #0x23, lsl #12  ; [pp+0x23768] "assets/images/check_circle.svg"
    //     0x15a0ab0: ldr             x0, [x0, #0x768]
    // 0x15a0ab4: stur            x1, [fp, #-8]
    // 0x15a0ab8: StoreField: r1->field_7 = r0
    //     0x15a0ab8: stur            w0, [x1, #7]
    // 0x15a0abc: r0 = "Login"
    //     0x15a0abc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23770] "Login"
    //     0x15a0ac0: ldr             x0, [x0, #0x770]
    // 0x15a0ac4: StoreField: r1->field_b = r0
    //     0x15a0ac4: stur            w0, [x1, #0xb]
    // 0x15a0ac8: r0 = true
    //     0x15a0ac8: add             x0, NULL, #0x20  ; true
    // 0x15a0acc: StoreField: r1->field_f = r0
    //     0x15a0acc: stur            w0, [x1, #0xf]
    // 0x15a0ad0: r0 = BreadCrumbData()
    //     0x15a0ad0: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0ad4: mov             x1, x0
    // 0x15a0ad8: r0 = "assets/images/breadcrumb_disabled_2.svg"
    //     0x15a0ad8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23778] "assets/images/breadcrumb_disabled_2.svg"
    //     0x15a0adc: ldr             x0, [x0, #0x778]
    // 0x15a0ae0: stur            x1, [fp, #-0x10]
    // 0x15a0ae4: StoreField: r1->field_7 = r0
    //     0x15a0ae4: stur            w0, [x1, #7]
    // 0x15a0ae8: r0 = "Address"
    //     0x15a0ae8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23780] "Address"
    //     0x15a0aec: ldr             x0, [x0, #0x780]
    // 0x15a0af0: StoreField: r1->field_b = r0
    //     0x15a0af0: stur            w0, [x1, #0xb]
    // 0x15a0af4: r0 = false
    //     0x15a0af4: add             x0, NULL, #0x30  ; false
    // 0x15a0af8: StoreField: r1->field_f = r0
    //     0x15a0af8: stur            w0, [x1, #0xf]
    // 0x15a0afc: r0 = BreadCrumbData()
    //     0x15a0afc: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0b00: mov             x1, x0
    // 0x15a0b04: r0 = "assets/images/breadcrumb_disabled_3.svg"
    //     0x15a0b04: add             x0, PP, #0x23, lsl #12  ; [pp+0x23788] "assets/images/breadcrumb_disabled_3.svg"
    //     0x15a0b08: ldr             x0, [x0, #0x788]
    // 0x15a0b0c: stur            x1, [fp, #-0x18]
    // 0x15a0b10: StoreField: r1->field_7 = r0
    //     0x15a0b10: stur            w0, [x1, #7]
    // 0x15a0b14: r0 = "Payment"
    //     0x15a0b14: add             x0, PP, #0x23, lsl #12  ; [pp+0x23790] "Payment"
    //     0x15a0b18: ldr             x0, [x0, #0x790]
    // 0x15a0b1c: StoreField: r1->field_b = r0
    //     0x15a0b1c: stur            w0, [x1, #0xb]
    // 0x15a0b20: r0 = false
    //     0x15a0b20: add             x0, NULL, #0x30  ; false
    // 0x15a0b24: StoreField: r1->field_f = r0
    //     0x15a0b24: stur            w0, [x1, #0xf]
    // 0x15a0b28: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x15a0b28: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15a0b2c: ldr             x0, [x0]
    //     0x15a0b30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15a0b34: cmp             w0, w16
    //     0x15a0b38: b.ne            #0x15a0b44
    //     0x15a0b3c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x15a0b40: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15a0b44: r1 = <BreadCrumbData>
    //     0x15a0b44: add             x1, PP, #0x23, lsl #12  ; [pp+0x23798] TypeArguments: <BreadCrumbData>
    //     0x15a0b48: ldr             x1, [x1, #0x798]
    // 0x15a0b4c: stur            x0, [fp, #-0x20]
    // 0x15a0b50: r0 = AllocateGrowableArray()
    //     0x15a0b50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15a0b54: mov             x2, x0
    // 0x15a0b58: ldur            x0, [fp, #-0x20]
    // 0x15a0b5c: stur            x2, [fp, #-0x28]
    // 0x15a0b60: StoreField: r2->field_f = r0
    //     0x15a0b60: stur            w0, [x2, #0xf]
    // 0x15a0b64: StoreField: r2->field_b = rZR
    //     0x15a0b64: stur            wzr, [x2, #0xb]
    // 0x15a0b68: LoadField: r1 = r0->field_b
    //     0x15a0b68: ldur            w1, [x0, #0xb]
    // 0x15a0b6c: cbnz            w1, #0x15a0b78
    // 0x15a0b70: mov             x1, x2
    // 0x15a0b74: r0 = _growToNextCapacity()
    //     0x15a0b74: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0b78: ldur            x2, [fp, #-0x28]
    // 0x15a0b7c: r0 = 2
    //     0x15a0b7c: movz            x0, #0x2
    // 0x15a0b80: StoreField: r2->field_b = r0
    //     0x15a0b80: stur            w0, [x2, #0xb]
    // 0x15a0b84: LoadField: r3 = r2->field_f
    //     0x15a0b84: ldur            w3, [x2, #0xf]
    // 0x15a0b88: DecompressPointer r3
    //     0x15a0b88: add             x3, x3, HEAP, lsl #32
    // 0x15a0b8c: mov             x1, x3
    // 0x15a0b90: ldur            x0, [fp, #-8]
    // 0x15a0b94: ArrayStore: r1[0] = r0  ; List_4
    //     0x15a0b94: add             x25, x1, #0xf
    //     0x15a0b98: str             w0, [x25]
    //     0x15a0b9c: tbz             w0, #0, #0x15a0bb8
    //     0x15a0ba0: ldurb           w16, [x1, #-1]
    //     0x15a0ba4: ldurb           w17, [x0, #-1]
    //     0x15a0ba8: and             x16, x17, x16, lsr #2
    //     0x15a0bac: tst             x16, HEAP, lsr #32
    //     0x15a0bb0: b.eq            #0x15a0bb8
    //     0x15a0bb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0bb8: LoadField: r0 = r3->field_b
    //     0x15a0bb8: ldur            w0, [x3, #0xb]
    // 0x15a0bbc: cmp             w0, #2
    // 0x15a0bc0: b.ne            #0x15a0bcc
    // 0x15a0bc4: mov             x1, x2
    // 0x15a0bc8: r0 = _growToNextCapacity()
    //     0x15a0bc8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0bcc: ldur            x2, [fp, #-0x28]
    // 0x15a0bd0: r0 = 4
    //     0x15a0bd0: movz            x0, #0x4
    // 0x15a0bd4: StoreField: r2->field_b = r0
    //     0x15a0bd4: stur            w0, [x2, #0xb]
    // 0x15a0bd8: LoadField: r3 = r2->field_f
    //     0x15a0bd8: ldur            w3, [x2, #0xf]
    // 0x15a0bdc: DecompressPointer r3
    //     0x15a0bdc: add             x3, x3, HEAP, lsl #32
    // 0x15a0be0: mov             x1, x3
    // 0x15a0be4: ldur            x0, [fp, #-0x10]
    // 0x15a0be8: ArrayStore: r1[1] = r0  ; List_4
    //     0x15a0be8: add             x25, x1, #0x13
    //     0x15a0bec: str             w0, [x25]
    //     0x15a0bf0: tbz             w0, #0, #0x15a0c0c
    //     0x15a0bf4: ldurb           w16, [x1, #-1]
    //     0x15a0bf8: ldurb           w17, [x0, #-1]
    //     0x15a0bfc: and             x16, x17, x16, lsr #2
    //     0x15a0c00: tst             x16, HEAP, lsr #32
    //     0x15a0c04: b.eq            #0x15a0c0c
    //     0x15a0c08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0c0c: LoadField: r0 = r3->field_b
    //     0x15a0c0c: ldur            w0, [x3, #0xb]
    // 0x15a0c10: cmp             w0, #4
    // 0x15a0c14: b.ne            #0x15a0c20
    // 0x15a0c18: mov             x1, x2
    // 0x15a0c1c: r0 = _growToNextCapacity()
    //     0x15a0c1c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0c20: ldur            x2, [fp, #-0x28]
    // 0x15a0c24: r3 = 6
    //     0x15a0c24: movz            x3, #0x6
    // 0x15a0c28: StoreField: r2->field_b = r3
    //     0x15a0c28: stur            w3, [x2, #0xb]
    // 0x15a0c2c: LoadField: r1 = r2->field_f
    //     0x15a0c2c: ldur            w1, [x2, #0xf]
    // 0x15a0c30: DecompressPointer r1
    //     0x15a0c30: add             x1, x1, HEAP, lsl #32
    // 0x15a0c34: ldur            x0, [fp, #-0x18]
    // 0x15a0c38: ArrayStore: r1[2] = r0  ; List_4
    //     0x15a0c38: add             x25, x1, #0x17
    //     0x15a0c3c: str             w0, [x25]
    //     0x15a0c40: tbz             w0, #0, #0x15a0c5c
    //     0x15a0c44: ldurb           w16, [x1, #-1]
    //     0x15a0c48: ldurb           w17, [x0, #-1]
    //     0x15a0c4c: and             x16, x17, x16, lsr #2
    //     0x15a0c50: tst             x16, HEAP, lsr #32
    //     0x15a0c54: b.eq            #0x15a0c5c
    //     0x15a0c58: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0c5c: r0 = Null
    //     0x15a0c5c: mov             x0, NULL
    // 0x15a0c60: LeaveFrame
    //     0x15a0c60: mov             SP, fp
    //     0x15a0c64: ldp             fp, lr, [SP], #0x10
    // 0x15a0c68: ret
    //     0x15a0c68: ret             
    // 0x15a0c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a0c6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a0c70: b               #0x15a0aa4
  }
  _ createLineBreadCrumbData(/* No info */) {
    // ** addr: 0x15a0c74, size: 0x1e8
    // 0x15a0c74: EnterFrame
    //     0x15a0c74: stp             fp, lr, [SP, #-0x10]!
    //     0x15a0c78: mov             fp, SP
    // 0x15a0c7c: AllocStack(0x28)
    //     0x15a0c7c: sub             SP, SP, #0x28
    // 0x15a0c80: CheckStackOverflow
    //     0x15a0c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a0c84: cmp             SP, x16
    //     0x15a0c88: b.ls            #0x15a0e54
    // 0x15a0c8c: r0 = BreadCrumbData()
    //     0x15a0c8c: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0c90: mov             x1, x0
    // 0x15a0c94: r0 = "assets/images/breadcrumb_1.svg"
    //     0x15a0c94: add             x0, PP, #0x23, lsl #12  ; [pp+0x237a8] "assets/images/breadcrumb_1.svg"
    //     0x15a0c98: ldr             x0, [x0, #0x7a8]
    // 0x15a0c9c: stur            x1, [fp, #-8]
    // 0x15a0ca0: StoreField: r1->field_7 = r0
    //     0x15a0ca0: stur            w0, [x1, #7]
    // 0x15a0ca4: r0 = "Login"
    //     0x15a0ca4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23770] "Login"
    //     0x15a0ca8: ldr             x0, [x0, #0x770]
    // 0x15a0cac: StoreField: r1->field_b = r0
    //     0x15a0cac: stur            w0, [x1, #0xb]
    // 0x15a0cb0: r0 = true
    //     0x15a0cb0: add             x0, NULL, #0x20  ; true
    // 0x15a0cb4: StoreField: r1->field_f = r0
    //     0x15a0cb4: stur            w0, [x1, #0xf]
    // 0x15a0cb8: r0 = BreadCrumbData()
    //     0x15a0cb8: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0cbc: mov             x1, x0
    // 0x15a0cc0: r0 = "assets/images/breadcrumb_disabled_line_2.svg"
    //     0x15a0cc0: add             x0, PP, #0x23, lsl #12  ; [pp+0x237b0] "assets/images/breadcrumb_disabled_line_2.svg"
    //     0x15a0cc4: ldr             x0, [x0, #0x7b0]
    // 0x15a0cc8: stur            x1, [fp, #-0x10]
    // 0x15a0ccc: StoreField: r1->field_7 = r0
    //     0x15a0ccc: stur            w0, [x1, #7]
    // 0x15a0cd0: r0 = "Address"
    //     0x15a0cd0: add             x0, PP, #0x23, lsl #12  ; [pp+0x23780] "Address"
    //     0x15a0cd4: ldr             x0, [x0, #0x780]
    // 0x15a0cd8: StoreField: r1->field_b = r0
    //     0x15a0cd8: stur            w0, [x1, #0xb]
    // 0x15a0cdc: r0 = false
    //     0x15a0cdc: add             x0, NULL, #0x30  ; false
    // 0x15a0ce0: StoreField: r1->field_f = r0
    //     0x15a0ce0: stur            w0, [x1, #0xf]
    // 0x15a0ce4: r0 = BreadCrumbData()
    //     0x15a0ce4: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0ce8: mov             x1, x0
    // 0x15a0cec: r0 = "assets/images/breadcrumb_disabled_line_3.svg"
    //     0x15a0cec: add             x0, PP, #0x23, lsl #12  ; [pp+0x237b8] "assets/images/breadcrumb_disabled_line_3.svg"
    //     0x15a0cf0: ldr             x0, [x0, #0x7b8]
    // 0x15a0cf4: stur            x1, [fp, #-0x18]
    // 0x15a0cf8: StoreField: r1->field_7 = r0
    //     0x15a0cf8: stur            w0, [x1, #7]
    // 0x15a0cfc: r0 = "Payment"
    //     0x15a0cfc: add             x0, PP, #0x23, lsl #12  ; [pp+0x23790] "Payment"
    //     0x15a0d00: ldr             x0, [x0, #0x790]
    // 0x15a0d04: StoreField: r1->field_b = r0
    //     0x15a0d04: stur            w0, [x1, #0xb]
    // 0x15a0d08: r0 = false
    //     0x15a0d08: add             x0, NULL, #0x30  ; false
    // 0x15a0d0c: StoreField: r1->field_f = r0
    //     0x15a0d0c: stur            w0, [x1, #0xf]
    // 0x15a0d10: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x15a0d10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15a0d14: ldr             x0, [x0]
    //     0x15a0d18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15a0d1c: cmp             w0, w16
    //     0x15a0d20: b.ne            #0x15a0d2c
    //     0x15a0d24: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x15a0d28: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15a0d2c: r1 = <BreadCrumbData>
    //     0x15a0d2c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23798] TypeArguments: <BreadCrumbData>
    //     0x15a0d30: ldr             x1, [x1, #0x798]
    // 0x15a0d34: stur            x0, [fp, #-0x20]
    // 0x15a0d38: r0 = AllocateGrowableArray()
    //     0x15a0d38: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15a0d3c: mov             x2, x0
    // 0x15a0d40: ldur            x0, [fp, #-0x20]
    // 0x15a0d44: stur            x2, [fp, #-0x28]
    // 0x15a0d48: StoreField: r2->field_f = r0
    //     0x15a0d48: stur            w0, [x2, #0xf]
    // 0x15a0d4c: StoreField: r2->field_b = rZR
    //     0x15a0d4c: stur            wzr, [x2, #0xb]
    // 0x15a0d50: LoadField: r1 = r0->field_b
    //     0x15a0d50: ldur            w1, [x0, #0xb]
    // 0x15a0d54: cbnz            w1, #0x15a0d60
    // 0x15a0d58: mov             x1, x2
    // 0x15a0d5c: r0 = _growToNextCapacity()
    //     0x15a0d5c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0d60: ldur            x2, [fp, #-0x28]
    // 0x15a0d64: r0 = 2
    //     0x15a0d64: movz            x0, #0x2
    // 0x15a0d68: StoreField: r2->field_b = r0
    //     0x15a0d68: stur            w0, [x2, #0xb]
    // 0x15a0d6c: LoadField: r3 = r2->field_f
    //     0x15a0d6c: ldur            w3, [x2, #0xf]
    // 0x15a0d70: DecompressPointer r3
    //     0x15a0d70: add             x3, x3, HEAP, lsl #32
    // 0x15a0d74: mov             x1, x3
    // 0x15a0d78: ldur            x0, [fp, #-8]
    // 0x15a0d7c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15a0d7c: add             x25, x1, #0xf
    //     0x15a0d80: str             w0, [x25]
    //     0x15a0d84: tbz             w0, #0, #0x15a0da0
    //     0x15a0d88: ldurb           w16, [x1, #-1]
    //     0x15a0d8c: ldurb           w17, [x0, #-1]
    //     0x15a0d90: and             x16, x17, x16, lsr #2
    //     0x15a0d94: tst             x16, HEAP, lsr #32
    //     0x15a0d98: b.eq            #0x15a0da0
    //     0x15a0d9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0da0: LoadField: r0 = r3->field_b
    //     0x15a0da0: ldur            w0, [x3, #0xb]
    // 0x15a0da4: cmp             w0, #2
    // 0x15a0da8: b.ne            #0x15a0db4
    // 0x15a0dac: mov             x1, x2
    // 0x15a0db0: r0 = _growToNextCapacity()
    //     0x15a0db0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0db4: ldur            x2, [fp, #-0x28]
    // 0x15a0db8: r0 = 4
    //     0x15a0db8: movz            x0, #0x4
    // 0x15a0dbc: StoreField: r2->field_b = r0
    //     0x15a0dbc: stur            w0, [x2, #0xb]
    // 0x15a0dc0: LoadField: r3 = r2->field_f
    //     0x15a0dc0: ldur            w3, [x2, #0xf]
    // 0x15a0dc4: DecompressPointer r3
    //     0x15a0dc4: add             x3, x3, HEAP, lsl #32
    // 0x15a0dc8: mov             x1, x3
    // 0x15a0dcc: ldur            x0, [fp, #-0x10]
    // 0x15a0dd0: ArrayStore: r1[1] = r0  ; List_4
    //     0x15a0dd0: add             x25, x1, #0x13
    //     0x15a0dd4: str             w0, [x25]
    //     0x15a0dd8: tbz             w0, #0, #0x15a0df4
    //     0x15a0ddc: ldurb           w16, [x1, #-1]
    //     0x15a0de0: ldurb           w17, [x0, #-1]
    //     0x15a0de4: and             x16, x17, x16, lsr #2
    //     0x15a0de8: tst             x16, HEAP, lsr #32
    //     0x15a0dec: b.eq            #0x15a0df4
    //     0x15a0df0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0df4: LoadField: r0 = r3->field_b
    //     0x15a0df4: ldur            w0, [x3, #0xb]
    // 0x15a0df8: cmp             w0, #4
    // 0x15a0dfc: b.ne            #0x15a0e08
    // 0x15a0e00: mov             x1, x2
    // 0x15a0e04: r0 = _growToNextCapacity()
    //     0x15a0e04: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0e08: ldur            x2, [fp, #-0x28]
    // 0x15a0e0c: r3 = 6
    //     0x15a0e0c: movz            x3, #0x6
    // 0x15a0e10: StoreField: r2->field_b = r3
    //     0x15a0e10: stur            w3, [x2, #0xb]
    // 0x15a0e14: LoadField: r1 = r2->field_f
    //     0x15a0e14: ldur            w1, [x2, #0xf]
    // 0x15a0e18: DecompressPointer r1
    //     0x15a0e18: add             x1, x1, HEAP, lsl #32
    // 0x15a0e1c: ldur            x0, [fp, #-0x18]
    // 0x15a0e20: ArrayStore: r1[2] = r0  ; List_4
    //     0x15a0e20: add             x25, x1, #0x17
    //     0x15a0e24: str             w0, [x25]
    //     0x15a0e28: tbz             w0, #0, #0x15a0e44
    //     0x15a0e2c: ldurb           w16, [x1, #-1]
    //     0x15a0e30: ldurb           w17, [x0, #-1]
    //     0x15a0e34: and             x16, x17, x16, lsr #2
    //     0x15a0e38: tst             x16, HEAP, lsr #32
    //     0x15a0e3c: b.eq            #0x15a0e44
    //     0x15a0e40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0e44: r0 = Null
    //     0x15a0e44: mov             x0, NULL
    // 0x15a0e48: LeaveFrame
    //     0x15a0e48: mov             SP, fp
    //     0x15a0e4c: ldp             fp, lr, [SP], #0x10
    // 0x15a0e50: ret
    //     0x15a0e50: ret             
    // 0x15a0e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a0e54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a0e58: b               #0x15a0c8c
  }
  _ createCosmeticsBreadCrumbData(/* No info */) {
    // ** addr: 0x15a0e5c, size: 0x1e8
    // 0x15a0e5c: EnterFrame
    //     0x15a0e5c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a0e60: mov             fp, SP
    // 0x15a0e64: AllocStack(0x28)
    //     0x15a0e64: sub             SP, SP, #0x28
    // 0x15a0e68: CheckStackOverflow
    //     0x15a0e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a0e6c: cmp             SP, x16
    //     0x15a0e70: b.ls            #0x15a103c
    // 0x15a0e74: r0 = BreadCrumbData()
    //     0x15a0e74: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0e78: mov             x1, x0
    // 0x15a0e7c: r0 = "assets/images/breadcrumb_filled_1.svg"
    //     0x15a0e7c: add             x0, PP, #0x23, lsl #12  ; [pp+0x237a0] "assets/images/breadcrumb_filled_1.svg"
    //     0x15a0e80: ldr             x0, [x0, #0x7a0]
    // 0x15a0e84: stur            x1, [fp, #-8]
    // 0x15a0e88: StoreField: r1->field_7 = r0
    //     0x15a0e88: stur            w0, [x1, #7]
    // 0x15a0e8c: r0 = "Login"
    //     0x15a0e8c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23770] "Login"
    //     0x15a0e90: ldr             x0, [x0, #0x770]
    // 0x15a0e94: StoreField: r1->field_b = r0
    //     0x15a0e94: stur            w0, [x1, #0xb]
    // 0x15a0e98: r0 = true
    //     0x15a0e98: add             x0, NULL, #0x20  ; true
    // 0x15a0e9c: StoreField: r1->field_f = r0
    //     0x15a0e9c: stur            w0, [x1, #0xf]
    // 0x15a0ea0: r0 = BreadCrumbData()
    //     0x15a0ea0: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0ea4: mov             x1, x0
    // 0x15a0ea8: r0 = "assets/images/breadcrumb_disabled_2.svg"
    //     0x15a0ea8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23778] "assets/images/breadcrumb_disabled_2.svg"
    //     0x15a0eac: ldr             x0, [x0, #0x778]
    // 0x15a0eb0: stur            x1, [fp, #-0x10]
    // 0x15a0eb4: StoreField: r1->field_7 = r0
    //     0x15a0eb4: stur            w0, [x1, #7]
    // 0x15a0eb8: r0 = "Address"
    //     0x15a0eb8: add             x0, PP, #0x23, lsl #12  ; [pp+0x23780] "Address"
    //     0x15a0ebc: ldr             x0, [x0, #0x780]
    // 0x15a0ec0: StoreField: r1->field_b = r0
    //     0x15a0ec0: stur            w0, [x1, #0xb]
    // 0x15a0ec4: r0 = false
    //     0x15a0ec4: add             x0, NULL, #0x30  ; false
    // 0x15a0ec8: StoreField: r1->field_f = r0
    //     0x15a0ec8: stur            w0, [x1, #0xf]
    // 0x15a0ecc: r0 = BreadCrumbData()
    //     0x15a0ecc: bl              #0xa08634  ; AllocateBreadCrumbDataStub -> BreadCrumbData (size=0x14)
    // 0x15a0ed0: mov             x1, x0
    // 0x15a0ed4: r0 = "assets/images/breadcrumb_disabled_3.svg"
    //     0x15a0ed4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23788] "assets/images/breadcrumb_disabled_3.svg"
    //     0x15a0ed8: ldr             x0, [x0, #0x788]
    // 0x15a0edc: stur            x1, [fp, #-0x18]
    // 0x15a0ee0: StoreField: r1->field_7 = r0
    //     0x15a0ee0: stur            w0, [x1, #7]
    // 0x15a0ee4: r0 = "Payment"
    //     0x15a0ee4: add             x0, PP, #0x23, lsl #12  ; [pp+0x23790] "Payment"
    //     0x15a0ee8: ldr             x0, [x0, #0x790]
    // 0x15a0eec: StoreField: r1->field_b = r0
    //     0x15a0eec: stur            w0, [x1, #0xb]
    // 0x15a0ef0: r0 = false
    //     0x15a0ef0: add             x0, NULL, #0x30  ; false
    // 0x15a0ef4: StoreField: r1->field_f = r0
    //     0x15a0ef4: stur            w0, [x1, #0xf]
    // 0x15a0ef8: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x15a0ef8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15a0efc: ldr             x0, [x0]
    //     0x15a0f00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15a0f04: cmp             w0, w16
    //     0x15a0f08: b.ne            #0x15a0f14
    //     0x15a0f0c: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x15a0f10: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15a0f14: r1 = <BreadCrumbData>
    //     0x15a0f14: add             x1, PP, #0x23, lsl #12  ; [pp+0x23798] TypeArguments: <BreadCrumbData>
    //     0x15a0f18: ldr             x1, [x1, #0x798]
    // 0x15a0f1c: stur            x0, [fp, #-0x20]
    // 0x15a0f20: r0 = AllocateGrowableArray()
    //     0x15a0f20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15a0f24: mov             x2, x0
    // 0x15a0f28: ldur            x0, [fp, #-0x20]
    // 0x15a0f2c: stur            x2, [fp, #-0x28]
    // 0x15a0f30: StoreField: r2->field_f = r0
    //     0x15a0f30: stur            w0, [x2, #0xf]
    // 0x15a0f34: StoreField: r2->field_b = rZR
    //     0x15a0f34: stur            wzr, [x2, #0xb]
    // 0x15a0f38: LoadField: r1 = r0->field_b
    //     0x15a0f38: ldur            w1, [x0, #0xb]
    // 0x15a0f3c: cbnz            w1, #0x15a0f48
    // 0x15a0f40: mov             x1, x2
    // 0x15a0f44: r0 = _growToNextCapacity()
    //     0x15a0f44: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0f48: ldur            x2, [fp, #-0x28]
    // 0x15a0f4c: r0 = 2
    //     0x15a0f4c: movz            x0, #0x2
    // 0x15a0f50: StoreField: r2->field_b = r0
    //     0x15a0f50: stur            w0, [x2, #0xb]
    // 0x15a0f54: LoadField: r3 = r2->field_f
    //     0x15a0f54: ldur            w3, [x2, #0xf]
    // 0x15a0f58: DecompressPointer r3
    //     0x15a0f58: add             x3, x3, HEAP, lsl #32
    // 0x15a0f5c: mov             x1, x3
    // 0x15a0f60: ldur            x0, [fp, #-8]
    // 0x15a0f64: ArrayStore: r1[0] = r0  ; List_4
    //     0x15a0f64: add             x25, x1, #0xf
    //     0x15a0f68: str             w0, [x25]
    //     0x15a0f6c: tbz             w0, #0, #0x15a0f88
    //     0x15a0f70: ldurb           w16, [x1, #-1]
    //     0x15a0f74: ldurb           w17, [x0, #-1]
    //     0x15a0f78: and             x16, x17, x16, lsr #2
    //     0x15a0f7c: tst             x16, HEAP, lsr #32
    //     0x15a0f80: b.eq            #0x15a0f88
    //     0x15a0f84: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0f88: LoadField: r0 = r3->field_b
    //     0x15a0f88: ldur            w0, [x3, #0xb]
    // 0x15a0f8c: cmp             w0, #2
    // 0x15a0f90: b.ne            #0x15a0f9c
    // 0x15a0f94: mov             x1, x2
    // 0x15a0f98: r0 = _growToNextCapacity()
    //     0x15a0f98: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0f9c: ldur            x2, [fp, #-0x28]
    // 0x15a0fa0: r0 = 4
    //     0x15a0fa0: movz            x0, #0x4
    // 0x15a0fa4: StoreField: r2->field_b = r0
    //     0x15a0fa4: stur            w0, [x2, #0xb]
    // 0x15a0fa8: LoadField: r3 = r2->field_f
    //     0x15a0fa8: ldur            w3, [x2, #0xf]
    // 0x15a0fac: DecompressPointer r3
    //     0x15a0fac: add             x3, x3, HEAP, lsl #32
    // 0x15a0fb0: mov             x1, x3
    // 0x15a0fb4: ldur            x0, [fp, #-0x10]
    // 0x15a0fb8: ArrayStore: r1[1] = r0  ; List_4
    //     0x15a0fb8: add             x25, x1, #0x13
    //     0x15a0fbc: str             w0, [x25]
    //     0x15a0fc0: tbz             w0, #0, #0x15a0fdc
    //     0x15a0fc4: ldurb           w16, [x1, #-1]
    //     0x15a0fc8: ldurb           w17, [x0, #-1]
    //     0x15a0fcc: and             x16, x17, x16, lsr #2
    //     0x15a0fd0: tst             x16, HEAP, lsr #32
    //     0x15a0fd4: b.eq            #0x15a0fdc
    //     0x15a0fd8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a0fdc: LoadField: r0 = r3->field_b
    //     0x15a0fdc: ldur            w0, [x3, #0xb]
    // 0x15a0fe0: cmp             w0, #4
    // 0x15a0fe4: b.ne            #0x15a0ff0
    // 0x15a0fe8: mov             x1, x2
    // 0x15a0fec: r0 = _growToNextCapacity()
    //     0x15a0fec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x15a0ff0: ldur            x2, [fp, #-0x28]
    // 0x15a0ff4: r3 = 6
    //     0x15a0ff4: movz            x3, #0x6
    // 0x15a0ff8: StoreField: r2->field_b = r3
    //     0x15a0ff8: stur            w3, [x2, #0xb]
    // 0x15a0ffc: LoadField: r1 = r2->field_f
    //     0x15a0ffc: ldur            w1, [x2, #0xf]
    // 0x15a1000: DecompressPointer r1
    //     0x15a1000: add             x1, x1, HEAP, lsl #32
    // 0x15a1004: ldur            x0, [fp, #-0x18]
    // 0x15a1008: ArrayStore: r1[2] = r0  ; List_4
    //     0x15a1008: add             x25, x1, #0x17
    //     0x15a100c: str             w0, [x25]
    //     0x15a1010: tbz             w0, #0, #0x15a102c
    //     0x15a1014: ldurb           w16, [x1, #-1]
    //     0x15a1018: ldurb           w17, [x0, #-1]
    //     0x15a101c: and             x16, x17, x16, lsr #2
    //     0x15a1020: tst             x16, HEAP, lsr #32
    //     0x15a1024: b.eq            #0x15a102c
    //     0x15a1028: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15a102c: r0 = Null
    //     0x15a102c: mov             x0, NULL
    // 0x15a1030: LeaveFrame
    //     0x15a1030: mov             SP, fp
    //     0x15a1034: ldp             fp, lr, [SP], #0x10
    // 0x15a1038: ret
    //     0x15a1038: ret             
    // 0x15a103c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a103c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1040: b               #0x15a0e74
  }
  [closure] Set<OfferParams> <anonymous closure>(dynamic, OfferParams) {
    // ** addr: 0x15a1044, size: 0x90
    // 0x15a1044: EnterFrame
    //     0x15a1044: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1048: mov             fp, SP
    // 0x15a104c: AllocStack(0x10)
    //     0x15a104c: sub             SP, SP, #0x10
    // 0x15a1050: SetupParameters()
    //     0x15a1050: ldr             x0, [fp, #0x18]
    //     0x15a1054: ldur            w2, [x0, #0x17]
    //     0x15a1058: add             x2, x2, HEAP, lsl #32
    //     0x15a105c: stur            x2, [fp, #-8]
    // 0x15a1060: CheckStackOverflow
    //     0x15a1060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1064: cmp             SP, x16
    //     0x15a1068: b.ls            #0x15a10cc
    // 0x15a106c: r1 = <OfferParams>
    //     0x15a106c: add             x1, PP, #0xa, lsl #12  ; [pp+0xac38] TypeArguments: <OfferParams>
    //     0x15a1070: ldr             x1, [x1, #0xc38]
    // 0x15a1074: r0 = _Set()
    //     0x15a1074: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x15a1078: mov             x3, x0
    // 0x15a107c: r0 = _Uint32List
    //     0x15a107c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x15a1080: stur            x3, [fp, #-0x10]
    // 0x15a1084: StoreField: r3->field_1b = r0
    //     0x15a1084: stur            w0, [x3, #0x1b]
    // 0x15a1088: StoreField: r3->field_b = rZR
    //     0x15a1088: stur            wzr, [x3, #0xb]
    // 0x15a108c: r0 = const []
    //     0x15a108c: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x15a1090: StoreField: r3->field_f = r0
    //     0x15a1090: stur            w0, [x3, #0xf]
    // 0x15a1094: StoreField: r3->field_13 = rZR
    //     0x15a1094: stur            wzr, [x3, #0x13]
    // 0x15a1098: ArrayStore: r3[0] = rZR  ; List_4
    //     0x15a1098: stur            wzr, [x3, #0x17]
    // 0x15a109c: ldur            x0, [fp, #-8]
    // 0x15a10a0: LoadField: r1 = r0->field_f
    //     0x15a10a0: ldur            w1, [x0, #0xf]
    // 0x15a10a4: DecompressPointer r1
    //     0x15a10a4: add             x1, x1, HEAP, lsl #32
    // 0x15a10a8: ldr             x2, [fp, #0x10]
    // 0x15a10ac: r0 = offerParams=()
    //     0x15a10ac: bl              #0x15a10d4  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::offerParams=
    // 0x15a10b0: ldur            x1, [fp, #-0x10]
    // 0x15a10b4: ldr             x2, [fp, #0x10]
    // 0x15a10b8: r0 = add()
    //     0x15a10b8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a10bc: ldur            x0, [fp, #-0x10]
    // 0x15a10c0: LeaveFrame
    //     0x15a10c0: mov             SP, fp
    //     0x15a10c4: ldp             fp, lr, [SP], #0x10
    // 0x15a10c8: ret
    //     0x15a10c8: ret             
    // 0x15a10cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a10cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a10d0: b               #0x15a106c
  }
  set _ offerParams=(/* No info */) {
    // ** addr: 0x15a10d4, size: 0x4c
    // 0x15a10d4: EnterFrame
    //     0x15a10d4: stp             fp, lr, [SP, #-0x10]!
    //     0x15a10d8: mov             fp, SP
    // 0x15a10dc: AllocStack(0x8)
    //     0x15a10dc: sub             SP, SP, #8
    // 0x15a10e0: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x15a10e0: mov             x0, x2
    //     0x15a10e4: stur            x2, [fp, #-8]
    // 0x15a10e8: CheckStackOverflow
    //     0x15a10e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a10ec: cmp             SP, x16
    //     0x15a10f0: b.ls            #0x15a1118
    // 0x15a10f4: LoadField: r2 = r1->field_b3
    //     0x15a10f4: ldur            w2, [x1, #0xb3]
    // 0x15a10f8: DecompressPointer r2
    //     0x15a10f8: add             x2, x2, HEAP, lsl #32
    // 0x15a10fc: mov             x1, x2
    // 0x15a1100: mov             x2, x0
    // 0x15a1104: r0 = value=()
    //     0x15a1104: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a1108: ldur            x0, [fp, #-8]
    // 0x15a110c: LeaveFrame
    //     0x15a110c: mov             SP, fp
    //     0x15a1110: ldp             fp, lr, [SP], #0x10
    // 0x15a1114: ret
    //     0x15a1114: ret             
    // 0x15a1118: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a1118: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a111c: b               #0x15a10f4
  }
  _ listenOtp(/* No info */) async {
    // ** addr: 0x15a1120, size: 0x44
    // 0x15a1120: EnterFrame
    //     0x15a1120: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1124: mov             fp, SP
    // 0x15a1128: AllocStack(0x10)
    //     0x15a1128: sub             SP, SP, #0x10
    // 0x15a112c: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */)
    //     0x15a112c: stur            NULL, [fp, #-8]
    //     0x15a1130: stur            x1, [fp, #-0x10]
    // 0x15a1134: CheckStackOverflow
    //     0x15a1134: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1138: cmp             SP, x16
    //     0x15a113c: b.ls            #0x15a115c
    // 0x15a1140: InitAsync() -> Future<void?>
    //     0x15a1140: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15a1144: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a1148: r1 = Null
    //     0x15a1148: mov             x1, NULL
    // 0x15a114c: r0 = SmsAutoFill()
    //     0x15a114c: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0x15a1150: mov             x1, x0
    // 0x15a1154: r0 = listenForCode()
    //     0x15a1154: bl              #0x905bf0  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::listenForCode
    // 0x15a1158: r0 = ReturnAsync()
    //     0x15a1158: b               #0x63cf54  ; ReturnAsyncStub
    // 0x15a115c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a115c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1160: b               #0x15a1140
  }
  _ checkLoginStatus(/* No info */) {
    // ** addr: 0x15a1164, size: 0x94
    // 0x15a1164: EnterFrame
    //     0x15a1164: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1168: mov             fp, SP
    // 0x15a116c: AllocStack(0x28)
    //     0x15a116c: sub             SP, SP, #0x28
    // 0x15a1170: SetupParameters(LoginController this /* r1 => r1, fp-0x8 */)
    //     0x15a1170: stur            x1, [fp, #-8]
    // 0x15a1174: CheckStackOverflow
    //     0x15a1174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1178: cmp             SP, x16
    //     0x15a117c: b.ls            #0x15a11f0
    // 0x15a1180: r1 = 1
    //     0x15a1180: movz            x1, #0x1
    // 0x15a1184: r0 = AllocateContext()
    //     0x15a1184: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a1188: mov             x3, x0
    // 0x15a118c: ldur            x0, [fp, #-8]
    // 0x15a1190: stur            x3, [fp, #-0x10]
    // 0x15a1194: StoreField: r3->field_f = r0
    //     0x15a1194: stur            w0, [x3, #0xf]
    // 0x15a1198: LoadField: r1 = r0->field_63
    //     0x15a1198: ldur            w1, [x0, #0x63]
    // 0x15a119c: DecompressPointer r1
    //     0x15a119c: add             x1, x1, HEAP, lsl #32
    // 0x15a11a0: r2 = "token"
    //     0x15a11a0: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x15a11a4: ldr             x2, [x2, #0x958]
    // 0x15a11a8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15a11a8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15a11ac: r0 = getString()
    //     0x15a11ac: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x15a11b0: ldur            x2, [fp, #-0x10]
    // 0x15a11b4: r1 = Function '<anonymous closure>':.
    //     0x15a11b4: add             x1, PP, #0x23, lsl #12  ; [pp+0x238d8] AnonymousClosure: (0x15a11f8), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::checkLoginStatus (0x15a1164)
    //     0x15a11b8: ldr             x1, [x1, #0x8d8]
    // 0x15a11bc: stur            x0, [fp, #-8]
    // 0x15a11c0: r0 = AllocateClosure()
    //     0x15a11c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a11c4: r16 = <Set<Set<bool>>>
    //     0x15a11c4: add             x16, PP, #0x23, lsl #12  ; [pp+0x238e0] TypeArguments: <Set<Set<bool>>>
    //     0x15a11c8: ldr             x16, [x16, #0x8e0]
    // 0x15a11cc: ldur            lr, [fp, #-8]
    // 0x15a11d0: stp             lr, x16, [SP, #8]
    // 0x15a11d4: str             x0, [SP]
    // 0x15a11d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a11d8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a11dc: r0 = then()
    //     0x15a11dc: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a11e0: r0 = Null
    //     0x15a11e0: mov             x0, NULL
    // 0x15a11e4: LeaveFrame
    //     0x15a11e4: mov             SP, fp
    //     0x15a11e8: ldp             fp, lr, [SP], #0x10
    // 0x15a11ec: ret
    //     0x15a11ec: ret             
    // 0x15a11f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a11f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a11f4: b               #0x15a1180
  }
  [closure] Set<Set<bool>> <anonymous closure>(dynamic, String) {
    // ** addr: 0x15a11f8, size: 0x13c
    // 0x15a11f8: EnterFrame
    //     0x15a11f8: stp             fp, lr, [SP, #-0x10]!
    //     0x15a11fc: mov             fp, SP
    // 0x15a1200: AllocStack(0x18)
    //     0x15a1200: sub             SP, SP, #0x18
    // 0x15a1204: SetupParameters()
    //     0x15a1204: ldr             x0, [fp, #0x18]
    //     0x15a1208: ldur            w2, [x0, #0x17]
    //     0x15a120c: add             x2, x2, HEAP, lsl #32
    //     0x15a1210: stur            x2, [fp, #-8]
    // 0x15a1214: CheckStackOverflow
    //     0x15a1214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1218: cmp             SP, x16
    //     0x15a121c: b.ls            #0x15a132c
    // 0x15a1220: r1 = <Set<bool>>
    //     0x15a1220: add             x1, PP, #0x11, lsl #12  ; [pp+0x11f68] TypeArguments: <Set<bool>>
    //     0x15a1224: ldr             x1, [x1, #0xf68]
    // 0x15a1228: r0 = _Set()
    //     0x15a1228: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x15a122c: mov             x2, x0
    // 0x15a1230: r0 = _Uint32List
    //     0x15a1230: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x15a1234: stur            x2, [fp, #-0x10]
    // 0x15a1238: StoreField: r2->field_1b = r0
    //     0x15a1238: stur            w0, [x2, #0x1b]
    // 0x15a123c: StoreField: r2->field_b = rZR
    //     0x15a123c: stur            wzr, [x2, #0xb]
    // 0x15a1240: r3 = const []
    //     0x15a1240: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x15a1244: StoreField: r2->field_f = r3
    //     0x15a1244: stur            w3, [x2, #0xf]
    // 0x15a1248: StoreField: r2->field_13 = rZR
    //     0x15a1248: stur            wzr, [x2, #0x13]
    // 0x15a124c: ArrayStore: r2[0] = rZR  ; List_4
    //     0x15a124c: stur            wzr, [x2, #0x17]
    // 0x15a1250: ldr             x1, [fp, #0x10]
    // 0x15a1254: LoadField: r4 = r1->field_7
    //     0x15a1254: ldur            w4, [x1, #7]
    // 0x15a1258: cbz             w4, #0x15a12bc
    // 0x15a125c: ldur            x4, [fp, #-8]
    // 0x15a1260: r1 = <bool>
    //     0x15a1260: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x15a1264: r0 = _Set()
    //     0x15a1264: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x15a1268: mov             x3, x0
    // 0x15a126c: r0 = _Uint32List
    //     0x15a126c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x15a1270: stur            x3, [fp, #-0x18]
    // 0x15a1274: StoreField: r3->field_1b = r0
    //     0x15a1274: stur            w0, [x3, #0x1b]
    // 0x15a1278: StoreField: r3->field_b = rZR
    //     0x15a1278: stur            wzr, [x3, #0xb]
    // 0x15a127c: r2 = const []
    //     0x15a127c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x15a1280: StoreField: r3->field_f = r2
    //     0x15a1280: stur            w2, [x3, #0xf]
    // 0x15a1284: StoreField: r3->field_13 = rZR
    //     0x15a1284: stur            wzr, [x3, #0x13]
    // 0x15a1288: ArrayStore: r3[0] = rZR  ; List_4
    //     0x15a1288: stur            wzr, [x3, #0x17]
    // 0x15a128c: ldur            x4, [fp, #-8]
    // 0x15a1290: LoadField: r1 = r4->field_f
    //     0x15a1290: ldur            w1, [x4, #0xf]
    // 0x15a1294: DecompressPointer r1
    //     0x15a1294: add             x1, x1, HEAP, lsl #32
    // 0x15a1298: r2 = true
    //     0x15a1298: add             x2, NULL, #0x20  ; true
    // 0x15a129c: r0 = couponType=()
    //     0x15a129c: bl              #0xbd0440  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::couponType=
    // 0x15a12a0: ldur            x1, [fp, #-0x18]
    // 0x15a12a4: r2 = true
    //     0x15a12a4: add             x2, NULL, #0x20  ; true
    // 0x15a12a8: r0 = add()
    //     0x15a12a8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a12ac: ldur            x1, [fp, #-0x10]
    // 0x15a12b0: ldur            x2, [fp, #-0x18]
    // 0x15a12b4: r0 = add()
    //     0x15a12b4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a12b8: b               #0x15a131c
    // 0x15a12bc: ldur            x4, [fp, #-8]
    // 0x15a12c0: mov             x2, x3
    // 0x15a12c4: r1 = <bool>
    //     0x15a12c4: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x15a12c8: r0 = _Set()
    //     0x15a12c8: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x15a12cc: mov             x3, x0
    // 0x15a12d0: r0 = _Uint32List
    //     0x15a12d0: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x15a12d4: stur            x3, [fp, #-0x18]
    // 0x15a12d8: StoreField: r3->field_1b = r0
    //     0x15a12d8: stur            w0, [x3, #0x1b]
    // 0x15a12dc: StoreField: r3->field_b = rZR
    //     0x15a12dc: stur            wzr, [x3, #0xb]
    // 0x15a12e0: r0 = const []
    //     0x15a12e0: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x15a12e4: StoreField: r3->field_f = r0
    //     0x15a12e4: stur            w0, [x3, #0xf]
    // 0x15a12e8: StoreField: r3->field_13 = rZR
    //     0x15a12e8: stur            wzr, [x3, #0x13]
    // 0x15a12ec: ArrayStore: r3[0] = rZR  ; List_4
    //     0x15a12ec: stur            wzr, [x3, #0x17]
    // 0x15a12f0: ldur            x0, [fp, #-8]
    // 0x15a12f4: LoadField: r1 = r0->field_f
    //     0x15a12f4: ldur            w1, [x0, #0xf]
    // 0x15a12f8: DecompressPointer r1
    //     0x15a12f8: add             x1, x1, HEAP, lsl #32
    // 0x15a12fc: r2 = false
    //     0x15a12fc: add             x2, NULL, #0x30  ; false
    // 0x15a1300: r0 = couponType=()
    //     0x15a1300: bl              #0xbd0440  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::couponType=
    // 0x15a1304: ldur            x1, [fp, #-0x18]
    // 0x15a1308: r2 = false
    //     0x15a1308: add             x2, NULL, #0x30  ; false
    // 0x15a130c: r0 = add()
    //     0x15a130c: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a1310: ldur            x1, [fp, #-0x10]
    // 0x15a1314: ldur            x2, [fp, #-0x18]
    // 0x15a1318: r0 = add()
    //     0x15a1318: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a131c: ldur            x0, [fp, #-0x10]
    // 0x15a1320: LeaveFrame
    //     0x15a1320: mov             SP, fp
    //     0x15a1324: ldp             fp, lr, [SP], #0x10
    // 0x15a1328: ret
    //     0x15a1328: ret             
    // 0x15a132c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a132c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1330: b               #0x15a1220
  }
  _ showSearchOrBag(/* No info */) {
    // ** addr: 0x15a1334, size: 0x50
    // 0x15a1334: EnterFrame
    //     0x15a1334: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1338: mov             fp, SP
    // 0x15a133c: AllocStack(0x8)
    //     0x15a133c: sub             SP, SP, #8
    // 0x15a1340: SetupParameters(LoginController this /* r1 => r0, fp-0x8 */)
    //     0x15a1340: mov             x0, x1
    //     0x15a1344: stur            x1, [fp, #-8]
    // 0x15a1348: CheckStackOverflow
    //     0x15a1348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a134c: cmp             SP, x16
    //     0x15a1350: b.ls            #0x15a137c
    // 0x15a1354: mov             x1, x0
    // 0x15a1358: r2 = false
    //     0x15a1358: add             x2, NULL, #0x30  ; false
    // 0x15a135c: r0 = isShowSearch=()
    //     0x15a135c: bl              #0x15a13c4  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::isShowSearch=
    // 0x15a1360: ldur            x1, [fp, #-8]
    // 0x15a1364: r2 = true
    //     0x15a1364: add             x2, NULL, #0x20  ; true
    // 0x15a1368: r0 = isShowBag=()
    //     0x15a1368: bl              #0x15a1384  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::isShowBag=
    // 0x15a136c: r0 = Null
    //     0x15a136c: mov             x0, NULL
    // 0x15a1370: LeaveFrame
    //     0x15a1370: mov             SP, fp
    //     0x15a1374: ldp             fp, lr, [SP], #0x10
    // 0x15a1378: ret
    //     0x15a1378: ret             
    // 0x15a137c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a137c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1380: b               #0x15a1354
  }
  set _ isShowBag=(/* No info */) {
    // ** addr: 0x15a1384, size: 0x40
    // 0x15a1384: EnterFrame
    //     0x15a1384: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1388: mov             fp, SP
    // 0x15a138c: CheckStackOverflow
    //     0x15a138c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1390: cmp             SP, x16
    //     0x15a1394: b.ls            #0x15a13bc
    // 0x15a1398: LoadField: r0 = r1->field_c3
    //     0x15a1398: ldur            w0, [x1, #0xc3]
    // 0x15a139c: DecompressPointer r0
    //     0x15a139c: add             x0, x0, HEAP, lsl #32
    // 0x15a13a0: mov             x1, x0
    // 0x15a13a4: r2 = true
    //     0x15a13a4: add             x2, NULL, #0x20  ; true
    // 0x15a13a8: r0 = value=()
    //     0x15a13a8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a13ac: r0 = true
    //     0x15a13ac: add             x0, NULL, #0x20  ; true
    // 0x15a13b0: LeaveFrame
    //     0x15a13b0: mov             SP, fp
    //     0x15a13b4: ldp             fp, lr, [SP], #0x10
    // 0x15a13b8: ret
    //     0x15a13b8: ret             
    // 0x15a13bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a13bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a13c0: b               #0x15a1398
  }
  set _ isShowSearch=(/* No info */) {
    // ** addr: 0x15a13c4, size: 0x40
    // 0x15a13c4: EnterFrame
    //     0x15a13c4: stp             fp, lr, [SP, #-0x10]!
    //     0x15a13c8: mov             fp, SP
    // 0x15a13cc: CheckStackOverflow
    //     0x15a13cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a13d0: cmp             SP, x16
    //     0x15a13d4: b.ls            #0x15a13fc
    // 0x15a13d8: LoadField: r0 = r1->field_bf
    //     0x15a13d8: ldur            w0, [x1, #0xbf]
    // 0x15a13dc: DecompressPointer r0
    //     0x15a13dc: add             x0, x0, HEAP, lsl #32
    // 0x15a13e0: mov             x1, x0
    // 0x15a13e4: r2 = false
    //     0x15a13e4: add             x2, NULL, #0x30  ; false
    // 0x15a13e8: r0 = value=()
    //     0x15a13e8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a13ec: r0 = false
    //     0x15a13ec: add             x0, NULL, #0x30  ; false
    // 0x15a13f0: LeaveFrame
    //     0x15a13f0: mov             SP, fp
    //     0x15a13f4: ldp             fp, lr, [SP], #0x10
    // 0x15a13f8: ret
    //     0x15a13f8: ret             
    // 0x15a13fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a13fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1400: b               #0x15a13d8
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x15a1404, size: 0x78
    // 0x15a1404: EnterFrame
    //     0x15a1404: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1408: mov             fp, SP
    // 0x15a140c: AllocStack(0x8)
    //     0x15a140c: sub             SP, SP, #8
    // 0x15a1410: SetupParameters()
    //     0x15a1410: ldr             x0, [fp, #0x18]
    //     0x15a1414: ldur            w2, [x0, #0x17]
    //     0x15a1418: add             x2, x2, HEAP, lsl #32
    //     0x15a141c: stur            x2, [fp, #-8]
    // 0x15a1420: CheckStackOverflow
    //     0x15a1420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1424: cmp             SP, x16
    //     0x15a1428: b.ls            #0x15a1474
    // 0x15a142c: LoadField: r1 = r2->field_f
    //     0x15a142c: ldur            w1, [x2, #0xf]
    // 0x15a1430: DecompressPointer r1
    //     0x15a1430: add             x1, x1, HEAP, lsl #32
    // 0x15a1434: ldr             x0, [fp, #0x10]
    // 0x15a1438: StoreField: r1->field_ab = r0
    //     0x15a1438: stur            w0, [x1, #0xab]
    // 0x15a143c: tbnz            w0, #4, #0x15a1464
    // 0x15a1440: r0 = getBagCount()
    //     0x15a1440: bl              #0x15a1c00  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::getBagCount
    // 0x15a1444: ldur            x0, [fp, #-8]
    // 0x15a1448: LoadField: r1 = r0->field_f
    //     0x15a1448: ldur            w1, [x0, #0xf]
    // 0x15a144c: DecompressPointer r1
    //     0x15a144c: add             x1, x1, HEAP, lsl #32
    // 0x15a1450: LoadField: r0 = r1->field_9f
    //     0x15a1450: ldur            w0, [x1, #0x9f]
    // 0x15a1454: DecompressPointer r0
    //     0x15a1454: add             x0, x0, HEAP, lsl #32
    // 0x15a1458: LoadField: r2 = r0->field_7
    //     0x15a1458: ldur            w2, [x0, #7]
    // 0x15a145c: cbz             w2, #0x15a1464
    // 0x15a1460: r0 = performCheckout()
    //     0x15a1460: bl              #0x15a147c  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::performCheckout
    // 0x15a1464: r0 = Null
    //     0x15a1464: mov             x0, NULL
    // 0x15a1468: LeaveFrame
    //     0x15a1468: mov             SP, fp
    //     0x15a146c: ldp             fp, lr, [SP], #0x10
    // 0x15a1470: ret
    //     0x15a1470: ret             
    // 0x15a1474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a1474: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1478: b               #0x15a142c
  }
  _ performCheckout(/* No info */) async {
    // ** addr: 0x15a147c, size: 0x1a0
    // 0x15a147c: EnterFrame
    //     0x15a147c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1480: mov             fp, SP
    // 0x15a1484: AllocStack(0x70)
    //     0x15a1484: sub             SP, SP, #0x70
    // 0x15a1488: SetupParameters(LoginController this /* r1 => r1, fp-0x10 */)
    //     0x15a1488: stur            NULL, [fp, #-8]
    //     0x15a148c: stur            x1, [fp, #-0x10]
    // 0x15a1490: CheckStackOverflow
    //     0x15a1490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1494: cmp             SP, x16
    //     0x15a1498: b.ls            #0x15a1608
    // 0x15a149c: r1 = 1
    //     0x15a149c: movz            x1, #0x1
    // 0x15a14a0: r0 = AllocateContext()
    //     0x15a14a0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a14a4: mov             x2, x0
    // 0x15a14a8: ldur            x1, [fp, #-0x10]
    // 0x15a14ac: stur            x2, [fp, #-0x18]
    // 0x15a14b0: StoreField: r2->field_f = r1
    //     0x15a14b0: stur            w1, [x2, #0xf]
    // 0x15a14b4: InitAsync() -> Future
    //     0x15a14b4: mov             x0, NULL
    //     0x15a14b8: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a14bc: ldur            x0, [fp, #-0x10]
    // 0x15a14c0: LoadField: r2 = r0->field_47
    //     0x15a14c0: ldur            w2, [x0, #0x47]
    // 0x15a14c4: DecompressPointer r2
    //     0x15a14c4: add             x2, x2, HEAP, lsl #32
    // 0x15a14c8: r16 = Sentinel
    //     0x15a14c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x15a14cc: cmp             w2, w16
    // 0x15a14d0: b.eq            #0x15a1610
    // 0x15a14d4: stur            x2, [fp, #-0x38]
    // 0x15a14d8: LoadField: r7 = r0->field_57
    //     0x15a14d8: ldur            w7, [x0, #0x57]
    // 0x15a14dc: DecompressPointer r7
    //     0x15a14dc: add             x7, x7, HEAP, lsl #32
    // 0x15a14e0: stur            x7, [fp, #-0x30]
    // 0x15a14e4: cmp             w7, NULL
    // 0x15a14e8: b.eq            #0x15a14f8
    // 0x15a14ec: r3 = "buyNow"
    //     0x15a14ec: add             x3, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x15a14f0: ldr             x3, [x3, #0x358]
    // 0x15a14f4: b               #0x15a14fc
    // 0x15a14f8: r3 = ""
    //     0x15a14f8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a14fc: stur            x3, [fp, #-0x28]
    // 0x15a1500: LoadField: r4 = r0->field_5b
    //     0x15a1500: ldur            w4, [x0, #0x5b]
    // 0x15a1504: DecompressPointer r4
    //     0x15a1504: add             x4, x4, HEAP, lsl #32
    // 0x15a1508: stur            x4, [fp, #-0x20]
    // 0x15a150c: LoadField: r1 = r0->field_5f
    //     0x15a150c: ldur            w1, [x0, #0x5f]
    // 0x15a1510: DecompressPointer r1
    //     0x15a1510: add             x1, x1, HEAP, lsl #32
    // 0x15a1514: cmp             w1, NULL
    // 0x15a1518: b.ne            #0x15a1520
    // 0x15a151c: r1 = "0"
    //     0x15a151c: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x15a1520: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15a1520: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15a1524: r0 = parse()
    //     0x15a1524: bl              #0x6255f0  ; [dart:core] int::parse
    // 0x15a1528: ldur            x1, [fp, #-0x10]
    // 0x15a152c: stur            x0, [fp, #-0x40]
    // 0x15a1530: r0 = bumperCouponData()
    //     0x15a1530: bl              #0x9be348  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::bumperCouponData
    // 0x15a1534: mov             x4, x0
    // 0x15a1538: ldur            x0, [fp, #-0x10]
    // 0x15a153c: stur            x4, [fp, #-0x50]
    // 0x15a1540: LoadField: r5 = r0->field_9b
    //     0x15a1540: ldur            w5, [x0, #0x9b]
    // 0x15a1544: DecompressPointer r5
    //     0x15a1544: add             x5, x5, HEAP, lsl #32
    // 0x15a1548: stur            x5, [fp, #-0x48]
    // 0x15a154c: LoadField: r1 = r0->field_af
    //     0x15a154c: ldur            w1, [x0, #0xaf]
    // 0x15a1550: DecompressPointer r1
    //     0x15a1550: add             x1, x1, HEAP, lsl #32
    // 0x15a1554: cmp             w1, NULL
    // 0x15a1558: b.ne            #0x15a1564
    // 0x15a155c: r5 = Null
    //     0x15a155c: mov             x5, NULL
    // 0x15a1560: b               #0x15a158c
    // 0x15a1564: r2 = "₹"
    //     0x15a1564: add             x2, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x15a1568: ldr             x2, [x2, #0x360]
    // 0x15a156c: r3 = ""
    //     0x15a156c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a1570: r0 = replaceAll()
    //     0x15a1570: bl              #0x628c30  ; [dart:core] _StringBase::replaceAll
    // 0x15a1574: mov             x1, x0
    // 0x15a1578: r2 = ","
    //     0x15a1578: add             x2, PP, #9, lsl #12  ; [pp+0x93f8] ","
    //     0x15a157c: ldr             x2, [x2, #0x3f8]
    // 0x15a1580: r3 = ""
    //     0x15a1580: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a1584: r0 = replaceAll()
    //     0x15a1584: bl              #0x628c30  ; [dart:core] _StringBase::replaceAll
    // 0x15a1588: mov             x5, x0
    // 0x15a158c: ldur            x0, [fp, #-0x40]
    // 0x15a1590: ldur            x1, [fp, #-0x10]
    // 0x15a1594: stur            x5, [fp, #-0x58]
    // 0x15a1598: r0 = isShowSearch()
    //     0x15a1598: bl              #0x12d4d28  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::isShowSearch
    // 0x15a159c: mov             x1, x0
    // 0x15a15a0: ldur            x0, [fp, #-0x40]
    // 0x15a15a4: ldur            x16, [fp, #-0x48]
    // 0x15a15a8: stp             x16, x0, [SP, #8]
    // 0x15a15ac: ldur            x16, [fp, #-0x20]
    // 0x15a15b0: str             x16, [SP]
    // 0x15a15b4: mov             x6, x1
    // 0x15a15b8: ldur            x1, [fp, #-0x38]
    // 0x15a15bc: ldur            x2, [fp, #-0x28]
    // 0x15a15c0: ldur            x3, [fp, #-0x50]
    // 0x15a15c4: ldur            x5, [fp, #-0x58]
    // 0x15a15c8: ldur            x7, [fp, #-0x30]
    // 0x15a15cc: r0 = performCheckout()
    //     0x15a15cc: bl              #0x15a161c  ; [package:customer_app/app/data/repositories/login/login_repo_impl.dart] LoginRepoImpl::performCheckout
    // 0x15a15d0: ldur            x2, [fp, #-0x18]
    // 0x15a15d4: r1 = Function '<anonymous closure>':.
    //     0x15a15d4: add             x1, PP, #0x22, lsl #12  ; [pp+0x22368] AnonymousClosure: (0x15a1ab0), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::performCheckout (0x15a147c)
    //     0x15a15d8: ldr             x1, [x1, #0x368]
    // 0x15a15dc: stur            x0, [fp, #-0x10]
    // 0x15a15e0: r0 = AllocateClosure()
    //     0x15a15e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a15e4: r16 = <Set<Object>>
    //     0x15a15e4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22370] TypeArguments: <Set<Object>>
    //     0x15a15e8: ldr             x16, [x16, #0x370]
    // 0x15a15ec: ldur            lr, [fp, #-0x10]
    // 0x15a15f0: stp             lr, x16, [SP, #8]
    // 0x15a15f4: str             x0, [SP]
    // 0x15a15f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15a15f8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15a15fc: r0 = then()
    //     0x15a15fc: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15a1600: r0 = Null
    //     0x15a1600: mov             x0, NULL
    // 0x15a1604: r0 = ReturnAsyncNotFuture()
    //     0x15a1604: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a1608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a1608: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a160c: b               #0x15a149c
    // 0x15a1610: r9 = loginRepo
    //     0x15a1610: add             x9, PP, #0x22, lsl #12  ; [pp+0x22378] Field <LoginController.loginRepo>: late (offset: 0x48)
    //     0x15a1614: ldr             x9, [x9, #0x378]
    // 0x15a1618: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x15a1618: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Set<Object> <anonymous closure>(dynamic, CheckoutResponse) {
    // ** addr: 0x15a1ab0, size: 0x150
    // 0x15a1ab0: EnterFrame
    //     0x15a1ab0: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1ab4: mov             fp, SP
    // 0x15a1ab8: AllocStack(0x20)
    //     0x15a1ab8: sub             SP, SP, #0x20
    // 0x15a1abc: SetupParameters()
    //     0x15a1abc: ldr             x0, [fp, #0x18]
    //     0x15a1ac0: ldur            w2, [x0, #0x17]
    //     0x15a1ac4: add             x2, x2, HEAP, lsl #32
    //     0x15a1ac8: stur            x2, [fp, #-8]
    // 0x15a1acc: CheckStackOverflow
    //     0x15a1acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1ad0: cmp             SP, x16
    //     0x15a1ad4: b.ls            #0x15a1bf8
    // 0x15a1ad8: r1 = <Object>
    //     0x15a1ad8: ldr             x1, [PP, #0x768]  ; [pp+0x768] TypeArguments: <Object>
    // 0x15a1adc: r0 = _Set()
    //     0x15a1adc: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x15a1ae0: mov             x3, x0
    // 0x15a1ae4: r0 = _Uint32List
    //     0x15a1ae4: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x15a1ae8: stur            x3, [fp, #-0x10]
    // 0x15a1aec: StoreField: r3->field_1b = r0
    //     0x15a1aec: stur            w0, [x3, #0x1b]
    // 0x15a1af0: StoreField: r3->field_b = rZR
    //     0x15a1af0: stur            wzr, [x3, #0xb]
    // 0x15a1af4: r0 = const []
    //     0x15a1af4: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x15a1af8: StoreField: r3->field_f = r0
    //     0x15a1af8: stur            w0, [x3, #0xf]
    // 0x15a1afc: StoreField: r3->field_13 = rZR
    //     0x15a1afc: stur            wzr, [x3, #0x13]
    // 0x15a1b00: ArrayStore: r3[0] = rZR  ; List_4
    //     0x15a1b00: stur            wzr, [x3, #0x17]
    // 0x15a1b04: ldur            x0, [fp, #-8]
    // 0x15a1b08: LoadField: r1 = r0->field_f
    //     0x15a1b08: ldur            w1, [x0, #0xf]
    // 0x15a1b0c: DecompressPointer r1
    //     0x15a1b0c: add             x1, x1, HEAP, lsl #32
    // 0x15a1b10: ldr             x2, [fp, #0x10]
    // 0x15a1b14: r0 = pinCodeValidateResponse=()
    //     0x15a1b14: bl              #0x1390684  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::pinCodeValidateResponse=
    // 0x15a1b18: ldur            x1, [fp, #-0x10]
    // 0x15a1b1c: ldr             x2, [fp, #0x10]
    // 0x15a1b20: r0 = add()
    //     0x15a1b20: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a1b24: ldur            x0, [fp, #-8]
    // 0x15a1b28: LoadField: r2 = r0->field_f
    //     0x15a1b28: ldur            w2, [x0, #0xf]
    // 0x15a1b2c: DecompressPointer r2
    //     0x15a1b2c: add             x2, x2, HEAP, lsl #32
    // 0x15a1b30: mov             x1, x2
    // 0x15a1b34: stur            x2, [fp, #-0x18]
    // 0x15a1b38: r0 = configData()
    //     0x15a1b38: bl              #0x8a3140  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::configData
    // 0x15a1b3c: LoadField: r1 = r0->field_b
    //     0x15a1b3c: ldur            w1, [x0, #0xb]
    // 0x15a1b40: DecompressPointer r1
    //     0x15a1b40: add             x1, x1, HEAP, lsl #32
    // 0x15a1b44: cmp             w1, NULL
    // 0x15a1b48: b.ne            #0x15a1b54
    // 0x15a1b4c: r0 = Null
    //     0x15a1b4c: mov             x0, NULL
    // 0x15a1b50: b               #0x15a1b78
    // 0x15a1b54: LoadField: r0 = r1->field_33
    //     0x15a1b54: ldur            w0, [x1, #0x33]
    // 0x15a1b58: DecompressPointer r0
    //     0x15a1b58: add             x0, x0, HEAP, lsl #32
    // 0x15a1b5c: cmp             w0, NULL
    // 0x15a1b60: b.ne            #0x15a1b6c
    // 0x15a1b64: r0 = Null
    //     0x15a1b64: mov             x0, NULL
    // 0x15a1b68: b               #0x15a1b78
    // 0x15a1b6c: LoadField: r1 = r0->field_7
    //     0x15a1b6c: ldur            w1, [x0, #7]
    // 0x15a1b70: DecompressPointer r1
    //     0x15a1b70: add             x1, x1, HEAP, lsl #32
    // 0x15a1b74: mov             x0, x1
    // 0x15a1b78: cmp             w0, NULL
    // 0x15a1b7c: b.ne            #0x15a1b88
    // 0x15a1b80: r3 = ""
    //     0x15a1b80: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a1b84: b               #0x15a1b8c
    // 0x15a1b88: mov             x3, x0
    // 0x15a1b8c: ldur            x0, [fp, #-0x18]
    // 0x15a1b90: stur            x3, [fp, #-0x20]
    // 0x15a1b94: LoadField: r4 = r0->field_77
    //     0x15a1b94: ldur            w4, [x0, #0x77]
    // 0x15a1b98: DecompressPointer r4
    //     0x15a1b98: add             x4, x4, HEAP, lsl #32
    // 0x15a1b9c: mov             x0, x3
    // 0x15a1ba0: stur            x4, [fp, #-8]
    // 0x15a1ba4: r2 = Null
    //     0x15a1ba4: mov             x2, NULL
    // 0x15a1ba8: r1 = Null
    //     0x15a1ba8: mov             x1, NULL
    // 0x15a1bac: r4 = LoadClassIdInstr(r0)
    //     0x15a1bac: ldur            x4, [x0, #-1]
    //     0x15a1bb0: ubfx            x4, x4, #0xc, #0x14
    // 0x15a1bb4: sub             x4, x4, #0x5e
    // 0x15a1bb8: cmp             x4, #1
    // 0x15a1bbc: b.ls            #0x15a1bd0
    // 0x15a1bc0: r8 = String
    //     0x15a1bc0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15a1bc4: r3 = Null
    //     0x15a1bc4: add             x3, PP, #0x22, lsl #12  ; [pp+0x22380] Null
    //     0x15a1bc8: ldr             x3, [x3, #0x380]
    // 0x15a1bcc: r0 = String()
    //     0x15a1bcc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15a1bd0: ldur            x1, [fp, #-8]
    // 0x15a1bd4: ldur            x2, [fp, #-0x20]
    // 0x15a1bd8: r0 = value=()
    //     0x15a1bd8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a1bdc: ldur            x1, [fp, #-0x10]
    // 0x15a1be0: ldur            x2, [fp, #-0x20]
    // 0x15a1be4: r0 = add()
    //     0x15a1be4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a1be8: ldur            x0, [fp, #-0x10]
    // 0x15a1bec: LeaveFrame
    //     0x15a1bec: mov             SP, fp
    //     0x15a1bf0: ldp             fp, lr, [SP], #0x10
    // 0x15a1bf4: ret
    //     0x15a1bf4: ret             
    // 0x15a1bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a1bf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1bfc: b               #0x15a1ad8
  }
  _ getBagCount(/* No info */) {
    // ** addr: 0x15a1c00, size: 0x280
    // 0x15a1c00: EnterFrame
    //     0x15a1c00: stp             fp, lr, [SP, #-0x10]!
    //     0x15a1c04: mov             fp, SP
    // 0x15a1c08: AllocStack(0x40)
    //     0x15a1c08: sub             SP, SP, #0x40
    // 0x15a1c0c: SetupParameters(LoginController this /* r1 => r2, fp-0x8 */)
    //     0x15a1c0c: mov             x2, x1
    //     0x15a1c10: stur            x1, [fp, #-8]
    // 0x15a1c14: CheckStackOverflow
    //     0x15a1c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a1c18: cmp             SP, x16
    //     0x15a1c1c: b.ls            #0x15a1e6c
    // 0x15a1c20: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15a1c20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15a1c24: ldr             x0, [x0, #0x1c80]
    //     0x15a1c28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15a1c2c: cmp             w0, w16
    //     0x15a1c30: b.ne            #0x15a1c3c
    //     0x15a1c34: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15a1c38: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15a1c3c: r0 = GetNavigation.arguments()
    //     0x15a1c3c: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x15a1c40: stur            x0, [fp, #-0x10]
    // 0x15a1c44: cmp             w0, NULL
    // 0x15a1c48: b.eq            #0x15a1df0
    // 0x15a1c4c: ldur            x2, [fp, #-8]
    // 0x15a1c50: r16 = "bag_data"
    //     0x15a1c50: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d00] "bag_data"
    //     0x15a1c54: ldr             x16, [x16, #0xd00]
    // 0x15a1c58: stp             x16, x0, [SP]
    // 0x15a1c5c: r4 = 0
    //     0x15a1c5c: movz            x4, #0
    // 0x15a1c60: ldr             x0, [SP, #8]
    // 0x15a1c64: r16 = UnlinkedCall_0x613b5c
    //     0x15a1c64: add             x16, PP, #0x23, lsl #12  ; [pp+0x236a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a1c68: add             x16, x16, #0x6a8
    // 0x15a1c6c: ldp             x5, lr, [x16]
    // 0x15a1c70: blr             lr
    // 0x15a1c74: mov             x3, x0
    // 0x15a1c78: r2 = Null
    //     0x15a1c78: mov             x2, NULL
    // 0x15a1c7c: r1 = Null
    //     0x15a1c7c: mov             x1, NULL
    // 0x15a1c80: stur            x3, [fp, #-0x18]
    // 0x15a1c84: r4 = 60
    //     0x15a1c84: movz            x4, #0x3c
    // 0x15a1c88: branchIfSmi(r0, 0x15a1c94)
    //     0x15a1c88: tbz             w0, #0, #0x15a1c94
    // 0x15a1c8c: r4 = LoadClassIdInstr(r0)
    //     0x15a1c8c: ldur            x4, [x0, #-1]
    //     0x15a1c90: ubfx            x4, x4, #0xc, #0x14
    // 0x15a1c94: r17 = 5419
    //     0x15a1c94: movz            x17, #0x152b
    // 0x15a1c98: cmp             x4, x17
    // 0x15a1c9c: b.eq            #0x15a1cb4
    // 0x15a1ca0: r8 = BagResponse?
    //     0x15a1ca0: add             x8, PP, #0x11, lsl #12  ; [pp+0x11d18] Type: BagResponse?
    //     0x15a1ca4: ldr             x8, [x8, #0xd18]
    // 0x15a1ca8: r3 = Null
    //     0x15a1ca8: add             x3, PP, #0x23, lsl #12  ; [pp+0x236b8] Null
    //     0x15a1cac: ldr             x3, [x3, #0x6b8]
    // 0x15a1cb0: r0 = DefaultNullableTypeTest()
    //     0x15a1cb0: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x15a1cb4: ldur            x0, [fp, #-0x18]
    // 0x15a1cb8: ldur            x2, [fp, #-8]
    // 0x15a1cbc: StoreField: r2->field_4b = r0
    //     0x15a1cbc: stur            w0, [x2, #0x4b]
    //     0x15a1cc0: ldurb           w16, [x2, #-1]
    //     0x15a1cc4: ldurb           w17, [x0, #-1]
    //     0x15a1cc8: and             x16, x17, x16, lsr #2
    //     0x15a1ccc: tst             x16, HEAP, lsr #32
    //     0x15a1cd0: b.eq            #0x15a1cd8
    //     0x15a1cd4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a1cd8: ldur            x16, [fp, #-0x10]
    // 0x15a1cdc: r30 = "previousScreenSource"
    //     0x15a1cdc: add             lr, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15a1ce0: ldr             lr, [lr, #0x448]
    // 0x15a1ce4: stp             lr, x16, [SP]
    // 0x15a1ce8: r4 = 0
    //     0x15a1ce8: movz            x4, #0
    // 0x15a1cec: ldr             x0, [SP, #8]
    // 0x15a1cf0: r16 = UnlinkedCall_0x613b5c
    //     0x15a1cf0: add             x16, PP, #0x23, lsl #12  ; [pp+0x236c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a1cf4: add             x16, x16, #0x6c8
    // 0x15a1cf8: ldp             x5, lr, [x16]
    // 0x15a1cfc: blr             lr
    // 0x15a1d00: mov             x3, x0
    // 0x15a1d04: r2 = Null
    //     0x15a1d04: mov             x2, NULL
    // 0x15a1d08: r1 = Null
    //     0x15a1d08: mov             x1, NULL
    // 0x15a1d0c: stur            x3, [fp, #-0x18]
    // 0x15a1d10: r4 = 60
    //     0x15a1d10: movz            x4, #0x3c
    // 0x15a1d14: branchIfSmi(r0, 0x15a1d20)
    //     0x15a1d14: tbz             w0, #0, #0x15a1d20
    // 0x15a1d18: r4 = LoadClassIdInstr(r0)
    //     0x15a1d18: ldur            x4, [x0, #-1]
    //     0x15a1d1c: ubfx            x4, x4, #0xc, #0x14
    // 0x15a1d20: sub             x4, x4, #0x5e
    // 0x15a1d24: cmp             x4, #1
    // 0x15a1d28: b.ls            #0x15a1d3c
    // 0x15a1d2c: r8 = String?
    //     0x15a1d2c: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a1d30: r3 = Null
    //     0x15a1d30: add             x3, PP, #0x23, lsl #12  ; [pp+0x236d8] Null
    //     0x15a1d34: ldr             x3, [x3, #0x6d8]
    // 0x15a1d38: r0 = String?()
    //     0x15a1d38: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a1d3c: ldur            x0, [fp, #-0x18]
    // 0x15a1d40: ldur            x2, [fp, #-8]
    // 0x15a1d44: StoreField: r2->field_53 = r0
    //     0x15a1d44: stur            w0, [x2, #0x53]
    //     0x15a1d48: ldurb           w16, [x2, #-1]
    //     0x15a1d4c: ldurb           w17, [x0, #-1]
    //     0x15a1d50: and             x16, x17, x16, lsr #2
    //     0x15a1d54: tst             x16, HEAP, lsr #32
    //     0x15a1d58: b.eq            #0x15a1d60
    //     0x15a1d5c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a1d60: ldur            x16, [fp, #-0x10]
    // 0x15a1d64: r30 = "checkout_event_data"
    //     0x15a1d64: add             lr, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0x15a1d68: ldr             lr, [lr, #0xd50]
    // 0x15a1d6c: stp             lr, x16, [SP]
    // 0x15a1d70: r4 = 0
    //     0x15a1d70: movz            x4, #0
    // 0x15a1d74: ldr             x0, [SP, #8]
    // 0x15a1d78: r16 = UnlinkedCall_0x613b5c
    //     0x15a1d78: add             x16, PP, #0x23, lsl #12  ; [pp+0x236e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a1d7c: add             x16, x16, #0x6e8
    // 0x15a1d80: ldp             x5, lr, [x16]
    // 0x15a1d84: blr             lr
    // 0x15a1d88: mov             x3, x0
    // 0x15a1d8c: r2 = Null
    //     0x15a1d8c: mov             x2, NULL
    // 0x15a1d90: r1 = Null
    //     0x15a1d90: mov             x1, NULL
    // 0x15a1d94: stur            x3, [fp, #-0x10]
    // 0x15a1d98: r4 = 60
    //     0x15a1d98: movz            x4, #0x3c
    // 0x15a1d9c: branchIfSmi(r0, 0x15a1da8)
    //     0x15a1d9c: tbz             w0, #0, #0x15a1da8
    // 0x15a1da0: r4 = LoadClassIdInstr(r0)
    //     0x15a1da0: ldur            x4, [x0, #-1]
    //     0x15a1da4: ubfx            x4, x4, #0xc, #0x14
    // 0x15a1da8: r17 = 5260
    //     0x15a1da8: movz            x17, #0x148c
    // 0x15a1dac: cmp             x4, x17
    // 0x15a1db0: b.eq            #0x15a1dc8
    // 0x15a1db4: r8 = CheckoutEventData?
    //     0x15a1db4: add             x8, PP, #0x11, lsl #12  ; [pp+0x11d68] Type: CheckoutEventData?
    //     0x15a1db8: ldr             x8, [x8, #0xd68]
    // 0x15a1dbc: r3 = Null
    //     0x15a1dbc: add             x3, PP, #0x23, lsl #12  ; [pp+0x236f8] Null
    //     0x15a1dc0: ldr             x3, [x3, #0x6f8]
    // 0x15a1dc4: r0 = DefaultNullableTypeTest()
    //     0x15a1dc4: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x15a1dc8: ldur            x0, [fp, #-0x10]
    // 0x15a1dcc: ldur            x2, [fp, #-8]
    // 0x15a1dd0: StoreField: r2->field_4f = r0
    //     0x15a1dd0: stur            w0, [x2, #0x4f]
    //     0x15a1dd4: ldurb           w16, [x2, #-1]
    //     0x15a1dd8: ldurb           w17, [x0, #-1]
    //     0x15a1ddc: and             x16, x17, x16, lsr #2
    //     0x15a1de0: tst             x16, HEAP, lsr #32
    //     0x15a1de4: b.eq            #0x15a1dec
    //     0x15a1de8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a1dec: b               #0x15a1df4
    // 0x15a1df0: ldur            x2, [fp, #-8]
    // 0x15a1df4: LoadField: r1 = r2->field_47
    //     0x15a1df4: ldur            w1, [x2, #0x47]
    // 0x15a1df8: DecompressPointer r1
    //     0x15a1df8: add             x1, x1, HEAP, lsl #32
    // 0x15a1dfc: r16 = Sentinel
    //     0x15a1dfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x15a1e00: cmp             w1, w16
    // 0x15a1e04: b.eq            #0x15a1e74
    // 0x15a1e08: r0 = getBagCount()
    //     0x15a1e08: bl              #0x15a1e80  ; [package:customer_app/app/data/repositories/login/login_repo_impl.dart] LoginRepoImpl::getBagCount
    // 0x15a1e0c: ldur            x2, [fp, #-8]
    // 0x15a1e10: r1 = Function '_handleBagCountResponse@1161085715':.
    //     0x15a1e10: add             x1, PP, #0x23, lsl #12  ; [pp+0x23708] AnonymousClosure: (0x15a209c), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleBagCountResponse (0x15a20d8)
    //     0x15a1e14: ldr             x1, [x1, #0x708]
    // 0x15a1e18: stur            x0, [fp, #-0x10]
    // 0x15a1e1c: r0 = AllocateClosure()
    //     0x15a1e1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a1e20: ldur            x2, [fp, #-8]
    // 0x15a1e24: r1 = Function '_handleError@1161085715':.
    //     0x15a1e24: add             x1, PP, #0x23, lsl #12  ; [pp+0x23710] AnonymousClosure: (0x131e244), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleError (0x131e280)
    //     0x15a1e28: ldr             x1, [x1, #0x710]
    // 0x15a1e2c: stur            x0, [fp, #-0x18]
    // 0x15a1e30: r0 = AllocateClosure()
    //     0x15a1e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a1e34: r16 = <BagCountResponse>
    //     0x15a1e34: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf58] TypeArguments: <BagCountResponse>
    //     0x15a1e38: ldr             x16, [x16, #0xf58]
    // 0x15a1e3c: ldur            lr, [fp, #-8]
    // 0x15a1e40: stp             lr, x16, [SP, #0x18]
    // 0x15a1e44: ldur            x16, [fp, #-0x10]
    // 0x15a1e48: stp             x0, x16, [SP, #8]
    // 0x15a1e4c: ldur            x16, [fp, #-0x18]
    // 0x15a1e50: str             x16, [SP]
    // 0x15a1e54: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x15a1e54: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x15a1e58: r0 = callDataService()
    //     0x15a1e58: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x15a1e5c: r0 = Null
    //     0x15a1e5c: mov             x0, NULL
    // 0x15a1e60: LeaveFrame
    //     0x15a1e60: mov             SP, fp
    //     0x15a1e64: ldp             fp, lr, [SP], #0x10
    // 0x15a1e68: ret
    //     0x15a1e68: ret             
    // 0x15a1e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a1e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a1e70: b               #0x15a1c20
    // 0x15a1e74: r9 = loginRepo
    //     0x15a1e74: add             x9, PP, #0x22, lsl #12  ; [pp+0x22378] Field <LoginController.loginRepo>: late (offset: 0x48)
    //     0x15a1e78: ldr             x9, [x9, #0x378]
    // 0x15a1e7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x15a1e7c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleBagCountResponse(dynamic, BagCountResponse) {
    // ** addr: 0x15a209c, size: 0x3c
    // 0x15a209c: EnterFrame
    //     0x15a209c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a20a0: mov             fp, SP
    // 0x15a20a4: ldr             x0, [fp, #0x18]
    // 0x15a20a8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15a20a8: ldur            w1, [x0, #0x17]
    // 0x15a20ac: DecompressPointer r1
    //     0x15a20ac: add             x1, x1, HEAP, lsl #32
    // 0x15a20b0: CheckStackOverflow
    //     0x15a20b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a20b4: cmp             SP, x16
    //     0x15a20b8: b.ls            #0x15a20d0
    // 0x15a20bc: ldr             x2, [fp, #0x10]
    // 0x15a20c0: r0 = _handleBagCountResponse()
    //     0x15a20c0: bl              #0x15a20d8  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::_handleBagCountResponse
    // 0x15a20c4: LeaveFrame
    //     0x15a20c4: mov             SP, fp
    //     0x15a20c8: ldp             fp, lr, [SP], #0x10
    // 0x15a20cc: ret
    //     0x15a20cc: ret             
    // 0x15a20d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a20d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a20d4: b               #0x15a20bc
  }
  _ _handleBagCountResponse(/* No info */) {
    // ** addr: 0x15a20d8, size: 0x70
    // 0x15a20d8: EnterFrame
    //     0x15a20d8: stp             fp, lr, [SP, #-0x10]!
    //     0x15a20dc: mov             fp, SP
    // 0x15a20e0: CheckStackOverflow
    //     0x15a20e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a20e4: cmp             SP, x16
    //     0x15a20e8: b.ls            #0x15a2140
    // 0x15a20ec: LoadField: r0 = r2->field_b
    //     0x15a20ec: ldur            w0, [x2, #0xb]
    // 0x15a20f0: DecompressPointer r0
    //     0x15a20f0: add             x0, x0, HEAP, lsl #32
    // 0x15a20f4: cmp             w0, NULL
    // 0x15a20f8: b.ne            #0x15a2104
    // 0x15a20fc: r0 = Null
    //     0x15a20fc: mov             x0, NULL
    // 0x15a2100: b               #0x15a2110
    // 0x15a2104: LoadField: r2 = r0->field_7
    //     0x15a2104: ldur            w2, [x0, #7]
    // 0x15a2108: DecompressPointer r2
    //     0x15a2108: add             x2, x2, HEAP, lsl #32
    // 0x15a210c: mov             x0, x2
    // 0x15a2110: cmp             w0, NULL
    // 0x15a2114: b.ne            #0x15a2120
    // 0x15a2118: r2 = 0
    //     0x15a2118: movz            x2, #0
    // 0x15a211c: b               #0x15a212c
    // 0x15a2120: r2 = LoadInt32Instr(r0)
    //     0x15a2120: sbfx            x2, x0, #1, #0x1f
    //     0x15a2124: tbz             w0, #0, #0x15a212c
    //     0x15a2128: ldur            x2, [x0, #7]
    // 0x15a212c: r0 = setBadgeCount()
    //     0x15a212c: bl              #0x15a2148  ; [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::setBadgeCount
    // 0x15a2130: r0 = Null
    //     0x15a2130: mov             x0, NULL
    // 0x15a2134: LeaveFrame
    //     0x15a2134: mov             SP, fp
    //     0x15a2138: ldp             fp, lr, [SP], #0x10
    // 0x15a213c: ret
    //     0x15a213c: ret             
    // 0x15a2140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a2140: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a2144: b               #0x15a20ec
  }
  _ setBadgeCount(/* No info */) {
    // ** addr: 0x15a2148, size: 0x54
    // 0x15a2148: EnterFrame
    //     0x15a2148: stp             fp, lr, [SP, #-0x10]!
    //     0x15a214c: mov             fp, SP
    // 0x15a2150: CheckStackOverflow
    //     0x15a2150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a2154: cmp             SP, x16
    //     0x15a2158: b.ls            #0x15a2194
    // 0x15a215c: LoadField: r3 = r1->field_c7
    //     0x15a215c: ldur            w3, [x1, #0xc7]
    // 0x15a2160: DecompressPointer r3
    //     0x15a2160: add             x3, x3, HEAP, lsl #32
    // 0x15a2164: r0 = BoxInt64Instr(r2)
    //     0x15a2164: sbfiz           x0, x2, #1, #0x1f
    //     0x15a2168: cmp             x2, x0, asr #1
    //     0x15a216c: b.eq            #0x15a2178
    //     0x15a2170: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x15a2174: stur            x2, [x0, #7]
    // 0x15a2178: mov             x1, x3
    // 0x15a217c: mov             x2, x0
    // 0x15a2180: r0 = value=()
    //     0x15a2180: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a2184: r0 = Null
    //     0x15a2184: mov             x0, NULL
    // 0x15a2188: LeaveFrame
    //     0x15a2188: mov             SP, fp
    //     0x15a218c: ldp             fp, lr, [SP], #0x10
    // 0x15a2190: ret
    //     0x15a2190: ret             
    // 0x15a2194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a2194: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a2198: b               #0x15a215c
  }
  [closure] Set<LocalConfigData> <anonymous closure>(dynamic, LocalConfigData) {
    // ** addr: 0x15a219c, size: 0x9c
    // 0x15a219c: EnterFrame
    //     0x15a219c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a21a0: mov             fp, SP
    // 0x15a21a4: AllocStack(0x10)
    //     0x15a21a4: sub             SP, SP, #0x10
    // 0x15a21a8: SetupParameters()
    //     0x15a21a8: ldr             x0, [fp, #0x18]
    //     0x15a21ac: ldur            w2, [x0, #0x17]
    //     0x15a21b0: add             x2, x2, HEAP, lsl #32
    //     0x15a21b4: stur            x2, [fp, #-8]
    // 0x15a21b8: CheckStackOverflow
    //     0x15a21b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a21bc: cmp             SP, x16
    //     0x15a21c0: b.ls            #0x15a2230
    // 0x15a21c4: r1 = <LocalConfigData>
    //     0x15a21c4: add             x1, PP, #0xa, lsl #12  ; [pp+0xab30] TypeArguments: <LocalConfigData>
    //     0x15a21c8: ldr             x1, [x1, #0xb30]
    // 0x15a21cc: r0 = _Set()
    //     0x15a21cc: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x15a21d0: mov             x3, x0
    // 0x15a21d4: r0 = _Uint32List
    //     0x15a21d4: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x15a21d8: stur            x3, [fp, #-0x10]
    // 0x15a21dc: StoreField: r3->field_1b = r0
    //     0x15a21dc: stur            w0, [x3, #0x1b]
    // 0x15a21e0: StoreField: r3->field_b = rZR
    //     0x15a21e0: stur            wzr, [x3, #0xb]
    // 0x15a21e4: r0 = const []
    //     0x15a21e4: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x15a21e8: StoreField: r3->field_f = r0
    //     0x15a21e8: stur            w0, [x3, #0xf]
    // 0x15a21ec: StoreField: r3->field_13 = rZR
    //     0x15a21ec: stur            wzr, [x3, #0x13]
    // 0x15a21f0: ArrayStore: r3[0] = rZR  ; List_4
    //     0x15a21f0: stur            wzr, [x3, #0x17]
    // 0x15a21f4: ldur            x0, [fp, #-8]
    // 0x15a21f8: LoadField: r1 = r0->field_f
    //     0x15a21f8: ldur            w1, [x0, #0xf]
    // 0x15a21fc: DecompressPointer r1
    //     0x15a21fc: add             x1, x1, HEAP, lsl #32
    // 0x15a2200: LoadField: r0 = r1->field_93
    //     0x15a2200: ldur            w0, [x1, #0x93]
    // 0x15a2204: DecompressPointer r0
    //     0x15a2204: add             x0, x0, HEAP, lsl #32
    // 0x15a2208: mov             x1, x0
    // 0x15a220c: ldr             x2, [fp, #0x10]
    // 0x15a2210: r0 = value=()
    //     0x15a2210: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a2214: ldur            x1, [fp, #-0x10]
    // 0x15a2218: ldr             x2, [fp, #0x10]
    // 0x15a221c: r0 = add()
    //     0x15a221c: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x15a2220: ldur            x0, [fp, #-0x10]
    // 0x15a2224: LeaveFrame
    //     0x15a2224: mov             SP, fp
    //     0x15a2228: ldp             fp, lr, [SP], #0x10
    // 0x15a222c: ret
    //     0x15a222c: ret             
    // 0x15a2230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a2230: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a2234: b               #0x15a21c4
  }
  _ LoginController(/* No info */) {
    // ** addr: 0x1601494, size: 0x6c4
    // 0x1601494: EnterFrame
    //     0x1601494: stp             fp, lr, [SP, #-0x10]!
    //     0x1601498: mov             fp, SP
    // 0x160149c: AllocStack(0x28)
    //     0x160149c: sub             SP, SP, #0x28
    // 0x16014a0: r3 = Sentinel
    //     0x16014a0: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x16014a4: r2 = ""
    //     0x16014a4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x16014a8: r0 = false
    //     0x16014a8: add             x0, NULL, #0x30  ; false
    // 0x16014ac: stur            x1, [fp, #-8]
    // 0x16014b0: CheckStackOverflow
    //     0x16014b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x16014b4: cmp             SP, x16
    //     0x16014b8: b.ls            #0x1601b50
    // 0x16014bc: StoreField: r1->field_47 = r3
    //     0x16014bc: stur            w3, [x1, #0x47]
    // 0x16014c0: StoreField: r1->field_9b = r2
    //     0x16014c0: stur            w2, [x1, #0x9b]
    // 0x16014c4: StoreField: r1->field_9f = r2
    //     0x16014c4: stur            w2, [x1, #0x9f]
    // 0x16014c8: StoreField: r1->field_a3 = r2
    //     0x16014c8: stur            w2, [x1, #0xa3]
    // 0x16014cc: StoreField: r1->field_ab = r0
    //     0x16014cc: stur            w0, [x1, #0xab]
    // 0x16014d0: r0 = FocusNode()
    //     0x16014d0: bl              #0x8182fc  ; AllocateFocusNodeStub -> FocusNode (size=0x68)
    // 0x16014d4: mov             x1, x0
    // 0x16014d8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x16014d8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x16014dc: r0 = FocusNode()
    //     0x16014dc: bl              #0x695c10  ; [package:flutter/src/widgets/focus_manager.dart] FocusNode::FocusNode
    // 0x16014e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x16014e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x16014e4: ldr             x0, [x0, #0x1c80]
    //     0x16014e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x16014ec: cmp             w0, w16
    //     0x16014f0: b.ne            #0x16014fc
    //     0x16014f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x16014f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x16014fc: r16 = PreferenceManager
    //     0x16014fc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1601500: ldr             x16, [x16, #0x878]
    // 0x1601504: str             x16, [SP]
    // 0x1601508: r0 = toString()
    //     0x1601508: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x160150c: r16 = <PreferenceManager>
    //     0x160150c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x1601510: ldr             x16, [x16, #0x880]
    // 0x1601514: stp             x0, x16, [SP]
    // 0x1601518: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1601518: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x160151c: r0 = Inst.find()
    //     0x160151c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1601520: ldur            x1, [fp, #-8]
    // 0x1601524: StoreField: r1->field_63 = r0
    //     0x1601524: stur            w0, [x1, #0x63]
    //     0x1601528: ldurb           w16, [x1, #-1]
    //     0x160152c: ldurb           w17, [x0, #-1]
    //     0x1601530: and             x16, x17, x16, lsr #2
    //     0x1601534: tst             x16, HEAP, lsr #32
    //     0x1601538: b.eq            #0x1601540
    //     0x160153c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1601540: r0 = GeneralResponse()
    //     0x1601540: bl              #0x8b2184  ; AllocateGeneralResponseStub -> GeneralResponse (size=0x14)
    // 0x1601544: r16 = <GeneralResponse>
    //     0x1601544: add             x16, PP, #0xa, lsl #12  ; [pp+0xac10] TypeArguments: <GeneralResponse>
    //     0x1601548: ldr             x16, [x16, #0xc10]
    // 0x160154c: stp             x0, x16, [SP]
    // 0x1601550: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1601550: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1601554: r0 = RxT.obs()
    //     0x1601554: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1601558: ldur            x2, [fp, #-8]
    // 0x160155c: StoreField: r2->field_67 = r0
    //     0x160155c: stur            w0, [x2, #0x67]
    //     0x1601560: ldurb           w16, [x2, #-1]
    //     0x1601564: ldurb           w17, [x0, #-1]
    //     0x1601568: and             x16, x17, x16, lsr #2
    //     0x160156c: tst             x16, HEAP, lsr #32
    //     0x1601570: b.eq            #0x1601578
    //     0x1601574: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601578: r1 = false
    //     0x1601578: add             x1, NULL, #0x30  ; false
    // 0x160157c: r0 = BoolExtension.obs()
    //     0x160157c: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1601580: ldur            x1, [fp, #-8]
    // 0x1601584: StoreField: r1->field_6b = r0
    //     0x1601584: stur            w0, [x1, #0x6b]
    //     0x1601588: ldurb           w16, [x1, #-1]
    //     0x160158c: ldurb           w17, [x0, #-1]
    //     0x1601590: and             x16, x17, x16, lsr #2
    //     0x1601594: tst             x16, HEAP, lsr #32
    //     0x1601598: b.eq            #0x16015a0
    //     0x160159c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x16015a0: r0 = VerifyOtpResponse()
    //     0x16015a0: bl              #0x918568  ; AllocateVerifyOtpResponseStub -> VerifyOtpResponse (size=0x1c)
    // 0x16015a4: r16 = <VerifyOtpResponse>
    //     0x16015a4: add             x16, PP, #0xa, lsl #12  ; [pp+0xac18] TypeArguments: <VerifyOtpResponse>
    //     0x16015a8: ldr             x16, [x16, #0xc18]
    // 0x16015ac: stp             x0, x16, [SP]
    // 0x16015b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x16015b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x16015b4: r0 = RxT.obs()
    //     0x16015b4: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x16015b8: ldur            x1, [fp, #-8]
    // 0x16015bc: StoreField: r1->field_6f = r0
    //     0x16015bc: stur            w0, [x1, #0x6f]
    //     0x16015c0: ldurb           w16, [x1, #-1]
    //     0x16015c4: ldurb           w17, [x0, #-1]
    //     0x16015c8: and             x16, x17, x16, lsr #2
    //     0x16015cc: tst             x16, HEAP, lsr #32
    //     0x16015d0: b.eq            #0x16015d8
    //     0x16015d4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x16015d8: r0 = CheckoutResponse()
    //     0x16015d8: bl              #0xa15674  ; AllocateCheckoutResponseStub -> CheckoutResponse (size=0x14)
    // 0x16015dc: r16 = <CheckoutResponse>
    //     0x16015dc: add             x16, PP, #0xa, lsl #12  ; [pp+0xac20] TypeArguments: <CheckoutResponse>
    //     0x16015e0: ldr             x16, [x16, #0xc20]
    // 0x16015e4: stp             x0, x16, [SP]
    // 0x16015e8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x16015e8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x16015ec: r0 = RxT.obs()
    //     0x16015ec: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x16015f0: ldur            x1, [fp, #-8]
    // 0x16015f4: StoreField: r1->field_73 = r0
    //     0x16015f4: stur            w0, [x1, #0x73]
    //     0x16015f8: ldurb           w16, [x1, #-1]
    //     0x16015fc: ldurb           w17, [x0, #-1]
    //     0x1601600: and             x16, x17, x16, lsr #2
    //     0x1601604: tst             x16, HEAP, lsr #32
    //     0x1601608: b.eq            #0x1601610
    //     0x160160c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1601610: r0 = StringExtension.obs()
    //     0x1601610: bl              #0x12c6058  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::StringExtension.obs
    // 0x1601614: ldur            x1, [fp, #-8]
    // 0x1601618: StoreField: r1->field_77 = r0
    //     0x1601618: stur            w0, [x1, #0x77]
    //     0x160161c: ldurb           w16, [x1, #-1]
    //     0x1601620: ldurb           w17, [x0, #-1]
    //     0x1601624: and             x16, x17, x16, lsr #2
    //     0x1601628: tst             x16, HEAP, lsr #32
    //     0x160162c: b.eq            #0x1601634
    //     0x1601630: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1601634: r0 = LocalConfigData()
    //     0x1601634: bl              #0x8933d8  ; AllocateLocalConfigDataStub -> LocalConfigData (size=0x80)
    // 0x1601638: r16 = <LocalConfigData>
    //     0x1601638: add             x16, PP, #0xa, lsl #12  ; [pp+0xab30] TypeArguments: <LocalConfigData>
    //     0x160163c: ldr             x16, [x16, #0xb30]
    // 0x1601640: stp             x0, x16, [SP]
    // 0x1601644: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1601644: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1601648: r0 = RxT.obs()
    //     0x1601648: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x160164c: ldur            x2, [fp, #-8]
    // 0x1601650: StoreField: r2->field_7b = r0
    //     0x1601650: stur            w0, [x2, #0x7b]
    //     0x1601654: ldurb           w16, [x2, #-1]
    //     0x1601658: ldurb           w17, [x0, #-1]
    //     0x160165c: and             x16, x17, x16, lsr #2
    //     0x1601660: tst             x16, HEAP, lsr #32
    //     0x1601664: b.eq            #0x160166c
    //     0x1601668: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x160166c: r1 = <String?, String>
    //     0x160166c: add             x1, PP, #0xa, lsl #12  ; [pp+0xac28] TypeArguments: <String?, String>
    //     0x1601670: ldr             x1, [x1, #0xc28]
    // 0x1601674: r0 = Rxn()
    //     0x1601674: bl              #0x12c3fbc  ; AllocateRxnStub -> Rxn<C1X0> (size=0x1c)
    // 0x1601678: mov             x2, x0
    // 0x160167c: r0 = Sentinel
    //     0x160167c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1601680: stur            x2, [fp, #-0x10]
    // 0x1601684: StoreField: r2->field_13 = r0
    //     0x1601684: stur            w0, [x2, #0x13]
    // 0x1601688: r3 = true
    //     0x1601688: add             x3, NULL, #0x20  ; true
    // 0x160168c: ArrayStore: r2[0] = r3  ; List_4
    //     0x160168c: stur            w3, [x2, #0x17]
    // 0x1601690: mov             x1, x2
    // 0x1601694: r0 = RxNotifier()
    //     0x1601694: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1601698: ldur            x0, [fp, #-0x10]
    // 0x160169c: r1 = ""
    //     0x160169c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x16016a0: StoreField: r0->field_13 = r1
    //     0x16016a0: stur            w1, [x0, #0x13]
    // 0x16016a4: ldur            x2, [fp, #-8]
    // 0x16016a8: StoreField: r2->field_7f = r0
    //     0x16016a8: stur            w0, [x2, #0x7f]
    //     0x16016ac: ldurb           w16, [x2, #-1]
    //     0x16016b0: ldurb           w17, [x0, #-1]
    //     0x16016b4: and             x16, x17, x16, lsr #2
    //     0x16016b8: tst             x16, HEAP, lsr #32
    //     0x16016bc: b.eq            #0x16016c4
    //     0x16016c0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x16016c4: r1 = <bool?>
    //     0x16016c4: ldr             x1, [PP, #0x7f38]  ; [pp+0x7f38] TypeArguments: <bool?>
    // 0x16016c8: r0 = RxnBool()
    //     0x16016c8: bl              #0x12c3ca8  ; AllocateRxnBoolStub -> RxnBool (size=0x1c)
    // 0x16016cc: mov             x2, x0
    // 0x16016d0: r0 = Sentinel
    //     0x16016d0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x16016d4: stur            x2, [fp, #-0x10]
    // 0x16016d8: StoreField: r2->field_13 = r0
    //     0x16016d8: stur            w0, [x2, #0x13]
    // 0x16016dc: r3 = true
    //     0x16016dc: add             x3, NULL, #0x20  ; true
    // 0x16016e0: ArrayStore: r2[0] = r3  ; List_4
    //     0x16016e0: stur            w3, [x2, #0x17]
    // 0x16016e4: mov             x1, x2
    // 0x16016e8: r0 = RxNotifier()
    //     0x16016e8: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x16016ec: ldur            x0, [fp, #-0x10]
    // 0x16016f0: r2 = false
    //     0x16016f0: add             x2, NULL, #0x30  ; false
    // 0x16016f4: StoreField: r0->field_13 = r2
    //     0x16016f4: stur            w2, [x0, #0x13]
    // 0x16016f8: ldur            x3, [fp, #-8]
    // 0x16016fc: StoreField: r3->field_83 = r0
    //     0x16016fc: stur            w0, [x3, #0x83]
    //     0x1601700: ldurb           w16, [x3, #-1]
    //     0x1601704: ldurb           w17, [x0, #-1]
    //     0x1601708: and             x16, x17, x16, lsr #2
    //     0x160170c: tst             x16, HEAP, lsr #32
    //     0x1601710: b.eq            #0x1601718
    //     0x1601714: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x1601718: r1 = <bool?>
    //     0x1601718: ldr             x1, [PP, #0x7f38]  ; [pp+0x7f38] TypeArguments: <bool?>
    // 0x160171c: r0 = RxnBool()
    //     0x160171c: bl              #0x12c3ca8  ; AllocateRxnBoolStub -> RxnBool (size=0x1c)
    // 0x1601720: mov             x2, x0
    // 0x1601724: r0 = Sentinel
    //     0x1601724: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1601728: stur            x2, [fp, #-0x10]
    // 0x160172c: StoreField: r2->field_13 = r0
    //     0x160172c: stur            w0, [x2, #0x13]
    // 0x1601730: r3 = true
    //     0x1601730: add             x3, NULL, #0x20  ; true
    // 0x1601734: ArrayStore: r2[0] = r3  ; List_4
    //     0x1601734: stur            w3, [x2, #0x17]
    // 0x1601738: mov             x1, x2
    // 0x160173c: r0 = RxNotifier()
    //     0x160173c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1601740: ldur            x0, [fp, #-0x10]
    // 0x1601744: r2 = false
    //     0x1601744: add             x2, NULL, #0x30  ; false
    // 0x1601748: StoreField: r0->field_13 = r2
    //     0x1601748: stur            w2, [x0, #0x13]
    // 0x160174c: ldur            x3, [fp, #-8]
    // 0x1601750: StoreField: r3->field_87 = r0
    //     0x1601750: stur            w0, [x3, #0x87]
    //     0x1601754: ldurb           w16, [x3, #-1]
    //     0x1601758: ldurb           w17, [x0, #-1]
    //     0x160175c: and             x16, x17, x16, lsr #2
    //     0x1601760: tst             x16, HEAP, lsr #32
    //     0x1601764: b.eq            #0x160176c
    //     0x1601768: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x160176c: r1 = <bool?>
    //     0x160176c: ldr             x1, [PP, #0x7f38]  ; [pp+0x7f38] TypeArguments: <bool?>
    // 0x1601770: r0 = RxnBool()
    //     0x1601770: bl              #0x12c3ca8  ; AllocateRxnBoolStub -> RxnBool (size=0x1c)
    // 0x1601774: mov             x2, x0
    // 0x1601778: r0 = Sentinel
    //     0x1601778: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x160177c: stur            x2, [fp, #-0x10]
    // 0x1601780: StoreField: r2->field_13 = r0
    //     0x1601780: stur            w0, [x2, #0x13]
    // 0x1601784: r3 = true
    //     0x1601784: add             x3, NULL, #0x20  ; true
    // 0x1601788: ArrayStore: r2[0] = r3  ; List_4
    //     0x1601788: stur            w3, [x2, #0x17]
    // 0x160178c: mov             x1, x2
    // 0x1601790: r0 = RxNotifier()
    //     0x1601790: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1601794: ldur            x0, [fp, #-0x10]
    // 0x1601798: r2 = false
    //     0x1601798: add             x2, NULL, #0x30  ; false
    // 0x160179c: StoreField: r0->field_13 = r2
    //     0x160179c: stur            w2, [x0, #0x13]
    // 0x16017a0: ldur            x3, [fp, #-8]
    // 0x16017a4: StoreField: r3->field_8b = r0
    //     0x16017a4: stur            w0, [x3, #0x8b]
    //     0x16017a8: ldurb           w16, [x3, #-1]
    //     0x16017ac: ldurb           w17, [x0, #-1]
    //     0x16017b0: and             x16, x17, x16, lsr #2
    //     0x16017b4: tst             x16, HEAP, lsr #32
    //     0x16017b8: b.eq            #0x16017c0
    //     0x16017bc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x16017c0: r1 = <String?, String>
    //     0x16017c0: add             x1, PP, #0xa, lsl #12  ; [pp+0xac28] TypeArguments: <String?, String>
    //     0x16017c4: ldr             x1, [x1, #0xc28]
    // 0x16017c8: r0 = Rxn()
    //     0x16017c8: bl              #0x12c3fbc  ; AllocateRxnStub -> Rxn<C1X0> (size=0x1c)
    // 0x16017cc: mov             x2, x0
    // 0x16017d0: r0 = Sentinel
    //     0x16017d0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x16017d4: stur            x2, [fp, #-0x10]
    // 0x16017d8: StoreField: r2->field_13 = r0
    //     0x16017d8: stur            w0, [x2, #0x13]
    // 0x16017dc: r3 = true
    //     0x16017dc: add             x3, NULL, #0x20  ; true
    // 0x16017e0: ArrayStore: r2[0] = r3  ; List_4
    //     0x16017e0: stur            w3, [x2, #0x17]
    // 0x16017e4: mov             x1, x2
    // 0x16017e8: r0 = RxNotifier()
    //     0x16017e8: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x16017ec: ldur            x0, [fp, #-0x10]
    // 0x16017f0: StoreField: r0->field_13 = rNULL
    //     0x16017f0: stur            NULL, [x0, #0x13]
    // 0x16017f4: r1 = <String?, String>
    //     0x16017f4: add             x1, PP, #0xa, lsl #12  ; [pp+0xac28] TypeArguments: <String?, String>
    //     0x16017f8: ldr             x1, [x1, #0xc28]
    // 0x16017fc: r0 = Rxn()
    //     0x16017fc: bl              #0x12c3fbc  ; AllocateRxnStub -> Rxn<C1X0> (size=0x1c)
    // 0x1601800: mov             x2, x0
    // 0x1601804: r0 = Sentinel
    //     0x1601804: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1601808: stur            x2, [fp, #-0x10]
    // 0x160180c: StoreField: r2->field_13 = r0
    //     0x160180c: stur            w0, [x2, #0x13]
    // 0x1601810: r3 = true
    //     0x1601810: add             x3, NULL, #0x20  ; true
    // 0x1601814: ArrayStore: r2[0] = r3  ; List_4
    //     0x1601814: stur            w3, [x2, #0x17]
    // 0x1601818: mov             x1, x2
    // 0x160181c: r0 = RxNotifier()
    //     0x160181c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1601820: ldur            x0, [fp, #-0x10]
    // 0x1601824: StoreField: r0->field_13 = rNULL
    //     0x1601824: stur            NULL, [x0, #0x13]
    // 0x1601828: r1 = <bool?>
    //     0x1601828: ldr             x1, [PP, #0x7f38]  ; [pp+0x7f38] TypeArguments: <bool?>
    // 0x160182c: r0 = RxnBool()
    //     0x160182c: bl              #0x12c3ca8  ; AllocateRxnBoolStub -> RxnBool (size=0x1c)
    // 0x1601830: mov             x2, x0
    // 0x1601834: r0 = Sentinel
    //     0x1601834: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1601838: stur            x2, [fp, #-0x10]
    // 0x160183c: StoreField: r2->field_13 = r0
    //     0x160183c: stur            w0, [x2, #0x13]
    // 0x1601840: r3 = true
    //     0x1601840: add             x3, NULL, #0x20  ; true
    // 0x1601844: ArrayStore: r2[0] = r3  ; List_4
    //     0x1601844: stur            w3, [x2, #0x17]
    // 0x1601848: mov             x1, x2
    // 0x160184c: r0 = RxNotifier()
    //     0x160184c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1601850: ldur            x0, [fp, #-0x10]
    // 0x1601854: r2 = false
    //     0x1601854: add             x2, NULL, #0x30  ; false
    // 0x1601858: StoreField: r0->field_13 = r2
    //     0x1601858: stur            w2, [x0, #0x13]
    // 0x160185c: ldur            x3, [fp, #-8]
    // 0x1601860: StoreField: r3->field_8f = r0
    //     0x1601860: stur            w0, [x3, #0x8f]
    //     0x1601864: ldurb           w16, [x3, #-1]
    //     0x1601868: ldurb           w17, [x0, #-1]
    //     0x160186c: and             x16, x17, x16, lsr #2
    //     0x1601870: tst             x16, HEAP, lsr #32
    //     0x1601874: b.eq            #0x160187c
    //     0x1601878: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x160187c: r1 = <LocalConfigData?, LocalConfigData>
    //     0x160187c: add             x1, PP, #0xa, lsl #12  ; [pp+0xac30] TypeArguments: <LocalConfigData?, LocalConfigData>
    //     0x1601880: ldr             x1, [x1, #0xc30]
    // 0x1601884: r0 = Rxn()
    //     0x1601884: bl              #0x12c3fbc  ; AllocateRxnStub -> Rxn<C1X0> (size=0x1c)
    // 0x1601888: mov             x2, x0
    // 0x160188c: r0 = Sentinel
    //     0x160188c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1601890: stur            x2, [fp, #-0x10]
    // 0x1601894: StoreField: r2->field_13 = r0
    //     0x1601894: stur            w0, [x2, #0x13]
    // 0x1601898: r3 = true
    //     0x1601898: add             x3, NULL, #0x20  ; true
    // 0x160189c: ArrayStore: r2[0] = r3  ; List_4
    //     0x160189c: stur            w3, [x2, #0x17]
    // 0x16018a0: mov             x1, x2
    // 0x16018a4: r0 = RxNotifier()
    //     0x16018a4: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x16018a8: ldur            x0, [fp, #-0x10]
    // 0x16018ac: StoreField: r0->field_13 = rNULL
    //     0x16018ac: stur            NULL, [x0, #0x13]
    // 0x16018b0: ldur            x2, [fp, #-8]
    // 0x16018b4: StoreField: r2->field_93 = r0
    //     0x16018b4: stur            w0, [x2, #0x93]
    //     0x16018b8: ldurb           w16, [x2, #-1]
    //     0x16018bc: ldurb           w17, [x0, #-1]
    //     0x16018c0: and             x16, x17, x16, lsr #2
    //     0x16018c4: tst             x16, HEAP, lsr #32
    //     0x16018c8: b.eq            #0x16018d0
    //     0x16018cc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x16018d0: r1 = <bool?>
    //     0x16018d0: ldr             x1, [PP, #0x7f38]  ; [pp+0x7f38] TypeArguments: <bool?>
    // 0x16018d4: r0 = RxnBool()
    //     0x16018d4: bl              #0x12c3ca8  ; AllocateRxnBoolStub -> RxnBool (size=0x1c)
    // 0x16018d8: mov             x2, x0
    // 0x16018dc: r0 = Sentinel
    //     0x16018dc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x16018e0: stur            x2, [fp, #-0x10]
    // 0x16018e4: StoreField: r2->field_13 = r0
    //     0x16018e4: stur            w0, [x2, #0x13]
    // 0x16018e8: r3 = true
    //     0x16018e8: add             x3, NULL, #0x20  ; true
    // 0x16018ec: ArrayStore: r2[0] = r3  ; List_4
    //     0x16018ec: stur            w3, [x2, #0x17]
    // 0x16018f0: mov             x1, x2
    // 0x16018f4: r0 = RxNotifier()
    //     0x16018f4: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x16018f8: ldur            x0, [fp, #-0x10]
    // 0x16018fc: r1 = false
    //     0x16018fc: add             x1, NULL, #0x30  ; false
    // 0x1601900: StoreField: r0->field_13 = r1
    //     0x1601900: stur            w1, [x0, #0x13]
    // 0x1601904: ldur            x2, [fp, #-8]
    // 0x1601908: StoreField: r2->field_97 = r0
    //     0x1601908: stur            w0, [x2, #0x97]
    //     0x160190c: ldurb           w16, [x2, #-1]
    //     0x1601910: ldurb           w17, [x0, #-1]
    //     0x1601914: and             x16, x17, x16, lsr #2
    //     0x1601918: tst             x16, HEAP, lsr #32
    //     0x160191c: b.eq            #0x1601924
    //     0x1601920: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601924: r16 = ConnectionController
    //     0x1601924: add             x16, PP, #0xa, lsl #12  ; [pp+0xaaf8] Type: ConnectionController
    //     0x1601928: ldr             x16, [x16, #0xaf8]
    // 0x160192c: str             x16, [SP]
    // 0x1601930: r0 = toString()
    //     0x1601930: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1601934: r16 = <ConnectionController>
    //     0x1601934: add             x16, PP, #0xa, lsl #12  ; [pp+0xab00] TypeArguments: <ConnectionController>
    //     0x1601938: ldr             x16, [x16, #0xb00]
    // 0x160193c: stp             x0, x16, [SP]
    // 0x1601940: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1601940: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1601944: r0 = Inst.find()
    //     0x1601944: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1601948: ldur            x1, [fp, #-8]
    // 0x160194c: StoreField: r1->field_a7 = r0
    //     0x160194c: stur            w0, [x1, #0xa7]
    //     0x1601950: ldurb           w16, [x1, #-1]
    //     0x1601954: ldurb           w17, [x0, #-1]
    //     0x1601958: and             x16, x17, x16, lsr #2
    //     0x160195c: tst             x16, HEAP, lsr #32
    //     0x1601960: b.eq            #0x1601968
    //     0x1601964: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1601968: r0 = OfferParams()
    //     0x1601968: bl              #0x8b5168  ; AllocateOfferParamsStub -> OfferParams (size=0x18)
    // 0x160196c: mov             x1, x0
    // 0x1601970: stur            x0, [fp, #-0x10]
    // 0x1601974: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1601974: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1601978: r0 = OfferParams()
    //     0x1601978: bl              #0x8b4ee4  ; [package:customer_app/app/data/models/common/request/offer_params.dart] OfferParams::OfferParams
    // 0x160197c: r16 = <OfferParams>
    //     0x160197c: add             x16, PP, #0xa, lsl #12  ; [pp+0xac38] TypeArguments: <OfferParams>
    //     0x1601980: ldr             x16, [x16, #0xc38]
    // 0x1601984: ldur            lr, [fp, #-0x10]
    // 0x1601988: stp             lr, x16, [SP]
    // 0x160198c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x160198c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1601990: r0 = RxT.obs()
    //     0x1601990: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1601994: ldur            x2, [fp, #-8]
    // 0x1601998: StoreField: r2->field_b3 = r0
    //     0x1601998: stur            w0, [x2, #0xb3]
    //     0x160199c: ldurb           w16, [x2, #-1]
    //     0x16019a0: ldurb           w17, [x0, #-1]
    //     0x16019a4: and             x16, x17, x16, lsr #2
    //     0x16019a8: tst             x16, HEAP, lsr #32
    //     0x16019ac: b.eq            #0x16019b4
    //     0x16019b0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x16019b4: r1 = false
    //     0x16019b4: add             x1, NULL, #0x30  ; false
    // 0x16019b8: r0 = BoolExtension.obs()
    //     0x16019b8: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x16019bc: ldur            x2, [fp, #-8]
    // 0x16019c0: StoreField: r2->field_b7 = r0
    //     0x16019c0: stur            w0, [x2, #0xb7]
    //     0x16019c4: ldurb           w16, [x2, #-1]
    //     0x16019c8: ldurb           w17, [x0, #-1]
    //     0x16019cc: and             x16, x17, x16, lsr #2
    //     0x16019d0: tst             x16, HEAP, lsr #32
    //     0x16019d4: b.eq            #0x16019dc
    //     0x16019d8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x16019dc: r1 = false
    //     0x16019dc: add             x1, NULL, #0x30  ; false
    // 0x16019e0: r0 = BoolExtension.obs()
    //     0x16019e0: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x16019e4: ldur            x2, [fp, #-8]
    // 0x16019e8: StoreField: r2->field_bb = r0
    //     0x16019e8: stur            w0, [x2, #0xbb]
    //     0x16019ec: ldurb           w16, [x2, #-1]
    //     0x16019f0: ldurb           w17, [x0, #-1]
    //     0x16019f4: and             x16, x17, x16, lsr #2
    //     0x16019f8: tst             x16, HEAP, lsr #32
    //     0x16019fc: b.eq            #0x1601a04
    //     0x1601a00: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601a04: r1 = false
    //     0x1601a04: add             x1, NULL, #0x30  ; false
    // 0x1601a08: r0 = BoolExtension.obs()
    //     0x1601a08: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1601a0c: ldur            x2, [fp, #-8]
    // 0x1601a10: StoreField: r2->field_bf = r0
    //     0x1601a10: stur            w0, [x2, #0xbf]
    //     0x1601a14: ldurb           w16, [x2, #-1]
    //     0x1601a18: ldurb           w17, [x0, #-1]
    //     0x1601a1c: and             x16, x17, x16, lsr #2
    //     0x1601a20: tst             x16, HEAP, lsr #32
    //     0x1601a24: b.eq            #0x1601a2c
    //     0x1601a28: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601a2c: r1 = false
    //     0x1601a2c: add             x1, NULL, #0x30  ; false
    // 0x1601a30: r0 = BoolExtension.obs()
    //     0x1601a30: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1601a34: ldur            x2, [fp, #-8]
    // 0x1601a38: StoreField: r2->field_c3 = r0
    //     0x1601a38: stur            w0, [x2, #0xc3]
    //     0x1601a3c: ldurb           w16, [x2, #-1]
    //     0x1601a40: ldurb           w17, [x0, #-1]
    //     0x1601a44: and             x16, x17, x16, lsr #2
    //     0x1601a48: tst             x16, HEAP, lsr #32
    //     0x1601a4c: b.eq            #0x1601a54
    //     0x1601a50: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601a54: r1 = <int?, int>
    //     0x1601a54: add             x1, PP, #0xa, lsl #12  ; [pp+0xac40] TypeArguments: <int?, int>
    //     0x1601a58: ldr             x1, [x1, #0xc40]
    // 0x1601a5c: r0 = Rxn()
    //     0x1601a5c: bl              #0x12c3fbc  ; AllocateRxnStub -> Rxn<C1X0> (size=0x1c)
    // 0x1601a60: mov             x2, x0
    // 0x1601a64: r0 = Sentinel
    //     0x1601a64: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1601a68: stur            x2, [fp, #-0x10]
    // 0x1601a6c: StoreField: r2->field_13 = r0
    //     0x1601a6c: stur            w0, [x2, #0x13]
    // 0x1601a70: r0 = true
    //     0x1601a70: add             x0, NULL, #0x20  ; true
    // 0x1601a74: ArrayStore: r2[0] = r0  ; List_4
    //     0x1601a74: stur            w0, [x2, #0x17]
    // 0x1601a78: mov             x1, x2
    // 0x1601a7c: r0 = RxNotifier()
    //     0x1601a7c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0x1601a80: ldur            x0, [fp, #-0x10]
    // 0x1601a84: StoreField: r0->field_13 = rNULL
    //     0x1601a84: stur            NULL, [x0, #0x13]
    // 0x1601a88: ldur            x2, [fp, #-8]
    // 0x1601a8c: StoreField: r2->field_c7 = r0
    //     0x1601a8c: stur            w0, [x2, #0xc7]
    //     0x1601a90: ldurb           w16, [x2, #-1]
    //     0x1601a94: ldurb           w17, [x0, #-1]
    //     0x1601a98: and             x16, x17, x16, lsr #2
    //     0x1601a9c: tst             x16, HEAP, lsr #32
    //     0x1601aa0: b.eq            #0x1601aa8
    //     0x1601aa4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601aa8: r1 = Null
    //     0x1601aa8: mov             x1, NULL
    // 0x1601aac: r0 = SmsAutoFill()
    //     0x1601aac: bl              #0x905c88  ; [package:sms_autofill/sms_autofill.dart] SmsAutoFill::SmsAutoFill
    // 0x1601ab0: ldur            x1, [fp, #-8]
    // 0x1601ab4: r0 = BaseController()
    //     0x1601ab4: bl              #0x12c396c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::BaseController
    // 0x1601ab8: r16 = LoginRepo
    //     0x1601ab8: add             x16, PP, #0xa, lsl #12  ; [pp+0xac48] Type: LoginRepo
    //     0x1601abc: ldr             x16, [x16, #0xc48]
    // 0x1601ac0: str             x16, [SP]
    // 0x1601ac4: r0 = toString()
    //     0x1601ac4: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1601ac8: r1 = Function '<anonymous closure>':.
    //     0x1601ac8: add             x1, PP, #0xa, lsl #12  ; [pp+0xac50] AnonymousClosure: (0x1601b58), in [package:customer_app/app/presentation/controllers/login/login_controller.dart] LoginController::LoginController (0x1601494)
    //     0x1601acc: ldr             x1, [x1, #0xc50]
    // 0x1601ad0: r2 = Null
    //     0x1601ad0: mov             x2, NULL
    // 0x1601ad4: stur            x0, [fp, #-0x10]
    // 0x1601ad8: r0 = AllocateClosure()
    //     0x1601ad8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1601adc: r16 = <LoginRepo>
    //     0x1601adc: add             x16, PP, #0xa, lsl #12  ; [pp+0xac58] TypeArguments: <LoginRepo>
    //     0x1601ae0: ldr             x16, [x16, #0xc58]
    // 0x1601ae4: stp             x0, x16, [SP, #8]
    // 0x1601ae8: ldur            x16, [fp, #-0x10]
    // 0x1601aec: str             x16, [SP]
    // 0x1601af0: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x1601af0: add             x4, PP, #0xa, lsl #12  ; [pp+0xaac8] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x1601af4: ldr             x4, [x4, #0xac8]
    // 0x1601af8: r0 = Inst.lazyPut()
    //     0x1601af8: bl              #0x12c3844  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x1601afc: r16 = LoginRepo
    //     0x1601afc: add             x16, PP, #0xa, lsl #12  ; [pp+0xac48] Type: LoginRepo
    //     0x1601b00: ldr             x16, [x16, #0xc48]
    // 0x1601b04: str             x16, [SP]
    // 0x1601b08: r0 = toString()
    //     0x1601b08: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1601b0c: r16 = <LoginRepo>
    //     0x1601b0c: add             x16, PP, #0xa, lsl #12  ; [pp+0xac58] TypeArguments: <LoginRepo>
    //     0x1601b10: ldr             x16, [x16, #0xc58]
    // 0x1601b14: stp             x0, x16, [SP]
    // 0x1601b18: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1601b18: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1601b1c: r0 = Inst.find()
    //     0x1601b1c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1601b20: ldur            x1, [fp, #-8]
    // 0x1601b24: StoreField: r1->field_47 = r0
    //     0x1601b24: stur            w0, [x1, #0x47]
    //     0x1601b28: ldurb           w16, [x1, #-1]
    //     0x1601b2c: ldurb           w17, [x0, #-1]
    //     0x1601b30: and             x16, x17, x16, lsr #2
    //     0x1601b34: tst             x16, HEAP, lsr #32
    //     0x1601b38: b.eq            #0x1601b40
    //     0x1601b3c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1601b40: r0 = Null
    //     0x1601b40: mov             x0, NULL
    // 0x1601b44: LeaveFrame
    //     0x1601b44: mov             SP, fp
    //     0x1601b48: ldp             fp, lr, [SP], #0x10
    // 0x1601b4c: ret
    //     0x1601b4c: ret             
    // 0x1601b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1601b50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1601b54: b               #0x16014bc
  }
  [closure] LoginRepoImpl <anonymous closure>(dynamic) {
    // ** addr: 0x1601b58, size: 0x40
    // 0x1601b58: EnterFrame
    //     0x1601b58: stp             fp, lr, [SP, #-0x10]!
    //     0x1601b5c: mov             fp, SP
    // 0x1601b60: AllocStack(0x8)
    //     0x1601b60: sub             SP, SP, #8
    // 0x1601b64: CheckStackOverflow
    //     0x1601b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1601b68: cmp             SP, x16
    //     0x1601b6c: b.ls            #0x1601b90
    // 0x1601b70: r0 = LoginRepoImpl()
    //     0x1601b70: bl              #0x1601c9c  ; AllocateLoginRepoImplStub -> LoginRepoImpl (size=0xc)
    // 0x1601b74: mov             x1, x0
    // 0x1601b78: stur            x0, [fp, #-8]
    // 0x1601b7c: r0 = LoginRepoImpl()
    //     0x1601b7c: bl              #0x1601b98  ; [package:customer_app/app/data/repositories/login/login_repo_impl.dart] LoginRepoImpl::LoginRepoImpl
    // 0x1601b80: ldur            x0, [fp, #-8]
    // 0x1601b84: LeaveFrame
    //     0x1601b84: mov             SP, fp
    //     0x1601b88: ldp             fp, lr, [SP], #0x10
    // 0x1601b8c: ret
    //     0x1601b8c: ret             
    // 0x1601b90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1601b90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1601b94: b               #0x1601b70
  }
}
