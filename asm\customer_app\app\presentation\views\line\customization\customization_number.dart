// lib: , url: package:customer_app/app/presentation/views/line/customization/customization_number.dart

// class id: 1049508, size: 0x8
class :: {
}

// class id: 3256, size: 0x1c, field offset: 0x14
class _CustomisationNumberState extends State<dynamic> {

  [closure] bool <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0xa3afc0, size: 0xa0
    // 0xa3afc0: EnterFrame
    //     0xa3afc0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3afc4: mov             fp, SP
    // 0xa3afc8: AllocStack(0x10)
    //     0xa3afc8: sub             SP, SP, #0x10
    // 0xa3afcc: SetupParameters()
    //     0xa3afcc: ldr             x0, [fp, #0x18]
    //     0xa3afd0: ldur            w1, [x0, #0x17]
    //     0xa3afd4: add             x1, x1, HEAP, lsl #32
    // 0xa3afd8: CheckStackOverflow
    //     0xa3afd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3afdc: cmp             SP, x16
    //     0xa3afe0: b.ls            #0xa3b054
    // 0xa3afe4: ldr             x0, [fp, #0x10]
    // 0xa3afe8: LoadField: r2 = r0->field_b
    //     0xa3afe8: ldur            w2, [x0, #0xb]
    // 0xa3afec: DecompressPointer r2
    //     0xa3afec: add             x2, x2, HEAP, lsl #32
    // 0xa3aff0: LoadField: r0 = r1->field_f
    //     0xa3aff0: ldur            w0, [x1, #0xf]
    // 0xa3aff4: DecompressPointer r0
    //     0xa3aff4: add             x0, x0, HEAP, lsl #32
    // 0xa3aff8: LoadField: r1 = r0->field_b
    //     0xa3aff8: ldur            w1, [x0, #0xb]
    // 0xa3affc: DecompressPointer r1
    //     0xa3affc: add             x1, x1, HEAP, lsl #32
    // 0xa3b000: cmp             w1, NULL
    // 0xa3b004: b.eq            #0xa3b05c
    // 0xa3b008: LoadField: r0 = r1->field_b
    //     0xa3b008: ldur            w0, [x1, #0xb]
    // 0xa3b00c: DecompressPointer r0
    //     0xa3b00c: add             x0, x0, HEAP, lsl #32
    // 0xa3b010: cmp             w0, NULL
    // 0xa3b014: b.ne            #0xa3b020
    // 0xa3b018: r0 = Null
    //     0xa3b018: mov             x0, NULL
    // 0xa3b01c: b               #0xa3b02c
    // 0xa3b020: LoadField: r1 = r0->field_7
    //     0xa3b020: ldur            w1, [x0, #7]
    // 0xa3b024: DecompressPointer r1
    //     0xa3b024: add             x1, x1, HEAP, lsl #32
    // 0xa3b028: mov             x0, x1
    // 0xa3b02c: r1 = LoadClassIdInstr(r2)
    //     0xa3b02c: ldur            x1, [x2, #-1]
    //     0xa3b030: ubfx            x1, x1, #0xc, #0x14
    // 0xa3b034: stp             x0, x2, [SP]
    // 0xa3b038: mov             x0, x1
    // 0xa3b03c: mov             lr, x0
    // 0xa3b040: ldr             lr, [x21, lr, lsl #3]
    // 0xa3b044: blr             lr
    // 0xa3b048: LeaveFrame
    //     0xa3b048: mov             SP, fp
    //     0xa3b04c: ldp             fp, lr, [SP], #0x10
    // 0xa3b050: ret
    //     0xa3b050: ret             
    // 0xa3b054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3b054: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3b058: b               #0xa3afe4
    // 0xa3b05c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b05c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa3b060, size: 0x5cc
    // 0xa3b060: EnterFrame
    //     0xa3b060: stp             fp, lr, [SP, #-0x10]!
    //     0xa3b064: mov             fp, SP
    // 0xa3b068: AllocStack(0x78)
    //     0xa3b068: sub             SP, SP, #0x78
    // 0xa3b06c: SetupParameters()
    //     0xa3b06c: ldr             x0, [fp, #0x18]
    //     0xa3b070: ldur            w3, [x0, #0x17]
    //     0xa3b074: add             x3, x3, HEAP, lsl #32
    //     0xa3b078: stur            x3, [fp, #-0x10]
    // 0xa3b07c: CheckStackOverflow
    //     0xa3b07c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3b080: cmp             SP, x16
    //     0xa3b084: b.ls            #0xa3b608
    // 0xa3b088: LoadField: r0 = r3->field_f
    //     0xa3b088: ldur            w0, [x3, #0xf]
    // 0xa3b08c: DecompressPointer r0
    //     0xa3b08c: add             x0, x0, HEAP, lsl #32
    // 0xa3b090: LoadField: r1 = r0->field_b
    //     0xa3b090: ldur            w1, [x0, #0xb]
    // 0xa3b094: DecompressPointer r1
    //     0xa3b094: add             x1, x1, HEAP, lsl #32
    // 0xa3b098: cmp             w1, NULL
    // 0xa3b09c: b.eq            #0xa3b610
    // 0xa3b0a0: LoadField: r0 = r1->field_f
    //     0xa3b0a0: ldur            w0, [x1, #0xf]
    // 0xa3b0a4: DecompressPointer r0
    //     0xa3b0a4: add             x0, x0, HEAP, lsl #32
    // 0xa3b0a8: stur            x0, [fp, #-8]
    // 0xa3b0ac: LoadField: r1 = r0->field_b
    //     0xa3b0ac: ldur            w1, [x0, #0xb]
    // 0xa3b0b0: cbz             w1, #0xa3b168
    // 0xa3b0b4: mov             x2, x3
    // 0xa3b0b8: r1 = Function '<anonymous closure>':.
    //     0xa3b0b8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a430] AnonymousClosure: (0xa3afc0), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xa3b0bc: ldr             x1, [x1, #0x430]
    // 0xa3b0c0: r0 = AllocateClosure()
    //     0xa3b0c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa3b0c4: r1 = Function '<anonymous closure>':.
    //     0xa3b0c4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a438] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xa3b0c8: ldr             x1, [x1, #0x438]
    // 0xa3b0cc: r2 = Null
    //     0xa3b0cc: mov             x2, NULL
    // 0xa3b0d0: stur            x0, [fp, #-0x18]
    // 0xa3b0d4: r0 = AllocateClosure()
    //     0xa3b0d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa3b0d8: str             x0, [SP]
    // 0xa3b0dc: ldur            x1, [fp, #-8]
    // 0xa3b0e0: ldur            x2, [fp, #-0x18]
    // 0xa3b0e4: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xa3b0e4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xa3b0e8: ldr             x4, [x4, #0xb48]
    // 0xa3b0ec: r0 = firstWhere()
    //     0xa3b0ec: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xa3b0f0: LoadField: r1 = r0->field_b
    //     0xa3b0f0: ldur            w1, [x0, #0xb]
    // 0xa3b0f4: DecompressPointer r1
    //     0xa3b0f4: add             x1, x1, HEAP, lsl #32
    // 0xa3b0f8: cmp             w1, NULL
    // 0xa3b0fc: b.eq            #0xa3b168
    // 0xa3b100: ldur            x1, [fp, #-0x10]
    // 0xa3b104: LoadField: r2 = r1->field_f
    //     0xa3b104: ldur            w2, [x1, #0xf]
    // 0xa3b108: DecompressPointer r2
    //     0xa3b108: add             x2, x2, HEAP, lsl #32
    // 0xa3b10c: LoadField: r3 = r2->field_b
    //     0xa3b10c: ldur            w3, [x2, #0xb]
    // 0xa3b110: DecompressPointer r3
    //     0xa3b110: add             x3, x3, HEAP, lsl #32
    // 0xa3b114: cmp             w3, NULL
    // 0xa3b118: b.eq            #0xa3b614
    // 0xa3b11c: LoadField: r2 = r3->field_b
    //     0xa3b11c: ldur            w2, [x3, #0xb]
    // 0xa3b120: DecompressPointer r2
    //     0xa3b120: add             x2, x2, HEAP, lsl #32
    // 0xa3b124: cmp             w2, NULL
    // 0xa3b128: b.ne            #0xa3b134
    // 0xa3b12c: r2 = Null
    //     0xa3b12c: mov             x2, NULL
    // 0xa3b130: b               #0xa3b140
    // 0xa3b134: LoadField: r4 = r2->field_23
    //     0xa3b134: ldur            w4, [x2, #0x23]
    // 0xa3b138: DecompressPointer r4
    //     0xa3b138: add             x4, x4, HEAP, lsl #32
    // 0xa3b13c: mov             x2, x4
    // 0xa3b140: LoadField: r4 = r3->field_13
    //     0xa3b140: ldur            w4, [x3, #0x13]
    // 0xa3b144: DecompressPointer r4
    //     0xa3b144: add             x4, x4, HEAP, lsl #32
    // 0xa3b148: stp             x2, x4, [SP, #8]
    // 0xa3b14c: str             x0, [SP]
    // 0xa3b150: r4 = 0
    //     0xa3b150: movz            x4, #0
    // 0xa3b154: ldr             x0, [SP, #0x10]
    // 0xa3b158: r16 = UnlinkedCall_0x613b5c
    //     0xa3b158: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a440] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa3b15c: add             x16, x16, #0x440
    // 0xa3b160: ldp             x5, lr, [x16]
    // 0xa3b164: blr             lr
    // 0xa3b168: ldr             x0, [fp, #0x10]
    // 0xa3b16c: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xa3b16c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa3b170: ldr             x0, [x0]
    //     0xa3b174: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3b178: cmp             w0, w16
    //     0xa3b17c: b.ne            #0xa3b188
    //     0xa3b180: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xa3b184: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa3b188: r1 = <CustomerResponse>
    //     0xa3b188: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xa3b18c: ldr             x1, [x1, #0x5a8]
    // 0xa3b190: stur            x0, [fp, #-8]
    // 0xa3b194: r0 = AllocateGrowableArray()
    //     0xa3b194: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa3b198: mov             x2, x0
    // 0xa3b19c: ldur            x0, [fp, #-8]
    // 0xa3b1a0: stur            x2, [fp, #-0x18]
    // 0xa3b1a4: StoreField: r2->field_f = r0
    //     0xa3b1a4: stur            w0, [x2, #0xf]
    // 0xa3b1a8: StoreField: r2->field_b = rZR
    //     0xa3b1a8: stur            wzr, [x2, #0xb]
    // 0xa3b1ac: r1 = <ProductCustomisation>
    //     0xa3b1ac: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xa3b1b0: ldr             x1, [x1, #0x370]
    // 0xa3b1b4: r0 = AllocateGrowableArray()
    //     0xa3b1b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa3b1b8: mov             x1, x0
    // 0xa3b1bc: ldur            x0, [fp, #-8]
    // 0xa3b1c0: stur            x1, [fp, #-0x28]
    // 0xa3b1c4: StoreField: r1->field_f = r0
    //     0xa3b1c4: stur            w0, [x1, #0xf]
    // 0xa3b1c8: StoreField: r1->field_b = rZR
    //     0xa3b1c8: stur            wzr, [x1, #0xb]
    // 0xa3b1cc: ldr             x2, [fp, #0x10]
    // 0xa3b1d0: cmp             w2, NULL
    // 0xa3b1d4: b.eq            #0xa3b558
    // 0xa3b1d8: ldur            x3, [fp, #-0x10]
    // 0xa3b1dc: LoadField: r4 = r3->field_f
    //     0xa3b1dc: ldur            w4, [x3, #0xf]
    // 0xa3b1e0: DecompressPointer r4
    //     0xa3b1e0: add             x4, x4, HEAP, lsl #32
    // 0xa3b1e4: LoadField: r5 = r4->field_b
    //     0xa3b1e4: ldur            w5, [x4, #0xb]
    // 0xa3b1e8: DecompressPointer r5
    //     0xa3b1e8: add             x5, x5, HEAP, lsl #32
    // 0xa3b1ec: cmp             w5, NULL
    // 0xa3b1f0: b.eq            #0xa3b618
    // 0xa3b1f4: LoadField: r4 = r5->field_b
    //     0xa3b1f4: ldur            w4, [x5, #0xb]
    // 0xa3b1f8: DecompressPointer r4
    //     0xa3b1f8: add             x4, x4, HEAP, lsl #32
    // 0xa3b1fc: cmp             w4, NULL
    // 0xa3b200: b.ne            #0xa3b20c
    // 0xa3b204: r4 = Null
    //     0xa3b204: mov             x4, NULL
    // 0xa3b208: b               #0xa3b218
    // 0xa3b20c: LoadField: r5 = r4->field_1b
    //     0xa3b20c: ldur            w5, [x4, #0x1b]
    // 0xa3b210: DecompressPointer r5
    //     0xa3b210: add             x5, x5, HEAP, lsl #32
    // 0xa3b214: mov             x4, x5
    // 0xa3b218: stur            x4, [fp, #-0x20]
    // 0xa3b21c: r0 = CustomerResponse()
    //     0xa3b21c: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xa3b220: mov             x2, x0
    // 0xa3b224: ldur            x0, [fp, #-0x20]
    // 0xa3b228: stur            x2, [fp, #-0x30]
    // 0xa3b22c: StoreField: r2->field_7 = r0
    //     0xa3b22c: stur            w0, [x2, #7]
    // 0xa3b230: ldr             x0, [fp, #0x10]
    // 0xa3b234: StoreField: r2->field_b = r0
    //     0xa3b234: stur            w0, [x2, #0xb]
    // 0xa3b238: ldur            x1, [fp, #-8]
    // 0xa3b23c: LoadField: r3 = r1->field_b
    //     0xa3b23c: ldur            w3, [x1, #0xb]
    // 0xa3b240: cbnz            w3, #0xa3b24c
    // 0xa3b244: ldur            x1, [fp, #-0x18]
    // 0xa3b248: r0 = _growToNextCapacity()
    //     0xa3b248: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa3b24c: ldur            x3, [fp, #-0x10]
    // 0xa3b250: ldur            x4, [fp, #-0x18]
    // 0xa3b254: r0 = 2
    //     0xa3b254: movz            x0, #0x2
    // 0xa3b258: StoreField: r4->field_b = r0
    //     0xa3b258: stur            w0, [x4, #0xb]
    // 0xa3b25c: LoadField: r1 = r4->field_f
    //     0xa3b25c: ldur            w1, [x4, #0xf]
    // 0xa3b260: DecompressPointer r1
    //     0xa3b260: add             x1, x1, HEAP, lsl #32
    // 0xa3b264: ldur            x0, [fp, #-0x30]
    // 0xa3b268: ArrayStore: r1[0] = r0  ; List_4
    //     0xa3b268: add             x25, x1, #0xf
    //     0xa3b26c: str             w0, [x25]
    //     0xa3b270: tbz             w0, #0, #0xa3b28c
    //     0xa3b274: ldurb           w16, [x1, #-1]
    //     0xa3b278: ldurb           w17, [x0, #-1]
    //     0xa3b27c: and             x16, x17, x16, lsr #2
    //     0xa3b280: tst             x16, HEAP, lsr #32
    //     0xa3b284: b.eq            #0xa3b28c
    //     0xa3b288: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa3b28c: LoadField: r0 = r3->field_f
    //     0xa3b28c: ldur            w0, [x3, #0xf]
    // 0xa3b290: DecompressPointer r0
    //     0xa3b290: add             x0, x0, HEAP, lsl #32
    // 0xa3b294: LoadField: r1 = r0->field_b
    //     0xa3b294: ldur            w1, [x0, #0xb]
    // 0xa3b298: DecompressPointer r1
    //     0xa3b298: add             x1, x1, HEAP, lsl #32
    // 0xa3b29c: cmp             w1, NULL
    // 0xa3b2a0: b.eq            #0xa3b61c
    // 0xa3b2a4: LoadField: r0 = r1->field_b
    //     0xa3b2a4: ldur            w0, [x1, #0xb]
    // 0xa3b2a8: DecompressPointer r0
    //     0xa3b2a8: add             x0, x0, HEAP, lsl #32
    // 0xa3b2ac: cmp             w0, NULL
    // 0xa3b2b0: b.ne            #0xa3b2bc
    // 0xa3b2b4: r5 = Null
    //     0xa3b2b4: mov             x5, NULL
    // 0xa3b2b8: b               #0xa3b2c8
    // 0xa3b2bc: LoadField: r1 = r0->field_7
    //     0xa3b2bc: ldur            w1, [x0, #7]
    // 0xa3b2c0: DecompressPointer r1
    //     0xa3b2c0: add             x1, x1, HEAP, lsl #32
    // 0xa3b2c4: mov             x5, x1
    // 0xa3b2c8: stur            x5, [fp, #-0x38]
    // 0xa3b2cc: cmp             w0, NULL
    // 0xa3b2d0: b.ne            #0xa3b2dc
    // 0xa3b2d4: r6 = Null
    //     0xa3b2d4: mov             x6, NULL
    // 0xa3b2d8: b               #0xa3b2e8
    // 0xa3b2dc: LoadField: r1 = r0->field_b
    //     0xa3b2dc: ldur            w1, [x0, #0xb]
    // 0xa3b2e0: DecompressPointer r1
    //     0xa3b2e0: add             x1, x1, HEAP, lsl #32
    // 0xa3b2e4: mov             x6, x1
    // 0xa3b2e8: stur            x6, [fp, #-0x30]
    // 0xa3b2ec: cmp             w0, NULL
    // 0xa3b2f0: b.ne            #0xa3b2fc
    // 0xa3b2f4: r7 = Null
    //     0xa3b2f4: mov             x7, NULL
    // 0xa3b2f8: b               #0xa3b308
    // 0xa3b2fc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa3b2fc: ldur            w1, [x0, #0x17]
    // 0xa3b300: DecompressPointer r1
    //     0xa3b300: add             x1, x1, HEAP, lsl #32
    // 0xa3b304: mov             x7, x1
    // 0xa3b308: stur            x7, [fp, #-0x20]
    // 0xa3b30c: cmp             w0, NULL
    // 0xa3b310: b.ne            #0xa3b31c
    // 0xa3b314: r0 = Null
    //     0xa3b314: mov             x0, NULL
    // 0xa3b318: b               #0xa3b328
    // 0xa3b31c: LoadField: r1 = r0->field_23
    //     0xa3b31c: ldur            w1, [x0, #0x23]
    // 0xa3b320: DecompressPointer r1
    //     0xa3b320: add             x1, x1, HEAP, lsl #32
    // 0xa3b324: mov             x0, x1
    // 0xa3b328: stur            x0, [fp, #-8]
    // 0xa3b32c: r1 = Null
    //     0xa3b32c: mov             x1, NULL
    // 0xa3b330: r2 = 4
    //     0xa3b330: movz            x2, #0x4
    // 0xa3b334: r0 = AllocateArray()
    //     0xa3b334: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa3b338: stur            x0, [fp, #-0x40]
    // 0xa3b33c: r16 = "₹"
    //     0xa3b33c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xa3b340: ldr             x16, [x16, #0x360]
    // 0xa3b344: StoreField: r0->field_f = r16
    //     0xa3b344: stur            w16, [x0, #0xf]
    // 0xa3b348: r1 = Function '<anonymous closure>': static.
    //     0xa3b348: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xa3b34c: ldr             x1, [x1, #0x1a0]
    // 0xa3b350: r2 = Null
    //     0xa3b350: mov             x2, NULL
    // 0xa3b354: r0 = AllocateClosure()
    //     0xa3b354: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa3b358: mov             x3, x0
    // 0xa3b35c: r1 = Null
    //     0xa3b35c: mov             x1, NULL
    // 0xa3b360: r2 = Null
    //     0xa3b360: mov             x2, NULL
    // 0xa3b364: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xa3b364: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xa3b368: r0 = NumberFormat._forPattern()
    //     0xa3b368: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xa3b36c: mov             x1, x0
    // 0xa3b370: ldur            x0, [fp, #-0x10]
    // 0xa3b374: LoadField: r2 = r0->field_f
    //     0xa3b374: ldur            w2, [x0, #0xf]
    // 0xa3b378: DecompressPointer r2
    //     0xa3b378: add             x2, x2, HEAP, lsl #32
    // 0xa3b37c: LoadField: r3 = r2->field_b
    //     0xa3b37c: ldur            w3, [x2, #0xb]
    // 0xa3b380: DecompressPointer r3
    //     0xa3b380: add             x3, x3, HEAP, lsl #32
    // 0xa3b384: cmp             w3, NULL
    // 0xa3b388: b.eq            #0xa3b620
    // 0xa3b38c: LoadField: r2 = r3->field_b
    //     0xa3b38c: ldur            w2, [x3, #0xb]
    // 0xa3b390: DecompressPointer r2
    //     0xa3b390: add             x2, x2, HEAP, lsl #32
    // 0xa3b394: cmp             w2, NULL
    // 0xa3b398: b.ne            #0xa3b3a4
    // 0xa3b39c: r2 = Null
    //     0xa3b39c: mov             x2, NULL
    // 0xa3b3a0: b               #0xa3b3b0
    // 0xa3b3a4: LoadField: r3 = r2->field_23
    //     0xa3b3a4: ldur            w3, [x2, #0x23]
    // 0xa3b3a8: DecompressPointer r3
    //     0xa3b3a8: add             x3, x3, HEAP, lsl #32
    // 0xa3b3ac: mov             x2, x3
    // 0xa3b3b0: ldur            x4, [fp, #-0x38]
    // 0xa3b3b4: ldur            x5, [fp, #-0x30]
    // 0xa3b3b8: ldur            x6, [fp, #-0x20]
    // 0xa3b3bc: ldur            x7, [fp, #-8]
    // 0xa3b3c0: ldur            x3, [fp, #-0x18]
    // 0xa3b3c4: ldur            x8, [fp, #-0x28]
    // 0xa3b3c8: r0 = format()
    //     0xa3b3c8: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xa3b3cc: ldur            x1, [fp, #-0x40]
    // 0xa3b3d0: ArrayStore: r1[1] = r0  ; List_4
    //     0xa3b3d0: add             x25, x1, #0x13
    //     0xa3b3d4: str             w0, [x25]
    //     0xa3b3d8: tbz             w0, #0, #0xa3b3f4
    //     0xa3b3dc: ldurb           w16, [x1, #-1]
    //     0xa3b3e0: ldurb           w17, [x0, #-1]
    //     0xa3b3e4: and             x16, x17, x16, lsr #2
    //     0xa3b3e8: tst             x16, HEAP, lsr #32
    //     0xa3b3ec: b.eq            #0xa3b3f4
    //     0xa3b3f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa3b3f4: ldur            x16, [fp, #-0x40]
    // 0xa3b3f8: str             x16, [SP]
    // 0xa3b3fc: r0 = _interpolate()
    //     0xa3b3fc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa3b400: stur            x0, [fp, #-0x40]
    // 0xa3b404: r0 = ProductCustomisation()
    //     0xa3b404: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xa3b408: mov             x2, x0
    // 0xa3b40c: ldur            x0, [fp, #-0x38]
    // 0xa3b410: stur            x2, [fp, #-0x48]
    // 0xa3b414: StoreField: r2->field_b = r0
    //     0xa3b414: stur            w0, [x2, #0xb]
    // 0xa3b418: ldur            x0, [fp, #-0x30]
    // 0xa3b41c: StoreField: r2->field_f = r0
    //     0xa3b41c: stur            w0, [x2, #0xf]
    // 0xa3b420: ldur            x0, [fp, #-0x20]
    // 0xa3b424: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3b424: stur            w0, [x2, #0x17]
    // 0xa3b428: ldur            x0, [fp, #-0x18]
    // 0xa3b42c: StoreField: r2->field_23 = r0
    //     0xa3b42c: stur            w0, [x2, #0x23]
    // 0xa3b430: ldur            x0, [fp, #-8]
    // 0xa3b434: StoreField: r2->field_27 = r0
    //     0xa3b434: stur            w0, [x2, #0x27]
    // 0xa3b438: ldur            x0, [fp, #-0x40]
    // 0xa3b43c: StoreField: r2->field_2b = r0
    //     0xa3b43c: stur            w0, [x2, #0x2b]
    // 0xa3b440: ldur            x1, [fp, #-0x28]
    // 0xa3b444: r0 = clear()
    //     0xa3b444: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xa3b448: ldur            x0, [fp, #-0x28]
    // 0xa3b44c: LoadField: r1 = r0->field_b
    //     0xa3b44c: ldur            w1, [x0, #0xb]
    // 0xa3b450: LoadField: r2 = r0->field_f
    //     0xa3b450: ldur            w2, [x0, #0xf]
    // 0xa3b454: DecompressPointer r2
    //     0xa3b454: add             x2, x2, HEAP, lsl #32
    // 0xa3b458: LoadField: r3 = r2->field_b
    //     0xa3b458: ldur            w3, [x2, #0xb]
    // 0xa3b45c: r2 = LoadInt32Instr(r1)
    //     0xa3b45c: sbfx            x2, x1, #1, #0x1f
    // 0xa3b460: stur            x2, [fp, #-0x50]
    // 0xa3b464: r1 = LoadInt32Instr(r3)
    //     0xa3b464: sbfx            x1, x3, #1, #0x1f
    // 0xa3b468: cmp             x2, x1
    // 0xa3b46c: b.ne            #0xa3b478
    // 0xa3b470: mov             x1, x0
    // 0xa3b474: r0 = _growToNextCapacity()
    //     0xa3b474: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa3b478: ldur            x4, [fp, #-0x10]
    // 0xa3b47c: ldur            x2, [fp, #-0x28]
    // 0xa3b480: ldur            x3, [fp, #-0x50]
    // 0xa3b484: add             x0, x3, #1
    // 0xa3b488: lsl             x1, x0, #1
    // 0xa3b48c: StoreField: r2->field_b = r1
    //     0xa3b48c: stur            w1, [x2, #0xb]
    // 0xa3b490: LoadField: r1 = r2->field_f
    //     0xa3b490: ldur            w1, [x2, #0xf]
    // 0xa3b494: DecompressPointer r1
    //     0xa3b494: add             x1, x1, HEAP, lsl #32
    // 0xa3b498: ldur            x0, [fp, #-0x48]
    // 0xa3b49c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa3b49c: add             x25, x1, x3, lsl #2
    //     0xa3b4a0: add             x25, x25, #0xf
    //     0xa3b4a4: str             w0, [x25]
    //     0xa3b4a8: tbz             w0, #0, #0xa3b4c4
    //     0xa3b4ac: ldurb           w16, [x1, #-1]
    //     0xa3b4b0: ldurb           w17, [x0, #-1]
    //     0xa3b4b4: and             x16, x17, x16, lsr #2
    //     0xa3b4b8: tst             x16, HEAP, lsr #32
    //     0xa3b4bc: b.eq            #0xa3b4c4
    //     0xa3b4c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa3b4c4: LoadField: r0 = r4->field_f
    //     0xa3b4c4: ldur            w0, [x4, #0xf]
    // 0xa3b4c8: DecompressPointer r0
    //     0xa3b4c8: add             x0, x0, HEAP, lsl #32
    // 0xa3b4cc: LoadField: r1 = r0->field_b
    //     0xa3b4cc: ldur            w1, [x0, #0xb]
    // 0xa3b4d0: DecompressPointer r1
    //     0xa3b4d0: add             x1, x1, HEAP, lsl #32
    // 0xa3b4d4: cmp             w1, NULL
    // 0xa3b4d8: b.eq            #0xa3b624
    // 0xa3b4dc: LoadField: r0 = r1->field_b
    //     0xa3b4dc: ldur            w0, [x1, #0xb]
    // 0xa3b4e0: DecompressPointer r0
    //     0xa3b4e0: add             x0, x0, HEAP, lsl #32
    // 0xa3b4e4: cmp             w0, NULL
    // 0xa3b4e8: b.ne            #0xa3b4f4
    // 0xa3b4ec: r3 = Null
    //     0xa3b4ec: mov             x3, NULL
    // 0xa3b4f0: b               #0xa3b4fc
    // 0xa3b4f4: LoadField: r3 = r0->field_23
    //     0xa3b4f4: ldur            w3, [x0, #0x23]
    // 0xa3b4f8: DecompressPointer r3
    //     0xa3b4f8: add             x3, x3, HEAP, lsl #32
    // 0xa3b4fc: cmp             w0, NULL
    // 0xa3b500: b.ne            #0xa3b50c
    // 0xa3b504: r0 = Null
    //     0xa3b504: mov             x0, NULL
    // 0xa3b508: b               #0xa3b518
    // 0xa3b50c: LoadField: r4 = r0->field_2b
    //     0xa3b50c: ldur            w4, [x0, #0x2b]
    // 0xa3b510: DecompressPointer r4
    //     0xa3b510: add             x4, x4, HEAP, lsl #32
    // 0xa3b514: mov             x0, x4
    // 0xa3b518: cmp             w0, NULL
    // 0xa3b51c: b.ne            #0xa3b524
    // 0xa3b520: r0 = false
    //     0xa3b520: add             x0, NULL, #0x30  ; false
    // 0xa3b524: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xa3b524: ldur            w4, [x1, #0x17]
    // 0xa3b528: DecompressPointer r4
    //     0xa3b528: add             x4, x4, HEAP, lsl #32
    // 0xa3b52c: ldr             x16, [fp, #0x10]
    // 0xa3b530: stp             x16, x4, [SP, #0x18]
    // 0xa3b534: stp             x2, x3, [SP, #8]
    // 0xa3b538: str             x0, [SP]
    // 0xa3b53c: r4 = 0
    //     0xa3b53c: movz            x4, #0
    // 0xa3b540: ldr             x0, [SP, #0x20]
    // 0xa3b544: r16 = UnlinkedCall_0x613b5c
    //     0xa3b544: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a450] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa3b548: add             x16, x16, #0x450
    // 0xa3b54c: ldp             x5, lr, [x16]
    // 0xa3b550: blr             lr
    // 0xa3b554: b               #0xa3b5f8
    // 0xa3b558: ldur            x4, [fp, #-0x10]
    // 0xa3b55c: mov             x2, x1
    // 0xa3b560: mov             x1, x2
    // 0xa3b564: r0 = clear()
    //     0xa3b564: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xa3b568: ldur            x0, [fp, #-0x10]
    // 0xa3b56c: LoadField: r1 = r0->field_f
    //     0xa3b56c: ldur            w1, [x0, #0xf]
    // 0xa3b570: DecompressPointer r1
    //     0xa3b570: add             x1, x1, HEAP, lsl #32
    // 0xa3b574: LoadField: r0 = r1->field_b
    //     0xa3b574: ldur            w0, [x1, #0xb]
    // 0xa3b578: DecompressPointer r0
    //     0xa3b578: add             x0, x0, HEAP, lsl #32
    // 0xa3b57c: cmp             w0, NULL
    // 0xa3b580: b.eq            #0xa3b628
    // 0xa3b584: LoadField: r1 = r0->field_b
    //     0xa3b584: ldur            w1, [x0, #0xb]
    // 0xa3b588: DecompressPointer r1
    //     0xa3b588: add             x1, x1, HEAP, lsl #32
    // 0xa3b58c: cmp             w1, NULL
    // 0xa3b590: b.ne            #0xa3b59c
    // 0xa3b594: r2 = Null
    //     0xa3b594: mov             x2, NULL
    // 0xa3b598: b               #0xa3b5a4
    // 0xa3b59c: LoadField: r2 = r1->field_23
    //     0xa3b59c: ldur            w2, [x1, #0x23]
    // 0xa3b5a0: DecompressPointer r2
    //     0xa3b5a0: add             x2, x2, HEAP, lsl #32
    // 0xa3b5a4: cmp             w1, NULL
    // 0xa3b5a8: b.ne            #0xa3b5b4
    // 0xa3b5ac: r1 = Null
    //     0xa3b5ac: mov             x1, NULL
    // 0xa3b5b0: b               #0xa3b5c0
    // 0xa3b5b4: LoadField: r3 = r1->field_2b
    //     0xa3b5b4: ldur            w3, [x1, #0x2b]
    // 0xa3b5b8: DecompressPointer r3
    //     0xa3b5b8: add             x3, x3, HEAP, lsl #32
    // 0xa3b5bc: mov             x1, x3
    // 0xa3b5c0: cmp             w1, NULL
    // 0xa3b5c4: b.ne            #0xa3b5cc
    // 0xa3b5c8: r1 = false
    //     0xa3b5c8: add             x1, NULL, #0x30  ; false
    // 0xa3b5cc: LoadField: r3 = r0->field_1b
    //     0xa3b5cc: ldur            w3, [x0, #0x1b]
    // 0xa3b5d0: DecompressPointer r3
    //     0xa3b5d0: add             x3, x3, HEAP, lsl #32
    // 0xa3b5d4: stp             x2, x3, [SP, #0x10]
    // 0xa3b5d8: ldur            x16, [fp, #-0x28]
    // 0xa3b5dc: stp             x1, x16, [SP]
    // 0xa3b5e0: r4 = 0
    //     0xa3b5e0: movz            x4, #0
    // 0xa3b5e4: ldr             x0, [SP, #0x18]
    // 0xa3b5e8: r16 = UnlinkedCall_0x613b5c
    //     0xa3b5e8: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a460] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa3b5ec: add             x16, x16, #0x460
    // 0xa3b5f0: ldp             x5, lr, [x16]
    // 0xa3b5f4: blr             lr
    // 0xa3b5f8: r0 = Null
    //     0xa3b5f8: mov             x0, NULL
    // 0xa3b5fc: LeaveFrame
    //     0xa3b5fc: mov             SP, fp
    //     0xa3b600: ldp             fp, lr, [SP], #0x10
    // 0xa3b604: ret
    //     0xa3b604: ret             
    // 0xa3b608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3b608: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3b60c: b               #0xa3b088
    // 0xa3b610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b610: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b614: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b618: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b61c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b61c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b620: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b624: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3b628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3b628: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbd9810, size: 0x7a0
    // 0xbd9810: EnterFrame
    //     0xbd9810: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9814: mov             fp, SP
    // 0xbd9818: AllocStack(0x88)
    //     0xbd9818: sub             SP, SP, #0x88
    // 0xbd981c: SetupParameters(_CustomisationNumberState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbd981c: mov             x0, x1
    //     0xbd9820: stur            x1, [fp, #-8]
    //     0xbd9824: mov             x1, x2
    //     0xbd9828: stur            x2, [fp, #-0x10]
    // 0xbd982c: CheckStackOverflow
    //     0xbd982c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9830: cmp             SP, x16
    //     0xbd9834: b.ls            #0xbd9f9c
    // 0xbd9838: r1 = 1
    //     0xbd9838: movz            x1, #0x1
    // 0xbd983c: r0 = AllocateContext()
    //     0xbd983c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd9840: mov             x3, x0
    // 0xbd9844: ldur            x0, [fp, #-8]
    // 0xbd9848: stur            x3, [fp, #-0x20]
    // 0xbd984c: StoreField: r3->field_f = r0
    //     0xbd984c: stur            w0, [x3, #0xf]
    // 0xbd9850: LoadField: r1 = r0->field_b
    //     0xbd9850: ldur            w1, [x0, #0xb]
    // 0xbd9854: DecompressPointer r1
    //     0xbd9854: add             x1, x1, HEAP, lsl #32
    // 0xbd9858: cmp             w1, NULL
    // 0xbd985c: b.eq            #0xbd9fa4
    // 0xbd9860: LoadField: r2 = r1->field_b
    //     0xbd9860: ldur            w2, [x1, #0xb]
    // 0xbd9864: DecompressPointer r2
    //     0xbd9864: add             x2, x2, HEAP, lsl #32
    // 0xbd9868: cmp             w2, NULL
    // 0xbd986c: b.ne            #0xbd9878
    // 0xbd9870: r1 = Null
    //     0xbd9870: mov             x1, NULL
    // 0xbd9874: b               #0xbd9880
    // 0xbd9878: LoadField: r1 = r2->field_2b
    //     0xbd9878: ldur            w1, [x2, #0x2b]
    // 0xbd987c: DecompressPointer r1
    //     0xbd987c: add             x1, x1, HEAP, lsl #32
    // 0xbd9880: cmp             w1, NULL
    // 0xbd9884: b.eq            #0xbd98e0
    // 0xbd9888: tbnz            w1, #4, #0xbd98e0
    // 0xbd988c: cmp             w2, NULL
    // 0xbd9890: b.ne            #0xbd989c
    // 0xbd9894: r4 = Null
    //     0xbd9894: mov             x4, NULL
    // 0xbd9898: b               #0xbd98a8
    // 0xbd989c: LoadField: r1 = r2->field_1b
    //     0xbd989c: ldur            w1, [x2, #0x1b]
    // 0xbd98a0: DecompressPointer r1
    //     0xbd98a0: add             x1, x1, HEAP, lsl #32
    // 0xbd98a4: mov             x4, x1
    // 0xbd98a8: stur            x4, [fp, #-0x18]
    // 0xbd98ac: r1 = Null
    //     0xbd98ac: mov             x1, NULL
    // 0xbd98b0: r2 = 4
    //     0xbd98b0: movz            x2, #0x4
    // 0xbd98b4: r0 = AllocateArray()
    //     0xbd98b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd98b8: mov             x1, x0
    // 0xbd98bc: ldur            x0, [fp, #-0x18]
    // 0xbd98c0: StoreField: r1->field_f = r0
    //     0xbd98c0: stur            w0, [x1, #0xf]
    // 0xbd98c4: r16 = " *"
    //     0xbd98c4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xbd98c8: ldr             x16, [x16, #0xfc8]
    // 0xbd98cc: StoreField: r1->field_13 = r16
    //     0xbd98cc: stur            w16, [x1, #0x13]
    // 0xbd98d0: str             x1, [SP]
    // 0xbd98d4: r0 = _interpolate()
    //     0xbd98d4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd98d8: mov             x2, x0
    // 0xbd98dc: b               #0xbd9904
    // 0xbd98e0: cmp             w2, NULL
    // 0xbd98e4: b.ne            #0xbd98f0
    // 0xbd98e8: r0 = Null
    //     0xbd98e8: mov             x0, NULL
    // 0xbd98ec: b               #0xbd98f8
    // 0xbd98f0: LoadField: r0 = r2->field_1b
    //     0xbd98f0: ldur            w0, [x2, #0x1b]
    // 0xbd98f4: DecompressPointer r0
    //     0xbd98f4: add             x0, x0, HEAP, lsl #32
    // 0xbd98f8: str             x0, [SP]
    // 0xbd98fc: r0 = _interpolateSingle()
    //     0xbd98fc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbd9900: mov             x2, x0
    // 0xbd9904: ldur            x0, [fp, #-8]
    // 0xbd9908: ldur            x1, [fp, #-0x10]
    // 0xbd990c: stur            x2, [fp, #-0x18]
    // 0xbd9910: r0 = of()
    //     0xbd9910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd9914: LoadField: r1 = r0->field_87
    //     0xbd9914: ldur            w1, [x0, #0x87]
    // 0xbd9918: DecompressPointer r1
    //     0xbd9918: add             x1, x1, HEAP, lsl #32
    // 0xbd991c: LoadField: r0 = r1->field_7
    //     0xbd991c: ldur            w0, [x1, #7]
    // 0xbd9920: DecompressPointer r0
    //     0xbd9920: add             x0, x0, HEAP, lsl #32
    // 0xbd9924: stur            x0, [fp, #-0x28]
    // 0xbd9928: r1 = Instance_Color
    //     0xbd9928: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd992c: d0 = 0.700000
    //     0xbd992c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd9930: ldr             d0, [x17, #0xf48]
    // 0xbd9934: r0 = withOpacity()
    //     0xbd9934: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd9938: r16 = 14.000000
    //     0xbd9938: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd993c: ldr             x16, [x16, #0x1d8]
    // 0xbd9940: stp             x0, x16, [SP]
    // 0xbd9944: ldur            x1, [fp, #-0x28]
    // 0xbd9948: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd9948: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd994c: ldr             x4, [x4, #0xaa0]
    // 0xbd9950: r0 = copyWith()
    //     0xbd9950: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd9954: stur            x0, [fp, #-0x28]
    // 0xbd9958: r0 = Text()
    //     0xbd9958: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd995c: mov             x1, x0
    // 0xbd9960: ldur            x0, [fp, #-0x18]
    // 0xbd9964: stur            x1, [fp, #-0x30]
    // 0xbd9968: StoreField: r1->field_b = r0
    //     0xbd9968: stur            w0, [x1, #0xb]
    // 0xbd996c: ldur            x0, [fp, #-0x28]
    // 0xbd9970: StoreField: r1->field_13 = r0
    //     0xbd9970: stur            w0, [x1, #0x13]
    // 0xbd9974: r0 = Padding()
    //     0xbd9974: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd9978: mov             x3, x0
    // 0xbd997c: r0 = Instance_EdgeInsets
    //     0xbd997c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd9980: ldr             x0, [x0, #0x980]
    // 0xbd9984: stur            x3, [fp, #-0x38]
    // 0xbd9988: StoreField: r3->field_f = r0
    //     0xbd9988: stur            w0, [x3, #0xf]
    // 0xbd998c: ldur            x1, [fp, #-0x30]
    // 0xbd9990: StoreField: r3->field_b = r1
    //     0xbd9990: stur            w1, [x3, #0xb]
    // 0xbd9994: ldur            x4, [fp, #-8]
    // 0xbd9998: LoadField: r1 = r4->field_b
    //     0xbd9998: ldur            w1, [x4, #0xb]
    // 0xbd999c: DecompressPointer r1
    //     0xbd999c: add             x1, x1, HEAP, lsl #32
    // 0xbd99a0: cmp             w1, NULL
    // 0xbd99a4: b.eq            #0xbd9fa8
    // 0xbd99a8: LoadField: r5 = r1->field_b
    //     0xbd99a8: ldur            w5, [x1, #0xb]
    // 0xbd99ac: DecompressPointer r5
    //     0xbd99ac: add             x5, x5, HEAP, lsl #32
    // 0xbd99b0: stur            x5, [fp, #-0x28]
    // 0xbd99b4: cmp             w5, NULL
    // 0xbd99b8: b.ne            #0xbd99c4
    // 0xbd99bc: r1 = Null
    //     0xbd99bc: mov             x1, NULL
    // 0xbd99c0: b               #0xbd99cc
    // 0xbd99c4: LoadField: r1 = r5->field_23
    //     0xbd99c4: ldur            w1, [x5, #0x23]
    // 0xbd99c8: DecompressPointer r1
    //     0xbd99c8: add             x1, x1, HEAP, lsl #32
    // 0xbd99cc: cbnz            w1, #0xbd99d8
    // 0xbd99d0: r6 = false
    //     0xbd99d0: add             x6, NULL, #0x30  ; false
    // 0xbd99d4: b               #0xbd99dc
    // 0xbd99d8: r6 = true
    //     0xbd99d8: add             x6, NULL, #0x20  ; true
    // 0xbd99dc: stur            x6, [fp, #-0x18]
    // 0xbd99e0: r1 = Null
    //     0xbd99e0: mov             x1, NULL
    // 0xbd99e4: r2 = 4
    //     0xbd99e4: movz            x2, #0x4
    // 0xbd99e8: r0 = AllocateArray()
    //     0xbd99e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd99ec: r16 = "+ "
    //     0xbd99ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xbd99f0: ldr             x16, [x16, #0xc30]
    // 0xbd99f4: StoreField: r0->field_f = r16
    //     0xbd99f4: stur            w16, [x0, #0xf]
    // 0xbd99f8: ldur            x1, [fp, #-0x28]
    // 0xbd99fc: cmp             w1, NULL
    // 0xbd9a00: b.ne            #0xbd9a0c
    // 0xbd9a04: r1 = Null
    //     0xbd9a04: mov             x1, NULL
    // 0xbd9a08: b               #0xbd9a18
    // 0xbd9a0c: LoadField: r2 = r1->field_27
    //     0xbd9a0c: ldur            w2, [x1, #0x27]
    // 0xbd9a10: DecompressPointer r2
    //     0xbd9a10: add             x2, x2, HEAP, lsl #32
    // 0xbd9a14: mov             x1, x2
    // 0xbd9a18: cmp             w1, NULL
    // 0xbd9a1c: b.ne            #0xbd9a28
    // 0xbd9a20: r4 = " "
    //     0xbd9a20: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbd9a24: b               #0xbd9a2c
    // 0xbd9a28: mov             x4, x1
    // 0xbd9a2c: ldur            x2, [fp, #-8]
    // 0xbd9a30: ldur            x1, [fp, #-0x38]
    // 0xbd9a34: ldur            x3, [fp, #-0x18]
    // 0xbd9a38: StoreField: r0->field_13 = r4
    //     0xbd9a38: stur            w4, [x0, #0x13]
    // 0xbd9a3c: str             x0, [SP]
    // 0xbd9a40: r0 = _interpolate()
    //     0xbd9a40: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd9a44: ldur            x1, [fp, #-0x10]
    // 0xbd9a48: stur            x0, [fp, #-0x28]
    // 0xbd9a4c: r0 = of()
    //     0xbd9a4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd9a50: LoadField: r1 = r0->field_87
    //     0xbd9a50: ldur            w1, [x0, #0x87]
    // 0xbd9a54: DecompressPointer r1
    //     0xbd9a54: add             x1, x1, HEAP, lsl #32
    // 0xbd9a58: LoadField: r0 = r1->field_2b
    //     0xbd9a58: ldur            w0, [x1, #0x2b]
    // 0xbd9a5c: DecompressPointer r0
    //     0xbd9a5c: add             x0, x0, HEAP, lsl #32
    // 0xbd9a60: stur            x0, [fp, #-0x30]
    // 0xbd9a64: r1 = Instance_Color
    //     0xbd9a64: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd9a68: d0 = 0.700000
    //     0xbd9a68: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd9a6c: ldr             d0, [x17, #0xf48]
    // 0xbd9a70: r0 = withOpacity()
    //     0xbd9a70: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd9a74: r16 = 12.000000
    //     0xbd9a74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd9a78: ldr             x16, [x16, #0x9e8]
    // 0xbd9a7c: stp             x0, x16, [SP]
    // 0xbd9a80: ldur            x1, [fp, #-0x30]
    // 0xbd9a84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd9a84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd9a88: ldr             x4, [x4, #0xaa0]
    // 0xbd9a8c: r0 = copyWith()
    //     0xbd9a8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd9a90: stur            x0, [fp, #-0x30]
    // 0xbd9a94: r0 = Text()
    //     0xbd9a94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd9a98: mov             x1, x0
    // 0xbd9a9c: ldur            x0, [fp, #-0x28]
    // 0xbd9aa0: stur            x1, [fp, #-0x40]
    // 0xbd9aa4: StoreField: r1->field_b = r0
    //     0xbd9aa4: stur            w0, [x1, #0xb]
    // 0xbd9aa8: ldur            x0, [fp, #-0x30]
    // 0xbd9aac: StoreField: r1->field_13 = r0
    //     0xbd9aac: stur            w0, [x1, #0x13]
    // 0xbd9ab0: r0 = Visibility()
    //     0xbd9ab0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd9ab4: mov             x1, x0
    // 0xbd9ab8: ldur            x0, [fp, #-0x40]
    // 0xbd9abc: stur            x1, [fp, #-0x28]
    // 0xbd9ac0: StoreField: r1->field_b = r0
    //     0xbd9ac0: stur            w0, [x1, #0xb]
    // 0xbd9ac4: r0 = Instance_SizedBox
    //     0xbd9ac4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd9ac8: StoreField: r1->field_f = r0
    //     0xbd9ac8: stur            w0, [x1, #0xf]
    // 0xbd9acc: ldur            x2, [fp, #-0x18]
    // 0xbd9ad0: StoreField: r1->field_13 = r2
    //     0xbd9ad0: stur            w2, [x1, #0x13]
    // 0xbd9ad4: r2 = false
    //     0xbd9ad4: add             x2, NULL, #0x30  ; false
    // 0xbd9ad8: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd9ad8: stur            w2, [x1, #0x17]
    // 0xbd9adc: StoreField: r1->field_1b = r2
    //     0xbd9adc: stur            w2, [x1, #0x1b]
    // 0xbd9ae0: StoreField: r1->field_1f = r2
    //     0xbd9ae0: stur            w2, [x1, #0x1f]
    // 0xbd9ae4: StoreField: r1->field_23 = r2
    //     0xbd9ae4: stur            w2, [x1, #0x23]
    // 0xbd9ae8: StoreField: r1->field_27 = r2
    //     0xbd9ae8: stur            w2, [x1, #0x27]
    // 0xbd9aec: StoreField: r1->field_2b = r2
    //     0xbd9aec: stur            w2, [x1, #0x2b]
    // 0xbd9af0: r0 = Padding()
    //     0xbd9af0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd9af4: mov             x3, x0
    // 0xbd9af8: r0 = Instance_EdgeInsets
    //     0xbd9af8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd9afc: ldr             x0, [x0, #0x980]
    // 0xbd9b00: stur            x3, [fp, #-0x18]
    // 0xbd9b04: StoreField: r3->field_f = r0
    //     0xbd9b04: stur            w0, [x3, #0xf]
    // 0xbd9b08: ldur            x0, [fp, #-0x28]
    // 0xbd9b0c: StoreField: r3->field_b = r0
    //     0xbd9b0c: stur            w0, [x3, #0xb]
    // 0xbd9b10: r1 = Null
    //     0xbd9b10: mov             x1, NULL
    // 0xbd9b14: r2 = 6
    //     0xbd9b14: movz            x2, #0x6
    // 0xbd9b18: r0 = AllocateArray()
    //     0xbd9b18: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd9b1c: mov             x2, x0
    // 0xbd9b20: ldur            x0, [fp, #-0x38]
    // 0xbd9b24: stur            x2, [fp, #-0x28]
    // 0xbd9b28: StoreField: r2->field_f = r0
    //     0xbd9b28: stur            w0, [x2, #0xf]
    // 0xbd9b2c: r16 = Instance_Spacer
    //     0xbd9b2c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd9b30: ldr             x16, [x16, #0xf0]
    // 0xbd9b34: StoreField: r2->field_13 = r16
    //     0xbd9b34: stur            w16, [x2, #0x13]
    // 0xbd9b38: ldur            x0, [fp, #-0x18]
    // 0xbd9b3c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd9b3c: stur            w0, [x2, #0x17]
    // 0xbd9b40: r1 = <Widget>
    //     0xbd9b40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd9b44: r0 = AllocateGrowableArray()
    //     0xbd9b44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd9b48: mov             x1, x0
    // 0xbd9b4c: ldur            x0, [fp, #-0x28]
    // 0xbd9b50: stur            x1, [fp, #-0x18]
    // 0xbd9b54: StoreField: r1->field_f = r0
    //     0xbd9b54: stur            w0, [x1, #0xf]
    // 0xbd9b58: r2 = 6
    //     0xbd9b58: movz            x2, #0x6
    // 0xbd9b5c: StoreField: r1->field_b = r2
    //     0xbd9b5c: stur            w2, [x1, #0xb]
    // 0xbd9b60: r0 = Row()
    //     0xbd9b60: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd9b64: mov             x2, x0
    // 0xbd9b68: r0 = Instance_Axis
    //     0xbd9b68: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd9b6c: stur            x2, [fp, #-0x30]
    // 0xbd9b70: StoreField: r2->field_f = r0
    //     0xbd9b70: stur            w0, [x2, #0xf]
    // 0xbd9b74: r0 = Instance_MainAxisAlignment
    //     0xbd9b74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd9b78: ldr             x0, [x0, #0xa08]
    // 0xbd9b7c: StoreField: r2->field_13 = r0
    //     0xbd9b7c: stur            w0, [x2, #0x13]
    // 0xbd9b80: r3 = Instance_MainAxisSize
    //     0xbd9b80: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd9b84: ldr             x3, [x3, #0xa10]
    // 0xbd9b88: ArrayStore: r2[0] = r3  ; List_4
    //     0xbd9b88: stur            w3, [x2, #0x17]
    // 0xbd9b8c: r1 = Instance_CrossAxisAlignment
    //     0xbd9b8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd9b90: ldr             x1, [x1, #0xa18]
    // 0xbd9b94: StoreField: r2->field_1b = r1
    //     0xbd9b94: stur            w1, [x2, #0x1b]
    // 0xbd9b98: r4 = Instance_VerticalDirection
    //     0xbd9b98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd9b9c: ldr             x4, [x4, #0xa20]
    // 0xbd9ba0: StoreField: r2->field_23 = r4
    //     0xbd9ba0: stur            w4, [x2, #0x23]
    // 0xbd9ba4: r5 = Instance_Clip
    //     0xbd9ba4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd9ba8: ldr             x5, [x5, #0x38]
    // 0xbd9bac: StoreField: r2->field_2b = r5
    //     0xbd9bac: stur            w5, [x2, #0x2b]
    // 0xbd9bb0: StoreField: r2->field_2f = rZR
    //     0xbd9bb0: stur            xzr, [x2, #0x2f]
    // 0xbd9bb4: ldur            x1, [fp, #-0x18]
    // 0xbd9bb8: StoreField: r2->field_b = r1
    //     0xbd9bb8: stur            w1, [x2, #0xb]
    // 0xbd9bbc: ldur            x6, [fp, #-8]
    // 0xbd9bc0: LoadField: r1 = r6->field_b
    //     0xbd9bc0: ldur            w1, [x6, #0xb]
    // 0xbd9bc4: DecompressPointer r1
    //     0xbd9bc4: add             x1, x1, HEAP, lsl #32
    // 0xbd9bc8: cmp             w1, NULL
    // 0xbd9bcc: b.eq            #0xbd9fac
    // 0xbd9bd0: LoadField: r7 = r1->field_b
    //     0xbd9bd0: ldur            w7, [x1, #0xb]
    // 0xbd9bd4: DecompressPointer r7
    //     0xbd9bd4: add             x7, x7, HEAP, lsl #32
    // 0xbd9bd8: cmp             w7, NULL
    // 0xbd9bdc: b.ne            #0xbd9be8
    // 0xbd9be0: r1 = Null
    //     0xbd9be0: mov             x1, NULL
    // 0xbd9be4: b               #0xbd9c14
    // 0xbd9be8: LoadField: r1 = r7->field_1f
    //     0xbd9be8: ldur            w1, [x7, #0x1f]
    // 0xbd9bec: DecompressPointer r1
    //     0xbd9bec: add             x1, x1, HEAP, lsl #32
    // 0xbd9bf0: cmp             w1, NULL
    // 0xbd9bf4: b.ne            #0xbd9c00
    // 0xbd9bf8: r1 = Null
    //     0xbd9bf8: mov             x1, NULL
    // 0xbd9bfc: b               #0xbd9c14
    // 0xbd9c00: LoadField: r8 = r1->field_7
    //     0xbd9c00: ldur            w8, [x1, #7]
    // 0xbd9c04: cbnz            w8, #0xbd9c10
    // 0xbd9c08: r1 = false
    //     0xbd9c08: add             x1, NULL, #0x30  ; false
    // 0xbd9c0c: b               #0xbd9c14
    // 0xbd9c10: r1 = true
    //     0xbd9c10: add             x1, NULL, #0x20  ; true
    // 0xbd9c14: cmp             w1, NULL
    // 0xbd9c18: b.ne            #0xbd9c24
    // 0xbd9c1c: r8 = false
    //     0xbd9c1c: add             x8, NULL, #0x30  ; false
    // 0xbd9c20: b               #0xbd9c28
    // 0xbd9c24: mov             x8, x1
    // 0xbd9c28: stur            x8, [fp, #-0x28]
    // 0xbd9c2c: cmp             w7, NULL
    // 0xbd9c30: b.ne            #0xbd9c3c
    // 0xbd9c34: r1 = Null
    //     0xbd9c34: mov             x1, NULL
    // 0xbd9c38: b               #0xbd9c44
    // 0xbd9c3c: LoadField: r1 = r7->field_1f
    //     0xbd9c3c: ldur            w1, [x7, #0x1f]
    // 0xbd9c40: DecompressPointer r1
    //     0xbd9c40: add             x1, x1, HEAP, lsl #32
    // 0xbd9c44: cmp             w1, NULL
    // 0xbd9c48: b.ne            #0xbd9c54
    // 0xbd9c4c: r7 = ""
    //     0xbd9c4c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd9c50: b               #0xbd9c58
    // 0xbd9c54: mov             x7, x1
    // 0xbd9c58: ldur            x1, [fp, #-0x10]
    // 0xbd9c5c: stur            x7, [fp, #-0x18]
    // 0xbd9c60: r0 = of()
    //     0xbd9c60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd9c64: LoadField: r1 = r0->field_87
    //     0xbd9c64: ldur            w1, [x0, #0x87]
    // 0xbd9c68: DecompressPointer r1
    //     0xbd9c68: add             x1, x1, HEAP, lsl #32
    // 0xbd9c6c: LoadField: r0 = r1->field_2b
    //     0xbd9c6c: ldur            w0, [x1, #0x2b]
    // 0xbd9c70: DecompressPointer r0
    //     0xbd9c70: add             x0, x0, HEAP, lsl #32
    // 0xbd9c74: stur            x0, [fp, #-0x38]
    // 0xbd9c78: r1 = Instance_Color
    //     0xbd9c78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd9c7c: d0 = 0.700000
    //     0xbd9c7c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd9c80: ldr             d0, [x17, #0xf48]
    // 0xbd9c84: r0 = withOpacity()
    //     0xbd9c84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd9c88: r16 = 12.000000
    //     0xbd9c88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd9c8c: ldr             x16, [x16, #0x9e8]
    // 0xbd9c90: stp             x0, x16, [SP]
    // 0xbd9c94: ldur            x1, [fp, #-0x38]
    // 0xbd9c98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd9c98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd9c9c: ldr             x4, [x4, #0xaa0]
    // 0xbd9ca0: r0 = copyWith()
    //     0xbd9ca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd9ca4: stur            x0, [fp, #-0x38]
    // 0xbd9ca8: r0 = Text()
    //     0xbd9ca8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd9cac: mov             x1, x0
    // 0xbd9cb0: ldur            x0, [fp, #-0x18]
    // 0xbd9cb4: stur            x1, [fp, #-0x40]
    // 0xbd9cb8: StoreField: r1->field_b = r0
    //     0xbd9cb8: stur            w0, [x1, #0xb]
    // 0xbd9cbc: ldur            x0, [fp, #-0x38]
    // 0xbd9cc0: StoreField: r1->field_13 = r0
    //     0xbd9cc0: stur            w0, [x1, #0x13]
    // 0xbd9cc4: r0 = Padding()
    //     0xbd9cc4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd9cc8: mov             x1, x0
    // 0xbd9ccc: r0 = Instance_EdgeInsets
    //     0xbd9ccc: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a408] Obj!EdgeInsets@d582b1
    //     0xbd9cd0: ldr             x0, [x0, #0x408]
    // 0xbd9cd4: stur            x1, [fp, #-0x18]
    // 0xbd9cd8: StoreField: r1->field_f = r0
    //     0xbd9cd8: stur            w0, [x1, #0xf]
    // 0xbd9cdc: ldur            x0, [fp, #-0x40]
    // 0xbd9ce0: StoreField: r1->field_b = r0
    //     0xbd9ce0: stur            w0, [x1, #0xb]
    // 0xbd9ce4: r0 = Visibility()
    //     0xbd9ce4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd9ce8: mov             x1, x0
    // 0xbd9cec: ldur            x0, [fp, #-0x18]
    // 0xbd9cf0: stur            x1, [fp, #-0x38]
    // 0xbd9cf4: StoreField: r1->field_b = r0
    //     0xbd9cf4: stur            w0, [x1, #0xb]
    // 0xbd9cf8: r0 = Instance_SizedBox
    //     0xbd9cf8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd9cfc: StoreField: r1->field_f = r0
    //     0xbd9cfc: stur            w0, [x1, #0xf]
    // 0xbd9d00: ldur            x0, [fp, #-0x28]
    // 0xbd9d04: StoreField: r1->field_13 = r0
    //     0xbd9d04: stur            w0, [x1, #0x13]
    // 0xbd9d08: r0 = false
    //     0xbd9d08: add             x0, NULL, #0x30  ; false
    // 0xbd9d0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd9d0c: stur            w0, [x1, #0x17]
    // 0xbd9d10: StoreField: r1->field_1b = r0
    //     0xbd9d10: stur            w0, [x1, #0x1b]
    // 0xbd9d14: StoreField: r1->field_1f = r0
    //     0xbd9d14: stur            w0, [x1, #0x1f]
    // 0xbd9d18: StoreField: r1->field_23 = r0
    //     0xbd9d18: stur            w0, [x1, #0x23]
    // 0xbd9d1c: StoreField: r1->field_27 = r0
    //     0xbd9d1c: stur            w0, [x1, #0x27]
    // 0xbd9d20: StoreField: r1->field_2b = r0
    //     0xbd9d20: stur            w0, [x1, #0x2b]
    // 0xbd9d24: ldur            x0, [fp, #-8]
    // 0xbd9d28: LoadField: r2 = r0->field_13
    //     0xbd9d28: ldur            w2, [x0, #0x13]
    // 0xbd9d2c: DecompressPointer r2
    //     0xbd9d2c: add             x2, x2, HEAP, lsl #32
    // 0xbd9d30: stur            x2, [fp, #-0x18]
    // 0xbd9d34: r0 = LengthLimitingTextInputFormatter()
    //     0xbd9d34: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbd9d38: mov             x1, x0
    // 0xbd9d3c: r0 = 60
    //     0xbd9d3c: movz            x0, #0x3c
    // 0xbd9d40: stur            x1, [fp, #-0x28]
    // 0xbd9d44: StoreField: r1->field_7 = r0
    //     0xbd9d44: stur            w0, [x1, #7]
    // 0xbd9d48: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbd9d48: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd9d4c: ldr             x0, [x0, #0x1530]
    //     0xbd9d50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd9d54: cmp             w0, w16
    //     0xbd9d58: b.ne            #0xbd9d68
    //     0xbd9d5c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbd9d60: ldr             x2, [x2, #0x120]
    //     0xbd9d64: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd9d68: r1 = Null
    //     0xbd9d68: mov             x1, NULL
    // 0xbd9d6c: r2 = 4
    //     0xbd9d6c: movz            x2, #0x4
    // 0xbd9d70: stur            x0, [fp, #-0x40]
    // 0xbd9d74: r0 = AllocateArray()
    //     0xbd9d74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd9d78: mov             x2, x0
    // 0xbd9d7c: ldur            x0, [fp, #-0x28]
    // 0xbd9d80: stur            x2, [fp, #-0x48]
    // 0xbd9d84: StoreField: r2->field_f = r0
    //     0xbd9d84: stur            w0, [x2, #0xf]
    // 0xbd9d88: ldur            x0, [fp, #-0x40]
    // 0xbd9d8c: StoreField: r2->field_13 = r0
    //     0xbd9d8c: stur            w0, [x2, #0x13]
    // 0xbd9d90: r1 = <TextInputFormatter>
    //     0xbd9d90: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbd9d94: ldr             x1, [x1, #0x7b0]
    // 0xbd9d98: r0 = AllocateGrowableArray()
    //     0xbd9d98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd9d9c: mov             x2, x0
    // 0xbd9da0: ldur            x0, [fp, #-0x48]
    // 0xbd9da4: stur            x2, [fp, #-0x28]
    // 0xbd9da8: StoreField: r2->field_f = r0
    //     0xbd9da8: stur            w0, [x2, #0xf]
    // 0xbd9dac: r0 = 4
    //     0xbd9dac: movz            x0, #0x4
    // 0xbd9db0: StoreField: r2->field_b = r0
    //     0xbd9db0: stur            w0, [x2, #0xb]
    // 0xbd9db4: ldur            x1, [fp, #-0x10]
    // 0xbd9db8: r0 = of()
    //     0xbd9db8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd9dbc: LoadField: r1 = r0->field_87
    //     0xbd9dbc: ldur            w1, [x0, #0x87]
    // 0xbd9dc0: DecompressPointer r1
    //     0xbd9dc0: add             x1, x1, HEAP, lsl #32
    // 0xbd9dc4: LoadField: r0 = r1->field_2b
    //     0xbd9dc4: ldur            w0, [x1, #0x2b]
    // 0xbd9dc8: DecompressPointer r0
    //     0xbd9dc8: add             x0, x0, HEAP, lsl #32
    // 0xbd9dcc: ldur            x1, [fp, #-0x10]
    // 0xbd9dd0: stur            x0, [fp, #-0x40]
    // 0xbd9dd4: r0 = of()
    //     0xbd9dd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd9dd8: LoadField: r1 = r0->field_5b
    //     0xbd9dd8: ldur            w1, [x0, #0x5b]
    // 0xbd9ddc: DecompressPointer r1
    //     0xbd9ddc: add             x1, x1, HEAP, lsl #32
    // 0xbd9de0: r16 = 12.000000
    //     0xbd9de0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd9de4: ldr             x16, [x16, #0x9e8]
    // 0xbd9de8: stp             x1, x16, [SP]
    // 0xbd9dec: ldur            x1, [fp, #-0x40]
    // 0xbd9df0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd9df0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd9df4: ldr             x4, [x4, #0xaa0]
    // 0xbd9df8: r0 = copyWith()
    //     0xbd9df8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd9dfc: mov             x2, x0
    // 0xbd9e00: ldur            x0, [fp, #-8]
    // 0xbd9e04: stur            x2, [fp, #-0x48]
    // 0xbd9e08: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbd9e08: ldur            w3, [x0, #0x17]
    // 0xbd9e0c: DecompressPointer r3
    //     0xbd9e0c: add             x3, x3, HEAP, lsl #32
    // 0xbd9e10: ldur            x1, [fp, #-0x10]
    // 0xbd9e14: stur            x3, [fp, #-0x40]
    // 0xbd9e18: r0 = getTextFormFieldInputDecoration()
    //     0xbd9e18: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbd9e1c: r16 = Instance_EdgeInsets
    //     0xbd9e1c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbd9e20: ldr             x16, [x16, #0xc40]
    // 0xbd9e24: str             x16, [SP]
    // 0xbd9e28: mov             x1, x0
    // 0xbd9e2c: r4 = const [0, 0x2, 0x1, 0x1, contentPadding, 0x1, null]
    //     0xbd9e2c: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a410] List(7) [0, 0x2, 0x1, 0x1, "contentPadding", 0x1, Null]
    //     0xbd9e30: ldr             x4, [x4, #0x410]
    // 0xbd9e34: r0 = copyWith()
    //     0xbd9e34: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbd9e38: ldur            x2, [fp, #-0x20]
    // 0xbd9e3c: r1 = Function '<anonymous closure>':.
    //     0xbd9e3c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a418] AnonymousClosure: (0xa3b060), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xbd9e40: ldr             x1, [x1, #0x418]
    // 0xbd9e44: stur            x0, [fp, #-8]
    // 0xbd9e48: r0 = AllocateClosure()
    //     0xbd9e48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd9e4c: r1 = <String>
    //     0xbd9e4c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbd9e50: stur            x0, [fp, #-0x10]
    // 0xbd9e54: r0 = TextFormField()
    //     0xbd9e54: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbd9e58: stur            x0, [fp, #-0x20]
    // 0xbd9e5c: r16 = false
    //     0xbd9e5c: add             x16, NULL, #0x30  ; false
    // 0xbd9e60: r30 = Instance_AutovalidateMode
    //     0xbd9e60: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbd9e64: ldr             lr, [lr, #0x7e8]
    // 0xbd9e68: stp             lr, x16, [SP, #0x30]
    // 0xbd9e6c: ldur            x16, [fp, #-0x28]
    // 0xbd9e70: r30 = Instance_TextInputType
    //     0xbd9e70: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6a420] Obj!TextInputType@d55bc1
    //     0xbd9e74: ldr             lr, [lr, #0x420]
    // 0xbd9e78: stp             lr, x16, [SP, #0x20]
    // 0xbd9e7c: r16 = 2
    //     0xbd9e7c: movz            x16, #0x2
    // 0xbd9e80: ldur            lr, [fp, #-0x48]
    // 0xbd9e84: stp             lr, x16, [SP, #0x10]
    // 0xbd9e88: ldur            x16, [fp, #-0x40]
    // 0xbd9e8c: ldur            lr, [fp, #-0x10]
    // 0xbd9e90: stp             lr, x16, [SP]
    // 0xbd9e94: mov             x1, x0
    // 0xbd9e98: ldur            x2, [fp, #-8]
    // 0xbd9e9c: r4 = const [0, 0xa, 0x8, 0x2, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x6, onChanged, 0x9, style, 0x7, null]
    //     0xbd9e9c: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a428] List(21) [0, 0xa, 0x8, 0x2, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x6, "onChanged", 0x9, "style", 0x7, Null]
    //     0xbd9ea0: ldr             x4, [x4, #0x428]
    // 0xbd9ea4: r0 = TextFormField()
    //     0xbd9ea4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbd9ea8: r0 = Form()
    //     0xbd9ea8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbd9eac: mov             x1, x0
    // 0xbd9eb0: ldur            x0, [fp, #-0x20]
    // 0xbd9eb4: stur            x1, [fp, #-8]
    // 0xbd9eb8: StoreField: r1->field_b = r0
    //     0xbd9eb8: stur            w0, [x1, #0xb]
    // 0xbd9ebc: r0 = Instance_AutovalidateMode
    //     0xbd9ebc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbd9ec0: ldr             x0, [x0, #0x800]
    // 0xbd9ec4: StoreField: r1->field_23 = r0
    //     0xbd9ec4: stur            w0, [x1, #0x23]
    // 0xbd9ec8: ldur            x0, [fp, #-0x18]
    // 0xbd9ecc: StoreField: r1->field_7 = r0
    //     0xbd9ecc: stur            w0, [x1, #7]
    // 0xbd9ed0: r0 = Padding()
    //     0xbd9ed0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd9ed4: mov             x3, x0
    // 0xbd9ed8: r0 = Instance_EdgeInsets
    //     0xbd9ed8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbd9edc: ldr             x0, [x0, #0x668]
    // 0xbd9ee0: stur            x3, [fp, #-0x10]
    // 0xbd9ee4: StoreField: r3->field_f = r0
    //     0xbd9ee4: stur            w0, [x3, #0xf]
    // 0xbd9ee8: ldur            x0, [fp, #-8]
    // 0xbd9eec: StoreField: r3->field_b = r0
    //     0xbd9eec: stur            w0, [x3, #0xb]
    // 0xbd9ef0: r1 = Null
    //     0xbd9ef0: mov             x1, NULL
    // 0xbd9ef4: r2 = 6
    //     0xbd9ef4: movz            x2, #0x6
    // 0xbd9ef8: r0 = AllocateArray()
    //     0xbd9ef8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd9efc: mov             x2, x0
    // 0xbd9f00: ldur            x0, [fp, #-0x30]
    // 0xbd9f04: stur            x2, [fp, #-8]
    // 0xbd9f08: StoreField: r2->field_f = r0
    //     0xbd9f08: stur            w0, [x2, #0xf]
    // 0xbd9f0c: ldur            x0, [fp, #-0x38]
    // 0xbd9f10: StoreField: r2->field_13 = r0
    //     0xbd9f10: stur            w0, [x2, #0x13]
    // 0xbd9f14: ldur            x0, [fp, #-0x10]
    // 0xbd9f18: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd9f18: stur            w0, [x2, #0x17]
    // 0xbd9f1c: r1 = <Widget>
    //     0xbd9f1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd9f20: r0 = AllocateGrowableArray()
    //     0xbd9f20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd9f24: mov             x1, x0
    // 0xbd9f28: ldur            x0, [fp, #-8]
    // 0xbd9f2c: stur            x1, [fp, #-0x10]
    // 0xbd9f30: StoreField: r1->field_f = r0
    //     0xbd9f30: stur            w0, [x1, #0xf]
    // 0xbd9f34: r0 = 6
    //     0xbd9f34: movz            x0, #0x6
    // 0xbd9f38: StoreField: r1->field_b = r0
    //     0xbd9f38: stur            w0, [x1, #0xb]
    // 0xbd9f3c: r0 = Column()
    //     0xbd9f3c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbd9f40: r1 = Instance_Axis
    //     0xbd9f40: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd9f44: StoreField: r0->field_f = r1
    //     0xbd9f44: stur            w1, [x0, #0xf]
    // 0xbd9f48: r1 = Instance_MainAxisAlignment
    //     0xbd9f48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd9f4c: ldr             x1, [x1, #0xa08]
    // 0xbd9f50: StoreField: r0->field_13 = r1
    //     0xbd9f50: stur            w1, [x0, #0x13]
    // 0xbd9f54: r1 = Instance_MainAxisSize
    //     0xbd9f54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd9f58: ldr             x1, [x1, #0xa10]
    // 0xbd9f5c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd9f5c: stur            w1, [x0, #0x17]
    // 0xbd9f60: r1 = Instance_CrossAxisAlignment
    //     0xbd9f60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbd9f64: ldr             x1, [x1, #0x890]
    // 0xbd9f68: StoreField: r0->field_1b = r1
    //     0xbd9f68: stur            w1, [x0, #0x1b]
    // 0xbd9f6c: r1 = Instance_VerticalDirection
    //     0xbd9f6c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd9f70: ldr             x1, [x1, #0xa20]
    // 0xbd9f74: StoreField: r0->field_23 = r1
    //     0xbd9f74: stur            w1, [x0, #0x23]
    // 0xbd9f78: r1 = Instance_Clip
    //     0xbd9f78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd9f7c: ldr             x1, [x1, #0x38]
    // 0xbd9f80: StoreField: r0->field_2b = r1
    //     0xbd9f80: stur            w1, [x0, #0x2b]
    // 0xbd9f84: StoreField: r0->field_2f = rZR
    //     0xbd9f84: stur            xzr, [x0, #0x2f]
    // 0xbd9f88: ldur            x1, [fp, #-0x10]
    // 0xbd9f8c: StoreField: r0->field_b = r1
    //     0xbd9f8c: stur            w1, [x0, #0xb]
    // 0xbd9f90: LeaveFrame
    //     0xbd9f90: mov             SP, fp
    //     0xbd9f94: ldp             fp, lr, [SP], #0x10
    // 0xbd9f98: ret
    //     0xbd9f98: ret             
    // 0xbd9f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9f9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9fa0: b               #0xbd9838
    // 0xbd9fa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd9fa4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd9fa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd9fa8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd9fac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd9fac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4002, size: 0x20, field offset: 0xc
//   const constructor, 
class CustomisationNumber extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8086c, size: 0x48
    // 0xc8086c: EnterFrame
    //     0xc8086c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80870: mov             fp, SP
    // 0xc80874: AllocStack(0x8)
    //     0xc80874: sub             SP, SP, #8
    // 0xc80878: CheckStackOverflow
    //     0xc80878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc8087c: cmp             SP, x16
    //     0xc80880: b.ls            #0xc808ac
    // 0xc80884: r1 = <CustomisationNumber>
    //     0xc80884: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c30] TypeArguments: <CustomisationNumber>
    //     0xc80888: ldr             x1, [x1, #0xc30]
    // 0xc8088c: r0 = _CustomisationNumberState()
    //     0xc8088c: bl              #0xc808b4  ; Allocate_CustomisationNumberStateStub -> _CustomisationNumberState (size=0x1c)
    // 0xc80890: mov             x1, x0
    // 0xc80894: stur            x0, [fp, #-8]
    // 0xc80898: r0 = _CustomisationTextState()
    //     0xc80898: bl              #0xc7b760  ; [package:customer_app/app/presentation/views/basic/customization/customisation_text.dart] _CustomisationTextState::_CustomisationTextState
    // 0xc8089c: ldur            x0, [fp, #-8]
    // 0xc808a0: LeaveFrame
    //     0xc808a0: mov             SP, fp
    //     0xc808a4: ldp             fp, lr, [SP], #0x10
    // 0xc808a8: ret
    //     0xc808a8: ret             
    // 0xc808ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc808ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc808b0: b               #0xc80884
  }
}
