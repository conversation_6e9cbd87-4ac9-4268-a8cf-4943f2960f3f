// lib: , url: package:customer_app/app/presentation/custom_widgets/orders/bumper_coupon_timer_widget.dart

// class id: 1049070, size: 0x8
class :: {
}

// class id: 3588, size: 0x14, field offset: 0x14
class _BumperCouponTimerWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x98f650, size: 0xfc4
    // 0x98f650: EnterFrame
    //     0x98f650: stp             fp, lr, [SP, #-0x10]!
    //     0x98f654: mov             fp, SP
    // 0x98f658: AllocStack(0x80)
    //     0x98f658: sub             SP, SP, #0x80
    // 0x98f65c: SetupParameters(_BumperCouponTimerWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x98f65c: mov             x0, x1
    //     0x98f660: stur            x1, [fp, #-8]
    //     0x98f664: mov             x1, x2
    //     0x98f668: stur            x2, [fp, #-0x10]
    // 0x98f66c: CheckStackOverflow
    //     0x98f66c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98f670: cmp             SP, x16
    //     0x98f674: b.ls            #0x990600
    // 0x98f678: r1 = 2
    //     0x98f678: movz            x1, #0x2
    // 0x98f67c: r0 = AllocateContext()
    //     0x98f67c: bl              #0x16f6108  ; AllocateContextStub
    // 0x98f680: mov             x1, x0
    // 0x98f684: ldur            x0, [fp, #-8]
    // 0x98f688: stur            x1, [fp, #-0x28]
    // 0x98f68c: StoreField: r1->field_f = r0
    //     0x98f68c: stur            w0, [x1, #0xf]
    // 0x98f690: ldur            x2, [fp, #-0x10]
    // 0x98f694: StoreField: r1->field_13 = r2
    //     0x98f694: stur            w2, [x1, #0x13]
    // 0x98f698: LoadField: r3 = r0->field_b
    //     0x98f698: ldur            w3, [x0, #0xb]
    // 0x98f69c: DecompressPointer r3
    //     0x98f69c: add             x3, x3, HEAP, lsl #32
    // 0x98f6a0: stur            x3, [fp, #-0x20]
    // 0x98f6a4: cmp             w3, NULL
    // 0x98f6a8: b.eq            #0x990608
    // 0x98f6ac: LoadField: r4 = r3->field_23
    //     0x98f6ac: ldur            w4, [x3, #0x23]
    // 0x98f6b0: DecompressPointer r4
    //     0x98f6b0: add             x4, x4, HEAP, lsl #32
    // 0x98f6b4: stur            x4, [fp, #-0x18]
    // 0x98f6b8: ArrayLoad: d0 = r3[0]  ; List_8
    //     0x98f6b8: ldur            d0, [x3, #0x17]
    // 0x98f6bc: stur            d0, [fp, #-0x68]
    // 0x98f6c0: r0 = Radius()
    //     0x98f6c0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x98f6c4: ldur            d0, [fp, #-0x68]
    // 0x98f6c8: stur            x0, [fp, #-0x30]
    // 0x98f6cc: StoreField: r0->field_7 = d0
    //     0x98f6cc: stur            d0, [x0, #7]
    // 0x98f6d0: StoreField: r0->field_f = d0
    //     0x98f6d0: stur            d0, [x0, #0xf]
    // 0x98f6d4: r0 = BorderRadius()
    //     0x98f6d4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x98f6d8: mov             x1, x0
    // 0x98f6dc: ldur            x0, [fp, #-0x30]
    // 0x98f6e0: stur            x1, [fp, #-0x38]
    // 0x98f6e4: StoreField: r1->field_7 = r0
    //     0x98f6e4: stur            w0, [x1, #7]
    // 0x98f6e8: StoreField: r1->field_b = r0
    //     0x98f6e8: stur            w0, [x1, #0xb]
    // 0x98f6ec: StoreField: r1->field_f = r0
    //     0x98f6ec: stur            w0, [x1, #0xf]
    // 0x98f6f0: StoreField: r1->field_13 = r0
    //     0x98f6f0: stur            w0, [x1, #0x13]
    // 0x98f6f4: ldur            x0, [fp, #-0x20]
    // 0x98f6f8: LoadField: r2 = r0->field_1f
    //     0x98f6f8: ldur            w2, [x0, #0x1f]
    // 0x98f6fc: DecompressPointer r2
    //     0x98f6fc: add             x2, x2, HEAP, lsl #32
    // 0x98f700: stur            x2, [fp, #-0x30]
    // 0x98f704: r0 = BoxDecoration()
    //     0x98f704: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x98f708: mov             x1, x0
    // 0x98f70c: ldur            x0, [fp, #-0x30]
    // 0x98f710: stur            x1, [fp, #-0x40]
    // 0x98f714: StoreField: r1->field_7 = r0
    //     0x98f714: stur            w0, [x1, #7]
    // 0x98f718: ldur            x0, [fp, #-0x18]
    // 0x98f71c: StoreField: r1->field_f = r0
    //     0x98f71c: stur            w0, [x1, #0xf]
    // 0x98f720: ldur            x0, [fp, #-0x38]
    // 0x98f724: StoreField: r1->field_13 = r0
    //     0x98f724: stur            w0, [x1, #0x13]
    // 0x98f728: r0 = Instance_BoxShape
    //     0x98f728: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x98f72c: ldr             x0, [x0, #0x80]
    // 0x98f730: StoreField: r1->field_23 = r0
    //     0x98f730: stur            w0, [x1, #0x23]
    // 0x98f734: r0 = Radius()
    //     0x98f734: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x98f738: ldur            d0, [fp, #-0x68]
    // 0x98f73c: stur            x0, [fp, #-0x18]
    // 0x98f740: StoreField: r0->field_7 = d0
    //     0x98f740: stur            d0, [x0, #7]
    // 0x98f744: StoreField: r0->field_f = d0
    //     0x98f744: stur            d0, [x0, #0xf]
    // 0x98f748: r0 = Radius()
    //     0x98f748: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x98f74c: ldur            d0, [fp, #-0x68]
    // 0x98f750: stur            x0, [fp, #-0x30]
    // 0x98f754: StoreField: r0->field_7 = d0
    //     0x98f754: stur            d0, [x0, #7]
    // 0x98f758: StoreField: r0->field_f = d0
    //     0x98f758: stur            d0, [x0, #0xf]
    // 0x98f75c: r0 = BorderRadius()
    //     0x98f75c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x98f760: mov             x1, x0
    // 0x98f764: ldur            x0, [fp, #-0x18]
    // 0x98f768: stur            x1, [fp, #-0x38]
    // 0x98f76c: StoreField: r1->field_7 = r0
    //     0x98f76c: stur            w0, [x1, #7]
    // 0x98f770: ldur            x0, [fp, #-0x30]
    // 0x98f774: StoreField: r1->field_b = r0
    //     0x98f774: stur            w0, [x1, #0xb]
    // 0x98f778: r0 = Instance_Radius
    //     0x98f778: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b48] Obj!Radius@d6be01
    //     0x98f77c: ldr             x0, [x0, #0xb48]
    // 0x98f780: StoreField: r1->field_f = r0
    //     0x98f780: stur            w0, [x1, #0xf]
    // 0x98f784: StoreField: r1->field_13 = r0
    //     0x98f784: stur            w0, [x1, #0x13]
    // 0x98f788: ldur            x0, [fp, #-0x20]
    // 0x98f78c: LoadField: r2 = r0->field_b
    //     0x98f78c: ldur            w2, [x0, #0xb]
    // 0x98f790: DecompressPointer r2
    //     0x98f790: add             x2, x2, HEAP, lsl #32
    // 0x98f794: cmp             w2, NULL
    // 0x98f798: b.ne            #0x98f7b8
    // 0x98f79c: r2 = 4
    //     0x98f79c: movz            x2, #0x4
    // 0x98f7a0: r1 = Instance_ColorSpace
    //     0x98f7a0: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x98f7a4: d2 = 255.000000
    //     0x98f7a4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x98f7a8: d0 = 1.000000
    //     0x98f7a8: fmov            d0, #1.00000000
    // 0x98f7ac: d1 = 0.700000
    //     0x98f7ac: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98f7b0: ldr             d1, [x17, #0xf48]
    // 0x98f7b4: b               #0x98fb30
    // 0x98f7b8: LoadField: r3 = r2->field_b
    //     0x98f7b8: ldur            w3, [x2, #0xb]
    // 0x98f7bc: DecompressPointer r3
    //     0x98f7bc: add             x3, x3, HEAP, lsl #32
    // 0x98f7c0: cmp             w3, NULL
    // 0x98f7c4: b.eq            #0x98fb18
    // 0x98f7c8: LoadField: r0 = r2->field_4b
    //     0x98f7c8: ldur            w0, [x2, #0x4b]
    // 0x98f7cc: DecompressPointer r0
    //     0x98f7cc: add             x0, x0, HEAP, lsl #32
    // 0x98f7d0: stur            x0, [fp, #-0x18]
    // 0x98f7d4: cmp             w0, NULL
    // 0x98f7d8: b.ne            #0x98f7e4
    // 0x98f7dc: r2 = Null
    //     0x98f7dc: mov             x2, NULL
    // 0x98f7e0: b               #0x98f808
    // 0x98f7e4: LoadField: r2 = r0->field_13
    //     0x98f7e4: ldur            w2, [x0, #0x13]
    // 0x98f7e8: DecompressPointer r2
    //     0x98f7e8: add             x2, x2, HEAP, lsl #32
    // 0x98f7ec: cmp             w2, NULL
    // 0x98f7f0: b.ne            #0x98f7fc
    // 0x98f7f4: r2 = Null
    //     0x98f7f4: mov             x2, NULL
    // 0x98f7f8: b               #0x98f808
    // 0x98f7fc: LoadField: r3 = r2->field_7
    //     0x98f7fc: ldur            w3, [x2, #7]
    // 0x98f800: DecompressPointer r3
    //     0x98f800: add             x3, x3, HEAP, lsl #32
    // 0x98f804: mov             x2, x3
    // 0x98f808: cmp             w2, NULL
    // 0x98f80c: b.ne            #0x98f818
    // 0x98f810: r2 = 0
    //     0x98f810: movz            x2, #0
    // 0x98f814: b               #0x98f828
    // 0x98f818: r3 = LoadInt32Instr(r2)
    //     0x98f818: sbfx            x3, x2, #1, #0x1f
    //     0x98f81c: tbz             w2, #0, #0x98f824
    //     0x98f820: ldur            x3, [x2, #7]
    // 0x98f824: mov             x2, x3
    // 0x98f828: stur            x2, [fp, #-0x58]
    // 0x98f82c: cmp             w0, NULL
    // 0x98f830: b.ne            #0x98f83c
    // 0x98f834: r3 = Null
    //     0x98f834: mov             x3, NULL
    // 0x98f838: b               #0x98f860
    // 0x98f83c: LoadField: r3 = r0->field_13
    //     0x98f83c: ldur            w3, [x0, #0x13]
    // 0x98f840: DecompressPointer r3
    //     0x98f840: add             x3, x3, HEAP, lsl #32
    // 0x98f844: cmp             w3, NULL
    // 0x98f848: b.ne            #0x98f854
    // 0x98f84c: r3 = Null
    //     0x98f84c: mov             x3, NULL
    // 0x98f850: b               #0x98f860
    // 0x98f854: LoadField: r4 = r3->field_b
    //     0x98f854: ldur            w4, [x3, #0xb]
    // 0x98f858: DecompressPointer r4
    //     0x98f858: add             x4, x4, HEAP, lsl #32
    // 0x98f85c: mov             x3, x4
    // 0x98f860: cmp             w3, NULL
    // 0x98f864: b.ne            #0x98f870
    // 0x98f868: r3 = 0
    //     0x98f868: movz            x3, #0
    // 0x98f86c: b               #0x98f880
    // 0x98f870: r4 = LoadInt32Instr(r3)
    //     0x98f870: sbfx            x4, x3, #1, #0x1f
    //     0x98f874: tbz             w3, #0, #0x98f87c
    //     0x98f878: ldur            x4, [x3, #7]
    // 0x98f87c: mov             x3, x4
    // 0x98f880: stur            x3, [fp, #-0x50]
    // 0x98f884: cmp             w0, NULL
    // 0x98f888: b.ne            #0x98f894
    // 0x98f88c: r4 = Null
    //     0x98f88c: mov             x4, NULL
    // 0x98f890: b               #0x98f8b8
    // 0x98f894: LoadField: r4 = r0->field_13
    //     0x98f894: ldur            w4, [x0, #0x13]
    // 0x98f898: DecompressPointer r4
    //     0x98f898: add             x4, x4, HEAP, lsl #32
    // 0x98f89c: cmp             w4, NULL
    // 0x98f8a0: b.ne            #0x98f8ac
    // 0x98f8a4: r4 = Null
    //     0x98f8a4: mov             x4, NULL
    // 0x98f8a8: b               #0x98f8b8
    // 0x98f8ac: LoadField: r5 = r4->field_f
    //     0x98f8ac: ldur            w5, [x4, #0xf]
    // 0x98f8b0: DecompressPointer r5
    //     0x98f8b0: add             x5, x5, HEAP, lsl #32
    // 0x98f8b4: mov             x4, x5
    // 0x98f8b8: cmp             w4, NULL
    // 0x98f8bc: b.ne            #0x98f8c8
    // 0x98f8c0: r4 = 0
    //     0x98f8c0: movz            x4, #0
    // 0x98f8c4: b               #0x98f8d8
    // 0x98f8c8: r5 = LoadInt32Instr(r4)
    //     0x98f8c8: sbfx            x5, x4, #1, #0x1f
    //     0x98f8cc: tbz             w4, #0, #0x98f8d4
    //     0x98f8d0: ldur            x5, [x4, #7]
    // 0x98f8d4: mov             x4, x5
    // 0x98f8d8: stur            x4, [fp, #-0x48]
    // 0x98f8dc: r0 = Color()
    //     0x98f8dc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x98f8e0: mov             x1, x0
    // 0x98f8e4: r0 = Instance_ColorSpace
    //     0x98f8e4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x98f8e8: stur            x1, [fp, #-0x30]
    // 0x98f8ec: StoreField: r1->field_27 = r0
    //     0x98f8ec: stur            w0, [x1, #0x27]
    // 0x98f8f0: d0 = 1.000000
    //     0x98f8f0: fmov            d0, #1.00000000
    // 0x98f8f4: StoreField: r1->field_7 = d0
    //     0x98f8f4: stur            d0, [x1, #7]
    // 0x98f8f8: ldur            x2, [fp, #-0x58]
    // 0x98f8fc: ubfx            x2, x2, #0, #0x20
    // 0x98f900: and             w3, w2, #0xff
    // 0x98f904: ubfx            x3, x3, #0, #0x20
    // 0x98f908: scvtf           d0, x3
    // 0x98f90c: d1 = 255.000000
    //     0x98f90c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x98f910: fdiv            d2, d0, d1
    // 0x98f914: StoreField: r1->field_f = d2
    //     0x98f914: stur            d2, [x1, #0xf]
    // 0x98f918: ldur            x2, [fp, #-0x50]
    // 0x98f91c: ubfx            x2, x2, #0, #0x20
    // 0x98f920: and             w3, w2, #0xff
    // 0x98f924: ubfx            x3, x3, #0, #0x20
    // 0x98f928: scvtf           d0, x3
    // 0x98f92c: fdiv            d2, d0, d1
    // 0x98f930: ArrayStore: r1[0] = d2  ; List_8
    //     0x98f930: stur            d2, [x1, #0x17]
    // 0x98f934: ldur            x2, [fp, #-0x48]
    // 0x98f938: ubfx            x2, x2, #0, #0x20
    // 0x98f93c: and             w3, w2, #0xff
    // 0x98f940: ubfx            x3, x3, #0, #0x20
    // 0x98f944: scvtf           d0, x3
    // 0x98f948: fdiv            d2, d0, d1
    // 0x98f94c: StoreField: r1->field_1f = d2
    //     0x98f94c: stur            d2, [x1, #0x1f]
    // 0x98f950: ldur            x2, [fp, #-0x18]
    // 0x98f954: cmp             w2, NULL
    // 0x98f958: b.ne            #0x98f964
    // 0x98f95c: r3 = Null
    //     0x98f95c: mov             x3, NULL
    // 0x98f960: b               #0x98f988
    // 0x98f964: LoadField: r3 = r2->field_13
    //     0x98f964: ldur            w3, [x2, #0x13]
    // 0x98f968: DecompressPointer r3
    //     0x98f968: add             x3, x3, HEAP, lsl #32
    // 0x98f96c: cmp             w3, NULL
    // 0x98f970: b.ne            #0x98f97c
    // 0x98f974: r3 = Null
    //     0x98f974: mov             x3, NULL
    // 0x98f978: b               #0x98f988
    // 0x98f97c: LoadField: r4 = r3->field_7
    //     0x98f97c: ldur            w4, [x3, #7]
    // 0x98f980: DecompressPointer r4
    //     0x98f980: add             x4, x4, HEAP, lsl #32
    // 0x98f984: mov             x3, x4
    // 0x98f988: cmp             w3, NULL
    // 0x98f98c: b.ne            #0x98f998
    // 0x98f990: r3 = 0
    //     0x98f990: movz            x3, #0
    // 0x98f994: b               #0x98f9a8
    // 0x98f998: r4 = LoadInt32Instr(r3)
    //     0x98f998: sbfx            x4, x3, #1, #0x1f
    //     0x98f99c: tbz             w3, #0, #0x98f9a4
    //     0x98f9a0: ldur            x4, [x3, #7]
    // 0x98f9a4: mov             x3, x4
    // 0x98f9a8: stur            x3, [fp, #-0x58]
    // 0x98f9ac: cmp             w2, NULL
    // 0x98f9b0: b.ne            #0x98f9bc
    // 0x98f9b4: r4 = Null
    //     0x98f9b4: mov             x4, NULL
    // 0x98f9b8: b               #0x98f9e0
    // 0x98f9bc: LoadField: r4 = r2->field_13
    //     0x98f9bc: ldur            w4, [x2, #0x13]
    // 0x98f9c0: DecompressPointer r4
    //     0x98f9c0: add             x4, x4, HEAP, lsl #32
    // 0x98f9c4: cmp             w4, NULL
    // 0x98f9c8: b.ne            #0x98f9d4
    // 0x98f9cc: r4 = Null
    //     0x98f9cc: mov             x4, NULL
    // 0x98f9d0: b               #0x98f9e0
    // 0x98f9d4: LoadField: r5 = r4->field_b
    //     0x98f9d4: ldur            w5, [x4, #0xb]
    // 0x98f9d8: DecompressPointer r5
    //     0x98f9d8: add             x5, x5, HEAP, lsl #32
    // 0x98f9dc: mov             x4, x5
    // 0x98f9e0: cmp             w4, NULL
    // 0x98f9e4: b.ne            #0x98f9f0
    // 0x98f9e8: r4 = 0
    //     0x98f9e8: movz            x4, #0
    // 0x98f9ec: b               #0x98fa00
    // 0x98f9f0: r5 = LoadInt32Instr(r4)
    //     0x98f9f0: sbfx            x5, x4, #1, #0x1f
    //     0x98f9f4: tbz             w4, #0, #0x98f9fc
    //     0x98f9f8: ldur            x5, [x4, #7]
    // 0x98f9fc: mov             x4, x5
    // 0x98fa00: stur            x4, [fp, #-0x50]
    // 0x98fa04: cmp             w2, NULL
    // 0x98fa08: b.ne            #0x98fa14
    // 0x98fa0c: r2 = Null
    //     0x98fa0c: mov             x2, NULL
    // 0x98fa10: b               #0x98fa34
    // 0x98fa14: LoadField: r5 = r2->field_13
    //     0x98fa14: ldur            w5, [x2, #0x13]
    // 0x98fa18: DecompressPointer r5
    //     0x98fa18: add             x5, x5, HEAP, lsl #32
    // 0x98fa1c: cmp             w5, NULL
    // 0x98fa20: b.ne            #0x98fa2c
    // 0x98fa24: r2 = Null
    //     0x98fa24: mov             x2, NULL
    // 0x98fa28: b               #0x98fa34
    // 0x98fa2c: LoadField: r2 = r5->field_f
    //     0x98fa2c: ldur            w2, [x5, #0xf]
    // 0x98fa30: DecompressPointer r2
    //     0x98fa30: add             x2, x2, HEAP, lsl #32
    // 0x98fa34: cmp             w2, NULL
    // 0x98fa38: b.ne            #0x98fa44
    // 0x98fa3c: r2 = 0
    //     0x98fa3c: movz            x2, #0
    // 0x98fa40: b               #0x98fa54
    // 0x98fa44: r5 = LoadInt32Instr(r2)
    //     0x98fa44: sbfx            x5, x2, #1, #0x1f
    //     0x98fa48: tbz             w2, #0, #0x98fa50
    //     0x98fa4c: ldur            x5, [x2, #7]
    // 0x98fa50: mov             x2, x5
    // 0x98fa54: stur            x2, [fp, #-0x48]
    // 0x98fa58: r0 = Color()
    //     0x98fa58: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x98fa5c: r1 = Instance_ColorSpace
    //     0x98fa5c: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x98fa60: stur            x0, [fp, #-0x18]
    // 0x98fa64: StoreField: r0->field_27 = r1
    //     0x98fa64: stur            w1, [x0, #0x27]
    // 0x98fa68: d1 = 0.700000
    //     0x98fa68: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98fa6c: ldr             d1, [x17, #0xf48]
    // 0x98fa70: StoreField: r0->field_7 = d1
    //     0x98fa70: stur            d1, [x0, #7]
    // 0x98fa74: ldur            x1, [fp, #-0x58]
    // 0x98fa78: ubfx            x1, x1, #0, #0x20
    // 0x98fa7c: and             w2, w1, #0xff
    // 0x98fa80: ubfx            x2, x2, #0, #0x20
    // 0x98fa84: scvtf           d0, x2
    // 0x98fa88: d2 = 255.000000
    //     0x98fa88: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x98fa8c: fdiv            d1, d0, d2
    // 0x98fa90: StoreField: r0->field_f = d1
    //     0x98fa90: stur            d1, [x0, #0xf]
    // 0x98fa94: ldur            x1, [fp, #-0x50]
    // 0x98fa98: ubfx            x1, x1, #0, #0x20
    // 0x98fa9c: and             w2, w1, #0xff
    // 0x98faa0: ubfx            x2, x2, #0, #0x20
    // 0x98faa4: scvtf           d0, x2
    // 0x98faa8: fdiv            d1, d0, d2
    // 0x98faac: ArrayStore: r0[0] = d1  ; List_8
    //     0x98faac: stur            d1, [x0, #0x17]
    // 0x98fab0: ldur            x1, [fp, #-0x48]
    // 0x98fab4: ubfx            x1, x1, #0, #0x20
    // 0x98fab8: and             w2, w1, #0xff
    // 0x98fabc: ubfx            x2, x2, #0, #0x20
    // 0x98fac0: scvtf           d0, x2
    // 0x98fac4: fdiv            d1, d0, d2
    // 0x98fac8: StoreField: r0->field_1f = d1
    //     0x98fac8: stur            d1, [x0, #0x1f]
    // 0x98facc: r1 = Null
    //     0x98facc: mov             x1, NULL
    // 0x98fad0: r2 = 4
    //     0x98fad0: movz            x2, #0x4
    // 0x98fad4: r0 = AllocateArray()
    //     0x98fad4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98fad8: mov             x2, x0
    // 0x98fadc: ldur            x0, [fp, #-0x30]
    // 0x98fae0: stur            x2, [fp, #-0x60]
    // 0x98fae4: StoreField: r2->field_f = r0
    //     0x98fae4: stur            w0, [x2, #0xf]
    // 0x98fae8: ldur            x0, [fp, #-0x18]
    // 0x98faec: StoreField: r2->field_13 = r0
    //     0x98faec: stur            w0, [x2, #0x13]
    // 0x98faf0: r1 = <Color>
    //     0x98faf0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x98faf4: ldr             x1, [x1, #0xf80]
    // 0x98faf8: r0 = AllocateGrowableArray()
    //     0x98faf8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98fafc: mov             x1, x0
    // 0x98fb00: ldur            x0, [fp, #-0x60]
    // 0x98fb04: StoreField: r1->field_f = r0
    //     0x98fb04: stur            w0, [x1, #0xf]
    // 0x98fb08: r2 = 4
    //     0x98fb08: movz            x2, #0x4
    // 0x98fb0c: StoreField: r1->field_b = r2
    //     0x98fb0c: stur            w2, [x1, #0xb]
    // 0x98fb10: mov             x4, x1
    // 0x98fb14: b               #0x98ff08
    // 0x98fb18: r2 = 4
    //     0x98fb18: movz            x2, #0x4
    // 0x98fb1c: r1 = Instance_ColorSpace
    //     0x98fb1c: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x98fb20: d2 = 255.000000
    //     0x98fb20: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x98fb24: d0 = 1.000000
    //     0x98fb24: fmov            d0, #1.00000000
    // 0x98fb28: d1 = 0.700000
    //     0x98fb28: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98fb2c: ldr             d1, [x17, #0xf48]
    // 0x98fb30: LoadField: r3 = r0->field_f
    //     0x98fb30: ldur            w3, [x0, #0xf]
    // 0x98fb34: DecompressPointer r3
    //     0x98fb34: add             x3, x3, HEAP, lsl #32
    // 0x98fb38: LoadField: r0 = r3->field_b
    //     0x98fb38: ldur            w0, [x3, #0xb]
    // 0x98fb3c: DecompressPointer r0
    //     0x98fb3c: add             x0, x0, HEAP, lsl #32
    // 0x98fb40: stur            x0, [fp, #-0x18]
    // 0x98fb44: cmp             w0, NULL
    // 0x98fb48: b.ne            #0x98fb54
    // 0x98fb4c: r3 = Null
    //     0x98fb4c: mov             x3, NULL
    // 0x98fb50: b               #0x98fb8c
    // 0x98fb54: LoadField: r3 = r0->field_1f
    //     0x98fb54: ldur            w3, [x0, #0x1f]
    // 0x98fb58: DecompressPointer r3
    //     0x98fb58: add             x3, x3, HEAP, lsl #32
    // 0x98fb5c: cmp             w3, NULL
    // 0x98fb60: b.ne            #0x98fb6c
    // 0x98fb64: r3 = Null
    //     0x98fb64: mov             x3, NULL
    // 0x98fb68: b               #0x98fb8c
    // 0x98fb6c: LoadField: r4 = r3->field_13
    //     0x98fb6c: ldur            w4, [x3, #0x13]
    // 0x98fb70: DecompressPointer r4
    //     0x98fb70: add             x4, x4, HEAP, lsl #32
    // 0x98fb74: cmp             w4, NULL
    // 0x98fb78: b.ne            #0x98fb84
    // 0x98fb7c: r3 = Null
    //     0x98fb7c: mov             x3, NULL
    // 0x98fb80: b               #0x98fb8c
    // 0x98fb84: LoadField: r3 = r4->field_7
    //     0x98fb84: ldur            w3, [x4, #7]
    // 0x98fb88: DecompressPointer r3
    //     0x98fb88: add             x3, x3, HEAP, lsl #32
    // 0x98fb8c: cmp             w3, NULL
    // 0x98fb90: b.ne            #0x98fb9c
    // 0x98fb94: r3 = 0
    //     0x98fb94: movz            x3, #0
    // 0x98fb98: b               #0x98fbac
    // 0x98fb9c: r4 = LoadInt32Instr(r3)
    //     0x98fb9c: sbfx            x4, x3, #1, #0x1f
    //     0x98fba0: tbz             w3, #0, #0x98fba8
    //     0x98fba4: ldur            x4, [x3, #7]
    // 0x98fba8: mov             x3, x4
    // 0x98fbac: stur            x3, [fp, #-0x58]
    // 0x98fbb0: cmp             w0, NULL
    // 0x98fbb4: b.ne            #0x98fbc0
    // 0x98fbb8: r4 = Null
    //     0x98fbb8: mov             x4, NULL
    // 0x98fbbc: b               #0x98fbf8
    // 0x98fbc0: LoadField: r4 = r0->field_1f
    //     0x98fbc0: ldur            w4, [x0, #0x1f]
    // 0x98fbc4: DecompressPointer r4
    //     0x98fbc4: add             x4, x4, HEAP, lsl #32
    // 0x98fbc8: cmp             w4, NULL
    // 0x98fbcc: b.ne            #0x98fbd8
    // 0x98fbd0: r4 = Null
    //     0x98fbd0: mov             x4, NULL
    // 0x98fbd4: b               #0x98fbf8
    // 0x98fbd8: LoadField: r5 = r4->field_13
    //     0x98fbd8: ldur            w5, [x4, #0x13]
    // 0x98fbdc: DecompressPointer r5
    //     0x98fbdc: add             x5, x5, HEAP, lsl #32
    // 0x98fbe0: cmp             w5, NULL
    // 0x98fbe4: b.ne            #0x98fbf0
    // 0x98fbe8: r4 = Null
    //     0x98fbe8: mov             x4, NULL
    // 0x98fbec: b               #0x98fbf8
    // 0x98fbf0: LoadField: r4 = r5->field_b
    //     0x98fbf0: ldur            w4, [x5, #0xb]
    // 0x98fbf4: DecompressPointer r4
    //     0x98fbf4: add             x4, x4, HEAP, lsl #32
    // 0x98fbf8: cmp             w4, NULL
    // 0x98fbfc: b.ne            #0x98fc08
    // 0x98fc00: r4 = 0
    //     0x98fc00: movz            x4, #0
    // 0x98fc04: b               #0x98fc18
    // 0x98fc08: r5 = LoadInt32Instr(r4)
    //     0x98fc08: sbfx            x5, x4, #1, #0x1f
    //     0x98fc0c: tbz             w4, #0, #0x98fc14
    //     0x98fc10: ldur            x5, [x4, #7]
    // 0x98fc14: mov             x4, x5
    // 0x98fc18: stur            x4, [fp, #-0x50]
    // 0x98fc1c: cmp             w0, NULL
    // 0x98fc20: b.ne            #0x98fc2c
    // 0x98fc24: r5 = Null
    //     0x98fc24: mov             x5, NULL
    // 0x98fc28: b               #0x98fc64
    // 0x98fc2c: LoadField: r5 = r0->field_1f
    //     0x98fc2c: ldur            w5, [x0, #0x1f]
    // 0x98fc30: DecompressPointer r5
    //     0x98fc30: add             x5, x5, HEAP, lsl #32
    // 0x98fc34: cmp             w5, NULL
    // 0x98fc38: b.ne            #0x98fc44
    // 0x98fc3c: r5 = Null
    //     0x98fc3c: mov             x5, NULL
    // 0x98fc40: b               #0x98fc64
    // 0x98fc44: LoadField: r6 = r5->field_13
    //     0x98fc44: ldur            w6, [x5, #0x13]
    // 0x98fc48: DecompressPointer r6
    //     0x98fc48: add             x6, x6, HEAP, lsl #32
    // 0x98fc4c: cmp             w6, NULL
    // 0x98fc50: b.ne            #0x98fc5c
    // 0x98fc54: r5 = Null
    //     0x98fc54: mov             x5, NULL
    // 0x98fc58: b               #0x98fc64
    // 0x98fc5c: LoadField: r5 = r6->field_f
    //     0x98fc5c: ldur            w5, [x6, #0xf]
    // 0x98fc60: DecompressPointer r5
    //     0x98fc60: add             x5, x5, HEAP, lsl #32
    // 0x98fc64: cmp             w5, NULL
    // 0x98fc68: b.ne            #0x98fc74
    // 0x98fc6c: r5 = 0
    //     0x98fc6c: movz            x5, #0
    // 0x98fc70: b               #0x98fc84
    // 0x98fc74: r6 = LoadInt32Instr(r5)
    //     0x98fc74: sbfx            x6, x5, #1, #0x1f
    //     0x98fc78: tbz             w5, #0, #0x98fc80
    //     0x98fc7c: ldur            x6, [x5, #7]
    // 0x98fc80: mov             x5, x6
    // 0x98fc84: stur            x5, [fp, #-0x48]
    // 0x98fc88: r0 = Color()
    //     0x98fc88: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x98fc8c: mov             x1, x0
    // 0x98fc90: r0 = Instance_ColorSpace
    //     0x98fc90: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x98fc94: stur            x1, [fp, #-0x20]
    // 0x98fc98: StoreField: r1->field_27 = r0
    //     0x98fc98: stur            w0, [x1, #0x27]
    // 0x98fc9c: d0 = 1.000000
    //     0x98fc9c: fmov            d0, #1.00000000
    // 0x98fca0: StoreField: r1->field_7 = d0
    //     0x98fca0: stur            d0, [x1, #7]
    // 0x98fca4: ldur            x2, [fp, #-0x58]
    // 0x98fca8: ubfx            x2, x2, #0, #0x20
    // 0x98fcac: and             w3, w2, #0xff
    // 0x98fcb0: ubfx            x3, x3, #0, #0x20
    // 0x98fcb4: scvtf           d0, x3
    // 0x98fcb8: d1 = 255.000000
    //     0x98fcb8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x98fcbc: fdiv            d2, d0, d1
    // 0x98fcc0: StoreField: r1->field_f = d2
    //     0x98fcc0: stur            d2, [x1, #0xf]
    // 0x98fcc4: ldur            x2, [fp, #-0x50]
    // 0x98fcc8: ubfx            x2, x2, #0, #0x20
    // 0x98fccc: and             w3, w2, #0xff
    // 0x98fcd0: ubfx            x3, x3, #0, #0x20
    // 0x98fcd4: scvtf           d0, x3
    // 0x98fcd8: fdiv            d2, d0, d1
    // 0x98fcdc: ArrayStore: r1[0] = d2  ; List_8
    //     0x98fcdc: stur            d2, [x1, #0x17]
    // 0x98fce0: ldur            x2, [fp, #-0x48]
    // 0x98fce4: ubfx            x2, x2, #0, #0x20
    // 0x98fce8: and             w3, w2, #0xff
    // 0x98fcec: ubfx            x3, x3, #0, #0x20
    // 0x98fcf0: scvtf           d0, x3
    // 0x98fcf4: fdiv            d2, d0, d1
    // 0x98fcf8: StoreField: r1->field_1f = d2
    //     0x98fcf8: stur            d2, [x1, #0x1f]
    // 0x98fcfc: ldur            x2, [fp, #-0x18]
    // 0x98fd00: cmp             w2, NULL
    // 0x98fd04: b.ne            #0x98fd10
    // 0x98fd08: r3 = Null
    //     0x98fd08: mov             x3, NULL
    // 0x98fd0c: b               #0x98fd48
    // 0x98fd10: LoadField: r3 = r2->field_1f
    //     0x98fd10: ldur            w3, [x2, #0x1f]
    // 0x98fd14: DecompressPointer r3
    //     0x98fd14: add             x3, x3, HEAP, lsl #32
    // 0x98fd18: cmp             w3, NULL
    // 0x98fd1c: b.ne            #0x98fd28
    // 0x98fd20: r3 = Null
    //     0x98fd20: mov             x3, NULL
    // 0x98fd24: b               #0x98fd48
    // 0x98fd28: LoadField: r4 = r3->field_13
    //     0x98fd28: ldur            w4, [x3, #0x13]
    // 0x98fd2c: DecompressPointer r4
    //     0x98fd2c: add             x4, x4, HEAP, lsl #32
    // 0x98fd30: cmp             w4, NULL
    // 0x98fd34: b.ne            #0x98fd40
    // 0x98fd38: r3 = Null
    //     0x98fd38: mov             x3, NULL
    // 0x98fd3c: b               #0x98fd48
    // 0x98fd40: LoadField: r3 = r4->field_7
    //     0x98fd40: ldur            w3, [x4, #7]
    // 0x98fd44: DecompressPointer r3
    //     0x98fd44: add             x3, x3, HEAP, lsl #32
    // 0x98fd48: cmp             w3, NULL
    // 0x98fd4c: b.ne            #0x98fd58
    // 0x98fd50: r3 = 0
    //     0x98fd50: movz            x3, #0
    // 0x98fd54: b               #0x98fd68
    // 0x98fd58: r4 = LoadInt32Instr(r3)
    //     0x98fd58: sbfx            x4, x3, #1, #0x1f
    //     0x98fd5c: tbz             w3, #0, #0x98fd64
    //     0x98fd60: ldur            x4, [x3, #7]
    // 0x98fd64: mov             x3, x4
    // 0x98fd68: stur            x3, [fp, #-0x58]
    // 0x98fd6c: cmp             w2, NULL
    // 0x98fd70: b.ne            #0x98fd7c
    // 0x98fd74: r4 = Null
    //     0x98fd74: mov             x4, NULL
    // 0x98fd78: b               #0x98fdb4
    // 0x98fd7c: LoadField: r4 = r2->field_1f
    //     0x98fd7c: ldur            w4, [x2, #0x1f]
    // 0x98fd80: DecompressPointer r4
    //     0x98fd80: add             x4, x4, HEAP, lsl #32
    // 0x98fd84: cmp             w4, NULL
    // 0x98fd88: b.ne            #0x98fd94
    // 0x98fd8c: r4 = Null
    //     0x98fd8c: mov             x4, NULL
    // 0x98fd90: b               #0x98fdb4
    // 0x98fd94: LoadField: r5 = r4->field_13
    //     0x98fd94: ldur            w5, [x4, #0x13]
    // 0x98fd98: DecompressPointer r5
    //     0x98fd98: add             x5, x5, HEAP, lsl #32
    // 0x98fd9c: cmp             w5, NULL
    // 0x98fda0: b.ne            #0x98fdac
    // 0x98fda4: r4 = Null
    //     0x98fda4: mov             x4, NULL
    // 0x98fda8: b               #0x98fdb4
    // 0x98fdac: LoadField: r4 = r5->field_b
    //     0x98fdac: ldur            w4, [x5, #0xb]
    // 0x98fdb0: DecompressPointer r4
    //     0x98fdb0: add             x4, x4, HEAP, lsl #32
    // 0x98fdb4: cmp             w4, NULL
    // 0x98fdb8: b.ne            #0x98fdc4
    // 0x98fdbc: r4 = 0
    //     0x98fdbc: movz            x4, #0
    // 0x98fdc0: b               #0x98fdd4
    // 0x98fdc4: r5 = LoadInt32Instr(r4)
    //     0x98fdc4: sbfx            x5, x4, #1, #0x1f
    //     0x98fdc8: tbz             w4, #0, #0x98fdd0
    //     0x98fdcc: ldur            x5, [x4, #7]
    // 0x98fdd0: mov             x4, x5
    // 0x98fdd4: stur            x4, [fp, #-0x50]
    // 0x98fdd8: cmp             w2, NULL
    // 0x98fddc: b.ne            #0x98fde8
    // 0x98fde0: r2 = Null
    //     0x98fde0: mov             x2, NULL
    // 0x98fde4: b               #0x98fe24
    // 0x98fde8: LoadField: r5 = r2->field_1f
    //     0x98fde8: ldur            w5, [x2, #0x1f]
    // 0x98fdec: DecompressPointer r5
    //     0x98fdec: add             x5, x5, HEAP, lsl #32
    // 0x98fdf0: cmp             w5, NULL
    // 0x98fdf4: b.ne            #0x98fe00
    // 0x98fdf8: r2 = Null
    //     0x98fdf8: mov             x2, NULL
    // 0x98fdfc: b               #0x98fe24
    // 0x98fe00: LoadField: r2 = r5->field_13
    //     0x98fe00: ldur            w2, [x5, #0x13]
    // 0x98fe04: DecompressPointer r2
    //     0x98fe04: add             x2, x2, HEAP, lsl #32
    // 0x98fe08: cmp             w2, NULL
    // 0x98fe0c: b.ne            #0x98fe18
    // 0x98fe10: r2 = Null
    //     0x98fe10: mov             x2, NULL
    // 0x98fe14: b               #0x98fe24
    // 0x98fe18: LoadField: r5 = r2->field_f
    //     0x98fe18: ldur            w5, [x2, #0xf]
    // 0x98fe1c: DecompressPointer r5
    //     0x98fe1c: add             x5, x5, HEAP, lsl #32
    // 0x98fe20: mov             x2, x5
    // 0x98fe24: cmp             w2, NULL
    // 0x98fe28: b.ne            #0x98fe34
    // 0x98fe2c: r2 = 0
    //     0x98fe2c: movz            x2, #0
    // 0x98fe30: b               #0x98fe44
    // 0x98fe34: r5 = LoadInt32Instr(r2)
    //     0x98fe34: sbfx            x5, x2, #1, #0x1f
    //     0x98fe38: tbz             w2, #0, #0x98fe40
    //     0x98fe3c: ldur            x5, [x2, #7]
    // 0x98fe40: mov             x2, x5
    // 0x98fe44: stur            x2, [fp, #-0x48]
    // 0x98fe48: r0 = Color()
    //     0x98fe48: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x98fe4c: mov             x3, x0
    // 0x98fe50: r0 = Instance_ColorSpace
    //     0x98fe50: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x98fe54: stur            x3, [fp, #-0x18]
    // 0x98fe58: StoreField: r3->field_27 = r0
    //     0x98fe58: stur            w0, [x3, #0x27]
    // 0x98fe5c: d0 = 0.700000
    //     0x98fe5c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98fe60: ldr             d0, [x17, #0xf48]
    // 0x98fe64: StoreField: r3->field_7 = d0
    //     0x98fe64: stur            d0, [x3, #7]
    // 0x98fe68: ldur            x0, [fp, #-0x58]
    // 0x98fe6c: ubfx            x0, x0, #0, #0x20
    // 0x98fe70: and             w1, w0, #0xff
    // 0x98fe74: ubfx            x1, x1, #0, #0x20
    // 0x98fe78: scvtf           d0, x1
    // 0x98fe7c: d1 = 255.000000
    //     0x98fe7c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x98fe80: fdiv            d2, d0, d1
    // 0x98fe84: StoreField: r3->field_f = d2
    //     0x98fe84: stur            d2, [x3, #0xf]
    // 0x98fe88: ldur            x0, [fp, #-0x50]
    // 0x98fe8c: ubfx            x0, x0, #0, #0x20
    // 0x98fe90: and             w1, w0, #0xff
    // 0x98fe94: ubfx            x1, x1, #0, #0x20
    // 0x98fe98: scvtf           d0, x1
    // 0x98fe9c: fdiv            d2, d0, d1
    // 0x98fea0: ArrayStore: r3[0] = d2  ; List_8
    //     0x98fea0: stur            d2, [x3, #0x17]
    // 0x98fea4: ldur            x0, [fp, #-0x48]
    // 0x98fea8: ubfx            x0, x0, #0, #0x20
    // 0x98feac: and             w1, w0, #0xff
    // 0x98feb0: ubfx            x1, x1, #0, #0x20
    // 0x98feb4: scvtf           d0, x1
    // 0x98feb8: fdiv            d2, d0, d1
    // 0x98febc: StoreField: r3->field_1f = d2
    //     0x98febc: stur            d2, [x3, #0x1f]
    // 0x98fec0: r1 = Null
    //     0x98fec0: mov             x1, NULL
    // 0x98fec4: r2 = 4
    //     0x98fec4: movz            x2, #0x4
    // 0x98fec8: r0 = AllocateArray()
    //     0x98fec8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98fecc: mov             x2, x0
    // 0x98fed0: ldur            x0, [fp, #-0x20]
    // 0x98fed4: stur            x2, [fp, #-0x30]
    // 0x98fed8: StoreField: r2->field_f = r0
    //     0x98fed8: stur            w0, [x2, #0xf]
    // 0x98fedc: ldur            x0, [fp, #-0x18]
    // 0x98fee0: StoreField: r2->field_13 = r0
    //     0x98fee0: stur            w0, [x2, #0x13]
    // 0x98fee4: r1 = <Color>
    //     0x98fee4: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x98fee8: ldr             x1, [x1, #0xf80]
    // 0x98feec: r0 = AllocateGrowableArray()
    //     0x98feec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98fef0: mov             x1, x0
    // 0x98fef4: ldur            x0, [fp, #-0x30]
    // 0x98fef8: StoreField: r1->field_f = r0
    //     0x98fef8: stur            w0, [x1, #0xf]
    // 0x98fefc: r2 = 4
    //     0x98fefc: movz            x2, #0x4
    // 0x98ff00: StoreField: r1->field_b = r2
    //     0x98ff00: stur            w2, [x1, #0xb]
    // 0x98ff04: mov             x4, x1
    // 0x98ff08: ldur            x1, [fp, #-8]
    // 0x98ff0c: ldur            x3, [fp, #-0x28]
    // 0x98ff10: ldur            x0, [fp, #-0x38]
    // 0x98ff14: stur            x4, [fp, #-0x18]
    // 0x98ff18: r0 = LinearGradient()
    //     0x98ff18: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0x98ff1c: mov             x1, x0
    // 0x98ff20: r0 = Instance_Alignment
    //     0x98ff20: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0x98ff24: ldr             x0, [x0, #0xce0]
    // 0x98ff28: stur            x1, [fp, #-0x20]
    // 0x98ff2c: StoreField: r1->field_13 = r0
    //     0x98ff2c: stur            w0, [x1, #0x13]
    // 0x98ff30: r0 = Instance_Alignment
    //     0x98ff30: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0x98ff34: ldr             x0, [x0, #0xce8]
    // 0x98ff38: ArrayStore: r1[0] = r0  ; List_4
    //     0x98ff38: stur            w0, [x1, #0x17]
    // 0x98ff3c: r0 = Instance_TileMode
    //     0x98ff3c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0x98ff40: ldr             x0, [x0, #0xcf0]
    // 0x98ff44: StoreField: r1->field_1b = r0
    //     0x98ff44: stur            w0, [x1, #0x1b]
    // 0x98ff48: ldur            x0, [fp, #-0x18]
    // 0x98ff4c: StoreField: r1->field_7 = r0
    //     0x98ff4c: stur            w0, [x1, #7]
    // 0x98ff50: r0 = BoxDecoration()
    //     0x98ff50: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x98ff54: mov             x2, x0
    // 0x98ff58: ldur            x0, [fp, #-0x38]
    // 0x98ff5c: stur            x2, [fp, #-0x18]
    // 0x98ff60: StoreField: r2->field_13 = r0
    //     0x98ff60: stur            w0, [x2, #0x13]
    // 0x98ff64: ldur            x0, [fp, #-0x20]
    // 0x98ff68: StoreField: r2->field_1b = r0
    //     0x98ff68: stur            w0, [x2, #0x1b]
    // 0x98ff6c: r0 = Instance_BoxShape
    //     0x98ff6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x98ff70: ldr             x0, [x0, #0x80]
    // 0x98ff74: StoreField: r2->field_23 = r0
    //     0x98ff74: stur            w0, [x2, #0x23]
    // 0x98ff78: ldur            x1, [fp, #-0x10]
    // 0x98ff7c: r0 = of()
    //     0x98ff7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98ff80: LoadField: r1 = r0->field_87
    //     0x98ff80: ldur            w1, [x0, #0x87]
    // 0x98ff84: DecompressPointer r1
    //     0x98ff84: add             x1, x1, HEAP, lsl #32
    // 0x98ff88: LoadField: r0 = r1->field_7
    //     0x98ff88: ldur            w0, [x1, #7]
    // 0x98ff8c: DecompressPointer r0
    //     0x98ff8c: add             x0, x0, HEAP, lsl #32
    // 0x98ff90: r16 = 16.000000
    //     0x98ff90: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x98ff94: ldr             x16, [x16, #0x188]
    // 0x98ff98: r30 = Instance_Color
    //     0x98ff98: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x98ff9c: stp             lr, x16, [SP]
    // 0x98ffa0: mov             x1, x0
    // 0x98ffa4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x98ffa4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x98ffa8: ldr             x4, [x4, #0xaa0]
    // 0x98ffac: r0 = copyWith()
    //     0x98ffac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x98ffb0: stur            x0, [fp, #-0x10]
    // 0x98ffb4: r0 = TextSpan()
    //     0x98ffb4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x98ffb8: mov             x2, x0
    // 0x98ffbc: r0 = "BUMPER OFFER\n"
    //     0x98ffbc: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0x98ffc0: ldr             x0, [x0, #0x338]
    // 0x98ffc4: stur            x2, [fp, #-0x20]
    // 0x98ffc8: StoreField: r2->field_b = r0
    //     0x98ffc8: stur            w0, [x2, #0xb]
    // 0x98ffcc: r0 = Instance__DeferringMouseCursor
    //     0x98ffcc: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x98ffd0: ArrayStore: r2[0] = r0  ; List_4
    //     0x98ffd0: stur            w0, [x2, #0x17]
    // 0x98ffd4: ldur            x1, [fp, #-0x10]
    // 0x98ffd8: StoreField: r2->field_7 = r1
    //     0x98ffd8: stur            w1, [x2, #7]
    // 0x98ffdc: ldur            x3, [fp, #-0x28]
    // 0x98ffe0: LoadField: r1 = r3->field_13
    //     0x98ffe0: ldur            w1, [x3, #0x13]
    // 0x98ffe4: DecompressPointer r1
    //     0x98ffe4: add             x1, x1, HEAP, lsl #32
    // 0x98ffe8: r0 = of()
    //     0x98ffe8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98ffec: LoadField: r1 = r0->field_87
    //     0x98ffec: ldur            w1, [x0, #0x87]
    // 0x98fff0: DecompressPointer r1
    //     0x98fff0: add             x1, x1, HEAP, lsl #32
    // 0x98fff4: LoadField: r0 = r1->field_2b
    //     0x98fff4: ldur            w0, [x1, #0x2b]
    // 0x98fff8: DecompressPointer r0
    //     0x98fff8: add             x0, x0, HEAP, lsl #32
    // 0x98fffc: r16 = 12.000000
    //     0x98fffc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x990000: ldr             x16, [x16, #0x9e8]
    // 0x990004: r30 = Instance_Color
    //     0x990004: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x990008: stp             lr, x16, [SP]
    // 0x99000c: mov             x1, x0
    // 0x990010: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x990010: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x990014: ldr             x4, [x4, #0xaa0]
    // 0x990018: r0 = copyWith()
    //     0x990018: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99001c: stur            x0, [fp, #-0x10]
    // 0x990020: r0 = TextSpan()
    //     0x990020: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x990024: mov             x3, x0
    // 0x990028: r0 = "Unlocked from your last order"
    //     0x990028: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0x99002c: ldr             x0, [x0, #0x340]
    // 0x990030: stur            x3, [fp, #-0x30]
    // 0x990034: StoreField: r3->field_b = r0
    //     0x990034: stur            w0, [x3, #0xb]
    // 0x990038: r0 = Instance__DeferringMouseCursor
    //     0x990038: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x99003c: ArrayStore: r3[0] = r0  ; List_4
    //     0x99003c: stur            w0, [x3, #0x17]
    // 0x990040: ldur            x1, [fp, #-0x10]
    // 0x990044: StoreField: r3->field_7 = r1
    //     0x990044: stur            w1, [x3, #7]
    // 0x990048: r1 = Null
    //     0x990048: mov             x1, NULL
    // 0x99004c: r2 = 4
    //     0x99004c: movz            x2, #0x4
    // 0x990050: r0 = AllocateArray()
    //     0x990050: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990054: mov             x2, x0
    // 0x990058: ldur            x0, [fp, #-0x20]
    // 0x99005c: stur            x2, [fp, #-0x10]
    // 0x990060: StoreField: r2->field_f = r0
    //     0x990060: stur            w0, [x2, #0xf]
    // 0x990064: ldur            x0, [fp, #-0x30]
    // 0x990068: StoreField: r2->field_13 = r0
    //     0x990068: stur            w0, [x2, #0x13]
    // 0x99006c: r1 = <InlineSpan>
    //     0x99006c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x990070: ldr             x1, [x1, #0xe40]
    // 0x990074: r0 = AllocateGrowableArray()
    //     0x990074: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x990078: mov             x1, x0
    // 0x99007c: ldur            x0, [fp, #-0x10]
    // 0x990080: stur            x1, [fp, #-0x20]
    // 0x990084: StoreField: r1->field_f = r0
    //     0x990084: stur            w0, [x1, #0xf]
    // 0x990088: r2 = 4
    //     0x990088: movz            x2, #0x4
    // 0x99008c: StoreField: r1->field_b = r2
    //     0x99008c: stur            w2, [x1, #0xb]
    // 0x990090: r0 = TextSpan()
    //     0x990090: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x990094: mov             x1, x0
    // 0x990098: ldur            x0, [fp, #-0x20]
    // 0x99009c: stur            x1, [fp, #-0x10]
    // 0x9900a0: StoreField: r1->field_f = r0
    //     0x9900a0: stur            w0, [x1, #0xf]
    // 0x9900a4: r0 = Instance__DeferringMouseCursor
    //     0x9900a4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x9900a8: ArrayStore: r1[0] = r0  ; List_4
    //     0x9900a8: stur            w0, [x1, #0x17]
    // 0x9900ac: r0 = RichText()
    //     0x9900ac: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x9900b0: mov             x1, x0
    // 0x9900b4: ldur            x2, [fp, #-0x10]
    // 0x9900b8: stur            x0, [fp, #-0x10]
    // 0x9900bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9900bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9900c0: r0 = RichText()
    //     0x9900c0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x9900c4: ldur            x0, [fp, #-8]
    // 0x9900c8: LoadField: r1 = r0->field_b
    //     0x9900c8: ldur            w1, [x0, #0xb]
    // 0x9900cc: DecompressPointer r1
    //     0x9900cc: add             x1, x1, HEAP, lsl #32
    // 0x9900d0: cmp             w1, NULL
    // 0x9900d4: b.eq            #0x99060c
    // 0x9900d8: LoadField: r2 = r1->field_b
    //     0x9900d8: ldur            w2, [x1, #0xb]
    // 0x9900dc: DecompressPointer r2
    //     0x9900dc: add             x2, x2, HEAP, lsl #32
    // 0x9900e0: cmp             w2, NULL
    // 0x9900e4: b.ne            #0x9900f0
    // 0x9900e8: r2 = Null
    //     0x9900e8: mov             x2, NULL
    // 0x9900ec: b               #0x990110
    // 0x9900f0: LoadField: r3 = r2->field_4b
    //     0x9900f0: ldur            w3, [x2, #0x4b]
    // 0x9900f4: DecompressPointer r3
    //     0x9900f4: add             x3, x3, HEAP, lsl #32
    // 0x9900f8: cmp             w3, NULL
    // 0x9900fc: b.ne            #0x990108
    // 0x990100: r2 = Null
    //     0x990100: mov             x2, NULL
    // 0x990104: b               #0x990110
    // 0x990108: LoadField: r2 = r3->field_7
    //     0x990108: ldur            w2, [x3, #7]
    // 0x99010c: DecompressPointer r2
    //     0x99010c: add             x2, x2, HEAP, lsl #32
    // 0x990110: cmp             w2, NULL
    // 0x990114: b.ne            #0x990160
    // 0x990118: LoadField: r2 = r1->field_f
    //     0x990118: ldur            w2, [x1, #0xf]
    // 0x99011c: DecompressPointer r2
    //     0x99011c: add             x2, x2, HEAP, lsl #32
    // 0x990120: LoadField: r1 = r2->field_b
    //     0x990120: ldur            w1, [x2, #0xb]
    // 0x990124: DecompressPointer r1
    //     0x990124: add             x1, x1, HEAP, lsl #32
    // 0x990128: cmp             w1, NULL
    // 0x99012c: b.ne            #0x990138
    // 0x990130: r1 = Null
    //     0x990130: mov             x1, NULL
    // 0x990134: b               #0x990158
    // 0x990138: LoadField: r2 = r1->field_1f
    //     0x990138: ldur            w2, [x1, #0x1f]
    // 0x99013c: DecompressPointer r2
    //     0x99013c: add             x2, x2, HEAP, lsl #32
    // 0x990140: cmp             w2, NULL
    // 0x990144: b.ne            #0x990150
    // 0x990148: r1 = Null
    //     0x990148: mov             x1, NULL
    // 0x99014c: b               #0x990158
    // 0x990150: LoadField: r1 = r2->field_7
    //     0x990150: ldur            w1, [x2, #7]
    // 0x990154: DecompressPointer r1
    //     0x990154: add             x1, x1, HEAP, lsl #32
    // 0x990158: mov             x5, x1
    // 0x99015c: b               #0x990164
    // 0x990160: mov             x5, x2
    // 0x990164: ldur            x4, [fp, #-0x28]
    // 0x990168: ldur            x3, [fp, #-0x10]
    // 0x99016c: stur            x5, [fp, #-0x20]
    // 0x990170: r1 = Null
    //     0x990170: mov             x1, NULL
    // 0x990174: r2 = 4
    //     0x990174: movz            x2, #0x4
    // 0x990178: r0 = AllocateArray()
    //     0x990178: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99017c: mov             x1, x0
    // 0x990180: ldur            x0, [fp, #-0x20]
    // 0x990184: StoreField: r1->field_f = r0
    //     0x990184: stur            w0, [x1, #0xf]
    // 0x990188: r16 = "\n"
    //     0x990188: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0x99018c: StoreField: r1->field_13 = r16
    //     0x99018c: stur            w16, [x1, #0x13]
    // 0x990190: str             x1, [SP]
    // 0x990194: r0 = _interpolate()
    //     0x990194: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x990198: ldur            x2, [fp, #-0x28]
    // 0x99019c: stur            x0, [fp, #-0x20]
    // 0x9901a0: LoadField: r1 = r2->field_13
    //     0x9901a0: ldur            w1, [x2, #0x13]
    // 0x9901a4: DecompressPointer r1
    //     0x9901a4: add             x1, x1, HEAP, lsl #32
    // 0x9901a8: r0 = of()
    //     0x9901a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9901ac: LoadField: r1 = r0->field_87
    //     0x9901ac: ldur            w1, [x0, #0x87]
    // 0x9901b0: DecompressPointer r1
    //     0x9901b0: add             x1, x1, HEAP, lsl #32
    // 0x9901b4: LoadField: r0 = r1->field_23
    //     0x9901b4: ldur            w0, [x1, #0x23]
    // 0x9901b8: DecompressPointer r0
    //     0x9901b8: add             x0, x0, HEAP, lsl #32
    // 0x9901bc: r16 = 32.000000
    //     0x9901bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0x9901c0: ldr             x16, [x16, #0x848]
    // 0x9901c4: r30 = Instance_Color
    //     0x9901c4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x9901c8: stp             lr, x16, [SP]
    // 0x9901cc: mov             x1, x0
    // 0x9901d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9901d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9901d4: ldr             x4, [x4, #0xaa0]
    // 0x9901d8: r0 = copyWith()
    //     0x9901d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9901dc: ldur            x2, [fp, #-0x28]
    // 0x9901e0: stur            x0, [fp, #-0x30]
    // 0x9901e4: LoadField: r1 = r2->field_13
    //     0x9901e4: ldur            w1, [x2, #0x13]
    // 0x9901e8: DecompressPointer r1
    //     0x9901e8: add             x1, x1, HEAP, lsl #32
    // 0x9901ec: r0 = of()
    //     0x9901ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9901f0: LoadField: r1 = r0->field_87
    //     0x9901f0: ldur            w1, [x0, #0x87]
    // 0x9901f4: DecompressPointer r1
    //     0x9901f4: add             x1, x1, HEAP, lsl #32
    // 0x9901f8: LoadField: r0 = r1->field_2b
    //     0x9901f8: ldur            w0, [x1, #0x2b]
    // 0x9901fc: DecompressPointer r0
    //     0x9901fc: add             x0, x0, HEAP, lsl #32
    // 0x990200: r16 = Instance_Color
    //     0x990200: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x990204: r30 = 16.000000
    //     0x990204: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x990208: ldr             lr, [lr, #0x188]
    // 0x99020c: stp             lr, x16, [SP]
    // 0x990210: mov             x1, x0
    // 0x990214: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990214: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990218: ldr             x4, [x4, #0x9b8]
    // 0x99021c: r0 = copyWith()
    //     0x99021c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990220: stur            x0, [fp, #-0x38]
    // 0x990224: r0 = TextSpan()
    //     0x990224: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x990228: mov             x3, x0
    // 0x99022c: r0 = "OFF"
    //     0x99022c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0x990230: ldr             x0, [x0, #0x348]
    // 0x990234: stur            x3, [fp, #-0x60]
    // 0x990238: StoreField: r3->field_b = r0
    //     0x990238: stur            w0, [x3, #0xb]
    // 0x99023c: r0 = Instance__DeferringMouseCursor
    //     0x99023c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x990240: ArrayStore: r3[0] = r0  ; List_4
    //     0x990240: stur            w0, [x3, #0x17]
    // 0x990244: ldur            x1, [fp, #-0x38]
    // 0x990248: StoreField: r3->field_7 = r1
    //     0x990248: stur            w1, [x3, #7]
    // 0x99024c: r1 = Null
    //     0x99024c: mov             x1, NULL
    // 0x990250: r2 = 2
    //     0x990250: movz            x2, #0x2
    // 0x990254: r0 = AllocateArray()
    //     0x990254: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990258: mov             x2, x0
    // 0x99025c: ldur            x0, [fp, #-0x60]
    // 0x990260: stur            x2, [fp, #-0x38]
    // 0x990264: StoreField: r2->field_f = r0
    //     0x990264: stur            w0, [x2, #0xf]
    // 0x990268: r1 = <InlineSpan>
    //     0x990268: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x99026c: ldr             x1, [x1, #0xe40]
    // 0x990270: r0 = AllocateGrowableArray()
    //     0x990270: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x990274: mov             x1, x0
    // 0x990278: ldur            x0, [fp, #-0x38]
    // 0x99027c: stur            x1, [fp, #-0x60]
    // 0x990280: StoreField: r1->field_f = r0
    //     0x990280: stur            w0, [x1, #0xf]
    // 0x990284: r0 = 2
    //     0x990284: movz            x0, #0x2
    // 0x990288: StoreField: r1->field_b = r0
    //     0x990288: stur            w0, [x1, #0xb]
    // 0x99028c: r0 = TextSpan()
    //     0x99028c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x990290: mov             x1, x0
    // 0x990294: ldur            x0, [fp, #-0x20]
    // 0x990298: stur            x1, [fp, #-0x38]
    // 0x99029c: StoreField: r1->field_b = r0
    //     0x99029c: stur            w0, [x1, #0xb]
    // 0x9902a0: ldur            x0, [fp, #-0x60]
    // 0x9902a4: StoreField: r1->field_f = r0
    //     0x9902a4: stur            w0, [x1, #0xf]
    // 0x9902a8: r0 = Instance__DeferringMouseCursor
    //     0x9902a8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x9902ac: ArrayStore: r1[0] = r0  ; List_4
    //     0x9902ac: stur            w0, [x1, #0x17]
    // 0x9902b0: ldur            x0, [fp, #-0x30]
    // 0x9902b4: StoreField: r1->field_7 = r0
    //     0x9902b4: stur            w0, [x1, #7]
    // 0x9902b8: r0 = RichText()
    //     0x9902b8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x9902bc: stur            x0, [fp, #-0x20]
    // 0x9902c0: r16 = Instance_TextAlign
    //     0x9902c0: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x9902c4: str             x16, [SP]
    // 0x9902c8: mov             x1, x0
    // 0x9902cc: ldur            x2, [fp, #-0x38]
    // 0x9902d0: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0x9902d0: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0x9902d4: ldr             x4, [x4, #0x350]
    // 0x9902d8: r0 = RichText()
    //     0x9902d8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x9902dc: r1 = Null
    //     0x9902dc: mov             x1, NULL
    // 0x9902e0: r2 = 6
    //     0x9902e0: movz            x2, #0x6
    // 0x9902e4: r0 = AllocateArray()
    //     0x9902e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9902e8: mov             x2, x0
    // 0x9902ec: ldur            x0, [fp, #-0x10]
    // 0x9902f0: stur            x2, [fp, #-0x30]
    // 0x9902f4: StoreField: r2->field_f = r0
    //     0x9902f4: stur            w0, [x2, #0xf]
    // 0x9902f8: r16 = Instance_VerticalDivider
    //     0x9902f8: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0x9902fc: ldr             x16, [x16, #0x760]
    // 0x990300: StoreField: r2->field_13 = r16
    //     0x990300: stur            w16, [x2, #0x13]
    // 0x990304: ldur            x0, [fp, #-0x20]
    // 0x990308: ArrayStore: r2[0] = r0  ; List_4
    //     0x990308: stur            w0, [x2, #0x17]
    // 0x99030c: r1 = <Widget>
    //     0x99030c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x990310: r0 = AllocateGrowableArray()
    //     0x990310: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x990314: mov             x1, x0
    // 0x990318: ldur            x0, [fp, #-0x30]
    // 0x99031c: stur            x1, [fp, #-0x10]
    // 0x990320: StoreField: r1->field_f = r0
    //     0x990320: stur            w0, [x1, #0xf]
    // 0x990324: r2 = 6
    //     0x990324: movz            x2, #0x6
    // 0x990328: StoreField: r1->field_b = r2
    //     0x990328: stur            w2, [x1, #0xb]
    // 0x99032c: r0 = Row()
    //     0x99032c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x990330: mov             x1, x0
    // 0x990334: r0 = Instance_Axis
    //     0x990334: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x990338: stur            x1, [fp, #-0x20]
    // 0x99033c: StoreField: r1->field_f = r0
    //     0x99033c: stur            w0, [x1, #0xf]
    // 0x990340: r0 = Instance_MainAxisAlignment
    //     0x990340: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x990344: ldr             x0, [x0, #0xa8]
    // 0x990348: StoreField: r1->field_13 = r0
    //     0x990348: stur            w0, [x1, #0x13]
    // 0x99034c: r0 = Instance_MainAxisSize
    //     0x99034c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x990350: ldr             x0, [x0, #0xa10]
    // 0x990354: ArrayStore: r1[0] = r0  ; List_4
    //     0x990354: stur            w0, [x1, #0x17]
    // 0x990358: r2 = Instance_CrossAxisAlignment
    //     0x990358: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x99035c: ldr             x2, [x2, #0xa18]
    // 0x990360: StoreField: r1->field_1b = r2
    //     0x990360: stur            w2, [x1, #0x1b]
    // 0x990364: r3 = Instance_VerticalDirection
    //     0x990364: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x990368: ldr             x3, [x3, #0xa20]
    // 0x99036c: StoreField: r1->field_23 = r3
    //     0x99036c: stur            w3, [x1, #0x23]
    // 0x990370: r4 = Instance_Clip
    //     0x990370: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x990374: ldr             x4, [x4, #0x38]
    // 0x990378: StoreField: r1->field_2b = r4
    //     0x990378: stur            w4, [x1, #0x2b]
    // 0x99037c: StoreField: r1->field_2f = rZR
    //     0x99037c: stur            xzr, [x1, #0x2f]
    // 0x990380: ldur            x5, [fp, #-0x10]
    // 0x990384: StoreField: r1->field_b = r5
    //     0x990384: stur            w5, [x1, #0xb]
    // 0x990388: r0 = Padding()
    //     0x990388: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x99038c: mov             x1, x0
    // 0x990390: r0 = Instance_EdgeInsets
    //     0x990390: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0x990394: ldr             x0, [x0, #0x358]
    // 0x990398: stur            x1, [fp, #-0x10]
    // 0x99039c: StoreField: r1->field_f = r0
    //     0x99039c: stur            w0, [x1, #0xf]
    // 0x9903a0: ldur            x0, [fp, #-0x20]
    // 0x9903a4: StoreField: r1->field_b = r0
    //     0x9903a4: stur            w0, [x1, #0xb]
    // 0x9903a8: r0 = IntrinsicHeight()
    //     0x9903a8: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x9903ac: mov             x1, x0
    // 0x9903b0: ldur            x0, [fp, #-0x10]
    // 0x9903b4: stur            x1, [fp, #-0x20]
    // 0x9903b8: StoreField: r1->field_b = r0
    //     0x9903b8: stur            w0, [x1, #0xb]
    // 0x9903bc: r0 = Container()
    //     0x9903bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9903c0: stur            x0, [fp, #-0x10]
    // 0x9903c4: r16 = 100.000000
    //     0x9903c4: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x9903c8: ldur            lr, [fp, #-0x18]
    // 0x9903cc: stp             lr, x16, [SP, #8]
    // 0x9903d0: ldur            x16, [fp, #-0x20]
    // 0x9903d4: str             x16, [SP]
    // 0x9903d8: mov             x1, x0
    // 0x9903dc: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x9903dc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x9903e0: ldr             x4, [x4, #0xc78]
    // 0x9903e4: r0 = Container()
    //     0x9903e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9903e8: r1 = <Path>
    //     0x9903e8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0x9903ec: ldr             x1, [x1, #0xd30]
    // 0x9903f0: r0 = MovieTicketClipper()
    //     0x9903f0: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0x9903f4: stur            x0, [fp, #-0x18]
    // 0x9903f8: r0 = ClipPath()
    //     0x9903f8: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0x9903fc: mov             x3, x0
    // 0x990400: ldur            x0, [fp, #-0x18]
    // 0x990404: stur            x3, [fp, #-0x20]
    // 0x990408: StoreField: r3->field_f = r0
    //     0x990408: stur            w0, [x3, #0xf]
    // 0x99040c: r0 = Instance_Clip
    //     0x99040c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x990410: ldr             x0, [x0, #0x138]
    // 0x990414: StoreField: r3->field_13 = r0
    //     0x990414: stur            w0, [x3, #0x13]
    // 0x990418: ldur            x0, [fp, #-0x10]
    // 0x99041c: StoreField: r3->field_b = r0
    //     0x99041c: stur            w0, [x3, #0xb]
    // 0x990420: ldur            x0, [fp, #-8]
    // 0x990424: LoadField: r1 = r0->field_b
    //     0x990424: ldur            w1, [x0, #0xb]
    // 0x990428: DecompressPointer r1
    //     0x990428: add             x1, x1, HEAP, lsl #32
    // 0x99042c: cmp             w1, NULL
    // 0x990430: b.eq            #0x990610
    // 0x990434: LoadField: r0 = r1->field_f
    //     0x990434: ldur            w0, [x1, #0xf]
    // 0x990438: DecompressPointer r0
    //     0x990438: add             x0, x0, HEAP, lsl #32
    // 0x99043c: LoadField: r2 = r0->field_b
    //     0x99043c: ldur            w2, [x0, #0xb]
    // 0x990440: DecompressPointer r2
    //     0x990440: add             x2, x2, HEAP, lsl #32
    // 0x990444: cmp             w2, NULL
    // 0x990448: b.ne            #0x990454
    // 0x99044c: r0 = Null
    //     0x99044c: mov             x0, NULL
    // 0x990450: b               #0x990478
    // 0x990454: LoadField: r0 = r2->field_1f
    //     0x990454: ldur            w0, [x2, #0x1f]
    // 0x990458: DecompressPointer r0
    //     0x990458: add             x0, x0, HEAP, lsl #32
    // 0x99045c: cmp             w0, NULL
    // 0x990460: b.ne            #0x99046c
    // 0x990464: r0 = Null
    //     0x990464: mov             x0, NULL
    // 0x990468: b               #0x990478
    // 0x99046c: LoadField: r2 = r0->field_f
    //     0x99046c: ldur            w2, [x0, #0xf]
    // 0x990470: DecompressPointer r2
    //     0x990470: add             x2, x2, HEAP, lsl #32
    // 0x990474: mov             x0, x2
    // 0x990478: cmp             w0, NULL
    // 0x99047c: b.ne            #0x9904dc
    // 0x990480: LoadField: r0 = r1->field_b
    //     0x990480: ldur            w0, [x1, #0xb]
    // 0x990484: DecompressPointer r0
    //     0x990484: add             x0, x0, HEAP, lsl #32
    // 0x990488: cmp             w0, NULL
    // 0x99048c: b.ne            #0x990498
    // 0x990490: r0 = Null
    //     0x990490: mov             x0, NULL
    // 0x990494: b               #0x9904b8
    // 0x990498: LoadField: r1 = r0->field_4b
    //     0x990498: ldur            w1, [x0, #0x4b]
    // 0x99049c: DecompressPointer r1
    //     0x99049c: add             x1, x1, HEAP, lsl #32
    // 0x9904a0: cmp             w1, NULL
    // 0x9904a4: b.ne            #0x9904b0
    // 0x9904a8: r0 = Null
    //     0x9904a8: mov             x0, NULL
    // 0x9904ac: b               #0x9904b8
    // 0x9904b0: LoadField: r0 = r1->field_f
    //     0x9904b0: ldur            w0, [x1, #0xf]
    // 0x9904b4: DecompressPointer r0
    //     0x9904b4: add             x0, x0, HEAP, lsl #32
    // 0x9904b8: cmp             w0, NULL
    // 0x9904bc: b.ne            #0x9904c8
    // 0x9904c0: r0 = 0
    //     0x9904c0: movz            x0, #0
    // 0x9904c4: b               #0x9904ec
    // 0x9904c8: r1 = LoadInt32Instr(r0)
    //     0x9904c8: sbfx            x1, x0, #1, #0x1f
    //     0x9904cc: tbz             w0, #0, #0x9904d4
    //     0x9904d0: ldur            x1, [x0, #7]
    // 0x9904d4: mov             x0, x1
    // 0x9904d8: b               #0x9904ec
    // 0x9904dc: r1 = LoadInt32Instr(r0)
    //     0x9904dc: sbfx            x1, x0, #1, #0x1f
    //     0x9904e0: tbz             w0, #0, #0x9904e8
    //     0x9904e4: ldur            x1, [x0, #7]
    // 0x9904e8: mov             x0, x1
    // 0x9904ec: ldur            x2, [fp, #-0x28]
    // 0x9904f0: stur            x0, [fp, #-0x48]
    // 0x9904f4: r1 = Function '<anonymous closure>':.
    //     0x9904f4: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c030] AnonymousClosure: (0x990700), in [package:customer_app/app/presentation/custom_widgets/orders/bumper_coupon_timer_widget.dart] _BumperCouponTimerWidgetState::build (0x98f650)
    //     0x9904f8: ldr             x1, [x1, #0x30]
    // 0x9904fc: r0 = AllocateClosure()
    //     0x9904fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x990500: stur            x0, [fp, #-8]
    // 0x990504: r0 = CountdownTimer()
    //     0x990504: bl              #0x990638  ; AllocateCountdownTimerStub -> CountdownTimer (size=0x20)
    // 0x990508: mov             x3, x0
    // 0x99050c: ldur            x0, [fp, #-8]
    // 0x990510: stur            x3, [fp, #-0x10]
    // 0x990514: StoreField: r3->field_b = r0
    //     0x990514: stur            w0, [x3, #0xb]
    // 0x990518: ldur            x0, [fp, #-0x48]
    // 0x99051c: ArrayStore: r3[0] = r0  ; List_8
    //     0x99051c: stur            x0, [x3, #0x17]
    // 0x990520: r1 = Null
    //     0x990520: mov             x1, NULL
    // 0x990524: r2 = 6
    //     0x990524: movz            x2, #0x6
    // 0x990528: r0 = AllocateArray()
    //     0x990528: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99052c: mov             x2, x0
    // 0x990530: ldur            x0, [fp, #-0x20]
    // 0x990534: stur            x2, [fp, #-8]
    // 0x990538: StoreField: r2->field_f = r0
    //     0x990538: stur            w0, [x2, #0xf]
    // 0x99053c: r16 = Instance_SizedBox
    //     0x99053c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x990540: ldr             x16, [x16, #0x8f0]
    // 0x990544: StoreField: r2->field_13 = r16
    //     0x990544: stur            w16, [x2, #0x13]
    // 0x990548: ldur            x0, [fp, #-0x10]
    // 0x99054c: ArrayStore: r2[0] = r0  ; List_4
    //     0x99054c: stur            w0, [x2, #0x17]
    // 0x990550: r1 = <Widget>
    //     0x990550: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x990554: r0 = AllocateGrowableArray()
    //     0x990554: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x990558: mov             x1, x0
    // 0x99055c: ldur            x0, [fp, #-8]
    // 0x990560: stur            x1, [fp, #-0x10]
    // 0x990564: StoreField: r1->field_f = r0
    //     0x990564: stur            w0, [x1, #0xf]
    // 0x990568: r0 = 6
    //     0x990568: movz            x0, #0x6
    // 0x99056c: StoreField: r1->field_b = r0
    //     0x99056c: stur            w0, [x1, #0xb]
    // 0x990570: r0 = Column()
    //     0x990570: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x990574: mov             x1, x0
    // 0x990578: r0 = Instance_Axis
    //     0x990578: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x99057c: stur            x1, [fp, #-8]
    // 0x990580: StoreField: r1->field_f = r0
    //     0x990580: stur            w0, [x1, #0xf]
    // 0x990584: r0 = Instance_MainAxisAlignment
    //     0x990584: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x990588: ldr             x0, [x0, #0xab0]
    // 0x99058c: StoreField: r1->field_13 = r0
    //     0x99058c: stur            w0, [x1, #0x13]
    // 0x990590: r0 = Instance_MainAxisSize
    //     0x990590: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x990594: ldr             x0, [x0, #0xa10]
    // 0x990598: ArrayStore: r1[0] = r0  ; List_4
    //     0x990598: stur            w0, [x1, #0x17]
    // 0x99059c: r0 = Instance_CrossAxisAlignment
    //     0x99059c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9905a0: ldr             x0, [x0, #0xa18]
    // 0x9905a4: StoreField: r1->field_1b = r0
    //     0x9905a4: stur            w0, [x1, #0x1b]
    // 0x9905a8: r0 = Instance_VerticalDirection
    //     0x9905a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9905ac: ldr             x0, [x0, #0xa20]
    // 0x9905b0: StoreField: r1->field_23 = r0
    //     0x9905b0: stur            w0, [x1, #0x23]
    // 0x9905b4: r0 = Instance_Clip
    //     0x9905b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9905b8: ldr             x0, [x0, #0x38]
    // 0x9905bc: StoreField: r1->field_2b = r0
    //     0x9905bc: stur            w0, [x1, #0x2b]
    // 0x9905c0: StoreField: r1->field_2f = rZR
    //     0x9905c0: stur            xzr, [x1, #0x2f]
    // 0x9905c4: ldur            x0, [fp, #-0x10]
    // 0x9905c8: StoreField: r1->field_b = r0
    //     0x9905c8: stur            w0, [x1, #0xb]
    // 0x9905cc: r0 = Container()
    //     0x9905cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9905d0: stur            x0, [fp, #-0x10]
    // 0x9905d4: ldur            x16, [fp, #-0x40]
    // 0x9905d8: ldur            lr, [fp, #-8]
    // 0x9905dc: stp             lr, x16, [SP]
    // 0x9905e0: mov             x1, x0
    // 0x9905e4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x9905e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x9905e8: ldr             x4, [x4, #0x88]
    // 0x9905ec: r0 = Container()
    //     0x9905ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9905f0: ldur            x0, [fp, #-0x10]
    // 0x9905f4: LeaveFrame
    //     0x9905f4: mov             SP, fp
    //     0x9905f8: ldp             fp, lr, [SP], #0x10
    // 0x9905fc: ret
    //     0x9905fc: ret             
    // 0x990600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x990600: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x990604: b               #0x98f678
    // 0x990608: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x990608: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99060c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99060c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x990610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x990610: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, CurrentRemainingTime?) {
    // ** addr: 0x990700, size: 0x164
    // 0x990700: EnterFrame
    //     0x990700: stp             fp, lr, [SP, #-0x10]!
    //     0x990704: mov             fp, SP
    // 0x990708: AllocStack(0x18)
    //     0x990708: sub             SP, SP, #0x18
    // 0x99070c: SetupParameters()
    //     0x99070c: ldr             x0, [fp, #0x20]
    //     0x990710: ldur            w2, [x0, #0x17]
    //     0x990714: add             x2, x2, HEAP, lsl #32
    // 0x990718: CheckStackOverflow
    //     0x990718: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99071c: cmp             SP, x16
    //     0x990720: b.ls            #0x990858
    // 0x990724: ldr             x0, [fp, #0x10]
    // 0x990728: cmp             w0, NULL
    // 0x99072c: b.ne            #0x990834
    // 0x990730: r0 = LoadStaticField(0x878)
    //     0x990730: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x990734: ldr             x0, [x0, #0x10f0]
    // 0x990738: cmp             w0, NULL
    // 0x99073c: b.eq            #0x990860
    // 0x990740: LoadField: r3 = r0->field_53
    //     0x990740: ldur            w3, [x0, #0x53]
    // 0x990744: DecompressPointer r3
    //     0x990744: add             x3, x3, HEAP, lsl #32
    // 0x990748: stur            x3, [fp, #-0x10]
    // 0x99074c: LoadField: r0 = r3->field_7
    //     0x99074c: ldur            w0, [x3, #7]
    // 0x990750: DecompressPointer r0
    //     0x990750: add             x0, x0, HEAP, lsl #32
    // 0x990754: stur            x0, [fp, #-8]
    // 0x990758: r1 = Function '<anonymous closure>':.
    //     0x990758: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c038] AnonymousClosure: (0x991510), in [package:customer_app/app/presentation/custom_widgets/orders/bumper_coupon_timer_widget.dart] _BumperCouponTimerWidgetState::build (0x98f650)
    //     0x99075c: ldr             x1, [x1, #0x38]
    // 0x990760: r0 = AllocateClosure()
    //     0x990760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x990764: ldur            x2, [fp, #-8]
    // 0x990768: mov             x3, x0
    // 0x99076c: r1 = Null
    //     0x99076c: mov             x1, NULL
    // 0x990770: stur            x3, [fp, #-8]
    // 0x990774: cmp             w2, NULL
    // 0x990778: b.eq            #0x990798
    // 0x99077c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x99077c: ldur            w4, [x2, #0x17]
    // 0x990780: DecompressPointer r4
    //     0x990780: add             x4, x4, HEAP, lsl #32
    // 0x990784: r8 = X0
    //     0x990784: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x990788: LoadField: r9 = r4->field_7
    //     0x990788: ldur            x9, [x4, #7]
    // 0x99078c: r3 = Null
    //     0x99078c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c040] Null
    //     0x990790: ldr             x3, [x3, #0x40]
    // 0x990794: blr             x9
    // 0x990798: ldur            x0, [fp, #-0x10]
    // 0x99079c: LoadField: r1 = r0->field_b
    //     0x99079c: ldur            w1, [x0, #0xb]
    // 0x9907a0: LoadField: r2 = r0->field_f
    //     0x9907a0: ldur            w2, [x0, #0xf]
    // 0x9907a4: DecompressPointer r2
    //     0x9907a4: add             x2, x2, HEAP, lsl #32
    // 0x9907a8: LoadField: r3 = r2->field_b
    //     0x9907a8: ldur            w3, [x2, #0xb]
    // 0x9907ac: r2 = LoadInt32Instr(r1)
    //     0x9907ac: sbfx            x2, x1, #1, #0x1f
    // 0x9907b0: stur            x2, [fp, #-0x18]
    // 0x9907b4: r1 = LoadInt32Instr(r3)
    //     0x9907b4: sbfx            x1, x3, #1, #0x1f
    // 0x9907b8: cmp             x2, x1
    // 0x9907bc: b.ne            #0x9907c8
    // 0x9907c0: mov             x1, x0
    // 0x9907c4: r0 = _growToNextCapacity()
    //     0x9907c4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9907c8: ldur            x0, [fp, #-0x10]
    // 0x9907cc: ldur            x2, [fp, #-0x18]
    // 0x9907d0: add             x1, x2, #1
    // 0x9907d4: lsl             x3, x1, #1
    // 0x9907d8: StoreField: r0->field_b = r3
    //     0x9907d8: stur            w3, [x0, #0xb]
    // 0x9907dc: LoadField: r1 = r0->field_f
    //     0x9907dc: ldur            w1, [x0, #0xf]
    // 0x9907e0: DecompressPointer r1
    //     0x9907e0: add             x1, x1, HEAP, lsl #32
    // 0x9907e4: ldur            x0, [fp, #-8]
    // 0x9907e8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x9907e8: add             x25, x1, x2, lsl #2
    //     0x9907ec: add             x25, x25, #0xf
    //     0x9907f0: str             w0, [x25]
    //     0x9907f4: tbz             w0, #0, #0x990810
    //     0x9907f8: ldurb           w16, [x1, #-1]
    //     0x9907fc: ldurb           w17, [x0, #-1]
    //     0x990800: and             x16, x17, x16, lsr #2
    //     0x990804: tst             x16, HEAP, lsr #32
    //     0x990808: b.eq            #0x990810
    //     0x99080c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x990810: r0 = Container()
    //     0x990810: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x990814: mov             x1, x0
    // 0x990818: stur            x0, [fp, #-8]
    // 0x99081c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99081c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x990820: r0 = Container()
    //     0x990820: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x990824: ldur            x0, [fp, #-8]
    // 0x990828: LeaveFrame
    //     0x990828: mov             SP, fp
    //     0x99082c: ldp             fp, lr, [SP], #0x10
    // 0x990830: ret
    //     0x990830: ret             
    // 0x990834: LoadField: r1 = r2->field_f
    //     0x990834: ldur            w1, [x2, #0xf]
    // 0x990838: DecompressPointer r1
    //     0x990838: add             x1, x1, HEAP, lsl #32
    // 0x99083c: LoadField: r3 = r2->field_13
    //     0x99083c: ldur            w3, [x2, #0x13]
    // 0x990840: DecompressPointer r3
    //     0x990840: add             x3, x3, HEAP, lsl #32
    // 0x990844: mov             x2, x0
    // 0x990848: r0 = timerCardCosmetic()
    //     0x990848: bl              #0x990864  ; [package:customer_app/app/presentation/custom_widgets/orders/bumper_coupon_timer_widget.dart] _BumperCouponTimerWidgetState::timerCardCosmetic
    // 0x99084c: LeaveFrame
    //     0x99084c: mov             SP, fp
    //     0x990850: ldp             fp, lr, [SP], #0x10
    // 0x990854: ret
    //     0x990854: ret             
    // 0x990858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x990858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99085c: b               #0x990724
    // 0x990860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x990860: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ timerCardCosmetic(/* No info */) {
    // ** addr: 0x990864, size: 0xcac
    // 0x990864: EnterFrame
    //     0x990864: stp             fp, lr, [SP, #-0x10]!
    //     0x990868: mov             fp, SP
    // 0x99086c: AllocStack(0x78)
    //     0x99086c: sub             SP, SP, #0x78
    // 0x990870: SetupParameters(_BumperCouponTimerWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0x990870: mov             x0, x1
    //     0x990874: stur            x1, [fp, #-8]
    //     0x990878: mov             x1, x3
    //     0x99087c: stur            x2, [fp, #-0x10]
    //     0x990880: stur            x3, [fp, #-0x18]
    // 0x990884: CheckStackOverflow
    //     0x990884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x990888: cmp             SP, x16
    //     0x99088c: b.ls            #0x9914fc
    // 0x990890: LoadField: r3 = r0->field_b
    //     0x990890: ldur            w3, [x0, #0xb]
    // 0x990894: DecompressPointer r3
    //     0x990894: add             x3, x3, HEAP, lsl #32
    // 0x990898: cmp             w3, NULL
    // 0x99089c: b.eq            #0x991504
    // 0x9908a0: ArrayLoad: d0 = r3[0]  ; List_8
    //     0x9908a0: ldur            d0, [x3, #0x17]
    // 0x9908a4: stur            d0, [fp, #-0x58]
    // 0x9908a8: r0 = Radius()
    //     0x9908a8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x9908ac: ldur            d0, [fp, #-0x58]
    // 0x9908b0: stur            x0, [fp, #-0x20]
    // 0x9908b4: StoreField: r0->field_7 = d0
    //     0x9908b4: stur            d0, [x0, #7]
    // 0x9908b8: StoreField: r0->field_f = d0
    //     0x9908b8: stur            d0, [x0, #0xf]
    // 0x9908bc: r0 = BorderRadius()
    //     0x9908bc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x9908c0: mov             x2, x0
    // 0x9908c4: ldur            x0, [fp, #-0x20]
    // 0x9908c8: stur            x2, [fp, #-0x28]
    // 0x9908cc: StoreField: r2->field_7 = r0
    //     0x9908cc: stur            w0, [x2, #7]
    // 0x9908d0: StoreField: r2->field_b = r0
    //     0x9908d0: stur            w0, [x2, #0xb]
    // 0x9908d4: StoreField: r2->field_f = r0
    //     0x9908d4: stur            w0, [x2, #0xf]
    // 0x9908d8: StoreField: r2->field_13 = r0
    //     0x9908d8: stur            w0, [x2, #0x13]
    // 0x9908dc: ldur            x1, [fp, #-0x18]
    // 0x9908e0: r0 = of()
    //     0x9908e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9908e4: LoadField: r1 = r0->field_5b
    //     0x9908e4: ldur            w1, [x0, #0x5b]
    // 0x9908e8: DecompressPointer r1
    //     0x9908e8: add             x1, x1, HEAP, lsl #32
    // 0x9908ec: r0 = LoadClassIdInstr(r1)
    //     0x9908ec: ldur            x0, [x1, #-1]
    //     0x9908f0: ubfx            x0, x0, #0xc, #0x14
    // 0x9908f4: d0 = 0.100000
    //     0x9908f4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x9908f8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x9908f8: sub             lr, x0, #0xffa
    //     0x9908fc: ldr             lr, [x21, lr, lsl #3]
    //     0x990900: blr             lr
    // 0x990904: mov             x2, x0
    // 0x990908: r1 = Null
    //     0x990908: mov             x1, NULL
    // 0x99090c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x99090c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x990910: r0 = Border.all()
    //     0x990910: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x990914: stur            x0, [fp, #-0x20]
    // 0x990918: r0 = BoxDecoration()
    //     0x990918: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x99091c: mov             x3, x0
    // 0x990920: ldur            x0, [fp, #-0x20]
    // 0x990924: stur            x3, [fp, #-0x30]
    // 0x990928: StoreField: r3->field_f = r0
    //     0x990928: stur            w0, [x3, #0xf]
    // 0x99092c: ldur            x0, [fp, #-0x28]
    // 0x990930: StoreField: r3->field_13 = r0
    //     0x990930: stur            w0, [x3, #0x13]
    // 0x990934: r0 = Instance_BoxShape
    //     0x990934: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x990938: ldr             x0, [x0, #0x80]
    // 0x99093c: StoreField: r3->field_23 = r0
    //     0x99093c: stur            w0, [x3, #0x23]
    // 0x990940: ldur            x4, [fp, #-0x10]
    // 0x990944: LoadField: r5 = r4->field_b
    //     0x990944: ldur            w5, [x4, #0xb]
    // 0x990948: DecompressPointer r5
    //     0x990948: add             x5, x5, HEAP, lsl #32
    // 0x99094c: stur            x5, [fp, #-0x20]
    // 0x990950: cmp             w5, NULL
    // 0x990954: b.eq            #0x990a3c
    // 0x990958: r1 = LoadInt32Instr(r5)
    //     0x990958: sbfx            x1, x5, #1, #0x1f
    //     0x99095c: tbz             w5, #0, #0x990964
    //     0x990960: ldur            x1, [x5, #7]
    // 0x990964: cmp             x1, #0xa
    // 0x990968: b.ge            #0x990994
    // 0x99096c: r1 = Null
    //     0x99096c: mov             x1, NULL
    // 0x990970: r2 = 4
    //     0x990970: movz            x2, #0x4
    // 0x990974: r0 = AllocateArray()
    //     0x990974: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990978: r16 = "0"
    //     0x990978: ldr             x16, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0x99097c: StoreField: r0->field_f = r16
    //     0x99097c: stur            w16, [x0, #0xf]
    // 0x990980: ldur            x3, [fp, #-0x20]
    // 0x990984: StoreField: r0->field_13 = r3
    //     0x990984: stur            w3, [x0, #0x13]
    // 0x990988: str             x0, [SP]
    // 0x99098c: r0 = _interpolate()
    //     0x99098c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x990990: b               #0x9909bc
    // 0x990994: mov             x3, x5
    // 0x990998: r1 = Null
    //     0x990998: mov             x1, NULL
    // 0x99099c: r2 = 4
    //     0x99099c: movz            x2, #0x4
    // 0x9909a0: r0 = AllocateArray()
    //     0x9909a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9909a4: r16 = " "
    //     0x9909a4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9909a8: StoreField: r0->field_f = r16
    //     0x9909a8: stur            w16, [x0, #0xf]
    // 0x9909ac: ldur            x1, [fp, #-0x20]
    // 0x9909b0: StoreField: r0->field_13 = r1
    //     0x9909b0: stur            w1, [x0, #0x13]
    // 0x9909b4: str             x0, [SP]
    // 0x9909b8: r0 = _interpolate()
    //     0x9909b8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9909bc: ldur            x1, [fp, #-0x18]
    // 0x9909c0: stur            x0, [fp, #-0x20]
    // 0x9909c4: r0 = of()
    //     0x9909c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9909c8: LoadField: r1 = r0->field_87
    //     0x9909c8: ldur            w1, [x0, #0x87]
    // 0x9909cc: DecompressPointer r1
    //     0x9909cc: add             x1, x1, HEAP, lsl #32
    // 0x9909d0: LoadField: r0 = r1->field_2b
    //     0x9909d0: ldur            w0, [x1, #0x2b]
    // 0x9909d4: DecompressPointer r0
    //     0x9909d4: add             x0, x0, HEAP, lsl #32
    // 0x9909d8: r16 = Instance_Color
    //     0x9909d8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9909dc: r30 = 16.000000
    //     0x9909dc: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9909e0: ldr             lr, [lr, #0x188]
    // 0x9909e4: stp             lr, x16, [SP]
    // 0x9909e8: mov             x1, x0
    // 0x9909ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9909ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9909f0: ldr             x4, [x4, #0x9b8]
    // 0x9909f4: r0 = copyWith()
    //     0x9909f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9909f8: stur            x0, [fp, #-0x28]
    // 0x9909fc: r0 = Text()
    //     0x9909fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990a00: mov             x1, x0
    // 0x990a04: ldur            x0, [fp, #-0x20]
    // 0x990a08: stur            x1, [fp, #-0x38]
    // 0x990a0c: StoreField: r1->field_b = r0
    //     0x990a0c: stur            w0, [x1, #0xb]
    // 0x990a10: ldur            x0, [fp, #-0x28]
    // 0x990a14: StoreField: r1->field_13 = r0
    //     0x990a14: stur            w0, [x1, #0x13]
    // 0x990a18: r0 = Align()
    //     0x990a18: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x990a1c: mov             x1, x0
    // 0x990a20: r0 = Instance_Alignment
    //     0x990a20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x990a24: ldr             x0, [x0, #0xb10]
    // 0x990a28: StoreField: r1->field_f = r0
    //     0x990a28: stur            w0, [x1, #0xf]
    // 0x990a2c: ldur            x2, [fp, #-0x38]
    // 0x990a30: StoreField: r1->field_b = r2
    //     0x990a30: stur            w2, [x1, #0xb]
    // 0x990a34: mov             x3, x1
    // 0x990a38: b               #0x990ac0
    // 0x990a3c: r0 = Instance_Alignment
    //     0x990a3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x990a40: ldr             x0, [x0, #0xb10]
    // 0x990a44: ldur            x1, [fp, #-0x18]
    // 0x990a48: r0 = of()
    //     0x990a48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990a4c: LoadField: r1 = r0->field_87
    //     0x990a4c: ldur            w1, [x0, #0x87]
    // 0x990a50: DecompressPointer r1
    //     0x990a50: add             x1, x1, HEAP, lsl #32
    // 0x990a54: LoadField: r0 = r1->field_2b
    //     0x990a54: ldur            w0, [x1, #0x2b]
    // 0x990a58: DecompressPointer r0
    //     0x990a58: add             x0, x0, HEAP, lsl #32
    // 0x990a5c: r16 = Instance_Color
    //     0x990a5c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x990a60: r30 = 16.000000
    //     0x990a60: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x990a64: ldr             lr, [lr, #0x188]
    // 0x990a68: stp             lr, x16, [SP]
    // 0x990a6c: mov             x1, x0
    // 0x990a70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990a70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990a74: ldr             x4, [x4, #0x9b8]
    // 0x990a78: r0 = copyWith()
    //     0x990a78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990a7c: stur            x0, [fp, #-0x20]
    // 0x990a80: r0 = Text()
    //     0x990a80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990a84: mov             x1, x0
    // 0x990a88: r0 = "00"
    //     0x990a88: add             x0, PP, #0x26, lsl #12  ; [pp+0x261d8] "00"
    //     0x990a8c: ldr             x0, [x0, #0x1d8]
    // 0x990a90: stur            x1, [fp, #-0x28]
    // 0x990a94: StoreField: r1->field_b = r0
    //     0x990a94: stur            w0, [x1, #0xb]
    // 0x990a98: ldur            x2, [fp, #-0x20]
    // 0x990a9c: StoreField: r1->field_13 = r2
    //     0x990a9c: stur            w2, [x1, #0x13]
    // 0x990aa0: r0 = Align()
    //     0x990aa0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x990aa4: mov             x1, x0
    // 0x990aa8: r0 = Instance_Alignment
    //     0x990aa8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x990aac: ldr             x0, [x0, #0xb10]
    // 0x990ab0: StoreField: r1->field_f = r0
    //     0x990ab0: stur            w0, [x1, #0xf]
    // 0x990ab4: ldur            x2, [fp, #-0x28]
    // 0x990ab8: StoreField: r1->field_b = r2
    //     0x990ab8: stur            w2, [x1, #0xb]
    // 0x990abc: mov             x3, x1
    // 0x990ac0: ldur            x2, [fp, #-8]
    // 0x990ac4: ldur            x1, [fp, #-0x10]
    // 0x990ac8: stur            x3, [fp, #-0x20]
    // 0x990acc: r0 = Container()
    //     0x990acc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x990ad0: stur            x0, [fp, #-0x28]
    // 0x990ad4: r16 = 30.000000
    //     0x990ad4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0x990ad8: ldr             x16, [x16, #0x768]
    // 0x990adc: r30 = 50.000000
    //     0x990adc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x990ae0: ldr             lr, [lr, #0xa90]
    // 0x990ae4: stp             lr, x16, [SP, #0x10]
    // 0x990ae8: ldur            x16, [fp, #-0x30]
    // 0x990aec: ldur            lr, [fp, #-0x20]
    // 0x990af0: stp             lr, x16, [SP]
    // 0x990af4: mov             x1, x0
    // 0x990af8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x990af8: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x990afc: ldr             x4, [x4, #0x8c0]
    // 0x990b00: r0 = Container()
    //     0x990b00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x990b04: ldur            x1, [fp, #-0x18]
    // 0x990b08: r0 = of()
    //     0x990b08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990b0c: LoadField: r1 = r0->field_87
    //     0x990b0c: ldur            w1, [x0, #0x87]
    // 0x990b10: DecompressPointer r1
    //     0x990b10: add             x1, x1, HEAP, lsl #32
    // 0x990b14: LoadField: r0 = r1->field_2b
    //     0x990b14: ldur            w0, [x1, #0x2b]
    // 0x990b18: DecompressPointer r0
    //     0x990b18: add             x0, x0, HEAP, lsl #32
    // 0x990b1c: r16 = Instance_Color
    //     0x990b1c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x990b20: r30 = 12.000000
    //     0x990b20: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x990b24: ldr             lr, [lr, #0x9e8]
    // 0x990b28: stp             lr, x16, [SP]
    // 0x990b2c: mov             x1, x0
    // 0x990b30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990b30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990b34: ldr             x4, [x4, #0x9b8]
    // 0x990b38: r0 = copyWith()
    //     0x990b38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990b3c: stur            x0, [fp, #-0x20]
    // 0x990b40: r0 = Text()
    //     0x990b40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990b44: mov             x3, x0
    // 0x990b48: r0 = "H"
    //     0x990b48: add             x0, PP, #0x11, lsl #12  ; [pp+0x117f8] "H"
    //     0x990b4c: ldr             x0, [x0, #0x7f8]
    // 0x990b50: stur            x3, [fp, #-0x30]
    // 0x990b54: StoreField: r3->field_b = r0
    //     0x990b54: stur            w0, [x3, #0xb]
    // 0x990b58: ldur            x0, [fp, #-0x20]
    // 0x990b5c: StoreField: r3->field_13 = r0
    //     0x990b5c: stur            w0, [x3, #0x13]
    // 0x990b60: r1 = Null
    //     0x990b60: mov             x1, NULL
    // 0x990b64: r2 = 4
    //     0x990b64: movz            x2, #0x4
    // 0x990b68: r0 = AllocateArray()
    //     0x990b68: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990b6c: mov             x2, x0
    // 0x990b70: ldur            x0, [fp, #-0x28]
    // 0x990b74: stur            x2, [fp, #-0x20]
    // 0x990b78: StoreField: r2->field_f = r0
    //     0x990b78: stur            w0, [x2, #0xf]
    // 0x990b7c: ldur            x0, [fp, #-0x30]
    // 0x990b80: StoreField: r2->field_13 = r0
    //     0x990b80: stur            w0, [x2, #0x13]
    // 0x990b84: r1 = <Widget>
    //     0x990b84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x990b88: r0 = AllocateGrowableArray()
    //     0x990b88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x990b8c: mov             x1, x0
    // 0x990b90: ldur            x0, [fp, #-0x20]
    // 0x990b94: stur            x1, [fp, #-0x28]
    // 0x990b98: StoreField: r1->field_f = r0
    //     0x990b98: stur            w0, [x1, #0xf]
    // 0x990b9c: r2 = 4
    //     0x990b9c: movz            x2, #0x4
    // 0x990ba0: StoreField: r1->field_b = r2
    //     0x990ba0: stur            w2, [x1, #0xb]
    // 0x990ba4: r0 = Column()
    //     0x990ba4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x990ba8: mov             x2, x0
    // 0x990bac: r0 = Instance_Axis
    //     0x990bac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x990bb0: stur            x2, [fp, #-0x20]
    // 0x990bb4: StoreField: r2->field_f = r0
    //     0x990bb4: stur            w0, [x2, #0xf]
    // 0x990bb8: r3 = Instance_MainAxisAlignment
    //     0x990bb8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x990bbc: ldr             x3, [x3, #0xa08]
    // 0x990bc0: StoreField: r2->field_13 = r3
    //     0x990bc0: stur            w3, [x2, #0x13]
    // 0x990bc4: r4 = Instance_MainAxisSize
    //     0x990bc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x990bc8: ldr             x4, [x4, #0xa10]
    // 0x990bcc: ArrayStore: r2[0] = r4  ; List_4
    //     0x990bcc: stur            w4, [x2, #0x17]
    // 0x990bd0: r5 = Instance_CrossAxisAlignment
    //     0x990bd0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x990bd4: ldr             x5, [x5, #0xa18]
    // 0x990bd8: StoreField: r2->field_1b = r5
    //     0x990bd8: stur            w5, [x2, #0x1b]
    // 0x990bdc: r6 = Instance_VerticalDirection
    //     0x990bdc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x990be0: ldr             x6, [x6, #0xa20]
    // 0x990be4: StoreField: r2->field_23 = r6
    //     0x990be4: stur            w6, [x2, #0x23]
    // 0x990be8: r7 = Instance_Clip
    //     0x990be8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x990bec: ldr             x7, [x7, #0x38]
    // 0x990bf0: StoreField: r2->field_2b = r7
    //     0x990bf0: stur            w7, [x2, #0x2b]
    // 0x990bf4: StoreField: r2->field_2f = rZR
    //     0x990bf4: stur            xzr, [x2, #0x2f]
    // 0x990bf8: ldur            x1, [fp, #-0x28]
    // 0x990bfc: StoreField: r2->field_b = r1
    //     0x990bfc: stur            w1, [x2, #0xb]
    // 0x990c00: ldur            x1, [fp, #-0x18]
    // 0x990c04: r0 = of()
    //     0x990c04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990c08: LoadField: r1 = r0->field_87
    //     0x990c08: ldur            w1, [x0, #0x87]
    // 0x990c0c: DecompressPointer r1
    //     0x990c0c: add             x1, x1, HEAP, lsl #32
    // 0x990c10: LoadField: r0 = r1->field_2b
    //     0x990c10: ldur            w0, [x1, #0x2b]
    // 0x990c14: DecompressPointer r0
    //     0x990c14: add             x0, x0, HEAP, lsl #32
    // 0x990c18: r16 = Instance_Color
    //     0x990c18: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x990c1c: r30 = 16.000000
    //     0x990c1c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x990c20: ldr             lr, [lr, #0x188]
    // 0x990c24: stp             lr, x16, [SP]
    // 0x990c28: mov             x1, x0
    // 0x990c2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990c2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990c30: ldr             x4, [x4, #0x9b8]
    // 0x990c34: r0 = copyWith()
    //     0x990c34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990c38: stur            x0, [fp, #-0x28]
    // 0x990c3c: r0 = Text()
    //     0x990c3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990c40: mov             x1, x0
    // 0x990c44: r0 = "   :   "
    //     0x990c44: add             x0, PP, #0x49, lsl #12  ; [pp+0x49770] "   :   "
    //     0x990c48: ldr             x0, [x0, #0x770]
    // 0x990c4c: stur            x1, [fp, #-0x30]
    // 0x990c50: StoreField: r1->field_b = r0
    //     0x990c50: stur            w0, [x1, #0xb]
    // 0x990c54: ldur            x2, [fp, #-0x28]
    // 0x990c58: StoreField: r1->field_13 = r2
    //     0x990c58: stur            w2, [x1, #0x13]
    // 0x990c5c: r0 = Padding()
    //     0x990c5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x990c60: mov             x1, x0
    // 0x990c64: r0 = Instance_EdgeInsets
    //     0x990c64: add             x0, PP, #0x49, lsl #12  ; [pp+0x49778] Obj!EdgeInsets@d573e1
    //     0x990c68: ldr             x0, [x0, #0x778]
    // 0x990c6c: stur            x1, [fp, #-0x28]
    // 0x990c70: StoreField: r1->field_f = r0
    //     0x990c70: stur            w0, [x1, #0xf]
    // 0x990c74: ldur            x2, [fp, #-0x30]
    // 0x990c78: StoreField: r1->field_b = r2
    //     0x990c78: stur            w2, [x1, #0xb]
    // 0x990c7c: ldur            x2, [fp, #-8]
    // 0x990c80: LoadField: r3 = r2->field_b
    //     0x990c80: ldur            w3, [x2, #0xb]
    // 0x990c84: DecompressPointer r3
    //     0x990c84: add             x3, x3, HEAP, lsl #32
    // 0x990c88: cmp             w3, NULL
    // 0x990c8c: b.eq            #0x991508
    // 0x990c90: ArrayLoad: d0 = r3[0]  ; List_8
    //     0x990c90: ldur            d0, [x3, #0x17]
    // 0x990c94: stur            d0, [fp, #-0x58]
    // 0x990c98: r0 = Radius()
    //     0x990c98: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x990c9c: ldur            d0, [fp, #-0x58]
    // 0x990ca0: stur            x0, [fp, #-0x30]
    // 0x990ca4: StoreField: r0->field_7 = d0
    //     0x990ca4: stur            d0, [x0, #7]
    // 0x990ca8: StoreField: r0->field_f = d0
    //     0x990ca8: stur            d0, [x0, #0xf]
    // 0x990cac: r0 = BorderRadius()
    //     0x990cac: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x990cb0: mov             x2, x0
    // 0x990cb4: ldur            x0, [fp, #-0x30]
    // 0x990cb8: stur            x2, [fp, #-0x38]
    // 0x990cbc: StoreField: r2->field_7 = r0
    //     0x990cbc: stur            w0, [x2, #7]
    // 0x990cc0: StoreField: r2->field_b = r0
    //     0x990cc0: stur            w0, [x2, #0xb]
    // 0x990cc4: StoreField: r2->field_f = r0
    //     0x990cc4: stur            w0, [x2, #0xf]
    // 0x990cc8: StoreField: r2->field_13 = r0
    //     0x990cc8: stur            w0, [x2, #0x13]
    // 0x990ccc: ldur            x1, [fp, #-0x18]
    // 0x990cd0: r0 = of()
    //     0x990cd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990cd4: LoadField: r1 = r0->field_5b
    //     0x990cd4: ldur            w1, [x0, #0x5b]
    // 0x990cd8: DecompressPointer r1
    //     0x990cd8: add             x1, x1, HEAP, lsl #32
    // 0x990cdc: r0 = LoadClassIdInstr(r1)
    //     0x990cdc: ldur            x0, [x1, #-1]
    //     0x990ce0: ubfx            x0, x0, #0xc, #0x14
    // 0x990ce4: d0 = 0.100000
    //     0x990ce4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x990ce8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x990ce8: sub             lr, x0, #0xffa
    //     0x990cec: ldr             lr, [x21, lr, lsl #3]
    //     0x990cf0: blr             lr
    // 0x990cf4: mov             x2, x0
    // 0x990cf8: r1 = Null
    //     0x990cf8: mov             x1, NULL
    // 0x990cfc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x990cfc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x990d00: r0 = Border.all()
    //     0x990d00: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x990d04: stur            x0, [fp, #-0x30]
    // 0x990d08: r0 = BoxDecoration()
    //     0x990d08: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x990d0c: mov             x3, x0
    // 0x990d10: ldur            x0, [fp, #-0x30]
    // 0x990d14: stur            x3, [fp, #-0x40]
    // 0x990d18: StoreField: r3->field_f = r0
    //     0x990d18: stur            w0, [x3, #0xf]
    // 0x990d1c: ldur            x0, [fp, #-0x38]
    // 0x990d20: StoreField: r3->field_13 = r0
    //     0x990d20: stur            w0, [x3, #0x13]
    // 0x990d24: r0 = Instance_BoxShape
    //     0x990d24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x990d28: ldr             x0, [x0, #0x80]
    // 0x990d2c: StoreField: r3->field_23 = r0
    //     0x990d2c: stur            w0, [x3, #0x23]
    // 0x990d30: ldur            x4, [fp, #-0x10]
    // 0x990d34: LoadField: r5 = r4->field_f
    //     0x990d34: ldur            w5, [x4, #0xf]
    // 0x990d38: DecompressPointer r5
    //     0x990d38: add             x5, x5, HEAP, lsl #32
    // 0x990d3c: stur            x5, [fp, #-0x30]
    // 0x990d40: cmp             w5, NULL
    // 0x990d44: b.eq            #0x990e40
    // 0x990d48: r1 = LoadInt32Instr(r5)
    //     0x990d48: sbfx            x1, x5, #1, #0x1f
    //     0x990d4c: tbz             w5, #0, #0x990d54
    //     0x990d50: ldur            x1, [x5, #7]
    // 0x990d54: cmp             x1, #0xa
    // 0x990d58: b.ge            #0x990d90
    // 0x990d5c: r1 = Null
    //     0x990d5c: mov             x1, NULL
    // 0x990d60: r2 = 6
    //     0x990d60: movz            x2, #0x6
    // 0x990d64: r0 = AllocateArray()
    //     0x990d64: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990d68: r16 = " 0"
    //     0x990d68: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] " 0"
    //     0x990d6c: ldr             x16, [x16, #0xdf0]
    // 0x990d70: StoreField: r0->field_f = r16
    //     0x990d70: stur            w16, [x0, #0xf]
    // 0x990d74: ldur            x3, [fp, #-0x30]
    // 0x990d78: StoreField: r0->field_13 = r3
    //     0x990d78: stur            w3, [x0, #0x13]
    // 0x990d7c: r16 = " "
    //     0x990d7c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x990d80: ArrayStore: r0[0] = r16  ; List_4
    //     0x990d80: stur            w16, [x0, #0x17]
    // 0x990d84: str             x0, [SP]
    // 0x990d88: r0 = _interpolate()
    //     0x990d88: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x990d8c: b               #0x990dc0
    // 0x990d90: mov             x3, x5
    // 0x990d94: r1 = Null
    //     0x990d94: mov             x1, NULL
    // 0x990d98: r2 = 6
    //     0x990d98: movz            x2, #0x6
    // 0x990d9c: r0 = AllocateArray()
    //     0x990d9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990da0: r16 = " "
    //     0x990da0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x990da4: StoreField: r0->field_f = r16
    //     0x990da4: stur            w16, [x0, #0xf]
    // 0x990da8: ldur            x1, [fp, #-0x30]
    // 0x990dac: StoreField: r0->field_13 = r1
    //     0x990dac: stur            w1, [x0, #0x13]
    // 0x990db0: r16 = " "
    //     0x990db0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x990db4: ArrayStore: r0[0] = r16  ; List_4
    //     0x990db4: stur            w16, [x0, #0x17]
    // 0x990db8: str             x0, [SP]
    // 0x990dbc: r0 = _interpolate()
    //     0x990dbc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x990dc0: ldur            x1, [fp, #-0x18]
    // 0x990dc4: stur            x0, [fp, #-0x30]
    // 0x990dc8: r0 = of()
    //     0x990dc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990dcc: LoadField: r1 = r0->field_87
    //     0x990dcc: ldur            w1, [x0, #0x87]
    // 0x990dd0: DecompressPointer r1
    //     0x990dd0: add             x1, x1, HEAP, lsl #32
    // 0x990dd4: LoadField: r0 = r1->field_2b
    //     0x990dd4: ldur            w0, [x1, #0x2b]
    // 0x990dd8: DecompressPointer r0
    //     0x990dd8: add             x0, x0, HEAP, lsl #32
    // 0x990ddc: r16 = Instance_Color
    //     0x990ddc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x990de0: r30 = 16.000000
    //     0x990de0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x990de4: ldr             lr, [lr, #0x188]
    // 0x990de8: stp             lr, x16, [SP]
    // 0x990dec: mov             x1, x0
    // 0x990df0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990df0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990df4: ldr             x4, [x4, #0x9b8]
    // 0x990df8: r0 = copyWith()
    //     0x990df8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990dfc: stur            x0, [fp, #-0x38]
    // 0x990e00: r0 = Text()
    //     0x990e00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990e04: mov             x1, x0
    // 0x990e08: ldur            x0, [fp, #-0x30]
    // 0x990e0c: stur            x1, [fp, #-0x48]
    // 0x990e10: StoreField: r1->field_b = r0
    //     0x990e10: stur            w0, [x1, #0xb]
    // 0x990e14: ldur            x0, [fp, #-0x38]
    // 0x990e18: StoreField: r1->field_13 = r0
    //     0x990e18: stur            w0, [x1, #0x13]
    // 0x990e1c: r0 = Align()
    //     0x990e1c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x990e20: mov             x1, x0
    // 0x990e24: r0 = Instance_Alignment
    //     0x990e24: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x990e28: ldr             x0, [x0, #0xb10]
    // 0x990e2c: StoreField: r1->field_f = r0
    //     0x990e2c: stur            w0, [x1, #0xf]
    // 0x990e30: ldur            x2, [fp, #-0x48]
    // 0x990e34: StoreField: r1->field_b = r2
    //     0x990e34: stur            w2, [x1, #0xb]
    // 0x990e38: mov             x3, x1
    // 0x990e3c: b               #0x990ec4
    // 0x990e40: r0 = Instance_Alignment
    //     0x990e40: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x990e44: ldr             x0, [x0, #0xb10]
    // 0x990e48: ldur            x1, [fp, #-0x18]
    // 0x990e4c: r0 = of()
    //     0x990e4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990e50: LoadField: r1 = r0->field_87
    //     0x990e50: ldur            w1, [x0, #0x87]
    // 0x990e54: DecompressPointer r1
    //     0x990e54: add             x1, x1, HEAP, lsl #32
    // 0x990e58: LoadField: r0 = r1->field_2b
    //     0x990e58: ldur            w0, [x1, #0x2b]
    // 0x990e5c: DecompressPointer r0
    //     0x990e5c: add             x0, x0, HEAP, lsl #32
    // 0x990e60: r16 = Instance_Color
    //     0x990e60: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x990e64: r30 = 16.000000
    //     0x990e64: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x990e68: ldr             lr, [lr, #0x188]
    // 0x990e6c: stp             lr, x16, [SP]
    // 0x990e70: mov             x1, x0
    // 0x990e74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990e74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990e78: ldr             x4, [x4, #0x9b8]
    // 0x990e7c: r0 = copyWith()
    //     0x990e7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990e80: stur            x0, [fp, #-0x30]
    // 0x990e84: r0 = Text()
    //     0x990e84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990e88: mov             x1, x0
    // 0x990e8c: r0 = "00"
    //     0x990e8c: add             x0, PP, #0x26, lsl #12  ; [pp+0x261d8] "00"
    //     0x990e90: ldr             x0, [x0, #0x1d8]
    // 0x990e94: stur            x1, [fp, #-0x38]
    // 0x990e98: StoreField: r1->field_b = r0
    //     0x990e98: stur            w0, [x1, #0xb]
    // 0x990e9c: ldur            x0, [fp, #-0x30]
    // 0x990ea0: StoreField: r1->field_13 = r0
    //     0x990ea0: stur            w0, [x1, #0x13]
    // 0x990ea4: r0 = Align()
    //     0x990ea4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x990ea8: mov             x1, x0
    // 0x990eac: r0 = Instance_Alignment
    //     0x990eac: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x990eb0: ldr             x0, [x0, #0xb10]
    // 0x990eb4: StoreField: r1->field_f = r0
    //     0x990eb4: stur            w0, [x1, #0xf]
    // 0x990eb8: ldur            x2, [fp, #-0x38]
    // 0x990ebc: StoreField: r1->field_b = r2
    //     0x990ebc: stur            w2, [x1, #0xb]
    // 0x990ec0: mov             x3, x1
    // 0x990ec4: ldur            x2, [fp, #-8]
    // 0x990ec8: ldur            x1, [fp, #-0x10]
    // 0x990ecc: stur            x3, [fp, #-0x30]
    // 0x990ed0: r0 = Container()
    //     0x990ed0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x990ed4: stur            x0, [fp, #-0x38]
    // 0x990ed8: r16 = 30.000000
    //     0x990ed8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0x990edc: ldr             x16, [x16, #0x768]
    // 0x990ee0: r30 = 50.000000
    //     0x990ee0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x990ee4: ldr             lr, [lr, #0xa90]
    // 0x990ee8: stp             lr, x16, [SP, #0x10]
    // 0x990eec: ldur            x16, [fp, #-0x40]
    // 0x990ef0: ldur            lr, [fp, #-0x30]
    // 0x990ef4: stp             lr, x16, [SP]
    // 0x990ef8: mov             x1, x0
    // 0x990efc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x990efc: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x990f00: ldr             x4, [x4, #0x8c0]
    // 0x990f04: r0 = Container()
    //     0x990f04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x990f08: ldur            x1, [fp, #-0x18]
    // 0x990f0c: r0 = of()
    //     0x990f0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x990f10: LoadField: r1 = r0->field_87
    //     0x990f10: ldur            w1, [x0, #0x87]
    // 0x990f14: DecompressPointer r1
    //     0x990f14: add             x1, x1, HEAP, lsl #32
    // 0x990f18: LoadField: r0 = r1->field_2b
    //     0x990f18: ldur            w0, [x1, #0x2b]
    // 0x990f1c: DecompressPointer r0
    //     0x990f1c: add             x0, x0, HEAP, lsl #32
    // 0x990f20: r16 = Instance_Color
    //     0x990f20: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x990f24: r30 = 12.000000
    //     0x990f24: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x990f28: ldr             lr, [lr, #0x9e8]
    // 0x990f2c: stp             lr, x16, [SP]
    // 0x990f30: mov             x1, x0
    // 0x990f34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x990f34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x990f38: ldr             x4, [x4, #0x9b8]
    // 0x990f3c: r0 = copyWith()
    //     0x990f3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x990f40: stur            x0, [fp, #-0x30]
    // 0x990f44: r0 = Text()
    //     0x990f44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x990f48: mov             x3, x0
    // 0x990f4c: r0 = "M"
    //     0x990f4c: add             x0, PP, #0x49, lsl #12  ; [pp+0x49780] "M"
    //     0x990f50: ldr             x0, [x0, #0x780]
    // 0x990f54: stur            x3, [fp, #-0x40]
    // 0x990f58: StoreField: r3->field_b = r0
    //     0x990f58: stur            w0, [x3, #0xb]
    // 0x990f5c: ldur            x0, [fp, #-0x30]
    // 0x990f60: StoreField: r3->field_13 = r0
    //     0x990f60: stur            w0, [x3, #0x13]
    // 0x990f64: r1 = Null
    //     0x990f64: mov             x1, NULL
    // 0x990f68: r2 = 4
    //     0x990f68: movz            x2, #0x4
    // 0x990f6c: r0 = AllocateArray()
    //     0x990f6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x990f70: mov             x2, x0
    // 0x990f74: ldur            x0, [fp, #-0x38]
    // 0x990f78: stur            x2, [fp, #-0x30]
    // 0x990f7c: StoreField: r2->field_f = r0
    //     0x990f7c: stur            w0, [x2, #0xf]
    // 0x990f80: ldur            x0, [fp, #-0x40]
    // 0x990f84: StoreField: r2->field_13 = r0
    //     0x990f84: stur            w0, [x2, #0x13]
    // 0x990f88: r1 = <Widget>
    //     0x990f88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x990f8c: r0 = AllocateGrowableArray()
    //     0x990f8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x990f90: mov             x1, x0
    // 0x990f94: ldur            x0, [fp, #-0x30]
    // 0x990f98: stur            x1, [fp, #-0x38]
    // 0x990f9c: StoreField: r1->field_f = r0
    //     0x990f9c: stur            w0, [x1, #0xf]
    // 0x990fa0: r2 = 4
    //     0x990fa0: movz            x2, #0x4
    // 0x990fa4: StoreField: r1->field_b = r2
    //     0x990fa4: stur            w2, [x1, #0xb]
    // 0x990fa8: r0 = Column()
    //     0x990fa8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x990fac: mov             x2, x0
    // 0x990fb0: r0 = Instance_Axis
    //     0x990fb0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x990fb4: stur            x2, [fp, #-0x30]
    // 0x990fb8: StoreField: r2->field_f = r0
    //     0x990fb8: stur            w0, [x2, #0xf]
    // 0x990fbc: r3 = Instance_MainAxisAlignment
    //     0x990fbc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x990fc0: ldr             x3, [x3, #0xa08]
    // 0x990fc4: StoreField: r2->field_13 = r3
    //     0x990fc4: stur            w3, [x2, #0x13]
    // 0x990fc8: r4 = Instance_MainAxisSize
    //     0x990fc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x990fcc: ldr             x4, [x4, #0xa10]
    // 0x990fd0: ArrayStore: r2[0] = r4  ; List_4
    //     0x990fd0: stur            w4, [x2, #0x17]
    // 0x990fd4: r5 = Instance_CrossAxisAlignment
    //     0x990fd4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x990fd8: ldr             x5, [x5, #0xa18]
    // 0x990fdc: StoreField: r2->field_1b = r5
    //     0x990fdc: stur            w5, [x2, #0x1b]
    // 0x990fe0: r6 = Instance_VerticalDirection
    //     0x990fe0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x990fe4: ldr             x6, [x6, #0xa20]
    // 0x990fe8: StoreField: r2->field_23 = r6
    //     0x990fe8: stur            w6, [x2, #0x23]
    // 0x990fec: r7 = Instance_Clip
    //     0x990fec: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x990ff0: ldr             x7, [x7, #0x38]
    // 0x990ff4: StoreField: r2->field_2b = r7
    //     0x990ff4: stur            w7, [x2, #0x2b]
    // 0x990ff8: StoreField: r2->field_2f = rZR
    //     0x990ff8: stur            xzr, [x2, #0x2f]
    // 0x990ffc: ldur            x1, [fp, #-0x38]
    // 0x991000: StoreField: r2->field_b = r1
    //     0x991000: stur            w1, [x2, #0xb]
    // 0x991004: ldur            x1, [fp, #-0x18]
    // 0x991008: r0 = of()
    //     0x991008: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x99100c: LoadField: r1 = r0->field_87
    //     0x99100c: ldur            w1, [x0, #0x87]
    // 0x991010: DecompressPointer r1
    //     0x991010: add             x1, x1, HEAP, lsl #32
    // 0x991014: LoadField: r0 = r1->field_2b
    //     0x991014: ldur            w0, [x1, #0x2b]
    // 0x991018: DecompressPointer r0
    //     0x991018: add             x0, x0, HEAP, lsl #32
    // 0x99101c: r16 = Instance_Color
    //     0x99101c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x991020: r30 = 16.000000
    //     0x991020: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x991024: ldr             lr, [lr, #0x188]
    // 0x991028: stp             lr, x16, [SP]
    // 0x99102c: mov             x1, x0
    // 0x991030: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x991030: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x991034: ldr             x4, [x4, #0x9b8]
    // 0x991038: r0 = copyWith()
    //     0x991038: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x99103c: stur            x0, [fp, #-0x38]
    // 0x991040: r0 = Text()
    //     0x991040: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x991044: mov             x1, x0
    // 0x991048: r0 = "   :   "
    //     0x991048: add             x0, PP, #0x49, lsl #12  ; [pp+0x49770] "   :   "
    //     0x99104c: ldr             x0, [x0, #0x770]
    // 0x991050: stur            x1, [fp, #-0x40]
    // 0x991054: StoreField: r1->field_b = r0
    //     0x991054: stur            w0, [x1, #0xb]
    // 0x991058: ldur            x0, [fp, #-0x38]
    // 0x99105c: StoreField: r1->field_13 = r0
    //     0x99105c: stur            w0, [x1, #0x13]
    // 0x991060: r0 = Padding()
    //     0x991060: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x991064: mov             x1, x0
    // 0x991068: r0 = Instance_EdgeInsets
    //     0x991068: add             x0, PP, #0x49, lsl #12  ; [pp+0x49778] Obj!EdgeInsets@d573e1
    //     0x99106c: ldr             x0, [x0, #0x778]
    // 0x991070: stur            x1, [fp, #-0x38]
    // 0x991074: StoreField: r1->field_f = r0
    //     0x991074: stur            w0, [x1, #0xf]
    // 0x991078: ldur            x0, [fp, #-0x40]
    // 0x99107c: StoreField: r1->field_b = r0
    //     0x99107c: stur            w0, [x1, #0xb]
    // 0x991080: ldur            x0, [fp, #-8]
    // 0x991084: LoadField: r2 = r0->field_b
    //     0x991084: ldur            w2, [x0, #0xb]
    // 0x991088: DecompressPointer r2
    //     0x991088: add             x2, x2, HEAP, lsl #32
    // 0x99108c: cmp             w2, NULL
    // 0x991090: b.eq            #0x99150c
    // 0x991094: ArrayLoad: d0 = r2[0]  ; List_8
    //     0x991094: ldur            d0, [x2, #0x17]
    // 0x991098: stur            d0, [fp, #-0x58]
    // 0x99109c: r0 = Radius()
    //     0x99109c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x9910a0: ldur            d0, [fp, #-0x58]
    // 0x9910a4: stur            x0, [fp, #-8]
    // 0x9910a8: StoreField: r0->field_7 = d0
    //     0x9910a8: stur            d0, [x0, #7]
    // 0x9910ac: StoreField: r0->field_f = d0
    //     0x9910ac: stur            d0, [x0, #0xf]
    // 0x9910b0: r0 = BorderRadius()
    //     0x9910b0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x9910b4: mov             x2, x0
    // 0x9910b8: ldur            x0, [fp, #-8]
    // 0x9910bc: stur            x2, [fp, #-0x40]
    // 0x9910c0: StoreField: r2->field_7 = r0
    //     0x9910c0: stur            w0, [x2, #7]
    // 0x9910c4: StoreField: r2->field_b = r0
    //     0x9910c4: stur            w0, [x2, #0xb]
    // 0x9910c8: StoreField: r2->field_f = r0
    //     0x9910c8: stur            w0, [x2, #0xf]
    // 0x9910cc: StoreField: r2->field_13 = r0
    //     0x9910cc: stur            w0, [x2, #0x13]
    // 0x9910d0: r1 = Instance_Color
    //     0x9910d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9910d4: d0 = 0.100000
    //     0x9910d4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x9910d8: r0 = withOpacity()
    //     0x9910d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x9910dc: mov             x2, x0
    // 0x9910e0: r1 = Null
    //     0x9910e0: mov             x1, NULL
    // 0x9910e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9910e4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9910e8: r0 = Border.all()
    //     0x9910e8: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x9910ec: stur            x0, [fp, #-8]
    // 0x9910f0: r0 = BoxDecoration()
    //     0x9910f0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9910f4: mov             x3, x0
    // 0x9910f8: ldur            x0, [fp, #-8]
    // 0x9910fc: stur            x3, [fp, #-0x48]
    // 0x991100: StoreField: r3->field_f = r0
    //     0x991100: stur            w0, [x3, #0xf]
    // 0x991104: ldur            x0, [fp, #-0x40]
    // 0x991108: StoreField: r3->field_13 = r0
    //     0x991108: stur            w0, [x3, #0x13]
    // 0x99110c: r0 = Instance_BoxShape
    //     0x99110c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x991110: ldr             x0, [x0, #0x80]
    // 0x991114: StoreField: r3->field_23 = r0
    //     0x991114: stur            w0, [x3, #0x23]
    // 0x991118: ldur            x0, [fp, #-0x10]
    // 0x99111c: LoadField: r4 = r0->field_13
    //     0x99111c: ldur            x4, [x0, #0x13]
    // 0x991120: stur            x4, [fp, #-0x50]
    // 0x991124: cmp             x4, #0xa
    // 0x991128: b.ge            #0x99117c
    // 0x99112c: r1 = Null
    //     0x99112c: mov             x1, NULL
    // 0x991130: r2 = 6
    //     0x991130: movz            x2, #0x6
    // 0x991134: r0 = AllocateArray()
    //     0x991134: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991138: mov             x2, x0
    // 0x99113c: r16 = " 0"
    //     0x99113c: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] " 0"
    //     0x991140: ldr             x16, [x16, #0xdf0]
    // 0x991144: StoreField: r2->field_f = r16
    //     0x991144: stur            w16, [x2, #0xf]
    // 0x991148: ldur            x3, [fp, #-0x50]
    // 0x99114c: r0 = BoxInt64Instr(r3)
    //     0x99114c: sbfiz           x0, x3, #1, #0x1f
    //     0x991150: cmp             x3, x0, asr #1
    //     0x991154: b.eq            #0x991160
    //     0x991158: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x99115c: stur            x3, [x0, #7]
    // 0x991160: StoreField: r2->field_13 = r0
    //     0x991160: stur            w0, [x2, #0x13]
    // 0x991164: r16 = " "
    //     0x991164: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x991168: ArrayStore: r2[0] = r16  ; List_4
    //     0x991168: stur            w16, [x2, #0x17]
    // 0x99116c: str             x2, [SP]
    // 0x991170: r0 = _interpolate()
    //     0x991170: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x991174: mov             x5, x0
    // 0x991178: b               #0x9911c8
    // 0x99117c: mov             x3, x4
    // 0x991180: r1 = Null
    //     0x991180: mov             x1, NULL
    // 0x991184: r2 = 6
    //     0x991184: movz            x2, #0x6
    // 0x991188: r0 = AllocateArray()
    //     0x991188: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99118c: mov             x2, x0
    // 0x991190: r16 = " "
    //     0x991190: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x991194: StoreField: r2->field_f = r16
    //     0x991194: stur            w16, [x2, #0xf]
    // 0x991198: ldur            x3, [fp, #-0x50]
    // 0x99119c: r0 = BoxInt64Instr(r3)
    //     0x99119c: sbfiz           x0, x3, #1, #0x1f
    //     0x9911a0: cmp             x3, x0, asr #1
    //     0x9911a4: b.eq            #0x9911b0
    //     0x9911a8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9911ac: stur            x3, [x0, #7]
    // 0x9911b0: StoreField: r2->field_13 = r0
    //     0x9911b0: stur            w0, [x2, #0x13]
    // 0x9911b4: r16 = " "
    //     0x9911b4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9911b8: ArrayStore: r2[0] = r16  ; List_4
    //     0x9911b8: stur            w16, [x2, #0x17]
    // 0x9911bc: str             x2, [SP]
    // 0x9911c0: r0 = _interpolate()
    //     0x9911c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9911c4: mov             x5, x0
    // 0x9911c8: ldur            x4, [fp, #-0x20]
    // 0x9911cc: ldur            x3, [fp, #-0x28]
    // 0x9911d0: ldur            x2, [fp, #-0x30]
    // 0x9911d4: ldur            x0, [fp, #-0x38]
    // 0x9911d8: ldur            x1, [fp, #-0x18]
    // 0x9911dc: stur            x5, [fp, #-8]
    // 0x9911e0: r0 = of()
    //     0x9911e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9911e4: LoadField: r1 = r0->field_87
    //     0x9911e4: ldur            w1, [x0, #0x87]
    // 0x9911e8: DecompressPointer r1
    //     0x9911e8: add             x1, x1, HEAP, lsl #32
    // 0x9911ec: LoadField: r0 = r1->field_2b
    //     0x9911ec: ldur            w0, [x1, #0x2b]
    // 0x9911f0: DecompressPointer r0
    //     0x9911f0: add             x0, x0, HEAP, lsl #32
    // 0x9911f4: r16 = Instance_Color
    //     0x9911f4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9911f8: r30 = 16.000000
    //     0x9911f8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9911fc: ldr             lr, [lr, #0x188]
    // 0x991200: stp             lr, x16, [SP]
    // 0x991204: mov             x1, x0
    // 0x991208: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x991208: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x99120c: ldr             x4, [x4, #0x9b8]
    // 0x991210: r0 = copyWith()
    //     0x991210: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x991214: stur            x0, [fp, #-0x10]
    // 0x991218: r0 = Text()
    //     0x991218: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x99121c: mov             x1, x0
    // 0x991220: ldur            x0, [fp, #-8]
    // 0x991224: stur            x1, [fp, #-0x40]
    // 0x991228: StoreField: r1->field_b = r0
    //     0x991228: stur            w0, [x1, #0xb]
    // 0x99122c: ldur            x0, [fp, #-0x10]
    // 0x991230: StoreField: r1->field_13 = r0
    //     0x991230: stur            w0, [x1, #0x13]
    // 0x991234: r0 = Align()
    //     0x991234: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x991238: mov             x1, x0
    // 0x99123c: r0 = Instance_Alignment
    //     0x99123c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x991240: ldr             x0, [x0, #0xb10]
    // 0x991244: stur            x1, [fp, #-8]
    // 0x991248: StoreField: r1->field_f = r0
    //     0x991248: stur            w0, [x1, #0xf]
    // 0x99124c: ldur            x0, [fp, #-0x40]
    // 0x991250: StoreField: r1->field_b = r0
    //     0x991250: stur            w0, [x1, #0xb]
    // 0x991254: r0 = Container()
    //     0x991254: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x991258: stur            x0, [fp, #-0x10]
    // 0x99125c: r16 = 30.000000
    //     0x99125c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0x991260: ldr             x16, [x16, #0x768]
    // 0x991264: r30 = 50.000000
    //     0x991264: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x991268: ldr             lr, [lr, #0xa90]
    // 0x99126c: stp             lr, x16, [SP, #0x10]
    // 0x991270: ldur            x16, [fp, #-0x48]
    // 0x991274: ldur            lr, [fp, #-8]
    // 0x991278: stp             lr, x16, [SP]
    // 0x99127c: mov             x1, x0
    // 0x991280: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x991280: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x991284: ldr             x4, [x4, #0x8c0]
    // 0x991288: r0 = Container()
    //     0x991288: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x99128c: ldur            x1, [fp, #-0x18]
    // 0x991290: r0 = of()
    //     0x991290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x991294: LoadField: r1 = r0->field_87
    //     0x991294: ldur            w1, [x0, #0x87]
    // 0x991298: DecompressPointer r1
    //     0x991298: add             x1, x1, HEAP, lsl #32
    // 0x99129c: LoadField: r0 = r1->field_2b
    //     0x99129c: ldur            w0, [x1, #0x2b]
    // 0x9912a0: DecompressPointer r0
    //     0x9912a0: add             x0, x0, HEAP, lsl #32
    // 0x9912a4: r16 = Instance_Color
    //     0x9912a4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x9912a8: r30 = 12.000000
    //     0x9912a8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9912ac: ldr             lr, [lr, #0x9e8]
    // 0x9912b0: stp             lr, x16, [SP]
    // 0x9912b4: mov             x1, x0
    // 0x9912b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9912b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9912bc: ldr             x4, [x4, #0x9b8]
    // 0x9912c0: r0 = copyWith()
    //     0x9912c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9912c4: stur            x0, [fp, #-8]
    // 0x9912c8: r0 = Text()
    //     0x9912c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9912cc: mov             x3, x0
    // 0x9912d0: r0 = "S"
    //     0x9912d0: add             x0, PP, #0x49, lsl #12  ; [pp+0x49788] "S"
    //     0x9912d4: ldr             x0, [x0, #0x788]
    // 0x9912d8: stur            x3, [fp, #-0x18]
    // 0x9912dc: StoreField: r3->field_b = r0
    //     0x9912dc: stur            w0, [x3, #0xb]
    // 0x9912e0: ldur            x0, [fp, #-8]
    // 0x9912e4: StoreField: r3->field_13 = r0
    //     0x9912e4: stur            w0, [x3, #0x13]
    // 0x9912e8: r1 = Null
    //     0x9912e8: mov             x1, NULL
    // 0x9912ec: r2 = 4
    //     0x9912ec: movz            x2, #0x4
    // 0x9912f0: r0 = AllocateArray()
    //     0x9912f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9912f4: mov             x2, x0
    // 0x9912f8: ldur            x0, [fp, #-0x10]
    // 0x9912fc: stur            x2, [fp, #-8]
    // 0x991300: StoreField: r2->field_f = r0
    //     0x991300: stur            w0, [x2, #0xf]
    // 0x991304: ldur            x0, [fp, #-0x18]
    // 0x991308: StoreField: r2->field_13 = r0
    //     0x991308: stur            w0, [x2, #0x13]
    // 0x99130c: r1 = <Widget>
    //     0x99130c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991310: r0 = AllocateGrowableArray()
    //     0x991310: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991314: mov             x1, x0
    // 0x991318: ldur            x0, [fp, #-8]
    // 0x99131c: stur            x1, [fp, #-0x10]
    // 0x991320: StoreField: r1->field_f = r0
    //     0x991320: stur            w0, [x1, #0xf]
    // 0x991324: r0 = 4
    //     0x991324: movz            x0, #0x4
    // 0x991328: StoreField: r1->field_b = r0
    //     0x991328: stur            w0, [x1, #0xb]
    // 0x99132c: r0 = Column()
    //     0x99132c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x991330: mov             x3, x0
    // 0x991334: r0 = Instance_Axis
    //     0x991334: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x991338: stur            x3, [fp, #-8]
    // 0x99133c: StoreField: r3->field_f = r0
    //     0x99133c: stur            w0, [x3, #0xf]
    // 0x991340: r1 = Instance_MainAxisAlignment
    //     0x991340: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x991344: ldr             x1, [x1, #0xa08]
    // 0x991348: StoreField: r3->field_13 = r1
    //     0x991348: stur            w1, [x3, #0x13]
    // 0x99134c: r1 = Instance_MainAxisSize
    //     0x99134c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x991350: ldr             x1, [x1, #0xa10]
    // 0x991354: ArrayStore: r3[0] = r1  ; List_4
    //     0x991354: stur            w1, [x3, #0x17]
    // 0x991358: r4 = Instance_CrossAxisAlignment
    //     0x991358: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x99135c: ldr             x4, [x4, #0xa18]
    // 0x991360: StoreField: r3->field_1b = r4
    //     0x991360: stur            w4, [x3, #0x1b]
    // 0x991364: r5 = Instance_VerticalDirection
    //     0x991364: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x991368: ldr             x5, [x5, #0xa20]
    // 0x99136c: StoreField: r3->field_23 = r5
    //     0x99136c: stur            w5, [x3, #0x23]
    // 0x991370: r6 = Instance_Clip
    //     0x991370: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x991374: ldr             x6, [x6, #0x38]
    // 0x991378: StoreField: r3->field_2b = r6
    //     0x991378: stur            w6, [x3, #0x2b]
    // 0x99137c: StoreField: r3->field_2f = rZR
    //     0x99137c: stur            xzr, [x3, #0x2f]
    // 0x991380: ldur            x1, [fp, #-0x10]
    // 0x991384: StoreField: r3->field_b = r1
    //     0x991384: stur            w1, [x3, #0xb]
    // 0x991388: r1 = Null
    //     0x991388: mov             x1, NULL
    // 0x99138c: r2 = 10
    //     0x99138c: movz            x2, #0xa
    // 0x991390: r0 = AllocateArray()
    //     0x991390: bl              #0x16f7198  ; AllocateArrayStub
    // 0x991394: mov             x2, x0
    // 0x991398: ldur            x0, [fp, #-0x20]
    // 0x99139c: stur            x2, [fp, #-0x10]
    // 0x9913a0: StoreField: r2->field_f = r0
    //     0x9913a0: stur            w0, [x2, #0xf]
    // 0x9913a4: ldur            x0, [fp, #-0x28]
    // 0x9913a8: StoreField: r2->field_13 = r0
    //     0x9913a8: stur            w0, [x2, #0x13]
    // 0x9913ac: ldur            x0, [fp, #-0x30]
    // 0x9913b0: ArrayStore: r2[0] = r0  ; List_4
    //     0x9913b0: stur            w0, [x2, #0x17]
    // 0x9913b4: ldur            x0, [fp, #-0x38]
    // 0x9913b8: StoreField: r2->field_1b = r0
    //     0x9913b8: stur            w0, [x2, #0x1b]
    // 0x9913bc: ldur            x0, [fp, #-8]
    // 0x9913c0: StoreField: r2->field_1f = r0
    //     0x9913c0: stur            w0, [x2, #0x1f]
    // 0x9913c4: r1 = <Widget>
    //     0x9913c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9913c8: r0 = AllocateGrowableArray()
    //     0x9913c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9913cc: mov             x1, x0
    // 0x9913d0: ldur            x0, [fp, #-0x10]
    // 0x9913d4: stur            x1, [fp, #-8]
    // 0x9913d8: StoreField: r1->field_f = r0
    //     0x9913d8: stur            w0, [x1, #0xf]
    // 0x9913dc: r0 = 10
    //     0x9913dc: movz            x0, #0xa
    // 0x9913e0: StoreField: r1->field_b = r0
    //     0x9913e0: stur            w0, [x1, #0xb]
    // 0x9913e4: r0 = Row()
    //     0x9913e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9913e8: mov             x3, x0
    // 0x9913ec: r0 = Instance_Axis
    //     0x9913ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9913f0: stur            x3, [fp, #-0x10]
    // 0x9913f4: StoreField: r3->field_f = r0
    //     0x9913f4: stur            w0, [x3, #0xf]
    // 0x9913f8: r0 = Instance_MainAxisAlignment
    //     0x9913f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9913fc: ldr             x0, [x0, #0xab0]
    // 0x991400: StoreField: r3->field_13 = r0
    //     0x991400: stur            w0, [x3, #0x13]
    // 0x991404: r4 = Instance_MainAxisSize
    //     0x991404: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x991408: ldr             x4, [x4, #0xdd0]
    // 0x99140c: ArrayStore: r3[0] = r4  ; List_4
    //     0x99140c: stur            w4, [x3, #0x17]
    // 0x991410: r5 = Instance_CrossAxisAlignment
    //     0x991410: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x991414: ldr             x5, [x5, #0xa18]
    // 0x991418: StoreField: r3->field_1b = r5
    //     0x991418: stur            w5, [x3, #0x1b]
    // 0x99141c: r6 = Instance_VerticalDirection
    //     0x99141c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x991420: ldr             x6, [x6, #0xa20]
    // 0x991424: StoreField: r3->field_23 = r6
    //     0x991424: stur            w6, [x3, #0x23]
    // 0x991428: r7 = Instance_Clip
    //     0x991428: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x99142c: ldr             x7, [x7, #0x38]
    // 0x991430: StoreField: r3->field_2b = r7
    //     0x991430: stur            w7, [x3, #0x2b]
    // 0x991434: StoreField: r3->field_2f = rZR
    //     0x991434: stur            xzr, [x3, #0x2f]
    // 0x991438: ldur            x1, [fp, #-8]
    // 0x99143c: StoreField: r3->field_b = r1
    //     0x99143c: stur            w1, [x3, #0xb]
    // 0x991440: r1 = Null
    //     0x991440: mov             x1, NULL
    // 0x991444: r2 = 2
    //     0x991444: movz            x2, #0x2
    // 0x991448: r0 = AllocateArray()
    //     0x991448: bl              #0x16f7198  ; AllocateArrayStub
    // 0x99144c: mov             x2, x0
    // 0x991450: ldur            x0, [fp, #-0x10]
    // 0x991454: stur            x2, [fp, #-8]
    // 0x991458: StoreField: r2->field_f = r0
    //     0x991458: stur            w0, [x2, #0xf]
    // 0x99145c: r1 = <Widget>
    //     0x99145c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x991460: r0 = AllocateGrowableArray()
    //     0x991460: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x991464: mov             x1, x0
    // 0x991468: ldur            x0, [fp, #-8]
    // 0x99146c: stur            x1, [fp, #-0x10]
    // 0x991470: StoreField: r1->field_f = r0
    //     0x991470: stur            w0, [x1, #0xf]
    // 0x991474: r0 = 2
    //     0x991474: movz            x0, #0x2
    // 0x991478: StoreField: r1->field_b = r0
    //     0x991478: stur            w0, [x1, #0xb]
    // 0x99147c: r0 = Column()
    //     0x99147c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x991480: mov             x1, x0
    // 0x991484: r0 = Instance_Axis
    //     0x991484: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x991488: stur            x1, [fp, #-8]
    // 0x99148c: StoreField: r1->field_f = r0
    //     0x99148c: stur            w0, [x1, #0xf]
    // 0x991490: r0 = Instance_MainAxisAlignment
    //     0x991490: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x991494: ldr             x0, [x0, #0xab0]
    // 0x991498: StoreField: r1->field_13 = r0
    //     0x991498: stur            w0, [x1, #0x13]
    // 0x99149c: r0 = Instance_MainAxisSize
    //     0x99149c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x9914a0: ldr             x0, [x0, #0xdd0]
    // 0x9914a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x9914a4: stur            w0, [x1, #0x17]
    // 0x9914a8: r0 = Instance_CrossAxisAlignment
    //     0x9914a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9914ac: ldr             x0, [x0, #0xa18]
    // 0x9914b0: StoreField: r1->field_1b = r0
    //     0x9914b0: stur            w0, [x1, #0x1b]
    // 0x9914b4: r0 = Instance_VerticalDirection
    //     0x9914b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9914b8: ldr             x0, [x0, #0xa20]
    // 0x9914bc: StoreField: r1->field_23 = r0
    //     0x9914bc: stur            w0, [x1, #0x23]
    // 0x9914c0: r0 = Instance_Clip
    //     0x9914c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9914c4: ldr             x0, [x0, #0x38]
    // 0x9914c8: StoreField: r1->field_2b = r0
    //     0x9914c8: stur            w0, [x1, #0x2b]
    // 0x9914cc: StoreField: r1->field_2f = rZR
    //     0x9914cc: stur            xzr, [x1, #0x2f]
    // 0x9914d0: ldur            x0, [fp, #-0x10]
    // 0x9914d4: StoreField: r1->field_b = r0
    //     0x9914d4: stur            w0, [x1, #0xb]
    // 0x9914d8: r0 = SizedBox()
    //     0x9914d8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9914dc: r1 = 70.000000
    //     0x9914dc: add             x1, PP, #0x33, lsl #12  ; [pp+0x33748] 70
    //     0x9914e0: ldr             x1, [x1, #0x748]
    // 0x9914e4: StoreField: r0->field_13 = r1
    //     0x9914e4: stur            w1, [x0, #0x13]
    // 0x9914e8: ldur            x1, [fp, #-8]
    // 0x9914ec: StoreField: r0->field_b = r1
    //     0x9914ec: stur            w1, [x0, #0xb]
    // 0x9914f0: LeaveFrame
    //     0x9914f0: mov             SP, fp
    //     0x9914f4: ldp             fp, lr, [SP], #0x10
    // 0x9914f8: ret
    //     0x9914f8: ret             
    // 0x9914fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9914fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x991500: b               #0x990890
    // 0x991504: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x991504: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x991508: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x991508: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x99150c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99150c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x991510, size: 0x70
    // 0x991510: EnterFrame
    //     0x991510: stp             fp, lr, [SP, #-0x10]!
    //     0x991514: mov             fp, SP
    // 0x991518: AllocStack(0x8)
    //     0x991518: sub             SP, SP, #8
    // 0x99151c: SetupParameters()
    //     0x99151c: ldr             x0, [fp, #0x18]
    //     0x991520: ldur            w1, [x0, #0x17]
    //     0x991524: add             x1, x1, HEAP, lsl #32
    // 0x991528: CheckStackOverflow
    //     0x991528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x99152c: cmp             SP, x16
    //     0x991530: b.ls            #0x991574
    // 0x991534: LoadField: r0 = r1->field_f
    //     0x991534: ldur            w0, [x1, #0xf]
    // 0x991538: DecompressPointer r0
    //     0x991538: add             x0, x0, HEAP, lsl #32
    // 0x99153c: LoadField: r1 = r0->field_b
    //     0x99153c: ldur            w1, [x0, #0xb]
    // 0x991540: DecompressPointer r1
    //     0x991540: add             x1, x1, HEAP, lsl #32
    // 0x991544: cmp             w1, NULL
    // 0x991548: b.eq            #0x99157c
    // 0x99154c: LoadField: r0 = r1->field_13
    //     0x99154c: ldur            w0, [x1, #0x13]
    // 0x991550: DecompressPointer r0
    //     0x991550: add             x0, x0, HEAP, lsl #32
    // 0x991554: str             x0, [SP]
    // 0x991558: ClosureCall
    //     0x991558: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x99155c: ldur            x2, [x0, #0x1f]
    //     0x991560: blr             x2
    // 0x991564: r0 = Null
    //     0x991564: mov             x0, NULL
    // 0x991568: LeaveFrame
    //     0x991568: mov             SP, fp
    //     0x99156c: ldp             fp, lr, [SP], #0x10
    // 0x991570: ret
    //     0x991570: ret             
    // 0x991574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x991574: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x991578: b               #0x991534
    // 0x99157c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x99157c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4315, size: 0x28, field offset: 0xc
class BumperCouponTimerWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79a18, size: 0x24
    // 0xc79a18: EnterFrame
    //     0xc79a18: stp             fp, lr, [SP, #-0x10]!
    //     0xc79a1c: mov             fp, SP
    // 0xc79a20: mov             x0, x1
    // 0xc79a24: r1 = <BumperCouponTimerWidget>
    //     0xc79a24: add             x1, PP, #0x49, lsl #12  ; [pp+0x49370] TypeArguments: <BumperCouponTimerWidget>
    //     0xc79a28: ldr             x1, [x1, #0x370]
    // 0xc79a2c: r0 = _BumperCouponTimerWidgetState()
    //     0xc79a2c: bl              #0xc79a3c  ; Allocate_BumperCouponTimerWidgetStateStub -> _BumperCouponTimerWidgetState (size=0x14)
    // 0xc79a30: LeaveFrame
    //     0xc79a30: mov             SP, fp
    //     0xc79a34: ldp             fp, lr, [SP], #0x10
    // 0xc79a38: ret
    //     0xc79a38: ret             
  }
}
