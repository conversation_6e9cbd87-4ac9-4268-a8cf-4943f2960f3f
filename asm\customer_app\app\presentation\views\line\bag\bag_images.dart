// lib: , url: package:customer_app/app/presentation/views/line/bag/bag_images.dart

// class id: 1049462, size: 0x8
class :: {
}

// class id: 3295, size: 0x20, field offset: 0x14
class _BagImagesState extends State<dynamic> {

  late String value; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x945144, size: 0x268
    // 0x945144: EnterFrame
    //     0x945144: stp             fp, lr, [SP, #-0x10]!
    //     0x945148: mov             fp, SP
    // 0x94514c: AllocStack(0x20)
    //     0x94514c: sub             SP, SP, #0x20
    // 0x945150: SetupParameters(_BagImagesState this /* r1 => r2, fp-0x10 */)
    //     0x945150: mov             x2, x1
    //     0x945154: stur            x1, [fp, #-0x10]
    // 0x945158: CheckStackOverflow
    //     0x945158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94515c: cmp             SP, x16
    //     0x945160: b.ls            #0x945398
    // 0x945164: LoadField: r3 = r2->field_b
    //     0x945164: ldur            w3, [x2, #0xb]
    // 0x945168: DecompressPointer r3
    //     0x945168: add             x3, x3, HEAP, lsl #32
    // 0x94516c: cmp             w3, NULL
    // 0x945170: b.eq            #0x9453a0
    // 0x945174: LoadField: r0 = r3->field_b
    //     0x945174: ldur            w0, [x3, #0xb]
    // 0x945178: DecompressPointer r0
    //     0x945178: add             x0, x0, HEAP, lsl #32
    // 0x94517c: cmp             w0, NULL
    // 0x945180: b.ne            #0x94518c
    // 0x945184: r0 = Null
    //     0x945184: mov             x0, NULL
    // 0x945188: b               #0x9451d4
    // 0x94518c: LoadField: r4 = r0->field_23
    //     0x94518c: ldur            w4, [x0, #0x23]
    // 0x945190: DecompressPointer r4
    //     0x945190: add             x4, x4, HEAP, lsl #32
    // 0x945194: cmp             w4, NULL
    // 0x945198: b.ne            #0x9451a4
    // 0x94519c: r0 = Null
    //     0x94519c: mov             x0, NULL
    // 0x9451a0: b               #0x9451d4
    // 0x9451a4: LoadField: r0 = r4->field_b
    //     0x9451a4: ldur            w0, [x4, #0xb]
    // 0x9451a8: r1 = LoadInt32Instr(r0)
    //     0x9451a8: sbfx            x1, x0, #1, #0x1f
    // 0x9451ac: mov             x0, x1
    // 0x9451b0: r1 = 0
    //     0x9451b0: movz            x1, #0
    // 0x9451b4: cmp             x1, x0
    // 0x9451b8: b.hs            #0x9453a4
    // 0x9451bc: LoadField: r0 = r4->field_f
    //     0x9451bc: ldur            w0, [x4, #0xf]
    // 0x9451c0: DecompressPointer r0
    //     0x9451c0: add             x0, x0, HEAP, lsl #32
    // 0x9451c4: LoadField: r1 = r0->field_f
    //     0x9451c4: ldur            w1, [x0, #0xf]
    // 0x9451c8: DecompressPointer r1
    //     0x9451c8: add             x1, x1, HEAP, lsl #32
    // 0x9451cc: LoadField: r0 = r1->field_b
    //     0x9451cc: ldur            w0, [x1, #0xb]
    // 0x9451d0: DecompressPointer r0
    //     0x9451d0: add             x0, x0, HEAP, lsl #32
    // 0x9451d4: cmp             w0, NULL
    // 0x9451d8: b.ne            #0x9451e0
    // 0x9451dc: r0 = ""
    //     0x9451dc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9451e0: StoreField: r2->field_13 = r0
    //     0x9451e0: stur            w0, [x2, #0x13]
    //     0x9451e4: ldurb           w16, [x2, #-1]
    //     0x9451e8: ldurb           w17, [x0, #-1]
    //     0x9451ec: and             x16, x17, x16, lsr #2
    //     0x9451f0: tst             x16, HEAP, lsr #32
    //     0x9451f4: b.eq            #0x9451fc
    //     0x9451f8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9451fc: LoadField: r0 = r3->field_f
    //     0x9451fc: ldur            w0, [x3, #0xf]
    // 0x945200: DecompressPointer r0
    //     0x945200: add             x0, x0, HEAP, lsl #32
    // 0x945204: cmp             w0, NULL
    // 0x945208: b.ne            #0x945214
    // 0x94520c: r0 = Null
    //     0x94520c: mov             x0, NULL
    // 0x945210: b               #0x94525c
    // 0x945214: LoadField: r3 = r0->field_23
    //     0x945214: ldur            w3, [x0, #0x23]
    // 0x945218: DecompressPointer r3
    //     0x945218: add             x3, x3, HEAP, lsl #32
    // 0x94521c: cmp             w3, NULL
    // 0x945220: b.ne            #0x94522c
    // 0x945224: r0 = Null
    //     0x945224: mov             x0, NULL
    // 0x945228: b               #0x94525c
    // 0x94522c: LoadField: r0 = r3->field_b
    //     0x94522c: ldur            w0, [x3, #0xb]
    // 0x945230: r1 = LoadInt32Instr(r0)
    //     0x945230: sbfx            x1, x0, #1, #0x1f
    // 0x945234: mov             x0, x1
    // 0x945238: r1 = 0
    //     0x945238: movz            x1, #0
    // 0x94523c: cmp             x1, x0
    // 0x945240: b.hs            #0x9453a8
    // 0x945244: LoadField: r0 = r3->field_f
    //     0x945244: ldur            w0, [x3, #0xf]
    // 0x945248: DecompressPointer r0
    //     0x945248: add             x0, x0, HEAP, lsl #32
    // 0x94524c: LoadField: r1 = r0->field_f
    //     0x94524c: ldur            w1, [x0, #0xf]
    // 0x945250: DecompressPointer r1
    //     0x945250: add             x1, x1, HEAP, lsl #32
    // 0x945254: LoadField: r0 = r1->field_13
    //     0x945254: ldur            w0, [x1, #0x13]
    // 0x945258: DecompressPointer r0
    //     0x945258: add             x0, x0, HEAP, lsl #32
    // 0x94525c: cmp             w0, NULL
    // 0x945260: b.ne            #0x94526c
    // 0x945264: r1 = ""
    //     0x945264: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x945268: b               #0x945270
    // 0x94526c: mov             x1, x0
    // 0x945270: mov             x0, x1
    // 0x945274: stur            x1, [fp, #-8]
    // 0x945278: ArrayStore: r2[0] = r0  ; List_4
    //     0x945278: stur            w0, [x2, #0x17]
    //     0x94527c: ldurb           w16, [x2, #-1]
    //     0x945280: ldurb           w17, [x0, #-1]
    //     0x945284: and             x16, x17, x16, lsr #2
    //     0x945288: tst             x16, HEAP, lsr #32
    //     0x94528c: b.eq            #0x945294
    //     0x945290: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x945294: r0 = current()
    //     0x945294: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0x945298: r0 = _File()
    //     0x945298: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0x94529c: ldur            x1, [fp, #-8]
    // 0x9452a0: stur            x0, [fp, #-0x18]
    // 0x9452a4: StoreField: r0->field_7 = r1
    //     0x9452a4: stur            w1, [x0, #7]
    // 0x9452a8: r0 = _toUtf8Array()
    //     0x9452a8: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x9452ac: ldur            x3, [fp, #-0x18]
    // 0x9452b0: StoreField: r3->field_b = r0
    //     0x9452b0: stur            w0, [x3, #0xb]
    //     0x9452b4: ldurb           w16, [x3, #-1]
    //     0x9452b8: ldurb           w17, [x0, #-1]
    //     0x9452bc: and             x16, x17, x16, lsr #2
    //     0x9452c0: tst             x16, HEAP, lsr #32
    //     0x9452c4: b.eq            #0x9452cc
    //     0x9452c8: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9452cc: ldur            x0, [fp, #-0x10]
    // 0x9452d0: LoadField: r4 = r0->field_1b
    //     0x9452d0: ldur            w4, [x0, #0x1b]
    // 0x9452d4: DecompressPointer r4
    //     0x9452d4: add             x4, x4, HEAP, lsl #32
    // 0x9452d8: stur            x4, [fp, #-8]
    // 0x9452dc: LoadField: r2 = r4->field_7
    //     0x9452dc: ldur            w2, [x4, #7]
    // 0x9452e0: DecompressPointer r2
    //     0x9452e0: add             x2, x2, HEAP, lsl #32
    // 0x9452e4: mov             x0, x3
    // 0x9452e8: r1 = Null
    //     0x9452e8: mov             x1, NULL
    // 0x9452ec: cmp             w2, NULL
    // 0x9452f0: b.eq            #0x945310
    // 0x9452f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x9452f4: ldur            w4, [x2, #0x17]
    // 0x9452f8: DecompressPointer r4
    //     0x9452f8: add             x4, x4, HEAP, lsl #32
    // 0x9452fc: r8 = X0
    //     0x9452fc: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x945300: LoadField: r9 = r4->field_7
    //     0x945300: ldur            x9, [x4, #7]
    // 0x945304: r3 = Null
    //     0x945304: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a6d0] Null
    //     0x945308: ldr             x3, [x3, #0x6d0]
    // 0x94530c: blr             x9
    // 0x945310: ldur            x0, [fp, #-8]
    // 0x945314: LoadField: r1 = r0->field_b
    //     0x945314: ldur            w1, [x0, #0xb]
    // 0x945318: LoadField: r2 = r0->field_f
    //     0x945318: ldur            w2, [x0, #0xf]
    // 0x94531c: DecompressPointer r2
    //     0x94531c: add             x2, x2, HEAP, lsl #32
    // 0x945320: LoadField: r3 = r2->field_b
    //     0x945320: ldur            w3, [x2, #0xb]
    // 0x945324: r2 = LoadInt32Instr(r1)
    //     0x945324: sbfx            x2, x1, #1, #0x1f
    // 0x945328: stur            x2, [fp, #-0x20]
    // 0x94532c: r1 = LoadInt32Instr(r3)
    //     0x94532c: sbfx            x1, x3, #1, #0x1f
    // 0x945330: cmp             x2, x1
    // 0x945334: b.ne            #0x945340
    // 0x945338: mov             x1, x0
    // 0x94533c: r0 = _growToNextCapacity()
    //     0x94533c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x945340: ldur            x2, [fp, #-8]
    // 0x945344: ldur            x3, [fp, #-0x20]
    // 0x945348: add             x4, x3, #1
    // 0x94534c: lsl             x5, x4, #1
    // 0x945350: StoreField: r2->field_b = r5
    //     0x945350: stur            w5, [x2, #0xb]
    // 0x945354: LoadField: r1 = r2->field_f
    //     0x945354: ldur            w1, [x2, #0xf]
    // 0x945358: DecompressPointer r1
    //     0x945358: add             x1, x1, HEAP, lsl #32
    // 0x94535c: ldur            x0, [fp, #-0x18]
    // 0x945360: ArrayStore: r1[r3] = r0  ; List_4
    //     0x945360: add             x25, x1, x3, lsl #2
    //     0x945364: add             x25, x25, #0xf
    //     0x945368: str             w0, [x25]
    //     0x94536c: tbz             w0, #0, #0x945388
    //     0x945370: ldurb           w16, [x1, #-1]
    //     0x945374: ldurb           w17, [x0, #-1]
    //     0x945378: and             x16, x17, x16, lsr #2
    //     0x94537c: tst             x16, HEAP, lsr #32
    //     0x945380: b.eq            #0x945388
    //     0x945384: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x945388: r0 = Null
    //     0x945388: mov             x0, NULL
    // 0x94538c: LeaveFrame
    //     0x94538c: mov             SP, fp
    //     0x945390: ldp             fp, lr, [SP], #0x10
    // 0x945394: ret
    //     0x945394: ret             
    // 0x945398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945398: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94539c: b               #0x945164
    // 0x9453a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9453a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9453a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9453a4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9453a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9453a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb9dc84, size: 0x7b4
    // 0xb9dc84: EnterFrame
    //     0xb9dc84: stp             fp, lr, [SP, #-0x10]!
    //     0xb9dc88: mov             fp, SP
    // 0xb9dc8c: AllocStack(0x70)
    //     0xb9dc8c: sub             SP, SP, #0x70
    // 0xb9dc90: SetupParameters(_BagImagesState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xb9dc90: mov             x0, x2
    //     0xb9dc94: stur            x2, [fp, #-0x18]
    //     0xb9dc98: mov             x2, x1
    //     0xb9dc9c: stur            x1, [fp, #-0x10]
    // 0xb9dca0: CheckStackOverflow
    //     0xb9dca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9dca4: cmp             SP, x16
    //     0xb9dca8: b.ls            #0xb9e414
    // 0xb9dcac: LoadField: r1 = r2->field_b
    //     0xb9dcac: ldur            w1, [x2, #0xb]
    // 0xb9dcb0: DecompressPointer r1
    //     0xb9dcb0: add             x1, x1, HEAP, lsl #32
    // 0xb9dcb4: cmp             w1, NULL
    // 0xb9dcb8: b.eq            #0xb9e41c
    // 0xb9dcbc: LoadField: r3 = r1->field_b
    //     0xb9dcbc: ldur            w3, [x1, #0xb]
    // 0xb9dcc0: DecompressPointer r3
    //     0xb9dcc0: add             x3, x3, HEAP, lsl #32
    // 0xb9dcc4: cmp             w3, NULL
    // 0xb9dcc8: b.ne            #0xb9dcd4
    // 0xb9dccc: r4 = Null
    //     0xb9dccc: mov             x4, NULL
    // 0xb9dcd0: b               #0xb9dd00
    // 0xb9dcd4: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb9dcd4: ldur            w4, [x3, #0x17]
    // 0xb9dcd8: DecompressPointer r4
    //     0xb9dcd8: add             x4, x4, HEAP, lsl #32
    // 0xb9dcdc: cmp             w4, NULL
    // 0xb9dce0: b.ne            #0xb9dcec
    // 0xb9dce4: r4 = Null
    //     0xb9dce4: mov             x4, NULL
    // 0xb9dce8: b               #0xb9dd00
    // 0xb9dcec: LoadField: r5 = r4->field_7
    //     0xb9dcec: ldur            w5, [x4, #7]
    // 0xb9dcf0: cbnz            w5, #0xb9dcfc
    // 0xb9dcf4: r4 = false
    //     0xb9dcf4: add             x4, NULL, #0x30  ; false
    // 0xb9dcf8: b               #0xb9dd00
    // 0xb9dcfc: r4 = true
    //     0xb9dcfc: add             x4, NULL, #0x20  ; true
    // 0xb9dd00: cmp             w4, NULL
    // 0xb9dd04: b.ne            #0xb9dd50
    // 0xb9dd08: mov             x5, x2
    // 0xb9dd0c: r9 = Instance_CrossAxisAlignment
    //     0xb9dd0c: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9dd10: ldr             x9, [x9, #0x890]
    // 0xb9dd14: r7 = Instance_MainAxisAlignment
    //     0xb9dd14: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9dd18: ldr             x7, [x7, #0xa08]
    // 0xb9dd1c: r2 = 6
    //     0xb9dd1c: movz            x2, #0x6
    // 0xb9dd20: r4 = 4
    //     0xb9dd20: movz            x4, #0x4
    // 0xb9dd24: r3 = Instance_CrossAxisAlignment
    //     0xb9dd24: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9dd28: ldr             x3, [x3, #0xa18]
    // 0xb9dd2c: r8 = Instance_MainAxisSize
    //     0xb9dd2c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9dd30: ldr             x8, [x8, #0xa10]
    // 0xb9dd34: r0 = Instance_Axis
    //     0xb9dd34: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9dd38: r10 = Instance_VerticalDirection
    //     0xb9dd38: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9dd3c: ldr             x10, [x10, #0xa20]
    // 0xb9dd40: r6 = Instance_Axis
    //     0xb9dd40: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9dd44: r11 = Instance_Clip
    //     0xb9dd44: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9dd48: ldr             x11, [x11, #0x38]
    // 0xb9dd4c: b               #0xb9e100
    // 0xb9dd50: tbnz            w4, #4, #0xb9e0bc
    // 0xb9dd54: cmp             w3, NULL
    // 0xb9dd58: b.ne            #0xb9dd64
    // 0xb9dd5c: r1 = Null
    //     0xb9dd5c: mov             x1, NULL
    // 0xb9dd60: b               #0xb9dd6c
    // 0xb9dd64: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb9dd64: ldur            w1, [x3, #0x17]
    // 0xb9dd68: DecompressPointer r1
    //     0xb9dd68: add             x1, x1, HEAP, lsl #32
    // 0xb9dd6c: cmp             w1, NULL
    // 0xb9dd70: b.ne            #0xb9dd80
    // 0xb9dd74: r3 = " :"
    //     0xb9dd74: add             x3, PP, #0x40, lsl #12  ; [pp+0x407b8] " :"
    //     0xb9dd78: ldr             x3, [x3, #0x7b8]
    // 0xb9dd7c: b               #0xb9dd84
    // 0xb9dd80: mov             x3, x1
    // 0xb9dd84: mov             x1, x0
    // 0xb9dd88: stur            x3, [fp, #-8]
    // 0xb9dd8c: r0 = of()
    //     0xb9dd8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9dd90: LoadField: r1 = r0->field_87
    //     0xb9dd90: ldur            w1, [x0, #0x87]
    // 0xb9dd94: DecompressPointer r1
    //     0xb9dd94: add             x1, x1, HEAP, lsl #32
    // 0xb9dd98: LoadField: r0 = r1->field_2b
    //     0xb9dd98: ldur            w0, [x1, #0x2b]
    // 0xb9dd9c: DecompressPointer r0
    //     0xb9dd9c: add             x0, x0, HEAP, lsl #32
    // 0xb9dda0: stur            x0, [fp, #-0x20]
    // 0xb9dda4: r1 = Instance_Color
    //     0xb9dda4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9dda8: d0 = 0.400000
    //     0xb9dda8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb9ddac: r0 = withOpacity()
    //     0xb9ddac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9ddb0: r16 = 14.000000
    //     0xb9ddb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9ddb4: ldr             x16, [x16, #0x1d8]
    // 0xb9ddb8: stp             x0, x16, [SP]
    // 0xb9ddbc: ldur            x1, [fp, #-0x20]
    // 0xb9ddc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9ddc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9ddc4: ldr             x4, [x4, #0xaa0]
    // 0xb9ddc8: r0 = copyWith()
    //     0xb9ddc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9ddcc: stur            x0, [fp, #-0x20]
    // 0xb9ddd0: r0 = Text()
    //     0xb9ddd0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9ddd4: mov             x1, x0
    // 0xb9ddd8: ldur            x0, [fp, #-8]
    // 0xb9dddc: stur            x1, [fp, #-0x28]
    // 0xb9dde0: StoreField: r1->field_b = r0
    //     0xb9dde0: stur            w0, [x1, #0xb]
    // 0xb9dde4: ldur            x0, [fp, #-0x20]
    // 0xb9dde8: StoreField: r1->field_13 = r0
    //     0xb9dde8: stur            w0, [x1, #0x13]
    // 0xb9ddec: ldur            x2, [fp, #-0x10]
    // 0xb9ddf0: LoadField: r0 = r2->field_b
    //     0xb9ddf0: ldur            w0, [x2, #0xb]
    // 0xb9ddf4: DecompressPointer r0
    //     0xb9ddf4: add             x0, x0, HEAP, lsl #32
    // 0xb9ddf8: cmp             w0, NULL
    // 0xb9ddfc: b.eq            #0xb9e420
    // 0xb9de00: LoadField: r3 = r0->field_b
    //     0xb9de00: ldur            w3, [x0, #0xb]
    // 0xb9de04: DecompressPointer r3
    //     0xb9de04: add             x3, x3, HEAP, lsl #32
    // 0xb9de08: cmp             w3, NULL
    // 0xb9de0c: b.ne            #0xb9de18
    // 0xb9de10: r0 = Null
    //     0xb9de10: mov             x0, NULL
    // 0xb9de14: b               #0xb9de44
    // 0xb9de18: LoadField: r0 = r3->field_2b
    //     0xb9de18: ldur            w0, [x3, #0x2b]
    // 0xb9de1c: DecompressPointer r0
    //     0xb9de1c: add             x0, x0, HEAP, lsl #32
    // 0xb9de20: r3 = LoadClassIdInstr(r0)
    //     0xb9de20: ldur            x3, [x0, #-1]
    //     0xb9de24: ubfx            x3, x3, #0xc, #0x14
    // 0xb9de28: str             x0, [SP]
    // 0xb9de2c: mov             x0, x3
    // 0xb9de30: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb9de30: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb9de34: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb9de34: movz            x17, #0x2700
    //     0xb9de38: add             lr, x0, x17
    //     0xb9de3c: ldr             lr, [x21, lr, lsl #3]
    //     0xb9de40: blr             lr
    // 0xb9de44: cmp             w0, NULL
    // 0xb9de48: b.ne            #0xb9de54
    // 0xb9de4c: r3 = " "
    //     0xb9de4c: ldr             x3, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb9de50: b               #0xb9de58
    // 0xb9de54: mov             x3, x0
    // 0xb9de58: ldur            x2, [fp, #-0x10]
    // 0xb9de5c: ldur            x0, [fp, #-0x28]
    // 0xb9de60: ldur            x1, [fp, #-0x18]
    // 0xb9de64: stur            x3, [fp, #-8]
    // 0xb9de68: r0 = of()
    //     0xb9de68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9de6c: LoadField: r1 = r0->field_87
    //     0xb9de6c: ldur            w1, [x0, #0x87]
    // 0xb9de70: DecompressPointer r1
    //     0xb9de70: add             x1, x1, HEAP, lsl #32
    // 0xb9de74: LoadField: r0 = r1->field_2b
    //     0xb9de74: ldur            w0, [x1, #0x2b]
    // 0xb9de78: DecompressPointer r0
    //     0xb9de78: add             x0, x0, HEAP, lsl #32
    // 0xb9de7c: r16 = 14.000000
    //     0xb9de7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9de80: ldr             x16, [x16, #0x1d8]
    // 0xb9de84: r30 = Instance_Color
    //     0xb9de84: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9de88: stp             lr, x16, [SP]
    // 0xb9de8c: mov             x1, x0
    // 0xb9de90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9de90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9de94: ldr             x4, [x4, #0xaa0]
    // 0xb9de98: r0 = copyWith()
    //     0xb9de98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9de9c: stur            x0, [fp, #-0x20]
    // 0xb9dea0: r0 = Text()
    //     0xb9dea0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9dea4: mov             x3, x0
    // 0xb9dea8: ldur            x0, [fp, #-8]
    // 0xb9deac: stur            x3, [fp, #-0x30]
    // 0xb9deb0: StoreField: r3->field_b = r0
    //     0xb9deb0: stur            w0, [x3, #0xb]
    // 0xb9deb4: ldur            x0, [fp, #-0x20]
    // 0xb9deb8: StoreField: r3->field_13 = r0
    //     0xb9deb8: stur            w0, [x3, #0x13]
    // 0xb9debc: r1 = Null
    //     0xb9debc: mov             x1, NULL
    // 0xb9dec0: r2 = 6
    //     0xb9dec0: movz            x2, #0x6
    // 0xb9dec4: r0 = AllocateArray()
    //     0xb9dec4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9dec8: mov             x2, x0
    // 0xb9decc: ldur            x0, [fp, #-0x28]
    // 0xb9ded0: stur            x2, [fp, #-8]
    // 0xb9ded4: StoreField: r2->field_f = r0
    //     0xb9ded4: stur            w0, [x2, #0xf]
    // 0xb9ded8: r16 = Instance_Spacer
    //     0xb9ded8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb9dedc: ldr             x16, [x16, #0xf0]
    // 0xb9dee0: StoreField: r2->field_13 = r16
    //     0xb9dee0: stur            w16, [x2, #0x13]
    // 0xb9dee4: ldur            x0, [fp, #-0x30]
    // 0xb9dee8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9dee8: stur            w0, [x2, #0x17]
    // 0xb9deec: r1 = <Widget>
    //     0xb9deec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9def0: r0 = AllocateGrowableArray()
    //     0xb9def0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9def4: mov             x1, x0
    // 0xb9def8: ldur            x0, [fp, #-8]
    // 0xb9defc: stur            x1, [fp, #-0x20]
    // 0xb9df00: StoreField: r1->field_f = r0
    //     0xb9df00: stur            w0, [x1, #0xf]
    // 0xb9df04: r2 = 6
    //     0xb9df04: movz            x2, #0x6
    // 0xb9df08: StoreField: r1->field_b = r2
    //     0xb9df08: stur            w2, [x1, #0xb]
    // 0xb9df0c: r0 = Row()
    //     0xb9df0c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9df10: mov             x1, x0
    // 0xb9df14: r0 = Instance_Axis
    //     0xb9df14: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9df18: stur            x1, [fp, #-0x28]
    // 0xb9df1c: StoreField: r1->field_f = r0
    //     0xb9df1c: stur            w0, [x1, #0xf]
    // 0xb9df20: r0 = Instance_MainAxisAlignment
    //     0xb9df20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9df24: ldr             x0, [x0, #0xa08]
    // 0xb9df28: StoreField: r1->field_13 = r0
    //     0xb9df28: stur            w0, [x1, #0x13]
    // 0xb9df2c: r2 = Instance_MainAxisSize
    //     0xb9df2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9df30: ldr             x2, [x2, #0xa10]
    // 0xb9df34: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9df34: stur            w2, [x1, #0x17]
    // 0xb9df38: r3 = Instance_CrossAxisAlignment
    //     0xb9df38: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9df3c: ldr             x3, [x3, #0xa18]
    // 0xb9df40: StoreField: r1->field_1b = r3
    //     0xb9df40: stur            w3, [x1, #0x1b]
    // 0xb9df44: r3 = Instance_VerticalDirection
    //     0xb9df44: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9df48: ldr             x3, [x3, #0xa20]
    // 0xb9df4c: StoreField: r1->field_23 = r3
    //     0xb9df4c: stur            w3, [x1, #0x23]
    // 0xb9df50: r4 = Instance_Clip
    //     0xb9df50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9df54: ldr             x4, [x4, #0x38]
    // 0xb9df58: StoreField: r1->field_2b = r4
    //     0xb9df58: stur            w4, [x1, #0x2b]
    // 0xb9df5c: StoreField: r1->field_2f = rZR
    //     0xb9df5c: stur            xzr, [x1, #0x2f]
    // 0xb9df60: ldur            x5, [fp, #-0x20]
    // 0xb9df64: StoreField: r1->field_b = r5
    //     0xb9df64: stur            w5, [x1, #0xb]
    // 0xb9df68: ldur            x5, [fp, #-0x10]
    // 0xb9df6c: LoadField: r6 = r5->field_13
    //     0xb9df6c: ldur            w6, [x5, #0x13]
    // 0xb9df70: DecompressPointer r6
    //     0xb9df70: add             x6, x6, HEAP, lsl #32
    // 0xb9df74: r16 = Sentinel
    //     0xb9df74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb9df78: cmp             w6, w16
    // 0xb9df7c: b.eq            #0xb9e424
    // 0xb9df80: stur            x6, [fp, #-8]
    // 0xb9df84: r0 = ImageHeaders.forImages()
    //     0xb9df84: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xb9df88: r1 = Function '<anonymous closure>':.
    //     0xb9df88: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a6b8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9df8c: ldr             x1, [x1, #0x6b8]
    // 0xb9df90: r2 = Null
    //     0xb9df90: mov             x2, NULL
    // 0xb9df94: stur            x0, [fp, #-0x20]
    // 0xb9df98: r0 = AllocateClosure()
    //     0xb9df98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9df9c: r1 = Function '<anonymous closure>':.
    //     0xb9df9c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a6c0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9dfa0: ldr             x1, [x1, #0x6c0]
    // 0xb9dfa4: r2 = Null
    //     0xb9dfa4: mov             x2, NULL
    // 0xb9dfa8: stur            x0, [fp, #-0x30]
    // 0xb9dfac: r0 = AllocateClosure()
    //     0xb9dfac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9dfb0: stur            x0, [fp, #-0x38]
    // 0xb9dfb4: r0 = CachedNetworkImage()
    //     0xb9dfb4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb9dfb8: stur            x0, [fp, #-0x40]
    // 0xb9dfbc: r16 = 40.000000
    //     0xb9dfbc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb9dfc0: ldr             x16, [x16, #8]
    // 0xb9dfc4: r30 = 40.000000
    //     0xb9dfc4: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb9dfc8: ldr             lr, [lr, #8]
    // 0xb9dfcc: stp             lr, x16, [SP, #0x20]
    // 0xb9dfd0: ldur            x16, [fp, #-0x20]
    // 0xb9dfd4: r30 = Instance_BoxFit
    //     0xb9dfd4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb9dfd8: ldr             lr, [lr, #0x118]
    // 0xb9dfdc: stp             lr, x16, [SP, #0x10]
    // 0xb9dfe0: ldur            x16, [fp, #-0x30]
    // 0xb9dfe4: ldur            lr, [fp, #-0x38]
    // 0xb9dfe8: stp             lr, x16, [SP]
    // 0xb9dfec: mov             x1, x0
    // 0xb9dff0: ldur            x2, [fp, #-8]
    // 0xb9dff4: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xb9dff4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fbc8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xb9dff8: ldr             x4, [x4, #0xbc8]
    // 0xb9dffc: r0 = CachedNetworkImage()
    //     0xb9dffc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb9e000: r0 = Padding()
    //     0xb9e000: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb9e004: mov             x3, x0
    // 0xb9e008: r0 = Instance_EdgeInsets
    //     0xb9e008: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb9e00c: ldr             x0, [x0, #0x668]
    // 0xb9e010: stur            x3, [fp, #-8]
    // 0xb9e014: StoreField: r3->field_f = r0
    //     0xb9e014: stur            w0, [x3, #0xf]
    // 0xb9e018: ldur            x0, [fp, #-0x40]
    // 0xb9e01c: StoreField: r3->field_b = r0
    //     0xb9e01c: stur            w0, [x3, #0xb]
    // 0xb9e020: r1 = Null
    //     0xb9e020: mov             x1, NULL
    // 0xb9e024: r2 = 4
    //     0xb9e024: movz            x2, #0x4
    // 0xb9e028: r0 = AllocateArray()
    //     0xb9e028: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9e02c: mov             x2, x0
    // 0xb9e030: ldur            x0, [fp, #-0x28]
    // 0xb9e034: stur            x2, [fp, #-0x20]
    // 0xb9e038: StoreField: r2->field_f = r0
    //     0xb9e038: stur            w0, [x2, #0xf]
    // 0xb9e03c: ldur            x0, [fp, #-8]
    // 0xb9e040: StoreField: r2->field_13 = r0
    //     0xb9e040: stur            w0, [x2, #0x13]
    // 0xb9e044: r1 = <Widget>
    //     0xb9e044: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9e048: r0 = AllocateGrowableArray()
    //     0xb9e048: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9e04c: mov             x1, x0
    // 0xb9e050: ldur            x0, [fp, #-0x20]
    // 0xb9e054: stur            x1, [fp, #-8]
    // 0xb9e058: StoreField: r1->field_f = r0
    //     0xb9e058: stur            w0, [x1, #0xf]
    // 0xb9e05c: r4 = 4
    //     0xb9e05c: movz            x4, #0x4
    // 0xb9e060: StoreField: r1->field_b = r4
    //     0xb9e060: stur            w4, [x1, #0xb]
    // 0xb9e064: r0 = Column()
    //     0xb9e064: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9e068: r6 = Instance_Axis
    //     0xb9e068: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9e06c: StoreField: r0->field_f = r6
    //     0xb9e06c: stur            w6, [x0, #0xf]
    // 0xb9e070: r7 = Instance_MainAxisAlignment
    //     0xb9e070: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9e074: ldr             x7, [x7, #0xa08]
    // 0xb9e078: StoreField: r0->field_13 = r7
    //     0xb9e078: stur            w7, [x0, #0x13]
    // 0xb9e07c: r8 = Instance_MainAxisSize
    //     0xb9e07c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9e080: ldr             x8, [x8, #0xa10]
    // 0xb9e084: ArrayStore: r0[0] = r8  ; List_4
    //     0xb9e084: stur            w8, [x0, #0x17]
    // 0xb9e088: r9 = Instance_CrossAxisAlignment
    //     0xb9e088: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9e08c: ldr             x9, [x9, #0x890]
    // 0xb9e090: StoreField: r0->field_1b = r9
    //     0xb9e090: stur            w9, [x0, #0x1b]
    // 0xb9e094: r10 = Instance_VerticalDirection
    //     0xb9e094: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9e098: ldr             x10, [x10, #0xa20]
    // 0xb9e09c: StoreField: r0->field_23 = r10
    //     0xb9e09c: stur            w10, [x0, #0x23]
    // 0xb9e0a0: r11 = Instance_Clip
    //     0xb9e0a0: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9e0a4: ldr             x11, [x11, #0x38]
    // 0xb9e0a8: StoreField: r0->field_2b = r11
    //     0xb9e0a8: stur            w11, [x0, #0x2b]
    // 0xb9e0ac: StoreField: r0->field_2f = rZR
    //     0xb9e0ac: stur            xzr, [x0, #0x2f]
    // 0xb9e0b0: ldur            x1, [fp, #-8]
    // 0xb9e0b4: StoreField: r0->field_b = r1
    //     0xb9e0b4: stur            w1, [x0, #0xb]
    // 0xb9e0b8: b               #0xb9e408
    // 0xb9e0bc: mov             x5, x2
    // 0xb9e0c0: r9 = Instance_CrossAxisAlignment
    //     0xb9e0c0: add             x9, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9e0c4: ldr             x9, [x9, #0x890]
    // 0xb9e0c8: r7 = Instance_MainAxisAlignment
    //     0xb9e0c8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9e0cc: ldr             x7, [x7, #0xa08]
    // 0xb9e0d0: r2 = 6
    //     0xb9e0d0: movz            x2, #0x6
    // 0xb9e0d4: r4 = 4
    //     0xb9e0d4: movz            x4, #0x4
    // 0xb9e0d8: r3 = Instance_CrossAxisAlignment
    //     0xb9e0d8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9e0dc: ldr             x3, [x3, #0xa18]
    // 0xb9e0e0: r8 = Instance_MainAxisSize
    //     0xb9e0e0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9e0e4: ldr             x8, [x8, #0xa10]
    // 0xb9e0e8: r0 = Instance_Axis
    //     0xb9e0e8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9e0ec: r10 = Instance_VerticalDirection
    //     0xb9e0ec: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9e0f0: ldr             x10, [x10, #0xa20]
    // 0xb9e0f4: r6 = Instance_Axis
    //     0xb9e0f4: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9e0f8: r11 = Instance_Clip
    //     0xb9e0f8: add             x11, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9e0fc: ldr             x11, [x11, #0x38]
    // 0xb9e100: LoadField: r12 = r1->field_f
    //     0xb9e100: ldur            w12, [x1, #0xf]
    // 0xb9e104: DecompressPointer r12
    //     0xb9e104: add             x12, x12, HEAP, lsl #32
    // 0xb9e108: cmp             w12, NULL
    // 0xb9e10c: b.ne            #0xb9e118
    // 0xb9e110: r1 = Null
    //     0xb9e110: mov             x1, NULL
    // 0xb9e114: b               #0xb9e120
    // 0xb9e118: ArrayLoad: r1 = r12[0]  ; List_4
    //     0xb9e118: ldur            w1, [x12, #0x17]
    // 0xb9e11c: DecompressPointer r1
    //     0xb9e11c: add             x1, x1, HEAP, lsl #32
    // 0xb9e120: cmp             w1, NULL
    // 0xb9e124: b.ne            #0xb9e134
    // 0xb9e128: r12 = " :"
    //     0xb9e128: add             x12, PP, #0x40, lsl #12  ; [pp+0x407b8] " :"
    //     0xb9e12c: ldr             x12, [x12, #0x7b8]
    // 0xb9e130: b               #0xb9e138
    // 0xb9e134: mov             x12, x1
    // 0xb9e138: ldur            x1, [fp, #-0x18]
    // 0xb9e13c: stur            x12, [fp, #-8]
    // 0xb9e140: r0 = of()
    //     0xb9e140: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9e144: LoadField: r1 = r0->field_87
    //     0xb9e144: ldur            w1, [x0, #0x87]
    // 0xb9e148: DecompressPointer r1
    //     0xb9e148: add             x1, x1, HEAP, lsl #32
    // 0xb9e14c: LoadField: r0 = r1->field_2b
    //     0xb9e14c: ldur            w0, [x1, #0x2b]
    // 0xb9e150: DecompressPointer r0
    //     0xb9e150: add             x0, x0, HEAP, lsl #32
    // 0xb9e154: stur            x0, [fp, #-0x20]
    // 0xb9e158: r1 = Instance_Color
    //     0xb9e158: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9e15c: d0 = 0.400000
    //     0xb9e15c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb9e160: r0 = withOpacity()
    //     0xb9e160: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb9e164: r16 = 14.000000
    //     0xb9e164: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9e168: ldr             x16, [x16, #0x1d8]
    // 0xb9e16c: stp             x0, x16, [SP]
    // 0xb9e170: ldur            x1, [fp, #-0x20]
    // 0xb9e174: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9e174: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9e178: ldr             x4, [x4, #0xaa0]
    // 0xb9e17c: r0 = copyWith()
    //     0xb9e17c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9e180: stur            x0, [fp, #-0x20]
    // 0xb9e184: r0 = Text()
    //     0xb9e184: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9e188: mov             x1, x0
    // 0xb9e18c: ldur            x0, [fp, #-8]
    // 0xb9e190: stur            x1, [fp, #-0x28]
    // 0xb9e194: StoreField: r1->field_b = r0
    //     0xb9e194: stur            w0, [x1, #0xb]
    // 0xb9e198: ldur            x0, [fp, #-0x20]
    // 0xb9e19c: StoreField: r1->field_13 = r0
    //     0xb9e19c: stur            w0, [x1, #0x13]
    // 0xb9e1a0: ldur            x2, [fp, #-0x10]
    // 0xb9e1a4: LoadField: r0 = r2->field_b
    //     0xb9e1a4: ldur            w0, [x2, #0xb]
    // 0xb9e1a8: DecompressPointer r0
    //     0xb9e1a8: add             x0, x0, HEAP, lsl #32
    // 0xb9e1ac: cmp             w0, NULL
    // 0xb9e1b0: b.eq            #0xb9e430
    // 0xb9e1b4: LoadField: r3 = r0->field_f
    //     0xb9e1b4: ldur            w3, [x0, #0xf]
    // 0xb9e1b8: DecompressPointer r3
    //     0xb9e1b8: add             x3, x3, HEAP, lsl #32
    // 0xb9e1bc: cmp             w3, NULL
    // 0xb9e1c0: b.ne            #0xb9e1cc
    // 0xb9e1c4: r0 = Null
    //     0xb9e1c4: mov             x0, NULL
    // 0xb9e1c8: b               #0xb9e1f8
    // 0xb9e1cc: LoadField: r0 = r3->field_2b
    //     0xb9e1cc: ldur            w0, [x3, #0x2b]
    // 0xb9e1d0: DecompressPointer r0
    //     0xb9e1d0: add             x0, x0, HEAP, lsl #32
    // 0xb9e1d4: r3 = LoadClassIdInstr(r0)
    //     0xb9e1d4: ldur            x3, [x0, #-1]
    //     0xb9e1d8: ubfx            x3, x3, #0xc, #0x14
    // 0xb9e1dc: str             x0, [SP]
    // 0xb9e1e0: mov             x0, x3
    // 0xb9e1e4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb9e1e4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb9e1e8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb9e1e8: movz            x17, #0x2700
    //     0xb9e1ec: add             lr, x0, x17
    //     0xb9e1f0: ldr             lr, [x21, lr, lsl #3]
    //     0xb9e1f4: blr             lr
    // 0xb9e1f8: cmp             w0, NULL
    // 0xb9e1fc: b.ne            #0xb9e208
    // 0xb9e200: r3 = " "
    //     0xb9e200: ldr             x3, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb9e204: b               #0xb9e20c
    // 0xb9e208: mov             x3, x0
    // 0xb9e20c: ldur            x2, [fp, #-0x10]
    // 0xb9e210: ldur            x0, [fp, #-0x28]
    // 0xb9e214: ldur            x1, [fp, #-0x18]
    // 0xb9e218: stur            x3, [fp, #-8]
    // 0xb9e21c: r0 = of()
    //     0xb9e21c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9e220: LoadField: r1 = r0->field_87
    //     0xb9e220: ldur            w1, [x0, #0x87]
    // 0xb9e224: DecompressPointer r1
    //     0xb9e224: add             x1, x1, HEAP, lsl #32
    // 0xb9e228: LoadField: r0 = r1->field_2b
    //     0xb9e228: ldur            w0, [x1, #0x2b]
    // 0xb9e22c: DecompressPointer r0
    //     0xb9e22c: add             x0, x0, HEAP, lsl #32
    // 0xb9e230: r16 = 14.000000
    //     0xb9e230: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb9e234: ldr             x16, [x16, #0x1d8]
    // 0xb9e238: r30 = Instance_Color
    //     0xb9e238: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9e23c: stp             lr, x16, [SP]
    // 0xb9e240: mov             x1, x0
    // 0xb9e244: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9e244: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9e248: ldr             x4, [x4, #0xaa0]
    // 0xb9e24c: r0 = copyWith()
    //     0xb9e24c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9e250: stur            x0, [fp, #-0x18]
    // 0xb9e254: r0 = Text()
    //     0xb9e254: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9e258: mov             x3, x0
    // 0xb9e25c: ldur            x0, [fp, #-8]
    // 0xb9e260: stur            x3, [fp, #-0x20]
    // 0xb9e264: StoreField: r3->field_b = r0
    //     0xb9e264: stur            w0, [x3, #0xb]
    // 0xb9e268: ldur            x0, [fp, #-0x18]
    // 0xb9e26c: StoreField: r3->field_13 = r0
    //     0xb9e26c: stur            w0, [x3, #0x13]
    // 0xb9e270: r1 = Null
    //     0xb9e270: mov             x1, NULL
    // 0xb9e274: r2 = 6
    //     0xb9e274: movz            x2, #0x6
    // 0xb9e278: r0 = AllocateArray()
    //     0xb9e278: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9e27c: mov             x2, x0
    // 0xb9e280: ldur            x0, [fp, #-0x28]
    // 0xb9e284: stur            x2, [fp, #-8]
    // 0xb9e288: StoreField: r2->field_f = r0
    //     0xb9e288: stur            w0, [x2, #0xf]
    // 0xb9e28c: r16 = Instance_Spacer
    //     0xb9e28c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb9e290: ldr             x16, [x16, #0xf0]
    // 0xb9e294: StoreField: r2->field_13 = r16
    //     0xb9e294: stur            w16, [x2, #0x13]
    // 0xb9e298: ldur            x0, [fp, #-0x20]
    // 0xb9e29c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9e29c: stur            w0, [x2, #0x17]
    // 0xb9e2a0: r1 = <Widget>
    //     0xb9e2a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9e2a4: r0 = AllocateGrowableArray()
    //     0xb9e2a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9e2a8: mov             x1, x0
    // 0xb9e2ac: ldur            x0, [fp, #-8]
    // 0xb9e2b0: stur            x1, [fp, #-0x18]
    // 0xb9e2b4: StoreField: r1->field_f = r0
    //     0xb9e2b4: stur            w0, [x1, #0xf]
    // 0xb9e2b8: r0 = 6
    //     0xb9e2b8: movz            x0, #0x6
    // 0xb9e2bc: StoreField: r1->field_b = r0
    //     0xb9e2bc: stur            w0, [x1, #0xb]
    // 0xb9e2c0: r0 = Row()
    //     0xb9e2c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9e2c4: mov             x2, x0
    // 0xb9e2c8: r0 = Instance_Axis
    //     0xb9e2c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9e2cc: stur            x2, [fp, #-0x20]
    // 0xb9e2d0: StoreField: r2->field_f = r0
    //     0xb9e2d0: stur            w0, [x2, #0xf]
    // 0xb9e2d4: r3 = Instance_MainAxisAlignment
    //     0xb9e2d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9e2d8: ldr             x3, [x3, #0xa08]
    // 0xb9e2dc: StoreField: r2->field_13 = r3
    //     0xb9e2dc: stur            w3, [x2, #0x13]
    // 0xb9e2e0: r4 = Instance_MainAxisSize
    //     0xb9e2e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9e2e4: ldr             x4, [x4, #0xa10]
    // 0xb9e2e8: ArrayStore: r2[0] = r4  ; List_4
    //     0xb9e2e8: stur            w4, [x2, #0x17]
    // 0xb9e2ec: r0 = Instance_CrossAxisAlignment
    //     0xb9e2ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9e2f0: ldr             x0, [x0, #0xa18]
    // 0xb9e2f4: StoreField: r2->field_1b = r0
    //     0xb9e2f4: stur            w0, [x2, #0x1b]
    // 0xb9e2f8: r5 = Instance_VerticalDirection
    //     0xb9e2f8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9e2fc: ldr             x5, [x5, #0xa20]
    // 0xb9e300: StoreField: r2->field_23 = r5
    //     0xb9e300: stur            w5, [x2, #0x23]
    // 0xb9e304: r6 = Instance_Clip
    //     0xb9e304: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9e308: ldr             x6, [x6, #0x38]
    // 0xb9e30c: StoreField: r2->field_2b = r6
    //     0xb9e30c: stur            w6, [x2, #0x2b]
    // 0xb9e310: StoreField: r2->field_2f = rZR
    //     0xb9e310: stur            xzr, [x2, #0x2f]
    // 0xb9e314: ldur            x0, [fp, #-0x18]
    // 0xb9e318: StoreField: r2->field_b = r0
    //     0xb9e318: stur            w0, [x2, #0xb]
    // 0xb9e31c: ldur            x0, [fp, #-0x10]
    // 0xb9e320: LoadField: r7 = r0->field_1b
    //     0xb9e320: ldur            w7, [x0, #0x1b]
    // 0xb9e324: DecompressPointer r7
    //     0xb9e324: add             x7, x7, HEAP, lsl #32
    // 0xb9e328: LoadField: r0 = r7->field_b
    //     0xb9e328: ldur            w0, [x7, #0xb]
    // 0xb9e32c: r1 = LoadInt32Instr(r0)
    //     0xb9e32c: sbfx            x1, x0, #1, #0x1f
    // 0xb9e330: mov             x0, x1
    // 0xb9e334: r1 = 0
    //     0xb9e334: movz            x1, #0
    // 0xb9e338: cmp             x1, x0
    // 0xb9e33c: b.hs            #0xb9e434
    // 0xb9e340: LoadField: r0 = r7->field_f
    //     0xb9e340: ldur            w0, [x7, #0xf]
    // 0xb9e344: DecompressPointer r0
    //     0xb9e344: add             x0, x0, HEAP, lsl #32
    // 0xb9e348: LoadField: r1 = r0->field_f
    //     0xb9e348: ldur            w1, [x0, #0xf]
    // 0xb9e34c: DecompressPointer r1
    //     0xb9e34c: add             x1, x1, HEAP, lsl #32
    // 0xb9e350: stur            x1, [fp, #-8]
    // 0xb9e354: r0 = Image()
    //     0xb9e354: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xb9e358: mov             x1, x0
    // 0xb9e35c: ldur            x2, [fp, #-8]
    // 0xb9e360: d0 = 40.000000
    //     0xb9e360: ldr             d0, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xb9e364: d1 = 40.000000
    //     0xb9e364: ldr             d1, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xb9e368: stur            x0, [fp, #-8]
    // 0xb9e36c: r0 = Image.file()
    //     0xb9e36c: bl              #0x9d34d0  ; [package:flutter/src/widgets/image.dart] Image::Image.file
    // 0xb9e370: r1 = Null
    //     0xb9e370: mov             x1, NULL
    // 0xb9e374: r2 = 4
    //     0xb9e374: movz            x2, #0x4
    // 0xb9e378: r0 = AllocateArray()
    //     0xb9e378: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9e37c: mov             x2, x0
    // 0xb9e380: ldur            x0, [fp, #-0x20]
    // 0xb9e384: stur            x2, [fp, #-0x10]
    // 0xb9e388: StoreField: r2->field_f = r0
    //     0xb9e388: stur            w0, [x2, #0xf]
    // 0xb9e38c: ldur            x0, [fp, #-8]
    // 0xb9e390: StoreField: r2->field_13 = r0
    //     0xb9e390: stur            w0, [x2, #0x13]
    // 0xb9e394: r1 = <Widget>
    //     0xb9e394: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9e398: r0 = AllocateGrowableArray()
    //     0xb9e398: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9e39c: mov             x1, x0
    // 0xb9e3a0: ldur            x0, [fp, #-0x10]
    // 0xb9e3a4: stur            x1, [fp, #-8]
    // 0xb9e3a8: StoreField: r1->field_f = r0
    //     0xb9e3a8: stur            w0, [x1, #0xf]
    // 0xb9e3ac: r0 = 4
    //     0xb9e3ac: movz            x0, #0x4
    // 0xb9e3b0: StoreField: r1->field_b = r0
    //     0xb9e3b0: stur            w0, [x1, #0xb]
    // 0xb9e3b4: r0 = Column()
    //     0xb9e3b4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb9e3b8: r1 = Instance_Axis
    //     0xb9e3b8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9e3bc: StoreField: r0->field_f = r1
    //     0xb9e3bc: stur            w1, [x0, #0xf]
    // 0xb9e3c0: r1 = Instance_MainAxisAlignment
    //     0xb9e3c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9e3c4: ldr             x1, [x1, #0xa08]
    // 0xb9e3c8: StoreField: r0->field_13 = r1
    //     0xb9e3c8: stur            w1, [x0, #0x13]
    // 0xb9e3cc: r1 = Instance_MainAxisSize
    //     0xb9e3cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9e3d0: ldr             x1, [x1, #0xa10]
    // 0xb9e3d4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb9e3d4: stur            w1, [x0, #0x17]
    // 0xb9e3d8: r1 = Instance_CrossAxisAlignment
    //     0xb9e3d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb9e3dc: ldr             x1, [x1, #0x890]
    // 0xb9e3e0: StoreField: r0->field_1b = r1
    //     0xb9e3e0: stur            w1, [x0, #0x1b]
    // 0xb9e3e4: r1 = Instance_VerticalDirection
    //     0xb9e3e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9e3e8: ldr             x1, [x1, #0xa20]
    // 0xb9e3ec: StoreField: r0->field_23 = r1
    //     0xb9e3ec: stur            w1, [x0, #0x23]
    // 0xb9e3f0: r1 = Instance_Clip
    //     0xb9e3f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9e3f4: ldr             x1, [x1, #0x38]
    // 0xb9e3f8: StoreField: r0->field_2b = r1
    //     0xb9e3f8: stur            w1, [x0, #0x2b]
    // 0xb9e3fc: StoreField: r0->field_2f = rZR
    //     0xb9e3fc: stur            xzr, [x0, #0x2f]
    // 0xb9e400: ldur            x1, [fp, #-8]
    // 0xb9e404: StoreField: r0->field_b = r1
    //     0xb9e404: stur            w1, [x0, #0xb]
    // 0xb9e408: LeaveFrame
    //     0xb9e408: mov             SP, fp
    //     0xb9e40c: ldp             fp, lr, [SP], #0x10
    // 0xb9e410: ret
    //     0xb9e410: ret             
    // 0xb9e414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9e414: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9e418: b               #0xb9dcac
    // 0xb9e41c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9e41c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9e420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9e420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9e424: r9 = value
    //     0xb9e424: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a6c8] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb9e428: ldr             x9, [x9, #0x6c8]
    // 0xb9e42c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb9e42c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb9e430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9e430: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9e434: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9e434: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4038, size: 0x14, field offset: 0xc
//   const constructor, 
class BagImages extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fdb0, size: 0x48
    // 0xc7fdb0: EnterFrame
    //     0xc7fdb0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fdb4: mov             fp, SP
    // 0xc7fdb8: AllocStack(0x8)
    //     0xc7fdb8: sub             SP, SP, #8
    // 0xc7fdbc: CheckStackOverflow
    //     0xc7fdbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fdc0: cmp             SP, x16
    //     0xc7fdc4: b.ls            #0xc7fdf0
    // 0xc7fdc8: r1 = <BagImages>
    //     0xc7fdc8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d50] TypeArguments: <BagImages>
    //     0xc7fdcc: ldr             x1, [x1, #0xd50]
    // 0xc7fdd0: r0 = _BagImagesState()
    //     0xc7fdd0: bl              #0xc7fdf8  ; Allocate_BagImagesStateStub -> _BagImagesState (size=0x20)
    // 0xc7fdd4: mov             x1, x0
    // 0xc7fdd8: stur            x0, [fp, #-8]
    // 0xc7fddc: r0 = _BagImagesState()
    //     0xc7fddc: bl              #0xc7a0e8  ; [package:customer_app/app/presentation/views/basic/bag/bag_images.dart] _BagImagesState::_BagImagesState
    // 0xc7fde0: ldur            x0, [fp, #-8]
    // 0xc7fde4: LeaveFrame
    //     0xc7fde4: mov             SP, fp
    //     0xc7fde8: ldp             fp, lr, [SP], #0x10
    // 0xc7fdec: ret
    //     0xc7fdec: ret             
    // 0xc7fdf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fdf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fdf4: b               #0xc7fdc8
  }
}
