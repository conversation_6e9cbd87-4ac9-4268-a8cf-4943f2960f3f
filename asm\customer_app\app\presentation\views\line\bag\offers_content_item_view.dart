// lib: , url: package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart

// class id: 1049471, size: 0x8
class :: {
}

// class id: 3287, size: 0x14, field offset: 0x14
class _OffersContentItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xba6fa4, size: 0x2b8
    // 0xba6fa4: EnterFrame
    //     0xba6fa4: stp             fp, lr, [SP, #-0x10]!
    //     0xba6fa8: mov             fp, SP
    // 0xba6fac: AllocStack(0x40)
    //     0xba6fac: sub             SP, SP, #0x40
    // 0xba6fb0: SetupParameters(_OffersContentItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba6fb0: mov             x0, x1
    //     0xba6fb4: stur            x1, [fp, #-8]
    //     0xba6fb8: mov             x1, x2
    //     0xba6fbc: stur            x2, [fp, #-0x10]
    // 0xba6fc0: CheckStackOverflow
    //     0xba6fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba6fc4: cmp             SP, x16
    //     0xba6fc8: b.ls            #0xba724c
    // 0xba6fcc: r1 = 1
    //     0xba6fcc: movz            x1, #0x1
    // 0xba6fd0: r0 = AllocateContext()
    //     0xba6fd0: bl              #0x16f6108  ; AllocateContextStub
    // 0xba6fd4: mov             x2, x0
    // 0xba6fd8: ldur            x0, [fp, #-8]
    // 0xba6fdc: stur            x2, [fp, #-0x28]
    // 0xba6fe0: StoreField: r2->field_f = r0
    //     0xba6fe0: stur            w0, [x2, #0xf]
    // 0xba6fe4: LoadField: r1 = r0->field_b
    //     0xba6fe4: ldur            w1, [x0, #0xb]
    // 0xba6fe8: DecompressPointer r1
    //     0xba6fe8: add             x1, x1, HEAP, lsl #32
    // 0xba6fec: cmp             w1, NULL
    // 0xba6ff0: b.eq            #0xba7254
    // 0xba6ff4: LoadField: r3 = r1->field_f
    //     0xba6ff4: ldur            w3, [x1, #0xf]
    // 0xba6ff8: DecompressPointer r3
    //     0xba6ff8: add             x3, x3, HEAP, lsl #32
    // 0xba6ffc: cmp             w3, NULL
    // 0xba7000: b.ne            #0xba700c
    // 0xba7004: r1 = Null
    //     0xba7004: mov             x1, NULL
    // 0xba7008: b               #0xba7028
    // 0xba700c: LoadField: r1 = r3->field_f
    //     0xba700c: ldur            w1, [x3, #0xf]
    // 0xba7010: DecompressPointer r1
    //     0xba7010: add             x1, x1, HEAP, lsl #32
    // 0xba7014: LoadField: r4 = r1->field_b
    //     0xba7014: ldur            w4, [x1, #0xb]
    // 0xba7018: cbnz            w4, #0xba7024
    // 0xba701c: r1 = false
    //     0xba701c: add             x1, NULL, #0x30  ; false
    // 0xba7020: b               #0xba7028
    // 0xba7024: r1 = true
    //     0xba7024: add             x1, NULL, #0x20  ; true
    // 0xba7028: cmp             w1, NULL
    // 0xba702c: b.ne            #0xba7038
    // 0xba7030: r4 = false
    //     0xba7030: add             x4, NULL, #0x30  ; false
    // 0xba7034: b               #0xba703c
    // 0xba7038: mov             x4, x1
    // 0xba703c: stur            x4, [fp, #-0x20]
    // 0xba7040: cmp             w3, NULL
    // 0xba7044: b.ne            #0xba7050
    // 0xba7048: r1 = Null
    //     0xba7048: mov             x1, NULL
    // 0xba704c: b               #0xba7058
    // 0xba7050: LoadField: r1 = r3->field_b
    //     0xba7050: ldur            w1, [x3, #0xb]
    // 0xba7054: DecompressPointer r1
    //     0xba7054: add             x1, x1, HEAP, lsl #32
    // 0xba7058: cmp             w1, NULL
    // 0xba705c: b.ne            #0xba7068
    // 0xba7060: r3 = ""
    //     0xba7060: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba7064: b               #0xba706c
    // 0xba7068: mov             x3, x1
    // 0xba706c: ldur            x1, [fp, #-0x10]
    // 0xba7070: stur            x3, [fp, #-0x18]
    // 0xba7074: r0 = of()
    //     0xba7074: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba7078: LoadField: r1 = r0->field_87
    //     0xba7078: ldur            w1, [x0, #0x87]
    // 0xba707c: DecompressPointer r1
    //     0xba707c: add             x1, x1, HEAP, lsl #32
    // 0xba7080: LoadField: r0 = r1->field_7
    //     0xba7080: ldur            w0, [x1, #7]
    // 0xba7084: DecompressPointer r0
    //     0xba7084: add             x0, x0, HEAP, lsl #32
    // 0xba7088: r16 = 16.000000
    //     0xba7088: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xba708c: ldr             x16, [x16, #0x188]
    // 0xba7090: r30 = Instance_Color
    //     0xba7090: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba7094: stp             lr, x16, [SP]
    // 0xba7098: mov             x1, x0
    // 0xba709c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba709c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba70a0: ldr             x4, [x4, #0xaa0]
    // 0xba70a4: r0 = copyWith()
    //     0xba70a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba70a8: stur            x0, [fp, #-0x10]
    // 0xba70ac: r0 = Text()
    //     0xba70ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba70b0: mov             x1, x0
    // 0xba70b4: ldur            x0, [fp, #-0x18]
    // 0xba70b8: stur            x1, [fp, #-0x30]
    // 0xba70bc: StoreField: r1->field_b = r0
    //     0xba70bc: stur            w0, [x1, #0xb]
    // 0xba70c0: ldur            x0, [fp, #-0x10]
    // 0xba70c4: StoreField: r1->field_13 = r0
    //     0xba70c4: stur            w0, [x1, #0x13]
    // 0xba70c8: r0 = Padding()
    //     0xba70c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba70cc: mov             x1, x0
    // 0xba70d0: r0 = Instance_EdgeInsets
    //     0xba70d0: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xba70d4: ldr             x0, [x0, #0xf70]
    // 0xba70d8: stur            x1, [fp, #-0x10]
    // 0xba70dc: StoreField: r1->field_f = r0
    //     0xba70dc: stur            w0, [x1, #0xf]
    // 0xba70e0: ldur            x0, [fp, #-0x30]
    // 0xba70e4: StoreField: r1->field_b = r0
    //     0xba70e4: stur            w0, [x1, #0xb]
    // 0xba70e8: r0 = Visibility()
    //     0xba70e8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xba70ec: mov             x3, x0
    // 0xba70f0: ldur            x0, [fp, #-0x10]
    // 0xba70f4: stur            x3, [fp, #-0x18]
    // 0xba70f8: StoreField: r3->field_b = r0
    //     0xba70f8: stur            w0, [x3, #0xb]
    // 0xba70fc: r0 = Instance_SizedBox
    //     0xba70fc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xba7100: StoreField: r3->field_f = r0
    //     0xba7100: stur            w0, [x3, #0xf]
    // 0xba7104: ldur            x0, [fp, #-0x20]
    // 0xba7108: StoreField: r3->field_13 = r0
    //     0xba7108: stur            w0, [x3, #0x13]
    // 0xba710c: r0 = false
    //     0xba710c: add             x0, NULL, #0x30  ; false
    // 0xba7110: ArrayStore: r3[0] = r0  ; List_4
    //     0xba7110: stur            w0, [x3, #0x17]
    // 0xba7114: StoreField: r3->field_1b = r0
    //     0xba7114: stur            w0, [x3, #0x1b]
    // 0xba7118: StoreField: r3->field_1f = r0
    //     0xba7118: stur            w0, [x3, #0x1f]
    // 0xba711c: StoreField: r3->field_23 = r0
    //     0xba711c: stur            w0, [x3, #0x23]
    // 0xba7120: StoreField: r3->field_27 = r0
    //     0xba7120: stur            w0, [x3, #0x27]
    // 0xba7124: StoreField: r3->field_2b = r0
    //     0xba7124: stur            w0, [x3, #0x2b]
    // 0xba7128: ldur            x0, [fp, #-8]
    // 0xba712c: LoadField: r1 = r0->field_b
    //     0xba712c: ldur            w1, [x0, #0xb]
    // 0xba7130: DecompressPointer r1
    //     0xba7130: add             x1, x1, HEAP, lsl #32
    // 0xba7134: cmp             w1, NULL
    // 0xba7138: b.eq            #0xba7258
    // 0xba713c: LoadField: r0 = r1->field_f
    //     0xba713c: ldur            w0, [x1, #0xf]
    // 0xba7140: DecompressPointer r0
    //     0xba7140: add             x0, x0, HEAP, lsl #32
    // 0xba7144: cmp             w0, NULL
    // 0xba7148: b.ne            #0xba7154
    // 0xba714c: r0 = Null
    //     0xba714c: mov             x0, NULL
    // 0xba7150: b               #0xba7160
    // 0xba7154: LoadField: r1 = r0->field_f
    //     0xba7154: ldur            w1, [x0, #0xf]
    // 0xba7158: DecompressPointer r1
    //     0xba7158: add             x1, x1, HEAP, lsl #32
    // 0xba715c: LoadField: r0 = r1->field_b
    //     0xba715c: ldur            w0, [x1, #0xb]
    // 0xba7160: ldur            x2, [fp, #-0x28]
    // 0xba7164: stur            x0, [fp, #-8]
    // 0xba7168: r1 = Function '<anonymous closure>':.
    //     0xba7168: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a5c0] AnonymousClosure: (0xba727c), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba716c: ldr             x1, [x1, #0x5c0]
    // 0xba7170: r0 = AllocateClosure()
    //     0xba7170: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba7174: stur            x0, [fp, #-0x10]
    // 0xba7178: r0 = ListView()
    //     0xba7178: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xba717c: stur            x0, [fp, #-0x20]
    // 0xba7180: r16 = true
    //     0xba7180: add             x16, NULL, #0x20  ; true
    // 0xba7184: r30 = Instance_NeverScrollableScrollPhysics
    //     0xba7184: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xba7188: ldr             lr, [lr, #0x1c8]
    // 0xba718c: stp             lr, x16, [SP]
    // 0xba7190: mov             x1, x0
    // 0xba7194: ldur            x2, [fp, #-0x10]
    // 0xba7198: ldur            x3, [fp, #-8]
    // 0xba719c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xba719c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xba71a0: ldr             x4, [x4, #8]
    // 0xba71a4: r0 = ListView.builder()
    //     0xba71a4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xba71a8: r1 = Null
    //     0xba71a8: mov             x1, NULL
    // 0xba71ac: r2 = 4
    //     0xba71ac: movz            x2, #0x4
    // 0xba71b0: r0 = AllocateArray()
    //     0xba71b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba71b4: mov             x2, x0
    // 0xba71b8: ldur            x0, [fp, #-0x18]
    // 0xba71bc: stur            x2, [fp, #-8]
    // 0xba71c0: StoreField: r2->field_f = r0
    //     0xba71c0: stur            w0, [x2, #0xf]
    // 0xba71c4: ldur            x0, [fp, #-0x20]
    // 0xba71c8: StoreField: r2->field_13 = r0
    //     0xba71c8: stur            w0, [x2, #0x13]
    // 0xba71cc: r1 = <Widget>
    //     0xba71cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba71d0: r0 = AllocateGrowableArray()
    //     0xba71d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba71d4: mov             x1, x0
    // 0xba71d8: ldur            x0, [fp, #-8]
    // 0xba71dc: stur            x1, [fp, #-0x10]
    // 0xba71e0: StoreField: r1->field_f = r0
    //     0xba71e0: stur            w0, [x1, #0xf]
    // 0xba71e4: r0 = 4
    //     0xba71e4: movz            x0, #0x4
    // 0xba71e8: StoreField: r1->field_b = r0
    //     0xba71e8: stur            w0, [x1, #0xb]
    // 0xba71ec: r0 = Column()
    //     0xba71ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba71f0: r1 = Instance_Axis
    //     0xba71f0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba71f4: StoreField: r0->field_f = r1
    //     0xba71f4: stur            w1, [x0, #0xf]
    // 0xba71f8: r1 = Instance_MainAxisAlignment
    //     0xba71f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba71fc: ldr             x1, [x1, #0xa08]
    // 0xba7200: StoreField: r0->field_13 = r1
    //     0xba7200: stur            w1, [x0, #0x13]
    // 0xba7204: r1 = Instance_MainAxisSize
    //     0xba7204: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba7208: ldr             x1, [x1, #0xa10]
    // 0xba720c: ArrayStore: r0[0] = r1  ; List_4
    //     0xba720c: stur            w1, [x0, #0x17]
    // 0xba7210: r1 = Instance_CrossAxisAlignment
    //     0xba7210: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba7214: ldr             x1, [x1, #0x890]
    // 0xba7218: StoreField: r0->field_1b = r1
    //     0xba7218: stur            w1, [x0, #0x1b]
    // 0xba721c: r1 = Instance_VerticalDirection
    //     0xba721c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba7220: ldr             x1, [x1, #0xa20]
    // 0xba7224: StoreField: r0->field_23 = r1
    //     0xba7224: stur            w1, [x0, #0x23]
    // 0xba7228: r1 = Instance_Clip
    //     0xba7228: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba722c: ldr             x1, [x1, #0x38]
    // 0xba7230: StoreField: r0->field_2b = r1
    //     0xba7230: stur            w1, [x0, #0x2b]
    // 0xba7234: StoreField: r0->field_2f = rZR
    //     0xba7234: stur            xzr, [x0, #0x2f]
    // 0xba7238: ldur            x1, [fp, #-0x10]
    // 0xba723c: StoreField: r0->field_b = r1
    //     0xba723c: stur            w1, [x0, #0xb]
    // 0xba7240: LeaveFrame
    //     0xba7240: mov             SP, fp
    //     0xba7244: ldp             fp, lr, [SP], #0x10
    // 0xba7248: ret
    //     0xba7248: ret             
    // 0xba724c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba724c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba7250: b               #0xba6fcc
    // 0xba7254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba7254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba7258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba7258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xba727c, size: 0x20f0
    // 0xba727c: EnterFrame
    //     0xba727c: stp             fp, lr, [SP, #-0x10]!
    //     0xba7280: mov             fp, SP
    // 0xba7284: AllocStack(0x80)
    //     0xba7284: sub             SP, SP, #0x80
    // 0xba7288: SetupParameters()
    //     0xba7288: ldr             x0, [fp, #0x20]
    //     0xba728c: ldur            w1, [x0, #0x17]
    //     0xba7290: add             x1, x1, HEAP, lsl #32
    //     0xba7294: stur            x1, [fp, #-8]
    // 0xba7298: CheckStackOverflow
    //     0xba7298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba729c: cmp             SP, x16
    //     0xba72a0: b.ls            #0xba9298
    // 0xba72a4: r1 = 2
    //     0xba72a4: movz            x1, #0x2
    // 0xba72a8: r0 = AllocateContext()
    //     0xba72a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xba72ac: mov             x3, x0
    // 0xba72b0: ldur            x2, [fp, #-8]
    // 0xba72b4: stur            x3, [fp, #-0x10]
    // 0xba72b8: StoreField: r3->field_b = r2
    //     0xba72b8: stur            w2, [x3, #0xb]
    // 0xba72bc: ldr             x0, [fp, #0x18]
    // 0xba72c0: StoreField: r3->field_f = r0
    //     0xba72c0: stur            w0, [x3, #0xf]
    // 0xba72c4: ldr             x0, [fp, #0x10]
    // 0xba72c8: StoreField: r3->field_13 = r0
    //     0xba72c8: stur            w0, [x3, #0x13]
    // 0xba72cc: LoadField: r1 = r2->field_f
    //     0xba72cc: ldur            w1, [x2, #0xf]
    // 0xba72d0: DecompressPointer r1
    //     0xba72d0: add             x1, x1, HEAP, lsl #32
    // 0xba72d4: LoadField: r4 = r1->field_b
    //     0xba72d4: ldur            w4, [x1, #0xb]
    // 0xba72d8: DecompressPointer r4
    //     0xba72d8: add             x4, x4, HEAP, lsl #32
    // 0xba72dc: cmp             w4, NULL
    // 0xba72e0: b.eq            #0xba92a0
    // 0xba72e4: LoadField: r1 = r4->field_f
    //     0xba72e4: ldur            w1, [x4, #0xf]
    // 0xba72e8: DecompressPointer r1
    //     0xba72e8: add             x1, x1, HEAP, lsl #32
    // 0xba72ec: cmp             w1, NULL
    // 0xba72f0: b.ne            #0xba72fc
    // 0xba72f4: r0 = Null
    //     0xba72f4: mov             x0, NULL
    // 0xba72f8: b               #0xba7340
    // 0xba72fc: LoadField: r4 = r1->field_f
    //     0xba72fc: ldur            w4, [x1, #0xf]
    // 0xba7300: DecompressPointer r4
    //     0xba7300: add             x4, x4, HEAP, lsl #32
    // 0xba7304: LoadField: r1 = r4->field_b
    //     0xba7304: ldur            w1, [x4, #0xb]
    // 0xba7308: r5 = LoadInt32Instr(r0)
    //     0xba7308: sbfx            x5, x0, #1, #0x1f
    //     0xba730c: tbz             w0, #0, #0xba7314
    //     0xba7310: ldur            x5, [x0, #7]
    // 0xba7314: r0 = LoadInt32Instr(r1)
    //     0xba7314: sbfx            x0, x1, #1, #0x1f
    // 0xba7318: mov             x1, x5
    // 0xba731c: cmp             x1, x0
    // 0xba7320: b.hs            #0xba92a4
    // 0xba7324: LoadField: r0 = r4->field_f
    //     0xba7324: ldur            w0, [x4, #0xf]
    // 0xba7328: DecompressPointer r0
    //     0xba7328: add             x0, x0, HEAP, lsl #32
    // 0xba732c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba732c: add             x16, x0, x5, lsl #2
    //     0xba7330: ldur            w1, [x16, #0xf]
    // 0xba7334: DecompressPointer r1
    //     0xba7334: add             x1, x1, HEAP, lsl #32
    // 0xba7338: LoadField: r0 = r1->field_b
    //     0xba7338: ldur            w0, [x1, #0xb]
    // 0xba733c: DecompressPointer r0
    //     0xba733c: add             x0, x0, HEAP, lsl #32
    // 0xba7340: r1 = LoadClassIdInstr(r0)
    //     0xba7340: ldur            x1, [x0, #-1]
    //     0xba7344: ubfx            x1, x1, #0xc, #0x14
    // 0xba7348: r16 = "bumper_coupon"
    //     0xba7348: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xba734c: ldr             x16, [x16, #0x8d8]
    // 0xba7350: stp             x16, x0, [SP]
    // 0xba7354: mov             x0, x1
    // 0xba7358: mov             lr, x0
    // 0xba735c: ldr             lr, [x21, lr, lsl #3]
    // 0xba7360: blr             lr
    // 0xba7364: tbnz            w0, #4, #0xba8304
    // 0xba7368: ldur            x1, [fp, #-8]
    // 0xba736c: LoadField: r0 = r1->field_f
    //     0xba736c: ldur            w0, [x1, #0xf]
    // 0xba7370: DecompressPointer r0
    //     0xba7370: add             x0, x0, HEAP, lsl #32
    // 0xba7374: LoadField: r2 = r0->field_b
    //     0xba7374: ldur            w2, [x0, #0xb]
    // 0xba7378: DecompressPointer r2
    //     0xba7378: add             x2, x2, HEAP, lsl #32
    // 0xba737c: cmp             w2, NULL
    // 0xba7380: b.eq            #0xba92a8
    // 0xba7384: LoadField: r0 = r2->field_b
    //     0xba7384: ldur            w0, [x2, #0xb]
    // 0xba7388: DecompressPointer r0
    //     0xba7388: add             x0, x0, HEAP, lsl #32
    // 0xba738c: r2 = LoadClassIdInstr(r0)
    //     0xba738c: ldur            x2, [x0, #-1]
    //     0xba7390: ubfx            x2, x2, #0xc, #0x14
    // 0xba7394: r16 = "other_offers"
    //     0xba7394: add             x16, PP, #0x57, lsl #12  ; [pp+0x571d0] "other_offers"
    //     0xba7398: ldr             x16, [x16, #0x1d0]
    // 0xba739c: stp             x16, x0, [SP]
    // 0xba73a0: mov             x0, x2
    // 0xba73a4: mov             lr, x0
    // 0xba73a8: ldr             lr, [x21, lr, lsl #3]
    // 0xba73ac: blr             lr
    // 0xba73b0: tbz             w0, #4, #0xba827c
    // 0xba73b4: ldur            x0, [fp, #-8]
    // 0xba73b8: LoadField: r1 = r0->field_f
    //     0xba73b8: ldur            w1, [x0, #0xf]
    // 0xba73bc: DecompressPointer r1
    //     0xba73bc: add             x1, x1, HEAP, lsl #32
    // 0xba73c0: LoadField: r2 = r1->field_b
    //     0xba73c0: ldur            w2, [x1, #0xb]
    // 0xba73c4: DecompressPointer r2
    //     0xba73c4: add             x2, x2, HEAP, lsl #32
    // 0xba73c8: stur            x2, [fp, #-0x30]
    // 0xba73cc: cmp             w2, NULL
    // 0xba73d0: b.eq            #0xba92ac
    // 0xba73d4: LoadField: r1 = r2->field_23
    //     0xba73d4: ldur            w1, [x2, #0x23]
    // 0xba73d8: DecompressPointer r1
    //     0xba73d8: add             x1, x1, HEAP, lsl #32
    // 0xba73dc: LoadField: r3 = r1->field_13
    //     0xba73dc: ldur            w3, [x1, #0x13]
    // 0xba73e0: DecompressPointer r3
    //     0xba73e0: add             x3, x3, HEAP, lsl #32
    // 0xba73e4: cmp             w3, NULL
    // 0xba73e8: b.ne            #0xba73f4
    // 0xba73ec: r1 = Null
    //     0xba73ec: mov             x1, NULL
    // 0xba73f0: b               #0xba73fc
    // 0xba73f4: LoadField: r1 = r3->field_7
    //     0xba73f4: ldur            w1, [x3, #7]
    // 0xba73f8: DecompressPointer r1
    //     0xba73f8: add             x1, x1, HEAP, lsl #32
    // 0xba73fc: cmp             w1, NULL
    // 0xba7400: b.ne            #0xba740c
    // 0xba7404: r1 = 0
    //     0xba7404: movz            x1, #0
    // 0xba7408: b               #0xba741c
    // 0xba740c: r3 = LoadInt32Instr(r1)
    //     0xba740c: sbfx            x3, x1, #1, #0x1f
    //     0xba7410: tbz             w1, #0, #0xba7418
    //     0xba7414: ldur            x3, [x1, #7]
    // 0xba7418: mov             x1, x3
    // 0xba741c: stur            x1, [fp, #-0x28]
    // 0xba7420: LoadField: r3 = r2->field_23
    //     0xba7420: ldur            w3, [x2, #0x23]
    // 0xba7424: DecompressPointer r3
    //     0xba7424: add             x3, x3, HEAP, lsl #32
    // 0xba7428: LoadField: r4 = r3->field_13
    //     0xba7428: ldur            w4, [x3, #0x13]
    // 0xba742c: DecompressPointer r4
    //     0xba742c: add             x4, x4, HEAP, lsl #32
    // 0xba7430: cmp             w4, NULL
    // 0xba7434: b.ne            #0xba7440
    // 0xba7438: r3 = Null
    //     0xba7438: mov             x3, NULL
    // 0xba743c: b               #0xba7448
    // 0xba7440: LoadField: r3 = r4->field_b
    //     0xba7440: ldur            w3, [x4, #0xb]
    // 0xba7444: DecompressPointer r3
    //     0xba7444: add             x3, x3, HEAP, lsl #32
    // 0xba7448: cmp             w3, NULL
    // 0xba744c: b.ne            #0xba7458
    // 0xba7450: r3 = 0
    //     0xba7450: movz            x3, #0
    // 0xba7454: b               #0xba7468
    // 0xba7458: r4 = LoadInt32Instr(r3)
    //     0xba7458: sbfx            x4, x3, #1, #0x1f
    //     0xba745c: tbz             w3, #0, #0xba7464
    //     0xba7460: ldur            x4, [x3, #7]
    // 0xba7464: mov             x3, x4
    // 0xba7468: stur            x3, [fp, #-0x20]
    // 0xba746c: LoadField: r4 = r2->field_23
    //     0xba746c: ldur            w4, [x2, #0x23]
    // 0xba7470: DecompressPointer r4
    //     0xba7470: add             x4, x4, HEAP, lsl #32
    // 0xba7474: LoadField: r5 = r4->field_13
    //     0xba7474: ldur            w5, [x4, #0x13]
    // 0xba7478: DecompressPointer r5
    //     0xba7478: add             x5, x5, HEAP, lsl #32
    // 0xba747c: cmp             w5, NULL
    // 0xba7480: b.ne            #0xba748c
    // 0xba7484: r4 = Null
    //     0xba7484: mov             x4, NULL
    // 0xba7488: b               #0xba7494
    // 0xba748c: LoadField: r4 = r5->field_f
    //     0xba748c: ldur            w4, [x5, #0xf]
    // 0xba7490: DecompressPointer r4
    //     0xba7490: add             x4, x4, HEAP, lsl #32
    // 0xba7494: cmp             w4, NULL
    // 0xba7498: b.ne            #0xba74a4
    // 0xba749c: r4 = 0
    //     0xba749c: movz            x4, #0
    // 0xba74a0: b               #0xba74b4
    // 0xba74a4: r5 = LoadInt32Instr(r4)
    //     0xba74a4: sbfx            x5, x4, #1, #0x1f
    //     0xba74a8: tbz             w4, #0, #0xba74b0
    //     0xba74ac: ldur            x5, [x4, #7]
    // 0xba74b0: mov             x4, x5
    // 0xba74b4: stur            x4, [fp, #-0x18]
    // 0xba74b8: r0 = Color()
    //     0xba74b8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xba74bc: mov             x1, x0
    // 0xba74c0: r0 = Instance_ColorSpace
    //     0xba74c0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xba74c4: stur            x1, [fp, #-0x38]
    // 0xba74c8: StoreField: r1->field_27 = r0
    //     0xba74c8: stur            w0, [x1, #0x27]
    // 0xba74cc: d1 = 1.000000
    //     0xba74cc: fmov            d1, #1.00000000
    // 0xba74d0: StoreField: r1->field_7 = d1
    //     0xba74d0: stur            d1, [x1, #7]
    // 0xba74d4: ldur            x2, [fp, #-0x28]
    // 0xba74d8: ubfx            x2, x2, #0, #0x20
    // 0xba74dc: and             w3, w2, #0xff
    // 0xba74e0: ubfx            x3, x3, #0, #0x20
    // 0xba74e4: scvtf           d0, x3
    // 0xba74e8: d1 = 255.000000
    //     0xba74e8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xba74ec: fdiv            d2, d0, d1
    // 0xba74f0: StoreField: r1->field_f = d2
    //     0xba74f0: stur            d2, [x1, #0xf]
    // 0xba74f4: ldur            x2, [fp, #-0x20]
    // 0xba74f8: ubfx            x2, x2, #0, #0x20
    // 0xba74fc: and             w3, w2, #0xff
    // 0xba7500: ubfx            x3, x3, #0, #0x20
    // 0xba7504: scvtf           d0, x3
    // 0xba7508: fdiv            d2, d0, d1
    // 0xba750c: ArrayStore: r1[0] = d2  ; List_8
    //     0xba750c: stur            d2, [x1, #0x17]
    // 0xba7510: ldur            x2, [fp, #-0x18]
    // 0xba7514: ubfx            x2, x2, #0, #0x20
    // 0xba7518: and             w3, w2, #0xff
    // 0xba751c: ubfx            x3, x3, #0, #0x20
    // 0xba7520: scvtf           d0, x3
    // 0xba7524: fdiv            d2, d0, d1
    // 0xba7528: StoreField: r1->field_1f = d2
    //     0xba7528: stur            d2, [x1, #0x1f]
    // 0xba752c: ldur            x2, [fp, #-0x30]
    // 0xba7530: LoadField: r3 = r2->field_23
    //     0xba7530: ldur            w3, [x2, #0x23]
    // 0xba7534: DecompressPointer r3
    //     0xba7534: add             x3, x3, HEAP, lsl #32
    // 0xba7538: LoadField: r4 = r3->field_13
    //     0xba7538: ldur            w4, [x3, #0x13]
    // 0xba753c: DecompressPointer r4
    //     0xba753c: add             x4, x4, HEAP, lsl #32
    // 0xba7540: cmp             w4, NULL
    // 0xba7544: b.ne            #0xba7550
    // 0xba7548: r3 = Null
    //     0xba7548: mov             x3, NULL
    // 0xba754c: b               #0xba7558
    // 0xba7550: LoadField: r3 = r4->field_7
    //     0xba7550: ldur            w3, [x4, #7]
    // 0xba7554: DecompressPointer r3
    //     0xba7554: add             x3, x3, HEAP, lsl #32
    // 0xba7558: cmp             w3, NULL
    // 0xba755c: b.ne            #0xba7568
    // 0xba7560: r3 = 0
    //     0xba7560: movz            x3, #0
    // 0xba7564: b               #0xba7578
    // 0xba7568: r4 = LoadInt32Instr(r3)
    //     0xba7568: sbfx            x4, x3, #1, #0x1f
    //     0xba756c: tbz             w3, #0, #0xba7574
    //     0xba7570: ldur            x4, [x3, #7]
    // 0xba7574: mov             x3, x4
    // 0xba7578: stur            x3, [fp, #-0x28]
    // 0xba757c: LoadField: r4 = r2->field_23
    //     0xba757c: ldur            w4, [x2, #0x23]
    // 0xba7580: DecompressPointer r4
    //     0xba7580: add             x4, x4, HEAP, lsl #32
    // 0xba7584: LoadField: r5 = r4->field_13
    //     0xba7584: ldur            w5, [x4, #0x13]
    // 0xba7588: DecompressPointer r5
    //     0xba7588: add             x5, x5, HEAP, lsl #32
    // 0xba758c: cmp             w5, NULL
    // 0xba7590: b.ne            #0xba759c
    // 0xba7594: r4 = Null
    //     0xba7594: mov             x4, NULL
    // 0xba7598: b               #0xba75a4
    // 0xba759c: LoadField: r4 = r5->field_b
    //     0xba759c: ldur            w4, [x5, #0xb]
    // 0xba75a0: DecompressPointer r4
    //     0xba75a0: add             x4, x4, HEAP, lsl #32
    // 0xba75a4: cmp             w4, NULL
    // 0xba75a8: b.ne            #0xba75b4
    // 0xba75ac: r4 = 0
    //     0xba75ac: movz            x4, #0
    // 0xba75b0: b               #0xba75c4
    // 0xba75b4: r5 = LoadInt32Instr(r4)
    //     0xba75b4: sbfx            x5, x4, #1, #0x1f
    //     0xba75b8: tbz             w4, #0, #0xba75c0
    //     0xba75bc: ldur            x5, [x4, #7]
    // 0xba75c0: mov             x4, x5
    // 0xba75c4: stur            x4, [fp, #-0x20]
    // 0xba75c8: LoadField: r5 = r2->field_23
    //     0xba75c8: ldur            w5, [x2, #0x23]
    // 0xba75cc: DecompressPointer r5
    //     0xba75cc: add             x5, x5, HEAP, lsl #32
    // 0xba75d0: LoadField: r2 = r5->field_13
    //     0xba75d0: ldur            w2, [x5, #0x13]
    // 0xba75d4: DecompressPointer r2
    //     0xba75d4: add             x2, x2, HEAP, lsl #32
    // 0xba75d8: cmp             w2, NULL
    // 0xba75dc: b.ne            #0xba75e8
    // 0xba75e0: r2 = Null
    //     0xba75e0: mov             x2, NULL
    // 0xba75e4: b               #0xba75f4
    // 0xba75e8: LoadField: r5 = r2->field_f
    //     0xba75e8: ldur            w5, [x2, #0xf]
    // 0xba75ec: DecompressPointer r5
    //     0xba75ec: add             x5, x5, HEAP, lsl #32
    // 0xba75f0: mov             x2, x5
    // 0xba75f4: cmp             w2, NULL
    // 0xba75f8: b.ne            #0xba7604
    // 0xba75fc: r6 = 0
    //     0xba75fc: movz            x6, #0
    // 0xba7600: b               #0xba7614
    // 0xba7604: r5 = LoadInt32Instr(r2)
    //     0xba7604: sbfx            x5, x2, #1, #0x1f
    //     0xba7608: tbz             w2, #0, #0xba7610
    //     0xba760c: ldur            x5, [x2, #7]
    // 0xba7610: mov             x6, x5
    // 0xba7614: ldur            x2, [fp, #-8]
    // 0xba7618: ldur            x5, [fp, #-0x10]
    // 0xba761c: stur            x6, [fp, #-0x18]
    // 0xba7620: r0 = Color()
    //     0xba7620: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xba7624: mov             x3, x0
    // 0xba7628: r0 = Instance_ColorSpace
    //     0xba7628: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xba762c: stur            x3, [fp, #-0x30]
    // 0xba7630: StoreField: r3->field_27 = r0
    //     0xba7630: stur            w0, [x3, #0x27]
    // 0xba7634: d0 = 0.700000
    //     0xba7634: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba7638: ldr             d0, [x17, #0xf48]
    // 0xba763c: StoreField: r3->field_7 = d0
    //     0xba763c: stur            d0, [x3, #7]
    // 0xba7640: ldur            x0, [fp, #-0x28]
    // 0xba7644: ubfx            x0, x0, #0, #0x20
    // 0xba7648: and             w1, w0, #0xff
    // 0xba764c: ubfx            x1, x1, #0, #0x20
    // 0xba7650: scvtf           d0, x1
    // 0xba7654: d1 = 255.000000
    //     0xba7654: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xba7658: fdiv            d2, d0, d1
    // 0xba765c: StoreField: r3->field_f = d2
    //     0xba765c: stur            d2, [x3, #0xf]
    // 0xba7660: ldur            x0, [fp, #-0x20]
    // 0xba7664: ubfx            x0, x0, #0, #0x20
    // 0xba7668: and             w1, w0, #0xff
    // 0xba766c: ubfx            x1, x1, #0, #0x20
    // 0xba7670: scvtf           d0, x1
    // 0xba7674: fdiv            d2, d0, d1
    // 0xba7678: ArrayStore: r3[0] = d2  ; List_8
    //     0xba7678: stur            d2, [x3, #0x17]
    // 0xba767c: ldur            x0, [fp, #-0x18]
    // 0xba7680: ubfx            x0, x0, #0, #0x20
    // 0xba7684: and             w1, w0, #0xff
    // 0xba7688: ubfx            x1, x1, #0, #0x20
    // 0xba768c: scvtf           d0, x1
    // 0xba7690: fdiv            d2, d0, d1
    // 0xba7694: StoreField: r3->field_1f = d2
    //     0xba7694: stur            d2, [x3, #0x1f]
    // 0xba7698: r1 = Null
    //     0xba7698: mov             x1, NULL
    // 0xba769c: r2 = 4
    //     0xba769c: movz            x2, #0x4
    // 0xba76a0: r0 = AllocateArray()
    //     0xba76a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba76a4: mov             x2, x0
    // 0xba76a8: ldur            x0, [fp, #-0x38]
    // 0xba76ac: stur            x2, [fp, #-0x40]
    // 0xba76b0: StoreField: r2->field_f = r0
    //     0xba76b0: stur            w0, [x2, #0xf]
    // 0xba76b4: ldur            x0, [fp, #-0x30]
    // 0xba76b8: StoreField: r2->field_13 = r0
    //     0xba76b8: stur            w0, [x2, #0x13]
    // 0xba76bc: r1 = <Color>
    //     0xba76bc: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xba76c0: ldr             x1, [x1, #0xf80]
    // 0xba76c4: r0 = AllocateGrowableArray()
    //     0xba76c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba76c8: mov             x1, x0
    // 0xba76cc: ldur            x0, [fp, #-0x40]
    // 0xba76d0: stur            x1, [fp, #-0x30]
    // 0xba76d4: StoreField: r1->field_f = r0
    //     0xba76d4: stur            w0, [x1, #0xf]
    // 0xba76d8: r2 = 4
    //     0xba76d8: movz            x2, #0x4
    // 0xba76dc: StoreField: r1->field_b = r2
    //     0xba76dc: stur            w2, [x1, #0xb]
    // 0xba76e0: r0 = LinearGradient()
    //     0xba76e0: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xba76e4: mov             x1, x0
    // 0xba76e8: r0 = Instance_Alignment
    //     0xba76e8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xba76ec: ldr             x0, [x0, #0xce0]
    // 0xba76f0: stur            x1, [fp, #-0x38]
    // 0xba76f4: StoreField: r1->field_13 = r0
    //     0xba76f4: stur            w0, [x1, #0x13]
    // 0xba76f8: r0 = Instance_Alignment
    //     0xba76f8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xba76fc: ldr             x0, [x0, #0xce8]
    // 0xba7700: ArrayStore: r1[0] = r0  ; List_4
    //     0xba7700: stur            w0, [x1, #0x17]
    // 0xba7704: r0 = Instance_TileMode
    //     0xba7704: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xba7708: ldr             x0, [x0, #0xcf0]
    // 0xba770c: StoreField: r1->field_1b = r0
    //     0xba770c: stur            w0, [x1, #0x1b]
    // 0xba7710: ldur            x0, [fp, #-0x30]
    // 0xba7714: StoreField: r1->field_7 = r0
    //     0xba7714: stur            w0, [x1, #7]
    // 0xba7718: r0 = BoxDecoration()
    //     0xba7718: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xba771c: mov             x2, x0
    // 0xba7720: ldur            x0, [fp, #-0x38]
    // 0xba7724: stur            x2, [fp, #-0x30]
    // 0xba7728: StoreField: r2->field_1b = r0
    //     0xba7728: stur            w0, [x2, #0x1b]
    // 0xba772c: r0 = Instance_BoxShape
    //     0xba772c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba7730: ldr             x0, [x0, #0x80]
    // 0xba7734: StoreField: r2->field_23 = r0
    //     0xba7734: stur            w0, [x2, #0x23]
    // 0xba7738: ldur            x3, [fp, #-0x10]
    // 0xba773c: LoadField: r1 = r3->field_f
    //     0xba773c: ldur            w1, [x3, #0xf]
    // 0xba7740: DecompressPointer r1
    //     0xba7740: add             x1, x1, HEAP, lsl #32
    // 0xba7744: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xba7744: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xba7748: r0 = _of()
    //     0xba7748: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xba774c: LoadField: r1 = r0->field_7
    //     0xba774c: ldur            w1, [x0, #7]
    // 0xba7750: DecompressPointer r1
    //     0xba7750: add             x1, x1, HEAP, lsl #32
    // 0xba7754: LoadField: d0 = r1->field_7
    //     0xba7754: ldur            d0, [x1, #7]
    // 0xba7758: d2 = 0.500000
    //     0xba7758: fmov            d2, #0.50000000
    // 0xba775c: fmul            d1, d0, d2
    // 0xba7760: ldur            x0, [fp, #-8]
    // 0xba7764: stur            d1, [fp, #-0x68]
    // 0xba7768: LoadField: r1 = r0->field_f
    //     0xba7768: ldur            w1, [x0, #0xf]
    // 0xba776c: DecompressPointer r1
    //     0xba776c: add             x1, x1, HEAP, lsl #32
    // 0xba7770: LoadField: r3 = r1->field_b
    //     0xba7770: ldur            w3, [x1, #0xb]
    // 0xba7774: DecompressPointer r3
    //     0xba7774: add             x3, x3, HEAP, lsl #32
    // 0xba7778: stur            x3, [fp, #-0x38]
    // 0xba777c: cmp             w3, NULL
    // 0xba7780: b.eq            #0xba92b0
    // 0xba7784: LoadField: r1 = r3->field_1f
    //     0xba7784: ldur            w1, [x3, #0x1f]
    // 0xba7788: DecompressPointer r1
    //     0xba7788: add             x1, x1, HEAP, lsl #32
    // 0xba778c: tbz             w1, #4, #0xba77fc
    // 0xba7790: r1 = Null
    //     0xba7790: mov             x1, NULL
    // 0xba7794: r2 = 6
    //     0xba7794: movz            x2, #0x6
    // 0xba7798: r0 = AllocateArray()
    //     0xba7798: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba779c: r16 = "Save upto "
    //     0xba779c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca0] "Save upto "
    //     0xba77a0: ldr             x16, [x16, #0xca0]
    // 0xba77a4: StoreField: r0->field_f = r16
    //     0xba77a4: stur            w16, [x0, #0xf]
    // 0xba77a8: ldur            x2, [fp, #-0x38]
    // 0xba77ac: LoadField: r1 = r2->field_27
    //     0xba77ac: ldur            w1, [x2, #0x27]
    // 0xba77b0: DecompressPointer r1
    //     0xba77b0: add             x1, x1, HEAP, lsl #32
    // 0xba77b4: cmp             w1, NULL
    // 0xba77b8: b.ne            #0xba77c4
    // 0xba77bc: r1 = Null
    //     0xba77bc: mov             x1, NULL
    // 0xba77c0: b               #0xba77d0
    // 0xba77c4: LoadField: r2 = r1->field_f
    //     0xba77c4: ldur            w2, [x1, #0xf]
    // 0xba77c8: DecompressPointer r2
    //     0xba77c8: add             x2, x2, HEAP, lsl #32
    // 0xba77cc: mov             x1, x2
    // 0xba77d0: cmp             w1, NULL
    // 0xba77d4: b.ne            #0xba77dc
    // 0xba77d8: r1 = ""
    //     0xba77d8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba77dc: StoreField: r0->field_13 = r1
    //     0xba77dc: stur            w1, [x0, #0x13]
    // 0xba77e0: r16 = "!"
    //     0xba77e0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xba77e4: ldr             x16, [x16, #0xd00]
    // 0xba77e8: ArrayStore: r0[0] = r16  ; List_4
    //     0xba77e8: stur            w16, [x0, #0x17]
    // 0xba77ec: str             x0, [SP]
    // 0xba77f0: r0 = _interpolate()
    //     0xba77f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba77f4: mov             x3, x0
    // 0xba77f8: b               #0xba79ac
    // 0xba77fc: mov             x2, x3
    // 0xba7800: LoadField: r0 = r2->field_f
    //     0xba7800: ldur            w0, [x2, #0xf]
    // 0xba7804: DecompressPointer r0
    //     0xba7804: add             x0, x0, HEAP, lsl #32
    // 0xba7808: cmp             w0, NULL
    // 0xba780c: b.ne            #0xba781c
    // 0xba7810: ldur            x3, [fp, #-0x10]
    // 0xba7814: r0 = Null
    //     0xba7814: mov             x0, NULL
    // 0xba7818: b               #0xba786c
    // 0xba781c: ldur            x3, [fp, #-0x10]
    // 0xba7820: LoadField: r4 = r0->field_f
    //     0xba7820: ldur            w4, [x0, #0xf]
    // 0xba7824: DecompressPointer r4
    //     0xba7824: add             x4, x4, HEAP, lsl #32
    // 0xba7828: LoadField: r0 = r3->field_13
    //     0xba7828: ldur            w0, [x3, #0x13]
    // 0xba782c: DecompressPointer r0
    //     0xba782c: add             x0, x0, HEAP, lsl #32
    // 0xba7830: LoadField: r1 = r4->field_b
    //     0xba7830: ldur            w1, [x4, #0xb]
    // 0xba7834: r5 = LoadInt32Instr(r0)
    //     0xba7834: sbfx            x5, x0, #1, #0x1f
    //     0xba7838: tbz             w0, #0, #0xba7840
    //     0xba783c: ldur            x5, [x0, #7]
    // 0xba7840: r0 = LoadInt32Instr(r1)
    //     0xba7840: sbfx            x0, x1, #1, #0x1f
    // 0xba7844: mov             x1, x5
    // 0xba7848: cmp             x1, x0
    // 0xba784c: b.hs            #0xba92b4
    // 0xba7850: LoadField: r0 = r4->field_f
    //     0xba7850: ldur            w0, [x4, #0xf]
    // 0xba7854: DecompressPointer r0
    //     0xba7854: add             x0, x0, HEAP, lsl #32
    // 0xba7858: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba7858: add             x16, x0, x5, lsl #2
    //     0xba785c: ldur            w1, [x16, #0xf]
    // 0xba7860: DecompressPointer r1
    //     0xba7860: add             x1, x1, HEAP, lsl #32
    // 0xba7864: LoadField: r0 = r1->field_7
    //     0xba7864: ldur            w0, [x1, #7]
    // 0xba7868: DecompressPointer r0
    //     0xba7868: add             x0, x0, HEAP, lsl #32
    // 0xba786c: LoadField: r1 = r2->field_13
    //     0xba786c: ldur            w1, [x2, #0x13]
    // 0xba7870: DecompressPointer r1
    //     0xba7870: add             x1, x1, HEAP, lsl #32
    // 0xba7874: r2 = LoadClassIdInstr(r0)
    //     0xba7874: ldur            x2, [x0, #-1]
    //     0xba7878: ubfx            x2, x2, #0xc, #0x14
    // 0xba787c: stp             x1, x0, [SP]
    // 0xba7880: mov             x0, x2
    // 0xba7884: mov             lr, x0
    // 0xba7888: ldr             lr, [x21, lr, lsl #3]
    // 0xba788c: blr             lr
    // 0xba7890: tbnz            w0, #4, #0xba7918
    // 0xba7894: ldur            x0, [fp, #-8]
    // 0xba7898: r1 = Null
    //     0xba7898: mov             x1, NULL
    // 0xba789c: r2 = 6
    //     0xba789c: movz            x2, #0x6
    // 0xba78a0: r0 = AllocateArray()
    //     0xba78a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba78a4: r16 = "You\'ve Saved "
    //     0xba78a4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a5c8] "You\'ve Saved "
    //     0xba78a8: ldr             x16, [x16, #0x5c8]
    // 0xba78ac: StoreField: r0->field_f = r16
    //     0xba78ac: stur            w16, [x0, #0xf]
    // 0xba78b0: ldur            x1, [fp, #-8]
    // 0xba78b4: LoadField: r2 = r1->field_f
    //     0xba78b4: ldur            w2, [x1, #0xf]
    // 0xba78b8: DecompressPointer r2
    //     0xba78b8: add             x2, x2, HEAP, lsl #32
    // 0xba78bc: LoadField: r3 = r2->field_b
    //     0xba78bc: ldur            w3, [x2, #0xb]
    // 0xba78c0: DecompressPointer r3
    //     0xba78c0: add             x3, x3, HEAP, lsl #32
    // 0xba78c4: cmp             w3, NULL
    // 0xba78c8: b.eq            #0xba92b8
    // 0xba78cc: LoadField: r2 = r3->field_27
    //     0xba78cc: ldur            w2, [x3, #0x27]
    // 0xba78d0: DecompressPointer r2
    //     0xba78d0: add             x2, x2, HEAP, lsl #32
    // 0xba78d4: cmp             w2, NULL
    // 0xba78d8: b.ne            #0xba78e4
    // 0xba78dc: r2 = Null
    //     0xba78dc: mov             x2, NULL
    // 0xba78e0: b               #0xba78f0
    // 0xba78e4: LoadField: r3 = r2->field_f
    //     0xba78e4: ldur            w3, [x2, #0xf]
    // 0xba78e8: DecompressPointer r3
    //     0xba78e8: add             x3, x3, HEAP, lsl #32
    // 0xba78ec: mov             x2, x3
    // 0xba78f0: cmp             w2, NULL
    // 0xba78f4: b.ne            #0xba78fc
    // 0xba78f8: r2 = ""
    //     0xba78f8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba78fc: StoreField: r0->field_13 = r2
    //     0xba78fc: stur            w2, [x0, #0x13]
    // 0xba7900: r16 = "!"
    //     0xba7900: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xba7904: ldr             x16, [x16, #0xd00]
    // 0xba7908: ArrayStore: r0[0] = r16  ; List_4
    //     0xba7908: stur            w16, [x0, #0x17]
    // 0xba790c: str             x0, [SP]
    // 0xba7910: r0 = _interpolate()
    //     0xba7910: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba7914: b               #0xba79a8
    // 0xba7918: ldur            x2, [fp, #-8]
    // 0xba791c: LoadField: r0 = r2->field_f
    //     0xba791c: ldur            w0, [x2, #0xf]
    // 0xba7920: DecompressPointer r0
    //     0xba7920: add             x0, x0, HEAP, lsl #32
    // 0xba7924: LoadField: r1 = r0->field_b
    //     0xba7924: ldur            w1, [x0, #0xb]
    // 0xba7928: DecompressPointer r1
    //     0xba7928: add             x1, x1, HEAP, lsl #32
    // 0xba792c: cmp             w1, NULL
    // 0xba7930: b.eq            #0xba92bc
    // 0xba7934: LoadField: r0 = r1->field_f
    //     0xba7934: ldur            w0, [x1, #0xf]
    // 0xba7938: DecompressPointer r0
    //     0xba7938: add             x0, x0, HEAP, lsl #32
    // 0xba793c: cmp             w0, NULL
    // 0xba7940: b.ne            #0xba7950
    // 0xba7944: ldur            x3, [fp, #-0x10]
    // 0xba7948: r0 = Null
    //     0xba7948: mov             x0, NULL
    // 0xba794c: b               #0xba79a0
    // 0xba7950: ldur            x3, [fp, #-0x10]
    // 0xba7954: LoadField: r4 = r0->field_f
    //     0xba7954: ldur            w4, [x0, #0xf]
    // 0xba7958: DecompressPointer r4
    //     0xba7958: add             x4, x4, HEAP, lsl #32
    // 0xba795c: LoadField: r0 = r3->field_13
    //     0xba795c: ldur            w0, [x3, #0x13]
    // 0xba7960: DecompressPointer r0
    //     0xba7960: add             x0, x0, HEAP, lsl #32
    // 0xba7964: LoadField: r1 = r4->field_b
    //     0xba7964: ldur            w1, [x4, #0xb]
    // 0xba7968: r5 = LoadInt32Instr(r0)
    //     0xba7968: sbfx            x5, x0, #1, #0x1f
    //     0xba796c: tbz             w0, #0, #0xba7974
    //     0xba7970: ldur            x5, [x0, #7]
    // 0xba7974: r0 = LoadInt32Instr(r1)
    //     0xba7974: sbfx            x0, x1, #1, #0x1f
    // 0xba7978: mov             x1, x5
    // 0xba797c: cmp             x1, x0
    // 0xba7980: b.hs            #0xba92c0
    // 0xba7984: LoadField: r0 = r4->field_f
    //     0xba7984: ldur            w0, [x4, #0xf]
    // 0xba7988: DecompressPointer r0
    //     0xba7988: add             x0, x0, HEAP, lsl #32
    // 0xba798c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba798c: add             x16, x0, x5, lsl #2
    //     0xba7990: ldur            w1, [x16, #0xf]
    // 0xba7994: DecompressPointer r1
    //     0xba7994: add             x1, x1, HEAP, lsl #32
    // 0xba7998: LoadField: r0 = r1->field_13
    //     0xba7998: ldur            w0, [x1, #0x13]
    // 0xba799c: DecompressPointer r0
    //     0xba799c: add             x0, x0, HEAP, lsl #32
    // 0xba79a0: str             x0, [SP]
    // 0xba79a4: r0 = _interpolateSingle()
    //     0xba79a4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xba79a8: mov             x3, x0
    // 0xba79ac: ldur            x0, [fp, #-8]
    // 0xba79b0: ldur            x2, [fp, #-0x10]
    // 0xba79b4: ldur            d0, [fp, #-0x68]
    // 0xba79b8: stur            x3, [fp, #-0x38]
    // 0xba79bc: LoadField: r1 = r2->field_f
    //     0xba79bc: ldur            w1, [x2, #0xf]
    // 0xba79c0: DecompressPointer r1
    //     0xba79c0: add             x1, x1, HEAP, lsl #32
    // 0xba79c4: r0 = of()
    //     0xba79c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba79c8: LoadField: r1 = r0->field_87
    //     0xba79c8: ldur            w1, [x0, #0x87]
    // 0xba79cc: DecompressPointer r1
    //     0xba79cc: add             x1, x1, HEAP, lsl #32
    // 0xba79d0: LoadField: r0 = r1->field_2b
    //     0xba79d0: ldur            w0, [x1, #0x2b]
    // 0xba79d4: DecompressPointer r0
    //     0xba79d4: add             x0, x0, HEAP, lsl #32
    // 0xba79d8: r16 = 12.000000
    //     0xba79d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba79dc: ldr             x16, [x16, #0x9e8]
    // 0xba79e0: r30 = Instance_Color
    //     0xba79e0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba79e4: stp             lr, x16, [SP]
    // 0xba79e8: mov             x1, x0
    // 0xba79ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba79ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba79f0: ldr             x4, [x4, #0xaa0]
    // 0xba79f4: r0 = copyWith()
    //     0xba79f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba79f8: stur            x0, [fp, #-0x40]
    // 0xba79fc: r0 = Text()
    //     0xba79fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba7a00: mov             x1, x0
    // 0xba7a04: ldur            x0, [fp, #-0x38]
    // 0xba7a08: stur            x1, [fp, #-0x48]
    // 0xba7a0c: StoreField: r1->field_b = r0
    //     0xba7a0c: stur            w0, [x1, #0xb]
    // 0xba7a10: ldur            x0, [fp, #-0x40]
    // 0xba7a14: StoreField: r1->field_13 = r0
    //     0xba7a14: stur            w0, [x1, #0x13]
    // 0xba7a18: r2 = 4
    //     0xba7a18: movz            x2, #0x4
    // 0xba7a1c: StoreField: r1->field_37 = r2
    //     0xba7a1c: stur            w2, [x1, #0x37]
    // 0xba7a20: r0 = Padding()
    //     0xba7a20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba7a24: mov             x1, x0
    // 0xba7a28: r0 = Instance_EdgeInsets
    //     0xba7a28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba7a2c: ldr             x0, [x0, #0x980]
    // 0xba7a30: stur            x1, [fp, #-0x40]
    // 0xba7a34: StoreField: r1->field_f = r0
    //     0xba7a34: stur            w0, [x1, #0xf]
    // 0xba7a38: ldur            x2, [fp, #-0x48]
    // 0xba7a3c: StoreField: r1->field_b = r2
    //     0xba7a3c: stur            w2, [x1, #0xb]
    // 0xba7a40: ldur            d0, [fp, #-0x68]
    // 0xba7a44: r2 = inline_Allocate_Double()
    //     0xba7a44: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xba7a48: add             x2, x2, #0x10
    //     0xba7a4c: cmp             x3, x2
    //     0xba7a50: b.ls            #0xba92c4
    //     0xba7a54: str             x2, [THR, #0x50]  ; THR::top
    //     0xba7a58: sub             x2, x2, #0xf
    //     0xba7a5c: movz            x3, #0xe15c
    //     0xba7a60: movk            x3, #0x3, lsl #16
    //     0xba7a64: stur            x3, [x2, #-1]
    // 0xba7a68: StoreField: r2->field_7 = d0
    //     0xba7a68: stur            d0, [x2, #7]
    // 0xba7a6c: stur            x2, [fp, #-0x38]
    // 0xba7a70: r0 = SizedBox()
    //     0xba7a70: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba7a74: mov             x2, x0
    // 0xba7a78: ldur            x0, [fp, #-0x38]
    // 0xba7a7c: stur            x2, [fp, #-0x48]
    // 0xba7a80: StoreField: r2->field_f = r0
    //     0xba7a80: stur            w0, [x2, #0xf]
    // 0xba7a84: ldur            x0, [fp, #-0x40]
    // 0xba7a88: StoreField: r2->field_b = r0
    //     0xba7a88: stur            w0, [x2, #0xb]
    // 0xba7a8c: ldur            x3, [fp, #-8]
    // 0xba7a90: LoadField: r0 = r3->field_f
    //     0xba7a90: ldur            w0, [x3, #0xf]
    // 0xba7a94: DecompressPointer r0
    //     0xba7a94: add             x0, x0, HEAP, lsl #32
    // 0xba7a98: LoadField: r4 = r0->field_b
    //     0xba7a98: ldur            w4, [x0, #0xb]
    // 0xba7a9c: DecompressPointer r4
    //     0xba7a9c: add             x4, x4, HEAP, lsl #32
    // 0xba7aa0: cmp             w4, NULL
    // 0xba7aa4: b.eq            #0xba92e0
    // 0xba7aa8: LoadField: r0 = r4->field_f
    //     0xba7aa8: ldur            w0, [x4, #0xf]
    // 0xba7aac: DecompressPointer r0
    //     0xba7aac: add             x0, x0, HEAP, lsl #32
    // 0xba7ab0: cmp             w0, NULL
    // 0xba7ab4: b.ne            #0xba7ac4
    // 0xba7ab8: ldur            x5, [fp, #-0x10]
    // 0xba7abc: r0 = Null
    //     0xba7abc: mov             x0, NULL
    // 0xba7ac0: b               #0xba7b14
    // 0xba7ac4: ldur            x5, [fp, #-0x10]
    // 0xba7ac8: LoadField: r6 = r0->field_f
    //     0xba7ac8: ldur            w6, [x0, #0xf]
    // 0xba7acc: DecompressPointer r6
    //     0xba7acc: add             x6, x6, HEAP, lsl #32
    // 0xba7ad0: LoadField: r0 = r5->field_13
    //     0xba7ad0: ldur            w0, [x5, #0x13]
    // 0xba7ad4: DecompressPointer r0
    //     0xba7ad4: add             x0, x0, HEAP, lsl #32
    // 0xba7ad8: LoadField: r1 = r6->field_b
    //     0xba7ad8: ldur            w1, [x6, #0xb]
    // 0xba7adc: r7 = LoadInt32Instr(r0)
    //     0xba7adc: sbfx            x7, x0, #1, #0x1f
    //     0xba7ae0: tbz             w0, #0, #0xba7ae8
    //     0xba7ae4: ldur            x7, [x0, #7]
    // 0xba7ae8: r0 = LoadInt32Instr(r1)
    //     0xba7ae8: sbfx            x0, x1, #1, #0x1f
    // 0xba7aec: mov             x1, x7
    // 0xba7af0: cmp             x1, x0
    // 0xba7af4: b.hs            #0xba92e4
    // 0xba7af8: LoadField: r0 = r6->field_f
    //     0xba7af8: ldur            w0, [x6, #0xf]
    // 0xba7afc: DecompressPointer r0
    //     0xba7afc: add             x0, x0, HEAP, lsl #32
    // 0xba7b00: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xba7b00: add             x16, x0, x7, lsl #2
    //     0xba7b04: ldur            w1, [x16, #0xf]
    // 0xba7b08: DecompressPointer r1
    //     0xba7b08: add             x1, x1, HEAP, lsl #32
    // 0xba7b0c: LoadField: r0 = r1->field_7
    //     0xba7b0c: ldur            w0, [x1, #7]
    // 0xba7b10: DecompressPointer r0
    //     0xba7b10: add             x0, x0, HEAP, lsl #32
    // 0xba7b14: LoadField: r1 = r4->field_13
    //     0xba7b14: ldur            w1, [x4, #0x13]
    // 0xba7b18: DecompressPointer r1
    //     0xba7b18: add             x1, x1, HEAP, lsl #32
    // 0xba7b1c: r4 = LoadClassIdInstr(r0)
    //     0xba7b1c: ldur            x4, [x0, #-1]
    //     0xba7b20: ubfx            x4, x4, #0xc, #0x14
    // 0xba7b24: stp             x1, x0, [SP]
    // 0xba7b28: mov             x0, x4
    // 0xba7b2c: mov             lr, x0
    // 0xba7b30: ldr             lr, [x21, lr, lsl #3]
    // 0xba7b34: blr             lr
    // 0xba7b38: tbnz            w0, #4, #0xba7b44
    // 0xba7b3c: ldur            x0, [fp, #-8]
    // 0xba7b40: b               #0xba7b6c
    // 0xba7b44: ldur            x0, [fp, #-8]
    // 0xba7b48: LoadField: r1 = r0->field_f
    //     0xba7b48: ldur            w1, [x0, #0xf]
    // 0xba7b4c: DecompressPointer r1
    //     0xba7b4c: add             x1, x1, HEAP, lsl #32
    // 0xba7b50: LoadField: r2 = r1->field_b
    //     0xba7b50: ldur            w2, [x1, #0xb]
    // 0xba7b54: DecompressPointer r2
    //     0xba7b54: add             x2, x2, HEAP, lsl #32
    // 0xba7b58: cmp             w2, NULL
    // 0xba7b5c: b.eq            #0xba92e8
    // 0xba7b60: LoadField: r1 = r2->field_1f
    //     0xba7b60: ldur            w1, [x2, #0x1f]
    // 0xba7b64: DecompressPointer r1
    //     0xba7b64: add             x1, x1, HEAP, lsl #32
    // 0xba7b68: tbz             w1, #4, #0xba7bdc
    // 0xba7b6c: ldur            x2, [fp, #-0x10]
    // 0xba7b70: LoadField: r1 = r2->field_f
    //     0xba7b70: ldur            w1, [x2, #0xf]
    // 0xba7b74: DecompressPointer r1
    //     0xba7b74: add             x1, x1, HEAP, lsl #32
    // 0xba7b78: r0 = of()
    //     0xba7b78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba7b7c: LoadField: r1 = r0->field_87
    //     0xba7b7c: ldur            w1, [x0, #0x87]
    // 0xba7b80: DecompressPointer r1
    //     0xba7b80: add             x1, x1, HEAP, lsl #32
    // 0xba7b84: LoadField: r0 = r1->field_2b
    //     0xba7b84: ldur            w0, [x1, #0x2b]
    // 0xba7b88: DecompressPointer r0
    //     0xba7b88: add             x0, x0, HEAP, lsl #32
    // 0xba7b8c: r16 = Instance_Color
    //     0xba7b8c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba7b90: r30 = 12.000000
    //     0xba7b90: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba7b94: ldr             lr, [lr, #0x9e8]
    // 0xba7b98: stp             lr, x16, [SP]
    // 0xba7b9c: mov             x1, x0
    // 0xba7ba0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba7ba0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba7ba4: ldr             x4, [x4, #0x9b8]
    // 0xba7ba8: r0 = copyWith()
    //     0xba7ba8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba7bac: stur            x0, [fp, #-0x38]
    // 0xba7bb0: r0 = Text()
    //     0xba7bb0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba7bb4: mov             x1, x0
    // 0xba7bb8: r0 = "Bumper Offer Applied!"
    //     0xba7bb8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d08] "Bumper Offer Applied!"
    //     0xba7bbc: ldr             x0, [x0, #0xd08]
    // 0xba7bc0: StoreField: r1->field_b = r0
    //     0xba7bc0: stur            w0, [x1, #0xb]
    // 0xba7bc4: ldur            x0, [fp, #-0x38]
    // 0xba7bc8: StoreField: r1->field_13 = r0
    //     0xba7bc8: stur            w0, [x1, #0x13]
    // 0xba7bcc: r2 = 4
    //     0xba7bcc: movz            x2, #0x4
    // 0xba7bd0: StoreField: r1->field_37 = r2
    //     0xba7bd0: stur            w2, [x1, #0x37]
    // 0xba7bd4: mov             x2, x1
    // 0xba7bd8: b               #0xba7d34
    // 0xba7bdc: ldur            x0, [fp, #-0x10]
    // 0xba7be0: r2 = 4
    //     0xba7be0: movz            x2, #0x4
    // 0xba7be4: LoadField: r1 = r0->field_f
    //     0xba7be4: ldur            w1, [x0, #0xf]
    // 0xba7be8: DecompressPointer r1
    //     0xba7be8: add             x1, x1, HEAP, lsl #32
    // 0xba7bec: r0 = of()
    //     0xba7bec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba7bf0: LoadField: r1 = r0->field_87
    //     0xba7bf0: ldur            w1, [x0, #0x87]
    // 0xba7bf4: DecompressPointer r1
    //     0xba7bf4: add             x1, x1, HEAP, lsl #32
    // 0xba7bf8: LoadField: r0 = r1->field_2b
    //     0xba7bf8: ldur            w0, [x1, #0x2b]
    // 0xba7bfc: DecompressPointer r0
    //     0xba7bfc: add             x0, x0, HEAP, lsl #32
    // 0xba7c00: r16 = Instance_Color
    //     0xba7c00: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba7c04: r30 = 12.000000
    //     0xba7c04: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba7c08: ldr             lr, [lr, #0x9e8]
    // 0xba7c0c: stp             lr, x16, [SP]
    // 0xba7c10: mov             x1, x0
    // 0xba7c14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba7c14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba7c18: ldr             x4, [x4, #0x9b8]
    // 0xba7c1c: r0 = copyWith()
    //     0xba7c1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba7c20: stur            x0, [fp, #-0x38]
    // 0xba7c24: r0 = TextSpan()
    //     0xba7c24: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba7c28: mov             x2, x0
    // 0xba7c2c: r0 = "Know "
    //     0xba7c2c: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xba7c30: ldr             x0, [x0, #0x5d0]
    // 0xba7c34: stur            x2, [fp, #-0x40]
    // 0xba7c38: StoreField: r2->field_b = r0
    //     0xba7c38: stur            w0, [x2, #0xb]
    // 0xba7c3c: r0 = Instance__DeferringMouseCursor
    //     0xba7c3c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba7c40: ArrayStore: r2[0] = r0  ; List_4
    //     0xba7c40: stur            w0, [x2, #0x17]
    // 0xba7c44: ldur            x1, [fp, #-0x38]
    // 0xba7c48: StoreField: r2->field_7 = r1
    //     0xba7c48: stur            w1, [x2, #7]
    // 0xba7c4c: ldur            x3, [fp, #-0x10]
    // 0xba7c50: LoadField: r1 = r3->field_f
    //     0xba7c50: ldur            w1, [x3, #0xf]
    // 0xba7c54: DecompressPointer r1
    //     0xba7c54: add             x1, x1, HEAP, lsl #32
    // 0xba7c58: r0 = of()
    //     0xba7c58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba7c5c: LoadField: r1 = r0->field_87
    //     0xba7c5c: ldur            w1, [x0, #0x87]
    // 0xba7c60: DecompressPointer r1
    //     0xba7c60: add             x1, x1, HEAP, lsl #32
    // 0xba7c64: LoadField: r0 = r1->field_2b
    //     0xba7c64: ldur            w0, [x1, #0x2b]
    // 0xba7c68: DecompressPointer r0
    //     0xba7c68: add             x0, x0, HEAP, lsl #32
    // 0xba7c6c: r16 = Instance_Color
    //     0xba7c6c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba7c70: r30 = 12.000000
    //     0xba7c70: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba7c74: ldr             lr, [lr, #0x9e8]
    // 0xba7c78: stp             lr, x16, [SP]
    // 0xba7c7c: mov             x1, x0
    // 0xba7c80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba7c80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba7c84: ldr             x4, [x4, #0x9b8]
    // 0xba7c88: r0 = copyWith()
    //     0xba7c88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba7c8c: stur            x0, [fp, #-0x38]
    // 0xba7c90: r0 = TextSpan()
    //     0xba7c90: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba7c94: r2 = "More"
    //     0xba7c94: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xba7c98: ldr             x2, [x2, #0x5d8]
    // 0xba7c9c: stur            x0, [fp, #-0x50]
    // 0xba7ca0: StoreField: r0->field_b = r2
    //     0xba7ca0: stur            w2, [x0, #0xb]
    // 0xba7ca4: r3 = Instance__DeferringMouseCursor
    //     0xba7ca4: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba7ca8: ArrayStore: r0[0] = r3  ; List_4
    //     0xba7ca8: stur            w3, [x0, #0x17]
    // 0xba7cac: ldur            x1, [fp, #-0x38]
    // 0xba7cb0: StoreField: r0->field_7 = r1
    //     0xba7cb0: stur            w1, [x0, #7]
    // 0xba7cb4: r1 = Null
    //     0xba7cb4: mov             x1, NULL
    // 0xba7cb8: r2 = 4
    //     0xba7cb8: movz            x2, #0x4
    // 0xba7cbc: r0 = AllocateArray()
    //     0xba7cbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba7cc0: mov             x2, x0
    // 0xba7cc4: ldur            x0, [fp, #-0x40]
    // 0xba7cc8: stur            x2, [fp, #-0x38]
    // 0xba7ccc: StoreField: r2->field_f = r0
    //     0xba7ccc: stur            w0, [x2, #0xf]
    // 0xba7cd0: ldur            x0, [fp, #-0x50]
    // 0xba7cd4: StoreField: r2->field_13 = r0
    //     0xba7cd4: stur            w0, [x2, #0x13]
    // 0xba7cd8: r1 = <TextSpan>
    //     0xba7cd8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xba7cdc: ldr             x1, [x1, #0x940]
    // 0xba7ce0: r0 = AllocateGrowableArray()
    //     0xba7ce0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba7ce4: mov             x1, x0
    // 0xba7ce8: ldur            x0, [fp, #-0x38]
    // 0xba7cec: stur            x1, [fp, #-0x40]
    // 0xba7cf0: StoreField: r1->field_f = r0
    //     0xba7cf0: stur            w0, [x1, #0xf]
    // 0xba7cf4: r2 = 4
    //     0xba7cf4: movz            x2, #0x4
    // 0xba7cf8: StoreField: r1->field_b = r2
    //     0xba7cf8: stur            w2, [x1, #0xb]
    // 0xba7cfc: r0 = TextSpan()
    //     0xba7cfc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba7d00: mov             x1, x0
    // 0xba7d04: ldur            x0, [fp, #-0x40]
    // 0xba7d08: stur            x1, [fp, #-0x38]
    // 0xba7d0c: StoreField: r1->field_f = r0
    //     0xba7d0c: stur            w0, [x1, #0xf]
    // 0xba7d10: r3 = Instance__DeferringMouseCursor
    //     0xba7d10: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba7d14: ArrayStore: r1[0] = r3  ; List_4
    //     0xba7d14: stur            w3, [x1, #0x17]
    // 0xba7d18: r0 = RichText()
    //     0xba7d18: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba7d1c: mov             x1, x0
    // 0xba7d20: ldur            x2, [fp, #-0x38]
    // 0xba7d24: stur            x0, [fp, #-0x38]
    // 0xba7d28: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba7d28: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba7d2c: r0 = RichText()
    //     0xba7d2c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba7d30: ldur            x2, [fp, #-0x38]
    // 0xba7d34: ldur            x0, [fp, #-8]
    // 0xba7d38: ldur            x1, [fp, #-0x48]
    // 0xba7d3c: stur            x2, [fp, #-0x38]
    // 0xba7d40: r0 = Padding()
    //     0xba7d40: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba7d44: r4 = Instance_EdgeInsets
    //     0xba7d44: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xba7d48: ldr             x4, [x4, #0xc40]
    // 0xba7d4c: stur            x0, [fp, #-0x40]
    // 0xba7d50: StoreField: r0->field_f = r4
    //     0xba7d50: stur            w4, [x0, #0xf]
    // 0xba7d54: ldur            x1, [fp, #-0x38]
    // 0xba7d58: StoreField: r0->field_b = r1
    //     0xba7d58: stur            w1, [x0, #0xb]
    // 0xba7d5c: r0 = InkWell()
    //     0xba7d5c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xba7d60: mov             x3, x0
    // 0xba7d64: ldur            x0, [fp, #-0x40]
    // 0xba7d68: stur            x3, [fp, #-0x38]
    // 0xba7d6c: StoreField: r3->field_b = r0
    //     0xba7d6c: stur            w0, [x3, #0xb]
    // 0xba7d70: ldur            x2, [fp, #-0x10]
    // 0xba7d74: r1 = Function '<anonymous closure>':.
    //     0xba7d74: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a5e0] AnonymousClosure: (0xba9d1c), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba7d78: ldr             x1, [x1, #0x5e0]
    // 0xba7d7c: r0 = AllocateClosure()
    //     0xba7d7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba7d80: mov             x1, x0
    // 0xba7d84: ldur            x0, [fp, #-0x38]
    // 0xba7d88: StoreField: r0->field_f = r1
    //     0xba7d88: stur            w1, [x0, #0xf]
    // 0xba7d8c: r2 = true
    //     0xba7d8c: add             x2, NULL, #0x20  ; true
    // 0xba7d90: StoreField: r0->field_43 = r2
    //     0xba7d90: stur            w2, [x0, #0x43]
    // 0xba7d94: r3 = Instance_BoxShape
    //     0xba7d94: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba7d98: ldr             x3, [x3, #0x80]
    // 0xba7d9c: StoreField: r0->field_47 = r3
    //     0xba7d9c: stur            w3, [x0, #0x47]
    // 0xba7da0: StoreField: r0->field_6f = r2
    //     0xba7da0: stur            w2, [x0, #0x6f]
    // 0xba7da4: r4 = false
    //     0xba7da4: add             x4, NULL, #0x30  ; false
    // 0xba7da8: StoreField: r0->field_73 = r4
    //     0xba7da8: stur            w4, [x0, #0x73]
    // 0xba7dac: StoreField: r0->field_83 = r2
    //     0xba7dac: stur            w2, [x0, #0x83]
    // 0xba7db0: StoreField: r0->field_7b = r4
    //     0xba7db0: stur            w4, [x0, #0x7b]
    // 0xba7db4: r1 = <FlexParentData>
    //     0xba7db4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba7db8: ldr             x1, [x1, #0xe00]
    // 0xba7dbc: r0 = Expanded()
    //     0xba7dbc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba7dc0: stur            x0, [fp, #-0x40]
    // 0xba7dc4: StoreField: r0->field_13 = rZR
    //     0xba7dc4: stur            xzr, [x0, #0x13]
    // 0xba7dc8: r3 = Instance_FlexFit
    //     0xba7dc8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba7dcc: ldr             x3, [x3, #0xe08]
    // 0xba7dd0: StoreField: r0->field_1b = r3
    //     0xba7dd0: stur            w3, [x0, #0x1b]
    // 0xba7dd4: ldur            x1, [fp, #-0x38]
    // 0xba7dd8: StoreField: r0->field_b = r1
    //     0xba7dd8: stur            w1, [x0, #0xb]
    // 0xba7ddc: r1 = Null
    //     0xba7ddc: mov             x1, NULL
    // 0xba7de0: r2 = 4
    //     0xba7de0: movz            x2, #0x4
    // 0xba7de4: r0 = AllocateArray()
    //     0xba7de4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba7de8: mov             x2, x0
    // 0xba7dec: ldur            x0, [fp, #-0x48]
    // 0xba7df0: stur            x2, [fp, #-0x38]
    // 0xba7df4: StoreField: r2->field_f = r0
    //     0xba7df4: stur            w0, [x2, #0xf]
    // 0xba7df8: ldur            x0, [fp, #-0x40]
    // 0xba7dfc: StoreField: r2->field_13 = r0
    //     0xba7dfc: stur            w0, [x2, #0x13]
    // 0xba7e00: r1 = <Widget>
    //     0xba7e00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba7e04: r0 = AllocateGrowableArray()
    //     0xba7e04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba7e08: mov             x1, x0
    // 0xba7e0c: ldur            x0, [fp, #-0x38]
    // 0xba7e10: stur            x1, [fp, #-0x40]
    // 0xba7e14: StoreField: r1->field_f = r0
    //     0xba7e14: stur            w0, [x1, #0xf]
    // 0xba7e18: r5 = 4
    //     0xba7e18: movz            x5, #0x4
    // 0xba7e1c: StoreField: r1->field_b = r5
    //     0xba7e1c: stur            w5, [x1, #0xb]
    // 0xba7e20: r0 = Column()
    //     0xba7e20: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba7e24: mov             x2, x0
    // 0xba7e28: r6 = Instance_Axis
    //     0xba7e28: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba7e2c: stur            x2, [fp, #-0x38]
    // 0xba7e30: StoreField: r2->field_f = r6
    //     0xba7e30: stur            w6, [x2, #0xf]
    // 0xba7e34: r3 = Instance_MainAxisAlignment
    //     0xba7e34: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba7e38: ldr             x3, [x3, #0xa08]
    // 0xba7e3c: StoreField: r2->field_13 = r3
    //     0xba7e3c: stur            w3, [x2, #0x13]
    // 0xba7e40: r4 = Instance_MainAxisSize
    //     0xba7e40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba7e44: ldr             x4, [x4, #0xa10]
    // 0xba7e48: ArrayStore: r2[0] = r4  ; List_4
    //     0xba7e48: stur            w4, [x2, #0x17]
    // 0xba7e4c: r7 = Instance_CrossAxisAlignment
    //     0xba7e4c: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba7e50: ldr             x7, [x7, #0x890]
    // 0xba7e54: StoreField: r2->field_1b = r7
    //     0xba7e54: stur            w7, [x2, #0x1b]
    // 0xba7e58: r5 = Instance_VerticalDirection
    //     0xba7e58: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba7e5c: ldr             x5, [x5, #0xa20]
    // 0xba7e60: StoreField: r2->field_23 = r5
    //     0xba7e60: stur            w5, [x2, #0x23]
    // 0xba7e64: r6 = Instance_Clip
    //     0xba7e64: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba7e68: ldr             x6, [x6, #0x38]
    // 0xba7e6c: StoreField: r2->field_2b = r6
    //     0xba7e6c: stur            w6, [x2, #0x2b]
    // 0xba7e70: StoreField: r2->field_2f = rZR
    //     0xba7e70: stur            xzr, [x2, #0x2f]
    // 0xba7e74: ldur            x0, [fp, #-0x40]
    // 0xba7e78: StoreField: r2->field_b = r0
    //     0xba7e78: stur            w0, [x2, #0xb]
    // 0xba7e7c: ldur            x7, [fp, #-8]
    // 0xba7e80: LoadField: r0 = r7->field_f
    //     0xba7e80: ldur            w0, [x7, #0xf]
    // 0xba7e84: DecompressPointer r0
    //     0xba7e84: add             x0, x0, HEAP, lsl #32
    // 0xba7e88: LoadField: r8 = r0->field_b
    //     0xba7e88: ldur            w8, [x0, #0xb]
    // 0xba7e8c: DecompressPointer r8
    //     0xba7e8c: add             x8, x8, HEAP, lsl #32
    // 0xba7e90: cmp             w8, NULL
    // 0xba7e94: b.eq            #0xba92ec
    // 0xba7e98: LoadField: r0 = r8->field_1f
    //     0xba7e98: ldur            w0, [x8, #0x1f]
    // 0xba7e9c: DecompressPointer r0
    //     0xba7e9c: add             x0, x0, HEAP, lsl #32
    // 0xba7ea0: tbz             w0, #4, #0xba7eac
    // 0xba7ea4: mov             x8, x7
    // 0xba7ea8: b               #0xba7f44
    // 0xba7eac: LoadField: r0 = r8->field_f
    //     0xba7eac: ldur            w0, [x8, #0xf]
    // 0xba7eb0: DecompressPointer r0
    //     0xba7eb0: add             x0, x0, HEAP, lsl #32
    // 0xba7eb4: cmp             w0, NULL
    // 0xba7eb8: b.ne            #0xba7ec8
    // 0xba7ebc: ldur            x9, [fp, #-0x10]
    // 0xba7ec0: r0 = Null
    //     0xba7ec0: mov             x0, NULL
    // 0xba7ec4: b               #0xba7f18
    // 0xba7ec8: ldur            x9, [fp, #-0x10]
    // 0xba7ecc: LoadField: r10 = r0->field_f
    //     0xba7ecc: ldur            w10, [x0, #0xf]
    // 0xba7ed0: DecompressPointer r10
    //     0xba7ed0: add             x10, x10, HEAP, lsl #32
    // 0xba7ed4: LoadField: r0 = r9->field_13
    //     0xba7ed4: ldur            w0, [x9, #0x13]
    // 0xba7ed8: DecompressPointer r0
    //     0xba7ed8: add             x0, x0, HEAP, lsl #32
    // 0xba7edc: LoadField: r1 = r10->field_b
    //     0xba7edc: ldur            w1, [x10, #0xb]
    // 0xba7ee0: r11 = LoadInt32Instr(r0)
    //     0xba7ee0: sbfx            x11, x0, #1, #0x1f
    //     0xba7ee4: tbz             w0, #0, #0xba7eec
    //     0xba7ee8: ldur            x11, [x0, #7]
    // 0xba7eec: r0 = LoadInt32Instr(r1)
    //     0xba7eec: sbfx            x0, x1, #1, #0x1f
    // 0xba7ef0: mov             x1, x11
    // 0xba7ef4: cmp             x1, x0
    // 0xba7ef8: b.hs            #0xba92f0
    // 0xba7efc: LoadField: r0 = r10->field_f
    //     0xba7efc: ldur            w0, [x10, #0xf]
    // 0xba7f00: DecompressPointer r0
    //     0xba7f00: add             x0, x0, HEAP, lsl #32
    // 0xba7f04: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xba7f04: add             x16, x0, x11, lsl #2
    //     0xba7f08: ldur            w1, [x16, #0xf]
    // 0xba7f0c: DecompressPointer r1
    //     0xba7f0c: add             x1, x1, HEAP, lsl #32
    // 0xba7f10: LoadField: r0 = r1->field_7
    //     0xba7f10: ldur            w0, [x1, #7]
    // 0xba7f14: DecompressPointer r0
    //     0xba7f14: add             x0, x0, HEAP, lsl #32
    // 0xba7f18: LoadField: r1 = r8->field_13
    //     0xba7f18: ldur            w1, [x8, #0x13]
    // 0xba7f1c: DecompressPointer r1
    //     0xba7f1c: add             x1, x1, HEAP, lsl #32
    // 0xba7f20: r8 = LoadClassIdInstr(r0)
    //     0xba7f20: ldur            x8, [x0, #-1]
    //     0xba7f24: ubfx            x8, x8, #0xc, #0x14
    // 0xba7f28: stp             x1, x0, [SP]
    // 0xba7f2c: mov             x0, x8
    // 0xba7f30: mov             lr, x0
    // 0xba7f34: ldr             lr, [x21, lr, lsl #3]
    // 0xba7f38: blr             lr
    // 0xba7f3c: tbnz            w0, #4, #0xba8054
    // 0xba7f40: ldur            x8, [fp, #-8]
    // 0xba7f44: LoadField: r0 = r8->field_f
    //     0xba7f44: ldur            w0, [x8, #0xf]
    // 0xba7f48: DecompressPointer r0
    //     0xba7f48: add             x0, x0, HEAP, lsl #32
    // 0xba7f4c: LoadField: r1 = r0->field_b
    //     0xba7f4c: ldur            w1, [x0, #0xb]
    // 0xba7f50: DecompressPointer r1
    //     0xba7f50: add             x1, x1, HEAP, lsl #32
    // 0xba7f54: cmp             w1, NULL
    // 0xba7f58: b.eq            #0xba92f4
    // 0xba7f5c: LoadField: r0 = r1->field_f
    //     0xba7f5c: ldur            w0, [x1, #0xf]
    // 0xba7f60: DecompressPointer r0
    //     0xba7f60: add             x0, x0, HEAP, lsl #32
    // 0xba7f64: cmp             w0, NULL
    // 0xba7f68: b.ne            #0xba7f78
    // 0xba7f6c: ldur            x2, [fp, #-0x10]
    // 0xba7f70: r0 = Null
    //     0xba7f70: mov             x0, NULL
    // 0xba7f74: b               #0xba7fc8
    // 0xba7f78: ldur            x2, [fp, #-0x10]
    // 0xba7f7c: LoadField: r3 = r0->field_f
    //     0xba7f7c: ldur            w3, [x0, #0xf]
    // 0xba7f80: DecompressPointer r3
    //     0xba7f80: add             x3, x3, HEAP, lsl #32
    // 0xba7f84: LoadField: r0 = r2->field_13
    //     0xba7f84: ldur            w0, [x2, #0x13]
    // 0xba7f88: DecompressPointer r0
    //     0xba7f88: add             x0, x0, HEAP, lsl #32
    // 0xba7f8c: LoadField: r1 = r3->field_b
    //     0xba7f8c: ldur            w1, [x3, #0xb]
    // 0xba7f90: r4 = LoadInt32Instr(r0)
    //     0xba7f90: sbfx            x4, x0, #1, #0x1f
    //     0xba7f94: tbz             w0, #0, #0xba7f9c
    //     0xba7f98: ldur            x4, [x0, #7]
    // 0xba7f9c: r0 = LoadInt32Instr(r1)
    //     0xba7f9c: sbfx            x0, x1, #1, #0x1f
    // 0xba7fa0: mov             x1, x4
    // 0xba7fa4: cmp             x1, x0
    // 0xba7fa8: b.hs            #0xba92f8
    // 0xba7fac: LoadField: r0 = r3->field_f
    //     0xba7fac: ldur            w0, [x3, #0xf]
    // 0xba7fb0: DecompressPointer r0
    //     0xba7fb0: add             x0, x0, HEAP, lsl #32
    // 0xba7fb4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xba7fb4: add             x16, x0, x4, lsl #2
    //     0xba7fb8: ldur            w1, [x16, #0xf]
    // 0xba7fbc: DecompressPointer r1
    //     0xba7fbc: add             x1, x1, HEAP, lsl #32
    // 0xba7fc0: LoadField: r0 = r1->field_b
    //     0xba7fc0: ldur            w0, [x1, #0xb]
    // 0xba7fc4: DecompressPointer r0
    //     0xba7fc4: add             x0, x0, HEAP, lsl #32
    // 0xba7fc8: r1 = LoadClassIdInstr(r0)
    //     0xba7fc8: ldur            x1, [x0, #-1]
    //     0xba7fcc: ubfx            x1, x1, #0xc, #0x14
    // 0xba7fd0: r16 = "bumper_coupon"
    //     0xba7fd0: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xba7fd4: ldr             x16, [x16, #0x8d8]
    // 0xba7fd8: stp             x16, x0, [SP]
    // 0xba7fdc: mov             x0, x1
    // 0xba7fe0: mov             lr, x0
    // 0xba7fe4: ldr             lr, [x21, lr, lsl #3]
    // 0xba7fe8: blr             lr
    // 0xba7fec: tbnz            w0, #4, #0xba8054
    // 0xba7ff0: ldur            x2, [fp, #-0x10]
    // 0xba7ff4: LoadField: r1 = r2->field_f
    //     0xba7ff4: ldur            w1, [x2, #0xf]
    // 0xba7ff8: DecompressPointer r1
    //     0xba7ff8: add             x1, x1, HEAP, lsl #32
    // 0xba7ffc: r0 = of()
    //     0xba7ffc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba8000: LoadField: r1 = r0->field_87
    //     0xba8000: ldur            w1, [x0, #0x87]
    // 0xba8004: DecompressPointer r1
    //     0xba8004: add             x1, x1, HEAP, lsl #32
    // 0xba8008: LoadField: r0 = r1->field_7
    //     0xba8008: ldur            w0, [x1, #7]
    // 0xba800c: DecompressPointer r0
    //     0xba800c: add             x0, x0, HEAP, lsl #32
    // 0xba8010: r16 = 12.000000
    //     0xba8010: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba8014: ldr             x16, [x16, #0x9e8]
    // 0xba8018: r30 = Instance_Color
    //     0xba8018: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba801c: stp             lr, x16, [SP]
    // 0xba8020: mov             x1, x0
    // 0xba8024: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba8024: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba8028: ldr             x4, [x4, #0xaa0]
    // 0xba802c: r0 = copyWith()
    //     0xba802c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba8030: stur            x0, [fp, #-0x40]
    // 0xba8034: r0 = Text()
    //     0xba8034: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba8038: r9 = "APPLIED"
    //     0xba8038: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xba803c: ldr             x9, [x9, #0x5e8]
    // 0xba8040: StoreField: r0->field_b = r9
    //     0xba8040: stur            w9, [x0, #0xb]
    // 0xba8044: ldur            x1, [fp, #-0x40]
    // 0xba8048: StoreField: r0->field_13 = r1
    //     0xba8048: stur            w1, [x0, #0x13]
    // 0xba804c: mov             x1, x0
    // 0xba8050: b               #0xba80b4
    // 0xba8054: ldur            x2, [fp, #-0x10]
    // 0xba8058: LoadField: r1 = r2->field_f
    //     0xba8058: ldur            w1, [x2, #0xf]
    // 0xba805c: DecompressPointer r1
    //     0xba805c: add             x1, x1, HEAP, lsl #32
    // 0xba8060: r0 = of()
    //     0xba8060: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba8064: LoadField: r1 = r0->field_87
    //     0xba8064: ldur            w1, [x0, #0x87]
    // 0xba8068: DecompressPointer r1
    //     0xba8068: add             x1, x1, HEAP, lsl #32
    // 0xba806c: LoadField: r0 = r1->field_7
    //     0xba806c: ldur            w0, [x1, #7]
    // 0xba8070: DecompressPointer r0
    //     0xba8070: add             x0, x0, HEAP, lsl #32
    // 0xba8074: r16 = 14.000000
    //     0xba8074: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba8078: ldr             x16, [x16, #0x1d8]
    // 0xba807c: r30 = Instance_Color
    //     0xba807c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xba8080: stp             lr, x16, [SP]
    // 0xba8084: mov             x1, x0
    // 0xba8088: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba8088: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba808c: ldr             x4, [x4, #0xaa0]
    // 0xba8090: r0 = copyWith()
    //     0xba8090: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba8094: stur            x0, [fp, #-0x40]
    // 0xba8098: r0 = Text()
    //     0xba8098: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba809c: r10 = "APPLY"
    //     0xba809c: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xba80a0: ldr             x10, [x10, #0x5f0]
    // 0xba80a4: StoreField: r0->field_b = r10
    //     0xba80a4: stur            w10, [x0, #0xb]
    // 0xba80a8: ldur            x1, [fp, #-0x40]
    // 0xba80ac: StoreField: r0->field_13 = r1
    //     0xba80ac: stur            w1, [x0, #0x13]
    // 0xba80b0: mov             x1, x0
    // 0xba80b4: ldur            x0, [fp, #-0x38]
    // 0xba80b8: stur            x1, [fp, #-0x40]
    // 0xba80bc: r0 = InkWell()
    //     0xba80bc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xba80c0: mov             x3, x0
    // 0xba80c4: ldur            x0, [fp, #-0x40]
    // 0xba80c8: stur            x3, [fp, #-0x48]
    // 0xba80cc: StoreField: r3->field_b = r0
    //     0xba80cc: stur            w0, [x3, #0xb]
    // 0xba80d0: ldur            x2, [fp, #-0x10]
    // 0xba80d4: r1 = Function '<anonymous closure>':.
    //     0xba80d4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a5f8] AnonymousClosure: (0xba99b8), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba80d8: ldr             x1, [x1, #0x5f8]
    // 0xba80dc: r0 = AllocateClosure()
    //     0xba80dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba80e0: mov             x1, x0
    // 0xba80e4: ldur            x0, [fp, #-0x48]
    // 0xba80e8: StoreField: r0->field_f = r1
    //     0xba80e8: stur            w1, [x0, #0xf]
    // 0xba80ec: r11 = true
    //     0xba80ec: add             x11, NULL, #0x20  ; true
    // 0xba80f0: StoreField: r0->field_43 = r11
    //     0xba80f0: stur            w11, [x0, #0x43]
    // 0xba80f4: r12 = Instance_BoxShape
    //     0xba80f4: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba80f8: ldr             x12, [x12, #0x80]
    // 0xba80fc: StoreField: r0->field_47 = r12
    //     0xba80fc: stur            w12, [x0, #0x47]
    // 0xba8100: StoreField: r0->field_6f = r11
    //     0xba8100: stur            w11, [x0, #0x6f]
    // 0xba8104: r13 = false
    //     0xba8104: add             x13, NULL, #0x30  ; false
    // 0xba8108: StoreField: r0->field_73 = r13
    //     0xba8108: stur            w13, [x0, #0x73]
    // 0xba810c: StoreField: r0->field_83 = r11
    //     0xba810c: stur            w11, [x0, #0x83]
    // 0xba8110: StoreField: r0->field_7b = r13
    //     0xba8110: stur            w13, [x0, #0x7b]
    // 0xba8114: r1 = <FlexParentData>
    //     0xba8114: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba8118: ldr             x1, [x1, #0xe00]
    // 0xba811c: r0 = Expanded()
    //     0xba811c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba8120: r14 = 1
    //     0xba8120: movz            x14, #0x1
    // 0xba8124: stur            x0, [fp, #-0x40]
    // 0xba8128: StoreField: r0->field_13 = r14
    //     0xba8128: stur            x14, [x0, #0x13]
    // 0xba812c: r19 = Instance_FlexFit
    //     0xba812c: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba8130: ldr             x19, [x19, #0xe08]
    // 0xba8134: StoreField: r0->field_1b = r19
    //     0xba8134: stur            w19, [x0, #0x1b]
    // 0xba8138: ldur            x1, [fp, #-0x48]
    // 0xba813c: StoreField: r0->field_b = r1
    //     0xba813c: stur            w1, [x0, #0xb]
    // 0xba8140: r1 = Null
    //     0xba8140: mov             x1, NULL
    // 0xba8144: r2 = 8
    //     0xba8144: movz            x2, #0x8
    // 0xba8148: r0 = AllocateArray()
    //     0xba8148: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba814c: stur            x0, [fp, #-0x48]
    // 0xba8150: r16 = Instance_Expanded
    //     0xba8150: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a600] Obj!Expanded@d656d1
    //     0xba8154: ldr             x16, [x16, #0x600]
    // 0xba8158: StoreField: r0->field_f = r16
    //     0xba8158: stur            w16, [x0, #0xf]
    // 0xba815c: ldur            x1, [fp, #-0x38]
    // 0xba8160: StoreField: r0->field_13 = r1
    //     0xba8160: stur            w1, [x0, #0x13]
    // 0xba8164: r16 = Instance_Spacer
    //     0xba8164: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba8168: ldr             x16, [x16, #0xf0]
    // 0xba816c: ArrayStore: r0[0] = r16  ; List_4
    //     0xba816c: stur            w16, [x0, #0x17]
    // 0xba8170: ldur            x1, [fp, #-0x40]
    // 0xba8174: StoreField: r0->field_1b = r1
    //     0xba8174: stur            w1, [x0, #0x1b]
    // 0xba8178: r1 = <Widget>
    //     0xba8178: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba817c: r0 = AllocateGrowableArray()
    //     0xba817c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba8180: mov             x1, x0
    // 0xba8184: ldur            x0, [fp, #-0x48]
    // 0xba8188: stur            x1, [fp, #-0x38]
    // 0xba818c: StoreField: r1->field_f = r0
    //     0xba818c: stur            w0, [x1, #0xf]
    // 0xba8190: r20 = 8
    //     0xba8190: movz            x20, #0x8
    // 0xba8194: StoreField: r1->field_b = r20
    //     0xba8194: stur            w20, [x1, #0xb]
    // 0xba8198: r0 = Row()
    //     0xba8198: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba819c: r23 = Instance_Axis
    //     0xba819c: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba81a0: stur            x0, [fp, #-0x40]
    // 0xba81a4: StoreField: r0->field_f = r23
    //     0xba81a4: stur            w23, [x0, #0xf]
    // 0xba81a8: r24 = Instance_MainAxisAlignment
    //     0xba81a8: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba81ac: ldr             x24, [x24, #0xa08]
    // 0xba81b0: StoreField: r0->field_13 = r24
    //     0xba81b0: stur            w24, [x0, #0x13]
    // 0xba81b4: r25 = Instance_MainAxisSize
    //     0xba81b4: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba81b8: ldr             x25, [x25, #0xa10]
    // 0xba81bc: ArrayStore: r0[0] = r25  ; List_4
    //     0xba81bc: stur            w25, [x0, #0x17]
    // 0xba81c0: r1 = Instance_CrossAxisAlignment
    //     0xba81c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba81c4: ldr             x1, [x1, #0xa18]
    // 0xba81c8: StoreField: r0->field_1b = r1
    //     0xba81c8: stur            w1, [x0, #0x1b]
    // 0xba81cc: r1 = Instance_VerticalDirection
    //     0xba81cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba81d0: ldr             x1, [x1, #0xa20]
    // 0xba81d4: StoreField: r0->field_23 = r1
    //     0xba81d4: stur            w1, [x0, #0x23]
    // 0xba81d8: r1 = Instance_Clip
    //     0xba81d8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba81dc: ldr             x1, [x1, #0x38]
    // 0xba81e0: StoreField: r0->field_2b = r1
    //     0xba81e0: stur            w1, [x0, #0x2b]
    // 0xba81e4: StoreField: r0->field_2f = rZR
    //     0xba81e4: stur            xzr, [x0, #0x2f]
    // 0xba81e8: ldur            x1, [fp, #-0x38]
    // 0xba81ec: StoreField: r0->field_b = r1
    //     0xba81ec: stur            w1, [x0, #0xb]
    // 0xba81f0: r0 = Padding()
    //     0xba81f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba81f4: mov             x1, x0
    // 0xba81f8: r0 = Instance_EdgeInsets
    //     0xba81f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba81fc: ldr             x0, [x0, #0x980]
    // 0xba8200: stur            x1, [fp, #-0x38]
    // 0xba8204: StoreField: r1->field_f = r0
    //     0xba8204: stur            w0, [x1, #0xf]
    // 0xba8208: ldur            x2, [fp, #-0x40]
    // 0xba820c: StoreField: r1->field_b = r2
    //     0xba820c: stur            w2, [x1, #0xb]
    // 0xba8210: r0 = Container()
    //     0xba8210: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xba8214: stur            x0, [fp, #-0x40]
    // 0xba8218: r16 = 85.000000
    //     0xba8218: add             x16, PP, #0x44, lsl #12  ; [pp+0x44d78] 85
    //     0xba821c: ldr             x16, [x16, #0xd78]
    // 0xba8220: ldur            lr, [fp, #-0x30]
    // 0xba8224: stp             lr, x16, [SP, #8]
    // 0xba8228: ldur            x16, [fp, #-0x38]
    // 0xba822c: str             x16, [SP]
    // 0xba8230: mov             x1, x0
    // 0xba8234: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xba8234: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xba8238: ldr             x4, [x4, #0xc78]
    // 0xba823c: r0 = Container()
    //     0xba823c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xba8240: r1 = <Path>
    //     0xba8240: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xba8244: ldr             x1, [x1, #0xd30]
    // 0xba8248: r0 = MovieTicketClipper()
    //     0xba8248: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xba824c: stur            x0, [fp, #-0x30]
    // 0xba8250: r0 = ClipPath()
    //     0xba8250: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xba8254: mov             x1, x0
    // 0xba8258: ldur            x0, [fp, #-0x30]
    // 0xba825c: StoreField: r1->field_f = r0
    //     0xba825c: stur            w0, [x1, #0xf]
    // 0xba8260: r0 = Instance_Clip
    //     0xba8260: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xba8264: ldr             x0, [x0, #0x138]
    // 0xba8268: StoreField: r1->field_13 = r0
    //     0xba8268: stur            w0, [x1, #0x13]
    // 0xba826c: ldur            x0, [fp, #-0x40]
    // 0xba8270: StoreField: r1->field_b = r0
    //     0xba8270: stur            w0, [x1, #0xb]
    // 0xba8274: mov             x0, x1
    // 0xba8278: b               #0xba9270
    // 0xba827c: ldur            x8, [fp, #-8]
    // 0xba8280: r11 = true
    //     0xba8280: add             x11, NULL, #0x20  ; true
    // 0xba8284: r5 = 4
    //     0xba8284: movz            x5, #0x4
    // 0xba8288: r0 = "Know "
    //     0xba8288: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xba828c: ldr             x0, [x0, #0x5d0]
    // 0xba8290: r2 = "More"
    //     0xba8290: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xba8294: ldr             x2, [x2, #0x5d8]
    // 0xba8298: r4 = Instance_EdgeInsets
    //     0xba8298: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xba829c: ldr             x4, [x4, #0xc40]
    // 0xba82a0: r7 = Instance_CrossAxisAlignment
    //     0xba82a0: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba82a4: ldr             x7, [x7, #0x890]
    // 0xba82a8: r24 = Instance_MainAxisAlignment
    //     0xba82a8: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba82ac: ldr             x24, [x24, #0xa08]
    // 0xba82b0: r13 = false
    //     0xba82b0: add             x13, NULL, #0x30  ; false
    // 0xba82b4: r10 = "APPLY"
    //     0xba82b4: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xba82b8: ldr             x10, [x10, #0x5f0]
    // 0xba82bc: r9 = "APPLIED"
    //     0xba82bc: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xba82c0: ldr             x9, [x9, #0x5e8]
    // 0xba82c4: r25 = Instance_MainAxisSize
    //     0xba82c4: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba82c8: ldr             x25, [x25, #0xa10]
    // 0xba82cc: r12 = Instance_BoxShape
    //     0xba82cc: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba82d0: ldr             x12, [x12, #0x80]
    // 0xba82d4: r19 = Instance_FlexFit
    //     0xba82d4: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba82d8: ldr             x19, [x19, #0xe08]
    // 0xba82dc: r20 = 8
    //     0xba82dc: movz            x20, #0x8
    // 0xba82e0: r1 = Instance_CrossAxisAlignment
    //     0xba82e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba82e4: ldr             x1, [x1, #0xa18]
    // 0xba82e8: r23 = Instance_Axis
    //     0xba82e8: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba82ec: r3 = Instance__DeferringMouseCursor
    //     0xba82ec: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba82f0: r6 = Instance_Axis
    //     0xba82f0: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba82f4: d1 = 1.000000
    //     0xba82f4: fmov            d1, #1.00000000
    // 0xba82f8: d2 = 0.500000
    //     0xba82f8: fmov            d2, #0.50000000
    // 0xba82fc: r14 = 1
    //     0xba82fc: movz            x14, #0x1
    // 0xba8300: b               #0xba8388
    // 0xba8304: ldur            x8, [fp, #-8]
    // 0xba8308: r11 = true
    //     0xba8308: add             x11, NULL, #0x20  ; true
    // 0xba830c: r5 = 4
    //     0xba830c: movz            x5, #0x4
    // 0xba8310: r0 = "Know "
    //     0xba8310: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xba8314: ldr             x0, [x0, #0x5d0]
    // 0xba8318: r2 = "More"
    //     0xba8318: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xba831c: ldr             x2, [x2, #0x5d8]
    // 0xba8320: r4 = Instance_EdgeInsets
    //     0xba8320: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xba8324: ldr             x4, [x4, #0xc40]
    // 0xba8328: r7 = Instance_CrossAxisAlignment
    //     0xba8328: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba832c: ldr             x7, [x7, #0x890]
    // 0xba8330: r24 = Instance_MainAxisAlignment
    //     0xba8330: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba8334: ldr             x24, [x24, #0xa08]
    // 0xba8338: r13 = false
    //     0xba8338: add             x13, NULL, #0x30  ; false
    // 0xba833c: r10 = "APPLY"
    //     0xba833c: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xba8340: ldr             x10, [x10, #0x5f0]
    // 0xba8344: r9 = "APPLIED"
    //     0xba8344: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xba8348: ldr             x9, [x9, #0x5e8]
    // 0xba834c: r25 = Instance_MainAxisSize
    //     0xba834c: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba8350: ldr             x25, [x25, #0xa10]
    // 0xba8354: r12 = Instance_BoxShape
    //     0xba8354: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba8358: ldr             x12, [x12, #0x80]
    // 0xba835c: r19 = Instance_FlexFit
    //     0xba835c: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba8360: ldr             x19, [x19, #0xe08]
    // 0xba8364: r20 = 8
    //     0xba8364: movz            x20, #0x8
    // 0xba8368: r1 = Instance_CrossAxisAlignment
    //     0xba8368: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba836c: ldr             x1, [x1, #0xa18]
    // 0xba8370: r23 = Instance_Axis
    //     0xba8370: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba8374: r3 = Instance__DeferringMouseCursor
    //     0xba8374: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba8378: r6 = Instance_Axis
    //     0xba8378: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba837c: d1 = 1.000000
    //     0xba837c: fmov            d1, #1.00000000
    // 0xba8380: d2 = 0.500000
    //     0xba8380: fmov            d2, #0.50000000
    // 0xba8384: r14 = 1
    //     0xba8384: movz            x14, #0x1
    // 0xba8388: r1 = Instance_MaterialColor
    //     0xba8388: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xba838c: ldr             x1, [x1, #0xdc0]
    // 0xba8390: d0 = 0.300000
    //     0xba8390: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xba8394: ldr             d0, [x17, #0x658]
    // 0xba8398: r0 = withOpacity()
    //     0xba8398: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba839c: mov             x3, x0
    // 0xba83a0: ldur            x2, [fp, #-8]
    // 0xba83a4: stur            x3, [fp, #-0x30]
    // 0xba83a8: LoadField: r0 = r2->field_f
    //     0xba83a8: ldur            w0, [x2, #0xf]
    // 0xba83ac: DecompressPointer r0
    //     0xba83ac: add             x0, x0, HEAP, lsl #32
    // 0xba83b0: LoadField: r1 = r0->field_b
    //     0xba83b0: ldur            w1, [x0, #0xb]
    // 0xba83b4: DecompressPointer r1
    //     0xba83b4: add             x1, x1, HEAP, lsl #32
    // 0xba83b8: cmp             w1, NULL
    // 0xba83bc: b.eq            #0xba92fc
    // 0xba83c0: LoadField: r0 = r1->field_f
    //     0xba83c0: ldur            w0, [x1, #0xf]
    // 0xba83c4: DecompressPointer r0
    //     0xba83c4: add             x0, x0, HEAP, lsl #32
    // 0xba83c8: cmp             w0, NULL
    // 0xba83cc: b.ne            #0xba83dc
    // 0xba83d0: ldur            x4, [fp, #-0x10]
    // 0xba83d4: r0 = Null
    //     0xba83d4: mov             x0, NULL
    // 0xba83d8: b               #0xba842c
    // 0xba83dc: ldur            x4, [fp, #-0x10]
    // 0xba83e0: LoadField: r5 = r0->field_f
    //     0xba83e0: ldur            w5, [x0, #0xf]
    // 0xba83e4: DecompressPointer r5
    //     0xba83e4: add             x5, x5, HEAP, lsl #32
    // 0xba83e8: LoadField: r0 = r4->field_13
    //     0xba83e8: ldur            w0, [x4, #0x13]
    // 0xba83ec: DecompressPointer r0
    //     0xba83ec: add             x0, x0, HEAP, lsl #32
    // 0xba83f0: LoadField: r1 = r5->field_b
    //     0xba83f0: ldur            w1, [x5, #0xb]
    // 0xba83f4: r6 = LoadInt32Instr(r0)
    //     0xba83f4: sbfx            x6, x0, #1, #0x1f
    //     0xba83f8: tbz             w0, #0, #0xba8400
    //     0xba83fc: ldur            x6, [x0, #7]
    // 0xba8400: r0 = LoadInt32Instr(r1)
    //     0xba8400: sbfx            x0, x1, #1, #0x1f
    // 0xba8404: mov             x1, x6
    // 0xba8408: cmp             x1, x0
    // 0xba840c: b.hs            #0xba9300
    // 0xba8410: LoadField: r0 = r5->field_f
    //     0xba8410: ldur            w0, [x5, #0xf]
    // 0xba8414: DecompressPointer r0
    //     0xba8414: add             x0, x0, HEAP, lsl #32
    // 0xba8418: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba8418: add             x16, x0, x6, lsl #2
    //     0xba841c: ldur            w1, [x16, #0xf]
    // 0xba8420: DecompressPointer r1
    //     0xba8420: add             x1, x1, HEAP, lsl #32
    // 0xba8424: LoadField: r0 = r1->field_b
    //     0xba8424: ldur            w0, [x1, #0xb]
    // 0xba8428: DecompressPointer r0
    //     0xba8428: add             x0, x0, HEAP, lsl #32
    // 0xba842c: r1 = LoadClassIdInstr(r0)
    //     0xba842c: ldur            x1, [x0, #-1]
    //     0xba8430: ubfx            x1, x1, #0xc, #0x14
    // 0xba8434: r16 = "free_gift"
    //     0xba8434: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a608] "free_gift"
    //     0xba8438: ldr             x16, [x16, #0x608]
    // 0xba843c: stp             x16, x0, [SP]
    // 0xba8440: mov             x0, x1
    // 0xba8444: mov             lr, x0
    // 0xba8448: ldr             lr, [x21, lr, lsl #3]
    // 0xba844c: blr             lr
    // 0xba8450: tbz             w0, #4, #0xba85b8
    // 0xba8454: ldur            x2, [fp, #-8]
    // 0xba8458: LoadField: r0 = r2->field_f
    //     0xba8458: ldur            w0, [x2, #0xf]
    // 0xba845c: DecompressPointer r0
    //     0xba845c: add             x0, x0, HEAP, lsl #32
    // 0xba8460: LoadField: r3 = r0->field_b
    //     0xba8460: ldur            w3, [x0, #0xb]
    // 0xba8464: DecompressPointer r3
    //     0xba8464: add             x3, x3, HEAP, lsl #32
    // 0xba8468: cmp             w3, NULL
    // 0xba846c: b.eq            #0xba9304
    // 0xba8470: LoadField: r0 = r3->field_f
    //     0xba8470: ldur            w0, [x3, #0xf]
    // 0xba8474: DecompressPointer r0
    //     0xba8474: add             x0, x0, HEAP, lsl #32
    // 0xba8478: cmp             w0, NULL
    // 0xba847c: b.ne            #0xba848c
    // 0xba8480: ldur            x4, [fp, #-0x10]
    // 0xba8484: r0 = Null
    //     0xba8484: mov             x0, NULL
    // 0xba8488: b               #0xba84dc
    // 0xba848c: ldur            x4, [fp, #-0x10]
    // 0xba8490: LoadField: r5 = r0->field_f
    //     0xba8490: ldur            w5, [x0, #0xf]
    // 0xba8494: DecompressPointer r5
    //     0xba8494: add             x5, x5, HEAP, lsl #32
    // 0xba8498: LoadField: r0 = r4->field_13
    //     0xba8498: ldur            w0, [x4, #0x13]
    // 0xba849c: DecompressPointer r0
    //     0xba849c: add             x0, x0, HEAP, lsl #32
    // 0xba84a0: LoadField: r1 = r5->field_b
    //     0xba84a0: ldur            w1, [x5, #0xb]
    // 0xba84a4: r6 = LoadInt32Instr(r0)
    //     0xba84a4: sbfx            x6, x0, #1, #0x1f
    //     0xba84a8: tbz             w0, #0, #0xba84b0
    //     0xba84ac: ldur            x6, [x0, #7]
    // 0xba84b0: r0 = LoadInt32Instr(r1)
    //     0xba84b0: sbfx            x0, x1, #1, #0x1f
    // 0xba84b4: mov             x1, x6
    // 0xba84b8: cmp             x1, x0
    // 0xba84bc: b.hs            #0xba9308
    // 0xba84c0: LoadField: r0 = r5->field_f
    //     0xba84c0: ldur            w0, [x5, #0xf]
    // 0xba84c4: DecompressPointer r0
    //     0xba84c4: add             x0, x0, HEAP, lsl #32
    // 0xba84c8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba84c8: add             x16, x0, x6, lsl #2
    //     0xba84cc: ldur            w1, [x16, #0xf]
    // 0xba84d0: DecompressPointer r1
    //     0xba84d0: add             x1, x1, HEAP, lsl #32
    // 0xba84d4: LoadField: r0 = r1->field_27
    //     0xba84d4: ldur            w0, [x1, #0x27]
    // 0xba84d8: DecompressPointer r0
    //     0xba84d8: add             x0, x0, HEAP, lsl #32
    // 0xba84dc: cmp             w0, NULL
    // 0xba84e0: b.ne            #0xba8574
    // 0xba84e4: LoadField: r0 = r3->field_f
    //     0xba84e4: ldur            w0, [x3, #0xf]
    // 0xba84e8: DecompressPointer r0
    //     0xba84e8: add             x0, x0, HEAP, lsl #32
    // 0xba84ec: cmp             w0, NULL
    // 0xba84f0: b.ne            #0xba84fc
    // 0xba84f4: r0 = Null
    //     0xba84f4: mov             x0, NULL
    // 0xba84f8: b               #0xba8548
    // 0xba84fc: LoadField: r3 = r0->field_f
    //     0xba84fc: ldur            w3, [x0, #0xf]
    // 0xba8500: DecompressPointer r3
    //     0xba8500: add             x3, x3, HEAP, lsl #32
    // 0xba8504: LoadField: r0 = r4->field_13
    //     0xba8504: ldur            w0, [x4, #0x13]
    // 0xba8508: DecompressPointer r0
    //     0xba8508: add             x0, x0, HEAP, lsl #32
    // 0xba850c: LoadField: r1 = r3->field_b
    //     0xba850c: ldur            w1, [x3, #0xb]
    // 0xba8510: r5 = LoadInt32Instr(r0)
    //     0xba8510: sbfx            x5, x0, #1, #0x1f
    //     0xba8514: tbz             w0, #0, #0xba851c
    //     0xba8518: ldur            x5, [x0, #7]
    // 0xba851c: r0 = LoadInt32Instr(r1)
    //     0xba851c: sbfx            x0, x1, #1, #0x1f
    // 0xba8520: mov             x1, x5
    // 0xba8524: cmp             x1, x0
    // 0xba8528: b.hs            #0xba930c
    // 0xba852c: LoadField: r0 = r3->field_f
    //     0xba852c: ldur            w0, [x3, #0xf]
    // 0xba8530: DecompressPointer r0
    //     0xba8530: add             x0, x0, HEAP, lsl #32
    // 0xba8534: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba8534: add             x16, x0, x5, lsl #2
    //     0xba8538: ldur            w1, [x16, #0xf]
    // 0xba853c: DecompressPointer r1
    //     0xba853c: add             x1, x1, HEAP, lsl #32
    // 0xba8540: LoadField: r0 = r1->field_f
    //     0xba8540: ldur            w0, [x1, #0xf]
    // 0xba8544: DecompressPointer r0
    //     0xba8544: add             x0, x0, HEAP, lsl #32
    // 0xba8548: r1 = LoadClassIdInstr(r0)
    //     0xba8548: ldur            x1, [x0, #-1]
    //     0xba854c: ubfx            x1, x1, #0xc, #0x14
    // 0xba8550: r16 = "sale_event_static_coupon"
    //     0xba8550: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xba8554: ldr             x16, [x16, #0x610]
    // 0xba8558: stp             x16, x0, [SP]
    // 0xba855c: mov             x0, x1
    // 0xba8560: mov             lr, x0
    // 0xba8564: ldr             lr, [x21, lr, lsl #3]
    // 0xba8568: blr             lr
    // 0xba856c: tbnz            w0, #4, #0xba8584
    // 0xba8570: b               #0xba8578
    // 0xba8574: tbnz            w0, #4, #0xba8584
    // 0xba8578: r0 = Instance_Color
    //     0xba8578: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba857c: ldr             x0, [x0, #0x858]
    // 0xba8580: b               #0xba8590
    // 0xba8584: r1 = Instance_Color
    //     0xba8584: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba8588: d0 = 0.400000
    //     0xba8588: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xba858c: r0 = withOpacity()
    //     0xba858c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba8590: stur            x0, [fp, #-0x38]
    // 0xba8594: r0 = Icon()
    //     0xba8594: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xba8598: mov             x1, x0
    // 0xba859c: r0 = Instance_IconData
    //     0xba859c: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a618] Obj!IconData@d55541
    //     0xba85a0: ldr             x0, [x0, #0x618]
    // 0xba85a4: StoreField: r1->field_b = r0
    //     0xba85a4: stur            w0, [x1, #0xb]
    // 0xba85a8: ldur            x0, [fp, #-0x38]
    // 0xba85ac: StoreField: r1->field_23 = r0
    //     0xba85ac: stur            w0, [x1, #0x23]
    // 0xba85b0: mov             x3, x1
    // 0xba85b4: b               #0xba85f0
    // 0xba85b8: r0 = SvgPicture()
    //     0xba85b8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xba85bc: stur            x0, [fp, #-0x38]
    // 0xba85c0: r16 = 30.000000
    //     0xba85c0: add             x16, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xba85c4: ldr             x16, [x16, #0x768]
    // 0xba85c8: r30 = 30.000000
    //     0xba85c8: add             lr, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xba85cc: ldr             lr, [lr, #0x768]
    // 0xba85d0: stp             lr, x16, [SP]
    // 0xba85d4: mov             x1, x0
    // 0xba85d8: r2 = "assets/images/gift-icon-popup.svg"
    //     0xba85d8: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xba85dc: ldr             x2, [x2, #0x8e8]
    // 0xba85e0: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xba85e0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xba85e4: ldr             x4, [x4, #0x900]
    // 0xba85e8: r0 = SvgPicture.asset()
    //     0xba85e8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xba85ec: ldur            x3, [fp, #-0x38]
    // 0xba85f0: ldur            x0, [fp, #-8]
    // 0xba85f4: ldur            x2, [fp, #-0x10]
    // 0xba85f8: stur            x3, [fp, #-0x38]
    // 0xba85fc: r1 = <FlexParentData>
    //     0xba85fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba8600: ldr             x1, [x1, #0xe00]
    // 0xba8604: r0 = Expanded()
    //     0xba8604: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba8608: stur            x0, [fp, #-0x40]
    // 0xba860c: StoreField: r0->field_13 = rZR
    //     0xba860c: stur            xzr, [x0, #0x13]
    // 0xba8610: r2 = Instance_FlexFit
    //     0xba8610: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba8614: ldr             x2, [x2, #0xe08]
    // 0xba8618: StoreField: r0->field_1b = r2
    //     0xba8618: stur            w2, [x0, #0x1b]
    // 0xba861c: ldur            x1, [fp, #-0x38]
    // 0xba8620: StoreField: r0->field_b = r1
    //     0xba8620: stur            w1, [x0, #0xb]
    // 0xba8624: ldur            x3, [fp, #-0x10]
    // 0xba8628: LoadField: r1 = r3->field_f
    //     0xba8628: ldur            w1, [x3, #0xf]
    // 0xba862c: DecompressPointer r1
    //     0xba862c: add             x1, x1, HEAP, lsl #32
    // 0xba8630: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xba8630: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xba8634: r0 = _of()
    //     0xba8634: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xba8638: LoadField: r1 = r0->field_7
    //     0xba8638: ldur            w1, [x0, #7]
    // 0xba863c: DecompressPointer r1
    //     0xba863c: add             x1, x1, HEAP, lsl #32
    // 0xba8640: LoadField: d0 = r1->field_7
    //     0xba8640: ldur            d0, [x1, #7]
    // 0xba8644: d1 = 0.500000
    //     0xba8644: fmov            d1, #0.50000000
    // 0xba8648: fmul            d2, d0, d1
    // 0xba864c: ldur            x2, [fp, #-8]
    // 0xba8650: stur            d2, [fp, #-0x68]
    // 0xba8654: LoadField: r0 = r2->field_f
    //     0xba8654: ldur            w0, [x2, #0xf]
    // 0xba8658: DecompressPointer r0
    //     0xba8658: add             x0, x0, HEAP, lsl #32
    // 0xba865c: LoadField: r1 = r0->field_b
    //     0xba865c: ldur            w1, [x0, #0xb]
    // 0xba8660: DecompressPointer r1
    //     0xba8660: add             x1, x1, HEAP, lsl #32
    // 0xba8664: cmp             w1, NULL
    // 0xba8668: b.eq            #0xba9310
    // 0xba866c: LoadField: r0 = r1->field_f
    //     0xba866c: ldur            w0, [x1, #0xf]
    // 0xba8670: DecompressPointer r0
    //     0xba8670: add             x0, x0, HEAP, lsl #32
    // 0xba8674: cmp             w0, NULL
    // 0xba8678: b.ne            #0xba8688
    // 0xba867c: ldur            x3, [fp, #-0x10]
    // 0xba8680: r0 = Null
    //     0xba8680: mov             x0, NULL
    // 0xba8684: b               #0xba86d8
    // 0xba8688: ldur            x3, [fp, #-0x10]
    // 0xba868c: LoadField: r4 = r0->field_f
    //     0xba868c: ldur            w4, [x0, #0xf]
    // 0xba8690: DecompressPointer r4
    //     0xba8690: add             x4, x4, HEAP, lsl #32
    // 0xba8694: LoadField: r0 = r3->field_13
    //     0xba8694: ldur            w0, [x3, #0x13]
    // 0xba8698: DecompressPointer r0
    //     0xba8698: add             x0, x0, HEAP, lsl #32
    // 0xba869c: LoadField: r1 = r4->field_b
    //     0xba869c: ldur            w1, [x4, #0xb]
    // 0xba86a0: r5 = LoadInt32Instr(r0)
    //     0xba86a0: sbfx            x5, x0, #1, #0x1f
    //     0xba86a4: tbz             w0, #0, #0xba86ac
    //     0xba86a8: ldur            x5, [x0, #7]
    // 0xba86ac: r0 = LoadInt32Instr(r1)
    //     0xba86ac: sbfx            x0, x1, #1, #0x1f
    // 0xba86b0: mov             x1, x5
    // 0xba86b4: cmp             x1, x0
    // 0xba86b8: b.hs            #0xba9314
    // 0xba86bc: LoadField: r0 = r4->field_f
    //     0xba86bc: ldur            w0, [x4, #0xf]
    // 0xba86c0: DecompressPointer r0
    //     0xba86c0: add             x0, x0, HEAP, lsl #32
    // 0xba86c4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba86c4: add             x16, x0, x5, lsl #2
    //     0xba86c8: ldur            w1, [x16, #0xf]
    // 0xba86cc: DecompressPointer r1
    //     0xba86cc: add             x1, x1, HEAP, lsl #32
    // 0xba86d0: LoadField: r0 = r1->field_13
    //     0xba86d0: ldur            w0, [x1, #0x13]
    // 0xba86d4: DecompressPointer r0
    //     0xba86d4: add             x0, x0, HEAP, lsl #32
    // 0xba86d8: cmp             w0, NULL
    // 0xba86dc: b.ne            #0xba86e4
    // 0xba86e0: r0 = ""
    //     0xba86e0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba86e4: stur            x0, [fp, #-0x38]
    // 0xba86e8: LoadField: r1 = r3->field_f
    //     0xba86e8: ldur            w1, [x3, #0xf]
    // 0xba86ec: DecompressPointer r1
    //     0xba86ec: add             x1, x1, HEAP, lsl #32
    // 0xba86f0: r0 = of()
    //     0xba86f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba86f4: LoadField: r1 = r0->field_87
    //     0xba86f4: ldur            w1, [x0, #0x87]
    // 0xba86f8: DecompressPointer r1
    //     0xba86f8: add             x1, x1, HEAP, lsl #32
    // 0xba86fc: LoadField: r2 = r1->field_7
    //     0xba86fc: ldur            w2, [x1, #7]
    // 0xba8700: DecompressPointer r2
    //     0xba8700: add             x2, x2, HEAP, lsl #32
    // 0xba8704: ldur            x3, [fp, #-8]
    // 0xba8708: stur            x2, [fp, #-0x48]
    // 0xba870c: LoadField: r0 = r3->field_f
    //     0xba870c: ldur            w0, [x3, #0xf]
    // 0xba8710: DecompressPointer r0
    //     0xba8710: add             x0, x0, HEAP, lsl #32
    // 0xba8714: LoadField: r4 = r0->field_b
    //     0xba8714: ldur            w4, [x0, #0xb]
    // 0xba8718: DecompressPointer r4
    //     0xba8718: add             x4, x4, HEAP, lsl #32
    // 0xba871c: cmp             w4, NULL
    // 0xba8720: b.eq            #0xba9318
    // 0xba8724: LoadField: r0 = r4->field_f
    //     0xba8724: ldur            w0, [x4, #0xf]
    // 0xba8728: DecompressPointer r0
    //     0xba8728: add             x0, x0, HEAP, lsl #32
    // 0xba872c: cmp             w0, NULL
    // 0xba8730: b.ne            #0xba8740
    // 0xba8734: ldur            x5, [fp, #-0x10]
    // 0xba8738: r0 = Null
    //     0xba8738: mov             x0, NULL
    // 0xba873c: b               #0xba8790
    // 0xba8740: ldur            x5, [fp, #-0x10]
    // 0xba8744: LoadField: r6 = r0->field_f
    //     0xba8744: ldur            w6, [x0, #0xf]
    // 0xba8748: DecompressPointer r6
    //     0xba8748: add             x6, x6, HEAP, lsl #32
    // 0xba874c: LoadField: r0 = r5->field_13
    //     0xba874c: ldur            w0, [x5, #0x13]
    // 0xba8750: DecompressPointer r0
    //     0xba8750: add             x0, x0, HEAP, lsl #32
    // 0xba8754: LoadField: r1 = r6->field_b
    //     0xba8754: ldur            w1, [x6, #0xb]
    // 0xba8758: r7 = LoadInt32Instr(r0)
    //     0xba8758: sbfx            x7, x0, #1, #0x1f
    //     0xba875c: tbz             w0, #0, #0xba8764
    //     0xba8760: ldur            x7, [x0, #7]
    // 0xba8764: r0 = LoadInt32Instr(r1)
    //     0xba8764: sbfx            x0, x1, #1, #0x1f
    // 0xba8768: mov             x1, x7
    // 0xba876c: cmp             x1, x0
    // 0xba8770: b.hs            #0xba931c
    // 0xba8774: LoadField: r0 = r6->field_f
    //     0xba8774: ldur            w0, [x6, #0xf]
    // 0xba8778: DecompressPointer r0
    //     0xba8778: add             x0, x0, HEAP, lsl #32
    // 0xba877c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xba877c: add             x16, x0, x7, lsl #2
    //     0xba8780: ldur            w1, [x16, #0xf]
    // 0xba8784: DecompressPointer r1
    //     0xba8784: add             x1, x1, HEAP, lsl #32
    // 0xba8788: LoadField: r0 = r1->field_27
    //     0xba8788: ldur            w0, [x1, #0x27]
    // 0xba878c: DecompressPointer r0
    //     0xba878c: add             x0, x0, HEAP, lsl #32
    // 0xba8790: cmp             w0, NULL
    // 0xba8794: b.ne            #0xba8828
    // 0xba8798: LoadField: r0 = r4->field_f
    //     0xba8798: ldur            w0, [x4, #0xf]
    // 0xba879c: DecompressPointer r0
    //     0xba879c: add             x0, x0, HEAP, lsl #32
    // 0xba87a0: cmp             w0, NULL
    // 0xba87a4: b.ne            #0xba87b0
    // 0xba87a8: r0 = Null
    //     0xba87a8: mov             x0, NULL
    // 0xba87ac: b               #0xba87fc
    // 0xba87b0: LoadField: r4 = r0->field_f
    //     0xba87b0: ldur            w4, [x0, #0xf]
    // 0xba87b4: DecompressPointer r4
    //     0xba87b4: add             x4, x4, HEAP, lsl #32
    // 0xba87b8: LoadField: r0 = r5->field_13
    //     0xba87b8: ldur            w0, [x5, #0x13]
    // 0xba87bc: DecompressPointer r0
    //     0xba87bc: add             x0, x0, HEAP, lsl #32
    // 0xba87c0: LoadField: r1 = r4->field_b
    //     0xba87c0: ldur            w1, [x4, #0xb]
    // 0xba87c4: r6 = LoadInt32Instr(r0)
    //     0xba87c4: sbfx            x6, x0, #1, #0x1f
    //     0xba87c8: tbz             w0, #0, #0xba87d0
    //     0xba87cc: ldur            x6, [x0, #7]
    // 0xba87d0: r0 = LoadInt32Instr(r1)
    //     0xba87d0: sbfx            x0, x1, #1, #0x1f
    // 0xba87d4: mov             x1, x6
    // 0xba87d8: cmp             x1, x0
    // 0xba87dc: b.hs            #0xba9320
    // 0xba87e0: LoadField: r0 = r4->field_f
    //     0xba87e0: ldur            w0, [x4, #0xf]
    // 0xba87e4: DecompressPointer r0
    //     0xba87e4: add             x0, x0, HEAP, lsl #32
    // 0xba87e8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba87e8: add             x16, x0, x6, lsl #2
    //     0xba87ec: ldur            w1, [x16, #0xf]
    // 0xba87f0: DecompressPointer r1
    //     0xba87f0: add             x1, x1, HEAP, lsl #32
    // 0xba87f4: LoadField: r0 = r1->field_f
    //     0xba87f4: ldur            w0, [x1, #0xf]
    // 0xba87f8: DecompressPointer r0
    //     0xba87f8: add             x0, x0, HEAP, lsl #32
    // 0xba87fc: r1 = LoadClassIdInstr(r0)
    //     0xba87fc: ldur            x1, [x0, #-1]
    //     0xba8800: ubfx            x1, x1, #0xc, #0x14
    // 0xba8804: r16 = "sale_event_static_coupon"
    //     0xba8804: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xba8808: ldr             x16, [x16, #0x610]
    // 0xba880c: stp             x16, x0, [SP]
    // 0xba8810: mov             x0, x1
    // 0xba8814: mov             lr, x0
    // 0xba8818: ldr             lr, [x21, lr, lsl #3]
    // 0xba881c: blr             lr
    // 0xba8820: tbnz            w0, #4, #0xba8834
    // 0xba8824: b               #0xba882c
    // 0xba8828: tbnz            w0, #4, #0xba8834
    // 0xba882c: r1 = Instance_Color
    //     0xba882c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba8830: b               #0xba8844
    // 0xba8834: r1 = Instance_Color
    //     0xba8834: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba8838: d0 = 0.400000
    //     0xba8838: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xba883c: r0 = withOpacity()
    //     0xba883c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba8840: mov             x1, x0
    // 0xba8844: ldur            x0, [fp, #-8]
    // 0xba8848: ldur            d0, [fp, #-0x68]
    // 0xba884c: ldur            x2, [fp, #-0x38]
    // 0xba8850: r16 = 14.000000
    //     0xba8850: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xba8854: ldr             x16, [x16, #0x1d8]
    // 0xba8858: stp             x1, x16, [SP]
    // 0xba885c: ldur            x1, [fp, #-0x48]
    // 0xba8860: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba8860: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba8864: ldr             x4, [x4, #0xaa0]
    // 0xba8868: r0 = copyWith()
    //     0xba8868: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba886c: stur            x0, [fp, #-0x48]
    // 0xba8870: r0 = Text()
    //     0xba8870: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba8874: mov             x1, x0
    // 0xba8878: ldur            x0, [fp, #-0x38]
    // 0xba887c: stur            x1, [fp, #-0x50]
    // 0xba8880: StoreField: r1->field_b = r0
    //     0xba8880: stur            w0, [x1, #0xb]
    // 0xba8884: ldur            x0, [fp, #-0x48]
    // 0xba8888: StoreField: r1->field_13 = r0
    //     0xba8888: stur            w0, [x1, #0x13]
    // 0xba888c: r2 = 4
    //     0xba888c: movz            x2, #0x4
    // 0xba8890: StoreField: r1->field_37 = r2
    //     0xba8890: stur            w2, [x1, #0x37]
    // 0xba8894: r0 = Padding()
    //     0xba8894: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba8898: mov             x1, x0
    // 0xba889c: r0 = Instance_EdgeInsets
    //     0xba889c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba88a0: ldr             x0, [x0, #0x980]
    // 0xba88a4: stur            x1, [fp, #-0x48]
    // 0xba88a8: StoreField: r1->field_f = r0
    //     0xba88a8: stur            w0, [x1, #0xf]
    // 0xba88ac: ldur            x2, [fp, #-0x50]
    // 0xba88b0: StoreField: r1->field_b = r2
    //     0xba88b0: stur            w2, [x1, #0xb]
    // 0xba88b4: ldur            d0, [fp, #-0x68]
    // 0xba88b8: r2 = inline_Allocate_Double()
    //     0xba88b8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xba88bc: add             x2, x2, #0x10
    //     0xba88c0: cmp             x3, x2
    //     0xba88c4: b.ls            #0xba9324
    //     0xba88c8: str             x2, [THR, #0x50]  ; THR::top
    //     0xba88cc: sub             x2, x2, #0xf
    //     0xba88d0: movz            x3, #0xe15c
    //     0xba88d4: movk            x3, #0x3, lsl #16
    //     0xba88d8: stur            x3, [x2, #-1]
    // 0xba88dc: StoreField: r2->field_7 = d0
    //     0xba88dc: stur            d0, [x2, #7]
    // 0xba88e0: stur            x2, [fp, #-0x38]
    // 0xba88e4: r0 = SizedBox()
    //     0xba88e4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xba88e8: mov             x2, x0
    // 0xba88ec: ldur            x0, [fp, #-0x38]
    // 0xba88f0: stur            x2, [fp, #-0x50]
    // 0xba88f4: StoreField: r2->field_f = r0
    //     0xba88f4: stur            w0, [x2, #0xf]
    // 0xba88f8: ldur            x0, [fp, #-0x48]
    // 0xba88fc: StoreField: r2->field_b = r0
    //     0xba88fc: stur            w0, [x2, #0xb]
    // 0xba8900: ldur            x3, [fp, #-8]
    // 0xba8904: LoadField: r0 = r3->field_f
    //     0xba8904: ldur            w0, [x3, #0xf]
    // 0xba8908: DecompressPointer r0
    //     0xba8908: add             x0, x0, HEAP, lsl #32
    // 0xba890c: LoadField: r1 = r0->field_b
    //     0xba890c: ldur            w1, [x0, #0xb]
    // 0xba8910: DecompressPointer r1
    //     0xba8910: add             x1, x1, HEAP, lsl #32
    // 0xba8914: cmp             w1, NULL
    // 0xba8918: b.eq            #0xba9340
    // 0xba891c: LoadField: r0 = r1->field_f
    //     0xba891c: ldur            w0, [x1, #0xf]
    // 0xba8920: DecompressPointer r0
    //     0xba8920: add             x0, x0, HEAP, lsl #32
    // 0xba8924: cmp             w0, NULL
    // 0xba8928: b.ne            #0xba8938
    // 0xba892c: ldur            x4, [fp, #-0x10]
    // 0xba8930: r0 = Null
    //     0xba8930: mov             x0, NULL
    // 0xba8934: b               #0xba8988
    // 0xba8938: ldur            x4, [fp, #-0x10]
    // 0xba893c: LoadField: r5 = r0->field_f
    //     0xba893c: ldur            w5, [x0, #0xf]
    // 0xba8940: DecompressPointer r5
    //     0xba8940: add             x5, x5, HEAP, lsl #32
    // 0xba8944: LoadField: r0 = r4->field_13
    //     0xba8944: ldur            w0, [x4, #0x13]
    // 0xba8948: DecompressPointer r0
    //     0xba8948: add             x0, x0, HEAP, lsl #32
    // 0xba894c: LoadField: r1 = r5->field_b
    //     0xba894c: ldur            w1, [x5, #0xb]
    // 0xba8950: r6 = LoadInt32Instr(r0)
    //     0xba8950: sbfx            x6, x0, #1, #0x1f
    //     0xba8954: tbz             w0, #0, #0xba895c
    //     0xba8958: ldur            x6, [x0, #7]
    // 0xba895c: r0 = LoadInt32Instr(r1)
    //     0xba895c: sbfx            x0, x1, #1, #0x1f
    // 0xba8960: mov             x1, x6
    // 0xba8964: cmp             x1, x0
    // 0xba8968: b.hs            #0xba9344
    // 0xba896c: LoadField: r0 = r5->field_f
    //     0xba896c: ldur            w0, [x5, #0xf]
    // 0xba8970: DecompressPointer r0
    //     0xba8970: add             x0, x0, HEAP, lsl #32
    // 0xba8974: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba8974: add             x16, x0, x6, lsl #2
    //     0xba8978: ldur            w1, [x16, #0xf]
    // 0xba897c: DecompressPointer r1
    //     0xba897c: add             x1, x1, HEAP, lsl #32
    // 0xba8980: LoadField: r0 = r1->field_f
    //     0xba8980: ldur            w0, [x1, #0xf]
    // 0xba8984: DecompressPointer r0
    //     0xba8984: add             x0, x0, HEAP, lsl #32
    // 0xba8988: r1 = LoadClassIdInstr(r0)
    //     0xba8988: ldur            x1, [x0, #-1]
    //     0xba898c: ubfx            x1, x1, #0xc, #0x14
    // 0xba8990: r16 = "sale_event_static_coupon"
    //     0xba8990: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xba8994: ldr             x16, [x16, #0x610]
    // 0xba8998: stp             x16, x0, [SP]
    // 0xba899c: mov             x0, x1
    // 0xba89a0: mov             lr, x0
    // 0xba89a4: ldr             lr, [x21, lr, lsl #3]
    // 0xba89a8: blr             lr
    // 0xba89ac: eor             x2, x0, #0x10
    // 0xba89b0: ldur            x0, [fp, #-0x10]
    // 0xba89b4: stur            x2, [fp, #-0x38]
    // 0xba89b8: LoadField: r1 = r0->field_f
    //     0xba89b8: ldur            w1, [x0, #0xf]
    // 0xba89bc: DecompressPointer r1
    //     0xba89bc: add             x1, x1, HEAP, lsl #32
    // 0xba89c0: r0 = of()
    //     0xba89c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba89c4: LoadField: r1 = r0->field_87
    //     0xba89c4: ldur            w1, [x0, #0x87]
    // 0xba89c8: DecompressPointer r1
    //     0xba89c8: add             x1, x1, HEAP, lsl #32
    // 0xba89cc: LoadField: r0 = r1->field_2b
    //     0xba89cc: ldur            w0, [x1, #0x2b]
    // 0xba89d0: DecompressPointer r0
    //     0xba89d0: add             x0, x0, HEAP, lsl #32
    // 0xba89d4: r16 = 12.000000
    //     0xba89d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba89d8: ldr             x16, [x16, #0x9e8]
    // 0xba89dc: r30 = Instance_Color
    //     0xba89dc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba89e0: ldr             lr, [lr, #0x858]
    // 0xba89e4: stp             lr, x16, [SP]
    // 0xba89e8: mov             x1, x0
    // 0xba89ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba89ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba89f0: ldr             x4, [x4, #0xaa0]
    // 0xba89f4: r0 = copyWith()
    //     0xba89f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba89f8: stur            x0, [fp, #-0x48]
    // 0xba89fc: r0 = TextSpan()
    //     0xba89fc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba8a00: mov             x2, x0
    // 0xba8a04: r0 = "Know "
    //     0xba8a04: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xba8a08: ldr             x0, [x0, #0x5d0]
    // 0xba8a0c: stur            x2, [fp, #-0x58]
    // 0xba8a10: StoreField: r2->field_b = r0
    //     0xba8a10: stur            w0, [x2, #0xb]
    // 0xba8a14: r0 = Instance__DeferringMouseCursor
    //     0xba8a14: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba8a18: ArrayStore: r2[0] = r0  ; List_4
    //     0xba8a18: stur            w0, [x2, #0x17]
    // 0xba8a1c: ldur            x1, [fp, #-0x48]
    // 0xba8a20: StoreField: r2->field_7 = r1
    //     0xba8a20: stur            w1, [x2, #7]
    // 0xba8a24: ldur            x3, [fp, #-0x10]
    // 0xba8a28: LoadField: r1 = r3->field_f
    //     0xba8a28: ldur            w1, [x3, #0xf]
    // 0xba8a2c: DecompressPointer r1
    //     0xba8a2c: add             x1, x1, HEAP, lsl #32
    // 0xba8a30: r0 = of()
    //     0xba8a30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba8a34: LoadField: r1 = r0->field_87
    //     0xba8a34: ldur            w1, [x0, #0x87]
    // 0xba8a38: DecompressPointer r1
    //     0xba8a38: add             x1, x1, HEAP, lsl #32
    // 0xba8a3c: LoadField: r0 = r1->field_2b
    //     0xba8a3c: ldur            w0, [x1, #0x2b]
    // 0xba8a40: DecompressPointer r0
    //     0xba8a40: add             x0, x0, HEAP, lsl #32
    // 0xba8a44: r16 = Instance_Color
    //     0xba8a44: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba8a48: ldr             x16, [x16, #0x858]
    // 0xba8a4c: r30 = 12.000000
    //     0xba8a4c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba8a50: ldr             lr, [lr, #0x9e8]
    // 0xba8a54: stp             lr, x16, [SP]
    // 0xba8a58: mov             x1, x0
    // 0xba8a5c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba8a5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba8a60: ldr             x4, [x4, #0x9b8]
    // 0xba8a64: r0 = copyWith()
    //     0xba8a64: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba8a68: stur            x0, [fp, #-0x48]
    // 0xba8a6c: r0 = TextSpan()
    //     0xba8a6c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba8a70: mov             x3, x0
    // 0xba8a74: r0 = "More"
    //     0xba8a74: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xba8a78: ldr             x0, [x0, #0x5d8]
    // 0xba8a7c: stur            x3, [fp, #-0x60]
    // 0xba8a80: StoreField: r3->field_b = r0
    //     0xba8a80: stur            w0, [x3, #0xb]
    // 0xba8a84: r0 = Instance__DeferringMouseCursor
    //     0xba8a84: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba8a88: ArrayStore: r3[0] = r0  ; List_4
    //     0xba8a88: stur            w0, [x3, #0x17]
    // 0xba8a8c: ldur            x1, [fp, #-0x48]
    // 0xba8a90: StoreField: r3->field_7 = r1
    //     0xba8a90: stur            w1, [x3, #7]
    // 0xba8a94: r1 = Null
    //     0xba8a94: mov             x1, NULL
    // 0xba8a98: r2 = 4
    //     0xba8a98: movz            x2, #0x4
    // 0xba8a9c: r0 = AllocateArray()
    //     0xba8a9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba8aa0: mov             x2, x0
    // 0xba8aa4: ldur            x0, [fp, #-0x58]
    // 0xba8aa8: stur            x2, [fp, #-0x48]
    // 0xba8aac: StoreField: r2->field_f = r0
    //     0xba8aac: stur            w0, [x2, #0xf]
    // 0xba8ab0: ldur            x0, [fp, #-0x60]
    // 0xba8ab4: StoreField: r2->field_13 = r0
    //     0xba8ab4: stur            w0, [x2, #0x13]
    // 0xba8ab8: r1 = <TextSpan>
    //     0xba8ab8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xba8abc: ldr             x1, [x1, #0x940]
    // 0xba8ac0: r0 = AllocateGrowableArray()
    //     0xba8ac0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba8ac4: mov             x1, x0
    // 0xba8ac8: ldur            x0, [fp, #-0x48]
    // 0xba8acc: stur            x1, [fp, #-0x58]
    // 0xba8ad0: StoreField: r1->field_f = r0
    //     0xba8ad0: stur            w0, [x1, #0xf]
    // 0xba8ad4: r2 = 4
    //     0xba8ad4: movz            x2, #0x4
    // 0xba8ad8: StoreField: r1->field_b = r2
    //     0xba8ad8: stur            w2, [x1, #0xb]
    // 0xba8adc: r0 = TextSpan()
    //     0xba8adc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba8ae0: mov             x1, x0
    // 0xba8ae4: ldur            x0, [fp, #-0x58]
    // 0xba8ae8: stur            x1, [fp, #-0x48]
    // 0xba8aec: StoreField: r1->field_f = r0
    //     0xba8aec: stur            w0, [x1, #0xf]
    // 0xba8af0: r0 = Instance__DeferringMouseCursor
    //     0xba8af0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba8af4: ArrayStore: r1[0] = r0  ; List_4
    //     0xba8af4: stur            w0, [x1, #0x17]
    // 0xba8af8: r0 = RichText()
    //     0xba8af8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba8afc: mov             x1, x0
    // 0xba8b00: ldur            x2, [fp, #-0x48]
    // 0xba8b04: stur            x0, [fp, #-0x48]
    // 0xba8b08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba8b08: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba8b0c: r0 = RichText()
    //     0xba8b0c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba8b10: r0 = Padding()
    //     0xba8b10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba8b14: mov             x1, x0
    // 0xba8b18: r0 = Instance_EdgeInsets
    //     0xba8b18: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xba8b1c: ldr             x0, [x0, #0xc40]
    // 0xba8b20: stur            x1, [fp, #-0x58]
    // 0xba8b24: StoreField: r1->field_f = r0
    //     0xba8b24: stur            w0, [x1, #0xf]
    // 0xba8b28: ldur            x0, [fp, #-0x48]
    // 0xba8b2c: StoreField: r1->field_b = r0
    //     0xba8b2c: stur            w0, [x1, #0xb]
    // 0xba8b30: r0 = Visibility()
    //     0xba8b30: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xba8b34: mov             x1, x0
    // 0xba8b38: ldur            x0, [fp, #-0x58]
    // 0xba8b3c: stur            x1, [fp, #-0x48]
    // 0xba8b40: StoreField: r1->field_b = r0
    //     0xba8b40: stur            w0, [x1, #0xb]
    // 0xba8b44: r0 = Instance_SizedBox
    //     0xba8b44: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xba8b48: StoreField: r1->field_f = r0
    //     0xba8b48: stur            w0, [x1, #0xf]
    // 0xba8b4c: ldur            x2, [fp, #-0x38]
    // 0xba8b50: StoreField: r1->field_13 = r2
    //     0xba8b50: stur            w2, [x1, #0x13]
    // 0xba8b54: r2 = false
    //     0xba8b54: add             x2, NULL, #0x30  ; false
    // 0xba8b58: ArrayStore: r1[0] = r2  ; List_4
    //     0xba8b58: stur            w2, [x1, #0x17]
    // 0xba8b5c: StoreField: r1->field_1b = r2
    //     0xba8b5c: stur            w2, [x1, #0x1b]
    // 0xba8b60: StoreField: r1->field_1f = r2
    //     0xba8b60: stur            w2, [x1, #0x1f]
    // 0xba8b64: StoreField: r1->field_23 = r2
    //     0xba8b64: stur            w2, [x1, #0x23]
    // 0xba8b68: StoreField: r1->field_27 = r2
    //     0xba8b68: stur            w2, [x1, #0x27]
    // 0xba8b6c: StoreField: r1->field_2b = r2
    //     0xba8b6c: stur            w2, [x1, #0x2b]
    // 0xba8b70: r0 = InkWell()
    //     0xba8b70: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xba8b74: mov             x3, x0
    // 0xba8b78: ldur            x0, [fp, #-0x48]
    // 0xba8b7c: stur            x3, [fp, #-0x38]
    // 0xba8b80: StoreField: r3->field_b = r0
    //     0xba8b80: stur            w0, [x3, #0xb]
    // 0xba8b84: ldur            x2, [fp, #-0x10]
    // 0xba8b88: r1 = Function '<anonymous closure>':.
    //     0xba8b88: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a620] AnonymousClosure: (0xba96c4), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba8b8c: ldr             x1, [x1, #0x620]
    // 0xba8b90: r0 = AllocateClosure()
    //     0xba8b90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba8b94: mov             x1, x0
    // 0xba8b98: ldur            x0, [fp, #-0x38]
    // 0xba8b9c: StoreField: r0->field_f = r1
    //     0xba8b9c: stur            w1, [x0, #0xf]
    // 0xba8ba0: r2 = true
    //     0xba8ba0: add             x2, NULL, #0x20  ; true
    // 0xba8ba4: StoreField: r0->field_43 = r2
    //     0xba8ba4: stur            w2, [x0, #0x43]
    // 0xba8ba8: r3 = Instance_BoxShape
    //     0xba8ba8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba8bac: ldr             x3, [x3, #0x80]
    // 0xba8bb0: StoreField: r0->field_47 = r3
    //     0xba8bb0: stur            w3, [x0, #0x47]
    // 0xba8bb4: StoreField: r0->field_6f = r2
    //     0xba8bb4: stur            w2, [x0, #0x6f]
    // 0xba8bb8: r4 = false
    //     0xba8bb8: add             x4, NULL, #0x30  ; false
    // 0xba8bbc: StoreField: r0->field_73 = r4
    //     0xba8bbc: stur            w4, [x0, #0x73]
    // 0xba8bc0: StoreField: r0->field_83 = r2
    //     0xba8bc0: stur            w2, [x0, #0x83]
    // 0xba8bc4: StoreField: r0->field_7b = r4
    //     0xba8bc4: stur            w4, [x0, #0x7b]
    // 0xba8bc8: r1 = <FlexParentData>
    //     0xba8bc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba8bcc: ldr             x1, [x1, #0xe00]
    // 0xba8bd0: r0 = Expanded()
    //     0xba8bd0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba8bd4: stur            x0, [fp, #-0x48]
    // 0xba8bd8: StoreField: r0->field_13 = rZR
    //     0xba8bd8: stur            xzr, [x0, #0x13]
    // 0xba8bdc: r3 = Instance_FlexFit
    //     0xba8bdc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba8be0: ldr             x3, [x3, #0xe08]
    // 0xba8be4: StoreField: r0->field_1b = r3
    //     0xba8be4: stur            w3, [x0, #0x1b]
    // 0xba8be8: ldur            x1, [fp, #-0x38]
    // 0xba8bec: StoreField: r0->field_b = r1
    //     0xba8bec: stur            w1, [x0, #0xb]
    // 0xba8bf0: r1 = Null
    //     0xba8bf0: mov             x1, NULL
    // 0xba8bf4: r2 = 4
    //     0xba8bf4: movz            x2, #0x4
    // 0xba8bf8: r0 = AllocateArray()
    //     0xba8bf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba8bfc: mov             x2, x0
    // 0xba8c00: ldur            x0, [fp, #-0x50]
    // 0xba8c04: stur            x2, [fp, #-0x38]
    // 0xba8c08: StoreField: r2->field_f = r0
    //     0xba8c08: stur            w0, [x2, #0xf]
    // 0xba8c0c: ldur            x0, [fp, #-0x48]
    // 0xba8c10: StoreField: r2->field_13 = r0
    //     0xba8c10: stur            w0, [x2, #0x13]
    // 0xba8c14: r1 = <Widget>
    //     0xba8c14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba8c18: r0 = AllocateGrowableArray()
    //     0xba8c18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba8c1c: mov             x1, x0
    // 0xba8c20: ldur            x0, [fp, #-0x38]
    // 0xba8c24: stur            x1, [fp, #-0x48]
    // 0xba8c28: StoreField: r1->field_f = r0
    //     0xba8c28: stur            w0, [x1, #0xf]
    // 0xba8c2c: r0 = 4
    //     0xba8c2c: movz            x0, #0x4
    // 0xba8c30: StoreField: r1->field_b = r0
    //     0xba8c30: stur            w0, [x1, #0xb]
    // 0xba8c34: r0 = Column()
    //     0xba8c34: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xba8c38: mov             x1, x0
    // 0xba8c3c: r0 = Instance_Axis
    //     0xba8c3c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xba8c40: stur            x1, [fp, #-0x38]
    // 0xba8c44: StoreField: r1->field_f = r0
    //     0xba8c44: stur            w0, [x1, #0xf]
    // 0xba8c48: r2 = Instance_MainAxisAlignment
    //     0xba8c48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba8c4c: ldr             x2, [x2, #0xa08]
    // 0xba8c50: StoreField: r1->field_13 = r2
    //     0xba8c50: stur            w2, [x1, #0x13]
    // 0xba8c54: r0 = Instance_MainAxisSize
    //     0xba8c54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xba8c58: ldr             x0, [x0, #0xdd0]
    // 0xba8c5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xba8c5c: stur            w0, [x1, #0x17]
    // 0xba8c60: r0 = Instance_CrossAxisAlignment
    //     0xba8c60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xba8c64: ldr             x0, [x0, #0x890]
    // 0xba8c68: StoreField: r1->field_1b = r0
    //     0xba8c68: stur            w0, [x1, #0x1b]
    // 0xba8c6c: r3 = Instance_VerticalDirection
    //     0xba8c6c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba8c70: ldr             x3, [x3, #0xa20]
    // 0xba8c74: StoreField: r1->field_23 = r3
    //     0xba8c74: stur            w3, [x1, #0x23]
    // 0xba8c78: r4 = Instance_Clip
    //     0xba8c78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba8c7c: ldr             x4, [x4, #0x38]
    // 0xba8c80: StoreField: r1->field_2b = r4
    //     0xba8c80: stur            w4, [x1, #0x2b]
    // 0xba8c84: StoreField: r1->field_2f = rZR
    //     0xba8c84: stur            xzr, [x1, #0x2f]
    // 0xba8c88: ldur            x0, [fp, #-0x48]
    // 0xba8c8c: StoreField: r1->field_b = r0
    //     0xba8c8c: stur            w0, [x1, #0xb]
    // 0xba8c90: ldur            x5, [fp, #-8]
    // 0xba8c94: LoadField: r0 = r5->field_f
    //     0xba8c94: ldur            w0, [x5, #0xf]
    // 0xba8c98: DecompressPointer r0
    //     0xba8c98: add             x0, x0, HEAP, lsl #32
    // 0xba8c9c: LoadField: r6 = r0->field_b
    //     0xba8c9c: ldur            w6, [x0, #0xb]
    // 0xba8ca0: DecompressPointer r6
    //     0xba8ca0: add             x6, x6, HEAP, lsl #32
    // 0xba8ca4: cmp             w6, NULL
    // 0xba8ca8: b.eq            #0xba9348
    // 0xba8cac: LoadField: r0 = r6->field_b
    //     0xba8cac: ldur            w0, [x6, #0xb]
    // 0xba8cb0: DecompressPointer r0
    //     0xba8cb0: add             x0, x0, HEAP, lsl #32
    // 0xba8cb4: r6 = LoadClassIdInstr(r0)
    //     0xba8cb4: ldur            x6, [x0, #-1]
    //     0xba8cb8: ubfx            x6, x6, #0xc, #0x14
    // 0xba8cbc: r16 = "checkout_offers"
    //     0xba8cbc: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xba8cc0: ldr             x16, [x16, #0x1c8]
    // 0xba8cc4: stp             x16, x0, [SP]
    // 0xba8cc8: mov             x0, x6
    // 0xba8ccc: mov             lr, x0
    // 0xba8cd0: ldr             lr, [x21, lr, lsl #3]
    // 0xba8cd4: blr             lr
    // 0xba8cd8: tbz             w0, #4, #0xba8da8
    // 0xba8cdc: ldur            x2, [fp, #-8]
    // 0xba8ce0: LoadField: r0 = r2->field_f
    //     0xba8ce0: ldur            w0, [x2, #0xf]
    // 0xba8ce4: DecompressPointer r0
    //     0xba8ce4: add             x0, x0, HEAP, lsl #32
    // 0xba8ce8: LoadField: r1 = r0->field_b
    //     0xba8ce8: ldur            w1, [x0, #0xb]
    // 0xba8cec: DecompressPointer r1
    //     0xba8cec: add             x1, x1, HEAP, lsl #32
    // 0xba8cf0: cmp             w1, NULL
    // 0xba8cf4: b.eq            #0xba934c
    // 0xba8cf8: LoadField: r0 = r1->field_1f
    //     0xba8cf8: ldur            w0, [x1, #0x1f]
    // 0xba8cfc: DecompressPointer r0
    //     0xba8cfc: add             x0, x0, HEAP, lsl #32
    // 0xba8d00: tbnz            w0, #4, #0xba8d0c
    // 0xba8d04: r3 = true
    //     0xba8d04: add             x3, NULL, #0x20  ; true
    // 0xba8d08: b               #0xba8db0
    // 0xba8d0c: LoadField: r0 = r1->field_f
    //     0xba8d0c: ldur            w0, [x1, #0xf]
    // 0xba8d10: DecompressPointer r0
    //     0xba8d10: add             x0, x0, HEAP, lsl #32
    // 0xba8d14: cmp             w0, NULL
    // 0xba8d18: b.ne            #0xba8d28
    // 0xba8d1c: ldur            x3, [fp, #-0x10]
    // 0xba8d20: r0 = Null
    //     0xba8d20: mov             x0, NULL
    // 0xba8d24: b               #0xba8d78
    // 0xba8d28: ldur            x3, [fp, #-0x10]
    // 0xba8d2c: LoadField: r4 = r0->field_f
    //     0xba8d2c: ldur            w4, [x0, #0xf]
    // 0xba8d30: DecompressPointer r4
    //     0xba8d30: add             x4, x4, HEAP, lsl #32
    // 0xba8d34: LoadField: r0 = r3->field_13
    //     0xba8d34: ldur            w0, [x3, #0x13]
    // 0xba8d38: DecompressPointer r0
    //     0xba8d38: add             x0, x0, HEAP, lsl #32
    // 0xba8d3c: LoadField: r1 = r4->field_b
    //     0xba8d3c: ldur            w1, [x4, #0xb]
    // 0xba8d40: r5 = LoadInt32Instr(r0)
    //     0xba8d40: sbfx            x5, x0, #1, #0x1f
    //     0xba8d44: tbz             w0, #0, #0xba8d4c
    //     0xba8d48: ldur            x5, [x0, #7]
    // 0xba8d4c: r0 = LoadInt32Instr(r1)
    //     0xba8d4c: sbfx            x0, x1, #1, #0x1f
    // 0xba8d50: mov             x1, x5
    // 0xba8d54: cmp             x1, x0
    // 0xba8d58: b.hs            #0xba9350
    // 0xba8d5c: LoadField: r0 = r4->field_f
    //     0xba8d5c: ldur            w0, [x4, #0xf]
    // 0xba8d60: DecompressPointer r0
    //     0xba8d60: add             x0, x0, HEAP, lsl #32
    // 0xba8d64: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba8d64: add             x16, x0, x5, lsl #2
    //     0xba8d68: ldur            w1, [x16, #0xf]
    // 0xba8d6c: DecompressPointer r1
    //     0xba8d6c: add             x1, x1, HEAP, lsl #32
    // 0xba8d70: LoadField: r0 = r1->field_f
    //     0xba8d70: ldur            w0, [x1, #0xf]
    // 0xba8d74: DecompressPointer r0
    //     0xba8d74: add             x0, x0, HEAP, lsl #32
    // 0xba8d78: r1 = LoadClassIdInstr(r0)
    //     0xba8d78: ldur            x1, [x0, #-1]
    //     0xba8d7c: ubfx            x1, x1, #0xc, #0x14
    // 0xba8d80: r16 = "sale_event_static_coupon"
    //     0xba8d80: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xba8d84: ldr             x16, [x16, #0x610]
    // 0xba8d88: stp             x16, x0, [SP]
    // 0xba8d8c: mov             x0, x1
    // 0xba8d90: mov             lr, x0
    // 0xba8d94: ldr             lr, [x21, lr, lsl #3]
    // 0xba8d98: blr             lr
    // 0xba8d9c: mov             x3, x0
    // 0xba8da0: ldur            x2, [fp, #-8]
    // 0xba8da4: b               #0xba8db0
    // 0xba8da8: ldur            x2, [fp, #-8]
    // 0xba8dac: r3 = false
    //     0xba8dac: add             x3, NULL, #0x30  ; false
    // 0xba8db0: stur            x3, [fp, #-0x48]
    // 0xba8db4: LoadField: r0 = r2->field_f
    //     0xba8db4: ldur            w0, [x2, #0xf]
    // 0xba8db8: DecompressPointer r0
    //     0xba8db8: add             x0, x0, HEAP, lsl #32
    // 0xba8dbc: LoadField: r4 = r0->field_b
    //     0xba8dbc: ldur            w4, [x0, #0xb]
    // 0xba8dc0: DecompressPointer r4
    //     0xba8dc0: add             x4, x4, HEAP, lsl #32
    // 0xba8dc4: cmp             w4, NULL
    // 0xba8dc8: b.eq            #0xba9354
    // 0xba8dcc: LoadField: r0 = r4->field_f
    //     0xba8dcc: ldur            w0, [x4, #0xf]
    // 0xba8dd0: DecompressPointer r0
    //     0xba8dd0: add             x0, x0, HEAP, lsl #32
    // 0xba8dd4: cmp             w0, NULL
    // 0xba8dd8: b.ne            #0xba8de8
    // 0xba8ddc: ldur            x5, [fp, #-0x10]
    // 0xba8de0: r0 = Null
    //     0xba8de0: mov             x0, NULL
    // 0xba8de4: b               #0xba8e38
    // 0xba8de8: ldur            x5, [fp, #-0x10]
    // 0xba8dec: LoadField: r6 = r0->field_f
    //     0xba8dec: ldur            w6, [x0, #0xf]
    // 0xba8df0: DecompressPointer r6
    //     0xba8df0: add             x6, x6, HEAP, lsl #32
    // 0xba8df4: LoadField: r0 = r5->field_13
    //     0xba8df4: ldur            w0, [x5, #0x13]
    // 0xba8df8: DecompressPointer r0
    //     0xba8df8: add             x0, x0, HEAP, lsl #32
    // 0xba8dfc: LoadField: r1 = r6->field_b
    //     0xba8dfc: ldur            w1, [x6, #0xb]
    // 0xba8e00: r7 = LoadInt32Instr(r0)
    //     0xba8e00: sbfx            x7, x0, #1, #0x1f
    //     0xba8e04: tbz             w0, #0, #0xba8e0c
    //     0xba8e08: ldur            x7, [x0, #7]
    // 0xba8e0c: r0 = LoadInt32Instr(r1)
    //     0xba8e0c: sbfx            x0, x1, #1, #0x1f
    // 0xba8e10: mov             x1, x7
    // 0xba8e14: cmp             x1, x0
    // 0xba8e18: b.hs            #0xba9358
    // 0xba8e1c: LoadField: r0 = r6->field_f
    //     0xba8e1c: ldur            w0, [x6, #0xf]
    // 0xba8e20: DecompressPointer r0
    //     0xba8e20: add             x0, x0, HEAP, lsl #32
    // 0xba8e24: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xba8e24: add             x16, x0, x7, lsl #2
    //     0xba8e28: ldur            w1, [x16, #0xf]
    // 0xba8e2c: DecompressPointer r1
    //     0xba8e2c: add             x1, x1, HEAP, lsl #32
    // 0xba8e30: LoadField: r0 = r1->field_7
    //     0xba8e30: ldur            w0, [x1, #7]
    // 0xba8e34: DecompressPointer r0
    //     0xba8e34: add             x0, x0, HEAP, lsl #32
    // 0xba8e38: LoadField: r1 = r4->field_13
    //     0xba8e38: ldur            w1, [x4, #0x13]
    // 0xba8e3c: DecompressPointer r1
    //     0xba8e3c: add             x1, x1, HEAP, lsl #32
    // 0xba8e40: r4 = LoadClassIdInstr(r0)
    //     0xba8e40: ldur            x4, [x0, #-1]
    //     0xba8e44: ubfx            x4, x4, #0xc, #0x14
    // 0xba8e48: stp             x1, x0, [SP]
    // 0xba8e4c: mov             x0, x4
    // 0xba8e50: mov             lr, x0
    // 0xba8e54: ldr             lr, [x21, lr, lsl #3]
    // 0xba8e58: blr             lr
    // 0xba8e5c: tbz             w0, #4, #0xba8f10
    // 0xba8e60: ldur            x2, [fp, #-8]
    // 0xba8e64: LoadField: r0 = r2->field_f
    //     0xba8e64: ldur            w0, [x2, #0xf]
    // 0xba8e68: DecompressPointer r0
    //     0xba8e68: add             x0, x0, HEAP, lsl #32
    // 0xba8e6c: LoadField: r1 = r0->field_b
    //     0xba8e6c: ldur            w1, [x0, #0xb]
    // 0xba8e70: DecompressPointer r1
    //     0xba8e70: add             x1, x1, HEAP, lsl #32
    // 0xba8e74: cmp             w1, NULL
    // 0xba8e78: b.eq            #0xba935c
    // 0xba8e7c: LoadField: r0 = r1->field_f
    //     0xba8e7c: ldur            w0, [x1, #0xf]
    // 0xba8e80: DecompressPointer r0
    //     0xba8e80: add             x0, x0, HEAP, lsl #32
    // 0xba8e84: cmp             w0, NULL
    // 0xba8e88: b.ne            #0xba8e98
    // 0xba8e8c: ldur            x3, [fp, #-0x10]
    // 0xba8e90: r0 = Null
    //     0xba8e90: mov             x0, NULL
    // 0xba8e94: b               #0xba8ee8
    // 0xba8e98: ldur            x3, [fp, #-0x10]
    // 0xba8e9c: LoadField: r4 = r0->field_f
    //     0xba8e9c: ldur            w4, [x0, #0xf]
    // 0xba8ea0: DecompressPointer r4
    //     0xba8ea0: add             x4, x4, HEAP, lsl #32
    // 0xba8ea4: LoadField: r0 = r3->field_13
    //     0xba8ea4: ldur            w0, [x3, #0x13]
    // 0xba8ea8: DecompressPointer r0
    //     0xba8ea8: add             x0, x0, HEAP, lsl #32
    // 0xba8eac: LoadField: r1 = r4->field_b
    //     0xba8eac: ldur            w1, [x4, #0xb]
    // 0xba8eb0: r5 = LoadInt32Instr(r0)
    //     0xba8eb0: sbfx            x5, x0, #1, #0x1f
    //     0xba8eb4: tbz             w0, #0, #0xba8ebc
    //     0xba8eb8: ldur            x5, [x0, #7]
    // 0xba8ebc: r0 = LoadInt32Instr(r1)
    //     0xba8ebc: sbfx            x0, x1, #1, #0x1f
    // 0xba8ec0: mov             x1, x5
    // 0xba8ec4: cmp             x1, x0
    // 0xba8ec8: b.hs            #0xba9360
    // 0xba8ecc: LoadField: r0 = r4->field_f
    //     0xba8ecc: ldur            w0, [x4, #0xf]
    // 0xba8ed0: DecompressPointer r0
    //     0xba8ed0: add             x0, x0, HEAP, lsl #32
    // 0xba8ed4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba8ed4: add             x16, x0, x5, lsl #2
    //     0xba8ed8: ldur            w1, [x16, #0xf]
    // 0xba8edc: DecompressPointer r1
    //     0xba8edc: add             x1, x1, HEAP, lsl #32
    // 0xba8ee0: LoadField: r0 = r1->field_f
    //     0xba8ee0: ldur            w0, [x1, #0xf]
    // 0xba8ee4: DecompressPointer r0
    //     0xba8ee4: add             x0, x0, HEAP, lsl #32
    // 0xba8ee8: r1 = LoadClassIdInstr(r0)
    //     0xba8ee8: ldur            x1, [x0, #-1]
    //     0xba8eec: ubfx            x1, x1, #0xc, #0x14
    // 0xba8ef0: r16 = "sale_event_static_coupon"
    //     0xba8ef0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xba8ef4: ldr             x16, [x16, #0x610]
    // 0xba8ef8: stp             x16, x0, [SP]
    // 0xba8efc: mov             x0, x1
    // 0xba8f00: mov             lr, x0
    // 0xba8f04: ldr             lr, [x21, lr, lsl #3]
    // 0xba8f08: blr             lr
    // 0xba8f0c: tbnz            w0, #4, #0xba8f84
    // 0xba8f10: ldur            x2, [fp, #-0x10]
    // 0xba8f14: LoadField: r1 = r2->field_f
    //     0xba8f14: ldur            w1, [x2, #0xf]
    // 0xba8f18: DecompressPointer r1
    //     0xba8f18: add             x1, x1, HEAP, lsl #32
    // 0xba8f1c: r0 = of()
    //     0xba8f1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba8f20: LoadField: r1 = r0->field_87
    //     0xba8f20: ldur            w1, [x0, #0x87]
    // 0xba8f24: DecompressPointer r1
    //     0xba8f24: add             x1, x1, HEAP, lsl #32
    // 0xba8f28: LoadField: r0 = r1->field_7
    //     0xba8f28: ldur            w0, [x1, #7]
    // 0xba8f2c: DecompressPointer r0
    //     0xba8f2c: add             x0, x0, HEAP, lsl #32
    // 0xba8f30: r16 = Instance_Color
    //     0xba8f30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xba8f34: ldr             x16, [x16, #0x858]
    // 0xba8f38: r30 = 12.000000
    //     0xba8f38: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba8f3c: ldr             lr, [lr, #0x9e8]
    // 0xba8f40: stp             lr, x16, [SP]
    // 0xba8f44: mov             x1, x0
    // 0xba8f48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xba8f48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xba8f4c: ldr             x4, [x4, #0x9b8]
    // 0xba8f50: r0 = copyWith()
    //     0xba8f50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba8f54: stur            x0, [fp, #-0x50]
    // 0xba8f58: r0 = Text()
    //     0xba8f58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba8f5c: mov             x1, x0
    // 0xba8f60: r0 = "APPLIED"
    //     0xba8f60: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xba8f64: ldr             x0, [x0, #0x5e8]
    // 0xba8f68: StoreField: r1->field_b = r0
    //     0xba8f68: stur            w0, [x1, #0xb]
    // 0xba8f6c: ldur            x0, [fp, #-0x50]
    // 0xba8f70: StoreField: r1->field_13 = r0
    //     0xba8f70: stur            w0, [x1, #0x13]
    // 0xba8f74: r0 = Instance_TextAlign
    //     0xba8f74: ldr             x0, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xba8f78: StoreField: r1->field_1b = r0
    //     0xba8f78: stur            w0, [x1, #0x1b]
    // 0xba8f7c: mov             x3, x1
    // 0xba8f80: b               #0xba9098
    // 0xba8f84: ldur            x3, [fp, #-8]
    // 0xba8f88: ldur            x2, [fp, #-0x10]
    // 0xba8f8c: r0 = Instance_TextAlign
    //     0xba8f8c: ldr             x0, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xba8f90: LoadField: r1 = r2->field_f
    //     0xba8f90: ldur            w1, [x2, #0xf]
    // 0xba8f94: DecompressPointer r1
    //     0xba8f94: add             x1, x1, HEAP, lsl #32
    // 0xba8f98: r0 = of()
    //     0xba8f98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba8f9c: LoadField: r1 = r0->field_87
    //     0xba8f9c: ldur            w1, [x0, #0x87]
    // 0xba8fa0: DecompressPointer r1
    //     0xba8fa0: add             x1, x1, HEAP, lsl #32
    // 0xba8fa4: LoadField: r2 = r1->field_7
    //     0xba8fa4: ldur            w2, [x1, #7]
    // 0xba8fa8: DecompressPointer r2
    //     0xba8fa8: add             x2, x2, HEAP, lsl #32
    // 0xba8fac: ldur            x0, [fp, #-8]
    // 0xba8fb0: LoadField: r1 = r0->field_f
    //     0xba8fb0: ldur            w1, [x0, #0xf]
    // 0xba8fb4: DecompressPointer r1
    //     0xba8fb4: add             x1, x1, HEAP, lsl #32
    // 0xba8fb8: LoadField: r0 = r1->field_b
    //     0xba8fb8: ldur            w0, [x1, #0xb]
    // 0xba8fbc: DecompressPointer r0
    //     0xba8fbc: add             x0, x0, HEAP, lsl #32
    // 0xba8fc0: cmp             w0, NULL
    // 0xba8fc4: b.eq            #0xba9364
    // 0xba8fc8: LoadField: r1 = r0->field_f
    //     0xba8fc8: ldur            w1, [x0, #0xf]
    // 0xba8fcc: DecompressPointer r1
    //     0xba8fcc: add             x1, x1, HEAP, lsl #32
    // 0xba8fd0: cmp             w1, NULL
    // 0xba8fd4: b.ne            #0xba8fe4
    // 0xba8fd8: ldur            x3, [fp, #-0x10]
    // 0xba8fdc: r0 = Null
    //     0xba8fdc: mov             x0, NULL
    // 0xba8fe0: b               #0xba9034
    // 0xba8fe4: ldur            x3, [fp, #-0x10]
    // 0xba8fe8: LoadField: r4 = r1->field_f
    //     0xba8fe8: ldur            w4, [x1, #0xf]
    // 0xba8fec: DecompressPointer r4
    //     0xba8fec: add             x4, x4, HEAP, lsl #32
    // 0xba8ff0: LoadField: r0 = r3->field_13
    //     0xba8ff0: ldur            w0, [x3, #0x13]
    // 0xba8ff4: DecompressPointer r0
    //     0xba8ff4: add             x0, x0, HEAP, lsl #32
    // 0xba8ff8: LoadField: r1 = r4->field_b
    //     0xba8ff8: ldur            w1, [x4, #0xb]
    // 0xba8ffc: r5 = LoadInt32Instr(r0)
    //     0xba8ffc: sbfx            x5, x0, #1, #0x1f
    //     0xba9000: tbz             w0, #0, #0xba9008
    //     0xba9004: ldur            x5, [x0, #7]
    // 0xba9008: r0 = LoadInt32Instr(r1)
    //     0xba9008: sbfx            x0, x1, #1, #0x1f
    // 0xba900c: mov             x1, x5
    // 0xba9010: cmp             x1, x0
    // 0xba9014: b.hs            #0xba9368
    // 0xba9018: LoadField: r0 = r4->field_f
    //     0xba9018: ldur            w0, [x4, #0xf]
    // 0xba901c: DecompressPointer r0
    //     0xba901c: add             x0, x0, HEAP, lsl #32
    // 0xba9020: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba9020: add             x16, x0, x5, lsl #2
    //     0xba9024: ldur            w1, [x16, #0xf]
    // 0xba9028: DecompressPointer r1
    //     0xba9028: add             x1, x1, HEAP, lsl #32
    // 0xba902c: LoadField: r0 = r1->field_27
    //     0xba902c: ldur            w0, [x1, #0x27]
    // 0xba9030: DecompressPointer r0
    //     0xba9030: add             x0, x0, HEAP, lsl #32
    // 0xba9034: cmp             w0, NULL
    // 0xba9038: b.eq            #0xba9048
    // 0xba903c: tbnz            w0, #4, #0xba9048
    // 0xba9040: r0 = Instance_Color
    //     0xba9040: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba9044: b               #0xba9050
    // 0xba9048: r0 = Instance_MaterialColor
    //     0xba9048: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xba904c: ldr             x0, [x0, #0xdc0]
    // 0xba9050: r16 = 12.000000
    //     0xba9050: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba9054: ldr             x16, [x16, #0x9e8]
    // 0xba9058: stp             x0, x16, [SP]
    // 0xba905c: mov             x1, x2
    // 0xba9060: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba9060: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba9064: ldr             x4, [x4, #0xaa0]
    // 0xba9068: r0 = copyWith()
    //     0xba9068: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba906c: stur            x0, [fp, #-8]
    // 0xba9070: r0 = Text()
    //     0xba9070: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba9074: mov             x1, x0
    // 0xba9078: r0 = "APPLY"
    //     0xba9078: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xba907c: ldr             x0, [x0, #0x5f0]
    // 0xba9080: StoreField: r1->field_b = r0
    //     0xba9080: stur            w0, [x1, #0xb]
    // 0xba9084: ldur            x0, [fp, #-8]
    // 0xba9088: StoreField: r1->field_13 = r0
    //     0xba9088: stur            w0, [x1, #0x13]
    // 0xba908c: r0 = Instance_TextAlign
    //     0xba908c: ldr             x0, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xba9090: StoreField: r1->field_1b = r0
    //     0xba9090: stur            w0, [x1, #0x1b]
    // 0xba9094: mov             x3, x1
    // 0xba9098: ldur            x2, [fp, #-0x40]
    // 0xba909c: ldur            x1, [fp, #-0x38]
    // 0xba90a0: ldur            x0, [fp, #-0x48]
    // 0xba90a4: stur            x3, [fp, #-8]
    // 0xba90a8: r0 = InkWell()
    //     0xba90a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xba90ac: mov             x3, x0
    // 0xba90b0: ldur            x0, [fp, #-8]
    // 0xba90b4: stur            x3, [fp, #-0x50]
    // 0xba90b8: StoreField: r3->field_b = r0
    //     0xba90b8: stur            w0, [x3, #0xb]
    // 0xba90bc: ldur            x2, [fp, #-0x10]
    // 0xba90c0: r1 = Function '<anonymous closure>':.
    //     0xba90c0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a628] AnonymousClosure: (0xba936c), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba90c4: ldr             x1, [x1, #0x628]
    // 0xba90c8: r0 = AllocateClosure()
    //     0xba90c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba90cc: mov             x1, x0
    // 0xba90d0: ldur            x0, [fp, #-0x50]
    // 0xba90d4: StoreField: r0->field_f = r1
    //     0xba90d4: stur            w1, [x0, #0xf]
    // 0xba90d8: r1 = true
    //     0xba90d8: add             x1, NULL, #0x20  ; true
    // 0xba90dc: StoreField: r0->field_43 = r1
    //     0xba90dc: stur            w1, [x0, #0x43]
    // 0xba90e0: r2 = Instance_BoxShape
    //     0xba90e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xba90e4: ldr             x2, [x2, #0x80]
    // 0xba90e8: StoreField: r0->field_47 = r2
    //     0xba90e8: stur            w2, [x0, #0x47]
    // 0xba90ec: StoreField: r0->field_6f = r1
    //     0xba90ec: stur            w1, [x0, #0x6f]
    // 0xba90f0: r2 = false
    //     0xba90f0: add             x2, NULL, #0x30  ; false
    // 0xba90f4: StoreField: r0->field_73 = r2
    //     0xba90f4: stur            w2, [x0, #0x73]
    // 0xba90f8: StoreField: r0->field_83 = r1
    //     0xba90f8: stur            w1, [x0, #0x83]
    // 0xba90fc: StoreField: r0->field_7b = r2
    //     0xba90fc: stur            w2, [x0, #0x7b]
    // 0xba9100: r1 = <FlexParentData>
    //     0xba9100: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xba9104: ldr             x1, [x1, #0xe00]
    // 0xba9108: r0 = Expanded()
    //     0xba9108: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xba910c: mov             x1, x0
    // 0xba9110: r0 = 1
    //     0xba9110: movz            x0, #0x1
    // 0xba9114: stur            x1, [fp, #-8]
    // 0xba9118: StoreField: r1->field_13 = r0
    //     0xba9118: stur            x0, [x1, #0x13]
    // 0xba911c: r0 = Instance_FlexFit
    //     0xba911c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xba9120: ldr             x0, [x0, #0xe08]
    // 0xba9124: StoreField: r1->field_1b = r0
    //     0xba9124: stur            w0, [x1, #0x1b]
    // 0xba9128: ldur            x0, [fp, #-0x50]
    // 0xba912c: StoreField: r1->field_b = r0
    //     0xba912c: stur            w0, [x1, #0xb]
    // 0xba9130: r0 = Visibility()
    //     0xba9130: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xba9134: mov             x3, x0
    // 0xba9138: ldur            x0, [fp, #-8]
    // 0xba913c: stur            x3, [fp, #-0x10]
    // 0xba9140: StoreField: r3->field_b = r0
    //     0xba9140: stur            w0, [x3, #0xb]
    // 0xba9144: r0 = Instance_SizedBox
    //     0xba9144: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xba9148: StoreField: r3->field_f = r0
    //     0xba9148: stur            w0, [x3, #0xf]
    // 0xba914c: ldur            x0, [fp, #-0x48]
    // 0xba9150: StoreField: r3->field_13 = r0
    //     0xba9150: stur            w0, [x3, #0x13]
    // 0xba9154: r0 = false
    //     0xba9154: add             x0, NULL, #0x30  ; false
    // 0xba9158: ArrayStore: r3[0] = r0  ; List_4
    //     0xba9158: stur            w0, [x3, #0x17]
    // 0xba915c: StoreField: r3->field_1b = r0
    //     0xba915c: stur            w0, [x3, #0x1b]
    // 0xba9160: StoreField: r3->field_1f = r0
    //     0xba9160: stur            w0, [x3, #0x1f]
    // 0xba9164: StoreField: r3->field_23 = r0
    //     0xba9164: stur            w0, [x3, #0x23]
    // 0xba9168: StoreField: r3->field_27 = r0
    //     0xba9168: stur            w0, [x3, #0x27]
    // 0xba916c: StoreField: r3->field_2b = r0
    //     0xba916c: stur            w0, [x3, #0x2b]
    // 0xba9170: r1 = Null
    //     0xba9170: mov             x1, NULL
    // 0xba9174: r2 = 8
    //     0xba9174: movz            x2, #0x8
    // 0xba9178: r0 = AllocateArray()
    //     0xba9178: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba917c: mov             x2, x0
    // 0xba9180: ldur            x0, [fp, #-0x40]
    // 0xba9184: stur            x2, [fp, #-8]
    // 0xba9188: StoreField: r2->field_f = r0
    //     0xba9188: stur            w0, [x2, #0xf]
    // 0xba918c: ldur            x0, [fp, #-0x38]
    // 0xba9190: StoreField: r2->field_13 = r0
    //     0xba9190: stur            w0, [x2, #0x13]
    // 0xba9194: r16 = Instance_Spacer
    //     0xba9194: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba9198: ldr             x16, [x16, #0xf0]
    // 0xba919c: ArrayStore: r2[0] = r16  ; List_4
    //     0xba919c: stur            w16, [x2, #0x17]
    // 0xba91a0: ldur            x0, [fp, #-0x10]
    // 0xba91a4: StoreField: r2->field_1b = r0
    //     0xba91a4: stur            w0, [x2, #0x1b]
    // 0xba91a8: r1 = <Widget>
    //     0xba91a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba91ac: r0 = AllocateGrowableArray()
    //     0xba91ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba91b0: mov             x1, x0
    // 0xba91b4: ldur            x0, [fp, #-8]
    // 0xba91b8: stur            x1, [fp, #-0x10]
    // 0xba91bc: StoreField: r1->field_f = r0
    //     0xba91bc: stur            w0, [x1, #0xf]
    // 0xba91c0: r0 = 8
    //     0xba91c0: movz            x0, #0x8
    // 0xba91c4: StoreField: r1->field_b = r0
    //     0xba91c4: stur            w0, [x1, #0xb]
    // 0xba91c8: r0 = Row()
    //     0xba91c8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba91cc: mov             x1, x0
    // 0xba91d0: r0 = Instance_Axis
    //     0xba91d0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba91d4: stur            x1, [fp, #-8]
    // 0xba91d8: StoreField: r1->field_f = r0
    //     0xba91d8: stur            w0, [x1, #0xf]
    // 0xba91dc: r0 = Instance_MainAxisAlignment
    //     0xba91dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba91e0: ldr             x0, [x0, #0xa08]
    // 0xba91e4: StoreField: r1->field_13 = r0
    //     0xba91e4: stur            w0, [x1, #0x13]
    // 0xba91e8: r0 = Instance_MainAxisSize
    //     0xba91e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba91ec: ldr             x0, [x0, #0xa10]
    // 0xba91f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xba91f0: stur            w0, [x1, #0x17]
    // 0xba91f4: r0 = Instance_CrossAxisAlignment
    //     0xba91f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba91f8: ldr             x0, [x0, #0xa18]
    // 0xba91fc: StoreField: r1->field_1b = r0
    //     0xba91fc: stur            w0, [x1, #0x1b]
    // 0xba9200: r0 = Instance_VerticalDirection
    //     0xba9200: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba9204: ldr             x0, [x0, #0xa20]
    // 0xba9208: StoreField: r1->field_23 = r0
    //     0xba9208: stur            w0, [x1, #0x23]
    // 0xba920c: r0 = Instance_Clip
    //     0xba920c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba9210: ldr             x0, [x0, #0x38]
    // 0xba9214: StoreField: r1->field_2b = r0
    //     0xba9214: stur            w0, [x1, #0x2b]
    // 0xba9218: StoreField: r1->field_2f = rZR
    //     0xba9218: stur            xzr, [x1, #0x2f]
    // 0xba921c: ldur            x0, [fp, #-0x10]
    // 0xba9220: StoreField: r1->field_b = r0
    //     0xba9220: stur            w0, [x1, #0xb]
    // 0xba9224: r0 = Padding()
    //     0xba9224: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba9228: mov             x1, x0
    // 0xba922c: r0 = Instance_EdgeInsets
    //     0xba922c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba9230: ldr             x0, [x0, #0x980]
    // 0xba9234: stur            x1, [fp, #-0x10]
    // 0xba9238: StoreField: r1->field_f = r0
    //     0xba9238: stur            w0, [x1, #0xf]
    // 0xba923c: ldur            x2, [fp, #-8]
    // 0xba9240: StoreField: r1->field_b = r2
    //     0xba9240: stur            w2, [x1, #0xb]
    // 0xba9244: r0 = DottedBorder()
    //     0xba9244: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0xba9248: mov             x1, x0
    // 0xba924c: ldur            x2, [fp, #-0x10]
    // 0xba9250: ldur            x3, [fp, #-0x30]
    // 0xba9254: r5 = const [5.0, 5.0]
    //     0xba9254: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0xba9258: ldr             x5, [x5, #0xab0]
    // 0xba925c: d0 = 1.000000
    //     0xba925c: fmov            d0, #1.00000000
    // 0xba9260: stur            x0, [fp, #-8]
    // 0xba9264: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xba9264: ldr             x4, [PP, #0x1330]  ; [pp+0x1330] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xba9268: r0 = DottedBorder()
    //     0xba9268: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0xba926c: ldur            x0, [fp, #-8]
    // 0xba9270: stur            x0, [fp, #-8]
    // 0xba9274: r0 = Padding()
    //     0xba9274: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xba9278: r1 = Instance_EdgeInsets
    //     0xba9278: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xba927c: ldr             x1, [x1, #0x980]
    // 0xba9280: StoreField: r0->field_f = r1
    //     0xba9280: stur            w1, [x0, #0xf]
    // 0xba9284: ldur            x1, [fp, #-8]
    // 0xba9288: StoreField: r0->field_b = r1
    //     0xba9288: stur            w1, [x0, #0xb]
    // 0xba928c: LeaveFrame
    //     0xba928c: mov             SP, fp
    //     0xba9290: ldp             fp, lr, [SP], #0x10
    // 0xba9294: ret
    //     0xba9294: ret             
    // 0xba9298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9298: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba929c: b               #0xba72a4
    // 0xba92a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba92a4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba92a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92b0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xba92b0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xba92b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba92b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba92b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba92c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba92c4: SaveReg d0
    //     0xba92c4: str             q0, [SP, #-0x10]!
    // 0xba92c8: stp             x0, x1, [SP, #-0x10]!
    // 0xba92cc: r0 = AllocateDouble()
    //     0xba92cc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xba92d0: mov             x2, x0
    // 0xba92d4: ldp             x0, x1, [SP], #0x10
    // 0xba92d8: RestoreReg d0
    //     0xba92d8: ldr             q0, [SP], #0x10
    // 0xba92dc: b               #0xba7a68
    // 0xba92e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba92e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba92e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba92f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba92f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba92f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba92f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba92fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba92fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9300: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9300: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9304: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9308: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9308: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba930c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba930c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9310: r0 = NullCastErrorSharedWithFPURegs()
    //     0xba9310: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xba9314: r0 = RangeErrorSharedWithFPURegs()
    //     0xba9314: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xba9318: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9318: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba931c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba931c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9320: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9320: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9324: SaveReg d0
    //     0xba9324: str             q0, [SP, #-0x10]!
    // 0xba9328: stp             x0, x1, [SP, #-0x10]!
    // 0xba932c: r0 = AllocateDouble()
    //     0xba932c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xba9330: mov             x2, x0
    // 0xba9334: ldp             x0, x1, [SP], #0x10
    // 0xba9338: RestoreReg d0
    //     0xba9338: ldr             q0, [SP], #0x10
    // 0xba933c: b               #0xba88dc
    // 0xba9340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9340: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9344: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9344: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9348: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba934c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba934c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9350: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9350: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9354: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9358: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba935c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba935c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9360: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9360: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9364: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9368: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9368: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xba936c, size: 0x358
    // 0xba936c: EnterFrame
    //     0xba936c: stp             fp, lr, [SP, #-0x10]!
    //     0xba9370: mov             fp, SP
    // 0xba9374: AllocStack(0x28)
    //     0xba9374: sub             SP, SP, #0x28
    // 0xba9378: SetupParameters()
    //     0xba9378: ldr             x0, [fp, #0x10]
    //     0xba937c: ldur            w2, [x0, #0x17]
    //     0xba9380: add             x2, x2, HEAP, lsl #32
    //     0xba9384: stur            x2, [fp, #-8]
    // 0xba9388: CheckStackOverflow
    //     0xba9388: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba938c: cmp             SP, x16
    //     0xba9390: b.ls            #0xba96a4
    // 0xba9394: LoadField: r0 = r2->field_b
    //     0xba9394: ldur            w0, [x2, #0xb]
    // 0xba9398: DecompressPointer r0
    //     0xba9398: add             x0, x0, HEAP, lsl #32
    // 0xba939c: LoadField: r1 = r0->field_f
    //     0xba939c: ldur            w1, [x0, #0xf]
    // 0xba93a0: DecompressPointer r1
    //     0xba93a0: add             x1, x1, HEAP, lsl #32
    // 0xba93a4: LoadField: r3 = r1->field_b
    //     0xba93a4: ldur            w3, [x1, #0xb]
    // 0xba93a8: DecompressPointer r3
    //     0xba93a8: add             x3, x3, HEAP, lsl #32
    // 0xba93ac: cmp             w3, NULL
    // 0xba93b0: b.eq            #0xba96ac
    // 0xba93b4: LoadField: r0 = r3->field_f
    //     0xba93b4: ldur            w0, [x3, #0xf]
    // 0xba93b8: DecompressPointer r0
    //     0xba93b8: add             x0, x0, HEAP, lsl #32
    // 0xba93bc: cmp             w0, NULL
    // 0xba93c0: b.ne            #0xba93cc
    // 0xba93c4: r0 = Null
    //     0xba93c4: mov             x0, NULL
    // 0xba93c8: b               #0xba9418
    // 0xba93cc: LoadField: r4 = r0->field_f
    //     0xba93cc: ldur            w4, [x0, #0xf]
    // 0xba93d0: DecompressPointer r4
    //     0xba93d0: add             x4, x4, HEAP, lsl #32
    // 0xba93d4: LoadField: r0 = r2->field_13
    //     0xba93d4: ldur            w0, [x2, #0x13]
    // 0xba93d8: DecompressPointer r0
    //     0xba93d8: add             x0, x0, HEAP, lsl #32
    // 0xba93dc: LoadField: r1 = r4->field_b
    //     0xba93dc: ldur            w1, [x4, #0xb]
    // 0xba93e0: r5 = LoadInt32Instr(r0)
    //     0xba93e0: sbfx            x5, x0, #1, #0x1f
    //     0xba93e4: tbz             w0, #0, #0xba93ec
    //     0xba93e8: ldur            x5, [x0, #7]
    // 0xba93ec: r0 = LoadInt32Instr(r1)
    //     0xba93ec: sbfx            x0, x1, #1, #0x1f
    // 0xba93f0: mov             x1, x5
    // 0xba93f4: cmp             x1, x0
    // 0xba93f8: b.hs            #0xba96b0
    // 0xba93fc: LoadField: r0 = r4->field_f
    //     0xba93fc: ldur            w0, [x4, #0xf]
    // 0xba9400: DecompressPointer r0
    //     0xba9400: add             x0, x0, HEAP, lsl #32
    // 0xba9404: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba9404: add             x16, x0, x5, lsl #2
    //     0xba9408: ldur            w1, [x16, #0xf]
    // 0xba940c: DecompressPointer r1
    //     0xba940c: add             x1, x1, HEAP, lsl #32
    // 0xba9410: LoadField: r0 = r1->field_27
    //     0xba9410: ldur            w0, [x1, #0x27]
    // 0xba9414: DecompressPointer r0
    //     0xba9414: add             x0, x0, HEAP, lsl #32
    // 0xba9418: cmp             w0, NULL
    // 0xba941c: b.eq            #0xba9694
    // 0xba9420: tbnz            w0, #4, #0xba9694
    // 0xba9424: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xba9424: ldur            w0, [x3, #0x17]
    // 0xba9428: DecompressPointer r0
    //     0xba9428: add             x0, x0, HEAP, lsl #32
    // 0xba942c: cmp             w0, NULL
    // 0xba9430: b.eq            #0xba954c
    // 0xba9434: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xba9434: ldur            w4, [x3, #0x17]
    // 0xba9438: DecompressPointer r4
    //     0xba9438: add             x4, x4, HEAP, lsl #32
    // 0xba943c: cmp             w4, NULL
    // 0xba9440: b.eq            #0xba9678
    // 0xba9444: LoadField: r0 = r3->field_f
    //     0xba9444: ldur            w0, [x3, #0xf]
    // 0xba9448: DecompressPointer r0
    //     0xba9448: add             x0, x0, HEAP, lsl #32
    // 0xba944c: cmp             w0, NULL
    // 0xba9450: b.ne            #0xba945c
    // 0xba9454: r0 = Null
    //     0xba9454: mov             x0, NULL
    // 0xba9458: b               #0xba94a8
    // 0xba945c: LoadField: r5 = r0->field_f
    //     0xba945c: ldur            w5, [x0, #0xf]
    // 0xba9460: DecompressPointer r5
    //     0xba9460: add             x5, x5, HEAP, lsl #32
    // 0xba9464: LoadField: r0 = r2->field_13
    //     0xba9464: ldur            w0, [x2, #0x13]
    // 0xba9468: DecompressPointer r0
    //     0xba9468: add             x0, x0, HEAP, lsl #32
    // 0xba946c: LoadField: r1 = r5->field_b
    //     0xba946c: ldur            w1, [x5, #0xb]
    // 0xba9470: r6 = LoadInt32Instr(r0)
    //     0xba9470: sbfx            x6, x0, #1, #0x1f
    //     0xba9474: tbz             w0, #0, #0xba947c
    //     0xba9478: ldur            x6, [x0, #7]
    // 0xba947c: r0 = LoadInt32Instr(r1)
    //     0xba947c: sbfx            x0, x1, #1, #0x1f
    // 0xba9480: mov             x1, x6
    // 0xba9484: cmp             x1, x0
    // 0xba9488: b.hs            #0xba96b4
    // 0xba948c: LoadField: r0 = r5->field_f
    //     0xba948c: ldur            w0, [x5, #0xf]
    // 0xba9490: DecompressPointer r0
    //     0xba9490: add             x0, x0, HEAP, lsl #32
    // 0xba9494: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba9494: add             x16, x0, x6, lsl #2
    //     0xba9498: ldur            w1, [x16, #0xf]
    // 0xba949c: DecompressPointer r1
    //     0xba949c: add             x1, x1, HEAP, lsl #32
    // 0xba94a0: LoadField: r0 = r1->field_7
    //     0xba94a0: ldur            w0, [x1, #7]
    // 0xba94a4: DecompressPointer r0
    //     0xba94a4: add             x0, x0, HEAP, lsl #32
    // 0xba94a8: cmp             w0, NULL
    // 0xba94ac: b.ne            #0xba94b8
    // 0xba94b0: r5 = ""
    //     0xba94b0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba94b4: b               #0xba94bc
    // 0xba94b8: mov             x5, x0
    // 0xba94bc: LoadField: r0 = r3->field_f
    //     0xba94bc: ldur            w0, [x3, #0xf]
    // 0xba94c0: DecompressPointer r0
    //     0xba94c0: add             x0, x0, HEAP, lsl #32
    // 0xba94c4: cmp             w0, NULL
    // 0xba94c8: b.ne            #0xba94d4
    // 0xba94cc: r0 = Null
    //     0xba94cc: mov             x0, NULL
    // 0xba94d0: b               #0xba9520
    // 0xba94d4: LoadField: r3 = r0->field_f
    //     0xba94d4: ldur            w3, [x0, #0xf]
    // 0xba94d8: DecompressPointer r3
    //     0xba94d8: add             x3, x3, HEAP, lsl #32
    // 0xba94dc: LoadField: r0 = r2->field_13
    //     0xba94dc: ldur            w0, [x2, #0x13]
    // 0xba94e0: DecompressPointer r0
    //     0xba94e0: add             x0, x0, HEAP, lsl #32
    // 0xba94e4: LoadField: r1 = r3->field_b
    //     0xba94e4: ldur            w1, [x3, #0xb]
    // 0xba94e8: r6 = LoadInt32Instr(r0)
    //     0xba94e8: sbfx            x6, x0, #1, #0x1f
    //     0xba94ec: tbz             w0, #0, #0xba94f4
    //     0xba94f0: ldur            x6, [x0, #7]
    // 0xba94f4: r0 = LoadInt32Instr(r1)
    //     0xba94f4: sbfx            x0, x1, #1, #0x1f
    // 0xba94f8: mov             x1, x6
    // 0xba94fc: cmp             x1, x0
    // 0xba9500: b.hs            #0xba96b8
    // 0xba9504: LoadField: r0 = r3->field_f
    //     0xba9504: ldur            w0, [x3, #0xf]
    // 0xba9508: DecompressPointer r0
    //     0xba9508: add             x0, x0, HEAP, lsl #32
    // 0xba950c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba950c: add             x16, x0, x6, lsl #2
    //     0xba9510: ldur            w1, [x16, #0xf]
    // 0xba9514: DecompressPointer r1
    //     0xba9514: add             x1, x1, HEAP, lsl #32
    // 0xba9518: LoadField: r0 = r1->field_b
    //     0xba9518: ldur            w0, [x1, #0xb]
    // 0xba951c: DecompressPointer r0
    //     0xba951c: add             x0, x0, HEAP, lsl #32
    // 0xba9520: stp             x5, x4, [SP, #0x10]
    // 0xba9524: r16 = "apply"
    //     0xba9524: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xba9528: ldr             x16, [x16, #0xfe8]
    // 0xba952c: stp             x16, x0, [SP]
    // 0xba9530: r4 = 0
    //     0xba9530: movz            x4, #0
    // 0xba9534: ldr             x0, [SP, #0x18]
    // 0xba9538: r16 = UnlinkedCall_0x613b5c
    //     0xba9538: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a630] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba953c: add             x16, x16, #0x630
    // 0xba9540: ldp             x5, lr, [x16]
    // 0xba9544: blr             lr
    // 0xba9548: b               #0xba9678
    // 0xba954c: LoadField: r0 = r3->field_1b
    //     0xba954c: ldur            w0, [x3, #0x1b]
    // 0xba9550: DecompressPointer r0
    //     0xba9550: add             x0, x0, HEAP, lsl #32
    // 0xba9554: cmp             w0, NULL
    // 0xba9558: b.eq            #0xba9678
    // 0xba955c: LoadField: r2 = r3->field_1b
    //     0xba955c: ldur            w2, [x3, #0x1b]
    // 0xba9560: DecompressPointer r2
    //     0xba9560: add             x2, x2, HEAP, lsl #32
    // 0xba9564: cmp             w2, NULL
    // 0xba9568: b.eq            #0xba9678
    // 0xba956c: LoadField: r0 = r3->field_f
    //     0xba956c: ldur            w0, [x3, #0xf]
    // 0xba9570: DecompressPointer r0
    //     0xba9570: add             x0, x0, HEAP, lsl #32
    // 0xba9574: cmp             w0, NULL
    // 0xba9578: b.ne            #0xba9588
    // 0xba957c: ldur            x4, [fp, #-8]
    // 0xba9580: r0 = Null
    //     0xba9580: mov             x0, NULL
    // 0xba9584: b               #0xba95d8
    // 0xba9588: ldur            x4, [fp, #-8]
    // 0xba958c: LoadField: r5 = r0->field_f
    //     0xba958c: ldur            w5, [x0, #0xf]
    // 0xba9590: DecompressPointer r5
    //     0xba9590: add             x5, x5, HEAP, lsl #32
    // 0xba9594: LoadField: r0 = r4->field_13
    //     0xba9594: ldur            w0, [x4, #0x13]
    // 0xba9598: DecompressPointer r0
    //     0xba9598: add             x0, x0, HEAP, lsl #32
    // 0xba959c: LoadField: r1 = r5->field_b
    //     0xba959c: ldur            w1, [x5, #0xb]
    // 0xba95a0: r6 = LoadInt32Instr(r0)
    //     0xba95a0: sbfx            x6, x0, #1, #0x1f
    //     0xba95a4: tbz             w0, #0, #0xba95ac
    //     0xba95a8: ldur            x6, [x0, #7]
    // 0xba95ac: r0 = LoadInt32Instr(r1)
    //     0xba95ac: sbfx            x0, x1, #1, #0x1f
    // 0xba95b0: mov             x1, x6
    // 0xba95b4: cmp             x1, x0
    // 0xba95b8: b.hs            #0xba96bc
    // 0xba95bc: LoadField: r0 = r5->field_f
    //     0xba95bc: ldur            w0, [x5, #0xf]
    // 0xba95c0: DecompressPointer r0
    //     0xba95c0: add             x0, x0, HEAP, lsl #32
    // 0xba95c4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba95c4: add             x16, x0, x6, lsl #2
    //     0xba95c8: ldur            w1, [x16, #0xf]
    // 0xba95cc: DecompressPointer r1
    //     0xba95cc: add             x1, x1, HEAP, lsl #32
    // 0xba95d0: LoadField: r0 = r1->field_7
    //     0xba95d0: ldur            w0, [x1, #7]
    // 0xba95d4: DecompressPointer r0
    //     0xba95d4: add             x0, x0, HEAP, lsl #32
    // 0xba95d8: cmp             w0, NULL
    // 0xba95dc: b.ne            #0xba95e8
    // 0xba95e0: r5 = ""
    //     0xba95e0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba95e4: b               #0xba95ec
    // 0xba95e8: mov             x5, x0
    // 0xba95ec: LoadField: r0 = r3->field_f
    //     0xba95ec: ldur            w0, [x3, #0xf]
    // 0xba95f0: DecompressPointer r0
    //     0xba95f0: add             x0, x0, HEAP, lsl #32
    // 0xba95f4: cmp             w0, NULL
    // 0xba95f8: b.ne            #0xba9604
    // 0xba95fc: r0 = Null
    //     0xba95fc: mov             x0, NULL
    // 0xba9600: b               #0xba9650
    // 0xba9604: LoadField: r3 = r0->field_f
    //     0xba9604: ldur            w3, [x0, #0xf]
    // 0xba9608: DecompressPointer r3
    //     0xba9608: add             x3, x3, HEAP, lsl #32
    // 0xba960c: LoadField: r0 = r4->field_13
    //     0xba960c: ldur            w0, [x4, #0x13]
    // 0xba9610: DecompressPointer r0
    //     0xba9610: add             x0, x0, HEAP, lsl #32
    // 0xba9614: LoadField: r1 = r3->field_b
    //     0xba9614: ldur            w1, [x3, #0xb]
    // 0xba9618: r6 = LoadInt32Instr(r0)
    //     0xba9618: sbfx            x6, x0, #1, #0x1f
    //     0xba961c: tbz             w0, #0, #0xba9624
    //     0xba9620: ldur            x6, [x0, #7]
    // 0xba9624: r0 = LoadInt32Instr(r1)
    //     0xba9624: sbfx            x0, x1, #1, #0x1f
    // 0xba9628: mov             x1, x6
    // 0xba962c: cmp             x1, x0
    // 0xba9630: b.hs            #0xba96c0
    // 0xba9634: LoadField: r0 = r3->field_f
    //     0xba9634: ldur            w0, [x3, #0xf]
    // 0xba9638: DecompressPointer r0
    //     0xba9638: add             x0, x0, HEAP, lsl #32
    // 0xba963c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba963c: add             x16, x0, x6, lsl #2
    //     0xba9640: ldur            w1, [x16, #0xf]
    // 0xba9644: DecompressPointer r1
    //     0xba9644: add             x1, x1, HEAP, lsl #32
    // 0xba9648: LoadField: r0 = r1->field_b
    //     0xba9648: ldur            w0, [x1, #0xb]
    // 0xba964c: DecompressPointer r0
    //     0xba964c: add             x0, x0, HEAP, lsl #32
    // 0xba9650: stp             x5, x2, [SP, #0x10]
    // 0xba9654: r16 = "remove"
    //     0xba9654: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xba9658: ldr             x16, [x16, #0xff0]
    // 0xba965c: stp             x16, x0, [SP]
    // 0xba9660: r4 = 0
    //     0xba9660: movz            x4, #0
    // 0xba9664: ldr             x0, [SP, #0x18]
    // 0xba9668: r16 = UnlinkedCall_0x613b5c
    //     0xba9668: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a640] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba966c: add             x16, x16, #0x640
    // 0xba9670: ldp             x5, lr, [x16]
    // 0xba9674: blr             lr
    // 0xba9678: ldur            x0, [fp, #-8]
    // 0xba967c: LoadField: r1 = r0->field_f
    //     0xba967c: ldur            w1, [x0, #0xf]
    // 0xba9680: DecompressPointer r1
    //     0xba9680: add             x1, x1, HEAP, lsl #32
    // 0xba9684: r16 = <Object?>
    //     0xba9684: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xba9688: stp             x1, x16, [SP]
    // 0xba968c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba968c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba9690: r0 = pop()
    //     0xba9690: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xba9694: r0 = Null
    //     0xba9694: mov             x0, NULL
    // 0xba9698: LeaveFrame
    //     0xba9698: mov             SP, fp
    //     0xba969c: ldp             fp, lr, [SP], #0x10
    // 0xba96a0: ret
    //     0xba96a0: ret             
    // 0xba96a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba96a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba96a8: b               #0xba9394
    // 0xba96ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba96ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba96b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba96b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba96b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba96b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba96b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba96b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba96bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba96bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba96c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba96c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xba96c4, size: 0x9c
    // 0xba96c4: EnterFrame
    //     0xba96c4: stp             fp, lr, [SP, #-0x10]!
    //     0xba96c8: mov             fp, SP
    // 0xba96cc: AllocStack(0x38)
    //     0xba96cc: sub             SP, SP, #0x38
    // 0xba96d0: SetupParameters()
    //     0xba96d0: ldr             x0, [fp, #0x10]
    //     0xba96d4: ldur            w2, [x0, #0x17]
    //     0xba96d8: add             x2, x2, HEAP, lsl #32
    //     0xba96dc: stur            x2, [fp, #-8]
    // 0xba96e0: CheckStackOverflow
    //     0xba96e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba96e4: cmp             SP, x16
    //     0xba96e8: b.ls            #0xba9758
    // 0xba96ec: LoadField: r0 = r2->field_f
    //     0xba96ec: ldur            w0, [x2, #0xf]
    // 0xba96f0: DecompressPointer r0
    //     0xba96f0: add             x0, x0, HEAP, lsl #32
    // 0xba96f4: r16 = <Object?>
    //     0xba96f4: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xba96f8: stp             x0, x16, [SP]
    // 0xba96fc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba96fc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba9700: r0 = pop()
    //     0xba9700: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xba9704: ldur            x2, [fp, #-8]
    // 0xba9708: LoadField: r0 = r2->field_f
    //     0xba9708: ldur            w0, [x2, #0xf]
    // 0xba970c: DecompressPointer r0
    //     0xba970c: add             x0, x0, HEAP, lsl #32
    // 0xba9710: stur            x0, [fp, #-0x10]
    // 0xba9714: r1 = Function '<anonymous closure>':.
    //     0xba9714: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a650] AnonymousClosure: (0xba9760), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba9718: ldr             x1, [x1, #0x650]
    // 0xba971c: r0 = AllocateClosure()
    //     0xba971c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba9720: stp             x0, NULL, [SP, #0x18]
    // 0xba9724: ldur            x16, [fp, #-0x10]
    // 0xba9728: r30 = true
    //     0xba9728: add             lr, NULL, #0x20  ; true
    // 0xba972c: stp             lr, x16, [SP, #8]
    // 0xba9730: r16 = Instance_RoundedRectangleBorder
    //     0xba9730: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xba9734: ldr             x16, [x16, #0xd68]
    // 0xba9738: str             x16, [SP]
    // 0xba973c: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xba973c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xba9740: ldr             x4, [x4, #0xb20]
    // 0xba9744: r0 = showModalBottomSheet()
    //     0xba9744: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xba9748: r0 = Null
    //     0xba9748: mov             x0, NULL
    // 0xba974c: LeaveFrame
    //     0xba974c: mov             SP, fp
    //     0xba9750: ldp             fp, lr, [SP], #0x10
    // 0xba9754: ret
    //     0xba9754: ret             
    // 0xba9758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9758: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba975c: b               #0xba96ec
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xba9760, size: 0x24c
    // 0xba9760: EnterFrame
    //     0xba9760: stp             fp, lr, [SP, #-0x10]!
    //     0xba9764: mov             fp, SP
    // 0xba9768: AllocStack(0x60)
    //     0xba9768: sub             SP, SP, #0x60
    // 0xba976c: SetupParameters()
    //     0xba976c: ldr             x0, [fp, #0x18]
    //     0xba9770: ldur            w1, [x0, #0x17]
    //     0xba9774: add             x1, x1, HEAP, lsl #32
    //     0xba9778: stur            x1, [fp, #-8]
    // 0xba977c: CheckStackOverflow
    //     0xba977c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba9780: cmp             SP, x16
    //     0xba9784: b.ls            #0xba9998
    // 0xba9788: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba9788: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba978c: ldr             x0, [x0, #0x1c80]
    //     0xba9790: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba9794: cmp             w0, w16
    //     0xba9798: b.ne            #0xba97a4
    //     0xba979c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba97a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba97a4: r0 = GetNavigation.size()
    //     0xba97a4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba97a8: LoadField: d0 = r0->field_f
    //     0xba97a8: ldur            d0, [x0, #0xf]
    // 0xba97ac: d1 = 0.650000
    //     0xba97ac: add             x17, PP, #0x45, lsl #12  ; [pp+0x45448] IMM: double(0.65) from 0x3fe4cccccccccccd
    //     0xba97b0: ldr             d1, [x17, #0x448]
    // 0xba97b4: fmul            d2, d0, d1
    // 0xba97b8: ldur            x1, [fp, #-8]
    // 0xba97bc: stur            d2, [fp, #-0x48]
    // 0xba97c0: LoadField: r2 = r1->field_b
    //     0xba97c0: ldur            w2, [x1, #0xb]
    // 0xba97c4: DecompressPointer r2
    //     0xba97c4: add             x2, x2, HEAP, lsl #32
    // 0xba97c8: stur            x2, [fp, #-0x10]
    // 0xba97cc: LoadField: r0 = r2->field_f
    //     0xba97cc: ldur            w0, [x2, #0xf]
    // 0xba97d0: DecompressPointer r0
    //     0xba97d0: add             x0, x0, HEAP, lsl #32
    // 0xba97d4: LoadField: r3 = r0->field_b
    //     0xba97d4: ldur            w3, [x0, #0xb]
    // 0xba97d8: DecompressPointer r3
    //     0xba97d8: add             x3, x3, HEAP, lsl #32
    // 0xba97dc: cmp             w3, NULL
    // 0xba97e0: b.eq            #0xba99a0
    // 0xba97e4: LoadField: r0 = r3->field_b
    //     0xba97e4: ldur            w0, [x3, #0xb]
    // 0xba97e8: DecompressPointer r0
    //     0xba97e8: add             x0, x0, HEAP, lsl #32
    // 0xba97ec: r3 = LoadClassIdInstr(r0)
    //     0xba97ec: ldur            x3, [x0, #-1]
    //     0xba97f0: ubfx            x3, x3, #0xc, #0x14
    // 0xba97f4: r16 = "checkout_offers"
    //     0xba97f4: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xba97f8: ldr             x16, [x16, #0x1c8]
    // 0xba97fc: stp             x16, x0, [SP]
    // 0xba9800: mov             x0, x3
    // 0xba9804: mov             lr, x0
    // 0xba9808: ldr             lr, [x21, lr, lsl #3]
    // 0xba980c: blr             lr
    // 0xba9810: tbnz            w0, #4, #0xba9830
    // 0xba9814: r0 = GetNavigation.size()
    //     0xba9814: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba9818: LoadField: d0 = r0->field_f
    //     0xba9818: ldur            d0, [x0, #0xf]
    // 0xba981c: d1 = 0.300000
    //     0xba981c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xba9820: ldr             d1, [x17, #0x658]
    // 0xba9824: fmul            d2, d0, d1
    // 0xba9828: mov             v1.16b, v2.16b
    // 0xba982c: b               #0xba9848
    // 0xba9830: r0 = GetNavigation.size()
    //     0xba9830: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba9834: LoadField: d0 = r0->field_f
    //     0xba9834: ldur            d0, [x0, #0xf]
    // 0xba9838: d1 = 0.450000
    //     0xba9838: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xba983c: ldr             d1, [x17, #0xd08]
    // 0xba9840: fmul            d2, d0, d1
    // 0xba9844: mov             v1.16b, v2.16b
    // 0xba9848: ldur            d0, [fp, #-0x48]
    // 0xba984c: ldur            x0, [fp, #-0x10]
    // 0xba9850: stur            d1, [fp, #-0x50]
    // 0xba9854: r0 = BoxConstraints()
    //     0xba9854: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba9858: mov             x2, x0
    // 0xba985c: stur            x2, [fp, #-0x38]
    // 0xba9860: StoreField: r2->field_7 = rZR
    //     0xba9860: stur            xzr, [x2, #7]
    // 0xba9864: d0 = inf
    //     0xba9864: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba9868: StoreField: r2->field_f = d0
    //     0xba9868: stur            d0, [x2, #0xf]
    // 0xba986c: ldur            d0, [fp, #-0x50]
    // 0xba9870: ArrayStore: r2[0] = d0  ; List_8
    //     0xba9870: stur            d0, [x2, #0x17]
    // 0xba9874: ldur            d0, [fp, #-0x48]
    // 0xba9878: StoreField: r2->field_1f = d0
    //     0xba9878: stur            d0, [x2, #0x1f]
    // 0xba987c: ldur            x0, [fp, #-0x10]
    // 0xba9880: LoadField: r1 = r0->field_f
    //     0xba9880: ldur            w1, [x0, #0xf]
    // 0xba9884: DecompressPointer r1
    //     0xba9884: add             x1, x1, HEAP, lsl #32
    // 0xba9888: LoadField: r3 = r1->field_b
    //     0xba9888: ldur            w3, [x1, #0xb]
    // 0xba988c: DecompressPointer r3
    //     0xba988c: add             x3, x3, HEAP, lsl #32
    // 0xba9890: cmp             w3, NULL
    // 0xba9894: b.eq            #0xba99a4
    // 0xba9898: LoadField: r4 = r3->field_1f
    //     0xba9898: ldur            w4, [x3, #0x1f]
    // 0xba989c: DecompressPointer r4
    //     0xba989c: add             x4, x4, HEAP, lsl #32
    // 0xba98a0: stur            x4, [fp, #-0x30]
    // 0xba98a4: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xba98a4: ldur            w5, [x3, #0x17]
    // 0xba98a8: DecompressPointer r5
    //     0xba98a8: add             x5, x5, HEAP, lsl #32
    // 0xba98ac: stur            x5, [fp, #-0x28]
    // 0xba98b0: LoadField: r6 = r3->field_1b
    //     0xba98b0: ldur            w6, [x3, #0x1b]
    // 0xba98b4: DecompressPointer r6
    //     0xba98b4: add             x6, x6, HEAP, lsl #32
    // 0xba98b8: stur            x6, [fp, #-0x20]
    // 0xba98bc: LoadField: r7 = r3->field_b
    //     0xba98bc: ldur            w7, [x3, #0xb]
    // 0xba98c0: DecompressPointer r7
    //     0xba98c0: add             x7, x7, HEAP, lsl #32
    // 0xba98c4: stur            x7, [fp, #-0x18]
    // 0xba98c8: LoadField: r0 = r3->field_f
    //     0xba98c8: ldur            w0, [x3, #0xf]
    // 0xba98cc: DecompressPointer r0
    //     0xba98cc: add             x0, x0, HEAP, lsl #32
    // 0xba98d0: cmp             w0, NULL
    // 0xba98d4: b.ne            #0xba98e0
    // 0xba98d8: r0 = Null
    //     0xba98d8: mov             x0, NULL
    // 0xba98dc: b               #0xba992c
    // 0xba98e0: ldur            x1, [fp, #-8]
    // 0xba98e4: LoadField: r8 = r0->field_f
    //     0xba98e4: ldur            w8, [x0, #0xf]
    // 0xba98e8: DecompressPointer r8
    //     0xba98e8: add             x8, x8, HEAP, lsl #32
    // 0xba98ec: LoadField: r0 = r1->field_13
    //     0xba98ec: ldur            w0, [x1, #0x13]
    // 0xba98f0: DecompressPointer r0
    //     0xba98f0: add             x0, x0, HEAP, lsl #32
    // 0xba98f4: LoadField: r1 = r8->field_b
    //     0xba98f4: ldur            w1, [x8, #0xb]
    // 0xba98f8: r9 = LoadInt32Instr(r0)
    //     0xba98f8: sbfx            x9, x0, #1, #0x1f
    //     0xba98fc: tbz             w0, #0, #0xba9904
    //     0xba9900: ldur            x9, [x0, #7]
    // 0xba9904: r0 = LoadInt32Instr(r1)
    //     0xba9904: sbfx            x0, x1, #1, #0x1f
    // 0xba9908: mov             x1, x9
    // 0xba990c: cmp             x1, x0
    // 0xba9910: b.hs            #0xba99a8
    // 0xba9914: LoadField: r0 = r8->field_f
    //     0xba9914: ldur            w0, [x8, #0xf]
    // 0xba9918: DecompressPointer r0
    //     0xba9918: add             x0, x0, HEAP, lsl #32
    // 0xba991c: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xba991c: add             x16, x0, x9, lsl #2
    //     0xba9920: ldur            w1, [x16, #0xf]
    // 0xba9924: DecompressPointer r1
    //     0xba9924: add             x1, x1, HEAP, lsl #32
    // 0xba9928: mov             x0, x1
    // 0xba992c: stur            x0, [fp, #-0x10]
    // 0xba9930: LoadField: r1 = r3->field_13
    //     0xba9930: ldur            w1, [x3, #0x13]
    // 0xba9934: DecompressPointer r1
    //     0xba9934: add             x1, x1, HEAP, lsl #32
    // 0xba9938: stur            x1, [fp, #-8]
    // 0xba993c: r0 = OffersBottomSheet()
    //     0xba993c: bl              #0xba99ac  ; AllocateOffersBottomSheetStub -> OffersBottomSheet (size=0x24)
    // 0xba9940: mov             x1, x0
    // 0xba9944: ldur            x0, [fp, #-0x10]
    // 0xba9948: stur            x1, [fp, #-0x40]
    // 0xba994c: StoreField: r1->field_b = r0
    //     0xba994c: stur            w0, [x1, #0xb]
    // 0xba9950: ldur            x0, [fp, #-8]
    // 0xba9954: StoreField: r1->field_f = r0
    //     0xba9954: stur            w0, [x1, #0xf]
    // 0xba9958: ldur            x0, [fp, #-0x18]
    // 0xba995c: StoreField: r1->field_13 = r0
    //     0xba995c: stur            w0, [x1, #0x13]
    // 0xba9960: ldur            x0, [fp, #-0x28]
    // 0xba9964: ArrayStore: r1[0] = r0  ; List_4
    //     0xba9964: stur            w0, [x1, #0x17]
    // 0xba9968: ldur            x0, [fp, #-0x20]
    // 0xba996c: StoreField: r1->field_1b = r0
    //     0xba996c: stur            w0, [x1, #0x1b]
    // 0xba9970: ldur            x0, [fp, #-0x30]
    // 0xba9974: StoreField: r1->field_1f = r0
    //     0xba9974: stur            w0, [x1, #0x1f]
    // 0xba9978: r0 = ConstrainedBox()
    //     0xba9978: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba997c: ldur            x1, [fp, #-0x38]
    // 0xba9980: StoreField: r0->field_f = r1
    //     0xba9980: stur            w1, [x0, #0xf]
    // 0xba9984: ldur            x1, [fp, #-0x40]
    // 0xba9988: StoreField: r0->field_b = r1
    //     0xba9988: stur            w1, [x0, #0xb]
    // 0xba998c: LeaveFrame
    //     0xba998c: mov             SP, fp
    //     0xba9990: ldp             fp, lr, [SP], #0x10
    // 0xba9994: ret
    //     0xba9994: ret             
    // 0xba9998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9998: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba999c: b               #0xba9788
    // 0xba99a0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xba99a0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xba99a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba99a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba99a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba99a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xba99b8, size: 0x364
    // 0xba99b8: EnterFrame
    //     0xba99b8: stp             fp, lr, [SP, #-0x10]!
    //     0xba99bc: mov             fp, SP
    // 0xba99c0: AllocStack(0x28)
    //     0xba99c0: sub             SP, SP, #0x28
    // 0xba99c4: SetupParameters()
    //     0xba99c4: ldr             x0, [fp, #0x10]
    //     0xba99c8: ldur            w2, [x0, #0x17]
    //     0xba99cc: add             x2, x2, HEAP, lsl #32
    //     0xba99d0: stur            x2, [fp, #-8]
    // 0xba99d4: CheckStackOverflow
    //     0xba99d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba99d8: cmp             SP, x16
    //     0xba99dc: b.ls            #0xba9cfc
    // 0xba99e0: LoadField: r0 = r2->field_b
    //     0xba99e0: ldur            w0, [x2, #0xb]
    // 0xba99e4: DecompressPointer r0
    //     0xba99e4: add             x0, x0, HEAP, lsl #32
    // 0xba99e8: LoadField: r1 = r0->field_f
    //     0xba99e8: ldur            w1, [x0, #0xf]
    // 0xba99ec: DecompressPointer r1
    //     0xba99ec: add             x1, x1, HEAP, lsl #32
    // 0xba99f0: LoadField: r3 = r1->field_b
    //     0xba99f0: ldur            w3, [x1, #0xb]
    // 0xba99f4: DecompressPointer r3
    //     0xba99f4: add             x3, x3, HEAP, lsl #32
    // 0xba99f8: cmp             w3, NULL
    // 0xba99fc: b.eq            #0xba9d04
    // 0xba9a00: LoadField: r0 = r3->field_1f
    //     0xba9a00: ldur            w0, [x3, #0x1f]
    // 0xba9a04: DecompressPointer r0
    //     0xba9a04: add             x0, x0, HEAP, lsl #32
    // 0xba9a08: tbnz            w0, #4, #0xba9cec
    // 0xba9a0c: LoadField: r0 = r3->field_f
    //     0xba9a0c: ldur            w0, [x3, #0xf]
    // 0xba9a10: DecompressPointer r0
    //     0xba9a10: add             x0, x0, HEAP, lsl #32
    // 0xba9a14: cmp             w0, NULL
    // 0xba9a18: b.ne            #0xba9a24
    // 0xba9a1c: r0 = Null
    //     0xba9a1c: mov             x0, NULL
    // 0xba9a20: b               #0xba9a70
    // 0xba9a24: LoadField: r4 = r0->field_f
    //     0xba9a24: ldur            w4, [x0, #0xf]
    // 0xba9a28: DecompressPointer r4
    //     0xba9a28: add             x4, x4, HEAP, lsl #32
    // 0xba9a2c: LoadField: r0 = r2->field_13
    //     0xba9a2c: ldur            w0, [x2, #0x13]
    // 0xba9a30: DecompressPointer r0
    //     0xba9a30: add             x0, x0, HEAP, lsl #32
    // 0xba9a34: LoadField: r1 = r4->field_b
    //     0xba9a34: ldur            w1, [x4, #0xb]
    // 0xba9a38: r5 = LoadInt32Instr(r0)
    //     0xba9a38: sbfx            x5, x0, #1, #0x1f
    //     0xba9a3c: tbz             w0, #0, #0xba9a44
    //     0xba9a40: ldur            x5, [x0, #7]
    // 0xba9a44: r0 = LoadInt32Instr(r1)
    //     0xba9a44: sbfx            x0, x1, #1, #0x1f
    // 0xba9a48: mov             x1, x5
    // 0xba9a4c: cmp             x1, x0
    // 0xba9a50: b.hs            #0xba9d08
    // 0xba9a54: LoadField: r0 = r4->field_f
    //     0xba9a54: ldur            w0, [x4, #0xf]
    // 0xba9a58: DecompressPointer r0
    //     0xba9a58: add             x0, x0, HEAP, lsl #32
    // 0xba9a5c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xba9a5c: add             x16, x0, x5, lsl #2
    //     0xba9a60: ldur            w1, [x16, #0xf]
    // 0xba9a64: DecompressPointer r1
    //     0xba9a64: add             x1, x1, HEAP, lsl #32
    // 0xba9a68: LoadField: r0 = r1->field_27
    //     0xba9a68: ldur            w0, [x1, #0x27]
    // 0xba9a6c: DecompressPointer r0
    //     0xba9a6c: add             x0, x0, HEAP, lsl #32
    // 0xba9a70: cmp             w0, NULL
    // 0xba9a74: b.eq            #0xba9cec
    // 0xba9a78: tbnz            w0, #4, #0xba9cec
    // 0xba9a7c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xba9a7c: ldur            w0, [x3, #0x17]
    // 0xba9a80: DecompressPointer r0
    //     0xba9a80: add             x0, x0, HEAP, lsl #32
    // 0xba9a84: cmp             w0, NULL
    // 0xba9a88: b.eq            #0xba9ba4
    // 0xba9a8c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xba9a8c: ldur            w4, [x3, #0x17]
    // 0xba9a90: DecompressPointer r4
    //     0xba9a90: add             x4, x4, HEAP, lsl #32
    // 0xba9a94: cmp             w4, NULL
    // 0xba9a98: b.eq            #0xba9cd0
    // 0xba9a9c: LoadField: r0 = r3->field_f
    //     0xba9a9c: ldur            w0, [x3, #0xf]
    // 0xba9aa0: DecompressPointer r0
    //     0xba9aa0: add             x0, x0, HEAP, lsl #32
    // 0xba9aa4: cmp             w0, NULL
    // 0xba9aa8: b.ne            #0xba9ab4
    // 0xba9aac: r0 = Null
    //     0xba9aac: mov             x0, NULL
    // 0xba9ab0: b               #0xba9b00
    // 0xba9ab4: LoadField: r5 = r0->field_f
    //     0xba9ab4: ldur            w5, [x0, #0xf]
    // 0xba9ab8: DecompressPointer r5
    //     0xba9ab8: add             x5, x5, HEAP, lsl #32
    // 0xba9abc: LoadField: r0 = r2->field_13
    //     0xba9abc: ldur            w0, [x2, #0x13]
    // 0xba9ac0: DecompressPointer r0
    //     0xba9ac0: add             x0, x0, HEAP, lsl #32
    // 0xba9ac4: LoadField: r1 = r5->field_b
    //     0xba9ac4: ldur            w1, [x5, #0xb]
    // 0xba9ac8: r6 = LoadInt32Instr(r0)
    //     0xba9ac8: sbfx            x6, x0, #1, #0x1f
    //     0xba9acc: tbz             w0, #0, #0xba9ad4
    //     0xba9ad0: ldur            x6, [x0, #7]
    // 0xba9ad4: r0 = LoadInt32Instr(r1)
    //     0xba9ad4: sbfx            x0, x1, #1, #0x1f
    // 0xba9ad8: mov             x1, x6
    // 0xba9adc: cmp             x1, x0
    // 0xba9ae0: b.hs            #0xba9d0c
    // 0xba9ae4: LoadField: r0 = r5->field_f
    //     0xba9ae4: ldur            w0, [x5, #0xf]
    // 0xba9ae8: DecompressPointer r0
    //     0xba9ae8: add             x0, x0, HEAP, lsl #32
    // 0xba9aec: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba9aec: add             x16, x0, x6, lsl #2
    //     0xba9af0: ldur            w1, [x16, #0xf]
    // 0xba9af4: DecompressPointer r1
    //     0xba9af4: add             x1, x1, HEAP, lsl #32
    // 0xba9af8: LoadField: r0 = r1->field_7
    //     0xba9af8: ldur            w0, [x1, #7]
    // 0xba9afc: DecompressPointer r0
    //     0xba9afc: add             x0, x0, HEAP, lsl #32
    // 0xba9b00: cmp             w0, NULL
    // 0xba9b04: b.ne            #0xba9b10
    // 0xba9b08: r5 = ""
    //     0xba9b08: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba9b0c: b               #0xba9b14
    // 0xba9b10: mov             x5, x0
    // 0xba9b14: LoadField: r0 = r3->field_f
    //     0xba9b14: ldur            w0, [x3, #0xf]
    // 0xba9b18: DecompressPointer r0
    //     0xba9b18: add             x0, x0, HEAP, lsl #32
    // 0xba9b1c: cmp             w0, NULL
    // 0xba9b20: b.ne            #0xba9b2c
    // 0xba9b24: r0 = Null
    //     0xba9b24: mov             x0, NULL
    // 0xba9b28: b               #0xba9b78
    // 0xba9b2c: LoadField: r3 = r0->field_f
    //     0xba9b2c: ldur            w3, [x0, #0xf]
    // 0xba9b30: DecompressPointer r3
    //     0xba9b30: add             x3, x3, HEAP, lsl #32
    // 0xba9b34: LoadField: r0 = r2->field_13
    //     0xba9b34: ldur            w0, [x2, #0x13]
    // 0xba9b38: DecompressPointer r0
    //     0xba9b38: add             x0, x0, HEAP, lsl #32
    // 0xba9b3c: LoadField: r1 = r3->field_b
    //     0xba9b3c: ldur            w1, [x3, #0xb]
    // 0xba9b40: r6 = LoadInt32Instr(r0)
    //     0xba9b40: sbfx            x6, x0, #1, #0x1f
    //     0xba9b44: tbz             w0, #0, #0xba9b4c
    //     0xba9b48: ldur            x6, [x0, #7]
    // 0xba9b4c: r0 = LoadInt32Instr(r1)
    //     0xba9b4c: sbfx            x0, x1, #1, #0x1f
    // 0xba9b50: mov             x1, x6
    // 0xba9b54: cmp             x1, x0
    // 0xba9b58: b.hs            #0xba9d10
    // 0xba9b5c: LoadField: r0 = r3->field_f
    //     0xba9b5c: ldur            w0, [x3, #0xf]
    // 0xba9b60: DecompressPointer r0
    //     0xba9b60: add             x0, x0, HEAP, lsl #32
    // 0xba9b64: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba9b64: add             x16, x0, x6, lsl #2
    //     0xba9b68: ldur            w1, [x16, #0xf]
    // 0xba9b6c: DecompressPointer r1
    //     0xba9b6c: add             x1, x1, HEAP, lsl #32
    // 0xba9b70: LoadField: r0 = r1->field_b
    //     0xba9b70: ldur            w0, [x1, #0xb]
    // 0xba9b74: DecompressPointer r0
    //     0xba9b74: add             x0, x0, HEAP, lsl #32
    // 0xba9b78: stp             x5, x4, [SP, #0x10]
    // 0xba9b7c: r16 = "apply"
    //     0xba9b7c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xba9b80: ldr             x16, [x16, #0xfe8]
    // 0xba9b84: stp             x16, x0, [SP]
    // 0xba9b88: r4 = 0
    //     0xba9b88: movz            x4, #0
    // 0xba9b8c: ldr             x0, [SP, #0x18]
    // 0xba9b90: r16 = UnlinkedCall_0x613b5c
    //     0xba9b90: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a658] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba9b94: add             x16, x16, #0x658
    // 0xba9b98: ldp             x5, lr, [x16]
    // 0xba9b9c: blr             lr
    // 0xba9ba0: b               #0xba9cd0
    // 0xba9ba4: LoadField: r0 = r3->field_1b
    //     0xba9ba4: ldur            w0, [x3, #0x1b]
    // 0xba9ba8: DecompressPointer r0
    //     0xba9ba8: add             x0, x0, HEAP, lsl #32
    // 0xba9bac: cmp             w0, NULL
    // 0xba9bb0: b.eq            #0xba9cd0
    // 0xba9bb4: LoadField: r2 = r3->field_1b
    //     0xba9bb4: ldur            w2, [x3, #0x1b]
    // 0xba9bb8: DecompressPointer r2
    //     0xba9bb8: add             x2, x2, HEAP, lsl #32
    // 0xba9bbc: cmp             w2, NULL
    // 0xba9bc0: b.eq            #0xba9cd0
    // 0xba9bc4: LoadField: r0 = r3->field_f
    //     0xba9bc4: ldur            w0, [x3, #0xf]
    // 0xba9bc8: DecompressPointer r0
    //     0xba9bc8: add             x0, x0, HEAP, lsl #32
    // 0xba9bcc: cmp             w0, NULL
    // 0xba9bd0: b.ne            #0xba9be0
    // 0xba9bd4: ldur            x4, [fp, #-8]
    // 0xba9bd8: r0 = Null
    //     0xba9bd8: mov             x0, NULL
    // 0xba9bdc: b               #0xba9c30
    // 0xba9be0: ldur            x4, [fp, #-8]
    // 0xba9be4: LoadField: r5 = r0->field_f
    //     0xba9be4: ldur            w5, [x0, #0xf]
    // 0xba9be8: DecompressPointer r5
    //     0xba9be8: add             x5, x5, HEAP, lsl #32
    // 0xba9bec: LoadField: r0 = r4->field_13
    //     0xba9bec: ldur            w0, [x4, #0x13]
    // 0xba9bf0: DecompressPointer r0
    //     0xba9bf0: add             x0, x0, HEAP, lsl #32
    // 0xba9bf4: LoadField: r1 = r5->field_b
    //     0xba9bf4: ldur            w1, [x5, #0xb]
    // 0xba9bf8: r6 = LoadInt32Instr(r0)
    //     0xba9bf8: sbfx            x6, x0, #1, #0x1f
    //     0xba9bfc: tbz             w0, #0, #0xba9c04
    //     0xba9c00: ldur            x6, [x0, #7]
    // 0xba9c04: r0 = LoadInt32Instr(r1)
    //     0xba9c04: sbfx            x0, x1, #1, #0x1f
    // 0xba9c08: mov             x1, x6
    // 0xba9c0c: cmp             x1, x0
    // 0xba9c10: b.hs            #0xba9d14
    // 0xba9c14: LoadField: r0 = r5->field_f
    //     0xba9c14: ldur            w0, [x5, #0xf]
    // 0xba9c18: DecompressPointer r0
    //     0xba9c18: add             x0, x0, HEAP, lsl #32
    // 0xba9c1c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba9c1c: add             x16, x0, x6, lsl #2
    //     0xba9c20: ldur            w1, [x16, #0xf]
    // 0xba9c24: DecompressPointer r1
    //     0xba9c24: add             x1, x1, HEAP, lsl #32
    // 0xba9c28: LoadField: r0 = r1->field_7
    //     0xba9c28: ldur            w0, [x1, #7]
    // 0xba9c2c: DecompressPointer r0
    //     0xba9c2c: add             x0, x0, HEAP, lsl #32
    // 0xba9c30: cmp             w0, NULL
    // 0xba9c34: b.ne            #0xba9c40
    // 0xba9c38: r5 = ""
    //     0xba9c38: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba9c3c: b               #0xba9c44
    // 0xba9c40: mov             x5, x0
    // 0xba9c44: LoadField: r0 = r3->field_f
    //     0xba9c44: ldur            w0, [x3, #0xf]
    // 0xba9c48: DecompressPointer r0
    //     0xba9c48: add             x0, x0, HEAP, lsl #32
    // 0xba9c4c: cmp             w0, NULL
    // 0xba9c50: b.ne            #0xba9c5c
    // 0xba9c54: r0 = Null
    //     0xba9c54: mov             x0, NULL
    // 0xba9c58: b               #0xba9ca8
    // 0xba9c5c: LoadField: r3 = r0->field_f
    //     0xba9c5c: ldur            w3, [x0, #0xf]
    // 0xba9c60: DecompressPointer r3
    //     0xba9c60: add             x3, x3, HEAP, lsl #32
    // 0xba9c64: LoadField: r0 = r4->field_13
    //     0xba9c64: ldur            w0, [x4, #0x13]
    // 0xba9c68: DecompressPointer r0
    //     0xba9c68: add             x0, x0, HEAP, lsl #32
    // 0xba9c6c: LoadField: r1 = r3->field_b
    //     0xba9c6c: ldur            w1, [x3, #0xb]
    // 0xba9c70: r6 = LoadInt32Instr(r0)
    //     0xba9c70: sbfx            x6, x0, #1, #0x1f
    //     0xba9c74: tbz             w0, #0, #0xba9c7c
    //     0xba9c78: ldur            x6, [x0, #7]
    // 0xba9c7c: r0 = LoadInt32Instr(r1)
    //     0xba9c7c: sbfx            x0, x1, #1, #0x1f
    // 0xba9c80: mov             x1, x6
    // 0xba9c84: cmp             x1, x0
    // 0xba9c88: b.hs            #0xba9d18
    // 0xba9c8c: LoadField: r0 = r3->field_f
    //     0xba9c8c: ldur            w0, [x3, #0xf]
    // 0xba9c90: DecompressPointer r0
    //     0xba9c90: add             x0, x0, HEAP, lsl #32
    // 0xba9c94: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xba9c94: add             x16, x0, x6, lsl #2
    //     0xba9c98: ldur            w1, [x16, #0xf]
    // 0xba9c9c: DecompressPointer r1
    //     0xba9c9c: add             x1, x1, HEAP, lsl #32
    // 0xba9ca0: LoadField: r0 = r1->field_b
    //     0xba9ca0: ldur            w0, [x1, #0xb]
    // 0xba9ca4: DecompressPointer r0
    //     0xba9ca4: add             x0, x0, HEAP, lsl #32
    // 0xba9ca8: stp             x5, x2, [SP, #0x10]
    // 0xba9cac: r16 = "remove"
    //     0xba9cac: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xba9cb0: ldr             x16, [x16, #0xff0]
    // 0xba9cb4: stp             x16, x0, [SP]
    // 0xba9cb8: r4 = 0
    //     0xba9cb8: movz            x4, #0
    // 0xba9cbc: ldr             x0, [SP, #0x18]
    // 0xba9cc0: r16 = UnlinkedCall_0x613b5c
    //     0xba9cc0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a668] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xba9cc4: add             x16, x16, #0x668
    // 0xba9cc8: ldp             x5, lr, [x16]
    // 0xba9ccc: blr             lr
    // 0xba9cd0: ldur            x0, [fp, #-8]
    // 0xba9cd4: LoadField: r1 = r0->field_f
    //     0xba9cd4: ldur            w1, [x0, #0xf]
    // 0xba9cd8: DecompressPointer r1
    //     0xba9cd8: add             x1, x1, HEAP, lsl #32
    // 0xba9cdc: r16 = <Object?>
    //     0xba9cdc: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xba9ce0: stp             x1, x16, [SP]
    // 0xba9ce4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba9ce4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba9ce8: r0 = pop()
    //     0xba9ce8: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xba9cec: r0 = Null
    //     0xba9cec: mov             x0, NULL
    // 0xba9cf0: LeaveFrame
    //     0xba9cf0: mov             SP, fp
    //     0xba9cf4: ldp             fp, lr, [SP], #0x10
    // 0xba9cf8: ret
    //     0xba9cf8: ret             
    // 0xba9cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9cfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba9d00: b               #0xba99e0
    // 0xba9d04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba9d04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba9d08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9d08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9d0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9d0c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9d10: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9d10: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9d14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9d14: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xba9d18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xba9d18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xba9d1c, size: 0x9c
    // 0xba9d1c: EnterFrame
    //     0xba9d1c: stp             fp, lr, [SP, #-0x10]!
    //     0xba9d20: mov             fp, SP
    // 0xba9d24: AllocStack(0x38)
    //     0xba9d24: sub             SP, SP, #0x38
    // 0xba9d28: SetupParameters()
    //     0xba9d28: ldr             x0, [fp, #0x10]
    //     0xba9d2c: ldur            w2, [x0, #0x17]
    //     0xba9d30: add             x2, x2, HEAP, lsl #32
    //     0xba9d34: stur            x2, [fp, #-8]
    // 0xba9d38: CheckStackOverflow
    //     0xba9d38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba9d3c: cmp             SP, x16
    //     0xba9d40: b.ls            #0xba9db0
    // 0xba9d44: LoadField: r0 = r2->field_f
    //     0xba9d44: ldur            w0, [x2, #0xf]
    // 0xba9d48: DecompressPointer r0
    //     0xba9d48: add             x0, x0, HEAP, lsl #32
    // 0xba9d4c: r16 = <Object?>
    //     0xba9d4c: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xba9d50: stp             x0, x16, [SP]
    // 0xba9d54: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xba9d54: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xba9d58: r0 = pop()
    //     0xba9d58: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xba9d5c: ldur            x2, [fp, #-8]
    // 0xba9d60: LoadField: r0 = r2->field_f
    //     0xba9d60: ldur            w0, [x2, #0xf]
    // 0xba9d64: DecompressPointer r0
    //     0xba9d64: add             x0, x0, HEAP, lsl #32
    // 0xba9d68: stur            x0, [fp, #-0x10]
    // 0xba9d6c: r1 = Function '<anonymous closure>':.
    //     0xba9d6c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a678] AnonymousClosure: (0xba9760), in [package:customer_app/app/presentation/views/line/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xba6fa4)
    //     0xba9d70: ldr             x1, [x1, #0x678]
    // 0xba9d74: r0 = AllocateClosure()
    //     0xba9d74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xba9d78: stp             x0, NULL, [SP, #0x18]
    // 0xba9d7c: ldur            x16, [fp, #-0x10]
    // 0xba9d80: r30 = true
    //     0xba9d80: add             lr, NULL, #0x20  ; true
    // 0xba9d84: stp             lr, x16, [SP, #8]
    // 0xba9d88: r16 = Instance_RoundedRectangleBorder
    //     0xba9d88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xba9d8c: ldr             x16, [x16, #0xd68]
    // 0xba9d90: str             x16, [SP]
    // 0xba9d94: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xba9d94: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xba9d98: ldr             x4, [x4, #0xb20]
    // 0xba9d9c: r0 = showModalBottomSheet()
    //     0xba9d9c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xba9da0: r0 = Null
    //     0xba9da0: mov             x0, NULL
    // 0xba9da4: LeaveFrame
    //     0xba9da4: mov             SP, fp
    //     0xba9da8: ldp             fp, lr, [SP], #0x10
    // 0xba9dac: ret
    //     0xba9dac: ret             
    // 0xba9db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba9db0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba9db4: b               #0xba9d44
  }
}

// class id: 4030, size: 0x2c, field offset: 0xc
//   const constructor, 
class OffersContentItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80018, size: 0x24
    // 0xc80018: EnterFrame
    //     0xc80018: stp             fp, lr, [SP, #-0x10]!
    //     0xc8001c: mov             fp, SP
    // 0xc80020: mov             x0, x1
    // 0xc80024: r1 = <OffersContentItemView>
    //     0xc80024: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d18] TypeArguments: <OffersContentItemView>
    //     0xc80028: ldr             x1, [x1, #0xd18]
    // 0xc8002c: r0 = _OffersContentItemViewState()
    //     0xc8002c: bl              #0xc8003c  ; Allocate_OffersContentItemViewStateStub -> _OffersContentItemViewState (size=0x14)
    // 0xc80030: LeaveFrame
    //     0xc80030: mov             SP, fp
    //     0xc80034: ldp             fp, lr, [SP], #0x10
    // 0xc80038: ret
    //     0xc80038: ret             
  }
}
