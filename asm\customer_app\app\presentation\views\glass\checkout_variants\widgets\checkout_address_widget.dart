// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart

// class id: 1049363, size: 0x8
class :: {
}

// class id: 3369, size: 0x6c, field offset: 0x14
class CheckoutAddressWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93f114, size: 0x54c
    // 0x93f114: EnterFrame
    //     0x93f114: stp             fp, lr, [SP, #-0x10]!
    //     0x93f118: mov             fp, SP
    // 0x93f11c: AllocStack(0x28)
    //     0x93f11c: sub             SP, SP, #0x28
    // 0x93f120: SetupParameters(CheckoutAddressWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x93f120: stur            x1, [fp, #-8]
    // 0x93f124: CheckStackOverflow
    //     0x93f124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93f128: cmp             SP, x16
    //     0x93f12c: b.ls            #0x93f630
    // 0x93f130: r1 = 1
    //     0x93f130: movz            x1, #0x1
    // 0x93f134: r0 = AllocateContext()
    //     0x93f134: bl              #0x16f6108  ; AllocateContextStub
    // 0x93f138: ldur            x3, [fp, #-8]
    // 0x93f13c: StoreField: r0->field_f = r3
    //     0x93f13c: stur            w3, [x0, #0xf]
    // 0x93f140: LoadField: r1 = r3->field_b
    //     0x93f140: ldur            w1, [x3, #0xb]
    // 0x93f144: DecompressPointer r1
    //     0x93f144: add             x1, x1, HEAP, lsl #32
    // 0x93f148: cmp             w1, NULL
    // 0x93f14c: b.eq            #0x93f638
    // 0x93f150: LoadField: r2 = r1->field_1b
    //     0x93f150: ldur            w2, [x1, #0x1b]
    // 0x93f154: DecompressPointer r2
    //     0x93f154: add             x2, x2, HEAP, lsl #32
    // 0x93f158: LoadField: r4 = r2->field_1b
    //     0x93f158: ldur            w4, [x2, #0x1b]
    // 0x93f15c: DecompressPointer r4
    //     0x93f15c: add             x4, x4, HEAP, lsl #32
    // 0x93f160: cmp             w4, NULL
    // 0x93f164: b.ne            #0x93f170
    // 0x93f168: r1 = Null
    //     0x93f168: mov             x1, NULL
    // 0x93f16c: b               #0x93f188
    // 0x93f170: LoadField: r1 = r4->field_b
    //     0x93f170: ldur            w1, [x4, #0xb]
    // 0x93f174: cbnz            w1, #0x93f180
    // 0x93f178: r2 = false
    //     0x93f178: add             x2, NULL, #0x30  ; false
    // 0x93f17c: b               #0x93f184
    // 0x93f180: r2 = true
    //     0x93f180: add             x2, NULL, #0x20  ; true
    // 0x93f184: mov             x1, x2
    // 0x93f188: cmp             w1, NULL
    // 0x93f18c: b.eq            #0x93f51c
    // 0x93f190: tbnz            w1, #4, #0x93f51c
    // 0x93f194: cmp             w4, NULL
    // 0x93f198: b.ne            #0x93f1a4
    // 0x93f19c: r2 = Null
    //     0x93f19c: mov             x2, NULL
    // 0x93f1a0: b               #0x93f1d0
    // 0x93f1a4: LoadField: r0 = r4->field_b
    //     0x93f1a4: ldur            w0, [x4, #0xb]
    // 0x93f1a8: r1 = LoadInt32Instr(r0)
    //     0x93f1a8: sbfx            x1, x0, #1, #0x1f
    // 0x93f1ac: mov             x0, x1
    // 0x93f1b0: r1 = 0
    //     0x93f1b0: movz            x1, #0
    // 0x93f1b4: cmp             x1, x0
    // 0x93f1b8: b.hs            #0x93f63c
    // 0x93f1bc: LoadField: r0 = r4->field_f
    //     0x93f1bc: ldur            w0, [x4, #0xf]
    // 0x93f1c0: DecompressPointer r0
    //     0x93f1c0: add             x0, x0, HEAP, lsl #32
    // 0x93f1c4: LoadField: r1 = r0->field_f
    //     0x93f1c4: ldur            w1, [x0, #0xf]
    // 0x93f1c8: DecompressPointer r1
    //     0x93f1c8: add             x1, x1, HEAP, lsl #32
    // 0x93f1cc: mov             x2, x1
    // 0x93f1d0: mov             x1, x3
    // 0x93f1d4: r0 = setUpAddress()
    //     0x93f1d4: bl              #0x93f684  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::setUpAddress
    // 0x93f1d8: ldur            x0, [fp, #-8]
    // 0x93f1dc: LoadField: r1 = r0->field_b
    //     0x93f1dc: ldur            w1, [x0, #0xb]
    // 0x93f1e0: DecompressPointer r1
    //     0x93f1e0: add             x1, x1, HEAP, lsl #32
    // 0x93f1e4: cmp             w1, NULL
    // 0x93f1e8: b.eq            #0x93f640
    // 0x93f1ec: LoadField: r2 = r1->field_1f
    //     0x93f1ec: ldur            w2, [x1, #0x1f]
    // 0x93f1f0: DecompressPointer r2
    //     0x93f1f0: add             x2, x2, HEAP, lsl #32
    // 0x93f1f4: str             x2, [SP]
    // 0x93f1f8: r4 = 0
    //     0x93f1f8: movz            x4, #0
    // 0x93f1fc: ldr             x0, [SP]
    // 0x93f200: r16 = UnlinkedCall_0x613b5c
    //     0x93f200: add             x16, PP, #0x56, lsl #12  ; [pp+0x56918] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93f204: add             x16, x16, #0x918
    // 0x93f208: ldp             x5, lr, [x16]
    // 0x93f20c: blr             lr
    // 0x93f210: ldur            x3, [fp, #-8]
    // 0x93f214: LoadField: r0 = r3->field_b
    //     0x93f214: ldur            w0, [x3, #0xb]
    // 0x93f218: DecompressPointer r0
    //     0x93f218: add             x0, x0, HEAP, lsl #32
    // 0x93f21c: cmp             w0, NULL
    // 0x93f220: b.eq            #0x93f644
    // 0x93f224: LoadField: r1 = r0->field_1b
    //     0x93f224: ldur            w1, [x0, #0x1b]
    // 0x93f228: DecompressPointer r1
    //     0x93f228: add             x1, x1, HEAP, lsl #32
    // 0x93f22c: LoadField: r0 = r1->field_b
    //     0x93f22c: ldur            w0, [x1, #0xb]
    // 0x93f230: DecompressPointer r0
    //     0x93f230: add             x0, x0, HEAP, lsl #32
    // 0x93f234: cmp             w0, NULL
    // 0x93f238: b.ne            #0x93f244
    // 0x93f23c: r0 = Null
    //     0x93f23c: mov             x0, NULL
    // 0x93f240: b               #0x93f258
    // 0x93f244: LoadField: r2 = r0->field_7
    //     0x93f244: ldur            w2, [x0, #7]
    // 0x93f248: cbnz            w2, #0x93f254
    // 0x93f24c: r0 = false
    //     0x93f24c: add             x0, NULL, #0x30  ; false
    // 0x93f250: b               #0x93f258
    // 0x93f254: r0 = true
    //     0x93f254: add             x0, NULL, #0x20  ; true
    // 0x93f258: cmp             w0, NULL
    // 0x93f25c: b.ne            #0x93f268
    // 0x93f260: r2 = true
    //     0x93f260: add             x2, NULL, #0x20  ; true
    // 0x93f264: b               #0x93f27c
    // 0x93f268: tbnz            w0, #4, #0x93f278
    // 0x93f26c: r2 = true
    //     0x93f26c: add             x2, NULL, #0x20  ; true
    // 0x93f270: StoreField: r3->field_53 = r2
    //     0x93f270: stur            w2, [x3, #0x53]
    // 0x93f274: b               #0x93f27c
    // 0x93f278: r2 = true
    //     0x93f278: add             x2, NULL, #0x20  ; true
    // 0x93f27c: LoadField: r4 = r1->field_1b
    //     0x93f27c: ldur            w4, [x1, #0x1b]
    // 0x93f280: DecompressPointer r4
    //     0x93f280: add             x4, x4, HEAP, lsl #32
    // 0x93f284: cmp             w4, NULL
    // 0x93f288: b.ne            #0x93f294
    // 0x93f28c: r0 = Null
    //     0x93f28c: mov             x0, NULL
    // 0x93f290: b               #0x93f2f8
    // 0x93f294: LoadField: r0 = r4->field_b
    //     0x93f294: ldur            w0, [x4, #0xb]
    // 0x93f298: r1 = LoadInt32Instr(r0)
    //     0x93f298: sbfx            x1, x0, #1, #0x1f
    // 0x93f29c: mov             x0, x1
    // 0x93f2a0: r1 = 0
    //     0x93f2a0: movz            x1, #0
    // 0x93f2a4: cmp             x1, x0
    // 0x93f2a8: b.hs            #0x93f648
    // 0x93f2ac: LoadField: r0 = r4->field_f
    //     0x93f2ac: ldur            w0, [x4, #0xf]
    // 0x93f2b0: DecompressPointer r0
    //     0x93f2b0: add             x0, x0, HEAP, lsl #32
    // 0x93f2b4: LoadField: r1 = r0->field_f
    //     0x93f2b4: ldur            w1, [x0, #0xf]
    // 0x93f2b8: DecompressPointer r1
    //     0x93f2b8: add             x1, x1, HEAP, lsl #32
    // 0x93f2bc: cmp             w1, NULL
    // 0x93f2c0: b.ne            #0x93f2cc
    // 0x93f2c4: r0 = Null
    //     0x93f2c4: mov             x0, NULL
    // 0x93f2c8: b               #0x93f2f8
    // 0x93f2cc: LoadField: r0 = r1->field_13
    //     0x93f2cc: ldur            w0, [x1, #0x13]
    // 0x93f2d0: DecompressPointer r0
    //     0x93f2d0: add             x0, x0, HEAP, lsl #32
    // 0x93f2d4: cmp             w0, NULL
    // 0x93f2d8: b.ne            #0x93f2e4
    // 0x93f2dc: r0 = Null
    //     0x93f2dc: mov             x0, NULL
    // 0x93f2e0: b               #0x93f2f8
    // 0x93f2e4: LoadField: r1 = r0->field_7
    //     0x93f2e4: ldur            w1, [x0, #7]
    // 0x93f2e8: cbnz            w1, #0x93f2f4
    // 0x93f2ec: r0 = false
    //     0x93f2ec: add             x0, NULL, #0x30  ; false
    // 0x93f2f0: b               #0x93f2f8
    // 0x93f2f4: r0 = true
    //     0x93f2f4: add             x0, NULL, #0x20  ; true
    // 0x93f2f8: cmp             w0, NULL
    // 0x93f2fc: b.eq            #0x93f308
    // 0x93f300: tbnz            w0, #4, #0x93f308
    // 0x93f304: StoreField: r3->field_5b = r2
    //     0x93f304: stur            w2, [x3, #0x5b]
    // 0x93f308: cmp             w4, NULL
    // 0x93f30c: b.ne            #0x93f318
    // 0x93f310: r0 = Null
    //     0x93f310: mov             x0, NULL
    // 0x93f314: b               #0x93f37c
    // 0x93f318: LoadField: r0 = r4->field_b
    //     0x93f318: ldur            w0, [x4, #0xb]
    // 0x93f31c: r1 = LoadInt32Instr(r0)
    //     0x93f31c: sbfx            x1, x0, #1, #0x1f
    // 0x93f320: mov             x0, x1
    // 0x93f324: r1 = 0
    //     0x93f324: movz            x1, #0
    // 0x93f328: cmp             x1, x0
    // 0x93f32c: b.hs            #0x93f64c
    // 0x93f330: LoadField: r0 = r4->field_f
    //     0x93f330: ldur            w0, [x4, #0xf]
    // 0x93f334: DecompressPointer r0
    //     0x93f334: add             x0, x0, HEAP, lsl #32
    // 0x93f338: LoadField: r1 = r0->field_f
    //     0x93f338: ldur            w1, [x0, #0xf]
    // 0x93f33c: DecompressPointer r1
    //     0x93f33c: add             x1, x1, HEAP, lsl #32
    // 0x93f340: cmp             w1, NULL
    // 0x93f344: b.ne            #0x93f350
    // 0x93f348: r0 = Null
    //     0x93f348: mov             x0, NULL
    // 0x93f34c: b               #0x93f37c
    // 0x93f350: LoadField: r0 = r1->field_2b
    //     0x93f350: ldur            w0, [x1, #0x2b]
    // 0x93f354: DecompressPointer r0
    //     0x93f354: add             x0, x0, HEAP, lsl #32
    // 0x93f358: cmp             w0, NULL
    // 0x93f35c: b.ne            #0x93f368
    // 0x93f360: r0 = Null
    //     0x93f360: mov             x0, NULL
    // 0x93f364: b               #0x93f37c
    // 0x93f368: LoadField: r1 = r0->field_7
    //     0x93f368: ldur            w1, [x0, #7]
    // 0x93f36c: cbnz            w1, #0x93f378
    // 0x93f370: r0 = false
    //     0x93f370: add             x0, NULL, #0x30  ; false
    // 0x93f374: b               #0x93f37c
    // 0x93f378: r0 = true
    //     0x93f378: add             x0, NULL, #0x20  ; true
    // 0x93f37c: cmp             w0, NULL
    // 0x93f380: b.eq            #0x93f38c
    // 0x93f384: tbnz            w0, #4, #0x93f38c
    // 0x93f388: StoreField: r3->field_57 = r2
    //     0x93f388: stur            w2, [x3, #0x57]
    // 0x93f38c: cmp             w4, NULL
    // 0x93f390: b.ne            #0x93f39c
    // 0x93f394: r0 = Null
    //     0x93f394: mov             x0, NULL
    // 0x93f398: b               #0x93f400
    // 0x93f39c: LoadField: r0 = r4->field_b
    //     0x93f39c: ldur            w0, [x4, #0xb]
    // 0x93f3a0: r1 = LoadInt32Instr(r0)
    //     0x93f3a0: sbfx            x1, x0, #1, #0x1f
    // 0x93f3a4: mov             x0, x1
    // 0x93f3a8: r1 = 0
    //     0x93f3a8: movz            x1, #0
    // 0x93f3ac: cmp             x1, x0
    // 0x93f3b0: b.hs            #0x93f650
    // 0x93f3b4: LoadField: r0 = r4->field_f
    //     0x93f3b4: ldur            w0, [x4, #0xf]
    // 0x93f3b8: DecompressPointer r0
    //     0x93f3b8: add             x0, x0, HEAP, lsl #32
    // 0x93f3bc: LoadField: r1 = r0->field_f
    //     0x93f3bc: ldur            w1, [x0, #0xf]
    // 0x93f3c0: DecompressPointer r1
    //     0x93f3c0: add             x1, x1, HEAP, lsl #32
    // 0x93f3c4: cmp             w1, NULL
    // 0x93f3c8: b.ne            #0x93f3d4
    // 0x93f3cc: r0 = Null
    //     0x93f3cc: mov             x0, NULL
    // 0x93f3d0: b               #0x93f400
    // 0x93f3d4: LoadField: r0 = r1->field_1b
    //     0x93f3d4: ldur            w0, [x1, #0x1b]
    // 0x93f3d8: DecompressPointer r0
    //     0x93f3d8: add             x0, x0, HEAP, lsl #32
    // 0x93f3dc: cmp             w0, NULL
    // 0x93f3e0: b.ne            #0x93f3ec
    // 0x93f3e4: r0 = Null
    //     0x93f3e4: mov             x0, NULL
    // 0x93f3e8: b               #0x93f400
    // 0x93f3ec: LoadField: r1 = r0->field_7
    //     0x93f3ec: ldur            w1, [x0, #7]
    // 0x93f3f0: cbnz            w1, #0x93f3fc
    // 0x93f3f4: r0 = false
    //     0x93f3f4: add             x0, NULL, #0x30  ; false
    // 0x93f3f8: b               #0x93f400
    // 0x93f3fc: r0 = true
    //     0x93f3fc: add             x0, NULL, #0x20  ; true
    // 0x93f400: cmp             w0, NULL
    // 0x93f404: b.eq            #0x93f410
    // 0x93f408: tbnz            w0, #4, #0x93f410
    // 0x93f40c: StoreField: r3->field_5f = r2
    //     0x93f40c: stur            w2, [x3, #0x5f]
    // 0x93f410: cmp             w4, NULL
    // 0x93f414: b.ne            #0x93f420
    // 0x93f418: r0 = Null
    //     0x93f418: mov             x0, NULL
    // 0x93f41c: b               #0x93f484
    // 0x93f420: LoadField: r0 = r4->field_b
    //     0x93f420: ldur            w0, [x4, #0xb]
    // 0x93f424: r1 = LoadInt32Instr(r0)
    //     0x93f424: sbfx            x1, x0, #1, #0x1f
    // 0x93f428: mov             x0, x1
    // 0x93f42c: r1 = 0
    //     0x93f42c: movz            x1, #0
    // 0x93f430: cmp             x1, x0
    // 0x93f434: b.hs            #0x93f654
    // 0x93f438: LoadField: r0 = r4->field_f
    //     0x93f438: ldur            w0, [x4, #0xf]
    // 0x93f43c: DecompressPointer r0
    //     0x93f43c: add             x0, x0, HEAP, lsl #32
    // 0x93f440: LoadField: r1 = r0->field_f
    //     0x93f440: ldur            w1, [x0, #0xf]
    // 0x93f444: DecompressPointer r1
    //     0x93f444: add             x1, x1, HEAP, lsl #32
    // 0x93f448: cmp             w1, NULL
    // 0x93f44c: b.ne            #0x93f458
    // 0x93f450: r0 = Null
    //     0x93f450: mov             x0, NULL
    // 0x93f454: b               #0x93f484
    // 0x93f458: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x93f458: ldur            w0, [x1, #0x17]
    // 0x93f45c: DecompressPointer r0
    //     0x93f45c: add             x0, x0, HEAP, lsl #32
    // 0x93f460: cmp             w0, NULL
    // 0x93f464: b.ne            #0x93f470
    // 0x93f468: r0 = Null
    //     0x93f468: mov             x0, NULL
    // 0x93f46c: b               #0x93f484
    // 0x93f470: LoadField: r1 = r0->field_7
    //     0x93f470: ldur            w1, [x0, #7]
    // 0x93f474: cbnz            w1, #0x93f480
    // 0x93f478: r0 = false
    //     0x93f478: add             x0, NULL, #0x30  ; false
    // 0x93f47c: b               #0x93f484
    // 0x93f480: r0 = true
    //     0x93f480: add             x0, NULL, #0x20  ; true
    // 0x93f484: cmp             w0, NULL
    // 0x93f488: b.eq            #0x93f494
    // 0x93f48c: tbnz            w0, #4, #0x93f494
    // 0x93f490: StoreField: r3->field_63 = r2
    //     0x93f490: stur            w2, [x3, #0x63]
    // 0x93f494: cmp             w4, NULL
    // 0x93f498: b.ne            #0x93f4a4
    // 0x93f49c: r0 = Null
    //     0x93f49c: mov             x0, NULL
    // 0x93f4a0: b               #0x93f508
    // 0x93f4a4: LoadField: r0 = r4->field_b
    //     0x93f4a4: ldur            w0, [x4, #0xb]
    // 0x93f4a8: r1 = LoadInt32Instr(r0)
    //     0x93f4a8: sbfx            x1, x0, #1, #0x1f
    // 0x93f4ac: mov             x0, x1
    // 0x93f4b0: r1 = 0
    //     0x93f4b0: movz            x1, #0
    // 0x93f4b4: cmp             x1, x0
    // 0x93f4b8: b.hs            #0x93f658
    // 0x93f4bc: LoadField: r0 = r4->field_f
    //     0x93f4bc: ldur            w0, [x4, #0xf]
    // 0x93f4c0: DecompressPointer r0
    //     0x93f4c0: add             x0, x0, HEAP, lsl #32
    // 0x93f4c4: LoadField: r1 = r0->field_f
    //     0x93f4c4: ldur            w1, [x0, #0xf]
    // 0x93f4c8: DecompressPointer r1
    //     0x93f4c8: add             x1, x1, HEAP, lsl #32
    // 0x93f4cc: cmp             w1, NULL
    // 0x93f4d0: b.ne            #0x93f4dc
    // 0x93f4d4: r0 = Null
    //     0x93f4d4: mov             x0, NULL
    // 0x93f4d8: b               #0x93f508
    // 0x93f4dc: LoadField: r0 = r1->field_2f
    //     0x93f4dc: ldur            w0, [x1, #0x2f]
    // 0x93f4e0: DecompressPointer r0
    //     0x93f4e0: add             x0, x0, HEAP, lsl #32
    // 0x93f4e4: cmp             w0, NULL
    // 0x93f4e8: b.ne            #0x93f4f4
    // 0x93f4ec: r0 = Null
    //     0x93f4ec: mov             x0, NULL
    // 0x93f4f0: b               #0x93f508
    // 0x93f4f4: LoadField: r1 = r0->field_7
    //     0x93f4f4: ldur            w1, [x0, #7]
    // 0x93f4f8: cbnz            w1, #0x93f504
    // 0x93f4fc: r0 = false
    //     0x93f4fc: add             x0, NULL, #0x30  ; false
    // 0x93f500: b               #0x93f508
    // 0x93f504: r0 = true
    //     0x93f504: add             x0, NULL, #0x20  ; true
    // 0x93f508: cmp             w0, NULL
    // 0x93f50c: b.eq            #0x93f620
    // 0x93f510: tbnz            w0, #4, #0x93f620
    // 0x93f514: StoreField: r3->field_67 = r2
    //     0x93f514: stur            w2, [x3, #0x67]
    // 0x93f518: b               #0x93f620
    // 0x93f51c: r1 = LoadStaticField(0x878)
    //     0x93f51c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x93f520: ldr             x1, [x1, #0x10f0]
    // 0x93f524: cmp             w1, NULL
    // 0x93f528: b.eq            #0x93f65c
    // 0x93f52c: LoadField: r4 = r1->field_53
    //     0x93f52c: ldur            w4, [x1, #0x53]
    // 0x93f530: DecompressPointer r4
    //     0x93f530: add             x4, x4, HEAP, lsl #32
    // 0x93f534: stur            x4, [fp, #-0x18]
    // 0x93f538: LoadField: r5 = r4->field_7
    //     0x93f538: ldur            w5, [x4, #7]
    // 0x93f53c: DecompressPointer r5
    //     0x93f53c: add             x5, x5, HEAP, lsl #32
    // 0x93f540: mov             x2, x0
    // 0x93f544: stur            x5, [fp, #-0x10]
    // 0x93f548: r1 = Function '<anonymous closure>':.
    //     0x93f548: add             x1, PP, #0x56, lsl #12  ; [pp+0x56928] AnonymousClosure: (0x9402f8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::initState (0x93f114)
    //     0x93f54c: ldr             x1, [x1, #0x928]
    // 0x93f550: r0 = AllocateClosure()
    //     0x93f550: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93f554: ldur            x2, [fp, #-0x10]
    // 0x93f558: mov             x3, x0
    // 0x93f55c: r1 = Null
    //     0x93f55c: mov             x1, NULL
    // 0x93f560: stur            x3, [fp, #-0x10]
    // 0x93f564: cmp             w2, NULL
    // 0x93f568: b.eq            #0x93f588
    // 0x93f56c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93f56c: ldur            w4, [x2, #0x17]
    // 0x93f570: DecompressPointer r4
    //     0x93f570: add             x4, x4, HEAP, lsl #32
    // 0x93f574: r8 = X0
    //     0x93f574: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93f578: LoadField: r9 = r4->field_7
    //     0x93f578: ldur            x9, [x4, #7]
    // 0x93f57c: r3 = Null
    //     0x93f57c: add             x3, PP, #0x56, lsl #12  ; [pp+0x56930] Null
    //     0x93f580: ldr             x3, [x3, #0x930]
    // 0x93f584: blr             x9
    // 0x93f588: ldur            x0, [fp, #-0x18]
    // 0x93f58c: LoadField: r1 = r0->field_b
    //     0x93f58c: ldur            w1, [x0, #0xb]
    // 0x93f590: LoadField: r2 = r0->field_f
    //     0x93f590: ldur            w2, [x0, #0xf]
    // 0x93f594: DecompressPointer r2
    //     0x93f594: add             x2, x2, HEAP, lsl #32
    // 0x93f598: LoadField: r3 = r2->field_b
    //     0x93f598: ldur            w3, [x2, #0xb]
    // 0x93f59c: r2 = LoadInt32Instr(r1)
    //     0x93f59c: sbfx            x2, x1, #1, #0x1f
    // 0x93f5a0: stur            x2, [fp, #-0x20]
    // 0x93f5a4: r1 = LoadInt32Instr(r3)
    //     0x93f5a4: sbfx            x1, x3, #1, #0x1f
    // 0x93f5a8: cmp             x2, x1
    // 0x93f5ac: b.ne            #0x93f5b8
    // 0x93f5b0: mov             x1, x0
    // 0x93f5b4: r0 = _growToNextCapacity()
    //     0x93f5b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93f5b8: ldur            x4, [fp, #-8]
    // 0x93f5bc: ldur            x2, [fp, #-0x18]
    // 0x93f5c0: ldur            x3, [fp, #-0x20]
    // 0x93f5c4: r5 = false
    //     0x93f5c4: add             x5, NULL, #0x30  ; false
    // 0x93f5c8: add             x6, x3, #1
    // 0x93f5cc: lsl             x7, x6, #1
    // 0x93f5d0: StoreField: r2->field_b = r7
    //     0x93f5d0: stur            w7, [x2, #0xb]
    // 0x93f5d4: LoadField: r1 = r2->field_f
    //     0x93f5d4: ldur            w1, [x2, #0xf]
    // 0x93f5d8: DecompressPointer r1
    //     0x93f5d8: add             x1, x1, HEAP, lsl #32
    // 0x93f5dc: ldur            x0, [fp, #-0x10]
    // 0x93f5e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93f5e0: add             x25, x1, x3, lsl #2
    //     0x93f5e4: add             x25, x25, #0xf
    //     0x93f5e8: str             w0, [x25]
    //     0x93f5ec: tbz             w0, #0, #0x93f608
    //     0x93f5f0: ldurb           w16, [x1, #-1]
    //     0x93f5f4: ldurb           w17, [x0, #-1]
    //     0x93f5f8: and             x16, x17, x16, lsr #2
    //     0x93f5fc: tst             x16, HEAP, lsr #32
    //     0x93f600: b.eq            #0x93f608
    //     0x93f604: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93f608: StoreField: r4->field_53 = r5
    //     0x93f608: stur            w5, [x4, #0x53]
    // 0x93f60c: StoreField: r4->field_57 = r5
    //     0x93f60c: stur            w5, [x4, #0x57]
    // 0x93f610: StoreField: r4->field_5b = r5
    //     0x93f610: stur            w5, [x4, #0x5b]
    // 0x93f614: StoreField: r4->field_5f = r5
    //     0x93f614: stur            w5, [x4, #0x5f]
    // 0x93f618: StoreField: r4->field_63 = r5
    //     0x93f618: stur            w5, [x4, #0x63]
    // 0x93f61c: StoreField: r4->field_67 = r5
    //     0x93f61c: stur            w5, [x4, #0x67]
    // 0x93f620: r0 = Null
    //     0x93f620: mov             x0, NULL
    // 0x93f624: LeaveFrame
    //     0x93f624: mov             SP, fp
    //     0x93f628: ldp             fp, lr, [SP], #0x10
    // 0x93f62c: ret
    //     0x93f62c: ret             
    // 0x93f630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93f630: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93f634: b               #0x93f130
    // 0x93f638: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f638: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93f63c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f63c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f640: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f640: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93f644: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f644: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93f648: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f648: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f64c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f64c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f650: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f650: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f654: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f654: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f658: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93f658: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93f65c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93f65c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setUpAddress(/* No info */) {
    // ** addr: 0x93f684, size: 0x698
    // 0x93f684: EnterFrame
    //     0x93f684: stp             fp, lr, [SP, #-0x10]!
    //     0x93f688: mov             fp, SP
    // 0x93f68c: AllocStack(0x40)
    //     0x93f68c: sub             SP, SP, #0x40
    // 0x93f690: SetupParameters(CheckoutAddressWidgetState this /* r1 => r1, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x93f690: stur            x1, [fp, #-0x20]
    //     0x93f694: stur            x2, [fp, #-0x28]
    // 0x93f698: CheckStackOverflow
    //     0x93f698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93f69c: cmp             SP, x16
    //     0x93f6a0: b.ls            #0x93fd08
    // 0x93f6a4: LoadField: r0 = r1->field_3f
    //     0x93f6a4: ldur            w0, [x1, #0x3f]
    // 0x93f6a8: DecompressPointer r0
    //     0x93f6a8: add             x0, x0, HEAP, lsl #32
    // 0x93f6ac: stur            x0, [fp, #-0x18]
    // 0x93f6b0: cmp             w2, NULL
    // 0x93f6b4: b.ne            #0x93f6c0
    // 0x93f6b8: r3 = Null
    //     0x93f6b8: mov             x3, NULL
    // 0x93f6bc: b               #0x93f6c8
    // 0x93f6c0: LoadField: r3 = r2->field_1b
    //     0x93f6c0: ldur            w3, [x2, #0x1b]
    // 0x93f6c4: DecompressPointer r3
    //     0x93f6c4: add             x3, x3, HEAP, lsl #32
    // 0x93f6c8: cmp             w3, NULL
    // 0x93f6cc: b.ne            #0x93f6d4
    // 0x93f6d0: r3 = ""
    //     0x93f6d0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f6d4: stur            x3, [fp, #-0x10]
    // 0x93f6d8: cmp             w2, NULL
    // 0x93f6dc: b.ne            #0x93f6e8
    // 0x93f6e0: r4 = Null
    //     0x93f6e0: mov             x4, NULL
    // 0x93f6e4: b               #0x93f708
    // 0x93f6e8: LoadField: r4 = r2->field_1b
    //     0x93f6e8: ldur            w4, [x2, #0x1b]
    // 0x93f6ec: DecompressPointer r4
    //     0x93f6ec: add             x4, x4, HEAP, lsl #32
    // 0x93f6f0: cmp             w4, NULL
    // 0x93f6f4: b.ne            #0x93f700
    // 0x93f6f8: r4 = Null
    //     0x93f6f8: mov             x4, NULL
    // 0x93f6fc: b               #0x93f708
    // 0x93f700: LoadField: r5 = r4->field_7
    //     0x93f700: ldur            w5, [x4, #7]
    // 0x93f704: mov             x4, x5
    // 0x93f708: cmp             w4, NULL
    // 0x93f70c: b.ne            #0x93f718
    // 0x93f710: r4 = 0
    //     0x93f710: movz            x4, #0
    // 0x93f714: b               #0x93f720
    // 0x93f718: r5 = LoadInt32Instr(r4)
    //     0x93f718: sbfx            x5, x4, #1, #0x1f
    // 0x93f71c: mov             x4, x5
    // 0x93f720: stur            x4, [fp, #-8]
    // 0x93f724: r0 = TextSelection()
    //     0x93f724: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93f728: mov             x1, x0
    // 0x93f72c: ldur            x0, [fp, #-8]
    // 0x93f730: stur            x1, [fp, #-0x30]
    // 0x93f734: ArrayStore: r1[0] = r0  ; List_8
    //     0x93f734: stur            x0, [x1, #0x17]
    // 0x93f738: StoreField: r1->field_1f = r0
    //     0x93f738: stur            x0, [x1, #0x1f]
    // 0x93f73c: r2 = Instance_TextAffinity
    //     0x93f73c: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93f740: StoreField: r1->field_27 = r2
    //     0x93f740: stur            w2, [x1, #0x27]
    // 0x93f744: r3 = false
    //     0x93f744: add             x3, NULL, #0x30  ; false
    // 0x93f748: StoreField: r1->field_2b = r3
    //     0x93f748: stur            w3, [x1, #0x2b]
    // 0x93f74c: StoreField: r1->field_7 = r0
    //     0x93f74c: stur            x0, [x1, #7]
    // 0x93f750: StoreField: r1->field_f = r0
    //     0x93f750: stur            x0, [x1, #0xf]
    // 0x93f754: r0 = TextEditingValue()
    //     0x93f754: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93f758: mov             x1, x0
    // 0x93f75c: ldur            x0, [fp, #-0x10]
    // 0x93f760: StoreField: r1->field_7 = r0
    //     0x93f760: stur            w0, [x1, #7]
    // 0x93f764: ldur            x0, [fp, #-0x30]
    // 0x93f768: StoreField: r1->field_b = r0
    //     0x93f768: stur            w0, [x1, #0xb]
    // 0x93f76c: r0 = Instance_TextRange
    //     0x93f76c: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93f770: StoreField: r1->field_f = r0
    //     0x93f770: stur            w0, [x1, #0xf]
    // 0x93f774: mov             x2, x1
    // 0x93f778: ldur            x1, [fp, #-0x18]
    // 0x93f77c: r0 = value=()
    //     0x93f77c: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93f780: ldur            x1, [fp, #-0x20]
    // 0x93f784: LoadField: r0 = r1->field_3f
    //     0x93f784: ldur            w0, [x1, #0x3f]
    // 0x93f788: DecompressPointer r0
    //     0x93f788: add             x0, x0, HEAP, lsl #32
    // 0x93f78c: LoadField: r2 = r0->field_27
    //     0x93f78c: ldur            w2, [x0, #0x27]
    // 0x93f790: DecompressPointer r2
    //     0x93f790: add             x2, x2, HEAP, lsl #32
    // 0x93f794: LoadField: r0 = r2->field_7
    //     0x93f794: ldur            w0, [x2, #7]
    // 0x93f798: DecompressPointer r0
    //     0x93f798: add             x0, x0, HEAP, lsl #32
    // 0x93f79c: LoadField: r2 = r0->field_7
    //     0x93f79c: ldur            w2, [x0, #7]
    // 0x93f7a0: cmp             w2, #0xc
    // 0x93f7a4: b.ne            #0x93f7dc
    // 0x93f7a8: LoadField: r2 = r1->field_b
    //     0x93f7a8: ldur            w2, [x1, #0xb]
    // 0x93f7ac: DecompressPointer r2
    //     0x93f7ac: add             x2, x2, HEAP, lsl #32
    // 0x93f7b0: cmp             w2, NULL
    // 0x93f7b4: b.eq            #0x93fd10
    // 0x93f7b8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x93f7b8: ldur            w3, [x2, #0x17]
    // 0x93f7bc: DecompressPointer r3
    //     0x93f7bc: add             x3, x3, HEAP, lsl #32
    // 0x93f7c0: stp             x0, x3, [SP]
    // 0x93f7c4: r4 = 0
    //     0x93f7c4: movz            x4, #0
    // 0x93f7c8: ldr             x0, [SP, #8]
    // 0x93f7cc: r16 = UnlinkedCall_0x613b5c
    //     0x93f7cc: add             x16, PP, #0x56, lsl #12  ; [pp+0x56950] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93f7d0: add             x16, x16, #0x950
    // 0x93f7d4: ldp             x5, lr, [x16]
    // 0x93f7d8: blr             lr
    // 0x93f7dc: ldur            x1, [fp, #-0x20]
    // 0x93f7e0: ldur            x0, [fp, #-0x28]
    // 0x93f7e4: LoadField: r2 = r1->field_3b
    //     0x93f7e4: ldur            w2, [x1, #0x3b]
    // 0x93f7e8: DecompressPointer r2
    //     0x93f7e8: add             x2, x2, HEAP, lsl #32
    // 0x93f7ec: stur            x2, [fp, #-0x18]
    // 0x93f7f0: cmp             w0, NULL
    // 0x93f7f4: b.ne            #0x93f800
    // 0x93f7f8: r3 = Null
    //     0x93f7f8: mov             x3, NULL
    // 0x93f7fc: b               #0x93f808
    // 0x93f800: LoadField: r3 = r0->field_2b
    //     0x93f800: ldur            w3, [x0, #0x2b]
    // 0x93f804: DecompressPointer r3
    //     0x93f804: add             x3, x3, HEAP, lsl #32
    // 0x93f808: cmp             w3, NULL
    // 0x93f80c: b.ne            #0x93f814
    // 0x93f810: r3 = ""
    //     0x93f810: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f814: stur            x3, [fp, #-0x10]
    // 0x93f818: cmp             w0, NULL
    // 0x93f81c: b.ne            #0x93f828
    // 0x93f820: r4 = Null
    //     0x93f820: mov             x4, NULL
    // 0x93f824: b               #0x93f848
    // 0x93f828: LoadField: r4 = r0->field_2b
    //     0x93f828: ldur            w4, [x0, #0x2b]
    // 0x93f82c: DecompressPointer r4
    //     0x93f82c: add             x4, x4, HEAP, lsl #32
    // 0x93f830: cmp             w4, NULL
    // 0x93f834: b.ne            #0x93f840
    // 0x93f838: r4 = Null
    //     0x93f838: mov             x4, NULL
    // 0x93f83c: b               #0x93f848
    // 0x93f840: LoadField: r5 = r4->field_7
    //     0x93f840: ldur            w5, [x4, #7]
    // 0x93f844: mov             x4, x5
    // 0x93f848: cmp             w4, NULL
    // 0x93f84c: b.ne            #0x93f858
    // 0x93f850: r4 = 0
    //     0x93f850: movz            x4, #0
    // 0x93f854: b               #0x93f860
    // 0x93f858: r5 = LoadInt32Instr(r4)
    //     0x93f858: sbfx            x5, x4, #1, #0x1f
    // 0x93f85c: mov             x4, x5
    // 0x93f860: stur            x4, [fp, #-8]
    // 0x93f864: r0 = TextSelection()
    //     0x93f864: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93f868: mov             x1, x0
    // 0x93f86c: ldur            x0, [fp, #-8]
    // 0x93f870: stur            x1, [fp, #-0x30]
    // 0x93f874: ArrayStore: r1[0] = r0  ; List_8
    //     0x93f874: stur            x0, [x1, #0x17]
    // 0x93f878: StoreField: r1->field_1f = r0
    //     0x93f878: stur            x0, [x1, #0x1f]
    // 0x93f87c: r2 = Instance_TextAffinity
    //     0x93f87c: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93f880: StoreField: r1->field_27 = r2
    //     0x93f880: stur            w2, [x1, #0x27]
    // 0x93f884: r3 = false
    //     0x93f884: add             x3, NULL, #0x30  ; false
    // 0x93f888: StoreField: r1->field_2b = r3
    //     0x93f888: stur            w3, [x1, #0x2b]
    // 0x93f88c: StoreField: r1->field_7 = r0
    //     0x93f88c: stur            x0, [x1, #7]
    // 0x93f890: StoreField: r1->field_f = r0
    //     0x93f890: stur            x0, [x1, #0xf]
    // 0x93f894: r0 = TextEditingValue()
    //     0x93f894: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93f898: mov             x1, x0
    // 0x93f89c: ldur            x0, [fp, #-0x10]
    // 0x93f8a0: StoreField: r1->field_7 = r0
    //     0x93f8a0: stur            w0, [x1, #7]
    // 0x93f8a4: ldur            x0, [fp, #-0x30]
    // 0x93f8a8: StoreField: r1->field_b = r0
    //     0x93f8a8: stur            w0, [x1, #0xb]
    // 0x93f8ac: r0 = Instance_TextRange
    //     0x93f8ac: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93f8b0: StoreField: r1->field_f = r0
    //     0x93f8b0: stur            w0, [x1, #0xf]
    // 0x93f8b4: mov             x2, x1
    // 0x93f8b8: ldur            x1, [fp, #-0x18]
    // 0x93f8bc: r0 = value=()
    //     0x93f8bc: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93f8c0: ldur            x1, [fp, #-0x20]
    // 0x93f8c4: LoadField: r0 = r1->field_47
    //     0x93f8c4: ldur            w0, [x1, #0x47]
    // 0x93f8c8: DecompressPointer r0
    //     0x93f8c8: add             x0, x0, HEAP, lsl #32
    // 0x93f8cc: ldur            x2, [fp, #-0x28]
    // 0x93f8d0: stur            x0, [fp, #-0x18]
    // 0x93f8d4: cmp             w2, NULL
    // 0x93f8d8: b.ne            #0x93f8e4
    // 0x93f8dc: r3 = Null
    //     0x93f8dc: mov             x3, NULL
    // 0x93f8e0: b               #0x93f8ec
    // 0x93f8e4: LoadField: r3 = r2->field_13
    //     0x93f8e4: ldur            w3, [x2, #0x13]
    // 0x93f8e8: DecompressPointer r3
    //     0x93f8e8: add             x3, x3, HEAP, lsl #32
    // 0x93f8ec: cmp             w3, NULL
    // 0x93f8f0: b.ne            #0x93f8f8
    // 0x93f8f4: r3 = ""
    //     0x93f8f4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f8f8: stur            x3, [fp, #-0x10]
    // 0x93f8fc: cmp             w2, NULL
    // 0x93f900: b.ne            #0x93f90c
    // 0x93f904: r4 = Null
    //     0x93f904: mov             x4, NULL
    // 0x93f908: b               #0x93f92c
    // 0x93f90c: LoadField: r4 = r2->field_13
    //     0x93f90c: ldur            w4, [x2, #0x13]
    // 0x93f910: DecompressPointer r4
    //     0x93f910: add             x4, x4, HEAP, lsl #32
    // 0x93f914: cmp             w4, NULL
    // 0x93f918: b.ne            #0x93f924
    // 0x93f91c: r4 = Null
    //     0x93f91c: mov             x4, NULL
    // 0x93f920: b               #0x93f92c
    // 0x93f924: LoadField: r5 = r4->field_7
    //     0x93f924: ldur            w5, [x4, #7]
    // 0x93f928: mov             x4, x5
    // 0x93f92c: cmp             w4, NULL
    // 0x93f930: b.ne            #0x93f93c
    // 0x93f934: r4 = 0
    //     0x93f934: movz            x4, #0
    // 0x93f938: b               #0x93f944
    // 0x93f93c: r5 = LoadInt32Instr(r4)
    //     0x93f93c: sbfx            x5, x4, #1, #0x1f
    // 0x93f940: mov             x4, x5
    // 0x93f944: stur            x4, [fp, #-8]
    // 0x93f948: r0 = TextSelection()
    //     0x93f948: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93f94c: mov             x1, x0
    // 0x93f950: ldur            x0, [fp, #-8]
    // 0x93f954: stur            x1, [fp, #-0x30]
    // 0x93f958: ArrayStore: r1[0] = r0  ; List_8
    //     0x93f958: stur            x0, [x1, #0x17]
    // 0x93f95c: StoreField: r1->field_1f = r0
    //     0x93f95c: stur            x0, [x1, #0x1f]
    // 0x93f960: r2 = Instance_TextAffinity
    //     0x93f960: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93f964: StoreField: r1->field_27 = r2
    //     0x93f964: stur            w2, [x1, #0x27]
    // 0x93f968: r3 = false
    //     0x93f968: add             x3, NULL, #0x30  ; false
    // 0x93f96c: StoreField: r1->field_2b = r3
    //     0x93f96c: stur            w3, [x1, #0x2b]
    // 0x93f970: StoreField: r1->field_7 = r0
    //     0x93f970: stur            x0, [x1, #7]
    // 0x93f974: StoreField: r1->field_f = r0
    //     0x93f974: stur            x0, [x1, #0xf]
    // 0x93f978: r0 = TextEditingValue()
    //     0x93f978: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93f97c: mov             x1, x0
    // 0x93f980: ldur            x0, [fp, #-0x10]
    // 0x93f984: StoreField: r1->field_7 = r0
    //     0x93f984: stur            w0, [x1, #7]
    // 0x93f988: ldur            x0, [fp, #-0x30]
    // 0x93f98c: StoreField: r1->field_b = r0
    //     0x93f98c: stur            w0, [x1, #0xb]
    // 0x93f990: r0 = Instance_TextRange
    //     0x93f990: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93f994: StoreField: r1->field_f = r0
    //     0x93f994: stur            w0, [x1, #0xf]
    // 0x93f998: mov             x2, x1
    // 0x93f99c: ldur            x1, [fp, #-0x18]
    // 0x93f9a0: r0 = value=()
    //     0x93f9a0: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93f9a4: ldur            x1, [fp, #-0x20]
    // 0x93f9a8: LoadField: r0 = r1->field_37
    //     0x93f9a8: ldur            w0, [x1, #0x37]
    // 0x93f9ac: DecompressPointer r0
    //     0x93f9ac: add             x0, x0, HEAP, lsl #32
    // 0x93f9b0: ldur            x2, [fp, #-0x28]
    // 0x93f9b4: stur            x0, [fp, #-0x18]
    // 0x93f9b8: cmp             w2, NULL
    // 0x93f9bc: b.ne            #0x93f9c8
    // 0x93f9c0: r3 = Null
    //     0x93f9c0: mov             x3, NULL
    // 0x93f9c4: b               #0x93f9d0
    // 0x93f9c8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x93f9c8: ldur            w3, [x2, #0x17]
    // 0x93f9cc: DecompressPointer r3
    //     0x93f9cc: add             x3, x3, HEAP, lsl #32
    // 0x93f9d0: cmp             w3, NULL
    // 0x93f9d4: b.ne            #0x93f9dc
    // 0x93f9d8: r3 = ""
    //     0x93f9d8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93f9dc: stur            x3, [fp, #-0x10]
    // 0x93f9e0: cmp             w2, NULL
    // 0x93f9e4: b.ne            #0x93f9f0
    // 0x93f9e8: r4 = Null
    //     0x93f9e8: mov             x4, NULL
    // 0x93f9ec: b               #0x93fa10
    // 0x93f9f0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93f9f0: ldur            w4, [x2, #0x17]
    // 0x93f9f4: DecompressPointer r4
    //     0x93f9f4: add             x4, x4, HEAP, lsl #32
    // 0x93f9f8: cmp             w4, NULL
    // 0x93f9fc: b.ne            #0x93fa08
    // 0x93fa00: r4 = Null
    //     0x93fa00: mov             x4, NULL
    // 0x93fa04: b               #0x93fa10
    // 0x93fa08: LoadField: r5 = r4->field_7
    //     0x93fa08: ldur            w5, [x4, #7]
    // 0x93fa0c: mov             x4, x5
    // 0x93fa10: cmp             w4, NULL
    // 0x93fa14: b.ne            #0x93fa20
    // 0x93fa18: r4 = 0
    //     0x93fa18: movz            x4, #0
    // 0x93fa1c: b               #0x93fa28
    // 0x93fa20: r5 = LoadInt32Instr(r4)
    //     0x93fa20: sbfx            x5, x4, #1, #0x1f
    // 0x93fa24: mov             x4, x5
    // 0x93fa28: stur            x4, [fp, #-8]
    // 0x93fa2c: r0 = TextSelection()
    //     0x93fa2c: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93fa30: mov             x1, x0
    // 0x93fa34: ldur            x0, [fp, #-8]
    // 0x93fa38: stur            x1, [fp, #-0x30]
    // 0x93fa3c: ArrayStore: r1[0] = r0  ; List_8
    //     0x93fa3c: stur            x0, [x1, #0x17]
    // 0x93fa40: StoreField: r1->field_1f = r0
    //     0x93fa40: stur            x0, [x1, #0x1f]
    // 0x93fa44: r2 = Instance_TextAffinity
    //     0x93fa44: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93fa48: StoreField: r1->field_27 = r2
    //     0x93fa48: stur            w2, [x1, #0x27]
    // 0x93fa4c: r3 = false
    //     0x93fa4c: add             x3, NULL, #0x30  ; false
    // 0x93fa50: StoreField: r1->field_2b = r3
    //     0x93fa50: stur            w3, [x1, #0x2b]
    // 0x93fa54: StoreField: r1->field_7 = r0
    //     0x93fa54: stur            x0, [x1, #7]
    // 0x93fa58: StoreField: r1->field_f = r0
    //     0x93fa58: stur            x0, [x1, #0xf]
    // 0x93fa5c: r0 = TextEditingValue()
    //     0x93fa5c: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93fa60: mov             x1, x0
    // 0x93fa64: ldur            x0, [fp, #-0x10]
    // 0x93fa68: StoreField: r1->field_7 = r0
    //     0x93fa68: stur            w0, [x1, #7]
    // 0x93fa6c: ldur            x0, [fp, #-0x30]
    // 0x93fa70: StoreField: r1->field_b = r0
    //     0x93fa70: stur            w0, [x1, #0xb]
    // 0x93fa74: r0 = Instance_TextRange
    //     0x93fa74: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93fa78: StoreField: r1->field_f = r0
    //     0x93fa78: stur            w0, [x1, #0xf]
    // 0x93fa7c: mov             x2, x1
    // 0x93fa80: ldur            x1, [fp, #-0x18]
    // 0x93fa84: r0 = value=()
    //     0x93fa84: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93fa88: ldur            x1, [fp, #-0x20]
    // 0x93fa8c: LoadField: r0 = r1->field_43
    //     0x93fa8c: ldur            w0, [x1, #0x43]
    // 0x93fa90: DecompressPointer r0
    //     0x93fa90: add             x0, x0, HEAP, lsl #32
    // 0x93fa94: ldur            x2, [fp, #-0x28]
    // 0x93fa98: stur            x0, [fp, #-0x18]
    // 0x93fa9c: cmp             w2, NULL
    // 0x93faa0: b.ne            #0x93faac
    // 0x93faa4: r3 = Null
    //     0x93faa4: mov             x3, NULL
    // 0x93faa8: b               #0x93fab4
    // 0x93faac: LoadField: r3 = r2->field_2f
    //     0x93faac: ldur            w3, [x2, #0x2f]
    // 0x93fab0: DecompressPointer r3
    //     0x93fab0: add             x3, x3, HEAP, lsl #32
    // 0x93fab4: cmp             w3, NULL
    // 0x93fab8: b.ne            #0x93fac0
    // 0x93fabc: r3 = ""
    //     0x93fabc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93fac0: stur            x3, [fp, #-0x10]
    // 0x93fac4: cmp             w2, NULL
    // 0x93fac8: b.ne            #0x93fad4
    // 0x93facc: r2 = Null
    //     0x93facc: mov             x2, NULL
    // 0x93fad0: b               #0x93faf0
    // 0x93fad4: LoadField: r4 = r2->field_2f
    //     0x93fad4: ldur            w4, [x2, #0x2f]
    // 0x93fad8: DecompressPointer r4
    //     0x93fad8: add             x4, x4, HEAP, lsl #32
    // 0x93fadc: cmp             w4, NULL
    // 0x93fae0: b.ne            #0x93faec
    // 0x93fae4: r2 = Null
    //     0x93fae4: mov             x2, NULL
    // 0x93fae8: b               #0x93faf0
    // 0x93faec: LoadField: r2 = r4->field_7
    //     0x93faec: ldur            w2, [x4, #7]
    // 0x93faf0: cmp             w2, NULL
    // 0x93faf4: b.ne            #0x93fb00
    // 0x93faf8: r2 = 0
    //     0x93faf8: movz            x2, #0
    // 0x93fafc: b               #0x93fb08
    // 0x93fb00: r4 = LoadInt32Instr(r2)
    //     0x93fb00: sbfx            x4, x2, #1, #0x1f
    // 0x93fb04: mov             x2, x4
    // 0x93fb08: stur            x2, [fp, #-8]
    // 0x93fb0c: r0 = TextSelection()
    //     0x93fb0c: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93fb10: mov             x1, x0
    // 0x93fb14: ldur            x0, [fp, #-8]
    // 0x93fb18: stur            x1, [fp, #-0x28]
    // 0x93fb1c: ArrayStore: r1[0] = r0  ; List_8
    //     0x93fb1c: stur            x0, [x1, #0x17]
    // 0x93fb20: StoreField: r1->field_1f = r0
    //     0x93fb20: stur            x0, [x1, #0x1f]
    // 0x93fb24: r2 = Instance_TextAffinity
    //     0x93fb24: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93fb28: StoreField: r1->field_27 = r2
    //     0x93fb28: stur            w2, [x1, #0x27]
    // 0x93fb2c: r3 = false
    //     0x93fb2c: add             x3, NULL, #0x30  ; false
    // 0x93fb30: StoreField: r1->field_2b = r3
    //     0x93fb30: stur            w3, [x1, #0x2b]
    // 0x93fb34: StoreField: r1->field_7 = r0
    //     0x93fb34: stur            x0, [x1, #7]
    // 0x93fb38: StoreField: r1->field_f = r0
    //     0x93fb38: stur            x0, [x1, #0xf]
    // 0x93fb3c: r0 = TextEditingValue()
    //     0x93fb3c: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93fb40: mov             x1, x0
    // 0x93fb44: ldur            x0, [fp, #-0x10]
    // 0x93fb48: StoreField: r1->field_7 = r0
    //     0x93fb48: stur            w0, [x1, #7]
    // 0x93fb4c: ldur            x0, [fp, #-0x28]
    // 0x93fb50: StoreField: r1->field_b = r0
    //     0x93fb50: stur            w0, [x1, #0xb]
    // 0x93fb54: r0 = Instance_TextRange
    //     0x93fb54: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93fb58: StoreField: r1->field_f = r0
    //     0x93fb58: stur            w0, [x1, #0xf]
    // 0x93fb5c: mov             x2, x1
    // 0x93fb60: ldur            x1, [fp, #-0x18]
    // 0x93fb64: r0 = value=()
    //     0x93fb64: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93fb68: ldur            x0, [fp, #-0x20]
    // 0x93fb6c: LoadField: r3 = r0->field_33
    //     0x93fb6c: ldur            w3, [x0, #0x33]
    // 0x93fb70: DecompressPointer r3
    //     0x93fb70: add             x3, x3, HEAP, lsl #32
    // 0x93fb74: stur            x3, [fp, #-0x28]
    // 0x93fb78: LoadField: r1 = r0->field_b
    //     0x93fb78: ldur            w1, [x0, #0xb]
    // 0x93fb7c: DecompressPointer r1
    //     0x93fb7c: add             x1, x1, HEAP, lsl #32
    // 0x93fb80: cmp             w1, NULL
    // 0x93fb84: b.eq            #0x93fd14
    // 0x93fb88: LoadField: r4 = r1->field_1b
    //     0x93fb88: ldur            w4, [x1, #0x1b]
    // 0x93fb8c: DecompressPointer r4
    //     0x93fb8c: add             x4, x4, HEAP, lsl #32
    // 0x93fb90: stur            x4, [fp, #-0x18]
    // 0x93fb94: LoadField: r1 = r4->field_b
    //     0x93fb94: ldur            w1, [x4, #0xb]
    // 0x93fb98: DecompressPointer r1
    //     0x93fb98: add             x1, x1, HEAP, lsl #32
    // 0x93fb9c: cmp             w1, NULL
    // 0x93fba0: b.ne            #0x93fbac
    // 0x93fba4: r5 = ""
    //     0x93fba4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93fba8: b               #0x93fbb0
    // 0x93fbac: mov             x5, x1
    // 0x93fbb0: stur            x5, [fp, #-0x10]
    // 0x93fbb4: r1 = Null
    //     0x93fbb4: mov             x1, NULL
    // 0x93fbb8: r2 = 6
    //     0x93fbb8: movz            x2, #0x6
    // 0x93fbbc: r0 = AllocateArray()
    //     0x93fbbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93fbc0: mov             x1, x0
    // 0x93fbc4: ldur            x0, [fp, #-0x10]
    // 0x93fbc8: StoreField: r1->field_f = r0
    //     0x93fbc8: stur            w0, [x1, #0xf]
    // 0x93fbcc: r16 = " "
    //     0x93fbcc: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x93fbd0: StoreField: r1->field_13 = r16
    //     0x93fbd0: stur            w16, [x1, #0x13]
    // 0x93fbd4: ldur            x0, [fp, #-0x18]
    // 0x93fbd8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x93fbd8: ldur            w2, [x0, #0x17]
    // 0x93fbdc: DecompressPointer r2
    //     0x93fbdc: add             x2, x2, HEAP, lsl #32
    // 0x93fbe0: cmp             w2, NULL
    // 0x93fbe4: b.ne            #0x93fbec
    // 0x93fbe8: r2 = ""
    //     0x93fbe8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93fbec: ldur            x0, [fp, #-0x20]
    // 0x93fbf0: ArrayStore: r1[0] = r2  ; List_4
    //     0x93fbf0: stur            w2, [x1, #0x17]
    // 0x93fbf4: str             x1, [SP]
    // 0x93fbf8: r0 = _interpolate()
    //     0x93fbf8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x93fbfc: mov             x1, x0
    // 0x93fc00: r0 = trim()
    //     0x93fc00: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x93fc04: ldur            x1, [fp, #-0x20]
    // 0x93fc08: stur            x0, [fp, #-0x10]
    // 0x93fc0c: LoadField: r2 = r1->field_b
    //     0x93fc0c: ldur            w2, [x1, #0xb]
    // 0x93fc10: DecompressPointer r2
    //     0x93fc10: add             x2, x2, HEAP, lsl #32
    // 0x93fc14: cmp             w2, NULL
    // 0x93fc18: b.eq            #0x93fd18
    // 0x93fc1c: LoadField: r3 = r2->field_1b
    //     0x93fc1c: ldur            w3, [x2, #0x1b]
    // 0x93fc20: DecompressPointer r3
    //     0x93fc20: add             x3, x3, HEAP, lsl #32
    // 0x93fc24: LoadField: r2 = r3->field_b
    //     0x93fc24: ldur            w2, [x3, #0xb]
    // 0x93fc28: DecompressPointer r2
    //     0x93fc28: add             x2, x2, HEAP, lsl #32
    // 0x93fc2c: cmp             w2, NULL
    // 0x93fc30: b.ne            #0x93fc3c
    // 0x93fc34: r2 = Null
    //     0x93fc34: mov             x2, NULL
    // 0x93fc38: b               #0x93fc44
    // 0x93fc3c: LoadField: r4 = r2->field_7
    //     0x93fc3c: ldur            w4, [x2, #7]
    // 0x93fc40: mov             x2, x4
    // 0x93fc44: cmp             w2, NULL
    // 0x93fc48: b.ne            #0x93fc88
    // 0x93fc4c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x93fc4c: ldur            w2, [x3, #0x17]
    // 0x93fc50: DecompressPointer r2
    //     0x93fc50: add             x2, x2, HEAP, lsl #32
    // 0x93fc54: cmp             w2, NULL
    // 0x93fc58: b.ne            #0x93fc64
    // 0x93fc5c: r2 = Null
    //     0x93fc5c: mov             x2, NULL
    // 0x93fc60: b               #0x93fc6c
    // 0x93fc64: LoadField: r3 = r2->field_7
    //     0x93fc64: ldur            w3, [x2, #7]
    // 0x93fc68: mov             x2, x3
    // 0x93fc6c: cmp             w2, NULL
    // 0x93fc70: b.ne            #0x93fc7c
    // 0x93fc74: r2 = 0
    //     0x93fc74: movz            x2, #0
    // 0x93fc78: b               #0x93fc90
    // 0x93fc7c: r3 = LoadInt32Instr(r2)
    //     0x93fc7c: sbfx            x3, x2, #1, #0x1f
    // 0x93fc80: mov             x2, x3
    // 0x93fc84: b               #0x93fc90
    // 0x93fc88: r3 = LoadInt32Instr(r2)
    //     0x93fc88: sbfx            x3, x2, #1, #0x1f
    // 0x93fc8c: mov             x2, x3
    // 0x93fc90: stur            x2, [fp, #-8]
    // 0x93fc94: r0 = TextSelection()
    //     0x93fc94: bl              #0x6772f4  ; AllocateTextSelectionStub -> TextSelection (size=0x30)
    // 0x93fc98: mov             x1, x0
    // 0x93fc9c: ldur            x0, [fp, #-8]
    // 0x93fca0: stur            x1, [fp, #-0x18]
    // 0x93fca4: ArrayStore: r1[0] = r0  ; List_8
    //     0x93fca4: stur            x0, [x1, #0x17]
    // 0x93fca8: StoreField: r1->field_1f = r0
    //     0x93fca8: stur            x0, [x1, #0x1f]
    // 0x93fcac: r2 = Instance_TextAffinity
    //     0x93fcac: ldr             x2, [PP, #0x4710]  ; [pp+0x4710] Obj!TextAffinity@d764e1
    // 0x93fcb0: StoreField: r1->field_27 = r2
    //     0x93fcb0: stur            w2, [x1, #0x27]
    // 0x93fcb4: r2 = false
    //     0x93fcb4: add             x2, NULL, #0x30  ; false
    // 0x93fcb8: StoreField: r1->field_2b = r2
    //     0x93fcb8: stur            w2, [x1, #0x2b]
    // 0x93fcbc: StoreField: r1->field_7 = r0
    //     0x93fcbc: stur            x0, [x1, #7]
    // 0x93fcc0: StoreField: r1->field_f = r0
    //     0x93fcc0: stur            x0, [x1, #0xf]
    // 0x93fcc4: r0 = TextEditingValue()
    //     0x93fcc4: bl              #0x6c6790  ; AllocateTextEditingValueStub -> TextEditingValue (size=0x14)
    // 0x93fcc8: mov             x1, x0
    // 0x93fccc: ldur            x0, [fp, #-0x10]
    // 0x93fcd0: StoreField: r1->field_7 = r0
    //     0x93fcd0: stur            w0, [x1, #7]
    // 0x93fcd4: ldur            x0, [fp, #-0x18]
    // 0x93fcd8: StoreField: r1->field_b = r0
    //     0x93fcd8: stur            w0, [x1, #0xb]
    // 0x93fcdc: r0 = Instance_TextRange
    //     0x93fcdc: ldr             x0, [PP, #0x70f8]  ; [pp+0x70f8] Obj!TextRange@d68bd1
    // 0x93fce0: StoreField: r1->field_f = r0
    //     0x93fce0: stur            w0, [x1, #0xf]
    // 0x93fce4: mov             x2, x1
    // 0x93fce8: ldur            x1, [fp, #-0x28]
    // 0x93fcec: r0 = value=()
    //     0x93fcec: bl              #0x63bdfc  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x93fcf0: ldur            x1, [fp, #-0x20]
    // 0x93fcf4: r0 = _validateAllFields()
    //     0x93fcf4: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0x93fcf8: r0 = Null
    //     0x93fcf8: mov             x0, NULL
    // 0x93fcfc: LeaveFrame
    //     0x93fcfc: mov             SP, fp
    //     0x93fd00: ldp             fp, lr, [SP], #0x10
    // 0x93fd04: ret
    //     0x93fd04: ret             
    // 0x93fd08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93fd08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93fd0c: b               #0x93f6a4
    // 0x93fd10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93fd10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93fd14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93fd14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93fd18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93fd18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _validateAllFields(/* No info */) {
    // ** addr: 0x93fd1c, size: 0x308
    // 0x93fd1c: EnterFrame
    //     0x93fd1c: stp             fp, lr, [SP, #-0x10]!
    //     0x93fd20: mov             fp, SP
    // 0x93fd24: AllocStack(0x38)
    //     0x93fd24: sub             SP, SP, #0x38
    // 0x93fd28: SetupParameters(CheckoutAddressWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x93fd28: stur            x1, [fp, #-8]
    // 0x93fd2c: CheckStackOverflow
    //     0x93fd2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93fd30: cmp             SP, x16
    //     0x93fd34: b.ls            #0x940014
    // 0x93fd38: r1 = 2
    //     0x93fd38: movz            x1, #0x2
    // 0x93fd3c: r0 = AllocateContext()
    //     0x93fd3c: bl              #0x16f6108  ; AllocateContextStub
    // 0x93fd40: mov             x3, x0
    // 0x93fd44: ldur            x0, [fp, #-8]
    // 0x93fd48: stur            x3, [fp, #-0x38]
    // 0x93fd4c: StoreField: r3->field_f = r0
    //     0x93fd4c: stur            w0, [x3, #0xf]
    // 0x93fd50: LoadField: r1 = r0->field_33
    //     0x93fd50: ldur            w1, [x0, #0x33]
    // 0x93fd54: DecompressPointer r1
    //     0x93fd54: add             x1, x1, HEAP, lsl #32
    // 0x93fd58: LoadField: r2 = r1->field_27
    //     0x93fd58: ldur            w2, [x1, #0x27]
    // 0x93fd5c: DecompressPointer r2
    //     0x93fd5c: add             x2, x2, HEAP, lsl #32
    // 0x93fd60: LoadField: r1 = r2->field_7
    //     0x93fd60: ldur            w1, [x2, #7]
    // 0x93fd64: DecompressPointer r1
    //     0x93fd64: add             x1, x1, HEAP, lsl #32
    // 0x93fd68: LoadField: r2 = r1->field_7
    //     0x93fd68: ldur            w2, [x1, #7]
    // 0x93fd6c: r4 = LoadInt32Instr(r2)
    //     0x93fd6c: sbfx            x4, x2, #1, #0x1f
    // 0x93fd70: stur            x4, [fp, #-0x30]
    // 0x93fd74: LoadField: r1 = r0->field_3f
    //     0x93fd74: ldur            w1, [x0, #0x3f]
    // 0x93fd78: DecompressPointer r1
    //     0x93fd78: add             x1, x1, HEAP, lsl #32
    // 0x93fd7c: LoadField: r2 = r1->field_27
    //     0x93fd7c: ldur            w2, [x1, #0x27]
    // 0x93fd80: DecompressPointer r2
    //     0x93fd80: add             x2, x2, HEAP, lsl #32
    // 0x93fd84: LoadField: r1 = r2->field_7
    //     0x93fd84: ldur            w1, [x2, #7]
    // 0x93fd88: DecompressPointer r1
    //     0x93fd88: add             x1, x1, HEAP, lsl #32
    // 0x93fd8c: LoadField: r2 = r1->field_7
    //     0x93fd8c: ldur            w2, [x1, #7]
    // 0x93fd90: cmp             w2, #0xc
    // 0x93fd94: b.ne            #0x93fe0c
    // 0x93fd98: LoadField: r1 = r0->field_b
    //     0x93fd98: ldur            w1, [x0, #0xb]
    // 0x93fd9c: DecompressPointer r1
    //     0x93fd9c: add             x1, x1, HEAP, lsl #32
    // 0x93fda0: cmp             w1, NULL
    // 0x93fda4: b.eq            #0x94001c
    // 0x93fda8: LoadField: r2 = r1->field_13
    //     0x93fda8: ldur            w2, [x1, #0x13]
    // 0x93fdac: DecompressPointer r2
    //     0x93fdac: add             x2, x2, HEAP, lsl #32
    // 0x93fdb0: LoadField: r1 = r2->field_b
    //     0x93fdb0: ldur            w1, [x2, #0xb]
    // 0x93fdb4: DecompressPointer r1
    //     0x93fdb4: add             x1, x1, HEAP, lsl #32
    // 0x93fdb8: cmp             w1, NULL
    // 0x93fdbc: b.ne            #0x93fdc8
    // 0x93fdc0: r1 = Null
    //     0x93fdc0: mov             x1, NULL
    // 0x93fdc4: b               #0x93fdf8
    // 0x93fdc8: LoadField: r2 = r1->field_13
    //     0x93fdc8: ldur            w2, [x1, #0x13]
    // 0x93fdcc: DecompressPointer r2
    //     0x93fdcc: add             x2, x2, HEAP, lsl #32
    // 0x93fdd0: cmp             w2, NULL
    // 0x93fdd4: b.ne            #0x93fde0
    // 0x93fdd8: r1 = Null
    //     0x93fdd8: mov             x1, NULL
    // 0x93fddc: b               #0x93fdf8
    // 0x93fde0: LoadField: r1 = r2->field_7
    //     0x93fde0: ldur            w1, [x2, #7]
    // 0x93fde4: cbnz            w1, #0x93fdf0
    // 0x93fde8: r2 = false
    //     0x93fde8: add             x2, NULL, #0x30  ; false
    // 0x93fdec: b               #0x93fdf4
    // 0x93fdf0: r2 = true
    //     0x93fdf0: add             x2, NULL, #0x20  ; true
    // 0x93fdf4: mov             x1, x2
    // 0x93fdf8: cmp             w1, NULL
    // 0x93fdfc: b.ne            #0x93fe04
    // 0x93fe00: r1 = false
    //     0x93fe00: add             x1, NULL, #0x30  ; false
    // 0x93fe04: mov             x5, x1
    // 0x93fe08: b               #0x93fe10
    // 0x93fe0c: r5 = false
    //     0x93fe0c: add             x5, NULL, #0x30  ; false
    // 0x93fe10: stur            x5, [fp, #-0x28]
    // 0x93fe14: LoadField: r1 = r0->field_3b
    //     0x93fe14: ldur            w1, [x0, #0x3b]
    // 0x93fe18: DecompressPointer r1
    //     0x93fe18: add             x1, x1, HEAP, lsl #32
    // 0x93fe1c: LoadField: r2 = r1->field_27
    //     0x93fe1c: ldur            w2, [x1, #0x27]
    // 0x93fe20: DecompressPointer r2
    //     0x93fe20: add             x2, x2, HEAP, lsl #32
    // 0x93fe24: LoadField: r1 = r2->field_7
    //     0x93fe24: ldur            w1, [x2, #7]
    // 0x93fe28: DecompressPointer r1
    //     0x93fe28: add             x1, x1, HEAP, lsl #32
    // 0x93fe2c: LoadField: r6 = r1->field_7
    //     0x93fe2c: ldur            w6, [x1, #7]
    // 0x93fe30: stur            x6, [fp, #-0x20]
    // 0x93fe34: LoadField: r1 = r0->field_47
    //     0x93fe34: ldur            w1, [x0, #0x47]
    // 0x93fe38: DecompressPointer r1
    //     0x93fe38: add             x1, x1, HEAP, lsl #32
    // 0x93fe3c: LoadField: r2 = r1->field_27
    //     0x93fe3c: ldur            w2, [x1, #0x27]
    // 0x93fe40: DecompressPointer r2
    //     0x93fe40: add             x2, x2, HEAP, lsl #32
    // 0x93fe44: LoadField: r1 = r2->field_7
    //     0x93fe44: ldur            w1, [x2, #7]
    // 0x93fe48: DecompressPointer r1
    //     0x93fe48: add             x1, x1, HEAP, lsl #32
    // 0x93fe4c: LoadField: r2 = r1->field_7
    //     0x93fe4c: ldur            w2, [x1, #7]
    // 0x93fe50: r7 = LoadInt32Instr(r2)
    //     0x93fe50: sbfx            x7, x2, #1, #0x1f
    // 0x93fe54: stur            x7, [fp, #-0x18]
    // 0x93fe58: LoadField: r1 = r0->field_37
    //     0x93fe58: ldur            w1, [x0, #0x37]
    // 0x93fe5c: DecompressPointer r1
    //     0x93fe5c: add             x1, x1, HEAP, lsl #32
    // 0x93fe60: LoadField: r2 = r1->field_27
    //     0x93fe60: ldur            w2, [x1, #0x27]
    // 0x93fe64: DecompressPointer r2
    //     0x93fe64: add             x2, x2, HEAP, lsl #32
    // 0x93fe68: LoadField: r1 = r2->field_7
    //     0x93fe68: ldur            w1, [x2, #7]
    // 0x93fe6c: DecompressPointer r1
    //     0x93fe6c: add             x1, x1, HEAP, lsl #32
    // 0x93fe70: LoadField: r2 = r1->field_7
    //     0x93fe70: ldur            w2, [x1, #7]
    // 0x93fe74: cbnz            w2, #0x93fe80
    // 0x93fe78: r8 = true
    //     0x93fe78: add             x8, NULL, #0x20  ; true
    // 0x93fe7c: b               #0x93fe98
    // 0x93fe80: r1 = LoadInt32Instr(r2)
    //     0x93fe80: sbfx            x1, x2, #1, #0x1f
    // 0x93fe84: cmp             x1, #5
    // 0x93fe88: r16 = true
    //     0x93fe88: add             x16, NULL, #0x20  ; true
    // 0x93fe8c: r17 = false
    //     0x93fe8c: add             x17, NULL, #0x30  ; false
    // 0x93fe90: csel            x2, x16, x17, ge
    // 0x93fe94: mov             x8, x2
    // 0x93fe98: stur            x8, [fp, #-0x10]
    // 0x93fe9c: LoadField: r1 = r0->field_43
    //     0x93fe9c: ldur            w1, [x0, #0x43]
    // 0x93fea0: DecompressPointer r1
    //     0x93fea0: add             x1, x1, HEAP, lsl #32
    // 0x93fea4: LoadField: r2 = r1->field_27
    //     0x93fea4: ldur            w2, [x1, #0x27]
    // 0x93fea8: DecompressPointer r2
    //     0x93fea8: add             x2, x2, HEAP, lsl #32
    // 0x93feac: LoadField: r1 = r2->field_7
    //     0x93feac: ldur            w1, [x2, #7]
    // 0x93feb0: DecompressPointer r1
    //     0x93feb0: add             x1, x1, HEAP, lsl #32
    // 0x93feb4: LoadField: r2 = r1->field_7
    //     0x93feb4: ldur            w2, [x1, #7]
    // 0x93feb8: cbnz            w2, #0x93fec8
    // 0x93febc: mov             x0, x4
    // 0x93fec0: r1 = true
    //     0x93fec0: add             x1, NULL, #0x20  ; true
    // 0x93fec4: b               #0x93fedc
    // 0x93fec8: mov             x2, x1
    // 0x93fecc: mov             x1, x0
    // 0x93fed0: r0 = _isValidPhoneNumber()
    //     0x93fed0: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x93fed4: mov             x1, x0
    // 0x93fed8: ldur            x0, [fp, #-0x30]
    // 0x93fedc: cmp             x0, #1
    // 0x93fee0: b.le            #0x93ff10
    // 0x93fee4: ldur            x0, [fp, #-0x28]
    // 0x93fee8: tbnz            w0, #4, #0x93ff10
    // 0x93feec: ldur            x0, [fp, #-0x20]
    // 0x93fef0: cbz             w0, #0x93ff10
    // 0x93fef4: ldur            x0, [fp, #-0x18]
    // 0x93fef8: cmp             x0, #0x14
    // 0x93fefc: b.lt            #0x93ff10
    // 0x93ff00: ldur            x0, [fp, #-0x10]
    // 0x93ff04: tbnz            w0, #4, #0x93ff10
    // 0x93ff08: mov             x0, x1
    // 0x93ff0c: b               #0x93ff14
    // 0x93ff10: r0 = false
    //     0x93ff10: add             x0, NULL, #0x30  ; false
    // 0x93ff14: ldur            x2, [fp, #-0x38]
    // 0x93ff18: StoreField: r2->field_13 = r0
    //     0x93ff18: stur            w0, [x2, #0x13]
    // 0x93ff1c: r0 = LoadStaticField(0x878)
    //     0x93ff1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93ff20: ldr             x0, [x0, #0x10f0]
    // 0x93ff24: cmp             w0, NULL
    // 0x93ff28: b.eq            #0x940020
    // 0x93ff2c: LoadField: r3 = r0->field_53
    //     0x93ff2c: ldur            w3, [x0, #0x53]
    // 0x93ff30: DecompressPointer r3
    //     0x93ff30: add             x3, x3, HEAP, lsl #32
    // 0x93ff34: stur            x3, [fp, #-0x20]
    // 0x93ff38: LoadField: r0 = r3->field_7
    //     0x93ff38: ldur            w0, [x3, #7]
    // 0x93ff3c: DecompressPointer r0
    //     0x93ff3c: add             x0, x0, HEAP, lsl #32
    // 0x93ff40: stur            x0, [fp, #-0x10]
    // 0x93ff44: r1 = Function '<anonymous closure>':.
    //     0x93ff44: add             x1, PP, #0x56, lsl #12  ; [pp+0x568a0] AnonymousClosure: (0x9401dc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields (0x93fd1c)
    //     0x93ff48: ldr             x1, [x1, #0x8a0]
    // 0x93ff4c: r0 = AllocateClosure()
    //     0x93ff4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ff50: ldur            x2, [fp, #-0x10]
    // 0x93ff54: mov             x3, x0
    // 0x93ff58: r1 = Null
    //     0x93ff58: mov             x1, NULL
    // 0x93ff5c: stur            x3, [fp, #-0x10]
    // 0x93ff60: cmp             w2, NULL
    // 0x93ff64: b.eq            #0x93ff84
    // 0x93ff68: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93ff68: ldur            w4, [x2, #0x17]
    // 0x93ff6c: DecompressPointer r4
    //     0x93ff6c: add             x4, x4, HEAP, lsl #32
    // 0x93ff70: r8 = X0
    //     0x93ff70: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93ff74: LoadField: r9 = r4->field_7
    //     0x93ff74: ldur            x9, [x4, #7]
    // 0x93ff78: r3 = Null
    //     0x93ff78: add             x3, PP, #0x56, lsl #12  ; [pp+0x568a8] Null
    //     0x93ff7c: ldr             x3, [x3, #0x8a8]
    // 0x93ff80: blr             x9
    // 0x93ff84: ldur            x0, [fp, #-0x20]
    // 0x93ff88: LoadField: r1 = r0->field_b
    //     0x93ff88: ldur            w1, [x0, #0xb]
    // 0x93ff8c: LoadField: r2 = r0->field_f
    //     0x93ff8c: ldur            w2, [x0, #0xf]
    // 0x93ff90: DecompressPointer r2
    //     0x93ff90: add             x2, x2, HEAP, lsl #32
    // 0x93ff94: LoadField: r3 = r2->field_b
    //     0x93ff94: ldur            w3, [x2, #0xb]
    // 0x93ff98: r2 = LoadInt32Instr(r1)
    //     0x93ff98: sbfx            x2, x1, #1, #0x1f
    // 0x93ff9c: stur            x2, [fp, #-0x18]
    // 0x93ffa0: r1 = LoadInt32Instr(r3)
    //     0x93ffa0: sbfx            x1, x3, #1, #0x1f
    // 0x93ffa4: cmp             x2, x1
    // 0x93ffa8: b.ne            #0x93ffb4
    // 0x93ffac: mov             x1, x0
    // 0x93ffb0: r0 = _growToNextCapacity()
    //     0x93ffb0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93ffb4: ldur            x0, [fp, #-0x20]
    // 0x93ffb8: ldur            x2, [fp, #-0x18]
    // 0x93ffbc: add             x1, x2, #1
    // 0x93ffc0: lsl             x3, x1, #1
    // 0x93ffc4: StoreField: r0->field_b = r3
    //     0x93ffc4: stur            w3, [x0, #0xb]
    // 0x93ffc8: LoadField: r1 = r0->field_f
    //     0x93ffc8: ldur            w1, [x0, #0xf]
    // 0x93ffcc: DecompressPointer r1
    //     0x93ffcc: add             x1, x1, HEAP, lsl #32
    // 0x93ffd0: ldur            x0, [fp, #-0x10]
    // 0x93ffd4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x93ffd4: add             x25, x1, x2, lsl #2
    //     0x93ffd8: add             x25, x25, #0xf
    //     0x93ffdc: str             w0, [x25]
    //     0x93ffe0: tbz             w0, #0, #0x93fffc
    //     0x93ffe4: ldurb           w16, [x1, #-1]
    //     0x93ffe8: ldurb           w17, [x0, #-1]
    //     0x93ffec: and             x16, x17, x16, lsr #2
    //     0x93fff0: tst             x16, HEAP, lsr #32
    //     0x93fff4: b.eq            #0x93fffc
    //     0x93fff8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93fffc: ldur            x1, [fp, #-8]
    // 0x940000: r0 = _checkFirstTimeInput()
    //     0x940000: bl              #0x940024  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_checkFirstTimeInput
    // 0x940004: r0 = Null
    //     0x940004: mov             x0, NULL
    // 0x940008: LeaveFrame
    //     0x940008: mov             SP, fp
    //     0x94000c: ldp             fp, lr, [SP], #0x10
    // 0x940010: ret
    //     0x940010: ret             
    // 0x940014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x940014: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x940018: b               #0x93fd38
    // 0x94001c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94001c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x940020: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x940020: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _checkFirstTimeInput(/* No info */) {
    // ** addr: 0x940024, size: 0x1b8
    // 0x940024: EnterFrame
    //     0x940024: stp             fp, lr, [SP, #-0x10]!
    //     0x940028: mov             fp, SP
    // 0x94002c: AllocStack(0x48)
    //     0x94002c: sub             SP, SP, #0x48
    // 0x940030: r0 = 12
    //     0x940030: movz            x0, #0xc
    // 0x940034: mov             x3, x1
    // 0x940038: stur            x1, [fp, #-0x38]
    // 0x94003c: CheckStackOverflow
    //     0x94003c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940040: cmp             SP, x16
    //     0x940044: b.ls            #0x9401d0
    // 0x940048: LoadField: r1 = r3->field_33
    //     0x940048: ldur            w1, [x3, #0x33]
    // 0x94004c: DecompressPointer r1
    //     0x94004c: add             x1, x1, HEAP, lsl #32
    // 0x940050: LoadField: r2 = r1->field_27
    //     0x940050: ldur            w2, [x1, #0x27]
    // 0x940054: DecompressPointer r2
    //     0x940054: add             x2, x2, HEAP, lsl #32
    // 0x940058: LoadField: r4 = r2->field_7
    //     0x940058: ldur            w4, [x2, #7]
    // 0x94005c: DecompressPointer r4
    //     0x94005c: add             x4, x4, HEAP, lsl #32
    // 0x940060: stur            x4, [fp, #-0x30]
    // 0x940064: LoadField: r1 = r3->field_3f
    //     0x940064: ldur            w1, [x3, #0x3f]
    // 0x940068: DecompressPointer r1
    //     0x940068: add             x1, x1, HEAP, lsl #32
    // 0x94006c: LoadField: r2 = r1->field_27
    //     0x94006c: ldur            w2, [x1, #0x27]
    // 0x940070: DecompressPointer r2
    //     0x940070: add             x2, x2, HEAP, lsl #32
    // 0x940074: LoadField: r5 = r2->field_7
    //     0x940074: ldur            w5, [x2, #7]
    // 0x940078: DecompressPointer r5
    //     0x940078: add             x5, x5, HEAP, lsl #32
    // 0x94007c: stur            x5, [fp, #-0x28]
    // 0x940080: LoadField: r1 = r3->field_3b
    //     0x940080: ldur            w1, [x3, #0x3b]
    // 0x940084: DecompressPointer r1
    //     0x940084: add             x1, x1, HEAP, lsl #32
    // 0x940088: LoadField: r2 = r1->field_27
    //     0x940088: ldur            w2, [x1, #0x27]
    // 0x94008c: DecompressPointer r2
    //     0x94008c: add             x2, x2, HEAP, lsl #32
    // 0x940090: LoadField: r6 = r2->field_7
    //     0x940090: ldur            w6, [x2, #7]
    // 0x940094: DecompressPointer r6
    //     0x940094: add             x6, x6, HEAP, lsl #32
    // 0x940098: stur            x6, [fp, #-0x20]
    // 0x94009c: LoadField: r1 = r3->field_47
    //     0x94009c: ldur            w1, [x3, #0x47]
    // 0x9400a0: DecompressPointer r1
    //     0x9400a0: add             x1, x1, HEAP, lsl #32
    // 0x9400a4: LoadField: r2 = r1->field_27
    //     0x9400a4: ldur            w2, [x1, #0x27]
    // 0x9400a8: DecompressPointer r2
    //     0x9400a8: add             x2, x2, HEAP, lsl #32
    // 0x9400ac: LoadField: r7 = r2->field_7
    //     0x9400ac: ldur            w7, [x2, #7]
    // 0x9400b0: DecompressPointer r7
    //     0x9400b0: add             x7, x7, HEAP, lsl #32
    // 0x9400b4: stur            x7, [fp, #-0x18]
    // 0x9400b8: LoadField: r1 = r3->field_43
    //     0x9400b8: ldur            w1, [x3, #0x43]
    // 0x9400bc: DecompressPointer r1
    //     0x9400bc: add             x1, x1, HEAP, lsl #32
    // 0x9400c0: LoadField: r2 = r1->field_27
    //     0x9400c0: ldur            w2, [x1, #0x27]
    // 0x9400c4: DecompressPointer r2
    //     0x9400c4: add             x2, x2, HEAP, lsl #32
    // 0x9400c8: LoadField: r8 = r2->field_7
    //     0x9400c8: ldur            w8, [x2, #7]
    // 0x9400cc: DecompressPointer r8
    //     0x9400cc: add             x8, x8, HEAP, lsl #32
    // 0x9400d0: stur            x8, [fp, #-0x10]
    // 0x9400d4: LoadField: r1 = r3->field_37
    //     0x9400d4: ldur            w1, [x3, #0x37]
    // 0x9400d8: DecompressPointer r1
    //     0x9400d8: add             x1, x1, HEAP, lsl #32
    // 0x9400dc: LoadField: r2 = r1->field_27
    //     0x9400dc: ldur            w2, [x1, #0x27]
    // 0x9400e0: DecompressPointer r2
    //     0x9400e0: add             x2, x2, HEAP, lsl #32
    // 0x9400e4: LoadField: r9 = r2->field_7
    //     0x9400e4: ldur            w9, [x2, #7]
    // 0x9400e8: DecompressPointer r9
    //     0x9400e8: add             x9, x9, HEAP, lsl #32
    // 0x9400ec: mov             x2, x0
    // 0x9400f0: stur            x9, [fp, #-8]
    // 0x9400f4: r1 = Null
    //     0x9400f4: mov             x1, NULL
    // 0x9400f8: r0 = AllocateArray()
    //     0x9400f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9400fc: mov             x2, x0
    // 0x940100: ldur            x0, [fp, #-0x30]
    // 0x940104: stur            x2, [fp, #-0x40]
    // 0x940108: StoreField: r2->field_f = r0
    //     0x940108: stur            w0, [x2, #0xf]
    // 0x94010c: ldur            x0, [fp, #-0x28]
    // 0x940110: StoreField: r2->field_13 = r0
    //     0x940110: stur            w0, [x2, #0x13]
    // 0x940114: ldur            x0, [fp, #-0x20]
    // 0x940118: ArrayStore: r2[0] = r0  ; List_4
    //     0x940118: stur            w0, [x2, #0x17]
    // 0x94011c: ldur            x0, [fp, #-0x18]
    // 0x940120: StoreField: r2->field_1b = r0
    //     0x940120: stur            w0, [x2, #0x1b]
    // 0x940124: ldur            x0, [fp, #-0x10]
    // 0x940128: StoreField: r2->field_1f = r0
    //     0x940128: stur            w0, [x2, #0x1f]
    // 0x94012c: ldur            x0, [fp, #-8]
    // 0x940130: StoreField: r2->field_23 = r0
    //     0x940130: stur            w0, [x2, #0x23]
    // 0x940134: r1 = <String>
    //     0x940134: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x940138: r0 = AllocateGrowableArray()
    //     0x940138: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x94013c: mov             x3, x0
    // 0x940140: ldur            x0, [fp, #-0x40]
    // 0x940144: stur            x3, [fp, #-8]
    // 0x940148: StoreField: r3->field_f = r0
    //     0x940148: stur            w0, [x3, #0xf]
    // 0x94014c: r0 = 12
    //     0x94014c: movz            x0, #0xc
    // 0x940150: StoreField: r3->field_b = r0
    //     0x940150: stur            w0, [x3, #0xb]
    // 0x940154: r1 = Function '<anonymous closure>':.
    //     0x940154: add             x1, PP, #0x56, lsl #12  ; [pp+0x568c8] AnonymousClosure: (0x9051f4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_checkFirstTimeInput (0x905210)
    //     0x940158: ldr             x1, [x1, #0x8c8]
    // 0x94015c: r2 = Null
    //     0x94015c: mov             x2, NULL
    // 0x940160: r0 = AllocateClosure()
    //     0x940160: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x940164: ldur            x1, [fp, #-8]
    // 0x940168: mov             x2, x0
    // 0x94016c: r0 = any()
    //     0x94016c: bl              #0x7d624c  ; [dart:collection] ListBase::any
    // 0x940170: tbnz            w0, #4, #0x9401c0
    // 0x940174: ldur            x0, [fp, #-0x38]
    // 0x940178: LoadField: r1 = r0->field_4b
    //     0x940178: ldur            x1, [x0, #0x4b]
    // 0x94017c: add             x2, x1, #1
    // 0x940180: StoreField: r0->field_4b = r2
    //     0x940180: stur            x2, [x0, #0x4b]
    // 0x940184: cmp             x2, #1
    // 0x940188: b.ne            #0x9401c0
    // 0x94018c: LoadField: r1 = r0->field_b
    //     0x94018c: ldur            w1, [x0, #0xb]
    // 0x940190: DecompressPointer r1
    //     0x940190: add             x1, x1, HEAP, lsl #32
    // 0x940194: cmp             w1, NULL
    // 0x940198: b.eq            #0x9401d8
    // 0x94019c: LoadField: r0 = r1->field_1f
    //     0x94019c: ldur            w0, [x1, #0x1f]
    // 0x9401a0: DecompressPointer r0
    //     0x9401a0: add             x0, x0, HEAP, lsl #32
    // 0x9401a4: str             x0, [SP]
    // 0x9401a8: r4 = 0
    //     0x9401a8: movz            x4, #0
    // 0x9401ac: ldr             x0, [SP]
    // 0x9401b0: r16 = UnlinkedCall_0x613b5c
    //     0x9401b0: add             x16, PP, #0x56, lsl #12  ; [pp+0x568d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9401b4: add             x16, x16, #0x8d0
    // 0x9401b8: ldp             x5, lr, [x16]
    // 0x9401bc: blr             lr
    // 0x9401c0: r0 = Null
    //     0x9401c0: mov             x0, NULL
    // 0x9401c4: LeaveFrame
    //     0x9401c4: mov             SP, fp
    //     0x9401c8: ldp             fp, lr, [SP], #0x10
    // 0x9401cc: ret
    //     0x9401cc: ret             
    // 0x9401d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9401d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9401d4: b               #0x940048
    // 0x9401d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9401d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9401dc, size: 0x11c
    // 0x9401dc: EnterFrame
    //     0x9401dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9401e0: mov             fp, SP
    // 0x9401e4: AllocStack(0x40)
    //     0x9401e4: sub             SP, SP, #0x40
    // 0x9401e8: SetupParameters()
    //     0x9401e8: ldr             x0, [fp, #0x18]
    //     0x9401ec: ldur            w1, [x0, #0x17]
    //     0x9401f0: add             x1, x1, HEAP, lsl #32
    // 0x9401f4: CheckStackOverflow
    //     0x9401f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9401f8: cmp             SP, x16
    //     0x9401fc: b.ls            #0x9402ec
    // 0x940200: LoadField: r0 = r1->field_f
    //     0x940200: ldur            w0, [x1, #0xf]
    // 0x940204: DecompressPointer r0
    //     0x940204: add             x0, x0, HEAP, lsl #32
    // 0x940208: LoadField: r2 = r0->field_b
    //     0x940208: ldur            w2, [x0, #0xb]
    // 0x94020c: DecompressPointer r2
    //     0x94020c: add             x2, x2, HEAP, lsl #32
    // 0x940210: cmp             w2, NULL
    // 0x940214: b.eq            #0x9402f4
    // 0x940218: LoadField: r3 = r0->field_33
    //     0x940218: ldur            w3, [x0, #0x33]
    // 0x94021c: DecompressPointer r3
    //     0x94021c: add             x3, x3, HEAP, lsl #32
    // 0x940220: LoadField: r4 = r3->field_27
    //     0x940220: ldur            w4, [x3, #0x27]
    // 0x940224: DecompressPointer r4
    //     0x940224: add             x4, x4, HEAP, lsl #32
    // 0x940228: LoadField: r3 = r4->field_7
    //     0x940228: ldur            w3, [x4, #7]
    // 0x94022c: DecompressPointer r3
    //     0x94022c: add             x3, x3, HEAP, lsl #32
    // 0x940230: LoadField: r4 = r0->field_3f
    //     0x940230: ldur            w4, [x0, #0x3f]
    // 0x940234: DecompressPointer r4
    //     0x940234: add             x4, x4, HEAP, lsl #32
    // 0x940238: LoadField: r5 = r4->field_27
    //     0x940238: ldur            w5, [x4, #0x27]
    // 0x94023c: DecompressPointer r5
    //     0x94023c: add             x5, x5, HEAP, lsl #32
    // 0x940240: LoadField: r4 = r5->field_7
    //     0x940240: ldur            w4, [x5, #7]
    // 0x940244: DecompressPointer r4
    //     0x940244: add             x4, x4, HEAP, lsl #32
    // 0x940248: LoadField: r5 = r0->field_3b
    //     0x940248: ldur            w5, [x0, #0x3b]
    // 0x94024c: DecompressPointer r5
    //     0x94024c: add             x5, x5, HEAP, lsl #32
    // 0x940250: LoadField: r6 = r5->field_27
    //     0x940250: ldur            w6, [x5, #0x27]
    // 0x940254: DecompressPointer r6
    //     0x940254: add             x6, x6, HEAP, lsl #32
    // 0x940258: LoadField: r5 = r6->field_7
    //     0x940258: ldur            w5, [x6, #7]
    // 0x94025c: DecompressPointer r5
    //     0x94025c: add             x5, x5, HEAP, lsl #32
    // 0x940260: LoadField: r6 = r0->field_47
    //     0x940260: ldur            w6, [x0, #0x47]
    // 0x940264: DecompressPointer r6
    //     0x940264: add             x6, x6, HEAP, lsl #32
    // 0x940268: LoadField: r7 = r6->field_27
    //     0x940268: ldur            w7, [x6, #0x27]
    // 0x94026c: DecompressPointer r7
    //     0x94026c: add             x7, x7, HEAP, lsl #32
    // 0x940270: LoadField: r6 = r7->field_7
    //     0x940270: ldur            w6, [x7, #7]
    // 0x940274: DecompressPointer r6
    //     0x940274: add             x6, x6, HEAP, lsl #32
    // 0x940278: LoadField: r7 = r0->field_37
    //     0x940278: ldur            w7, [x0, #0x37]
    // 0x94027c: DecompressPointer r7
    //     0x94027c: add             x7, x7, HEAP, lsl #32
    // 0x940280: LoadField: r8 = r7->field_27
    //     0x940280: ldur            w8, [x7, #0x27]
    // 0x940284: DecompressPointer r8
    //     0x940284: add             x8, x8, HEAP, lsl #32
    // 0x940288: LoadField: r7 = r8->field_7
    //     0x940288: ldur            w7, [x8, #7]
    // 0x94028c: DecompressPointer r7
    //     0x94028c: add             x7, x7, HEAP, lsl #32
    // 0x940290: LoadField: r8 = r0->field_43
    //     0x940290: ldur            w8, [x0, #0x43]
    // 0x940294: DecompressPointer r8
    //     0x940294: add             x8, x8, HEAP, lsl #32
    // 0x940298: LoadField: r0 = r8->field_27
    //     0x940298: ldur            w0, [x8, #0x27]
    // 0x94029c: DecompressPointer r0
    //     0x94029c: add             x0, x0, HEAP, lsl #32
    // 0x9402a0: LoadField: r8 = r0->field_7
    //     0x9402a0: ldur            w8, [x0, #7]
    // 0x9402a4: DecompressPointer r8
    //     0x9402a4: add             x8, x8, HEAP, lsl #32
    // 0x9402a8: LoadField: r0 = r1->field_13
    //     0x9402a8: ldur            w0, [x1, #0x13]
    // 0x9402ac: DecompressPointer r0
    //     0x9402ac: add             x0, x0, HEAP, lsl #32
    // 0x9402b0: LoadField: r1 = r2->field_f
    //     0x9402b0: ldur            w1, [x2, #0xf]
    // 0x9402b4: DecompressPointer r1
    //     0x9402b4: add             x1, x1, HEAP, lsl #32
    // 0x9402b8: stp             x3, x1, [SP, #0x30]
    // 0x9402bc: stp             x5, x4, [SP, #0x20]
    // 0x9402c0: stp             x7, x6, [SP, #0x10]
    // 0x9402c4: stp             x0, x8, [SP]
    // 0x9402c8: r4 = 0
    //     0x9402c8: movz            x4, #0
    // 0x9402cc: ldr             x0, [SP, #0x38]
    // 0x9402d0: r16 = UnlinkedCall_0x613b5c
    //     0x9402d0: add             x16, PP, #0x56, lsl #12  ; [pp+0x568b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x9402d4: add             x16, x16, #0x8b8
    // 0x9402d8: ldp             x5, lr, [x16]
    // 0x9402dc: blr             lr
    // 0x9402e0: LeaveFrame
    //     0x9402e0: mov             SP, fp
    //     0x9402e4: ldp             fp, lr, [SP], #0x10
    // 0x9402e8: ret
    //     0x9402e8: ret             
    // 0x9402ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9402ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9402f0: b               #0x940200
    // 0x9402f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9402f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9402f8, size: 0xa0
    // 0x9402f8: EnterFrame
    //     0x9402f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9402fc: mov             fp, SP
    // 0x940300: AllocStack(0x40)
    //     0x940300: sub             SP, SP, #0x40
    // 0x940304: SetupParameters()
    //     0x940304: ldr             x0, [fp, #0x18]
    //     0x940308: ldur            w1, [x0, #0x17]
    //     0x94030c: add             x1, x1, HEAP, lsl #32
    // 0x940310: CheckStackOverflow
    //     0x940310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x940314: cmp             SP, x16
    //     0x940318: b.ls            #0x94038c
    // 0x94031c: LoadField: r0 = r1->field_f
    //     0x94031c: ldur            w0, [x1, #0xf]
    // 0x940320: DecompressPointer r0
    //     0x940320: add             x0, x0, HEAP, lsl #32
    // 0x940324: LoadField: r1 = r0->field_b
    //     0x940324: ldur            w1, [x0, #0xb]
    // 0x940328: DecompressPointer r1
    //     0x940328: add             x1, x1, HEAP, lsl #32
    // 0x94032c: cmp             w1, NULL
    // 0x940330: b.eq            #0x940394
    // 0x940334: LoadField: r0 = r1->field_f
    //     0x940334: ldur            w0, [x1, #0xf]
    // 0x940338: DecompressPointer r0
    //     0x940338: add             x0, x0, HEAP, lsl #32
    // 0x94033c: r16 = ""
    //     0x94033c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940340: stp             x16, x0, [SP, #0x30]
    // 0x940344: r16 = ""
    //     0x940344: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940348: r30 = ""
    //     0x940348: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94034c: stp             lr, x16, [SP, #0x20]
    // 0x940350: r16 = ""
    //     0x940350: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940354: r30 = ""
    //     0x940354: ldr             lr, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940358: stp             lr, x16, [SP, #0x10]
    // 0x94035c: r16 = ""
    //     0x94035c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x940360: r30 = false
    //     0x940360: add             lr, NULL, #0x30  ; false
    // 0x940364: stp             lr, x16, [SP]
    // 0x940368: r4 = 0
    //     0x940368: movz            x4, #0
    // 0x94036c: ldr             x0, [SP, #0x38]
    // 0x940370: r16 = UnlinkedCall_0x613b5c
    //     0x940370: add             x16, PP, #0x56, lsl #12  ; [pp+0x56940] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x940374: add             x16, x16, #0x940
    // 0x940378: ldp             x5, lr, [x16]
    // 0x94037c: blr             lr
    // 0x940380: LeaveFrame
    //     0x940380: mov             SP, fp
    //     0x940384: ldp             fp, lr, [SP], #0x10
    // 0x940388: ret
    //     0x940388: ret             
    // 0x94038c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94038c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x940390: b               #0x94031c
    // 0x940394: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x940394: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb40ae8, size: 0x2e9c
    // 0xb40ae8: EnterFrame
    //     0xb40ae8: stp             fp, lr, [SP, #-0x10]!
    //     0xb40aec: mov             fp, SP
    // 0xb40af0: AllocStack(0x100)
    //     0xb40af0: sub             SP, SP, #0x100
    // 0xb40af4: SetupParameters(CheckoutAddressWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb40af4: stur            x1, [fp, #-8]
    //     0xb40af8: mov             x16, x2
    //     0xb40afc: mov             x2, x1
    //     0xb40b00: mov             x1, x16
    //     0xb40b04: stur            x1, [fp, #-0x10]
    // 0xb40b08: CheckStackOverflow
    //     0xb40b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb40b0c: cmp             SP, x16
    //     0xb40b10: b.ls            #0xb43938
    // 0xb40b14: r1 = 1
    //     0xb40b14: movz            x1, #0x1
    // 0xb40b18: r0 = AllocateContext()
    //     0xb40b18: bl              #0x16f6108  ; AllocateContextStub
    // 0xb40b1c: ldur            x2, [fp, #-8]
    // 0xb40b20: stur            x0, [fp, #-0x18]
    // 0xb40b24: StoreField: r0->field_f = r2
    //     0xb40b24: stur            w2, [x0, #0xf]
    // 0xb40b28: ldur            x1, [fp, #-0x10]
    // 0xb40b2c: r0 = of()
    //     0xb40b2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40b30: LoadField: r1 = r0->field_87
    //     0xb40b30: ldur            w1, [x0, #0x87]
    // 0xb40b34: DecompressPointer r1
    //     0xb40b34: add             x1, x1, HEAP, lsl #32
    // 0xb40b38: LoadField: r0 = r1->field_2b
    //     0xb40b38: ldur            w0, [x1, #0x2b]
    // 0xb40b3c: DecompressPointer r0
    //     0xb40b3c: add             x0, x0, HEAP, lsl #32
    // 0xb40b40: r16 = 12.000000
    //     0xb40b40: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb40b44: ldr             x16, [x16, #0x9e8]
    // 0xb40b48: r30 = Instance_Color
    //     0xb40b48: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb40b4c: stp             lr, x16, [SP]
    // 0xb40b50: mov             x1, x0
    // 0xb40b54: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb40b54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb40b58: ldr             x4, [x4, #0xaa0]
    // 0xb40b5c: r0 = copyWith()
    //     0xb40b5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb40b60: stur            x0, [fp, #-0x20]
    // 0xb40b64: r0 = Text()
    //     0xb40b64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb40b68: mov             x1, x0
    // 0xb40b6c: r0 = "Full Name*"
    //     0xb40b6c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54048] "Full Name*"
    //     0xb40b70: ldr             x0, [x0, #0x48]
    // 0xb40b74: stur            x1, [fp, #-0x28]
    // 0xb40b78: StoreField: r1->field_b = r0
    //     0xb40b78: stur            w0, [x1, #0xb]
    // 0xb40b7c: ldur            x0, [fp, #-0x20]
    // 0xb40b80: StoreField: r1->field_13 = r0
    //     0xb40b80: stur            w0, [x1, #0x13]
    // 0xb40b84: r0 = Padding()
    //     0xb40b84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb40b88: mov             x1, x0
    // 0xb40b8c: r0 = Instance_EdgeInsets
    //     0xb40b8c: add             x0, PP, #0x56, lsl #12  ; [pp+0x567e0] Obj!EdgeInsets@d58d31
    //     0xb40b90: ldr             x0, [x0, #0x7e0]
    // 0xb40b94: stur            x1, [fp, #-0x30]
    // 0xb40b98: StoreField: r1->field_f = r0
    //     0xb40b98: stur            w0, [x1, #0xf]
    // 0xb40b9c: ldur            x2, [fp, #-0x28]
    // 0xb40ba0: StoreField: r1->field_b = r2
    //     0xb40ba0: stur            w2, [x1, #0xb]
    // 0xb40ba4: ldur            x2, [fp, #-8]
    // 0xb40ba8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb40ba8: ldur            w3, [x2, #0x17]
    // 0xb40bac: DecompressPointer r3
    //     0xb40bac: add             x3, x3, HEAP, lsl #32
    // 0xb40bb0: stur            x3, [fp, #-0x28]
    // 0xb40bb4: LoadField: r4 = r2->field_b
    //     0xb40bb4: ldur            w4, [x2, #0xb]
    // 0xb40bb8: DecompressPointer r4
    //     0xb40bb8: add             x4, x4, HEAP, lsl #32
    // 0xb40bbc: cmp             w4, NULL
    // 0xb40bc0: b.eq            #0xb43940
    // 0xb40bc4: LoadField: r5 = r4->field_1b
    //     0xb40bc4: ldur            w5, [x4, #0x1b]
    // 0xb40bc8: DecompressPointer r5
    //     0xb40bc8: add             x5, x5, HEAP, lsl #32
    // 0xb40bcc: LoadField: r4 = r5->field_b
    //     0xb40bcc: ldur            w4, [x5, #0xb]
    // 0xb40bd0: DecompressPointer r4
    //     0xb40bd0: add             x4, x4, HEAP, lsl #32
    // 0xb40bd4: cmp             w4, NULL
    // 0xb40bd8: b.ne            #0xb40be4
    // 0xb40bdc: r4 = Null
    //     0xb40bdc: mov             x4, NULL
    // 0xb40be0: b               #0xb40bf8
    // 0xb40be4: LoadField: r5 = r4->field_7
    //     0xb40be4: ldur            w5, [x4, #7]
    // 0xb40be8: cbz             w5, #0xb40bf4
    // 0xb40bec: r4 = false
    //     0xb40bec: add             x4, NULL, #0x30  ; false
    // 0xb40bf0: b               #0xb40bf8
    // 0xb40bf4: r4 = true
    //     0xb40bf4: add             x4, NULL, #0x20  ; true
    // 0xb40bf8: cmp             w4, NULL
    // 0xb40bfc: b.ne            #0xb40c04
    // 0xb40c00: r4 = true
    //     0xb40c00: add             x4, NULL, #0x20  ; true
    // 0xb40c04: stur            x4, [fp, #-0x20]
    // 0xb40c08: r16 = "[a-zA-Z ]"
    //     0xb40c08: add             x16, PP, #0x54, lsl #12  ; [pp+0x54040] "[a-zA-Z ]"
    //     0xb40c0c: ldr             x16, [x16, #0x40]
    // 0xb40c10: stp             x16, NULL, [SP, #0x20]
    // 0xb40c14: r16 = false
    //     0xb40c14: add             x16, NULL, #0x30  ; false
    // 0xb40c18: r30 = true
    //     0xb40c18: add             lr, NULL, #0x20  ; true
    // 0xb40c1c: stp             lr, x16, [SP, #0x10]
    // 0xb40c20: r16 = false
    //     0xb40c20: add             x16, NULL, #0x30  ; false
    // 0xb40c24: r30 = false
    //     0xb40c24: add             lr, NULL, #0x30  ; false
    // 0xb40c28: stp             lr, x16, [SP]
    // 0xb40c2c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb40c2c: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb40c30: r0 = _RegExp()
    //     0xb40c30: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb40c34: stur            x0, [fp, #-0x38]
    // 0xb40c38: r0 = FilteringTextInputFormatter()
    //     0xb40c38: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb40c3c: mov             x1, x0
    // 0xb40c40: ldur            x0, [fp, #-0x38]
    // 0xb40c44: stur            x1, [fp, #-0x40]
    // 0xb40c48: StoreField: r1->field_b = r0
    //     0xb40c48: stur            w0, [x1, #0xb]
    // 0xb40c4c: r0 = true
    //     0xb40c4c: add             x0, NULL, #0x20  ; true
    // 0xb40c50: StoreField: r1->field_7 = r0
    //     0xb40c50: stur            w0, [x1, #7]
    // 0xb40c54: r2 = ""
    //     0xb40c54: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb40c58: StoreField: r1->field_f = r2
    //     0xb40c58: stur            w2, [x1, #0xf]
    // 0xb40c5c: r0 = LengthLimitingTextInputFormatter()
    //     0xb40c5c: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb40c60: mov             x3, x0
    // 0xb40c64: r0 = 180
    //     0xb40c64: movz            x0, #0xb4
    // 0xb40c68: stur            x3, [fp, #-0x38]
    // 0xb40c6c: StoreField: r3->field_7 = r0
    //     0xb40c6c: stur            w0, [x3, #7]
    // 0xb40c70: r1 = Null
    //     0xb40c70: mov             x1, NULL
    // 0xb40c74: r2 = 4
    //     0xb40c74: movz            x2, #0x4
    // 0xb40c78: r0 = AllocateArray()
    //     0xb40c78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb40c7c: mov             x2, x0
    // 0xb40c80: ldur            x0, [fp, #-0x40]
    // 0xb40c84: stur            x2, [fp, #-0x48]
    // 0xb40c88: StoreField: r2->field_f = r0
    //     0xb40c88: stur            w0, [x2, #0xf]
    // 0xb40c8c: ldur            x0, [fp, #-0x38]
    // 0xb40c90: StoreField: r2->field_13 = r0
    //     0xb40c90: stur            w0, [x2, #0x13]
    // 0xb40c94: r1 = <TextInputFormatter>
    //     0xb40c94: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb40c98: ldr             x1, [x1, #0x7b0]
    // 0xb40c9c: r0 = AllocateGrowableArray()
    //     0xb40c9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb40ca0: mov             x2, x0
    // 0xb40ca4: ldur            x0, [fp, #-0x48]
    // 0xb40ca8: stur            x2, [fp, #-0x38]
    // 0xb40cac: StoreField: r2->field_f = r0
    //     0xb40cac: stur            w0, [x2, #0xf]
    // 0xb40cb0: r0 = 4
    //     0xb40cb0: movz            x0, #0x4
    // 0xb40cb4: StoreField: r2->field_b = r0
    //     0xb40cb4: stur            w0, [x2, #0xb]
    // 0xb40cb8: ldur            x1, [fp, #-0x10]
    // 0xb40cbc: r0 = of()
    //     0xb40cbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40cc0: LoadField: r1 = r0->field_87
    //     0xb40cc0: ldur            w1, [x0, #0x87]
    // 0xb40cc4: DecompressPointer r1
    //     0xb40cc4: add             x1, x1, HEAP, lsl #32
    // 0xb40cc8: LoadField: r0 = r1->field_2b
    //     0xb40cc8: ldur            w0, [x1, #0x2b]
    // 0xb40ccc: DecompressPointer r0
    //     0xb40ccc: add             x0, x0, HEAP, lsl #32
    // 0xb40cd0: ldur            x1, [fp, #-0x10]
    // 0xb40cd4: stur            x0, [fp, #-0x40]
    // 0xb40cd8: r0 = of()
    //     0xb40cd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40cdc: LoadField: r1 = r0->field_5b
    //     0xb40cdc: ldur            w1, [x0, #0x5b]
    // 0xb40ce0: DecompressPointer r1
    //     0xb40ce0: add             x1, x1, HEAP, lsl #32
    // 0xb40ce4: r16 = 14.000000
    //     0xb40ce4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb40ce8: ldr             x16, [x16, #0x1d8]
    // 0xb40cec: stp             x1, x16, [SP]
    // 0xb40cf0: ldur            x1, [fp, #-0x40]
    // 0xb40cf4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb40cf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb40cf8: ldr             x4, [x4, #0xaa0]
    // 0xb40cfc: r0 = copyWith()
    //     0xb40cfc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb40d00: ldur            x2, [fp, #-8]
    // 0xb40d04: stur            x0, [fp, #-0x48]
    // 0xb40d08: LoadField: r3 = r2->field_33
    //     0xb40d08: ldur            w3, [x2, #0x33]
    // 0xb40d0c: DecompressPointer r3
    //     0xb40d0c: add             x3, x3, HEAP, lsl #32
    // 0xb40d10: ldur            x1, [fp, #-0x10]
    // 0xb40d14: stur            x3, [fp, #-0x40]
    // 0xb40d18: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb40d18: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb40d1c: ldur            x1, [fp, #-0x10]
    // 0xb40d20: stur            x0, [fp, #-0x50]
    // 0xb40d24: r0 = of()
    //     0xb40d24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40d28: LoadField: r1 = r0->field_5b
    //     0xb40d28: ldur            w1, [x0, #0x5b]
    // 0xb40d2c: DecompressPointer r1
    //     0xb40d2c: add             x1, x1, HEAP, lsl #32
    // 0xb40d30: stur            x1, [fp, #-0x58]
    // 0xb40d34: r0 = BorderSide()
    //     0xb40d34: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb40d38: mov             x1, x0
    // 0xb40d3c: ldur            x0, [fp, #-0x58]
    // 0xb40d40: stur            x1, [fp, #-0x60]
    // 0xb40d44: StoreField: r1->field_7 = r0
    //     0xb40d44: stur            w0, [x1, #7]
    // 0xb40d48: d0 = 1.000000
    //     0xb40d48: fmov            d0, #1.00000000
    // 0xb40d4c: StoreField: r1->field_b = d0
    //     0xb40d4c: stur            d0, [x1, #0xb]
    // 0xb40d50: r0 = Instance_BorderStyle
    //     0xb40d50: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb40d54: ldr             x0, [x0, #0xf68]
    // 0xb40d58: StoreField: r1->field_13 = r0
    //     0xb40d58: stur            w0, [x1, #0x13]
    // 0xb40d5c: d1 = -1.000000
    //     0xb40d5c: fmov            d1, #-1.00000000
    // 0xb40d60: ArrayStore: r1[0] = d1  ; List_8
    //     0xb40d60: stur            d1, [x1, #0x17]
    // 0xb40d64: r0 = Radius()
    //     0xb40d64: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb40d68: d0 = 30.000000
    //     0xb40d68: fmov            d0, #30.00000000
    // 0xb40d6c: stur            x0, [fp, #-0x58]
    // 0xb40d70: StoreField: r0->field_7 = d0
    //     0xb40d70: stur            d0, [x0, #7]
    // 0xb40d74: StoreField: r0->field_f = d0
    //     0xb40d74: stur            d0, [x0, #0xf]
    // 0xb40d78: r0 = BorderRadius()
    //     0xb40d78: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb40d7c: mov             x1, x0
    // 0xb40d80: ldur            x0, [fp, #-0x58]
    // 0xb40d84: stur            x1, [fp, #-0x68]
    // 0xb40d88: StoreField: r1->field_7 = r0
    //     0xb40d88: stur            w0, [x1, #7]
    // 0xb40d8c: StoreField: r1->field_b = r0
    //     0xb40d8c: stur            w0, [x1, #0xb]
    // 0xb40d90: StoreField: r1->field_f = r0
    //     0xb40d90: stur            w0, [x1, #0xf]
    // 0xb40d94: StoreField: r1->field_13 = r0
    //     0xb40d94: stur            w0, [x1, #0x13]
    // 0xb40d98: r0 = OutlineInputBorder()
    //     0xb40d98: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb40d9c: mov             x2, x0
    // 0xb40da0: ldur            x0, [fp, #-0x68]
    // 0xb40da4: stur            x2, [fp, #-0x58]
    // 0xb40da8: StoreField: r2->field_13 = r0
    //     0xb40da8: stur            w0, [x2, #0x13]
    // 0xb40dac: d0 = 4.000000
    //     0xb40dac: fmov            d0, #4.00000000
    // 0xb40db0: StoreField: r2->field_b = d0
    //     0xb40db0: stur            d0, [x2, #0xb]
    // 0xb40db4: ldur            x0, [fp, #-0x60]
    // 0xb40db8: StoreField: r2->field_7 = r0
    //     0xb40db8: stur            w0, [x2, #7]
    // 0xb40dbc: ldur            x1, [fp, #-0x10]
    // 0xb40dc0: r0 = of()
    //     0xb40dc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40dc4: LoadField: r1 = r0->field_87
    //     0xb40dc4: ldur            w1, [x0, #0x87]
    // 0xb40dc8: DecompressPointer r1
    //     0xb40dc8: add             x1, x1, HEAP, lsl #32
    // 0xb40dcc: LoadField: r0 = r1->field_2b
    //     0xb40dcc: ldur            w0, [x1, #0x2b]
    // 0xb40dd0: DecompressPointer r0
    //     0xb40dd0: add             x0, x0, HEAP, lsl #32
    // 0xb40dd4: r16 = 12.000000
    //     0xb40dd4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb40dd8: ldr             x16, [x16, #0x9e8]
    // 0xb40ddc: r30 = Instance_MaterialColor
    //     0xb40ddc: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb40de0: ldr             lr, [lr, #0x180]
    // 0xb40de4: stp             lr, x16, [SP]
    // 0xb40de8: mov             x1, x0
    // 0xb40dec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb40dec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb40df0: ldr             x4, [x4, #0xaa0]
    // 0xb40df4: r0 = copyWith()
    //     0xb40df4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb40df8: ldur            x1, [fp, #-0x10]
    // 0xb40dfc: stur            x0, [fp, #-0x60]
    // 0xb40e00: r0 = of()
    //     0xb40e00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40e04: LoadField: r1 = r0->field_87
    //     0xb40e04: ldur            w1, [x0, #0x87]
    // 0xb40e08: DecompressPointer r1
    //     0xb40e08: add             x1, x1, HEAP, lsl #32
    // 0xb40e0c: LoadField: r0 = r1->field_2b
    //     0xb40e0c: ldur            w0, [x1, #0x2b]
    // 0xb40e10: DecompressPointer r0
    //     0xb40e10: add             x0, x0, HEAP, lsl #32
    // 0xb40e14: stur            x0, [fp, #-0x68]
    // 0xb40e18: r1 = Instance_Color
    //     0xb40e18: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb40e1c: d0 = 0.400000
    //     0xb40e1c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb40e20: r0 = withOpacity()
    //     0xb40e20: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb40e24: r16 = 14.000000
    //     0xb40e24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb40e28: ldr             x16, [x16, #0x1d8]
    // 0xb40e2c: stp             x0, x16, [SP]
    // 0xb40e30: ldur            x1, [fp, #-0x68]
    // 0xb40e34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb40e34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb40e38: ldr             x4, [x4, #0xaa0]
    // 0xb40e3c: r0 = copyWith()
    //     0xb40e3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb40e40: ldur            x2, [fp, #-8]
    // 0xb40e44: stur            x0, [fp, #-0x78]
    // 0xb40e48: LoadField: r1 = r2->field_53
    //     0xb40e48: ldur            w1, [x2, #0x53]
    // 0xb40e4c: DecompressPointer r1
    //     0xb40e4c: add             x1, x1, HEAP, lsl #32
    // 0xb40e50: tbnz            w1, #4, #0xb40edc
    // 0xb40e54: LoadField: r1 = r2->field_33
    //     0xb40e54: ldur            w1, [x2, #0x33]
    // 0xb40e58: DecompressPointer r1
    //     0xb40e58: add             x1, x1, HEAP, lsl #32
    // 0xb40e5c: LoadField: r3 = r1->field_27
    //     0xb40e5c: ldur            w3, [x1, #0x27]
    // 0xb40e60: DecompressPointer r3
    //     0xb40e60: add             x3, x3, HEAP, lsl #32
    // 0xb40e64: LoadField: r1 = r3->field_7
    //     0xb40e64: ldur            w1, [x3, #7]
    // 0xb40e68: DecompressPointer r1
    //     0xb40e68: add             x1, x1, HEAP, lsl #32
    // 0xb40e6c: LoadField: r3 = r1->field_7
    //     0xb40e6c: ldur            w3, [x1, #7]
    // 0xb40e70: cbz             w3, #0xb40e8c
    // 0xb40e74: r1 = LoadInt32Instr(r3)
    //     0xb40e74: sbfx            x1, x3, #1, #0x1f
    // 0xb40e78: cmp             x1, #1
    // 0xb40e7c: b.le            #0xb40e8c
    // 0xb40e80: r1 = Instance_IconData
    //     0xb40e80: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb40e84: ldr             x1, [x1, #0x130]
    // 0xb40e88: b               #0xb40e94
    // 0xb40e8c: r1 = Instance_IconData
    //     0xb40e8c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb40e90: ldr             x1, [x1, #0x138]
    // 0xb40e94: stur            x1, [fp, #-0x70]
    // 0xb40e98: cbz             w3, #0xb40eb4
    // 0xb40e9c: r4 = LoadInt32Instr(r3)
    //     0xb40e9c: sbfx            x4, x3, #1, #0x1f
    // 0xb40ea0: cmp             x4, #1
    // 0xb40ea4: b.le            #0xb40eb4
    // 0xb40ea8: r3 = Instance_Color
    //     0xb40ea8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb40eac: ldr             x3, [x3, #0x858]
    // 0xb40eb0: b               #0xb40ebc
    // 0xb40eb4: r3 = Instance_Color
    //     0xb40eb4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb40eb8: ldr             x3, [x3, #0x50]
    // 0xb40ebc: stur            x3, [fp, #-0x68]
    // 0xb40ec0: r0 = Icon()
    //     0xb40ec0: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb40ec4: mov             x1, x0
    // 0xb40ec8: ldur            x0, [fp, #-0x70]
    // 0xb40ecc: StoreField: r1->field_b = r0
    //     0xb40ecc: stur            w0, [x1, #0xb]
    // 0xb40ed0: ldur            x0, [fp, #-0x68]
    // 0xb40ed4: StoreField: r1->field_23 = r0
    //     0xb40ed4: stur            w0, [x1, #0x23]
    // 0xb40ed8: b               #0xb40ee0
    // 0xb40edc: r1 = Null
    //     0xb40edc: mov             x1, NULL
    // 0xb40ee0: ldur            x2, [fp, #-8]
    // 0xb40ee4: ldur            x0, [fp, #-0x28]
    // 0xb40ee8: ldur            x16, [fp, #-0x58]
    // 0xb40eec: r30 = Instance_EdgeInsets
    //     0xb40eec: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb40ef0: ldr             lr, [lr, #0xa78]
    // 0xb40ef4: stp             lr, x16, [SP, #0x20]
    // 0xb40ef8: ldur            x16, [fp, #-0x60]
    // 0xb40efc: r30 = "Full Name"
    //     0xb40efc: add             lr, PP, #0x56, lsl #12  ; [pp+0x567e8] "Full Name"
    //     0xb40f00: ldr             lr, [lr, #0x7e8]
    // 0xb40f04: stp             lr, x16, [SP, #0x10]
    // 0xb40f08: ldur            x16, [fp, #-0x78]
    // 0xb40f0c: stp             x1, x16, [SP]
    // 0xb40f10: ldur            x1, [fp, #-0x50]
    // 0xb40f14: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x2, errorStyle, 0x3, focusedBorder, 0x1, hintStyle, 0x5, hintText, 0x4, suffixIcon, 0x6, null]
    //     0xb40f14: add             x4, PP, #0x56, lsl #12  ; [pp+0x567f0] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x2, "errorStyle", 0x3, "focusedBorder", 0x1, "hintStyle", 0x5, "hintText", 0x4, "suffixIcon", 0x6, Null]
    //     0xb40f18: ldr             x4, [x4, #0x7f0]
    // 0xb40f1c: r0 = copyWith()
    //     0xb40f1c: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb40f20: ldur            x2, [fp, #-8]
    // 0xb40f24: r1 = Function '_validateCustomerNameNumber@1549293123':.
    //     0xb40f24: add             x1, PP, #0x56, lsl #12  ; [pp+0x567f8] AnonymousClosure: (0xb43e08), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateCustomerNameNumber (0xa05a54)
    //     0xb40f28: ldr             x1, [x1, #0x7f8]
    // 0xb40f2c: stur            x0, [fp, #-0x50]
    // 0xb40f30: r0 = AllocateClosure()
    //     0xb40f30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb40f34: ldur            x2, [fp, #-0x18]
    // 0xb40f38: r1 = Function '<anonymous closure>':.
    //     0xb40f38: add             x1, PP, #0x56, lsl #12  ; [pp+0x56800] AnonymousClosure: (0xb43d90), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xb40ae8)
    //     0xb40f3c: ldr             x1, [x1, #0x800]
    // 0xb40f40: stur            x0, [fp, #-0x58]
    // 0xb40f44: r0 = AllocateClosure()
    //     0xb40f44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb40f48: r1 = <String>
    //     0xb40f48: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb40f4c: stur            x0, [fp, #-0x60]
    // 0xb40f50: r0 = TextFormField()
    //     0xb40f50: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb40f54: stur            x0, [fp, #-0x68]
    // 0xb40f58: ldur            x16, [fp, #-0x58]
    // 0xb40f5c: r30 = true
    //     0xb40f5c: add             lr, NULL, #0x20  ; true
    // 0xb40f60: stp             lr, x16, [SP, #0x40]
    // 0xb40f64: ldur            x16, [fp, #-0x20]
    // 0xb40f68: r30 = Instance_AutovalidateMode
    //     0xb40f68: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb40f6c: ldr             lr, [lr, #0x7e8]
    // 0xb40f70: stp             lr, x16, [SP, #0x30]
    // 0xb40f74: ldur            x16, [fp, #-0x38]
    // 0xb40f78: ldur            lr, [fp, #-0x48]
    // 0xb40f7c: stp             lr, x16, [SP, #0x20]
    // 0xb40f80: r16 = Instance_TextInputType
    //     0xb40f80: add             x16, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xb40f84: ldr             x16, [x16, #0x68]
    // 0xb40f88: r30 = 2
    //     0xb40f88: movz            lr, #0x2
    // 0xb40f8c: stp             lr, x16, [SP, #0x10]
    // 0xb40f90: ldur            x16, [fp, #-0x40]
    // 0xb40f94: ldur            lr, [fp, #-0x60]
    // 0xb40f98: stp             lr, x16, [SP]
    // 0xb40f9c: mov             x1, x0
    // 0xb40fa0: ldur            x2, [fp, #-0x50]
    // 0xb40fa4: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x8, maxLines, 0x9, onChanged, 0xb, style, 0x7, validator, 0x2, null]
    //     0xb40fa4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54070] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x8, "maxLines", 0x9, "onChanged", 0xb, "style", 0x7, "validator", 0x2, Null]
    //     0xb40fa8: ldr             x4, [x4, #0x70]
    // 0xb40fac: r0 = TextFormField()
    //     0xb40fac: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb40fb0: r0 = Form()
    //     0xb40fb0: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb40fb4: mov             x2, x0
    // 0xb40fb8: ldur            x0, [fp, #-0x68]
    // 0xb40fbc: stur            x2, [fp, #-0x20]
    // 0xb40fc0: StoreField: r2->field_b = r0
    //     0xb40fc0: stur            w0, [x2, #0xb]
    // 0xb40fc4: r0 = Instance_AutovalidateMode
    //     0xb40fc4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb40fc8: ldr             x0, [x0, #0x800]
    // 0xb40fcc: StoreField: r2->field_23 = r0
    //     0xb40fcc: stur            w0, [x2, #0x23]
    // 0xb40fd0: ldur            x1, [fp, #-0x28]
    // 0xb40fd4: StoreField: r2->field_7 = r1
    //     0xb40fd4: stur            w1, [x2, #7]
    // 0xb40fd8: ldur            x1, [fp, #-0x10]
    // 0xb40fdc: r0 = of()
    //     0xb40fdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb40fe0: LoadField: r1 = r0->field_87
    //     0xb40fe0: ldur            w1, [x0, #0x87]
    // 0xb40fe4: DecompressPointer r1
    //     0xb40fe4: add             x1, x1, HEAP, lsl #32
    // 0xb40fe8: LoadField: r0 = r1->field_2b
    //     0xb40fe8: ldur            w0, [x1, #0x2b]
    // 0xb40fec: DecompressPointer r0
    //     0xb40fec: add             x0, x0, HEAP, lsl #32
    // 0xb40ff0: r16 = 12.000000
    //     0xb40ff0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb40ff4: ldr             x16, [x16, #0x9e8]
    // 0xb40ff8: r30 = Instance_Color
    //     0xb40ff8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb40ffc: stp             lr, x16, [SP]
    // 0xb41000: mov             x1, x0
    // 0xb41004: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb41004: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41008: ldr             x4, [x4, #0xaa0]
    // 0xb4100c: r0 = copyWith()
    //     0xb4100c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41010: stur            x0, [fp, #-0x28]
    // 0xb41014: r0 = Text()
    //     0xb41014: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb41018: mov             x1, x0
    // 0xb4101c: r0 = "House No./ Building Name*"
    //     0xb4101c: add             x0, PP, #0x54, lsl #12  ; [pp+0x54080] "House No./ Building Name*"
    //     0xb41020: ldr             x0, [x0, #0x80]
    // 0xb41024: stur            x1, [fp, #-0x38]
    // 0xb41028: StoreField: r1->field_b = r0
    //     0xb41028: stur            w0, [x1, #0xb]
    // 0xb4102c: ldur            x0, [fp, #-0x28]
    // 0xb41030: StoreField: r1->field_13 = r0
    //     0xb41030: stur            w0, [x1, #0x13]
    // 0xb41034: r0 = Padding()
    //     0xb41034: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb41038: mov             x3, x0
    // 0xb4103c: r2 = Instance_EdgeInsets
    //     0xb4103c: add             x2, PP, #0x56, lsl #12  ; [pp+0x567e0] Obj!EdgeInsets@d58d31
    //     0xb41040: ldr             x2, [x2, #0x7e0]
    // 0xb41044: stur            x3, [fp, #-0x40]
    // 0xb41048: StoreField: r3->field_f = r2
    //     0xb41048: stur            w2, [x3, #0xf]
    // 0xb4104c: ldur            x0, [fp, #-0x38]
    // 0xb41050: StoreField: r3->field_b = r0
    //     0xb41050: stur            w0, [x3, #0xb]
    // 0xb41054: ldur            x4, [fp, #-8]
    // 0xb41058: LoadField: r5 = r4->field_13
    //     0xb41058: ldur            w5, [x4, #0x13]
    // 0xb4105c: DecompressPointer r5
    //     0xb4105c: add             x5, x5, HEAP, lsl #32
    // 0xb41060: stur            x5, [fp, #-0x38]
    // 0xb41064: LoadField: r0 = r4->field_b
    //     0xb41064: ldur            w0, [x4, #0xb]
    // 0xb41068: DecompressPointer r0
    //     0xb41068: add             x0, x0, HEAP, lsl #32
    // 0xb4106c: cmp             w0, NULL
    // 0xb41070: b.eq            #0xb43944
    // 0xb41074: LoadField: r1 = r0->field_1b
    //     0xb41074: ldur            w1, [x0, #0x1b]
    // 0xb41078: DecompressPointer r1
    //     0xb41078: add             x1, x1, HEAP, lsl #32
    // 0xb4107c: LoadField: r6 = r1->field_1b
    //     0xb4107c: ldur            w6, [x1, #0x1b]
    // 0xb41080: DecompressPointer r6
    //     0xb41080: add             x6, x6, HEAP, lsl #32
    // 0xb41084: cmp             w6, NULL
    // 0xb41088: b.ne            #0xb41094
    // 0xb4108c: r0 = Null
    //     0xb4108c: mov             x0, NULL
    // 0xb41090: b               #0xb410ac
    // 0xb41094: LoadField: r0 = r6->field_b
    //     0xb41094: ldur            w0, [x6, #0xb]
    // 0xb41098: cbz             w0, #0xb410a4
    // 0xb4109c: r1 = false
    //     0xb4109c: add             x1, NULL, #0x30  ; false
    // 0xb410a0: b               #0xb410a8
    // 0xb410a4: r1 = true
    //     0xb410a4: add             x1, NULL, #0x20  ; true
    // 0xb410a8: mov             x0, x1
    // 0xb410ac: cmp             w0, NULL
    // 0xb410b0: b.eq            #0xb410b8
    // 0xb410b4: tbnz            w0, #4, #0xb410c0
    // 0xb410b8: r0 = true
    //     0xb410b8: add             x0, NULL, #0x20  ; true
    // 0xb410bc: b               #0xb41140
    // 0xb410c0: cmp             w6, NULL
    // 0xb410c4: b.ne            #0xb410d0
    // 0xb410c8: r0 = Null
    //     0xb410c8: mov             x0, NULL
    // 0xb410cc: b               #0xb41134
    // 0xb410d0: LoadField: r0 = r6->field_b
    //     0xb410d0: ldur            w0, [x6, #0xb]
    // 0xb410d4: r1 = LoadInt32Instr(r0)
    //     0xb410d4: sbfx            x1, x0, #1, #0x1f
    // 0xb410d8: mov             x0, x1
    // 0xb410dc: r1 = 0
    //     0xb410dc: movz            x1, #0
    // 0xb410e0: cmp             x1, x0
    // 0xb410e4: b.hs            #0xb43948
    // 0xb410e8: LoadField: r0 = r6->field_f
    //     0xb410e8: ldur            w0, [x6, #0xf]
    // 0xb410ec: DecompressPointer r0
    //     0xb410ec: add             x0, x0, HEAP, lsl #32
    // 0xb410f0: LoadField: r1 = r0->field_f
    //     0xb410f0: ldur            w1, [x0, #0xf]
    // 0xb410f4: DecompressPointer r1
    //     0xb410f4: add             x1, x1, HEAP, lsl #32
    // 0xb410f8: cmp             w1, NULL
    // 0xb410fc: b.ne            #0xb41108
    // 0xb41100: r0 = Null
    //     0xb41100: mov             x0, NULL
    // 0xb41104: b               #0xb41134
    // 0xb41108: LoadField: r0 = r1->field_2b
    //     0xb41108: ldur            w0, [x1, #0x2b]
    // 0xb4110c: DecompressPointer r0
    //     0xb4110c: add             x0, x0, HEAP, lsl #32
    // 0xb41110: cmp             w0, NULL
    // 0xb41114: b.ne            #0xb41120
    // 0xb41118: r0 = Null
    //     0xb41118: mov             x0, NULL
    // 0xb4111c: b               #0xb41134
    // 0xb41120: LoadField: r1 = r0->field_7
    //     0xb41120: ldur            w1, [x0, #7]
    // 0xb41124: cbz             w1, #0xb41130
    // 0xb41128: r0 = false
    //     0xb41128: add             x0, NULL, #0x30  ; false
    // 0xb4112c: b               #0xb41134
    // 0xb41130: r0 = true
    //     0xb41130: add             x0, NULL, #0x20  ; true
    // 0xb41134: cmp             w0, NULL
    // 0xb41138: b.ne            #0xb41140
    // 0xb4113c: r0 = true
    //     0xb4113c: add             x0, NULL, #0x20  ; true
    // 0xb41140: ldur            x1, [fp, #-0x10]
    // 0xb41144: stur            x0, [fp, #-0x28]
    // 0xb41148: r0 = of()
    //     0xb41148: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4114c: LoadField: r1 = r0->field_87
    //     0xb4114c: ldur            w1, [x0, #0x87]
    // 0xb41150: DecompressPointer r1
    //     0xb41150: add             x1, x1, HEAP, lsl #32
    // 0xb41154: LoadField: r0 = r1->field_2b
    //     0xb41154: ldur            w0, [x1, #0x2b]
    // 0xb41158: DecompressPointer r0
    //     0xb41158: add             x0, x0, HEAP, lsl #32
    // 0xb4115c: ldur            x1, [fp, #-0x10]
    // 0xb41160: stur            x0, [fp, #-0x48]
    // 0xb41164: r0 = of()
    //     0xb41164: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41168: LoadField: r1 = r0->field_5b
    //     0xb41168: ldur            w1, [x0, #0x5b]
    // 0xb4116c: DecompressPointer r1
    //     0xb4116c: add             x1, x1, HEAP, lsl #32
    // 0xb41170: r16 = 14.000000
    //     0xb41170: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb41174: ldr             x16, [x16, #0x1d8]
    // 0xb41178: stp             x16, x1, [SP]
    // 0xb4117c: ldur            x1, [fp, #-0x48]
    // 0xb41180: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb41180: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb41184: ldr             x4, [x4, #0x9b8]
    // 0xb41188: r0 = copyWith()
    //     0xb41188: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4118c: stur            x0, [fp, #-0x48]
    // 0xb41190: r0 = InitLateStaticField(0xa94) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::singleLineFormatter
    //     0xb41190: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb41194: ldr             x0, [x0, #0x1528]
    //     0xb41198: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4119c: cmp             w0, w16
    //     0xb411a0: b.ne            #0xb411b0
    //     0xb411a4: add             x2, PP, #0x54, lsl #12  ; [pp+0x54078] Field <FilteringTextInputFormatter.singleLineFormatter>: static late final (offset: 0xa94)
    //     0xb411a8: ldr             x2, [x2, #0x78]
    //     0xb411ac: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb411b0: stur            x0, [fp, #-0x50]
    // 0xb411b4: r0 = LengthLimitingTextInputFormatter()
    //     0xb411b4: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb411b8: mov             x3, x0
    // 0xb411bc: r0 = 240
    //     0xb411bc: movz            x0, #0xf0
    // 0xb411c0: stur            x3, [fp, #-0x58]
    // 0xb411c4: StoreField: r3->field_7 = r0
    //     0xb411c4: stur            w0, [x3, #7]
    // 0xb411c8: r1 = Null
    //     0xb411c8: mov             x1, NULL
    // 0xb411cc: r2 = 4
    //     0xb411cc: movz            x2, #0x4
    // 0xb411d0: r0 = AllocateArray()
    //     0xb411d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb411d4: mov             x2, x0
    // 0xb411d8: ldur            x0, [fp, #-0x50]
    // 0xb411dc: stur            x2, [fp, #-0x60]
    // 0xb411e0: StoreField: r2->field_f = r0
    //     0xb411e0: stur            w0, [x2, #0xf]
    // 0xb411e4: ldur            x0, [fp, #-0x58]
    // 0xb411e8: StoreField: r2->field_13 = r0
    //     0xb411e8: stur            w0, [x2, #0x13]
    // 0xb411ec: r1 = <TextInputFormatter>
    //     0xb411ec: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb411f0: ldr             x1, [x1, #0x7b0]
    // 0xb411f4: r0 = AllocateGrowableArray()
    //     0xb411f4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb411f8: mov             x2, x0
    // 0xb411fc: ldur            x0, [fp, #-0x60]
    // 0xb41200: stur            x2, [fp, #-0x58]
    // 0xb41204: StoreField: r2->field_f = r0
    //     0xb41204: stur            w0, [x2, #0xf]
    // 0xb41208: r0 = 4
    //     0xb41208: movz            x0, #0x4
    // 0xb4120c: StoreField: r2->field_b = r0
    //     0xb4120c: stur            w0, [x2, #0xb]
    // 0xb41210: ldur            x3, [fp, #-8]
    // 0xb41214: LoadField: r4 = r3->field_3b
    //     0xb41214: ldur            w4, [x3, #0x3b]
    // 0xb41218: DecompressPointer r4
    //     0xb41218: add             x4, x4, HEAP, lsl #32
    // 0xb4121c: ldur            x1, [fp, #-0x10]
    // 0xb41220: stur            x4, [fp, #-0x50]
    // 0xb41224: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb41224: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb41228: ldur            x1, [fp, #-0x10]
    // 0xb4122c: stur            x0, [fp, #-0x60]
    // 0xb41230: r0 = of()
    //     0xb41230: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41234: LoadField: r1 = r0->field_5b
    //     0xb41234: ldur            w1, [x0, #0x5b]
    // 0xb41238: DecompressPointer r1
    //     0xb41238: add             x1, x1, HEAP, lsl #32
    // 0xb4123c: stur            x1, [fp, #-0x68]
    // 0xb41240: r0 = BorderSide()
    //     0xb41240: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb41244: mov             x1, x0
    // 0xb41248: ldur            x0, [fp, #-0x68]
    // 0xb4124c: stur            x1, [fp, #-0x70]
    // 0xb41250: StoreField: r1->field_7 = r0
    //     0xb41250: stur            w0, [x1, #7]
    // 0xb41254: d0 = 1.000000
    //     0xb41254: fmov            d0, #1.00000000
    // 0xb41258: StoreField: r1->field_b = d0
    //     0xb41258: stur            d0, [x1, #0xb]
    // 0xb4125c: r0 = Instance_BorderStyle
    //     0xb4125c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb41260: ldr             x0, [x0, #0xf68]
    // 0xb41264: StoreField: r1->field_13 = r0
    //     0xb41264: stur            w0, [x1, #0x13]
    // 0xb41268: d1 = -1.000000
    //     0xb41268: fmov            d1, #-1.00000000
    // 0xb4126c: ArrayStore: r1[0] = d1  ; List_8
    //     0xb4126c: stur            d1, [x1, #0x17]
    // 0xb41270: r0 = Radius()
    //     0xb41270: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb41274: d0 = 30.000000
    //     0xb41274: fmov            d0, #30.00000000
    // 0xb41278: stur            x0, [fp, #-0x68]
    // 0xb4127c: StoreField: r0->field_7 = d0
    //     0xb4127c: stur            d0, [x0, #7]
    // 0xb41280: StoreField: r0->field_f = d0
    //     0xb41280: stur            d0, [x0, #0xf]
    // 0xb41284: r0 = BorderRadius()
    //     0xb41284: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb41288: mov             x1, x0
    // 0xb4128c: ldur            x0, [fp, #-0x68]
    // 0xb41290: stur            x1, [fp, #-0x78]
    // 0xb41294: StoreField: r1->field_7 = r0
    //     0xb41294: stur            w0, [x1, #7]
    // 0xb41298: StoreField: r1->field_b = r0
    //     0xb41298: stur            w0, [x1, #0xb]
    // 0xb4129c: StoreField: r1->field_f = r0
    //     0xb4129c: stur            w0, [x1, #0xf]
    // 0xb412a0: StoreField: r1->field_13 = r0
    //     0xb412a0: stur            w0, [x1, #0x13]
    // 0xb412a4: r0 = OutlineInputBorder()
    //     0xb412a4: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb412a8: mov             x2, x0
    // 0xb412ac: ldur            x0, [fp, #-0x78]
    // 0xb412b0: stur            x2, [fp, #-0x68]
    // 0xb412b4: StoreField: r2->field_13 = r0
    //     0xb412b4: stur            w0, [x2, #0x13]
    // 0xb412b8: d0 = 4.000000
    //     0xb412b8: fmov            d0, #4.00000000
    // 0xb412bc: StoreField: r2->field_b = d0
    //     0xb412bc: stur            d0, [x2, #0xb]
    // 0xb412c0: ldur            x0, [fp, #-0x70]
    // 0xb412c4: StoreField: r2->field_7 = r0
    //     0xb412c4: stur            w0, [x2, #7]
    // 0xb412c8: ldur            x1, [fp, #-0x10]
    // 0xb412cc: r0 = of()
    //     0xb412cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb412d0: LoadField: r1 = r0->field_87
    //     0xb412d0: ldur            w1, [x0, #0x87]
    // 0xb412d4: DecompressPointer r1
    //     0xb412d4: add             x1, x1, HEAP, lsl #32
    // 0xb412d8: LoadField: r0 = r1->field_2b
    //     0xb412d8: ldur            w0, [x1, #0x2b]
    // 0xb412dc: DecompressPointer r0
    //     0xb412dc: add             x0, x0, HEAP, lsl #32
    // 0xb412e0: r16 = 12.000000
    //     0xb412e0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb412e4: ldr             x16, [x16, #0x9e8]
    // 0xb412e8: r30 = Instance_MaterialColor
    //     0xb412e8: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb412ec: ldr             lr, [lr, #0x180]
    // 0xb412f0: stp             lr, x16, [SP]
    // 0xb412f4: mov             x1, x0
    // 0xb412f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb412f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb412fc: ldr             x4, [x4, #0xaa0]
    // 0xb41300: r0 = copyWith()
    //     0xb41300: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41304: ldur            x1, [fp, #-0x10]
    // 0xb41308: stur            x0, [fp, #-0x70]
    // 0xb4130c: r0 = of()
    //     0xb4130c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41310: LoadField: r1 = r0->field_87
    //     0xb41310: ldur            w1, [x0, #0x87]
    // 0xb41314: DecompressPointer r1
    //     0xb41314: add             x1, x1, HEAP, lsl #32
    // 0xb41318: LoadField: r0 = r1->field_2b
    //     0xb41318: ldur            w0, [x1, #0x2b]
    // 0xb4131c: DecompressPointer r0
    //     0xb4131c: add             x0, x0, HEAP, lsl #32
    // 0xb41320: stur            x0, [fp, #-0x78]
    // 0xb41324: r1 = Instance_Color
    //     0xb41324: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb41328: d0 = 0.400000
    //     0xb41328: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb4132c: r0 = withOpacity()
    //     0xb4132c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb41330: r16 = 14.000000
    //     0xb41330: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb41334: ldr             x16, [x16, #0x1d8]
    // 0xb41338: stp             x0, x16, [SP]
    // 0xb4133c: ldur            x1, [fp, #-0x78]
    // 0xb41340: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb41340: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41344: ldr             x4, [x4, #0xaa0]
    // 0xb41348: r0 = copyWith()
    //     0xb41348: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4134c: ldur            x2, [fp, #-8]
    // 0xb41350: stur            x0, [fp, #-0x88]
    // 0xb41354: LoadField: r1 = r2->field_57
    //     0xb41354: ldur            w1, [x2, #0x57]
    // 0xb41358: DecompressPointer r1
    //     0xb41358: add             x1, x1, HEAP, lsl #32
    // 0xb4135c: tbnz            w1, #4, #0xb413d0
    // 0xb41360: LoadField: r1 = r2->field_3b
    //     0xb41360: ldur            w1, [x2, #0x3b]
    // 0xb41364: DecompressPointer r1
    //     0xb41364: add             x1, x1, HEAP, lsl #32
    // 0xb41368: LoadField: r3 = r1->field_27
    //     0xb41368: ldur            w3, [x1, #0x27]
    // 0xb4136c: DecompressPointer r3
    //     0xb4136c: add             x3, x3, HEAP, lsl #32
    // 0xb41370: LoadField: r1 = r3->field_7
    //     0xb41370: ldur            w1, [x3, #7]
    // 0xb41374: DecompressPointer r1
    //     0xb41374: add             x1, x1, HEAP, lsl #32
    // 0xb41378: LoadField: r3 = r1->field_7
    //     0xb41378: ldur            w3, [x1, #7]
    // 0xb4137c: cbz             w3, #0xb4138c
    // 0xb41380: r1 = Instance_IconData
    //     0xb41380: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb41384: ldr             x1, [x1, #0x130]
    // 0xb41388: b               #0xb41394
    // 0xb4138c: r1 = Instance_IconData
    //     0xb4138c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb41390: ldr             x1, [x1, #0x138]
    // 0xb41394: stur            x1, [fp, #-0x80]
    // 0xb41398: cbz             w3, #0xb413a8
    // 0xb4139c: r3 = Instance_Color
    //     0xb4139c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb413a0: ldr             x3, [x3, #0x858]
    // 0xb413a4: b               #0xb413b0
    // 0xb413a8: r3 = Instance_Color
    //     0xb413a8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb413ac: ldr             x3, [x3, #0x50]
    // 0xb413b0: stur            x3, [fp, #-0x78]
    // 0xb413b4: r0 = Icon()
    //     0xb413b4: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb413b8: mov             x1, x0
    // 0xb413bc: ldur            x0, [fp, #-0x80]
    // 0xb413c0: StoreField: r1->field_b = r0
    //     0xb413c0: stur            w0, [x1, #0xb]
    // 0xb413c4: ldur            x0, [fp, #-0x78]
    // 0xb413c8: StoreField: r1->field_23 = r0
    //     0xb413c8: stur            w0, [x1, #0x23]
    // 0xb413cc: b               #0xb413d4
    // 0xb413d0: r1 = Null
    //     0xb413d0: mov             x1, NULL
    // 0xb413d4: ldur            x2, [fp, #-8]
    // 0xb413d8: ldur            x0, [fp, #-0x38]
    // 0xb413dc: ldur            x16, [fp, #-0x68]
    // 0xb413e0: r30 = Instance_EdgeInsets
    //     0xb413e0: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb413e4: ldr             lr, [lr, #0xa78]
    // 0xb413e8: stp             lr, x16, [SP, #0x28]
    // 0xb413ec: ldur            x16, [fp, #-0x70]
    // 0xb413f0: r30 = "House No./ Building Name*"
    //     0xb413f0: add             lr, PP, #0x54, lsl #12  ; [pp+0x54080] "House No./ Building Name*"
    //     0xb413f4: ldr             lr, [lr, #0x80]
    // 0xb413f8: stp             lr, x16, [SP, #0x18]
    // 0xb413fc: r16 = 4
    //     0xb413fc: movz            x16, #0x4
    // 0xb41400: ldur            lr, [fp, #-0x88]
    // 0xb41404: stp             lr, x16, [SP, #8]
    // 0xb41408: str             x1, [SP]
    // 0xb4140c: ldur            x1, [fp, #-0x60]
    // 0xb41410: r4 = const [0, 0x8, 0x7, 0x1, contentPadding, 0x2, errorMaxLines, 0x5, errorStyle, 0x3, focusedBorder, 0x1, hintStyle, 0x6, hintText, 0x4, suffixIcon, 0x7, null]
    //     0xb41410: add             x4, PP, #0x56, lsl #12  ; [pp+0x56808] List(19) [0, 0x8, 0x7, 0x1, "contentPadding", 0x2, "errorMaxLines", 0x5, "errorStyle", 0x3, "focusedBorder", 0x1, "hintStyle", 0x6, "hintText", 0x4, "suffixIcon", 0x7, Null]
    //     0xb41414: ldr             x4, [x4, #0x808]
    // 0xb41418: r0 = copyWith()
    //     0xb41418: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb4141c: ldur            x2, [fp, #-8]
    // 0xb41420: r1 = Function '_validateHouseNumber@1549293123':.
    //     0xb41420: add             x1, PP, #0x56, lsl #12  ; [pp+0x56810] AnonymousClosure: (0xb43d54), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateHouseNumber (0xa0594c)
    //     0xb41424: ldr             x1, [x1, #0x810]
    // 0xb41428: stur            x0, [fp, #-0x60]
    // 0xb4142c: r0 = AllocateClosure()
    //     0xb4142c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb41430: ldur            x2, [fp, #-0x18]
    // 0xb41434: r1 = Function '<anonymous closure>':.
    //     0xb41434: add             x1, PP, #0x56, lsl #12  ; [pp+0x56818] AnonymousClosure: (0xb43cdc), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xb40ae8)
    //     0xb41438: ldr             x1, [x1, #0x818]
    // 0xb4143c: stur            x0, [fp, #-0x68]
    // 0xb41440: r0 = AllocateClosure()
    //     0xb41440: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb41444: r1 = <String>
    //     0xb41444: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb41448: stur            x0, [fp, #-0x70]
    // 0xb4144c: r0 = TextFormField()
    //     0xb4144c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb41450: stur            x0, [fp, #-0x78]
    // 0xb41454: ldur            x16, [fp, #-0x68]
    // 0xb41458: r30 = Instance_AutovalidateMode
    //     0xb41458: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb4145c: ldr             lr, [lr, #0x7e8]
    // 0xb41460: stp             lr, x16, [SP, #0x40]
    // 0xb41464: r16 = true
    //     0xb41464: add             x16, NULL, #0x20  ; true
    // 0xb41468: ldur            lr, [fp, #-0x28]
    // 0xb4146c: stp             lr, x16, [SP, #0x30]
    // 0xb41470: ldur            x16, [fp, #-0x48]
    // 0xb41474: ldur            lr, [fp, #-0x58]
    // 0xb41478: stp             lr, x16, [SP, #0x20]
    // 0xb4147c: r16 = Instance_TextInputType
    //     0xb4147c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33bb8] Obj!TextInputType@d55b41
    //     0xb41480: ldr             x16, [x16, #0xbb8]
    // 0xb41484: ldur            lr, [fp, #-0x50]
    // 0xb41488: stp             lr, x16, [SP, #0x10]
    // 0xb4148c: r16 = 2
    //     0xb4148c: movz            x16, #0x2
    // 0xb41490: ldur            lr, [fp, #-0x70]
    // 0xb41494: stp             lr, x16, [SP]
    // 0xb41498: mov             x1, x0
    // 0xb4149c: ldur            x2, [fp, #-0x60]
    // 0xb414a0: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x9, enableSuggestions, 0x4, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0xa, onChanged, 0xb, style, 0x6, validator, 0x2, null]
    //     0xb414a0: add             x4, PP, #0x56, lsl #12  ; [pp+0x56820] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x9, "enableSuggestions", 0x4, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0xa, "onChanged", 0xb, "style", 0x6, "validator", 0x2, Null]
    //     0xb414a4: ldr             x4, [x4, #0x820]
    // 0xb414a8: r0 = TextFormField()
    //     0xb414a8: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb414ac: r0 = Form()
    //     0xb414ac: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb414b0: mov             x2, x0
    // 0xb414b4: ldur            x0, [fp, #-0x78]
    // 0xb414b8: stur            x2, [fp, #-0x28]
    // 0xb414bc: StoreField: r2->field_b = r0
    //     0xb414bc: stur            w0, [x2, #0xb]
    // 0xb414c0: r0 = Instance_AutovalidateMode
    //     0xb414c0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb414c4: ldr             x0, [x0, #0x800]
    // 0xb414c8: StoreField: r2->field_23 = r0
    //     0xb414c8: stur            w0, [x2, #0x23]
    // 0xb414cc: ldur            x1, [fp, #-0x38]
    // 0xb414d0: StoreField: r2->field_7 = r1
    //     0xb414d0: stur            w1, [x2, #7]
    // 0xb414d4: ldur            x1, [fp, #-0x10]
    // 0xb414d8: r0 = of()
    //     0xb414d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb414dc: LoadField: r1 = r0->field_87
    //     0xb414dc: ldur            w1, [x0, #0x87]
    // 0xb414e0: DecompressPointer r1
    //     0xb414e0: add             x1, x1, HEAP, lsl #32
    // 0xb414e4: LoadField: r0 = r1->field_2b
    //     0xb414e4: ldur            w0, [x1, #0x2b]
    // 0xb414e8: DecompressPointer r0
    //     0xb414e8: add             x0, x0, HEAP, lsl #32
    // 0xb414ec: r16 = 12.000000
    //     0xb414ec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb414f0: ldr             x16, [x16, #0x9e8]
    // 0xb414f4: r30 = Instance_Color
    //     0xb414f4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb414f8: stp             lr, x16, [SP]
    // 0xb414fc: mov             x1, x0
    // 0xb41500: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb41500: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41504: ldr             x4, [x4, #0xaa0]
    // 0xb41508: r0 = copyWith()
    //     0xb41508: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4150c: stur            x0, [fp, #-0x38]
    // 0xb41510: r0 = Text()
    //     0xb41510: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb41514: mov             x1, x0
    // 0xb41518: r0 = "Road Name / Area / Colony*"
    //     0xb41518: add             x0, PP, #0x54, lsl #12  ; [pp+0x540b0] "Road Name / Area / Colony*"
    //     0xb4151c: ldr             x0, [x0, #0xb0]
    // 0xb41520: stur            x1, [fp, #-0x48]
    // 0xb41524: StoreField: r1->field_b = r0
    //     0xb41524: stur            w0, [x1, #0xb]
    // 0xb41528: ldur            x0, [fp, #-0x38]
    // 0xb4152c: StoreField: r1->field_13 = r0
    //     0xb4152c: stur            w0, [x1, #0x13]
    // 0xb41530: r0 = Padding()
    //     0xb41530: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb41534: mov             x3, x0
    // 0xb41538: r0 = Instance_EdgeInsets
    //     0xb41538: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb4153c: ldr             x0, [x0, #0x850]
    // 0xb41540: stur            x3, [fp, #-0x50]
    // 0xb41544: StoreField: r3->field_f = r0
    //     0xb41544: stur            w0, [x3, #0xf]
    // 0xb41548: ldur            x0, [fp, #-0x48]
    // 0xb4154c: StoreField: r3->field_b = r0
    //     0xb4154c: stur            w0, [x3, #0xb]
    // 0xb41550: ldur            x4, [fp, #-8]
    // 0xb41554: LoadField: r5 = r4->field_2f
    //     0xb41554: ldur            w5, [x4, #0x2f]
    // 0xb41558: DecompressPointer r5
    //     0xb41558: add             x5, x5, HEAP, lsl #32
    // 0xb4155c: stur            x5, [fp, #-0x48]
    // 0xb41560: LoadField: r2 = r4->field_b
    //     0xb41560: ldur            w2, [x4, #0xb]
    // 0xb41564: DecompressPointer r2
    //     0xb41564: add             x2, x2, HEAP, lsl #32
    // 0xb41568: cmp             w2, NULL
    // 0xb4156c: b.eq            #0xb4394c
    // 0xb41570: LoadField: r0 = r2->field_1b
    //     0xb41570: ldur            w0, [x2, #0x1b]
    // 0xb41574: DecompressPointer r0
    //     0xb41574: add             x0, x0, HEAP, lsl #32
    // 0xb41578: LoadField: r6 = r0->field_1b
    //     0xb41578: ldur            w6, [x0, #0x1b]
    // 0xb4157c: DecompressPointer r6
    //     0xb4157c: add             x6, x6, HEAP, lsl #32
    // 0xb41580: cmp             w6, NULL
    // 0xb41584: b.ne            #0xb41590
    // 0xb41588: r0 = Null
    //     0xb41588: mov             x0, NULL
    // 0xb4158c: b               #0xb415a8
    // 0xb41590: LoadField: r0 = r6->field_b
    //     0xb41590: ldur            w0, [x6, #0xb]
    // 0xb41594: cbz             w0, #0xb415a0
    // 0xb41598: r1 = false
    //     0xb41598: add             x1, NULL, #0x30  ; false
    // 0xb4159c: b               #0xb415a4
    // 0xb415a0: r1 = true
    //     0xb415a0: add             x1, NULL, #0x20  ; true
    // 0xb415a4: mov             x0, x1
    // 0xb415a8: cmp             w0, NULL
    // 0xb415ac: b.eq            #0xb415b4
    // 0xb415b0: tbnz            w0, #4, #0xb415bc
    // 0xb415b4: r6 = true
    //     0xb415b4: add             x6, NULL, #0x20  ; true
    // 0xb415b8: b               #0xb41640
    // 0xb415bc: cmp             w6, NULL
    // 0xb415c0: b.ne            #0xb415cc
    // 0xb415c4: r0 = Null
    //     0xb415c4: mov             x0, NULL
    // 0xb415c8: b               #0xb41630
    // 0xb415cc: LoadField: r0 = r6->field_b
    //     0xb415cc: ldur            w0, [x6, #0xb]
    // 0xb415d0: r1 = LoadInt32Instr(r0)
    //     0xb415d0: sbfx            x1, x0, #1, #0x1f
    // 0xb415d4: mov             x0, x1
    // 0xb415d8: r1 = 0
    //     0xb415d8: movz            x1, #0
    // 0xb415dc: cmp             x1, x0
    // 0xb415e0: b.hs            #0xb43950
    // 0xb415e4: LoadField: r0 = r6->field_f
    //     0xb415e4: ldur            w0, [x6, #0xf]
    // 0xb415e8: DecompressPointer r0
    //     0xb415e8: add             x0, x0, HEAP, lsl #32
    // 0xb415ec: LoadField: r1 = r0->field_f
    //     0xb415ec: ldur            w1, [x0, #0xf]
    // 0xb415f0: DecompressPointer r1
    //     0xb415f0: add             x1, x1, HEAP, lsl #32
    // 0xb415f4: cmp             w1, NULL
    // 0xb415f8: b.ne            #0xb41604
    // 0xb415fc: r0 = Null
    //     0xb415fc: mov             x0, NULL
    // 0xb41600: b               #0xb41630
    // 0xb41604: LoadField: r0 = r1->field_13
    //     0xb41604: ldur            w0, [x1, #0x13]
    // 0xb41608: DecompressPointer r0
    //     0xb41608: add             x0, x0, HEAP, lsl #32
    // 0xb4160c: cmp             w0, NULL
    // 0xb41610: b.ne            #0xb4161c
    // 0xb41614: r0 = Null
    //     0xb41614: mov             x0, NULL
    // 0xb41618: b               #0xb41630
    // 0xb4161c: LoadField: r1 = r0->field_7
    //     0xb4161c: ldur            w1, [x0, #7]
    // 0xb41620: cbz             w1, #0xb4162c
    // 0xb41624: r0 = false
    //     0xb41624: add             x0, NULL, #0x30  ; false
    // 0xb41628: b               #0xb41630
    // 0xb4162c: r0 = true
    //     0xb4162c: add             x0, NULL, #0x20  ; true
    // 0xb41630: cmp             w0, NULL
    // 0xb41634: b.ne            #0xb4163c
    // 0xb41638: r0 = true
    //     0xb41638: add             x0, NULL, #0x20  ; true
    // 0xb4163c: mov             x6, x0
    // 0xb41640: stur            x6, [fp, #-0x38]
    // 0xb41644: LoadField: r0 = r2->field_b
    //     0xb41644: ldur            w0, [x2, #0xb]
    // 0xb41648: DecompressPointer r0
    //     0xb41648: add             x0, x0, HEAP, lsl #32
    // 0xb4164c: LoadField: r1 = r0->field_f
    //     0xb4164c: ldur            w1, [x0, #0xf]
    // 0xb41650: DecompressPointer r1
    //     0xb41650: add             x1, x1, HEAP, lsl #32
    // 0xb41654: cmp             w1, NULL
    // 0xb41658: b.ne            #0xb41664
    // 0xb4165c: r0 = Null
    //     0xb4165c: mov             x0, NULL
    // 0xb41660: b               #0xb41684
    // 0xb41664: r0 = LoadClassIdInstr(r1)
    //     0xb41664: ldur            x0, [x1, #-1]
    //     0xb41668: ubfx            x0, x0, #0xc, #0x14
    // 0xb4166c: r2 = "landmark"
    //     0xb4166c: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xb41670: ldr             x2, [x2, #0x930]
    // 0xb41674: r0 = GDT[cid_x0 + 0xe437]()
    //     0xb41674: movz            x17, #0xe437
    //     0xb41678: add             lr, x0, x17
    //     0xb4167c: ldr             lr, [x21, lr, lsl #3]
    //     0xb41680: blr             lr
    // 0xb41684: cmp             w0, NULL
    // 0xb41688: b.eq            #0xb41698
    // 0xb4168c: tbnz            w0, #4, #0xb41698
    // 0xb41690: r0 = 250
    //     0xb41690: movz            x0, #0xfa
    // 0xb41694: b               #0xb4169c
    // 0xb41698: r0 = 370
    //     0xb41698: movz            x0, #0x172
    // 0xb4169c: ldur            x2, [fp, #-8]
    // 0xb416a0: lsl             x1, x0, #1
    // 0xb416a4: stur            x1, [fp, #-0x58]
    // 0xb416a8: r0 = LengthLimitingTextInputFormatter()
    //     0xb416a8: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb416ac: mov             x1, x0
    // 0xb416b0: ldur            x0, [fp, #-0x58]
    // 0xb416b4: stur            x1, [fp, #-0x60]
    // 0xb416b8: StoreField: r1->field_7 = r0
    //     0xb416b8: stur            w0, [x1, #7]
    // 0xb416bc: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb416bc: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb416c0: ldr             x16, [x16, #0xa8]
    // 0xb416c4: stp             x16, NULL, [SP, #0x20]
    // 0xb416c8: r16 = false
    //     0xb416c8: add             x16, NULL, #0x30  ; false
    // 0xb416cc: r30 = true
    //     0xb416cc: add             lr, NULL, #0x20  ; true
    // 0xb416d0: stp             lr, x16, [SP, #0x10]
    // 0xb416d4: r16 = false
    //     0xb416d4: add             x16, NULL, #0x30  ; false
    // 0xb416d8: r30 = false
    //     0xb416d8: add             lr, NULL, #0x30  ; false
    // 0xb416dc: stp             lr, x16, [SP]
    // 0xb416e0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb416e0: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb416e4: r0 = _RegExp()
    //     0xb416e4: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb416e8: stur            x0, [fp, #-0x58]
    // 0xb416ec: r0 = FilteringTextInputFormatter()
    //     0xb416ec: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb416f0: mov             x3, x0
    // 0xb416f4: ldur            x0, [fp, #-0x58]
    // 0xb416f8: stur            x3, [fp, #-0x68]
    // 0xb416fc: StoreField: r3->field_b = r0
    //     0xb416fc: stur            w0, [x3, #0xb]
    // 0xb41700: r0 = true
    //     0xb41700: add             x0, NULL, #0x20  ; true
    // 0xb41704: StoreField: r3->field_7 = r0
    //     0xb41704: stur            w0, [x3, #7]
    // 0xb41708: r4 = ""
    //     0xb41708: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4170c: StoreField: r3->field_f = r4
    //     0xb4170c: stur            w4, [x3, #0xf]
    // 0xb41710: r1 = Null
    //     0xb41710: mov             x1, NULL
    // 0xb41714: r2 = 4
    //     0xb41714: movz            x2, #0x4
    // 0xb41718: r0 = AllocateArray()
    //     0xb41718: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4171c: mov             x2, x0
    // 0xb41720: ldur            x0, [fp, #-0x60]
    // 0xb41724: stur            x2, [fp, #-0x58]
    // 0xb41728: StoreField: r2->field_f = r0
    //     0xb41728: stur            w0, [x2, #0xf]
    // 0xb4172c: ldur            x0, [fp, #-0x68]
    // 0xb41730: StoreField: r2->field_13 = r0
    //     0xb41730: stur            w0, [x2, #0x13]
    // 0xb41734: r1 = <TextInputFormatter>
    //     0xb41734: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb41738: ldr             x1, [x1, #0x7b0]
    // 0xb4173c: r0 = AllocateGrowableArray()
    //     0xb4173c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb41740: mov             x2, x0
    // 0xb41744: ldur            x0, [fp, #-0x58]
    // 0xb41748: stur            x2, [fp, #-0x60]
    // 0xb4174c: StoreField: r2->field_f = r0
    //     0xb4174c: stur            w0, [x2, #0xf]
    // 0xb41750: r0 = 4
    //     0xb41750: movz            x0, #0x4
    // 0xb41754: StoreField: r2->field_b = r0
    //     0xb41754: stur            w0, [x2, #0xb]
    // 0xb41758: ldur            x1, [fp, #-0x10]
    // 0xb4175c: r0 = of()
    //     0xb4175c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41760: LoadField: r1 = r0->field_87
    //     0xb41760: ldur            w1, [x0, #0x87]
    // 0xb41764: DecompressPointer r1
    //     0xb41764: add             x1, x1, HEAP, lsl #32
    // 0xb41768: LoadField: r0 = r1->field_2b
    //     0xb41768: ldur            w0, [x1, #0x2b]
    // 0xb4176c: DecompressPointer r0
    //     0xb4176c: add             x0, x0, HEAP, lsl #32
    // 0xb41770: ldur            x1, [fp, #-0x10]
    // 0xb41774: stur            x0, [fp, #-0x58]
    // 0xb41778: r0 = of()
    //     0xb41778: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4177c: LoadField: r1 = r0->field_5b
    //     0xb4177c: ldur            w1, [x0, #0x5b]
    // 0xb41780: DecompressPointer r1
    //     0xb41780: add             x1, x1, HEAP, lsl #32
    // 0xb41784: r16 = 14.000000
    //     0xb41784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb41788: ldr             x16, [x16, #0x1d8]
    // 0xb4178c: stp             x16, x1, [SP]
    // 0xb41790: ldur            x1, [fp, #-0x58]
    // 0xb41794: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb41794: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb41798: ldr             x4, [x4, #0x9b8]
    // 0xb4179c: r0 = copyWith()
    //     0xb4179c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb417a0: ldur            x2, [fp, #-8]
    // 0xb417a4: stur            x0, [fp, #-0x68]
    // 0xb417a8: LoadField: r3 = r2->field_47
    //     0xb417a8: ldur            w3, [x2, #0x47]
    // 0xb417ac: DecompressPointer r3
    //     0xb417ac: add             x3, x3, HEAP, lsl #32
    // 0xb417b0: ldur            x1, [fp, #-0x10]
    // 0xb417b4: stur            x3, [fp, #-0x58]
    // 0xb417b8: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb417b8: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb417bc: ldur            x1, [fp, #-0x10]
    // 0xb417c0: stur            x0, [fp, #-0x70]
    // 0xb417c4: r0 = of()
    //     0xb417c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb417c8: LoadField: r1 = r0->field_5b
    //     0xb417c8: ldur            w1, [x0, #0x5b]
    // 0xb417cc: DecompressPointer r1
    //     0xb417cc: add             x1, x1, HEAP, lsl #32
    // 0xb417d0: stur            x1, [fp, #-0x78]
    // 0xb417d4: r0 = BorderSide()
    //     0xb417d4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb417d8: mov             x1, x0
    // 0xb417dc: ldur            x0, [fp, #-0x78]
    // 0xb417e0: stur            x1, [fp, #-0x80]
    // 0xb417e4: StoreField: r1->field_7 = r0
    //     0xb417e4: stur            w0, [x1, #7]
    // 0xb417e8: d0 = 1.000000
    //     0xb417e8: fmov            d0, #1.00000000
    // 0xb417ec: StoreField: r1->field_b = d0
    //     0xb417ec: stur            d0, [x1, #0xb]
    // 0xb417f0: r0 = Instance_BorderStyle
    //     0xb417f0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb417f4: ldr             x0, [x0, #0xf68]
    // 0xb417f8: StoreField: r1->field_13 = r0
    //     0xb417f8: stur            w0, [x1, #0x13]
    // 0xb417fc: d1 = -1.000000
    //     0xb417fc: fmov            d1, #-1.00000000
    // 0xb41800: ArrayStore: r1[0] = d1  ; List_8
    //     0xb41800: stur            d1, [x1, #0x17]
    // 0xb41804: r0 = Radius()
    //     0xb41804: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb41808: d0 = 30.000000
    //     0xb41808: fmov            d0, #30.00000000
    // 0xb4180c: stur            x0, [fp, #-0x78]
    // 0xb41810: StoreField: r0->field_7 = d0
    //     0xb41810: stur            d0, [x0, #7]
    // 0xb41814: StoreField: r0->field_f = d0
    //     0xb41814: stur            d0, [x0, #0xf]
    // 0xb41818: r0 = BorderRadius()
    //     0xb41818: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4181c: mov             x1, x0
    // 0xb41820: ldur            x0, [fp, #-0x78]
    // 0xb41824: stur            x1, [fp, #-0x88]
    // 0xb41828: StoreField: r1->field_7 = r0
    //     0xb41828: stur            w0, [x1, #7]
    // 0xb4182c: StoreField: r1->field_b = r0
    //     0xb4182c: stur            w0, [x1, #0xb]
    // 0xb41830: StoreField: r1->field_f = r0
    //     0xb41830: stur            w0, [x1, #0xf]
    // 0xb41834: StoreField: r1->field_13 = r0
    //     0xb41834: stur            w0, [x1, #0x13]
    // 0xb41838: r0 = OutlineInputBorder()
    //     0xb41838: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb4183c: mov             x2, x0
    // 0xb41840: ldur            x0, [fp, #-0x88]
    // 0xb41844: stur            x2, [fp, #-0x78]
    // 0xb41848: StoreField: r2->field_13 = r0
    //     0xb41848: stur            w0, [x2, #0x13]
    // 0xb4184c: d0 = 4.000000
    //     0xb4184c: fmov            d0, #4.00000000
    // 0xb41850: StoreField: r2->field_b = d0
    //     0xb41850: stur            d0, [x2, #0xb]
    // 0xb41854: ldur            x0, [fp, #-0x80]
    // 0xb41858: StoreField: r2->field_7 = r0
    //     0xb41858: stur            w0, [x2, #7]
    // 0xb4185c: ldur            x1, [fp, #-0x10]
    // 0xb41860: r0 = of()
    //     0xb41860: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41864: LoadField: r1 = r0->field_87
    //     0xb41864: ldur            w1, [x0, #0x87]
    // 0xb41868: DecompressPointer r1
    //     0xb41868: add             x1, x1, HEAP, lsl #32
    // 0xb4186c: LoadField: r0 = r1->field_2b
    //     0xb4186c: ldur            w0, [x1, #0x2b]
    // 0xb41870: DecompressPointer r0
    //     0xb41870: add             x0, x0, HEAP, lsl #32
    // 0xb41874: r16 = 12.000000
    //     0xb41874: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb41878: ldr             x16, [x16, #0x9e8]
    // 0xb4187c: r30 = Instance_MaterialColor
    //     0xb4187c: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb41880: ldr             lr, [lr, #0x180]
    // 0xb41884: stp             lr, x16, [SP]
    // 0xb41888: mov             x1, x0
    // 0xb4188c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4188c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41890: ldr             x4, [x4, #0xaa0]
    // 0xb41894: r0 = copyWith()
    //     0xb41894: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41898: ldur            x1, [fp, #-0x10]
    // 0xb4189c: stur            x0, [fp, #-0x80]
    // 0xb418a0: r0 = of()
    //     0xb418a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb418a4: LoadField: r1 = r0->field_87
    //     0xb418a4: ldur            w1, [x0, #0x87]
    // 0xb418a8: DecompressPointer r1
    //     0xb418a8: add             x1, x1, HEAP, lsl #32
    // 0xb418ac: LoadField: r0 = r1->field_2b
    //     0xb418ac: ldur            w0, [x1, #0x2b]
    // 0xb418b0: DecompressPointer r0
    //     0xb418b0: add             x0, x0, HEAP, lsl #32
    // 0xb418b4: stur            x0, [fp, #-0x88]
    // 0xb418b8: r1 = Instance_Color
    //     0xb418b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb418bc: d0 = 0.400000
    //     0xb418bc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb418c0: r0 = withOpacity()
    //     0xb418c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb418c4: r16 = 14.000000
    //     0xb418c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb418c8: ldr             x16, [x16, #0x1d8]
    // 0xb418cc: stp             x0, x16, [SP]
    // 0xb418d0: ldur            x1, [fp, #-0x88]
    // 0xb418d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb418d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb418d8: ldr             x4, [x4, #0xaa0]
    // 0xb418dc: r0 = copyWith()
    //     0xb418dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb418e0: mov             x1, x0
    // 0xb418e4: ldur            x2, [fp, #-8]
    // 0xb418e8: stur            x1, [fp, #-0x88]
    // 0xb418ec: LoadField: r0 = r2->field_5b
    //     0xb418ec: ldur            w0, [x2, #0x5b]
    // 0xb418f0: DecompressPointer r0
    //     0xb418f0: add             x0, x0, HEAP, lsl #32
    // 0xb418f4: tbnz            w0, #4, #0xb41a20
    // 0xb418f8: LoadField: r0 = r2->field_47
    //     0xb418f8: ldur            w0, [x2, #0x47]
    // 0xb418fc: DecompressPointer r0
    //     0xb418fc: add             x0, x0, HEAP, lsl #32
    // 0xb41900: LoadField: r3 = r0->field_27
    //     0xb41900: ldur            w3, [x0, #0x27]
    // 0xb41904: DecompressPointer r3
    //     0xb41904: add             x3, x3, HEAP, lsl #32
    // 0xb41908: LoadField: r0 = r3->field_7
    //     0xb41908: ldur            w0, [x3, #7]
    // 0xb4190c: DecompressPointer r0
    //     0xb4190c: add             x0, x0, HEAP, lsl #32
    // 0xb41910: r3 = LoadClassIdInstr(r0)
    //     0xb41910: ldur            x3, [x0, #-1]
    //     0xb41914: ubfx            x3, x3, #0xc, #0x14
    // 0xb41918: r16 = ""
    //     0xb41918: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4191c: stp             x16, x0, [SP]
    // 0xb41920: mov             x0, x3
    // 0xb41924: mov             lr, x0
    // 0xb41928: ldr             lr, [x21, lr, lsl #3]
    // 0xb4192c: blr             lr
    // 0xb41930: tbz             w0, #4, #0xb4196c
    // 0xb41934: ldur            x2, [fp, #-8]
    // 0xb41938: LoadField: r0 = r2->field_47
    //     0xb41938: ldur            w0, [x2, #0x47]
    // 0xb4193c: DecompressPointer r0
    //     0xb4193c: add             x0, x0, HEAP, lsl #32
    // 0xb41940: LoadField: r1 = r0->field_27
    //     0xb41940: ldur            w1, [x0, #0x27]
    // 0xb41944: DecompressPointer r1
    //     0xb41944: add             x1, x1, HEAP, lsl #32
    // 0xb41948: LoadField: r0 = r1->field_7
    //     0xb41948: ldur            w0, [x1, #7]
    // 0xb4194c: DecompressPointer r0
    //     0xb4194c: add             x0, x0, HEAP, lsl #32
    // 0xb41950: LoadField: r1 = r0->field_7
    //     0xb41950: ldur            w1, [x0, #7]
    // 0xb41954: r0 = LoadInt32Instr(r1)
    //     0xb41954: sbfx            x0, x1, #1, #0x1f
    // 0xb41958: cmp             x0, #0x14
    // 0xb4195c: b.lt            #0xb41970
    // 0xb41960: r1 = Instance_IconData
    //     0xb41960: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb41964: ldr             x1, [x1, #0x130]
    // 0xb41968: b               #0xb41978
    // 0xb4196c: ldur            x2, [fp, #-8]
    // 0xb41970: r1 = Instance_IconData
    //     0xb41970: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb41974: ldr             x1, [x1, #0x138]
    // 0xb41978: stur            x1, [fp, #-0x90]
    // 0xb4197c: LoadField: r0 = r2->field_47
    //     0xb4197c: ldur            w0, [x2, #0x47]
    // 0xb41980: DecompressPointer r0
    //     0xb41980: add             x0, x0, HEAP, lsl #32
    // 0xb41984: LoadField: r3 = r0->field_27
    //     0xb41984: ldur            w3, [x0, #0x27]
    // 0xb41988: DecompressPointer r3
    //     0xb41988: add             x3, x3, HEAP, lsl #32
    // 0xb4198c: LoadField: r0 = r3->field_7
    //     0xb4198c: ldur            w0, [x3, #7]
    // 0xb41990: DecompressPointer r0
    //     0xb41990: add             x0, x0, HEAP, lsl #32
    // 0xb41994: r3 = LoadClassIdInstr(r0)
    //     0xb41994: ldur            x3, [x0, #-1]
    //     0xb41998: ubfx            x3, x3, #0xc, #0x14
    // 0xb4199c: r16 = ""
    //     0xb4199c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb419a0: stp             x16, x0, [SP]
    // 0xb419a4: mov             x0, x3
    // 0xb419a8: mov             lr, x0
    // 0xb419ac: ldr             lr, [x21, lr, lsl #3]
    // 0xb419b0: blr             lr
    // 0xb419b4: tbz             w0, #4, #0xb419f0
    // 0xb419b8: ldur            x2, [fp, #-8]
    // 0xb419bc: LoadField: r0 = r2->field_47
    //     0xb419bc: ldur            w0, [x2, #0x47]
    // 0xb419c0: DecompressPointer r0
    //     0xb419c0: add             x0, x0, HEAP, lsl #32
    // 0xb419c4: LoadField: r1 = r0->field_27
    //     0xb419c4: ldur            w1, [x0, #0x27]
    // 0xb419c8: DecompressPointer r1
    //     0xb419c8: add             x1, x1, HEAP, lsl #32
    // 0xb419cc: LoadField: r0 = r1->field_7
    //     0xb419cc: ldur            w0, [x1, #7]
    // 0xb419d0: DecompressPointer r0
    //     0xb419d0: add             x0, x0, HEAP, lsl #32
    // 0xb419d4: LoadField: r1 = r0->field_7
    //     0xb419d4: ldur            w1, [x0, #7]
    // 0xb419d8: r0 = LoadInt32Instr(r1)
    //     0xb419d8: sbfx            x0, x1, #1, #0x1f
    // 0xb419dc: cmp             x0, #0x14
    // 0xb419e0: b.lt            #0xb419f4
    // 0xb419e4: r1 = Instance_Color
    //     0xb419e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb419e8: ldr             x1, [x1, #0x858]
    // 0xb419ec: b               #0xb419fc
    // 0xb419f0: ldur            x2, [fp, #-8]
    // 0xb419f4: r1 = Instance_Color
    //     0xb419f4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb419f8: ldr             x1, [x1, #0x50]
    // 0xb419fc: ldur            x0, [fp, #-0x90]
    // 0xb41a00: stur            x1, [fp, #-0x98]
    // 0xb41a04: r0 = Icon()
    //     0xb41a04: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb41a08: mov             x1, x0
    // 0xb41a0c: ldur            x0, [fp, #-0x90]
    // 0xb41a10: StoreField: r1->field_b = r0
    //     0xb41a10: stur            w0, [x1, #0xb]
    // 0xb41a14: ldur            x0, [fp, #-0x98]
    // 0xb41a18: StoreField: r1->field_23 = r0
    //     0xb41a18: stur            w0, [x1, #0x23]
    // 0xb41a1c: b               #0xb41a24
    // 0xb41a20: r1 = Null
    //     0xb41a20: mov             x1, NULL
    // 0xb41a24: ldur            x2, [fp, #-8]
    // 0xb41a28: ldur            x0, [fp, #-0x50]
    // 0xb41a2c: ldur            x3, [fp, #-0x48]
    // 0xb41a30: ldur            x16, [fp, #-0x78]
    // 0xb41a34: ldur            lr, [fp, #-0x80]
    // 0xb41a38: stp             lr, x16, [SP, #0x20]
    // 0xb41a3c: r16 = Instance_EdgeInsets
    //     0xb41a3c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56828] Obj!EdgeInsets@d58d01
    //     0xb41a40: ldr             x16, [x16, #0x828]
    // 0xb41a44: r30 = "Road Name / Area / Colony*"
    //     0xb41a44: add             lr, PP, #0x54, lsl #12  ; [pp+0x540b0] "Road Name / Area / Colony*"
    //     0xb41a48: ldr             lr, [lr, #0xb0]
    // 0xb41a4c: stp             lr, x16, [SP, #0x10]
    // 0xb41a50: ldur            x16, [fp, #-0x88]
    // 0xb41a54: stp             x1, x16, [SP]
    // 0xb41a58: ldur            x1, [fp, #-0x70]
    // 0xb41a5c: r4 = const [0, 0x7, 0x6, 0x1, contentPadding, 0x3, errorStyle, 0x2, focusedBorder, 0x1, hintStyle, 0x5, hintText, 0x4, suffixIcon, 0x6, null]
    //     0xb41a5c: add             x4, PP, #0x56, lsl #12  ; [pp+0x56830] List(17) [0, 0x7, 0x6, 0x1, "contentPadding", 0x3, "errorStyle", 0x2, "focusedBorder", 0x1, "hintStyle", 0x5, "hintText", 0x4, "suffixIcon", 0x6, Null]
    //     0xb41a60: ldr             x4, [x4, #0x830]
    // 0xb41a64: r0 = copyWith()
    //     0xb41a64: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb41a68: ldur            x2, [fp, #-8]
    // 0xb41a6c: r1 = Function '_validateAddress@1549293123':.
    //     0xb41a6c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56838] AnonymousClosure: (0xb43ca0), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress (0xa023b0)
    //     0xb41a70: ldr             x1, [x1, #0x838]
    // 0xb41a74: stur            x0, [fp, #-0x70]
    // 0xb41a78: r0 = AllocateClosure()
    //     0xb41a78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb41a7c: ldur            x2, [fp, #-0x18]
    // 0xb41a80: r1 = Function '<anonymous closure>':.
    //     0xb41a80: add             x1, PP, #0x56, lsl #12  ; [pp+0x56840] AnonymousClosure: (0xb43c28), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xb40ae8)
    //     0xb41a84: ldr             x1, [x1, #0x840]
    // 0xb41a88: stur            x0, [fp, #-0x78]
    // 0xb41a8c: r0 = AllocateClosure()
    //     0xb41a8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb41a90: r1 = <String>
    //     0xb41a90: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb41a94: stur            x0, [fp, #-0x80]
    // 0xb41a98: r0 = TextFormField()
    //     0xb41a98: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb41a9c: stur            x0, [fp, #-0x88]
    // 0xb41aa0: ldur            x16, [fp, #-0x78]
    // 0xb41aa4: r30 = Instance_AutovalidateMode
    //     0xb41aa4: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb41aa8: ldr             lr, [lr, #0x7e8]
    // 0xb41aac: stp             lr, x16, [SP, #0x48]
    // 0xb41ab0: r16 = true
    //     0xb41ab0: add             x16, NULL, #0x20  ; true
    // 0xb41ab4: ldur            lr, [fp, #-0x38]
    // 0xb41ab8: stp             lr, x16, [SP, #0x38]
    // 0xb41abc: ldur            x16, [fp, #-0x60]
    // 0xb41ac0: r30 = Instance_TextInputType
    //     0xb41ac0: add             lr, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0xb41ac4: ldr             lr, [lr, #0x7f0]
    // 0xb41ac8: stp             lr, x16, [SP, #0x28]
    // 0xb41acc: r16 = 2
    //     0xb41acc: movz            x16, #0x2
    // 0xb41ad0: r30 = 6
    //     0xb41ad0: movz            lr, #0x6
    // 0xb41ad4: stp             lr, x16, [SP, #0x18]
    // 0xb41ad8: ldur            x16, [fp, #-0x68]
    // 0xb41adc: ldur            lr, [fp, #-0x58]
    // 0xb41ae0: stp             lr, x16, [SP, #8]
    // 0xb41ae4: ldur            x16, [fp, #-0x80]
    // 0xb41ae8: str             x16, [SP]
    // 0xb41aec: mov             x1, x0
    // 0xb41af0: ldur            x2, [fp, #-0x70]
    // 0xb41af4: r4 = const [0, 0xd, 0xb, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0xb, enableSuggestions, 0x4, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, minLines, 0x8, onChanged, 0xc, style, 0xa, validator, 0x2, null]
    //     0xb41af4: add             x4, PP, #0x54, lsl #12  ; [pp+0x548b0] List(27) [0, 0xd, 0xb, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0xb, "enableSuggestions", 0x4, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "minLines", 0x8, "onChanged", 0xc, "style", 0xa, "validator", 0x2, Null]
    //     0xb41af8: ldr             x4, [x4, #0x8b0]
    // 0xb41afc: r0 = TextFormField()
    //     0xb41afc: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb41b00: r0 = Form()
    //     0xb41b00: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb41b04: mov             x3, x0
    // 0xb41b08: ldur            x0, [fp, #-0x88]
    // 0xb41b0c: stur            x3, [fp, #-0x38]
    // 0xb41b10: StoreField: r3->field_b = r0
    //     0xb41b10: stur            w0, [x3, #0xb]
    // 0xb41b14: r0 = Instance_AutovalidateMode
    //     0xb41b14: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb41b18: ldr             x0, [x0, #0x800]
    // 0xb41b1c: StoreField: r3->field_23 = r0
    //     0xb41b1c: stur            w0, [x3, #0x23]
    // 0xb41b20: ldur            x1, [fp, #-0x48]
    // 0xb41b24: StoreField: r3->field_7 = r1
    //     0xb41b24: stur            w1, [x3, #7]
    // 0xb41b28: r1 = Null
    //     0xb41b28: mov             x1, NULL
    // 0xb41b2c: r2 = 4
    //     0xb41b2c: movz            x2, #0x4
    // 0xb41b30: r0 = AllocateArray()
    //     0xb41b30: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb41b34: mov             x2, x0
    // 0xb41b38: ldur            x0, [fp, #-0x50]
    // 0xb41b3c: stur            x2, [fp, #-0x48]
    // 0xb41b40: StoreField: r2->field_f = r0
    //     0xb41b40: stur            w0, [x2, #0xf]
    // 0xb41b44: ldur            x0, [fp, #-0x38]
    // 0xb41b48: StoreField: r2->field_13 = r0
    //     0xb41b48: stur            w0, [x2, #0x13]
    // 0xb41b4c: r1 = <Widget>
    //     0xb41b4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb41b50: r0 = AllocateGrowableArray()
    //     0xb41b50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb41b54: mov             x1, x0
    // 0xb41b58: ldur            x0, [fp, #-0x48]
    // 0xb41b5c: stur            x1, [fp, #-0x38]
    // 0xb41b60: StoreField: r1->field_f = r0
    //     0xb41b60: stur            w0, [x1, #0xf]
    // 0xb41b64: r2 = 4
    //     0xb41b64: movz            x2, #0x4
    // 0xb41b68: StoreField: r1->field_b = r2
    //     0xb41b68: stur            w2, [x1, #0xb]
    // 0xb41b6c: r0 = Column()
    //     0xb41b6c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb41b70: mov             x1, x0
    // 0xb41b74: r0 = Instance_Axis
    //     0xb41b74: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb41b78: stur            x1, [fp, #-0x48]
    // 0xb41b7c: StoreField: r1->field_f = r0
    //     0xb41b7c: stur            w0, [x1, #0xf]
    // 0xb41b80: r2 = Instance_MainAxisAlignment
    //     0xb41b80: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb41b84: ldr             x2, [x2, #0xa08]
    // 0xb41b88: StoreField: r1->field_13 = r2
    //     0xb41b88: stur            w2, [x1, #0x13]
    // 0xb41b8c: r3 = Instance_MainAxisSize
    //     0xb41b8c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb41b90: ldr             x3, [x3, #0xa10]
    // 0xb41b94: ArrayStore: r1[0] = r3  ; List_4
    //     0xb41b94: stur            w3, [x1, #0x17]
    // 0xb41b98: r4 = Instance_CrossAxisAlignment
    //     0xb41b98: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb41b9c: ldr             x4, [x4, #0x890]
    // 0xb41ba0: StoreField: r1->field_1b = r4
    //     0xb41ba0: stur            w4, [x1, #0x1b]
    // 0xb41ba4: r5 = Instance_VerticalDirection
    //     0xb41ba4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb41ba8: ldr             x5, [x5, #0xa20]
    // 0xb41bac: StoreField: r1->field_23 = r5
    //     0xb41bac: stur            w5, [x1, #0x23]
    // 0xb41bb0: r6 = Instance_Clip
    //     0xb41bb0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb41bb4: ldr             x6, [x6, #0x38]
    // 0xb41bb8: StoreField: r1->field_2b = r6
    //     0xb41bb8: stur            w6, [x1, #0x2b]
    // 0xb41bbc: StoreField: r1->field_2f = rZR
    //     0xb41bbc: stur            xzr, [x1, #0x2f]
    // 0xb41bc0: ldur            x7, [fp, #-0x38]
    // 0xb41bc4: StoreField: r1->field_b = r7
    //     0xb41bc4: stur            w7, [x1, #0xb]
    // 0xb41bc8: r0 = Padding()
    //     0xb41bc8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb41bcc: mov             x2, x0
    // 0xb41bd0: r0 = Instance_EdgeInsets
    //     0xb41bd0: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb41bd4: ldr             x0, [x0, #0x858]
    // 0xb41bd8: stur            x2, [fp, #-0x38]
    // 0xb41bdc: StoreField: r2->field_f = r0
    //     0xb41bdc: stur            w0, [x2, #0xf]
    // 0xb41be0: ldur            x1, [fp, #-0x48]
    // 0xb41be4: StoreField: r2->field_b = r1
    //     0xb41be4: stur            w1, [x2, #0xb]
    // 0xb41be8: ldur            x1, [fp, #-0x10]
    // 0xb41bec: r0 = of()
    //     0xb41bec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41bf0: LoadField: r1 = r0->field_87
    //     0xb41bf0: ldur            w1, [x0, #0x87]
    // 0xb41bf4: DecompressPointer r1
    //     0xb41bf4: add             x1, x1, HEAP, lsl #32
    // 0xb41bf8: LoadField: r0 = r1->field_2b
    //     0xb41bf8: ldur            w0, [x1, #0x2b]
    // 0xb41bfc: DecompressPointer r0
    //     0xb41bfc: add             x0, x0, HEAP, lsl #32
    // 0xb41c00: r16 = 12.000000
    //     0xb41c00: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb41c04: ldr             x16, [x16, #0x9e8]
    // 0xb41c08: r30 = Instance_Color
    //     0xb41c08: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb41c0c: stp             lr, x16, [SP]
    // 0xb41c10: mov             x1, x0
    // 0xb41c14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb41c14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41c18: ldr             x4, [x4, #0xaa0]
    // 0xb41c1c: r0 = copyWith()
    //     0xb41c1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41c20: stur            x0, [fp, #-0x48]
    // 0xb41c24: r0 = Text()
    //     0xb41c24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb41c28: mov             x1, x0
    // 0xb41c2c: r0 = "Pincode*"
    //     0xb41c2c: add             x0, PP, #0x54, lsl #12  ; [pp+0x540d8] "Pincode*"
    //     0xb41c30: ldr             x0, [x0, #0xd8]
    // 0xb41c34: stur            x1, [fp, #-0x50]
    // 0xb41c38: StoreField: r1->field_b = r0
    //     0xb41c38: stur            w0, [x1, #0xb]
    // 0xb41c3c: ldur            x0, [fp, #-0x48]
    // 0xb41c40: StoreField: r1->field_13 = r0
    //     0xb41c40: stur            w0, [x1, #0x13]
    // 0xb41c44: r0 = Padding()
    //     0xb41c44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb41c48: mov             x2, x0
    // 0xb41c4c: r0 = Instance_EdgeInsets
    //     0xb41c4c: add             x0, PP, #0x56, lsl #12  ; [pp+0x567e0] Obj!EdgeInsets@d58d31
    //     0xb41c50: ldr             x0, [x0, #0x7e0]
    // 0xb41c54: stur            x2, [fp, #-0x58]
    // 0xb41c58: StoreField: r2->field_f = r0
    //     0xb41c58: stur            w0, [x2, #0xf]
    // 0xb41c5c: ldur            x0, [fp, #-0x50]
    // 0xb41c60: StoreField: r2->field_b = r0
    //     0xb41c60: stur            w0, [x2, #0xb]
    // 0xb41c64: ldur            x3, [fp, #-8]
    // 0xb41c68: LoadField: r4 = r3->field_1f
    //     0xb41c68: ldur            w4, [x3, #0x1f]
    // 0xb41c6c: DecompressPointer r4
    //     0xb41c6c: add             x4, x4, HEAP, lsl #32
    // 0xb41c70: stur            x4, [fp, #-0x50]
    // 0xb41c74: LoadField: r0 = r3->field_b
    //     0xb41c74: ldur            w0, [x3, #0xb]
    // 0xb41c78: DecompressPointer r0
    //     0xb41c78: add             x0, x0, HEAP, lsl #32
    // 0xb41c7c: cmp             w0, NULL
    // 0xb41c80: b.eq            #0xb43954
    // 0xb41c84: LoadField: r1 = r0->field_1b
    //     0xb41c84: ldur            w1, [x0, #0x1b]
    // 0xb41c88: DecompressPointer r1
    //     0xb41c88: add             x1, x1, HEAP, lsl #32
    // 0xb41c8c: LoadField: r5 = r1->field_1b
    //     0xb41c8c: ldur            w5, [x1, #0x1b]
    // 0xb41c90: DecompressPointer r5
    //     0xb41c90: add             x5, x5, HEAP, lsl #32
    // 0xb41c94: cmp             w5, NULL
    // 0xb41c98: b.ne            #0xb41ca4
    // 0xb41c9c: r0 = Null
    //     0xb41c9c: mov             x0, NULL
    // 0xb41ca0: b               #0xb41cbc
    // 0xb41ca4: LoadField: r0 = r5->field_b
    //     0xb41ca4: ldur            w0, [x5, #0xb]
    // 0xb41ca8: cbz             w0, #0xb41cb4
    // 0xb41cac: r1 = false
    //     0xb41cac: add             x1, NULL, #0x30  ; false
    // 0xb41cb0: b               #0xb41cb8
    // 0xb41cb4: r1 = true
    //     0xb41cb4: add             x1, NULL, #0x20  ; true
    // 0xb41cb8: mov             x0, x1
    // 0xb41cbc: cmp             w0, NULL
    // 0xb41cc0: b.eq            #0xb41cc8
    // 0xb41cc4: tbnz            w0, #4, #0xb41cd0
    // 0xb41cc8: r0 = true
    //     0xb41cc8: add             x0, NULL, #0x20  ; true
    // 0xb41ccc: b               #0xb41d50
    // 0xb41cd0: cmp             w5, NULL
    // 0xb41cd4: b.ne            #0xb41ce0
    // 0xb41cd8: r0 = Null
    //     0xb41cd8: mov             x0, NULL
    // 0xb41cdc: b               #0xb41d44
    // 0xb41ce0: LoadField: r0 = r5->field_b
    //     0xb41ce0: ldur            w0, [x5, #0xb]
    // 0xb41ce4: r1 = LoadInt32Instr(r0)
    //     0xb41ce4: sbfx            x1, x0, #1, #0x1f
    // 0xb41ce8: mov             x0, x1
    // 0xb41cec: r1 = 0
    //     0xb41cec: movz            x1, #0
    // 0xb41cf0: cmp             x1, x0
    // 0xb41cf4: b.hs            #0xb43958
    // 0xb41cf8: LoadField: r0 = r5->field_f
    //     0xb41cf8: ldur            w0, [x5, #0xf]
    // 0xb41cfc: DecompressPointer r0
    //     0xb41cfc: add             x0, x0, HEAP, lsl #32
    // 0xb41d00: LoadField: r1 = r0->field_f
    //     0xb41d00: ldur            w1, [x0, #0xf]
    // 0xb41d04: DecompressPointer r1
    //     0xb41d04: add             x1, x1, HEAP, lsl #32
    // 0xb41d08: cmp             w1, NULL
    // 0xb41d0c: b.ne            #0xb41d18
    // 0xb41d10: r0 = Null
    //     0xb41d10: mov             x0, NULL
    // 0xb41d14: b               #0xb41d44
    // 0xb41d18: LoadField: r0 = r1->field_1b
    //     0xb41d18: ldur            w0, [x1, #0x1b]
    // 0xb41d1c: DecompressPointer r0
    //     0xb41d1c: add             x0, x0, HEAP, lsl #32
    // 0xb41d20: cmp             w0, NULL
    // 0xb41d24: b.ne            #0xb41d30
    // 0xb41d28: r0 = Null
    //     0xb41d28: mov             x0, NULL
    // 0xb41d2c: b               #0xb41d44
    // 0xb41d30: LoadField: r1 = r0->field_7
    //     0xb41d30: ldur            w1, [x0, #7]
    // 0xb41d34: cbz             w1, #0xb41d40
    // 0xb41d38: r0 = false
    //     0xb41d38: add             x0, NULL, #0x30  ; false
    // 0xb41d3c: b               #0xb41d44
    // 0xb41d40: r0 = true
    //     0xb41d40: add             x0, NULL, #0x20  ; true
    // 0xb41d44: cmp             w0, NULL
    // 0xb41d48: b.ne            #0xb41d50
    // 0xb41d4c: r0 = true
    //     0xb41d4c: add             x0, NULL, #0x20  ; true
    // 0xb41d50: ldur            x1, [fp, #-0x10]
    // 0xb41d54: stur            x0, [fp, #-0x48]
    // 0xb41d58: r0 = of()
    //     0xb41d58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41d5c: LoadField: r1 = r0->field_87
    //     0xb41d5c: ldur            w1, [x0, #0x87]
    // 0xb41d60: DecompressPointer r1
    //     0xb41d60: add             x1, x1, HEAP, lsl #32
    // 0xb41d64: LoadField: r0 = r1->field_2b
    //     0xb41d64: ldur            w0, [x1, #0x2b]
    // 0xb41d68: DecompressPointer r0
    //     0xb41d68: add             x0, x0, HEAP, lsl #32
    // 0xb41d6c: ldur            x1, [fp, #-0x10]
    // 0xb41d70: stur            x0, [fp, #-0x60]
    // 0xb41d74: r0 = of()
    //     0xb41d74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41d78: LoadField: r1 = r0->field_5b
    //     0xb41d78: ldur            w1, [x0, #0x5b]
    // 0xb41d7c: DecompressPointer r1
    //     0xb41d7c: add             x1, x1, HEAP, lsl #32
    // 0xb41d80: r16 = 14.000000
    //     0xb41d80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb41d84: ldr             x16, [x16, #0x1d8]
    // 0xb41d88: stp             x16, x1, [SP]
    // 0xb41d8c: ldur            x1, [fp, #-0x60]
    // 0xb41d90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb41d90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb41d94: ldr             x4, [x4, #0x9b8]
    // 0xb41d98: r0 = copyWith()
    //     0xb41d98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41d9c: stur            x0, [fp, #-0x60]
    // 0xb41da0: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xb41da0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb41da4: ldr             x0, [x0, #0x1530]
    //     0xb41da8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb41dac: cmp             w0, w16
    //     0xb41db0: b.ne            #0xb41dc0
    //     0xb41db4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xb41db8: ldr             x2, [x2, #0x120]
    //     0xb41dbc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb41dc0: stur            x0, [fp, #-0x68]
    // 0xb41dc4: r16 = "[0-9]"
    //     0xb41dc4: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xb41dc8: ldr             x16, [x16, #0x128]
    // 0xb41dcc: stp             x16, NULL, [SP, #0x20]
    // 0xb41dd0: r16 = false
    //     0xb41dd0: add             x16, NULL, #0x30  ; false
    // 0xb41dd4: r30 = true
    //     0xb41dd4: add             lr, NULL, #0x20  ; true
    // 0xb41dd8: stp             lr, x16, [SP, #0x10]
    // 0xb41ddc: r16 = false
    //     0xb41ddc: add             x16, NULL, #0x30  ; false
    // 0xb41de0: r30 = false
    //     0xb41de0: add             lr, NULL, #0x30  ; false
    // 0xb41de4: stp             lr, x16, [SP]
    // 0xb41de8: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb41de8: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb41dec: r0 = _RegExp()
    //     0xb41dec: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb41df0: stur            x0, [fp, #-0x70]
    // 0xb41df4: r0 = FilteringTextInputFormatter()
    //     0xb41df4: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb41df8: mov             x1, x0
    // 0xb41dfc: ldur            x0, [fp, #-0x70]
    // 0xb41e00: stur            x1, [fp, #-0x78]
    // 0xb41e04: StoreField: r1->field_b = r0
    //     0xb41e04: stur            w0, [x1, #0xb]
    // 0xb41e08: r0 = true
    //     0xb41e08: add             x0, NULL, #0x20  ; true
    // 0xb41e0c: StoreField: r1->field_7 = r0
    //     0xb41e0c: stur            w0, [x1, #7]
    // 0xb41e10: r2 = ""
    //     0xb41e10: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb41e14: StoreField: r1->field_f = r2
    //     0xb41e14: stur            w2, [x1, #0xf]
    // 0xb41e18: r0 = LengthLimitingTextInputFormatter()
    //     0xb41e18: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb41e1c: mov             x3, x0
    // 0xb41e20: r0 = 12
    //     0xb41e20: movz            x0, #0xc
    // 0xb41e24: stur            x3, [fp, #-0x70]
    // 0xb41e28: StoreField: r3->field_7 = r0
    //     0xb41e28: stur            w0, [x3, #7]
    // 0xb41e2c: r1 = Null
    //     0xb41e2c: mov             x1, NULL
    // 0xb41e30: r2 = 6
    //     0xb41e30: movz            x2, #0x6
    // 0xb41e34: r0 = AllocateArray()
    //     0xb41e34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb41e38: mov             x2, x0
    // 0xb41e3c: ldur            x0, [fp, #-0x68]
    // 0xb41e40: stur            x2, [fp, #-0x80]
    // 0xb41e44: StoreField: r2->field_f = r0
    //     0xb41e44: stur            w0, [x2, #0xf]
    // 0xb41e48: ldur            x1, [fp, #-0x78]
    // 0xb41e4c: StoreField: r2->field_13 = r1
    //     0xb41e4c: stur            w1, [x2, #0x13]
    // 0xb41e50: ldur            x1, [fp, #-0x70]
    // 0xb41e54: ArrayStore: r2[0] = r1  ; List_4
    //     0xb41e54: stur            w1, [x2, #0x17]
    // 0xb41e58: r1 = <TextInputFormatter>
    //     0xb41e58: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb41e5c: ldr             x1, [x1, #0x7b0]
    // 0xb41e60: r0 = AllocateGrowableArray()
    //     0xb41e60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb41e64: mov             x2, x0
    // 0xb41e68: ldur            x0, [fp, #-0x80]
    // 0xb41e6c: stur            x2, [fp, #-0x78]
    // 0xb41e70: StoreField: r2->field_f = r0
    //     0xb41e70: stur            w0, [x2, #0xf]
    // 0xb41e74: r0 = 6
    //     0xb41e74: movz            x0, #0x6
    // 0xb41e78: StoreField: r2->field_b = r0
    //     0xb41e78: stur            w0, [x2, #0xb]
    // 0xb41e7c: ldur            x3, [fp, #-8]
    // 0xb41e80: LoadField: r4 = r3->field_3f
    //     0xb41e80: ldur            w4, [x3, #0x3f]
    // 0xb41e84: DecompressPointer r4
    //     0xb41e84: add             x4, x4, HEAP, lsl #32
    // 0xb41e88: ldur            x1, [fp, #-0x10]
    // 0xb41e8c: stur            x4, [fp, #-0x70]
    // 0xb41e90: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb41e90: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb41e94: ldur            x1, [fp, #-0x10]
    // 0xb41e98: stur            x0, [fp, #-0x80]
    // 0xb41e9c: r0 = of()
    //     0xb41e9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41ea0: LoadField: r1 = r0->field_5b
    //     0xb41ea0: ldur            w1, [x0, #0x5b]
    // 0xb41ea4: DecompressPointer r1
    //     0xb41ea4: add             x1, x1, HEAP, lsl #32
    // 0xb41ea8: stur            x1, [fp, #-0x88]
    // 0xb41eac: r0 = BorderSide()
    //     0xb41eac: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb41eb0: mov             x1, x0
    // 0xb41eb4: ldur            x0, [fp, #-0x88]
    // 0xb41eb8: stur            x1, [fp, #-0x90]
    // 0xb41ebc: StoreField: r1->field_7 = r0
    //     0xb41ebc: stur            w0, [x1, #7]
    // 0xb41ec0: d0 = 1.000000
    //     0xb41ec0: fmov            d0, #1.00000000
    // 0xb41ec4: StoreField: r1->field_b = d0
    //     0xb41ec4: stur            d0, [x1, #0xb]
    // 0xb41ec8: r0 = Instance_BorderStyle
    //     0xb41ec8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb41ecc: ldr             x0, [x0, #0xf68]
    // 0xb41ed0: StoreField: r1->field_13 = r0
    //     0xb41ed0: stur            w0, [x1, #0x13]
    // 0xb41ed4: d0 = -1.000000
    //     0xb41ed4: fmov            d0, #-1.00000000
    // 0xb41ed8: ArrayStore: r1[0] = d0  ; List_8
    //     0xb41ed8: stur            d0, [x1, #0x17]
    // 0xb41edc: r0 = Radius()
    //     0xb41edc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb41ee0: d0 = 30.000000
    //     0xb41ee0: fmov            d0, #30.00000000
    // 0xb41ee4: stur            x0, [fp, #-0x88]
    // 0xb41ee8: StoreField: r0->field_7 = d0
    //     0xb41ee8: stur            d0, [x0, #7]
    // 0xb41eec: StoreField: r0->field_f = d0
    //     0xb41eec: stur            d0, [x0, #0xf]
    // 0xb41ef0: r0 = BorderRadius()
    //     0xb41ef0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb41ef4: mov             x1, x0
    // 0xb41ef8: ldur            x0, [fp, #-0x88]
    // 0xb41efc: stur            x1, [fp, #-0x98]
    // 0xb41f00: StoreField: r1->field_7 = r0
    //     0xb41f00: stur            w0, [x1, #7]
    // 0xb41f04: StoreField: r1->field_b = r0
    //     0xb41f04: stur            w0, [x1, #0xb]
    // 0xb41f08: StoreField: r1->field_f = r0
    //     0xb41f08: stur            w0, [x1, #0xf]
    // 0xb41f0c: StoreField: r1->field_13 = r0
    //     0xb41f0c: stur            w0, [x1, #0x13]
    // 0xb41f10: r0 = OutlineInputBorder()
    //     0xb41f10: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xb41f14: mov             x2, x0
    // 0xb41f18: ldur            x0, [fp, #-0x98]
    // 0xb41f1c: stur            x2, [fp, #-0x88]
    // 0xb41f20: StoreField: r2->field_13 = r0
    //     0xb41f20: stur            w0, [x2, #0x13]
    // 0xb41f24: d0 = 4.000000
    //     0xb41f24: fmov            d0, #4.00000000
    // 0xb41f28: StoreField: r2->field_b = d0
    //     0xb41f28: stur            d0, [x2, #0xb]
    // 0xb41f2c: ldur            x0, [fp, #-0x90]
    // 0xb41f30: StoreField: r2->field_7 = r0
    //     0xb41f30: stur            w0, [x2, #7]
    // 0xb41f34: ldur            x1, [fp, #-0x10]
    // 0xb41f38: r0 = of()
    //     0xb41f38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41f3c: LoadField: r1 = r0->field_87
    //     0xb41f3c: ldur            w1, [x0, #0x87]
    // 0xb41f40: DecompressPointer r1
    //     0xb41f40: add             x1, x1, HEAP, lsl #32
    // 0xb41f44: LoadField: r0 = r1->field_2b
    //     0xb41f44: ldur            w0, [x1, #0x2b]
    // 0xb41f48: DecompressPointer r0
    //     0xb41f48: add             x0, x0, HEAP, lsl #32
    // 0xb41f4c: r16 = 12.000000
    //     0xb41f4c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb41f50: ldr             x16, [x16, #0x9e8]
    // 0xb41f54: r30 = Instance_MaterialColor
    //     0xb41f54: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb41f58: ldr             lr, [lr, #0x180]
    // 0xb41f5c: stp             lr, x16, [SP]
    // 0xb41f60: mov             x1, x0
    // 0xb41f64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb41f64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41f68: ldr             x4, [x4, #0xaa0]
    // 0xb41f6c: r0 = copyWith()
    //     0xb41f6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41f70: ldur            x1, [fp, #-0x10]
    // 0xb41f74: stur            x0, [fp, #-0x90]
    // 0xb41f78: r0 = of()
    //     0xb41f78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb41f7c: LoadField: r1 = r0->field_87
    //     0xb41f7c: ldur            w1, [x0, #0x87]
    // 0xb41f80: DecompressPointer r1
    //     0xb41f80: add             x1, x1, HEAP, lsl #32
    // 0xb41f84: LoadField: r0 = r1->field_2b
    //     0xb41f84: ldur            w0, [x1, #0x2b]
    // 0xb41f88: DecompressPointer r0
    //     0xb41f88: add             x0, x0, HEAP, lsl #32
    // 0xb41f8c: stur            x0, [fp, #-0x98]
    // 0xb41f90: r1 = Instance_Color
    //     0xb41f90: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb41f94: d0 = 0.400000
    //     0xb41f94: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb41f98: r0 = withOpacity()
    //     0xb41f98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb41f9c: r16 = 14.000000
    //     0xb41f9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb41fa0: ldr             x16, [x16, #0x1d8]
    // 0xb41fa4: stp             x0, x16, [SP]
    // 0xb41fa8: ldur            x1, [fp, #-0x98]
    // 0xb41fac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb41fac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb41fb0: ldr             x4, [x4, #0xaa0]
    // 0xb41fb4: r0 = copyWith()
    //     0xb41fb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb41fb8: ldur            x2, [fp, #-8]
    // 0xb41fbc: stur            x0, [fp, #-0xa8]
    // 0xb41fc0: LoadField: r1 = r2->field_5f
    //     0xb41fc0: ldur            w1, [x2, #0x5f]
    // 0xb41fc4: DecompressPointer r1
    //     0xb41fc4: add             x1, x1, HEAP, lsl #32
    // 0xb41fc8: tbnz            w1, #4, #0xb4211c
    // 0xb41fcc: LoadField: r1 = r2->field_3f
    //     0xb41fcc: ldur            w1, [x2, #0x3f]
    // 0xb41fd0: DecompressPointer r1
    //     0xb41fd0: add             x1, x1, HEAP, lsl #32
    // 0xb41fd4: LoadField: r3 = r1->field_27
    //     0xb41fd4: ldur            w3, [x1, #0x27]
    // 0xb41fd8: DecompressPointer r3
    //     0xb41fd8: add             x3, x3, HEAP, lsl #32
    // 0xb41fdc: LoadField: r1 = r3->field_7
    //     0xb41fdc: ldur            w1, [x3, #7]
    // 0xb41fe0: DecompressPointer r1
    //     0xb41fe0: add             x1, x1, HEAP, lsl #32
    // 0xb41fe4: LoadField: r3 = r1->field_7
    //     0xb41fe4: ldur            w3, [x1, #7]
    // 0xb41fe8: cmp             w3, #0xc
    // 0xb41fec: b.ne            #0xb42068
    // 0xb41ff0: LoadField: r1 = r2->field_b
    //     0xb41ff0: ldur            w1, [x2, #0xb]
    // 0xb41ff4: DecompressPointer r1
    //     0xb41ff4: add             x1, x1, HEAP, lsl #32
    // 0xb41ff8: cmp             w1, NULL
    // 0xb41ffc: b.eq            #0xb4395c
    // 0xb42000: LoadField: r4 = r1->field_13
    //     0xb42000: ldur            w4, [x1, #0x13]
    // 0xb42004: DecompressPointer r4
    //     0xb42004: add             x4, x4, HEAP, lsl #32
    // 0xb42008: LoadField: r1 = r4->field_b
    //     0xb42008: ldur            w1, [x4, #0xb]
    // 0xb4200c: DecompressPointer r1
    //     0xb4200c: add             x1, x1, HEAP, lsl #32
    // 0xb42010: cmp             w1, NULL
    // 0xb42014: b.ne            #0xb42020
    // 0xb42018: r1 = Null
    //     0xb42018: mov             x1, NULL
    // 0xb4201c: b               #0xb42050
    // 0xb42020: LoadField: r4 = r1->field_13
    //     0xb42020: ldur            w4, [x1, #0x13]
    // 0xb42024: DecompressPointer r4
    //     0xb42024: add             x4, x4, HEAP, lsl #32
    // 0xb42028: cmp             w4, NULL
    // 0xb4202c: b.ne            #0xb42038
    // 0xb42030: r1 = Null
    //     0xb42030: mov             x1, NULL
    // 0xb42034: b               #0xb42050
    // 0xb42038: LoadField: r1 = r4->field_7
    //     0xb42038: ldur            w1, [x4, #7]
    // 0xb4203c: cbnz            w1, #0xb42048
    // 0xb42040: r4 = false
    //     0xb42040: add             x4, NULL, #0x30  ; false
    // 0xb42044: b               #0xb4204c
    // 0xb42048: r4 = true
    //     0xb42048: add             x4, NULL, #0x20  ; true
    // 0xb4204c: mov             x1, x4
    // 0xb42050: cmp             w1, NULL
    // 0xb42054: b.eq            #0xb42068
    // 0xb42058: tbnz            w1, #4, #0xb42068
    // 0xb4205c: r1 = Instance_IconData
    //     0xb4205c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb42060: ldr             x1, [x1, #0x130]
    // 0xb42064: b               #0xb42070
    // 0xb42068: r1 = Instance_IconData
    //     0xb42068: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb4206c: ldr             x1, [x1, #0x138]
    // 0xb42070: stur            x1, [fp, #-0xa0]
    // 0xb42074: cmp             w3, #0xc
    // 0xb42078: b.ne            #0xb420f4
    // 0xb4207c: LoadField: r3 = r2->field_b
    //     0xb4207c: ldur            w3, [x2, #0xb]
    // 0xb42080: DecompressPointer r3
    //     0xb42080: add             x3, x3, HEAP, lsl #32
    // 0xb42084: cmp             w3, NULL
    // 0xb42088: b.eq            #0xb43960
    // 0xb4208c: LoadField: r4 = r3->field_13
    //     0xb4208c: ldur            w4, [x3, #0x13]
    // 0xb42090: DecompressPointer r4
    //     0xb42090: add             x4, x4, HEAP, lsl #32
    // 0xb42094: LoadField: r3 = r4->field_b
    //     0xb42094: ldur            w3, [x4, #0xb]
    // 0xb42098: DecompressPointer r3
    //     0xb42098: add             x3, x3, HEAP, lsl #32
    // 0xb4209c: cmp             w3, NULL
    // 0xb420a0: b.ne            #0xb420ac
    // 0xb420a4: r3 = Null
    //     0xb420a4: mov             x3, NULL
    // 0xb420a8: b               #0xb420dc
    // 0xb420ac: LoadField: r4 = r3->field_13
    //     0xb420ac: ldur            w4, [x3, #0x13]
    // 0xb420b0: DecompressPointer r4
    //     0xb420b0: add             x4, x4, HEAP, lsl #32
    // 0xb420b4: cmp             w4, NULL
    // 0xb420b8: b.ne            #0xb420c4
    // 0xb420bc: r3 = Null
    //     0xb420bc: mov             x3, NULL
    // 0xb420c0: b               #0xb420dc
    // 0xb420c4: LoadField: r3 = r4->field_7
    //     0xb420c4: ldur            w3, [x4, #7]
    // 0xb420c8: cbnz            w3, #0xb420d4
    // 0xb420cc: r4 = false
    //     0xb420cc: add             x4, NULL, #0x30  ; false
    // 0xb420d0: b               #0xb420d8
    // 0xb420d4: r4 = true
    //     0xb420d4: add             x4, NULL, #0x20  ; true
    // 0xb420d8: mov             x3, x4
    // 0xb420dc: cmp             w3, NULL
    // 0xb420e0: b.eq            #0xb420f4
    // 0xb420e4: tbnz            w3, #4, #0xb420f4
    // 0xb420e8: r3 = Instance_Color
    //     0xb420e8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb420ec: ldr             x3, [x3, #0x858]
    // 0xb420f0: b               #0xb420fc
    // 0xb420f4: r3 = Instance_Color
    //     0xb420f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb420f8: ldr             x3, [x3, #0x50]
    // 0xb420fc: stur            x3, [fp, #-0x98]
    // 0xb42100: r0 = Icon()
    //     0xb42100: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb42104: mov             x1, x0
    // 0xb42108: ldur            x0, [fp, #-0xa0]
    // 0xb4210c: StoreField: r1->field_b = r0
    //     0xb4210c: stur            w0, [x1, #0xb]
    // 0xb42110: ldur            x0, [fp, #-0x98]
    // 0xb42114: StoreField: r1->field_23 = r0
    //     0xb42114: stur            w0, [x1, #0x23]
    // 0xb42118: b               #0xb42120
    // 0xb4211c: r1 = Null
    //     0xb4211c: mov             x1, NULL
    // 0xb42120: ldur            x2, [fp, #-8]
    // 0xb42124: ldur            x0, [fp, #-0x50]
    // 0xb42128: ldur            x16, [fp, #-0x88]
    // 0xb4212c: r30 = Instance_EdgeInsets
    //     0xb4212c: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb42130: ldr             lr, [lr, #0xa78]
    // 0xb42134: stp             lr, x16, [SP, #0x28]
    // 0xb42138: ldur            x16, [fp, #-0x90]
    // 0xb4213c: r30 = "Pincode*"
    //     0xb4213c: add             lr, PP, #0x54, lsl #12  ; [pp+0x540d8] "Pincode*"
    //     0xb42140: ldr             lr, [lr, #0xd8]
    // 0xb42144: stp             lr, x16, [SP, #0x18]
    // 0xb42148: ldur            x16, [fp, #-0xa8]
    // 0xb4214c: r30 = 4
    //     0xb4214c: movz            lr, #0x4
    // 0xb42150: stp             lr, x16, [SP, #8]
    // 0xb42154: str             x1, [SP]
    // 0xb42158: ldur            x1, [fp, #-0x80]
    // 0xb4215c: r4 = const [0, 0x8, 0x7, 0x1, contentPadding, 0x2, errorMaxLines, 0x6, errorStyle, 0x3, focusedBorder, 0x1, hintStyle, 0x5, hintText, 0x4, suffixIcon, 0x7, null]
    //     0xb4215c: add             x4, PP, #0x56, lsl #12  ; [pp+0x56848] List(19) [0, 0x8, 0x7, 0x1, "contentPadding", 0x2, "errorMaxLines", 0x6, "errorStyle", 0x3, "focusedBorder", 0x1, "hintStyle", 0x5, "hintText", 0x4, "suffixIcon", 0x7, Null]
    //     0xb42160: ldr             x4, [x4, #0x848]
    // 0xb42164: r0 = copyWith()
    //     0xb42164: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb42168: ldur            x2, [fp, #-8]
    // 0xb4216c: r1 = Function '_validatePinCode@1549293123':.
    //     0xb4216c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56850] AnonymousClosure: (0xb43bec), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validatePinCode (0xa056a0)
    //     0xb42170: ldr             x1, [x1, #0x850]
    // 0xb42174: stur            x0, [fp, #-0x80]
    // 0xb42178: r0 = AllocateClosure()
    //     0xb42178: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4217c: ldur            x2, [fp, #-0x18]
    // 0xb42180: r1 = Function '<anonymous closure>':.
    //     0xb42180: add             x1, PP, #0x56, lsl #12  ; [pp+0x56858] AnonymousClosure: (0xb43aec), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xb40ae8)
    //     0xb42184: ldr             x1, [x1, #0x858]
    // 0xb42188: stur            x0, [fp, #-0x88]
    // 0xb4218c: r0 = AllocateClosure()
    //     0xb4218c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb42190: r1 = <String>
    //     0xb42190: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb42194: stur            x0, [fp, #-0x90]
    // 0xb42198: r0 = TextFormField()
    //     0xb42198: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb4219c: stur            x0, [fp, #-0x98]
    // 0xb421a0: ldur            x16, [fp, #-0x88]
    // 0xb421a4: r30 = Instance_AutovalidateMode
    //     0xb421a4: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb421a8: ldr             lr, [lr, #0x7e8]
    // 0xb421ac: stp             lr, x16, [SP, #0x40]
    // 0xb421b0: r16 = true
    //     0xb421b0: add             x16, NULL, #0x20  ; true
    // 0xb421b4: ldur            lr, [fp, #-0x48]
    // 0xb421b8: stp             lr, x16, [SP, #0x30]
    // 0xb421bc: ldur            x16, [fp, #-0x60]
    // 0xb421c0: ldur            lr, [fp, #-0x78]
    // 0xb421c4: stp             lr, x16, [SP, #0x20]
    // 0xb421c8: r16 = Instance_TextInputType
    //     0xb421c8: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb421cc: ldr             x16, [x16, #0x1a0]
    // 0xb421d0: ldur            lr, [fp, #-0x70]
    // 0xb421d4: stp             lr, x16, [SP, #0x10]
    // 0xb421d8: r16 = 2
    //     0xb421d8: movz            x16, #0x2
    // 0xb421dc: ldur            lr, [fp, #-0x90]
    // 0xb421e0: stp             lr, x16, [SP]
    // 0xb421e4: mov             x1, x0
    // 0xb421e8: ldur            x2, [fp, #-0x80]
    // 0xb421ec: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x5, autovalidateMode, 0x3, controller, 0x9, enableSuggestions, 0x4, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0xa, onChanged, 0xb, style, 0x6, validator, 0x2, null]
    //     0xb421ec: add             x4, PP, #0x56, lsl #12  ; [pp+0x56820] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x5, "autovalidateMode", 0x3, "controller", 0x9, "enableSuggestions", 0x4, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0xa, "onChanged", 0xb, "style", 0x6, "validator", 0x2, Null]
    //     0xb421f0: ldr             x4, [x4, #0x820]
    // 0xb421f4: r0 = TextFormField()
    //     0xb421f4: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb421f8: r0 = Form()
    //     0xb421f8: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb421fc: mov             x2, x0
    // 0xb42200: ldur            x0, [fp, #-0x98]
    // 0xb42204: stur            x2, [fp, #-0x48]
    // 0xb42208: StoreField: r2->field_b = r0
    //     0xb42208: stur            w0, [x2, #0xb]
    // 0xb4220c: r0 = Instance_AutovalidateMode
    //     0xb4220c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb42210: ldr             x0, [x0, #0x800]
    // 0xb42214: StoreField: r2->field_23 = r0
    //     0xb42214: stur            w0, [x2, #0x23]
    // 0xb42218: ldur            x1, [fp, #-0x50]
    // 0xb4221c: StoreField: r2->field_7 = r1
    //     0xb4221c: stur            w1, [x2, #7]
    // 0xb42220: ldur            x1, [fp, #-0x10]
    // 0xb42224: r0 = of()
    //     0xb42224: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42228: LoadField: r1 = r0->field_87
    //     0xb42228: ldur            w1, [x0, #0x87]
    // 0xb4222c: DecompressPointer r1
    //     0xb4222c: add             x1, x1, HEAP, lsl #32
    // 0xb42230: LoadField: r0 = r1->field_2b
    //     0xb42230: ldur            w0, [x1, #0x2b]
    // 0xb42234: DecompressPointer r0
    //     0xb42234: add             x0, x0, HEAP, lsl #32
    // 0xb42238: r16 = 12.000000
    //     0xb42238: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4223c: ldr             x16, [x16, #0x9e8]
    // 0xb42240: r30 = Instance_Color
    //     0xb42240: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb42244: stp             lr, x16, [SP]
    // 0xb42248: mov             x1, x0
    // 0xb4224c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4224c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb42250: ldr             x4, [x4, #0xaa0]
    // 0xb42254: r0 = copyWith()
    //     0xb42254: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42258: stur            x0, [fp, #-0x50]
    // 0xb4225c: r0 = Text()
    //     0xb4225c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb42260: mov             x2, x0
    // 0xb42264: r0 = "City*"
    //     0xb42264: add             x0, PP, #0x54, lsl #12  ; [pp+0x540f0] "City*"
    //     0xb42268: ldr             x0, [x0, #0xf0]
    // 0xb4226c: stur            x2, [fp, #-0x70]
    // 0xb42270: StoreField: r2->field_b = r0
    //     0xb42270: stur            w0, [x2, #0xb]
    // 0xb42274: ldur            x0, [fp, #-0x50]
    // 0xb42278: StoreField: r2->field_13 = r0
    //     0xb42278: stur            w0, [x2, #0x13]
    // 0xb4227c: ldur            x0, [fp, #-8]
    // 0xb42280: LoadField: r3 = r0->field_23
    //     0xb42280: ldur            w3, [x0, #0x23]
    // 0xb42284: DecompressPointer r3
    //     0xb42284: add             x3, x3, HEAP, lsl #32
    // 0xb42288: stur            x3, [fp, #-0x60]
    // 0xb4228c: LoadField: r1 = r0->field_3f
    //     0xb4228c: ldur            w1, [x0, #0x3f]
    // 0xb42290: DecompressPointer r1
    //     0xb42290: add             x1, x1, HEAP, lsl #32
    // 0xb42294: LoadField: r4 = r1->field_27
    //     0xb42294: ldur            w4, [x1, #0x27]
    // 0xb42298: DecompressPointer r4
    //     0xb42298: add             x4, x4, HEAP, lsl #32
    // 0xb4229c: LoadField: r1 = r4->field_7
    //     0xb4229c: ldur            w1, [x4, #7]
    // 0xb422a0: DecompressPointer r1
    //     0xb422a0: add             x1, x1, HEAP, lsl #32
    // 0xb422a4: LoadField: r4 = r1->field_7
    //     0xb422a4: ldur            w4, [x1, #7]
    // 0xb422a8: cmp             w4, #0xc
    // 0xb422ac: b.ne            #0xb422f4
    // 0xb422b0: LoadField: r1 = r0->field_b
    //     0xb422b0: ldur            w1, [x0, #0xb]
    // 0xb422b4: DecompressPointer r1
    //     0xb422b4: add             x1, x1, HEAP, lsl #32
    // 0xb422b8: cmp             w1, NULL
    // 0xb422bc: b.eq            #0xb43964
    // 0xb422c0: LoadField: r4 = r1->field_13
    //     0xb422c0: ldur            w4, [x1, #0x13]
    // 0xb422c4: DecompressPointer r4
    //     0xb422c4: add             x4, x4, HEAP, lsl #32
    // 0xb422c8: LoadField: r1 = r4->field_b
    //     0xb422c8: ldur            w1, [x4, #0xb]
    // 0xb422cc: DecompressPointer r1
    //     0xb422cc: add             x1, x1, HEAP, lsl #32
    // 0xb422d0: cmp             w1, NULL
    // 0xb422d4: b.ne            #0xb422e0
    // 0xb422d8: r1 = Null
    //     0xb422d8: mov             x1, NULL
    // 0xb422dc: b               #0xb422ec
    // 0xb422e0: LoadField: r4 = r1->field_13
    //     0xb422e0: ldur            w4, [x1, #0x13]
    // 0xb422e4: DecompressPointer r4
    //     0xb422e4: add             x4, x4, HEAP, lsl #32
    // 0xb422e8: mov             x1, x4
    // 0xb422ec: mov             x4, x1
    // 0xb422f0: b               #0xb422f8
    // 0xb422f4: r4 = ""
    //     0xb422f4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb422f8: stur            x4, [fp, #-0x50]
    // 0xb422fc: r1 = <TextEditingValue>
    //     0xb422fc: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xb42300: r0 = TextEditingController()
    //     0xb42300: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xb42304: stur            x0, [fp, #-0x78]
    // 0xb42308: ldur            x16, [fp, #-0x50]
    // 0xb4230c: str             x16, [SP]
    // 0xb42310: mov             x1, x0
    // 0xb42314: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xb42314: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xb42318: ldr             x4, [x4, #0xc40]
    // 0xb4231c: r0 = TextEditingController()
    //     0xb4231c: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xb42320: ldur            x1, [fp, #-0x10]
    // 0xb42324: r0 = of()
    //     0xb42324: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42328: LoadField: r1 = r0->field_87
    //     0xb42328: ldur            w1, [x0, #0x87]
    // 0xb4232c: DecompressPointer r1
    //     0xb4232c: add             x1, x1, HEAP, lsl #32
    // 0xb42330: LoadField: r0 = r1->field_2b
    //     0xb42330: ldur            w0, [x1, #0x2b]
    // 0xb42334: DecompressPointer r0
    //     0xb42334: add             x0, x0, HEAP, lsl #32
    // 0xb42338: ldur            x1, [fp, #-0x10]
    // 0xb4233c: stur            x0, [fp, #-0x50]
    // 0xb42340: r0 = of()
    //     0xb42340: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42344: LoadField: r1 = r0->field_5b
    //     0xb42344: ldur            w1, [x0, #0x5b]
    // 0xb42348: DecompressPointer r1
    //     0xb42348: add             x1, x1, HEAP, lsl #32
    // 0xb4234c: r16 = 14.000000
    //     0xb4234c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb42350: ldr             x16, [x16, #0x1d8]
    // 0xb42354: stp             x1, x16, [SP]
    // 0xb42358: ldur            x1, [fp, #-0x50]
    // 0xb4235c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4235c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb42360: ldr             x4, [x4, #0xaa0]
    // 0xb42364: r0 = copyWith()
    //     0xb42364: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42368: ldur            x1, [fp, #-0x10]
    // 0xb4236c: stur            x0, [fp, #-0x50]
    // 0xb42370: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb42370: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb42374: ldur            x1, [fp, #-0x10]
    // 0xb42378: stur            x0, [fp, #-0x80]
    // 0xb4237c: r0 = of()
    //     0xb4237c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42380: LoadField: r1 = r0->field_5b
    //     0xb42380: ldur            w1, [x0, #0x5b]
    // 0xb42384: DecompressPointer r1
    //     0xb42384: add             x1, x1, HEAP, lsl #32
    // 0xb42388: r0 = LoadClassIdInstr(r1)
    //     0xb42388: ldur            x0, [x1, #-1]
    //     0xb4238c: ubfx            x0, x0, #0xc, #0x14
    // 0xb42390: d0 = 0.100000
    //     0xb42390: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb42394: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb42394: sub             lr, x0, #0xffa
    //     0xb42398: ldr             lr, [x21, lr, lsl #3]
    //     0xb4239c: blr             lr
    // 0xb423a0: ldur            x1, [fp, #-0x10]
    // 0xb423a4: stur            x0, [fp, #-0x88]
    // 0xb423a8: r0 = of()
    //     0xb423a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb423ac: LoadField: r1 = r0->field_87
    //     0xb423ac: ldur            w1, [x0, #0x87]
    // 0xb423b0: DecompressPointer r1
    //     0xb423b0: add             x1, x1, HEAP, lsl #32
    // 0xb423b4: LoadField: r0 = r1->field_2b
    //     0xb423b4: ldur            w0, [x1, #0x2b]
    // 0xb423b8: DecompressPointer r0
    //     0xb423b8: add             x0, x0, HEAP, lsl #32
    // 0xb423bc: stur            x0, [fp, #-0x90]
    // 0xb423c0: r1 = Instance_Color
    //     0xb423c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb423c4: d0 = 0.400000
    //     0xb423c4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb423c8: r0 = withOpacity()
    //     0xb423c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb423cc: r16 = 14.000000
    //     0xb423cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb423d0: ldr             x16, [x16, #0x1d8]
    // 0xb423d4: stp             x0, x16, [SP]
    // 0xb423d8: ldur            x1, [fp, #-0x90]
    // 0xb423dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb423dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb423e0: ldr             x4, [x4, #0xaa0]
    // 0xb423e4: r0 = copyWith()
    //     0xb423e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb423e8: ldur            x2, [fp, #-8]
    // 0xb423ec: stur            x0, [fp, #-0xa0]
    // 0xb423f0: LoadField: r1 = r2->field_5f
    //     0xb423f0: ldur            w1, [x2, #0x5f]
    // 0xb423f4: DecompressPointer r1
    //     0xb423f4: add             x1, x1, HEAP, lsl #32
    // 0xb423f8: tbnz            w1, #4, #0xb4254c
    // 0xb423fc: LoadField: r1 = r2->field_3f
    //     0xb423fc: ldur            w1, [x2, #0x3f]
    // 0xb42400: DecompressPointer r1
    //     0xb42400: add             x1, x1, HEAP, lsl #32
    // 0xb42404: LoadField: r3 = r1->field_27
    //     0xb42404: ldur            w3, [x1, #0x27]
    // 0xb42408: DecompressPointer r3
    //     0xb42408: add             x3, x3, HEAP, lsl #32
    // 0xb4240c: LoadField: r1 = r3->field_7
    //     0xb4240c: ldur            w1, [x3, #7]
    // 0xb42410: DecompressPointer r1
    //     0xb42410: add             x1, x1, HEAP, lsl #32
    // 0xb42414: LoadField: r3 = r1->field_7
    //     0xb42414: ldur            w3, [x1, #7]
    // 0xb42418: cmp             w3, #0xc
    // 0xb4241c: b.ne            #0xb42498
    // 0xb42420: LoadField: r1 = r2->field_b
    //     0xb42420: ldur            w1, [x2, #0xb]
    // 0xb42424: DecompressPointer r1
    //     0xb42424: add             x1, x1, HEAP, lsl #32
    // 0xb42428: cmp             w1, NULL
    // 0xb4242c: b.eq            #0xb43968
    // 0xb42430: LoadField: r4 = r1->field_13
    //     0xb42430: ldur            w4, [x1, #0x13]
    // 0xb42434: DecompressPointer r4
    //     0xb42434: add             x4, x4, HEAP, lsl #32
    // 0xb42438: LoadField: r1 = r4->field_b
    //     0xb42438: ldur            w1, [x4, #0xb]
    // 0xb4243c: DecompressPointer r1
    //     0xb4243c: add             x1, x1, HEAP, lsl #32
    // 0xb42440: cmp             w1, NULL
    // 0xb42444: b.ne            #0xb42450
    // 0xb42448: r1 = Null
    //     0xb42448: mov             x1, NULL
    // 0xb4244c: b               #0xb42480
    // 0xb42450: LoadField: r4 = r1->field_13
    //     0xb42450: ldur            w4, [x1, #0x13]
    // 0xb42454: DecompressPointer r4
    //     0xb42454: add             x4, x4, HEAP, lsl #32
    // 0xb42458: cmp             w4, NULL
    // 0xb4245c: b.ne            #0xb42468
    // 0xb42460: r1 = Null
    //     0xb42460: mov             x1, NULL
    // 0xb42464: b               #0xb42480
    // 0xb42468: LoadField: r1 = r4->field_7
    //     0xb42468: ldur            w1, [x4, #7]
    // 0xb4246c: cbnz            w1, #0xb42478
    // 0xb42470: r4 = false
    //     0xb42470: add             x4, NULL, #0x30  ; false
    // 0xb42474: b               #0xb4247c
    // 0xb42478: r4 = true
    //     0xb42478: add             x4, NULL, #0x20  ; true
    // 0xb4247c: mov             x1, x4
    // 0xb42480: cmp             w1, NULL
    // 0xb42484: b.eq            #0xb42498
    // 0xb42488: tbnz            w1, #4, #0xb42498
    // 0xb4248c: r1 = Instance_IconData
    //     0xb4248c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb42490: ldr             x1, [x1, #0x130]
    // 0xb42494: b               #0xb424a0
    // 0xb42498: r1 = Instance_IconData
    //     0xb42498: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb4249c: ldr             x1, [x1, #0x138]
    // 0xb424a0: stur            x1, [fp, #-0x98]
    // 0xb424a4: cmp             w3, #0xc
    // 0xb424a8: b.ne            #0xb42524
    // 0xb424ac: LoadField: r3 = r2->field_b
    //     0xb424ac: ldur            w3, [x2, #0xb]
    // 0xb424b0: DecompressPointer r3
    //     0xb424b0: add             x3, x3, HEAP, lsl #32
    // 0xb424b4: cmp             w3, NULL
    // 0xb424b8: b.eq            #0xb4396c
    // 0xb424bc: LoadField: r4 = r3->field_13
    //     0xb424bc: ldur            w4, [x3, #0x13]
    // 0xb424c0: DecompressPointer r4
    //     0xb424c0: add             x4, x4, HEAP, lsl #32
    // 0xb424c4: LoadField: r3 = r4->field_b
    //     0xb424c4: ldur            w3, [x4, #0xb]
    // 0xb424c8: DecompressPointer r3
    //     0xb424c8: add             x3, x3, HEAP, lsl #32
    // 0xb424cc: cmp             w3, NULL
    // 0xb424d0: b.ne            #0xb424dc
    // 0xb424d4: r3 = Null
    //     0xb424d4: mov             x3, NULL
    // 0xb424d8: b               #0xb4250c
    // 0xb424dc: LoadField: r4 = r3->field_13
    //     0xb424dc: ldur            w4, [x3, #0x13]
    // 0xb424e0: DecompressPointer r4
    //     0xb424e0: add             x4, x4, HEAP, lsl #32
    // 0xb424e4: cmp             w4, NULL
    // 0xb424e8: b.ne            #0xb424f4
    // 0xb424ec: r3 = Null
    //     0xb424ec: mov             x3, NULL
    // 0xb424f0: b               #0xb4250c
    // 0xb424f4: LoadField: r3 = r4->field_7
    //     0xb424f4: ldur            w3, [x4, #7]
    // 0xb424f8: cbnz            w3, #0xb42504
    // 0xb424fc: r4 = false
    //     0xb424fc: add             x4, NULL, #0x30  ; false
    // 0xb42500: b               #0xb42508
    // 0xb42504: r4 = true
    //     0xb42504: add             x4, NULL, #0x20  ; true
    // 0xb42508: mov             x3, x4
    // 0xb4250c: cmp             w3, NULL
    // 0xb42510: b.eq            #0xb42524
    // 0xb42514: tbnz            w3, #4, #0xb42524
    // 0xb42518: r3 = Instance_Color
    //     0xb42518: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4251c: ldr             x3, [x3, #0x858]
    // 0xb42520: b               #0xb4252c
    // 0xb42524: r3 = Instance_Color
    //     0xb42524: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb42528: ldr             x3, [x3, #0x50]
    // 0xb4252c: stur            x3, [fp, #-0x90]
    // 0xb42530: r0 = Icon()
    //     0xb42530: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb42534: mov             x1, x0
    // 0xb42538: ldur            x0, [fp, #-0x98]
    // 0xb4253c: StoreField: r1->field_b = r0
    //     0xb4253c: stur            w0, [x1, #0xb]
    // 0xb42540: ldur            x0, [fp, #-0x90]
    // 0xb42544: StoreField: r1->field_23 = r0
    //     0xb42544: stur            w0, [x1, #0x23]
    // 0xb42548: b               #0xb42550
    // 0xb4254c: r1 = Null
    //     0xb4254c: mov             x1, NULL
    // 0xb42550: ldur            x2, [fp, #-8]
    // 0xb42554: ldur            x0, [fp, #-0x70]
    // 0xb42558: ldur            x3, [fp, #-0x60]
    // 0xb4255c: ldur            x16, [fp, #-0x88]
    // 0xb42560: r30 = "City*"
    //     0xb42560: add             lr, PP, #0x54, lsl #12  ; [pp+0x540f0] "City*"
    //     0xb42564: ldr             lr, [lr, #0xf0]
    // 0xb42568: stp             lr, x16, [SP, #0x10]
    // 0xb4256c: ldur            x16, [fp, #-0xa0]
    // 0xb42570: stp             x1, x16, [SP]
    // 0xb42574: ldur            x1, [fp, #-0x80]
    // 0xb42578: r4 = const [0, 0x5, 0x4, 0x1, fillColor, 0x1, hintStyle, 0x3, hintText, 0x2, suffixIcon, 0x4, null]
    //     0xb42578: add             x4, PP, #0x56, lsl #12  ; [pp+0x56860] List(13) [0, 0x5, 0x4, 0x1, "fillColor", 0x1, "hintStyle", 0x3, "hintText", 0x2, "suffixIcon", 0x4, Null]
    //     0xb4257c: ldr             x4, [x4, #0x860]
    // 0xb42580: r0 = copyWith()
    //     0xb42580: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb42584: r1 = <String>
    //     0xb42584: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb42588: stur            x0, [fp, #-0x80]
    // 0xb4258c: r0 = TextFormField()
    //     0xb4258c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb42590: stur            x0, [fp, #-0x88]
    // 0xb42594: r16 = true
    //     0xb42594: add             x16, NULL, #0x20  ; true
    // 0xb42598: r30 = true
    //     0xb42598: add             lr, NULL, #0x20  ; true
    // 0xb4259c: stp             lr, x16, [SP, #0x10]
    // 0xb425a0: ldur            x16, [fp, #-0x78]
    // 0xb425a4: ldur            lr, [fp, #-0x50]
    // 0xb425a8: stp             lr, x16, [SP]
    // 0xb425ac: mov             x1, x0
    // 0xb425b0: ldur            x2, [fp, #-0x80]
    // 0xb425b4: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xb425b4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xb425b8: ldr             x4, [x4, #0x100]
    // 0xb425bc: r0 = TextFormField()
    //     0xb425bc: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb425c0: r0 = Form()
    //     0xb425c0: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb425c4: mov             x3, x0
    // 0xb425c8: ldur            x0, [fp, #-0x88]
    // 0xb425cc: stur            x3, [fp, #-0x50]
    // 0xb425d0: StoreField: r3->field_b = r0
    //     0xb425d0: stur            w0, [x3, #0xb]
    // 0xb425d4: r0 = Instance_AutovalidateMode
    //     0xb425d4: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb425d8: ldr             x0, [x0, #0x800]
    // 0xb425dc: StoreField: r3->field_23 = r0
    //     0xb425dc: stur            w0, [x3, #0x23]
    // 0xb425e0: ldur            x1, [fp, #-0x60]
    // 0xb425e4: StoreField: r3->field_7 = r1
    //     0xb425e4: stur            w1, [x3, #7]
    // 0xb425e8: r1 = Null
    //     0xb425e8: mov             x1, NULL
    // 0xb425ec: r2 = 4
    //     0xb425ec: movz            x2, #0x4
    // 0xb425f0: r0 = AllocateArray()
    //     0xb425f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb425f4: mov             x2, x0
    // 0xb425f8: ldur            x0, [fp, #-0x70]
    // 0xb425fc: stur            x2, [fp, #-0x60]
    // 0xb42600: StoreField: r2->field_f = r0
    //     0xb42600: stur            w0, [x2, #0xf]
    // 0xb42604: ldur            x0, [fp, #-0x50]
    // 0xb42608: StoreField: r2->field_13 = r0
    //     0xb42608: stur            w0, [x2, #0x13]
    // 0xb4260c: r1 = <Widget>
    //     0xb4260c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb42610: r0 = AllocateGrowableArray()
    //     0xb42610: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb42614: mov             x1, x0
    // 0xb42618: ldur            x0, [fp, #-0x60]
    // 0xb4261c: stur            x1, [fp, #-0x50]
    // 0xb42620: StoreField: r1->field_f = r0
    //     0xb42620: stur            w0, [x1, #0xf]
    // 0xb42624: r2 = 4
    //     0xb42624: movz            x2, #0x4
    // 0xb42628: StoreField: r1->field_b = r2
    //     0xb42628: stur            w2, [x1, #0xb]
    // 0xb4262c: r0 = Column()
    //     0xb4262c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb42630: mov             x2, x0
    // 0xb42634: r0 = Instance_Axis
    //     0xb42634: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb42638: stur            x2, [fp, #-0x60]
    // 0xb4263c: StoreField: r2->field_f = r0
    //     0xb4263c: stur            w0, [x2, #0xf]
    // 0xb42640: r3 = Instance_MainAxisAlignment
    //     0xb42640: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb42644: ldr             x3, [x3, #0xa08]
    // 0xb42648: StoreField: r2->field_13 = r3
    //     0xb42648: stur            w3, [x2, #0x13]
    // 0xb4264c: r4 = Instance_MainAxisSize
    //     0xb4264c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb42650: ldr             x4, [x4, #0xa10]
    // 0xb42654: ArrayStore: r2[0] = r4  ; List_4
    //     0xb42654: stur            w4, [x2, #0x17]
    // 0xb42658: r5 = Instance_CrossAxisAlignment
    //     0xb42658: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4265c: ldr             x5, [x5, #0x890]
    // 0xb42660: StoreField: r2->field_1b = r5
    //     0xb42660: stur            w5, [x2, #0x1b]
    // 0xb42664: r6 = Instance_VerticalDirection
    //     0xb42664: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb42668: ldr             x6, [x6, #0xa20]
    // 0xb4266c: StoreField: r2->field_23 = r6
    //     0xb4266c: stur            w6, [x2, #0x23]
    // 0xb42670: r7 = Instance_Clip
    //     0xb42670: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb42674: ldr             x7, [x7, #0x38]
    // 0xb42678: StoreField: r2->field_2b = r7
    //     0xb42678: stur            w7, [x2, #0x2b]
    // 0xb4267c: StoreField: r2->field_2f = rZR
    //     0xb4267c: stur            xzr, [x2, #0x2f]
    // 0xb42680: ldur            x1, [fp, #-0x50]
    // 0xb42684: StoreField: r2->field_b = r1
    //     0xb42684: stur            w1, [x2, #0xb]
    // 0xb42688: r1 = <FlexParentData>
    //     0xb42688: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb4268c: ldr             x1, [x1, #0xe00]
    // 0xb42690: r0 = Flexible()
    //     0xb42690: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb42694: mov             x2, x0
    // 0xb42698: r0 = 1
    //     0xb42698: movz            x0, #0x1
    // 0xb4269c: stur            x2, [fp, #-0x50]
    // 0xb426a0: StoreField: r2->field_13 = r0
    //     0xb426a0: stur            x0, [x2, #0x13]
    // 0xb426a4: r3 = Instance_FlexFit
    //     0xb426a4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb426a8: ldr             x3, [x3, #0xe08]
    // 0xb426ac: StoreField: r2->field_1b = r3
    //     0xb426ac: stur            w3, [x2, #0x1b]
    // 0xb426b0: ldur            x1, [fp, #-0x60]
    // 0xb426b4: StoreField: r2->field_b = r1
    //     0xb426b4: stur            w1, [x2, #0xb]
    // 0xb426b8: ldur            x1, [fp, #-0x10]
    // 0xb426bc: r0 = of()
    //     0xb426bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb426c0: LoadField: r1 = r0->field_87
    //     0xb426c0: ldur            w1, [x0, #0x87]
    // 0xb426c4: DecompressPointer r1
    //     0xb426c4: add             x1, x1, HEAP, lsl #32
    // 0xb426c8: LoadField: r0 = r1->field_2b
    //     0xb426c8: ldur            w0, [x1, #0x2b]
    // 0xb426cc: DecompressPointer r0
    //     0xb426cc: add             x0, x0, HEAP, lsl #32
    // 0xb426d0: r16 = 12.000000
    //     0xb426d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb426d4: ldr             x16, [x16, #0x9e8]
    // 0xb426d8: r30 = Instance_Color
    //     0xb426d8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb426dc: stp             lr, x16, [SP]
    // 0xb426e0: mov             x1, x0
    // 0xb426e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb426e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb426e8: ldr             x4, [x4, #0xaa0]
    // 0xb426ec: r0 = copyWith()
    //     0xb426ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb426f0: stur            x0, [fp, #-0x60]
    // 0xb426f4: r0 = Text()
    //     0xb426f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb426f8: mov             x2, x0
    // 0xb426fc: r0 = "State*"
    //     0xb426fc: add             x0, PP, #0x54, lsl #12  ; [pp+0x54108] "State*"
    //     0xb42700: ldr             x0, [x0, #0x108]
    // 0xb42704: stur            x2, [fp, #-0x78]
    // 0xb42708: StoreField: r2->field_b = r0
    //     0xb42708: stur            w0, [x2, #0xb]
    // 0xb4270c: ldur            x0, [fp, #-0x60]
    // 0xb42710: StoreField: r2->field_13 = r0
    //     0xb42710: stur            w0, [x2, #0x13]
    // 0xb42714: ldur            x0, [fp, #-8]
    // 0xb42718: LoadField: r3 = r0->field_27
    //     0xb42718: ldur            w3, [x0, #0x27]
    // 0xb4271c: DecompressPointer r3
    //     0xb4271c: add             x3, x3, HEAP, lsl #32
    // 0xb42720: stur            x3, [fp, #-0x70]
    // 0xb42724: LoadField: r1 = r0->field_3f
    //     0xb42724: ldur            w1, [x0, #0x3f]
    // 0xb42728: DecompressPointer r1
    //     0xb42728: add             x1, x1, HEAP, lsl #32
    // 0xb4272c: LoadField: r4 = r1->field_27
    //     0xb4272c: ldur            w4, [x1, #0x27]
    // 0xb42730: DecompressPointer r4
    //     0xb42730: add             x4, x4, HEAP, lsl #32
    // 0xb42734: LoadField: r1 = r4->field_7
    //     0xb42734: ldur            w1, [x4, #7]
    // 0xb42738: DecompressPointer r1
    //     0xb42738: add             x1, x1, HEAP, lsl #32
    // 0xb4273c: LoadField: r4 = r1->field_7
    //     0xb4273c: ldur            w4, [x1, #7]
    // 0xb42740: cmp             w4, #0xc
    // 0xb42744: b.ne            #0xb4278c
    // 0xb42748: LoadField: r1 = r0->field_b
    //     0xb42748: ldur            w1, [x0, #0xb]
    // 0xb4274c: DecompressPointer r1
    //     0xb4274c: add             x1, x1, HEAP, lsl #32
    // 0xb42750: cmp             w1, NULL
    // 0xb42754: b.eq            #0xb43970
    // 0xb42758: LoadField: r4 = r1->field_13
    //     0xb42758: ldur            w4, [x1, #0x13]
    // 0xb4275c: DecompressPointer r4
    //     0xb4275c: add             x4, x4, HEAP, lsl #32
    // 0xb42760: LoadField: r1 = r4->field_b
    //     0xb42760: ldur            w1, [x4, #0xb]
    // 0xb42764: DecompressPointer r1
    //     0xb42764: add             x1, x1, HEAP, lsl #32
    // 0xb42768: cmp             w1, NULL
    // 0xb4276c: b.ne            #0xb42778
    // 0xb42770: r1 = Null
    //     0xb42770: mov             x1, NULL
    // 0xb42774: b               #0xb42784
    // 0xb42778: LoadField: r4 = r1->field_f
    //     0xb42778: ldur            w4, [x1, #0xf]
    // 0xb4277c: DecompressPointer r4
    //     0xb4277c: add             x4, x4, HEAP, lsl #32
    // 0xb42780: mov             x1, x4
    // 0xb42784: mov             x4, x1
    // 0xb42788: b               #0xb42790
    // 0xb4278c: r4 = ""
    //     0xb4278c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb42790: stur            x4, [fp, #-0x60]
    // 0xb42794: r1 = <TextEditingValue>
    //     0xb42794: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0xb42798: r0 = TextEditingController()
    //     0xb42798: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0xb4279c: stur            x0, [fp, #-0x80]
    // 0xb427a0: ldur            x16, [fp, #-0x60]
    // 0xb427a4: str             x16, [SP]
    // 0xb427a8: mov             x1, x0
    // 0xb427ac: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0xb427ac: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0xb427b0: ldr             x4, [x4, #0xc40]
    // 0xb427b4: r0 = TextEditingController()
    //     0xb427b4: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0xb427b8: ldur            x1, [fp, #-0x10]
    // 0xb427bc: r0 = of()
    //     0xb427bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb427c0: LoadField: r1 = r0->field_87
    //     0xb427c0: ldur            w1, [x0, #0x87]
    // 0xb427c4: DecompressPointer r1
    //     0xb427c4: add             x1, x1, HEAP, lsl #32
    // 0xb427c8: LoadField: r0 = r1->field_2b
    //     0xb427c8: ldur            w0, [x1, #0x2b]
    // 0xb427cc: DecompressPointer r0
    //     0xb427cc: add             x0, x0, HEAP, lsl #32
    // 0xb427d0: ldur            x1, [fp, #-0x10]
    // 0xb427d4: stur            x0, [fp, #-0x60]
    // 0xb427d8: r0 = of()
    //     0xb427d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb427dc: LoadField: r1 = r0->field_5b
    //     0xb427dc: ldur            w1, [x0, #0x5b]
    // 0xb427e0: DecompressPointer r1
    //     0xb427e0: add             x1, x1, HEAP, lsl #32
    // 0xb427e4: r16 = 14.000000
    //     0xb427e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb427e8: ldr             x16, [x16, #0x1d8]
    // 0xb427ec: stp             x1, x16, [SP]
    // 0xb427f0: ldur            x1, [fp, #-0x60]
    // 0xb427f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb427f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb427f8: ldr             x4, [x4, #0xaa0]
    // 0xb427fc: r0 = copyWith()
    //     0xb427fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42800: ldur            x1, [fp, #-0x10]
    // 0xb42804: stur            x0, [fp, #-0x60]
    // 0xb42808: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb42808: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb4280c: ldur            x1, [fp, #-0x10]
    // 0xb42810: stur            x0, [fp, #-0x88]
    // 0xb42814: r0 = of()
    //     0xb42814: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42818: LoadField: r1 = r0->field_5b
    //     0xb42818: ldur            w1, [x0, #0x5b]
    // 0xb4281c: DecompressPointer r1
    //     0xb4281c: add             x1, x1, HEAP, lsl #32
    // 0xb42820: r0 = LoadClassIdInstr(r1)
    //     0xb42820: ldur            x0, [x1, #-1]
    //     0xb42824: ubfx            x0, x0, #0xc, #0x14
    // 0xb42828: d0 = 0.100000
    //     0xb42828: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4282c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4282c: sub             lr, x0, #0xffa
    //     0xb42830: ldr             lr, [x21, lr, lsl #3]
    //     0xb42834: blr             lr
    // 0xb42838: ldur            x1, [fp, #-0x10]
    // 0xb4283c: stur            x0, [fp, #-0x90]
    // 0xb42840: r0 = of()
    //     0xb42840: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42844: LoadField: r1 = r0->field_87
    //     0xb42844: ldur            w1, [x0, #0x87]
    // 0xb42848: DecompressPointer r1
    //     0xb42848: add             x1, x1, HEAP, lsl #32
    // 0xb4284c: LoadField: r0 = r1->field_2b
    //     0xb4284c: ldur            w0, [x1, #0x2b]
    // 0xb42850: DecompressPointer r0
    //     0xb42850: add             x0, x0, HEAP, lsl #32
    // 0xb42854: stur            x0, [fp, #-0x98]
    // 0xb42858: r1 = Instance_Color
    //     0xb42858: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4285c: d0 = 0.400000
    //     0xb4285c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb42860: r0 = withOpacity()
    //     0xb42860: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb42864: r16 = 14.000000
    //     0xb42864: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb42868: ldr             x16, [x16, #0x1d8]
    // 0xb4286c: stp             x0, x16, [SP]
    // 0xb42870: ldur            x1, [fp, #-0x98]
    // 0xb42874: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb42874: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb42878: ldr             x4, [x4, #0xaa0]
    // 0xb4287c: r0 = copyWith()
    //     0xb4287c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42880: ldur            x2, [fp, #-8]
    // 0xb42884: stur            x0, [fp, #-0xa8]
    // 0xb42888: LoadField: r1 = r2->field_5f
    //     0xb42888: ldur            w1, [x2, #0x5f]
    // 0xb4288c: DecompressPointer r1
    //     0xb4288c: add             x1, x1, HEAP, lsl #32
    // 0xb42890: tbnz            w1, #4, #0xb429e4
    // 0xb42894: LoadField: r1 = r2->field_3f
    //     0xb42894: ldur            w1, [x2, #0x3f]
    // 0xb42898: DecompressPointer r1
    //     0xb42898: add             x1, x1, HEAP, lsl #32
    // 0xb4289c: LoadField: r3 = r1->field_27
    //     0xb4289c: ldur            w3, [x1, #0x27]
    // 0xb428a0: DecompressPointer r3
    //     0xb428a0: add             x3, x3, HEAP, lsl #32
    // 0xb428a4: LoadField: r1 = r3->field_7
    //     0xb428a4: ldur            w1, [x3, #7]
    // 0xb428a8: DecompressPointer r1
    //     0xb428a8: add             x1, x1, HEAP, lsl #32
    // 0xb428ac: LoadField: r3 = r1->field_7
    //     0xb428ac: ldur            w3, [x1, #7]
    // 0xb428b0: cmp             w3, #0xc
    // 0xb428b4: b.ne            #0xb42930
    // 0xb428b8: LoadField: r1 = r2->field_b
    //     0xb428b8: ldur            w1, [x2, #0xb]
    // 0xb428bc: DecompressPointer r1
    //     0xb428bc: add             x1, x1, HEAP, lsl #32
    // 0xb428c0: cmp             w1, NULL
    // 0xb428c4: b.eq            #0xb43974
    // 0xb428c8: LoadField: r4 = r1->field_13
    //     0xb428c8: ldur            w4, [x1, #0x13]
    // 0xb428cc: DecompressPointer r4
    //     0xb428cc: add             x4, x4, HEAP, lsl #32
    // 0xb428d0: LoadField: r1 = r4->field_b
    //     0xb428d0: ldur            w1, [x4, #0xb]
    // 0xb428d4: DecompressPointer r1
    //     0xb428d4: add             x1, x1, HEAP, lsl #32
    // 0xb428d8: cmp             w1, NULL
    // 0xb428dc: b.ne            #0xb428e8
    // 0xb428e0: r1 = Null
    //     0xb428e0: mov             x1, NULL
    // 0xb428e4: b               #0xb42918
    // 0xb428e8: LoadField: r4 = r1->field_13
    //     0xb428e8: ldur            w4, [x1, #0x13]
    // 0xb428ec: DecompressPointer r4
    //     0xb428ec: add             x4, x4, HEAP, lsl #32
    // 0xb428f0: cmp             w4, NULL
    // 0xb428f4: b.ne            #0xb42900
    // 0xb428f8: r1 = Null
    //     0xb428f8: mov             x1, NULL
    // 0xb428fc: b               #0xb42918
    // 0xb42900: LoadField: r1 = r4->field_7
    //     0xb42900: ldur            w1, [x4, #7]
    // 0xb42904: cbnz            w1, #0xb42910
    // 0xb42908: r4 = false
    //     0xb42908: add             x4, NULL, #0x30  ; false
    // 0xb4290c: b               #0xb42914
    // 0xb42910: r4 = true
    //     0xb42910: add             x4, NULL, #0x20  ; true
    // 0xb42914: mov             x1, x4
    // 0xb42918: cmp             w1, NULL
    // 0xb4291c: b.eq            #0xb42930
    // 0xb42920: tbnz            w1, #4, #0xb42930
    // 0xb42924: r1 = Instance_IconData
    //     0xb42924: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb42928: ldr             x1, [x1, #0x130]
    // 0xb4292c: b               #0xb42938
    // 0xb42930: r1 = Instance_IconData
    //     0xb42930: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb42934: ldr             x1, [x1, #0x138]
    // 0xb42938: stur            x1, [fp, #-0xa0]
    // 0xb4293c: cmp             w3, #0xc
    // 0xb42940: b.ne            #0xb429bc
    // 0xb42944: LoadField: r3 = r2->field_b
    //     0xb42944: ldur            w3, [x2, #0xb]
    // 0xb42948: DecompressPointer r3
    //     0xb42948: add             x3, x3, HEAP, lsl #32
    // 0xb4294c: cmp             w3, NULL
    // 0xb42950: b.eq            #0xb43978
    // 0xb42954: LoadField: r4 = r3->field_13
    //     0xb42954: ldur            w4, [x3, #0x13]
    // 0xb42958: DecompressPointer r4
    //     0xb42958: add             x4, x4, HEAP, lsl #32
    // 0xb4295c: LoadField: r3 = r4->field_b
    //     0xb4295c: ldur            w3, [x4, #0xb]
    // 0xb42960: DecompressPointer r3
    //     0xb42960: add             x3, x3, HEAP, lsl #32
    // 0xb42964: cmp             w3, NULL
    // 0xb42968: b.ne            #0xb42974
    // 0xb4296c: r3 = Null
    //     0xb4296c: mov             x3, NULL
    // 0xb42970: b               #0xb429a4
    // 0xb42974: LoadField: r4 = r3->field_f
    //     0xb42974: ldur            w4, [x3, #0xf]
    // 0xb42978: DecompressPointer r4
    //     0xb42978: add             x4, x4, HEAP, lsl #32
    // 0xb4297c: cmp             w4, NULL
    // 0xb42980: b.ne            #0xb4298c
    // 0xb42984: r3 = Null
    //     0xb42984: mov             x3, NULL
    // 0xb42988: b               #0xb429a4
    // 0xb4298c: LoadField: r3 = r4->field_7
    //     0xb4298c: ldur            w3, [x4, #7]
    // 0xb42990: cbnz            w3, #0xb4299c
    // 0xb42994: r4 = false
    //     0xb42994: add             x4, NULL, #0x30  ; false
    // 0xb42998: b               #0xb429a0
    // 0xb4299c: r4 = true
    //     0xb4299c: add             x4, NULL, #0x20  ; true
    // 0xb429a0: mov             x3, x4
    // 0xb429a4: cmp             w3, NULL
    // 0xb429a8: b.eq            #0xb429bc
    // 0xb429ac: tbnz            w3, #4, #0xb429bc
    // 0xb429b0: r3 = Instance_Color
    //     0xb429b0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb429b4: ldr             x3, [x3, #0x858]
    // 0xb429b8: b               #0xb429c4
    // 0xb429bc: r3 = Instance_Color
    //     0xb429bc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb429c0: ldr             x3, [x3, #0x50]
    // 0xb429c4: stur            x3, [fp, #-0x98]
    // 0xb429c8: r0 = Icon()
    //     0xb429c8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb429cc: mov             x1, x0
    // 0xb429d0: ldur            x0, [fp, #-0xa0]
    // 0xb429d4: StoreField: r1->field_b = r0
    //     0xb429d4: stur            w0, [x1, #0xb]
    // 0xb429d8: ldur            x0, [fp, #-0x98]
    // 0xb429dc: StoreField: r1->field_23 = r0
    //     0xb429dc: stur            w0, [x1, #0x23]
    // 0xb429e0: b               #0xb429e8
    // 0xb429e4: r1 = Null
    //     0xb429e4: mov             x1, NULL
    // 0xb429e8: ldur            x2, [fp, #-8]
    // 0xb429ec: ldur            x11, [fp, #-0x30]
    // 0xb429f0: ldur            x10, [fp, #-0x20]
    // 0xb429f4: ldur            x9, [fp, #-0x40]
    // 0xb429f8: ldur            x8, [fp, #-0x28]
    // 0xb429fc: ldur            x7, [fp, #-0x38]
    // 0xb42a00: ldur            x6, [fp, #-0x58]
    // 0xb42a04: ldur            x5, [fp, #-0x48]
    // 0xb42a08: ldur            x4, [fp, #-0x50]
    // 0xb42a0c: ldur            x0, [fp, #-0x78]
    // 0xb42a10: ldur            x3, [fp, #-0x70]
    // 0xb42a14: ldur            x16, [fp, #-0x90]
    // 0xb42a18: r30 = "State*"
    //     0xb42a18: add             lr, PP, #0x54, lsl #12  ; [pp+0x54108] "State*"
    //     0xb42a1c: ldr             lr, [lr, #0x108]
    // 0xb42a20: stp             lr, x16, [SP, #0x10]
    // 0xb42a24: ldur            x16, [fp, #-0xa8]
    // 0xb42a28: stp             x1, x16, [SP]
    // 0xb42a2c: ldur            x1, [fp, #-0x88]
    // 0xb42a30: r4 = const [0, 0x5, 0x4, 0x1, fillColor, 0x1, hintStyle, 0x3, hintText, 0x2, suffixIcon, 0x4, null]
    //     0xb42a30: add             x4, PP, #0x56, lsl #12  ; [pp+0x56860] List(13) [0, 0x5, 0x4, 0x1, "fillColor", 0x1, "hintStyle", 0x3, "hintText", 0x2, "suffixIcon", 0x4, Null]
    //     0xb42a34: ldr             x4, [x4, #0x860]
    // 0xb42a38: r0 = copyWith()
    //     0xb42a38: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb42a3c: r1 = <String>
    //     0xb42a3c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb42a40: stur            x0, [fp, #-0x88]
    // 0xb42a44: r0 = TextFormField()
    //     0xb42a44: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb42a48: stur            x0, [fp, #-0x90]
    // 0xb42a4c: r16 = true
    //     0xb42a4c: add             x16, NULL, #0x20  ; true
    // 0xb42a50: r30 = true
    //     0xb42a50: add             lr, NULL, #0x20  ; true
    // 0xb42a54: stp             lr, x16, [SP, #0x10]
    // 0xb42a58: ldur            x16, [fp, #-0x80]
    // 0xb42a5c: ldur            lr, [fp, #-0x60]
    // 0xb42a60: stp             lr, x16, [SP]
    // 0xb42a64: mov             x1, x0
    // 0xb42a68: ldur            x2, [fp, #-0x88]
    // 0xb42a6c: r4 = const [0, 0x6, 0x4, 0x2, controller, 0x4, ignorePointers, 0x3, readOnly, 0x2, style, 0x5, null]
    //     0xb42a6c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54100] List(13) [0, 0x6, 0x4, 0x2, "controller", 0x4, "ignorePointers", 0x3, "readOnly", 0x2, "style", 0x5, Null]
    //     0xb42a70: ldr             x4, [x4, #0x100]
    // 0xb42a74: r0 = TextFormField()
    //     0xb42a74: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb42a78: r0 = Form()
    //     0xb42a78: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb42a7c: mov             x3, x0
    // 0xb42a80: ldur            x0, [fp, #-0x90]
    // 0xb42a84: stur            x3, [fp, #-0x60]
    // 0xb42a88: StoreField: r3->field_b = r0
    //     0xb42a88: stur            w0, [x3, #0xb]
    // 0xb42a8c: r0 = Instance_AutovalidateMode
    //     0xb42a8c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb42a90: ldr             x0, [x0, #0x800]
    // 0xb42a94: StoreField: r3->field_23 = r0
    //     0xb42a94: stur            w0, [x3, #0x23]
    // 0xb42a98: ldur            x1, [fp, #-0x70]
    // 0xb42a9c: StoreField: r3->field_7 = r1
    //     0xb42a9c: stur            w1, [x3, #7]
    // 0xb42aa0: r1 = Null
    //     0xb42aa0: mov             x1, NULL
    // 0xb42aa4: r2 = 4
    //     0xb42aa4: movz            x2, #0x4
    // 0xb42aa8: r0 = AllocateArray()
    //     0xb42aa8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb42aac: mov             x2, x0
    // 0xb42ab0: ldur            x0, [fp, #-0x78]
    // 0xb42ab4: stur            x2, [fp, #-0x70]
    // 0xb42ab8: StoreField: r2->field_f = r0
    //     0xb42ab8: stur            w0, [x2, #0xf]
    // 0xb42abc: ldur            x0, [fp, #-0x60]
    // 0xb42ac0: StoreField: r2->field_13 = r0
    //     0xb42ac0: stur            w0, [x2, #0x13]
    // 0xb42ac4: r1 = <Widget>
    //     0xb42ac4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb42ac8: r0 = AllocateGrowableArray()
    //     0xb42ac8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb42acc: mov             x1, x0
    // 0xb42ad0: ldur            x0, [fp, #-0x70]
    // 0xb42ad4: stur            x1, [fp, #-0x60]
    // 0xb42ad8: StoreField: r1->field_f = r0
    //     0xb42ad8: stur            w0, [x1, #0xf]
    // 0xb42adc: r2 = 4
    //     0xb42adc: movz            x2, #0x4
    // 0xb42ae0: StoreField: r1->field_b = r2
    //     0xb42ae0: stur            w2, [x1, #0xb]
    // 0xb42ae4: r0 = Column()
    //     0xb42ae4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb42ae8: mov             x2, x0
    // 0xb42aec: r0 = Instance_Axis
    //     0xb42aec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb42af0: stur            x2, [fp, #-0x70]
    // 0xb42af4: StoreField: r2->field_f = r0
    //     0xb42af4: stur            w0, [x2, #0xf]
    // 0xb42af8: r3 = Instance_MainAxisAlignment
    //     0xb42af8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb42afc: ldr             x3, [x3, #0xa08]
    // 0xb42b00: StoreField: r2->field_13 = r3
    //     0xb42b00: stur            w3, [x2, #0x13]
    // 0xb42b04: r4 = Instance_MainAxisSize
    //     0xb42b04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb42b08: ldr             x4, [x4, #0xa10]
    // 0xb42b0c: ArrayStore: r2[0] = r4  ; List_4
    //     0xb42b0c: stur            w4, [x2, #0x17]
    // 0xb42b10: r5 = Instance_CrossAxisAlignment
    //     0xb42b10: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb42b14: ldr             x5, [x5, #0x890]
    // 0xb42b18: StoreField: r2->field_1b = r5
    //     0xb42b18: stur            w5, [x2, #0x1b]
    // 0xb42b1c: r6 = Instance_VerticalDirection
    //     0xb42b1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb42b20: ldr             x6, [x6, #0xa20]
    // 0xb42b24: StoreField: r2->field_23 = r6
    //     0xb42b24: stur            w6, [x2, #0x23]
    // 0xb42b28: r7 = Instance_Clip
    //     0xb42b28: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb42b2c: ldr             x7, [x7, #0x38]
    // 0xb42b30: StoreField: r2->field_2b = r7
    //     0xb42b30: stur            w7, [x2, #0x2b]
    // 0xb42b34: StoreField: r2->field_2f = rZR
    //     0xb42b34: stur            xzr, [x2, #0x2f]
    // 0xb42b38: ldur            x1, [fp, #-0x60]
    // 0xb42b3c: StoreField: r2->field_b = r1
    //     0xb42b3c: stur            w1, [x2, #0xb]
    // 0xb42b40: r1 = <FlexParentData>
    //     0xb42b40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb42b44: ldr             x1, [x1, #0xe00]
    // 0xb42b48: r0 = Flexible()
    //     0xb42b48: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb42b4c: mov             x3, x0
    // 0xb42b50: r0 = 1
    //     0xb42b50: movz            x0, #0x1
    // 0xb42b54: stur            x3, [fp, #-0x60]
    // 0xb42b58: StoreField: r3->field_13 = r0
    //     0xb42b58: stur            x0, [x3, #0x13]
    // 0xb42b5c: r0 = Instance_FlexFit
    //     0xb42b5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb42b60: ldr             x0, [x0, #0xe08]
    // 0xb42b64: StoreField: r3->field_1b = r0
    //     0xb42b64: stur            w0, [x3, #0x1b]
    // 0xb42b68: ldur            x0, [fp, #-0x70]
    // 0xb42b6c: StoreField: r3->field_b = r0
    //     0xb42b6c: stur            w0, [x3, #0xb]
    // 0xb42b70: r1 = Null
    //     0xb42b70: mov             x1, NULL
    // 0xb42b74: r2 = 6
    //     0xb42b74: movz            x2, #0x6
    // 0xb42b78: r0 = AllocateArray()
    //     0xb42b78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb42b7c: mov             x2, x0
    // 0xb42b80: ldur            x0, [fp, #-0x50]
    // 0xb42b84: stur            x2, [fp, #-0x70]
    // 0xb42b88: StoreField: r2->field_f = r0
    //     0xb42b88: stur            w0, [x2, #0xf]
    // 0xb42b8c: r16 = Instance_SizedBox
    //     0xb42b8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb42b90: ldr             x16, [x16, #0x998]
    // 0xb42b94: StoreField: r2->field_13 = r16
    //     0xb42b94: stur            w16, [x2, #0x13]
    // 0xb42b98: ldur            x0, [fp, #-0x60]
    // 0xb42b9c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb42b9c: stur            w0, [x2, #0x17]
    // 0xb42ba0: r1 = <Widget>
    //     0xb42ba0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb42ba4: r0 = AllocateGrowableArray()
    //     0xb42ba4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb42ba8: mov             x1, x0
    // 0xb42bac: ldur            x0, [fp, #-0x70]
    // 0xb42bb0: stur            x1, [fp, #-0x50]
    // 0xb42bb4: StoreField: r1->field_f = r0
    //     0xb42bb4: stur            w0, [x1, #0xf]
    // 0xb42bb8: r2 = 6
    //     0xb42bb8: movz            x2, #0x6
    // 0xb42bbc: StoreField: r1->field_b = r2
    //     0xb42bbc: stur            w2, [x1, #0xb]
    // 0xb42bc0: r0 = Row()
    //     0xb42bc0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb42bc4: mov             x1, x0
    // 0xb42bc8: r0 = Instance_Axis
    //     0xb42bc8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb42bcc: stur            x1, [fp, #-0x60]
    // 0xb42bd0: StoreField: r1->field_f = r0
    //     0xb42bd0: stur            w0, [x1, #0xf]
    // 0xb42bd4: r0 = Instance_MainAxisAlignment
    //     0xb42bd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xb42bd8: ldr             x0, [x0, #0xd10]
    // 0xb42bdc: StoreField: r1->field_13 = r0
    //     0xb42bdc: stur            w0, [x1, #0x13]
    // 0xb42be0: r0 = Instance_MainAxisSize
    //     0xb42be0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb42be4: ldr             x0, [x0, #0xa10]
    // 0xb42be8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb42be8: stur            w0, [x1, #0x17]
    // 0xb42bec: r2 = Instance_CrossAxisAlignment
    //     0xb42bec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb42bf0: ldr             x2, [x2, #0x890]
    // 0xb42bf4: StoreField: r1->field_1b = r2
    //     0xb42bf4: stur            w2, [x1, #0x1b]
    // 0xb42bf8: r3 = Instance_VerticalDirection
    //     0xb42bf8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb42bfc: ldr             x3, [x3, #0xa20]
    // 0xb42c00: StoreField: r1->field_23 = r3
    //     0xb42c00: stur            w3, [x1, #0x23]
    // 0xb42c04: r4 = Instance_Clip
    //     0xb42c04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb42c08: ldr             x4, [x4, #0x38]
    // 0xb42c0c: StoreField: r1->field_2b = r4
    //     0xb42c0c: stur            w4, [x1, #0x2b]
    // 0xb42c10: StoreField: r1->field_2f = rZR
    //     0xb42c10: stur            xzr, [x1, #0x2f]
    // 0xb42c14: ldur            x5, [fp, #-0x50]
    // 0xb42c18: StoreField: r1->field_b = r5
    //     0xb42c18: stur            w5, [x1, #0xb]
    // 0xb42c1c: r0 = Padding()
    //     0xb42c1c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb42c20: mov             x3, x0
    // 0xb42c24: r0 = Instance_EdgeInsets
    //     0xb42c24: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb42c28: ldr             x0, [x0, #0x858]
    // 0xb42c2c: stur            x3, [fp, #-0x50]
    // 0xb42c30: StoreField: r3->field_f = r0
    //     0xb42c30: stur            w0, [x3, #0xf]
    // 0xb42c34: ldur            x1, [fp, #-0x60]
    // 0xb42c38: StoreField: r3->field_b = r1
    //     0xb42c38: stur            w1, [x3, #0xb]
    // 0xb42c3c: r1 = Null
    //     0xb42c3c: mov             x1, NULL
    // 0xb42c40: r2 = 16
    //     0xb42c40: movz            x2, #0x10
    // 0xb42c44: r0 = AllocateArray()
    //     0xb42c44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb42c48: mov             x2, x0
    // 0xb42c4c: ldur            x0, [fp, #-0x30]
    // 0xb42c50: stur            x2, [fp, #-0x60]
    // 0xb42c54: StoreField: r2->field_f = r0
    //     0xb42c54: stur            w0, [x2, #0xf]
    // 0xb42c58: ldur            x0, [fp, #-0x20]
    // 0xb42c5c: StoreField: r2->field_13 = r0
    //     0xb42c5c: stur            w0, [x2, #0x13]
    // 0xb42c60: ldur            x0, [fp, #-0x40]
    // 0xb42c64: ArrayStore: r2[0] = r0  ; List_4
    //     0xb42c64: stur            w0, [x2, #0x17]
    // 0xb42c68: ldur            x0, [fp, #-0x28]
    // 0xb42c6c: StoreField: r2->field_1b = r0
    //     0xb42c6c: stur            w0, [x2, #0x1b]
    // 0xb42c70: ldur            x0, [fp, #-0x38]
    // 0xb42c74: StoreField: r2->field_1f = r0
    //     0xb42c74: stur            w0, [x2, #0x1f]
    // 0xb42c78: ldur            x0, [fp, #-0x58]
    // 0xb42c7c: StoreField: r2->field_23 = r0
    //     0xb42c7c: stur            w0, [x2, #0x23]
    // 0xb42c80: ldur            x0, [fp, #-0x48]
    // 0xb42c84: StoreField: r2->field_27 = r0
    //     0xb42c84: stur            w0, [x2, #0x27]
    // 0xb42c88: ldur            x0, [fp, #-0x50]
    // 0xb42c8c: StoreField: r2->field_2b = r0
    //     0xb42c8c: stur            w0, [x2, #0x2b]
    // 0xb42c90: r1 = <Widget>
    //     0xb42c90: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb42c94: r0 = AllocateGrowableArray()
    //     0xb42c94: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb42c98: mov             x1, x0
    // 0xb42c9c: ldur            x0, [fp, #-0x60]
    // 0xb42ca0: stur            x1, [fp, #-0x20]
    // 0xb42ca4: StoreField: r1->field_f = r0
    //     0xb42ca4: stur            w0, [x1, #0xf]
    // 0xb42ca8: r0 = 16
    //     0xb42ca8: movz            x0, #0x10
    // 0xb42cac: StoreField: r1->field_b = r0
    //     0xb42cac: stur            w0, [x1, #0xb]
    // 0xb42cb0: r0 = Column()
    //     0xb42cb0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb42cb4: mov             x4, x0
    // 0xb42cb8: r3 = Instance_Axis
    //     0xb42cb8: ldr             x3, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb42cbc: stur            x4, [fp, #-0x28]
    // 0xb42cc0: StoreField: r4->field_f = r3
    //     0xb42cc0: stur            w3, [x4, #0xf]
    // 0xb42cc4: r5 = Instance_MainAxisAlignment
    //     0xb42cc4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb42cc8: ldr             x5, [x5, #0xa08]
    // 0xb42ccc: StoreField: r4->field_13 = r5
    //     0xb42ccc: stur            w5, [x4, #0x13]
    // 0xb42cd0: r6 = Instance_MainAxisSize
    //     0xb42cd0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb42cd4: ldr             x6, [x6, #0xa10]
    // 0xb42cd8: ArrayStore: r4[0] = r6  ; List_4
    //     0xb42cd8: stur            w6, [x4, #0x17]
    // 0xb42cdc: r7 = Instance_CrossAxisAlignment
    //     0xb42cdc: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb42ce0: ldr             x7, [x7, #0x890]
    // 0xb42ce4: StoreField: r4->field_1b = r7
    //     0xb42ce4: stur            w7, [x4, #0x1b]
    // 0xb42ce8: r8 = Instance_VerticalDirection
    //     0xb42ce8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb42cec: ldr             x8, [x8, #0xa20]
    // 0xb42cf0: StoreField: r4->field_23 = r8
    //     0xb42cf0: stur            w8, [x4, #0x23]
    // 0xb42cf4: r9 = Instance_Clip
    //     0xb42cf4: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb42cf8: ldr             x9, [x9, #0x38]
    // 0xb42cfc: StoreField: r4->field_2b = r9
    //     0xb42cfc: stur            w9, [x4, #0x2b]
    // 0xb42d00: StoreField: r4->field_2f = rZR
    //     0xb42d00: stur            xzr, [x4, #0x2f]
    // 0xb42d04: ldur            x0, [fp, #-0x20]
    // 0xb42d08: StoreField: r4->field_b = r0
    //     0xb42d08: stur            w0, [x4, #0xb]
    // 0xb42d0c: ldur            x10, [fp, #-8]
    // 0xb42d10: LoadField: r0 = r10->field_b
    //     0xb42d10: ldur            w0, [x10, #0xb]
    // 0xb42d14: DecompressPointer r0
    //     0xb42d14: add             x0, x0, HEAP, lsl #32
    // 0xb42d18: cmp             w0, NULL
    // 0xb42d1c: b.eq            #0xb4397c
    // 0xb42d20: LoadField: r1 = r0->field_b
    //     0xb42d20: ldur            w1, [x0, #0xb]
    // 0xb42d24: DecompressPointer r1
    //     0xb42d24: add             x1, x1, HEAP, lsl #32
    // 0xb42d28: LoadField: r0 = r1->field_f
    //     0xb42d28: ldur            w0, [x1, #0xf]
    // 0xb42d2c: DecompressPointer r0
    //     0xb42d2c: add             x0, x0, HEAP, lsl #32
    // 0xb42d30: cmp             w0, NULL
    // 0xb42d34: b.ne            #0xb42d40
    // 0xb42d38: r0 = Null
    //     0xb42d38: mov             x0, NULL
    // 0xb42d3c: b               #0xb42d6c
    // 0xb42d40: r1 = LoadClassIdInstr(r0)
    //     0xb42d40: ldur            x1, [x0, #-1]
    //     0xb42d44: ubfx            x1, x1, #0xc, #0x14
    // 0xb42d48: mov             x16, x0
    // 0xb42d4c: mov             x0, x1
    // 0xb42d50: mov             x1, x16
    // 0xb42d54: r2 = "landmark"
    //     0xb42d54: add             x2, PP, #0x24, lsl #12  ; [pp+0x24930] "landmark"
    //     0xb42d58: ldr             x2, [x2, #0x930]
    // 0xb42d5c: r0 = GDT[cid_x0 + 0xe437]()
    //     0xb42d5c: movz            x17, #0xe437
    //     0xb42d60: add             lr, x0, x17
    //     0xb42d64: ldr             lr, [x21, lr, lsl #3]
    //     0xb42d68: blr             lr
    // 0xb42d6c: cmp             w0, NULL
    // 0xb42d70: b.ne            #0xb42d78
    // 0xb42d74: r0 = false
    //     0xb42d74: add             x0, NULL, #0x30  ; false
    // 0xb42d78: ldur            x2, [fp, #-8]
    // 0xb42d7c: stur            x0, [fp, #-0x30]
    // 0xb42d80: LoadField: r1 = r2->field_37
    //     0xb42d80: ldur            w1, [x2, #0x37]
    // 0xb42d84: DecompressPointer r1
    //     0xb42d84: add             x1, x1, HEAP, lsl #32
    // 0xb42d88: LoadField: r3 = r1->field_27
    //     0xb42d88: ldur            w3, [x1, #0x27]
    // 0xb42d8c: DecompressPointer r3
    //     0xb42d8c: add             x3, x3, HEAP, lsl #32
    // 0xb42d90: LoadField: r1 = r3->field_7
    //     0xb42d90: ldur            w1, [x3, #7]
    // 0xb42d94: DecompressPointer r1
    //     0xb42d94: add             x1, x1, HEAP, lsl #32
    // 0xb42d98: LoadField: r3 = r1->field_7
    //     0xb42d98: ldur            w3, [x1, #7]
    // 0xb42d9c: cbnz            w3, #0xb42dac
    // 0xb42da0: r3 = "Landmark"
    //     0xb42da0: add             x3, PP, #0x54, lsl #12  ; [pp+0x54118] "Landmark"
    //     0xb42da4: ldr             x3, [x3, #0x118]
    // 0xb42da8: b               #0xb42db4
    // 0xb42dac: r3 = "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xb42dac: add             x3, PP, #0x54, lsl #12  ; [pp+0x54120] "Nearby Famous Place / Shop / School, etc. (Optional)"
    //     0xb42db0: ldr             x3, [x3, #0x120]
    // 0xb42db4: ldur            x1, [fp, #-0x10]
    // 0xb42db8: stur            x3, [fp, #-0x20]
    // 0xb42dbc: r0 = of()
    //     0xb42dbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42dc0: LoadField: r1 = r0->field_87
    //     0xb42dc0: ldur            w1, [x0, #0x87]
    // 0xb42dc4: DecompressPointer r1
    //     0xb42dc4: add             x1, x1, HEAP, lsl #32
    // 0xb42dc8: LoadField: r0 = r1->field_2b
    //     0xb42dc8: ldur            w0, [x1, #0x2b]
    // 0xb42dcc: DecompressPointer r0
    //     0xb42dcc: add             x0, x0, HEAP, lsl #32
    // 0xb42dd0: r16 = 12.000000
    //     0xb42dd0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb42dd4: ldr             x16, [x16, #0x9e8]
    // 0xb42dd8: r30 = Instance_Color
    //     0xb42dd8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb42ddc: stp             lr, x16, [SP]
    // 0xb42de0: mov             x1, x0
    // 0xb42de4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb42de4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb42de8: ldr             x4, [x4, #0xaa0]
    // 0xb42dec: r0 = copyWith()
    //     0xb42dec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42df0: stur            x0, [fp, #-0x38]
    // 0xb42df4: r0 = Text()
    //     0xb42df4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb42df8: mov             x1, x0
    // 0xb42dfc: ldur            x0, [fp, #-0x20]
    // 0xb42e00: stur            x1, [fp, #-0x40]
    // 0xb42e04: StoreField: r1->field_b = r0
    //     0xb42e04: stur            w0, [x1, #0xb]
    // 0xb42e08: ldur            x0, [fp, #-0x38]
    // 0xb42e0c: StoreField: r1->field_13 = r0
    //     0xb42e0c: stur            w0, [x1, #0x13]
    // 0xb42e10: r0 = Padding()
    //     0xb42e10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb42e14: mov             x1, x0
    // 0xb42e18: r0 = Instance_EdgeInsets
    //     0xb42e18: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb42e1c: ldr             x0, [x0, #0x4c0]
    // 0xb42e20: stur            x1, [fp, #-0x38]
    // 0xb42e24: StoreField: r1->field_f = r0
    //     0xb42e24: stur            w0, [x1, #0xf]
    // 0xb42e28: ldur            x2, [fp, #-0x40]
    // 0xb42e2c: StoreField: r1->field_b = r2
    //     0xb42e2c: stur            w2, [x1, #0xb]
    // 0xb42e30: ldur            x2, [fp, #-8]
    // 0xb42e34: LoadField: r3 = r2->field_1b
    //     0xb42e34: ldur            w3, [x2, #0x1b]
    // 0xb42e38: DecompressPointer r3
    //     0xb42e38: add             x3, x3, HEAP, lsl #32
    // 0xb42e3c: stur            x3, [fp, #-0x20]
    // 0xb42e40: r0 = LengthLimitingTextInputFormatter()
    //     0xb42e40: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb42e44: mov             x1, x0
    // 0xb42e48: r0 = 240
    //     0xb42e48: movz            x0, #0xf0
    // 0xb42e4c: stur            x1, [fp, #-0x40]
    // 0xb42e50: StoreField: r1->field_7 = r0
    //     0xb42e50: stur            w0, [x1, #7]
    // 0xb42e54: r16 = "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb42e54: add             x16, PP, #0x54, lsl #12  ; [pp+0x540a8] "[a-zA-Z0-9\\u0020-\\u007E]+"
    //     0xb42e58: ldr             x16, [x16, #0xa8]
    // 0xb42e5c: stp             x16, NULL, [SP, #0x20]
    // 0xb42e60: r16 = false
    //     0xb42e60: add             x16, NULL, #0x30  ; false
    // 0xb42e64: r30 = true
    //     0xb42e64: add             lr, NULL, #0x20  ; true
    // 0xb42e68: stp             lr, x16, [SP, #0x10]
    // 0xb42e6c: r16 = false
    //     0xb42e6c: add             x16, NULL, #0x30  ; false
    // 0xb42e70: r30 = false
    //     0xb42e70: add             lr, NULL, #0x30  ; false
    // 0xb42e74: stp             lr, x16, [SP]
    // 0xb42e78: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb42e78: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb42e7c: r0 = _RegExp()
    //     0xb42e7c: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb42e80: stur            x0, [fp, #-0x48]
    // 0xb42e84: r0 = FilteringTextInputFormatter()
    //     0xb42e84: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb42e88: mov             x3, x0
    // 0xb42e8c: ldur            x0, [fp, #-0x48]
    // 0xb42e90: stur            x3, [fp, #-0x50]
    // 0xb42e94: StoreField: r3->field_b = r0
    //     0xb42e94: stur            w0, [x3, #0xb]
    // 0xb42e98: r0 = true
    //     0xb42e98: add             x0, NULL, #0x20  ; true
    // 0xb42e9c: StoreField: r3->field_7 = r0
    //     0xb42e9c: stur            w0, [x3, #7]
    // 0xb42ea0: r4 = ""
    //     0xb42ea0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb42ea4: StoreField: r3->field_f = r4
    //     0xb42ea4: stur            w4, [x3, #0xf]
    // 0xb42ea8: r1 = Null
    //     0xb42ea8: mov             x1, NULL
    // 0xb42eac: r2 = 4
    //     0xb42eac: movz            x2, #0x4
    // 0xb42eb0: r0 = AllocateArray()
    //     0xb42eb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb42eb4: mov             x2, x0
    // 0xb42eb8: ldur            x0, [fp, #-0x40]
    // 0xb42ebc: stur            x2, [fp, #-0x48]
    // 0xb42ec0: StoreField: r2->field_f = r0
    //     0xb42ec0: stur            w0, [x2, #0xf]
    // 0xb42ec4: ldur            x0, [fp, #-0x50]
    // 0xb42ec8: StoreField: r2->field_13 = r0
    //     0xb42ec8: stur            w0, [x2, #0x13]
    // 0xb42ecc: r1 = <TextInputFormatter>
    //     0xb42ecc: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb42ed0: ldr             x1, [x1, #0x7b0]
    // 0xb42ed4: r0 = AllocateGrowableArray()
    //     0xb42ed4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb42ed8: mov             x2, x0
    // 0xb42edc: ldur            x0, [fp, #-0x48]
    // 0xb42ee0: stur            x2, [fp, #-0x40]
    // 0xb42ee4: StoreField: r2->field_f = r0
    //     0xb42ee4: stur            w0, [x2, #0xf]
    // 0xb42ee8: r0 = 4
    //     0xb42ee8: movz            x0, #0x4
    // 0xb42eec: StoreField: r2->field_b = r0
    //     0xb42eec: stur            w0, [x2, #0xb]
    // 0xb42ef0: ldur            x1, [fp, #-0x10]
    // 0xb42ef4: r0 = of()
    //     0xb42ef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42ef8: LoadField: r1 = r0->field_87
    //     0xb42ef8: ldur            w1, [x0, #0x87]
    // 0xb42efc: DecompressPointer r1
    //     0xb42efc: add             x1, x1, HEAP, lsl #32
    // 0xb42f00: LoadField: r0 = r1->field_2b
    //     0xb42f00: ldur            w0, [x1, #0x2b]
    // 0xb42f04: DecompressPointer r0
    //     0xb42f04: add             x0, x0, HEAP, lsl #32
    // 0xb42f08: ldur            x1, [fp, #-0x10]
    // 0xb42f0c: stur            x0, [fp, #-0x48]
    // 0xb42f10: r0 = of()
    //     0xb42f10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42f14: LoadField: r1 = r0->field_5b
    //     0xb42f14: ldur            w1, [x0, #0x5b]
    // 0xb42f18: DecompressPointer r1
    //     0xb42f18: add             x1, x1, HEAP, lsl #32
    // 0xb42f1c: r16 = 14.000000
    //     0xb42f1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb42f20: ldr             x16, [x16, #0x1d8]
    // 0xb42f24: stp             x16, x1, [SP]
    // 0xb42f28: ldur            x1, [fp, #-0x48]
    // 0xb42f2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb42f2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb42f30: ldr             x4, [x4, #0x9b8]
    // 0xb42f34: r0 = copyWith()
    //     0xb42f34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42f38: ldur            x2, [fp, #-8]
    // 0xb42f3c: stur            x0, [fp, #-0x50]
    // 0xb42f40: LoadField: r3 = r2->field_37
    //     0xb42f40: ldur            w3, [x2, #0x37]
    // 0xb42f44: DecompressPointer r3
    //     0xb42f44: add             x3, x3, HEAP, lsl #32
    // 0xb42f48: ldur            x1, [fp, #-0x10]
    // 0xb42f4c: stur            x3, [fp, #-0x48]
    // 0xb42f50: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb42f50: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb42f54: ldur            x1, [fp, #-0x10]
    // 0xb42f58: stur            x0, [fp, #-0x58]
    // 0xb42f5c: r0 = of()
    //     0xb42f5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42f60: LoadField: r1 = r0->field_87
    //     0xb42f60: ldur            w1, [x0, #0x87]
    // 0xb42f64: DecompressPointer r1
    //     0xb42f64: add             x1, x1, HEAP, lsl #32
    // 0xb42f68: LoadField: r0 = r1->field_2b
    //     0xb42f68: ldur            w0, [x1, #0x2b]
    // 0xb42f6c: DecompressPointer r0
    //     0xb42f6c: add             x0, x0, HEAP, lsl #32
    // 0xb42f70: r16 = 12.000000
    //     0xb42f70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb42f74: ldr             x16, [x16, #0x9e8]
    // 0xb42f78: r30 = Instance_MaterialColor
    //     0xb42f78: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb42f7c: ldr             lr, [lr, #0x180]
    // 0xb42f80: stp             lr, x16, [SP]
    // 0xb42f84: mov             x1, x0
    // 0xb42f88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb42f88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb42f8c: ldr             x4, [x4, #0xaa0]
    // 0xb42f90: r0 = copyWith()
    //     0xb42f90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42f94: ldur            x1, [fp, #-0x10]
    // 0xb42f98: stur            x0, [fp, #-0x60]
    // 0xb42f9c: r0 = of()
    //     0xb42f9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb42fa0: LoadField: r1 = r0->field_87
    //     0xb42fa0: ldur            w1, [x0, #0x87]
    // 0xb42fa4: DecompressPointer r1
    //     0xb42fa4: add             x1, x1, HEAP, lsl #32
    // 0xb42fa8: LoadField: r0 = r1->field_2b
    //     0xb42fa8: ldur            w0, [x1, #0x2b]
    // 0xb42fac: DecompressPointer r0
    //     0xb42fac: add             x0, x0, HEAP, lsl #32
    // 0xb42fb0: stur            x0, [fp, #-0x70]
    // 0xb42fb4: r1 = Instance_Color
    //     0xb42fb4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb42fb8: d0 = 0.400000
    //     0xb42fb8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb42fbc: r0 = withOpacity()
    //     0xb42fbc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb42fc0: r16 = 14.000000
    //     0xb42fc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb42fc4: ldr             x16, [x16, #0x1d8]
    // 0xb42fc8: stp             x0, x16, [SP]
    // 0xb42fcc: ldur            x1, [fp, #-0x70]
    // 0xb42fd0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb42fd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb42fd4: ldr             x4, [x4, #0xaa0]
    // 0xb42fd8: r0 = copyWith()
    //     0xb42fd8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb42fdc: ldur            x2, [fp, #-8]
    // 0xb42fe0: stur            x0, [fp, #-0x80]
    // 0xb42fe4: LoadField: r1 = r2->field_63
    //     0xb42fe4: ldur            w1, [x2, #0x63]
    // 0xb42fe8: DecompressPointer r1
    //     0xb42fe8: add             x1, x1, HEAP, lsl #32
    // 0xb42fec: tbnz            w1, #4, #0xb43084
    // 0xb42ff0: LoadField: r1 = r2->field_37
    //     0xb42ff0: ldur            w1, [x2, #0x37]
    // 0xb42ff4: DecompressPointer r1
    //     0xb42ff4: add             x1, x1, HEAP, lsl #32
    // 0xb42ff8: LoadField: r3 = r1->field_27
    //     0xb42ff8: ldur            w3, [x1, #0x27]
    // 0xb42ffc: DecompressPointer r3
    //     0xb42ffc: add             x3, x3, HEAP, lsl #32
    // 0xb43000: LoadField: r1 = r3->field_7
    //     0xb43000: ldur            w1, [x3, #7]
    // 0xb43004: DecompressPointer r1
    //     0xb43004: add             x1, x1, HEAP, lsl #32
    // 0xb43008: LoadField: r3 = r1->field_7
    //     0xb43008: ldur            w3, [x1, #7]
    // 0xb4300c: cbz             w3, #0xb43028
    // 0xb43010: r1 = LoadInt32Instr(r3)
    //     0xb43010: sbfx            x1, x3, #1, #0x1f
    // 0xb43014: cmp             x1, #5
    // 0xb43018: b.lt            #0xb43028
    // 0xb4301c: r1 = Instance_IconData
    //     0xb4301c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb43020: ldr             x1, [x1, #0x130]
    // 0xb43024: b               #0xb4303c
    // 0xb43028: cbz             w3, #0xb43038
    // 0xb4302c: r1 = Instance_IconData
    //     0xb4302c: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb43030: ldr             x1, [x1, #0x138]
    // 0xb43034: b               #0xb4303c
    // 0xb43038: r1 = Null
    //     0xb43038: mov             x1, NULL
    // 0xb4303c: stur            x1, [fp, #-0x78]
    // 0xb43040: cbz             w3, #0xb4305c
    // 0xb43044: r4 = LoadInt32Instr(r3)
    //     0xb43044: sbfx            x4, x3, #1, #0x1f
    // 0xb43048: cmp             x4, #5
    // 0xb4304c: b.lt            #0xb4305c
    // 0xb43050: r3 = Instance_Color
    //     0xb43050: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb43054: ldr             x3, [x3, #0x858]
    // 0xb43058: b               #0xb43064
    // 0xb4305c: r3 = Instance_Color
    //     0xb4305c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb43060: ldr             x3, [x3, #0x50]
    // 0xb43064: stur            x3, [fp, #-0x70]
    // 0xb43068: r0 = Icon()
    //     0xb43068: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb4306c: mov             x1, x0
    // 0xb43070: ldur            x0, [fp, #-0x78]
    // 0xb43074: StoreField: r1->field_b = r0
    //     0xb43074: stur            w0, [x1, #0xb]
    // 0xb43078: ldur            x0, [fp, #-0x70]
    // 0xb4307c: StoreField: r1->field_23 = r0
    //     0xb4307c: stur            w0, [x1, #0x23]
    // 0xb43080: b               #0xb43088
    // 0xb43084: r1 = Null
    //     0xb43084: mov             x1, NULL
    // 0xb43088: ldur            x2, [fp, #-8]
    // 0xb4308c: ldur            x4, [fp, #-0x30]
    // 0xb43090: ldur            x0, [fp, #-0x38]
    // 0xb43094: ldur            x3, [fp, #-0x20]
    // 0xb43098: ldur            x16, [fp, #-0x60]
    // 0xb4309c: r30 = Instance_EdgeInsets
    //     0xb4309c: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb430a0: ldr             lr, [lr, #0xa78]
    // 0xb430a4: stp             lr, x16, [SP, #0x18]
    // 0xb430a8: r16 = "Landmark"
    //     0xb430a8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54118] "Landmark"
    //     0xb430ac: ldr             x16, [x16, #0x118]
    // 0xb430b0: ldur            lr, [fp, #-0x80]
    // 0xb430b4: stp             lr, x16, [SP, #8]
    // 0xb430b8: str             x1, [SP]
    // 0xb430bc: ldur            x1, [fp, #-0x58]
    // 0xb430c0: r4 = const [0, 0x6, 0x5, 0x1, contentPadding, 0x2, errorStyle, 0x1, hintStyle, 0x4, hintText, 0x3, suffixIcon, 0x5, null]
    //     0xb430c0: add             x4, PP, #0x56, lsl #12  ; [pp+0x56868] List(15) [0, 0x6, 0x5, 0x1, "contentPadding", 0x2, "errorStyle", 0x1, "hintStyle", 0x4, "hintText", 0x3, "suffixIcon", 0x5, Null]
    //     0xb430c4: ldr             x4, [x4, #0x868]
    // 0xb430c8: r0 = copyWith()
    //     0xb430c8: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb430cc: ldur            x2, [fp, #-8]
    // 0xb430d0: r1 = Function '_validateLandmark@1549293123':.
    //     0xb430d0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56870] AnonymousClosure: (0xb43ab0), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateLandmark (0xa054c0)
    //     0xb430d4: ldr             x1, [x1, #0x870]
    // 0xb430d8: stur            x0, [fp, #-0x58]
    // 0xb430dc: r0 = AllocateClosure()
    //     0xb430dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb430e0: ldur            x2, [fp, #-0x18]
    // 0xb430e4: r1 = Function '<anonymous closure>':.
    //     0xb430e4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56878] AnonymousClosure: (0xb43a38), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xb40ae8)
    //     0xb430e8: ldr             x1, [x1, #0x878]
    // 0xb430ec: stur            x0, [fp, #-0x60]
    // 0xb430f0: r0 = AllocateClosure()
    //     0xb430f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb430f4: r1 = <String>
    //     0xb430f4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb430f8: stur            x0, [fp, #-0x70]
    // 0xb430fc: r0 = TextFormField()
    //     0xb430fc: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb43100: stur            x0, [fp, #-0x78]
    // 0xb43104: ldur            x16, [fp, #-0x60]
    // 0xb43108: r30 = true
    //     0xb43108: add             lr, NULL, #0x20  ; true
    // 0xb4310c: stp             lr, x16, [SP, #0x38]
    // 0xb43110: r16 = Instance_AutovalidateMode
    //     0xb43110: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb43114: ldr             x16, [x16, #0x7e8]
    // 0xb43118: ldur            lr, [fp, #-0x40]
    // 0xb4311c: stp             lr, x16, [SP, #0x28]
    // 0xb43120: ldur            x16, [fp, #-0x50]
    // 0xb43124: r30 = Instance_TextInputType
    //     0xb43124: add             lr, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xb43128: ldr             lr, [lr, #0x68]
    // 0xb4312c: stp             lr, x16, [SP, #0x18]
    // 0xb43130: r16 = 2
    //     0xb43130: movz            x16, #0x2
    // 0xb43134: ldur            lr, [fp, #-0x48]
    // 0xb43138: stp             lr, x16, [SP, #8]
    // 0xb4313c: ldur            x16, [fp, #-0x70]
    // 0xb43140: str             x16, [SP]
    // 0xb43144: mov             x1, x0
    // 0xb43148: ldur            x2, [fp, #-0x58]
    // 0xb4314c: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x9, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x7, maxLines, 0x8, onChanged, 0xa, style, 0x6, validator, 0x2, null]
    //     0xb4314c: add             x4, PP, #0x56, lsl #12  ; [pp+0x56880] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x9, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x7, "maxLines", 0x8, "onChanged", 0xa, "style", 0x6, "validator", 0x2, Null]
    //     0xb43150: ldr             x4, [x4, #0x880]
    // 0xb43154: r0 = TextFormField()
    //     0xb43154: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb43158: r0 = Form()
    //     0xb43158: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb4315c: mov             x3, x0
    // 0xb43160: ldur            x0, [fp, #-0x78]
    // 0xb43164: stur            x3, [fp, #-0x40]
    // 0xb43168: StoreField: r3->field_b = r0
    //     0xb43168: stur            w0, [x3, #0xb]
    // 0xb4316c: r0 = Instance_AutovalidateMode
    //     0xb4316c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb43170: ldr             x0, [x0, #0x800]
    // 0xb43174: StoreField: r3->field_23 = r0
    //     0xb43174: stur            w0, [x3, #0x23]
    // 0xb43178: ldur            x1, [fp, #-0x20]
    // 0xb4317c: StoreField: r3->field_7 = r1
    //     0xb4317c: stur            w1, [x3, #7]
    // 0xb43180: r1 = Null
    //     0xb43180: mov             x1, NULL
    // 0xb43184: r2 = 4
    //     0xb43184: movz            x2, #0x4
    // 0xb43188: r0 = AllocateArray()
    //     0xb43188: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4318c: mov             x2, x0
    // 0xb43190: ldur            x0, [fp, #-0x38]
    // 0xb43194: stur            x2, [fp, #-0x20]
    // 0xb43198: StoreField: r2->field_f = r0
    //     0xb43198: stur            w0, [x2, #0xf]
    // 0xb4319c: ldur            x0, [fp, #-0x40]
    // 0xb431a0: StoreField: r2->field_13 = r0
    //     0xb431a0: stur            w0, [x2, #0x13]
    // 0xb431a4: r1 = <Widget>
    //     0xb431a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb431a8: r0 = AllocateGrowableArray()
    //     0xb431a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb431ac: mov             x1, x0
    // 0xb431b0: ldur            x0, [fp, #-0x20]
    // 0xb431b4: stur            x1, [fp, #-0x38]
    // 0xb431b8: StoreField: r1->field_f = r0
    //     0xb431b8: stur            w0, [x1, #0xf]
    // 0xb431bc: r2 = 4
    //     0xb431bc: movz            x2, #0x4
    // 0xb431c0: StoreField: r1->field_b = r2
    //     0xb431c0: stur            w2, [x1, #0xb]
    // 0xb431c4: r0 = Column()
    //     0xb431c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb431c8: mov             x1, x0
    // 0xb431cc: r0 = Instance_Axis
    //     0xb431cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb431d0: stur            x1, [fp, #-0x20]
    // 0xb431d4: StoreField: r1->field_f = r0
    //     0xb431d4: stur            w0, [x1, #0xf]
    // 0xb431d8: r2 = Instance_MainAxisAlignment
    //     0xb431d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb431dc: ldr             x2, [x2, #0xa08]
    // 0xb431e0: StoreField: r1->field_13 = r2
    //     0xb431e0: stur            w2, [x1, #0x13]
    // 0xb431e4: r3 = Instance_MainAxisSize
    //     0xb431e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb431e8: ldr             x3, [x3, #0xa10]
    // 0xb431ec: ArrayStore: r1[0] = r3  ; List_4
    //     0xb431ec: stur            w3, [x1, #0x17]
    // 0xb431f0: r4 = Instance_CrossAxisAlignment
    //     0xb431f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb431f4: ldr             x4, [x4, #0x890]
    // 0xb431f8: StoreField: r1->field_1b = r4
    //     0xb431f8: stur            w4, [x1, #0x1b]
    // 0xb431fc: r5 = Instance_VerticalDirection
    //     0xb431fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb43200: ldr             x5, [x5, #0xa20]
    // 0xb43204: StoreField: r1->field_23 = r5
    //     0xb43204: stur            w5, [x1, #0x23]
    // 0xb43208: r6 = Instance_Clip
    //     0xb43208: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4320c: ldr             x6, [x6, #0x38]
    // 0xb43210: StoreField: r1->field_2b = r6
    //     0xb43210: stur            w6, [x1, #0x2b]
    // 0xb43214: StoreField: r1->field_2f = rZR
    //     0xb43214: stur            xzr, [x1, #0x2f]
    // 0xb43218: ldur            x7, [fp, #-0x38]
    // 0xb4321c: StoreField: r1->field_b = r7
    //     0xb4321c: stur            w7, [x1, #0xb]
    // 0xb43220: r0 = Padding()
    //     0xb43220: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb43224: mov             x1, x0
    // 0xb43228: r0 = Instance_EdgeInsets
    //     0xb43228: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb4322c: ldr             x0, [x0, #0x858]
    // 0xb43230: stur            x1, [fp, #-0x38]
    // 0xb43234: StoreField: r1->field_f = r0
    //     0xb43234: stur            w0, [x1, #0xf]
    // 0xb43238: ldur            x2, [fp, #-0x20]
    // 0xb4323c: StoreField: r1->field_b = r2
    //     0xb4323c: stur            w2, [x1, #0xb]
    // 0xb43240: r0 = Visibility()
    //     0xb43240: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb43244: mov             x3, x0
    // 0xb43248: ldur            x0, [fp, #-0x38]
    // 0xb4324c: stur            x3, [fp, #-0x20]
    // 0xb43250: StoreField: r3->field_b = r0
    //     0xb43250: stur            w0, [x3, #0xb]
    // 0xb43254: r4 = Instance_SizedBox
    //     0xb43254: ldr             x4, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb43258: StoreField: r3->field_f = r4
    //     0xb43258: stur            w4, [x3, #0xf]
    // 0xb4325c: ldur            x0, [fp, #-0x30]
    // 0xb43260: StoreField: r3->field_13 = r0
    //     0xb43260: stur            w0, [x3, #0x13]
    // 0xb43264: r5 = false
    //     0xb43264: add             x5, NULL, #0x30  ; false
    // 0xb43268: ArrayStore: r3[0] = r5  ; List_4
    //     0xb43268: stur            w5, [x3, #0x17]
    // 0xb4326c: StoreField: r3->field_1b = r5
    //     0xb4326c: stur            w5, [x3, #0x1b]
    // 0xb43270: StoreField: r3->field_1f = r5
    //     0xb43270: stur            w5, [x3, #0x1f]
    // 0xb43274: StoreField: r3->field_23 = r5
    //     0xb43274: stur            w5, [x3, #0x23]
    // 0xb43278: StoreField: r3->field_27 = r5
    //     0xb43278: stur            w5, [x3, #0x27]
    // 0xb4327c: StoreField: r3->field_2b = r5
    //     0xb4327c: stur            w5, [x3, #0x2b]
    // 0xb43280: ldur            x6, [fp, #-8]
    // 0xb43284: LoadField: r0 = r6->field_b
    //     0xb43284: ldur            w0, [x6, #0xb]
    // 0xb43288: DecompressPointer r0
    //     0xb43288: add             x0, x0, HEAP, lsl #32
    // 0xb4328c: cmp             w0, NULL
    // 0xb43290: b.eq            #0xb43980
    // 0xb43294: LoadField: r1 = r0->field_b
    //     0xb43294: ldur            w1, [x0, #0xb]
    // 0xb43298: DecompressPointer r1
    //     0xb43298: add             x1, x1, HEAP, lsl #32
    // 0xb4329c: LoadField: r0 = r1->field_f
    //     0xb4329c: ldur            w0, [x1, #0xf]
    // 0xb432a0: DecompressPointer r0
    //     0xb432a0: add             x0, x0, HEAP, lsl #32
    // 0xb432a4: cmp             w0, NULL
    // 0xb432a8: b.ne            #0xb432b4
    // 0xb432ac: r0 = Null
    //     0xb432ac: mov             x0, NULL
    // 0xb432b0: b               #0xb432e0
    // 0xb432b4: r1 = LoadClassIdInstr(r0)
    //     0xb432b4: ldur            x1, [x0, #-1]
    //     0xb432b8: ubfx            x1, x1, #0xc, #0x14
    // 0xb432bc: mov             x16, x0
    // 0xb432c0: mov             x0, x1
    // 0xb432c4: mov             x1, x16
    // 0xb432c8: r2 = "alternate_contact_number"
    //     0xb432c8: add             x2, PP, #0x54, lsl #12  ; [pp+0x54140] "alternate_contact_number"
    //     0xb432cc: ldr             x2, [x2, #0x140]
    // 0xb432d0: r0 = GDT[cid_x0 + 0xe437]()
    //     0xb432d0: movz            x17, #0xe437
    //     0xb432d4: add             lr, x0, x17
    //     0xb432d8: ldr             lr, [x21, lr, lsl #3]
    //     0xb432dc: blr             lr
    // 0xb432e0: cmp             w0, NULL
    // 0xb432e4: b.ne            #0xb432f0
    // 0xb432e8: r3 = false
    //     0xb432e8: add             x3, NULL, #0x30  ; false
    // 0xb432ec: b               #0xb432f4
    // 0xb432f0: mov             x3, x0
    // 0xb432f4: ldur            x2, [fp, #-8]
    // 0xb432f8: ldur            x0, [fp, #-0x68]
    // 0xb432fc: ldur            x1, [fp, #-0x10]
    // 0xb43300: stur            x3, [fp, #-0x30]
    // 0xb43304: r0 = of()
    //     0xb43304: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb43308: LoadField: r1 = r0->field_87
    //     0xb43308: ldur            w1, [x0, #0x87]
    // 0xb4330c: DecompressPointer r1
    //     0xb4330c: add             x1, x1, HEAP, lsl #32
    // 0xb43310: LoadField: r0 = r1->field_2b
    //     0xb43310: ldur            w0, [x1, #0x2b]
    // 0xb43314: DecompressPointer r0
    //     0xb43314: add             x0, x0, HEAP, lsl #32
    // 0xb43318: r16 = 12.000000
    //     0xb43318: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4331c: ldr             x16, [x16, #0x9e8]
    // 0xb43320: r30 = Instance_Color
    //     0xb43320: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb43324: stp             lr, x16, [SP]
    // 0xb43328: mov             x1, x0
    // 0xb4332c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4332c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb43330: ldr             x4, [x4, #0xaa0]
    // 0xb43334: r0 = copyWith()
    //     0xb43334: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb43338: stur            x0, [fp, #-0x38]
    // 0xb4333c: r0 = Text()
    //     0xb4333c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb43340: mov             x1, x0
    // 0xb43344: r0 = "Alternate Ph No"
    //     0xb43344: add             x0, PP, #0x54, lsl #12  ; [pp+0x54148] "Alternate Ph No"
    //     0xb43348: ldr             x0, [x0, #0x148]
    // 0xb4334c: stur            x1, [fp, #-0x40]
    // 0xb43350: StoreField: r1->field_b = r0
    //     0xb43350: stur            w0, [x1, #0xb]
    // 0xb43354: ldur            x0, [fp, #-0x38]
    // 0xb43358: StoreField: r1->field_13 = r0
    //     0xb43358: stur            w0, [x1, #0x13]
    // 0xb4335c: r0 = Padding()
    //     0xb4335c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb43360: mov             x1, x0
    // 0xb43364: r0 = Instance_EdgeInsets
    //     0xb43364: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xb43368: ldr             x0, [x0, #0x4c0]
    // 0xb4336c: stur            x1, [fp, #-0x48]
    // 0xb43370: StoreField: r1->field_f = r0
    //     0xb43370: stur            w0, [x1, #0xf]
    // 0xb43374: ldur            x0, [fp, #-0x40]
    // 0xb43378: StoreField: r1->field_b = r0
    //     0xb43378: stur            w0, [x1, #0xb]
    // 0xb4337c: ldur            x2, [fp, #-8]
    // 0xb43380: LoadField: r0 = r2->field_2b
    //     0xb43380: ldur            w0, [x2, #0x2b]
    // 0xb43384: DecompressPointer r0
    //     0xb43384: add             x0, x0, HEAP, lsl #32
    // 0xb43388: stur            x0, [fp, #-0x38]
    // 0xb4338c: r16 = "[0-9]"
    //     0xb4338c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37128] "[0-9]"
    //     0xb43390: ldr             x16, [x16, #0x128]
    // 0xb43394: stp             x16, NULL, [SP, #0x20]
    // 0xb43398: r16 = false
    //     0xb43398: add             x16, NULL, #0x30  ; false
    // 0xb4339c: r30 = true
    //     0xb4339c: add             lr, NULL, #0x20  ; true
    // 0xb433a0: stp             lr, x16, [SP, #0x10]
    // 0xb433a4: r16 = false
    //     0xb433a4: add             x16, NULL, #0x30  ; false
    // 0xb433a8: r30 = false
    //     0xb433a8: add             lr, NULL, #0x30  ; false
    // 0xb433ac: stp             lr, x16, [SP]
    // 0xb433b0: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xb433b0: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xb433b4: r0 = _RegExp()
    //     0xb433b4: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0xb433b8: stur            x0, [fp, #-0x40]
    // 0xb433bc: r0 = FilteringTextInputFormatter()
    //     0xb433bc: bl              #0xa01244  ; AllocateFilteringTextInputFormatterStub -> FilteringTextInputFormatter (size=0x14)
    // 0xb433c0: mov             x1, x0
    // 0xb433c4: ldur            x0, [fp, #-0x40]
    // 0xb433c8: stur            x1, [fp, #-0x50]
    // 0xb433cc: StoreField: r1->field_b = r0
    //     0xb433cc: stur            w0, [x1, #0xb]
    // 0xb433d0: r0 = true
    //     0xb433d0: add             x0, NULL, #0x20  ; true
    // 0xb433d4: StoreField: r1->field_7 = r0
    //     0xb433d4: stur            w0, [x1, #7]
    // 0xb433d8: r0 = ""
    //     0xb433d8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb433dc: StoreField: r1->field_f = r0
    //     0xb433dc: stur            w0, [x1, #0xf]
    // 0xb433e0: r0 = LengthLimitingTextInputFormatter()
    //     0xb433e0: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb433e4: mov             x3, x0
    // 0xb433e8: r0 = 20
    //     0xb433e8: movz            x0, #0x14
    // 0xb433ec: stur            x3, [fp, #-0x40]
    // 0xb433f0: StoreField: r3->field_7 = r0
    //     0xb433f0: stur            w0, [x3, #7]
    // 0xb433f4: r1 = Null
    //     0xb433f4: mov             x1, NULL
    // 0xb433f8: r2 = 6
    //     0xb433f8: movz            x2, #0x6
    // 0xb433fc: r0 = AllocateArray()
    //     0xb433fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb43400: mov             x2, x0
    // 0xb43404: ldur            x0, [fp, #-0x68]
    // 0xb43408: stur            x2, [fp, #-0x58]
    // 0xb4340c: StoreField: r2->field_f = r0
    //     0xb4340c: stur            w0, [x2, #0xf]
    // 0xb43410: ldur            x0, [fp, #-0x50]
    // 0xb43414: StoreField: r2->field_13 = r0
    //     0xb43414: stur            w0, [x2, #0x13]
    // 0xb43418: ldur            x0, [fp, #-0x40]
    // 0xb4341c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4341c: stur            w0, [x2, #0x17]
    // 0xb43420: r1 = <TextInputFormatter>
    //     0xb43420: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb43424: ldr             x1, [x1, #0x7b0]
    // 0xb43428: r0 = AllocateGrowableArray()
    //     0xb43428: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4342c: mov             x2, x0
    // 0xb43430: ldur            x0, [fp, #-0x58]
    // 0xb43434: stur            x2, [fp, #-0x40]
    // 0xb43438: StoreField: r2->field_f = r0
    //     0xb43438: stur            w0, [x2, #0xf]
    // 0xb4343c: r0 = 6
    //     0xb4343c: movz            x0, #0x6
    // 0xb43440: StoreField: r2->field_b = r0
    //     0xb43440: stur            w0, [x2, #0xb]
    // 0xb43444: ldur            x1, [fp, #-0x10]
    // 0xb43448: r0 = of()
    //     0xb43448: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4344c: LoadField: r1 = r0->field_87
    //     0xb4344c: ldur            w1, [x0, #0x87]
    // 0xb43450: DecompressPointer r1
    //     0xb43450: add             x1, x1, HEAP, lsl #32
    // 0xb43454: LoadField: r0 = r1->field_2b
    //     0xb43454: ldur            w0, [x1, #0x2b]
    // 0xb43458: DecompressPointer r0
    //     0xb43458: add             x0, x0, HEAP, lsl #32
    // 0xb4345c: ldur            x1, [fp, #-0x10]
    // 0xb43460: stur            x0, [fp, #-0x50]
    // 0xb43464: r0 = of()
    //     0xb43464: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb43468: LoadField: r1 = r0->field_5b
    //     0xb43468: ldur            w1, [x0, #0x5b]
    // 0xb4346c: DecompressPointer r1
    //     0xb4346c: add             x1, x1, HEAP, lsl #32
    // 0xb43470: r16 = 14.000000
    //     0xb43470: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb43474: ldr             x16, [x16, #0x1d8]
    // 0xb43478: stp             x16, x1, [SP]
    // 0xb4347c: ldur            x1, [fp, #-0x50]
    // 0xb43480: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb43480: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb43484: ldr             x4, [x4, #0x9b8]
    // 0xb43488: r0 = copyWith()
    //     0xb43488: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4348c: ldur            x2, [fp, #-8]
    // 0xb43490: stur            x0, [fp, #-0x58]
    // 0xb43494: LoadField: r3 = r2->field_43
    //     0xb43494: ldur            w3, [x2, #0x43]
    // 0xb43498: DecompressPointer r3
    //     0xb43498: add             x3, x3, HEAP, lsl #32
    // 0xb4349c: ldur            x1, [fp, #-0x10]
    // 0xb434a0: stur            x3, [fp, #-0x50]
    // 0xb434a4: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb434a4: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb434a8: ldur            x1, [fp, #-0x10]
    // 0xb434ac: stur            x0, [fp, #-0x60]
    // 0xb434b0: r0 = of()
    //     0xb434b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb434b4: LoadField: r1 = r0->field_87
    //     0xb434b4: ldur            w1, [x0, #0x87]
    // 0xb434b8: DecompressPointer r1
    //     0xb434b8: add             x1, x1, HEAP, lsl #32
    // 0xb434bc: LoadField: r0 = r1->field_2b
    //     0xb434bc: ldur            w0, [x1, #0x2b]
    // 0xb434c0: DecompressPointer r0
    //     0xb434c0: add             x0, x0, HEAP, lsl #32
    // 0xb434c4: r16 = 12.000000
    //     0xb434c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb434c8: ldr             x16, [x16, #0x9e8]
    // 0xb434cc: r30 = Instance_MaterialColor
    //     0xb434cc: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xb434d0: ldr             lr, [lr, #0x180]
    // 0xb434d4: stp             lr, x16, [SP]
    // 0xb434d8: mov             x1, x0
    // 0xb434dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb434dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb434e0: ldr             x4, [x4, #0xaa0]
    // 0xb434e4: r0 = copyWith()
    //     0xb434e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb434e8: ldur            x1, [fp, #-0x10]
    // 0xb434ec: stur            x0, [fp, #-0x10]
    // 0xb434f0: r0 = of()
    //     0xb434f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb434f4: LoadField: r1 = r0->field_87
    //     0xb434f4: ldur            w1, [x0, #0x87]
    // 0xb434f8: DecompressPointer r1
    //     0xb434f8: add             x1, x1, HEAP, lsl #32
    // 0xb434fc: LoadField: r0 = r1->field_2b
    //     0xb434fc: ldur            w0, [x1, #0x2b]
    // 0xb43500: DecompressPointer r0
    //     0xb43500: add             x0, x0, HEAP, lsl #32
    // 0xb43504: stur            x0, [fp, #-0x68]
    // 0xb43508: r1 = Instance_Color
    //     0xb43508: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4350c: d0 = 0.400000
    //     0xb4350c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb43510: r0 = withOpacity()
    //     0xb43510: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb43514: r16 = 14.000000
    //     0xb43514: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb43518: ldr             x16, [x16, #0x1d8]
    // 0xb4351c: stp             x0, x16, [SP]
    // 0xb43520: ldur            x1, [fp, #-0x68]
    // 0xb43524: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb43524: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb43528: ldr             x4, [x4, #0xaa0]
    // 0xb4352c: r0 = copyWith()
    //     0xb4352c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb43530: mov             x3, x0
    // 0xb43534: ldur            x0, [fp, #-8]
    // 0xb43538: stur            x3, [fp, #-0x68]
    // 0xb4353c: LoadField: r1 = r0->field_67
    //     0xb4353c: ldur            w1, [x0, #0x67]
    // 0xb43540: DecompressPointer r1
    //     0xb43540: add             x1, x1, HEAP, lsl #32
    // 0xb43544: tbnz            w1, #4, #0xb4366c
    // 0xb43548: LoadField: r1 = r0->field_43
    //     0xb43548: ldur            w1, [x0, #0x43]
    // 0xb4354c: DecompressPointer r1
    //     0xb4354c: add             x1, x1, HEAP, lsl #32
    // 0xb43550: LoadField: r2 = r1->field_27
    //     0xb43550: ldur            w2, [x1, #0x27]
    // 0xb43554: DecompressPointer r2
    //     0xb43554: add             x2, x2, HEAP, lsl #32
    // 0xb43558: LoadField: r1 = r2->field_7
    //     0xb43558: ldur            w1, [x2, #7]
    // 0xb4355c: DecompressPointer r1
    //     0xb4355c: add             x1, x1, HEAP, lsl #32
    // 0xb43560: LoadField: r2 = r1->field_7
    //     0xb43560: ldur            w2, [x1, #7]
    // 0xb43564: cbz             w2, #0xb435a8
    // 0xb43568: cmp             w2, #0x14
    // 0xb4356c: b.ne            #0xb435a8
    // 0xb43570: r16 = 2
    //     0xb43570: movz            x16, #0x2
    // 0xb43574: str             x16, [SP]
    // 0xb43578: r2 = 0
    //     0xb43578: movz            x2, #0
    // 0xb4357c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb4357c: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb43580: r0 = substring()
    //     0xb43580: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xb43584: mov             x1, x0
    // 0xb43588: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb43588: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb4358c: r0 = parse()
    //     0xb4358c: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb43590: cmp             x0, #6
    // 0xb43594: b.lt            #0xb435a8
    // 0xb43598: ldur            x0, [fp, #-8]
    // 0xb4359c: r3 = Instance_IconData
    //     0xb4359c: add             x3, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xb435a0: ldr             x3, [x3, #0x130]
    // 0xb435a4: b               #0xb435e0
    // 0xb435a8: ldur            x0, [fp, #-8]
    // 0xb435ac: LoadField: r1 = r0->field_43
    //     0xb435ac: ldur            w1, [x0, #0x43]
    // 0xb435b0: DecompressPointer r1
    //     0xb435b0: add             x1, x1, HEAP, lsl #32
    // 0xb435b4: LoadField: r2 = r1->field_27
    //     0xb435b4: ldur            w2, [x1, #0x27]
    // 0xb435b8: DecompressPointer r2
    //     0xb435b8: add             x2, x2, HEAP, lsl #32
    // 0xb435bc: LoadField: r1 = r2->field_7
    //     0xb435bc: ldur            w1, [x2, #7]
    // 0xb435c0: DecompressPointer r1
    //     0xb435c0: add             x1, x1, HEAP, lsl #32
    // 0xb435c4: LoadField: r2 = r1->field_7
    //     0xb435c4: ldur            w2, [x1, #7]
    // 0xb435c8: cbz             w2, #0xb435d8
    // 0xb435cc: r1 = Instance_IconData
    //     0xb435cc: add             x1, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xb435d0: ldr             x1, [x1, #0x138]
    // 0xb435d4: b               #0xb435dc
    // 0xb435d8: r1 = Null
    //     0xb435d8: mov             x1, NULL
    // 0xb435dc: mov             x3, x1
    // 0xb435e0: stur            x3, [fp, #-0x70]
    // 0xb435e4: LoadField: r1 = r0->field_43
    //     0xb435e4: ldur            w1, [x0, #0x43]
    // 0xb435e8: DecompressPointer r1
    //     0xb435e8: add             x1, x1, HEAP, lsl #32
    // 0xb435ec: LoadField: r2 = r1->field_27
    //     0xb435ec: ldur            w2, [x1, #0x27]
    // 0xb435f0: DecompressPointer r2
    //     0xb435f0: add             x2, x2, HEAP, lsl #32
    // 0xb435f4: LoadField: r1 = r2->field_7
    //     0xb435f4: ldur            w1, [x2, #7]
    // 0xb435f8: DecompressPointer r1
    //     0xb435f8: add             x1, x1, HEAP, lsl #32
    // 0xb435fc: LoadField: r2 = r1->field_7
    //     0xb435fc: ldur            w2, [x1, #7]
    // 0xb43600: cbz             w2, #0xb43640
    // 0xb43604: cmp             w2, #0x14
    // 0xb43608: b.ne            #0xb43640
    // 0xb4360c: r16 = 2
    //     0xb4360c: movz            x16, #0x2
    // 0xb43610: str             x16, [SP]
    // 0xb43614: r2 = 0
    //     0xb43614: movz            x2, #0
    // 0xb43618: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb43618: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb4361c: r0 = substring()
    //     0xb4361c: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0xb43620: mov             x1, x0
    // 0xb43624: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb43624: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb43628: r0 = parse()
    //     0xb43628: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xb4362c: cmp             x0, #6
    // 0xb43630: b.lt            #0xb43640
    // 0xb43634: r1 = Instance_Color
    //     0xb43634: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb43638: ldr             x1, [x1, #0x858]
    // 0xb4363c: b               #0xb43648
    // 0xb43640: r1 = Instance_Color
    //     0xb43640: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb43644: ldr             x1, [x1, #0x50]
    // 0xb43648: ldur            x0, [fp, #-0x70]
    // 0xb4364c: stur            x1, [fp, #-0x78]
    // 0xb43650: r0 = Icon()
    //     0xb43650: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb43654: mov             x1, x0
    // 0xb43658: ldur            x0, [fp, #-0x70]
    // 0xb4365c: StoreField: r1->field_b = r0
    //     0xb4365c: stur            w0, [x1, #0xb]
    // 0xb43660: ldur            x0, [fp, #-0x78]
    // 0xb43664: StoreField: r1->field_23 = r0
    //     0xb43664: stur            w0, [x1, #0x23]
    // 0xb43668: b               #0xb43670
    // 0xb4366c: r1 = Null
    //     0xb4366c: mov             x1, NULL
    // 0xb43670: ldur            x5, [fp, #-0x28]
    // 0xb43674: ldur            x4, [fp, #-0x20]
    // 0xb43678: ldur            x3, [fp, #-0x30]
    // 0xb4367c: ldur            x0, [fp, #-0x48]
    // 0xb43680: ldur            x2, [fp, #-0x38]
    // 0xb43684: ldur            x16, [fp, #-0x10]
    // 0xb43688: r30 = Instance_EdgeInsets
    //     0xb43688: add             lr, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb4368c: ldr             lr, [lr, #0xa78]
    // 0xb43690: stp             lr, x16, [SP, #0x18]
    // 0xb43694: r16 = "Alternate Ph No"
    //     0xb43694: add             x16, PP, #0x54, lsl #12  ; [pp+0x54148] "Alternate Ph No"
    //     0xb43698: ldr             x16, [x16, #0x148]
    // 0xb4369c: ldur            lr, [fp, #-0x68]
    // 0xb436a0: stp             lr, x16, [SP, #8]
    // 0xb436a4: str             x1, [SP]
    // 0xb436a8: ldur            x1, [fp, #-0x60]
    // 0xb436ac: r4 = const [0, 0x6, 0x5, 0x1, contentPadding, 0x2, errorStyle, 0x1, hintStyle, 0x4, hintText, 0x3, suffixIcon, 0x5, null]
    //     0xb436ac: add             x4, PP, #0x56, lsl #12  ; [pp+0x56868] List(15) [0, 0x6, 0x5, 0x1, "contentPadding", 0x2, "errorStyle", 0x1, "hintStyle", 0x4, "hintText", 0x3, "suffixIcon", 0x5, Null]
    //     0xb436b0: ldr             x4, [x4, #0x868]
    // 0xb436b4: r0 = copyWith()
    //     0xb436b4: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb436b8: ldur            x2, [fp, #-8]
    // 0xb436bc: r1 = Function '_validateAlternateNo@1549293123':.
    //     0xb436bc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56888] AnonymousClosure: (0xb439fc), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAlternateNo (0xa05330)
    //     0xb436c0: ldr             x1, [x1, #0x888]
    // 0xb436c4: stur            x0, [fp, #-8]
    // 0xb436c8: r0 = AllocateClosure()
    //     0xb436c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb436cc: ldur            x2, [fp, #-0x18]
    // 0xb436d0: r1 = Function '<anonymous closure>':.
    //     0xb436d0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56890] AnonymousClosure: (0xb43984), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xb40ae8)
    //     0xb436d4: ldr             x1, [x1, #0x890]
    // 0xb436d8: stur            x0, [fp, #-0x10]
    // 0xb436dc: r0 = AllocateClosure()
    //     0xb436dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb436e0: r1 = <String>
    //     0xb436e0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb436e4: stur            x0, [fp, #-0x18]
    // 0xb436e8: r0 = TextFormField()
    //     0xb436e8: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb436ec: stur            x0, [fp, #-0x60]
    // 0xb436f0: ldur            x16, [fp, #-0x10]
    // 0xb436f4: r30 = true
    //     0xb436f4: add             lr, NULL, #0x20  ; true
    // 0xb436f8: stp             lr, x16, [SP, #0x38]
    // 0xb436fc: r16 = Instance_AutovalidateMode
    //     0xb436fc: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb43700: ldr             x16, [x16, #0x7e8]
    // 0xb43704: ldur            lr, [fp, #-0x40]
    // 0xb43708: stp             lr, x16, [SP, #0x28]
    // 0xb4370c: ldur            x16, [fp, #-0x58]
    // 0xb43710: r30 = Instance_TextInputType
    //     0xb43710: add             lr, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xb43714: ldr             lr, [lr, #0x1a0]
    // 0xb43718: stp             lr, x16, [SP, #0x18]
    // 0xb4371c: r16 = 2
    //     0xb4371c: movz            x16, #0x2
    // 0xb43720: ldur            lr, [fp, #-0x50]
    // 0xb43724: stp             lr, x16, [SP, #8]
    // 0xb43728: ldur            x16, [fp, #-0x18]
    // 0xb4372c: str             x16, [SP]
    // 0xb43730: mov             x1, x0
    // 0xb43734: ldur            x2, [fp, #-8]
    // 0xb43738: r4 = const [0, 0xb, 0x9, 0x2, autovalidateMode, 0x4, controller, 0x9, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x7, maxLines, 0x8, onChanged, 0xa, style, 0x6, validator, 0x2, null]
    //     0xb43738: add             x4, PP, #0x56, lsl #12  ; [pp+0x56880] List(23) [0, 0xb, 0x9, 0x2, "autovalidateMode", 0x4, "controller", 0x9, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x7, "maxLines", 0x8, "onChanged", 0xa, "style", 0x6, "validator", 0x2, Null]
    //     0xb4373c: ldr             x4, [x4, #0x880]
    // 0xb43740: r0 = TextFormField()
    //     0xb43740: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb43744: r0 = Form()
    //     0xb43744: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb43748: mov             x3, x0
    // 0xb4374c: ldur            x0, [fp, #-0x60]
    // 0xb43750: stur            x3, [fp, #-8]
    // 0xb43754: StoreField: r3->field_b = r0
    //     0xb43754: stur            w0, [x3, #0xb]
    // 0xb43758: r0 = Instance_AutovalidateMode
    //     0xb43758: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb4375c: ldr             x0, [x0, #0x800]
    // 0xb43760: StoreField: r3->field_23 = r0
    //     0xb43760: stur            w0, [x3, #0x23]
    // 0xb43764: ldur            x0, [fp, #-0x38]
    // 0xb43768: StoreField: r3->field_7 = r0
    //     0xb43768: stur            w0, [x3, #7]
    // 0xb4376c: r1 = Null
    //     0xb4376c: mov             x1, NULL
    // 0xb43770: r2 = 4
    //     0xb43770: movz            x2, #0x4
    // 0xb43774: r0 = AllocateArray()
    //     0xb43774: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb43778: mov             x2, x0
    // 0xb4377c: ldur            x0, [fp, #-0x48]
    // 0xb43780: stur            x2, [fp, #-0x10]
    // 0xb43784: StoreField: r2->field_f = r0
    //     0xb43784: stur            w0, [x2, #0xf]
    // 0xb43788: ldur            x0, [fp, #-8]
    // 0xb4378c: StoreField: r2->field_13 = r0
    //     0xb4378c: stur            w0, [x2, #0x13]
    // 0xb43790: r1 = <Widget>
    //     0xb43790: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb43794: r0 = AllocateGrowableArray()
    //     0xb43794: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb43798: mov             x1, x0
    // 0xb4379c: ldur            x0, [fp, #-0x10]
    // 0xb437a0: stur            x1, [fp, #-8]
    // 0xb437a4: StoreField: r1->field_f = r0
    //     0xb437a4: stur            w0, [x1, #0xf]
    // 0xb437a8: r0 = 4
    //     0xb437a8: movz            x0, #0x4
    // 0xb437ac: StoreField: r1->field_b = r0
    //     0xb437ac: stur            w0, [x1, #0xb]
    // 0xb437b0: r0 = Column()
    //     0xb437b0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb437b4: mov             x1, x0
    // 0xb437b8: r0 = Instance_Axis
    //     0xb437b8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb437bc: stur            x1, [fp, #-0x10]
    // 0xb437c0: StoreField: r1->field_f = r0
    //     0xb437c0: stur            w0, [x1, #0xf]
    // 0xb437c4: r2 = Instance_MainAxisAlignment
    //     0xb437c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb437c8: ldr             x2, [x2, #0xa08]
    // 0xb437cc: StoreField: r1->field_13 = r2
    //     0xb437cc: stur            w2, [x1, #0x13]
    // 0xb437d0: r3 = Instance_MainAxisSize
    //     0xb437d0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb437d4: ldr             x3, [x3, #0xa10]
    // 0xb437d8: ArrayStore: r1[0] = r3  ; List_4
    //     0xb437d8: stur            w3, [x1, #0x17]
    // 0xb437dc: r4 = Instance_CrossAxisAlignment
    //     0xb437dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb437e0: ldr             x4, [x4, #0x890]
    // 0xb437e4: StoreField: r1->field_1b = r4
    //     0xb437e4: stur            w4, [x1, #0x1b]
    // 0xb437e8: r5 = Instance_VerticalDirection
    //     0xb437e8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb437ec: ldr             x5, [x5, #0xa20]
    // 0xb437f0: StoreField: r1->field_23 = r5
    //     0xb437f0: stur            w5, [x1, #0x23]
    // 0xb437f4: r6 = Instance_Clip
    //     0xb437f4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb437f8: ldr             x6, [x6, #0x38]
    // 0xb437fc: StoreField: r1->field_2b = r6
    //     0xb437fc: stur            w6, [x1, #0x2b]
    // 0xb43800: StoreField: r1->field_2f = rZR
    //     0xb43800: stur            xzr, [x1, #0x2f]
    // 0xb43804: ldur            x7, [fp, #-8]
    // 0xb43808: StoreField: r1->field_b = r7
    //     0xb43808: stur            w7, [x1, #0xb]
    // 0xb4380c: r0 = Padding()
    //     0xb4380c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb43810: mov             x1, x0
    // 0xb43814: r0 = Instance_EdgeInsets
    //     0xb43814: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb43818: ldr             x0, [x0, #0x858]
    // 0xb4381c: stur            x1, [fp, #-8]
    // 0xb43820: StoreField: r1->field_f = r0
    //     0xb43820: stur            w0, [x1, #0xf]
    // 0xb43824: ldur            x0, [fp, #-0x10]
    // 0xb43828: StoreField: r1->field_b = r0
    //     0xb43828: stur            w0, [x1, #0xb]
    // 0xb4382c: r0 = Visibility()
    //     0xb4382c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb43830: mov             x3, x0
    // 0xb43834: ldur            x0, [fp, #-8]
    // 0xb43838: stur            x3, [fp, #-0x10]
    // 0xb4383c: StoreField: r3->field_b = r0
    //     0xb4383c: stur            w0, [x3, #0xb]
    // 0xb43840: r0 = Instance_SizedBox
    //     0xb43840: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb43844: StoreField: r3->field_f = r0
    //     0xb43844: stur            w0, [x3, #0xf]
    // 0xb43848: ldur            x0, [fp, #-0x30]
    // 0xb4384c: StoreField: r3->field_13 = r0
    //     0xb4384c: stur            w0, [x3, #0x13]
    // 0xb43850: r0 = false
    //     0xb43850: add             x0, NULL, #0x30  ; false
    // 0xb43854: ArrayStore: r3[0] = r0  ; List_4
    //     0xb43854: stur            w0, [x3, #0x17]
    // 0xb43858: StoreField: r3->field_1b = r0
    //     0xb43858: stur            w0, [x3, #0x1b]
    // 0xb4385c: StoreField: r3->field_1f = r0
    //     0xb4385c: stur            w0, [x3, #0x1f]
    // 0xb43860: StoreField: r3->field_23 = r0
    //     0xb43860: stur            w0, [x3, #0x23]
    // 0xb43864: StoreField: r3->field_27 = r0
    //     0xb43864: stur            w0, [x3, #0x27]
    // 0xb43868: StoreField: r3->field_2b = r0
    //     0xb43868: stur            w0, [x3, #0x2b]
    // 0xb4386c: r1 = Null
    //     0xb4386c: mov             x1, NULL
    // 0xb43870: r2 = 6
    //     0xb43870: movz            x2, #0x6
    // 0xb43874: r0 = AllocateArray()
    //     0xb43874: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb43878: mov             x2, x0
    // 0xb4387c: ldur            x0, [fp, #-0x28]
    // 0xb43880: stur            x2, [fp, #-8]
    // 0xb43884: StoreField: r2->field_f = r0
    //     0xb43884: stur            w0, [x2, #0xf]
    // 0xb43888: ldur            x0, [fp, #-0x20]
    // 0xb4388c: StoreField: r2->field_13 = r0
    //     0xb4388c: stur            w0, [x2, #0x13]
    // 0xb43890: ldur            x0, [fp, #-0x10]
    // 0xb43894: ArrayStore: r2[0] = r0  ; List_4
    //     0xb43894: stur            w0, [x2, #0x17]
    // 0xb43898: r1 = <Widget>
    //     0xb43898: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4389c: r0 = AllocateGrowableArray()
    //     0xb4389c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb438a0: mov             x1, x0
    // 0xb438a4: ldur            x0, [fp, #-8]
    // 0xb438a8: stur            x1, [fp, #-0x10]
    // 0xb438ac: StoreField: r1->field_f = r0
    //     0xb438ac: stur            w0, [x1, #0xf]
    // 0xb438b0: r0 = 6
    //     0xb438b0: movz            x0, #0x6
    // 0xb438b4: StoreField: r1->field_b = r0
    //     0xb438b4: stur            w0, [x1, #0xb]
    // 0xb438b8: r0 = Column()
    //     0xb438b8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb438bc: mov             x1, x0
    // 0xb438c0: r0 = Instance_Axis
    //     0xb438c0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb438c4: stur            x1, [fp, #-8]
    // 0xb438c8: StoreField: r1->field_f = r0
    //     0xb438c8: stur            w0, [x1, #0xf]
    // 0xb438cc: r0 = Instance_MainAxisAlignment
    //     0xb438cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb438d0: ldr             x0, [x0, #0xa08]
    // 0xb438d4: StoreField: r1->field_13 = r0
    //     0xb438d4: stur            w0, [x1, #0x13]
    // 0xb438d8: r0 = Instance_MainAxisSize
    //     0xb438d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb438dc: ldr             x0, [x0, #0xa10]
    // 0xb438e0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb438e0: stur            w0, [x1, #0x17]
    // 0xb438e4: r0 = Instance_CrossAxisAlignment
    //     0xb438e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb438e8: ldr             x0, [x0, #0x890]
    // 0xb438ec: StoreField: r1->field_1b = r0
    //     0xb438ec: stur            w0, [x1, #0x1b]
    // 0xb438f0: r0 = Instance_VerticalDirection
    //     0xb438f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb438f4: ldr             x0, [x0, #0xa20]
    // 0xb438f8: StoreField: r1->field_23 = r0
    //     0xb438f8: stur            w0, [x1, #0x23]
    // 0xb438fc: r0 = Instance_Clip
    //     0xb438fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb43900: ldr             x0, [x0, #0x38]
    // 0xb43904: StoreField: r1->field_2b = r0
    //     0xb43904: stur            w0, [x1, #0x2b]
    // 0xb43908: StoreField: r1->field_2f = rZR
    //     0xb43908: stur            xzr, [x1, #0x2f]
    // 0xb4390c: ldur            x0, [fp, #-0x10]
    // 0xb43910: StoreField: r1->field_b = r0
    //     0xb43910: stur            w0, [x1, #0xb]
    // 0xb43914: r0 = Padding()
    //     0xb43914: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb43918: r1 = Instance_EdgeInsets
    //     0xb43918: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4391c: ldr             x1, [x1, #0x1f0]
    // 0xb43920: StoreField: r0->field_f = r1
    //     0xb43920: stur            w1, [x0, #0xf]
    // 0xb43924: ldur            x1, [fp, #-8]
    // 0xb43928: StoreField: r0->field_b = r1
    //     0xb43928: stur            w1, [x0, #0xb]
    // 0xb4392c: LeaveFrame
    //     0xb4392c: mov             SP, fp
    //     0xb43930: ldp             fp, lr, [SP], #0x10
    // 0xb43934: ret
    //     0xb43934: ret             
    // 0xb43938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43938: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4393c: b               #0xb40b14
    // 0xb43940: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43940: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43944: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43944: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb43948: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb4394c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4394c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43950: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb43950: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb43954: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43954: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43958: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb43958: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb4395c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4395c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43960: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43960: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43964: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43964: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43968: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43968: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4396c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4396c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43970: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43970: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43974: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43978: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43978: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4397c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4397c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb43980: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43980: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb43984, size: 0x78
    // 0xb43984: EnterFrame
    //     0xb43984: stp             fp, lr, [SP, #-0x10]!
    //     0xb43988: mov             fp, SP
    // 0xb4398c: AllocStack(0x10)
    //     0xb4398c: sub             SP, SP, #0x10
    // 0xb43990: SetupParameters()
    //     0xb43990: ldr             x0, [fp, #0x18]
    //     0xb43994: ldur            w3, [x0, #0x17]
    //     0xb43998: add             x3, x3, HEAP, lsl #32
    //     0xb4399c: stur            x3, [fp, #-0x10]
    // 0xb439a0: CheckStackOverflow
    //     0xb439a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb439a4: cmp             SP, x16
    //     0xb439a8: b.ls            #0xb439f4
    // 0xb439ac: LoadField: r0 = r3->field_f
    //     0xb439ac: ldur            w0, [x3, #0xf]
    // 0xb439b0: DecompressPointer r0
    //     0xb439b0: add             x0, x0, HEAP, lsl #32
    // 0xb439b4: mov             x2, x3
    // 0xb439b8: stur            x0, [fp, #-8]
    // 0xb439bc: r1 = Function '<anonymous closure>':.
    //     0xb439bc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56898] AnonymousClosure: (0xa05258), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb439c0: ldr             x1, [x1, #0x898]
    // 0xb439c4: r0 = AllocateClosure()
    //     0xb439c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb439c8: ldur            x1, [fp, #-8]
    // 0xb439cc: mov             x2, x0
    // 0xb439d0: r0 = setState()
    //     0xb439d0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb439d4: ldur            x0, [fp, #-0x10]
    // 0xb439d8: LoadField: r1 = r0->field_f
    //     0xb439d8: ldur            w1, [x0, #0xf]
    // 0xb439dc: DecompressPointer r1
    //     0xb439dc: add             x1, x1, HEAP, lsl #32
    // 0xb439e0: r0 = _validateAllFields()
    //     0xb439e0: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xb439e4: r0 = Null
    //     0xb439e4: mov             x0, NULL
    // 0xb439e8: LeaveFrame
    //     0xb439e8: mov             SP, fp
    //     0xb439ec: ldp             fp, lr, [SP], #0x10
    // 0xb439f0: ret
    //     0xb439f0: ret             
    // 0xb439f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb439f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb439f8: b               #0xb439ac
  }
  [closure] String? _validateAlternateNo(dynamic, String?) {
    // ** addr: 0xb439fc, size: 0x3c
    // 0xb439fc: EnterFrame
    //     0xb439fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb43a00: mov             fp, SP
    // 0xb43a04: ldr             x0, [fp, #0x18]
    // 0xb43a08: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb43a08: ldur            w1, [x0, #0x17]
    // 0xb43a0c: DecompressPointer r1
    //     0xb43a0c: add             x1, x1, HEAP, lsl #32
    // 0xb43a10: CheckStackOverflow
    //     0xb43a10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43a14: cmp             SP, x16
    //     0xb43a18: b.ls            #0xb43a30
    // 0xb43a1c: ldr             x2, [fp, #0x10]
    // 0xb43a20: r0 = _validateAlternateNo()
    //     0xb43a20: bl              #0xa05330  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAlternateNo
    // 0xb43a24: LeaveFrame
    //     0xb43a24: mov             SP, fp
    //     0xb43a28: ldp             fp, lr, [SP], #0x10
    // 0xb43a2c: ret
    //     0xb43a2c: ret             
    // 0xb43a30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43a30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43a34: b               #0xb43a1c
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb43a38, size: 0x78
    // 0xb43a38: EnterFrame
    //     0xb43a38: stp             fp, lr, [SP, #-0x10]!
    //     0xb43a3c: mov             fp, SP
    // 0xb43a40: AllocStack(0x10)
    //     0xb43a40: sub             SP, SP, #0x10
    // 0xb43a44: SetupParameters()
    //     0xb43a44: ldr             x0, [fp, #0x18]
    //     0xb43a48: ldur            w3, [x0, #0x17]
    //     0xb43a4c: add             x3, x3, HEAP, lsl #32
    //     0xb43a50: stur            x3, [fp, #-0x10]
    // 0xb43a54: CheckStackOverflow
    //     0xb43a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43a58: cmp             SP, x16
    //     0xb43a5c: b.ls            #0xb43aa8
    // 0xb43a60: LoadField: r0 = r3->field_f
    //     0xb43a60: ldur            w0, [x3, #0xf]
    // 0xb43a64: DecompressPointer r0
    //     0xb43a64: add             x0, x0, HEAP, lsl #32
    // 0xb43a68: mov             x2, x3
    // 0xb43a6c: stur            x0, [fp, #-8]
    // 0xb43a70: r1 = Function '<anonymous closure>':.
    //     0xb43a70: add             x1, PP, #0x56, lsl #12  ; [pp+0x568e0] AnonymousClosure: (0xa01a18), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb43a74: ldr             x1, [x1, #0x8e0]
    // 0xb43a78: r0 = AllocateClosure()
    //     0xb43a78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb43a7c: ldur            x1, [fp, #-8]
    // 0xb43a80: mov             x2, x0
    // 0xb43a84: r0 = setState()
    //     0xb43a84: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb43a88: ldur            x0, [fp, #-0x10]
    // 0xb43a8c: LoadField: r1 = r0->field_f
    //     0xb43a8c: ldur            w1, [x0, #0xf]
    // 0xb43a90: DecompressPointer r1
    //     0xb43a90: add             x1, x1, HEAP, lsl #32
    // 0xb43a94: r0 = _validateAllFields()
    //     0xb43a94: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xb43a98: r0 = Null
    //     0xb43a98: mov             x0, NULL
    // 0xb43a9c: LeaveFrame
    //     0xb43a9c: mov             SP, fp
    //     0xb43aa0: ldp             fp, lr, [SP], #0x10
    // 0xb43aa4: ret
    //     0xb43aa4: ret             
    // 0xb43aa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43aa8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43aac: b               #0xb43a60
  }
  [closure] String? _validateLandmark(dynamic, String?) {
    // ** addr: 0xb43ab0, size: 0x3c
    // 0xb43ab0: EnterFrame
    //     0xb43ab0: stp             fp, lr, [SP, #-0x10]!
    //     0xb43ab4: mov             fp, SP
    // 0xb43ab8: ldr             x0, [fp, #0x18]
    // 0xb43abc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb43abc: ldur            w1, [x0, #0x17]
    // 0xb43ac0: DecompressPointer r1
    //     0xb43ac0: add             x1, x1, HEAP, lsl #32
    // 0xb43ac4: CheckStackOverflow
    //     0xb43ac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43ac8: cmp             SP, x16
    //     0xb43acc: b.ls            #0xb43ae4
    // 0xb43ad0: ldr             x2, [fp, #0x10]
    // 0xb43ad4: r0 = _validateLandmark()
    //     0xb43ad4: bl              #0xa054c0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateLandmark
    // 0xb43ad8: LeaveFrame
    //     0xb43ad8: mov             SP, fp
    //     0xb43adc: ldp             fp, lr, [SP], #0x10
    // 0xb43ae0: ret
    //     0xb43ae0: ret             
    // 0xb43ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43ae4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43ae8: b               #0xb43ad0
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb43aec, size: 0x100
    // 0xb43aec: EnterFrame
    //     0xb43aec: stp             fp, lr, [SP, #-0x10]!
    //     0xb43af0: mov             fp, SP
    // 0xb43af4: AllocStack(0x20)
    //     0xb43af4: sub             SP, SP, #0x20
    // 0xb43af8: SetupParameters()
    //     0xb43af8: ldr             x0, [fp, #0x18]
    //     0xb43afc: ldur            w3, [x0, #0x17]
    //     0xb43b00: add             x3, x3, HEAP, lsl #32
    //     0xb43b04: stur            x3, [fp, #-0x10]
    // 0xb43b08: CheckStackOverflow
    //     0xb43b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43b0c: cmp             SP, x16
    //     0xb43b10: b.ls            #0xb43be0
    // 0xb43b14: LoadField: r0 = r3->field_f
    //     0xb43b14: ldur            w0, [x3, #0xf]
    // 0xb43b18: DecompressPointer r0
    //     0xb43b18: add             x0, x0, HEAP, lsl #32
    // 0xb43b1c: mov             x2, x3
    // 0xb43b20: stur            x0, [fp, #-8]
    // 0xb43b24: r1 = Function '<anonymous closure>':.
    //     0xb43b24: add             x1, PP, #0x56, lsl #12  ; [pp+0x568e8] AnonymousClosure: (0xa01cc4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb43b28: ldr             x1, [x1, #0x8e8]
    // 0xb43b2c: r0 = AllocateClosure()
    //     0xb43b2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb43b30: ldur            x1, [fp, #-8]
    // 0xb43b34: mov             x2, x0
    // 0xb43b38: r0 = setState()
    //     0xb43b38: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb43b3c: ldr             x0, [fp, #0x10]
    // 0xb43b40: cmp             w0, NULL
    // 0xb43b44: b.ne            #0xb43b50
    // 0xb43b48: r1 = Null
    //     0xb43b48: mov             x1, NULL
    // 0xb43b4c: b               #0xb43b54
    // 0xb43b50: LoadField: r1 = r0->field_7
    //     0xb43b50: ldur            w1, [x0, #7]
    // 0xb43b54: cmp             w1, NULL
    // 0xb43b58: b.ne            #0xb43b64
    // 0xb43b5c: r1 = 0
    //     0xb43b5c: movz            x1, #0
    // 0xb43b60: b               #0xb43b6c
    // 0xb43b64: r2 = LoadInt32Instr(r1)
    //     0xb43b64: sbfx            x2, x1, #1, #0x1f
    // 0xb43b68: mov             x1, x2
    // 0xb43b6c: cmp             x1, #5
    // 0xb43b70: b.le            #0xb43bc0
    // 0xb43b74: ldur            x1, [fp, #-0x10]
    // 0xb43b78: LoadField: r2 = r1->field_f
    //     0xb43b78: ldur            w2, [x1, #0xf]
    // 0xb43b7c: DecompressPointer r2
    //     0xb43b7c: add             x2, x2, HEAP, lsl #32
    // 0xb43b80: LoadField: r3 = r2->field_b
    //     0xb43b80: ldur            w3, [x2, #0xb]
    // 0xb43b84: DecompressPointer r3
    //     0xb43b84: add             x3, x3, HEAP, lsl #32
    // 0xb43b88: cmp             w3, NULL
    // 0xb43b8c: b.eq            #0xb43be8
    // 0xb43b90: cmp             w0, NULL
    // 0xb43b94: b.ne            #0xb43b9c
    // 0xb43b98: r0 = ""
    //     0xb43b98: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb43b9c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb43b9c: ldur            w2, [x3, #0x17]
    // 0xb43ba0: DecompressPointer r2
    //     0xb43ba0: add             x2, x2, HEAP, lsl #32
    // 0xb43ba4: stp             x0, x2, [SP]
    // 0xb43ba8: r4 = 0
    //     0xb43ba8: movz            x4, #0
    // 0xb43bac: ldr             x0, [SP, #8]
    // 0xb43bb0: r16 = UnlinkedCall_0x613b5c
    //     0xb43bb0: add             x16, PP, #0x56, lsl #12  ; [pp+0x568f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb43bb4: add             x16, x16, #0x8f0
    // 0xb43bb8: ldp             x5, lr, [x16]
    // 0xb43bbc: blr             lr
    // 0xb43bc0: ldur            x0, [fp, #-0x10]
    // 0xb43bc4: LoadField: r1 = r0->field_f
    //     0xb43bc4: ldur            w1, [x0, #0xf]
    // 0xb43bc8: DecompressPointer r1
    //     0xb43bc8: add             x1, x1, HEAP, lsl #32
    // 0xb43bcc: r0 = _validateAllFields()
    //     0xb43bcc: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xb43bd0: r0 = Null
    //     0xb43bd0: mov             x0, NULL
    // 0xb43bd4: LeaveFrame
    //     0xb43bd4: mov             SP, fp
    //     0xb43bd8: ldp             fp, lr, [SP], #0x10
    // 0xb43bdc: ret
    //     0xb43bdc: ret             
    // 0xb43be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43be0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43be4: b               #0xb43b14
    // 0xb43be8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb43be8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] String? _validatePinCode(dynamic, String?) {
    // ** addr: 0xb43bec, size: 0x3c
    // 0xb43bec: EnterFrame
    //     0xb43bec: stp             fp, lr, [SP, #-0x10]!
    //     0xb43bf0: mov             fp, SP
    // 0xb43bf4: ldr             x0, [fp, #0x18]
    // 0xb43bf8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb43bf8: ldur            w1, [x0, #0x17]
    // 0xb43bfc: DecompressPointer r1
    //     0xb43bfc: add             x1, x1, HEAP, lsl #32
    // 0xb43c00: CheckStackOverflow
    //     0xb43c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43c04: cmp             SP, x16
    //     0xb43c08: b.ls            #0xb43c20
    // 0xb43c0c: ldr             x2, [fp, #0x10]
    // 0xb43c10: r0 = _validatePinCode()
    //     0xb43c10: bl              #0xa056a0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validatePinCode
    // 0xb43c14: LeaveFrame
    //     0xb43c14: mov             SP, fp
    //     0xb43c18: ldp             fp, lr, [SP], #0x10
    // 0xb43c1c: ret
    //     0xb43c1c: ret             
    // 0xb43c20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43c20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43c24: b               #0xb43c0c
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb43c28, size: 0x78
    // 0xb43c28: EnterFrame
    //     0xb43c28: stp             fp, lr, [SP, #-0x10]!
    //     0xb43c2c: mov             fp, SP
    // 0xb43c30: AllocStack(0x10)
    //     0xb43c30: sub             SP, SP, #0x10
    // 0xb43c34: SetupParameters()
    //     0xb43c34: ldr             x0, [fp, #0x18]
    //     0xb43c38: ldur            w3, [x0, #0x17]
    //     0xb43c3c: add             x3, x3, HEAP, lsl #32
    //     0xb43c40: stur            x3, [fp, #-0x10]
    // 0xb43c44: CheckStackOverflow
    //     0xb43c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43c48: cmp             SP, x16
    //     0xb43c4c: b.ls            #0xb43c98
    // 0xb43c50: LoadField: r0 = r3->field_f
    //     0xb43c50: ldur            w0, [x3, #0xf]
    // 0xb43c54: DecompressPointer r0
    //     0xb43c54: add             x0, x0, HEAP, lsl #32
    // 0xb43c58: mov             x2, x3
    // 0xb43c5c: stur            x0, [fp, #-8]
    // 0xb43c60: r1 = Function '<anonymous closure>':.
    //     0xb43c60: add             x1, PP, #0x56, lsl #12  ; [pp+0x56900] AnonymousClosure: (0xa0202c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb43c64: ldr             x1, [x1, #0x900]
    // 0xb43c68: r0 = AllocateClosure()
    //     0xb43c68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb43c6c: ldur            x1, [fp, #-8]
    // 0xb43c70: mov             x2, x0
    // 0xb43c74: r0 = setState()
    //     0xb43c74: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb43c78: ldur            x0, [fp, #-0x10]
    // 0xb43c7c: LoadField: r1 = r0->field_f
    //     0xb43c7c: ldur            w1, [x0, #0xf]
    // 0xb43c80: DecompressPointer r1
    //     0xb43c80: add             x1, x1, HEAP, lsl #32
    // 0xb43c84: r0 = _validateAllFields()
    //     0xb43c84: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xb43c88: r0 = Null
    //     0xb43c88: mov             x0, NULL
    // 0xb43c8c: LeaveFrame
    //     0xb43c8c: mov             SP, fp
    //     0xb43c90: ldp             fp, lr, [SP], #0x10
    // 0xb43c94: ret
    //     0xb43c94: ret             
    // 0xb43c98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43c98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43c9c: b               #0xb43c50
  }
  [closure] String? _validateAddress(dynamic, String?) {
    // ** addr: 0xb43ca0, size: 0x3c
    // 0xb43ca0: EnterFrame
    //     0xb43ca0: stp             fp, lr, [SP, #-0x10]!
    //     0xb43ca4: mov             fp, SP
    // 0xb43ca8: ldr             x0, [fp, #0x18]
    // 0xb43cac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb43cac: ldur            w1, [x0, #0x17]
    // 0xb43cb0: DecompressPointer r1
    //     0xb43cb0: add             x1, x1, HEAP, lsl #32
    // 0xb43cb4: CheckStackOverflow
    //     0xb43cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43cb8: cmp             SP, x16
    //     0xb43cbc: b.ls            #0xb43cd4
    // 0xb43cc0: ldr             x2, [fp, #0x10]
    // 0xb43cc4: r0 = _validateAddress()
    //     0xb43cc4: bl              #0xa023b0  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/change_address_bottom_sheet.dart] _ChangeAddressBottomSheetState::_validateAddress
    // 0xb43cc8: LeaveFrame
    //     0xb43cc8: mov             SP, fp
    //     0xb43ccc: ldp             fp, lr, [SP], #0x10
    // 0xb43cd0: ret
    //     0xb43cd0: ret             
    // 0xb43cd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43cd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43cd8: b               #0xb43cc0
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb43cdc, size: 0x78
    // 0xb43cdc: EnterFrame
    //     0xb43cdc: stp             fp, lr, [SP, #-0x10]!
    //     0xb43ce0: mov             fp, SP
    // 0xb43ce4: AllocStack(0x10)
    //     0xb43ce4: sub             SP, SP, #0x10
    // 0xb43ce8: SetupParameters()
    //     0xb43ce8: ldr             x0, [fp, #0x18]
    //     0xb43cec: ldur            w3, [x0, #0x17]
    //     0xb43cf0: add             x3, x3, HEAP, lsl #32
    //     0xb43cf4: stur            x3, [fp, #-0x10]
    // 0xb43cf8: CheckStackOverflow
    //     0xb43cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43cfc: cmp             SP, x16
    //     0xb43d00: b.ls            #0xb43d4c
    // 0xb43d04: LoadField: r0 = r3->field_f
    //     0xb43d04: ldur            w0, [x3, #0xf]
    // 0xb43d08: DecompressPointer r0
    //     0xb43d08: add             x0, x0, HEAP, lsl #32
    // 0xb43d0c: mov             x2, x3
    // 0xb43d10: stur            x0, [fp, #-8]
    // 0xb43d14: r1 = Function '<anonymous closure>':.
    //     0xb43d14: add             x1, PP, #0x56, lsl #12  ; [pp+0x56908] AnonymousClosure: (0xa022d8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb43d18: ldr             x1, [x1, #0x908]
    // 0xb43d1c: r0 = AllocateClosure()
    //     0xb43d1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb43d20: ldur            x1, [fp, #-8]
    // 0xb43d24: mov             x2, x0
    // 0xb43d28: r0 = setState()
    //     0xb43d28: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb43d2c: ldur            x0, [fp, #-0x10]
    // 0xb43d30: LoadField: r1 = r0->field_f
    //     0xb43d30: ldur            w1, [x0, #0xf]
    // 0xb43d34: DecompressPointer r1
    //     0xb43d34: add             x1, x1, HEAP, lsl #32
    // 0xb43d38: r0 = _validateAllFields()
    //     0xb43d38: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xb43d3c: r0 = Null
    //     0xb43d3c: mov             x0, NULL
    // 0xb43d40: LeaveFrame
    //     0xb43d40: mov             SP, fp
    //     0xb43d44: ldp             fp, lr, [SP], #0x10
    // 0xb43d48: ret
    //     0xb43d48: ret             
    // 0xb43d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43d4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43d50: b               #0xb43d04
  }
  [closure] String? _validateHouseNumber(dynamic, String?) {
    // ** addr: 0xb43d54, size: 0x3c
    // 0xb43d54: EnterFrame
    //     0xb43d54: stp             fp, lr, [SP, #-0x10]!
    //     0xb43d58: mov             fp, SP
    // 0xb43d5c: ldr             x0, [fp, #0x18]
    // 0xb43d60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb43d60: ldur            w1, [x0, #0x17]
    // 0xb43d64: DecompressPointer r1
    //     0xb43d64: add             x1, x1, HEAP, lsl #32
    // 0xb43d68: CheckStackOverflow
    //     0xb43d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43d6c: cmp             SP, x16
    //     0xb43d70: b.ls            #0xb43d88
    // 0xb43d74: ldr             x2, [fp, #0x10]
    // 0xb43d78: r0 = _validateHouseNumber()
    //     0xb43d78: bl              #0xa0594c  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateHouseNumber
    // 0xb43d7c: LeaveFrame
    //     0xb43d7c: mov             SP, fp
    //     0xb43d80: ldp             fp, lr, [SP], #0x10
    // 0xb43d84: ret
    //     0xb43d84: ret             
    // 0xb43d88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43d88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43d8c: b               #0xb43d74
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb43d90, size: 0x78
    // 0xb43d90: EnterFrame
    //     0xb43d90: stp             fp, lr, [SP, #-0x10]!
    //     0xb43d94: mov             fp, SP
    // 0xb43d98: AllocStack(0x10)
    //     0xb43d98: sub             SP, SP, #0x10
    // 0xb43d9c: SetupParameters()
    //     0xb43d9c: ldr             x0, [fp, #0x18]
    //     0xb43da0: ldur            w3, [x0, #0x17]
    //     0xb43da4: add             x3, x3, HEAP, lsl #32
    //     0xb43da8: stur            x3, [fp, #-0x10]
    // 0xb43dac: CheckStackOverflow
    //     0xb43dac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43db0: cmp             SP, x16
    //     0xb43db4: b.ls            #0xb43e00
    // 0xb43db8: LoadField: r0 = r3->field_f
    //     0xb43db8: ldur            w0, [x3, #0xf]
    // 0xb43dbc: DecompressPointer r0
    //     0xb43dbc: add             x0, x0, HEAP, lsl #32
    // 0xb43dc0: mov             x2, x3
    // 0xb43dc4: stur            x0, [fp, #-8]
    // 0xb43dc8: r1 = Function '<anonymous closure>':.
    //     0xb43dc8: add             x1, PP, #0x56, lsl #12  ; [pp+0x56910] AnonymousClosure: (0xa024dc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::build (0xbb1138)
    //     0xb43dcc: ldr             x1, [x1, #0x910]
    // 0xb43dd0: r0 = AllocateClosure()
    //     0xb43dd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb43dd4: ldur            x1, [fp, #-8]
    // 0xb43dd8: mov             x2, x0
    // 0xb43ddc: r0 = setState()
    //     0xb43ddc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb43de0: ldur            x0, [fp, #-0x10]
    // 0xb43de4: LoadField: r1 = r0->field_f
    //     0xb43de4: ldur            w1, [x0, #0xf]
    // 0xb43de8: DecompressPointer r1
    //     0xb43de8: add             x1, x1, HEAP, lsl #32
    // 0xb43dec: r0 = _validateAllFields()
    //     0xb43dec: bl              #0x93fd1c  ; [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateAllFields
    // 0xb43df0: r0 = Null
    //     0xb43df0: mov             x0, NULL
    // 0xb43df4: LeaveFrame
    //     0xb43df4: mov             SP, fp
    //     0xb43df8: ldp             fp, lr, [SP], #0x10
    // 0xb43dfc: ret
    //     0xb43dfc: ret             
    // 0xb43e00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43e00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43e04: b               #0xb43db8
  }
  [closure] String? _validateCustomerNameNumber(dynamic, String?) {
    // ** addr: 0xb43e08, size: 0x3c
    // 0xb43e08: EnterFrame
    //     0xb43e08: stp             fp, lr, [SP, #-0x10]!
    //     0xb43e0c: mov             fp, SP
    // 0xb43e10: ldr             x0, [fp, #0x18]
    // 0xb43e14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb43e14: ldur            w1, [x0, #0x17]
    // 0xb43e18: DecompressPointer r1
    //     0xb43e18: add             x1, x1, HEAP, lsl #32
    // 0xb43e1c: CheckStackOverflow
    //     0xb43e1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43e20: cmp             SP, x16
    //     0xb43e24: b.ls            #0xb43e3c
    // 0xb43e28: ldr             x2, [fp, #0x10]
    // 0xb43e2c: r0 = _validateCustomerNameNumber()
    //     0xb43e2c: bl              #0xa05a54  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_validateCustomerNameNumber
    // 0xb43e30: LeaveFrame
    //     0xb43e30: mov             SP, fp
    //     0xb43e34: ldp             fp, lr, [SP], #0x10
    // 0xb43e38: ret
    //     0xb43e38: ret             
    // 0xb43e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb43e3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb43e40: b               #0xb43e28
  }
}

// class id: 4107, size: 0x24, field offset: 0xc
class CheckoutAddressWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e924, size: 0x48
    // 0xc7e924: EnterFrame
    //     0xc7e924: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e928: mov             fp, SP
    // 0xc7e92c: AllocStack(0x8)
    //     0xc7e92c: sub             SP, SP, #8
    // 0xc7e930: CheckStackOverflow
    //     0xc7e930: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e934: cmp             SP, x16
    //     0xc7e938: b.ls            #0xc7e964
    // 0xc7e93c: r1 = <CheckoutAddressWidget>
    //     0xc7e93c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a10] TypeArguments: <CheckoutAddressWidget>
    //     0xc7e940: ldr             x1, [x1, #0xa10]
    // 0xc7e944: r0 = CheckoutAddressWidgetState()
    //     0xc7e944: bl              #0xc7e96c  ; AllocateCheckoutAddressWidgetStateStub -> CheckoutAddressWidgetState (size=0x6c)
    // 0xc7e948: mov             x1, x0
    // 0xc7e94c: stur            x0, [fp, #-8]
    // 0xc7e950: r0 = CheckoutAddressWidgetState()
    //     0xc7e950: bl              #0xc7a884  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::CheckoutAddressWidgetState
    // 0xc7e954: ldur            x0, [fp, #-8]
    // 0xc7e958: LeaveFrame
    //     0xc7e958: mov             SP, fp
    //     0xc7e95c: ldp             fp, lr, [SP], #0x10
    // 0xc7e960: ret
    //     0xc7e960: ret             
    // 0xc7e964: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e964: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e968: b               #0xc7e93c
  }
}
