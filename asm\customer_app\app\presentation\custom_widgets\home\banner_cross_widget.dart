// lib: , url: package:customer_app/app/presentation/custom_widgets/home/<USER>

// class id: 1049065, size: 0x8
class :: {
}

// class id: 3593, size: 0x20, field offset: 0x14
class _BannerCrossWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x98d1d8, size: 0x380
    // 0x98d1d8: EnterFrame
    //     0x98d1d8: stp             fp, lr, [SP, #-0x10]!
    //     0x98d1dc: mov             fp, SP
    // 0x98d1e0: AllocStack(0x48)
    //     0x98d1e0: sub             SP, SP, #0x48
    // 0x98d1e4: SetupParameters(_BannerCrossWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x98d1e4: stur            x1, [fp, #-8]
    // 0x98d1e8: CheckStackOverflow
    //     0x98d1e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98d1ec: cmp             SP, x16
    //     0x98d1f0: b.ls            #0x98d54c
    // 0x98d1f4: r1 = 1
    //     0x98d1f4: movz            x1, #0x1
    // 0x98d1f8: r0 = AllocateContext()
    //     0x98d1f8: bl              #0x16f6108  ; AllocateContextStub
    // 0x98d1fc: mov             x1, x0
    // 0x98d200: ldur            x0, [fp, #-8]
    // 0x98d204: stur            x1, [fp, #-0x28]
    // 0x98d208: StoreField: r1->field_f = r0
    //     0x98d208: stur            w0, [x1, #0xf]
    // 0x98d20c: LoadField: r2 = r0->field_1b
    //     0x98d20c: ldur            w2, [x0, #0x1b]
    // 0x98d210: DecompressPointer r2
    //     0x98d210: add             x2, x2, HEAP, lsl #32
    // 0x98d214: stur            x2, [fp, #-0x20]
    // 0x98d218: LoadField: r3 = r0->field_b
    //     0x98d218: ldur            w3, [x0, #0xb]
    // 0x98d21c: DecompressPointer r3
    //     0x98d21c: add             x3, x3, HEAP, lsl #32
    // 0x98d220: cmp             w3, NULL
    // 0x98d224: b.eq            #0x98d554
    // 0x98d228: LoadField: r0 = r3->field_27
    //     0x98d228: ldur            w0, [x3, #0x27]
    // 0x98d22c: DecompressPointer r0
    //     0x98d22c: add             x0, x0, HEAP, lsl #32
    // 0x98d230: stur            x0, [fp, #-0x18]
    // 0x98d234: LoadField: r3 = r0->field_b
    //     0x98d234: ldur            w3, [x0, #0xb]
    // 0x98d238: r4 = LoadInt32Instr(r3)
    //     0x98d238: sbfx            x4, x3, #1, #0x1f
    // 0x98d23c: stur            x4, [fp, #-0x10]
    // 0x98d240: cmp             x4, #1
    // 0x98d244: r16 = true
    //     0x98d244: add             x16, NULL, #0x20  ; true
    // 0x98d248: r17 = false
    //     0x98d248: add             x17, NULL, #0x30  ; false
    // 0x98d24c: csel            x3, x16, x17, gt
    // 0x98d250: stur            x3, [fp, #-8]
    // 0x98d254: r0 = CarouselOptions()
    //     0x98d254: bl              #0x98d588  ; AllocateCarouselOptionsStub -> CarouselOptions (size=0x7c)
    // 0x98d258: d0 = 200.000000
    //     0x98d258: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0x98d25c: ldr             d0, [x17, #0x360]
    // 0x98d260: stur            x0, [fp, #-0x30]
    // 0x98d264: StoreField: r0->field_7 = d0
    //     0x98d264: stur            d0, [x0, #7]
    // 0x98d268: d0 = 1.000000
    //     0x98d268: fmov            d0, #1.00000000
    // 0x98d26c: StoreField: r0->field_f = d0
    //     0x98d26c: stur            d0, [x0, #0xf]
    // 0x98d270: ArrayStore: r0[0] = rZR  ; List_8
    //     0x98d270: stur            xzr, [x0, #0x17]
    // 0x98d274: ldur            x1, [fp, #-8]
    // 0x98d278: StoreField: r0->field_1f = r1
    //     0x98d278: stur            w1, [x0, #0x1f]
    // 0x98d27c: r3 = true
    //     0x98d27c: add             x3, NULL, #0x20  ; true
    // 0x98d280: StoreField: r0->field_23 = r3
    //     0x98d280: stur            w3, [x0, #0x23]
    // 0x98d284: r4 = false
    //     0x98d284: add             x4, NULL, #0x30  ; false
    // 0x98d288: StoreField: r0->field_27 = r4
    //     0x98d288: stur            w4, [x0, #0x27]
    // 0x98d28c: StoreField: r0->field_2b = r3
    //     0x98d28c: stur            w3, [x0, #0x2b]
    // 0x98d290: r1 = Instance_Duration
    //     0x98d290: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0x98d294: ldr             x1, [x1, #0xbd8]
    // 0x98d298: StoreField: r0->field_2f = r1
    //     0x98d298: stur            w1, [x0, #0x2f]
    // 0x98d29c: r1 = Instance_Duration
    //     0x98d29c: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b758] Obj!Duration@d77731
    //     0x98d2a0: ldr             x1, [x1, #0x758]
    // 0x98d2a4: StoreField: r0->field_33 = r1
    //     0x98d2a4: stur            w1, [x0, #0x33]
    // 0x98d2a8: r1 = Instance_Cubic
    //     0x98d2a8: ldr             x1, [PP, #0x6d28]  ; [pp+0x6d28] Obj!Cubic@d5b531
    // 0x98d2ac: StoreField: r0->field_37 = r1
    //     0x98d2ac: stur            w1, [x0, #0x37]
    // 0x98d2b0: StoreField: r0->field_3b = r4
    //     0x98d2b0: stur            w4, [x0, #0x3b]
    // 0x98d2b4: ldur            x2, [fp, #-0x28]
    // 0x98d2b8: r1 = Function '<anonymous closure>':.
    //     0x98d2b8: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b948] AnonymousClosure: (0x98e098), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98d2bc: ldr             x1, [x1, #0x948]
    // 0x98d2c0: r0 = AllocateClosure()
    //     0x98d2c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98d2c4: mov             x1, x0
    // 0x98d2c8: ldur            x0, [fp, #-0x30]
    // 0x98d2cc: StoreField: r0->field_43 = r1
    //     0x98d2cc: stur            w1, [x0, #0x43]
    // 0x98d2d0: r1 = true
    //     0x98d2d0: add             x1, NULL, #0x20  ; true
    // 0x98d2d4: StoreField: r0->field_4f = r1
    //     0x98d2d4: stur            w1, [x0, #0x4f]
    // 0x98d2d8: r2 = Instance_Axis
    //     0x98d2d8: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x98d2dc: StoreField: r0->field_3f = r2
    //     0x98d2dc: stur            w2, [x0, #0x3f]
    // 0x98d2e0: StoreField: r0->field_53 = r1
    //     0x98d2e0: stur            w1, [x0, #0x53]
    // 0x98d2e4: StoreField: r0->field_57 = r1
    //     0x98d2e4: stur            w1, [x0, #0x57]
    // 0x98d2e8: r3 = false
    //     0x98d2e8: add             x3, NULL, #0x30  ; false
    // 0x98d2ec: StoreField: r0->field_5b = r3
    //     0x98d2ec: stur            w3, [x0, #0x5b]
    // 0x98d2f0: r4 = Instance_CenterPageEnlargeStrategy
    //     0x98d2f0: add             x4, PP, #0x6b, lsl #12  ; [pp+0x6b768] Obj!CenterPageEnlargeStrategy@d75901
    //     0x98d2f4: ldr             x4, [x4, #0x768]
    // 0x98d2f8: StoreField: r0->field_63 = r4
    //     0x98d2f8: stur            w4, [x0, #0x63]
    // 0x98d2fc: d0 = 0.300000
    //     0x98d2fc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x98d300: ldr             d0, [x17, #0x658]
    // 0x98d304: StoreField: r0->field_67 = d0
    //     0x98d304: stur            d0, [x0, #0x67]
    // 0x98d308: StoreField: r0->field_6f = r3
    //     0x98d308: stur            w3, [x0, #0x6f]
    // 0x98d30c: StoreField: r0->field_73 = r1
    //     0x98d30c: stur            w1, [x0, #0x73]
    // 0x98d310: r1 = Instance_Clip
    //     0x98d310: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x98d314: ldr             x1, [x1, #0x7e0]
    // 0x98d318: StoreField: r0->field_77 = r1
    //     0x98d318: stur            w1, [x0, #0x77]
    // 0x98d31c: r0 = CarouselSlider()
    //     0x98d31c: bl              #0x98d57c  ; AllocateCarouselSliderStub -> CarouselSlider (size=0x28)
    // 0x98d320: mov             x3, x0
    // 0x98d324: ldur            x0, [fp, #-0x10]
    // 0x98d328: stur            x3, [fp, #-8]
    // 0x98d32c: StoreField: r3->field_1f = r0
    //     0x98d32c: stur            x0, [x3, #0x1f]
    // 0x98d330: ldur            x2, [fp, #-0x28]
    // 0x98d334: r1 = Function '<anonymous closure>':.
    //     0x98d334: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b950] AnonymousClosure: (0x98de74), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98d338: ldr             x1, [x1, #0x950]
    // 0x98d33c: r0 = AllocateClosure()
    //     0x98d33c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98d340: mov             x1, x0
    // 0x98d344: ldur            x0, [fp, #-8]
    // 0x98d348: ArrayStore: r0[0] = r1  ; List_4
    //     0x98d348: stur            w1, [x0, #0x17]
    // 0x98d34c: ldur            x1, [fp, #-0x30]
    // 0x98d350: StoreField: r0->field_b = r1
    //     0x98d350: stur            w1, [x0, #0xb]
    // 0x98d354: ldur            x1, [fp, #-0x20]
    // 0x98d358: StoreField: r0->field_1b = r1
    //     0x98d358: stur            w1, [x0, #0x1b]
    // 0x98d35c: r1 = Null
    //     0x98d35c: mov             x1, NULL
    // 0x98d360: r2 = 2
    //     0x98d360: movz            x2, #0x2
    // 0x98d364: r0 = AllocateArray()
    //     0x98d364: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98d368: mov             x2, x0
    // 0x98d36c: ldur            x0, [fp, #-8]
    // 0x98d370: stur            x2, [fp, #-0x20]
    // 0x98d374: StoreField: r2->field_f = r0
    //     0x98d374: stur            w0, [x2, #0xf]
    // 0x98d378: r1 = <Widget>
    //     0x98d378: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x98d37c: r0 = AllocateGrowableArray()
    //     0x98d37c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98d380: mov             x2, x0
    // 0x98d384: ldur            x0, [fp, #-0x20]
    // 0x98d388: stur            x2, [fp, #-8]
    // 0x98d38c: StoreField: r2->field_f = r0
    //     0x98d38c: stur            w0, [x2, #0xf]
    // 0x98d390: r0 = 2
    //     0x98d390: movz            x0, #0x2
    // 0x98d394: StoreField: r2->field_b = r0
    //     0x98d394: stur            w0, [x2, #0xb]
    // 0x98d398: ldur            x1, [fp, #-0x18]
    // 0x98d39c: LoadField: r0 = r1->field_b
    //     0x98d39c: ldur            w0, [x1, #0xb]
    // 0x98d3a0: r3 = LoadInt32Instr(r0)
    //     0x98d3a0: sbfx            x3, x0, #1, #0x1f
    // 0x98d3a4: cmp             x3, #1
    // 0x98d3a8: b.le            #0x98d4ec
    // 0x98d3ac: r0 = asMap()
    //     0x98d3ac: bl              #0x7143ec  ; [dart:collection] ListBase::asMap
    // 0x98d3b0: mov             x1, x0
    // 0x98d3b4: r0 = entries()
    //     0x98d3b4: bl              #0x1641968  ; [dart:collection] MapBase::entries
    // 0x98d3b8: ldur            x2, [fp, #-0x28]
    // 0x98d3bc: r1 = Function '<anonymous closure>':.
    //     0x98d3bc: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b958] AnonymousClosure: (0x98d594), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98d3c0: ldr             x1, [x1, #0x958]
    // 0x98d3c4: stur            x0, [fp, #-0x18]
    // 0x98d3c8: r0 = AllocateClosure()
    //     0x98d3c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98d3cc: r16 = <GestureDetector>
    //     0x98d3cc: add             x16, PP, #0x53, lsl #12  ; [pp+0x53890] TypeArguments: <GestureDetector>
    //     0x98d3d0: ldr             x16, [x16, #0x890]
    // 0x98d3d4: ldur            lr, [fp, #-0x18]
    // 0x98d3d8: stp             lr, x16, [SP, #8]
    // 0x98d3dc: str             x0, [SP]
    // 0x98d3e0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x98d3e0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x98d3e4: r0 = map()
    //     0x98d3e4: bl              #0x78898c  ; [dart:_internal] ListIterable::map
    // 0x98d3e8: mov             x1, x0
    // 0x98d3ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x98d3ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x98d3f0: r0 = toList()
    //     0x98d3f0: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x98d3f4: stur            x0, [fp, #-0x18]
    // 0x98d3f8: r0 = Row()
    //     0x98d3f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x98d3fc: mov             x1, x0
    // 0x98d400: r0 = Instance_Axis
    //     0x98d400: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x98d404: stur            x1, [fp, #-0x20]
    // 0x98d408: StoreField: r1->field_f = r0
    //     0x98d408: stur            w0, [x1, #0xf]
    // 0x98d40c: r0 = Instance_MainAxisAlignment
    //     0x98d40c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x98d410: ldr             x0, [x0, #0xab0]
    // 0x98d414: StoreField: r1->field_13 = r0
    //     0x98d414: stur            w0, [x1, #0x13]
    // 0x98d418: r0 = Instance_MainAxisSize
    //     0x98d418: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98d41c: ldr             x0, [x0, #0xa10]
    // 0x98d420: ArrayStore: r1[0] = r0  ; List_4
    //     0x98d420: stur            w0, [x1, #0x17]
    // 0x98d424: r2 = Instance_CrossAxisAlignment
    //     0x98d424: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98d428: ldr             x2, [x2, #0xa18]
    // 0x98d42c: StoreField: r1->field_1b = r2
    //     0x98d42c: stur            w2, [x1, #0x1b]
    // 0x98d430: r3 = Instance_VerticalDirection
    //     0x98d430: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98d434: ldr             x3, [x3, #0xa20]
    // 0x98d438: StoreField: r1->field_23 = r3
    //     0x98d438: stur            w3, [x1, #0x23]
    // 0x98d43c: r4 = Instance_Clip
    //     0x98d43c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98d440: ldr             x4, [x4, #0x38]
    // 0x98d444: StoreField: r1->field_2b = r4
    //     0x98d444: stur            w4, [x1, #0x2b]
    // 0x98d448: StoreField: r1->field_2f = rZR
    //     0x98d448: stur            xzr, [x1, #0x2f]
    // 0x98d44c: ldur            x5, [fp, #-0x18]
    // 0x98d450: StoreField: r1->field_b = r5
    //     0x98d450: stur            w5, [x1, #0xb]
    // 0x98d454: r0 = Padding()
    //     0x98d454: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x98d458: mov             x2, x0
    // 0x98d45c: r0 = Instance_EdgeInsets
    //     0x98d45c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x98d460: ldr             x0, [x0, #0x668]
    // 0x98d464: stur            x2, [fp, #-0x18]
    // 0x98d468: StoreField: r2->field_f = r0
    //     0x98d468: stur            w0, [x2, #0xf]
    // 0x98d46c: ldur            x0, [fp, #-0x20]
    // 0x98d470: StoreField: r2->field_b = r0
    //     0x98d470: stur            w0, [x2, #0xb]
    // 0x98d474: ldur            x0, [fp, #-8]
    // 0x98d478: LoadField: r1 = r0->field_b
    //     0x98d478: ldur            w1, [x0, #0xb]
    // 0x98d47c: LoadField: r3 = r0->field_f
    //     0x98d47c: ldur            w3, [x0, #0xf]
    // 0x98d480: DecompressPointer r3
    //     0x98d480: add             x3, x3, HEAP, lsl #32
    // 0x98d484: LoadField: r4 = r3->field_b
    //     0x98d484: ldur            w4, [x3, #0xb]
    // 0x98d488: r3 = LoadInt32Instr(r1)
    //     0x98d488: sbfx            x3, x1, #1, #0x1f
    // 0x98d48c: stur            x3, [fp, #-0x10]
    // 0x98d490: r1 = LoadInt32Instr(r4)
    //     0x98d490: sbfx            x1, x4, #1, #0x1f
    // 0x98d494: cmp             x3, x1
    // 0x98d498: b.ne            #0x98d4a4
    // 0x98d49c: mov             x1, x0
    // 0x98d4a0: r0 = _growToNextCapacity()
    //     0x98d4a0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x98d4a4: ldur            x2, [fp, #-8]
    // 0x98d4a8: ldur            x3, [fp, #-0x10]
    // 0x98d4ac: add             x0, x3, #1
    // 0x98d4b0: lsl             x1, x0, #1
    // 0x98d4b4: StoreField: r2->field_b = r1
    //     0x98d4b4: stur            w1, [x2, #0xb]
    // 0x98d4b8: LoadField: r1 = r2->field_f
    //     0x98d4b8: ldur            w1, [x2, #0xf]
    // 0x98d4bc: DecompressPointer r1
    //     0x98d4bc: add             x1, x1, HEAP, lsl #32
    // 0x98d4c0: ldur            x0, [fp, #-0x18]
    // 0x98d4c4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x98d4c4: add             x25, x1, x3, lsl #2
    //     0x98d4c8: add             x25, x25, #0xf
    //     0x98d4cc: str             w0, [x25]
    //     0x98d4d0: tbz             w0, #0, #0x98d4ec
    //     0x98d4d4: ldurb           w16, [x1, #-1]
    //     0x98d4d8: ldurb           w17, [x0, #-1]
    //     0x98d4dc: and             x16, x17, x16, lsr #2
    //     0x98d4e0: tst             x16, HEAP, lsr #32
    //     0x98d4e4: b.eq            #0x98d4ec
    //     0x98d4e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x98d4ec: r0 = Column()
    //     0x98d4ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x98d4f0: r1 = Instance_Axis
    //     0x98d4f0: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x98d4f4: StoreField: r0->field_f = r1
    //     0x98d4f4: stur            w1, [x0, #0xf]
    // 0x98d4f8: r1 = Instance_MainAxisAlignment
    //     0x98d4f8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x98d4fc: ldr             x1, [x1, #0xa08]
    // 0x98d500: StoreField: r0->field_13 = r1
    //     0x98d500: stur            w1, [x0, #0x13]
    // 0x98d504: r1 = Instance_MainAxisSize
    //     0x98d504: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98d508: ldr             x1, [x1, #0xa10]
    // 0x98d50c: ArrayStore: r0[0] = r1  ; List_4
    //     0x98d50c: stur            w1, [x0, #0x17]
    // 0x98d510: r1 = Instance_CrossAxisAlignment
    //     0x98d510: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98d514: ldr             x1, [x1, #0xa18]
    // 0x98d518: StoreField: r0->field_1b = r1
    //     0x98d518: stur            w1, [x0, #0x1b]
    // 0x98d51c: r1 = Instance_VerticalDirection
    //     0x98d51c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98d520: ldr             x1, [x1, #0xa20]
    // 0x98d524: StoreField: r0->field_23 = r1
    //     0x98d524: stur            w1, [x0, #0x23]
    // 0x98d528: r1 = Instance_Clip
    //     0x98d528: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98d52c: ldr             x1, [x1, #0x38]
    // 0x98d530: StoreField: r0->field_2b = r1
    //     0x98d530: stur            w1, [x0, #0x2b]
    // 0x98d534: StoreField: r0->field_2f = rZR
    //     0x98d534: stur            xzr, [x0, #0x2f]
    // 0x98d538: ldur            x1, [fp, #-8]
    // 0x98d53c: StoreField: r0->field_b = r1
    //     0x98d53c: stur            w1, [x0, #0xb]
    // 0x98d540: LeaveFrame
    //     0x98d540: mov             SP, fp
    //     0x98d544: ldp             fp, lr, [SP], #0x10
    // 0x98d548: ret
    //     0x98d548: ret             
    // 0x98d54c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98d54c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98d550: b               #0x98d1f4
    // 0x98d554: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d554: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, MapEntry<int, Entity>) {
    // ** addr: 0x98d594, size: 0x1d0
    // 0x98d594: EnterFrame
    //     0x98d594: stp             fp, lr, [SP, #-0x10]!
    //     0x98d598: mov             fp, SP
    // 0x98d59c: AllocStack(0x40)
    //     0x98d59c: sub             SP, SP, #0x40
    // 0x98d5a0: SetupParameters()
    //     0x98d5a0: ldr             x0, [fp, #0x18]
    //     0x98d5a4: ldur            w1, [x0, #0x17]
    //     0x98d5a8: add             x1, x1, HEAP, lsl #32
    //     0x98d5ac: stur            x1, [fp, #-8]
    // 0x98d5b0: CheckStackOverflow
    //     0x98d5b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98d5b4: cmp             SP, x16
    //     0x98d5b8: b.ls            #0x98d750
    // 0x98d5bc: r1 = 1
    //     0x98d5bc: movz            x1, #0x1
    // 0x98d5c0: r0 = AllocateContext()
    //     0x98d5c0: bl              #0x16f6108  ; AllocateContextStub
    // 0x98d5c4: mov             x3, x0
    // 0x98d5c8: ldur            x0, [fp, #-8]
    // 0x98d5cc: stur            x3, [fp, #-0x10]
    // 0x98d5d0: StoreField: r3->field_b = r0
    //     0x98d5d0: stur            w0, [x3, #0xb]
    // 0x98d5d4: ldr             x1, [fp, #0x10]
    // 0x98d5d8: StoreField: r3->field_f = r1
    //     0x98d5d8: stur            w1, [x3, #0xf]
    // 0x98d5dc: LoadField: r1 = r0->field_f
    //     0x98d5dc: ldur            w1, [x0, #0xf]
    // 0x98d5e0: DecompressPointer r1
    //     0x98d5e0: add             x1, x1, HEAP, lsl #32
    // 0x98d5e4: LoadField: r2 = r1->field_b
    //     0x98d5e4: ldur            w2, [x1, #0xb]
    // 0x98d5e8: DecompressPointer r2
    //     0x98d5e8: add             x2, x2, HEAP, lsl #32
    // 0x98d5ec: cmp             w2, NULL
    // 0x98d5f0: b.eq            #0x98d758
    // 0x98d5f4: LoadField: r1 = r2->field_1b
    //     0x98d5f4: ldur            w1, [x2, #0x1b]
    // 0x98d5f8: DecompressPointer r1
    //     0x98d5f8: add             x1, x1, HEAP, lsl #32
    // 0x98d5fc: mov             x2, x1
    // 0x98d600: r1 = Null
    //     0x98d600: mov             x1, NULL
    // 0x98d604: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x98d604: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x98d608: r0 = Border.all()
    //     0x98d608: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x98d60c: mov             x3, x0
    // 0x98d610: ldur            x2, [fp, #-8]
    // 0x98d614: stur            x3, [fp, #-0x18]
    // 0x98d618: LoadField: r0 = r2->field_f
    //     0x98d618: ldur            w0, [x2, #0xf]
    // 0x98d61c: DecompressPointer r0
    //     0x98d61c: add             x0, x0, HEAP, lsl #32
    // 0x98d620: LoadField: r4 = r0->field_13
    //     0x98d620: ldur            x4, [x0, #0x13]
    // 0x98d624: ldur            x5, [fp, #-0x10]
    // 0x98d628: LoadField: r0 = r5->field_f
    //     0x98d628: ldur            w0, [x5, #0xf]
    // 0x98d62c: DecompressPointer r0
    //     0x98d62c: add             x0, x0, HEAP, lsl #32
    // 0x98d630: LoadField: r6 = r0->field_b
    //     0x98d630: ldur            w6, [x0, #0xb]
    // 0x98d634: DecompressPointer r6
    //     0x98d634: add             x6, x6, HEAP, lsl #32
    // 0x98d638: r0 = BoxInt64Instr(r4)
    //     0x98d638: sbfiz           x0, x4, #1, #0x1f
    //     0x98d63c: cmp             x4, x0, asr #1
    //     0x98d640: b.eq            #0x98d64c
    //     0x98d644: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x98d648: stur            x4, [x0, #7]
    // 0x98d64c: stp             x6, x0, [SP]
    // 0x98d650: r0 = ==()
    //     0x98d650: bl              #0x169f0b8  ; [dart:core] _IntegerImplementation::==
    // 0x98d654: tbnz            w0, #4, #0x98d680
    // 0x98d658: ldur            x0, [fp, #-8]
    // 0x98d65c: LoadField: r1 = r0->field_f
    //     0x98d65c: ldur            w1, [x0, #0xf]
    // 0x98d660: DecompressPointer r1
    //     0x98d660: add             x1, x1, HEAP, lsl #32
    // 0x98d664: LoadField: r0 = r1->field_b
    //     0x98d664: ldur            w0, [x1, #0xb]
    // 0x98d668: DecompressPointer r0
    //     0x98d668: add             x0, x0, HEAP, lsl #32
    // 0x98d66c: cmp             w0, NULL
    // 0x98d670: b.eq            #0x98d75c
    // 0x98d674: LoadField: r1 = r0->field_1b
    //     0x98d674: ldur            w1, [x0, #0x1b]
    // 0x98d678: DecompressPointer r1
    //     0x98d678: add             x1, x1, HEAP, lsl #32
    // 0x98d67c: b               #0x98d6a4
    // 0x98d680: ldur            x0, [fp, #-8]
    // 0x98d684: LoadField: r1 = r0->field_f
    //     0x98d684: ldur            w1, [x0, #0xf]
    // 0x98d688: DecompressPointer r1
    //     0x98d688: add             x1, x1, HEAP, lsl #32
    // 0x98d68c: LoadField: r0 = r1->field_b
    //     0x98d68c: ldur            w0, [x1, #0xb]
    // 0x98d690: DecompressPointer r0
    //     0x98d690: add             x0, x0, HEAP, lsl #32
    // 0x98d694: cmp             w0, NULL
    // 0x98d698: b.eq            #0x98d760
    // 0x98d69c: r1 = Instance_Color
    //     0x98d69c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x98d6a0: ldr             x1, [x1, #0x90]
    // 0x98d6a4: ldur            x0, [fp, #-0x18]
    // 0x98d6a8: stur            x1, [fp, #-8]
    // 0x98d6ac: r0 = BoxDecoration()
    //     0x98d6ac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x98d6b0: mov             x1, x0
    // 0x98d6b4: ldur            x0, [fp, #-8]
    // 0x98d6b8: stur            x1, [fp, #-0x20]
    // 0x98d6bc: StoreField: r1->field_7 = r0
    //     0x98d6bc: stur            w0, [x1, #7]
    // 0x98d6c0: ldur            x0, [fp, #-0x18]
    // 0x98d6c4: StoreField: r1->field_f = r0
    //     0x98d6c4: stur            w0, [x1, #0xf]
    // 0x98d6c8: r0 = Instance_BoxShape
    //     0x98d6c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x98d6cc: ldr             x0, [x0, #0x970]
    // 0x98d6d0: StoreField: r1->field_23 = r0
    //     0x98d6d0: stur            w0, [x1, #0x23]
    // 0x98d6d4: r0 = Container()
    //     0x98d6d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x98d6d8: stur            x0, [fp, #-8]
    // 0x98d6dc: r16 = 8.000000
    //     0x98d6dc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0x98d6e0: ldr             x16, [x16, #0x608]
    // 0x98d6e4: r30 = 8.000000
    //     0x98d6e4: add             lr, PP, #0x36, lsl #12  ; [pp+0x36608] 8
    //     0x98d6e8: ldr             lr, [lr, #0x608]
    // 0x98d6ec: stp             lr, x16, [SP, #0x10]
    // 0x98d6f0: r16 = Instance_EdgeInsets
    //     0x98d6f0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0x98d6f4: ldr             x16, [x16, #0xa68]
    // 0x98d6f8: ldur            lr, [fp, #-0x20]
    // 0x98d6fc: stp             lr, x16, [SP]
    // 0x98d700: mov             x1, x0
    // 0x98d704: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x2, margin, 0x3, width, 0x1, null]
    //     0x98d704: add             x4, PP, #0x6b, lsl #12  ; [pp+0x6b780] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x2, "margin", 0x3, "width", 0x1, Null]
    //     0x98d708: ldr             x4, [x4, #0x780]
    // 0x98d70c: r0 = Container()
    //     0x98d70c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x98d710: r0 = GestureDetector()
    //     0x98d710: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x98d714: ldur            x2, [fp, #-0x10]
    // 0x98d718: r1 = Function '<anonymous closure>':.
    //     0x98d718: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b960] AnonymousClosure: (0x98d804), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_banner_cross_widget.dart] _ProductBannerCrossWidgetState::build (0x99ec1c)
    //     0x98d71c: ldr             x1, [x1, #0x960]
    // 0x98d720: stur            x0, [fp, #-0x10]
    // 0x98d724: r0 = AllocateClosure()
    //     0x98d724: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98d728: ldur            x16, [fp, #-8]
    // 0x98d72c: stp             x16, x0, [SP]
    // 0x98d730: ldur            x1, [fp, #-0x10]
    // 0x98d734: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x98d734: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x98d738: ldr             x4, [x4, #0xaf0]
    // 0x98d73c: r0 = GestureDetector()
    //     0x98d73c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x98d740: ldur            x0, [fp, #-0x10]
    // 0x98d744: LeaveFrame
    //     0x98d744: mov             SP, fp
    //     0x98d748: ldp             fp, lr, [SP], #0x10
    // 0x98d74c: ret
    //     0x98d74c: ret             
    // 0x98d750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98d750: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98d754: b               #0x98d5bc
    // 0x98d758: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d758: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d75c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d75c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98d760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98d760: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int, int) {
    // ** addr: 0x98de74, size: 0xd8
    // 0x98de74: EnterFrame
    //     0x98de74: stp             fp, lr, [SP, #-0x10]!
    //     0x98de78: mov             fp, SP
    // 0x98de7c: AllocStack(0x28)
    //     0x98de7c: sub             SP, SP, #0x28
    // 0x98de80: SetupParameters()
    //     0x98de80: ldr             x0, [fp, #0x28]
    //     0x98de84: ldur            w1, [x0, #0x17]
    //     0x98de88: add             x1, x1, HEAP, lsl #32
    //     0x98de8c: stur            x1, [fp, #-8]
    // 0x98de90: CheckStackOverflow
    //     0x98de90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98de94: cmp             SP, x16
    //     0x98de98: b.ls            #0x98df40
    // 0x98de9c: r1 = 1
    //     0x98de9c: movz            x1, #0x1
    // 0x98dea0: r0 = AllocateContext()
    //     0x98dea0: bl              #0x16f6108  ; AllocateContextStub
    // 0x98dea4: mov             x1, x0
    // 0x98dea8: ldur            x0, [fp, #-8]
    // 0x98deac: stur            x1, [fp, #-0x10]
    // 0x98deb0: StoreField: r1->field_b = r0
    //     0x98deb0: stur            w0, [x1, #0xb]
    // 0x98deb4: ldr             x2, [fp, #0x18]
    // 0x98deb8: StoreField: r1->field_f = r2
    //     0x98deb8: stur            w2, [x1, #0xf]
    // 0x98debc: LoadField: r3 = r0->field_f
    //     0x98debc: ldur            w3, [x0, #0xf]
    // 0x98dec0: DecompressPointer r3
    //     0x98dec0: add             x3, x3, HEAP, lsl #32
    // 0x98dec4: LoadField: r0 = r3->field_b
    //     0x98dec4: ldur            w0, [x3, #0xb]
    // 0x98dec8: DecompressPointer r0
    //     0x98dec8: add             x0, x0, HEAP, lsl #32
    // 0x98decc: cmp             w0, NULL
    // 0x98ded0: b.eq            #0x98df48
    // 0x98ded4: LoadField: r3 = r0->field_27
    //     0x98ded4: ldur            w3, [x0, #0x27]
    // 0x98ded8: DecompressPointer r3
    //     0x98ded8: add             x3, x3, HEAP, lsl #32
    // 0x98dedc: LoadField: r4 = r0->field_47
    //     0x98dedc: ldur            w4, [x0, #0x47]
    // 0x98dee0: DecompressPointer r4
    //     0x98dee0: add             x4, x4, HEAP, lsl #32
    // 0x98dee4: stp             x3, x4, [SP, #8]
    // 0x98dee8: str             x2, [SP]
    // 0x98deec: mov             x0, x4
    // 0x98def0: ClosureCall
    //     0x98def0: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x98def4: ldur            x2, [x0, #0x1f]
    //     0x98def8: blr             x2
    // 0x98defc: stur            x0, [fp, #-8]
    // 0x98df00: r0 = GestureDetector()
    //     0x98df00: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x98df04: ldur            x2, [fp, #-0x10]
    // 0x98df08: r1 = Function '<anonymous closure>':.
    //     0x98df08: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b968] AnonymousClosure: (0x98df4c), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98df0c: ldr             x1, [x1, #0x968]
    // 0x98df10: stur            x0, [fp, #-0x10]
    // 0x98df14: r0 = AllocateClosure()
    //     0x98df14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98df18: ldur            x16, [fp, #-8]
    // 0x98df1c: stp             x16, x0, [SP]
    // 0x98df20: ldur            x1, [fp, #-0x10]
    // 0x98df24: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x98df24: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x98df28: ldr             x4, [x4, #0xaf0]
    // 0x98df2c: r0 = GestureDetector()
    //     0x98df2c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x98df30: ldur            x0, [fp, #-0x10]
    // 0x98df34: LeaveFrame
    //     0x98df34: mov             SP, fp
    //     0x98df38: ldp             fp, lr, [SP], #0x10
    // 0x98df3c: ret
    //     0x98df3c: ret             
    // 0x98df40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98df40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98df44: b               #0x98de9c
    // 0x98df48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98df48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x98df4c, size: 0x14c
    // 0x98df4c: EnterFrame
    //     0x98df4c: stp             fp, lr, [SP, #-0x10]!
    //     0x98df50: mov             fp, SP
    // 0x98df54: AllocStack(0x40)
    //     0x98df54: sub             SP, SP, #0x40
    // 0x98df58: SetupParameters()
    //     0x98df58: ldr             x0, [fp, #0x10]
    //     0x98df5c: ldur            w1, [x0, #0x17]
    //     0x98df60: add             x1, x1, HEAP, lsl #32
    // 0x98df64: CheckStackOverflow
    //     0x98df64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98df68: cmp             SP, x16
    //     0x98df6c: b.ls            #0x98e088
    // 0x98df70: LoadField: r0 = r1->field_b
    //     0x98df70: ldur            w0, [x1, #0xb]
    // 0x98df74: DecompressPointer r0
    //     0x98df74: add             x0, x0, HEAP, lsl #32
    // 0x98df78: LoadField: r2 = r0->field_f
    //     0x98df78: ldur            w2, [x0, #0xf]
    // 0x98df7c: DecompressPointer r2
    //     0x98df7c: add             x2, x2, HEAP, lsl #32
    // 0x98df80: LoadField: r3 = r2->field_b
    //     0x98df80: ldur            w3, [x2, #0xb]
    // 0x98df84: DecompressPointer r3
    //     0x98df84: add             x3, x3, HEAP, lsl #32
    // 0x98df88: cmp             w3, NULL
    // 0x98df8c: b.eq            #0x98e090
    // 0x98df90: LoadField: r2 = r3->field_27
    //     0x98df90: ldur            w2, [x3, #0x27]
    // 0x98df94: DecompressPointer r2
    //     0x98df94: add             x2, x2, HEAP, lsl #32
    // 0x98df98: LoadField: r0 = r1->field_f
    //     0x98df98: ldur            w0, [x1, #0xf]
    // 0x98df9c: DecompressPointer r0
    //     0x98df9c: add             x0, x0, HEAP, lsl #32
    // 0x98dfa0: LoadField: r1 = r2->field_b
    //     0x98dfa0: ldur            w1, [x2, #0xb]
    // 0x98dfa4: r4 = LoadInt32Instr(r0)
    //     0x98dfa4: sbfx            x4, x0, #1, #0x1f
    //     0x98dfa8: tbz             w0, #0, #0x98dfb0
    //     0x98dfac: ldur            x4, [x0, #7]
    // 0x98dfb0: r0 = LoadInt32Instr(r1)
    //     0x98dfb0: sbfx            x0, x1, #1, #0x1f
    // 0x98dfb4: mov             x1, x4
    // 0x98dfb8: cmp             x1, x0
    // 0x98dfbc: b.hs            #0x98e094
    // 0x98dfc0: LoadField: r0 = r2->field_f
    //     0x98dfc0: ldur            w0, [x2, #0xf]
    // 0x98dfc4: DecompressPointer r0
    //     0x98dfc4: add             x0, x0, HEAP, lsl #32
    // 0x98dfc8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x98dfc8: add             x16, x0, x4, lsl #2
    //     0x98dfcc: ldur            w1, [x16, #0xf]
    // 0x98dfd0: DecompressPointer r1
    //     0x98dfd0: add             x1, x1, HEAP, lsl #32
    // 0x98dfd4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x98dfd4: ldur            w0, [x1, #0x17]
    // 0x98dfd8: DecompressPointer r0
    //     0x98dfd8: add             x0, x0, HEAP, lsl #32
    // 0x98dfdc: LoadField: r1 = r3->field_2f
    //     0x98dfdc: ldur            w1, [x3, #0x2f]
    // 0x98dfe0: DecompressPointer r1
    //     0x98dfe0: add             x1, x1, HEAP, lsl #32
    // 0x98dfe4: cmp             w1, NULL
    // 0x98dfe8: b.ne            #0x98dff0
    // 0x98dfec: r1 = ""
    //     0x98dfec: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98dff0: LoadField: r2 = r3->field_33
    //     0x98dff0: ldur            w2, [x3, #0x33]
    // 0x98dff4: DecompressPointer r2
    //     0x98dff4: add             x2, x2, HEAP, lsl #32
    // 0x98dff8: cmp             w2, NULL
    // 0x98dffc: b.ne            #0x98e004
    // 0x98e000: r2 = ""
    //     0x98e000: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98e004: LoadField: r4 = r3->field_37
    //     0x98e004: ldur            w4, [x3, #0x37]
    // 0x98e008: DecompressPointer r4
    //     0x98e008: add             x4, x4, HEAP, lsl #32
    // 0x98e00c: LoadField: r5 = r3->field_3b
    //     0x98e00c: ldur            w5, [x3, #0x3b]
    // 0x98e010: DecompressPointer r5
    //     0x98e010: add             x5, x5, HEAP, lsl #32
    // 0x98e014: cmp             w5, NULL
    // 0x98e018: b.ne            #0x98e020
    // 0x98e01c: r5 = ""
    //     0x98e01c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98e020: LoadField: r6 = r3->field_3f
    //     0x98e020: ldur            w6, [x3, #0x3f]
    // 0x98e024: DecompressPointer r6
    //     0x98e024: add             x6, x6, HEAP, lsl #32
    // 0x98e028: cmp             w6, NULL
    // 0x98e02c: b.ne            #0x98e034
    // 0x98e030: r6 = ""
    //     0x98e030: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98e034: LoadField: r7 = r3->field_43
    //     0x98e034: ldur            w7, [x3, #0x43]
    // 0x98e038: DecompressPointer r7
    //     0x98e038: add             x7, x7, HEAP, lsl #32
    // 0x98e03c: cmp             w7, NULL
    // 0x98e040: b.ne            #0x98e048
    // 0x98e044: r7 = ""
    //     0x98e044: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98e048: LoadField: r8 = r3->field_2b
    //     0x98e048: ldur            w8, [x3, #0x2b]
    // 0x98e04c: DecompressPointer r8
    //     0x98e04c: add             x8, x8, HEAP, lsl #32
    // 0x98e050: stp             x0, x8, [SP, #0x30]
    // 0x98e054: stp             x2, x1, [SP, #0x20]
    // 0x98e058: stp             x5, x4, [SP, #0x10]
    // 0x98e05c: stp             x7, x6, [SP]
    // 0x98e060: r4 = 0
    //     0x98e060: movz            x4, #0
    // 0x98e064: ldr             x0, [SP, #0x38]
    // 0x98e068: r16 = UnlinkedCall_0x613b5c
    //     0x98e068: add             x16, PP, #0x6b, lsl #12  ; [pp+0x6b970] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x98e06c: add             x16, x16, #0x970
    // 0x98e070: ldp             x5, lr, [x16]
    // 0x98e074: blr             lr
    // 0x98e078: r0 = Null
    //     0x98e078: mov             x0, NULL
    // 0x98e07c: LeaveFrame
    //     0x98e07c: mov             SP, fp
    //     0x98e080: ldp             fp, lr, [SP], #0x10
    // 0x98e084: ret
    //     0x98e084: ret             
    // 0x98e088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98e088: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98e08c: b               #0x98df70
    // 0x98e090: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98e090: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98e094: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x98e094: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, int, CarouselPageChangedReason) {
    // ** addr: 0x98e098, size: 0xa4
    // 0x98e098: EnterFrame
    //     0x98e098: stp             fp, lr, [SP, #-0x10]!
    //     0x98e09c: mov             fp, SP
    // 0x98e0a0: AllocStack(0x10)
    //     0x98e0a0: sub             SP, SP, #0x10
    // 0x98e0a4: SetupParameters()
    //     0x98e0a4: ldr             x0, [fp, #0x20]
    //     0x98e0a8: ldur            w1, [x0, #0x17]
    //     0x98e0ac: add             x1, x1, HEAP, lsl #32
    //     0x98e0b0: stur            x1, [fp, #-8]
    // 0x98e0b4: CheckStackOverflow
    //     0x98e0b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98e0b8: cmp             SP, x16
    //     0x98e0bc: b.ls            #0x98e130
    // 0x98e0c0: r1 = 1
    //     0x98e0c0: movz            x1, #0x1
    // 0x98e0c4: r0 = AllocateContext()
    //     0x98e0c4: bl              #0x16f6108  ; AllocateContextStub
    // 0x98e0c8: mov             x1, x0
    // 0x98e0cc: ldur            x0, [fp, #-8]
    // 0x98e0d0: StoreField: r1->field_b = r0
    //     0x98e0d0: stur            w0, [x1, #0xb]
    // 0x98e0d4: ldr             x2, [fp, #0x18]
    // 0x98e0d8: StoreField: r1->field_f = r2
    //     0x98e0d8: stur            w2, [x1, #0xf]
    // 0x98e0dc: LoadField: r3 = r0->field_f
    //     0x98e0dc: ldur            w3, [x0, #0xf]
    // 0x98e0e0: DecompressPointer r3
    //     0x98e0e0: add             x3, x3, HEAP, lsl #32
    // 0x98e0e4: mov             x2, x1
    // 0x98e0e8: stur            x3, [fp, #-0x10]
    // 0x98e0ec: r1 = Function '<anonymous closure>':.
    //     0x98e0ec: add             x1, PP, #0x6b, lsl #12  ; [pp+0x6b980] AnonymousClosure: (0x98e13c), in [package:customer_app/app/presentation/custom_widgets/product_detail/product_banner_cross_widget.dart] _ProductBannerCrossWidgetState::build (0x99ec1c)
    //     0x98e0f0: ldr             x1, [x1, #0x980]
    // 0x98e0f4: r0 = AllocateClosure()
    //     0x98e0f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98e0f8: ldur            x1, [fp, #-0x10]
    // 0x98e0fc: mov             x2, x0
    // 0x98e100: r0 = setState()
    //     0x98e100: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x98e104: ldur            x1, [fp, #-8]
    // 0x98e108: LoadField: r2 = r1->field_f
    //     0x98e108: ldur            w2, [x1, #0xf]
    // 0x98e10c: DecompressPointer r2
    //     0x98e10c: add             x2, x2, HEAP, lsl #32
    // 0x98e110: LoadField: r1 = r2->field_b
    //     0x98e110: ldur            w1, [x2, #0xb]
    // 0x98e114: DecompressPointer r1
    //     0x98e114: add             x1, x1, HEAP, lsl #32
    // 0x98e118: cmp             w1, NULL
    // 0x98e11c: b.eq            #0x98e138
    // 0x98e120: r0 = Null
    //     0x98e120: mov             x0, NULL
    // 0x98e124: LeaveFrame
    //     0x98e124: mov             SP, fp
    //     0x98e128: ldp             fp, lr, [SP], #0x10
    // 0x98e12c: ret
    //     0x98e12c: ret             
    // 0x98e130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98e130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98e134: b               #0x98e0c0
    // 0x98e138: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98e138: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4320, size: 0x4c, field offset: 0xc
//   const constructor, 
class BannerCrossWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc797dc, size: 0x78
    // 0xc797dc: EnterFrame
    //     0xc797dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc797e0: mov             fp, SP
    // 0xc797e4: AllocStack(0x10)
    //     0xc797e4: sub             SP, SP, #0x10
    // 0xc797e8: CheckStackOverflow
    //     0xc797e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc797ec: cmp             SP, x16
    //     0xc797f0: b.ls            #0xc7984c
    // 0xc797f4: r1 = <BannerCrossWidget>
    //     0xc797f4: add             x1, PP, #0x62, lsl #12  ; [pp+0x629a0] TypeArguments: <BannerCrossWidget>
    //     0xc797f8: ldr             x1, [x1, #0x9a0]
    // 0xc797fc: r0 = _BannerCrossWidgetState()
    //     0xc797fc: bl              #0xc79940  ; Allocate_BannerCrossWidgetStateStub -> _BannerCrossWidgetState (size=0x20)
    // 0xc79800: stur            x0, [fp, #-8]
    // 0xc79804: StoreField: r0->field_13 = rZR
    //     0xc79804: stur            xzr, [x0, #0x13]
    // 0xc79808: r0 = CarouselSliderControllerImpl()
    //     0xc79808: bl              #0xc79934  ; AllocateCarouselSliderControllerImplStub -> CarouselSliderControllerImpl (size=0x10)
    // 0xc7980c: mov             x1, x0
    // 0xc79810: stur            x0, [fp, #-0x10]
    // 0xc79814: r0 = CarouselSliderControllerImpl()
    //     0xc79814: bl              #0xc79894  ; [package:carousel_slider/carousel_controller.dart] CarouselSliderControllerImpl::CarouselSliderControllerImpl
    // 0xc79818: ldur            x0, [fp, #-0x10]
    // 0xc7981c: ldur            x1, [fp, #-8]
    // 0xc79820: StoreField: r1->field_1b = r0
    //     0xc79820: stur            w0, [x1, #0x1b]
    //     0xc79824: ldurb           w16, [x1, #-1]
    //     0xc79828: ldurb           w17, [x0, #-1]
    //     0xc7982c: and             x16, x17, x16, lsr #2
    //     0xc79830: tst             x16, HEAP, lsr #32
    //     0xc79834: b.eq            #0xc7983c
    //     0xc79838: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7983c: mov             x0, x1
    // 0xc79840: LeaveFrame
    //     0xc79840: mov             SP, fp
    //     0xc79844: ldp             fp, lr, [SP], #0x10
    // 0xc79848: ret
    //     0xc79848: ret             
    // 0xc7984c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7984c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc79850: b               #0xc797f4
  }
}
