// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/partial_popup_bottom_sheet.dart

// class id: 1049376, size: 0x8
class :: {
}

// class id: 3354, size: 0x14, field offset: 0x14
class _PartialCodPopupBottomSheet extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb4dbd4, size: 0xe1c
    // 0xb4dbd4: EnterFrame
    //     0xb4dbd4: stp             fp, lr, [SP, #-0x10]!
    //     0xb4dbd8: mov             fp, SP
    // 0xb4dbdc: AllocStack(0x80)
    //     0xb4dbdc: sub             SP, SP, #0x80
    // 0xb4dbe0: SetupParameters(_PartialCodPopupBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb4dbe0: mov             x0, x1
    //     0xb4dbe4: stur            x1, [fp, #-8]
    //     0xb4dbe8: mov             x1, x2
    //     0xb4dbec: stur            x2, [fp, #-0x10]
    // 0xb4dbf0: CheckStackOverflow
    //     0xb4dbf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4dbf4: cmp             SP, x16
    //     0xb4dbf8: b.ls            #0xb4e9d0
    // 0xb4dbfc: r1 = 1
    //     0xb4dbfc: movz            x1, #0x1
    // 0xb4dc00: r0 = AllocateContext()
    //     0xb4dc00: bl              #0x16f6108  ; AllocateContextStub
    // 0xb4dc04: mov             x2, x0
    // 0xb4dc08: ldur            x0, [fp, #-8]
    // 0xb4dc0c: stur            x2, [fp, #-0x18]
    // 0xb4dc10: StoreField: r2->field_f = r0
    //     0xb4dc10: stur            w0, [x2, #0xf]
    // 0xb4dc14: ldur            x1, [fp, #-0x10]
    // 0xb4dc18: r0 = of()
    //     0xb4dc18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4dc1c: LoadField: r1 = r0->field_87
    //     0xb4dc1c: ldur            w1, [x0, #0x87]
    // 0xb4dc20: DecompressPointer r1
    //     0xb4dc20: add             x1, x1, HEAP, lsl #32
    // 0xb4dc24: LoadField: r0 = r1->field_7
    //     0xb4dc24: ldur            w0, [x1, #7]
    // 0xb4dc28: DecompressPointer r0
    //     0xb4dc28: add             x0, x0, HEAP, lsl #32
    // 0xb4dc2c: r16 = Instance_Color
    //     0xb4dc2c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4dc30: r30 = 16.000000
    //     0xb4dc30: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4dc34: ldr             lr, [lr, #0x188]
    // 0xb4dc38: stp             lr, x16, [SP]
    // 0xb4dc3c: mov             x1, x0
    // 0xb4dc40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4dc40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4dc44: ldr             x4, [x4, #0x9b8]
    // 0xb4dc48: r0 = copyWith()
    //     0xb4dc48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4dc4c: stur            x0, [fp, #-0x20]
    // 0xb4dc50: r0 = Text()
    //     0xb4dc50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4dc54: mov             x2, x0
    // 0xb4dc58: r0 = "Pay in Advance"
    //     0xb4dc58: add             x0, PP, #0x56, lsl #12  ; [pp+0x56aa8] "Pay in Advance"
    //     0xb4dc5c: ldr             x0, [x0, #0xaa8]
    // 0xb4dc60: stur            x2, [fp, #-0x28]
    // 0xb4dc64: StoreField: r2->field_b = r0
    //     0xb4dc64: stur            w0, [x2, #0xb]
    // 0xb4dc68: ldur            x0, [fp, #-0x20]
    // 0xb4dc6c: StoreField: r2->field_13 = r0
    //     0xb4dc6c: stur            w0, [x2, #0x13]
    // 0xb4dc70: ldur            x0, [fp, #-8]
    // 0xb4dc74: LoadField: r1 = r0->field_b
    //     0xb4dc74: ldur            w1, [x0, #0xb]
    // 0xb4dc78: DecompressPointer r1
    //     0xb4dc78: add             x1, x1, HEAP, lsl #32
    // 0xb4dc7c: cmp             w1, NULL
    // 0xb4dc80: b.eq            #0xb4e9d8
    // 0xb4dc84: LoadField: r3 = r1->field_b
    //     0xb4dc84: ldur            w3, [x1, #0xb]
    // 0xb4dc88: DecompressPointer r3
    //     0xb4dc88: add             x3, x3, HEAP, lsl #32
    // 0xb4dc8c: LoadField: r1 = r3->field_b
    //     0xb4dc8c: ldur            w1, [x3, #0xb]
    // 0xb4dc90: DecompressPointer r1
    //     0xb4dc90: add             x1, x1, HEAP, lsl #32
    // 0xb4dc94: cmp             w1, NULL
    // 0xb4dc98: b.ne            #0xb4dca4
    // 0xb4dc9c: r3 = ""
    //     0xb4dc9c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4dca0: b               #0xb4dca8
    // 0xb4dca4: mov             x3, x1
    // 0xb4dca8: ldur            x1, [fp, #-0x10]
    // 0xb4dcac: stur            x3, [fp, #-0x20]
    // 0xb4dcb0: r0 = of()
    //     0xb4dcb0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4dcb4: LoadField: r1 = r0->field_87
    //     0xb4dcb4: ldur            w1, [x0, #0x87]
    // 0xb4dcb8: DecompressPointer r1
    //     0xb4dcb8: add             x1, x1, HEAP, lsl #32
    // 0xb4dcbc: LoadField: r0 = r1->field_2b
    //     0xb4dcbc: ldur            w0, [x1, #0x2b]
    // 0xb4dcc0: DecompressPointer r0
    //     0xb4dcc0: add             x0, x0, HEAP, lsl #32
    // 0xb4dcc4: r16 = 12.000000
    //     0xb4dcc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4dcc8: ldr             x16, [x16, #0x9e8]
    // 0xb4dccc: r30 = Instance_Color
    //     0xb4dccc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4dcd0: stp             lr, x16, [SP]
    // 0xb4dcd4: mov             x1, x0
    // 0xb4dcd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4dcd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4dcdc: ldr             x4, [x4, #0xaa0]
    // 0xb4dce0: r0 = copyWith()
    //     0xb4dce0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4dce4: stur            x0, [fp, #-0x30]
    // 0xb4dce8: r0 = Text()
    //     0xb4dce8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4dcec: mov             x1, x0
    // 0xb4dcf0: ldur            x0, [fp, #-0x20]
    // 0xb4dcf4: stur            x1, [fp, #-0x38]
    // 0xb4dcf8: StoreField: r1->field_b = r0
    //     0xb4dcf8: stur            w0, [x1, #0xb]
    // 0xb4dcfc: ldur            x0, [fp, #-0x30]
    // 0xb4dd00: StoreField: r1->field_13 = r0
    //     0xb4dd00: stur            w0, [x1, #0x13]
    // 0xb4dd04: r0 = Padding()
    //     0xb4dd04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4dd08: mov             x1, x0
    // 0xb4dd0c: r0 = Instance_EdgeInsets
    //     0xb4dd0c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xb4dd10: ldr             x0, [x0, #0xdb0]
    // 0xb4dd14: stur            x1, [fp, #-0x20]
    // 0xb4dd18: StoreField: r1->field_f = r0
    //     0xb4dd18: stur            w0, [x1, #0xf]
    // 0xb4dd1c: ldur            x0, [fp, #-0x38]
    // 0xb4dd20: StoreField: r1->field_b = r0
    //     0xb4dd20: stur            w0, [x1, #0xb]
    // 0xb4dd24: r0 = Container()
    //     0xb4dd24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4dd28: stur            x0, [fp, #-0x30]
    // 0xb4dd2c: r16 = Instance_BoxDecoration
    //     0xb4dd2c: add             x16, PP, #0x56, lsl #12  ; [pp+0x56ab0] Obj!BoxDecoration@d64a71
    //     0xb4dd30: ldr             x16, [x16, #0xab0]
    // 0xb4dd34: ldur            lr, [fp, #-0x20]
    // 0xb4dd38: stp             lr, x16, [SP]
    // 0xb4dd3c: mov             x1, x0
    // 0xb4dd40: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb4dd40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb4dd44: ldr             x4, [x4, #0x88]
    // 0xb4dd48: r0 = Container()
    //     0xb4dd48: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4dd4c: r1 = Null
    //     0xb4dd4c: mov             x1, NULL
    // 0xb4dd50: r2 = 6
    //     0xb4dd50: movz            x2, #0x6
    // 0xb4dd54: r0 = AllocateArray()
    //     0xb4dd54: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4dd58: mov             x2, x0
    // 0xb4dd5c: ldur            x0, [fp, #-0x28]
    // 0xb4dd60: stur            x2, [fp, #-0x20]
    // 0xb4dd64: StoreField: r2->field_f = r0
    //     0xb4dd64: stur            w0, [x2, #0xf]
    // 0xb4dd68: r16 = Instance_SizedBox
    //     0xb4dd68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb4dd6c: ldr             x16, [x16, #0xb20]
    // 0xb4dd70: StoreField: r2->field_13 = r16
    //     0xb4dd70: stur            w16, [x2, #0x13]
    // 0xb4dd74: ldur            x0, [fp, #-0x30]
    // 0xb4dd78: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4dd78: stur            w0, [x2, #0x17]
    // 0xb4dd7c: r1 = <Widget>
    //     0xb4dd7c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4dd80: r0 = AllocateGrowableArray()
    //     0xb4dd80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4dd84: mov             x1, x0
    // 0xb4dd88: ldur            x0, [fp, #-0x20]
    // 0xb4dd8c: stur            x1, [fp, #-0x28]
    // 0xb4dd90: StoreField: r1->field_f = r0
    //     0xb4dd90: stur            w0, [x1, #0xf]
    // 0xb4dd94: r0 = 6
    //     0xb4dd94: movz            x0, #0x6
    // 0xb4dd98: StoreField: r1->field_b = r0
    //     0xb4dd98: stur            w0, [x1, #0xb]
    // 0xb4dd9c: r0 = Row()
    //     0xb4dd9c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4dda0: mov             x2, x0
    // 0xb4dda4: r0 = Instance_Axis
    //     0xb4dda4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4dda8: stur            x2, [fp, #-0x30]
    // 0xb4ddac: StoreField: r2->field_f = r0
    //     0xb4ddac: stur            w0, [x2, #0xf]
    // 0xb4ddb0: r3 = Instance_MainAxisAlignment
    //     0xb4ddb0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4ddb4: ldr             x3, [x3, #0xa08]
    // 0xb4ddb8: StoreField: r2->field_13 = r3
    //     0xb4ddb8: stur            w3, [x2, #0x13]
    // 0xb4ddbc: r4 = Instance_MainAxisSize
    //     0xb4ddbc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4ddc0: ldr             x4, [x4, #0xa10]
    // 0xb4ddc4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb4ddc4: stur            w4, [x2, #0x17]
    // 0xb4ddc8: r5 = Instance_CrossAxisAlignment
    //     0xb4ddc8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4ddcc: ldr             x5, [x5, #0xa18]
    // 0xb4ddd0: StoreField: r2->field_1b = r5
    //     0xb4ddd0: stur            w5, [x2, #0x1b]
    // 0xb4ddd4: r6 = Instance_VerticalDirection
    //     0xb4ddd4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4ddd8: ldr             x6, [x6, #0xa20]
    // 0xb4dddc: StoreField: r2->field_23 = r6
    //     0xb4dddc: stur            w6, [x2, #0x23]
    // 0xb4dde0: r7 = Instance_Clip
    //     0xb4dde0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4dde4: ldr             x7, [x7, #0x38]
    // 0xb4dde8: StoreField: r2->field_2b = r7
    //     0xb4dde8: stur            w7, [x2, #0x2b]
    // 0xb4ddec: StoreField: r2->field_2f = rZR
    //     0xb4ddec: stur            xzr, [x2, #0x2f]
    // 0xb4ddf0: ldur            x1, [fp, #-0x28]
    // 0xb4ddf4: StoreField: r2->field_b = r1
    //     0xb4ddf4: stur            w1, [x2, #0xb]
    // 0xb4ddf8: ldur            x8, [fp, #-8]
    // 0xb4ddfc: LoadField: r1 = r8->field_b
    //     0xb4ddfc: ldur            w1, [x8, #0xb]
    // 0xb4de00: DecompressPointer r1
    //     0xb4de00: add             x1, x1, HEAP, lsl #32
    // 0xb4de04: cmp             w1, NULL
    // 0xb4de08: b.eq            #0xb4e9dc
    // 0xb4de0c: LoadField: r9 = r1->field_b
    //     0xb4de0c: ldur            w9, [x1, #0xb]
    // 0xb4de10: DecompressPointer r9
    //     0xb4de10: add             x9, x9, HEAP, lsl #32
    // 0xb4de14: LoadField: r1 = r9->field_f
    //     0xb4de14: ldur            w1, [x9, #0xf]
    // 0xb4de18: DecompressPointer r1
    //     0xb4de18: add             x1, x1, HEAP, lsl #32
    // 0xb4de1c: cmp             w1, NULL
    // 0xb4de20: b.ne            #0xb4de2c
    // 0xb4de24: r9 = ""
    //     0xb4de24: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4de28: b               #0xb4de30
    // 0xb4de2c: mov             x9, x1
    // 0xb4de30: ldur            x1, [fp, #-0x10]
    // 0xb4de34: stur            x9, [fp, #-0x20]
    // 0xb4de38: r0 = of()
    //     0xb4de38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4de3c: LoadField: r1 = r0->field_87
    //     0xb4de3c: ldur            w1, [x0, #0x87]
    // 0xb4de40: DecompressPointer r1
    //     0xb4de40: add             x1, x1, HEAP, lsl #32
    // 0xb4de44: LoadField: r0 = r1->field_2b
    //     0xb4de44: ldur            w0, [x1, #0x2b]
    // 0xb4de48: DecompressPointer r0
    //     0xb4de48: add             x0, x0, HEAP, lsl #32
    // 0xb4de4c: stur            x0, [fp, #-0x28]
    // 0xb4de50: r1 = Instance_Color
    //     0xb4de50: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4de54: d0 = 0.700000
    //     0xb4de54: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4de58: ldr             d0, [x17, #0xf48]
    // 0xb4de5c: r0 = withOpacity()
    //     0xb4de5c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4de60: r16 = 14.000000
    //     0xb4de60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb4de64: ldr             x16, [x16, #0x1d8]
    // 0xb4de68: stp             x0, x16, [SP]
    // 0xb4de6c: ldur            x1, [fp, #-0x28]
    // 0xb4de70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4de70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4de74: ldr             x4, [x4, #0xaa0]
    // 0xb4de78: r0 = copyWith()
    //     0xb4de78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4de7c: stur            x0, [fp, #-0x28]
    // 0xb4de80: r0 = Text()
    //     0xb4de80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4de84: mov             x2, x0
    // 0xb4de88: ldur            x0, [fp, #-0x20]
    // 0xb4de8c: stur            x2, [fp, #-0x38]
    // 0xb4de90: StoreField: r2->field_b = r0
    //     0xb4de90: stur            w0, [x2, #0xb]
    // 0xb4de94: ldur            x0, [fp, #-0x28]
    // 0xb4de98: StoreField: r2->field_13 = r0
    //     0xb4de98: stur            w0, [x2, #0x13]
    // 0xb4de9c: ldur            x0, [fp, #-8]
    // 0xb4dea0: LoadField: r1 = r0->field_b
    //     0xb4dea0: ldur            w1, [x0, #0xb]
    // 0xb4dea4: DecompressPointer r1
    //     0xb4dea4: add             x1, x1, HEAP, lsl #32
    // 0xb4dea8: cmp             w1, NULL
    // 0xb4deac: b.eq            #0xb4e9e0
    // 0xb4deb0: LoadField: r3 = r1->field_b
    //     0xb4deb0: ldur            w3, [x1, #0xb]
    // 0xb4deb4: DecompressPointer r3
    //     0xb4deb4: add             x3, x3, HEAP, lsl #32
    // 0xb4deb8: LoadField: r1 = r3->field_13
    //     0xb4deb8: ldur            w1, [x3, #0x13]
    // 0xb4debc: DecompressPointer r1
    //     0xb4debc: add             x1, x1, HEAP, lsl #32
    // 0xb4dec0: cmp             w1, NULL
    // 0xb4dec4: b.ne            #0xb4ded0
    // 0xb4dec8: r1 = Null
    //     0xb4dec8: mov             x1, NULL
    // 0xb4decc: b               #0xb4dedc
    // 0xb4ded0: LoadField: r3 = r1->field_7
    //     0xb4ded0: ldur            w3, [x1, #7]
    // 0xb4ded4: DecompressPointer r3
    //     0xb4ded4: add             x3, x3, HEAP, lsl #32
    // 0xb4ded8: mov             x1, x3
    // 0xb4dedc: cmp             w1, NULL
    // 0xb4dee0: b.ne            #0xb4deec
    // 0xb4dee4: r3 = ""
    //     0xb4dee4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4dee8: b               #0xb4def0
    // 0xb4deec: mov             x3, x1
    // 0xb4def0: ldur            x1, [fp, #-0x10]
    // 0xb4def4: stur            x3, [fp, #-0x20]
    // 0xb4def8: r0 = of()
    //     0xb4def8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4defc: LoadField: r1 = r0->field_87
    //     0xb4defc: ldur            w1, [x0, #0x87]
    // 0xb4df00: DecompressPointer r1
    //     0xb4df00: add             x1, x1, HEAP, lsl #32
    // 0xb4df04: LoadField: r0 = r1->field_7
    //     0xb4df04: ldur            w0, [x1, #7]
    // 0xb4df08: DecompressPointer r0
    //     0xb4df08: add             x0, x0, HEAP, lsl #32
    // 0xb4df0c: r16 = 16.000000
    //     0xb4df0c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4df10: ldr             x16, [x16, #0x188]
    // 0xb4df14: r30 = Instance_Color
    //     0xb4df14: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4df18: stp             lr, x16, [SP]
    // 0xb4df1c: mov             x1, x0
    // 0xb4df20: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4df20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4df24: ldr             x4, [x4, #0xaa0]
    // 0xb4df28: r0 = copyWith()
    //     0xb4df28: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4df2c: stur            x0, [fp, #-0x28]
    // 0xb4df30: r0 = Text()
    //     0xb4df30: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4df34: mov             x1, x0
    // 0xb4df38: ldur            x0, [fp, #-0x20]
    // 0xb4df3c: stur            x1, [fp, #-0x40]
    // 0xb4df40: StoreField: r1->field_b = r0
    //     0xb4df40: stur            w0, [x1, #0xb]
    // 0xb4df44: ldur            x0, [fp, #-0x28]
    // 0xb4df48: StoreField: r1->field_13 = r0
    //     0xb4df48: stur            w0, [x1, #0x13]
    // 0xb4df4c: r0 = Radius()
    //     0xb4df4c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb4df50: d0 = 12.000000
    //     0xb4df50: fmov            d0, #12.00000000
    // 0xb4df54: stur            x0, [fp, #-0x20]
    // 0xb4df58: StoreField: r0->field_7 = d0
    //     0xb4df58: stur            d0, [x0, #7]
    // 0xb4df5c: StoreField: r0->field_f = d0
    //     0xb4df5c: stur            d0, [x0, #0xf]
    // 0xb4df60: r0 = BorderRadius()
    //     0xb4df60: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb4df64: mov             x1, x0
    // 0xb4df68: ldur            x0, [fp, #-0x20]
    // 0xb4df6c: stur            x1, [fp, #-0x28]
    // 0xb4df70: StoreField: r1->field_7 = r0
    //     0xb4df70: stur            w0, [x1, #7]
    // 0xb4df74: StoreField: r1->field_b = r0
    //     0xb4df74: stur            w0, [x1, #0xb]
    // 0xb4df78: StoreField: r1->field_f = r0
    //     0xb4df78: stur            w0, [x1, #0xf]
    // 0xb4df7c: StoreField: r1->field_13 = r0
    //     0xb4df7c: stur            w0, [x1, #0x13]
    // 0xb4df80: r0 = BoxDecoration()
    //     0xb4df80: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb4df84: mov             x2, x0
    // 0xb4df88: r0 = Instance_Color
    //     0xb4df88: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4df8c: stur            x2, [fp, #-0x20]
    // 0xb4df90: StoreField: r2->field_7 = r0
    //     0xb4df90: stur            w0, [x2, #7]
    // 0xb4df94: ldur            x0, [fp, #-0x28]
    // 0xb4df98: StoreField: r2->field_13 = r0
    //     0xb4df98: stur            w0, [x2, #0x13]
    // 0xb4df9c: r0 = Instance_BoxShape
    //     0xb4df9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb4dfa0: ldr             x0, [x0, #0x80]
    // 0xb4dfa4: StoreField: r2->field_23 = r0
    //     0xb4dfa4: stur            w0, [x2, #0x23]
    // 0xb4dfa8: ldur            x1, [fp, #-0x10]
    // 0xb4dfac: r0 = of()
    //     0xb4dfac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4dfb0: LoadField: r1 = r0->field_87
    //     0xb4dfb0: ldur            w1, [x0, #0x87]
    // 0xb4dfb4: DecompressPointer r1
    //     0xb4dfb4: add             x1, x1, HEAP, lsl #32
    // 0xb4dfb8: LoadField: r0 = r1->field_2b
    //     0xb4dfb8: ldur            w0, [x1, #0x2b]
    // 0xb4dfbc: DecompressPointer r0
    //     0xb4dfbc: add             x0, x0, HEAP, lsl #32
    // 0xb4dfc0: stur            x0, [fp, #-0x28]
    // 0xb4dfc4: r1 = Instance_Color
    //     0xb4dfc4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4dfc8: d0 = 0.700000
    //     0xb4dfc8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4dfcc: ldr             d0, [x17, #0xf48]
    // 0xb4dfd0: r0 = withOpacity()
    //     0xb4dfd0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4dfd4: r16 = 12.000000
    //     0xb4dfd4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4dfd8: ldr             x16, [x16, #0x9e8]
    // 0xb4dfdc: stp             x0, x16, [SP]
    // 0xb4dfe0: ldur            x1, [fp, #-0x28]
    // 0xb4dfe4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4dfe4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4dfe8: ldr             x4, [x4, #0xaa0]
    // 0xb4dfec: r0 = copyWith()
    //     0xb4dfec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4dff0: stur            x0, [fp, #-0x28]
    // 0xb4dff4: r0 = Text()
    //     0xb4dff4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4dff8: mov             x1, x0
    // 0xb4dffc: r0 = "Final Price"
    //     0xb4dffc: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ab8] "Final Price"
    //     0xb4e000: ldr             x0, [x0, #0xab8]
    // 0xb4e004: stur            x1, [fp, #-0x48]
    // 0xb4e008: StoreField: r1->field_b = r0
    //     0xb4e008: stur            w0, [x1, #0xb]
    // 0xb4e00c: ldur            x0, [fp, #-0x28]
    // 0xb4e010: StoreField: r1->field_13 = r0
    //     0xb4e010: stur            w0, [x1, #0x13]
    // 0xb4e014: ldur            x0, [fp, #-8]
    // 0xb4e018: LoadField: r2 = r0->field_b
    //     0xb4e018: ldur            w2, [x0, #0xb]
    // 0xb4e01c: DecompressPointer r2
    //     0xb4e01c: add             x2, x2, HEAP, lsl #32
    // 0xb4e020: cmp             w2, NULL
    // 0xb4e024: b.eq            #0xb4e9e4
    // 0xb4e028: LoadField: r3 = r2->field_b
    //     0xb4e028: ldur            w3, [x2, #0xb]
    // 0xb4e02c: DecompressPointer r3
    //     0xb4e02c: add             x3, x3, HEAP, lsl #32
    // 0xb4e030: LoadField: r2 = r3->field_13
    //     0xb4e030: ldur            w2, [x3, #0x13]
    // 0xb4e034: DecompressPointer r2
    //     0xb4e034: add             x2, x2, HEAP, lsl #32
    // 0xb4e038: cmp             w2, NULL
    // 0xb4e03c: b.ne            #0xb4e048
    // 0xb4e040: r2 = Null
    //     0xb4e040: mov             x2, NULL
    // 0xb4e044: b               #0xb4e054
    // 0xb4e048: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb4e048: ldur            w3, [x2, #0x17]
    // 0xb4e04c: DecompressPointer r3
    //     0xb4e04c: add             x3, x3, HEAP, lsl #32
    // 0xb4e050: mov             x2, x3
    // 0xb4e054: str             x2, [SP]
    // 0xb4e058: r0 = _interpolateSingle()
    //     0xb4e058: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb4e05c: ldur            x1, [fp, #-0x10]
    // 0xb4e060: stur            x0, [fp, #-0x28]
    // 0xb4e064: r0 = of()
    //     0xb4e064: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e068: LoadField: r1 = r0->field_87
    //     0xb4e068: ldur            w1, [x0, #0x87]
    // 0xb4e06c: DecompressPointer r1
    //     0xb4e06c: add             x1, x1, HEAP, lsl #32
    // 0xb4e070: LoadField: r0 = r1->field_2b
    //     0xb4e070: ldur            w0, [x1, #0x2b]
    // 0xb4e074: DecompressPointer r0
    //     0xb4e074: add             x0, x0, HEAP, lsl #32
    // 0xb4e078: r16 = Instance_Color
    //     0xb4e078: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb4e07c: ldr             x16, [x16, #0x858]
    // 0xb4e080: r30 = 12.000000
    //     0xb4e080: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4e084: ldr             lr, [lr, #0x9e8]
    // 0xb4e088: stp             lr, x16, [SP]
    // 0xb4e08c: mov             x1, x0
    // 0xb4e090: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4e090: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4e094: ldr             x4, [x4, #0x9b8]
    // 0xb4e098: r0 = copyWith()
    //     0xb4e098: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4e09c: stur            x0, [fp, #-0x50]
    // 0xb4e0a0: r0 = Text()
    //     0xb4e0a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4e0a4: mov             x3, x0
    // 0xb4e0a8: ldur            x0, [fp, #-0x28]
    // 0xb4e0ac: stur            x3, [fp, #-0x58]
    // 0xb4e0b0: StoreField: r3->field_b = r0
    //     0xb4e0b0: stur            w0, [x3, #0xb]
    // 0xb4e0b4: ldur            x0, [fp, #-0x50]
    // 0xb4e0b8: StoreField: r3->field_13 = r0
    //     0xb4e0b8: stur            w0, [x3, #0x13]
    // 0xb4e0bc: r1 = Null
    //     0xb4e0bc: mov             x1, NULL
    // 0xb4e0c0: r2 = 4
    //     0xb4e0c0: movz            x2, #0x4
    // 0xb4e0c4: r0 = AllocateArray()
    //     0xb4e0c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e0c8: mov             x2, x0
    // 0xb4e0cc: ldur            x0, [fp, #-0x48]
    // 0xb4e0d0: stur            x2, [fp, #-0x28]
    // 0xb4e0d4: StoreField: r2->field_f = r0
    //     0xb4e0d4: stur            w0, [x2, #0xf]
    // 0xb4e0d8: ldur            x0, [fp, #-0x58]
    // 0xb4e0dc: StoreField: r2->field_13 = r0
    //     0xb4e0dc: stur            w0, [x2, #0x13]
    // 0xb4e0e0: r1 = <Widget>
    //     0xb4e0e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e0e4: r0 = AllocateGrowableArray()
    //     0xb4e0e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e0e8: mov             x1, x0
    // 0xb4e0ec: ldur            x0, [fp, #-0x28]
    // 0xb4e0f0: stur            x1, [fp, #-0x48]
    // 0xb4e0f4: StoreField: r1->field_f = r0
    //     0xb4e0f4: stur            w0, [x1, #0xf]
    // 0xb4e0f8: r2 = 4
    //     0xb4e0f8: movz            x2, #0x4
    // 0xb4e0fc: StoreField: r1->field_b = r2
    //     0xb4e0fc: stur            w2, [x1, #0xb]
    // 0xb4e100: r0 = Row()
    //     0xb4e100: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4e104: mov             x2, x0
    // 0xb4e108: r0 = Instance_Axis
    //     0xb4e108: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4e10c: stur            x2, [fp, #-0x28]
    // 0xb4e110: StoreField: r2->field_f = r0
    //     0xb4e110: stur            w0, [x2, #0xf]
    // 0xb4e114: r3 = Instance_MainAxisAlignment
    //     0xb4e114: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4e118: ldr             x3, [x3, #0xa8]
    // 0xb4e11c: StoreField: r2->field_13 = r3
    //     0xb4e11c: stur            w3, [x2, #0x13]
    // 0xb4e120: r4 = Instance_MainAxisSize
    //     0xb4e120: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4e124: ldr             x4, [x4, #0xa10]
    // 0xb4e128: ArrayStore: r2[0] = r4  ; List_4
    //     0xb4e128: stur            w4, [x2, #0x17]
    // 0xb4e12c: r5 = Instance_CrossAxisAlignment
    //     0xb4e12c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4e130: ldr             x5, [x5, #0xa18]
    // 0xb4e134: StoreField: r2->field_1b = r5
    //     0xb4e134: stur            w5, [x2, #0x1b]
    // 0xb4e138: r6 = Instance_VerticalDirection
    //     0xb4e138: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4e13c: ldr             x6, [x6, #0xa20]
    // 0xb4e140: StoreField: r2->field_23 = r6
    //     0xb4e140: stur            w6, [x2, #0x23]
    // 0xb4e144: r7 = Instance_Clip
    //     0xb4e144: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4e148: ldr             x7, [x7, #0x38]
    // 0xb4e14c: StoreField: r2->field_2b = r7
    //     0xb4e14c: stur            w7, [x2, #0x2b]
    // 0xb4e150: StoreField: r2->field_2f = rZR
    //     0xb4e150: stur            xzr, [x2, #0x2f]
    // 0xb4e154: ldur            x1, [fp, #-0x48]
    // 0xb4e158: StoreField: r2->field_b = r1
    //     0xb4e158: stur            w1, [x2, #0xb]
    // 0xb4e15c: ldur            x1, [fp, #-0x10]
    // 0xb4e160: r0 = of()
    //     0xb4e160: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e164: LoadField: r1 = r0->field_87
    //     0xb4e164: ldur            w1, [x0, #0x87]
    // 0xb4e168: DecompressPointer r1
    //     0xb4e168: add             x1, x1, HEAP, lsl #32
    // 0xb4e16c: LoadField: r0 = r1->field_2b
    //     0xb4e16c: ldur            w0, [x1, #0x2b]
    // 0xb4e170: DecompressPointer r0
    //     0xb4e170: add             x0, x0, HEAP, lsl #32
    // 0xb4e174: stur            x0, [fp, #-0x48]
    // 0xb4e178: r1 = Instance_Color
    //     0xb4e178: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4e17c: d0 = 0.700000
    //     0xb4e17c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4e180: ldr             d0, [x17, #0xf48]
    // 0xb4e184: r0 = withOpacity()
    //     0xb4e184: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4e188: r16 = 12.000000
    //     0xb4e188: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4e18c: ldr             x16, [x16, #0x9e8]
    // 0xb4e190: stp             x0, x16, [SP]
    // 0xb4e194: ldur            x1, [fp, #-0x48]
    // 0xb4e198: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4e198: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4e19c: ldr             x4, [x4, #0xaa0]
    // 0xb4e1a0: r0 = copyWith()
    //     0xb4e1a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4e1a4: stur            x0, [fp, #-0x48]
    // 0xb4e1a8: r0 = Text()
    //     0xb4e1a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4e1ac: mov             x1, x0
    // 0xb4e1b0: r0 = "Online Advance"
    //     0xb4e1b0: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ac0] "Online Advance"
    //     0xb4e1b4: ldr             x0, [x0, #0xac0]
    // 0xb4e1b8: stur            x1, [fp, #-0x50]
    // 0xb4e1bc: StoreField: r1->field_b = r0
    //     0xb4e1bc: stur            w0, [x1, #0xb]
    // 0xb4e1c0: ldur            x0, [fp, #-0x48]
    // 0xb4e1c4: StoreField: r1->field_13 = r0
    //     0xb4e1c4: stur            w0, [x1, #0x13]
    // 0xb4e1c8: ldur            x0, [fp, #-8]
    // 0xb4e1cc: LoadField: r2 = r0->field_b
    //     0xb4e1cc: ldur            w2, [x0, #0xb]
    // 0xb4e1d0: DecompressPointer r2
    //     0xb4e1d0: add             x2, x2, HEAP, lsl #32
    // 0xb4e1d4: cmp             w2, NULL
    // 0xb4e1d8: b.eq            #0xb4e9e8
    // 0xb4e1dc: LoadField: r3 = r2->field_b
    //     0xb4e1dc: ldur            w3, [x2, #0xb]
    // 0xb4e1e0: DecompressPointer r3
    //     0xb4e1e0: add             x3, x3, HEAP, lsl #32
    // 0xb4e1e4: LoadField: r2 = r3->field_13
    //     0xb4e1e4: ldur            w2, [x3, #0x13]
    // 0xb4e1e8: DecompressPointer r2
    //     0xb4e1e8: add             x2, x2, HEAP, lsl #32
    // 0xb4e1ec: cmp             w2, NULL
    // 0xb4e1f0: b.ne            #0xb4e1fc
    // 0xb4e1f4: r2 = Null
    //     0xb4e1f4: mov             x2, NULL
    // 0xb4e1f8: b               #0xb4e208
    // 0xb4e1fc: LoadField: r3 = r2->field_27
    //     0xb4e1fc: ldur            w3, [x2, #0x27]
    // 0xb4e200: DecompressPointer r3
    //     0xb4e200: add             x3, x3, HEAP, lsl #32
    // 0xb4e204: mov             x2, x3
    // 0xb4e208: str             x2, [SP]
    // 0xb4e20c: r0 = _interpolateSingle()
    //     0xb4e20c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb4e210: ldur            x1, [fp, #-0x10]
    // 0xb4e214: stur            x0, [fp, #-0x48]
    // 0xb4e218: r0 = of()
    //     0xb4e218: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e21c: LoadField: r1 = r0->field_87
    //     0xb4e21c: ldur            w1, [x0, #0x87]
    // 0xb4e220: DecompressPointer r1
    //     0xb4e220: add             x1, x1, HEAP, lsl #32
    // 0xb4e224: LoadField: r0 = r1->field_2b
    //     0xb4e224: ldur            w0, [x1, #0x2b]
    // 0xb4e228: DecompressPointer r0
    //     0xb4e228: add             x0, x0, HEAP, lsl #32
    // 0xb4e22c: stur            x0, [fp, #-0x58]
    // 0xb4e230: r1 = Instance_Color
    //     0xb4e230: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4e234: d0 = 0.700000
    //     0xb4e234: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb4e238: ldr             d0, [x17, #0xf48]
    // 0xb4e23c: r0 = withOpacity()
    //     0xb4e23c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb4e240: r16 = 12.000000
    //     0xb4e240: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4e244: ldr             x16, [x16, #0x9e8]
    // 0xb4e248: stp             x0, x16, [SP]
    // 0xb4e24c: ldur            x1, [fp, #-0x58]
    // 0xb4e250: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4e250: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4e254: ldr             x4, [x4, #0xaa0]
    // 0xb4e258: r0 = copyWith()
    //     0xb4e258: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4e25c: stur            x0, [fp, #-0x58]
    // 0xb4e260: r0 = Text()
    //     0xb4e260: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4e264: mov             x3, x0
    // 0xb4e268: ldur            x0, [fp, #-0x48]
    // 0xb4e26c: stur            x3, [fp, #-0x60]
    // 0xb4e270: StoreField: r3->field_b = r0
    //     0xb4e270: stur            w0, [x3, #0xb]
    // 0xb4e274: ldur            x0, [fp, #-0x58]
    // 0xb4e278: StoreField: r3->field_13 = r0
    //     0xb4e278: stur            w0, [x3, #0x13]
    // 0xb4e27c: r1 = Null
    //     0xb4e27c: mov             x1, NULL
    // 0xb4e280: r2 = 4
    //     0xb4e280: movz            x2, #0x4
    // 0xb4e284: r0 = AllocateArray()
    //     0xb4e284: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e288: mov             x2, x0
    // 0xb4e28c: ldur            x0, [fp, #-0x50]
    // 0xb4e290: stur            x2, [fp, #-0x48]
    // 0xb4e294: StoreField: r2->field_f = r0
    //     0xb4e294: stur            w0, [x2, #0xf]
    // 0xb4e298: ldur            x0, [fp, #-0x60]
    // 0xb4e29c: StoreField: r2->field_13 = r0
    //     0xb4e29c: stur            w0, [x2, #0x13]
    // 0xb4e2a0: r1 = <Widget>
    //     0xb4e2a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e2a4: r0 = AllocateGrowableArray()
    //     0xb4e2a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e2a8: mov             x1, x0
    // 0xb4e2ac: ldur            x0, [fp, #-0x48]
    // 0xb4e2b0: stur            x1, [fp, #-0x50]
    // 0xb4e2b4: StoreField: r1->field_f = r0
    //     0xb4e2b4: stur            w0, [x1, #0xf]
    // 0xb4e2b8: r2 = 4
    //     0xb4e2b8: movz            x2, #0x4
    // 0xb4e2bc: StoreField: r1->field_b = r2
    //     0xb4e2bc: stur            w2, [x1, #0xb]
    // 0xb4e2c0: r0 = Row()
    //     0xb4e2c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4e2c4: mov             x2, x0
    // 0xb4e2c8: r0 = Instance_Axis
    //     0xb4e2c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4e2cc: stur            x2, [fp, #-0x48]
    // 0xb4e2d0: StoreField: r2->field_f = r0
    //     0xb4e2d0: stur            w0, [x2, #0xf]
    // 0xb4e2d4: r3 = Instance_MainAxisAlignment
    //     0xb4e2d4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4e2d8: ldr             x3, [x3, #0xa8]
    // 0xb4e2dc: StoreField: r2->field_13 = r3
    //     0xb4e2dc: stur            w3, [x2, #0x13]
    // 0xb4e2e0: r4 = Instance_MainAxisSize
    //     0xb4e2e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4e2e4: ldr             x4, [x4, #0xa10]
    // 0xb4e2e8: ArrayStore: r2[0] = r4  ; List_4
    //     0xb4e2e8: stur            w4, [x2, #0x17]
    // 0xb4e2ec: r5 = Instance_CrossAxisAlignment
    //     0xb4e2ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4e2f0: ldr             x5, [x5, #0xa18]
    // 0xb4e2f4: StoreField: r2->field_1b = r5
    //     0xb4e2f4: stur            w5, [x2, #0x1b]
    // 0xb4e2f8: r6 = Instance_VerticalDirection
    //     0xb4e2f8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4e2fc: ldr             x6, [x6, #0xa20]
    // 0xb4e300: StoreField: r2->field_23 = r6
    //     0xb4e300: stur            w6, [x2, #0x23]
    // 0xb4e304: r7 = Instance_Clip
    //     0xb4e304: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4e308: ldr             x7, [x7, #0x38]
    // 0xb4e30c: StoreField: r2->field_2b = r7
    //     0xb4e30c: stur            w7, [x2, #0x2b]
    // 0xb4e310: StoreField: r2->field_2f = rZR
    //     0xb4e310: stur            xzr, [x2, #0x2f]
    // 0xb4e314: ldur            x1, [fp, #-0x50]
    // 0xb4e318: StoreField: r2->field_b = r1
    //     0xb4e318: stur            w1, [x2, #0xb]
    // 0xb4e31c: ldur            x1, [fp, #-0x10]
    // 0xb4e320: r0 = of()
    //     0xb4e320: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e324: LoadField: r1 = r0->field_5b
    //     0xb4e324: ldur            w1, [x0, #0x5b]
    // 0xb4e328: DecompressPointer r1
    //     0xb4e328: add             x1, x1, HEAP, lsl #32
    // 0xb4e32c: r0 = LoadClassIdInstr(r1)
    //     0xb4e32c: ldur            x0, [x1, #-1]
    //     0xb4e330: ubfx            x0, x0, #0xc, #0x14
    // 0xb4e334: d0 = 0.100000
    //     0xb4e334: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb4e338: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb4e338: sub             lr, x0, #0xffa
    //     0xb4e33c: ldr             lr, [x21, lr, lsl #3]
    //     0xb4e340: blr             lr
    // 0xb4e344: stur            x0, [fp, #-0x50]
    // 0xb4e348: r0 = Divider()
    //     0xb4e348: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb4e34c: mov             x2, x0
    // 0xb4e350: ldur            x0, [fp, #-0x50]
    // 0xb4e354: stur            x2, [fp, #-0x58]
    // 0xb4e358: StoreField: r2->field_1f = r0
    //     0xb4e358: stur            w0, [x2, #0x1f]
    // 0xb4e35c: ldur            x1, [fp, #-0x10]
    // 0xb4e360: r0 = of()
    //     0xb4e360: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e364: LoadField: r1 = r0->field_87
    //     0xb4e364: ldur            w1, [x0, #0x87]
    // 0xb4e368: DecompressPointer r1
    //     0xb4e368: add             x1, x1, HEAP, lsl #32
    // 0xb4e36c: LoadField: r0 = r1->field_7
    //     0xb4e36c: ldur            w0, [x1, #7]
    // 0xb4e370: DecompressPointer r0
    //     0xb4e370: add             x0, x0, HEAP, lsl #32
    // 0xb4e374: r16 = Instance_Color
    //     0xb4e374: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4e378: r30 = 12.000000
    //     0xb4e378: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4e37c: ldr             lr, [lr, #0x9e8]
    // 0xb4e380: stp             lr, x16, [SP]
    // 0xb4e384: mov             x1, x0
    // 0xb4e388: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb4e388: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb4e38c: ldr             x4, [x4, #0x9b8]
    // 0xb4e390: r0 = copyWith()
    //     0xb4e390: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4e394: stur            x0, [fp, #-0x50]
    // 0xb4e398: r0 = Text()
    //     0xb4e398: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4e39c: mov             x1, x0
    // 0xb4e3a0: r0 = "Cash to be paid on Delivery"
    //     0xb4e3a0: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ac8] "Cash to be paid on Delivery"
    //     0xb4e3a4: ldr             x0, [x0, #0xac8]
    // 0xb4e3a8: stur            x1, [fp, #-0x60]
    // 0xb4e3ac: StoreField: r1->field_b = r0
    //     0xb4e3ac: stur            w0, [x1, #0xb]
    // 0xb4e3b0: ldur            x0, [fp, #-0x50]
    // 0xb4e3b4: StoreField: r1->field_13 = r0
    //     0xb4e3b4: stur            w0, [x1, #0x13]
    // 0xb4e3b8: ldur            x0, [fp, #-8]
    // 0xb4e3bc: LoadField: r2 = r0->field_b
    //     0xb4e3bc: ldur            w2, [x0, #0xb]
    // 0xb4e3c0: DecompressPointer r2
    //     0xb4e3c0: add             x2, x2, HEAP, lsl #32
    // 0xb4e3c4: cmp             w2, NULL
    // 0xb4e3c8: b.eq            #0xb4e9ec
    // 0xb4e3cc: LoadField: r0 = r2->field_b
    //     0xb4e3cc: ldur            w0, [x2, #0xb]
    // 0xb4e3d0: DecompressPointer r0
    //     0xb4e3d0: add             x0, x0, HEAP, lsl #32
    // 0xb4e3d4: LoadField: r2 = r0->field_13
    //     0xb4e3d4: ldur            w2, [x0, #0x13]
    // 0xb4e3d8: DecompressPointer r2
    //     0xb4e3d8: add             x2, x2, HEAP, lsl #32
    // 0xb4e3dc: cmp             w2, NULL
    // 0xb4e3e0: b.ne            #0xb4e3ec
    // 0xb4e3e4: r7 = Null
    //     0xb4e3e4: mov             x7, NULL
    // 0xb4e3e8: b               #0xb4e3f8
    // 0xb4e3ec: LoadField: r0 = r2->field_2f
    //     0xb4e3ec: ldur            w0, [x2, #0x2f]
    // 0xb4e3f0: DecompressPointer r0
    //     0xb4e3f0: add             x0, x0, HEAP, lsl #32
    // 0xb4e3f4: mov             x7, x0
    // 0xb4e3f8: ldur            x6, [fp, #-0x30]
    // 0xb4e3fc: ldur            x5, [fp, #-0x38]
    // 0xb4e400: ldur            x4, [fp, #-0x40]
    // 0xb4e404: ldur            x3, [fp, #-0x28]
    // 0xb4e408: ldur            x2, [fp, #-0x48]
    // 0xb4e40c: ldur            x0, [fp, #-0x58]
    // 0xb4e410: str             x7, [SP]
    // 0xb4e414: r0 = _interpolateSingle()
    //     0xb4e414: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb4e418: ldur            x1, [fp, #-0x10]
    // 0xb4e41c: stur            x0, [fp, #-8]
    // 0xb4e420: r0 = of()
    //     0xb4e420: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e424: LoadField: r1 = r0->field_87
    //     0xb4e424: ldur            w1, [x0, #0x87]
    // 0xb4e428: DecompressPointer r1
    //     0xb4e428: add             x1, x1, HEAP, lsl #32
    // 0xb4e42c: LoadField: r0 = r1->field_7
    //     0xb4e42c: ldur            w0, [x1, #7]
    // 0xb4e430: DecompressPointer r0
    //     0xb4e430: add             x0, x0, HEAP, lsl #32
    // 0xb4e434: r16 = 12.000000
    //     0xb4e434: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4e438: ldr             x16, [x16, #0x9e8]
    // 0xb4e43c: r30 = Instance_Color
    //     0xb4e43c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4e440: stp             lr, x16, [SP]
    // 0xb4e444: mov             x1, x0
    // 0xb4e448: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4e448: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb4e44c: ldr             x4, [x4, #0xaa0]
    // 0xb4e450: r0 = copyWith()
    //     0xb4e450: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4e454: stur            x0, [fp, #-0x50]
    // 0xb4e458: r0 = Text()
    //     0xb4e458: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4e45c: mov             x3, x0
    // 0xb4e460: ldur            x0, [fp, #-8]
    // 0xb4e464: stur            x3, [fp, #-0x68]
    // 0xb4e468: StoreField: r3->field_b = r0
    //     0xb4e468: stur            w0, [x3, #0xb]
    // 0xb4e46c: ldur            x0, [fp, #-0x50]
    // 0xb4e470: StoreField: r3->field_13 = r0
    //     0xb4e470: stur            w0, [x3, #0x13]
    // 0xb4e474: r1 = Null
    //     0xb4e474: mov             x1, NULL
    // 0xb4e478: r2 = 4
    //     0xb4e478: movz            x2, #0x4
    // 0xb4e47c: r0 = AllocateArray()
    //     0xb4e47c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e480: mov             x2, x0
    // 0xb4e484: ldur            x0, [fp, #-0x60]
    // 0xb4e488: stur            x2, [fp, #-8]
    // 0xb4e48c: StoreField: r2->field_f = r0
    //     0xb4e48c: stur            w0, [x2, #0xf]
    // 0xb4e490: ldur            x0, [fp, #-0x68]
    // 0xb4e494: StoreField: r2->field_13 = r0
    //     0xb4e494: stur            w0, [x2, #0x13]
    // 0xb4e498: r1 = <Widget>
    //     0xb4e498: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e49c: r0 = AllocateGrowableArray()
    //     0xb4e49c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e4a0: mov             x1, x0
    // 0xb4e4a4: ldur            x0, [fp, #-8]
    // 0xb4e4a8: stur            x1, [fp, #-0x50]
    // 0xb4e4ac: StoreField: r1->field_f = r0
    //     0xb4e4ac: stur            w0, [x1, #0xf]
    // 0xb4e4b0: r2 = 4
    //     0xb4e4b0: movz            x2, #0x4
    // 0xb4e4b4: StoreField: r1->field_b = r2
    //     0xb4e4b4: stur            w2, [x1, #0xb]
    // 0xb4e4b8: r0 = Row()
    //     0xb4e4b8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb4e4bc: mov             x3, x0
    // 0xb4e4c0: r0 = Instance_Axis
    //     0xb4e4c0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4e4c4: stur            x3, [fp, #-8]
    // 0xb4e4c8: StoreField: r3->field_f = r0
    //     0xb4e4c8: stur            w0, [x3, #0xf]
    // 0xb4e4cc: r0 = Instance_MainAxisAlignment
    //     0xb4e4cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb4e4d0: ldr             x0, [x0, #0xa8]
    // 0xb4e4d4: StoreField: r3->field_13 = r0
    //     0xb4e4d4: stur            w0, [x3, #0x13]
    // 0xb4e4d8: r0 = Instance_MainAxisSize
    //     0xb4e4d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4e4dc: ldr             x0, [x0, #0xa10]
    // 0xb4e4e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb4e4e0: stur            w0, [x3, #0x17]
    // 0xb4e4e4: r4 = Instance_CrossAxisAlignment
    //     0xb4e4e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4e4e8: ldr             x4, [x4, #0xa18]
    // 0xb4e4ec: StoreField: r3->field_1b = r4
    //     0xb4e4ec: stur            w4, [x3, #0x1b]
    // 0xb4e4f0: r5 = Instance_VerticalDirection
    //     0xb4e4f0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4e4f4: ldr             x5, [x5, #0xa20]
    // 0xb4e4f8: StoreField: r3->field_23 = r5
    //     0xb4e4f8: stur            w5, [x3, #0x23]
    // 0xb4e4fc: r6 = Instance_Clip
    //     0xb4e4fc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4e500: ldr             x6, [x6, #0x38]
    // 0xb4e504: StoreField: r3->field_2b = r6
    //     0xb4e504: stur            w6, [x3, #0x2b]
    // 0xb4e508: StoreField: r3->field_2f = rZR
    //     0xb4e508: stur            xzr, [x3, #0x2f]
    // 0xb4e50c: ldur            x1, [fp, #-0x50]
    // 0xb4e510: StoreField: r3->field_b = r1
    //     0xb4e510: stur            w1, [x3, #0xb]
    // 0xb4e514: r1 = Null
    //     0xb4e514: mov             x1, NULL
    // 0xb4e518: r2 = 10
    //     0xb4e518: movz            x2, #0xa
    // 0xb4e51c: r0 = AllocateArray()
    //     0xb4e51c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e520: mov             x2, x0
    // 0xb4e524: ldur            x0, [fp, #-0x28]
    // 0xb4e528: stur            x2, [fp, #-0x50]
    // 0xb4e52c: StoreField: r2->field_f = r0
    //     0xb4e52c: stur            w0, [x2, #0xf]
    // 0xb4e530: r16 = Instance_SizedBox
    //     0xb4e530: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xb4e534: ldr             x16, [x16, #0x8b8]
    // 0xb4e538: StoreField: r2->field_13 = r16
    //     0xb4e538: stur            w16, [x2, #0x13]
    // 0xb4e53c: ldur            x0, [fp, #-0x48]
    // 0xb4e540: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4e540: stur            w0, [x2, #0x17]
    // 0xb4e544: ldur            x0, [fp, #-0x58]
    // 0xb4e548: StoreField: r2->field_1b = r0
    //     0xb4e548: stur            w0, [x2, #0x1b]
    // 0xb4e54c: ldur            x0, [fp, #-8]
    // 0xb4e550: StoreField: r2->field_1f = r0
    //     0xb4e550: stur            w0, [x2, #0x1f]
    // 0xb4e554: r1 = <Widget>
    //     0xb4e554: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e558: r0 = AllocateGrowableArray()
    //     0xb4e558: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e55c: mov             x1, x0
    // 0xb4e560: ldur            x0, [fp, #-0x50]
    // 0xb4e564: stur            x1, [fp, #-8]
    // 0xb4e568: StoreField: r1->field_f = r0
    //     0xb4e568: stur            w0, [x1, #0xf]
    // 0xb4e56c: r2 = 10
    //     0xb4e56c: movz            x2, #0xa
    // 0xb4e570: StoreField: r1->field_b = r2
    //     0xb4e570: stur            w2, [x1, #0xb]
    // 0xb4e574: r0 = Column()
    //     0xb4e574: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4e578: mov             x1, x0
    // 0xb4e57c: r0 = Instance_Axis
    //     0xb4e57c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4e580: stur            x1, [fp, #-0x28]
    // 0xb4e584: StoreField: r1->field_f = r0
    //     0xb4e584: stur            w0, [x1, #0xf]
    // 0xb4e588: r2 = Instance_MainAxisAlignment
    //     0xb4e588: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4e58c: ldr             x2, [x2, #0xa08]
    // 0xb4e590: StoreField: r1->field_13 = r2
    //     0xb4e590: stur            w2, [x1, #0x13]
    // 0xb4e594: r3 = Instance_MainAxisSize
    //     0xb4e594: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4e598: ldr             x3, [x3, #0xa10]
    // 0xb4e59c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb4e59c: stur            w3, [x1, #0x17]
    // 0xb4e5a0: r4 = Instance_CrossAxisAlignment
    //     0xb4e5a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb4e5a4: ldr             x4, [x4, #0xa18]
    // 0xb4e5a8: StoreField: r1->field_1b = r4
    //     0xb4e5a8: stur            w4, [x1, #0x1b]
    // 0xb4e5ac: r4 = Instance_VerticalDirection
    //     0xb4e5ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4e5b0: ldr             x4, [x4, #0xa20]
    // 0xb4e5b4: StoreField: r1->field_23 = r4
    //     0xb4e5b4: stur            w4, [x1, #0x23]
    // 0xb4e5b8: r5 = Instance_Clip
    //     0xb4e5b8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4e5bc: ldr             x5, [x5, #0x38]
    // 0xb4e5c0: StoreField: r1->field_2b = r5
    //     0xb4e5c0: stur            w5, [x1, #0x2b]
    // 0xb4e5c4: StoreField: r1->field_2f = rZR
    //     0xb4e5c4: stur            xzr, [x1, #0x2f]
    // 0xb4e5c8: ldur            x6, [fp, #-8]
    // 0xb4e5cc: StoreField: r1->field_b = r6
    //     0xb4e5cc: stur            w6, [x1, #0xb]
    // 0xb4e5d0: r0 = Container()
    //     0xb4e5d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4e5d4: stur            x0, [fp, #-8]
    // 0xb4e5d8: ldur            x16, [fp, #-0x20]
    // 0xb4e5dc: r30 = Instance_EdgeInsets
    //     0xb4e5dc: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f340] Obj!EdgeInsets@d58161
    //     0xb4e5e0: ldr             lr, [lr, #0x340]
    // 0xb4e5e4: stp             lr, x16, [SP, #8]
    // 0xb4e5e8: ldur            x16, [fp, #-0x28]
    // 0xb4e5ec: str             x16, [SP]
    // 0xb4e5f0: mov             x1, x0
    // 0xb4e5f4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb4e5f4: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb4e5f8: ldr             x4, [x4, #0xb40]
    // 0xb4e5fc: r0 = Container()
    //     0xb4e5fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4e600: r1 = Null
    //     0xb4e600: mov             x1, NULL
    // 0xb4e604: r2 = 8
    //     0xb4e604: movz            x2, #0x8
    // 0xb4e608: r0 = AllocateArray()
    //     0xb4e608: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e60c: mov             x2, x0
    // 0xb4e610: ldur            x0, [fp, #-0x40]
    // 0xb4e614: stur            x2, [fp, #-0x20]
    // 0xb4e618: StoreField: r2->field_f = r0
    //     0xb4e618: stur            w0, [x2, #0xf]
    // 0xb4e61c: r16 = Instance_SizedBox
    //     0xb4e61c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb4e620: ldr             x16, [x16, #0x8f0]
    // 0xb4e624: StoreField: r2->field_13 = r16
    //     0xb4e624: stur            w16, [x2, #0x13]
    // 0xb4e628: ldur            x0, [fp, #-8]
    // 0xb4e62c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4e62c: stur            w0, [x2, #0x17]
    // 0xb4e630: r16 = Instance_SizedBox
    //     0xb4e630: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb4e634: ldr             x16, [x16, #0x578]
    // 0xb4e638: StoreField: r2->field_1b = r16
    //     0xb4e638: stur            w16, [x2, #0x1b]
    // 0xb4e63c: r1 = <Widget>
    //     0xb4e63c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e640: r0 = AllocateGrowableArray()
    //     0xb4e640: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e644: mov             x1, x0
    // 0xb4e648: ldur            x0, [fp, #-0x20]
    // 0xb4e64c: stur            x1, [fp, #-8]
    // 0xb4e650: StoreField: r1->field_f = r0
    //     0xb4e650: stur            w0, [x1, #0xf]
    // 0xb4e654: r0 = 8
    //     0xb4e654: movz            x0, #0x8
    // 0xb4e658: StoreField: r1->field_b = r0
    //     0xb4e658: stur            w0, [x1, #0xb]
    // 0xb4e65c: r0 = Column()
    //     0xb4e65c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb4e660: mov             x3, x0
    // 0xb4e664: r0 = Instance_Axis
    //     0xb4e664: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb4e668: stur            x3, [fp, #-0x20]
    // 0xb4e66c: StoreField: r3->field_f = r0
    //     0xb4e66c: stur            w0, [x3, #0xf]
    // 0xb4e670: r0 = Instance_MainAxisAlignment
    //     0xb4e670: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4e674: ldr             x0, [x0, #0xa08]
    // 0xb4e678: StoreField: r3->field_13 = r0
    //     0xb4e678: stur            w0, [x3, #0x13]
    // 0xb4e67c: r0 = Instance_MainAxisSize
    //     0xb4e67c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb4e680: ldr             x0, [x0, #0xa10]
    // 0xb4e684: ArrayStore: r3[0] = r0  ; List_4
    //     0xb4e684: stur            w0, [x3, #0x17]
    // 0xb4e688: r0 = Instance_CrossAxisAlignment
    //     0xb4e688: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4e68c: ldr             x0, [x0, #0x890]
    // 0xb4e690: StoreField: r3->field_1b = r0
    //     0xb4e690: stur            w0, [x3, #0x1b]
    // 0xb4e694: r0 = Instance_VerticalDirection
    //     0xb4e694: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4e698: ldr             x0, [x0, #0xa20]
    // 0xb4e69c: StoreField: r3->field_23 = r0
    //     0xb4e69c: stur            w0, [x3, #0x23]
    // 0xb4e6a0: r0 = Instance_Clip
    //     0xb4e6a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4e6a4: ldr             x0, [x0, #0x38]
    // 0xb4e6a8: StoreField: r3->field_2b = r0
    //     0xb4e6a8: stur            w0, [x3, #0x2b]
    // 0xb4e6ac: StoreField: r3->field_2f = rZR
    //     0xb4e6ac: stur            xzr, [x3, #0x2f]
    // 0xb4e6b0: ldur            x0, [fp, #-8]
    // 0xb4e6b4: StoreField: r3->field_b = r0
    //     0xb4e6b4: stur            w0, [x3, #0xb]
    // 0xb4e6b8: r1 = Null
    //     0xb4e6b8: mov             x1, NULL
    // 0xb4e6bc: r2 = 10
    //     0xb4e6bc: movz            x2, #0xa
    // 0xb4e6c0: r0 = AllocateArray()
    //     0xb4e6c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e6c4: mov             x2, x0
    // 0xb4e6c8: ldur            x0, [fp, #-0x30]
    // 0xb4e6cc: stur            x2, [fp, #-8]
    // 0xb4e6d0: StoreField: r2->field_f = r0
    //     0xb4e6d0: stur            w0, [x2, #0xf]
    // 0xb4e6d4: r16 = Instance_SizedBox
    //     0xb4e6d4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb4e6d8: ldr             x16, [x16, #0x578]
    // 0xb4e6dc: StoreField: r2->field_13 = r16
    //     0xb4e6dc: stur            w16, [x2, #0x13]
    // 0xb4e6e0: ldur            x0, [fp, #-0x38]
    // 0xb4e6e4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4e6e4: stur            w0, [x2, #0x17]
    // 0xb4e6e8: r16 = Instance_SizedBox
    //     0xb4e6e8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb4e6ec: ldr             x16, [x16, #0x578]
    // 0xb4e6f0: StoreField: r2->field_1b = r16
    //     0xb4e6f0: stur            w16, [x2, #0x1b]
    // 0xb4e6f4: ldur            x0, [fp, #-0x20]
    // 0xb4e6f8: StoreField: r2->field_1f = r0
    //     0xb4e6f8: stur            w0, [x2, #0x1f]
    // 0xb4e6fc: r1 = <Widget>
    //     0xb4e6fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e700: r0 = AllocateGrowableArray()
    //     0xb4e700: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e704: mov             x1, x0
    // 0xb4e708: ldur            x0, [fp, #-8]
    // 0xb4e70c: stur            x1, [fp, #-0x20]
    // 0xb4e710: StoreField: r1->field_f = r0
    //     0xb4e710: stur            w0, [x1, #0xf]
    // 0xb4e714: r0 = 10
    //     0xb4e714: movz            x0, #0xa
    // 0xb4e718: StoreField: r1->field_b = r0
    //     0xb4e718: stur            w0, [x1, #0xb]
    // 0xb4e71c: r0 = ListView()
    //     0xb4e71c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb4e720: stur            x0, [fp, #-8]
    // 0xb4e724: r16 = Instance_BouncingScrollPhysics
    //     0xb4e724: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb4e728: ldr             x16, [x16, #0x890]
    // 0xb4e72c: str             x16, [SP]
    // 0xb4e730: mov             x1, x0
    // 0xb4e734: ldur            x2, [fp, #-0x20]
    // 0xb4e738: r4 = const [0, 0x3, 0x1, 0x2, physics, 0x2, null]
    //     0xb4e738: add             x4, PP, #0x54, lsl #12  ; [pp+0x542e8] List(7) [0, 0x3, 0x1, 0x2, "physics", 0x2, Null]
    //     0xb4e73c: ldr             x4, [x4, #0x2e8]
    // 0xb4e740: r0 = ListView()
    //     0xb4e740: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xb4e744: r0 = SizedBox()
    //     0xb4e744: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb4e748: mov             x1, x0
    // 0xb4e74c: ldur            x0, [fp, #-8]
    // 0xb4e750: stur            x1, [fp, #-0x20]
    // 0xb4e754: StoreField: r1->field_b = r0
    //     0xb4e754: stur            w0, [x1, #0xb]
    // 0xb4e758: r16 = <EdgeInsets>
    //     0xb4e758: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb4e75c: ldr             x16, [x16, #0xda0]
    // 0xb4e760: r30 = Instance_EdgeInsets
    //     0xb4e760: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb4e764: ldr             lr, [lr, #0x1f0]
    // 0xb4e768: stp             lr, x16, [SP]
    // 0xb4e76c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4e76c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4e770: r0 = all()
    //     0xb4e770: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4e774: ldur            x1, [fp, #-0x10]
    // 0xb4e778: stur            x0, [fp, #-8]
    // 0xb4e77c: r0 = of()
    //     0xb4e77c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e780: LoadField: r1 = r0->field_5b
    //     0xb4e780: ldur            w1, [x0, #0x5b]
    // 0xb4e784: DecompressPointer r1
    //     0xb4e784: add             x1, x1, HEAP, lsl #32
    // 0xb4e788: r16 = <Color>
    //     0xb4e788: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb4e78c: ldr             x16, [x16, #0xf80]
    // 0xb4e790: stp             x1, x16, [SP]
    // 0xb4e794: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4e794: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4e798: r0 = all()
    //     0xb4e798: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4e79c: stur            x0, [fp, #-0x28]
    // 0xb4e7a0: r16 = <RoundedRectangleBorder>
    //     0xb4e7a0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb4e7a4: ldr             x16, [x16, #0xf78]
    // 0xb4e7a8: r30 = Instance_RoundedRectangleBorder
    //     0xb4e7a8: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0xb4e7ac: ldr             lr, [lr, #0x888]
    // 0xb4e7b0: stp             lr, x16, [SP]
    // 0xb4e7b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb4e7b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb4e7b8: r0 = all()
    //     0xb4e7b8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb4e7bc: stur            x0, [fp, #-0x30]
    // 0xb4e7c0: r0 = ButtonStyle()
    //     0xb4e7c0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb4e7c4: mov             x1, x0
    // 0xb4e7c8: ldur            x0, [fp, #-0x28]
    // 0xb4e7cc: stur            x1, [fp, #-0x38]
    // 0xb4e7d0: StoreField: r1->field_b = r0
    //     0xb4e7d0: stur            w0, [x1, #0xb]
    // 0xb4e7d4: ldur            x0, [fp, #-8]
    // 0xb4e7d8: StoreField: r1->field_23 = r0
    //     0xb4e7d8: stur            w0, [x1, #0x23]
    // 0xb4e7dc: ldur            x0, [fp, #-0x30]
    // 0xb4e7e0: StoreField: r1->field_43 = r0
    //     0xb4e7e0: stur            w0, [x1, #0x43]
    // 0xb4e7e4: r0 = TextButtonThemeData()
    //     0xb4e7e4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb4e7e8: mov             x2, x0
    // 0xb4e7ec: ldur            x0, [fp, #-0x38]
    // 0xb4e7f0: stur            x2, [fp, #-8]
    // 0xb4e7f4: StoreField: r2->field_7 = r0
    //     0xb4e7f4: stur            w0, [x2, #7]
    // 0xb4e7f8: ldur            x1, [fp, #-0x10]
    // 0xb4e7fc: r0 = of()
    //     0xb4e7fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb4e800: LoadField: r1 = r0->field_87
    //     0xb4e800: ldur            w1, [x0, #0x87]
    // 0xb4e804: DecompressPointer r1
    //     0xb4e804: add             x1, x1, HEAP, lsl #32
    // 0xb4e808: LoadField: r0 = r1->field_27
    //     0xb4e808: ldur            w0, [x1, #0x27]
    // 0xb4e80c: DecompressPointer r0
    //     0xb4e80c: add             x0, x0, HEAP, lsl #32
    // 0xb4e810: r16 = 16.000000
    //     0xb4e810: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb4e814: ldr             x16, [x16, #0x188]
    // 0xb4e818: r30 = Instance_Color
    //     0xb4e818: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4e81c: stp             lr, x16, [SP, #8]
    // 0xb4e820: r16 = Instance_FontWeight
    //     0xb4e820: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb4e824: ldr             x16, [x16, #0x20]
    // 0xb4e828: str             x16, [SP]
    // 0xb4e82c: mov             x1, x0
    // 0xb4e830: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb4e830: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb4e834: ldr             x4, [x4, #0xc48]
    // 0xb4e838: r0 = copyWith()
    //     0xb4e838: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb4e83c: stur            x0, [fp, #-0x10]
    // 0xb4e840: r0 = Text()
    //     0xb4e840: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb4e844: mov             x3, x0
    // 0xb4e848: r0 = "Okay, Got It"
    //     0xb4e848: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cc18] "Okay, Got It"
    //     0xb4e84c: ldr             x0, [x0, #0xc18]
    // 0xb4e850: stur            x3, [fp, #-0x28]
    // 0xb4e854: StoreField: r3->field_b = r0
    //     0xb4e854: stur            w0, [x3, #0xb]
    // 0xb4e858: ldur            x0, [fp, #-0x10]
    // 0xb4e85c: StoreField: r3->field_13 = r0
    //     0xb4e85c: stur            w0, [x3, #0x13]
    // 0xb4e860: ldur            x2, [fp, #-0x18]
    // 0xb4e864: r1 = Function '<anonymous closure>':.
    //     0xb4e864: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ad0] AnonymousClosure: (0xb4ea10), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/partial_popup_bottom_sheet.dart] _PartialCodPopupBottomSheet::build (0xb4dbd4)
    //     0xb4e868: ldr             x1, [x1, #0xad0]
    // 0xb4e86c: r0 = AllocateClosure()
    //     0xb4e86c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb4e870: stur            x0, [fp, #-0x10]
    // 0xb4e874: r0 = TextButton()
    //     0xb4e874: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb4e878: mov             x1, x0
    // 0xb4e87c: ldur            x0, [fp, #-0x10]
    // 0xb4e880: stur            x1, [fp, #-0x18]
    // 0xb4e884: StoreField: r1->field_b = r0
    //     0xb4e884: stur            w0, [x1, #0xb]
    // 0xb4e888: r0 = false
    //     0xb4e888: add             x0, NULL, #0x30  ; false
    // 0xb4e88c: StoreField: r1->field_27 = r0
    //     0xb4e88c: stur            w0, [x1, #0x27]
    // 0xb4e890: r0 = true
    //     0xb4e890: add             x0, NULL, #0x20  ; true
    // 0xb4e894: StoreField: r1->field_2f = r0
    //     0xb4e894: stur            w0, [x1, #0x2f]
    // 0xb4e898: ldur            x0, [fp, #-0x28]
    // 0xb4e89c: StoreField: r1->field_37 = r0
    //     0xb4e89c: stur            w0, [x1, #0x37]
    // 0xb4e8a0: r0 = TextButtonTheme()
    //     0xb4e8a0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb4e8a4: mov             x1, x0
    // 0xb4e8a8: ldur            x0, [fp, #-8]
    // 0xb4e8ac: stur            x1, [fp, #-0x10]
    // 0xb4e8b0: StoreField: r1->field_f = r0
    //     0xb4e8b0: stur            w0, [x1, #0xf]
    // 0xb4e8b4: ldur            x0, [fp, #-0x18]
    // 0xb4e8b8: StoreField: r1->field_b = r0
    //     0xb4e8b8: stur            w0, [x1, #0xb]
    // 0xb4e8bc: r0 = SizedBox()
    //     0xb4e8bc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb4e8c0: mov             x1, x0
    // 0xb4e8c4: r0 = inf
    //     0xb4e8c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb4e8c8: ldr             x0, [x0, #0x9f8]
    // 0xb4e8cc: stur            x1, [fp, #-8]
    // 0xb4e8d0: StoreField: r1->field_f = r0
    //     0xb4e8d0: stur            w0, [x1, #0xf]
    // 0xb4e8d4: ldur            x0, [fp, #-0x10]
    // 0xb4e8d8: StoreField: r1->field_b = r0
    //     0xb4e8d8: stur            w0, [x1, #0xb]
    // 0xb4e8dc: r0 = Container()
    //     0xb4e8dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb4e8e0: stur            x0, [fp, #-0x10]
    // 0xb4e8e4: r16 = Instance_EdgeInsets
    //     0xb4e8e4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb4e8e8: ldr             x16, [x16, #0xa00]
    // 0xb4e8ec: ldur            lr, [fp, #-8]
    // 0xb4e8f0: stp             lr, x16, [SP]
    // 0xb4e8f4: mov             x1, x0
    // 0xb4e8f8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb4e8f8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb4e8fc: ldr             x4, [x4, #0x30]
    // 0xb4e900: r0 = Container()
    //     0xb4e900: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb4e904: r1 = <StackParentData>
    //     0xb4e904: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb4e908: ldr             x1, [x1, #0x8e0]
    // 0xb4e90c: r0 = Positioned()
    //     0xb4e90c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb4e910: mov             x3, x0
    // 0xb4e914: r0 = 0.000000
    //     0xb4e914: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb4e918: stur            x3, [fp, #-8]
    // 0xb4e91c: StoreField: r3->field_13 = r0
    //     0xb4e91c: stur            w0, [x3, #0x13]
    // 0xb4e920: StoreField: r3->field_1b = r0
    //     0xb4e920: stur            w0, [x3, #0x1b]
    // 0xb4e924: StoreField: r3->field_1f = r0
    //     0xb4e924: stur            w0, [x3, #0x1f]
    // 0xb4e928: ldur            x0, [fp, #-0x10]
    // 0xb4e92c: StoreField: r3->field_b = r0
    //     0xb4e92c: stur            w0, [x3, #0xb]
    // 0xb4e930: r1 = Null
    //     0xb4e930: mov             x1, NULL
    // 0xb4e934: r2 = 4
    //     0xb4e934: movz            x2, #0x4
    // 0xb4e938: r0 = AllocateArray()
    //     0xb4e938: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4e93c: mov             x2, x0
    // 0xb4e940: ldur            x0, [fp, #-0x20]
    // 0xb4e944: stur            x2, [fp, #-0x10]
    // 0xb4e948: StoreField: r2->field_f = r0
    //     0xb4e948: stur            w0, [x2, #0xf]
    // 0xb4e94c: ldur            x0, [fp, #-8]
    // 0xb4e950: StoreField: r2->field_13 = r0
    //     0xb4e950: stur            w0, [x2, #0x13]
    // 0xb4e954: r1 = <Widget>
    //     0xb4e954: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb4e958: r0 = AllocateGrowableArray()
    //     0xb4e958: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb4e95c: mov             x1, x0
    // 0xb4e960: ldur            x0, [fp, #-0x10]
    // 0xb4e964: stur            x1, [fp, #-8]
    // 0xb4e968: StoreField: r1->field_f = r0
    //     0xb4e968: stur            w0, [x1, #0xf]
    // 0xb4e96c: r0 = 4
    //     0xb4e96c: movz            x0, #0x4
    // 0xb4e970: StoreField: r1->field_b = r0
    //     0xb4e970: stur            w0, [x1, #0xb]
    // 0xb4e974: r0 = Stack()
    //     0xb4e974: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb4e978: mov             x1, x0
    // 0xb4e97c: r0 = Instance_AlignmentDirectional
    //     0xb4e97c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xb4e980: ldr             x0, [x0, #0xd08]
    // 0xb4e984: stur            x1, [fp, #-0x10]
    // 0xb4e988: StoreField: r1->field_f = r0
    //     0xb4e988: stur            w0, [x1, #0xf]
    // 0xb4e98c: r0 = Instance_StackFit
    //     0xb4e98c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb4e990: ldr             x0, [x0, #0xfa8]
    // 0xb4e994: ArrayStore: r1[0] = r0  ; List_4
    //     0xb4e994: stur            w0, [x1, #0x17]
    // 0xb4e998: r0 = Instance_Clip
    //     0xb4e998: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb4e99c: ldr             x0, [x0, #0x7e0]
    // 0xb4e9a0: StoreField: r1->field_1b = r0
    //     0xb4e9a0: stur            w0, [x1, #0x1b]
    // 0xb4e9a4: ldur            x0, [fp, #-8]
    // 0xb4e9a8: StoreField: r1->field_b = r0
    //     0xb4e9a8: stur            w0, [x1, #0xb]
    // 0xb4e9ac: r0 = Padding()
    //     0xb4e9ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4e9b0: r1 = Instance_EdgeInsets
    //     0xb4e9b0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56ad8] Obj!EdgeInsets@d58dc1
    //     0xb4e9b4: ldr             x1, [x1, #0xad8]
    // 0xb4e9b8: StoreField: r0->field_f = r1
    //     0xb4e9b8: stur            w1, [x0, #0xf]
    // 0xb4e9bc: ldur            x1, [fp, #-0x10]
    // 0xb4e9c0: StoreField: r0->field_b = r1
    //     0xb4e9c0: stur            w1, [x0, #0xb]
    // 0xb4e9c4: LeaveFrame
    //     0xb4e9c4: mov             SP, fp
    //     0xb4e9c8: ldp             fp, lr, [SP], #0x10
    // 0xb4e9cc: ret
    //     0xb4e9cc: ret             
    // 0xb4e9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4e9d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4e9d4: b               #0xb4dbfc
    // 0xb4e9d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4e9d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4e9dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4e9dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4e9e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4e9e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4e9e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4e9e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4e9e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4e9e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4e9ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4e9ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb4ea10, size: 0xac
    // 0xb4ea10: EnterFrame
    //     0xb4ea10: stp             fp, lr, [SP, #-0x10]!
    //     0xb4ea14: mov             fp, SP
    // 0xb4ea18: AllocStack(0x10)
    //     0xb4ea18: sub             SP, SP, #0x10
    // 0xb4ea1c: SetupParameters()
    //     0xb4ea1c: ldr             x0, [fp, #0x10]
    //     0xb4ea20: ldur            w1, [x0, #0x17]
    //     0xb4ea24: add             x1, x1, HEAP, lsl #32
    //     0xb4ea28: stur            x1, [fp, #-8]
    // 0xb4ea2c: CheckStackOverflow
    //     0xb4ea2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb4ea30: cmp             SP, x16
    //     0xb4ea34: b.ls            #0xb4eab0
    // 0xb4ea38: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb4ea38: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb4ea3c: ldr             x0, [x0, #0x1c80]
    //     0xb4ea40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb4ea44: cmp             w0, w16
    //     0xb4ea48: b.ne            #0xb4ea54
    //     0xb4ea4c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb4ea50: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb4ea54: str             NULL, [SP]
    // 0xb4ea58: r4 = const [0x1, 0, 0, 0, null]
    //     0xb4ea58: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb4ea5c: r0 = GetNavigation.back()
    //     0xb4ea5c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb4ea60: ldur            x0, [fp, #-8]
    // 0xb4ea64: LoadField: r1 = r0->field_f
    //     0xb4ea64: ldur            w1, [x0, #0xf]
    // 0xb4ea68: DecompressPointer r1
    //     0xb4ea68: add             x1, x1, HEAP, lsl #32
    // 0xb4ea6c: LoadField: r0 = r1->field_b
    //     0xb4ea6c: ldur            w0, [x1, #0xb]
    // 0xb4ea70: DecompressPointer r0
    //     0xb4ea70: add             x0, x0, HEAP, lsl #32
    // 0xb4ea74: cmp             w0, NULL
    // 0xb4ea78: b.eq            #0xb4eab8
    // 0xb4ea7c: LoadField: r1 = r0->field_f
    //     0xb4ea7c: ldur            w1, [x0, #0xf]
    // 0xb4ea80: DecompressPointer r1
    //     0xb4ea80: add             x1, x1, HEAP, lsl #32
    // 0xb4ea84: str             x1, [SP]
    // 0xb4ea88: r4 = 0
    //     0xb4ea88: movz            x4, #0
    // 0xb4ea8c: ldr             x0, [SP]
    // 0xb4ea90: r16 = UnlinkedCall_0x613b5c
    //     0xb4ea90: add             x16, PP, #0x56, lsl #12  ; [pp+0x56ae0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4ea94: add             x16, x16, #0xae0
    // 0xb4ea98: ldp             x5, lr, [x16]
    // 0xb4ea9c: blr             lr
    // 0xb4eaa0: r0 = Null
    //     0xb4eaa0: mov             x0, NULL
    // 0xb4eaa4: LeaveFrame
    //     0xb4eaa4: mov             SP, fp
    //     0xb4eaa8: ldp             fp, lr, [SP], #0x10
    // 0xb4eaac: ret
    //     0xb4eaac: ret             
    // 0xb4eab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb4eab0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4eab4: b               #0xb4ea38
    // 0xb4eab8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4eab8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4094, size: 0x14, field offset: 0xc
//   const constructor, 
class PartialCodPopupBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7eef8, size: 0x24
    // 0xc7eef8: EnterFrame
    //     0xc7eef8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7eefc: mov             fp, SP
    // 0xc7ef00: mov             x0, x1
    // 0xc7ef04: r1 = <PartialCodPopupBottomSheet>
    //     0xc7ef04: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a30] TypeArguments: <PartialCodPopupBottomSheet>
    //     0xc7ef08: ldr             x1, [x1, #0xa30]
    // 0xc7ef0c: r0 = _PartialCodPopupBottomSheet()
    //     0xc7ef0c: bl              #0xc7ef1c  ; Allocate_PartialCodPopupBottomSheetStub -> _PartialCodPopupBottomSheet (size=0x14)
    // 0xc7ef10: LeaveFrame
    //     0xc7ef10: mov             SP, fp
    //     0xc7ef14: ldp             fp, lr, [SP], #0x10
    // 0xc7ef18: ret
    //     0xc7ef18: ret             
  }
}
