// lib: , url: package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart

// class id: 1049349, size: 0x8
class :: {
}

// class id: 3376, size: 0x14, field offset: 0x14
class _OffersContentItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb35ba0, size: 0x2b8
    // 0xb35ba0: EnterFrame
    //     0xb35ba0: stp             fp, lr, [SP, #-0x10]!
    //     0xb35ba4: mov             fp, SP
    // 0xb35ba8: AllocStack(0x40)
    //     0xb35ba8: sub             SP, SP, #0x40
    // 0xb35bac: SetupParameters(_OffersContentItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb35bac: mov             x0, x1
    //     0xb35bb0: stur            x1, [fp, #-8]
    //     0xb35bb4: mov             x1, x2
    //     0xb35bb8: stur            x2, [fp, #-0x10]
    // 0xb35bbc: CheckStackOverflow
    //     0xb35bbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb35bc0: cmp             SP, x16
    //     0xb35bc4: b.ls            #0xb35e48
    // 0xb35bc8: r1 = 1
    //     0xb35bc8: movz            x1, #0x1
    // 0xb35bcc: r0 = AllocateContext()
    //     0xb35bcc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb35bd0: mov             x2, x0
    // 0xb35bd4: ldur            x0, [fp, #-8]
    // 0xb35bd8: stur            x2, [fp, #-0x28]
    // 0xb35bdc: StoreField: r2->field_f = r0
    //     0xb35bdc: stur            w0, [x2, #0xf]
    // 0xb35be0: LoadField: r1 = r0->field_b
    //     0xb35be0: ldur            w1, [x0, #0xb]
    // 0xb35be4: DecompressPointer r1
    //     0xb35be4: add             x1, x1, HEAP, lsl #32
    // 0xb35be8: cmp             w1, NULL
    // 0xb35bec: b.eq            #0xb35e50
    // 0xb35bf0: LoadField: r3 = r1->field_f
    //     0xb35bf0: ldur            w3, [x1, #0xf]
    // 0xb35bf4: DecompressPointer r3
    //     0xb35bf4: add             x3, x3, HEAP, lsl #32
    // 0xb35bf8: cmp             w3, NULL
    // 0xb35bfc: b.ne            #0xb35c08
    // 0xb35c00: r1 = Null
    //     0xb35c00: mov             x1, NULL
    // 0xb35c04: b               #0xb35c24
    // 0xb35c08: LoadField: r1 = r3->field_f
    //     0xb35c08: ldur            w1, [x3, #0xf]
    // 0xb35c0c: DecompressPointer r1
    //     0xb35c0c: add             x1, x1, HEAP, lsl #32
    // 0xb35c10: LoadField: r4 = r1->field_b
    //     0xb35c10: ldur            w4, [x1, #0xb]
    // 0xb35c14: cbnz            w4, #0xb35c20
    // 0xb35c18: r1 = false
    //     0xb35c18: add             x1, NULL, #0x30  ; false
    // 0xb35c1c: b               #0xb35c24
    // 0xb35c20: r1 = true
    //     0xb35c20: add             x1, NULL, #0x20  ; true
    // 0xb35c24: cmp             w1, NULL
    // 0xb35c28: b.ne            #0xb35c34
    // 0xb35c2c: r4 = false
    //     0xb35c2c: add             x4, NULL, #0x30  ; false
    // 0xb35c30: b               #0xb35c38
    // 0xb35c34: mov             x4, x1
    // 0xb35c38: stur            x4, [fp, #-0x20]
    // 0xb35c3c: cmp             w3, NULL
    // 0xb35c40: b.ne            #0xb35c4c
    // 0xb35c44: r1 = Null
    //     0xb35c44: mov             x1, NULL
    // 0xb35c48: b               #0xb35c54
    // 0xb35c4c: LoadField: r1 = r3->field_b
    //     0xb35c4c: ldur            w1, [x3, #0xb]
    // 0xb35c50: DecompressPointer r1
    //     0xb35c50: add             x1, x1, HEAP, lsl #32
    // 0xb35c54: cmp             w1, NULL
    // 0xb35c58: b.ne            #0xb35c64
    // 0xb35c5c: r3 = ""
    //     0xb35c5c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb35c60: b               #0xb35c68
    // 0xb35c64: mov             x3, x1
    // 0xb35c68: ldur            x1, [fp, #-0x10]
    // 0xb35c6c: stur            x3, [fp, #-0x18]
    // 0xb35c70: r0 = of()
    //     0xb35c70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb35c74: LoadField: r1 = r0->field_87
    //     0xb35c74: ldur            w1, [x0, #0x87]
    // 0xb35c78: DecompressPointer r1
    //     0xb35c78: add             x1, x1, HEAP, lsl #32
    // 0xb35c7c: LoadField: r0 = r1->field_2b
    //     0xb35c7c: ldur            w0, [x1, #0x2b]
    // 0xb35c80: DecompressPointer r0
    //     0xb35c80: add             x0, x0, HEAP, lsl #32
    // 0xb35c84: r16 = 14.000000
    //     0xb35c84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb35c88: ldr             x16, [x16, #0x1d8]
    // 0xb35c8c: r30 = Instance_Color
    //     0xb35c8c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb35c90: stp             lr, x16, [SP]
    // 0xb35c94: mov             x1, x0
    // 0xb35c98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb35c98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb35c9c: ldr             x4, [x4, #0xaa0]
    // 0xb35ca0: r0 = copyWith()
    //     0xb35ca0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb35ca4: stur            x0, [fp, #-0x10]
    // 0xb35ca8: r0 = Text()
    //     0xb35ca8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb35cac: mov             x1, x0
    // 0xb35cb0: ldur            x0, [fp, #-0x18]
    // 0xb35cb4: stur            x1, [fp, #-0x30]
    // 0xb35cb8: StoreField: r1->field_b = r0
    //     0xb35cb8: stur            w0, [x1, #0xb]
    // 0xb35cbc: ldur            x0, [fp, #-0x10]
    // 0xb35cc0: StoreField: r1->field_13 = r0
    //     0xb35cc0: stur            w0, [x1, #0x13]
    // 0xb35cc4: r0 = Padding()
    //     0xb35cc4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb35cc8: mov             x1, x0
    // 0xb35ccc: r0 = Instance_EdgeInsets
    //     0xb35ccc: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb35cd0: ldr             x0, [x0, #0xf70]
    // 0xb35cd4: stur            x1, [fp, #-0x10]
    // 0xb35cd8: StoreField: r1->field_f = r0
    //     0xb35cd8: stur            w0, [x1, #0xf]
    // 0xb35cdc: ldur            x0, [fp, #-0x30]
    // 0xb35ce0: StoreField: r1->field_b = r0
    //     0xb35ce0: stur            w0, [x1, #0xb]
    // 0xb35ce4: r0 = Visibility()
    //     0xb35ce4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb35ce8: mov             x3, x0
    // 0xb35cec: ldur            x0, [fp, #-0x10]
    // 0xb35cf0: stur            x3, [fp, #-0x18]
    // 0xb35cf4: StoreField: r3->field_b = r0
    //     0xb35cf4: stur            w0, [x3, #0xb]
    // 0xb35cf8: r0 = Instance_SizedBox
    //     0xb35cf8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb35cfc: StoreField: r3->field_f = r0
    //     0xb35cfc: stur            w0, [x3, #0xf]
    // 0xb35d00: ldur            x0, [fp, #-0x20]
    // 0xb35d04: StoreField: r3->field_13 = r0
    //     0xb35d04: stur            w0, [x3, #0x13]
    // 0xb35d08: r0 = false
    //     0xb35d08: add             x0, NULL, #0x30  ; false
    // 0xb35d0c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb35d0c: stur            w0, [x3, #0x17]
    // 0xb35d10: StoreField: r3->field_1b = r0
    //     0xb35d10: stur            w0, [x3, #0x1b]
    // 0xb35d14: StoreField: r3->field_1f = r0
    //     0xb35d14: stur            w0, [x3, #0x1f]
    // 0xb35d18: StoreField: r3->field_23 = r0
    //     0xb35d18: stur            w0, [x3, #0x23]
    // 0xb35d1c: StoreField: r3->field_27 = r0
    //     0xb35d1c: stur            w0, [x3, #0x27]
    // 0xb35d20: StoreField: r3->field_2b = r0
    //     0xb35d20: stur            w0, [x3, #0x2b]
    // 0xb35d24: ldur            x0, [fp, #-8]
    // 0xb35d28: LoadField: r1 = r0->field_b
    //     0xb35d28: ldur            w1, [x0, #0xb]
    // 0xb35d2c: DecompressPointer r1
    //     0xb35d2c: add             x1, x1, HEAP, lsl #32
    // 0xb35d30: cmp             w1, NULL
    // 0xb35d34: b.eq            #0xb35e54
    // 0xb35d38: LoadField: r0 = r1->field_f
    //     0xb35d38: ldur            w0, [x1, #0xf]
    // 0xb35d3c: DecompressPointer r0
    //     0xb35d3c: add             x0, x0, HEAP, lsl #32
    // 0xb35d40: cmp             w0, NULL
    // 0xb35d44: b.ne            #0xb35d50
    // 0xb35d48: r0 = Null
    //     0xb35d48: mov             x0, NULL
    // 0xb35d4c: b               #0xb35d5c
    // 0xb35d50: LoadField: r1 = r0->field_f
    //     0xb35d50: ldur            w1, [x0, #0xf]
    // 0xb35d54: DecompressPointer r1
    //     0xb35d54: add             x1, x1, HEAP, lsl #32
    // 0xb35d58: LoadField: r0 = r1->field_b
    //     0xb35d58: ldur            w0, [x1, #0xb]
    // 0xb35d5c: ldur            x2, [fp, #-0x28]
    // 0xb35d60: stur            x0, [fp, #-8]
    // 0xb35d64: r1 = Function '<anonymous closure>':.
    //     0xb35d64: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aad0] AnonymousClosure: (0xb35e7c), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb35d68: ldr             x1, [x1, #0xad0]
    // 0xb35d6c: r0 = AllocateClosure()
    //     0xb35d6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb35d70: stur            x0, [fp, #-0x10]
    // 0xb35d74: r0 = ListView()
    //     0xb35d74: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb35d78: stur            x0, [fp, #-0x20]
    // 0xb35d7c: r16 = true
    //     0xb35d7c: add             x16, NULL, #0x20  ; true
    // 0xb35d80: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb35d80: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb35d84: ldr             lr, [lr, #0x1c8]
    // 0xb35d88: stp             lr, x16, [SP]
    // 0xb35d8c: mov             x1, x0
    // 0xb35d90: ldur            x2, [fp, #-0x10]
    // 0xb35d94: ldur            x3, [fp, #-8]
    // 0xb35d98: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb35d98: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb35d9c: ldr             x4, [x4, #8]
    // 0xb35da0: r0 = ListView.builder()
    //     0xb35da0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb35da4: r1 = Null
    //     0xb35da4: mov             x1, NULL
    // 0xb35da8: r2 = 4
    //     0xb35da8: movz            x2, #0x4
    // 0xb35dac: r0 = AllocateArray()
    //     0xb35dac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb35db0: mov             x2, x0
    // 0xb35db4: ldur            x0, [fp, #-0x18]
    // 0xb35db8: stur            x2, [fp, #-8]
    // 0xb35dbc: StoreField: r2->field_f = r0
    //     0xb35dbc: stur            w0, [x2, #0xf]
    // 0xb35dc0: ldur            x0, [fp, #-0x20]
    // 0xb35dc4: StoreField: r2->field_13 = r0
    //     0xb35dc4: stur            w0, [x2, #0x13]
    // 0xb35dc8: r1 = <Widget>
    //     0xb35dc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb35dcc: r0 = AllocateGrowableArray()
    //     0xb35dcc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb35dd0: mov             x1, x0
    // 0xb35dd4: ldur            x0, [fp, #-8]
    // 0xb35dd8: stur            x1, [fp, #-0x10]
    // 0xb35ddc: StoreField: r1->field_f = r0
    //     0xb35ddc: stur            w0, [x1, #0xf]
    // 0xb35de0: r0 = 4
    //     0xb35de0: movz            x0, #0x4
    // 0xb35de4: StoreField: r1->field_b = r0
    //     0xb35de4: stur            w0, [x1, #0xb]
    // 0xb35de8: r0 = Column()
    //     0xb35de8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb35dec: r1 = Instance_Axis
    //     0xb35dec: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb35df0: StoreField: r0->field_f = r1
    //     0xb35df0: stur            w1, [x0, #0xf]
    // 0xb35df4: r1 = Instance_MainAxisAlignment
    //     0xb35df4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb35df8: ldr             x1, [x1, #0xa08]
    // 0xb35dfc: StoreField: r0->field_13 = r1
    //     0xb35dfc: stur            w1, [x0, #0x13]
    // 0xb35e00: r1 = Instance_MainAxisSize
    //     0xb35e00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb35e04: ldr             x1, [x1, #0xa10]
    // 0xb35e08: ArrayStore: r0[0] = r1  ; List_4
    //     0xb35e08: stur            w1, [x0, #0x17]
    // 0xb35e0c: r1 = Instance_CrossAxisAlignment
    //     0xb35e0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb35e10: ldr             x1, [x1, #0x890]
    // 0xb35e14: StoreField: r0->field_1b = r1
    //     0xb35e14: stur            w1, [x0, #0x1b]
    // 0xb35e18: r1 = Instance_VerticalDirection
    //     0xb35e18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb35e1c: ldr             x1, [x1, #0xa20]
    // 0xb35e20: StoreField: r0->field_23 = r1
    //     0xb35e20: stur            w1, [x0, #0x23]
    // 0xb35e24: r1 = Instance_Clip
    //     0xb35e24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb35e28: ldr             x1, [x1, #0x38]
    // 0xb35e2c: StoreField: r0->field_2b = r1
    //     0xb35e2c: stur            w1, [x0, #0x2b]
    // 0xb35e30: StoreField: r0->field_2f = rZR
    //     0xb35e30: stur            xzr, [x0, #0x2f]
    // 0xb35e34: ldur            x1, [fp, #-0x10]
    // 0xb35e38: StoreField: r0->field_b = r1
    //     0xb35e38: stur            w1, [x0, #0xb]
    // 0xb35e3c: LeaveFrame
    //     0xb35e3c: mov             SP, fp
    //     0xb35e40: ldp             fp, lr, [SP], #0x10
    // 0xb35e44: ret
    //     0xb35e44: ret             
    // 0xb35e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb35e48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb35e4c: b               #0xb35bc8
    // 0xb35e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb35e7c, size: 0x2124
    // 0xb35e7c: EnterFrame
    //     0xb35e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xb35e80: mov             fp, SP
    // 0xb35e84: AllocStack(0x80)
    //     0xb35e84: sub             SP, SP, #0x80
    // 0xb35e88: SetupParameters()
    //     0xb35e88: ldr             x0, [fp, #0x20]
    //     0xb35e8c: ldur            w1, [x0, #0x17]
    //     0xb35e90: add             x1, x1, HEAP, lsl #32
    //     0xb35e94: stur            x1, [fp, #-8]
    // 0xb35e98: CheckStackOverflow
    //     0xb35e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb35e9c: cmp             SP, x16
    //     0xb35ea0: b.ls            #0xb37ecc
    // 0xb35ea4: r1 = 2
    //     0xb35ea4: movz            x1, #0x2
    // 0xb35ea8: r0 = AllocateContext()
    //     0xb35ea8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb35eac: mov             x3, x0
    // 0xb35eb0: ldur            x2, [fp, #-8]
    // 0xb35eb4: stur            x3, [fp, #-0x10]
    // 0xb35eb8: StoreField: r3->field_b = r2
    //     0xb35eb8: stur            w2, [x3, #0xb]
    // 0xb35ebc: ldr             x0, [fp, #0x18]
    // 0xb35ec0: StoreField: r3->field_f = r0
    //     0xb35ec0: stur            w0, [x3, #0xf]
    // 0xb35ec4: ldr             x0, [fp, #0x10]
    // 0xb35ec8: StoreField: r3->field_13 = r0
    //     0xb35ec8: stur            w0, [x3, #0x13]
    // 0xb35ecc: LoadField: r1 = r2->field_f
    //     0xb35ecc: ldur            w1, [x2, #0xf]
    // 0xb35ed0: DecompressPointer r1
    //     0xb35ed0: add             x1, x1, HEAP, lsl #32
    // 0xb35ed4: LoadField: r4 = r1->field_b
    //     0xb35ed4: ldur            w4, [x1, #0xb]
    // 0xb35ed8: DecompressPointer r4
    //     0xb35ed8: add             x4, x4, HEAP, lsl #32
    // 0xb35edc: cmp             w4, NULL
    // 0xb35ee0: b.eq            #0xb37ed4
    // 0xb35ee4: LoadField: r1 = r4->field_f
    //     0xb35ee4: ldur            w1, [x4, #0xf]
    // 0xb35ee8: DecompressPointer r1
    //     0xb35ee8: add             x1, x1, HEAP, lsl #32
    // 0xb35eec: cmp             w1, NULL
    // 0xb35ef0: b.ne            #0xb35efc
    // 0xb35ef4: r0 = Null
    //     0xb35ef4: mov             x0, NULL
    // 0xb35ef8: b               #0xb35f40
    // 0xb35efc: LoadField: r4 = r1->field_f
    //     0xb35efc: ldur            w4, [x1, #0xf]
    // 0xb35f00: DecompressPointer r4
    //     0xb35f00: add             x4, x4, HEAP, lsl #32
    // 0xb35f04: LoadField: r1 = r4->field_b
    //     0xb35f04: ldur            w1, [x4, #0xb]
    // 0xb35f08: r5 = LoadInt32Instr(r0)
    //     0xb35f08: sbfx            x5, x0, #1, #0x1f
    //     0xb35f0c: tbz             w0, #0, #0xb35f14
    //     0xb35f10: ldur            x5, [x0, #7]
    // 0xb35f14: r0 = LoadInt32Instr(r1)
    //     0xb35f14: sbfx            x0, x1, #1, #0x1f
    // 0xb35f18: mov             x1, x5
    // 0xb35f1c: cmp             x1, x0
    // 0xb35f20: b.hs            #0xb37ed8
    // 0xb35f24: LoadField: r0 = r4->field_f
    //     0xb35f24: ldur            w0, [x4, #0xf]
    // 0xb35f28: DecompressPointer r0
    //     0xb35f28: add             x0, x0, HEAP, lsl #32
    // 0xb35f2c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb35f2c: add             x16, x0, x5, lsl #2
    //     0xb35f30: ldur            w1, [x16, #0xf]
    // 0xb35f34: DecompressPointer r1
    //     0xb35f34: add             x1, x1, HEAP, lsl #32
    // 0xb35f38: LoadField: r0 = r1->field_b
    //     0xb35f38: ldur            w0, [x1, #0xb]
    // 0xb35f3c: DecompressPointer r0
    //     0xb35f3c: add             x0, x0, HEAP, lsl #32
    // 0xb35f40: r1 = LoadClassIdInstr(r0)
    //     0xb35f40: ldur            x1, [x0, #-1]
    //     0xb35f44: ubfx            x1, x1, #0xc, #0x14
    // 0xb35f48: r16 = "bumper_coupon"
    //     0xb35f48: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xb35f4c: ldr             x16, [x16, #0x8d8]
    // 0xb35f50: stp             x16, x0, [SP]
    // 0xb35f54: mov             x0, x1
    // 0xb35f58: mov             lr, x0
    // 0xb35f5c: ldr             lr, [x21, lr, lsl #3]
    // 0xb35f60: blr             lr
    // 0xb35f64: tbnz            w0, #4, #0xb36f00
    // 0xb35f68: ldur            x1, [fp, #-8]
    // 0xb35f6c: LoadField: r0 = r1->field_f
    //     0xb35f6c: ldur            w0, [x1, #0xf]
    // 0xb35f70: DecompressPointer r0
    //     0xb35f70: add             x0, x0, HEAP, lsl #32
    // 0xb35f74: LoadField: r2 = r0->field_b
    //     0xb35f74: ldur            w2, [x0, #0xb]
    // 0xb35f78: DecompressPointer r2
    //     0xb35f78: add             x2, x2, HEAP, lsl #32
    // 0xb35f7c: cmp             w2, NULL
    // 0xb35f80: b.eq            #0xb37edc
    // 0xb35f84: LoadField: r0 = r2->field_b
    //     0xb35f84: ldur            w0, [x2, #0xb]
    // 0xb35f88: DecompressPointer r0
    //     0xb35f88: add             x0, x0, HEAP, lsl #32
    // 0xb35f8c: r2 = LoadClassIdInstr(r0)
    //     0xb35f8c: ldur            x2, [x0, #-1]
    //     0xb35f90: ubfx            x2, x2, #0xc, #0x14
    // 0xb35f94: r16 = "other_offers"
    //     0xb35f94: add             x16, PP, #0x57, lsl #12  ; [pp+0x571d0] "other_offers"
    //     0xb35f98: ldr             x16, [x16, #0x1d0]
    // 0xb35f9c: stp             x16, x0, [SP]
    // 0xb35fa0: mov             x0, x2
    // 0xb35fa4: mov             lr, x0
    // 0xb35fa8: ldr             lr, [x21, lr, lsl #3]
    // 0xb35fac: blr             lr
    // 0xb35fb0: tbz             w0, #4, #0xb36e78
    // 0xb35fb4: ldur            x0, [fp, #-8]
    // 0xb35fb8: LoadField: r1 = r0->field_f
    //     0xb35fb8: ldur            w1, [x0, #0xf]
    // 0xb35fbc: DecompressPointer r1
    //     0xb35fbc: add             x1, x1, HEAP, lsl #32
    // 0xb35fc0: LoadField: r2 = r1->field_b
    //     0xb35fc0: ldur            w2, [x1, #0xb]
    // 0xb35fc4: DecompressPointer r2
    //     0xb35fc4: add             x2, x2, HEAP, lsl #32
    // 0xb35fc8: stur            x2, [fp, #-0x30]
    // 0xb35fcc: cmp             w2, NULL
    // 0xb35fd0: b.eq            #0xb37ee0
    // 0xb35fd4: LoadField: r1 = r2->field_23
    //     0xb35fd4: ldur            w1, [x2, #0x23]
    // 0xb35fd8: DecompressPointer r1
    //     0xb35fd8: add             x1, x1, HEAP, lsl #32
    // 0xb35fdc: LoadField: r3 = r1->field_13
    //     0xb35fdc: ldur            w3, [x1, #0x13]
    // 0xb35fe0: DecompressPointer r3
    //     0xb35fe0: add             x3, x3, HEAP, lsl #32
    // 0xb35fe4: cmp             w3, NULL
    // 0xb35fe8: b.ne            #0xb35ff4
    // 0xb35fec: r1 = Null
    //     0xb35fec: mov             x1, NULL
    // 0xb35ff0: b               #0xb35ffc
    // 0xb35ff4: LoadField: r1 = r3->field_7
    //     0xb35ff4: ldur            w1, [x3, #7]
    // 0xb35ff8: DecompressPointer r1
    //     0xb35ff8: add             x1, x1, HEAP, lsl #32
    // 0xb35ffc: cmp             w1, NULL
    // 0xb36000: b.ne            #0xb3600c
    // 0xb36004: r1 = 0
    //     0xb36004: movz            x1, #0
    // 0xb36008: b               #0xb3601c
    // 0xb3600c: r3 = LoadInt32Instr(r1)
    //     0xb3600c: sbfx            x3, x1, #1, #0x1f
    //     0xb36010: tbz             w1, #0, #0xb36018
    //     0xb36014: ldur            x3, [x1, #7]
    // 0xb36018: mov             x1, x3
    // 0xb3601c: stur            x1, [fp, #-0x28]
    // 0xb36020: LoadField: r3 = r2->field_23
    //     0xb36020: ldur            w3, [x2, #0x23]
    // 0xb36024: DecompressPointer r3
    //     0xb36024: add             x3, x3, HEAP, lsl #32
    // 0xb36028: LoadField: r4 = r3->field_13
    //     0xb36028: ldur            w4, [x3, #0x13]
    // 0xb3602c: DecompressPointer r4
    //     0xb3602c: add             x4, x4, HEAP, lsl #32
    // 0xb36030: cmp             w4, NULL
    // 0xb36034: b.ne            #0xb36040
    // 0xb36038: r3 = Null
    //     0xb36038: mov             x3, NULL
    // 0xb3603c: b               #0xb36048
    // 0xb36040: LoadField: r3 = r4->field_b
    //     0xb36040: ldur            w3, [x4, #0xb]
    // 0xb36044: DecompressPointer r3
    //     0xb36044: add             x3, x3, HEAP, lsl #32
    // 0xb36048: cmp             w3, NULL
    // 0xb3604c: b.ne            #0xb36058
    // 0xb36050: r3 = 0
    //     0xb36050: movz            x3, #0
    // 0xb36054: b               #0xb36068
    // 0xb36058: r4 = LoadInt32Instr(r3)
    //     0xb36058: sbfx            x4, x3, #1, #0x1f
    //     0xb3605c: tbz             w3, #0, #0xb36064
    //     0xb36060: ldur            x4, [x3, #7]
    // 0xb36064: mov             x3, x4
    // 0xb36068: stur            x3, [fp, #-0x20]
    // 0xb3606c: LoadField: r4 = r2->field_23
    //     0xb3606c: ldur            w4, [x2, #0x23]
    // 0xb36070: DecompressPointer r4
    //     0xb36070: add             x4, x4, HEAP, lsl #32
    // 0xb36074: LoadField: r5 = r4->field_13
    //     0xb36074: ldur            w5, [x4, #0x13]
    // 0xb36078: DecompressPointer r5
    //     0xb36078: add             x5, x5, HEAP, lsl #32
    // 0xb3607c: cmp             w5, NULL
    // 0xb36080: b.ne            #0xb3608c
    // 0xb36084: r4 = Null
    //     0xb36084: mov             x4, NULL
    // 0xb36088: b               #0xb36094
    // 0xb3608c: LoadField: r4 = r5->field_f
    //     0xb3608c: ldur            w4, [x5, #0xf]
    // 0xb36090: DecompressPointer r4
    //     0xb36090: add             x4, x4, HEAP, lsl #32
    // 0xb36094: cmp             w4, NULL
    // 0xb36098: b.ne            #0xb360a4
    // 0xb3609c: r4 = 0
    //     0xb3609c: movz            x4, #0
    // 0xb360a0: b               #0xb360b4
    // 0xb360a4: r5 = LoadInt32Instr(r4)
    //     0xb360a4: sbfx            x5, x4, #1, #0x1f
    //     0xb360a8: tbz             w4, #0, #0xb360b0
    //     0xb360ac: ldur            x5, [x4, #7]
    // 0xb360b0: mov             x4, x5
    // 0xb360b4: stur            x4, [fp, #-0x18]
    // 0xb360b8: r0 = Color()
    //     0xb360b8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb360bc: mov             x1, x0
    // 0xb360c0: r0 = Instance_ColorSpace
    //     0xb360c0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb360c4: stur            x1, [fp, #-0x38]
    // 0xb360c8: StoreField: r1->field_27 = r0
    //     0xb360c8: stur            w0, [x1, #0x27]
    // 0xb360cc: d1 = 1.000000
    //     0xb360cc: fmov            d1, #1.00000000
    // 0xb360d0: StoreField: r1->field_7 = d1
    //     0xb360d0: stur            d1, [x1, #7]
    // 0xb360d4: ldur            x2, [fp, #-0x28]
    // 0xb360d8: ubfx            x2, x2, #0, #0x20
    // 0xb360dc: and             w3, w2, #0xff
    // 0xb360e0: ubfx            x3, x3, #0, #0x20
    // 0xb360e4: scvtf           d0, x3
    // 0xb360e8: d1 = 255.000000
    //     0xb360e8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb360ec: fdiv            d2, d0, d1
    // 0xb360f0: StoreField: r1->field_f = d2
    //     0xb360f0: stur            d2, [x1, #0xf]
    // 0xb360f4: ldur            x2, [fp, #-0x20]
    // 0xb360f8: ubfx            x2, x2, #0, #0x20
    // 0xb360fc: and             w3, w2, #0xff
    // 0xb36100: ubfx            x3, x3, #0, #0x20
    // 0xb36104: scvtf           d0, x3
    // 0xb36108: fdiv            d2, d0, d1
    // 0xb3610c: ArrayStore: r1[0] = d2  ; List_8
    //     0xb3610c: stur            d2, [x1, #0x17]
    // 0xb36110: ldur            x2, [fp, #-0x18]
    // 0xb36114: ubfx            x2, x2, #0, #0x20
    // 0xb36118: and             w3, w2, #0xff
    // 0xb3611c: ubfx            x3, x3, #0, #0x20
    // 0xb36120: scvtf           d0, x3
    // 0xb36124: fdiv            d2, d0, d1
    // 0xb36128: StoreField: r1->field_1f = d2
    //     0xb36128: stur            d2, [x1, #0x1f]
    // 0xb3612c: ldur            x2, [fp, #-0x30]
    // 0xb36130: LoadField: r3 = r2->field_23
    //     0xb36130: ldur            w3, [x2, #0x23]
    // 0xb36134: DecompressPointer r3
    //     0xb36134: add             x3, x3, HEAP, lsl #32
    // 0xb36138: LoadField: r4 = r3->field_13
    //     0xb36138: ldur            w4, [x3, #0x13]
    // 0xb3613c: DecompressPointer r4
    //     0xb3613c: add             x4, x4, HEAP, lsl #32
    // 0xb36140: cmp             w4, NULL
    // 0xb36144: b.ne            #0xb36150
    // 0xb36148: r3 = Null
    //     0xb36148: mov             x3, NULL
    // 0xb3614c: b               #0xb36158
    // 0xb36150: LoadField: r3 = r4->field_7
    //     0xb36150: ldur            w3, [x4, #7]
    // 0xb36154: DecompressPointer r3
    //     0xb36154: add             x3, x3, HEAP, lsl #32
    // 0xb36158: cmp             w3, NULL
    // 0xb3615c: b.ne            #0xb36168
    // 0xb36160: r3 = 0
    //     0xb36160: movz            x3, #0
    // 0xb36164: b               #0xb36178
    // 0xb36168: r4 = LoadInt32Instr(r3)
    //     0xb36168: sbfx            x4, x3, #1, #0x1f
    //     0xb3616c: tbz             w3, #0, #0xb36174
    //     0xb36170: ldur            x4, [x3, #7]
    // 0xb36174: mov             x3, x4
    // 0xb36178: stur            x3, [fp, #-0x28]
    // 0xb3617c: LoadField: r4 = r2->field_23
    //     0xb3617c: ldur            w4, [x2, #0x23]
    // 0xb36180: DecompressPointer r4
    //     0xb36180: add             x4, x4, HEAP, lsl #32
    // 0xb36184: LoadField: r5 = r4->field_13
    //     0xb36184: ldur            w5, [x4, #0x13]
    // 0xb36188: DecompressPointer r5
    //     0xb36188: add             x5, x5, HEAP, lsl #32
    // 0xb3618c: cmp             w5, NULL
    // 0xb36190: b.ne            #0xb3619c
    // 0xb36194: r4 = Null
    //     0xb36194: mov             x4, NULL
    // 0xb36198: b               #0xb361a4
    // 0xb3619c: LoadField: r4 = r5->field_b
    //     0xb3619c: ldur            w4, [x5, #0xb]
    // 0xb361a0: DecompressPointer r4
    //     0xb361a0: add             x4, x4, HEAP, lsl #32
    // 0xb361a4: cmp             w4, NULL
    // 0xb361a8: b.ne            #0xb361b4
    // 0xb361ac: r4 = 0
    //     0xb361ac: movz            x4, #0
    // 0xb361b0: b               #0xb361c4
    // 0xb361b4: r5 = LoadInt32Instr(r4)
    //     0xb361b4: sbfx            x5, x4, #1, #0x1f
    //     0xb361b8: tbz             w4, #0, #0xb361c0
    //     0xb361bc: ldur            x5, [x4, #7]
    // 0xb361c0: mov             x4, x5
    // 0xb361c4: stur            x4, [fp, #-0x20]
    // 0xb361c8: LoadField: r5 = r2->field_23
    //     0xb361c8: ldur            w5, [x2, #0x23]
    // 0xb361cc: DecompressPointer r5
    //     0xb361cc: add             x5, x5, HEAP, lsl #32
    // 0xb361d0: LoadField: r2 = r5->field_13
    //     0xb361d0: ldur            w2, [x5, #0x13]
    // 0xb361d4: DecompressPointer r2
    //     0xb361d4: add             x2, x2, HEAP, lsl #32
    // 0xb361d8: cmp             w2, NULL
    // 0xb361dc: b.ne            #0xb361e8
    // 0xb361e0: r2 = Null
    //     0xb361e0: mov             x2, NULL
    // 0xb361e4: b               #0xb361f4
    // 0xb361e8: LoadField: r5 = r2->field_f
    //     0xb361e8: ldur            w5, [x2, #0xf]
    // 0xb361ec: DecompressPointer r5
    //     0xb361ec: add             x5, x5, HEAP, lsl #32
    // 0xb361f0: mov             x2, x5
    // 0xb361f4: cmp             w2, NULL
    // 0xb361f8: b.ne            #0xb36204
    // 0xb361fc: r6 = 0
    //     0xb361fc: movz            x6, #0
    // 0xb36200: b               #0xb36214
    // 0xb36204: r5 = LoadInt32Instr(r2)
    //     0xb36204: sbfx            x5, x2, #1, #0x1f
    //     0xb36208: tbz             w2, #0, #0xb36210
    //     0xb3620c: ldur            x5, [x2, #7]
    // 0xb36210: mov             x6, x5
    // 0xb36214: ldur            x2, [fp, #-8]
    // 0xb36218: ldur            x5, [fp, #-0x10]
    // 0xb3621c: stur            x6, [fp, #-0x18]
    // 0xb36220: r0 = Color()
    //     0xb36220: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb36224: mov             x3, x0
    // 0xb36228: r0 = Instance_ColorSpace
    //     0xb36228: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb3622c: stur            x3, [fp, #-0x30]
    // 0xb36230: StoreField: r3->field_27 = r0
    //     0xb36230: stur            w0, [x3, #0x27]
    // 0xb36234: d0 = 0.700000
    //     0xb36234: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb36238: ldr             d0, [x17, #0xf48]
    // 0xb3623c: StoreField: r3->field_7 = d0
    //     0xb3623c: stur            d0, [x3, #7]
    // 0xb36240: ldur            x0, [fp, #-0x28]
    // 0xb36244: ubfx            x0, x0, #0, #0x20
    // 0xb36248: and             w1, w0, #0xff
    // 0xb3624c: ubfx            x1, x1, #0, #0x20
    // 0xb36250: scvtf           d0, x1
    // 0xb36254: d1 = 255.000000
    //     0xb36254: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb36258: fdiv            d2, d0, d1
    // 0xb3625c: StoreField: r3->field_f = d2
    //     0xb3625c: stur            d2, [x3, #0xf]
    // 0xb36260: ldur            x0, [fp, #-0x20]
    // 0xb36264: ubfx            x0, x0, #0, #0x20
    // 0xb36268: and             w1, w0, #0xff
    // 0xb3626c: ubfx            x1, x1, #0, #0x20
    // 0xb36270: scvtf           d0, x1
    // 0xb36274: fdiv            d2, d0, d1
    // 0xb36278: ArrayStore: r3[0] = d2  ; List_8
    //     0xb36278: stur            d2, [x3, #0x17]
    // 0xb3627c: ldur            x0, [fp, #-0x18]
    // 0xb36280: ubfx            x0, x0, #0, #0x20
    // 0xb36284: and             w1, w0, #0xff
    // 0xb36288: ubfx            x1, x1, #0, #0x20
    // 0xb3628c: scvtf           d0, x1
    // 0xb36290: fdiv            d2, d0, d1
    // 0xb36294: StoreField: r3->field_1f = d2
    //     0xb36294: stur            d2, [x3, #0x1f]
    // 0xb36298: r1 = Null
    //     0xb36298: mov             x1, NULL
    // 0xb3629c: r2 = 4
    //     0xb3629c: movz            x2, #0x4
    // 0xb362a0: r0 = AllocateArray()
    //     0xb362a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb362a4: mov             x2, x0
    // 0xb362a8: ldur            x0, [fp, #-0x38]
    // 0xb362ac: stur            x2, [fp, #-0x40]
    // 0xb362b0: StoreField: r2->field_f = r0
    //     0xb362b0: stur            w0, [x2, #0xf]
    // 0xb362b4: ldur            x0, [fp, #-0x30]
    // 0xb362b8: StoreField: r2->field_13 = r0
    //     0xb362b8: stur            w0, [x2, #0x13]
    // 0xb362bc: r1 = <Color>
    //     0xb362bc: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb362c0: ldr             x1, [x1, #0xf80]
    // 0xb362c4: r0 = AllocateGrowableArray()
    //     0xb362c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb362c8: mov             x1, x0
    // 0xb362cc: ldur            x0, [fp, #-0x40]
    // 0xb362d0: stur            x1, [fp, #-0x30]
    // 0xb362d4: StoreField: r1->field_f = r0
    //     0xb362d4: stur            w0, [x1, #0xf]
    // 0xb362d8: r2 = 4
    //     0xb362d8: movz            x2, #0x4
    // 0xb362dc: StoreField: r1->field_b = r2
    //     0xb362dc: stur            w2, [x1, #0xb]
    // 0xb362e0: r0 = LinearGradient()
    //     0xb362e0: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb362e4: mov             x1, x0
    // 0xb362e8: r0 = Instance_Alignment
    //     0xb362e8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb362ec: ldr             x0, [x0, #0xce0]
    // 0xb362f0: stur            x1, [fp, #-0x38]
    // 0xb362f4: StoreField: r1->field_13 = r0
    //     0xb362f4: stur            w0, [x1, #0x13]
    // 0xb362f8: r0 = Instance_Alignment
    //     0xb362f8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb362fc: ldr             x0, [x0, #0xce8]
    // 0xb36300: ArrayStore: r1[0] = r0  ; List_4
    //     0xb36300: stur            w0, [x1, #0x17]
    // 0xb36304: r0 = Instance_TileMode
    //     0xb36304: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb36308: ldr             x0, [x0, #0xcf0]
    // 0xb3630c: StoreField: r1->field_1b = r0
    //     0xb3630c: stur            w0, [x1, #0x1b]
    // 0xb36310: ldur            x0, [fp, #-0x30]
    // 0xb36314: StoreField: r1->field_7 = r0
    //     0xb36314: stur            w0, [x1, #7]
    // 0xb36318: r0 = BoxDecoration()
    //     0xb36318: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb3631c: mov             x2, x0
    // 0xb36320: ldur            x0, [fp, #-0x38]
    // 0xb36324: stur            x2, [fp, #-0x30]
    // 0xb36328: StoreField: r2->field_1b = r0
    //     0xb36328: stur            w0, [x2, #0x1b]
    // 0xb3632c: r0 = Instance_BoxShape
    //     0xb3632c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb36330: ldr             x0, [x0, #0x80]
    // 0xb36334: StoreField: r2->field_23 = r0
    //     0xb36334: stur            w0, [x2, #0x23]
    // 0xb36338: ldur            x3, [fp, #-0x10]
    // 0xb3633c: LoadField: r1 = r3->field_f
    //     0xb3633c: ldur            w1, [x3, #0xf]
    // 0xb36340: DecompressPointer r1
    //     0xb36340: add             x1, x1, HEAP, lsl #32
    // 0xb36344: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb36344: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb36348: r0 = _of()
    //     0xb36348: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb3634c: LoadField: r1 = r0->field_7
    //     0xb3634c: ldur            w1, [x0, #7]
    // 0xb36350: DecompressPointer r1
    //     0xb36350: add             x1, x1, HEAP, lsl #32
    // 0xb36354: LoadField: d0 = r1->field_7
    //     0xb36354: ldur            d0, [x1, #7]
    // 0xb36358: d2 = 0.500000
    //     0xb36358: fmov            d2, #0.50000000
    // 0xb3635c: fmul            d1, d0, d2
    // 0xb36360: ldur            x0, [fp, #-8]
    // 0xb36364: stur            d1, [fp, #-0x68]
    // 0xb36368: LoadField: r1 = r0->field_f
    //     0xb36368: ldur            w1, [x0, #0xf]
    // 0xb3636c: DecompressPointer r1
    //     0xb3636c: add             x1, x1, HEAP, lsl #32
    // 0xb36370: LoadField: r3 = r1->field_b
    //     0xb36370: ldur            w3, [x1, #0xb]
    // 0xb36374: DecompressPointer r3
    //     0xb36374: add             x3, x3, HEAP, lsl #32
    // 0xb36378: stur            x3, [fp, #-0x38]
    // 0xb3637c: cmp             w3, NULL
    // 0xb36380: b.eq            #0xb37ee4
    // 0xb36384: LoadField: r1 = r3->field_1f
    //     0xb36384: ldur            w1, [x3, #0x1f]
    // 0xb36388: DecompressPointer r1
    //     0xb36388: add             x1, x1, HEAP, lsl #32
    // 0xb3638c: tbz             w1, #4, #0xb363fc
    // 0xb36390: r1 = Null
    //     0xb36390: mov             x1, NULL
    // 0xb36394: r2 = 6
    //     0xb36394: movz            x2, #0x6
    // 0xb36398: r0 = AllocateArray()
    //     0xb36398: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3639c: r16 = "Save upto "
    //     0xb3639c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38ca0] "Save upto "
    //     0xb363a0: ldr             x16, [x16, #0xca0]
    // 0xb363a4: StoreField: r0->field_f = r16
    //     0xb363a4: stur            w16, [x0, #0xf]
    // 0xb363a8: ldur            x2, [fp, #-0x38]
    // 0xb363ac: LoadField: r1 = r2->field_27
    //     0xb363ac: ldur            w1, [x2, #0x27]
    // 0xb363b0: DecompressPointer r1
    //     0xb363b0: add             x1, x1, HEAP, lsl #32
    // 0xb363b4: cmp             w1, NULL
    // 0xb363b8: b.ne            #0xb363c4
    // 0xb363bc: r1 = Null
    //     0xb363bc: mov             x1, NULL
    // 0xb363c0: b               #0xb363d0
    // 0xb363c4: LoadField: r2 = r1->field_f
    //     0xb363c4: ldur            w2, [x1, #0xf]
    // 0xb363c8: DecompressPointer r2
    //     0xb363c8: add             x2, x2, HEAP, lsl #32
    // 0xb363cc: mov             x1, x2
    // 0xb363d0: cmp             w1, NULL
    // 0xb363d4: b.ne            #0xb363dc
    // 0xb363d8: r1 = ""
    //     0xb363d8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb363dc: StoreField: r0->field_13 = r1
    //     0xb363dc: stur            w1, [x0, #0x13]
    // 0xb363e0: r16 = "!"
    //     0xb363e0: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xb363e4: ldr             x16, [x16, #0xd00]
    // 0xb363e8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb363e8: stur            w16, [x0, #0x17]
    // 0xb363ec: str             x0, [SP]
    // 0xb363f0: r0 = _interpolate()
    //     0xb363f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb363f4: mov             x3, x0
    // 0xb363f8: b               #0xb365ac
    // 0xb363fc: mov             x2, x3
    // 0xb36400: LoadField: r0 = r2->field_f
    //     0xb36400: ldur            w0, [x2, #0xf]
    // 0xb36404: DecompressPointer r0
    //     0xb36404: add             x0, x0, HEAP, lsl #32
    // 0xb36408: cmp             w0, NULL
    // 0xb3640c: b.ne            #0xb3641c
    // 0xb36410: ldur            x3, [fp, #-0x10]
    // 0xb36414: r0 = Null
    //     0xb36414: mov             x0, NULL
    // 0xb36418: b               #0xb3646c
    // 0xb3641c: ldur            x3, [fp, #-0x10]
    // 0xb36420: LoadField: r4 = r0->field_f
    //     0xb36420: ldur            w4, [x0, #0xf]
    // 0xb36424: DecompressPointer r4
    //     0xb36424: add             x4, x4, HEAP, lsl #32
    // 0xb36428: LoadField: r0 = r3->field_13
    //     0xb36428: ldur            w0, [x3, #0x13]
    // 0xb3642c: DecompressPointer r0
    //     0xb3642c: add             x0, x0, HEAP, lsl #32
    // 0xb36430: LoadField: r1 = r4->field_b
    //     0xb36430: ldur            w1, [x4, #0xb]
    // 0xb36434: r5 = LoadInt32Instr(r0)
    //     0xb36434: sbfx            x5, x0, #1, #0x1f
    //     0xb36438: tbz             w0, #0, #0xb36440
    //     0xb3643c: ldur            x5, [x0, #7]
    // 0xb36440: r0 = LoadInt32Instr(r1)
    //     0xb36440: sbfx            x0, x1, #1, #0x1f
    // 0xb36444: mov             x1, x5
    // 0xb36448: cmp             x1, x0
    // 0xb3644c: b.hs            #0xb37ee8
    // 0xb36450: LoadField: r0 = r4->field_f
    //     0xb36450: ldur            w0, [x4, #0xf]
    // 0xb36454: DecompressPointer r0
    //     0xb36454: add             x0, x0, HEAP, lsl #32
    // 0xb36458: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb36458: add             x16, x0, x5, lsl #2
    //     0xb3645c: ldur            w1, [x16, #0xf]
    // 0xb36460: DecompressPointer r1
    //     0xb36460: add             x1, x1, HEAP, lsl #32
    // 0xb36464: LoadField: r0 = r1->field_7
    //     0xb36464: ldur            w0, [x1, #7]
    // 0xb36468: DecompressPointer r0
    //     0xb36468: add             x0, x0, HEAP, lsl #32
    // 0xb3646c: LoadField: r1 = r2->field_13
    //     0xb3646c: ldur            w1, [x2, #0x13]
    // 0xb36470: DecompressPointer r1
    //     0xb36470: add             x1, x1, HEAP, lsl #32
    // 0xb36474: r2 = LoadClassIdInstr(r0)
    //     0xb36474: ldur            x2, [x0, #-1]
    //     0xb36478: ubfx            x2, x2, #0xc, #0x14
    // 0xb3647c: stp             x1, x0, [SP]
    // 0xb36480: mov             x0, x2
    // 0xb36484: mov             lr, x0
    // 0xb36488: ldr             lr, [x21, lr, lsl #3]
    // 0xb3648c: blr             lr
    // 0xb36490: tbnz            w0, #4, #0xb36518
    // 0xb36494: ldur            x0, [fp, #-8]
    // 0xb36498: r1 = Null
    //     0xb36498: mov             x1, NULL
    // 0xb3649c: r2 = 6
    //     0xb3649c: movz            x2, #0x6
    // 0xb364a0: r0 = AllocateArray()
    //     0xb364a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb364a4: r16 = "You\'ve Saved "
    //     0xb364a4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a5c8] "You\'ve Saved "
    //     0xb364a8: ldr             x16, [x16, #0x5c8]
    // 0xb364ac: StoreField: r0->field_f = r16
    //     0xb364ac: stur            w16, [x0, #0xf]
    // 0xb364b0: ldur            x1, [fp, #-8]
    // 0xb364b4: LoadField: r2 = r1->field_f
    //     0xb364b4: ldur            w2, [x1, #0xf]
    // 0xb364b8: DecompressPointer r2
    //     0xb364b8: add             x2, x2, HEAP, lsl #32
    // 0xb364bc: LoadField: r3 = r2->field_b
    //     0xb364bc: ldur            w3, [x2, #0xb]
    // 0xb364c0: DecompressPointer r3
    //     0xb364c0: add             x3, x3, HEAP, lsl #32
    // 0xb364c4: cmp             w3, NULL
    // 0xb364c8: b.eq            #0xb37eec
    // 0xb364cc: LoadField: r2 = r3->field_27
    //     0xb364cc: ldur            w2, [x3, #0x27]
    // 0xb364d0: DecompressPointer r2
    //     0xb364d0: add             x2, x2, HEAP, lsl #32
    // 0xb364d4: cmp             w2, NULL
    // 0xb364d8: b.ne            #0xb364e4
    // 0xb364dc: r2 = Null
    //     0xb364dc: mov             x2, NULL
    // 0xb364e0: b               #0xb364f0
    // 0xb364e4: LoadField: r3 = r2->field_f
    //     0xb364e4: ldur            w3, [x2, #0xf]
    // 0xb364e8: DecompressPointer r3
    //     0xb364e8: add             x3, x3, HEAP, lsl #32
    // 0xb364ec: mov             x2, x3
    // 0xb364f0: cmp             w2, NULL
    // 0xb364f4: b.ne            #0xb364fc
    // 0xb364f8: r2 = ""
    //     0xb364f8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb364fc: StoreField: r0->field_13 = r2
    //     0xb364fc: stur            w2, [x0, #0x13]
    // 0xb36500: r16 = "!"
    //     0xb36500: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d00] "!"
    //     0xb36504: ldr             x16, [x16, #0xd00]
    // 0xb36508: ArrayStore: r0[0] = r16  ; List_4
    //     0xb36508: stur            w16, [x0, #0x17]
    // 0xb3650c: str             x0, [SP]
    // 0xb36510: r0 = _interpolate()
    //     0xb36510: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb36514: b               #0xb365a8
    // 0xb36518: ldur            x2, [fp, #-8]
    // 0xb3651c: LoadField: r0 = r2->field_f
    //     0xb3651c: ldur            w0, [x2, #0xf]
    // 0xb36520: DecompressPointer r0
    //     0xb36520: add             x0, x0, HEAP, lsl #32
    // 0xb36524: LoadField: r1 = r0->field_b
    //     0xb36524: ldur            w1, [x0, #0xb]
    // 0xb36528: DecompressPointer r1
    //     0xb36528: add             x1, x1, HEAP, lsl #32
    // 0xb3652c: cmp             w1, NULL
    // 0xb36530: b.eq            #0xb37ef0
    // 0xb36534: LoadField: r0 = r1->field_f
    //     0xb36534: ldur            w0, [x1, #0xf]
    // 0xb36538: DecompressPointer r0
    //     0xb36538: add             x0, x0, HEAP, lsl #32
    // 0xb3653c: cmp             w0, NULL
    // 0xb36540: b.ne            #0xb36550
    // 0xb36544: ldur            x3, [fp, #-0x10]
    // 0xb36548: r0 = Null
    //     0xb36548: mov             x0, NULL
    // 0xb3654c: b               #0xb365a0
    // 0xb36550: ldur            x3, [fp, #-0x10]
    // 0xb36554: LoadField: r4 = r0->field_f
    //     0xb36554: ldur            w4, [x0, #0xf]
    // 0xb36558: DecompressPointer r4
    //     0xb36558: add             x4, x4, HEAP, lsl #32
    // 0xb3655c: LoadField: r0 = r3->field_13
    //     0xb3655c: ldur            w0, [x3, #0x13]
    // 0xb36560: DecompressPointer r0
    //     0xb36560: add             x0, x0, HEAP, lsl #32
    // 0xb36564: LoadField: r1 = r4->field_b
    //     0xb36564: ldur            w1, [x4, #0xb]
    // 0xb36568: r5 = LoadInt32Instr(r0)
    //     0xb36568: sbfx            x5, x0, #1, #0x1f
    //     0xb3656c: tbz             w0, #0, #0xb36574
    //     0xb36570: ldur            x5, [x0, #7]
    // 0xb36574: r0 = LoadInt32Instr(r1)
    //     0xb36574: sbfx            x0, x1, #1, #0x1f
    // 0xb36578: mov             x1, x5
    // 0xb3657c: cmp             x1, x0
    // 0xb36580: b.hs            #0xb37ef4
    // 0xb36584: LoadField: r0 = r4->field_f
    //     0xb36584: ldur            w0, [x4, #0xf]
    // 0xb36588: DecompressPointer r0
    //     0xb36588: add             x0, x0, HEAP, lsl #32
    // 0xb3658c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb3658c: add             x16, x0, x5, lsl #2
    //     0xb36590: ldur            w1, [x16, #0xf]
    // 0xb36594: DecompressPointer r1
    //     0xb36594: add             x1, x1, HEAP, lsl #32
    // 0xb36598: LoadField: r0 = r1->field_13
    //     0xb36598: ldur            w0, [x1, #0x13]
    // 0xb3659c: DecompressPointer r0
    //     0xb3659c: add             x0, x0, HEAP, lsl #32
    // 0xb365a0: str             x0, [SP]
    // 0xb365a4: r0 = _interpolateSingle()
    //     0xb365a4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb365a8: mov             x3, x0
    // 0xb365ac: ldur            x0, [fp, #-8]
    // 0xb365b0: ldur            x2, [fp, #-0x10]
    // 0xb365b4: ldur            d0, [fp, #-0x68]
    // 0xb365b8: stur            x3, [fp, #-0x38]
    // 0xb365bc: LoadField: r1 = r2->field_f
    //     0xb365bc: ldur            w1, [x2, #0xf]
    // 0xb365c0: DecompressPointer r1
    //     0xb365c0: add             x1, x1, HEAP, lsl #32
    // 0xb365c4: r0 = of()
    //     0xb365c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb365c8: LoadField: r1 = r0->field_87
    //     0xb365c8: ldur            w1, [x0, #0x87]
    // 0xb365cc: DecompressPointer r1
    //     0xb365cc: add             x1, x1, HEAP, lsl #32
    // 0xb365d0: LoadField: r0 = r1->field_7
    //     0xb365d0: ldur            w0, [x1, #7]
    // 0xb365d4: DecompressPointer r0
    //     0xb365d4: add             x0, x0, HEAP, lsl #32
    // 0xb365d8: r16 = 16.000000
    //     0xb365d8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb365dc: ldr             x16, [x16, #0x188]
    // 0xb365e0: r30 = Instance_Color
    //     0xb365e0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb365e4: stp             lr, x16, [SP]
    // 0xb365e8: mov             x1, x0
    // 0xb365ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb365ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb365f0: ldr             x4, [x4, #0xaa0]
    // 0xb365f4: r0 = copyWith()
    //     0xb365f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb365f8: stur            x0, [fp, #-0x40]
    // 0xb365fc: r0 = Text()
    //     0xb365fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb36600: mov             x1, x0
    // 0xb36604: ldur            x0, [fp, #-0x38]
    // 0xb36608: stur            x1, [fp, #-0x48]
    // 0xb3660c: StoreField: r1->field_b = r0
    //     0xb3660c: stur            w0, [x1, #0xb]
    // 0xb36610: ldur            x0, [fp, #-0x40]
    // 0xb36614: StoreField: r1->field_13 = r0
    //     0xb36614: stur            w0, [x1, #0x13]
    // 0xb36618: r2 = 4
    //     0xb36618: movz            x2, #0x4
    // 0xb3661c: StoreField: r1->field_37 = r2
    //     0xb3661c: stur            w2, [x1, #0x37]
    // 0xb36620: r0 = Padding()
    //     0xb36620: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb36624: mov             x1, x0
    // 0xb36628: r0 = Instance_EdgeInsets
    //     0xb36628: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb3662c: ldr             x0, [x0, #0x980]
    // 0xb36630: stur            x1, [fp, #-0x40]
    // 0xb36634: StoreField: r1->field_f = r0
    //     0xb36634: stur            w0, [x1, #0xf]
    // 0xb36638: ldur            x2, [fp, #-0x48]
    // 0xb3663c: StoreField: r1->field_b = r2
    //     0xb3663c: stur            w2, [x1, #0xb]
    // 0xb36640: ldur            d0, [fp, #-0x68]
    // 0xb36644: r2 = inline_Allocate_Double()
    //     0xb36644: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb36648: add             x2, x2, #0x10
    //     0xb3664c: cmp             x3, x2
    //     0xb36650: b.ls            #0xb37ef8
    //     0xb36654: str             x2, [THR, #0x50]  ; THR::top
    //     0xb36658: sub             x2, x2, #0xf
    //     0xb3665c: movz            x3, #0xe15c
    //     0xb36660: movk            x3, #0x3, lsl #16
    //     0xb36664: stur            x3, [x2, #-1]
    // 0xb36668: StoreField: r2->field_7 = d0
    //     0xb36668: stur            d0, [x2, #7]
    // 0xb3666c: stur            x2, [fp, #-0x38]
    // 0xb36670: r0 = SizedBox()
    //     0xb36670: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb36674: mov             x2, x0
    // 0xb36678: ldur            x0, [fp, #-0x38]
    // 0xb3667c: stur            x2, [fp, #-0x48]
    // 0xb36680: StoreField: r2->field_f = r0
    //     0xb36680: stur            w0, [x2, #0xf]
    // 0xb36684: ldur            x0, [fp, #-0x40]
    // 0xb36688: StoreField: r2->field_b = r0
    //     0xb36688: stur            w0, [x2, #0xb]
    // 0xb3668c: ldur            x3, [fp, #-8]
    // 0xb36690: LoadField: r0 = r3->field_f
    //     0xb36690: ldur            w0, [x3, #0xf]
    // 0xb36694: DecompressPointer r0
    //     0xb36694: add             x0, x0, HEAP, lsl #32
    // 0xb36698: LoadField: r4 = r0->field_b
    //     0xb36698: ldur            w4, [x0, #0xb]
    // 0xb3669c: DecompressPointer r4
    //     0xb3669c: add             x4, x4, HEAP, lsl #32
    // 0xb366a0: cmp             w4, NULL
    // 0xb366a4: b.eq            #0xb37f14
    // 0xb366a8: LoadField: r0 = r4->field_f
    //     0xb366a8: ldur            w0, [x4, #0xf]
    // 0xb366ac: DecompressPointer r0
    //     0xb366ac: add             x0, x0, HEAP, lsl #32
    // 0xb366b0: cmp             w0, NULL
    // 0xb366b4: b.ne            #0xb366c4
    // 0xb366b8: ldur            x5, [fp, #-0x10]
    // 0xb366bc: r0 = Null
    //     0xb366bc: mov             x0, NULL
    // 0xb366c0: b               #0xb36714
    // 0xb366c4: ldur            x5, [fp, #-0x10]
    // 0xb366c8: LoadField: r6 = r0->field_f
    //     0xb366c8: ldur            w6, [x0, #0xf]
    // 0xb366cc: DecompressPointer r6
    //     0xb366cc: add             x6, x6, HEAP, lsl #32
    // 0xb366d0: LoadField: r0 = r5->field_13
    //     0xb366d0: ldur            w0, [x5, #0x13]
    // 0xb366d4: DecompressPointer r0
    //     0xb366d4: add             x0, x0, HEAP, lsl #32
    // 0xb366d8: LoadField: r1 = r6->field_b
    //     0xb366d8: ldur            w1, [x6, #0xb]
    // 0xb366dc: r7 = LoadInt32Instr(r0)
    //     0xb366dc: sbfx            x7, x0, #1, #0x1f
    //     0xb366e0: tbz             w0, #0, #0xb366e8
    //     0xb366e4: ldur            x7, [x0, #7]
    // 0xb366e8: r0 = LoadInt32Instr(r1)
    //     0xb366e8: sbfx            x0, x1, #1, #0x1f
    // 0xb366ec: mov             x1, x7
    // 0xb366f0: cmp             x1, x0
    // 0xb366f4: b.hs            #0xb37f18
    // 0xb366f8: LoadField: r0 = r6->field_f
    //     0xb366f8: ldur            w0, [x6, #0xf]
    // 0xb366fc: DecompressPointer r0
    //     0xb366fc: add             x0, x0, HEAP, lsl #32
    // 0xb36700: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb36700: add             x16, x0, x7, lsl #2
    //     0xb36704: ldur            w1, [x16, #0xf]
    // 0xb36708: DecompressPointer r1
    //     0xb36708: add             x1, x1, HEAP, lsl #32
    // 0xb3670c: LoadField: r0 = r1->field_7
    //     0xb3670c: ldur            w0, [x1, #7]
    // 0xb36710: DecompressPointer r0
    //     0xb36710: add             x0, x0, HEAP, lsl #32
    // 0xb36714: LoadField: r1 = r4->field_13
    //     0xb36714: ldur            w1, [x4, #0x13]
    // 0xb36718: DecompressPointer r1
    //     0xb36718: add             x1, x1, HEAP, lsl #32
    // 0xb3671c: r4 = LoadClassIdInstr(r0)
    //     0xb3671c: ldur            x4, [x0, #-1]
    //     0xb36720: ubfx            x4, x4, #0xc, #0x14
    // 0xb36724: stp             x1, x0, [SP]
    // 0xb36728: mov             x0, x4
    // 0xb3672c: mov             lr, x0
    // 0xb36730: ldr             lr, [x21, lr, lsl #3]
    // 0xb36734: blr             lr
    // 0xb36738: tbnz            w0, #4, #0xb36744
    // 0xb3673c: ldur            x0, [fp, #-8]
    // 0xb36740: b               #0xb3676c
    // 0xb36744: ldur            x0, [fp, #-8]
    // 0xb36748: LoadField: r1 = r0->field_f
    //     0xb36748: ldur            w1, [x0, #0xf]
    // 0xb3674c: DecompressPointer r1
    //     0xb3674c: add             x1, x1, HEAP, lsl #32
    // 0xb36750: LoadField: r2 = r1->field_b
    //     0xb36750: ldur            w2, [x1, #0xb]
    // 0xb36754: DecompressPointer r2
    //     0xb36754: add             x2, x2, HEAP, lsl #32
    // 0xb36758: cmp             w2, NULL
    // 0xb3675c: b.eq            #0xb37f1c
    // 0xb36760: LoadField: r1 = r2->field_1f
    //     0xb36760: ldur            w1, [x2, #0x1f]
    // 0xb36764: DecompressPointer r1
    //     0xb36764: add             x1, x1, HEAP, lsl #32
    // 0xb36768: tbz             w1, #4, #0xb367dc
    // 0xb3676c: ldur            x2, [fp, #-0x10]
    // 0xb36770: LoadField: r1 = r2->field_f
    //     0xb36770: ldur            w1, [x2, #0xf]
    // 0xb36774: DecompressPointer r1
    //     0xb36774: add             x1, x1, HEAP, lsl #32
    // 0xb36778: r0 = of()
    //     0xb36778: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3677c: LoadField: r1 = r0->field_87
    //     0xb3677c: ldur            w1, [x0, #0x87]
    // 0xb36780: DecompressPointer r1
    //     0xb36780: add             x1, x1, HEAP, lsl #32
    // 0xb36784: LoadField: r0 = r1->field_7
    //     0xb36784: ldur            w0, [x1, #7]
    // 0xb36788: DecompressPointer r0
    //     0xb36788: add             x0, x0, HEAP, lsl #32
    // 0xb3678c: r16 = Instance_Color
    //     0xb3678c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb36790: r30 = 14.000000
    //     0xb36790: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb36794: ldr             lr, [lr, #0x1d8]
    // 0xb36798: stp             lr, x16, [SP]
    // 0xb3679c: mov             x1, x0
    // 0xb367a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb367a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb367a4: ldr             x4, [x4, #0x9b8]
    // 0xb367a8: r0 = copyWith()
    //     0xb367a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb367ac: stur            x0, [fp, #-0x38]
    // 0xb367b0: r0 = Text()
    //     0xb367b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb367b4: mov             x1, x0
    // 0xb367b8: r0 = "Bumper Offer Applied!"
    //     0xb367b8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d08] "Bumper Offer Applied!"
    //     0xb367bc: ldr             x0, [x0, #0xd08]
    // 0xb367c0: StoreField: r1->field_b = r0
    //     0xb367c0: stur            w0, [x1, #0xb]
    // 0xb367c4: ldur            x0, [fp, #-0x38]
    // 0xb367c8: StoreField: r1->field_13 = r0
    //     0xb367c8: stur            w0, [x1, #0x13]
    // 0xb367cc: r2 = 4
    //     0xb367cc: movz            x2, #0x4
    // 0xb367d0: StoreField: r1->field_37 = r2
    //     0xb367d0: stur            w2, [x1, #0x37]
    // 0xb367d4: mov             x2, x1
    // 0xb367d8: b               #0xb36934
    // 0xb367dc: ldur            x0, [fp, #-0x10]
    // 0xb367e0: r2 = 4
    //     0xb367e0: movz            x2, #0x4
    // 0xb367e4: LoadField: r1 = r0->field_f
    //     0xb367e4: ldur            w1, [x0, #0xf]
    // 0xb367e8: DecompressPointer r1
    //     0xb367e8: add             x1, x1, HEAP, lsl #32
    // 0xb367ec: r0 = of()
    //     0xb367ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb367f0: LoadField: r1 = r0->field_87
    //     0xb367f0: ldur            w1, [x0, #0x87]
    // 0xb367f4: DecompressPointer r1
    //     0xb367f4: add             x1, x1, HEAP, lsl #32
    // 0xb367f8: LoadField: r0 = r1->field_7
    //     0xb367f8: ldur            w0, [x1, #7]
    // 0xb367fc: DecompressPointer r0
    //     0xb367fc: add             x0, x0, HEAP, lsl #32
    // 0xb36800: r16 = Instance_Color
    //     0xb36800: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb36804: r30 = 14.000000
    //     0xb36804: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb36808: ldr             lr, [lr, #0x1d8]
    // 0xb3680c: stp             lr, x16, [SP]
    // 0xb36810: mov             x1, x0
    // 0xb36814: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb36814: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb36818: ldr             x4, [x4, #0x9b8]
    // 0xb3681c: r0 = copyWith()
    //     0xb3681c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb36820: stur            x0, [fp, #-0x38]
    // 0xb36824: r0 = TextSpan()
    //     0xb36824: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb36828: mov             x2, x0
    // 0xb3682c: r0 = "Know "
    //     0xb3682c: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xb36830: ldr             x0, [x0, #0x5d0]
    // 0xb36834: stur            x2, [fp, #-0x40]
    // 0xb36838: StoreField: r2->field_b = r0
    //     0xb36838: stur            w0, [x2, #0xb]
    // 0xb3683c: r0 = Instance__DeferringMouseCursor
    //     0xb3683c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb36840: ArrayStore: r2[0] = r0  ; List_4
    //     0xb36840: stur            w0, [x2, #0x17]
    // 0xb36844: ldur            x1, [fp, #-0x38]
    // 0xb36848: StoreField: r2->field_7 = r1
    //     0xb36848: stur            w1, [x2, #7]
    // 0xb3684c: ldur            x3, [fp, #-0x10]
    // 0xb36850: LoadField: r1 = r3->field_f
    //     0xb36850: ldur            w1, [x3, #0xf]
    // 0xb36854: DecompressPointer r1
    //     0xb36854: add             x1, x1, HEAP, lsl #32
    // 0xb36858: r0 = of()
    //     0xb36858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb3685c: LoadField: r1 = r0->field_87
    //     0xb3685c: ldur            w1, [x0, #0x87]
    // 0xb36860: DecompressPointer r1
    //     0xb36860: add             x1, x1, HEAP, lsl #32
    // 0xb36864: LoadField: r0 = r1->field_7
    //     0xb36864: ldur            w0, [x1, #7]
    // 0xb36868: DecompressPointer r0
    //     0xb36868: add             x0, x0, HEAP, lsl #32
    // 0xb3686c: r16 = Instance_Color
    //     0xb3686c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb36870: r30 = 14.000000
    //     0xb36870: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb36874: ldr             lr, [lr, #0x1d8]
    // 0xb36878: stp             lr, x16, [SP]
    // 0xb3687c: mov             x1, x0
    // 0xb36880: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb36880: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb36884: ldr             x4, [x4, #0x9b8]
    // 0xb36888: r0 = copyWith()
    //     0xb36888: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3688c: stur            x0, [fp, #-0x38]
    // 0xb36890: r0 = TextSpan()
    //     0xb36890: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb36894: r2 = "More"
    //     0xb36894: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xb36898: ldr             x2, [x2, #0x5d8]
    // 0xb3689c: stur            x0, [fp, #-0x50]
    // 0xb368a0: StoreField: r0->field_b = r2
    //     0xb368a0: stur            w2, [x0, #0xb]
    // 0xb368a4: r3 = Instance__DeferringMouseCursor
    //     0xb368a4: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb368a8: ArrayStore: r0[0] = r3  ; List_4
    //     0xb368a8: stur            w3, [x0, #0x17]
    // 0xb368ac: ldur            x1, [fp, #-0x38]
    // 0xb368b0: StoreField: r0->field_7 = r1
    //     0xb368b0: stur            w1, [x0, #7]
    // 0xb368b4: r1 = Null
    //     0xb368b4: mov             x1, NULL
    // 0xb368b8: r2 = 4
    //     0xb368b8: movz            x2, #0x4
    // 0xb368bc: r0 = AllocateArray()
    //     0xb368bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb368c0: mov             x2, x0
    // 0xb368c4: ldur            x0, [fp, #-0x40]
    // 0xb368c8: stur            x2, [fp, #-0x38]
    // 0xb368cc: StoreField: r2->field_f = r0
    //     0xb368cc: stur            w0, [x2, #0xf]
    // 0xb368d0: ldur            x0, [fp, #-0x50]
    // 0xb368d4: StoreField: r2->field_13 = r0
    //     0xb368d4: stur            w0, [x2, #0x13]
    // 0xb368d8: r1 = <TextSpan>
    //     0xb368d8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xb368dc: ldr             x1, [x1, #0x940]
    // 0xb368e0: r0 = AllocateGrowableArray()
    //     0xb368e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb368e4: mov             x1, x0
    // 0xb368e8: ldur            x0, [fp, #-0x38]
    // 0xb368ec: stur            x1, [fp, #-0x40]
    // 0xb368f0: StoreField: r1->field_f = r0
    //     0xb368f0: stur            w0, [x1, #0xf]
    // 0xb368f4: r2 = 4
    //     0xb368f4: movz            x2, #0x4
    // 0xb368f8: StoreField: r1->field_b = r2
    //     0xb368f8: stur            w2, [x1, #0xb]
    // 0xb368fc: r0 = TextSpan()
    //     0xb368fc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb36900: mov             x1, x0
    // 0xb36904: ldur            x0, [fp, #-0x40]
    // 0xb36908: stur            x1, [fp, #-0x38]
    // 0xb3690c: StoreField: r1->field_f = r0
    //     0xb3690c: stur            w0, [x1, #0xf]
    // 0xb36910: r3 = Instance__DeferringMouseCursor
    //     0xb36910: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb36914: ArrayStore: r1[0] = r3  ; List_4
    //     0xb36914: stur            w3, [x1, #0x17]
    // 0xb36918: r0 = RichText()
    //     0xb36918: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb3691c: mov             x1, x0
    // 0xb36920: ldur            x2, [fp, #-0x38]
    // 0xb36924: stur            x0, [fp, #-0x38]
    // 0xb36928: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb36928: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb3692c: r0 = RichText()
    //     0xb3692c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb36930: ldur            x2, [fp, #-0x38]
    // 0xb36934: ldur            x0, [fp, #-8]
    // 0xb36938: ldur            x1, [fp, #-0x48]
    // 0xb3693c: stur            x2, [fp, #-0x38]
    // 0xb36940: r0 = Padding()
    //     0xb36940: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb36944: r4 = Instance_EdgeInsets
    //     0xb36944: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb36948: ldr             x4, [x4, #0xc40]
    // 0xb3694c: stur            x0, [fp, #-0x40]
    // 0xb36950: StoreField: r0->field_f = r4
    //     0xb36950: stur            w4, [x0, #0xf]
    // 0xb36954: ldur            x1, [fp, #-0x38]
    // 0xb36958: StoreField: r0->field_b = r1
    //     0xb36958: stur            w1, [x0, #0xb]
    // 0xb3695c: r0 = InkWell()
    //     0xb3695c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb36960: mov             x3, x0
    // 0xb36964: ldur            x0, [fp, #-0x40]
    // 0xb36968: stur            x3, [fp, #-0x38]
    // 0xb3696c: StoreField: r3->field_b = r0
    //     0xb3696c: stur            w0, [x3, #0xb]
    // 0xb36970: ldur            x2, [fp, #-0x10]
    // 0xb36974: r1 = Function '<anonymous closure>':.
    //     0xb36974: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aad8] AnonymousClosure: (0xb38950), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb36978: ldr             x1, [x1, #0xad8]
    // 0xb3697c: r0 = AllocateClosure()
    //     0xb3697c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb36980: mov             x1, x0
    // 0xb36984: ldur            x0, [fp, #-0x38]
    // 0xb36988: StoreField: r0->field_f = r1
    //     0xb36988: stur            w1, [x0, #0xf]
    // 0xb3698c: r2 = true
    //     0xb3698c: add             x2, NULL, #0x20  ; true
    // 0xb36990: StoreField: r0->field_43 = r2
    //     0xb36990: stur            w2, [x0, #0x43]
    // 0xb36994: r3 = Instance_BoxShape
    //     0xb36994: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb36998: ldr             x3, [x3, #0x80]
    // 0xb3699c: StoreField: r0->field_47 = r3
    //     0xb3699c: stur            w3, [x0, #0x47]
    // 0xb369a0: StoreField: r0->field_6f = r2
    //     0xb369a0: stur            w2, [x0, #0x6f]
    // 0xb369a4: r4 = false
    //     0xb369a4: add             x4, NULL, #0x30  ; false
    // 0xb369a8: StoreField: r0->field_73 = r4
    //     0xb369a8: stur            w4, [x0, #0x73]
    // 0xb369ac: StoreField: r0->field_83 = r2
    //     0xb369ac: stur            w2, [x0, #0x83]
    // 0xb369b0: StoreField: r0->field_7b = r4
    //     0xb369b0: stur            w4, [x0, #0x7b]
    // 0xb369b4: r1 = <FlexParentData>
    //     0xb369b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb369b8: ldr             x1, [x1, #0xe00]
    // 0xb369bc: r0 = Expanded()
    //     0xb369bc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb369c0: stur            x0, [fp, #-0x40]
    // 0xb369c4: StoreField: r0->field_13 = rZR
    //     0xb369c4: stur            xzr, [x0, #0x13]
    // 0xb369c8: r3 = Instance_FlexFit
    //     0xb369c8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb369cc: ldr             x3, [x3, #0xe08]
    // 0xb369d0: StoreField: r0->field_1b = r3
    //     0xb369d0: stur            w3, [x0, #0x1b]
    // 0xb369d4: ldur            x1, [fp, #-0x38]
    // 0xb369d8: StoreField: r0->field_b = r1
    //     0xb369d8: stur            w1, [x0, #0xb]
    // 0xb369dc: r1 = Null
    //     0xb369dc: mov             x1, NULL
    // 0xb369e0: r2 = 4
    //     0xb369e0: movz            x2, #0x4
    // 0xb369e4: r0 = AllocateArray()
    //     0xb369e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb369e8: mov             x2, x0
    // 0xb369ec: ldur            x0, [fp, #-0x48]
    // 0xb369f0: stur            x2, [fp, #-0x38]
    // 0xb369f4: StoreField: r2->field_f = r0
    //     0xb369f4: stur            w0, [x2, #0xf]
    // 0xb369f8: ldur            x0, [fp, #-0x40]
    // 0xb369fc: StoreField: r2->field_13 = r0
    //     0xb369fc: stur            w0, [x2, #0x13]
    // 0xb36a00: r1 = <Widget>
    //     0xb36a00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb36a04: r0 = AllocateGrowableArray()
    //     0xb36a04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb36a08: mov             x1, x0
    // 0xb36a0c: ldur            x0, [fp, #-0x38]
    // 0xb36a10: stur            x1, [fp, #-0x40]
    // 0xb36a14: StoreField: r1->field_f = r0
    //     0xb36a14: stur            w0, [x1, #0xf]
    // 0xb36a18: r5 = 4
    //     0xb36a18: movz            x5, #0x4
    // 0xb36a1c: StoreField: r1->field_b = r5
    //     0xb36a1c: stur            w5, [x1, #0xb]
    // 0xb36a20: r0 = Column()
    //     0xb36a20: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb36a24: mov             x2, x0
    // 0xb36a28: r6 = Instance_Axis
    //     0xb36a28: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb36a2c: stur            x2, [fp, #-0x38]
    // 0xb36a30: StoreField: r2->field_f = r6
    //     0xb36a30: stur            w6, [x2, #0xf]
    // 0xb36a34: r3 = Instance_MainAxisAlignment
    //     0xb36a34: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb36a38: ldr             x3, [x3, #0xa08]
    // 0xb36a3c: StoreField: r2->field_13 = r3
    //     0xb36a3c: stur            w3, [x2, #0x13]
    // 0xb36a40: r4 = Instance_MainAxisSize
    //     0xb36a40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb36a44: ldr             x4, [x4, #0xa10]
    // 0xb36a48: ArrayStore: r2[0] = r4  ; List_4
    //     0xb36a48: stur            w4, [x2, #0x17]
    // 0xb36a4c: r7 = Instance_CrossAxisAlignment
    //     0xb36a4c: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb36a50: ldr             x7, [x7, #0x890]
    // 0xb36a54: StoreField: r2->field_1b = r7
    //     0xb36a54: stur            w7, [x2, #0x1b]
    // 0xb36a58: r5 = Instance_VerticalDirection
    //     0xb36a58: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb36a5c: ldr             x5, [x5, #0xa20]
    // 0xb36a60: StoreField: r2->field_23 = r5
    //     0xb36a60: stur            w5, [x2, #0x23]
    // 0xb36a64: r6 = Instance_Clip
    //     0xb36a64: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb36a68: ldr             x6, [x6, #0x38]
    // 0xb36a6c: StoreField: r2->field_2b = r6
    //     0xb36a6c: stur            w6, [x2, #0x2b]
    // 0xb36a70: StoreField: r2->field_2f = rZR
    //     0xb36a70: stur            xzr, [x2, #0x2f]
    // 0xb36a74: ldur            x0, [fp, #-0x40]
    // 0xb36a78: StoreField: r2->field_b = r0
    //     0xb36a78: stur            w0, [x2, #0xb]
    // 0xb36a7c: ldur            x7, [fp, #-8]
    // 0xb36a80: LoadField: r0 = r7->field_f
    //     0xb36a80: ldur            w0, [x7, #0xf]
    // 0xb36a84: DecompressPointer r0
    //     0xb36a84: add             x0, x0, HEAP, lsl #32
    // 0xb36a88: LoadField: r8 = r0->field_b
    //     0xb36a88: ldur            w8, [x0, #0xb]
    // 0xb36a8c: DecompressPointer r8
    //     0xb36a8c: add             x8, x8, HEAP, lsl #32
    // 0xb36a90: cmp             w8, NULL
    // 0xb36a94: b.eq            #0xb37f20
    // 0xb36a98: LoadField: r0 = r8->field_1f
    //     0xb36a98: ldur            w0, [x8, #0x1f]
    // 0xb36a9c: DecompressPointer r0
    //     0xb36a9c: add             x0, x0, HEAP, lsl #32
    // 0xb36aa0: tbz             w0, #4, #0xb36aac
    // 0xb36aa4: mov             x8, x7
    // 0xb36aa8: b               #0xb36b44
    // 0xb36aac: LoadField: r0 = r8->field_f
    //     0xb36aac: ldur            w0, [x8, #0xf]
    // 0xb36ab0: DecompressPointer r0
    //     0xb36ab0: add             x0, x0, HEAP, lsl #32
    // 0xb36ab4: cmp             w0, NULL
    // 0xb36ab8: b.ne            #0xb36ac8
    // 0xb36abc: ldur            x9, [fp, #-0x10]
    // 0xb36ac0: r0 = Null
    //     0xb36ac0: mov             x0, NULL
    // 0xb36ac4: b               #0xb36b18
    // 0xb36ac8: ldur            x9, [fp, #-0x10]
    // 0xb36acc: LoadField: r10 = r0->field_f
    //     0xb36acc: ldur            w10, [x0, #0xf]
    // 0xb36ad0: DecompressPointer r10
    //     0xb36ad0: add             x10, x10, HEAP, lsl #32
    // 0xb36ad4: LoadField: r0 = r9->field_13
    //     0xb36ad4: ldur            w0, [x9, #0x13]
    // 0xb36ad8: DecompressPointer r0
    //     0xb36ad8: add             x0, x0, HEAP, lsl #32
    // 0xb36adc: LoadField: r1 = r10->field_b
    //     0xb36adc: ldur            w1, [x10, #0xb]
    // 0xb36ae0: r11 = LoadInt32Instr(r0)
    //     0xb36ae0: sbfx            x11, x0, #1, #0x1f
    //     0xb36ae4: tbz             w0, #0, #0xb36aec
    //     0xb36ae8: ldur            x11, [x0, #7]
    // 0xb36aec: r0 = LoadInt32Instr(r1)
    //     0xb36aec: sbfx            x0, x1, #1, #0x1f
    // 0xb36af0: mov             x1, x11
    // 0xb36af4: cmp             x1, x0
    // 0xb36af8: b.hs            #0xb37f24
    // 0xb36afc: LoadField: r0 = r10->field_f
    //     0xb36afc: ldur            w0, [x10, #0xf]
    // 0xb36b00: DecompressPointer r0
    //     0xb36b00: add             x0, x0, HEAP, lsl #32
    // 0xb36b04: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0xb36b04: add             x16, x0, x11, lsl #2
    //     0xb36b08: ldur            w1, [x16, #0xf]
    // 0xb36b0c: DecompressPointer r1
    //     0xb36b0c: add             x1, x1, HEAP, lsl #32
    // 0xb36b10: LoadField: r0 = r1->field_7
    //     0xb36b10: ldur            w0, [x1, #7]
    // 0xb36b14: DecompressPointer r0
    //     0xb36b14: add             x0, x0, HEAP, lsl #32
    // 0xb36b18: LoadField: r1 = r8->field_13
    //     0xb36b18: ldur            w1, [x8, #0x13]
    // 0xb36b1c: DecompressPointer r1
    //     0xb36b1c: add             x1, x1, HEAP, lsl #32
    // 0xb36b20: r8 = LoadClassIdInstr(r0)
    //     0xb36b20: ldur            x8, [x0, #-1]
    //     0xb36b24: ubfx            x8, x8, #0xc, #0x14
    // 0xb36b28: stp             x1, x0, [SP]
    // 0xb36b2c: mov             x0, x8
    // 0xb36b30: mov             lr, x0
    // 0xb36b34: ldr             lr, [x21, lr, lsl #3]
    // 0xb36b38: blr             lr
    // 0xb36b3c: tbnz            w0, #4, #0xb36c54
    // 0xb36b40: ldur            x8, [fp, #-8]
    // 0xb36b44: LoadField: r0 = r8->field_f
    //     0xb36b44: ldur            w0, [x8, #0xf]
    // 0xb36b48: DecompressPointer r0
    //     0xb36b48: add             x0, x0, HEAP, lsl #32
    // 0xb36b4c: LoadField: r1 = r0->field_b
    //     0xb36b4c: ldur            w1, [x0, #0xb]
    // 0xb36b50: DecompressPointer r1
    //     0xb36b50: add             x1, x1, HEAP, lsl #32
    // 0xb36b54: cmp             w1, NULL
    // 0xb36b58: b.eq            #0xb37f28
    // 0xb36b5c: LoadField: r0 = r1->field_f
    //     0xb36b5c: ldur            w0, [x1, #0xf]
    // 0xb36b60: DecompressPointer r0
    //     0xb36b60: add             x0, x0, HEAP, lsl #32
    // 0xb36b64: cmp             w0, NULL
    // 0xb36b68: b.ne            #0xb36b78
    // 0xb36b6c: ldur            x2, [fp, #-0x10]
    // 0xb36b70: r0 = Null
    //     0xb36b70: mov             x0, NULL
    // 0xb36b74: b               #0xb36bc8
    // 0xb36b78: ldur            x2, [fp, #-0x10]
    // 0xb36b7c: LoadField: r3 = r0->field_f
    //     0xb36b7c: ldur            w3, [x0, #0xf]
    // 0xb36b80: DecompressPointer r3
    //     0xb36b80: add             x3, x3, HEAP, lsl #32
    // 0xb36b84: LoadField: r0 = r2->field_13
    //     0xb36b84: ldur            w0, [x2, #0x13]
    // 0xb36b88: DecompressPointer r0
    //     0xb36b88: add             x0, x0, HEAP, lsl #32
    // 0xb36b8c: LoadField: r1 = r3->field_b
    //     0xb36b8c: ldur            w1, [x3, #0xb]
    // 0xb36b90: r4 = LoadInt32Instr(r0)
    //     0xb36b90: sbfx            x4, x0, #1, #0x1f
    //     0xb36b94: tbz             w0, #0, #0xb36b9c
    //     0xb36b98: ldur            x4, [x0, #7]
    // 0xb36b9c: r0 = LoadInt32Instr(r1)
    //     0xb36b9c: sbfx            x0, x1, #1, #0x1f
    // 0xb36ba0: mov             x1, x4
    // 0xb36ba4: cmp             x1, x0
    // 0xb36ba8: b.hs            #0xb37f2c
    // 0xb36bac: LoadField: r0 = r3->field_f
    //     0xb36bac: ldur            w0, [x3, #0xf]
    // 0xb36bb0: DecompressPointer r0
    //     0xb36bb0: add             x0, x0, HEAP, lsl #32
    // 0xb36bb4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb36bb4: add             x16, x0, x4, lsl #2
    //     0xb36bb8: ldur            w1, [x16, #0xf]
    // 0xb36bbc: DecompressPointer r1
    //     0xb36bbc: add             x1, x1, HEAP, lsl #32
    // 0xb36bc0: LoadField: r0 = r1->field_b
    //     0xb36bc0: ldur            w0, [x1, #0xb]
    // 0xb36bc4: DecompressPointer r0
    //     0xb36bc4: add             x0, x0, HEAP, lsl #32
    // 0xb36bc8: r1 = LoadClassIdInstr(r0)
    //     0xb36bc8: ldur            x1, [x0, #-1]
    //     0xb36bcc: ubfx            x1, x1, #0xc, #0x14
    // 0xb36bd0: r16 = "bumper_coupon"
    //     0xb36bd0: add             x16, PP, #0x12, lsl #12  ; [pp+0x128d8] "bumper_coupon"
    //     0xb36bd4: ldr             x16, [x16, #0x8d8]
    // 0xb36bd8: stp             x16, x0, [SP]
    // 0xb36bdc: mov             x0, x1
    // 0xb36be0: mov             lr, x0
    // 0xb36be4: ldr             lr, [x21, lr, lsl #3]
    // 0xb36be8: blr             lr
    // 0xb36bec: tbnz            w0, #4, #0xb36c54
    // 0xb36bf0: ldur            x2, [fp, #-0x10]
    // 0xb36bf4: LoadField: r1 = r2->field_f
    //     0xb36bf4: ldur            w1, [x2, #0xf]
    // 0xb36bf8: DecompressPointer r1
    //     0xb36bf8: add             x1, x1, HEAP, lsl #32
    // 0xb36bfc: r0 = of()
    //     0xb36bfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb36c00: LoadField: r1 = r0->field_87
    //     0xb36c00: ldur            w1, [x0, #0x87]
    // 0xb36c04: DecompressPointer r1
    //     0xb36c04: add             x1, x1, HEAP, lsl #32
    // 0xb36c08: LoadField: r0 = r1->field_7
    //     0xb36c08: ldur            w0, [x1, #7]
    // 0xb36c0c: DecompressPointer r0
    //     0xb36c0c: add             x0, x0, HEAP, lsl #32
    // 0xb36c10: r16 = Instance_Color
    //     0xb36c10: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb36c14: r30 = 12.000000
    //     0xb36c14: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb36c18: ldr             lr, [lr, #0x9e8]
    // 0xb36c1c: stp             lr, x16, [SP]
    // 0xb36c20: mov             x1, x0
    // 0xb36c24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb36c24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb36c28: ldr             x4, [x4, #0x9b8]
    // 0xb36c2c: r0 = copyWith()
    //     0xb36c2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb36c30: stur            x0, [fp, #-0x40]
    // 0xb36c34: r0 = Text()
    //     0xb36c34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb36c38: r9 = "APPLIED"
    //     0xb36c38: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xb36c3c: ldr             x9, [x9, #0x5e8]
    // 0xb36c40: StoreField: r0->field_b = r9
    //     0xb36c40: stur            w9, [x0, #0xb]
    // 0xb36c44: ldur            x1, [fp, #-0x40]
    // 0xb36c48: StoreField: r0->field_13 = r1
    //     0xb36c48: stur            w1, [x0, #0x13]
    // 0xb36c4c: mov             x1, x0
    // 0xb36c50: b               #0xb36cb4
    // 0xb36c54: ldur            x2, [fp, #-0x10]
    // 0xb36c58: LoadField: r1 = r2->field_f
    //     0xb36c58: ldur            w1, [x2, #0xf]
    // 0xb36c5c: DecompressPointer r1
    //     0xb36c5c: add             x1, x1, HEAP, lsl #32
    // 0xb36c60: r0 = of()
    //     0xb36c60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb36c64: LoadField: r1 = r0->field_87
    //     0xb36c64: ldur            w1, [x0, #0x87]
    // 0xb36c68: DecompressPointer r1
    //     0xb36c68: add             x1, x1, HEAP, lsl #32
    // 0xb36c6c: LoadField: r0 = r1->field_7
    //     0xb36c6c: ldur            w0, [x1, #7]
    // 0xb36c70: DecompressPointer r0
    //     0xb36c70: add             x0, x0, HEAP, lsl #32
    // 0xb36c74: r16 = Instance_Color
    //     0xb36c74: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb36c78: r30 = 14.000000
    //     0xb36c78: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb36c7c: ldr             lr, [lr, #0x1d8]
    // 0xb36c80: stp             lr, x16, [SP]
    // 0xb36c84: mov             x1, x0
    // 0xb36c88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb36c88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb36c8c: ldr             x4, [x4, #0x9b8]
    // 0xb36c90: r0 = copyWith()
    //     0xb36c90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb36c94: stur            x0, [fp, #-0x40]
    // 0xb36c98: r0 = Text()
    //     0xb36c98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb36c9c: r10 = "APPLY"
    //     0xb36c9c: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xb36ca0: ldr             x10, [x10, #0x5f0]
    // 0xb36ca4: StoreField: r0->field_b = r10
    //     0xb36ca4: stur            w10, [x0, #0xb]
    // 0xb36ca8: ldur            x1, [fp, #-0x40]
    // 0xb36cac: StoreField: r0->field_13 = r1
    //     0xb36cac: stur            w1, [x0, #0x13]
    // 0xb36cb0: mov             x1, x0
    // 0xb36cb4: ldur            x0, [fp, #-0x38]
    // 0xb36cb8: stur            x1, [fp, #-0x40]
    // 0xb36cbc: r0 = InkWell()
    //     0xb36cbc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb36cc0: mov             x3, x0
    // 0xb36cc4: ldur            x0, [fp, #-0x40]
    // 0xb36cc8: stur            x3, [fp, #-0x48]
    // 0xb36ccc: StoreField: r3->field_b = r0
    //     0xb36ccc: stur            w0, [x3, #0xb]
    // 0xb36cd0: ldur            x2, [fp, #-0x10]
    // 0xb36cd4: r1 = Function '<anonymous closure>':.
    //     0xb36cd4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aae0] AnonymousClosure: (0xb385ec), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb36cd8: ldr             x1, [x1, #0xae0]
    // 0xb36cdc: r0 = AllocateClosure()
    //     0xb36cdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb36ce0: mov             x1, x0
    // 0xb36ce4: ldur            x0, [fp, #-0x48]
    // 0xb36ce8: StoreField: r0->field_f = r1
    //     0xb36ce8: stur            w1, [x0, #0xf]
    // 0xb36cec: r11 = true
    //     0xb36cec: add             x11, NULL, #0x20  ; true
    // 0xb36cf0: StoreField: r0->field_43 = r11
    //     0xb36cf0: stur            w11, [x0, #0x43]
    // 0xb36cf4: r12 = Instance_BoxShape
    //     0xb36cf4: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb36cf8: ldr             x12, [x12, #0x80]
    // 0xb36cfc: StoreField: r0->field_47 = r12
    //     0xb36cfc: stur            w12, [x0, #0x47]
    // 0xb36d00: StoreField: r0->field_6f = r11
    //     0xb36d00: stur            w11, [x0, #0x6f]
    // 0xb36d04: r13 = false
    //     0xb36d04: add             x13, NULL, #0x30  ; false
    // 0xb36d08: StoreField: r0->field_73 = r13
    //     0xb36d08: stur            w13, [x0, #0x73]
    // 0xb36d0c: StoreField: r0->field_83 = r11
    //     0xb36d0c: stur            w11, [x0, #0x83]
    // 0xb36d10: StoreField: r0->field_7b = r13
    //     0xb36d10: stur            w13, [x0, #0x7b]
    // 0xb36d14: r1 = <FlexParentData>
    //     0xb36d14: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb36d18: ldr             x1, [x1, #0xe00]
    // 0xb36d1c: r0 = Expanded()
    //     0xb36d1c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb36d20: r14 = 1
    //     0xb36d20: movz            x14, #0x1
    // 0xb36d24: stur            x0, [fp, #-0x40]
    // 0xb36d28: StoreField: r0->field_13 = r14
    //     0xb36d28: stur            x14, [x0, #0x13]
    // 0xb36d2c: r19 = Instance_FlexFit
    //     0xb36d2c: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb36d30: ldr             x19, [x19, #0xe08]
    // 0xb36d34: StoreField: r0->field_1b = r19
    //     0xb36d34: stur            w19, [x0, #0x1b]
    // 0xb36d38: ldur            x1, [fp, #-0x48]
    // 0xb36d3c: StoreField: r0->field_b = r1
    //     0xb36d3c: stur            w1, [x0, #0xb]
    // 0xb36d40: r1 = Null
    //     0xb36d40: mov             x1, NULL
    // 0xb36d44: r2 = 8
    //     0xb36d44: movz            x2, #0x8
    // 0xb36d48: r0 = AllocateArray()
    //     0xb36d48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb36d4c: stur            x0, [fp, #-0x48]
    // 0xb36d50: r16 = Instance_Expanded
    //     0xb36d50: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a600] Obj!Expanded@d656d1
    //     0xb36d54: ldr             x16, [x16, #0x600]
    // 0xb36d58: StoreField: r0->field_f = r16
    //     0xb36d58: stur            w16, [x0, #0xf]
    // 0xb36d5c: ldur            x1, [fp, #-0x38]
    // 0xb36d60: StoreField: r0->field_13 = r1
    //     0xb36d60: stur            w1, [x0, #0x13]
    // 0xb36d64: r16 = Instance_Spacer
    //     0xb36d64: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb36d68: ldr             x16, [x16, #0xf0]
    // 0xb36d6c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb36d6c: stur            w16, [x0, #0x17]
    // 0xb36d70: ldur            x1, [fp, #-0x40]
    // 0xb36d74: StoreField: r0->field_1b = r1
    //     0xb36d74: stur            w1, [x0, #0x1b]
    // 0xb36d78: r1 = <Widget>
    //     0xb36d78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb36d7c: r0 = AllocateGrowableArray()
    //     0xb36d7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb36d80: mov             x1, x0
    // 0xb36d84: ldur            x0, [fp, #-0x48]
    // 0xb36d88: stur            x1, [fp, #-0x38]
    // 0xb36d8c: StoreField: r1->field_f = r0
    //     0xb36d8c: stur            w0, [x1, #0xf]
    // 0xb36d90: r20 = 8
    //     0xb36d90: movz            x20, #0x8
    // 0xb36d94: StoreField: r1->field_b = r20
    //     0xb36d94: stur            w20, [x1, #0xb]
    // 0xb36d98: r0 = Row()
    //     0xb36d98: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb36d9c: r23 = Instance_Axis
    //     0xb36d9c: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb36da0: stur            x0, [fp, #-0x40]
    // 0xb36da4: StoreField: r0->field_f = r23
    //     0xb36da4: stur            w23, [x0, #0xf]
    // 0xb36da8: r24 = Instance_MainAxisAlignment
    //     0xb36da8: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb36dac: ldr             x24, [x24, #0xa08]
    // 0xb36db0: StoreField: r0->field_13 = r24
    //     0xb36db0: stur            w24, [x0, #0x13]
    // 0xb36db4: r25 = Instance_MainAxisSize
    //     0xb36db4: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb36db8: ldr             x25, [x25, #0xa10]
    // 0xb36dbc: ArrayStore: r0[0] = r25  ; List_4
    //     0xb36dbc: stur            w25, [x0, #0x17]
    // 0xb36dc0: r1 = Instance_CrossAxisAlignment
    //     0xb36dc0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb36dc4: ldr             x1, [x1, #0xa18]
    // 0xb36dc8: StoreField: r0->field_1b = r1
    //     0xb36dc8: stur            w1, [x0, #0x1b]
    // 0xb36dcc: r1 = Instance_VerticalDirection
    //     0xb36dcc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb36dd0: ldr             x1, [x1, #0xa20]
    // 0xb36dd4: StoreField: r0->field_23 = r1
    //     0xb36dd4: stur            w1, [x0, #0x23]
    // 0xb36dd8: r1 = Instance_Clip
    //     0xb36dd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb36ddc: ldr             x1, [x1, #0x38]
    // 0xb36de0: StoreField: r0->field_2b = r1
    //     0xb36de0: stur            w1, [x0, #0x2b]
    // 0xb36de4: StoreField: r0->field_2f = rZR
    //     0xb36de4: stur            xzr, [x0, #0x2f]
    // 0xb36de8: ldur            x1, [fp, #-0x38]
    // 0xb36dec: StoreField: r0->field_b = r1
    //     0xb36dec: stur            w1, [x0, #0xb]
    // 0xb36df0: r0 = Padding()
    //     0xb36df0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb36df4: mov             x1, x0
    // 0xb36df8: r0 = Instance_EdgeInsets
    //     0xb36df8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb36dfc: ldr             x0, [x0, #0x980]
    // 0xb36e00: stur            x1, [fp, #-0x38]
    // 0xb36e04: StoreField: r1->field_f = r0
    //     0xb36e04: stur            w0, [x1, #0xf]
    // 0xb36e08: ldur            x2, [fp, #-0x40]
    // 0xb36e0c: StoreField: r1->field_b = r2
    //     0xb36e0c: stur            w2, [x1, #0xb]
    // 0xb36e10: r0 = Container()
    //     0xb36e10: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb36e14: stur            x0, [fp, #-0x40]
    // 0xb36e18: r16 = 100.000000
    //     0xb36e18: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb36e1c: ldur            lr, [fp, #-0x30]
    // 0xb36e20: stp             lr, x16, [SP, #8]
    // 0xb36e24: ldur            x16, [fp, #-0x38]
    // 0xb36e28: str             x16, [SP]
    // 0xb36e2c: mov             x1, x0
    // 0xb36e30: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb36e30: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb36e34: ldr             x4, [x4, #0xc78]
    // 0xb36e38: r0 = Container()
    //     0xb36e38: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb36e3c: r1 = <Path>
    //     0xb36e3c: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb36e40: ldr             x1, [x1, #0xd30]
    // 0xb36e44: r0 = MovieTicketClipper()
    //     0xb36e44: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb36e48: stur            x0, [fp, #-0x30]
    // 0xb36e4c: r0 = ClipPath()
    //     0xb36e4c: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb36e50: mov             x1, x0
    // 0xb36e54: ldur            x0, [fp, #-0x30]
    // 0xb36e58: StoreField: r1->field_f = r0
    //     0xb36e58: stur            w0, [x1, #0xf]
    // 0xb36e5c: r0 = Instance_Clip
    //     0xb36e5c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb36e60: ldr             x0, [x0, #0x138]
    // 0xb36e64: StoreField: r1->field_13 = r0
    //     0xb36e64: stur            w0, [x1, #0x13]
    // 0xb36e68: ldur            x0, [fp, #-0x40]
    // 0xb36e6c: StoreField: r1->field_b = r0
    //     0xb36e6c: stur            w0, [x1, #0xb]
    // 0xb36e70: mov             x0, x1
    // 0xb36e74: b               #0xb37ea4
    // 0xb36e78: ldur            x8, [fp, #-8]
    // 0xb36e7c: r11 = true
    //     0xb36e7c: add             x11, NULL, #0x20  ; true
    // 0xb36e80: r5 = 4
    //     0xb36e80: movz            x5, #0x4
    // 0xb36e84: r0 = "Know "
    //     0xb36e84: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xb36e88: ldr             x0, [x0, #0x5d0]
    // 0xb36e8c: r2 = "More"
    //     0xb36e8c: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xb36e90: ldr             x2, [x2, #0x5d8]
    // 0xb36e94: r4 = Instance_EdgeInsets
    //     0xb36e94: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb36e98: ldr             x4, [x4, #0xc40]
    // 0xb36e9c: r7 = Instance_CrossAxisAlignment
    //     0xb36e9c: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb36ea0: ldr             x7, [x7, #0x890]
    // 0xb36ea4: r24 = Instance_MainAxisAlignment
    //     0xb36ea4: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb36ea8: ldr             x24, [x24, #0xa08]
    // 0xb36eac: r13 = false
    //     0xb36eac: add             x13, NULL, #0x30  ; false
    // 0xb36eb0: r10 = "APPLY"
    //     0xb36eb0: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xb36eb4: ldr             x10, [x10, #0x5f0]
    // 0xb36eb8: r9 = "APPLIED"
    //     0xb36eb8: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xb36ebc: ldr             x9, [x9, #0x5e8]
    // 0xb36ec0: r25 = Instance_MainAxisSize
    //     0xb36ec0: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb36ec4: ldr             x25, [x25, #0xa10]
    // 0xb36ec8: r12 = Instance_BoxShape
    //     0xb36ec8: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb36ecc: ldr             x12, [x12, #0x80]
    // 0xb36ed0: r19 = Instance_FlexFit
    //     0xb36ed0: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb36ed4: ldr             x19, [x19, #0xe08]
    // 0xb36ed8: r20 = 8
    //     0xb36ed8: movz            x20, #0x8
    // 0xb36edc: r1 = Instance_CrossAxisAlignment
    //     0xb36edc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb36ee0: ldr             x1, [x1, #0xa18]
    // 0xb36ee4: r23 = Instance_Axis
    //     0xb36ee4: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb36ee8: r3 = Instance__DeferringMouseCursor
    //     0xb36ee8: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb36eec: r6 = Instance_Axis
    //     0xb36eec: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb36ef0: d1 = 1.000000
    //     0xb36ef0: fmov            d1, #1.00000000
    // 0xb36ef4: d2 = 0.500000
    //     0xb36ef4: fmov            d2, #0.50000000
    // 0xb36ef8: r14 = 1
    //     0xb36ef8: movz            x14, #0x1
    // 0xb36efc: b               #0xb36f84
    // 0xb36f00: ldur            x8, [fp, #-8]
    // 0xb36f04: r11 = true
    //     0xb36f04: add             x11, NULL, #0x20  ; true
    // 0xb36f08: r5 = 4
    //     0xb36f08: movz            x5, #0x4
    // 0xb36f0c: r0 = "Know "
    //     0xb36f0c: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xb36f10: ldr             x0, [x0, #0x5d0]
    // 0xb36f14: r2 = "More"
    //     0xb36f14: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xb36f18: ldr             x2, [x2, #0x5d8]
    // 0xb36f1c: r4 = Instance_EdgeInsets
    //     0xb36f1c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb36f20: ldr             x4, [x4, #0xc40]
    // 0xb36f24: r7 = Instance_CrossAxisAlignment
    //     0xb36f24: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb36f28: ldr             x7, [x7, #0x890]
    // 0xb36f2c: r24 = Instance_MainAxisAlignment
    //     0xb36f2c: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb36f30: ldr             x24, [x24, #0xa08]
    // 0xb36f34: r13 = false
    //     0xb36f34: add             x13, NULL, #0x30  ; false
    // 0xb36f38: r10 = "APPLY"
    //     0xb36f38: add             x10, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xb36f3c: ldr             x10, [x10, #0x5f0]
    // 0xb36f40: r9 = "APPLIED"
    //     0xb36f40: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xb36f44: ldr             x9, [x9, #0x5e8]
    // 0xb36f48: r25 = Instance_MainAxisSize
    //     0xb36f48: add             x25, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb36f4c: ldr             x25, [x25, #0xa10]
    // 0xb36f50: r12 = Instance_BoxShape
    //     0xb36f50: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb36f54: ldr             x12, [x12, #0x80]
    // 0xb36f58: r19 = Instance_FlexFit
    //     0xb36f58: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb36f5c: ldr             x19, [x19, #0xe08]
    // 0xb36f60: r20 = 8
    //     0xb36f60: movz            x20, #0x8
    // 0xb36f64: r1 = Instance_CrossAxisAlignment
    //     0xb36f64: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb36f68: ldr             x1, [x1, #0xa18]
    // 0xb36f6c: r23 = Instance_Axis
    //     0xb36f6c: ldr             x23, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb36f70: r3 = Instance__DeferringMouseCursor
    //     0xb36f70: ldr             x3, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb36f74: r6 = Instance_Axis
    //     0xb36f74: ldr             x6, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb36f78: d1 = 1.000000
    //     0xb36f78: fmov            d1, #1.00000000
    // 0xb36f7c: d2 = 0.500000
    //     0xb36f7c: fmov            d2, #0.50000000
    // 0xb36f80: r14 = 1
    //     0xb36f80: movz            x14, #0x1
    // 0xb36f84: r1 = Instance_MaterialColor
    //     0xb36f84: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb36f88: ldr             x1, [x1, #0xdc0]
    // 0xb36f8c: d0 = 0.300000
    //     0xb36f8c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb36f90: ldr             d0, [x17, #0x658]
    // 0xb36f94: r0 = withOpacity()
    //     0xb36f94: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb36f98: mov             x3, x0
    // 0xb36f9c: ldur            x2, [fp, #-8]
    // 0xb36fa0: stur            x3, [fp, #-0x30]
    // 0xb36fa4: LoadField: r0 = r2->field_f
    //     0xb36fa4: ldur            w0, [x2, #0xf]
    // 0xb36fa8: DecompressPointer r0
    //     0xb36fa8: add             x0, x0, HEAP, lsl #32
    // 0xb36fac: LoadField: r1 = r0->field_b
    //     0xb36fac: ldur            w1, [x0, #0xb]
    // 0xb36fb0: DecompressPointer r1
    //     0xb36fb0: add             x1, x1, HEAP, lsl #32
    // 0xb36fb4: cmp             w1, NULL
    // 0xb36fb8: b.eq            #0xb37f30
    // 0xb36fbc: LoadField: r0 = r1->field_f
    //     0xb36fbc: ldur            w0, [x1, #0xf]
    // 0xb36fc0: DecompressPointer r0
    //     0xb36fc0: add             x0, x0, HEAP, lsl #32
    // 0xb36fc4: cmp             w0, NULL
    // 0xb36fc8: b.ne            #0xb36fd8
    // 0xb36fcc: ldur            x4, [fp, #-0x10]
    // 0xb36fd0: r0 = Null
    //     0xb36fd0: mov             x0, NULL
    // 0xb36fd4: b               #0xb37028
    // 0xb36fd8: ldur            x4, [fp, #-0x10]
    // 0xb36fdc: LoadField: r5 = r0->field_f
    //     0xb36fdc: ldur            w5, [x0, #0xf]
    // 0xb36fe0: DecompressPointer r5
    //     0xb36fe0: add             x5, x5, HEAP, lsl #32
    // 0xb36fe4: LoadField: r0 = r4->field_13
    //     0xb36fe4: ldur            w0, [x4, #0x13]
    // 0xb36fe8: DecompressPointer r0
    //     0xb36fe8: add             x0, x0, HEAP, lsl #32
    // 0xb36fec: LoadField: r1 = r5->field_b
    //     0xb36fec: ldur            w1, [x5, #0xb]
    // 0xb36ff0: r6 = LoadInt32Instr(r0)
    //     0xb36ff0: sbfx            x6, x0, #1, #0x1f
    //     0xb36ff4: tbz             w0, #0, #0xb36ffc
    //     0xb36ff8: ldur            x6, [x0, #7]
    // 0xb36ffc: r0 = LoadInt32Instr(r1)
    //     0xb36ffc: sbfx            x0, x1, #1, #0x1f
    // 0xb37000: mov             x1, x6
    // 0xb37004: cmp             x1, x0
    // 0xb37008: b.hs            #0xb37f34
    // 0xb3700c: LoadField: r0 = r5->field_f
    //     0xb3700c: ldur            w0, [x5, #0xf]
    // 0xb37010: DecompressPointer r0
    //     0xb37010: add             x0, x0, HEAP, lsl #32
    // 0xb37014: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb37014: add             x16, x0, x6, lsl #2
    //     0xb37018: ldur            w1, [x16, #0xf]
    // 0xb3701c: DecompressPointer r1
    //     0xb3701c: add             x1, x1, HEAP, lsl #32
    // 0xb37020: LoadField: r0 = r1->field_b
    //     0xb37020: ldur            w0, [x1, #0xb]
    // 0xb37024: DecompressPointer r0
    //     0xb37024: add             x0, x0, HEAP, lsl #32
    // 0xb37028: r1 = LoadClassIdInstr(r0)
    //     0xb37028: ldur            x1, [x0, #-1]
    //     0xb3702c: ubfx            x1, x1, #0xc, #0x14
    // 0xb37030: r16 = "free_gift"
    //     0xb37030: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a608] "free_gift"
    //     0xb37034: ldr             x16, [x16, #0x608]
    // 0xb37038: stp             x16, x0, [SP]
    // 0xb3703c: mov             x0, x1
    // 0xb37040: mov             lr, x0
    // 0xb37044: ldr             lr, [x21, lr, lsl #3]
    // 0xb37048: blr             lr
    // 0xb3704c: tbz             w0, #4, #0xb371b4
    // 0xb37050: ldur            x2, [fp, #-8]
    // 0xb37054: LoadField: r0 = r2->field_f
    //     0xb37054: ldur            w0, [x2, #0xf]
    // 0xb37058: DecompressPointer r0
    //     0xb37058: add             x0, x0, HEAP, lsl #32
    // 0xb3705c: LoadField: r3 = r0->field_b
    //     0xb3705c: ldur            w3, [x0, #0xb]
    // 0xb37060: DecompressPointer r3
    //     0xb37060: add             x3, x3, HEAP, lsl #32
    // 0xb37064: cmp             w3, NULL
    // 0xb37068: b.eq            #0xb37f38
    // 0xb3706c: LoadField: r0 = r3->field_f
    //     0xb3706c: ldur            w0, [x3, #0xf]
    // 0xb37070: DecompressPointer r0
    //     0xb37070: add             x0, x0, HEAP, lsl #32
    // 0xb37074: cmp             w0, NULL
    // 0xb37078: b.ne            #0xb37088
    // 0xb3707c: ldur            x4, [fp, #-0x10]
    // 0xb37080: r0 = Null
    //     0xb37080: mov             x0, NULL
    // 0xb37084: b               #0xb370d8
    // 0xb37088: ldur            x4, [fp, #-0x10]
    // 0xb3708c: LoadField: r5 = r0->field_f
    //     0xb3708c: ldur            w5, [x0, #0xf]
    // 0xb37090: DecompressPointer r5
    //     0xb37090: add             x5, x5, HEAP, lsl #32
    // 0xb37094: LoadField: r0 = r4->field_13
    //     0xb37094: ldur            w0, [x4, #0x13]
    // 0xb37098: DecompressPointer r0
    //     0xb37098: add             x0, x0, HEAP, lsl #32
    // 0xb3709c: LoadField: r1 = r5->field_b
    //     0xb3709c: ldur            w1, [x5, #0xb]
    // 0xb370a0: r6 = LoadInt32Instr(r0)
    //     0xb370a0: sbfx            x6, x0, #1, #0x1f
    //     0xb370a4: tbz             w0, #0, #0xb370ac
    //     0xb370a8: ldur            x6, [x0, #7]
    // 0xb370ac: r0 = LoadInt32Instr(r1)
    //     0xb370ac: sbfx            x0, x1, #1, #0x1f
    // 0xb370b0: mov             x1, x6
    // 0xb370b4: cmp             x1, x0
    // 0xb370b8: b.hs            #0xb37f3c
    // 0xb370bc: LoadField: r0 = r5->field_f
    //     0xb370bc: ldur            w0, [x5, #0xf]
    // 0xb370c0: DecompressPointer r0
    //     0xb370c0: add             x0, x0, HEAP, lsl #32
    // 0xb370c4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb370c4: add             x16, x0, x6, lsl #2
    //     0xb370c8: ldur            w1, [x16, #0xf]
    // 0xb370cc: DecompressPointer r1
    //     0xb370cc: add             x1, x1, HEAP, lsl #32
    // 0xb370d0: LoadField: r0 = r1->field_27
    //     0xb370d0: ldur            w0, [x1, #0x27]
    // 0xb370d4: DecompressPointer r0
    //     0xb370d4: add             x0, x0, HEAP, lsl #32
    // 0xb370d8: cmp             w0, NULL
    // 0xb370dc: b.ne            #0xb37170
    // 0xb370e0: LoadField: r0 = r3->field_f
    //     0xb370e0: ldur            w0, [x3, #0xf]
    // 0xb370e4: DecompressPointer r0
    //     0xb370e4: add             x0, x0, HEAP, lsl #32
    // 0xb370e8: cmp             w0, NULL
    // 0xb370ec: b.ne            #0xb370f8
    // 0xb370f0: r0 = Null
    //     0xb370f0: mov             x0, NULL
    // 0xb370f4: b               #0xb37144
    // 0xb370f8: LoadField: r3 = r0->field_f
    //     0xb370f8: ldur            w3, [x0, #0xf]
    // 0xb370fc: DecompressPointer r3
    //     0xb370fc: add             x3, x3, HEAP, lsl #32
    // 0xb37100: LoadField: r0 = r4->field_13
    //     0xb37100: ldur            w0, [x4, #0x13]
    // 0xb37104: DecompressPointer r0
    //     0xb37104: add             x0, x0, HEAP, lsl #32
    // 0xb37108: LoadField: r1 = r3->field_b
    //     0xb37108: ldur            w1, [x3, #0xb]
    // 0xb3710c: r5 = LoadInt32Instr(r0)
    //     0xb3710c: sbfx            x5, x0, #1, #0x1f
    //     0xb37110: tbz             w0, #0, #0xb37118
    //     0xb37114: ldur            x5, [x0, #7]
    // 0xb37118: r0 = LoadInt32Instr(r1)
    //     0xb37118: sbfx            x0, x1, #1, #0x1f
    // 0xb3711c: mov             x1, x5
    // 0xb37120: cmp             x1, x0
    // 0xb37124: b.hs            #0xb37f40
    // 0xb37128: LoadField: r0 = r3->field_f
    //     0xb37128: ldur            w0, [x3, #0xf]
    // 0xb3712c: DecompressPointer r0
    //     0xb3712c: add             x0, x0, HEAP, lsl #32
    // 0xb37130: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb37130: add             x16, x0, x5, lsl #2
    //     0xb37134: ldur            w1, [x16, #0xf]
    // 0xb37138: DecompressPointer r1
    //     0xb37138: add             x1, x1, HEAP, lsl #32
    // 0xb3713c: LoadField: r0 = r1->field_f
    //     0xb3713c: ldur            w0, [x1, #0xf]
    // 0xb37140: DecompressPointer r0
    //     0xb37140: add             x0, x0, HEAP, lsl #32
    // 0xb37144: r1 = LoadClassIdInstr(r0)
    //     0xb37144: ldur            x1, [x0, #-1]
    //     0xb37148: ubfx            x1, x1, #0xc, #0x14
    // 0xb3714c: r16 = "sale_event_static_coupon"
    //     0xb3714c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xb37150: ldr             x16, [x16, #0x610]
    // 0xb37154: stp             x16, x0, [SP]
    // 0xb37158: mov             x0, x1
    // 0xb3715c: mov             lr, x0
    // 0xb37160: ldr             lr, [x21, lr, lsl #3]
    // 0xb37164: blr             lr
    // 0xb37168: tbnz            w0, #4, #0xb37180
    // 0xb3716c: b               #0xb37174
    // 0xb37170: tbnz            w0, #4, #0xb37180
    // 0xb37174: r0 = Instance_Color
    //     0xb37174: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb37178: ldr             x0, [x0, #0x858]
    // 0xb3717c: b               #0xb3718c
    // 0xb37180: r1 = Instance_Color
    //     0xb37180: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb37184: d0 = 0.400000
    //     0xb37184: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb37188: r0 = withOpacity()
    //     0xb37188: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3718c: stur            x0, [fp, #-0x38]
    // 0xb37190: r0 = Icon()
    //     0xb37190: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb37194: mov             x1, x0
    // 0xb37198: r0 = Instance_IconData
    //     0xb37198: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a618] Obj!IconData@d55541
    //     0xb3719c: ldr             x0, [x0, #0x618]
    // 0xb371a0: StoreField: r1->field_b = r0
    //     0xb371a0: stur            w0, [x1, #0xb]
    // 0xb371a4: ldur            x0, [fp, #-0x38]
    // 0xb371a8: StoreField: r1->field_23 = r0
    //     0xb371a8: stur            w0, [x1, #0x23]
    // 0xb371ac: mov             x3, x1
    // 0xb371b0: b               #0xb371ec
    // 0xb371b4: r0 = SvgPicture()
    //     0xb371b4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb371b8: stur            x0, [fp, #-0x38]
    // 0xb371bc: r16 = 23.000000
    //     0xb371bc: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aae8] 23
    //     0xb371c0: ldr             x16, [x16, #0xae8]
    // 0xb371c4: r30 = 23.000000
    //     0xb371c4: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6aae8] 23
    //     0xb371c8: ldr             lr, [lr, #0xae8]
    // 0xb371cc: stp             lr, x16, [SP]
    // 0xb371d0: mov             x1, x0
    // 0xb371d4: r2 = "assets/images/gift-icon-popup.svg"
    //     0xb371d4: add             x2, PP, #0x52, lsl #12  ; [pp+0x528e8] "assets/images/gift-icon-popup.svg"
    //     0xb371d8: ldr             x2, [x2, #0x8e8]
    // 0xb371dc: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0xb371dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0xb371e0: ldr             x4, [x4, #0x900]
    // 0xb371e4: r0 = SvgPicture.asset()
    //     0xb371e4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb371e8: ldur            x3, [fp, #-0x38]
    // 0xb371ec: ldur            x0, [fp, #-8]
    // 0xb371f0: ldur            x2, [fp, #-0x10]
    // 0xb371f4: stur            x3, [fp, #-0x38]
    // 0xb371f8: r1 = <FlexParentData>
    //     0xb371f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb371fc: ldr             x1, [x1, #0xe00]
    // 0xb37200: r0 = Expanded()
    //     0xb37200: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb37204: stur            x0, [fp, #-0x40]
    // 0xb37208: StoreField: r0->field_13 = rZR
    //     0xb37208: stur            xzr, [x0, #0x13]
    // 0xb3720c: r2 = Instance_FlexFit
    //     0xb3720c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb37210: ldr             x2, [x2, #0xe08]
    // 0xb37214: StoreField: r0->field_1b = r2
    //     0xb37214: stur            w2, [x0, #0x1b]
    // 0xb37218: ldur            x1, [fp, #-0x38]
    // 0xb3721c: StoreField: r0->field_b = r1
    //     0xb3721c: stur            w1, [x0, #0xb]
    // 0xb37220: ldur            x3, [fp, #-0x10]
    // 0xb37224: LoadField: r1 = r3->field_f
    //     0xb37224: ldur            w1, [x3, #0xf]
    // 0xb37228: DecompressPointer r1
    //     0xb37228: add             x1, x1, HEAP, lsl #32
    // 0xb3722c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb3722c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb37230: r0 = _of()
    //     0xb37230: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb37234: LoadField: r1 = r0->field_7
    //     0xb37234: ldur            w1, [x0, #7]
    // 0xb37238: DecompressPointer r1
    //     0xb37238: add             x1, x1, HEAP, lsl #32
    // 0xb3723c: LoadField: d0 = r1->field_7
    //     0xb3723c: ldur            d0, [x1, #7]
    // 0xb37240: d1 = 0.500000
    //     0xb37240: fmov            d1, #0.50000000
    // 0xb37244: fmul            d2, d0, d1
    // 0xb37248: ldur            x2, [fp, #-8]
    // 0xb3724c: stur            d2, [fp, #-0x68]
    // 0xb37250: LoadField: r0 = r2->field_f
    //     0xb37250: ldur            w0, [x2, #0xf]
    // 0xb37254: DecompressPointer r0
    //     0xb37254: add             x0, x0, HEAP, lsl #32
    // 0xb37258: LoadField: r1 = r0->field_b
    //     0xb37258: ldur            w1, [x0, #0xb]
    // 0xb3725c: DecompressPointer r1
    //     0xb3725c: add             x1, x1, HEAP, lsl #32
    // 0xb37260: cmp             w1, NULL
    // 0xb37264: b.eq            #0xb37f44
    // 0xb37268: LoadField: r0 = r1->field_f
    //     0xb37268: ldur            w0, [x1, #0xf]
    // 0xb3726c: DecompressPointer r0
    //     0xb3726c: add             x0, x0, HEAP, lsl #32
    // 0xb37270: cmp             w0, NULL
    // 0xb37274: b.ne            #0xb37284
    // 0xb37278: ldur            x3, [fp, #-0x10]
    // 0xb3727c: r0 = Null
    //     0xb3727c: mov             x0, NULL
    // 0xb37280: b               #0xb372d4
    // 0xb37284: ldur            x3, [fp, #-0x10]
    // 0xb37288: LoadField: r4 = r0->field_f
    //     0xb37288: ldur            w4, [x0, #0xf]
    // 0xb3728c: DecompressPointer r4
    //     0xb3728c: add             x4, x4, HEAP, lsl #32
    // 0xb37290: LoadField: r0 = r3->field_13
    //     0xb37290: ldur            w0, [x3, #0x13]
    // 0xb37294: DecompressPointer r0
    //     0xb37294: add             x0, x0, HEAP, lsl #32
    // 0xb37298: LoadField: r1 = r4->field_b
    //     0xb37298: ldur            w1, [x4, #0xb]
    // 0xb3729c: r5 = LoadInt32Instr(r0)
    //     0xb3729c: sbfx            x5, x0, #1, #0x1f
    //     0xb372a0: tbz             w0, #0, #0xb372a8
    //     0xb372a4: ldur            x5, [x0, #7]
    // 0xb372a8: r0 = LoadInt32Instr(r1)
    //     0xb372a8: sbfx            x0, x1, #1, #0x1f
    // 0xb372ac: mov             x1, x5
    // 0xb372b0: cmp             x1, x0
    // 0xb372b4: b.hs            #0xb37f48
    // 0xb372b8: LoadField: r0 = r4->field_f
    //     0xb372b8: ldur            w0, [x4, #0xf]
    // 0xb372bc: DecompressPointer r0
    //     0xb372bc: add             x0, x0, HEAP, lsl #32
    // 0xb372c0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb372c0: add             x16, x0, x5, lsl #2
    //     0xb372c4: ldur            w1, [x16, #0xf]
    // 0xb372c8: DecompressPointer r1
    //     0xb372c8: add             x1, x1, HEAP, lsl #32
    // 0xb372cc: LoadField: r0 = r1->field_13
    //     0xb372cc: ldur            w0, [x1, #0x13]
    // 0xb372d0: DecompressPointer r0
    //     0xb372d0: add             x0, x0, HEAP, lsl #32
    // 0xb372d4: cmp             w0, NULL
    // 0xb372d8: b.ne            #0xb372e0
    // 0xb372dc: r0 = ""
    //     0xb372dc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb372e0: stur            x0, [fp, #-0x38]
    // 0xb372e4: LoadField: r1 = r3->field_f
    //     0xb372e4: ldur            w1, [x3, #0xf]
    // 0xb372e8: DecompressPointer r1
    //     0xb372e8: add             x1, x1, HEAP, lsl #32
    // 0xb372ec: r0 = of()
    //     0xb372ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb372f0: LoadField: r1 = r0->field_87
    //     0xb372f0: ldur            w1, [x0, #0x87]
    // 0xb372f4: DecompressPointer r1
    //     0xb372f4: add             x1, x1, HEAP, lsl #32
    // 0xb372f8: LoadField: r2 = r1->field_2b
    //     0xb372f8: ldur            w2, [x1, #0x2b]
    // 0xb372fc: DecompressPointer r2
    //     0xb372fc: add             x2, x2, HEAP, lsl #32
    // 0xb37300: ldur            x3, [fp, #-8]
    // 0xb37304: stur            x2, [fp, #-0x48]
    // 0xb37308: LoadField: r0 = r3->field_f
    //     0xb37308: ldur            w0, [x3, #0xf]
    // 0xb3730c: DecompressPointer r0
    //     0xb3730c: add             x0, x0, HEAP, lsl #32
    // 0xb37310: LoadField: r4 = r0->field_b
    //     0xb37310: ldur            w4, [x0, #0xb]
    // 0xb37314: DecompressPointer r4
    //     0xb37314: add             x4, x4, HEAP, lsl #32
    // 0xb37318: cmp             w4, NULL
    // 0xb3731c: b.eq            #0xb37f4c
    // 0xb37320: LoadField: r0 = r4->field_f
    //     0xb37320: ldur            w0, [x4, #0xf]
    // 0xb37324: DecompressPointer r0
    //     0xb37324: add             x0, x0, HEAP, lsl #32
    // 0xb37328: cmp             w0, NULL
    // 0xb3732c: b.ne            #0xb3733c
    // 0xb37330: ldur            x5, [fp, #-0x10]
    // 0xb37334: r0 = Null
    //     0xb37334: mov             x0, NULL
    // 0xb37338: b               #0xb3738c
    // 0xb3733c: ldur            x5, [fp, #-0x10]
    // 0xb37340: LoadField: r6 = r0->field_f
    //     0xb37340: ldur            w6, [x0, #0xf]
    // 0xb37344: DecompressPointer r6
    //     0xb37344: add             x6, x6, HEAP, lsl #32
    // 0xb37348: LoadField: r0 = r5->field_13
    //     0xb37348: ldur            w0, [x5, #0x13]
    // 0xb3734c: DecompressPointer r0
    //     0xb3734c: add             x0, x0, HEAP, lsl #32
    // 0xb37350: LoadField: r1 = r6->field_b
    //     0xb37350: ldur            w1, [x6, #0xb]
    // 0xb37354: r7 = LoadInt32Instr(r0)
    //     0xb37354: sbfx            x7, x0, #1, #0x1f
    //     0xb37358: tbz             w0, #0, #0xb37360
    //     0xb3735c: ldur            x7, [x0, #7]
    // 0xb37360: r0 = LoadInt32Instr(r1)
    //     0xb37360: sbfx            x0, x1, #1, #0x1f
    // 0xb37364: mov             x1, x7
    // 0xb37368: cmp             x1, x0
    // 0xb3736c: b.hs            #0xb37f50
    // 0xb37370: LoadField: r0 = r6->field_f
    //     0xb37370: ldur            w0, [x6, #0xf]
    // 0xb37374: DecompressPointer r0
    //     0xb37374: add             x0, x0, HEAP, lsl #32
    // 0xb37378: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb37378: add             x16, x0, x7, lsl #2
    //     0xb3737c: ldur            w1, [x16, #0xf]
    // 0xb37380: DecompressPointer r1
    //     0xb37380: add             x1, x1, HEAP, lsl #32
    // 0xb37384: LoadField: r0 = r1->field_27
    //     0xb37384: ldur            w0, [x1, #0x27]
    // 0xb37388: DecompressPointer r0
    //     0xb37388: add             x0, x0, HEAP, lsl #32
    // 0xb3738c: cmp             w0, NULL
    // 0xb37390: b.ne            #0xb37424
    // 0xb37394: LoadField: r0 = r4->field_f
    //     0xb37394: ldur            w0, [x4, #0xf]
    // 0xb37398: DecompressPointer r0
    //     0xb37398: add             x0, x0, HEAP, lsl #32
    // 0xb3739c: cmp             w0, NULL
    // 0xb373a0: b.ne            #0xb373ac
    // 0xb373a4: r0 = Null
    //     0xb373a4: mov             x0, NULL
    // 0xb373a8: b               #0xb373f8
    // 0xb373ac: LoadField: r4 = r0->field_f
    //     0xb373ac: ldur            w4, [x0, #0xf]
    // 0xb373b0: DecompressPointer r4
    //     0xb373b0: add             x4, x4, HEAP, lsl #32
    // 0xb373b4: LoadField: r0 = r5->field_13
    //     0xb373b4: ldur            w0, [x5, #0x13]
    // 0xb373b8: DecompressPointer r0
    //     0xb373b8: add             x0, x0, HEAP, lsl #32
    // 0xb373bc: LoadField: r1 = r4->field_b
    //     0xb373bc: ldur            w1, [x4, #0xb]
    // 0xb373c0: r6 = LoadInt32Instr(r0)
    //     0xb373c0: sbfx            x6, x0, #1, #0x1f
    //     0xb373c4: tbz             w0, #0, #0xb373cc
    //     0xb373c8: ldur            x6, [x0, #7]
    // 0xb373cc: r0 = LoadInt32Instr(r1)
    //     0xb373cc: sbfx            x0, x1, #1, #0x1f
    // 0xb373d0: mov             x1, x6
    // 0xb373d4: cmp             x1, x0
    // 0xb373d8: b.hs            #0xb37f54
    // 0xb373dc: LoadField: r0 = r4->field_f
    //     0xb373dc: ldur            w0, [x4, #0xf]
    // 0xb373e0: DecompressPointer r0
    //     0xb373e0: add             x0, x0, HEAP, lsl #32
    // 0xb373e4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb373e4: add             x16, x0, x6, lsl #2
    //     0xb373e8: ldur            w1, [x16, #0xf]
    // 0xb373ec: DecompressPointer r1
    //     0xb373ec: add             x1, x1, HEAP, lsl #32
    // 0xb373f0: LoadField: r0 = r1->field_f
    //     0xb373f0: ldur            w0, [x1, #0xf]
    // 0xb373f4: DecompressPointer r0
    //     0xb373f4: add             x0, x0, HEAP, lsl #32
    // 0xb373f8: r1 = LoadClassIdInstr(r0)
    //     0xb373f8: ldur            x1, [x0, #-1]
    //     0xb373fc: ubfx            x1, x1, #0xc, #0x14
    // 0xb37400: r16 = "sale_event_static_coupon"
    //     0xb37400: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xb37404: ldr             x16, [x16, #0x610]
    // 0xb37408: stp             x16, x0, [SP]
    // 0xb3740c: mov             x0, x1
    // 0xb37410: mov             lr, x0
    // 0xb37414: ldr             lr, [x21, lr, lsl #3]
    // 0xb37418: blr             lr
    // 0xb3741c: tbnz            w0, #4, #0xb37430
    // 0xb37420: b               #0xb37428
    // 0xb37424: tbnz            w0, #4, #0xb37430
    // 0xb37428: r1 = Instance_Color
    //     0xb37428: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb3742c: b               #0xb37440
    // 0xb37430: r1 = Instance_Color
    //     0xb37430: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb37434: d0 = 0.400000
    //     0xb37434: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb37438: r0 = withOpacity()
    //     0xb37438: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb3743c: mov             x1, x0
    // 0xb37440: ldur            x0, [fp, #-8]
    // 0xb37444: ldur            d0, [fp, #-0x68]
    // 0xb37448: ldur            x2, [fp, #-0x38]
    // 0xb3744c: r16 = 14.000000
    //     0xb3744c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb37450: ldr             x16, [x16, #0x1d8]
    // 0xb37454: stp             x16, x1, [SP]
    // 0xb37458: ldur            x1, [fp, #-0x48]
    // 0xb3745c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb3745c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb37460: ldr             x4, [x4, #0x9b8]
    // 0xb37464: r0 = copyWith()
    //     0xb37464: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb37468: stur            x0, [fp, #-0x48]
    // 0xb3746c: r0 = Text()
    //     0xb3746c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb37470: mov             x1, x0
    // 0xb37474: ldur            x0, [fp, #-0x38]
    // 0xb37478: stur            x1, [fp, #-0x50]
    // 0xb3747c: StoreField: r1->field_b = r0
    //     0xb3747c: stur            w0, [x1, #0xb]
    // 0xb37480: ldur            x0, [fp, #-0x48]
    // 0xb37484: StoreField: r1->field_13 = r0
    //     0xb37484: stur            w0, [x1, #0x13]
    // 0xb37488: r2 = 4
    //     0xb37488: movz            x2, #0x4
    // 0xb3748c: StoreField: r1->field_37 = r2
    //     0xb3748c: stur            w2, [x1, #0x37]
    // 0xb37490: r0 = Padding()
    //     0xb37490: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb37494: mov             x1, x0
    // 0xb37498: r0 = Instance_EdgeInsets
    //     0xb37498: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb3749c: ldr             x0, [x0, #0x980]
    // 0xb374a0: stur            x1, [fp, #-0x48]
    // 0xb374a4: StoreField: r1->field_f = r0
    //     0xb374a4: stur            w0, [x1, #0xf]
    // 0xb374a8: ldur            x2, [fp, #-0x50]
    // 0xb374ac: StoreField: r1->field_b = r2
    //     0xb374ac: stur            w2, [x1, #0xb]
    // 0xb374b0: ldur            d0, [fp, #-0x68]
    // 0xb374b4: r2 = inline_Allocate_Double()
    //     0xb374b4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb374b8: add             x2, x2, #0x10
    //     0xb374bc: cmp             x3, x2
    //     0xb374c0: b.ls            #0xb37f58
    //     0xb374c4: str             x2, [THR, #0x50]  ; THR::top
    //     0xb374c8: sub             x2, x2, #0xf
    //     0xb374cc: movz            x3, #0xe15c
    //     0xb374d0: movk            x3, #0x3, lsl #16
    //     0xb374d4: stur            x3, [x2, #-1]
    // 0xb374d8: StoreField: r2->field_7 = d0
    //     0xb374d8: stur            d0, [x2, #7]
    // 0xb374dc: stur            x2, [fp, #-0x38]
    // 0xb374e0: r0 = SizedBox()
    //     0xb374e0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb374e4: mov             x2, x0
    // 0xb374e8: ldur            x0, [fp, #-0x38]
    // 0xb374ec: stur            x2, [fp, #-0x50]
    // 0xb374f0: StoreField: r2->field_f = r0
    //     0xb374f0: stur            w0, [x2, #0xf]
    // 0xb374f4: ldur            x0, [fp, #-0x48]
    // 0xb374f8: StoreField: r2->field_b = r0
    //     0xb374f8: stur            w0, [x2, #0xb]
    // 0xb374fc: ldur            x3, [fp, #-8]
    // 0xb37500: LoadField: r0 = r3->field_f
    //     0xb37500: ldur            w0, [x3, #0xf]
    // 0xb37504: DecompressPointer r0
    //     0xb37504: add             x0, x0, HEAP, lsl #32
    // 0xb37508: LoadField: r1 = r0->field_b
    //     0xb37508: ldur            w1, [x0, #0xb]
    // 0xb3750c: DecompressPointer r1
    //     0xb3750c: add             x1, x1, HEAP, lsl #32
    // 0xb37510: cmp             w1, NULL
    // 0xb37514: b.eq            #0xb37f74
    // 0xb37518: LoadField: r0 = r1->field_f
    //     0xb37518: ldur            w0, [x1, #0xf]
    // 0xb3751c: DecompressPointer r0
    //     0xb3751c: add             x0, x0, HEAP, lsl #32
    // 0xb37520: cmp             w0, NULL
    // 0xb37524: b.ne            #0xb37534
    // 0xb37528: ldur            x4, [fp, #-0x10]
    // 0xb3752c: r0 = Null
    //     0xb3752c: mov             x0, NULL
    // 0xb37530: b               #0xb37584
    // 0xb37534: ldur            x4, [fp, #-0x10]
    // 0xb37538: LoadField: r5 = r0->field_f
    //     0xb37538: ldur            w5, [x0, #0xf]
    // 0xb3753c: DecompressPointer r5
    //     0xb3753c: add             x5, x5, HEAP, lsl #32
    // 0xb37540: LoadField: r0 = r4->field_13
    //     0xb37540: ldur            w0, [x4, #0x13]
    // 0xb37544: DecompressPointer r0
    //     0xb37544: add             x0, x0, HEAP, lsl #32
    // 0xb37548: LoadField: r1 = r5->field_b
    //     0xb37548: ldur            w1, [x5, #0xb]
    // 0xb3754c: r6 = LoadInt32Instr(r0)
    //     0xb3754c: sbfx            x6, x0, #1, #0x1f
    //     0xb37550: tbz             w0, #0, #0xb37558
    //     0xb37554: ldur            x6, [x0, #7]
    // 0xb37558: r0 = LoadInt32Instr(r1)
    //     0xb37558: sbfx            x0, x1, #1, #0x1f
    // 0xb3755c: mov             x1, x6
    // 0xb37560: cmp             x1, x0
    // 0xb37564: b.hs            #0xb37f78
    // 0xb37568: LoadField: r0 = r5->field_f
    //     0xb37568: ldur            w0, [x5, #0xf]
    // 0xb3756c: DecompressPointer r0
    //     0xb3756c: add             x0, x0, HEAP, lsl #32
    // 0xb37570: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb37570: add             x16, x0, x6, lsl #2
    //     0xb37574: ldur            w1, [x16, #0xf]
    // 0xb37578: DecompressPointer r1
    //     0xb37578: add             x1, x1, HEAP, lsl #32
    // 0xb3757c: LoadField: r0 = r1->field_f
    //     0xb3757c: ldur            w0, [x1, #0xf]
    // 0xb37580: DecompressPointer r0
    //     0xb37580: add             x0, x0, HEAP, lsl #32
    // 0xb37584: r1 = LoadClassIdInstr(r0)
    //     0xb37584: ldur            x1, [x0, #-1]
    //     0xb37588: ubfx            x1, x1, #0xc, #0x14
    // 0xb3758c: r16 = "sale_event_static_coupon"
    //     0xb3758c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xb37590: ldr             x16, [x16, #0x610]
    // 0xb37594: stp             x16, x0, [SP]
    // 0xb37598: mov             x0, x1
    // 0xb3759c: mov             lr, x0
    // 0xb375a0: ldr             lr, [x21, lr, lsl #3]
    // 0xb375a4: blr             lr
    // 0xb375a8: eor             x2, x0, #0x10
    // 0xb375ac: ldur            x0, [fp, #-0x10]
    // 0xb375b0: stur            x2, [fp, #-0x38]
    // 0xb375b4: LoadField: r1 = r0->field_f
    //     0xb375b4: ldur            w1, [x0, #0xf]
    // 0xb375b8: DecompressPointer r1
    //     0xb375b8: add             x1, x1, HEAP, lsl #32
    // 0xb375bc: r0 = of()
    //     0xb375bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb375c0: LoadField: r1 = r0->field_87
    //     0xb375c0: ldur            w1, [x0, #0x87]
    // 0xb375c4: DecompressPointer r1
    //     0xb375c4: add             x1, x1, HEAP, lsl #32
    // 0xb375c8: LoadField: r0 = r1->field_7
    //     0xb375c8: ldur            w0, [x1, #7]
    // 0xb375cc: DecompressPointer r0
    //     0xb375cc: add             x0, x0, HEAP, lsl #32
    // 0xb375d0: r16 = Instance_Color
    //     0xb375d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb375d4: ldr             x16, [x16, #0x858]
    // 0xb375d8: r30 = 14.000000
    //     0xb375d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb375dc: ldr             lr, [lr, #0x1d8]
    // 0xb375e0: stp             lr, x16, [SP]
    // 0xb375e4: mov             x1, x0
    // 0xb375e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb375e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb375ec: ldr             x4, [x4, #0x9b8]
    // 0xb375f0: r0 = copyWith()
    //     0xb375f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb375f4: stur            x0, [fp, #-0x48]
    // 0xb375f8: r0 = TextSpan()
    //     0xb375f8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb375fc: mov             x2, x0
    // 0xb37600: r0 = "Know "
    //     0xb37600: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d0] "Know "
    //     0xb37604: ldr             x0, [x0, #0x5d0]
    // 0xb37608: stur            x2, [fp, #-0x58]
    // 0xb3760c: StoreField: r2->field_b = r0
    //     0xb3760c: stur            w0, [x2, #0xb]
    // 0xb37610: r0 = Instance__DeferringMouseCursor
    //     0xb37610: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb37614: ArrayStore: r2[0] = r0  ; List_4
    //     0xb37614: stur            w0, [x2, #0x17]
    // 0xb37618: ldur            x1, [fp, #-0x48]
    // 0xb3761c: StoreField: r2->field_7 = r1
    //     0xb3761c: stur            w1, [x2, #7]
    // 0xb37620: ldur            x3, [fp, #-0x10]
    // 0xb37624: LoadField: r1 = r3->field_f
    //     0xb37624: ldur            w1, [x3, #0xf]
    // 0xb37628: DecompressPointer r1
    //     0xb37628: add             x1, x1, HEAP, lsl #32
    // 0xb3762c: r0 = of()
    //     0xb3762c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb37630: LoadField: r1 = r0->field_87
    //     0xb37630: ldur            w1, [x0, #0x87]
    // 0xb37634: DecompressPointer r1
    //     0xb37634: add             x1, x1, HEAP, lsl #32
    // 0xb37638: LoadField: r0 = r1->field_7
    //     0xb37638: ldur            w0, [x1, #7]
    // 0xb3763c: DecompressPointer r0
    //     0xb3763c: add             x0, x0, HEAP, lsl #32
    // 0xb37640: r16 = Instance_Color
    //     0xb37640: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb37644: ldr             x16, [x16, #0x858]
    // 0xb37648: r30 = 14.000000
    //     0xb37648: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3764c: ldr             lr, [lr, #0x1d8]
    // 0xb37650: stp             lr, x16, [SP]
    // 0xb37654: mov             x1, x0
    // 0xb37658: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb37658: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3765c: ldr             x4, [x4, #0x9b8]
    // 0xb37660: r0 = copyWith()
    //     0xb37660: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb37664: stur            x0, [fp, #-0x48]
    // 0xb37668: r0 = TextSpan()
    //     0xb37668: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb3766c: mov             x3, x0
    // 0xb37670: r0 = "More"
    //     0xb37670: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5d8] "More"
    //     0xb37674: ldr             x0, [x0, #0x5d8]
    // 0xb37678: stur            x3, [fp, #-0x60]
    // 0xb3767c: StoreField: r3->field_b = r0
    //     0xb3767c: stur            w0, [x3, #0xb]
    // 0xb37680: r0 = Instance__DeferringMouseCursor
    //     0xb37680: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb37684: ArrayStore: r3[0] = r0  ; List_4
    //     0xb37684: stur            w0, [x3, #0x17]
    // 0xb37688: ldur            x1, [fp, #-0x48]
    // 0xb3768c: StoreField: r3->field_7 = r1
    //     0xb3768c: stur            w1, [x3, #7]
    // 0xb37690: r1 = Null
    //     0xb37690: mov             x1, NULL
    // 0xb37694: r2 = 4
    //     0xb37694: movz            x2, #0x4
    // 0xb37698: r0 = AllocateArray()
    //     0xb37698: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3769c: mov             x2, x0
    // 0xb376a0: ldur            x0, [fp, #-0x58]
    // 0xb376a4: stur            x2, [fp, #-0x48]
    // 0xb376a8: StoreField: r2->field_f = r0
    //     0xb376a8: stur            w0, [x2, #0xf]
    // 0xb376ac: ldur            x0, [fp, #-0x60]
    // 0xb376b0: StoreField: r2->field_13 = r0
    //     0xb376b0: stur            w0, [x2, #0x13]
    // 0xb376b4: r1 = <TextSpan>
    //     0xb376b4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0xb376b8: ldr             x1, [x1, #0x940]
    // 0xb376bc: r0 = AllocateGrowableArray()
    //     0xb376bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb376c0: mov             x1, x0
    // 0xb376c4: ldur            x0, [fp, #-0x48]
    // 0xb376c8: stur            x1, [fp, #-0x58]
    // 0xb376cc: StoreField: r1->field_f = r0
    //     0xb376cc: stur            w0, [x1, #0xf]
    // 0xb376d0: r2 = 4
    //     0xb376d0: movz            x2, #0x4
    // 0xb376d4: StoreField: r1->field_b = r2
    //     0xb376d4: stur            w2, [x1, #0xb]
    // 0xb376d8: r0 = TextSpan()
    //     0xb376d8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb376dc: mov             x1, x0
    // 0xb376e0: ldur            x0, [fp, #-0x58]
    // 0xb376e4: stur            x1, [fp, #-0x48]
    // 0xb376e8: StoreField: r1->field_f = r0
    //     0xb376e8: stur            w0, [x1, #0xf]
    // 0xb376ec: r0 = Instance__DeferringMouseCursor
    //     0xb376ec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb376f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb376f0: stur            w0, [x1, #0x17]
    // 0xb376f4: r0 = RichText()
    //     0xb376f4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb376f8: mov             x1, x0
    // 0xb376fc: ldur            x2, [fp, #-0x48]
    // 0xb37700: stur            x0, [fp, #-0x48]
    // 0xb37704: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb37704: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb37708: r0 = RichText()
    //     0xb37708: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb3770c: r0 = Padding()
    //     0xb3770c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb37710: mov             x1, x0
    // 0xb37714: r0 = Instance_EdgeInsets
    //     0xb37714: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb37718: ldr             x0, [x0, #0xc40]
    // 0xb3771c: stur            x1, [fp, #-0x58]
    // 0xb37720: StoreField: r1->field_f = r0
    //     0xb37720: stur            w0, [x1, #0xf]
    // 0xb37724: ldur            x0, [fp, #-0x48]
    // 0xb37728: StoreField: r1->field_b = r0
    //     0xb37728: stur            w0, [x1, #0xb]
    // 0xb3772c: r0 = Visibility()
    //     0xb3772c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb37730: mov             x1, x0
    // 0xb37734: ldur            x0, [fp, #-0x58]
    // 0xb37738: stur            x1, [fp, #-0x48]
    // 0xb3773c: StoreField: r1->field_b = r0
    //     0xb3773c: stur            w0, [x1, #0xb]
    // 0xb37740: r0 = Instance_SizedBox
    //     0xb37740: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb37744: StoreField: r1->field_f = r0
    //     0xb37744: stur            w0, [x1, #0xf]
    // 0xb37748: ldur            x2, [fp, #-0x38]
    // 0xb3774c: StoreField: r1->field_13 = r2
    //     0xb3774c: stur            w2, [x1, #0x13]
    // 0xb37750: r2 = false
    //     0xb37750: add             x2, NULL, #0x30  ; false
    // 0xb37754: ArrayStore: r1[0] = r2  ; List_4
    //     0xb37754: stur            w2, [x1, #0x17]
    // 0xb37758: StoreField: r1->field_1b = r2
    //     0xb37758: stur            w2, [x1, #0x1b]
    // 0xb3775c: StoreField: r1->field_1f = r2
    //     0xb3775c: stur            w2, [x1, #0x1f]
    // 0xb37760: StoreField: r1->field_23 = r2
    //     0xb37760: stur            w2, [x1, #0x23]
    // 0xb37764: StoreField: r1->field_27 = r2
    //     0xb37764: stur            w2, [x1, #0x27]
    // 0xb37768: StoreField: r1->field_2b = r2
    //     0xb37768: stur            w2, [x1, #0x2b]
    // 0xb3776c: r0 = InkWell()
    //     0xb3776c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb37770: mov             x3, x0
    // 0xb37774: ldur            x0, [fp, #-0x48]
    // 0xb37778: stur            x3, [fp, #-0x38]
    // 0xb3777c: StoreField: r3->field_b = r0
    //     0xb3777c: stur            w0, [x3, #0xb]
    // 0xb37780: ldur            x2, [fp, #-0x10]
    // 0xb37784: r1 = Function '<anonymous closure>':.
    //     0xb37784: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aaf0] AnonymousClosure: (0xb382f8), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb37788: ldr             x1, [x1, #0xaf0]
    // 0xb3778c: r0 = AllocateClosure()
    //     0xb3778c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb37790: mov             x1, x0
    // 0xb37794: ldur            x0, [fp, #-0x38]
    // 0xb37798: StoreField: r0->field_f = r1
    //     0xb37798: stur            w1, [x0, #0xf]
    // 0xb3779c: r2 = true
    //     0xb3779c: add             x2, NULL, #0x20  ; true
    // 0xb377a0: StoreField: r0->field_43 = r2
    //     0xb377a0: stur            w2, [x0, #0x43]
    // 0xb377a4: r3 = Instance_BoxShape
    //     0xb377a4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb377a8: ldr             x3, [x3, #0x80]
    // 0xb377ac: StoreField: r0->field_47 = r3
    //     0xb377ac: stur            w3, [x0, #0x47]
    // 0xb377b0: StoreField: r0->field_6f = r2
    //     0xb377b0: stur            w2, [x0, #0x6f]
    // 0xb377b4: r4 = false
    //     0xb377b4: add             x4, NULL, #0x30  ; false
    // 0xb377b8: StoreField: r0->field_73 = r4
    //     0xb377b8: stur            w4, [x0, #0x73]
    // 0xb377bc: StoreField: r0->field_83 = r2
    //     0xb377bc: stur            w2, [x0, #0x83]
    // 0xb377c0: StoreField: r0->field_7b = r4
    //     0xb377c0: stur            w4, [x0, #0x7b]
    // 0xb377c4: r1 = <FlexParentData>
    //     0xb377c4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb377c8: ldr             x1, [x1, #0xe00]
    // 0xb377cc: r0 = Expanded()
    //     0xb377cc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb377d0: stur            x0, [fp, #-0x48]
    // 0xb377d4: StoreField: r0->field_13 = rZR
    //     0xb377d4: stur            xzr, [x0, #0x13]
    // 0xb377d8: r3 = Instance_FlexFit
    //     0xb377d8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb377dc: ldr             x3, [x3, #0xe08]
    // 0xb377e0: StoreField: r0->field_1b = r3
    //     0xb377e0: stur            w3, [x0, #0x1b]
    // 0xb377e4: ldur            x1, [fp, #-0x38]
    // 0xb377e8: StoreField: r0->field_b = r1
    //     0xb377e8: stur            w1, [x0, #0xb]
    // 0xb377ec: r1 = Null
    //     0xb377ec: mov             x1, NULL
    // 0xb377f0: r2 = 4
    //     0xb377f0: movz            x2, #0x4
    // 0xb377f4: r0 = AllocateArray()
    //     0xb377f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb377f8: mov             x2, x0
    // 0xb377fc: ldur            x0, [fp, #-0x50]
    // 0xb37800: stur            x2, [fp, #-0x38]
    // 0xb37804: StoreField: r2->field_f = r0
    //     0xb37804: stur            w0, [x2, #0xf]
    // 0xb37808: ldur            x0, [fp, #-0x48]
    // 0xb3780c: StoreField: r2->field_13 = r0
    //     0xb3780c: stur            w0, [x2, #0x13]
    // 0xb37810: r1 = <Widget>
    //     0xb37810: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb37814: r0 = AllocateGrowableArray()
    //     0xb37814: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb37818: mov             x1, x0
    // 0xb3781c: ldur            x0, [fp, #-0x38]
    // 0xb37820: stur            x1, [fp, #-0x48]
    // 0xb37824: StoreField: r1->field_f = r0
    //     0xb37824: stur            w0, [x1, #0xf]
    // 0xb37828: r0 = 4
    //     0xb37828: movz            x0, #0x4
    // 0xb3782c: StoreField: r1->field_b = r0
    //     0xb3782c: stur            w0, [x1, #0xb]
    // 0xb37830: r0 = Column()
    //     0xb37830: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb37834: mov             x1, x0
    // 0xb37838: r0 = Instance_Axis
    //     0xb37838: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3783c: stur            x1, [fp, #-0x38]
    // 0xb37840: StoreField: r1->field_f = r0
    //     0xb37840: stur            w0, [x1, #0xf]
    // 0xb37844: r2 = Instance_MainAxisAlignment
    //     0xb37844: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb37848: ldr             x2, [x2, #0xa08]
    // 0xb3784c: StoreField: r1->field_13 = r2
    //     0xb3784c: stur            w2, [x1, #0x13]
    // 0xb37850: r0 = Instance_MainAxisSize
    //     0xb37850: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb37854: ldr             x0, [x0, #0xdd0]
    // 0xb37858: ArrayStore: r1[0] = r0  ; List_4
    //     0xb37858: stur            w0, [x1, #0x17]
    // 0xb3785c: r0 = Instance_CrossAxisAlignment
    //     0xb3785c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb37860: ldr             x0, [x0, #0x890]
    // 0xb37864: StoreField: r1->field_1b = r0
    //     0xb37864: stur            w0, [x1, #0x1b]
    // 0xb37868: r3 = Instance_VerticalDirection
    //     0xb37868: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb3786c: ldr             x3, [x3, #0xa20]
    // 0xb37870: StoreField: r1->field_23 = r3
    //     0xb37870: stur            w3, [x1, #0x23]
    // 0xb37874: r4 = Instance_Clip
    //     0xb37874: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb37878: ldr             x4, [x4, #0x38]
    // 0xb3787c: StoreField: r1->field_2b = r4
    //     0xb3787c: stur            w4, [x1, #0x2b]
    // 0xb37880: StoreField: r1->field_2f = rZR
    //     0xb37880: stur            xzr, [x1, #0x2f]
    // 0xb37884: ldur            x0, [fp, #-0x48]
    // 0xb37888: StoreField: r1->field_b = r0
    //     0xb37888: stur            w0, [x1, #0xb]
    // 0xb3788c: ldur            x5, [fp, #-8]
    // 0xb37890: LoadField: r0 = r5->field_f
    //     0xb37890: ldur            w0, [x5, #0xf]
    // 0xb37894: DecompressPointer r0
    //     0xb37894: add             x0, x0, HEAP, lsl #32
    // 0xb37898: LoadField: r6 = r0->field_b
    //     0xb37898: ldur            w6, [x0, #0xb]
    // 0xb3789c: DecompressPointer r6
    //     0xb3789c: add             x6, x6, HEAP, lsl #32
    // 0xb378a0: cmp             w6, NULL
    // 0xb378a4: b.eq            #0xb37f7c
    // 0xb378a8: LoadField: r0 = r6->field_b
    //     0xb378a8: ldur            w0, [x6, #0xb]
    // 0xb378ac: DecompressPointer r0
    //     0xb378ac: add             x0, x0, HEAP, lsl #32
    // 0xb378b0: r6 = LoadClassIdInstr(r0)
    //     0xb378b0: ldur            x6, [x0, #-1]
    //     0xb378b4: ubfx            x6, x6, #0xc, #0x14
    // 0xb378b8: r16 = "checkout_offers"
    //     0xb378b8: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xb378bc: ldr             x16, [x16, #0x1c8]
    // 0xb378c0: stp             x16, x0, [SP]
    // 0xb378c4: mov             x0, x6
    // 0xb378c8: mov             lr, x0
    // 0xb378cc: ldr             lr, [x21, lr, lsl #3]
    // 0xb378d0: blr             lr
    // 0xb378d4: tbz             w0, #4, #0xb379a4
    // 0xb378d8: ldur            x2, [fp, #-8]
    // 0xb378dc: LoadField: r0 = r2->field_f
    //     0xb378dc: ldur            w0, [x2, #0xf]
    // 0xb378e0: DecompressPointer r0
    //     0xb378e0: add             x0, x0, HEAP, lsl #32
    // 0xb378e4: LoadField: r1 = r0->field_b
    //     0xb378e4: ldur            w1, [x0, #0xb]
    // 0xb378e8: DecompressPointer r1
    //     0xb378e8: add             x1, x1, HEAP, lsl #32
    // 0xb378ec: cmp             w1, NULL
    // 0xb378f0: b.eq            #0xb37f80
    // 0xb378f4: LoadField: r0 = r1->field_1f
    //     0xb378f4: ldur            w0, [x1, #0x1f]
    // 0xb378f8: DecompressPointer r0
    //     0xb378f8: add             x0, x0, HEAP, lsl #32
    // 0xb378fc: tbnz            w0, #4, #0xb37908
    // 0xb37900: r3 = true
    //     0xb37900: add             x3, NULL, #0x20  ; true
    // 0xb37904: b               #0xb379ac
    // 0xb37908: LoadField: r0 = r1->field_f
    //     0xb37908: ldur            w0, [x1, #0xf]
    // 0xb3790c: DecompressPointer r0
    //     0xb3790c: add             x0, x0, HEAP, lsl #32
    // 0xb37910: cmp             w0, NULL
    // 0xb37914: b.ne            #0xb37924
    // 0xb37918: ldur            x3, [fp, #-0x10]
    // 0xb3791c: r0 = Null
    //     0xb3791c: mov             x0, NULL
    // 0xb37920: b               #0xb37974
    // 0xb37924: ldur            x3, [fp, #-0x10]
    // 0xb37928: LoadField: r4 = r0->field_f
    //     0xb37928: ldur            w4, [x0, #0xf]
    // 0xb3792c: DecompressPointer r4
    //     0xb3792c: add             x4, x4, HEAP, lsl #32
    // 0xb37930: LoadField: r0 = r3->field_13
    //     0xb37930: ldur            w0, [x3, #0x13]
    // 0xb37934: DecompressPointer r0
    //     0xb37934: add             x0, x0, HEAP, lsl #32
    // 0xb37938: LoadField: r1 = r4->field_b
    //     0xb37938: ldur            w1, [x4, #0xb]
    // 0xb3793c: r5 = LoadInt32Instr(r0)
    //     0xb3793c: sbfx            x5, x0, #1, #0x1f
    //     0xb37940: tbz             w0, #0, #0xb37948
    //     0xb37944: ldur            x5, [x0, #7]
    // 0xb37948: r0 = LoadInt32Instr(r1)
    //     0xb37948: sbfx            x0, x1, #1, #0x1f
    // 0xb3794c: mov             x1, x5
    // 0xb37950: cmp             x1, x0
    // 0xb37954: b.hs            #0xb37f84
    // 0xb37958: LoadField: r0 = r4->field_f
    //     0xb37958: ldur            w0, [x4, #0xf]
    // 0xb3795c: DecompressPointer r0
    //     0xb3795c: add             x0, x0, HEAP, lsl #32
    // 0xb37960: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb37960: add             x16, x0, x5, lsl #2
    //     0xb37964: ldur            w1, [x16, #0xf]
    // 0xb37968: DecompressPointer r1
    //     0xb37968: add             x1, x1, HEAP, lsl #32
    // 0xb3796c: LoadField: r0 = r1->field_f
    //     0xb3796c: ldur            w0, [x1, #0xf]
    // 0xb37970: DecompressPointer r0
    //     0xb37970: add             x0, x0, HEAP, lsl #32
    // 0xb37974: r1 = LoadClassIdInstr(r0)
    //     0xb37974: ldur            x1, [x0, #-1]
    //     0xb37978: ubfx            x1, x1, #0xc, #0x14
    // 0xb3797c: r16 = "sale_event_static_coupon"
    //     0xb3797c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xb37980: ldr             x16, [x16, #0x610]
    // 0xb37984: stp             x16, x0, [SP]
    // 0xb37988: mov             x0, x1
    // 0xb3798c: mov             lr, x0
    // 0xb37990: ldr             lr, [x21, lr, lsl #3]
    // 0xb37994: blr             lr
    // 0xb37998: mov             x3, x0
    // 0xb3799c: ldur            x2, [fp, #-8]
    // 0xb379a0: b               #0xb379ac
    // 0xb379a4: ldur            x2, [fp, #-8]
    // 0xb379a8: r3 = false
    //     0xb379a8: add             x3, NULL, #0x30  ; false
    // 0xb379ac: stur            x3, [fp, #-0x48]
    // 0xb379b0: LoadField: r0 = r2->field_f
    //     0xb379b0: ldur            w0, [x2, #0xf]
    // 0xb379b4: DecompressPointer r0
    //     0xb379b4: add             x0, x0, HEAP, lsl #32
    // 0xb379b8: LoadField: r4 = r0->field_b
    //     0xb379b8: ldur            w4, [x0, #0xb]
    // 0xb379bc: DecompressPointer r4
    //     0xb379bc: add             x4, x4, HEAP, lsl #32
    // 0xb379c0: cmp             w4, NULL
    // 0xb379c4: b.eq            #0xb37f88
    // 0xb379c8: LoadField: r0 = r4->field_f
    //     0xb379c8: ldur            w0, [x4, #0xf]
    // 0xb379cc: DecompressPointer r0
    //     0xb379cc: add             x0, x0, HEAP, lsl #32
    // 0xb379d0: cmp             w0, NULL
    // 0xb379d4: b.ne            #0xb379e4
    // 0xb379d8: ldur            x5, [fp, #-0x10]
    // 0xb379dc: r0 = Null
    //     0xb379dc: mov             x0, NULL
    // 0xb379e0: b               #0xb37a34
    // 0xb379e4: ldur            x5, [fp, #-0x10]
    // 0xb379e8: LoadField: r6 = r0->field_f
    //     0xb379e8: ldur            w6, [x0, #0xf]
    // 0xb379ec: DecompressPointer r6
    //     0xb379ec: add             x6, x6, HEAP, lsl #32
    // 0xb379f0: LoadField: r0 = r5->field_13
    //     0xb379f0: ldur            w0, [x5, #0x13]
    // 0xb379f4: DecompressPointer r0
    //     0xb379f4: add             x0, x0, HEAP, lsl #32
    // 0xb379f8: LoadField: r1 = r6->field_b
    //     0xb379f8: ldur            w1, [x6, #0xb]
    // 0xb379fc: r7 = LoadInt32Instr(r0)
    //     0xb379fc: sbfx            x7, x0, #1, #0x1f
    //     0xb37a00: tbz             w0, #0, #0xb37a08
    //     0xb37a04: ldur            x7, [x0, #7]
    // 0xb37a08: r0 = LoadInt32Instr(r1)
    //     0xb37a08: sbfx            x0, x1, #1, #0x1f
    // 0xb37a0c: mov             x1, x7
    // 0xb37a10: cmp             x1, x0
    // 0xb37a14: b.hs            #0xb37f8c
    // 0xb37a18: LoadField: r0 = r6->field_f
    //     0xb37a18: ldur            w0, [x6, #0xf]
    // 0xb37a1c: DecompressPointer r0
    //     0xb37a1c: add             x0, x0, HEAP, lsl #32
    // 0xb37a20: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb37a20: add             x16, x0, x7, lsl #2
    //     0xb37a24: ldur            w1, [x16, #0xf]
    // 0xb37a28: DecompressPointer r1
    //     0xb37a28: add             x1, x1, HEAP, lsl #32
    // 0xb37a2c: LoadField: r0 = r1->field_7
    //     0xb37a2c: ldur            w0, [x1, #7]
    // 0xb37a30: DecompressPointer r0
    //     0xb37a30: add             x0, x0, HEAP, lsl #32
    // 0xb37a34: LoadField: r1 = r4->field_13
    //     0xb37a34: ldur            w1, [x4, #0x13]
    // 0xb37a38: DecompressPointer r1
    //     0xb37a38: add             x1, x1, HEAP, lsl #32
    // 0xb37a3c: r4 = LoadClassIdInstr(r0)
    //     0xb37a3c: ldur            x4, [x0, #-1]
    //     0xb37a40: ubfx            x4, x4, #0xc, #0x14
    // 0xb37a44: stp             x1, x0, [SP]
    // 0xb37a48: mov             x0, x4
    // 0xb37a4c: mov             lr, x0
    // 0xb37a50: ldr             lr, [x21, lr, lsl #3]
    // 0xb37a54: blr             lr
    // 0xb37a58: tbz             w0, #4, #0xb37b0c
    // 0xb37a5c: ldur            x2, [fp, #-8]
    // 0xb37a60: LoadField: r0 = r2->field_f
    //     0xb37a60: ldur            w0, [x2, #0xf]
    // 0xb37a64: DecompressPointer r0
    //     0xb37a64: add             x0, x0, HEAP, lsl #32
    // 0xb37a68: LoadField: r1 = r0->field_b
    //     0xb37a68: ldur            w1, [x0, #0xb]
    // 0xb37a6c: DecompressPointer r1
    //     0xb37a6c: add             x1, x1, HEAP, lsl #32
    // 0xb37a70: cmp             w1, NULL
    // 0xb37a74: b.eq            #0xb37f90
    // 0xb37a78: LoadField: r0 = r1->field_f
    //     0xb37a78: ldur            w0, [x1, #0xf]
    // 0xb37a7c: DecompressPointer r0
    //     0xb37a7c: add             x0, x0, HEAP, lsl #32
    // 0xb37a80: cmp             w0, NULL
    // 0xb37a84: b.ne            #0xb37a94
    // 0xb37a88: ldur            x3, [fp, #-0x10]
    // 0xb37a8c: r0 = Null
    //     0xb37a8c: mov             x0, NULL
    // 0xb37a90: b               #0xb37ae4
    // 0xb37a94: ldur            x3, [fp, #-0x10]
    // 0xb37a98: LoadField: r4 = r0->field_f
    //     0xb37a98: ldur            w4, [x0, #0xf]
    // 0xb37a9c: DecompressPointer r4
    //     0xb37a9c: add             x4, x4, HEAP, lsl #32
    // 0xb37aa0: LoadField: r0 = r3->field_13
    //     0xb37aa0: ldur            w0, [x3, #0x13]
    // 0xb37aa4: DecompressPointer r0
    //     0xb37aa4: add             x0, x0, HEAP, lsl #32
    // 0xb37aa8: LoadField: r1 = r4->field_b
    //     0xb37aa8: ldur            w1, [x4, #0xb]
    // 0xb37aac: r5 = LoadInt32Instr(r0)
    //     0xb37aac: sbfx            x5, x0, #1, #0x1f
    //     0xb37ab0: tbz             w0, #0, #0xb37ab8
    //     0xb37ab4: ldur            x5, [x0, #7]
    // 0xb37ab8: r0 = LoadInt32Instr(r1)
    //     0xb37ab8: sbfx            x0, x1, #1, #0x1f
    // 0xb37abc: mov             x1, x5
    // 0xb37ac0: cmp             x1, x0
    // 0xb37ac4: b.hs            #0xb37f94
    // 0xb37ac8: LoadField: r0 = r4->field_f
    //     0xb37ac8: ldur            w0, [x4, #0xf]
    // 0xb37acc: DecompressPointer r0
    //     0xb37acc: add             x0, x0, HEAP, lsl #32
    // 0xb37ad0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb37ad0: add             x16, x0, x5, lsl #2
    //     0xb37ad4: ldur            w1, [x16, #0xf]
    // 0xb37ad8: DecompressPointer r1
    //     0xb37ad8: add             x1, x1, HEAP, lsl #32
    // 0xb37adc: LoadField: r0 = r1->field_f
    //     0xb37adc: ldur            w0, [x1, #0xf]
    // 0xb37ae0: DecompressPointer r0
    //     0xb37ae0: add             x0, x0, HEAP, lsl #32
    // 0xb37ae4: r1 = LoadClassIdInstr(r0)
    //     0xb37ae4: ldur            x1, [x0, #-1]
    //     0xb37ae8: ubfx            x1, x1, #0xc, #0x14
    // 0xb37aec: r16 = "sale_event_static_coupon"
    //     0xb37aec: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a610] "sale_event_static_coupon"
    //     0xb37af0: ldr             x16, [x16, #0x610]
    // 0xb37af4: stp             x16, x0, [SP]
    // 0xb37af8: mov             x0, x1
    // 0xb37afc: mov             lr, x0
    // 0xb37b00: ldr             lr, [x21, lr, lsl #3]
    // 0xb37b04: blr             lr
    // 0xb37b08: tbnz            w0, #4, #0xb37b78
    // 0xb37b0c: ldur            x2, [fp, #-0x10]
    // 0xb37b10: LoadField: r1 = r2->field_f
    //     0xb37b10: ldur            w1, [x2, #0xf]
    // 0xb37b14: DecompressPointer r1
    //     0xb37b14: add             x1, x1, HEAP, lsl #32
    // 0xb37b18: r0 = of()
    //     0xb37b18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb37b1c: LoadField: r1 = r0->field_87
    //     0xb37b1c: ldur            w1, [x0, #0x87]
    // 0xb37b20: DecompressPointer r1
    //     0xb37b20: add             x1, x1, HEAP, lsl #32
    // 0xb37b24: LoadField: r0 = r1->field_7
    //     0xb37b24: ldur            w0, [x1, #7]
    // 0xb37b28: DecompressPointer r0
    //     0xb37b28: add             x0, x0, HEAP, lsl #32
    // 0xb37b2c: r16 = Instance_Color
    //     0xb37b2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb37b30: ldr             x16, [x16, #0x858]
    // 0xb37b34: r30 = 12.000000
    //     0xb37b34: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb37b38: ldr             lr, [lr, #0x9e8]
    // 0xb37b3c: stp             lr, x16, [SP]
    // 0xb37b40: mov             x1, x0
    // 0xb37b44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb37b44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb37b48: ldr             x4, [x4, #0x9b8]
    // 0xb37b4c: r0 = copyWith()
    //     0xb37b4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb37b50: stur            x0, [fp, #-0x50]
    // 0xb37b54: r0 = Text()
    //     0xb37b54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb37b58: mov             x1, x0
    // 0xb37b5c: r0 = "APPLIED"
    //     0xb37b5c: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5e8] "APPLIED"
    //     0xb37b60: ldr             x0, [x0, #0x5e8]
    // 0xb37b64: StoreField: r1->field_b = r0
    //     0xb37b64: stur            w0, [x1, #0xb]
    // 0xb37b68: ldur            x0, [fp, #-0x50]
    // 0xb37b6c: StoreField: r1->field_13 = r0
    //     0xb37b6c: stur            w0, [x1, #0x13]
    // 0xb37b70: mov             x3, x1
    // 0xb37b74: b               #0xb37c88
    // 0xb37b78: ldur            x0, [fp, #-8]
    // 0xb37b7c: ldur            x2, [fp, #-0x10]
    // 0xb37b80: LoadField: r1 = r2->field_f
    //     0xb37b80: ldur            w1, [x2, #0xf]
    // 0xb37b84: DecompressPointer r1
    //     0xb37b84: add             x1, x1, HEAP, lsl #32
    // 0xb37b88: r0 = of()
    //     0xb37b88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb37b8c: LoadField: r1 = r0->field_87
    //     0xb37b8c: ldur            w1, [x0, #0x87]
    // 0xb37b90: DecompressPointer r1
    //     0xb37b90: add             x1, x1, HEAP, lsl #32
    // 0xb37b94: LoadField: r2 = r1->field_7
    //     0xb37b94: ldur            w2, [x1, #7]
    // 0xb37b98: DecompressPointer r2
    //     0xb37b98: add             x2, x2, HEAP, lsl #32
    // 0xb37b9c: ldur            x0, [fp, #-8]
    // 0xb37ba0: stur            x2, [fp, #-0x50]
    // 0xb37ba4: LoadField: r1 = r0->field_f
    //     0xb37ba4: ldur            w1, [x0, #0xf]
    // 0xb37ba8: DecompressPointer r1
    //     0xb37ba8: add             x1, x1, HEAP, lsl #32
    // 0xb37bac: LoadField: r0 = r1->field_b
    //     0xb37bac: ldur            w0, [x1, #0xb]
    // 0xb37bb0: DecompressPointer r0
    //     0xb37bb0: add             x0, x0, HEAP, lsl #32
    // 0xb37bb4: cmp             w0, NULL
    // 0xb37bb8: b.eq            #0xb37f98
    // 0xb37bbc: LoadField: r1 = r0->field_f
    //     0xb37bbc: ldur            w1, [x0, #0xf]
    // 0xb37bc0: DecompressPointer r1
    //     0xb37bc0: add             x1, x1, HEAP, lsl #32
    // 0xb37bc4: cmp             w1, NULL
    // 0xb37bc8: b.ne            #0xb37bd8
    // 0xb37bcc: ldur            x3, [fp, #-0x10]
    // 0xb37bd0: r0 = Null
    //     0xb37bd0: mov             x0, NULL
    // 0xb37bd4: b               #0xb37c28
    // 0xb37bd8: ldur            x3, [fp, #-0x10]
    // 0xb37bdc: LoadField: r4 = r1->field_f
    //     0xb37bdc: ldur            w4, [x1, #0xf]
    // 0xb37be0: DecompressPointer r4
    //     0xb37be0: add             x4, x4, HEAP, lsl #32
    // 0xb37be4: LoadField: r0 = r3->field_13
    //     0xb37be4: ldur            w0, [x3, #0x13]
    // 0xb37be8: DecompressPointer r0
    //     0xb37be8: add             x0, x0, HEAP, lsl #32
    // 0xb37bec: LoadField: r1 = r4->field_b
    //     0xb37bec: ldur            w1, [x4, #0xb]
    // 0xb37bf0: r5 = LoadInt32Instr(r0)
    //     0xb37bf0: sbfx            x5, x0, #1, #0x1f
    //     0xb37bf4: tbz             w0, #0, #0xb37bfc
    //     0xb37bf8: ldur            x5, [x0, #7]
    // 0xb37bfc: r0 = LoadInt32Instr(r1)
    //     0xb37bfc: sbfx            x0, x1, #1, #0x1f
    // 0xb37c00: mov             x1, x5
    // 0xb37c04: cmp             x1, x0
    // 0xb37c08: b.hs            #0xb37f9c
    // 0xb37c0c: LoadField: r0 = r4->field_f
    //     0xb37c0c: ldur            w0, [x4, #0xf]
    // 0xb37c10: DecompressPointer r0
    //     0xb37c10: add             x0, x0, HEAP, lsl #32
    // 0xb37c14: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb37c14: add             x16, x0, x5, lsl #2
    //     0xb37c18: ldur            w1, [x16, #0xf]
    // 0xb37c1c: DecompressPointer r1
    //     0xb37c1c: add             x1, x1, HEAP, lsl #32
    // 0xb37c20: LoadField: r0 = r1->field_27
    //     0xb37c20: ldur            w0, [x1, #0x27]
    // 0xb37c24: DecompressPointer r0
    //     0xb37c24: add             x0, x0, HEAP, lsl #32
    // 0xb37c28: cmp             w0, NULL
    // 0xb37c2c: b.eq            #0xb37c3c
    // 0xb37c30: tbnz            w0, #4, #0xb37c3c
    // 0xb37c34: r0 = Instance_Color
    //     0xb37c34: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb37c38: b               #0xb37c48
    // 0xb37c3c: r1 = Instance_Color
    //     0xb37c3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb37c40: d0 = 0.400000
    //     0xb37c40: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb37c44: r0 = withOpacity()
    //     0xb37c44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb37c48: r16 = 14.000000
    //     0xb37c48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb37c4c: ldr             x16, [x16, #0x1d8]
    // 0xb37c50: stp             x16, x0, [SP]
    // 0xb37c54: ldur            x1, [fp, #-0x50]
    // 0xb37c58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb37c58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb37c5c: ldr             x4, [x4, #0x9b8]
    // 0xb37c60: r0 = copyWith()
    //     0xb37c60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb37c64: stur            x0, [fp, #-8]
    // 0xb37c68: r0 = Text()
    //     0xb37c68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb37c6c: mov             x1, x0
    // 0xb37c70: r0 = "APPLY"
    //     0xb37c70: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a5f0] "APPLY"
    //     0xb37c74: ldr             x0, [x0, #0x5f0]
    // 0xb37c78: StoreField: r1->field_b = r0
    //     0xb37c78: stur            w0, [x1, #0xb]
    // 0xb37c7c: ldur            x0, [fp, #-8]
    // 0xb37c80: StoreField: r1->field_13 = r0
    //     0xb37c80: stur            w0, [x1, #0x13]
    // 0xb37c84: mov             x3, x1
    // 0xb37c88: ldur            x2, [fp, #-0x40]
    // 0xb37c8c: ldur            x1, [fp, #-0x38]
    // 0xb37c90: ldur            x0, [fp, #-0x48]
    // 0xb37c94: stur            x3, [fp, #-8]
    // 0xb37c98: r0 = InkWell()
    //     0xb37c98: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb37c9c: mov             x3, x0
    // 0xb37ca0: ldur            x0, [fp, #-8]
    // 0xb37ca4: stur            x3, [fp, #-0x50]
    // 0xb37ca8: StoreField: r3->field_b = r0
    //     0xb37ca8: stur            w0, [x3, #0xb]
    // 0xb37cac: ldur            x2, [fp, #-0x10]
    // 0xb37cb0: r1 = Function '<anonymous closure>':.
    //     0xb37cb0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aaf8] AnonymousClosure: (0xb37fa0), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb37cb4: ldr             x1, [x1, #0xaf8]
    // 0xb37cb8: r0 = AllocateClosure()
    //     0xb37cb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb37cbc: mov             x1, x0
    // 0xb37cc0: ldur            x0, [fp, #-0x50]
    // 0xb37cc4: StoreField: r0->field_f = r1
    //     0xb37cc4: stur            w1, [x0, #0xf]
    // 0xb37cc8: r1 = true
    //     0xb37cc8: add             x1, NULL, #0x20  ; true
    // 0xb37ccc: StoreField: r0->field_43 = r1
    //     0xb37ccc: stur            w1, [x0, #0x43]
    // 0xb37cd0: r2 = Instance_BoxShape
    //     0xb37cd0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb37cd4: ldr             x2, [x2, #0x80]
    // 0xb37cd8: StoreField: r0->field_47 = r2
    //     0xb37cd8: stur            w2, [x0, #0x47]
    // 0xb37cdc: StoreField: r0->field_6f = r1
    //     0xb37cdc: stur            w1, [x0, #0x6f]
    // 0xb37ce0: r2 = false
    //     0xb37ce0: add             x2, NULL, #0x30  ; false
    // 0xb37ce4: StoreField: r0->field_73 = r2
    //     0xb37ce4: stur            w2, [x0, #0x73]
    // 0xb37ce8: StoreField: r0->field_83 = r1
    //     0xb37ce8: stur            w1, [x0, #0x83]
    // 0xb37cec: StoreField: r0->field_7b = r2
    //     0xb37cec: stur            w2, [x0, #0x7b]
    // 0xb37cf0: r1 = <FlexParentData>
    //     0xb37cf0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb37cf4: ldr             x1, [x1, #0xe00]
    // 0xb37cf8: r0 = Expanded()
    //     0xb37cf8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb37cfc: mov             x1, x0
    // 0xb37d00: r0 = 1
    //     0xb37d00: movz            x0, #0x1
    // 0xb37d04: stur            x1, [fp, #-8]
    // 0xb37d08: StoreField: r1->field_13 = r0
    //     0xb37d08: stur            x0, [x1, #0x13]
    // 0xb37d0c: r0 = Instance_FlexFit
    //     0xb37d0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb37d10: ldr             x0, [x0, #0xe08]
    // 0xb37d14: StoreField: r1->field_1b = r0
    //     0xb37d14: stur            w0, [x1, #0x1b]
    // 0xb37d18: ldur            x0, [fp, #-0x50]
    // 0xb37d1c: StoreField: r1->field_b = r0
    //     0xb37d1c: stur            w0, [x1, #0xb]
    // 0xb37d20: r0 = Visibility()
    //     0xb37d20: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb37d24: mov             x3, x0
    // 0xb37d28: ldur            x0, [fp, #-8]
    // 0xb37d2c: stur            x3, [fp, #-0x10]
    // 0xb37d30: StoreField: r3->field_b = r0
    //     0xb37d30: stur            w0, [x3, #0xb]
    // 0xb37d34: r0 = Instance_SizedBox
    //     0xb37d34: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb37d38: StoreField: r3->field_f = r0
    //     0xb37d38: stur            w0, [x3, #0xf]
    // 0xb37d3c: ldur            x0, [fp, #-0x48]
    // 0xb37d40: StoreField: r3->field_13 = r0
    //     0xb37d40: stur            w0, [x3, #0x13]
    // 0xb37d44: r0 = false
    //     0xb37d44: add             x0, NULL, #0x30  ; false
    // 0xb37d48: ArrayStore: r3[0] = r0  ; List_4
    //     0xb37d48: stur            w0, [x3, #0x17]
    // 0xb37d4c: StoreField: r3->field_1b = r0
    //     0xb37d4c: stur            w0, [x3, #0x1b]
    // 0xb37d50: StoreField: r3->field_1f = r0
    //     0xb37d50: stur            w0, [x3, #0x1f]
    // 0xb37d54: StoreField: r3->field_23 = r0
    //     0xb37d54: stur            w0, [x3, #0x23]
    // 0xb37d58: StoreField: r3->field_27 = r0
    //     0xb37d58: stur            w0, [x3, #0x27]
    // 0xb37d5c: StoreField: r3->field_2b = r0
    //     0xb37d5c: stur            w0, [x3, #0x2b]
    // 0xb37d60: r1 = Null
    //     0xb37d60: mov             x1, NULL
    // 0xb37d64: r2 = 8
    //     0xb37d64: movz            x2, #0x8
    // 0xb37d68: r0 = AllocateArray()
    //     0xb37d68: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb37d6c: mov             x2, x0
    // 0xb37d70: ldur            x0, [fp, #-0x40]
    // 0xb37d74: stur            x2, [fp, #-8]
    // 0xb37d78: StoreField: r2->field_f = r0
    //     0xb37d78: stur            w0, [x2, #0xf]
    // 0xb37d7c: ldur            x0, [fp, #-0x38]
    // 0xb37d80: StoreField: r2->field_13 = r0
    //     0xb37d80: stur            w0, [x2, #0x13]
    // 0xb37d84: r16 = Instance_Spacer
    //     0xb37d84: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb37d88: ldr             x16, [x16, #0xf0]
    // 0xb37d8c: ArrayStore: r2[0] = r16  ; List_4
    //     0xb37d8c: stur            w16, [x2, #0x17]
    // 0xb37d90: ldur            x0, [fp, #-0x10]
    // 0xb37d94: StoreField: r2->field_1b = r0
    //     0xb37d94: stur            w0, [x2, #0x1b]
    // 0xb37d98: r1 = <Widget>
    //     0xb37d98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb37d9c: r0 = AllocateGrowableArray()
    //     0xb37d9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb37da0: mov             x1, x0
    // 0xb37da4: ldur            x0, [fp, #-8]
    // 0xb37da8: stur            x1, [fp, #-0x10]
    // 0xb37dac: StoreField: r1->field_f = r0
    //     0xb37dac: stur            w0, [x1, #0xf]
    // 0xb37db0: r0 = 8
    //     0xb37db0: movz            x0, #0x8
    // 0xb37db4: StoreField: r1->field_b = r0
    //     0xb37db4: stur            w0, [x1, #0xb]
    // 0xb37db8: r0 = Row()
    //     0xb37db8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb37dbc: mov             x1, x0
    // 0xb37dc0: r0 = Instance_Axis
    //     0xb37dc0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb37dc4: stur            x1, [fp, #-8]
    // 0xb37dc8: StoreField: r1->field_f = r0
    //     0xb37dc8: stur            w0, [x1, #0xf]
    // 0xb37dcc: r0 = Instance_MainAxisAlignment
    //     0xb37dcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb37dd0: ldr             x0, [x0, #0xa08]
    // 0xb37dd4: StoreField: r1->field_13 = r0
    //     0xb37dd4: stur            w0, [x1, #0x13]
    // 0xb37dd8: r0 = Instance_MainAxisSize
    //     0xb37dd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb37ddc: ldr             x0, [x0, #0xa10]
    // 0xb37de0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb37de0: stur            w0, [x1, #0x17]
    // 0xb37de4: r0 = Instance_CrossAxisAlignment
    //     0xb37de4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb37de8: ldr             x0, [x0, #0xa18]
    // 0xb37dec: StoreField: r1->field_1b = r0
    //     0xb37dec: stur            w0, [x1, #0x1b]
    // 0xb37df0: r0 = Instance_VerticalDirection
    //     0xb37df0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb37df4: ldr             x0, [x0, #0xa20]
    // 0xb37df8: StoreField: r1->field_23 = r0
    //     0xb37df8: stur            w0, [x1, #0x23]
    // 0xb37dfc: r0 = Instance_Clip
    //     0xb37dfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb37e00: ldr             x0, [x0, #0x38]
    // 0xb37e04: StoreField: r1->field_2b = r0
    //     0xb37e04: stur            w0, [x1, #0x2b]
    // 0xb37e08: StoreField: r1->field_2f = rZR
    //     0xb37e08: stur            xzr, [x1, #0x2f]
    // 0xb37e0c: ldur            x0, [fp, #-0x10]
    // 0xb37e10: StoreField: r1->field_b = r0
    //     0xb37e10: stur            w0, [x1, #0xb]
    // 0xb37e14: r0 = Padding()
    //     0xb37e14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb37e18: mov             x1, x0
    // 0xb37e1c: r0 = Instance_EdgeInsets
    //     0xb37e1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb37e20: ldr             x0, [x0, #0x980]
    // 0xb37e24: stur            x1, [fp, #-0x10]
    // 0xb37e28: StoreField: r1->field_f = r0
    //     0xb37e28: stur            w0, [x1, #0xf]
    // 0xb37e2c: ldur            x2, [fp, #-8]
    // 0xb37e30: StoreField: r1->field_b = r2
    //     0xb37e30: stur            w2, [x1, #0xb]
    // 0xb37e34: r0 = ClipRRect()
    //     0xb37e34: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb37e38: mov             x1, x0
    // 0xb37e3c: r0 = Instance_BorderRadius
    //     0xb37e3c: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb37e40: ldr             x0, [x0, #0x460]
    // 0xb37e44: stur            x1, [fp, #-8]
    // 0xb37e48: StoreField: r1->field_f = r0
    //     0xb37e48: stur            w0, [x1, #0xf]
    // 0xb37e4c: r0 = Instance_Clip
    //     0xb37e4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb37e50: ldr             x0, [x0, #0x138]
    // 0xb37e54: ArrayStore: r1[0] = r0  ; List_4
    //     0xb37e54: stur            w0, [x1, #0x17]
    // 0xb37e58: ldur            x0, [fp, #-0x10]
    // 0xb37e5c: StoreField: r1->field_b = r0
    //     0xb37e5c: stur            w0, [x1, #0xb]
    // 0xb37e60: r0 = DottedBorder()
    //     0xb37e60: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0xb37e64: stur            x0, [fp, #-0x10]
    // 0xb37e68: r16 = Instance_BorderType
    //     0xb37e68: add             x16, PP, #0x40, lsl #12  ; [pp+0x40078] Obj!BorderType@d750a1
    //     0xb37e6c: ldr             x16, [x16, #0x78]
    // 0xb37e70: r30 = Instance_Radius
    //     0xb37e70: add             lr, PP, #0x40, lsl #12  ; [pp+0x40758] Obj!Radius@d6be41
    //     0xb37e74: ldr             lr, [lr, #0x758]
    // 0xb37e78: stp             lr, x16, [SP]
    // 0xb37e7c: mov             x1, x0
    // 0xb37e80: ldur            x2, [fp, #-8]
    // 0xb37e84: ldur            x3, [fp, #-0x30]
    // 0xb37e88: r5 = const [5.0, 5.0]
    //     0xb37e88: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0xb37e8c: ldr             x5, [x5, #0xab0]
    // 0xb37e90: d0 = 1.000000
    //     0xb37e90: fmov            d0, #1.00000000
    // 0xb37e94: r4 = const [0, 0x7, 0x2, 0x5, borderType, 0x5, radius, 0x6, null]
    //     0xb37e94: add             x4, PP, #0x40, lsl #12  ; [pp+0x40088] List(9) [0, 0x7, 0x2, 0x5, "borderType", 0x5, "radius", 0x6, Null]
    //     0xb37e98: ldr             x4, [x4, #0x88]
    // 0xb37e9c: r0 = DottedBorder()
    //     0xb37e9c: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0xb37ea0: ldur            x0, [fp, #-0x10]
    // 0xb37ea4: stur            x0, [fp, #-8]
    // 0xb37ea8: r0 = Padding()
    //     0xb37ea8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb37eac: r1 = Instance_EdgeInsets
    //     0xb37eac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb37eb0: ldr             x1, [x1, #0x980]
    // 0xb37eb4: StoreField: r0->field_f = r1
    //     0xb37eb4: stur            w1, [x0, #0xf]
    // 0xb37eb8: ldur            x1, [fp, #-8]
    // 0xb37ebc: StoreField: r0->field_b = r1
    //     0xb37ebc: stur            w1, [x0, #0xb]
    // 0xb37ec0: LeaveFrame
    //     0xb37ec0: mov             SP, fp
    //     0xb37ec4: ldp             fp, lr, [SP], #0x10
    // 0xb37ec8: ret
    //     0xb37ec8: ret             
    // 0xb37ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb37ecc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb37ed0: b               #0xb35ea4
    // 0xb37ed4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37ed4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37ed8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37ed8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37edc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37edc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37ee0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37ee0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37ee4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb37ee4: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb37ee8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37ee8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37eec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37eec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37ef0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37ef0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37ef4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37ef4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37ef8: SaveReg d0
    //     0xb37ef8: str             q0, [SP, #-0x10]!
    // 0xb37efc: stp             x0, x1, [SP, #-0x10]!
    // 0xb37f00: r0 = AllocateDouble()
    //     0xb37f00: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb37f04: mov             x2, x0
    // 0xb37f08: ldp             x0, x1, [SP], #0x10
    // 0xb37f0c: RestoreReg d0
    //     0xb37f0c: ldr             q0, [SP], #0x10
    // 0xb37f10: b               #0xb36668
    // 0xb37f14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f18: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f34: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f34: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f3c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f40: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f44: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb37f44: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb37f48: r0 = RangeErrorSharedWithFPURegs()
    //     0xb37f48: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xb37f4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f58: SaveReg d0
    //     0xb37f58: str             q0, [SP, #-0x10]!
    // 0xb37f5c: stp             x0, x1, [SP, #-0x10]!
    // 0xb37f60: r0 = AllocateDouble()
    //     0xb37f60: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb37f64: mov             x2, x0
    // 0xb37f68: ldp             x0, x1, [SP], #0x10
    // 0xb37f6c: RestoreReg d0
    //     0xb37f6c: ldr             q0, [SP], #0x10
    // 0xb37f70: b               #0xb374d8
    // 0xb37f74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f78: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f84: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f8c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f94: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb37f98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb37f98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb37f9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb37f9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb37fa0, size: 0x358
    // 0xb37fa0: EnterFrame
    //     0xb37fa0: stp             fp, lr, [SP, #-0x10]!
    //     0xb37fa4: mov             fp, SP
    // 0xb37fa8: AllocStack(0x28)
    //     0xb37fa8: sub             SP, SP, #0x28
    // 0xb37fac: SetupParameters()
    //     0xb37fac: ldr             x0, [fp, #0x10]
    //     0xb37fb0: ldur            w2, [x0, #0x17]
    //     0xb37fb4: add             x2, x2, HEAP, lsl #32
    //     0xb37fb8: stur            x2, [fp, #-8]
    // 0xb37fbc: CheckStackOverflow
    //     0xb37fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb37fc0: cmp             SP, x16
    //     0xb37fc4: b.ls            #0xb382d8
    // 0xb37fc8: LoadField: r0 = r2->field_b
    //     0xb37fc8: ldur            w0, [x2, #0xb]
    // 0xb37fcc: DecompressPointer r0
    //     0xb37fcc: add             x0, x0, HEAP, lsl #32
    // 0xb37fd0: LoadField: r1 = r0->field_f
    //     0xb37fd0: ldur            w1, [x0, #0xf]
    // 0xb37fd4: DecompressPointer r1
    //     0xb37fd4: add             x1, x1, HEAP, lsl #32
    // 0xb37fd8: LoadField: r3 = r1->field_b
    //     0xb37fd8: ldur            w3, [x1, #0xb]
    // 0xb37fdc: DecompressPointer r3
    //     0xb37fdc: add             x3, x3, HEAP, lsl #32
    // 0xb37fe0: cmp             w3, NULL
    // 0xb37fe4: b.eq            #0xb382e0
    // 0xb37fe8: LoadField: r0 = r3->field_f
    //     0xb37fe8: ldur            w0, [x3, #0xf]
    // 0xb37fec: DecompressPointer r0
    //     0xb37fec: add             x0, x0, HEAP, lsl #32
    // 0xb37ff0: cmp             w0, NULL
    // 0xb37ff4: b.ne            #0xb38000
    // 0xb37ff8: r0 = Null
    //     0xb37ff8: mov             x0, NULL
    // 0xb37ffc: b               #0xb3804c
    // 0xb38000: LoadField: r4 = r0->field_f
    //     0xb38000: ldur            w4, [x0, #0xf]
    // 0xb38004: DecompressPointer r4
    //     0xb38004: add             x4, x4, HEAP, lsl #32
    // 0xb38008: LoadField: r0 = r2->field_13
    //     0xb38008: ldur            w0, [x2, #0x13]
    // 0xb3800c: DecompressPointer r0
    //     0xb3800c: add             x0, x0, HEAP, lsl #32
    // 0xb38010: LoadField: r1 = r4->field_b
    //     0xb38010: ldur            w1, [x4, #0xb]
    // 0xb38014: r5 = LoadInt32Instr(r0)
    //     0xb38014: sbfx            x5, x0, #1, #0x1f
    //     0xb38018: tbz             w0, #0, #0xb38020
    //     0xb3801c: ldur            x5, [x0, #7]
    // 0xb38020: r0 = LoadInt32Instr(r1)
    //     0xb38020: sbfx            x0, x1, #1, #0x1f
    // 0xb38024: mov             x1, x5
    // 0xb38028: cmp             x1, x0
    // 0xb3802c: b.hs            #0xb382e4
    // 0xb38030: LoadField: r0 = r4->field_f
    //     0xb38030: ldur            w0, [x4, #0xf]
    // 0xb38034: DecompressPointer r0
    //     0xb38034: add             x0, x0, HEAP, lsl #32
    // 0xb38038: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb38038: add             x16, x0, x5, lsl #2
    //     0xb3803c: ldur            w1, [x16, #0xf]
    // 0xb38040: DecompressPointer r1
    //     0xb38040: add             x1, x1, HEAP, lsl #32
    // 0xb38044: LoadField: r0 = r1->field_27
    //     0xb38044: ldur            w0, [x1, #0x27]
    // 0xb38048: DecompressPointer r0
    //     0xb38048: add             x0, x0, HEAP, lsl #32
    // 0xb3804c: cmp             w0, NULL
    // 0xb38050: b.eq            #0xb382c8
    // 0xb38054: tbnz            w0, #4, #0xb382c8
    // 0xb38058: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb38058: ldur            w0, [x3, #0x17]
    // 0xb3805c: DecompressPointer r0
    //     0xb3805c: add             x0, x0, HEAP, lsl #32
    // 0xb38060: cmp             w0, NULL
    // 0xb38064: b.eq            #0xb38180
    // 0xb38068: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb38068: ldur            w4, [x3, #0x17]
    // 0xb3806c: DecompressPointer r4
    //     0xb3806c: add             x4, x4, HEAP, lsl #32
    // 0xb38070: cmp             w4, NULL
    // 0xb38074: b.eq            #0xb382ac
    // 0xb38078: LoadField: r0 = r3->field_f
    //     0xb38078: ldur            w0, [x3, #0xf]
    // 0xb3807c: DecompressPointer r0
    //     0xb3807c: add             x0, x0, HEAP, lsl #32
    // 0xb38080: cmp             w0, NULL
    // 0xb38084: b.ne            #0xb38090
    // 0xb38088: r0 = Null
    //     0xb38088: mov             x0, NULL
    // 0xb3808c: b               #0xb380dc
    // 0xb38090: LoadField: r5 = r0->field_f
    //     0xb38090: ldur            w5, [x0, #0xf]
    // 0xb38094: DecompressPointer r5
    //     0xb38094: add             x5, x5, HEAP, lsl #32
    // 0xb38098: LoadField: r0 = r2->field_13
    //     0xb38098: ldur            w0, [x2, #0x13]
    // 0xb3809c: DecompressPointer r0
    //     0xb3809c: add             x0, x0, HEAP, lsl #32
    // 0xb380a0: LoadField: r1 = r5->field_b
    //     0xb380a0: ldur            w1, [x5, #0xb]
    // 0xb380a4: r6 = LoadInt32Instr(r0)
    //     0xb380a4: sbfx            x6, x0, #1, #0x1f
    //     0xb380a8: tbz             w0, #0, #0xb380b0
    //     0xb380ac: ldur            x6, [x0, #7]
    // 0xb380b0: r0 = LoadInt32Instr(r1)
    //     0xb380b0: sbfx            x0, x1, #1, #0x1f
    // 0xb380b4: mov             x1, x6
    // 0xb380b8: cmp             x1, x0
    // 0xb380bc: b.hs            #0xb382e8
    // 0xb380c0: LoadField: r0 = r5->field_f
    //     0xb380c0: ldur            w0, [x5, #0xf]
    // 0xb380c4: DecompressPointer r0
    //     0xb380c4: add             x0, x0, HEAP, lsl #32
    // 0xb380c8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb380c8: add             x16, x0, x6, lsl #2
    //     0xb380cc: ldur            w1, [x16, #0xf]
    // 0xb380d0: DecompressPointer r1
    //     0xb380d0: add             x1, x1, HEAP, lsl #32
    // 0xb380d4: LoadField: r0 = r1->field_7
    //     0xb380d4: ldur            w0, [x1, #7]
    // 0xb380d8: DecompressPointer r0
    //     0xb380d8: add             x0, x0, HEAP, lsl #32
    // 0xb380dc: cmp             w0, NULL
    // 0xb380e0: b.ne            #0xb380ec
    // 0xb380e4: r5 = ""
    //     0xb380e4: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb380e8: b               #0xb380f0
    // 0xb380ec: mov             x5, x0
    // 0xb380f0: LoadField: r0 = r3->field_f
    //     0xb380f0: ldur            w0, [x3, #0xf]
    // 0xb380f4: DecompressPointer r0
    //     0xb380f4: add             x0, x0, HEAP, lsl #32
    // 0xb380f8: cmp             w0, NULL
    // 0xb380fc: b.ne            #0xb38108
    // 0xb38100: r0 = Null
    //     0xb38100: mov             x0, NULL
    // 0xb38104: b               #0xb38154
    // 0xb38108: LoadField: r3 = r0->field_f
    //     0xb38108: ldur            w3, [x0, #0xf]
    // 0xb3810c: DecompressPointer r3
    //     0xb3810c: add             x3, x3, HEAP, lsl #32
    // 0xb38110: LoadField: r0 = r2->field_13
    //     0xb38110: ldur            w0, [x2, #0x13]
    // 0xb38114: DecompressPointer r0
    //     0xb38114: add             x0, x0, HEAP, lsl #32
    // 0xb38118: LoadField: r1 = r3->field_b
    //     0xb38118: ldur            w1, [x3, #0xb]
    // 0xb3811c: r6 = LoadInt32Instr(r0)
    //     0xb3811c: sbfx            x6, x0, #1, #0x1f
    //     0xb38120: tbz             w0, #0, #0xb38128
    //     0xb38124: ldur            x6, [x0, #7]
    // 0xb38128: r0 = LoadInt32Instr(r1)
    //     0xb38128: sbfx            x0, x1, #1, #0x1f
    // 0xb3812c: mov             x1, x6
    // 0xb38130: cmp             x1, x0
    // 0xb38134: b.hs            #0xb382ec
    // 0xb38138: LoadField: r0 = r3->field_f
    //     0xb38138: ldur            w0, [x3, #0xf]
    // 0xb3813c: DecompressPointer r0
    //     0xb3813c: add             x0, x0, HEAP, lsl #32
    // 0xb38140: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb38140: add             x16, x0, x6, lsl #2
    //     0xb38144: ldur            w1, [x16, #0xf]
    // 0xb38148: DecompressPointer r1
    //     0xb38148: add             x1, x1, HEAP, lsl #32
    // 0xb3814c: LoadField: r0 = r1->field_b
    //     0xb3814c: ldur            w0, [x1, #0xb]
    // 0xb38150: DecompressPointer r0
    //     0xb38150: add             x0, x0, HEAP, lsl #32
    // 0xb38154: stp             x5, x4, [SP, #0x10]
    // 0xb38158: r16 = "apply"
    //     0xb38158: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xb3815c: ldr             x16, [x16, #0xfe8]
    // 0xb38160: stp             x16, x0, [SP]
    // 0xb38164: r4 = 0
    //     0xb38164: movz            x4, #0
    // 0xb38168: ldr             x0, [SP, #0x18]
    // 0xb3816c: r16 = UnlinkedCall_0x613b5c
    //     0xb3816c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ab00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb38170: add             x16, x16, #0xb00
    // 0xb38174: ldp             x5, lr, [x16]
    // 0xb38178: blr             lr
    // 0xb3817c: b               #0xb382ac
    // 0xb38180: LoadField: r0 = r3->field_1b
    //     0xb38180: ldur            w0, [x3, #0x1b]
    // 0xb38184: DecompressPointer r0
    //     0xb38184: add             x0, x0, HEAP, lsl #32
    // 0xb38188: cmp             w0, NULL
    // 0xb3818c: b.eq            #0xb382ac
    // 0xb38190: LoadField: r2 = r3->field_1b
    //     0xb38190: ldur            w2, [x3, #0x1b]
    // 0xb38194: DecompressPointer r2
    //     0xb38194: add             x2, x2, HEAP, lsl #32
    // 0xb38198: cmp             w2, NULL
    // 0xb3819c: b.eq            #0xb382ac
    // 0xb381a0: LoadField: r0 = r3->field_f
    //     0xb381a0: ldur            w0, [x3, #0xf]
    // 0xb381a4: DecompressPointer r0
    //     0xb381a4: add             x0, x0, HEAP, lsl #32
    // 0xb381a8: cmp             w0, NULL
    // 0xb381ac: b.ne            #0xb381bc
    // 0xb381b0: ldur            x4, [fp, #-8]
    // 0xb381b4: r0 = Null
    //     0xb381b4: mov             x0, NULL
    // 0xb381b8: b               #0xb3820c
    // 0xb381bc: ldur            x4, [fp, #-8]
    // 0xb381c0: LoadField: r5 = r0->field_f
    //     0xb381c0: ldur            w5, [x0, #0xf]
    // 0xb381c4: DecompressPointer r5
    //     0xb381c4: add             x5, x5, HEAP, lsl #32
    // 0xb381c8: LoadField: r0 = r4->field_13
    //     0xb381c8: ldur            w0, [x4, #0x13]
    // 0xb381cc: DecompressPointer r0
    //     0xb381cc: add             x0, x0, HEAP, lsl #32
    // 0xb381d0: LoadField: r1 = r5->field_b
    //     0xb381d0: ldur            w1, [x5, #0xb]
    // 0xb381d4: r6 = LoadInt32Instr(r0)
    //     0xb381d4: sbfx            x6, x0, #1, #0x1f
    //     0xb381d8: tbz             w0, #0, #0xb381e0
    //     0xb381dc: ldur            x6, [x0, #7]
    // 0xb381e0: r0 = LoadInt32Instr(r1)
    //     0xb381e0: sbfx            x0, x1, #1, #0x1f
    // 0xb381e4: mov             x1, x6
    // 0xb381e8: cmp             x1, x0
    // 0xb381ec: b.hs            #0xb382f0
    // 0xb381f0: LoadField: r0 = r5->field_f
    //     0xb381f0: ldur            w0, [x5, #0xf]
    // 0xb381f4: DecompressPointer r0
    //     0xb381f4: add             x0, x0, HEAP, lsl #32
    // 0xb381f8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb381f8: add             x16, x0, x6, lsl #2
    //     0xb381fc: ldur            w1, [x16, #0xf]
    // 0xb38200: DecompressPointer r1
    //     0xb38200: add             x1, x1, HEAP, lsl #32
    // 0xb38204: LoadField: r0 = r1->field_7
    //     0xb38204: ldur            w0, [x1, #7]
    // 0xb38208: DecompressPointer r0
    //     0xb38208: add             x0, x0, HEAP, lsl #32
    // 0xb3820c: cmp             w0, NULL
    // 0xb38210: b.ne            #0xb3821c
    // 0xb38214: r5 = ""
    //     0xb38214: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb38218: b               #0xb38220
    // 0xb3821c: mov             x5, x0
    // 0xb38220: LoadField: r0 = r3->field_f
    //     0xb38220: ldur            w0, [x3, #0xf]
    // 0xb38224: DecompressPointer r0
    //     0xb38224: add             x0, x0, HEAP, lsl #32
    // 0xb38228: cmp             w0, NULL
    // 0xb3822c: b.ne            #0xb38238
    // 0xb38230: r0 = Null
    //     0xb38230: mov             x0, NULL
    // 0xb38234: b               #0xb38284
    // 0xb38238: LoadField: r3 = r0->field_f
    //     0xb38238: ldur            w3, [x0, #0xf]
    // 0xb3823c: DecompressPointer r3
    //     0xb3823c: add             x3, x3, HEAP, lsl #32
    // 0xb38240: LoadField: r0 = r4->field_13
    //     0xb38240: ldur            w0, [x4, #0x13]
    // 0xb38244: DecompressPointer r0
    //     0xb38244: add             x0, x0, HEAP, lsl #32
    // 0xb38248: LoadField: r1 = r3->field_b
    //     0xb38248: ldur            w1, [x3, #0xb]
    // 0xb3824c: r6 = LoadInt32Instr(r0)
    //     0xb3824c: sbfx            x6, x0, #1, #0x1f
    //     0xb38250: tbz             w0, #0, #0xb38258
    //     0xb38254: ldur            x6, [x0, #7]
    // 0xb38258: r0 = LoadInt32Instr(r1)
    //     0xb38258: sbfx            x0, x1, #1, #0x1f
    // 0xb3825c: mov             x1, x6
    // 0xb38260: cmp             x1, x0
    // 0xb38264: b.hs            #0xb382f4
    // 0xb38268: LoadField: r0 = r3->field_f
    //     0xb38268: ldur            w0, [x3, #0xf]
    // 0xb3826c: DecompressPointer r0
    //     0xb3826c: add             x0, x0, HEAP, lsl #32
    // 0xb38270: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb38270: add             x16, x0, x6, lsl #2
    //     0xb38274: ldur            w1, [x16, #0xf]
    // 0xb38278: DecompressPointer r1
    //     0xb38278: add             x1, x1, HEAP, lsl #32
    // 0xb3827c: LoadField: r0 = r1->field_b
    //     0xb3827c: ldur            w0, [x1, #0xb]
    // 0xb38280: DecompressPointer r0
    //     0xb38280: add             x0, x0, HEAP, lsl #32
    // 0xb38284: stp             x5, x2, [SP, #0x10]
    // 0xb38288: r16 = "remove"
    //     0xb38288: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xb3828c: ldr             x16, [x16, #0xff0]
    // 0xb38290: stp             x16, x0, [SP]
    // 0xb38294: r4 = 0
    //     0xb38294: movz            x4, #0
    // 0xb38298: ldr             x0, [SP, #0x18]
    // 0xb3829c: r16 = UnlinkedCall_0x613b5c
    //     0xb3829c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ab10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb382a0: add             x16, x16, #0xb10
    // 0xb382a4: ldp             x5, lr, [x16]
    // 0xb382a8: blr             lr
    // 0xb382ac: ldur            x0, [fp, #-8]
    // 0xb382b0: LoadField: r1 = r0->field_f
    //     0xb382b0: ldur            w1, [x0, #0xf]
    // 0xb382b4: DecompressPointer r1
    //     0xb382b4: add             x1, x1, HEAP, lsl #32
    // 0xb382b8: r16 = <Object?>
    //     0xb382b8: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xb382bc: stp             x1, x16, [SP]
    // 0xb382c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb382c0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb382c4: r0 = pop()
    //     0xb382c4: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb382c8: r0 = Null
    //     0xb382c8: mov             x0, NULL
    // 0xb382cc: LeaveFrame
    //     0xb382cc: mov             SP, fp
    //     0xb382d0: ldp             fp, lr, [SP], #0x10
    // 0xb382d4: ret
    //     0xb382d4: ret             
    // 0xb382d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb382d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb382dc: b               #0xb37fc8
    // 0xb382e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb382e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb382e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb382e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb382e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb382e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb382ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb382ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb382f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb382f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb382f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb382f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb382f8, size: 0x9c
    // 0xb382f8: EnterFrame
    //     0xb382f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb382fc: mov             fp, SP
    // 0xb38300: AllocStack(0x38)
    //     0xb38300: sub             SP, SP, #0x38
    // 0xb38304: SetupParameters()
    //     0xb38304: ldr             x0, [fp, #0x10]
    //     0xb38308: ldur            w2, [x0, #0x17]
    //     0xb3830c: add             x2, x2, HEAP, lsl #32
    //     0xb38310: stur            x2, [fp, #-8]
    // 0xb38314: CheckStackOverflow
    //     0xb38314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb38318: cmp             SP, x16
    //     0xb3831c: b.ls            #0xb3838c
    // 0xb38320: LoadField: r0 = r2->field_f
    //     0xb38320: ldur            w0, [x2, #0xf]
    // 0xb38324: DecompressPointer r0
    //     0xb38324: add             x0, x0, HEAP, lsl #32
    // 0xb38328: r16 = <Object?>
    //     0xb38328: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xb3832c: stp             x0, x16, [SP]
    // 0xb38330: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb38330: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb38334: r0 = pop()
    //     0xb38334: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb38338: ldur            x2, [fp, #-8]
    // 0xb3833c: LoadField: r0 = r2->field_f
    //     0xb3833c: ldur            w0, [x2, #0xf]
    // 0xb38340: DecompressPointer r0
    //     0xb38340: add             x0, x0, HEAP, lsl #32
    // 0xb38344: stur            x0, [fp, #-0x10]
    // 0xb38348: r1 = Function '<anonymous closure>':.
    //     0xb38348: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ab20] AnonymousClosure: (0xb38394), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb3834c: ldr             x1, [x1, #0xb20]
    // 0xb38350: r0 = AllocateClosure()
    //     0xb38350: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb38354: stp             x0, NULL, [SP, #0x18]
    // 0xb38358: ldur            x16, [fp, #-0x10]
    // 0xb3835c: r30 = true
    //     0xb3835c: add             lr, NULL, #0x20  ; true
    // 0xb38360: stp             lr, x16, [SP, #8]
    // 0xb38364: r16 = Instance_RoundedRectangleBorder
    //     0xb38364: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb38368: ldr             x16, [x16, #0xc78]
    // 0xb3836c: str             x16, [SP]
    // 0xb38370: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb38370: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb38374: ldr             x4, [x4, #0xb20]
    // 0xb38378: r0 = showModalBottomSheet()
    //     0xb38378: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb3837c: r0 = Null
    //     0xb3837c: mov             x0, NULL
    // 0xb38380: LeaveFrame
    //     0xb38380: mov             SP, fp
    //     0xb38384: ldp             fp, lr, [SP], #0x10
    // 0xb38388: ret
    //     0xb38388: ret             
    // 0xb3838c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb3838c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb38390: b               #0xb38320
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xb38394, size: 0x24c
    // 0xb38394: EnterFrame
    //     0xb38394: stp             fp, lr, [SP, #-0x10]!
    //     0xb38398: mov             fp, SP
    // 0xb3839c: AllocStack(0x60)
    //     0xb3839c: sub             SP, SP, #0x60
    // 0xb383a0: SetupParameters()
    //     0xb383a0: ldr             x0, [fp, #0x18]
    //     0xb383a4: ldur            w1, [x0, #0x17]
    //     0xb383a8: add             x1, x1, HEAP, lsl #32
    //     0xb383ac: stur            x1, [fp, #-8]
    // 0xb383b0: CheckStackOverflow
    //     0xb383b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb383b4: cmp             SP, x16
    //     0xb383b8: b.ls            #0xb385cc
    // 0xb383bc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb383bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb383c0: ldr             x0, [x0, #0x1c80]
    //     0xb383c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb383c8: cmp             w0, w16
    //     0xb383cc: b.ne            #0xb383d8
    //     0xb383d0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb383d4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb383d8: r0 = GetNavigation.size()
    //     0xb383d8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb383dc: LoadField: d0 = r0->field_f
    //     0xb383dc: ldur            d0, [x0, #0xf]
    // 0xb383e0: d1 = 0.650000
    //     0xb383e0: add             x17, PP, #0x45, lsl #12  ; [pp+0x45448] IMM: double(0.65) from 0x3fe4cccccccccccd
    //     0xb383e4: ldr             d1, [x17, #0x448]
    // 0xb383e8: fmul            d2, d0, d1
    // 0xb383ec: ldur            x1, [fp, #-8]
    // 0xb383f0: stur            d2, [fp, #-0x48]
    // 0xb383f4: LoadField: r2 = r1->field_b
    //     0xb383f4: ldur            w2, [x1, #0xb]
    // 0xb383f8: DecompressPointer r2
    //     0xb383f8: add             x2, x2, HEAP, lsl #32
    // 0xb383fc: stur            x2, [fp, #-0x10]
    // 0xb38400: LoadField: r0 = r2->field_f
    //     0xb38400: ldur            w0, [x2, #0xf]
    // 0xb38404: DecompressPointer r0
    //     0xb38404: add             x0, x0, HEAP, lsl #32
    // 0xb38408: LoadField: r3 = r0->field_b
    //     0xb38408: ldur            w3, [x0, #0xb]
    // 0xb3840c: DecompressPointer r3
    //     0xb3840c: add             x3, x3, HEAP, lsl #32
    // 0xb38410: cmp             w3, NULL
    // 0xb38414: b.eq            #0xb385d4
    // 0xb38418: LoadField: r0 = r3->field_b
    //     0xb38418: ldur            w0, [x3, #0xb]
    // 0xb3841c: DecompressPointer r0
    //     0xb3841c: add             x0, x0, HEAP, lsl #32
    // 0xb38420: r3 = LoadClassIdInstr(r0)
    //     0xb38420: ldur            x3, [x0, #-1]
    //     0xb38424: ubfx            x3, x3, #0xc, #0x14
    // 0xb38428: r16 = "checkout_offers"
    //     0xb38428: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xb3842c: ldr             x16, [x16, #0x1c8]
    // 0xb38430: stp             x16, x0, [SP]
    // 0xb38434: mov             x0, x3
    // 0xb38438: mov             lr, x0
    // 0xb3843c: ldr             lr, [x21, lr, lsl #3]
    // 0xb38440: blr             lr
    // 0xb38444: tbnz            w0, #4, #0xb38464
    // 0xb38448: r0 = GetNavigation.size()
    //     0xb38448: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb3844c: LoadField: d0 = r0->field_f
    //     0xb3844c: ldur            d0, [x0, #0xf]
    // 0xb38450: d1 = 0.300000
    //     0xb38450: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb38454: ldr             d1, [x17, #0x658]
    // 0xb38458: fmul            d2, d0, d1
    // 0xb3845c: mov             v1.16b, v2.16b
    // 0xb38460: b               #0xb3847c
    // 0xb38464: r0 = GetNavigation.size()
    //     0xb38464: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb38468: LoadField: d0 = r0->field_f
    //     0xb38468: ldur            d0, [x0, #0xf]
    // 0xb3846c: d1 = 0.450000
    //     0xb3846c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xb38470: ldr             d1, [x17, #0xd08]
    // 0xb38474: fmul            d2, d0, d1
    // 0xb38478: mov             v1.16b, v2.16b
    // 0xb3847c: ldur            d0, [fp, #-0x48]
    // 0xb38480: ldur            x0, [fp, #-0x10]
    // 0xb38484: stur            d1, [fp, #-0x50]
    // 0xb38488: r0 = BoxConstraints()
    //     0xb38488: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb3848c: mov             x2, x0
    // 0xb38490: stur            x2, [fp, #-0x38]
    // 0xb38494: StoreField: r2->field_7 = rZR
    //     0xb38494: stur            xzr, [x2, #7]
    // 0xb38498: d0 = inf
    //     0xb38498: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb3849c: StoreField: r2->field_f = d0
    //     0xb3849c: stur            d0, [x2, #0xf]
    // 0xb384a0: ldur            d0, [fp, #-0x50]
    // 0xb384a4: ArrayStore: r2[0] = d0  ; List_8
    //     0xb384a4: stur            d0, [x2, #0x17]
    // 0xb384a8: ldur            d0, [fp, #-0x48]
    // 0xb384ac: StoreField: r2->field_1f = d0
    //     0xb384ac: stur            d0, [x2, #0x1f]
    // 0xb384b0: ldur            x0, [fp, #-0x10]
    // 0xb384b4: LoadField: r1 = r0->field_f
    //     0xb384b4: ldur            w1, [x0, #0xf]
    // 0xb384b8: DecompressPointer r1
    //     0xb384b8: add             x1, x1, HEAP, lsl #32
    // 0xb384bc: LoadField: r3 = r1->field_b
    //     0xb384bc: ldur            w3, [x1, #0xb]
    // 0xb384c0: DecompressPointer r3
    //     0xb384c0: add             x3, x3, HEAP, lsl #32
    // 0xb384c4: cmp             w3, NULL
    // 0xb384c8: b.eq            #0xb385d8
    // 0xb384cc: LoadField: r4 = r3->field_1f
    //     0xb384cc: ldur            w4, [x3, #0x1f]
    // 0xb384d0: DecompressPointer r4
    //     0xb384d0: add             x4, x4, HEAP, lsl #32
    // 0xb384d4: stur            x4, [fp, #-0x30]
    // 0xb384d8: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xb384d8: ldur            w5, [x3, #0x17]
    // 0xb384dc: DecompressPointer r5
    //     0xb384dc: add             x5, x5, HEAP, lsl #32
    // 0xb384e0: stur            x5, [fp, #-0x28]
    // 0xb384e4: LoadField: r6 = r3->field_1b
    //     0xb384e4: ldur            w6, [x3, #0x1b]
    // 0xb384e8: DecompressPointer r6
    //     0xb384e8: add             x6, x6, HEAP, lsl #32
    // 0xb384ec: stur            x6, [fp, #-0x20]
    // 0xb384f0: LoadField: r7 = r3->field_b
    //     0xb384f0: ldur            w7, [x3, #0xb]
    // 0xb384f4: DecompressPointer r7
    //     0xb384f4: add             x7, x7, HEAP, lsl #32
    // 0xb384f8: stur            x7, [fp, #-0x18]
    // 0xb384fc: LoadField: r0 = r3->field_f
    //     0xb384fc: ldur            w0, [x3, #0xf]
    // 0xb38500: DecompressPointer r0
    //     0xb38500: add             x0, x0, HEAP, lsl #32
    // 0xb38504: cmp             w0, NULL
    // 0xb38508: b.ne            #0xb38514
    // 0xb3850c: r0 = Null
    //     0xb3850c: mov             x0, NULL
    // 0xb38510: b               #0xb38560
    // 0xb38514: ldur            x1, [fp, #-8]
    // 0xb38518: LoadField: r8 = r0->field_f
    //     0xb38518: ldur            w8, [x0, #0xf]
    // 0xb3851c: DecompressPointer r8
    //     0xb3851c: add             x8, x8, HEAP, lsl #32
    // 0xb38520: LoadField: r0 = r1->field_13
    //     0xb38520: ldur            w0, [x1, #0x13]
    // 0xb38524: DecompressPointer r0
    //     0xb38524: add             x0, x0, HEAP, lsl #32
    // 0xb38528: LoadField: r1 = r8->field_b
    //     0xb38528: ldur            w1, [x8, #0xb]
    // 0xb3852c: r9 = LoadInt32Instr(r0)
    //     0xb3852c: sbfx            x9, x0, #1, #0x1f
    //     0xb38530: tbz             w0, #0, #0xb38538
    //     0xb38534: ldur            x9, [x0, #7]
    // 0xb38538: r0 = LoadInt32Instr(r1)
    //     0xb38538: sbfx            x0, x1, #1, #0x1f
    // 0xb3853c: mov             x1, x9
    // 0xb38540: cmp             x1, x0
    // 0xb38544: b.hs            #0xb385dc
    // 0xb38548: LoadField: r0 = r8->field_f
    //     0xb38548: ldur            w0, [x8, #0xf]
    // 0xb3854c: DecompressPointer r0
    //     0xb3854c: add             x0, x0, HEAP, lsl #32
    // 0xb38550: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xb38550: add             x16, x0, x9, lsl #2
    //     0xb38554: ldur            w1, [x16, #0xf]
    // 0xb38558: DecompressPointer r1
    //     0xb38558: add             x1, x1, HEAP, lsl #32
    // 0xb3855c: mov             x0, x1
    // 0xb38560: stur            x0, [fp, #-0x10]
    // 0xb38564: LoadField: r1 = r3->field_13
    //     0xb38564: ldur            w1, [x3, #0x13]
    // 0xb38568: DecompressPointer r1
    //     0xb38568: add             x1, x1, HEAP, lsl #32
    // 0xb3856c: stur            x1, [fp, #-8]
    // 0xb38570: r0 = OffersBottomSheet()
    //     0xb38570: bl              #0xb385e0  ; AllocateOffersBottomSheetStub -> OffersBottomSheet (size=0x24)
    // 0xb38574: mov             x1, x0
    // 0xb38578: ldur            x0, [fp, #-0x10]
    // 0xb3857c: stur            x1, [fp, #-0x40]
    // 0xb38580: StoreField: r1->field_b = r0
    //     0xb38580: stur            w0, [x1, #0xb]
    // 0xb38584: ldur            x0, [fp, #-8]
    // 0xb38588: StoreField: r1->field_f = r0
    //     0xb38588: stur            w0, [x1, #0xf]
    // 0xb3858c: ldur            x0, [fp, #-0x18]
    // 0xb38590: StoreField: r1->field_13 = r0
    //     0xb38590: stur            w0, [x1, #0x13]
    // 0xb38594: ldur            x0, [fp, #-0x28]
    // 0xb38598: ArrayStore: r1[0] = r0  ; List_4
    //     0xb38598: stur            w0, [x1, #0x17]
    // 0xb3859c: ldur            x0, [fp, #-0x20]
    // 0xb385a0: StoreField: r1->field_1b = r0
    //     0xb385a0: stur            w0, [x1, #0x1b]
    // 0xb385a4: ldur            x0, [fp, #-0x30]
    // 0xb385a8: StoreField: r1->field_1f = r0
    //     0xb385a8: stur            w0, [x1, #0x1f]
    // 0xb385ac: r0 = ConstrainedBox()
    //     0xb385ac: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xb385b0: ldur            x1, [fp, #-0x38]
    // 0xb385b4: StoreField: r0->field_f = r1
    //     0xb385b4: stur            w1, [x0, #0xf]
    // 0xb385b8: ldur            x1, [fp, #-0x40]
    // 0xb385bc: StoreField: r0->field_b = r1
    //     0xb385bc: stur            w1, [x0, #0xb]
    // 0xb385c0: LeaveFrame
    //     0xb385c0: mov             SP, fp
    //     0xb385c4: ldp             fp, lr, [SP], #0x10
    // 0xb385c8: ret
    //     0xb385c8: ret             
    // 0xb385cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb385cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb385d0: b               #0xb383bc
    // 0xb385d4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb385d4: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb385d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb385d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb385dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb385dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb385ec, size: 0x364
    // 0xb385ec: EnterFrame
    //     0xb385ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb385f0: mov             fp, SP
    // 0xb385f4: AllocStack(0x28)
    //     0xb385f4: sub             SP, SP, #0x28
    // 0xb385f8: SetupParameters()
    //     0xb385f8: ldr             x0, [fp, #0x10]
    //     0xb385fc: ldur            w2, [x0, #0x17]
    //     0xb38600: add             x2, x2, HEAP, lsl #32
    //     0xb38604: stur            x2, [fp, #-8]
    // 0xb38608: CheckStackOverflow
    //     0xb38608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb3860c: cmp             SP, x16
    //     0xb38610: b.ls            #0xb38930
    // 0xb38614: LoadField: r0 = r2->field_b
    //     0xb38614: ldur            w0, [x2, #0xb]
    // 0xb38618: DecompressPointer r0
    //     0xb38618: add             x0, x0, HEAP, lsl #32
    // 0xb3861c: LoadField: r1 = r0->field_f
    //     0xb3861c: ldur            w1, [x0, #0xf]
    // 0xb38620: DecompressPointer r1
    //     0xb38620: add             x1, x1, HEAP, lsl #32
    // 0xb38624: LoadField: r3 = r1->field_b
    //     0xb38624: ldur            w3, [x1, #0xb]
    // 0xb38628: DecompressPointer r3
    //     0xb38628: add             x3, x3, HEAP, lsl #32
    // 0xb3862c: cmp             w3, NULL
    // 0xb38630: b.eq            #0xb38938
    // 0xb38634: LoadField: r0 = r3->field_1f
    //     0xb38634: ldur            w0, [x3, #0x1f]
    // 0xb38638: DecompressPointer r0
    //     0xb38638: add             x0, x0, HEAP, lsl #32
    // 0xb3863c: tbnz            w0, #4, #0xb38920
    // 0xb38640: LoadField: r0 = r3->field_f
    //     0xb38640: ldur            w0, [x3, #0xf]
    // 0xb38644: DecompressPointer r0
    //     0xb38644: add             x0, x0, HEAP, lsl #32
    // 0xb38648: cmp             w0, NULL
    // 0xb3864c: b.ne            #0xb38658
    // 0xb38650: r0 = Null
    //     0xb38650: mov             x0, NULL
    // 0xb38654: b               #0xb386a4
    // 0xb38658: LoadField: r4 = r0->field_f
    //     0xb38658: ldur            w4, [x0, #0xf]
    // 0xb3865c: DecompressPointer r4
    //     0xb3865c: add             x4, x4, HEAP, lsl #32
    // 0xb38660: LoadField: r0 = r2->field_13
    //     0xb38660: ldur            w0, [x2, #0x13]
    // 0xb38664: DecompressPointer r0
    //     0xb38664: add             x0, x0, HEAP, lsl #32
    // 0xb38668: LoadField: r1 = r4->field_b
    //     0xb38668: ldur            w1, [x4, #0xb]
    // 0xb3866c: r5 = LoadInt32Instr(r0)
    //     0xb3866c: sbfx            x5, x0, #1, #0x1f
    //     0xb38670: tbz             w0, #0, #0xb38678
    //     0xb38674: ldur            x5, [x0, #7]
    // 0xb38678: r0 = LoadInt32Instr(r1)
    //     0xb38678: sbfx            x0, x1, #1, #0x1f
    // 0xb3867c: mov             x1, x5
    // 0xb38680: cmp             x1, x0
    // 0xb38684: b.hs            #0xb3893c
    // 0xb38688: LoadField: r0 = r4->field_f
    //     0xb38688: ldur            w0, [x4, #0xf]
    // 0xb3868c: DecompressPointer r0
    //     0xb3868c: add             x0, x0, HEAP, lsl #32
    // 0xb38690: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb38690: add             x16, x0, x5, lsl #2
    //     0xb38694: ldur            w1, [x16, #0xf]
    // 0xb38698: DecompressPointer r1
    //     0xb38698: add             x1, x1, HEAP, lsl #32
    // 0xb3869c: LoadField: r0 = r1->field_27
    //     0xb3869c: ldur            w0, [x1, #0x27]
    // 0xb386a0: DecompressPointer r0
    //     0xb386a0: add             x0, x0, HEAP, lsl #32
    // 0xb386a4: cmp             w0, NULL
    // 0xb386a8: b.eq            #0xb38920
    // 0xb386ac: tbnz            w0, #4, #0xb38920
    // 0xb386b0: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xb386b0: ldur            w0, [x3, #0x17]
    // 0xb386b4: DecompressPointer r0
    //     0xb386b4: add             x0, x0, HEAP, lsl #32
    // 0xb386b8: cmp             w0, NULL
    // 0xb386bc: b.eq            #0xb387d8
    // 0xb386c0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xb386c0: ldur            w4, [x3, #0x17]
    // 0xb386c4: DecompressPointer r4
    //     0xb386c4: add             x4, x4, HEAP, lsl #32
    // 0xb386c8: cmp             w4, NULL
    // 0xb386cc: b.eq            #0xb38904
    // 0xb386d0: LoadField: r0 = r3->field_f
    //     0xb386d0: ldur            w0, [x3, #0xf]
    // 0xb386d4: DecompressPointer r0
    //     0xb386d4: add             x0, x0, HEAP, lsl #32
    // 0xb386d8: cmp             w0, NULL
    // 0xb386dc: b.ne            #0xb386e8
    // 0xb386e0: r0 = Null
    //     0xb386e0: mov             x0, NULL
    // 0xb386e4: b               #0xb38734
    // 0xb386e8: LoadField: r5 = r0->field_f
    //     0xb386e8: ldur            w5, [x0, #0xf]
    // 0xb386ec: DecompressPointer r5
    //     0xb386ec: add             x5, x5, HEAP, lsl #32
    // 0xb386f0: LoadField: r0 = r2->field_13
    //     0xb386f0: ldur            w0, [x2, #0x13]
    // 0xb386f4: DecompressPointer r0
    //     0xb386f4: add             x0, x0, HEAP, lsl #32
    // 0xb386f8: LoadField: r1 = r5->field_b
    //     0xb386f8: ldur            w1, [x5, #0xb]
    // 0xb386fc: r6 = LoadInt32Instr(r0)
    //     0xb386fc: sbfx            x6, x0, #1, #0x1f
    //     0xb38700: tbz             w0, #0, #0xb38708
    //     0xb38704: ldur            x6, [x0, #7]
    // 0xb38708: r0 = LoadInt32Instr(r1)
    //     0xb38708: sbfx            x0, x1, #1, #0x1f
    // 0xb3870c: mov             x1, x6
    // 0xb38710: cmp             x1, x0
    // 0xb38714: b.hs            #0xb38940
    // 0xb38718: LoadField: r0 = r5->field_f
    //     0xb38718: ldur            w0, [x5, #0xf]
    // 0xb3871c: DecompressPointer r0
    //     0xb3871c: add             x0, x0, HEAP, lsl #32
    // 0xb38720: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb38720: add             x16, x0, x6, lsl #2
    //     0xb38724: ldur            w1, [x16, #0xf]
    // 0xb38728: DecompressPointer r1
    //     0xb38728: add             x1, x1, HEAP, lsl #32
    // 0xb3872c: LoadField: r0 = r1->field_7
    //     0xb3872c: ldur            w0, [x1, #7]
    // 0xb38730: DecompressPointer r0
    //     0xb38730: add             x0, x0, HEAP, lsl #32
    // 0xb38734: cmp             w0, NULL
    // 0xb38738: b.ne            #0xb38744
    // 0xb3873c: r5 = ""
    //     0xb3873c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb38740: b               #0xb38748
    // 0xb38744: mov             x5, x0
    // 0xb38748: LoadField: r0 = r3->field_f
    //     0xb38748: ldur            w0, [x3, #0xf]
    // 0xb3874c: DecompressPointer r0
    //     0xb3874c: add             x0, x0, HEAP, lsl #32
    // 0xb38750: cmp             w0, NULL
    // 0xb38754: b.ne            #0xb38760
    // 0xb38758: r0 = Null
    //     0xb38758: mov             x0, NULL
    // 0xb3875c: b               #0xb387ac
    // 0xb38760: LoadField: r3 = r0->field_f
    //     0xb38760: ldur            w3, [x0, #0xf]
    // 0xb38764: DecompressPointer r3
    //     0xb38764: add             x3, x3, HEAP, lsl #32
    // 0xb38768: LoadField: r0 = r2->field_13
    //     0xb38768: ldur            w0, [x2, #0x13]
    // 0xb3876c: DecompressPointer r0
    //     0xb3876c: add             x0, x0, HEAP, lsl #32
    // 0xb38770: LoadField: r1 = r3->field_b
    //     0xb38770: ldur            w1, [x3, #0xb]
    // 0xb38774: r6 = LoadInt32Instr(r0)
    //     0xb38774: sbfx            x6, x0, #1, #0x1f
    //     0xb38778: tbz             w0, #0, #0xb38780
    //     0xb3877c: ldur            x6, [x0, #7]
    // 0xb38780: r0 = LoadInt32Instr(r1)
    //     0xb38780: sbfx            x0, x1, #1, #0x1f
    // 0xb38784: mov             x1, x6
    // 0xb38788: cmp             x1, x0
    // 0xb3878c: b.hs            #0xb38944
    // 0xb38790: LoadField: r0 = r3->field_f
    //     0xb38790: ldur            w0, [x3, #0xf]
    // 0xb38794: DecompressPointer r0
    //     0xb38794: add             x0, x0, HEAP, lsl #32
    // 0xb38798: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb38798: add             x16, x0, x6, lsl #2
    //     0xb3879c: ldur            w1, [x16, #0xf]
    // 0xb387a0: DecompressPointer r1
    //     0xb387a0: add             x1, x1, HEAP, lsl #32
    // 0xb387a4: LoadField: r0 = r1->field_b
    //     0xb387a4: ldur            w0, [x1, #0xb]
    // 0xb387a8: DecompressPointer r0
    //     0xb387a8: add             x0, x0, HEAP, lsl #32
    // 0xb387ac: stp             x5, x4, [SP, #0x10]
    // 0xb387b0: r16 = "apply"
    //     0xb387b0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xb387b4: ldr             x16, [x16, #0xfe8]
    // 0xb387b8: stp             x16, x0, [SP]
    // 0xb387bc: r4 = 0
    //     0xb387bc: movz            x4, #0
    // 0xb387c0: ldr             x0, [SP, #0x18]
    // 0xb387c4: r16 = UnlinkedCall_0x613b5c
    //     0xb387c4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ab28] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb387c8: add             x16, x16, #0xb28
    // 0xb387cc: ldp             x5, lr, [x16]
    // 0xb387d0: blr             lr
    // 0xb387d4: b               #0xb38904
    // 0xb387d8: LoadField: r0 = r3->field_1b
    //     0xb387d8: ldur            w0, [x3, #0x1b]
    // 0xb387dc: DecompressPointer r0
    //     0xb387dc: add             x0, x0, HEAP, lsl #32
    // 0xb387e0: cmp             w0, NULL
    // 0xb387e4: b.eq            #0xb38904
    // 0xb387e8: LoadField: r2 = r3->field_1b
    //     0xb387e8: ldur            w2, [x3, #0x1b]
    // 0xb387ec: DecompressPointer r2
    //     0xb387ec: add             x2, x2, HEAP, lsl #32
    // 0xb387f0: cmp             w2, NULL
    // 0xb387f4: b.eq            #0xb38904
    // 0xb387f8: LoadField: r0 = r3->field_f
    //     0xb387f8: ldur            w0, [x3, #0xf]
    // 0xb387fc: DecompressPointer r0
    //     0xb387fc: add             x0, x0, HEAP, lsl #32
    // 0xb38800: cmp             w0, NULL
    // 0xb38804: b.ne            #0xb38814
    // 0xb38808: ldur            x4, [fp, #-8]
    // 0xb3880c: r0 = Null
    //     0xb3880c: mov             x0, NULL
    // 0xb38810: b               #0xb38864
    // 0xb38814: ldur            x4, [fp, #-8]
    // 0xb38818: LoadField: r5 = r0->field_f
    //     0xb38818: ldur            w5, [x0, #0xf]
    // 0xb3881c: DecompressPointer r5
    //     0xb3881c: add             x5, x5, HEAP, lsl #32
    // 0xb38820: LoadField: r0 = r4->field_13
    //     0xb38820: ldur            w0, [x4, #0x13]
    // 0xb38824: DecompressPointer r0
    //     0xb38824: add             x0, x0, HEAP, lsl #32
    // 0xb38828: LoadField: r1 = r5->field_b
    //     0xb38828: ldur            w1, [x5, #0xb]
    // 0xb3882c: r6 = LoadInt32Instr(r0)
    //     0xb3882c: sbfx            x6, x0, #1, #0x1f
    //     0xb38830: tbz             w0, #0, #0xb38838
    //     0xb38834: ldur            x6, [x0, #7]
    // 0xb38838: r0 = LoadInt32Instr(r1)
    //     0xb38838: sbfx            x0, x1, #1, #0x1f
    // 0xb3883c: mov             x1, x6
    // 0xb38840: cmp             x1, x0
    // 0xb38844: b.hs            #0xb38948
    // 0xb38848: LoadField: r0 = r5->field_f
    //     0xb38848: ldur            w0, [x5, #0xf]
    // 0xb3884c: DecompressPointer r0
    //     0xb3884c: add             x0, x0, HEAP, lsl #32
    // 0xb38850: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb38850: add             x16, x0, x6, lsl #2
    //     0xb38854: ldur            w1, [x16, #0xf]
    // 0xb38858: DecompressPointer r1
    //     0xb38858: add             x1, x1, HEAP, lsl #32
    // 0xb3885c: LoadField: r0 = r1->field_7
    //     0xb3885c: ldur            w0, [x1, #7]
    // 0xb38860: DecompressPointer r0
    //     0xb38860: add             x0, x0, HEAP, lsl #32
    // 0xb38864: cmp             w0, NULL
    // 0xb38868: b.ne            #0xb38874
    // 0xb3886c: r5 = ""
    //     0xb3886c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb38870: b               #0xb38878
    // 0xb38874: mov             x5, x0
    // 0xb38878: LoadField: r0 = r3->field_f
    //     0xb38878: ldur            w0, [x3, #0xf]
    // 0xb3887c: DecompressPointer r0
    //     0xb3887c: add             x0, x0, HEAP, lsl #32
    // 0xb38880: cmp             w0, NULL
    // 0xb38884: b.ne            #0xb38890
    // 0xb38888: r0 = Null
    //     0xb38888: mov             x0, NULL
    // 0xb3888c: b               #0xb388dc
    // 0xb38890: LoadField: r3 = r0->field_f
    //     0xb38890: ldur            w3, [x0, #0xf]
    // 0xb38894: DecompressPointer r3
    //     0xb38894: add             x3, x3, HEAP, lsl #32
    // 0xb38898: LoadField: r0 = r4->field_13
    //     0xb38898: ldur            w0, [x4, #0x13]
    // 0xb3889c: DecompressPointer r0
    //     0xb3889c: add             x0, x0, HEAP, lsl #32
    // 0xb388a0: LoadField: r1 = r3->field_b
    //     0xb388a0: ldur            w1, [x3, #0xb]
    // 0xb388a4: r6 = LoadInt32Instr(r0)
    //     0xb388a4: sbfx            x6, x0, #1, #0x1f
    //     0xb388a8: tbz             w0, #0, #0xb388b0
    //     0xb388ac: ldur            x6, [x0, #7]
    // 0xb388b0: r0 = LoadInt32Instr(r1)
    //     0xb388b0: sbfx            x0, x1, #1, #0x1f
    // 0xb388b4: mov             x1, x6
    // 0xb388b8: cmp             x1, x0
    // 0xb388bc: b.hs            #0xb3894c
    // 0xb388c0: LoadField: r0 = r3->field_f
    //     0xb388c0: ldur            w0, [x3, #0xf]
    // 0xb388c4: DecompressPointer r0
    //     0xb388c4: add             x0, x0, HEAP, lsl #32
    // 0xb388c8: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb388c8: add             x16, x0, x6, lsl #2
    //     0xb388cc: ldur            w1, [x16, #0xf]
    // 0xb388d0: DecompressPointer r1
    //     0xb388d0: add             x1, x1, HEAP, lsl #32
    // 0xb388d4: LoadField: r0 = r1->field_b
    //     0xb388d4: ldur            w0, [x1, #0xb]
    // 0xb388d8: DecompressPointer r0
    //     0xb388d8: add             x0, x0, HEAP, lsl #32
    // 0xb388dc: stp             x5, x2, [SP, #0x10]
    // 0xb388e0: r16 = "remove"
    //     0xb388e0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xb388e4: ldr             x16, [x16, #0xff0]
    // 0xb388e8: stp             x16, x0, [SP]
    // 0xb388ec: r4 = 0
    //     0xb388ec: movz            x4, #0
    // 0xb388f0: ldr             x0, [SP, #0x18]
    // 0xb388f4: r16 = UnlinkedCall_0x613b5c
    //     0xb388f4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6ab38] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb388f8: add             x16, x16, #0xb38
    // 0xb388fc: ldp             x5, lr, [x16]
    // 0xb38900: blr             lr
    // 0xb38904: ldur            x0, [fp, #-8]
    // 0xb38908: LoadField: r1 = r0->field_f
    //     0xb38908: ldur            w1, [x0, #0xf]
    // 0xb3890c: DecompressPointer r1
    //     0xb3890c: add             x1, x1, HEAP, lsl #32
    // 0xb38910: r16 = <Object?>
    //     0xb38910: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xb38914: stp             x1, x16, [SP]
    // 0xb38918: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb38918: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3891c: r0 = pop()
    //     0xb3891c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb38920: r0 = Null
    //     0xb38920: mov             x0, NULL
    // 0xb38924: LeaveFrame
    //     0xb38924: mov             SP, fp
    //     0xb38928: ldp             fp, lr, [SP], #0x10
    // 0xb3892c: ret
    //     0xb3892c: ret             
    // 0xb38930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb38930: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb38934: b               #0xb38614
    // 0xb38938: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb38938: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3893c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3893c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb38940: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb38940: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb38944: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb38944: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb38948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb38948: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3894c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3894c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb38950, size: 0x9c
    // 0xb38950: EnterFrame
    //     0xb38950: stp             fp, lr, [SP, #-0x10]!
    //     0xb38954: mov             fp, SP
    // 0xb38958: AllocStack(0x38)
    //     0xb38958: sub             SP, SP, #0x38
    // 0xb3895c: SetupParameters()
    //     0xb3895c: ldr             x0, [fp, #0x10]
    //     0xb38960: ldur            w2, [x0, #0x17]
    //     0xb38964: add             x2, x2, HEAP, lsl #32
    //     0xb38968: stur            x2, [fp, #-8]
    // 0xb3896c: CheckStackOverflow
    //     0xb3896c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb38970: cmp             SP, x16
    //     0xb38974: b.ls            #0xb389e4
    // 0xb38978: LoadField: r0 = r2->field_f
    //     0xb38978: ldur            w0, [x2, #0xf]
    // 0xb3897c: DecompressPointer r0
    //     0xb3897c: add             x0, x0, HEAP, lsl #32
    // 0xb38980: r16 = <Object?>
    //     0xb38980: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xb38984: stp             x0, x16, [SP]
    // 0xb38988: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb38988: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3898c: r0 = pop()
    //     0xb3898c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb38990: ldur            x2, [fp, #-8]
    // 0xb38994: LoadField: r0 = r2->field_f
    //     0xb38994: ldur            w0, [x2, #0xf]
    // 0xb38998: DecompressPointer r0
    //     0xb38998: add             x0, x0, HEAP, lsl #32
    // 0xb3899c: stur            x0, [fp, #-0x10]
    // 0xb389a0: r1 = Function '<anonymous closure>':.
    //     0xb389a0: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6ab48] AnonymousClosure: (0xb38394), in [package:customer_app/app/presentation/views/glass/bag/offers_content_item_view.dart] _OffersContentItemViewState::build (0xb35ba0)
    //     0xb389a4: ldr             x1, [x1, #0xb48]
    // 0xb389a8: r0 = AllocateClosure()
    //     0xb389a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb389ac: stp             x0, NULL, [SP, #0x18]
    // 0xb389b0: ldur            x16, [fp, #-0x10]
    // 0xb389b4: r30 = true
    //     0xb389b4: add             lr, NULL, #0x20  ; true
    // 0xb389b8: stp             lr, x16, [SP, #8]
    // 0xb389bc: r16 = Instance_RoundedRectangleBorder
    //     0xb389bc: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0xb389c0: ldr             x16, [x16, #0xc78]
    // 0xb389c4: str             x16, [SP]
    // 0xb389c8: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xb389c8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xb389cc: ldr             x4, [x4, #0xb20]
    // 0xb389d0: r0 = showModalBottomSheet()
    //     0xb389d0: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xb389d4: r0 = Null
    //     0xb389d4: mov             x0, NULL
    // 0xb389d8: LeaveFrame
    //     0xb389d8: mov             SP, fp
    //     0xb389dc: ldp             fp, lr, [SP], #0x10
    // 0xb389e0: ret
    //     0xb389e0: ret             
    // 0xb389e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb389e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb389e8: b               #0xb38978
  }
}

// class id: 4114, size: 0x2c, field offset: 0xc
//   const constructor, 
class OffersContentItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e7a8, size: 0x24
    // 0xc7e7a8: EnterFrame
    //     0xc7e7a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e7ac: mov             fp, SP
    // 0xc7e7b0: mov             x0, x1
    // 0xc7e7b4: r1 = <OffersContentItemView>
    //     0xc7e7b4: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e40] TypeArguments: <OffersContentItemView>
    //     0xc7e7b8: ldr             x1, [x1, #0xe40]
    // 0xc7e7bc: r0 = _OffersContentItemViewState()
    //     0xc7e7bc: bl              #0xc7e7cc  ; Allocate_OffersContentItemViewStateStub -> _OffersContentItemViewState (size=0x14)
    // 0xc7e7c0: LeaveFrame
    //     0xc7e7c0: mov             SP, fp
    //     0xc7e7c4: ldp             fp, lr, [SP], #0x10
    // 0xc7e7c8: ret
    //     0xc7e7c8: ret             
  }
}
