// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/select_quantity_picker.dart

// class id: 1049447, size: 0x8
class :: {
}

// class id: 3305, size: 0x14, field offset: 0x14
class _SelectQuantityPickerState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb90964, size: 0x6ac
    // 0xb90964: EnterFrame
    //     0xb90964: stp             fp, lr, [SP, #-0x10]!
    //     0xb90968: mov             fp, SP
    // 0xb9096c: AllocStack(0x50)
    //     0xb9096c: sub             SP, SP, #0x50
    // 0xb90970: SetupParameters(_SelectQuantityPickerState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb90970: stur            x1, [fp, #-8]
    //     0xb90974: stur            x2, [fp, #-0x10]
    // 0xb90978: CheckStackOverflow
    //     0xb90978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9097c: cmp             SP, x16
    //     0xb90980: b.ls            #0xb90fdc
    // 0xb90984: r1 = 4
    //     0xb90984: movz            x1, #0x4
    // 0xb90988: r0 = AllocateContext()
    //     0xb90988: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9098c: mov             x1, x0
    // 0xb90990: ldur            x0, [fp, #-8]
    // 0xb90994: stur            x1, [fp, #-0x18]
    // 0xb90998: StoreField: r1->field_f = r0
    //     0xb90998: stur            w0, [x1, #0xf]
    // 0xb9099c: ldur            x2, [fp, #-0x10]
    // 0xb909a0: StoreField: r1->field_13 = r2
    //     0xb909a0: stur            w2, [x1, #0x13]
    // 0xb909a4: r2 = Sentinel
    //     0xb909a4: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb909a8: ArrayStore: r1[0] = r2  ; List_4
    //     0xb909a8: stur            w2, [x1, #0x17]
    // 0xb909ac: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb909ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb909b0: ldr             x0, [x0]
    //     0xb909b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb909b8: cmp             w0, w16
    //     0xb909bc: b.ne            #0xb909c8
    //     0xb909c0: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb909c4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb909c8: r1 = <int>
    //     0xb909c8: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb909cc: stur            x0, [fp, #-0x10]
    // 0xb909d0: r0 = AllocateGrowableArray()
    //     0xb909d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb909d4: mov             x1, x0
    // 0xb909d8: ldur            x0, [fp, #-0x10]
    // 0xb909dc: StoreField: r1->field_f = r0
    //     0xb909dc: stur            w0, [x1, #0xf]
    // 0xb909e0: StoreField: r1->field_b = rZR
    //     0xb909e0: stur            wzr, [x1, #0xb]
    // 0xb909e4: ldur            x3, [fp, #-0x18]
    // 0xb909e8: StoreField: r3->field_1b = r1
    //     0xb909e8: stur            w1, [x3, #0x1b]
    // 0xb909ec: ldur            x4, [fp, #-8]
    // 0xb909f0: LoadField: r0 = r4->field_b
    //     0xb909f0: ldur            w0, [x4, #0xb]
    // 0xb909f4: DecompressPointer r0
    //     0xb909f4: add             x0, x0, HEAP, lsl #32
    // 0xb909f8: cmp             w0, NULL
    // 0xb909fc: b.eq            #0xb90fe4
    // 0xb90a00: LoadField: r1 = r0->field_b
    //     0xb90a00: ldur            w1, [x0, #0xb]
    // 0xb90a04: DecompressPointer r1
    //     0xb90a04: add             x1, x1, HEAP, lsl #32
    // 0xb90a08: LoadField: r2 = r1->field_7b
    //     0xb90a08: ldur            w2, [x1, #0x7b]
    // 0xb90a0c: DecompressPointer r2
    //     0xb90a0c: add             x2, x2, HEAP, lsl #32
    // 0xb90a10: cmp             w2, NULL
    // 0xb90a14: b.ne            #0xb90a20
    // 0xb90a18: r5 = 1
    //     0xb90a18: movz            x5, #0x1
    // 0xb90a1c: b               #0xb90a30
    // 0xb90a20: r0 = LoadInt32Instr(r2)
    //     0xb90a20: sbfx            x0, x2, #1, #0x1f
    //     0xb90a24: tbz             w2, #0, #0xb90a2c
    //     0xb90a28: ldur            x0, [x2, #7]
    // 0xb90a2c: mov             x5, x0
    // 0xb90a30: r0 = BoxInt64Instr(r5)
    //     0xb90a30: sbfiz           x0, x5, #1, #0x1f
    //     0xb90a34: cmp             x5, x0, asr #1
    //     0xb90a38: b.eq            #0xb90a44
    //     0xb90a3c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb90a40: stur            x5, [x0, #7]
    // 0xb90a44: stur            x0, [fp, #-0x10]
    // 0xb90a48: cmp             x5, #5
    // 0xb90a4c: b.le            #0xb90b14
    // 0xb90a50: r1 = <int>
    //     0xb90a50: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb90a54: r2 = 5
    //     0xb90a54: movz            x2, #0x5
    // 0xb90a58: r0 = _GrowableList()
    //     0xb90a58: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb90a5c: LoadField: r1 = r0->field_b
    //     0xb90a5c: ldur            w1, [x0, #0xb]
    // 0xb90a60: r2 = LoadInt32Instr(r1)
    //     0xb90a60: sbfx            x2, x1, #1, #0x1f
    // 0xb90a64: LoadField: r3 = r0->field_f
    //     0xb90a64: ldur            w3, [x0, #0xf]
    // 0xb90a68: DecompressPointer r3
    //     0xb90a68: add             x3, x3, HEAP, lsl #32
    // 0xb90a6c: r1 = 0
    //     0xb90a6c: movz            x1, #0
    // 0xb90a70: CheckStackOverflow
    //     0xb90a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90a74: cmp             SP, x16
    //     0xb90a78: b.ls            #0xb90fe8
    // 0xb90a7c: cmp             x1, x2
    // 0xb90a80: b.ge            #0xb90a9c
    // 0xb90a84: add             x4, x1, #1
    // 0xb90a88: lsl             x5, x4, #1
    // 0xb90a8c: ArrayStore: r3[r1] = r5  ; Unknown_4
    //     0xb90a8c: add             x6, x3, x1, lsl #2
    //     0xb90a90: stur            w5, [x6, #0xf]
    // 0xb90a94: mov             x1, x4
    // 0xb90a98: b               #0xb90a70
    // 0xb90a9c: ldur            x5, [fp, #-8]
    // 0xb90aa0: ldur            x4, [fp, #-0x18]
    // 0xb90aa4: StoreField: r4->field_1b = r0
    //     0xb90aa4: stur            w0, [x4, #0x1b]
    //     0xb90aa8: ldurb           w16, [x4, #-1]
    //     0xb90aac: ldurb           w17, [x0, #-1]
    //     0xb90ab0: and             x16, x17, x16, lsr #2
    //     0xb90ab4: tst             x16, HEAP, lsr #32
    //     0xb90ab8: b.eq            #0xb90ac0
    //     0xb90abc: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb90ac0: LoadField: r0 = r5->field_b
    //     0xb90ac0: ldur            w0, [x5, #0xb]
    // 0xb90ac4: DecompressPointer r0
    //     0xb90ac4: add             x0, x0, HEAP, lsl #32
    // 0xb90ac8: cmp             w0, NULL
    // 0xb90acc: b.eq            #0xb90ff0
    // 0xb90ad0: LoadField: r5 = r0->field_f
    //     0xb90ad0: ldur            x5, [x0, #0xf]
    // 0xb90ad4: mov             x0, x2
    // 0xb90ad8: mov             x1, x5
    // 0xb90adc: cmp             x1, x0
    // 0xb90ae0: b.hs            #0xb90ff4
    // 0xb90ae4: ArrayLoad: r0 = r3[r5]  ; Unknown_4
    //     0xb90ae4: add             x16, x3, x5, lsl #2
    //     0xb90ae8: ldur            w0, [x16, #0xf]
    // 0xb90aec: DecompressPointer r0
    //     0xb90aec: add             x0, x0, HEAP, lsl #32
    // 0xb90af0: ArrayStore: r4[0] = r0  ; List_4
    //     0xb90af0: stur            w0, [x4, #0x17]
    //     0xb90af4: tbz             w0, #0, #0xb90b10
    //     0xb90af8: ldurb           w16, [x4, #-1]
    //     0xb90afc: ldurb           w17, [x0, #-1]
    //     0xb90b00: and             x16, x17, x16, lsr #2
    //     0xb90b04: tst             x16, HEAP, lsr #32
    //     0xb90b08: b.eq            #0xb90b10
    //     0xb90b0c: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb90b10: b               #0xb90c78
    // 0xb90b14: mov             x5, x4
    // 0xb90b18: mov             x4, x3
    // 0xb90b1c: cbnz            w0, #0xb90b98
    // 0xb90b20: ArrayStore: r4[0] = rZR  ; List_4
    //     0xb90b20: stur            wzr, [x4, #0x17]
    // 0xb90b24: r1 = <int>
    //     0xb90b24: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb90b28: r2 = 1
    //     0xb90b28: movz            x2, #0x1
    // 0xb90b2c: r0 = _GrowableList()
    //     0xb90b2c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb90b30: LoadField: r1 = r0->field_b
    //     0xb90b30: ldur            w1, [x0, #0xb]
    // 0xb90b34: r2 = LoadInt32Instr(r1)
    //     0xb90b34: sbfx            x2, x1, #1, #0x1f
    // 0xb90b38: LoadField: r1 = r0->field_f
    //     0xb90b38: ldur            w1, [x0, #0xf]
    // 0xb90b3c: DecompressPointer r1
    //     0xb90b3c: add             x1, x1, HEAP, lsl #32
    // 0xb90b40: r3 = 0
    //     0xb90b40: movz            x3, #0
    // 0xb90b44: CheckStackOverflow
    //     0xb90b44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90b48: cmp             SP, x16
    //     0xb90b4c: b.ls            #0xb90ff8
    // 0xb90b50: cmp             x3, x2
    // 0xb90b54: b.ge            #0xb90b70
    // 0xb90b58: lsl             x4, x3, #1
    // 0xb90b5c: ArrayStore: r1[r3] = r4  ; Unknown_4
    //     0xb90b5c: add             x5, x1, x3, lsl #2
    //     0xb90b60: stur            w4, [x5, #0xf]
    // 0xb90b64: add             x4, x3, #1
    // 0xb90b68: mov             x3, x4
    // 0xb90b6c: b               #0xb90b44
    // 0xb90b70: ldur            x3, [fp, #-0x18]
    // 0xb90b74: StoreField: r3->field_1b = r0
    //     0xb90b74: stur            w0, [x3, #0x1b]
    //     0xb90b78: ldurb           w16, [x3, #-1]
    //     0xb90b7c: ldurb           w17, [x0, #-1]
    //     0xb90b80: and             x16, x17, x16, lsr #2
    //     0xb90b84: tst             x16, HEAP, lsr #32
    //     0xb90b88: b.eq            #0xb90b90
    //     0xb90b8c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb90b90: mov             x4, x3
    // 0xb90b94: b               #0xb90c78
    // 0xb90b98: mov             x3, x4
    // 0xb90b9c: cmp             w2, NULL
    // 0xb90ba0: b.ne            #0xb90bac
    // 0xb90ba4: r2 = 1
    //     0xb90ba4: movz            x2, #0x1
    // 0xb90ba8: b               #0xb90bbc
    // 0xb90bac: r0 = LoadInt32Instr(r2)
    //     0xb90bac: sbfx            x0, x2, #1, #0x1f
    //     0xb90bb0: tbz             w2, #0, #0xb90bb8
    //     0xb90bb4: ldur            x0, [x2, #7]
    // 0xb90bb8: mov             x2, x0
    // 0xb90bbc: r1 = <int>
    //     0xb90bbc: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb90bc0: r0 = _GrowableList()
    //     0xb90bc0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb90bc4: LoadField: r1 = r0->field_b
    //     0xb90bc4: ldur            w1, [x0, #0xb]
    // 0xb90bc8: r2 = LoadInt32Instr(r1)
    //     0xb90bc8: sbfx            x2, x1, #1, #0x1f
    // 0xb90bcc: LoadField: r3 = r0->field_f
    //     0xb90bcc: ldur            w3, [x0, #0xf]
    // 0xb90bd0: DecompressPointer r3
    //     0xb90bd0: add             x3, x3, HEAP, lsl #32
    // 0xb90bd4: r1 = 0
    //     0xb90bd4: movz            x1, #0
    // 0xb90bd8: CheckStackOverflow
    //     0xb90bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb90bdc: cmp             SP, x16
    //     0xb90be0: b.ls            #0xb91000
    // 0xb90be4: cmp             x1, x2
    // 0xb90be8: b.ge            #0xb90c04
    // 0xb90bec: add             x4, x1, #1
    // 0xb90bf0: lsl             x5, x4, #1
    // 0xb90bf4: ArrayStore: r3[r1] = r5  ; Unknown_4
    //     0xb90bf4: add             x6, x3, x1, lsl #2
    //     0xb90bf8: stur            w5, [x6, #0xf]
    // 0xb90bfc: mov             x1, x4
    // 0xb90c00: b               #0xb90bd8
    // 0xb90c04: ldur            x1, [fp, #-8]
    // 0xb90c08: ldur            x4, [fp, #-0x18]
    // 0xb90c0c: StoreField: r4->field_1b = r0
    //     0xb90c0c: stur            w0, [x4, #0x1b]
    //     0xb90c10: ldurb           w16, [x4, #-1]
    //     0xb90c14: ldurb           w17, [x0, #-1]
    //     0xb90c18: and             x16, x17, x16, lsr #2
    //     0xb90c1c: tst             x16, HEAP, lsr #32
    //     0xb90c20: b.eq            #0xb90c28
    //     0xb90c24: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb90c28: LoadField: r0 = r1->field_b
    //     0xb90c28: ldur            w0, [x1, #0xb]
    // 0xb90c2c: DecompressPointer r0
    //     0xb90c2c: add             x0, x0, HEAP, lsl #32
    // 0xb90c30: cmp             w0, NULL
    // 0xb90c34: b.eq            #0xb91008
    // 0xb90c38: LoadField: r5 = r0->field_f
    //     0xb90c38: ldur            x5, [x0, #0xf]
    // 0xb90c3c: mov             x0, x2
    // 0xb90c40: mov             x1, x5
    // 0xb90c44: cmp             x1, x0
    // 0xb90c48: b.hs            #0xb9100c
    // 0xb90c4c: ArrayLoad: r0 = r3[r5]  ; Unknown_4
    //     0xb90c4c: add             x16, x3, x5, lsl #2
    //     0xb90c50: ldur            w0, [x16, #0xf]
    // 0xb90c54: DecompressPointer r0
    //     0xb90c54: add             x0, x0, HEAP, lsl #32
    // 0xb90c58: ArrayStore: r4[0] = r0  ; List_4
    //     0xb90c58: stur            w0, [x4, #0x17]
    //     0xb90c5c: tbz             w0, #0, #0xb90c78
    //     0xb90c60: ldurb           w16, [x4, #-1]
    //     0xb90c64: ldurb           w17, [x0, #-1]
    //     0xb90c68: and             x16, x17, x16, lsr #2
    //     0xb90c6c: tst             x16, HEAP, lsr #32
    //     0xb90c70: b.eq            #0xb90c78
    //     0xb90c74: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0xb90c78: LoadField: r1 = r4->field_13
    //     0xb90c78: ldur            w1, [x4, #0x13]
    // 0xb90c7c: DecompressPointer r1
    //     0xb90c7c: add             x1, x1, HEAP, lsl #32
    // 0xb90c80: r0 = of()
    //     0xb90c80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb90c84: LoadField: r1 = r0->field_87
    //     0xb90c84: ldur            w1, [x0, #0x87]
    // 0xb90c88: DecompressPointer r1
    //     0xb90c88: add             x1, x1, HEAP, lsl #32
    // 0xb90c8c: LoadField: r0 = r1->field_7
    //     0xb90c8c: ldur            w0, [x1, #7]
    // 0xb90c90: DecompressPointer r0
    //     0xb90c90: add             x0, x0, HEAP, lsl #32
    // 0xb90c94: r16 = 12.000000
    //     0xb90c94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb90c98: ldr             x16, [x16, #0x9e8]
    // 0xb90c9c: r30 = Instance_Color
    //     0xb90c9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb90ca0: stp             lr, x16, [SP]
    // 0xb90ca4: mov             x1, x0
    // 0xb90ca8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb90ca8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb90cac: ldr             x4, [x4, #0xaa0]
    // 0xb90cb0: r0 = copyWith()
    //     0xb90cb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb90cb4: stur            x0, [fp, #-8]
    // 0xb90cb8: r0 = Text()
    //     0xb90cb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb90cbc: mov             x2, x0
    // 0xb90cc0: r0 = "Quantity"
    //     0xb90cc0: add             x0, PP, #0x52, lsl #12  ; [pp+0x523f8] "Quantity"
    //     0xb90cc4: ldr             x0, [x0, #0x3f8]
    // 0xb90cc8: stur            x2, [fp, #-0x20]
    // 0xb90ccc: StoreField: r2->field_b = r0
    //     0xb90ccc: stur            w0, [x2, #0xb]
    // 0xb90cd0: ldur            x0, [fp, #-8]
    // 0xb90cd4: StoreField: r2->field_13 = r0
    //     0xb90cd4: stur            w0, [x2, #0x13]
    // 0xb90cd8: ldur            x0, [fp, #-0x18]
    // 0xb90cdc: LoadField: r1 = r0->field_13
    //     0xb90cdc: ldur            w1, [x0, #0x13]
    // 0xb90ce0: DecompressPointer r1
    //     0xb90ce0: add             x1, x1, HEAP, lsl #32
    // 0xb90ce4: r0 = of()
    //     0xb90ce4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb90ce8: LoadField: r1 = r0->field_5b
    //     0xb90ce8: ldur            w1, [x0, #0x5b]
    // 0xb90cec: DecompressPointer r1
    //     0xb90cec: add             x1, x1, HEAP, lsl #32
    // 0xb90cf0: r0 = LoadClassIdInstr(r1)
    //     0xb90cf0: ldur            x0, [x1, #-1]
    //     0xb90cf4: ubfx            x0, x0, #0xc, #0x14
    // 0xb90cf8: d0 = 0.030000
    //     0xb90cf8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb90cfc: ldr             d0, [x17, #0x238]
    // 0xb90d00: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb90d00: sub             lr, x0, #0xffa
    //     0xb90d04: ldr             lr, [x21, lr, lsl #3]
    //     0xb90d08: blr             lr
    // 0xb90d0c: stur            x0, [fp, #-8]
    // 0xb90d10: r0 = Radius()
    //     0xb90d10: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb90d14: d0 = 30.000000
    //     0xb90d14: fmov            d0, #30.00000000
    // 0xb90d18: stur            x0, [fp, #-0x28]
    // 0xb90d1c: StoreField: r0->field_7 = d0
    //     0xb90d1c: stur            d0, [x0, #7]
    // 0xb90d20: StoreField: r0->field_f = d0
    //     0xb90d20: stur            d0, [x0, #0xf]
    // 0xb90d24: r0 = BorderRadius()
    //     0xb90d24: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb90d28: mov             x1, x0
    // 0xb90d2c: ldur            x0, [fp, #-0x28]
    // 0xb90d30: stur            x1, [fp, #-0x30]
    // 0xb90d34: StoreField: r1->field_7 = r0
    //     0xb90d34: stur            w0, [x1, #7]
    // 0xb90d38: StoreField: r1->field_b = r0
    //     0xb90d38: stur            w0, [x1, #0xb]
    // 0xb90d3c: StoreField: r1->field_f = r0
    //     0xb90d3c: stur            w0, [x1, #0xf]
    // 0xb90d40: StoreField: r1->field_13 = r0
    //     0xb90d40: stur            w0, [x1, #0x13]
    // 0xb90d44: r0 = BoxDecoration()
    //     0xb90d44: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb90d48: mov             x2, x0
    // 0xb90d4c: ldur            x0, [fp, #-8]
    // 0xb90d50: stur            x2, [fp, #-0x28]
    // 0xb90d54: StoreField: r2->field_7 = r0
    //     0xb90d54: stur            w0, [x2, #7]
    // 0xb90d58: ldur            x0, [fp, #-0x30]
    // 0xb90d5c: StoreField: r2->field_13 = r0
    //     0xb90d5c: stur            w0, [x2, #0x13]
    // 0xb90d60: r0 = Instance_BoxShape
    //     0xb90d60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb90d64: ldr             x0, [x0, #0x80]
    // 0xb90d68: StoreField: r2->field_23 = r0
    //     0xb90d68: stur            w0, [x2, #0x23]
    // 0xb90d6c: ldur            x0, [fp, #-0x18]
    // 0xb90d70: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xb90d70: ldur            w5, [x0, #0x17]
    // 0xb90d74: DecompressPointer r5
    //     0xb90d74: add             x5, x5, HEAP, lsl #32
    // 0xb90d78: stur            x5, [fp, #-8]
    // 0xb90d7c: r16 = Sentinel
    //     0xb90d7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb90d80: cmp             w5, w16
    // 0xb90d84: b.eq            #0xb90fc8
    // 0xb90d88: ldur            x3, [fp, #-0x10]
    // 0xb90d8c: LoadField: r1 = r0->field_13
    //     0xb90d8c: ldur            w1, [x0, #0x13]
    // 0xb90d90: DecompressPointer r1
    //     0xb90d90: add             x1, x1, HEAP, lsl #32
    // 0xb90d94: r0 = of()
    //     0xb90d94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb90d98: LoadField: r3 = r0->field_5b
    //     0xb90d98: ldur            w3, [x0, #0x5b]
    // 0xb90d9c: DecompressPointer r3
    //     0xb90d9c: add             x3, x3, HEAP, lsl #32
    // 0xb90da0: ldur            x0, [fp, #-0x18]
    // 0xb90da4: stur            x3, [fp, #-0x38]
    // 0xb90da8: LoadField: r4 = r0->field_1b
    //     0xb90da8: ldur            w4, [x0, #0x1b]
    // 0xb90dac: DecompressPointer r4
    //     0xb90dac: add             x4, x4, HEAP, lsl #32
    // 0xb90db0: mov             x2, x0
    // 0xb90db4: stur            x4, [fp, #-0x30]
    // 0xb90db8: r1 = Function '<anonymous closure>':.
    //     0xb90db8: add             x1, PP, #0x55, lsl #12  ; [pp+0x552d8] AnonymousClosure: (0xb911ec), in [package:customer_app/app/presentation/views/line/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xc0c320)
    //     0xb90dbc: ldr             x1, [x1, #0x2d8]
    // 0xb90dc0: r0 = AllocateClosure()
    //     0xb90dc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90dc4: r16 = <DropdownMenuItem<int>>
    //     0xb90dc4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52408] TypeArguments: <DropdownMenuItem<int>>
    //     0xb90dc8: ldr             x16, [x16, #0x408]
    // 0xb90dcc: ldur            lr, [fp, #-0x30]
    // 0xb90dd0: stp             lr, x16, [SP, #8]
    // 0xb90dd4: str             x0, [SP]
    // 0xb90dd8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb90dd8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb90ddc: r0 = map()
    //     0xb90ddc: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xb90de0: mov             x1, x0
    // 0xb90de4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb90de4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb90de8: r0 = toList()
    //     0xb90de8: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xb90dec: mov             x3, x0
    // 0xb90df0: ldur            x0, [fp, #-0x10]
    // 0xb90df4: stur            x3, [fp, #-0x30]
    // 0xb90df8: cbz             w0, #0xb90e14
    // 0xb90dfc: ldur            x2, [fp, #-0x18]
    // 0xb90e00: r1 = Function '<anonymous closure>':.
    //     0xb90e00: add             x1, PP, #0x55, lsl #12  ; [pp+0x552e0] AnonymousClosure: (0xb91030), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xb90964)
    //     0xb90e04: ldr             x1, [x1, #0x2e0]
    // 0xb90e08: r0 = AllocateClosure()
    //     0xb90e08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb90e0c: mov             x3, x0
    // 0xb90e10: b               #0xb90e18
    // 0xb90e14: r3 = Null
    //     0xb90e14: mov             x3, NULL
    // 0xb90e18: ldur            x0, [fp, #-0x20]
    // 0xb90e1c: stur            x3, [fp, #-0x10]
    // 0xb90e20: r1 = <int>
    //     0xb90e20: ldr             x1, [PP, #0x58]  ; [pp+0x58] TypeArguments: <int>
    // 0xb90e24: r0 = DropdownButton()
    //     0xb90e24: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xb90e28: stur            x0, [fp, #-0x18]
    // 0xb90e2c: r16 = Instance_Icon
    //     0xb90e2c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e530] Obj!Icon@d66771
    //     0xb90e30: ldr             x16, [x16, #0x530]
    // 0xb90e34: ldur            lr, [fp, #-0x38]
    // 0xb90e38: stp             lr, x16, [SP]
    // 0xb90e3c: mov             x1, x0
    // 0xb90e40: ldur            x2, [fp, #-0x30]
    // 0xb90e44: ldur            x3, [fp, #-0x10]
    // 0xb90e48: ldur            x5, [fp, #-8]
    // 0xb90e4c: r4 = const [0, 0x6, 0x2, 0x4, icon, 0x4, iconEnabledColor, 0x5, null]
    //     0xb90e4c: add             x4, PP, #0x55, lsl #12  ; [pp+0x552b8] List(9) [0, 0x6, 0x2, 0x4, "icon", 0x4, "iconEnabledColor", 0x5, Null]
    //     0xb90e50: ldr             x4, [x4, #0x2b8]
    // 0xb90e54: r0 = DropdownButton()
    //     0xb90e54: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xb90e58: r0 = DropdownButtonHideUnderline()
    //     0xb90e58: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xb90e5c: mov             x1, x0
    // 0xb90e60: ldur            x0, [fp, #-0x18]
    // 0xb90e64: stur            x1, [fp, #-8]
    // 0xb90e68: StoreField: r1->field_b = r0
    //     0xb90e68: stur            w0, [x1, #0xb]
    // 0xb90e6c: r0 = Container()
    //     0xb90e6c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb90e70: stur            x0, [fp, #-0x10]
    // 0xb90e74: r16 = Instance_Alignment
    //     0xb90e74: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb90e78: ldr             x16, [x16, #0xb10]
    // 0xb90e7c: r30 = 44.000000
    //     0xb90e7c: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb90e80: ldr             lr, [lr, #0xad8]
    // 0xb90e84: stp             lr, x16, [SP, #8]
    // 0xb90e88: ldur            x16, [fp, #-8]
    // 0xb90e8c: str             x16, [SP]
    // 0xb90e90: mov             x1, x0
    // 0xb90e94: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, height, 0x2, null]
    //     0xb90e94: add             x4, PP, #0x52, lsl #12  ; [pp+0x523a0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "height", 0x2, Null]
    //     0xb90e98: ldr             x4, [x4, #0x3a0]
    // 0xb90e9c: r0 = Container()
    //     0xb90e9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb90ea0: r0 = Padding()
    //     0xb90ea0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb90ea4: mov             x1, x0
    // 0xb90ea8: r0 = Instance_EdgeInsets
    //     0xb90ea8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xb90eac: ldr             x0, [x0, #0xc10]
    // 0xb90eb0: stur            x1, [fp, #-8]
    // 0xb90eb4: StoreField: r1->field_f = r0
    //     0xb90eb4: stur            w0, [x1, #0xf]
    // 0xb90eb8: ldur            x0, [fp, #-0x10]
    // 0xb90ebc: StoreField: r1->field_b = r0
    //     0xb90ebc: stur            w0, [x1, #0xb]
    // 0xb90ec0: r0 = Container()
    //     0xb90ec0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb90ec4: stur            x0, [fp, #-0x10]
    // 0xb90ec8: r16 = Instance_AlignmentDirectional
    //     0xb90ec8: add             x16, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xb90ecc: ldr             x16, [x16, #0x3a8]
    // 0xb90ed0: ldur            lr, [fp, #-0x28]
    // 0xb90ed4: stp             lr, x16, [SP, #8]
    // 0xb90ed8: ldur            x16, [fp, #-8]
    // 0xb90edc: str             x16, [SP]
    // 0xb90ee0: mov             x1, x0
    // 0xb90ee4: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, decoration, 0x2, null]
    //     0xb90ee4: add             x4, PP, #0x52, lsl #12  ; [pp+0x523b0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "decoration", 0x2, Null]
    //     0xb90ee8: ldr             x4, [x4, #0x3b0]
    // 0xb90eec: r0 = Container()
    //     0xb90eec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb90ef0: r1 = Null
    //     0xb90ef0: mov             x1, NULL
    // 0xb90ef4: r2 = 6
    //     0xb90ef4: movz            x2, #0x6
    // 0xb90ef8: r0 = AllocateArray()
    //     0xb90ef8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb90efc: mov             x2, x0
    // 0xb90f00: ldur            x0, [fp, #-0x20]
    // 0xb90f04: stur            x2, [fp, #-8]
    // 0xb90f08: StoreField: r2->field_f = r0
    //     0xb90f08: stur            w0, [x2, #0xf]
    // 0xb90f0c: r16 = Instance_SizedBox
    //     0xb90f0c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xb90f10: ldr             x16, [x16, #0x328]
    // 0xb90f14: StoreField: r2->field_13 = r16
    //     0xb90f14: stur            w16, [x2, #0x13]
    // 0xb90f18: ldur            x0, [fp, #-0x10]
    // 0xb90f1c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb90f1c: stur            w0, [x2, #0x17]
    // 0xb90f20: r1 = <Widget>
    //     0xb90f20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb90f24: r0 = AllocateGrowableArray()
    //     0xb90f24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb90f28: mov             x1, x0
    // 0xb90f2c: ldur            x0, [fp, #-8]
    // 0xb90f30: stur            x1, [fp, #-0x10]
    // 0xb90f34: StoreField: r1->field_f = r0
    //     0xb90f34: stur            w0, [x1, #0xf]
    // 0xb90f38: r0 = 6
    //     0xb90f38: movz            x0, #0x6
    // 0xb90f3c: StoreField: r1->field_b = r0
    //     0xb90f3c: stur            w0, [x1, #0xb]
    // 0xb90f40: r0 = Column()
    //     0xb90f40: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb90f44: mov             x1, x0
    // 0xb90f48: r0 = Instance_Axis
    //     0xb90f48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb90f4c: stur            x1, [fp, #-8]
    // 0xb90f50: StoreField: r1->field_f = r0
    //     0xb90f50: stur            w0, [x1, #0xf]
    // 0xb90f54: r0 = Instance_MainAxisAlignment
    //     0xb90f54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb90f58: ldr             x0, [x0, #0xa08]
    // 0xb90f5c: StoreField: r1->field_13 = r0
    //     0xb90f5c: stur            w0, [x1, #0x13]
    // 0xb90f60: r0 = Instance_MainAxisSize
    //     0xb90f60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb90f64: ldr             x0, [x0, #0xa10]
    // 0xb90f68: ArrayStore: r1[0] = r0  ; List_4
    //     0xb90f68: stur            w0, [x1, #0x17]
    // 0xb90f6c: r0 = Instance_CrossAxisAlignment
    //     0xb90f6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb90f70: ldr             x0, [x0, #0x890]
    // 0xb90f74: StoreField: r1->field_1b = r0
    //     0xb90f74: stur            w0, [x1, #0x1b]
    // 0xb90f78: r0 = Instance_VerticalDirection
    //     0xb90f78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb90f7c: ldr             x0, [x0, #0xa20]
    // 0xb90f80: StoreField: r1->field_23 = r0
    //     0xb90f80: stur            w0, [x1, #0x23]
    // 0xb90f84: r0 = Instance_Clip
    //     0xb90f84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb90f88: ldr             x0, [x0, #0x38]
    // 0xb90f8c: StoreField: r1->field_2b = r0
    //     0xb90f8c: stur            w0, [x1, #0x2b]
    // 0xb90f90: StoreField: r1->field_2f = rZR
    //     0xb90f90: stur            xzr, [x1, #0x2f]
    // 0xb90f94: ldur            x0, [fp, #-0x10]
    // 0xb90f98: StoreField: r1->field_b = r0
    //     0xb90f98: stur            w0, [x1, #0xb]
    // 0xb90f9c: r0 = Padding()
    //     0xb90f9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb90fa0: mov             x1, x0
    // 0xb90fa4: r0 = Instance_EdgeInsets
    //     0xb90fa4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb90fa8: ldr             x0, [x0, #0x240]
    // 0xb90fac: StoreField: r1->field_f = r0
    //     0xb90fac: stur            w0, [x1, #0xf]
    // 0xb90fb0: ldur            x0, [fp, #-8]
    // 0xb90fb4: StoreField: r1->field_b = r0
    //     0xb90fb4: stur            w0, [x1, #0xb]
    // 0xb90fb8: mov             x0, x1
    // 0xb90fbc: LeaveFrame
    //     0xb90fbc: mov             SP, fp
    //     0xb90fc0: ldp             fp, lr, [SP], #0x10
    // 0xb90fc4: ret
    //     0xb90fc4: ret             
    // 0xb90fc8: r16 = "dropdownValue"
    //     0xb90fc8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52418] "dropdownValue"
    //     0xb90fcc: ldr             x16, [x16, #0x418]
    // 0xb90fd0: str             x16, [SP]
    // 0xb90fd4: r0 = _throwLocalNotInitialized()
    //     0xb90fd4: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xb90fd8: brk             #0
    // 0xb90fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90fdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90fe0: b               #0xb90984
    // 0xb90fe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb90fe4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb90fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90fe8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90fec: b               #0xb90a7c
    // 0xb90ff0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb90ff0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb90ff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb90ff4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb90ff8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb90ff8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb90ffc: b               #0xb90b50
    // 0xb91000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb91000: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb91004: b               #0xb90be4
    // 0xb91008: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb91008: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9100c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9100c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int?) {
    // ** addr: 0xb91030, size: 0x84
    // 0xb91030: EnterFrame
    //     0xb91030: stp             fp, lr, [SP, #-0x10]!
    //     0xb91034: mov             fp, SP
    // 0xb91038: AllocStack(0x10)
    //     0xb91038: sub             SP, SP, #0x10
    // 0xb9103c: SetupParameters()
    //     0xb9103c: ldr             x0, [fp, #0x18]
    //     0xb91040: ldur            w1, [x0, #0x17]
    //     0xb91044: add             x1, x1, HEAP, lsl #32
    //     0xb91048: stur            x1, [fp, #-8]
    // 0xb9104c: CheckStackOverflow
    //     0xb9104c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb91050: cmp             SP, x16
    //     0xb91054: b.ls            #0xb910ac
    // 0xb91058: r1 = 1
    //     0xb91058: movz            x1, #0x1
    // 0xb9105c: r0 = AllocateContext()
    //     0xb9105c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb91060: mov             x1, x0
    // 0xb91064: ldur            x0, [fp, #-8]
    // 0xb91068: StoreField: r1->field_b = r0
    //     0xb91068: stur            w0, [x1, #0xb]
    // 0xb9106c: ldr             x2, [fp, #0x10]
    // 0xb91070: StoreField: r1->field_f = r2
    //     0xb91070: stur            w2, [x1, #0xf]
    // 0xb91074: LoadField: r3 = r0->field_f
    //     0xb91074: ldur            w3, [x0, #0xf]
    // 0xb91078: DecompressPointer r3
    //     0xb91078: add             x3, x3, HEAP, lsl #32
    // 0xb9107c: mov             x2, x1
    // 0xb91080: stur            x3, [fp, #-0x10]
    // 0xb91084: r1 = Function '<anonymous closure>':.
    //     0xb91084: add             x1, PP, #0x55, lsl #12  ; [pp+0x552e8] AnonymousClosure: (0xb910b4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/select_quantity_picker.dart] _SelectQuantityPickerState::build (0xb90964)
    //     0xb91088: ldr             x1, [x1, #0x2e8]
    // 0xb9108c: r0 = AllocateClosure()
    //     0xb9108c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb91090: ldur            x1, [fp, #-0x10]
    // 0xb91094: mov             x2, x0
    // 0xb91098: r0 = setState()
    //     0xb91098: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9109c: r0 = Null
    //     0xb9109c: mov             x0, NULL
    // 0xb910a0: LeaveFrame
    //     0xb910a0: mov             SP, fp
    //     0xb910a4: ldp             fp, lr, [SP], #0x10
    // 0xb910a8: ret
    //     0xb910a8: ret             
    // 0xb910ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb910ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb910b0: b               #0xb91058
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb910b4, size: 0x138
    // 0xb910b4: EnterFrame
    //     0xb910b4: stp             fp, lr, [SP, #-0x10]!
    //     0xb910b8: mov             fp, SP
    // 0xb910bc: AllocStack(0x18)
    //     0xb910bc: sub             SP, SP, #0x18
    // 0xb910c0: SetupParameters()
    //     0xb910c0: ldr             x0, [fp, #0x10]
    //     0xb910c4: ldur            w1, [x0, #0x17]
    //     0xb910c8: add             x1, x1, HEAP, lsl #32
    // 0xb910cc: CheckStackOverflow
    //     0xb910cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb910d0: cmp             SP, x16
    //     0xb910d4: b.ls            #0xb911d4
    // 0xb910d8: LoadField: r2 = r1->field_f
    //     0xb910d8: ldur            w2, [x1, #0xf]
    // 0xb910dc: DecompressPointer r2
    //     0xb910dc: add             x2, x2, HEAP, lsl #32
    // 0xb910e0: cmp             w2, NULL
    // 0xb910e4: b.eq            #0xb911dc
    // 0xb910e8: LoadField: r3 = r1->field_b
    //     0xb910e8: ldur            w3, [x1, #0xb]
    // 0xb910ec: DecompressPointer r3
    //     0xb910ec: add             x3, x3, HEAP, lsl #32
    // 0xb910f0: mov             x0, x2
    // 0xb910f4: ArrayStore: r3[0] = r0  ; List_4
    //     0xb910f4: stur            w0, [x3, #0x17]
    //     0xb910f8: tbz             w0, #0, #0xb91114
    //     0xb910fc: ldurb           w16, [x3, #-1]
    //     0xb91100: ldurb           w17, [x0, #-1]
    //     0xb91104: and             x16, x17, x16, lsr #2
    //     0xb91108: tst             x16, HEAP, lsr #32
    //     0xb9110c: b.eq            #0xb91114
    //     0xb91110: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb91114: LoadField: r0 = r3->field_f
    //     0xb91114: ldur            w0, [x3, #0xf]
    // 0xb91118: DecompressPointer r0
    //     0xb91118: add             x0, x0, HEAP, lsl #32
    // 0xb9111c: LoadField: r1 = r0->field_b
    //     0xb9111c: ldur            w1, [x0, #0xb]
    // 0xb91120: DecompressPointer r1
    //     0xb91120: add             x1, x1, HEAP, lsl #32
    // 0xb91124: cmp             w1, NULL
    // 0xb91128: b.eq            #0xb911e0
    // 0xb9112c: LoadField: r0 = r3->field_1b
    //     0xb9112c: ldur            w0, [x3, #0x1b]
    // 0xb91130: DecompressPointer r0
    //     0xb91130: add             x0, x0, HEAP, lsl #32
    // 0xb91134: LoadField: r3 = r0->field_b
    //     0xb91134: ldur            w3, [x0, #0xb]
    // 0xb91138: r4 = LoadInt32Instr(r3)
    //     0xb91138: sbfx            x4, x3, #1, #0x1f
    // 0xb9113c: LoadField: r3 = r0->field_f
    //     0xb9113c: ldur            w3, [x0, #0xf]
    // 0xb91140: DecompressPointer r3
    //     0xb91140: add             x3, x3, HEAP, lsl #32
    // 0xb91144: r0 = 0
    //     0xb91144: movz            x0, #0
    // 0xb91148: CheckStackOverflow
    //     0xb91148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9114c: cmp             SP, x16
    //     0xb91150: b.ls            #0xb911e4
    // 0xb91154: cmp             x0, x4
    // 0xb91158: b.ge            #0xb91194
    // 0xb9115c: ArrayLoad: r5 = r3[r0]  ; Unknown_4
    //     0xb9115c: add             x16, x3, x0, lsl #2
    //     0xb91160: ldur            w5, [x16, #0xf]
    // 0xb91164: DecompressPointer r5
    //     0xb91164: add             x5, x5, HEAP, lsl #32
    // 0xb91168: r6 = LoadInt32Instr(r2)
    //     0xb91168: sbfx            x6, x2, #1, #0x1f
    //     0xb9116c: tbz             w2, #0, #0xb91174
    //     0xb91170: ldur            x6, [x2, #7]
    // 0xb91174: r7 = LoadInt32Instr(r5)
    //     0xb91174: sbfx            x7, x5, #1, #0x1f
    //     0xb91178: tbz             w5, #0, #0xb91180
    //     0xb9117c: ldur            x7, [x5, #7]
    // 0xb91180: cmp             x7, x6
    // 0xb91184: b.eq            #0xb91198
    // 0xb91188: add             x5, x0, #1
    // 0xb9118c: mov             x0, x5
    // 0xb91190: b               #0xb91148
    // 0xb91194: r0 = -1
    //     0xb91194: movn            x0, #0
    // 0xb91198: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb91198: ldur            w3, [x1, #0x17]
    // 0xb9119c: DecompressPointer r3
    //     0xb9119c: add             x3, x3, HEAP, lsl #32
    // 0xb911a0: lsl             x1, x0, #1
    // 0xb911a4: stp             x2, x3, [SP, #8]
    // 0xb911a8: str             x1, [SP]
    // 0xb911ac: r4 = 0
    //     0xb911ac: movz            x4, #0
    // 0xb911b0: ldr             x0, [SP, #0x10]
    // 0xb911b4: r16 = UnlinkedCall_0x613b5c
    //     0xb911b4: add             x16, PP, #0x55, lsl #12  ; [pp+0x552f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb911b8: add             x16, x16, #0x2f0
    // 0xb911bc: ldp             x5, lr, [x16]
    // 0xb911c0: blr             lr
    // 0xb911c4: r0 = Null
    //     0xb911c4: mov             x0, NULL
    // 0xb911c8: LeaveFrame
    //     0xb911c8: mov             SP, fp
    //     0xb911cc: ldp             fp, lr, [SP], #0x10
    // 0xb911d0: ret
    //     0xb911d0: ret             
    // 0xb911d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb911d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb911d8: b               #0xb910d8
    // 0xb911dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb911dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb911e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb911e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb911e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb911e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb911e8: b               #0xb91154
  }
}

// class id: 4048, size: 0x1c, field offset: 0xc
class SelectQuantityPicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fab8, size: 0x50
    // 0xc7fab8: EnterFrame
    //     0xc7fab8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fabc: mov             fp, SP
    // 0xc7fac0: CheckStackOverflow
    //     0xc7fac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fac4: cmp             SP, x16
    //     0xc7fac8: b.ls            #0xc7fb00
    // 0xc7facc: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc7facc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7fad0: ldr             x0, [x0]
    //     0xc7fad4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7fad8: cmp             w0, w16
    //     0xc7fadc: b.ne            #0xc7fae8
    //     0xc7fae0: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc7fae4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7fae8: r1 = <SelectQuantityPicker>
    //     0xc7fae8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48730] TypeArguments: <SelectQuantityPicker>
    //     0xc7faec: ldr             x1, [x1, #0x730]
    // 0xc7faf0: r0 = _SelectQuantityPickerState()
    //     0xc7faf0: bl              #0xc7fb08  ; Allocate_SelectQuantityPickerStateStub -> _SelectQuantityPickerState (size=0x14)
    // 0xc7faf4: LeaveFrame
    //     0xc7faf4: mov             SP, fp
    //     0xc7faf8: ldp             fp, lr, [SP], #0x10
    // 0xc7fafc: ret
    //     0xc7fafc: ret             
    // 0xc7fb00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fb00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fb04: b               #0xc7facc
  }
}
