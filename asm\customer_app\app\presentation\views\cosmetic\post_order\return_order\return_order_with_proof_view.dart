// lib: , url: package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart

// class id: 1049305, size: 0x8
class :: {
}

// class id: 4589, size: 0x14, field offset: 0x14
//   const constructor, 
class ReturnOrderWithProofView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x135542c, size: 0x64
    // 0x135542c: EnterFrame
    //     0x135542c: stp             fp, lr, [SP, #-0x10]!
    //     0x1355430: mov             fp, SP
    // 0x1355434: AllocStack(0x18)
    //     0x1355434: sub             SP, SP, #0x18
    // 0x1355438: SetupParameters(ReturnOrderWithProofView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1355438: stur            x1, [fp, #-8]
    //     0x135543c: stur            x2, [fp, #-0x10]
    // 0x1355440: r1 = 2
    //     0x1355440: movz            x1, #0x2
    // 0x1355444: r0 = AllocateContext()
    //     0x1355444: bl              #0x16f6108  ; AllocateContextStub
    // 0x1355448: mov             x1, x0
    // 0x135544c: ldur            x0, [fp, #-8]
    // 0x1355450: stur            x1, [fp, #-0x18]
    // 0x1355454: StoreField: r1->field_f = r0
    //     0x1355454: stur            w0, [x1, #0xf]
    // 0x1355458: ldur            x0, [fp, #-0x10]
    // 0x135545c: StoreField: r1->field_13 = r0
    //     0x135545c: stur            w0, [x1, #0x13]
    // 0x1355460: r0 = Obx()
    //     0x1355460: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1355464: ldur            x2, [fp, #-0x18]
    // 0x1355468: r1 = Function '<anonymous closure>':.
    //     0x1355468: add             x1, PP, #0x42, lsl #12  ; [pp+0x425c0] AnonymousClosure: (0x1355490), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::bottomNavigationBar (0x135542c)
    //     0x135546c: ldr             x1, [x1, #0x5c0]
    // 0x1355470: stur            x0, [fp, #-8]
    // 0x1355474: r0 = AllocateClosure()
    //     0x1355474: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1355478: mov             x1, x0
    // 0x135547c: ldur            x0, [fp, #-8]
    // 0x1355480: StoreField: r0->field_b = r1
    //     0x1355480: stur            w1, [x0, #0xb]
    // 0x1355484: LeaveFrame
    //     0x1355484: mov             SP, fp
    //     0x1355488: ldp             fp, lr, [SP], #0x10
    // 0x135548c: ret
    //     0x135548c: ret             
  }
  [closure] Container <anonymous closure>(dynamic) {
    // ** addr: 0x1355490, size: 0xdb0
    // 0x1355490: EnterFrame
    //     0x1355490: stp             fp, lr, [SP, #-0x10]!
    //     0x1355494: mov             fp, SP
    // 0x1355498: AllocStack(0x70)
    //     0x1355498: sub             SP, SP, #0x70
    // 0x135549c: SetupParameters()
    //     0x135549c: ldr             x0, [fp, #0x10]
    //     0x13554a0: ldur            w2, [x0, #0x17]
    //     0x13554a4: add             x2, x2, HEAP, lsl #32
    //     0x13554a8: stur            x2, [fp, #-8]
    // 0x13554ac: CheckStackOverflow
    //     0x13554ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13554b0: cmp             SP, x16
    //     0x13554b4: b.ls            #0x1356220
    // 0x13554b8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13554b8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13554bc: ldr             x0, [x0, #0x1c80]
    //     0x13554c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13554c4: cmp             w0, w16
    //     0x13554c8: b.ne            #0x13554d4
    //     0x13554cc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13554d0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13554d4: r0 = GetNavigation.size()
    //     0x13554d4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x13554d8: LoadField: d0 = r0->field_f
    //     0x13554d8: ldur            d0, [x0, #0xf]
    // 0x13554dc: d1 = 0.170000
    //     0x13554dc: add             x17, PP, #0x33, lsl #12  ; [pp+0x33f10] IMM: double(0.17) from 0x3fc5c28f5c28f5c3
    //     0x13554e0: ldr             d1, [x17, #0xf10]
    // 0x13554e4: fmul            d2, d0, d1
    // 0x13554e8: stur            d2, [fp, #-0x50]
    // 0x13554ec: r1 = _ConstMap len:11
    //     0x13554ec: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x13554f0: ldr             x1, [x1, #0xc28]
    // 0x13554f4: r2 = 8
    //     0x13554f4: movz            x2, #0x8
    // 0x13554f8: r0 = []()
    //     0x13554f8: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x13554fc: stur            x0, [fp, #-0x10]
    // 0x1355500: r0 = BoxDecoration()
    //     0x1355500: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1355504: mov             x2, x0
    // 0x1355508: r0 = Instance_Color
    //     0x1355508: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x135550c: stur            x2, [fp, #-0x18]
    // 0x1355510: StoreField: r2->field_7 = r0
    //     0x1355510: stur            w0, [x2, #7]
    // 0x1355514: r0 = Instance_BorderRadius
    //     0x1355514: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] Obj!BorderRadius@d5a201
    //     0x1355518: ldr             x0, [x0, #0xbf8]
    // 0x135551c: StoreField: r2->field_13 = r0
    //     0x135551c: stur            w0, [x2, #0x13]
    // 0x1355520: ldur            x0, [fp, #-0x10]
    // 0x1355524: ArrayStore: r2[0] = r0  ; List_4
    //     0x1355524: stur            w0, [x2, #0x17]
    // 0x1355528: r0 = Instance_BoxShape
    //     0x1355528: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x135552c: ldr             x0, [x0, #0x80]
    // 0x1355530: StoreField: r2->field_23 = r0
    //     0x1355530: stur            w0, [x2, #0x23]
    // 0x1355534: ldur            x3, [fp, #-8]
    // 0x1355538: LoadField: r1 = r3->field_f
    //     0x1355538: ldur            w1, [x3, #0xf]
    // 0x135553c: DecompressPointer r1
    //     0x135553c: add             x1, x1, HEAP, lsl #32
    // 0x1355540: r0 = controller()
    //     0x1355540: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355544: LoadField: r1 = r0->field_6b
    //     0x1355544: ldur            w1, [x0, #0x6b]
    // 0x1355548: DecompressPointer r1
    //     0x1355548: add             x1, x1, HEAP, lsl #32
    // 0x135554c: r0 = value()
    //     0x135554c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355550: cmp             w0, NULL
    // 0x1355554: b.eq            #0x135555c
    // 0x1355558: tbz             w0, #4, #0x1355568
    // 0x135555c: r0 = Instance_IconData
    //     0x135555c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c30] Obj!IconData@d55461
    //     0x1355560: ldr             x0, [x0, #0xc30]
    // 0x1355564: b               #0x1355570
    // 0x1355568: r0 = Instance_IconData
    //     0x1355568: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c38] Obj!IconData@d55481
    //     0x135556c: ldr             x0, [x0, #0xc38]
    // 0x1355570: ldur            x2, [fp, #-8]
    // 0x1355574: stur            x0, [fp, #-0x10]
    // 0x1355578: r0 = Icon()
    //     0x1355578: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x135557c: mov             x1, x0
    // 0x1355580: ldur            x0, [fp, #-0x10]
    // 0x1355584: stur            x1, [fp, #-0x20]
    // 0x1355588: StoreField: r1->field_b = r0
    //     0x1355588: stur            w0, [x1, #0xb]
    // 0x135558c: r0 = GetNavigation.size()
    //     0x135558c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1355590: LoadField: d0 = r0->field_7
    //     0x1355590: ldur            d0, [x0, #7]
    // 0x1355594: d1 = 0.800000
    //     0x1355594: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x1355598: ldr             d1, [x17, #0xb28]
    // 0x135559c: fmul            d2, d0, d1
    // 0x13555a0: stur            d2, [fp, #-0x58]
    // 0x13555a4: r0 = BoxConstraints()
    //     0x13555a4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x13555a8: stur            x0, [fp, #-0x10]
    // 0x13555ac: StoreField: r0->field_7 = rZR
    //     0x13555ac: stur            xzr, [x0, #7]
    // 0x13555b0: ldur            d0, [fp, #-0x58]
    // 0x13555b4: StoreField: r0->field_f = d0
    //     0x13555b4: stur            d0, [x0, #0xf]
    // 0x13555b8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13555b8: stur            xzr, [x0, #0x17]
    // 0x13555bc: d0 = inf
    //     0x13555bc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x13555c0: StoreField: r0->field_1f = d0
    //     0x13555c0: stur            d0, [x0, #0x1f]
    // 0x13555c4: ldur            x2, [fp, #-8]
    // 0x13555c8: LoadField: r1 = r2->field_f
    //     0x13555c8: ldur            w1, [x2, #0xf]
    // 0x13555cc: DecompressPointer r1
    //     0x13555cc: add             x1, x1, HEAP, lsl #32
    // 0x13555d0: r0 = controller()
    //     0x13555d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13555d4: LoadField: r1 = r0->field_57
    //     0x13555d4: ldur            w1, [x0, #0x57]
    // 0x13555d8: DecompressPointer r1
    //     0x13555d8: add             x1, x1, HEAP, lsl #32
    // 0x13555dc: r0 = value()
    //     0x13555dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13555e0: cmp             w0, NULL
    // 0x13555e4: b.ne            #0x13555f0
    // 0x13555e8: r0 = Null
    //     0x13555e8: mov             x0, NULL
    // 0x13555ec: b               #0x13555fc
    // 0x13555f0: LoadField: r1 = r0->field_1f
    //     0x13555f0: ldur            w1, [x0, #0x1f]
    // 0x13555f4: DecompressPointer r1
    //     0x13555f4: add             x1, x1, HEAP, lsl #32
    // 0x13555f8: mov             x0, x1
    // 0x13555fc: cmp             w0, NULL
    // 0x1355600: b.ne            #0x1355608
    // 0x1355604: r0 = ""
    //     0x1355604: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1355608: ldur            x2, [fp, #-8]
    // 0x135560c: stur            x0, [fp, #-0x28]
    // 0x1355610: LoadField: r1 = r2->field_13
    //     0x1355610: ldur            w1, [x2, #0x13]
    // 0x1355614: DecompressPointer r1
    //     0x1355614: add             x1, x1, HEAP, lsl #32
    // 0x1355618: r0 = of()
    //     0x1355618: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x135561c: LoadField: r1 = r0->field_87
    //     0x135561c: ldur            w1, [x0, #0x87]
    // 0x1355620: DecompressPointer r1
    //     0x1355620: add             x1, x1, HEAP, lsl #32
    // 0x1355624: LoadField: r0 = r1->field_33
    //     0x1355624: ldur            w0, [x1, #0x33]
    // 0x1355628: DecompressPointer r0
    //     0x1355628: add             x0, x0, HEAP, lsl #32
    // 0x135562c: cmp             w0, NULL
    // 0x1355630: b.ne            #0x135563c
    // 0x1355634: r4 = Null
    //     0x1355634: mov             x4, NULL
    // 0x1355638: b               #0x135565c
    // 0x135563c: r16 = 10.000000
    //     0x135563c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x1355640: r30 = Instance_Color
    //     0x1355640: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1355644: stp             lr, x16, [SP]
    // 0x1355648: mov             x1, x0
    // 0x135564c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x135564c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1355650: ldr             x4, [x4, #0xaa0]
    // 0x1355654: r0 = copyWith()
    //     0x1355654: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1355658: mov             x4, x0
    // 0x135565c: ldur            x2, [fp, #-8]
    // 0x1355660: ldur            x3, [fp, #-0x20]
    // 0x1355664: ldur            x1, [fp, #-0x10]
    // 0x1355668: ldur            x0, [fp, #-0x28]
    // 0x135566c: stur            x4, [fp, #-0x30]
    // 0x1355670: r0 = Text()
    //     0x1355670: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1355674: mov             x1, x0
    // 0x1355678: ldur            x0, [fp, #-0x28]
    // 0x135567c: stur            x1, [fp, #-0x38]
    // 0x1355680: StoreField: r1->field_b = r0
    //     0x1355680: stur            w0, [x1, #0xb]
    // 0x1355684: ldur            x0, [fp, #-0x30]
    // 0x1355688: StoreField: r1->field_13 = r0
    //     0x1355688: stur            w0, [x1, #0x13]
    // 0x135568c: r0 = ConstrainedBox()
    //     0x135568c: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x1355690: mov             x1, x0
    // 0x1355694: ldur            x0, [fp, #-0x10]
    // 0x1355698: stur            x1, [fp, #-0x28]
    // 0x135569c: StoreField: r1->field_f = r0
    //     0x135569c: stur            w0, [x1, #0xf]
    // 0x13556a0: ldur            x0, [fp, #-0x38]
    // 0x13556a4: StoreField: r1->field_b = r0
    //     0x13556a4: stur            w0, [x1, #0xb]
    // 0x13556a8: r0 = Padding()
    //     0x13556a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13556ac: mov             x3, x0
    // 0x13556b0: r0 = Instance_EdgeInsets
    //     0x13556b0: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x13556b4: ldr             x0, [x0, #0xc40]
    // 0x13556b8: stur            x3, [fp, #-0x10]
    // 0x13556bc: StoreField: r3->field_f = r0
    //     0x13556bc: stur            w0, [x3, #0xf]
    // 0x13556c0: ldur            x0, [fp, #-0x28]
    // 0x13556c4: StoreField: r3->field_b = r0
    //     0x13556c4: stur            w0, [x3, #0xb]
    // 0x13556c8: r1 = Null
    //     0x13556c8: mov             x1, NULL
    // 0x13556cc: r2 = 4
    //     0x13556cc: movz            x2, #0x4
    // 0x13556d0: r0 = AllocateArray()
    //     0x13556d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13556d4: mov             x2, x0
    // 0x13556d8: ldur            x0, [fp, #-0x20]
    // 0x13556dc: stur            x2, [fp, #-0x28]
    // 0x13556e0: StoreField: r2->field_f = r0
    //     0x13556e0: stur            w0, [x2, #0xf]
    // 0x13556e4: ldur            x0, [fp, #-0x10]
    // 0x13556e8: StoreField: r2->field_13 = r0
    //     0x13556e8: stur            w0, [x2, #0x13]
    // 0x13556ec: r1 = <Widget>
    //     0x13556ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13556f0: r0 = AllocateGrowableArray()
    //     0x13556f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13556f4: mov             x1, x0
    // 0x13556f8: ldur            x0, [fp, #-0x28]
    // 0x13556fc: stur            x1, [fp, #-0x10]
    // 0x1355700: StoreField: r1->field_f = r0
    //     0x1355700: stur            w0, [x1, #0xf]
    // 0x1355704: r2 = 4
    //     0x1355704: movz            x2, #0x4
    // 0x1355708: StoreField: r1->field_b = r2
    //     0x1355708: stur            w2, [x1, #0xb]
    // 0x135570c: r0 = Row()
    //     0x135570c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1355710: mov             x1, x0
    // 0x1355714: r0 = Instance_Axis
    //     0x1355714: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1355718: stur            x1, [fp, #-0x20]
    // 0x135571c: StoreField: r1->field_f = r0
    //     0x135571c: stur            w0, [x1, #0xf]
    // 0x1355720: r2 = Instance_MainAxisAlignment
    //     0x1355720: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1355724: ldr             x2, [x2, #0xa08]
    // 0x1355728: StoreField: r1->field_13 = r2
    //     0x1355728: stur            w2, [x1, #0x13]
    // 0x135572c: r3 = Instance_MainAxisSize
    //     0x135572c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1355730: ldr             x3, [x3, #0xa10]
    // 0x1355734: ArrayStore: r1[0] = r3  ; List_4
    //     0x1355734: stur            w3, [x1, #0x17]
    // 0x1355738: r4 = Instance_CrossAxisAlignment
    //     0x1355738: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x135573c: ldr             x4, [x4, #0xa18]
    // 0x1355740: StoreField: r1->field_1b = r4
    //     0x1355740: stur            w4, [x1, #0x1b]
    // 0x1355744: r5 = Instance_VerticalDirection
    //     0x1355744: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1355748: ldr             x5, [x5, #0xa20]
    // 0x135574c: StoreField: r1->field_23 = r5
    //     0x135574c: stur            w5, [x1, #0x23]
    // 0x1355750: r6 = Instance_Clip
    //     0x1355750: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1355754: ldr             x6, [x6, #0x38]
    // 0x1355758: StoreField: r1->field_2b = r6
    //     0x1355758: stur            w6, [x1, #0x2b]
    // 0x135575c: StoreField: r1->field_2f = rZR
    //     0x135575c: stur            xzr, [x1, #0x2f]
    // 0x1355760: ldur            x7, [fp, #-0x10]
    // 0x1355764: StoreField: r1->field_b = r7
    //     0x1355764: stur            w7, [x1, #0xb]
    // 0x1355768: r0 = Padding()
    //     0x1355768: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x135576c: mov             x1, x0
    // 0x1355770: r0 = Instance_EdgeInsets
    //     0x1355770: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x1355774: ldr             x0, [x0, #0x868]
    // 0x1355778: stur            x1, [fp, #-0x10]
    // 0x135577c: StoreField: r1->field_f = r0
    //     0x135577c: stur            w0, [x1, #0xf]
    // 0x1355780: ldur            x0, [fp, #-0x20]
    // 0x1355784: StoreField: r1->field_b = r0
    //     0x1355784: stur            w0, [x1, #0xb]
    // 0x1355788: r0 = InkWell()
    //     0x1355788: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x135578c: mov             x3, x0
    // 0x1355790: ldur            x0, [fp, #-0x10]
    // 0x1355794: stur            x3, [fp, #-0x20]
    // 0x1355798: StoreField: r3->field_b = r0
    //     0x1355798: stur            w0, [x3, #0xb]
    // 0x135579c: ldur            x2, [fp, #-8]
    // 0x13557a0: r1 = Function '<anonymous closure>':.
    //     0x13557a0: add             x1, PP, #0x42, lsl #12  ; [pp+0x425c8] AnonymousClosure: (0x13304b8), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::bottomNavigationBar (0x136a000)
    //     0x13557a4: ldr             x1, [x1, #0x5c8]
    // 0x13557a8: r0 = AllocateClosure()
    //     0x13557a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13557ac: mov             x1, x0
    // 0x13557b0: ldur            x0, [fp, #-0x20]
    // 0x13557b4: StoreField: r0->field_f = r1
    //     0x13557b4: stur            w1, [x0, #0xf]
    // 0x13557b8: r2 = true
    //     0x13557b8: add             x2, NULL, #0x20  ; true
    // 0x13557bc: StoreField: r0->field_43 = r2
    //     0x13557bc: stur            w2, [x0, #0x43]
    // 0x13557c0: r1 = Instance_BoxShape
    //     0x13557c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13557c4: ldr             x1, [x1, #0x80]
    // 0x13557c8: StoreField: r0->field_47 = r1
    //     0x13557c8: stur            w1, [x0, #0x47]
    // 0x13557cc: StoreField: r0->field_6f = r2
    //     0x13557cc: stur            w2, [x0, #0x6f]
    // 0x13557d0: r3 = false
    //     0x13557d0: add             x3, NULL, #0x30  ; false
    // 0x13557d4: StoreField: r0->field_73 = r3
    //     0x13557d4: stur            w3, [x0, #0x73]
    // 0x13557d8: StoreField: r0->field_83 = r2
    //     0x13557d8: stur            w2, [x0, #0x83]
    // 0x13557dc: StoreField: r0->field_7b = r3
    //     0x13557dc: stur            w3, [x0, #0x7b]
    // 0x13557e0: ldur            x4, [fp, #-8]
    // 0x13557e4: LoadField: r1 = r4->field_f
    //     0x13557e4: ldur            w1, [x4, #0xf]
    // 0x13557e8: DecompressPointer r1
    //     0x13557e8: add             x1, x1, HEAP, lsl #32
    // 0x13557ec: r0 = controller()
    //     0x13557ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13557f0: LoadField: r1 = r0->field_57
    //     0x13557f0: ldur            w1, [x0, #0x57]
    // 0x13557f4: DecompressPointer r1
    //     0x13557f4: add             x1, x1, HEAP, lsl #32
    // 0x13557f8: r0 = value()
    //     0x13557f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13557fc: cmp             w0, NULL
    // 0x1355800: b.ne            #0x135580c
    // 0x1355804: r0 = Null
    //     0x1355804: mov             x0, NULL
    // 0x1355808: b               #0x135582c
    // 0x135580c: LoadField: r1 = r0->field_b
    //     0x135580c: ldur            w1, [x0, #0xb]
    // 0x1355810: DecompressPointer r1
    //     0x1355810: add             x1, x1, HEAP, lsl #32
    // 0x1355814: cmp             w1, NULL
    // 0x1355818: b.ne            #0x1355824
    // 0x135581c: r0 = Null
    //     0x135581c: mov             x0, NULL
    // 0x1355820: b               #0x135582c
    // 0x1355824: LoadField: r0 = r1->field_b
    //     0x1355824: ldur            w0, [x1, #0xb]
    // 0x1355828: DecompressPointer r0
    //     0x1355828: add             x0, x0, HEAP, lsl #32
    // 0x135582c: ldur            x2, [fp, #-8]
    // 0x1355830: cbnz            w0, #0x135583c
    // 0x1355834: r3 = false
    //     0x1355834: add             x3, NULL, #0x30  ; false
    // 0x1355838: b               #0x1355840
    // 0x135583c: r3 = true
    //     0x135583c: add             x3, NULL, #0x20  ; true
    // 0x1355840: stur            x3, [fp, #-0x10]
    // 0x1355844: LoadField: r1 = r2->field_f
    //     0x1355844: ldur            w1, [x2, #0xf]
    // 0x1355848: DecompressPointer r1
    //     0x1355848: add             x1, x1, HEAP, lsl #32
    // 0x135584c: r0 = controller()
    //     0x135584c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355850: LoadField: r1 = r0->field_57
    //     0x1355850: ldur            w1, [x0, #0x57]
    // 0x1355854: DecompressPointer r1
    //     0x1355854: add             x1, x1, HEAP, lsl #32
    // 0x1355858: r0 = value()
    //     0x1355858: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x135585c: cmp             w0, NULL
    // 0x1355860: b.ne            #0x135586c
    // 0x1355864: r0 = Null
    //     0x1355864: mov             x0, NULL
    // 0x1355868: b               #0x135588c
    // 0x135586c: LoadField: r1 = r0->field_33
    //     0x135586c: ldur            w1, [x0, #0x33]
    // 0x1355870: DecompressPointer r1
    //     0x1355870: add             x1, x1, HEAP, lsl #32
    // 0x1355874: cmp             w1, NULL
    // 0x1355878: b.ne            #0x1355884
    // 0x135587c: r0 = Null
    //     0x135587c: mov             x0, NULL
    // 0x1355880: b               #0x135588c
    // 0x1355884: LoadField: r0 = r1->field_2b
    //     0x1355884: ldur            w0, [x1, #0x2b]
    // 0x1355888: DecompressPointer r0
    //     0x1355888: add             x0, x0, HEAP, lsl #32
    // 0x135588c: cmp             w0, NULL
    // 0x1355890: b.ne            #0x1355898
    // 0x1355894: r0 = ""
    //     0x1355894: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1355898: ldur            x2, [fp, #-8]
    // 0x135589c: stur            x0, [fp, #-0x28]
    // 0x13558a0: LoadField: r1 = r2->field_13
    //     0x13558a0: ldur            w1, [x2, #0x13]
    // 0x13558a4: DecompressPointer r1
    //     0x13558a4: add             x1, x1, HEAP, lsl #32
    // 0x13558a8: r0 = of()
    //     0x13558a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13558ac: LoadField: r1 = r0->field_87
    //     0x13558ac: ldur            w1, [x0, #0x87]
    // 0x13558b0: DecompressPointer r1
    //     0x13558b0: add             x1, x1, HEAP, lsl #32
    // 0x13558b4: LoadField: r0 = r1->field_33
    //     0x13558b4: ldur            w0, [x1, #0x33]
    // 0x13558b8: DecompressPointer r0
    //     0x13558b8: add             x0, x0, HEAP, lsl #32
    // 0x13558bc: cmp             w0, NULL
    // 0x13558c0: b.ne            #0x13558cc
    // 0x13558c4: r1 = Null
    //     0x13558c4: mov             x1, NULL
    // 0x13558c8: b               #0x13558f0
    // 0x13558cc: r16 = 16.000000
    //     0x13558cc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x13558d0: ldr             x16, [x16, #0x188]
    // 0x13558d4: r30 = Instance_Color
    //     0x13558d4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13558d8: stp             lr, x16, [SP]
    // 0x13558dc: mov             x1, x0
    // 0x13558e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13558e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13558e4: ldr             x4, [x4, #0xaa0]
    // 0x13558e8: r0 = copyWith()
    //     0x13558e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13558ec: mov             x1, x0
    // 0x13558f0: ldur            x2, [fp, #-8]
    // 0x13558f4: ldur            x0, [fp, #-0x28]
    // 0x13558f8: stur            x1, [fp, #-0x30]
    // 0x13558fc: r0 = Text()
    //     0x13558fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1355900: mov             x1, x0
    // 0x1355904: ldur            x0, [fp, #-0x28]
    // 0x1355908: stur            x1, [fp, #-0x38]
    // 0x135590c: StoreField: r1->field_b = r0
    //     0x135590c: stur            w0, [x1, #0xb]
    // 0x1355910: ldur            x0, [fp, #-0x30]
    // 0x1355914: StoreField: r1->field_13 = r0
    //     0x1355914: stur            w0, [x1, #0x13]
    // 0x1355918: r0 = Padding()
    //     0x1355918: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x135591c: mov             x2, x0
    // 0x1355920: r0 = Instance_EdgeInsets
    //     0x1355920: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c50] Obj!EdgeInsets@d59541
    //     0x1355924: ldr             x0, [x0, #0xc50]
    // 0x1355928: stur            x2, [fp, #-0x28]
    // 0x135592c: StoreField: r2->field_f = r0
    //     0x135592c: stur            w0, [x2, #0xf]
    // 0x1355930: ldur            x0, [fp, #-0x38]
    // 0x1355934: StoreField: r2->field_b = r0
    //     0x1355934: stur            w0, [x2, #0xb]
    // 0x1355938: ldur            x0, [fp, #-8]
    // 0x135593c: LoadField: r1 = r0->field_f
    //     0x135593c: ldur            w1, [x0, #0xf]
    // 0x1355940: DecompressPointer r1
    //     0x1355940: add             x1, x1, HEAP, lsl #32
    // 0x1355944: r0 = controller()
    //     0x1355944: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355948: LoadField: r1 = r0->field_57
    //     0x1355948: ldur            w1, [x0, #0x57]
    // 0x135594c: DecompressPointer r1
    //     0x135594c: add             x1, x1, HEAP, lsl #32
    // 0x1355950: r0 = value()
    //     0x1355950: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355954: cmp             w0, NULL
    // 0x1355958: b.ne            #0x1355964
    // 0x135595c: r0 = Null
    //     0x135595c: mov             x0, NULL
    // 0x1355960: b               #0x1355984
    // 0x1355964: LoadField: r1 = r0->field_b
    //     0x1355964: ldur            w1, [x0, #0xb]
    // 0x1355968: DecompressPointer r1
    //     0x1355968: add             x1, x1, HEAP, lsl #32
    // 0x135596c: cmp             w1, NULL
    // 0x1355970: b.ne            #0x135597c
    // 0x1355974: r0 = Null
    //     0x1355974: mov             x0, NULL
    // 0x1355978: b               #0x1355984
    // 0x135597c: LoadField: r0 = r1->field_7
    //     0x135597c: ldur            w0, [x1, #7]
    // 0x1355980: DecompressPointer r0
    //     0x1355980: add             x0, x0, HEAP, lsl #32
    // 0x1355984: cmp             w0, NULL
    // 0x1355988: b.ne            #0x1355994
    // 0x135598c: r4 = ""
    //     0x135598c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1355990: b               #0x1355998
    // 0x1355994: mov             x4, x0
    // 0x1355998: ldur            x2, [fp, #-8]
    // 0x135599c: ldur            x0, [fp, #-0x28]
    // 0x13559a0: ldur            x3, [fp, #-0x10]
    // 0x13559a4: stur            x4, [fp, #-0x30]
    // 0x13559a8: LoadField: r1 = r2->field_13
    //     0x13559a8: ldur            w1, [x2, #0x13]
    // 0x13559ac: DecompressPointer r1
    //     0x13559ac: add             x1, x1, HEAP, lsl #32
    // 0x13559b0: r0 = of()
    //     0x13559b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13559b4: LoadField: r1 = r0->field_87
    //     0x13559b4: ldur            w1, [x0, #0x87]
    // 0x13559b8: DecompressPointer r1
    //     0x13559b8: add             x1, x1, HEAP, lsl #32
    // 0x13559bc: LoadField: r0 = r1->field_7
    //     0x13559bc: ldur            w0, [x1, #7]
    // 0x13559c0: DecompressPointer r0
    //     0x13559c0: add             x0, x0, HEAP, lsl #32
    // 0x13559c4: r16 = 16.000000
    //     0x13559c4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x13559c8: ldr             x16, [x16, #0x188]
    // 0x13559cc: r30 = Instance_Color
    //     0x13559cc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13559d0: stp             lr, x16, [SP]
    // 0x13559d4: mov             x1, x0
    // 0x13559d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13559d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13559dc: ldr             x4, [x4, #0xaa0]
    // 0x13559e0: r0 = copyWith()
    //     0x13559e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13559e4: stur            x0, [fp, #-0x38]
    // 0x13559e8: r0 = Text()
    //     0x13559e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13559ec: mov             x3, x0
    // 0x13559f0: ldur            x0, [fp, #-0x30]
    // 0x13559f4: stur            x3, [fp, #-0x40]
    // 0x13559f8: StoreField: r3->field_b = r0
    //     0x13559f8: stur            w0, [x3, #0xb]
    // 0x13559fc: ldur            x0, [fp, #-0x38]
    // 0x1355a00: StoreField: r3->field_13 = r0
    //     0x1355a00: stur            w0, [x3, #0x13]
    // 0x1355a04: r1 = Null
    //     0x1355a04: mov             x1, NULL
    // 0x1355a08: r2 = 4
    //     0x1355a08: movz            x2, #0x4
    // 0x1355a0c: r0 = AllocateArray()
    //     0x1355a0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1355a10: mov             x2, x0
    // 0x1355a14: ldur            x0, [fp, #-0x28]
    // 0x1355a18: stur            x2, [fp, #-0x30]
    // 0x1355a1c: StoreField: r2->field_f = r0
    //     0x1355a1c: stur            w0, [x2, #0xf]
    // 0x1355a20: ldur            x0, [fp, #-0x40]
    // 0x1355a24: StoreField: r2->field_13 = r0
    //     0x1355a24: stur            w0, [x2, #0x13]
    // 0x1355a28: r1 = <Widget>
    //     0x1355a28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1355a2c: r0 = AllocateGrowableArray()
    //     0x1355a2c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1355a30: mov             x1, x0
    // 0x1355a34: ldur            x0, [fp, #-0x30]
    // 0x1355a38: stur            x1, [fp, #-0x28]
    // 0x1355a3c: StoreField: r1->field_f = r0
    //     0x1355a3c: stur            w0, [x1, #0xf]
    // 0x1355a40: r2 = 4
    //     0x1355a40: movz            x2, #0x4
    // 0x1355a44: StoreField: r1->field_b = r2
    //     0x1355a44: stur            w2, [x1, #0xb]
    // 0x1355a48: r0 = Column()
    //     0x1355a48: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1355a4c: mov             x1, x0
    // 0x1355a50: r0 = Instance_Axis
    //     0x1355a50: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1355a54: stur            x1, [fp, #-0x30]
    // 0x1355a58: StoreField: r1->field_f = r0
    //     0x1355a58: stur            w0, [x1, #0xf]
    // 0x1355a5c: r2 = Instance_MainAxisAlignment
    //     0x1355a5c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1355a60: ldr             x2, [x2, #0xa08]
    // 0x1355a64: StoreField: r1->field_13 = r2
    //     0x1355a64: stur            w2, [x1, #0x13]
    // 0x1355a68: r3 = Instance_MainAxisSize
    //     0x1355a68: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1355a6c: ldr             x3, [x3, #0xa10]
    // 0x1355a70: ArrayStore: r1[0] = r3  ; List_4
    //     0x1355a70: stur            w3, [x1, #0x17]
    // 0x1355a74: r4 = Instance_CrossAxisAlignment
    //     0x1355a74: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1355a78: ldr             x4, [x4, #0x890]
    // 0x1355a7c: StoreField: r1->field_1b = r4
    //     0x1355a7c: stur            w4, [x1, #0x1b]
    // 0x1355a80: r4 = Instance_VerticalDirection
    //     0x1355a80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1355a84: ldr             x4, [x4, #0xa20]
    // 0x1355a88: StoreField: r1->field_23 = r4
    //     0x1355a88: stur            w4, [x1, #0x23]
    // 0x1355a8c: r5 = Instance_Clip
    //     0x1355a8c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1355a90: ldr             x5, [x5, #0x38]
    // 0x1355a94: StoreField: r1->field_2b = r5
    //     0x1355a94: stur            w5, [x1, #0x2b]
    // 0x1355a98: StoreField: r1->field_2f = rZR
    //     0x1355a98: stur            xzr, [x1, #0x2f]
    // 0x1355a9c: ldur            x6, [fp, #-0x28]
    // 0x1355aa0: StoreField: r1->field_b = r6
    //     0x1355aa0: stur            w6, [x1, #0xb]
    // 0x1355aa4: r0 = Visibility()
    //     0x1355aa4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1355aa8: mov             x2, x0
    // 0x1355aac: ldur            x0, [fp, #-0x30]
    // 0x1355ab0: stur            x2, [fp, #-0x28]
    // 0x1355ab4: StoreField: r2->field_b = r0
    //     0x1355ab4: stur            w0, [x2, #0xb]
    // 0x1355ab8: r0 = Instance_SizedBox
    //     0x1355ab8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1355abc: StoreField: r2->field_f = r0
    //     0x1355abc: stur            w0, [x2, #0xf]
    // 0x1355ac0: ldur            x0, [fp, #-0x10]
    // 0x1355ac4: StoreField: r2->field_13 = r0
    //     0x1355ac4: stur            w0, [x2, #0x13]
    // 0x1355ac8: r0 = false
    //     0x1355ac8: add             x0, NULL, #0x30  ; false
    // 0x1355acc: ArrayStore: r2[0] = r0  ; List_4
    //     0x1355acc: stur            w0, [x2, #0x17]
    // 0x1355ad0: StoreField: r2->field_1b = r0
    //     0x1355ad0: stur            w0, [x2, #0x1b]
    // 0x1355ad4: StoreField: r2->field_1f = r0
    //     0x1355ad4: stur            w0, [x2, #0x1f]
    // 0x1355ad8: StoreField: r2->field_23 = r0
    //     0x1355ad8: stur            w0, [x2, #0x23]
    // 0x1355adc: StoreField: r2->field_27 = r0
    //     0x1355adc: stur            w0, [x2, #0x27]
    // 0x1355ae0: StoreField: r2->field_2b = r0
    //     0x1355ae0: stur            w0, [x2, #0x2b]
    // 0x1355ae4: ldur            x3, [fp, #-8]
    // 0x1355ae8: LoadField: r1 = r3->field_f
    //     0x1355ae8: ldur            w1, [x3, #0xf]
    // 0x1355aec: DecompressPointer r1
    //     0x1355aec: add             x1, x1, HEAP, lsl #32
    // 0x1355af0: r0 = controller()
    //     0x1355af0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355af4: LoadField: r1 = r0->field_57
    //     0x1355af4: ldur            w1, [x0, #0x57]
    // 0x1355af8: DecompressPointer r1
    //     0x1355af8: add             x1, x1, HEAP, lsl #32
    // 0x1355afc: r0 = value()
    //     0x1355afc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355b00: cmp             w0, NULL
    // 0x1355b04: b.eq            #0x1355b24
    // 0x1355b08: LoadField: r1 = r0->field_b
    //     0x1355b08: ldur            w1, [x0, #0xb]
    // 0x1355b0c: DecompressPointer r1
    //     0x1355b0c: add             x1, x1, HEAP, lsl #32
    // 0x1355b10: cmp             w1, NULL
    // 0x1355b14: b.eq            #0x1355b24
    // 0x1355b18: LoadField: r0 = r1->field_b
    //     0x1355b18: ldur            w0, [x1, #0xb]
    // 0x1355b1c: DecompressPointer r0
    //     0x1355b1c: add             x0, x0, HEAP, lsl #32
    // 0x1355b20: cbz             w0, #0x1355b2c
    // 0x1355b24: r0 = 0
    //     0x1355b24: movz            x0, #0
    // 0x1355b28: b               #0x1355b30
    // 0x1355b2c: r0 = 1
    //     0x1355b2c: movz            x0, #0x1
    // 0x1355b30: ldur            x2, [fp, #-8]
    // 0x1355b34: stur            x0, [fp, #-0x48]
    // 0x1355b38: r16 = <EdgeInsets>
    //     0x1355b38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1355b3c: ldr             x16, [x16, #0xda0]
    // 0x1355b40: r30 = Instance_EdgeInsets
    //     0x1355b40: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0x1355b44: ldr             lr, [lr, #0x28]
    // 0x1355b48: stp             lr, x16, [SP]
    // 0x1355b4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1355b4c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1355b50: r0 = all()
    //     0x1355b50: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1355b54: ldur            x2, [fp, #-8]
    // 0x1355b58: stur            x0, [fp, #-0x10]
    // 0x1355b5c: LoadField: r1 = r2->field_f
    //     0x1355b5c: ldur            w1, [x2, #0xf]
    // 0x1355b60: DecompressPointer r1
    //     0x1355b60: add             x1, x1, HEAP, lsl #32
    // 0x1355b64: r0 = controller()
    //     0x1355b64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355b68: LoadField: r1 = r0->field_57
    //     0x1355b68: ldur            w1, [x0, #0x57]
    // 0x1355b6c: DecompressPointer r1
    //     0x1355b6c: add             x1, x1, HEAP, lsl #32
    // 0x1355b70: r0 = value()
    //     0x1355b70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355b74: cmp             w0, NULL
    // 0x1355b78: b.eq            #0x1355ccc
    // 0x1355b7c: LoadField: r1 = r0->field_2b
    //     0x1355b7c: ldur            w1, [x0, #0x2b]
    // 0x1355b80: DecompressPointer r1
    //     0x1355b80: add             x1, x1, HEAP, lsl #32
    // 0x1355b84: LoadField: r0 = r1->field_b
    //     0x1355b84: ldur            w0, [x1, #0xb]
    // 0x1355b88: cmp             w0, #4
    // 0x1355b8c: b.ne            #0x1355ccc
    // 0x1355b90: ldur            x2, [fp, #-8]
    // 0x1355b94: LoadField: r1 = r2->field_f
    //     0x1355b94: ldur            w1, [x2, #0xf]
    // 0x1355b98: DecompressPointer r1
    //     0x1355b98: add             x1, x1, HEAP, lsl #32
    // 0x1355b9c: r0 = controller()
    //     0x1355b9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355ba0: LoadField: r1 = r0->field_6b
    //     0x1355ba0: ldur            w1, [x0, #0x6b]
    // 0x1355ba4: DecompressPointer r1
    //     0x1355ba4: add             x1, x1, HEAP, lsl #32
    // 0x1355ba8: r0 = value()
    //     0x1355ba8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355bac: cmp             w0, NULL
    // 0x1355bb0: b.eq            #0x1355cb8
    // 0x1355bb4: tbnz            w0, #4, #0x1355cb8
    // 0x1355bb8: ldur            x2, [fp, #-8]
    // 0x1355bbc: LoadField: r1 = r2->field_f
    //     0x1355bbc: ldur            w1, [x2, #0xf]
    // 0x1355bc0: DecompressPointer r1
    //     0x1355bc0: add             x1, x1, HEAP, lsl #32
    // 0x1355bc4: r0 = controller()
    //     0x1355bc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355bc8: LoadField: r1 = r0->field_6f
    //     0x1355bc8: ldur            w1, [x0, #0x6f]
    // 0x1355bcc: DecompressPointer r1
    //     0x1355bcc: add             x1, x1, HEAP, lsl #32
    // 0x1355bd0: r0 = value()
    //     0x1355bd0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1355bd4: r1 = LoadClassIdInstr(r0)
    //     0x1355bd4: ldur            x1, [x0, #-1]
    //     0x1355bd8: ubfx            x1, x1, #0xc, #0x14
    // 0x1355bdc: str             x0, [SP]
    // 0x1355be0: mov             x0, x1
    // 0x1355be4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1355be4: movz            x17, #0xc898
    //     0x1355be8: add             lr, x0, x17
    //     0x1355bec: ldr             lr, [x21, lr, lsl #3]
    //     0x1355bf0: blr             lr
    // 0x1355bf4: cbz             w0, #0x1355cb8
    // 0x1355bf8: ldur            x2, [fp, #-8]
    // 0x1355bfc: LoadField: r1 = r2->field_f
    //     0x1355bfc: ldur            w1, [x2, #0xf]
    // 0x1355c00: DecompressPointer r1
    //     0x1355c00: add             x1, x1, HEAP, lsl #32
    // 0x1355c04: r0 = controller()
    //     0x1355c04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355c08: LoadField: r1 = r0->field_9b
    //     0x1355c08: ldur            w1, [x0, #0x9b]
    // 0x1355c0c: DecompressPointer r1
    //     0x1355c0c: add             x1, x1, HEAP, lsl #32
    // 0x1355c10: r0 = value()
    //     0x1355c10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355c14: tbnz            w0, #4, #0x1355cb8
    // 0x1355c18: ldur            x2, [fp, #-8]
    // 0x1355c1c: LoadField: r1 = r2->field_f
    //     0x1355c1c: ldur            w1, [x2, #0xf]
    // 0x1355c20: DecompressPointer r1
    //     0x1355c20: add             x1, x1, HEAP, lsl #32
    // 0x1355c24: r0 = controller()
    //     0x1355c24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355c28: LoadField: r1 = r0->field_77
    //     0x1355c28: ldur            w1, [x0, #0x77]
    // 0x1355c2c: DecompressPointer r1
    //     0x1355c2c: add             x1, x1, HEAP, lsl #32
    // 0x1355c30: r0 = value()
    //     0x1355c30: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1355c34: r1 = LoadClassIdInstr(r0)
    //     0x1355c34: ldur            x1, [x0, #-1]
    //     0x1355c38: ubfx            x1, x1, #0xc, #0x14
    // 0x1355c3c: str             x0, [SP]
    // 0x1355c40: mov             x0, x1
    // 0x1355c44: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1355c44: movz            x17, #0xc898
    //     0x1355c48: add             lr, x0, x17
    //     0x1355c4c: ldr             lr, [x21, lr, lsl #3]
    //     0x1355c50: blr             lr
    // 0x1355c54: cbz             w0, #0x1355cb8
    // 0x1355c58: ldur            x2, [fp, #-8]
    // 0x1355c5c: LoadField: r1 = r2->field_f
    //     0x1355c5c: ldur            w1, [x2, #0xf]
    // 0x1355c60: DecompressPointer r1
    //     0x1355c60: add             x1, x1, HEAP, lsl #32
    // 0x1355c64: r0 = controller()
    //     0x1355c64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355c68: LoadField: r1 = r0->field_97
    //     0x1355c68: ldur            w1, [x0, #0x97]
    // 0x1355c6c: DecompressPointer r1
    //     0x1355c6c: add             x1, x1, HEAP, lsl #32
    // 0x1355c70: r0 = value()
    //     0x1355c70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355c74: tbnz            w0, #4, #0x1355cb8
    // 0x1355c78: ldur            x2, [fp, #-8]
    // 0x1355c7c: LoadField: r1 = r2->field_f
    //     0x1355c7c: ldur            w1, [x2, #0xf]
    // 0x1355c80: DecompressPointer r1
    //     0x1355c80: add             x1, x1, HEAP, lsl #32
    // 0x1355c84: r0 = controller()
    //     0x1355c84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355c88: LoadField: r1 = r0->field_93
    //     0x1355c88: ldur            w1, [x0, #0x93]
    // 0x1355c8c: DecompressPointer r1
    //     0x1355c8c: add             x1, x1, HEAP, lsl #32
    // 0x1355c90: r0 = value()
    //     0x1355c90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355c94: tbnz            w0, #4, #0x1355cb8
    // 0x1355c98: ldur            x2, [fp, #-8]
    // 0x1355c9c: LoadField: r1 = r2->field_13
    //     0x1355c9c: ldur            w1, [x2, #0x13]
    // 0x1355ca0: DecompressPointer r1
    //     0x1355ca0: add             x1, x1, HEAP, lsl #32
    // 0x1355ca4: r0 = of()
    //     0x1355ca4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1355ca8: LoadField: r1 = r0->field_5b
    //     0x1355ca8: ldur            w1, [x0, #0x5b]
    // 0x1355cac: DecompressPointer r1
    //     0x1355cac: add             x1, x1, HEAP, lsl #32
    // 0x1355cb0: mov             x0, x1
    // 0x1355cb4: b               #0x1355cc4
    // 0x1355cb8: r1 = Instance_Color
    //     0x1355cb8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1355cbc: d0 = 0.400000
    //     0x1355cbc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1355cc0: r0 = withOpacity()
    //     0x1355cc0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1355cc4: mov             x1, x0
    // 0x1355cc8: b               #0x1355e04
    // 0x1355ccc: ldur            x2, [fp, #-8]
    // 0x1355cd0: LoadField: r1 = r2->field_f
    //     0x1355cd0: ldur            w1, [x2, #0xf]
    // 0x1355cd4: DecompressPointer r1
    //     0x1355cd4: add             x1, x1, HEAP, lsl #32
    // 0x1355cd8: r0 = controller()
    //     0x1355cd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355cdc: LoadField: r1 = r0->field_6b
    //     0x1355cdc: ldur            w1, [x0, #0x6b]
    // 0x1355ce0: DecompressPointer r1
    //     0x1355ce0: add             x1, x1, HEAP, lsl #32
    // 0x1355ce4: r0 = value()
    //     0x1355ce4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355ce8: cmp             w0, NULL
    // 0x1355cec: b.eq            #0x1355df4
    // 0x1355cf0: tbnz            w0, #4, #0x1355df4
    // 0x1355cf4: ldur            x2, [fp, #-8]
    // 0x1355cf8: LoadField: r1 = r2->field_f
    //     0x1355cf8: ldur            w1, [x2, #0xf]
    // 0x1355cfc: DecompressPointer r1
    //     0x1355cfc: add             x1, x1, HEAP, lsl #32
    // 0x1355d00: r0 = controller()
    //     0x1355d00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355d04: LoadField: r1 = r0->field_6f
    //     0x1355d04: ldur            w1, [x0, #0x6f]
    // 0x1355d08: DecompressPointer r1
    //     0x1355d08: add             x1, x1, HEAP, lsl #32
    // 0x1355d0c: r0 = value()
    //     0x1355d0c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1355d10: r1 = LoadClassIdInstr(r0)
    //     0x1355d10: ldur            x1, [x0, #-1]
    //     0x1355d14: ubfx            x1, x1, #0xc, #0x14
    // 0x1355d18: str             x0, [SP]
    // 0x1355d1c: mov             x0, x1
    // 0x1355d20: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1355d20: movz            x17, #0xc898
    //     0x1355d24: add             lr, x0, x17
    //     0x1355d28: ldr             lr, [x21, lr, lsl #3]
    //     0x1355d2c: blr             lr
    // 0x1355d30: cbz             w0, #0x1355d54
    // 0x1355d34: ldur            x2, [fp, #-8]
    // 0x1355d38: LoadField: r1 = r2->field_f
    //     0x1355d38: ldur            w1, [x2, #0xf]
    // 0x1355d3c: DecompressPointer r1
    //     0x1355d3c: add             x1, x1, HEAP, lsl #32
    // 0x1355d40: r0 = controller()
    //     0x1355d40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355d44: LoadField: r1 = r0->field_9b
    //     0x1355d44: ldur            w1, [x0, #0x9b]
    // 0x1355d48: DecompressPointer r1
    //     0x1355d48: add             x1, x1, HEAP, lsl #32
    // 0x1355d4c: r0 = value()
    //     0x1355d4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355d50: tbz             w0, #4, #0x1355db4
    // 0x1355d54: ldur            x2, [fp, #-8]
    // 0x1355d58: LoadField: r1 = r2->field_f
    //     0x1355d58: ldur            w1, [x2, #0xf]
    // 0x1355d5c: DecompressPointer r1
    //     0x1355d5c: add             x1, x1, HEAP, lsl #32
    // 0x1355d60: r0 = controller()
    //     0x1355d60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355d64: LoadField: r1 = r0->field_77
    //     0x1355d64: ldur            w1, [x0, #0x77]
    // 0x1355d68: DecompressPointer r1
    //     0x1355d68: add             x1, x1, HEAP, lsl #32
    // 0x1355d6c: r0 = value()
    //     0x1355d6c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1355d70: r1 = LoadClassIdInstr(r0)
    //     0x1355d70: ldur            x1, [x0, #-1]
    //     0x1355d74: ubfx            x1, x1, #0xc, #0x14
    // 0x1355d78: str             x0, [SP]
    // 0x1355d7c: mov             x0, x1
    // 0x1355d80: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1355d80: movz            x17, #0xc898
    //     0x1355d84: add             lr, x0, x17
    //     0x1355d88: ldr             lr, [x21, lr, lsl #3]
    //     0x1355d8c: blr             lr
    // 0x1355d90: cbz             w0, #0x1355df4
    // 0x1355d94: ldur            x2, [fp, #-8]
    // 0x1355d98: LoadField: r1 = r2->field_f
    //     0x1355d98: ldur            w1, [x2, #0xf]
    // 0x1355d9c: DecompressPointer r1
    //     0x1355d9c: add             x1, x1, HEAP, lsl #32
    // 0x1355da0: r0 = controller()
    //     0x1355da0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355da4: LoadField: r1 = r0->field_97
    //     0x1355da4: ldur            w1, [x0, #0x97]
    // 0x1355da8: DecompressPointer r1
    //     0x1355da8: add             x1, x1, HEAP, lsl #32
    // 0x1355dac: r0 = value()
    //     0x1355dac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355db0: tbnz            w0, #4, #0x1355df4
    // 0x1355db4: ldur            x2, [fp, #-8]
    // 0x1355db8: LoadField: r1 = r2->field_f
    //     0x1355db8: ldur            w1, [x2, #0xf]
    // 0x1355dbc: DecompressPointer r1
    //     0x1355dbc: add             x1, x1, HEAP, lsl #32
    // 0x1355dc0: r0 = controller()
    //     0x1355dc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355dc4: LoadField: r1 = r0->field_93
    //     0x1355dc4: ldur            w1, [x0, #0x93]
    // 0x1355dc8: DecompressPointer r1
    //     0x1355dc8: add             x1, x1, HEAP, lsl #32
    // 0x1355dcc: r0 = value()
    //     0x1355dcc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355dd0: tbnz            w0, #4, #0x1355df4
    // 0x1355dd4: ldur            x2, [fp, #-8]
    // 0x1355dd8: LoadField: r1 = r2->field_13
    //     0x1355dd8: ldur            w1, [x2, #0x13]
    // 0x1355ddc: DecompressPointer r1
    //     0x1355ddc: add             x1, x1, HEAP, lsl #32
    // 0x1355de0: r0 = of()
    //     0x1355de0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1355de4: LoadField: r1 = r0->field_5b
    //     0x1355de4: ldur            w1, [x0, #0x5b]
    // 0x1355de8: DecompressPointer r1
    //     0x1355de8: add             x1, x1, HEAP, lsl #32
    // 0x1355dec: mov             x0, x1
    // 0x1355df0: b               #0x1355e00
    // 0x1355df4: r1 = Instance_Color
    //     0x1355df4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1355df8: d0 = 0.400000
    //     0x1355df8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1355dfc: r0 = withOpacity()
    //     0x1355dfc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1355e00: mov             x1, x0
    // 0x1355e04: ldur            x2, [fp, #-8]
    // 0x1355e08: ldur            x0, [fp, #-0x10]
    // 0x1355e0c: r16 = <Color>
    //     0x1355e0c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1355e10: ldr             x16, [x16, #0xf80]
    // 0x1355e14: stp             x1, x16, [SP]
    // 0x1355e18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1355e18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1355e1c: r0 = all()
    //     0x1355e1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1355e20: stur            x0, [fp, #-0x30]
    // 0x1355e24: r0 = Radius()
    //     0x1355e24: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1355e28: d0 = 30.000000
    //     0x1355e28: fmov            d0, #30.00000000
    // 0x1355e2c: stur            x0, [fp, #-0x38]
    // 0x1355e30: StoreField: r0->field_7 = d0
    //     0x1355e30: stur            d0, [x0, #7]
    // 0x1355e34: StoreField: r0->field_f = d0
    //     0x1355e34: stur            d0, [x0, #0xf]
    // 0x1355e38: r0 = BorderRadius()
    //     0x1355e38: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1355e3c: mov             x1, x0
    // 0x1355e40: ldur            x0, [fp, #-0x38]
    // 0x1355e44: stur            x1, [fp, #-0x40]
    // 0x1355e48: StoreField: r1->field_7 = r0
    //     0x1355e48: stur            w0, [x1, #7]
    // 0x1355e4c: StoreField: r1->field_b = r0
    //     0x1355e4c: stur            w0, [x1, #0xb]
    // 0x1355e50: StoreField: r1->field_f = r0
    //     0x1355e50: stur            w0, [x1, #0xf]
    // 0x1355e54: StoreField: r1->field_13 = r0
    //     0x1355e54: stur            w0, [x1, #0x13]
    // 0x1355e58: r0 = RoundedRectangleBorder()
    //     0x1355e58: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1355e5c: mov             x1, x0
    // 0x1355e60: ldur            x0, [fp, #-0x40]
    // 0x1355e64: StoreField: r1->field_b = r0
    //     0x1355e64: stur            w0, [x1, #0xb]
    // 0x1355e68: r0 = Instance_BorderSide
    //     0x1355e68: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1355e6c: ldr             x0, [x0, #0xe20]
    // 0x1355e70: StoreField: r1->field_7 = r0
    //     0x1355e70: stur            w0, [x1, #7]
    // 0x1355e74: r16 = <RoundedRectangleBorder>
    //     0x1355e74: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1355e78: ldr             x16, [x16, #0xf78]
    // 0x1355e7c: stp             x1, x16, [SP]
    // 0x1355e80: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1355e80: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1355e84: r0 = all()
    //     0x1355e84: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1355e88: stur            x0, [fp, #-0x38]
    // 0x1355e8c: r0 = ButtonStyle()
    //     0x1355e8c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1355e90: mov             x1, x0
    // 0x1355e94: ldur            x0, [fp, #-0x30]
    // 0x1355e98: stur            x1, [fp, #-0x40]
    // 0x1355e9c: StoreField: r1->field_b = r0
    //     0x1355e9c: stur            w0, [x1, #0xb]
    // 0x1355ea0: ldur            x0, [fp, #-0x10]
    // 0x1355ea4: StoreField: r1->field_23 = r0
    //     0x1355ea4: stur            w0, [x1, #0x23]
    // 0x1355ea8: ldur            x0, [fp, #-0x38]
    // 0x1355eac: StoreField: r1->field_43 = r0
    //     0x1355eac: stur            w0, [x1, #0x43]
    // 0x1355eb0: r0 = TextButtonThemeData()
    //     0x1355eb0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1355eb4: mov             x2, x0
    // 0x1355eb8: ldur            x0, [fp, #-0x40]
    // 0x1355ebc: stur            x2, [fp, #-0x10]
    // 0x1355ec0: StoreField: r2->field_7 = r0
    //     0x1355ec0: stur            w0, [x2, #7]
    // 0x1355ec4: ldur            x0, [fp, #-8]
    // 0x1355ec8: LoadField: r1 = r0->field_f
    //     0x1355ec8: ldur            w1, [x0, #0xf]
    // 0x1355ecc: DecompressPointer r1
    //     0x1355ecc: add             x1, x1, HEAP, lsl #32
    // 0x1355ed0: r0 = controller()
    //     0x1355ed0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1355ed4: LoadField: r1 = r0->field_57
    //     0x1355ed4: ldur            w1, [x0, #0x57]
    // 0x1355ed8: DecompressPointer r1
    //     0x1355ed8: add             x1, x1, HEAP, lsl #32
    // 0x1355edc: r0 = value()
    //     0x1355edc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1355ee0: cmp             w0, NULL
    // 0x1355ee4: b.ne            #0x1355ef0
    // 0x1355ee8: r0 = Null
    //     0x1355ee8: mov             x0, NULL
    // 0x1355eec: b               #0x1355f10
    // 0x1355ef0: LoadField: r1 = r0->field_33
    //     0x1355ef0: ldur            w1, [x0, #0x33]
    // 0x1355ef4: DecompressPointer r1
    //     0x1355ef4: add             x1, x1, HEAP, lsl #32
    // 0x1355ef8: cmp             w1, NULL
    // 0x1355efc: b.ne            #0x1355f08
    // 0x1355f00: r0 = Null
    //     0x1355f00: mov             x0, NULL
    // 0x1355f04: b               #0x1355f10
    // 0x1355f08: LoadField: r0 = r1->field_27
    //     0x1355f08: ldur            w0, [x1, #0x27]
    // 0x1355f0c: DecompressPointer r0
    //     0x1355f0c: add             x0, x0, HEAP, lsl #32
    // 0x1355f10: cmp             w0, NULL
    // 0x1355f14: b.ne            #0x1355f1c
    // 0x1355f18: r0 = ""
    //     0x1355f18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1355f1c: ldur            x2, [fp, #-8]
    // 0x1355f20: ldur            d0, [fp, #-0x50]
    // 0x1355f24: ldur            x5, [fp, #-0x20]
    // 0x1355f28: ldur            x4, [fp, #-0x28]
    // 0x1355f2c: ldur            x3, [fp, #-0x48]
    // 0x1355f30: ldur            x1, [fp, #-0x10]
    // 0x1355f34: r6 = LoadClassIdInstr(r0)
    //     0x1355f34: ldur            x6, [x0, #-1]
    //     0x1355f38: ubfx            x6, x6, #0xc, #0x14
    // 0x1355f3c: str             x0, [SP]
    // 0x1355f40: mov             x0, x6
    // 0x1355f44: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1355f44: sub             lr, x0, #0xffa
    //     0x1355f48: ldr             lr, [x21, lr, lsl #3]
    //     0x1355f4c: blr             lr
    // 0x1355f50: mov             x1, x0
    // 0x1355f54: r0 = capitalizeFirstWord()
    //     0x1355f54: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x1355f58: ldur            x2, [fp, #-8]
    // 0x1355f5c: stur            x0, [fp, #-0x30]
    // 0x1355f60: LoadField: r1 = r2->field_13
    //     0x1355f60: ldur            w1, [x2, #0x13]
    // 0x1355f64: DecompressPointer r1
    //     0x1355f64: add             x1, x1, HEAP, lsl #32
    // 0x1355f68: r0 = of()
    //     0x1355f68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1355f6c: LoadField: r1 = r0->field_87
    //     0x1355f6c: ldur            w1, [x0, #0x87]
    // 0x1355f70: DecompressPointer r1
    //     0x1355f70: add             x1, x1, HEAP, lsl #32
    // 0x1355f74: LoadField: r0 = r1->field_7
    //     0x1355f74: ldur            w0, [x1, #7]
    // 0x1355f78: DecompressPointer r0
    //     0x1355f78: add             x0, x0, HEAP, lsl #32
    // 0x1355f7c: r16 = Instance_Color
    //     0x1355f7c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1355f80: r30 = 16.000000
    //     0x1355f80: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1355f84: ldr             lr, [lr, #0x188]
    // 0x1355f88: stp             lr, x16, [SP]
    // 0x1355f8c: mov             x1, x0
    // 0x1355f90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1355f90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1355f94: ldr             x4, [x4, #0x9b8]
    // 0x1355f98: r0 = copyWith()
    //     0x1355f98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1355f9c: stur            x0, [fp, #-0x38]
    // 0x1355fa0: r0 = Text()
    //     0x1355fa0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1355fa4: mov             x3, x0
    // 0x1355fa8: ldur            x0, [fp, #-0x30]
    // 0x1355fac: stur            x3, [fp, #-0x40]
    // 0x1355fb0: StoreField: r3->field_b = r0
    //     0x1355fb0: stur            w0, [x3, #0xb]
    // 0x1355fb4: ldur            x0, [fp, #-0x38]
    // 0x1355fb8: StoreField: r3->field_13 = r0
    //     0x1355fb8: stur            w0, [x3, #0x13]
    // 0x1355fbc: ldur            x2, [fp, #-8]
    // 0x1355fc0: r1 = Function '<anonymous closure>':.
    //     0x1355fc0: add             x1, PP, #0x42, lsl #12  ; [pp+0x425d0] AnonymousClosure: (0x132e2d4), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::bottomNavigationBar (0x136a000)
    //     0x1355fc4: ldr             x1, [x1, #0x5d0]
    // 0x1355fc8: r0 = AllocateClosure()
    //     0x1355fc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1355fcc: stur            x0, [fp, #-8]
    // 0x1355fd0: r0 = TextButton()
    //     0x1355fd0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1355fd4: mov             x1, x0
    // 0x1355fd8: ldur            x0, [fp, #-8]
    // 0x1355fdc: stur            x1, [fp, #-0x30]
    // 0x1355fe0: StoreField: r1->field_b = r0
    //     0x1355fe0: stur            w0, [x1, #0xb]
    // 0x1355fe4: r0 = false
    //     0x1355fe4: add             x0, NULL, #0x30  ; false
    // 0x1355fe8: StoreField: r1->field_27 = r0
    //     0x1355fe8: stur            w0, [x1, #0x27]
    // 0x1355fec: r0 = true
    //     0x1355fec: add             x0, NULL, #0x20  ; true
    // 0x1355ff0: StoreField: r1->field_2f = r0
    //     0x1355ff0: stur            w0, [x1, #0x2f]
    // 0x1355ff4: ldur            x0, [fp, #-0x40]
    // 0x1355ff8: StoreField: r1->field_37 = r0
    //     0x1355ff8: stur            w0, [x1, #0x37]
    // 0x1355ffc: r0 = TextButtonTheme()
    //     0x1355ffc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1356000: mov             x2, x0
    // 0x1356004: ldur            x0, [fp, #-0x10]
    // 0x1356008: stur            x2, [fp, #-8]
    // 0x135600c: StoreField: r2->field_f = r0
    //     0x135600c: stur            w0, [x2, #0xf]
    // 0x1356010: ldur            x0, [fp, #-0x30]
    // 0x1356014: StoreField: r2->field_b = r0
    //     0x1356014: stur            w0, [x2, #0xb]
    // 0x1356018: r1 = <FlexParentData>
    //     0x1356018: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x135601c: ldr             x1, [x1, #0xe00]
    // 0x1356020: r0 = Expanded()
    //     0x1356020: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1356024: mov             x3, x0
    // 0x1356028: ldur            x0, [fp, #-0x48]
    // 0x135602c: stur            x3, [fp, #-0x10]
    // 0x1356030: StoreField: r3->field_13 = r0
    //     0x1356030: stur            x0, [x3, #0x13]
    // 0x1356034: r0 = Instance_FlexFit
    //     0x1356034: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1356038: ldr             x0, [x0, #0xe08]
    // 0x135603c: StoreField: r3->field_1b = r0
    //     0x135603c: stur            w0, [x3, #0x1b]
    // 0x1356040: ldur            x0, [fp, #-8]
    // 0x1356044: StoreField: r3->field_b = r0
    //     0x1356044: stur            w0, [x3, #0xb]
    // 0x1356048: r1 = Null
    //     0x1356048: mov             x1, NULL
    // 0x135604c: r2 = 4
    //     0x135604c: movz            x2, #0x4
    // 0x1356050: r0 = AllocateArray()
    //     0x1356050: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1356054: mov             x2, x0
    // 0x1356058: ldur            x0, [fp, #-0x28]
    // 0x135605c: stur            x2, [fp, #-8]
    // 0x1356060: StoreField: r2->field_f = r0
    //     0x1356060: stur            w0, [x2, #0xf]
    // 0x1356064: ldur            x0, [fp, #-0x10]
    // 0x1356068: StoreField: r2->field_13 = r0
    //     0x1356068: stur            w0, [x2, #0x13]
    // 0x135606c: r1 = <Widget>
    //     0x135606c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1356070: r0 = AllocateGrowableArray()
    //     0x1356070: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1356074: mov             x1, x0
    // 0x1356078: ldur            x0, [fp, #-8]
    // 0x135607c: stur            x1, [fp, #-0x10]
    // 0x1356080: StoreField: r1->field_f = r0
    //     0x1356080: stur            w0, [x1, #0xf]
    // 0x1356084: r0 = 4
    //     0x1356084: movz            x0, #0x4
    // 0x1356088: StoreField: r1->field_b = r0
    //     0x1356088: stur            w0, [x1, #0xb]
    // 0x135608c: r0 = Row()
    //     0x135608c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1356090: mov             x3, x0
    // 0x1356094: r0 = Instance_Axis
    //     0x1356094: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1356098: stur            x3, [fp, #-8]
    // 0x135609c: StoreField: r3->field_f = r0
    //     0x135609c: stur            w0, [x3, #0xf]
    // 0x13560a0: r0 = Instance_MainAxisAlignment
    //     0x13560a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x13560a4: ldr             x0, [x0, #0xa8]
    // 0x13560a8: StoreField: r3->field_13 = r0
    //     0x13560a8: stur            w0, [x3, #0x13]
    // 0x13560ac: r0 = Instance_MainAxisSize
    //     0x13560ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13560b0: ldr             x0, [x0, #0xa10]
    // 0x13560b4: ArrayStore: r3[0] = r0  ; List_4
    //     0x13560b4: stur            w0, [x3, #0x17]
    // 0x13560b8: r1 = Instance_CrossAxisAlignment
    //     0x13560b8: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0x13560bc: ldr             x1, [x1, #0xc68]
    // 0x13560c0: StoreField: r3->field_1b = r1
    //     0x13560c0: stur            w1, [x3, #0x1b]
    // 0x13560c4: r4 = Instance_VerticalDirection
    //     0x13560c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13560c8: ldr             x4, [x4, #0xa20]
    // 0x13560cc: StoreField: r3->field_23 = r4
    //     0x13560cc: stur            w4, [x3, #0x23]
    // 0x13560d0: r5 = Instance_Clip
    //     0x13560d0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13560d4: ldr             x5, [x5, #0x38]
    // 0x13560d8: StoreField: r3->field_2b = r5
    //     0x13560d8: stur            w5, [x3, #0x2b]
    // 0x13560dc: StoreField: r3->field_2f = rZR
    //     0x13560dc: stur            xzr, [x3, #0x2f]
    // 0x13560e0: ldur            x1, [fp, #-0x10]
    // 0x13560e4: StoreField: r3->field_b = r1
    //     0x13560e4: stur            w1, [x3, #0xb]
    // 0x13560e8: r1 = Null
    //     0x13560e8: mov             x1, NULL
    // 0x13560ec: r2 = 6
    //     0x13560ec: movz            x2, #0x6
    // 0x13560f0: r0 = AllocateArray()
    //     0x13560f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13560f4: mov             x2, x0
    // 0x13560f8: ldur            x0, [fp, #-0x20]
    // 0x13560fc: stur            x2, [fp, #-0x10]
    // 0x1356100: StoreField: r2->field_f = r0
    //     0x1356100: stur            w0, [x2, #0xf]
    // 0x1356104: r16 = Instance_SizedBox
    //     0x1356104: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1356108: ldr             x16, [x16, #0xc70]
    // 0x135610c: StoreField: r2->field_13 = r16
    //     0x135610c: stur            w16, [x2, #0x13]
    // 0x1356110: ldur            x0, [fp, #-8]
    // 0x1356114: ArrayStore: r2[0] = r0  ; List_4
    //     0x1356114: stur            w0, [x2, #0x17]
    // 0x1356118: r1 = <Widget>
    //     0x1356118: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x135611c: r0 = AllocateGrowableArray()
    //     0x135611c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1356120: mov             x1, x0
    // 0x1356124: ldur            x0, [fp, #-0x10]
    // 0x1356128: stur            x1, [fp, #-8]
    // 0x135612c: StoreField: r1->field_f = r0
    //     0x135612c: stur            w0, [x1, #0xf]
    // 0x1356130: r0 = 6
    //     0x1356130: movz            x0, #0x6
    // 0x1356134: StoreField: r1->field_b = r0
    //     0x1356134: stur            w0, [x1, #0xb]
    // 0x1356138: r0 = Column()
    //     0x1356138: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x135613c: mov             x1, x0
    // 0x1356140: r0 = Instance_Axis
    //     0x1356140: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1356144: stur            x1, [fp, #-0x10]
    // 0x1356148: StoreField: r1->field_f = r0
    //     0x1356148: stur            w0, [x1, #0xf]
    // 0x135614c: r0 = Instance_MainAxisAlignment
    //     0x135614c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1356150: ldr             x0, [x0, #0xa08]
    // 0x1356154: StoreField: r1->field_13 = r0
    //     0x1356154: stur            w0, [x1, #0x13]
    // 0x1356158: r0 = Instance_MainAxisSize
    //     0x1356158: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x135615c: ldr             x0, [x0, #0xa10]
    // 0x1356160: ArrayStore: r1[0] = r0  ; List_4
    //     0x1356160: stur            w0, [x1, #0x17]
    // 0x1356164: r0 = Instance_CrossAxisAlignment
    //     0x1356164: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1356168: ldr             x0, [x0, #0xa18]
    // 0x135616c: StoreField: r1->field_1b = r0
    //     0x135616c: stur            w0, [x1, #0x1b]
    // 0x1356170: r0 = Instance_VerticalDirection
    //     0x1356170: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1356174: ldr             x0, [x0, #0xa20]
    // 0x1356178: StoreField: r1->field_23 = r0
    //     0x1356178: stur            w0, [x1, #0x23]
    // 0x135617c: r0 = Instance_Clip
    //     0x135617c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1356180: ldr             x0, [x0, #0x38]
    // 0x1356184: StoreField: r1->field_2b = r0
    //     0x1356184: stur            w0, [x1, #0x2b]
    // 0x1356188: StoreField: r1->field_2f = rZR
    //     0x1356188: stur            xzr, [x1, #0x2f]
    // 0x135618c: ldur            x0, [fp, #-8]
    // 0x1356190: StoreField: r1->field_b = r0
    //     0x1356190: stur            w0, [x1, #0xb]
    // 0x1356194: r0 = Padding()
    //     0x1356194: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1356198: mov             x1, x0
    // 0x135619c: r0 = Instance_EdgeInsets
    //     0x135619c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13561a0: ldr             x0, [x0, #0x1f0]
    // 0x13561a4: stur            x1, [fp, #-0x20]
    // 0x13561a8: StoreField: r1->field_f = r0
    //     0x13561a8: stur            w0, [x1, #0xf]
    // 0x13561ac: ldur            x0, [fp, #-0x10]
    // 0x13561b0: StoreField: r1->field_b = r0
    //     0x13561b0: stur            w0, [x1, #0xb]
    // 0x13561b4: ldur            d0, [fp, #-0x50]
    // 0x13561b8: r0 = inline_Allocate_Double()
    //     0x13561b8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x13561bc: add             x0, x0, #0x10
    //     0x13561c0: cmp             x2, x0
    //     0x13561c4: b.ls            #0x1356228
    //     0x13561c8: str             x0, [THR, #0x50]  ; THR::top
    //     0x13561cc: sub             x0, x0, #0xf
    //     0x13561d0: movz            x2, #0xe15c
    //     0x13561d4: movk            x2, #0x3, lsl #16
    //     0x13561d8: stur            x2, [x0, #-1]
    // 0x13561dc: StoreField: r0->field_7 = d0
    //     0x13561dc: stur            d0, [x0, #7]
    // 0x13561e0: stur            x0, [fp, #-8]
    // 0x13561e4: r0 = Container()
    //     0x13561e4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13561e8: stur            x0, [fp, #-0x10]
    // 0x13561ec: ldur            x16, [fp, #-8]
    // 0x13561f0: ldur            lr, [fp, #-0x18]
    // 0x13561f4: stp             lr, x16, [SP, #8]
    // 0x13561f8: ldur            x16, [fp, #-0x20]
    // 0x13561fc: str             x16, [SP]
    // 0x1356200: mov             x1, x0
    // 0x1356204: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0x1356204: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0x1356208: ldr             x4, [x4, #0xc78]
    // 0x135620c: r0 = Container()
    //     0x135620c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1356210: ldur            x0, [fp, #-0x10]
    // 0x1356214: LeaveFrame
    //     0x1356214: mov             SP, fp
    //     0x1356218: ldp             fp, lr, [SP], #0x10
    // 0x135621c: ret
    //     0x135621c: ret             
    // 0x1356220: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1356220: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1356224: b               #0x13554b8
    // 0x1356228: SaveReg d0
    //     0x1356228: str             q0, [SP, #-0x10]!
    // 0x135622c: SaveReg r1
    //     0x135622c: str             x1, [SP, #-8]!
    // 0x1356230: r0 = AllocateDouble()
    //     0x1356230: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1356234: RestoreReg r1
    //     0x1356234: ldr             x1, [SP], #8
    // 0x1356238: RestoreReg d0
    //     0x1356238: ldr             q0, [SP], #0x10
    // 0x135623c: b               #0x13561dc
  }
  _ body(/* No info */) {
    // ** addr: 0x14c7518, size: 0xb4
    // 0x14c7518: EnterFrame
    //     0x14c7518: stp             fp, lr, [SP, #-0x10]!
    //     0x14c751c: mov             fp, SP
    // 0x14c7520: AllocStack(0x18)
    //     0x14c7520: sub             SP, SP, #0x18
    // 0x14c7524: SetupParameters(ReturnOrderWithProofView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14c7524: mov             x0, x1
    //     0x14c7528: stur            x1, [fp, #-8]
    //     0x14c752c: stur            x2, [fp, #-0x10]
    // 0x14c7530: r1 = 2
    //     0x14c7530: movz            x1, #0x2
    // 0x14c7534: r0 = AllocateContext()
    //     0x14c7534: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c7538: ldur            x2, [fp, #-8]
    // 0x14c753c: stur            x0, [fp, #-0x18]
    // 0x14c7540: StoreField: r0->field_f = r2
    //     0x14c7540: stur            w2, [x0, #0xf]
    // 0x14c7544: ldur            x1, [fp, #-0x10]
    // 0x14c7548: StoreField: r0->field_13 = r1
    //     0x14c7548: stur            w1, [x0, #0x13]
    // 0x14c754c: r0 = Obx()
    //     0x14c754c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14c7550: ldur            x2, [fp, #-0x18]
    // 0x14c7554: r1 = Function '<anonymous closure>':.
    //     0x14c7554: add             x1, PP, #0x42, lsl #12  ; [pp+0x425d8] AnonymousClosure: (0x14c7604), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x14c7518)
    //     0x14c7558: ldr             x1, [x1, #0x5d8]
    // 0x14c755c: stur            x0, [fp, #-0x10]
    // 0x14c7560: r0 = AllocateClosure()
    //     0x14c7560: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c7564: mov             x1, x0
    // 0x14c7568: ldur            x0, [fp, #-0x10]
    // 0x14c756c: StoreField: r0->field_b = r1
    //     0x14c756c: stur            w1, [x0, #0xb]
    // 0x14c7570: r0 = ColoredBox()
    //     0x14c7570: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x14c7574: mov             x1, x0
    // 0x14c7578: r0 = Instance_Color
    //     0x14c7578: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14c757c: ldr             x0, [x0, #0x90]
    // 0x14c7580: stur            x1, [fp, #-0x18]
    // 0x14c7584: StoreField: r1->field_f = r0
    //     0x14c7584: stur            w0, [x1, #0xf]
    // 0x14c7588: ldur            x0, [fp, #-0x10]
    // 0x14c758c: StoreField: r1->field_b = r0
    //     0x14c758c: stur            w0, [x1, #0xb]
    // 0x14c7590: r0 = WillPopScope()
    //     0x14c7590: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14c7594: mov             x3, x0
    // 0x14c7598: ldur            x0, [fp, #-0x18]
    // 0x14c759c: stur            x3, [fp, #-0x10]
    // 0x14c75a0: StoreField: r3->field_b = r0
    //     0x14c75a0: stur            w0, [x3, #0xb]
    // 0x14c75a4: ldur            x2, [fp, #-8]
    // 0x14c75a8: r1 = Function 'onBackPress':.
    //     0x14c75a8: add             x1, PP, #0x42, lsl #12  ; [pp+0x425e0] AnonymousClosure: (0x14c75cc), in [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress (0x1441560)
    //     0x14c75ac: ldr             x1, [x1, #0x5e0]
    // 0x14c75b0: r0 = AllocateClosure()
    //     0x14c75b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c75b4: mov             x1, x0
    // 0x14c75b8: ldur            x0, [fp, #-0x10]
    // 0x14c75bc: StoreField: r0->field_f = r1
    //     0x14c75bc: stur            w1, [x0, #0xf]
    // 0x14c75c0: LeaveFrame
    //     0x14c75c0: mov             SP, fp
    //     0x14c75c4: ldp             fp, lr, [SP], #0x10
    // 0x14c75c8: ret
    //     0x14c75c8: ret             
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14c75cc, size: 0x38
    // 0x14c75cc: EnterFrame
    //     0x14c75cc: stp             fp, lr, [SP, #-0x10]!
    //     0x14c75d0: mov             fp, SP
    // 0x14c75d4: ldr             x0, [fp, #0x10]
    // 0x14c75d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14c75d8: ldur            w1, [x0, #0x17]
    // 0x14c75dc: DecompressPointer r1
    //     0x14c75dc: add             x1, x1, HEAP, lsl #32
    // 0x14c75e0: CheckStackOverflow
    //     0x14c75e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c75e4: cmp             SP, x16
    //     0x14c75e8: b.ls            #0x14c75fc
    // 0x14c75ec: r0 = onBackPress()
    //     0x14c75ec: bl              #0x1441560  ; [package:customer_app/app/presentation/views/basic/post_order/return_order/return_order_view.dart] ReturnOrderView::onBackPress
    // 0x14c75f0: LeaveFrame
    //     0x14c75f0: mov             SP, fp
    //     0x14c75f4: ldp             fp, lr, [SP], #0x10
    // 0x14c75f8: ret
    //     0x14c75f8: ret             
    // 0x14c75fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c75fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c7600: b               #0x14c75ec
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14c7604, size: 0x19d4
    // 0x14c7604: EnterFrame
    //     0x14c7604: stp             fp, lr, [SP, #-0x10]!
    //     0x14c7608: mov             fp, SP
    // 0x14c760c: AllocStack(0xc0)
    //     0x14c760c: sub             SP, SP, #0xc0
    // 0x14c7610: SetupParameters()
    //     0x14c7610: ldr             x0, [fp, #0x10]
    //     0x14c7614: ldur            w2, [x0, #0x17]
    //     0x14c7618: add             x2, x2, HEAP, lsl #32
    //     0x14c761c: stur            x2, [fp, #-8]
    // 0x14c7620: CheckStackOverflow
    //     0x14c7620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c7624: cmp             SP, x16
    //     0x14c7628: b.ls            #0x14c8fd0
    // 0x14c762c: LoadField: r1 = r2->field_f
    //     0x14c762c: ldur            w1, [x2, #0xf]
    // 0x14c7630: DecompressPointer r1
    //     0x14c7630: add             x1, x1, HEAP, lsl #32
    // 0x14c7634: r0 = controller()
    //     0x14c7634: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c7638: LoadField: r1 = r0->field_57
    //     0x14c7638: ldur            w1, [x0, #0x57]
    // 0x14c763c: DecompressPointer r1
    //     0x14c763c: add             x1, x1, HEAP, lsl #32
    // 0x14c7640: r0 = value()
    //     0x14c7640: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c7644: cmp             w0, NULL
    // 0x14c7648: b.eq            #0x14c8fac
    // 0x14c764c: ldur            x2, [fp, #-8]
    // 0x14c7650: r0 = Radius()
    //     0x14c7650: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c7654: d0 = 15.000000
    //     0x14c7654: fmov            d0, #15.00000000
    // 0x14c7658: stur            x0, [fp, #-0x10]
    // 0x14c765c: StoreField: r0->field_7 = d0
    //     0x14c765c: stur            d0, [x0, #7]
    // 0x14c7660: StoreField: r0->field_f = d0
    //     0x14c7660: stur            d0, [x0, #0xf]
    // 0x14c7664: r0 = BorderRadius()
    //     0x14c7664: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c7668: mov             x1, x0
    // 0x14c766c: ldur            x0, [fp, #-0x10]
    // 0x14c7670: stur            x1, [fp, #-0x18]
    // 0x14c7674: StoreField: r1->field_7 = r0
    //     0x14c7674: stur            w0, [x1, #7]
    // 0x14c7678: StoreField: r1->field_b = r0
    //     0x14c7678: stur            w0, [x1, #0xb]
    // 0x14c767c: StoreField: r1->field_f = r0
    //     0x14c767c: stur            w0, [x1, #0xf]
    // 0x14c7680: StoreField: r1->field_13 = r0
    //     0x14c7680: stur            w0, [x1, #0x13]
    // 0x14c7684: r0 = RoundedRectangleBorder()
    //     0x14c7684: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14c7688: mov             x1, x0
    // 0x14c768c: ldur            x0, [fp, #-0x18]
    // 0x14c7690: stur            x1, [fp, #-0x10]
    // 0x14c7694: StoreField: r1->field_b = r0
    //     0x14c7694: stur            w0, [x1, #0xb]
    // 0x14c7698: r0 = Instance_BorderSide
    //     0x14c7698: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14c769c: ldr             x0, [x0, #0xe20]
    // 0x14c76a0: StoreField: r1->field_7 = r0
    //     0x14c76a0: stur            w0, [x1, #7]
    // 0x14c76a4: r0 = Radius()
    //     0x14c76a4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c76a8: d0 = 12.000000
    //     0x14c76a8: fmov            d0, #12.00000000
    // 0x14c76ac: stur            x0, [fp, #-0x18]
    // 0x14c76b0: StoreField: r0->field_7 = d0
    //     0x14c76b0: stur            d0, [x0, #7]
    // 0x14c76b4: StoreField: r0->field_f = d0
    //     0x14c76b4: stur            d0, [x0, #0xf]
    // 0x14c76b8: r0 = BorderRadius()
    //     0x14c76b8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c76bc: mov             x2, x0
    // 0x14c76c0: ldur            x0, [fp, #-0x18]
    // 0x14c76c4: stur            x2, [fp, #-0x20]
    // 0x14c76c8: StoreField: r2->field_7 = r0
    //     0x14c76c8: stur            w0, [x2, #7]
    // 0x14c76cc: StoreField: r2->field_b = r0
    //     0x14c76cc: stur            w0, [x2, #0xb]
    // 0x14c76d0: StoreField: r2->field_f = r0
    //     0x14c76d0: stur            w0, [x2, #0xf]
    // 0x14c76d4: StoreField: r2->field_13 = r0
    //     0x14c76d4: stur            w0, [x2, #0x13]
    // 0x14c76d8: ldur            x0, [fp, #-8]
    // 0x14c76dc: LoadField: r1 = r0->field_f
    //     0x14c76dc: ldur            w1, [x0, #0xf]
    // 0x14c76e0: DecompressPointer r1
    //     0x14c76e0: add             x1, x1, HEAP, lsl #32
    // 0x14c76e4: r0 = controller()
    //     0x14c76e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c76e8: LoadField: r1 = r0->field_57
    //     0x14c76e8: ldur            w1, [x0, #0x57]
    // 0x14c76ec: DecompressPointer r1
    //     0x14c76ec: add             x1, x1, HEAP, lsl #32
    // 0x14c76f0: r0 = value()
    //     0x14c76f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c76f4: cmp             w0, NULL
    // 0x14c76f8: b.ne            #0x14c7704
    // 0x14c76fc: r0 = Null
    //     0x14c76fc: mov             x0, NULL
    // 0x14c7700: b               #0x14c7724
    // 0x14c7704: LoadField: r1 = r0->field_f
    //     0x14c7704: ldur            w1, [x0, #0xf]
    // 0x14c7708: DecompressPointer r1
    //     0x14c7708: add             x1, x1, HEAP, lsl #32
    // 0x14c770c: cmp             w1, NULL
    // 0x14c7710: b.ne            #0x14c771c
    // 0x14c7714: r0 = Null
    //     0x14c7714: mov             x0, NULL
    // 0x14c7718: b               #0x14c7724
    // 0x14c771c: LoadField: r0 = r1->field_7
    //     0x14c771c: ldur            w0, [x1, #7]
    // 0x14c7720: DecompressPointer r0
    //     0x14c7720: add             x0, x0, HEAP, lsl #32
    // 0x14c7724: cmp             w0, NULL
    // 0x14c7728: b.ne            #0x14c7734
    // 0x14c772c: r4 = ""
    //     0x14c772c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c7730: b               #0x14c7738
    // 0x14c7734: mov             x4, x0
    // 0x14c7738: ldur            x3, [fp, #-8]
    // 0x14c773c: ldur            x0, [fp, #-0x20]
    // 0x14c7740: stur            x4, [fp, #-0x18]
    // 0x14c7744: r1 = Function '<anonymous closure>':.
    //     0x14c7744: add             x1, PP, #0x42, lsl #12  ; [pp+0x425e8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14c7748: ldr             x1, [x1, #0x5e8]
    // 0x14c774c: r2 = Null
    //     0x14c774c: mov             x2, NULL
    // 0x14c7750: r0 = AllocateClosure()
    //     0x14c7750: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c7754: r1 = Function '<anonymous closure>':.
    //     0x14c7754: add             x1, PP, #0x42, lsl #12  ; [pp+0x425f0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14c7758: ldr             x1, [x1, #0x5f0]
    // 0x14c775c: r2 = Null
    //     0x14c775c: mov             x2, NULL
    // 0x14c7760: stur            x0, [fp, #-0x28]
    // 0x14c7764: r0 = AllocateClosure()
    //     0x14c7764: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c7768: stur            x0, [fp, #-0x30]
    // 0x14c776c: r0 = CachedNetworkImage()
    //     0x14c776c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14c7770: stur            x0, [fp, #-0x38]
    // 0x14c7774: r16 = 56.000000
    //     0x14c7774: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x14c7778: ldr             x16, [x16, #0xb78]
    // 0x14c777c: r30 = 56.000000
    //     0x14c777c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x14c7780: ldr             lr, [lr, #0xb78]
    // 0x14c7784: stp             lr, x16, [SP, #0x18]
    // 0x14c7788: r16 = Instance_BoxFit
    //     0x14c7788: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14c778c: ldr             x16, [x16, #0x118]
    // 0x14c7790: ldur            lr, [fp, #-0x28]
    // 0x14c7794: stp             lr, x16, [SP, #8]
    // 0x14c7798: ldur            x16, [fp, #-0x30]
    // 0x14c779c: str             x16, [SP]
    // 0x14c77a0: mov             x1, x0
    // 0x14c77a4: ldur            x2, [fp, #-0x18]
    // 0x14c77a8: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x14c77a8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x14c77ac: ldr             x4, [x4, #0xc28]
    // 0x14c77b0: r0 = CachedNetworkImage()
    //     0x14c77b0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14c77b4: r0 = ClipRRect()
    //     0x14c77b4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14c77b8: mov             x2, x0
    // 0x14c77bc: ldur            x0, [fp, #-0x20]
    // 0x14c77c0: stur            x2, [fp, #-0x18]
    // 0x14c77c4: StoreField: r2->field_f = r0
    //     0x14c77c4: stur            w0, [x2, #0xf]
    // 0x14c77c8: r0 = Instance_Clip
    //     0x14c77c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14c77cc: ldr             x0, [x0, #0x138]
    // 0x14c77d0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c77d0: stur            w0, [x2, #0x17]
    // 0x14c77d4: ldur            x0, [fp, #-0x38]
    // 0x14c77d8: StoreField: r2->field_b = r0
    //     0x14c77d8: stur            w0, [x2, #0xb]
    // 0x14c77dc: ldur            x0, [fp, #-8]
    // 0x14c77e0: LoadField: r1 = r0->field_f
    //     0x14c77e0: ldur            w1, [x0, #0xf]
    // 0x14c77e4: DecompressPointer r1
    //     0x14c77e4: add             x1, x1, HEAP, lsl #32
    // 0x14c77e8: r0 = controller()
    //     0x14c77e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c77ec: LoadField: r1 = r0->field_57
    //     0x14c77ec: ldur            w1, [x0, #0x57]
    // 0x14c77f0: DecompressPointer r1
    //     0x14c77f0: add             x1, x1, HEAP, lsl #32
    // 0x14c77f4: r0 = value()
    //     0x14c77f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c77f8: cmp             w0, NULL
    // 0x14c77fc: b.ne            #0x14c7808
    // 0x14c7800: r0 = Null
    //     0x14c7800: mov             x0, NULL
    // 0x14c7804: b               #0x14c7828
    // 0x14c7808: LoadField: r1 = r0->field_f
    //     0x14c7808: ldur            w1, [x0, #0xf]
    // 0x14c780c: DecompressPointer r1
    //     0x14c780c: add             x1, x1, HEAP, lsl #32
    // 0x14c7810: cmp             w1, NULL
    // 0x14c7814: b.ne            #0x14c7820
    // 0x14c7818: r0 = Null
    //     0x14c7818: mov             x0, NULL
    // 0x14c781c: b               #0x14c7828
    // 0x14c7820: LoadField: r0 = r1->field_b
    //     0x14c7820: ldur            w0, [x1, #0xb]
    // 0x14c7824: DecompressPointer r0
    //     0x14c7824: add             x0, x0, HEAP, lsl #32
    // 0x14c7828: cmp             w0, NULL
    // 0x14c782c: b.ne            #0x14c7838
    // 0x14c7830: r1 = ""
    //     0x14c7830: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c7834: b               #0x14c783c
    // 0x14c7838: mov             x1, x0
    // 0x14c783c: ldur            x2, [fp, #-8]
    // 0x14c7840: r0 = capitalizeFirstWord()
    //     0x14c7840: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x14c7844: ldur            x2, [fp, #-8]
    // 0x14c7848: stur            x0, [fp, #-0x20]
    // 0x14c784c: LoadField: r1 = r2->field_13
    //     0x14c784c: ldur            w1, [x2, #0x13]
    // 0x14c7850: DecompressPointer r1
    //     0x14c7850: add             x1, x1, HEAP, lsl #32
    // 0x14c7854: r0 = of()
    //     0x14c7854: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c7858: LoadField: r1 = r0->field_87
    //     0x14c7858: ldur            w1, [x0, #0x87]
    // 0x14c785c: DecompressPointer r1
    //     0x14c785c: add             x1, x1, HEAP, lsl #32
    // 0x14c7860: LoadField: r0 = r1->field_2b
    //     0x14c7860: ldur            w0, [x1, #0x2b]
    // 0x14c7864: DecompressPointer r0
    //     0x14c7864: add             x0, x0, HEAP, lsl #32
    // 0x14c7868: stur            x0, [fp, #-0x28]
    // 0x14c786c: r1 = Instance_Color
    //     0x14c786c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c7870: d0 = 0.700000
    //     0x14c7870: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c7874: ldr             d0, [x17, #0xf48]
    // 0x14c7878: r0 = withOpacity()
    //     0x14c7878: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c787c: r16 = 12.000000
    //     0x14c787c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c7880: ldr             x16, [x16, #0x9e8]
    // 0x14c7884: stp             x16, x0, [SP]
    // 0x14c7888: ldur            x1, [fp, #-0x28]
    // 0x14c788c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c788c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c7890: ldr             x4, [x4, #0x9b8]
    // 0x14c7894: r0 = copyWith()
    //     0x14c7894: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c7898: stur            x0, [fp, #-0x28]
    // 0x14c789c: r0 = Text()
    //     0x14c789c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c78a0: mov             x2, x0
    // 0x14c78a4: ldur            x0, [fp, #-0x20]
    // 0x14c78a8: stur            x2, [fp, #-0x30]
    // 0x14c78ac: StoreField: r2->field_b = r0
    //     0x14c78ac: stur            w0, [x2, #0xb]
    // 0x14c78b0: ldur            x0, [fp, #-0x28]
    // 0x14c78b4: StoreField: r2->field_13 = r0
    //     0x14c78b4: stur            w0, [x2, #0x13]
    // 0x14c78b8: r0 = Instance_TextOverflow
    //     0x14c78b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14c78bc: ldr             x0, [x0, #0xe10]
    // 0x14c78c0: StoreField: r2->field_2b = r0
    //     0x14c78c0: stur            w0, [x2, #0x2b]
    // 0x14c78c4: r3 = 4
    //     0x14c78c4: movz            x3, #0x4
    // 0x14c78c8: StoreField: r2->field_37 = r3
    //     0x14c78c8: stur            w3, [x2, #0x37]
    // 0x14c78cc: ldur            x4, [fp, #-8]
    // 0x14c78d0: LoadField: r1 = r4->field_f
    //     0x14c78d0: ldur            w1, [x4, #0xf]
    // 0x14c78d4: DecompressPointer r1
    //     0x14c78d4: add             x1, x1, HEAP, lsl #32
    // 0x14c78d8: r0 = controller()
    //     0x14c78d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c78dc: LoadField: r1 = r0->field_57
    //     0x14c78dc: ldur            w1, [x0, #0x57]
    // 0x14c78e0: DecompressPointer r1
    //     0x14c78e0: add             x1, x1, HEAP, lsl #32
    // 0x14c78e4: r0 = value()
    //     0x14c78e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c78e8: cmp             w0, NULL
    // 0x14c78ec: b.ne            #0x14c78f8
    // 0x14c78f0: r0 = Null
    //     0x14c78f0: mov             x0, NULL
    // 0x14c78f4: b               #0x14c7918
    // 0x14c78f8: LoadField: r1 = r0->field_f
    //     0x14c78f8: ldur            w1, [x0, #0xf]
    // 0x14c78fc: DecompressPointer r1
    //     0x14c78fc: add             x1, x1, HEAP, lsl #32
    // 0x14c7900: cmp             w1, NULL
    // 0x14c7904: b.ne            #0x14c7910
    // 0x14c7908: r0 = Null
    //     0x14c7908: mov             x0, NULL
    // 0x14c790c: b               #0x14c7918
    // 0x14c7910: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x14c7910: ldur            w0, [x1, #0x17]
    // 0x14c7914: DecompressPointer r0
    //     0x14c7914: add             x0, x0, HEAP, lsl #32
    // 0x14c7918: r1 = LoadClassIdInstr(r0)
    //     0x14c7918: ldur            x1, [x0, #-1]
    //     0x14c791c: ubfx            x1, x1, #0xc, #0x14
    // 0x14c7920: r16 = "size"
    //     0x14c7920: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x14c7924: ldr             x16, [x16, #0x9c0]
    // 0x14c7928: stp             x16, x0, [SP]
    // 0x14c792c: mov             x0, x1
    // 0x14c7930: mov             lr, x0
    // 0x14c7934: ldr             lr, [x21, lr, lsl #3]
    // 0x14c7938: blr             lr
    // 0x14c793c: tbnz            w0, #4, #0x14c7a74
    // 0x14c7940: ldur            x0, [fp, #-8]
    // 0x14c7944: r1 = Null
    //     0x14c7944: mov             x1, NULL
    // 0x14c7948: r2 = 8
    //     0x14c7948: movz            x2, #0x8
    // 0x14c794c: r0 = AllocateArray()
    //     0x14c794c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c7950: stur            x0, [fp, #-0x20]
    // 0x14c7954: r16 = "Size :  "
    //     0x14c7954: add             x16, PP, #0x33, lsl #12  ; [pp+0x33758] "Size :  "
    //     0x14c7958: ldr             x16, [x16, #0x758]
    // 0x14c795c: StoreField: r0->field_f = r16
    //     0x14c795c: stur            w16, [x0, #0xf]
    // 0x14c7960: ldur            x2, [fp, #-8]
    // 0x14c7964: LoadField: r1 = r2->field_f
    //     0x14c7964: ldur            w1, [x2, #0xf]
    // 0x14c7968: DecompressPointer r1
    //     0x14c7968: add             x1, x1, HEAP, lsl #32
    // 0x14c796c: r0 = controller()
    //     0x14c796c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c7970: LoadField: r1 = r0->field_57
    //     0x14c7970: ldur            w1, [x0, #0x57]
    // 0x14c7974: DecompressPointer r1
    //     0x14c7974: add             x1, x1, HEAP, lsl #32
    // 0x14c7978: r0 = value()
    //     0x14c7978: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c797c: cmp             w0, NULL
    // 0x14c7980: b.ne            #0x14c798c
    // 0x14c7984: r0 = Null
    //     0x14c7984: mov             x0, NULL
    // 0x14c7988: b               #0x14c79ac
    // 0x14c798c: LoadField: r1 = r0->field_f
    //     0x14c798c: ldur            w1, [x0, #0xf]
    // 0x14c7990: DecompressPointer r1
    //     0x14c7990: add             x1, x1, HEAP, lsl #32
    // 0x14c7994: cmp             w1, NULL
    // 0x14c7998: b.ne            #0x14c79a4
    // 0x14c799c: r0 = Null
    //     0x14c799c: mov             x0, NULL
    // 0x14c79a0: b               #0x14c79ac
    // 0x14c79a4: LoadField: r0 = r1->field_f
    //     0x14c79a4: ldur            w0, [x1, #0xf]
    // 0x14c79a8: DecompressPointer r0
    //     0x14c79a8: add             x0, x0, HEAP, lsl #32
    // 0x14c79ac: ldur            x3, [fp, #-8]
    // 0x14c79b0: ldur            x2, [fp, #-0x20]
    // 0x14c79b4: mov             x1, x2
    // 0x14c79b8: ArrayStore: r1[1] = r0  ; List_4
    //     0x14c79b8: add             x25, x1, #0x13
    //     0x14c79bc: str             w0, [x25]
    //     0x14c79c0: tbz             w0, #0, #0x14c79dc
    //     0x14c79c4: ldurb           w16, [x1, #-1]
    //     0x14c79c8: ldurb           w17, [x0, #-1]
    //     0x14c79cc: and             x16, x17, x16, lsr #2
    //     0x14c79d0: tst             x16, HEAP, lsr #32
    //     0x14c79d4: b.eq            #0x14c79dc
    //     0x14c79d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c79dc: r16 = "/ Qty: "
    //     0x14c79dc: add             x16, PP, #0x42, lsl #12  ; [pp+0x425f8] "/ Qty: "
    //     0x14c79e0: ldr             x16, [x16, #0x5f8]
    // 0x14c79e4: ArrayStore: r2[0] = r16  ; List_4
    //     0x14c79e4: stur            w16, [x2, #0x17]
    // 0x14c79e8: LoadField: r1 = r3->field_f
    //     0x14c79e8: ldur            w1, [x3, #0xf]
    // 0x14c79ec: DecompressPointer r1
    //     0x14c79ec: add             x1, x1, HEAP, lsl #32
    // 0x14c79f0: r0 = controller()
    //     0x14c79f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c79f4: LoadField: r1 = r0->field_57
    //     0x14c79f4: ldur            w1, [x0, #0x57]
    // 0x14c79f8: DecompressPointer r1
    //     0x14c79f8: add             x1, x1, HEAP, lsl #32
    // 0x14c79fc: r0 = value()
    //     0x14c79fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c7a00: cmp             w0, NULL
    // 0x14c7a04: b.ne            #0x14c7a10
    // 0x14c7a08: r0 = Null
    //     0x14c7a08: mov             x0, NULL
    // 0x14c7a0c: b               #0x14c7a30
    // 0x14c7a10: LoadField: r1 = r0->field_f
    //     0x14c7a10: ldur            w1, [x0, #0xf]
    // 0x14c7a14: DecompressPointer r1
    //     0x14c7a14: add             x1, x1, HEAP, lsl #32
    // 0x14c7a18: cmp             w1, NULL
    // 0x14c7a1c: b.ne            #0x14c7a28
    // 0x14c7a20: r0 = Null
    //     0x14c7a20: mov             x0, NULL
    // 0x14c7a24: b               #0x14c7a30
    // 0x14c7a28: LoadField: r0 = r1->field_13
    //     0x14c7a28: ldur            w0, [x1, #0x13]
    // 0x14c7a2c: DecompressPointer r0
    //     0x14c7a2c: add             x0, x0, HEAP, lsl #32
    // 0x14c7a30: cmp             w0, NULL
    // 0x14c7a34: b.ne            #0x14c7a3c
    // 0x14c7a38: r0 = ""
    //     0x14c7a38: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c7a3c: ldur            x1, [fp, #-0x20]
    // 0x14c7a40: ArrayStore: r1[3] = r0  ; List_4
    //     0x14c7a40: add             x25, x1, #0x1b
    //     0x14c7a44: str             w0, [x25]
    //     0x14c7a48: tbz             w0, #0, #0x14c7a64
    //     0x14c7a4c: ldurb           w16, [x1, #-1]
    //     0x14c7a50: ldurb           w17, [x0, #-1]
    //     0x14c7a54: and             x16, x17, x16, lsr #2
    //     0x14c7a58: tst             x16, HEAP, lsr #32
    //     0x14c7a5c: b.eq            #0x14c7a64
    //     0x14c7a60: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c7a64: ldur            x16, [fp, #-0x20]
    // 0x14c7a68: str             x16, [SP]
    // 0x14c7a6c: r0 = _interpolate()
    //     0x14c7a6c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14c7a70: b               #0x14c7ba4
    // 0x14c7a74: ldur            x0, [fp, #-8]
    // 0x14c7a78: r1 = Null
    //     0x14c7a78: mov             x1, NULL
    // 0x14c7a7c: r2 = 8
    //     0x14c7a7c: movz            x2, #0x8
    // 0x14c7a80: r0 = AllocateArray()
    //     0x14c7a80: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c7a84: stur            x0, [fp, #-0x20]
    // 0x14c7a88: r16 = "Variant : "
    //     0x14c7a88: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0x14c7a8c: ldr             x16, [x16, #0x768]
    // 0x14c7a90: StoreField: r0->field_f = r16
    //     0x14c7a90: stur            w16, [x0, #0xf]
    // 0x14c7a94: ldur            x2, [fp, #-8]
    // 0x14c7a98: LoadField: r1 = r2->field_f
    //     0x14c7a98: ldur            w1, [x2, #0xf]
    // 0x14c7a9c: DecompressPointer r1
    //     0x14c7a9c: add             x1, x1, HEAP, lsl #32
    // 0x14c7aa0: r0 = controller()
    //     0x14c7aa0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c7aa4: LoadField: r1 = r0->field_57
    //     0x14c7aa4: ldur            w1, [x0, #0x57]
    // 0x14c7aa8: DecompressPointer r1
    //     0x14c7aa8: add             x1, x1, HEAP, lsl #32
    // 0x14c7aac: r0 = value()
    //     0x14c7aac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c7ab0: cmp             w0, NULL
    // 0x14c7ab4: b.ne            #0x14c7ac0
    // 0x14c7ab8: r0 = Null
    //     0x14c7ab8: mov             x0, NULL
    // 0x14c7abc: b               #0x14c7ae0
    // 0x14c7ac0: LoadField: r1 = r0->field_f
    //     0x14c7ac0: ldur            w1, [x0, #0xf]
    // 0x14c7ac4: DecompressPointer r1
    //     0x14c7ac4: add             x1, x1, HEAP, lsl #32
    // 0x14c7ac8: cmp             w1, NULL
    // 0x14c7acc: b.ne            #0x14c7ad8
    // 0x14c7ad0: r0 = Null
    //     0x14c7ad0: mov             x0, NULL
    // 0x14c7ad4: b               #0x14c7ae0
    // 0x14c7ad8: LoadField: r0 = r1->field_f
    //     0x14c7ad8: ldur            w0, [x1, #0xf]
    // 0x14c7adc: DecompressPointer r0
    //     0x14c7adc: add             x0, x0, HEAP, lsl #32
    // 0x14c7ae0: ldur            x3, [fp, #-8]
    // 0x14c7ae4: ldur            x2, [fp, #-0x20]
    // 0x14c7ae8: mov             x1, x2
    // 0x14c7aec: ArrayStore: r1[1] = r0  ; List_4
    //     0x14c7aec: add             x25, x1, #0x13
    //     0x14c7af0: str             w0, [x25]
    //     0x14c7af4: tbz             w0, #0, #0x14c7b10
    //     0x14c7af8: ldurb           w16, [x1, #-1]
    //     0x14c7afc: ldurb           w17, [x0, #-1]
    //     0x14c7b00: and             x16, x17, x16, lsr #2
    //     0x14c7b04: tst             x16, HEAP, lsr #32
    //     0x14c7b08: b.eq            #0x14c7b10
    //     0x14c7b0c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c7b10: r16 = "/ Qty: "
    //     0x14c7b10: add             x16, PP, #0x42, lsl #12  ; [pp+0x425f8] "/ Qty: "
    //     0x14c7b14: ldr             x16, [x16, #0x5f8]
    // 0x14c7b18: ArrayStore: r2[0] = r16  ; List_4
    //     0x14c7b18: stur            w16, [x2, #0x17]
    // 0x14c7b1c: LoadField: r1 = r3->field_f
    //     0x14c7b1c: ldur            w1, [x3, #0xf]
    // 0x14c7b20: DecompressPointer r1
    //     0x14c7b20: add             x1, x1, HEAP, lsl #32
    // 0x14c7b24: r0 = controller()
    //     0x14c7b24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c7b28: LoadField: r1 = r0->field_57
    //     0x14c7b28: ldur            w1, [x0, #0x57]
    // 0x14c7b2c: DecompressPointer r1
    //     0x14c7b2c: add             x1, x1, HEAP, lsl #32
    // 0x14c7b30: r0 = value()
    //     0x14c7b30: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c7b34: cmp             w0, NULL
    // 0x14c7b38: b.ne            #0x14c7b44
    // 0x14c7b3c: r0 = Null
    //     0x14c7b3c: mov             x0, NULL
    // 0x14c7b40: b               #0x14c7b64
    // 0x14c7b44: LoadField: r1 = r0->field_f
    //     0x14c7b44: ldur            w1, [x0, #0xf]
    // 0x14c7b48: DecompressPointer r1
    //     0x14c7b48: add             x1, x1, HEAP, lsl #32
    // 0x14c7b4c: cmp             w1, NULL
    // 0x14c7b50: b.ne            #0x14c7b5c
    // 0x14c7b54: r0 = Null
    //     0x14c7b54: mov             x0, NULL
    // 0x14c7b58: b               #0x14c7b64
    // 0x14c7b5c: LoadField: r0 = r1->field_13
    //     0x14c7b5c: ldur            w0, [x1, #0x13]
    // 0x14c7b60: DecompressPointer r0
    //     0x14c7b60: add             x0, x0, HEAP, lsl #32
    // 0x14c7b64: cmp             w0, NULL
    // 0x14c7b68: b.ne            #0x14c7b70
    // 0x14c7b6c: r0 = ""
    //     0x14c7b6c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c7b70: ldur            x1, [fp, #-0x20]
    // 0x14c7b74: ArrayStore: r1[3] = r0  ; List_4
    //     0x14c7b74: add             x25, x1, #0x1b
    //     0x14c7b78: str             w0, [x25]
    //     0x14c7b7c: tbz             w0, #0, #0x14c7b98
    //     0x14c7b80: ldurb           w16, [x1, #-1]
    //     0x14c7b84: ldurb           w17, [x0, #-1]
    //     0x14c7b88: and             x16, x17, x16, lsr #2
    //     0x14c7b8c: tst             x16, HEAP, lsr #32
    //     0x14c7b90: b.eq            #0x14c7b98
    //     0x14c7b94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c7b98: ldur            x16, [fp, #-0x20]
    // 0x14c7b9c: str             x16, [SP]
    // 0x14c7ba0: r0 = _interpolate()
    //     0x14c7ba0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14c7ba4: ldur            x2, [fp, #-8]
    // 0x14c7ba8: stur            x0, [fp, #-0x20]
    // 0x14c7bac: LoadField: r1 = r2->field_13
    //     0x14c7bac: ldur            w1, [x2, #0x13]
    // 0x14c7bb0: DecompressPointer r1
    //     0x14c7bb0: add             x1, x1, HEAP, lsl #32
    // 0x14c7bb4: r0 = of()
    //     0x14c7bb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c7bb8: LoadField: r1 = r0->field_87
    //     0x14c7bb8: ldur            w1, [x0, #0x87]
    // 0x14c7bbc: DecompressPointer r1
    //     0x14c7bbc: add             x1, x1, HEAP, lsl #32
    // 0x14c7bc0: LoadField: r0 = r1->field_7
    //     0x14c7bc0: ldur            w0, [x1, #7]
    // 0x14c7bc4: DecompressPointer r0
    //     0x14c7bc4: add             x0, x0, HEAP, lsl #32
    // 0x14c7bc8: stur            x0, [fp, #-0x28]
    // 0x14c7bcc: r1 = Instance_Color
    //     0x14c7bcc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c7bd0: d0 = 0.700000
    //     0x14c7bd0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c7bd4: ldr             d0, [x17, #0xf48]
    // 0x14c7bd8: r0 = withOpacity()
    //     0x14c7bd8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c7bdc: r16 = 12.000000
    //     0x14c7bdc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c7be0: ldr             x16, [x16, #0x9e8]
    // 0x14c7be4: stp             x0, x16, [SP]
    // 0x14c7be8: ldur            x1, [fp, #-0x28]
    // 0x14c7bec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c7bec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c7bf0: ldr             x4, [x4, #0xaa0]
    // 0x14c7bf4: r0 = copyWith()
    //     0x14c7bf4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c7bf8: stur            x0, [fp, #-0x28]
    // 0x14c7bfc: r0 = Text()
    //     0x14c7bfc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c7c00: mov             x1, x0
    // 0x14c7c04: ldur            x0, [fp, #-0x20]
    // 0x14c7c08: stur            x1, [fp, #-0x38]
    // 0x14c7c0c: StoreField: r1->field_b = r0
    //     0x14c7c0c: stur            w0, [x1, #0xb]
    // 0x14c7c10: ldur            x0, [fp, #-0x28]
    // 0x14c7c14: StoreField: r1->field_13 = r0
    //     0x14c7c14: stur            w0, [x1, #0x13]
    // 0x14c7c18: r0 = Padding()
    //     0x14c7c18: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c7c1c: mov             x2, x0
    // 0x14c7c20: r0 = Instance_EdgeInsets
    //     0x14c7c20: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x14c7c24: ldr             x0, [x0, #0x668]
    // 0x14c7c28: stur            x2, [fp, #-0x20]
    // 0x14c7c2c: StoreField: r2->field_f = r0
    //     0x14c7c2c: stur            w0, [x2, #0xf]
    // 0x14c7c30: ldur            x1, [fp, #-0x38]
    // 0x14c7c34: StoreField: r2->field_b = r1
    //     0x14c7c34: stur            w1, [x2, #0xb]
    // 0x14c7c38: ldur            x3, [fp, #-8]
    // 0x14c7c3c: LoadField: r1 = r3->field_f
    //     0x14c7c3c: ldur            w1, [x3, #0xf]
    // 0x14c7c40: DecompressPointer r1
    //     0x14c7c40: add             x1, x1, HEAP, lsl #32
    // 0x14c7c44: r0 = controller()
    //     0x14c7c44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c7c48: LoadField: r1 = r0->field_57
    //     0x14c7c48: ldur            w1, [x0, #0x57]
    // 0x14c7c4c: DecompressPointer r1
    //     0x14c7c4c: add             x1, x1, HEAP, lsl #32
    // 0x14c7c50: r0 = value()
    //     0x14c7c50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c7c54: cmp             w0, NULL
    // 0x14c7c58: b.ne            #0x14c7c64
    // 0x14c7c5c: r5 = Null
    //     0x14c7c5c: mov             x5, NULL
    // 0x14c7c60: b               #0x14c7ca4
    // 0x14c7c64: LoadField: r1 = r0->field_f
    //     0x14c7c64: ldur            w1, [x0, #0xf]
    // 0x14c7c68: DecompressPointer r1
    //     0x14c7c68: add             x1, x1, HEAP, lsl #32
    // 0x14c7c6c: cmp             w1, NULL
    // 0x14c7c70: b.ne            #0x14c7c7c
    // 0x14c7c74: r0 = Null
    //     0x14c7c74: mov             x0, NULL
    // 0x14c7c78: b               #0x14c7ca0
    // 0x14c7c7c: LoadField: r0 = r1->field_1b
    //     0x14c7c7c: ldur            w0, [x1, #0x1b]
    // 0x14c7c80: DecompressPointer r0
    //     0x14c7c80: add             x0, x0, HEAP, lsl #32
    // 0x14c7c84: cmp             w0, NULL
    // 0x14c7c88: b.ne            #0x14c7c94
    // 0x14c7c8c: r0 = Null
    //     0x14c7c8c: mov             x0, NULL
    // 0x14c7c90: b               #0x14c7ca0
    // 0x14c7c94: LoadField: r1 = r0->field_7
    //     0x14c7c94: ldur            w1, [x0, #7]
    // 0x14c7c98: DecompressPointer r1
    //     0x14c7c98: add             x1, x1, HEAP, lsl #32
    // 0x14c7c9c: mov             x0, x1
    // 0x14c7ca0: mov             x5, x0
    // 0x14c7ca4: ldur            x2, [fp, #-8]
    // 0x14c7ca8: ldur            x4, [fp, #-0x10]
    // 0x14c7cac: ldur            x3, [fp, #-0x18]
    // 0x14c7cb0: ldur            x1, [fp, #-0x30]
    // 0x14c7cb4: ldur            x0, [fp, #-0x20]
    // 0x14c7cb8: str             x5, [SP]
    // 0x14c7cbc: r0 = _interpolateSingle()
    //     0x14c7cbc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x14c7cc0: ldur            x2, [fp, #-8]
    // 0x14c7cc4: stur            x0, [fp, #-0x28]
    // 0x14c7cc8: LoadField: r1 = r2->field_13
    //     0x14c7cc8: ldur            w1, [x2, #0x13]
    // 0x14c7ccc: DecompressPointer r1
    //     0x14c7ccc: add             x1, x1, HEAP, lsl #32
    // 0x14c7cd0: r0 = of()
    //     0x14c7cd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c7cd4: LoadField: r1 = r0->field_87
    //     0x14c7cd4: ldur            w1, [x0, #0x87]
    // 0x14c7cd8: DecompressPointer r1
    //     0x14c7cd8: add             x1, x1, HEAP, lsl #32
    // 0x14c7cdc: LoadField: r0 = r1->field_2b
    //     0x14c7cdc: ldur            w0, [x1, #0x2b]
    // 0x14c7ce0: DecompressPointer r0
    //     0x14c7ce0: add             x0, x0, HEAP, lsl #32
    // 0x14c7ce4: r16 = Instance_Color
    //     0x14c7ce4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c7ce8: r30 = 12.000000
    //     0x14c7ce8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c7cec: ldr             lr, [lr, #0x9e8]
    // 0x14c7cf0: stp             lr, x16, [SP]
    // 0x14c7cf4: mov             x1, x0
    // 0x14c7cf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c7cf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c7cfc: ldr             x4, [x4, #0x9b8]
    // 0x14c7d00: r0 = copyWith()
    //     0x14c7d00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c7d04: stur            x0, [fp, #-0x38]
    // 0x14c7d08: r0 = Text()
    //     0x14c7d08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c7d0c: mov             x3, x0
    // 0x14c7d10: ldur            x0, [fp, #-0x28]
    // 0x14c7d14: stur            x3, [fp, #-0x40]
    // 0x14c7d18: StoreField: r3->field_b = r0
    //     0x14c7d18: stur            w0, [x3, #0xb]
    // 0x14c7d1c: ldur            x0, [fp, #-0x38]
    // 0x14c7d20: StoreField: r3->field_13 = r0
    //     0x14c7d20: stur            w0, [x3, #0x13]
    // 0x14c7d24: r1 = Null
    //     0x14c7d24: mov             x1, NULL
    // 0x14c7d28: r2 = 8
    //     0x14c7d28: movz            x2, #0x8
    // 0x14c7d2c: r0 = AllocateArray()
    //     0x14c7d2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c7d30: mov             x2, x0
    // 0x14c7d34: ldur            x0, [fp, #-0x30]
    // 0x14c7d38: stur            x2, [fp, #-0x28]
    // 0x14c7d3c: StoreField: r2->field_f = r0
    //     0x14c7d3c: stur            w0, [x2, #0xf]
    // 0x14c7d40: ldur            x0, [fp, #-0x20]
    // 0x14c7d44: StoreField: r2->field_13 = r0
    //     0x14c7d44: stur            w0, [x2, #0x13]
    // 0x14c7d48: r16 = Instance_SizedBox
    //     0x14c7d48: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x14c7d4c: ldr             x16, [x16, #0xc70]
    // 0x14c7d50: ArrayStore: r2[0] = r16  ; List_4
    //     0x14c7d50: stur            w16, [x2, #0x17]
    // 0x14c7d54: ldur            x0, [fp, #-0x40]
    // 0x14c7d58: StoreField: r2->field_1b = r0
    //     0x14c7d58: stur            w0, [x2, #0x1b]
    // 0x14c7d5c: r1 = <Widget>
    //     0x14c7d5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c7d60: r0 = AllocateGrowableArray()
    //     0x14c7d60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c7d64: mov             x1, x0
    // 0x14c7d68: ldur            x0, [fp, #-0x28]
    // 0x14c7d6c: stur            x1, [fp, #-0x20]
    // 0x14c7d70: StoreField: r1->field_f = r0
    //     0x14c7d70: stur            w0, [x1, #0xf]
    // 0x14c7d74: r2 = 8
    //     0x14c7d74: movz            x2, #0x8
    // 0x14c7d78: StoreField: r1->field_b = r2
    //     0x14c7d78: stur            w2, [x1, #0xb]
    // 0x14c7d7c: r0 = Column()
    //     0x14c7d7c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c7d80: mov             x2, x0
    // 0x14c7d84: r0 = Instance_Axis
    //     0x14c7d84: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c7d88: stur            x2, [fp, #-0x28]
    // 0x14c7d8c: StoreField: r2->field_f = r0
    //     0x14c7d8c: stur            w0, [x2, #0xf]
    // 0x14c7d90: r1 = Instance_MainAxisAlignment
    //     0x14c7d90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x14c7d94: ldr             x1, [x1, #0xa8]
    // 0x14c7d98: StoreField: r2->field_13 = r1
    //     0x14c7d98: stur            w1, [x2, #0x13]
    // 0x14c7d9c: r3 = Instance_MainAxisSize
    //     0x14c7d9c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14c7da0: ldr             x3, [x3, #0xdd0]
    // 0x14c7da4: ArrayStore: r2[0] = r3  ; List_4
    //     0x14c7da4: stur            w3, [x2, #0x17]
    // 0x14c7da8: r4 = Instance_CrossAxisAlignment
    //     0x14c7da8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c7dac: ldr             x4, [x4, #0x890]
    // 0x14c7db0: StoreField: r2->field_1b = r4
    //     0x14c7db0: stur            w4, [x2, #0x1b]
    // 0x14c7db4: r5 = Instance_VerticalDirection
    //     0x14c7db4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c7db8: ldr             x5, [x5, #0xa20]
    // 0x14c7dbc: StoreField: r2->field_23 = r5
    //     0x14c7dbc: stur            w5, [x2, #0x23]
    // 0x14c7dc0: r6 = Instance_Clip
    //     0x14c7dc0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c7dc4: ldr             x6, [x6, #0x38]
    // 0x14c7dc8: StoreField: r2->field_2b = r6
    //     0x14c7dc8: stur            w6, [x2, #0x2b]
    // 0x14c7dcc: StoreField: r2->field_2f = rZR
    //     0x14c7dcc: stur            xzr, [x2, #0x2f]
    // 0x14c7dd0: ldur            x1, [fp, #-0x20]
    // 0x14c7dd4: StoreField: r2->field_b = r1
    //     0x14c7dd4: stur            w1, [x2, #0xb]
    // 0x14c7dd8: r1 = <FlexParentData>
    //     0x14c7dd8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14c7ddc: ldr             x1, [x1, #0xe00]
    // 0x14c7de0: r0 = Expanded()
    //     0x14c7de0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14c7de4: mov             x3, x0
    // 0x14c7de8: r0 = 1
    //     0x14c7de8: movz            x0, #0x1
    // 0x14c7dec: stur            x3, [fp, #-0x20]
    // 0x14c7df0: StoreField: r3->field_13 = r0
    //     0x14c7df0: stur            x0, [x3, #0x13]
    // 0x14c7df4: r4 = Instance_FlexFit
    //     0x14c7df4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14c7df8: ldr             x4, [x4, #0xe08]
    // 0x14c7dfc: StoreField: r3->field_1b = r4
    //     0x14c7dfc: stur            w4, [x3, #0x1b]
    // 0x14c7e00: ldur            x1, [fp, #-0x28]
    // 0x14c7e04: StoreField: r3->field_b = r1
    //     0x14c7e04: stur            w1, [x3, #0xb]
    // 0x14c7e08: r1 = Null
    //     0x14c7e08: mov             x1, NULL
    // 0x14c7e0c: r2 = 6
    //     0x14c7e0c: movz            x2, #0x6
    // 0x14c7e10: r0 = AllocateArray()
    //     0x14c7e10: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c7e14: mov             x2, x0
    // 0x14c7e18: ldur            x0, [fp, #-0x18]
    // 0x14c7e1c: stur            x2, [fp, #-0x28]
    // 0x14c7e20: StoreField: r2->field_f = r0
    //     0x14c7e20: stur            w0, [x2, #0xf]
    // 0x14c7e24: r16 = Instance_SizedBox
    //     0x14c7e24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14c7e28: ldr             x16, [x16, #0xb20]
    // 0x14c7e2c: StoreField: r2->field_13 = r16
    //     0x14c7e2c: stur            w16, [x2, #0x13]
    // 0x14c7e30: ldur            x0, [fp, #-0x20]
    // 0x14c7e34: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c7e34: stur            w0, [x2, #0x17]
    // 0x14c7e38: r1 = <Widget>
    //     0x14c7e38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c7e3c: r0 = AllocateGrowableArray()
    //     0x14c7e3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c7e40: mov             x1, x0
    // 0x14c7e44: ldur            x0, [fp, #-0x28]
    // 0x14c7e48: stur            x1, [fp, #-0x18]
    // 0x14c7e4c: StoreField: r1->field_f = r0
    //     0x14c7e4c: stur            w0, [x1, #0xf]
    // 0x14c7e50: r2 = 6
    //     0x14c7e50: movz            x2, #0x6
    // 0x14c7e54: StoreField: r1->field_b = r2
    //     0x14c7e54: stur            w2, [x1, #0xb]
    // 0x14c7e58: r0 = Row()
    //     0x14c7e58: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14c7e5c: mov             x3, x0
    // 0x14c7e60: r0 = Instance_Axis
    //     0x14c7e60: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c7e64: stur            x3, [fp, #-0x20]
    // 0x14c7e68: StoreField: r3->field_f = r0
    //     0x14c7e68: stur            w0, [x3, #0xf]
    // 0x14c7e6c: r4 = Instance_MainAxisAlignment
    //     0x14c7e6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c7e70: ldr             x4, [x4, #0xa08]
    // 0x14c7e74: StoreField: r3->field_13 = r4
    //     0x14c7e74: stur            w4, [x3, #0x13]
    // 0x14c7e78: r5 = Instance_MainAxisSize
    //     0x14c7e78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c7e7c: ldr             x5, [x5, #0xa10]
    // 0x14c7e80: ArrayStore: r3[0] = r5  ; List_4
    //     0x14c7e80: stur            w5, [x3, #0x17]
    // 0x14c7e84: r6 = Instance_CrossAxisAlignment
    //     0x14c7e84: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c7e88: ldr             x6, [x6, #0xa18]
    // 0x14c7e8c: StoreField: r3->field_1b = r6
    //     0x14c7e8c: stur            w6, [x3, #0x1b]
    // 0x14c7e90: r7 = Instance_VerticalDirection
    //     0x14c7e90: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c7e94: ldr             x7, [x7, #0xa20]
    // 0x14c7e98: StoreField: r3->field_23 = r7
    //     0x14c7e98: stur            w7, [x3, #0x23]
    // 0x14c7e9c: r8 = Instance_Clip
    //     0x14c7e9c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c7ea0: ldr             x8, [x8, #0x38]
    // 0x14c7ea4: StoreField: r3->field_2b = r8
    //     0x14c7ea4: stur            w8, [x3, #0x2b]
    // 0x14c7ea8: StoreField: r3->field_2f = rZR
    //     0x14c7ea8: stur            xzr, [x3, #0x2f]
    // 0x14c7eac: ldur            x1, [fp, #-0x18]
    // 0x14c7eb0: StoreField: r3->field_b = r1
    //     0x14c7eb0: stur            w1, [x3, #0xb]
    // 0x14c7eb4: r1 = Null
    //     0x14c7eb4: mov             x1, NULL
    // 0x14c7eb8: r2 = 2
    //     0x14c7eb8: movz            x2, #0x2
    // 0x14c7ebc: r0 = AllocateArray()
    //     0x14c7ebc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c7ec0: mov             x2, x0
    // 0x14c7ec4: ldur            x0, [fp, #-0x20]
    // 0x14c7ec8: stur            x2, [fp, #-0x18]
    // 0x14c7ecc: StoreField: r2->field_f = r0
    //     0x14c7ecc: stur            w0, [x2, #0xf]
    // 0x14c7ed0: r1 = <Widget>
    //     0x14c7ed0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c7ed4: r0 = AllocateGrowableArray()
    //     0x14c7ed4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c7ed8: mov             x1, x0
    // 0x14c7edc: ldur            x0, [fp, #-0x18]
    // 0x14c7ee0: stur            x1, [fp, #-0x20]
    // 0x14c7ee4: StoreField: r1->field_f = r0
    //     0x14c7ee4: stur            w0, [x1, #0xf]
    // 0x14c7ee8: r2 = 2
    //     0x14c7ee8: movz            x2, #0x2
    // 0x14c7eec: StoreField: r1->field_b = r2
    //     0x14c7eec: stur            w2, [x1, #0xb]
    // 0x14c7ef0: r0 = Column()
    //     0x14c7ef0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c7ef4: mov             x3, x0
    // 0x14c7ef8: r0 = Instance_Axis
    //     0x14c7ef8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c7efc: stur            x3, [fp, #-0x18]
    // 0x14c7f00: StoreField: r3->field_f = r0
    //     0x14c7f00: stur            w0, [x3, #0xf]
    // 0x14c7f04: r4 = Instance_MainAxisAlignment
    //     0x14c7f04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c7f08: ldr             x4, [x4, #0xa08]
    // 0x14c7f0c: StoreField: r3->field_13 = r4
    //     0x14c7f0c: stur            w4, [x3, #0x13]
    // 0x14c7f10: r5 = Instance_MainAxisSize
    //     0x14c7f10: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c7f14: ldr             x5, [x5, #0xa10]
    // 0x14c7f18: ArrayStore: r3[0] = r5  ; List_4
    //     0x14c7f18: stur            w5, [x3, #0x17]
    // 0x14c7f1c: r6 = Instance_CrossAxisAlignment
    //     0x14c7f1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c7f20: ldr             x6, [x6, #0xa18]
    // 0x14c7f24: StoreField: r3->field_1b = r6
    //     0x14c7f24: stur            w6, [x3, #0x1b]
    // 0x14c7f28: r7 = Instance_VerticalDirection
    //     0x14c7f28: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c7f2c: ldr             x7, [x7, #0xa20]
    // 0x14c7f30: StoreField: r3->field_23 = r7
    //     0x14c7f30: stur            w7, [x3, #0x23]
    // 0x14c7f34: r8 = Instance_Clip
    //     0x14c7f34: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c7f38: ldr             x8, [x8, #0x38]
    // 0x14c7f3c: StoreField: r3->field_2b = r8
    //     0x14c7f3c: stur            w8, [x3, #0x2b]
    // 0x14c7f40: StoreField: r3->field_2f = rZR
    //     0x14c7f40: stur            xzr, [x3, #0x2f]
    // 0x14c7f44: ldur            x1, [fp, #-0x20]
    // 0x14c7f48: StoreField: r3->field_b = r1
    //     0x14c7f48: stur            w1, [x3, #0xb]
    // 0x14c7f4c: r1 = Null
    //     0x14c7f4c: mov             x1, NULL
    // 0x14c7f50: r2 = 2
    //     0x14c7f50: movz            x2, #0x2
    // 0x14c7f54: r0 = AllocateArray()
    //     0x14c7f54: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c7f58: mov             x2, x0
    // 0x14c7f5c: ldur            x0, [fp, #-0x18]
    // 0x14c7f60: stur            x2, [fp, #-0x20]
    // 0x14c7f64: StoreField: r2->field_f = r0
    //     0x14c7f64: stur            w0, [x2, #0xf]
    // 0x14c7f68: r1 = <Widget>
    //     0x14c7f68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c7f6c: r0 = AllocateGrowableArray()
    //     0x14c7f6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c7f70: mov             x1, x0
    // 0x14c7f74: ldur            x0, [fp, #-0x20]
    // 0x14c7f78: stur            x1, [fp, #-0x18]
    // 0x14c7f7c: StoreField: r1->field_f = r0
    //     0x14c7f7c: stur            w0, [x1, #0xf]
    // 0x14c7f80: r2 = 2
    //     0x14c7f80: movz            x2, #0x2
    // 0x14c7f84: StoreField: r1->field_b = r2
    //     0x14c7f84: stur            w2, [x1, #0xb]
    // 0x14c7f88: r0 = Column()
    //     0x14c7f88: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c7f8c: mov             x1, x0
    // 0x14c7f90: r0 = Instance_Axis
    //     0x14c7f90: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c7f94: stur            x1, [fp, #-0x20]
    // 0x14c7f98: StoreField: r1->field_f = r0
    //     0x14c7f98: stur            w0, [x1, #0xf]
    // 0x14c7f9c: r2 = Instance_MainAxisAlignment
    //     0x14c7f9c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c7fa0: ldr             x2, [x2, #0xa08]
    // 0x14c7fa4: StoreField: r1->field_13 = r2
    //     0x14c7fa4: stur            w2, [x1, #0x13]
    // 0x14c7fa8: r3 = Instance_MainAxisSize
    //     0x14c7fa8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c7fac: ldr             x3, [x3, #0xa10]
    // 0x14c7fb0: ArrayStore: r1[0] = r3  ; List_4
    //     0x14c7fb0: stur            w3, [x1, #0x17]
    // 0x14c7fb4: r4 = Instance_CrossAxisAlignment
    //     0x14c7fb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c7fb8: ldr             x4, [x4, #0xa18]
    // 0x14c7fbc: StoreField: r1->field_1b = r4
    //     0x14c7fbc: stur            w4, [x1, #0x1b]
    // 0x14c7fc0: r5 = Instance_VerticalDirection
    //     0x14c7fc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c7fc4: ldr             x5, [x5, #0xa20]
    // 0x14c7fc8: StoreField: r1->field_23 = r5
    //     0x14c7fc8: stur            w5, [x1, #0x23]
    // 0x14c7fcc: r6 = Instance_Clip
    //     0x14c7fcc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c7fd0: ldr             x6, [x6, #0x38]
    // 0x14c7fd4: StoreField: r1->field_2b = r6
    //     0x14c7fd4: stur            w6, [x1, #0x2b]
    // 0x14c7fd8: StoreField: r1->field_2f = rZR
    //     0x14c7fd8: stur            xzr, [x1, #0x2f]
    // 0x14c7fdc: ldur            x7, [fp, #-0x18]
    // 0x14c7fe0: StoreField: r1->field_b = r7
    //     0x14c7fe0: stur            w7, [x1, #0xb]
    // 0x14c7fe4: r0 = Padding()
    //     0x14c7fe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c7fe8: mov             x1, x0
    // 0x14c7fec: r0 = Instance_EdgeInsets
    //     0x14c7fec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14c7ff0: ldr             x0, [x0, #0x1f0]
    // 0x14c7ff4: stur            x1, [fp, #-0x18]
    // 0x14c7ff8: StoreField: r1->field_f = r0
    //     0x14c7ff8: stur            w0, [x1, #0xf]
    // 0x14c7ffc: ldur            x2, [fp, #-0x20]
    // 0x14c8000: StoreField: r1->field_b = r2
    //     0x14c8000: stur            w2, [x1, #0xb]
    // 0x14c8004: r0 = Card()
    //     0x14c8004: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14c8008: mov             x2, x0
    // 0x14c800c: r0 = 0.000000
    //     0x14c800c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14c8010: stur            x2, [fp, #-0x20]
    // 0x14c8014: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c8014: stur            w0, [x2, #0x17]
    // 0x14c8018: ldur            x0, [fp, #-0x10]
    // 0x14c801c: StoreField: r2->field_1b = r0
    //     0x14c801c: stur            w0, [x2, #0x1b]
    // 0x14c8020: r0 = true
    //     0x14c8020: add             x0, NULL, #0x20  ; true
    // 0x14c8024: StoreField: r2->field_1f = r0
    //     0x14c8024: stur            w0, [x2, #0x1f]
    // 0x14c8028: ldur            x1, [fp, #-0x18]
    // 0x14c802c: StoreField: r2->field_2f = r1
    //     0x14c802c: stur            w1, [x2, #0x2f]
    // 0x14c8030: StoreField: r2->field_2b = r0
    //     0x14c8030: stur            w0, [x2, #0x2b]
    // 0x14c8034: r0 = Instance__CardVariant
    //     0x14c8034: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14c8038: ldr             x0, [x0, #0xa68]
    // 0x14c803c: StoreField: r2->field_33 = r0
    //     0x14c803c: stur            w0, [x2, #0x33]
    // 0x14c8040: ldur            x0, [fp, #-8]
    // 0x14c8044: LoadField: r1 = r0->field_13
    //     0x14c8044: ldur            w1, [x0, #0x13]
    // 0x14c8048: DecompressPointer r1
    //     0x14c8048: add             x1, x1, HEAP, lsl #32
    // 0x14c804c: r0 = of()
    //     0x14c804c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8050: LoadField: r1 = r0->field_87
    //     0x14c8050: ldur            w1, [x0, #0x87]
    // 0x14c8054: DecompressPointer r1
    //     0x14c8054: add             x1, x1, HEAP, lsl #32
    // 0x14c8058: LoadField: r0 = r1->field_7
    //     0x14c8058: ldur            w0, [x1, #7]
    // 0x14c805c: DecompressPointer r0
    //     0x14c805c: add             x0, x0, HEAP, lsl #32
    // 0x14c8060: r16 = 14.000000
    //     0x14c8060: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c8064: ldr             x16, [x16, #0x1d8]
    // 0x14c8068: r30 = Instance_Color
    //     0x14c8068: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c806c: stp             lr, x16, [SP]
    // 0x14c8070: mov             x1, x0
    // 0x14c8074: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c8074: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c8078: ldr             x4, [x4, #0xaa0]
    // 0x14c807c: r0 = copyWith()
    //     0x14c807c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8080: stur            x0, [fp, #-0x10]
    // 0x14c8084: r0 = Text()
    //     0x14c8084: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8088: mov             x1, x0
    // 0x14c808c: r0 = "Share Proofs"
    //     0x14c808c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33780] "Share Proofs"
    //     0x14c8090: ldr             x0, [x0, #0x780]
    // 0x14c8094: stur            x1, [fp, #-0x18]
    // 0x14c8098: StoreField: r1->field_b = r0
    //     0x14c8098: stur            w0, [x1, #0xb]
    // 0x14c809c: ldur            x0, [fp, #-0x10]
    // 0x14c80a0: StoreField: r1->field_13 = r0
    //     0x14c80a0: stur            w0, [x1, #0x13]
    // 0x14c80a4: r0 = Padding()
    //     0x14c80a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c80a8: mov             x2, x0
    // 0x14c80ac: r0 = Instance_EdgeInsets
    //     0x14c80ac: add             x0, PP, #0x40, lsl #12  ; [pp+0x408a8] Obj!EdgeInsets@d57d71
    //     0x14c80b0: ldr             x0, [x0, #0x8a8]
    // 0x14c80b4: stur            x2, [fp, #-0x10]
    // 0x14c80b8: StoreField: r2->field_f = r0
    //     0x14c80b8: stur            w0, [x2, #0xf]
    // 0x14c80bc: ldur            x0, [fp, #-0x18]
    // 0x14c80c0: StoreField: r2->field_b = r0
    //     0x14c80c0: stur            w0, [x2, #0xb]
    // 0x14c80c4: ldur            x0, [fp, #-8]
    // 0x14c80c8: LoadField: r1 = r0->field_f
    //     0x14c80c8: ldur            w1, [x0, #0xf]
    // 0x14c80cc: DecompressPointer r1
    //     0x14c80cc: add             x1, x1, HEAP, lsl #32
    // 0x14c80d0: r0 = controller()
    //     0x14c80d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c80d4: LoadField: r1 = r0->field_57
    //     0x14c80d4: ldur            w1, [x0, #0x57]
    // 0x14c80d8: DecompressPointer r1
    //     0x14c80d8: add             x1, x1, HEAP, lsl #32
    // 0x14c80dc: r0 = value()
    //     0x14c80dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c80e0: cmp             w0, NULL
    // 0x14c80e4: b.ne            #0x14c80f0
    // 0x14c80e8: r0 = Null
    //     0x14c80e8: mov             x0, NULL
    // 0x14c80ec: b               #0x14c80fc
    // 0x14c80f0: LoadField: r1 = r0->field_27
    //     0x14c80f0: ldur            w1, [x0, #0x27]
    // 0x14c80f4: DecompressPointer r1
    //     0x14c80f4: add             x1, x1, HEAP, lsl #32
    // 0x14c80f8: mov             x0, x1
    // 0x14c80fc: cmp             w0, NULL
    // 0x14c8100: b.ne            #0x14c8108
    // 0x14c8104: r0 = ""
    //     0x14c8104: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c8108: ldur            x2, [fp, #-8]
    // 0x14c810c: stur            x0, [fp, #-0x18]
    // 0x14c8110: LoadField: r1 = r2->field_13
    //     0x14c8110: ldur            w1, [x2, #0x13]
    // 0x14c8114: DecompressPointer r1
    //     0x14c8114: add             x1, x1, HEAP, lsl #32
    // 0x14c8118: r0 = of()
    //     0x14c8118: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c811c: LoadField: r1 = r0->field_87
    //     0x14c811c: ldur            w1, [x0, #0x87]
    // 0x14c8120: DecompressPointer r1
    //     0x14c8120: add             x1, x1, HEAP, lsl #32
    // 0x14c8124: LoadField: r0 = r1->field_2b
    //     0x14c8124: ldur            w0, [x1, #0x2b]
    // 0x14c8128: DecompressPointer r0
    //     0x14c8128: add             x0, x0, HEAP, lsl #32
    // 0x14c812c: stur            x0, [fp, #-0x28]
    // 0x14c8130: r1 = Instance_Color
    //     0x14c8130: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8134: d0 = 0.400000
    //     0x14c8134: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14c8138: r0 = withOpacity()
    //     0x14c8138: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c813c: r16 = 12.000000
    //     0x14c813c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c8140: ldr             x16, [x16, #0x9e8]
    // 0x14c8144: stp             x16, x0, [SP]
    // 0x14c8148: ldur            x1, [fp, #-0x28]
    // 0x14c814c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c814c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c8150: ldr             x4, [x4, #0x9b8]
    // 0x14c8154: r0 = copyWith()
    //     0x14c8154: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8158: stur            x0, [fp, #-0x28]
    // 0x14c815c: r0 = Text()
    //     0x14c815c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8160: mov             x2, x0
    // 0x14c8164: ldur            x0, [fp, #-0x18]
    // 0x14c8168: stur            x2, [fp, #-0x30]
    // 0x14c816c: StoreField: r2->field_b = r0
    //     0x14c816c: stur            w0, [x2, #0xb]
    // 0x14c8170: ldur            x0, [fp, #-0x28]
    // 0x14c8174: StoreField: r2->field_13 = r0
    //     0x14c8174: stur            w0, [x2, #0x13]
    // 0x14c8178: ldur            x0, [fp, #-8]
    // 0x14c817c: LoadField: r1 = r0->field_f
    //     0x14c817c: ldur            w1, [x0, #0xf]
    // 0x14c8180: DecompressPointer r1
    //     0x14c8180: add             x1, x1, HEAP, lsl #32
    // 0x14c8184: r0 = controller()
    //     0x14c8184: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c8188: LoadField: r1 = r0->field_57
    //     0x14c8188: ldur            w1, [x0, #0x57]
    // 0x14c818c: DecompressPointer r1
    //     0x14c818c: add             x1, x1, HEAP, lsl #32
    // 0x14c8190: r0 = value()
    //     0x14c8190: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c8194: cmp             w0, NULL
    // 0x14c8198: b.ne            #0x14c81a4
    // 0x14c819c: r3 = Null
    //     0x14c819c: mov             x3, NULL
    // 0x14c81a0: b               #0x14c81b4
    // 0x14c81a4: LoadField: r1 = r0->field_2b
    //     0x14c81a4: ldur            w1, [x0, #0x2b]
    // 0x14c81a8: DecompressPointer r1
    //     0x14c81a8: add             x1, x1, HEAP, lsl #32
    // 0x14c81ac: LoadField: r0 = r1->field_b
    //     0x14c81ac: ldur            w0, [x1, #0xb]
    // 0x14c81b0: mov             x3, x0
    // 0x14c81b4: ldur            x0, [fp, #-8]
    // 0x14c81b8: mov             x2, x0
    // 0x14c81bc: stur            x3, [fp, #-0x18]
    // 0x14c81c0: r1 = Function '<anonymous closure>':.
    //     0x14c81c0: add             x1, PP, #0x42, lsl #12  ; [pp+0x42600] AnonymousClosure: (0x14c8fd8), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x14c7518)
    //     0x14c81c4: ldr             x1, [x1, #0x600]
    // 0x14c81c8: r0 = AllocateClosure()
    //     0x14c81c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c81cc: stur            x0, [fp, #-0x28]
    // 0x14c81d0: r0 = ListView()
    //     0x14c81d0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14c81d4: stur            x0, [fp, #-0x38]
    // 0x14c81d8: r16 = Instance_NeverScrollableScrollPhysics
    //     0x14c81d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14c81dc: ldr             x16, [x16, #0x1c8]
    // 0x14c81e0: r30 = true
    //     0x14c81e0: add             lr, NULL, #0x20  ; true
    // 0x14c81e4: stp             lr, x16, [SP, #8]
    // 0x14c81e8: r16 = true
    //     0x14c81e8: add             x16, NULL, #0x20  ; true
    // 0x14c81ec: str             x16, [SP]
    // 0x14c81f0: mov             x1, x0
    // 0x14c81f4: ldur            x2, [fp, #-0x28]
    // 0x14c81f8: ldur            x3, [fp, #-0x18]
    // 0x14c81fc: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x3, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x14c81fc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32058] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x3, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x14c8200: ldr             x4, [x4, #0x58]
    // 0x14c8204: r0 = ListView.builder()
    //     0x14c8204: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14c8208: r0 = Padding()
    //     0x14c8208: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c820c: mov             x2, x0
    // 0x14c8210: r0 = Instance_EdgeInsets
    //     0x14c8210: add             x0, PP, #0x33, lsl #12  ; [pp+0x33848] Obj!EdgeInsets@d58071
    //     0x14c8214: ldr             x0, [x0, #0x848]
    // 0x14c8218: stur            x2, [fp, #-0x18]
    // 0x14c821c: StoreField: r2->field_f = r0
    //     0x14c821c: stur            w0, [x2, #0xf]
    // 0x14c8220: ldur            x0, [fp, #-0x38]
    // 0x14c8224: StoreField: r2->field_b = r0
    //     0x14c8224: stur            w0, [x2, #0xb]
    // 0x14c8228: ldur            x0, [fp, #-8]
    // 0x14c822c: LoadField: r1 = r0->field_13
    //     0x14c822c: ldur            w1, [x0, #0x13]
    // 0x14c8230: DecompressPointer r1
    //     0x14c8230: add             x1, x1, HEAP, lsl #32
    // 0x14c8234: r0 = of()
    //     0x14c8234: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8238: LoadField: r1 = r0->field_87
    //     0x14c8238: ldur            w1, [x0, #0x87]
    // 0x14c823c: DecompressPointer r1
    //     0x14c823c: add             x1, x1, HEAP, lsl #32
    // 0x14c8240: LoadField: r0 = r1->field_7
    //     0x14c8240: ldur            w0, [x1, #7]
    // 0x14c8244: DecompressPointer r0
    //     0x14c8244: add             x0, x0, HEAP, lsl #32
    // 0x14c8248: r16 = 14.000000
    //     0x14c8248: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c824c: ldr             x16, [x16, #0x1d8]
    // 0x14c8250: str             x16, [SP]
    // 0x14c8254: mov             x1, x0
    // 0x14c8258: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14c8258: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14c825c: ldr             x4, [x4, #0x798]
    // 0x14c8260: r0 = copyWith()
    //     0x14c8260: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8264: stur            x0, [fp, #-0x28]
    // 0x14c8268: r0 = TextSpan()
    //     0x14c8268: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c826c: mov             x2, x0
    // 0x14c8270: r0 = "Remark"
    //     0x14c8270: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a0] "Remark"
    //     0x14c8274: ldr             x0, [x0, #0x7a0]
    // 0x14c8278: stur            x2, [fp, #-0x38]
    // 0x14c827c: StoreField: r2->field_b = r0
    //     0x14c827c: stur            w0, [x2, #0xb]
    // 0x14c8280: r0 = Instance__DeferringMouseCursor
    //     0x14c8280: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c8284: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c8284: stur            w0, [x2, #0x17]
    // 0x14c8288: ldur            x1, [fp, #-0x28]
    // 0x14c828c: StoreField: r2->field_7 = r1
    //     0x14c828c: stur            w1, [x2, #7]
    // 0x14c8290: ldur            x3, [fp, #-8]
    // 0x14c8294: LoadField: r1 = r3->field_13
    //     0x14c8294: ldur            w1, [x3, #0x13]
    // 0x14c8298: DecompressPointer r1
    //     0x14c8298: add             x1, x1, HEAP, lsl #32
    // 0x14c829c: r0 = of()
    //     0x14c829c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c82a0: LoadField: r1 = r0->field_87
    //     0x14c82a0: ldur            w1, [x0, #0x87]
    // 0x14c82a4: DecompressPointer r1
    //     0x14c82a4: add             x1, x1, HEAP, lsl #32
    // 0x14c82a8: LoadField: r0 = r1->field_7
    //     0x14c82a8: ldur            w0, [x1, #7]
    // 0x14c82ac: DecompressPointer r0
    //     0x14c82ac: add             x0, x0, HEAP, lsl #32
    // 0x14c82b0: r16 = 14.000000
    //     0x14c82b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c82b4: ldr             x16, [x16, #0x1d8]
    // 0x14c82b8: r30 = Instance_Color
    //     0x14c82b8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14c82bc: ldr             lr, [lr, #0x50]
    // 0x14c82c0: stp             lr, x16, [SP]
    // 0x14c82c4: mov             x1, x0
    // 0x14c82c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c82c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c82cc: ldr             x4, [x4, #0xaa0]
    // 0x14c82d0: r0 = copyWith()
    //     0x14c82d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c82d4: stur            x0, [fp, #-0x28]
    // 0x14c82d8: r0 = TextSpan()
    //     0x14c82d8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c82dc: mov             x3, x0
    // 0x14c82e0: r0 = "*"
    //     0x14c82e0: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "*"
    //     0x14c82e4: ldr             x0, [x0, #0x7a8]
    // 0x14c82e8: stur            x3, [fp, #-0x40]
    // 0x14c82ec: StoreField: r3->field_b = r0
    //     0x14c82ec: stur            w0, [x3, #0xb]
    // 0x14c82f0: r0 = Instance__DeferringMouseCursor
    //     0x14c82f0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c82f4: ArrayStore: r3[0] = r0  ; List_4
    //     0x14c82f4: stur            w0, [x3, #0x17]
    // 0x14c82f8: ldur            x1, [fp, #-0x28]
    // 0x14c82fc: StoreField: r3->field_7 = r1
    //     0x14c82fc: stur            w1, [x3, #7]
    // 0x14c8300: r1 = Null
    //     0x14c8300: mov             x1, NULL
    // 0x14c8304: r2 = 4
    //     0x14c8304: movz            x2, #0x4
    // 0x14c8308: r0 = AllocateArray()
    //     0x14c8308: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c830c: mov             x2, x0
    // 0x14c8310: ldur            x0, [fp, #-0x38]
    // 0x14c8314: stur            x2, [fp, #-0x28]
    // 0x14c8318: StoreField: r2->field_f = r0
    //     0x14c8318: stur            w0, [x2, #0xf]
    // 0x14c831c: ldur            x0, [fp, #-0x40]
    // 0x14c8320: StoreField: r2->field_13 = r0
    //     0x14c8320: stur            w0, [x2, #0x13]
    // 0x14c8324: r1 = <InlineSpan>
    //     0x14c8324: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14c8328: ldr             x1, [x1, #0xe40]
    // 0x14c832c: r0 = AllocateGrowableArray()
    //     0x14c832c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c8330: mov             x1, x0
    // 0x14c8334: ldur            x0, [fp, #-0x28]
    // 0x14c8338: stur            x1, [fp, #-0x38]
    // 0x14c833c: StoreField: r1->field_f = r0
    //     0x14c833c: stur            w0, [x1, #0xf]
    // 0x14c8340: r2 = 4
    //     0x14c8340: movz            x2, #0x4
    // 0x14c8344: StoreField: r1->field_b = r2
    //     0x14c8344: stur            w2, [x1, #0xb]
    // 0x14c8348: r0 = TextSpan()
    //     0x14c8348: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c834c: mov             x1, x0
    // 0x14c8350: ldur            x0, [fp, #-0x38]
    // 0x14c8354: stur            x1, [fp, #-0x28]
    // 0x14c8358: StoreField: r1->field_f = r0
    //     0x14c8358: stur            w0, [x1, #0xf]
    // 0x14c835c: r0 = Instance__DeferringMouseCursor
    //     0x14c835c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c8360: ArrayStore: r1[0] = r0  ; List_4
    //     0x14c8360: stur            w0, [x1, #0x17]
    // 0x14c8364: r0 = RichText()
    //     0x14c8364: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14c8368: mov             x1, x0
    // 0x14c836c: ldur            x2, [fp, #-0x28]
    // 0x14c8370: stur            x0, [fp, #-0x28]
    // 0x14c8374: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14c8374: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14c8378: r0 = RichText()
    //     0x14c8378: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14c837c: r0 = Padding()
    //     0x14c837c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8380: mov             x2, x0
    // 0x14c8384: r0 = Instance_EdgeInsets
    //     0x14c8384: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x14c8388: ldr             x0, [x0, #0x868]
    // 0x14c838c: stur            x2, [fp, #-0x38]
    // 0x14c8390: StoreField: r2->field_f = r0
    //     0x14c8390: stur            w0, [x2, #0xf]
    // 0x14c8394: ldur            x1, [fp, #-0x28]
    // 0x14c8398: StoreField: r2->field_b = r1
    //     0x14c8398: stur            w1, [x2, #0xb]
    // 0x14c839c: ldur            x3, [fp, #-8]
    // 0x14c83a0: LoadField: r1 = r3->field_f
    //     0x14c83a0: ldur            w1, [x3, #0xf]
    // 0x14c83a4: DecompressPointer r1
    //     0x14c83a4: add             x1, x1, HEAP, lsl #32
    // 0x14c83a8: r0 = controller()
    //     0x14c83a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c83ac: LoadField: r2 = r0->field_4f
    //     0x14c83ac: ldur            w2, [x0, #0x4f]
    // 0x14c83b0: DecompressPointer r2
    //     0x14c83b0: add             x2, x2, HEAP, lsl #32
    // 0x14c83b4: ldur            x0, [fp, #-8]
    // 0x14c83b8: stur            x2, [fp, #-0x28]
    // 0x14c83bc: LoadField: r1 = r0->field_f
    //     0x14c83bc: ldur            w1, [x0, #0xf]
    // 0x14c83c0: DecompressPointer r1
    //     0x14c83c0: add             x1, x1, HEAP, lsl #32
    // 0x14c83c4: r0 = controller()
    //     0x14c83c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c83c8: stur            x0, [fp, #-0x40]
    // 0x14c83cc: r0 = LengthLimitingTextInputFormatter()
    //     0x14c83cc: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14c83d0: mov             x3, x0
    // 0x14c83d4: r0 = 300
    //     0x14c83d4: movz            x0, #0x12c
    // 0x14c83d8: stur            x3, [fp, #-0x48]
    // 0x14c83dc: StoreField: r3->field_7 = r0
    //     0x14c83dc: stur            w0, [x3, #7]
    // 0x14c83e0: r1 = Null
    //     0x14c83e0: mov             x1, NULL
    // 0x14c83e4: r2 = 2
    //     0x14c83e4: movz            x2, #0x2
    // 0x14c83e8: r0 = AllocateArray()
    //     0x14c83e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c83ec: mov             x2, x0
    // 0x14c83f0: ldur            x0, [fp, #-0x48]
    // 0x14c83f4: stur            x2, [fp, #-0x50]
    // 0x14c83f8: StoreField: r2->field_f = r0
    //     0x14c83f8: stur            w0, [x2, #0xf]
    // 0x14c83fc: r1 = <TextInputFormatter>
    //     0x14c83fc: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14c8400: ldr             x1, [x1, #0x7b0]
    // 0x14c8404: r0 = AllocateGrowableArray()
    //     0x14c8404: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c8408: mov             x2, x0
    // 0x14c840c: ldur            x0, [fp, #-0x50]
    // 0x14c8410: stur            x2, [fp, #-0x48]
    // 0x14c8414: StoreField: r2->field_f = r0
    //     0x14c8414: stur            w0, [x2, #0xf]
    // 0x14c8418: r0 = 2
    //     0x14c8418: movz            x0, #0x2
    // 0x14c841c: StoreField: r2->field_b = r0
    //     0x14c841c: stur            w0, [x2, #0xb]
    // 0x14c8420: ldur            x0, [fp, #-8]
    // 0x14c8424: LoadField: r1 = r0->field_13
    //     0x14c8424: ldur            w1, [x0, #0x13]
    // 0x14c8428: DecompressPointer r1
    //     0x14c8428: add             x1, x1, HEAP, lsl #32
    // 0x14c842c: r0 = of()
    //     0x14c842c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8430: LoadField: r1 = r0->field_87
    //     0x14c8430: ldur            w1, [x0, #0x87]
    // 0x14c8434: DecompressPointer r1
    //     0x14c8434: add             x1, x1, HEAP, lsl #32
    // 0x14c8438: LoadField: r0 = r1->field_2b
    //     0x14c8438: ldur            w0, [x1, #0x2b]
    // 0x14c843c: DecompressPointer r0
    //     0x14c843c: add             x0, x0, HEAP, lsl #32
    // 0x14c8440: ldur            x2, [fp, #-8]
    // 0x14c8444: stur            x0, [fp, #-0x50]
    // 0x14c8448: LoadField: r1 = r2->field_13
    //     0x14c8448: ldur            w1, [x2, #0x13]
    // 0x14c844c: DecompressPointer r1
    //     0x14c844c: add             x1, x1, HEAP, lsl #32
    // 0x14c8450: r0 = of()
    //     0x14c8450: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8454: LoadField: r1 = r0->field_5b
    //     0x14c8454: ldur            w1, [x0, #0x5b]
    // 0x14c8458: DecompressPointer r1
    //     0x14c8458: add             x1, x1, HEAP, lsl #32
    // 0x14c845c: r16 = 12.000000
    //     0x14c845c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c8460: ldr             x16, [x16, #0x9e8]
    // 0x14c8464: stp             x16, x1, [SP]
    // 0x14c8468: ldur            x1, [fp, #-0x50]
    // 0x14c846c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c846c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c8470: ldr             x4, [x4, #0x9b8]
    // 0x14c8474: r0 = copyWith()
    //     0x14c8474: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8478: ldur            x2, [fp, #-8]
    // 0x14c847c: stur            x0, [fp, #-0x50]
    // 0x14c8480: LoadField: r1 = r2->field_f
    //     0x14c8480: ldur            w1, [x2, #0xf]
    // 0x14c8484: DecompressPointer r1
    //     0x14c8484: add             x1, x1, HEAP, lsl #32
    // 0x14c8488: r0 = controller()
    //     0x14c8488: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c848c: LoadField: r2 = r0->field_53
    //     0x14c848c: ldur            w2, [x0, #0x53]
    // 0x14c8490: DecompressPointer r2
    //     0x14c8490: add             x2, x2, HEAP, lsl #32
    // 0x14c8494: ldur            x0, [fp, #-8]
    // 0x14c8498: stur            x2, [fp, #-0x58]
    // 0x14c849c: LoadField: r1 = r0->field_13
    //     0x14c849c: ldur            w1, [x0, #0x13]
    // 0x14c84a0: DecompressPointer r1
    //     0x14c84a0: add             x1, x1, HEAP, lsl #32
    // 0x14c84a4: r0 = getTextFormFieldInputDecorationCircle()
    //     0x14c84a4: bl              #0xac2dd8  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircle
    // 0x14c84a8: r1 = Instance_Color
    //     0x14c84a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c84ac: d0 = 0.030000
    //     0x14c84ac: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14c84b0: ldr             d0, [x17, #0x238]
    // 0x14c84b4: stur            x0, [fp, #-0x60]
    // 0x14c84b8: r0 = withOpacity()
    //     0x14c84b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c84bc: ldur            x2, [fp, #-8]
    // 0x14c84c0: stur            x0, [fp, #-0x68]
    // 0x14c84c4: LoadField: r1 = r2->field_13
    //     0x14c84c4: ldur            w1, [x2, #0x13]
    // 0x14c84c8: DecompressPointer r1
    //     0x14c84c8: add             x1, x1, HEAP, lsl #32
    // 0x14c84cc: r0 = of()
    //     0x14c84cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c84d0: LoadField: r1 = r0->field_87
    //     0x14c84d0: ldur            w1, [x0, #0x87]
    // 0x14c84d4: DecompressPointer r1
    //     0x14c84d4: add             x1, x1, HEAP, lsl #32
    // 0x14c84d8: LoadField: r0 = r1->field_2b
    //     0x14c84d8: ldur            w0, [x1, #0x2b]
    // 0x14c84dc: DecompressPointer r0
    //     0x14c84dc: add             x0, x0, HEAP, lsl #32
    // 0x14c84e0: stur            x0, [fp, #-0x70]
    // 0x14c84e4: r1 = Instance_Color
    //     0x14c84e4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c84e8: d0 = 0.400000
    //     0x14c84e8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14c84ec: r0 = withOpacity()
    //     0x14c84ec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c84f0: r16 = 12.000000
    //     0x14c84f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c84f4: ldr             x16, [x16, #0x9e8]
    // 0x14c84f8: stp             x16, x0, [SP]
    // 0x14c84fc: ldur            x1, [fp, #-0x70]
    // 0x14c8500: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c8500: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c8504: ldr             x4, [x4, #0x9b8]
    // 0x14c8508: r0 = copyWith()
    //     0x14c8508: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c850c: ldur            x2, [fp, #-8]
    // 0x14c8510: stur            x0, [fp, #-0x70]
    // 0x14c8514: LoadField: r1 = r2->field_13
    //     0x14c8514: ldur            w1, [x2, #0x13]
    // 0x14c8518: DecompressPointer r1
    //     0x14c8518: add             x1, x1, HEAP, lsl #32
    // 0x14c851c: r0 = of()
    //     0x14c851c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8520: LoadField: r1 = r0->field_87
    //     0x14c8520: ldur            w1, [x0, #0x87]
    // 0x14c8524: DecompressPointer r1
    //     0x14c8524: add             x1, x1, HEAP, lsl #32
    // 0x14c8528: LoadField: r0 = r1->field_2b
    //     0x14c8528: ldur            w0, [x1, #0x2b]
    // 0x14c852c: DecompressPointer r0
    //     0x14c852c: add             x0, x0, HEAP, lsl #32
    // 0x14c8530: r16 = Instance_Color
    //     0x14c8530: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x14c8534: ldr             x16, [x16, #0x7c0]
    // 0x14c8538: r30 = 12.000000
    //     0x14c8538: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c853c: ldr             lr, [lr, #0x9e8]
    // 0x14c8540: stp             lr, x16, [SP]
    // 0x14c8544: mov             x1, x0
    // 0x14c8548: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14c8548: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14c854c: ldr             x4, [x4, #0x9b8]
    // 0x14c8550: r0 = copyWith()
    //     0x14c8550: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8554: ldur            x16, [fp, #-0x68]
    // 0x14c8558: r30 = "Describe your issue in detail"
    //     0x14c8558: add             lr, PP, #0x33, lsl #12  ; [pp+0x337c8] "Describe your issue in detail"
    //     0x14c855c: ldr             lr, [lr, #0x7c8]
    // 0x14c8560: stp             lr, x16, [SP, #0x10]
    // 0x14c8564: ldur            x16, [fp, #-0x70]
    // 0x14c8568: stp             x0, x16, [SP]
    // 0x14c856c: ldur            x1, [fp, #-0x60]
    // 0x14c8570: r4 = const [0, 0x5, 0x4, 0x1, errorStyle, 0x4, fillColor, 0x1, hintStyle, 0x3, hintText, 0x2, null]
    //     0x14c8570: add             x4, PP, #0x42, lsl #12  ; [pp+0x42608] List(13) [0, 0x5, 0x4, 0x1, "errorStyle", 0x4, "fillColor", 0x1, "hintStyle", 0x3, "hintText", 0x2, Null]
    //     0x14c8574: ldr             x4, [x4, #0x608]
    // 0x14c8578: r0 = copyWith()
    //     0x14c8578: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x14c857c: ldur            x2, [fp, #-0x40]
    // 0x14c8580: r1 = Function 'validateRemark':.
    //     0x14c8580: add             x1, PP, #0x33, lsl #12  ; [pp+0x337d8] AnonymousClosure: (0x1449034), in [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::validateRemark (0x1447158)
    //     0x14c8584: ldr             x1, [x1, #0x7d8]
    // 0x14c8588: stur            x0, [fp, #-0x40]
    // 0x14c858c: r0 = AllocateClosure()
    //     0x14c858c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c8590: ldur            x2, [fp, #-8]
    // 0x14c8594: r1 = Function '<anonymous closure>':.
    //     0x14c8594: add             x1, PP, #0x42, lsl #12  ; [pp+0x42610] AnonymousClosure: (0x14454e0), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x1509194)
    //     0x14c8598: ldr             x1, [x1, #0x610]
    // 0x14c859c: stur            x0, [fp, #-0x60]
    // 0x14c85a0: r0 = AllocateClosure()
    //     0x14c85a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c85a4: r1 = <String>
    //     0x14c85a4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14c85a8: stur            x0, [fp, #-0x68]
    // 0x14c85ac: r0 = TextFormField()
    //     0x14c85ac: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x14c85b0: stur            x0, [fp, #-0x70]
    // 0x14c85b4: ldur            x16, [fp, #-0x60]
    // 0x14c85b8: r30 = false
    //     0x14c85b8: add             lr, NULL, #0x30  ; false
    // 0x14c85bc: stp             lr, x16, [SP, #0x40]
    // 0x14c85c0: r16 = Instance_AutovalidateMode
    //     0x14c85c0: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x14c85c4: ldr             x16, [x16, #0x7e8]
    // 0x14c85c8: ldur            lr, [fp, #-0x48]
    // 0x14c85cc: stp             lr, x16, [SP, #0x30]
    // 0x14c85d0: r16 = Instance_TextInputType
    //     0x14c85d0: add             x16, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x14c85d4: ldr             x16, [x16, #0x7f0]
    // 0x14c85d8: r30 = 6
    //     0x14c85d8: movz            lr, #0x6
    // 0x14c85dc: stp             lr, x16, [SP, #0x20]
    // 0x14c85e0: r16 = 10
    //     0x14c85e0: movz            x16, #0xa
    // 0x14c85e4: ldur            lr, [fp, #-0x50]
    // 0x14c85e8: stp             lr, x16, [SP, #0x10]
    // 0x14c85ec: ldur            x16, [fp, #-0x58]
    // 0x14c85f0: ldur            lr, [fp, #-0x68]
    // 0x14c85f4: stp             lr, x16, [SP]
    // 0x14c85f8: mov             x1, x0
    // 0x14c85fc: ldur            x2, [fp, #-0x40]
    // 0x14c8600: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x4, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x5, keyboardType, 0x6, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, validator, 0x2, null]
    //     0x14c8600: add             x4, PP, #0x33, lsl #12  ; [pp+0x337f8] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x4, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x5, "keyboardType", 0x6, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "validator", 0x2, Null]
    //     0x14c8604: ldr             x4, [x4, #0x7f8]
    // 0x14c8608: r0 = TextFormField()
    //     0x14c8608: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14c860c: r0 = Form()
    //     0x14c860c: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14c8610: mov             x1, x0
    // 0x14c8614: ldur            x0, [fp, #-0x70]
    // 0x14c8618: stur            x1, [fp, #-0x40]
    // 0x14c861c: StoreField: r1->field_b = r0
    //     0x14c861c: stur            w0, [x1, #0xb]
    // 0x14c8620: r0 = Instance_AutovalidateMode
    //     0x14c8620: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14c8624: ldr             x0, [x0, #0x800]
    // 0x14c8628: StoreField: r1->field_23 = r0
    //     0x14c8628: stur            w0, [x1, #0x23]
    // 0x14c862c: ldur            x0, [fp, #-0x28]
    // 0x14c8630: StoreField: r1->field_7 = r0
    //     0x14c8630: stur            w0, [x1, #7]
    // 0x14c8634: r0 = Padding()
    //     0x14c8634: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8638: mov             x2, x0
    // 0x14c863c: r0 = Instance_EdgeInsets
    //     0x14c863c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x14c8640: ldr             x0, [x0, #0x868]
    // 0x14c8644: stur            x2, [fp, #-0x28]
    // 0x14c8648: StoreField: r2->field_f = r0
    //     0x14c8648: stur            w0, [x2, #0xf]
    // 0x14c864c: ldur            x1, [fp, #-0x40]
    // 0x14c8650: StoreField: r2->field_b = r1
    //     0x14c8650: stur            w1, [x2, #0xb]
    // 0x14c8654: ldur            x3, [fp, #-8]
    // 0x14c8658: LoadField: r1 = r3->field_f
    //     0x14c8658: ldur            w1, [x3, #0xf]
    // 0x14c865c: DecompressPointer r1
    //     0x14c865c: add             x1, x1, HEAP, lsl #32
    // 0x14c8660: r0 = controller()
    //     0x14c8660: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c8664: LoadField: r1 = r0->field_57
    //     0x14c8664: ldur            w1, [x0, #0x57]
    // 0x14c8668: DecompressPointer r1
    //     0x14c8668: add             x1, x1, HEAP, lsl #32
    // 0x14c866c: r0 = value()
    //     0x14c866c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c8670: cmp             w0, NULL
    // 0x14c8674: b.ne            #0x14c8680
    // 0x14c8678: r0 = Null
    //     0x14c8678: mov             x0, NULL
    // 0x14c867c: b               #0x14c868c
    // 0x14c8680: LoadField: r1 = r0->field_23
    //     0x14c8680: ldur            w1, [x0, #0x23]
    // 0x14c8684: DecompressPointer r1
    //     0x14c8684: add             x1, x1, HEAP, lsl #32
    // 0x14c8688: mov             x0, x1
    // 0x14c868c: cmp             w0, NULL
    // 0x14c8690: b.ne            #0x14c869c
    // 0x14c8694: r2 = false
    //     0x14c8694: add             x2, NULL, #0x30  ; false
    // 0x14c8698: b               #0x14c86a0
    // 0x14c869c: mov             x2, x0
    // 0x14c86a0: ldur            x0, [fp, #-8]
    // 0x14c86a4: stur            x2, [fp, #-0x40]
    // 0x14c86a8: LoadField: r1 = r0->field_f
    //     0x14c86a8: ldur            w1, [x0, #0xf]
    // 0x14c86ac: DecompressPointer r1
    //     0x14c86ac: add             x1, x1, HEAP, lsl #32
    // 0x14c86b0: r0 = controller()
    //     0x14c86b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c86b4: LoadField: r1 = r0->field_57
    //     0x14c86b4: ldur            w1, [x0, #0x57]
    // 0x14c86b8: DecompressPointer r1
    //     0x14c86b8: add             x1, x1, HEAP, lsl #32
    // 0x14c86bc: r0 = value()
    //     0x14c86bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c86c0: cmp             w0, NULL
    // 0x14c86c4: b.ne            #0x14c86d0
    // 0x14c86c8: r0 = Null
    //     0x14c86c8: mov             x0, NULL
    // 0x14c86cc: b               #0x14c86f0
    // 0x14c86d0: LoadField: r1 = r0->field_13
    //     0x14c86d0: ldur            w1, [x0, #0x13]
    // 0x14c86d4: DecompressPointer r1
    //     0x14c86d4: add             x1, x1, HEAP, lsl #32
    // 0x14c86d8: cmp             w1, NULL
    // 0x14c86dc: b.ne            #0x14c86e8
    // 0x14c86e0: r0 = Null
    //     0x14c86e0: mov             x0, NULL
    // 0x14c86e4: b               #0x14c86f0
    // 0x14c86e8: LoadField: r0 = r1->field_7
    //     0x14c86e8: ldur            w0, [x1, #7]
    // 0x14c86ec: DecompressPointer r0
    //     0x14c86ec: add             x0, x0, HEAP, lsl #32
    // 0x14c86f0: cmp             w0, NULL
    // 0x14c86f4: b.ne            #0x14c8700
    // 0x14c86f8: r2 = ""
    //     0x14c86f8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c86fc: b               #0x14c8704
    // 0x14c8700: mov             x2, x0
    // 0x14c8704: ldur            x0, [fp, #-8]
    // 0x14c8708: stur            x2, [fp, #-0x48]
    // 0x14c870c: LoadField: r1 = r0->field_13
    //     0x14c870c: ldur            w1, [x0, #0x13]
    // 0x14c8710: DecompressPointer r1
    //     0x14c8710: add             x1, x1, HEAP, lsl #32
    // 0x14c8714: r0 = of()
    //     0x14c8714: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8718: LoadField: r1 = r0->field_87
    //     0x14c8718: ldur            w1, [x0, #0x87]
    // 0x14c871c: DecompressPointer r1
    //     0x14c871c: add             x1, x1, HEAP, lsl #32
    // 0x14c8720: LoadField: r0 = r1->field_7
    //     0x14c8720: ldur            w0, [x1, #7]
    // 0x14c8724: DecompressPointer r0
    //     0x14c8724: add             x0, x0, HEAP, lsl #32
    // 0x14c8728: r16 = 14.000000
    //     0x14c8728: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c872c: ldr             x16, [x16, #0x1d8]
    // 0x14c8730: r30 = Instance_Color
    //     0x14c8730: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8734: stp             lr, x16, [SP]
    // 0x14c8738: mov             x1, x0
    // 0x14c873c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c873c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c8740: ldr             x4, [x4, #0xaa0]
    // 0x14c8744: r0 = copyWith()
    //     0x14c8744: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8748: stur            x0, [fp, #-0x50]
    // 0x14c874c: r0 = Text()
    //     0x14c874c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8750: mov             x1, x0
    // 0x14c8754: ldur            x0, [fp, #-0x48]
    // 0x14c8758: stur            x1, [fp, #-0x58]
    // 0x14c875c: StoreField: r1->field_b = r0
    //     0x14c875c: stur            w0, [x1, #0xb]
    // 0x14c8760: ldur            x0, [fp, #-0x50]
    // 0x14c8764: StoreField: r1->field_13 = r0
    //     0x14c8764: stur            w0, [x1, #0x13]
    // 0x14c8768: r0 = Padding()
    //     0x14c8768: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c876c: mov             x2, x0
    // 0x14c8770: r0 = Instance_EdgeInsets
    //     0x14c8770: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x14c8774: ldr             x0, [x0, #0x668]
    // 0x14c8778: stur            x2, [fp, #-0x48]
    // 0x14c877c: StoreField: r2->field_f = r0
    //     0x14c877c: stur            w0, [x2, #0xf]
    // 0x14c8780: ldur            x0, [fp, #-0x58]
    // 0x14c8784: StoreField: r2->field_b = r0
    //     0x14c8784: stur            w0, [x2, #0xb]
    // 0x14c8788: ldur            x0, [fp, #-8]
    // 0x14c878c: LoadField: r1 = r0->field_f
    //     0x14c878c: ldur            w1, [x0, #0xf]
    // 0x14c8790: DecompressPointer r1
    //     0x14c8790: add             x1, x1, HEAP, lsl #32
    // 0x14c8794: r0 = controller()
    //     0x14c8794: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c8798: LoadField: r1 = r0->field_57
    //     0x14c8798: ldur            w1, [x0, #0x57]
    // 0x14c879c: DecompressPointer r1
    //     0x14c879c: add             x1, x1, HEAP, lsl #32
    // 0x14c87a0: r0 = value()
    //     0x14c87a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c87a4: cmp             w0, NULL
    // 0x14c87a8: b.ne            #0x14c87b4
    // 0x14c87ac: r0 = Null
    //     0x14c87ac: mov             x0, NULL
    // 0x14c87b0: b               #0x14c87d4
    // 0x14c87b4: LoadField: r1 = r0->field_13
    //     0x14c87b4: ldur            w1, [x0, #0x13]
    // 0x14c87b8: DecompressPointer r1
    //     0x14c87b8: add             x1, x1, HEAP, lsl #32
    // 0x14c87bc: cmp             w1, NULL
    // 0x14c87c0: b.ne            #0x14c87cc
    // 0x14c87c4: r0 = Null
    //     0x14c87c4: mov             x0, NULL
    // 0x14c87c8: b               #0x14c87d4
    // 0x14c87cc: LoadField: r0 = r1->field_b
    //     0x14c87cc: ldur            w0, [x1, #0xb]
    // 0x14c87d0: DecompressPointer r0
    //     0x14c87d0: add             x0, x0, HEAP, lsl #32
    // 0x14c87d4: cmp             w0, NULL
    // 0x14c87d8: b.ne            #0x14c87e4
    // 0x14c87dc: r3 = ""
    //     0x14c87dc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c87e0: b               #0x14c87e8
    // 0x14c87e4: mov             x3, x0
    // 0x14c87e8: ldur            x2, [fp, #-8]
    // 0x14c87ec: ldur            x0, [fp, #-0x48]
    // 0x14c87f0: stur            x3, [fp, #-0x50]
    // 0x14c87f4: LoadField: r1 = r2->field_13
    //     0x14c87f4: ldur            w1, [x2, #0x13]
    // 0x14c87f8: DecompressPointer r1
    //     0x14c87f8: add             x1, x1, HEAP, lsl #32
    // 0x14c87fc: r0 = of()
    //     0x14c87fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8800: LoadField: r1 = r0->field_87
    //     0x14c8800: ldur            w1, [x0, #0x87]
    // 0x14c8804: DecompressPointer r1
    //     0x14c8804: add             x1, x1, HEAP, lsl #32
    // 0x14c8808: LoadField: r0 = r1->field_2b
    //     0x14c8808: ldur            w0, [x1, #0x2b]
    // 0x14c880c: DecompressPointer r0
    //     0x14c880c: add             x0, x0, HEAP, lsl #32
    // 0x14c8810: stur            x0, [fp, #-0x58]
    // 0x14c8814: r1 = Instance_Color
    //     0x14c8814: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8818: d0 = 0.700000
    //     0x14c8818: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c881c: ldr             d0, [x17, #0xf48]
    // 0x14c8820: r0 = withOpacity()
    //     0x14c8820: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c8824: r16 = 12.000000
    //     0x14c8824: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c8828: ldr             x16, [x16, #0x9e8]
    // 0x14c882c: stp             x0, x16, [SP]
    // 0x14c8830: ldur            x1, [fp, #-0x58]
    // 0x14c8834: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c8834: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c8838: ldr             x4, [x4, #0xaa0]
    // 0x14c883c: r0 = copyWith()
    //     0x14c883c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8840: stur            x0, [fp, #-0x58]
    // 0x14c8844: r0 = Text()
    //     0x14c8844: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8848: mov             x1, x0
    // 0x14c884c: ldur            x0, [fp, #-0x50]
    // 0x14c8850: stur            x1, [fp, #-0x60]
    // 0x14c8854: StoreField: r1->field_b = r0
    //     0x14c8854: stur            w0, [x1, #0xb]
    // 0x14c8858: ldur            x0, [fp, #-0x58]
    // 0x14c885c: StoreField: r1->field_13 = r0
    //     0x14c885c: stur            w0, [x1, #0x13]
    // 0x14c8860: r0 = Padding()
    //     0x14c8860: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8864: mov             x3, x0
    // 0x14c8868: r0 = Instance_EdgeInsets
    //     0x14c8868: add             x0, PP, #0x42, lsl #12  ; [pp+0x42618] Obj!EdgeInsets@d59fc1
    //     0x14c886c: ldr             x0, [x0, #0x618]
    // 0x14c8870: stur            x3, [fp, #-0x50]
    // 0x14c8874: StoreField: r3->field_f = r0
    //     0x14c8874: stur            w0, [x3, #0xf]
    // 0x14c8878: ldur            x0, [fp, #-0x60]
    // 0x14c887c: StoreField: r3->field_b = r0
    //     0x14c887c: stur            w0, [x3, #0xb]
    // 0x14c8880: r1 = Null
    //     0x14c8880: mov             x1, NULL
    // 0x14c8884: r2 = 4
    //     0x14c8884: movz            x2, #0x4
    // 0x14c8888: r0 = AllocateArray()
    //     0x14c8888: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c888c: mov             x2, x0
    // 0x14c8890: ldur            x0, [fp, #-0x48]
    // 0x14c8894: stur            x2, [fp, #-0x58]
    // 0x14c8898: StoreField: r2->field_f = r0
    //     0x14c8898: stur            w0, [x2, #0xf]
    // 0x14c889c: ldur            x0, [fp, #-0x50]
    // 0x14c88a0: StoreField: r2->field_13 = r0
    //     0x14c88a0: stur            w0, [x2, #0x13]
    // 0x14c88a4: r1 = <Widget>
    //     0x14c88a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c88a8: r0 = AllocateGrowableArray()
    //     0x14c88a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c88ac: mov             x1, x0
    // 0x14c88b0: ldur            x0, [fp, #-0x58]
    // 0x14c88b4: stur            x1, [fp, #-0x48]
    // 0x14c88b8: StoreField: r1->field_f = r0
    //     0x14c88b8: stur            w0, [x1, #0xf]
    // 0x14c88bc: r0 = 4
    //     0x14c88bc: movz            x0, #0x4
    // 0x14c88c0: StoreField: r1->field_b = r0
    //     0x14c88c0: stur            w0, [x1, #0xb]
    // 0x14c88c4: r0 = Column()
    //     0x14c88c4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c88c8: mov             x1, x0
    // 0x14c88cc: r0 = Instance_Axis
    //     0x14c88cc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c88d0: stur            x1, [fp, #-0x50]
    // 0x14c88d4: StoreField: r1->field_f = r0
    //     0x14c88d4: stur            w0, [x1, #0xf]
    // 0x14c88d8: r2 = Instance_MainAxisAlignment
    //     0x14c88d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c88dc: ldr             x2, [x2, #0xa08]
    // 0x14c88e0: StoreField: r1->field_13 = r2
    //     0x14c88e0: stur            w2, [x1, #0x13]
    // 0x14c88e4: r3 = Instance_MainAxisSize
    //     0x14c88e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c88e8: ldr             x3, [x3, #0xa10]
    // 0x14c88ec: ArrayStore: r1[0] = r3  ; List_4
    //     0x14c88ec: stur            w3, [x1, #0x17]
    // 0x14c88f0: r4 = Instance_CrossAxisAlignment
    //     0x14c88f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c88f4: ldr             x4, [x4, #0x890]
    // 0x14c88f8: StoreField: r1->field_1b = r4
    //     0x14c88f8: stur            w4, [x1, #0x1b]
    // 0x14c88fc: r5 = Instance_VerticalDirection
    //     0x14c88fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c8900: ldr             x5, [x5, #0xa20]
    // 0x14c8904: StoreField: r1->field_23 = r5
    //     0x14c8904: stur            w5, [x1, #0x23]
    // 0x14c8908: r6 = Instance_Clip
    //     0x14c8908: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c890c: ldr             x6, [x6, #0x38]
    // 0x14c8910: StoreField: r1->field_2b = r6
    //     0x14c8910: stur            w6, [x1, #0x2b]
    // 0x14c8914: StoreField: r1->field_2f = rZR
    //     0x14c8914: stur            xzr, [x1, #0x2f]
    // 0x14c8918: ldur            x7, [fp, #-0x48]
    // 0x14c891c: StoreField: r1->field_b = r7
    //     0x14c891c: stur            w7, [x1, #0xb]
    // 0x14c8920: r0 = Container()
    //     0x14c8920: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c8924: stur            x0, [fp, #-0x48]
    // 0x14c8928: r16 = inf
    //     0x14c8928: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14c892c: ldr             x16, [x16, #0x9f8]
    // 0x14c8930: r30 = Instance_EdgeInsets
    //     0x14c8930: add             lr, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0x14c8934: ldr             lr, [lr, #0x620]
    // 0x14c8938: stp             lr, x16, [SP, #8]
    // 0x14c893c: ldur            x16, [fp, #-0x50]
    // 0x14c8940: str             x16, [SP]
    // 0x14c8944: mov             x1, x0
    // 0x14c8948: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0x14c8948: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0x14c894c: ldr             x4, [x4, #0x628]
    // 0x14c8950: r0 = Container()
    //     0x14c8950: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c8954: r1 = Instance_Color
    //     0x14c8954: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8958: d0 = 0.030000
    //     0x14c8958: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14c895c: ldr             d0, [x17, #0x238]
    // 0x14c8960: r0 = withOpacity()
    //     0x14c8960: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c8964: stur            x0, [fp, #-0x50]
    // 0x14c8968: r0 = Radius()
    //     0x14c8968: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c896c: d0 = 12.000000
    //     0x14c896c: fmov            d0, #12.00000000
    // 0x14c8970: stur            x0, [fp, #-0x58]
    // 0x14c8974: StoreField: r0->field_7 = d0
    //     0x14c8974: stur            d0, [x0, #7]
    // 0x14c8978: StoreField: r0->field_f = d0
    //     0x14c8978: stur            d0, [x0, #0xf]
    // 0x14c897c: r0 = BorderRadius()
    //     0x14c897c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c8980: mov             x1, x0
    // 0x14c8984: ldur            x0, [fp, #-0x58]
    // 0x14c8988: stur            x1, [fp, #-0x60]
    // 0x14c898c: StoreField: r1->field_7 = r0
    //     0x14c898c: stur            w0, [x1, #7]
    // 0x14c8990: StoreField: r1->field_b = r0
    //     0x14c8990: stur            w0, [x1, #0xb]
    // 0x14c8994: StoreField: r1->field_f = r0
    //     0x14c8994: stur            w0, [x1, #0xf]
    // 0x14c8998: StoreField: r1->field_13 = r0
    //     0x14c8998: stur            w0, [x1, #0x13]
    // 0x14c899c: r0 = BoxDecoration()
    //     0x14c899c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14c89a0: mov             x1, x0
    // 0x14c89a4: ldur            x0, [fp, #-0x50]
    // 0x14c89a8: stur            x1, [fp, #-0x58]
    // 0x14c89ac: StoreField: r1->field_7 = r0
    //     0x14c89ac: stur            w0, [x1, #7]
    // 0x14c89b0: ldur            x0, [fp, #-0x60]
    // 0x14c89b4: StoreField: r1->field_13 = r0
    //     0x14c89b4: stur            w0, [x1, #0x13]
    // 0x14c89b8: r0 = Instance_BoxShape
    //     0x14c89b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c89bc: ldr             x0, [x0, #0x80]
    // 0x14c89c0: StoreField: r1->field_23 = r0
    //     0x14c89c0: stur            w0, [x1, #0x23]
    // 0x14c89c4: r0 = SvgPicture()
    //     0x14c89c4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14c89c8: stur            x0, [fp, #-0x50]
    // 0x14c89cc: r16 = Instance_BoxFit
    //     0x14c89cc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x14c89d0: ldr             x16, [x16, #0xb18]
    // 0x14c89d4: r30 = Instance_ColorFilter
    //     0x14c89d4: add             lr, PP, #0x33, lsl #12  ; [pp+0x33818] Obj!ColorFilter@d69801
    //     0x14c89d8: ldr             lr, [lr, #0x818]
    // 0x14c89dc: stp             lr, x16, [SP]
    // 0x14c89e0: mov             x1, x0
    // 0x14c89e4: r2 = "assets/images/reason_cosmetic_icon.svg"
    //     0x14c89e4: add             x2, PP, #0x42, lsl #12  ; [pp+0x42630] "assets/images/reason_cosmetic_icon.svg"
    //     0x14c89e8: ldr             x2, [x2, #0x630]
    // 0x14c89ec: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14c89ec: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14c89f0: ldr             x4, [x4, #0x820]
    // 0x14c89f4: r0 = SvgPicture.asset()
    //     0x14c89f4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14c89f8: ldur            x0, [fp, #-8]
    // 0x14c89fc: LoadField: r1 = r0->field_f
    //     0x14c89fc: ldur            w1, [x0, #0xf]
    // 0x14c8a00: DecompressPointer r1
    //     0x14c8a00: add             x1, x1, HEAP, lsl #32
    // 0x14c8a04: r0 = controller()
    //     0x14c8a04: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c8a08: LoadField: r1 = r0->field_57
    //     0x14c8a08: ldur            w1, [x0, #0x57]
    // 0x14c8a0c: DecompressPointer r1
    //     0x14c8a0c: add             x1, x1, HEAP, lsl #32
    // 0x14c8a10: r0 = value()
    //     0x14c8a10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c8a14: cmp             w0, NULL
    // 0x14c8a18: b.ne            #0x14c8a24
    // 0x14c8a1c: r0 = Null
    //     0x14c8a1c: mov             x0, NULL
    // 0x14c8a20: b               #0x14c8a30
    // 0x14c8a24: LoadField: r1 = r0->field_1b
    //     0x14c8a24: ldur            w1, [x0, #0x1b]
    // 0x14c8a28: DecompressPointer r1
    //     0x14c8a28: add             x1, x1, HEAP, lsl #32
    // 0x14c8a2c: mov             x0, x1
    // 0x14c8a30: cmp             w0, NULL
    // 0x14c8a34: b.ne            #0x14c8a40
    // 0x14c8a38: r3 = ""
    //     0x14c8a38: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c8a3c: b               #0x14c8a44
    // 0x14c8a40: mov             x3, x0
    // 0x14c8a44: ldur            x0, [fp, #-8]
    // 0x14c8a48: ldur            x2, [fp, #-0x50]
    // 0x14c8a4c: stur            x3, [fp, #-0x60]
    // 0x14c8a50: LoadField: r1 = r0->field_13
    //     0x14c8a50: ldur            w1, [x0, #0x13]
    // 0x14c8a54: DecompressPointer r1
    //     0x14c8a54: add             x1, x1, HEAP, lsl #32
    // 0x14c8a58: r0 = of()
    //     0x14c8a58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8a5c: LoadField: r1 = r0->field_87
    //     0x14c8a5c: ldur            w1, [x0, #0x87]
    // 0x14c8a60: DecompressPointer r1
    //     0x14c8a60: add             x1, x1, HEAP, lsl #32
    // 0x14c8a64: LoadField: r0 = r1->field_2b
    //     0x14c8a64: ldur            w0, [x1, #0x2b]
    // 0x14c8a68: DecompressPointer r0
    //     0x14c8a68: add             x0, x0, HEAP, lsl #32
    // 0x14c8a6c: stur            x0, [fp, #-0x68]
    // 0x14c8a70: r1 = Instance_Color
    //     0x14c8a70: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8a74: d0 = 0.700000
    //     0x14c8a74: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c8a78: ldr             d0, [x17, #0xf48]
    // 0x14c8a7c: r0 = withOpacity()
    //     0x14c8a7c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c8a80: r16 = 12.000000
    //     0x14c8a80: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c8a84: ldr             x16, [x16, #0x9e8]
    // 0x14c8a88: stp             x0, x16, [SP]
    // 0x14c8a8c: ldur            x1, [fp, #-0x68]
    // 0x14c8a90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c8a90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c8a94: ldr             x4, [x4, #0xaa0]
    // 0x14c8a98: r0 = copyWith()
    //     0x14c8a98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8a9c: stur            x0, [fp, #-0x68]
    // 0x14c8aa0: r0 = Text()
    //     0x14c8aa0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8aa4: mov             x2, x0
    // 0x14c8aa8: ldur            x0, [fp, #-0x60]
    // 0x14c8aac: stur            x2, [fp, #-0x70]
    // 0x14c8ab0: StoreField: r2->field_b = r0
    //     0x14c8ab0: stur            w0, [x2, #0xb]
    // 0x14c8ab4: ldur            x0, [fp, #-0x68]
    // 0x14c8ab8: StoreField: r2->field_13 = r0
    //     0x14c8ab8: stur            w0, [x2, #0x13]
    // 0x14c8abc: r0 = Instance_TextOverflow
    //     0x14c8abc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14c8ac0: ldr             x0, [x0, #0xe10]
    // 0x14c8ac4: StoreField: r2->field_2b = r0
    //     0x14c8ac4: stur            w0, [x2, #0x2b]
    // 0x14c8ac8: r0 = 4
    //     0x14c8ac8: movz            x0, #0x4
    // 0x14c8acc: StoreField: r2->field_37 = r0
    //     0x14c8acc: stur            w0, [x2, #0x37]
    // 0x14c8ad0: r1 = <FlexParentData>
    //     0x14c8ad0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14c8ad4: ldr             x1, [x1, #0xe00]
    // 0x14c8ad8: r0 = Expanded()
    //     0x14c8ad8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14c8adc: mov             x3, x0
    // 0x14c8ae0: r0 = 1
    //     0x14c8ae0: movz            x0, #0x1
    // 0x14c8ae4: stur            x3, [fp, #-0x60]
    // 0x14c8ae8: StoreField: r3->field_13 = r0
    //     0x14c8ae8: stur            x0, [x3, #0x13]
    // 0x14c8aec: r0 = Instance_FlexFit
    //     0x14c8aec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14c8af0: ldr             x0, [x0, #0xe08]
    // 0x14c8af4: StoreField: r3->field_1b = r0
    //     0x14c8af4: stur            w0, [x3, #0x1b]
    // 0x14c8af8: ldur            x0, [fp, #-0x70]
    // 0x14c8afc: StoreField: r3->field_b = r0
    //     0x14c8afc: stur            w0, [x3, #0xb]
    // 0x14c8b00: r1 = Null
    //     0x14c8b00: mov             x1, NULL
    // 0x14c8b04: r2 = 6
    //     0x14c8b04: movz            x2, #0x6
    // 0x14c8b08: r0 = AllocateArray()
    //     0x14c8b08: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c8b0c: mov             x2, x0
    // 0x14c8b10: ldur            x0, [fp, #-0x50]
    // 0x14c8b14: stur            x2, [fp, #-0x68]
    // 0x14c8b18: StoreField: r2->field_f = r0
    //     0x14c8b18: stur            w0, [x2, #0xf]
    // 0x14c8b1c: r16 = Instance_SizedBox
    //     0x14c8b1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14c8b20: ldr             x16, [x16, #0xb20]
    // 0x14c8b24: StoreField: r2->field_13 = r16
    //     0x14c8b24: stur            w16, [x2, #0x13]
    // 0x14c8b28: ldur            x0, [fp, #-0x60]
    // 0x14c8b2c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c8b2c: stur            w0, [x2, #0x17]
    // 0x14c8b30: r1 = <Widget>
    //     0x14c8b30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c8b34: r0 = AllocateGrowableArray()
    //     0x14c8b34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c8b38: mov             x1, x0
    // 0x14c8b3c: ldur            x0, [fp, #-0x68]
    // 0x14c8b40: stur            x1, [fp, #-0x50]
    // 0x14c8b44: StoreField: r1->field_f = r0
    //     0x14c8b44: stur            w0, [x1, #0xf]
    // 0x14c8b48: r0 = 6
    //     0x14c8b48: movz            x0, #0x6
    // 0x14c8b4c: StoreField: r1->field_b = r0
    //     0x14c8b4c: stur            w0, [x1, #0xb]
    // 0x14c8b50: r0 = Row()
    //     0x14c8b50: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14c8b54: mov             x1, x0
    // 0x14c8b58: r0 = Instance_Axis
    //     0x14c8b58: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c8b5c: stur            x1, [fp, #-0x60]
    // 0x14c8b60: StoreField: r1->field_f = r0
    //     0x14c8b60: stur            w0, [x1, #0xf]
    // 0x14c8b64: r0 = Instance_MainAxisAlignment
    //     0x14c8b64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c8b68: ldr             x0, [x0, #0xa08]
    // 0x14c8b6c: StoreField: r1->field_13 = r0
    //     0x14c8b6c: stur            w0, [x1, #0x13]
    // 0x14c8b70: r2 = Instance_MainAxisSize
    //     0x14c8b70: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c8b74: ldr             x2, [x2, #0xa10]
    // 0x14c8b78: ArrayStore: r1[0] = r2  ; List_4
    //     0x14c8b78: stur            w2, [x1, #0x17]
    // 0x14c8b7c: r3 = Instance_CrossAxisAlignment
    //     0x14c8b7c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14c8b80: ldr             x3, [x3, #0xa18]
    // 0x14c8b84: StoreField: r1->field_1b = r3
    //     0x14c8b84: stur            w3, [x1, #0x1b]
    // 0x14c8b88: r3 = Instance_VerticalDirection
    //     0x14c8b88: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c8b8c: ldr             x3, [x3, #0xa20]
    // 0x14c8b90: StoreField: r1->field_23 = r3
    //     0x14c8b90: stur            w3, [x1, #0x23]
    // 0x14c8b94: r4 = Instance_Clip
    //     0x14c8b94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c8b98: ldr             x4, [x4, #0x38]
    // 0x14c8b9c: StoreField: r1->field_2b = r4
    //     0x14c8b9c: stur            w4, [x1, #0x2b]
    // 0x14c8ba0: StoreField: r1->field_2f = rZR
    //     0x14c8ba0: stur            xzr, [x1, #0x2f]
    // 0x14c8ba4: ldur            x5, [fp, #-0x50]
    // 0x14c8ba8: StoreField: r1->field_b = r5
    //     0x14c8ba8: stur            w5, [x1, #0xb]
    // 0x14c8bac: r0 = Container()
    //     0x14c8bac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c8bb0: stur            x0, [fp, #-0x50]
    // 0x14c8bb4: r16 = Instance_EdgeInsets
    //     0x14c8bb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14c8bb8: ldr             x16, [x16, #0x1f0]
    // 0x14c8bbc: ldur            lr, [fp, #-0x58]
    // 0x14c8bc0: stp             lr, x16, [SP, #8]
    // 0x14c8bc4: ldur            x16, [fp, #-0x60]
    // 0x14c8bc8: str             x16, [SP]
    // 0x14c8bcc: mov             x1, x0
    // 0x14c8bd0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x14c8bd0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x14c8bd4: ldr             x4, [x4, #0x610]
    // 0x14c8bd8: r0 = Container()
    //     0x14c8bd8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c8bdc: r0 = Padding()
    //     0x14c8bdc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8be0: mov             x2, x0
    // 0x14c8be4: r0 = Instance_EdgeInsets
    //     0x14c8be4: add             x0, PP, #0x42, lsl #12  ; [pp+0x42638] Obj!EdgeInsets@d591e1
    //     0x14c8be8: ldr             x0, [x0, #0x638]
    // 0x14c8bec: stur            x2, [fp, #-0x58]
    // 0x14c8bf0: StoreField: r2->field_f = r0
    //     0x14c8bf0: stur            w0, [x2, #0xf]
    // 0x14c8bf4: ldur            x0, [fp, #-0x50]
    // 0x14c8bf8: StoreField: r2->field_b = r0
    //     0x14c8bf8: stur            w0, [x2, #0xb]
    // 0x14c8bfc: ldur            x0, [fp, #-8]
    // 0x14c8c00: LoadField: r1 = r0->field_13
    //     0x14c8c00: ldur            w1, [x0, #0x13]
    // 0x14c8c04: DecompressPointer r1
    //     0x14c8c04: add             x1, x1, HEAP, lsl #32
    // 0x14c8c08: r0 = of()
    //     0x14c8c08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8c0c: LoadField: r1 = r0->field_87
    //     0x14c8c0c: ldur            w1, [x0, #0x87]
    // 0x14c8c10: DecompressPointer r1
    //     0x14c8c10: add             x1, x1, HEAP, lsl #32
    // 0x14c8c14: LoadField: r0 = r1->field_7
    //     0x14c8c14: ldur            w0, [x1, #7]
    // 0x14c8c18: DecompressPointer r0
    //     0x14c8c18: add             x0, x0, HEAP, lsl #32
    // 0x14c8c1c: r16 = 14.000000
    //     0x14c8c1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c8c20: ldr             x16, [x16, #0x1d8]
    // 0x14c8c24: r30 = Instance_Color
    //     0x14c8c24: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8c28: stp             lr, x16, [SP]
    // 0x14c8c2c: mov             x1, x0
    // 0x14c8c30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c8c30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c8c34: ldr             x4, [x4, #0xaa0]
    // 0x14c8c38: r0 = copyWith()
    //     0x14c8c38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8c3c: stur            x0, [fp, #-0x50]
    // 0x14c8c40: r0 = Text()
    //     0x14c8c40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8c44: mov             x1, x0
    // 0x14c8c48: r0 = "Note:"
    //     0x14c8c48: add             x0, PP, #0x33, lsl #12  ; [pp+0x33838] "Note:"
    //     0x14c8c4c: ldr             x0, [x0, #0x838]
    // 0x14c8c50: stur            x1, [fp, #-0x60]
    // 0x14c8c54: StoreField: r1->field_b = r0
    //     0x14c8c54: stur            w0, [x1, #0xb]
    // 0x14c8c58: ldur            x0, [fp, #-0x50]
    // 0x14c8c5c: StoreField: r1->field_13 = r0
    //     0x14c8c5c: stur            w0, [x1, #0x13]
    // 0x14c8c60: r0 = Padding()
    //     0x14c8c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8c64: mov             x2, x0
    // 0x14c8c68: r0 = Instance_EdgeInsets
    //     0x14c8c68: add             x0, PP, #0x33, lsl #12  ; [pp+0x33840] Obj!EdgeInsets@d59ed1
    //     0x14c8c6c: ldr             x0, [x0, #0x840]
    // 0x14c8c70: stur            x2, [fp, #-0x50]
    // 0x14c8c74: StoreField: r2->field_f = r0
    //     0x14c8c74: stur            w0, [x2, #0xf]
    // 0x14c8c78: ldur            x0, [fp, #-0x60]
    // 0x14c8c7c: StoreField: r2->field_b = r0
    //     0x14c8c7c: stur            w0, [x2, #0xb]
    // 0x14c8c80: ldur            x0, [fp, #-8]
    // 0x14c8c84: LoadField: r1 = r0->field_f
    //     0x14c8c84: ldur            w1, [x0, #0xf]
    // 0x14c8c88: DecompressPointer r1
    //     0x14c8c88: add             x1, x1, HEAP, lsl #32
    // 0x14c8c8c: r0 = controller()
    //     0x14c8c8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c8c90: LoadField: r1 = r0->field_57
    //     0x14c8c90: ldur            w1, [x0, #0x57]
    // 0x14c8c94: DecompressPointer r1
    //     0x14c8c94: add             x1, x1, HEAP, lsl #32
    // 0x14c8c98: r0 = value()
    //     0x14c8c98: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c8c9c: cmp             w0, NULL
    // 0x14c8ca0: b.ne            #0x14c8cac
    // 0x14c8ca4: r0 = Null
    //     0x14c8ca4: mov             x0, NULL
    // 0x14c8ca8: b               #0x14c8cb8
    // 0x14c8cac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14c8cac: ldur            w1, [x0, #0x17]
    // 0x14c8cb0: DecompressPointer r1
    //     0x14c8cb0: add             x1, x1, HEAP, lsl #32
    // 0x14c8cb4: mov             x0, x1
    // 0x14c8cb8: cmp             w0, NULL
    // 0x14c8cbc: b.ne            #0x14c8cc8
    // 0x14c8cc0: r11 = ""
    //     0x14c8cc0: ldr             x11, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c8cc4: b               #0x14c8ccc
    // 0x14c8cc8: mov             x11, x0
    // 0x14c8ccc: ldur            x1, [fp, #-8]
    // 0x14c8cd0: ldur            x10, [fp, #-0x20]
    // 0x14c8cd4: ldur            x9, [fp, #-0x10]
    // 0x14c8cd8: ldur            x8, [fp, #-0x30]
    // 0x14c8cdc: ldur            x7, [fp, #-0x18]
    // 0x14c8ce0: ldur            x6, [fp, #-0x38]
    // 0x14c8ce4: ldur            x5, [fp, #-0x28]
    // 0x14c8ce8: ldur            x4, [fp, #-0x40]
    // 0x14c8cec: ldur            x3, [fp, #-0x48]
    // 0x14c8cf0: ldur            x2, [fp, #-0x58]
    // 0x14c8cf4: ldur            x0, [fp, #-0x50]
    // 0x14c8cf8: stur            x11, [fp, #-0x60]
    // 0x14c8cfc: LoadField: r12 = r1->field_13
    //     0x14c8cfc: ldur            w12, [x1, #0x13]
    // 0x14c8d00: DecompressPointer r12
    //     0x14c8d00: add             x12, x12, HEAP, lsl #32
    // 0x14c8d04: mov             x1, x12
    // 0x14c8d08: r0 = of()
    //     0x14c8d08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c8d0c: LoadField: r1 = r0->field_87
    //     0x14c8d0c: ldur            w1, [x0, #0x87]
    // 0x14c8d10: DecompressPointer r1
    //     0x14c8d10: add             x1, x1, HEAP, lsl #32
    // 0x14c8d14: LoadField: r0 = r1->field_2b
    //     0x14c8d14: ldur            w0, [x1, #0x2b]
    // 0x14c8d18: DecompressPointer r0
    //     0x14c8d18: add             x0, x0, HEAP, lsl #32
    // 0x14c8d1c: stur            x0, [fp, #-8]
    // 0x14c8d20: r1 = Instance_Color
    //     0x14c8d20: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c8d24: d0 = 0.700000
    //     0x14c8d24: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c8d28: ldr             d0, [x17, #0xf48]
    // 0x14c8d2c: r0 = withOpacity()
    //     0x14c8d2c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c8d30: r16 = 12.000000
    //     0x14c8d30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c8d34: ldr             x16, [x16, #0x9e8]
    // 0x14c8d38: stp             x0, x16, [SP]
    // 0x14c8d3c: ldur            x1, [fp, #-8]
    // 0x14c8d40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c8d40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c8d44: ldr             x4, [x4, #0xaa0]
    // 0x14c8d48: r0 = copyWith()
    //     0x14c8d48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c8d4c: stur            x0, [fp, #-8]
    // 0x14c8d50: r0 = Text()
    //     0x14c8d50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c8d54: mov             x1, x0
    // 0x14c8d58: ldur            x0, [fp, #-0x60]
    // 0x14c8d5c: stur            x1, [fp, #-0x68]
    // 0x14c8d60: StoreField: r1->field_b = r0
    //     0x14c8d60: stur            w0, [x1, #0xb]
    // 0x14c8d64: ldur            x0, [fp, #-8]
    // 0x14c8d68: StoreField: r1->field_13 = r0
    //     0x14c8d68: stur            w0, [x1, #0x13]
    // 0x14c8d6c: r0 = Padding()
    //     0x14c8d6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8d70: mov             x3, x0
    // 0x14c8d74: r0 = Instance_EdgeInsets
    //     0x14c8d74: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x14c8d78: ldr             x0, [x0, #0x868]
    // 0x14c8d7c: stur            x3, [fp, #-8]
    // 0x14c8d80: StoreField: r3->field_f = r0
    //     0x14c8d80: stur            w0, [x3, #0xf]
    // 0x14c8d84: ldur            x0, [fp, #-0x68]
    // 0x14c8d88: StoreField: r3->field_b = r0
    //     0x14c8d88: stur            w0, [x3, #0xb]
    // 0x14c8d8c: r1 = Null
    //     0x14c8d8c: mov             x1, NULL
    // 0x14c8d90: r2 = 8
    //     0x14c8d90: movz            x2, #0x8
    // 0x14c8d94: r0 = AllocateArray()
    //     0x14c8d94: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c8d98: mov             x2, x0
    // 0x14c8d9c: ldur            x0, [fp, #-0x48]
    // 0x14c8da0: stur            x2, [fp, #-0x60]
    // 0x14c8da4: StoreField: r2->field_f = r0
    //     0x14c8da4: stur            w0, [x2, #0xf]
    // 0x14c8da8: ldur            x0, [fp, #-0x58]
    // 0x14c8dac: StoreField: r2->field_13 = r0
    //     0x14c8dac: stur            w0, [x2, #0x13]
    // 0x14c8db0: ldur            x0, [fp, #-0x50]
    // 0x14c8db4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c8db4: stur            w0, [x2, #0x17]
    // 0x14c8db8: ldur            x0, [fp, #-8]
    // 0x14c8dbc: StoreField: r2->field_1b = r0
    //     0x14c8dbc: stur            w0, [x2, #0x1b]
    // 0x14c8dc0: r1 = <Widget>
    //     0x14c8dc0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c8dc4: r0 = AllocateGrowableArray()
    //     0x14c8dc4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c8dc8: mov             x1, x0
    // 0x14c8dcc: ldur            x0, [fp, #-0x60]
    // 0x14c8dd0: stur            x1, [fp, #-8]
    // 0x14c8dd4: StoreField: r1->field_f = r0
    //     0x14c8dd4: stur            w0, [x1, #0xf]
    // 0x14c8dd8: r0 = 8
    //     0x14c8dd8: movz            x0, #0x8
    // 0x14c8ddc: StoreField: r1->field_b = r0
    //     0x14c8ddc: stur            w0, [x1, #0xb]
    // 0x14c8de0: r0 = Column()
    //     0x14c8de0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c8de4: mov             x1, x0
    // 0x14c8de8: r0 = Instance_Axis
    //     0x14c8de8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c8dec: stur            x1, [fp, #-0x48]
    // 0x14c8df0: StoreField: r1->field_f = r0
    //     0x14c8df0: stur            w0, [x1, #0xf]
    // 0x14c8df4: r2 = Instance_MainAxisAlignment
    //     0x14c8df4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c8df8: ldr             x2, [x2, #0xa08]
    // 0x14c8dfc: StoreField: r1->field_13 = r2
    //     0x14c8dfc: stur            w2, [x1, #0x13]
    // 0x14c8e00: r3 = Instance_MainAxisSize
    //     0x14c8e00: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14c8e04: ldr             x3, [x3, #0xdd0]
    // 0x14c8e08: ArrayStore: r1[0] = r3  ; List_4
    //     0x14c8e08: stur            w3, [x1, #0x17]
    // 0x14c8e0c: r3 = Instance_CrossAxisAlignment
    //     0x14c8e0c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c8e10: ldr             x3, [x3, #0x890]
    // 0x14c8e14: StoreField: r1->field_1b = r3
    //     0x14c8e14: stur            w3, [x1, #0x1b]
    // 0x14c8e18: r4 = Instance_VerticalDirection
    //     0x14c8e18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c8e1c: ldr             x4, [x4, #0xa20]
    // 0x14c8e20: StoreField: r1->field_23 = r4
    //     0x14c8e20: stur            w4, [x1, #0x23]
    // 0x14c8e24: r5 = Instance_Clip
    //     0x14c8e24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c8e28: ldr             x5, [x5, #0x38]
    // 0x14c8e2c: StoreField: r1->field_2b = r5
    //     0x14c8e2c: stur            w5, [x1, #0x2b]
    // 0x14c8e30: StoreField: r1->field_2f = rZR
    //     0x14c8e30: stur            xzr, [x1, #0x2f]
    // 0x14c8e34: ldur            x6, [fp, #-8]
    // 0x14c8e38: StoreField: r1->field_b = r6
    //     0x14c8e38: stur            w6, [x1, #0xb]
    // 0x14c8e3c: r0 = Visibility()
    //     0x14c8e3c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14c8e40: mov             x3, x0
    // 0x14c8e44: ldur            x0, [fp, #-0x48]
    // 0x14c8e48: stur            x3, [fp, #-8]
    // 0x14c8e4c: StoreField: r3->field_b = r0
    //     0x14c8e4c: stur            w0, [x3, #0xb]
    // 0x14c8e50: r0 = Instance_SizedBox
    //     0x14c8e50: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14c8e54: StoreField: r3->field_f = r0
    //     0x14c8e54: stur            w0, [x3, #0xf]
    // 0x14c8e58: ldur            x0, [fp, #-0x40]
    // 0x14c8e5c: StoreField: r3->field_13 = r0
    //     0x14c8e5c: stur            w0, [x3, #0x13]
    // 0x14c8e60: r0 = false
    //     0x14c8e60: add             x0, NULL, #0x30  ; false
    // 0x14c8e64: ArrayStore: r3[0] = r0  ; List_4
    //     0x14c8e64: stur            w0, [x3, #0x17]
    // 0x14c8e68: StoreField: r3->field_1b = r0
    //     0x14c8e68: stur            w0, [x3, #0x1b]
    // 0x14c8e6c: StoreField: r3->field_1f = r0
    //     0x14c8e6c: stur            w0, [x3, #0x1f]
    // 0x14c8e70: StoreField: r3->field_23 = r0
    //     0x14c8e70: stur            w0, [x3, #0x23]
    // 0x14c8e74: StoreField: r3->field_27 = r0
    //     0x14c8e74: stur            w0, [x3, #0x27]
    // 0x14c8e78: StoreField: r3->field_2b = r0
    //     0x14c8e78: stur            w0, [x3, #0x2b]
    // 0x14c8e7c: r1 = Null
    //     0x14c8e7c: mov             x1, NULL
    // 0x14c8e80: r2 = 14
    //     0x14c8e80: movz            x2, #0xe
    // 0x14c8e84: r0 = AllocateArray()
    //     0x14c8e84: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c8e88: mov             x2, x0
    // 0x14c8e8c: ldur            x0, [fp, #-0x20]
    // 0x14c8e90: stur            x2, [fp, #-0x40]
    // 0x14c8e94: StoreField: r2->field_f = r0
    //     0x14c8e94: stur            w0, [x2, #0xf]
    // 0x14c8e98: ldur            x0, [fp, #-0x10]
    // 0x14c8e9c: StoreField: r2->field_13 = r0
    //     0x14c8e9c: stur            w0, [x2, #0x13]
    // 0x14c8ea0: ldur            x0, [fp, #-0x30]
    // 0x14c8ea4: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c8ea4: stur            w0, [x2, #0x17]
    // 0x14c8ea8: ldur            x0, [fp, #-0x18]
    // 0x14c8eac: StoreField: r2->field_1b = r0
    //     0x14c8eac: stur            w0, [x2, #0x1b]
    // 0x14c8eb0: ldur            x0, [fp, #-0x38]
    // 0x14c8eb4: StoreField: r2->field_1f = r0
    //     0x14c8eb4: stur            w0, [x2, #0x1f]
    // 0x14c8eb8: ldur            x0, [fp, #-0x28]
    // 0x14c8ebc: StoreField: r2->field_23 = r0
    //     0x14c8ebc: stur            w0, [x2, #0x23]
    // 0x14c8ec0: ldur            x0, [fp, #-8]
    // 0x14c8ec4: StoreField: r2->field_27 = r0
    //     0x14c8ec4: stur            w0, [x2, #0x27]
    // 0x14c8ec8: r1 = <Widget>
    //     0x14c8ec8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c8ecc: r0 = AllocateGrowableArray()
    //     0x14c8ecc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c8ed0: mov             x1, x0
    // 0x14c8ed4: ldur            x0, [fp, #-0x40]
    // 0x14c8ed8: stur            x1, [fp, #-8]
    // 0x14c8edc: StoreField: r1->field_f = r0
    //     0x14c8edc: stur            w0, [x1, #0xf]
    // 0x14c8ee0: r0 = 14
    //     0x14c8ee0: movz            x0, #0xe
    // 0x14c8ee4: StoreField: r1->field_b = r0
    //     0x14c8ee4: stur            w0, [x1, #0xb]
    // 0x14c8ee8: r0 = Column()
    //     0x14c8ee8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c8eec: mov             x1, x0
    // 0x14c8ef0: r0 = Instance_Axis
    //     0x14c8ef0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c8ef4: stur            x1, [fp, #-0x10]
    // 0x14c8ef8: StoreField: r1->field_f = r0
    //     0x14c8ef8: stur            w0, [x1, #0xf]
    // 0x14c8efc: r2 = Instance_MainAxisAlignment
    //     0x14c8efc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c8f00: ldr             x2, [x2, #0xa08]
    // 0x14c8f04: StoreField: r1->field_13 = r2
    //     0x14c8f04: stur            w2, [x1, #0x13]
    // 0x14c8f08: r2 = Instance_MainAxisSize
    //     0x14c8f08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c8f0c: ldr             x2, [x2, #0xa10]
    // 0x14c8f10: ArrayStore: r1[0] = r2  ; List_4
    //     0x14c8f10: stur            w2, [x1, #0x17]
    // 0x14c8f14: r2 = Instance_CrossAxisAlignment
    //     0x14c8f14: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c8f18: ldr             x2, [x2, #0x890]
    // 0x14c8f1c: StoreField: r1->field_1b = r2
    //     0x14c8f1c: stur            w2, [x1, #0x1b]
    // 0x14c8f20: r2 = Instance_VerticalDirection
    //     0x14c8f20: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c8f24: ldr             x2, [x2, #0xa20]
    // 0x14c8f28: StoreField: r1->field_23 = r2
    //     0x14c8f28: stur            w2, [x1, #0x23]
    // 0x14c8f2c: r2 = Instance_Clip
    //     0x14c8f2c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c8f30: ldr             x2, [x2, #0x38]
    // 0x14c8f34: StoreField: r1->field_2b = r2
    //     0x14c8f34: stur            w2, [x1, #0x2b]
    // 0x14c8f38: StoreField: r1->field_2f = rZR
    //     0x14c8f38: stur            xzr, [x1, #0x2f]
    // 0x14c8f3c: ldur            x2, [fp, #-8]
    // 0x14c8f40: StoreField: r1->field_b = r2
    //     0x14c8f40: stur            w2, [x1, #0xb]
    // 0x14c8f44: r0 = Padding()
    //     0x14c8f44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c8f48: mov             x1, x0
    // 0x14c8f4c: r0 = Instance_EdgeInsets
    //     0x14c8f4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14c8f50: ldr             x0, [x0, #0x1f0]
    // 0x14c8f54: stur            x1, [fp, #-8]
    // 0x14c8f58: StoreField: r1->field_f = r0
    //     0x14c8f58: stur            w0, [x1, #0xf]
    // 0x14c8f5c: ldur            x0, [fp, #-0x10]
    // 0x14c8f60: StoreField: r1->field_b = r0
    //     0x14c8f60: stur            w0, [x1, #0xb]
    // 0x14c8f64: r0 = SingleChildScrollView()
    //     0x14c8f64: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14c8f68: mov             x1, x0
    // 0x14c8f6c: r0 = Instance_Axis
    //     0x14c8f6c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c8f70: StoreField: r1->field_b = r0
    //     0x14c8f70: stur            w0, [x1, #0xb]
    // 0x14c8f74: r0 = false
    //     0x14c8f74: add             x0, NULL, #0x30  ; false
    // 0x14c8f78: StoreField: r1->field_f = r0
    //     0x14c8f78: stur            w0, [x1, #0xf]
    // 0x14c8f7c: ldur            x0, [fp, #-8]
    // 0x14c8f80: StoreField: r1->field_23 = r0
    //     0x14c8f80: stur            w0, [x1, #0x23]
    // 0x14c8f84: r0 = Instance_DragStartBehavior
    //     0x14c8f84: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14c8f88: StoreField: r1->field_27 = r0
    //     0x14c8f88: stur            w0, [x1, #0x27]
    // 0x14c8f8c: r0 = Instance_Clip
    //     0x14c8f8c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14c8f90: ldr             x0, [x0, #0x7e0]
    // 0x14c8f94: StoreField: r1->field_2b = r0
    //     0x14c8f94: stur            w0, [x1, #0x2b]
    // 0x14c8f98: r0 = Instance_HitTestBehavior
    //     0x14c8f98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14c8f9c: ldr             x0, [x0, #0x288]
    // 0x14c8fa0: StoreField: r1->field_2f = r0
    //     0x14c8fa0: stur            w0, [x1, #0x2f]
    // 0x14c8fa4: mov             x0, x1
    // 0x14c8fa8: b               #0x14c8fc4
    // 0x14c8fac: r0 = Container()
    //     0x14c8fac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c8fb0: mov             x1, x0
    // 0x14c8fb4: stur            x0, [fp, #-8]
    // 0x14c8fb8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14c8fb8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14c8fbc: r0 = Container()
    //     0x14c8fbc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c8fc0: ldur            x0, [fp, #-8]
    // 0x14c8fc4: LeaveFrame
    //     0x14c8fc4: mov             SP, fp
    //     0x14c8fc8: ldp             fp, lr, [SP], #0x10
    // 0x14c8fcc: ret
    //     0x14c8fcc: ret             
    // 0x14c8fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c8fd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c8fd4: b               #0x14c762c
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14c8fd8, size: 0x74
    // 0x14c8fd8: EnterFrame
    //     0x14c8fd8: stp             fp, lr, [SP, #-0x10]!
    //     0x14c8fdc: mov             fp, SP
    // 0x14c8fe0: AllocStack(0x10)
    //     0x14c8fe0: sub             SP, SP, #0x10
    // 0x14c8fe4: SetupParameters()
    //     0x14c8fe4: ldr             x0, [fp, #0x20]
    //     0x14c8fe8: ldur            w1, [x0, #0x17]
    //     0x14c8fec: add             x1, x1, HEAP, lsl #32
    //     0x14c8ff0: stur            x1, [fp, #-8]
    // 0x14c8ff4: r1 = 2
    //     0x14c8ff4: movz            x1, #0x2
    // 0x14c8ff8: r0 = AllocateContext()
    //     0x14c8ff8: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c8ffc: mov             x1, x0
    // 0x14c9000: ldur            x0, [fp, #-8]
    // 0x14c9004: stur            x1, [fp, #-0x10]
    // 0x14c9008: StoreField: r1->field_b = r0
    //     0x14c9008: stur            w0, [x1, #0xb]
    // 0x14c900c: ldr             x0, [fp, #0x18]
    // 0x14c9010: StoreField: r1->field_f = r0
    //     0x14c9010: stur            w0, [x1, #0xf]
    // 0x14c9014: ldr             x0, [fp, #0x10]
    // 0x14c9018: StoreField: r1->field_13 = r0
    //     0x14c9018: stur            w0, [x1, #0x13]
    // 0x14c901c: r0 = Obx()
    //     0x14c901c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14c9020: ldur            x2, [fp, #-0x10]
    // 0x14c9024: r1 = Function '<anonymous closure>':.
    //     0x14c9024: add             x1, PP, #0x42, lsl #12  ; [pp+0x42640] AnonymousClosure: (0x14c904c), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::body (0x14c7518)
    //     0x14c9028: ldr             x1, [x1, #0x640]
    // 0x14c902c: stur            x0, [fp, #-8]
    // 0x14c9030: r0 = AllocateClosure()
    //     0x14c9030: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c9034: mov             x1, x0
    // 0x14c9038: ldur            x0, [fp, #-8]
    // 0x14c903c: StoreField: r0->field_b = r1
    //     0x14c903c: stur            w1, [x0, #0xb]
    // 0x14c9040: LeaveFrame
    //     0x14c9040: mov             SP, fp
    //     0x14c9044: ldp             fp, lr, [SP], #0x10
    // 0x14c9048: ret
    //     0x14c9048: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x14c904c, size: 0xdc
    // 0x14c904c: EnterFrame
    //     0x14c904c: stp             fp, lr, [SP, #-0x10]!
    //     0x14c9050: mov             fp, SP
    // 0x14c9054: AllocStack(0x18)
    //     0x14c9054: sub             SP, SP, #0x18
    // 0x14c9058: SetupParameters()
    //     0x14c9058: ldr             x0, [fp, #0x10]
    //     0x14c905c: ldur            w2, [x0, #0x17]
    //     0x14c9060: add             x2, x2, HEAP, lsl #32
    //     0x14c9064: stur            x2, [fp, #-0x18]
    // 0x14c9068: CheckStackOverflow
    //     0x14c9068: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c906c: cmp             SP, x16
    //     0x14c9070: b.ls            #0x14c911c
    // 0x14c9074: LoadField: r0 = r2->field_b
    //     0x14c9074: ldur            w0, [x2, #0xb]
    // 0x14c9078: DecompressPointer r0
    //     0x14c9078: add             x0, x0, HEAP, lsl #32
    // 0x14c907c: LoadField: r3 = r0->field_f
    //     0x14c907c: ldur            w3, [x0, #0xf]
    // 0x14c9080: DecompressPointer r3
    //     0x14c9080: add             x3, x3, HEAP, lsl #32
    // 0x14c9084: stur            x3, [fp, #-0x10]
    // 0x14c9088: LoadField: r0 = r2->field_f
    //     0x14c9088: ldur            w0, [x2, #0xf]
    // 0x14c908c: DecompressPointer r0
    //     0x14c908c: add             x0, x0, HEAP, lsl #32
    // 0x14c9090: mov             x1, x3
    // 0x14c9094: stur            x0, [fp, #-8]
    // 0x14c9098: r0 = controller()
    //     0x14c9098: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c909c: LoadField: r1 = r0->field_57
    //     0x14c909c: ldur            w1, [x0, #0x57]
    // 0x14c90a0: DecompressPointer r1
    //     0x14c90a0: add             x1, x1, HEAP, lsl #32
    // 0x14c90a4: r0 = value()
    //     0x14c90a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14c90a8: cmp             w0, NULL
    // 0x14c90ac: b.ne            #0x14c90b8
    // 0x14c90b0: r3 = Null
    //     0x14c90b0: mov             x3, NULL
    // 0x14c90b4: b               #0x14c9104
    // 0x14c90b8: ldur            x1, [fp, #-0x18]
    // 0x14c90bc: LoadField: r2 = r0->field_2b
    //     0x14c90bc: ldur            w2, [x0, #0x2b]
    // 0x14c90c0: DecompressPointer r2
    //     0x14c90c0: add             x2, x2, HEAP, lsl #32
    // 0x14c90c4: LoadField: r0 = r1->field_13
    //     0x14c90c4: ldur            w0, [x1, #0x13]
    // 0x14c90c8: DecompressPointer r0
    //     0x14c90c8: add             x0, x0, HEAP, lsl #32
    // 0x14c90cc: LoadField: r1 = r2->field_b
    //     0x14c90cc: ldur            w1, [x2, #0xb]
    // 0x14c90d0: r3 = LoadInt32Instr(r0)
    //     0x14c90d0: sbfx            x3, x0, #1, #0x1f
    //     0x14c90d4: tbz             w0, #0, #0x14c90dc
    //     0x14c90d8: ldur            x3, [x0, #7]
    // 0x14c90dc: r0 = LoadInt32Instr(r1)
    //     0x14c90dc: sbfx            x0, x1, #1, #0x1f
    // 0x14c90e0: mov             x1, x3
    // 0x14c90e4: cmp             x1, x0
    // 0x14c90e8: b.hs            #0x14c9124
    // 0x14c90ec: LoadField: r0 = r2->field_f
    //     0x14c90ec: ldur            w0, [x2, #0xf]
    // 0x14c90f0: DecompressPointer r0
    //     0x14c90f0: add             x0, x0, HEAP, lsl #32
    // 0x14c90f4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14c90f4: add             x16, x0, x3, lsl #2
    //     0x14c90f8: ldur            w1, [x16, #0xf]
    // 0x14c90fc: DecompressPointer r1
    //     0x14c90fc: add             x1, x1, HEAP, lsl #32
    // 0x14c9100: mov             x3, x1
    // 0x14c9104: ldur            x1, [fp, #-0x10]
    // 0x14c9108: ldur            x2, [fp, #-8]
    // 0x14c910c: r0 = proofFieldWidget()
    //     0x14c910c: bl              #0x14c9128  ; [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget
    // 0x14c9110: LeaveFrame
    //     0x14c9110: mov             SP, fp
    //     0x14c9114: ldp             fp, lr, [SP], #0x10
    // 0x14c9118: ret
    //     0x14c9118: ret             
    // 0x14c911c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c911c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c9120: b               #0x14c9074
    // 0x14c9124: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14c9124: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ proofFieldWidget(/* No info */) {
    // ** addr: 0x14c9128, size: 0xd48
    // 0x14c9128: EnterFrame
    //     0x14c9128: stp             fp, lr, [SP, #-0x10]!
    //     0x14c912c: mov             fp, SP
    // 0x14c9130: AllocStack(0x58)
    //     0x14c9130: sub             SP, SP, #0x58
    // 0x14c9134: SetupParameters(ReturnOrderWithProofView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x14c9134: stur            x1, [fp, #-8]
    //     0x14c9138: stur            x2, [fp, #-0x10]
    //     0x14c913c: stur            x3, [fp, #-0x18]
    // 0x14c9140: CheckStackOverflow
    //     0x14c9140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c9144: cmp             SP, x16
    //     0x14c9148: b.ls            #0x14c9e68
    // 0x14c914c: r1 = 3
    //     0x14c914c: movz            x1, #0x3
    // 0x14c9150: r0 = AllocateContext()
    //     0x14c9150: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c9154: mov             x2, x0
    // 0x14c9158: ldur            x1, [fp, #-8]
    // 0x14c915c: stur            x2, [fp, #-0x20]
    // 0x14c9160: StoreField: r2->field_f = r1
    //     0x14c9160: stur            w1, [x2, #0xf]
    // 0x14c9164: ldur            x0, [fp, #-0x10]
    // 0x14c9168: StoreField: r2->field_13 = r0
    //     0x14c9168: stur            w0, [x2, #0x13]
    // 0x14c916c: ldur            x0, [fp, #-0x18]
    // 0x14c9170: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c9170: stur            w0, [x2, #0x17]
    // 0x14c9174: cmp             w0, NULL
    // 0x14c9178: b.ne            #0x14c9184
    // 0x14c917c: r0 = Null
    //     0x14c917c: mov             x0, NULL
    // 0x14c9180: b               #0x14c9190
    // 0x14c9184: LoadField: r3 = r0->field_7
    //     0x14c9184: ldur            w3, [x0, #7]
    // 0x14c9188: DecompressPointer r3
    //     0x14c9188: add             x3, x3, HEAP, lsl #32
    // 0x14c918c: mov             x0, x3
    // 0x14c9190: r3 = LoadClassIdInstr(r0)
    //     0x14c9190: ldur            x3, [x0, #-1]
    //     0x14c9194: ubfx            x3, x3, #0xc, #0x14
    // 0x14c9198: r16 = "unboxing_videos"
    //     0x14c9198: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "unboxing_videos"
    //     0x14c919c: ldr             x16, [x16, #0x860]
    // 0x14c91a0: stp             x16, x0, [SP]
    // 0x14c91a4: mov             x0, x3
    // 0x14c91a8: mov             lr, x0
    // 0x14c91ac: ldr             lr, [x21, lr, lsl #3]
    // 0x14c91b0: blr             lr
    // 0x14c91b4: tbnz            w0, #4, #0x14c91e4
    // 0x14c91b8: ldur            x2, [fp, #-0x20]
    // 0x14c91bc: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14c91bc: ldur            w0, [x2, #0x17]
    // 0x14c91c0: DecompressPointer r0
    //     0x14c91c0: add             x0, x0, HEAP, lsl #32
    // 0x14c91c4: cmp             w0, NULL
    // 0x14c91c8: b.ne            #0x14c91d4
    // 0x14c91cc: r0 = Null
    //     0x14c91cc: mov             x0, NULL
    // 0x14c91d0: b               #0x14c920c
    // 0x14c91d4: LoadField: r1 = r0->field_b
    //     0x14c91d4: ldur            w1, [x0, #0xb]
    // 0x14c91d8: DecompressPointer r1
    //     0x14c91d8: add             x1, x1, HEAP, lsl #32
    // 0x14c91dc: mov             x0, x1
    // 0x14c91e0: b               #0x14c920c
    // 0x14c91e4: ldur            x2, [fp, #-0x20]
    // 0x14c91e8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14c91e8: ldur            w0, [x2, #0x17]
    // 0x14c91ec: DecompressPointer r0
    //     0x14c91ec: add             x0, x0, HEAP, lsl #32
    // 0x14c91f0: cmp             w0, NULL
    // 0x14c91f4: b.ne            #0x14c9200
    // 0x14c91f8: r0 = Null
    //     0x14c91f8: mov             x0, NULL
    // 0x14c91fc: b               #0x14c920c
    // 0x14c9200: LoadField: r1 = r0->field_b
    //     0x14c9200: ldur            w1, [x0, #0xb]
    // 0x14c9204: DecompressPointer r1
    //     0x14c9204: add             x1, x1, HEAP, lsl #32
    // 0x14c9208: mov             x0, x1
    // 0x14c920c: stur            x0, [fp, #-0x10]
    // 0x14c9210: LoadField: r1 = r2->field_13
    //     0x14c9210: ldur            w1, [x2, #0x13]
    // 0x14c9214: DecompressPointer r1
    //     0x14c9214: add             x1, x1, HEAP, lsl #32
    // 0x14c9218: r0 = of()
    //     0x14c9218: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c921c: LoadField: r1 = r0->field_87
    //     0x14c921c: ldur            w1, [x0, #0x87]
    // 0x14c9220: DecompressPointer r1
    //     0x14c9220: add             x1, x1, HEAP, lsl #32
    // 0x14c9224: LoadField: r0 = r1->field_7
    //     0x14c9224: ldur            w0, [x1, #7]
    // 0x14c9228: DecompressPointer r0
    //     0x14c9228: add             x0, x0, HEAP, lsl #32
    // 0x14c922c: stur            x0, [fp, #-0x18]
    // 0x14c9230: r1 = Instance_Color
    //     0x14c9230: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c9234: d0 = 0.700000
    //     0x14c9234: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14c9238: ldr             d0, [x17, #0xf48]
    // 0x14c923c: r0 = withOpacity()
    //     0x14c923c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c9240: r16 = 12.000000
    //     0x14c9240: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c9244: ldr             x16, [x16, #0x9e8]
    // 0x14c9248: stp             x0, x16, [SP]
    // 0x14c924c: ldur            x1, [fp, #-0x18]
    // 0x14c9250: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c9250: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c9254: ldr             x4, [x4, #0xaa0]
    // 0x14c9258: r0 = copyWith()
    //     0x14c9258: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c925c: stur            x0, [fp, #-0x18]
    // 0x14c9260: r0 = TextSpan()
    //     0x14c9260: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c9264: mov             x2, x0
    // 0x14c9268: ldur            x0, [fp, #-0x10]
    // 0x14c926c: stur            x2, [fp, #-0x28]
    // 0x14c9270: StoreField: r2->field_b = r0
    //     0x14c9270: stur            w0, [x2, #0xb]
    // 0x14c9274: r0 = Instance__DeferringMouseCursor
    //     0x14c9274: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c9278: ArrayStore: r2[0] = r0  ; List_4
    //     0x14c9278: stur            w0, [x2, #0x17]
    // 0x14c927c: ldur            x1, [fp, #-0x18]
    // 0x14c9280: StoreField: r2->field_7 = r1
    //     0x14c9280: stur            w1, [x2, #7]
    // 0x14c9284: ldur            x3, [fp, #-0x20]
    // 0x14c9288: LoadField: r1 = r3->field_13
    //     0x14c9288: ldur            w1, [x3, #0x13]
    // 0x14c928c: DecompressPointer r1
    //     0x14c928c: add             x1, x1, HEAP, lsl #32
    // 0x14c9290: r0 = of()
    //     0x14c9290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c9294: LoadField: r1 = r0->field_87
    //     0x14c9294: ldur            w1, [x0, #0x87]
    // 0x14c9298: DecompressPointer r1
    //     0x14c9298: add             x1, x1, HEAP, lsl #32
    // 0x14c929c: LoadField: r0 = r1->field_7
    //     0x14c929c: ldur            w0, [x1, #7]
    // 0x14c92a0: DecompressPointer r0
    //     0x14c92a0: add             x0, x0, HEAP, lsl #32
    // 0x14c92a4: r16 = 14.000000
    //     0x14c92a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14c92a8: ldr             x16, [x16, #0x1d8]
    // 0x14c92ac: r30 = Instance_Color
    //     0x14c92ac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x14c92b0: ldr             lr, [lr, #0x50]
    // 0x14c92b4: stp             lr, x16, [SP]
    // 0x14c92b8: mov             x1, x0
    // 0x14c92bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c92bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c92c0: ldr             x4, [x4, #0xaa0]
    // 0x14c92c4: r0 = copyWith()
    //     0x14c92c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c92c8: stur            x0, [fp, #-0x10]
    // 0x14c92cc: r0 = TextSpan()
    //     0x14c92cc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c92d0: mov             x3, x0
    // 0x14c92d4: r0 = "*"
    //     0x14c92d4: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "*"
    //     0x14c92d8: ldr             x0, [x0, #0x7a8]
    // 0x14c92dc: stur            x3, [fp, #-0x18]
    // 0x14c92e0: StoreField: r3->field_b = r0
    //     0x14c92e0: stur            w0, [x3, #0xb]
    // 0x14c92e4: r0 = Instance__DeferringMouseCursor
    //     0x14c92e4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c92e8: ArrayStore: r3[0] = r0  ; List_4
    //     0x14c92e8: stur            w0, [x3, #0x17]
    // 0x14c92ec: ldur            x1, [fp, #-0x10]
    // 0x14c92f0: StoreField: r3->field_7 = r1
    //     0x14c92f0: stur            w1, [x3, #7]
    // 0x14c92f4: r1 = Null
    //     0x14c92f4: mov             x1, NULL
    // 0x14c92f8: r2 = 4
    //     0x14c92f8: movz            x2, #0x4
    // 0x14c92fc: r0 = AllocateArray()
    //     0x14c92fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c9300: mov             x2, x0
    // 0x14c9304: ldur            x0, [fp, #-0x28]
    // 0x14c9308: stur            x2, [fp, #-0x10]
    // 0x14c930c: StoreField: r2->field_f = r0
    //     0x14c930c: stur            w0, [x2, #0xf]
    // 0x14c9310: ldur            x0, [fp, #-0x18]
    // 0x14c9314: StoreField: r2->field_13 = r0
    //     0x14c9314: stur            w0, [x2, #0x13]
    // 0x14c9318: r1 = <InlineSpan>
    //     0x14c9318: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x14c931c: ldr             x1, [x1, #0xe40]
    // 0x14c9320: r0 = AllocateGrowableArray()
    //     0x14c9320: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c9324: mov             x1, x0
    // 0x14c9328: ldur            x0, [fp, #-0x10]
    // 0x14c932c: stur            x1, [fp, #-0x18]
    // 0x14c9330: StoreField: r1->field_f = r0
    //     0x14c9330: stur            w0, [x1, #0xf]
    // 0x14c9334: r2 = 4
    //     0x14c9334: movz            x2, #0x4
    // 0x14c9338: StoreField: r1->field_b = r2
    //     0x14c9338: stur            w2, [x1, #0xb]
    // 0x14c933c: r0 = TextSpan()
    //     0x14c933c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x14c9340: mov             x1, x0
    // 0x14c9344: ldur            x0, [fp, #-0x18]
    // 0x14c9348: stur            x1, [fp, #-0x10]
    // 0x14c934c: StoreField: r1->field_f = r0
    //     0x14c934c: stur            w0, [x1, #0xf]
    // 0x14c9350: r0 = Instance__DeferringMouseCursor
    //     0x14c9350: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x14c9354: ArrayStore: r1[0] = r0  ; List_4
    //     0x14c9354: stur            w0, [x1, #0x17]
    // 0x14c9358: r0 = RichText()
    //     0x14c9358: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x14c935c: mov             x1, x0
    // 0x14c9360: ldur            x2, [fp, #-0x10]
    // 0x14c9364: stur            x0, [fp, #-0x10]
    // 0x14c9368: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14c9368: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14c936c: r0 = RichText()
    //     0x14c936c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x14c9370: ldur            x2, [fp, #-0x20]
    // 0x14c9374: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14c9374: ldur            w0, [x2, #0x17]
    // 0x14c9378: DecompressPointer r0
    //     0x14c9378: add             x0, x0, HEAP, lsl #32
    // 0x14c937c: cmp             w0, NULL
    // 0x14c9380: b.ne            #0x14c938c
    // 0x14c9384: r0 = Null
    //     0x14c9384: mov             x0, NULL
    // 0x14c9388: b               #0x14c9398
    // 0x14c938c: LoadField: r1 = r0->field_7
    //     0x14c938c: ldur            w1, [x0, #7]
    // 0x14c9390: DecompressPointer r1
    //     0x14c9390: add             x1, x1, HEAP, lsl #32
    // 0x14c9394: mov             x0, x1
    // 0x14c9398: r1 = LoadClassIdInstr(r0)
    //     0x14c9398: ldur            x1, [x0, #-1]
    //     0x14c939c: ubfx            x1, x1, #0xc, #0x14
    // 0x14c93a0: r16 = "unboxing_videos"
    //     0x14c93a0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "unboxing_videos"
    //     0x14c93a4: ldr             x16, [x16, #0x860]
    // 0x14c93a8: stp             x16, x0, [SP]
    // 0x14c93ac: mov             x0, x1
    // 0x14c93b0: mov             lr, x0
    // 0x14c93b4: ldr             lr, [x21, lr, lsl #3]
    // 0x14c93b8: blr             lr
    // 0x14c93bc: tbnz            w0, #4, #0x14c93fc
    // 0x14c93c0: ldur            x2, [fp, #-0x20]
    // 0x14c93c4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14c93c4: ldur            w0, [x2, #0x17]
    // 0x14c93c8: DecompressPointer r0
    //     0x14c93c8: add             x0, x0, HEAP, lsl #32
    // 0x14c93cc: cmp             w0, NULL
    // 0x14c93d0: b.ne            #0x14c93dc
    // 0x14c93d4: r0 = Null
    //     0x14c93d4: mov             x0, NULL
    // 0x14c93d8: b               #0x14c93e8
    // 0x14c93dc: LoadField: r1 = r0->field_f
    //     0x14c93dc: ldur            w1, [x0, #0xf]
    // 0x14c93e0: DecompressPointer r1
    //     0x14c93e0: add             x1, x1, HEAP, lsl #32
    // 0x14c93e4: mov             x0, x1
    // 0x14c93e8: cmp             w0, NULL
    // 0x14c93ec: b.ne            #0x14c93f4
    // 0x14c93f0: r0 = ""
    //     0x14c93f0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c93f4: mov             x3, x0
    // 0x14c93f8: b               #0x14c9434
    // 0x14c93fc: ldur            x2, [fp, #-0x20]
    // 0x14c9400: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14c9400: ldur            w0, [x2, #0x17]
    // 0x14c9404: DecompressPointer r0
    //     0x14c9404: add             x0, x0, HEAP, lsl #32
    // 0x14c9408: cmp             w0, NULL
    // 0x14c940c: b.ne            #0x14c9418
    // 0x14c9410: r0 = Null
    //     0x14c9410: mov             x0, NULL
    // 0x14c9414: b               #0x14c9424
    // 0x14c9418: LoadField: r1 = r0->field_f
    //     0x14c9418: ldur            w1, [x0, #0xf]
    // 0x14c941c: DecompressPointer r1
    //     0x14c941c: add             x1, x1, HEAP, lsl #32
    // 0x14c9420: mov             x0, x1
    // 0x14c9424: cmp             w0, NULL
    // 0x14c9428: b.ne            #0x14c9430
    // 0x14c942c: r0 = ""
    //     0x14c942c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14c9430: mov             x3, x0
    // 0x14c9434: ldur            x0, [fp, #-0x10]
    // 0x14c9438: stur            x3, [fp, #-0x18]
    // 0x14c943c: LoadField: r1 = r2->field_13
    //     0x14c943c: ldur            w1, [x2, #0x13]
    // 0x14c9440: DecompressPointer r1
    //     0x14c9440: add             x1, x1, HEAP, lsl #32
    // 0x14c9444: r0 = of()
    //     0x14c9444: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c9448: LoadField: r1 = r0->field_87
    //     0x14c9448: ldur            w1, [x0, #0x87]
    // 0x14c944c: DecompressPointer r1
    //     0x14c944c: add             x1, x1, HEAP, lsl #32
    // 0x14c9450: LoadField: r0 = r1->field_2b
    //     0x14c9450: ldur            w0, [x1, #0x2b]
    // 0x14c9454: DecompressPointer r0
    //     0x14c9454: add             x0, x0, HEAP, lsl #32
    // 0x14c9458: stur            x0, [fp, #-0x28]
    // 0x14c945c: r1 = Instance_Color
    //     0x14c945c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c9460: d0 = 0.400000
    //     0x14c9460: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14c9464: r0 = withOpacity()
    //     0x14c9464: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c9468: r16 = 12.000000
    //     0x14c9468: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14c946c: ldr             x16, [x16, #0x9e8]
    // 0x14c9470: stp             x0, x16, [SP]
    // 0x14c9474: ldur            x1, [fp, #-0x28]
    // 0x14c9478: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14c9478: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14c947c: ldr             x4, [x4, #0xaa0]
    // 0x14c9480: r0 = copyWith()
    //     0x14c9480: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14c9484: stur            x0, [fp, #-0x28]
    // 0x14c9488: r0 = Text()
    //     0x14c9488: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14c948c: mov             x1, x0
    // 0x14c9490: ldur            x0, [fp, #-0x18]
    // 0x14c9494: stur            x1, [fp, #-0x30]
    // 0x14c9498: StoreField: r1->field_b = r0
    //     0x14c9498: stur            w0, [x1, #0xb]
    // 0x14c949c: ldur            x0, [fp, #-0x28]
    // 0x14c94a0: StoreField: r1->field_13 = r0
    //     0x14c94a0: stur            w0, [x1, #0x13]
    // 0x14c94a4: r0 = Padding()
    //     0x14c94a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c94a8: mov             x3, x0
    // 0x14c94ac: r0 = Instance_EdgeInsets
    //     0x14c94ac: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x14c94b0: ldr             x0, [x0, #0x770]
    // 0x14c94b4: stur            x3, [fp, #-0x18]
    // 0x14c94b8: StoreField: r3->field_f = r0
    //     0x14c94b8: stur            w0, [x3, #0xf]
    // 0x14c94bc: ldur            x0, [fp, #-0x30]
    // 0x14c94c0: StoreField: r3->field_b = r0
    //     0x14c94c0: stur            w0, [x3, #0xb]
    // 0x14c94c4: r1 = Null
    //     0x14c94c4: mov             x1, NULL
    // 0x14c94c8: r2 = 4
    //     0x14c94c8: movz            x2, #0x4
    // 0x14c94cc: r0 = AllocateArray()
    //     0x14c94cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14c94d0: mov             x2, x0
    // 0x14c94d4: ldur            x0, [fp, #-0x10]
    // 0x14c94d8: stur            x2, [fp, #-0x28]
    // 0x14c94dc: StoreField: r2->field_f = r0
    //     0x14c94dc: stur            w0, [x2, #0xf]
    // 0x14c94e0: ldur            x0, [fp, #-0x18]
    // 0x14c94e4: StoreField: r2->field_13 = r0
    //     0x14c94e4: stur            w0, [x2, #0x13]
    // 0x14c94e8: r1 = <Widget>
    //     0x14c94e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14c94ec: r0 = AllocateGrowableArray()
    //     0x14c94ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14c94f0: mov             x1, x0
    // 0x14c94f4: ldur            x0, [fp, #-0x28]
    // 0x14c94f8: stur            x1, [fp, #-0x10]
    // 0x14c94fc: StoreField: r1->field_f = r0
    //     0x14c94fc: stur            w0, [x1, #0xf]
    // 0x14c9500: r0 = 4
    //     0x14c9500: movz            x0, #0x4
    // 0x14c9504: StoreField: r1->field_b = r0
    //     0x14c9504: stur            w0, [x1, #0xb]
    // 0x14c9508: ldur            x2, [fp, #-0x20]
    // 0x14c950c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x14c950c: ldur            w0, [x2, #0x17]
    // 0x14c9510: DecompressPointer r0
    //     0x14c9510: add             x0, x0, HEAP, lsl #32
    // 0x14c9514: cmp             w0, NULL
    // 0x14c9518: b.ne            #0x14c9524
    // 0x14c951c: r0 = Null
    //     0x14c951c: mov             x0, NULL
    // 0x14c9520: b               #0x14c9530
    // 0x14c9524: LoadField: r3 = r0->field_7
    //     0x14c9524: ldur            w3, [x0, #7]
    // 0x14c9528: DecompressPointer r3
    //     0x14c9528: add             x3, x3, HEAP, lsl #32
    // 0x14c952c: mov             x0, x3
    // 0x14c9530: r3 = LoadClassIdInstr(r0)
    //     0x14c9530: ldur            x3, [x0, #-1]
    //     0x14c9534: ubfx            x3, x3, #0xc, #0x14
    // 0x14c9538: r16 = "unboxing_videos"
    //     0x14c9538: add             x16, PP, #0x33, lsl #12  ; [pp+0x33860] "unboxing_videos"
    //     0x14c953c: ldr             x16, [x16, #0x860]
    // 0x14c9540: stp             x16, x0, [SP]
    // 0x14c9544: mov             x0, x3
    // 0x14c9548: mov             lr, x0
    // 0x14c954c: ldr             lr, [x21, lr, lsl #3]
    // 0x14c9550: blr             lr
    // 0x14c9554: tbnz            w0, #4, #0x14c9990
    // 0x14c9558: ldur            x1, [fp, #-8]
    // 0x14c955c: r0 = controller()
    //     0x14c955c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9560: LoadField: r1 = r0->field_73
    //     0x14c9560: ldur            w1, [x0, #0x73]
    // 0x14c9564: DecompressPointer r1
    //     0x14c9564: add             x1, x1, HEAP, lsl #32
    // 0x14c9568: r0 = value()
    //     0x14c9568: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c956c: r1 = LoadClassIdInstr(r0)
    //     0x14c956c: ldur            x1, [x0, #-1]
    //     0x14c9570: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9574: str             x0, [SP]
    // 0x14c9578: mov             x0, x1
    // 0x14c957c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c957c: movz            x17, #0xc898
    //     0x14c9580: add             lr, x0, x17
    //     0x14c9584: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9588: blr             lr
    // 0x14c958c: cbnz            w0, #0x14c9740
    // 0x14c9590: ldur            x2, [fp, #-0x20]
    // 0x14c9594: r0 = Radius()
    //     0x14c9594: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c9598: d0 = 12.000000
    //     0x14c9598: fmov            d0, #12.00000000
    // 0x14c959c: stur            x0, [fp, #-0x18]
    // 0x14c95a0: StoreField: r0->field_7 = d0
    //     0x14c95a0: stur            d0, [x0, #7]
    // 0x14c95a4: StoreField: r0->field_f = d0
    //     0x14c95a4: stur            d0, [x0, #0xf]
    // 0x14c95a8: r0 = BorderRadius()
    //     0x14c95a8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c95ac: mov             x2, x0
    // 0x14c95b0: ldur            x0, [fp, #-0x18]
    // 0x14c95b4: stur            x2, [fp, #-0x28]
    // 0x14c95b8: StoreField: r2->field_7 = r0
    //     0x14c95b8: stur            w0, [x2, #7]
    // 0x14c95bc: StoreField: r2->field_b = r0
    //     0x14c95bc: stur            w0, [x2, #0xb]
    // 0x14c95c0: StoreField: r2->field_f = r0
    //     0x14c95c0: stur            w0, [x2, #0xf]
    // 0x14c95c4: StoreField: r2->field_13 = r0
    //     0x14c95c4: stur            w0, [x2, #0x13]
    // 0x14c95c8: r1 = Instance_Color
    //     0x14c95c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c95cc: d0 = 0.100000
    //     0x14c95cc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14c95d0: r0 = withOpacity()
    //     0x14c95d0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c95d4: r16 = 1.000000
    //     0x14c95d4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14c95d8: str             x16, [SP]
    // 0x14c95dc: mov             x2, x0
    // 0x14c95e0: r1 = Null
    //     0x14c95e0: mov             x1, NULL
    // 0x14c95e4: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14c95e4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14c95e8: ldr             x4, [x4, #0x108]
    // 0x14c95ec: r0 = Border.all()
    //     0x14c95ec: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14c95f0: stur            x0, [fp, #-0x18]
    // 0x14c95f4: r0 = BoxDecoration()
    //     0x14c95f4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14c95f8: mov             x2, x0
    // 0x14c95fc: ldur            x0, [fp, #-0x18]
    // 0x14c9600: stur            x2, [fp, #-0x30]
    // 0x14c9604: StoreField: r2->field_f = r0
    //     0x14c9604: stur            w0, [x2, #0xf]
    // 0x14c9608: ldur            x0, [fp, #-0x28]
    // 0x14c960c: StoreField: r2->field_13 = r0
    //     0x14c960c: stur            w0, [x2, #0x13]
    // 0x14c9610: r0 = Instance_BoxShape
    //     0x14c9610: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c9614: ldr             x0, [x0, #0x80]
    // 0x14c9618: StoreField: r2->field_23 = r0
    //     0x14c9618: stur            w0, [x2, #0x23]
    // 0x14c961c: ldur            x3, [fp, #-0x20]
    // 0x14c9620: LoadField: r1 = r3->field_13
    //     0x14c9620: ldur            w1, [x3, #0x13]
    // 0x14c9624: DecompressPointer r1
    //     0x14c9624: add             x1, x1, HEAP, lsl #32
    // 0x14c9628: r0 = of()
    //     0x14c9628: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c962c: LoadField: r1 = r0->field_5b
    //     0x14c962c: ldur            w1, [x0, #0x5b]
    // 0x14c9630: DecompressPointer r1
    //     0x14c9630: add             x1, x1, HEAP, lsl #32
    // 0x14c9634: stur            x1, [fp, #-0x18]
    // 0x14c9638: r0 = ColorFilter()
    //     0x14c9638: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14c963c: mov             x1, x0
    // 0x14c9640: ldur            x0, [fp, #-0x18]
    // 0x14c9644: stur            x1, [fp, #-0x28]
    // 0x14c9648: StoreField: r1->field_7 = r0
    //     0x14c9648: stur            w0, [x1, #7]
    // 0x14c964c: r0 = Instance_BlendMode
    //     0x14c964c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14c9650: ldr             x0, [x0, #0xb30]
    // 0x14c9654: StoreField: r1->field_b = r0
    //     0x14c9654: stur            w0, [x1, #0xb]
    // 0x14c9658: r2 = 1
    //     0x14c9658: movz            x2, #0x1
    // 0x14c965c: StoreField: r1->field_13 = r2
    //     0x14c965c: stur            x2, [x1, #0x13]
    // 0x14c9660: r0 = SvgPicture()
    //     0x14c9660: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14c9664: stur            x0, [fp, #-0x18]
    // 0x14c9668: r16 = Instance_BoxFit
    //     0x14c9668: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14c966c: ldr             x16, [x16, #0x118]
    // 0x14c9670: ldur            lr, [fp, #-0x28]
    // 0x14c9674: stp             lr, x16, [SP]
    // 0x14c9678: mov             x1, x0
    // 0x14c967c: r2 = "assets/images/image_bg.svg"
    //     0x14c967c: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14c9680: ldr             x2, [x2, #0x868]
    // 0x14c9684: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14c9684: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14c9688: ldr             x4, [x4, #0x820]
    // 0x14c968c: r0 = SvgPicture.asset()
    //     0x14c968c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14c9690: r0 = Center()
    //     0x14c9690: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14c9694: r3 = Instance_Alignment
    //     0x14c9694: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14c9698: ldr             x3, [x3, #0xb10]
    // 0x14c969c: stur            x0, [fp, #-0x28]
    // 0x14c96a0: StoreField: r0->field_f = r3
    //     0x14c96a0: stur            w3, [x0, #0xf]
    // 0x14c96a4: ldur            x1, [fp, #-0x18]
    // 0x14c96a8: StoreField: r0->field_b = r1
    //     0x14c96a8: stur            w1, [x0, #0xb]
    // 0x14c96ac: r0 = Container()
    //     0x14c96ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c96b0: stur            x0, [fp, #-0x18]
    // 0x14c96b4: r16 = inf
    //     0x14c96b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14c96b8: ldr             x16, [x16, #0x9f8]
    // 0x14c96bc: r30 = 60.000000
    //     0x14c96bc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14c96c0: ldr             lr, [lr, #0x110]
    // 0x14c96c4: stp             lr, x16, [SP, #0x10]
    // 0x14c96c8: ldur            x16, [fp, #-0x30]
    // 0x14c96cc: ldur            lr, [fp, #-0x28]
    // 0x14c96d0: stp             lr, x16, [SP]
    // 0x14c96d4: mov             x1, x0
    // 0x14c96d8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14c96d8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14c96dc: ldr             x4, [x4, #0x870]
    // 0x14c96e0: r0 = Container()
    //     0x14c96e0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c96e4: r0 = InkWell()
    //     0x14c96e4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14c96e8: mov             x3, x0
    // 0x14c96ec: ldur            x0, [fp, #-0x18]
    // 0x14c96f0: stur            x3, [fp, #-0x28]
    // 0x14c96f4: StoreField: r3->field_b = r0
    //     0x14c96f4: stur            w0, [x3, #0xb]
    // 0x14c96f8: ldur            x2, [fp, #-0x20]
    // 0x14c96fc: r1 = Function '<anonymous closure>':.
    //     0x14c96fc: add             x1, PP, #0x42, lsl #12  ; [pp+0x42648] AnonymousClosure: (0x9a826c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14c9700: ldr             x1, [x1, #0x648]
    // 0x14c9704: r0 = AllocateClosure()
    //     0x14c9704: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c9708: mov             x1, x0
    // 0x14c970c: ldur            x0, [fp, #-0x28]
    // 0x14c9710: StoreField: r0->field_f = r1
    //     0x14c9710: stur            w1, [x0, #0xf]
    // 0x14c9714: r4 = true
    //     0x14c9714: add             x4, NULL, #0x20  ; true
    // 0x14c9718: StoreField: r0->field_43 = r4
    //     0x14c9718: stur            w4, [x0, #0x43]
    // 0x14c971c: r5 = Instance_BoxShape
    //     0x14c971c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c9720: ldr             x5, [x5, #0x80]
    // 0x14c9724: StoreField: r0->field_47 = r5
    //     0x14c9724: stur            w5, [x0, #0x47]
    // 0x14c9728: StoreField: r0->field_6f = r4
    //     0x14c9728: stur            w4, [x0, #0x6f]
    // 0x14c972c: r6 = false
    //     0x14c972c: add             x6, NULL, #0x30  ; false
    // 0x14c9730: StoreField: r0->field_73 = r6
    //     0x14c9730: stur            w6, [x0, #0x73]
    // 0x14c9734: StoreField: r0->field_83 = r4
    //     0x14c9734: stur            w4, [x0, #0x83]
    // 0x14c9738: StoreField: r0->field_7b = r6
    //     0x14c9738: stur            w6, [x0, #0x7b]
    // 0x14c973c: b               #0x14c98d0
    // 0x14c9740: ldur            x2, [fp, #-0x20]
    // 0x14c9744: ldur            x1, [fp, #-8]
    // 0x14c9748: r0 = controller()
    //     0x14c9748: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c974c: LoadField: r1 = r0->field_73
    //     0x14c974c: ldur            w1, [x0, #0x73]
    // 0x14c9750: DecompressPointer r1
    //     0x14c9750: add             x1, x1, HEAP, lsl #32
    // 0x14c9754: r0 = value()
    //     0x14c9754: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9758: r1 = LoadClassIdInstr(r0)
    //     0x14c9758: ldur            x1, [x0, #-1]
    //     0x14c975c: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9760: str             x0, [SP]
    // 0x14c9764: mov             x0, x1
    // 0x14c9768: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9768: movz            x17, #0xc898
    //     0x14c976c: add             lr, x0, x17
    //     0x14c9770: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9774: blr             lr
    // 0x14c9778: ldur            x2, [fp, #-0x20]
    // 0x14c977c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14c977c: ldur            w1, [x2, #0x17]
    // 0x14c9780: DecompressPointer r1
    //     0x14c9780: add             x1, x1, HEAP, lsl #32
    // 0x14c9784: cmp             w1, NULL
    // 0x14c9788: b.ne            #0x14c9794
    // 0x14c978c: r1 = Null
    //     0x14c978c: mov             x1, NULL
    // 0x14c9790: b               #0x14c97a0
    // 0x14c9794: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x14c9794: ldur            w3, [x1, #0x17]
    // 0x14c9798: DecompressPointer r3
    //     0x14c9798: add             x3, x3, HEAP, lsl #32
    // 0x14c979c: mov             x1, x3
    // 0x14c97a0: cmp             w1, NULL
    // 0x14c97a4: b.ne            #0x14c97b0
    // 0x14c97a8: r1 = 0
    //     0x14c97a8: movz            x1, #0
    // 0x14c97ac: b               #0x14c97c0
    // 0x14c97b0: r3 = LoadInt32Instr(r1)
    //     0x14c97b0: sbfx            x3, x1, #1, #0x1f
    //     0x14c97b4: tbz             w1, #0, #0x14c97bc
    //     0x14c97b8: ldur            x3, [x1, #7]
    // 0x14c97bc: mov             x1, x3
    // 0x14c97c0: r3 = LoadInt32Instr(r0)
    //     0x14c97c0: sbfx            x3, x0, #1, #0x1f
    //     0x14c97c4: tbz             w0, #0, #0x14c97cc
    //     0x14c97c8: ldur            x3, [x0, #7]
    // 0x14c97cc: cmp             x3, x1
    // 0x14c97d0: b.ge            #0x14c9820
    // 0x14c97d4: ldur            x1, [fp, #-8]
    // 0x14c97d8: r0 = controller()
    //     0x14c97d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c97dc: LoadField: r1 = r0->field_73
    //     0x14c97dc: ldur            w1, [x0, #0x73]
    // 0x14c97e0: DecompressPointer r1
    //     0x14c97e0: add             x1, x1, HEAP, lsl #32
    // 0x14c97e4: r0 = value()
    //     0x14c97e4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c97e8: r1 = LoadClassIdInstr(r0)
    //     0x14c97e8: ldur            x1, [x0, #-1]
    //     0x14c97ec: ubfx            x1, x1, #0xc, #0x14
    // 0x14c97f0: str             x0, [SP]
    // 0x14c97f4: mov             x0, x1
    // 0x14c97f8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c97f8: movz            x17, #0xc898
    //     0x14c97fc: add             lr, x0, x17
    //     0x14c9800: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9804: blr             lr
    // 0x14c9808: r1 = LoadInt32Instr(r0)
    //     0x14c9808: sbfx            x1, x0, #1, #0x1f
    //     0x14c980c: tbz             w0, #0, #0x14c9814
    //     0x14c9810: ldur            x1, [x0, #7]
    // 0x14c9814: add             x0, x1, #1
    // 0x14c9818: mov             x3, x0
    // 0x14c981c: b               #0x14c9864
    // 0x14c9820: ldur            x1, [fp, #-8]
    // 0x14c9824: r0 = controller()
    //     0x14c9824: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9828: LoadField: r1 = r0->field_73
    //     0x14c9828: ldur            w1, [x0, #0x73]
    // 0x14c982c: DecompressPointer r1
    //     0x14c982c: add             x1, x1, HEAP, lsl #32
    // 0x14c9830: r0 = value()
    //     0x14c9830: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9834: r1 = LoadClassIdInstr(r0)
    //     0x14c9834: ldur            x1, [x0, #-1]
    //     0x14c9838: ubfx            x1, x1, #0xc, #0x14
    // 0x14c983c: str             x0, [SP]
    // 0x14c9840: mov             x0, x1
    // 0x14c9844: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9844: movz            x17, #0xc898
    //     0x14c9848: add             lr, x0, x17
    //     0x14c984c: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9850: blr             lr
    // 0x14c9854: r1 = LoadInt32Instr(r0)
    //     0x14c9854: sbfx            x1, x0, #1, #0x1f
    //     0x14c9858: tbz             w0, #0, #0x14c9860
    //     0x14c985c: ldur            x1, [x0, #7]
    // 0x14c9860: mov             x3, x1
    // 0x14c9864: stur            x3, [fp, #-0x38]
    // 0x14c9868: r1 = Function '<anonymous closure>':.
    //     0x14c9868: add             x1, PP, #0x42, lsl #12  ; [pp+0x42650] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14c986c: ldr             x1, [x1, #0x650]
    // 0x14c9870: r2 = Null
    //     0x14c9870: mov             x2, NULL
    // 0x14c9874: r0 = AllocateClosure()
    //     0x14c9874: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c9878: ldur            x2, [fp, #-0x20]
    // 0x14c987c: r1 = Function '<anonymous closure>':.
    //     0x14c987c: add             x1, PP, #0x42, lsl #12  ; [pp+0x42658] AnonymousClosure: (0x14ca6a4), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14c9128)
    //     0x14c9880: ldr             x1, [x1, #0x658]
    // 0x14c9884: stur            x0, [fp, #-0x18]
    // 0x14c9888: r0 = AllocateClosure()
    //     0x14c9888: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c988c: stur            x0, [fp, #-0x28]
    // 0x14c9890: r0 = ListView()
    //     0x14c9890: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14c9894: stur            x0, [fp, #-0x30]
    // 0x14c9898: r16 = Instance_BouncingScrollPhysics
    //     0x14c9898: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x14c989c: ldr             x16, [x16, #0x890]
    // 0x14c98a0: r30 = true
    //     0x14c98a0: add             lr, NULL, #0x20  ; true
    // 0x14c98a4: stp             lr, x16, [SP, #8]
    // 0x14c98a8: r16 = Instance_Axis
    //     0x14c98a8: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c98ac: str             x16, [SP]
    // 0x14c98b0: mov             x1, x0
    // 0x14c98b4: ldur            x2, [fp, #-0x28]
    // 0x14c98b8: ldur            x3, [fp, #-0x38]
    // 0x14c98bc: ldur            x5, [fp, #-0x18]
    // 0x14c98c0: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x6, shrinkWrap, 0x5, null]
    //     0x14c98c0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33898] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x6, "shrinkWrap", 0x5, Null]
    //     0x14c98c4: ldr             x4, [x4, #0x898]
    // 0x14c98c8: r0 = ListView.separated()
    //     0x14c98c8: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14c98cc: ldur            x0, [fp, #-0x30]
    // 0x14c98d0: ldur            x1, [fp, #-0x10]
    // 0x14c98d4: stur            x0, [fp, #-0x18]
    // 0x14c98d8: r0 = SizedBox()
    //     0x14c98d8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14c98dc: r7 = 60.000000
    //     0x14c98dc: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14c98e0: ldr             x7, [x7, #0x110]
    // 0x14c98e4: stur            x0, [fp, #-0x28]
    // 0x14c98e8: StoreField: r0->field_13 = r7
    //     0x14c98e8: stur            w7, [x0, #0x13]
    // 0x14c98ec: ldur            x1, [fp, #-0x18]
    // 0x14c98f0: StoreField: r0->field_b = r1
    //     0x14c98f0: stur            w1, [x0, #0xb]
    // 0x14c98f4: r0 = Padding()
    //     0x14c98f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c98f8: r8 = Instance_EdgeInsets
    //     0x14c98f8: add             x8, PP, #0x33, lsl #12  ; [pp+0x338a0] Obj!EdgeInsets@d575f1
    //     0x14c98fc: ldr             x8, [x8, #0x8a0]
    // 0x14c9900: stur            x0, [fp, #-0x18]
    // 0x14c9904: StoreField: r0->field_f = r8
    //     0x14c9904: stur            w8, [x0, #0xf]
    // 0x14c9908: ldur            x1, [fp, #-0x28]
    // 0x14c990c: StoreField: r0->field_b = r1
    //     0x14c990c: stur            w1, [x0, #0xb]
    // 0x14c9910: ldur            x2, [fp, #-0x10]
    // 0x14c9914: LoadField: r1 = r2->field_b
    //     0x14c9914: ldur            w1, [x2, #0xb]
    // 0x14c9918: LoadField: r3 = r2->field_f
    //     0x14c9918: ldur            w3, [x2, #0xf]
    // 0x14c991c: DecompressPointer r3
    //     0x14c991c: add             x3, x3, HEAP, lsl #32
    // 0x14c9920: LoadField: r4 = r3->field_b
    //     0x14c9920: ldur            w4, [x3, #0xb]
    // 0x14c9924: r3 = LoadInt32Instr(r1)
    //     0x14c9924: sbfx            x3, x1, #1, #0x1f
    // 0x14c9928: stur            x3, [fp, #-0x38]
    // 0x14c992c: r1 = LoadInt32Instr(r4)
    //     0x14c992c: sbfx            x1, x4, #1, #0x1f
    // 0x14c9930: cmp             x3, x1
    // 0x14c9934: b.ne            #0x14c9940
    // 0x14c9938: mov             x1, x2
    // 0x14c993c: r0 = _growToNextCapacity()
    //     0x14c993c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14c9940: ldur            x9, [fp, #-0x10]
    // 0x14c9944: ldur            x2, [fp, #-0x38]
    // 0x14c9948: add             x0, x2, #1
    // 0x14c994c: lsl             x1, x0, #1
    // 0x14c9950: StoreField: r9->field_b = r1
    //     0x14c9950: stur            w1, [x9, #0xb]
    // 0x14c9954: LoadField: r1 = r9->field_f
    //     0x14c9954: ldur            w1, [x9, #0xf]
    // 0x14c9958: DecompressPointer r1
    //     0x14c9958: add             x1, x1, HEAP, lsl #32
    // 0x14c995c: ldur            x0, [fp, #-0x18]
    // 0x14c9960: ArrayStore: r1[r2] = r0  ; List_4
    //     0x14c9960: add             x25, x1, x2, lsl #2
    //     0x14c9964: add             x25, x25, #0xf
    //     0x14c9968: str             w0, [x25]
    //     0x14c996c: tbz             w0, #0, #0x14c9988
    //     0x14c9970: ldurb           w16, [x1, #-1]
    //     0x14c9974: ldurb           w17, [x0, #-1]
    //     0x14c9978: and             x16, x17, x16, lsr #2
    //     0x14c997c: tst             x16, HEAP, lsr #32
    //     0x14c9980: b.eq            #0x14c9988
    //     0x14c9984: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c9988: mov             x2, x9
    // 0x14c998c: b               #0x14c9e08
    // 0x14c9990: ldur            x9, [fp, #-0x10]
    // 0x14c9994: r4 = true
    //     0x14c9994: add             x4, NULL, #0x20  ; true
    // 0x14c9998: r7 = 60.000000
    //     0x14c9998: add             x7, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14c999c: ldr             x7, [x7, #0x110]
    // 0x14c99a0: r8 = Instance_EdgeInsets
    //     0x14c99a0: add             x8, PP, #0x33, lsl #12  ; [pp+0x338a0] Obj!EdgeInsets@d575f1
    //     0x14c99a4: ldr             x8, [x8, #0x8a0]
    // 0x14c99a8: r5 = Instance_BoxShape
    //     0x14c99a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c99ac: ldr             x5, [x5, #0x80]
    // 0x14c99b0: r0 = Instance_BlendMode
    //     0x14c99b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14c99b4: ldr             x0, [x0, #0xb30]
    // 0x14c99b8: r6 = false
    //     0x14c99b8: add             x6, NULL, #0x30  ; false
    // 0x14c99bc: r3 = Instance_Alignment
    //     0x14c99bc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14c99c0: ldr             x3, [x3, #0xb10]
    // 0x14c99c4: d0 = 12.000000
    //     0x14c99c4: fmov            d0, #12.00000000
    // 0x14c99c8: r2 = 1
    //     0x14c99c8: movz            x2, #0x1
    // 0x14c99cc: ldur            x1, [fp, #-8]
    // 0x14c99d0: r0 = controller()
    //     0x14c99d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c99d4: LoadField: r1 = r0->field_7b
    //     0x14c99d4: ldur            w1, [x0, #0x7b]
    // 0x14c99d8: DecompressPointer r1
    //     0x14c99d8: add             x1, x1, HEAP, lsl #32
    // 0x14c99dc: r0 = value()
    //     0x14c99dc: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c99e0: r1 = LoadClassIdInstr(r0)
    //     0x14c99e0: ldur            x1, [x0, #-1]
    //     0x14c99e4: ubfx            x1, x1, #0xc, #0x14
    // 0x14c99e8: str             x0, [SP]
    // 0x14c99ec: mov             x0, x1
    // 0x14c99f0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c99f0: movz            x17, #0xc898
    //     0x14c99f4: add             lr, x0, x17
    //     0x14c99f8: ldr             lr, [x21, lr, lsl #3]
    //     0x14c99fc: blr             lr
    // 0x14c9a00: cbnz            w0, #0x14c9bb8
    // 0x14c9a04: ldur            x2, [fp, #-0x20]
    // 0x14c9a08: r0 = Radius()
    //     0x14c9a08: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c9a0c: d0 = 12.000000
    //     0x14c9a0c: fmov            d0, #12.00000000
    // 0x14c9a10: stur            x0, [fp, #-0x18]
    // 0x14c9a14: StoreField: r0->field_7 = d0
    //     0x14c9a14: stur            d0, [x0, #7]
    // 0x14c9a18: StoreField: r0->field_f = d0
    //     0x14c9a18: stur            d0, [x0, #0xf]
    // 0x14c9a1c: r0 = BorderRadius()
    //     0x14c9a1c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c9a20: mov             x2, x0
    // 0x14c9a24: ldur            x0, [fp, #-0x18]
    // 0x14c9a28: stur            x2, [fp, #-0x28]
    // 0x14c9a2c: StoreField: r2->field_7 = r0
    //     0x14c9a2c: stur            w0, [x2, #7]
    // 0x14c9a30: StoreField: r2->field_b = r0
    //     0x14c9a30: stur            w0, [x2, #0xb]
    // 0x14c9a34: StoreField: r2->field_f = r0
    //     0x14c9a34: stur            w0, [x2, #0xf]
    // 0x14c9a38: StoreField: r2->field_13 = r0
    //     0x14c9a38: stur            w0, [x2, #0x13]
    // 0x14c9a3c: r1 = Instance_Color
    //     0x14c9a3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c9a40: d0 = 0.100000
    //     0x14c9a40: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14c9a44: r0 = withOpacity()
    //     0x14c9a44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c9a48: r16 = 1.000000
    //     0x14c9a48: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14c9a4c: str             x16, [SP]
    // 0x14c9a50: mov             x2, x0
    // 0x14c9a54: r1 = Null
    //     0x14c9a54: mov             x1, NULL
    // 0x14c9a58: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14c9a58: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14c9a5c: ldr             x4, [x4, #0x108]
    // 0x14c9a60: r0 = Border.all()
    //     0x14c9a60: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14c9a64: stur            x0, [fp, #-0x18]
    // 0x14c9a68: r0 = BoxDecoration()
    //     0x14c9a68: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14c9a6c: mov             x2, x0
    // 0x14c9a70: ldur            x0, [fp, #-0x18]
    // 0x14c9a74: stur            x2, [fp, #-0x30]
    // 0x14c9a78: StoreField: r2->field_f = r0
    //     0x14c9a78: stur            w0, [x2, #0xf]
    // 0x14c9a7c: ldur            x0, [fp, #-0x28]
    // 0x14c9a80: StoreField: r2->field_13 = r0
    //     0x14c9a80: stur            w0, [x2, #0x13]
    // 0x14c9a84: r0 = Instance_BoxShape
    //     0x14c9a84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c9a88: ldr             x0, [x0, #0x80]
    // 0x14c9a8c: StoreField: r2->field_23 = r0
    //     0x14c9a8c: stur            w0, [x2, #0x23]
    // 0x14c9a90: ldur            x3, [fp, #-0x20]
    // 0x14c9a94: LoadField: r1 = r3->field_13
    //     0x14c9a94: ldur            w1, [x3, #0x13]
    // 0x14c9a98: DecompressPointer r1
    //     0x14c9a98: add             x1, x1, HEAP, lsl #32
    // 0x14c9a9c: r0 = of()
    //     0x14c9a9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14c9aa0: LoadField: r1 = r0->field_5b
    //     0x14c9aa0: ldur            w1, [x0, #0x5b]
    // 0x14c9aa4: DecompressPointer r1
    //     0x14c9aa4: add             x1, x1, HEAP, lsl #32
    // 0x14c9aa8: stur            x1, [fp, #-0x18]
    // 0x14c9aac: r0 = ColorFilter()
    //     0x14c9aac: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14c9ab0: mov             x1, x0
    // 0x14c9ab4: ldur            x0, [fp, #-0x18]
    // 0x14c9ab8: stur            x1, [fp, #-0x28]
    // 0x14c9abc: StoreField: r1->field_7 = r0
    //     0x14c9abc: stur            w0, [x1, #7]
    // 0x14c9ac0: r0 = Instance_BlendMode
    //     0x14c9ac0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14c9ac4: ldr             x0, [x0, #0xb30]
    // 0x14c9ac8: StoreField: r1->field_b = r0
    //     0x14c9ac8: stur            w0, [x1, #0xb]
    // 0x14c9acc: r0 = 1
    //     0x14c9acc: movz            x0, #0x1
    // 0x14c9ad0: StoreField: r1->field_13 = r0
    //     0x14c9ad0: stur            x0, [x1, #0x13]
    // 0x14c9ad4: r0 = SvgPicture()
    //     0x14c9ad4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14c9ad8: stur            x0, [fp, #-0x18]
    // 0x14c9adc: r16 = Instance_BoxFit
    //     0x14c9adc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14c9ae0: ldr             x16, [x16, #0x118]
    // 0x14c9ae4: ldur            lr, [fp, #-0x28]
    // 0x14c9ae8: stp             lr, x16, [SP]
    // 0x14c9aec: mov             x1, x0
    // 0x14c9af0: r2 = "assets/images/image_bg.svg"
    //     0x14c9af0: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14c9af4: ldr             x2, [x2, #0x868]
    // 0x14c9af8: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14c9af8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14c9afc: ldr             x4, [x4, #0x820]
    // 0x14c9b00: r0 = SvgPicture.asset()
    //     0x14c9b00: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14c9b04: r0 = Center()
    //     0x14c9b04: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14c9b08: mov             x1, x0
    // 0x14c9b0c: r0 = Instance_Alignment
    //     0x14c9b0c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14c9b10: ldr             x0, [x0, #0xb10]
    // 0x14c9b14: stur            x1, [fp, #-0x28]
    // 0x14c9b18: StoreField: r1->field_f = r0
    //     0x14c9b18: stur            w0, [x1, #0xf]
    // 0x14c9b1c: ldur            x0, [fp, #-0x18]
    // 0x14c9b20: StoreField: r1->field_b = r0
    //     0x14c9b20: stur            w0, [x1, #0xb]
    // 0x14c9b24: r0 = Container()
    //     0x14c9b24: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14c9b28: stur            x0, [fp, #-0x18]
    // 0x14c9b2c: r16 = inf
    //     0x14c9b2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x14c9b30: ldr             x16, [x16, #0x9f8]
    // 0x14c9b34: r30 = 60.000000
    //     0x14c9b34: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14c9b38: ldr             lr, [lr, #0x110]
    // 0x14c9b3c: stp             lr, x16, [SP, #0x10]
    // 0x14c9b40: ldur            x16, [fp, #-0x30]
    // 0x14c9b44: ldur            lr, [fp, #-0x28]
    // 0x14c9b48: stp             lr, x16, [SP]
    // 0x14c9b4c: mov             x1, x0
    // 0x14c9b50: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14c9b50: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14c9b54: ldr             x4, [x4, #0x870]
    // 0x14c9b58: r0 = Container()
    //     0x14c9b58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14c9b5c: r0 = InkWell()
    //     0x14c9b5c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14c9b60: mov             x3, x0
    // 0x14c9b64: ldur            x0, [fp, #-0x18]
    // 0x14c9b68: stur            x3, [fp, #-0x28]
    // 0x14c9b6c: StoreField: r3->field_b = r0
    //     0x14c9b6c: stur            w0, [x3, #0xb]
    // 0x14c9b70: ldur            x2, [fp, #-0x20]
    // 0x14c9b74: r1 = Function '<anonymous closure>':.
    //     0x14c9b74: add             x1, PP, #0x42, lsl #12  ; [pp+0x42660] AnonymousClosure: (0x9a7888), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14c9b78: ldr             x1, [x1, #0x660]
    // 0x14c9b7c: r0 = AllocateClosure()
    //     0x14c9b7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c9b80: mov             x1, x0
    // 0x14c9b84: ldur            x0, [fp, #-0x28]
    // 0x14c9b88: StoreField: r0->field_f = r1
    //     0x14c9b88: stur            w1, [x0, #0xf]
    // 0x14c9b8c: r1 = true
    //     0x14c9b8c: add             x1, NULL, #0x20  ; true
    // 0x14c9b90: StoreField: r0->field_43 = r1
    //     0x14c9b90: stur            w1, [x0, #0x43]
    // 0x14c9b94: r2 = Instance_BoxShape
    //     0x14c9b94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14c9b98: ldr             x2, [x2, #0x80]
    // 0x14c9b9c: StoreField: r0->field_47 = r2
    //     0x14c9b9c: stur            w2, [x0, #0x47]
    // 0x14c9ba0: StoreField: r0->field_6f = r1
    //     0x14c9ba0: stur            w1, [x0, #0x6f]
    // 0x14c9ba4: r2 = false
    //     0x14c9ba4: add             x2, NULL, #0x30  ; false
    // 0x14c9ba8: StoreField: r0->field_73 = r2
    //     0x14c9ba8: stur            w2, [x0, #0x73]
    // 0x14c9bac: StoreField: r0->field_83 = r1
    //     0x14c9bac: stur            w1, [x0, #0x83]
    // 0x14c9bb0: StoreField: r0->field_7b = r2
    //     0x14c9bb0: stur            w2, [x0, #0x7b]
    // 0x14c9bb4: b               #0x14c9d48
    // 0x14c9bb8: ldur            x2, [fp, #-0x20]
    // 0x14c9bbc: ldur            x1, [fp, #-8]
    // 0x14c9bc0: r0 = controller()
    //     0x14c9bc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9bc4: LoadField: r1 = r0->field_7b
    //     0x14c9bc4: ldur            w1, [x0, #0x7b]
    // 0x14c9bc8: DecompressPointer r1
    //     0x14c9bc8: add             x1, x1, HEAP, lsl #32
    // 0x14c9bcc: r0 = value()
    //     0x14c9bcc: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9bd0: r1 = LoadClassIdInstr(r0)
    //     0x14c9bd0: ldur            x1, [x0, #-1]
    //     0x14c9bd4: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9bd8: str             x0, [SP]
    // 0x14c9bdc: mov             x0, x1
    // 0x14c9be0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9be0: movz            x17, #0xc898
    //     0x14c9be4: add             lr, x0, x17
    //     0x14c9be8: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9bec: blr             lr
    // 0x14c9bf0: ldur            x2, [fp, #-0x20]
    // 0x14c9bf4: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x14c9bf4: ldur            w1, [x2, #0x17]
    // 0x14c9bf8: DecompressPointer r1
    //     0x14c9bf8: add             x1, x1, HEAP, lsl #32
    // 0x14c9bfc: cmp             w1, NULL
    // 0x14c9c00: b.ne            #0x14c9c0c
    // 0x14c9c04: r1 = Null
    //     0x14c9c04: mov             x1, NULL
    // 0x14c9c08: b               #0x14c9c18
    // 0x14c9c0c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x14c9c0c: ldur            w3, [x1, #0x17]
    // 0x14c9c10: DecompressPointer r3
    //     0x14c9c10: add             x3, x3, HEAP, lsl #32
    // 0x14c9c14: mov             x1, x3
    // 0x14c9c18: cmp             w1, NULL
    // 0x14c9c1c: b.ne            #0x14c9c28
    // 0x14c9c20: r1 = 0
    //     0x14c9c20: movz            x1, #0
    // 0x14c9c24: b               #0x14c9c38
    // 0x14c9c28: r3 = LoadInt32Instr(r1)
    //     0x14c9c28: sbfx            x3, x1, #1, #0x1f
    //     0x14c9c2c: tbz             w1, #0, #0x14c9c34
    //     0x14c9c30: ldur            x3, [x1, #7]
    // 0x14c9c34: mov             x1, x3
    // 0x14c9c38: r3 = LoadInt32Instr(r0)
    //     0x14c9c38: sbfx            x3, x0, #1, #0x1f
    //     0x14c9c3c: tbz             w0, #0, #0x14c9c44
    //     0x14c9c40: ldur            x3, [x0, #7]
    // 0x14c9c44: cmp             x3, x1
    // 0x14c9c48: b.ge            #0x14c9c98
    // 0x14c9c4c: ldur            x1, [fp, #-8]
    // 0x14c9c50: r0 = controller()
    //     0x14c9c50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9c54: LoadField: r1 = r0->field_7b
    //     0x14c9c54: ldur            w1, [x0, #0x7b]
    // 0x14c9c58: DecompressPointer r1
    //     0x14c9c58: add             x1, x1, HEAP, lsl #32
    // 0x14c9c5c: r0 = value()
    //     0x14c9c5c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9c60: r1 = LoadClassIdInstr(r0)
    //     0x14c9c60: ldur            x1, [x0, #-1]
    //     0x14c9c64: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9c68: str             x0, [SP]
    // 0x14c9c6c: mov             x0, x1
    // 0x14c9c70: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9c70: movz            x17, #0xc898
    //     0x14c9c74: add             lr, x0, x17
    //     0x14c9c78: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9c7c: blr             lr
    // 0x14c9c80: r1 = LoadInt32Instr(r0)
    //     0x14c9c80: sbfx            x1, x0, #1, #0x1f
    //     0x14c9c84: tbz             w0, #0, #0x14c9c8c
    //     0x14c9c88: ldur            x1, [x0, #7]
    // 0x14c9c8c: add             x0, x1, #1
    // 0x14c9c90: mov             x3, x0
    // 0x14c9c94: b               #0x14c9cdc
    // 0x14c9c98: ldur            x1, [fp, #-8]
    // 0x14c9c9c: r0 = controller()
    //     0x14c9c9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9ca0: LoadField: r1 = r0->field_7b
    //     0x14c9ca0: ldur            w1, [x0, #0x7b]
    // 0x14c9ca4: DecompressPointer r1
    //     0x14c9ca4: add             x1, x1, HEAP, lsl #32
    // 0x14c9ca8: r0 = value()
    //     0x14c9ca8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9cac: r1 = LoadClassIdInstr(r0)
    //     0x14c9cac: ldur            x1, [x0, #-1]
    //     0x14c9cb0: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9cb4: str             x0, [SP]
    // 0x14c9cb8: mov             x0, x1
    // 0x14c9cbc: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9cbc: movz            x17, #0xc898
    //     0x14c9cc0: add             lr, x0, x17
    //     0x14c9cc4: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9cc8: blr             lr
    // 0x14c9ccc: r1 = LoadInt32Instr(r0)
    //     0x14c9ccc: sbfx            x1, x0, #1, #0x1f
    //     0x14c9cd0: tbz             w0, #0, #0x14c9cd8
    //     0x14c9cd4: ldur            x1, [x0, #7]
    // 0x14c9cd8: mov             x3, x1
    // 0x14c9cdc: stur            x3, [fp, #-0x38]
    // 0x14c9ce0: r1 = Function '<anonymous closure>':.
    //     0x14c9ce0: add             x1, PP, #0x42, lsl #12  ; [pp+0x42668] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14c9ce4: ldr             x1, [x1, #0x668]
    // 0x14c9ce8: r2 = Null
    //     0x14c9ce8: mov             x2, NULL
    // 0x14c9cec: r0 = AllocateClosure()
    //     0x14c9cec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c9cf0: ldur            x2, [fp, #-0x20]
    // 0x14c9cf4: r1 = Function '<anonymous closure>':.
    //     0x14c9cf4: add             x1, PP, #0x42, lsl #12  ; [pp+0x42670] AnonymousClosure: (0x14c9e70), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14c9128)
    //     0x14c9cf8: ldr             x1, [x1, #0x670]
    // 0x14c9cfc: stur            x0, [fp, #-8]
    // 0x14c9d00: r0 = AllocateClosure()
    //     0x14c9d00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14c9d04: stur            x0, [fp, #-0x18]
    // 0x14c9d08: r0 = ListView()
    //     0x14c9d08: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14c9d0c: stur            x0, [fp, #-0x20]
    // 0x14c9d10: r16 = Instance_BouncingScrollPhysics
    //     0x14c9d10: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x14c9d14: ldr             x16, [x16, #0x890]
    // 0x14c9d18: r30 = true
    //     0x14c9d18: add             lr, NULL, #0x20  ; true
    // 0x14c9d1c: stp             lr, x16, [SP, #8]
    // 0x14c9d20: r16 = Instance_Axis
    //     0x14c9d20: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14c9d24: str             x16, [SP]
    // 0x14c9d28: mov             x1, x0
    // 0x14c9d2c: ldur            x2, [fp, #-0x18]
    // 0x14c9d30: ldur            x3, [fp, #-0x38]
    // 0x14c9d34: ldur            x5, [fp, #-8]
    // 0x14c9d38: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x6, shrinkWrap, 0x5, null]
    //     0x14c9d38: add             x4, PP, #0x33, lsl #12  ; [pp+0x33898] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x6, "shrinkWrap", 0x5, Null]
    //     0x14c9d3c: ldr             x4, [x4, #0x898]
    // 0x14c9d40: r0 = ListView.separated()
    //     0x14c9d40: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14c9d44: ldur            x0, [fp, #-0x20]
    // 0x14c9d48: ldur            x1, [fp, #-0x10]
    // 0x14c9d4c: stur            x0, [fp, #-8]
    // 0x14c9d50: r0 = SizedBox()
    //     0x14c9d50: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14c9d54: mov             x1, x0
    // 0x14c9d58: r0 = 60.000000
    //     0x14c9d58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14c9d5c: ldr             x0, [x0, #0x110]
    // 0x14c9d60: stur            x1, [fp, #-0x18]
    // 0x14c9d64: StoreField: r1->field_13 = r0
    //     0x14c9d64: stur            w0, [x1, #0x13]
    // 0x14c9d68: ldur            x0, [fp, #-8]
    // 0x14c9d6c: StoreField: r1->field_b = r0
    //     0x14c9d6c: stur            w0, [x1, #0xb]
    // 0x14c9d70: r0 = Padding()
    //     0x14c9d70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14c9d74: mov             x2, x0
    // 0x14c9d78: r0 = Instance_EdgeInsets
    //     0x14c9d78: add             x0, PP, #0x33, lsl #12  ; [pp+0x338a0] Obj!EdgeInsets@d575f1
    //     0x14c9d7c: ldr             x0, [x0, #0x8a0]
    // 0x14c9d80: stur            x2, [fp, #-8]
    // 0x14c9d84: StoreField: r2->field_f = r0
    //     0x14c9d84: stur            w0, [x2, #0xf]
    // 0x14c9d88: ldur            x0, [fp, #-0x18]
    // 0x14c9d8c: StoreField: r2->field_b = r0
    //     0x14c9d8c: stur            w0, [x2, #0xb]
    // 0x14c9d90: ldur            x0, [fp, #-0x10]
    // 0x14c9d94: LoadField: r1 = r0->field_b
    //     0x14c9d94: ldur            w1, [x0, #0xb]
    // 0x14c9d98: LoadField: r3 = r0->field_f
    //     0x14c9d98: ldur            w3, [x0, #0xf]
    // 0x14c9d9c: DecompressPointer r3
    //     0x14c9d9c: add             x3, x3, HEAP, lsl #32
    // 0x14c9da0: LoadField: r4 = r3->field_b
    //     0x14c9da0: ldur            w4, [x3, #0xb]
    // 0x14c9da4: r3 = LoadInt32Instr(r1)
    //     0x14c9da4: sbfx            x3, x1, #1, #0x1f
    // 0x14c9da8: stur            x3, [fp, #-0x38]
    // 0x14c9dac: r1 = LoadInt32Instr(r4)
    //     0x14c9dac: sbfx            x1, x4, #1, #0x1f
    // 0x14c9db0: cmp             x3, x1
    // 0x14c9db4: b.ne            #0x14c9dc0
    // 0x14c9db8: mov             x1, x0
    // 0x14c9dbc: r0 = _growToNextCapacity()
    //     0x14c9dbc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14c9dc0: ldur            x2, [fp, #-0x10]
    // 0x14c9dc4: ldur            x3, [fp, #-0x38]
    // 0x14c9dc8: add             x0, x3, #1
    // 0x14c9dcc: lsl             x1, x0, #1
    // 0x14c9dd0: StoreField: r2->field_b = r1
    //     0x14c9dd0: stur            w1, [x2, #0xb]
    // 0x14c9dd4: LoadField: r1 = r2->field_f
    //     0x14c9dd4: ldur            w1, [x2, #0xf]
    // 0x14c9dd8: DecompressPointer r1
    //     0x14c9dd8: add             x1, x1, HEAP, lsl #32
    // 0x14c9ddc: ldur            x0, [fp, #-8]
    // 0x14c9de0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14c9de0: add             x25, x1, x3, lsl #2
    //     0x14c9de4: add             x25, x25, #0xf
    //     0x14c9de8: str             w0, [x25]
    //     0x14c9dec: tbz             w0, #0, #0x14c9e08
    //     0x14c9df0: ldurb           w16, [x1, #-1]
    //     0x14c9df4: ldurb           w17, [x0, #-1]
    //     0x14c9df8: and             x16, x17, x16, lsr #2
    //     0x14c9dfc: tst             x16, HEAP, lsr #32
    //     0x14c9e00: b.eq            #0x14c9e08
    //     0x14c9e04: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14c9e08: r0 = Column()
    //     0x14c9e08: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14c9e0c: r1 = Instance_Axis
    //     0x14c9e0c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14c9e10: StoreField: r0->field_f = r1
    //     0x14c9e10: stur            w1, [x0, #0xf]
    // 0x14c9e14: r1 = Instance_MainAxisAlignment
    //     0x14c9e14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14c9e18: ldr             x1, [x1, #0xa08]
    // 0x14c9e1c: StoreField: r0->field_13 = r1
    //     0x14c9e1c: stur            w1, [x0, #0x13]
    // 0x14c9e20: r1 = Instance_MainAxisSize
    //     0x14c9e20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14c9e24: ldr             x1, [x1, #0xa10]
    // 0x14c9e28: ArrayStore: r0[0] = r1  ; List_4
    //     0x14c9e28: stur            w1, [x0, #0x17]
    // 0x14c9e2c: r1 = Instance_CrossAxisAlignment
    //     0x14c9e2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14c9e30: ldr             x1, [x1, #0x890]
    // 0x14c9e34: StoreField: r0->field_1b = r1
    //     0x14c9e34: stur            w1, [x0, #0x1b]
    // 0x14c9e38: r1 = Instance_VerticalDirection
    //     0x14c9e38: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14c9e3c: ldr             x1, [x1, #0xa20]
    // 0x14c9e40: StoreField: r0->field_23 = r1
    //     0x14c9e40: stur            w1, [x0, #0x23]
    // 0x14c9e44: r1 = Instance_Clip
    //     0x14c9e44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14c9e48: ldr             x1, [x1, #0x38]
    // 0x14c9e4c: StoreField: r0->field_2b = r1
    //     0x14c9e4c: stur            w1, [x0, #0x2b]
    // 0x14c9e50: StoreField: r0->field_2f = rZR
    //     0x14c9e50: stur            xzr, [x0, #0x2f]
    // 0x14c9e54: ldur            x1, [fp, #-0x10]
    // 0x14c9e58: StoreField: r0->field_b = r1
    //     0x14c9e58: stur            w1, [x0, #0xb]
    // 0x14c9e5c: LeaveFrame
    //     0x14c9e5c: mov             SP, fp
    //     0x14c9e60: ldp             fp, lr, [SP], #0x10
    // 0x14c9e64: ret
    //     0x14c9e64: ret             
    // 0x14c9e68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14c9e68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14c9e6c: b               #0x14c914c
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14c9e70, size: 0x518
    // 0x14c9e70: EnterFrame
    //     0x14c9e70: stp             fp, lr, [SP, #-0x10]!
    //     0x14c9e74: mov             fp, SP
    // 0x14c9e78: AllocStack(0x48)
    //     0x14c9e78: sub             SP, SP, #0x48
    // 0x14c9e7c: SetupParameters()
    //     0x14c9e7c: ldr             x0, [fp, #0x20]
    //     0x14c9e80: ldur            w1, [x0, #0x17]
    //     0x14c9e84: add             x1, x1, HEAP, lsl #32
    //     0x14c9e88: stur            x1, [fp, #-8]
    // 0x14c9e8c: CheckStackOverflow
    //     0x14c9e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14c9e90: cmp             SP, x16
    //     0x14c9e94: b.ls            #0x14ca380
    // 0x14c9e98: r1 = 1
    //     0x14c9e98: movz            x1, #0x1
    // 0x14c9e9c: r0 = AllocateContext()
    //     0x14c9e9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14c9ea0: mov             x2, x0
    // 0x14c9ea4: ldur            x0, [fp, #-8]
    // 0x14c9ea8: stur            x2, [fp, #-0x10]
    // 0x14c9eac: StoreField: r2->field_b = r0
    //     0x14c9eac: stur            w0, [x2, #0xb]
    // 0x14c9eb0: ldr             x3, [fp, #0x10]
    // 0x14c9eb4: StoreField: r2->field_f = r3
    //     0x14c9eb4: stur            w3, [x2, #0xf]
    // 0x14c9eb8: LoadField: r1 = r0->field_f
    //     0x14c9eb8: ldur            w1, [x0, #0xf]
    // 0x14c9ebc: DecompressPointer r1
    //     0x14c9ebc: add             x1, x1, HEAP, lsl #32
    // 0x14c9ec0: r0 = controller()
    //     0x14c9ec0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9ec4: LoadField: r1 = r0->field_7b
    //     0x14c9ec4: ldur            w1, [x0, #0x7b]
    // 0x14c9ec8: DecompressPointer r1
    //     0x14c9ec8: add             x1, x1, HEAP, lsl #32
    // 0x14c9ecc: r0 = value()
    //     0x14c9ecc: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9ed0: r1 = LoadClassIdInstr(r0)
    //     0x14c9ed0: ldur            x1, [x0, #-1]
    //     0x14c9ed4: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9ed8: str             x0, [SP]
    // 0x14c9edc: mov             x0, x1
    // 0x14c9ee0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9ee0: movz            x17, #0xc898
    //     0x14c9ee4: add             lr, x0, x17
    //     0x14c9ee8: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9eec: blr             lr
    // 0x14c9ef0: mov             x1, x0
    // 0x14c9ef4: ldr             x0, [fp, #0x10]
    // 0x14c9ef8: r2 = LoadInt32Instr(r0)
    //     0x14c9ef8: sbfx            x2, x0, #1, #0x1f
    //     0x14c9efc: tbz             w0, #0, #0x14c9f04
    //     0x14c9f00: ldur            x2, [x0, #7]
    // 0x14c9f04: r0 = LoadInt32Instr(r1)
    //     0x14c9f04: sbfx            x0, x1, #1, #0x1f
    //     0x14c9f08: tbz             w1, #0, #0x14c9f10
    //     0x14c9f0c: ldur            x0, [x1, #7]
    // 0x14c9f10: cmp             x2, x0
    // 0x14c9f14: b.ne            #0x14ca16c
    // 0x14c9f18: ldur            x0, [fp, #-8]
    // 0x14c9f1c: LoadField: r1 = r0->field_f
    //     0x14c9f1c: ldur            w1, [x0, #0xf]
    // 0x14c9f20: DecompressPointer r1
    //     0x14c9f20: add             x1, x1, HEAP, lsl #32
    // 0x14c9f24: r0 = controller()
    //     0x14c9f24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14c9f28: LoadField: r1 = r0->field_7b
    //     0x14c9f28: ldur            w1, [x0, #0x7b]
    // 0x14c9f2c: DecompressPointer r1
    //     0x14c9f2c: add             x1, x1, HEAP, lsl #32
    // 0x14c9f30: r0 = value()
    //     0x14c9f30: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14c9f34: r1 = LoadClassIdInstr(r0)
    //     0x14c9f34: ldur            x1, [x0, #-1]
    //     0x14c9f38: ubfx            x1, x1, #0xc, #0x14
    // 0x14c9f3c: str             x0, [SP]
    // 0x14c9f40: mov             x0, x1
    // 0x14c9f44: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14c9f44: movz            x17, #0xc898
    //     0x14c9f48: add             lr, x0, x17
    //     0x14c9f4c: ldr             lr, [x21, lr, lsl #3]
    //     0x14c9f50: blr             lr
    // 0x14c9f54: mov             x1, x0
    // 0x14c9f58: ldur            x0, [fp, #-8]
    // 0x14c9f5c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14c9f5c: ldur            w2, [x0, #0x17]
    // 0x14c9f60: DecompressPointer r2
    //     0x14c9f60: add             x2, x2, HEAP, lsl #32
    // 0x14c9f64: cmp             w2, NULL
    // 0x14c9f68: b.ne            #0x14c9f74
    // 0x14c9f6c: r2 = Null
    //     0x14c9f6c: mov             x2, NULL
    // 0x14c9f70: b               #0x14c9f80
    // 0x14c9f74: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14c9f74: ldur            w3, [x2, #0x17]
    // 0x14c9f78: DecompressPointer r3
    //     0x14c9f78: add             x3, x3, HEAP, lsl #32
    // 0x14c9f7c: mov             x2, x3
    // 0x14c9f80: cmp             w2, NULL
    // 0x14c9f84: b.ne            #0x14c9f90
    // 0x14c9f88: r2 = 0
    //     0x14c9f88: movz            x2, #0
    // 0x14c9f8c: b               #0x14c9fa0
    // 0x14c9f90: r3 = LoadInt32Instr(r2)
    //     0x14c9f90: sbfx            x3, x2, #1, #0x1f
    //     0x14c9f94: tbz             w2, #0, #0x14c9f9c
    //     0x14c9f98: ldur            x3, [x2, #7]
    // 0x14c9f9c: mov             x2, x3
    // 0x14c9fa0: r3 = LoadInt32Instr(r1)
    //     0x14c9fa0: sbfx            x3, x1, #1, #0x1f
    //     0x14c9fa4: tbz             w1, #0, #0x14c9fac
    //     0x14c9fa8: ldur            x3, [x1, #7]
    // 0x14c9fac: cmp             x3, x2
    // 0x14c9fb0: b.ge            #0x14ca164
    // 0x14c9fb4: r0 = Radius()
    //     0x14c9fb4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14c9fb8: d0 = 12.000000
    //     0x14c9fb8: fmov            d0, #12.00000000
    // 0x14c9fbc: stur            x0, [fp, #-0x18]
    // 0x14c9fc0: StoreField: r0->field_7 = d0
    //     0x14c9fc0: stur            d0, [x0, #7]
    // 0x14c9fc4: StoreField: r0->field_f = d0
    //     0x14c9fc4: stur            d0, [x0, #0xf]
    // 0x14c9fc8: r0 = BorderRadius()
    //     0x14c9fc8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14c9fcc: mov             x2, x0
    // 0x14c9fd0: ldur            x0, [fp, #-0x18]
    // 0x14c9fd4: stur            x2, [fp, #-0x20]
    // 0x14c9fd8: StoreField: r2->field_7 = r0
    //     0x14c9fd8: stur            w0, [x2, #7]
    // 0x14c9fdc: StoreField: r2->field_b = r0
    //     0x14c9fdc: stur            w0, [x2, #0xb]
    // 0x14c9fe0: StoreField: r2->field_f = r0
    //     0x14c9fe0: stur            w0, [x2, #0xf]
    // 0x14c9fe4: StoreField: r2->field_13 = r0
    //     0x14c9fe4: stur            w0, [x2, #0x13]
    // 0x14c9fe8: r1 = Instance_Color
    //     0x14c9fe8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14c9fec: d0 = 0.100000
    //     0x14c9fec: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14c9ff0: r0 = withOpacity()
    //     0x14c9ff0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14c9ff4: r16 = 1.000000
    //     0x14c9ff4: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14c9ff8: str             x16, [SP]
    // 0x14c9ffc: mov             x2, x0
    // 0x14ca000: r1 = Null
    //     0x14ca000: mov             x1, NULL
    // 0x14ca004: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14ca004: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14ca008: ldr             x4, [x4, #0x108]
    // 0x14ca00c: r0 = Border.all()
    //     0x14ca00c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14ca010: stur            x0, [fp, #-0x18]
    // 0x14ca014: r0 = BoxDecoration()
    //     0x14ca014: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14ca018: mov             x2, x0
    // 0x14ca01c: ldur            x0, [fp, #-0x18]
    // 0x14ca020: stur            x2, [fp, #-0x28]
    // 0x14ca024: StoreField: r2->field_f = r0
    //     0x14ca024: stur            w0, [x2, #0xf]
    // 0x14ca028: ldur            x0, [fp, #-0x20]
    // 0x14ca02c: StoreField: r2->field_13 = r0
    //     0x14ca02c: stur            w0, [x2, #0x13]
    // 0x14ca030: r0 = Instance_BoxShape
    //     0x14ca030: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14ca034: ldr             x0, [x0, #0x80]
    // 0x14ca038: StoreField: r2->field_23 = r0
    //     0x14ca038: stur            w0, [x2, #0x23]
    // 0x14ca03c: ldr             x1, [fp, #0x18]
    // 0x14ca040: r0 = of()
    //     0x14ca040: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ca044: LoadField: r1 = r0->field_5b
    //     0x14ca044: ldur            w1, [x0, #0x5b]
    // 0x14ca048: DecompressPointer r1
    //     0x14ca048: add             x1, x1, HEAP, lsl #32
    // 0x14ca04c: stur            x1, [fp, #-0x18]
    // 0x14ca050: r0 = ColorFilter()
    //     0x14ca050: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14ca054: mov             x1, x0
    // 0x14ca058: ldur            x0, [fp, #-0x18]
    // 0x14ca05c: stur            x1, [fp, #-0x20]
    // 0x14ca060: StoreField: r1->field_7 = r0
    //     0x14ca060: stur            w0, [x1, #7]
    // 0x14ca064: r0 = Instance_BlendMode
    //     0x14ca064: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14ca068: ldr             x0, [x0, #0xb30]
    // 0x14ca06c: StoreField: r1->field_b = r0
    //     0x14ca06c: stur            w0, [x1, #0xb]
    // 0x14ca070: r0 = 1
    //     0x14ca070: movz            x0, #0x1
    // 0x14ca074: StoreField: r1->field_13 = r0
    //     0x14ca074: stur            x0, [x1, #0x13]
    // 0x14ca078: r0 = SvgPicture()
    //     0x14ca078: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14ca07c: stur            x0, [fp, #-0x18]
    // 0x14ca080: r16 = Instance_BoxFit
    //     0x14ca080: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14ca084: ldr             x16, [x16, #0x118]
    // 0x14ca088: ldur            lr, [fp, #-0x20]
    // 0x14ca08c: stp             lr, x16, [SP]
    // 0x14ca090: mov             x1, x0
    // 0x14ca094: r2 = "assets/images/image_bg.svg"
    //     0x14ca094: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14ca098: ldr             x2, [x2, #0x868]
    // 0x14ca09c: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14ca09c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14ca0a0: ldr             x4, [x4, #0x820]
    // 0x14ca0a4: r0 = SvgPicture.asset()
    //     0x14ca0a4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14ca0a8: r0 = Center()
    //     0x14ca0a8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14ca0ac: mov             x1, x0
    // 0x14ca0b0: r0 = Instance_Alignment
    //     0x14ca0b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14ca0b4: ldr             x0, [x0, #0xb10]
    // 0x14ca0b8: stur            x1, [fp, #-0x20]
    // 0x14ca0bc: StoreField: r1->field_f = r0
    //     0x14ca0bc: stur            w0, [x1, #0xf]
    // 0x14ca0c0: ldur            x0, [fp, #-0x18]
    // 0x14ca0c4: StoreField: r1->field_b = r0
    //     0x14ca0c4: stur            w0, [x1, #0xb]
    // 0x14ca0c8: r0 = Container()
    //     0x14ca0c8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ca0cc: stur            x0, [fp, #-0x18]
    // 0x14ca0d0: r16 = 60.000000
    //     0x14ca0d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14ca0d4: ldr             x16, [x16, #0x110]
    // 0x14ca0d8: r30 = 60.000000
    //     0x14ca0d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14ca0dc: ldr             lr, [lr, #0x110]
    // 0x14ca0e0: stp             lr, x16, [SP, #0x10]
    // 0x14ca0e4: ldur            x16, [fp, #-0x28]
    // 0x14ca0e8: ldur            lr, [fp, #-0x20]
    // 0x14ca0ec: stp             lr, x16, [SP]
    // 0x14ca0f0: mov             x1, x0
    // 0x14ca0f4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x14ca0f4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x14ca0f8: ldr             x4, [x4, #0x8c0]
    // 0x14ca0fc: r0 = Container()
    //     0x14ca0fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ca100: r0 = InkWell()
    //     0x14ca100: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14ca104: mov             x3, x0
    // 0x14ca108: ldur            x0, [fp, #-0x18]
    // 0x14ca10c: stur            x3, [fp, #-0x20]
    // 0x14ca110: StoreField: r3->field_b = r0
    //     0x14ca110: stur            w0, [x3, #0xb]
    // 0x14ca114: ldur            x2, [fp, #-0x10]
    // 0x14ca118: r1 = Function '<anonymous closure>':.
    //     0x14ca118: add             x1, PP, #0x42, lsl #12  ; [pp+0x42678] AnonymousClosure: (0x9a5e48), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14ca11c: ldr             x1, [x1, #0x678]
    // 0x14ca120: r0 = AllocateClosure()
    //     0x14ca120: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ca124: mov             x1, x0
    // 0x14ca128: ldur            x0, [fp, #-0x20]
    // 0x14ca12c: StoreField: r0->field_f = r1
    //     0x14ca12c: stur            w1, [x0, #0xf]
    // 0x14ca130: r1 = true
    //     0x14ca130: add             x1, NULL, #0x20  ; true
    // 0x14ca134: StoreField: r0->field_43 = r1
    //     0x14ca134: stur            w1, [x0, #0x43]
    // 0x14ca138: r2 = Instance_BoxShape
    //     0x14ca138: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14ca13c: ldr             x2, [x2, #0x80]
    // 0x14ca140: StoreField: r0->field_47 = r2
    //     0x14ca140: stur            w2, [x0, #0x47]
    // 0x14ca144: StoreField: r0->field_6f = r1
    //     0x14ca144: stur            w1, [x0, #0x6f]
    // 0x14ca148: r2 = false
    //     0x14ca148: add             x2, NULL, #0x30  ; false
    // 0x14ca14c: StoreField: r0->field_73 = r2
    //     0x14ca14c: stur            w2, [x0, #0x73]
    // 0x14ca150: StoreField: r0->field_83 = r1
    //     0x14ca150: stur            w1, [x0, #0x83]
    // 0x14ca154: StoreField: r0->field_7b = r2
    //     0x14ca154: stur            w2, [x0, #0x7b]
    // 0x14ca158: LeaveFrame
    //     0x14ca158: mov             SP, fp
    //     0x14ca15c: ldp             fp, lr, [SP], #0x10
    // 0x14ca160: ret
    //     0x14ca160: ret             
    // 0x14ca164: d0 = 12.000000
    //     0x14ca164: fmov            d0, #12.00000000
    // 0x14ca168: b               #0x14ca174
    // 0x14ca16c: ldur            x0, [fp, #-8]
    // 0x14ca170: d0 = 12.000000
    //     0x14ca170: fmov            d0, #12.00000000
    // 0x14ca174: ldur            x2, [fp, #-0x10]
    // 0x14ca178: r0 = Radius()
    //     0x14ca178: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14ca17c: d0 = 12.000000
    //     0x14ca17c: fmov            d0, #12.00000000
    // 0x14ca180: stur            x0, [fp, #-0x18]
    // 0x14ca184: StoreField: r0->field_7 = d0
    //     0x14ca184: stur            d0, [x0, #7]
    // 0x14ca188: StoreField: r0->field_f = d0
    //     0x14ca188: stur            d0, [x0, #0xf]
    // 0x14ca18c: r0 = BorderRadius()
    //     0x14ca18c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14ca190: mov             x2, x0
    // 0x14ca194: ldur            x0, [fp, #-0x18]
    // 0x14ca198: stur            x2, [fp, #-0x20]
    // 0x14ca19c: StoreField: r2->field_7 = r0
    //     0x14ca19c: stur            w0, [x2, #7]
    // 0x14ca1a0: StoreField: r2->field_b = r0
    //     0x14ca1a0: stur            w0, [x2, #0xb]
    // 0x14ca1a4: StoreField: r2->field_f = r0
    //     0x14ca1a4: stur            w0, [x2, #0xf]
    // 0x14ca1a8: StoreField: r2->field_13 = r0
    //     0x14ca1a8: stur            w0, [x2, #0x13]
    // 0x14ca1ac: ldur            x0, [fp, #-8]
    // 0x14ca1b0: LoadField: r1 = r0->field_f
    //     0x14ca1b0: ldur            w1, [x0, #0xf]
    // 0x14ca1b4: DecompressPointer r1
    //     0x14ca1b4: add             x1, x1, HEAP, lsl #32
    // 0x14ca1b8: r0 = controller()
    //     0x14ca1b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca1bc: LoadField: r1 = r0->field_7b
    //     0x14ca1bc: ldur            w1, [x0, #0x7b]
    // 0x14ca1c0: DecompressPointer r1
    //     0x14ca1c0: add             x1, x1, HEAP, lsl #32
    // 0x14ca1c4: ldur            x2, [fp, #-0x10]
    // 0x14ca1c8: LoadField: r0 = r2->field_f
    //     0x14ca1c8: ldur            w0, [x2, #0xf]
    // 0x14ca1cc: DecompressPointer r0
    //     0x14ca1cc: add             x0, x0, HEAP, lsl #32
    // 0x14ca1d0: stur            x0, [fp, #-8]
    // 0x14ca1d4: r0 = value()
    //     0x14ca1d4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14ca1d8: r1 = LoadClassIdInstr(r0)
    //     0x14ca1d8: ldur            x1, [x0, #-1]
    //     0x14ca1dc: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca1e0: ldur            x16, [fp, #-8]
    // 0x14ca1e4: stp             x16, x0, [SP]
    // 0x14ca1e8: mov             x0, x1
    // 0x14ca1ec: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14ca1ec: sub             lr, x0, #0xb7
    //     0x14ca1f0: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca1f4: blr             lr
    // 0x14ca1f8: stur            x0, [fp, #-8]
    // 0x14ca1fc: r0 = Image()
    //     0x14ca1fc: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x14ca200: mov             x1, x0
    // 0x14ca204: ldur            x2, [fp, #-8]
    // 0x14ca208: d0 = 60.000000
    //     0x14ca208: ldr             d0, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14ca20c: d1 = 60.000000
    //     0x14ca20c: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14ca210: stur            x0, [fp, #-8]
    // 0x14ca214: r0 = Image.memory()
    //     0x14ca214: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x14ca218: r0 = ClipRRect()
    //     0x14ca218: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14ca21c: mov             x2, x0
    // 0x14ca220: ldur            x0, [fp, #-0x20]
    // 0x14ca224: stur            x2, [fp, #-0x18]
    // 0x14ca228: StoreField: r2->field_f = r0
    //     0x14ca228: stur            w0, [x2, #0xf]
    // 0x14ca22c: r0 = Instance_Clip
    //     0x14ca22c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14ca230: ldr             x0, [x0, #0x138]
    // 0x14ca234: ArrayStore: r2[0] = r0  ; List_4
    //     0x14ca234: stur            w0, [x2, #0x17]
    // 0x14ca238: ldur            x0, [fp, #-8]
    // 0x14ca23c: StoreField: r2->field_b = r0
    //     0x14ca23c: stur            w0, [x2, #0xb]
    // 0x14ca240: ldr             x1, [fp, #0x18]
    // 0x14ca244: r0 = of()
    //     0x14ca244: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ca248: LoadField: r2 = r0->field_5b
    //     0x14ca248: ldur            w2, [x0, #0x5b]
    // 0x14ca24c: DecompressPointer r2
    //     0x14ca24c: add             x2, x2, HEAP, lsl #32
    // 0x14ca250: r16 = 1.000000
    //     0x14ca250: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14ca254: str             x16, [SP]
    // 0x14ca258: r1 = Null
    //     0x14ca258: mov             x1, NULL
    // 0x14ca25c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14ca25c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14ca260: ldr             x4, [x4, #0x108]
    // 0x14ca264: r0 = Border.all()
    //     0x14ca264: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14ca268: stur            x0, [fp, #-8]
    // 0x14ca26c: r0 = BoxDecoration()
    //     0x14ca26c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14ca270: mov             x1, x0
    // 0x14ca274: ldur            x0, [fp, #-8]
    // 0x14ca278: stur            x1, [fp, #-0x20]
    // 0x14ca27c: StoreField: r1->field_f = r0
    //     0x14ca27c: stur            w0, [x1, #0xf]
    // 0x14ca280: r0 = Instance_BoxShape
    //     0x14ca280: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14ca284: ldr             x0, [x0, #0x970]
    // 0x14ca288: StoreField: r1->field_23 = r0
    //     0x14ca288: stur            w0, [x1, #0x23]
    // 0x14ca28c: r0 = Container()
    //     0x14ca28c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ca290: stur            x0, [fp, #-8]
    // 0x14ca294: ldur            x16, [fp, #-0x20]
    // 0x14ca298: r30 = Instance_Icon
    //     0x14ca298: add             lr, PP, #0x33, lsl #12  ; [pp+0x338d0] Obj!Icon@d65db1
    //     0x14ca29c: ldr             lr, [lr, #0x8d0]
    // 0x14ca2a0: stp             lr, x16, [SP]
    // 0x14ca2a4: mov             x1, x0
    // 0x14ca2a8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14ca2a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14ca2ac: ldr             x4, [x4, #0x88]
    // 0x14ca2b0: r0 = Container()
    //     0x14ca2b0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ca2b4: r0 = GestureDetector()
    //     0x14ca2b4: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14ca2b8: ldur            x2, [fp, #-0x10]
    // 0x14ca2bc: r1 = Function '<anonymous closure>':.
    //     0x14ca2bc: add             x1, PP, #0x42, lsl #12  ; [pp+0x42680] AnonymousClosure: (0x14ca388), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14c9128)
    //     0x14ca2c0: ldr             x1, [x1, #0x680]
    // 0x14ca2c4: stur            x0, [fp, #-0x10]
    // 0x14ca2c8: r0 = AllocateClosure()
    //     0x14ca2c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ca2cc: ldur            x16, [fp, #-8]
    // 0x14ca2d0: stp             x16, x0, [SP]
    // 0x14ca2d4: ldur            x1, [fp, #-0x10]
    // 0x14ca2d8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x14ca2d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x14ca2dc: ldr             x4, [x4, #0xaf0]
    // 0x14ca2e0: r0 = GestureDetector()
    //     0x14ca2e0: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14ca2e4: r1 = <StackParentData>
    //     0x14ca2e4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14ca2e8: ldr             x1, [x1, #0x8e0]
    // 0x14ca2ec: r0 = Positioned()
    //     0x14ca2ec: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14ca2f0: mov             x3, x0
    // 0x14ca2f4: ldur            x0, [fp, #-0x10]
    // 0x14ca2f8: stur            x3, [fp, #-8]
    // 0x14ca2fc: StoreField: r3->field_b = r0
    //     0x14ca2fc: stur            w0, [x3, #0xb]
    // 0x14ca300: r1 = Null
    //     0x14ca300: mov             x1, NULL
    // 0x14ca304: r2 = 4
    //     0x14ca304: movz            x2, #0x4
    // 0x14ca308: r0 = AllocateArray()
    //     0x14ca308: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14ca30c: mov             x2, x0
    // 0x14ca310: ldur            x0, [fp, #-0x18]
    // 0x14ca314: stur            x2, [fp, #-0x10]
    // 0x14ca318: StoreField: r2->field_f = r0
    //     0x14ca318: stur            w0, [x2, #0xf]
    // 0x14ca31c: ldur            x0, [fp, #-8]
    // 0x14ca320: StoreField: r2->field_13 = r0
    //     0x14ca320: stur            w0, [x2, #0x13]
    // 0x14ca324: r1 = <Widget>
    //     0x14ca324: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14ca328: r0 = AllocateGrowableArray()
    //     0x14ca328: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14ca32c: mov             x1, x0
    // 0x14ca330: ldur            x0, [fp, #-0x10]
    // 0x14ca334: stur            x1, [fp, #-8]
    // 0x14ca338: StoreField: r1->field_f = r0
    //     0x14ca338: stur            w0, [x1, #0xf]
    // 0x14ca33c: r0 = 4
    //     0x14ca33c: movz            x0, #0x4
    // 0x14ca340: StoreField: r1->field_b = r0
    //     0x14ca340: stur            w0, [x1, #0xb]
    // 0x14ca344: r0 = Stack()
    //     0x14ca344: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14ca348: r1 = Instance_Alignment
    //     0x14ca348: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14ca34c: ldr             x1, [x1, #0x950]
    // 0x14ca350: StoreField: r0->field_f = r1
    //     0x14ca350: stur            w1, [x0, #0xf]
    // 0x14ca354: r1 = Instance_StackFit
    //     0x14ca354: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14ca358: ldr             x1, [x1, #0xfa8]
    // 0x14ca35c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14ca35c: stur            w1, [x0, #0x17]
    // 0x14ca360: r1 = Instance_Clip
    //     0x14ca360: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14ca364: ldr             x1, [x1, #0x7e0]
    // 0x14ca368: StoreField: r0->field_1b = r1
    //     0x14ca368: stur            w1, [x0, #0x1b]
    // 0x14ca36c: ldur            x1, [fp, #-8]
    // 0x14ca370: StoreField: r0->field_b = r1
    //     0x14ca370: stur            w1, [x0, #0xb]
    // 0x14ca374: LeaveFrame
    //     0x14ca374: mov             SP, fp
    //     0x14ca378: ldp             fp, lr, [SP], #0x10
    // 0x14ca37c: ret
    //     0x14ca37c: ret             
    // 0x14ca380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ca380: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ca384: b               #0x14c9e98
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14ca388, size: 0x31c
    // 0x14ca388: EnterFrame
    //     0x14ca388: stp             fp, lr, [SP, #-0x10]!
    //     0x14ca38c: mov             fp, SP
    // 0x14ca390: AllocStack(0x38)
    //     0x14ca390: sub             SP, SP, #0x38
    // 0x14ca394: SetupParameters(ReturnOrderWithProofView this /* r1 */)
    //     0x14ca394: stur            NULL, [fp, #-8]
    //     0x14ca398: movz            x0, #0
    //     0x14ca39c: add             x1, fp, w0, sxtw #2
    //     0x14ca3a0: ldr             x1, [x1, #0x10]
    //     0x14ca3a4: ldur            w2, [x1, #0x17]
    //     0x14ca3a8: add             x2, x2, HEAP, lsl #32
    //     0x14ca3ac: stur            x2, [fp, #-0x10]
    // 0x14ca3b0: CheckStackOverflow
    //     0x14ca3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ca3b4: cmp             SP, x16
    //     0x14ca3b8: b.ls            #0x14ca698
    // 0x14ca3bc: InitAsync() -> Future<void?>
    //     0x14ca3bc: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14ca3c0: bl              #0x6326e0  ; InitAsyncStub
    // 0x14ca3c4: ldur            x0, [fp, #-0x10]
    // 0x14ca3c8: LoadField: r2 = r0->field_b
    //     0x14ca3c8: ldur            w2, [x0, #0xb]
    // 0x14ca3cc: DecompressPointer r2
    //     0x14ca3cc: add             x2, x2, HEAP, lsl #32
    // 0x14ca3d0: stur            x2, [fp, #-0x18]
    // 0x14ca3d4: LoadField: r1 = r2->field_f
    //     0x14ca3d4: ldur            w1, [x2, #0xf]
    // 0x14ca3d8: DecompressPointer r1
    //     0x14ca3d8: add             x1, x1, HEAP, lsl #32
    // 0x14ca3dc: r0 = controller()
    //     0x14ca3dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca3e0: LoadField: r1 = r0->field_77
    //     0x14ca3e0: ldur            w1, [x0, #0x77]
    // 0x14ca3e4: DecompressPointer r1
    //     0x14ca3e4: add             x1, x1, HEAP, lsl #32
    // 0x14ca3e8: ldur            x0, [fp, #-0x10]
    // 0x14ca3ec: LoadField: r2 = r0->field_f
    //     0x14ca3ec: ldur            w2, [x0, #0xf]
    // 0x14ca3f0: DecompressPointer r2
    //     0x14ca3f0: add             x2, x2, HEAP, lsl #32
    // 0x14ca3f4: stur            x2, [fp, #-0x20]
    // 0x14ca3f8: r0 = value()
    //     0x14ca3f8: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14ca3fc: r1 = LoadClassIdInstr(r0)
    //     0x14ca3fc: ldur            x1, [x0, #-1]
    //     0x14ca400: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca404: ldur            x16, [fp, #-0x20]
    // 0x14ca408: stp             x16, x0, [SP]
    // 0x14ca40c: mov             x0, x1
    // 0x14ca410: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14ca410: sub             lr, x0, #0xb7
    //     0x14ca414: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca418: blr             lr
    // 0x14ca41c: r1 = LoadClassIdInstr(r0)
    //     0x14ca41c: ldur            x1, [x0, #-1]
    //     0x14ca420: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca424: mov             x16, x0
    // 0x14ca428: mov             x0, x1
    // 0x14ca42c: mov             x1, x16
    // 0x14ca430: r0 = GDT[cid_x0 + -0xe96]()
    //     0x14ca430: sub             lr, x0, #0xe96
    //     0x14ca434: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca438: blr             lr
    // 0x14ca43c: mov             x1, x0
    // 0x14ca440: r0 = lookupMimeType()
    //     0x14ca440: bl              #0x8ab24c  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0x14ca444: cmp             w0, NULL
    // 0x14ca448: b.ne            #0x14ca454
    // 0x14ca44c: r2 = Null
    //     0x14ca44c: mov             x2, NULL
    // 0x14ca450: b               #0x14ca47c
    // 0x14ca454: r1 = LoadClassIdInstr(r0)
    //     0x14ca454: ldur            x1, [x0, #-1]
    //     0x14ca458: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca45c: mov             x16, x0
    // 0x14ca460: mov             x0, x1
    // 0x14ca464: mov             x1, x16
    // 0x14ca468: r2 = "/"
    //     0x14ca468: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x14ca46c: r0 = GDT[cid_x0 + -0xffc]()
    //     0x14ca46c: sub             lr, x0, #0xffc
    //     0x14ca470: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca474: blr             lr
    // 0x14ca478: mov             x2, x0
    // 0x14ca47c: cmp             w2, NULL
    // 0x14ca480: b.ne            #0x14ca48c
    // 0x14ca484: r0 = Null
    //     0x14ca484: mov             x0, NULL
    // 0x14ca488: b               #0x14ca4b8
    // 0x14ca48c: LoadField: r0 = r2->field_b
    //     0x14ca48c: ldur            w0, [x2, #0xb]
    // 0x14ca490: r1 = LoadInt32Instr(r0)
    //     0x14ca490: sbfx            x1, x0, #1, #0x1f
    // 0x14ca494: mov             x0, x1
    // 0x14ca498: r1 = 0
    //     0x14ca498: movz            x1, #0
    // 0x14ca49c: cmp             x1, x0
    // 0x14ca4a0: b.hs            #0x14ca6a0
    // 0x14ca4a4: LoadField: r0 = r2->field_f
    //     0x14ca4a4: ldur            w0, [x2, #0xf]
    // 0x14ca4a8: DecompressPointer r0
    //     0x14ca4a8: add             x0, x0, HEAP, lsl #32
    // 0x14ca4ac: LoadField: r1 = r0->field_f
    //     0x14ca4ac: ldur            w1, [x0, #0xf]
    // 0x14ca4b0: DecompressPointer r1
    //     0x14ca4b0: add             x1, x1, HEAP, lsl #32
    // 0x14ca4b4: mov             x0, x1
    // 0x14ca4b8: r1 = LoadClassIdInstr(r0)
    //     0x14ca4b8: ldur            x1, [x0, #-1]
    //     0x14ca4bc: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca4c0: r16 = "video"
    //     0x14ca4c0: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x14ca4c4: ldr             x16, [x16, #0xb50]
    // 0x14ca4c8: stp             x16, x0, [SP]
    // 0x14ca4cc: mov             x0, x1
    // 0x14ca4d0: mov             lr, x0
    // 0x14ca4d4: ldr             lr, [x21, lr, lsl #3]
    // 0x14ca4d8: blr             lr
    // 0x14ca4dc: tbnz            w0, #4, #0x14ca500
    // 0x14ca4e0: ldur            x0, [fp, #-0x18]
    // 0x14ca4e4: LoadField: r1 = r0->field_f
    //     0x14ca4e4: ldur            w1, [x0, #0xf]
    // 0x14ca4e8: DecompressPointer r1
    //     0x14ca4e8: add             x1, x1, HEAP, lsl #32
    // 0x14ca4ec: r0 = controller()
    //     0x14ca4ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca4f0: LoadField: r1 = r0->field_87
    //     0x14ca4f0: ldur            x1, [x0, #0x87]
    // 0x14ca4f4: sub             x2, x1, #1
    // 0x14ca4f8: StoreField: r0->field_87 = r2
    //     0x14ca4f8: stur            x2, [x0, #0x87]
    // 0x14ca4fc: b               #0x14ca51c
    // 0x14ca500: ldur            x0, [fp, #-0x18]
    // 0x14ca504: LoadField: r1 = r0->field_f
    //     0x14ca504: ldur            w1, [x0, #0xf]
    // 0x14ca508: DecompressPointer r1
    //     0x14ca508: add             x1, x1, HEAP, lsl #32
    // 0x14ca50c: r0 = controller()
    //     0x14ca50c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca510: LoadField: r1 = r0->field_7f
    //     0x14ca510: ldur            x1, [x0, #0x7f]
    // 0x14ca514: sub             x2, x1, #1
    // 0x14ca518: StoreField: r0->field_7f = r2
    //     0x14ca518: stur            x2, [x0, #0x7f]
    // 0x14ca51c: ldur            x2, [fp, #-0x10]
    // 0x14ca520: ldur            x0, [fp, #-0x18]
    // 0x14ca524: LoadField: r1 = r0->field_f
    //     0x14ca524: ldur            w1, [x0, #0xf]
    // 0x14ca528: DecompressPointer r1
    //     0x14ca528: add             x1, x1, HEAP, lsl #32
    // 0x14ca52c: r0 = controller()
    //     0x14ca52c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca530: mov             x2, x0
    // 0x14ca534: ldur            x0, [fp, #-0x18]
    // 0x14ca538: stur            x2, [fp, #-0x20]
    // 0x14ca53c: LoadField: r1 = r0->field_f
    //     0x14ca53c: ldur            w1, [x0, #0xf]
    // 0x14ca540: DecompressPointer r1
    //     0x14ca540: add             x1, x1, HEAP, lsl #32
    // 0x14ca544: r0 = controller()
    //     0x14ca544: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca548: LoadField: r1 = r0->field_77
    //     0x14ca548: ldur            w1, [x0, #0x77]
    // 0x14ca54c: DecompressPointer r1
    //     0x14ca54c: add             x1, x1, HEAP, lsl #32
    // 0x14ca550: ldur            x0, [fp, #-0x10]
    // 0x14ca554: LoadField: r2 = r0->field_f
    //     0x14ca554: ldur            w2, [x0, #0xf]
    // 0x14ca558: DecompressPointer r2
    //     0x14ca558: add             x2, x2, HEAP, lsl #32
    // 0x14ca55c: stur            x2, [fp, #-0x28]
    // 0x14ca560: r0 = value()
    //     0x14ca560: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14ca564: r1 = LoadClassIdInstr(r0)
    //     0x14ca564: ldur            x1, [x0, #-1]
    //     0x14ca568: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca56c: ldur            x16, [fp, #-0x28]
    // 0x14ca570: stp             x16, x0, [SP]
    // 0x14ca574: mov             x0, x1
    // 0x14ca578: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14ca578: sub             lr, x0, #0xb7
    //     0x14ca57c: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca580: blr             lr
    // 0x14ca584: r1 = LoadClassIdInstr(r0)
    //     0x14ca584: ldur            x1, [x0, #-1]
    //     0x14ca588: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca58c: mov             x16, x0
    // 0x14ca590: mov             x0, x1
    // 0x14ca594: mov             x1, x16
    // 0x14ca598: r0 = GDT[cid_x0 + -0xe96]()
    //     0x14ca598: sub             lr, x0, #0xe96
    //     0x14ca59c: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca5a0: blr             lr
    // 0x14ca5a4: ldur            x1, [fp, #-0x20]
    // 0x14ca5a8: mov             x2, x0
    // 0x14ca5ac: r0 = getFileSize()
    //     0x14ca5ac: bl              #0x9a577c  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::getFileSize
    // 0x14ca5b0: mov             x1, x0
    // 0x14ca5b4: stur            x1, [fp, #-0x20]
    // 0x14ca5b8: r0 = Await()
    //     0x14ca5b8: bl              #0x63248c  ; AwaitStub
    // 0x14ca5bc: mov             x3, x0
    // 0x14ca5c0: r2 = Null
    //     0x14ca5c0: mov             x2, NULL
    // 0x14ca5c4: r1 = Null
    //     0x14ca5c4: mov             x1, NULL
    // 0x14ca5c8: stur            x3, [fp, #-0x20]
    // 0x14ca5cc: branchIfSmi(r0, 0x14ca5f4)
    //     0x14ca5cc: tbz             w0, #0, #0x14ca5f4
    // 0x14ca5d0: r4 = LoadClassIdInstr(r0)
    //     0x14ca5d0: ldur            x4, [x0, #-1]
    //     0x14ca5d4: ubfx            x4, x4, #0xc, #0x14
    // 0x14ca5d8: sub             x4, x4, #0x3c
    // 0x14ca5dc: cmp             x4, #1
    // 0x14ca5e0: b.ls            #0x14ca5f4
    // 0x14ca5e4: r8 = int
    //     0x14ca5e4: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14ca5e8: r3 = Null
    //     0x14ca5e8: add             x3, PP, #0x42, lsl #12  ; [pp+0x42688] Null
    //     0x14ca5ec: ldr             x3, [x3, #0x688]
    // 0x14ca5f0: r0 = int()
    //     0x14ca5f0: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14ca5f4: ldur            x0, [fp, #-0x18]
    // 0x14ca5f8: LoadField: r1 = r0->field_f
    //     0x14ca5f8: ldur            w1, [x0, #0xf]
    // 0x14ca5fc: DecompressPointer r1
    //     0x14ca5fc: add             x1, x1, HEAP, lsl #32
    // 0x14ca600: r0 = controller()
    //     0x14ca600: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca604: LoadField: r1 = r0->field_cf
    //     0x14ca604: ldur            x1, [x0, #0xcf]
    // 0x14ca608: ldur            x2, [fp, #-0x20]
    // 0x14ca60c: r3 = LoadInt32Instr(r2)
    //     0x14ca60c: sbfx            x3, x2, #1, #0x1f
    //     0x14ca610: tbz             w2, #0, #0x14ca618
    //     0x14ca614: ldur            x3, [x2, #7]
    // 0x14ca618: sub             x2, x1, x3
    // 0x14ca61c: StoreField: r0->field_cf = r2
    //     0x14ca61c: stur            x2, [x0, #0xcf]
    // 0x14ca620: ldur            x0, [fp, #-0x18]
    // 0x14ca624: LoadField: r1 = r0->field_f
    //     0x14ca624: ldur            w1, [x0, #0xf]
    // 0x14ca628: DecompressPointer r1
    //     0x14ca628: add             x1, x1, HEAP, lsl #32
    // 0x14ca62c: r0 = controller()
    //     0x14ca62c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca630: LoadField: r1 = r0->field_77
    //     0x14ca630: ldur            w1, [x0, #0x77]
    // 0x14ca634: DecompressPointer r1
    //     0x14ca634: add             x1, x1, HEAP, lsl #32
    // 0x14ca638: ldur            x0, [fp, #-0x10]
    // 0x14ca63c: LoadField: r2 = r0->field_f
    //     0x14ca63c: ldur            w2, [x0, #0xf]
    // 0x14ca640: DecompressPointer r2
    //     0x14ca640: add             x2, x2, HEAP, lsl #32
    // 0x14ca644: r3 = LoadInt32Instr(r2)
    //     0x14ca644: sbfx            x3, x2, #1, #0x1f
    //     0x14ca648: tbz             w2, #0, #0x14ca650
    //     0x14ca64c: ldur            x3, [x2, #7]
    // 0x14ca650: mov             x2, x3
    // 0x14ca654: r0 = removeAt()
    //     0x14ca654: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14ca658: ldur            x0, [fp, #-0x18]
    // 0x14ca65c: LoadField: r1 = r0->field_f
    //     0x14ca65c: ldur            w1, [x0, #0xf]
    // 0x14ca660: DecompressPointer r1
    //     0x14ca660: add             x1, x1, HEAP, lsl #32
    // 0x14ca664: r0 = controller()
    //     0x14ca664: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca668: LoadField: r1 = r0->field_7b
    //     0x14ca668: ldur            w1, [x0, #0x7b]
    // 0x14ca66c: DecompressPointer r1
    //     0x14ca66c: add             x1, x1, HEAP, lsl #32
    // 0x14ca670: ldur            x0, [fp, #-0x10]
    // 0x14ca674: LoadField: r2 = r0->field_f
    //     0x14ca674: ldur            w2, [x0, #0xf]
    // 0x14ca678: DecompressPointer r2
    //     0x14ca678: add             x2, x2, HEAP, lsl #32
    // 0x14ca67c: r0 = LoadInt32Instr(r2)
    //     0x14ca67c: sbfx            x0, x2, #1, #0x1f
    //     0x14ca680: tbz             w2, #0, #0x14ca688
    //     0x14ca684: ldur            x0, [x2, #7]
    // 0x14ca688: mov             x2, x0
    // 0x14ca68c: r0 = removeAt()
    //     0x14ca68c: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14ca690: r0 = Null
    //     0x14ca690: mov             x0, NULL
    // 0x14ca694: r0 = ReturnAsyncNotFuture()
    //     0x14ca694: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14ca698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ca698: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ca69c: b               #0x14ca3bc
    // 0x14ca6a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ca6a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14ca6a4, size: 0x518
    // 0x14ca6a4: EnterFrame
    //     0x14ca6a4: stp             fp, lr, [SP, #-0x10]!
    //     0x14ca6a8: mov             fp, SP
    // 0x14ca6ac: AllocStack(0x48)
    //     0x14ca6ac: sub             SP, SP, #0x48
    // 0x14ca6b0: SetupParameters()
    //     0x14ca6b0: ldr             x0, [fp, #0x20]
    //     0x14ca6b4: ldur            w1, [x0, #0x17]
    //     0x14ca6b8: add             x1, x1, HEAP, lsl #32
    //     0x14ca6bc: stur            x1, [fp, #-8]
    // 0x14ca6c0: CheckStackOverflow
    //     0x14ca6c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ca6c4: cmp             SP, x16
    //     0x14ca6c8: b.ls            #0x14cabb4
    // 0x14ca6cc: r1 = 1
    //     0x14ca6cc: movz            x1, #0x1
    // 0x14ca6d0: r0 = AllocateContext()
    //     0x14ca6d0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14ca6d4: mov             x2, x0
    // 0x14ca6d8: ldur            x0, [fp, #-8]
    // 0x14ca6dc: stur            x2, [fp, #-0x10]
    // 0x14ca6e0: StoreField: r2->field_b = r0
    //     0x14ca6e0: stur            w0, [x2, #0xb]
    // 0x14ca6e4: ldr             x3, [fp, #0x10]
    // 0x14ca6e8: StoreField: r2->field_f = r3
    //     0x14ca6e8: stur            w3, [x2, #0xf]
    // 0x14ca6ec: LoadField: r1 = r0->field_f
    //     0x14ca6ec: ldur            w1, [x0, #0xf]
    // 0x14ca6f0: DecompressPointer r1
    //     0x14ca6f0: add             x1, x1, HEAP, lsl #32
    // 0x14ca6f4: r0 = controller()
    //     0x14ca6f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca6f8: LoadField: r1 = r0->field_73
    //     0x14ca6f8: ldur            w1, [x0, #0x73]
    // 0x14ca6fc: DecompressPointer r1
    //     0x14ca6fc: add             x1, x1, HEAP, lsl #32
    // 0x14ca700: r0 = value()
    //     0x14ca700: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14ca704: r1 = LoadClassIdInstr(r0)
    //     0x14ca704: ldur            x1, [x0, #-1]
    //     0x14ca708: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca70c: str             x0, [SP]
    // 0x14ca710: mov             x0, x1
    // 0x14ca714: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14ca714: movz            x17, #0xc898
    //     0x14ca718: add             lr, x0, x17
    //     0x14ca71c: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca720: blr             lr
    // 0x14ca724: mov             x1, x0
    // 0x14ca728: ldr             x0, [fp, #0x10]
    // 0x14ca72c: r2 = LoadInt32Instr(r0)
    //     0x14ca72c: sbfx            x2, x0, #1, #0x1f
    //     0x14ca730: tbz             w0, #0, #0x14ca738
    //     0x14ca734: ldur            x2, [x0, #7]
    // 0x14ca738: r0 = LoadInt32Instr(r1)
    //     0x14ca738: sbfx            x0, x1, #1, #0x1f
    //     0x14ca73c: tbz             w1, #0, #0x14ca744
    //     0x14ca740: ldur            x0, [x1, #7]
    // 0x14ca744: cmp             x2, x0
    // 0x14ca748: b.ne            #0x14ca9a0
    // 0x14ca74c: ldur            x0, [fp, #-8]
    // 0x14ca750: LoadField: r1 = r0->field_f
    //     0x14ca750: ldur            w1, [x0, #0xf]
    // 0x14ca754: DecompressPointer r1
    //     0x14ca754: add             x1, x1, HEAP, lsl #32
    // 0x14ca758: r0 = controller()
    //     0x14ca758: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca75c: LoadField: r1 = r0->field_73
    //     0x14ca75c: ldur            w1, [x0, #0x73]
    // 0x14ca760: DecompressPointer r1
    //     0x14ca760: add             x1, x1, HEAP, lsl #32
    // 0x14ca764: r0 = value()
    //     0x14ca764: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14ca768: r1 = LoadClassIdInstr(r0)
    //     0x14ca768: ldur            x1, [x0, #-1]
    //     0x14ca76c: ubfx            x1, x1, #0xc, #0x14
    // 0x14ca770: str             x0, [SP]
    // 0x14ca774: mov             x0, x1
    // 0x14ca778: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14ca778: movz            x17, #0xc898
    //     0x14ca77c: add             lr, x0, x17
    //     0x14ca780: ldr             lr, [x21, lr, lsl #3]
    //     0x14ca784: blr             lr
    // 0x14ca788: mov             x1, x0
    // 0x14ca78c: ldur            x0, [fp, #-8]
    // 0x14ca790: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x14ca790: ldur            w2, [x0, #0x17]
    // 0x14ca794: DecompressPointer r2
    //     0x14ca794: add             x2, x2, HEAP, lsl #32
    // 0x14ca798: cmp             w2, NULL
    // 0x14ca79c: b.ne            #0x14ca7a8
    // 0x14ca7a0: r2 = Null
    //     0x14ca7a0: mov             x2, NULL
    // 0x14ca7a4: b               #0x14ca7b4
    // 0x14ca7a8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14ca7a8: ldur            w3, [x2, #0x17]
    // 0x14ca7ac: DecompressPointer r3
    //     0x14ca7ac: add             x3, x3, HEAP, lsl #32
    // 0x14ca7b0: mov             x2, x3
    // 0x14ca7b4: cmp             w2, NULL
    // 0x14ca7b8: b.ne            #0x14ca7c4
    // 0x14ca7bc: r2 = 0
    //     0x14ca7bc: movz            x2, #0
    // 0x14ca7c0: b               #0x14ca7d4
    // 0x14ca7c4: r3 = LoadInt32Instr(r2)
    //     0x14ca7c4: sbfx            x3, x2, #1, #0x1f
    //     0x14ca7c8: tbz             w2, #0, #0x14ca7d0
    //     0x14ca7cc: ldur            x3, [x2, #7]
    // 0x14ca7d0: mov             x2, x3
    // 0x14ca7d4: r3 = LoadInt32Instr(r1)
    //     0x14ca7d4: sbfx            x3, x1, #1, #0x1f
    //     0x14ca7d8: tbz             w1, #0, #0x14ca7e0
    //     0x14ca7dc: ldur            x3, [x1, #7]
    // 0x14ca7e0: cmp             x3, x2
    // 0x14ca7e4: b.ge            #0x14ca998
    // 0x14ca7e8: r0 = Radius()
    //     0x14ca7e8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14ca7ec: d0 = 12.000000
    //     0x14ca7ec: fmov            d0, #12.00000000
    // 0x14ca7f0: stur            x0, [fp, #-0x18]
    // 0x14ca7f4: StoreField: r0->field_7 = d0
    //     0x14ca7f4: stur            d0, [x0, #7]
    // 0x14ca7f8: StoreField: r0->field_f = d0
    //     0x14ca7f8: stur            d0, [x0, #0xf]
    // 0x14ca7fc: r0 = BorderRadius()
    //     0x14ca7fc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14ca800: mov             x2, x0
    // 0x14ca804: ldur            x0, [fp, #-0x18]
    // 0x14ca808: stur            x2, [fp, #-0x20]
    // 0x14ca80c: StoreField: r2->field_7 = r0
    //     0x14ca80c: stur            w0, [x2, #7]
    // 0x14ca810: StoreField: r2->field_b = r0
    //     0x14ca810: stur            w0, [x2, #0xb]
    // 0x14ca814: StoreField: r2->field_f = r0
    //     0x14ca814: stur            w0, [x2, #0xf]
    // 0x14ca818: StoreField: r2->field_13 = r0
    //     0x14ca818: stur            w0, [x2, #0x13]
    // 0x14ca81c: r1 = Instance_Color
    //     0x14ca81c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ca820: d0 = 0.100000
    //     0x14ca820: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14ca824: r0 = withOpacity()
    //     0x14ca824: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14ca828: r16 = 1.000000
    //     0x14ca828: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14ca82c: str             x16, [SP]
    // 0x14ca830: mov             x2, x0
    // 0x14ca834: r1 = Null
    //     0x14ca834: mov             x1, NULL
    // 0x14ca838: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14ca838: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14ca83c: ldr             x4, [x4, #0x108]
    // 0x14ca840: r0 = Border.all()
    //     0x14ca840: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14ca844: stur            x0, [fp, #-0x18]
    // 0x14ca848: r0 = BoxDecoration()
    //     0x14ca848: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14ca84c: mov             x2, x0
    // 0x14ca850: ldur            x0, [fp, #-0x18]
    // 0x14ca854: stur            x2, [fp, #-0x28]
    // 0x14ca858: StoreField: r2->field_f = r0
    //     0x14ca858: stur            w0, [x2, #0xf]
    // 0x14ca85c: ldur            x0, [fp, #-0x20]
    // 0x14ca860: StoreField: r2->field_13 = r0
    //     0x14ca860: stur            w0, [x2, #0x13]
    // 0x14ca864: r0 = Instance_BoxShape
    //     0x14ca864: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14ca868: ldr             x0, [x0, #0x80]
    // 0x14ca86c: StoreField: r2->field_23 = r0
    //     0x14ca86c: stur            w0, [x2, #0x23]
    // 0x14ca870: ldr             x1, [fp, #0x18]
    // 0x14ca874: r0 = of()
    //     0x14ca874: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14ca878: LoadField: r1 = r0->field_5b
    //     0x14ca878: ldur            w1, [x0, #0x5b]
    // 0x14ca87c: DecompressPointer r1
    //     0x14ca87c: add             x1, x1, HEAP, lsl #32
    // 0x14ca880: stur            x1, [fp, #-0x18]
    // 0x14ca884: r0 = ColorFilter()
    //     0x14ca884: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x14ca888: mov             x1, x0
    // 0x14ca88c: ldur            x0, [fp, #-0x18]
    // 0x14ca890: stur            x1, [fp, #-0x20]
    // 0x14ca894: StoreField: r1->field_7 = r0
    //     0x14ca894: stur            w0, [x1, #7]
    // 0x14ca898: r0 = Instance_BlendMode
    //     0x14ca898: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x14ca89c: ldr             x0, [x0, #0xb30]
    // 0x14ca8a0: StoreField: r1->field_b = r0
    //     0x14ca8a0: stur            w0, [x1, #0xb]
    // 0x14ca8a4: r0 = 1
    //     0x14ca8a4: movz            x0, #0x1
    // 0x14ca8a8: StoreField: r1->field_13 = r0
    //     0x14ca8a8: stur            x0, [x1, #0x13]
    // 0x14ca8ac: r0 = SvgPicture()
    //     0x14ca8ac: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14ca8b0: stur            x0, [fp, #-0x18]
    // 0x14ca8b4: r16 = Instance_BoxFit
    //     0x14ca8b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14ca8b8: ldr             x16, [x16, #0x118]
    // 0x14ca8bc: ldur            lr, [fp, #-0x20]
    // 0x14ca8c0: stp             lr, x16, [SP]
    // 0x14ca8c4: mov             x1, x0
    // 0x14ca8c8: r2 = "assets/images/image_bg.svg"
    //     0x14ca8c8: add             x2, PP, #0x33, lsl #12  ; [pp+0x33868] "assets/images/image_bg.svg"
    //     0x14ca8cc: ldr             x2, [x2, #0x868]
    // 0x14ca8d0: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x14ca8d0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x14ca8d4: ldr             x4, [x4, #0x820]
    // 0x14ca8d8: r0 = SvgPicture.asset()
    //     0x14ca8d8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14ca8dc: r0 = Center()
    //     0x14ca8dc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14ca8e0: mov             x1, x0
    // 0x14ca8e4: r0 = Instance_Alignment
    //     0x14ca8e4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14ca8e8: ldr             x0, [x0, #0xb10]
    // 0x14ca8ec: stur            x1, [fp, #-0x20]
    // 0x14ca8f0: StoreField: r1->field_f = r0
    //     0x14ca8f0: stur            w0, [x1, #0xf]
    // 0x14ca8f4: ldur            x0, [fp, #-0x18]
    // 0x14ca8f8: StoreField: r1->field_b = r0
    //     0x14ca8f8: stur            w0, [x1, #0xb]
    // 0x14ca8fc: r0 = Container()
    //     0x14ca8fc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ca900: stur            x0, [fp, #-0x18]
    // 0x14ca904: r16 = 60.000000
    //     0x14ca904: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14ca908: ldr             x16, [x16, #0x110]
    // 0x14ca90c: r30 = 60.000000
    //     0x14ca90c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14ca910: ldr             lr, [lr, #0x110]
    // 0x14ca914: stp             lr, x16, [SP, #0x10]
    // 0x14ca918: ldur            x16, [fp, #-0x28]
    // 0x14ca91c: ldur            lr, [fp, #-0x20]
    // 0x14ca920: stp             lr, x16, [SP]
    // 0x14ca924: mov             x1, x0
    // 0x14ca928: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x14ca928: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x14ca92c: ldr             x4, [x4, #0x8c0]
    // 0x14ca930: r0 = Container()
    //     0x14ca930: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ca934: r0 = InkWell()
    //     0x14ca934: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14ca938: mov             x3, x0
    // 0x14ca93c: ldur            x0, [fp, #-0x18]
    // 0x14ca940: stur            x3, [fp, #-0x20]
    // 0x14ca944: StoreField: r3->field_b = r0
    //     0x14ca944: stur            w0, [x3, #0xb]
    // 0x14ca948: ldur            x2, [fp, #-0x10]
    // 0x14ca94c: r1 = Function '<anonymous closure>':.
    //     0x14ca94c: add             x1, PP, #0x42, lsl #12  ; [pp+0x42698] AnonymousClosure: (0x9a80e0), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x14ca950: ldr             x1, [x1, #0x698]
    // 0x14ca954: r0 = AllocateClosure()
    //     0x14ca954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ca958: mov             x1, x0
    // 0x14ca95c: ldur            x0, [fp, #-0x20]
    // 0x14ca960: StoreField: r0->field_f = r1
    //     0x14ca960: stur            w1, [x0, #0xf]
    // 0x14ca964: r1 = true
    //     0x14ca964: add             x1, NULL, #0x20  ; true
    // 0x14ca968: StoreField: r0->field_43 = r1
    //     0x14ca968: stur            w1, [x0, #0x43]
    // 0x14ca96c: r2 = Instance_BoxShape
    //     0x14ca96c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14ca970: ldr             x2, [x2, #0x80]
    // 0x14ca974: StoreField: r0->field_47 = r2
    //     0x14ca974: stur            w2, [x0, #0x47]
    // 0x14ca978: StoreField: r0->field_6f = r1
    //     0x14ca978: stur            w1, [x0, #0x6f]
    // 0x14ca97c: r2 = false
    //     0x14ca97c: add             x2, NULL, #0x30  ; false
    // 0x14ca980: StoreField: r0->field_73 = r2
    //     0x14ca980: stur            w2, [x0, #0x73]
    // 0x14ca984: StoreField: r0->field_83 = r1
    //     0x14ca984: stur            w1, [x0, #0x83]
    // 0x14ca988: StoreField: r0->field_7b = r2
    //     0x14ca988: stur            w2, [x0, #0x7b]
    // 0x14ca98c: LeaveFrame
    //     0x14ca98c: mov             SP, fp
    //     0x14ca990: ldp             fp, lr, [SP], #0x10
    // 0x14ca994: ret
    //     0x14ca994: ret             
    // 0x14ca998: d0 = 12.000000
    //     0x14ca998: fmov            d0, #12.00000000
    // 0x14ca99c: b               #0x14ca9a8
    // 0x14ca9a0: ldur            x0, [fp, #-8]
    // 0x14ca9a4: d0 = 12.000000
    //     0x14ca9a4: fmov            d0, #12.00000000
    // 0x14ca9a8: ldur            x2, [fp, #-0x10]
    // 0x14ca9ac: r0 = Radius()
    //     0x14ca9ac: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14ca9b0: d0 = 12.000000
    //     0x14ca9b0: fmov            d0, #12.00000000
    // 0x14ca9b4: stur            x0, [fp, #-0x18]
    // 0x14ca9b8: StoreField: r0->field_7 = d0
    //     0x14ca9b8: stur            d0, [x0, #7]
    // 0x14ca9bc: StoreField: r0->field_f = d0
    //     0x14ca9bc: stur            d0, [x0, #0xf]
    // 0x14ca9c0: r0 = BorderRadius()
    //     0x14ca9c0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14ca9c4: mov             x2, x0
    // 0x14ca9c8: ldur            x0, [fp, #-0x18]
    // 0x14ca9cc: stur            x2, [fp, #-0x20]
    // 0x14ca9d0: StoreField: r2->field_7 = r0
    //     0x14ca9d0: stur            w0, [x2, #7]
    // 0x14ca9d4: StoreField: r2->field_b = r0
    //     0x14ca9d4: stur            w0, [x2, #0xb]
    // 0x14ca9d8: StoreField: r2->field_f = r0
    //     0x14ca9d8: stur            w0, [x2, #0xf]
    // 0x14ca9dc: StoreField: r2->field_13 = r0
    //     0x14ca9dc: stur            w0, [x2, #0x13]
    // 0x14ca9e0: ldur            x0, [fp, #-8]
    // 0x14ca9e4: LoadField: r1 = r0->field_f
    //     0x14ca9e4: ldur            w1, [x0, #0xf]
    // 0x14ca9e8: DecompressPointer r1
    //     0x14ca9e8: add             x1, x1, HEAP, lsl #32
    // 0x14ca9ec: r0 = controller()
    //     0x14ca9ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14ca9f0: LoadField: r1 = r0->field_73
    //     0x14ca9f0: ldur            w1, [x0, #0x73]
    // 0x14ca9f4: DecompressPointer r1
    //     0x14ca9f4: add             x1, x1, HEAP, lsl #32
    // 0x14ca9f8: ldur            x2, [fp, #-0x10]
    // 0x14ca9fc: LoadField: r0 = r2->field_f
    //     0x14ca9fc: ldur            w0, [x2, #0xf]
    // 0x14caa00: DecompressPointer r0
    //     0x14caa00: add             x0, x0, HEAP, lsl #32
    // 0x14caa04: stur            x0, [fp, #-8]
    // 0x14caa08: r0 = value()
    //     0x14caa08: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14caa0c: r1 = LoadClassIdInstr(r0)
    //     0x14caa0c: ldur            x1, [x0, #-1]
    //     0x14caa10: ubfx            x1, x1, #0xc, #0x14
    // 0x14caa14: ldur            x16, [fp, #-8]
    // 0x14caa18: stp             x16, x0, [SP]
    // 0x14caa1c: mov             x0, x1
    // 0x14caa20: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14caa20: sub             lr, x0, #0xb7
    //     0x14caa24: ldr             lr, [x21, lr, lsl #3]
    //     0x14caa28: blr             lr
    // 0x14caa2c: stur            x0, [fp, #-8]
    // 0x14caa30: r0 = Image()
    //     0x14caa30: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x14caa34: mov             x1, x0
    // 0x14caa38: ldur            x2, [fp, #-8]
    // 0x14caa3c: d0 = 60.000000
    //     0x14caa3c: ldr             d0, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14caa40: d1 = 60.000000
    //     0x14caa40: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0x14caa44: stur            x0, [fp, #-8]
    // 0x14caa48: r0 = Image.memory()
    //     0x14caa48: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x14caa4c: r0 = ClipRRect()
    //     0x14caa4c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14caa50: mov             x2, x0
    // 0x14caa54: ldur            x0, [fp, #-0x20]
    // 0x14caa58: stur            x2, [fp, #-0x18]
    // 0x14caa5c: StoreField: r2->field_f = r0
    //     0x14caa5c: stur            w0, [x2, #0xf]
    // 0x14caa60: r0 = Instance_Clip
    //     0x14caa60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14caa64: ldr             x0, [x0, #0x138]
    // 0x14caa68: ArrayStore: r2[0] = r0  ; List_4
    //     0x14caa68: stur            w0, [x2, #0x17]
    // 0x14caa6c: ldur            x0, [fp, #-8]
    // 0x14caa70: StoreField: r2->field_b = r0
    //     0x14caa70: stur            w0, [x2, #0xb]
    // 0x14caa74: ldr             x1, [fp, #0x18]
    // 0x14caa78: r0 = of()
    //     0x14caa78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14caa7c: LoadField: r2 = r0->field_5b
    //     0x14caa7c: ldur            w2, [x0, #0x5b]
    // 0x14caa80: DecompressPointer r2
    //     0x14caa80: add             x2, x2, HEAP, lsl #32
    // 0x14caa84: r16 = 1.000000
    //     0x14caa84: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x14caa88: str             x16, [SP]
    // 0x14caa8c: r1 = Null
    //     0x14caa8c: mov             x1, NULL
    // 0x14caa90: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14caa90: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14caa94: ldr             x4, [x4, #0x108]
    // 0x14caa98: r0 = Border.all()
    //     0x14caa98: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14caa9c: stur            x0, [fp, #-8]
    // 0x14caaa0: r0 = BoxDecoration()
    //     0x14caaa0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14caaa4: mov             x1, x0
    // 0x14caaa8: ldur            x0, [fp, #-8]
    // 0x14caaac: stur            x1, [fp, #-0x20]
    // 0x14caab0: StoreField: r1->field_f = r0
    //     0x14caab0: stur            w0, [x1, #0xf]
    // 0x14caab4: r0 = Instance_BoxShape
    //     0x14caab4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14caab8: ldr             x0, [x0, #0x970]
    // 0x14caabc: StoreField: r1->field_23 = r0
    //     0x14caabc: stur            w0, [x1, #0x23]
    // 0x14caac0: r0 = Container()
    //     0x14caac0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14caac4: stur            x0, [fp, #-8]
    // 0x14caac8: ldur            x16, [fp, #-0x20]
    // 0x14caacc: r30 = Instance_Icon
    //     0x14caacc: add             lr, PP, #0x33, lsl #12  ; [pp+0x338d0] Obj!Icon@d65db1
    //     0x14caad0: ldr             lr, [lr, #0x8d0]
    // 0x14caad4: stp             lr, x16, [SP]
    // 0x14caad8: mov             x1, x0
    // 0x14caadc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x14caadc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x14caae0: ldr             x4, [x4, #0x88]
    // 0x14caae4: r0 = Container()
    //     0x14caae4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14caae8: r0 = GestureDetector()
    //     0x14caae8: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x14caaec: ldur            x2, [fp, #-0x10]
    // 0x14caaf0: r1 = Function '<anonymous closure>':.
    //     0x14caaf0: add             x1, PP, #0x42, lsl #12  ; [pp+0x426a0] AnonymousClosure: (0x14cabbc), in [package:customer_app/app/presentation/views/cosmetic/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x14c9128)
    //     0x14caaf4: ldr             x1, [x1, #0x6a0]
    // 0x14caaf8: stur            x0, [fp, #-0x10]
    // 0x14caafc: r0 = AllocateClosure()
    //     0x14caafc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14cab00: ldur            x16, [fp, #-8]
    // 0x14cab04: stp             x16, x0, [SP]
    // 0x14cab08: ldur            x1, [fp, #-0x10]
    // 0x14cab0c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x14cab0c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x14cab10: ldr             x4, [x4, #0xaf0]
    // 0x14cab14: r0 = GestureDetector()
    //     0x14cab14: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x14cab18: r1 = <StackParentData>
    //     0x14cab18: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x14cab1c: ldr             x1, [x1, #0x8e0]
    // 0x14cab20: r0 = Positioned()
    //     0x14cab20: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x14cab24: mov             x3, x0
    // 0x14cab28: ldur            x0, [fp, #-0x10]
    // 0x14cab2c: stur            x3, [fp, #-8]
    // 0x14cab30: StoreField: r3->field_b = r0
    //     0x14cab30: stur            w0, [x3, #0xb]
    // 0x14cab34: r1 = Null
    //     0x14cab34: mov             x1, NULL
    // 0x14cab38: r2 = 4
    //     0x14cab38: movz            x2, #0x4
    // 0x14cab3c: r0 = AllocateArray()
    //     0x14cab3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14cab40: mov             x2, x0
    // 0x14cab44: ldur            x0, [fp, #-0x18]
    // 0x14cab48: stur            x2, [fp, #-0x10]
    // 0x14cab4c: StoreField: r2->field_f = r0
    //     0x14cab4c: stur            w0, [x2, #0xf]
    // 0x14cab50: ldur            x0, [fp, #-8]
    // 0x14cab54: StoreField: r2->field_13 = r0
    //     0x14cab54: stur            w0, [x2, #0x13]
    // 0x14cab58: r1 = <Widget>
    //     0x14cab58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14cab5c: r0 = AllocateGrowableArray()
    //     0x14cab5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14cab60: mov             x1, x0
    // 0x14cab64: ldur            x0, [fp, #-0x10]
    // 0x14cab68: stur            x1, [fp, #-8]
    // 0x14cab6c: StoreField: r1->field_f = r0
    //     0x14cab6c: stur            w0, [x1, #0xf]
    // 0x14cab70: r0 = 4
    //     0x14cab70: movz            x0, #0x4
    // 0x14cab74: StoreField: r1->field_b = r0
    //     0x14cab74: stur            w0, [x1, #0xb]
    // 0x14cab78: r0 = Stack()
    //     0x14cab78: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14cab7c: r1 = Instance_Alignment
    //     0x14cab7c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14cab80: ldr             x1, [x1, #0x950]
    // 0x14cab84: StoreField: r0->field_f = r1
    //     0x14cab84: stur            w1, [x0, #0xf]
    // 0x14cab88: r1 = Instance_StackFit
    //     0x14cab88: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x14cab8c: ldr             x1, [x1, #0xfa8]
    // 0x14cab90: ArrayStore: r0[0] = r1  ; List_4
    //     0x14cab90: stur            w1, [x0, #0x17]
    // 0x14cab94: r1 = Instance_Clip
    //     0x14cab94: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14cab98: ldr             x1, [x1, #0x7e0]
    // 0x14cab9c: StoreField: r0->field_1b = r1
    //     0x14cab9c: stur            w1, [x0, #0x1b]
    // 0x14caba0: ldur            x1, [fp, #-8]
    // 0x14caba4: StoreField: r0->field_b = r1
    //     0x14caba4: stur            w1, [x0, #0xb]
    // 0x14caba8: LeaveFrame
    //     0x14caba8: mov             SP, fp
    //     0x14cabac: ldp             fp, lr, [SP], #0x10
    // 0x14cabb0: ret
    //     0x14cabb0: ret             
    // 0x14cabb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14cabb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cabb8: b               #0x14ca6cc
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14cabbc, size: 0x228
    // 0x14cabbc: EnterFrame
    //     0x14cabbc: stp             fp, lr, [SP, #-0x10]!
    //     0x14cabc0: mov             fp, SP
    // 0x14cabc4: AllocStack(0x38)
    //     0x14cabc4: sub             SP, SP, #0x38
    // 0x14cabc8: SetupParameters(ReturnOrderWithProofView this /* r1 */)
    //     0x14cabc8: stur            NULL, [fp, #-8]
    //     0x14cabcc: movz            x0, #0
    //     0x14cabd0: add             x1, fp, w0, sxtw #2
    //     0x14cabd4: ldr             x1, [x1, #0x10]
    //     0x14cabd8: ldur            w2, [x1, #0x17]
    //     0x14cabdc: add             x2, x2, HEAP, lsl #32
    //     0x14cabe0: stur            x2, [fp, #-0x10]
    // 0x14cabe4: CheckStackOverflow
    //     0x14cabe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14cabe8: cmp             SP, x16
    //     0x14cabec: b.ls            #0x14caddc
    // 0x14cabf0: InitAsync() -> Future<void?>
    //     0x14cabf0: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14cabf4: bl              #0x6326e0  ; InitAsyncStub
    // 0x14cabf8: ldur            x0, [fp, #-0x10]
    // 0x14cabfc: LoadField: r2 = r0->field_b
    //     0x14cabfc: ldur            w2, [x0, #0xb]
    // 0x14cac00: DecompressPointer r2
    //     0x14cac00: add             x2, x2, HEAP, lsl #32
    // 0x14cac04: stur            x2, [fp, #-0x18]
    // 0x14cac08: LoadField: r1 = r2->field_f
    //     0x14cac08: ldur            w1, [x2, #0xf]
    // 0x14cac0c: DecompressPointer r1
    //     0x14cac0c: add             x1, x1, HEAP, lsl #32
    // 0x14cac10: r0 = controller()
    //     0x14cac10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cac14: mov             x2, x0
    // 0x14cac18: ldur            x0, [fp, #-0x18]
    // 0x14cac1c: stur            x2, [fp, #-0x20]
    // 0x14cac20: LoadField: r1 = r0->field_f
    //     0x14cac20: ldur            w1, [x0, #0xf]
    // 0x14cac24: DecompressPointer r1
    //     0x14cac24: add             x1, x1, HEAP, lsl #32
    // 0x14cac28: r0 = controller()
    //     0x14cac28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cac2c: LoadField: r1 = r0->field_6f
    //     0x14cac2c: ldur            w1, [x0, #0x6f]
    // 0x14cac30: DecompressPointer r1
    //     0x14cac30: add             x1, x1, HEAP, lsl #32
    // 0x14cac34: ldur            x0, [fp, #-0x10]
    // 0x14cac38: LoadField: r2 = r0->field_f
    //     0x14cac38: ldur            w2, [x0, #0xf]
    // 0x14cac3c: DecompressPointer r2
    //     0x14cac3c: add             x2, x2, HEAP, lsl #32
    // 0x14cac40: stur            x2, [fp, #-0x28]
    // 0x14cac44: r0 = value()
    //     0x14cac44: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14cac48: r1 = LoadClassIdInstr(r0)
    //     0x14cac48: ldur            x1, [x0, #-1]
    //     0x14cac4c: ubfx            x1, x1, #0xc, #0x14
    // 0x14cac50: ldur            x16, [fp, #-0x28]
    // 0x14cac54: stp             x16, x0, [SP]
    // 0x14cac58: mov             x0, x1
    // 0x14cac5c: r0 = GDT[cid_x0 + -0xb7]()
    //     0x14cac5c: sub             lr, x0, #0xb7
    //     0x14cac60: ldr             lr, [x21, lr, lsl #3]
    //     0x14cac64: blr             lr
    // 0x14cac68: r1 = LoadClassIdInstr(r0)
    //     0x14cac68: ldur            x1, [x0, #-1]
    //     0x14cac6c: ubfx            x1, x1, #0xc, #0x14
    // 0x14cac70: mov             x16, x0
    // 0x14cac74: mov             x0, x1
    // 0x14cac78: mov             x1, x16
    // 0x14cac7c: r0 = GDT[cid_x0 + -0xe96]()
    //     0x14cac7c: sub             lr, x0, #0xe96
    //     0x14cac80: ldr             lr, [x21, lr, lsl #3]
    //     0x14cac84: blr             lr
    // 0x14cac88: ldur            x1, [fp, #-0x20]
    // 0x14cac8c: mov             x2, x0
    // 0x14cac90: r0 = getFileSize()
    //     0x14cac90: bl              #0x9a577c  ; [package:customer_app/app/presentation/controllers/post_order/return_order_controller.dart] ReturnOrderController::getFileSize
    // 0x14cac94: mov             x1, x0
    // 0x14cac98: stur            x1, [fp, #-0x20]
    // 0x14cac9c: r0 = Await()
    //     0x14cac9c: bl              #0x63248c  ; AwaitStub
    // 0x14caca0: mov             x3, x0
    // 0x14caca4: r2 = Null
    //     0x14caca4: mov             x2, NULL
    // 0x14caca8: r1 = Null
    //     0x14caca8: mov             x1, NULL
    // 0x14cacac: stur            x3, [fp, #-0x20]
    // 0x14cacb0: branchIfSmi(r0, 0x14cacd8)
    //     0x14cacb0: tbz             w0, #0, #0x14cacd8
    // 0x14cacb4: r4 = LoadClassIdInstr(r0)
    //     0x14cacb4: ldur            x4, [x0, #-1]
    //     0x14cacb8: ubfx            x4, x4, #0xc, #0x14
    // 0x14cacbc: sub             x4, x4, #0x3c
    // 0x14cacc0: cmp             x4, #1
    // 0x14cacc4: b.ls            #0x14cacd8
    // 0x14cacc8: r8 = int
    //     0x14cacc8: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14caccc: r3 = Null
    //     0x14caccc: add             x3, PP, #0x42, lsl #12  ; [pp+0x426a8] Null
    //     0x14cacd0: ldr             x3, [x3, #0x6a8]
    // 0x14cacd4: r0 = int()
    //     0x14cacd4: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14cacd8: ldur            x0, [fp, #-0x18]
    // 0x14cacdc: LoadField: r1 = r0->field_f
    //     0x14cacdc: ldur            w1, [x0, #0xf]
    // 0x14cace0: DecompressPointer r1
    //     0x14cace0: add             x1, x1, HEAP, lsl #32
    // 0x14cace4: r0 = controller()
    //     0x14cace4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cace8: LoadField: r1 = r0->field_c7
    //     0x14cace8: ldur            x1, [x0, #0xc7]
    // 0x14cacec: ldur            x2, [fp, #-0x20]
    // 0x14cacf0: r3 = LoadInt32Instr(r2)
    //     0x14cacf0: sbfx            x3, x2, #1, #0x1f
    //     0x14cacf4: tbz             w2, #0, #0x14cacfc
    //     0x14cacf8: ldur            x3, [x2, #7]
    // 0x14cacfc: sub             x2, x1, x3
    // 0x14cad00: StoreField: r0->field_c7 = r2
    //     0x14cad00: stur            x2, [x0, #0xc7]
    // 0x14cad04: ldur            x0, [fp, #-0x18]
    // 0x14cad08: LoadField: r1 = r0->field_f
    //     0x14cad08: ldur            w1, [x0, #0xf]
    // 0x14cad0c: DecompressPointer r1
    //     0x14cad0c: add             x1, x1, HEAP, lsl #32
    // 0x14cad10: r0 = controller()
    //     0x14cad10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cad14: LoadField: r1 = r0->field_6f
    //     0x14cad14: ldur            w1, [x0, #0x6f]
    // 0x14cad18: DecompressPointer r1
    //     0x14cad18: add             x1, x1, HEAP, lsl #32
    // 0x14cad1c: ldur            x0, [fp, #-0x10]
    // 0x14cad20: LoadField: r2 = r0->field_f
    //     0x14cad20: ldur            w2, [x0, #0xf]
    // 0x14cad24: DecompressPointer r2
    //     0x14cad24: add             x2, x2, HEAP, lsl #32
    // 0x14cad28: r3 = LoadInt32Instr(r2)
    //     0x14cad28: sbfx            x3, x2, #1, #0x1f
    //     0x14cad2c: tbz             w2, #0, #0x14cad34
    //     0x14cad30: ldur            x3, [x2, #7]
    // 0x14cad34: mov             x2, x3
    // 0x14cad38: r0 = removeAt()
    //     0x14cad38: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14cad3c: ldur            x0, [fp, #-0x18]
    // 0x14cad40: LoadField: r1 = r0->field_f
    //     0x14cad40: ldur            w1, [x0, #0xf]
    // 0x14cad44: DecompressPointer r1
    //     0x14cad44: add             x1, x1, HEAP, lsl #32
    // 0x14cad48: r0 = controller()
    //     0x14cad48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cad4c: LoadField: r1 = r0->field_73
    //     0x14cad4c: ldur            w1, [x0, #0x73]
    // 0x14cad50: DecompressPointer r1
    //     0x14cad50: add             x1, x1, HEAP, lsl #32
    // 0x14cad54: ldur            x0, [fp, #-0x10]
    // 0x14cad58: LoadField: r2 = r0->field_f
    //     0x14cad58: ldur            w2, [x0, #0xf]
    // 0x14cad5c: DecompressPointer r2
    //     0x14cad5c: add             x2, x2, HEAP, lsl #32
    // 0x14cad60: r0 = LoadInt32Instr(r2)
    //     0x14cad60: sbfx            x0, x2, #1, #0x1f
    //     0x14cad64: tbz             w2, #0, #0x14cad6c
    //     0x14cad68: ldur            x0, [x2, #7]
    // 0x14cad6c: mov             x2, x0
    // 0x14cad70: r0 = removeAt()
    //     0x14cad70: bl              #0x64b64c  ; [dart:collection] ListBase::removeAt
    // 0x14cad74: ldur            x0, [fp, #-0x18]
    // 0x14cad78: LoadField: r1 = r0->field_f
    //     0x14cad78: ldur            w1, [x0, #0xf]
    // 0x14cad7c: DecompressPointer r1
    //     0x14cad7c: add             x1, x1, HEAP, lsl #32
    // 0x14cad80: r0 = controller()
    //     0x14cad80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cad84: LoadField: r1 = r0->field_73
    //     0x14cad84: ldur            w1, [x0, #0x73]
    // 0x14cad88: DecompressPointer r1
    //     0x14cad88: add             x1, x1, HEAP, lsl #32
    // 0x14cad8c: r0 = value()
    //     0x14cad8c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14cad90: r1 = LoadClassIdInstr(r0)
    //     0x14cad90: ldur            x1, [x0, #-1]
    //     0x14cad94: ubfx            x1, x1, #0xc, #0x14
    // 0x14cad98: str             x0, [SP]
    // 0x14cad9c: mov             x0, x1
    // 0x14cada0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14cada0: movz            x17, #0xc898
    //     0x14cada4: add             lr, x0, x17
    //     0x14cada8: ldr             lr, [x21, lr, lsl #3]
    //     0x14cadac: blr             lr
    // 0x14cadb0: cbnz            w0, #0x14cadd4
    // 0x14cadb4: ldur            x0, [fp, #-0x18]
    // 0x14cadb8: LoadField: r1 = r0->field_f
    //     0x14cadb8: ldur            w1, [x0, #0xf]
    // 0x14cadbc: DecompressPointer r1
    //     0x14cadbc: add             x1, x1, HEAP, lsl #32
    // 0x14cadc0: r0 = controller()
    //     0x14cadc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14cadc4: LoadField: r1 = r0->field_9b
    //     0x14cadc4: ldur            w1, [x0, #0x9b]
    // 0x14cadc8: DecompressPointer r1
    //     0x14cadc8: add             x1, x1, HEAP, lsl #32
    // 0x14cadcc: r2 = false
    //     0x14cadcc: add             x2, NULL, #0x30  ; false
    // 0x14cadd0: r0 = value=()
    //     0x14cadd0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14cadd4: r0 = Null
    //     0x14cadd4: mov             x0, NULL
    // 0x14cadd8: r0 = ReturnAsyncNotFuture()
    //     0x14cadd8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14caddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14caddc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14cade0: b               #0x14cabf0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dd7d8, size: 0x18c
    // 0x15dd7d8: EnterFrame
    //     0x15dd7d8: stp             fp, lr, [SP, #-0x10]!
    //     0x15dd7dc: mov             fp, SP
    // 0x15dd7e0: AllocStack(0x28)
    //     0x15dd7e0: sub             SP, SP, #0x28
    // 0x15dd7e4: SetupParameters(ReturnOrderWithProofView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15dd7e4: mov             x0, x1
    //     0x15dd7e8: stur            x1, [fp, #-8]
    //     0x15dd7ec: mov             x1, x2
    //     0x15dd7f0: stur            x2, [fp, #-0x10]
    // 0x15dd7f4: CheckStackOverflow
    //     0x15dd7f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dd7f8: cmp             SP, x16
    //     0x15dd7fc: b.ls            #0x15dd95c
    // 0x15dd800: r1 = 2
    //     0x15dd800: movz            x1, #0x2
    // 0x15dd804: r0 = AllocateContext()
    //     0x15dd804: bl              #0x16f6108  ; AllocateContextStub
    // 0x15dd808: mov             x1, x0
    // 0x15dd80c: ldur            x0, [fp, #-8]
    // 0x15dd810: stur            x1, [fp, #-0x18]
    // 0x15dd814: StoreField: r1->field_f = r0
    //     0x15dd814: stur            w0, [x1, #0xf]
    // 0x15dd818: ldur            x0, [fp, #-0x10]
    // 0x15dd81c: StoreField: r1->field_13 = r0
    //     0x15dd81c: stur            w0, [x1, #0x13]
    // 0x15dd820: r0 = Obx()
    //     0x15dd820: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15dd824: ldur            x2, [fp, #-0x18]
    // 0x15dd828: r1 = Function '<anonymous closure>':.
    //     0x15dd828: add             x1, PP, #0x42, lsl #12  ; [pp+0x426b8] AnonymousClosure: (0x15d5454), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15dd82c: ldr             x1, [x1, #0x6b8]
    // 0x15dd830: stur            x0, [fp, #-8]
    // 0x15dd834: r0 = AllocateClosure()
    //     0x15dd834: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dd838: mov             x1, x0
    // 0x15dd83c: ldur            x0, [fp, #-8]
    // 0x15dd840: StoreField: r0->field_b = r1
    //     0x15dd840: stur            w1, [x0, #0xb]
    // 0x15dd844: ldur            x1, [fp, #-0x10]
    // 0x15dd848: r0 = of()
    //     0x15dd848: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dd84c: LoadField: r1 = r0->field_5b
    //     0x15dd84c: ldur            w1, [x0, #0x5b]
    // 0x15dd850: DecompressPointer r1
    //     0x15dd850: add             x1, x1, HEAP, lsl #32
    // 0x15dd854: stur            x1, [fp, #-0x10]
    // 0x15dd858: r0 = ColorFilter()
    //     0x15dd858: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dd85c: mov             x1, x0
    // 0x15dd860: ldur            x0, [fp, #-0x10]
    // 0x15dd864: stur            x1, [fp, #-0x20]
    // 0x15dd868: StoreField: r1->field_7 = r0
    //     0x15dd868: stur            w0, [x1, #7]
    // 0x15dd86c: r0 = Instance_BlendMode
    //     0x15dd86c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dd870: ldr             x0, [x0, #0xb30]
    // 0x15dd874: StoreField: r1->field_b = r0
    //     0x15dd874: stur            w0, [x1, #0xb]
    // 0x15dd878: r0 = 1
    //     0x15dd878: movz            x0, #0x1
    // 0x15dd87c: StoreField: r1->field_13 = r0
    //     0x15dd87c: stur            x0, [x1, #0x13]
    // 0x15dd880: r0 = SvgPicture()
    //     0x15dd880: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dd884: stur            x0, [fp, #-0x10]
    // 0x15dd888: ldur            x16, [fp, #-0x20]
    // 0x15dd88c: str             x16, [SP]
    // 0x15dd890: mov             x1, x0
    // 0x15dd894: r2 = "assets/images/appbar_arrow.svg"
    //     0x15dd894: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15dd898: ldr             x2, [x2, #0xa40]
    // 0x15dd89c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dd89c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dd8a0: ldr             x4, [x4, #0xa38]
    // 0x15dd8a4: r0 = SvgPicture.asset()
    //     0x15dd8a4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dd8a8: r0 = Align()
    //     0x15dd8a8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dd8ac: mov             x1, x0
    // 0x15dd8b0: r0 = Instance_Alignment
    //     0x15dd8b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dd8b4: ldr             x0, [x0, #0xb10]
    // 0x15dd8b8: stur            x1, [fp, #-0x20]
    // 0x15dd8bc: StoreField: r1->field_f = r0
    //     0x15dd8bc: stur            w0, [x1, #0xf]
    // 0x15dd8c0: r0 = 1.000000
    //     0x15dd8c0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dd8c4: StoreField: r1->field_13 = r0
    //     0x15dd8c4: stur            w0, [x1, #0x13]
    // 0x15dd8c8: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dd8c8: stur            w0, [x1, #0x17]
    // 0x15dd8cc: ldur            x0, [fp, #-0x10]
    // 0x15dd8d0: StoreField: r1->field_b = r0
    //     0x15dd8d0: stur            w0, [x1, #0xb]
    // 0x15dd8d4: r0 = InkWell()
    //     0x15dd8d4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dd8d8: mov             x3, x0
    // 0x15dd8dc: ldur            x0, [fp, #-0x20]
    // 0x15dd8e0: stur            x3, [fp, #-0x10]
    // 0x15dd8e4: StoreField: r3->field_b = r0
    //     0x15dd8e4: stur            w0, [x3, #0xb]
    // 0x15dd8e8: ldur            x2, [fp, #-0x18]
    // 0x15dd8ec: r1 = Function '<anonymous closure>':.
    //     0x15dd8ec: add             x1, PP, #0x42, lsl #12  ; [pp+0x426c0] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15dd8f0: ldr             x1, [x1, #0x6c0]
    // 0x15dd8f4: r0 = AllocateClosure()
    //     0x15dd8f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dd8f8: ldur            x2, [fp, #-0x10]
    // 0x15dd8fc: StoreField: r2->field_f = r0
    //     0x15dd8fc: stur            w0, [x2, #0xf]
    // 0x15dd900: r0 = true
    //     0x15dd900: add             x0, NULL, #0x20  ; true
    // 0x15dd904: StoreField: r2->field_43 = r0
    //     0x15dd904: stur            w0, [x2, #0x43]
    // 0x15dd908: r1 = Instance_BoxShape
    //     0x15dd908: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dd90c: ldr             x1, [x1, #0x80]
    // 0x15dd910: StoreField: r2->field_47 = r1
    //     0x15dd910: stur            w1, [x2, #0x47]
    // 0x15dd914: StoreField: r2->field_6f = r0
    //     0x15dd914: stur            w0, [x2, #0x6f]
    // 0x15dd918: r1 = false
    //     0x15dd918: add             x1, NULL, #0x30  ; false
    // 0x15dd91c: StoreField: r2->field_73 = r1
    //     0x15dd91c: stur            w1, [x2, #0x73]
    // 0x15dd920: StoreField: r2->field_83 = r0
    //     0x15dd920: stur            w0, [x2, #0x83]
    // 0x15dd924: StoreField: r2->field_7b = r1
    //     0x15dd924: stur            w1, [x2, #0x7b]
    // 0x15dd928: r0 = AppBar()
    //     0x15dd928: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dd92c: stur            x0, [fp, #-0x18]
    // 0x15dd930: ldur            x16, [fp, #-8]
    // 0x15dd934: str             x16, [SP]
    // 0x15dd938: mov             x1, x0
    // 0x15dd93c: ldur            x2, [fp, #-0x10]
    // 0x15dd940: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15dd940: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15dd944: ldr             x4, [x4, #0xf00]
    // 0x15dd948: r0 = AppBar()
    //     0x15dd948: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dd94c: ldur            x0, [fp, #-0x18]
    // 0x15dd950: LeaveFrame
    //     0x15dd950: mov             SP, fp
    //     0x15dd954: ldp             fp, lr, [SP], #0x10
    // 0x15dd958: ret
    //     0x15dd958: ret             
    // 0x15dd95c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dd95c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dd960: b               #0x15dd800
  }
}
