// lib: , url: package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart

// class id: 1049259, size: 0x8
class :: {
}

// class id: 3439, size: 0x34, field offset: 0x14
class _CollectionFilterState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x939d50, size: 0x610
    // 0x939d50: EnterFrame
    //     0x939d50: stp             fp, lr, [SP, #-0x10]!
    //     0x939d54: mov             fp, SP
    // 0x939d58: AllocStack(0x50)
    //     0x939d58: sub             SP, SP, #0x50
    // 0x939d5c: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */)
    //     0x939d5c: mov             x0, x1
    //     0x939d60: stur            x1, [fp, #-8]
    // 0x939d64: CheckStackOverflow
    //     0x939d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x939d68: cmp             SP, x16
    //     0x939d6c: b.ls            #0x93a330
    // 0x939d70: LoadField: r1 = r0->field_b
    //     0x939d70: ldur            w1, [x0, #0xb]
    // 0x939d74: DecompressPointer r1
    //     0x939d74: add             x1, x1, HEAP, lsl #32
    // 0x939d78: cmp             w1, NULL
    // 0x939d7c: b.eq            #0x93a338
    // 0x939d80: LoadField: r2 = r1->field_f
    //     0x939d80: ldur            w2, [x1, #0xf]
    // 0x939d84: DecompressPointer r2
    //     0x939d84: add             x2, x2, HEAP, lsl #32
    // 0x939d88: LoadField: r1 = r2->field_7
    //     0x939d88: ldur            w1, [x2, #7]
    // 0x939d8c: DecompressPointer r1
    //     0x939d8c: add             x1, x1, HEAP, lsl #32
    // 0x939d90: cmp             w1, NULL
    // 0x939d94: b.eq            #0x939e64
    // 0x939d98: LoadField: r2 = r0->field_13
    //     0x939d98: ldur            w2, [x0, #0x13]
    // 0x939d9c: DecompressPointer r2
    //     0x939d9c: add             x2, x2, HEAP, lsl #32
    // 0x939da0: mov             x16, x1
    // 0x939da4: mov             x1, x2
    // 0x939da8: mov             x2, x16
    // 0x939dac: r0 = addAll()
    //     0x939dac: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x939db0: ldur            x0, [fp, #-8]
    // 0x939db4: LoadField: r1 = r0->field_1b
    //     0x939db4: ldur            w1, [x0, #0x1b]
    // 0x939db8: DecompressPointer r1
    //     0x939db8: add             x1, x1, HEAP, lsl #32
    // 0x939dbc: stur            x1, [fp, #-0x10]
    // 0x939dc0: LoadField: r2 = r0->field_b
    //     0x939dc0: ldur            w2, [x0, #0xb]
    // 0x939dc4: DecompressPointer r2
    //     0x939dc4: add             x2, x2, HEAP, lsl #32
    // 0x939dc8: cmp             w2, NULL
    // 0x939dcc: b.eq            #0x93a33c
    // 0x939dd0: LoadField: r3 = r2->field_f
    //     0x939dd0: ldur            w3, [x2, #0xf]
    // 0x939dd4: DecompressPointer r3
    //     0x939dd4: add             x3, x3, HEAP, lsl #32
    // 0x939dd8: LoadField: r2 = r3->field_7
    //     0x939dd8: ldur            w2, [x3, #7]
    // 0x939ddc: DecompressPointer r2
    //     0x939ddc: add             x2, x2, HEAP, lsl #32
    // 0x939de0: cmp             w2, NULL
    // 0x939de4: b.ne            #0x939e24
    // 0x939de8: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x939de8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x939dec: ldr             x0, [x0]
    //     0x939df0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x939df4: cmp             w0, w16
    //     0x939df8: b.ne            #0x939e04
    //     0x939dfc: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x939e00: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x939e04: r1 = <String>
    //     0x939e04: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x939e08: stur            x0, [fp, #-0x18]
    // 0x939e0c: r0 = AllocateGrowableArray()
    //     0x939e0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x939e10: mov             x1, x0
    // 0x939e14: ldur            x0, [fp, #-0x18]
    // 0x939e18: StoreField: r1->field_f = r0
    //     0x939e18: stur            w0, [x1, #0xf]
    // 0x939e1c: StoreField: r1->field_b = rZR
    //     0x939e1c: stur            wzr, [x1, #0xb]
    // 0x939e20: mov             x2, x1
    // 0x939e24: ldur            x0, [fp, #-8]
    // 0x939e28: ldur            x1, [fp, #-0x10]
    // 0x939e2c: r0 = addAll()
    //     0x939e2c: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x939e30: ldur            x3, [fp, #-8]
    // 0x939e34: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x939e34: ldur            w1, [x3, #0x17]
    // 0x939e38: DecompressPointer r1
    //     0x939e38: add             x1, x1, HEAP, lsl #32
    // 0x939e3c: LoadField: r0 = r3->field_1b
    //     0x939e3c: ldur            w0, [x3, #0x1b]
    // 0x939e40: DecompressPointer r0
    //     0x939e40: add             x0, x0, HEAP, lsl #32
    // 0x939e44: StoreField: r1->field_7 = r0
    //     0x939e44: stur            w0, [x1, #7]
    //     0x939e48: ldurb           w16, [x1, #-1]
    //     0x939e4c: ldurb           w17, [x0, #-1]
    //     0x939e50: and             x16, x17, x16, lsr #2
    //     0x939e54: tst             x16, HEAP, lsr #32
    //     0x939e58: b.eq            #0x939e60
    //     0x939e5c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x939e60: b               #0x939e68
    // 0x939e64: mov             x3, x0
    // 0x939e68: LoadField: r0 = r3->field_b
    //     0x939e68: ldur            w0, [x3, #0xb]
    // 0x939e6c: DecompressPointer r0
    //     0x939e6c: add             x0, x0, HEAP, lsl #32
    // 0x939e70: cmp             w0, NULL
    // 0x939e74: b.eq            #0x93a340
    // 0x939e78: LoadField: r1 = r0->field_f
    //     0x939e78: ldur            w1, [x0, #0xf]
    // 0x939e7c: DecompressPointer r1
    //     0x939e7c: add             x1, x1, HEAP, lsl #32
    // 0x939e80: LoadField: r2 = r1->field_b
    //     0x939e80: ldur            w2, [x1, #0xb]
    // 0x939e84: DecompressPointer r2
    //     0x939e84: add             x2, x2, HEAP, lsl #32
    // 0x939e88: cmp             w2, NULL
    // 0x939e8c: b.eq            #0x939f4c
    // 0x939e90: LoadField: r1 = r3->field_13
    //     0x939e90: ldur            w1, [x3, #0x13]
    // 0x939e94: DecompressPointer r1
    //     0x939e94: add             x1, x1, HEAP, lsl #32
    // 0x939e98: r0 = addAll()
    //     0x939e98: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x939e9c: ldur            x0, [fp, #-8]
    // 0x939ea0: LoadField: r1 = r0->field_2b
    //     0x939ea0: ldur            w1, [x0, #0x2b]
    // 0x939ea4: DecompressPointer r1
    //     0x939ea4: add             x1, x1, HEAP, lsl #32
    // 0x939ea8: stur            x1, [fp, #-0x10]
    // 0x939eac: LoadField: r2 = r0->field_b
    //     0x939eac: ldur            w2, [x0, #0xb]
    // 0x939eb0: DecompressPointer r2
    //     0x939eb0: add             x2, x2, HEAP, lsl #32
    // 0x939eb4: cmp             w2, NULL
    // 0x939eb8: b.eq            #0x93a344
    // 0x939ebc: LoadField: r3 = r2->field_f
    //     0x939ebc: ldur            w3, [x2, #0xf]
    // 0x939ec0: DecompressPointer r3
    //     0x939ec0: add             x3, x3, HEAP, lsl #32
    // 0x939ec4: LoadField: r2 = r3->field_b
    //     0x939ec4: ldur            w2, [x3, #0xb]
    // 0x939ec8: DecompressPointer r2
    //     0x939ec8: add             x2, x2, HEAP, lsl #32
    // 0x939ecc: cmp             w2, NULL
    // 0x939ed0: b.ne            #0x939f10
    // 0x939ed4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x939ed4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x939ed8: ldr             x0, [x0]
    //     0x939edc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x939ee0: cmp             w0, w16
    //     0x939ee4: b.ne            #0x939ef0
    //     0x939ee8: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x939eec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x939ef0: r1 = <String>
    //     0x939ef0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x939ef4: stur            x0, [fp, #-0x18]
    // 0x939ef8: r0 = AllocateGrowableArray()
    //     0x939ef8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x939efc: mov             x1, x0
    // 0x939f00: ldur            x0, [fp, #-0x18]
    // 0x939f04: StoreField: r1->field_f = r0
    //     0x939f04: stur            w0, [x1, #0xf]
    // 0x939f08: StoreField: r1->field_b = rZR
    //     0x939f08: stur            wzr, [x1, #0xb]
    // 0x939f0c: mov             x2, x1
    // 0x939f10: ldur            x0, [fp, #-8]
    // 0x939f14: ldur            x1, [fp, #-0x10]
    // 0x939f18: r0 = addAll()
    //     0x939f18: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x939f1c: ldur            x3, [fp, #-8]
    // 0x939f20: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x939f20: ldur            w1, [x3, #0x17]
    // 0x939f24: DecompressPointer r1
    //     0x939f24: add             x1, x1, HEAP, lsl #32
    // 0x939f28: LoadField: r0 = r3->field_2b
    //     0x939f28: ldur            w0, [x3, #0x2b]
    // 0x939f2c: DecompressPointer r0
    //     0x939f2c: add             x0, x0, HEAP, lsl #32
    // 0x939f30: StoreField: r1->field_f = r0
    //     0x939f30: stur            w0, [x1, #0xf]
    //     0x939f34: ldurb           w16, [x1, #-1]
    //     0x939f38: ldurb           w17, [x0, #-1]
    //     0x939f3c: and             x16, x17, x16, lsr #2
    //     0x939f40: tst             x16, HEAP, lsr #32
    //     0x939f44: b.eq            #0x939f4c
    //     0x939f48: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x939f4c: LoadField: r0 = r3->field_b
    //     0x939f4c: ldur            w0, [x3, #0xb]
    // 0x939f50: DecompressPointer r0
    //     0x939f50: add             x0, x0, HEAP, lsl #32
    // 0x939f54: cmp             w0, NULL
    // 0x939f58: b.eq            #0x93a348
    // 0x939f5c: LoadField: r1 = r0->field_f
    //     0x939f5c: ldur            w1, [x0, #0xf]
    // 0x939f60: DecompressPointer r1
    //     0x939f60: add             x1, x1, HEAP, lsl #32
    // 0x939f64: LoadField: r2 = r1->field_f
    //     0x939f64: ldur            w2, [x1, #0xf]
    // 0x939f68: DecompressPointer r2
    //     0x939f68: add             x2, x2, HEAP, lsl #32
    // 0x939f6c: cmp             w2, NULL
    // 0x939f70: b.eq            #0x93a034
    // 0x939f74: LoadField: r1 = r3->field_13
    //     0x939f74: ldur            w1, [x3, #0x13]
    // 0x939f78: DecompressPointer r1
    //     0x939f78: add             x1, x1, HEAP, lsl #32
    // 0x939f7c: r0 = addAll()
    //     0x939f7c: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x939f80: ldur            x0, [fp, #-8]
    // 0x939f84: LoadField: r1 = r0->field_23
    //     0x939f84: ldur            w1, [x0, #0x23]
    // 0x939f88: DecompressPointer r1
    //     0x939f88: add             x1, x1, HEAP, lsl #32
    // 0x939f8c: stur            x1, [fp, #-0x10]
    // 0x939f90: LoadField: r2 = r0->field_b
    //     0x939f90: ldur            w2, [x0, #0xb]
    // 0x939f94: DecompressPointer r2
    //     0x939f94: add             x2, x2, HEAP, lsl #32
    // 0x939f98: cmp             w2, NULL
    // 0x939f9c: b.eq            #0x93a34c
    // 0x939fa0: LoadField: r3 = r2->field_f
    //     0x939fa0: ldur            w3, [x2, #0xf]
    // 0x939fa4: DecompressPointer r3
    //     0x939fa4: add             x3, x3, HEAP, lsl #32
    // 0x939fa8: LoadField: r2 = r3->field_f
    //     0x939fa8: ldur            w2, [x3, #0xf]
    // 0x939fac: DecompressPointer r2
    //     0x939fac: add             x2, x2, HEAP, lsl #32
    // 0x939fb0: cmp             w2, NULL
    // 0x939fb4: b.ne            #0x939ff4
    // 0x939fb8: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x939fb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x939fbc: ldr             x0, [x0]
    //     0x939fc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x939fc4: cmp             w0, w16
    //     0x939fc8: b.ne            #0x939fd4
    //     0x939fcc: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x939fd0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x939fd4: r1 = <String>
    //     0x939fd4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x939fd8: stur            x0, [fp, #-0x18]
    // 0x939fdc: r0 = AllocateGrowableArray()
    //     0x939fdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x939fe0: mov             x1, x0
    // 0x939fe4: ldur            x0, [fp, #-0x18]
    // 0x939fe8: StoreField: r1->field_f = r0
    //     0x939fe8: stur            w0, [x1, #0xf]
    // 0x939fec: StoreField: r1->field_b = rZR
    //     0x939fec: stur            wzr, [x1, #0xb]
    // 0x939ff0: mov             x2, x1
    // 0x939ff4: ldur            x0, [fp, #-8]
    // 0x939ff8: ldur            x1, [fp, #-0x10]
    // 0x939ffc: r0 = addAll()
    //     0x939ffc: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x93a000: ldur            x1, [fp, #-8]
    // 0x93a004: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x93a004: ldur            w2, [x1, #0x17]
    // 0x93a008: DecompressPointer r2
    //     0x93a008: add             x2, x2, HEAP, lsl #32
    // 0x93a00c: LoadField: r0 = r1->field_23
    //     0x93a00c: ldur            w0, [x1, #0x23]
    // 0x93a010: DecompressPointer r0
    //     0x93a010: add             x0, x0, HEAP, lsl #32
    // 0x93a014: StoreField: r2->field_b = r0
    //     0x93a014: stur            w0, [x2, #0xb]
    //     0x93a018: ldurb           w16, [x2, #-1]
    //     0x93a01c: ldurb           w17, [x0, #-1]
    //     0x93a020: and             x16, x17, x16, lsr #2
    //     0x93a024: tst             x16, HEAP, lsr #32
    //     0x93a028: b.eq            #0x93a030
    //     0x93a02c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93a030: b               #0x93a038
    // 0x93a034: mov             x1, x3
    // 0x93a038: LoadField: r0 = r1->field_b
    //     0x93a038: ldur            w0, [x1, #0xb]
    // 0x93a03c: DecompressPointer r0
    //     0x93a03c: add             x0, x0, HEAP, lsl #32
    // 0x93a040: cmp             w0, NULL
    // 0x93a044: b.eq            #0x93a350
    // 0x93a048: LoadField: r2 = r0->field_f
    //     0x93a048: ldur            w2, [x0, #0xf]
    // 0x93a04c: DecompressPointer r2
    //     0x93a04c: add             x2, x2, HEAP, lsl #32
    // 0x93a050: LoadField: r0 = r2->field_13
    //     0x93a050: ldur            w0, [x2, #0x13]
    // 0x93a054: DecompressPointer r0
    //     0x93a054: add             x0, x0, HEAP, lsl #32
    // 0x93a058: cmp             w0, NULL
    // 0x93a05c: b.eq            #0x93a300
    // 0x93a060: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x93a060: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93a064: ldr             x0, [x0]
    //     0x93a068: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93a06c: cmp             w0, w16
    //     0x93a070: b.ne            #0x93a07c
    //     0x93a074: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x93a078: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93a07c: r1 = <String>
    //     0x93a07c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x93a080: stur            x0, [fp, #-0x10]
    // 0x93a084: r0 = AllocateGrowableArray()
    //     0x93a084: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x93a088: mov             x2, x0
    // 0x93a08c: ldur            x0, [fp, #-0x10]
    // 0x93a090: stur            x2, [fp, #-0x18]
    // 0x93a094: StoreField: r2->field_f = r0
    //     0x93a094: stur            w0, [x2, #0xf]
    // 0x93a098: StoreField: r2->field_b = rZR
    //     0x93a098: stur            wzr, [x2, #0xb]
    // 0x93a09c: ldur            x3, [fp, #-8]
    // 0x93a0a0: LoadField: r1 = r3->field_b
    //     0x93a0a0: ldur            w1, [x3, #0xb]
    // 0x93a0a4: DecompressPointer r1
    //     0x93a0a4: add             x1, x1, HEAP, lsl #32
    // 0x93a0a8: cmp             w1, NULL
    // 0x93a0ac: b.eq            #0x93a354
    // 0x93a0b0: LoadField: r4 = r1->field_f
    //     0x93a0b0: ldur            w4, [x1, #0xf]
    // 0x93a0b4: DecompressPointer r4
    //     0x93a0b4: add             x4, x4, HEAP, lsl #32
    // 0x93a0b8: LoadField: r1 = r4->field_13
    //     0x93a0b8: ldur            w1, [x4, #0x13]
    // 0x93a0bc: DecompressPointer r1
    //     0x93a0bc: add             x1, x1, HEAP, lsl #32
    // 0x93a0c0: cmp             w1, NULL
    // 0x93a0c4: b.ne            #0x93a0ec
    // 0x93a0c8: r1 = <PriceRangeFilter>
    //     0x93a0c8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb08] TypeArguments: <PriceRangeFilter>
    //     0x93a0cc: ldr             x1, [x1, #0xb08]
    // 0x93a0d0: r0 = AllocateGrowableArray()
    //     0x93a0d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x93a0d4: mov             x1, x0
    // 0x93a0d8: ldur            x0, [fp, #-0x10]
    // 0x93a0dc: StoreField: r1->field_f = r0
    //     0x93a0dc: stur            w0, [x1, #0xf]
    // 0x93a0e0: StoreField: r1->field_b = rZR
    //     0x93a0e0: stur            wzr, [x1, #0xb]
    // 0x93a0e4: mov             x3, x1
    // 0x93a0e8: b               #0x93a0f0
    // 0x93a0ec: mov             x3, x1
    // 0x93a0f0: stur            x3, [fp, #-0x38]
    // 0x93a0f4: LoadField: r4 = r3->field_7
    //     0x93a0f4: ldur            w4, [x3, #7]
    // 0x93a0f8: DecompressPointer r4
    //     0x93a0f8: add             x4, x4, HEAP, lsl #32
    // 0x93a0fc: stur            x4, [fp, #-0x30]
    // 0x93a100: LoadField: r0 = r3->field_b
    //     0x93a100: ldur            w0, [x3, #0xb]
    // 0x93a104: r5 = LoadInt32Instr(r0)
    //     0x93a104: sbfx            x5, x0, #1, #0x1f
    // 0x93a108: stur            x5, [fp, #-0x28]
    // 0x93a10c: r0 = 0
    //     0x93a10c: movz            x0, #0
    // 0x93a110: ldur            x7, [fp, #-8]
    // 0x93a114: ldur            x6, [fp, #-0x18]
    // 0x93a118: CheckStackOverflow
    //     0x93a118: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93a11c: cmp             SP, x16
    //     0x93a120: b.ls            #0x93a358
    // 0x93a124: LoadField: r1 = r3->field_b
    //     0x93a124: ldur            w1, [x3, #0xb]
    // 0x93a128: r2 = LoadInt32Instr(r1)
    //     0x93a128: sbfx            x2, x1, #1, #0x1f
    // 0x93a12c: cmp             x5, x2
    // 0x93a130: b.ne            #0x93a310
    // 0x93a134: cmp             x0, x2
    // 0x93a138: b.ge            #0x93a2bc
    // 0x93a13c: LoadField: r1 = r3->field_f
    //     0x93a13c: ldur            w1, [x3, #0xf]
    // 0x93a140: DecompressPointer r1
    //     0x93a140: add             x1, x1, HEAP, lsl #32
    // 0x93a144: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x93a144: add             x16, x1, x0, lsl #2
    //     0x93a148: ldur            w8, [x16, #0xf]
    // 0x93a14c: DecompressPointer r8
    //     0x93a14c: add             x8, x8, HEAP, lsl #32
    // 0x93a150: stur            x8, [fp, #-0x10]
    // 0x93a154: add             x9, x0, #1
    // 0x93a158: stur            x9, [fp, #-0x20]
    // 0x93a15c: cmp             w8, NULL
    // 0x93a160: b.ne            #0x93a194
    // 0x93a164: mov             x0, x8
    // 0x93a168: mov             x2, x4
    // 0x93a16c: r1 = Null
    //     0x93a16c: mov             x1, NULL
    // 0x93a170: cmp             w2, NULL
    // 0x93a174: b.eq            #0x93a194
    // 0x93a178: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93a178: ldur            w4, [x2, #0x17]
    // 0x93a17c: DecompressPointer r4
    //     0x93a17c: add             x4, x4, HEAP, lsl #32
    // 0x93a180: r8 = X0
    //     0x93a180: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93a184: LoadField: r9 = r4->field_7
    //     0x93a184: ldur            x9, [x4, #7]
    // 0x93a188: r3 = Null
    //     0x93a188: add             x3, PP, #0x58, lsl #12  ; [pp+0x589c0] Null
    //     0x93a18c: ldr             x3, [x3, #0x9c0]
    // 0x93a190: blr             x9
    // 0x93a194: ldur            x0, [fp, #-0x10]
    // 0x93a198: LoadField: r3 = r0->field_7
    //     0x93a198: ldur            w3, [x0, #7]
    // 0x93a19c: DecompressPointer r3
    //     0x93a19c: add             x3, x3, HEAP, lsl #32
    // 0x93a1a0: stur            x3, [fp, #-0x40]
    // 0x93a1a4: r1 = Null
    //     0x93a1a4: mov             x1, NULL
    // 0x93a1a8: r2 = 6
    //     0x93a1a8: movz            x2, #0x6
    // 0x93a1ac: r0 = AllocateArray()
    //     0x93a1ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93a1b0: mov             x1, x0
    // 0x93a1b4: ldur            x0, [fp, #-0x40]
    // 0x93a1b8: StoreField: r1->field_f = r0
    //     0x93a1b8: stur            w0, [x1, #0xf]
    // 0x93a1bc: r16 = " - "
    //     0x93a1bc: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0x93a1c0: ldr             x16, [x16, #0xc08]
    // 0x93a1c4: StoreField: r1->field_13 = r16
    //     0x93a1c4: stur            w16, [x1, #0x13]
    // 0x93a1c8: ldur            x2, [fp, #-0x10]
    // 0x93a1cc: LoadField: r3 = r2->field_b
    //     0x93a1cc: ldur            w3, [x2, #0xb]
    // 0x93a1d0: DecompressPointer r3
    //     0x93a1d0: add             x3, x3, HEAP, lsl #32
    // 0x93a1d4: cmp             w3, NULL
    // 0x93a1d8: b.ne            #0x93a1e4
    // 0x93a1dc: r3 = "above"
    //     0x93a1dc: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0x93a1e0: ldr             x3, [x3, #0xc10]
    // 0x93a1e4: ldur            x0, [fp, #-0x18]
    // 0x93a1e8: ArrayStore: r1[0] = r3  ; List_4
    //     0x93a1e8: stur            w3, [x1, #0x17]
    // 0x93a1ec: str             x1, [SP]
    // 0x93a1f0: r0 = _interpolate()
    //     0x93a1f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x93a1f4: mov             x2, x0
    // 0x93a1f8: ldur            x0, [fp, #-0x18]
    // 0x93a1fc: stur            x2, [fp, #-0x40]
    // 0x93a200: LoadField: r1 = r0->field_b
    //     0x93a200: ldur            w1, [x0, #0xb]
    // 0x93a204: LoadField: r3 = r0->field_f
    //     0x93a204: ldur            w3, [x0, #0xf]
    // 0x93a208: DecompressPointer r3
    //     0x93a208: add             x3, x3, HEAP, lsl #32
    // 0x93a20c: LoadField: r4 = r3->field_b
    //     0x93a20c: ldur            w4, [x3, #0xb]
    // 0x93a210: r3 = LoadInt32Instr(r1)
    //     0x93a210: sbfx            x3, x1, #1, #0x1f
    // 0x93a214: stur            x3, [fp, #-0x48]
    // 0x93a218: r1 = LoadInt32Instr(r4)
    //     0x93a218: sbfx            x1, x4, #1, #0x1f
    // 0x93a21c: cmp             x3, x1
    // 0x93a220: b.ne            #0x93a22c
    // 0x93a224: mov             x1, x0
    // 0x93a228: r0 = _growToNextCapacity()
    //     0x93a228: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93a22c: ldur            x4, [fp, #-8]
    // 0x93a230: ldur            x2, [fp, #-0x18]
    // 0x93a234: ldur            x3, [fp, #-0x48]
    // 0x93a238: add             x0, x3, #1
    // 0x93a23c: lsl             x1, x0, #1
    // 0x93a240: StoreField: r2->field_b = r1
    //     0x93a240: stur            w1, [x2, #0xb]
    // 0x93a244: LoadField: r1 = r2->field_f
    //     0x93a244: ldur            w1, [x2, #0xf]
    // 0x93a248: DecompressPointer r1
    //     0x93a248: add             x1, x1, HEAP, lsl #32
    // 0x93a24c: ldur            x0, [fp, #-0x40]
    // 0x93a250: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93a250: add             x25, x1, x3, lsl #2
    //     0x93a254: add             x25, x25, #0xf
    //     0x93a258: str             w0, [x25]
    //     0x93a25c: tbz             w0, #0, #0x93a278
    //     0x93a260: ldurb           w16, [x1, #-1]
    //     0x93a264: ldurb           w17, [x0, #-1]
    //     0x93a268: and             x16, x17, x16, lsr #2
    //     0x93a26c: tst             x16, HEAP, lsr #32
    //     0x93a270: b.eq            #0x93a278
    //     0x93a274: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93a278: LoadField: r1 = r4->field_27
    //     0x93a278: ldur            w1, [x4, #0x27]
    // 0x93a27c: DecompressPointer r1
    //     0x93a27c: add             x1, x1, HEAP, lsl #32
    // 0x93a280: stur            x1, [fp, #-0x40]
    // 0x93a284: ldur            x16, [fp, #-0x10]
    // 0x93a288: str             x16, [SP]
    // 0x93a28c: r0 = hashCode()
    //     0x93a28c: bl              #0x153c88c  ; [package:customer_app/app/data/models/collection/filter_response.dart] PriceRangeFilter::hashCode
    // 0x93a290: r3 = LoadInt32Instr(r0)
    //     0x93a290: sbfx            x3, x0, #1, #0x1f
    //     0x93a294: tbz             w0, #0, #0x93a29c
    //     0x93a298: ldur            x3, [x0, #7]
    // 0x93a29c: ldur            x1, [fp, #-0x40]
    // 0x93a2a0: ldur            x2, [fp, #-0x10]
    // 0x93a2a4: r0 = _add()
    //     0x93a2a4: bl              #0x69d2b0  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x93a2a8: ldur            x0, [fp, #-0x20]
    // 0x93a2ac: ldur            x3, [fp, #-0x38]
    // 0x93a2b0: ldur            x4, [fp, #-0x30]
    // 0x93a2b4: ldur            x5, [fp, #-0x28]
    // 0x93a2b8: b               #0x93a110
    // 0x93a2bc: mov             x1, x7
    // 0x93a2c0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x93a2c0: ldur            w2, [x1, #0x17]
    // 0x93a2c4: DecompressPointer r2
    //     0x93a2c4: add             x2, x2, HEAP, lsl #32
    // 0x93a2c8: LoadField: r0 = r1->field_27
    //     0x93a2c8: ldur            w0, [x1, #0x27]
    // 0x93a2cc: DecompressPointer r0
    //     0x93a2cc: add             x0, x0, HEAP, lsl #32
    // 0x93a2d0: StoreField: r2->field_13 = r0
    //     0x93a2d0: stur            w0, [x2, #0x13]
    //     0x93a2d4: ldurb           w16, [x2, #-1]
    //     0x93a2d8: ldurb           w17, [x0, #-1]
    //     0x93a2dc: and             x16, x17, x16, lsr #2
    //     0x93a2e0: tst             x16, HEAP, lsr #32
    //     0x93a2e4: b.eq            #0x93a2ec
    //     0x93a2e8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93a2ec: LoadField: r0 = r1->field_13
    //     0x93a2ec: ldur            w0, [x1, #0x13]
    // 0x93a2f0: DecompressPointer r0
    //     0x93a2f0: add             x0, x0, HEAP, lsl #32
    // 0x93a2f4: mov             x1, x0
    // 0x93a2f8: ldur            x2, [fp, #-0x18]
    // 0x93a2fc: r0 = addAll()
    //     0x93a2fc: bl              #0x715914  ; [dart:core] _GrowableList::addAll
    // 0x93a300: r0 = Null
    //     0x93a300: mov             x0, NULL
    // 0x93a304: LeaveFrame
    //     0x93a304: mov             SP, fp
    //     0x93a308: ldp             fp, lr, [SP], #0x10
    // 0x93a30c: ret
    //     0x93a30c: ret             
    // 0x93a310: mov             x0, x3
    // 0x93a314: r0 = ConcurrentModificationError()
    //     0x93a314: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x93a318: mov             x1, x0
    // 0x93a31c: ldur            x0, [fp, #-0x38]
    // 0x93a320: StoreField: r1->field_b = r0
    //     0x93a320: stur            w0, [x1, #0xb]
    // 0x93a324: mov             x0, x1
    // 0x93a328: r0 = Throw()
    //     0x93a328: bl              #0x16f5420  ; ThrowStub
    // 0x93a32c: brk             #0
    // 0x93a330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93a330: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93a334: b               #0x939d70
    // 0x93a338: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a338: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a33c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a33c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a340: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a344: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a348: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a34c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a34c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a350: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a350: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93a354: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93a358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93a358: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93a35c: b               #0x93a124
  }
  _ build(/* No info */) {
    // ** addr: 0xad9b44, size: 0x11b8
    // 0xad9b44: EnterFrame
    //     0xad9b44: stp             fp, lr, [SP, #-0x10]!
    //     0xad9b48: mov             fp, SP
    // 0xad9b4c: AllocStack(0x68)
    //     0xad9b4c: sub             SP, SP, #0x68
    // 0xad9b50: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xad9b50: mov             x0, x1
    //     0xad9b54: stur            x1, [fp, #-8]
    //     0xad9b58: mov             x1, x2
    //     0xad9b5c: stur            x2, [fp, #-0x10]
    // 0xad9b60: CheckStackOverflow
    //     0xad9b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9b64: cmp             SP, x16
    //     0xad9b68: b.ls            #0xadaccc
    // 0xad9b6c: r1 = 2
    //     0xad9b6c: movz            x1, #0x2
    // 0xad9b70: r0 = AllocateContext()
    //     0xad9b70: bl              #0x16f6108  ; AllocateContextStub
    // 0xad9b74: mov             x1, x0
    // 0xad9b78: ldur            x0, [fp, #-8]
    // 0xad9b7c: stur            x1, [fp, #-0x38]
    // 0xad9b80: StoreField: r1->field_f = r0
    //     0xad9b80: stur            w0, [x1, #0xf]
    // 0xad9b84: ldur            x2, [fp, #-0x10]
    // 0xad9b88: StoreField: r1->field_13 = r2
    //     0xad9b88: stur            w2, [x1, #0x13]
    // 0xad9b8c: LoadField: r3 = r0->field_2f
    //     0xad9b8c: ldur            w3, [x0, #0x2f]
    // 0xad9b90: DecompressPointer r3
    //     0xad9b90: add             x3, x3, HEAP, lsl #32
    // 0xad9b94: tbnz            w3, #4, #0xad9ba0
    // 0xad9b98: r4 = Instance_Brightness
    //     0xad9b98: ldr             x4, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xad9b9c: b               #0xad9ba4
    // 0xad9ba0: r4 = Instance_Brightness
    //     0xad9ba0: ldr             x4, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xad9ba4: stur            x4, [fp, #-0x30]
    // 0xad9ba8: tbnz            w3, #4, #0xad9bb4
    // 0xad9bac: r5 = Instance_Brightness
    //     0xad9bac: ldr             x5, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xad9bb0: b               #0xad9bb8
    // 0xad9bb4: r5 = Instance_Brightness
    //     0xad9bb4: ldr             x5, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xad9bb8: stur            x5, [fp, #-0x28]
    // 0xad9bbc: tbnz            w3, #4, #0xad9bc8
    // 0xad9bc0: r6 = Instance_Color
    //     0xad9bc0: ldr             x6, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xad9bc4: b               #0xad9bcc
    // 0xad9bc8: r6 = Instance_Color
    //     0xad9bc8: ldr             x6, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xad9bcc: stur            x6, [fp, #-0x20]
    // 0xad9bd0: tbnz            w3, #4, #0xad9bdc
    // 0xad9bd4: r3 = Instance_Brightness
    //     0xad9bd4: ldr             x3, [PP, #0x5468]  ; [pp+0x5468] Obj!Brightness@d76341
    // 0xad9bd8: b               #0xad9be0
    // 0xad9bdc: r3 = Instance_Brightness
    //     0xad9bdc: ldr             x3, [PP, #0x5470]  ; [pp+0x5470] Obj!Brightness@d76361
    // 0xad9be0: stur            x3, [fp, #-0x18]
    // 0xad9be4: r0 = SystemUiOverlayStyle()
    //     0xad9be4: bl              #0x6e633c  ; AllocateSystemUiOverlayStyleStub -> SystemUiOverlayStyle (size=0x28)
    // 0xad9be8: mov             x1, x0
    // 0xad9bec: ldur            x0, [fp, #-0x20]
    // 0xad9bf0: stur            x1, [fp, #-0x40]
    // 0xad9bf4: StoreField: r1->field_7 = r0
    //     0xad9bf4: stur            w0, [x1, #7]
    // 0xad9bf8: r0 = Instance_Color
    //     0xad9bf8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xad9bfc: ldr             x0, [x0, #0xf88]
    // 0xad9c00: StoreField: r1->field_b = r0
    //     0xad9c00: stur            w0, [x1, #0xb]
    // 0xad9c04: ldur            x2, [fp, #-0x18]
    // 0xad9c08: StoreField: r1->field_f = r2
    //     0xad9c08: stur            w2, [x1, #0xf]
    // 0xad9c0c: r2 = false
    //     0xad9c0c: add             x2, NULL, #0x30  ; false
    // 0xad9c10: StoreField: r1->field_13 = r2
    //     0xad9c10: stur            w2, [x1, #0x13]
    // 0xad9c14: ArrayStore: r1[0] = r0  ; List_4
    //     0xad9c14: stur            w0, [x1, #0x17]
    // 0xad9c18: ldur            x0, [fp, #-0x28]
    // 0xad9c1c: StoreField: r1->field_1b = r0
    //     0xad9c1c: stur            w0, [x1, #0x1b]
    // 0xad9c20: ldur            x0, [fp, #-0x30]
    // 0xad9c24: StoreField: r1->field_1f = r0
    //     0xad9c24: stur            w0, [x1, #0x1f]
    // 0xad9c28: StoreField: r1->field_23 = r2
    //     0xad9c28: stur            w2, [x1, #0x23]
    // 0xad9c2c: r0 = InkWell()
    //     0xad9c2c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xad9c30: mov             x3, x0
    // 0xad9c34: r0 = Instance_Icon
    //     0xad9c34: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xad9c38: ldr             x0, [x0, #0x2b8]
    // 0xad9c3c: stur            x3, [fp, #-0x18]
    // 0xad9c40: StoreField: r3->field_b = r0
    //     0xad9c40: stur            w0, [x3, #0xb]
    // 0xad9c44: r1 = Function '<anonymous closure>':.
    //     0xad9c44: add             x1, PP, #0x58, lsl #12  ; [pp+0x58948] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xad9c48: ldr             x1, [x1, #0x948]
    // 0xad9c4c: r2 = Null
    //     0xad9c4c: mov             x2, NULL
    // 0xad9c50: r0 = AllocateClosure()
    //     0xad9c50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xad9c54: mov             x1, x0
    // 0xad9c58: ldur            x0, [fp, #-0x18]
    // 0xad9c5c: StoreField: r0->field_f = r1
    //     0xad9c5c: stur            w1, [x0, #0xf]
    // 0xad9c60: r2 = true
    //     0xad9c60: add             x2, NULL, #0x20  ; true
    // 0xad9c64: StoreField: r0->field_43 = r2
    //     0xad9c64: stur            w2, [x0, #0x43]
    // 0xad9c68: r3 = Instance_BoxShape
    //     0xad9c68: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xad9c6c: ldr             x3, [x3, #0x80]
    // 0xad9c70: StoreField: r0->field_47 = r3
    //     0xad9c70: stur            w3, [x0, #0x47]
    // 0xad9c74: StoreField: r0->field_6f = r2
    //     0xad9c74: stur            w2, [x0, #0x6f]
    // 0xad9c78: r4 = false
    //     0xad9c78: add             x4, NULL, #0x30  ; false
    // 0xad9c7c: StoreField: r0->field_73 = r4
    //     0xad9c7c: stur            w4, [x0, #0x73]
    // 0xad9c80: StoreField: r0->field_83 = r2
    //     0xad9c80: stur            w2, [x0, #0x83]
    // 0xad9c84: StoreField: r0->field_7b = r4
    //     0xad9c84: stur            w4, [x0, #0x7b]
    // 0xad9c88: ldur            x1, [fp, #-0x10]
    // 0xad9c8c: r0 = of()
    //     0xad9c8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xad9c90: LoadField: r1 = r0->field_87
    //     0xad9c90: ldur            w1, [x0, #0x87]
    // 0xad9c94: DecompressPointer r1
    //     0xad9c94: add             x1, x1, HEAP, lsl #32
    // 0xad9c98: LoadField: r0 = r1->field_7
    //     0xad9c98: ldur            w0, [x1, #7]
    // 0xad9c9c: DecompressPointer r0
    //     0xad9c9c: add             x0, x0, HEAP, lsl #32
    // 0xad9ca0: r16 = 16.000000
    //     0xad9ca0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xad9ca4: ldr             x16, [x16, #0x188]
    // 0xad9ca8: r30 = Instance_Color
    //     0xad9ca8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xad9cac: stp             lr, x16, [SP]
    // 0xad9cb0: mov             x1, x0
    // 0xad9cb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xad9cb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xad9cb8: ldr             x4, [x4, #0xaa0]
    // 0xad9cbc: r0 = copyWith()
    //     0xad9cbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad9cc0: stur            x0, [fp, #-0x10]
    // 0xad9cc4: r0 = Text()
    //     0xad9cc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xad9cc8: mov             x3, x0
    // 0xad9ccc: r0 = "Filters"
    //     0xad9ccc: add             x0, PP, #0x40, lsl #12  ; [pp+0x40bb0] "Filters"
    //     0xad9cd0: ldr             x0, [x0, #0xbb0]
    // 0xad9cd4: stur            x3, [fp, #-0x20]
    // 0xad9cd8: StoreField: r3->field_b = r0
    //     0xad9cd8: stur            w0, [x3, #0xb]
    // 0xad9cdc: ldur            x0, [fp, #-0x10]
    // 0xad9ce0: StoreField: r3->field_13 = r0
    //     0xad9ce0: stur            w0, [x3, #0x13]
    // 0xad9ce4: r1 = Null
    //     0xad9ce4: mov             x1, NULL
    // 0xad9ce8: r2 = 4
    //     0xad9ce8: movz            x2, #0x4
    // 0xad9cec: r0 = AllocateArray()
    //     0xad9cec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xad9cf0: mov             x2, x0
    // 0xad9cf4: ldur            x0, [fp, #-0x18]
    // 0xad9cf8: stur            x2, [fp, #-0x10]
    // 0xad9cfc: StoreField: r2->field_f = r0
    //     0xad9cfc: stur            w0, [x2, #0xf]
    // 0xad9d00: ldur            x0, [fp, #-0x20]
    // 0xad9d04: StoreField: r2->field_13 = r0
    //     0xad9d04: stur            w0, [x2, #0x13]
    // 0xad9d08: r1 = <Widget>
    //     0xad9d08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xad9d0c: r0 = AllocateGrowableArray()
    //     0xad9d0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xad9d10: mov             x1, x0
    // 0xad9d14: ldur            x0, [fp, #-0x10]
    // 0xad9d18: stur            x1, [fp, #-0x18]
    // 0xad9d1c: StoreField: r1->field_f = r0
    //     0xad9d1c: stur            w0, [x1, #0xf]
    // 0xad9d20: r2 = 4
    //     0xad9d20: movz            x2, #0x4
    // 0xad9d24: StoreField: r1->field_b = r2
    //     0xad9d24: stur            w2, [x1, #0xb]
    // 0xad9d28: r0 = Row()
    //     0xad9d28: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xad9d2c: mov             x1, x0
    // 0xad9d30: r0 = Instance_Axis
    //     0xad9d30: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xad9d34: stur            x1, [fp, #-0x10]
    // 0xad9d38: StoreField: r1->field_f = r0
    //     0xad9d38: stur            w0, [x1, #0xf]
    // 0xad9d3c: r2 = Instance_MainAxisAlignment
    //     0xad9d3c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xad9d40: ldr             x2, [x2, #0xd10]
    // 0xad9d44: StoreField: r1->field_13 = r2
    //     0xad9d44: stur            w2, [x1, #0x13]
    // 0xad9d48: r2 = Instance_MainAxisSize
    //     0xad9d48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xad9d4c: ldr             x2, [x2, #0xa10]
    // 0xad9d50: ArrayStore: r1[0] = r2  ; List_4
    //     0xad9d50: stur            w2, [x1, #0x17]
    // 0xad9d54: r3 = Instance_CrossAxisAlignment
    //     0xad9d54: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xad9d58: ldr             x3, [x3, #0xa18]
    // 0xad9d5c: StoreField: r1->field_1b = r3
    //     0xad9d5c: stur            w3, [x1, #0x1b]
    // 0xad9d60: r4 = Instance_VerticalDirection
    //     0xad9d60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xad9d64: ldr             x4, [x4, #0xa20]
    // 0xad9d68: StoreField: r1->field_23 = r4
    //     0xad9d68: stur            w4, [x1, #0x23]
    // 0xad9d6c: r5 = Instance_Clip
    //     0xad9d6c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xad9d70: ldr             x5, [x5, #0x38]
    // 0xad9d74: StoreField: r1->field_2b = r5
    //     0xad9d74: stur            w5, [x1, #0x2b]
    // 0xad9d78: StoreField: r1->field_2f = rZR
    //     0xad9d78: stur            xzr, [x1, #0x2f]
    // 0xad9d7c: ldur            x6, [fp, #-0x18]
    // 0xad9d80: StoreField: r1->field_b = r6
    //     0xad9d80: stur            w6, [x1, #0xb]
    // 0xad9d84: r0 = SizedBox()
    //     0xad9d84: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xad9d88: mov             x2, x0
    // 0xad9d8c: r0 = 100.000000
    //     0xad9d8c: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xad9d90: stur            x2, [fp, #-0x18]
    // 0xad9d94: StoreField: r2->field_f = r0
    //     0xad9d94: stur            w0, [x2, #0xf]
    // 0xad9d98: ldur            x0, [fp, #-0x10]
    // 0xad9d9c: StoreField: r2->field_b = r0
    //     0xad9d9c: stur            w0, [x2, #0xb]
    // 0xad9da0: ldur            x0, [fp, #-0x38]
    // 0xad9da4: LoadField: r1 = r0->field_13
    //     0xad9da4: ldur            w1, [x0, #0x13]
    // 0xad9da8: DecompressPointer r1
    //     0xad9da8: add             x1, x1, HEAP, lsl #32
    // 0xad9dac: r0 = of()
    //     0xad9dac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xad9db0: LoadField: r1 = r0->field_5b
    //     0xad9db0: ldur            w1, [x0, #0x5b]
    // 0xad9db4: DecompressPointer r1
    //     0xad9db4: add             x1, x1, HEAP, lsl #32
    // 0xad9db8: r0 = LoadClassIdInstr(r1)
    //     0xad9db8: ldur            x0, [x1, #-1]
    //     0xad9dbc: ubfx            x0, x0, #0xc, #0x14
    // 0xad9dc0: d0 = 0.030000
    //     0xad9dc0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xad9dc4: ldr             d0, [x17, #0x238]
    // 0xad9dc8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xad9dc8: sub             lr, x0, #0xffa
    //     0xad9dcc: ldr             lr, [x21, lr, lsl #3]
    //     0xad9dd0: blr             lr
    // 0xad9dd4: stur            x0, [fp, #-0x10]
    // 0xad9dd8: r0 = Radius()
    //     0xad9dd8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xad9ddc: d0 = 20.000000
    //     0xad9ddc: fmov            d0, #20.00000000
    // 0xad9de0: stur            x0, [fp, #-0x20]
    // 0xad9de4: StoreField: r0->field_7 = d0
    //     0xad9de4: stur            d0, [x0, #7]
    // 0xad9de8: StoreField: r0->field_f = d0
    //     0xad9de8: stur            d0, [x0, #0xf]
    // 0xad9dec: r0 = BorderRadius()
    //     0xad9dec: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xad9df0: mov             x1, x0
    // 0xad9df4: ldur            x0, [fp, #-0x20]
    // 0xad9df8: stur            x1, [fp, #-0x28]
    // 0xad9dfc: StoreField: r1->field_7 = r0
    //     0xad9dfc: stur            w0, [x1, #7]
    // 0xad9e00: StoreField: r1->field_b = r0
    //     0xad9e00: stur            w0, [x1, #0xb]
    // 0xad9e04: StoreField: r1->field_f = r0
    //     0xad9e04: stur            w0, [x1, #0xf]
    // 0xad9e08: StoreField: r1->field_13 = r0
    //     0xad9e08: stur            w0, [x1, #0x13]
    // 0xad9e0c: r0 = BoxDecoration()
    //     0xad9e0c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xad9e10: mov             x2, x0
    // 0xad9e14: ldur            x0, [fp, #-0x10]
    // 0xad9e18: stur            x2, [fp, #-0x20]
    // 0xad9e1c: StoreField: r2->field_7 = r0
    //     0xad9e1c: stur            w0, [x2, #7]
    // 0xad9e20: ldur            x0, [fp, #-0x28]
    // 0xad9e24: StoreField: r2->field_13 = r0
    //     0xad9e24: stur            w0, [x2, #0x13]
    // 0xad9e28: r0 = Instance_BoxShape
    //     0xad9e28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xad9e2c: ldr             x0, [x0, #0x80]
    // 0xad9e30: StoreField: r2->field_23 = r0
    //     0xad9e30: stur            w0, [x2, #0x23]
    // 0xad9e34: r1 = "clear all"
    //     0xad9e34: add             x1, PP, #0x56, lsl #12  ; [pp+0x56528] "clear all"
    //     0xad9e38: ldr             x1, [x1, #0x528]
    // 0xad9e3c: r0 = capitalizeFirstWord()
    //     0xad9e3c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xad9e40: ldur            x2, [fp, #-0x38]
    // 0xad9e44: stur            x0, [fp, #-0x10]
    // 0xad9e48: LoadField: r1 = r2->field_13
    //     0xad9e48: ldur            w1, [x2, #0x13]
    // 0xad9e4c: DecompressPointer r1
    //     0xad9e4c: add             x1, x1, HEAP, lsl #32
    // 0xad9e50: r0 = of()
    //     0xad9e50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xad9e54: LoadField: r1 = r0->field_87
    //     0xad9e54: ldur            w1, [x0, #0x87]
    // 0xad9e58: DecompressPointer r1
    //     0xad9e58: add             x1, x1, HEAP, lsl #32
    // 0xad9e5c: LoadField: r0 = r1->field_7
    //     0xad9e5c: ldur            w0, [x1, #7]
    // 0xad9e60: DecompressPointer r0
    //     0xad9e60: add             x0, x0, HEAP, lsl #32
    // 0xad9e64: ldur            x2, [fp, #-0x38]
    // 0xad9e68: stur            x0, [fp, #-0x28]
    // 0xad9e6c: LoadField: r1 = r2->field_13
    //     0xad9e6c: ldur            w1, [x2, #0x13]
    // 0xad9e70: DecompressPointer r1
    //     0xad9e70: add             x1, x1, HEAP, lsl #32
    // 0xad9e74: r0 = of()
    //     0xad9e74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xad9e78: LoadField: r1 = r0->field_5b
    //     0xad9e78: ldur            w1, [x0, #0x5b]
    // 0xad9e7c: DecompressPointer r1
    //     0xad9e7c: add             x1, x1, HEAP, lsl #32
    // 0xad9e80: r0 = LoadClassIdInstr(r1)
    //     0xad9e80: ldur            x0, [x1, #-1]
    //     0xad9e84: ubfx            x0, x0, #0xc, #0x14
    // 0xad9e88: d0 = 0.700000
    //     0xad9e88: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xad9e8c: ldr             d0, [x17, #0xf48]
    // 0xad9e90: r0 = GDT[cid_x0 + -0xffa]()
    //     0xad9e90: sub             lr, x0, #0xffa
    //     0xad9e94: ldr             lr, [x21, lr, lsl #3]
    //     0xad9e98: blr             lr
    // 0xad9e9c: r16 = 14.000000
    //     0xad9e9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xad9ea0: ldr             x16, [x16, #0x1d8]
    // 0xad9ea4: stp             x0, x16, [SP]
    // 0xad9ea8: ldur            x1, [fp, #-0x28]
    // 0xad9eac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xad9eac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xad9eb0: ldr             x4, [x4, #0xaa0]
    // 0xad9eb4: r0 = copyWith()
    //     0xad9eb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xad9eb8: stur            x0, [fp, #-0x28]
    // 0xad9ebc: r0 = Text()
    //     0xad9ebc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xad9ec0: mov             x1, x0
    // 0xad9ec4: ldur            x0, [fp, #-0x10]
    // 0xad9ec8: stur            x1, [fp, #-0x30]
    // 0xad9ecc: StoreField: r1->field_b = r0
    //     0xad9ecc: stur            w0, [x1, #0xb]
    // 0xad9ed0: ldur            x0, [fp, #-0x28]
    // 0xad9ed4: StoreField: r1->field_13 = r0
    //     0xad9ed4: stur            w0, [x1, #0x13]
    // 0xad9ed8: r0 = Center()
    //     0xad9ed8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xad9edc: mov             x1, x0
    // 0xad9ee0: r0 = Instance_Alignment
    //     0xad9ee0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xad9ee4: ldr             x0, [x0, #0xb10]
    // 0xad9ee8: stur            x1, [fp, #-0x10]
    // 0xad9eec: StoreField: r1->field_f = r0
    //     0xad9eec: stur            w0, [x1, #0xf]
    // 0xad9ef0: ldur            x0, [fp, #-0x30]
    // 0xad9ef4: StoreField: r1->field_b = r0
    //     0xad9ef4: stur            w0, [x1, #0xb]
    // 0xad9ef8: r0 = Container()
    //     0xad9ef8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xad9efc: stur            x0, [fp, #-0x28]
    // 0xad9f00: r16 = 28.000000
    //     0xad9f00: add             x16, PP, #0x52, lsl #12  ; [pp+0x52600] 28
    //     0xad9f04: ldr             x16, [x16, #0x600]
    // 0xad9f08: r30 = Instance_EdgeInsets
    //     0xad9f08: add             lr, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xad9f0c: ldr             lr, [lr, #0x50]
    // 0xad9f10: stp             lr, x16, [SP, #0x10]
    // 0xad9f14: ldur            x16, [fp, #-0x20]
    // 0xad9f18: ldur            lr, [fp, #-0x10]
    // 0xad9f1c: stp             lr, x16, [SP]
    // 0xad9f20: mov             x1, x0
    // 0xad9f24: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, padding, 0x2, null]
    //     0xad9f24: add             x4, PP, #0x56, lsl #12  ; [pp+0x56530] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xad9f28: ldr             x4, [x4, #0x530]
    // 0xad9f2c: r0 = Container()
    //     0xad9f2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xad9f30: r0 = InkWell()
    //     0xad9f30: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xad9f34: mov             x3, x0
    // 0xad9f38: ldur            x0, [fp, #-0x28]
    // 0xad9f3c: stur            x3, [fp, #-0x10]
    // 0xad9f40: StoreField: r3->field_b = r0
    //     0xad9f40: stur            w0, [x3, #0xb]
    // 0xad9f44: ldur            x2, [fp, #-0x38]
    // 0xad9f48: r1 = Function '<anonymous closure>':.
    //     0xad9f48: add             x1, PP, #0x58, lsl #12  ; [pp+0x58950] AnonymousClosure: (0xadc3f8), in [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xad9b44)
    //     0xad9f4c: ldr             x1, [x1, #0x950]
    // 0xad9f50: r0 = AllocateClosure()
    //     0xad9f50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xad9f54: mov             x1, x0
    // 0xad9f58: ldur            x0, [fp, #-0x10]
    // 0xad9f5c: StoreField: r0->field_f = r1
    //     0xad9f5c: stur            w1, [x0, #0xf]
    // 0xad9f60: r3 = true
    //     0xad9f60: add             x3, NULL, #0x20  ; true
    // 0xad9f64: StoreField: r0->field_43 = r3
    //     0xad9f64: stur            w3, [x0, #0x43]
    // 0xad9f68: r1 = Instance_BoxShape
    //     0xad9f68: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xad9f6c: ldr             x1, [x1, #0x80]
    // 0xad9f70: StoreField: r0->field_47 = r1
    //     0xad9f70: stur            w1, [x0, #0x47]
    // 0xad9f74: StoreField: r0->field_6f = r3
    //     0xad9f74: stur            w3, [x0, #0x6f]
    // 0xad9f78: r4 = false
    //     0xad9f78: add             x4, NULL, #0x30  ; false
    // 0xad9f7c: StoreField: r0->field_73 = r4
    //     0xad9f7c: stur            w4, [x0, #0x73]
    // 0xad9f80: StoreField: r0->field_83 = r3
    //     0xad9f80: stur            w3, [x0, #0x83]
    // 0xad9f84: StoreField: r0->field_7b = r4
    //     0xad9f84: stur            w4, [x0, #0x7b]
    // 0xad9f88: r1 = Null
    //     0xad9f88: mov             x1, NULL
    // 0xad9f8c: r2 = 4
    //     0xad9f8c: movz            x2, #0x4
    // 0xad9f90: r0 = AllocateArray()
    //     0xad9f90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xad9f94: mov             x2, x0
    // 0xad9f98: ldur            x0, [fp, #-0x18]
    // 0xad9f9c: stur            x2, [fp, #-0x20]
    // 0xad9fa0: StoreField: r2->field_f = r0
    //     0xad9fa0: stur            w0, [x2, #0xf]
    // 0xad9fa4: ldur            x0, [fp, #-0x10]
    // 0xad9fa8: StoreField: r2->field_13 = r0
    //     0xad9fa8: stur            w0, [x2, #0x13]
    // 0xad9fac: r1 = <Widget>
    //     0xad9fac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xad9fb0: r0 = AllocateGrowableArray()
    //     0xad9fb0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xad9fb4: mov             x1, x0
    // 0xad9fb8: ldur            x0, [fp, #-0x20]
    // 0xad9fbc: stur            x1, [fp, #-0x10]
    // 0xad9fc0: StoreField: r1->field_f = r0
    //     0xad9fc0: stur            w0, [x1, #0xf]
    // 0xad9fc4: r0 = 4
    //     0xad9fc4: movz            x0, #0x4
    // 0xad9fc8: StoreField: r1->field_b = r0
    //     0xad9fc8: stur            w0, [x1, #0xb]
    // 0xad9fcc: r0 = Row()
    //     0xad9fcc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xad9fd0: mov             x1, x0
    // 0xad9fd4: r0 = Instance_Axis
    //     0xad9fd4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xad9fd8: stur            x1, [fp, #-0x18]
    // 0xad9fdc: StoreField: r1->field_f = r0
    //     0xad9fdc: stur            w0, [x1, #0xf]
    // 0xad9fe0: r0 = Instance_MainAxisAlignment
    //     0xad9fe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xad9fe4: ldr             x0, [x0, #0xa8]
    // 0xad9fe8: StoreField: r1->field_13 = r0
    //     0xad9fe8: stur            w0, [x1, #0x13]
    // 0xad9fec: r0 = Instance_MainAxisSize
    //     0xad9fec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xad9ff0: ldr             x0, [x0, #0xa10]
    // 0xad9ff4: ArrayStore: r1[0] = r0  ; List_4
    //     0xad9ff4: stur            w0, [x1, #0x17]
    // 0xad9ff8: r2 = Instance_CrossAxisAlignment
    //     0xad9ff8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xad9ffc: ldr             x2, [x2, #0xa18]
    // 0xada000: StoreField: r1->field_1b = r2
    //     0xada000: stur            w2, [x1, #0x1b]
    // 0xada004: r3 = Instance_VerticalDirection
    //     0xada004: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xada008: ldr             x3, [x3, #0xa20]
    // 0xada00c: StoreField: r1->field_23 = r3
    //     0xada00c: stur            w3, [x1, #0x23]
    // 0xada010: r4 = Instance_Clip
    //     0xada010: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xada014: ldr             x4, [x4, #0x38]
    // 0xada018: StoreField: r1->field_2b = r4
    //     0xada018: stur            w4, [x1, #0x2b]
    // 0xada01c: StoreField: r1->field_2f = rZR
    //     0xada01c: stur            xzr, [x1, #0x2f]
    // 0xada020: ldur            x5, [fp, #-0x10]
    // 0xada024: StoreField: r1->field_b = r5
    //     0xada024: stur            w5, [x1, #0xb]
    // 0xada028: r0 = Padding()
    //     0xada028: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada02c: mov             x3, x0
    // 0xada030: r0 = Instance_EdgeInsets
    //     0xada030: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xada034: ldr             x0, [x0, #0x560]
    // 0xada038: stur            x3, [fp, #-0x10]
    // 0xada03c: StoreField: r3->field_f = r0
    //     0xada03c: stur            w0, [x3, #0xf]
    // 0xada040: ldur            x0, [fp, #-0x18]
    // 0xada044: StoreField: r3->field_b = r0
    //     0xada044: stur            w0, [x3, #0xb]
    // 0xada048: r1 = <Widget>
    //     0xada048: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xada04c: r2 = 36
    //     0xada04c: movz            x2, #0x24
    // 0xada050: r0 = AllocateArray()
    //     0xada050: bl              #0x16f7198  ; AllocateArrayStub
    // 0xada054: mov             x2, x0
    // 0xada058: ldur            x0, [fp, #-0x10]
    // 0xada05c: stur            x2, [fp, #-0x18]
    // 0xada060: StoreField: r2->field_f = r0
    //     0xada060: stur            w0, [x2, #0xf]
    // 0xada064: r16 = Instance_SizedBox
    //     0xada064: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xada068: ldr             x16, [x16, #0x8f0]
    // 0xada06c: StoreField: r2->field_13 = r16
    //     0xada06c: stur            w16, [x2, #0x13]
    // 0xada070: ldur            x0, [fp, #-0x38]
    // 0xada074: LoadField: r1 = r0->field_13
    //     0xada074: ldur            w1, [x0, #0x13]
    // 0xada078: DecompressPointer r1
    //     0xada078: add             x1, x1, HEAP, lsl #32
    // 0xada07c: r0 = of()
    //     0xada07c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada080: LoadField: r1 = r0->field_87
    //     0xada080: ldur            w1, [x0, #0x87]
    // 0xada084: DecompressPointer r1
    //     0xada084: add             x1, x1, HEAP, lsl #32
    // 0xada088: LoadField: r0 = r1->field_7
    //     0xada088: ldur            w0, [x1, #7]
    // 0xada08c: DecompressPointer r0
    //     0xada08c: add             x0, x0, HEAP, lsl #32
    // 0xada090: r16 = 14.000000
    //     0xada090: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xada094: ldr             x16, [x16, #0x1d8]
    // 0xada098: r30 = Instance_Color
    //     0xada098: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xada09c: stp             lr, x16, [SP]
    // 0xada0a0: mov             x1, x0
    // 0xada0a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada0a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada0a8: ldr             x4, [x4, #0xaa0]
    // 0xada0ac: r0 = copyWith()
    //     0xada0ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada0b0: stur            x0, [fp, #-0x10]
    // 0xada0b4: r0 = Text()
    //     0xada0b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xada0b8: mov             x3, x0
    // 0xada0bc: r0 = "Product Type"
    //     0xada0bc: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d08] "Product Type"
    //     0xada0c0: ldr             x0, [x0, #0xd08]
    // 0xada0c4: stur            x3, [fp, #-0x20]
    // 0xada0c8: StoreField: r3->field_b = r0
    //     0xada0c8: stur            w0, [x3, #0xb]
    // 0xada0cc: ldur            x0, [fp, #-0x10]
    // 0xada0d0: StoreField: r3->field_13 = r0
    //     0xada0d0: stur            w0, [x3, #0x13]
    // 0xada0d4: ldur            x0, [fp, #-8]
    // 0xada0d8: LoadField: r1 = r0->field_b
    //     0xada0d8: ldur            w1, [x0, #0xb]
    // 0xada0dc: DecompressPointer r1
    //     0xada0dc: add             x1, x1, HEAP, lsl #32
    // 0xada0e0: cmp             w1, NULL
    // 0xada0e4: b.eq            #0xadacd4
    // 0xada0e8: LoadField: r2 = r1->field_b
    //     0xada0e8: ldur            w2, [x1, #0xb]
    // 0xada0ec: DecompressPointer r2
    //     0xada0ec: add             x2, x2, HEAP, lsl #32
    // 0xada0f0: cmp             w2, NULL
    // 0xada0f4: b.ne            #0xada100
    // 0xada0f8: r6 = Null
    //     0xada0f8: mov             x6, NULL
    // 0xada0fc: b               #0xada124
    // 0xada100: LoadField: r1 = r2->field_7
    //     0xada100: ldur            w1, [x2, #7]
    // 0xada104: DecompressPointer r1
    //     0xada104: add             x1, x1, HEAP, lsl #32
    // 0xada108: cmp             w1, NULL
    // 0xada10c: b.ne            #0xada118
    // 0xada110: r1 = Null
    //     0xada110: mov             x1, NULL
    // 0xada114: b               #0xada120
    // 0xada118: LoadField: r2 = r1->field_b
    //     0xada118: ldur            w2, [x1, #0xb]
    // 0xada11c: mov             x1, x2
    // 0xada120: mov             x6, x1
    // 0xada124: ldur            x5, [fp, #-0x38]
    // 0xada128: ldur            x4, [fp, #-0x18]
    // 0xada12c: mov             x2, x5
    // 0xada130: stur            x6, [fp, #-0x10]
    // 0xada134: r1 = Function '<anonymous closure>':.
    //     0xada134: add             x1, PP, #0x58, lsl #12  ; [pp+0x58958] AnonymousClosure: (0xadc058), in [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xad9b44)
    //     0xada138: ldr             x1, [x1, #0x958]
    // 0xada13c: r0 = AllocateClosure()
    //     0xada13c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xada140: stur            x0, [fp, #-0x28]
    // 0xada144: r0 = ListView()
    //     0xada144: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xada148: stur            x0, [fp, #-0x30]
    // 0xada14c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xada14c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xada150: ldr             x16, [x16, #0x1c8]
    // 0xada154: r30 = true
    //     0xada154: add             lr, NULL, #0x20  ; true
    // 0xada158: stp             lr, x16, [SP]
    // 0xada15c: mov             x1, x0
    // 0xada160: ldur            x2, [fp, #-0x28]
    // 0xada164: ldur            x3, [fp, #-0x10]
    // 0xada168: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xada168: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xada16c: ldr             x4, [x4, #0xd18]
    // 0xada170: r0 = ListView.builder()
    //     0xada170: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xada174: r0 = Padding()
    //     0xada174: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada178: mov             x2, x0
    // 0xada17c: r0 = Instance_EdgeInsets
    //     0xada17c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xada180: ldr             x0, [x0, #0xd48]
    // 0xada184: stur            x2, [fp, #-0x10]
    // 0xada188: StoreField: r2->field_f = r0
    //     0xada188: stur            w0, [x2, #0xf]
    // 0xada18c: ldur            x1, [fp, #-0x30]
    // 0xada190: StoreField: r2->field_b = r1
    //     0xada190: stur            w1, [x2, #0xb]
    // 0xada194: ldur            x3, [fp, #-0x38]
    // 0xada198: LoadField: r1 = r3->field_13
    //     0xada198: ldur            w1, [x3, #0x13]
    // 0xada19c: DecompressPointer r1
    //     0xada19c: add             x1, x1, HEAP, lsl #32
    // 0xada1a0: r0 = of()
    //     0xada1a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada1a4: LoadField: r1 = r0->field_87
    //     0xada1a4: ldur            w1, [x0, #0x87]
    // 0xada1a8: DecompressPointer r1
    //     0xada1a8: add             x1, x1, HEAP, lsl #32
    // 0xada1ac: LoadField: r0 = r1->field_27
    //     0xada1ac: ldur            w0, [x1, #0x27]
    // 0xada1b0: DecompressPointer r0
    //     0xada1b0: add             x0, x0, HEAP, lsl #32
    // 0xada1b4: ldur            x2, [fp, #-0x38]
    // 0xada1b8: stur            x0, [fp, #-0x28]
    // 0xada1bc: LoadField: r1 = r2->field_13
    //     0xada1bc: ldur            w1, [x2, #0x13]
    // 0xada1c0: DecompressPointer r1
    //     0xada1c0: add             x1, x1, HEAP, lsl #32
    // 0xada1c4: r0 = of()
    //     0xada1c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada1c8: LoadField: r1 = r0->field_5b
    //     0xada1c8: ldur            w1, [x0, #0x5b]
    // 0xada1cc: DecompressPointer r1
    //     0xada1cc: add             x1, x1, HEAP, lsl #32
    // 0xada1d0: r0 = LoadClassIdInstr(r1)
    //     0xada1d0: ldur            x0, [x1, #-1]
    //     0xada1d4: ubfx            x0, x0, #0xc, #0x14
    // 0xada1d8: d0 = 0.400000
    //     0xada1d8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xada1dc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xada1dc: sub             lr, x0, #0xffa
    //     0xada1e0: ldr             lr, [x21, lr, lsl #3]
    //     0xada1e4: blr             lr
    // 0xada1e8: r16 = 16.000000
    //     0xada1e8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xada1ec: ldr             x16, [x16, #0x188]
    // 0xada1f0: stp             x0, x16, [SP]
    // 0xada1f4: ldur            x1, [fp, #-0x28]
    // 0xada1f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada1f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada1fc: ldr             x4, [x4, #0xaa0]
    // 0xada200: r0 = copyWith()
    //     0xada200: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada204: r0 = Accordion()
    //     0xada204: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xada208: mov             x1, x0
    // 0xada20c: ldur            x0, [fp, #-0x20]
    // 0xada210: StoreField: r1->field_b = r0
    //     0xada210: stur            w0, [x1, #0xb]
    // 0xada214: ldur            x0, [fp, #-0x10]
    // 0xada218: StoreField: r1->field_13 = r0
    //     0xada218: stur            w0, [x1, #0x13]
    // 0xada21c: r2 = false
    //     0xada21c: add             x2, NULL, #0x30  ; false
    // 0xada220: ArrayStore: r1[0] = r2  ; List_4
    //     0xada220: stur            w2, [x1, #0x17]
    // 0xada224: d0 = 25.000000
    //     0xada224: fmov            d0, #25.00000000
    // 0xada228: StoreField: r1->field_1b = d0
    //     0xada228: stur            d0, [x1, #0x1b]
    // 0xada22c: r3 = true
    //     0xada22c: add             x3, NULL, #0x20  ; true
    // 0xada230: StoreField: r1->field_23 = r3
    //     0xada230: stur            w3, [x1, #0x23]
    // 0xada234: mov             x0, x1
    // 0xada238: ldur            x1, [fp, #-0x18]
    // 0xada23c: ArrayStore: r1[2] = r0  ; List_4
    //     0xada23c: add             x25, x1, #0x17
    //     0xada240: str             w0, [x25]
    //     0xada244: tbz             w0, #0, #0xada260
    //     0xada248: ldurb           w16, [x1, #-1]
    //     0xada24c: ldurb           w17, [x0, #-1]
    //     0xada250: and             x16, x17, x16, lsr #2
    //     0xada254: tst             x16, HEAP, lsr #32
    //     0xada258: b.eq            #0xada260
    //     0xada25c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xada260: ldur            x0, [fp, #-0x18]
    // 0xada264: r16 = Instance_SizedBox
    //     0xada264: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada268: ldr             x16, [x16, #0x568]
    // 0xada26c: StoreField: r0->field_1b = r16
    //     0xada26c: stur            w16, [x0, #0x1b]
    // 0xada270: r16 = Instance_Padding
    //     0xada270: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xada274: ldr             x16, [x16, #0xd20]
    // 0xada278: StoreField: r0->field_1f = r16
    //     0xada278: stur            w16, [x0, #0x1f]
    // 0xada27c: r16 = Instance_SizedBox
    //     0xada27c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada280: ldr             x16, [x16, #0x568]
    // 0xada284: StoreField: r0->field_23 = r16
    //     0xada284: stur            w16, [x0, #0x23]
    // 0xada288: ldur            x4, [fp, #-0x38]
    // 0xada28c: LoadField: r1 = r4->field_13
    //     0xada28c: ldur            w1, [x4, #0x13]
    // 0xada290: DecompressPointer r1
    //     0xada290: add             x1, x1, HEAP, lsl #32
    // 0xada294: r0 = of()
    //     0xada294: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada298: LoadField: r1 = r0->field_87
    //     0xada298: ldur            w1, [x0, #0x87]
    // 0xada29c: DecompressPointer r1
    //     0xada29c: add             x1, x1, HEAP, lsl #32
    // 0xada2a0: LoadField: r0 = r1->field_7
    //     0xada2a0: ldur            w0, [x1, #7]
    // 0xada2a4: DecompressPointer r0
    //     0xada2a4: add             x0, x0, HEAP, lsl #32
    // 0xada2a8: r16 = 14.000000
    //     0xada2a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xada2ac: ldr             x16, [x16, #0x1d8]
    // 0xada2b0: r30 = Instance_Color
    //     0xada2b0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xada2b4: stp             lr, x16, [SP]
    // 0xada2b8: mov             x1, x0
    // 0xada2bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada2bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada2c0: ldr             x4, [x4, #0xaa0]
    // 0xada2c4: r0 = copyWith()
    //     0xada2c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada2c8: stur            x0, [fp, #-0x10]
    // 0xada2cc: r0 = Text()
    //     0xada2cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xada2d0: mov             x3, x0
    // 0xada2d4: r0 = "Size"
    //     0xada2d4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xada2d8: ldr             x0, [x0, #0x730]
    // 0xada2dc: stur            x3, [fp, #-0x20]
    // 0xada2e0: StoreField: r3->field_b = r0
    //     0xada2e0: stur            w0, [x3, #0xb]
    // 0xada2e4: ldur            x0, [fp, #-0x10]
    // 0xada2e8: StoreField: r3->field_13 = r0
    //     0xada2e8: stur            w0, [x3, #0x13]
    // 0xada2ec: ldur            x0, [fp, #-8]
    // 0xada2f0: LoadField: r1 = r0->field_b
    //     0xada2f0: ldur            w1, [x0, #0xb]
    // 0xada2f4: DecompressPointer r1
    //     0xada2f4: add             x1, x1, HEAP, lsl #32
    // 0xada2f8: cmp             w1, NULL
    // 0xada2fc: b.eq            #0xadacd8
    // 0xada300: LoadField: r2 = r1->field_b
    //     0xada300: ldur            w2, [x1, #0xb]
    // 0xada304: DecompressPointer r2
    //     0xada304: add             x2, x2, HEAP, lsl #32
    // 0xada308: cmp             w2, NULL
    // 0xada30c: b.ne            #0xada318
    // 0xada310: r6 = Null
    //     0xada310: mov             x6, NULL
    // 0xada314: b               #0xada33c
    // 0xada318: LoadField: r1 = r2->field_f
    //     0xada318: ldur            w1, [x2, #0xf]
    // 0xada31c: DecompressPointer r1
    //     0xada31c: add             x1, x1, HEAP, lsl #32
    // 0xada320: cmp             w1, NULL
    // 0xada324: b.ne            #0xada330
    // 0xada328: r1 = Null
    //     0xada328: mov             x1, NULL
    // 0xada32c: b               #0xada338
    // 0xada330: LoadField: r2 = r1->field_b
    //     0xada330: ldur            w2, [x1, #0xb]
    // 0xada334: mov             x1, x2
    // 0xada338: mov             x6, x1
    // 0xada33c: ldur            x5, [fp, #-0x38]
    // 0xada340: ldur            x4, [fp, #-0x18]
    // 0xada344: mov             x2, x5
    // 0xada348: stur            x6, [fp, #-0x10]
    // 0xada34c: r1 = Function '<anonymous closure>':.
    //     0xada34c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58960] AnonymousClosure: (0xadbce8), in [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xad9b44)
    //     0xada350: ldr             x1, [x1, #0x960]
    // 0xada354: r0 = AllocateClosure()
    //     0xada354: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xada358: stur            x0, [fp, #-0x28]
    // 0xada35c: r0 = ListView()
    //     0xada35c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xada360: stur            x0, [fp, #-0x30]
    // 0xada364: r16 = Instance_NeverScrollableScrollPhysics
    //     0xada364: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xada368: ldr             x16, [x16, #0x1c8]
    // 0xada36c: r30 = true
    //     0xada36c: add             lr, NULL, #0x20  ; true
    // 0xada370: stp             lr, x16, [SP]
    // 0xada374: mov             x1, x0
    // 0xada378: ldur            x2, [fp, #-0x28]
    // 0xada37c: ldur            x3, [fp, #-0x10]
    // 0xada380: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xada380: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xada384: ldr             x4, [x4, #0xd18]
    // 0xada388: r0 = ListView.builder()
    //     0xada388: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xada38c: r0 = Padding()
    //     0xada38c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada390: mov             x2, x0
    // 0xada394: r0 = Instance_EdgeInsets
    //     0xada394: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xada398: ldr             x0, [x0, #0xd48]
    // 0xada39c: stur            x2, [fp, #-0x10]
    // 0xada3a0: StoreField: r2->field_f = r0
    //     0xada3a0: stur            w0, [x2, #0xf]
    // 0xada3a4: ldur            x1, [fp, #-0x30]
    // 0xada3a8: StoreField: r2->field_b = r1
    //     0xada3a8: stur            w1, [x2, #0xb]
    // 0xada3ac: ldur            x3, [fp, #-0x38]
    // 0xada3b0: LoadField: r1 = r3->field_13
    //     0xada3b0: ldur            w1, [x3, #0x13]
    // 0xada3b4: DecompressPointer r1
    //     0xada3b4: add             x1, x1, HEAP, lsl #32
    // 0xada3b8: r0 = of()
    //     0xada3b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada3bc: LoadField: r1 = r0->field_87
    //     0xada3bc: ldur            w1, [x0, #0x87]
    // 0xada3c0: DecompressPointer r1
    //     0xada3c0: add             x1, x1, HEAP, lsl #32
    // 0xada3c4: LoadField: r0 = r1->field_7
    //     0xada3c4: ldur            w0, [x1, #7]
    // 0xada3c8: DecompressPointer r0
    //     0xada3c8: add             x0, x0, HEAP, lsl #32
    // 0xada3cc: stur            x0, [fp, #-0x28]
    // 0xada3d0: r1 = Instance_Color
    //     0xada3d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xada3d4: d0 = 0.700000
    //     0xada3d4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xada3d8: ldr             d0, [x17, #0xf48]
    // 0xada3dc: r0 = withOpacity()
    //     0xada3dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xada3e0: r16 = 16.000000
    //     0xada3e0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xada3e4: ldr             x16, [x16, #0x188]
    // 0xada3e8: stp             x0, x16, [SP]
    // 0xada3ec: ldur            x1, [fp, #-0x28]
    // 0xada3f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada3f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada3f4: ldr             x4, [x4, #0xaa0]
    // 0xada3f8: r0 = copyWith()
    //     0xada3f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada3fc: r0 = Accordion()
    //     0xada3fc: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xada400: mov             x1, x0
    // 0xada404: ldur            x0, [fp, #-0x20]
    // 0xada408: StoreField: r1->field_b = r0
    //     0xada408: stur            w0, [x1, #0xb]
    // 0xada40c: ldur            x0, [fp, #-0x10]
    // 0xada410: StoreField: r1->field_13 = r0
    //     0xada410: stur            w0, [x1, #0x13]
    // 0xada414: r2 = false
    //     0xada414: add             x2, NULL, #0x30  ; false
    // 0xada418: ArrayStore: r1[0] = r2  ; List_4
    //     0xada418: stur            w2, [x1, #0x17]
    // 0xada41c: d0 = 25.000000
    //     0xada41c: fmov            d0, #25.00000000
    // 0xada420: StoreField: r1->field_1b = d0
    //     0xada420: stur            d0, [x1, #0x1b]
    // 0xada424: r3 = true
    //     0xada424: add             x3, NULL, #0x20  ; true
    // 0xada428: StoreField: r1->field_23 = r3
    //     0xada428: stur            w3, [x1, #0x23]
    // 0xada42c: mov             x0, x1
    // 0xada430: ldur            x1, [fp, #-0x18]
    // 0xada434: ArrayStore: r1[6] = r0  ; List_4
    //     0xada434: add             x25, x1, #0x27
    //     0xada438: str             w0, [x25]
    //     0xada43c: tbz             w0, #0, #0xada458
    //     0xada440: ldurb           w16, [x1, #-1]
    //     0xada444: ldurb           w17, [x0, #-1]
    //     0xada448: and             x16, x17, x16, lsr #2
    //     0xada44c: tst             x16, HEAP, lsr #32
    //     0xada450: b.eq            #0xada458
    //     0xada454: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xada458: ldur            x0, [fp, #-0x18]
    // 0xada45c: r16 = Instance_SizedBox
    //     0xada45c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada460: ldr             x16, [x16, #0x568]
    // 0xada464: StoreField: r0->field_2b = r16
    //     0xada464: stur            w16, [x0, #0x2b]
    // 0xada468: r16 = Instance_Padding
    //     0xada468: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xada46c: ldr             x16, [x16, #0xd20]
    // 0xada470: StoreField: r0->field_2f = r16
    //     0xada470: stur            w16, [x0, #0x2f]
    // 0xada474: r16 = Instance_SizedBox
    //     0xada474: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada478: ldr             x16, [x16, #0x568]
    // 0xada47c: StoreField: r0->field_33 = r16
    //     0xada47c: stur            w16, [x0, #0x33]
    // 0xada480: ldur            x4, [fp, #-0x38]
    // 0xada484: LoadField: r1 = r4->field_13
    //     0xada484: ldur            w1, [x4, #0x13]
    // 0xada488: DecompressPointer r1
    //     0xada488: add             x1, x1, HEAP, lsl #32
    // 0xada48c: r0 = of()
    //     0xada48c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada490: LoadField: r1 = r0->field_87
    //     0xada490: ldur            w1, [x0, #0x87]
    // 0xada494: DecompressPointer r1
    //     0xada494: add             x1, x1, HEAP, lsl #32
    // 0xada498: LoadField: r0 = r1->field_7
    //     0xada498: ldur            w0, [x1, #7]
    // 0xada49c: DecompressPointer r0
    //     0xada49c: add             x0, x0, HEAP, lsl #32
    // 0xada4a0: r16 = 14.000000
    //     0xada4a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xada4a4: ldr             x16, [x16, #0x1d8]
    // 0xada4a8: r30 = Instance_Color
    //     0xada4a8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xada4ac: stp             lr, x16, [SP]
    // 0xada4b0: mov             x1, x0
    // 0xada4b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada4b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada4b8: ldr             x4, [x4, #0xaa0]
    // 0xada4bc: r0 = copyWith()
    //     0xada4bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada4c0: stur            x0, [fp, #-0x10]
    // 0xada4c4: r0 = Text()
    //     0xada4c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xada4c8: mov             x3, x0
    // 0xada4cc: r0 = "Colors"
    //     0xada4cc: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d30] "Colors"
    //     0xada4d0: ldr             x0, [x0, #0xd30]
    // 0xada4d4: stur            x3, [fp, #-0x20]
    // 0xada4d8: StoreField: r3->field_b = r0
    //     0xada4d8: stur            w0, [x3, #0xb]
    // 0xada4dc: ldur            x0, [fp, #-0x10]
    // 0xada4e0: StoreField: r3->field_13 = r0
    //     0xada4e0: stur            w0, [x3, #0x13]
    // 0xada4e4: ldur            x0, [fp, #-8]
    // 0xada4e8: LoadField: r1 = r0->field_b
    //     0xada4e8: ldur            w1, [x0, #0xb]
    // 0xada4ec: DecompressPointer r1
    //     0xada4ec: add             x1, x1, HEAP, lsl #32
    // 0xada4f0: cmp             w1, NULL
    // 0xada4f4: b.eq            #0xadacdc
    // 0xada4f8: LoadField: r2 = r1->field_b
    //     0xada4f8: ldur            w2, [x1, #0xb]
    // 0xada4fc: DecompressPointer r2
    //     0xada4fc: add             x2, x2, HEAP, lsl #32
    // 0xada500: cmp             w2, NULL
    // 0xada504: b.ne            #0xada510
    // 0xada508: r6 = Null
    //     0xada508: mov             x6, NULL
    // 0xada50c: b               #0xada534
    // 0xada510: LoadField: r1 = r2->field_b
    //     0xada510: ldur            w1, [x2, #0xb]
    // 0xada514: DecompressPointer r1
    //     0xada514: add             x1, x1, HEAP, lsl #32
    // 0xada518: cmp             w1, NULL
    // 0xada51c: b.ne            #0xada528
    // 0xada520: r1 = Null
    //     0xada520: mov             x1, NULL
    // 0xada524: b               #0xada530
    // 0xada528: LoadField: r2 = r1->field_b
    //     0xada528: ldur            w2, [x1, #0xb]
    // 0xada52c: mov             x1, x2
    // 0xada530: mov             x6, x1
    // 0xada534: ldur            x5, [fp, #-0x38]
    // 0xada538: ldur            x4, [fp, #-0x18]
    // 0xada53c: mov             x2, x5
    // 0xada540: stur            x6, [fp, #-0x10]
    // 0xada544: r1 = Function '<anonymous closure>':.
    //     0xada544: add             x1, PP, #0x58, lsl #12  ; [pp+0x58968] AnonymousClosure: (0xadb95c), in [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xad9b44)
    //     0xada548: ldr             x1, [x1, #0x968]
    // 0xada54c: r0 = AllocateClosure()
    //     0xada54c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xada550: stur            x0, [fp, #-0x28]
    // 0xada554: r0 = ListView()
    //     0xada554: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xada558: stur            x0, [fp, #-0x30]
    // 0xada55c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xada55c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xada560: ldr             x16, [x16, #0x1c8]
    // 0xada564: r30 = true
    //     0xada564: add             lr, NULL, #0x20  ; true
    // 0xada568: stp             lr, x16, [SP]
    // 0xada56c: mov             x1, x0
    // 0xada570: ldur            x2, [fp, #-0x28]
    // 0xada574: ldur            x3, [fp, #-0x10]
    // 0xada578: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xada578: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xada57c: ldr             x4, [x4, #0xd18]
    // 0xada580: r0 = ListView.builder()
    //     0xada580: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xada584: r0 = Padding()
    //     0xada584: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada588: mov             x2, x0
    // 0xada58c: r0 = Instance_EdgeInsets
    //     0xada58c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xada590: ldr             x0, [x0, #0xd48]
    // 0xada594: stur            x2, [fp, #-0x10]
    // 0xada598: StoreField: r2->field_f = r0
    //     0xada598: stur            w0, [x2, #0xf]
    // 0xada59c: ldur            x1, [fp, #-0x30]
    // 0xada5a0: StoreField: r2->field_b = r1
    //     0xada5a0: stur            w1, [x2, #0xb]
    // 0xada5a4: ldur            x3, [fp, #-0x38]
    // 0xada5a8: LoadField: r1 = r3->field_13
    //     0xada5a8: ldur            w1, [x3, #0x13]
    // 0xada5ac: DecompressPointer r1
    //     0xada5ac: add             x1, x1, HEAP, lsl #32
    // 0xada5b0: r0 = of()
    //     0xada5b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada5b4: LoadField: r1 = r0->field_87
    //     0xada5b4: ldur            w1, [x0, #0x87]
    // 0xada5b8: DecompressPointer r1
    //     0xada5b8: add             x1, x1, HEAP, lsl #32
    // 0xada5bc: LoadField: r0 = r1->field_7
    //     0xada5bc: ldur            w0, [x1, #7]
    // 0xada5c0: DecompressPointer r0
    //     0xada5c0: add             x0, x0, HEAP, lsl #32
    // 0xada5c4: ldur            x2, [fp, #-0x38]
    // 0xada5c8: stur            x0, [fp, #-0x28]
    // 0xada5cc: LoadField: r1 = r2->field_13
    //     0xada5cc: ldur            w1, [x2, #0x13]
    // 0xada5d0: DecompressPointer r1
    //     0xada5d0: add             x1, x1, HEAP, lsl #32
    // 0xada5d4: r0 = of()
    //     0xada5d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada5d8: LoadField: r1 = r0->field_5b
    //     0xada5d8: ldur            w1, [x0, #0x5b]
    // 0xada5dc: DecompressPointer r1
    //     0xada5dc: add             x1, x1, HEAP, lsl #32
    // 0xada5e0: r0 = LoadClassIdInstr(r1)
    //     0xada5e0: ldur            x0, [x1, #-1]
    //     0xada5e4: ubfx            x0, x0, #0xc, #0x14
    // 0xada5e8: d0 = 0.700000
    //     0xada5e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xada5ec: ldr             d0, [x17, #0xf48]
    // 0xada5f0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xada5f0: sub             lr, x0, #0xffa
    //     0xada5f4: ldr             lr, [x21, lr, lsl #3]
    //     0xada5f8: blr             lr
    // 0xada5fc: r16 = 16.000000
    //     0xada5fc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xada600: ldr             x16, [x16, #0x188]
    // 0xada604: stp             x0, x16, [SP]
    // 0xada608: ldur            x1, [fp, #-0x28]
    // 0xada60c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada60c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada610: ldr             x4, [x4, #0xaa0]
    // 0xada614: r0 = copyWith()
    //     0xada614: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada618: r0 = Accordion()
    //     0xada618: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xada61c: mov             x1, x0
    // 0xada620: ldur            x0, [fp, #-0x20]
    // 0xada624: StoreField: r1->field_b = r0
    //     0xada624: stur            w0, [x1, #0xb]
    // 0xada628: ldur            x0, [fp, #-0x10]
    // 0xada62c: StoreField: r1->field_13 = r0
    //     0xada62c: stur            w0, [x1, #0x13]
    // 0xada630: r2 = false
    //     0xada630: add             x2, NULL, #0x30  ; false
    // 0xada634: ArrayStore: r1[0] = r2  ; List_4
    //     0xada634: stur            w2, [x1, #0x17]
    // 0xada638: d0 = 25.000000
    //     0xada638: fmov            d0, #25.00000000
    // 0xada63c: StoreField: r1->field_1b = d0
    //     0xada63c: stur            d0, [x1, #0x1b]
    // 0xada640: r3 = true
    //     0xada640: add             x3, NULL, #0x20  ; true
    // 0xada644: StoreField: r1->field_23 = r3
    //     0xada644: stur            w3, [x1, #0x23]
    // 0xada648: mov             x0, x1
    // 0xada64c: ldur            x1, [fp, #-0x18]
    // 0xada650: ArrayStore: r1[10] = r0  ; List_4
    //     0xada650: add             x25, x1, #0x37
    //     0xada654: str             w0, [x25]
    //     0xada658: tbz             w0, #0, #0xada674
    //     0xada65c: ldurb           w16, [x1, #-1]
    //     0xada660: ldurb           w17, [x0, #-1]
    //     0xada664: and             x16, x17, x16, lsr #2
    //     0xada668: tst             x16, HEAP, lsr #32
    //     0xada66c: b.eq            #0xada674
    //     0xada670: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xada674: ldur            x0, [fp, #-0x18]
    // 0xada678: r16 = Instance_SizedBox
    //     0xada678: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada67c: ldr             x16, [x16, #0x568]
    // 0xada680: StoreField: r0->field_3b = r16
    //     0xada680: stur            w16, [x0, #0x3b]
    // 0xada684: r16 = Instance_Padding
    //     0xada684: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xada688: ldr             x16, [x16, #0xd20]
    // 0xada68c: StoreField: r0->field_3f = r16
    //     0xada68c: stur            w16, [x0, #0x3f]
    // 0xada690: r16 = Instance_SizedBox
    //     0xada690: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada694: ldr             x16, [x16, #0x568]
    // 0xada698: StoreField: r0->field_43 = r16
    //     0xada698: stur            w16, [x0, #0x43]
    // 0xada69c: ldur            x4, [fp, #-0x38]
    // 0xada6a0: LoadField: r1 = r4->field_13
    //     0xada6a0: ldur            w1, [x4, #0x13]
    // 0xada6a4: DecompressPointer r1
    //     0xada6a4: add             x1, x1, HEAP, lsl #32
    // 0xada6a8: r0 = of()
    //     0xada6a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada6ac: LoadField: r1 = r0->field_87
    //     0xada6ac: ldur            w1, [x0, #0x87]
    // 0xada6b0: DecompressPointer r1
    //     0xada6b0: add             x1, x1, HEAP, lsl #32
    // 0xada6b4: LoadField: r0 = r1->field_7
    //     0xada6b4: ldur            w0, [x1, #7]
    // 0xada6b8: DecompressPointer r0
    //     0xada6b8: add             x0, x0, HEAP, lsl #32
    // 0xada6bc: r16 = 14.000000
    //     0xada6bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xada6c0: ldr             x16, [x16, #0x1d8]
    // 0xada6c4: r30 = Instance_Color
    //     0xada6c4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xada6c8: stp             lr, x16, [SP]
    // 0xada6cc: mov             x1, x0
    // 0xada6d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada6d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada6d4: ldr             x4, [x4, #0xaa0]
    // 0xada6d8: r0 = copyWith()
    //     0xada6d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada6dc: stur            x0, [fp, #-0x10]
    // 0xada6e0: r0 = Text()
    //     0xada6e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xada6e4: mov             x3, x0
    // 0xada6e8: r0 = "Price Range"
    //     0xada6e8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53d40] "Price Range"
    //     0xada6ec: ldr             x0, [x0, #0xd40]
    // 0xada6f0: stur            x3, [fp, #-0x20]
    // 0xada6f4: StoreField: r3->field_b = r0
    //     0xada6f4: stur            w0, [x3, #0xb]
    // 0xada6f8: ldur            x0, [fp, #-0x10]
    // 0xada6fc: StoreField: r3->field_13 = r0
    //     0xada6fc: stur            w0, [x3, #0x13]
    // 0xada700: ldur            x0, [fp, #-8]
    // 0xada704: LoadField: r1 = r0->field_b
    //     0xada704: ldur            w1, [x0, #0xb]
    // 0xada708: DecompressPointer r1
    //     0xada708: add             x1, x1, HEAP, lsl #32
    // 0xada70c: cmp             w1, NULL
    // 0xada710: b.eq            #0xadace0
    // 0xada714: LoadField: r2 = r1->field_b
    //     0xada714: ldur            w2, [x1, #0xb]
    // 0xada718: DecompressPointer r2
    //     0xada718: add             x2, x2, HEAP, lsl #32
    // 0xada71c: cmp             w2, NULL
    // 0xada720: b.ne            #0xada72c
    // 0xada724: r7 = Null
    //     0xada724: mov             x7, NULL
    // 0xada728: b               #0xada750
    // 0xada72c: LoadField: r1 = r2->field_13
    //     0xada72c: ldur            w1, [x2, #0x13]
    // 0xada730: DecompressPointer r1
    //     0xada730: add             x1, x1, HEAP, lsl #32
    // 0xada734: cmp             w1, NULL
    // 0xada738: b.ne            #0xada744
    // 0xada73c: r1 = Null
    //     0xada73c: mov             x1, NULL
    // 0xada740: b               #0xada74c
    // 0xada744: LoadField: r2 = r1->field_b
    //     0xada744: ldur            w2, [x1, #0xb]
    // 0xada748: mov             x1, x2
    // 0xada74c: mov             x7, x1
    // 0xada750: ldur            x5, [fp, #-0x38]
    // 0xada754: ldur            x6, [fp, #-0x40]
    // 0xada758: ldur            x4, [fp, #-0x18]
    // 0xada75c: mov             x2, x5
    // 0xada760: stur            x7, [fp, #-0x10]
    // 0xada764: r1 = Function '<anonymous closure>':.
    //     0xada764: add             x1, PP, #0x58, lsl #12  ; [pp+0x58970] AnonymousClosure: (0xadacfc), in [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xad9b44)
    //     0xada768: ldr             x1, [x1, #0x970]
    // 0xada76c: r0 = AllocateClosure()
    //     0xada76c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xada770: stur            x0, [fp, #-0x28]
    // 0xada774: r0 = ListView()
    //     0xada774: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xada778: stur            x0, [fp, #-0x30]
    // 0xada77c: r16 = true
    //     0xada77c: add             x16, NULL, #0x20  ; true
    // 0xada780: r30 = Instance_NeverScrollableScrollPhysics
    //     0xada780: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xada784: ldr             lr, [lr, #0x1c8]
    // 0xada788: stp             lr, x16, [SP]
    // 0xada78c: mov             x1, x0
    // 0xada790: ldur            x2, [fp, #-0x28]
    // 0xada794: ldur            x3, [fp, #-0x10]
    // 0xada798: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xada798: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xada79c: ldr             x4, [x4, #8]
    // 0xada7a0: r0 = ListView.builder()
    //     0xada7a0: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xada7a4: r0 = Padding()
    //     0xada7a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada7a8: mov             x2, x0
    // 0xada7ac: r0 = Instance_EdgeInsets
    //     0xada7ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xada7b0: ldr             x0, [x0, #0xd48]
    // 0xada7b4: stur            x2, [fp, #-0x10]
    // 0xada7b8: StoreField: r2->field_f = r0
    //     0xada7b8: stur            w0, [x2, #0xf]
    // 0xada7bc: ldur            x0, [fp, #-0x30]
    // 0xada7c0: StoreField: r2->field_b = r0
    //     0xada7c0: stur            w0, [x2, #0xb]
    // 0xada7c4: ldur            x0, [fp, #-0x38]
    // 0xada7c8: LoadField: r1 = r0->field_13
    //     0xada7c8: ldur            w1, [x0, #0x13]
    // 0xada7cc: DecompressPointer r1
    //     0xada7cc: add             x1, x1, HEAP, lsl #32
    // 0xada7d0: r0 = of()
    //     0xada7d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada7d4: LoadField: r1 = r0->field_87
    //     0xada7d4: ldur            w1, [x0, #0x87]
    // 0xada7d8: DecompressPointer r1
    //     0xada7d8: add             x1, x1, HEAP, lsl #32
    // 0xada7dc: LoadField: r0 = r1->field_27
    //     0xada7dc: ldur            w0, [x1, #0x27]
    // 0xada7e0: DecompressPointer r0
    //     0xada7e0: add             x0, x0, HEAP, lsl #32
    // 0xada7e4: ldur            x2, [fp, #-0x38]
    // 0xada7e8: stur            x0, [fp, #-0x28]
    // 0xada7ec: LoadField: r1 = r2->field_13
    //     0xada7ec: ldur            w1, [x2, #0x13]
    // 0xada7f0: DecompressPointer r1
    //     0xada7f0: add             x1, x1, HEAP, lsl #32
    // 0xada7f4: r0 = of()
    //     0xada7f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xada7f8: LoadField: r1 = r0->field_5b
    //     0xada7f8: ldur            w1, [x0, #0x5b]
    // 0xada7fc: DecompressPointer r1
    //     0xada7fc: add             x1, x1, HEAP, lsl #32
    // 0xada800: r0 = LoadClassIdInstr(r1)
    //     0xada800: ldur            x0, [x1, #-1]
    //     0xada804: ubfx            x0, x0, #0xc, #0x14
    // 0xada808: d0 = 0.700000
    //     0xada808: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xada80c: ldr             d0, [x17, #0xf48]
    // 0xada810: r0 = GDT[cid_x0 + -0xffa]()
    //     0xada810: sub             lr, x0, #0xffa
    //     0xada814: ldr             lr, [x21, lr, lsl #3]
    //     0xada818: blr             lr
    // 0xada81c: r16 = 16.000000
    //     0xada81c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xada820: ldr             x16, [x16, #0x188]
    // 0xada824: stp             x0, x16, [SP]
    // 0xada828: ldur            x1, [fp, #-0x28]
    // 0xada82c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xada82c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xada830: ldr             x4, [x4, #0xaa0]
    // 0xada834: r0 = copyWith()
    //     0xada834: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xada838: r0 = Accordion()
    //     0xada838: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xada83c: mov             x1, x0
    // 0xada840: ldur            x0, [fp, #-0x20]
    // 0xada844: StoreField: r1->field_b = r0
    //     0xada844: stur            w0, [x1, #0xb]
    // 0xada848: ldur            x0, [fp, #-0x10]
    // 0xada84c: StoreField: r1->field_13 = r0
    //     0xada84c: stur            w0, [x1, #0x13]
    // 0xada850: r2 = false
    //     0xada850: add             x2, NULL, #0x30  ; false
    // 0xada854: ArrayStore: r1[0] = r2  ; List_4
    //     0xada854: stur            w2, [x1, #0x17]
    // 0xada858: d0 = 25.000000
    //     0xada858: fmov            d0, #25.00000000
    // 0xada85c: StoreField: r1->field_1b = d0
    //     0xada85c: stur            d0, [x1, #0x1b]
    // 0xada860: r3 = true
    //     0xada860: add             x3, NULL, #0x20  ; true
    // 0xada864: StoreField: r1->field_23 = r3
    //     0xada864: stur            w3, [x1, #0x23]
    // 0xada868: mov             x0, x1
    // 0xada86c: ldur            x1, [fp, #-0x18]
    // 0xada870: ArrayStore: r1[14] = r0  ; List_4
    //     0xada870: add             x25, x1, #0x47
    //     0xada874: str             w0, [x25]
    //     0xada878: tbz             w0, #0, #0xada894
    //     0xada87c: ldurb           w16, [x1, #-1]
    //     0xada880: ldurb           w17, [x0, #-1]
    //     0xada884: and             x16, x17, x16, lsr #2
    //     0xada888: tst             x16, HEAP, lsr #32
    //     0xada88c: b.eq            #0xada894
    //     0xada890: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xada894: ldur            x0, [fp, #-0x18]
    // 0xada898: r16 = Instance_SizedBox
    //     0xada898: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada89c: ldr             x16, [x16, #0x568]
    // 0xada8a0: StoreField: r0->field_4b = r16
    //     0xada8a0: stur            w16, [x0, #0x4b]
    // 0xada8a4: r16 = Instance_Padding
    //     0xada8a4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53d20] Obj!Padding@d684c1
    //     0xada8a8: ldr             x16, [x16, #0xd20]
    // 0xada8ac: StoreField: r0->field_4f = r16
    //     0xada8ac: stur            w16, [x0, #0x4f]
    // 0xada8b0: r16 = Instance_SizedBox
    //     0xada8b0: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e568] Obj!SizedBox@d67fa1
    //     0xada8b4: ldr             x16, [x16, #0x568]
    // 0xada8b8: StoreField: r0->field_53 = r16
    //     0xada8b8: stur            w16, [x0, #0x53]
    // 0xada8bc: r1 = <Widget>
    //     0xada8bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xada8c0: r0 = AllocateGrowableArray()
    //     0xada8c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xada8c4: mov             x1, x0
    // 0xada8c8: ldur            x0, [fp, #-0x18]
    // 0xada8cc: stur            x1, [fp, #-0x10]
    // 0xada8d0: StoreField: r1->field_f = r0
    //     0xada8d0: stur            w0, [x1, #0xf]
    // 0xada8d4: r0 = 36
    //     0xada8d4: movz            x0, #0x24
    // 0xada8d8: StoreField: r1->field_b = r0
    //     0xada8d8: stur            w0, [x1, #0xb]
    // 0xada8dc: r0 = Column()
    //     0xada8dc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xada8e0: mov             x1, x0
    // 0xada8e4: r0 = Instance_Axis
    //     0xada8e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xada8e8: stur            x1, [fp, #-0x18]
    // 0xada8ec: StoreField: r1->field_f = r0
    //     0xada8ec: stur            w0, [x1, #0xf]
    // 0xada8f0: r2 = Instance_MainAxisAlignment
    //     0xada8f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xada8f4: ldr             x2, [x2, #0xa08]
    // 0xada8f8: StoreField: r1->field_13 = r2
    //     0xada8f8: stur            w2, [x1, #0x13]
    // 0xada8fc: r2 = Instance_MainAxisSize
    //     0xada8fc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xada900: ldr             x2, [x2, #0xa10]
    // 0xada904: ArrayStore: r1[0] = r2  ; List_4
    //     0xada904: stur            w2, [x1, #0x17]
    // 0xada908: r2 = Instance_CrossAxisAlignment
    //     0xada908: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xada90c: ldr             x2, [x2, #0xa18]
    // 0xada910: StoreField: r1->field_1b = r2
    //     0xada910: stur            w2, [x1, #0x1b]
    // 0xada914: r2 = Instance_VerticalDirection
    //     0xada914: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xada918: ldr             x2, [x2, #0xa20]
    // 0xada91c: StoreField: r1->field_23 = r2
    //     0xada91c: stur            w2, [x1, #0x23]
    // 0xada920: r2 = Instance_Clip
    //     0xada920: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xada924: ldr             x2, [x2, #0x38]
    // 0xada928: StoreField: r1->field_2b = r2
    //     0xada928: stur            w2, [x1, #0x2b]
    // 0xada92c: StoreField: r1->field_2f = rZR
    //     0xada92c: stur            xzr, [x1, #0x2f]
    // 0xada930: ldur            x2, [fp, #-0x10]
    // 0xada934: StoreField: r1->field_b = r2
    //     0xada934: stur            w2, [x1, #0xb]
    // 0xada938: r0 = SingleChildScrollView()
    //     0xada938: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xada93c: mov             x1, x0
    // 0xada940: r0 = Instance_Axis
    //     0xada940: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xada944: stur            x1, [fp, #-0x10]
    // 0xada948: StoreField: r1->field_b = r0
    //     0xada948: stur            w0, [x1, #0xb]
    // 0xada94c: r0 = false
    //     0xada94c: add             x0, NULL, #0x30  ; false
    // 0xada950: StoreField: r1->field_f = r0
    //     0xada950: stur            w0, [x1, #0xf]
    // 0xada954: ldur            x2, [fp, #-0x18]
    // 0xada958: StoreField: r1->field_23 = r2
    //     0xada958: stur            w2, [x1, #0x23]
    // 0xada95c: r2 = Instance_DragStartBehavior
    //     0xada95c: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xada960: StoreField: r1->field_27 = r2
    //     0xada960: stur            w2, [x1, #0x27]
    // 0xada964: r2 = Instance_Clip
    //     0xada964: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xada968: ldr             x2, [x2, #0x7e0]
    // 0xada96c: StoreField: r1->field_2b = r2
    //     0xada96c: stur            w2, [x1, #0x2b]
    // 0xada970: r2 = Instance_HitTestBehavior
    //     0xada970: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xada974: ldr             x2, [x2, #0x288]
    // 0xada978: StoreField: r1->field_2f = r2
    //     0xada978: stur            w2, [x1, #0x2f]
    // 0xada97c: r0 = SafeArea()
    //     0xada97c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xada980: mov             x2, x0
    // 0xada984: r0 = true
    //     0xada984: add             x0, NULL, #0x20  ; true
    // 0xada988: stur            x2, [fp, #-0x18]
    // 0xada98c: StoreField: r2->field_b = r0
    //     0xada98c: stur            w0, [x2, #0xb]
    // 0xada990: StoreField: r2->field_f = r0
    //     0xada990: stur            w0, [x2, #0xf]
    // 0xada994: StoreField: r2->field_13 = r0
    //     0xada994: stur            w0, [x2, #0x13]
    // 0xada998: ArrayStore: r2[0] = r0  ; List_4
    //     0xada998: stur            w0, [x2, #0x17]
    // 0xada99c: r1 = Instance_EdgeInsets
    //     0xada99c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xada9a0: StoreField: r2->field_1b = r1
    //     0xada9a0: stur            w1, [x2, #0x1b]
    // 0xada9a4: r3 = false
    //     0xada9a4: add             x3, NULL, #0x30  ; false
    // 0xada9a8: StoreField: r2->field_1f = r3
    //     0xada9a8: stur            w3, [x2, #0x1f]
    // 0xada9ac: ldur            x1, [fp, #-0x10]
    // 0xada9b0: StoreField: r2->field_23 = r1
    //     0xada9b0: stur            w1, [x2, #0x23]
    // 0xada9b4: ldur            x1, [fp, #-8]
    // 0xada9b8: LoadField: r4 = r1->field_13
    //     0xada9b8: ldur            w4, [x1, #0x13]
    // 0xada9bc: DecompressPointer r4
    //     0xada9bc: add             x4, x4, HEAP, lsl #32
    // 0xada9c0: LoadField: r1 = r4->field_b
    //     0xada9c0: ldur            w1, [x4, #0xb]
    // 0xada9c4: cbnz            w1, #0xada9d0
    // 0xada9c8: r4 = false
    //     0xada9c8: add             x4, NULL, #0x30  ; false
    // 0xada9cc: b               #0xada9d4
    // 0xada9d0: r4 = true
    //     0xada9d0: add             x4, NULL, #0x20  ; true
    // 0xada9d4: ldur            x5, [fp, #-0x38]
    // 0xada9d8: stur            x4, [fp, #-8]
    // 0xada9dc: LoadField: r1 = r5->field_13
    //     0xada9dc: ldur            w1, [x5, #0x13]
    // 0xada9e0: DecompressPointer r1
    //     0xada9e0: add             x1, x1, HEAP, lsl #32
    // 0xada9e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xada9e4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xada9e8: r0 = _of()
    //     0xada9e8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xada9ec: LoadField: r1 = r0->field_7
    //     0xada9ec: ldur            w1, [x0, #7]
    // 0xada9f0: DecompressPointer r1
    //     0xada9f0: add             x1, x1, HEAP, lsl #32
    // 0xada9f4: LoadField: d0 = r1->field_7
    //     0xada9f4: ldur            d0, [x1, #7]
    // 0xada9f8: stur            d0, [fp, #-0x48]
    // 0xada9fc: r16 = <EdgeInsets>
    //     0xada9fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xadaa00: ldr             x16, [x16, #0xda0]
    // 0xadaa04: r30 = Instance_EdgeInsets
    //     0xadaa04: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xadaa08: ldr             lr, [lr, #0x1f0]
    // 0xadaa0c: stp             lr, x16, [SP]
    // 0xadaa10: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadaa10: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadaa14: r0 = all()
    //     0xadaa14: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xadaa18: ldur            x2, [fp, #-0x38]
    // 0xadaa1c: stur            x0, [fp, #-0x10]
    // 0xadaa20: LoadField: r1 = r2->field_13
    //     0xadaa20: ldur            w1, [x2, #0x13]
    // 0xadaa24: DecompressPointer r1
    //     0xadaa24: add             x1, x1, HEAP, lsl #32
    // 0xadaa28: r0 = of()
    //     0xadaa28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadaa2c: LoadField: r1 = r0->field_5b
    //     0xadaa2c: ldur            w1, [x0, #0x5b]
    // 0xadaa30: DecompressPointer r1
    //     0xadaa30: add             x1, x1, HEAP, lsl #32
    // 0xadaa34: r16 = <Color>
    //     0xadaa34: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xadaa38: ldr             x16, [x16, #0xf80]
    // 0xadaa3c: stp             x1, x16, [SP]
    // 0xadaa40: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadaa40: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadaa44: r0 = all()
    //     0xadaa44: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xadaa48: stur            x0, [fp, #-0x20]
    // 0xadaa4c: r0 = Radius()
    //     0xadaa4c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xadaa50: d0 = 30.000000
    //     0xadaa50: fmov            d0, #30.00000000
    // 0xadaa54: stur            x0, [fp, #-0x28]
    // 0xadaa58: StoreField: r0->field_7 = d0
    //     0xadaa58: stur            d0, [x0, #7]
    // 0xadaa5c: StoreField: r0->field_f = d0
    //     0xadaa5c: stur            d0, [x0, #0xf]
    // 0xadaa60: r0 = BorderRadius()
    //     0xadaa60: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xadaa64: mov             x1, x0
    // 0xadaa68: ldur            x0, [fp, #-0x28]
    // 0xadaa6c: stur            x1, [fp, #-0x30]
    // 0xadaa70: StoreField: r1->field_7 = r0
    //     0xadaa70: stur            w0, [x1, #7]
    // 0xadaa74: StoreField: r1->field_b = r0
    //     0xadaa74: stur            w0, [x1, #0xb]
    // 0xadaa78: StoreField: r1->field_f = r0
    //     0xadaa78: stur            w0, [x1, #0xf]
    // 0xadaa7c: StoreField: r1->field_13 = r0
    //     0xadaa7c: stur            w0, [x1, #0x13]
    // 0xadaa80: r0 = RoundedRectangleBorder()
    //     0xadaa80: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xadaa84: mov             x1, x0
    // 0xadaa88: ldur            x0, [fp, #-0x30]
    // 0xadaa8c: StoreField: r1->field_b = r0
    //     0xadaa8c: stur            w0, [x1, #0xb]
    // 0xadaa90: r0 = Instance_BorderSide
    //     0xadaa90: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xadaa94: ldr             x0, [x0, #0xe20]
    // 0xadaa98: StoreField: r1->field_7 = r0
    //     0xadaa98: stur            w0, [x1, #7]
    // 0xadaa9c: r16 = <RoundedRectangleBorder>
    //     0xadaa9c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xadaaa0: ldr             x16, [x16, #0xf78]
    // 0xadaaa4: stp             x1, x16, [SP]
    // 0xadaaa8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadaaa8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadaaac: r0 = all()
    //     0xadaaac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xadaab0: stur            x0, [fp, #-0x28]
    // 0xadaab4: r0 = ButtonStyle()
    //     0xadaab4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xadaab8: mov             x1, x0
    // 0xadaabc: ldur            x0, [fp, #-0x20]
    // 0xadaac0: stur            x1, [fp, #-0x30]
    // 0xadaac4: StoreField: r1->field_b = r0
    //     0xadaac4: stur            w0, [x1, #0xb]
    // 0xadaac8: ldur            x0, [fp, #-0x10]
    // 0xadaacc: StoreField: r1->field_23 = r0
    //     0xadaacc: stur            w0, [x1, #0x23]
    // 0xadaad0: ldur            x0, [fp, #-0x28]
    // 0xadaad4: StoreField: r1->field_43 = r0
    //     0xadaad4: stur            w0, [x1, #0x43]
    // 0xadaad8: r0 = TextButtonThemeData()
    //     0xadaad8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xadaadc: mov             x2, x0
    // 0xadaae0: ldur            x0, [fp, #-0x30]
    // 0xadaae4: stur            x2, [fp, #-0x10]
    // 0xadaae8: StoreField: r2->field_7 = r0
    //     0xadaae8: stur            w0, [x2, #7]
    // 0xadaaec: r1 = "show result"
    //     0xadaaec: add             x1, PP, #0x56, lsl #12  ; [pp+0x56560] "show result"
    //     0xadaaf0: ldr             x1, [x1, #0x560]
    // 0xadaaf4: r0 = capitalizeFirstWord()
    //     0xadaaf4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xadaaf8: ldur            x2, [fp, #-0x38]
    // 0xadaafc: stur            x0, [fp, #-0x20]
    // 0xadab00: LoadField: r1 = r2->field_13
    //     0xadab00: ldur            w1, [x2, #0x13]
    // 0xadab04: DecompressPointer r1
    //     0xadab04: add             x1, x1, HEAP, lsl #32
    // 0xadab08: r0 = of()
    //     0xadab08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadab0c: LoadField: r1 = r0->field_87
    //     0xadab0c: ldur            w1, [x0, #0x87]
    // 0xadab10: DecompressPointer r1
    //     0xadab10: add             x1, x1, HEAP, lsl #32
    // 0xadab14: LoadField: r0 = r1->field_7
    //     0xadab14: ldur            w0, [x1, #7]
    // 0xadab18: DecompressPointer r0
    //     0xadab18: add             x0, x0, HEAP, lsl #32
    // 0xadab1c: r16 = 16.000000
    //     0xadab1c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xadab20: ldr             x16, [x16, #0x188]
    // 0xadab24: r30 = Instance_Color
    //     0xadab24: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xadab28: stp             lr, x16, [SP]
    // 0xadab2c: mov             x1, x0
    // 0xadab30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadab30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadab34: ldr             x4, [x4, #0xaa0]
    // 0xadab38: r0 = copyWith()
    //     0xadab38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadab3c: stur            x0, [fp, #-0x28]
    // 0xadab40: r0 = Text()
    //     0xadab40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadab44: mov             x3, x0
    // 0xadab48: ldur            x0, [fp, #-0x20]
    // 0xadab4c: stur            x3, [fp, #-0x30]
    // 0xadab50: StoreField: r3->field_b = r0
    //     0xadab50: stur            w0, [x3, #0xb]
    // 0xadab54: ldur            x0, [fp, #-0x28]
    // 0xadab58: StoreField: r3->field_13 = r0
    //     0xadab58: stur            w0, [x3, #0x13]
    // 0xadab5c: ldur            x2, [fp, #-0x38]
    // 0xadab60: r1 = Function '<anonymous closure>':.
    //     0xadab60: add             x1, PP, #0x58, lsl #12  ; [pp+0x58978] AnonymousClosure: (0xa2da14), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::build (0xbd4660)
    //     0xadab64: ldr             x1, [x1, #0x978]
    // 0xadab68: r0 = AllocateClosure()
    //     0xadab68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xadab6c: stur            x0, [fp, #-0x20]
    // 0xadab70: r0 = TextButton()
    //     0xadab70: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xadab74: mov             x1, x0
    // 0xadab78: ldur            x0, [fp, #-0x20]
    // 0xadab7c: stur            x1, [fp, #-0x28]
    // 0xadab80: StoreField: r1->field_b = r0
    //     0xadab80: stur            w0, [x1, #0xb]
    // 0xadab84: r0 = false
    //     0xadab84: add             x0, NULL, #0x30  ; false
    // 0xadab88: StoreField: r1->field_27 = r0
    //     0xadab88: stur            w0, [x1, #0x27]
    // 0xadab8c: r2 = true
    //     0xadab8c: add             x2, NULL, #0x20  ; true
    // 0xadab90: StoreField: r1->field_2f = r2
    //     0xadab90: stur            w2, [x1, #0x2f]
    // 0xadab94: ldur            x3, [fp, #-0x30]
    // 0xadab98: StoreField: r1->field_37 = r3
    //     0xadab98: stur            w3, [x1, #0x37]
    // 0xadab9c: r0 = TextButtonTheme()
    //     0xadab9c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xadaba0: mov             x1, x0
    // 0xadaba4: ldur            x0, [fp, #-0x10]
    // 0xadaba8: stur            x1, [fp, #-0x20]
    // 0xadabac: StoreField: r1->field_f = r0
    //     0xadabac: stur            w0, [x1, #0xf]
    // 0xadabb0: ldur            x0, [fp, #-0x28]
    // 0xadabb4: StoreField: r1->field_b = r0
    //     0xadabb4: stur            w0, [x1, #0xb]
    // 0xadabb8: r0 = Padding()
    //     0xadabb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadabbc: mov             x1, x0
    // 0xadabc0: r0 = Instance_EdgeInsets
    //     0xadabc0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xadabc4: ldr             x0, [x0, #0x1f0]
    // 0xadabc8: stur            x1, [fp, #-0x28]
    // 0xadabcc: StoreField: r1->field_f = r0
    //     0xadabcc: stur            w0, [x1, #0xf]
    // 0xadabd0: ldur            x0, [fp, #-0x20]
    // 0xadabd4: StoreField: r1->field_b = r0
    //     0xadabd4: stur            w0, [x1, #0xb]
    // 0xadabd8: ldur            d0, [fp, #-0x48]
    // 0xadabdc: r0 = inline_Allocate_Double()
    //     0xadabdc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadabe0: add             x0, x0, #0x10
    //     0xadabe4: cmp             x2, x0
    //     0xadabe8: b.ls            #0xadace4
    //     0xadabec: str             x0, [THR, #0x50]  ; THR::top
    //     0xadabf0: sub             x0, x0, #0xf
    //     0xadabf4: movz            x2, #0xe15c
    //     0xadabf8: movk            x2, #0x3, lsl #16
    //     0xadabfc: stur            x2, [x0, #-1]
    // 0xadac00: StoreField: r0->field_7 = d0
    //     0xadac00: stur            d0, [x0, #7]
    // 0xadac04: stur            x0, [fp, #-0x10]
    // 0xadac08: r0 = SizedBox()
    //     0xadac08: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xadac0c: mov             x1, x0
    // 0xadac10: ldur            x0, [fp, #-0x10]
    // 0xadac14: stur            x1, [fp, #-0x20]
    // 0xadac18: StoreField: r1->field_f = r0
    //     0xadac18: stur            w0, [x1, #0xf]
    // 0xadac1c: ldur            x0, [fp, #-0x28]
    // 0xadac20: StoreField: r1->field_b = r0
    //     0xadac20: stur            w0, [x1, #0xb]
    // 0xadac24: r0 = Visibility()
    //     0xadac24: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xadac28: mov             x1, x0
    // 0xadac2c: ldur            x0, [fp, #-0x20]
    // 0xadac30: stur            x1, [fp, #-0x10]
    // 0xadac34: StoreField: r1->field_b = r0
    //     0xadac34: stur            w0, [x1, #0xb]
    // 0xadac38: r0 = Instance_SizedBox
    //     0xadac38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xadac3c: StoreField: r1->field_f = r0
    //     0xadac3c: stur            w0, [x1, #0xf]
    // 0xadac40: ldur            x0, [fp, #-8]
    // 0xadac44: StoreField: r1->field_13 = r0
    //     0xadac44: stur            w0, [x1, #0x13]
    // 0xadac48: r0 = false
    //     0xadac48: add             x0, NULL, #0x30  ; false
    // 0xadac4c: ArrayStore: r1[0] = r0  ; List_4
    //     0xadac4c: stur            w0, [x1, #0x17]
    // 0xadac50: StoreField: r1->field_1b = r0
    //     0xadac50: stur            w0, [x1, #0x1b]
    // 0xadac54: StoreField: r1->field_1f = r0
    //     0xadac54: stur            w0, [x1, #0x1f]
    // 0xadac58: StoreField: r1->field_23 = r0
    //     0xadac58: stur            w0, [x1, #0x23]
    // 0xadac5c: StoreField: r1->field_27 = r0
    //     0xadac5c: stur            w0, [x1, #0x27]
    // 0xadac60: StoreField: r1->field_2b = r0
    //     0xadac60: stur            w0, [x1, #0x2b]
    // 0xadac64: r0 = Scaffold()
    //     0xadac64: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xadac68: mov             x2, x0
    // 0xadac6c: ldur            x0, [fp, #-0x18]
    // 0xadac70: stur            x2, [fp, #-8]
    // 0xadac74: ArrayStore: r2[0] = r0  ; List_4
    //     0xadac74: stur            w0, [x2, #0x17]
    // 0xadac78: ldur            x0, [fp, #-0x10]
    // 0xadac7c: StoreField: r2->field_1b = r0
    //     0xadac7c: stur            w0, [x2, #0x1b]
    // 0xadac80: r0 = Instance__CenterFloatFabLocation
    //     0xadac80: add             x0, PP, #0x56, lsl #12  ; [pp+0x56570] Obj!_CenterFloatFabLocation@d5ade1
    //     0xadac84: ldr             x0, [x0, #0x570]
    // 0xadac88: StoreField: r2->field_1f = r0
    //     0xadac88: stur            w0, [x2, #0x1f]
    // 0xadac8c: r0 = true
    //     0xadac8c: add             x0, NULL, #0x20  ; true
    // 0xadac90: StoreField: r2->field_43 = r0
    //     0xadac90: stur            w0, [x2, #0x43]
    // 0xadac94: r1 = false
    //     0xadac94: add             x1, NULL, #0x30  ; false
    // 0xadac98: StoreField: r2->field_b = r1
    //     0xadac98: stur            w1, [x2, #0xb]
    // 0xadac9c: StoreField: r2->field_f = r1
    //     0xadac9c: stur            w1, [x2, #0xf]
    // 0xadaca0: r1 = <SystemUiOverlayStyle>
    //     0xadaca0: ldr             x1, [PP, #0x2848]  ; [pp+0x2848] TypeArguments: <SystemUiOverlayStyle>
    // 0xadaca4: r0 = AnnotatedRegion()
    //     0xadaca4: bl              #0xa2da08  ; AllocateAnnotatedRegionStub -> AnnotatedRegion<X0> (size=0x1c)
    // 0xadaca8: ldur            x1, [fp, #-0x40]
    // 0xadacac: StoreField: r0->field_13 = r1
    //     0xadacac: stur            w1, [x0, #0x13]
    // 0xadacb0: r1 = true
    //     0xadacb0: add             x1, NULL, #0x20  ; true
    // 0xadacb4: ArrayStore: r0[0] = r1  ; List_4
    //     0xadacb4: stur            w1, [x0, #0x17]
    // 0xadacb8: ldur            x1, [fp, #-8]
    // 0xadacbc: StoreField: r0->field_b = r1
    //     0xadacbc: stur            w1, [x0, #0xb]
    // 0xadacc0: LeaveFrame
    //     0xadacc0: mov             SP, fp
    //     0xadacc4: ldp             fp, lr, [SP], #0x10
    // 0xadacc8: ret
    //     0xadacc8: ret             
    // 0xadaccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadaccc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadacd0: b               #0xad9b6c
    // 0xadacd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadacd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadacd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadacd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadacdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadacdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadace0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadace0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadace4: SaveReg d0
    //     0xadace4: str             q0, [SP, #-0x10]!
    // 0xadace8: SaveReg r1
    //     0xadace8: str             x1, [SP, #-8]!
    // 0xadacec: r0 = AllocateDouble()
    //     0xadacec: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xadacf0: RestoreReg r1
    //     0xadacf0: ldr             x1, [SP], #8
    // 0xadacf4: RestoreReg d0
    //     0xadacf4: ldr             q0, [SP], #0x10
    // 0xadacf8: b               #0xadac00
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadacfc, size: 0x46c
    // 0xadacfc: EnterFrame
    //     0xadacfc: stp             fp, lr, [SP, #-0x10]!
    //     0xadad00: mov             fp, SP
    // 0xadad04: AllocStack(0x40)
    //     0xadad04: sub             SP, SP, #0x40
    // 0xadad08: SetupParameters()
    //     0xadad08: ldr             x0, [fp, #0x20]
    //     0xadad0c: ldur            w4, [x0, #0x17]
    //     0xadad10: add             x4, x4, HEAP, lsl #32
    //     0xadad14: stur            x4, [fp, #-0x10]
    // 0xadad18: CheckStackOverflow
    //     0xadad18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadad1c: cmp             SP, x16
    //     0xadad20: b.ls            #0xadb144
    // 0xadad24: LoadField: r1 = r4->field_f
    //     0xadad24: ldur            w1, [x4, #0xf]
    // 0xadad28: DecompressPointer r1
    //     0xadad28: add             x1, x1, HEAP, lsl #32
    // 0xadad2c: LoadField: r0 = r1->field_b
    //     0xadad2c: ldur            w0, [x1, #0xb]
    // 0xadad30: DecompressPointer r0
    //     0xadad30: add             x0, x0, HEAP, lsl #32
    // 0xadad34: cmp             w0, NULL
    // 0xadad38: b.eq            #0xadb14c
    // 0xadad3c: LoadField: r2 = r0->field_b
    //     0xadad3c: ldur            w2, [x0, #0xb]
    // 0xadad40: DecompressPointer r2
    //     0xadad40: add             x2, x2, HEAP, lsl #32
    // 0xadad44: ldr             x0, [fp, #0x10]
    // 0xadad48: r6 = LoadInt32Instr(r0)
    //     0xadad48: sbfx            x6, x0, #1, #0x1f
    //     0xadad4c: tbz             w0, #0, #0xadad54
    //     0xadad50: ldur            x6, [x0, #7]
    // 0xadad54: mov             x5, x6
    // 0xadad58: stur            x6, [fp, #-8]
    // 0xadad5c: r3 = Instance_ItemFilterType
    //     0xadad5c: add             x3, PP, #0x58, lsl #12  ; [pp+0x58980] Obj!ItemFilterType@d753a1
    //     0xadad60: ldr             x3, [x3, #0x980]
    // 0xadad64: r0 = _productBuildItem()
    //     0xadad64: bl              #0xadb168  ; [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xadad68: mov             x3, x0
    // 0xadad6c: ldur            x2, [fp, #-0x10]
    // 0xadad70: stur            x3, [fp, #-0x18]
    // 0xadad74: LoadField: r0 = r2->field_f
    //     0xadad74: ldur            w0, [x2, #0xf]
    // 0xadad78: DecompressPointer r0
    //     0xadad78: add             x0, x0, HEAP, lsl #32
    // 0xadad7c: LoadField: r1 = r0->field_b
    //     0xadad7c: ldur            w1, [x0, #0xb]
    // 0xadad80: DecompressPointer r1
    //     0xadad80: add             x1, x1, HEAP, lsl #32
    // 0xadad84: cmp             w1, NULL
    // 0xadad88: b.eq            #0xadb150
    // 0xadad8c: LoadField: r0 = r1->field_b
    //     0xadad8c: ldur            w0, [x1, #0xb]
    // 0xadad90: DecompressPointer r0
    //     0xadad90: add             x0, x0, HEAP, lsl #32
    // 0xadad94: cmp             w0, NULL
    // 0xadad98: b.ne            #0xadada4
    // 0xadad9c: r0 = Null
    //     0xadad9c: mov             x0, NULL
    // 0xadada0: b               #0xadae20
    // 0xadada4: LoadField: r4 = r0->field_13
    //     0xadada4: ldur            w4, [x0, #0x13]
    // 0xadada8: DecompressPointer r4
    //     0xadada8: add             x4, x4, HEAP, lsl #32
    // 0xadadac: cmp             w4, NULL
    // 0xadadb0: b.ne            #0xadadbc
    // 0xadadb4: r0 = Null
    //     0xadadb4: mov             x0, NULL
    // 0xadadb8: b               #0xadae20
    // 0xadadbc: ldur            x5, [fp, #-8]
    // 0xadadc0: LoadField: r0 = r4->field_b
    //     0xadadc0: ldur            w0, [x4, #0xb]
    // 0xadadc4: r1 = LoadInt32Instr(r0)
    //     0xadadc4: sbfx            x1, x0, #1, #0x1f
    // 0xadadc8: mov             x0, x1
    // 0xadadcc: mov             x1, x5
    // 0xadadd0: cmp             x1, x0
    // 0xadadd4: b.hs            #0xadb154
    // 0xadadd8: LoadField: r0 = r4->field_f
    //     0xadadd8: ldur            w0, [x4, #0xf]
    // 0xadaddc: DecompressPointer r0
    //     0xadaddc: add             x0, x0, HEAP, lsl #32
    // 0xadade0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadade0: add             x16, x0, x5, lsl #2
    //     0xadade4: ldur            w1, [x16, #0xf]
    // 0xadade8: DecompressPointer r1
    //     0xadade8: add             x1, x1, HEAP, lsl #32
    // 0xadadec: LoadField: r0 = r1->field_7
    //     0xadadec: ldur            w0, [x1, #7]
    // 0xadadf0: DecompressPointer r0
    //     0xadadf0: add             x0, x0, HEAP, lsl #32
    // 0xadadf4: r1 = 60
    //     0xadadf4: movz            x1, #0x3c
    // 0xadadf8: branchIfSmi(r0, 0xadae04)
    //     0xadadf8: tbz             w0, #0, #0xadae04
    // 0xadadfc: r1 = LoadClassIdInstr(r0)
    //     0xadadfc: ldur            x1, [x0, #-1]
    //     0xadae00: ubfx            x1, x1, #0xc, #0x14
    // 0xadae04: str             x0, [SP]
    // 0xadae08: mov             x0, x1
    // 0xadae0c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xadae0c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xadae10: r0 = GDT[cid_x0 + 0x2700]()
    //     0xadae10: movz            x17, #0x2700
    //     0xadae14: add             lr, x0, x17
    //     0xadae18: ldr             lr, [x21, lr, lsl #3]
    //     0xadae1c: blr             lr
    // 0xadae20: cmp             w0, NULL
    // 0xadae24: b.ne            #0xadae30
    // 0xadae28: r3 = ""
    //     0xadae28: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadae2c: b               #0xadae34
    // 0xadae30: mov             x3, x0
    // 0xadae34: ldur            x0, [fp, #-0x10]
    // 0xadae38: stur            x3, [fp, #-0x20]
    // 0xadae3c: r1 = Null
    //     0xadae3c: mov             x1, NULL
    // 0xadae40: r2 = 6
    //     0xadae40: movz            x2, #0x6
    // 0xadae44: r0 = AllocateArray()
    //     0xadae44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadae48: mov             x2, x0
    // 0xadae4c: ldur            x0, [fp, #-0x20]
    // 0xadae50: StoreField: r2->field_f = r0
    //     0xadae50: stur            w0, [x2, #0xf]
    // 0xadae54: r16 = " - "
    //     0xadae54: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xadae58: ldr             x16, [x16, #0xc08]
    // 0xadae5c: StoreField: r2->field_13 = r16
    //     0xadae5c: stur            w16, [x2, #0x13]
    // 0xadae60: ldur            x3, [fp, #-0x10]
    // 0xadae64: LoadField: r0 = r3->field_f
    //     0xadae64: ldur            w0, [x3, #0xf]
    // 0xadae68: DecompressPointer r0
    //     0xadae68: add             x0, x0, HEAP, lsl #32
    // 0xadae6c: LoadField: r1 = r0->field_b
    //     0xadae6c: ldur            w1, [x0, #0xb]
    // 0xadae70: DecompressPointer r1
    //     0xadae70: add             x1, x1, HEAP, lsl #32
    // 0xadae74: cmp             w1, NULL
    // 0xadae78: b.eq            #0xadb158
    // 0xadae7c: LoadField: r0 = r1->field_b
    //     0xadae7c: ldur            w0, [x1, #0xb]
    // 0xadae80: DecompressPointer r0
    //     0xadae80: add             x0, x0, HEAP, lsl #32
    // 0xadae84: cmp             w0, NULL
    // 0xadae88: b.ne            #0xadae98
    // 0xadae8c: ldur            x5, [fp, #-8]
    // 0xadae90: r0 = Null
    //     0xadae90: mov             x0, NULL
    // 0xadae94: b               #0xadaeec
    // 0xadae98: LoadField: r4 = r0->field_13
    //     0xadae98: ldur            w4, [x0, #0x13]
    // 0xadae9c: DecompressPointer r4
    //     0xadae9c: add             x4, x4, HEAP, lsl #32
    // 0xadaea0: cmp             w4, NULL
    // 0xadaea4: b.ne            #0xadaeb4
    // 0xadaea8: ldur            x5, [fp, #-8]
    // 0xadaeac: r0 = Null
    //     0xadaeac: mov             x0, NULL
    // 0xadaeb0: b               #0xadaeec
    // 0xadaeb4: ldur            x5, [fp, #-8]
    // 0xadaeb8: LoadField: r0 = r4->field_b
    //     0xadaeb8: ldur            w0, [x4, #0xb]
    // 0xadaebc: r1 = LoadInt32Instr(r0)
    //     0xadaebc: sbfx            x1, x0, #1, #0x1f
    // 0xadaec0: mov             x0, x1
    // 0xadaec4: mov             x1, x5
    // 0xadaec8: cmp             x1, x0
    // 0xadaecc: b.hs            #0xadb15c
    // 0xadaed0: LoadField: r0 = r4->field_f
    //     0xadaed0: ldur            w0, [x4, #0xf]
    // 0xadaed4: DecompressPointer r0
    //     0xadaed4: add             x0, x0, HEAP, lsl #32
    // 0xadaed8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadaed8: add             x16, x0, x5, lsl #2
    //     0xadaedc: ldur            w1, [x16, #0xf]
    // 0xadaee0: DecompressPointer r1
    //     0xadaee0: add             x1, x1, HEAP, lsl #32
    // 0xadaee4: LoadField: r0 = r1->field_b
    //     0xadaee4: ldur            w0, [x1, #0xb]
    // 0xadaee8: DecompressPointer r0
    //     0xadaee8: add             x0, x0, HEAP, lsl #32
    // 0xadaeec: cmp             w0, NULL
    // 0xadaef0: b.ne            #0xadaefc
    // 0xadaef4: r0 = "above"
    //     0xadaef4: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xadaef8: ldr             x0, [x0, #0xc10]
    // 0xadaefc: ArrayStore: r2[0] = r0  ; List_4
    //     0xadaefc: stur            w0, [x2, #0x17]
    // 0xadaf00: str             x2, [SP]
    // 0xadaf04: r0 = _interpolate()
    //     0xadaf04: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xadaf08: ldr             x1, [fp, #0x18]
    // 0xadaf0c: stur            x0, [fp, #-0x20]
    // 0xadaf10: r0 = of()
    //     0xadaf10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadaf14: LoadField: r1 = r0->field_87
    //     0xadaf14: ldur            w1, [x0, #0x87]
    // 0xadaf18: DecompressPointer r1
    //     0xadaf18: add             x1, x1, HEAP, lsl #32
    // 0xadaf1c: LoadField: r0 = r1->field_2b
    //     0xadaf1c: ldur            w0, [x1, #0x2b]
    // 0xadaf20: DecompressPointer r0
    //     0xadaf20: add             x0, x0, HEAP, lsl #32
    // 0xadaf24: r16 = 14.000000
    //     0xadaf24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xadaf28: ldr             x16, [x16, #0x1d8]
    // 0xadaf2c: r30 = Instance_Color
    //     0xadaf2c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadaf30: stp             lr, x16, [SP]
    // 0xadaf34: mov             x1, x0
    // 0xadaf38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadaf38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadaf3c: ldr             x4, [x4, #0xaa0]
    // 0xadaf40: r0 = copyWith()
    //     0xadaf40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadaf44: stur            x0, [fp, #-0x28]
    // 0xadaf48: r0 = Text()
    //     0xadaf48: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadaf4c: mov             x2, x0
    // 0xadaf50: ldur            x0, [fp, #-0x20]
    // 0xadaf54: stur            x2, [fp, #-0x30]
    // 0xadaf58: StoreField: r2->field_b = r0
    //     0xadaf58: stur            w0, [x2, #0xb]
    // 0xadaf5c: ldur            x0, [fp, #-0x28]
    // 0xadaf60: StoreField: r2->field_13 = r0
    //     0xadaf60: stur            w0, [x2, #0x13]
    // 0xadaf64: ldur            x0, [fp, #-0x10]
    // 0xadaf68: LoadField: r1 = r0->field_f
    //     0xadaf68: ldur            w1, [x0, #0xf]
    // 0xadaf6c: DecompressPointer r1
    //     0xadaf6c: add             x1, x1, HEAP, lsl #32
    // 0xadaf70: LoadField: r0 = r1->field_b
    //     0xadaf70: ldur            w0, [x1, #0xb]
    // 0xadaf74: DecompressPointer r0
    //     0xadaf74: add             x0, x0, HEAP, lsl #32
    // 0xadaf78: cmp             w0, NULL
    // 0xadaf7c: b.eq            #0xadb160
    // 0xadaf80: LoadField: r1 = r0->field_b
    //     0xadaf80: ldur            w1, [x0, #0xb]
    // 0xadaf84: DecompressPointer r1
    //     0xadaf84: add             x1, x1, HEAP, lsl #32
    // 0xadaf88: cmp             w1, NULL
    // 0xadaf8c: b.ne            #0xadaf98
    // 0xadaf90: r0 = Null
    //     0xadaf90: mov             x0, NULL
    // 0xadaf94: b               #0xadb014
    // 0xadaf98: LoadField: r3 = r1->field_13
    //     0xadaf98: ldur            w3, [x1, #0x13]
    // 0xadaf9c: DecompressPointer r3
    //     0xadaf9c: add             x3, x3, HEAP, lsl #32
    // 0xadafa0: cmp             w3, NULL
    // 0xadafa4: b.ne            #0xadafb0
    // 0xadafa8: r0 = Null
    //     0xadafa8: mov             x0, NULL
    // 0xadafac: b               #0xadb014
    // 0xadafb0: ldur            x4, [fp, #-8]
    // 0xadafb4: LoadField: r0 = r3->field_b
    //     0xadafb4: ldur            w0, [x3, #0xb]
    // 0xadafb8: r1 = LoadInt32Instr(r0)
    //     0xadafb8: sbfx            x1, x0, #1, #0x1f
    // 0xadafbc: mov             x0, x1
    // 0xadafc0: mov             x1, x4
    // 0xadafc4: cmp             x1, x0
    // 0xadafc8: b.hs            #0xadb164
    // 0xadafcc: LoadField: r0 = r3->field_f
    //     0xadafcc: ldur            w0, [x3, #0xf]
    // 0xadafd0: DecompressPointer r0
    //     0xadafd0: add             x0, x0, HEAP, lsl #32
    // 0xadafd4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xadafd4: add             x16, x0, x4, lsl #2
    //     0xadafd8: ldur            w1, [x16, #0xf]
    // 0xadafdc: DecompressPointer r1
    //     0xadafdc: add             x1, x1, HEAP, lsl #32
    // 0xadafe0: LoadField: r0 = r1->field_f
    //     0xadafe0: ldur            w0, [x1, #0xf]
    // 0xadafe4: DecompressPointer r0
    //     0xadafe4: add             x0, x0, HEAP, lsl #32
    // 0xadafe8: r1 = 60
    //     0xadafe8: movz            x1, #0x3c
    // 0xadafec: branchIfSmi(r0, 0xadaff8)
    //     0xadafec: tbz             w0, #0, #0xadaff8
    // 0xadaff0: r1 = LoadClassIdInstr(r0)
    //     0xadaff0: ldur            x1, [x0, #-1]
    //     0xadaff4: ubfx            x1, x1, #0xc, #0x14
    // 0xadaff8: str             x0, [SP]
    // 0xadaffc: mov             x0, x1
    // 0xadb000: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xadb000: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xadb004: r0 = GDT[cid_x0 + 0x2700]()
    //     0xadb004: movz            x17, #0x2700
    //     0xadb008: add             lr, x0, x17
    //     0xadb00c: ldr             lr, [x21, lr, lsl #3]
    //     0xadb010: blr             lr
    // 0xadb014: cmp             w0, NULL
    // 0xadb018: b.ne            #0xadb024
    // 0xadb01c: r3 = ""
    //     0xadb01c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadb020: b               #0xadb028
    // 0xadb024: mov             x3, x0
    // 0xadb028: ldur            x2, [fp, #-0x18]
    // 0xadb02c: ldur            x0, [fp, #-0x30]
    // 0xadb030: ldr             x1, [fp, #0x18]
    // 0xadb034: stur            x3, [fp, #-0x10]
    // 0xadb038: r0 = of()
    //     0xadb038: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadb03c: LoadField: r1 = r0->field_87
    //     0xadb03c: ldur            w1, [x0, #0x87]
    // 0xadb040: DecompressPointer r1
    //     0xadb040: add             x1, x1, HEAP, lsl #32
    // 0xadb044: LoadField: r0 = r1->field_2b
    //     0xadb044: ldur            w0, [x1, #0x2b]
    // 0xadb048: DecompressPointer r0
    //     0xadb048: add             x0, x0, HEAP, lsl #32
    // 0xadb04c: r16 = 14.000000
    //     0xadb04c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xadb050: ldr             x16, [x16, #0x1d8]
    // 0xadb054: r30 = Instance_Color
    //     0xadb054: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadb058: stp             lr, x16, [SP]
    // 0xadb05c: mov             x1, x0
    // 0xadb060: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadb060: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadb064: ldr             x4, [x4, #0xaa0]
    // 0xadb068: r0 = copyWith()
    //     0xadb068: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadb06c: stur            x0, [fp, #-0x20]
    // 0xadb070: r0 = Text()
    //     0xadb070: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadb074: mov             x3, x0
    // 0xadb078: ldur            x0, [fp, #-0x10]
    // 0xadb07c: stur            x3, [fp, #-0x28]
    // 0xadb080: StoreField: r3->field_b = r0
    //     0xadb080: stur            w0, [x3, #0xb]
    // 0xadb084: ldur            x0, [fp, #-0x20]
    // 0xadb088: StoreField: r3->field_13 = r0
    //     0xadb088: stur            w0, [x3, #0x13]
    // 0xadb08c: r1 = Null
    //     0xadb08c: mov             x1, NULL
    // 0xadb090: r2 = 8
    //     0xadb090: movz            x2, #0x8
    // 0xadb094: r0 = AllocateArray()
    //     0xadb094: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadb098: mov             x2, x0
    // 0xadb09c: ldur            x0, [fp, #-0x18]
    // 0xadb0a0: stur            x2, [fp, #-0x10]
    // 0xadb0a4: StoreField: r2->field_f = r0
    //     0xadb0a4: stur            w0, [x2, #0xf]
    // 0xadb0a8: ldur            x0, [fp, #-0x30]
    // 0xadb0ac: StoreField: r2->field_13 = r0
    //     0xadb0ac: stur            w0, [x2, #0x13]
    // 0xadb0b0: r16 = Instance_Spacer
    //     0xadb0b0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xadb0b4: ldr             x16, [x16, #0xf0]
    // 0xadb0b8: ArrayStore: r2[0] = r16  ; List_4
    //     0xadb0b8: stur            w16, [x2, #0x17]
    // 0xadb0bc: ldur            x0, [fp, #-0x28]
    // 0xadb0c0: StoreField: r2->field_1b = r0
    //     0xadb0c0: stur            w0, [x2, #0x1b]
    // 0xadb0c4: r1 = <Widget>
    //     0xadb0c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xadb0c8: r0 = AllocateGrowableArray()
    //     0xadb0c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xadb0cc: mov             x1, x0
    // 0xadb0d0: ldur            x0, [fp, #-0x10]
    // 0xadb0d4: stur            x1, [fp, #-0x18]
    // 0xadb0d8: StoreField: r1->field_f = r0
    //     0xadb0d8: stur            w0, [x1, #0xf]
    // 0xadb0dc: r0 = 8
    //     0xadb0dc: movz            x0, #0x8
    // 0xadb0e0: StoreField: r1->field_b = r0
    //     0xadb0e0: stur            w0, [x1, #0xb]
    // 0xadb0e4: r0 = Row()
    //     0xadb0e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xadb0e8: r1 = Instance_Axis
    //     0xadb0e8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xadb0ec: StoreField: r0->field_f = r1
    //     0xadb0ec: stur            w1, [x0, #0xf]
    // 0xadb0f0: r1 = Instance_MainAxisAlignment
    //     0xadb0f0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xadb0f4: ldr             x1, [x1, #0xa08]
    // 0xadb0f8: StoreField: r0->field_13 = r1
    //     0xadb0f8: stur            w1, [x0, #0x13]
    // 0xadb0fc: r1 = Instance_MainAxisSize
    //     0xadb0fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xadb100: ldr             x1, [x1, #0xa10]
    // 0xadb104: ArrayStore: r0[0] = r1  ; List_4
    //     0xadb104: stur            w1, [x0, #0x17]
    // 0xadb108: r1 = Instance_CrossAxisAlignment
    //     0xadb108: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xadb10c: ldr             x1, [x1, #0xa18]
    // 0xadb110: StoreField: r0->field_1b = r1
    //     0xadb110: stur            w1, [x0, #0x1b]
    // 0xadb114: r1 = Instance_VerticalDirection
    //     0xadb114: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xadb118: ldr             x1, [x1, #0xa20]
    // 0xadb11c: StoreField: r0->field_23 = r1
    //     0xadb11c: stur            w1, [x0, #0x23]
    // 0xadb120: r1 = Instance_Clip
    //     0xadb120: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xadb124: ldr             x1, [x1, #0x38]
    // 0xadb128: StoreField: r0->field_2b = r1
    //     0xadb128: stur            w1, [x0, #0x2b]
    // 0xadb12c: StoreField: r0->field_2f = rZR
    //     0xadb12c: stur            xzr, [x0, #0x2f]
    // 0xadb130: ldur            x1, [fp, #-0x18]
    // 0xadb134: StoreField: r0->field_b = r1
    //     0xadb134: stur            w1, [x0, #0xb]
    // 0xadb138: LeaveFrame
    //     0xadb138: mov             SP, fp
    //     0xadb13c: ldp             fp, lr, [SP], #0x10
    // 0xadb140: ret
    //     0xadb140: ret             
    // 0xadb144: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb144: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb148: b               #0xadad24
    // 0xadb14c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb14c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb150: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb154: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb154: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb158: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb158: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb15c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb15c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb160: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb164: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb164: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _productBuildItem(/* No info */) {
    // ** addr: 0xadb168, size: 0x704
    // 0xadb168: EnterFrame
    //     0xadb168: stp             fp, lr, [SP, #-0x10]!
    //     0xadb16c: mov             fp, SP
    // 0xadb170: AllocStack(0x38)
    //     0xadb170: sub             SP, SP, #0x38
    // 0xadb174: SetupParameters(_CollectionFilterState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xadb174: mov             x0, x1
    //     0xadb178: stur            x1, [fp, #-8]
    //     0xadb17c: mov             x1, x5
    //     0xadb180: stur            x2, [fp, #-0x10]
    //     0xadb184: stur            x3, [fp, #-0x18]
    //     0xadb188: stur            x5, [fp, #-0x20]
    // 0xadb18c: CheckStackOverflow
    //     0xadb18c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb190: cmp             SP, x16
    //     0xadb194: b.ls            #0xadb838
    // 0xadb198: r1 = 3
    //     0xadb198: movz            x1, #0x3
    // 0xadb19c: r0 = AllocateContext()
    //     0xadb19c: bl              #0x16f6108  ; AllocateContextStub
    // 0xadb1a0: mov             x4, x0
    // 0xadb1a4: ldur            x3, [fp, #-8]
    // 0xadb1a8: stur            x4, [fp, #-0x28]
    // 0xadb1ac: StoreField: r4->field_f = r3
    //     0xadb1ac: stur            w3, [x4, #0xf]
    // 0xadb1b0: ldur            x0, [fp, #-0x18]
    // 0xadb1b4: StoreField: r4->field_13 = r0
    //     0xadb1b4: stur            w0, [x4, #0x13]
    // 0xadb1b8: r1 = ""
    //     0xadb1b8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadb1bc: ArrayStore: r4[0] = r1  ; List_4
    //     0xadb1bc: stur            w1, [x4, #0x17]
    // 0xadb1c0: LoadField: r1 = r0->field_7
    //     0xadb1c0: ldur            x1, [x0, #7]
    // 0xadb1c4: cmp             x1, #1
    // 0xadb1c8: b.gt            #0xadb408
    // 0xadb1cc: cmp             x1, #0
    // 0xadb1d0: b.gt            #0xadb2f0
    // 0xadb1d4: ldur            x5, [fp, #-0x10]
    // 0xadb1d8: LoadField: r2 = r3->field_13
    //     0xadb1d8: ldur            w2, [x3, #0x13]
    // 0xadb1dc: DecompressPointer r2
    //     0xadb1dc: add             x2, x2, HEAP, lsl #32
    // 0xadb1e0: cmp             w5, NULL
    // 0xadb1e4: b.ne            #0xadb1f4
    // 0xadb1e8: ldur            x6, [fp, #-0x20]
    // 0xadb1ec: r0 = Null
    //     0xadb1ec: mov             x0, NULL
    // 0xadb1f0: b               #0xadb248
    // 0xadb1f4: LoadField: r3 = r5->field_7
    //     0xadb1f4: ldur            w3, [x5, #7]
    // 0xadb1f8: DecompressPointer r3
    //     0xadb1f8: add             x3, x3, HEAP, lsl #32
    // 0xadb1fc: cmp             w3, NULL
    // 0xadb200: b.ne            #0xadb210
    // 0xadb204: ldur            x6, [fp, #-0x20]
    // 0xadb208: r0 = Null
    //     0xadb208: mov             x0, NULL
    // 0xadb20c: b               #0xadb248
    // 0xadb210: ldur            x6, [fp, #-0x20]
    // 0xadb214: LoadField: r0 = r3->field_b
    //     0xadb214: ldur            w0, [x3, #0xb]
    // 0xadb218: r1 = LoadInt32Instr(r0)
    //     0xadb218: sbfx            x1, x0, #1, #0x1f
    // 0xadb21c: mov             x0, x1
    // 0xadb220: mov             x1, x6
    // 0xadb224: cmp             x1, x0
    // 0xadb228: b.hs            #0xadb840
    // 0xadb22c: LoadField: r0 = r3->field_f
    //     0xadb22c: ldur            w0, [x3, #0xf]
    // 0xadb230: DecompressPointer r0
    //     0xadb230: add             x0, x0, HEAP, lsl #32
    // 0xadb234: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xadb234: add             x16, x0, x6, lsl #2
    //     0xadb238: ldur            w1, [x16, #0xf]
    // 0xadb23c: DecompressPointer r1
    //     0xadb23c: add             x1, x1, HEAP, lsl #32
    // 0xadb240: LoadField: r0 = r1->field_7
    //     0xadb240: ldur            w0, [x1, #7]
    // 0xadb244: DecompressPointer r0
    //     0xadb244: add             x0, x0, HEAP, lsl #32
    // 0xadb248: mov             x1, x2
    // 0xadb24c: mov             x2, x0
    // 0xadb250: r0 = contains()
    //     0xadb250: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xadb254: mov             x2, x0
    // 0xadb258: ldur            x4, [fp, #-0x10]
    // 0xadb25c: cmp             w4, NULL
    // 0xadb260: b.ne            #0xadb26c
    // 0xadb264: r0 = Null
    //     0xadb264: mov             x0, NULL
    // 0xadb268: b               #0xadb2bc
    // 0xadb26c: LoadField: r3 = r4->field_7
    //     0xadb26c: ldur            w3, [x4, #7]
    // 0xadb270: DecompressPointer r3
    //     0xadb270: add             x3, x3, HEAP, lsl #32
    // 0xadb274: cmp             w3, NULL
    // 0xadb278: b.ne            #0xadb284
    // 0xadb27c: r0 = Null
    //     0xadb27c: mov             x0, NULL
    // 0xadb280: b               #0xadb2bc
    // 0xadb284: ldur            x5, [fp, #-0x20]
    // 0xadb288: LoadField: r0 = r3->field_b
    //     0xadb288: ldur            w0, [x3, #0xb]
    // 0xadb28c: r1 = LoadInt32Instr(r0)
    //     0xadb28c: sbfx            x1, x0, #1, #0x1f
    // 0xadb290: mov             x0, x1
    // 0xadb294: mov             x1, x5
    // 0xadb298: cmp             x1, x0
    // 0xadb29c: b.hs            #0xadb844
    // 0xadb2a0: LoadField: r0 = r3->field_f
    //     0xadb2a0: ldur            w0, [x3, #0xf]
    // 0xadb2a4: DecompressPointer r0
    //     0xadb2a4: add             x0, x0, HEAP, lsl #32
    // 0xadb2a8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb2a8: add             x16, x0, x5, lsl #2
    //     0xadb2ac: ldur            w1, [x16, #0xf]
    // 0xadb2b0: DecompressPointer r1
    //     0xadb2b0: add             x1, x1, HEAP, lsl #32
    // 0xadb2b4: LoadField: r0 = r1->field_7
    //     0xadb2b4: ldur            w0, [x1, #7]
    // 0xadb2b8: DecompressPointer r0
    //     0xadb2b8: add             x0, x0, HEAP, lsl #32
    // 0xadb2bc: cmp             w0, NULL
    // 0xadb2c0: b.ne            #0xadb2c8
    // 0xadb2c4: r0 = ""
    //     0xadb2c4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadb2c8: ldur            x6, [fp, #-0x28]
    // 0xadb2cc: ArrayStore: r6[0] = r0  ; List_4
    //     0xadb2cc: stur            w0, [x6, #0x17]
    //     0xadb2d0: ldurb           w16, [x6, #-1]
    //     0xadb2d4: ldurb           w17, [x0, #-1]
    //     0xadb2d8: and             x16, x17, x16, lsr #2
    //     0xadb2dc: tst             x16, HEAP, lsr #32
    //     0xadb2e0: b.eq            #0xadb2e8
    //     0xadb2e4: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xadb2e8: mov             x0, x2
    // 0xadb2ec: b               #0xadb794
    // 0xadb2f0: mov             x6, x4
    // 0xadb2f4: ldur            x4, [fp, #-0x10]
    // 0xadb2f8: ldur            x5, [fp, #-0x20]
    // 0xadb2fc: LoadField: r2 = r3->field_13
    //     0xadb2fc: ldur            w2, [x3, #0x13]
    // 0xadb300: DecompressPointer r2
    //     0xadb300: add             x2, x2, HEAP, lsl #32
    // 0xadb304: cmp             w4, NULL
    // 0xadb308: b.ne            #0xadb314
    // 0xadb30c: r0 = Null
    //     0xadb30c: mov             x0, NULL
    // 0xadb310: b               #0xadb360
    // 0xadb314: LoadField: r3 = r4->field_f
    //     0xadb314: ldur            w3, [x4, #0xf]
    // 0xadb318: DecompressPointer r3
    //     0xadb318: add             x3, x3, HEAP, lsl #32
    // 0xadb31c: cmp             w3, NULL
    // 0xadb320: b.ne            #0xadb32c
    // 0xadb324: r0 = Null
    //     0xadb324: mov             x0, NULL
    // 0xadb328: b               #0xadb360
    // 0xadb32c: LoadField: r0 = r3->field_b
    //     0xadb32c: ldur            w0, [x3, #0xb]
    // 0xadb330: r1 = LoadInt32Instr(r0)
    //     0xadb330: sbfx            x1, x0, #1, #0x1f
    // 0xadb334: mov             x0, x1
    // 0xadb338: mov             x1, x5
    // 0xadb33c: cmp             x1, x0
    // 0xadb340: b.hs            #0xadb848
    // 0xadb344: LoadField: r0 = r3->field_f
    //     0xadb344: ldur            w0, [x3, #0xf]
    // 0xadb348: DecompressPointer r0
    //     0xadb348: add             x0, x0, HEAP, lsl #32
    // 0xadb34c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb34c: add             x16, x0, x5, lsl #2
    //     0xadb350: ldur            w1, [x16, #0xf]
    // 0xadb354: DecompressPointer r1
    //     0xadb354: add             x1, x1, HEAP, lsl #32
    // 0xadb358: LoadField: r0 = r1->field_7
    //     0xadb358: ldur            w0, [x1, #7]
    // 0xadb35c: DecompressPointer r0
    //     0xadb35c: add             x0, x0, HEAP, lsl #32
    // 0xadb360: mov             x1, x2
    // 0xadb364: mov             x2, x0
    // 0xadb368: r0 = contains()
    //     0xadb368: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xadb36c: mov             x2, x0
    // 0xadb370: ldur            x4, [fp, #-0x10]
    // 0xadb374: cmp             w4, NULL
    // 0xadb378: b.ne            #0xadb384
    // 0xadb37c: r0 = Null
    //     0xadb37c: mov             x0, NULL
    // 0xadb380: b               #0xadb3d4
    // 0xadb384: LoadField: r3 = r4->field_f
    //     0xadb384: ldur            w3, [x4, #0xf]
    // 0xadb388: DecompressPointer r3
    //     0xadb388: add             x3, x3, HEAP, lsl #32
    // 0xadb38c: cmp             w3, NULL
    // 0xadb390: b.ne            #0xadb39c
    // 0xadb394: r0 = Null
    //     0xadb394: mov             x0, NULL
    // 0xadb398: b               #0xadb3d4
    // 0xadb39c: ldur            x5, [fp, #-0x20]
    // 0xadb3a0: LoadField: r0 = r3->field_b
    //     0xadb3a0: ldur            w0, [x3, #0xb]
    // 0xadb3a4: r1 = LoadInt32Instr(r0)
    //     0xadb3a4: sbfx            x1, x0, #1, #0x1f
    // 0xadb3a8: mov             x0, x1
    // 0xadb3ac: mov             x1, x5
    // 0xadb3b0: cmp             x1, x0
    // 0xadb3b4: b.hs            #0xadb84c
    // 0xadb3b8: LoadField: r0 = r3->field_f
    //     0xadb3b8: ldur            w0, [x3, #0xf]
    // 0xadb3bc: DecompressPointer r0
    //     0xadb3bc: add             x0, x0, HEAP, lsl #32
    // 0xadb3c0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb3c0: add             x16, x0, x5, lsl #2
    //     0xadb3c4: ldur            w1, [x16, #0xf]
    // 0xadb3c8: DecompressPointer r1
    //     0xadb3c8: add             x1, x1, HEAP, lsl #32
    // 0xadb3cc: LoadField: r0 = r1->field_7
    //     0xadb3cc: ldur            w0, [x1, #7]
    // 0xadb3d0: DecompressPointer r0
    //     0xadb3d0: add             x0, x0, HEAP, lsl #32
    // 0xadb3d4: cmp             w0, NULL
    // 0xadb3d8: b.ne            #0xadb3e0
    // 0xadb3dc: r0 = ""
    //     0xadb3dc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadb3e0: ldur            x6, [fp, #-0x28]
    // 0xadb3e4: ArrayStore: r6[0] = r0  ; List_4
    //     0xadb3e4: stur            w0, [x6, #0x17]
    //     0xadb3e8: ldurb           w16, [x6, #-1]
    //     0xadb3ec: ldurb           w17, [x0, #-1]
    //     0xadb3f0: and             x16, x17, x16, lsr #2
    //     0xadb3f4: tst             x16, HEAP, lsr #32
    //     0xadb3f8: b.eq            #0xadb400
    //     0xadb3fc: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xadb400: mov             x0, x2
    // 0xadb404: b               #0xadb794
    // 0xadb408: mov             x6, x4
    // 0xadb40c: ldur            x4, [fp, #-0x10]
    // 0xadb410: ldur            x5, [fp, #-0x20]
    // 0xadb414: cmp             x1, #2
    // 0xadb418: b.gt            #0xadb528
    // 0xadb41c: LoadField: r2 = r3->field_13
    //     0xadb41c: ldur            w2, [x3, #0x13]
    // 0xadb420: DecompressPointer r2
    //     0xadb420: add             x2, x2, HEAP, lsl #32
    // 0xadb424: cmp             w4, NULL
    // 0xadb428: b.ne            #0xadb434
    // 0xadb42c: r0 = Null
    //     0xadb42c: mov             x0, NULL
    // 0xadb430: b               #0xadb480
    // 0xadb434: LoadField: r3 = r4->field_b
    //     0xadb434: ldur            w3, [x4, #0xb]
    // 0xadb438: DecompressPointer r3
    //     0xadb438: add             x3, x3, HEAP, lsl #32
    // 0xadb43c: cmp             w3, NULL
    // 0xadb440: b.ne            #0xadb44c
    // 0xadb444: r0 = Null
    //     0xadb444: mov             x0, NULL
    // 0xadb448: b               #0xadb480
    // 0xadb44c: LoadField: r0 = r3->field_b
    //     0xadb44c: ldur            w0, [x3, #0xb]
    // 0xadb450: r1 = LoadInt32Instr(r0)
    //     0xadb450: sbfx            x1, x0, #1, #0x1f
    // 0xadb454: mov             x0, x1
    // 0xadb458: mov             x1, x5
    // 0xadb45c: cmp             x1, x0
    // 0xadb460: b.hs            #0xadb850
    // 0xadb464: LoadField: r0 = r3->field_f
    //     0xadb464: ldur            w0, [x3, #0xf]
    // 0xadb468: DecompressPointer r0
    //     0xadb468: add             x0, x0, HEAP, lsl #32
    // 0xadb46c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb46c: add             x16, x0, x5, lsl #2
    //     0xadb470: ldur            w1, [x16, #0xf]
    // 0xadb474: DecompressPointer r1
    //     0xadb474: add             x1, x1, HEAP, lsl #32
    // 0xadb478: LoadField: r0 = r1->field_7
    //     0xadb478: ldur            w0, [x1, #7]
    // 0xadb47c: DecompressPointer r0
    //     0xadb47c: add             x0, x0, HEAP, lsl #32
    // 0xadb480: mov             x1, x2
    // 0xadb484: mov             x2, x0
    // 0xadb488: r0 = contains()
    //     0xadb488: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xadb48c: mov             x2, x0
    // 0xadb490: ldur            x4, [fp, #-0x10]
    // 0xadb494: cmp             w4, NULL
    // 0xadb498: b.ne            #0xadb4a4
    // 0xadb49c: r0 = Null
    //     0xadb49c: mov             x0, NULL
    // 0xadb4a0: b               #0xadb4f4
    // 0xadb4a4: LoadField: r3 = r4->field_b
    //     0xadb4a4: ldur            w3, [x4, #0xb]
    // 0xadb4a8: DecompressPointer r3
    //     0xadb4a8: add             x3, x3, HEAP, lsl #32
    // 0xadb4ac: cmp             w3, NULL
    // 0xadb4b0: b.ne            #0xadb4bc
    // 0xadb4b4: r0 = Null
    //     0xadb4b4: mov             x0, NULL
    // 0xadb4b8: b               #0xadb4f4
    // 0xadb4bc: ldur            x5, [fp, #-0x20]
    // 0xadb4c0: LoadField: r0 = r3->field_b
    //     0xadb4c0: ldur            w0, [x3, #0xb]
    // 0xadb4c4: r1 = LoadInt32Instr(r0)
    //     0xadb4c4: sbfx            x1, x0, #1, #0x1f
    // 0xadb4c8: mov             x0, x1
    // 0xadb4cc: mov             x1, x5
    // 0xadb4d0: cmp             x1, x0
    // 0xadb4d4: b.hs            #0xadb854
    // 0xadb4d8: LoadField: r0 = r3->field_f
    //     0xadb4d8: ldur            w0, [x3, #0xf]
    // 0xadb4dc: DecompressPointer r0
    //     0xadb4dc: add             x0, x0, HEAP, lsl #32
    // 0xadb4e0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb4e0: add             x16, x0, x5, lsl #2
    //     0xadb4e4: ldur            w1, [x16, #0xf]
    // 0xadb4e8: DecompressPointer r1
    //     0xadb4e8: add             x1, x1, HEAP, lsl #32
    // 0xadb4ec: LoadField: r0 = r1->field_7
    //     0xadb4ec: ldur            w0, [x1, #7]
    // 0xadb4f0: DecompressPointer r0
    //     0xadb4f0: add             x0, x0, HEAP, lsl #32
    // 0xadb4f4: cmp             w0, NULL
    // 0xadb4f8: b.ne            #0xadb500
    // 0xadb4fc: r0 = ""
    //     0xadb4fc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadb500: ldur            x6, [fp, #-0x28]
    // 0xadb504: ArrayStore: r6[0] = r0  ; List_4
    //     0xadb504: stur            w0, [x6, #0x17]
    //     0xadb508: ldurb           w16, [x6, #-1]
    //     0xadb50c: ldurb           w17, [x0, #-1]
    //     0xadb510: and             x16, x17, x16, lsr #2
    //     0xadb514: tst             x16, HEAP, lsr #32
    //     0xadb518: b.eq            #0xadb520
    //     0xadb51c: bl              #0x16f5928  ; WriteBarrierWrappersStub
    // 0xadb520: mov             x0, x2
    // 0xadb524: b               #0xadb794
    // 0xadb528: cmp             w4, NULL
    // 0xadb52c: b.ne            #0xadb538
    // 0xadb530: r0 = Null
    //     0xadb530: mov             x0, NULL
    // 0xadb534: b               #0xadb584
    // 0xadb538: LoadField: r2 = r4->field_13
    //     0xadb538: ldur            w2, [x4, #0x13]
    // 0xadb53c: DecompressPointer r2
    //     0xadb53c: add             x2, x2, HEAP, lsl #32
    // 0xadb540: cmp             w2, NULL
    // 0xadb544: b.ne            #0xadb550
    // 0xadb548: r0 = Null
    //     0xadb548: mov             x0, NULL
    // 0xadb54c: b               #0xadb584
    // 0xadb550: LoadField: r0 = r2->field_b
    //     0xadb550: ldur            w0, [x2, #0xb]
    // 0xadb554: r1 = LoadInt32Instr(r0)
    //     0xadb554: sbfx            x1, x0, #1, #0x1f
    // 0xadb558: mov             x0, x1
    // 0xadb55c: mov             x1, x5
    // 0xadb560: cmp             x1, x0
    // 0xadb564: b.hs            #0xadb858
    // 0xadb568: LoadField: r0 = r2->field_f
    //     0xadb568: ldur            w0, [x2, #0xf]
    // 0xadb56c: DecompressPointer r0
    //     0xadb56c: add             x0, x0, HEAP, lsl #32
    // 0xadb570: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb570: add             x16, x0, x5, lsl #2
    //     0xadb574: ldur            w1, [x16, #0xf]
    // 0xadb578: DecompressPointer r1
    //     0xadb578: add             x1, x1, HEAP, lsl #32
    // 0xadb57c: LoadField: r0 = r1->field_7
    //     0xadb57c: ldur            w0, [x1, #7]
    // 0xadb580: DecompressPointer r0
    //     0xadb580: add             x0, x0, HEAP, lsl #32
    // 0xadb584: stur            x0, [fp, #-0x18]
    // 0xadb588: r1 = Null
    //     0xadb588: mov             x1, NULL
    // 0xadb58c: r2 = 6
    //     0xadb58c: movz            x2, #0x6
    // 0xadb590: r0 = AllocateArray()
    //     0xadb590: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadb594: mov             x2, x0
    // 0xadb598: ldur            x0, [fp, #-0x18]
    // 0xadb59c: StoreField: r2->field_f = r0
    //     0xadb59c: stur            w0, [x2, #0xf]
    // 0xadb5a0: r16 = " - "
    //     0xadb5a0: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xadb5a4: ldr             x16, [x16, #0xc08]
    // 0xadb5a8: StoreField: r2->field_13 = r16
    //     0xadb5a8: stur            w16, [x2, #0x13]
    // 0xadb5ac: ldur            x3, [fp, #-0x10]
    // 0xadb5b0: cmp             w3, NULL
    // 0xadb5b4: b.ne            #0xadb5c4
    // 0xadb5b8: ldur            x5, [fp, #-0x20]
    // 0xadb5bc: r0 = Null
    //     0xadb5bc: mov             x0, NULL
    // 0xadb5c0: b               #0xadb618
    // 0xadb5c4: LoadField: r4 = r3->field_13
    //     0xadb5c4: ldur            w4, [x3, #0x13]
    // 0xadb5c8: DecompressPointer r4
    //     0xadb5c8: add             x4, x4, HEAP, lsl #32
    // 0xadb5cc: cmp             w4, NULL
    // 0xadb5d0: b.ne            #0xadb5e0
    // 0xadb5d4: ldur            x5, [fp, #-0x20]
    // 0xadb5d8: r0 = Null
    //     0xadb5d8: mov             x0, NULL
    // 0xadb5dc: b               #0xadb618
    // 0xadb5e0: ldur            x5, [fp, #-0x20]
    // 0xadb5e4: LoadField: r0 = r4->field_b
    //     0xadb5e4: ldur            w0, [x4, #0xb]
    // 0xadb5e8: r1 = LoadInt32Instr(r0)
    //     0xadb5e8: sbfx            x1, x0, #1, #0x1f
    // 0xadb5ec: mov             x0, x1
    // 0xadb5f0: mov             x1, x5
    // 0xadb5f4: cmp             x1, x0
    // 0xadb5f8: b.hs            #0xadb85c
    // 0xadb5fc: LoadField: r0 = r4->field_f
    //     0xadb5fc: ldur            w0, [x4, #0xf]
    // 0xadb600: DecompressPointer r0
    //     0xadb600: add             x0, x0, HEAP, lsl #32
    // 0xadb604: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb604: add             x16, x0, x5, lsl #2
    //     0xadb608: ldur            w1, [x16, #0xf]
    // 0xadb60c: DecompressPointer r1
    //     0xadb60c: add             x1, x1, HEAP, lsl #32
    // 0xadb610: LoadField: r0 = r1->field_b
    //     0xadb610: ldur            w0, [x1, #0xb]
    // 0xadb614: DecompressPointer r0
    //     0xadb614: add             x0, x0, HEAP, lsl #32
    // 0xadb618: cmp             w0, NULL
    // 0xadb61c: b.ne            #0xadb628
    // 0xadb620: r0 = "above"
    //     0xadb620: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xadb624: ldr             x0, [x0, #0xc10]
    // 0xadb628: ArrayStore: r2[0] = r0  ; List_4
    //     0xadb628: stur            w0, [x2, #0x17]
    // 0xadb62c: str             x2, [SP]
    // 0xadb630: r0 = _interpolate()
    //     0xadb630: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xadb634: mov             x4, x0
    // 0xadb638: ldur            x3, [fp, #-0x10]
    // 0xadb63c: stur            x4, [fp, #-0x30]
    // 0xadb640: cmp             w3, NULL
    // 0xadb644: b.ne            #0xadb654
    // 0xadb648: ldur            x5, [fp, #-0x20]
    // 0xadb64c: r0 = Null
    //     0xadb64c: mov             x0, NULL
    // 0xadb650: b               #0xadb6a8
    // 0xadb654: LoadField: r2 = r3->field_13
    //     0xadb654: ldur            w2, [x3, #0x13]
    // 0xadb658: DecompressPointer r2
    //     0xadb658: add             x2, x2, HEAP, lsl #32
    // 0xadb65c: cmp             w2, NULL
    // 0xadb660: b.ne            #0xadb670
    // 0xadb664: ldur            x5, [fp, #-0x20]
    // 0xadb668: r0 = Null
    //     0xadb668: mov             x0, NULL
    // 0xadb66c: b               #0xadb6a8
    // 0xadb670: ldur            x5, [fp, #-0x20]
    // 0xadb674: LoadField: r0 = r2->field_b
    //     0xadb674: ldur            w0, [x2, #0xb]
    // 0xadb678: r1 = LoadInt32Instr(r0)
    //     0xadb678: sbfx            x1, x0, #1, #0x1f
    // 0xadb67c: mov             x0, x1
    // 0xadb680: mov             x1, x5
    // 0xadb684: cmp             x1, x0
    // 0xadb688: b.hs            #0xadb860
    // 0xadb68c: LoadField: r0 = r2->field_f
    //     0xadb68c: ldur            w0, [x2, #0xf]
    // 0xadb690: DecompressPointer r0
    //     0xadb690: add             x0, x0, HEAP, lsl #32
    // 0xadb694: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadb694: add             x16, x0, x5, lsl #2
    //     0xadb698: ldur            w1, [x16, #0xf]
    // 0xadb69c: DecompressPointer r1
    //     0xadb69c: add             x1, x1, HEAP, lsl #32
    // 0xadb6a0: LoadField: r0 = r1->field_7
    //     0xadb6a0: ldur            w0, [x1, #7]
    // 0xadb6a4: DecompressPointer r0
    //     0xadb6a4: add             x0, x0, HEAP, lsl #32
    // 0xadb6a8: stur            x0, [fp, #-0x18]
    // 0xadb6ac: r1 = Null
    //     0xadb6ac: mov             x1, NULL
    // 0xadb6b0: r2 = 6
    //     0xadb6b0: movz            x2, #0x6
    // 0xadb6b4: r0 = AllocateArray()
    //     0xadb6b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadb6b8: mov             x2, x0
    // 0xadb6bc: ldur            x0, [fp, #-0x18]
    // 0xadb6c0: StoreField: r2->field_f = r0
    //     0xadb6c0: stur            w0, [x2, #0xf]
    // 0xadb6c4: r16 = " - "
    //     0xadb6c4: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0xadb6c8: ldr             x16, [x16, #0xc08]
    // 0xadb6cc: StoreField: r2->field_13 = r16
    //     0xadb6cc: stur            w16, [x2, #0x13]
    // 0xadb6d0: ldur            x0, [fp, #-0x10]
    // 0xadb6d4: cmp             w0, NULL
    // 0xadb6d8: b.ne            #0xadb6e4
    // 0xadb6dc: r0 = Null
    //     0xadb6dc: mov             x0, NULL
    // 0xadb6e0: b               #0xadb734
    // 0xadb6e4: LoadField: r3 = r0->field_13
    //     0xadb6e4: ldur            w3, [x0, #0x13]
    // 0xadb6e8: DecompressPointer r3
    //     0xadb6e8: add             x3, x3, HEAP, lsl #32
    // 0xadb6ec: cmp             w3, NULL
    // 0xadb6f0: b.ne            #0xadb6fc
    // 0xadb6f4: r0 = Null
    //     0xadb6f4: mov             x0, NULL
    // 0xadb6f8: b               #0xadb734
    // 0xadb6fc: ldur            x4, [fp, #-0x20]
    // 0xadb700: LoadField: r0 = r3->field_b
    //     0xadb700: ldur            w0, [x3, #0xb]
    // 0xadb704: r1 = LoadInt32Instr(r0)
    //     0xadb704: sbfx            x1, x0, #1, #0x1f
    // 0xadb708: mov             x0, x1
    // 0xadb70c: mov             x1, x4
    // 0xadb710: cmp             x1, x0
    // 0xadb714: b.hs            #0xadb864
    // 0xadb718: LoadField: r0 = r3->field_f
    //     0xadb718: ldur            w0, [x3, #0xf]
    // 0xadb71c: DecompressPointer r0
    //     0xadb71c: add             x0, x0, HEAP, lsl #32
    // 0xadb720: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xadb720: add             x16, x0, x4, lsl #2
    //     0xadb724: ldur            w1, [x16, #0xf]
    // 0xadb728: DecompressPointer r1
    //     0xadb728: add             x1, x1, HEAP, lsl #32
    // 0xadb72c: LoadField: r0 = r1->field_b
    //     0xadb72c: ldur            w0, [x1, #0xb]
    // 0xadb730: DecompressPointer r0
    //     0xadb730: add             x0, x0, HEAP, lsl #32
    // 0xadb734: cmp             w0, NULL
    // 0xadb738: b.ne            #0xadb748
    // 0xadb73c: r3 = "above"
    //     0xadb73c: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0xadb740: ldr             x3, [x3, #0xc10]
    // 0xadb744: b               #0xadb74c
    // 0xadb748: mov             x3, x0
    // 0xadb74c: ldur            x0, [fp, #-8]
    // 0xadb750: ldur            x1, [fp, #-0x28]
    // 0xadb754: ArrayStore: r2[0] = r3  ; List_4
    //     0xadb754: stur            w3, [x2, #0x17]
    // 0xadb758: str             x2, [SP]
    // 0xadb75c: r0 = _interpolate()
    //     0xadb75c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xadb760: ldur            x3, [fp, #-0x28]
    // 0xadb764: ArrayStore: r3[0] = r0  ; List_4
    //     0xadb764: stur            w0, [x3, #0x17]
    //     0xadb768: ldurb           w16, [x3, #-1]
    //     0xadb76c: ldurb           w17, [x0, #-1]
    //     0xadb770: and             x16, x17, x16, lsr #2
    //     0xadb774: tst             x16, HEAP, lsr #32
    //     0xadb778: b.eq            #0xadb780
    //     0xadb77c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xadb780: ldur            x0, [fp, #-8]
    // 0xadb784: LoadField: r1 = r0->field_13
    //     0xadb784: ldur            w1, [x0, #0x13]
    // 0xadb788: DecompressPointer r1
    //     0xadb788: add             x1, x1, HEAP, lsl #32
    // 0xadb78c: ldur            x2, [fp, #-0x30]
    // 0xadb790: r0 = contains()
    //     0xadb790: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xadb794: stur            x0, [fp, #-8]
    // 0xadb798: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xadb798: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadb79c: ldr             x0, [x0, #0x1c80]
    //     0xadb7a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadb7a4: cmp             w0, w16
    //     0xadb7a8: b.ne            #0xadb7b4
    //     0xadb7ac: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xadb7b0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xadb7b4: r0 = GetNavigation.context()
    //     0xadb7b4: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0xadb7b8: cmp             w0, NULL
    // 0xadb7bc: b.eq            #0xadb868
    // 0xadb7c0: mov             x1, x0
    // 0xadb7c4: r0 = of()
    //     0xadb7c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadb7c8: LoadField: r1 = r0->field_5b
    //     0xadb7c8: ldur            w1, [x0, #0x5b]
    // 0xadb7cc: DecompressPointer r1
    //     0xadb7cc: add             x1, x1, HEAP, lsl #32
    // 0xadb7d0: stur            x1, [fp, #-0x10]
    // 0xadb7d4: r0 = Checkbox()
    //     0xadb7d4: bl              #0xa2e5f0  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xadb7d8: mov             x3, x0
    // 0xadb7dc: ldur            x0, [fp, #-8]
    // 0xadb7e0: stur            x3, [fp, #-0x18]
    // 0xadb7e4: StoreField: r3->field_b = r0
    //     0xadb7e4: stur            w0, [x3, #0xb]
    // 0xadb7e8: r0 = false
    //     0xadb7e8: add             x0, NULL, #0x30  ; false
    // 0xadb7ec: StoreField: r3->field_23 = r0
    //     0xadb7ec: stur            w0, [x3, #0x23]
    // 0xadb7f0: ldur            x2, [fp, #-0x28]
    // 0xadb7f4: r1 = Function '<anonymous closure>':.
    //     0xadb7f4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58988] AnonymousClosure: (0xadb86c), in [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem (0xadb168)
    //     0xadb7f8: ldr             x1, [x1, #0x988]
    // 0xadb7fc: r0 = AllocateClosure()
    //     0xadb7fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xadb800: mov             x1, x0
    // 0xadb804: ldur            x0, [fp, #-0x18]
    // 0xadb808: StoreField: r0->field_f = r1
    //     0xadb808: stur            w1, [x0, #0xf]
    // 0xadb80c: ldur            x1, [fp, #-0x10]
    // 0xadb810: ArrayStore: r0[0] = r1  ; List_4
    //     0xadb810: stur            w1, [x0, #0x17]
    // 0xadb814: r1 = false
    //     0xadb814: add             x1, NULL, #0x30  ; false
    // 0xadb818: StoreField: r0->field_43 = r1
    //     0xadb818: stur            w1, [x0, #0x43]
    // 0xadb81c: StoreField: r0->field_4f = r1
    //     0xadb81c: stur            w1, [x0, #0x4f]
    // 0xadb820: r1 = Instance__CheckboxType
    //     0xadb820: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d80] Obj!_CheckboxType@d74601
    //     0xadb824: ldr             x1, [x1, #0xd80]
    // 0xadb828: StoreField: r0->field_57 = r1
    //     0xadb828: stur            w1, [x0, #0x57]
    // 0xadb82c: LeaveFrame
    //     0xadb82c: mov             SP, fp
    //     0xadb830: ldp             fp, lr, [SP], #0x10
    // 0xadb834: ret
    //     0xadb834: ret             
    // 0xadb838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb838: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb83c: b               #0xadb198
    // 0xadb840: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb840: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb844: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb844: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb848: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb848: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb84c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb84c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb850: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb850: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb854: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb854: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb858: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb858: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb85c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb85c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb860: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb860: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb864: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadb864: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadb868: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb868: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool?) {
    // ** addr: 0xadb86c, size: 0x68
    // 0xadb86c: EnterFrame
    //     0xadb86c: stp             fp, lr, [SP, #-0x10]!
    //     0xadb870: mov             fp, SP
    // 0xadb874: ldr             x0, [fp, #0x18]
    // 0xadb878: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadb878: ldur            w1, [x0, #0x17]
    // 0xadb87c: DecompressPointer r1
    //     0xadb87c: add             x1, x1, HEAP, lsl #32
    // 0xadb880: CheckStackOverflow
    //     0xadb880: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb884: cmp             SP, x16
    //     0xadb888: b.ls            #0xadb8c8
    // 0xadb88c: LoadField: r0 = r1->field_f
    //     0xadb88c: ldur            w0, [x1, #0xf]
    // 0xadb890: DecompressPointer r0
    //     0xadb890: add             x0, x0, HEAP, lsl #32
    // 0xadb894: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xadb894: ldur            w2, [x1, #0x17]
    // 0xadb898: DecompressPointer r2
    //     0xadb898: add             x2, x2, HEAP, lsl #32
    // 0xadb89c: ldr             x3, [fp, #0x10]
    // 0xadb8a0: cmp             w3, NULL
    // 0xadb8a4: b.eq            #0xadb8d0
    // 0xadb8a8: LoadField: r5 = r1->field_13
    //     0xadb8a8: ldur            w5, [x1, #0x13]
    // 0xadb8ac: DecompressPointer r5
    //     0xadb8ac: add             x5, x5, HEAP, lsl #32
    // 0xadb8b0: mov             x1, x0
    // 0xadb8b4: r0 = _onItemCheckedChange()
    //     0xadb8b4: bl              #0xadb8d4  ; [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::_onItemCheckedChange
    // 0xadb8b8: r0 = Null
    //     0xadb8b8: mov             x0, NULL
    // 0xadb8bc: LeaveFrame
    //     0xadb8bc: mov             SP, fp
    //     0xadb8c0: ldp             fp, lr, [SP], #0x10
    // 0xadb8c4: ret
    //     0xadb8c4: ret             
    // 0xadb8c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb8c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb8cc: b               #0xadb88c
    // 0xadb8d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb8d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _onItemCheckedChange(/* No info */) {
    // ** addr: 0xadb8d4, size: 0x88
    // 0xadb8d4: EnterFrame
    //     0xadb8d4: stp             fp, lr, [SP, #-0x10]!
    //     0xadb8d8: mov             fp, SP
    // 0xadb8dc: AllocStack(0x20)
    //     0xadb8dc: sub             SP, SP, #0x20
    // 0xadb8e0: SetupParameters(_CollectionFilterState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xadb8e0: stur            x1, [fp, #-8]
    //     0xadb8e4: stur            x2, [fp, #-0x10]
    //     0xadb8e8: stur            x3, [fp, #-0x18]
    //     0xadb8ec: stur            x5, [fp, #-0x20]
    // 0xadb8f0: CheckStackOverflow
    //     0xadb8f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb8f4: cmp             SP, x16
    //     0xadb8f8: b.ls            #0xadb954
    // 0xadb8fc: r1 = 4
    //     0xadb8fc: movz            x1, #0x4
    // 0xadb900: r0 = AllocateContext()
    //     0xadb900: bl              #0x16f6108  ; AllocateContextStub
    // 0xadb904: mov             x1, x0
    // 0xadb908: ldur            x0, [fp, #-8]
    // 0xadb90c: StoreField: r1->field_f = r0
    //     0xadb90c: stur            w0, [x1, #0xf]
    // 0xadb910: ldur            x2, [fp, #-0x10]
    // 0xadb914: StoreField: r1->field_13 = r2
    //     0xadb914: stur            w2, [x1, #0x13]
    // 0xadb918: ldur            x2, [fp, #-0x18]
    // 0xadb91c: ArrayStore: r1[0] = r2  ; List_4
    //     0xadb91c: stur            w2, [x1, #0x17]
    // 0xadb920: ldur            x2, [fp, #-0x20]
    // 0xadb924: StoreField: r1->field_1b = r2
    //     0xadb924: stur            w2, [x1, #0x1b]
    // 0xadb928: mov             x2, x1
    // 0xadb92c: r1 = Function '<anonymous closure>':.
    //     0xadb92c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58990] AnonymousClosure: (0xa2e6ec), in [package:customer_app/app/presentation/views/line/collections/widgets/collection_filter.dart] _CollectionFilterState::_onItemCheckedChange (0xa2ed14)
    //     0xadb930: ldr             x1, [x1, #0x990]
    // 0xadb934: r0 = AllocateClosure()
    //     0xadb934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xadb938: ldur            x1, [fp, #-8]
    // 0xadb93c: mov             x2, x0
    // 0xadb940: r0 = setState()
    //     0xadb940: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xadb944: r0 = Null
    //     0xadb944: mov             x0, NULL
    // 0xadb948: LeaveFrame
    //     0xadb948: mov             SP, fp
    //     0xadb94c: ldp             fp, lr, [SP], #0x10
    // 0xadb950: ret
    //     0xadb950: ret             
    // 0xadb954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb954: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb958: b               #0xadb8fc
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadb95c, size: 0x38c
    // 0xadb95c: EnterFrame
    //     0xadb95c: stp             fp, lr, [SP, #-0x10]!
    //     0xadb960: mov             fp, SP
    // 0xadb964: AllocStack(0x40)
    //     0xadb964: sub             SP, SP, #0x40
    // 0xadb968: SetupParameters()
    //     0xadb968: ldr             x0, [fp, #0x20]
    //     0xadb96c: ldur            w4, [x0, #0x17]
    //     0xadb970: add             x4, x4, HEAP, lsl #32
    //     0xadb974: stur            x4, [fp, #-0x10]
    // 0xadb978: CheckStackOverflow
    //     0xadb978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb97c: cmp             SP, x16
    //     0xadb980: b.ls            #0xadbccc
    // 0xadb984: LoadField: r1 = r4->field_f
    //     0xadb984: ldur            w1, [x4, #0xf]
    // 0xadb988: DecompressPointer r1
    //     0xadb988: add             x1, x1, HEAP, lsl #32
    // 0xadb98c: LoadField: r0 = r1->field_b
    //     0xadb98c: ldur            w0, [x1, #0xb]
    // 0xadb990: DecompressPointer r0
    //     0xadb990: add             x0, x0, HEAP, lsl #32
    // 0xadb994: cmp             w0, NULL
    // 0xadb998: b.eq            #0xadbcd4
    // 0xadb99c: LoadField: r2 = r0->field_b
    //     0xadb99c: ldur            w2, [x0, #0xb]
    // 0xadb9a0: DecompressPointer r2
    //     0xadb9a0: add             x2, x2, HEAP, lsl #32
    // 0xadb9a4: ldr             x0, [fp, #0x10]
    // 0xadb9a8: r6 = LoadInt32Instr(r0)
    //     0xadb9a8: sbfx            x6, x0, #1, #0x1f
    //     0xadb9ac: tbz             w0, #0, #0xadb9b4
    //     0xadb9b0: ldur            x6, [x0, #7]
    // 0xadb9b4: mov             x5, x6
    // 0xadb9b8: stur            x6, [fp, #-8]
    // 0xadb9bc: r3 = Instance_ItemFilterType
    //     0xadb9bc: add             x3, PP, #0x58, lsl #12  ; [pp+0x58998] Obj!ItemFilterType@d753c1
    //     0xadb9c0: ldr             x3, [x3, #0x998]
    // 0xadb9c4: r0 = _productBuildItem()
    //     0xadb9c4: bl              #0xadb168  ; [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xadb9c8: mov             x3, x0
    // 0xadb9cc: ldur            x2, [fp, #-0x10]
    // 0xadb9d0: stur            x3, [fp, #-0x20]
    // 0xadb9d4: LoadField: r0 = r2->field_f
    //     0xadb9d4: ldur            w0, [x2, #0xf]
    // 0xadb9d8: DecompressPointer r0
    //     0xadb9d8: add             x0, x0, HEAP, lsl #32
    // 0xadb9dc: LoadField: r1 = r0->field_b
    //     0xadb9dc: ldur            w1, [x0, #0xb]
    // 0xadb9e0: DecompressPointer r1
    //     0xadb9e0: add             x1, x1, HEAP, lsl #32
    // 0xadb9e4: cmp             w1, NULL
    // 0xadb9e8: b.eq            #0xadbcd8
    // 0xadb9ec: LoadField: r0 = r1->field_b
    //     0xadb9ec: ldur            w0, [x1, #0xb]
    // 0xadb9f0: DecompressPointer r0
    //     0xadb9f0: add             x0, x0, HEAP, lsl #32
    // 0xadb9f4: cmp             w0, NULL
    // 0xadb9f8: b.ne            #0xadba08
    // 0xadb9fc: ldur            x5, [fp, #-8]
    // 0xadba00: r0 = Null
    //     0xadba00: mov             x0, NULL
    // 0xadba04: b               #0xadba5c
    // 0xadba08: LoadField: r4 = r0->field_b
    //     0xadba08: ldur            w4, [x0, #0xb]
    // 0xadba0c: DecompressPointer r4
    //     0xadba0c: add             x4, x4, HEAP, lsl #32
    // 0xadba10: cmp             w4, NULL
    // 0xadba14: b.ne            #0xadba24
    // 0xadba18: ldur            x5, [fp, #-8]
    // 0xadba1c: r0 = Null
    //     0xadba1c: mov             x0, NULL
    // 0xadba20: b               #0xadba5c
    // 0xadba24: ldur            x5, [fp, #-8]
    // 0xadba28: LoadField: r0 = r4->field_b
    //     0xadba28: ldur            w0, [x4, #0xb]
    // 0xadba2c: r1 = LoadInt32Instr(r0)
    //     0xadba2c: sbfx            x1, x0, #1, #0x1f
    // 0xadba30: mov             x0, x1
    // 0xadba34: mov             x1, x5
    // 0xadba38: cmp             x1, x0
    // 0xadba3c: b.hs            #0xadbcdc
    // 0xadba40: LoadField: r0 = r4->field_f
    //     0xadba40: ldur            w0, [x4, #0xf]
    // 0xadba44: DecompressPointer r0
    //     0xadba44: add             x0, x0, HEAP, lsl #32
    // 0xadba48: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadba48: add             x16, x0, x5, lsl #2
    //     0xadba4c: ldur            w1, [x16, #0xf]
    // 0xadba50: DecompressPointer r1
    //     0xadba50: add             x1, x1, HEAP, lsl #32
    // 0xadba54: LoadField: r0 = r1->field_7
    //     0xadba54: ldur            w0, [x1, #7]
    // 0xadba58: DecompressPointer r0
    //     0xadba58: add             x0, x0, HEAP, lsl #32
    // 0xadba5c: cmp             w0, NULL
    // 0xadba60: b.ne            #0xadba68
    // 0xadba64: r0 = ""
    //     0xadba64: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadba68: ldr             x1, [fp, #0x18]
    // 0xadba6c: stur            x0, [fp, #-0x18]
    // 0xadba70: r0 = of()
    //     0xadba70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadba74: LoadField: r1 = r0->field_87
    //     0xadba74: ldur            w1, [x0, #0x87]
    // 0xadba78: DecompressPointer r1
    //     0xadba78: add             x1, x1, HEAP, lsl #32
    // 0xadba7c: LoadField: r0 = r1->field_2b
    //     0xadba7c: ldur            w0, [x1, #0x2b]
    // 0xadba80: DecompressPointer r0
    //     0xadba80: add             x0, x0, HEAP, lsl #32
    // 0xadba84: r16 = 14.000000
    //     0xadba84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xadba88: ldr             x16, [x16, #0x1d8]
    // 0xadba8c: r30 = Instance_Color
    //     0xadba8c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadba90: stp             lr, x16, [SP]
    // 0xadba94: mov             x1, x0
    // 0xadba98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadba98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadba9c: ldr             x4, [x4, #0xaa0]
    // 0xadbaa0: r0 = copyWith()
    //     0xadbaa0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadbaa4: stur            x0, [fp, #-0x28]
    // 0xadbaa8: r0 = Text()
    //     0xadbaa8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadbaac: mov             x2, x0
    // 0xadbab0: ldur            x0, [fp, #-0x18]
    // 0xadbab4: stur            x2, [fp, #-0x30]
    // 0xadbab8: StoreField: r2->field_b = r0
    //     0xadbab8: stur            w0, [x2, #0xb]
    // 0xadbabc: ldur            x0, [fp, #-0x28]
    // 0xadbac0: StoreField: r2->field_13 = r0
    //     0xadbac0: stur            w0, [x2, #0x13]
    // 0xadbac4: ldur            x0, [fp, #-0x10]
    // 0xadbac8: LoadField: r1 = r0->field_f
    //     0xadbac8: ldur            w1, [x0, #0xf]
    // 0xadbacc: DecompressPointer r1
    //     0xadbacc: add             x1, x1, HEAP, lsl #32
    // 0xadbad0: LoadField: r0 = r1->field_b
    //     0xadbad0: ldur            w0, [x1, #0xb]
    // 0xadbad4: DecompressPointer r0
    //     0xadbad4: add             x0, x0, HEAP, lsl #32
    // 0xadbad8: cmp             w0, NULL
    // 0xadbadc: b.eq            #0xadbce0
    // 0xadbae0: LoadField: r1 = r0->field_b
    //     0xadbae0: ldur            w1, [x0, #0xb]
    // 0xadbae4: DecompressPointer r1
    //     0xadbae4: add             x1, x1, HEAP, lsl #32
    // 0xadbae8: cmp             w1, NULL
    // 0xadbaec: b.ne            #0xadbaf8
    // 0xadbaf0: r0 = Null
    //     0xadbaf0: mov             x0, NULL
    // 0xadbaf4: b               #0xadbb74
    // 0xadbaf8: LoadField: r3 = r1->field_b
    //     0xadbaf8: ldur            w3, [x1, #0xb]
    // 0xadbafc: DecompressPointer r3
    //     0xadbafc: add             x3, x3, HEAP, lsl #32
    // 0xadbb00: cmp             w3, NULL
    // 0xadbb04: b.ne            #0xadbb10
    // 0xadbb08: r0 = Null
    //     0xadbb08: mov             x0, NULL
    // 0xadbb0c: b               #0xadbb74
    // 0xadbb10: ldur            x4, [fp, #-8]
    // 0xadbb14: LoadField: r0 = r3->field_b
    //     0xadbb14: ldur            w0, [x3, #0xb]
    // 0xadbb18: r1 = LoadInt32Instr(r0)
    //     0xadbb18: sbfx            x1, x0, #1, #0x1f
    // 0xadbb1c: mov             x0, x1
    // 0xadbb20: mov             x1, x4
    // 0xadbb24: cmp             x1, x0
    // 0xadbb28: b.hs            #0xadbce4
    // 0xadbb2c: LoadField: r0 = r3->field_f
    //     0xadbb2c: ldur            w0, [x3, #0xf]
    // 0xadbb30: DecompressPointer r0
    //     0xadbb30: add             x0, x0, HEAP, lsl #32
    // 0xadbb34: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xadbb34: add             x16, x0, x4, lsl #2
    //     0xadbb38: ldur            w1, [x16, #0xf]
    // 0xadbb3c: DecompressPointer r1
    //     0xadbb3c: add             x1, x1, HEAP, lsl #32
    // 0xadbb40: LoadField: r0 = r1->field_b
    //     0xadbb40: ldur            w0, [x1, #0xb]
    // 0xadbb44: DecompressPointer r0
    //     0xadbb44: add             x0, x0, HEAP, lsl #32
    // 0xadbb48: r1 = 60
    //     0xadbb48: movz            x1, #0x3c
    // 0xadbb4c: branchIfSmi(r0, 0xadbb58)
    //     0xadbb4c: tbz             w0, #0, #0xadbb58
    // 0xadbb50: r1 = LoadClassIdInstr(r0)
    //     0xadbb50: ldur            x1, [x0, #-1]
    //     0xadbb54: ubfx            x1, x1, #0xc, #0x14
    // 0xadbb58: str             x0, [SP]
    // 0xadbb5c: mov             x0, x1
    // 0xadbb60: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xadbb60: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xadbb64: r0 = GDT[cid_x0 + 0x2700]()
    //     0xadbb64: movz            x17, #0x2700
    //     0xadbb68: add             lr, x0, x17
    //     0xadbb6c: ldr             lr, [x21, lr, lsl #3]
    //     0xadbb70: blr             lr
    // 0xadbb74: cmp             w0, NULL
    // 0xadbb78: b.ne            #0xadbb84
    // 0xadbb7c: r3 = ""
    //     0xadbb7c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadbb80: b               #0xadbb88
    // 0xadbb84: mov             x3, x0
    // 0xadbb88: ldur            x2, [fp, #-0x20]
    // 0xadbb8c: ldur            x0, [fp, #-0x30]
    // 0xadbb90: ldr             x1, [fp, #0x18]
    // 0xadbb94: stur            x3, [fp, #-0x10]
    // 0xadbb98: r0 = of()
    //     0xadbb98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadbb9c: LoadField: r1 = r0->field_87
    //     0xadbb9c: ldur            w1, [x0, #0x87]
    // 0xadbba0: DecompressPointer r1
    //     0xadbba0: add             x1, x1, HEAP, lsl #32
    // 0xadbba4: LoadField: r0 = r1->field_2b
    //     0xadbba4: ldur            w0, [x1, #0x2b]
    // 0xadbba8: DecompressPointer r0
    //     0xadbba8: add             x0, x0, HEAP, lsl #32
    // 0xadbbac: ldr             x1, [fp, #0x18]
    // 0xadbbb0: stur            x0, [fp, #-0x18]
    // 0xadbbb4: r0 = of()
    //     0xadbbb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadbbb8: LoadField: r1 = r0->field_5b
    //     0xadbbb8: ldur            w1, [x0, #0x5b]
    // 0xadbbbc: DecompressPointer r1
    //     0xadbbbc: add             x1, x1, HEAP, lsl #32
    // 0xadbbc0: r0 = LoadClassIdInstr(r1)
    //     0xadbbc0: ldur            x0, [x1, #-1]
    //     0xadbbc4: ubfx            x0, x0, #0xc, #0x14
    // 0xadbbc8: d0 = 0.400000
    //     0xadbbc8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xadbbcc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xadbbcc: sub             lr, x0, #0xffa
    //     0xadbbd0: ldr             lr, [x21, lr, lsl #3]
    //     0xadbbd4: blr             lr
    // 0xadbbd8: r16 = 12.000000
    //     0xadbbd8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xadbbdc: ldr             x16, [x16, #0x9e8]
    // 0xadbbe0: stp             x0, x16, [SP]
    // 0xadbbe4: ldur            x1, [fp, #-0x18]
    // 0xadbbe8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadbbe8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadbbec: ldr             x4, [x4, #0xaa0]
    // 0xadbbf0: r0 = copyWith()
    //     0xadbbf0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadbbf4: stur            x0, [fp, #-0x18]
    // 0xadbbf8: r0 = Text()
    //     0xadbbf8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadbbfc: mov             x3, x0
    // 0xadbc00: ldur            x0, [fp, #-0x10]
    // 0xadbc04: stur            x3, [fp, #-0x28]
    // 0xadbc08: StoreField: r3->field_b = r0
    //     0xadbc08: stur            w0, [x3, #0xb]
    // 0xadbc0c: ldur            x0, [fp, #-0x18]
    // 0xadbc10: StoreField: r3->field_13 = r0
    //     0xadbc10: stur            w0, [x3, #0x13]
    // 0xadbc14: r1 = Null
    //     0xadbc14: mov             x1, NULL
    // 0xadbc18: r2 = 8
    //     0xadbc18: movz            x2, #0x8
    // 0xadbc1c: r0 = AllocateArray()
    //     0xadbc1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadbc20: mov             x2, x0
    // 0xadbc24: ldur            x0, [fp, #-0x20]
    // 0xadbc28: stur            x2, [fp, #-0x10]
    // 0xadbc2c: StoreField: r2->field_f = r0
    //     0xadbc2c: stur            w0, [x2, #0xf]
    // 0xadbc30: ldur            x0, [fp, #-0x30]
    // 0xadbc34: StoreField: r2->field_13 = r0
    //     0xadbc34: stur            w0, [x2, #0x13]
    // 0xadbc38: r16 = Instance_Spacer
    //     0xadbc38: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xadbc3c: ldr             x16, [x16, #0xf0]
    // 0xadbc40: ArrayStore: r2[0] = r16  ; List_4
    //     0xadbc40: stur            w16, [x2, #0x17]
    // 0xadbc44: ldur            x0, [fp, #-0x28]
    // 0xadbc48: StoreField: r2->field_1b = r0
    //     0xadbc48: stur            w0, [x2, #0x1b]
    // 0xadbc4c: r1 = <Widget>
    //     0xadbc4c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xadbc50: r0 = AllocateGrowableArray()
    //     0xadbc50: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xadbc54: mov             x1, x0
    // 0xadbc58: ldur            x0, [fp, #-0x10]
    // 0xadbc5c: stur            x1, [fp, #-0x18]
    // 0xadbc60: StoreField: r1->field_f = r0
    //     0xadbc60: stur            w0, [x1, #0xf]
    // 0xadbc64: r0 = 8
    //     0xadbc64: movz            x0, #0x8
    // 0xadbc68: StoreField: r1->field_b = r0
    //     0xadbc68: stur            w0, [x1, #0xb]
    // 0xadbc6c: r0 = Row()
    //     0xadbc6c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xadbc70: r1 = Instance_Axis
    //     0xadbc70: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xadbc74: StoreField: r0->field_f = r1
    //     0xadbc74: stur            w1, [x0, #0xf]
    // 0xadbc78: r1 = Instance_MainAxisAlignment
    //     0xadbc78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xadbc7c: ldr             x1, [x1, #0xa08]
    // 0xadbc80: StoreField: r0->field_13 = r1
    //     0xadbc80: stur            w1, [x0, #0x13]
    // 0xadbc84: r1 = Instance_MainAxisSize
    //     0xadbc84: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xadbc88: ldr             x1, [x1, #0xa10]
    // 0xadbc8c: ArrayStore: r0[0] = r1  ; List_4
    //     0xadbc8c: stur            w1, [x0, #0x17]
    // 0xadbc90: r1 = Instance_CrossAxisAlignment
    //     0xadbc90: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xadbc94: ldr             x1, [x1, #0xa18]
    // 0xadbc98: StoreField: r0->field_1b = r1
    //     0xadbc98: stur            w1, [x0, #0x1b]
    // 0xadbc9c: r1 = Instance_VerticalDirection
    //     0xadbc9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xadbca0: ldr             x1, [x1, #0xa20]
    // 0xadbca4: StoreField: r0->field_23 = r1
    //     0xadbca4: stur            w1, [x0, #0x23]
    // 0xadbca8: r1 = Instance_Clip
    //     0xadbca8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xadbcac: ldr             x1, [x1, #0x38]
    // 0xadbcb0: StoreField: r0->field_2b = r1
    //     0xadbcb0: stur            w1, [x0, #0x2b]
    // 0xadbcb4: StoreField: r0->field_2f = rZR
    //     0xadbcb4: stur            xzr, [x0, #0x2f]
    // 0xadbcb8: ldur            x1, [fp, #-0x18]
    // 0xadbcbc: StoreField: r0->field_b = r1
    //     0xadbcbc: stur            w1, [x0, #0xb]
    // 0xadbcc0: LeaveFrame
    //     0xadbcc0: mov             SP, fp
    //     0xadbcc4: ldp             fp, lr, [SP], #0x10
    // 0xadbcc8: ret
    //     0xadbcc8: ret             
    // 0xadbccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadbccc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadbcd0: b               #0xadb984
    // 0xadbcd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadbcd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadbcd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadbcd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadbcdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadbcdc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadbce0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadbce0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadbce4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadbce4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadbce8, size: 0x370
    // 0xadbce8: EnterFrame
    //     0xadbce8: stp             fp, lr, [SP, #-0x10]!
    //     0xadbcec: mov             fp, SP
    // 0xadbcf0: AllocStack(0x40)
    //     0xadbcf0: sub             SP, SP, #0x40
    // 0xadbcf4: SetupParameters()
    //     0xadbcf4: ldr             x0, [fp, #0x20]
    //     0xadbcf8: ldur            w4, [x0, #0x17]
    //     0xadbcfc: add             x4, x4, HEAP, lsl #32
    //     0xadbd00: stur            x4, [fp, #-0x10]
    // 0xadbd04: CheckStackOverflow
    //     0xadbd04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadbd08: cmp             SP, x16
    //     0xadbd0c: b.ls            #0xadc03c
    // 0xadbd10: LoadField: r1 = r4->field_f
    //     0xadbd10: ldur            w1, [x4, #0xf]
    // 0xadbd14: DecompressPointer r1
    //     0xadbd14: add             x1, x1, HEAP, lsl #32
    // 0xadbd18: LoadField: r0 = r1->field_b
    //     0xadbd18: ldur            w0, [x1, #0xb]
    // 0xadbd1c: DecompressPointer r0
    //     0xadbd1c: add             x0, x0, HEAP, lsl #32
    // 0xadbd20: cmp             w0, NULL
    // 0xadbd24: b.eq            #0xadc044
    // 0xadbd28: LoadField: r2 = r0->field_b
    //     0xadbd28: ldur            w2, [x0, #0xb]
    // 0xadbd2c: DecompressPointer r2
    //     0xadbd2c: add             x2, x2, HEAP, lsl #32
    // 0xadbd30: ldr             x0, [fp, #0x10]
    // 0xadbd34: r6 = LoadInt32Instr(r0)
    //     0xadbd34: sbfx            x6, x0, #1, #0x1f
    //     0xadbd38: tbz             w0, #0, #0xadbd40
    //     0xadbd3c: ldur            x6, [x0, #7]
    // 0xadbd40: mov             x5, x6
    // 0xadbd44: stur            x6, [fp, #-8]
    // 0xadbd48: r3 = Instance_ItemFilterType
    //     0xadbd48: add             x3, PP, #0x58, lsl #12  ; [pp+0x589a0] Obj!ItemFilterType@d753e1
    //     0xadbd4c: ldr             x3, [x3, #0x9a0]
    // 0xadbd50: r0 = _productBuildItem()
    //     0xadbd50: bl              #0xadb168  ; [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xadbd54: mov             x3, x0
    // 0xadbd58: ldur            x2, [fp, #-0x10]
    // 0xadbd5c: stur            x3, [fp, #-0x20]
    // 0xadbd60: LoadField: r0 = r2->field_f
    //     0xadbd60: ldur            w0, [x2, #0xf]
    // 0xadbd64: DecompressPointer r0
    //     0xadbd64: add             x0, x0, HEAP, lsl #32
    // 0xadbd68: LoadField: r1 = r0->field_b
    //     0xadbd68: ldur            w1, [x0, #0xb]
    // 0xadbd6c: DecompressPointer r1
    //     0xadbd6c: add             x1, x1, HEAP, lsl #32
    // 0xadbd70: cmp             w1, NULL
    // 0xadbd74: b.eq            #0xadc048
    // 0xadbd78: LoadField: r0 = r1->field_b
    //     0xadbd78: ldur            w0, [x1, #0xb]
    // 0xadbd7c: DecompressPointer r0
    //     0xadbd7c: add             x0, x0, HEAP, lsl #32
    // 0xadbd80: cmp             w0, NULL
    // 0xadbd84: b.ne            #0xadbd94
    // 0xadbd88: ldur            x5, [fp, #-8]
    // 0xadbd8c: r0 = Null
    //     0xadbd8c: mov             x0, NULL
    // 0xadbd90: b               #0xadbde8
    // 0xadbd94: LoadField: r4 = r0->field_f
    //     0xadbd94: ldur            w4, [x0, #0xf]
    // 0xadbd98: DecompressPointer r4
    //     0xadbd98: add             x4, x4, HEAP, lsl #32
    // 0xadbd9c: cmp             w4, NULL
    // 0xadbda0: b.ne            #0xadbdb0
    // 0xadbda4: ldur            x5, [fp, #-8]
    // 0xadbda8: r0 = Null
    //     0xadbda8: mov             x0, NULL
    // 0xadbdac: b               #0xadbde8
    // 0xadbdb0: ldur            x5, [fp, #-8]
    // 0xadbdb4: LoadField: r0 = r4->field_b
    //     0xadbdb4: ldur            w0, [x4, #0xb]
    // 0xadbdb8: r1 = LoadInt32Instr(r0)
    //     0xadbdb8: sbfx            x1, x0, #1, #0x1f
    // 0xadbdbc: mov             x0, x1
    // 0xadbdc0: mov             x1, x5
    // 0xadbdc4: cmp             x1, x0
    // 0xadbdc8: b.hs            #0xadc04c
    // 0xadbdcc: LoadField: r0 = r4->field_f
    //     0xadbdcc: ldur            w0, [x4, #0xf]
    // 0xadbdd0: DecompressPointer r0
    //     0xadbdd0: add             x0, x0, HEAP, lsl #32
    // 0xadbdd4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadbdd4: add             x16, x0, x5, lsl #2
    //     0xadbdd8: ldur            w1, [x16, #0xf]
    // 0xadbddc: DecompressPointer r1
    //     0xadbddc: add             x1, x1, HEAP, lsl #32
    // 0xadbde0: LoadField: r0 = r1->field_7
    //     0xadbde0: ldur            w0, [x1, #7]
    // 0xadbde4: DecompressPointer r0
    //     0xadbde4: add             x0, x0, HEAP, lsl #32
    // 0xadbde8: cmp             w0, NULL
    // 0xadbdec: b.ne            #0xadbdf4
    // 0xadbdf0: r0 = ""
    //     0xadbdf0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadbdf4: ldr             x1, [fp, #0x18]
    // 0xadbdf8: stur            x0, [fp, #-0x18]
    // 0xadbdfc: r0 = of()
    //     0xadbdfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadbe00: LoadField: r1 = r0->field_87
    //     0xadbe00: ldur            w1, [x0, #0x87]
    // 0xadbe04: DecompressPointer r1
    //     0xadbe04: add             x1, x1, HEAP, lsl #32
    // 0xadbe08: LoadField: r0 = r1->field_2b
    //     0xadbe08: ldur            w0, [x1, #0x2b]
    // 0xadbe0c: DecompressPointer r0
    //     0xadbe0c: add             x0, x0, HEAP, lsl #32
    // 0xadbe10: r16 = 14.000000
    //     0xadbe10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xadbe14: ldr             x16, [x16, #0x1d8]
    // 0xadbe18: r30 = Instance_Color
    //     0xadbe18: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadbe1c: stp             lr, x16, [SP]
    // 0xadbe20: mov             x1, x0
    // 0xadbe24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadbe24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadbe28: ldr             x4, [x4, #0xaa0]
    // 0xadbe2c: r0 = copyWith()
    //     0xadbe2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadbe30: stur            x0, [fp, #-0x28]
    // 0xadbe34: r0 = Text()
    //     0xadbe34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadbe38: mov             x2, x0
    // 0xadbe3c: ldur            x0, [fp, #-0x18]
    // 0xadbe40: stur            x2, [fp, #-0x30]
    // 0xadbe44: StoreField: r2->field_b = r0
    //     0xadbe44: stur            w0, [x2, #0xb]
    // 0xadbe48: ldur            x0, [fp, #-0x28]
    // 0xadbe4c: StoreField: r2->field_13 = r0
    //     0xadbe4c: stur            w0, [x2, #0x13]
    // 0xadbe50: ldur            x0, [fp, #-0x10]
    // 0xadbe54: LoadField: r1 = r0->field_f
    //     0xadbe54: ldur            w1, [x0, #0xf]
    // 0xadbe58: DecompressPointer r1
    //     0xadbe58: add             x1, x1, HEAP, lsl #32
    // 0xadbe5c: LoadField: r0 = r1->field_b
    //     0xadbe5c: ldur            w0, [x1, #0xb]
    // 0xadbe60: DecompressPointer r0
    //     0xadbe60: add             x0, x0, HEAP, lsl #32
    // 0xadbe64: cmp             w0, NULL
    // 0xadbe68: b.eq            #0xadc050
    // 0xadbe6c: LoadField: r1 = r0->field_b
    //     0xadbe6c: ldur            w1, [x0, #0xb]
    // 0xadbe70: DecompressPointer r1
    //     0xadbe70: add             x1, x1, HEAP, lsl #32
    // 0xadbe74: cmp             w1, NULL
    // 0xadbe78: b.ne            #0xadbe84
    // 0xadbe7c: r0 = Null
    //     0xadbe7c: mov             x0, NULL
    // 0xadbe80: b               #0xadbf00
    // 0xadbe84: LoadField: r3 = r1->field_f
    //     0xadbe84: ldur            w3, [x1, #0xf]
    // 0xadbe88: DecompressPointer r3
    //     0xadbe88: add             x3, x3, HEAP, lsl #32
    // 0xadbe8c: cmp             w3, NULL
    // 0xadbe90: b.ne            #0xadbe9c
    // 0xadbe94: r0 = Null
    //     0xadbe94: mov             x0, NULL
    // 0xadbe98: b               #0xadbf00
    // 0xadbe9c: ldur            x4, [fp, #-8]
    // 0xadbea0: LoadField: r0 = r3->field_b
    //     0xadbea0: ldur            w0, [x3, #0xb]
    // 0xadbea4: r1 = LoadInt32Instr(r0)
    //     0xadbea4: sbfx            x1, x0, #1, #0x1f
    // 0xadbea8: mov             x0, x1
    // 0xadbeac: mov             x1, x4
    // 0xadbeb0: cmp             x1, x0
    // 0xadbeb4: b.hs            #0xadc054
    // 0xadbeb8: LoadField: r0 = r3->field_f
    //     0xadbeb8: ldur            w0, [x3, #0xf]
    // 0xadbebc: DecompressPointer r0
    //     0xadbebc: add             x0, x0, HEAP, lsl #32
    // 0xadbec0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xadbec0: add             x16, x0, x4, lsl #2
    //     0xadbec4: ldur            w1, [x16, #0xf]
    // 0xadbec8: DecompressPointer r1
    //     0xadbec8: add             x1, x1, HEAP, lsl #32
    // 0xadbecc: LoadField: r0 = r1->field_b
    //     0xadbecc: ldur            w0, [x1, #0xb]
    // 0xadbed0: DecompressPointer r0
    //     0xadbed0: add             x0, x0, HEAP, lsl #32
    // 0xadbed4: r1 = 60
    //     0xadbed4: movz            x1, #0x3c
    // 0xadbed8: branchIfSmi(r0, 0xadbee4)
    //     0xadbed8: tbz             w0, #0, #0xadbee4
    // 0xadbedc: r1 = LoadClassIdInstr(r0)
    //     0xadbedc: ldur            x1, [x0, #-1]
    //     0xadbee0: ubfx            x1, x1, #0xc, #0x14
    // 0xadbee4: str             x0, [SP]
    // 0xadbee8: mov             x0, x1
    // 0xadbeec: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xadbeec: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xadbef0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xadbef0: movz            x17, #0x2700
    //     0xadbef4: add             lr, x0, x17
    //     0xadbef8: ldr             lr, [x21, lr, lsl #3]
    //     0xadbefc: blr             lr
    // 0xadbf00: cmp             w0, NULL
    // 0xadbf04: b.ne            #0xadbf10
    // 0xadbf08: r3 = ""
    //     0xadbf08: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadbf0c: b               #0xadbf14
    // 0xadbf10: mov             x3, x0
    // 0xadbf14: ldur            x2, [fp, #-0x20]
    // 0xadbf18: ldur            x0, [fp, #-0x30]
    // 0xadbf1c: ldr             x1, [fp, #0x18]
    // 0xadbf20: stur            x3, [fp, #-0x10]
    // 0xadbf24: r0 = of()
    //     0xadbf24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadbf28: LoadField: r1 = r0->field_87
    //     0xadbf28: ldur            w1, [x0, #0x87]
    // 0xadbf2c: DecompressPointer r1
    //     0xadbf2c: add             x1, x1, HEAP, lsl #32
    // 0xadbf30: LoadField: r0 = r1->field_2b
    //     0xadbf30: ldur            w0, [x1, #0x2b]
    // 0xadbf34: DecompressPointer r0
    //     0xadbf34: add             x0, x0, HEAP, lsl #32
    // 0xadbf38: stur            x0, [fp, #-0x18]
    // 0xadbf3c: r1 = Instance_Color
    //     0xadbf3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadbf40: d0 = 0.400000
    //     0xadbf40: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xadbf44: r0 = withOpacity()
    //     0xadbf44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xadbf48: r16 = 12.000000
    //     0xadbf48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xadbf4c: ldr             x16, [x16, #0x9e8]
    // 0xadbf50: stp             x0, x16, [SP]
    // 0xadbf54: ldur            x1, [fp, #-0x18]
    // 0xadbf58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadbf58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadbf5c: ldr             x4, [x4, #0xaa0]
    // 0xadbf60: r0 = copyWith()
    //     0xadbf60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadbf64: stur            x0, [fp, #-0x18]
    // 0xadbf68: r0 = Text()
    //     0xadbf68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadbf6c: mov             x3, x0
    // 0xadbf70: ldur            x0, [fp, #-0x10]
    // 0xadbf74: stur            x3, [fp, #-0x28]
    // 0xadbf78: StoreField: r3->field_b = r0
    //     0xadbf78: stur            w0, [x3, #0xb]
    // 0xadbf7c: ldur            x0, [fp, #-0x18]
    // 0xadbf80: StoreField: r3->field_13 = r0
    //     0xadbf80: stur            w0, [x3, #0x13]
    // 0xadbf84: r1 = Null
    //     0xadbf84: mov             x1, NULL
    // 0xadbf88: r2 = 8
    //     0xadbf88: movz            x2, #0x8
    // 0xadbf8c: r0 = AllocateArray()
    //     0xadbf8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadbf90: mov             x2, x0
    // 0xadbf94: ldur            x0, [fp, #-0x20]
    // 0xadbf98: stur            x2, [fp, #-0x10]
    // 0xadbf9c: StoreField: r2->field_f = r0
    //     0xadbf9c: stur            w0, [x2, #0xf]
    // 0xadbfa0: ldur            x0, [fp, #-0x30]
    // 0xadbfa4: StoreField: r2->field_13 = r0
    //     0xadbfa4: stur            w0, [x2, #0x13]
    // 0xadbfa8: r16 = Instance_Spacer
    //     0xadbfa8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xadbfac: ldr             x16, [x16, #0xf0]
    // 0xadbfb0: ArrayStore: r2[0] = r16  ; List_4
    //     0xadbfb0: stur            w16, [x2, #0x17]
    // 0xadbfb4: ldur            x0, [fp, #-0x28]
    // 0xadbfb8: StoreField: r2->field_1b = r0
    //     0xadbfb8: stur            w0, [x2, #0x1b]
    // 0xadbfbc: r1 = <Widget>
    //     0xadbfbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xadbfc0: r0 = AllocateGrowableArray()
    //     0xadbfc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xadbfc4: mov             x1, x0
    // 0xadbfc8: ldur            x0, [fp, #-0x10]
    // 0xadbfcc: stur            x1, [fp, #-0x18]
    // 0xadbfd0: StoreField: r1->field_f = r0
    //     0xadbfd0: stur            w0, [x1, #0xf]
    // 0xadbfd4: r0 = 8
    //     0xadbfd4: movz            x0, #0x8
    // 0xadbfd8: StoreField: r1->field_b = r0
    //     0xadbfd8: stur            w0, [x1, #0xb]
    // 0xadbfdc: r0 = Row()
    //     0xadbfdc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xadbfe0: r1 = Instance_Axis
    //     0xadbfe0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xadbfe4: StoreField: r0->field_f = r1
    //     0xadbfe4: stur            w1, [x0, #0xf]
    // 0xadbfe8: r1 = Instance_MainAxisAlignment
    //     0xadbfe8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xadbfec: ldr             x1, [x1, #0xa08]
    // 0xadbff0: StoreField: r0->field_13 = r1
    //     0xadbff0: stur            w1, [x0, #0x13]
    // 0xadbff4: r1 = Instance_MainAxisSize
    //     0xadbff4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xadbff8: ldr             x1, [x1, #0xa10]
    // 0xadbffc: ArrayStore: r0[0] = r1  ; List_4
    //     0xadbffc: stur            w1, [x0, #0x17]
    // 0xadc000: r1 = Instance_CrossAxisAlignment
    //     0xadc000: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xadc004: ldr             x1, [x1, #0xa18]
    // 0xadc008: StoreField: r0->field_1b = r1
    //     0xadc008: stur            w1, [x0, #0x1b]
    // 0xadc00c: r1 = Instance_VerticalDirection
    //     0xadc00c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xadc010: ldr             x1, [x1, #0xa20]
    // 0xadc014: StoreField: r0->field_23 = r1
    //     0xadc014: stur            w1, [x0, #0x23]
    // 0xadc018: r1 = Instance_Clip
    //     0xadc018: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xadc01c: ldr             x1, [x1, #0x38]
    // 0xadc020: StoreField: r0->field_2b = r1
    //     0xadc020: stur            w1, [x0, #0x2b]
    // 0xadc024: StoreField: r0->field_2f = rZR
    //     0xadc024: stur            xzr, [x0, #0x2f]
    // 0xadc028: ldur            x1, [fp, #-0x18]
    // 0xadc02c: StoreField: r0->field_b = r1
    //     0xadc02c: stur            w1, [x0, #0xb]
    // 0xadc030: LeaveFrame
    //     0xadc030: mov             SP, fp
    //     0xadc034: ldp             fp, lr, [SP], #0x10
    // 0xadc038: ret
    //     0xadc038: ret             
    // 0xadc03c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc03c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc040: b               #0xadbd10
    // 0xadc044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc044: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc048: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc048: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc04c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc04c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc050: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc054: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc054: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xadc058, size: 0x3a0
    // 0xadc058: EnterFrame
    //     0xadc058: stp             fp, lr, [SP, #-0x10]!
    //     0xadc05c: mov             fp, SP
    // 0xadc060: AllocStack(0x40)
    //     0xadc060: sub             SP, SP, #0x40
    // 0xadc064: SetupParameters()
    //     0xadc064: ldr             x0, [fp, #0x20]
    //     0xadc068: ldur            w4, [x0, #0x17]
    //     0xadc06c: add             x4, x4, HEAP, lsl #32
    //     0xadc070: stur            x4, [fp, #-0x10]
    // 0xadc074: CheckStackOverflow
    //     0xadc074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc078: cmp             SP, x16
    //     0xadc07c: b.ls            #0xadc3dc
    // 0xadc080: LoadField: r1 = r4->field_f
    //     0xadc080: ldur            w1, [x4, #0xf]
    // 0xadc084: DecompressPointer r1
    //     0xadc084: add             x1, x1, HEAP, lsl #32
    // 0xadc088: LoadField: r0 = r1->field_b
    //     0xadc088: ldur            w0, [x1, #0xb]
    // 0xadc08c: DecompressPointer r0
    //     0xadc08c: add             x0, x0, HEAP, lsl #32
    // 0xadc090: cmp             w0, NULL
    // 0xadc094: b.eq            #0xadc3e4
    // 0xadc098: LoadField: r2 = r0->field_b
    //     0xadc098: ldur            w2, [x0, #0xb]
    // 0xadc09c: DecompressPointer r2
    //     0xadc09c: add             x2, x2, HEAP, lsl #32
    // 0xadc0a0: ldr             x0, [fp, #0x10]
    // 0xadc0a4: r6 = LoadInt32Instr(r0)
    //     0xadc0a4: sbfx            x6, x0, #1, #0x1f
    //     0xadc0a8: tbz             w0, #0, #0xadc0b0
    //     0xadc0ac: ldur            x6, [x0, #7]
    // 0xadc0b0: mov             x5, x6
    // 0xadc0b4: stur            x6, [fp, #-8]
    // 0xadc0b8: r3 = Instance_ItemFilterType
    //     0xadc0b8: add             x3, PP, #0x58, lsl #12  ; [pp+0x589a8] Obj!ItemFilterType@d75401
    //     0xadc0bc: ldr             x3, [x3, #0x9a8]
    // 0xadc0c0: r0 = _productBuildItem()
    //     0xadc0c0: bl              #0xadb168  ; [package:customer_app/app/presentation/views/cosmetic/collections/widgets/collection_filter.dart] _CollectionFilterState::_productBuildItem
    // 0xadc0c4: mov             x3, x0
    // 0xadc0c8: ldur            x2, [fp, #-0x10]
    // 0xadc0cc: stur            x3, [fp, #-0x20]
    // 0xadc0d0: LoadField: r0 = r2->field_f
    //     0xadc0d0: ldur            w0, [x2, #0xf]
    // 0xadc0d4: DecompressPointer r0
    //     0xadc0d4: add             x0, x0, HEAP, lsl #32
    // 0xadc0d8: LoadField: r1 = r0->field_b
    //     0xadc0d8: ldur            w1, [x0, #0xb]
    // 0xadc0dc: DecompressPointer r1
    //     0xadc0dc: add             x1, x1, HEAP, lsl #32
    // 0xadc0e0: cmp             w1, NULL
    // 0xadc0e4: b.eq            #0xadc3e8
    // 0xadc0e8: LoadField: r0 = r1->field_b
    //     0xadc0e8: ldur            w0, [x1, #0xb]
    // 0xadc0ec: DecompressPointer r0
    //     0xadc0ec: add             x0, x0, HEAP, lsl #32
    // 0xadc0f0: cmp             w0, NULL
    // 0xadc0f4: b.ne            #0xadc104
    // 0xadc0f8: ldur            x5, [fp, #-8]
    // 0xadc0fc: r0 = Null
    //     0xadc0fc: mov             x0, NULL
    // 0xadc100: b               #0xadc158
    // 0xadc104: LoadField: r4 = r0->field_7
    //     0xadc104: ldur            w4, [x0, #7]
    // 0xadc108: DecompressPointer r4
    //     0xadc108: add             x4, x4, HEAP, lsl #32
    // 0xadc10c: cmp             w4, NULL
    // 0xadc110: b.ne            #0xadc120
    // 0xadc114: ldur            x5, [fp, #-8]
    // 0xadc118: r0 = Null
    //     0xadc118: mov             x0, NULL
    // 0xadc11c: b               #0xadc158
    // 0xadc120: ldur            x5, [fp, #-8]
    // 0xadc124: LoadField: r0 = r4->field_b
    //     0xadc124: ldur            w0, [x4, #0xb]
    // 0xadc128: r1 = LoadInt32Instr(r0)
    //     0xadc128: sbfx            x1, x0, #1, #0x1f
    // 0xadc12c: mov             x0, x1
    // 0xadc130: mov             x1, x5
    // 0xadc134: cmp             x1, x0
    // 0xadc138: b.hs            #0xadc3ec
    // 0xadc13c: LoadField: r0 = r4->field_f
    //     0xadc13c: ldur            w0, [x4, #0xf]
    // 0xadc140: DecompressPointer r0
    //     0xadc140: add             x0, x0, HEAP, lsl #32
    // 0xadc144: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xadc144: add             x16, x0, x5, lsl #2
    //     0xadc148: ldur            w1, [x16, #0xf]
    // 0xadc14c: DecompressPointer r1
    //     0xadc14c: add             x1, x1, HEAP, lsl #32
    // 0xadc150: LoadField: r0 = r1->field_f
    //     0xadc150: ldur            w0, [x1, #0xf]
    // 0xadc154: DecompressPointer r0
    //     0xadc154: add             x0, x0, HEAP, lsl #32
    // 0xadc158: cmp             w0, NULL
    // 0xadc15c: b.ne            #0xadc164
    // 0xadc160: r0 = ""
    //     0xadc160: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadc164: ldr             x1, [fp, #0x18]
    // 0xadc168: stur            x0, [fp, #-0x18]
    // 0xadc16c: r0 = of()
    //     0xadc16c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadc170: LoadField: r1 = r0->field_87
    //     0xadc170: ldur            w1, [x0, #0x87]
    // 0xadc174: DecompressPointer r1
    //     0xadc174: add             x1, x1, HEAP, lsl #32
    // 0xadc178: LoadField: r0 = r1->field_2b
    //     0xadc178: ldur            w0, [x1, #0x2b]
    // 0xadc17c: DecompressPointer r0
    //     0xadc17c: add             x0, x0, HEAP, lsl #32
    // 0xadc180: r16 = 14.000000
    //     0xadc180: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xadc184: ldr             x16, [x16, #0x1d8]
    // 0xadc188: r30 = Instance_Color
    //     0xadc188: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadc18c: stp             lr, x16, [SP]
    // 0xadc190: mov             x1, x0
    // 0xadc194: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadc194: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadc198: ldr             x4, [x4, #0xaa0]
    // 0xadc19c: r0 = copyWith()
    //     0xadc19c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadc1a0: stur            x0, [fp, #-0x28]
    // 0xadc1a4: r0 = Text()
    //     0xadc1a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadc1a8: mov             x2, x0
    // 0xadc1ac: ldur            x0, [fp, #-0x18]
    // 0xadc1b0: stur            x2, [fp, #-0x30]
    // 0xadc1b4: StoreField: r2->field_b = r0
    //     0xadc1b4: stur            w0, [x2, #0xb]
    // 0xadc1b8: ldur            x0, [fp, #-0x28]
    // 0xadc1bc: StoreField: r2->field_13 = r0
    //     0xadc1bc: stur            w0, [x2, #0x13]
    // 0xadc1c0: r1 = <FlexParentData>
    //     0xadc1c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xadc1c4: ldr             x1, [x1, #0xe00]
    // 0xadc1c8: r0 = Flexible()
    //     0xadc1c8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xadc1cc: mov             x2, x0
    // 0xadc1d0: r0 = 1
    //     0xadc1d0: movz            x0, #0x1
    // 0xadc1d4: stur            x2, [fp, #-0x18]
    // 0xadc1d8: StoreField: r2->field_13 = r0
    //     0xadc1d8: stur            x0, [x2, #0x13]
    // 0xadc1dc: r0 = Instance_FlexFit
    //     0xadc1dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xadc1e0: ldr             x0, [x0, #0xe08]
    // 0xadc1e4: StoreField: r2->field_1b = r0
    //     0xadc1e4: stur            w0, [x2, #0x1b]
    // 0xadc1e8: ldur            x0, [fp, #-0x30]
    // 0xadc1ec: StoreField: r2->field_b = r0
    //     0xadc1ec: stur            w0, [x2, #0xb]
    // 0xadc1f0: ldur            x0, [fp, #-0x10]
    // 0xadc1f4: LoadField: r1 = r0->field_f
    //     0xadc1f4: ldur            w1, [x0, #0xf]
    // 0xadc1f8: DecompressPointer r1
    //     0xadc1f8: add             x1, x1, HEAP, lsl #32
    // 0xadc1fc: LoadField: r0 = r1->field_b
    //     0xadc1fc: ldur            w0, [x1, #0xb]
    // 0xadc200: DecompressPointer r0
    //     0xadc200: add             x0, x0, HEAP, lsl #32
    // 0xadc204: cmp             w0, NULL
    // 0xadc208: b.eq            #0xadc3f0
    // 0xadc20c: LoadField: r1 = r0->field_b
    //     0xadc20c: ldur            w1, [x0, #0xb]
    // 0xadc210: DecompressPointer r1
    //     0xadc210: add             x1, x1, HEAP, lsl #32
    // 0xadc214: cmp             w1, NULL
    // 0xadc218: b.ne            #0xadc224
    // 0xadc21c: r0 = Null
    //     0xadc21c: mov             x0, NULL
    // 0xadc220: b               #0xadc2a0
    // 0xadc224: LoadField: r3 = r1->field_7
    //     0xadc224: ldur            w3, [x1, #7]
    // 0xadc228: DecompressPointer r3
    //     0xadc228: add             x3, x3, HEAP, lsl #32
    // 0xadc22c: cmp             w3, NULL
    // 0xadc230: b.ne            #0xadc23c
    // 0xadc234: r0 = Null
    //     0xadc234: mov             x0, NULL
    // 0xadc238: b               #0xadc2a0
    // 0xadc23c: ldur            x4, [fp, #-8]
    // 0xadc240: LoadField: r0 = r3->field_b
    //     0xadc240: ldur            w0, [x3, #0xb]
    // 0xadc244: r1 = LoadInt32Instr(r0)
    //     0xadc244: sbfx            x1, x0, #1, #0x1f
    // 0xadc248: mov             x0, x1
    // 0xadc24c: mov             x1, x4
    // 0xadc250: cmp             x1, x0
    // 0xadc254: b.hs            #0xadc3f4
    // 0xadc258: LoadField: r0 = r3->field_f
    //     0xadc258: ldur            w0, [x3, #0xf]
    // 0xadc25c: DecompressPointer r0
    //     0xadc25c: add             x0, x0, HEAP, lsl #32
    // 0xadc260: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xadc260: add             x16, x0, x4, lsl #2
    //     0xadc264: ldur            w1, [x16, #0xf]
    // 0xadc268: DecompressPointer r1
    //     0xadc268: add             x1, x1, HEAP, lsl #32
    // 0xadc26c: LoadField: r0 = r1->field_b
    //     0xadc26c: ldur            w0, [x1, #0xb]
    // 0xadc270: DecompressPointer r0
    //     0xadc270: add             x0, x0, HEAP, lsl #32
    // 0xadc274: r1 = 60
    //     0xadc274: movz            x1, #0x3c
    // 0xadc278: branchIfSmi(r0, 0xadc284)
    //     0xadc278: tbz             w0, #0, #0xadc284
    // 0xadc27c: r1 = LoadClassIdInstr(r0)
    //     0xadc27c: ldur            x1, [x0, #-1]
    //     0xadc280: ubfx            x1, x1, #0xc, #0x14
    // 0xadc284: str             x0, [SP]
    // 0xadc288: mov             x0, x1
    // 0xadc28c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xadc28c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xadc290: r0 = GDT[cid_x0 + 0x2700]()
    //     0xadc290: movz            x17, #0x2700
    //     0xadc294: add             lr, x0, x17
    //     0xadc298: ldr             lr, [x21, lr, lsl #3]
    //     0xadc29c: blr             lr
    // 0xadc2a0: cmp             w0, NULL
    // 0xadc2a4: b.ne            #0xadc2b0
    // 0xadc2a8: r3 = ""
    //     0xadc2a8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xadc2ac: b               #0xadc2b4
    // 0xadc2b0: mov             x3, x0
    // 0xadc2b4: ldur            x2, [fp, #-0x20]
    // 0xadc2b8: ldur            x0, [fp, #-0x18]
    // 0xadc2bc: ldr             x1, [fp, #0x18]
    // 0xadc2c0: stur            x3, [fp, #-0x10]
    // 0xadc2c4: r0 = of()
    //     0xadc2c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xadc2c8: LoadField: r1 = r0->field_87
    //     0xadc2c8: ldur            w1, [x0, #0x87]
    // 0xadc2cc: DecompressPointer r1
    //     0xadc2cc: add             x1, x1, HEAP, lsl #32
    // 0xadc2d0: LoadField: r0 = r1->field_2b
    //     0xadc2d0: ldur            w0, [x1, #0x2b]
    // 0xadc2d4: DecompressPointer r0
    //     0xadc2d4: add             x0, x0, HEAP, lsl #32
    // 0xadc2d8: stur            x0, [fp, #-0x28]
    // 0xadc2dc: r1 = Instance_Color
    //     0xadc2dc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xadc2e0: d0 = 0.400000
    //     0xadc2e0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xadc2e4: r0 = withOpacity()
    //     0xadc2e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xadc2e8: r16 = 12.000000
    //     0xadc2e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xadc2ec: ldr             x16, [x16, #0x9e8]
    // 0xadc2f0: stp             x0, x16, [SP]
    // 0xadc2f4: ldur            x1, [fp, #-0x28]
    // 0xadc2f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xadc2f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xadc2fc: ldr             x4, [x4, #0xaa0]
    // 0xadc300: r0 = copyWith()
    //     0xadc300: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xadc304: stur            x0, [fp, #-0x28]
    // 0xadc308: r0 = Text()
    //     0xadc308: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xadc30c: mov             x3, x0
    // 0xadc310: ldur            x0, [fp, #-0x10]
    // 0xadc314: stur            x3, [fp, #-0x30]
    // 0xadc318: StoreField: r3->field_b = r0
    //     0xadc318: stur            w0, [x3, #0xb]
    // 0xadc31c: ldur            x0, [fp, #-0x28]
    // 0xadc320: StoreField: r3->field_13 = r0
    //     0xadc320: stur            w0, [x3, #0x13]
    // 0xadc324: r1 = Null
    //     0xadc324: mov             x1, NULL
    // 0xadc328: r2 = 8
    //     0xadc328: movz            x2, #0x8
    // 0xadc32c: r0 = AllocateArray()
    //     0xadc32c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xadc330: mov             x2, x0
    // 0xadc334: ldur            x0, [fp, #-0x20]
    // 0xadc338: stur            x2, [fp, #-0x10]
    // 0xadc33c: StoreField: r2->field_f = r0
    //     0xadc33c: stur            w0, [x2, #0xf]
    // 0xadc340: ldur            x0, [fp, #-0x18]
    // 0xadc344: StoreField: r2->field_13 = r0
    //     0xadc344: stur            w0, [x2, #0x13]
    // 0xadc348: r16 = Instance_Spacer
    //     0xadc348: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xadc34c: ldr             x16, [x16, #0xf0]
    // 0xadc350: ArrayStore: r2[0] = r16  ; List_4
    //     0xadc350: stur            w16, [x2, #0x17]
    // 0xadc354: ldur            x0, [fp, #-0x30]
    // 0xadc358: StoreField: r2->field_1b = r0
    //     0xadc358: stur            w0, [x2, #0x1b]
    // 0xadc35c: r1 = <Widget>
    //     0xadc35c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xadc360: r0 = AllocateGrowableArray()
    //     0xadc360: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xadc364: mov             x1, x0
    // 0xadc368: ldur            x0, [fp, #-0x10]
    // 0xadc36c: stur            x1, [fp, #-0x18]
    // 0xadc370: StoreField: r1->field_f = r0
    //     0xadc370: stur            w0, [x1, #0xf]
    // 0xadc374: r0 = 8
    //     0xadc374: movz            x0, #0x8
    // 0xadc378: StoreField: r1->field_b = r0
    //     0xadc378: stur            w0, [x1, #0xb]
    // 0xadc37c: r0 = Row()
    //     0xadc37c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xadc380: r1 = Instance_Axis
    //     0xadc380: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xadc384: StoreField: r0->field_f = r1
    //     0xadc384: stur            w1, [x0, #0xf]
    // 0xadc388: r1 = Instance_MainAxisAlignment
    //     0xadc388: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xadc38c: ldr             x1, [x1, #0xa08]
    // 0xadc390: StoreField: r0->field_13 = r1
    //     0xadc390: stur            w1, [x0, #0x13]
    // 0xadc394: r1 = Instance_MainAxisSize
    //     0xadc394: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xadc398: ldr             x1, [x1, #0xa10]
    // 0xadc39c: ArrayStore: r0[0] = r1  ; List_4
    //     0xadc39c: stur            w1, [x0, #0x17]
    // 0xadc3a0: r1 = Instance_CrossAxisAlignment
    //     0xadc3a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xadc3a4: ldr             x1, [x1, #0xa18]
    // 0xadc3a8: StoreField: r0->field_1b = r1
    //     0xadc3a8: stur            w1, [x0, #0x1b]
    // 0xadc3ac: r1 = Instance_VerticalDirection
    //     0xadc3ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xadc3b0: ldr             x1, [x1, #0xa20]
    // 0xadc3b4: StoreField: r0->field_23 = r1
    //     0xadc3b4: stur            w1, [x0, #0x23]
    // 0xadc3b8: r1 = Instance_Clip
    //     0xadc3b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xadc3bc: ldr             x1, [x1, #0x38]
    // 0xadc3c0: StoreField: r0->field_2b = r1
    //     0xadc3c0: stur            w1, [x0, #0x2b]
    // 0xadc3c4: StoreField: r0->field_2f = rZR
    //     0xadc3c4: stur            xzr, [x0, #0x2f]
    // 0xadc3c8: ldur            x1, [fp, #-0x18]
    // 0xadc3cc: StoreField: r0->field_b = r1
    //     0xadc3cc: stur            w1, [x0, #0xb]
    // 0xadc3d0: LeaveFrame
    //     0xadc3d0: mov             SP, fp
    //     0xadc3d4: ldp             fp, lr, [SP], #0x10
    // 0xadc3d8: ret
    //     0xadc3d8: ret             
    // 0xadc3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc3dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc3e0: b               #0xadc080
    // 0xadc3e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc3e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc3e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc3e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc3ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc3ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc3f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc3f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc3f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc3f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadc3f8, size: 0x98
    // 0xadc3f8: EnterFrame
    //     0xadc3f8: stp             fp, lr, [SP, #-0x10]!
    //     0xadc3fc: mov             fp, SP
    // 0xadc400: AllocStack(0x10)
    //     0xadc400: sub             SP, SP, #0x10
    // 0xadc404: SetupParameters()
    //     0xadc404: ldr             x0, [fp, #0x10]
    //     0xadc408: ldur            w2, [x0, #0x17]
    //     0xadc40c: add             x2, x2, HEAP, lsl #32
    //     0xadc410: stur            x2, [fp, #-8]
    // 0xadc414: CheckStackOverflow
    //     0xadc414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc418: cmp             SP, x16
    //     0xadc41c: b.ls            #0xadc484
    // 0xadc420: LoadField: r0 = r2->field_f
    //     0xadc420: ldur            w0, [x2, #0xf]
    // 0xadc424: DecompressPointer r0
    //     0xadc424: add             x0, x0, HEAP, lsl #32
    // 0xadc428: LoadField: r1 = r0->field_13
    //     0xadc428: ldur            w1, [x0, #0x13]
    // 0xadc42c: DecompressPointer r1
    //     0xadc42c: add             x1, x1, HEAP, lsl #32
    // 0xadc430: r0 = clear()
    //     0xadc430: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xadc434: ldur            x0, [fp, #-8]
    // 0xadc438: LoadField: r1 = r0->field_f
    //     0xadc438: ldur            w1, [x0, #0xf]
    // 0xadc43c: DecompressPointer r1
    //     0xadc43c: add             x1, x1, HEAP, lsl #32
    // 0xadc440: LoadField: r0 = r1->field_b
    //     0xadc440: ldur            w0, [x1, #0xb]
    // 0xadc444: DecompressPointer r0
    //     0xadc444: add             x0, x0, HEAP, lsl #32
    // 0xadc448: cmp             w0, NULL
    // 0xadc44c: b.eq            #0xadc48c
    // 0xadc450: LoadField: r1 = r0->field_13
    //     0xadc450: ldur            w1, [x0, #0x13]
    // 0xadc454: DecompressPointer r1
    //     0xadc454: add             x1, x1, HEAP, lsl #32
    // 0xadc458: str             x1, [SP]
    // 0xadc45c: r4 = 0
    //     0xadc45c: movz            x4, #0
    // 0xadc460: ldr             x0, [SP]
    // 0xadc464: r16 = UnlinkedCall_0x613b5c
    //     0xadc464: add             x16, PP, #0x58, lsl #12  ; [pp+0x589b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xadc468: add             x16, x16, #0x9b0
    // 0xadc46c: ldp             x5, lr, [x16]
    // 0xadc470: blr             lr
    // 0xadc474: r0 = Null
    //     0xadc474: mov             x0, NULL
    // 0xadc478: LeaveFrame
    //     0xadc478: mov             SP, fp
    //     0xadc47c: ldp             fp, lr, [SP], #0x10
    // 0xadc480: ret
    //     0xadc480: ret             
    // 0xadc484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc484: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc488: b               #0xadc420
    // 0xadc48c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc48c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4174, size: 0x18, field offset: 0xc
//   const constructor, 
class CollectionFilter extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7d758, size: 0x48
    // 0xc7d758: EnterFrame
    //     0xc7d758: stp             fp, lr, [SP, #-0x10]!
    //     0xc7d75c: mov             fp, SP
    // 0xc7d760: AllocStack(0x8)
    //     0xc7d760: sub             SP, SP, #8
    // 0xc7d764: CheckStackOverflow
    //     0xc7d764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7d768: cmp             SP, x16
    //     0xc7d76c: b.ls            #0xc7d798
    // 0xc7d770: r1 = <CollectionFilter>
    //     0xc7d770: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d18] TypeArguments: <CollectionFilter>
    //     0xc7d774: ldr             x1, [x1, #0xd18]
    // 0xc7d778: r0 = _CollectionFilterState()
    //     0xc7d778: bl              #0xc7d7a0  ; Allocate_CollectionFilterStateStub -> _CollectionFilterState (size=0x34)
    // 0xc7d77c: mov             x1, x0
    // 0xc7d780: stur            x0, [fp, #-8]
    // 0xc7d784: r0 = _CollectionFilterState()
    //     0xc7d784: bl              #0xc7b32c  ; [package:customer_app/app/presentation/views/basic/collections/widgets/collection_filter.dart] _CollectionFilterState::_CollectionFilterState
    // 0xc7d788: ldur            x0, [fp, #-8]
    // 0xc7d78c: LeaveFrame
    //     0xc7d78c: mov             SP, fp
    //     0xc7d790: ldp             fp, lr, [SP], #0x10
    // 0xc7d794: ret
    //     0xc7d794: ret             
    // 0xc7d798: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7d798: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7d79c: b               #0xc7d770
  }
}

// class id: 7083, size: 0x14, field offset: 0x14
enum ItemFilterType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
