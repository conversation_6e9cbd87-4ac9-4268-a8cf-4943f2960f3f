// lib: , url: package:customer_app/app/presentation/controllers/image/image_controller.dart

// class id: 1049044, size: 0x8
class :: {
}

// class id: 1292, size: 0x50, field offset: 0x48
class ImageController extends BaseController {

  get _ imageList(/* No info */) {
    // ** addr: 0x1401638, size: 0x3c
    // 0x1401638: EnterFrame
    //     0x1401638: stp             fp, lr, [SP, #-0x10]!
    //     0x140163c: mov             fp, SP
    // 0x1401640: CheckStackOverflow
    //     0x1401640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1401644: cmp             SP, x16
    //     0x1401648: b.ls            #0x140166c
    // 0x140164c: LoadField: r0 = r1->field_4b
    //     0x140164c: ldur            w0, [x1, #0x4b]
    // 0x1401650: DecompressPointer r0
    //     0x1401650: add             x0, x0, HEAP, lsl #32
    // 0x1401654: mov             x1, x0
    // 0x1401658: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1401658: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x140165c: r0 = toList()
    //     0x140165c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x1401660: LeaveFrame
    //     0x1401660: mov             SP, fp
    //     0x1401664: ldp             fp, lr, [SP], #0x10
    // 0x1401668: ret
    //     0x1401668: ret             
    // 0x140166c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140166c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1401670: b               #0x140164c
  }
  _ onInit(/* No info */) {
    // ** addr: 0x15a0044, size: 0xc8
    // 0x15a0044: EnterFrame
    //     0x15a0044: stp             fp, lr, [SP, #-0x10]!
    //     0x15a0048: mov             fp, SP
    // 0x15a004c: AllocStack(0x28)
    //     0x15a004c: sub             SP, SP, #0x28
    // 0x15a0050: SetupParameters(ImageController this /* r1 => r1, fp-0x8 */)
    //     0x15a0050: stur            x1, [fp, #-8]
    // 0x15a0054: CheckStackOverflow
    //     0x15a0054: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a0058: cmp             SP, x16
    //     0x15a005c: b.ls            #0x15a0104
    // 0x15a0060: LoadField: r0 = r1->field_47
    //     0x15a0060: ldur            w0, [x1, #0x47]
    // 0x15a0064: DecompressPointer r0
    //     0x15a0064: add             x0, x0, HEAP, lsl #32
    // 0x15a0068: stp             xzr, x0, [SP]
    // 0x15a006c: r4 = 0
    //     0x15a006c: movz            x4, #0
    // 0x15a0070: ldr             x0, [SP, #8]
    // 0x15a0074: r16 = UnlinkedCall_0x613b5c
    //     0x15a0074: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5cfc8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a0078: add             x16, x16, #0xfc8
    // 0x15a007c: ldp             x5, lr, [x16]
    // 0x15a0080: blr             lr
    // 0x15a0084: r16 = "images"
    //     0x15a0084: add             x16, PP, #0xf, lsl #12  ; [pp+0xfa98] "images"
    //     0x15a0088: ldr             x16, [x16, #0xa98]
    // 0x15a008c: stp             x16, x0, [SP]
    // 0x15a0090: r4 = 0
    //     0x15a0090: movz            x4, #0
    // 0x15a0094: ldr             x0, [SP, #8]
    // 0x15a0098: r16 = UnlinkedCall_0x613b5c
    //     0x15a0098: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5cfd8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a009c: add             x16, x16, #0xfd8
    // 0x15a00a0: ldp             x5, lr, [x16]
    // 0x15a00a4: blr             lr
    // 0x15a00a8: mov             x4, x0
    // 0x15a00ac: ldur            x3, [fp, #-8]
    // 0x15a00b0: stur            x4, [fp, #-0x18]
    // 0x15a00b4: LoadField: r5 = r3->field_4b
    //     0x15a00b4: ldur            w5, [x3, #0x4b]
    // 0x15a00b8: DecompressPointer r5
    //     0x15a00b8: add             x5, x5, HEAP, lsl #32
    // 0x15a00bc: mov             x0, x4
    // 0x15a00c0: stur            x5, [fp, #-0x10]
    // 0x15a00c4: r2 = Null
    //     0x15a00c4: mov             x2, NULL
    // 0x15a00c8: r1 = Null
    //     0x15a00c8: mov             x1, NULL
    // 0x15a00cc: r8 = List<WidgetEntity?>?
    //     0x15a00cc: add             x8, PP, #0x5c, lsl #12  ; [pp+0x5cfe8] Type: List<WidgetEntity?>?
    //     0x15a00d0: ldr             x8, [x8, #0xfe8]
    // 0x15a00d4: r3 = Null
    //     0x15a00d4: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cff0] Null
    //     0x15a00d8: ldr             x3, [x3, #0xff0]
    // 0x15a00dc: r0 = List<WidgetEntity?>?()
    //     0x15a00dc: bl              #0x15a010c  ; IsType_List<WidgetEntity?>?_Stub
    // 0x15a00e0: ldur            x1, [fp, #-0x10]
    // 0x15a00e4: ldur            x2, [fp, #-0x18]
    // 0x15a00e8: r0 = call()
    //     0x15a00e8: bl              #0x8a9c54  ; [package:get/get_rx/src/rx_types/rx_types.dart] _RxList&ListMixin&NotifyManager&RxObjectMixin::call
    // 0x15a00ec: ldur            x1, [fp, #-8]
    // 0x15a00f0: r0 = onInit()
    //     0x15a00f0: bl              #0x158ae60  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::onInit
    // 0x15a00f4: r0 = Null
    //     0x15a00f4: mov             x0, NULL
    // 0x15a00f8: LeaveFrame
    //     0x15a00f8: mov             SP, fp
    //     0x15a00fc: ldp             fp, lr, [SP], #0x10
    // 0x15a0100: ret
    //     0x15a0100: ret             
    // 0x15a0104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a0104: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a0108: b               #0x15a0060
  }
  _ ImageController(/* No info */) {
    // ** addr: 0x1601330, size: 0xa8
    // 0x1601330: EnterFrame
    //     0x1601330: stp             fp, lr, [SP, #-0x10]!
    //     0x1601334: mov             fp, SP
    // 0x1601338: AllocStack(0x8)
    //     0x1601338: sub             SP, SP, #8
    // 0x160133c: SetupParameters(ImageController this /* r1 => r1, fp-0x8 */)
    //     0x160133c: stur            x1, [fp, #-8]
    // 0x1601340: CheckStackOverflow
    //     0x1601340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1601344: cmp             SP, x16
    //     0x1601348: b.ls            #0x16013d0
    // 0x160134c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x160134c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1601350: ldr             x0, [x0, #0x1c80]
    //     0x1601354: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1601358: cmp             w0, w16
    //     0x160135c: b.ne            #0x1601368
    //     0x1601360: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1601364: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1601368: r0 = GetNavigation.arguments()
    //     0x1601368: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x160136c: ldur            x2, [fp, #-8]
    // 0x1601370: StoreField: r2->field_47 = r0
    //     0x1601370: stur            w0, [x2, #0x47]
    //     0x1601374: tbz             w0, #0, #0x1601390
    //     0x1601378: ldurb           w16, [x2, #-1]
    //     0x160137c: ldurb           w17, [x0, #-1]
    //     0x1601380: and             x16, x17, x16, lsr #2
    //     0x1601384: tst             x16, HEAP, lsr #32
    //     0x1601388: b.eq            #0x1601390
    //     0x160138c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1601390: r1 = <WidgetEntity?>
    //     0x1601390: add             x1, PP, #0x31, lsl #12  ; [pp+0x31e28] TypeArguments: <WidgetEntity?>
    //     0x1601394: ldr             x1, [x1, #0xe28]
    // 0x1601398: r0 = RxList.empty()
    //     0x1601398: bl              #0x12df608  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::RxList.empty
    // 0x160139c: ldur            x1, [fp, #-8]
    // 0x16013a0: StoreField: r1->field_4b = r0
    //     0x16013a0: stur            w0, [x1, #0x4b]
    //     0x16013a4: ldurb           w16, [x1, #-1]
    //     0x16013a8: ldurb           w17, [x0, #-1]
    //     0x16013ac: and             x16, x17, x16, lsr #2
    //     0x16013b0: tst             x16, HEAP, lsr #32
    //     0x16013b4: b.eq            #0x16013bc
    //     0x16013b8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x16013bc: r0 = BaseController()
    //     0x16013bc: bl              #0x12c396c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::BaseController
    // 0x16013c0: r0 = Null
    //     0x16013c0: mov             x0, NULL
    // 0x16013c4: LeaveFrame
    //     0x16013c4: mov             SP, fp
    //     0x16013c8: ldp             fp, lr, [SP], #0x10
    // 0x16013cc: ret
    //     0x16013cc: ret             
    // 0x16013d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x16013d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x16013d4: b               #0x160134c
  }
}
