// lib: , url: package:customer_app/app/presentation/views/line/profile/profile_view.dart

// class id: 1049577, size: 0x8
class :: {
}

// class id: 4520, size: 0x14, field offset: 0x14
//   const constructor, 
class ProfileView extends BaseView<dynamic> {

  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14762a4, size: 0x120
    // 0x14762a4: EnterFrame
    //     0x14762a4: stp             fp, lr, [SP, #-0x10]!
    //     0x14762a8: mov             fp, SP
    // 0x14762ac: AllocStack(0x20)
    //     0x14762ac: sub             SP, SP, #0x20
    // 0x14762b0: SetupParameters(ProfileView this /* r1 */)
    //     0x14762b0: stur            NULL, [fp, #-8]
    //     0x14762b4: movz            x0, #0
    //     0x14762b8: add             x1, fp, w0, sxtw #2
    //     0x14762bc: ldr             x1, [x1, #0x10]
    //     0x14762c0: ldur            w2, [x1, #0x17]
    //     0x14762c4: add             x2, x2, HEAP, lsl #32
    //     0x14762c8: stur            x2, [fp, #-0x10]
    // 0x14762cc: CheckStackOverflow
    //     0x14762cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14762d0: cmp             SP, x16
    //     0x14762d4: b.ls            #0x14763b8
    // 0x14762d8: InitAsync() -> Future<void?>
    //     0x14762d8: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14762dc: bl              #0x6326e0  ; InitAsyncStub
    // 0x14762e0: ldur            x0, [fp, #-0x10]
    // 0x14762e4: LoadField: r1 = r0->field_f
    //     0x14762e4: ldur            w1, [x0, #0xf]
    // 0x14762e8: DecompressPointer r1
    //     0x14762e8: add             x1, x1, HEAP, lsl #32
    // 0x14762ec: r0 = controller()
    //     0x14762ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14762f0: LoadField: r1 = r0->field_77
    //     0x14762f0: ldur            w1, [x0, #0x77]
    // 0x14762f4: DecompressPointer r1
    //     0x14762f4: add             x1, x1, HEAP, lsl #32
    // 0x14762f8: r0 = deleteToken()
    //     0x14762f8: bl              #0x9186fc  ; [package:firebase_messaging/firebase_messaging.dart] FirebaseMessaging::deleteToken
    // 0x14762fc: mov             x1, x0
    // 0x1476300: stur            x1, [fp, #-0x18]
    // 0x1476304: r0 = Await()
    //     0x1476304: bl              #0x63248c  ; AwaitStub
    // 0x1476308: ldur            x0, [fp, #-0x10]
    // 0x147630c: LoadField: r1 = r0->field_f
    //     0x147630c: ldur            w1, [x0, #0xf]
    // 0x1476310: DecompressPointer r1
    //     0x1476310: add             x1, x1, HEAP, lsl #32
    // 0x1476314: r0 = controller()
    //     0x1476314: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1476318: LoadField: r1 = r0->field_57
    //     0x1476318: ldur            w1, [x0, #0x57]
    // 0x147631c: DecompressPointer r1
    //     0x147631c: add             x1, x1, HEAP, lsl #32
    // 0x1476320: str             x1, [SP]
    // 0x1476324: r0 = removeAllPreferenceExceptWebConfig()
    //     0x1476324: bl              #0x889ba4  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::removeAllPreferenceExceptWebConfig
    // 0x1476328: mov             x1, x0
    // 0x147632c: stur            x1, [fp, #-0x18]
    // 0x1476330: r0 = Await()
    //     0x1476330: bl              #0x63248c  ; AwaitStub
    // 0x1476334: ldur            x0, [fp, #-0x10]
    // 0x1476338: LoadField: r1 = r0->field_f
    //     0x1476338: ldur            w1, [x0, #0xf]
    // 0x147633c: DecompressPointer r1
    //     0x147633c: add             x1, x1, HEAP, lsl #32
    // 0x1476340: r0 = controller()
    //     0x1476340: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1476344: mov             x1, x0
    // 0x1476348: r0 = checkLoginStatus()
    //     0x1476348: bl              #0x913d8c  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::checkLoginStatus
    // 0x147634c: mov             x1, x0
    // 0x1476350: stur            x1, [fp, #-0x18]
    // 0x1476354: r0 = Await()
    //     0x1476354: bl              #0x63248c  ; AwaitStub
    // 0x1476358: ldur            x0, [fp, #-0x10]
    // 0x147635c: LoadField: r1 = r0->field_f
    //     0x147635c: ldur            w1, [x0, #0xf]
    // 0x1476360: DecompressPointer r1
    //     0x1476360: add             x1, x1, HEAP, lsl #32
    // 0x1476364: r0 = controller()
    //     0x1476364: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1476368: mov             x1, x0
    // 0x147636c: r0 = getSalesAndCouponData()
    //     0x147636c: bl              #0x8b2268  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::getSalesAndCouponData
    // 0x1476370: mov             x1, x0
    // 0x1476374: stur            x1, [fp, #-0x18]
    // 0x1476378: r0 = Await()
    //     0x1476378: bl              #0x63248c  ; AwaitStub
    // 0x147637c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x147637c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1476380: ldr             x0, [x0, #0x1c80]
    //     0x1476384: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1476388: cmp             w0, w16
    //     0x147638c: b.ne            #0x1476398
    //     0x1476390: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1476394: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1476398: r0 = GetNavigation.context()
    //     0x1476398: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x147639c: cmp             w0, NULL
    // 0x14763a0: b.eq            #0x14763c0
    // 0x14763a4: mov             x1, x0
    // 0x14763a8: r0 = restartApp()
    //     0x14763a8: bl              #0x8a539c  ; [package:customer_app/app/presentation/custom_widgets/restart_widget.dart] RestartWidget::restartApp
    // 0x14763ac: r0 = GetResetExt.reset()
    //     0x14763ac: bl              #0x8a5030  ; [package:get/get_common/get_reset.dart] ::GetResetExt.reset
    // 0x14763b0: r0 = Null
    //     0x14763b0: mov             x0, NULL
    // 0x14763b4: r0 = ReturnAsyncNotFuture()
    //     0x14763b4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14763b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14763b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14763bc: b               #0x14762d8
    // 0x14763c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14763c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x14763c4, size: 0xfb0
    // 0x14763c4: EnterFrame
    //     0x14763c4: stp             fp, lr, [SP, #-0x10]!
    //     0x14763c8: mov             fp, SP
    // 0x14763cc: AllocStack(0x30)
    //     0x14763cc: sub             SP, SP, #0x30
    // 0x14763d0: SetupParameters()
    //     0x14763d0: ldr             x0, [fp, #0x10]
    //     0x14763d4: ldur            w2, [x0, #0x17]
    //     0x14763d8: add             x2, x2, HEAP, lsl #32
    //     0x14763dc: stur            x2, [fp, #-8]
    // 0x14763e0: CheckStackOverflow
    //     0x14763e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14763e4: cmp             SP, x16
    //     0x14763e8: b.ls            #0x147736c
    // 0x14763ec: LoadField: r1 = r2->field_f
    //     0x14763ec: ldur            w1, [x2, #0xf]
    // 0x14763f0: DecompressPointer r1
    //     0x14763f0: add             x1, x1, HEAP, lsl #32
    // 0x14763f4: r0 = controller()
    //     0x14763f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14763f8: mov             x1, x0
    // 0x14763fc: r0 = appConfigResponse()
    //     0x14763fc: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0x1476400: eor             x2, x0, #0x10
    // 0x1476404: ldur            x0, [fp, #-8]
    // 0x1476408: stur            x2, [fp, #-0x10]
    // 0x147640c: LoadField: r1 = r0->field_13
    //     0x147640c: ldur            w1, [x0, #0x13]
    // 0x1476410: DecompressPointer r1
    //     0x1476410: add             x1, x1, HEAP, lsl #32
    // 0x1476414: r0 = of()
    //     0x1476414: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1476418: LoadField: r1 = r0->field_87
    //     0x1476418: ldur            w1, [x0, #0x87]
    // 0x147641c: DecompressPointer r1
    //     0x147641c: add             x1, x1, HEAP, lsl #32
    // 0x1476420: LoadField: r0 = r1->field_7
    //     0x1476420: ldur            w0, [x1, #7]
    // 0x1476424: DecompressPointer r0
    //     0x1476424: add             x0, x0, HEAP, lsl #32
    // 0x1476428: r16 = 14.000000
    //     0x1476428: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x147642c: ldr             x16, [x16, #0x1d8]
    // 0x1476430: str             x16, [SP]
    // 0x1476434: mov             x1, x0
    // 0x1476438: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1476438: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x147643c: ldr             x4, [x4, #0x798]
    // 0x1476440: r0 = copyWith()
    //     0x1476440: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1476444: stur            x0, [fp, #-0x18]
    // 0x1476448: r0 = Text()
    //     0x1476448: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x147644c: mov             x3, x0
    // 0x1476450: r0 = "Login"
    //     0x1476450: add             x0, PP, #0x23, lsl #12  ; [pp+0x23770] "Login"
    //     0x1476454: ldr             x0, [x0, #0x770]
    // 0x1476458: stur            x3, [fp, #-0x20]
    // 0x147645c: StoreField: r3->field_b = r0
    //     0x147645c: stur            w0, [x3, #0xb]
    // 0x1476460: ldur            x0, [fp, #-0x18]
    // 0x1476464: StoreField: r3->field_13 = r0
    //     0x1476464: stur            w0, [x3, #0x13]
    // 0x1476468: r1 = Null
    //     0x1476468: mov             x1, NULL
    // 0x147646c: r2 = 6
    //     0x147646c: movz            x2, #0x6
    // 0x1476470: r0 = AllocateArray()
    //     0x1476470: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1476474: stur            x0, [fp, #-0x18]
    // 0x1476478: r16 = Instance_Icon
    //     0x1476478: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c38] Obj!Icon@d66a71
    //     0x147647c: ldr             x16, [x16, #0xc38]
    // 0x1476480: StoreField: r0->field_f = r16
    //     0x1476480: stur            w16, [x0, #0xf]
    // 0x1476484: r16 = Instance_SizedBox
    //     0x1476484: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x1476488: ldr             x16, [x16, #0xaa8]
    // 0x147648c: StoreField: r0->field_13 = r16
    //     0x147648c: stur            w16, [x0, #0x13]
    // 0x1476490: ldur            x1, [fp, #-0x20]
    // 0x1476494: ArrayStore: r0[0] = r1  ; List_4
    //     0x1476494: stur            w1, [x0, #0x17]
    // 0x1476498: r1 = <Widget>
    //     0x1476498: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x147649c: r0 = AllocateGrowableArray()
    //     0x147649c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14764a0: mov             x1, x0
    // 0x14764a4: ldur            x0, [fp, #-0x18]
    // 0x14764a8: stur            x1, [fp, #-0x20]
    // 0x14764ac: StoreField: r1->field_f = r0
    //     0x14764ac: stur            w0, [x1, #0xf]
    // 0x14764b0: r2 = 6
    //     0x14764b0: movz            x2, #0x6
    // 0x14764b4: StoreField: r1->field_b = r2
    //     0x14764b4: stur            w2, [x1, #0xb]
    // 0x14764b8: r0 = Row()
    //     0x14764b8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14764bc: mov             x1, x0
    // 0x14764c0: r0 = Instance_Axis
    //     0x14764c0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14764c4: stur            x1, [fp, #-0x18]
    // 0x14764c8: StoreField: r1->field_f = r0
    //     0x14764c8: stur            w0, [x1, #0xf]
    // 0x14764cc: r2 = Instance_MainAxisAlignment
    //     0x14764cc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14764d0: ldr             x2, [x2, #0xa08]
    // 0x14764d4: StoreField: r1->field_13 = r2
    //     0x14764d4: stur            w2, [x1, #0x13]
    // 0x14764d8: r3 = Instance_MainAxisSize
    //     0x14764d8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14764dc: ldr             x3, [x3, #0xa10]
    // 0x14764e0: ArrayStore: r1[0] = r3  ; List_4
    //     0x14764e0: stur            w3, [x1, #0x17]
    // 0x14764e4: r4 = Instance_CrossAxisAlignment
    //     0x14764e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14764e8: ldr             x4, [x4, #0xa18]
    // 0x14764ec: StoreField: r1->field_1b = r4
    //     0x14764ec: stur            w4, [x1, #0x1b]
    // 0x14764f0: r5 = Instance_VerticalDirection
    //     0x14764f0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14764f4: ldr             x5, [x5, #0xa20]
    // 0x14764f8: StoreField: r1->field_23 = r5
    //     0x14764f8: stur            w5, [x1, #0x23]
    // 0x14764fc: r6 = Instance_Clip
    //     0x14764fc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1476500: ldr             x6, [x6, #0x38]
    // 0x1476504: StoreField: r1->field_2b = r6
    //     0x1476504: stur            w6, [x1, #0x2b]
    // 0x1476508: StoreField: r1->field_2f = rZR
    //     0x1476508: stur            xzr, [x1, #0x2f]
    // 0x147650c: ldur            x7, [fp, #-0x20]
    // 0x1476510: StoreField: r1->field_b = r7
    //     0x1476510: stur            w7, [x1, #0xb]
    // 0x1476514: r0 = InkWell()
    //     0x1476514: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1476518: mov             x3, x0
    // 0x147651c: ldur            x0, [fp, #-0x18]
    // 0x1476520: stur            x3, [fp, #-0x20]
    // 0x1476524: StoreField: r3->field_b = r0
    //     0x1476524: stur            w0, [x3, #0xb]
    // 0x1476528: ldur            x2, [fp, #-8]
    // 0x147652c: r1 = Function '<anonymous closure>':.
    //     0x147652c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c40] AnonymousClosure: (0x1477608), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1476530: ldr             x1, [x1, #0xc40]
    // 0x1476534: r0 = AllocateClosure()
    //     0x1476534: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1476538: mov             x1, x0
    // 0x147653c: ldur            x0, [fp, #-0x20]
    // 0x1476540: StoreField: r0->field_f = r1
    //     0x1476540: stur            w1, [x0, #0xf]
    // 0x1476544: r1 = true
    //     0x1476544: add             x1, NULL, #0x20  ; true
    // 0x1476548: StoreField: r0->field_43 = r1
    //     0x1476548: stur            w1, [x0, #0x43]
    // 0x147654c: r2 = Instance_BoxShape
    //     0x147654c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1476550: ldr             x2, [x2, #0x80]
    // 0x1476554: StoreField: r0->field_47 = r2
    //     0x1476554: stur            w2, [x0, #0x47]
    // 0x1476558: r3 = Instance_Color
    //     0x1476558: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x147655c: ldr             x3, [x3, #0xf88]
    // 0x1476560: StoreField: r0->field_5f = r3
    //     0x1476560: stur            w3, [x0, #0x5f]
    // 0x1476564: r4 = Instance__NoSplashFactory
    //     0x1476564: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1476568: ldr             x4, [x4, #0xc48]
    // 0x147656c: StoreField: r0->field_6b = r4
    //     0x147656c: stur            w4, [x0, #0x6b]
    // 0x1476570: StoreField: r0->field_6f = r1
    //     0x1476570: stur            w1, [x0, #0x6f]
    // 0x1476574: r5 = false
    //     0x1476574: add             x5, NULL, #0x30  ; false
    // 0x1476578: StoreField: r0->field_73 = r5
    //     0x1476578: stur            w5, [x0, #0x73]
    // 0x147657c: StoreField: r0->field_83 = r1
    //     0x147657c: stur            w1, [x0, #0x83]
    // 0x1476580: StoreField: r0->field_7b = r5
    //     0x1476580: stur            w5, [x0, #0x7b]
    // 0x1476584: r0 = Visibility()
    //     0x1476584: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1476588: mov             x3, x0
    // 0x147658c: ldur            x0, [fp, #-0x20]
    // 0x1476590: stur            x3, [fp, #-0x18]
    // 0x1476594: StoreField: r3->field_b = r0
    //     0x1476594: stur            w0, [x3, #0xb]
    // 0x1476598: r0 = Instance_SizedBox
    //     0x1476598: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x147659c: StoreField: r3->field_f = r0
    //     0x147659c: stur            w0, [x3, #0xf]
    // 0x14765a0: ldur            x1, [fp, #-0x10]
    // 0x14765a4: StoreField: r3->field_13 = r1
    //     0x14765a4: stur            w1, [x3, #0x13]
    // 0x14765a8: r4 = false
    //     0x14765a8: add             x4, NULL, #0x30  ; false
    // 0x14765ac: ArrayStore: r3[0] = r4  ; List_4
    //     0x14765ac: stur            w4, [x3, #0x17]
    // 0x14765b0: StoreField: r3->field_1b = r4
    //     0x14765b0: stur            w4, [x3, #0x1b]
    // 0x14765b4: StoreField: r3->field_1f = r4
    //     0x14765b4: stur            w4, [x3, #0x1f]
    // 0x14765b8: StoreField: r3->field_23 = r4
    //     0x14765b8: stur            w4, [x3, #0x23]
    // 0x14765bc: StoreField: r3->field_27 = r4
    //     0x14765bc: stur            w4, [x3, #0x27]
    // 0x14765c0: StoreField: r3->field_2b = r4
    //     0x14765c0: stur            w4, [x3, #0x2b]
    // 0x14765c4: r1 = <Widget>
    //     0x14765c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14765c8: r2 = 30
    //     0x14765c8: movz            x2, #0x1e
    // 0x14765cc: r0 = AllocateArray()
    //     0x14765cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14765d0: mov             x2, x0
    // 0x14765d4: ldur            x0, [fp, #-0x18]
    // 0x14765d8: stur            x2, [fp, #-0x10]
    // 0x14765dc: StoreField: r2->field_f = r0
    //     0x14765dc: stur            w0, [x2, #0xf]
    // 0x14765e0: ldur            x0, [fp, #-8]
    // 0x14765e4: LoadField: r1 = r0->field_f
    //     0x14765e4: ldur            w1, [x0, #0xf]
    // 0x14765e8: DecompressPointer r1
    //     0x14765e8: add             x1, x1, HEAP, lsl #32
    // 0x14765ec: r0 = controller()
    //     0x14765ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14765f0: LoadField: r1 = r0->field_5b
    //     0x14765f0: ldur            w1, [x0, #0x5b]
    // 0x14765f4: DecompressPointer r1
    //     0x14765f4: add             x1, x1, HEAP, lsl #32
    // 0x14765f8: r0 = value()
    //     0x14765f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14765fc: eor             x1, x0, #0x10
    // 0x1476600: stur            x1, [fp, #-0x18]
    // 0x1476604: r0 = Visibility()
    //     0x1476604: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1476608: mov             x1, x0
    // 0x147660c: r0 = Instance_SizedBox
    //     0x147660c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1476610: ldr             x0, [x0, #0xc50]
    // 0x1476614: StoreField: r1->field_b = r0
    //     0x1476614: stur            w0, [x1, #0xb]
    // 0x1476618: r2 = Instance_SizedBox
    //     0x1476618: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x147661c: StoreField: r1->field_f = r2
    //     0x147661c: stur            w2, [x1, #0xf]
    // 0x1476620: ldur            x0, [fp, #-0x18]
    // 0x1476624: StoreField: r1->field_13 = r0
    //     0x1476624: stur            w0, [x1, #0x13]
    // 0x1476628: r3 = false
    //     0x1476628: add             x3, NULL, #0x30  ; false
    // 0x147662c: ArrayStore: r1[0] = r3  ; List_4
    //     0x147662c: stur            w3, [x1, #0x17]
    // 0x1476630: StoreField: r1->field_1b = r3
    //     0x1476630: stur            w3, [x1, #0x1b]
    // 0x1476634: StoreField: r1->field_1f = r3
    //     0x1476634: stur            w3, [x1, #0x1f]
    // 0x1476638: StoreField: r1->field_23 = r3
    //     0x1476638: stur            w3, [x1, #0x23]
    // 0x147663c: StoreField: r1->field_27 = r3
    //     0x147663c: stur            w3, [x1, #0x27]
    // 0x1476640: StoreField: r1->field_2b = r3
    //     0x1476640: stur            w3, [x1, #0x2b]
    // 0x1476644: mov             x0, x1
    // 0x1476648: ldur            x1, [fp, #-0x10]
    // 0x147664c: ArrayStore: r1[1] = r0  ; List_4
    //     0x147664c: add             x25, x1, #0x13
    //     0x1476650: str             w0, [x25]
    //     0x1476654: tbz             w0, #0, #0x1476670
    //     0x1476658: ldurb           w16, [x1, #-1]
    //     0x147665c: ldurb           w17, [x0, #-1]
    //     0x1476660: and             x16, x17, x16, lsr #2
    //     0x1476664: tst             x16, HEAP, lsr #32
    //     0x1476668: b.eq            #0x1476670
    //     0x147666c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1476670: ldur            x0, [fp, #-8]
    // 0x1476674: LoadField: r1 = r0->field_13
    //     0x1476674: ldur            w1, [x0, #0x13]
    // 0x1476678: DecompressPointer r1
    //     0x1476678: add             x1, x1, HEAP, lsl #32
    // 0x147667c: r0 = of()
    //     0x147667c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1476680: LoadField: r1 = r0->field_87
    //     0x1476680: ldur            w1, [x0, #0x87]
    // 0x1476684: DecompressPointer r1
    //     0x1476684: add             x1, x1, HEAP, lsl #32
    // 0x1476688: LoadField: r0 = r1->field_7
    //     0x1476688: ldur            w0, [x1, #7]
    // 0x147668c: DecompressPointer r0
    //     0x147668c: add             x0, x0, HEAP, lsl #32
    // 0x1476690: r16 = 14.000000
    //     0x1476690: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1476694: ldr             x16, [x16, #0x1d8]
    // 0x1476698: str             x16, [SP]
    // 0x147669c: mov             x1, x0
    // 0x14766a0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x14766a0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x14766a4: ldr             x4, [x4, #0x798]
    // 0x14766a8: r0 = copyWith()
    //     0x14766a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14766ac: stur            x0, [fp, #-0x18]
    // 0x14766b0: r0 = Text()
    //     0x14766b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14766b4: mov             x3, x0
    // 0x14766b8: r0 = "About Us"
    //     0x14766b8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c58] "About Us"
    //     0x14766bc: ldr             x0, [x0, #0xc58]
    // 0x14766c0: stur            x3, [fp, #-0x20]
    // 0x14766c4: StoreField: r3->field_b = r0
    //     0x14766c4: stur            w0, [x3, #0xb]
    // 0x14766c8: ldur            x0, [fp, #-0x18]
    // 0x14766cc: StoreField: r3->field_13 = r0
    //     0x14766cc: stur            w0, [x3, #0x13]
    // 0x14766d0: r1 = Null
    //     0x14766d0: mov             x1, NULL
    // 0x14766d4: r2 = 2
    //     0x14766d4: movz            x2, #0x2
    // 0x14766d8: r0 = AllocateArray()
    //     0x14766d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14766dc: mov             x2, x0
    // 0x14766e0: ldur            x0, [fp, #-0x20]
    // 0x14766e4: stur            x2, [fp, #-0x18]
    // 0x14766e8: StoreField: r2->field_f = r0
    //     0x14766e8: stur            w0, [x2, #0xf]
    // 0x14766ec: r1 = <Widget>
    //     0x14766ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14766f0: r0 = AllocateGrowableArray()
    //     0x14766f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14766f4: mov             x1, x0
    // 0x14766f8: ldur            x0, [fp, #-0x18]
    // 0x14766fc: stur            x1, [fp, #-0x20]
    // 0x1476700: StoreField: r1->field_f = r0
    //     0x1476700: stur            w0, [x1, #0xf]
    // 0x1476704: r2 = 2
    //     0x1476704: movz            x2, #0x2
    // 0x1476708: StoreField: r1->field_b = r2
    //     0x1476708: stur            w2, [x1, #0xb]
    // 0x147670c: r0 = Row()
    //     0x147670c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1476710: mov             x1, x0
    // 0x1476714: r0 = Instance_Axis
    //     0x1476714: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1476718: stur            x1, [fp, #-0x18]
    // 0x147671c: StoreField: r1->field_f = r0
    //     0x147671c: stur            w0, [x1, #0xf]
    // 0x1476720: r2 = Instance_MainAxisAlignment
    //     0x1476720: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1476724: ldr             x2, [x2, #0xa08]
    // 0x1476728: StoreField: r1->field_13 = r2
    //     0x1476728: stur            w2, [x1, #0x13]
    // 0x147672c: r3 = Instance_MainAxisSize
    //     0x147672c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1476730: ldr             x3, [x3, #0xa10]
    // 0x1476734: ArrayStore: r1[0] = r3  ; List_4
    //     0x1476734: stur            w3, [x1, #0x17]
    // 0x1476738: r4 = Instance_CrossAxisAlignment
    //     0x1476738: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x147673c: ldr             x4, [x4, #0xa18]
    // 0x1476740: StoreField: r1->field_1b = r4
    //     0x1476740: stur            w4, [x1, #0x1b]
    // 0x1476744: r5 = Instance_VerticalDirection
    //     0x1476744: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1476748: ldr             x5, [x5, #0xa20]
    // 0x147674c: StoreField: r1->field_23 = r5
    //     0x147674c: stur            w5, [x1, #0x23]
    // 0x1476750: r6 = Instance_Clip
    //     0x1476750: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1476754: ldr             x6, [x6, #0x38]
    // 0x1476758: StoreField: r1->field_2b = r6
    //     0x1476758: stur            w6, [x1, #0x2b]
    // 0x147675c: StoreField: r1->field_2f = rZR
    //     0x147675c: stur            xzr, [x1, #0x2f]
    // 0x1476760: ldur            x7, [fp, #-0x20]
    // 0x1476764: StoreField: r1->field_b = r7
    //     0x1476764: stur            w7, [x1, #0xb]
    // 0x1476768: r0 = InkWell()
    //     0x1476768: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x147676c: mov             x3, x0
    // 0x1476770: ldur            x0, [fp, #-0x18]
    // 0x1476774: stur            x3, [fp, #-0x20]
    // 0x1476778: StoreField: r3->field_b = r0
    //     0x1476778: stur            w0, [x3, #0xb]
    // 0x147677c: ldur            x2, [fp, #-8]
    // 0x1476780: r1 = Function '<anonymous closure>':.
    //     0x1476780: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c60] AnonymousClosure: (0x14775b8), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1476784: ldr             x1, [x1, #0xc60]
    // 0x1476788: r0 = AllocateClosure()
    //     0x1476788: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147678c: mov             x1, x0
    // 0x1476790: ldur            x0, [fp, #-0x20]
    // 0x1476794: StoreField: r0->field_f = r1
    //     0x1476794: stur            w1, [x0, #0xf]
    // 0x1476798: r1 = true
    //     0x1476798: add             x1, NULL, #0x20  ; true
    // 0x147679c: StoreField: r0->field_43 = r1
    //     0x147679c: stur            w1, [x0, #0x43]
    // 0x14767a0: r2 = Instance_BoxShape
    //     0x14767a0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14767a4: ldr             x2, [x2, #0x80]
    // 0x14767a8: StoreField: r0->field_47 = r2
    //     0x14767a8: stur            w2, [x0, #0x47]
    // 0x14767ac: r3 = Instance_Color
    //     0x14767ac: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x14767b0: ldr             x3, [x3, #0xf88]
    // 0x14767b4: StoreField: r0->field_5f = r3
    //     0x14767b4: stur            w3, [x0, #0x5f]
    // 0x14767b8: r4 = Instance__NoSplashFactory
    //     0x14767b8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x14767bc: ldr             x4, [x4, #0xc48]
    // 0x14767c0: StoreField: r0->field_6b = r4
    //     0x14767c0: stur            w4, [x0, #0x6b]
    // 0x14767c4: StoreField: r0->field_6f = r1
    //     0x14767c4: stur            w1, [x0, #0x6f]
    // 0x14767c8: r5 = false
    //     0x14767c8: add             x5, NULL, #0x30  ; false
    // 0x14767cc: StoreField: r0->field_73 = r5
    //     0x14767cc: stur            w5, [x0, #0x73]
    // 0x14767d0: StoreField: r0->field_83 = r1
    //     0x14767d0: stur            w1, [x0, #0x83]
    // 0x14767d4: StoreField: r0->field_7b = r5
    //     0x14767d4: stur            w5, [x0, #0x7b]
    // 0x14767d8: r0 = Align()
    //     0x14767d8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14767dc: r2 = Instance_Alignment
    //     0x14767dc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x14767e0: ldr             x2, [x2, #0xfa0]
    // 0x14767e4: StoreField: r0->field_f = r2
    //     0x14767e4: stur            w2, [x0, #0xf]
    // 0x14767e8: ldur            x1, [fp, #-0x20]
    // 0x14767ec: StoreField: r0->field_b = r1
    //     0x14767ec: stur            w1, [x0, #0xb]
    // 0x14767f0: ldur            x1, [fp, #-0x10]
    // 0x14767f4: ArrayStore: r1[2] = r0  ; List_4
    //     0x14767f4: add             x25, x1, #0x17
    //     0x14767f8: str             w0, [x25]
    //     0x14767fc: tbz             w0, #0, #0x1476818
    //     0x1476800: ldurb           w16, [x1, #-1]
    //     0x1476804: ldurb           w17, [x0, #-1]
    //     0x1476808: and             x16, x17, x16, lsr #2
    //     0x147680c: tst             x16, HEAP, lsr #32
    //     0x1476810: b.eq            #0x1476818
    //     0x1476814: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1476818: ldur            x0, [fp, #-0x10]
    // 0x147681c: r16 = Instance_SizedBox
    //     0x147681c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1476820: ldr             x16, [x16, #0xc50]
    // 0x1476824: StoreField: r0->field_1b = r16
    //     0x1476824: stur            w16, [x0, #0x1b]
    // 0x1476828: ldur            x3, [fp, #-8]
    // 0x147682c: LoadField: r1 = r3->field_13
    //     0x147682c: ldur            w1, [x3, #0x13]
    // 0x1476830: DecompressPointer r1
    //     0x1476830: add             x1, x1, HEAP, lsl #32
    // 0x1476834: r0 = of()
    //     0x1476834: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1476838: LoadField: r1 = r0->field_87
    //     0x1476838: ldur            w1, [x0, #0x87]
    // 0x147683c: DecompressPointer r1
    //     0x147683c: add             x1, x1, HEAP, lsl #32
    // 0x1476840: LoadField: r0 = r1->field_7
    //     0x1476840: ldur            w0, [x1, #7]
    // 0x1476844: DecompressPointer r0
    //     0x1476844: add             x0, x0, HEAP, lsl #32
    // 0x1476848: r16 = 14.000000
    //     0x1476848: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x147684c: ldr             x16, [x16, #0x1d8]
    // 0x1476850: str             x16, [SP]
    // 0x1476854: mov             x1, x0
    // 0x1476858: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1476858: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x147685c: ldr             x4, [x4, #0x798]
    // 0x1476860: r0 = copyWith()
    //     0x1476860: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1476864: stur            x0, [fp, #-0x18]
    // 0x1476868: r0 = Text()
    //     0x1476868: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x147686c: mov             x3, x0
    // 0x1476870: r0 = "Privacy Policy"
    //     0x1476870: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c68] "Privacy Policy"
    //     0x1476874: ldr             x0, [x0, #0xc68]
    // 0x1476878: stur            x3, [fp, #-0x20]
    // 0x147687c: StoreField: r3->field_b = r0
    //     0x147687c: stur            w0, [x3, #0xb]
    // 0x1476880: ldur            x0, [fp, #-0x18]
    // 0x1476884: StoreField: r3->field_13 = r0
    //     0x1476884: stur            w0, [x3, #0x13]
    // 0x1476888: r1 = Null
    //     0x1476888: mov             x1, NULL
    // 0x147688c: r2 = 2
    //     0x147688c: movz            x2, #0x2
    // 0x1476890: r0 = AllocateArray()
    //     0x1476890: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1476894: mov             x2, x0
    // 0x1476898: ldur            x0, [fp, #-0x20]
    // 0x147689c: stur            x2, [fp, #-0x18]
    // 0x14768a0: StoreField: r2->field_f = r0
    //     0x14768a0: stur            w0, [x2, #0xf]
    // 0x14768a4: r1 = <Widget>
    //     0x14768a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14768a8: r0 = AllocateGrowableArray()
    //     0x14768a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14768ac: mov             x1, x0
    // 0x14768b0: ldur            x0, [fp, #-0x18]
    // 0x14768b4: stur            x1, [fp, #-0x20]
    // 0x14768b8: StoreField: r1->field_f = r0
    //     0x14768b8: stur            w0, [x1, #0xf]
    // 0x14768bc: r2 = 2
    //     0x14768bc: movz            x2, #0x2
    // 0x14768c0: StoreField: r1->field_b = r2
    //     0x14768c0: stur            w2, [x1, #0xb]
    // 0x14768c4: r0 = Row()
    //     0x14768c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14768c8: mov             x1, x0
    // 0x14768cc: r0 = Instance_Axis
    //     0x14768cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14768d0: stur            x1, [fp, #-0x18]
    // 0x14768d4: StoreField: r1->field_f = r0
    //     0x14768d4: stur            w0, [x1, #0xf]
    // 0x14768d8: r2 = Instance_MainAxisAlignment
    //     0x14768d8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14768dc: ldr             x2, [x2, #0xa08]
    // 0x14768e0: StoreField: r1->field_13 = r2
    //     0x14768e0: stur            w2, [x1, #0x13]
    // 0x14768e4: r3 = Instance_MainAxisSize
    //     0x14768e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14768e8: ldr             x3, [x3, #0xa10]
    // 0x14768ec: ArrayStore: r1[0] = r3  ; List_4
    //     0x14768ec: stur            w3, [x1, #0x17]
    // 0x14768f0: r4 = Instance_CrossAxisAlignment
    //     0x14768f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14768f4: ldr             x4, [x4, #0xa18]
    // 0x14768f8: StoreField: r1->field_1b = r4
    //     0x14768f8: stur            w4, [x1, #0x1b]
    // 0x14768fc: r5 = Instance_VerticalDirection
    //     0x14768fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1476900: ldr             x5, [x5, #0xa20]
    // 0x1476904: StoreField: r1->field_23 = r5
    //     0x1476904: stur            w5, [x1, #0x23]
    // 0x1476908: r6 = Instance_Clip
    //     0x1476908: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x147690c: ldr             x6, [x6, #0x38]
    // 0x1476910: StoreField: r1->field_2b = r6
    //     0x1476910: stur            w6, [x1, #0x2b]
    // 0x1476914: StoreField: r1->field_2f = rZR
    //     0x1476914: stur            xzr, [x1, #0x2f]
    // 0x1476918: ldur            x7, [fp, #-0x20]
    // 0x147691c: StoreField: r1->field_b = r7
    //     0x147691c: stur            w7, [x1, #0xb]
    // 0x1476920: r0 = InkWell()
    //     0x1476920: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1476924: mov             x3, x0
    // 0x1476928: ldur            x0, [fp, #-0x18]
    // 0x147692c: stur            x3, [fp, #-0x20]
    // 0x1476930: StoreField: r3->field_b = r0
    //     0x1476930: stur            w0, [x3, #0xb]
    // 0x1476934: ldur            x2, [fp, #-8]
    // 0x1476938: r1 = Function '<anonymous closure>':.
    //     0x1476938: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c70] AnonymousClosure: (0x1477568), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x147693c: ldr             x1, [x1, #0xc70]
    // 0x1476940: r0 = AllocateClosure()
    //     0x1476940: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1476944: mov             x1, x0
    // 0x1476948: ldur            x0, [fp, #-0x20]
    // 0x147694c: StoreField: r0->field_f = r1
    //     0x147694c: stur            w1, [x0, #0xf]
    // 0x1476950: r1 = true
    //     0x1476950: add             x1, NULL, #0x20  ; true
    // 0x1476954: StoreField: r0->field_43 = r1
    //     0x1476954: stur            w1, [x0, #0x43]
    // 0x1476958: r2 = Instance_BoxShape
    //     0x1476958: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x147695c: ldr             x2, [x2, #0x80]
    // 0x1476960: StoreField: r0->field_47 = r2
    //     0x1476960: stur            w2, [x0, #0x47]
    // 0x1476964: r3 = Instance_Color
    //     0x1476964: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1476968: ldr             x3, [x3, #0xf88]
    // 0x147696c: StoreField: r0->field_5f = r3
    //     0x147696c: stur            w3, [x0, #0x5f]
    // 0x1476970: r4 = Instance__NoSplashFactory
    //     0x1476970: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1476974: ldr             x4, [x4, #0xc48]
    // 0x1476978: StoreField: r0->field_6b = r4
    //     0x1476978: stur            w4, [x0, #0x6b]
    // 0x147697c: StoreField: r0->field_6f = r1
    //     0x147697c: stur            w1, [x0, #0x6f]
    // 0x1476980: r5 = false
    //     0x1476980: add             x5, NULL, #0x30  ; false
    // 0x1476984: StoreField: r0->field_73 = r5
    //     0x1476984: stur            w5, [x0, #0x73]
    // 0x1476988: StoreField: r0->field_83 = r1
    //     0x1476988: stur            w1, [x0, #0x83]
    // 0x147698c: StoreField: r0->field_7b = r5
    //     0x147698c: stur            w5, [x0, #0x7b]
    // 0x1476990: r0 = Align()
    //     0x1476990: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1476994: r2 = Instance_Alignment
    //     0x1476994: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x1476998: ldr             x2, [x2, #0xfa0]
    // 0x147699c: StoreField: r0->field_f = r2
    //     0x147699c: stur            w2, [x0, #0xf]
    // 0x14769a0: ldur            x1, [fp, #-0x20]
    // 0x14769a4: StoreField: r0->field_b = r1
    //     0x14769a4: stur            w1, [x0, #0xb]
    // 0x14769a8: ldur            x1, [fp, #-0x10]
    // 0x14769ac: ArrayStore: r1[4] = r0  ; List_4
    //     0x14769ac: add             x25, x1, #0x1f
    //     0x14769b0: str             w0, [x25]
    //     0x14769b4: tbz             w0, #0, #0x14769d0
    //     0x14769b8: ldurb           w16, [x1, #-1]
    //     0x14769bc: ldurb           w17, [x0, #-1]
    //     0x14769c0: and             x16, x17, x16, lsr #2
    //     0x14769c4: tst             x16, HEAP, lsr #32
    //     0x14769c8: b.eq            #0x14769d0
    //     0x14769cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14769d0: ldur            x0, [fp, #-0x10]
    // 0x14769d4: r16 = Instance_SizedBox
    //     0x14769d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14769d8: ldr             x16, [x16, #0xc50]
    // 0x14769dc: StoreField: r0->field_23 = r16
    //     0x14769dc: stur            w16, [x0, #0x23]
    // 0x14769e0: ldur            x3, [fp, #-8]
    // 0x14769e4: LoadField: r1 = r3->field_13
    //     0x14769e4: ldur            w1, [x3, #0x13]
    // 0x14769e8: DecompressPointer r1
    //     0x14769e8: add             x1, x1, HEAP, lsl #32
    // 0x14769ec: r0 = of()
    //     0x14769ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14769f0: LoadField: r1 = r0->field_87
    //     0x14769f0: ldur            w1, [x0, #0x87]
    // 0x14769f4: DecompressPointer r1
    //     0x14769f4: add             x1, x1, HEAP, lsl #32
    // 0x14769f8: LoadField: r0 = r1->field_7
    //     0x14769f8: ldur            w0, [x1, #7]
    // 0x14769fc: DecompressPointer r0
    //     0x14769fc: add             x0, x0, HEAP, lsl #32
    // 0x1476a00: r16 = 14.000000
    //     0x1476a00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1476a04: ldr             x16, [x16, #0x1d8]
    // 0x1476a08: str             x16, [SP]
    // 0x1476a0c: mov             x1, x0
    // 0x1476a10: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1476a10: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1476a14: ldr             x4, [x4, #0x798]
    // 0x1476a18: r0 = copyWith()
    //     0x1476a18: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1476a1c: stur            x0, [fp, #-0x18]
    // 0x1476a20: r0 = Text()
    //     0x1476a20: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1476a24: mov             x3, x0
    // 0x1476a28: r0 = "Return Policy"
    //     0x1476a28: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c78] "Return Policy"
    //     0x1476a2c: ldr             x0, [x0, #0xc78]
    // 0x1476a30: stur            x3, [fp, #-0x20]
    // 0x1476a34: StoreField: r3->field_b = r0
    //     0x1476a34: stur            w0, [x3, #0xb]
    // 0x1476a38: ldur            x0, [fp, #-0x18]
    // 0x1476a3c: StoreField: r3->field_13 = r0
    //     0x1476a3c: stur            w0, [x3, #0x13]
    // 0x1476a40: r1 = Null
    //     0x1476a40: mov             x1, NULL
    // 0x1476a44: r2 = 2
    //     0x1476a44: movz            x2, #0x2
    // 0x1476a48: r0 = AllocateArray()
    //     0x1476a48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1476a4c: mov             x2, x0
    // 0x1476a50: ldur            x0, [fp, #-0x20]
    // 0x1476a54: stur            x2, [fp, #-0x18]
    // 0x1476a58: StoreField: r2->field_f = r0
    //     0x1476a58: stur            w0, [x2, #0xf]
    // 0x1476a5c: r1 = <Widget>
    //     0x1476a5c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1476a60: r0 = AllocateGrowableArray()
    //     0x1476a60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1476a64: mov             x1, x0
    // 0x1476a68: ldur            x0, [fp, #-0x18]
    // 0x1476a6c: stur            x1, [fp, #-0x20]
    // 0x1476a70: StoreField: r1->field_f = r0
    //     0x1476a70: stur            w0, [x1, #0xf]
    // 0x1476a74: r2 = 2
    //     0x1476a74: movz            x2, #0x2
    // 0x1476a78: StoreField: r1->field_b = r2
    //     0x1476a78: stur            w2, [x1, #0xb]
    // 0x1476a7c: r0 = Row()
    //     0x1476a7c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1476a80: mov             x1, x0
    // 0x1476a84: r0 = Instance_Axis
    //     0x1476a84: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1476a88: stur            x1, [fp, #-0x18]
    // 0x1476a8c: StoreField: r1->field_f = r0
    //     0x1476a8c: stur            w0, [x1, #0xf]
    // 0x1476a90: r2 = Instance_MainAxisAlignment
    //     0x1476a90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1476a94: ldr             x2, [x2, #0xa08]
    // 0x1476a98: StoreField: r1->field_13 = r2
    //     0x1476a98: stur            w2, [x1, #0x13]
    // 0x1476a9c: r3 = Instance_MainAxisSize
    //     0x1476a9c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1476aa0: ldr             x3, [x3, #0xa10]
    // 0x1476aa4: ArrayStore: r1[0] = r3  ; List_4
    //     0x1476aa4: stur            w3, [x1, #0x17]
    // 0x1476aa8: r4 = Instance_CrossAxisAlignment
    //     0x1476aa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1476aac: ldr             x4, [x4, #0xa18]
    // 0x1476ab0: StoreField: r1->field_1b = r4
    //     0x1476ab0: stur            w4, [x1, #0x1b]
    // 0x1476ab4: r5 = Instance_VerticalDirection
    //     0x1476ab4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1476ab8: ldr             x5, [x5, #0xa20]
    // 0x1476abc: StoreField: r1->field_23 = r5
    //     0x1476abc: stur            w5, [x1, #0x23]
    // 0x1476ac0: r6 = Instance_Clip
    //     0x1476ac0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1476ac4: ldr             x6, [x6, #0x38]
    // 0x1476ac8: StoreField: r1->field_2b = r6
    //     0x1476ac8: stur            w6, [x1, #0x2b]
    // 0x1476acc: StoreField: r1->field_2f = rZR
    //     0x1476acc: stur            xzr, [x1, #0x2f]
    // 0x1476ad0: ldur            x7, [fp, #-0x20]
    // 0x1476ad4: StoreField: r1->field_b = r7
    //     0x1476ad4: stur            w7, [x1, #0xb]
    // 0x1476ad8: r0 = InkWell()
    //     0x1476ad8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1476adc: mov             x3, x0
    // 0x1476ae0: ldur            x0, [fp, #-0x18]
    // 0x1476ae4: stur            x3, [fp, #-0x20]
    // 0x1476ae8: StoreField: r3->field_b = r0
    //     0x1476ae8: stur            w0, [x3, #0xb]
    // 0x1476aec: ldur            x2, [fp, #-8]
    // 0x1476af0: r1 = Function '<anonymous closure>':.
    //     0x1476af0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c80] AnonymousClosure: (0x1477518), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1476af4: ldr             x1, [x1, #0xc80]
    // 0x1476af8: r0 = AllocateClosure()
    //     0x1476af8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1476afc: mov             x1, x0
    // 0x1476b00: ldur            x0, [fp, #-0x20]
    // 0x1476b04: StoreField: r0->field_f = r1
    //     0x1476b04: stur            w1, [x0, #0xf]
    // 0x1476b08: r1 = true
    //     0x1476b08: add             x1, NULL, #0x20  ; true
    // 0x1476b0c: StoreField: r0->field_43 = r1
    //     0x1476b0c: stur            w1, [x0, #0x43]
    // 0x1476b10: r2 = Instance_BoxShape
    //     0x1476b10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1476b14: ldr             x2, [x2, #0x80]
    // 0x1476b18: StoreField: r0->field_47 = r2
    //     0x1476b18: stur            w2, [x0, #0x47]
    // 0x1476b1c: r3 = Instance_Color
    //     0x1476b1c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1476b20: ldr             x3, [x3, #0xf88]
    // 0x1476b24: StoreField: r0->field_5f = r3
    //     0x1476b24: stur            w3, [x0, #0x5f]
    // 0x1476b28: r4 = Instance__NoSplashFactory
    //     0x1476b28: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1476b2c: ldr             x4, [x4, #0xc48]
    // 0x1476b30: StoreField: r0->field_6b = r4
    //     0x1476b30: stur            w4, [x0, #0x6b]
    // 0x1476b34: StoreField: r0->field_6f = r1
    //     0x1476b34: stur            w1, [x0, #0x6f]
    // 0x1476b38: r5 = false
    //     0x1476b38: add             x5, NULL, #0x30  ; false
    // 0x1476b3c: StoreField: r0->field_73 = r5
    //     0x1476b3c: stur            w5, [x0, #0x73]
    // 0x1476b40: StoreField: r0->field_83 = r1
    //     0x1476b40: stur            w1, [x0, #0x83]
    // 0x1476b44: StoreField: r0->field_7b = r5
    //     0x1476b44: stur            w5, [x0, #0x7b]
    // 0x1476b48: r0 = Align()
    //     0x1476b48: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1476b4c: r2 = Instance_Alignment
    //     0x1476b4c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x1476b50: ldr             x2, [x2, #0xfa0]
    // 0x1476b54: StoreField: r0->field_f = r2
    //     0x1476b54: stur            w2, [x0, #0xf]
    // 0x1476b58: ldur            x1, [fp, #-0x20]
    // 0x1476b5c: StoreField: r0->field_b = r1
    //     0x1476b5c: stur            w1, [x0, #0xb]
    // 0x1476b60: ldur            x1, [fp, #-0x10]
    // 0x1476b64: ArrayStore: r1[6] = r0  ; List_4
    //     0x1476b64: add             x25, x1, #0x27
    //     0x1476b68: str             w0, [x25]
    //     0x1476b6c: tbz             w0, #0, #0x1476b88
    //     0x1476b70: ldurb           w16, [x1, #-1]
    //     0x1476b74: ldurb           w17, [x0, #-1]
    //     0x1476b78: and             x16, x17, x16, lsr #2
    //     0x1476b7c: tst             x16, HEAP, lsr #32
    //     0x1476b80: b.eq            #0x1476b88
    //     0x1476b84: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1476b88: ldur            x0, [fp, #-0x10]
    // 0x1476b8c: r16 = Instance_SizedBox
    //     0x1476b8c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1476b90: ldr             x16, [x16, #0xc50]
    // 0x1476b94: StoreField: r0->field_2b = r16
    //     0x1476b94: stur            w16, [x0, #0x2b]
    // 0x1476b98: ldur            x3, [fp, #-8]
    // 0x1476b9c: LoadField: r1 = r3->field_13
    //     0x1476b9c: ldur            w1, [x3, #0x13]
    // 0x1476ba0: DecompressPointer r1
    //     0x1476ba0: add             x1, x1, HEAP, lsl #32
    // 0x1476ba4: r0 = of()
    //     0x1476ba4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1476ba8: LoadField: r1 = r0->field_87
    //     0x1476ba8: ldur            w1, [x0, #0x87]
    // 0x1476bac: DecompressPointer r1
    //     0x1476bac: add             x1, x1, HEAP, lsl #32
    // 0x1476bb0: LoadField: r0 = r1->field_7
    //     0x1476bb0: ldur            w0, [x1, #7]
    // 0x1476bb4: DecompressPointer r0
    //     0x1476bb4: add             x0, x0, HEAP, lsl #32
    // 0x1476bb8: r16 = 14.000000
    //     0x1476bb8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1476bbc: ldr             x16, [x16, #0x1d8]
    // 0x1476bc0: str             x16, [SP]
    // 0x1476bc4: mov             x1, x0
    // 0x1476bc8: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1476bc8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1476bcc: ldr             x4, [x4, #0x798]
    // 0x1476bd0: r0 = copyWith()
    //     0x1476bd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1476bd4: stur            x0, [fp, #-0x18]
    // 0x1476bd8: r0 = Text()
    //     0x1476bd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1476bdc: mov             x3, x0
    // 0x1476be0: r0 = "Shipping Policy"
    //     0x1476be0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c88] "Shipping Policy"
    //     0x1476be4: ldr             x0, [x0, #0xc88]
    // 0x1476be8: stur            x3, [fp, #-0x20]
    // 0x1476bec: StoreField: r3->field_b = r0
    //     0x1476bec: stur            w0, [x3, #0xb]
    // 0x1476bf0: ldur            x0, [fp, #-0x18]
    // 0x1476bf4: StoreField: r3->field_13 = r0
    //     0x1476bf4: stur            w0, [x3, #0x13]
    // 0x1476bf8: r1 = Null
    //     0x1476bf8: mov             x1, NULL
    // 0x1476bfc: r2 = 2
    //     0x1476bfc: movz            x2, #0x2
    // 0x1476c00: r0 = AllocateArray()
    //     0x1476c00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1476c04: mov             x2, x0
    // 0x1476c08: ldur            x0, [fp, #-0x20]
    // 0x1476c0c: stur            x2, [fp, #-0x18]
    // 0x1476c10: StoreField: r2->field_f = r0
    //     0x1476c10: stur            w0, [x2, #0xf]
    // 0x1476c14: r1 = <Widget>
    //     0x1476c14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1476c18: r0 = AllocateGrowableArray()
    //     0x1476c18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1476c1c: mov             x1, x0
    // 0x1476c20: ldur            x0, [fp, #-0x18]
    // 0x1476c24: stur            x1, [fp, #-0x20]
    // 0x1476c28: StoreField: r1->field_f = r0
    //     0x1476c28: stur            w0, [x1, #0xf]
    // 0x1476c2c: r2 = 2
    //     0x1476c2c: movz            x2, #0x2
    // 0x1476c30: StoreField: r1->field_b = r2
    //     0x1476c30: stur            w2, [x1, #0xb]
    // 0x1476c34: r0 = Row()
    //     0x1476c34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1476c38: mov             x1, x0
    // 0x1476c3c: r0 = Instance_Axis
    //     0x1476c3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1476c40: stur            x1, [fp, #-0x18]
    // 0x1476c44: StoreField: r1->field_f = r0
    //     0x1476c44: stur            w0, [x1, #0xf]
    // 0x1476c48: r2 = Instance_MainAxisAlignment
    //     0x1476c48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1476c4c: ldr             x2, [x2, #0xa08]
    // 0x1476c50: StoreField: r1->field_13 = r2
    //     0x1476c50: stur            w2, [x1, #0x13]
    // 0x1476c54: r3 = Instance_MainAxisSize
    //     0x1476c54: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1476c58: ldr             x3, [x3, #0xa10]
    // 0x1476c5c: ArrayStore: r1[0] = r3  ; List_4
    //     0x1476c5c: stur            w3, [x1, #0x17]
    // 0x1476c60: r4 = Instance_CrossAxisAlignment
    //     0x1476c60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1476c64: ldr             x4, [x4, #0xa18]
    // 0x1476c68: StoreField: r1->field_1b = r4
    //     0x1476c68: stur            w4, [x1, #0x1b]
    // 0x1476c6c: r5 = Instance_VerticalDirection
    //     0x1476c6c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1476c70: ldr             x5, [x5, #0xa20]
    // 0x1476c74: StoreField: r1->field_23 = r5
    //     0x1476c74: stur            w5, [x1, #0x23]
    // 0x1476c78: r6 = Instance_Clip
    //     0x1476c78: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1476c7c: ldr             x6, [x6, #0x38]
    // 0x1476c80: StoreField: r1->field_2b = r6
    //     0x1476c80: stur            w6, [x1, #0x2b]
    // 0x1476c84: StoreField: r1->field_2f = rZR
    //     0x1476c84: stur            xzr, [x1, #0x2f]
    // 0x1476c88: ldur            x7, [fp, #-0x20]
    // 0x1476c8c: StoreField: r1->field_b = r7
    //     0x1476c8c: stur            w7, [x1, #0xb]
    // 0x1476c90: r0 = InkWell()
    //     0x1476c90: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1476c94: mov             x3, x0
    // 0x1476c98: ldur            x0, [fp, #-0x18]
    // 0x1476c9c: stur            x3, [fp, #-0x20]
    // 0x1476ca0: StoreField: r3->field_b = r0
    //     0x1476ca0: stur            w0, [x3, #0xb]
    // 0x1476ca4: ldur            x2, [fp, #-8]
    // 0x1476ca8: r1 = Function '<anonymous closure>':.
    //     0x1476ca8: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c90] AnonymousClosure: (0x14774c8), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1476cac: ldr             x1, [x1, #0xc90]
    // 0x1476cb0: r0 = AllocateClosure()
    //     0x1476cb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1476cb4: mov             x1, x0
    // 0x1476cb8: ldur            x0, [fp, #-0x20]
    // 0x1476cbc: StoreField: r0->field_f = r1
    //     0x1476cbc: stur            w1, [x0, #0xf]
    // 0x1476cc0: r1 = true
    //     0x1476cc0: add             x1, NULL, #0x20  ; true
    // 0x1476cc4: StoreField: r0->field_43 = r1
    //     0x1476cc4: stur            w1, [x0, #0x43]
    // 0x1476cc8: r2 = Instance_BoxShape
    //     0x1476cc8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1476ccc: ldr             x2, [x2, #0x80]
    // 0x1476cd0: StoreField: r0->field_47 = r2
    //     0x1476cd0: stur            w2, [x0, #0x47]
    // 0x1476cd4: r3 = Instance_Color
    //     0x1476cd4: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1476cd8: ldr             x3, [x3, #0xf88]
    // 0x1476cdc: StoreField: r0->field_5f = r3
    //     0x1476cdc: stur            w3, [x0, #0x5f]
    // 0x1476ce0: r4 = Instance__NoSplashFactory
    //     0x1476ce0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1476ce4: ldr             x4, [x4, #0xc48]
    // 0x1476ce8: StoreField: r0->field_6b = r4
    //     0x1476ce8: stur            w4, [x0, #0x6b]
    // 0x1476cec: StoreField: r0->field_6f = r1
    //     0x1476cec: stur            w1, [x0, #0x6f]
    // 0x1476cf0: r5 = false
    //     0x1476cf0: add             x5, NULL, #0x30  ; false
    // 0x1476cf4: StoreField: r0->field_73 = r5
    //     0x1476cf4: stur            w5, [x0, #0x73]
    // 0x1476cf8: StoreField: r0->field_83 = r1
    //     0x1476cf8: stur            w1, [x0, #0x83]
    // 0x1476cfc: StoreField: r0->field_7b = r5
    //     0x1476cfc: stur            w5, [x0, #0x7b]
    // 0x1476d00: r0 = Align()
    //     0x1476d00: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1476d04: r2 = Instance_Alignment
    //     0x1476d04: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x1476d08: ldr             x2, [x2, #0xfa0]
    // 0x1476d0c: StoreField: r0->field_f = r2
    //     0x1476d0c: stur            w2, [x0, #0xf]
    // 0x1476d10: ldur            x1, [fp, #-0x20]
    // 0x1476d14: StoreField: r0->field_b = r1
    //     0x1476d14: stur            w1, [x0, #0xb]
    // 0x1476d18: ldur            x1, [fp, #-0x10]
    // 0x1476d1c: ArrayStore: r1[8] = r0  ; List_4
    //     0x1476d1c: add             x25, x1, #0x2f
    //     0x1476d20: str             w0, [x25]
    //     0x1476d24: tbz             w0, #0, #0x1476d40
    //     0x1476d28: ldurb           w16, [x1, #-1]
    //     0x1476d2c: ldurb           w17, [x0, #-1]
    //     0x1476d30: and             x16, x17, x16, lsr #2
    //     0x1476d34: tst             x16, HEAP, lsr #32
    //     0x1476d38: b.eq            #0x1476d40
    //     0x1476d3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1476d40: ldur            x0, [fp, #-0x10]
    // 0x1476d44: r16 = Instance_SizedBox
    //     0x1476d44: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1476d48: ldr             x16, [x16, #0xc50]
    // 0x1476d4c: StoreField: r0->field_33 = r16
    //     0x1476d4c: stur            w16, [x0, #0x33]
    // 0x1476d50: ldur            x3, [fp, #-8]
    // 0x1476d54: LoadField: r1 = r3->field_13
    //     0x1476d54: ldur            w1, [x3, #0x13]
    // 0x1476d58: DecompressPointer r1
    //     0x1476d58: add             x1, x1, HEAP, lsl #32
    // 0x1476d5c: r0 = of()
    //     0x1476d5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1476d60: LoadField: r1 = r0->field_87
    //     0x1476d60: ldur            w1, [x0, #0x87]
    // 0x1476d64: DecompressPointer r1
    //     0x1476d64: add             x1, x1, HEAP, lsl #32
    // 0x1476d68: LoadField: r0 = r1->field_7
    //     0x1476d68: ldur            w0, [x1, #7]
    // 0x1476d6c: DecompressPointer r0
    //     0x1476d6c: add             x0, x0, HEAP, lsl #32
    // 0x1476d70: r16 = 14.000000
    //     0x1476d70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1476d74: ldr             x16, [x16, #0x1d8]
    // 0x1476d78: str             x16, [SP]
    // 0x1476d7c: mov             x1, x0
    // 0x1476d80: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1476d80: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1476d84: ldr             x4, [x4, #0x798]
    // 0x1476d88: r0 = copyWith()
    //     0x1476d88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1476d8c: stur            x0, [fp, #-0x18]
    // 0x1476d90: r0 = Text()
    //     0x1476d90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1476d94: mov             x3, x0
    // 0x1476d98: r0 = "Terms & Condition"
    //     0x1476d98: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c98] "Terms & Condition"
    //     0x1476d9c: ldr             x0, [x0, #0xc98]
    // 0x1476da0: stur            x3, [fp, #-0x20]
    // 0x1476da4: StoreField: r3->field_b = r0
    //     0x1476da4: stur            w0, [x3, #0xb]
    // 0x1476da8: ldur            x0, [fp, #-0x18]
    // 0x1476dac: StoreField: r3->field_13 = r0
    //     0x1476dac: stur            w0, [x3, #0x13]
    // 0x1476db0: r1 = Null
    //     0x1476db0: mov             x1, NULL
    // 0x1476db4: r2 = 2
    //     0x1476db4: movz            x2, #0x2
    // 0x1476db8: r0 = AllocateArray()
    //     0x1476db8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1476dbc: mov             x2, x0
    // 0x1476dc0: ldur            x0, [fp, #-0x20]
    // 0x1476dc4: stur            x2, [fp, #-0x18]
    // 0x1476dc8: StoreField: r2->field_f = r0
    //     0x1476dc8: stur            w0, [x2, #0xf]
    // 0x1476dcc: r1 = <Widget>
    //     0x1476dcc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1476dd0: r0 = AllocateGrowableArray()
    //     0x1476dd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1476dd4: mov             x1, x0
    // 0x1476dd8: ldur            x0, [fp, #-0x18]
    // 0x1476ddc: stur            x1, [fp, #-0x20]
    // 0x1476de0: StoreField: r1->field_f = r0
    //     0x1476de0: stur            w0, [x1, #0xf]
    // 0x1476de4: r2 = 2
    //     0x1476de4: movz            x2, #0x2
    // 0x1476de8: StoreField: r1->field_b = r2
    //     0x1476de8: stur            w2, [x1, #0xb]
    // 0x1476dec: r0 = Row()
    //     0x1476dec: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1476df0: mov             x1, x0
    // 0x1476df4: r0 = Instance_Axis
    //     0x1476df4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1476df8: stur            x1, [fp, #-0x18]
    // 0x1476dfc: StoreField: r1->field_f = r0
    //     0x1476dfc: stur            w0, [x1, #0xf]
    // 0x1476e00: r2 = Instance_MainAxisAlignment
    //     0x1476e00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1476e04: ldr             x2, [x2, #0xa08]
    // 0x1476e08: StoreField: r1->field_13 = r2
    //     0x1476e08: stur            w2, [x1, #0x13]
    // 0x1476e0c: r3 = Instance_MainAxisSize
    //     0x1476e0c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1476e10: ldr             x3, [x3, #0xa10]
    // 0x1476e14: ArrayStore: r1[0] = r3  ; List_4
    //     0x1476e14: stur            w3, [x1, #0x17]
    // 0x1476e18: r4 = Instance_CrossAxisAlignment
    //     0x1476e18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1476e1c: ldr             x4, [x4, #0xa18]
    // 0x1476e20: StoreField: r1->field_1b = r4
    //     0x1476e20: stur            w4, [x1, #0x1b]
    // 0x1476e24: r5 = Instance_VerticalDirection
    //     0x1476e24: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1476e28: ldr             x5, [x5, #0xa20]
    // 0x1476e2c: StoreField: r1->field_23 = r5
    //     0x1476e2c: stur            w5, [x1, #0x23]
    // 0x1476e30: r6 = Instance_Clip
    //     0x1476e30: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1476e34: ldr             x6, [x6, #0x38]
    // 0x1476e38: StoreField: r1->field_2b = r6
    //     0x1476e38: stur            w6, [x1, #0x2b]
    // 0x1476e3c: StoreField: r1->field_2f = rZR
    //     0x1476e3c: stur            xzr, [x1, #0x2f]
    // 0x1476e40: ldur            x7, [fp, #-0x20]
    // 0x1476e44: StoreField: r1->field_b = r7
    //     0x1476e44: stur            w7, [x1, #0xb]
    // 0x1476e48: r0 = InkWell()
    //     0x1476e48: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1476e4c: mov             x3, x0
    // 0x1476e50: ldur            x0, [fp, #-0x18]
    // 0x1476e54: stur            x3, [fp, #-0x20]
    // 0x1476e58: StoreField: r3->field_b = r0
    //     0x1476e58: stur            w0, [x3, #0xb]
    // 0x1476e5c: ldur            x2, [fp, #-8]
    // 0x1476e60: r1 = Function '<anonymous closure>':.
    //     0x1476e60: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ca0] AnonymousClosure: (0x14773d4), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1476e64: ldr             x1, [x1, #0xca0]
    // 0x1476e68: r0 = AllocateClosure()
    //     0x1476e68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1476e6c: mov             x1, x0
    // 0x1476e70: ldur            x0, [fp, #-0x20]
    // 0x1476e74: StoreField: r0->field_f = r1
    //     0x1476e74: stur            w1, [x0, #0xf]
    // 0x1476e78: r1 = true
    //     0x1476e78: add             x1, NULL, #0x20  ; true
    // 0x1476e7c: StoreField: r0->field_43 = r1
    //     0x1476e7c: stur            w1, [x0, #0x43]
    // 0x1476e80: r2 = Instance_BoxShape
    //     0x1476e80: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1476e84: ldr             x2, [x2, #0x80]
    // 0x1476e88: StoreField: r0->field_47 = r2
    //     0x1476e88: stur            w2, [x0, #0x47]
    // 0x1476e8c: r3 = Instance_Color
    //     0x1476e8c: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1476e90: ldr             x3, [x3, #0xf88]
    // 0x1476e94: StoreField: r0->field_5f = r3
    //     0x1476e94: stur            w3, [x0, #0x5f]
    // 0x1476e98: r4 = Instance__NoSplashFactory
    //     0x1476e98: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1476e9c: ldr             x4, [x4, #0xc48]
    // 0x1476ea0: StoreField: r0->field_6b = r4
    //     0x1476ea0: stur            w4, [x0, #0x6b]
    // 0x1476ea4: StoreField: r0->field_6f = r1
    //     0x1476ea4: stur            w1, [x0, #0x6f]
    // 0x1476ea8: r5 = false
    //     0x1476ea8: add             x5, NULL, #0x30  ; false
    // 0x1476eac: StoreField: r0->field_73 = r5
    //     0x1476eac: stur            w5, [x0, #0x73]
    // 0x1476eb0: StoreField: r0->field_83 = r1
    //     0x1476eb0: stur            w1, [x0, #0x83]
    // 0x1476eb4: StoreField: r0->field_7b = r5
    //     0x1476eb4: stur            w5, [x0, #0x7b]
    // 0x1476eb8: r0 = Align()
    //     0x1476eb8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1476ebc: r2 = Instance_Alignment
    //     0x1476ebc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x1476ec0: ldr             x2, [x2, #0xfa0]
    // 0x1476ec4: StoreField: r0->field_f = r2
    //     0x1476ec4: stur            w2, [x0, #0xf]
    // 0x1476ec8: ldur            x1, [fp, #-0x20]
    // 0x1476ecc: StoreField: r0->field_b = r1
    //     0x1476ecc: stur            w1, [x0, #0xb]
    // 0x1476ed0: ldur            x1, [fp, #-0x10]
    // 0x1476ed4: ArrayStore: r1[10] = r0  ; List_4
    //     0x1476ed4: add             x25, x1, #0x37
    //     0x1476ed8: str             w0, [x25]
    //     0x1476edc: tbz             w0, #0, #0x1476ef8
    //     0x1476ee0: ldurb           w16, [x1, #-1]
    //     0x1476ee4: ldurb           w17, [x0, #-1]
    //     0x1476ee8: and             x16, x17, x16, lsr #2
    //     0x1476eec: tst             x16, HEAP, lsr #32
    //     0x1476ef0: b.eq            #0x1476ef8
    //     0x1476ef4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1476ef8: ldur            x0, [fp, #-0x10]
    // 0x1476efc: r16 = Instance_SizedBox
    //     0x1476efc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x1476f00: ldr             x16, [x16, #0xc50]
    // 0x1476f04: StoreField: r0->field_3b = r16
    //     0x1476f04: stur            w16, [x0, #0x3b]
    // 0x1476f08: ldur            x3, [fp, #-8]
    // 0x1476f0c: LoadField: r1 = r3->field_13
    //     0x1476f0c: ldur            w1, [x3, #0x13]
    // 0x1476f10: DecompressPointer r1
    //     0x1476f10: add             x1, x1, HEAP, lsl #32
    // 0x1476f14: r0 = of()
    //     0x1476f14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1476f18: LoadField: r1 = r0->field_87
    //     0x1476f18: ldur            w1, [x0, #0x87]
    // 0x1476f1c: DecompressPointer r1
    //     0x1476f1c: add             x1, x1, HEAP, lsl #32
    // 0x1476f20: LoadField: r0 = r1->field_7
    //     0x1476f20: ldur            w0, [x1, #7]
    // 0x1476f24: DecompressPointer r0
    //     0x1476f24: add             x0, x0, HEAP, lsl #32
    // 0x1476f28: r16 = 14.000000
    //     0x1476f28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1476f2c: ldr             x16, [x16, #0x1d8]
    // 0x1476f30: str             x16, [SP]
    // 0x1476f34: mov             x1, x0
    // 0x1476f38: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1476f38: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x1476f3c: ldr             x4, [x4, #0x798]
    // 0x1476f40: r0 = copyWith()
    //     0x1476f40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1476f44: stur            x0, [fp, #-0x18]
    // 0x1476f48: r0 = Text()
    //     0x1476f48: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1476f4c: mov             x3, x0
    // 0x1476f50: r0 = "Contact Us"
    //     0x1476f50: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ca8] "Contact Us"
    //     0x1476f54: ldr             x0, [x0, #0xca8]
    // 0x1476f58: stur            x3, [fp, #-0x20]
    // 0x1476f5c: StoreField: r3->field_b = r0
    //     0x1476f5c: stur            w0, [x3, #0xb]
    // 0x1476f60: ldur            x0, [fp, #-0x18]
    // 0x1476f64: StoreField: r3->field_13 = r0
    //     0x1476f64: stur            w0, [x3, #0x13]
    // 0x1476f68: r1 = Null
    //     0x1476f68: mov             x1, NULL
    // 0x1476f6c: r2 = 2
    //     0x1476f6c: movz            x2, #0x2
    // 0x1476f70: r0 = AllocateArray()
    //     0x1476f70: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1476f74: mov             x2, x0
    // 0x1476f78: ldur            x0, [fp, #-0x20]
    // 0x1476f7c: stur            x2, [fp, #-0x18]
    // 0x1476f80: StoreField: r2->field_f = r0
    //     0x1476f80: stur            w0, [x2, #0xf]
    // 0x1476f84: r1 = <Widget>
    //     0x1476f84: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1476f88: r0 = AllocateGrowableArray()
    //     0x1476f88: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1476f8c: mov             x1, x0
    // 0x1476f90: ldur            x0, [fp, #-0x18]
    // 0x1476f94: stur            x1, [fp, #-0x20]
    // 0x1476f98: StoreField: r1->field_f = r0
    //     0x1476f98: stur            w0, [x1, #0xf]
    // 0x1476f9c: r0 = 2
    //     0x1476f9c: movz            x0, #0x2
    // 0x1476fa0: StoreField: r1->field_b = r0
    //     0x1476fa0: stur            w0, [x1, #0xb]
    // 0x1476fa4: r0 = Row()
    //     0x1476fa4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1476fa8: mov             x1, x0
    // 0x1476fac: r0 = Instance_Axis
    //     0x1476fac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1476fb0: stur            x1, [fp, #-0x18]
    // 0x1476fb4: StoreField: r1->field_f = r0
    //     0x1476fb4: stur            w0, [x1, #0xf]
    // 0x1476fb8: r2 = Instance_MainAxisAlignment
    //     0x1476fb8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1476fbc: ldr             x2, [x2, #0xa08]
    // 0x1476fc0: StoreField: r1->field_13 = r2
    //     0x1476fc0: stur            w2, [x1, #0x13]
    // 0x1476fc4: r3 = Instance_MainAxisSize
    //     0x1476fc4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1476fc8: ldr             x3, [x3, #0xa10]
    // 0x1476fcc: ArrayStore: r1[0] = r3  ; List_4
    //     0x1476fcc: stur            w3, [x1, #0x17]
    // 0x1476fd0: r4 = Instance_CrossAxisAlignment
    //     0x1476fd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1476fd4: ldr             x4, [x4, #0xa18]
    // 0x1476fd8: StoreField: r1->field_1b = r4
    //     0x1476fd8: stur            w4, [x1, #0x1b]
    // 0x1476fdc: r5 = Instance_VerticalDirection
    //     0x1476fdc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1476fe0: ldr             x5, [x5, #0xa20]
    // 0x1476fe4: StoreField: r1->field_23 = r5
    //     0x1476fe4: stur            w5, [x1, #0x23]
    // 0x1476fe8: r6 = Instance_Clip
    //     0x1476fe8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1476fec: ldr             x6, [x6, #0x38]
    // 0x1476ff0: StoreField: r1->field_2b = r6
    //     0x1476ff0: stur            w6, [x1, #0x2b]
    // 0x1476ff4: StoreField: r1->field_2f = rZR
    //     0x1476ff4: stur            xzr, [x1, #0x2f]
    // 0x1476ff8: ldur            x7, [fp, #-0x20]
    // 0x1476ffc: StoreField: r1->field_b = r7
    //     0x1476ffc: stur            w7, [x1, #0xb]
    // 0x1477000: r0 = InkWell()
    //     0x1477000: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1477004: mov             x3, x0
    // 0x1477008: ldur            x0, [fp, #-0x18]
    // 0x147700c: stur            x3, [fp, #-0x20]
    // 0x1477010: StoreField: r3->field_b = r0
    //     0x1477010: stur            w0, [x3, #0xb]
    // 0x1477014: r1 = Function '<anonymous closure>':.
    //     0x1477014: add             x1, PP, #0x36, lsl #12  ; [pp+0x36cb0] AnonymousClosure: (0x1477374), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1477018: ldr             x1, [x1, #0xcb0]
    // 0x147701c: r2 = Null
    //     0x147701c: mov             x2, NULL
    // 0x1477020: r0 = AllocateClosure()
    //     0x1477020: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1477024: mov             x1, x0
    // 0x1477028: ldur            x0, [fp, #-0x20]
    // 0x147702c: StoreField: r0->field_f = r1
    //     0x147702c: stur            w1, [x0, #0xf]
    // 0x1477030: r1 = true
    //     0x1477030: add             x1, NULL, #0x20  ; true
    // 0x1477034: StoreField: r0->field_43 = r1
    //     0x1477034: stur            w1, [x0, #0x43]
    // 0x1477038: r2 = Instance_BoxShape
    //     0x1477038: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x147703c: ldr             x2, [x2, #0x80]
    // 0x1477040: StoreField: r0->field_47 = r2
    //     0x1477040: stur            w2, [x0, #0x47]
    // 0x1477044: r3 = Instance_Color
    //     0x1477044: add             x3, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x1477048: ldr             x3, [x3, #0xf88]
    // 0x147704c: StoreField: r0->field_5f = r3
    //     0x147704c: stur            w3, [x0, #0x5f]
    // 0x1477050: r4 = Instance__NoSplashFactory
    //     0x1477050: add             x4, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1477054: ldr             x4, [x4, #0xc48]
    // 0x1477058: StoreField: r0->field_6b = r4
    //     0x1477058: stur            w4, [x0, #0x6b]
    // 0x147705c: StoreField: r0->field_6f = r1
    //     0x147705c: stur            w1, [x0, #0x6f]
    // 0x1477060: r5 = false
    //     0x1477060: add             x5, NULL, #0x30  ; false
    // 0x1477064: StoreField: r0->field_73 = r5
    //     0x1477064: stur            w5, [x0, #0x73]
    // 0x1477068: StoreField: r0->field_83 = r1
    //     0x1477068: stur            w1, [x0, #0x83]
    // 0x147706c: StoreField: r0->field_7b = r5
    //     0x147706c: stur            w5, [x0, #0x7b]
    // 0x1477070: r0 = Align()
    //     0x1477070: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x1477074: mov             x1, x0
    // 0x1477078: r0 = Instance_Alignment
    //     0x1477078: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0x147707c: ldr             x0, [x0, #0xfa0]
    // 0x1477080: StoreField: r1->field_f = r0
    //     0x1477080: stur            w0, [x1, #0xf]
    // 0x1477084: ldur            x0, [fp, #-0x20]
    // 0x1477088: StoreField: r1->field_b = r0
    //     0x1477088: stur            w0, [x1, #0xb]
    // 0x147708c: mov             x0, x1
    // 0x1477090: ldur            x1, [fp, #-0x10]
    // 0x1477094: ArrayStore: r1[12] = r0  ; List_4
    //     0x1477094: add             x25, x1, #0x3f
    //     0x1477098: str             w0, [x25]
    //     0x147709c: tbz             w0, #0, #0x14770b8
    //     0x14770a0: ldurb           w16, [x1, #-1]
    //     0x14770a4: ldurb           w17, [x0, #-1]
    //     0x14770a8: and             x16, x17, x16, lsr #2
    //     0x14770ac: tst             x16, HEAP, lsr #32
    //     0x14770b0: b.eq            #0x14770b8
    //     0x14770b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14770b8: ldur            x0, [fp, #-0x10]
    // 0x14770bc: r16 = Instance_SizedBox
    //     0x14770bc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36c50] Obj!SizedBox@d68201
    //     0x14770c0: ldr             x16, [x16, #0xc50]
    // 0x14770c4: StoreField: r0->field_43 = r16
    //     0x14770c4: stur            w16, [x0, #0x43]
    // 0x14770c8: ldur            x2, [fp, #-8]
    // 0x14770cc: LoadField: r1 = r2->field_f
    //     0x14770cc: ldur            w1, [x2, #0xf]
    // 0x14770d0: DecompressPointer r1
    //     0x14770d0: add             x1, x1, HEAP, lsl #32
    // 0x14770d4: r0 = controller()
    //     0x14770d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14770d8: LoadField: r1 = r0->field_5b
    //     0x14770d8: ldur            w1, [x0, #0x5b]
    // 0x14770dc: DecompressPointer r1
    //     0x14770dc: add             x1, x1, HEAP, lsl #32
    // 0x14770e0: r0 = value()
    //     0x14770e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14770e4: ldur            x2, [fp, #-8]
    // 0x14770e8: stur            x0, [fp, #-0x18]
    // 0x14770ec: LoadField: r1 = r2->field_13
    //     0x14770ec: ldur            w1, [x2, #0x13]
    // 0x14770f0: DecompressPointer r1
    //     0x14770f0: add             x1, x1, HEAP, lsl #32
    // 0x14770f4: r0 = of()
    //     0x14770f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14770f8: LoadField: r1 = r0->field_87
    //     0x14770f8: ldur            w1, [x0, #0x87]
    // 0x14770fc: DecompressPointer r1
    //     0x14770fc: add             x1, x1, HEAP, lsl #32
    // 0x1477100: LoadField: r0 = r1->field_7
    //     0x1477100: ldur            w0, [x1, #7]
    // 0x1477104: DecompressPointer r0
    //     0x1477104: add             x0, x0, HEAP, lsl #32
    // 0x1477108: r16 = 14.000000
    //     0x1477108: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x147710c: ldr             x16, [x16, #0x1d8]
    // 0x1477110: str             x16, [SP]
    // 0x1477114: mov             x1, x0
    // 0x1477118: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0x1477118: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0x147711c: ldr             x4, [x4, #0x798]
    // 0x1477120: r0 = copyWith()
    //     0x1477120: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1477124: stur            x0, [fp, #-0x20]
    // 0x1477128: r0 = Text()
    //     0x1477128: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x147712c: mov             x3, x0
    // 0x1477130: r0 = "Logout"
    //     0x1477130: add             x0, PP, #0x36, lsl #12  ; [pp+0x36cb8] "Logout"
    //     0x1477134: ldr             x0, [x0, #0xcb8]
    // 0x1477138: stur            x3, [fp, #-0x28]
    // 0x147713c: StoreField: r3->field_b = r0
    //     0x147713c: stur            w0, [x3, #0xb]
    // 0x1477140: ldur            x0, [fp, #-0x20]
    // 0x1477144: StoreField: r3->field_13 = r0
    //     0x1477144: stur            w0, [x3, #0x13]
    // 0x1477148: r1 = Null
    //     0x1477148: mov             x1, NULL
    // 0x147714c: r2 = 6
    //     0x147714c: movz            x2, #0x6
    // 0x1477150: r0 = AllocateArray()
    //     0x1477150: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1477154: stur            x0, [fp, #-0x20]
    // 0x1477158: r16 = Instance_Icon
    //     0x1477158: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cc0] Obj!Icon@d66a31
    //     0x147715c: ldr             x16, [x16, #0xcc0]
    // 0x1477160: StoreField: r0->field_f = r16
    //     0x1477160: stur            w16, [x0, #0xf]
    // 0x1477164: r16 = Instance_SizedBox
    //     0x1477164: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x1477168: ldr             x16, [x16, #0xaa8]
    // 0x147716c: StoreField: r0->field_13 = r16
    //     0x147716c: stur            w16, [x0, #0x13]
    // 0x1477170: ldur            x1, [fp, #-0x28]
    // 0x1477174: ArrayStore: r0[0] = r1  ; List_4
    //     0x1477174: stur            w1, [x0, #0x17]
    // 0x1477178: r1 = <Widget>
    //     0x1477178: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x147717c: r0 = AllocateGrowableArray()
    //     0x147717c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1477180: mov             x1, x0
    // 0x1477184: ldur            x0, [fp, #-0x20]
    // 0x1477188: stur            x1, [fp, #-0x28]
    // 0x147718c: StoreField: r1->field_f = r0
    //     0x147718c: stur            w0, [x1, #0xf]
    // 0x1477190: r0 = 6
    //     0x1477190: movz            x0, #0x6
    // 0x1477194: StoreField: r1->field_b = r0
    //     0x1477194: stur            w0, [x1, #0xb]
    // 0x1477198: r0 = Row()
    //     0x1477198: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x147719c: mov             x1, x0
    // 0x14771a0: r0 = Instance_Axis
    //     0x14771a0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14771a4: stur            x1, [fp, #-0x20]
    // 0x14771a8: StoreField: r1->field_f = r0
    //     0x14771a8: stur            w0, [x1, #0xf]
    // 0x14771ac: r0 = Instance_MainAxisAlignment
    //     0x14771ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14771b0: ldr             x0, [x0, #0xa08]
    // 0x14771b4: StoreField: r1->field_13 = r0
    //     0x14771b4: stur            w0, [x1, #0x13]
    // 0x14771b8: r2 = Instance_MainAxisSize
    //     0x14771b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14771bc: ldr             x2, [x2, #0xa10]
    // 0x14771c0: ArrayStore: r1[0] = r2  ; List_4
    //     0x14771c0: stur            w2, [x1, #0x17]
    // 0x14771c4: r3 = Instance_CrossAxisAlignment
    //     0x14771c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14771c8: ldr             x3, [x3, #0xa18]
    // 0x14771cc: StoreField: r1->field_1b = r3
    //     0x14771cc: stur            w3, [x1, #0x1b]
    // 0x14771d0: r4 = Instance_VerticalDirection
    //     0x14771d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14771d4: ldr             x4, [x4, #0xa20]
    // 0x14771d8: StoreField: r1->field_23 = r4
    //     0x14771d8: stur            w4, [x1, #0x23]
    // 0x14771dc: r5 = Instance_Clip
    //     0x14771dc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14771e0: ldr             x5, [x5, #0x38]
    // 0x14771e4: StoreField: r1->field_2b = r5
    //     0x14771e4: stur            w5, [x1, #0x2b]
    // 0x14771e8: StoreField: r1->field_2f = rZR
    //     0x14771e8: stur            xzr, [x1, #0x2f]
    // 0x14771ec: ldur            x6, [fp, #-0x28]
    // 0x14771f0: StoreField: r1->field_b = r6
    //     0x14771f0: stur            w6, [x1, #0xb]
    // 0x14771f4: r0 = InkWell()
    //     0x14771f4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14771f8: mov             x3, x0
    // 0x14771fc: ldur            x0, [fp, #-0x20]
    // 0x1477200: stur            x3, [fp, #-0x28]
    // 0x1477204: StoreField: r3->field_b = r0
    //     0x1477204: stur            w0, [x3, #0xb]
    // 0x1477208: ldur            x2, [fp, #-8]
    // 0x147720c: r1 = Function '<anonymous closure>':.
    //     0x147720c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36cc8] AnonymousClosure: (0x14762a4), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x1477210: ldr             x1, [x1, #0xcc8]
    // 0x1477214: r0 = AllocateClosure()
    //     0x1477214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1477218: mov             x1, x0
    // 0x147721c: ldur            x0, [fp, #-0x28]
    // 0x1477220: StoreField: r0->field_f = r1
    //     0x1477220: stur            w1, [x0, #0xf]
    // 0x1477224: r1 = true
    //     0x1477224: add             x1, NULL, #0x20  ; true
    // 0x1477228: StoreField: r0->field_43 = r1
    //     0x1477228: stur            w1, [x0, #0x43]
    // 0x147722c: r2 = Instance_BoxShape
    //     0x147722c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1477230: ldr             x2, [x2, #0x80]
    // 0x1477234: StoreField: r0->field_47 = r2
    //     0x1477234: stur            w2, [x0, #0x47]
    // 0x1477238: r2 = Instance_Color
    //     0x1477238: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x147723c: ldr             x2, [x2, #0xf88]
    // 0x1477240: StoreField: r0->field_5f = r2
    //     0x1477240: stur            w2, [x0, #0x5f]
    // 0x1477244: r2 = Instance__NoSplashFactory
    //     0x1477244: add             x2, PP, #0x36, lsl #12  ; [pp+0x36c48] Obj!_NoSplashFactory@d685e1
    //     0x1477248: ldr             x2, [x2, #0xc48]
    // 0x147724c: StoreField: r0->field_6b = r2
    //     0x147724c: stur            w2, [x0, #0x6b]
    // 0x1477250: StoreField: r0->field_6f = r1
    //     0x1477250: stur            w1, [x0, #0x6f]
    // 0x1477254: r2 = false
    //     0x1477254: add             x2, NULL, #0x30  ; false
    // 0x1477258: StoreField: r0->field_73 = r2
    //     0x1477258: stur            w2, [x0, #0x73]
    // 0x147725c: StoreField: r0->field_83 = r1
    //     0x147725c: stur            w1, [x0, #0x83]
    // 0x1477260: StoreField: r0->field_7b = r2
    //     0x1477260: stur            w2, [x0, #0x7b]
    // 0x1477264: r0 = Visibility()
    //     0x1477264: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1477268: mov             x1, x0
    // 0x147726c: ldur            x0, [fp, #-0x28]
    // 0x1477270: StoreField: r1->field_b = r0
    //     0x1477270: stur            w0, [x1, #0xb]
    // 0x1477274: r0 = Instance_SizedBox
    //     0x1477274: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1477278: StoreField: r1->field_f = r0
    //     0x1477278: stur            w0, [x1, #0xf]
    // 0x147727c: ldur            x0, [fp, #-0x18]
    // 0x1477280: StoreField: r1->field_13 = r0
    //     0x1477280: stur            w0, [x1, #0x13]
    // 0x1477284: r0 = false
    //     0x1477284: add             x0, NULL, #0x30  ; false
    // 0x1477288: ArrayStore: r1[0] = r0  ; List_4
    //     0x1477288: stur            w0, [x1, #0x17]
    // 0x147728c: StoreField: r1->field_1b = r0
    //     0x147728c: stur            w0, [x1, #0x1b]
    // 0x1477290: StoreField: r1->field_1f = r0
    //     0x1477290: stur            w0, [x1, #0x1f]
    // 0x1477294: StoreField: r1->field_23 = r0
    //     0x1477294: stur            w0, [x1, #0x23]
    // 0x1477298: StoreField: r1->field_27 = r0
    //     0x1477298: stur            w0, [x1, #0x27]
    // 0x147729c: StoreField: r1->field_2b = r0
    //     0x147729c: stur            w0, [x1, #0x2b]
    // 0x14772a0: mov             x0, x1
    // 0x14772a4: ldur            x1, [fp, #-0x10]
    // 0x14772a8: ArrayStore: r1[14] = r0  ; List_4
    //     0x14772a8: add             x25, x1, #0x47
    //     0x14772ac: str             w0, [x25]
    //     0x14772b0: tbz             w0, #0, #0x14772cc
    //     0x14772b4: ldurb           w16, [x1, #-1]
    //     0x14772b8: ldurb           w17, [x0, #-1]
    //     0x14772bc: and             x16, x17, x16, lsr #2
    //     0x14772c0: tst             x16, HEAP, lsr #32
    //     0x14772c4: b.eq            #0x14772cc
    //     0x14772c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14772cc: r1 = <Widget>
    //     0x14772cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14772d0: r0 = AllocateGrowableArray()
    //     0x14772d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14772d4: mov             x1, x0
    // 0x14772d8: ldur            x0, [fp, #-0x10]
    // 0x14772dc: stur            x1, [fp, #-8]
    // 0x14772e0: StoreField: r1->field_f = r0
    //     0x14772e0: stur            w0, [x1, #0xf]
    // 0x14772e4: r0 = 30
    //     0x14772e4: movz            x0, #0x1e
    // 0x14772e8: StoreField: r1->field_b = r0
    //     0x14772e8: stur            w0, [x1, #0xb]
    // 0x14772ec: r0 = Column()
    //     0x14772ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14772f0: mov             x1, x0
    // 0x14772f4: r0 = Instance_Axis
    //     0x14772f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14772f8: stur            x1, [fp, #-0x10]
    // 0x14772fc: StoreField: r1->field_f = r0
    //     0x14772fc: stur            w0, [x1, #0xf]
    // 0x1477300: r0 = Instance_MainAxisAlignment
    //     0x1477300: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1477304: ldr             x0, [x0, #0xa08]
    // 0x1477308: StoreField: r1->field_13 = r0
    //     0x1477308: stur            w0, [x1, #0x13]
    // 0x147730c: r0 = Instance_MainAxisSize
    //     0x147730c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1477310: ldr             x0, [x0, #0xa10]
    // 0x1477314: ArrayStore: r1[0] = r0  ; List_4
    //     0x1477314: stur            w0, [x1, #0x17]
    // 0x1477318: r0 = Instance_CrossAxisAlignment
    //     0x1477318: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x147731c: ldr             x0, [x0, #0xa18]
    // 0x1477320: StoreField: r1->field_1b = r0
    //     0x1477320: stur            w0, [x1, #0x1b]
    // 0x1477324: r0 = Instance_VerticalDirection
    //     0x1477324: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1477328: ldr             x0, [x0, #0xa20]
    // 0x147732c: StoreField: r1->field_23 = r0
    //     0x147732c: stur            w0, [x1, #0x23]
    // 0x1477330: r0 = Instance_Clip
    //     0x1477330: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1477334: ldr             x0, [x0, #0x38]
    // 0x1477338: StoreField: r1->field_2b = r0
    //     0x1477338: stur            w0, [x1, #0x2b]
    // 0x147733c: StoreField: r1->field_2f = rZR
    //     0x147733c: stur            xzr, [x1, #0x2f]
    // 0x1477340: ldur            x0, [fp, #-8]
    // 0x1477344: StoreField: r1->field_b = r0
    //     0x1477344: stur            w0, [x1, #0xb]
    // 0x1477348: r0 = Padding()
    //     0x1477348: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x147734c: r1 = Instance_EdgeInsets
    //     0x147734c: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f98] Obj!EdgeInsets@d573b1
    //     0x1477350: ldr             x1, [x1, #0xf98]
    // 0x1477354: StoreField: r0->field_f = r1
    //     0x1477354: stur            w1, [x0, #0xf]
    // 0x1477358: ldur            x1, [fp, #-0x10]
    // 0x147735c: StoreField: r0->field_b = r1
    //     0x147735c: stur            w1, [x0, #0xb]
    // 0x1477360: LeaveFrame
    //     0x1477360: mov             SP, fp
    //     0x1477364: ldp             fp, lr, [SP], #0x10
    // 0x1477368: ret
    //     0x1477368: ret             
    // 0x147736c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147736c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1477370: b               #0x14763ec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1477374, size: 0x60
    // 0x1477374: EnterFrame
    //     0x1477374: stp             fp, lr, [SP, #-0x10]!
    //     0x1477378: mov             fp, SP
    // 0x147737c: AllocStack(0x10)
    //     0x147737c: sub             SP, SP, #0x10
    // 0x1477380: CheckStackOverflow
    //     0x1477380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1477384: cmp             SP, x16
    //     0x1477388: b.ls            #0x14773cc
    // 0x147738c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x147738c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1477390: ldr             x0, [x0, #0x1c80]
    //     0x1477394: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1477398: cmp             w0, w16
    //     0x147739c: b.ne            #0x14773a8
    //     0x14773a0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14773a4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14773a8: r16 = "/contact-us"
    //     0x14773a8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd958] "/contact-us"
    //     0x14773ac: ldr             x16, [x16, #0x958]
    // 0x14773b0: stp             x16, NULL, [SP]
    // 0x14773b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x14773b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x14773b8: r0 = GetNavigation.toNamed()
    //     0x14773b8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x14773bc: r0 = Null
    //     0x14773bc: mov             x0, NULL
    // 0x14773c0: LeaveFrame
    //     0x14773c0: mov             SP, fp
    //     0x14773c4: ldp             fp, lr, [SP], #0x10
    // 0x14773c8: ret
    //     0x14773c8: ret             
    // 0x14773cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14773cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14773d0: b               #0x147738c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14773d4, size: 0x50
    // 0x14773d4: EnterFrame
    //     0x14773d4: stp             fp, lr, [SP, #-0x10]!
    //     0x14773d8: mov             fp, SP
    // 0x14773dc: ldr             x0, [fp, #0x10]
    // 0x14773e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14773e0: ldur            w1, [x0, #0x17]
    // 0x14773e4: DecompressPointer r1
    //     0x14773e4: add             x1, x1, HEAP, lsl #32
    // 0x14773e8: CheckStackOverflow
    //     0x14773e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14773ec: cmp             SP, x16
    //     0x14773f0: b.ls            #0x147741c
    // 0x14773f4: LoadField: r0 = r1->field_f
    //     0x14773f4: ldur            w0, [x1, #0xf]
    // 0x14773f8: DecompressPointer r0
    //     0x14773f8: add             x0, x0, HEAP, lsl #32
    // 0x14773fc: mov             x1, x0
    // 0x1477400: r2 = "terms_and_conditions"
    //     0x1477400: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cd0] "terms_and_conditions"
    //     0x1477404: ldr             x2, [x2, #0xcd0]
    // 0x1477408: r0 = openPolicyPage()
    //     0x1477408: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x147740c: r0 = Null
    //     0x147740c: mov             x0, NULL
    // 0x1477410: LeaveFrame
    //     0x1477410: mov             SP, fp
    //     0x1477414: ldp             fp, lr, [SP], #0x10
    // 0x1477418: ret
    //     0x1477418: ret             
    // 0x147741c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147741c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1477420: b               #0x14773f4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14774c8, size: 0x50
    // 0x14774c8: EnterFrame
    //     0x14774c8: stp             fp, lr, [SP, #-0x10]!
    //     0x14774cc: mov             fp, SP
    // 0x14774d0: ldr             x0, [fp, #0x10]
    // 0x14774d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14774d4: ldur            w1, [x0, #0x17]
    // 0x14774d8: DecompressPointer r1
    //     0x14774d8: add             x1, x1, HEAP, lsl #32
    // 0x14774dc: CheckStackOverflow
    //     0x14774dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14774e0: cmp             SP, x16
    //     0x14774e4: b.ls            #0x1477510
    // 0x14774e8: LoadField: r0 = r1->field_f
    //     0x14774e8: ldur            w0, [x1, #0xf]
    // 0x14774ec: DecompressPointer r0
    //     0x14774ec: add             x0, x0, HEAP, lsl #32
    // 0x14774f0: mov             x1, x0
    // 0x14774f4: r2 = "shipping_policy"
    //     0x14774f4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cd8] "shipping_policy"
    //     0x14774f8: ldr             x2, [x2, #0xcd8]
    // 0x14774fc: r0 = openPolicyPage()
    //     0x14774fc: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x1477500: r0 = Null
    //     0x1477500: mov             x0, NULL
    // 0x1477504: LeaveFrame
    //     0x1477504: mov             SP, fp
    //     0x1477508: ldp             fp, lr, [SP], #0x10
    // 0x147750c: ret
    //     0x147750c: ret             
    // 0x1477510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1477510: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1477514: b               #0x14774e8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1477518, size: 0x50
    // 0x1477518: EnterFrame
    //     0x1477518: stp             fp, lr, [SP, #-0x10]!
    //     0x147751c: mov             fp, SP
    // 0x1477520: ldr             x0, [fp, #0x10]
    // 0x1477524: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1477524: ldur            w1, [x0, #0x17]
    // 0x1477528: DecompressPointer r1
    //     0x1477528: add             x1, x1, HEAP, lsl #32
    // 0x147752c: CheckStackOverflow
    //     0x147752c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1477530: cmp             SP, x16
    //     0x1477534: b.ls            #0x1477560
    // 0x1477538: LoadField: r0 = r1->field_f
    //     0x1477538: ldur            w0, [x1, #0xf]
    // 0x147753c: DecompressPointer r0
    //     0x147753c: add             x0, x0, HEAP, lsl #32
    // 0x1477540: mov             x1, x0
    // 0x1477544: r2 = "return_policy"
    //     0x1477544: add             x2, PP, #0x36, lsl #12  ; [pp+0x36ce0] "return_policy"
    //     0x1477548: ldr             x2, [x2, #0xce0]
    // 0x147754c: r0 = openPolicyPage()
    //     0x147754c: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x1477550: r0 = Null
    //     0x1477550: mov             x0, NULL
    // 0x1477554: LeaveFrame
    //     0x1477554: mov             SP, fp
    //     0x1477558: ldp             fp, lr, [SP], #0x10
    // 0x147755c: ret
    //     0x147755c: ret             
    // 0x1477560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1477560: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1477564: b               #0x1477538
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1477568, size: 0x50
    // 0x1477568: EnterFrame
    //     0x1477568: stp             fp, lr, [SP, #-0x10]!
    //     0x147756c: mov             fp, SP
    // 0x1477570: ldr             x0, [fp, #0x10]
    // 0x1477574: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1477574: ldur            w1, [x0, #0x17]
    // 0x1477578: DecompressPointer r1
    //     0x1477578: add             x1, x1, HEAP, lsl #32
    // 0x147757c: CheckStackOverflow
    //     0x147757c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1477580: cmp             SP, x16
    //     0x1477584: b.ls            #0x14775b0
    // 0x1477588: LoadField: r0 = r1->field_f
    //     0x1477588: ldur            w0, [x1, #0xf]
    // 0x147758c: DecompressPointer r0
    //     0x147758c: add             x0, x0, HEAP, lsl #32
    // 0x1477590: mov             x1, x0
    // 0x1477594: r2 = "privacy_policy"
    //     0x1477594: add             x2, PP, #0x36, lsl #12  ; [pp+0x36ce8] "privacy_policy"
    //     0x1477598: ldr             x2, [x2, #0xce8]
    // 0x147759c: r0 = openPolicyPage()
    //     0x147759c: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14775a0: r0 = Null
    //     0x14775a0: mov             x0, NULL
    // 0x14775a4: LeaveFrame
    //     0x14775a4: mov             SP, fp
    //     0x14775a8: ldp             fp, lr, [SP], #0x10
    // 0x14775ac: ret
    //     0x14775ac: ret             
    // 0x14775b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14775b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14775b4: b               #0x1477588
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14775b8, size: 0x50
    // 0x14775b8: EnterFrame
    //     0x14775b8: stp             fp, lr, [SP, #-0x10]!
    //     0x14775bc: mov             fp, SP
    // 0x14775c0: ldr             x0, [fp, #0x10]
    // 0x14775c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14775c4: ldur            w1, [x0, #0x17]
    // 0x14775c8: DecompressPointer r1
    //     0x14775c8: add             x1, x1, HEAP, lsl #32
    // 0x14775cc: CheckStackOverflow
    //     0x14775cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14775d0: cmp             SP, x16
    //     0x14775d4: b.ls            #0x1477600
    // 0x14775d8: LoadField: r0 = r1->field_f
    //     0x14775d8: ldur            w0, [x1, #0xf]
    // 0x14775dc: DecompressPointer r0
    //     0x14775dc: add             x0, x0, HEAP, lsl #32
    // 0x14775e0: mov             x1, x0
    // 0x14775e4: r2 = "about_us"
    //     0x14775e4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36cf0] "about_us"
    //     0x14775e8: ldr             x2, [x2, #0xcf0]
    // 0x14775ec: r0 = openPolicyPage()
    //     0x14775ec: bl              #0x1477424  ; [package:customer_app/app/presentation/views/basic/profile/profile_view.dart] ProfileView::openPolicyPage
    // 0x14775f0: r0 = Null
    //     0x14775f0: mov             x0, NULL
    // 0x14775f4: LeaveFrame
    //     0x14775f4: mov             SP, fp
    //     0x14775f8: ldp             fp, lr, [SP], #0x10
    // 0x14775fc: ret
    //     0x14775fc: ret             
    // 0x1477600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1477600: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1477604: b               #0x14775d8
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x1477608, size: 0x12c
    // 0x1477608: EnterFrame
    //     0x1477608: stp             fp, lr, [SP, #-0x10]!
    //     0x147760c: mov             fp, SP
    // 0x1477610: AllocStack(0x30)
    //     0x1477610: sub             SP, SP, #0x30
    // 0x1477614: SetupParameters(ProfileView this /* r1 */)
    //     0x1477614: stur            NULL, [fp, #-8]
    //     0x1477618: movz            x0, #0
    //     0x147761c: add             x1, fp, w0, sxtw #2
    //     0x1477620: ldr             x1, [x1, #0x10]
    //     0x1477624: ldur            w2, [x1, #0x17]
    //     0x1477628: add             x2, x2, HEAP, lsl #32
    //     0x147762c: stur            x2, [fp, #-0x10]
    // 0x1477630: CheckStackOverflow
    //     0x1477630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1477634: cmp             SP, x16
    //     0x1477638: b.ls            #0x147772c
    // 0x147763c: InitAsync() -> Future<void?>
    //     0x147763c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x1477640: bl              #0x6326e0  ; InitAsyncStub
    // 0x1477644: ldur            x0, [fp, #-0x10]
    // 0x1477648: LoadField: r1 = r0->field_f
    //     0x1477648: ldur            w1, [x0, #0xf]
    // 0x147764c: DecompressPointer r1
    //     0x147764c: add             x1, x1, HEAP, lsl #32
    // 0x1477650: r0 = controller()
    //     0x1477650: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1477654: LoadField: r1 = r0->field_5b
    //     0x1477654: ldur            w1, [x0, #0x5b]
    // 0x1477658: DecompressPointer r1
    //     0x1477658: add             x1, x1, HEAP, lsl #32
    // 0x147765c: r0 = value()
    //     0x147765c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1477660: tbz             w0, #4, #0x1477724
    // 0x1477664: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1477664: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1477668: ldr             x0, [x0, #0x1c80]
    //     0x147766c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1477670: cmp             w0, w16
    //     0x1477674: b.ne            #0x1477680
    //     0x1477678: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x147767c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1477680: r1 = Null
    //     0x1477680: mov             x1, NULL
    // 0x1477684: r2 = 4
    //     0x1477684: movz            x2, #0x4
    // 0x1477688: r0 = AllocateArray()
    //     0x1477688: bl              #0x16f7198  ; AllocateArrayStub
    // 0x147768c: r16 = "previousScreenSource"
    //     0x147768c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x1477690: ldr             x16, [x16, #0x448]
    // 0x1477694: StoreField: r0->field_f = r16
    //     0x1477694: stur            w16, [x0, #0xf]
    // 0x1477698: r16 = "profile_page"
    //     0x1477698: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf8] "profile_page"
    //     0x147769c: ldr             x16, [x16, #0xcf8]
    // 0x14776a0: StoreField: r0->field_13 = r16
    //     0x14776a0: stur            w16, [x0, #0x13]
    // 0x14776a4: r16 = <String, String>
    //     0x14776a4: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x14776a8: ldr             x16, [x16, #0x788]
    // 0x14776ac: stp             x0, x16, [SP]
    // 0x14776b0: r0 = Map._fromLiteral()
    //     0x14776b0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x14776b4: r16 = "/login"
    //     0x14776b4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd880] "/login"
    //     0x14776b8: ldr             x16, [x16, #0x880]
    // 0x14776bc: stp             x16, NULL, [SP, #8]
    // 0x14776c0: str             x0, [SP]
    // 0x14776c4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x14776c4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x14776c8: ldr             x4, [x4, #0x438]
    // 0x14776cc: r0 = GetNavigation.toNamed()
    //     0x14776cc: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x14776d0: mov             x1, x0
    // 0x14776d4: stur            x1, [fp, #-0x18]
    // 0x14776d8: r0 = Await()
    //     0x14776d8: bl              #0x63248c  ; AwaitStub
    // 0x14776dc: r1 = 60
    //     0x14776dc: movz            x1, #0x3c
    // 0x14776e0: branchIfSmi(r0, 0x14776ec)
    //     0x14776e0: tbz             w0, #0, #0x14776ec
    // 0x14776e4: r1 = LoadClassIdInstr(r0)
    //     0x14776e4: ldur            x1, [x0, #-1]
    //     0x14776e8: ubfx            x1, x1, #0xc, #0x14
    // 0x14776ec: r16 = "success"
    //     0x14776ec: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d918] "success"
    //     0x14776f0: ldr             x16, [x16, #0x918]
    // 0x14776f4: stp             x16, x0, [SP]
    // 0x14776f8: mov             x0, x1
    // 0x14776fc: mov             lr, x0
    // 0x1477700: ldr             lr, [x21, lr, lsl #3]
    // 0x1477704: blr             lr
    // 0x1477708: tbnz            w0, #4, #0x1477724
    // 0x147770c: ldur            x0, [fp, #-0x10]
    // 0x1477710: LoadField: r1 = r0->field_f
    //     0x1477710: ldur            w1, [x0, #0xf]
    // 0x1477714: DecompressPointer r1
    //     0x1477714: add             x1, x1, HEAP, lsl #32
    // 0x1477718: r0 = controller()
    //     0x1477718: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147771c: mov             x1, x0
    // 0x1477720: r0 = checkLoginStatus()
    //     0x1477720: bl              #0x913d8c  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::checkLoginStatus
    // 0x1477724: r0 = Null
    //     0x1477724: mov             x0, NULL
    // 0x1477728: r0 = ReturnAsyncNotFuture()
    //     0x1477728: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x147772c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147772c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1477730: b               #0x147763c
  }
  _ body(/* No info */) {
    // ** addr: 0x1509484, size: 0x64
    // 0x1509484: EnterFrame
    //     0x1509484: stp             fp, lr, [SP, #-0x10]!
    //     0x1509488: mov             fp, SP
    // 0x150948c: AllocStack(0x18)
    //     0x150948c: sub             SP, SP, #0x18
    // 0x1509490: SetupParameters(ProfileView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1509490: stur            x1, [fp, #-8]
    //     0x1509494: stur            x2, [fp, #-0x10]
    // 0x1509498: r1 = 2
    //     0x1509498: movz            x1, #0x2
    // 0x150949c: r0 = AllocateContext()
    //     0x150949c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15094a0: mov             x1, x0
    // 0x15094a4: ldur            x0, [fp, #-8]
    // 0x15094a8: stur            x1, [fp, #-0x18]
    // 0x15094ac: StoreField: r1->field_f = r0
    //     0x15094ac: stur            w0, [x1, #0xf]
    // 0x15094b0: ldur            x0, [fp, #-0x10]
    // 0x15094b4: StoreField: r1->field_13 = r0
    //     0x15094b4: stur            w0, [x1, #0x13]
    // 0x15094b8: r0 = Obx()
    //     0x15094b8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15094bc: ldur            x2, [fp, #-0x18]
    // 0x15094c0: r1 = Function '<anonymous closure>':.
    //     0x15094c0: add             x1, PP, #0x36, lsl #12  ; [pp+0x36c30] AnonymousClosure: (0x14763c4), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::body (0x1509484)
    //     0x15094c4: ldr             x1, [x1, #0xc30]
    // 0x15094c8: stur            x0, [fp, #-8]
    // 0x15094cc: r0 = AllocateClosure()
    //     0x15094cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15094d0: mov             x1, x0
    // 0x15094d4: ldur            x0, [fp, #-8]
    // 0x15094d8: StoreField: r0->field_b = r1
    //     0x15094d8: stur            w1, [x0, #0xb]
    // 0x15094dc: LeaveFrame
    //     0x15094dc: mov             SP, fp
    //     0x15094e0: ldp             fp, lr, [SP], #0x10
    // 0x15094e4: ret
    //     0x15094e4: ret             
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x15d6d84, size: 0x50
    // 0x15d6d84: EnterFrame
    //     0x15d6d84: stp             fp, lr, [SP, #-0x10]!
    //     0x15d6d88: mov             fp, SP
    // 0x15d6d8c: ldr             x0, [fp, #0x18]
    // 0x15d6d90: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15d6d90: ldur            w1, [x0, #0x17]
    // 0x15d6d94: DecompressPointer r1
    //     0x15d6d94: add             x1, x1, HEAP, lsl #32
    // 0x15d6d98: CheckStackOverflow
    //     0x15d6d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d6d9c: cmp             SP, x16
    //     0x15d6da0: b.ls            #0x15d6dcc
    // 0x15d6da4: LoadField: r0 = r1->field_f
    //     0x15d6da4: ldur            w0, [x1, #0xf]
    // 0x15d6da8: DecompressPointer r0
    //     0x15d6da8: add             x0, x0, HEAP, lsl #32
    // 0x15d6dac: mov             x1, x0
    // 0x15d6db0: r0 = controller()
    //     0x15d6db0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d6db4: mov             x1, x0
    // 0x15d6db8: r0 = getBagCount()
    //     0x15d6db8: bl              #0x9134f0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::getBagCount
    // 0x15d6dbc: r0 = Null
    //     0x15d6dbc: mov             x0, NULL
    // 0x15d6dc0: LeaveFrame
    //     0x15d6dc0: mov             SP, fp
    //     0x15d6dc4: ldp             fp, lr, [SP], #0x10
    // 0x15d6dc8: ret
    //     0x15d6dc8: ret             
    // 0x15d6dcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d6dcc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d6dd0: b               #0x15d6da4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d6dd4, size: 0xdc
    // 0x15d6dd4: EnterFrame
    //     0x15d6dd4: stp             fp, lr, [SP, #-0x10]!
    //     0x15d6dd8: mov             fp, SP
    // 0x15d6ddc: AllocStack(0x28)
    //     0x15d6ddc: sub             SP, SP, #0x28
    // 0x15d6de0: SetupParameters()
    //     0x15d6de0: ldr             x0, [fp, #0x10]
    //     0x15d6de4: ldur            w2, [x0, #0x17]
    //     0x15d6de8: add             x2, x2, HEAP, lsl #32
    //     0x15d6dec: stur            x2, [fp, #-8]
    // 0x15d6df0: CheckStackOverflow
    //     0x15d6df0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d6df4: cmp             SP, x16
    //     0x15d6df8: b.ls            #0x15d6ea8
    // 0x15d6dfc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d6dfc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d6e00: ldr             x0, [x0, #0x1c80]
    //     0x15d6e04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d6e08: cmp             w0, w16
    //     0x15d6e0c: b.ne            #0x15d6e18
    //     0x15d6e10: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d6e14: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d6e18: r1 = Null
    //     0x15d6e18: mov             x1, NULL
    // 0x15d6e1c: r2 = 4
    //     0x15d6e1c: movz            x2, #0x4
    // 0x15d6e20: r0 = AllocateArray()
    //     0x15d6e20: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d6e24: r16 = "previousScreenSource"
    //     0x15d6e24: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15d6e28: ldr             x16, [x16, #0x448]
    // 0x15d6e2c: StoreField: r0->field_f = r16
    //     0x15d6e2c: stur            w16, [x0, #0xf]
    // 0x15d6e30: r16 = "profile_page"
    //     0x15d6e30: add             x16, PP, #0x36, lsl #12  ; [pp+0x36cf8] "profile_page"
    //     0x15d6e34: ldr             x16, [x16, #0xcf8]
    // 0x15d6e38: StoreField: r0->field_13 = r16
    //     0x15d6e38: stur            w16, [x0, #0x13]
    // 0x15d6e3c: r16 = <String, String>
    //     0x15d6e3c: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15d6e40: ldr             x16, [x16, #0x788]
    // 0x15d6e44: stp             x0, x16, [SP]
    // 0x15d6e48: r0 = Map._fromLiteral()
    //     0x15d6e48: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15d6e4c: r16 = "/bag"
    //     0x15d6e4c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15d6e50: ldr             x16, [x16, #0x468]
    // 0x15d6e54: stp             x16, NULL, [SP, #8]
    // 0x15d6e58: str             x0, [SP]
    // 0x15d6e5c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15d6e5c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15d6e60: ldr             x4, [x4, #0x438]
    // 0x15d6e64: r0 = GetNavigation.toNamed()
    //     0x15d6e64: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d6e68: stur            x0, [fp, #-0x10]
    // 0x15d6e6c: cmp             w0, NULL
    // 0x15d6e70: b.eq            #0x15d6e98
    // 0x15d6e74: ldur            x2, [fp, #-8]
    // 0x15d6e78: r1 = Function '<anonymous closure>':.
    //     0x15d6e78: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d20] AnonymousClosure: (0x15d6d84), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15d6e7c: ldr             x1, [x1, #0xd20]
    // 0x15d6e80: r0 = AllocateClosure()
    //     0x15d6e80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d6e84: ldur            x16, [fp, #-0x10]
    // 0x15d6e88: stp             x16, NULL, [SP, #8]
    // 0x15d6e8c: str             x0, [SP]
    // 0x15d6e90: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15d6e90: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15d6e94: r0 = then()
    //     0x15d6e94: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15d6e98: r0 = Null
    //     0x15d6e98: mov             x0, NULL
    // 0x15d6e9c: LeaveFrame
    //     0x15d6e9c: mov             SP, fp
    //     0x15d6ea0: ldp             fp, lr, [SP], #0x10
    // 0x15d6ea4: ret
    //     0x15d6ea4: ret             
    // 0x15d6ea8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d6ea8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d6eac: b               #0x15d6dfc
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15d6eb0, size: 0x2f8
    // 0x15d6eb0: EnterFrame
    //     0x15d6eb0: stp             fp, lr, [SP, #-0x10]!
    //     0x15d6eb4: mov             fp, SP
    // 0x15d6eb8: AllocStack(0x58)
    //     0x15d6eb8: sub             SP, SP, #0x58
    // 0x15d6ebc: SetupParameters()
    //     0x15d6ebc: ldr             x0, [fp, #0x10]
    //     0x15d6ec0: ldur            w2, [x0, #0x17]
    //     0x15d6ec4: add             x2, x2, HEAP, lsl #32
    //     0x15d6ec8: stur            x2, [fp, #-8]
    // 0x15d6ecc: CheckStackOverflow
    //     0x15d6ecc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d6ed0: cmp             SP, x16
    //     0x15d6ed4: b.ls            #0x15d71a0
    // 0x15d6ed8: LoadField: r1 = r2->field_f
    //     0x15d6ed8: ldur            w1, [x2, #0xf]
    // 0x15d6edc: DecompressPointer r1
    //     0x15d6edc: add             x1, x1, HEAP, lsl #32
    // 0x15d6ee0: r0 = controller()
    //     0x15d6ee0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d6ee4: LoadField: r1 = r0->field_6b
    //     0x15d6ee4: ldur            w1, [x0, #0x6b]
    // 0x15d6ee8: DecompressPointer r1
    //     0x15d6ee8: add             x1, x1, HEAP, lsl #32
    // 0x15d6eec: r0 = value()
    //     0x15d6eec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d6ef0: LoadField: r1 = r0->field_1f
    //     0x15d6ef0: ldur            w1, [x0, #0x1f]
    // 0x15d6ef4: DecompressPointer r1
    //     0x15d6ef4: add             x1, x1, HEAP, lsl #32
    // 0x15d6ef8: cmp             w1, NULL
    // 0x15d6efc: b.ne            #0x15d6f08
    // 0x15d6f00: r0 = Null
    //     0x15d6f00: mov             x0, NULL
    // 0x15d6f04: b               #0x15d6f10
    // 0x15d6f08: LoadField: r0 = r1->field_7
    //     0x15d6f08: ldur            w0, [x1, #7]
    // 0x15d6f0c: DecompressPointer r0
    //     0x15d6f0c: add             x0, x0, HEAP, lsl #32
    // 0x15d6f10: cmp             w0, NULL
    // 0x15d6f14: b.ne            #0x15d6f20
    // 0x15d6f18: r0 = false
    //     0x15d6f18: add             x0, NULL, #0x30  ; false
    // 0x15d6f1c: b               #0x15d7108
    // 0x15d6f20: tbnz            w0, #4, #0x15d7104
    // 0x15d6f24: ldur            x2, [fp, #-8]
    // 0x15d6f28: LoadField: r1 = r2->field_f
    //     0x15d6f28: ldur            w1, [x2, #0xf]
    // 0x15d6f2c: DecompressPointer r1
    //     0x15d6f2c: add             x1, x1, HEAP, lsl #32
    // 0x15d6f30: r0 = controller()
    //     0x15d6f30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d6f34: mov             x1, x0
    // 0x15d6f38: r0 = couponType()
    //     0x15d6f38: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x15d6f3c: ldur            x2, [fp, #-8]
    // 0x15d6f40: stur            x0, [fp, #-0x10]
    // 0x15d6f44: LoadField: r1 = r2->field_13
    //     0x15d6f44: ldur            w1, [x2, #0x13]
    // 0x15d6f48: DecompressPointer r1
    //     0x15d6f48: add             x1, x1, HEAP, lsl #32
    // 0x15d6f4c: r0 = of()
    //     0x15d6f4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d6f50: LoadField: r2 = r0->field_5b
    //     0x15d6f50: ldur            w2, [x0, #0x5b]
    // 0x15d6f54: DecompressPointer r2
    //     0x15d6f54: add             x2, x2, HEAP, lsl #32
    // 0x15d6f58: ldur            x0, [fp, #-8]
    // 0x15d6f5c: stur            x2, [fp, #-0x18]
    // 0x15d6f60: LoadField: r1 = r0->field_f
    //     0x15d6f60: ldur            w1, [x0, #0xf]
    // 0x15d6f64: DecompressPointer r1
    //     0x15d6f64: add             x1, x1, HEAP, lsl #32
    // 0x15d6f68: r0 = controller()
    //     0x15d6f68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d6f6c: LoadField: r1 = r0->field_83
    //     0x15d6f6c: ldur            w1, [x0, #0x83]
    // 0x15d6f70: DecompressPointer r1
    //     0x15d6f70: add             x1, x1, HEAP, lsl #32
    // 0x15d6f74: r0 = value()
    //     0x15d6f74: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d6f78: cmp             w0, NULL
    // 0x15d6f7c: r16 = true
    //     0x15d6f7c: add             x16, NULL, #0x20  ; true
    // 0x15d6f80: r17 = false
    //     0x15d6f80: add             x17, NULL, #0x30  ; false
    // 0x15d6f84: csel            x2, x16, x17, ne
    // 0x15d6f88: ldur            x0, [fp, #-8]
    // 0x15d6f8c: stur            x2, [fp, #-0x20]
    // 0x15d6f90: LoadField: r1 = r0->field_f
    //     0x15d6f90: ldur            w1, [x0, #0xf]
    // 0x15d6f94: DecompressPointer r1
    //     0x15d6f94: add             x1, x1, HEAP, lsl #32
    // 0x15d6f98: r0 = controller()
    //     0x15d6f98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d6f9c: LoadField: r1 = r0->field_83
    //     0x15d6f9c: ldur            w1, [x0, #0x83]
    // 0x15d6fa0: DecompressPointer r1
    //     0x15d6fa0: add             x1, x1, HEAP, lsl #32
    // 0x15d6fa4: r0 = value()
    //     0x15d6fa4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d6fa8: str             x0, [SP]
    // 0x15d6fac: r0 = _interpolateSingle()
    //     0x15d6fac: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15d6fb0: ldur            x2, [fp, #-8]
    // 0x15d6fb4: stur            x0, [fp, #-0x28]
    // 0x15d6fb8: LoadField: r1 = r2->field_13
    //     0x15d6fb8: ldur            w1, [x2, #0x13]
    // 0x15d6fbc: DecompressPointer r1
    //     0x15d6fbc: add             x1, x1, HEAP, lsl #32
    // 0x15d6fc0: r0 = of()
    //     0x15d6fc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d6fc4: LoadField: r1 = r0->field_87
    //     0x15d6fc4: ldur            w1, [x0, #0x87]
    // 0x15d6fc8: DecompressPointer r1
    //     0x15d6fc8: add             x1, x1, HEAP, lsl #32
    // 0x15d6fcc: LoadField: r0 = r1->field_27
    //     0x15d6fcc: ldur            w0, [x1, #0x27]
    // 0x15d6fd0: DecompressPointer r0
    //     0x15d6fd0: add             x0, x0, HEAP, lsl #32
    // 0x15d6fd4: r16 = Instance_Color
    //     0x15d6fd4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15d6fd8: str             x16, [SP]
    // 0x15d6fdc: mov             x1, x0
    // 0x15d6fe0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15d6fe0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15d6fe4: ldr             x4, [x4, #0xf40]
    // 0x15d6fe8: r0 = copyWith()
    //     0x15d6fe8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d6fec: stur            x0, [fp, #-0x30]
    // 0x15d6ff0: r0 = Text()
    //     0x15d6ff0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d6ff4: mov             x2, x0
    // 0x15d6ff8: ldur            x0, [fp, #-0x28]
    // 0x15d6ffc: stur            x2, [fp, #-0x38]
    // 0x15d7000: StoreField: r2->field_b = r0
    //     0x15d7000: stur            w0, [x2, #0xb]
    // 0x15d7004: ldur            x0, [fp, #-0x30]
    // 0x15d7008: StoreField: r2->field_13 = r0
    //     0x15d7008: stur            w0, [x2, #0x13]
    // 0x15d700c: ldur            x0, [fp, #-8]
    // 0x15d7010: LoadField: r1 = r0->field_13
    //     0x15d7010: ldur            w1, [x0, #0x13]
    // 0x15d7014: DecompressPointer r1
    //     0x15d7014: add             x1, x1, HEAP, lsl #32
    // 0x15d7018: r0 = of()
    //     0x15d7018: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d701c: LoadField: r1 = r0->field_5b
    //     0x15d701c: ldur            w1, [x0, #0x5b]
    // 0x15d7020: DecompressPointer r1
    //     0x15d7020: add             x1, x1, HEAP, lsl #32
    // 0x15d7024: stur            x1, [fp, #-0x28]
    // 0x15d7028: r0 = ColorFilter()
    //     0x15d7028: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d702c: mov             x1, x0
    // 0x15d7030: ldur            x0, [fp, #-0x28]
    // 0x15d7034: stur            x1, [fp, #-0x30]
    // 0x15d7038: StoreField: r1->field_7 = r0
    //     0x15d7038: stur            w0, [x1, #7]
    // 0x15d703c: r0 = Instance_BlendMode
    //     0x15d703c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d7040: ldr             x0, [x0, #0xb30]
    // 0x15d7044: StoreField: r1->field_b = r0
    //     0x15d7044: stur            w0, [x1, #0xb]
    // 0x15d7048: r0 = 1
    //     0x15d7048: movz            x0, #0x1
    // 0x15d704c: StoreField: r1->field_13 = r0
    //     0x15d704c: stur            x0, [x1, #0x13]
    // 0x15d7050: r0 = SvgPicture()
    //     0x15d7050: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d7054: stur            x0, [fp, #-0x28]
    // 0x15d7058: r16 = Instance_BoxFit
    //     0x15d7058: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d705c: ldr             x16, [x16, #0xb18]
    // 0x15d7060: r30 = 24.000000
    //     0x15d7060: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d7064: ldr             lr, [lr, #0xba8]
    // 0x15d7068: stp             lr, x16, [SP, #0x10]
    // 0x15d706c: r16 = 24.000000
    //     0x15d706c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d7070: ldr             x16, [x16, #0xba8]
    // 0x15d7074: ldur            lr, [fp, #-0x30]
    // 0x15d7078: stp             lr, x16, [SP]
    // 0x15d707c: mov             x1, x0
    // 0x15d7080: r2 = "assets/images/shopping_bag.svg"
    //     0x15d7080: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15d7084: ldr             x2, [x2, #0xa60]
    // 0x15d7088: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d7088: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d708c: ldr             x4, [x4, #0xa68]
    // 0x15d7090: r0 = SvgPicture.asset()
    //     0x15d7090: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d7094: r0 = Badge()
    //     0x15d7094: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15d7098: mov             x1, x0
    // 0x15d709c: ldur            x0, [fp, #-0x18]
    // 0x15d70a0: stur            x1, [fp, #-0x30]
    // 0x15d70a4: StoreField: r1->field_b = r0
    //     0x15d70a4: stur            w0, [x1, #0xb]
    // 0x15d70a8: ldur            x0, [fp, #-0x38]
    // 0x15d70ac: StoreField: r1->field_27 = r0
    //     0x15d70ac: stur            w0, [x1, #0x27]
    // 0x15d70b0: ldur            x0, [fp, #-0x20]
    // 0x15d70b4: StoreField: r1->field_2b = r0
    //     0x15d70b4: stur            w0, [x1, #0x2b]
    // 0x15d70b8: ldur            x0, [fp, #-0x28]
    // 0x15d70bc: StoreField: r1->field_2f = r0
    //     0x15d70bc: stur            w0, [x1, #0x2f]
    // 0x15d70c0: r0 = Visibility()
    //     0x15d70c0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d70c4: mov             x1, x0
    // 0x15d70c8: ldur            x0, [fp, #-0x30]
    // 0x15d70cc: StoreField: r1->field_b = r0
    //     0x15d70cc: stur            w0, [x1, #0xb]
    // 0x15d70d0: r0 = Instance_SizedBox
    //     0x15d70d0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d70d4: StoreField: r1->field_f = r0
    //     0x15d70d4: stur            w0, [x1, #0xf]
    // 0x15d70d8: ldur            x0, [fp, #-0x10]
    // 0x15d70dc: StoreField: r1->field_13 = r0
    //     0x15d70dc: stur            w0, [x1, #0x13]
    // 0x15d70e0: r0 = false
    //     0x15d70e0: add             x0, NULL, #0x30  ; false
    // 0x15d70e4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d70e4: stur            w0, [x1, #0x17]
    // 0x15d70e8: StoreField: r1->field_1b = r0
    //     0x15d70e8: stur            w0, [x1, #0x1b]
    // 0x15d70ec: StoreField: r1->field_1f = r0
    //     0x15d70ec: stur            w0, [x1, #0x1f]
    // 0x15d70f0: StoreField: r1->field_23 = r0
    //     0x15d70f0: stur            w0, [x1, #0x23]
    // 0x15d70f4: StoreField: r1->field_27 = r0
    //     0x15d70f4: stur            w0, [x1, #0x27]
    // 0x15d70f8: StoreField: r1->field_2b = r0
    //     0x15d70f8: stur            w0, [x1, #0x2b]
    // 0x15d70fc: mov             x0, x1
    // 0x15d7100: b               #0x15d7120
    // 0x15d7104: r0 = false
    //     0x15d7104: add             x0, NULL, #0x30  ; false
    // 0x15d7108: r0 = Container()
    //     0x15d7108: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15d710c: mov             x1, x0
    // 0x15d7110: stur            x0, [fp, #-0x10]
    // 0x15d7114: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15d7114: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15d7118: r0 = Container()
    //     0x15d7118: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15d711c: ldur            x0, [fp, #-0x10]
    // 0x15d7120: stur            x0, [fp, #-0x10]
    // 0x15d7124: r0 = InkWell()
    //     0x15d7124: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d7128: mov             x3, x0
    // 0x15d712c: ldur            x0, [fp, #-0x10]
    // 0x15d7130: stur            x3, [fp, #-0x18]
    // 0x15d7134: StoreField: r3->field_b = r0
    //     0x15d7134: stur            w0, [x3, #0xb]
    // 0x15d7138: ldur            x2, [fp, #-8]
    // 0x15d713c: r1 = Function '<anonymous closure>':.
    //     0x15d713c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d18] AnonymousClosure: (0x15d6dd4), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15d7140: ldr             x1, [x1, #0xd18]
    // 0x15d7144: r0 = AllocateClosure()
    //     0x15d7144: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d7148: mov             x1, x0
    // 0x15d714c: ldur            x0, [fp, #-0x18]
    // 0x15d7150: StoreField: r0->field_f = r1
    //     0x15d7150: stur            w1, [x0, #0xf]
    // 0x15d7154: r1 = true
    //     0x15d7154: add             x1, NULL, #0x20  ; true
    // 0x15d7158: StoreField: r0->field_43 = r1
    //     0x15d7158: stur            w1, [x0, #0x43]
    // 0x15d715c: r2 = Instance_BoxShape
    //     0x15d715c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d7160: ldr             x2, [x2, #0x80]
    // 0x15d7164: StoreField: r0->field_47 = r2
    //     0x15d7164: stur            w2, [x0, #0x47]
    // 0x15d7168: StoreField: r0->field_6f = r1
    //     0x15d7168: stur            w1, [x0, #0x6f]
    // 0x15d716c: r2 = false
    //     0x15d716c: add             x2, NULL, #0x30  ; false
    // 0x15d7170: StoreField: r0->field_73 = r2
    //     0x15d7170: stur            w2, [x0, #0x73]
    // 0x15d7174: StoreField: r0->field_83 = r1
    //     0x15d7174: stur            w1, [x0, #0x83]
    // 0x15d7178: StoreField: r0->field_7b = r2
    //     0x15d7178: stur            w2, [x0, #0x7b]
    // 0x15d717c: r0 = Padding()
    //     0x15d717c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15d7180: r1 = Instance_EdgeInsets
    //     0x15d7180: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15d7184: ldr             x1, [x1, #0xa78]
    // 0x15d7188: StoreField: r0->field_f = r1
    //     0x15d7188: stur            w1, [x0, #0xf]
    // 0x15d718c: ldur            x1, [fp, #-0x18]
    // 0x15d7190: StoreField: r0->field_b = r1
    //     0x15d7190: stur            w1, [x0, #0xb]
    // 0x15d7194: LeaveFrame
    //     0x15d7194: mov             SP, fp
    //     0x15d7198: ldp             fp, lr, [SP], #0x10
    // 0x15d719c: ret
    //     0x15d719c: ret             
    // 0x15d71a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d71a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d71a4: b               #0x15d6ed8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d71a8, size: 0xb8
    // 0x15d71a8: EnterFrame
    //     0x15d71a8: stp             fp, lr, [SP, #-0x10]!
    //     0x15d71ac: mov             fp, SP
    // 0x15d71b0: AllocStack(0x10)
    //     0x15d71b0: sub             SP, SP, #0x10
    // 0x15d71b4: SetupParameters()
    //     0x15d71b4: ldr             x0, [fp, #0x10]
    //     0x15d71b8: ldur            w1, [x0, #0x17]
    //     0x15d71bc: add             x1, x1, HEAP, lsl #32
    // 0x15d71c0: CheckStackOverflow
    //     0x15d71c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d71c4: cmp             SP, x16
    //     0x15d71c8: b.ls            #0x15d7258
    // 0x15d71cc: LoadField: r0 = r1->field_f
    //     0x15d71cc: ldur            w0, [x1, #0xf]
    // 0x15d71d0: DecompressPointer r0
    //     0x15d71d0: add             x0, x0, HEAP, lsl #32
    // 0x15d71d4: mov             x1, x0
    // 0x15d71d8: r0 = controller()
    //     0x15d71d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d71dc: LoadField: r1 = r0->field_7b
    //     0x15d71dc: ldur            w1, [x0, #0x7b]
    // 0x15d71e0: DecompressPointer r1
    //     0x15d71e0: add             x1, x1, HEAP, lsl #32
    // 0x15d71e4: r0 = value()
    //     0x15d71e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d71e8: tbnz            w0, #4, #0x15d7220
    // 0x15d71ec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d71ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d71f0: ldr             x0, [x0, #0x1c80]
    //     0x15d71f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d71f8: cmp             w0, w16
    //     0x15d71fc: b.ne            #0x15d7208
    //     0x15d7200: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d7204: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d7208: r16 = "/search"
    //     0x15d7208: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15d720c: ldr             x16, [x16, #0x838]
    // 0x15d7210: stp             x16, NULL, [SP]
    // 0x15d7214: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15d7214: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15d7218: r0 = GetNavigation.toNamed()
    //     0x15d7218: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d721c: b               #0x15d7248
    // 0x15d7220: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d7220: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d7224: ldr             x0, [x0, #0x1c80]
    //     0x15d7228: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d722c: cmp             w0, w16
    //     0x15d7230: b.ne            #0x15d723c
    //     0x15d7234: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d7238: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d723c: str             NULL, [SP]
    // 0x15d7240: r4 = const [0x1, 0, 0, 0, null]
    //     0x15d7240: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x15d7244: r0 = GetNavigation.back()
    //     0x15d7244: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x15d7248: r0 = Null
    //     0x15d7248: mov             x0, NULL
    // 0x15d724c: LeaveFrame
    //     0x15d724c: mov             SP, fp
    //     0x15d7250: ldp             fp, lr, [SP], #0x10
    // 0x15d7254: ret
    //     0x15d7254: ret             
    // 0x15d7258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d7258: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d725c: b               #0x15d71cc
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15eaf50, size: 0x434
    // 0x15eaf50: EnterFrame
    //     0x15eaf50: stp             fp, lr, [SP, #-0x10]!
    //     0x15eaf54: mov             fp, SP
    // 0x15eaf58: AllocStack(0x48)
    //     0x15eaf58: sub             SP, SP, #0x48
    // 0x15eaf5c: SetupParameters()
    //     0x15eaf5c: ldr             x0, [fp, #0x10]
    //     0x15eaf60: ldur            w2, [x0, #0x17]
    //     0x15eaf64: add             x2, x2, HEAP, lsl #32
    //     0x15eaf68: stur            x2, [fp, #-8]
    // 0x15eaf6c: CheckStackOverflow
    //     0x15eaf6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15eaf70: cmp             SP, x16
    //     0x15eaf74: b.ls            #0x15eb37c
    // 0x15eaf78: LoadField: r1 = r2->field_f
    //     0x15eaf78: ldur            w1, [x2, #0xf]
    // 0x15eaf7c: DecompressPointer r1
    //     0x15eaf7c: add             x1, x1, HEAP, lsl #32
    // 0x15eaf80: r0 = controller()
    //     0x15eaf80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eaf84: LoadField: r1 = r0->field_6b
    //     0x15eaf84: ldur            w1, [x0, #0x6b]
    // 0x15eaf88: DecompressPointer r1
    //     0x15eaf88: add             x1, x1, HEAP, lsl #32
    // 0x15eaf8c: r0 = value()
    //     0x15eaf8c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eaf90: LoadField: r1 = r0->field_3f
    //     0x15eaf90: ldur            w1, [x0, #0x3f]
    // 0x15eaf94: DecompressPointer r1
    //     0x15eaf94: add             x1, x1, HEAP, lsl #32
    // 0x15eaf98: cmp             w1, NULL
    // 0x15eaf9c: b.ne            #0x15eafa8
    // 0x15eafa0: r0 = Null
    //     0x15eafa0: mov             x0, NULL
    // 0x15eafa4: b               #0x15eafb0
    // 0x15eafa8: LoadField: r0 = r1->field_f
    //     0x15eafa8: ldur            w0, [x1, #0xf]
    // 0x15eafac: DecompressPointer r0
    //     0x15eafac: add             x0, x0, HEAP, lsl #32
    // 0x15eafb0: r1 = LoadClassIdInstr(r0)
    //     0x15eafb0: ldur            x1, [x0, #-1]
    //     0x15eafb4: ubfx            x1, x1, #0xc, #0x14
    // 0x15eafb8: r16 = "image_text"
    //     0x15eafb8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15eafbc: ldr             x16, [x16, #0xa88]
    // 0x15eafc0: stp             x16, x0, [SP]
    // 0x15eafc4: mov             x0, x1
    // 0x15eafc8: mov             lr, x0
    // 0x15eafcc: ldr             lr, [x21, lr, lsl #3]
    // 0x15eafd0: blr             lr
    // 0x15eafd4: tbnz            w0, #4, #0x15eafe0
    // 0x15eafd8: r1 = true
    //     0x15eafd8: add             x1, NULL, #0x20  ; true
    // 0x15eafdc: b               #0x15eb040
    // 0x15eafe0: ldur            x0, [fp, #-8]
    // 0x15eafe4: LoadField: r1 = r0->field_f
    //     0x15eafe4: ldur            w1, [x0, #0xf]
    // 0x15eafe8: DecompressPointer r1
    //     0x15eafe8: add             x1, x1, HEAP, lsl #32
    // 0x15eafec: r0 = controller()
    //     0x15eafec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eaff0: LoadField: r1 = r0->field_6b
    //     0x15eaff0: ldur            w1, [x0, #0x6b]
    // 0x15eaff4: DecompressPointer r1
    //     0x15eaff4: add             x1, x1, HEAP, lsl #32
    // 0x15eaff8: r0 = value()
    //     0x15eaff8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eaffc: LoadField: r1 = r0->field_3f
    //     0x15eaffc: ldur            w1, [x0, #0x3f]
    // 0x15eb000: DecompressPointer r1
    //     0x15eb000: add             x1, x1, HEAP, lsl #32
    // 0x15eb004: cmp             w1, NULL
    // 0x15eb008: b.ne            #0x15eb014
    // 0x15eb00c: r0 = Null
    //     0x15eb00c: mov             x0, NULL
    // 0x15eb010: b               #0x15eb01c
    // 0x15eb014: LoadField: r0 = r1->field_f
    //     0x15eb014: ldur            w0, [x1, #0xf]
    // 0x15eb018: DecompressPointer r0
    //     0x15eb018: add             x0, x0, HEAP, lsl #32
    // 0x15eb01c: r1 = LoadClassIdInstr(r0)
    //     0x15eb01c: ldur            x1, [x0, #-1]
    //     0x15eb020: ubfx            x1, x1, #0xc, #0x14
    // 0x15eb024: r16 = "image"
    //     0x15eb024: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15eb028: stp             x16, x0, [SP]
    // 0x15eb02c: mov             x0, x1
    // 0x15eb030: mov             lr, x0
    // 0x15eb034: ldr             lr, [x21, lr, lsl #3]
    // 0x15eb038: blr             lr
    // 0x15eb03c: mov             x1, x0
    // 0x15eb040: ldur            x0, [fp, #-8]
    // 0x15eb044: stur            x1, [fp, #-0x10]
    // 0x15eb048: r0 = ImageHeaders.forImages()
    //     0x15eb048: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15eb04c: mov             x2, x0
    // 0x15eb050: ldur            x0, [fp, #-8]
    // 0x15eb054: stur            x2, [fp, #-0x18]
    // 0x15eb058: LoadField: r1 = r0->field_f
    //     0x15eb058: ldur            w1, [x0, #0xf]
    // 0x15eb05c: DecompressPointer r1
    //     0x15eb05c: add             x1, x1, HEAP, lsl #32
    // 0x15eb060: r0 = controller()
    //     0x15eb060: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eb064: LoadField: r1 = r0->field_6b
    //     0x15eb064: ldur            w1, [x0, #0x6b]
    // 0x15eb068: DecompressPointer r1
    //     0x15eb068: add             x1, x1, HEAP, lsl #32
    // 0x15eb06c: r0 = value()
    //     0x15eb06c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eb070: LoadField: r1 = r0->field_27
    //     0x15eb070: ldur            w1, [x0, #0x27]
    // 0x15eb074: DecompressPointer r1
    //     0x15eb074: add             x1, x1, HEAP, lsl #32
    // 0x15eb078: cmp             w1, NULL
    // 0x15eb07c: b.ne            #0x15eb088
    // 0x15eb080: r2 = ""
    //     0x15eb080: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15eb084: b               #0x15eb08c
    // 0x15eb088: mov             x2, x1
    // 0x15eb08c: ldur            x0, [fp, #-8]
    // 0x15eb090: ldur            x1, [fp, #-0x10]
    // 0x15eb094: stur            x2, [fp, #-0x20]
    // 0x15eb098: r0 = CachedNetworkImage()
    //     0x15eb098: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15eb09c: stur            x0, [fp, #-0x28]
    // 0x15eb0a0: ldur            x16, [fp, #-0x18]
    // 0x15eb0a4: r30 = Instance_BoxFit
    //     0x15eb0a4: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15eb0a8: ldr             lr, [lr, #0xb18]
    // 0x15eb0ac: stp             lr, x16, [SP, #0x10]
    // 0x15eb0b0: r16 = 50.000000
    //     0x15eb0b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15eb0b4: ldr             x16, [x16, #0xa90]
    // 0x15eb0b8: r30 = 50.000000
    //     0x15eb0b8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15eb0bc: ldr             lr, [lr, #0xa90]
    // 0x15eb0c0: stp             lr, x16, [SP]
    // 0x15eb0c4: mov             x1, x0
    // 0x15eb0c8: ldur            x2, [fp, #-0x20]
    // 0x15eb0cc: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15eb0cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15eb0d0: ldr             x4, [x4, #0xa98]
    // 0x15eb0d4: r0 = CachedNetworkImage()
    //     0x15eb0d4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15eb0d8: r0 = Visibility()
    //     0x15eb0d8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15eb0dc: mov             x2, x0
    // 0x15eb0e0: ldur            x0, [fp, #-0x28]
    // 0x15eb0e4: stur            x2, [fp, #-0x18]
    // 0x15eb0e8: StoreField: r2->field_b = r0
    //     0x15eb0e8: stur            w0, [x2, #0xb]
    // 0x15eb0ec: r0 = Instance_SizedBox
    //     0x15eb0ec: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15eb0f0: StoreField: r2->field_f = r0
    //     0x15eb0f0: stur            w0, [x2, #0xf]
    // 0x15eb0f4: ldur            x1, [fp, #-0x10]
    // 0x15eb0f8: StoreField: r2->field_13 = r1
    //     0x15eb0f8: stur            w1, [x2, #0x13]
    // 0x15eb0fc: r3 = false
    //     0x15eb0fc: add             x3, NULL, #0x30  ; false
    // 0x15eb100: ArrayStore: r2[0] = r3  ; List_4
    //     0x15eb100: stur            w3, [x2, #0x17]
    // 0x15eb104: StoreField: r2->field_1b = r3
    //     0x15eb104: stur            w3, [x2, #0x1b]
    // 0x15eb108: StoreField: r2->field_1f = r3
    //     0x15eb108: stur            w3, [x2, #0x1f]
    // 0x15eb10c: StoreField: r2->field_23 = r3
    //     0x15eb10c: stur            w3, [x2, #0x23]
    // 0x15eb110: StoreField: r2->field_27 = r3
    //     0x15eb110: stur            w3, [x2, #0x27]
    // 0x15eb114: StoreField: r2->field_2b = r3
    //     0x15eb114: stur            w3, [x2, #0x2b]
    // 0x15eb118: ldur            x4, [fp, #-8]
    // 0x15eb11c: LoadField: r1 = r4->field_f
    //     0x15eb11c: ldur            w1, [x4, #0xf]
    // 0x15eb120: DecompressPointer r1
    //     0x15eb120: add             x1, x1, HEAP, lsl #32
    // 0x15eb124: r0 = controller()
    //     0x15eb124: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eb128: LoadField: r1 = r0->field_6b
    //     0x15eb128: ldur            w1, [x0, #0x6b]
    // 0x15eb12c: DecompressPointer r1
    //     0x15eb12c: add             x1, x1, HEAP, lsl #32
    // 0x15eb130: r0 = value()
    //     0x15eb130: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eb134: LoadField: r1 = r0->field_3f
    //     0x15eb134: ldur            w1, [x0, #0x3f]
    // 0x15eb138: DecompressPointer r1
    //     0x15eb138: add             x1, x1, HEAP, lsl #32
    // 0x15eb13c: cmp             w1, NULL
    // 0x15eb140: b.ne            #0x15eb14c
    // 0x15eb144: r0 = Null
    //     0x15eb144: mov             x0, NULL
    // 0x15eb148: b               #0x15eb154
    // 0x15eb14c: LoadField: r0 = r1->field_f
    //     0x15eb14c: ldur            w0, [x1, #0xf]
    // 0x15eb150: DecompressPointer r0
    //     0x15eb150: add             x0, x0, HEAP, lsl #32
    // 0x15eb154: r1 = LoadClassIdInstr(r0)
    //     0x15eb154: ldur            x1, [x0, #-1]
    //     0x15eb158: ubfx            x1, x1, #0xc, #0x14
    // 0x15eb15c: r16 = "image_text"
    //     0x15eb15c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15eb160: ldr             x16, [x16, #0xa88]
    // 0x15eb164: stp             x16, x0, [SP]
    // 0x15eb168: mov             x0, x1
    // 0x15eb16c: mov             lr, x0
    // 0x15eb170: ldr             lr, [x21, lr, lsl #3]
    // 0x15eb174: blr             lr
    // 0x15eb178: tbnz            w0, #4, #0x15eb184
    // 0x15eb17c: r2 = true
    //     0x15eb17c: add             x2, NULL, #0x20  ; true
    // 0x15eb180: b               #0x15eb1e4
    // 0x15eb184: ldur            x0, [fp, #-8]
    // 0x15eb188: LoadField: r1 = r0->field_f
    //     0x15eb188: ldur            w1, [x0, #0xf]
    // 0x15eb18c: DecompressPointer r1
    //     0x15eb18c: add             x1, x1, HEAP, lsl #32
    // 0x15eb190: r0 = controller()
    //     0x15eb190: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eb194: LoadField: r1 = r0->field_6b
    //     0x15eb194: ldur            w1, [x0, #0x6b]
    // 0x15eb198: DecompressPointer r1
    //     0x15eb198: add             x1, x1, HEAP, lsl #32
    // 0x15eb19c: r0 = value()
    //     0x15eb19c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eb1a0: LoadField: r1 = r0->field_3f
    //     0x15eb1a0: ldur            w1, [x0, #0x3f]
    // 0x15eb1a4: DecompressPointer r1
    //     0x15eb1a4: add             x1, x1, HEAP, lsl #32
    // 0x15eb1a8: cmp             w1, NULL
    // 0x15eb1ac: b.ne            #0x15eb1b8
    // 0x15eb1b0: r0 = Null
    //     0x15eb1b0: mov             x0, NULL
    // 0x15eb1b4: b               #0x15eb1c0
    // 0x15eb1b8: LoadField: r0 = r1->field_f
    //     0x15eb1b8: ldur            w0, [x1, #0xf]
    // 0x15eb1bc: DecompressPointer r0
    //     0x15eb1bc: add             x0, x0, HEAP, lsl #32
    // 0x15eb1c0: r1 = LoadClassIdInstr(r0)
    //     0x15eb1c0: ldur            x1, [x0, #-1]
    //     0x15eb1c4: ubfx            x1, x1, #0xc, #0x14
    // 0x15eb1c8: r16 = "text"
    //     0x15eb1c8: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15eb1cc: stp             x16, x0, [SP]
    // 0x15eb1d0: mov             x0, x1
    // 0x15eb1d4: mov             lr, x0
    // 0x15eb1d8: ldr             lr, [x21, lr, lsl #3]
    // 0x15eb1dc: blr             lr
    // 0x15eb1e0: mov             x2, x0
    // 0x15eb1e4: ldur            x0, [fp, #-8]
    // 0x15eb1e8: stur            x2, [fp, #-0x10]
    // 0x15eb1ec: LoadField: r1 = r0->field_f
    //     0x15eb1ec: ldur            w1, [x0, #0xf]
    // 0x15eb1f0: DecompressPointer r1
    //     0x15eb1f0: add             x1, x1, HEAP, lsl #32
    // 0x15eb1f4: r0 = controller()
    //     0x15eb1f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15eb1f8: LoadField: r1 = r0->field_6b
    //     0x15eb1f8: ldur            w1, [x0, #0x6b]
    // 0x15eb1fc: DecompressPointer r1
    //     0x15eb1fc: add             x1, x1, HEAP, lsl #32
    // 0x15eb200: r0 = value()
    //     0x15eb200: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15eb204: LoadField: r1 = r0->field_2b
    //     0x15eb204: ldur            w1, [x0, #0x2b]
    // 0x15eb208: DecompressPointer r1
    //     0x15eb208: add             x1, x1, HEAP, lsl #32
    // 0x15eb20c: cmp             w1, NULL
    // 0x15eb210: b.ne            #0x15eb21c
    // 0x15eb214: r4 = ""
    //     0x15eb214: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15eb218: b               #0x15eb220
    // 0x15eb21c: mov             x4, x1
    // 0x15eb220: ldur            x0, [fp, #-8]
    // 0x15eb224: ldur            x3, [fp, #-0x18]
    // 0x15eb228: ldur            x2, [fp, #-0x10]
    // 0x15eb22c: stur            x4, [fp, #-0x20]
    // 0x15eb230: LoadField: r1 = r0->field_13
    //     0x15eb230: ldur            w1, [x0, #0x13]
    // 0x15eb234: DecompressPointer r1
    //     0x15eb234: add             x1, x1, HEAP, lsl #32
    // 0x15eb238: r0 = of()
    //     0x15eb238: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15eb23c: LoadField: r1 = r0->field_87
    //     0x15eb23c: ldur            w1, [x0, #0x87]
    // 0x15eb240: DecompressPointer r1
    //     0x15eb240: add             x1, x1, HEAP, lsl #32
    // 0x15eb244: LoadField: r0 = r1->field_2b
    //     0x15eb244: ldur            w0, [x1, #0x2b]
    // 0x15eb248: DecompressPointer r0
    //     0x15eb248: add             x0, x0, HEAP, lsl #32
    // 0x15eb24c: r16 = 16.000000
    //     0x15eb24c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15eb250: ldr             x16, [x16, #0x188]
    // 0x15eb254: r30 = Instance_Color
    //     0x15eb254: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15eb258: stp             lr, x16, [SP]
    // 0x15eb25c: mov             x1, x0
    // 0x15eb260: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15eb260: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15eb264: ldr             x4, [x4, #0xaa0]
    // 0x15eb268: r0 = copyWith()
    //     0x15eb268: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15eb26c: stur            x0, [fp, #-8]
    // 0x15eb270: r0 = Text()
    //     0x15eb270: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15eb274: mov             x1, x0
    // 0x15eb278: ldur            x0, [fp, #-0x20]
    // 0x15eb27c: stur            x1, [fp, #-0x28]
    // 0x15eb280: StoreField: r1->field_b = r0
    //     0x15eb280: stur            w0, [x1, #0xb]
    // 0x15eb284: ldur            x0, [fp, #-8]
    // 0x15eb288: StoreField: r1->field_13 = r0
    //     0x15eb288: stur            w0, [x1, #0x13]
    // 0x15eb28c: r0 = Visibility()
    //     0x15eb28c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15eb290: mov             x3, x0
    // 0x15eb294: ldur            x0, [fp, #-0x28]
    // 0x15eb298: stur            x3, [fp, #-8]
    // 0x15eb29c: StoreField: r3->field_b = r0
    //     0x15eb29c: stur            w0, [x3, #0xb]
    // 0x15eb2a0: r0 = Instance_SizedBox
    //     0x15eb2a0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15eb2a4: StoreField: r3->field_f = r0
    //     0x15eb2a4: stur            w0, [x3, #0xf]
    // 0x15eb2a8: ldur            x0, [fp, #-0x10]
    // 0x15eb2ac: StoreField: r3->field_13 = r0
    //     0x15eb2ac: stur            w0, [x3, #0x13]
    // 0x15eb2b0: r0 = false
    //     0x15eb2b0: add             x0, NULL, #0x30  ; false
    // 0x15eb2b4: ArrayStore: r3[0] = r0  ; List_4
    //     0x15eb2b4: stur            w0, [x3, #0x17]
    // 0x15eb2b8: StoreField: r3->field_1b = r0
    //     0x15eb2b8: stur            w0, [x3, #0x1b]
    // 0x15eb2bc: StoreField: r3->field_1f = r0
    //     0x15eb2bc: stur            w0, [x3, #0x1f]
    // 0x15eb2c0: StoreField: r3->field_23 = r0
    //     0x15eb2c0: stur            w0, [x3, #0x23]
    // 0x15eb2c4: StoreField: r3->field_27 = r0
    //     0x15eb2c4: stur            w0, [x3, #0x27]
    // 0x15eb2c8: StoreField: r3->field_2b = r0
    //     0x15eb2c8: stur            w0, [x3, #0x2b]
    // 0x15eb2cc: r1 = Null
    //     0x15eb2cc: mov             x1, NULL
    // 0x15eb2d0: r2 = 6
    //     0x15eb2d0: movz            x2, #0x6
    // 0x15eb2d4: r0 = AllocateArray()
    //     0x15eb2d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15eb2d8: mov             x2, x0
    // 0x15eb2dc: ldur            x0, [fp, #-0x18]
    // 0x15eb2e0: stur            x2, [fp, #-0x10]
    // 0x15eb2e4: StoreField: r2->field_f = r0
    //     0x15eb2e4: stur            w0, [x2, #0xf]
    // 0x15eb2e8: r16 = Instance_SizedBox
    //     0x15eb2e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15eb2ec: ldr             x16, [x16, #0xaa8]
    // 0x15eb2f0: StoreField: r2->field_13 = r16
    //     0x15eb2f0: stur            w16, [x2, #0x13]
    // 0x15eb2f4: ldur            x0, [fp, #-8]
    // 0x15eb2f8: ArrayStore: r2[0] = r0  ; List_4
    //     0x15eb2f8: stur            w0, [x2, #0x17]
    // 0x15eb2fc: r1 = <Widget>
    //     0x15eb2fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15eb300: r0 = AllocateGrowableArray()
    //     0x15eb300: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15eb304: mov             x1, x0
    // 0x15eb308: ldur            x0, [fp, #-0x10]
    // 0x15eb30c: stur            x1, [fp, #-8]
    // 0x15eb310: StoreField: r1->field_f = r0
    //     0x15eb310: stur            w0, [x1, #0xf]
    // 0x15eb314: r0 = 6
    //     0x15eb314: movz            x0, #0x6
    // 0x15eb318: StoreField: r1->field_b = r0
    //     0x15eb318: stur            w0, [x1, #0xb]
    // 0x15eb31c: r0 = Row()
    //     0x15eb31c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15eb320: r1 = Instance_Axis
    //     0x15eb320: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15eb324: StoreField: r0->field_f = r1
    //     0x15eb324: stur            w1, [x0, #0xf]
    // 0x15eb328: r1 = Instance_MainAxisAlignment
    //     0x15eb328: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15eb32c: ldr             x1, [x1, #0xab0]
    // 0x15eb330: StoreField: r0->field_13 = r1
    //     0x15eb330: stur            w1, [x0, #0x13]
    // 0x15eb334: r1 = Instance_MainAxisSize
    //     0x15eb334: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15eb338: ldr             x1, [x1, #0xa10]
    // 0x15eb33c: ArrayStore: r0[0] = r1  ; List_4
    //     0x15eb33c: stur            w1, [x0, #0x17]
    // 0x15eb340: r1 = Instance_CrossAxisAlignment
    //     0x15eb340: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15eb344: ldr             x1, [x1, #0xa18]
    // 0x15eb348: StoreField: r0->field_1b = r1
    //     0x15eb348: stur            w1, [x0, #0x1b]
    // 0x15eb34c: r1 = Instance_VerticalDirection
    //     0x15eb34c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15eb350: ldr             x1, [x1, #0xa20]
    // 0x15eb354: StoreField: r0->field_23 = r1
    //     0x15eb354: stur            w1, [x0, #0x23]
    // 0x15eb358: r1 = Instance_Clip
    //     0x15eb358: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15eb35c: ldr             x1, [x1, #0x38]
    // 0x15eb360: StoreField: r0->field_2b = r1
    //     0x15eb360: stur            w1, [x0, #0x2b]
    // 0x15eb364: StoreField: r0->field_2f = rZR
    //     0x15eb364: stur            xzr, [x0, #0x2f]
    // 0x15eb368: ldur            x1, [fp, #-8]
    // 0x15eb36c: StoreField: r0->field_b = r1
    //     0x15eb36c: stur            w1, [x0, #0xb]
    // 0x15eb370: LeaveFrame
    //     0x15eb370: mov             SP, fp
    //     0x15eb374: ldp             fp, lr, [SP], #0x10
    // 0x15eb378: ret
    //     0x15eb378: ret             
    // 0x15eb37c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eb37c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eb380: b               #0x15eaf78
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15edec4, size: 0x2b0
    // 0x15edec4: EnterFrame
    //     0x15edec4: stp             fp, lr, [SP, #-0x10]!
    //     0x15edec8: mov             fp, SP
    // 0x15edecc: AllocStack(0x30)
    //     0x15edecc: sub             SP, SP, #0x30
    // 0x15eded0: SetupParameters(ProfileView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15eded0: stur            x1, [fp, #-8]
    //     0x15eded4: stur            x2, [fp, #-0x10]
    // 0x15eded8: CheckStackOverflow
    //     0x15eded8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ededc: cmp             SP, x16
    //     0x15edee0: b.ls            #0x15ee16c
    // 0x15edee4: r1 = 2
    //     0x15edee4: movz            x1, #0x2
    // 0x15edee8: r0 = AllocateContext()
    //     0x15edee8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15edeec: ldur            x1, [fp, #-8]
    // 0x15edef0: stur            x0, [fp, #-0x18]
    // 0x15edef4: StoreField: r0->field_f = r1
    //     0x15edef4: stur            w1, [x0, #0xf]
    // 0x15edef8: ldur            x2, [fp, #-0x10]
    // 0x15edefc: StoreField: r0->field_13 = r2
    //     0x15edefc: stur            w2, [x0, #0x13]
    // 0x15edf00: r0 = Obx()
    //     0x15edf00: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15edf04: ldur            x2, [fp, #-0x18]
    // 0x15edf08: r1 = Function '<anonymous closure>':.
    //     0x15edf08: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d00] AnonymousClosure: (0x15eaf50), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15edf0c: ldr             x1, [x1, #0xd00]
    // 0x15edf10: stur            x0, [fp, #-0x10]
    // 0x15edf14: r0 = AllocateClosure()
    //     0x15edf14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15edf18: mov             x1, x0
    // 0x15edf1c: ldur            x0, [fp, #-0x10]
    // 0x15edf20: StoreField: r0->field_b = r1
    //     0x15edf20: stur            w1, [x0, #0xb]
    // 0x15edf24: ldur            x1, [fp, #-8]
    // 0x15edf28: r0 = controller()
    //     0x15edf28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15edf2c: mov             x1, x0
    // 0x15edf30: r0 = userData()
    //     0x15edf30: bl              #0x8a9718  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::userData
    // 0x15edf34: tbnz            w0, #4, #0x15edfcc
    // 0x15edf38: ldur            x2, [fp, #-0x18]
    // 0x15edf3c: LoadField: r1 = r2->field_13
    //     0x15edf3c: ldur            w1, [x2, #0x13]
    // 0x15edf40: DecompressPointer r1
    //     0x15edf40: add             x1, x1, HEAP, lsl #32
    // 0x15edf44: r0 = of()
    //     0x15edf44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15edf48: LoadField: r1 = r0->field_5b
    //     0x15edf48: ldur            w1, [x0, #0x5b]
    // 0x15edf4c: DecompressPointer r1
    //     0x15edf4c: add             x1, x1, HEAP, lsl #32
    // 0x15edf50: stur            x1, [fp, #-8]
    // 0x15edf54: r0 = ColorFilter()
    //     0x15edf54: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15edf58: mov             x1, x0
    // 0x15edf5c: ldur            x0, [fp, #-8]
    // 0x15edf60: stur            x1, [fp, #-0x20]
    // 0x15edf64: StoreField: r1->field_7 = r0
    //     0x15edf64: stur            w0, [x1, #7]
    // 0x15edf68: r0 = Instance_BlendMode
    //     0x15edf68: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15edf6c: ldr             x0, [x0, #0xb30]
    // 0x15edf70: StoreField: r1->field_b = r0
    //     0x15edf70: stur            w0, [x1, #0xb]
    // 0x15edf74: r2 = 1
    //     0x15edf74: movz            x2, #0x1
    // 0x15edf78: StoreField: r1->field_13 = r2
    //     0x15edf78: stur            x2, [x1, #0x13]
    // 0x15edf7c: r0 = SvgPicture()
    //     0x15edf7c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15edf80: stur            x0, [fp, #-8]
    // 0x15edf84: ldur            x16, [fp, #-0x20]
    // 0x15edf88: str             x16, [SP]
    // 0x15edf8c: mov             x1, x0
    // 0x15edf90: r2 = "assets/images/search.svg"
    //     0x15edf90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15edf94: ldr             x2, [x2, #0xa30]
    // 0x15edf98: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15edf98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15edf9c: ldr             x4, [x4, #0xa38]
    // 0x15edfa0: r0 = SvgPicture.asset()
    //     0x15edfa0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15edfa4: r0 = Align()
    //     0x15edfa4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15edfa8: r3 = Instance_Alignment
    //     0x15edfa8: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15edfac: ldr             x3, [x3, #0xb10]
    // 0x15edfb0: StoreField: r0->field_f = r3
    //     0x15edfb0: stur            w3, [x0, #0xf]
    // 0x15edfb4: r4 = 1.000000
    //     0x15edfb4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15edfb8: StoreField: r0->field_13 = r4
    //     0x15edfb8: stur            w4, [x0, #0x13]
    // 0x15edfbc: ArrayStore: r0[0] = r4  ; List_4
    //     0x15edfbc: stur            w4, [x0, #0x17]
    // 0x15edfc0: ldur            x1, [fp, #-8]
    // 0x15edfc4: StoreField: r0->field_b = r1
    //     0x15edfc4: stur            w1, [x0, #0xb]
    // 0x15edfc8: b               #0x15ee07c
    // 0x15edfcc: ldur            x5, [fp, #-0x18]
    // 0x15edfd0: r4 = 1.000000
    //     0x15edfd0: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15edfd4: r0 = Instance_BlendMode
    //     0x15edfd4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15edfd8: ldr             x0, [x0, #0xb30]
    // 0x15edfdc: r3 = Instance_Alignment
    //     0x15edfdc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15edfe0: ldr             x3, [x3, #0xb10]
    // 0x15edfe4: r2 = 1
    //     0x15edfe4: movz            x2, #0x1
    // 0x15edfe8: LoadField: r1 = r5->field_13
    //     0x15edfe8: ldur            w1, [x5, #0x13]
    // 0x15edfec: DecompressPointer r1
    //     0x15edfec: add             x1, x1, HEAP, lsl #32
    // 0x15edff0: r0 = of()
    //     0x15edff0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15edff4: LoadField: r1 = r0->field_5b
    //     0x15edff4: ldur            w1, [x0, #0x5b]
    // 0x15edff8: DecompressPointer r1
    //     0x15edff8: add             x1, x1, HEAP, lsl #32
    // 0x15edffc: stur            x1, [fp, #-8]
    // 0x15ee000: r0 = ColorFilter()
    //     0x15ee000: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ee004: mov             x1, x0
    // 0x15ee008: ldur            x0, [fp, #-8]
    // 0x15ee00c: stur            x1, [fp, #-0x20]
    // 0x15ee010: StoreField: r1->field_7 = r0
    //     0x15ee010: stur            w0, [x1, #7]
    // 0x15ee014: r0 = Instance_BlendMode
    //     0x15ee014: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ee018: ldr             x0, [x0, #0xb30]
    // 0x15ee01c: StoreField: r1->field_b = r0
    //     0x15ee01c: stur            w0, [x1, #0xb]
    // 0x15ee020: r0 = 1
    //     0x15ee020: movz            x0, #0x1
    // 0x15ee024: StoreField: r1->field_13 = r0
    //     0x15ee024: stur            x0, [x1, #0x13]
    // 0x15ee028: r0 = SvgPicture()
    //     0x15ee028: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ee02c: stur            x0, [fp, #-8]
    // 0x15ee030: ldur            x16, [fp, #-0x20]
    // 0x15ee034: str             x16, [SP]
    // 0x15ee038: mov             x1, x0
    // 0x15ee03c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ee03c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ee040: ldr             x2, [x2, #0xa40]
    // 0x15ee044: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ee044: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ee048: ldr             x4, [x4, #0xa38]
    // 0x15ee04c: r0 = SvgPicture.asset()
    //     0x15ee04c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ee050: r0 = Align()
    //     0x15ee050: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ee054: mov             x1, x0
    // 0x15ee058: r0 = Instance_Alignment
    //     0x15ee058: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ee05c: ldr             x0, [x0, #0xb10]
    // 0x15ee060: StoreField: r1->field_f = r0
    //     0x15ee060: stur            w0, [x1, #0xf]
    // 0x15ee064: r0 = 1.000000
    //     0x15ee064: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ee068: StoreField: r1->field_13 = r0
    //     0x15ee068: stur            w0, [x1, #0x13]
    // 0x15ee06c: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ee06c: stur            w0, [x1, #0x17]
    // 0x15ee070: ldur            x0, [fp, #-8]
    // 0x15ee074: StoreField: r1->field_b = r0
    //     0x15ee074: stur            w0, [x1, #0xb]
    // 0x15ee078: mov             x0, x1
    // 0x15ee07c: stur            x0, [fp, #-8]
    // 0x15ee080: r0 = InkWell()
    //     0x15ee080: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ee084: mov             x3, x0
    // 0x15ee088: ldur            x0, [fp, #-8]
    // 0x15ee08c: stur            x3, [fp, #-0x20]
    // 0x15ee090: StoreField: r3->field_b = r0
    //     0x15ee090: stur            w0, [x3, #0xb]
    // 0x15ee094: ldur            x2, [fp, #-0x18]
    // 0x15ee098: r1 = Function '<anonymous closure>':.
    //     0x15ee098: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d08] AnonymousClosure: (0x15d71a8), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15ee09c: ldr             x1, [x1, #0xd08]
    // 0x15ee0a0: r0 = AllocateClosure()
    //     0x15ee0a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ee0a4: ldur            x2, [fp, #-0x20]
    // 0x15ee0a8: StoreField: r2->field_f = r0
    //     0x15ee0a8: stur            w0, [x2, #0xf]
    // 0x15ee0ac: r0 = true
    //     0x15ee0ac: add             x0, NULL, #0x20  ; true
    // 0x15ee0b0: StoreField: r2->field_43 = r0
    //     0x15ee0b0: stur            w0, [x2, #0x43]
    // 0x15ee0b4: r1 = Instance_BoxShape
    //     0x15ee0b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ee0b8: ldr             x1, [x1, #0x80]
    // 0x15ee0bc: StoreField: r2->field_47 = r1
    //     0x15ee0bc: stur            w1, [x2, #0x47]
    // 0x15ee0c0: StoreField: r2->field_6f = r0
    //     0x15ee0c0: stur            w0, [x2, #0x6f]
    // 0x15ee0c4: r1 = false
    //     0x15ee0c4: add             x1, NULL, #0x30  ; false
    // 0x15ee0c8: StoreField: r2->field_73 = r1
    //     0x15ee0c8: stur            w1, [x2, #0x73]
    // 0x15ee0cc: StoreField: r2->field_83 = r0
    //     0x15ee0cc: stur            w0, [x2, #0x83]
    // 0x15ee0d0: StoreField: r2->field_7b = r1
    //     0x15ee0d0: stur            w1, [x2, #0x7b]
    // 0x15ee0d4: r0 = Obx()
    //     0x15ee0d4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15ee0d8: ldur            x2, [fp, #-0x18]
    // 0x15ee0dc: r1 = Function '<anonymous closure>':.
    //     0x15ee0dc: add             x1, PP, #0x36, lsl #12  ; [pp+0x36d10] AnonymousClosure: (0x15d6eb0), in [package:customer_app/app/presentation/views/line/profile/profile_view.dart] ProfileView::appBar (0x15edec4)
    //     0x15ee0e0: ldr             x1, [x1, #0xd10]
    // 0x15ee0e4: stur            x0, [fp, #-8]
    // 0x15ee0e8: r0 = AllocateClosure()
    //     0x15ee0e8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ee0ec: mov             x1, x0
    // 0x15ee0f0: ldur            x0, [fp, #-8]
    // 0x15ee0f4: StoreField: r0->field_b = r1
    //     0x15ee0f4: stur            w1, [x0, #0xb]
    // 0x15ee0f8: r1 = Null
    //     0x15ee0f8: mov             x1, NULL
    // 0x15ee0fc: r2 = 2
    //     0x15ee0fc: movz            x2, #0x2
    // 0x15ee100: r0 = AllocateArray()
    //     0x15ee100: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15ee104: mov             x2, x0
    // 0x15ee108: ldur            x0, [fp, #-8]
    // 0x15ee10c: stur            x2, [fp, #-0x18]
    // 0x15ee110: StoreField: r2->field_f = r0
    //     0x15ee110: stur            w0, [x2, #0xf]
    // 0x15ee114: r1 = <Widget>
    //     0x15ee114: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15ee118: r0 = AllocateGrowableArray()
    //     0x15ee118: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15ee11c: mov             x1, x0
    // 0x15ee120: ldur            x0, [fp, #-0x18]
    // 0x15ee124: stur            x1, [fp, #-8]
    // 0x15ee128: StoreField: r1->field_f = r0
    //     0x15ee128: stur            w0, [x1, #0xf]
    // 0x15ee12c: r0 = 2
    //     0x15ee12c: movz            x0, #0x2
    // 0x15ee130: StoreField: r1->field_b = r0
    //     0x15ee130: stur            w0, [x1, #0xb]
    // 0x15ee134: r0 = AppBar()
    //     0x15ee134: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ee138: stur            x0, [fp, #-0x18]
    // 0x15ee13c: ldur            x16, [fp, #-0x10]
    // 0x15ee140: ldur            lr, [fp, #-8]
    // 0x15ee144: stp             lr, x16, [SP]
    // 0x15ee148: mov             x1, x0
    // 0x15ee14c: ldur            x2, [fp, #-0x20]
    // 0x15ee150: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15ee150: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15ee154: ldr             x4, [x4, #0xa58]
    // 0x15ee158: r0 = AppBar()
    //     0x15ee158: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ee15c: ldur            x0, [fp, #-0x18]
    // 0x15ee160: LeaveFrame
    //     0x15ee160: mov             SP, fp
    //     0x15ee164: ldp             fp, lr, [SP], #0x10
    // 0x15ee168: ret
    //     0x15ee168: ret             
    // 0x15ee16c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ee16c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ee170: b               #0x15edee4
  }
}
