// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_collection_poster_carousel.dart

// class id: 1049312, size: 0x8
class :: {
}

// class id: 3406, size: 0x20, field offset: 0x14
class _ProductCollectionPosterCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xb075c0, size: 0x648
    // 0xb075c0: EnterFrame
    //     0xb075c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb075c4: mov             fp, SP
    // 0xb075c8: AllocStack(0x68)
    //     0xb075c8: sub             SP, SP, #0x68
    // 0xb075cc: SetupParameters(_ProductCollectionPosterCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb075cc: mov             x0, x1
    //     0xb075d0: stur            x1, [fp, #-8]
    //     0xb075d4: mov             x1, x2
    //     0xb075d8: stur            x2, [fp, #-0x10]
    // 0xb075dc: CheckStackOverflow
    //     0xb075dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb075e0: cmp             SP, x16
    //     0xb075e4: b.ls            #0xb07be0
    // 0xb075e8: r1 = 1
    //     0xb075e8: movz            x1, #0x1
    // 0xb075ec: r0 = AllocateContext()
    //     0xb075ec: bl              #0x16f6108  ; AllocateContextStub
    // 0xb075f0: mov             x3, x0
    // 0xb075f4: ldur            x0, [fp, #-8]
    // 0xb075f8: stur            x3, [fp, #-0x20]
    // 0xb075fc: StoreField: r3->field_f = r0
    //     0xb075fc: stur            w0, [x3, #0xf]
    // 0xb07600: LoadField: r1 = r0->field_b
    //     0xb07600: ldur            w1, [x0, #0xb]
    // 0xb07604: DecompressPointer r1
    //     0xb07604: add             x1, x1, HEAP, lsl #32
    // 0xb07608: cmp             w1, NULL
    // 0xb0760c: b.eq            #0xb07be8
    // 0xb07610: LoadField: r2 = r1->field_13
    //     0xb07610: ldur            w2, [x1, #0x13]
    // 0xb07614: DecompressPointer r2
    //     0xb07614: add             x2, x2, HEAP, lsl #32
    // 0xb07618: LoadField: r1 = r2->field_7
    //     0xb07618: ldur            w1, [x2, #7]
    // 0xb0761c: DecompressPointer r1
    //     0xb0761c: add             x1, x1, HEAP, lsl #32
    // 0xb07620: cmp             w1, NULL
    // 0xb07624: b.ne            #0xb07630
    // 0xb07628: r1 = Instance_TitleAlignment
    //     0xb07628: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb0762c: ldr             x1, [x1, #0x518]
    // 0xb07630: r16 = Instance_TitleAlignment
    //     0xb07630: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb07634: ldr             x16, [x16, #0x520]
    // 0xb07638: cmp             w1, w16
    // 0xb0763c: b.ne            #0xb0764c
    // 0xb07640: r4 = Instance_CrossAxisAlignment
    //     0xb07640: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb07644: ldr             x4, [x4, #0xc68]
    // 0xb07648: b               #0xb07670
    // 0xb0764c: r16 = Instance_TitleAlignment
    //     0xb0764c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb07650: ldr             x16, [x16, #0x518]
    // 0xb07654: cmp             w1, w16
    // 0xb07658: b.ne            #0xb07668
    // 0xb0765c: r4 = Instance_CrossAxisAlignment
    //     0xb0765c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb07660: ldr             x4, [x4, #0x890]
    // 0xb07664: b               #0xb07670
    // 0xb07668: r4 = Instance_CrossAxisAlignment
    //     0xb07668: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb0766c: ldr             x4, [x4, #0xa18]
    // 0xb07670: stur            x4, [fp, #-0x18]
    // 0xb07674: r1 = <Widget>
    //     0xb07674: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb07678: r2 = 0
    //     0xb07678: movz            x2, #0
    // 0xb0767c: r0 = _GrowableList()
    //     0xb0767c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb07680: mov             x2, x0
    // 0xb07684: ldur            x1, [fp, #-8]
    // 0xb07688: stur            x2, [fp, #-0x28]
    // 0xb0768c: LoadField: r0 = r1->field_b
    //     0xb0768c: ldur            w0, [x1, #0xb]
    // 0xb07690: DecompressPointer r0
    //     0xb07690: add             x0, x0, HEAP, lsl #32
    // 0xb07694: cmp             w0, NULL
    // 0xb07698: b.eq            #0xb07bec
    // 0xb0769c: LoadField: r3 = r0->field_f
    //     0xb0769c: ldur            w3, [x0, #0xf]
    // 0xb076a0: DecompressPointer r3
    //     0xb076a0: add             x3, x3, HEAP, lsl #32
    // 0xb076a4: LoadField: r0 = r3->field_7
    //     0xb076a4: ldur            w0, [x3, #7]
    // 0xb076a8: cbz             w0, #0xb07838
    // 0xb076ac: r0 = LoadClassIdInstr(r3)
    //     0xb076ac: ldur            x0, [x3, #-1]
    //     0xb076b0: ubfx            x0, x0, #0xc, #0x14
    // 0xb076b4: str             x3, [SP]
    // 0xb076b8: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb076b8: sub             lr, x0, #1, lsl #12
    //     0xb076bc: ldr             lr, [x21, lr, lsl #3]
    //     0xb076c0: blr             lr
    // 0xb076c4: mov             x2, x0
    // 0xb076c8: ldur            x0, [fp, #-8]
    // 0xb076cc: stur            x2, [fp, #-0x38]
    // 0xb076d0: LoadField: r1 = r0->field_b
    //     0xb076d0: ldur            w1, [x0, #0xb]
    // 0xb076d4: DecompressPointer r1
    //     0xb076d4: add             x1, x1, HEAP, lsl #32
    // 0xb076d8: cmp             w1, NULL
    // 0xb076dc: b.eq            #0xb07bf0
    // 0xb076e0: LoadField: r3 = r1->field_13
    //     0xb076e0: ldur            w3, [x1, #0x13]
    // 0xb076e4: DecompressPointer r3
    //     0xb076e4: add             x3, x3, HEAP, lsl #32
    // 0xb076e8: LoadField: r1 = r3->field_7
    //     0xb076e8: ldur            w1, [x3, #7]
    // 0xb076ec: DecompressPointer r1
    //     0xb076ec: add             x1, x1, HEAP, lsl #32
    // 0xb076f0: cmp             w1, NULL
    // 0xb076f4: b.ne            #0xb07700
    // 0xb076f8: r1 = Instance_TitleAlignment
    //     0xb076f8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb076fc: ldr             x1, [x1, #0x518]
    // 0xb07700: r16 = Instance_TitleAlignment
    //     0xb07700: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb07704: ldr             x16, [x16, #0x520]
    // 0xb07708: cmp             w1, w16
    // 0xb0770c: b.ne            #0xb07718
    // 0xb07710: r4 = Instance_TextAlign
    //     0xb07710: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb07714: b               #0xb07734
    // 0xb07718: r16 = Instance_TitleAlignment
    //     0xb07718: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb0771c: ldr             x16, [x16, #0x518]
    // 0xb07720: cmp             w1, w16
    // 0xb07724: b.ne            #0xb07730
    // 0xb07728: r4 = Instance_TextAlign
    //     0xb07728: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb0772c: b               #0xb07734
    // 0xb07730: r4 = Instance_TextAlign
    //     0xb07730: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb07734: ldur            x3, [fp, #-0x28]
    // 0xb07738: ldur            x1, [fp, #-0x10]
    // 0xb0773c: stur            x4, [fp, #-0x30]
    // 0xb07740: r0 = of()
    //     0xb07740: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb07744: LoadField: r1 = r0->field_87
    //     0xb07744: ldur            w1, [x0, #0x87]
    // 0xb07748: DecompressPointer r1
    //     0xb07748: add             x1, x1, HEAP, lsl #32
    // 0xb0774c: LoadField: r0 = r1->field_7
    //     0xb0774c: ldur            w0, [x1, #7]
    // 0xb07750: DecompressPointer r0
    //     0xb07750: add             x0, x0, HEAP, lsl #32
    // 0xb07754: r16 = Instance_Color
    //     0xb07754: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb07758: r30 = 32.000000
    //     0xb07758: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb0775c: ldr             lr, [lr, #0x848]
    // 0xb07760: stp             lr, x16, [SP]
    // 0xb07764: mov             x1, x0
    // 0xb07768: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb07768: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb0776c: ldr             x4, [x4, #0x9b8]
    // 0xb07770: r0 = copyWith()
    //     0xb07770: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb07774: stur            x0, [fp, #-0x40]
    // 0xb07778: r0 = Text()
    //     0xb07778: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb0777c: mov             x1, x0
    // 0xb07780: ldur            x0, [fp, #-0x38]
    // 0xb07784: stur            x1, [fp, #-0x48]
    // 0xb07788: StoreField: r1->field_b = r0
    //     0xb07788: stur            w0, [x1, #0xb]
    // 0xb0778c: ldur            x0, [fp, #-0x40]
    // 0xb07790: StoreField: r1->field_13 = r0
    //     0xb07790: stur            w0, [x1, #0x13]
    // 0xb07794: ldur            x0, [fp, #-0x30]
    // 0xb07798: StoreField: r1->field_1b = r0
    //     0xb07798: stur            w0, [x1, #0x1b]
    // 0xb0779c: r0 = Padding()
    //     0xb0779c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb077a0: mov             x2, x0
    // 0xb077a4: r0 = Instance_EdgeInsets
    //     0xb077a4: add             x0, PP, #0x57, lsl #12  ; [pp+0x57da0] Obj!EdgeInsets@d58fd1
    //     0xb077a8: ldr             x0, [x0, #0xda0]
    // 0xb077ac: stur            x2, [fp, #-0x30]
    // 0xb077b0: StoreField: r2->field_f = r0
    //     0xb077b0: stur            w0, [x2, #0xf]
    // 0xb077b4: ldur            x0, [fp, #-0x48]
    // 0xb077b8: StoreField: r2->field_b = r0
    //     0xb077b8: stur            w0, [x2, #0xb]
    // 0xb077bc: ldur            x0, [fp, #-0x28]
    // 0xb077c0: LoadField: r1 = r0->field_b
    //     0xb077c0: ldur            w1, [x0, #0xb]
    // 0xb077c4: LoadField: r3 = r0->field_f
    //     0xb077c4: ldur            w3, [x0, #0xf]
    // 0xb077c8: DecompressPointer r3
    //     0xb077c8: add             x3, x3, HEAP, lsl #32
    // 0xb077cc: LoadField: r4 = r3->field_b
    //     0xb077cc: ldur            w4, [x3, #0xb]
    // 0xb077d0: r3 = LoadInt32Instr(r1)
    //     0xb077d0: sbfx            x3, x1, #1, #0x1f
    // 0xb077d4: stur            x3, [fp, #-0x50]
    // 0xb077d8: r1 = LoadInt32Instr(r4)
    //     0xb077d8: sbfx            x1, x4, #1, #0x1f
    // 0xb077dc: cmp             x3, x1
    // 0xb077e0: b.ne            #0xb077ec
    // 0xb077e4: mov             x1, x0
    // 0xb077e8: r0 = _growToNextCapacity()
    //     0xb077e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb077ec: ldur            x3, [fp, #-0x28]
    // 0xb077f0: ldur            x2, [fp, #-0x50]
    // 0xb077f4: add             x0, x2, #1
    // 0xb077f8: lsl             x1, x0, #1
    // 0xb077fc: StoreField: r3->field_b = r1
    //     0xb077fc: stur            w1, [x3, #0xb]
    // 0xb07800: LoadField: r1 = r3->field_f
    //     0xb07800: ldur            w1, [x3, #0xf]
    // 0xb07804: DecompressPointer r1
    //     0xb07804: add             x1, x1, HEAP, lsl #32
    // 0xb07808: ldur            x0, [fp, #-0x30]
    // 0xb0780c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb0780c: add             x25, x1, x2, lsl #2
    //     0xb07810: add             x25, x25, #0xf
    //     0xb07814: str             w0, [x25]
    //     0xb07818: tbz             w0, #0, #0xb07834
    //     0xb0781c: ldurb           w16, [x1, #-1]
    //     0xb07820: ldurb           w17, [x0, #-1]
    //     0xb07824: and             x16, x17, x16, lsr #2
    //     0xb07828: tst             x16, HEAP, lsr #32
    //     0xb0782c: b.eq            #0xb07834
    //     0xb07830: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb07834: b               #0xb0783c
    // 0xb07838: mov             x3, x2
    // 0xb0783c: ldur            x0, [fp, #-8]
    // 0xb07840: LoadField: r1 = r0->field_b
    //     0xb07840: ldur            w1, [x0, #0xb]
    // 0xb07844: DecompressPointer r1
    //     0xb07844: add             x1, x1, HEAP, lsl #32
    // 0xb07848: cmp             w1, NULL
    // 0xb0784c: b.eq            #0xb07bf4
    // 0xb07850: LoadField: r2 = r1->field_b
    //     0xb07850: ldur            w2, [x1, #0xb]
    // 0xb07854: DecompressPointer r2
    //     0xb07854: add             x2, x2, HEAP, lsl #32
    // 0xb07858: LoadField: r4 = r2->field_b
    //     0xb07858: ldur            w4, [x2, #0xb]
    // 0xb0785c: stur            x4, [fp, #-0x38]
    // 0xb07860: LoadField: r5 = r0->field_13
    //     0xb07860: ldur            w5, [x0, #0x13]
    // 0xb07864: DecompressPointer r5
    //     0xb07864: add             x5, x5, HEAP, lsl #32
    // 0xb07868: r16 = Sentinel
    //     0xb07868: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb0786c: cmp             w5, w16
    // 0xb07870: b.eq            #0xb07bf8
    // 0xb07874: ldur            x2, [fp, #-0x20]
    // 0xb07878: stur            x5, [fp, #-0x30]
    // 0xb0787c: r1 = Function '<anonymous closure>':.
    //     0xb0787c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57da8] AnonymousClosure: (0xb080e0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::build (0xb075c0)
    //     0xb07880: ldr             x1, [x1, #0xda8]
    // 0xb07884: r0 = AllocateClosure()
    //     0xb07884: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb07888: ldur            x2, [fp, #-0x20]
    // 0xb0788c: r1 = Function '<anonymous closure>':.
    //     0xb0788c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57db0] AnonymousClosure: (0xb07c2c), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::build (0xb075c0)
    //     0xb07890: ldr             x1, [x1, #0xdb0]
    // 0xb07894: stur            x0, [fp, #-0x20]
    // 0xb07898: r0 = AllocateClosure()
    //     0xb07898: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0789c: stur            x0, [fp, #-0x40]
    // 0xb078a0: r0 = PageView()
    //     0xb078a0: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb078a4: stur            x0, [fp, #-0x48]
    // 0xb078a8: r16 = Instance_BouncingScrollPhysics
    //     0xb078a8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb078ac: ldr             x16, [x16, #0x890]
    // 0xb078b0: ldur            lr, [fp, #-0x30]
    // 0xb078b4: stp             lr, x16, [SP]
    // 0xb078b8: mov             x1, x0
    // 0xb078bc: ldur            x2, [fp, #-0x40]
    // 0xb078c0: ldur            x3, [fp, #-0x38]
    // 0xb078c4: ldur            x5, [fp, #-0x20]
    // 0xb078c8: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb078c8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb078cc: ldr             x4, [x4, #0xe40]
    // 0xb078d0: r0 = PageView.builder()
    //     0xb078d0: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb078d4: r0 = AspectRatio()
    //     0xb078d4: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb078d8: d0 = 1.000000
    //     0xb078d8: fmov            d0, #1.00000000
    // 0xb078dc: stur            x0, [fp, #-0x20]
    // 0xb078e0: StoreField: r0->field_f = d0
    //     0xb078e0: stur            d0, [x0, #0xf]
    // 0xb078e4: ldur            x1, [fp, #-0x48]
    // 0xb078e8: StoreField: r0->field_b = r1
    //     0xb078e8: stur            w1, [x0, #0xb]
    // 0xb078ec: ldur            x2, [fp, #-0x28]
    // 0xb078f0: LoadField: r1 = r2->field_b
    //     0xb078f0: ldur            w1, [x2, #0xb]
    // 0xb078f4: LoadField: r3 = r2->field_f
    //     0xb078f4: ldur            w3, [x2, #0xf]
    // 0xb078f8: DecompressPointer r3
    //     0xb078f8: add             x3, x3, HEAP, lsl #32
    // 0xb078fc: LoadField: r4 = r3->field_b
    //     0xb078fc: ldur            w4, [x3, #0xb]
    // 0xb07900: r3 = LoadInt32Instr(r1)
    //     0xb07900: sbfx            x3, x1, #1, #0x1f
    // 0xb07904: stur            x3, [fp, #-0x50]
    // 0xb07908: r1 = LoadInt32Instr(r4)
    //     0xb07908: sbfx            x1, x4, #1, #0x1f
    // 0xb0790c: cmp             x3, x1
    // 0xb07910: b.ne            #0xb0791c
    // 0xb07914: mov             x1, x2
    // 0xb07918: r0 = _growToNextCapacity()
    //     0xb07918: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb0791c: ldur            x4, [fp, #-8]
    // 0xb07920: ldur            x2, [fp, #-0x28]
    // 0xb07924: ldur            x3, [fp, #-0x50]
    // 0xb07928: add             x0, x3, #1
    // 0xb0792c: lsl             x1, x0, #1
    // 0xb07930: StoreField: r2->field_b = r1
    //     0xb07930: stur            w1, [x2, #0xb]
    // 0xb07934: LoadField: r1 = r2->field_f
    //     0xb07934: ldur            w1, [x2, #0xf]
    // 0xb07938: DecompressPointer r1
    //     0xb07938: add             x1, x1, HEAP, lsl #32
    // 0xb0793c: ldur            x0, [fp, #-0x20]
    // 0xb07940: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb07940: add             x25, x1, x3, lsl #2
    //     0xb07944: add             x25, x25, #0xf
    //     0xb07948: str             w0, [x25]
    //     0xb0794c: tbz             w0, #0, #0xb07968
    //     0xb07950: ldurb           w16, [x1, #-1]
    //     0xb07954: ldurb           w17, [x0, #-1]
    //     0xb07958: and             x16, x17, x16, lsr #2
    //     0xb0795c: tst             x16, HEAP, lsr #32
    //     0xb07960: b.eq            #0xb07968
    //     0xb07964: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb07968: LoadField: r0 = r4->field_b
    //     0xb07968: ldur            w0, [x4, #0xb]
    // 0xb0796c: DecompressPointer r0
    //     0xb0796c: add             x0, x0, HEAP, lsl #32
    // 0xb07970: cmp             w0, NULL
    // 0xb07974: b.eq            #0xb07c04
    // 0xb07978: LoadField: r1 = r0->field_b
    //     0xb07978: ldur            w1, [x0, #0xb]
    // 0xb0797c: DecompressPointer r1
    //     0xb0797c: add             x1, x1, HEAP, lsl #32
    // 0xb07980: LoadField: r0 = r1->field_b
    //     0xb07980: ldur            w0, [x1, #0xb]
    // 0xb07984: r3 = LoadInt32Instr(r0)
    //     0xb07984: sbfx            x3, x0, #1, #0x1f
    // 0xb07988: stur            x3, [fp, #-0x58]
    // 0xb0798c: cmp             x3, #1
    // 0xb07990: b.le            #0xb07af4
    // 0xb07994: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xb07994: ldur            x0, [x4, #0x17]
    // 0xb07998: ldur            x1, [fp, #-0x10]
    // 0xb0799c: stur            x0, [fp, #-0x50]
    // 0xb079a0: r0 = of()
    //     0xb079a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb079a4: LoadField: r1 = r0->field_5b
    //     0xb079a4: ldur            w1, [x0, #0x5b]
    // 0xb079a8: DecompressPointer r1
    //     0xb079a8: add             x1, x1, HEAP, lsl #32
    // 0xb079ac: stur            x1, [fp, #-8]
    // 0xb079b0: r0 = CarouselIndicator()
    //     0xb079b0: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb079b4: mov             x3, x0
    // 0xb079b8: ldur            x0, [fp, #-0x58]
    // 0xb079bc: stur            x3, [fp, #-0x10]
    // 0xb079c0: StoreField: r3->field_b = r0
    //     0xb079c0: stur            x0, [x3, #0xb]
    // 0xb079c4: ldur            x0, [fp, #-0x50]
    // 0xb079c8: StoreField: r3->field_13 = r0
    //     0xb079c8: stur            x0, [x3, #0x13]
    // 0xb079cc: ldur            x0, [fp, #-8]
    // 0xb079d0: StoreField: r3->field_1b = r0
    //     0xb079d0: stur            w0, [x3, #0x1b]
    // 0xb079d4: r0 = Instance_Color
    //     0xb079d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb079d8: ldr             x0, [x0, #0x90]
    // 0xb079dc: StoreField: r3->field_1f = r0
    //     0xb079dc: stur            w0, [x3, #0x1f]
    // 0xb079e0: r1 = Null
    //     0xb079e0: mov             x1, NULL
    // 0xb079e4: r2 = 2
    //     0xb079e4: movz            x2, #0x2
    // 0xb079e8: r0 = AllocateArray()
    //     0xb079e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb079ec: mov             x2, x0
    // 0xb079f0: ldur            x0, [fp, #-0x10]
    // 0xb079f4: stur            x2, [fp, #-8]
    // 0xb079f8: StoreField: r2->field_f = r0
    //     0xb079f8: stur            w0, [x2, #0xf]
    // 0xb079fc: r1 = <Widget>
    //     0xb079fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb07a00: r0 = AllocateGrowableArray()
    //     0xb07a00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb07a04: mov             x1, x0
    // 0xb07a08: ldur            x0, [fp, #-8]
    // 0xb07a0c: stur            x1, [fp, #-0x10]
    // 0xb07a10: StoreField: r1->field_f = r0
    //     0xb07a10: stur            w0, [x1, #0xf]
    // 0xb07a14: r0 = 2
    //     0xb07a14: movz            x0, #0x2
    // 0xb07a18: StoreField: r1->field_b = r0
    //     0xb07a18: stur            w0, [x1, #0xb]
    // 0xb07a1c: r0 = Row()
    //     0xb07a1c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb07a20: mov             x2, x0
    // 0xb07a24: r0 = Instance_Axis
    //     0xb07a24: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb07a28: stur            x2, [fp, #-8]
    // 0xb07a2c: StoreField: r2->field_f = r0
    //     0xb07a2c: stur            w0, [x2, #0xf]
    // 0xb07a30: r0 = Instance_MainAxisAlignment
    //     0xb07a30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb07a34: ldr             x0, [x0, #0xab0]
    // 0xb07a38: StoreField: r2->field_13 = r0
    //     0xb07a38: stur            w0, [x2, #0x13]
    // 0xb07a3c: r0 = Instance_MainAxisSize
    //     0xb07a3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb07a40: ldr             x0, [x0, #0xa10]
    // 0xb07a44: ArrayStore: r2[0] = r0  ; List_4
    //     0xb07a44: stur            w0, [x2, #0x17]
    // 0xb07a48: r0 = Instance_CrossAxisAlignment
    //     0xb07a48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb07a4c: ldr             x0, [x0, #0xa18]
    // 0xb07a50: StoreField: r2->field_1b = r0
    //     0xb07a50: stur            w0, [x2, #0x1b]
    // 0xb07a54: r0 = Instance_VerticalDirection
    //     0xb07a54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb07a58: ldr             x0, [x0, #0xa20]
    // 0xb07a5c: StoreField: r2->field_23 = r0
    //     0xb07a5c: stur            w0, [x2, #0x23]
    // 0xb07a60: r3 = Instance_Clip
    //     0xb07a60: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb07a64: ldr             x3, [x3, #0x38]
    // 0xb07a68: StoreField: r2->field_2b = r3
    //     0xb07a68: stur            w3, [x2, #0x2b]
    // 0xb07a6c: StoreField: r2->field_2f = rZR
    //     0xb07a6c: stur            xzr, [x2, #0x2f]
    // 0xb07a70: ldur            x1, [fp, #-0x10]
    // 0xb07a74: StoreField: r2->field_b = r1
    //     0xb07a74: stur            w1, [x2, #0xb]
    // 0xb07a78: ldur            x4, [fp, #-0x28]
    // 0xb07a7c: LoadField: r1 = r4->field_b
    //     0xb07a7c: ldur            w1, [x4, #0xb]
    // 0xb07a80: LoadField: r5 = r4->field_f
    //     0xb07a80: ldur            w5, [x4, #0xf]
    // 0xb07a84: DecompressPointer r5
    //     0xb07a84: add             x5, x5, HEAP, lsl #32
    // 0xb07a88: LoadField: r6 = r5->field_b
    //     0xb07a88: ldur            w6, [x5, #0xb]
    // 0xb07a8c: r5 = LoadInt32Instr(r1)
    //     0xb07a8c: sbfx            x5, x1, #1, #0x1f
    // 0xb07a90: stur            x5, [fp, #-0x50]
    // 0xb07a94: r1 = LoadInt32Instr(r6)
    //     0xb07a94: sbfx            x1, x6, #1, #0x1f
    // 0xb07a98: cmp             x5, x1
    // 0xb07a9c: b.ne            #0xb07aa8
    // 0xb07aa0: mov             x1, x4
    // 0xb07aa4: r0 = _growToNextCapacity()
    //     0xb07aa4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb07aa8: ldur            x2, [fp, #-0x28]
    // 0xb07aac: ldur            x3, [fp, #-0x50]
    // 0xb07ab0: add             x0, x3, #1
    // 0xb07ab4: lsl             x1, x0, #1
    // 0xb07ab8: StoreField: r2->field_b = r1
    //     0xb07ab8: stur            w1, [x2, #0xb]
    // 0xb07abc: LoadField: r1 = r2->field_f
    //     0xb07abc: ldur            w1, [x2, #0xf]
    // 0xb07ac0: DecompressPointer r1
    //     0xb07ac0: add             x1, x1, HEAP, lsl #32
    // 0xb07ac4: ldur            x0, [fp, #-8]
    // 0xb07ac8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb07ac8: add             x25, x1, x3, lsl #2
    //     0xb07acc: add             x25, x25, #0xf
    //     0xb07ad0: str             w0, [x25]
    //     0xb07ad4: tbz             w0, #0, #0xb07af0
    //     0xb07ad8: ldurb           w16, [x1, #-1]
    //     0xb07adc: ldurb           w17, [x0, #-1]
    //     0xb07ae0: and             x16, x17, x16, lsr #2
    //     0xb07ae4: tst             x16, HEAP, lsr #32
    //     0xb07ae8: b.eq            #0xb07af0
    //     0xb07aec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb07af0: b               #0xb07b80
    // 0xb07af4: r0 = Container()
    //     0xb07af4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb07af8: mov             x1, x0
    // 0xb07afc: stur            x0, [fp, #-8]
    // 0xb07b00: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb07b00: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb07b04: r0 = Container()
    //     0xb07b04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb07b08: ldur            x0, [fp, #-0x28]
    // 0xb07b0c: LoadField: r1 = r0->field_b
    //     0xb07b0c: ldur            w1, [x0, #0xb]
    // 0xb07b10: LoadField: r2 = r0->field_f
    //     0xb07b10: ldur            w2, [x0, #0xf]
    // 0xb07b14: DecompressPointer r2
    //     0xb07b14: add             x2, x2, HEAP, lsl #32
    // 0xb07b18: LoadField: r3 = r2->field_b
    //     0xb07b18: ldur            w3, [x2, #0xb]
    // 0xb07b1c: r2 = LoadInt32Instr(r1)
    //     0xb07b1c: sbfx            x2, x1, #1, #0x1f
    // 0xb07b20: stur            x2, [fp, #-0x50]
    // 0xb07b24: r1 = LoadInt32Instr(r3)
    //     0xb07b24: sbfx            x1, x3, #1, #0x1f
    // 0xb07b28: cmp             x2, x1
    // 0xb07b2c: b.ne            #0xb07b38
    // 0xb07b30: mov             x1, x0
    // 0xb07b34: r0 = _growToNextCapacity()
    //     0xb07b34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb07b38: ldur            x2, [fp, #-0x28]
    // 0xb07b3c: ldur            x3, [fp, #-0x50]
    // 0xb07b40: add             x0, x3, #1
    // 0xb07b44: lsl             x1, x0, #1
    // 0xb07b48: StoreField: r2->field_b = r1
    //     0xb07b48: stur            w1, [x2, #0xb]
    // 0xb07b4c: LoadField: r1 = r2->field_f
    //     0xb07b4c: ldur            w1, [x2, #0xf]
    // 0xb07b50: DecompressPointer r1
    //     0xb07b50: add             x1, x1, HEAP, lsl #32
    // 0xb07b54: ldur            x0, [fp, #-8]
    // 0xb07b58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb07b58: add             x25, x1, x3, lsl #2
    //     0xb07b5c: add             x25, x25, #0xf
    //     0xb07b60: str             w0, [x25]
    //     0xb07b64: tbz             w0, #0, #0xb07b80
    //     0xb07b68: ldurb           w16, [x1, #-1]
    //     0xb07b6c: ldurb           w17, [x0, #-1]
    //     0xb07b70: and             x16, x17, x16, lsr #2
    //     0xb07b74: tst             x16, HEAP, lsr #32
    //     0xb07b78: b.eq            #0xb07b80
    //     0xb07b7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb07b80: ldur            x0, [fp, #-0x18]
    // 0xb07b84: r0 = Column()
    //     0xb07b84: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb07b88: r1 = Instance_Axis
    //     0xb07b88: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb07b8c: StoreField: r0->field_f = r1
    //     0xb07b8c: stur            w1, [x0, #0xf]
    // 0xb07b90: r1 = Instance_MainAxisAlignment
    //     0xb07b90: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb07b94: ldr             x1, [x1, #0xa08]
    // 0xb07b98: StoreField: r0->field_13 = r1
    //     0xb07b98: stur            w1, [x0, #0x13]
    // 0xb07b9c: r1 = Instance_MainAxisSize
    //     0xb07b9c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb07ba0: ldr             x1, [x1, #0xdd0]
    // 0xb07ba4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb07ba4: stur            w1, [x0, #0x17]
    // 0xb07ba8: ldur            x1, [fp, #-0x18]
    // 0xb07bac: StoreField: r0->field_1b = r1
    //     0xb07bac: stur            w1, [x0, #0x1b]
    // 0xb07bb0: r1 = Instance_VerticalDirection
    //     0xb07bb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb07bb4: ldr             x1, [x1, #0xa20]
    // 0xb07bb8: StoreField: r0->field_23 = r1
    //     0xb07bb8: stur            w1, [x0, #0x23]
    // 0xb07bbc: r1 = Instance_Clip
    //     0xb07bbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb07bc0: ldr             x1, [x1, #0x38]
    // 0xb07bc4: StoreField: r0->field_2b = r1
    //     0xb07bc4: stur            w1, [x0, #0x2b]
    // 0xb07bc8: StoreField: r0->field_2f = rZR
    //     0xb07bc8: stur            xzr, [x0, #0x2f]
    // 0xb07bcc: ldur            x1, [fp, #-0x28]
    // 0xb07bd0: StoreField: r0->field_b = r1
    //     0xb07bd0: stur            w1, [x0, #0xb]
    // 0xb07bd4: LeaveFrame
    //     0xb07bd4: mov             SP, fp
    //     0xb07bd8: ldp             fp, lr, [SP], #0x10
    // 0xb07bdc: ret
    //     0xb07bdc: ret             
    // 0xb07be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb07be0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb07be4: b               #0xb075e8
    // 0xb07be8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07be8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07bec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07bec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07bf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07bf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07bf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07bf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb07bf8: r9 = _pageController
    //     0xb07bf8: add             x9, PP, #0x57, lsl #12  ; [pp+0x57db8] Field <_ProductCollectionPosterCarouselState@1497231882._pageController@1497231882>: late (offset: 0x14)
    //     0xb07bfc: ldr             x9, [x9, #0xdb8]
    // 0xb07c00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb07c00: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb07c04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07c04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb07c2c, size: 0x70
    // 0xb07c2c: EnterFrame
    //     0xb07c2c: stp             fp, lr, [SP, #-0x10]!
    //     0xb07c30: mov             fp, SP
    // 0xb07c34: ldr             x0, [fp, #0x20]
    // 0xb07c38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb07c38: ldur            w1, [x0, #0x17]
    // 0xb07c3c: DecompressPointer r1
    //     0xb07c3c: add             x1, x1, HEAP, lsl #32
    // 0xb07c40: CheckStackOverflow
    //     0xb07c40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb07c44: cmp             SP, x16
    //     0xb07c48: b.ls            #0xb07c90
    // 0xb07c4c: LoadField: r0 = r1->field_f
    //     0xb07c4c: ldur            w0, [x1, #0xf]
    // 0xb07c50: DecompressPointer r0
    //     0xb07c50: add             x0, x0, HEAP, lsl #32
    // 0xb07c54: LoadField: r1 = r0->field_b
    //     0xb07c54: ldur            w1, [x0, #0xb]
    // 0xb07c58: DecompressPointer r1
    //     0xb07c58: add             x1, x1, HEAP, lsl #32
    // 0xb07c5c: cmp             w1, NULL
    // 0xb07c60: b.eq            #0xb07c98
    // 0xb07c64: LoadField: r2 = r1->field_b
    //     0xb07c64: ldur            w2, [x1, #0xb]
    // 0xb07c68: DecompressPointer r2
    //     0xb07c68: add             x2, x2, HEAP, lsl #32
    // 0xb07c6c: ldr             x1, [fp, #0x10]
    // 0xb07c70: r3 = LoadInt32Instr(r1)
    //     0xb07c70: sbfx            x3, x1, #1, #0x1f
    //     0xb07c74: tbz             w1, #0, #0xb07c7c
    //     0xb07c78: ldur            x3, [x1, #7]
    // 0xb07c7c: mov             x1, x0
    // 0xb07c80: r0 = cosmeticThemeSlider()
    //     0xb07c80: bl              #0xb07c9c  ; [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_collection_poster_carousel.dart] _ProductCollectionPosterCarouselState::cosmeticThemeSlider
    // 0xb07c84: LeaveFrame
    //     0xb07c84: mov             SP, fp
    //     0xb07c88: ldp             fp, lr, [SP], #0x10
    // 0xb07c8c: ret
    //     0xb07c8c: ret             
    // 0xb07c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb07c90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb07c94: b               #0xb07c4c
    // 0xb07c98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb07c98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeSlider(/* No info */) {
    // ** addr: 0xb07c9c, size: 0x444
    // 0xb07c9c: EnterFrame
    //     0xb07c9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb07ca0: mov             fp, SP
    // 0xb07ca4: AllocStack(0x68)
    //     0xb07ca4: sub             SP, SP, #0x68
    // 0xb07ca8: SetupParameters(_ProductCollectionPosterCarouselState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xb07ca8: mov             x0, x3
    //     0xb07cac: stur            x3, [fp, #-0x18]
    //     0xb07cb0: mov             x3, x1
    //     0xb07cb4: stur            x1, [fp, #-8]
    //     0xb07cb8: stur            x2, [fp, #-0x10]
    // 0xb07cbc: CheckStackOverflow
    //     0xb07cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb07cc0: cmp             SP, x16
    //     0xb07cc4: b.ls            #0xb08090
    // 0xb07cc8: LoadField: r1 = r3->field_f
    //     0xb07cc8: ldur            w1, [x3, #0xf]
    // 0xb07ccc: DecompressPointer r1
    //     0xb07ccc: add             x1, x1, HEAP, lsl #32
    // 0xb07cd0: cmp             w1, NULL
    // 0xb07cd4: b.eq            #0xb08098
    // 0xb07cd8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb07cd8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb07cdc: r0 = _of()
    //     0xb07cdc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb07ce0: LoadField: r1 = r0->field_7
    //     0xb07ce0: ldur            w1, [x0, #7]
    // 0xb07ce4: DecompressPointer r1
    //     0xb07ce4: add             x1, x1, HEAP, lsl #32
    // 0xb07ce8: LoadField: d0 = r1->field_f
    //     0xb07ce8: ldur            d0, [x1, #0xf]
    // 0xb07cec: d1 = 0.350000
    //     0xb07cec: add             x17, PP, #0x34, lsl #12  ; [pp+0x34ab8] IMM: double(0.35) from 0x3fd6666666666666
    //     0xb07cf0: ldr             d1, [x17, #0xab8]
    // 0xb07cf4: fmul            d2, d0, d1
    // 0xb07cf8: ldur            x0, [fp, #-8]
    // 0xb07cfc: stur            d2, [fp, #-0x48]
    // 0xb07d00: LoadField: r1 = r0->field_f
    //     0xb07d00: ldur            w1, [x0, #0xf]
    // 0xb07d04: DecompressPointer r1
    //     0xb07d04: add             x1, x1, HEAP, lsl #32
    // 0xb07d08: cmp             w1, NULL
    // 0xb07d0c: b.eq            #0xb0809c
    // 0xb07d10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb07d10: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb07d14: r0 = _of()
    //     0xb07d14: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb07d18: LoadField: r1 = r0->field_7
    //     0xb07d18: ldur            w1, [x0, #7]
    // 0xb07d1c: DecompressPointer r1
    //     0xb07d1c: add             x1, x1, HEAP, lsl #32
    // 0xb07d20: LoadField: d0 = r1->field_f
    //     0xb07d20: ldur            d0, [x1, #0xf]
    // 0xb07d24: d1 = 0.250000
    //     0xb07d24: fmov            d1, #0.25000000
    // 0xb07d28: fmul            d2, d0, d1
    // 0xb07d2c: stur            d2, [fp, #-0x50]
    // 0xb07d30: r0 = Radius()
    //     0xb07d30: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb07d34: d0 = 20.000000
    //     0xb07d34: fmov            d0, #20.00000000
    // 0xb07d38: stur            x0, [fp, #-0x20]
    // 0xb07d3c: StoreField: r0->field_7 = d0
    //     0xb07d3c: stur            d0, [x0, #7]
    // 0xb07d40: StoreField: r0->field_f = d0
    //     0xb07d40: stur            d0, [x0, #0xf]
    // 0xb07d44: r0 = BorderRadius()
    //     0xb07d44: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb07d48: mov             x3, x0
    // 0xb07d4c: ldur            x0, [fp, #-0x20]
    // 0xb07d50: stur            x3, [fp, #-0x28]
    // 0xb07d54: StoreField: r3->field_7 = r0
    //     0xb07d54: stur            w0, [x3, #7]
    // 0xb07d58: StoreField: r3->field_b = r0
    //     0xb07d58: stur            w0, [x3, #0xb]
    // 0xb07d5c: StoreField: r3->field_f = r0
    //     0xb07d5c: stur            w0, [x3, #0xf]
    // 0xb07d60: StoreField: r3->field_13 = r0
    //     0xb07d60: stur            w0, [x3, #0x13]
    // 0xb07d64: ldur            x4, [fp, #-0x10]
    // 0xb07d68: LoadField: r0 = r4->field_b
    //     0xb07d68: ldur            w0, [x4, #0xb]
    // 0xb07d6c: r1 = LoadInt32Instr(r0)
    //     0xb07d6c: sbfx            x1, x0, #1, #0x1f
    // 0xb07d70: mov             x0, x1
    // 0xb07d74: ldur            x1, [fp, #-0x18]
    // 0xb07d78: cmp             x1, x0
    // 0xb07d7c: b.hs            #0xb080a0
    // 0xb07d80: LoadField: r0 = r4->field_f
    //     0xb07d80: ldur            w0, [x4, #0xf]
    // 0xb07d84: DecompressPointer r0
    //     0xb07d84: add             x0, x0, HEAP, lsl #32
    // 0xb07d88: ldur            x5, [fp, #-0x18]
    // 0xb07d8c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb07d8c: add             x16, x0, x5, lsl #2
    //     0xb07d90: ldur            w1, [x16, #0xf]
    // 0xb07d94: DecompressPointer r1
    //     0xb07d94: add             x1, x1, HEAP, lsl #32
    // 0xb07d98: LoadField: r0 = r1->field_13
    //     0xb07d98: ldur            w0, [x1, #0x13]
    // 0xb07d9c: DecompressPointer r0
    //     0xb07d9c: add             x0, x0, HEAP, lsl #32
    // 0xb07da0: stur            x0, [fp, #-0x20]
    // 0xb07da4: cmp             w0, NULL
    // 0xb07da8: b.eq            #0xb080a4
    // 0xb07dac: r1 = Function '<anonymous closure>':.
    //     0xb07dac: add             x1, PP, #0x57, lsl #12  ; [pp+0x57dc0] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb07db0: ldr             x1, [x1, #0xdc0]
    // 0xb07db4: r2 = Null
    //     0xb07db4: mov             x2, NULL
    // 0xb07db8: r0 = AllocateClosure()
    //     0xb07db8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb07dbc: r1 = Function '<anonymous closure>':.
    //     0xb07dbc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57dc8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb07dc0: ldr             x1, [x1, #0xdc8]
    // 0xb07dc4: r2 = Null
    //     0xb07dc4: mov             x2, NULL
    // 0xb07dc8: stur            x0, [fp, #-0x30]
    // 0xb07dcc: r0 = AllocateClosure()
    //     0xb07dcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb07dd0: stur            x0, [fp, #-0x38]
    // 0xb07dd4: r0 = CachedNetworkImage()
    //     0xb07dd4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb07dd8: stur            x0, [fp, #-0x40]
    // 0xb07ddc: ldur            x16, [fp, #-0x30]
    // 0xb07de0: ldur            lr, [fp, #-0x38]
    // 0xb07de4: stp             lr, x16, [SP, #8]
    // 0xb07de8: r16 = Instance_BoxFit
    //     0xb07de8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xb07dec: ldr             x16, [x16, #0xf38]
    // 0xb07df0: str             x16, [SP]
    // 0xb07df4: mov             x1, x0
    // 0xb07df8: ldur            x2, [fp, #-0x20]
    // 0xb07dfc: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb07dfc: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb07e00: ldr             x4, [x4, #0x790]
    // 0xb07e04: r0 = CachedNetworkImage()
    //     0xb07e04: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb07e08: r0 = ClipRRect()
    //     0xb07e08: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb07e0c: mov             x1, x0
    // 0xb07e10: ldur            x0, [fp, #-0x28]
    // 0xb07e14: stur            x1, [fp, #-0x30]
    // 0xb07e18: StoreField: r1->field_f = r0
    //     0xb07e18: stur            w0, [x1, #0xf]
    // 0xb07e1c: r0 = Instance_Clip
    //     0xb07e1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb07e20: ldr             x0, [x0, #0x138]
    // 0xb07e24: ArrayStore: r1[0] = r0  ; List_4
    //     0xb07e24: stur            w0, [x1, #0x17]
    // 0xb07e28: ldur            x0, [fp, #-0x40]
    // 0xb07e2c: StoreField: r1->field_b = r0
    //     0xb07e2c: stur            w0, [x1, #0xb]
    // 0xb07e30: ldur            d0, [fp, #-0x50]
    // 0xb07e34: r0 = inline_Allocate_Double()
    //     0xb07e34: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb07e38: add             x0, x0, #0x10
    //     0xb07e3c: cmp             x2, x0
    //     0xb07e40: b.ls            #0xb080a8
    //     0xb07e44: str             x0, [THR, #0x50]  ; THR::top
    //     0xb07e48: sub             x0, x0, #0xf
    //     0xb07e4c: movz            x2, #0xe15c
    //     0xb07e50: movk            x2, #0x3, lsl #16
    //     0xb07e54: stur            x2, [x0, #-1]
    // 0xb07e58: StoreField: r0->field_7 = d0
    //     0xb07e58: stur            d0, [x0, #7]
    // 0xb07e5c: stur            x0, [fp, #-0x20]
    // 0xb07e60: r0 = SizedBox()
    //     0xb07e60: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb07e64: mov             x1, x0
    // 0xb07e68: ldur            x0, [fp, #-0x20]
    // 0xb07e6c: stur            x1, [fp, #-0x28]
    // 0xb07e70: StoreField: r1->field_f = r0
    //     0xb07e70: stur            w0, [x1, #0xf]
    // 0xb07e74: ldur            d0, [fp, #-0x48]
    // 0xb07e78: r0 = inline_Allocate_Double()
    //     0xb07e78: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb07e7c: add             x0, x0, #0x10
    //     0xb07e80: cmp             x2, x0
    //     0xb07e84: b.ls            #0xb080c0
    //     0xb07e88: str             x0, [THR, #0x50]  ; THR::top
    //     0xb07e8c: sub             x0, x0, #0xf
    //     0xb07e90: movz            x2, #0xe15c
    //     0xb07e94: movk            x2, #0x3, lsl #16
    //     0xb07e98: stur            x2, [x0, #-1]
    // 0xb07e9c: StoreField: r0->field_7 = d0
    //     0xb07e9c: stur            d0, [x0, #7]
    // 0xb07ea0: StoreField: r1->field_13 = r0
    //     0xb07ea0: stur            w0, [x1, #0x13]
    // 0xb07ea4: ldur            x0, [fp, #-0x30]
    // 0xb07ea8: StoreField: r1->field_b = r0
    //     0xb07ea8: stur            w0, [x1, #0xb]
    // 0xb07eac: r0 = Align()
    //     0xb07eac: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb07eb0: mov             x3, x0
    // 0xb07eb4: r0 = Instance_Alignment
    //     0xb07eb4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb07eb8: ldr             x0, [x0, #0xce0]
    // 0xb07ebc: stur            x3, [fp, #-0x20]
    // 0xb07ec0: StoreField: r3->field_f = r0
    //     0xb07ec0: stur            w0, [x3, #0xf]
    // 0xb07ec4: ldur            x0, [fp, #-0x28]
    // 0xb07ec8: StoreField: r3->field_b = r0
    //     0xb07ec8: stur            w0, [x3, #0xb]
    // 0xb07ecc: ldur            x2, [fp, #-0x10]
    // 0xb07ed0: LoadField: r0 = r2->field_b
    //     0xb07ed0: ldur            w0, [x2, #0xb]
    // 0xb07ed4: r1 = LoadInt32Instr(r0)
    //     0xb07ed4: sbfx            x1, x0, #1, #0x1f
    // 0xb07ed8: mov             x0, x1
    // 0xb07edc: ldur            x1, [fp, #-0x18]
    // 0xb07ee0: cmp             x1, x0
    // 0xb07ee4: b.hs            #0xb080d8
    // 0xb07ee8: LoadField: r0 = r2->field_f
    //     0xb07ee8: ldur            w0, [x2, #0xf]
    // 0xb07eec: DecompressPointer r0
    //     0xb07eec: add             x0, x0, HEAP, lsl #32
    // 0xb07ef0: ldur            x1, [fp, #-0x18]
    // 0xb07ef4: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xb07ef4: add             x16, x0, x1, lsl #2
    //     0xb07ef8: ldur            w2, [x16, #0xf]
    // 0xb07efc: DecompressPointer r2
    //     0xb07efc: add             x2, x2, HEAP, lsl #32
    // 0xb07f00: LoadField: r4 = r2->field_7
    //     0xb07f00: ldur            w4, [x2, #7]
    // 0xb07f04: DecompressPointer r4
    //     0xb07f04: add             x4, x4, HEAP, lsl #32
    // 0xb07f08: mov             x0, x4
    // 0xb07f0c: stur            x4, [fp, #-0x10]
    // 0xb07f10: r2 = Null
    //     0xb07f10: mov             x2, NULL
    // 0xb07f14: r1 = Null
    //     0xb07f14: mov             x1, NULL
    // 0xb07f18: r4 = LoadClassIdInstr(r0)
    //     0xb07f18: ldur            x4, [x0, #-1]
    //     0xb07f1c: ubfx            x4, x4, #0xc, #0x14
    // 0xb07f20: sub             x4, x4, #0x5e
    // 0xb07f24: cmp             x4, #1
    // 0xb07f28: b.ls            #0xb07f3c
    // 0xb07f2c: r8 = String
    //     0xb07f2c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb07f30: r3 = Null
    //     0xb07f30: add             x3, PP, #0x57, lsl #12  ; [pp+0x57dd0] Null
    //     0xb07f34: ldr             x3, [x3, #0xdd0]
    // 0xb07f38: r0 = String()
    //     0xb07f38: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb07f3c: ldur            x0, [fp, #-8]
    // 0xb07f40: LoadField: r1 = r0->field_f
    //     0xb07f40: ldur            w1, [x0, #0xf]
    // 0xb07f44: DecompressPointer r1
    //     0xb07f44: add             x1, x1, HEAP, lsl #32
    // 0xb07f48: cmp             w1, NULL
    // 0xb07f4c: b.eq            #0xb080dc
    // 0xb07f50: r0 = of()
    //     0xb07f50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb07f54: LoadField: r1 = r0->field_87
    //     0xb07f54: ldur            w1, [x0, #0x87]
    // 0xb07f58: DecompressPointer r1
    //     0xb07f58: add             x1, x1, HEAP, lsl #32
    // 0xb07f5c: LoadField: r0 = r1->field_27
    //     0xb07f5c: ldur            w0, [x1, #0x27]
    // 0xb07f60: DecompressPointer r0
    //     0xb07f60: add             x0, x0, HEAP, lsl #32
    // 0xb07f64: r16 = Instance_Color
    //     0xb07f64: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb07f68: r30 = 21.000000
    //     0xb07f68: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb07f6c: ldr             lr, [lr, #0x9b0]
    // 0xb07f70: stp             lr, x16, [SP]
    // 0xb07f74: mov             x1, x0
    // 0xb07f78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb07f78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb07f7c: ldr             x4, [x4, #0x9b8]
    // 0xb07f80: r0 = copyWith()
    //     0xb07f80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb07f84: stur            x0, [fp, #-8]
    // 0xb07f88: r0 = Text()
    //     0xb07f88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb07f8c: mov             x2, x0
    // 0xb07f90: ldur            x0, [fp, #-0x10]
    // 0xb07f94: stur            x2, [fp, #-0x28]
    // 0xb07f98: StoreField: r2->field_b = r0
    //     0xb07f98: stur            w0, [x2, #0xb]
    // 0xb07f9c: ldur            x0, [fp, #-8]
    // 0xb07fa0: StoreField: r2->field_13 = r0
    //     0xb07fa0: stur            w0, [x2, #0x13]
    // 0xb07fa4: r1 = <StackParentData>
    //     0xb07fa4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb07fa8: ldr             x1, [x1, #0x8e0]
    // 0xb07fac: r0 = Positioned()
    //     0xb07fac: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb07fb0: mov             x3, x0
    // 0xb07fb4: r0 = 110.000000
    //     0xb07fb4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb07fb8: ldr             x0, [x0, #0x770]
    // 0xb07fbc: stur            x3, [fp, #-8]
    // 0xb07fc0: StoreField: r3->field_1b = r0
    //     0xb07fc0: stur            w0, [x3, #0x1b]
    // 0xb07fc4: StoreField: r3->field_1f = r0
    //     0xb07fc4: stur            w0, [x3, #0x1f]
    // 0xb07fc8: ldur            x0, [fp, #-0x28]
    // 0xb07fcc: StoreField: r3->field_b = r0
    //     0xb07fcc: stur            w0, [x3, #0xb]
    // 0xb07fd0: r1 = Null
    //     0xb07fd0: mov             x1, NULL
    // 0xb07fd4: r2 = 4
    //     0xb07fd4: movz            x2, #0x4
    // 0xb07fd8: r0 = AllocateArray()
    //     0xb07fd8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb07fdc: mov             x2, x0
    // 0xb07fe0: ldur            x0, [fp, #-0x20]
    // 0xb07fe4: stur            x2, [fp, #-0x10]
    // 0xb07fe8: StoreField: r2->field_f = r0
    //     0xb07fe8: stur            w0, [x2, #0xf]
    // 0xb07fec: ldur            x0, [fp, #-8]
    // 0xb07ff0: StoreField: r2->field_13 = r0
    //     0xb07ff0: stur            w0, [x2, #0x13]
    // 0xb07ff4: r1 = <Widget>
    //     0xb07ff4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb07ff8: r0 = AllocateGrowableArray()
    //     0xb07ff8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb07ffc: mov             x1, x0
    // 0xb08000: ldur            x0, [fp, #-0x10]
    // 0xb08004: stur            x1, [fp, #-8]
    // 0xb08008: StoreField: r1->field_f = r0
    //     0xb08008: stur            w0, [x1, #0xf]
    // 0xb0800c: r0 = 4
    //     0xb0800c: movz            x0, #0x4
    // 0xb08010: StoreField: r1->field_b = r0
    //     0xb08010: stur            w0, [x1, #0xb]
    // 0xb08014: r0 = Stack()
    //     0xb08014: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb08018: mov             x1, x0
    // 0xb0801c: r0 = Instance_Alignment
    //     0xb0801c: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb08020: ldr             x0, [x0, #0x5b8]
    // 0xb08024: stur            x1, [fp, #-0x10]
    // 0xb08028: StoreField: r1->field_f = r0
    //     0xb08028: stur            w0, [x1, #0xf]
    // 0xb0802c: r0 = Instance_StackFit
    //     0xb0802c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb08030: ldr             x0, [x0, #0xfa8]
    // 0xb08034: ArrayStore: r1[0] = r0  ; List_4
    //     0xb08034: stur            w0, [x1, #0x17]
    // 0xb08038: r0 = Instance_Clip
    //     0xb08038: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb0803c: ldr             x0, [x0, #0x7e0]
    // 0xb08040: StoreField: r1->field_1b = r0
    //     0xb08040: stur            w0, [x1, #0x1b]
    // 0xb08044: ldur            x0, [fp, #-8]
    // 0xb08048: StoreField: r1->field_b = r0
    //     0xb08048: stur            w0, [x1, #0xb]
    // 0xb0804c: r0 = AnimatedContainer()
    //     0xb0804c: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb08050: stur            x0, [fp, #-8]
    // 0xb08054: r16 = Instance_EdgeInsets
    //     0xb08054: add             x16, PP, #0x42, lsl #12  ; [pp+0x42620] Obj!EdgeInsets@d57e01
    //     0xb08058: ldr             x16, [x16, #0x620]
    // 0xb0805c: r30 = Instance_Cubic
    //     0xb0805c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb08060: ldr             lr, [lr, #0xaf8]
    // 0xb08064: stp             lr, x16, [SP]
    // 0xb08068: mov             x1, x0
    // 0xb0806c: ldur            x2, [fp, #-0x10]
    // 0xb08070: r3 = Instance_Duration
    //     0xb08070: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb08074: r4 = const [0, 0x5, 0x2, 0x3, curve, 0x4, margin, 0x3, null]
    //     0xb08074: add             x4, PP, #0x57, lsl #12  ; [pp+0x57de0] List(9) [0, 0x5, 0x2, 0x3, "curve", 0x4, "margin", 0x3, Null]
    //     0xb08078: ldr             x4, [x4, #0xde0]
    // 0xb0807c: r0 = AnimatedContainer()
    //     0xb0807c: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb08080: ldur            x0, [fp, #-8]
    // 0xb08084: LeaveFrame
    //     0xb08084: mov             SP, fp
    //     0xb08088: ldp             fp, lr, [SP], #0x10
    // 0xb0808c: ret
    //     0xb0808c: ret             
    // 0xb08090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb08090: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb08094: b               #0xb07cc8
    // 0xb08098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb08098: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb0809c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb0809c: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb080a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb080a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb080a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb080a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb080a8: SaveReg d0
    //     0xb080a8: str             q0, [SP, #-0x10]!
    // 0xb080ac: SaveReg r1
    //     0xb080ac: str             x1, [SP, #-8]!
    // 0xb080b0: r0 = AllocateDouble()
    //     0xb080b0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb080b4: RestoreReg r1
    //     0xb080b4: ldr             x1, [SP], #8
    // 0xb080b8: RestoreReg d0
    //     0xb080b8: ldr             q0, [SP], #0x10
    // 0xb080bc: b               #0xb07e58
    // 0xb080c0: SaveReg d0
    //     0xb080c0: str             q0, [SP, #-0x10]!
    // 0xb080c4: SaveReg r1
    //     0xb080c4: str             x1, [SP, #-8]!
    // 0xb080c8: r0 = AllocateDouble()
    //     0xb080c8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb080cc: RestoreReg r1
    //     0xb080cc: ldr             x1, [SP], #8
    // 0xb080d0: RestoreReg d0
    //     0xb080d0: ldr             q0, [SP], #0x10
    // 0xb080d4: b               #0xb07e9c
    // 0xb080d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb080d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb080dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb080dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb080e0, size: 0x84
    // 0xb080e0: EnterFrame
    //     0xb080e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb080e4: mov             fp, SP
    // 0xb080e8: AllocStack(0x10)
    //     0xb080e8: sub             SP, SP, #0x10
    // 0xb080ec: SetupParameters()
    //     0xb080ec: ldr             x0, [fp, #0x18]
    //     0xb080f0: ldur            w1, [x0, #0x17]
    //     0xb080f4: add             x1, x1, HEAP, lsl #32
    //     0xb080f8: stur            x1, [fp, #-8]
    // 0xb080fc: CheckStackOverflow
    //     0xb080fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb08100: cmp             SP, x16
    //     0xb08104: b.ls            #0xb0815c
    // 0xb08108: r1 = 1
    //     0xb08108: movz            x1, #0x1
    // 0xb0810c: r0 = AllocateContext()
    //     0xb0810c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb08110: mov             x1, x0
    // 0xb08114: ldur            x0, [fp, #-8]
    // 0xb08118: StoreField: r1->field_b = r0
    //     0xb08118: stur            w0, [x1, #0xb]
    // 0xb0811c: ldr             x2, [fp, #0x10]
    // 0xb08120: StoreField: r1->field_f = r2
    //     0xb08120: stur            w2, [x1, #0xf]
    // 0xb08124: LoadField: r3 = r0->field_f
    //     0xb08124: ldur            w3, [x0, #0xf]
    // 0xb08128: DecompressPointer r3
    //     0xb08128: add             x3, x3, HEAP, lsl #32
    // 0xb0812c: mov             x2, x1
    // 0xb08130: stur            x3, [fp, #-0x10]
    // 0xb08134: r1 = Function '<anonymous closure>':.
    //     0xb08134: add             x1, PP, #0x57, lsl #12  ; [pp+0x57de8] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xb08138: ldr             x1, [x1, #0xde8]
    // 0xb0813c: r0 = AllocateClosure()
    //     0xb0813c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb08140: ldur            x1, [fp, #-0x10]
    // 0xb08144: mov             x2, x0
    // 0xb08148: r0 = setState()
    //     0xb08148: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb0814c: r0 = Null
    //     0xb0814c: mov             x0, NULL
    // 0xb08150: LeaveFrame
    //     0xb08150: mov             SP, fp
    //     0xb08154: ldp             fp, lr, [SP], #0x10
    // 0xb08158: ret
    //     0xb08158: ret             
    // 0xb0815c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0815c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb08160: b               #0xb08108
  }
}

// class id: 4143, size: 0x18, field offset: 0xc
//   const constructor, 
class ProductCollectionPosterCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7df44, size: 0x30
    // 0xc7df44: EnterFrame
    //     0xc7df44: stp             fp, lr, [SP, #-0x10]!
    //     0xc7df48: mov             fp, SP
    // 0xc7df4c: mov             x0, x1
    // 0xc7df50: r1 = <ProductCollectionPosterCarousel>
    //     0xc7df50: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b40] TypeArguments: <ProductCollectionPosterCarousel>
    //     0xc7df54: ldr             x1, [x1, #0xb40]
    // 0xc7df58: r0 = _ProductCollectionPosterCarouselState()
    //     0xc7df58: bl              #0xc7df74  ; Allocate_ProductCollectionPosterCarouselStateStub -> _ProductCollectionPosterCarouselState (size=0x20)
    // 0xc7df5c: r1 = Sentinel
    //     0xc7df5c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7df60: StoreField: r0->field_13 = r1
    //     0xc7df60: stur            w1, [x0, #0x13]
    // 0xc7df64: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7df64: stur            xzr, [x0, #0x17]
    // 0xc7df68: LeaveFrame
    //     0xc7df68: mov             SP, fp
    //     0xc7df6c: ldp             fp, lr, [SP], #0x10
    // 0xc7df70: ret
    //     0xc7df70: ret             
  }
}
