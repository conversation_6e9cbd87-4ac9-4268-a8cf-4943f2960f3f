// lib: , url: package:customer_app/app/presentation/views/glass/collections/collection_page.dart

// class id: 1049380, size: 0x8
class :: {
}

// class id: 4573, size: 0x2c, field offset: 0x14
class CollectionPage extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d81e0, size: 0xec
    // 0x14d81e0: EnterFrame
    //     0x14d81e0: stp             fp, lr, [SP, #-0x10]!
    //     0x14d81e4: mov             fp, SP
    // 0x14d81e8: AllocStack(0x20)
    //     0x14d81e8: sub             SP, SP, #0x20
    // 0x14d81ec: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d81ec: stur            x1, [fp, #-8]
    //     0x14d81f0: stur            x2, [fp, #-0x10]
    // 0x14d81f4: CheckStackOverflow
    //     0x14d81f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d81f8: cmp             SP, x16
    //     0x14d81fc: b.ls            #0x14d82c4
    // 0x14d8200: r1 = 2
    //     0x14d8200: movz            x1, #0x2
    // 0x14d8204: r0 = AllocateContext()
    //     0x14d8204: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d8208: mov             x1, x0
    // 0x14d820c: ldur            x0, [fp, #-8]
    // 0x14d8210: stur            x1, [fp, #-0x18]
    // 0x14d8214: StoreField: r1->field_f = r0
    //     0x14d8214: stur            w0, [x1, #0xf]
    // 0x14d8218: ldur            x0, [fp, #-0x10]
    // 0x14d821c: StoreField: r1->field_13 = r0
    //     0x14d821c: stur            w0, [x1, #0x13]
    // 0x14d8220: r0 = Obx()
    //     0x14d8220: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d8224: ldur            x2, [fp, #-0x18]
    // 0x14d8228: r1 = Function '<anonymous closure>':.
    //     0x14d8228: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b78] AnonymousClosure: (0x14d82cc), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d822c: ldr             x1, [x1, #0xb78]
    // 0x14d8230: stur            x0, [fp, #-8]
    // 0x14d8234: r0 = AllocateClosure()
    //     0x14d8234: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8238: mov             x1, x0
    // 0x14d823c: ldur            x0, [fp, #-8]
    // 0x14d8240: StoreField: r0->field_b = r1
    //     0x14d8240: stur            w1, [x0, #0xb]
    // 0x14d8244: ldur            x2, [fp, #-0x18]
    // 0x14d8248: r1 = Function '<anonymous closure>':.
    //     0x14d8248: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b80] AnonymousClosure: (0x148fc58), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x14d824c: ldr             x1, [x1, #0xb80]
    // 0x14d8250: r0 = AllocateClosure()
    //     0x14d8250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8254: ldur            x2, [fp, #-0x18]
    // 0x14d8258: r1 = Function '<anonymous closure>':.
    //     0x14d8258: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b88] AnonymousClosure: (0x148fba8), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x14d825c: ldr             x1, [x1, #0xb88]
    // 0x14d8260: stur            x0, [fp, #-0x10]
    // 0x14d8264: r0 = AllocateClosure()
    //     0x14d8264: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8268: stur            x0, [fp, #-0x20]
    // 0x14d826c: r0 = PagingView()
    //     0x14d826c: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14d8270: mov             x1, x0
    // 0x14d8274: ldur            x2, [fp, #-8]
    // 0x14d8278: ldur            x3, [fp, #-0x20]
    // 0x14d827c: ldur            x5, [fp, #-0x10]
    // 0x14d8280: stur            x0, [fp, #-8]
    // 0x14d8284: r0 = PagingView()
    //     0x14d8284: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14d8288: r0 = WillPopScope()
    //     0x14d8288: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d828c: mov             x3, x0
    // 0x14d8290: ldur            x0, [fp, #-8]
    // 0x14d8294: stur            x3, [fp, #-0x10]
    // 0x14d8298: StoreField: r3->field_b = r0
    //     0x14d8298: stur            w0, [x3, #0xb]
    // 0x14d829c: ldur            x2, [fp, #-0x18]
    // 0x14d82a0: r1 = Function '<anonymous closure>':.
    //     0x14d82a0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b90] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x14d82a4: ldr             x1, [x1, #0xb90]
    // 0x14d82a8: r0 = AllocateClosure()
    //     0x14d82a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d82ac: mov             x1, x0
    // 0x14d82b0: ldur            x0, [fp, #-0x10]
    // 0x14d82b4: StoreField: r0->field_f = r1
    //     0x14d82b4: stur            w1, [x0, #0xf]
    // 0x14d82b8: LeaveFrame
    //     0x14d82b8: mov             SP, fp
    //     0x14d82bc: ldp             fp, lr, [SP], #0x10
    // 0x14d82c0: ret
    //     0x14d82c0: ret             
    // 0x14d82c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d82c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d82c8: b               #0x14d8200
  }
  [closure] StatelessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14d82cc, size: 0x1048
    // 0x14d82cc: EnterFrame
    //     0x14d82cc: stp             fp, lr, [SP, #-0x10]!
    //     0x14d82d0: mov             fp, SP
    // 0x14d82d4: AllocStack(0xa8)
    //     0x14d82d4: sub             SP, SP, #0xa8
    // 0x14d82d8: SetupParameters()
    //     0x14d82d8: ldr             x0, [fp, #0x10]
    //     0x14d82dc: ldur            w2, [x0, #0x17]
    //     0x14d82e0: add             x2, x2, HEAP, lsl #32
    //     0x14d82e4: stur            x2, [fp, #-8]
    // 0x14d82e8: CheckStackOverflow
    //     0x14d82e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d82ec: cmp             SP, x16
    //     0x14d82f0: b.ls            #0x14d92ec
    // 0x14d82f4: LoadField: r1 = r2->field_f
    //     0x14d82f4: ldur            w1, [x2, #0xf]
    // 0x14d82f8: DecompressPointer r1
    //     0x14d82f8: add             x1, x1, HEAP, lsl #32
    // 0x14d82fc: r0 = controller()
    //     0x14d82fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8300: LoadField: r1 = r0->field_67
    //     0x14d8300: ldur            w1, [x0, #0x67]
    // 0x14d8304: DecompressPointer r1
    //     0x14d8304: add             x1, x1, HEAP, lsl #32
    // 0x14d8308: r0 = value()
    //     0x14d8308: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d830c: LoadField: r1 = r0->field_b
    //     0x14d830c: ldur            w1, [x0, #0xb]
    // 0x14d8310: DecompressPointer r1
    //     0x14d8310: add             x1, x1, HEAP, lsl #32
    // 0x14d8314: cmp             w1, NULL
    // 0x14d8318: b.ne            #0x14d8338
    // 0x14d831c: r0 = Container()
    //     0x14d831c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d8320: mov             x1, x0
    // 0x14d8324: stur            x0, [fp, #-0x10]
    // 0x14d8328: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d8328: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d832c: r0 = Container()
    //     0x14d832c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d8330: ldur            x0, [fp, #-0x10]
    // 0x14d8334: b               #0x14d92e0
    // 0x14d8338: ldur            x2, [fp, #-8]
    // 0x14d833c: r0 = Obx()
    //     0x14d833c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d8340: ldur            x2, [fp, #-8]
    // 0x14d8344: r1 = Function '<anonymous closure>':.
    //     0x14d8344: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b98] AnonymousClosure: (0x14dab04), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d8348: ldr             x1, [x1, #0xb98]
    // 0x14d834c: stur            x0, [fp, #-0x10]
    // 0x14d8350: r0 = AllocateClosure()
    //     0x14d8350: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8354: mov             x1, x0
    // 0x14d8358: ldur            x0, [fp, #-0x10]
    // 0x14d835c: StoreField: r0->field_b = r1
    //     0x14d835c: stur            w1, [x0, #0xb]
    // 0x14d8360: r0 = Obx()
    //     0x14d8360: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d8364: ldur            x2, [fp, #-8]
    // 0x14d8368: r1 = Function '<anonymous closure>':.
    //     0x14d8368: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ba0] AnonymousClosure: (0x14da704), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d836c: ldr             x1, [x1, #0xba0]
    // 0x14d8370: stur            x0, [fp, #-0x18]
    // 0x14d8374: r0 = AllocateClosure()
    //     0x14d8374: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8378: mov             x1, x0
    // 0x14d837c: ldur            x0, [fp, #-0x18]
    // 0x14d8380: StoreField: r0->field_b = r1
    //     0x14d8380: stur            w1, [x0, #0xb]
    // 0x14d8384: r0 = Container()
    //     0x14d8384: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d8388: stur            x0, [fp, #-0x20]
    // 0x14d838c: r16 = 10.000000
    //     0x14d838c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x14d8390: str             x16, [SP]
    // 0x14d8394: mov             x1, x0
    // 0x14d8398: r4 = const [0, 0x2, 0x1, 0x1, height, 0x1, null]
    //     0x14d8398: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c090] List(7) [0, 0x2, 0x1, 0x1, "height", 0x1, Null]
    //     0x14d839c: ldr             x4, [x4, #0x90]
    // 0x14d83a0: r0 = Container()
    //     0x14d83a0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d83a4: ldur            x2, [fp, #-8]
    // 0x14d83a8: LoadField: r1 = r2->field_f
    //     0x14d83a8: ldur            w1, [x2, #0xf]
    // 0x14d83ac: DecompressPointer r1
    //     0x14d83ac: add             x1, x1, HEAP, lsl #32
    // 0x14d83b0: r0 = controller()
    //     0x14d83b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d83b4: LoadField: r1 = r0->field_67
    //     0x14d83b4: ldur            w1, [x0, #0x67]
    // 0x14d83b8: DecompressPointer r1
    //     0x14d83b8: add             x1, x1, HEAP, lsl #32
    // 0x14d83bc: r0 = value()
    //     0x14d83bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d83c0: LoadField: r1 = r0->field_b
    //     0x14d83c0: ldur            w1, [x0, #0xb]
    // 0x14d83c4: DecompressPointer r1
    //     0x14d83c4: add             x1, x1, HEAP, lsl #32
    // 0x14d83c8: cmp             w1, NULL
    // 0x14d83cc: b.ne            #0x14d83d8
    // 0x14d83d0: r0 = Null
    //     0x14d83d0: mov             x0, NULL
    // 0x14d83d4: b               #0x14d83e0
    // 0x14d83d8: LoadField: r0 = r1->field_f
    //     0x14d83d8: ldur            w0, [x1, #0xf]
    // 0x14d83dc: DecompressPointer r0
    //     0x14d83dc: add             x0, x0, HEAP, lsl #32
    // 0x14d83e0: cmp             w0, NULL
    // 0x14d83e4: b.ne            #0x14d83ec
    // 0x14d83e8: r0 = ""
    //     0x14d83e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d83ec: ldur            x2, [fp, #-8]
    // 0x14d83f0: stur            x0, [fp, #-0x28]
    // 0x14d83f4: LoadField: r1 = r2->field_13
    //     0x14d83f4: ldur            w1, [x2, #0x13]
    // 0x14d83f8: DecompressPointer r1
    //     0x14d83f8: add             x1, x1, HEAP, lsl #32
    // 0x14d83fc: r0 = of()
    //     0x14d83fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d8400: LoadField: r1 = r0->field_87
    //     0x14d8400: ldur            w1, [x0, #0x87]
    // 0x14d8404: DecompressPointer r1
    //     0x14d8404: add             x1, x1, HEAP, lsl #32
    // 0x14d8408: LoadField: r0 = r1->field_1f
    //     0x14d8408: ldur            w0, [x1, #0x1f]
    // 0x14d840c: DecompressPointer r0
    //     0x14d840c: add             x0, x0, HEAP, lsl #32
    // 0x14d8410: r16 = Instance_Color
    //     0x14d8410: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d8414: r30 = 48.000000
    //     0x14d8414: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14d8418: ldr             lr, [lr, #0xad8]
    // 0x14d841c: stp             lr, x16, [SP]
    // 0x14d8420: mov             x1, x0
    // 0x14d8424: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d8424: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d8428: ldr             x4, [x4, #0x9b8]
    // 0x14d842c: r0 = copyWith()
    //     0x14d842c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d8430: stur            x0, [fp, #-0x30]
    // 0x14d8434: r0 = Text()
    //     0x14d8434: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d8438: mov             x2, x0
    // 0x14d843c: ldur            x0, [fp, #-0x28]
    // 0x14d8440: stur            x2, [fp, #-0x38]
    // 0x14d8444: StoreField: r2->field_b = r0
    //     0x14d8444: stur            w0, [x2, #0xb]
    // 0x14d8448: ldur            x0, [fp, #-0x30]
    // 0x14d844c: StoreField: r2->field_13 = r0
    //     0x14d844c: stur            w0, [x2, #0x13]
    // 0x14d8450: r1 = <FlexParentData>
    //     0x14d8450: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d8454: ldr             x1, [x1, #0xe00]
    // 0x14d8458: r0 = Flexible()
    //     0x14d8458: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14d845c: mov             x1, x0
    // 0x14d8460: r0 = 1
    //     0x14d8460: movz            x0, #0x1
    // 0x14d8464: stur            x1, [fp, #-0x28]
    // 0x14d8468: StoreField: r1->field_13 = r0
    //     0x14d8468: stur            x0, [x1, #0x13]
    // 0x14d846c: r2 = Instance_FlexFit
    //     0x14d846c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14d8470: ldr             x2, [x2, #0xe20]
    // 0x14d8474: StoreField: r1->field_1b = r2
    //     0x14d8474: stur            w2, [x1, #0x1b]
    // 0x14d8478: ldur            x3, [fp, #-0x38]
    // 0x14d847c: StoreField: r1->field_b = r3
    //     0x14d847c: stur            w3, [x1, #0xb]
    // 0x14d8480: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14d8480: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d8484: ldr             x0, [x0, #0x1c80]
    //     0x14d8488: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d848c: cmp             w0, w16
    //     0x14d8490: b.ne            #0x14d849c
    //     0x14d8494: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14d8498: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14d849c: r0 = GetNavigation.size()
    //     0x14d849c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14d84a0: LoadField: d0 = r0->field_7
    //     0x14d84a0: ldur            d0, [x0, #7]
    // 0x14d84a4: stur            d0, [fp, #-0x78]
    // 0x14d84a8: r0 = Radius()
    //     0x14d84a8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14d84ac: d0 = 30.000000
    //     0x14d84ac: fmov            d0, #30.00000000
    // 0x14d84b0: stur            x0, [fp, #-0x30]
    // 0x14d84b4: StoreField: r0->field_7 = d0
    //     0x14d84b4: stur            d0, [x0, #7]
    // 0x14d84b8: StoreField: r0->field_f = d0
    //     0x14d84b8: stur            d0, [x0, #0xf]
    // 0x14d84bc: r0 = BorderRadius()
    //     0x14d84bc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14d84c0: mov             x1, x0
    // 0x14d84c4: ldur            x0, [fp, #-0x30]
    // 0x14d84c8: stur            x1, [fp, #-0x38]
    // 0x14d84cc: StoreField: r1->field_7 = r0
    //     0x14d84cc: stur            w0, [x1, #7]
    // 0x14d84d0: StoreField: r1->field_b = r0
    //     0x14d84d0: stur            w0, [x1, #0xb]
    // 0x14d84d4: StoreField: r1->field_f = r0
    //     0x14d84d4: stur            w0, [x1, #0xf]
    // 0x14d84d8: StoreField: r1->field_13 = r0
    //     0x14d84d8: stur            w0, [x1, #0x13]
    // 0x14d84dc: r0 = RoundedRectangleBorder()
    //     0x14d84dc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x14d84e0: mov             x1, x0
    // 0x14d84e4: ldur            x0, [fp, #-0x38]
    // 0x14d84e8: stur            x1, [fp, #-0x30]
    // 0x14d84ec: StoreField: r1->field_b = r0
    //     0x14d84ec: stur            w0, [x1, #0xb]
    // 0x14d84f0: r0 = Instance_BorderSide
    //     0x14d84f0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x14d84f4: ldr             x0, [x0, #0xe20]
    // 0x14d84f8: StoreField: r1->field_7 = r0
    //     0x14d84f8: stur            w0, [x1, #7]
    // 0x14d84fc: r0 = SvgPicture()
    //     0x14d84fc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14d8500: stur            x0, [fp, #-0x38]
    // 0x14d8504: r16 = 20.000000
    //     0x14d8504: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x14d8508: ldr             x16, [x16, #0xac8]
    // 0x14d850c: r30 = 20.000000
    //     0x14d850c: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x14d8510: ldr             lr, [lr, #0xac8]
    // 0x14d8514: stp             lr, x16, [SP]
    // 0x14d8518: mov             x1, x0
    // 0x14d851c: r2 = "assets/images/filter_icon.svg"
    //     0x14d851c: add             x2, PP, #0x40, lsl #12  ; [pp+0x40ba8] "assets/images/filter_icon.svg"
    //     0x14d8520: ldr             x2, [x2, #0xba8]
    // 0x14d8524: r4 = const [0, 0x4, 0x2, 0x2, height, 0x2, width, 0x3, null]
    //     0x14d8524: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f900] List(9) [0, 0x4, 0x2, 0x2, "height", 0x2, "width", 0x3, Null]
    //     0x14d8528: ldr             x4, [x4, #0x900]
    // 0x14d852c: r0 = SvgPicture.asset()
    //     0x14d852c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14d8530: ldur            x2, [fp, #-8]
    // 0x14d8534: LoadField: r1 = r2->field_13
    //     0x14d8534: ldur            w1, [x2, #0x13]
    // 0x14d8538: DecompressPointer r1
    //     0x14d8538: add             x1, x1, HEAP, lsl #32
    // 0x14d853c: r0 = of()
    //     0x14d853c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d8540: LoadField: r1 = r0->field_87
    //     0x14d8540: ldur            w1, [x0, #0x87]
    // 0x14d8544: DecompressPointer r1
    //     0x14d8544: add             x1, x1, HEAP, lsl #32
    // 0x14d8548: LoadField: r0 = r1->field_7
    //     0x14d8548: ldur            w0, [x1, #7]
    // 0x14d854c: DecompressPointer r0
    //     0x14d854c: add             x0, x0, HEAP, lsl #32
    // 0x14d8550: stur            x0, [fp, #-0x40]
    // 0x14d8554: r1 = Instance_Color
    //     0x14d8554: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d8558: d0 = 0.700000
    //     0x14d8558: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14d855c: ldr             d0, [x17, #0xf48]
    // 0x14d8560: r0 = withOpacity()
    //     0x14d8560: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d8564: r16 = 12.000000
    //     0x14d8564: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14d8568: ldr             x16, [x16, #0x9e8]
    // 0x14d856c: stp             x16, x0, [SP]
    // 0x14d8570: ldur            x1, [fp, #-0x40]
    // 0x14d8574: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d8574: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d8578: ldr             x4, [x4, #0x9b8]
    // 0x14d857c: r0 = copyWith()
    //     0x14d857c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d8580: stur            x0, [fp, #-0x40]
    // 0x14d8584: r0 = Text()
    //     0x14d8584: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d8588: mov             x3, x0
    // 0x14d858c: r0 = "Filters"
    //     0x14d858c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40bb0] "Filters"
    //     0x14d8590: ldr             x0, [x0, #0xbb0]
    // 0x14d8594: stur            x3, [fp, #-0x48]
    // 0x14d8598: StoreField: r3->field_b = r0
    //     0x14d8598: stur            w0, [x3, #0xb]
    // 0x14d859c: ldur            x0, [fp, #-0x40]
    // 0x14d85a0: StoreField: r3->field_13 = r0
    //     0x14d85a0: stur            w0, [x3, #0x13]
    // 0x14d85a4: r0 = Instance_TextAlign
    //     0x14d85a4: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0x14d85a8: StoreField: r3->field_1b = r0
    //     0x14d85a8: stur            w0, [x3, #0x1b]
    // 0x14d85ac: r1 = Null
    //     0x14d85ac: mov             x1, NULL
    // 0x14d85b0: r2 = 6
    //     0x14d85b0: movz            x2, #0x6
    // 0x14d85b4: r0 = AllocateArray()
    //     0x14d85b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d85b8: mov             x2, x0
    // 0x14d85bc: ldur            x0, [fp, #-0x38]
    // 0x14d85c0: stur            x2, [fp, #-0x40]
    // 0x14d85c4: StoreField: r2->field_f = r0
    //     0x14d85c4: stur            w0, [x2, #0xf]
    // 0x14d85c8: r16 = Instance_SizedBox
    //     0x14d85c8: add             x16, PP, #0x40, lsl #12  ; [pp+0x40bb8] Obj!SizedBox@d68221
    //     0x14d85cc: ldr             x16, [x16, #0xbb8]
    // 0x14d85d0: StoreField: r2->field_13 = r16
    //     0x14d85d0: stur            w16, [x2, #0x13]
    // 0x14d85d4: ldur            x0, [fp, #-0x48]
    // 0x14d85d8: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d85d8: stur            w0, [x2, #0x17]
    // 0x14d85dc: r1 = <Widget>
    //     0x14d85dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d85e0: r0 = AllocateGrowableArray()
    //     0x14d85e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d85e4: mov             x1, x0
    // 0x14d85e8: ldur            x0, [fp, #-0x40]
    // 0x14d85ec: stur            x1, [fp, #-0x38]
    // 0x14d85f0: StoreField: r1->field_f = r0
    //     0x14d85f0: stur            w0, [x1, #0xf]
    // 0x14d85f4: r0 = 6
    //     0x14d85f4: movz            x0, #0x6
    // 0x14d85f8: StoreField: r1->field_b = r0
    //     0x14d85f8: stur            w0, [x1, #0xb]
    // 0x14d85fc: r0 = Row()
    //     0x14d85fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d8600: mov             x1, x0
    // 0x14d8604: r0 = Instance_Axis
    //     0x14d8604: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d8608: stur            x1, [fp, #-0x40]
    // 0x14d860c: StoreField: r1->field_f = r0
    //     0x14d860c: stur            w0, [x1, #0xf]
    // 0x14d8610: r2 = Instance_MainAxisAlignment
    //     0x14d8610: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x14d8614: ldr             x2, [x2, #0xab0]
    // 0x14d8618: StoreField: r1->field_13 = r2
    //     0x14d8618: stur            w2, [x1, #0x13]
    // 0x14d861c: r3 = Instance_MainAxisSize
    //     0x14d861c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14d8620: ldr             x3, [x3, #0xdd0]
    // 0x14d8624: ArrayStore: r1[0] = r3  ; List_4
    //     0x14d8624: stur            w3, [x1, #0x17]
    // 0x14d8628: r4 = Instance_CrossAxisAlignment
    //     0x14d8628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d862c: ldr             x4, [x4, #0xa18]
    // 0x14d8630: StoreField: r1->field_1b = r4
    //     0x14d8630: stur            w4, [x1, #0x1b]
    // 0x14d8634: r5 = Instance_VerticalDirection
    //     0x14d8634: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d8638: ldr             x5, [x5, #0xa20]
    // 0x14d863c: StoreField: r1->field_23 = r5
    //     0x14d863c: stur            w5, [x1, #0x23]
    // 0x14d8640: r6 = Instance_Clip
    //     0x14d8640: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d8644: ldr             x6, [x6, #0x38]
    // 0x14d8648: StoreField: r1->field_2b = r6
    //     0x14d8648: stur            w6, [x1, #0x2b]
    // 0x14d864c: StoreField: r1->field_2f = rZR
    //     0x14d864c: stur            xzr, [x1, #0x2f]
    // 0x14d8650: ldur            x7, [fp, #-0x38]
    // 0x14d8654: StoreField: r1->field_b = r7
    //     0x14d8654: stur            w7, [x1, #0xb]
    // 0x14d8658: r0 = InkWell()
    //     0x14d8658: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d865c: mov             x3, x0
    // 0x14d8660: ldur            x0, [fp, #-0x40]
    // 0x14d8664: stur            x3, [fp, #-0x38]
    // 0x14d8668: StoreField: r3->field_b = r0
    //     0x14d8668: stur            w0, [x3, #0xb]
    // 0x14d866c: ldur            x2, [fp, #-8]
    // 0x14d8670: r1 = Function '<anonymous closure>':.
    //     0x14d8670: add             x1, PP, #0x40, lsl #12  ; [pp+0x40bc0] AnonymousClosure: (0x14da44c), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d8674: ldr             x1, [x1, #0xbc0]
    // 0x14d8678: r0 = AllocateClosure()
    //     0x14d8678: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d867c: mov             x1, x0
    // 0x14d8680: ldur            x0, [fp, #-0x38]
    // 0x14d8684: StoreField: r0->field_f = r1
    //     0x14d8684: stur            w1, [x0, #0xf]
    // 0x14d8688: r1 = true
    //     0x14d8688: add             x1, NULL, #0x20  ; true
    // 0x14d868c: StoreField: r0->field_43 = r1
    //     0x14d868c: stur            w1, [x0, #0x43]
    // 0x14d8690: r2 = Instance_BoxShape
    //     0x14d8690: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d8694: ldr             x2, [x2, #0x80]
    // 0x14d8698: StoreField: r0->field_47 = r2
    //     0x14d8698: stur            w2, [x0, #0x47]
    // 0x14d869c: StoreField: r0->field_6f = r1
    //     0x14d869c: stur            w1, [x0, #0x6f]
    // 0x14d86a0: r2 = false
    //     0x14d86a0: add             x2, NULL, #0x30  ; false
    // 0x14d86a4: StoreField: r0->field_73 = r2
    //     0x14d86a4: stur            w2, [x0, #0x73]
    // 0x14d86a8: StoreField: r0->field_83 = r1
    //     0x14d86a8: stur            w1, [x0, #0x83]
    // 0x14d86ac: StoreField: r0->field_7b = r2
    //     0x14d86ac: stur            w2, [x0, #0x7b]
    // 0x14d86b0: r0 = Center()
    //     0x14d86b0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14d86b4: mov             x1, x0
    // 0x14d86b8: r0 = Instance_Alignment
    //     0x14d86b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14d86bc: ldr             x0, [x0, #0xb10]
    // 0x14d86c0: stur            x1, [fp, #-0x40]
    // 0x14d86c4: StoreField: r1->field_f = r0
    //     0x14d86c4: stur            w0, [x1, #0xf]
    // 0x14d86c8: ldur            x2, [fp, #-0x38]
    // 0x14d86cc: StoreField: r1->field_b = r2
    //     0x14d86cc: stur            w2, [x1, #0xb]
    // 0x14d86d0: r0 = Padding()
    //     0x14d86d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d86d4: mov             x2, x0
    // 0x14d86d8: r0 = Instance_EdgeInsets
    //     0x14d86d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14d86dc: ldr             x0, [x0, #0x980]
    // 0x14d86e0: stur            x2, [fp, #-0x38]
    // 0x14d86e4: StoreField: r2->field_f = r0
    //     0x14d86e4: stur            w0, [x2, #0xf]
    // 0x14d86e8: ldur            x0, [fp, #-0x40]
    // 0x14d86ec: StoreField: r2->field_b = r0
    //     0x14d86ec: stur            w0, [x2, #0xb]
    // 0x14d86f0: r1 = <FlexParentData>
    //     0x14d86f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d86f4: ldr             x1, [x1, #0xe00]
    // 0x14d86f8: r0 = Flexible()
    //     0x14d86f8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14d86fc: mov             x2, x0
    // 0x14d8700: r0 = 1
    //     0x14d8700: movz            x0, #0x1
    // 0x14d8704: stur            x2, [fp, #-0x40]
    // 0x14d8708: StoreField: r2->field_13 = r0
    //     0x14d8708: stur            x0, [x2, #0x13]
    // 0x14d870c: r3 = Instance_FlexFit
    //     0x14d870c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14d8710: ldr             x3, [x3, #0xe20]
    // 0x14d8714: StoreField: r2->field_1b = r3
    //     0x14d8714: stur            w3, [x2, #0x1b]
    // 0x14d8718: ldur            x1, [fp, #-0x38]
    // 0x14d871c: StoreField: r2->field_b = r1
    //     0x14d871c: stur            w1, [x2, #0xb]
    // 0x14d8720: r1 = Instance_Color
    //     0x14d8720: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d8724: d0 = 0.100000
    //     0x14d8724: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14d8728: r0 = withOpacity()
    //     0x14d8728: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d872c: stur            x0, [fp, #-0x38]
    // 0x14d8730: r0 = VerticalDivider()
    //     0x14d8730: bl              #0x99b78c  ; AllocateVerticalDividerStub -> VerticalDivider (size=0x28)
    // 0x14d8734: d0 = 1.000000
    //     0x14d8734: fmov            d0, #1.00000000
    // 0x14d8738: stur            x0, [fp, #-0x48]
    // 0x14d873c: StoreField: r0->field_f = d0
    //     0x14d873c: stur            d0, [x0, #0xf]
    // 0x14d8740: ldur            x1, [fp, #-0x38]
    // 0x14d8744: StoreField: r0->field_1f = r1
    //     0x14d8744: stur            w1, [x0, #0x1f]
    // 0x14d8748: r0 = Center()
    //     0x14d8748: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14d874c: mov             x1, x0
    // 0x14d8750: r0 = Instance_Alignment
    //     0x14d8750: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14d8754: ldr             x0, [x0, #0xb10]
    // 0x14d8758: stur            x1, [fp, #-0x38]
    // 0x14d875c: StoreField: r1->field_f = r0
    //     0x14d875c: stur            w0, [x1, #0xf]
    // 0x14d8760: ldur            x2, [fp, #-0x48]
    // 0x14d8764: StoreField: r1->field_b = r2
    //     0x14d8764: stur            w2, [x1, #0xb]
    // 0x14d8768: r0 = Padding()
    //     0x14d8768: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d876c: mov             x1, x0
    // 0x14d8770: r0 = Instance_EdgeInsets
    //     0x14d8770: add             x0, PP, #0x27, lsl #12  ; [pp+0x27868] Obj!EdgeInsets@d57081
    //     0x14d8774: ldr             x0, [x0, #0x868]
    // 0x14d8778: stur            x1, [fp, #-0x48]
    // 0x14d877c: StoreField: r1->field_f = r0
    //     0x14d877c: stur            w0, [x1, #0xf]
    // 0x14d8780: ldur            x0, [fp, #-0x38]
    // 0x14d8784: StoreField: r1->field_b = r0
    //     0x14d8784: stur            w0, [x1, #0xb]
    // 0x14d8788: r0 = SvgPicture()
    //     0x14d8788: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x14d878c: mov             x1, x0
    // 0x14d8790: r2 = "assets/images/popularity_icon.svg"
    //     0x14d8790: add             x2, PP, #0x40, lsl #12  ; [pp+0x40bc8] "assets/images/popularity_icon.svg"
    //     0x14d8794: ldr             x2, [x2, #0xbc8]
    // 0x14d8798: stur            x0, [fp, #-0x38]
    // 0x14d879c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14d879c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14d87a0: r0 = SvgPicture.asset()
    //     0x14d87a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x14d87a4: ldur            x2, [fp, #-8]
    // 0x14d87a8: LoadField: r1 = r2->field_f
    //     0x14d87a8: ldur            w1, [x2, #0xf]
    // 0x14d87ac: DecompressPointer r1
    //     0x14d87ac: add             x1, x1, HEAP, lsl #32
    // 0x14d87b0: r0 = controller()
    //     0x14d87b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d87b4: LoadField: r1 = r0->field_77
    //     0x14d87b4: ldur            w1, [x0, #0x77]
    // 0x14d87b8: DecompressPointer r1
    //     0x14d87b8: add             x1, x1, HEAP, lsl #32
    // 0x14d87bc: r0 = value()
    //     0x14d87bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d87c0: ldur            x2, [fp, #-8]
    // 0x14d87c4: stur            x0, [fp, #-0x50]
    // 0x14d87c8: LoadField: r1 = r2->field_f
    //     0x14d87c8: ldur            w1, [x2, #0xf]
    // 0x14d87cc: DecompressPointer r1
    //     0x14d87cc: add             x1, x1, HEAP, lsl #32
    // 0x14d87d0: r0 = controller()
    //     0x14d87d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d87d4: LoadField: r1 = r0->field_67
    //     0x14d87d4: ldur            w1, [x0, #0x67]
    // 0x14d87d8: DecompressPointer r1
    //     0x14d87d8: add             x1, x1, HEAP, lsl #32
    // 0x14d87dc: r0 = value()
    //     0x14d87dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d87e0: LoadField: r1 = r0->field_b
    //     0x14d87e0: ldur            w1, [x0, #0xb]
    // 0x14d87e4: DecompressPointer r1
    //     0x14d87e4: add             x1, x1, HEAP, lsl #32
    // 0x14d87e8: cmp             w1, NULL
    // 0x14d87ec: b.ne            #0x14d87f8
    // 0x14d87f0: r7 = Null
    //     0x14d87f0: mov             x7, NULL
    // 0x14d87f4: b               #0x14d8850
    // 0x14d87f8: LoadField: r0 = r1->field_13
    //     0x14d87f8: ldur            w0, [x1, #0x13]
    // 0x14d87fc: DecompressPointer r0
    //     0x14d87fc: add             x0, x0, HEAP, lsl #32
    // 0x14d8800: stur            x0, [fp, #-0x58]
    // 0x14d8804: cmp             w0, NULL
    // 0x14d8808: b.ne            #0x14d8814
    // 0x14d880c: r0 = Null
    //     0x14d880c: mov             x0, NULL
    // 0x14d8810: b               #0x14d884c
    // 0x14d8814: ldur            x2, [fp, #-8]
    // 0x14d8818: r1 = Function '<anonymous closure>':.
    //     0x14d8818: add             x1, PP, #0x40, lsl #12  ; [pp+0x40bd0] AnonymousClosure: (0x13ba140), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x14d881c: ldr             x1, [x1, #0xbd0]
    // 0x14d8820: r0 = AllocateClosure()
    //     0x14d8820: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8824: r16 = <DropdownMenuItem<Filter>>
    //     0x14d8824: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c0c8] TypeArguments: <DropdownMenuItem<Filter>>
    //     0x14d8828: ldr             x16, [x16, #0xc8]
    // 0x14d882c: ldur            lr, [fp, #-0x58]
    // 0x14d8830: stp             lr, x16, [SP, #8]
    // 0x14d8834: str             x0, [SP]
    // 0x14d8838: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14d8838: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14d883c: r0 = map()
    //     0x14d883c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x14d8840: mov             x1, x0
    // 0x14d8844: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d8844: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d8848: r0 = toList()
    //     0x14d8848: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0x14d884c: mov             x7, x0
    // 0x14d8850: ldur            x0, [fp, #-8]
    // 0x14d8854: ldur            x6, [fp, #-0x30]
    // 0x14d8858: ldur            x5, [fp, #-0x40]
    // 0x14d885c: ldur            x4, [fp, #-0x48]
    // 0x14d8860: ldur            x3, [fp, #-0x38]
    // 0x14d8864: ldur            d0, [fp, #-0x78]
    // 0x14d8868: mov             x2, x0
    // 0x14d886c: stur            x7, [fp, #-0x58]
    // 0x14d8870: r1 = Function '<anonymous closure>':.
    //     0x14d8870: add             x1, PP, #0x40, lsl #12  ; [pp+0x40bd8] AnonymousClosure: (0x14da350), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d8874: ldr             x1, [x1, #0xbd8]
    // 0x14d8878: r0 = AllocateClosure()
    //     0x14d8878: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d887c: r1 = <Filter>
    //     0x14d887c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bd10] TypeArguments: <Filter>
    //     0x14d8880: ldr             x1, [x1, #0xd10]
    // 0x14d8884: stur            x0, [fp, #-0x60]
    // 0x14d8888: r0 = DropdownButton()
    //     0x14d8888: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0x14d888c: stur            x0, [fp, #-0x68]
    // 0x14d8890: r16 = 0.000000
    //     0x14d8890: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14d8894: str             x16, [SP]
    // 0x14d8898: mov             x1, x0
    // 0x14d889c: ldur            x2, [fp, #-0x58]
    // 0x14d88a0: ldur            x3, [fp, #-0x60]
    // 0x14d88a4: ldur            x5, [fp, #-0x50]
    // 0x14d88a8: r4 = const [0, 0x5, 0x1, 0x4, iconSize, 0x4, null]
    //     0x14d88a8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c0d8] List(7) [0, 0x5, 0x1, 0x4, "iconSize", 0x4, Null]
    //     0x14d88ac: ldr             x4, [x4, #0xd8]
    // 0x14d88b0: r0 = DropdownButton()
    //     0x14d88b0: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0x14d88b4: r0 = DropdownButtonHideUnderline()
    //     0x14d88b4: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0x14d88b8: mov             x2, x0
    // 0x14d88bc: ldur            x0, [fp, #-0x68]
    // 0x14d88c0: stur            x2, [fp, #-0x50]
    // 0x14d88c4: StoreField: r2->field_b = r0
    //     0x14d88c4: stur            w0, [x2, #0xb]
    // 0x14d88c8: r1 = <FlexParentData>
    //     0x14d88c8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d88cc: ldr             x1, [x1, #0xe00]
    // 0x14d88d0: r0 = Flexible()
    //     0x14d88d0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14d88d4: mov             x3, x0
    // 0x14d88d8: r0 = 1
    //     0x14d88d8: movz            x0, #0x1
    // 0x14d88dc: stur            x3, [fp, #-0x58]
    // 0x14d88e0: StoreField: r3->field_13 = r0
    //     0x14d88e0: stur            x0, [x3, #0x13]
    // 0x14d88e4: r4 = Instance_FlexFit
    //     0x14d88e4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14d88e8: ldr             x4, [x4, #0xe20]
    // 0x14d88ec: StoreField: r3->field_1b = r4
    //     0x14d88ec: stur            w4, [x3, #0x1b]
    // 0x14d88f0: ldur            x1, [fp, #-0x50]
    // 0x14d88f4: StoreField: r3->field_b = r1
    //     0x14d88f4: stur            w1, [x3, #0xb]
    // 0x14d88f8: r1 = Null
    //     0x14d88f8: mov             x1, NULL
    // 0x14d88fc: r2 = 10
    //     0x14d88fc: movz            x2, #0xa
    // 0x14d8900: r0 = AllocateArray()
    //     0x14d8900: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d8904: mov             x2, x0
    // 0x14d8908: ldur            x0, [fp, #-0x40]
    // 0x14d890c: stur            x2, [fp, #-0x50]
    // 0x14d8910: StoreField: r2->field_f = r0
    //     0x14d8910: stur            w0, [x2, #0xf]
    // 0x14d8914: ldur            x0, [fp, #-0x48]
    // 0x14d8918: StoreField: r2->field_13 = r0
    //     0x14d8918: stur            w0, [x2, #0x13]
    // 0x14d891c: r16 = Instance_SizedBox
    //     0x14d891c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0x14d8920: ldr             x16, [x16, #0x940]
    // 0x14d8924: ArrayStore: r2[0] = r16  ; List_4
    //     0x14d8924: stur            w16, [x2, #0x17]
    // 0x14d8928: ldur            x0, [fp, #-0x38]
    // 0x14d892c: StoreField: r2->field_1b = r0
    //     0x14d892c: stur            w0, [x2, #0x1b]
    // 0x14d8930: ldur            x0, [fp, #-0x58]
    // 0x14d8934: StoreField: r2->field_1f = r0
    //     0x14d8934: stur            w0, [x2, #0x1f]
    // 0x14d8938: r1 = <Widget>
    //     0x14d8938: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d893c: r0 = AllocateGrowableArray()
    //     0x14d893c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d8940: mov             x1, x0
    // 0x14d8944: ldur            x0, [fp, #-0x50]
    // 0x14d8948: stur            x1, [fp, #-0x38]
    // 0x14d894c: StoreField: r1->field_f = r0
    //     0x14d894c: stur            w0, [x1, #0xf]
    // 0x14d8950: r0 = 10
    //     0x14d8950: movz            x0, #0xa
    // 0x14d8954: StoreField: r1->field_b = r0
    //     0x14d8954: stur            w0, [x1, #0xb]
    // 0x14d8958: r0 = Row()
    //     0x14d8958: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14d895c: mov             x1, x0
    // 0x14d8960: r0 = Instance_Axis
    //     0x14d8960: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14d8964: stur            x1, [fp, #-0x40]
    // 0x14d8968: StoreField: r1->field_f = r0
    //     0x14d8968: stur            w0, [x1, #0xf]
    // 0x14d896c: r0 = Instance_MainAxisAlignment
    //     0x14d896c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x14d8970: ldr             x0, [x0, #0xd10]
    // 0x14d8974: StoreField: r1->field_13 = r0
    //     0x14d8974: stur            w0, [x1, #0x13]
    // 0x14d8978: r0 = Instance_MainAxisSize
    //     0x14d8978: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14d897c: ldr             x0, [x0, #0xdd0]
    // 0x14d8980: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d8980: stur            w0, [x1, #0x17]
    // 0x14d8984: r2 = Instance_CrossAxisAlignment
    //     0x14d8984: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d8988: ldr             x2, [x2, #0xa18]
    // 0x14d898c: StoreField: r1->field_1b = r2
    //     0x14d898c: stur            w2, [x1, #0x1b]
    // 0x14d8990: r3 = Instance_VerticalDirection
    //     0x14d8990: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d8994: ldr             x3, [x3, #0xa20]
    // 0x14d8998: StoreField: r1->field_23 = r3
    //     0x14d8998: stur            w3, [x1, #0x23]
    // 0x14d899c: r4 = Instance_Clip
    //     0x14d899c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d89a0: ldr             x4, [x4, #0x38]
    // 0x14d89a4: StoreField: r1->field_2b = r4
    //     0x14d89a4: stur            w4, [x1, #0x2b]
    // 0x14d89a8: StoreField: r1->field_2f = rZR
    //     0x14d89a8: stur            xzr, [x1, #0x2f]
    // 0x14d89ac: ldur            x5, [fp, #-0x38]
    // 0x14d89b0: StoreField: r1->field_b = r5
    //     0x14d89b0: stur            w5, [x1, #0xb]
    // 0x14d89b4: r0 = IntrinsicHeight()
    //     0x14d89b4: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0x14d89b8: mov             x1, x0
    // 0x14d89bc: ldur            x0, [fp, #-0x40]
    // 0x14d89c0: stur            x1, [fp, #-0x38]
    // 0x14d89c4: StoreField: r1->field_b = r0
    //     0x14d89c4: stur            w0, [x1, #0xb]
    // 0x14d89c8: r0 = Card()
    //     0x14d89c8: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14d89cc: mov             x1, x0
    // 0x14d89d0: r0 = 0.000000
    //     0x14d89d0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x14d89d4: stur            x1, [fp, #-0x40]
    // 0x14d89d8: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d89d8: stur            w0, [x1, #0x17]
    // 0x14d89dc: ldur            x0, [fp, #-0x30]
    // 0x14d89e0: StoreField: r1->field_1b = r0
    //     0x14d89e0: stur            w0, [x1, #0x1b]
    // 0x14d89e4: r0 = true
    //     0x14d89e4: add             x0, NULL, #0x20  ; true
    // 0x14d89e8: StoreField: r1->field_1f = r0
    //     0x14d89e8: stur            w0, [x1, #0x1f]
    // 0x14d89ec: ldur            x2, [fp, #-0x38]
    // 0x14d89f0: StoreField: r1->field_2f = r2
    //     0x14d89f0: stur            w2, [x1, #0x2f]
    // 0x14d89f4: StoreField: r1->field_2b = r0
    //     0x14d89f4: stur            w0, [x1, #0x2b]
    // 0x14d89f8: r2 = Instance__CardVariant
    //     0x14d89f8: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14d89fc: ldr             x2, [x2, #0xa68]
    // 0x14d8a00: StoreField: r1->field_33 = r2
    //     0x14d8a00: stur            w2, [x1, #0x33]
    // 0x14d8a04: ldur            d0, [fp, #-0x78]
    // 0x14d8a08: r2 = inline_Allocate_Double()
    //     0x14d8a08: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x14d8a0c: add             x2, x2, #0x10
    //     0x14d8a10: cmp             x3, x2
    //     0x14d8a14: b.ls            #0x14d92f4
    //     0x14d8a18: str             x2, [THR, #0x50]  ; THR::top
    //     0x14d8a1c: sub             x2, x2, #0xf
    //     0x14d8a20: movz            x3, #0xe15c
    //     0x14d8a24: movk            x3, #0x3, lsl #16
    //     0x14d8a28: stur            x3, [x2, #-1]
    // 0x14d8a2c: StoreField: r2->field_7 = d0
    //     0x14d8a2c: stur            d0, [x2, #7]
    // 0x14d8a30: stur            x2, [fp, #-0x30]
    // 0x14d8a34: r0 = SizedBox()
    //     0x14d8a34: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14d8a38: mov             x1, x0
    // 0x14d8a3c: ldur            x0, [fp, #-0x30]
    // 0x14d8a40: stur            x1, [fp, #-0x38]
    // 0x14d8a44: StoreField: r1->field_f = r0
    //     0x14d8a44: stur            w0, [x1, #0xf]
    // 0x14d8a48: r0 = 48.000000
    //     0x14d8a48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x14d8a4c: ldr             x0, [x0, #0xad8]
    // 0x14d8a50: StoreField: r1->field_13 = r0
    //     0x14d8a50: stur            w0, [x1, #0x13]
    // 0x14d8a54: ldur            x0, [fp, #-0x40]
    // 0x14d8a58: StoreField: r1->field_b = r0
    //     0x14d8a58: stur            w0, [x1, #0xb]
    // 0x14d8a5c: r0 = Padding()
    //     0x14d8a5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d8a60: mov             x2, x0
    // 0x14d8a64: r0 = Instance_EdgeInsets
    //     0x14d8a64: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be0] Obj!EdgeInsets@d57da1
    //     0x14d8a68: ldr             x0, [x0, #0xbe0]
    // 0x14d8a6c: stur            x2, [fp, #-0x30]
    // 0x14d8a70: StoreField: r2->field_f = r0
    //     0x14d8a70: stur            w0, [x2, #0xf]
    // 0x14d8a74: ldur            x0, [fp, #-0x38]
    // 0x14d8a78: StoreField: r2->field_b = r0
    //     0x14d8a78: stur            w0, [x2, #0xb]
    // 0x14d8a7c: r1 = <FlexParentData>
    //     0x14d8a7c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d8a80: ldr             x1, [x1, #0xe00]
    // 0x14d8a84: r0 = Flexible()
    //     0x14d8a84: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14d8a88: mov             x2, x0
    // 0x14d8a8c: r0 = 1
    //     0x14d8a8c: movz            x0, #0x1
    // 0x14d8a90: stur            x2, [fp, #-0x38]
    // 0x14d8a94: StoreField: r2->field_13 = r0
    //     0x14d8a94: stur            x0, [x2, #0x13]
    // 0x14d8a98: r3 = Instance_FlexFit
    //     0x14d8a98: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14d8a9c: ldr             x3, [x3, #0xe20]
    // 0x14d8aa0: StoreField: r2->field_1b = r3
    //     0x14d8aa0: stur            w3, [x2, #0x1b]
    // 0x14d8aa4: ldur            x1, [fp, #-0x30]
    // 0x14d8aa8: StoreField: r2->field_b = r1
    //     0x14d8aa8: stur            w1, [x2, #0xb]
    // 0x14d8aac: ldur            x4, [fp, #-8]
    // 0x14d8ab0: LoadField: r1 = r4->field_f
    //     0x14d8ab0: ldur            w1, [x4, #0xf]
    // 0x14d8ab4: DecompressPointer r1
    //     0x14d8ab4: add             x1, x1, HEAP, lsl #32
    // 0x14d8ab8: r0 = controller()
    //     0x14d8ab8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8abc: LoadField: r1 = r0->field_5f
    //     0x14d8abc: ldur            w1, [x0, #0x5f]
    // 0x14d8ac0: DecompressPointer r1
    //     0x14d8ac0: add             x1, x1, HEAP, lsl #32
    // 0x14d8ac4: r0 = value()
    //     0x14d8ac4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8ac8: LoadField: r1 = r0->field_7
    //     0x14d8ac8: ldur            w1, [x0, #7]
    // 0x14d8acc: DecompressPointer r1
    //     0x14d8acc: add             x1, x1, HEAP, lsl #32
    // 0x14d8ad0: cmp             w1, NULL
    // 0x14d8ad4: b.ne            #0x14d8ae0
    // 0x14d8ad8: r0 = Null
    //     0x14d8ad8: mov             x0, NULL
    // 0x14d8adc: b               #0x14d8af8
    // 0x14d8ae0: LoadField: r0 = r1->field_b
    //     0x14d8ae0: ldur            w0, [x1, #0xb]
    // 0x14d8ae4: cbnz            w0, #0x14d8af0
    // 0x14d8ae8: r1 = false
    //     0x14d8ae8: add             x1, NULL, #0x30  ; false
    // 0x14d8aec: b               #0x14d8af4
    // 0x14d8af0: r1 = true
    //     0x14d8af0: add             x1, NULL, #0x20  ; true
    // 0x14d8af4: mov             x0, x1
    // 0x14d8af8: cmp             w0, NULL
    // 0x14d8afc: b.ne            #0x14d8b04
    // 0x14d8b00: r0 = false
    //     0x14d8b00: add             x0, NULL, #0x30  ; false
    // 0x14d8b04: ldur            x2, [fp, #-8]
    // 0x14d8b08: stur            x0, [fp, #-0x30]
    // 0x14d8b0c: LoadField: r1 = r2->field_13
    //     0x14d8b0c: ldur            w1, [x2, #0x13]
    // 0x14d8b10: DecompressPointer r1
    //     0x14d8b10: add             x1, x1, HEAP, lsl #32
    // 0x14d8b14: r0 = of()
    //     0x14d8b14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d8b18: LoadField: r1 = r0->field_87
    //     0x14d8b18: ldur            w1, [x0, #0x87]
    // 0x14d8b1c: DecompressPointer r1
    //     0x14d8b1c: add             x1, x1, HEAP, lsl #32
    // 0x14d8b20: LoadField: r0 = r1->field_2b
    //     0x14d8b20: ldur            w0, [x1, #0x2b]
    // 0x14d8b24: DecompressPointer r0
    //     0x14d8b24: add             x0, x0, HEAP, lsl #32
    // 0x14d8b28: stur            x0, [fp, #-0x40]
    // 0x14d8b2c: r1 = Instance_Color
    //     0x14d8b2c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d8b30: d0 = 0.700000
    //     0x14d8b30: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14d8b34: ldr             d0, [x17, #0xf48]
    // 0x14d8b38: r0 = withOpacity()
    //     0x14d8b38: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d8b3c: r16 = 12.000000
    //     0x14d8b3c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14d8b40: ldr             x16, [x16, #0x9e8]
    // 0x14d8b44: stp             x0, x16, [SP]
    // 0x14d8b48: ldur            x1, [fp, #-0x40]
    // 0x14d8b4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14d8b4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14d8b50: ldr             x4, [x4, #0xaa0]
    // 0x14d8b54: r0 = copyWith()
    //     0x14d8b54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d8b58: stur            x0, [fp, #-0x40]
    // 0x14d8b5c: r0 = FlexiChipStyle()
    //     0x14d8b5c: bl              #0x808284  ; AllocateFlexiChipStyleStub -> FlexiChipStyle (size=0x8c)
    // 0x14d8b60: mov             x1, x0
    // 0x14d8b64: ldur            x0, [fp, #-0x40]
    // 0x14d8b68: StoreField: r1->field_23 = r0
    //     0x14d8b68: stur            w0, [x1, #0x23]
    // 0x14d8b6c: r0 = Instance_Color
    //     0x14d8b6c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14d8b70: StoreField: r1->field_37 = r0
    //     0x14d8b70: stur            w0, [x1, #0x37]
    // 0x14d8b74: r0 = Instance_BorderRadius
    //     0x14d8b74: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0x14d8b78: ldr             x0, [x0, #0xbe8]
    // 0x14d8b7c: StoreField: r1->field_53 = r0
    //     0x14d8b7c: stur            w0, [x1, #0x53]
    // 0x14d8b80: mov             x2, x1
    // 0x14d8b84: r1 = Null
    //     0x14d8b84: mov             x1, NULL
    // 0x14d8b88: r0 = FlexiChipStyle.filled()
    //     0x14d8b88: bl              #0x13af730  ; [package:flexi_chip/src/style.dart] FlexiChipStyle::FlexiChipStyle.filled
    // 0x14d8b8c: ldur            x2, [fp, #-8]
    // 0x14d8b90: stur            x0, [fp, #-0x40]
    // 0x14d8b94: LoadField: r1 = r2->field_f
    //     0x14d8b94: ldur            w1, [x2, #0xf]
    // 0x14d8b98: DecompressPointer r1
    //     0x14d8b98: add             x1, x1, HEAP, lsl #32
    // 0x14d8b9c: r0 = controller()
    //     0x14d8b9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8ba0: LoadField: r1 = r0->field_5f
    //     0x14d8ba0: ldur            w1, [x0, #0x5f]
    // 0x14d8ba4: DecompressPointer r1
    //     0x14d8ba4: add             x1, x1, HEAP, lsl #32
    // 0x14d8ba8: r0 = value()
    //     0x14d8ba8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8bac: LoadField: r1 = r0->field_7
    //     0x14d8bac: ldur            w1, [x0, #7]
    // 0x14d8bb0: DecompressPointer r1
    //     0x14d8bb0: add             x1, x1, HEAP, lsl #32
    // 0x14d8bb4: cmp             w1, NULL
    // 0x14d8bb8: b.ne            #0x14d8bfc
    // 0x14d8bbc: r0 = 2
    //     0x14d8bbc: movz            x0, #0x2
    // 0x14d8bc0: mov             x2, x0
    // 0x14d8bc4: r1 = Null
    //     0x14d8bc4: mov             x1, NULL
    // 0x14d8bc8: r0 = AllocateArray()
    //     0x14d8bc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d8bcc: stur            x0, [fp, #-0x48]
    // 0x14d8bd0: r16 = ""
    //     0x14d8bd0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d8bd4: StoreField: r0->field_f = r16
    //     0x14d8bd4: stur            w16, [x0, #0xf]
    // 0x14d8bd8: r1 = <String?>
    //     0x14d8bd8: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x14d8bdc: r0 = AllocateGrowableArray()
    //     0x14d8bdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d8be0: mov             x1, x0
    // 0x14d8be4: ldur            x0, [fp, #-0x48]
    // 0x14d8be8: StoreField: r1->field_f = r0
    //     0x14d8be8: stur            w0, [x1, #0xf]
    // 0x14d8bec: r2 = 2
    //     0x14d8bec: movz            x2, #0x2
    // 0x14d8bf0: StoreField: r1->field_b = r2
    //     0x14d8bf0: stur            w2, [x1, #0xb]
    // 0x14d8bf4: mov             x6, x1
    // 0x14d8bf8: b               #0x14d8c30
    // 0x14d8bfc: ldur            x0, [fp, #-8]
    // 0x14d8c00: r2 = 2
    //     0x14d8c00: movz            x2, #0x2
    // 0x14d8c04: LoadField: r1 = r0->field_f
    //     0x14d8c04: ldur            w1, [x0, #0xf]
    // 0x14d8c08: DecompressPointer r1
    //     0x14d8c08: add             x1, x1, HEAP, lsl #32
    // 0x14d8c0c: r0 = controller()
    //     0x14d8c0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8c10: LoadField: r1 = r0->field_5f
    //     0x14d8c10: ldur            w1, [x0, #0x5f]
    // 0x14d8c14: DecompressPointer r1
    //     0x14d8c14: add             x1, x1, HEAP, lsl #32
    // 0x14d8c18: r0 = value()
    //     0x14d8c18: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8c1c: LoadField: r1 = r0->field_7
    //     0x14d8c1c: ldur            w1, [x0, #7]
    // 0x14d8c20: DecompressPointer r1
    //     0x14d8c20: add             x1, x1, HEAP, lsl #32
    // 0x14d8c24: cmp             w1, NULL
    // 0x14d8c28: b.eq            #0x14d9310
    // 0x14d8c2c: mov             x6, x1
    // 0x14d8c30: ldur            x2, [fp, #-8]
    // 0x14d8c34: stur            x6, [fp, #-0x48]
    // 0x14d8c38: LoadField: r1 = r2->field_f
    //     0x14d8c38: ldur            w1, [x2, #0xf]
    // 0x14d8c3c: DecompressPointer r1
    //     0x14d8c3c: add             x1, x1, HEAP, lsl #32
    // 0x14d8c40: r0 = controller()
    //     0x14d8c40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8c44: LoadField: r1 = r0->field_5f
    //     0x14d8c44: ldur            w1, [x0, #0x5f]
    // 0x14d8c48: DecompressPointer r1
    //     0x14d8c48: add             x1, x1, HEAP, lsl #32
    // 0x14d8c4c: r0 = value()
    //     0x14d8c4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8c50: LoadField: r1 = r0->field_7
    //     0x14d8c50: ldur            w1, [x0, #7]
    // 0x14d8c54: DecompressPointer r1
    //     0x14d8c54: add             x1, x1, HEAP, lsl #32
    // 0x14d8c58: cmp             w1, NULL
    // 0x14d8c5c: b.ne            #0x14d8ca0
    // 0x14d8c60: r0 = 2
    //     0x14d8c60: movz            x0, #0x2
    // 0x14d8c64: mov             x2, x0
    // 0x14d8c68: r1 = Null
    //     0x14d8c68: mov             x1, NULL
    // 0x14d8c6c: r0 = AllocateArray()
    //     0x14d8c6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d8c70: stur            x0, [fp, #-0x50]
    // 0x14d8c74: r16 = ""
    //     0x14d8c74: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d8c78: StoreField: r0->field_f = r16
    //     0x14d8c78: stur            w16, [x0, #0xf]
    // 0x14d8c7c: r1 = <String>
    //     0x14d8c7c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14d8c80: r0 = AllocateGrowableArray()
    //     0x14d8c80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d8c84: mov             x1, x0
    // 0x14d8c88: ldur            x0, [fp, #-0x50]
    // 0x14d8c8c: StoreField: r1->field_f = r0
    //     0x14d8c8c: stur            w0, [x1, #0xf]
    // 0x14d8c90: r2 = 2
    //     0x14d8c90: movz            x2, #0x2
    // 0x14d8c94: StoreField: r1->field_b = r2
    //     0x14d8c94: stur            w2, [x1, #0xb]
    // 0x14d8c98: mov             x9, x1
    // 0x14d8c9c: b               #0x14d8d18
    // 0x14d8ca0: ldur            x0, [fp, #-8]
    // 0x14d8ca4: r2 = 2
    //     0x14d8ca4: movz            x2, #0x2
    // 0x14d8ca8: LoadField: r1 = r0->field_f
    //     0x14d8ca8: ldur            w1, [x0, #0xf]
    // 0x14d8cac: DecompressPointer r1
    //     0x14d8cac: add             x1, x1, HEAP, lsl #32
    // 0x14d8cb0: r0 = controller()
    //     0x14d8cb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8cb4: LoadField: r1 = r0->field_5f
    //     0x14d8cb4: ldur            w1, [x0, #0x5f]
    // 0x14d8cb8: DecompressPointer r1
    //     0x14d8cb8: add             x1, x1, HEAP, lsl #32
    // 0x14d8cbc: r0 = value()
    //     0x14d8cbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8cc0: LoadField: r1 = r0->field_7
    //     0x14d8cc0: ldur            w1, [x0, #7]
    // 0x14d8cc4: DecompressPointer r1
    //     0x14d8cc4: add             x1, x1, HEAP, lsl #32
    // 0x14d8cc8: cmp             w1, NULL
    // 0x14d8ccc: b.ne            #0x14d8d10
    // 0x14d8cd0: r0 = 2
    //     0x14d8cd0: movz            x0, #0x2
    // 0x14d8cd4: mov             x2, x0
    // 0x14d8cd8: r1 = Null
    //     0x14d8cd8: mov             x1, NULL
    // 0x14d8cdc: r0 = AllocateArray()
    //     0x14d8cdc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d8ce0: stur            x0, [fp, #-0x50]
    // 0x14d8ce4: r16 = ""
    //     0x14d8ce4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d8ce8: StoreField: r0->field_f = r16
    //     0x14d8ce8: stur            w16, [x0, #0xf]
    // 0x14d8cec: r1 = <String>
    //     0x14d8cec: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x14d8cf0: r0 = AllocateGrowableArray()
    //     0x14d8cf0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d8cf4: mov             x1, x0
    // 0x14d8cf8: ldur            x0, [fp, #-0x50]
    // 0x14d8cfc: StoreField: r1->field_f = r0
    //     0x14d8cfc: stur            w0, [x1, #0xf]
    // 0x14d8d00: r0 = 2
    //     0x14d8d00: movz            x0, #0x2
    // 0x14d8d04: StoreField: r1->field_b = r0
    //     0x14d8d04: stur            w0, [x1, #0xb]
    // 0x14d8d08: mov             x0, x1
    // 0x14d8d0c: b               #0x14d8d14
    // 0x14d8d10: mov             x0, x1
    // 0x14d8d14: mov             x9, x0
    // 0x14d8d18: ldur            x0, [fp, #-8]
    // 0x14d8d1c: ldur            x8, [fp, #-0x10]
    // 0x14d8d20: ldur            x7, [fp, #-0x18]
    // 0x14d8d24: ldur            x6, [fp, #-0x20]
    // 0x14d8d28: ldur            x5, [fp, #-0x28]
    // 0x14d8d2c: ldur            x4, [fp, #-0x38]
    // 0x14d8d30: ldur            x3, [fp, #-0x30]
    // 0x14d8d34: stur            x9, [fp, #-0x50]
    // 0x14d8d38: r1 = Function '<anonymous closure>':.
    //     0x14d8d38: add             x1, PP, #0x40, lsl #12  ; [pp+0x40bf0] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x14d8d3c: ldr             x1, [x1, #0xbf0]
    // 0x14d8d40: r2 = Null
    //     0x14d8d40: mov             x2, NULL
    // 0x14d8d44: r0 = AllocateClosure()
    //     0x14d8d44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8d48: r1 = Function '<anonymous closure>':.
    //     0x14d8d48: add             x1, PP, #0x40, lsl #12  ; [pp+0x40bf8] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x14d8d4c: ldr             x1, [x1, #0xbf8]
    // 0x14d8d50: r2 = Null
    //     0x14d8d50: mov             x2, NULL
    // 0x14d8d54: stur            x0, [fp, #-0x58]
    // 0x14d8d58: r0 = AllocateClosure()
    //     0x14d8d58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8d5c: r1 = Function '<anonymous closure>':.
    //     0x14d8d5c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c00] Function: [dart:core] _Closure::call (0x16f2738)
    //     0x14d8d60: ldr             x1, [x1, #0xc00]
    // 0x14d8d64: r2 = Null
    //     0x14d8d64: mov             x2, NULL
    // 0x14d8d68: stur            x0, [fp, #-0x60]
    // 0x14d8d6c: r0 = AllocateClosure()
    //     0x14d8d6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8d70: ldur            x2, [fp, #-8]
    // 0x14d8d74: r1 = Function '<anonymous closure>':.
    //     0x14d8d74: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c08] AnonymousClosure: (0x14d979c), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d8d78: ldr             x1, [x1, #0xc08]
    // 0x14d8d7c: stur            x0, [fp, #-0x68]
    // 0x14d8d80: r0 = AllocateClosure()
    //     0x14d8d80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8d84: r16 = <String, String>
    //     0x14d8d84: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x14d8d88: ldr             x16, [x16, #0x788]
    // 0x14d8d8c: stp             x0, x16, [SP, #0x20]
    // 0x14d8d90: ldur            x16, [fp, #-0x60]
    // 0x14d8d94: ldur            lr, [fp, #-0x50]
    // 0x14d8d98: stp             lr, x16, [SP, #0x10]
    // 0x14d8d9c: ldur            x16, [fp, #-0x68]
    // 0x14d8da0: ldur            lr, [fp, #-0x58]
    // 0x14d8da4: stp             lr, x16, [SP]
    // 0x14d8da8: r4 = const [0x2, 0x5, 0x5, 0x5, null]
    //     0x14d8da8: ldr             x4, [PP, #0x4e8]  ; [pp+0x4e8] List(5) [0x2, 0x5, 0x5, 0x5, Null]
    // 0x14d8dac: r0 = listFrom()
    //     0x14d8dac: bl              #0x13af444  ; [package:chips_choice/src/choice.dart] C2Choice::listFrom
    // 0x14d8db0: r1 = Function '<anonymous closure>':.
    //     0x14d8db0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c10] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x14d8db4: ldr             x1, [x1, #0xc10]
    // 0x14d8db8: r2 = Null
    //     0x14d8db8: mov             x2, NULL
    // 0x14d8dbc: stur            x0, [fp, #-0x50]
    // 0x14d8dc0: r0 = AllocateClosure()
    //     0x14d8dc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8dc4: r1 = <String?>
    //     0x14d8dc4: ldr             x1, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x14d8dc8: stur            x0, [fp, #-0x58]
    // 0x14d8dcc: r0 = ChipsChoice()
    //     0x14d8dcc: bl              #0x13af438  ; AllocateChipsChoiceStub -> ChipsChoice<X0> (size=0xa8)
    // 0x14d8dd0: mov             x1, x0
    // 0x14d8dd4: ldur            x2, [fp, #-0x50]
    // 0x14d8dd8: ldur            x3, [fp, #-0x40]
    // 0x14d8ddc: ldur            x5, [fp, #-0x58]
    // 0x14d8de0: ldur            x6, [fp, #-0x48]
    // 0x14d8de4: stur            x0, [fp, #-0x40]
    // 0x14d8de8: r0 = ChipsChoice.multiple()
    //     0x14d8de8: bl              #0x13af328  ; [package:chips_choice/src/widget.dart] ChipsChoice::ChipsChoice.multiple
    // 0x14d8dec: r0 = Visibility()
    //     0x14d8dec: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x14d8df0: mov             x3, x0
    // 0x14d8df4: ldur            x0, [fp, #-0x40]
    // 0x14d8df8: stur            x3, [fp, #-0x48]
    // 0x14d8dfc: StoreField: r3->field_b = r0
    //     0x14d8dfc: stur            w0, [x3, #0xb]
    // 0x14d8e00: r0 = Instance_SizedBox
    //     0x14d8e00: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14d8e04: StoreField: r3->field_f = r0
    //     0x14d8e04: stur            w0, [x3, #0xf]
    // 0x14d8e08: ldur            x0, [fp, #-0x30]
    // 0x14d8e0c: StoreField: r3->field_13 = r0
    //     0x14d8e0c: stur            w0, [x3, #0x13]
    // 0x14d8e10: r0 = false
    //     0x14d8e10: add             x0, NULL, #0x30  ; false
    // 0x14d8e14: ArrayStore: r3[0] = r0  ; List_4
    //     0x14d8e14: stur            w0, [x3, #0x17]
    // 0x14d8e18: StoreField: r3->field_1b = r0
    //     0x14d8e18: stur            w0, [x3, #0x1b]
    // 0x14d8e1c: StoreField: r3->field_1f = r0
    //     0x14d8e1c: stur            w0, [x3, #0x1f]
    // 0x14d8e20: StoreField: r3->field_23 = r0
    //     0x14d8e20: stur            w0, [x3, #0x23]
    // 0x14d8e24: StoreField: r3->field_27 = r0
    //     0x14d8e24: stur            w0, [x3, #0x27]
    // 0x14d8e28: StoreField: r3->field_2b = r0
    //     0x14d8e28: stur            w0, [x3, #0x2b]
    // 0x14d8e2c: r1 = Null
    //     0x14d8e2c: mov             x1, NULL
    // 0x14d8e30: r2 = 12
    //     0x14d8e30: movz            x2, #0xc
    // 0x14d8e34: r0 = AllocateArray()
    //     0x14d8e34: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d8e38: mov             x2, x0
    // 0x14d8e3c: ldur            x0, [fp, #-0x10]
    // 0x14d8e40: stur            x2, [fp, #-0x30]
    // 0x14d8e44: StoreField: r2->field_f = r0
    //     0x14d8e44: stur            w0, [x2, #0xf]
    // 0x14d8e48: ldur            x0, [fp, #-0x18]
    // 0x14d8e4c: StoreField: r2->field_13 = r0
    //     0x14d8e4c: stur            w0, [x2, #0x13]
    // 0x14d8e50: ldur            x0, [fp, #-0x20]
    // 0x14d8e54: ArrayStore: r2[0] = r0  ; List_4
    //     0x14d8e54: stur            w0, [x2, #0x17]
    // 0x14d8e58: ldur            x0, [fp, #-0x28]
    // 0x14d8e5c: StoreField: r2->field_1b = r0
    //     0x14d8e5c: stur            w0, [x2, #0x1b]
    // 0x14d8e60: ldur            x0, [fp, #-0x38]
    // 0x14d8e64: StoreField: r2->field_1f = r0
    //     0x14d8e64: stur            w0, [x2, #0x1f]
    // 0x14d8e68: ldur            x0, [fp, #-0x48]
    // 0x14d8e6c: StoreField: r2->field_23 = r0
    //     0x14d8e6c: stur            w0, [x2, #0x23]
    // 0x14d8e70: r1 = <Widget>
    //     0x14d8e70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d8e74: r0 = AllocateGrowableArray()
    //     0x14d8e74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d8e78: mov             x2, x0
    // 0x14d8e7c: ldur            x0, [fp, #-0x30]
    // 0x14d8e80: stur            x2, [fp, #-0x10]
    // 0x14d8e84: StoreField: r2->field_f = r0
    //     0x14d8e84: stur            w0, [x2, #0xf]
    // 0x14d8e88: r0 = 12
    //     0x14d8e88: movz            x0, #0xc
    // 0x14d8e8c: StoreField: r2->field_b = r0
    //     0x14d8e8c: stur            w0, [x2, #0xb]
    // 0x14d8e90: ldur            x0, [fp, #-8]
    // 0x14d8e94: LoadField: r1 = r0->field_f
    //     0x14d8e94: ldur            w1, [x0, #0xf]
    // 0x14d8e98: DecompressPointer r1
    //     0x14d8e98: add             x1, x1, HEAP, lsl #32
    // 0x14d8e9c: r0 = controller()
    //     0x14d8e9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8ea0: LoadField: r1 = r0->field_5b
    //     0x14d8ea0: ldur            w1, [x0, #0x5b]
    // 0x14d8ea4: DecompressPointer r1
    //     0x14d8ea4: add             x1, x1, HEAP, lsl #32
    // 0x14d8ea8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d8ea8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d8eac: r0 = toList()
    //     0x14d8eac: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14d8eb0: LoadField: r1 = r0->field_b
    //     0x14d8eb0: ldur            w1, [x0, #0xb]
    // 0x14d8eb4: cbz             w1, #0x14d90f8
    // 0x14d8eb8: ldur            x2, [fp, #-8]
    // 0x14d8ebc: ldur            x0, [fp, #-0x10]
    // 0x14d8ec0: LoadField: r1 = r2->field_f
    //     0x14d8ec0: ldur            w1, [x2, #0xf]
    // 0x14d8ec4: DecompressPointer r1
    //     0x14d8ec4: add             x1, x1, HEAP, lsl #32
    // 0x14d8ec8: r0 = controller()
    //     0x14d8ec8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8ecc: LoadField: r1 = r0->field_5b
    //     0x14d8ecc: ldur            w1, [x0, #0x5b]
    // 0x14d8ed0: DecompressPointer r1
    //     0x14d8ed0: add             x1, x1, HEAP, lsl #32
    // 0x14d8ed4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d8ed4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d8ed8: r0 = toList()
    //     0x14d8ed8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14d8edc: ldur            x2, [fp, #-8]
    // 0x14d8ee0: stur            x0, [fp, #-0x18]
    // 0x14d8ee4: LoadField: r1 = r2->field_f
    //     0x14d8ee4: ldur            w1, [x2, #0xf]
    // 0x14d8ee8: DecompressPointer r1
    //     0x14d8ee8: add             x1, x1, HEAP, lsl #32
    // 0x14d8eec: r0 = controller()
    //     0x14d8eec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8ef0: LoadField: r2 = r0->field_d3
    //     0x14d8ef0: ldur            w2, [x0, #0xd3]
    // 0x14d8ef4: DecompressPointer r2
    //     0x14d8ef4: add             x2, x2, HEAP, lsl #32
    // 0x14d8ef8: ldur            x0, [fp, #-8]
    // 0x14d8efc: stur            x2, [fp, #-0x20]
    // 0x14d8f00: LoadField: r1 = r0->field_f
    //     0x14d8f00: ldur            w1, [x0, #0xf]
    // 0x14d8f04: DecompressPointer r1
    //     0x14d8f04: add             x1, x1, HEAP, lsl #32
    // 0x14d8f08: r0 = controller()
    //     0x14d8f08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8f0c: LoadField: r1 = r0->field_7f
    //     0x14d8f0c: ldur            w1, [x0, #0x7f]
    // 0x14d8f10: DecompressPointer r1
    //     0x14d8f10: add             x1, x1, HEAP, lsl #32
    // 0x14d8f14: r0 = value()
    //     0x14d8f14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8f18: stur            x0, [fp, #-0x28]
    // 0x14d8f1c: r0 = CustomStyle()
    //     0x14d8f1c: bl              #0x910168  ; AllocateCustomStyleStub -> CustomStyle (size=0x10)
    // 0x14d8f20: mov             x2, x0
    // 0x14d8f24: r0 = Instance_TitleAlignment
    //     0x14d8f24: add             x0, PP, #0x24, lsl #12  ; [pp+0x24510] Obj!TitleAlignment@d75601
    //     0x14d8f28: ldr             x0, [x0, #0x510]
    // 0x14d8f2c: stur            x2, [fp, #-0x30]
    // 0x14d8f30: StoreField: r2->field_7 = r0
    //     0x14d8f30: stur            w0, [x2, #7]
    // 0x14d8f34: ldur            x0, [fp, #-8]
    // 0x14d8f38: LoadField: r1 = r0->field_f
    //     0x14d8f38: ldur            w1, [x0, #0xf]
    // 0x14d8f3c: DecompressPointer r1
    //     0x14d8f3c: add             x1, x1, HEAP, lsl #32
    // 0x14d8f40: r0 = controller()
    //     0x14d8f40: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d8f44: LoadField: r1 = r0->field_8b
    //     0x14d8f44: ldur            w1, [x0, #0x8b]
    // 0x14d8f48: DecompressPointer r1
    //     0x14d8f48: add             x1, x1, HEAP, lsl #32
    // 0x14d8f4c: r0 = value()
    //     0x14d8f4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d8f50: stur            x0, [fp, #-0x38]
    // 0x14d8f54: r0 = ProductGridItemView()
    //     0x14d8f54: bl              #0xb9bf44  ; AllocateProductGridItemViewStub -> ProductGridItemView (size=0x5c)
    // 0x14d8f58: mov             x1, x0
    // 0x14d8f5c: ldur            x0, [fp, #-0x18]
    // 0x14d8f60: stur            x1, [fp, #-0x40]
    // 0x14d8f64: StoreField: r1->field_b = r0
    //     0x14d8f64: stur            w0, [x1, #0xb]
    // 0x14d8f68: r0 = ""
    //     0x14d8f68: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d8f6c: StoreField: r1->field_f = r0
    //     0x14d8f6c: stur            w0, [x1, #0xf]
    // 0x14d8f70: r0 = true
    //     0x14d8f70: add             x0, NULL, #0x20  ; true
    // 0x14d8f74: StoreField: r1->field_13 = r0
    //     0x14d8f74: stur            w0, [x1, #0x13]
    // 0x14d8f78: r0 = ViewAll()
    //     0x14d8f78: bl              #0x90ff98  ; AllocateViewAllStub -> ViewAll (size=0x10)
    // 0x14d8f7c: mov             x1, x0
    // 0x14d8f80: ldur            x0, [fp, #-0x40]
    // 0x14d8f84: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d8f84: stur            w1, [x0, #0x17]
    // 0x14d8f88: r3 = false
    //     0x14d8f88: add             x3, NULL, #0x30  ; false
    // 0x14d8f8c: StoreField: r0->field_1b = r3
    //     0x14d8f8c: stur            w3, [x0, #0x1b]
    // 0x14d8f90: ldur            x1, [fp, #-0x28]
    // 0x14d8f94: StoreField: r0->field_1f = r1
    //     0x14d8f94: stur            w1, [x0, #0x1f]
    // 0x14d8f98: ldur            x1, [fp, #-0x30]
    // 0x14d8f9c: StoreField: r0->field_23 = r1
    //     0x14d8f9c: stur            w1, [x0, #0x23]
    // 0x14d8fa0: r1 = "collection_page"
    //     0x14d8fa0: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0x14d8fa4: ldr             x1, [x1, #0x118]
    // 0x14d8fa8: StoreField: r0->field_27 = r1
    //     0x14d8fa8: stur            w1, [x0, #0x27]
    // 0x14d8fac: StoreField: r0->field_33 = r1
    //     0x14d8fac: stur            w1, [x0, #0x33]
    // 0x14d8fb0: StoreField: r0->field_37 = r1
    //     0x14d8fb0: stur            w1, [x0, #0x37]
    // 0x14d8fb4: ldur            x2, [fp, #-0x38]
    // 0x14d8fb8: StoreField: r0->field_43 = r2
    //     0x14d8fb8: stur            w2, [x0, #0x43]
    // 0x14d8fbc: StoreField: r0->field_3b = r1
    //     0x14d8fbc: stur            w1, [x0, #0x3b]
    // 0x14d8fc0: ldur            x2, [fp, #-8]
    // 0x14d8fc4: r1 = Function '<anonymous closure>':.
    //     0x14d8fc4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c18] AnonymousClosure: (0x13b3e3c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x14d8fc8: ldr             x1, [x1, #0xc18]
    // 0x14d8fcc: r0 = AllocateClosure()
    //     0x14d8fcc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8fd0: mov             x1, x0
    // 0x14d8fd4: ldur            x0, [fp, #-0x40]
    // 0x14d8fd8: StoreField: r0->field_47 = r1
    //     0x14d8fd8: stur            w1, [x0, #0x47]
    // 0x14d8fdc: ldur            x2, [fp, #-8]
    // 0x14d8fe0: r1 = Function '<anonymous closure>':.
    //     0x14d8fe0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c20] AnonymousClosure: (0x13b276c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d8fe4: ldr             x1, [x1, #0xc20]
    // 0x14d8fe8: r0 = AllocateClosure()
    //     0x14d8fe8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d8fec: mov             x1, x0
    // 0x14d8ff0: ldur            x0, [fp, #-0x40]
    // 0x14d8ff4: StoreField: r0->field_4f = r1
    //     0x14d8ff4: stur            w1, [x0, #0x4f]
    // 0x14d8ff8: ldur            x2, [fp, #-8]
    // 0x14d8ffc: r1 = Function '<anonymous closure>':.
    //     0x14d8ffc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c28] AnonymousClosure: (0x13b0b1c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d9000: ldr             x1, [x1, #0xc28]
    // 0x14d9004: r0 = AllocateClosure()
    //     0x14d9004: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d9008: mov             x1, x0
    // 0x14d900c: ldur            x0, [fp, #-0x40]
    // 0x14d9010: StoreField: r0->field_53 = r1
    //     0x14d9010: stur            w1, [x0, #0x53]
    // 0x14d9014: ldur            x1, [fp, #-0x20]
    // 0x14d9018: StoreField: r0->field_3f = r1
    //     0x14d9018: stur            w1, [x0, #0x3f]
    // 0x14d901c: ldur            x2, [fp, #-8]
    // 0x14d9020: r1 = Function '<anonymous closure>':.
    //     0x14d9020: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c30] AnonymousClosure: (0x14d961c), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d9024: ldr             x1, [x1, #0xc30]
    // 0x14d9028: r0 = AllocateClosure()
    //     0x14d9028: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d902c: mov             x1, x0
    // 0x14d9030: ldur            x0, [fp, #-0x40]
    // 0x14d9034: StoreField: r0->field_4b = r1
    //     0x14d9034: stur            w1, [x0, #0x4b]
    // 0x14d9038: ldur            x2, [fp, #-8]
    // 0x14d903c: r1 = Function '<anonymous closure>':.
    //     0x14d903c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c38] AnonymousClosure: (0x14d9314), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d9040: ldr             x1, [x1, #0xc38]
    // 0x14d9044: r0 = AllocateClosure()
    //     0x14d9044: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d9048: mov             x1, x0
    // 0x14d904c: ldur            x0, [fp, #-0x40]
    // 0x14d9050: StoreField: r0->field_57 = r1
    //     0x14d9050: stur            w1, [x0, #0x57]
    // 0x14d9054: r1 = <FlexParentData>
    //     0x14d9054: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d9058: ldr             x1, [x1, #0xe00]
    // 0x14d905c: r0 = Expanded()
    //     0x14d905c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14d9060: stur            x0, [fp, #-0x18]
    // 0x14d9064: StoreField: r0->field_13 = rZR
    //     0x14d9064: stur            xzr, [x0, #0x13]
    // 0x14d9068: r1 = Instance_FlexFit
    //     0x14d9068: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14d906c: ldr             x1, [x1, #0xe08]
    // 0x14d9070: StoreField: r0->field_1b = r1
    //     0x14d9070: stur            w1, [x0, #0x1b]
    // 0x14d9074: ldur            x1, [fp, #-0x40]
    // 0x14d9078: StoreField: r0->field_b = r1
    //     0x14d9078: stur            w1, [x0, #0xb]
    // 0x14d907c: ldur            x2, [fp, #-0x10]
    // 0x14d9080: LoadField: r1 = r2->field_b
    //     0x14d9080: ldur            w1, [x2, #0xb]
    // 0x14d9084: LoadField: r3 = r2->field_f
    //     0x14d9084: ldur            w3, [x2, #0xf]
    // 0x14d9088: DecompressPointer r3
    //     0x14d9088: add             x3, x3, HEAP, lsl #32
    // 0x14d908c: LoadField: r4 = r3->field_b
    //     0x14d908c: ldur            w4, [x3, #0xb]
    // 0x14d9090: r3 = LoadInt32Instr(r1)
    //     0x14d9090: sbfx            x3, x1, #1, #0x1f
    // 0x14d9094: stur            x3, [fp, #-0x70]
    // 0x14d9098: r1 = LoadInt32Instr(r4)
    //     0x14d9098: sbfx            x1, x4, #1, #0x1f
    // 0x14d909c: cmp             x3, x1
    // 0x14d90a0: b.ne            #0x14d90ac
    // 0x14d90a4: mov             x1, x2
    // 0x14d90a8: r0 = _growToNextCapacity()
    //     0x14d90a8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14d90ac: ldur            x2, [fp, #-0x10]
    // 0x14d90b0: ldur            x3, [fp, #-0x70]
    // 0x14d90b4: add             x0, x3, #1
    // 0x14d90b8: lsl             x1, x0, #1
    // 0x14d90bc: StoreField: r2->field_b = r1
    //     0x14d90bc: stur            w1, [x2, #0xb]
    // 0x14d90c0: LoadField: r1 = r2->field_f
    //     0x14d90c0: ldur            w1, [x2, #0xf]
    // 0x14d90c4: DecompressPointer r1
    //     0x14d90c4: add             x1, x1, HEAP, lsl #32
    // 0x14d90c8: ldur            x0, [fp, #-0x18]
    // 0x14d90cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14d90cc: add             x25, x1, x3, lsl #2
    //     0x14d90d0: add             x25, x25, #0xf
    //     0x14d90d4: str             w0, [x25]
    //     0x14d90d8: tbz             w0, #0, #0x14d90f4
    //     0x14d90dc: ldurb           w16, [x1, #-1]
    //     0x14d90e0: ldurb           w17, [x0, #-1]
    //     0x14d90e4: and             x16, x17, x16, lsr #2
    //     0x14d90e8: tst             x16, HEAP, lsr #32
    //     0x14d90ec: b.eq            #0x14d90f4
    //     0x14d90f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d90f4: b               #0x14d9248
    // 0x14d90f8: ldur            x0, [fp, #-8]
    // 0x14d90fc: ldur            x2, [fp, #-0x10]
    // 0x14d9100: LoadField: r1 = r0->field_13
    //     0x14d9100: ldur            w1, [x0, #0x13]
    // 0x14d9104: DecompressPointer r1
    //     0x14d9104: add             x1, x1, HEAP, lsl #32
    // 0x14d9108: r0 = of()
    //     0x14d9108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d910c: LoadField: r1 = r0->field_87
    //     0x14d910c: ldur            w1, [x0, #0x87]
    // 0x14d9110: DecompressPointer r1
    //     0x14d9110: add             x1, x1, HEAP, lsl #32
    // 0x14d9114: LoadField: r0 = r1->field_27
    //     0x14d9114: ldur            w0, [x1, #0x27]
    // 0x14d9118: DecompressPointer r0
    //     0x14d9118: add             x0, x0, HEAP, lsl #32
    // 0x14d911c: r16 = Instance_Color
    //     0x14d911c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d9120: r30 = 21.000000
    //     0x14d9120: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x14d9124: ldr             lr, [lr, #0x9b0]
    // 0x14d9128: stp             lr, x16, [SP]
    // 0x14d912c: mov             x1, x0
    // 0x14d9130: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d9130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d9134: ldr             x4, [x4, #0x9b8]
    // 0x14d9138: r0 = copyWith()
    //     0x14d9138: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d913c: stur            x0, [fp, #-8]
    // 0x14d9140: r0 = Text()
    //     0x14d9140: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d9144: mov             x1, x0
    // 0x14d9148: r0 = "No Products Found!"
    //     0x14d9148: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c148] "No Products Found!"
    //     0x14d914c: ldr             x0, [x0, #0x148]
    // 0x14d9150: stur            x1, [fp, #-0x18]
    // 0x14d9154: StoreField: r1->field_b = r0
    //     0x14d9154: stur            w0, [x1, #0xb]
    // 0x14d9158: ldur            x0, [fp, #-8]
    // 0x14d915c: StoreField: r1->field_13 = r0
    //     0x14d915c: stur            w0, [x1, #0x13]
    // 0x14d9160: r0 = Padding()
    //     0x14d9160: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d9164: mov             x1, x0
    // 0x14d9168: r0 = Instance_EdgeInsets
    //     0x14d9168: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c150] Obj!EdgeInsets@d59bd1
    //     0x14d916c: ldr             x0, [x0, #0x150]
    // 0x14d9170: stur            x1, [fp, #-8]
    // 0x14d9174: StoreField: r1->field_f = r0
    //     0x14d9174: stur            w0, [x1, #0xf]
    // 0x14d9178: ldur            x0, [fp, #-0x18]
    // 0x14d917c: StoreField: r1->field_b = r0
    //     0x14d917c: stur            w0, [x1, #0xb]
    // 0x14d9180: r0 = Align()
    //     0x14d9180: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14d9184: mov             x2, x0
    // 0x14d9188: r0 = Instance_Alignment
    //     0x14d9188: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14d918c: ldr             x0, [x0, #0xb10]
    // 0x14d9190: stur            x2, [fp, #-0x18]
    // 0x14d9194: StoreField: r2->field_f = r0
    //     0x14d9194: stur            w0, [x2, #0xf]
    // 0x14d9198: ldur            x0, [fp, #-8]
    // 0x14d919c: StoreField: r2->field_b = r0
    //     0x14d919c: stur            w0, [x2, #0xb]
    // 0x14d91a0: r1 = <FlexParentData>
    //     0x14d91a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14d91a4: ldr             x1, [x1, #0xe00]
    // 0x14d91a8: r0 = Flexible()
    //     0x14d91a8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14d91ac: mov             x2, x0
    // 0x14d91b0: r0 = 1
    //     0x14d91b0: movz            x0, #0x1
    // 0x14d91b4: stur            x2, [fp, #-8]
    // 0x14d91b8: StoreField: r2->field_13 = r0
    //     0x14d91b8: stur            x0, [x2, #0x13]
    // 0x14d91bc: r0 = Instance_FlexFit
    //     0x14d91bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14d91c0: ldr             x0, [x0, #0xe20]
    // 0x14d91c4: StoreField: r2->field_1b = r0
    //     0x14d91c4: stur            w0, [x2, #0x1b]
    // 0x14d91c8: ldur            x0, [fp, #-0x18]
    // 0x14d91cc: StoreField: r2->field_b = r0
    //     0x14d91cc: stur            w0, [x2, #0xb]
    // 0x14d91d0: ldur            x0, [fp, #-0x10]
    // 0x14d91d4: LoadField: r1 = r0->field_b
    //     0x14d91d4: ldur            w1, [x0, #0xb]
    // 0x14d91d8: LoadField: r3 = r0->field_f
    //     0x14d91d8: ldur            w3, [x0, #0xf]
    // 0x14d91dc: DecompressPointer r3
    //     0x14d91dc: add             x3, x3, HEAP, lsl #32
    // 0x14d91e0: LoadField: r4 = r3->field_b
    //     0x14d91e0: ldur            w4, [x3, #0xb]
    // 0x14d91e4: r3 = LoadInt32Instr(r1)
    //     0x14d91e4: sbfx            x3, x1, #1, #0x1f
    // 0x14d91e8: stur            x3, [fp, #-0x70]
    // 0x14d91ec: r1 = LoadInt32Instr(r4)
    //     0x14d91ec: sbfx            x1, x4, #1, #0x1f
    // 0x14d91f0: cmp             x3, x1
    // 0x14d91f4: b.ne            #0x14d9200
    // 0x14d91f8: mov             x1, x0
    // 0x14d91fc: r0 = _growToNextCapacity()
    //     0x14d91fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14d9200: ldur            x2, [fp, #-0x10]
    // 0x14d9204: ldur            x3, [fp, #-0x70]
    // 0x14d9208: add             x0, x3, #1
    // 0x14d920c: lsl             x1, x0, #1
    // 0x14d9210: StoreField: r2->field_b = r1
    //     0x14d9210: stur            w1, [x2, #0xb]
    // 0x14d9214: LoadField: r1 = r2->field_f
    //     0x14d9214: ldur            w1, [x2, #0xf]
    // 0x14d9218: DecompressPointer r1
    //     0x14d9218: add             x1, x1, HEAP, lsl #32
    // 0x14d921c: ldur            x0, [fp, #-8]
    // 0x14d9220: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14d9220: add             x25, x1, x3, lsl #2
    //     0x14d9224: add             x25, x25, #0xf
    //     0x14d9228: str             w0, [x25]
    //     0x14d922c: tbz             w0, #0, #0x14d9248
    //     0x14d9230: ldurb           w16, [x1, #-1]
    //     0x14d9234: ldurb           w17, [x0, #-1]
    //     0x14d9238: and             x16, x17, x16, lsr #2
    //     0x14d923c: tst             x16, HEAP, lsr #32
    //     0x14d9240: b.eq            #0x14d9248
    //     0x14d9244: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14d9248: r0 = Column()
    //     0x14d9248: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d924c: mov             x1, x0
    // 0x14d9250: r0 = Instance_Axis
    //     0x14d9250: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d9254: stur            x1, [fp, #-8]
    // 0x14d9258: StoreField: r1->field_f = r0
    //     0x14d9258: stur            w0, [x1, #0xf]
    // 0x14d925c: r2 = Instance_MainAxisAlignment
    //     0x14d925c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x14d9260: ldr             x2, [x2, #0xab0]
    // 0x14d9264: StoreField: r1->field_13 = r2
    //     0x14d9264: stur            w2, [x1, #0x13]
    // 0x14d9268: r2 = Instance_MainAxisSize
    //     0x14d9268: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x14d926c: ldr             x2, [x2, #0xdd0]
    // 0x14d9270: ArrayStore: r1[0] = r2  ; List_4
    //     0x14d9270: stur            w2, [x1, #0x17]
    // 0x14d9274: r2 = Instance_CrossAxisAlignment
    //     0x14d9274: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d9278: ldr             x2, [x2, #0xa18]
    // 0x14d927c: StoreField: r1->field_1b = r2
    //     0x14d927c: stur            w2, [x1, #0x1b]
    // 0x14d9280: r2 = Instance_VerticalDirection
    //     0x14d9280: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d9284: ldr             x2, [x2, #0xa20]
    // 0x14d9288: StoreField: r1->field_23 = r2
    //     0x14d9288: stur            w2, [x1, #0x23]
    // 0x14d928c: r2 = Instance_Clip
    //     0x14d928c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d9290: ldr             x2, [x2, #0x38]
    // 0x14d9294: StoreField: r1->field_2b = r2
    //     0x14d9294: stur            w2, [x1, #0x2b]
    // 0x14d9298: StoreField: r1->field_2f = rZR
    //     0x14d9298: stur            xzr, [x1, #0x2f]
    // 0x14d929c: ldur            x2, [fp, #-0x10]
    // 0x14d92a0: StoreField: r1->field_b = r2
    //     0x14d92a0: stur            w2, [x1, #0xb]
    // 0x14d92a4: r0 = SingleChildScrollView()
    //     0x14d92a4: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x14d92a8: r1 = Instance_Axis
    //     0x14d92a8: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d92ac: StoreField: r0->field_b = r1
    //     0x14d92ac: stur            w1, [x0, #0xb]
    // 0x14d92b0: r1 = false
    //     0x14d92b0: add             x1, NULL, #0x30  ; false
    // 0x14d92b4: StoreField: r0->field_f = r1
    //     0x14d92b4: stur            w1, [x0, #0xf]
    // 0x14d92b8: ldur            x1, [fp, #-8]
    // 0x14d92bc: StoreField: r0->field_23 = r1
    //     0x14d92bc: stur            w1, [x0, #0x23]
    // 0x14d92c0: r1 = Instance_DragStartBehavior
    //     0x14d92c0: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x14d92c4: StoreField: r0->field_27 = r1
    //     0x14d92c4: stur            w1, [x0, #0x27]
    // 0x14d92c8: r1 = Instance_Clip
    //     0x14d92c8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x14d92cc: ldr             x1, [x1, #0x7e0]
    // 0x14d92d0: StoreField: r0->field_2b = r1
    //     0x14d92d0: stur            w1, [x0, #0x2b]
    // 0x14d92d4: r1 = Instance_HitTestBehavior
    //     0x14d92d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x14d92d8: ldr             x1, [x1, #0x288]
    // 0x14d92dc: StoreField: r0->field_2f = r1
    //     0x14d92dc: stur            w1, [x0, #0x2f]
    // 0x14d92e0: LeaveFrame
    //     0x14d92e0: mov             SP, fp
    //     0x14d92e4: ldp             fp, lr, [SP], #0x10
    // 0x14d92e8: ret
    //     0x14d92e8: ret             
    // 0x14d92ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d92ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d92f0: b               #0x14d82f4
    // 0x14d92f4: SaveReg d0
    //     0x14d92f4: str             q0, [SP, #-0x10]!
    // 0x14d92f8: stp             x0, x1, [SP, #-0x10]!
    // 0x14d92fc: r0 = AllocateDouble()
    //     0x14d92fc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14d9300: mov             x2, x0
    // 0x14d9304: ldp             x0, x1, [SP], #0x10
    // 0x14d9308: RestoreReg d0
    //     0x14d9308: ldr             q0, [SP], #0x10
    // 0x14d930c: b               #0x14d8a2c
    // 0x14d9310: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14d9310: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, Entity, String, String) {
    // ** addr: 0x14d9314, size: 0x5c
    // 0x14d9314: EnterFrame
    //     0x14d9314: stp             fp, lr, [SP, #-0x10]!
    //     0x14d9318: mov             fp, SP
    // 0x14d931c: ldr             x0, [fp, #0x28]
    // 0x14d9320: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d9320: ldur            w1, [x0, #0x17]
    // 0x14d9324: DecompressPointer r1
    //     0x14d9324: add             x1, x1, HEAP, lsl #32
    // 0x14d9328: CheckStackOverflow
    //     0x14d9328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d932c: cmp             SP, x16
    //     0x14d9330: b.ls            #0x14d9368
    // 0x14d9334: LoadField: r0 = r1->field_f
    //     0x14d9334: ldur            w0, [x1, #0xf]
    // 0x14d9338: DecompressPointer r0
    //     0x14d9338: add             x0, x0, HEAP, lsl #32
    // 0x14d933c: LoadField: r6 = r1->field_13
    //     0x14d933c: ldur            w6, [x1, #0x13]
    // 0x14d9340: DecompressPointer r6
    //     0x14d9340: add             x6, x6, HEAP, lsl #32
    // 0x14d9344: mov             x1, x0
    // 0x14d9348: ldr             x2, [fp, #0x20]
    // 0x14d934c: ldr             x3, [fp, #0x18]
    // 0x14d9350: ldr             x5, [fp, #0x10]
    // 0x14d9354: r0 = showSizeBottomSheet()
    //     0x14d9354: bl              #0x14d9370  ; [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::showSizeBottomSheet
    // 0x14d9358: r0 = Null
    //     0x14d9358: mov             x0, NULL
    // 0x14d935c: LeaveFrame
    //     0x14d935c: mov             SP, fp
    //     0x14d9360: ldp             fp, lr, [SP], #0x10
    // 0x14d9364: ret
    //     0x14d9364: ret             
    // 0x14d9368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d9368: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d936c: b               #0x14d9334
  }
  _ showSizeBottomSheet(/* No info */) {
    // ** addr: 0x14d9370, size: 0xa0
    // 0x14d9370: EnterFrame
    //     0x14d9370: stp             fp, lr, [SP, #-0x10]!
    //     0x14d9374: mov             fp, SP
    // 0x14d9378: AllocStack(0x48)
    //     0x14d9378: sub             SP, SP, #0x48
    // 0x14d937c: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x14d937c: stur            x1, [fp, #-8]
    //     0x14d9380: stur            x2, [fp, #-0x10]
    //     0x14d9384: stur            x3, [fp, #-0x18]
    //     0x14d9388: stur            x5, [fp, #-0x20]
    //     0x14d938c: stur            x6, [fp, #-0x28]
    // 0x14d9390: CheckStackOverflow
    //     0x14d9390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d9394: cmp             SP, x16
    //     0x14d9398: b.ls            #0x14d9408
    // 0x14d939c: r1 = 4
    //     0x14d939c: movz            x1, #0x4
    // 0x14d93a0: r0 = AllocateContext()
    //     0x14d93a0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d93a4: mov             x1, x0
    // 0x14d93a8: ldur            x0, [fp, #-8]
    // 0x14d93ac: StoreField: r1->field_f = r0
    //     0x14d93ac: stur            w0, [x1, #0xf]
    // 0x14d93b0: ldur            x0, [fp, #-0x10]
    // 0x14d93b4: StoreField: r1->field_13 = r0
    //     0x14d93b4: stur            w0, [x1, #0x13]
    // 0x14d93b8: ldur            x0, [fp, #-0x18]
    // 0x14d93bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d93bc: stur            w0, [x1, #0x17]
    // 0x14d93c0: ldur            x0, [fp, #-0x20]
    // 0x14d93c4: StoreField: r1->field_1b = r0
    //     0x14d93c4: stur            w0, [x1, #0x1b]
    // 0x14d93c8: mov             x2, x1
    // 0x14d93cc: r1 = Function '<anonymous closure>':.
    //     0x14d93cc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c40] AnonymousClosure: (0x14d9410), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x14d9370)
    //     0x14d93d0: ldr             x1, [x1, #0xc40]
    // 0x14d93d4: r0 = AllocateClosure()
    //     0x14d93d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d93d8: stp             x0, NULL, [SP, #0x10]
    // 0x14d93dc: ldur            x16, [fp, #-0x28]
    // 0x14d93e0: r30 = Instance_RoundedRectangleBorder
    //     0x14d93e0: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f670] Obj!RoundedRectangleBorder@d5ace1
    //     0x14d93e4: ldr             lr, [lr, #0x670]
    // 0x14d93e8: stp             lr, x16, [SP]
    // 0x14d93ec: r4 = const [0x1, 0x3, 0x3, 0x2, shape, 0x2, null]
    //     0x14d93ec: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fea0] List(7) [0x1, 0x3, 0x3, 0x2, "shape", 0x2, Null]
    //     0x14d93f0: ldr             x4, [x4, #0xea0]
    // 0x14d93f4: r0 = showModalBottomSheet()
    //     0x14d93f4: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14d93f8: r0 = Null
    //     0x14d93f8: mov             x0, NULL
    // 0x14d93fc: LeaveFrame
    //     0x14d93fc: mov             SP, fp
    //     0x14d9400: ldp             fp, lr, [SP], #0x10
    // 0x14d9404: ret
    //     0x14d9404: ret             
    // 0x14d9408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d9408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d940c: b               #0x14d939c
  }
  [closure] SafeArea <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14d9410, size: 0x20c
    // 0x14d9410: EnterFrame
    //     0x14d9410: stp             fp, lr, [SP, #-0x10]!
    //     0x14d9414: mov             fp, SP
    // 0x14d9418: AllocStack(0x38)
    //     0x14d9418: sub             SP, SP, #0x38
    // 0x14d941c: SetupParameters()
    //     0x14d941c: ldr             x0, [fp, #0x18]
    //     0x14d9420: ldur            w2, [x0, #0x17]
    //     0x14d9424: add             x2, x2, HEAP, lsl #32
    //     0x14d9428: stur            x2, [fp, #-0x18]
    // 0x14d942c: CheckStackOverflow
    //     0x14d942c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d9430: cmp             SP, x16
    //     0x14d9434: b.ls            #0x14d9614
    // 0x14d9438: LoadField: r0 = r2->field_13
    //     0x14d9438: ldur            w0, [x2, #0x13]
    // 0x14d943c: DecompressPointer r0
    //     0x14d943c: add             x0, x0, HEAP, lsl #32
    // 0x14d9440: stur            x0, [fp, #-0x10]
    // 0x14d9444: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x14d9444: ldur            w3, [x2, #0x17]
    // 0x14d9448: DecompressPointer r3
    //     0x14d9448: add             x3, x3, HEAP, lsl #32
    // 0x14d944c: stur            x3, [fp, #-8]
    // 0x14d9450: LoadField: r1 = r2->field_f
    //     0x14d9450: ldur            w1, [x2, #0xf]
    // 0x14d9454: DecompressPointer r1
    //     0x14d9454: add             x1, x1, HEAP, lsl #32
    // 0x14d9458: r0 = controller()
    //     0x14d9458: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d945c: ldur            x2, [fp, #-0x18]
    // 0x14d9460: stur            x0, [fp, #-0x28]
    // 0x14d9464: LoadField: r3 = r2->field_1b
    //     0x14d9464: ldur            w3, [x2, #0x1b]
    // 0x14d9468: DecompressPointer r3
    //     0x14d9468: add             x3, x3, HEAP, lsl #32
    // 0x14d946c: stur            x3, [fp, #-0x20]
    // 0x14d9470: LoadField: r1 = r2->field_f
    //     0x14d9470: ldur            w1, [x2, #0xf]
    // 0x14d9474: DecompressPointer r1
    //     0x14d9474: add             x1, x1, HEAP, lsl #32
    // 0x14d9478: r0 = controller()
    //     0x14d9478: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d947c: LoadField: r1 = r0->field_7f
    //     0x14d947c: ldur            w1, [x0, #0x7f]
    // 0x14d9480: DecompressPointer r1
    //     0x14d9480: add             x1, x1, HEAP, lsl #32
    // 0x14d9484: r0 = value()
    //     0x14d9484: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9488: LoadField: r1 = r0->field_67
    //     0x14d9488: ldur            w1, [x0, #0x67]
    // 0x14d948c: DecompressPointer r1
    //     0x14d948c: add             x1, x1, HEAP, lsl #32
    // 0x14d9490: cmp             w1, NULL
    // 0x14d9494: b.ne            #0x14d94a0
    // 0x14d9498: r0 = Null
    //     0x14d9498: mov             x0, NULL
    // 0x14d949c: b               #0x14d94d8
    // 0x14d94a0: LoadField: r0 = r1->field_7
    //     0x14d94a0: ldur            w0, [x1, #7]
    // 0x14d94a4: DecompressPointer r0
    //     0x14d94a4: add             x0, x0, HEAP, lsl #32
    // 0x14d94a8: cmp             w0, NULL
    // 0x14d94ac: b.ne            #0x14d94b8
    // 0x14d94b0: r0 = Null
    //     0x14d94b0: mov             x0, NULL
    // 0x14d94b4: b               #0x14d94d8
    // 0x14d94b8: LoadField: r1 = r0->field_b
    //     0x14d94b8: ldur            w1, [x0, #0xb]
    // 0x14d94bc: DecompressPointer r1
    //     0x14d94bc: add             x1, x1, HEAP, lsl #32
    // 0x14d94c0: cmp             w1, NULL
    // 0x14d94c4: b.ne            #0x14d94d0
    // 0x14d94c8: r0 = Null
    //     0x14d94c8: mov             x0, NULL
    // 0x14d94cc: b               #0x14d94d8
    // 0x14d94d0: LoadField: r0 = r1->field_7
    //     0x14d94d0: ldur            w0, [x1, #7]
    // 0x14d94d4: DecompressPointer r0
    //     0x14d94d4: add             x0, x0, HEAP, lsl #32
    // 0x14d94d8: cmp             w0, NULL
    // 0x14d94dc: b.ne            #0x14d94e8
    // 0x14d94e0: r3 = false
    //     0x14d94e0: add             x3, NULL, #0x30  ; false
    // 0x14d94e4: b               #0x14d94ec
    // 0x14d94e8: mov             x3, x0
    // 0x14d94ec: ldur            x1, [fp, #-0x10]
    // 0x14d94f0: ldur            x2, [fp, #-8]
    // 0x14d94f4: ldur            x0, [fp, #-0x20]
    // 0x14d94f8: stur            x3, [fp, #-0x30]
    // 0x14d94fc: r0 = SelectSizeBottomSheet()
    //     0x14d94fc: bl              #0x143963c  ; AllocateSelectSizeBottomSheetStub -> SelectSizeBottomSheet (size=0x2c)
    // 0x14d9500: mov             x3, x0
    // 0x14d9504: ldur            x0, [fp, #-0x10]
    // 0x14d9508: stur            x3, [fp, #-0x38]
    // 0x14d950c: StoreField: r3->field_b = r0
    //     0x14d950c: stur            w0, [x3, #0xb]
    // 0x14d9510: ldur            x0, [fp, #-8]
    // 0x14d9514: StoreField: r3->field_f = r0
    //     0x14d9514: stur            w0, [x3, #0xf]
    // 0x14d9518: ldur            x2, [fp, #-0x28]
    // 0x14d951c: r1 = Function 'postEvents':.
    //     0x14d951c: add             x1, PP, #0x32, lsl #12  ; [pp+0x321b0] AnonymousClosure: (0x89c5cc), in [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents (0x8608dc)
    //     0x14d9520: ldr             x1, [x1, #0x1b0]
    // 0x14d9524: r0 = AllocateClosure()
    //     0x14d9524: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d9528: mov             x1, x0
    // 0x14d952c: ldur            x0, [fp, #-0x38]
    // 0x14d9530: StoreField: r0->field_13 = r1
    //     0x14d9530: stur            w1, [x0, #0x13]
    // 0x14d9534: ldur            x1, [fp, #-0x20]
    // 0x14d9538: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d9538: stur            w1, [x0, #0x17]
    // 0x14d953c: ldur            x2, [fp, #-0x18]
    // 0x14d9540: r1 = Function '<anonymous closure>':.
    //     0x14d9540: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c48] AnonymousClosure: (0x13b0548), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x14d9544: ldr             x1, [x1, #0xc48]
    // 0x14d9548: r0 = AllocateClosure()
    //     0x14d9548: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d954c: mov             x1, x0
    // 0x14d9550: ldur            x0, [fp, #-0x38]
    // 0x14d9554: StoreField: r0->field_1b = r1
    //     0x14d9554: stur            w1, [x0, #0x1b]
    // 0x14d9558: ldur            x2, [fp, #-0x18]
    // 0x14d955c: r1 = Function '<anonymous closure>':.
    //     0x14d955c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c50] AnonymousClosure: (0x13afd6c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x14d9560: ldr             x1, [x1, #0xc50]
    // 0x14d9564: r0 = AllocateClosure()
    //     0x14d9564: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d9568: mov             x1, x0
    // 0x14d956c: ldur            x0, [fp, #-0x38]
    // 0x14d9570: StoreField: r0->field_1f = r1
    //     0x14d9570: stur            w1, [x0, #0x1f]
    // 0x14d9574: ldur            x2, [fp, #-0x18]
    // 0x14d9578: r1 = Function '<anonymous closure>':.
    //     0x14d9578: add             x1, PP, #0x40, lsl #12  ; [pp+0x40c58] AnonymousClosure: (0x13afaf8), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::showSizeBottomSheet (0x13b062c)
    //     0x14d957c: ldr             x1, [x1, #0xc58]
    // 0x14d9580: r0 = AllocateClosure()
    //     0x14d9580: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d9584: mov             x1, x0
    // 0x14d9588: ldur            x0, [fp, #-0x38]
    // 0x14d958c: StoreField: r0->field_23 = r1
    //     0x14d958c: stur            w1, [x0, #0x23]
    // 0x14d9590: ldur            x1, [fp, #-0x30]
    // 0x14d9594: StoreField: r0->field_27 = r1
    //     0x14d9594: stur            w1, [x0, #0x27]
    // 0x14d9598: r0 = Card()
    //     0x14d9598: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14d959c: mov             x1, x0
    // 0x14d95a0: r0 = Instance_Color
    //     0x14d95a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14d95a4: ldr             x0, [x0, #0x90]
    // 0x14d95a8: stur            x1, [fp, #-8]
    // 0x14d95ac: StoreField: r1->field_b = r0
    //     0x14d95ac: stur            w0, [x1, #0xb]
    // 0x14d95b0: r0 = true
    //     0x14d95b0: add             x0, NULL, #0x20  ; true
    // 0x14d95b4: StoreField: r1->field_1f = r0
    //     0x14d95b4: stur            w0, [x1, #0x1f]
    // 0x14d95b8: r2 = Instance_EdgeInsets
    //     0x14d95b8: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14d95bc: StoreField: r1->field_27 = r2
    //     0x14d95bc: stur            w2, [x1, #0x27]
    // 0x14d95c0: ldur            x3, [fp, #-0x38]
    // 0x14d95c4: StoreField: r1->field_2f = r3
    //     0x14d95c4: stur            w3, [x1, #0x2f]
    // 0x14d95c8: StoreField: r1->field_2b = r0
    //     0x14d95c8: stur            w0, [x1, #0x2b]
    // 0x14d95cc: r3 = Instance__CardVariant
    //     0x14d95cc: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14d95d0: ldr             x3, [x3, #0xa68]
    // 0x14d95d4: StoreField: r1->field_33 = r3
    //     0x14d95d4: stur            w3, [x1, #0x33]
    // 0x14d95d8: r0 = SafeArea()
    //     0x14d95d8: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14d95dc: r1 = true
    //     0x14d95dc: add             x1, NULL, #0x20  ; true
    // 0x14d95e0: StoreField: r0->field_b = r1
    //     0x14d95e0: stur            w1, [x0, #0xb]
    // 0x14d95e4: StoreField: r0->field_f = r1
    //     0x14d95e4: stur            w1, [x0, #0xf]
    // 0x14d95e8: StoreField: r0->field_13 = r1
    //     0x14d95e8: stur            w1, [x0, #0x13]
    // 0x14d95ec: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d95ec: stur            w1, [x0, #0x17]
    // 0x14d95f0: r1 = Instance_EdgeInsets
    //     0x14d95f0: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14d95f4: StoreField: r0->field_1b = r1
    //     0x14d95f4: stur            w1, [x0, #0x1b]
    // 0x14d95f8: r1 = false
    //     0x14d95f8: add             x1, NULL, #0x30  ; false
    // 0x14d95fc: StoreField: r0->field_1f = r1
    //     0x14d95fc: stur            w1, [x0, #0x1f]
    // 0x14d9600: ldur            x1, [fp, #-8]
    // 0x14d9604: StoreField: r0->field_23 = r1
    //     0x14d9604: stur            w1, [x0, #0x23]
    // 0x14d9608: LeaveFrame
    //     0x14d9608: mov             SP, fp
    //     0x14d960c: ldp             fp, lr, [SP], #0x10
    // 0x14d9610: ret
    //     0x14d9610: ret             
    // 0x14d9614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d9614: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d9618: b               #0x14d9438
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x14d961c, size: 0x180
    // 0x14d961c: EnterFrame
    //     0x14d961c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d9620: mov             fp, SP
    // 0x14d9624: AllocStack(0x10)
    //     0x14d9624: sub             SP, SP, #0x10
    // 0x14d9628: SetupParameters()
    //     0x14d9628: ldr             x0, [fp, #0x38]
    //     0x14d962c: ldur            w1, [x0, #0x17]
    //     0x14d9630: add             x1, x1, HEAP, lsl #32
    // 0x14d9634: CheckStackOverflow
    //     0x14d9634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d9638: cmp             SP, x16
    //     0x14d963c: b.ls            #0x14d9794
    // 0x14d9640: LoadField: r0 = r1->field_f
    //     0x14d9640: ldur            w0, [x1, #0xf]
    // 0x14d9644: DecompressPointer r0
    //     0x14d9644: add             x0, x0, HEAP, lsl #32
    // 0x14d9648: mov             x1, x0
    // 0x14d964c: r0 = controller()
    //     0x14d964c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9650: mov             x3, x0
    // 0x14d9654: ldr             x0, [fp, #0x28]
    // 0x14d9658: r2 = Null
    //     0x14d9658: mov             x2, NULL
    // 0x14d965c: r1 = Null
    //     0x14d965c: mov             x1, NULL
    // 0x14d9660: stur            x3, [fp, #-8]
    // 0x14d9664: r4 = 60
    //     0x14d9664: movz            x4, #0x3c
    // 0x14d9668: branchIfSmi(r0, 0x14d9674)
    //     0x14d9668: tbz             w0, #0, #0x14d9674
    // 0x14d966c: r4 = LoadClassIdInstr(r0)
    //     0x14d966c: ldur            x4, [x0, #-1]
    //     0x14d9670: ubfx            x4, x4, #0xc, #0x14
    // 0x14d9674: sub             x4, x4, #0x5e
    // 0x14d9678: cmp             x4, #1
    // 0x14d967c: b.ls            #0x14d9690
    // 0x14d9680: r8 = String
    //     0x14d9680: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14d9684: r3 = Null
    //     0x14d9684: add             x3, PP, #0x40, lsl #12  ; [pp+0x40c60] Null
    //     0x14d9688: ldr             x3, [x3, #0xc60]
    // 0x14d968c: r0 = String()
    //     0x14d968c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14d9690: ldr             x0, [fp, #0x20]
    // 0x14d9694: r2 = Null
    //     0x14d9694: mov             x2, NULL
    // 0x14d9698: r1 = Null
    //     0x14d9698: mov             x1, NULL
    // 0x14d969c: r4 = 60
    //     0x14d969c: movz            x4, #0x3c
    // 0x14d96a0: branchIfSmi(r0, 0x14d96ac)
    //     0x14d96a0: tbz             w0, #0, #0x14d96ac
    // 0x14d96a4: r4 = LoadClassIdInstr(r0)
    //     0x14d96a4: ldur            x4, [x0, #-1]
    //     0x14d96a8: ubfx            x4, x4, #0xc, #0x14
    // 0x14d96ac: sub             x4, x4, #0x5e
    // 0x14d96b0: cmp             x4, #1
    // 0x14d96b4: b.ls            #0x14d96c8
    // 0x14d96b8: r8 = String
    //     0x14d96b8: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14d96bc: r3 = Null
    //     0x14d96bc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40c70] Null
    //     0x14d96c0: ldr             x3, [x3, #0xc70]
    // 0x14d96c4: r0 = String()
    //     0x14d96c4: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14d96c8: ldr             x0, [fp, #0x18]
    // 0x14d96cc: r2 = Null
    //     0x14d96cc: mov             x2, NULL
    // 0x14d96d0: r1 = Null
    //     0x14d96d0: mov             x1, NULL
    // 0x14d96d4: branchIfSmi(r0, 0x14d96fc)
    //     0x14d96d4: tbz             w0, #0, #0x14d96fc
    // 0x14d96d8: r4 = LoadClassIdInstr(r0)
    //     0x14d96d8: ldur            x4, [x0, #-1]
    //     0x14d96dc: ubfx            x4, x4, #0xc, #0x14
    // 0x14d96e0: sub             x4, x4, #0x3c
    // 0x14d96e4: cmp             x4, #1
    // 0x14d96e8: b.ls            #0x14d96fc
    // 0x14d96ec: r8 = int
    //     0x14d96ec: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14d96f0: r3 = Null
    //     0x14d96f0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40c80] Null
    //     0x14d96f4: ldr             x3, [x3, #0xc80]
    // 0x14d96f8: r0 = int()
    //     0x14d96f8: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14d96fc: ldr             x0, [fp, #0x10]
    // 0x14d9700: cmp             w0, NULL
    // 0x14d9704: b.ne            #0x14d9714
    // 0x14d9708: r0 = ProductRating()
    //     0x14d9708: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0x14d970c: mov             x4, x0
    // 0x14d9710: b               #0x14d9718
    // 0x14d9714: mov             x4, x0
    // 0x14d9718: ldr             x3, [fp, #0x18]
    // 0x14d971c: mov             x0, x4
    // 0x14d9720: stur            x4, [fp, #-0x10]
    // 0x14d9724: r2 = Null
    //     0x14d9724: mov             x2, NULL
    // 0x14d9728: r1 = Null
    //     0x14d9728: mov             x1, NULL
    // 0x14d972c: r4 = 60
    //     0x14d972c: movz            x4, #0x3c
    // 0x14d9730: branchIfSmi(r0, 0x14d973c)
    //     0x14d9730: tbz             w0, #0, #0x14d973c
    // 0x14d9734: r4 = LoadClassIdInstr(r0)
    //     0x14d9734: ldur            x4, [x0, #-1]
    //     0x14d9738: ubfx            x4, x4, #0xc, #0x14
    // 0x14d973c: r17 = 5178
    //     0x14d973c: movz            x17, #0x143a
    // 0x14d9740: cmp             x4, x17
    // 0x14d9744: b.eq            #0x14d975c
    // 0x14d9748: r8 = ProductRating
    //     0x14d9748: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee30] Type: ProductRating
    //     0x14d974c: ldr             x8, [x8, #0xe30]
    // 0x14d9750: r3 = Null
    //     0x14d9750: add             x3, PP, #0x40, lsl #12  ; [pp+0x40c90] Null
    //     0x14d9754: ldr             x3, [x3, #0xc90]
    // 0x14d9758: r0 = DefaultTypeTest()
    //     0x14d9758: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x14d975c: ldr             x0, [fp, #0x18]
    // 0x14d9760: r6 = LoadInt32Instr(r0)
    //     0x14d9760: sbfx            x6, x0, #1, #0x1f
    //     0x14d9764: tbz             w0, #0, #0x14d976c
    //     0x14d9768: ldur            x6, [x0, #7]
    // 0x14d976c: ldur            x1, [fp, #-8]
    // 0x14d9770: ldr             x2, [fp, #0x30]
    // 0x14d9774: ldr             x3, [fp, #0x28]
    // 0x14d9778: ldr             x5, [fp, #0x20]
    // 0x14d977c: ldur            x7, [fp, #-0x10]
    // 0x14d9780: r0 = productViewedEvent()
    //     0x14d9780: bl              #0x13b084c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::productViewedEvent
    // 0x14d9784: r0 = Null
    //     0x14d9784: mov             x0, NULL
    // 0x14d9788: LeaveFrame
    //     0x14d9788: mov             SP, fp
    //     0x14d978c: ldp             fp, lr, [SP], #0x10
    // 0x14d9790: ret
    //     0x14d9790: ret             
    // 0x14d9794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d9794: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d9798: b               #0x14d9640
  }
  [closure] (dynamic) => void <anonymous closure>(dynamic, int, String) {
    // ** addr: 0x14d979c, size: 0x54
    // 0x14d979c: EnterFrame
    //     0x14d979c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d97a0: mov             fp, SP
    // 0x14d97a4: AllocStack(0x8)
    //     0x14d97a4: sub             SP, SP, #8
    // 0x14d97a8: SetupParameters()
    //     0x14d97a8: ldr             x0, [fp, #0x20]
    //     0x14d97ac: ldur            w1, [x0, #0x17]
    //     0x14d97b0: add             x1, x1, HEAP, lsl #32
    //     0x14d97b4: stur            x1, [fp, #-8]
    // 0x14d97b8: r1 = 1
    //     0x14d97b8: movz            x1, #0x1
    // 0x14d97bc: r0 = AllocateContext()
    //     0x14d97bc: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d97c0: mov             x1, x0
    // 0x14d97c4: ldur            x0, [fp, #-8]
    // 0x14d97c8: StoreField: r1->field_b = r0
    //     0x14d97c8: stur            w0, [x1, #0xb]
    // 0x14d97cc: ldr             x0, [fp, #0x10]
    // 0x14d97d0: StoreField: r1->field_f = r0
    //     0x14d97d0: stur            w0, [x1, #0xf]
    // 0x14d97d4: mov             x2, x1
    // 0x14d97d8: r1 = Function '<anonymous closure>':.
    //     0x14d97d8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ca0] AnonymousClosure: (0x14d97f0), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14d97dc: ldr             x1, [x1, #0xca0]
    // 0x14d97e0: r0 = AllocateClosure()
    //     0x14d97e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d97e4: LeaveFrame
    //     0x14d97e4: mov             SP, fp
    //     0x14d97e8: ldp             fp, lr, [SP], #0x10
    // 0x14d97ec: ret
    //     0x14d97ec: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x14d97f0, size: 0xb60
    // 0x14d97f0: EnterFrame
    //     0x14d97f0: stp             fp, lr, [SP, #-0x10]!
    //     0x14d97f4: mov             fp, SP
    // 0x14d97f8: AllocStack(0x50)
    //     0x14d97f8: sub             SP, SP, #0x50
    // 0x14d97fc: SetupParameters()
    //     0x14d97fc: ldr             x0, [fp, #0x10]
    //     0x14d9800: ldur            w1, [x0, #0x17]
    //     0x14d9804: add             x1, x1, HEAP, lsl #32
    //     0x14d9808: stur            x1, [fp, #-0x18]
    // 0x14d980c: CheckStackOverflow
    //     0x14d980c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d9810: cmp             SP, x16
    //     0x14d9814: b.ls            #0x14da324
    // 0x14d9818: LoadField: r0 = r1->field_b
    //     0x14d9818: ldur            w0, [x1, #0xb]
    // 0x14d981c: DecompressPointer r0
    //     0x14d981c: add             x0, x0, HEAP, lsl #32
    // 0x14d9820: stur            x0, [fp, #-0x10]
    // 0x14d9824: LoadField: r2 = r0->field_f
    //     0x14d9824: ldur            w2, [x0, #0xf]
    // 0x14d9828: DecompressPointer r2
    //     0x14d9828: add             x2, x2, HEAP, lsl #32
    // 0x14d982c: stur            x2, [fp, #-8]
    // 0x14d9830: r0 = FilterResponse()
    //     0x14d9830: bl              #0x91d7fc  ; AllocateFilterResponseStub -> FilterResponse (size=0x18)
    // 0x14d9834: ldur            x1, [fp, #-8]
    // 0x14d9838: StoreField: r1->field_13 = r0
    //     0x14d9838: stur            w0, [x1, #0x13]
    //     0x14d983c: ldurb           w16, [x1, #-1]
    //     0x14d9840: ldurb           w17, [x0, #-1]
    //     0x14d9844: and             x16, x17, x16, lsr #2
    //     0x14d9848: tst             x16, HEAP, lsr #32
    //     0x14d984c: b.eq            #0x14d9854
    //     0x14d9850: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14d9854: r0 = controller()
    //     0x14d9854: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9858: LoadField: r1 = r0->field_5f
    //     0x14d9858: ldur            w1, [x0, #0x5f]
    // 0x14d985c: DecompressPointer r1
    //     0x14d985c: add             x1, x1, HEAP, lsl #32
    // 0x14d9860: r0 = value()
    //     0x14d9860: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9864: LoadField: r1 = r0->field_7
    //     0x14d9864: ldur            w1, [x0, #7]
    // 0x14d9868: DecompressPointer r1
    //     0x14d9868: add             x1, x1, HEAP, lsl #32
    // 0x14d986c: cmp             w1, NULL
    // 0x14d9870: b.eq            #0x14d9884
    // 0x14d9874: ldur            x0, [fp, #-0x18]
    // 0x14d9878: LoadField: r2 = r0->field_f
    //     0x14d9878: ldur            w2, [x0, #0xf]
    // 0x14d987c: DecompressPointer r2
    //     0x14d987c: add             x2, x2, HEAP, lsl #32
    // 0x14d9880: r0 = remove()
    //     0x14d9880: bl              #0x71df18  ; [dart:core] _GrowableList::remove
    // 0x14d9884: ldur            x0, [fp, #-0x10]
    // 0x14d9888: LoadField: r1 = r0->field_f
    //     0x14d9888: ldur            w1, [x0, #0xf]
    // 0x14d988c: DecompressPointer r1
    //     0x14d988c: add             x1, x1, HEAP, lsl #32
    // 0x14d9890: r0 = controller()
    //     0x14d9890: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9894: LoadField: r1 = r0->field_63
    //     0x14d9894: ldur            w1, [x0, #0x63]
    // 0x14d9898: DecompressPointer r1
    //     0x14d9898: add             x1, x1, HEAP, lsl #32
    // 0x14d989c: r0 = value()
    //     0x14d989c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d98a0: LoadField: r1 = r0->field_7
    //     0x14d98a0: ldur            w1, [x0, #7]
    // 0x14d98a4: DecompressPointer r1
    //     0x14d98a4: add             x1, x1, HEAP, lsl #32
    // 0x14d98a8: cmp             w1, NULL
    // 0x14d98ac: b.eq            #0x14d9b7c
    // 0x14d98b0: ldur            x0, [fp, #-0x10]
    // 0x14d98b4: LoadField: r1 = r0->field_f
    //     0x14d98b4: ldur            w1, [x0, #0xf]
    // 0x14d98b8: DecompressPointer r1
    //     0x14d98b8: add             x1, x1, HEAP, lsl #32
    // 0x14d98bc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x14d98bc: ldur            w2, [x1, #0x17]
    // 0x14d98c0: DecompressPointer r2
    //     0x14d98c0: add             x2, x2, HEAP, lsl #32
    // 0x14d98c4: stur            x2, [fp, #-8]
    // 0x14d98c8: r0 = controller()
    //     0x14d98c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d98cc: LoadField: r1 = r0->field_63
    //     0x14d98cc: ldur            w1, [x0, #0x63]
    // 0x14d98d0: DecompressPointer r1
    //     0x14d98d0: add             x1, x1, HEAP, lsl #32
    // 0x14d98d4: r0 = value()
    //     0x14d98d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d98d8: LoadField: r2 = r0->field_7
    //     0x14d98d8: ldur            w2, [x0, #7]
    // 0x14d98dc: DecompressPointer r2
    //     0x14d98dc: add             x2, x2, HEAP, lsl #32
    // 0x14d98e0: cmp             w2, NULL
    // 0x14d98e4: b.eq            #0x14da32c
    // 0x14d98e8: ldur            x1, [fp, #-8]
    // 0x14d98ec: r0 = addAll()
    //     0x14d98ec: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x14d98f0: ldur            x0, [fp, #-0x10]
    // 0x14d98f4: LoadField: r1 = r0->field_f
    //     0x14d98f4: ldur            w1, [x0, #0xf]
    // 0x14d98f8: DecompressPointer r1
    //     0x14d98f8: add             x1, x1, HEAP, lsl #32
    // 0x14d98fc: r0 = controller()
    //     0x14d98fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9900: LoadField: r1 = r0->field_67
    //     0x14d9900: ldur            w1, [x0, #0x67]
    // 0x14d9904: DecompressPointer r1
    //     0x14d9904: add             x1, x1, HEAP, lsl #32
    // 0x14d9908: r0 = value()
    //     0x14d9908: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d990c: LoadField: r1 = r0->field_b
    //     0x14d990c: ldur            w1, [x0, #0xb]
    // 0x14d9910: DecompressPointer r1
    //     0x14d9910: add             x1, x1, HEAP, lsl #32
    // 0x14d9914: cmp             w1, NULL
    // 0x14d9918: b.ne            #0x14d9924
    // 0x14d991c: r0 = Null
    //     0x14d991c: mov             x0, NULL
    // 0x14d9920: b               #0x14d9948
    // 0x14d9924: LoadField: r0 = r1->field_27
    //     0x14d9924: ldur            w0, [x1, #0x27]
    // 0x14d9928: DecompressPointer r0
    //     0x14d9928: add             x0, x0, HEAP, lsl #32
    // 0x14d992c: cmp             w0, NULL
    // 0x14d9930: b.ne            #0x14d993c
    // 0x14d9934: r0 = Null
    //     0x14d9934: mov             x0, NULL
    // 0x14d9938: b               #0x14d9948
    // 0x14d993c: LoadField: r1 = r0->field_7
    //     0x14d993c: ldur            w1, [x0, #7]
    // 0x14d9940: DecompressPointer r1
    //     0x14d9940: add             x1, x1, HEAP, lsl #32
    // 0x14d9944: mov             x0, x1
    // 0x14d9948: cmp             w0, NULL
    // 0x14d994c: b.ne            #0x14d9994
    // 0x14d9950: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x14d9950: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d9954: ldr             x0, [x0]
    //     0x14d9958: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d995c: cmp             w0, w16
    //     0x14d9960: b.ne            #0x14d996c
    //     0x14d9964: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x14d9968: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14d996c: r1 = <ProductType>
    //     0x14d996c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bbd0] TypeArguments: <ProductType>
    //     0x14d9970: ldr             x1, [x1, #0xbd0]
    // 0x14d9974: stur            x0, [fp, #-8]
    // 0x14d9978: r0 = AllocateGrowableArray()
    //     0x14d9978: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d997c: mov             x1, x0
    // 0x14d9980: ldur            x0, [fp, #-8]
    // 0x14d9984: StoreField: r1->field_f = r0
    //     0x14d9984: stur            w0, [x1, #0xf]
    // 0x14d9988: StoreField: r1->field_b = rZR
    //     0x14d9988: stur            wzr, [x1, #0xb]
    // 0x14d998c: mov             x3, x1
    // 0x14d9990: b               #0x14d9998
    // 0x14d9994: mov             x3, x0
    // 0x14d9998: stur            x3, [fp, #-0x38]
    // 0x14d999c: LoadField: r4 = r3->field_7
    //     0x14d999c: ldur            w4, [x3, #7]
    // 0x14d99a0: DecompressPointer r4
    //     0x14d99a0: add             x4, x4, HEAP, lsl #32
    // 0x14d99a4: stur            x4, [fp, #-0x30]
    // 0x14d99a8: LoadField: r0 = r3->field_b
    //     0x14d99a8: ldur            w0, [x3, #0xb]
    // 0x14d99ac: r5 = LoadInt32Instr(r0)
    //     0x14d99ac: sbfx            x5, x0, #1, #0x1f
    // 0x14d99b0: stur            x5, [fp, #-0x28]
    // 0x14d99b4: r0 = 0
    //     0x14d99b4: movz            x0, #0
    // 0x14d99b8: ldur            x7, [fp, #-0x18]
    // 0x14d99bc: ldur            x6, [fp, #-0x10]
    // 0x14d99c0: CheckStackOverflow
    //     0x14d99c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d99c4: cmp             SP, x16
    //     0x14d99c8: b.ls            #0x14da330
    // 0x14d99cc: LoadField: r1 = r3->field_b
    //     0x14d99cc: ldur            w1, [x3, #0xb]
    // 0x14d99d0: r2 = LoadInt32Instr(r1)
    //     0x14d99d0: sbfx            x2, x1, #1, #0x1f
    // 0x14d99d4: cmp             x5, x2
    // 0x14d99d8: b.ne            #0x14da304
    // 0x14d99dc: cmp             x0, x2
    // 0x14d99e0: b.ge            #0x14d9b04
    // 0x14d99e4: LoadField: r1 = r3->field_f
    //     0x14d99e4: ldur            w1, [x3, #0xf]
    // 0x14d99e8: DecompressPointer r1
    //     0x14d99e8: add             x1, x1, HEAP, lsl #32
    // 0x14d99ec: ArrayLoad: r8 = r1[r0]  ; Unknown_4
    //     0x14d99ec: add             x16, x1, x0, lsl #2
    //     0x14d99f0: ldur            w8, [x16, #0xf]
    // 0x14d99f4: DecompressPointer r8
    //     0x14d99f4: add             x8, x8, HEAP, lsl #32
    // 0x14d99f8: stur            x8, [fp, #-8]
    // 0x14d99fc: add             x9, x0, #1
    // 0x14d9a00: stur            x9, [fp, #-0x20]
    // 0x14d9a04: cmp             w8, NULL
    // 0x14d9a08: b.ne            #0x14d9a3c
    // 0x14d9a0c: mov             x0, x8
    // 0x14d9a10: mov             x2, x4
    // 0x14d9a14: r1 = Null
    //     0x14d9a14: mov             x1, NULL
    // 0x14d9a18: cmp             w2, NULL
    // 0x14d9a1c: b.eq            #0x14d9a3c
    // 0x14d9a20: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x14d9a20: ldur            w4, [x2, #0x17]
    // 0x14d9a24: DecompressPointer r4
    //     0x14d9a24: add             x4, x4, HEAP, lsl #32
    // 0x14d9a28: r8 = X0
    //     0x14d9a28: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x14d9a2c: LoadField: r9 = r4->field_7
    //     0x14d9a2c: ldur            x9, [x4, #7]
    // 0x14d9a30: r3 = Null
    //     0x14d9a30: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ca8] Null
    //     0x14d9a34: ldr             x3, [x3, #0xca8]
    // 0x14d9a38: blr             x9
    // 0x14d9a3c: ldur            x1, [fp, #-0x18]
    // 0x14d9a40: ldur            x2, [fp, #-8]
    // 0x14d9a44: LoadField: r0 = r2->field_f
    //     0x14d9a44: ldur            w0, [x2, #0xf]
    // 0x14d9a48: DecompressPointer r0
    //     0x14d9a48: add             x0, x0, HEAP, lsl #32
    // 0x14d9a4c: LoadField: r3 = r1->field_f
    //     0x14d9a4c: ldur            w3, [x1, #0xf]
    // 0x14d9a50: DecompressPointer r3
    //     0x14d9a50: add             x3, x3, HEAP, lsl #32
    // 0x14d9a54: r4 = LoadClassIdInstr(r0)
    //     0x14d9a54: ldur            x4, [x0, #-1]
    //     0x14d9a58: ubfx            x4, x4, #0xc, #0x14
    // 0x14d9a5c: stp             x3, x0, [SP]
    // 0x14d9a60: mov             x0, x4
    // 0x14d9a64: mov             lr, x0
    // 0x14d9a68: ldr             lr, [x21, lr, lsl #3]
    // 0x14d9a6c: blr             lr
    // 0x14d9a70: tbnz            w0, #4, #0x14d9af0
    // 0x14d9a74: ldur            x3, [fp, #-0x10]
    // 0x14d9a78: ldur            x0, [fp, #-8]
    // 0x14d9a7c: LoadField: r1 = r3->field_f
    //     0x14d9a7c: ldur            w1, [x3, #0xf]
    // 0x14d9a80: DecompressPointer r1
    //     0x14d9a80: add             x1, x1, HEAP, lsl #32
    // 0x14d9a84: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x14d9a84: ldur            w2, [x1, #0x17]
    // 0x14d9a88: DecompressPointer r2
    //     0x14d9a88: add             x2, x2, HEAP, lsl #32
    // 0x14d9a8c: LoadField: r1 = r0->field_7
    //     0x14d9a8c: ldur            w1, [x0, #7]
    // 0x14d9a90: DecompressPointer r1
    //     0x14d9a90: add             x1, x1, HEAP, lsl #32
    // 0x14d9a94: LoadField: r4 = r2->field_f
    //     0x14d9a94: ldur            w4, [x2, #0xf]
    // 0x14d9a98: DecompressPointer r4
    //     0x14d9a98: add             x4, x4, HEAP, lsl #32
    // 0x14d9a9c: mov             x16, x1
    // 0x14d9aa0: mov             x1, x2
    // 0x14d9aa4: mov             x2, x16
    // 0x14d9aa8: stur            x4, [fp, #-0x40]
    // 0x14d9aac: r0 = _getKeyOrData()
    //     0x14d9aac: bl              #0x69cfb4  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_getKeyOrData
    // 0x14d9ab0: mov             x1, x0
    // 0x14d9ab4: ldur            x0, [fp, #-0x40]
    // 0x14d9ab8: cmp             w0, w1
    // 0x14d9abc: b.eq            #0x14d9af0
    // 0x14d9ac0: ldur            x3, [fp, #-0x10]
    // 0x14d9ac4: ldur            x0, [fp, #-8]
    // 0x14d9ac8: LoadField: r1 = r3->field_f
    //     0x14d9ac8: ldur            w1, [x3, #0xf]
    // 0x14d9acc: DecompressPointer r1
    //     0x14d9acc: add             x1, x1, HEAP, lsl #32
    // 0x14d9ad0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x14d9ad0: ldur            w2, [x1, #0x17]
    // 0x14d9ad4: DecompressPointer r2
    //     0x14d9ad4: add             x2, x2, HEAP, lsl #32
    // 0x14d9ad8: LoadField: r1 = r0->field_7
    //     0x14d9ad8: ldur            w1, [x0, #7]
    // 0x14d9adc: DecompressPointer r1
    //     0x14d9adc: add             x1, x1, HEAP, lsl #32
    // 0x14d9ae0: mov             x16, x1
    // 0x14d9ae4: mov             x1, x2
    // 0x14d9ae8: mov             x2, x16
    // 0x14d9aec: r0 = remove()
    //     0x14d9aec: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x14d9af0: ldur            x0, [fp, #-0x20]
    // 0x14d9af4: ldur            x3, [fp, #-0x38]
    // 0x14d9af8: ldur            x4, [fp, #-0x30]
    // 0x14d9afc: ldur            x5, [fp, #-0x28]
    // 0x14d9b00: b               #0x14d99b8
    // 0x14d9b04: mov             x2, x6
    // 0x14d9b08: LoadField: r0 = r2->field_f
    //     0x14d9b08: ldur            w0, [x2, #0xf]
    // 0x14d9b0c: DecompressPointer r0
    //     0x14d9b0c: add             x0, x0, HEAP, lsl #32
    // 0x14d9b10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d9b10: ldur            w1, [x0, #0x17]
    // 0x14d9b14: DecompressPointer r1
    //     0x14d9b14: add             x1, x1, HEAP, lsl #32
    // 0x14d9b18: LoadField: r3 = r1->field_13
    //     0x14d9b18: ldur            w3, [x1, #0x13]
    // 0x14d9b1c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x14d9b1c: ldur            w4, [x1, #0x17]
    // 0x14d9b20: r5 = LoadInt32Instr(r3)
    //     0x14d9b20: sbfx            x5, x3, #1, #0x1f
    // 0x14d9b24: r3 = LoadInt32Instr(r4)
    //     0x14d9b24: sbfx            x3, x4, #1, #0x1f
    // 0x14d9b28: sub             x4, x5, x3
    // 0x14d9b2c: cbnz            x4, #0x14d9b48
    // 0x14d9b30: LoadField: r1 = r0->field_13
    //     0x14d9b30: ldur            w1, [x0, #0x13]
    // 0x14d9b34: DecompressPointer r1
    //     0x14d9b34: add             x1, x1, HEAP, lsl #32
    // 0x14d9b38: cmp             w1, NULL
    // 0x14d9b3c: b.eq            #0x14d9b80
    // 0x14d9b40: StoreField: r1->field_7 = rNULL
    //     0x14d9b40: stur            NULL, [x1, #7]
    // 0x14d9b44: b               #0x14d9b80
    // 0x14d9b48: LoadField: r3 = r0->field_13
    //     0x14d9b48: ldur            w3, [x0, #0x13]
    // 0x14d9b4c: DecompressPointer r3
    //     0x14d9b4c: add             x3, x3, HEAP, lsl #32
    // 0x14d9b50: cmp             w3, NULL
    // 0x14d9b54: b.eq            #0x14d9b80
    // 0x14d9b58: mov             x0, x1
    // 0x14d9b5c: StoreField: r3->field_7 = r0
    //     0x14d9b5c: stur            w0, [x3, #7]
    //     0x14d9b60: ldurb           w16, [x3, #-1]
    //     0x14d9b64: ldurb           w17, [x0, #-1]
    //     0x14d9b68: and             x16, x17, x16, lsr #2
    //     0x14d9b6c: tst             x16, HEAP, lsr #32
    //     0x14d9b70: b.eq            #0x14d9b78
    //     0x14d9b74: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14d9b78: b               #0x14d9b80
    // 0x14d9b7c: ldur            x2, [fp, #-0x10]
    // 0x14d9b80: LoadField: r1 = r2->field_f
    //     0x14d9b80: ldur            w1, [x2, #0xf]
    // 0x14d9b84: DecompressPointer r1
    //     0x14d9b84: add             x1, x1, HEAP, lsl #32
    // 0x14d9b88: r0 = controller()
    //     0x14d9b88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9b8c: LoadField: r1 = r0->field_63
    //     0x14d9b8c: ldur            w1, [x0, #0x63]
    // 0x14d9b90: DecompressPointer r1
    //     0x14d9b90: add             x1, x1, HEAP, lsl #32
    // 0x14d9b94: r0 = value()
    //     0x14d9b94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9b98: LoadField: r1 = r0->field_b
    //     0x14d9b98: ldur            w1, [x0, #0xb]
    // 0x14d9b9c: DecompressPointer r1
    //     0x14d9b9c: add             x1, x1, HEAP, lsl #32
    // 0x14d9ba0: cmp             w1, NULL
    // 0x14d9ba4: b.eq            #0x14d9cd0
    // 0x14d9ba8: ldur            x2, [fp, #-0x18]
    // 0x14d9bac: ldur            x0, [fp, #-0x10]
    // 0x14d9bb0: LoadField: r1 = r0->field_f
    //     0x14d9bb0: ldur            w1, [x0, #0xf]
    // 0x14d9bb4: DecompressPointer r1
    //     0x14d9bb4: add             x1, x1, HEAP, lsl #32
    // 0x14d9bb8: LoadField: r3 = r1->field_27
    //     0x14d9bb8: ldur            w3, [x1, #0x27]
    // 0x14d9bbc: DecompressPointer r3
    //     0x14d9bbc: add             x3, x3, HEAP, lsl #32
    // 0x14d9bc0: stur            x3, [fp, #-8]
    // 0x14d9bc4: r0 = controller()
    //     0x14d9bc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9bc8: LoadField: r1 = r0->field_63
    //     0x14d9bc8: ldur            w1, [x0, #0x63]
    // 0x14d9bcc: DecompressPointer r1
    //     0x14d9bcc: add             x1, x1, HEAP, lsl #32
    // 0x14d9bd0: r0 = value()
    //     0x14d9bd0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9bd4: LoadField: r2 = r0->field_b
    //     0x14d9bd4: ldur            w2, [x0, #0xb]
    // 0x14d9bd8: DecompressPointer r2
    //     0x14d9bd8: add             x2, x2, HEAP, lsl #32
    // 0x14d9bdc: cmp             w2, NULL
    // 0x14d9be0: b.eq            #0x14da338
    // 0x14d9be4: ldur            x1, [fp, #-8]
    // 0x14d9be8: r0 = addAll()
    //     0x14d9be8: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x14d9bec: ldur            x0, [fp, #-0x10]
    // 0x14d9bf0: LoadField: r1 = r0->field_f
    //     0x14d9bf0: ldur            w1, [x0, #0xf]
    // 0x14d9bf4: DecompressPointer r1
    //     0x14d9bf4: add             x1, x1, HEAP, lsl #32
    // 0x14d9bf8: LoadField: r2 = r1->field_27
    //     0x14d9bf8: ldur            w2, [x1, #0x27]
    // 0x14d9bfc: DecompressPointer r2
    //     0x14d9bfc: add             x2, x2, HEAP, lsl #32
    // 0x14d9c00: ldur            x3, [fp, #-0x18]
    // 0x14d9c04: LoadField: r1 = r3->field_f
    //     0x14d9c04: ldur            w1, [x3, #0xf]
    // 0x14d9c08: DecompressPointer r1
    //     0x14d9c08: add             x1, x1, HEAP, lsl #32
    // 0x14d9c0c: mov             x16, x1
    // 0x14d9c10: mov             x1, x2
    // 0x14d9c14: mov             x2, x16
    // 0x14d9c18: r0 = contains()
    //     0x14d9c18: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x14d9c1c: tbnz            w0, #4, #0x14d9c50
    // 0x14d9c20: ldur            x3, [fp, #-0x18]
    // 0x14d9c24: ldur            x0, [fp, #-0x10]
    // 0x14d9c28: LoadField: r1 = r0->field_f
    //     0x14d9c28: ldur            w1, [x0, #0xf]
    // 0x14d9c2c: DecompressPointer r1
    //     0x14d9c2c: add             x1, x1, HEAP, lsl #32
    // 0x14d9c30: LoadField: r2 = r1->field_27
    //     0x14d9c30: ldur            w2, [x1, #0x27]
    // 0x14d9c34: DecompressPointer r2
    //     0x14d9c34: add             x2, x2, HEAP, lsl #32
    // 0x14d9c38: LoadField: r1 = r3->field_f
    //     0x14d9c38: ldur            w1, [x3, #0xf]
    // 0x14d9c3c: DecompressPointer r1
    //     0x14d9c3c: add             x1, x1, HEAP, lsl #32
    // 0x14d9c40: mov             x16, x1
    // 0x14d9c44: mov             x1, x2
    // 0x14d9c48: mov             x2, x16
    // 0x14d9c4c: r0 = remove()
    //     0x14d9c4c: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x14d9c50: ldur            x0, [fp, #-0x10]
    // 0x14d9c54: LoadField: r1 = r0->field_f
    //     0x14d9c54: ldur            w1, [x0, #0xf]
    // 0x14d9c58: DecompressPointer r1
    //     0x14d9c58: add             x1, x1, HEAP, lsl #32
    // 0x14d9c5c: LoadField: r2 = r1->field_27
    //     0x14d9c5c: ldur            w2, [x1, #0x27]
    // 0x14d9c60: DecompressPointer r2
    //     0x14d9c60: add             x2, x2, HEAP, lsl #32
    // 0x14d9c64: LoadField: r3 = r2->field_13
    //     0x14d9c64: ldur            w3, [x2, #0x13]
    // 0x14d9c68: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x14d9c68: ldur            w4, [x2, #0x17]
    // 0x14d9c6c: r5 = LoadInt32Instr(r3)
    //     0x14d9c6c: sbfx            x5, x3, #1, #0x1f
    // 0x14d9c70: r3 = LoadInt32Instr(r4)
    //     0x14d9c70: sbfx            x3, x4, #1, #0x1f
    // 0x14d9c74: sub             x4, x5, x3
    // 0x14d9c78: cbnz            x4, #0x14d9c94
    // 0x14d9c7c: LoadField: r2 = r1->field_13
    //     0x14d9c7c: ldur            w2, [x1, #0x13]
    // 0x14d9c80: DecompressPointer r2
    //     0x14d9c80: add             x2, x2, HEAP, lsl #32
    // 0x14d9c84: cmp             w2, NULL
    // 0x14d9c88: b.eq            #0x14d9cd0
    // 0x14d9c8c: StoreField: r2->field_f = rNULL
    //     0x14d9c8c: stur            NULL, [x2, #0xf]
    // 0x14d9c90: b               #0x14d9cd0
    // 0x14d9c94: LoadField: r3 = r1->field_13
    //     0x14d9c94: ldur            w3, [x1, #0x13]
    // 0x14d9c98: DecompressPointer r3
    //     0x14d9c98: add             x3, x3, HEAP, lsl #32
    // 0x14d9c9c: stur            x3, [fp, #-8]
    // 0x14d9ca0: cmp             w3, NULL
    // 0x14d9ca4: b.eq            #0x14d9cd0
    // 0x14d9ca8: mov             x1, x2
    // 0x14d9cac: r0 = toSet()
    //     0x14d9cac: bl              #0x7d6b1c  ; [dart:_compact_hash] _Set::toSet
    // 0x14d9cb0: ldur            x1, [fp, #-8]
    // 0x14d9cb4: StoreField: r1->field_f = r0
    //     0x14d9cb4: stur            w0, [x1, #0xf]
    //     0x14d9cb8: ldurb           w16, [x1, #-1]
    //     0x14d9cbc: ldurb           w17, [x0, #-1]
    //     0x14d9cc0: and             x16, x17, x16, lsr #2
    //     0x14d9cc4: tst             x16, HEAP, lsr #32
    //     0x14d9cc8: b.eq            #0x14d9cd0
    //     0x14d9ccc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14d9cd0: ldur            x0, [fp, #-0x10]
    // 0x14d9cd4: LoadField: r1 = r0->field_f
    //     0x14d9cd4: ldur            w1, [x0, #0xf]
    // 0x14d9cd8: DecompressPointer r1
    //     0x14d9cd8: add             x1, x1, HEAP, lsl #32
    // 0x14d9cdc: r0 = controller()
    //     0x14d9cdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9ce0: LoadField: r1 = r0->field_63
    //     0x14d9ce0: ldur            w1, [x0, #0x63]
    // 0x14d9ce4: DecompressPointer r1
    //     0x14d9ce4: add             x1, x1, HEAP, lsl #32
    // 0x14d9ce8: r0 = value()
    //     0x14d9ce8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9cec: LoadField: r1 = r0->field_f
    //     0x14d9cec: ldur            w1, [x0, #0xf]
    // 0x14d9cf0: DecompressPointer r1
    //     0x14d9cf0: add             x1, x1, HEAP, lsl #32
    // 0x14d9cf4: cmp             w1, NULL
    // 0x14d9cf8: b.eq            #0x14d9e1c
    // 0x14d9cfc: ldur            x2, [fp, #-0x18]
    // 0x14d9d00: ldur            x0, [fp, #-0x10]
    // 0x14d9d04: LoadField: r1 = r0->field_f
    //     0x14d9d04: ldur            w1, [x0, #0xf]
    // 0x14d9d08: DecompressPointer r1
    //     0x14d9d08: add             x1, x1, HEAP, lsl #32
    // 0x14d9d0c: LoadField: r3 = r1->field_1b
    //     0x14d9d0c: ldur            w3, [x1, #0x1b]
    // 0x14d9d10: DecompressPointer r3
    //     0x14d9d10: add             x3, x3, HEAP, lsl #32
    // 0x14d9d14: stur            x3, [fp, #-8]
    // 0x14d9d18: r0 = controller()
    //     0x14d9d18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9d1c: LoadField: r1 = r0->field_63
    //     0x14d9d1c: ldur            w1, [x0, #0x63]
    // 0x14d9d20: DecompressPointer r1
    //     0x14d9d20: add             x1, x1, HEAP, lsl #32
    // 0x14d9d24: r0 = value()
    //     0x14d9d24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9d28: LoadField: r2 = r0->field_f
    //     0x14d9d28: ldur            w2, [x0, #0xf]
    // 0x14d9d2c: DecompressPointer r2
    //     0x14d9d2c: add             x2, x2, HEAP, lsl #32
    // 0x14d9d30: cmp             w2, NULL
    // 0x14d9d34: b.eq            #0x14da33c
    // 0x14d9d38: ldur            x1, [fp, #-8]
    // 0x14d9d3c: r0 = addAll()
    //     0x14d9d3c: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x14d9d40: ldur            x0, [fp, #-0x10]
    // 0x14d9d44: LoadField: r1 = r0->field_f
    //     0x14d9d44: ldur            w1, [x0, #0xf]
    // 0x14d9d48: DecompressPointer r1
    //     0x14d9d48: add             x1, x1, HEAP, lsl #32
    // 0x14d9d4c: LoadField: r2 = r1->field_1b
    //     0x14d9d4c: ldur            w2, [x1, #0x1b]
    // 0x14d9d50: DecompressPointer r2
    //     0x14d9d50: add             x2, x2, HEAP, lsl #32
    // 0x14d9d54: ldur            x3, [fp, #-0x18]
    // 0x14d9d58: LoadField: r1 = r3->field_f
    //     0x14d9d58: ldur            w1, [x3, #0xf]
    // 0x14d9d5c: DecompressPointer r1
    //     0x14d9d5c: add             x1, x1, HEAP, lsl #32
    // 0x14d9d60: mov             x16, x1
    // 0x14d9d64: mov             x1, x2
    // 0x14d9d68: mov             x2, x16
    // 0x14d9d6c: r0 = contains()
    //     0x14d9d6c: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x14d9d70: tbnz            w0, #4, #0x14d9da4
    // 0x14d9d74: ldur            x3, [fp, #-0x18]
    // 0x14d9d78: ldur            x0, [fp, #-0x10]
    // 0x14d9d7c: LoadField: r1 = r0->field_f
    //     0x14d9d7c: ldur            w1, [x0, #0xf]
    // 0x14d9d80: DecompressPointer r1
    //     0x14d9d80: add             x1, x1, HEAP, lsl #32
    // 0x14d9d84: LoadField: r2 = r1->field_1b
    //     0x14d9d84: ldur            w2, [x1, #0x1b]
    // 0x14d9d88: DecompressPointer r2
    //     0x14d9d88: add             x2, x2, HEAP, lsl #32
    // 0x14d9d8c: LoadField: r1 = r3->field_f
    //     0x14d9d8c: ldur            w1, [x3, #0xf]
    // 0x14d9d90: DecompressPointer r1
    //     0x14d9d90: add             x1, x1, HEAP, lsl #32
    // 0x14d9d94: mov             x16, x1
    // 0x14d9d98: mov             x1, x2
    // 0x14d9d9c: mov             x2, x16
    // 0x14d9da0: r0 = remove()
    //     0x14d9da0: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x14d9da4: ldur            x2, [fp, #-0x10]
    // 0x14d9da8: LoadField: r0 = r2->field_f
    //     0x14d9da8: ldur            w0, [x2, #0xf]
    // 0x14d9dac: DecompressPointer r0
    //     0x14d9dac: add             x0, x0, HEAP, lsl #32
    // 0x14d9db0: LoadField: r1 = r0->field_1b
    //     0x14d9db0: ldur            w1, [x0, #0x1b]
    // 0x14d9db4: DecompressPointer r1
    //     0x14d9db4: add             x1, x1, HEAP, lsl #32
    // 0x14d9db8: LoadField: r3 = r1->field_13
    //     0x14d9db8: ldur            w3, [x1, #0x13]
    // 0x14d9dbc: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x14d9dbc: ldur            w4, [x1, #0x17]
    // 0x14d9dc0: r5 = LoadInt32Instr(r3)
    //     0x14d9dc0: sbfx            x5, x3, #1, #0x1f
    // 0x14d9dc4: r3 = LoadInt32Instr(r4)
    //     0x14d9dc4: sbfx            x3, x4, #1, #0x1f
    // 0x14d9dc8: sub             x4, x5, x3
    // 0x14d9dcc: cbnz            x4, #0x14d9de8
    // 0x14d9dd0: LoadField: r1 = r0->field_13
    //     0x14d9dd0: ldur            w1, [x0, #0x13]
    // 0x14d9dd4: DecompressPointer r1
    //     0x14d9dd4: add             x1, x1, HEAP, lsl #32
    // 0x14d9dd8: cmp             w1, NULL
    // 0x14d9ddc: b.eq            #0x14d9e20
    // 0x14d9de0: StoreField: r1->field_b = rNULL
    //     0x14d9de0: stur            NULL, [x1, #0xb]
    // 0x14d9de4: b               #0x14d9e20
    // 0x14d9de8: LoadField: r3 = r0->field_13
    //     0x14d9de8: ldur            w3, [x0, #0x13]
    // 0x14d9dec: DecompressPointer r3
    //     0x14d9dec: add             x3, x3, HEAP, lsl #32
    // 0x14d9df0: cmp             w3, NULL
    // 0x14d9df4: b.eq            #0x14d9e20
    // 0x14d9df8: mov             x0, x1
    // 0x14d9dfc: StoreField: r3->field_b = r0
    //     0x14d9dfc: stur            w0, [x3, #0xb]
    //     0x14d9e00: ldurb           w16, [x3, #-1]
    //     0x14d9e04: ldurb           w17, [x0, #-1]
    //     0x14d9e08: and             x16, x17, x16, lsr #2
    //     0x14d9e0c: tst             x16, HEAP, lsr #32
    //     0x14d9e10: b.eq            #0x14d9e18
    //     0x14d9e14: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14d9e18: b               #0x14d9e20
    // 0x14d9e1c: ldur            x2, [fp, #-0x10]
    // 0x14d9e20: LoadField: r1 = r2->field_f
    //     0x14d9e20: ldur            w1, [x2, #0xf]
    // 0x14d9e24: DecompressPointer r1
    //     0x14d9e24: add             x1, x1, HEAP, lsl #32
    // 0x14d9e28: r0 = controller()
    //     0x14d9e28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9e2c: LoadField: r1 = r0->field_63
    //     0x14d9e2c: ldur            w1, [x0, #0x63]
    // 0x14d9e30: DecompressPointer r1
    //     0x14d9e30: add             x1, x1, HEAP, lsl #32
    // 0x14d9e34: r0 = value()
    //     0x14d9e34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9e38: LoadField: r1 = r0->field_13
    //     0x14d9e38: ldur            w1, [x0, #0x13]
    // 0x14d9e3c: DecompressPointer r1
    //     0x14d9e3c: add             x1, x1, HEAP, lsl #32
    // 0x14d9e40: cmp             w1, NULL
    // 0x14d9e44: b.eq            #0x14da18c
    // 0x14d9e48: ldur            x0, [fp, #-0x10]
    // 0x14d9e4c: LoadField: r1 = r0->field_f
    //     0x14d9e4c: ldur            w1, [x0, #0xf]
    // 0x14d9e50: DecompressPointer r1
    //     0x14d9e50: add             x1, x1, HEAP, lsl #32
    // 0x14d9e54: LoadField: r2 = r1->field_23
    //     0x14d9e54: ldur            w2, [x1, #0x23]
    // 0x14d9e58: DecompressPointer r2
    //     0x14d9e58: add             x2, x2, HEAP, lsl #32
    // 0x14d9e5c: stur            x2, [fp, #-8]
    // 0x14d9e60: r0 = controller()
    //     0x14d9e60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d9e64: LoadField: r1 = r0->field_63
    //     0x14d9e64: ldur            w1, [x0, #0x63]
    // 0x14d9e68: DecompressPointer r1
    //     0x14d9e68: add             x1, x1, HEAP, lsl #32
    // 0x14d9e6c: r0 = value()
    //     0x14d9e6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d9e70: LoadField: r1 = r0->field_13
    //     0x14d9e70: ldur            w1, [x0, #0x13]
    // 0x14d9e74: DecompressPointer r1
    //     0x14d9e74: add             x1, x1, HEAP, lsl #32
    // 0x14d9e78: cmp             w1, NULL
    // 0x14d9e7c: b.ne            #0x14d9ec4
    // 0x14d9e80: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x14d9e80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d9e84: ldr             x0, [x0]
    //     0x14d9e88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d9e8c: cmp             w0, w16
    //     0x14d9e90: b.ne            #0x14d9e9c
    //     0x14d9e94: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x14d9e98: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14d9e9c: r1 = <PriceRangeFilter>
    //     0x14d9e9c: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb08] TypeArguments: <PriceRangeFilter>
    //     0x14d9ea0: ldr             x1, [x1, #0xb08]
    // 0x14d9ea4: stur            x0, [fp, #-0x30]
    // 0x14d9ea8: r0 = AllocateGrowableArray()
    //     0x14d9ea8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d9eac: mov             x1, x0
    // 0x14d9eb0: ldur            x0, [fp, #-0x30]
    // 0x14d9eb4: StoreField: r1->field_f = r0
    //     0x14d9eb4: stur            w0, [x1, #0xf]
    // 0x14d9eb8: StoreField: r1->field_b = rZR
    //     0x14d9eb8: stur            wzr, [x1, #0xb]
    // 0x14d9ebc: mov             x2, x1
    // 0x14d9ec0: b               #0x14d9ec8
    // 0x14d9ec4: mov             x2, x1
    // 0x14d9ec8: ldur            x0, [fp, #-0x18]
    // 0x14d9ecc: ldur            x1, [fp, #-8]
    // 0x14d9ed0: r0 = addAll()
    //     0x14d9ed0: bl              #0x69d1dc  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::addAll
    // 0x14d9ed4: ldur            x0, [fp, #-0x18]
    // 0x14d9ed8: LoadField: r1 = r0->field_f
    //     0x14d9ed8: ldur            w1, [x0, #0xf]
    // 0x14d9edc: DecompressPointer r1
    //     0x14d9edc: add             x1, x1, HEAP, lsl #32
    // 0x14d9ee0: r0 = LoadClassIdInstr(r1)
    //     0x14d9ee0: ldur            x0, [x1, #-1]
    //     0x14d9ee4: ubfx            x0, x0, #0xc, #0x14
    // 0x14d9ee8: r2 = " - "
    //     0x14d9ee8: add             x2, PP, #0x3b, lsl #12  ; [pp+0x3bc08] " - "
    //     0x14d9eec: ldr             x2, [x2, #0xc08]
    // 0x14d9ef0: r0 = GDT[cid_x0 + -0xffc]()
    //     0x14d9ef0: sub             lr, x0, #0xffc
    //     0x14d9ef4: ldr             lr, [x21, lr, lsl #3]
    //     0x14d9ef8: blr             lr
    // 0x14d9efc: mov             x2, x0
    // 0x14d9f00: stur            x2, [fp, #-8]
    // 0x14d9f04: LoadField: r0 = r2->field_b
    //     0x14d9f04: ldur            w0, [x2, #0xb]
    // 0x14d9f08: r1 = LoadInt32Instr(r0)
    //     0x14d9f08: sbfx            x1, x0, #1, #0x1f
    // 0x14d9f0c: mov             x0, x1
    // 0x14d9f10: r1 = 1
    //     0x14d9f10: movz            x1, #0x1
    // 0x14d9f14: cmp             x1, x0
    // 0x14d9f18: b.hs            #0x14da340
    // 0x14d9f1c: LoadField: r0 = r2->field_f
    //     0x14d9f1c: ldur            w0, [x2, #0xf]
    // 0x14d9f20: DecompressPointer r0
    //     0x14d9f20: add             x0, x0, HEAP, lsl #32
    // 0x14d9f24: LoadField: r1 = r0->field_13
    //     0x14d9f24: ldur            w1, [x0, #0x13]
    // 0x14d9f28: DecompressPointer r1
    //     0x14d9f28: add             x1, x1, HEAP, lsl #32
    // 0x14d9f2c: r0 = LoadClassIdInstr(r1)
    //     0x14d9f2c: ldur            x0, [x1, #-1]
    //     0x14d9f30: ubfx            x0, x0, #0xc, #0x14
    // 0x14d9f34: r16 = "above"
    //     0x14d9f34: add             x16, PP, #0x3b, lsl #12  ; [pp+0x3bc10] "above"
    //     0x14d9f38: ldr             x16, [x16, #0xc10]
    // 0x14d9f3c: stp             x16, x1, [SP]
    // 0x14d9f40: mov             lr, x0
    // 0x14d9f44: ldr             lr, [x21, lr, lsl #3]
    // 0x14d9f48: blr             lr
    // 0x14d9f4c: tbnz            w0, #4, #0x14d9fe0
    // 0x14d9f50: ldur            x3, [fp, #-0x10]
    // 0x14d9f54: ldur            x2, [fp, #-8]
    // 0x14d9f58: LoadField: r0 = r3->field_f
    //     0x14d9f58: ldur            w0, [x3, #0xf]
    // 0x14d9f5c: DecompressPointer r0
    //     0x14d9f5c: add             x0, x0, HEAP, lsl #32
    // 0x14d9f60: LoadField: r4 = r0->field_1f
    //     0x14d9f60: ldur            w4, [x0, #0x1f]
    // 0x14d9f64: DecompressPointer r4
    //     0x14d9f64: add             x4, x4, HEAP, lsl #32
    // 0x14d9f68: stur            x4, [fp, #-0x18]
    // 0x14d9f6c: LoadField: r0 = r2->field_b
    //     0x14d9f6c: ldur            w0, [x2, #0xb]
    // 0x14d9f70: r1 = LoadInt32Instr(r0)
    //     0x14d9f70: sbfx            x1, x0, #1, #0x1f
    // 0x14d9f74: mov             x0, x1
    // 0x14d9f78: r1 = 0
    //     0x14d9f78: movz            x1, #0
    // 0x14d9f7c: cmp             x1, x0
    // 0x14d9f80: b.hs            #0x14da344
    // 0x14d9f84: LoadField: r0 = r2->field_f
    //     0x14d9f84: ldur            w0, [x2, #0xf]
    // 0x14d9f88: DecompressPointer r0
    //     0x14d9f88: add             x0, x0, HEAP, lsl #32
    // 0x14d9f8c: LoadField: r1 = r0->field_f
    //     0x14d9f8c: ldur            w1, [x0, #0xf]
    // 0x14d9f90: DecompressPointer r1
    //     0x14d9f90: add             x1, x1, HEAP, lsl #32
    // 0x14d9f94: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d9f94: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d9f98: r0 = tryParse()
    //     0x14d9f98: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x14d9f9c: ldur            x1, [fp, #-0x18]
    // 0x14d9fa0: StoreField: r1->field_7 = r0
    //     0x14d9fa0: stur            w0, [x1, #7]
    //     0x14d9fa4: tbz             w0, #0, #0x14d9fc0
    //     0x14d9fa8: ldurb           w16, [x1, #-1]
    //     0x14d9fac: ldurb           w17, [x0, #-1]
    //     0x14d9fb0: and             x16, x17, x16, lsr #2
    //     0x14d9fb4: tst             x16, HEAP, lsr #32
    //     0x14d9fb8: b.eq            #0x14d9fc0
    //     0x14d9fbc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14d9fc0: ldur            x3, [fp, #-0x10]
    // 0x14d9fc4: LoadField: r0 = r3->field_f
    //     0x14d9fc4: ldur            w0, [x3, #0xf]
    // 0x14d9fc8: DecompressPointer r0
    //     0x14d9fc8: add             x0, x0, HEAP, lsl #32
    // 0x14d9fcc: LoadField: r1 = r0->field_1f
    //     0x14d9fcc: ldur            w1, [x0, #0x1f]
    // 0x14d9fd0: DecompressPointer r1
    //     0x14d9fd0: add             x1, x1, HEAP, lsl #32
    // 0x14d9fd4: StoreField: r1->field_b = rNULL
    //     0x14d9fd4: stur            NULL, [x1, #0xb]
    // 0x14d9fd8: mov             x0, x3
    // 0x14d9fdc: b               #0x14da0c4
    // 0x14d9fe0: ldur            x3, [fp, #-0x10]
    // 0x14d9fe4: ldur            x2, [fp, #-8]
    // 0x14d9fe8: LoadField: r0 = r3->field_f
    //     0x14d9fe8: ldur            w0, [x3, #0xf]
    // 0x14d9fec: DecompressPointer r0
    //     0x14d9fec: add             x0, x0, HEAP, lsl #32
    // 0x14d9ff0: LoadField: r4 = r0->field_1f
    //     0x14d9ff0: ldur            w4, [x0, #0x1f]
    // 0x14d9ff4: DecompressPointer r4
    //     0x14d9ff4: add             x4, x4, HEAP, lsl #32
    // 0x14d9ff8: stur            x4, [fp, #-0x18]
    // 0x14d9ffc: LoadField: r0 = r2->field_b
    //     0x14d9ffc: ldur            w0, [x2, #0xb]
    // 0x14da000: r1 = LoadInt32Instr(r0)
    //     0x14da000: sbfx            x1, x0, #1, #0x1f
    // 0x14da004: mov             x0, x1
    // 0x14da008: r1 = 0
    //     0x14da008: movz            x1, #0
    // 0x14da00c: cmp             x1, x0
    // 0x14da010: b.hs            #0x14da348
    // 0x14da014: LoadField: r0 = r2->field_f
    //     0x14da014: ldur            w0, [x2, #0xf]
    // 0x14da018: DecompressPointer r0
    //     0x14da018: add             x0, x0, HEAP, lsl #32
    // 0x14da01c: LoadField: r1 = r0->field_f
    //     0x14da01c: ldur            w1, [x0, #0xf]
    // 0x14da020: DecompressPointer r1
    //     0x14da020: add             x1, x1, HEAP, lsl #32
    // 0x14da024: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14da024: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14da028: r0 = tryParse()
    //     0x14da028: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x14da02c: ldur            x1, [fp, #-0x18]
    // 0x14da030: StoreField: r1->field_7 = r0
    //     0x14da030: stur            w0, [x1, #7]
    //     0x14da034: tbz             w0, #0, #0x14da050
    //     0x14da038: ldurb           w16, [x1, #-1]
    //     0x14da03c: ldurb           w17, [x0, #-1]
    //     0x14da040: and             x16, x17, x16, lsr #2
    //     0x14da044: tst             x16, HEAP, lsr #32
    //     0x14da048: b.eq            #0x14da050
    //     0x14da04c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14da050: ldur            x2, [fp, #-0x10]
    // 0x14da054: LoadField: r0 = r2->field_f
    //     0x14da054: ldur            w0, [x2, #0xf]
    // 0x14da058: DecompressPointer r0
    //     0x14da058: add             x0, x0, HEAP, lsl #32
    // 0x14da05c: LoadField: r3 = r0->field_1f
    //     0x14da05c: ldur            w3, [x0, #0x1f]
    // 0x14da060: DecompressPointer r3
    //     0x14da060: add             x3, x3, HEAP, lsl #32
    // 0x14da064: ldur            x4, [fp, #-8]
    // 0x14da068: stur            x3, [fp, #-0x18]
    // 0x14da06c: LoadField: r0 = r4->field_b
    //     0x14da06c: ldur            w0, [x4, #0xb]
    // 0x14da070: r1 = LoadInt32Instr(r0)
    //     0x14da070: sbfx            x1, x0, #1, #0x1f
    // 0x14da074: mov             x0, x1
    // 0x14da078: r1 = 1
    //     0x14da078: movz            x1, #0x1
    // 0x14da07c: cmp             x1, x0
    // 0x14da080: b.hs            #0x14da34c
    // 0x14da084: LoadField: r0 = r4->field_f
    //     0x14da084: ldur            w0, [x4, #0xf]
    // 0x14da088: DecompressPointer r0
    //     0x14da088: add             x0, x0, HEAP, lsl #32
    // 0x14da08c: LoadField: r1 = r0->field_13
    //     0x14da08c: ldur            w1, [x0, #0x13]
    // 0x14da090: DecompressPointer r1
    //     0x14da090: add             x1, x1, HEAP, lsl #32
    // 0x14da094: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14da094: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14da098: r0 = tryParse()
    //     0x14da098: bl              #0x62d820  ; [dart:core] int::tryParse
    // 0x14da09c: ldur            x1, [fp, #-0x18]
    // 0x14da0a0: StoreField: r1->field_b = r0
    //     0x14da0a0: stur            w0, [x1, #0xb]
    //     0x14da0a4: tbz             w0, #0, #0x14da0c0
    //     0x14da0a8: ldurb           w16, [x1, #-1]
    //     0x14da0ac: ldurb           w17, [x0, #-1]
    //     0x14da0b0: and             x16, x17, x16, lsr #2
    //     0x14da0b4: tst             x16, HEAP, lsr #32
    //     0x14da0b8: b.eq            #0x14da0c0
    //     0x14da0bc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14da0c0: ldur            x0, [fp, #-0x10]
    // 0x14da0c4: LoadField: r1 = r0->field_f
    //     0x14da0c4: ldur            w1, [x0, #0xf]
    // 0x14da0c8: DecompressPointer r1
    //     0x14da0c8: add             x1, x1, HEAP, lsl #32
    // 0x14da0cc: LoadField: r2 = r1->field_23
    //     0x14da0cc: ldur            w2, [x1, #0x23]
    // 0x14da0d0: DecompressPointer r2
    //     0x14da0d0: add             x2, x2, HEAP, lsl #32
    // 0x14da0d4: LoadField: r3 = r1->field_1f
    //     0x14da0d4: ldur            w3, [x1, #0x1f]
    // 0x14da0d8: DecompressPointer r3
    //     0x14da0d8: add             x3, x3, HEAP, lsl #32
    // 0x14da0dc: mov             x1, x2
    // 0x14da0e0: mov             x2, x3
    // 0x14da0e4: r0 = contains()
    //     0x14da0e4: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0x14da0e8: tbnz            w0, #4, #0x14da114
    // 0x14da0ec: ldur            x0, [fp, #-0x10]
    // 0x14da0f0: LoadField: r1 = r0->field_f
    //     0x14da0f0: ldur            w1, [x0, #0xf]
    // 0x14da0f4: DecompressPointer r1
    //     0x14da0f4: add             x1, x1, HEAP, lsl #32
    // 0x14da0f8: LoadField: r2 = r1->field_23
    //     0x14da0f8: ldur            w2, [x1, #0x23]
    // 0x14da0fc: DecompressPointer r2
    //     0x14da0fc: add             x2, x2, HEAP, lsl #32
    // 0x14da100: LoadField: r3 = r1->field_1f
    //     0x14da100: ldur            w3, [x1, #0x1f]
    // 0x14da104: DecompressPointer r3
    //     0x14da104: add             x3, x3, HEAP, lsl #32
    // 0x14da108: mov             x1, x2
    // 0x14da10c: mov             x2, x3
    // 0x14da110: r0 = remove()
    //     0x14da110: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0x14da114: ldur            x2, [fp, #-0x10]
    // 0x14da118: LoadField: r0 = r2->field_f
    //     0x14da118: ldur            w0, [x2, #0xf]
    // 0x14da11c: DecompressPointer r0
    //     0x14da11c: add             x0, x0, HEAP, lsl #32
    // 0x14da120: LoadField: r1 = r0->field_23
    //     0x14da120: ldur            w1, [x0, #0x23]
    // 0x14da124: DecompressPointer r1
    //     0x14da124: add             x1, x1, HEAP, lsl #32
    // 0x14da128: LoadField: r3 = r1->field_13
    //     0x14da128: ldur            w3, [x1, #0x13]
    // 0x14da12c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x14da12c: ldur            w4, [x1, #0x17]
    // 0x14da130: r5 = LoadInt32Instr(r3)
    //     0x14da130: sbfx            x5, x3, #1, #0x1f
    // 0x14da134: r3 = LoadInt32Instr(r4)
    //     0x14da134: sbfx            x3, x4, #1, #0x1f
    // 0x14da138: sub             x4, x5, x3
    // 0x14da13c: cbnz            x4, #0x14da158
    // 0x14da140: LoadField: r1 = r0->field_13
    //     0x14da140: ldur            w1, [x0, #0x13]
    // 0x14da144: DecompressPointer r1
    //     0x14da144: add             x1, x1, HEAP, lsl #32
    // 0x14da148: cmp             w1, NULL
    // 0x14da14c: b.eq            #0x14da190
    // 0x14da150: StoreField: r1->field_13 = rNULL
    //     0x14da150: stur            NULL, [x1, #0x13]
    // 0x14da154: b               #0x14da190
    // 0x14da158: LoadField: r3 = r0->field_13
    //     0x14da158: ldur            w3, [x0, #0x13]
    // 0x14da15c: DecompressPointer r3
    //     0x14da15c: add             x3, x3, HEAP, lsl #32
    // 0x14da160: cmp             w3, NULL
    // 0x14da164: b.eq            #0x14da190
    // 0x14da168: mov             x0, x1
    // 0x14da16c: StoreField: r3->field_13 = r0
    //     0x14da16c: stur            w0, [x3, #0x13]
    //     0x14da170: ldurb           w16, [x3, #-1]
    //     0x14da174: ldurb           w17, [x0, #-1]
    //     0x14da178: and             x16, x17, x16, lsr #2
    //     0x14da17c: tst             x16, HEAP, lsr #32
    //     0x14da180: b.eq            #0x14da188
    //     0x14da184: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x14da188: b               #0x14da190
    // 0x14da18c: ldur            x2, [fp, #-0x10]
    // 0x14da190: LoadField: r1 = r2->field_f
    //     0x14da190: ldur            w1, [x2, #0xf]
    // 0x14da194: DecompressPointer r1
    //     0x14da194: add             x1, x1, HEAP, lsl #32
    // 0x14da198: LoadField: r0 = r1->field_13
    //     0x14da198: ldur            w0, [x1, #0x13]
    // 0x14da19c: DecompressPointer r0
    //     0x14da19c: add             x0, x0, HEAP, lsl #32
    // 0x14da1a0: cmp             w0, NULL
    // 0x14da1a4: b.eq            #0x14da1bc
    // 0x14da1a8: LoadField: r3 = r0->field_13
    //     0x14da1a8: ldur            w3, [x0, #0x13]
    // 0x14da1ac: DecompressPointer r3
    //     0x14da1ac: add             x3, x3, HEAP, lsl #32
    // 0x14da1b0: cmp             w3, NULL
    // 0x14da1b4: b.ne            #0x14da1bc
    // 0x14da1b8: StoreField: r0->field_13 = rNULL
    //     0x14da1b8: stur            NULL, [x0, #0x13]
    // 0x14da1bc: cmp             w0, NULL
    // 0x14da1c0: b.eq            #0x14da1d8
    // 0x14da1c4: LoadField: r3 = r0->field_f
    //     0x14da1c4: ldur            w3, [x0, #0xf]
    // 0x14da1c8: DecompressPointer r3
    //     0x14da1c8: add             x3, x3, HEAP, lsl #32
    // 0x14da1cc: cmp             w3, NULL
    // 0x14da1d0: b.ne            #0x14da1d8
    // 0x14da1d4: StoreField: r0->field_f = rNULL
    //     0x14da1d4: stur            NULL, [x0, #0xf]
    // 0x14da1d8: cmp             w0, NULL
    // 0x14da1dc: b.eq            #0x14da1f4
    // 0x14da1e0: LoadField: r3 = r0->field_b
    //     0x14da1e0: ldur            w3, [x0, #0xb]
    // 0x14da1e4: DecompressPointer r3
    //     0x14da1e4: add             x3, x3, HEAP, lsl #32
    // 0x14da1e8: cmp             w3, NULL
    // 0x14da1ec: b.ne            #0x14da1f4
    // 0x14da1f0: StoreField: r0->field_b = rNULL
    //     0x14da1f0: stur            NULL, [x0, #0xb]
    // 0x14da1f4: cmp             w0, NULL
    // 0x14da1f8: b.eq            #0x14da210
    // 0x14da1fc: LoadField: r3 = r0->field_7
    //     0x14da1fc: ldur            w3, [x0, #7]
    // 0x14da200: DecompressPointer r3
    //     0x14da200: add             x3, x3, HEAP, lsl #32
    // 0x14da204: cmp             w3, NULL
    // 0x14da208: b.ne            #0x14da210
    // 0x14da20c: StoreField: r0->field_7 = rNULL
    //     0x14da20c: stur            NULL, [x0, #7]
    // 0x14da210: cmp             w0, NULL
    // 0x14da214: b.eq            #0x14da228
    // 0x14da218: LoadField: r3 = r0->field_13
    //     0x14da218: ldur            w3, [x0, #0x13]
    // 0x14da21c: DecompressPointer r3
    //     0x14da21c: add             x3, x3, HEAP, lsl #32
    // 0x14da220: cmp             w3, NULL
    // 0x14da224: b.ne            #0x14da278
    // 0x14da228: cmp             w0, NULL
    // 0x14da22c: b.eq            #0x14da240
    // 0x14da230: LoadField: r3 = r0->field_f
    //     0x14da230: ldur            w3, [x0, #0xf]
    // 0x14da234: DecompressPointer r3
    //     0x14da234: add             x3, x3, HEAP, lsl #32
    // 0x14da238: cmp             w3, NULL
    // 0x14da23c: b.ne            #0x14da278
    // 0x14da240: cmp             w0, NULL
    // 0x14da244: b.eq            #0x14da258
    // 0x14da248: LoadField: r3 = r0->field_b
    //     0x14da248: ldur            w3, [x0, #0xb]
    // 0x14da24c: DecompressPointer r3
    //     0x14da24c: add             x3, x3, HEAP, lsl #32
    // 0x14da250: cmp             w3, NULL
    // 0x14da254: b.ne            #0x14da278
    // 0x14da258: cmp             w0, NULL
    // 0x14da25c: b.eq            #0x14da270
    // 0x14da260: LoadField: r3 = r0->field_7
    //     0x14da260: ldur            w3, [x0, #7]
    // 0x14da264: DecompressPointer r3
    //     0x14da264: add             x3, x3, HEAP, lsl #32
    // 0x14da268: cmp             w3, NULL
    // 0x14da26c: b.ne            #0x14da278
    // 0x14da270: StoreField: r1->field_13 = rNULL
    //     0x14da270: stur            NULL, [x1, #0x13]
    // 0x14da274: r0 = Null
    //     0x14da274: mov             x0, NULL
    // 0x14da278: cmp             w0, NULL
    // 0x14da27c: b.eq            #0x14da2e8
    // 0x14da280: r0 = controller()
    //     0x14da280: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da284: LoadField: r1 = r0->field_57
    //     0x14da284: ldur            w1, [x0, #0x57]
    // 0x14da288: DecompressPointer r1
    //     0x14da288: add             x1, x1, HEAP, lsl #32
    // 0x14da28c: r0 = initRefresh()
    //     0x14da28c: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x14da290: ldur            x0, [fp, #-0x10]
    // 0x14da294: LoadField: r1 = r0->field_f
    //     0x14da294: ldur            w1, [x0, #0xf]
    // 0x14da298: DecompressPointer r1
    //     0x14da298: add             x1, x1, HEAP, lsl #32
    // 0x14da29c: r0 = controller()
    //     0x14da29c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da2a0: mov             x2, x0
    // 0x14da2a4: ldur            x0, [fp, #-0x10]
    // 0x14da2a8: stur            x2, [fp, #-0x18]
    // 0x14da2ac: LoadField: r1 = r0->field_f
    //     0x14da2ac: ldur            w1, [x0, #0xf]
    // 0x14da2b0: DecompressPointer r1
    //     0x14da2b0: add             x1, x1, HEAP, lsl #32
    // 0x14da2b4: LoadField: r0 = r1->field_13
    //     0x14da2b4: ldur            w0, [x1, #0x13]
    // 0x14da2b8: DecompressPointer r0
    //     0x14da2b8: add             x0, x0, HEAP, lsl #32
    // 0x14da2bc: stur            x0, [fp, #-8]
    // 0x14da2c0: r0 = controller()
    //     0x14da2c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da2c4: LoadField: r1 = r0->field_87
    //     0x14da2c4: ldur            w1, [x0, #0x87]
    // 0x14da2c8: DecompressPointer r1
    //     0x14da2c8: add             x1, x1, HEAP, lsl #32
    // 0x14da2cc: str             x1, [SP]
    // 0x14da2d0: ldur            x1, [fp, #-0x18]
    // 0x14da2d4: ldur            x2, [fp, #-8]
    // 0x14da2d8: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0x14da2d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0x14da2dc: ldr             x4, [x4, #0x220]
    // 0x14da2e0: r0 = getFilterResponse()
    //     0x14da2e0: bl              #0x13b9e7c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getFilterResponse
    // 0x14da2e4: b               #0x14da2f4
    // 0x14da2e8: r0 = controller()
    //     0x14da2e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da2ec: mov             x1, x0
    // 0x14da2f0: r0 = onRefreshPage()
    //     0x14da2f0: bl              #0x13b5d28  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::onRefreshPage
    // 0x14da2f4: r0 = Null
    //     0x14da2f4: mov             x0, NULL
    // 0x14da2f8: LeaveFrame
    //     0x14da2f8: mov             SP, fp
    //     0x14da2fc: ldp             fp, lr, [SP], #0x10
    // 0x14da300: ret
    //     0x14da300: ret             
    // 0x14da304: mov             x0, x3
    // 0x14da308: r0 = ConcurrentModificationError()
    //     0x14da308: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x14da30c: mov             x1, x0
    // 0x14da310: ldur            x0, [fp, #-0x38]
    // 0x14da314: StoreField: r1->field_b = r0
    //     0x14da314: stur            w0, [x1, #0xb]
    // 0x14da318: mov             x0, x1
    // 0x14da31c: r0 = Throw()
    //     0x14da31c: bl              #0x16f5420  ; ThrowStub
    // 0x14da320: brk             #0
    // 0x14da324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da324: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da328: b               #0x14d9818
    // 0x14da32c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14da32c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x14da330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da330: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da334: b               #0x14d99cc
    // 0x14da338: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14da338: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x14da33c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14da33c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x14da340: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14da340: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14da344: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14da344: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14da348: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14da348: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14da34c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14da34c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Filter?) {
    // ** addr: 0x14da350, size: 0xfc
    // 0x14da350: EnterFrame
    //     0x14da350: stp             fp, lr, [SP, #-0x10]!
    //     0x14da354: mov             fp, SP
    // 0x14da358: AllocStack(0x18)
    //     0x14da358: sub             SP, SP, #0x18
    // 0x14da35c: SetupParameters()
    //     0x14da35c: ldr             x0, [fp, #0x18]
    //     0x14da360: ldur            w2, [x0, #0x17]
    //     0x14da364: add             x2, x2, HEAP, lsl #32
    //     0x14da368: stur            x2, [fp, #-8]
    // 0x14da36c: CheckStackOverflow
    //     0x14da36c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da370: cmp             SP, x16
    //     0x14da374: b.ls            #0x14da444
    // 0x14da378: LoadField: r1 = r2->field_f
    //     0x14da378: ldur            w1, [x2, #0xf]
    // 0x14da37c: DecompressPointer r1
    //     0x14da37c: add             x1, x1, HEAP, lsl #32
    // 0x14da380: r0 = controller()
    //     0x14da380: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da384: LoadField: r3 = r0->field_77
    //     0x14da384: ldur            w3, [x0, #0x77]
    // 0x14da388: DecompressPointer r3
    //     0x14da388: add             x3, x3, HEAP, lsl #32
    // 0x14da38c: ldr             x0, [fp, #0x10]
    // 0x14da390: stur            x3, [fp, #-0x10]
    // 0x14da394: r2 = Null
    //     0x14da394: mov             x2, NULL
    // 0x14da398: r1 = Null
    //     0x14da398: mov             x1, NULL
    // 0x14da39c: r4 = LoadClassIdInstr(r0)
    //     0x14da39c: ldur            x4, [x0, #-1]
    //     0x14da3a0: ubfx            x4, x4, #0xc, #0x14
    // 0x14da3a4: r17 = 5367
    //     0x14da3a4: movz            x17, #0x14f7
    // 0x14da3a8: cmp             x4, x17
    // 0x14da3ac: b.eq            #0x14da3c4
    // 0x14da3b0: r8 = Filter
    //     0x14da3b0: add             x8, PP, #0x3b, lsl #12  ; [pp+0x3bc18] Type: Filter
    //     0x14da3b4: ldr             x8, [x8, #0xc18]
    // 0x14da3b8: r3 = Null
    //     0x14da3b8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40cb8] Null
    //     0x14da3bc: ldr             x3, [x3, #0xcb8]
    // 0x14da3c0: r0 = Filter()
    //     0x14da3c0: bl              #0x13b74c8  ; IsType_Filter_Stub
    // 0x14da3c4: ldur            x1, [fp, #-0x10]
    // 0x14da3c8: ldr             x2, [fp, #0x10]
    // 0x14da3cc: r0 = value=()
    //     0x14da3cc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14da3d0: ldur            x0, [fp, #-8]
    // 0x14da3d4: LoadField: r1 = r0->field_f
    //     0x14da3d4: ldur            w1, [x0, #0xf]
    // 0x14da3d8: DecompressPointer r1
    //     0x14da3d8: add             x1, x1, HEAP, lsl #32
    // 0x14da3dc: r0 = controller()
    //     0x14da3dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da3e0: LoadField: r1 = r0->field_57
    //     0x14da3e0: ldur            w1, [x0, #0x57]
    // 0x14da3e4: DecompressPointer r1
    //     0x14da3e4: add             x1, x1, HEAP, lsl #32
    // 0x14da3e8: r0 = initRefresh()
    //     0x14da3e8: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x14da3ec: ldur            x0, [fp, #-8]
    // 0x14da3f0: LoadField: r1 = r0->field_f
    //     0x14da3f0: ldur            w1, [x0, #0xf]
    // 0x14da3f4: DecompressPointer r1
    //     0x14da3f4: add             x1, x1, HEAP, lsl #32
    // 0x14da3f8: r0 = controller()
    //     0x14da3f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da3fc: mov             x1, x0
    // 0x14da400: ldur            x0, [fp, #-8]
    // 0x14da404: LoadField: r2 = r0->field_f
    //     0x14da404: ldur            w2, [x0, #0xf]
    // 0x14da408: DecompressPointer r2
    //     0x14da408: add             x2, x2, HEAP, lsl #32
    // 0x14da40c: LoadField: r0 = r2->field_13
    //     0x14da40c: ldur            w0, [x2, #0x13]
    // 0x14da410: DecompressPointer r0
    //     0x14da410: add             x0, x0, HEAP, lsl #32
    // 0x14da414: ldr             x2, [fp, #0x10]
    // 0x14da418: LoadField: r3 = r2->field_7
    //     0x14da418: ldur            w3, [x2, #7]
    // 0x14da41c: DecompressPointer r3
    //     0x14da41c: add             x3, x3, HEAP, lsl #32
    // 0x14da420: str             x3, [SP]
    // 0x14da424: mov             x2, x0
    // 0x14da428: r4 = const [0, 0x3, 0x1, 0x2, sortBy, 0x2, null]
    //     0x14da428: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f220] List(7) [0, 0x3, 0x1, 0x2, "sortBy", 0x2, Null]
    //     0x14da42c: ldr             x4, [x4, #0x220]
    // 0x14da430: r0 = getFilterResponse()
    //     0x14da430: bl              #0x13b9e7c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getFilterResponse
    // 0x14da434: r0 = Null
    //     0x14da434: mov             x0, NULL
    // 0x14da438: LeaveFrame
    //     0x14da438: mov             SP, fp
    //     0x14da43c: ldp             fp, lr, [SP], #0x10
    // 0x14da440: ret
    //     0x14da440: ret             
    // 0x14da444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da448: b               #0x14da378
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14da44c, size: 0x1dc
    // 0x14da44c: EnterFrame
    //     0x14da44c: stp             fp, lr, [SP, #-0x10]!
    //     0x14da450: mov             fp, SP
    // 0x14da454: AllocStack(0x38)
    //     0x14da454: sub             SP, SP, #0x38
    // 0x14da458: SetupParameters(CollectionPage this /* r1 */)
    //     0x14da458: stur            NULL, [fp, #-8]
    //     0x14da45c: movz            x0, #0
    //     0x14da460: add             x1, fp, w0, sxtw #2
    //     0x14da464: ldr             x1, [x1, #0x10]
    //     0x14da468: ldur            w2, [x1, #0x17]
    //     0x14da46c: add             x2, x2, HEAP, lsl #32
    //     0x14da470: stur            x2, [fp, #-0x10]
    // 0x14da474: CheckStackOverflow
    //     0x14da474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da478: cmp             SP, x16
    //     0x14da47c: b.ls            #0x14da620
    // 0x14da480: InitAsync() -> Future<void?>
    //     0x14da480: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x14da484: bl              #0x6326e0  ; InitAsyncStub
    // 0x14da488: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14da488: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14da48c: ldr             x0, [x0, #0x1c80]
    //     0x14da490: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14da494: cmp             w0, w16
    //     0x14da498: b.ne            #0x14da4a4
    //     0x14da49c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14da4a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14da4a4: ldur            x2, [fp, #-0x10]
    // 0x14da4a8: r1 = Function '<anonymous closure>':.
    //     0x14da4a8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40cc8] AnonymousClosure: (0x14da628), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14da4ac: ldr             x1, [x1, #0xcc8]
    // 0x14da4b0: r0 = AllocateClosure()
    //     0x14da4b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14da4b4: stp             x0, NULL, [SP]
    // 0x14da4b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x14da4b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x14da4bc: r0 = GetNavigation.to()
    //     0x14da4bc: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0x14da4c0: mov             x1, x0
    // 0x14da4c4: stur            x1, [fp, #-0x20]
    // 0x14da4c8: cmp             w1, NULL
    // 0x14da4cc: b.eq            #0x14da600
    // 0x14da4d0: ldur            x2, [fp, #-0x10]
    // 0x14da4d4: LoadField: r3 = r2->field_f
    //     0x14da4d4: ldur            w3, [x2, #0xf]
    // 0x14da4d8: DecompressPointer r3
    //     0x14da4d8: add             x3, x3, HEAP, lsl #32
    // 0x14da4dc: mov             x0, x1
    // 0x14da4e0: stur            x3, [fp, #-0x18]
    // 0x14da4e4: r0 = Await()
    //     0x14da4e4: bl              #0x63248c  ; AwaitStub
    // 0x14da4e8: mov             x3, x0
    // 0x14da4ec: r2 = Null
    //     0x14da4ec: mov             x2, NULL
    // 0x14da4f0: r1 = Null
    //     0x14da4f0: mov             x1, NULL
    // 0x14da4f4: stur            x3, [fp, #-0x28]
    // 0x14da4f8: r4 = 60
    //     0x14da4f8: movz            x4, #0x3c
    // 0x14da4fc: branchIfSmi(r0, 0x14da508)
    //     0x14da4fc: tbz             w0, #0, #0x14da508
    // 0x14da500: r4 = LoadClassIdInstr(r0)
    //     0x14da500: ldur            x4, [x0, #-1]
    //     0x14da504: ubfx            x4, x4, #0xc, #0x14
    // 0x14da508: r17 = 5353
    //     0x14da508: movz            x17, #0x14e9
    // 0x14da50c: cmp             x4, x17
    // 0x14da510: b.eq            #0x14da528
    // 0x14da514: r8 = FilterResponse?
    //     0x14da514: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c2f0] Type: FilterResponse?
    //     0x14da518: ldr             x8, [x8, #0x2f0]
    // 0x14da51c: r3 = Null
    //     0x14da51c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40cd0] Null
    //     0x14da520: ldr             x3, [x3, #0xcd0]
    // 0x14da524: r0 = DefaultNullableTypeTest()
    //     0x14da524: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x14da528: ldur            x0, [fp, #-0x28]
    // 0x14da52c: ldur            x1, [fp, #-0x18]
    // 0x14da530: StoreField: r1->field_13 = r0
    //     0x14da530: stur            w0, [x1, #0x13]
    //     0x14da534: ldurb           w16, [x1, #-1]
    //     0x14da538: ldurb           w17, [x0, #-1]
    //     0x14da53c: and             x16, x17, x16, lsr #2
    //     0x14da540: tst             x16, HEAP, lsr #32
    //     0x14da544: b.eq            #0x14da54c
    //     0x14da548: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x14da54c: ldur            x0, [fp, #-0x10]
    // 0x14da550: LoadField: r1 = r0->field_f
    //     0x14da550: ldur            w1, [x0, #0xf]
    // 0x14da554: DecompressPointer r1
    //     0x14da554: add             x1, x1, HEAP, lsl #32
    // 0x14da558: r0 = controller()
    //     0x14da558: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da55c: LoadField: r1 = r0->field_57
    //     0x14da55c: ldur            w1, [x0, #0x57]
    // 0x14da560: DecompressPointer r1
    //     0x14da560: add             x1, x1, HEAP, lsl #32
    // 0x14da564: r0 = initRefresh()
    //     0x14da564: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x14da568: ldur            x0, [fp, #-0x10]
    // 0x14da56c: LoadField: r1 = r0->field_f
    //     0x14da56c: ldur            w1, [x0, #0xf]
    // 0x14da570: DecompressPointer r1
    //     0x14da570: add             x1, x1, HEAP, lsl #32
    // 0x14da574: r0 = controller()
    //     0x14da574: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da578: mov             x1, x0
    // 0x14da57c: ldur            x0, [fp, #-0x20]
    // 0x14da580: stur            x1, [fp, #-0x18]
    // 0x14da584: r0 = Await()
    //     0x14da584: bl              #0x63248c  ; AwaitStub
    // 0x14da588: mov             x3, x0
    // 0x14da58c: r2 = Null
    //     0x14da58c: mov             x2, NULL
    // 0x14da590: r1 = Null
    //     0x14da590: mov             x1, NULL
    // 0x14da594: stur            x3, [fp, #-0x20]
    // 0x14da598: r4 = 60
    //     0x14da598: movz            x4, #0x3c
    // 0x14da59c: branchIfSmi(r0, 0x14da5a8)
    //     0x14da59c: tbz             w0, #0, #0x14da5a8
    // 0x14da5a0: r4 = LoadClassIdInstr(r0)
    //     0x14da5a0: ldur            x4, [x0, #-1]
    //     0x14da5a4: ubfx            x4, x4, #0xc, #0x14
    // 0x14da5a8: r17 = 5353
    //     0x14da5a8: movz            x17, #0x14e9
    // 0x14da5ac: cmp             x4, x17
    // 0x14da5b0: b.eq            #0x14da5c8
    // 0x14da5b4: r8 = FilterResponse?
    //     0x14da5b4: add             x8, PP, #0x3c, lsl #12  ; [pp+0x3c2f0] Type: FilterResponse?
    //     0x14da5b8: ldr             x8, [x8, #0x2f0]
    // 0x14da5bc: r3 = Null
    //     0x14da5bc: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ce0] Null
    //     0x14da5c0: ldr             x3, [x3, #0xce0]
    // 0x14da5c4: r0 = DefaultNullableTypeTest()
    //     0x14da5c4: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x14da5c8: ldur            x0, [fp, #-0x10]
    // 0x14da5cc: LoadField: r1 = r0->field_f
    //     0x14da5cc: ldur            w1, [x0, #0xf]
    // 0x14da5d0: DecompressPointer r1
    //     0x14da5d0: add             x1, x1, HEAP, lsl #32
    // 0x14da5d4: r0 = controller()
    //     0x14da5d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da5d8: LoadField: r1 = r0->field_87
    //     0x14da5d8: ldur            w1, [x0, #0x87]
    // 0x14da5dc: DecompressPointer r1
    //     0x14da5dc: add             x1, x1, HEAP, lsl #32
    // 0x14da5e0: r16 = true
    //     0x14da5e0: add             x16, NULL, #0x20  ; true
    // 0x14da5e4: stp             x1, x16, [SP]
    // 0x14da5e8: ldur            x1, [fp, #-0x18]
    // 0x14da5ec: ldur            x2, [fp, #-0x20]
    // 0x14da5f0: r4 = const [0, 0x4, 0x2, 0x2, canReset, 0x2, sortBy, 0x3, null]
    //     0x14da5f0: add             x4, PP, #0x3b, lsl #12  ; [pp+0x3bb88] List(9) [0, 0x4, 0x2, 0x2, "canReset", 0x2, "sortBy", 0x3, Null]
    //     0x14da5f4: ldr             x4, [x4, #0xb88]
    // 0x14da5f8: r0 = getCollectionResponse()
    //     0x14da5f8: bl              #0x13b5d8c  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::getCollectionResponse
    // 0x14da5fc: b               #0x14da618
    // 0x14da600: ldur            x0, [fp, #-0x10]
    // 0x14da604: LoadField: r1 = r0->field_f
    //     0x14da604: ldur            w1, [x0, #0xf]
    // 0x14da608: DecompressPointer r1
    //     0x14da608: add             x1, x1, HEAP, lsl #32
    // 0x14da60c: r0 = controller()
    //     0x14da60c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da610: mov             x1, x0
    // 0x14da614: r0 = onRefreshPage()
    //     0x14da614: bl              #0x13b5d28  ; [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::onRefreshPage
    // 0x14da618: r0 = Null
    //     0x14da618: mov             x0, NULL
    // 0x14da61c: r0 = ReturnAsyncNotFuture()
    //     0x14da61c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x14da620: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da620: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da624: b               #0x14da480
  }
  [closure] CollectionFilter <anonymous closure>(dynamic) {
    // ** addr: 0x14da628, size: 0xd0
    // 0x14da628: EnterFrame
    //     0x14da628: stp             fp, lr, [SP, #-0x10]!
    //     0x14da62c: mov             fp, SP
    // 0x14da630: AllocStack(0x20)
    //     0x14da630: sub             SP, SP, #0x20
    // 0x14da634: SetupParameters()
    //     0x14da634: ldr             x0, [fp, #0x10]
    //     0x14da638: ldur            w2, [x0, #0x17]
    //     0x14da63c: add             x2, x2, HEAP, lsl #32
    //     0x14da640: stur            x2, [fp, #-8]
    // 0x14da644: CheckStackOverflow
    //     0x14da644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da648: cmp             SP, x16
    //     0x14da64c: b.ls            #0x14da6f0
    // 0x14da650: LoadField: r1 = r2->field_f
    //     0x14da650: ldur            w1, [x2, #0xf]
    // 0x14da654: DecompressPointer r1
    //     0x14da654: add             x1, x1, HEAP, lsl #32
    // 0x14da658: r0 = controller()
    //     0x14da658: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da65c: LoadField: r1 = r0->field_67
    //     0x14da65c: ldur            w1, [x0, #0x67]
    // 0x14da660: DecompressPointer r1
    //     0x14da660: add             x1, x1, HEAP, lsl #32
    // 0x14da664: r0 = value()
    //     0x14da664: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14da668: LoadField: r1 = r0->field_b
    //     0x14da668: ldur            w1, [x0, #0xb]
    // 0x14da66c: DecompressPointer r1
    //     0x14da66c: add             x1, x1, HEAP, lsl #32
    // 0x14da670: cmp             w1, NULL
    // 0x14da674: b.ne            #0x14da680
    // 0x14da678: r0 = Null
    //     0x14da678: mov             x0, NULL
    // 0x14da67c: b               #0x14da688
    // 0x14da680: LoadField: r0 = r1->field_27
    //     0x14da680: ldur            w0, [x1, #0x27]
    // 0x14da684: DecompressPointer r0
    //     0x14da684: add             x0, x0, HEAP, lsl #32
    // 0x14da688: ldur            x2, [fp, #-8]
    // 0x14da68c: stur            x0, [fp, #-0x10]
    // 0x14da690: LoadField: r1 = r2->field_f
    //     0x14da690: ldur            w1, [x2, #0xf]
    // 0x14da694: DecompressPointer r1
    //     0x14da694: add             x1, x1, HEAP, lsl #32
    // 0x14da698: r0 = controller()
    //     0x14da698: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da69c: LoadField: r1 = r0->field_63
    //     0x14da69c: ldur            w1, [x0, #0x63]
    // 0x14da6a0: DecompressPointer r1
    //     0x14da6a0: add             x1, x1, HEAP, lsl #32
    // 0x14da6a4: r0 = value()
    //     0x14da6a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14da6a8: stur            x0, [fp, #-0x18]
    // 0x14da6ac: r0 = CollectionFilter()
    //     0x14da6ac: bl              #0x14da6f8  ; AllocateCollectionFilterStub -> CollectionFilter (size=0x18)
    // 0x14da6b0: mov             x3, x0
    // 0x14da6b4: ldur            x0, [fp, #-0x10]
    // 0x14da6b8: stur            x3, [fp, #-0x20]
    // 0x14da6bc: StoreField: r3->field_b = r0
    //     0x14da6bc: stur            w0, [x3, #0xb]
    // 0x14da6c0: ldur            x0, [fp, #-0x18]
    // 0x14da6c4: StoreField: r3->field_f = r0
    //     0x14da6c4: stur            w0, [x3, #0xf]
    // 0x14da6c8: ldur            x2, [fp, #-8]
    // 0x14da6cc: r1 = Function '<anonymous closure>':.
    //     0x14da6cc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40cf0] AnonymousClosure: (0x13ba568), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::body (0x1504b2c)
    //     0x14da6d0: ldr             x1, [x1, #0xcf0]
    // 0x14da6d4: r0 = AllocateClosure()
    //     0x14da6d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14da6d8: mov             x1, x0
    // 0x14da6dc: ldur            x0, [fp, #-0x20]
    // 0x14da6e0: StoreField: r0->field_13 = r1
    //     0x14da6e0: stur            w1, [x0, #0x13]
    // 0x14da6e4: LeaveFrame
    //     0x14da6e4: mov             SP, fp
    //     0x14da6e8: ldp             fp, lr, [SP], #0x10
    // 0x14da6ec: ret
    //     0x14da6ec: ret             
    // 0x14da6f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da6f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da6f4: b               #0x14da650
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x14da704, size: 0x164
    // 0x14da704: EnterFrame
    //     0x14da704: stp             fp, lr, [SP, #-0x10]!
    //     0x14da708: mov             fp, SP
    // 0x14da70c: AllocStack(0x20)
    //     0x14da70c: sub             SP, SP, #0x20
    // 0x14da710: SetupParameters()
    //     0x14da710: ldr             x0, [fp, #0x10]
    //     0x14da714: ldur            w2, [x0, #0x17]
    //     0x14da718: add             x2, x2, HEAP, lsl #32
    //     0x14da71c: stur            x2, [fp, #-8]
    // 0x14da720: CheckStackOverflow
    //     0x14da720: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da724: cmp             SP, x16
    //     0x14da728: b.ls            #0x14da85c
    // 0x14da72c: LoadField: r1 = r2->field_f
    //     0x14da72c: ldur            w1, [x2, #0xf]
    // 0x14da730: DecompressPointer r1
    //     0x14da730: add             x1, x1, HEAP, lsl #32
    // 0x14da734: r0 = controller()
    //     0x14da734: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da738: LoadField: r1 = r0->field_a3
    //     0x14da738: ldur            w1, [x0, #0xa3]
    // 0x14da73c: DecompressPointer r1
    //     0x14da73c: add             x1, x1, HEAP, lsl #32
    // 0x14da740: r0 = value()
    //     0x14da740: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14da744: tbnz            w0, #4, #0x14da84c
    // 0x14da748: ldur            x2, [fp, #-8]
    // 0x14da74c: LoadField: r1 = r2->field_f
    //     0x14da74c: ldur            w1, [x2, #0xf]
    // 0x14da750: DecompressPointer r1
    //     0x14da750: add             x1, x1, HEAP, lsl #32
    // 0x14da754: r0 = controller()
    //     0x14da754: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da758: LoadField: r1 = r0->field_a3
    //     0x14da758: ldur            w1, [x0, #0xa3]
    // 0x14da75c: DecompressPointer r1
    //     0x14da75c: add             x1, x1, HEAP, lsl #32
    // 0x14da760: r2 = false
    //     0x14da760: add             x2, NULL, #0x30  ; false
    // 0x14da764: r0 = value=()
    //     0x14da764: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14da768: r0 = LoadStaticField(0x878)
    //     0x14da768: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14da76c: ldr             x0, [x0, #0x10f0]
    // 0x14da770: cmp             w0, NULL
    // 0x14da774: b.eq            #0x14da864
    // 0x14da778: LoadField: r3 = r0->field_53
    //     0x14da778: ldur            w3, [x0, #0x53]
    // 0x14da77c: DecompressPointer r3
    //     0x14da77c: add             x3, x3, HEAP, lsl #32
    // 0x14da780: stur            x3, [fp, #-0x18]
    // 0x14da784: LoadField: r0 = r3->field_7
    //     0x14da784: ldur            w0, [x3, #7]
    // 0x14da788: DecompressPointer r0
    //     0x14da788: add             x0, x0, HEAP, lsl #32
    // 0x14da78c: ldur            x2, [fp, #-8]
    // 0x14da790: stur            x0, [fp, #-0x10]
    // 0x14da794: r1 = Function '<anonymous closure>':.
    //     0x14da794: add             x1, PP, #0x40, lsl #12  ; [pp+0x40cf8] AnonymousClosure: (0x14da868), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14da798: ldr             x1, [x1, #0xcf8]
    // 0x14da79c: r0 = AllocateClosure()
    //     0x14da79c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14da7a0: ldur            x2, [fp, #-0x10]
    // 0x14da7a4: mov             x3, x0
    // 0x14da7a8: r1 = Null
    //     0x14da7a8: mov             x1, NULL
    // 0x14da7ac: stur            x3, [fp, #-8]
    // 0x14da7b0: cmp             w2, NULL
    // 0x14da7b4: b.eq            #0x14da7d4
    // 0x14da7b8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x14da7b8: ldur            w4, [x2, #0x17]
    // 0x14da7bc: DecompressPointer r4
    //     0x14da7bc: add             x4, x4, HEAP, lsl #32
    // 0x14da7c0: r8 = X0
    //     0x14da7c0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x14da7c4: LoadField: r9 = r4->field_7
    //     0x14da7c4: ldur            x9, [x4, #7]
    // 0x14da7c8: r3 = Null
    //     0x14da7c8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d00] Null
    //     0x14da7cc: ldr             x3, [x3, #0xd00]
    // 0x14da7d0: blr             x9
    // 0x14da7d4: ldur            x0, [fp, #-0x18]
    // 0x14da7d8: LoadField: r1 = r0->field_b
    //     0x14da7d8: ldur            w1, [x0, #0xb]
    // 0x14da7dc: LoadField: r2 = r0->field_f
    //     0x14da7dc: ldur            w2, [x0, #0xf]
    // 0x14da7e0: DecompressPointer r2
    //     0x14da7e0: add             x2, x2, HEAP, lsl #32
    // 0x14da7e4: LoadField: r3 = r2->field_b
    //     0x14da7e4: ldur            w3, [x2, #0xb]
    // 0x14da7e8: r2 = LoadInt32Instr(r1)
    //     0x14da7e8: sbfx            x2, x1, #1, #0x1f
    // 0x14da7ec: stur            x2, [fp, #-0x20]
    // 0x14da7f0: r1 = LoadInt32Instr(r3)
    //     0x14da7f0: sbfx            x1, x3, #1, #0x1f
    // 0x14da7f4: cmp             x2, x1
    // 0x14da7f8: b.ne            #0x14da804
    // 0x14da7fc: mov             x1, x0
    // 0x14da800: r0 = _growToNextCapacity()
    //     0x14da800: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14da804: ldur            x2, [fp, #-0x18]
    // 0x14da808: ldur            x3, [fp, #-0x20]
    // 0x14da80c: add             x4, x3, #1
    // 0x14da810: lsl             x5, x4, #1
    // 0x14da814: StoreField: r2->field_b = r5
    //     0x14da814: stur            w5, [x2, #0xb]
    // 0x14da818: LoadField: r1 = r2->field_f
    //     0x14da818: ldur            w1, [x2, #0xf]
    // 0x14da81c: DecompressPointer r1
    //     0x14da81c: add             x1, x1, HEAP, lsl #32
    // 0x14da820: ldur            x0, [fp, #-8]
    // 0x14da824: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14da824: add             x25, x1, x3, lsl #2
    //     0x14da828: add             x25, x25, #0xf
    //     0x14da82c: str             w0, [x25]
    //     0x14da830: tbz             w0, #0, #0x14da84c
    //     0x14da834: ldurb           w16, [x1, #-1]
    //     0x14da838: ldurb           w17, [x0, #-1]
    //     0x14da83c: and             x16, x17, x16, lsr #2
    //     0x14da840: tst             x16, HEAP, lsr #32
    //     0x14da844: b.eq            #0x14da84c
    //     0x14da848: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14da84c: r0 = Instance_SizedBox
    //     0x14da84c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14da850: LeaveFrame
    //     0x14da850: mov             SP, fp
    //     0x14da854: ldp             fp, lr, [SP], #0x10
    // 0x14da858: ret
    //     0x14da858: ret             
    // 0x14da85c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da85c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da860: b               #0x14da72c
    // 0x14da864: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14da864: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x14da868, size: 0x50
    // 0x14da868: EnterFrame
    //     0x14da868: stp             fp, lr, [SP, #-0x10]!
    //     0x14da86c: mov             fp, SP
    // 0x14da870: ldr             x0, [fp, #0x18]
    // 0x14da874: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14da874: ldur            w1, [x0, #0x17]
    // 0x14da878: DecompressPointer r1
    //     0x14da878: add             x1, x1, HEAP, lsl #32
    // 0x14da87c: CheckStackOverflow
    //     0x14da87c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da880: cmp             SP, x16
    //     0x14da884: b.ls            #0x14da8b0
    // 0x14da888: LoadField: r0 = r1->field_f
    //     0x14da888: ldur            w0, [x1, #0xf]
    // 0x14da88c: DecompressPointer r0
    //     0x14da88c: add             x0, x0, HEAP, lsl #32
    // 0x14da890: LoadField: r2 = r1->field_13
    //     0x14da890: ldur            w2, [x1, #0x13]
    // 0x14da894: DecompressPointer r2
    //     0x14da894: add             x2, x2, HEAP, lsl #32
    // 0x14da898: mov             x1, x0
    // 0x14da89c: r0 = _showCustomisedBottomSheet()
    //     0x14da89c: bl              #0x14da8b8  ; [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::_showCustomisedBottomSheet
    // 0x14da8a0: r0 = Null
    //     0x14da8a0: mov             x0, NULL
    // 0x14da8a4: LeaveFrame
    //     0x14da8a4: mov             SP, fp
    //     0x14da8a8: ldp             fp, lr, [SP], #0x10
    // 0x14da8ac: ret
    //     0x14da8ac: ret             
    // 0x14da8b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da8b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da8b4: b               #0x14da888
  }
  _ _showCustomisedBottomSheet(/* No info */) {
    // ** addr: 0x14da8b8, size: 0x88
    // 0x14da8b8: EnterFrame
    //     0x14da8b8: stp             fp, lr, [SP, #-0x10]!
    //     0x14da8bc: mov             fp, SP
    // 0x14da8c0: AllocStack(0x40)
    //     0x14da8c0: sub             SP, SP, #0x40
    // 0x14da8c4: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14da8c4: stur            x1, [fp, #-8]
    //     0x14da8c8: stur            x2, [fp, #-0x10]
    // 0x14da8cc: CheckStackOverflow
    //     0x14da8cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da8d0: cmp             SP, x16
    //     0x14da8d4: b.ls            #0x14da938
    // 0x14da8d8: r1 = 1
    //     0x14da8d8: movz            x1, #0x1
    // 0x14da8dc: r0 = AllocateContext()
    //     0x14da8dc: bl              #0x16f6108  ; AllocateContextStub
    // 0x14da8e0: mov             x1, x0
    // 0x14da8e4: ldur            x0, [fp, #-8]
    // 0x14da8e8: StoreField: r1->field_f = r0
    //     0x14da8e8: stur            w0, [x1, #0xf]
    // 0x14da8ec: mov             x2, x1
    // 0x14da8f0: r1 = Function '<anonymous closure>':.
    //     0x14da8f0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d10] AnonymousClosure: (0x14da940), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::_showCustomisedBottomSheet (0x14da8b8)
    //     0x14da8f4: ldr             x1, [x1, #0xd10]
    // 0x14da8f8: r0 = AllocateClosure()
    //     0x14da8f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14da8fc: stp             x0, NULL, [SP, #0x20]
    // 0x14da900: ldur            x16, [fp, #-0x10]
    // 0x14da904: r30 = Instance_Color
    //     0x14da904: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14da908: stp             lr, x16, [SP, #0x10]
    // 0x14da90c: r16 = Instance_RoundedRectangleBorder
    //     0x14da90c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x14da910: ldr             x16, [x16, #0xc78]
    // 0x14da914: r30 = true
    //     0x14da914: add             lr, NULL, #0x20  ; true
    // 0x14da918: stp             lr, x16, [SP]
    // 0x14da91c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x2, isScrollControlled, 0x4, shape, 0x3, null]
    //     0x14da91c: add             x4, PP, #0x34, lsl #12  ; [pp+0x345f0] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x4, "shape", 0x3, Null]
    //     0x14da920: ldr             x4, [x4, #0x5f0]
    // 0x14da924: r0 = showModalBottomSheet()
    //     0x14da924: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14da928: r0 = Null
    //     0x14da928: mov             x0, NULL
    // 0x14da92c: LeaveFrame
    //     0x14da92c: mov             SP, fp
    //     0x14da930: ldp             fp, lr, [SP], #0x10
    // 0x14da934: ret
    //     0x14da934: ret             
    // 0x14da938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14da938: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14da93c: b               #0x14da8d8
  }
  [closure] SafeArea <anonymous closure>(dynamic, BuildContext?) {
    // ** addr: 0x14da940, size: 0x1c4
    // 0x14da940: EnterFrame
    //     0x14da940: stp             fp, lr, [SP, #-0x10]!
    //     0x14da944: mov             fp, SP
    // 0x14da948: AllocStack(0x40)
    //     0x14da948: sub             SP, SP, #0x40
    // 0x14da94c: SetupParameters()
    //     0x14da94c: ldr             x0, [fp, #0x18]
    //     0x14da950: ldur            w2, [x0, #0x17]
    //     0x14da954: add             x2, x2, HEAP, lsl #32
    //     0x14da958: stur            x2, [fp, #-8]
    // 0x14da95c: CheckStackOverflow
    //     0x14da95c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14da960: cmp             SP, x16
    //     0x14da964: b.ls            #0x14daafc
    // 0x14da968: LoadField: r1 = r2->field_f
    //     0x14da968: ldur            w1, [x2, #0xf]
    // 0x14da96c: DecompressPointer r1
    //     0x14da96c: add             x1, x1, HEAP, lsl #32
    // 0x14da970: r0 = controller()
    //     0x14da970: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da974: LoadField: r1 = r0->field_73
    //     0x14da974: ldur            w1, [x0, #0x73]
    // 0x14da978: DecompressPointer r1
    //     0x14da978: add             x1, x1, HEAP, lsl #32
    // 0x14da97c: r0 = value()
    //     0x14da97c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14da980: ldur            x2, [fp, #-8]
    // 0x14da984: stur            x0, [fp, #-0x10]
    // 0x14da988: LoadField: r1 = r2->field_f
    //     0x14da988: ldur            w1, [x2, #0xf]
    // 0x14da98c: DecompressPointer r1
    //     0x14da98c: add             x1, x1, HEAP, lsl #32
    // 0x14da990: r0 = controller()
    //     0x14da990: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da994: LoadField: r2 = r0->field_c3
    //     0x14da994: ldur            w2, [x0, #0xc3]
    // 0x14da998: DecompressPointer r2
    //     0x14da998: add             x2, x2, HEAP, lsl #32
    // 0x14da99c: ldur            x0, [fp, #-8]
    // 0x14da9a0: stur            x2, [fp, #-0x18]
    // 0x14da9a4: LoadField: r1 = r0->field_f
    //     0x14da9a4: ldur            w1, [x0, #0xf]
    // 0x14da9a8: DecompressPointer r1
    //     0x14da9a8: add             x1, x1, HEAP, lsl #32
    // 0x14da9ac: r0 = controller()
    //     0x14da9ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da9b0: LoadField: r2 = r0->field_c7
    //     0x14da9b0: ldur            w2, [x0, #0xc7]
    // 0x14da9b4: DecompressPointer r2
    //     0x14da9b4: add             x2, x2, HEAP, lsl #32
    // 0x14da9b8: ldur            x0, [fp, #-8]
    // 0x14da9bc: stur            x2, [fp, #-0x20]
    // 0x14da9c0: LoadField: r1 = r0->field_f
    //     0x14da9c0: ldur            w1, [x0, #0xf]
    // 0x14da9c4: DecompressPointer r1
    //     0x14da9c4: add             x1, x1, HEAP, lsl #32
    // 0x14da9c8: r0 = controller()
    //     0x14da9c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da9cc: LoadField: r2 = r0->field_cb
    //     0x14da9cc: ldur            x2, [x0, #0xcb]
    // 0x14da9d0: ldur            x0, [fp, #-8]
    // 0x14da9d4: stur            x2, [fp, #-0x28]
    // 0x14da9d8: LoadField: r1 = r0->field_f
    //     0x14da9d8: ldur            w1, [x0, #0xf]
    // 0x14da9dc: DecompressPointer r1
    //     0x14da9dc: add             x1, x1, HEAP, lsl #32
    // 0x14da9e0: r0 = controller()
    //     0x14da9e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14da9e4: LoadField: r2 = r0->field_d3
    //     0x14da9e4: ldur            w2, [x0, #0xd3]
    // 0x14da9e8: DecompressPointer r2
    //     0x14da9e8: add             x2, x2, HEAP, lsl #32
    // 0x14da9ec: ldur            x0, [fp, #-8]
    // 0x14da9f0: stur            x2, [fp, #-0x30]
    // 0x14da9f4: LoadField: r1 = r0->field_f
    //     0x14da9f4: ldur            w1, [x0, #0xf]
    // 0x14da9f8: DecompressPointer r1
    //     0x14da9f8: add             x1, x1, HEAP, lsl #32
    // 0x14da9fc: r0 = controller()
    //     0x14da9fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14daa00: LoadField: r1 = r0->field_7f
    //     0x14daa00: ldur            w1, [x0, #0x7f]
    // 0x14daa04: DecompressPointer r1
    //     0x14daa04: add             x1, x1, HEAP, lsl #32
    // 0x14daa08: r0 = value()
    //     0x14daa08: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14daa0c: stur            x0, [fp, #-0x38]
    // 0x14daa10: r0 = CustomizedBottomSheet()
    //     0x14daa10: bl              #0x143dd1c  ; AllocateCustomizedBottomSheetStub -> CustomizedBottomSheet (size=0x50)
    // 0x14daa14: mov             x3, x0
    // 0x14daa18: ldur            x0, [fp, #-0x10]
    // 0x14daa1c: stur            x3, [fp, #-0x40]
    // 0x14daa20: StoreField: r3->field_b = r0
    //     0x14daa20: stur            w0, [x3, #0xb]
    // 0x14daa24: ldur            x0, [fp, #-0x18]
    // 0x14daa28: StoreField: r3->field_f = r0
    //     0x14daa28: stur            w0, [x3, #0xf]
    // 0x14daa2c: ldur            x0, [fp, #-0x20]
    // 0x14daa30: StoreField: r3->field_13 = r0
    //     0x14daa30: stur            w0, [x3, #0x13]
    // 0x14daa34: r0 = 1
    //     0x14daa34: movz            x0, #0x1
    // 0x14daa38: ArrayStore: r3[0] = r0  ; List_8
    //     0x14daa38: stur            x0, [x3, #0x17]
    // 0x14daa3c: ldur            x2, [fp, #-8]
    // 0x14daa40: r1 = Function '<anonymous closure>':.
    //     0x14daa40: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d18] AnonymousClosure: (0x13ba980), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showCustomisedBottomSheet (0x13ba7a8)
    //     0x14daa44: ldr             x1, [x1, #0xd18]
    // 0x14daa48: r0 = AllocateClosure()
    //     0x14daa48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14daa4c: mov             x1, x0
    // 0x14daa50: ldur            x0, [fp, #-0x40]
    // 0x14daa54: StoreField: r0->field_1f = r1
    //     0x14daa54: stur            w1, [x0, #0x1f]
    // 0x14daa58: r1 = "home_page"
    //     0x14daa58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x14daa5c: ldr             x1, [x1, #0xe60]
    // 0x14daa60: StoreField: r0->field_23 = r1
    //     0x14daa60: stur            w1, [x0, #0x23]
    // 0x14daa64: ldur            x1, [fp, #-0x28]
    // 0x14daa68: StoreField: r0->field_27 = r1
    //     0x14daa68: stur            x1, [x0, #0x27]
    // 0x14daa6c: ldur            x1, [fp, #-0x30]
    // 0x14daa70: StoreField: r0->field_2f = r1
    //     0x14daa70: stur            w1, [x0, #0x2f]
    // 0x14daa74: ldur            x1, [fp, #-0x38]
    // 0x14daa78: StoreField: r0->field_33 = r1
    //     0x14daa78: stur            w1, [x0, #0x33]
    // 0x14daa7c: r1 = ""
    //     0x14daa7c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14daa80: StoreField: r0->field_43 = r1
    //     0x14daa80: stur            w1, [x0, #0x43]
    // 0x14daa84: r0 = Card()
    //     0x14daa84: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x14daa88: mov             x1, x0
    // 0x14daa8c: r0 = Instance_Color
    //     0x14daa8c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14daa90: stur            x1, [fp, #-8]
    // 0x14daa94: StoreField: r1->field_b = r0
    //     0x14daa94: stur            w0, [x1, #0xb]
    // 0x14daa98: r0 = true
    //     0x14daa98: add             x0, NULL, #0x20  ; true
    // 0x14daa9c: StoreField: r1->field_1f = r0
    //     0x14daa9c: stur            w0, [x1, #0x1f]
    // 0x14daaa0: r2 = Instance_EdgeInsets
    //     0x14daaa0: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14daaa4: StoreField: r1->field_27 = r2
    //     0x14daaa4: stur            w2, [x1, #0x27]
    // 0x14daaa8: ldur            x3, [fp, #-0x40]
    // 0x14daaac: StoreField: r1->field_2f = r3
    //     0x14daaac: stur            w3, [x1, #0x2f]
    // 0x14daab0: StoreField: r1->field_2b = r0
    //     0x14daab0: stur            w0, [x1, #0x2b]
    // 0x14daab4: r3 = Instance__CardVariant
    //     0x14daab4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x14daab8: ldr             x3, [x3, #0xa68]
    // 0x14daabc: StoreField: r1->field_33 = r3
    //     0x14daabc: stur            w3, [x1, #0x33]
    // 0x14daac0: r0 = SafeArea()
    //     0x14daac0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14daac4: r1 = true
    //     0x14daac4: add             x1, NULL, #0x20  ; true
    // 0x14daac8: StoreField: r0->field_b = r1
    //     0x14daac8: stur            w1, [x0, #0xb]
    // 0x14daacc: StoreField: r0->field_f = r1
    //     0x14daacc: stur            w1, [x0, #0xf]
    // 0x14daad0: StoreField: r0->field_13 = r1
    //     0x14daad0: stur            w1, [x0, #0x13]
    // 0x14daad4: ArrayStore: r0[0] = r1  ; List_4
    //     0x14daad4: stur            w1, [x0, #0x17]
    // 0x14daad8: r1 = Instance_EdgeInsets
    //     0x14daad8: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14daadc: StoreField: r0->field_1b = r1
    //     0x14daadc: stur            w1, [x0, #0x1b]
    // 0x14daae0: r1 = false
    //     0x14daae0: add             x1, NULL, #0x30  ; false
    // 0x14daae4: StoreField: r0->field_1f = r1
    //     0x14daae4: stur            w1, [x0, #0x1f]
    // 0x14daae8: ldur            x1, [fp, #-8]
    // 0x14daaec: StoreField: r0->field_23 = r1
    //     0x14daaec: stur            w1, [x0, #0x23]
    // 0x14daaf0: LeaveFrame
    //     0x14daaf0: mov             SP, fp
    //     0x14daaf4: ldp             fp, lr, [SP], #0x10
    // 0x14daaf8: ret
    //     0x14daaf8: ret             
    // 0x14daafc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14daafc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14dab00: b               #0x14da968
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x14dab04, size: 0x164
    // 0x14dab04: EnterFrame
    //     0x14dab04: stp             fp, lr, [SP, #-0x10]!
    //     0x14dab08: mov             fp, SP
    // 0x14dab0c: AllocStack(0x20)
    //     0x14dab0c: sub             SP, SP, #0x20
    // 0x14dab10: SetupParameters()
    //     0x14dab10: ldr             x0, [fp, #0x10]
    //     0x14dab14: ldur            w2, [x0, #0x17]
    //     0x14dab18: add             x2, x2, HEAP, lsl #32
    //     0x14dab1c: stur            x2, [fp, #-8]
    // 0x14dab20: CheckStackOverflow
    //     0x14dab20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14dab24: cmp             SP, x16
    //     0x14dab28: b.ls            #0x14dac5c
    // 0x14dab2c: LoadField: r1 = r2->field_f
    //     0x14dab2c: ldur            w1, [x2, #0xf]
    // 0x14dab30: DecompressPointer r1
    //     0x14dab30: add             x1, x1, HEAP, lsl #32
    // 0x14dab34: r0 = controller()
    //     0x14dab34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dab38: LoadField: r1 = r0->field_9f
    //     0x14dab38: ldur            w1, [x0, #0x9f]
    // 0x14dab3c: DecompressPointer r1
    //     0x14dab3c: add             x1, x1, HEAP, lsl #32
    // 0x14dab40: r0 = value()
    //     0x14dab40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dab44: tbnz            w0, #4, #0x14dac4c
    // 0x14dab48: ldur            x2, [fp, #-8]
    // 0x14dab4c: LoadField: r1 = r2->field_f
    //     0x14dab4c: ldur            w1, [x2, #0xf]
    // 0x14dab50: DecompressPointer r1
    //     0x14dab50: add             x1, x1, HEAP, lsl #32
    // 0x14dab54: r0 = controller()
    //     0x14dab54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dab58: LoadField: r1 = r0->field_9f
    //     0x14dab58: ldur            w1, [x0, #0x9f]
    // 0x14dab5c: DecompressPointer r1
    //     0x14dab5c: add             x1, x1, HEAP, lsl #32
    // 0x14dab60: r2 = false
    //     0x14dab60: add             x2, NULL, #0x30  ; false
    // 0x14dab64: r0 = value=()
    //     0x14dab64: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x14dab68: r0 = LoadStaticField(0x878)
    //     0x14dab68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14dab6c: ldr             x0, [x0, #0x10f0]
    // 0x14dab70: cmp             w0, NULL
    // 0x14dab74: b.eq            #0x14dac64
    // 0x14dab78: LoadField: r3 = r0->field_53
    //     0x14dab78: ldur            w3, [x0, #0x53]
    // 0x14dab7c: DecompressPointer r3
    //     0x14dab7c: add             x3, x3, HEAP, lsl #32
    // 0x14dab80: stur            x3, [fp, #-0x18]
    // 0x14dab84: LoadField: r0 = r3->field_7
    //     0x14dab84: ldur            w0, [x3, #7]
    // 0x14dab88: DecompressPointer r0
    //     0x14dab88: add             x0, x0, HEAP, lsl #32
    // 0x14dab8c: ldur            x2, [fp, #-8]
    // 0x14dab90: stur            x0, [fp, #-0x10]
    // 0x14dab94: r1 = Function '<anonymous closure>':.
    //     0x14dab94: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d20] AnonymousClosure: (0x14dac68), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::body (0x14d81e0)
    //     0x14dab98: ldr             x1, [x1, #0xd20]
    // 0x14dab9c: r0 = AllocateClosure()
    //     0x14dab9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14daba0: ldur            x2, [fp, #-0x10]
    // 0x14daba4: mov             x3, x0
    // 0x14daba8: r1 = Null
    //     0x14daba8: mov             x1, NULL
    // 0x14dabac: stur            x3, [fp, #-8]
    // 0x14dabb0: cmp             w2, NULL
    // 0x14dabb4: b.eq            #0x14dabd4
    // 0x14dabb8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x14dabb8: ldur            w4, [x2, #0x17]
    // 0x14dabbc: DecompressPointer r4
    //     0x14dabbc: add             x4, x4, HEAP, lsl #32
    // 0x14dabc0: r8 = X0
    //     0x14dabc0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x14dabc4: LoadField: r9 = r4->field_7
    //     0x14dabc4: ldur            x9, [x4, #7]
    // 0x14dabc8: r3 = Null
    //     0x14dabc8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d28] Null
    //     0x14dabcc: ldr             x3, [x3, #0xd28]
    // 0x14dabd0: blr             x9
    // 0x14dabd4: ldur            x0, [fp, #-0x18]
    // 0x14dabd8: LoadField: r1 = r0->field_b
    //     0x14dabd8: ldur            w1, [x0, #0xb]
    // 0x14dabdc: LoadField: r2 = r0->field_f
    //     0x14dabdc: ldur            w2, [x0, #0xf]
    // 0x14dabe0: DecompressPointer r2
    //     0x14dabe0: add             x2, x2, HEAP, lsl #32
    // 0x14dabe4: LoadField: r3 = r2->field_b
    //     0x14dabe4: ldur            w3, [x2, #0xb]
    // 0x14dabe8: r2 = LoadInt32Instr(r1)
    //     0x14dabe8: sbfx            x2, x1, #1, #0x1f
    // 0x14dabec: stur            x2, [fp, #-0x20]
    // 0x14dabf0: r1 = LoadInt32Instr(r3)
    //     0x14dabf0: sbfx            x1, x3, #1, #0x1f
    // 0x14dabf4: cmp             x2, x1
    // 0x14dabf8: b.ne            #0x14dac04
    // 0x14dabfc: mov             x1, x0
    // 0x14dac00: r0 = _growToNextCapacity()
    //     0x14dac00: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14dac04: ldur            x2, [fp, #-0x18]
    // 0x14dac08: ldur            x3, [fp, #-0x20]
    // 0x14dac0c: add             x4, x3, #1
    // 0x14dac10: lsl             x5, x4, #1
    // 0x14dac14: StoreField: r2->field_b = r5
    //     0x14dac14: stur            w5, [x2, #0xb]
    // 0x14dac18: LoadField: r1 = r2->field_f
    //     0x14dac18: ldur            w1, [x2, #0xf]
    // 0x14dac1c: DecompressPointer r1
    //     0x14dac1c: add             x1, x1, HEAP, lsl #32
    // 0x14dac20: ldur            x0, [fp, #-8]
    // 0x14dac24: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14dac24: add             x25, x1, x3, lsl #2
    //     0x14dac28: add             x25, x25, #0xf
    //     0x14dac2c: str             w0, [x25]
    //     0x14dac30: tbz             w0, #0, #0x14dac4c
    //     0x14dac34: ldurb           w16, [x1, #-1]
    //     0x14dac38: ldurb           w17, [x0, #-1]
    //     0x14dac3c: and             x16, x17, x16, lsr #2
    //     0x14dac40: tst             x16, HEAP, lsr #32
    //     0x14dac44: b.eq            #0x14dac4c
    //     0x14dac48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14dac4c: r0 = Instance_SizedBox
    //     0x14dac4c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x14dac50: LeaveFrame
    //     0x14dac50: mov             SP, fp
    //     0x14dac54: ldp             fp, lr, [SP], #0x10
    // 0x14dac58: ret
    //     0x14dac58: ret             
    // 0x14dac5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14dac5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14dac60: b               #0x14dab2c
    // 0x14dac64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14dac64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x14dac68, size: 0x48
    // 0x14dac68: EnterFrame
    //     0x14dac68: stp             fp, lr, [SP, #-0x10]!
    //     0x14dac6c: mov             fp, SP
    // 0x14dac70: ldr             x0, [fp, #0x18]
    // 0x14dac74: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14dac74: ldur            w1, [x0, #0x17]
    // 0x14dac78: DecompressPointer r1
    //     0x14dac78: add             x1, x1, HEAP, lsl #32
    // 0x14dac7c: CheckStackOverflow
    //     0x14dac7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14dac80: cmp             SP, x16
    //     0x14dac84: b.ls            #0x14daca8
    // 0x14dac88: LoadField: r0 = r1->field_f
    //     0x14dac88: ldur            w0, [x1, #0xf]
    // 0x14dac8c: DecompressPointer r0
    //     0x14dac8c: add             x0, x0, HEAP, lsl #32
    // 0x14dac90: mov             x1, x0
    // 0x14dac94: r0 = _showBagBottomSheet()
    //     0x14dac94: bl              #0x14dacb0  ; [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::_showBagBottomSheet
    // 0x14dac98: r0 = Null
    //     0x14dac98: mov             x0, NULL
    // 0x14dac9c: LeaveFrame
    //     0x14dac9c: mov             SP, fp
    //     0x14daca0: ldp             fp, lr, [SP], #0x10
    // 0x14daca4: ret
    //     0x14daca4: ret             
    // 0x14daca8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14daca8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14dacac: b               #0x14dac88
  }
  _ _showBagBottomSheet(/* No info */) {
    // ** addr: 0x14dacb0, size: 0xb8
    // 0x14dacb0: EnterFrame
    //     0x14dacb0: stp             fp, lr, [SP, #-0x10]!
    //     0x14dacb4: mov             fp, SP
    // 0x14dacb8: AllocStack(0x40)
    //     0x14dacb8: sub             SP, SP, #0x40
    // 0x14dacbc: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */)
    //     0x14dacbc: stur            x1, [fp, #-8]
    // 0x14dacc0: CheckStackOverflow
    //     0x14dacc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14dacc4: cmp             SP, x16
    //     0x14dacc8: b.ls            #0x14dad5c
    // 0x14daccc: r1 = 1
    //     0x14daccc: movz            x1, #0x1
    // 0x14dacd0: r0 = AllocateContext()
    //     0x14dacd0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14dacd4: mov             x1, x0
    // 0x14dacd8: ldur            x0, [fp, #-8]
    // 0x14dacdc: stur            x1, [fp, #-0x10]
    // 0x14dace0: StoreField: r1->field_f = r0
    //     0x14dace0: stur            w0, [x1, #0xf]
    // 0x14dace4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14dace4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14dace8: ldr             x0, [x0, #0x1c80]
    //     0x14dacec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14dacf0: cmp             w0, w16
    //     0x14dacf4: b.ne            #0x14dad00
    //     0x14dacf8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14dacfc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14dad00: r0 = GetNavigation.context()
    //     0x14dad00: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x14dad04: stur            x0, [fp, #-8]
    // 0x14dad08: cmp             w0, NULL
    // 0x14dad0c: b.eq            #0x14dad64
    // 0x14dad10: ldur            x2, [fp, #-0x10]
    // 0x14dad14: r1 = Function '<anonymous closure>':.
    //     0x14dad14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d38] AnonymousClosure: (0x14dad68), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x14dacb0)
    //     0x14dad18: ldr             x1, [x1, #0xd38]
    // 0x14dad1c: r0 = AllocateClosure()
    //     0x14dad1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dad20: stp             x0, NULL, [SP, #0x20]
    // 0x14dad24: ldur            x16, [fp, #-8]
    // 0x14dad28: r30 = Instance_Color
    //     0x14dad28: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14dad2c: stp             lr, x16, [SP, #0x10]
    // 0x14dad30: r16 = Instance_RoundedRectangleBorder
    //     0x14dad30: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x14dad34: ldr             x16, [x16, #0xc78]
    // 0x14dad38: r30 = true
    //     0x14dad38: add             lr, NULL, #0x20  ; true
    // 0x14dad3c: stp             lr, x16, [SP]
    // 0x14dad40: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x2, isScrollControlled, 0x4, shape, 0x3, null]
    //     0x14dad40: add             x4, PP, #0x34, lsl #12  ; [pp+0x345f0] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x4, "shape", 0x3, Null]
    //     0x14dad44: ldr             x4, [x4, #0x5f0]
    // 0x14dad48: r0 = showModalBottomSheet()
    //     0x14dad48: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x14dad4c: r0 = Null
    //     0x14dad4c: mov             x0, NULL
    // 0x14dad50: LeaveFrame
    //     0x14dad50: mov             SP, fp
    //     0x14dad54: ldp             fp, lr, [SP], #0x10
    // 0x14dad58: ret
    //     0x14dad58: ret             
    // 0x14dad5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14dad5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14dad60: b               #0x14daccc
    // 0x14dad64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x14dad64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SafeArea <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x14dad68, size: 0x1cc
    // 0x14dad68: EnterFrame
    //     0x14dad68: stp             fp, lr, [SP, #-0x10]!
    //     0x14dad6c: mov             fp, SP
    // 0x14dad70: AllocStack(0x50)
    //     0x14dad70: sub             SP, SP, #0x50
    // 0x14dad74: SetupParameters()
    //     0x14dad74: ldr             x0, [fp, #0x18]
    //     0x14dad78: ldur            w2, [x0, #0x17]
    //     0x14dad7c: add             x2, x2, HEAP, lsl #32
    //     0x14dad80: stur            x2, [fp, #-8]
    // 0x14dad84: CheckStackOverflow
    //     0x14dad84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14dad88: cmp             SP, x16
    //     0x14dad8c: b.ls            #0x14daf2c
    // 0x14dad90: LoadField: r1 = r2->field_f
    //     0x14dad90: ldur            w1, [x2, #0xf]
    // 0x14dad94: DecompressPointer r1
    //     0x14dad94: add             x1, x1, HEAP, lsl #32
    // 0x14dad98: r0 = controller()
    //     0x14dad98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dad9c: LoadField: r1 = r0->field_7f
    //     0x14dad9c: ldur            w1, [x0, #0x7f]
    // 0x14dada0: DecompressPointer r1
    //     0x14dada0: add             x1, x1, HEAP, lsl #32
    // 0x14dada4: r0 = value()
    //     0x14dada4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dada8: ldur            x2, [fp, #-8]
    // 0x14dadac: stur            x0, [fp, #-0x10]
    // 0x14dadb0: LoadField: r1 = r2->field_f
    //     0x14dadb0: ldur            w1, [x2, #0xf]
    // 0x14dadb4: DecompressPointer r1
    //     0x14dadb4: add             x1, x1, HEAP, lsl #32
    // 0x14dadb8: r0 = controller()
    //     0x14dadb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dadbc: LoadField: r1 = r0->field_6f
    //     0x14dadbc: ldur            w1, [x0, #0x6f]
    // 0x14dadc0: DecompressPointer r1
    //     0x14dadc0: add             x1, x1, HEAP, lsl #32
    // 0x14dadc4: r0 = value()
    //     0x14dadc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dadc8: ldur            x2, [fp, #-8]
    // 0x14dadcc: stur            x0, [fp, #-0x18]
    // 0x14dadd0: LoadField: r1 = r2->field_f
    //     0x14dadd0: ldur            w1, [x2, #0xf]
    // 0x14dadd4: DecompressPointer r1
    //     0x14dadd4: add             x1, x1, HEAP, lsl #32
    // 0x14dadd8: r0 = controller()
    //     0x14dadd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14daddc: ldur            x2, [fp, #-8]
    // 0x14dade0: stur            x0, [fp, #-0x20]
    // 0x14dade4: LoadField: r1 = r2->field_f
    //     0x14dade4: ldur            w1, [x2, #0xf]
    // 0x14dade8: DecompressPointer r1
    //     0x14dade8: add             x1, x1, HEAP, lsl #32
    // 0x14dadec: r0 = controller()
    //     0x14dadec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dadf0: ldur            x2, [fp, #-8]
    // 0x14dadf4: stur            x0, [fp, #-0x28]
    // 0x14dadf8: LoadField: r1 = r2->field_f
    //     0x14dadf8: ldur            w1, [x2, #0xf]
    // 0x14dadfc: DecompressPointer r1
    //     0x14dadfc: add             x1, x1, HEAP, lsl #32
    // 0x14dae00: r0 = controller()
    //     0x14dae00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14dae04: LoadField: r1 = r0->field_8f
    //     0x14dae04: ldur            w1, [x0, #0x8f]
    // 0x14dae08: DecompressPointer r1
    //     0x14dae08: add             x1, x1, HEAP, lsl #32
    // 0x14dae0c: r0 = value()
    //     0x14dae0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14dae10: LoadField: r1 = r0->field_b
    //     0x14dae10: ldur            w1, [x0, #0xb]
    // 0x14dae14: DecompressPointer r1
    //     0x14dae14: add             x1, x1, HEAP, lsl #32
    // 0x14dae18: cmp             w1, NULL
    // 0x14dae1c: b.ne            #0x14dae28
    // 0x14dae20: r3 = Null
    //     0x14dae20: mov             x3, NULL
    // 0x14dae24: b               #0x14dae34
    // 0x14dae28: LoadField: r0 = r1->field_7
    //     0x14dae28: ldur            w0, [x1, #7]
    // 0x14dae2c: DecompressPointer r0
    //     0x14dae2c: add             x0, x0, HEAP, lsl #32
    // 0x14dae30: mov             x3, x0
    // 0x14dae34: mov             x0, x3
    // 0x14dae38: stur            x3, [fp, #-0x30]
    // 0x14dae3c: r2 = Null
    //     0x14dae3c: mov             x2, NULL
    // 0x14dae40: r1 = Null
    //     0x14dae40: mov             x1, NULL
    // 0x14dae44: r4 = 60
    //     0x14dae44: movz            x4, #0x3c
    // 0x14dae48: branchIfSmi(r0, 0x14dae54)
    //     0x14dae48: tbz             w0, #0, #0x14dae54
    // 0x14dae4c: r4 = LoadClassIdInstr(r0)
    //     0x14dae4c: ldur            x4, [x0, #-1]
    //     0x14dae50: ubfx            x4, x4, #0xc, #0x14
    // 0x14dae54: sub             x4, x4, #0x5e
    // 0x14dae58: cmp             x4, #1
    // 0x14dae5c: b.ls            #0x14dae70
    // 0x14dae60: r8 = String?
    //     0x14dae60: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x14dae64: r3 = Null
    //     0x14dae64: add             x3, PP, #0x40, lsl #12  ; [pp+0x40d40] Null
    //     0x14dae68: ldr             x3, [x3, #0xd40]
    // 0x14dae6c: r0 = String?()
    //     0x14dae6c: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x14dae70: ldur            x2, [fp, #-0x20]
    // 0x14dae74: r1 = Function 'removeItem':.
    //     0x14dae74: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c378] AnonymousClosure: (0x13bb6ac), in [package:customer_app/app/presentation/controllers/collections/collections_controller.dart] CollectionsController::removeItem (0x13bb6e8)
    //     0x14dae78: ldr             x1, [x1, #0x378]
    // 0x14dae7c: r0 = AllocateClosure()
    //     0x14dae7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dae80: ldur            x2, [fp, #-8]
    // 0x14dae84: r1 = Function '<anonymous closure>':.
    //     0x14dae84: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d50] AnonymousClosure: (0x14daf34), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x14dacb0)
    //     0x14dae88: ldr             x1, [x1, #0xd50]
    // 0x14dae8c: stur            x0, [fp, #-0x20]
    // 0x14dae90: r0 = AllocateClosure()
    //     0x14dae90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dae94: ldur            x2, [fp, #-8]
    // 0x14dae98: r1 = Function '<anonymous closure>':.
    //     0x14dae98: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d58] AnonymousClosure: (0x13bae38), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x13bab7c)
    //     0x14dae9c: ldr             x1, [x1, #0xd58]
    // 0x14daea0: stur            x0, [fp, #-8]
    // 0x14daea4: r0 = AllocateClosure()
    //     0x14daea4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14daea8: ldur            x2, [fp, #-0x28]
    // 0x14daeac: r1 = Function 'postEvents':.
    //     0x14daeac: add             x1, PP, #0x32, lsl #12  ; [pp+0x321b0] AnonymousClosure: (0x89c5cc), in [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents (0x8608dc)
    //     0x14daeb0: ldr             x1, [x1, #0x1b0]
    // 0x14daeb4: stur            x0, [fp, #-0x28]
    // 0x14daeb8: r0 = AllocateClosure()
    //     0x14daeb8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14daebc: stur            x0, [fp, #-0x38]
    // 0x14daec0: r0 = BagBottomSheet()
    //     0x14daec0: bl              #0x143e514  ; AllocateBagBottomSheetStub -> BagBottomSheet (size=0x2c)
    // 0x14daec4: stur            x0, [fp, #-0x40]
    // 0x14daec8: ldur            x16, [fp, #-0x20]
    // 0x14daecc: ldur            lr, [fp, #-8]
    // 0x14daed0: stp             lr, x16, [SP]
    // 0x14daed4: mov             x1, x0
    // 0x14daed8: ldur            x2, [fp, #-0x18]
    // 0x14daedc: ldur            x3, [fp, #-0x28]
    // 0x14daee0: ldur            x5, [fp, #-0x10]
    // 0x14daee4: ldur            x6, [fp, #-0x30]
    // 0x14daee8: ldur            x7, [fp, #-0x38]
    // 0x14daeec: r0 = BagBottomSheet()
    //     0x14daeec: bl              #0x143e0f0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::BagBottomSheet
    // 0x14daef0: r0 = SafeArea()
    //     0x14daef0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14daef4: r1 = true
    //     0x14daef4: add             x1, NULL, #0x20  ; true
    // 0x14daef8: StoreField: r0->field_b = r1
    //     0x14daef8: stur            w1, [x0, #0xb]
    // 0x14daefc: StoreField: r0->field_f = r1
    //     0x14daefc: stur            w1, [x0, #0xf]
    // 0x14daf00: StoreField: r0->field_13 = r1
    //     0x14daf00: stur            w1, [x0, #0x13]
    // 0x14daf04: ArrayStore: r0[0] = r1  ; List_4
    //     0x14daf04: stur            w1, [x0, #0x17]
    // 0x14daf08: r1 = Instance_EdgeInsets
    //     0x14daf08: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14daf0c: StoreField: r0->field_1b = r1
    //     0x14daf0c: stur            w1, [x0, #0x1b]
    // 0x14daf10: r1 = false
    //     0x14daf10: add             x1, NULL, #0x30  ; false
    // 0x14daf14: StoreField: r0->field_1f = r1
    //     0x14daf14: stur            w1, [x0, #0x1f]
    // 0x14daf18: ldur            x1, [fp, #-0x40]
    // 0x14daf1c: StoreField: r0->field_23 = r1
    //     0x14daf1c: stur            w1, [x0, #0x23]
    // 0x14daf20: LeaveFrame
    //     0x14daf20: mov             SP, fp
    //     0x14daf24: ldp             fp, lr, [SP], #0x10
    // 0x14daf28: ret
    //     0x14daf28: ret             
    // 0x14daf2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14daf2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14daf30: b               #0x14dad90
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x14daf34, size: 0xe8
    // 0x14daf34: EnterFrame
    //     0x14daf34: stp             fp, lr, [SP, #-0x10]!
    //     0x14daf38: mov             fp, SP
    // 0x14daf3c: AllocStack(0x28)
    //     0x14daf3c: sub             SP, SP, #0x28
    // 0x14daf40: SetupParameters()
    //     0x14daf40: ldr             x0, [fp, #0x10]
    //     0x14daf44: ldur            w2, [x0, #0x17]
    //     0x14daf48: add             x2, x2, HEAP, lsl #32
    //     0x14daf4c: stur            x2, [fp, #-8]
    // 0x14daf50: CheckStackOverflow
    //     0x14daf50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14daf54: cmp             SP, x16
    //     0x14daf58: b.ls            #0x14db014
    // 0x14daf5c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14daf5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14daf60: ldr             x0, [x0, #0x1c80]
    //     0x14daf64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14daf68: cmp             w0, w16
    //     0x14daf6c: b.ne            #0x14daf78
    //     0x14daf70: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14daf74: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14daf78: str             NULL, [SP]
    // 0x14daf7c: r4 = const [0x1, 0, 0, 0, null]
    //     0x14daf7c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x14daf80: r0 = GetNavigation.back()
    //     0x14daf80: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x14daf84: r1 = Null
    //     0x14daf84: mov             x1, NULL
    // 0x14daf88: r2 = 4
    //     0x14daf88: movz            x2, #0x4
    // 0x14daf8c: r0 = AllocateArray()
    //     0x14daf8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14daf90: r16 = "previousScreenSource"
    //     0x14daf90: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x14daf94: ldr             x16, [x16, #0x448]
    // 0x14daf98: StoreField: r0->field_f = r16
    //     0x14daf98: stur            w16, [x0, #0xf]
    // 0x14daf9c: r16 = "home_page"
    //     0x14daf9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x14dafa0: ldr             x16, [x16, #0xe60]
    // 0x14dafa4: StoreField: r0->field_13 = r16
    //     0x14dafa4: stur            w16, [x0, #0x13]
    // 0x14dafa8: r16 = <String, String>
    //     0x14dafa8: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x14dafac: ldr             x16, [x16, #0x788]
    // 0x14dafb0: stp             x0, x16, [SP]
    // 0x14dafb4: r0 = Map._fromLiteral()
    //     0x14dafb4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x14dafb8: r16 = "/bag"
    //     0x14dafb8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x14dafbc: ldr             x16, [x16, #0x468]
    // 0x14dafc0: stp             x16, NULL, [SP, #8]
    // 0x14dafc4: str             x0, [SP]
    // 0x14dafc8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x14dafc8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x14dafcc: ldr             x4, [x4, #0x438]
    // 0x14dafd0: r0 = GetNavigation.toNamed()
    //     0x14dafd0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x14dafd4: stur            x0, [fp, #-0x10]
    // 0x14dafd8: cmp             w0, NULL
    // 0x14dafdc: b.eq            #0x14db004
    // 0x14dafe0: ldur            x2, [fp, #-8]
    // 0x14dafe4: r1 = Function '<anonymous closure>':.
    //     0x14dafe4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d60] AnonymousClosure: (0x13bb65c), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::_showBagBottomSheet (0x13bab7c)
    //     0x14dafe8: ldr             x1, [x1, #0xd60]
    // 0x14dafec: r0 = AllocateClosure()
    //     0x14dafec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14daff0: ldur            x16, [fp, #-0x10]
    // 0x14daff4: stp             x16, NULL, [SP, #8]
    // 0x14daff8: str             x0, [SP]
    // 0x14daffc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x14daffc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x14db000: r0 = then()
    //     0x14db000: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x14db004: r0 = Null
    //     0x14db004: mov             x0, NULL
    // 0x14db008: LeaveFrame
    //     0x14db008: mov             SP, fp
    //     0x14db00c: ldp             fp, lr, [SP], #0x10
    // 0x14db010: ret
    //     0x14db010: ret             
    // 0x14db014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14db014: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14db018: b               #0x14daf5c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e11a4, size: 0x2b4
    // 0x15e11a4: EnterFrame
    //     0x15e11a4: stp             fp, lr, [SP, #-0x10]!
    //     0x15e11a8: mov             fp, SP
    // 0x15e11ac: AllocStack(0x30)
    //     0x15e11ac: sub             SP, SP, #0x30
    // 0x15e11b0: SetupParameters(CollectionPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e11b0: stur            x1, [fp, #-8]
    //     0x15e11b4: stur            x2, [fp, #-0x10]
    // 0x15e11b8: CheckStackOverflow
    //     0x15e11b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e11bc: cmp             SP, x16
    //     0x15e11c0: b.ls            #0x15e1450
    // 0x15e11c4: r1 = 2
    //     0x15e11c4: movz            x1, #0x2
    // 0x15e11c8: r0 = AllocateContext()
    //     0x15e11c8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e11cc: ldur            x1, [fp, #-8]
    // 0x15e11d0: stur            x0, [fp, #-0x18]
    // 0x15e11d4: StoreField: r0->field_f = r1
    //     0x15e11d4: stur            w1, [x0, #0xf]
    // 0x15e11d8: ldur            x2, [fp, #-0x10]
    // 0x15e11dc: StoreField: r0->field_13 = r2
    //     0x15e11dc: stur            w2, [x0, #0x13]
    // 0x15e11e0: r0 = Obx()
    //     0x15e11e0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e11e4: ldur            x2, [fp, #-0x18]
    // 0x15e11e8: r1 = Function '<anonymous closure>':.
    //     0x15e11e8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d68] AnonymousClosure: (0x15ceefc), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e11ec: ldr             x1, [x1, #0xd68]
    // 0x15e11f0: stur            x0, [fp, #-0x10]
    // 0x15e11f4: r0 = AllocateClosure()
    //     0x15e11f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e11f8: mov             x1, x0
    // 0x15e11fc: ldur            x0, [fp, #-0x10]
    // 0x15e1200: StoreField: r0->field_b = r1
    //     0x15e1200: stur            w1, [x0, #0xb]
    // 0x15e1204: ldur            x1, [fp, #-8]
    // 0x15e1208: r0 = controller()
    //     0x15e1208: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e120c: LoadField: r1 = r0->field_d7
    //     0x15e120c: ldur            w1, [x0, #0xd7]
    // 0x15e1210: DecompressPointer r1
    //     0x15e1210: add             x1, x1, HEAP, lsl #32
    // 0x15e1214: r0 = value()
    //     0x15e1214: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e1218: tbnz            w0, #4, #0x15e12b0
    // 0x15e121c: ldur            x2, [fp, #-0x18]
    // 0x15e1220: LoadField: r1 = r2->field_13
    //     0x15e1220: ldur            w1, [x2, #0x13]
    // 0x15e1224: DecompressPointer r1
    //     0x15e1224: add             x1, x1, HEAP, lsl #32
    // 0x15e1228: r0 = of()
    //     0x15e1228: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e122c: LoadField: r1 = r0->field_5b
    //     0x15e122c: ldur            w1, [x0, #0x5b]
    // 0x15e1230: DecompressPointer r1
    //     0x15e1230: add             x1, x1, HEAP, lsl #32
    // 0x15e1234: stur            x1, [fp, #-8]
    // 0x15e1238: r0 = ColorFilter()
    //     0x15e1238: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e123c: mov             x1, x0
    // 0x15e1240: ldur            x0, [fp, #-8]
    // 0x15e1244: stur            x1, [fp, #-0x20]
    // 0x15e1248: StoreField: r1->field_7 = r0
    //     0x15e1248: stur            w0, [x1, #7]
    // 0x15e124c: r0 = Instance_BlendMode
    //     0x15e124c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e1250: ldr             x0, [x0, #0xb30]
    // 0x15e1254: StoreField: r1->field_b = r0
    //     0x15e1254: stur            w0, [x1, #0xb]
    // 0x15e1258: r2 = 1
    //     0x15e1258: movz            x2, #0x1
    // 0x15e125c: StoreField: r1->field_13 = r2
    //     0x15e125c: stur            x2, [x1, #0x13]
    // 0x15e1260: r0 = SvgPicture()
    //     0x15e1260: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1264: stur            x0, [fp, #-8]
    // 0x15e1268: ldur            x16, [fp, #-0x20]
    // 0x15e126c: str             x16, [SP]
    // 0x15e1270: mov             x1, x0
    // 0x15e1274: r2 = "assets/images/search.svg"
    //     0x15e1274: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e1278: ldr             x2, [x2, #0xa30]
    // 0x15e127c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e127c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e1280: ldr             x4, [x4, #0xa38]
    // 0x15e1284: r0 = SvgPicture.asset()
    //     0x15e1284: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e1288: r0 = Align()
    //     0x15e1288: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e128c: r3 = Instance_Alignment
    //     0x15e128c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1290: ldr             x3, [x3, #0xb10]
    // 0x15e1294: StoreField: r0->field_f = r3
    //     0x15e1294: stur            w3, [x0, #0xf]
    // 0x15e1298: r4 = 1.000000
    //     0x15e1298: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e129c: StoreField: r0->field_13 = r4
    //     0x15e129c: stur            w4, [x0, #0x13]
    // 0x15e12a0: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e12a0: stur            w4, [x0, #0x17]
    // 0x15e12a4: ldur            x1, [fp, #-8]
    // 0x15e12a8: StoreField: r0->field_b = r1
    //     0x15e12a8: stur            w1, [x0, #0xb]
    // 0x15e12ac: b               #0x15e1360
    // 0x15e12b0: ldur            x5, [fp, #-0x18]
    // 0x15e12b4: r4 = 1.000000
    //     0x15e12b4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e12b8: r0 = Instance_BlendMode
    //     0x15e12b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e12bc: ldr             x0, [x0, #0xb30]
    // 0x15e12c0: r3 = Instance_Alignment
    //     0x15e12c0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e12c4: ldr             x3, [x3, #0xb10]
    // 0x15e12c8: r2 = 1
    //     0x15e12c8: movz            x2, #0x1
    // 0x15e12cc: LoadField: r1 = r5->field_13
    //     0x15e12cc: ldur            w1, [x5, #0x13]
    // 0x15e12d0: DecompressPointer r1
    //     0x15e12d0: add             x1, x1, HEAP, lsl #32
    // 0x15e12d4: r0 = of()
    //     0x15e12d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e12d8: LoadField: r1 = r0->field_5b
    //     0x15e12d8: ldur            w1, [x0, #0x5b]
    // 0x15e12dc: DecompressPointer r1
    //     0x15e12dc: add             x1, x1, HEAP, lsl #32
    // 0x15e12e0: stur            x1, [fp, #-8]
    // 0x15e12e4: r0 = ColorFilter()
    //     0x15e12e4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e12e8: mov             x1, x0
    // 0x15e12ec: ldur            x0, [fp, #-8]
    // 0x15e12f0: stur            x1, [fp, #-0x20]
    // 0x15e12f4: StoreField: r1->field_7 = r0
    //     0x15e12f4: stur            w0, [x1, #7]
    // 0x15e12f8: r0 = Instance_BlendMode
    //     0x15e12f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e12fc: ldr             x0, [x0, #0xb30]
    // 0x15e1300: StoreField: r1->field_b = r0
    //     0x15e1300: stur            w0, [x1, #0xb]
    // 0x15e1304: r0 = 1
    //     0x15e1304: movz            x0, #0x1
    // 0x15e1308: StoreField: r1->field_13 = r0
    //     0x15e1308: stur            x0, [x1, #0x13]
    // 0x15e130c: r0 = SvgPicture()
    //     0x15e130c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1310: stur            x0, [fp, #-8]
    // 0x15e1314: ldur            x16, [fp, #-0x20]
    // 0x15e1318: str             x16, [SP]
    // 0x15e131c: mov             x1, x0
    // 0x15e1320: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e1320: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e1324: ldr             x2, [x2, #0xa40]
    // 0x15e1328: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e1328: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e132c: ldr             x4, [x4, #0xa38]
    // 0x15e1330: r0 = SvgPicture.asset()
    //     0x15e1330: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e1334: r0 = Align()
    //     0x15e1334: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e1338: mov             x1, x0
    // 0x15e133c: r0 = Instance_Alignment
    //     0x15e133c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e1340: ldr             x0, [x0, #0xb10]
    // 0x15e1344: StoreField: r1->field_f = r0
    //     0x15e1344: stur            w0, [x1, #0xf]
    // 0x15e1348: r0 = 1.000000
    //     0x15e1348: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e134c: StoreField: r1->field_13 = r0
    //     0x15e134c: stur            w0, [x1, #0x13]
    // 0x15e1350: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e1350: stur            w0, [x1, #0x17]
    // 0x15e1354: ldur            x0, [fp, #-8]
    // 0x15e1358: StoreField: r1->field_b = r0
    //     0x15e1358: stur            w0, [x1, #0xb]
    // 0x15e135c: mov             x0, x1
    // 0x15e1360: stur            x0, [fp, #-8]
    // 0x15e1364: r0 = InkWell()
    //     0x15e1364: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e1368: mov             x3, x0
    // 0x15e136c: ldur            x0, [fp, #-8]
    // 0x15e1370: stur            x3, [fp, #-0x20]
    // 0x15e1374: StoreField: r3->field_b = r0
    //     0x15e1374: stur            w0, [x3, #0xb]
    // 0x15e1378: ldur            x2, [fp, #-0x18]
    // 0x15e137c: r1 = Function '<anonymous closure>':.
    //     0x15e137c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d70] AnonymousClosure: (0x15cee30), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e1380: ldr             x1, [x1, #0xd70]
    // 0x15e1384: r0 = AllocateClosure()
    //     0x15e1384: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e1388: ldur            x2, [fp, #-0x20]
    // 0x15e138c: StoreField: r2->field_f = r0
    //     0x15e138c: stur            w0, [x2, #0xf]
    // 0x15e1390: r0 = true
    //     0x15e1390: add             x0, NULL, #0x20  ; true
    // 0x15e1394: StoreField: r2->field_43 = r0
    //     0x15e1394: stur            w0, [x2, #0x43]
    // 0x15e1398: r1 = Instance_BoxShape
    //     0x15e1398: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e139c: ldr             x1, [x1, #0x80]
    // 0x15e13a0: StoreField: r2->field_47 = r1
    //     0x15e13a0: stur            w1, [x2, #0x47]
    // 0x15e13a4: StoreField: r2->field_6f = r0
    //     0x15e13a4: stur            w0, [x2, #0x6f]
    // 0x15e13a8: r1 = false
    //     0x15e13a8: add             x1, NULL, #0x30  ; false
    // 0x15e13ac: StoreField: r2->field_73 = r1
    //     0x15e13ac: stur            w1, [x2, #0x73]
    // 0x15e13b0: StoreField: r2->field_83 = r0
    //     0x15e13b0: stur            w0, [x2, #0x83]
    // 0x15e13b4: StoreField: r2->field_7b = r1
    //     0x15e13b4: stur            w1, [x2, #0x7b]
    // 0x15e13b8: r0 = Obx()
    //     0x15e13b8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e13bc: ldur            x2, [fp, #-0x18]
    // 0x15e13c0: r1 = Function '<anonymous closure>':.
    //     0x15e13c0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d78] AnonymousClosure: (0x15e1458), in [package:customer_app/app/presentation/views/glass/collections/collection_page.dart] CollectionPage::appBar (0x15e11a4)
    //     0x15e13c4: ldr             x1, [x1, #0xd78]
    // 0x15e13c8: stur            x0, [fp, #-8]
    // 0x15e13cc: r0 = AllocateClosure()
    //     0x15e13cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e13d0: mov             x1, x0
    // 0x15e13d4: ldur            x0, [fp, #-8]
    // 0x15e13d8: StoreField: r0->field_b = r1
    //     0x15e13d8: stur            w1, [x0, #0xb]
    // 0x15e13dc: r1 = Null
    //     0x15e13dc: mov             x1, NULL
    // 0x15e13e0: r2 = 2
    //     0x15e13e0: movz            x2, #0x2
    // 0x15e13e4: r0 = AllocateArray()
    //     0x15e13e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e13e8: mov             x2, x0
    // 0x15e13ec: ldur            x0, [fp, #-8]
    // 0x15e13f0: stur            x2, [fp, #-0x18]
    // 0x15e13f4: StoreField: r2->field_f = r0
    //     0x15e13f4: stur            w0, [x2, #0xf]
    // 0x15e13f8: r1 = <Widget>
    //     0x15e13f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e13fc: r0 = AllocateGrowableArray()
    //     0x15e13fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e1400: mov             x1, x0
    // 0x15e1404: ldur            x0, [fp, #-0x18]
    // 0x15e1408: stur            x1, [fp, #-8]
    // 0x15e140c: StoreField: r1->field_f = r0
    //     0x15e140c: stur            w0, [x1, #0xf]
    // 0x15e1410: r0 = 2
    //     0x15e1410: movz            x0, #0x2
    // 0x15e1414: StoreField: r1->field_b = r0
    //     0x15e1414: stur            w0, [x1, #0xb]
    // 0x15e1418: r0 = AppBar()
    //     0x15e1418: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e141c: stur            x0, [fp, #-0x18]
    // 0x15e1420: ldur            x16, [fp, #-0x10]
    // 0x15e1424: ldur            lr, [fp, #-8]
    // 0x15e1428: stp             lr, x16, [SP]
    // 0x15e142c: mov             x1, x0
    // 0x15e1430: ldur            x2, [fp, #-0x20]
    // 0x15e1434: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e1434: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e1438: ldr             x4, [x4, #0xa58]
    // 0x15e143c: r0 = AppBar()
    //     0x15e143c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e1440: ldur            x0, [fp, #-0x18]
    // 0x15e1444: LeaveFrame
    //     0x15e1444: mov             SP, fp
    //     0x15e1448: ldp             fp, lr, [SP], #0x10
    // 0x15e144c: ret
    //     0x15e144c: ret             
    // 0x15e1450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e1450: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e1454: b               #0x15e11c4
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e1458, size: 0x304
    // 0x15e1458: EnterFrame
    //     0x15e1458: stp             fp, lr, [SP, #-0x10]!
    //     0x15e145c: mov             fp, SP
    // 0x15e1460: AllocStack(0x58)
    //     0x15e1460: sub             SP, SP, #0x58
    // 0x15e1464: SetupParameters()
    //     0x15e1464: ldr             x0, [fp, #0x10]
    //     0x15e1468: ldur            w2, [x0, #0x17]
    //     0x15e146c: add             x2, x2, HEAP, lsl #32
    //     0x15e1470: stur            x2, [fp, #-8]
    // 0x15e1474: CheckStackOverflow
    //     0x15e1474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e1478: cmp             SP, x16
    //     0x15e147c: b.ls            #0x15e1754
    // 0x15e1480: LoadField: r1 = r2->field_f
    //     0x15e1480: ldur            w1, [x2, #0xf]
    // 0x15e1484: DecompressPointer r1
    //     0x15e1484: add             x1, x1, HEAP, lsl #32
    // 0x15e1488: r0 = controller()
    //     0x15e1488: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e148c: LoadField: r1 = r0->field_7f
    //     0x15e148c: ldur            w1, [x0, #0x7f]
    // 0x15e1490: DecompressPointer r1
    //     0x15e1490: add             x1, x1, HEAP, lsl #32
    // 0x15e1494: r0 = value()
    //     0x15e1494: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e1498: LoadField: r1 = r0->field_1f
    //     0x15e1498: ldur            w1, [x0, #0x1f]
    // 0x15e149c: DecompressPointer r1
    //     0x15e149c: add             x1, x1, HEAP, lsl #32
    // 0x15e14a0: cmp             w1, NULL
    // 0x15e14a4: b.ne            #0x15e14b0
    // 0x15e14a8: r0 = Null
    //     0x15e14a8: mov             x0, NULL
    // 0x15e14ac: b               #0x15e14b8
    // 0x15e14b0: LoadField: r0 = r1->field_7
    //     0x15e14b0: ldur            w0, [x1, #7]
    // 0x15e14b4: DecompressPointer r0
    //     0x15e14b4: add             x0, x0, HEAP, lsl #32
    // 0x15e14b8: cmp             w0, NULL
    // 0x15e14bc: b.ne            #0x15e14c8
    // 0x15e14c0: r0 = false
    //     0x15e14c0: add             x0, NULL, #0x30  ; false
    // 0x15e14c4: b               #0x15e16bc
    // 0x15e14c8: tbnz            w0, #4, #0x15e16b8
    // 0x15e14cc: ldur            x0, [fp, #-8]
    // 0x15e14d0: LoadField: r1 = r0->field_f
    //     0x15e14d0: ldur            w1, [x0, #0xf]
    // 0x15e14d4: DecompressPointer r1
    //     0x15e14d4: add             x1, x1, HEAP, lsl #32
    // 0x15e14d8: r0 = controller()
    //     0x15e14d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e14dc: LoadField: r1 = r0->field_db
    //     0x15e14dc: ldur            w1, [x0, #0xdb]
    // 0x15e14e0: DecompressPointer r1
    //     0x15e14e0: add             x1, x1, HEAP, lsl #32
    // 0x15e14e4: r0 = value()
    //     0x15e14e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e14e8: mov             x2, x0
    // 0x15e14ec: ldur            x0, [fp, #-8]
    // 0x15e14f0: stur            x2, [fp, #-0x10]
    // 0x15e14f4: LoadField: r1 = r0->field_13
    //     0x15e14f4: ldur            w1, [x0, #0x13]
    // 0x15e14f8: DecompressPointer r1
    //     0x15e14f8: add             x1, x1, HEAP, lsl #32
    // 0x15e14fc: r0 = of()
    //     0x15e14fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e1500: LoadField: r2 = r0->field_5b
    //     0x15e1500: ldur            w2, [x0, #0x5b]
    // 0x15e1504: DecompressPointer r2
    //     0x15e1504: add             x2, x2, HEAP, lsl #32
    // 0x15e1508: ldur            x0, [fp, #-8]
    // 0x15e150c: stur            x2, [fp, #-0x18]
    // 0x15e1510: LoadField: r1 = r0->field_f
    //     0x15e1510: ldur            w1, [x0, #0xf]
    // 0x15e1514: DecompressPointer r1
    //     0x15e1514: add             x1, x1, HEAP, lsl #32
    // 0x15e1518: r0 = controller()
    //     0x15e1518: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e151c: LoadField: r1 = r0->field_df
    //     0x15e151c: ldur            w1, [x0, #0xdf]
    // 0x15e1520: DecompressPointer r1
    //     0x15e1520: add             x1, x1, HEAP, lsl #32
    // 0x15e1524: r0 = value()
    //     0x15e1524: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e1528: cmp             w0, NULL
    // 0x15e152c: r16 = true
    //     0x15e152c: add             x16, NULL, #0x20  ; true
    // 0x15e1530: r17 = false
    //     0x15e1530: add             x17, NULL, #0x30  ; false
    // 0x15e1534: csel            x2, x16, x17, ne
    // 0x15e1538: ldur            x0, [fp, #-8]
    // 0x15e153c: stur            x2, [fp, #-0x20]
    // 0x15e1540: LoadField: r1 = r0->field_f
    //     0x15e1540: ldur            w1, [x0, #0xf]
    // 0x15e1544: DecompressPointer r1
    //     0x15e1544: add             x1, x1, HEAP, lsl #32
    // 0x15e1548: r0 = controller()
    //     0x15e1548: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e154c: LoadField: r1 = r0->field_df
    //     0x15e154c: ldur            w1, [x0, #0xdf]
    // 0x15e1550: DecompressPointer r1
    //     0x15e1550: add             x1, x1, HEAP, lsl #32
    // 0x15e1554: r0 = value()
    //     0x15e1554: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e1558: str             x0, [SP]
    // 0x15e155c: r0 = _interpolateSingle()
    //     0x15e155c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e1560: mov             x2, x0
    // 0x15e1564: ldur            x0, [fp, #-8]
    // 0x15e1568: stur            x2, [fp, #-0x28]
    // 0x15e156c: LoadField: r1 = r0->field_13
    //     0x15e156c: ldur            w1, [x0, #0x13]
    // 0x15e1570: DecompressPointer r1
    //     0x15e1570: add             x1, x1, HEAP, lsl #32
    // 0x15e1574: r0 = of()
    //     0x15e1574: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e1578: LoadField: r1 = r0->field_87
    //     0x15e1578: ldur            w1, [x0, #0x87]
    // 0x15e157c: DecompressPointer r1
    //     0x15e157c: add             x1, x1, HEAP, lsl #32
    // 0x15e1580: LoadField: r0 = r1->field_27
    //     0x15e1580: ldur            w0, [x1, #0x27]
    // 0x15e1584: DecompressPointer r0
    //     0x15e1584: add             x0, x0, HEAP, lsl #32
    // 0x15e1588: r16 = Instance_Color
    //     0x15e1588: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e158c: str             x16, [SP]
    // 0x15e1590: mov             x1, x0
    // 0x15e1594: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e1594: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e1598: ldr             x4, [x4, #0xf40]
    // 0x15e159c: r0 = copyWith()
    //     0x15e159c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e15a0: stur            x0, [fp, #-0x30]
    // 0x15e15a4: r0 = Text()
    //     0x15e15a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e15a8: mov             x2, x0
    // 0x15e15ac: ldur            x0, [fp, #-0x28]
    // 0x15e15b0: stur            x2, [fp, #-0x38]
    // 0x15e15b4: StoreField: r2->field_b = r0
    //     0x15e15b4: stur            w0, [x2, #0xb]
    // 0x15e15b8: ldur            x0, [fp, #-0x30]
    // 0x15e15bc: StoreField: r2->field_13 = r0
    //     0x15e15bc: stur            w0, [x2, #0x13]
    // 0x15e15c0: ldur            x0, [fp, #-8]
    // 0x15e15c4: LoadField: r1 = r0->field_13
    //     0x15e15c4: ldur            w1, [x0, #0x13]
    // 0x15e15c8: DecompressPointer r1
    //     0x15e15c8: add             x1, x1, HEAP, lsl #32
    // 0x15e15cc: r0 = of()
    //     0x15e15cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e15d0: LoadField: r1 = r0->field_5b
    //     0x15e15d0: ldur            w1, [x0, #0x5b]
    // 0x15e15d4: DecompressPointer r1
    //     0x15e15d4: add             x1, x1, HEAP, lsl #32
    // 0x15e15d8: stur            x1, [fp, #-8]
    // 0x15e15dc: r0 = ColorFilter()
    //     0x15e15dc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e15e0: mov             x1, x0
    // 0x15e15e4: ldur            x0, [fp, #-8]
    // 0x15e15e8: stur            x1, [fp, #-0x28]
    // 0x15e15ec: StoreField: r1->field_7 = r0
    //     0x15e15ec: stur            w0, [x1, #7]
    // 0x15e15f0: r0 = Instance_BlendMode
    //     0x15e15f0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e15f4: ldr             x0, [x0, #0xb30]
    // 0x15e15f8: StoreField: r1->field_b = r0
    //     0x15e15f8: stur            w0, [x1, #0xb]
    // 0x15e15fc: r0 = 1
    //     0x15e15fc: movz            x0, #0x1
    // 0x15e1600: StoreField: r1->field_13 = r0
    //     0x15e1600: stur            x0, [x1, #0x13]
    // 0x15e1604: r0 = SvgPicture()
    //     0x15e1604: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e1608: stur            x0, [fp, #-8]
    // 0x15e160c: r16 = Instance_BoxFit
    //     0x15e160c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e1610: ldr             x16, [x16, #0xb18]
    // 0x15e1614: r30 = 24.000000
    //     0x15e1614: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e1618: ldr             lr, [lr, #0xba8]
    // 0x15e161c: stp             lr, x16, [SP, #0x10]
    // 0x15e1620: r16 = 24.000000
    //     0x15e1620: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e1624: ldr             x16, [x16, #0xba8]
    // 0x15e1628: ldur            lr, [fp, #-0x28]
    // 0x15e162c: stp             lr, x16, [SP]
    // 0x15e1630: mov             x1, x0
    // 0x15e1634: r2 = "assets/images/shopping_bag.svg"
    //     0x15e1634: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e1638: ldr             x2, [x2, #0xa60]
    // 0x15e163c: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e163c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e1640: ldr             x4, [x4, #0xa68]
    // 0x15e1644: r0 = SvgPicture.asset()
    //     0x15e1644: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e1648: r0 = Badge()
    //     0x15e1648: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e164c: mov             x1, x0
    // 0x15e1650: ldur            x0, [fp, #-0x18]
    // 0x15e1654: stur            x1, [fp, #-0x28]
    // 0x15e1658: StoreField: r1->field_b = r0
    //     0x15e1658: stur            w0, [x1, #0xb]
    // 0x15e165c: ldur            x0, [fp, #-0x38]
    // 0x15e1660: StoreField: r1->field_27 = r0
    //     0x15e1660: stur            w0, [x1, #0x27]
    // 0x15e1664: ldur            x0, [fp, #-0x20]
    // 0x15e1668: StoreField: r1->field_2b = r0
    //     0x15e1668: stur            w0, [x1, #0x2b]
    // 0x15e166c: ldur            x0, [fp, #-8]
    // 0x15e1670: StoreField: r1->field_2f = r0
    //     0x15e1670: stur            w0, [x1, #0x2f]
    // 0x15e1674: r0 = Visibility()
    //     0x15e1674: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e1678: mov             x1, x0
    // 0x15e167c: ldur            x0, [fp, #-0x28]
    // 0x15e1680: StoreField: r1->field_b = r0
    //     0x15e1680: stur            w0, [x1, #0xb]
    // 0x15e1684: r0 = Instance_SizedBox
    //     0x15e1684: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e1688: StoreField: r1->field_f = r0
    //     0x15e1688: stur            w0, [x1, #0xf]
    // 0x15e168c: ldur            x0, [fp, #-0x10]
    // 0x15e1690: StoreField: r1->field_13 = r0
    //     0x15e1690: stur            w0, [x1, #0x13]
    // 0x15e1694: r0 = false
    //     0x15e1694: add             x0, NULL, #0x30  ; false
    // 0x15e1698: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e1698: stur            w0, [x1, #0x17]
    // 0x15e169c: StoreField: r1->field_1b = r0
    //     0x15e169c: stur            w0, [x1, #0x1b]
    // 0x15e16a0: StoreField: r1->field_1f = r0
    //     0x15e16a0: stur            w0, [x1, #0x1f]
    // 0x15e16a4: StoreField: r1->field_23 = r0
    //     0x15e16a4: stur            w0, [x1, #0x23]
    // 0x15e16a8: StoreField: r1->field_27 = r0
    //     0x15e16a8: stur            w0, [x1, #0x27]
    // 0x15e16ac: StoreField: r1->field_2b = r0
    //     0x15e16ac: stur            w0, [x1, #0x2b]
    // 0x15e16b0: mov             x0, x1
    // 0x15e16b4: b               #0x15e16d4
    // 0x15e16b8: r0 = false
    //     0x15e16b8: add             x0, NULL, #0x30  ; false
    // 0x15e16bc: r0 = Container()
    //     0x15e16bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e16c0: mov             x1, x0
    // 0x15e16c4: stur            x0, [fp, #-8]
    // 0x15e16c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e16c8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e16cc: r0 = Container()
    //     0x15e16cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e16d0: ldur            x0, [fp, #-8]
    // 0x15e16d4: stur            x0, [fp, #-8]
    // 0x15e16d8: r0 = InkWell()
    //     0x15e16d8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e16dc: mov             x3, x0
    // 0x15e16e0: ldur            x0, [fp, #-8]
    // 0x15e16e4: stur            x3, [fp, #-0x10]
    // 0x15e16e8: StoreField: r3->field_b = r0
    //     0x15e16e8: stur            w0, [x3, #0xb]
    // 0x15e16ec: r1 = Function '<anonymous closure>':.
    //     0x15e16ec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40d80] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15e16f0: ldr             x1, [x1, #0xd80]
    // 0x15e16f4: r2 = Null
    //     0x15e16f4: mov             x2, NULL
    // 0x15e16f8: r0 = AllocateClosure()
    //     0x15e16f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e16fc: mov             x1, x0
    // 0x15e1700: ldur            x0, [fp, #-0x10]
    // 0x15e1704: StoreField: r0->field_f = r1
    //     0x15e1704: stur            w1, [x0, #0xf]
    // 0x15e1708: r1 = true
    //     0x15e1708: add             x1, NULL, #0x20  ; true
    // 0x15e170c: StoreField: r0->field_43 = r1
    //     0x15e170c: stur            w1, [x0, #0x43]
    // 0x15e1710: r2 = Instance_BoxShape
    //     0x15e1710: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e1714: ldr             x2, [x2, #0x80]
    // 0x15e1718: StoreField: r0->field_47 = r2
    //     0x15e1718: stur            w2, [x0, #0x47]
    // 0x15e171c: StoreField: r0->field_6f = r1
    //     0x15e171c: stur            w1, [x0, #0x6f]
    // 0x15e1720: r2 = false
    //     0x15e1720: add             x2, NULL, #0x30  ; false
    // 0x15e1724: StoreField: r0->field_73 = r2
    //     0x15e1724: stur            w2, [x0, #0x73]
    // 0x15e1728: StoreField: r0->field_83 = r1
    //     0x15e1728: stur            w1, [x0, #0x83]
    // 0x15e172c: StoreField: r0->field_7b = r2
    //     0x15e172c: stur            w2, [x0, #0x7b]
    // 0x15e1730: r0 = Padding()
    //     0x15e1730: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e1734: r1 = Instance_EdgeInsets
    //     0x15e1734: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15e1738: ldr             x1, [x1, #0xa78]
    // 0x15e173c: StoreField: r0->field_f = r1
    //     0x15e173c: stur            w1, [x0, #0xf]
    // 0x15e1740: ldur            x1, [fp, #-0x10]
    // 0x15e1744: StoreField: r0->field_b = r1
    //     0x15e1744: stur            w1, [x0, #0xb]
    // 0x15e1748: LeaveFrame
    //     0x15e1748: mov             SP, fp
    //     0x15e174c: ldp             fp, lr, [SP], #0x10
    // 0x15e1750: ret
    //     0x15e1750: ret             
    // 0x15e1754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e1754: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e1758: b               #0x15e1480
  }
}
