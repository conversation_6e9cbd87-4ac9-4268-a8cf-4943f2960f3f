// lib: , url: package:customer_app/app/presentation/views/cosmetic/main/main_page.dart

// class id: 1049289, size: 0x8
class :: {
}

// class id: 3417, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __MainPageState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ activate(/* No info */) {
    // ** addr: 0x7f4f7c, size: 0x30
    // 0x7f4f7c: EnterFrame
    //     0x7f4f7c: stp             fp, lr, [SP, #-0x10]!
    //     0x7f4f80: mov             fp, SP
    // 0x7f4f84: CheckStackOverflow
    //     0x7f4f84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f4f88: cmp             SP, x16
    //     0x7f4f8c: b.ls            #0x7f4fa4
    // 0x7f4f90: r0 = _updateTickerModeNotifier()
    //     0x7f4f90: bl              #0x7f4fd0  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] __MainPageState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f4f94: r0 = Null
    //     0x7f4f94: mov             x0, NULL
    // 0x7f4f98: LeaveFrame
    //     0x7f4f98: mov             SP, fp
    //     0x7f4f9c: ldp             fp, lr, [SP], #0x10
    // 0x7f4fa0: ret
    //     0x7f4fa0: ret             
    // 0x7f4fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f4fa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f4fa8: b               #0x7f4f90
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x7f4fd0, size: 0x124
    // 0x7f4fd0: EnterFrame
    //     0x7f4fd0: stp             fp, lr, [SP, #-0x10]!
    //     0x7f4fd4: mov             fp, SP
    // 0x7f4fd8: AllocStack(0x18)
    //     0x7f4fd8: sub             SP, SP, #0x18
    // 0x7f4fdc: SetupParameters(__MainPageState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x7f4fdc: mov             x2, x1
    //     0x7f4fe0: stur            x1, [fp, #-8]
    // 0x7f4fe4: CheckStackOverflow
    //     0x7f4fe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f4fe8: cmp             SP, x16
    //     0x7f4fec: b.ls            #0x7f50e8
    // 0x7f4ff0: LoadField: r1 = r2->field_f
    //     0x7f4ff0: ldur            w1, [x2, #0xf]
    // 0x7f4ff4: DecompressPointer r1
    //     0x7f4ff4: add             x1, x1, HEAP, lsl #32
    // 0x7f4ff8: cmp             w1, NULL
    // 0x7f4ffc: b.eq            #0x7f50f0
    // 0x7f5000: r0 = getNotifier()
    //     0x7f5000: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x7f5004: mov             x3, x0
    // 0x7f5008: ldur            x0, [fp, #-8]
    // 0x7f500c: stur            x3, [fp, #-0x18]
    // 0x7f5010: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x7f5010: ldur            w4, [x0, #0x17]
    // 0x7f5014: DecompressPointer r4
    //     0x7f5014: add             x4, x4, HEAP, lsl #32
    // 0x7f5018: stur            x4, [fp, #-0x10]
    // 0x7f501c: cmp             w3, w4
    // 0x7f5020: b.ne            #0x7f5034
    // 0x7f5024: r0 = Null
    //     0x7f5024: mov             x0, NULL
    // 0x7f5028: LeaveFrame
    //     0x7f5028: mov             SP, fp
    //     0x7f502c: ldp             fp, lr, [SP], #0x10
    // 0x7f5030: ret
    //     0x7f5030: ret             
    // 0x7f5034: cmp             w4, NULL
    // 0x7f5038: b.eq            #0x7f507c
    // 0x7f503c: mov             x2, x0
    // 0x7f5040: r1 = Function '_updateTicker@356311458':.
    //     0x7f5040: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c738] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f5044: ldr             x1, [x1, #0x738]
    // 0x7f5048: r0 = AllocateClosure()
    //     0x7f5048: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f504c: ldur            x1, [fp, #-0x10]
    // 0x7f5050: r2 = LoadClassIdInstr(r1)
    //     0x7f5050: ldur            x2, [x1, #-1]
    //     0x7f5054: ubfx            x2, x2, #0xc, #0x14
    // 0x7f5058: mov             x16, x0
    // 0x7f505c: mov             x0, x2
    // 0x7f5060: mov             x2, x16
    // 0x7f5064: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x7f5064: movz            x17, #0xdc2b
    //     0x7f5068: add             lr, x0, x17
    //     0x7f506c: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5070: blr             lr
    // 0x7f5074: ldur            x0, [fp, #-8]
    // 0x7f5078: ldur            x3, [fp, #-0x18]
    // 0x7f507c: mov             x2, x0
    // 0x7f5080: r1 = Function '_updateTicker@356311458':.
    //     0x7f5080: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c738] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f5084: ldr             x1, [x1, #0x738]
    // 0x7f5088: r0 = AllocateClosure()
    //     0x7f5088: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f508c: ldur            x3, [fp, #-0x18]
    // 0x7f5090: r1 = LoadClassIdInstr(r3)
    //     0x7f5090: ldur            x1, [x3, #-1]
    //     0x7f5094: ubfx            x1, x1, #0xc, #0x14
    // 0x7f5098: mov             x2, x0
    // 0x7f509c: mov             x0, x1
    // 0x7f50a0: mov             x1, x3
    // 0x7f50a4: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x7f50a4: movz            x17, #0xdc71
    //     0x7f50a8: add             lr, x0, x17
    //     0x7f50ac: ldr             lr, [x21, lr, lsl #3]
    //     0x7f50b0: blr             lr
    // 0x7f50b4: ldur            x0, [fp, #-0x18]
    // 0x7f50b8: ldur            x1, [fp, #-8]
    // 0x7f50bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f50bc: stur            w0, [x1, #0x17]
    //     0x7f50c0: ldurb           w16, [x1, #-1]
    //     0x7f50c4: ldurb           w17, [x0, #-1]
    //     0x7f50c8: and             x16, x17, x16, lsr #2
    //     0x7f50cc: tst             x16, HEAP, lsr #32
    //     0x7f50d0: b.eq            #0x7f50d8
    //     0x7f50d4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x7f50d8: r0 = Null
    //     0x7f50d8: mov             x0, NULL
    // 0x7f50dc: LeaveFrame
    //     0x7f50dc: mov             SP, fp
    //     0x7f50e0: ldp             fp, lr, [SP], #0x10
    // 0x7f50e4: ret
    //     0x7f50e4: ret             
    // 0x7f50e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f50e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f50ec: b               #0x7f4ff0
    // 0x7f50f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f50f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc875a4, size: 0x94
    // 0xc875a4: EnterFrame
    //     0xc875a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc875a8: mov             fp, SP
    // 0xc875ac: AllocStack(0x10)
    //     0xc875ac: sub             SP, SP, #0x10
    // 0xc875b0: SetupParameters(__MainPageState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc875b0: mov             x0, x1
    //     0xc875b4: stur            x1, [fp, #-0x10]
    // 0xc875b8: CheckStackOverflow
    //     0xc875b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc875bc: cmp             SP, x16
    //     0xc875c0: b.ls            #0xc87630
    // 0xc875c4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc875c4: ldur            w3, [x0, #0x17]
    // 0xc875c8: DecompressPointer r3
    //     0xc875c8: add             x3, x3, HEAP, lsl #32
    // 0xc875cc: stur            x3, [fp, #-8]
    // 0xc875d0: cmp             w3, NULL
    // 0xc875d4: b.ne            #0xc875e0
    // 0xc875d8: mov             x1, x0
    // 0xc875dc: b               #0xc8761c
    // 0xc875e0: mov             x2, x0
    // 0xc875e4: r1 = Function '_updateTicker@356311458':.
    //     0xc875e4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c738] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc875e8: ldr             x1, [x1, #0x738]
    // 0xc875ec: r0 = AllocateClosure()
    //     0xc875ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc875f0: ldur            x1, [fp, #-8]
    // 0xc875f4: r2 = LoadClassIdInstr(r1)
    //     0xc875f4: ldur            x2, [x1, #-1]
    //     0xc875f8: ubfx            x2, x2, #0xc, #0x14
    // 0xc875fc: mov             x16, x0
    // 0xc87600: mov             x0, x2
    // 0xc87604: mov             x2, x16
    // 0xc87608: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc87608: movz            x17, #0xdc2b
    //     0xc8760c: add             lr, x0, x17
    //     0xc87610: ldr             lr, [x21, lr, lsl #3]
    //     0xc87614: blr             lr
    // 0xc87618: ldur            x1, [fp, #-0x10]
    // 0xc8761c: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc8761c: stur            NULL, [x1, #0x17]
    // 0xc87620: r0 = Null
    //     0xc87620: mov             x0, NULL
    // 0xc87624: LeaveFrame
    //     0xc87624: mov             SP, fp
    //     0xc87628: ldp             fp, lr, [SP], #0x10
    // 0xc8762c: ret
    //     0xc8762c: ret             
    // 0xc87630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87630: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87634: b               #0xc875c4
  }
}

// class id: 3418, size: 0x54, field offset: 0x1c
class _MainPageState extends __MainPageState&State&SingleTickerProviderStateMixin {

  static late final List<Widget> _pages; // offset: 0xe68

  _ initState(/* No info */) {
    // ** addr: 0x93abb4, size: 0x194
    // 0x93abb4: EnterFrame
    //     0x93abb4: stp             fp, lr, [SP, #-0x10]!
    //     0x93abb8: mov             fp, SP
    // 0x93abbc: AllocStack(0x20)
    //     0x93abbc: sub             SP, SP, #0x20
    // 0x93abc0: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0x93abc0: stur            x1, [fp, #-8]
    // 0x93abc4: CheckStackOverflow
    //     0x93abc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93abc8: cmp             SP, x16
    //     0x93abcc: b.ls            #0x93ad40
    // 0x93abd0: r1 = 1
    //     0x93abd0: movz            x1, #0x1
    // 0x93abd4: r0 = AllocateContext()
    //     0x93abd4: bl              #0x16f6108  ; AllocateContextStub
    // 0x93abd8: ldur            x1, [fp, #-8]
    // 0x93abdc: stur            x0, [fp, #-0x10]
    // 0x93abe0: StoreField: r0->field_f = r1
    //     0x93abe0: stur            w1, [x0, #0xf]
    // 0x93abe4: r0 = InitLateStaticField(0xbec) // [package:app_links/src/app_links.dart] AppLinks::_instance
    //     0x93abe4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93abe8: ldr             x0, [x0, #0x17d8]
    //     0x93abec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93abf0: cmp             w0, w16
    //     0x93abf4: b.ne            #0x93ac04
    //     0x93abf8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e8] Field <AppLinks._instance@597120238>: static late final (offset: 0xbec)
    //     0x93abfc: ldr             x2, [x2, #0x7e8]
    //     0x93ac00: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93ac04: mov             x1, x0
    // 0x93ac08: ldur            x2, [fp, #-8]
    // 0x93ac0c: StoreField: r2->field_43 = r0
    //     0x93ac0c: stur            w0, [x2, #0x43]
    //     0x93ac10: ldurb           w16, [x2, #-1]
    //     0x93ac14: ldurb           w17, [x0, #-1]
    //     0x93ac18: and             x16, x17, x16, lsr #2
    //     0x93ac1c: tst             x16, HEAP, lsr #32
    //     0x93ac20: b.eq            #0x93ac28
    //     0x93ac24: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93ac28: r0 = uriLinkStream()
    //     0x93ac28: bl              #0x914518  ; [package:app_links/src/app_links.dart] AppLinks::uriLinkStream
    // 0x93ac2c: ldur            x2, [fp, #-0x10]
    // 0x93ac30: r1 = Function '<anonymous closure>':.
    //     0x93ac30: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2ddd0] AnonymousClosure: (0x93b6a0), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::initState (0x93abb4)
    //     0x93ac34: ldr             x1, [x1, #0xdd0]
    // 0x93ac38: stur            x0, [fp, #-0x10]
    // 0x93ac3c: r0 = AllocateClosure()
    //     0x93ac3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ac40: ldur            x1, [fp, #-0x10]
    // 0x93ac44: mov             x2, x0
    // 0x93ac48: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93ac48: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93ac4c: r0 = listen()
    //     0x93ac4c: bl              #0x163918c  ; [dart:async] _StreamImpl::listen
    // 0x93ac50: ldur            x1, [fp, #-8]
    // 0x93ac54: StoreField: r1->field_47 = r0
    //     0x93ac54: stur            w0, [x1, #0x47]
    //     0x93ac58: ldurb           w16, [x1, #-1]
    //     0x93ac5c: ldurb           w17, [x0, #-1]
    //     0x93ac60: and             x16, x17, x16, lsr #2
    //     0x93ac64: tst             x16, HEAP, lsr #32
    //     0x93ac68: b.eq            #0x93ac70
    //     0x93ac6c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93ac70: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x93ac70: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93ac74: ldr             x0, [x0, #0x1c80]
    //     0x93ac78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93ac7c: cmp             w0, w16
    //     0x93ac80: b.ne            #0x93ac8c
    //     0x93ac84: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x93ac88: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93ac8c: r0 = GetNavigation.arguments()
    //     0x93ac8c: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x93ac90: cmp             w0, NULL
    // 0x93ac94: b.eq            #0x93ad14
    // 0x93ac98: ldur            x1, [fp, #-8]
    // 0x93ac9c: r16 = "selected_bottom_index"
    //     0x93ac9c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb478] "selected_bottom_index"
    //     0x93aca0: ldr             x16, [x16, #0x478]
    // 0x93aca4: stp             x16, x0, [SP]
    // 0x93aca8: r4 = 0
    //     0x93aca8: movz            x4, #0
    // 0x93acac: ldr             x0, [SP, #8]
    // 0x93acb0: r16 = UnlinkedCall_0x613b5c
    //     0x93acb0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2ddd8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93acb4: add             x16, x16, #0xdd8
    // 0x93acb8: ldp             x5, lr, [x16]
    // 0x93acbc: blr             lr
    // 0x93acc0: mov             x3, x0
    // 0x93acc4: r2 = Null
    //     0x93acc4: mov             x2, NULL
    // 0x93acc8: r1 = Null
    //     0x93acc8: mov             x1, NULL
    // 0x93accc: stur            x3, [fp, #-0x10]
    // 0x93acd0: branchIfSmi(r0, 0x93acf8)
    //     0x93acd0: tbz             w0, #0, #0x93acf8
    // 0x93acd4: r4 = LoadClassIdInstr(r0)
    //     0x93acd4: ldur            x4, [x0, #-1]
    //     0x93acd8: ubfx            x4, x4, #0xc, #0x14
    // 0x93acdc: sub             x4, x4, #0x3c
    // 0x93ace0: cmp             x4, #1
    // 0x93ace4: b.ls            #0x93acf8
    // 0x93ace8: r8 = int
    //     0x93ace8: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x93acec: r3 = Null
    //     0x93acec: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dde8] Null
    //     0x93acf0: ldr             x3, [x3, #0xde8]
    // 0x93acf4: r0 = int()
    //     0x93acf4: bl              #0x16fc548  ; IsType_int_Stub
    // 0x93acf8: ldur            x0, [fp, #-0x10]
    // 0x93acfc: r1 = LoadInt32Instr(r0)
    //     0x93acfc: sbfx            x1, x0, #1, #0x1f
    //     0x93ad00: tbz             w0, #0, #0x93ad08
    //     0x93ad04: ldur            x1, [x0, #7]
    // 0x93ad08: ldur            x0, [fp, #-8]
    // 0x93ad0c: StoreField: r0->field_1b = r1
    //     0x93ad0c: stur            x1, [x0, #0x1b]
    // 0x93ad10: b               #0x93ad18
    // 0x93ad14: ldur            x0, [fp, #-8]
    // 0x93ad18: LoadField: r1 = r0->field_1b
    //     0x93ad18: ldur            x1, [x0, #0x1b]
    // 0x93ad1c: cmp             x1, #1
    // 0x93ad20: b.ne            #0x93ad30
    // 0x93ad24: mov             x1, x0
    // 0x93ad28: r2 = 2
    //     0x93ad28: movz            x2, #0x2
    // 0x93ad2c: r0 = _onItemTapped()
    //     0x93ad2c: bl              #0x93ad48  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x93ad30: r0 = Null
    //     0x93ad30: mov             x0, NULL
    // 0x93ad34: LeaveFrame
    //     0x93ad34: mov             SP, fp
    //     0x93ad38: ldp             fp, lr, [SP], #0x10
    // 0x93ad3c: ret
    //     0x93ad3c: ret             
    // 0x93ad40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93ad40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93ad44: b               #0x93abd0
  }
  _ _onItemTapped(/* No info */) {
    // ** addr: 0x93ad48, size: 0x70
    // 0x93ad48: EnterFrame
    //     0x93ad48: stp             fp, lr, [SP, #-0x10]!
    //     0x93ad4c: mov             fp, SP
    // 0x93ad50: AllocStack(0x10)
    //     0x93ad50: sub             SP, SP, #0x10
    // 0x93ad54: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x93ad54: stur            x1, [fp, #-8]
    //     0x93ad58: stur            x2, [fp, #-0x10]
    // 0x93ad5c: CheckStackOverflow
    //     0x93ad5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93ad60: cmp             SP, x16
    //     0x93ad64: b.ls            #0x93adb0
    // 0x93ad68: r1 = 2
    //     0x93ad68: movz            x1, #0x2
    // 0x93ad6c: r0 = AllocateContext()
    //     0x93ad6c: bl              #0x16f6108  ; AllocateContextStub
    // 0x93ad70: mov             x1, x0
    // 0x93ad74: ldur            x0, [fp, #-8]
    // 0x93ad78: StoreField: r1->field_f = r0
    //     0x93ad78: stur            w0, [x1, #0xf]
    // 0x93ad7c: ldur            x2, [fp, #-0x10]
    // 0x93ad80: StoreField: r1->field_13 = r2
    //     0x93ad80: stur            w2, [x1, #0x13]
    // 0x93ad84: mov             x2, x1
    // 0x93ad88: r1 = Function '<anonymous closure>':.
    //     0x93ad88: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd00] AnonymousClosure: (0x93adf4), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped (0x93ad48)
    //     0x93ad8c: ldr             x1, [x1, #0xd00]
    // 0x93ad90: r0 = AllocateClosure()
    //     0x93ad90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93ad94: ldur            x1, [fp, #-8]
    // 0x93ad98: mov             x2, x0
    // 0x93ad9c: r0 = setState()
    //     0x93ad9c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93ada0: r0 = Null
    //     0x93ada0: mov             x0, NULL
    // 0x93ada4: LeaveFrame
    //     0x93ada4: mov             SP, fp
    //     0x93ada8: ldp             fp, lr, [SP], #0x10
    // 0x93adac: ret
    //     0x93adac: ret             
    // 0x93adb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93adb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93adb4: b               #0x93ad68
  }
  [closure] void _onItemTapped(dynamic, int) {
    // ** addr: 0x93adb8, size: 0x3c
    // 0x93adb8: EnterFrame
    //     0x93adb8: stp             fp, lr, [SP, #-0x10]!
    //     0x93adbc: mov             fp, SP
    // 0x93adc0: ldr             x0, [fp, #0x18]
    // 0x93adc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x93adc4: ldur            w1, [x0, #0x17]
    // 0x93adc8: DecompressPointer r1
    //     0x93adc8: add             x1, x1, HEAP, lsl #32
    // 0x93adcc: CheckStackOverflow
    //     0x93adcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93add0: cmp             SP, x16
    //     0x93add4: b.ls            #0x93adec
    // 0x93add8: ldr             x2, [fp, #0x10]
    // 0x93addc: r0 = _onItemTapped()
    //     0x93addc: bl              #0x93ad48  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x93ade0: LeaveFrame
    //     0x93ade0: mov             SP, fp
    //     0x93ade4: ldp             fp, lr, [SP], #0x10
    // 0x93ade8: ret
    //     0x93ade8: ret             
    // 0x93adec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93adec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93adf0: b               #0x93add8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x93adf4, size: 0x2ac
    // 0x93adf4: EnterFrame
    //     0x93adf4: stp             fp, lr, [SP, #-0x10]!
    //     0x93adf8: mov             fp, SP
    // 0x93adfc: AllocStack(0x38)
    //     0x93adfc: sub             SP, SP, #0x38
    // 0x93ae00: SetupParameters()
    //     0x93ae00: ldr             x0, [fp, #0x10]
    //     0x93ae04: ldur            w2, [x0, #0x17]
    //     0x93ae08: add             x2, x2, HEAP, lsl #32
    //     0x93ae0c: stur            x2, [fp, #-8]
    // 0x93ae10: CheckStackOverflow
    //     0x93ae10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93ae14: cmp             SP, x16
    //     0x93ae18: b.ls            #0x93b090
    // 0x93ae1c: LoadField: r0 = r2->field_f
    //     0x93ae1c: ldur            w0, [x2, #0xf]
    // 0x93ae20: DecompressPointer r0
    //     0x93ae20: add             x0, x0, HEAP, lsl #32
    // 0x93ae24: LoadField: r1 = r2->field_13
    //     0x93ae24: ldur            w1, [x2, #0x13]
    // 0x93ae28: DecompressPointer r1
    //     0x93ae28: add             x1, x1, HEAP, lsl #32
    // 0x93ae2c: r3 = LoadInt32Instr(r1)
    //     0x93ae2c: sbfx            x3, x1, #1, #0x1f
    //     0x93ae30: tbz             w1, #0, #0x93ae38
    //     0x93ae34: ldur            x3, [x1, #7]
    // 0x93ae38: StoreField: r0->field_1b = r3
    //     0x93ae38: stur            x3, [x0, #0x1b]
    // 0x93ae3c: cbnz            x3, #0x93aedc
    // 0x93ae40: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0x93ae40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93ae44: ldr             x0, [x0, #0x1cd0]
    //     0x93ae48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93ae4c: cmp             w0, w16
    //     0x93ae50: b.ne            #0x93ae60
    //     0x93ae54: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0x93ae58: ldr             x2, [x2, #0xca8]
    //     0x93ae5c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93ae60: mov             x2, x0
    // 0x93ae64: LoadField: r0 = r2->field_b
    //     0x93ae64: ldur            w0, [x2, #0xb]
    // 0x93ae68: r1 = LoadInt32Instr(r0)
    //     0x93ae68: sbfx            x1, x0, #1, #0x1f
    // 0x93ae6c: mov             x0, x1
    // 0x93ae70: r1 = 0
    //     0x93ae70: movz            x1, #0
    // 0x93ae74: cmp             x1, x0
    // 0x93ae78: b.hs            #0x93b098
    // 0x93ae7c: LoadField: r0 = r2->field_f
    //     0x93ae7c: ldur            w0, [x2, #0xf]
    // 0x93ae80: DecompressPointer r0
    //     0x93ae80: add             x0, x0, HEAP, lsl #32
    // 0x93ae84: LoadField: r3 = r0->field_f
    //     0x93ae84: ldur            w3, [x0, #0xf]
    // 0x93ae88: DecompressPointer r3
    //     0x93ae88: add             x3, x3, HEAP, lsl #32
    // 0x93ae8c: mov             x0, x3
    // 0x93ae90: stur            x3, [fp, #-0x10]
    // 0x93ae94: r2 = Null
    //     0x93ae94: mov             x2, NULL
    // 0x93ae98: r1 = Null
    //     0x93ae98: mov             x1, NULL
    // 0x93ae9c: r4 = 60
    //     0x93ae9c: movz            x4, #0x3c
    // 0x93aea0: branchIfSmi(r0, 0x93aeac)
    //     0x93aea0: tbz             w0, #0, #0x93aeac
    // 0x93aea4: r4 = LoadClassIdInstr(r0)
    //     0x93aea4: ldur            x4, [x0, #-1]
    //     0x93aea8: ubfx            x4, x4, #0xc, #0x14
    // 0x93aeac: r17 = 4599
    //     0x93aeac: movz            x17, #0x11f7
    // 0x93aeb0: cmp             x4, x17
    // 0x93aeb4: b.eq            #0x93aecc
    // 0x93aeb8: r8 = HomePage
    //     0x93aeb8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dd08] Type: HomePage
    //     0x93aebc: ldr             x8, [x8, #0xd08]
    // 0x93aec0: r3 = Null
    //     0x93aec0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd10] Null
    //     0x93aec4: ldr             x3, [x3, #0xd10]
    // 0x93aec8: r0 = DefaultTypeTest()
    //     0x93aec8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93aecc: ldur            x1, [fp, #-0x10]
    // 0x93aed0: r0 = controller()
    //     0x93aed0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93aed4: mov             x1, x0
    // 0x93aed8: r0 = onRefreshPage()
    //     0x93aed8: bl              #0x908a78  ; [package:customer_app/app/presentation/controllers/home/<USER>
    // 0x93aedc: ldur            x2, [fp, #-8]
    // 0x93aee0: LoadField: r0 = r2->field_13
    //     0x93aee0: ldur            w0, [x2, #0x13]
    // 0x93aee4: DecompressPointer r0
    //     0x93aee4: add             x0, x0, HEAP, lsl #32
    // 0x93aee8: r1 = LoadInt32Instr(r0)
    //     0x93aee8: sbfx            x1, x0, #1, #0x1f
    //     0x93aeec: tbz             w0, #0, #0x93aef4
    //     0x93aef0: ldur            x1, [x0, #7]
    // 0x93aef4: cmp             x1, #1
    // 0x93aef8: b.ne            #0x93b034
    // 0x93aefc: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0x93aefc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93af00: ldr             x0, [x0, #0x1cd0]
    //     0x93af04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93af08: cmp             w0, w16
    //     0x93af0c: b.ne            #0x93af1c
    //     0x93af10: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0x93af14: ldr             x2, [x2, #0xca8]
    //     0x93af18: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93af1c: mov             x2, x0
    // 0x93af20: LoadField: r0 = r2->field_b
    //     0x93af20: ldur            w0, [x2, #0xb]
    // 0x93af24: r1 = LoadInt32Instr(r0)
    //     0x93af24: sbfx            x1, x0, #1, #0x1f
    // 0x93af28: mov             x0, x1
    // 0x93af2c: r1 = 1
    //     0x93af2c: movz            x1, #0x1
    // 0x93af30: cmp             x1, x0
    // 0x93af34: b.hs            #0x93b09c
    // 0x93af38: LoadField: r0 = r2->field_f
    //     0x93af38: ldur            w0, [x2, #0xf]
    // 0x93af3c: DecompressPointer r0
    //     0x93af3c: add             x0, x0, HEAP, lsl #32
    // 0x93af40: LoadField: r3 = r0->field_13
    //     0x93af40: ldur            w3, [x0, #0x13]
    // 0x93af44: DecompressPointer r3
    //     0x93af44: add             x3, x3, HEAP, lsl #32
    // 0x93af48: mov             x0, x3
    // 0x93af4c: stur            x3, [fp, #-0x10]
    // 0x93af50: r2 = Null
    //     0x93af50: mov             x2, NULL
    // 0x93af54: r1 = Null
    //     0x93af54: mov             x1, NULL
    // 0x93af58: r4 = 60
    //     0x93af58: movz            x4, #0x3c
    // 0x93af5c: branchIfSmi(r0, 0x93af68)
    //     0x93af5c: tbz             w0, #0, #0x93af68
    // 0x93af60: r4 = LoadClassIdInstr(r0)
    //     0x93af60: ldur            x4, [x0, #-1]
    //     0x93af64: ubfx            x4, x4, #0xc, #0x14
    // 0x93af68: r17 = 4596
    //     0x93af68: movz            x17, #0x11f4
    // 0x93af6c: cmp             x4, x17
    // 0x93af70: b.eq            #0x93af88
    // 0x93af74: r8 = OrdersView
    //     0x93af74: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0x93af78: ldr             x8, [x8, #0xcb0]
    // 0x93af7c: r3 = Null
    //     0x93af7c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd20] Null
    //     0x93af80: ldr             x3, [x3, #0xd20]
    // 0x93af84: r0 = DefaultTypeTest()
    //     0x93af84: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93af88: ldur            x1, [fp, #-0x10]
    // 0x93af8c: r0 = controller()
    //     0x93af8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93af90: mov             x1, x0
    // 0x93af94: r0 = getBagCount()
    //     0x93af94: bl              #0x8a92ac  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getBagCount
    // 0x93af98: ldur            x0, [fp, #-8]
    // 0x93af9c: LoadField: r1 = r0->field_f
    //     0x93af9c: ldur            w1, [x0, #0xf]
    // 0x93afa0: DecompressPointer r1
    //     0x93afa0: add             x1, x1, HEAP, lsl #32
    // 0x93afa4: LoadField: r2 = r1->field_3f
    //     0x93afa4: ldur            w2, [x1, #0x3f]
    // 0x93afa8: DecompressPointer r2
    //     0x93afa8: add             x2, x2, HEAP, lsl #32
    // 0x93afac: LoadField: r1 = r2->field_4b
    //     0x93afac: ldur            w1, [x2, #0x4b]
    // 0x93afb0: DecompressPointer r1
    //     0x93afb0: add             x1, x1, HEAP, lsl #32
    // 0x93afb4: r16 = ""
    //     0x93afb4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93afb8: str             x16, [SP]
    // 0x93afbc: r2 = "token"
    //     0x93afbc: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x93afc0: ldr             x2, [x2, #0x958]
    // 0x93afc4: r4 = const [0, 0x3, 0x1, 0x2, defaultValue, 0x2, null]
    //     0x93afc4: add             x4, PP, #0x11, lsl #12  ; [pp+0x11f48] List(7) [0, 0x3, 0x1, 0x2, "defaultValue", 0x2, Null]
    //     0x93afc8: ldr             x4, [x4, #0xf48]
    // 0x93afcc: r0 = getString()
    //     0x93afcc: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x93afd0: stur            x0, [fp, #-0x18]
    // 0x93afd4: LoadField: r3 = r0->field_7
    //     0x93afd4: ldur            w3, [x0, #7]
    // 0x93afd8: DecompressPointer r3
    //     0x93afd8: add             x3, x3, HEAP, lsl #32
    // 0x93afdc: ldur            x2, [fp, #-8]
    // 0x93afe0: stur            x3, [fp, #-0x10]
    // 0x93afe4: r1 = Function '<anonymous closure>':.
    //     0x93afe4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd30] AnonymousClosure: (0x93b214), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped (0x93ad48)
    //     0x93afe8: ldr             x1, [x1, #0xd30]
    // 0x93afec: r0 = AllocateClosure()
    //     0x93afec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93aff0: ldur            x2, [fp, #-0x10]
    // 0x93aff4: mov             x3, x0
    // 0x93aff8: r1 = Null
    //     0x93aff8: mov             x1, NULL
    // 0x93affc: stur            x3, [fp, #-0x10]
    // 0x93b000: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0x93b000: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0x93b004: ldr             x8, [x8, #0xce8]
    // 0x93b008: LoadField: r9 = r8->field_7
    //     0x93b008: ldur            x9, [x8, #7]
    // 0x93b00c: r3 = Null
    //     0x93b00c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd38] Null
    //     0x93b010: ldr             x3, [x3, #0xd38]
    // 0x93b014: blr             x9
    // 0x93b018: ldur            x16, [fp, #-0x18]
    // 0x93b01c: stp             x16, NULL, [SP, #0x10]
    // 0x93b020: ldur            x16, [fp, #-0x10]
    // 0x93b024: stp             NULL, x16, [SP]
    // 0x93b028: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x93b028: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x93b02c: r0 = then()
    //     0x93b02c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x93b030: b               #0x93b080
    // 0x93b034: cmp             x1, #2
    // 0x93b038: b.ne            #0x93b080
    // 0x93b03c: ldur            x2, [fp, #-8]
    // 0x93b040: LoadField: r0 = r2->field_f
    //     0x93b040: ldur            w0, [x2, #0xf]
    // 0x93b044: DecompressPointer r0
    //     0x93b044: add             x0, x0, HEAP, lsl #32
    // 0x93b048: LoadField: r1 = r0->field_4b
    //     0x93b048: ldur            w1, [x0, #0x4b]
    // 0x93b04c: DecompressPointer r1
    //     0x93b04c: add             x1, x1, HEAP, lsl #32
    // 0x93b050: r0 = getConnectivityType()
    //     0x93b050: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x93b054: ldur            x2, [fp, #-8]
    // 0x93b058: r1 = Function '<anonymous closure>':.
    //     0x93b058: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd48] AnonymousClosure: (0x93b0a0), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped (0x93ad48)
    //     0x93b05c: ldr             x1, [x1, #0xd48]
    // 0x93b060: stur            x0, [fp, #-8]
    // 0x93b064: r0 = AllocateClosure()
    //     0x93b064: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93b068: r16 = <Null?>
    //     0x93b068: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x93b06c: ldur            lr, [fp, #-8]
    // 0x93b070: stp             lr, x16, [SP, #8]
    // 0x93b074: str             x0, [SP]
    // 0x93b078: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x93b078: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x93b07c: r0 = then()
    //     0x93b07c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x93b080: r0 = Null
    //     0x93b080: mov             x0, NULL
    // 0x93b084: LeaveFrame
    //     0x93b084: mov             SP, fp
    //     0x93b088: ldp             fp, lr, [SP], #0x10
    // 0x93b08c: ret
    //     0x93b08c: ret             
    // 0x93b090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b090: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b094: b               #0x93ae1c
    // 0x93b098: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b098: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93b09c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b09c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x93b0a0, size: 0x174
    // 0x93b0a0: EnterFrame
    //     0x93b0a0: stp             fp, lr, [SP, #-0x10]!
    //     0x93b0a4: mov             fp, SP
    // 0x93b0a8: AllocStack(0x10)
    //     0x93b0a8: sub             SP, SP, #0x10
    // 0x93b0ac: SetupParameters()
    //     0x93b0ac: ldr             x0, [fp, #0x18]
    //     0x93b0b0: ldur            w1, [x0, #0x17]
    //     0x93b0b4: add             x1, x1, HEAP, lsl #32
    // 0x93b0b8: CheckStackOverflow
    //     0x93b0b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b0bc: cmp             SP, x16
    //     0x93b0c0: b.ls            #0x93b204
    // 0x93b0c4: LoadField: r0 = r1->field_f
    //     0x93b0c4: ldur            w0, [x1, #0xf]
    // 0x93b0c8: DecompressPointer r0
    //     0x93b0c8: add             x0, x0, HEAP, lsl #32
    // 0x93b0cc: ldr             x1, [fp, #0x10]
    // 0x93b0d0: StoreField: r0->field_4f = r1
    //     0x93b0d0: stur            w1, [x0, #0x4f]
    // 0x93b0d4: tbnz            w1, #4, #0x93b1f4
    // 0x93b0d8: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0x93b0d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93b0dc: ldr             x0, [x0, #0x1cd0]
    //     0x93b0e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93b0e4: cmp             w0, w16
    //     0x93b0e8: b.ne            #0x93b0f8
    //     0x93b0ec: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0x93b0f0: ldr             x2, [x2, #0xca8]
    //     0x93b0f4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93b0f8: mov             x3, x0
    // 0x93b0fc: stur            x3, [fp, #-0x10]
    // 0x93b100: LoadField: r0 = r3->field_b
    //     0x93b100: ldur            w0, [x3, #0xb]
    // 0x93b104: r1 = LoadInt32Instr(r0)
    //     0x93b104: sbfx            x1, x0, #1, #0x1f
    // 0x93b108: mov             x0, x1
    // 0x93b10c: r1 = 2
    //     0x93b10c: movz            x1, #0x2
    // 0x93b110: cmp             x1, x0
    // 0x93b114: b.hs            #0x93b20c
    // 0x93b118: LoadField: r0 = r3->field_f
    //     0x93b118: ldur            w0, [x3, #0xf]
    // 0x93b11c: DecompressPointer r0
    //     0x93b11c: add             x0, x0, HEAP, lsl #32
    // 0x93b120: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x93b120: ldur            w4, [x0, #0x17]
    // 0x93b124: DecompressPointer r4
    //     0x93b124: add             x4, x4, HEAP, lsl #32
    // 0x93b128: mov             x0, x4
    // 0x93b12c: stur            x4, [fp, #-8]
    // 0x93b130: r2 = Null
    //     0x93b130: mov             x2, NULL
    // 0x93b134: r1 = Null
    //     0x93b134: mov             x1, NULL
    // 0x93b138: r4 = 60
    //     0x93b138: movz            x4, #0x3c
    // 0x93b13c: branchIfSmi(r0, 0x93b148)
    //     0x93b13c: tbz             w0, #0, #0x93b148
    // 0x93b140: r4 = LoadClassIdInstr(r0)
    //     0x93b140: ldur            x4, [x0, #-1]
    //     0x93b144: ubfx            x4, x4, #0xc, #0x14
    // 0x93b148: r17 = 4586
    //     0x93b148: movz            x17, #0x11ea
    // 0x93b14c: cmp             x4, x17
    // 0x93b150: b.eq            #0x93b168
    // 0x93b154: r8 = ProfileView
    //     0x93b154: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dd50] Type: ProfileView
    //     0x93b158: ldr             x8, [x8, #0xd50]
    // 0x93b15c: r3 = Null
    //     0x93b15c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd58] Null
    //     0x93b160: ldr             x3, [x3, #0xd58]
    // 0x93b164: r0 = DefaultTypeTest()
    //     0x93b164: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93b168: ldur            x1, [fp, #-8]
    // 0x93b16c: r0 = controller()
    //     0x93b16c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93b170: mov             x1, x0
    // 0x93b174: r0 = checkLoginStatus()
    //     0x93b174: bl              #0x913d8c  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::checkLoginStatus
    // 0x93b178: ldur            x2, [fp, #-0x10]
    // 0x93b17c: LoadField: r0 = r2->field_b
    //     0x93b17c: ldur            w0, [x2, #0xb]
    // 0x93b180: r1 = LoadInt32Instr(r0)
    //     0x93b180: sbfx            x1, x0, #1, #0x1f
    // 0x93b184: mov             x0, x1
    // 0x93b188: r1 = 2
    //     0x93b188: movz            x1, #0x2
    // 0x93b18c: cmp             x1, x0
    // 0x93b190: b.hs            #0x93b210
    // 0x93b194: LoadField: r0 = r2->field_f
    //     0x93b194: ldur            w0, [x2, #0xf]
    // 0x93b198: DecompressPointer r0
    //     0x93b198: add             x0, x0, HEAP, lsl #32
    // 0x93b19c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x93b19c: ldur            w3, [x0, #0x17]
    // 0x93b1a0: DecompressPointer r3
    //     0x93b1a0: add             x3, x3, HEAP, lsl #32
    // 0x93b1a4: mov             x0, x3
    // 0x93b1a8: stur            x3, [fp, #-8]
    // 0x93b1ac: r2 = Null
    //     0x93b1ac: mov             x2, NULL
    // 0x93b1b0: r1 = Null
    //     0x93b1b0: mov             x1, NULL
    // 0x93b1b4: r4 = 60
    //     0x93b1b4: movz            x4, #0x3c
    // 0x93b1b8: branchIfSmi(r0, 0x93b1c4)
    //     0x93b1b8: tbz             w0, #0, #0x93b1c4
    // 0x93b1bc: r4 = LoadClassIdInstr(r0)
    //     0x93b1bc: ldur            x4, [x0, #-1]
    //     0x93b1c0: ubfx            x4, x4, #0xc, #0x14
    // 0x93b1c4: r17 = 4586
    //     0x93b1c4: movz            x17, #0x11ea
    // 0x93b1c8: cmp             x4, x17
    // 0x93b1cc: b.eq            #0x93b1e4
    // 0x93b1d0: r8 = ProfileView
    //     0x93b1d0: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dd50] Type: ProfileView
    //     0x93b1d4: ldr             x8, [x8, #0xd50]
    // 0x93b1d8: r3 = Null
    //     0x93b1d8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd68] Null
    //     0x93b1dc: ldr             x3, [x3, #0xd68]
    // 0x93b1e0: r0 = DefaultTypeTest()
    //     0x93b1e0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93b1e4: ldur            x1, [fp, #-8]
    // 0x93b1e8: r0 = controller()
    //     0x93b1e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93b1ec: mov             x1, x0
    // 0x93b1f0: r0 = getBagCount()
    //     0x93b1f0: bl              #0x9134f0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::getBagCount
    // 0x93b1f4: r0 = Null
    //     0x93b1f4: mov             x0, NULL
    // 0x93b1f8: LeaveFrame
    //     0x93b1f8: mov             SP, fp
    //     0x93b1fc: ldp             fp, lr, [SP], #0x10
    // 0x93b200: ret
    //     0x93b200: ret             
    // 0x93b204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b204: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b208: b               #0x93b0c4
    // 0x93b20c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b20c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93b210: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b210: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Set<Set<dynamic>> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x93b214, size: 0x1c8
    // 0x93b214: EnterFrame
    //     0x93b214: stp             fp, lr, [SP, #-0x10]!
    //     0x93b218: mov             fp, SP
    // 0x93b21c: AllocStack(0x30)
    //     0x93b21c: sub             SP, SP, #0x30
    // 0x93b220: SetupParameters()
    //     0x93b220: ldr             x0, [fp, #0x18]
    //     0x93b224: ldur            w2, [x0, #0x17]
    //     0x93b228: add             x2, x2, HEAP, lsl #32
    //     0x93b22c: stur            x2, [fp, #-8]
    // 0x93b230: CheckStackOverflow
    //     0x93b230: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b234: cmp             SP, x16
    //     0x93b238: b.ls            #0x93b3d4
    // 0x93b23c: r1 = <Set>
    //     0x93b23c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24550] TypeArguments: <Set>
    //     0x93b240: ldr             x1, [x1, #0x550]
    // 0x93b244: r0 = _Set()
    //     0x93b244: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93b248: mov             x1, x0
    // 0x93b24c: r0 = _Uint32List
    //     0x93b24c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93b250: stur            x1, [fp, #-0x10]
    // 0x93b254: StoreField: r1->field_1b = r0
    //     0x93b254: stur            w0, [x1, #0x1b]
    // 0x93b258: StoreField: r1->field_b = rZR
    //     0x93b258: stur            wzr, [x1, #0xb]
    // 0x93b25c: r2 = const []
    //     0x93b25c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93b260: StoreField: r1->field_f = r2
    //     0x93b260: stur            w2, [x1, #0xf]
    // 0x93b264: StoreField: r1->field_13 = rZR
    //     0x93b264: stur            wzr, [x1, #0x13]
    // 0x93b268: ArrayStore: r1[0] = rZR  ; List_4
    //     0x93b268: stur            wzr, [x1, #0x17]
    // 0x93b26c: ldr             x16, [fp, #0x10]
    // 0x93b270: str             x16, [SP]
    // 0x93b274: r4 = 0
    //     0x93b274: movz            x4, #0
    // 0x93b278: ldr             x0, [SP]
    // 0x93b27c: r16 = UnlinkedCall_0x613b5c
    //     0x93b27c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2dd78] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x93b280: add             x16, x16, #0xd78
    // 0x93b284: ldp             x5, lr, [x16]
    // 0x93b288: blr             lr
    // 0x93b28c: mov             x3, x0
    // 0x93b290: r2 = Null
    //     0x93b290: mov             x2, NULL
    // 0x93b294: r1 = Null
    //     0x93b294: mov             x1, NULL
    // 0x93b298: stur            x3, [fp, #-0x18]
    // 0x93b29c: r4 = 60
    //     0x93b29c: movz            x4, #0x3c
    // 0x93b2a0: branchIfSmi(r0, 0x93b2ac)
    //     0x93b2a0: tbz             w0, #0, #0x93b2ac
    // 0x93b2a4: r4 = LoadClassIdInstr(r0)
    //     0x93b2a4: ldur            x4, [x0, #-1]
    //     0x93b2a8: ubfx            x4, x4, #0xc, #0x14
    // 0x93b2ac: cmp             x4, #0x3f
    // 0x93b2b0: b.eq            #0x93b2c4
    // 0x93b2b4: r8 = bool
    //     0x93b2b4: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x93b2b8: r3 = Null
    //     0x93b2b8: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dd88] Null
    //     0x93b2bc: ldr             x3, [x3, #0xd88]
    // 0x93b2c0: r0 = bool()
    //     0x93b2c0: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x93b2c4: ldur            x0, [fp, #-0x18]
    // 0x93b2c8: tbnz            w0, #4, #0x93b328
    // 0x93b2cc: ldur            x2, [fp, #-8]
    // 0x93b2d0: r1 = Null
    //     0x93b2d0: mov             x1, NULL
    // 0x93b2d4: r0 = _Set()
    //     0x93b2d4: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93b2d8: mov             x2, x0
    // 0x93b2dc: r0 = _Uint32List
    //     0x93b2dc: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93b2e0: stur            x2, [fp, #-0x18]
    // 0x93b2e4: StoreField: r2->field_1b = r0
    //     0x93b2e4: stur            w0, [x2, #0x1b]
    // 0x93b2e8: StoreField: r2->field_b = rZR
    //     0x93b2e8: stur            wzr, [x2, #0xb]
    // 0x93b2ec: r3 = const []
    //     0x93b2ec: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93b2f0: StoreField: r2->field_f = r3
    //     0x93b2f0: stur            w3, [x2, #0xf]
    // 0x93b2f4: StoreField: r2->field_13 = rZR
    //     0x93b2f4: stur            wzr, [x2, #0x13]
    // 0x93b2f8: ArrayStore: r2[0] = rZR  ; List_4
    //     0x93b2f8: stur            wzr, [x2, #0x17]
    // 0x93b2fc: ldur            x4, [fp, #-8]
    // 0x93b300: LoadField: r1 = r4->field_f
    //     0x93b300: ldur            w1, [x4, #0xf]
    // 0x93b304: DecompressPointer r1
    //     0x93b304: add             x1, x1, HEAP, lsl #32
    // 0x93b308: r0 = openLoginAwait()
    //     0x93b308: bl              #0x93b3dc  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::openLoginAwait
    // 0x93b30c: ldur            x1, [fp, #-0x18]
    // 0x93b310: mov             x2, x0
    // 0x93b314: r0 = add()
    //     0x93b314: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93b318: ldur            x1, [fp, #-0x10]
    // 0x93b31c: ldur            x2, [fp, #-0x18]
    // 0x93b320: r0 = add()
    //     0x93b320: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93b324: b               #0x93b3c4
    // 0x93b328: ldur            x4, [fp, #-8]
    // 0x93b32c: r0 = _Uint32List
    //     0x93b32c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93b330: r3 = const []
    //     0x93b330: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93b334: r1 = <Future<Null?>>
    //     0x93b334: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc88] TypeArguments: <Future<Null?>>
    //     0x93b338: ldr             x1, [x1, #0xc88]
    // 0x93b33c: r0 = _Set()
    //     0x93b33c: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93b340: mov             x2, x0
    // 0x93b344: r0 = _Uint32List
    //     0x93b344: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93b348: stur            x2, [fp, #-0x18]
    // 0x93b34c: StoreField: r2->field_1b = r0
    //     0x93b34c: stur            w0, [x2, #0x1b]
    // 0x93b350: StoreField: r2->field_b = rZR
    //     0x93b350: stur            wzr, [x2, #0xb]
    // 0x93b354: r0 = const []
    //     0x93b354: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93b358: StoreField: r2->field_f = r0
    //     0x93b358: stur            w0, [x2, #0xf]
    // 0x93b35c: StoreField: r2->field_13 = rZR
    //     0x93b35c: stur            wzr, [x2, #0x13]
    // 0x93b360: ArrayStore: r2[0] = rZR  ; List_4
    //     0x93b360: stur            wzr, [x2, #0x17]
    // 0x93b364: ldur            x0, [fp, #-8]
    // 0x93b368: LoadField: r1 = r0->field_f
    //     0x93b368: ldur            w1, [x0, #0xf]
    // 0x93b36c: DecompressPointer r1
    //     0x93b36c: add             x1, x1, HEAP, lsl #32
    // 0x93b370: LoadField: r3 = r1->field_4b
    //     0x93b370: ldur            w3, [x1, #0x4b]
    // 0x93b374: DecompressPointer r3
    //     0x93b374: add             x3, x3, HEAP, lsl #32
    // 0x93b378: mov             x1, x3
    // 0x93b37c: r0 = getConnectivityType()
    //     0x93b37c: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x93b380: ldur            x2, [fp, #-8]
    // 0x93b384: r1 = Function '<anonymous closure>':.
    //     0x93b384: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dd98] AnonymousClosure: (0x93b4b4), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped (0x93ad48)
    //     0x93b388: ldr             x1, [x1, #0xd98]
    // 0x93b38c: stur            x0, [fp, #-8]
    // 0x93b390: r0 = AllocateClosure()
    //     0x93b390: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93b394: r16 = <Null?>
    //     0x93b394: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x93b398: ldur            lr, [fp, #-8]
    // 0x93b39c: stp             lr, x16, [SP, #8]
    // 0x93b3a0: str             x0, [SP]
    // 0x93b3a4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x93b3a4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x93b3a8: r0 = then()
    //     0x93b3a8: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x93b3ac: ldur            x1, [fp, #-0x18]
    // 0x93b3b0: mov             x2, x0
    // 0x93b3b4: r0 = add()
    //     0x93b3b4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93b3b8: ldur            x1, [fp, #-0x10]
    // 0x93b3bc: ldur            x2, [fp, #-0x18]
    // 0x93b3c0: r0 = add()
    //     0x93b3c0: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93b3c4: ldur            x0, [fp, #-0x10]
    // 0x93b3c8: LeaveFrame
    //     0x93b3c8: mov             SP, fp
    //     0x93b3cc: ldp             fp, lr, [SP], #0x10
    // 0x93b3d0: ret
    //     0x93b3d0: ret             
    // 0x93b3d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b3d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b3d8: b               #0x93b23c
  }
  _ openLoginAwait(/* No info */) async {
    // ** addr: 0x93b3dc, size: 0xd8
    // 0x93b3dc: EnterFrame
    //     0x93b3dc: stp             fp, lr, [SP, #-0x10]!
    //     0x93b3e0: mov             fp, SP
    // 0x93b3e4: AllocStack(0x10)
    //     0x93b3e4: sub             SP, SP, #0x10
    // 0x93b3e8: SetupParameters(_MainPageState this /* r1 => r1, fp-0x10 */)
    //     0x93b3e8: stur            NULL, [fp, #-8]
    //     0x93b3ec: stur            x1, [fp, #-0x10]
    // 0x93b3f0: CheckStackOverflow
    //     0x93b3f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b3f4: cmp             SP, x16
    //     0x93b3f8: b.ls            #0x93b4a8
    // 0x93b3fc: InitAsync() -> Future
    //     0x93b3fc: mov             x0, NULL
    //     0x93b400: bl              #0x6326e0  ; InitAsyncStub
    // 0x93b404: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0x93b404: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93b408: ldr             x0, [x0, #0x1cd0]
    //     0x93b40c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93b410: cmp             w0, w16
    //     0x93b414: b.ne            #0x93b424
    //     0x93b418: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0x93b41c: ldr             x2, [x2, #0xca8]
    //     0x93b420: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93b424: mov             x2, x0
    // 0x93b428: LoadField: r0 = r2->field_b
    //     0x93b428: ldur            w0, [x2, #0xb]
    // 0x93b42c: r1 = LoadInt32Instr(r0)
    //     0x93b42c: sbfx            x1, x0, #1, #0x1f
    // 0x93b430: mov             x0, x1
    // 0x93b434: r1 = 1
    //     0x93b434: movz            x1, #0x1
    // 0x93b438: cmp             x1, x0
    // 0x93b43c: b.hs            #0x93b4b0
    // 0x93b440: LoadField: r0 = r2->field_f
    //     0x93b440: ldur            w0, [x2, #0xf]
    // 0x93b444: DecompressPointer r0
    //     0x93b444: add             x0, x0, HEAP, lsl #32
    // 0x93b448: LoadField: r3 = r0->field_13
    //     0x93b448: ldur            w3, [x0, #0x13]
    // 0x93b44c: DecompressPointer r3
    //     0x93b44c: add             x3, x3, HEAP, lsl #32
    // 0x93b450: mov             x0, x3
    // 0x93b454: stur            x3, [fp, #-0x10]
    // 0x93b458: r2 = Null
    //     0x93b458: mov             x2, NULL
    // 0x93b45c: r1 = Null
    //     0x93b45c: mov             x1, NULL
    // 0x93b460: r4 = 60
    //     0x93b460: movz            x4, #0x3c
    // 0x93b464: branchIfSmi(r0, 0x93b470)
    //     0x93b464: tbz             w0, #0, #0x93b470
    // 0x93b468: r4 = LoadClassIdInstr(r0)
    //     0x93b468: ldur            x4, [x0, #-1]
    //     0x93b46c: ubfx            x4, x4, #0xc, #0x14
    // 0x93b470: r17 = 4596
    //     0x93b470: movz            x17, #0x11f4
    // 0x93b474: cmp             x4, x17
    // 0x93b478: b.eq            #0x93b490
    // 0x93b47c: r8 = OrdersView
    //     0x93b47c: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0x93b480: ldr             x8, [x8, #0xcb0]
    // 0x93b484: r3 = Null
    //     0x93b484: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2ddc0] Null
    //     0x93b488: ldr             x3, [x3, #0xdc0]
    // 0x93b48c: r0 = DefaultTypeTest()
    //     0x93b48c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93b490: ldur            x1, [fp, #-0x10]
    // 0x93b494: r0 = controller()
    //     0x93b494: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93b498: mov             x1, x0
    // 0x93b49c: r0 = openLoginAwait()
    //     0x93b49c: bl              #0x8a55a0  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::openLoginAwait
    // 0x93b4a0: r0 = Null
    //     0x93b4a0: mov             x0, NULL
    // 0x93b4a4: r0 = ReturnAsyncNotFuture()
    //     0x93b4a4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93b4a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b4a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b4ac: b               #0x93b3fc
    // 0x93b4b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b4b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x93b4b4, size: 0x178
    // 0x93b4b4: EnterFrame
    //     0x93b4b4: stp             fp, lr, [SP, #-0x10]!
    //     0x93b4b8: mov             fp, SP
    // 0x93b4bc: AllocStack(0x10)
    //     0x93b4bc: sub             SP, SP, #0x10
    // 0x93b4c0: SetupParameters()
    //     0x93b4c0: ldr             x0, [fp, #0x18]
    //     0x93b4c4: ldur            w1, [x0, #0x17]
    //     0x93b4c8: add             x1, x1, HEAP, lsl #32
    // 0x93b4cc: CheckStackOverflow
    //     0x93b4cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b4d0: cmp             SP, x16
    //     0x93b4d4: b.ls            #0x93b61c
    // 0x93b4d8: LoadField: r0 = r1->field_f
    //     0x93b4d8: ldur            w0, [x1, #0xf]
    // 0x93b4dc: DecompressPointer r0
    //     0x93b4dc: add             x0, x0, HEAP, lsl #32
    // 0x93b4e0: ldr             x1, [fp, #0x10]
    // 0x93b4e4: StoreField: r0->field_4f = r1
    //     0x93b4e4: stur            w1, [x0, #0x4f]
    // 0x93b4e8: tbnz            w1, #4, #0x93b60c
    // 0x93b4ec: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0x93b4ec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93b4f0: ldr             x0, [x0, #0x1cd0]
    //     0x93b4f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93b4f8: cmp             w0, w16
    //     0x93b4fc: b.ne            #0x93b50c
    //     0x93b500: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0x93b504: ldr             x2, [x2, #0xca8]
    //     0x93b508: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93b50c: mov             x3, x0
    // 0x93b510: stur            x3, [fp, #-0x10]
    // 0x93b514: LoadField: r0 = r3->field_b
    //     0x93b514: ldur            w0, [x3, #0xb]
    // 0x93b518: r1 = LoadInt32Instr(r0)
    //     0x93b518: sbfx            x1, x0, #1, #0x1f
    // 0x93b51c: mov             x0, x1
    // 0x93b520: r1 = 1
    //     0x93b520: movz            x1, #0x1
    // 0x93b524: cmp             x1, x0
    // 0x93b528: b.hs            #0x93b624
    // 0x93b52c: LoadField: r0 = r3->field_f
    //     0x93b52c: ldur            w0, [x3, #0xf]
    // 0x93b530: DecompressPointer r0
    //     0x93b530: add             x0, x0, HEAP, lsl #32
    // 0x93b534: LoadField: r4 = r0->field_13
    //     0x93b534: ldur            w4, [x0, #0x13]
    // 0x93b538: DecompressPointer r4
    //     0x93b538: add             x4, x4, HEAP, lsl #32
    // 0x93b53c: mov             x0, x4
    // 0x93b540: stur            x4, [fp, #-8]
    // 0x93b544: r2 = Null
    //     0x93b544: mov             x2, NULL
    // 0x93b548: r1 = Null
    //     0x93b548: mov             x1, NULL
    // 0x93b54c: r4 = 60
    //     0x93b54c: movz            x4, #0x3c
    // 0x93b550: branchIfSmi(r0, 0x93b55c)
    //     0x93b550: tbz             w0, #0, #0x93b55c
    // 0x93b554: r4 = LoadClassIdInstr(r0)
    //     0x93b554: ldur            x4, [x0, #-1]
    //     0x93b558: ubfx            x4, x4, #0xc, #0x14
    // 0x93b55c: r17 = 4596
    //     0x93b55c: movz            x17, #0x11f4
    // 0x93b560: cmp             x4, x17
    // 0x93b564: b.eq            #0x93b57c
    // 0x93b568: r8 = OrdersView
    //     0x93b568: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0x93b56c: ldr             x8, [x8, #0xcb0]
    // 0x93b570: r3 = Null
    //     0x93b570: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dda0] Null
    //     0x93b574: ldr             x3, [x3, #0xda0]
    // 0x93b578: r0 = DefaultTypeTest()
    //     0x93b578: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93b57c: ldur            x1, [fp, #-8]
    // 0x93b580: r0 = controller()
    //     0x93b580: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93b584: LoadField: r1 = r0->field_4f
    //     0x93b584: ldur            w1, [x0, #0x4f]
    // 0x93b588: DecompressPointer r1
    //     0x93b588: add             x1, x1, HEAP, lsl #32
    // 0x93b58c: r0 = initRefresh()
    //     0x93b58c: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x93b590: ldur            x2, [fp, #-0x10]
    // 0x93b594: LoadField: r0 = r2->field_b
    //     0x93b594: ldur            w0, [x2, #0xb]
    // 0x93b598: r1 = LoadInt32Instr(r0)
    //     0x93b598: sbfx            x1, x0, #1, #0x1f
    // 0x93b59c: mov             x0, x1
    // 0x93b5a0: r1 = 1
    //     0x93b5a0: movz            x1, #0x1
    // 0x93b5a4: cmp             x1, x0
    // 0x93b5a8: b.hs            #0x93b628
    // 0x93b5ac: LoadField: r0 = r2->field_f
    //     0x93b5ac: ldur            w0, [x2, #0xf]
    // 0x93b5b0: DecompressPointer r0
    //     0x93b5b0: add             x0, x0, HEAP, lsl #32
    // 0x93b5b4: LoadField: r3 = r0->field_13
    //     0x93b5b4: ldur            w3, [x0, #0x13]
    // 0x93b5b8: DecompressPointer r3
    //     0x93b5b8: add             x3, x3, HEAP, lsl #32
    // 0x93b5bc: mov             x0, x3
    // 0x93b5c0: stur            x3, [fp, #-8]
    // 0x93b5c4: r2 = Null
    //     0x93b5c4: mov             x2, NULL
    // 0x93b5c8: r1 = Null
    //     0x93b5c8: mov             x1, NULL
    // 0x93b5cc: r4 = 60
    //     0x93b5cc: movz            x4, #0x3c
    // 0x93b5d0: branchIfSmi(r0, 0x93b5dc)
    //     0x93b5d0: tbz             w0, #0, #0x93b5dc
    // 0x93b5d4: r4 = LoadClassIdInstr(r0)
    //     0x93b5d4: ldur            x4, [x0, #-1]
    //     0x93b5d8: ubfx            x4, x4, #0xc, #0x14
    // 0x93b5dc: r17 = 4596
    //     0x93b5dc: movz            x17, #0x11f4
    // 0x93b5e0: cmp             x4, x17
    // 0x93b5e4: b.eq            #0x93b5fc
    // 0x93b5e8: r8 = OrdersView
    //     0x93b5e8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0x93b5ec: ldr             x8, [x8, #0xcb0]
    // 0x93b5f0: r3 = Null
    //     0x93b5f0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2ddb0] Null
    //     0x93b5f4: ldr             x3, [x3, #0xdb0]
    // 0x93b5f8: r0 = DefaultTypeTest()
    //     0x93b5f8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x93b5fc: ldur            x1, [fp, #-8]
    // 0x93b600: r0 = controller()
    //     0x93b600: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x93b604: mov             x1, x0
    // 0x93b608: r0 = getOrders()
    //     0x93b608: bl              #0x8a5968  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getOrders
    // 0x93b60c: r0 = Null
    //     0x93b60c: mov             x0, NULL
    // 0x93b610: LeaveFrame
    //     0x93b610: mov             SP, fp
    //     0x93b614: ldp             fp, lr, [SP], #0x10
    // 0x93b618: ret
    //     0x93b618: ret             
    // 0x93b61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b61c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b620: b               #0x93b4d8
    // 0x93b624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b624: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93b628: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93b628: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<Widget> _pages() {
    // ** addr: 0x93b62c, size: 0x74
    // 0x93b62c: EnterFrame
    //     0x93b62c: stp             fp, lr, [SP, #-0x10]!
    //     0x93b630: mov             fp, SP
    // 0x93b634: AllocStack(0x8)
    //     0x93b634: sub             SP, SP, #8
    // 0x93b638: r0 = 8
    //     0x93b638: movz            x0, #0x8
    // 0x93b63c: mov             x2, x0
    // 0x93b640: r1 = Null
    //     0x93b640: mov             x1, NULL
    // 0x93b644: r0 = AllocateArray()
    //     0x93b644: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93b648: stur            x0, [fp, #-8]
    // 0x93b64c: r16 = Instance_HomePage
    //     0x93b64c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf28] Obj!HomePage@d67701
    //     0x93b650: ldr             x16, [x16, #0xf28]
    // 0x93b654: StoreField: r0->field_f = r16
    //     0x93b654: stur            w16, [x0, #0xf]
    // 0x93b658: r16 = Instance_OrdersView
    //     0x93b658: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf00] Obj!OrdersView@d676c1
    //     0x93b65c: ldr             x16, [x16, #0xf00]
    // 0x93b660: StoreField: r0->field_13 = r16
    //     0x93b660: stur            w16, [x0, #0x13]
    // 0x93b664: r16 = Instance_ProfileView
    //     0x93b664: add             x16, PP, #0xd, lsl #12  ; [pp+0xdf08] Obj!ProfileView@d675a1
    //     0x93b668: ldr             x16, [x16, #0xf08]
    // 0x93b66c: ArrayStore: r0[0] = r16  ; List_4
    //     0x93b66c: stur            w16, [x0, #0x17]
    // 0x93b670: r16 = Instance_BrowseView
    //     0x93b670: add             x16, PP, #0xd, lsl #12  ; [pp+0xded8] Obj!BrowseView@d678a1
    //     0x93b674: ldr             x16, [x16, #0xed8]
    // 0x93b678: StoreField: r0->field_1b = r16
    //     0x93b678: stur            w16, [x0, #0x1b]
    // 0x93b67c: r1 = <Widget>
    //     0x93b67c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x93b680: r0 = AllocateGrowableArray()
    //     0x93b680: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x93b684: ldur            x1, [fp, #-8]
    // 0x93b688: StoreField: r0->field_f = r1
    //     0x93b688: stur            w1, [x0, #0xf]
    // 0x93b68c: r1 = 8
    //     0x93b68c: movz            x1, #0x8
    // 0x93b690: StoreField: r0->field_b = r1
    //     0x93b690: stur            w1, [x0, #0xb]
    // 0x93b694: LeaveFrame
    //     0x93b694: mov             SP, fp
    //     0x93b698: ldp             fp, lr, [SP], #0x10
    // 0x93b69c: ret
    //     0x93b69c: ret             
  }
  [closure] void <anonymous closure>(dynamic, Uri) {
    // ** addr: 0x93b6a0, size: 0xc4
    // 0x93b6a0: EnterFrame
    //     0x93b6a0: stp             fp, lr, [SP, #-0x10]!
    //     0x93b6a4: mov             fp, SP
    // 0x93b6a8: AllocStack(0x10)
    //     0x93b6a8: sub             SP, SP, #0x10
    // 0x93b6ac: SetupParameters()
    //     0x93b6ac: ldr             x0, [fp, #0x18]
    //     0x93b6b0: ldur            w1, [x0, #0x17]
    //     0x93b6b4: add             x1, x1, HEAP, lsl #32
    //     0x93b6b8: stur            x1, [fp, #-8]
    // 0x93b6bc: CheckStackOverflow
    //     0x93b6bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b6c0: cmp             SP, x16
    //     0x93b6c4: b.ls            #0x93b75c
    // 0x93b6c8: r0 = InitLateStaticField(0x678) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x93b6c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93b6cc: ldr             x0, [x0, #0xcf0]
    //     0x93b6d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93b6d4: cmp             w0, w16
    //     0x93b6d8: b.ne            #0x93b6e4
    //     0x93b6dc: ldr             x2, [PP, #0x880]  ; [pp+0x880] Field <::.debugPrint>: static late (offset: 0x678)
    //     0x93b6e0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x93b6e4: r1 = Null
    //     0x93b6e4: mov             x1, NULL
    // 0x93b6e8: r2 = 4
    //     0x93b6e8: movz            x2, #0x4
    // 0x93b6ec: r0 = AllocateArray()
    //     0x93b6ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93b6f0: r16 = "onAppLink: "
    //     0x93b6f0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d818] "onAppLink: "
    //     0x93b6f4: ldr             x16, [x16, #0x818]
    // 0x93b6f8: StoreField: r0->field_f = r16
    //     0x93b6f8: stur            w16, [x0, #0xf]
    // 0x93b6fc: ldr             x1, [fp, #0x10]
    // 0x93b700: StoreField: r0->field_13 = r1
    //     0x93b700: stur            w1, [x0, #0x13]
    // 0x93b704: str             x0, [SP]
    // 0x93b708: r0 = _interpolate()
    //     0x93b708: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x93b70c: str             NULL, [SP]
    // 0x93b710: mov             x1, x0
    // 0x93b714: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x93b714: ldr             x4, [PP, #0x890]  ; [pp+0x890] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x93b718: r0 = debugPrintThrottled()
    //     0x93b718: bl              #0x6368ec  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x93b71c: ldur            x0, [fp, #-8]
    // 0x93b720: LoadField: r1 = r0->field_f
    //     0x93b720: ldur            w1, [x0, #0xf]
    // 0x93b724: DecompressPointer r1
    //     0x93b724: add             x1, x1, HEAP, lsl #32
    // 0x93b728: ldr             x0, [fp, #0x10]
    // 0x93b72c: StoreField: r1->field_23 = r0
    //     0x93b72c: stur            w0, [x1, #0x23]
    //     0x93b730: ldurb           w16, [x1, #-1]
    //     0x93b734: ldurb           w17, [x0, #-1]
    //     0x93b738: and             x16, x17, x16, lsr #2
    //     0x93b73c: tst             x16, HEAP, lsr #32
    //     0x93b740: b.eq            #0x93b748
    //     0x93b744: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93b748: r0 = _handleInitialUri()
    //     0x93b748: bl              #0x93b764  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_handleInitialUri
    // 0x93b74c: r0 = Null
    //     0x93b74c: mov             x0, NULL
    // 0x93b750: LeaveFrame
    //     0x93b750: mov             SP, fp
    //     0x93b754: ldp             fp, lr, [SP], #0x10
    // 0x93b758: ret
    //     0x93b758: ret             
    // 0x93b75c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b75c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b760: b               #0x93b6c8
  }
  _ _handleInitialUri(/* No info */) async {
    // ** addr: 0x93b764, size: 0x298
    // 0x93b764: EnterFrame
    //     0x93b764: stp             fp, lr, [SP, #-0x10]!
    //     0x93b768: mov             fp, SP
    // 0x93b76c: AllocStack(0xb8)
    //     0x93b76c: sub             SP, SP, #0xb8
    // 0x93b770: SetupParameters(_MainPageState this /* r1 => r1, fp-0x70 */)
    //     0x93b770: stur            NULL, [fp, #-8]
    //     0x93b774: stur            x1, [fp, #-0x70]
    // 0x93b778: CheckStackOverflow
    //     0x93b778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93b77c: cmp             SP, x16
    //     0x93b780: b.ls            #0x93b9f4
    // 0x93b784: r1 = 2
    //     0x93b784: movz            x1, #0x2
    // 0x93b788: r0 = AllocateContext()
    //     0x93b788: bl              #0x16f6108  ; AllocateContextStub
    // 0x93b78c: mov             x2, x0
    // 0x93b790: ldur            x1, [fp, #-0x70]
    // 0x93b794: stur            x2, [fp, #-0x78]
    // 0x93b798: StoreField: r2->field_f = r1
    //     0x93b798: stur            w1, [x2, #0xf]
    // 0x93b79c: InitAsync() -> Future<void?>
    //     0x93b79c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x93b7a0: bl              #0x6326e0  ; InitAsyncStub
    // 0x93b7a4: ldur            x0, [fp, #-0x70]
    // 0x93b7a8: LoadField: r2 = r0->field_23
    //     0x93b7a8: ldur            w2, [x0, #0x23]
    // 0x93b7ac: DecompressPointer r2
    //     0x93b7ac: add             x2, x2, HEAP, lsl #32
    // 0x93b7b0: cmp             w2, NULL
    // 0x93b7b4: b.ne            #0x93b7c4
    // 0x93b7b8: mov             x1, x0
    // 0x93b7bc: r0 = setDefaultUtmParameters()
    //     0x93b7bc: bl              #0x916c2c  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::setDefaultUtmParameters
    // 0x93b7c0: b               #0x93b7cc
    // 0x93b7c4: ldur            x1, [fp, #-0x70]
    // 0x93b7c8: r0 = setUtmParameters()
    //     0x93b7c8: bl              #0x91690c  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::setUtmParameters
    // 0x93b7cc: ldur            x2, [fp, #-0x70]
    // 0x93b7d0: LoadField: r0 = r2->field_f
    //     0x93b7d0: ldur            w0, [x2, #0xf]
    // 0x93b7d4: DecompressPointer r0
    //     0x93b7d4: add             x0, x0, HEAP, lsl #32
    // 0x93b7d8: cmp             w0, NULL
    // 0x93b7dc: b.ne            #0x93b7e8
    // 0x93b7e0: r0 = Null
    //     0x93b7e0: mov             x0, NULL
    // 0x93b7e4: r0 = ReturnAsyncNotFuture()
    //     0x93b7e4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93b7e8: LoadField: r3 = r2->field_23
    //     0x93b7e8: ldur            w3, [x2, #0x23]
    // 0x93b7ec: DecompressPointer r3
    //     0x93b7ec: add             x3, x3, HEAP, lsl #32
    // 0x93b7f0: stur            x3, [fp, #-0x80]
    // 0x93b7f4: cmp             w3, NULL
    // 0x93b7f8: b.eq            #0x93b9dc
    // 0x93b7fc: r0 = LoadClassIdInstr(r3)
    //     0x93b7fc: ldur            x0, [x3, #-1]
    //     0x93b800: ubfx            x0, x0, #0xc, #0x14
    // 0x93b804: mov             x1, x3
    // 0x93b808: r0 = GDT[cid_x0 + -0xf32]()
    //     0x93b808: sub             lr, x0, #0xf32
    //     0x93b80c: ldr             lr, [x21, lr, lsl #3]
    //     0x93b810: blr             lr
    // 0x93b814: tbnz            w0, #4, #0x93b924
    // 0x93b818: ldur            x2, [fp, #-0x70]
    // 0x93b81c: LoadField: r3 = r2->field_23
    //     0x93b81c: ldur            w3, [x2, #0x23]
    // 0x93b820: DecompressPointer r3
    //     0x93b820: add             x3, x3, HEAP, lsl #32
    // 0x93b824: stur            x3, [fp, #-0x80]
    // 0x93b828: cmp             w3, NULL
    // 0x93b82c: b.ne            #0x93b83c
    // 0x93b830: mov             x3, x2
    // 0x93b834: r5 = Null
    //     0x93b834: mov             x5, NULL
    // 0x93b838: b               #0x93b85c
    // 0x93b83c: r0 = LoadClassIdInstr(r3)
    //     0x93b83c: ldur            x0, [x3, #-1]
    //     0x93b840: ubfx            x0, x0, #0xc, #0x14
    // 0x93b844: mov             x1, x3
    // 0x93b848: r0 = GDT[cid_x0 + -0xfd1]()
    //     0x93b848: sub             lr, x0, #0xfd1
    //     0x93b84c: ldr             lr, [x21, lr, lsl #3]
    //     0x93b850: blr             lr
    // 0x93b854: mov             x5, x0
    // 0x93b858: ldur            x3, [fp, #-0x70]
    // 0x93b85c: ldur            x4, [fp, #-0x78]
    // 0x93b860: mov             x0, x5
    // 0x93b864: stur            x5, [fp, #-0x88]
    // 0x93b868: StoreField: r4->field_13 = r0
    //     0x93b868: stur            w0, [x4, #0x13]
    //     0x93b86c: ldurb           w16, [x4, #-1]
    //     0x93b870: ldurb           w17, [x0, #-1]
    //     0x93b874: and             x16, x17, x16, lsr #2
    //     0x93b878: tst             x16, HEAP, lsr #32
    //     0x93b87c: b.eq            #0x93b884
    //     0x93b880: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x93b884: LoadField: r0 = r3->field_3f
    //     0x93b884: ldur            w0, [x3, #0x3f]
    // 0x93b888: DecompressPointer r0
    //     0x93b888: add             x0, x0, HEAP, lsl #32
    // 0x93b88c: LoadField: r6 = r0->field_4b
    //     0x93b88c: ldur            w6, [x0, #0x4b]
    // 0x93b890: DecompressPointer r6
    //     0x93b890: add             x6, x6, HEAP, lsl #32
    // 0x93b894: stur            x6, [fp, #-0x80]
    // 0x93b898: r16 = ""
    //     0x93b898: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93b89c: str             x16, [SP]
    // 0x93b8a0: mov             x1, x6
    // 0x93b8a4: r2 = "token"
    //     0x93b8a4: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x93b8a8: ldr             x2, [x2, #0x958]
    // 0x93b8ac: r4 = const [0, 0x3, 0x1, 0x2, defaultValue, 0x2, null]
    //     0x93b8ac: add             x4, PP, #0x11, lsl #12  ; [pp+0x11f48] List(7) [0, 0x3, 0x1, 0x2, "defaultValue", 0x2, Null]
    //     0x93b8b0: ldr             x4, [x4, #0xf48]
    // 0x93b8b4: r0 = getString()
    //     0x93b8b4: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x93b8b8: ldur            x2, [fp, #-0x78]
    // 0x93b8bc: r1 = Function '<anonymous closure>':.
    //     0x93b8bc: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2ddf8] AnonymousClosure: (0x93c154), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_handleInitialUri (0x93b764)
    //     0x93b8c0: ldr             x1, [x1, #0xdf8]
    // 0x93b8c4: stur            x0, [fp, #-0x80]
    // 0x93b8c8: r0 = AllocateClosure()
    //     0x93b8c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93b8cc: mov             x4, x0
    // 0x93b8d0: ldur            x3, [fp, #-0x80]
    // 0x93b8d4: stur            x4, [fp, #-0x98]
    // 0x93b8d8: LoadField: r5 = r3->field_7
    //     0x93b8d8: ldur            w5, [x3, #7]
    // 0x93b8dc: DecompressPointer r5
    //     0x93b8dc: add             x5, x5, HEAP, lsl #32
    // 0x93b8e0: mov             x0, x4
    // 0x93b8e4: mov             x2, x5
    // 0x93b8e8: stur            x5, [fp, #-0x90]
    // 0x93b8ec: r1 = Null
    //     0x93b8ec: mov             x1, NULL
    // 0x93b8f0: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0x93b8f0: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0x93b8f4: ldr             x8, [x8, #0xce8]
    // 0x93b8f8: LoadField: r9 = r8->field_7
    //     0x93b8f8: ldur            x9, [x8, #7]
    // 0x93b8fc: r3 = Null
    //     0x93b8fc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2de00] Null
    //     0x93b900: ldr             x3, [x3, #0xe00]
    // 0x93b904: blr             x9
    // 0x93b908: ldur            x16, [fp, #-0x80]
    // 0x93b90c: stp             x16, NULL, [SP, #0x10]
    // 0x93b910: ldur            x16, [fp, #-0x98]
    // 0x93b914: stp             NULL, x16, [SP]
    // 0x93b918: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x93b918: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x93b91c: r0 = then()
    //     0x93b91c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x93b920: b               #0x93b9dc
    // 0x93b924: ldur            x1, [fp, #-0x70]
    // 0x93b928: r0 = openUrlInApp()
    //     0x93b928: bl              #0x93b9fc  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x93b92c: b               #0x93b9dc
    // 0x93b930: sub             SP, fp, #0xb8
    // 0x93b934: mov             x4, x0
    // 0x93b938: mov             x3, x1
    // 0x93b93c: stur            x0, [fp, #-0x78]
    // 0x93b940: stur            x1, [fp, #-0x80]
    // 0x93b944: r0 = 60
    //     0x93b944: movz            x0, #0x3c
    // 0x93b948: branchIfSmi(r4, 0x93b954)
    //     0x93b948: tbz             w4, #0, #0x93b954
    // 0x93b94c: r0 = LoadClassIdInstr(r4)
    //     0x93b94c: ldur            x0, [x4, #-1]
    //     0x93b950: ubfx            x0, x0, #0xc, #0x14
    // 0x93b954: cmp             x0, #0x6ee
    // 0x93b958: b.ne            #0x93b964
    // 0x93b95c: r0 = Null
    //     0x93b95c: mov             x0, NULL
    // 0x93b960: r0 = ReturnAsyncNotFuture()
    //     0x93b960: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93b964: mov             x0, x4
    // 0x93b968: r2 = Null
    //     0x93b968: mov             x2, NULL
    // 0x93b96c: r1 = Null
    //     0x93b96c: mov             x1, NULL
    // 0x93b970: cmp             w0, NULL
    // 0x93b974: b.eq            #0x93b9b0
    // 0x93b978: branchIfSmi(r0, 0x93b9b0)
    //     0x93b978: tbz             w0, #0, #0x93b9b0
    // 0x93b97c: r3 = LoadClassIdInstr(r0)
    //     0x93b97c: ldur            x3, [x0, #-1]
    //     0x93b980: ubfx            x3, x3, #0xc, #0x14
    // 0x93b984: sub             x3, x3, #0xcf
    // 0x93b988: cmp             x3, #1
    // 0x93b98c: b.ls            #0x93b9b8
    // 0x93b990: cmp             x3, #0x217
    // 0x93b994: b.eq            #0x93b9b8
    // 0x93b998: sub             x3, x3, #0x36f
    // 0x93b99c: cmp             x3, #1
    // 0x93b9a0: b.ls            #0x93b9b8
    // 0x93b9a4: r17 = 5658
    //     0x93b9a4: movz            x17, #0x161a
    // 0x93b9a8: cmp             x3, x17
    // 0x93b9ac: b.eq            #0x93b9b8
    // 0x93b9b0: r0 = false
    //     0x93b9b0: add             x0, NULL, #0x30  ; false
    // 0x93b9b4: b               #0x93b9bc
    // 0x93b9b8: r0 = true
    //     0x93b9b8: add             x0, NULL, #0x20  ; true
    // 0x93b9bc: tbnz            w0, #4, #0x93b9e4
    // 0x93b9c0: ldur            x0, [fp, #-0x70]
    // 0x93b9c4: LoadField: r1 = r0->field_f
    //     0x93b9c4: ldur            w1, [x0, #0xf]
    // 0x93b9c8: DecompressPointer r1
    //     0x93b9c8: add             x1, x1, HEAP, lsl #32
    // 0x93b9cc: cmp             w1, NULL
    // 0x93b9d0: b.ne            #0x93b9dc
    // 0x93b9d4: r0 = Null
    //     0x93b9d4: mov             x0, NULL
    // 0x93b9d8: r0 = ReturnAsyncNotFuture()
    //     0x93b9d8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93b9dc: r0 = Null
    //     0x93b9dc: mov             x0, NULL
    // 0x93b9e0: r0 = ReturnAsyncNotFuture()
    //     0x93b9e0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93b9e4: ldur            x0, [fp, #-0x78]
    // 0x93b9e8: ldur            x1, [fp, #-0x80]
    // 0x93b9ec: r0 = ReThrow()
    //     0x93b9ec: bl              #0x16f53f4  ; ReThrowStub
    // 0x93b9f0: brk             #0
    // 0x93b9f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93b9f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93b9f8: b               #0x93b784
  }
  _ openUrlInApp(/* No info */) {
    // ** addr: 0x93b9fc, size: 0x758
    // 0x93b9fc: EnterFrame
    //     0x93b9fc: stp             fp, lr, [SP, #-0x10]!
    //     0x93ba00: mov             fp, SP
    // 0x93ba04: AllocStack(0x40)
    //     0x93ba04: sub             SP, SP, #0x40
    // 0x93ba08: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0x93ba08: stur            x1, [fp, #-8]
    // 0x93ba0c: CheckStackOverflow
    //     0x93ba0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93ba10: cmp             SP, x16
    //     0x93ba14: b.ls            #0x93c13c
    // 0x93ba18: r1 = 1
    //     0x93ba18: movz            x1, #0x1
    // 0x93ba1c: r0 = AllocateContext()
    //     0x93ba1c: bl              #0x16f6108  ; AllocateContextStub
    // 0x93ba20: mov             x3, x0
    // 0x93ba24: ldur            x2, [fp, #-8]
    // 0x93ba28: stur            x3, [fp, #-0x10]
    // 0x93ba2c: StoreField: r3->field_f = r2
    //     0x93ba2c: stur            w2, [x3, #0xf]
    // 0x93ba30: LoadField: r1 = r2->field_23
    //     0x93ba30: ldur            w1, [x2, #0x23]
    // 0x93ba34: DecompressPointer r1
    //     0x93ba34: add             x1, x1, HEAP, lsl #32
    // 0x93ba38: cmp             w1, NULL
    // 0x93ba3c: b.ne            #0x93ba48
    // 0x93ba40: r0 = Null
    //     0x93ba40: mov             x0, NULL
    // 0x93ba44: b               #0x93ba88
    // 0x93ba48: r0 = LoadClassIdInstr(r1)
    //     0x93ba48: ldur            x0, [x1, #-1]
    //     0x93ba4c: ubfx            x0, x0, #0xc, #0x14
    // 0x93ba50: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93ba50: sub             lr, x0, #1, lsl #12
    //     0x93ba54: ldr             lr, [x21, lr, lsl #3]
    //     0x93ba58: blr             lr
    // 0x93ba5c: r1 = LoadClassIdInstr(r0)
    //     0x93ba5c: ldur            x1, [x0, #-1]
    //     0x93ba60: ubfx            x1, x1, #0xc, #0x14
    // 0x93ba64: mov             x16, x0
    // 0x93ba68: mov             x0, x1
    // 0x93ba6c: mov             x1, x16
    // 0x93ba70: r2 = "/catalogue/"
    //     0x93ba70: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da58] "/catalogue/"
    //     0x93ba74: ldr             x2, [x2, #0xa58]
    // 0x93ba78: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93ba78: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93ba7c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x93ba7c: sub             lr, x0, #0xffe
    //     0x93ba80: ldr             lr, [x21, lr, lsl #3]
    //     0x93ba84: blr             lr
    // 0x93ba88: cmp             w0, NULL
    // 0x93ba8c: b.ne            #0x93ba98
    // 0x93ba90: ldur            x2, [fp, #-8]
    // 0x93ba94: b               #0x93bcb0
    // 0x93ba98: tbnz            w0, #4, #0x93bcac
    // 0x93ba9c: ldur            x2, [fp, #-8]
    // 0x93baa0: LoadField: r1 = r2->field_23
    //     0x93baa0: ldur            w1, [x2, #0x23]
    // 0x93baa4: DecompressPointer r1
    //     0x93baa4: add             x1, x1, HEAP, lsl #32
    // 0x93baa8: cmp             w1, NULL
    // 0x93baac: b.ne            #0x93bab8
    // 0x93bab0: r0 = Null
    //     0x93bab0: mov             x0, NULL
    // 0x93bab4: b               #0x93bacc
    // 0x93bab8: r0 = LoadClassIdInstr(r1)
    //     0x93bab8: ldur            x0, [x1, #-1]
    //     0x93babc: ubfx            x0, x0, #0xc, #0x14
    // 0x93bac0: r0 = GDT[cid_x0 + -0xea5]()
    //     0x93bac0: sub             lr, x0, #0xea5
    //     0x93bac4: ldr             lr, [x21, lr, lsl #3]
    //     0x93bac8: blr             lr
    // 0x93bacc: stur            x0, [fp, #-0x18]
    // 0x93bad0: cmp             w0, NULL
    // 0x93bad4: b.ne            #0x93bae0
    // 0x93bad8: r2 = Null
    //     0x93bad8: mov             x2, NULL
    // 0x93badc: b               #0x93bb10
    // 0x93bae0: mov             x1, x0
    // 0x93bae4: r2 = "catalogue"
    //     0x93bae4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da60] "catalogue"
    //     0x93bae8: ldr             x2, [x2, #0xa60]
    // 0x93baec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93baec: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93baf0: r0 = indexOf()
    //     0x93baf0: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x93baf4: mov             x2, x0
    // 0x93baf8: r0 = BoxInt64Instr(r2)
    //     0x93baf8: sbfiz           x0, x2, #1, #0x1f
    //     0x93bafc: cmp             x2, x0, asr #1
    //     0x93bb00: b.eq            #0x93bb0c
    //     0x93bb04: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93bb08: stur            x2, [x0, #7]
    // 0x93bb0c: mov             x2, x0
    // 0x93bb10: cmp             w2, NULL
    // 0x93bb14: b.eq            #0x93bbec
    // 0x93bb18: ldur            x3, [fp, #-0x18]
    // 0x93bb1c: cmp             w3, NULL
    // 0x93bb20: b.ne            #0x93bb2c
    // 0x93bb24: r0 = Null
    //     0x93bb24: mov             x0, NULL
    // 0x93bb28: b               #0x93bb60
    // 0x93bb2c: r0 = LoadInt32Instr(r2)
    //     0x93bb2c: sbfx            x0, x2, #1, #0x1f
    //     0x93bb30: tbz             w2, #0, #0x93bb38
    //     0x93bb34: ldur            x0, [x2, #7]
    // 0x93bb38: add             x4, x0, #2
    // 0x93bb3c: LoadField: r0 = r3->field_b
    //     0x93bb3c: ldur            w0, [x3, #0xb]
    // 0x93bb40: r1 = LoadInt32Instr(r0)
    //     0x93bb40: sbfx            x1, x0, #1, #0x1f
    // 0x93bb44: mov             x0, x1
    // 0x93bb48: mov             x1, x4
    // 0x93bb4c: cmp             x1, x0
    // 0x93bb50: b.hs            #0x93c144
    // 0x93bb54: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0x93bb54: add             x16, x3, x4, lsl #2
    //     0x93bb58: ldur            w0, [x16, #0xf]
    // 0x93bb5c: DecompressPointer r0
    //     0x93bb5c: add             x0, x0, HEAP, lsl #32
    // 0x93bb60: ldur            x4, [fp, #-8]
    // 0x93bb64: StoreField: r4->field_37 = r0
    //     0x93bb64: stur            w0, [x4, #0x37]
    //     0x93bb68: tbz             w0, #0, #0x93bb84
    //     0x93bb6c: ldurb           w16, [x4, #-1]
    //     0x93bb70: ldurb           w17, [x0, #-1]
    //     0x93bb74: and             x16, x17, x16, lsr #2
    //     0x93bb78: tst             x16, HEAP, lsr #32
    //     0x93bb7c: b.eq            #0x93bb84
    //     0x93bb80: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x93bb84: cmp             w3, NULL
    // 0x93bb88: b.ne            #0x93bb94
    // 0x93bb8c: r0 = Null
    //     0x93bb8c: mov             x0, NULL
    // 0x93bb90: b               #0x93bbc8
    // 0x93bb94: r0 = LoadInt32Instr(r2)
    //     0x93bb94: sbfx            x0, x2, #1, #0x1f
    //     0x93bb98: tbz             w2, #0, #0x93bba0
    //     0x93bb9c: ldur            x0, [x2, #7]
    // 0x93bba0: add             x2, x0, #1
    // 0x93bba4: LoadField: r0 = r3->field_b
    //     0x93bba4: ldur            w0, [x3, #0xb]
    // 0x93bba8: r1 = LoadInt32Instr(r0)
    //     0x93bba8: sbfx            x1, x0, #1, #0x1f
    // 0x93bbac: mov             x0, x1
    // 0x93bbb0: mov             x1, x2
    // 0x93bbb4: cmp             x1, x0
    // 0x93bbb8: b.hs            #0x93c148
    // 0x93bbbc: ArrayLoad: r0 = r3[r2]  ; Unknown_4
    //     0x93bbbc: add             x16, x3, x2, lsl #2
    //     0x93bbc0: ldur            w0, [x16, #0xf]
    // 0x93bbc4: DecompressPointer r0
    //     0x93bbc4: add             x0, x0, HEAP, lsl #32
    // 0x93bbc8: StoreField: r4->field_33 = r0
    //     0x93bbc8: stur            w0, [x4, #0x33]
    //     0x93bbcc: tbz             w0, #0, #0x93bbe8
    //     0x93bbd0: ldurb           w16, [x4, #-1]
    //     0x93bbd4: ldurb           w17, [x0, #-1]
    //     0x93bbd8: and             x16, x17, x16, lsr #2
    //     0x93bbdc: tst             x16, HEAP, lsr #32
    //     0x93bbe0: b.eq            #0x93bbe8
    //     0x93bbe4: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x93bbe8: b               #0x93bbf0
    // 0x93bbec: ldur            x4, [fp, #-8]
    // 0x93bbf0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x93bbf0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93bbf4: ldr             x0, [x0, #0x1c80]
    //     0x93bbf8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93bbfc: cmp             w0, w16
    //     0x93bc00: b.ne            #0x93bc0c
    //     0x93bc04: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x93bc08: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93bc0c: r1 = Null
    //     0x93bc0c: mov             x1, NULL
    // 0x93bc10: r2 = 16
    //     0x93bc10: movz            x2, #0x10
    // 0x93bc14: r0 = AllocateArray()
    //     0x93bc14: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93bc18: r16 = "short_id"
    //     0x93bc18: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x93bc1c: ldr             x16, [x16, #0x488]
    // 0x93bc20: StoreField: r0->field_f = r16
    //     0x93bc20: stur            w16, [x0, #0xf]
    // 0x93bc24: ldur            x2, [fp, #-8]
    // 0x93bc28: LoadField: r1 = r2->field_33
    //     0x93bc28: ldur            w1, [x2, #0x33]
    // 0x93bc2c: DecompressPointer r1
    //     0x93bc2c: add             x1, x1, HEAP, lsl #32
    // 0x93bc30: StoreField: r0->field_13 = r1
    //     0x93bc30: stur            w1, [x0, #0x13]
    // 0x93bc34: r16 = "sku_id"
    //     0x93bc34: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x93bc38: ldr             x16, [x16, #0x498]
    // 0x93bc3c: ArrayStore: r0[0] = r16  ; List_4
    //     0x93bc3c: stur            w16, [x0, #0x17]
    // 0x93bc40: LoadField: r1 = r2->field_37
    //     0x93bc40: ldur            w1, [x2, #0x37]
    // 0x93bc44: DecompressPointer r1
    //     0x93bc44: add             x1, x1, HEAP, lsl #32
    // 0x93bc48: StoreField: r0->field_1b = r1
    //     0x93bc48: stur            w1, [x0, #0x1b]
    // 0x93bc4c: r16 = "previousScreenSource"
    //     0x93bc4c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x93bc50: ldr             x16, [x16, #0x448]
    // 0x93bc54: StoreField: r0->field_1f = r16
    //     0x93bc54: stur            w16, [x0, #0x1f]
    // 0x93bc58: r16 = "Launch page"
    //     0x93bc58: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2da68] "Launch page"
    //     0x93bc5c: ldr             x16, [x16, #0xa68]
    // 0x93bc60: StoreField: r0->field_23 = r16
    //     0x93bc60: stur            w16, [x0, #0x23]
    // 0x93bc64: r16 = "screenSource"
    //     0x93bc64: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x93bc68: ldr             x16, [x16, #0x450]
    // 0x93bc6c: StoreField: r0->field_27 = r16
    //     0x93bc6c: stur            w16, [x0, #0x27]
    // 0x93bc70: r16 = "App Launching"
    //     0x93bc70: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2da70] "App Launching"
    //     0x93bc74: ldr             x16, [x16, #0xa70]
    // 0x93bc78: StoreField: r0->field_2b = r16
    //     0x93bc78: stur            w16, [x0, #0x2b]
    // 0x93bc7c: r16 = <String, String?>
    //     0x93bc7c: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x93bc80: ldr             x16, [x16, #0x3c8]
    // 0x93bc84: stp             x0, x16, [SP]
    // 0x93bc88: r0 = Map._fromLiteral()
    //     0x93bc88: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x93bc8c: r16 = "/product-detail"
    //     0x93bc8c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x93bc90: ldr             x16, [x16, #0x4a8]
    // 0x93bc94: stp             x16, NULL, [SP, #8]
    // 0x93bc98: str             x0, [SP]
    // 0x93bc9c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x93bc9c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x93bca0: ldr             x4, [x4, #0x438]
    // 0x93bca4: r0 = GetNavigation.toNamed()
    //     0x93bca4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x93bca8: b               #0x93c12c
    // 0x93bcac: ldur            x2, [fp, #-8]
    // 0x93bcb0: LoadField: r1 = r2->field_23
    //     0x93bcb0: ldur            w1, [x2, #0x23]
    // 0x93bcb4: DecompressPointer r1
    //     0x93bcb4: add             x1, x1, HEAP, lsl #32
    // 0x93bcb8: cmp             w1, NULL
    // 0x93bcbc: b.ne            #0x93bcc8
    // 0x93bcc0: r0 = Null
    //     0x93bcc0: mov             x0, NULL
    // 0x93bcc4: b               #0x93bd08
    // 0x93bcc8: r0 = LoadClassIdInstr(r1)
    //     0x93bcc8: ldur            x0, [x1, #-1]
    //     0x93bccc: ubfx            x0, x0, #0xc, #0x14
    // 0x93bcd0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93bcd0: sub             lr, x0, #1, lsl #12
    //     0x93bcd4: ldr             lr, [x21, lr, lsl #3]
    //     0x93bcd8: blr             lr
    // 0x93bcdc: r1 = LoadClassIdInstr(r0)
    //     0x93bcdc: ldur            x1, [x0, #-1]
    //     0x93bce0: ubfx            x1, x1, #0xc, #0x14
    // 0x93bce4: mov             x16, x0
    // 0x93bce8: mov             x0, x1
    // 0x93bcec: mov             x1, x16
    // 0x93bcf0: r2 = "/collection/"
    //     0x93bcf0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da78] "/collection/"
    //     0x93bcf4: ldr             x2, [x2, #0xa78]
    // 0x93bcf8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93bcf8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93bcfc: r0 = GDT[cid_x0 + -0xffe]()
    //     0x93bcfc: sub             lr, x0, #0xffe
    //     0x93bd00: ldr             lr, [x21, lr, lsl #3]
    //     0x93bd04: blr             lr
    // 0x93bd08: cmp             w0, NULL
    // 0x93bd0c: b.ne            #0x93bd18
    // 0x93bd10: ldur            x2, [fp, #-8]
    // 0x93bd14: b               #0x93be10
    // 0x93bd18: tbnz            w0, #4, #0x93be0c
    // 0x93bd1c: ldur            x2, [fp, #-8]
    // 0x93bd20: LoadField: r1 = r2->field_23
    //     0x93bd20: ldur            w1, [x2, #0x23]
    // 0x93bd24: DecompressPointer r1
    //     0x93bd24: add             x1, x1, HEAP, lsl #32
    // 0x93bd28: cmp             w1, NULL
    // 0x93bd2c: b.ne            #0x93bd38
    // 0x93bd30: r0 = Null
    //     0x93bd30: mov             x0, NULL
    // 0x93bd34: b               #0x93bd4c
    // 0x93bd38: r0 = LoadClassIdInstr(r1)
    //     0x93bd38: ldur            x0, [x1, #-1]
    //     0x93bd3c: ubfx            x0, x0, #0xc, #0x14
    // 0x93bd40: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93bd40: sub             lr, x0, #1, lsl #12
    //     0x93bd44: ldr             lr, [x21, lr, lsl #3]
    //     0x93bd48: blr             lr
    // 0x93bd4c: stur            x0, [fp, #-0x18]
    // 0x93bd50: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x93bd50: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93bd54: ldr             x0, [x0, #0x1c80]
    //     0x93bd58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93bd5c: cmp             w0, w16
    //     0x93bd60: b.ne            #0x93bd6c
    //     0x93bd64: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x93bd68: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93bd6c: r1 = Null
    //     0x93bd6c: mov             x1, NULL
    // 0x93bd70: r2 = 12
    //     0x93bd70: movz            x2, #0xc
    // 0x93bd74: r0 = AllocateArray()
    //     0x93bd74: bl              #0x16f7198  ; AllocateArrayStub
    // 0x93bd78: stur            x0, [fp, #-0x20]
    // 0x93bd7c: r16 = "link"
    //     0x93bd7c: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x93bd80: StoreField: r0->field_f = r16
    //     0x93bd80: stur            w16, [x0, #0xf]
    // 0x93bd84: ldur            x16, [fp, #-0x18]
    // 0x93bd88: str             x16, [SP]
    // 0x93bd8c: r0 = _interpolateSingle()
    //     0x93bd8c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x93bd90: ldur            x1, [fp, #-0x20]
    // 0x93bd94: ArrayStore: r1[1] = r0  ; List_4
    //     0x93bd94: add             x25, x1, #0x13
    //     0x93bd98: str             w0, [x25]
    //     0x93bd9c: tbz             w0, #0, #0x93bdb8
    //     0x93bda0: ldurb           w16, [x1, #-1]
    //     0x93bda4: ldurb           w17, [x0, #-1]
    //     0x93bda8: and             x16, x17, x16, lsr #2
    //     0x93bdac: tst             x16, HEAP, lsr #32
    //     0x93bdb0: b.eq            #0x93bdb8
    //     0x93bdb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93bdb8: ldur            x0, [fp, #-0x20]
    // 0x93bdbc: r16 = "previousScreenSource"
    //     0x93bdbc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x93bdc0: ldr             x16, [x16, #0x448]
    // 0x93bdc4: ArrayStore: r0[0] = r16  ; List_4
    //     0x93bdc4: stur            w16, [x0, #0x17]
    // 0x93bdc8: StoreField: r0->field_1b = rNULL
    //     0x93bdc8: stur            NULL, [x0, #0x1b]
    // 0x93bdcc: r16 = "screenSource"
    //     0x93bdcc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x93bdd0: ldr             x16, [x16, #0x450]
    // 0x93bdd4: StoreField: r0->field_1f = r16
    //     0x93bdd4: stur            w16, [x0, #0x1f]
    // 0x93bdd8: StoreField: r0->field_23 = rNULL
    //     0x93bdd8: stur            NULL, [x0, #0x23]
    // 0x93bddc: r16 = <String, String?>
    //     0x93bddc: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x93bde0: ldr             x16, [x16, #0x3c8]
    // 0x93bde4: stp             x0, x16, [SP]
    // 0x93bde8: r0 = Map._fromLiteral()
    //     0x93bde8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x93bdec: r16 = "/collection"
    //     0x93bdec: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x93bdf0: ldr             x16, [x16, #0x458]
    // 0x93bdf4: stp             x16, NULL, [SP, #8]
    // 0x93bdf8: str             x0, [SP]
    // 0x93bdfc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x93bdfc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x93be00: ldr             x4, [x4, #0x438]
    // 0x93be04: r0 = GetNavigation.toNamed()
    //     0x93be04: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x93be08: b               #0x93c12c
    // 0x93be0c: ldur            x2, [fp, #-8]
    // 0x93be10: LoadField: r1 = r2->field_23
    //     0x93be10: ldur            w1, [x2, #0x23]
    // 0x93be14: DecompressPointer r1
    //     0x93be14: add             x1, x1, HEAP, lsl #32
    // 0x93be18: cmp             w1, NULL
    // 0x93be1c: b.ne            #0x93be28
    // 0x93be20: r0 = Null
    //     0x93be20: mov             x0, NULL
    // 0x93be24: b               #0x93be68
    // 0x93be28: r0 = LoadClassIdInstr(r1)
    //     0x93be28: ldur            x0, [x1, #-1]
    //     0x93be2c: ubfx            x0, x0, #0xc, #0x14
    // 0x93be30: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93be30: sub             lr, x0, #1, lsl #12
    //     0x93be34: ldr             lr, [x21, lr, lsl #3]
    //     0x93be38: blr             lr
    // 0x93be3c: r1 = LoadClassIdInstr(r0)
    //     0x93be3c: ldur            x1, [x0, #-1]
    //     0x93be40: ubfx            x1, x1, #0xc, #0x14
    // 0x93be44: mov             x16, x0
    // 0x93be48: mov             x0, x1
    // 0x93be4c: mov             x1, x16
    // 0x93be50: r2 = "/bag"
    //     0x93be50: add             x2, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x93be54: ldr             x2, [x2, #0x468]
    // 0x93be58: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93be58: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93be5c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x93be5c: sub             lr, x0, #0xffe
    //     0x93be60: ldr             lr, [x21, lr, lsl #3]
    //     0x93be64: blr             lr
    // 0x93be68: cmp             w0, NULL
    // 0x93be6c: b.ne            #0x93be78
    // 0x93be70: ldur            x2, [fp, #-8]
    // 0x93be74: b               #0x93c0a0
    // 0x93be78: tbnz            w0, #4, #0x93c09c
    // 0x93be7c: ldur            x2, [fp, #-8]
    // 0x93be80: LoadField: r1 = r2->field_23
    //     0x93be80: ldur            w1, [x2, #0x23]
    // 0x93be84: DecompressPointer r1
    //     0x93be84: add             x1, x1, HEAP, lsl #32
    // 0x93be88: cmp             w1, NULL
    // 0x93be8c: b.ne            #0x93be98
    // 0x93be90: r0 = Null
    //     0x93be90: mov             x0, NULL
    // 0x93be94: b               #0x93bed8
    // 0x93be98: r0 = LoadClassIdInstr(r1)
    //     0x93be98: ldur            x0, [x1, #-1]
    //     0x93be9c: ubfx            x0, x0, #0xc, #0x14
    // 0x93bea0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93bea0: sub             lr, x0, #1, lsl #12
    //     0x93bea4: ldr             lr, [x21, lr, lsl #3]
    //     0x93bea8: blr             lr
    // 0x93beac: mov             x1, x0
    // 0x93beb0: r2 = "/"
    //     0x93beb0: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x93beb4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93beb4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93beb8: r0 = lastIndexOf()
    //     0x93beb8: bl              #0x6394bc  ; [dart:core] _StringBase::lastIndexOf
    // 0x93bebc: mov             x2, x0
    // 0x93bec0: r0 = BoxInt64Instr(r2)
    //     0x93bec0: sbfiz           x0, x2, #1, #0x1f
    //     0x93bec4: cmp             x2, x0, asr #1
    //     0x93bec8: b.eq            #0x93bed4
    //     0x93becc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93bed0: stur            x2, [x0, #7]
    // 0x93bed4: ldur            x2, [fp, #-8]
    // 0x93bed8: StoreField: r2->field_2b = r0
    //     0x93bed8: stur            w0, [x2, #0x2b]
    //     0x93bedc: tbz             w0, #0, #0x93bef8
    //     0x93bee0: ldurb           w16, [x2, #-1]
    //     0x93bee4: ldurb           w17, [x0, #-1]
    //     0x93bee8: and             x16, x17, x16, lsr #2
    //     0x93beec: tst             x16, HEAP, lsr #32
    //     0x93bef0: b.eq            #0x93bef8
    //     0x93bef4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93bef8: LoadField: r1 = r2->field_23
    //     0x93bef8: ldur            w1, [x2, #0x23]
    // 0x93befc: DecompressPointer r1
    //     0x93befc: add             x1, x1, HEAP, lsl #32
    // 0x93bf00: cmp             w1, NULL
    // 0x93bf04: b.ne            #0x93bf10
    // 0x93bf08: r0 = Null
    //     0x93bf08: mov             x0, NULL
    // 0x93bf0c: b               #0x93bf90
    // 0x93bf10: r0 = LoadClassIdInstr(r1)
    //     0x93bf10: ldur            x0, [x1, #-1]
    //     0x93bf14: ubfx            x0, x0, #0xc, #0x14
    // 0x93bf18: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93bf18: sub             lr, x0, #1, lsl #12
    //     0x93bf1c: ldr             lr, [x21, lr, lsl #3]
    //     0x93bf20: blr             lr
    // 0x93bf24: mov             x2, x0
    // 0x93bf28: ldur            x3, [fp, #-8]
    // 0x93bf2c: LoadField: r0 = r3->field_2b
    //     0x93bf2c: ldur            w0, [x3, #0x2b]
    // 0x93bf30: DecompressPointer r0
    //     0x93bf30: add             x0, x0, HEAP, lsl #32
    // 0x93bf34: cmp             w0, NULL
    // 0x93bf38: b.eq            #0x93c14c
    // 0x93bf3c: r1 = LoadInt32Instr(r0)
    //     0x93bf3c: sbfx            x1, x0, #1, #0x1f
    //     0x93bf40: tbz             w0, #0, #0x93bf48
    //     0x93bf44: ldur            x1, [x0, #7]
    // 0x93bf48: sub             x4, x1, #1
    // 0x93bf4c: r0 = BoxInt64Instr(r4)
    //     0x93bf4c: sbfiz           x0, x4, #1, #0x1f
    //     0x93bf50: cmp             x4, x0, asr #1
    //     0x93bf54: b.eq            #0x93bf60
    //     0x93bf58: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93bf5c: stur            x4, [x0, #7]
    // 0x93bf60: str             x0, [SP]
    // 0x93bf64: mov             x1, x2
    // 0x93bf68: r2 = "/"
    //     0x93bf68: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x93bf6c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x93bf6c: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x93bf70: r0 = lastIndexOf()
    //     0x93bf70: bl              #0x6394bc  ; [dart:core] _StringBase::lastIndexOf
    // 0x93bf74: mov             x2, x0
    // 0x93bf78: r0 = BoxInt64Instr(r2)
    //     0x93bf78: sbfiz           x0, x2, #1, #0x1f
    //     0x93bf7c: cmp             x2, x0, asr #1
    //     0x93bf80: b.eq            #0x93bf8c
    //     0x93bf84: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93bf88: stur            x2, [x0, #7]
    // 0x93bf8c: ldur            x2, [fp, #-8]
    // 0x93bf90: StoreField: r2->field_2f = r0
    //     0x93bf90: stur            w0, [x2, #0x2f]
    //     0x93bf94: tbz             w0, #0, #0x93bfb0
    //     0x93bf98: ldurb           w16, [x2, #-1]
    //     0x93bf9c: ldurb           w17, [x0, #-1]
    //     0x93bfa0: and             x16, x17, x16, lsr #2
    //     0x93bfa4: tst             x16, HEAP, lsr #32
    //     0x93bfa8: b.eq            #0x93bfb0
    //     0x93bfac: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x93bfb0: LoadField: r1 = r2->field_23
    //     0x93bfb0: ldur            w1, [x2, #0x23]
    // 0x93bfb4: DecompressPointer r1
    //     0x93bfb4: add             x1, x1, HEAP, lsl #32
    // 0x93bfb8: cmp             w1, NULL
    // 0x93bfbc: b.eq            #0x93c068
    // 0x93bfc0: r0 = LoadClassIdInstr(r1)
    //     0x93bfc0: ldur            x0, [x1, #-1]
    //     0x93bfc4: ubfx            x0, x0, #0xc, #0x14
    // 0x93bfc8: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93bfc8: sub             lr, x0, #1, lsl #12
    //     0x93bfcc: ldr             lr, [x21, lr, lsl #3]
    //     0x93bfd0: blr             lr
    // 0x93bfd4: mov             x3, x0
    // 0x93bfd8: ldur            x2, [fp, #-8]
    // 0x93bfdc: stur            x3, [fp, #-0x18]
    // 0x93bfe0: LoadField: r0 = r2->field_2f
    //     0x93bfe0: ldur            w0, [x2, #0x2f]
    // 0x93bfe4: DecompressPointer r0
    //     0x93bfe4: add             x0, x0, HEAP, lsl #32
    // 0x93bfe8: cmp             w0, NULL
    // 0x93bfec: b.eq            #0x93c150
    // 0x93bff0: r1 = LoadInt32Instr(r0)
    //     0x93bff0: sbfx            x1, x0, #1, #0x1f
    //     0x93bff4: tbz             w0, #0, #0x93bffc
    //     0x93bff8: ldur            x1, [x0, #7]
    // 0x93bffc: add             x4, x1, #1
    // 0x93c000: stur            x4, [fp, #-0x28]
    // 0x93c004: LoadField: r1 = r2->field_23
    //     0x93c004: ldur            w1, [x2, #0x23]
    // 0x93c008: DecompressPointer r1
    //     0x93c008: add             x1, x1, HEAP, lsl #32
    // 0x93c00c: cmp             w1, NULL
    // 0x93c010: b.ne            #0x93c020
    // 0x93c014: mov             x2, x4
    // 0x93c018: r3 = Null
    //     0x93c018: mov             x3, NULL
    // 0x93c01c: b               #0x93c040
    // 0x93c020: r0 = LoadClassIdInstr(r1)
    //     0x93c020: ldur            x0, [x1, #-1]
    //     0x93c024: ubfx            x0, x0, #0xc, #0x14
    // 0x93c028: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93c028: sub             lr, x0, #1, lsl #12
    //     0x93c02c: ldr             lr, [x21, lr, lsl #3]
    //     0x93c030: blr             lr
    // 0x93c034: LoadField: r1 = r0->field_7
    //     0x93c034: ldur            w1, [x0, #7]
    // 0x93c038: mov             x3, x1
    // 0x93c03c: ldur            x2, [fp, #-0x28]
    // 0x93c040: r0 = BoxInt64Instr(r2)
    //     0x93c040: sbfiz           x0, x2, #1, #0x1f
    //     0x93c044: cmp             x2, x0, asr #1
    //     0x93c048: b.eq            #0x93c054
    //     0x93c04c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x93c050: stur            x2, [x0, #7]
    // 0x93c054: str             x3, [SP]
    // 0x93c058: ldur            x1, [fp, #-0x18]
    // 0x93c05c: mov             x2, x0
    // 0x93c060: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x93c060: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x93c064: r0 = substring()
    //     0x93c064: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0x93c068: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x93c068: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93c06c: ldr             x0, [x0, #0x1c80]
    //     0x93c070: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x93c074: cmp             w0, w16
    //     0x93c078: b.ne            #0x93c084
    //     0x93c07c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x93c080: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x93c084: r16 = "/bag"
    //     0x93c084: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x93c088: ldr             x16, [x16, #0x468]
    // 0x93c08c: stp             x16, NULL, [SP]
    // 0x93c090: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x93c090: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x93c094: r0 = GetNavigation.toNamed()
    //     0x93c094: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x93c098: b               #0x93c12c
    // 0x93c09c: ldur            x2, [fp, #-8]
    // 0x93c0a0: LoadField: r1 = r2->field_23
    //     0x93c0a0: ldur            w1, [x2, #0x23]
    // 0x93c0a4: DecompressPointer r1
    //     0x93c0a4: add             x1, x1, HEAP, lsl #32
    // 0x93c0a8: cmp             w1, NULL
    // 0x93c0ac: b.ne            #0x93c0b8
    // 0x93c0b0: r0 = Null
    //     0x93c0b0: mov             x0, NULL
    // 0x93c0b4: b               #0x93c0f8
    // 0x93c0b8: r0 = LoadClassIdInstr(r1)
    //     0x93c0b8: ldur            x0, [x1, #-1]
    //     0x93c0bc: ubfx            x0, x0, #0xc, #0x14
    // 0x93c0c0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x93c0c0: sub             lr, x0, #1, lsl #12
    //     0x93c0c4: ldr             lr, [x21, lr, lsl #3]
    //     0x93c0c8: blr             lr
    // 0x93c0cc: r1 = LoadClassIdInstr(r0)
    //     0x93c0cc: ldur            x1, [x0, #-1]
    //     0x93c0d0: ubfx            x1, x1, #0xc, #0x14
    // 0x93c0d4: mov             x16, x0
    // 0x93c0d8: mov             x0, x1
    // 0x93c0dc: mov             x1, x16
    // 0x93c0e0: r2 = "/orders"
    //     0x93c0e0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da80] "/orders"
    //     0x93c0e4: ldr             x2, [x2, #0xa80]
    // 0x93c0e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x93c0e8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93c0ec: r0 = GDT[cid_x0 + -0xffe]()
    //     0x93c0ec: sub             lr, x0, #0xffe
    //     0x93c0f0: ldr             lr, [x21, lr, lsl #3]
    //     0x93c0f4: blr             lr
    // 0x93c0f8: cmp             w0, NULL
    // 0x93c0fc: b.eq            #0x93c12c
    // 0x93c100: tbnz            w0, #4, #0x93c12c
    // 0x93c104: ldur            x2, [fp, #-0x10]
    // 0x93c108: r1 = Function '<anonymous closure>':.
    //     0x93c108: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2de10] AnonymousClosure: (0x915664), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp (0x915688)
    //     0x93c10c: ldr             x1, [x1, #0xe10]
    // 0x93c110: r0 = AllocateClosure()
    //     0x93c110: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93c114: ldur            x1, [fp, #-8]
    // 0x93c118: mov             x2, x0
    // 0x93c11c: r0 = setState()
    //     0x93c11c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93c120: ldur            x1, [fp, #-8]
    // 0x93c124: r2 = 2
    //     0x93c124: movz            x2, #0x2
    // 0x93c128: r0 = _onItemTapped()
    //     0x93c128: bl              #0x93ad48  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x93c12c: r0 = Null
    //     0x93c12c: mov             x0, NULL
    // 0x93c130: LeaveFrame
    //     0x93c130: mov             SP, fp
    //     0x93c134: ldp             fp, lr, [SP], #0x10
    // 0x93c138: ret
    //     0x93c138: ret             
    // 0x93c13c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93c13c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93c140: b               #0x93ba18
    // 0x93c144: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93c144: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93c148: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x93c148: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x93c14c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93c14c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93c150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93c150: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Set<Set<void>>> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x93c154, size: 0x320
    // 0x93c154: EnterFrame
    //     0x93c154: stp             fp, lr, [SP, #-0x10]!
    //     0x93c158: mov             fp, SP
    // 0x93c15c: AllocStack(0x48)
    //     0x93c15c: sub             SP, SP, #0x48
    // 0x93c160: SetupParameters(_MainPageState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x93c160: stur            NULL, [fp, #-8]
    //     0x93c164: movz            x0, #0
    //     0x93c168: add             x1, fp, w0, sxtw #2
    //     0x93c16c: ldr             x1, [x1, #0x18]
    //     0x93c170: add             x2, fp, w0, sxtw #2
    //     0x93c174: ldr             x2, [x2, #0x10]
    //     0x93c178: stur            x2, [fp, #-0x18]
    //     0x93c17c: ldur            w3, [x1, #0x17]
    //     0x93c180: add             x3, x3, HEAP, lsl #32
    //     0x93c184: stur            x3, [fp, #-0x10]
    // 0x93c188: CheckStackOverflow
    //     0x93c188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93c18c: cmp             SP, x16
    //     0x93c190: b.ls            #0x93c46c
    // 0x93c194: InitAsync() -> Future<Set<Set<void?>>>
    //     0x93c194: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d720] TypeArguments: <Set<Set<void?>>>
    //     0x93c198: ldr             x0, [x0, #0x720]
    //     0x93c19c: bl              #0x6326e0  ; InitAsyncStub
    // 0x93c1a0: r1 = <Set<void?>>
    //     0x93c1a0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d730] TypeArguments: <Set<void?>>
    //     0x93c1a4: ldr             x1, [x1, #0x730]
    // 0x93c1a8: r0 = _Set()
    //     0x93c1a8: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93c1ac: mov             x2, x0
    // 0x93c1b0: r1 = _Uint32List
    //     0x93c1b0: ldr             x1, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c1b4: stur            x2, [fp, #-0x20]
    // 0x93c1b8: StoreField: r2->field_1b = r1
    //     0x93c1b8: stur            w1, [x2, #0x1b]
    // 0x93c1bc: StoreField: r2->field_b = rZR
    //     0x93c1bc: stur            wzr, [x2, #0xb]
    // 0x93c1c0: r3 = const []
    //     0x93c1c0: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c1c4: StoreField: r2->field_f = r3
    //     0x93c1c4: stur            w3, [x2, #0xf]
    // 0x93c1c8: StoreField: r2->field_13 = rZR
    //     0x93c1c8: stur            wzr, [x2, #0x13]
    // 0x93c1cc: ArrayStore: r2[0] = rZR  ; List_4
    //     0x93c1cc: stur            wzr, [x2, #0x17]
    // 0x93c1d0: ldur            x0, [fp, #-0x18]
    // 0x93c1d4: r4 = 60
    //     0x93c1d4: movz            x4, #0x3c
    // 0x93c1d8: branchIfSmi(r0, 0x93c1e4)
    //     0x93c1d8: tbz             w0, #0, #0x93c1e4
    // 0x93c1dc: r4 = LoadClassIdInstr(r0)
    //     0x93c1dc: ldur            x4, [x0, #-1]
    //     0x93c1e0: ubfx            x4, x4, #0xc, #0x14
    // 0x93c1e4: r16 = ""
    //     0x93c1e4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93c1e8: stp             x16, x0, [SP]
    // 0x93c1ec: mov             x0, x4
    // 0x93c1f0: mov             lr, x0
    // 0x93c1f4: ldr             lr, [x21, lr, lsl #3]
    // 0x93c1f8: blr             lr
    // 0x93c1fc: tbz             w0, #4, #0x93c25c
    // 0x93c200: ldur            x0, [fp, #-0x10]
    // 0x93c204: r1 = <void?>
    //     0x93c204: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x93c208: r0 = _Set()
    //     0x93c208: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93c20c: mov             x2, x0
    // 0x93c210: r0 = _Uint32List
    //     0x93c210: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c214: stur            x2, [fp, #-0x18]
    // 0x93c218: StoreField: r2->field_1b = r0
    //     0x93c218: stur            w0, [x2, #0x1b]
    // 0x93c21c: StoreField: r2->field_b = rZR
    //     0x93c21c: stur            wzr, [x2, #0xb]
    // 0x93c220: r3 = const []
    //     0x93c220: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c224: StoreField: r2->field_f = r3
    //     0x93c224: stur            w3, [x2, #0xf]
    // 0x93c228: StoreField: r2->field_13 = rZR
    //     0x93c228: stur            wzr, [x2, #0x13]
    // 0x93c22c: ArrayStore: r2[0] = rZR  ; List_4
    //     0x93c22c: stur            wzr, [x2, #0x17]
    // 0x93c230: ldur            x4, [fp, #-0x10]
    // 0x93c234: LoadField: r1 = r4->field_f
    //     0x93c234: ldur            w1, [x4, #0xf]
    // 0x93c238: DecompressPointer r1
    //     0x93c238: add             x1, x1, HEAP, lsl #32
    // 0x93c23c: r0 = openUrlInApp()
    //     0x93c23c: bl              #0x93b9fc  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x93c240: ldur            x1, [fp, #-0x18]
    // 0x93c244: r2 = Null
    //     0x93c244: mov             x2, NULL
    // 0x93c248: r0 = add()
    //     0x93c248: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c24c: ldur            x1, [fp, #-0x20]
    // 0x93c250: ldur            x2, [fp, #-0x18]
    // 0x93c254: r0 = add()
    //     0x93c254: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c258: b               #0x93c464
    // 0x93c25c: ldur            x4, [fp, #-0x10]
    // 0x93c260: r0 = _Uint32List
    //     0x93c260: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c264: r3 = const []
    //     0x93c264: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c268: r1 = <Set<void?>>
    //     0x93c268: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d730] TypeArguments: <Set<void?>>
    //     0x93c26c: ldr             x1, [x1, #0x730]
    // 0x93c270: r0 = _Set()
    //     0x93c270: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93c274: mov             x4, x0
    // 0x93c278: r3 = _Uint32List
    //     0x93c278: ldr             x3, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c27c: stur            x4, [fp, #-0x28]
    // 0x93c280: StoreField: r4->field_1b = r3
    //     0x93c280: stur            w3, [x4, #0x1b]
    // 0x93c284: StoreField: r4->field_b = rZR
    //     0x93c284: stur            wzr, [x4, #0xb]
    // 0x93c288: r5 = const []
    //     0x93c288: ldr             x5, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c28c: StoreField: r4->field_f = r5
    //     0x93c28c: stur            w5, [x4, #0xf]
    // 0x93c290: StoreField: r4->field_13 = rZR
    //     0x93c290: stur            wzr, [x4, #0x13]
    // 0x93c294: ArrayStore: r4[0] = rZR  ; List_4
    //     0x93c294: stur            wzr, [x4, #0x17]
    // 0x93c298: ldur            x6, [fp, #-0x10]
    // 0x93c29c: LoadField: r7 = r6->field_13
    //     0x93c29c: ldur            w7, [x6, #0x13]
    // 0x93c2a0: DecompressPointer r7
    //     0x93c2a0: add             x7, x7, HEAP, lsl #32
    // 0x93c2a4: stur            x7, [fp, #-0x18]
    // 0x93c2a8: cmp             w7, NULL
    // 0x93c2ac: b.ne            #0x93c2b8
    // 0x93c2b0: r0 = Null
    //     0x93c2b0: mov             x0, NULL
    // 0x93c2b4: b               #0x93c2fc
    // 0x93c2b8: r0 = LoadClassIdInstr(r7)
    //     0x93c2b8: ldur            x0, [x7, #-1]
    //     0x93c2bc: ubfx            x0, x0, #0xc, #0x14
    // 0x93c2c0: mov             x1, x7
    // 0x93c2c4: r2 = "auto_login_otp"
    //     0x93c2c4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d838] "auto_login_otp"
    //     0x93c2c8: ldr             x2, [x2, #0x838]
    // 0x93c2cc: r0 = GDT[cid_x0 + -0xfe]()
    //     0x93c2cc: sub             lr, x0, #0xfe
    //     0x93c2d0: ldr             lr, [x21, lr, lsl #3]
    //     0x93c2d4: blr             lr
    // 0x93c2d8: cmp             w0, NULL
    // 0x93c2dc: b.ne            #0x93c2e8
    // 0x93c2e0: r0 = Null
    //     0x93c2e0: mov             x0, NULL
    // 0x93c2e4: b               #0x93c2fc
    // 0x93c2e8: LoadField: r1 = r0->field_7
    //     0x93c2e8: ldur            w1, [x0, #7]
    // 0x93c2ec: cbnz            w1, #0x93c2f8
    // 0x93c2f0: r0 = false
    //     0x93c2f0: add             x0, NULL, #0x30  ; false
    // 0x93c2f4: b               #0x93c2fc
    // 0x93c2f8: r0 = true
    //     0x93c2f8: add             x0, NULL, #0x20  ; true
    // 0x93c2fc: cmp             w0, NULL
    // 0x93c300: b.ne            #0x93c314
    // 0x93c304: ldur            x3, [fp, #-0x10]
    // 0x93c308: r0 = _Uint32List
    //     0x93c308: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c30c: r2 = const []
    //     0x93c30c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c310: b               #0x93c404
    // 0x93c314: tbnz            w0, #4, #0x93c3f8
    // 0x93c318: ldur            x0, [fp, #-0x10]
    // 0x93c31c: ldur            x2, [fp, #-0x18]
    // 0x93c320: r1 = <void?>
    //     0x93c320: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x93c324: r0 = _Set()
    //     0x93c324: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93c328: mov             x3, x0
    // 0x93c32c: r0 = _Uint32List
    //     0x93c32c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c330: stur            x3, [fp, #-0x38]
    // 0x93c334: StoreField: r3->field_1b = r0
    //     0x93c334: stur            w0, [x3, #0x1b]
    // 0x93c338: StoreField: r3->field_b = rZR
    //     0x93c338: stur            wzr, [x3, #0xb]
    // 0x93c33c: r2 = const []
    //     0x93c33c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c340: StoreField: r3->field_f = r2
    //     0x93c340: stur            w2, [x3, #0xf]
    // 0x93c344: StoreField: r3->field_13 = rZR
    //     0x93c344: stur            wzr, [x3, #0x13]
    // 0x93c348: ArrayStore: r3[0] = rZR  ; List_4
    //     0x93c348: stur            wzr, [x3, #0x17]
    // 0x93c34c: ldur            x4, [fp, #-0x10]
    // 0x93c350: LoadField: r0 = r4->field_f
    //     0x93c350: ldur            w0, [x4, #0xf]
    // 0x93c354: DecompressPointer r0
    //     0x93c354: add             x0, x0, HEAP, lsl #32
    // 0x93c358: LoadField: r5 = r0->field_3b
    //     0x93c358: ldur            w5, [x0, #0x3b]
    // 0x93c35c: DecompressPointer r5
    //     0x93c35c: add             x5, x5, HEAP, lsl #32
    // 0x93c360: ldur            x1, [fp, #-0x18]
    // 0x93c364: stur            x5, [fp, #-0x30]
    // 0x93c368: cmp             w1, NULL
    // 0x93c36c: b.ne            #0x93c378
    // 0x93c370: r0 = Null
    //     0x93c370: mov             x0, NULL
    // 0x93c374: b               #0x93c394
    // 0x93c378: r0 = LoadClassIdInstr(r1)
    //     0x93c378: ldur            x0, [x1, #-1]
    //     0x93c37c: ubfx            x0, x0, #0xc, #0x14
    // 0x93c380: r2 = "auto_login_otp"
    //     0x93c380: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d838] "auto_login_otp"
    //     0x93c384: ldr             x2, [x2, #0x838]
    // 0x93c388: r0 = GDT[cid_x0 + -0xfe]()
    //     0x93c388: sub             lr, x0, #0xfe
    //     0x93c38c: ldr             lr, [x21, lr, lsl #3]
    //     0x93c390: blr             lr
    // 0x93c394: cmp             w0, NULL
    // 0x93c398: b.ne            #0x93c3a4
    // 0x93c39c: r2 = ""
    //     0x93c39c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x93c3a0: b               #0x93c3a8
    // 0x93c3a4: mov             x2, x0
    // 0x93c3a8: ldur            x0, [fp, #-0x10]
    // 0x93c3ac: ldur            x1, [fp, #-0x30]
    // 0x93c3b0: r0 = verifyAutoLoginOtp()
    //     0x93c3b0: bl              #0x916fa0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::verifyAutoLoginOtp
    // 0x93c3b4: mov             x1, x0
    // 0x93c3b8: stur            x1, [fp, #-0x18]
    // 0x93c3bc: r0 = Await()
    //     0x93c3bc: bl              #0x63248c  ; AwaitStub
    // 0x93c3c0: ldur            x1, [fp, #-0x38]
    // 0x93c3c4: mov             x2, x0
    // 0x93c3c8: r0 = add()
    //     0x93c3c8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c3cc: ldur            x3, [fp, #-0x10]
    // 0x93c3d0: LoadField: r1 = r3->field_f
    //     0x93c3d0: ldur            w1, [x3, #0xf]
    // 0x93c3d4: DecompressPointer r1
    //     0x93c3d4: add             x1, x1, HEAP, lsl #32
    // 0x93c3d8: r0 = openUrlInApp()
    //     0x93c3d8: bl              #0x93b9fc  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x93c3dc: ldur            x1, [fp, #-0x38]
    // 0x93c3e0: r2 = Null
    //     0x93c3e0: mov             x2, NULL
    // 0x93c3e4: r0 = add()
    //     0x93c3e4: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c3e8: ldur            x1, [fp, #-0x28]
    // 0x93c3ec: ldur            x2, [fp, #-0x38]
    // 0x93c3f0: r0 = add()
    //     0x93c3f0: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c3f4: b               #0x93c458
    // 0x93c3f8: ldur            x3, [fp, #-0x10]
    // 0x93c3fc: r0 = _Uint32List
    //     0x93c3fc: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c400: r2 = const []
    //     0x93c400: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c404: r1 = <void?>
    //     0x93c404: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x93c408: r0 = _Set()
    //     0x93c408: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x93c40c: mov             x2, x0
    // 0x93c410: r0 = _Uint32List
    //     0x93c410: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x93c414: stur            x2, [fp, #-0x18]
    // 0x93c418: StoreField: r2->field_1b = r0
    //     0x93c418: stur            w0, [x2, #0x1b]
    // 0x93c41c: StoreField: r2->field_b = rZR
    //     0x93c41c: stur            wzr, [x2, #0xb]
    // 0x93c420: r0 = const []
    //     0x93c420: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x93c424: StoreField: r2->field_f = r0
    //     0x93c424: stur            w0, [x2, #0xf]
    // 0x93c428: StoreField: r2->field_13 = rZR
    //     0x93c428: stur            wzr, [x2, #0x13]
    // 0x93c42c: ArrayStore: r2[0] = rZR  ; List_4
    //     0x93c42c: stur            wzr, [x2, #0x17]
    // 0x93c430: ldur            x0, [fp, #-0x10]
    // 0x93c434: LoadField: r1 = r0->field_f
    //     0x93c434: ldur            w1, [x0, #0xf]
    // 0x93c438: DecompressPointer r1
    //     0x93c438: add             x1, x1, HEAP, lsl #32
    // 0x93c43c: r0 = openUrlInApp()
    //     0x93c43c: bl              #0x93b9fc  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x93c440: ldur            x1, [fp, #-0x18]
    // 0x93c444: r2 = Null
    //     0x93c444: mov             x2, NULL
    // 0x93c448: r0 = add()
    //     0x93c448: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c44c: ldur            x1, [fp, #-0x28]
    // 0x93c450: ldur            x2, [fp, #-0x18]
    // 0x93c454: r0 = add()
    //     0x93c454: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c458: ldur            x1, [fp, #-0x20]
    // 0x93c45c: ldur            x2, [fp, #-0x28]
    // 0x93c460: r0 = add()
    //     0x93c460: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x93c464: ldur            x0, [fp, #-0x20]
    // 0x93c468: r0 = ReturnAsyncNotFuture()
    //     0x93c468: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x93c46c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93c46c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93c470: b               #0x93c194
  }
  _ build(/* No info */) {
    // ** addr: 0xaf6ce4, size: 0x58
    // 0xaf6ce4: EnterFrame
    //     0xaf6ce4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6ce8: mov             fp, SP
    // 0xaf6cec: AllocStack(0x10)
    //     0xaf6cec: sub             SP, SP, #0x10
    // 0xaf6cf0: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0xaf6cf0: stur            x1, [fp, #-8]
    // 0xaf6cf4: r1 = 1
    //     0xaf6cf4: movz            x1, #0x1
    // 0xaf6cf8: r0 = AllocateContext()
    //     0xaf6cf8: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf6cfc: mov             x1, x0
    // 0xaf6d00: ldur            x0, [fp, #-8]
    // 0xaf6d04: stur            x1, [fp, #-0x10]
    // 0xaf6d08: StoreField: r1->field_f = r0
    //     0xaf6d08: stur            w0, [x1, #0xf]
    // 0xaf6d0c: r0 = Obx()
    //     0xaf6d0c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xaf6d10: ldur            x2, [fp, #-0x10]
    // 0xaf6d14: r1 = Function '<anonymous closure>':.
    //     0xaf6d14: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dca0] AnonymousClosure: (0xaf6d3c), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::build (0xaf6ce4)
    //     0xaf6d18: ldr             x1, [x1, #0xca0]
    // 0xaf6d1c: stur            x0, [fp, #-8]
    // 0xaf6d20: r0 = AllocateClosure()
    //     0xaf6d20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf6d24: mov             x1, x0
    // 0xaf6d28: ldur            x0, [fp, #-8]
    // 0xaf6d2c: StoreField: r0->field_b = r1
    //     0xaf6d2c: stur            w1, [x0, #0xb]
    // 0xaf6d30: LeaveFrame
    //     0xaf6d30: mov             SP, fp
    //     0xaf6d34: ldp             fp, lr, [SP], #0x10
    // 0xaf6d38: ret
    //     0xaf6d38: ret             
  }
  [closure] Theme <anonymous closure>(dynamic) {
    // ** addr: 0xaf6d3c, size: 0x13c
    // 0xaf6d3c: EnterFrame
    //     0xaf6d3c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6d40: mov             fp, SP
    // 0xaf6d44: AllocStack(0x20)
    //     0xaf6d44: sub             SP, SP, #0x20
    // 0xaf6d48: SetupParameters()
    //     0xaf6d48: ldr             x0, [fp, #0x10]
    //     0xaf6d4c: ldur            w2, [x0, #0x17]
    //     0xaf6d50: add             x2, x2, HEAP, lsl #32
    //     0xaf6d54: stur            x2, [fp, #-8]
    // 0xaf6d58: CheckStackOverflow
    //     0xaf6d58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf6d5c: cmp             SP, x16
    //     0xaf6d60: b.ls            #0xaf6e6c
    // 0xaf6d64: LoadField: r0 = r2->field_f
    //     0xaf6d64: ldur            w0, [x2, #0xf]
    // 0xaf6d68: DecompressPointer r0
    //     0xaf6d68: add             x0, x0, HEAP, lsl #32
    // 0xaf6d6c: LoadField: r1 = r0->field_3f
    //     0xaf6d6c: ldur            w1, [x0, #0x3f]
    // 0xaf6d70: DecompressPointer r1
    //     0xaf6d70: add             x1, x1, HEAP, lsl #32
    // 0xaf6d74: LoadField: r0 = r1->field_5b
    //     0xaf6d74: ldur            w0, [x1, #0x5b]
    // 0xaf6d78: DecompressPointer r0
    //     0xaf6d78: add             x0, x0, HEAP, lsl #32
    // 0xaf6d7c: mov             x1, x0
    // 0xaf6d80: r0 = value()
    //     0xaf6d80: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf6d84: stur            x0, [fp, #-0x10]
    // 0xaf6d88: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0xaf6d88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf6d8c: ldr             x0, [x0, #0x1cd0]
    //     0xaf6d90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf6d94: cmp             w0, w16
    //     0xaf6d98: b.ne            #0xaf6da8
    //     0xaf6d9c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0xaf6da0: ldr             x2, [x2, #0xca8]
    //     0xaf6da4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf6da8: mov             x2, x0
    // 0xaf6dac: ldur            x0, [fp, #-8]
    // 0xaf6db0: LoadField: r3 = r0->field_f
    //     0xaf6db0: ldur            w3, [x0, #0xf]
    // 0xaf6db4: DecompressPointer r3
    //     0xaf6db4: add             x3, x3, HEAP, lsl #32
    // 0xaf6db8: stur            x3, [fp, #-0x18]
    // 0xaf6dbc: LoadField: r4 = r3->field_1b
    //     0xaf6dbc: ldur            x4, [x3, #0x1b]
    // 0xaf6dc0: LoadField: r0 = r2->field_b
    //     0xaf6dc0: ldur            w0, [x2, #0xb]
    // 0xaf6dc4: r1 = LoadInt32Instr(r0)
    //     0xaf6dc4: sbfx            x1, x0, #1, #0x1f
    // 0xaf6dc8: mov             x0, x1
    // 0xaf6dcc: mov             x1, x4
    // 0xaf6dd0: cmp             x1, x0
    // 0xaf6dd4: b.hs            #0xaf6e74
    // 0xaf6dd8: LoadField: r0 = r2->field_f
    //     0xaf6dd8: ldur            w0, [x2, #0xf]
    // 0xaf6ddc: DecompressPointer r0
    //     0xaf6ddc: add             x0, x0, HEAP, lsl #32
    // 0xaf6de0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xaf6de0: add             x16, x0, x4, lsl #2
    //     0xaf6de4: ldur            w1, [x16, #0xf]
    // 0xaf6de8: DecompressPointer r1
    //     0xaf6de8: add             x1, x1, HEAP, lsl #32
    // 0xaf6dec: stur            x1, [fp, #-8]
    // 0xaf6df0: r0 = Center()
    //     0xaf6df0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaf6df4: mov             x2, x0
    // 0xaf6df8: r0 = Instance_Alignment
    //     0xaf6df8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaf6dfc: ldr             x0, [x0, #0xb10]
    // 0xaf6e00: stur            x2, [fp, #-0x20]
    // 0xaf6e04: StoreField: r2->field_f = r0
    //     0xaf6e04: stur            w0, [x2, #0xf]
    // 0xaf6e08: ldur            x0, [fp, #-8]
    // 0xaf6e0c: StoreField: r2->field_b = r0
    //     0xaf6e0c: stur            w0, [x2, #0xb]
    // 0xaf6e10: ldur            x1, [fp, #-0x18]
    // 0xaf6e14: r0 = buildBottomNavigationMenu()
    //     0xaf6e14: bl              #0xaf6e78  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::buildBottomNavigationMenu
    // 0xaf6e18: stur            x0, [fp, #-8]
    // 0xaf6e1c: r0 = Scaffold()
    //     0xaf6e1c: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xaf6e20: mov             x1, x0
    // 0xaf6e24: ldur            x0, [fp, #-0x20]
    // 0xaf6e28: stur            x1, [fp, #-0x18]
    // 0xaf6e2c: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf6e2c: stur            w0, [x1, #0x17]
    // 0xaf6e30: ldur            x0, [fp, #-8]
    // 0xaf6e34: StoreField: r1->field_37 = r0
    //     0xaf6e34: stur            w0, [x1, #0x37]
    // 0xaf6e38: r0 = true
    //     0xaf6e38: add             x0, NULL, #0x20  ; true
    // 0xaf6e3c: StoreField: r1->field_43 = r0
    //     0xaf6e3c: stur            w0, [x1, #0x43]
    // 0xaf6e40: r0 = false
    //     0xaf6e40: add             x0, NULL, #0x30  ; false
    // 0xaf6e44: StoreField: r1->field_b = r0
    //     0xaf6e44: stur            w0, [x1, #0xb]
    // 0xaf6e48: StoreField: r1->field_f = r0
    //     0xaf6e48: stur            w0, [x1, #0xf]
    // 0xaf6e4c: r0 = Theme()
    //     0xaf6e4c: bl              #0x796f30  ; AllocateThemeStub -> Theme (size=0x14)
    // 0xaf6e50: ldur            x1, [fp, #-0x10]
    // 0xaf6e54: StoreField: r0->field_b = r1
    //     0xaf6e54: stur            w1, [x0, #0xb]
    // 0xaf6e58: ldur            x1, [fp, #-0x18]
    // 0xaf6e5c: StoreField: r0->field_f = r1
    //     0xaf6e5c: stur            w1, [x0, #0xf]
    // 0xaf6e60: LeaveFrame
    //     0xaf6e60: mov             SP, fp
    //     0xaf6e64: ldp             fp, lr, [SP], #0x10
    // 0xaf6e68: ret
    //     0xaf6e68: ret             
    // 0xaf6e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf6e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf6e70: b               #0xaf6d64
    // 0xaf6e74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf6e74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ buildBottomNavigationMenu(/* No info */) {
    // ** addr: 0xaf6e78, size: 0x1424
    // 0xaf6e78: EnterFrame
    //     0xaf6e78: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6e7c: mov             fp, SP
    // 0xaf6e80: AllocStack(0x70)
    //     0xaf6e80: sub             SP, SP, #0x70
    // 0xaf6e84: SetupParameters(_MainPageState this /* r1 => r2, fp-0x10 */)
    //     0xaf6e84: mov             x2, x1
    //     0xaf6e88: stur            x1, [fp, #-0x10]
    // 0xaf6e8c: CheckStackOverflow
    //     0xaf6e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf6e90: cmp             SP, x16
    //     0xaf6e94: b.ls            #0xaf827c
    // 0xaf6e98: LoadField: r0 = r2->field_1b
    //     0xaf6e98: ldur            x0, [x2, #0x1b]
    // 0xaf6e9c: stur            x0, [fp, #-8]
    // 0xaf6ea0: r0 = SvgPicture()
    //     0xaf6ea0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf6ea4: stur            x0, [fp, #-0x18]
    // 0xaf6ea8: r16 = "home"
    //     0xaf6ea8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb470] "home"
    //     0xaf6eac: ldr             x16, [x16, #0x470]
    // 0xaf6eb0: r30 = Instance_BoxFit
    //     0xaf6eb0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf6eb4: ldr             lr, [lr, #0xb18]
    // 0xaf6eb8: stp             lr, x16, [SP]
    // 0xaf6ebc: mov             x1, x0
    // 0xaf6ec0: r2 = "assets/images/home.svg"
    //     0xaf6ec0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "assets/images/home.svg"
    //     0xaf6ec4: ldr             x2, [x2, #0xb20]
    // 0xaf6ec8: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf6ec8: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf6ecc: ldr             x4, [x4, #0xb28]
    // 0xaf6ed0: r0 = SvgPicture.asset()
    //     0xaf6ed0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf6ed4: ldur            x2, [fp, #-0x10]
    // 0xaf6ed8: LoadField: r0 = r2->field_3f
    //     0xaf6ed8: ldur            w0, [x2, #0x3f]
    // 0xaf6edc: DecompressPointer r0
    //     0xaf6edc: add             x0, x0, HEAP, lsl #32
    // 0xaf6ee0: LoadField: r1 = r0->field_57
    //     0xaf6ee0: ldur            w1, [x0, #0x57]
    // 0xaf6ee4: DecompressPointer r1
    //     0xaf6ee4: add             x1, x1, HEAP, lsl #32
    // 0xaf6ee8: r0 = value()
    //     0xaf6ee8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf6eec: LoadField: r1 = r0->field_b
    //     0xaf6eec: ldur            w1, [x0, #0xb]
    // 0xaf6ef0: DecompressPointer r1
    //     0xaf6ef0: add             x1, x1, HEAP, lsl #32
    // 0xaf6ef4: cmp             w1, NULL
    // 0xaf6ef8: b.ne            #0xaf6f04
    // 0xaf6efc: r0 = Null
    //     0xaf6efc: mov             x0, NULL
    // 0xaf6f00: b               #0xaf6f3c
    // 0xaf6f04: LoadField: r0 = r1->field_57
    //     0xaf6f04: ldur            w0, [x1, #0x57]
    // 0xaf6f08: DecompressPointer r0
    //     0xaf6f08: add             x0, x0, HEAP, lsl #32
    // 0xaf6f0c: cmp             w0, NULL
    // 0xaf6f10: b.ne            #0xaf6f1c
    // 0xaf6f14: r0 = Null
    //     0xaf6f14: mov             x0, NULL
    // 0xaf6f18: b               #0xaf6f3c
    // 0xaf6f1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf6f1c: ldur            w1, [x0, #0x17]
    // 0xaf6f20: DecompressPointer r1
    //     0xaf6f20: add             x1, x1, HEAP, lsl #32
    // 0xaf6f24: cmp             w1, NULL
    // 0xaf6f28: b.ne            #0xaf6f34
    // 0xaf6f2c: r0 = Null
    //     0xaf6f2c: mov             x0, NULL
    // 0xaf6f30: b               #0xaf6f3c
    // 0xaf6f34: LoadField: r0 = r1->field_7
    //     0xaf6f34: ldur            w0, [x1, #7]
    // 0xaf6f38: DecompressPointer r0
    //     0xaf6f38: add             x0, x0, HEAP, lsl #32
    // 0xaf6f3c: cmp             w0, NULL
    // 0xaf6f40: b.ne            #0xaf6f4c
    // 0xaf6f44: r0 = 0
    //     0xaf6f44: movz            x0, #0
    // 0xaf6f48: b               #0xaf6f5c
    // 0xaf6f4c: r1 = LoadInt32Instr(r0)
    //     0xaf6f4c: sbfx            x1, x0, #1, #0x1f
    //     0xaf6f50: tbz             w0, #0, #0xaf6f58
    //     0xaf6f54: ldur            x1, [x0, #7]
    // 0xaf6f58: mov             x0, x1
    // 0xaf6f5c: ldur            x2, [fp, #-0x10]
    // 0xaf6f60: stur            x0, [fp, #-0x20]
    // 0xaf6f64: LoadField: r1 = r2->field_3f
    //     0xaf6f64: ldur            w1, [x2, #0x3f]
    // 0xaf6f68: DecompressPointer r1
    //     0xaf6f68: add             x1, x1, HEAP, lsl #32
    // 0xaf6f6c: LoadField: r3 = r1->field_57
    //     0xaf6f6c: ldur            w3, [x1, #0x57]
    // 0xaf6f70: DecompressPointer r3
    //     0xaf6f70: add             x3, x3, HEAP, lsl #32
    // 0xaf6f74: mov             x1, x3
    // 0xaf6f78: r0 = value()
    //     0xaf6f78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf6f7c: LoadField: r1 = r0->field_b
    //     0xaf6f7c: ldur            w1, [x0, #0xb]
    // 0xaf6f80: DecompressPointer r1
    //     0xaf6f80: add             x1, x1, HEAP, lsl #32
    // 0xaf6f84: cmp             w1, NULL
    // 0xaf6f88: b.ne            #0xaf6f94
    // 0xaf6f8c: r0 = Null
    //     0xaf6f8c: mov             x0, NULL
    // 0xaf6f90: b               #0xaf6fcc
    // 0xaf6f94: LoadField: r0 = r1->field_57
    //     0xaf6f94: ldur            w0, [x1, #0x57]
    // 0xaf6f98: DecompressPointer r0
    //     0xaf6f98: add             x0, x0, HEAP, lsl #32
    // 0xaf6f9c: cmp             w0, NULL
    // 0xaf6fa0: b.ne            #0xaf6fac
    // 0xaf6fa4: r0 = Null
    //     0xaf6fa4: mov             x0, NULL
    // 0xaf6fa8: b               #0xaf6fcc
    // 0xaf6fac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf6fac: ldur            w1, [x0, #0x17]
    // 0xaf6fb0: DecompressPointer r1
    //     0xaf6fb0: add             x1, x1, HEAP, lsl #32
    // 0xaf6fb4: cmp             w1, NULL
    // 0xaf6fb8: b.ne            #0xaf6fc4
    // 0xaf6fbc: r0 = Null
    //     0xaf6fbc: mov             x0, NULL
    // 0xaf6fc0: b               #0xaf6fcc
    // 0xaf6fc4: LoadField: r0 = r1->field_b
    //     0xaf6fc4: ldur            w0, [x1, #0xb]
    // 0xaf6fc8: DecompressPointer r0
    //     0xaf6fc8: add             x0, x0, HEAP, lsl #32
    // 0xaf6fcc: cmp             w0, NULL
    // 0xaf6fd0: b.ne            #0xaf6fdc
    // 0xaf6fd4: r0 = 0
    //     0xaf6fd4: movz            x0, #0
    // 0xaf6fd8: b               #0xaf6fec
    // 0xaf6fdc: r1 = LoadInt32Instr(r0)
    //     0xaf6fdc: sbfx            x1, x0, #1, #0x1f
    //     0xaf6fe0: tbz             w0, #0, #0xaf6fe8
    //     0xaf6fe4: ldur            x1, [x0, #7]
    // 0xaf6fe8: mov             x0, x1
    // 0xaf6fec: ldur            x2, [fp, #-0x10]
    // 0xaf6ff0: stur            x0, [fp, #-0x28]
    // 0xaf6ff4: LoadField: r1 = r2->field_3f
    //     0xaf6ff4: ldur            w1, [x2, #0x3f]
    // 0xaf6ff8: DecompressPointer r1
    //     0xaf6ff8: add             x1, x1, HEAP, lsl #32
    // 0xaf6ffc: LoadField: r3 = r1->field_57
    //     0xaf6ffc: ldur            w3, [x1, #0x57]
    // 0xaf7000: DecompressPointer r3
    //     0xaf7000: add             x3, x3, HEAP, lsl #32
    // 0xaf7004: mov             x1, x3
    // 0xaf7008: r0 = value()
    //     0xaf7008: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf700c: LoadField: r1 = r0->field_b
    //     0xaf700c: ldur            w1, [x0, #0xb]
    // 0xaf7010: DecompressPointer r1
    //     0xaf7010: add             x1, x1, HEAP, lsl #32
    // 0xaf7014: cmp             w1, NULL
    // 0xaf7018: b.ne            #0xaf7024
    // 0xaf701c: r0 = Null
    //     0xaf701c: mov             x0, NULL
    // 0xaf7020: b               #0xaf705c
    // 0xaf7024: LoadField: r0 = r1->field_57
    //     0xaf7024: ldur            w0, [x1, #0x57]
    // 0xaf7028: DecompressPointer r0
    //     0xaf7028: add             x0, x0, HEAP, lsl #32
    // 0xaf702c: cmp             w0, NULL
    // 0xaf7030: b.ne            #0xaf703c
    // 0xaf7034: r0 = Null
    //     0xaf7034: mov             x0, NULL
    // 0xaf7038: b               #0xaf705c
    // 0xaf703c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf703c: ldur            w1, [x0, #0x17]
    // 0xaf7040: DecompressPointer r1
    //     0xaf7040: add             x1, x1, HEAP, lsl #32
    // 0xaf7044: cmp             w1, NULL
    // 0xaf7048: b.ne            #0xaf7054
    // 0xaf704c: r0 = Null
    //     0xaf704c: mov             x0, NULL
    // 0xaf7050: b               #0xaf705c
    // 0xaf7054: LoadField: r0 = r1->field_f
    //     0xaf7054: ldur            w0, [x1, #0xf]
    // 0xaf7058: DecompressPointer r0
    //     0xaf7058: add             x0, x0, HEAP, lsl #32
    // 0xaf705c: cmp             w0, NULL
    // 0xaf7060: b.ne            #0xaf706c
    // 0xaf7064: r1 = 0
    //     0xaf7064: movz            x1, #0
    // 0xaf7068: b               #0xaf7078
    // 0xaf706c: r1 = LoadInt32Instr(r0)
    //     0xaf706c: sbfx            x1, x0, #1, #0x1f
    //     0xaf7070: tbz             w0, #0, #0xaf7078
    //     0xaf7074: ldur            x1, [x0, #7]
    // 0xaf7078: ldur            x2, [fp, #-0x10]
    // 0xaf707c: ldur            x0, [fp, #-0x18]
    // 0xaf7080: stur            x1, [fp, #-0x30]
    // 0xaf7084: r0 = Color()
    //     0xaf7084: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaf7088: mov             x1, x0
    // 0xaf708c: r0 = Instance_ColorSpace
    //     0xaf708c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaf7090: stur            x1, [fp, #-0x38]
    // 0xaf7094: StoreField: r1->field_27 = r0
    //     0xaf7094: stur            w0, [x1, #0x27]
    // 0xaf7098: d0 = 1.000000
    //     0xaf7098: fmov            d0, #1.00000000
    // 0xaf709c: StoreField: r1->field_7 = d0
    //     0xaf709c: stur            d0, [x1, #7]
    // 0xaf70a0: ldur            x2, [fp, #-0x20]
    // 0xaf70a4: ubfx            x2, x2, #0, #0x20
    // 0xaf70a8: and             w3, w2, #0xff
    // 0xaf70ac: ubfx            x3, x3, #0, #0x20
    // 0xaf70b0: scvtf           d1, x3
    // 0xaf70b4: d2 = 255.000000
    //     0xaf70b4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaf70b8: fdiv            d3, d1, d2
    // 0xaf70bc: StoreField: r1->field_f = d3
    //     0xaf70bc: stur            d3, [x1, #0xf]
    // 0xaf70c0: ldur            x2, [fp, #-0x28]
    // 0xaf70c4: ubfx            x2, x2, #0, #0x20
    // 0xaf70c8: and             w3, w2, #0xff
    // 0xaf70cc: ubfx            x3, x3, #0, #0x20
    // 0xaf70d0: scvtf           d1, x3
    // 0xaf70d4: fdiv            d3, d1, d2
    // 0xaf70d8: ArrayStore: r1[0] = d3  ; List_8
    //     0xaf70d8: stur            d3, [x1, #0x17]
    // 0xaf70dc: ldur            x2, [fp, #-0x30]
    // 0xaf70e0: ubfx            x2, x2, #0, #0x20
    // 0xaf70e4: and             w3, w2, #0xff
    // 0xaf70e8: ubfx            x3, x3, #0, #0x20
    // 0xaf70ec: scvtf           d1, x3
    // 0xaf70f0: fdiv            d3, d1, d2
    // 0xaf70f4: StoreField: r1->field_1f = d3
    //     0xaf70f4: stur            d3, [x1, #0x1f]
    // 0xaf70f8: r0 = ColorFilter()
    //     0xaf70f8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xaf70fc: mov             x1, x0
    // 0xaf7100: ldur            x0, [fp, #-0x38]
    // 0xaf7104: stur            x1, [fp, #-0x40]
    // 0xaf7108: StoreField: r1->field_7 = r0
    //     0xaf7108: stur            w0, [x1, #7]
    // 0xaf710c: r0 = Instance_BlendMode
    //     0xaf710c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xaf7110: ldr             x0, [x0, #0xb30]
    // 0xaf7114: StoreField: r1->field_b = r0
    //     0xaf7114: stur            w0, [x1, #0xb]
    // 0xaf7118: r2 = 1
    //     0xaf7118: movz            x2, #0x1
    // 0xaf711c: StoreField: r1->field_13 = r2
    //     0xaf711c: stur            x2, [x1, #0x13]
    // 0xaf7120: r0 = SvgPicture()
    //     0xaf7120: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf7124: stur            x0, [fp, #-0x38]
    // 0xaf7128: r16 = "home"
    //     0xaf7128: add             x16, PP, #0xb, lsl #12  ; [pp+0xb470] "home"
    //     0xaf712c: ldr             x16, [x16, #0x470]
    // 0xaf7130: r30 = Instance_BoxFit
    //     0xaf7130: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf7134: ldr             lr, [lr, #0xb18]
    // 0xaf7138: stp             lr, x16, [SP, #8]
    // 0xaf713c: ldur            x16, [fp, #-0x40]
    // 0xaf7140: str             x16, [SP]
    // 0xaf7144: mov             x1, x0
    // 0xaf7148: r2 = "assets/images/home.svg"
    //     0xaf7148: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "assets/images/home.svg"
    //     0xaf714c: ldr             x2, [x2, #0xb20]
    // 0xaf7150: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf7150: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf7154: ldr             x4, [x4, #0xb38]
    // 0xaf7158: r0 = SvgPicture.asset()
    //     0xaf7158: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf715c: r0 = BottomNavigationBarItem()
    //     0xaf715c: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xaf7160: mov             x2, x0
    // 0xaf7164: ldur            x0, [fp, #-0x18]
    // 0xaf7168: stur            x2, [fp, #-0x40]
    // 0xaf716c: StoreField: r2->field_b = r0
    //     0xaf716c: stur            w0, [x2, #0xb]
    // 0xaf7170: r0 = "Home"
    //     0xaf7170: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb40] "Home"
    //     0xaf7174: ldr             x0, [x0, #0xb40]
    // 0xaf7178: StoreField: r2->field_13 = r0
    //     0xaf7178: stur            w0, [x2, #0x13]
    // 0xaf717c: ldur            x0, [fp, #-0x38]
    // 0xaf7180: StoreField: r2->field_f = r0
    //     0xaf7180: stur            w0, [x2, #0xf]
    // 0xaf7184: ldur            x0, [fp, #-0x10]
    // 0xaf7188: LoadField: r1 = r0->field_3f
    //     0xaf7188: ldur            w1, [x0, #0x3f]
    // 0xaf718c: DecompressPointer r1
    //     0xaf718c: add             x1, x1, HEAP, lsl #32
    // 0xaf7190: LoadField: r3 = r1->field_57
    //     0xaf7190: ldur            w3, [x1, #0x57]
    // 0xaf7194: DecompressPointer r3
    //     0xaf7194: add             x3, x3, HEAP, lsl #32
    // 0xaf7198: mov             x1, x3
    // 0xaf719c: r0 = value()
    //     0xaf719c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf71a0: LoadField: r1 = r0->field_b
    //     0xaf71a0: ldur            w1, [x0, #0xb]
    // 0xaf71a4: DecompressPointer r1
    //     0xaf71a4: add             x1, x1, HEAP, lsl #32
    // 0xaf71a8: cmp             w1, NULL
    // 0xaf71ac: b.ne            #0xaf71b8
    // 0xaf71b0: r0 = Null
    //     0xaf71b0: mov             x0, NULL
    // 0xaf71b4: b               #0xaf71f0
    // 0xaf71b8: LoadField: r0 = r1->field_57
    //     0xaf71b8: ldur            w0, [x1, #0x57]
    // 0xaf71bc: DecompressPointer r0
    //     0xaf71bc: add             x0, x0, HEAP, lsl #32
    // 0xaf71c0: cmp             w0, NULL
    // 0xaf71c4: b.ne            #0xaf71d0
    // 0xaf71c8: r0 = Null
    //     0xaf71c8: mov             x0, NULL
    // 0xaf71cc: b               #0xaf71f0
    // 0xaf71d0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf71d0: ldur            w1, [x0, #0x17]
    // 0xaf71d4: DecompressPointer r1
    //     0xaf71d4: add             x1, x1, HEAP, lsl #32
    // 0xaf71d8: cmp             w1, NULL
    // 0xaf71dc: b.ne            #0xaf71e8
    // 0xaf71e0: r0 = Null
    //     0xaf71e0: mov             x0, NULL
    // 0xaf71e4: b               #0xaf71f0
    // 0xaf71e8: LoadField: r0 = r1->field_7
    //     0xaf71e8: ldur            w0, [x1, #7]
    // 0xaf71ec: DecompressPointer r0
    //     0xaf71ec: add             x0, x0, HEAP, lsl #32
    // 0xaf71f0: cmp             w0, NULL
    // 0xaf71f4: b.ne            #0xaf7200
    // 0xaf71f8: r0 = 0
    //     0xaf71f8: movz            x0, #0
    // 0xaf71fc: b               #0xaf7210
    // 0xaf7200: r1 = LoadInt32Instr(r0)
    //     0xaf7200: sbfx            x1, x0, #1, #0x1f
    //     0xaf7204: tbz             w0, #0, #0xaf720c
    //     0xaf7208: ldur            x1, [x0, #7]
    // 0xaf720c: mov             x0, x1
    // 0xaf7210: ldur            x2, [fp, #-0x10]
    // 0xaf7214: stur            x0, [fp, #-0x20]
    // 0xaf7218: LoadField: r1 = r2->field_3f
    //     0xaf7218: ldur            w1, [x2, #0x3f]
    // 0xaf721c: DecompressPointer r1
    //     0xaf721c: add             x1, x1, HEAP, lsl #32
    // 0xaf7220: LoadField: r3 = r1->field_57
    //     0xaf7220: ldur            w3, [x1, #0x57]
    // 0xaf7224: DecompressPointer r3
    //     0xaf7224: add             x3, x3, HEAP, lsl #32
    // 0xaf7228: mov             x1, x3
    // 0xaf722c: r0 = value()
    //     0xaf722c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7230: LoadField: r1 = r0->field_b
    //     0xaf7230: ldur            w1, [x0, #0xb]
    // 0xaf7234: DecompressPointer r1
    //     0xaf7234: add             x1, x1, HEAP, lsl #32
    // 0xaf7238: cmp             w1, NULL
    // 0xaf723c: b.ne            #0xaf7248
    // 0xaf7240: r0 = Null
    //     0xaf7240: mov             x0, NULL
    // 0xaf7244: b               #0xaf7280
    // 0xaf7248: LoadField: r0 = r1->field_57
    //     0xaf7248: ldur            w0, [x1, #0x57]
    // 0xaf724c: DecompressPointer r0
    //     0xaf724c: add             x0, x0, HEAP, lsl #32
    // 0xaf7250: cmp             w0, NULL
    // 0xaf7254: b.ne            #0xaf7260
    // 0xaf7258: r0 = Null
    //     0xaf7258: mov             x0, NULL
    // 0xaf725c: b               #0xaf7280
    // 0xaf7260: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7260: ldur            w1, [x0, #0x17]
    // 0xaf7264: DecompressPointer r1
    //     0xaf7264: add             x1, x1, HEAP, lsl #32
    // 0xaf7268: cmp             w1, NULL
    // 0xaf726c: b.ne            #0xaf7278
    // 0xaf7270: r0 = Null
    //     0xaf7270: mov             x0, NULL
    // 0xaf7274: b               #0xaf7280
    // 0xaf7278: LoadField: r0 = r1->field_b
    //     0xaf7278: ldur            w0, [x1, #0xb]
    // 0xaf727c: DecompressPointer r0
    //     0xaf727c: add             x0, x0, HEAP, lsl #32
    // 0xaf7280: cmp             w0, NULL
    // 0xaf7284: b.ne            #0xaf7290
    // 0xaf7288: r0 = 0
    //     0xaf7288: movz            x0, #0
    // 0xaf728c: b               #0xaf72a0
    // 0xaf7290: r1 = LoadInt32Instr(r0)
    //     0xaf7290: sbfx            x1, x0, #1, #0x1f
    //     0xaf7294: tbz             w0, #0, #0xaf729c
    //     0xaf7298: ldur            x1, [x0, #7]
    // 0xaf729c: mov             x0, x1
    // 0xaf72a0: ldur            x2, [fp, #-0x10]
    // 0xaf72a4: stur            x0, [fp, #-0x28]
    // 0xaf72a8: LoadField: r1 = r2->field_3f
    //     0xaf72a8: ldur            w1, [x2, #0x3f]
    // 0xaf72ac: DecompressPointer r1
    //     0xaf72ac: add             x1, x1, HEAP, lsl #32
    // 0xaf72b0: LoadField: r3 = r1->field_57
    //     0xaf72b0: ldur            w3, [x1, #0x57]
    // 0xaf72b4: DecompressPointer r3
    //     0xaf72b4: add             x3, x3, HEAP, lsl #32
    // 0xaf72b8: mov             x1, x3
    // 0xaf72bc: r0 = value()
    //     0xaf72bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf72c0: LoadField: r1 = r0->field_b
    //     0xaf72c0: ldur            w1, [x0, #0xb]
    // 0xaf72c4: DecompressPointer r1
    //     0xaf72c4: add             x1, x1, HEAP, lsl #32
    // 0xaf72c8: cmp             w1, NULL
    // 0xaf72cc: b.ne            #0xaf72d8
    // 0xaf72d0: r0 = Null
    //     0xaf72d0: mov             x0, NULL
    // 0xaf72d4: b               #0xaf7310
    // 0xaf72d8: LoadField: r0 = r1->field_57
    //     0xaf72d8: ldur            w0, [x1, #0x57]
    // 0xaf72dc: DecompressPointer r0
    //     0xaf72dc: add             x0, x0, HEAP, lsl #32
    // 0xaf72e0: cmp             w0, NULL
    // 0xaf72e4: b.ne            #0xaf72f0
    // 0xaf72e8: r0 = Null
    //     0xaf72e8: mov             x0, NULL
    // 0xaf72ec: b               #0xaf7310
    // 0xaf72f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf72f0: ldur            w1, [x0, #0x17]
    // 0xaf72f4: DecompressPointer r1
    //     0xaf72f4: add             x1, x1, HEAP, lsl #32
    // 0xaf72f8: cmp             w1, NULL
    // 0xaf72fc: b.ne            #0xaf7308
    // 0xaf7300: r0 = Null
    //     0xaf7300: mov             x0, NULL
    // 0xaf7304: b               #0xaf7310
    // 0xaf7308: LoadField: r0 = r1->field_f
    //     0xaf7308: ldur            w0, [x1, #0xf]
    // 0xaf730c: DecompressPointer r0
    //     0xaf730c: add             x0, x0, HEAP, lsl #32
    // 0xaf7310: cmp             w0, NULL
    // 0xaf7314: b.ne            #0xaf7320
    // 0xaf7318: r0 = 0
    //     0xaf7318: movz            x0, #0
    // 0xaf731c: b               #0xaf7330
    // 0xaf7320: r1 = LoadInt32Instr(r0)
    //     0xaf7320: sbfx            x1, x0, #1, #0x1f
    //     0xaf7324: tbz             w0, #0, #0xaf732c
    //     0xaf7328: ldur            x1, [x0, #7]
    // 0xaf732c: mov             x0, x1
    // 0xaf7330: ldur            x2, [fp, #-0x10]
    // 0xaf7334: stur            x0, [fp, #-0x30]
    // 0xaf7338: r0 = Color()
    //     0xaf7338: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaf733c: mov             x1, x0
    // 0xaf7340: r0 = Instance_ColorSpace
    //     0xaf7340: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaf7344: stur            x1, [fp, #-0x18]
    // 0xaf7348: StoreField: r1->field_27 = r0
    //     0xaf7348: stur            w0, [x1, #0x27]
    // 0xaf734c: d0 = 1.000000
    //     0xaf734c: fmov            d0, #1.00000000
    // 0xaf7350: StoreField: r1->field_7 = d0
    //     0xaf7350: stur            d0, [x1, #7]
    // 0xaf7354: ldur            x2, [fp, #-0x20]
    // 0xaf7358: ubfx            x2, x2, #0, #0x20
    // 0xaf735c: and             w3, w2, #0xff
    // 0xaf7360: ubfx            x3, x3, #0, #0x20
    // 0xaf7364: scvtf           d1, x3
    // 0xaf7368: d2 = 255.000000
    //     0xaf7368: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaf736c: fdiv            d3, d1, d2
    // 0xaf7370: StoreField: r1->field_f = d3
    //     0xaf7370: stur            d3, [x1, #0xf]
    // 0xaf7374: ldur            x2, [fp, #-0x28]
    // 0xaf7378: ubfx            x2, x2, #0, #0x20
    // 0xaf737c: and             w3, w2, #0xff
    // 0xaf7380: ubfx            x3, x3, #0, #0x20
    // 0xaf7384: scvtf           d1, x3
    // 0xaf7388: fdiv            d3, d1, d2
    // 0xaf738c: ArrayStore: r1[0] = d3  ; List_8
    //     0xaf738c: stur            d3, [x1, #0x17]
    // 0xaf7390: ldur            x2, [fp, #-0x30]
    // 0xaf7394: ubfx            x2, x2, #0, #0x20
    // 0xaf7398: and             w3, w2, #0xff
    // 0xaf739c: ubfx            x3, x3, #0, #0x20
    // 0xaf73a0: scvtf           d1, x3
    // 0xaf73a4: fdiv            d3, d1, d2
    // 0xaf73a8: StoreField: r1->field_1f = d3
    //     0xaf73a8: stur            d3, [x1, #0x1f]
    // 0xaf73ac: r0 = InitLateStaticField(0xe68) // [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_pages
    //     0xaf73ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf73b0: ldr             x0, [x0, #0x1cd0]
    //     0xaf73b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf73b8: cmp             w0, w16
    //     0xaf73bc: b.ne            #0xaf73cc
    //     0xaf73c0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dca8] Field <_MainPageState@1473460261._pages@1473460261>: static late final (offset: 0xe68)
    //     0xaf73c4: ldr             x2, [x2, #0xca8]
    //     0xaf73c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf73cc: mov             x3, x0
    // 0xaf73d0: stur            x3, [fp, #-0x48]
    // 0xaf73d4: LoadField: r0 = r3->field_b
    //     0xaf73d4: ldur            w0, [x3, #0xb]
    // 0xaf73d8: r1 = LoadInt32Instr(r0)
    //     0xaf73d8: sbfx            x1, x0, #1, #0x1f
    // 0xaf73dc: mov             x0, x1
    // 0xaf73e0: r1 = 1
    //     0xaf73e0: movz            x1, #0x1
    // 0xaf73e4: cmp             x1, x0
    // 0xaf73e8: b.hs            #0xaf8284
    // 0xaf73ec: LoadField: r0 = r3->field_f
    //     0xaf73ec: ldur            w0, [x3, #0xf]
    // 0xaf73f0: DecompressPointer r0
    //     0xaf73f0: add             x0, x0, HEAP, lsl #32
    // 0xaf73f4: LoadField: r4 = r0->field_13
    //     0xaf73f4: ldur            w4, [x0, #0x13]
    // 0xaf73f8: DecompressPointer r4
    //     0xaf73f8: add             x4, x4, HEAP, lsl #32
    // 0xaf73fc: mov             x0, x4
    // 0xaf7400: stur            x4, [fp, #-0x38]
    // 0xaf7404: r2 = Null
    //     0xaf7404: mov             x2, NULL
    // 0xaf7408: r1 = Null
    //     0xaf7408: mov             x1, NULL
    // 0xaf740c: r4 = 60
    //     0xaf740c: movz            x4, #0x3c
    // 0xaf7410: branchIfSmi(r0, 0xaf741c)
    //     0xaf7410: tbz             w0, #0, #0xaf741c
    // 0xaf7414: r4 = LoadClassIdInstr(r0)
    //     0xaf7414: ldur            x4, [x0, #-1]
    //     0xaf7418: ubfx            x4, x4, #0xc, #0x14
    // 0xaf741c: r17 = 4596
    //     0xaf741c: movz            x17, #0x11f4
    // 0xaf7420: cmp             x4, x17
    // 0xaf7424: b.eq            #0xaf743c
    // 0xaf7428: r8 = OrdersView
    //     0xaf7428: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0xaf742c: ldr             x8, [x8, #0xcb0]
    // 0xaf7430: r3 = Null
    //     0xaf7430: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dcb8] Null
    //     0xaf7434: ldr             x3, [x3, #0xcb8]
    // 0xaf7438: r0 = DefaultTypeTest()
    //     0xaf7438: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xaf743c: ldur            x1, [fp, #-0x38]
    // 0xaf7440: r0 = controller()
    //     0xaf7440: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf7444: LoadField: r1 = r0->field_5b
    //     0xaf7444: ldur            w1, [x0, #0x5b]
    // 0xaf7448: DecompressPointer r1
    //     0xaf7448: add             x1, x1, HEAP, lsl #32
    // 0xaf744c: r0 = value()
    //     0xaf744c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7450: ldur            x3, [fp, #-0x48]
    // 0xaf7454: LoadField: r0 = r3->field_b
    //     0xaf7454: ldur            w0, [x3, #0xb]
    // 0xaf7458: r1 = LoadInt32Instr(r0)
    //     0xaf7458: sbfx            x1, x0, #1, #0x1f
    // 0xaf745c: mov             x0, x1
    // 0xaf7460: r1 = 1
    //     0xaf7460: movz            x1, #0x1
    // 0xaf7464: cmp             x1, x0
    // 0xaf7468: b.hs            #0xaf8288
    // 0xaf746c: LoadField: r0 = r3->field_f
    //     0xaf746c: ldur            w0, [x3, #0xf]
    // 0xaf7470: DecompressPointer r0
    //     0xaf7470: add             x0, x0, HEAP, lsl #32
    // 0xaf7474: LoadField: r4 = r0->field_13
    //     0xaf7474: ldur            w4, [x0, #0x13]
    // 0xaf7478: DecompressPointer r4
    //     0xaf7478: add             x4, x4, HEAP, lsl #32
    // 0xaf747c: mov             x0, x4
    // 0xaf7480: stur            x4, [fp, #-0x38]
    // 0xaf7484: r2 = Null
    //     0xaf7484: mov             x2, NULL
    // 0xaf7488: r1 = Null
    //     0xaf7488: mov             x1, NULL
    // 0xaf748c: r4 = 60
    //     0xaf748c: movz            x4, #0x3c
    // 0xaf7490: branchIfSmi(r0, 0xaf749c)
    //     0xaf7490: tbz             w0, #0, #0xaf749c
    // 0xaf7494: r4 = LoadClassIdInstr(r0)
    //     0xaf7494: ldur            x4, [x0, #-1]
    //     0xaf7498: ubfx            x4, x4, #0xc, #0x14
    // 0xaf749c: r17 = 4596
    //     0xaf749c: movz            x17, #0x11f4
    // 0xaf74a0: cmp             x4, x17
    // 0xaf74a4: b.eq            #0xaf74bc
    // 0xaf74a8: r8 = OrdersView
    //     0xaf74a8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0xaf74ac: ldr             x8, [x8, #0xcb0]
    // 0xaf74b0: r3 = Null
    //     0xaf74b0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dcc8] Null
    //     0xaf74b4: ldr             x3, [x3, #0xcc8]
    // 0xaf74b8: r0 = DefaultTypeTest()
    //     0xaf74b8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xaf74bc: ldur            x1, [fp, #-0x38]
    // 0xaf74c0: r0 = controller()
    //     0xaf74c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf74c4: LoadField: r1 = r0->field_5b
    //     0xaf74c4: ldur            w1, [x0, #0x5b]
    // 0xaf74c8: DecompressPointer r1
    //     0xaf74c8: add             x1, x1, HEAP, lsl #32
    // 0xaf74cc: r0 = value()
    //     0xaf74cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf74d0: ldur            x2, [fp, #-0x10]
    // 0xaf74d4: LoadField: r1 = r2->field_f
    //     0xaf74d4: ldur            w1, [x2, #0xf]
    // 0xaf74d8: DecompressPointer r1
    //     0xaf74d8: add             x1, x1, HEAP, lsl #32
    // 0xaf74dc: cmp             w1, NULL
    // 0xaf74e0: b.eq            #0xaf828c
    // 0xaf74e4: r0 = of()
    //     0xaf74e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf74e8: LoadField: r1 = r0->field_87
    //     0xaf74e8: ldur            w1, [x0, #0x87]
    // 0xaf74ec: DecompressPointer r1
    //     0xaf74ec: add             x1, x1, HEAP, lsl #32
    // 0xaf74f0: LoadField: r0 = r1->field_27
    //     0xaf74f0: ldur            w0, [x1, #0x27]
    // 0xaf74f4: DecompressPointer r0
    //     0xaf74f4: add             x0, x0, HEAP, lsl #32
    // 0xaf74f8: r16 = Instance_Color
    //     0xaf74f8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf74fc: str             x16, [SP]
    // 0xaf7500: mov             x1, x0
    // 0xaf7504: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaf7504: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaf7508: ldr             x4, [x4, #0xf40]
    // 0xaf750c: r0 = copyWith()
    //     0xaf750c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf7510: stur            x0, [fp, #-0x38]
    // 0xaf7514: r0 = Text()
    //     0xaf7514: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf7518: mov             x1, x0
    // 0xaf751c: r0 = ""
    //     0xaf751c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf7520: stur            x1, [fp, #-0x50]
    // 0xaf7524: StoreField: r1->field_b = r0
    //     0xaf7524: stur            w0, [x1, #0xb]
    // 0xaf7528: ldur            x2, [fp, #-0x38]
    // 0xaf752c: StoreField: r1->field_13 = r2
    //     0xaf752c: stur            w2, [x1, #0x13]
    // 0xaf7530: r0 = SvgPicture()
    //     0xaf7530: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf7534: stur            x0, [fp, #-0x38]
    // 0xaf7538: r16 = "user"
    //     0xaf7538: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb70] "user"
    //     0xaf753c: ldr             x16, [x16, #0xb70]
    // 0xaf7540: r30 = Instance_BoxFit
    //     0xaf7540: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf7544: ldr             lr, [lr, #0xb18]
    // 0xaf7548: stp             lr, x16, [SP]
    // 0xaf754c: mov             x1, x0
    // 0xaf7550: r2 = "assets/images/user.svg"
    //     0xaf7550: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb78] "assets/images/user.svg"
    //     0xaf7554: ldr             x2, [x2, #0xb78]
    // 0xaf7558: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf7558: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf755c: ldr             x4, [x4, #0xb28]
    // 0xaf7560: r0 = SvgPicture.asset()
    //     0xaf7560: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf7564: r0 = Badge()
    //     0xaf7564: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0xaf7568: mov             x2, x0
    // 0xaf756c: ldur            x0, [fp, #-0x18]
    // 0xaf7570: stur            x2, [fp, #-0x58]
    // 0xaf7574: StoreField: r2->field_b = r0
    //     0xaf7574: stur            w0, [x2, #0xb]
    // 0xaf7578: ldur            x0, [fp, #-0x50]
    // 0xaf757c: StoreField: r2->field_27 = r0
    //     0xaf757c: stur            w0, [x2, #0x27]
    // 0xaf7580: r0 = false
    //     0xaf7580: add             x0, NULL, #0x30  ; false
    // 0xaf7584: StoreField: r2->field_2b = r0
    //     0xaf7584: stur            w0, [x2, #0x2b]
    // 0xaf7588: ldur            x1, [fp, #-0x38]
    // 0xaf758c: StoreField: r2->field_2f = r1
    //     0xaf758c: stur            w1, [x2, #0x2f]
    // 0xaf7590: ldur            x3, [fp, #-0x10]
    // 0xaf7594: LoadField: r1 = r3->field_3f
    //     0xaf7594: ldur            w1, [x3, #0x3f]
    // 0xaf7598: DecompressPointer r1
    //     0xaf7598: add             x1, x1, HEAP, lsl #32
    // 0xaf759c: LoadField: r4 = r1->field_57
    //     0xaf759c: ldur            w4, [x1, #0x57]
    // 0xaf75a0: DecompressPointer r4
    //     0xaf75a0: add             x4, x4, HEAP, lsl #32
    // 0xaf75a4: mov             x1, x4
    // 0xaf75a8: r0 = value()
    //     0xaf75a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf75ac: LoadField: r1 = r0->field_b
    //     0xaf75ac: ldur            w1, [x0, #0xb]
    // 0xaf75b0: DecompressPointer r1
    //     0xaf75b0: add             x1, x1, HEAP, lsl #32
    // 0xaf75b4: cmp             w1, NULL
    // 0xaf75b8: b.ne            #0xaf75c4
    // 0xaf75bc: r0 = Null
    //     0xaf75bc: mov             x0, NULL
    // 0xaf75c0: b               #0xaf75fc
    // 0xaf75c4: LoadField: r0 = r1->field_57
    //     0xaf75c4: ldur            w0, [x1, #0x57]
    // 0xaf75c8: DecompressPointer r0
    //     0xaf75c8: add             x0, x0, HEAP, lsl #32
    // 0xaf75cc: cmp             w0, NULL
    // 0xaf75d0: b.ne            #0xaf75dc
    // 0xaf75d4: r0 = Null
    //     0xaf75d4: mov             x0, NULL
    // 0xaf75d8: b               #0xaf75fc
    // 0xaf75dc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf75dc: ldur            w1, [x0, #0x17]
    // 0xaf75e0: DecompressPointer r1
    //     0xaf75e0: add             x1, x1, HEAP, lsl #32
    // 0xaf75e4: cmp             w1, NULL
    // 0xaf75e8: b.ne            #0xaf75f4
    // 0xaf75ec: r0 = Null
    //     0xaf75ec: mov             x0, NULL
    // 0xaf75f0: b               #0xaf75fc
    // 0xaf75f4: LoadField: r0 = r1->field_7
    //     0xaf75f4: ldur            w0, [x1, #7]
    // 0xaf75f8: DecompressPointer r0
    //     0xaf75f8: add             x0, x0, HEAP, lsl #32
    // 0xaf75fc: cmp             w0, NULL
    // 0xaf7600: b.ne            #0xaf760c
    // 0xaf7604: r0 = 0
    //     0xaf7604: movz            x0, #0
    // 0xaf7608: b               #0xaf761c
    // 0xaf760c: r1 = LoadInt32Instr(r0)
    //     0xaf760c: sbfx            x1, x0, #1, #0x1f
    //     0xaf7610: tbz             w0, #0, #0xaf7618
    //     0xaf7614: ldur            x1, [x0, #7]
    // 0xaf7618: mov             x0, x1
    // 0xaf761c: ldur            x2, [fp, #-0x10]
    // 0xaf7620: stur            x0, [fp, #-0x20]
    // 0xaf7624: LoadField: r1 = r2->field_3f
    //     0xaf7624: ldur            w1, [x2, #0x3f]
    // 0xaf7628: DecompressPointer r1
    //     0xaf7628: add             x1, x1, HEAP, lsl #32
    // 0xaf762c: LoadField: r3 = r1->field_57
    //     0xaf762c: ldur            w3, [x1, #0x57]
    // 0xaf7630: DecompressPointer r3
    //     0xaf7630: add             x3, x3, HEAP, lsl #32
    // 0xaf7634: mov             x1, x3
    // 0xaf7638: r0 = value()
    //     0xaf7638: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf763c: LoadField: r1 = r0->field_b
    //     0xaf763c: ldur            w1, [x0, #0xb]
    // 0xaf7640: DecompressPointer r1
    //     0xaf7640: add             x1, x1, HEAP, lsl #32
    // 0xaf7644: cmp             w1, NULL
    // 0xaf7648: b.ne            #0xaf7654
    // 0xaf764c: r0 = Null
    //     0xaf764c: mov             x0, NULL
    // 0xaf7650: b               #0xaf768c
    // 0xaf7654: LoadField: r0 = r1->field_57
    //     0xaf7654: ldur            w0, [x1, #0x57]
    // 0xaf7658: DecompressPointer r0
    //     0xaf7658: add             x0, x0, HEAP, lsl #32
    // 0xaf765c: cmp             w0, NULL
    // 0xaf7660: b.ne            #0xaf766c
    // 0xaf7664: r0 = Null
    //     0xaf7664: mov             x0, NULL
    // 0xaf7668: b               #0xaf768c
    // 0xaf766c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf766c: ldur            w1, [x0, #0x17]
    // 0xaf7670: DecompressPointer r1
    //     0xaf7670: add             x1, x1, HEAP, lsl #32
    // 0xaf7674: cmp             w1, NULL
    // 0xaf7678: b.ne            #0xaf7684
    // 0xaf767c: r0 = Null
    //     0xaf767c: mov             x0, NULL
    // 0xaf7680: b               #0xaf768c
    // 0xaf7684: LoadField: r0 = r1->field_b
    //     0xaf7684: ldur            w0, [x1, #0xb]
    // 0xaf7688: DecompressPointer r0
    //     0xaf7688: add             x0, x0, HEAP, lsl #32
    // 0xaf768c: cmp             w0, NULL
    // 0xaf7690: b.ne            #0xaf769c
    // 0xaf7694: r0 = 0
    //     0xaf7694: movz            x0, #0
    // 0xaf7698: b               #0xaf76ac
    // 0xaf769c: r1 = LoadInt32Instr(r0)
    //     0xaf769c: sbfx            x1, x0, #1, #0x1f
    //     0xaf76a0: tbz             w0, #0, #0xaf76a8
    //     0xaf76a4: ldur            x1, [x0, #7]
    // 0xaf76a8: mov             x0, x1
    // 0xaf76ac: ldur            x2, [fp, #-0x10]
    // 0xaf76b0: stur            x0, [fp, #-0x28]
    // 0xaf76b4: LoadField: r1 = r2->field_3f
    //     0xaf76b4: ldur            w1, [x2, #0x3f]
    // 0xaf76b8: DecompressPointer r1
    //     0xaf76b8: add             x1, x1, HEAP, lsl #32
    // 0xaf76bc: LoadField: r3 = r1->field_57
    //     0xaf76bc: ldur            w3, [x1, #0x57]
    // 0xaf76c0: DecompressPointer r3
    //     0xaf76c0: add             x3, x3, HEAP, lsl #32
    // 0xaf76c4: mov             x1, x3
    // 0xaf76c8: r0 = value()
    //     0xaf76c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf76cc: LoadField: r1 = r0->field_b
    //     0xaf76cc: ldur            w1, [x0, #0xb]
    // 0xaf76d0: DecompressPointer r1
    //     0xaf76d0: add             x1, x1, HEAP, lsl #32
    // 0xaf76d4: cmp             w1, NULL
    // 0xaf76d8: b.ne            #0xaf76e4
    // 0xaf76dc: r0 = Null
    //     0xaf76dc: mov             x0, NULL
    // 0xaf76e0: b               #0xaf771c
    // 0xaf76e4: LoadField: r0 = r1->field_57
    //     0xaf76e4: ldur            w0, [x1, #0x57]
    // 0xaf76e8: DecompressPointer r0
    //     0xaf76e8: add             x0, x0, HEAP, lsl #32
    // 0xaf76ec: cmp             w0, NULL
    // 0xaf76f0: b.ne            #0xaf76fc
    // 0xaf76f4: r0 = Null
    //     0xaf76f4: mov             x0, NULL
    // 0xaf76f8: b               #0xaf771c
    // 0xaf76fc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf76fc: ldur            w1, [x0, #0x17]
    // 0xaf7700: DecompressPointer r1
    //     0xaf7700: add             x1, x1, HEAP, lsl #32
    // 0xaf7704: cmp             w1, NULL
    // 0xaf7708: b.ne            #0xaf7714
    // 0xaf770c: r0 = Null
    //     0xaf770c: mov             x0, NULL
    // 0xaf7710: b               #0xaf771c
    // 0xaf7714: LoadField: r0 = r1->field_f
    //     0xaf7714: ldur            w0, [x1, #0xf]
    // 0xaf7718: DecompressPointer r0
    //     0xaf7718: add             x0, x0, HEAP, lsl #32
    // 0xaf771c: cmp             w0, NULL
    // 0xaf7720: b.ne            #0xaf772c
    // 0xaf7724: r1 = 0
    //     0xaf7724: movz            x1, #0
    // 0xaf7728: b               #0xaf7738
    // 0xaf772c: r1 = LoadInt32Instr(r0)
    //     0xaf772c: sbfx            x1, x0, #1, #0x1f
    //     0xaf7730: tbz             w0, #0, #0xaf7738
    //     0xaf7734: ldur            x1, [x0, #7]
    // 0xaf7738: ldur            x2, [fp, #-0x10]
    // 0xaf773c: ldur            x0, [fp, #-0x48]
    // 0xaf7740: stur            x1, [fp, #-0x30]
    // 0xaf7744: r0 = Color()
    //     0xaf7744: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaf7748: mov             x4, x0
    // 0xaf774c: r3 = Instance_ColorSpace
    //     0xaf774c: ldr             x3, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaf7750: stur            x4, [fp, #-0x38]
    // 0xaf7754: StoreField: r4->field_27 = r3
    //     0xaf7754: stur            w3, [x4, #0x27]
    // 0xaf7758: d0 = 1.000000
    //     0xaf7758: fmov            d0, #1.00000000
    // 0xaf775c: StoreField: r4->field_7 = d0
    //     0xaf775c: stur            d0, [x4, #7]
    // 0xaf7760: ldur            x0, [fp, #-0x20]
    // 0xaf7764: ubfx            x0, x0, #0, #0x20
    // 0xaf7768: and             w1, w0, #0xff
    // 0xaf776c: ubfx            x1, x1, #0, #0x20
    // 0xaf7770: scvtf           d1, x1
    // 0xaf7774: d2 = 255.000000
    //     0xaf7774: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaf7778: fdiv            d3, d1, d2
    // 0xaf777c: StoreField: r4->field_f = d3
    //     0xaf777c: stur            d3, [x4, #0xf]
    // 0xaf7780: ldur            x0, [fp, #-0x28]
    // 0xaf7784: ubfx            x0, x0, #0, #0x20
    // 0xaf7788: and             w1, w0, #0xff
    // 0xaf778c: ubfx            x1, x1, #0, #0x20
    // 0xaf7790: scvtf           d1, x1
    // 0xaf7794: fdiv            d3, d1, d2
    // 0xaf7798: ArrayStore: r4[0] = d3  ; List_8
    //     0xaf7798: stur            d3, [x4, #0x17]
    // 0xaf779c: ldur            x0, [fp, #-0x30]
    // 0xaf77a0: ubfx            x0, x0, #0, #0x20
    // 0xaf77a4: and             w1, w0, #0xff
    // 0xaf77a8: ubfx            x1, x1, #0, #0x20
    // 0xaf77ac: scvtf           d1, x1
    // 0xaf77b0: fdiv            d3, d1, d2
    // 0xaf77b4: StoreField: r4->field_1f = d3
    //     0xaf77b4: stur            d3, [x4, #0x1f]
    // 0xaf77b8: ldur            x5, [fp, #-0x48]
    // 0xaf77bc: LoadField: r0 = r5->field_b
    //     0xaf77bc: ldur            w0, [x5, #0xb]
    // 0xaf77c0: r1 = LoadInt32Instr(r0)
    //     0xaf77c0: sbfx            x1, x0, #1, #0x1f
    // 0xaf77c4: mov             x0, x1
    // 0xaf77c8: r1 = 1
    //     0xaf77c8: movz            x1, #0x1
    // 0xaf77cc: cmp             x1, x0
    // 0xaf77d0: b.hs            #0xaf8290
    // 0xaf77d4: LoadField: r0 = r5->field_f
    //     0xaf77d4: ldur            w0, [x5, #0xf]
    // 0xaf77d8: DecompressPointer r0
    //     0xaf77d8: add             x0, x0, HEAP, lsl #32
    // 0xaf77dc: LoadField: r6 = r0->field_13
    //     0xaf77dc: ldur            w6, [x0, #0x13]
    // 0xaf77e0: DecompressPointer r6
    //     0xaf77e0: add             x6, x6, HEAP, lsl #32
    // 0xaf77e4: mov             x0, x6
    // 0xaf77e8: stur            x6, [fp, #-0x18]
    // 0xaf77ec: r2 = Null
    //     0xaf77ec: mov             x2, NULL
    // 0xaf77f0: r1 = Null
    //     0xaf77f0: mov             x1, NULL
    // 0xaf77f4: r4 = 60
    //     0xaf77f4: movz            x4, #0x3c
    // 0xaf77f8: branchIfSmi(r0, 0xaf7804)
    //     0xaf77f8: tbz             w0, #0, #0xaf7804
    // 0xaf77fc: r4 = LoadClassIdInstr(r0)
    //     0xaf77fc: ldur            x4, [x0, #-1]
    //     0xaf7800: ubfx            x4, x4, #0xc, #0x14
    // 0xaf7804: r17 = 4596
    //     0xaf7804: movz            x17, #0x11f4
    // 0xaf7808: cmp             x4, x17
    // 0xaf780c: b.eq            #0xaf7824
    // 0xaf7810: r8 = OrdersView
    //     0xaf7810: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0xaf7814: ldr             x8, [x8, #0xcb0]
    // 0xaf7818: r3 = Null
    //     0xaf7818: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dcd8] Null
    //     0xaf781c: ldr             x3, [x3, #0xcd8]
    // 0xaf7820: r0 = DefaultTypeTest()
    //     0xaf7820: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xaf7824: ldur            x1, [fp, #-0x18]
    // 0xaf7828: r0 = controller()
    //     0xaf7828: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf782c: LoadField: r1 = r0->field_5b
    //     0xaf782c: ldur            w1, [x0, #0x5b]
    // 0xaf7830: DecompressPointer r1
    //     0xaf7830: add             x1, x1, HEAP, lsl #32
    // 0xaf7834: r0 = value()
    //     0xaf7834: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7838: ldur            x2, [fp, #-0x48]
    // 0xaf783c: LoadField: r0 = r2->field_b
    //     0xaf783c: ldur            w0, [x2, #0xb]
    // 0xaf7840: r1 = LoadInt32Instr(r0)
    //     0xaf7840: sbfx            x1, x0, #1, #0x1f
    // 0xaf7844: mov             x0, x1
    // 0xaf7848: r1 = 1
    //     0xaf7848: movz            x1, #0x1
    // 0xaf784c: cmp             x1, x0
    // 0xaf7850: b.hs            #0xaf8294
    // 0xaf7854: LoadField: r0 = r2->field_f
    //     0xaf7854: ldur            w0, [x2, #0xf]
    // 0xaf7858: DecompressPointer r0
    //     0xaf7858: add             x0, x0, HEAP, lsl #32
    // 0xaf785c: LoadField: r3 = r0->field_13
    //     0xaf785c: ldur            w3, [x0, #0x13]
    // 0xaf7860: DecompressPointer r3
    //     0xaf7860: add             x3, x3, HEAP, lsl #32
    // 0xaf7864: mov             x0, x3
    // 0xaf7868: stur            x3, [fp, #-0x18]
    // 0xaf786c: r2 = Null
    //     0xaf786c: mov             x2, NULL
    // 0xaf7870: r1 = Null
    //     0xaf7870: mov             x1, NULL
    // 0xaf7874: r4 = 60
    //     0xaf7874: movz            x4, #0x3c
    // 0xaf7878: branchIfSmi(r0, 0xaf7884)
    //     0xaf7878: tbz             w0, #0, #0xaf7884
    // 0xaf787c: r4 = LoadClassIdInstr(r0)
    //     0xaf787c: ldur            x4, [x0, #-1]
    //     0xaf7880: ubfx            x4, x4, #0xc, #0x14
    // 0xaf7884: r17 = 4596
    //     0xaf7884: movz            x17, #0x11f4
    // 0xaf7888: cmp             x4, x17
    // 0xaf788c: b.eq            #0xaf78a4
    // 0xaf7890: r8 = OrdersView
    //     0xaf7890: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dcb0] Type: OrdersView
    //     0xaf7894: ldr             x8, [x8, #0xcb0]
    // 0xaf7898: r3 = Null
    //     0xaf7898: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2dce8] Null
    //     0xaf789c: ldr             x3, [x3, #0xce8]
    // 0xaf78a0: r0 = DefaultTypeTest()
    //     0xaf78a0: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xaf78a4: ldur            x1, [fp, #-0x18]
    // 0xaf78a8: r0 = controller()
    //     0xaf78a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xaf78ac: LoadField: r1 = r0->field_5b
    //     0xaf78ac: ldur            w1, [x0, #0x5b]
    // 0xaf78b0: DecompressPointer r1
    //     0xaf78b0: add             x1, x1, HEAP, lsl #32
    // 0xaf78b4: r0 = value()
    //     0xaf78b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf78b8: ldur            x2, [fp, #-0x10]
    // 0xaf78bc: LoadField: r1 = r2->field_f
    //     0xaf78bc: ldur            w1, [x2, #0xf]
    // 0xaf78c0: DecompressPointer r1
    //     0xaf78c0: add             x1, x1, HEAP, lsl #32
    // 0xaf78c4: cmp             w1, NULL
    // 0xaf78c8: b.eq            #0xaf8298
    // 0xaf78cc: r0 = of()
    //     0xaf78cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf78d0: LoadField: r1 = r0->field_87
    //     0xaf78d0: ldur            w1, [x0, #0x87]
    // 0xaf78d4: DecompressPointer r1
    //     0xaf78d4: add             x1, x1, HEAP, lsl #32
    // 0xaf78d8: LoadField: r0 = r1->field_27
    //     0xaf78d8: ldur            w0, [x1, #0x27]
    // 0xaf78dc: DecompressPointer r0
    //     0xaf78dc: add             x0, x0, HEAP, lsl #32
    // 0xaf78e0: r16 = Instance_Color
    //     0xaf78e0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf78e4: str             x16, [SP]
    // 0xaf78e8: mov             x1, x0
    // 0xaf78ec: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xaf78ec: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xaf78f0: ldr             x4, [x4, #0xf40]
    // 0xaf78f4: r0 = copyWith()
    //     0xaf78f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf78f8: stur            x0, [fp, #-0x18]
    // 0xaf78fc: r0 = Text()
    //     0xaf78fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf7900: mov             x2, x0
    // 0xaf7904: r0 = ""
    //     0xaf7904: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf7908: stur            x2, [fp, #-0x48]
    // 0xaf790c: StoreField: r2->field_b = r0
    //     0xaf790c: stur            w0, [x2, #0xb]
    // 0xaf7910: ldur            x0, [fp, #-0x18]
    // 0xaf7914: StoreField: r2->field_13 = r0
    //     0xaf7914: stur            w0, [x2, #0x13]
    // 0xaf7918: ldur            x0, [fp, #-0x10]
    // 0xaf791c: LoadField: r1 = r0->field_3f
    //     0xaf791c: ldur            w1, [x0, #0x3f]
    // 0xaf7920: DecompressPointer r1
    //     0xaf7920: add             x1, x1, HEAP, lsl #32
    // 0xaf7924: LoadField: r3 = r1->field_57
    //     0xaf7924: ldur            w3, [x1, #0x57]
    // 0xaf7928: DecompressPointer r3
    //     0xaf7928: add             x3, x3, HEAP, lsl #32
    // 0xaf792c: mov             x1, x3
    // 0xaf7930: r0 = value()
    //     0xaf7930: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7934: LoadField: r1 = r0->field_b
    //     0xaf7934: ldur            w1, [x0, #0xb]
    // 0xaf7938: DecompressPointer r1
    //     0xaf7938: add             x1, x1, HEAP, lsl #32
    // 0xaf793c: cmp             w1, NULL
    // 0xaf7940: b.ne            #0xaf794c
    // 0xaf7944: r0 = Null
    //     0xaf7944: mov             x0, NULL
    // 0xaf7948: b               #0xaf7984
    // 0xaf794c: LoadField: r0 = r1->field_57
    //     0xaf794c: ldur            w0, [x1, #0x57]
    // 0xaf7950: DecompressPointer r0
    //     0xaf7950: add             x0, x0, HEAP, lsl #32
    // 0xaf7954: cmp             w0, NULL
    // 0xaf7958: b.ne            #0xaf7964
    // 0xaf795c: r0 = Null
    //     0xaf795c: mov             x0, NULL
    // 0xaf7960: b               #0xaf7984
    // 0xaf7964: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7964: ldur            w1, [x0, #0x17]
    // 0xaf7968: DecompressPointer r1
    //     0xaf7968: add             x1, x1, HEAP, lsl #32
    // 0xaf796c: cmp             w1, NULL
    // 0xaf7970: b.ne            #0xaf797c
    // 0xaf7974: r0 = Null
    //     0xaf7974: mov             x0, NULL
    // 0xaf7978: b               #0xaf7984
    // 0xaf797c: LoadField: r0 = r1->field_7
    //     0xaf797c: ldur            w0, [x1, #7]
    // 0xaf7980: DecompressPointer r0
    //     0xaf7980: add             x0, x0, HEAP, lsl #32
    // 0xaf7984: cmp             w0, NULL
    // 0xaf7988: b.ne            #0xaf7994
    // 0xaf798c: r0 = 0
    //     0xaf798c: movz            x0, #0
    // 0xaf7990: b               #0xaf79a4
    // 0xaf7994: r1 = LoadInt32Instr(r0)
    //     0xaf7994: sbfx            x1, x0, #1, #0x1f
    //     0xaf7998: tbz             w0, #0, #0xaf79a0
    //     0xaf799c: ldur            x1, [x0, #7]
    // 0xaf79a0: mov             x0, x1
    // 0xaf79a4: ldur            x2, [fp, #-0x10]
    // 0xaf79a8: stur            x0, [fp, #-0x20]
    // 0xaf79ac: LoadField: r1 = r2->field_3f
    //     0xaf79ac: ldur            w1, [x2, #0x3f]
    // 0xaf79b0: DecompressPointer r1
    //     0xaf79b0: add             x1, x1, HEAP, lsl #32
    // 0xaf79b4: LoadField: r3 = r1->field_57
    //     0xaf79b4: ldur            w3, [x1, #0x57]
    // 0xaf79b8: DecompressPointer r3
    //     0xaf79b8: add             x3, x3, HEAP, lsl #32
    // 0xaf79bc: mov             x1, x3
    // 0xaf79c0: r0 = value()
    //     0xaf79c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf79c4: LoadField: r1 = r0->field_b
    //     0xaf79c4: ldur            w1, [x0, #0xb]
    // 0xaf79c8: DecompressPointer r1
    //     0xaf79c8: add             x1, x1, HEAP, lsl #32
    // 0xaf79cc: cmp             w1, NULL
    // 0xaf79d0: b.ne            #0xaf79dc
    // 0xaf79d4: r0 = Null
    //     0xaf79d4: mov             x0, NULL
    // 0xaf79d8: b               #0xaf7a14
    // 0xaf79dc: LoadField: r0 = r1->field_57
    //     0xaf79dc: ldur            w0, [x1, #0x57]
    // 0xaf79e0: DecompressPointer r0
    //     0xaf79e0: add             x0, x0, HEAP, lsl #32
    // 0xaf79e4: cmp             w0, NULL
    // 0xaf79e8: b.ne            #0xaf79f4
    // 0xaf79ec: r0 = Null
    //     0xaf79ec: mov             x0, NULL
    // 0xaf79f0: b               #0xaf7a14
    // 0xaf79f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf79f4: ldur            w1, [x0, #0x17]
    // 0xaf79f8: DecompressPointer r1
    //     0xaf79f8: add             x1, x1, HEAP, lsl #32
    // 0xaf79fc: cmp             w1, NULL
    // 0xaf7a00: b.ne            #0xaf7a0c
    // 0xaf7a04: r0 = Null
    //     0xaf7a04: mov             x0, NULL
    // 0xaf7a08: b               #0xaf7a14
    // 0xaf7a0c: LoadField: r0 = r1->field_b
    //     0xaf7a0c: ldur            w0, [x1, #0xb]
    // 0xaf7a10: DecompressPointer r0
    //     0xaf7a10: add             x0, x0, HEAP, lsl #32
    // 0xaf7a14: cmp             w0, NULL
    // 0xaf7a18: b.ne            #0xaf7a24
    // 0xaf7a1c: r0 = 0
    //     0xaf7a1c: movz            x0, #0
    // 0xaf7a20: b               #0xaf7a34
    // 0xaf7a24: r1 = LoadInt32Instr(r0)
    //     0xaf7a24: sbfx            x1, x0, #1, #0x1f
    //     0xaf7a28: tbz             w0, #0, #0xaf7a30
    //     0xaf7a2c: ldur            x1, [x0, #7]
    // 0xaf7a30: mov             x0, x1
    // 0xaf7a34: ldur            x2, [fp, #-0x10]
    // 0xaf7a38: stur            x0, [fp, #-0x28]
    // 0xaf7a3c: LoadField: r1 = r2->field_3f
    //     0xaf7a3c: ldur            w1, [x2, #0x3f]
    // 0xaf7a40: DecompressPointer r1
    //     0xaf7a40: add             x1, x1, HEAP, lsl #32
    // 0xaf7a44: LoadField: r3 = r1->field_57
    //     0xaf7a44: ldur            w3, [x1, #0x57]
    // 0xaf7a48: DecompressPointer r3
    //     0xaf7a48: add             x3, x3, HEAP, lsl #32
    // 0xaf7a4c: mov             x1, x3
    // 0xaf7a50: r0 = value()
    //     0xaf7a50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7a54: LoadField: r1 = r0->field_b
    //     0xaf7a54: ldur            w1, [x0, #0xb]
    // 0xaf7a58: DecompressPointer r1
    //     0xaf7a58: add             x1, x1, HEAP, lsl #32
    // 0xaf7a5c: cmp             w1, NULL
    // 0xaf7a60: b.ne            #0xaf7a6c
    // 0xaf7a64: r0 = Null
    //     0xaf7a64: mov             x0, NULL
    // 0xaf7a68: b               #0xaf7aa4
    // 0xaf7a6c: LoadField: r0 = r1->field_57
    //     0xaf7a6c: ldur            w0, [x1, #0x57]
    // 0xaf7a70: DecompressPointer r0
    //     0xaf7a70: add             x0, x0, HEAP, lsl #32
    // 0xaf7a74: cmp             w0, NULL
    // 0xaf7a78: b.ne            #0xaf7a84
    // 0xaf7a7c: r0 = Null
    //     0xaf7a7c: mov             x0, NULL
    // 0xaf7a80: b               #0xaf7aa4
    // 0xaf7a84: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7a84: ldur            w1, [x0, #0x17]
    // 0xaf7a88: DecompressPointer r1
    //     0xaf7a88: add             x1, x1, HEAP, lsl #32
    // 0xaf7a8c: cmp             w1, NULL
    // 0xaf7a90: b.ne            #0xaf7a9c
    // 0xaf7a94: r0 = Null
    //     0xaf7a94: mov             x0, NULL
    // 0xaf7a98: b               #0xaf7aa4
    // 0xaf7a9c: LoadField: r0 = r1->field_f
    //     0xaf7a9c: ldur            w0, [x1, #0xf]
    // 0xaf7aa0: DecompressPointer r0
    //     0xaf7aa0: add             x0, x0, HEAP, lsl #32
    // 0xaf7aa4: cmp             w0, NULL
    // 0xaf7aa8: b.ne            #0xaf7ab4
    // 0xaf7aac: r4 = 0
    //     0xaf7aac: movz            x4, #0
    // 0xaf7ab0: b               #0xaf7ac4
    // 0xaf7ab4: r1 = LoadInt32Instr(r0)
    //     0xaf7ab4: sbfx            x1, x0, #1, #0x1f
    //     0xaf7ab8: tbz             w0, #0, #0xaf7ac0
    //     0xaf7abc: ldur            x1, [x0, #7]
    // 0xaf7ac0: mov             x4, x1
    // 0xaf7ac4: ldur            x2, [fp, #-0x10]
    // 0xaf7ac8: ldur            x3, [fp, #-0x58]
    // 0xaf7acc: ldur            x1, [fp, #-0x38]
    // 0xaf7ad0: ldur            x0, [fp, #-0x48]
    // 0xaf7ad4: stur            x4, [fp, #-0x30]
    // 0xaf7ad8: r0 = Color()
    //     0xaf7ad8: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaf7adc: mov             x1, x0
    // 0xaf7ae0: r0 = Instance_ColorSpace
    //     0xaf7ae0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaf7ae4: stur            x1, [fp, #-0x18]
    // 0xaf7ae8: StoreField: r1->field_27 = r0
    //     0xaf7ae8: stur            w0, [x1, #0x27]
    // 0xaf7aec: d0 = 1.000000
    //     0xaf7aec: fmov            d0, #1.00000000
    // 0xaf7af0: StoreField: r1->field_7 = d0
    //     0xaf7af0: stur            d0, [x1, #7]
    // 0xaf7af4: ldur            x2, [fp, #-0x20]
    // 0xaf7af8: ubfx            x2, x2, #0, #0x20
    // 0xaf7afc: and             w3, w2, #0xff
    // 0xaf7b00: ubfx            x3, x3, #0, #0x20
    // 0xaf7b04: scvtf           d1, x3
    // 0xaf7b08: d2 = 255.000000
    //     0xaf7b08: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaf7b0c: fdiv            d3, d1, d2
    // 0xaf7b10: StoreField: r1->field_f = d3
    //     0xaf7b10: stur            d3, [x1, #0xf]
    // 0xaf7b14: ldur            x2, [fp, #-0x28]
    // 0xaf7b18: ubfx            x2, x2, #0, #0x20
    // 0xaf7b1c: and             w3, w2, #0xff
    // 0xaf7b20: ubfx            x3, x3, #0, #0x20
    // 0xaf7b24: scvtf           d1, x3
    // 0xaf7b28: fdiv            d3, d1, d2
    // 0xaf7b2c: ArrayStore: r1[0] = d3  ; List_8
    //     0xaf7b2c: stur            d3, [x1, #0x17]
    // 0xaf7b30: ldur            x2, [fp, #-0x30]
    // 0xaf7b34: ubfx            x2, x2, #0, #0x20
    // 0xaf7b38: and             w3, w2, #0xff
    // 0xaf7b3c: ubfx            x3, x3, #0, #0x20
    // 0xaf7b40: scvtf           d1, x3
    // 0xaf7b44: fdiv            d3, d1, d2
    // 0xaf7b48: StoreField: r1->field_1f = d3
    //     0xaf7b48: stur            d3, [x1, #0x1f]
    // 0xaf7b4c: r0 = ColorFilter()
    //     0xaf7b4c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xaf7b50: mov             x1, x0
    // 0xaf7b54: ldur            x0, [fp, #-0x18]
    // 0xaf7b58: stur            x1, [fp, #-0x50]
    // 0xaf7b5c: StoreField: r1->field_7 = r0
    //     0xaf7b5c: stur            w0, [x1, #7]
    // 0xaf7b60: r0 = Instance_BlendMode
    //     0xaf7b60: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xaf7b64: ldr             x0, [x0, #0xb30]
    // 0xaf7b68: StoreField: r1->field_b = r0
    //     0xaf7b68: stur            w0, [x1, #0xb]
    // 0xaf7b6c: r2 = 1
    //     0xaf7b6c: movz            x2, #0x1
    // 0xaf7b70: StoreField: r1->field_13 = r2
    //     0xaf7b70: stur            x2, [x1, #0x13]
    // 0xaf7b74: r0 = SvgPicture()
    //     0xaf7b74: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf7b78: stur            x0, [fp, #-0x18]
    // 0xaf7b7c: r16 = "user"
    //     0xaf7b7c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb70] "user"
    //     0xaf7b80: ldr             x16, [x16, #0xb70]
    // 0xaf7b84: r30 = Instance_BoxFit
    //     0xaf7b84: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf7b88: ldr             lr, [lr, #0xb18]
    // 0xaf7b8c: stp             lr, x16, [SP, #8]
    // 0xaf7b90: ldur            x16, [fp, #-0x50]
    // 0xaf7b94: str             x16, [SP]
    // 0xaf7b98: mov             x1, x0
    // 0xaf7b9c: r2 = "assets/images/user.svg"
    //     0xaf7b9c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb78] "assets/images/user.svg"
    //     0xaf7ba0: ldr             x2, [x2, #0xb78]
    // 0xaf7ba4: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf7ba4: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf7ba8: ldr             x4, [x4, #0xb38]
    // 0xaf7bac: r0 = SvgPicture.asset()
    //     0xaf7bac: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf7bb0: r0 = Badge()
    //     0xaf7bb0: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0xaf7bb4: mov             x1, x0
    // 0xaf7bb8: ldur            x0, [fp, #-0x38]
    // 0xaf7bbc: stur            x1, [fp, #-0x50]
    // 0xaf7bc0: StoreField: r1->field_b = r0
    //     0xaf7bc0: stur            w0, [x1, #0xb]
    // 0xaf7bc4: ldur            x0, [fp, #-0x48]
    // 0xaf7bc8: StoreField: r1->field_27 = r0
    //     0xaf7bc8: stur            w0, [x1, #0x27]
    // 0xaf7bcc: r0 = false
    //     0xaf7bcc: add             x0, NULL, #0x30  ; false
    // 0xaf7bd0: StoreField: r1->field_2b = r0
    //     0xaf7bd0: stur            w0, [x1, #0x2b]
    // 0xaf7bd4: ldur            x0, [fp, #-0x18]
    // 0xaf7bd8: StoreField: r1->field_2f = r0
    //     0xaf7bd8: stur            w0, [x1, #0x2f]
    // 0xaf7bdc: r0 = BottomNavigationBarItem()
    //     0xaf7bdc: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xaf7be0: mov             x1, x0
    // 0xaf7be4: ldur            x0, [fp, #-0x58]
    // 0xaf7be8: stur            x1, [fp, #-0x18]
    // 0xaf7bec: StoreField: r1->field_b = r0
    //     0xaf7bec: stur            w0, [x1, #0xb]
    // 0xaf7bf0: r0 = "Order"
    //     0xaf7bf0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cba0] "Order"
    //     0xaf7bf4: ldr             x0, [x0, #0xba0]
    // 0xaf7bf8: StoreField: r1->field_13 = r0
    //     0xaf7bf8: stur            w0, [x1, #0x13]
    // 0xaf7bfc: r0 = "1"
    //     0xaf7bfc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xaf7c00: ldr             x0, [x0, #0xba8]
    // 0xaf7c04: StoreField: r1->field_1b = r0
    //     0xaf7c04: stur            w0, [x1, #0x1b]
    // 0xaf7c08: ldur            x0, [fp, #-0x50]
    // 0xaf7c0c: StoreField: r1->field_f = r0
    //     0xaf7c0c: stur            w0, [x1, #0xf]
    // 0xaf7c10: r0 = SvgPicture()
    //     0xaf7c10: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf7c14: stur            x0, [fp, #-0x38]
    // 0xaf7c18: r16 = "profile"
    //     0xaf7c18: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbb0] "profile"
    //     0xaf7c1c: ldr             x16, [x16, #0xbb0]
    // 0xaf7c20: r30 = Instance_BoxFit
    //     0xaf7c20: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf7c24: ldr             lr, [lr, #0xb18]
    // 0xaf7c28: stp             lr, x16, [SP]
    // 0xaf7c2c: mov             x1, x0
    // 0xaf7c30: r2 = "assets/images/profile_circle.svg"
    //     0xaf7c30: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbb8] "assets/images/profile_circle.svg"
    //     0xaf7c34: ldr             x2, [x2, #0xbb8]
    // 0xaf7c38: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf7c38: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf7c3c: ldr             x4, [x4, #0xb28]
    // 0xaf7c40: r0 = SvgPicture.asset()
    //     0xaf7c40: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf7c44: ldur            x2, [fp, #-0x10]
    // 0xaf7c48: LoadField: r0 = r2->field_3f
    //     0xaf7c48: ldur            w0, [x2, #0x3f]
    // 0xaf7c4c: DecompressPointer r0
    //     0xaf7c4c: add             x0, x0, HEAP, lsl #32
    // 0xaf7c50: LoadField: r1 = r0->field_57
    //     0xaf7c50: ldur            w1, [x0, #0x57]
    // 0xaf7c54: DecompressPointer r1
    //     0xaf7c54: add             x1, x1, HEAP, lsl #32
    // 0xaf7c58: r0 = value()
    //     0xaf7c58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7c5c: LoadField: r1 = r0->field_b
    //     0xaf7c5c: ldur            w1, [x0, #0xb]
    // 0xaf7c60: DecompressPointer r1
    //     0xaf7c60: add             x1, x1, HEAP, lsl #32
    // 0xaf7c64: cmp             w1, NULL
    // 0xaf7c68: b.ne            #0xaf7c74
    // 0xaf7c6c: r0 = Null
    //     0xaf7c6c: mov             x0, NULL
    // 0xaf7c70: b               #0xaf7cac
    // 0xaf7c74: LoadField: r0 = r1->field_57
    //     0xaf7c74: ldur            w0, [x1, #0x57]
    // 0xaf7c78: DecompressPointer r0
    //     0xaf7c78: add             x0, x0, HEAP, lsl #32
    // 0xaf7c7c: cmp             w0, NULL
    // 0xaf7c80: b.ne            #0xaf7c8c
    // 0xaf7c84: r0 = Null
    //     0xaf7c84: mov             x0, NULL
    // 0xaf7c88: b               #0xaf7cac
    // 0xaf7c8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7c8c: ldur            w1, [x0, #0x17]
    // 0xaf7c90: DecompressPointer r1
    //     0xaf7c90: add             x1, x1, HEAP, lsl #32
    // 0xaf7c94: cmp             w1, NULL
    // 0xaf7c98: b.ne            #0xaf7ca4
    // 0xaf7c9c: r0 = Null
    //     0xaf7c9c: mov             x0, NULL
    // 0xaf7ca0: b               #0xaf7cac
    // 0xaf7ca4: LoadField: r0 = r1->field_7
    //     0xaf7ca4: ldur            w0, [x1, #7]
    // 0xaf7ca8: DecompressPointer r0
    //     0xaf7ca8: add             x0, x0, HEAP, lsl #32
    // 0xaf7cac: cmp             w0, NULL
    // 0xaf7cb0: b.ne            #0xaf7cbc
    // 0xaf7cb4: r0 = 0
    //     0xaf7cb4: movz            x0, #0
    // 0xaf7cb8: b               #0xaf7ccc
    // 0xaf7cbc: r1 = LoadInt32Instr(r0)
    //     0xaf7cbc: sbfx            x1, x0, #1, #0x1f
    //     0xaf7cc0: tbz             w0, #0, #0xaf7cc8
    //     0xaf7cc4: ldur            x1, [x0, #7]
    // 0xaf7cc8: mov             x0, x1
    // 0xaf7ccc: ldur            x2, [fp, #-0x10]
    // 0xaf7cd0: stur            x0, [fp, #-0x20]
    // 0xaf7cd4: LoadField: r1 = r2->field_3f
    //     0xaf7cd4: ldur            w1, [x2, #0x3f]
    // 0xaf7cd8: DecompressPointer r1
    //     0xaf7cd8: add             x1, x1, HEAP, lsl #32
    // 0xaf7cdc: LoadField: r3 = r1->field_57
    //     0xaf7cdc: ldur            w3, [x1, #0x57]
    // 0xaf7ce0: DecompressPointer r3
    //     0xaf7ce0: add             x3, x3, HEAP, lsl #32
    // 0xaf7ce4: mov             x1, x3
    // 0xaf7ce8: r0 = value()
    //     0xaf7ce8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7cec: LoadField: r1 = r0->field_b
    //     0xaf7cec: ldur            w1, [x0, #0xb]
    // 0xaf7cf0: DecompressPointer r1
    //     0xaf7cf0: add             x1, x1, HEAP, lsl #32
    // 0xaf7cf4: cmp             w1, NULL
    // 0xaf7cf8: b.ne            #0xaf7d04
    // 0xaf7cfc: r0 = Null
    //     0xaf7cfc: mov             x0, NULL
    // 0xaf7d00: b               #0xaf7d3c
    // 0xaf7d04: LoadField: r0 = r1->field_57
    //     0xaf7d04: ldur            w0, [x1, #0x57]
    // 0xaf7d08: DecompressPointer r0
    //     0xaf7d08: add             x0, x0, HEAP, lsl #32
    // 0xaf7d0c: cmp             w0, NULL
    // 0xaf7d10: b.ne            #0xaf7d1c
    // 0xaf7d14: r0 = Null
    //     0xaf7d14: mov             x0, NULL
    // 0xaf7d18: b               #0xaf7d3c
    // 0xaf7d1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7d1c: ldur            w1, [x0, #0x17]
    // 0xaf7d20: DecompressPointer r1
    //     0xaf7d20: add             x1, x1, HEAP, lsl #32
    // 0xaf7d24: cmp             w1, NULL
    // 0xaf7d28: b.ne            #0xaf7d34
    // 0xaf7d2c: r0 = Null
    //     0xaf7d2c: mov             x0, NULL
    // 0xaf7d30: b               #0xaf7d3c
    // 0xaf7d34: LoadField: r0 = r1->field_b
    //     0xaf7d34: ldur            w0, [x1, #0xb]
    // 0xaf7d38: DecompressPointer r0
    //     0xaf7d38: add             x0, x0, HEAP, lsl #32
    // 0xaf7d3c: cmp             w0, NULL
    // 0xaf7d40: b.ne            #0xaf7d4c
    // 0xaf7d44: r0 = 0
    //     0xaf7d44: movz            x0, #0
    // 0xaf7d48: b               #0xaf7d5c
    // 0xaf7d4c: r1 = LoadInt32Instr(r0)
    //     0xaf7d4c: sbfx            x1, x0, #1, #0x1f
    //     0xaf7d50: tbz             w0, #0, #0xaf7d58
    //     0xaf7d54: ldur            x1, [x0, #7]
    // 0xaf7d58: mov             x0, x1
    // 0xaf7d5c: ldur            x2, [fp, #-0x10]
    // 0xaf7d60: stur            x0, [fp, #-0x28]
    // 0xaf7d64: LoadField: r1 = r2->field_3f
    //     0xaf7d64: ldur            w1, [x2, #0x3f]
    // 0xaf7d68: DecompressPointer r1
    //     0xaf7d68: add             x1, x1, HEAP, lsl #32
    // 0xaf7d6c: LoadField: r3 = r1->field_57
    //     0xaf7d6c: ldur            w3, [x1, #0x57]
    // 0xaf7d70: DecompressPointer r3
    //     0xaf7d70: add             x3, x3, HEAP, lsl #32
    // 0xaf7d74: mov             x1, x3
    // 0xaf7d78: r0 = value()
    //     0xaf7d78: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7d7c: LoadField: r1 = r0->field_b
    //     0xaf7d7c: ldur            w1, [x0, #0xb]
    // 0xaf7d80: DecompressPointer r1
    //     0xaf7d80: add             x1, x1, HEAP, lsl #32
    // 0xaf7d84: cmp             w1, NULL
    // 0xaf7d88: b.ne            #0xaf7d94
    // 0xaf7d8c: r0 = Null
    //     0xaf7d8c: mov             x0, NULL
    // 0xaf7d90: b               #0xaf7dcc
    // 0xaf7d94: LoadField: r0 = r1->field_57
    //     0xaf7d94: ldur            w0, [x1, #0x57]
    // 0xaf7d98: DecompressPointer r0
    //     0xaf7d98: add             x0, x0, HEAP, lsl #32
    // 0xaf7d9c: cmp             w0, NULL
    // 0xaf7da0: b.ne            #0xaf7dac
    // 0xaf7da4: r0 = Null
    //     0xaf7da4: mov             x0, NULL
    // 0xaf7da8: b               #0xaf7dcc
    // 0xaf7dac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7dac: ldur            w1, [x0, #0x17]
    // 0xaf7db0: DecompressPointer r1
    //     0xaf7db0: add             x1, x1, HEAP, lsl #32
    // 0xaf7db4: cmp             w1, NULL
    // 0xaf7db8: b.ne            #0xaf7dc4
    // 0xaf7dbc: r0 = Null
    //     0xaf7dbc: mov             x0, NULL
    // 0xaf7dc0: b               #0xaf7dcc
    // 0xaf7dc4: LoadField: r0 = r1->field_f
    //     0xaf7dc4: ldur            w0, [x1, #0xf]
    // 0xaf7dc8: DecompressPointer r0
    //     0xaf7dc8: add             x0, x0, HEAP, lsl #32
    // 0xaf7dcc: cmp             w0, NULL
    // 0xaf7dd0: b.ne            #0xaf7ddc
    // 0xaf7dd4: r1 = 0
    //     0xaf7dd4: movz            x1, #0
    // 0xaf7dd8: b               #0xaf7de8
    // 0xaf7ddc: r1 = LoadInt32Instr(r0)
    //     0xaf7ddc: sbfx            x1, x0, #1, #0x1f
    //     0xaf7de0: tbz             w0, #0, #0xaf7de8
    //     0xaf7de4: ldur            x1, [x0, #7]
    // 0xaf7de8: ldur            x2, [fp, #-0x10]
    // 0xaf7dec: ldur            x0, [fp, #-0x38]
    // 0xaf7df0: stur            x1, [fp, #-0x30]
    // 0xaf7df4: r0 = Color()
    //     0xaf7df4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaf7df8: mov             x1, x0
    // 0xaf7dfc: r0 = Instance_ColorSpace
    //     0xaf7dfc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaf7e00: stur            x1, [fp, #-0x48]
    // 0xaf7e04: StoreField: r1->field_27 = r0
    //     0xaf7e04: stur            w0, [x1, #0x27]
    // 0xaf7e08: d0 = 1.000000
    //     0xaf7e08: fmov            d0, #1.00000000
    // 0xaf7e0c: StoreField: r1->field_7 = d0
    //     0xaf7e0c: stur            d0, [x1, #7]
    // 0xaf7e10: ldur            x2, [fp, #-0x20]
    // 0xaf7e14: ubfx            x2, x2, #0, #0x20
    // 0xaf7e18: and             w3, w2, #0xff
    // 0xaf7e1c: ubfx            x3, x3, #0, #0x20
    // 0xaf7e20: scvtf           d1, x3
    // 0xaf7e24: d2 = 255.000000
    //     0xaf7e24: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaf7e28: fdiv            d3, d1, d2
    // 0xaf7e2c: StoreField: r1->field_f = d3
    //     0xaf7e2c: stur            d3, [x1, #0xf]
    // 0xaf7e30: ldur            x2, [fp, #-0x28]
    // 0xaf7e34: ubfx            x2, x2, #0, #0x20
    // 0xaf7e38: and             w3, w2, #0xff
    // 0xaf7e3c: ubfx            x3, x3, #0, #0x20
    // 0xaf7e40: scvtf           d1, x3
    // 0xaf7e44: fdiv            d3, d1, d2
    // 0xaf7e48: ArrayStore: r1[0] = d3  ; List_8
    //     0xaf7e48: stur            d3, [x1, #0x17]
    // 0xaf7e4c: ldur            x2, [fp, #-0x30]
    // 0xaf7e50: ubfx            x2, x2, #0, #0x20
    // 0xaf7e54: and             w3, w2, #0xff
    // 0xaf7e58: ubfx            x3, x3, #0, #0x20
    // 0xaf7e5c: scvtf           d1, x3
    // 0xaf7e60: fdiv            d3, d1, d2
    // 0xaf7e64: StoreField: r1->field_1f = d3
    //     0xaf7e64: stur            d3, [x1, #0x1f]
    // 0xaf7e68: r0 = ColorFilter()
    //     0xaf7e68: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xaf7e6c: mov             x1, x0
    // 0xaf7e70: ldur            x0, [fp, #-0x48]
    // 0xaf7e74: stur            x1, [fp, #-0x50]
    // 0xaf7e78: StoreField: r1->field_7 = r0
    //     0xaf7e78: stur            w0, [x1, #7]
    // 0xaf7e7c: r0 = Instance_BlendMode
    //     0xaf7e7c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xaf7e80: ldr             x0, [x0, #0xb30]
    // 0xaf7e84: StoreField: r1->field_b = r0
    //     0xaf7e84: stur            w0, [x1, #0xb]
    // 0xaf7e88: r2 = 1
    //     0xaf7e88: movz            x2, #0x1
    // 0xaf7e8c: StoreField: r1->field_13 = r2
    //     0xaf7e8c: stur            x2, [x1, #0x13]
    // 0xaf7e90: r0 = SvgPicture()
    //     0xaf7e90: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf7e94: stur            x0, [fp, #-0x48]
    // 0xaf7e98: r16 = "profile"
    //     0xaf7e98: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbb0] "profile"
    //     0xaf7e9c: ldr             x16, [x16, #0xbb0]
    // 0xaf7ea0: r30 = Instance_BoxFit
    //     0xaf7ea0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf7ea4: ldr             lr, [lr, #0xb18]
    // 0xaf7ea8: stp             lr, x16, [SP, #8]
    // 0xaf7eac: ldur            x16, [fp, #-0x50]
    // 0xaf7eb0: str             x16, [SP]
    // 0xaf7eb4: mov             x1, x0
    // 0xaf7eb8: r2 = "assets/images/profile_circle.svg"
    //     0xaf7eb8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbb8] "assets/images/profile_circle.svg"
    //     0xaf7ebc: ldr             x2, [x2, #0xbb8]
    // 0xaf7ec0: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf7ec0: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf7ec4: ldr             x4, [x4, #0xb38]
    // 0xaf7ec8: r0 = SvgPicture.asset()
    //     0xaf7ec8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf7ecc: r0 = BottomNavigationBarItem()
    //     0xaf7ecc: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xaf7ed0: mov             x1, x0
    // 0xaf7ed4: ldur            x0, [fp, #-0x38]
    // 0xaf7ed8: stur            x1, [fp, #-0x50]
    // 0xaf7edc: StoreField: r1->field_b = r0
    //     0xaf7edc: stur            w0, [x1, #0xb]
    // 0xaf7ee0: r0 = "Profile"
    //     0xaf7ee0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cbc0] "Profile"
    //     0xaf7ee4: ldr             x0, [x0, #0xbc0]
    // 0xaf7ee8: StoreField: r1->field_13 = r0
    //     0xaf7ee8: stur            w0, [x1, #0x13]
    // 0xaf7eec: ldur            x0, [fp, #-0x48]
    // 0xaf7ef0: StoreField: r1->field_f = r0
    //     0xaf7ef0: stur            w0, [x1, #0xf]
    // 0xaf7ef4: r0 = SvgPicture()
    //     0xaf7ef4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf7ef8: stur            x0, [fp, #-0x38]
    // 0xaf7efc: r16 = "menu"
    //     0xaf7efc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbc8] "menu"
    //     0xaf7f00: ldr             x16, [x16, #0xbc8]
    // 0xaf7f04: r30 = Instance_BoxFit
    //     0xaf7f04: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf7f08: ldr             lr, [lr, #0xb18]
    // 0xaf7f0c: stp             lr, x16, [SP]
    // 0xaf7f10: mov             x1, x0
    // 0xaf7f14: r2 = "assets/images/menu.svg"
    //     0xaf7f14: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbd0] "assets/images/menu.svg"
    //     0xaf7f18: ldr             x2, [x2, #0xbd0]
    // 0xaf7f1c: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf7f1c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf7f20: ldr             x4, [x4, #0xb28]
    // 0xaf7f24: r0 = SvgPicture.asset()
    //     0xaf7f24: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf7f28: ldur            x2, [fp, #-0x10]
    // 0xaf7f2c: LoadField: r0 = r2->field_3f
    //     0xaf7f2c: ldur            w0, [x2, #0x3f]
    // 0xaf7f30: DecompressPointer r0
    //     0xaf7f30: add             x0, x0, HEAP, lsl #32
    // 0xaf7f34: LoadField: r1 = r0->field_57
    //     0xaf7f34: ldur            w1, [x0, #0x57]
    // 0xaf7f38: DecompressPointer r1
    //     0xaf7f38: add             x1, x1, HEAP, lsl #32
    // 0xaf7f3c: r0 = value()
    //     0xaf7f3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7f40: LoadField: r1 = r0->field_b
    //     0xaf7f40: ldur            w1, [x0, #0xb]
    // 0xaf7f44: DecompressPointer r1
    //     0xaf7f44: add             x1, x1, HEAP, lsl #32
    // 0xaf7f48: cmp             w1, NULL
    // 0xaf7f4c: b.ne            #0xaf7f58
    // 0xaf7f50: r0 = Null
    //     0xaf7f50: mov             x0, NULL
    // 0xaf7f54: b               #0xaf7f90
    // 0xaf7f58: LoadField: r0 = r1->field_57
    //     0xaf7f58: ldur            w0, [x1, #0x57]
    // 0xaf7f5c: DecompressPointer r0
    //     0xaf7f5c: add             x0, x0, HEAP, lsl #32
    // 0xaf7f60: cmp             w0, NULL
    // 0xaf7f64: b.ne            #0xaf7f70
    // 0xaf7f68: r0 = Null
    //     0xaf7f68: mov             x0, NULL
    // 0xaf7f6c: b               #0xaf7f90
    // 0xaf7f70: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf7f70: ldur            w1, [x0, #0x17]
    // 0xaf7f74: DecompressPointer r1
    //     0xaf7f74: add             x1, x1, HEAP, lsl #32
    // 0xaf7f78: cmp             w1, NULL
    // 0xaf7f7c: b.ne            #0xaf7f88
    // 0xaf7f80: r0 = Null
    //     0xaf7f80: mov             x0, NULL
    // 0xaf7f84: b               #0xaf7f90
    // 0xaf7f88: LoadField: r0 = r1->field_7
    //     0xaf7f88: ldur            w0, [x1, #7]
    // 0xaf7f8c: DecompressPointer r0
    //     0xaf7f8c: add             x0, x0, HEAP, lsl #32
    // 0xaf7f90: cmp             w0, NULL
    // 0xaf7f94: b.ne            #0xaf7fa0
    // 0xaf7f98: r0 = 0
    //     0xaf7f98: movz            x0, #0
    // 0xaf7f9c: b               #0xaf7fb0
    // 0xaf7fa0: r1 = LoadInt32Instr(r0)
    //     0xaf7fa0: sbfx            x1, x0, #1, #0x1f
    //     0xaf7fa4: tbz             w0, #0, #0xaf7fac
    //     0xaf7fa8: ldur            x1, [x0, #7]
    // 0xaf7fac: mov             x0, x1
    // 0xaf7fb0: ldur            x2, [fp, #-0x10]
    // 0xaf7fb4: stur            x0, [fp, #-0x20]
    // 0xaf7fb8: LoadField: r1 = r2->field_3f
    //     0xaf7fb8: ldur            w1, [x2, #0x3f]
    // 0xaf7fbc: DecompressPointer r1
    //     0xaf7fbc: add             x1, x1, HEAP, lsl #32
    // 0xaf7fc0: LoadField: r3 = r1->field_57
    //     0xaf7fc0: ldur            w3, [x1, #0x57]
    // 0xaf7fc4: DecompressPointer r3
    //     0xaf7fc4: add             x3, x3, HEAP, lsl #32
    // 0xaf7fc8: mov             x1, x3
    // 0xaf7fcc: r0 = value()
    //     0xaf7fcc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf7fd0: LoadField: r1 = r0->field_b
    //     0xaf7fd0: ldur            w1, [x0, #0xb]
    // 0xaf7fd4: DecompressPointer r1
    //     0xaf7fd4: add             x1, x1, HEAP, lsl #32
    // 0xaf7fd8: cmp             w1, NULL
    // 0xaf7fdc: b.ne            #0xaf7fe8
    // 0xaf7fe0: r0 = Null
    //     0xaf7fe0: mov             x0, NULL
    // 0xaf7fe4: b               #0xaf8020
    // 0xaf7fe8: LoadField: r0 = r1->field_57
    //     0xaf7fe8: ldur            w0, [x1, #0x57]
    // 0xaf7fec: DecompressPointer r0
    //     0xaf7fec: add             x0, x0, HEAP, lsl #32
    // 0xaf7ff0: cmp             w0, NULL
    // 0xaf7ff4: b.ne            #0xaf8000
    // 0xaf7ff8: r0 = Null
    //     0xaf7ff8: mov             x0, NULL
    // 0xaf7ffc: b               #0xaf8020
    // 0xaf8000: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf8000: ldur            w1, [x0, #0x17]
    // 0xaf8004: DecompressPointer r1
    //     0xaf8004: add             x1, x1, HEAP, lsl #32
    // 0xaf8008: cmp             w1, NULL
    // 0xaf800c: b.ne            #0xaf8018
    // 0xaf8010: r0 = Null
    //     0xaf8010: mov             x0, NULL
    // 0xaf8014: b               #0xaf8020
    // 0xaf8018: LoadField: r0 = r1->field_b
    //     0xaf8018: ldur            w0, [x1, #0xb]
    // 0xaf801c: DecompressPointer r0
    //     0xaf801c: add             x0, x0, HEAP, lsl #32
    // 0xaf8020: cmp             w0, NULL
    // 0xaf8024: b.ne            #0xaf8030
    // 0xaf8028: r0 = 0
    //     0xaf8028: movz            x0, #0
    // 0xaf802c: b               #0xaf8040
    // 0xaf8030: r1 = LoadInt32Instr(r0)
    //     0xaf8030: sbfx            x1, x0, #1, #0x1f
    //     0xaf8034: tbz             w0, #0, #0xaf803c
    //     0xaf8038: ldur            x1, [x0, #7]
    // 0xaf803c: mov             x0, x1
    // 0xaf8040: ldur            x2, [fp, #-0x10]
    // 0xaf8044: stur            x0, [fp, #-0x28]
    // 0xaf8048: LoadField: r1 = r2->field_3f
    //     0xaf8048: ldur            w1, [x2, #0x3f]
    // 0xaf804c: DecompressPointer r1
    //     0xaf804c: add             x1, x1, HEAP, lsl #32
    // 0xaf8050: LoadField: r3 = r1->field_57
    //     0xaf8050: ldur            w3, [x1, #0x57]
    // 0xaf8054: DecompressPointer r3
    //     0xaf8054: add             x3, x3, HEAP, lsl #32
    // 0xaf8058: mov             x1, x3
    // 0xaf805c: r0 = value()
    //     0xaf805c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf8060: LoadField: r1 = r0->field_b
    //     0xaf8060: ldur            w1, [x0, #0xb]
    // 0xaf8064: DecompressPointer r1
    //     0xaf8064: add             x1, x1, HEAP, lsl #32
    // 0xaf8068: cmp             w1, NULL
    // 0xaf806c: b.ne            #0xaf8078
    // 0xaf8070: r0 = Null
    //     0xaf8070: mov             x0, NULL
    // 0xaf8074: b               #0xaf80b0
    // 0xaf8078: LoadField: r0 = r1->field_57
    //     0xaf8078: ldur            w0, [x1, #0x57]
    // 0xaf807c: DecompressPointer r0
    //     0xaf807c: add             x0, x0, HEAP, lsl #32
    // 0xaf8080: cmp             w0, NULL
    // 0xaf8084: b.ne            #0xaf8090
    // 0xaf8088: r0 = Null
    //     0xaf8088: mov             x0, NULL
    // 0xaf808c: b               #0xaf80b0
    // 0xaf8090: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf8090: ldur            w1, [x0, #0x17]
    // 0xaf8094: DecompressPointer r1
    //     0xaf8094: add             x1, x1, HEAP, lsl #32
    // 0xaf8098: cmp             w1, NULL
    // 0xaf809c: b.ne            #0xaf80a8
    // 0xaf80a0: r0 = Null
    //     0xaf80a0: mov             x0, NULL
    // 0xaf80a4: b               #0xaf80b0
    // 0xaf80a8: LoadField: r0 = r1->field_f
    //     0xaf80a8: ldur            w0, [x1, #0xf]
    // 0xaf80ac: DecompressPointer r0
    //     0xaf80ac: add             x0, x0, HEAP, lsl #32
    // 0xaf80b0: cmp             w0, NULL
    // 0xaf80b4: b.ne            #0xaf80c0
    // 0xaf80b8: r4 = 0
    //     0xaf80b8: movz            x4, #0
    // 0xaf80bc: b               #0xaf80d0
    // 0xaf80c0: r1 = LoadInt32Instr(r0)
    //     0xaf80c0: sbfx            x1, x0, #1, #0x1f
    //     0xaf80c4: tbz             w0, #0, #0xaf80cc
    //     0xaf80c8: ldur            x1, [x0, #7]
    // 0xaf80cc: mov             x4, x1
    // 0xaf80d0: ldur            x3, [fp, #-0x40]
    // 0xaf80d4: ldur            x2, [fp, #-0x18]
    // 0xaf80d8: ldur            x1, [fp, #-0x50]
    // 0xaf80dc: ldur            x0, [fp, #-0x38]
    // 0xaf80e0: stur            x4, [fp, #-0x30]
    // 0xaf80e4: r0 = Color()
    //     0xaf80e4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaf80e8: mov             x1, x0
    // 0xaf80ec: r0 = Instance_ColorSpace
    //     0xaf80ec: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaf80f0: stur            x1, [fp, #-0x48]
    // 0xaf80f4: StoreField: r1->field_27 = r0
    //     0xaf80f4: stur            w0, [x1, #0x27]
    // 0xaf80f8: d0 = 1.000000
    //     0xaf80f8: fmov            d0, #1.00000000
    // 0xaf80fc: StoreField: r1->field_7 = d0
    //     0xaf80fc: stur            d0, [x1, #7]
    // 0xaf8100: ldur            x0, [fp, #-0x20]
    // 0xaf8104: ubfx            x0, x0, #0, #0x20
    // 0xaf8108: and             w2, w0, #0xff
    // 0xaf810c: ubfx            x2, x2, #0, #0x20
    // 0xaf8110: scvtf           d0, x2
    // 0xaf8114: d1 = 255.000000
    //     0xaf8114: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaf8118: fdiv            d2, d0, d1
    // 0xaf811c: StoreField: r1->field_f = d2
    //     0xaf811c: stur            d2, [x1, #0xf]
    // 0xaf8120: ldur            x0, [fp, #-0x28]
    // 0xaf8124: ubfx            x0, x0, #0, #0x20
    // 0xaf8128: and             w2, w0, #0xff
    // 0xaf812c: ubfx            x2, x2, #0, #0x20
    // 0xaf8130: scvtf           d0, x2
    // 0xaf8134: fdiv            d2, d0, d1
    // 0xaf8138: ArrayStore: r1[0] = d2  ; List_8
    //     0xaf8138: stur            d2, [x1, #0x17]
    // 0xaf813c: ldur            x0, [fp, #-0x30]
    // 0xaf8140: ubfx            x0, x0, #0, #0x20
    // 0xaf8144: and             w2, w0, #0xff
    // 0xaf8148: ubfx            x2, x2, #0, #0x20
    // 0xaf814c: scvtf           d0, x2
    // 0xaf8150: fdiv            d2, d0, d1
    // 0xaf8154: StoreField: r1->field_1f = d2
    //     0xaf8154: stur            d2, [x1, #0x1f]
    // 0xaf8158: r0 = ColorFilter()
    //     0xaf8158: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xaf815c: mov             x1, x0
    // 0xaf8160: ldur            x0, [fp, #-0x48]
    // 0xaf8164: stur            x1, [fp, #-0x58]
    // 0xaf8168: StoreField: r1->field_7 = r0
    //     0xaf8168: stur            w0, [x1, #7]
    // 0xaf816c: r0 = Instance_BlendMode
    //     0xaf816c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xaf8170: ldr             x0, [x0, #0xb30]
    // 0xaf8174: StoreField: r1->field_b = r0
    //     0xaf8174: stur            w0, [x1, #0xb]
    // 0xaf8178: r0 = 1
    //     0xaf8178: movz            x0, #0x1
    // 0xaf817c: StoreField: r1->field_13 = r0
    //     0xaf817c: stur            x0, [x1, #0x13]
    // 0xaf8180: r0 = SvgPicture()
    //     0xaf8180: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xaf8184: stur            x0, [fp, #-0x48]
    // 0xaf8188: r16 = "menu"
    //     0xaf8188: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbc8] "menu"
    //     0xaf818c: ldr             x16, [x16, #0xbc8]
    // 0xaf8190: r30 = Instance_BoxFit
    //     0xaf8190: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xaf8194: ldr             lr, [lr, #0xb18]
    // 0xaf8198: stp             lr, x16, [SP, #8]
    // 0xaf819c: ldur            x16, [fp, #-0x58]
    // 0xaf81a0: str             x16, [SP]
    // 0xaf81a4: mov             x1, x0
    // 0xaf81a8: r2 = "assets/images/menu.svg"
    //     0xaf81a8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbd0] "assets/images/menu.svg"
    //     0xaf81ac: ldr             x2, [x2, #0xbd0]
    // 0xaf81b0: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xaf81b0: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xaf81b4: ldr             x4, [x4, #0xb38]
    // 0xaf81b8: r0 = SvgPicture.asset()
    //     0xaf81b8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xaf81bc: r0 = BottomNavigationBarItem()
    //     0xaf81bc: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xaf81c0: mov             x3, x0
    // 0xaf81c4: ldur            x0, [fp, #-0x38]
    // 0xaf81c8: stur            x3, [fp, #-0x58]
    // 0xaf81cc: StoreField: r3->field_b = r0
    //     0xaf81cc: stur            w0, [x3, #0xb]
    // 0xaf81d0: r0 = "Browse"
    //     0xaf81d0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cbd8] "Browse"
    //     0xaf81d4: ldr             x0, [x0, #0xbd8]
    // 0xaf81d8: StoreField: r3->field_13 = r0
    //     0xaf81d8: stur            w0, [x3, #0x13]
    // 0xaf81dc: ldur            x0, [fp, #-0x48]
    // 0xaf81e0: StoreField: r3->field_f = r0
    //     0xaf81e0: stur            w0, [x3, #0xf]
    // 0xaf81e4: r1 = Null
    //     0xaf81e4: mov             x1, NULL
    // 0xaf81e8: r2 = 8
    //     0xaf81e8: movz            x2, #0x8
    // 0xaf81ec: r0 = AllocateArray()
    //     0xaf81ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf81f0: mov             x2, x0
    // 0xaf81f4: ldur            x0, [fp, #-0x40]
    // 0xaf81f8: stur            x2, [fp, #-0x38]
    // 0xaf81fc: StoreField: r2->field_f = r0
    //     0xaf81fc: stur            w0, [x2, #0xf]
    // 0xaf8200: ldur            x0, [fp, #-0x18]
    // 0xaf8204: StoreField: r2->field_13 = r0
    //     0xaf8204: stur            w0, [x2, #0x13]
    // 0xaf8208: ldur            x0, [fp, #-0x50]
    // 0xaf820c: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf820c: stur            w0, [x2, #0x17]
    // 0xaf8210: ldur            x0, [fp, #-0x58]
    // 0xaf8214: StoreField: r2->field_1b = r0
    //     0xaf8214: stur            w0, [x2, #0x1b]
    // 0xaf8218: r1 = <BottomNavigationBarItem>
    //     0xaf8218: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbe0] TypeArguments: <BottomNavigationBarItem>
    //     0xaf821c: ldr             x1, [x1, #0xbe0]
    // 0xaf8220: r0 = AllocateGrowableArray()
    //     0xaf8220: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf8224: mov             x3, x0
    // 0xaf8228: ldur            x0, [fp, #-0x38]
    // 0xaf822c: stur            x3, [fp, #-0x18]
    // 0xaf8230: StoreField: r3->field_f = r0
    //     0xaf8230: stur            w0, [x3, #0xf]
    // 0xaf8234: r0 = 8
    //     0xaf8234: movz            x0, #0x8
    // 0xaf8238: StoreField: r3->field_b = r0
    //     0xaf8238: stur            w0, [x3, #0xb]
    // 0xaf823c: ldur            x2, [fp, #-0x10]
    // 0xaf8240: r1 = Function '_onItemTapped@1473460261':.
    //     0xaf8240: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dcf8] AnonymousClosure: (0x93adb8), in [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] _MainPageState::_onItemTapped (0x93ad48)
    //     0xaf8244: ldr             x1, [x1, #0xcf8]
    // 0xaf8248: r0 = AllocateClosure()
    //     0xaf8248: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf824c: stur            x0, [fp, #-0x10]
    // 0xaf8250: r0 = BottomNavigationBar()
    //     0xaf8250: bl              #0xa688fc  ; AllocateBottomNavigationBarStub -> BottomNavigationBar (size=0x70)
    // 0xaf8254: mov             x1, x0
    // 0xaf8258: ldur            x2, [fp, #-8]
    // 0xaf825c: ldur            x3, [fp, #-0x18]
    // 0xaf8260: ldur            x5, [fp, #-0x10]
    // 0xaf8264: stur            x0, [fp, #-0x10]
    // 0xaf8268: r0 = BottomNavigationBar()
    //     0xaf8268: bl              #0xa68858  ; [package:flutter/src/material/bottom_navigation_bar.dart] BottomNavigationBar::BottomNavigationBar
    // 0xaf826c: ldur            x0, [fp, #-0x10]
    // 0xaf8270: LeaveFrame
    //     0xaf8270: mov             SP, fp
    //     0xaf8274: ldp             fp, lr, [SP], #0x10
    // 0xaf8278: ret
    //     0xaf8278: ret             
    // 0xaf827c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf827c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf8280: b               #0xaf6e98
    // 0xaf8284: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf8284: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf8288: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf8288: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf828c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf828c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf8290: r0 = RangeErrorSharedWithFPURegs()
    //     0xaf8290: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xaf8294: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf8294: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaf8298: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf8298: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc8751c, size: 0x88
    // 0xc8751c: EnterFrame
    //     0xc8751c: stp             fp, lr, [SP, #-0x10]!
    //     0xc87520: mov             fp, SP
    // 0xc87524: AllocStack(0x10)
    //     0xc87524: sub             SP, SP, #0x10
    // 0xc87528: SetupParameters(_MainPageState this /* r1 => r1, fp-0x10 */)
    //     0xc87528: stur            x1, [fp, #-0x10]
    // 0xc8752c: CheckStackOverflow
    //     0xc8752c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87530: cmp             SP, x16
    //     0xc87534: b.ls            #0xc8759c
    // 0xc87538: LoadField: r0 = r1->field_3f
    //     0xc87538: ldur            w0, [x1, #0x3f]
    // 0xc8753c: DecompressPointer r0
    //     0xc8753c: add             x0, x0, HEAP, lsl #32
    // 0xc87540: LoadField: r2 = r0->field_4b
    //     0xc87540: ldur            w2, [x0, #0x4b]
    // 0xc87544: DecompressPointer r2
    //     0xc87544: add             x2, x2, HEAP, lsl #32
    // 0xc87548: stur            x2, [fp, #-8]
    // 0xc8754c: r0 = UtmData()
    //     0xc8754c: bl              #0x8939f0  ; AllocateUtmDataStub -> UtmData (size=0x1c)
    // 0xc87550: ldur            x1, [fp, #-8]
    // 0xc87554: mov             x2, x0
    // 0xc87558: r0 = setUtmData()
    //     0xc87558: bl              #0x916b50  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setUtmData
    // 0xc8755c: ldur            x2, [fp, #-0x10]
    // 0xc87560: LoadField: r1 = r2->field_47
    //     0xc87560: ldur            w1, [x2, #0x47]
    // 0xc87564: DecompressPointer r1
    //     0xc87564: add             x1, x1, HEAP, lsl #32
    // 0xc87568: cmp             w1, NULL
    // 0xc8756c: b.eq            #0xc87584
    // 0xc87570: r0 = LoadClassIdInstr(r1)
    //     0xc87570: ldur            x0, [x1, #-1]
    //     0xc87574: ubfx            x0, x0, #0xc, #0x14
    // 0xc87578: r0 = GDT[cid_x0 + -0xe14]()
    //     0xc87578: sub             lr, x0, #0xe14
    //     0xc8757c: ldr             lr, [x21, lr, lsl #3]
    //     0xc87580: blr             lr
    // 0xc87584: ldur            x1, [fp, #-0x10]
    // 0xc87588: r0 = dispose()
    //     0xc87588: bl              #0xc875a4  ; [package:customer_app/app/presentation/views/cosmetic/main/main_page.dart] __MainPageState&State&SingleTickerProviderStateMixin::dispose
    // 0xc8758c: r0 = Null
    //     0xc8758c: mov             x0, NULL
    // 0xc87590: LeaveFrame
    //     0xc87590: mov             SP, fp
    //     0xc87594: ldp             fp, lr, [SP], #0x10
    // 0xc87598: ret
    //     0xc87598: ret             
    // 0xc8759c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8759c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc875a0: b               #0xc87538
  }
}

// class id: 4154, size: 0xc, field offset: 0xc
//   const constructor, 
class MainPage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dce4, size: 0x48
    // 0xc7dce4: EnterFrame
    //     0xc7dce4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dce8: mov             fp, SP
    // 0xc7dcec: AllocStack(0x8)
    //     0xc7dcec: sub             SP, SP, #8
    // 0xc7dcf0: CheckStackOverflow
    //     0xc7dcf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7dcf4: cmp             SP, x16
    //     0xc7dcf8: b.ls            #0xc7dd24
    // 0xc7dcfc: r1 = <MainPage>
    //     0xc7dcfc: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ae80] TypeArguments: <MainPage>
    //     0xc7dd00: ldr             x1, [x1, #0xe80]
    // 0xc7dd04: r0 = _MainPageState()
    //     0xc7dd04: bl              #0xc7dd2c  ; Allocate_MainPageStateStub -> _MainPageState (size=0x54)
    // 0xc7dd08: mov             x1, x0
    // 0xc7dd0c: stur            x0, [fp, #-8]
    // 0xc7dd10: r0 = _MainPageState()
    //     0xc7dd10: bl              #0xc7be60  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::_MainPageState
    // 0xc7dd14: ldur            x0, [fp, #-8]
    // 0xc7dd18: LeaveFrame
    //     0xc7dd18: mov             SP, fp
    //     0xc7dd1c: ldp             fp, lr, [SP], #0x10
    // 0xc7dd20: ret
    //     0xc7dd20: ret             
    // 0xc7dd24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7dd24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7dd28: b               #0xc7dcfc
  }
}
