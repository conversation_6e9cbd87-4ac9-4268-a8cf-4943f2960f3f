// lib: , url: package:customer_app/app/presentation/views/glass/exchange/cancel_exchange_order_bottom_sheet.dart

// class id: 1049390, size: 0x8
class :: {
}

// class id: 3343, size: 0x14, field offset: 0x14
class _CancelExchangeBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb5c388, size: 0x8f4
    // 0xb5c388: EnterFrame
    //     0xb5c388: stp             fp, lr, [SP, #-0x10]!
    //     0xb5c38c: mov             fp, SP
    // 0xb5c390: AllocStack(0x58)
    //     0xb5c390: sub             SP, SP, #0x58
    // 0xb5c394: SetupParameters(_CancelExchangeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb5c394: mov             x0, x1
    //     0xb5c398: stur            x1, [fp, #-8]
    //     0xb5c39c: mov             x1, x2
    //     0xb5c3a0: stur            x2, [fp, #-0x10]
    // 0xb5c3a4: CheckStackOverflow
    //     0xb5c3a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5c3a8: cmp             SP, x16
    //     0xb5c3ac: b.ls            #0xb5cc68
    // 0xb5c3b0: r1 = 2
    //     0xb5c3b0: movz            x1, #0x2
    // 0xb5c3b4: r0 = AllocateContext()
    //     0xb5c3b4: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5c3b8: mov             x2, x0
    // 0xb5c3bc: ldur            x0, [fp, #-8]
    // 0xb5c3c0: stur            x2, [fp, #-0x20]
    // 0xb5c3c4: StoreField: r2->field_f = r0
    //     0xb5c3c4: stur            w0, [x2, #0xf]
    // 0xb5c3c8: ldur            x1, [fp, #-0x10]
    // 0xb5c3cc: StoreField: r2->field_13 = r1
    //     0xb5c3cc: stur            w1, [x2, #0x13]
    // 0xb5c3d0: LoadField: r3 = r0->field_b
    //     0xb5c3d0: ldur            w3, [x0, #0xb]
    // 0xb5c3d4: DecompressPointer r3
    //     0xb5c3d4: add             x3, x3, HEAP, lsl #32
    // 0xb5c3d8: cmp             w3, NULL
    // 0xb5c3dc: b.eq            #0xb5cc70
    // 0xb5c3e0: LoadField: r4 = r3->field_b
    //     0xb5c3e0: ldur            w4, [x3, #0xb]
    // 0xb5c3e4: DecompressPointer r4
    //     0xb5c3e4: add             x4, x4, HEAP, lsl #32
    // 0xb5c3e8: LoadField: r3 = r4->field_7
    //     0xb5c3e8: ldur            w3, [x4, #7]
    // 0xb5c3ec: DecompressPointer r3
    //     0xb5c3ec: add             x3, x3, HEAP, lsl #32
    // 0xb5c3f0: cmp             w3, NULL
    // 0xb5c3f4: b.ne            #0xb5c3fc
    // 0xb5c3f8: r3 = ""
    //     0xb5c3f8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5c3fc: stur            x3, [fp, #-0x18]
    // 0xb5c400: r0 = of()
    //     0xb5c400: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c404: LoadField: r1 = r0->field_87
    //     0xb5c404: ldur            w1, [x0, #0x87]
    // 0xb5c408: DecompressPointer r1
    //     0xb5c408: add             x1, x1, HEAP, lsl #32
    // 0xb5c40c: LoadField: r0 = r1->field_7
    //     0xb5c40c: ldur            w0, [x1, #7]
    // 0xb5c410: DecompressPointer r0
    //     0xb5c410: add             x0, x0, HEAP, lsl #32
    // 0xb5c414: r16 = Instance_Color
    //     0xb5c414: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5c418: r30 = 16.000000
    //     0xb5c418: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5c41c: ldr             lr, [lr, #0x188]
    // 0xb5c420: stp             lr, x16, [SP]
    // 0xb5c424: mov             x1, x0
    // 0xb5c428: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb5c428: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5c42c: ldr             x4, [x4, #0x9b8]
    // 0xb5c430: r0 = copyWith()
    //     0xb5c430: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5c434: stur            x0, [fp, #-0x10]
    // 0xb5c438: r0 = Text()
    //     0xb5c438: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5c43c: mov             x1, x0
    // 0xb5c440: ldur            x0, [fp, #-0x18]
    // 0xb5c444: stur            x1, [fp, #-0x28]
    // 0xb5c448: StoreField: r1->field_b = r0
    //     0xb5c448: stur            w0, [x1, #0xb]
    // 0xb5c44c: ldur            x0, [fp, #-0x10]
    // 0xb5c450: StoreField: r1->field_13 = r0
    //     0xb5c450: stur            w0, [x1, #0x13]
    // 0xb5c454: r0 = SvgPicture()
    //     0xb5c454: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb5c458: mov             x1, x0
    // 0xb5c45c: r2 = "assets/images/x.svg"
    //     0xb5c45c: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb5c460: ldr             x2, [x2, #0x5e8]
    // 0xb5c464: stur            x0, [fp, #-0x10]
    // 0xb5c468: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb5c468: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb5c46c: r0 = SvgPicture.asset()
    //     0xb5c46c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb5c470: r0 = InkWell()
    //     0xb5c470: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb5c474: mov             x3, x0
    // 0xb5c478: ldur            x0, [fp, #-0x10]
    // 0xb5c47c: stur            x3, [fp, #-0x18]
    // 0xb5c480: StoreField: r3->field_b = r0
    //     0xb5c480: stur            w0, [x3, #0xb]
    // 0xb5c484: ldur            x2, [fp, #-0x20]
    // 0xb5c488: r1 = Function '<anonymous closure>':.
    //     0xb5c488: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fc8] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb5c48c: ldr             x1, [x1, #0xfc8]
    // 0xb5c490: r0 = AllocateClosure()
    //     0xb5c490: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5c494: mov             x1, x0
    // 0xb5c498: ldur            x0, [fp, #-0x18]
    // 0xb5c49c: StoreField: r0->field_f = r1
    //     0xb5c49c: stur            w1, [x0, #0xf]
    // 0xb5c4a0: r3 = true
    //     0xb5c4a0: add             x3, NULL, #0x20  ; true
    // 0xb5c4a4: StoreField: r0->field_43 = r3
    //     0xb5c4a4: stur            w3, [x0, #0x43]
    // 0xb5c4a8: r1 = Instance_BoxShape
    //     0xb5c4a8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5c4ac: ldr             x1, [x1, #0x80]
    // 0xb5c4b0: StoreField: r0->field_47 = r1
    //     0xb5c4b0: stur            w1, [x0, #0x47]
    // 0xb5c4b4: StoreField: r0->field_6f = r3
    //     0xb5c4b4: stur            w3, [x0, #0x6f]
    // 0xb5c4b8: r4 = false
    //     0xb5c4b8: add             x4, NULL, #0x30  ; false
    // 0xb5c4bc: StoreField: r0->field_73 = r4
    //     0xb5c4bc: stur            w4, [x0, #0x73]
    // 0xb5c4c0: StoreField: r0->field_83 = r3
    //     0xb5c4c0: stur            w3, [x0, #0x83]
    // 0xb5c4c4: StoreField: r0->field_7b = r4
    //     0xb5c4c4: stur            w4, [x0, #0x7b]
    // 0xb5c4c8: r1 = Null
    //     0xb5c4c8: mov             x1, NULL
    // 0xb5c4cc: r2 = 4
    //     0xb5c4cc: movz            x2, #0x4
    // 0xb5c4d0: r0 = AllocateArray()
    //     0xb5c4d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5c4d4: mov             x2, x0
    // 0xb5c4d8: ldur            x0, [fp, #-0x28]
    // 0xb5c4dc: stur            x2, [fp, #-0x10]
    // 0xb5c4e0: StoreField: r2->field_f = r0
    //     0xb5c4e0: stur            w0, [x2, #0xf]
    // 0xb5c4e4: ldur            x0, [fp, #-0x18]
    // 0xb5c4e8: StoreField: r2->field_13 = r0
    //     0xb5c4e8: stur            w0, [x2, #0x13]
    // 0xb5c4ec: r1 = <Widget>
    //     0xb5c4ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5c4f0: r0 = AllocateGrowableArray()
    //     0xb5c4f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5c4f4: mov             x1, x0
    // 0xb5c4f8: ldur            x0, [fp, #-0x10]
    // 0xb5c4fc: stur            x1, [fp, #-0x18]
    // 0xb5c500: StoreField: r1->field_f = r0
    //     0xb5c500: stur            w0, [x1, #0xf]
    // 0xb5c504: r0 = 4
    //     0xb5c504: movz            x0, #0x4
    // 0xb5c508: StoreField: r1->field_b = r0
    //     0xb5c508: stur            w0, [x1, #0xb]
    // 0xb5c50c: r0 = Row()
    //     0xb5c50c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5c510: mov             x2, x0
    // 0xb5c514: r0 = Instance_Axis
    //     0xb5c514: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5c518: stur            x2, [fp, #-0x28]
    // 0xb5c51c: StoreField: r2->field_f = r0
    //     0xb5c51c: stur            w0, [x2, #0xf]
    // 0xb5c520: r3 = Instance_MainAxisAlignment
    //     0xb5c520: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb5c524: ldr             x3, [x3, #0xa8]
    // 0xb5c528: StoreField: r2->field_13 = r3
    //     0xb5c528: stur            w3, [x2, #0x13]
    // 0xb5c52c: r4 = Instance_MainAxisSize
    //     0xb5c52c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5c530: ldr             x4, [x4, #0xa10]
    // 0xb5c534: ArrayStore: r2[0] = r4  ; List_4
    //     0xb5c534: stur            w4, [x2, #0x17]
    // 0xb5c538: r1 = Instance_CrossAxisAlignment
    //     0xb5c538: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5c53c: ldr             x1, [x1, #0xa18]
    // 0xb5c540: StoreField: r2->field_1b = r1
    //     0xb5c540: stur            w1, [x2, #0x1b]
    // 0xb5c544: r5 = Instance_VerticalDirection
    //     0xb5c544: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5c548: ldr             x5, [x5, #0xa20]
    // 0xb5c54c: StoreField: r2->field_23 = r5
    //     0xb5c54c: stur            w5, [x2, #0x23]
    // 0xb5c550: r6 = Instance_Clip
    //     0xb5c550: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5c554: ldr             x6, [x6, #0x38]
    // 0xb5c558: StoreField: r2->field_2b = r6
    //     0xb5c558: stur            w6, [x2, #0x2b]
    // 0xb5c55c: StoreField: r2->field_2f = rZR
    //     0xb5c55c: stur            xzr, [x2, #0x2f]
    // 0xb5c560: ldur            x1, [fp, #-0x18]
    // 0xb5c564: StoreField: r2->field_b = r1
    //     0xb5c564: stur            w1, [x2, #0xb]
    // 0xb5c568: ldur            x7, [fp, #-8]
    // 0xb5c56c: LoadField: r1 = r7->field_b
    //     0xb5c56c: ldur            w1, [x7, #0xb]
    // 0xb5c570: DecompressPointer r1
    //     0xb5c570: add             x1, x1, HEAP, lsl #32
    // 0xb5c574: cmp             w1, NULL
    // 0xb5c578: b.eq            #0xb5cc74
    // 0xb5c57c: LoadField: r8 = r1->field_b
    //     0xb5c57c: ldur            w8, [x1, #0xb]
    // 0xb5c580: DecompressPointer r8
    //     0xb5c580: add             x8, x8, HEAP, lsl #32
    // 0xb5c584: LoadField: r1 = r8->field_f
    //     0xb5c584: ldur            w1, [x8, #0xf]
    // 0xb5c588: DecompressPointer r1
    //     0xb5c588: add             x1, x1, HEAP, lsl #32
    // 0xb5c58c: cmp             w1, NULL
    // 0xb5c590: b.ne            #0xb5c59c
    // 0xb5c594: r9 = ""
    //     0xb5c594: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5c598: b               #0xb5c5a0
    // 0xb5c59c: mov             x9, x1
    // 0xb5c5a0: ldur            x8, [fp, #-0x20]
    // 0xb5c5a4: stur            x9, [fp, #-0x10]
    // 0xb5c5a8: LoadField: r1 = r8->field_13
    //     0xb5c5a8: ldur            w1, [x8, #0x13]
    // 0xb5c5ac: DecompressPointer r1
    //     0xb5c5ac: add             x1, x1, HEAP, lsl #32
    // 0xb5c5b0: r0 = of()
    //     0xb5c5b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c5b4: LoadField: r1 = r0->field_87
    //     0xb5c5b4: ldur            w1, [x0, #0x87]
    // 0xb5c5b8: DecompressPointer r1
    //     0xb5c5b8: add             x1, x1, HEAP, lsl #32
    // 0xb5c5bc: LoadField: r0 = r1->field_2b
    //     0xb5c5bc: ldur            w0, [x1, #0x2b]
    // 0xb5c5c0: DecompressPointer r0
    //     0xb5c5c0: add             x0, x0, HEAP, lsl #32
    // 0xb5c5c4: stur            x0, [fp, #-0x18]
    // 0xb5c5c8: r1 = Instance_Color
    //     0xb5c5c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5c5cc: d0 = 0.700000
    //     0xb5c5cc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5c5d0: ldr             d0, [x17, #0xf48]
    // 0xb5c5d4: r0 = withOpacity()
    //     0xb5c5d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb5c5d8: r16 = 12.000000
    //     0xb5c5d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5c5dc: ldr             x16, [x16, #0x9e8]
    // 0xb5c5e0: stp             x0, x16, [SP]
    // 0xb5c5e4: ldur            x1, [fp, #-0x18]
    // 0xb5c5e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5c5e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5c5ec: ldr             x4, [x4, #0xaa0]
    // 0xb5c5f0: r0 = copyWith()
    //     0xb5c5f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5c5f4: stur            x0, [fp, #-0x18]
    // 0xb5c5f8: r0 = HtmlWidget()
    //     0xb5c5f8: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xb5c5fc: mov             x1, x0
    // 0xb5c600: ldur            x0, [fp, #-0x10]
    // 0xb5c604: stur            x1, [fp, #-0x30]
    // 0xb5c608: StoreField: r1->field_1f = r0
    //     0xb5c608: stur            w0, [x1, #0x1f]
    // 0xb5c60c: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb5c60c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb5c610: ldr             x0, [x0, #0x1e0]
    // 0xb5c614: StoreField: r1->field_23 = r0
    //     0xb5c614: stur            w0, [x1, #0x23]
    // 0xb5c618: r0 = Instance_ColumnMode
    //     0xb5c618: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb5c61c: ldr             x0, [x0, #0x1e8]
    // 0xb5c620: StoreField: r1->field_3b = r0
    //     0xb5c620: stur            w0, [x1, #0x3b]
    // 0xb5c624: ldur            x0, [fp, #-0x18]
    // 0xb5c628: StoreField: r1->field_3f = r0
    //     0xb5c628: stur            w0, [x1, #0x3f]
    // 0xb5c62c: r16 = <EdgeInsets>
    //     0xb5c62c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb5c630: ldr             x16, [x16, #0xda0]
    // 0xb5c634: r30 = Instance_EdgeInsets
    //     0xb5c634: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5c638: ldr             lr, [lr, #0x1f0]
    // 0xb5c63c: stp             lr, x16, [SP]
    // 0xb5c640: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5c640: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5c644: r0 = all()
    //     0xb5c644: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5c648: stur            x0, [fp, #-0x10]
    // 0xb5c64c: r16 = <Color>
    //     0xb5c64c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb5c650: ldr             x16, [x16, #0xf80]
    // 0xb5c654: r30 = Instance_Color
    //     0xb5c654: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb5c658: stp             lr, x16, [SP]
    // 0xb5c65c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5c65c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5c660: r0 = all()
    //     0xb5c660: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5c664: stur            x0, [fp, #-0x18]
    // 0xb5c668: r0 = Radius()
    //     0xb5c668: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb5c66c: d0 = 12.000000
    //     0xb5c66c: fmov            d0, #12.00000000
    // 0xb5c670: stur            x0, [fp, #-0x38]
    // 0xb5c674: StoreField: r0->field_7 = d0
    //     0xb5c674: stur            d0, [x0, #7]
    // 0xb5c678: StoreField: r0->field_f = d0
    //     0xb5c678: stur            d0, [x0, #0xf]
    // 0xb5c67c: r0 = BorderRadius()
    //     0xb5c67c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb5c680: mov             x2, x0
    // 0xb5c684: ldur            x0, [fp, #-0x38]
    // 0xb5c688: stur            x2, [fp, #-0x40]
    // 0xb5c68c: StoreField: r2->field_7 = r0
    //     0xb5c68c: stur            w0, [x2, #7]
    // 0xb5c690: StoreField: r2->field_b = r0
    //     0xb5c690: stur            w0, [x2, #0xb]
    // 0xb5c694: StoreField: r2->field_f = r0
    //     0xb5c694: stur            w0, [x2, #0xf]
    // 0xb5c698: StoreField: r2->field_13 = r0
    //     0xb5c698: stur            w0, [x2, #0x13]
    // 0xb5c69c: ldur            x0, [fp, #-0x20]
    // 0xb5c6a0: LoadField: r1 = r0->field_13
    //     0xb5c6a0: ldur            w1, [x0, #0x13]
    // 0xb5c6a4: DecompressPointer r1
    //     0xb5c6a4: add             x1, x1, HEAP, lsl #32
    // 0xb5c6a8: r0 = of()
    //     0xb5c6a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c6ac: LoadField: r1 = r0->field_5b
    //     0xb5c6ac: ldur            w1, [x0, #0x5b]
    // 0xb5c6b0: DecompressPointer r1
    //     0xb5c6b0: add             x1, x1, HEAP, lsl #32
    // 0xb5c6b4: stur            x1, [fp, #-0x38]
    // 0xb5c6b8: r0 = BorderSide()
    //     0xb5c6b8: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb5c6bc: mov             x1, x0
    // 0xb5c6c0: ldur            x0, [fp, #-0x38]
    // 0xb5c6c4: stur            x1, [fp, #-0x48]
    // 0xb5c6c8: StoreField: r1->field_7 = r0
    //     0xb5c6c8: stur            w0, [x1, #7]
    // 0xb5c6cc: d0 = 1.000000
    //     0xb5c6cc: fmov            d0, #1.00000000
    // 0xb5c6d0: StoreField: r1->field_b = d0
    //     0xb5c6d0: stur            d0, [x1, #0xb]
    // 0xb5c6d4: r0 = Instance_BorderStyle
    //     0xb5c6d4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb5c6d8: ldr             x0, [x0, #0xf68]
    // 0xb5c6dc: StoreField: r1->field_13 = r0
    //     0xb5c6dc: stur            w0, [x1, #0x13]
    // 0xb5c6e0: d0 = -1.000000
    //     0xb5c6e0: fmov            d0, #-1.00000000
    // 0xb5c6e4: ArrayStore: r1[0] = d0  ; List_8
    //     0xb5c6e4: stur            d0, [x1, #0x17]
    // 0xb5c6e8: r0 = RoundedRectangleBorder()
    //     0xb5c6e8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb5c6ec: mov             x1, x0
    // 0xb5c6f0: ldur            x0, [fp, #-0x40]
    // 0xb5c6f4: StoreField: r1->field_b = r0
    //     0xb5c6f4: stur            w0, [x1, #0xb]
    // 0xb5c6f8: ldur            x0, [fp, #-0x48]
    // 0xb5c6fc: StoreField: r1->field_7 = r0
    //     0xb5c6fc: stur            w0, [x1, #7]
    // 0xb5c700: r16 = <RoundedRectangleBorder>
    //     0xb5c700: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb5c704: ldr             x16, [x16, #0xf78]
    // 0xb5c708: stp             x1, x16, [SP]
    // 0xb5c70c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5c70c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5c710: r0 = all()
    //     0xb5c710: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5c714: stur            x0, [fp, #-0x38]
    // 0xb5c718: r0 = ButtonStyle()
    //     0xb5c718: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb5c71c: mov             x1, x0
    // 0xb5c720: ldur            x0, [fp, #-0x18]
    // 0xb5c724: stur            x1, [fp, #-0x40]
    // 0xb5c728: StoreField: r1->field_b = r0
    //     0xb5c728: stur            w0, [x1, #0xb]
    // 0xb5c72c: ldur            x0, [fp, #-0x10]
    // 0xb5c730: StoreField: r1->field_23 = r0
    //     0xb5c730: stur            w0, [x1, #0x23]
    // 0xb5c734: ldur            x0, [fp, #-0x38]
    // 0xb5c738: StoreField: r1->field_43 = r0
    //     0xb5c738: stur            w0, [x1, #0x43]
    // 0xb5c73c: r0 = TextButtonThemeData()
    //     0xb5c73c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb5c740: mov             x2, x0
    // 0xb5c744: ldur            x0, [fp, #-0x40]
    // 0xb5c748: stur            x2, [fp, #-0x10]
    // 0xb5c74c: StoreField: r2->field_7 = r0
    //     0xb5c74c: stur            w0, [x2, #7]
    // 0xb5c750: r1 = "go back"
    //     0xb5c750: add             x1, PP, #0x55, lsl #12  ; [pp+0x55a28] "go back"
    //     0xb5c754: ldr             x1, [x1, #0xa28]
    // 0xb5c758: r0 = capitalizeFirstWord()
    //     0xb5c758: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb5c75c: ldur            x2, [fp, #-0x20]
    // 0xb5c760: stur            x0, [fp, #-0x18]
    // 0xb5c764: LoadField: r1 = r2->field_13
    //     0xb5c764: ldur            w1, [x2, #0x13]
    // 0xb5c768: DecompressPointer r1
    //     0xb5c768: add             x1, x1, HEAP, lsl #32
    // 0xb5c76c: r0 = of()
    //     0xb5c76c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c770: LoadField: r1 = r0->field_87
    //     0xb5c770: ldur            w1, [x0, #0x87]
    // 0xb5c774: DecompressPointer r1
    //     0xb5c774: add             x1, x1, HEAP, lsl #32
    // 0xb5c778: LoadField: r0 = r1->field_7
    //     0xb5c778: ldur            w0, [x1, #7]
    // 0xb5c77c: DecompressPointer r0
    //     0xb5c77c: add             x0, x0, HEAP, lsl #32
    // 0xb5c780: ldur            x2, [fp, #-0x20]
    // 0xb5c784: stur            x0, [fp, #-0x38]
    // 0xb5c788: LoadField: r1 = r2->field_13
    //     0xb5c788: ldur            w1, [x2, #0x13]
    // 0xb5c78c: DecompressPointer r1
    //     0xb5c78c: add             x1, x1, HEAP, lsl #32
    // 0xb5c790: r0 = of()
    //     0xb5c790: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c794: LoadField: r1 = r0->field_5b
    //     0xb5c794: ldur            w1, [x0, #0x5b]
    // 0xb5c798: DecompressPointer r1
    //     0xb5c798: add             x1, x1, HEAP, lsl #32
    // 0xb5c79c: r16 = 14.000000
    //     0xb5c79c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5c7a0: ldr             x16, [x16, #0x1d8]
    // 0xb5c7a4: stp             x1, x16, [SP]
    // 0xb5c7a8: ldur            x1, [fp, #-0x38]
    // 0xb5c7ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5c7ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5c7b0: ldr             x4, [x4, #0xaa0]
    // 0xb5c7b4: r0 = copyWith()
    //     0xb5c7b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5c7b8: stur            x0, [fp, #-0x38]
    // 0xb5c7bc: r0 = Text()
    //     0xb5c7bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5c7c0: mov             x3, x0
    // 0xb5c7c4: ldur            x0, [fp, #-0x18]
    // 0xb5c7c8: stur            x3, [fp, #-0x40]
    // 0xb5c7cc: StoreField: r3->field_b = r0
    //     0xb5c7cc: stur            w0, [x3, #0xb]
    // 0xb5c7d0: ldur            x0, [fp, #-0x38]
    // 0xb5c7d4: StoreField: r3->field_13 = r0
    //     0xb5c7d4: stur            w0, [x3, #0x13]
    // 0xb5c7d8: r1 = Function '<anonymous closure>':.
    //     0xb5c7d8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fd0] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5c7dc: ldr             x1, [x1, #0xfd0]
    // 0xb5c7e0: r2 = Null
    //     0xb5c7e0: mov             x2, NULL
    // 0xb5c7e4: r0 = AllocateClosure()
    //     0xb5c7e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5c7e8: stur            x0, [fp, #-0x18]
    // 0xb5c7ec: r0 = TextButton()
    //     0xb5c7ec: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb5c7f0: mov             x1, x0
    // 0xb5c7f4: ldur            x0, [fp, #-0x18]
    // 0xb5c7f8: stur            x1, [fp, #-0x38]
    // 0xb5c7fc: StoreField: r1->field_b = r0
    //     0xb5c7fc: stur            w0, [x1, #0xb]
    // 0xb5c800: r0 = false
    //     0xb5c800: add             x0, NULL, #0x30  ; false
    // 0xb5c804: StoreField: r1->field_27 = r0
    //     0xb5c804: stur            w0, [x1, #0x27]
    // 0xb5c808: r2 = true
    //     0xb5c808: add             x2, NULL, #0x20  ; true
    // 0xb5c80c: StoreField: r1->field_2f = r2
    //     0xb5c80c: stur            w2, [x1, #0x2f]
    // 0xb5c810: ldur            x3, [fp, #-0x40]
    // 0xb5c814: StoreField: r1->field_37 = r3
    //     0xb5c814: stur            w3, [x1, #0x37]
    // 0xb5c818: r0 = TextButtonTheme()
    //     0xb5c818: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb5c81c: mov             x2, x0
    // 0xb5c820: ldur            x0, [fp, #-0x10]
    // 0xb5c824: stur            x2, [fp, #-0x18]
    // 0xb5c828: StoreField: r2->field_f = r0
    //     0xb5c828: stur            w0, [x2, #0xf]
    // 0xb5c82c: ldur            x0, [fp, #-0x38]
    // 0xb5c830: StoreField: r2->field_b = r0
    //     0xb5c830: stur            w0, [x2, #0xb]
    // 0xb5c834: r1 = <FlexParentData>
    //     0xb5c834: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5c838: ldr             x1, [x1, #0xe00]
    // 0xb5c83c: r0 = Expanded()
    //     0xb5c83c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb5c840: mov             x1, x0
    // 0xb5c844: r0 = 1
    //     0xb5c844: movz            x0, #0x1
    // 0xb5c848: stur            x1, [fp, #-0x10]
    // 0xb5c84c: StoreField: r1->field_13 = r0
    //     0xb5c84c: stur            x0, [x1, #0x13]
    // 0xb5c850: r2 = Instance_FlexFit
    //     0xb5c850: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb5c854: ldr             x2, [x2, #0xe08]
    // 0xb5c858: StoreField: r1->field_1b = r2
    //     0xb5c858: stur            w2, [x1, #0x1b]
    // 0xb5c85c: ldur            x3, [fp, #-0x18]
    // 0xb5c860: StoreField: r1->field_b = r3
    //     0xb5c860: stur            w3, [x1, #0xb]
    // 0xb5c864: r16 = <EdgeInsets>
    //     0xb5c864: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb5c868: ldr             x16, [x16, #0xda0]
    // 0xb5c86c: r30 = Instance_EdgeInsets
    //     0xb5c86c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5c870: ldr             lr, [lr, #0x1f0]
    // 0xb5c874: stp             lr, x16, [SP]
    // 0xb5c878: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5c878: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5c87c: r0 = all()
    //     0xb5c87c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5c880: ldur            x2, [fp, #-0x20]
    // 0xb5c884: stur            x0, [fp, #-0x18]
    // 0xb5c888: LoadField: r1 = r2->field_13
    //     0xb5c888: ldur            w1, [x2, #0x13]
    // 0xb5c88c: DecompressPointer r1
    //     0xb5c88c: add             x1, x1, HEAP, lsl #32
    // 0xb5c890: r0 = of()
    //     0xb5c890: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c894: LoadField: r1 = r0->field_5b
    //     0xb5c894: ldur            w1, [x0, #0x5b]
    // 0xb5c898: DecompressPointer r1
    //     0xb5c898: add             x1, x1, HEAP, lsl #32
    // 0xb5c89c: r16 = <Color>
    //     0xb5c89c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb5c8a0: ldr             x16, [x16, #0xf80]
    // 0xb5c8a4: stp             x1, x16, [SP]
    // 0xb5c8a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5c8a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5c8ac: r0 = all()
    //     0xb5c8ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5c8b0: stur            x0, [fp, #-0x38]
    // 0xb5c8b4: r0 = Radius()
    //     0xb5c8b4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb5c8b8: d0 = 12.000000
    //     0xb5c8b8: fmov            d0, #12.00000000
    // 0xb5c8bc: stur            x0, [fp, #-0x40]
    // 0xb5c8c0: StoreField: r0->field_7 = d0
    //     0xb5c8c0: stur            d0, [x0, #7]
    // 0xb5c8c4: StoreField: r0->field_f = d0
    //     0xb5c8c4: stur            d0, [x0, #0xf]
    // 0xb5c8c8: r0 = BorderRadius()
    //     0xb5c8c8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb5c8cc: mov             x1, x0
    // 0xb5c8d0: ldur            x0, [fp, #-0x40]
    // 0xb5c8d4: stur            x1, [fp, #-0x48]
    // 0xb5c8d8: StoreField: r1->field_7 = r0
    //     0xb5c8d8: stur            w0, [x1, #7]
    // 0xb5c8dc: StoreField: r1->field_b = r0
    //     0xb5c8dc: stur            w0, [x1, #0xb]
    // 0xb5c8e0: StoreField: r1->field_f = r0
    //     0xb5c8e0: stur            w0, [x1, #0xf]
    // 0xb5c8e4: StoreField: r1->field_13 = r0
    //     0xb5c8e4: stur            w0, [x1, #0x13]
    // 0xb5c8e8: r0 = RoundedRectangleBorder()
    //     0xb5c8e8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb5c8ec: mov             x1, x0
    // 0xb5c8f0: ldur            x0, [fp, #-0x48]
    // 0xb5c8f4: StoreField: r1->field_b = r0
    //     0xb5c8f4: stur            w0, [x1, #0xb]
    // 0xb5c8f8: r0 = Instance_BorderSide
    //     0xb5c8f8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb5c8fc: ldr             x0, [x0, #0xe20]
    // 0xb5c900: StoreField: r1->field_7 = r0
    //     0xb5c900: stur            w0, [x1, #7]
    // 0xb5c904: r16 = <RoundedRectangleBorder>
    //     0xb5c904: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb5c908: ldr             x16, [x16, #0xf78]
    // 0xb5c90c: stp             x1, x16, [SP]
    // 0xb5c910: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5c910: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5c914: r0 = all()
    //     0xb5c914: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5c918: stur            x0, [fp, #-0x40]
    // 0xb5c91c: r0 = ButtonStyle()
    //     0xb5c91c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb5c920: mov             x1, x0
    // 0xb5c924: ldur            x0, [fp, #-0x38]
    // 0xb5c928: stur            x1, [fp, #-0x48]
    // 0xb5c92c: StoreField: r1->field_b = r0
    //     0xb5c92c: stur            w0, [x1, #0xb]
    // 0xb5c930: ldur            x0, [fp, #-0x18]
    // 0xb5c934: StoreField: r1->field_23 = r0
    //     0xb5c934: stur            w0, [x1, #0x23]
    // 0xb5c938: ldur            x0, [fp, #-0x40]
    // 0xb5c93c: StoreField: r1->field_43 = r0
    //     0xb5c93c: stur            w0, [x1, #0x43]
    // 0xb5c940: r0 = TextButtonThemeData()
    //     0xb5c940: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb5c944: mov             x2, x0
    // 0xb5c948: ldur            x0, [fp, #-0x48]
    // 0xb5c94c: stur            x2, [fp, #-0x18]
    // 0xb5c950: StoreField: r2->field_7 = r0
    //     0xb5c950: stur            w0, [x2, #7]
    // 0xb5c954: ldur            x0, [fp, #-8]
    // 0xb5c958: LoadField: r1 = r0->field_b
    //     0xb5c958: ldur            w1, [x0, #0xb]
    // 0xb5c95c: DecompressPointer r1
    //     0xb5c95c: add             x1, x1, HEAP, lsl #32
    // 0xb5c960: cmp             w1, NULL
    // 0xb5c964: b.eq            #0xb5cc78
    // 0xb5c968: LoadField: r0 = r1->field_b
    //     0xb5c968: ldur            w0, [x1, #0xb]
    // 0xb5c96c: DecompressPointer r0
    //     0xb5c96c: add             x0, x0, HEAP, lsl #32
    // 0xb5c970: LoadField: r1 = r0->field_b
    //     0xb5c970: ldur            w1, [x0, #0xb]
    // 0xb5c974: DecompressPointer r1
    //     0xb5c974: add             x1, x1, HEAP, lsl #32
    // 0xb5c978: cmp             w1, NULL
    // 0xb5c97c: b.ne            #0xb5c984
    // 0xb5c980: r1 = ""
    //     0xb5c980: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5c984: ldur            x0, [fp, #-0x20]
    // 0xb5c988: ldur            x5, [fp, #-0x28]
    // 0xb5c98c: ldur            x4, [fp, #-0x30]
    // 0xb5c990: ldur            x3, [fp, #-0x10]
    // 0xb5c994: r0 = capitalizeFirstWord()
    //     0xb5c994: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb5c998: ldur            x2, [fp, #-0x20]
    // 0xb5c99c: stur            x0, [fp, #-8]
    // 0xb5c9a0: LoadField: r1 = r2->field_13
    //     0xb5c9a0: ldur            w1, [x2, #0x13]
    // 0xb5c9a4: DecompressPointer r1
    //     0xb5c9a4: add             x1, x1, HEAP, lsl #32
    // 0xb5c9a8: r0 = of()
    //     0xb5c9a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5c9ac: LoadField: r1 = r0->field_87
    //     0xb5c9ac: ldur            w1, [x0, #0x87]
    // 0xb5c9b0: DecompressPointer r1
    //     0xb5c9b0: add             x1, x1, HEAP, lsl #32
    // 0xb5c9b4: LoadField: r0 = r1->field_7
    //     0xb5c9b4: ldur            w0, [x1, #7]
    // 0xb5c9b8: DecompressPointer r0
    //     0xb5c9b8: add             x0, x0, HEAP, lsl #32
    // 0xb5c9bc: r16 = 14.000000
    //     0xb5c9bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5c9c0: ldr             x16, [x16, #0x1d8]
    // 0xb5c9c4: r30 = Instance_Color
    //     0xb5c9c4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb5c9c8: stp             lr, x16, [SP]
    // 0xb5c9cc: mov             x1, x0
    // 0xb5c9d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5c9d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5c9d4: ldr             x4, [x4, #0xaa0]
    // 0xb5c9d8: r0 = copyWith()
    //     0xb5c9d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5c9dc: stur            x0, [fp, #-0x38]
    // 0xb5c9e0: r0 = Text()
    //     0xb5c9e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5c9e4: mov             x3, x0
    // 0xb5c9e8: ldur            x0, [fp, #-8]
    // 0xb5c9ec: stur            x3, [fp, #-0x40]
    // 0xb5c9f0: StoreField: r3->field_b = r0
    //     0xb5c9f0: stur            w0, [x3, #0xb]
    // 0xb5c9f4: ldur            x0, [fp, #-0x38]
    // 0xb5c9f8: StoreField: r3->field_13 = r0
    //     0xb5c9f8: stur            w0, [x3, #0x13]
    // 0xb5c9fc: r0 = Instance_TextAlign
    //     0xb5c9fc: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb5ca00: StoreField: r3->field_1b = r0
    //     0xb5ca00: stur            w0, [x3, #0x1b]
    // 0xb5ca04: ldur            x2, [fp, #-0x20]
    // 0xb5ca08: r1 = Function '<anonymous closure>':.
    //     0xb5ca08: add             x1, PP, #0x55, lsl #12  ; [pp+0x55fd8] AnonymousClosure: (0xb5cc9c), in [package:customer_app/app/presentation/views/glass/exchange/cancel_exchange_order_bottom_sheet.dart] _CancelExchangeBottomSheetState::build (0xb5c388)
    //     0xb5ca0c: ldr             x1, [x1, #0xfd8]
    // 0xb5ca10: r0 = AllocateClosure()
    //     0xb5ca10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5ca14: stur            x0, [fp, #-8]
    // 0xb5ca18: r0 = TextButton()
    //     0xb5ca18: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb5ca1c: mov             x1, x0
    // 0xb5ca20: ldur            x0, [fp, #-8]
    // 0xb5ca24: stur            x1, [fp, #-0x20]
    // 0xb5ca28: StoreField: r1->field_b = r0
    //     0xb5ca28: stur            w0, [x1, #0xb]
    // 0xb5ca2c: r0 = false
    //     0xb5ca2c: add             x0, NULL, #0x30  ; false
    // 0xb5ca30: StoreField: r1->field_27 = r0
    //     0xb5ca30: stur            w0, [x1, #0x27]
    // 0xb5ca34: r0 = true
    //     0xb5ca34: add             x0, NULL, #0x20  ; true
    // 0xb5ca38: StoreField: r1->field_2f = r0
    //     0xb5ca38: stur            w0, [x1, #0x2f]
    // 0xb5ca3c: ldur            x0, [fp, #-0x40]
    // 0xb5ca40: StoreField: r1->field_37 = r0
    //     0xb5ca40: stur            w0, [x1, #0x37]
    // 0xb5ca44: r0 = TextButtonTheme()
    //     0xb5ca44: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb5ca48: mov             x2, x0
    // 0xb5ca4c: ldur            x0, [fp, #-0x18]
    // 0xb5ca50: stur            x2, [fp, #-8]
    // 0xb5ca54: StoreField: r2->field_f = r0
    //     0xb5ca54: stur            w0, [x2, #0xf]
    // 0xb5ca58: ldur            x0, [fp, #-0x20]
    // 0xb5ca5c: StoreField: r2->field_b = r0
    //     0xb5ca5c: stur            w0, [x2, #0xb]
    // 0xb5ca60: r1 = <FlexParentData>
    //     0xb5ca60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5ca64: ldr             x1, [x1, #0xe00]
    // 0xb5ca68: r0 = Expanded()
    //     0xb5ca68: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb5ca6c: mov             x3, x0
    // 0xb5ca70: r0 = 1
    //     0xb5ca70: movz            x0, #0x1
    // 0xb5ca74: stur            x3, [fp, #-0x18]
    // 0xb5ca78: StoreField: r3->field_13 = r0
    //     0xb5ca78: stur            x0, [x3, #0x13]
    // 0xb5ca7c: r0 = Instance_FlexFit
    //     0xb5ca7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb5ca80: ldr             x0, [x0, #0xe08]
    // 0xb5ca84: StoreField: r3->field_1b = r0
    //     0xb5ca84: stur            w0, [x3, #0x1b]
    // 0xb5ca88: ldur            x0, [fp, #-8]
    // 0xb5ca8c: StoreField: r3->field_b = r0
    //     0xb5ca8c: stur            w0, [x3, #0xb]
    // 0xb5ca90: r1 = Null
    //     0xb5ca90: mov             x1, NULL
    // 0xb5ca94: r2 = 6
    //     0xb5ca94: movz            x2, #0x6
    // 0xb5ca98: r0 = AllocateArray()
    //     0xb5ca98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5ca9c: mov             x2, x0
    // 0xb5caa0: ldur            x0, [fp, #-0x10]
    // 0xb5caa4: stur            x2, [fp, #-8]
    // 0xb5caa8: StoreField: r2->field_f = r0
    //     0xb5caa8: stur            w0, [x2, #0xf]
    // 0xb5caac: r16 = Instance_SizedBox
    //     0xb5caac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb5cab0: ldr             x16, [x16, #0xb20]
    // 0xb5cab4: StoreField: r2->field_13 = r16
    //     0xb5cab4: stur            w16, [x2, #0x13]
    // 0xb5cab8: ldur            x0, [fp, #-0x18]
    // 0xb5cabc: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5cabc: stur            w0, [x2, #0x17]
    // 0xb5cac0: r1 = <Widget>
    //     0xb5cac0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5cac4: r0 = AllocateGrowableArray()
    //     0xb5cac4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5cac8: mov             x1, x0
    // 0xb5cacc: ldur            x0, [fp, #-8]
    // 0xb5cad0: stur            x1, [fp, #-0x10]
    // 0xb5cad4: StoreField: r1->field_f = r0
    //     0xb5cad4: stur            w0, [x1, #0xf]
    // 0xb5cad8: r0 = 6
    //     0xb5cad8: movz            x0, #0x6
    // 0xb5cadc: StoreField: r1->field_b = r0
    //     0xb5cadc: stur            w0, [x1, #0xb]
    // 0xb5cae0: r0 = Row()
    //     0xb5cae0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5cae4: mov             x1, x0
    // 0xb5cae8: r0 = Instance_Axis
    //     0xb5cae8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5caec: stur            x1, [fp, #-8]
    // 0xb5caf0: StoreField: r1->field_f = r0
    //     0xb5caf0: stur            w0, [x1, #0xf]
    // 0xb5caf4: r0 = Instance_MainAxisAlignment
    //     0xb5caf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb5caf8: ldr             x0, [x0, #0xa8]
    // 0xb5cafc: StoreField: r1->field_13 = r0
    //     0xb5cafc: stur            w0, [x1, #0x13]
    // 0xb5cb00: r0 = Instance_MainAxisSize
    //     0xb5cb00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5cb04: ldr             x0, [x0, #0xa10]
    // 0xb5cb08: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5cb08: stur            w0, [x1, #0x17]
    // 0xb5cb0c: r0 = Instance_CrossAxisAlignment
    //     0xb5cb0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5cb10: ldr             x0, [x0, #0x890]
    // 0xb5cb14: StoreField: r1->field_1b = r0
    //     0xb5cb14: stur            w0, [x1, #0x1b]
    // 0xb5cb18: r2 = Instance_VerticalDirection
    //     0xb5cb18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5cb1c: ldr             x2, [x2, #0xa20]
    // 0xb5cb20: StoreField: r1->field_23 = r2
    //     0xb5cb20: stur            w2, [x1, #0x23]
    // 0xb5cb24: r3 = Instance_Clip
    //     0xb5cb24: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5cb28: ldr             x3, [x3, #0x38]
    // 0xb5cb2c: StoreField: r1->field_2b = r3
    //     0xb5cb2c: stur            w3, [x1, #0x2b]
    // 0xb5cb30: StoreField: r1->field_2f = rZR
    //     0xb5cb30: stur            xzr, [x1, #0x2f]
    // 0xb5cb34: ldur            x4, [fp, #-0x10]
    // 0xb5cb38: StoreField: r1->field_b = r4
    //     0xb5cb38: stur            w4, [x1, #0xb]
    // 0xb5cb3c: r0 = Padding()
    //     0xb5cb3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5cb40: mov             x3, x0
    // 0xb5cb44: r0 = Instance_EdgeInsets
    //     0xb5cb44: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xb5cb48: ldr             x0, [x0, #0x858]
    // 0xb5cb4c: stur            x3, [fp, #-0x10]
    // 0xb5cb50: StoreField: r3->field_f = r0
    //     0xb5cb50: stur            w0, [x3, #0xf]
    // 0xb5cb54: ldur            x0, [fp, #-8]
    // 0xb5cb58: StoreField: r3->field_b = r0
    //     0xb5cb58: stur            w0, [x3, #0xb]
    // 0xb5cb5c: r1 = Null
    //     0xb5cb5c: mov             x1, NULL
    // 0xb5cb60: r2 = 8
    //     0xb5cb60: movz            x2, #0x8
    // 0xb5cb64: r0 = AllocateArray()
    //     0xb5cb64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5cb68: mov             x2, x0
    // 0xb5cb6c: ldur            x0, [fp, #-0x28]
    // 0xb5cb70: stur            x2, [fp, #-8]
    // 0xb5cb74: StoreField: r2->field_f = r0
    //     0xb5cb74: stur            w0, [x2, #0xf]
    // 0xb5cb78: r16 = Instance_SizedBox
    //     0xb5cb78: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xb5cb7c: ldr             x16, [x16, #0x578]
    // 0xb5cb80: StoreField: r2->field_13 = r16
    //     0xb5cb80: stur            w16, [x2, #0x13]
    // 0xb5cb84: ldur            x0, [fp, #-0x30]
    // 0xb5cb88: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5cb88: stur            w0, [x2, #0x17]
    // 0xb5cb8c: ldur            x0, [fp, #-0x10]
    // 0xb5cb90: StoreField: r2->field_1b = r0
    //     0xb5cb90: stur            w0, [x2, #0x1b]
    // 0xb5cb94: r1 = <Widget>
    //     0xb5cb94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5cb98: r0 = AllocateGrowableArray()
    //     0xb5cb98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5cb9c: mov             x1, x0
    // 0xb5cba0: ldur            x0, [fp, #-8]
    // 0xb5cba4: stur            x1, [fp, #-0x10]
    // 0xb5cba8: StoreField: r1->field_f = r0
    //     0xb5cba8: stur            w0, [x1, #0xf]
    // 0xb5cbac: r0 = 8
    //     0xb5cbac: movz            x0, #0x8
    // 0xb5cbb0: StoreField: r1->field_b = r0
    //     0xb5cbb0: stur            w0, [x1, #0xb]
    // 0xb5cbb4: r0 = Column()
    //     0xb5cbb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5cbb8: mov             x1, x0
    // 0xb5cbbc: r0 = Instance_Axis
    //     0xb5cbbc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5cbc0: stur            x1, [fp, #-8]
    // 0xb5cbc4: StoreField: r1->field_f = r0
    //     0xb5cbc4: stur            w0, [x1, #0xf]
    // 0xb5cbc8: r0 = Instance_MainAxisAlignment
    //     0xb5cbc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5cbcc: ldr             x0, [x0, #0xa08]
    // 0xb5cbd0: StoreField: r1->field_13 = r0
    //     0xb5cbd0: stur            w0, [x1, #0x13]
    // 0xb5cbd4: r0 = Instance_MainAxisSize
    //     0xb5cbd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb5cbd8: ldr             x0, [x0, #0xdd0]
    // 0xb5cbdc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5cbdc: stur            w0, [x1, #0x17]
    // 0xb5cbe0: r0 = Instance_CrossAxisAlignment
    //     0xb5cbe0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb5cbe4: ldr             x0, [x0, #0x890]
    // 0xb5cbe8: StoreField: r1->field_1b = r0
    //     0xb5cbe8: stur            w0, [x1, #0x1b]
    // 0xb5cbec: r0 = Instance_VerticalDirection
    //     0xb5cbec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5cbf0: ldr             x0, [x0, #0xa20]
    // 0xb5cbf4: StoreField: r1->field_23 = r0
    //     0xb5cbf4: stur            w0, [x1, #0x23]
    // 0xb5cbf8: r0 = Instance_Clip
    //     0xb5cbf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5cbfc: ldr             x0, [x0, #0x38]
    // 0xb5cc00: StoreField: r1->field_2b = r0
    //     0xb5cc00: stur            w0, [x1, #0x2b]
    // 0xb5cc04: StoreField: r1->field_2f = rZR
    //     0xb5cc04: stur            xzr, [x1, #0x2f]
    // 0xb5cc08: ldur            x0, [fp, #-0x10]
    // 0xb5cc0c: StoreField: r1->field_b = r0
    //     0xb5cc0c: stur            w0, [x1, #0xb]
    // 0xb5cc10: r0 = Padding()
    //     0xb5cc10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5cc14: mov             x1, x0
    // 0xb5cc18: r0 = Instance_EdgeInsets
    //     0xb5cc18: add             x0, PP, #0x55, lsl #12  ; [pp+0x55a40] Obj!EdgeInsets@d58311
    //     0xb5cc1c: ldr             x0, [x0, #0xa40]
    // 0xb5cc20: stur            x1, [fp, #-0x10]
    // 0xb5cc24: StoreField: r1->field_f = r0
    //     0xb5cc24: stur            w0, [x1, #0xf]
    // 0xb5cc28: ldur            x0, [fp, #-8]
    // 0xb5cc2c: StoreField: r1->field_b = r0
    //     0xb5cc2c: stur            w0, [x1, #0xb]
    // 0xb5cc30: r0 = Container()
    //     0xb5cc30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5cc34: stur            x0, [fp, #-8]
    // 0xb5cc38: r16 = Instance_BoxDecoration
    //     0xb5cc38: add             x16, PP, #0x55, lsl #12  ; [pp+0x55fe0] Obj!BoxDecoration@d64981
    //     0xb5cc3c: ldr             x16, [x16, #0xfe0]
    // 0xb5cc40: ldur            lr, [fp, #-0x10]
    // 0xb5cc44: stp             lr, x16, [SP]
    // 0xb5cc48: mov             x1, x0
    // 0xb5cc4c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb5cc4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb5cc50: ldr             x4, [x4, #0x88]
    // 0xb5cc54: r0 = Container()
    //     0xb5cc54: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5cc58: ldur            x0, [fp, #-8]
    // 0xb5cc5c: LeaveFrame
    //     0xb5cc5c: mov             SP, fp
    //     0xb5cc60: ldp             fp, lr, [SP], #0x10
    // 0xb5cc64: ret
    //     0xb5cc64: ret             
    // 0xb5cc68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5cc68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5cc6c: b               #0xb5c3b0
    // 0xb5cc70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5cc70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5cc74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5cc74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5cc78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5cc78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb5cc9c, size: 0xac
    // 0xb5cc9c: EnterFrame
    //     0xb5cc9c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5cca0: mov             fp, SP
    // 0xb5cca4: AllocStack(0x10)
    //     0xb5cca4: sub             SP, SP, #0x10
    // 0xb5cca8: SetupParameters()
    //     0xb5cca8: ldr             x0, [fp, #0x10]
    //     0xb5ccac: ldur            w1, [x0, #0x17]
    //     0xb5ccb0: add             x1, x1, HEAP, lsl #32
    // 0xb5ccb4: CheckStackOverflow
    //     0xb5ccb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5ccb8: cmp             SP, x16
    //     0xb5ccbc: b.ls            #0xb5cd3c
    // 0xb5ccc0: LoadField: r0 = r1->field_f
    //     0xb5ccc0: ldur            w0, [x1, #0xf]
    // 0xb5ccc4: DecompressPointer r0
    //     0xb5ccc4: add             x0, x0, HEAP, lsl #32
    // 0xb5ccc8: LoadField: r1 = r0->field_b
    //     0xb5ccc8: ldur            w1, [x0, #0xb]
    // 0xb5cccc: DecompressPointer r1
    //     0xb5cccc: add             x1, x1, HEAP, lsl #32
    // 0xb5ccd0: cmp             w1, NULL
    // 0xb5ccd4: b.eq            #0xb5cd44
    // 0xb5ccd8: LoadField: r0 = r1->field_13
    //     0xb5ccd8: ldur            w0, [x1, #0x13]
    // 0xb5ccdc: DecompressPointer r0
    //     0xb5ccdc: add             x0, x0, HEAP, lsl #32
    // 0xb5cce0: LoadField: r2 = r1->field_f
    //     0xb5cce0: ldur            w2, [x1, #0xf]
    // 0xb5cce4: DecompressPointer r2
    //     0xb5cce4: add             x2, x2, HEAP, lsl #32
    // 0xb5cce8: stp             x0, x2, [SP]
    // 0xb5ccec: r4 = 0
    //     0xb5ccec: movz            x4, #0
    // 0xb5ccf0: ldr             x0, [SP, #8]
    // 0xb5ccf4: r16 = UnlinkedCall_0x613b5c
    //     0xb5ccf4: add             x16, PP, #0x55, lsl #12  ; [pp+0x55fe8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5ccf8: add             x16, x16, #0xfe8
    // 0xb5ccfc: ldp             x5, lr, [x16]
    // 0xb5cd00: blr             lr
    // 0xb5cd04: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb5cd04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb5cd08: ldr             x0, [x0, #0x1c80]
    //     0xb5cd0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb5cd10: cmp             w0, w16
    //     0xb5cd14: b.ne            #0xb5cd20
    //     0xb5cd18: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb5cd1c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb5cd20: str             NULL, [SP]
    // 0xb5cd24: r4 = const [0x1, 0, 0, 0, null]
    //     0xb5cd24: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb5cd28: r0 = GetNavigation.back()
    //     0xb5cd28: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xb5cd2c: r0 = Null
    //     0xb5cd2c: mov             x0, NULL
    // 0xb5cd30: LeaveFrame
    //     0xb5cd30: mov             SP, fp
    //     0xb5cd34: ldp             fp, lr, [SP], #0x10
    // 0xb5cd38: ret
    //     0xb5cd38: ret             
    // 0xb5cd3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5cd3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5cd40: b               #0xb5ccc0
    // 0xb5cd44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5cd44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4083, size: 0x18, field offset: 0xc
//   const constructor, 
class CancelExchangeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f1b4, size: 0x24
    // 0xc7f1b4: EnterFrame
    //     0xc7f1b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f1b8: mov             fp, SP
    // 0xc7f1bc: mov             x0, x1
    // 0xc7f1c0: r1 = <CancelExchangeBottomSheet>
    //     0xc7f1c0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48858] TypeArguments: <CancelExchangeBottomSheet>
    //     0xc7f1c4: ldr             x1, [x1, #0x858]
    // 0xc7f1c8: r0 = _CancelExchangeBottomSheetState()
    //     0xc7f1c8: bl              #0xc7f1d8  ; Allocate_CancelExchangeBottomSheetStateStub -> _CancelExchangeBottomSheetState (size=0x14)
    // 0xc7f1cc: LeaveFrame
    //     0xc7f1cc: mov             SP, fp
    //     0xc7f1d0: ldp             fp, lr, [SP], #0x10
    // 0xc7f1d4: ret
    //     0xc7f1d4: ret             
  }
}
