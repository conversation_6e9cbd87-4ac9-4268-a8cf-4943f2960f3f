// lib: , url: package:customer_app/app/presentation/views/glass/image/enlarge_image_view.dart

// class id: 1049409, size: 0x8
class :: {
}

// class id: 4565, size: 0x14, field offset: 0x14
//   const constructor, 
class EnlargeImageView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14ed8dc, size: 0x58
    // 0x14ed8dc: EnterFrame
    //     0x14ed8dc: stp             fp, lr, [SP, #-0x10]!
    //     0x14ed8e0: mov             fp, SP
    // 0x14ed8e4: AllocStack(0x10)
    //     0x14ed8e4: sub             SP, SP, #0x10
    // 0x14ed8e8: SetupParameters(EnlargeImageView this /* r1 => r1, fp-0x8 */)
    //     0x14ed8e8: stur            x1, [fp, #-8]
    // 0x14ed8ec: r1 = 1
    //     0x14ed8ec: movz            x1, #0x1
    // 0x14ed8f0: r0 = AllocateContext()
    //     0x14ed8f0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14ed8f4: mov             x1, x0
    // 0x14ed8f8: ldur            x0, [fp, #-8]
    // 0x14ed8fc: stur            x1, [fp, #-0x10]
    // 0x14ed900: StoreField: r1->field_f = r0
    //     0x14ed900: stur            w0, [x1, #0xf]
    // 0x14ed904: r0 = Obx()
    //     0x14ed904: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14ed908: ldur            x2, [fp, #-0x10]
    // 0x14ed90c: r1 = Function '<anonymous closure>':.
    //     0x14ed90c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40358] AnonymousClosure: (0x14ed934), in [package:customer_app/app/presentation/views/glass/image/enlarge_image_view.dart] EnlargeImageView::body (0x14ed8dc)
    //     0x14ed910: ldr             x1, [x1, #0x358]
    // 0x14ed914: stur            x0, [fp, #-8]
    // 0x14ed918: r0 = AllocateClosure()
    //     0x14ed918: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14ed91c: mov             x1, x0
    // 0x14ed920: ldur            x0, [fp, #-8]
    // 0x14ed924: StoreField: r0->field_b = r1
    //     0x14ed924: stur            w1, [x0, #0xb]
    // 0x14ed928: LeaveFrame
    //     0x14ed928: mov             SP, fp
    //     0x14ed92c: ldp             fp, lr, [SP], #0x10
    // 0x14ed930: ret
    //     0x14ed930: ret             
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x14ed934, size: 0x354
    // 0x14ed934: EnterFrame
    //     0x14ed934: stp             fp, lr, [SP, #-0x10]!
    //     0x14ed938: mov             fp, SP
    // 0x14ed93c: AllocStack(0x48)
    //     0x14ed93c: sub             SP, SP, #0x48
    // 0x14ed940: SetupParameters()
    //     0x14ed940: ldr             x0, [fp, #0x10]
    //     0x14ed944: ldur            w3, [x0, #0x17]
    //     0x14ed948: add             x3, x3, HEAP, lsl #32
    //     0x14ed94c: stur            x3, [fp, #-8]
    // 0x14ed950: CheckStackOverflow
    //     0x14ed950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ed954: cmp             SP, x16
    //     0x14ed958: b.ls            #0x14edc80
    // 0x14ed95c: r16 = 0.500000
    //     0x14ed95c: ldr             x16, [PP, #0x47c0]  ; [pp+0x47c0] 0.5
    // 0x14ed960: str             x16, [SP]
    // 0x14ed964: r1 = Null
    //     0x14ed964: mov             x1, NULL
    // 0x14ed968: r2 = Instance_Color
    //     0x14ed968: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14ed96c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x14ed96c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x14ed970: ldr             x4, [x4, #0x108]
    // 0x14ed974: r0 = Border.all()
    //     0x14ed974: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14ed978: stur            x0, [fp, #-0x10]
    // 0x14ed97c: r0 = BoxDecoration()
    //     0x14ed97c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14ed980: mov             x1, x0
    // 0x14ed984: r0 = Instance_Color
    //     0x14ed984: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x14ed988: stur            x1, [fp, #-0x18]
    // 0x14ed98c: StoreField: r1->field_7 = r0
    //     0x14ed98c: stur            w0, [x1, #7]
    // 0x14ed990: ldur            x0, [fp, #-0x10]
    // 0x14ed994: StoreField: r1->field_f = r0
    //     0x14ed994: stur            w0, [x1, #0xf]
    // 0x14ed998: r0 = Instance_BoxShape
    //     0x14ed998: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x14ed99c: ldr             x0, [x0, #0x970]
    // 0x14ed9a0: StoreField: r1->field_23 = r0
    //     0x14ed9a0: stur            w0, [x1, #0x23]
    // 0x14ed9a4: r0 = Container()
    //     0x14ed9a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14ed9a8: stur            x0, [fp, #-0x10]
    // 0x14ed9ac: r16 = 35.000000
    //     0x14ed9ac: add             x16, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0x14ed9b0: ldr             x16, [x16, #0x2b0]
    // 0x14ed9b4: r30 = 35.000000
    //     0x14ed9b4: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0x14ed9b8: ldr             lr, [lr, #0x2b0]
    // 0x14ed9bc: stp             lr, x16, [SP, #0x10]
    // 0x14ed9c0: ldur            x16, [fp, #-0x18]
    // 0x14ed9c4: r30 = Instance_Icon
    //     0x14ed9c4: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0x14ed9c8: ldr             lr, [lr, #0x2b8]
    // 0x14ed9cc: stp             lr, x16, [SP]
    // 0x14ed9d0: mov             x1, x0
    // 0x14ed9d4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14ed9d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14ed9d8: ldr             x4, [x4, #0x870]
    // 0x14ed9dc: r0 = Container()
    //     0x14ed9dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14ed9e0: r0 = InkWell()
    //     0x14ed9e0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14ed9e4: mov             x3, x0
    // 0x14ed9e8: ldur            x0, [fp, #-0x10]
    // 0x14ed9ec: stur            x3, [fp, #-0x18]
    // 0x14ed9f0: StoreField: r3->field_b = r0
    //     0x14ed9f0: stur            w0, [x3, #0xb]
    // 0x14ed9f4: r1 = Function '<anonymous closure>':.
    //     0x14ed9f4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40360] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14ed9f8: ldr             x1, [x1, #0x360]
    // 0x14ed9fc: r2 = Null
    //     0x14ed9fc: mov             x2, NULL
    // 0x14eda00: r0 = AllocateClosure()
    //     0x14eda00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14eda04: mov             x1, x0
    // 0x14eda08: ldur            x0, [fp, #-0x18]
    // 0x14eda0c: StoreField: r0->field_f = r1
    //     0x14eda0c: stur            w1, [x0, #0xf]
    // 0x14eda10: r1 = true
    //     0x14eda10: add             x1, NULL, #0x20  ; true
    // 0x14eda14: StoreField: r0->field_43 = r1
    //     0x14eda14: stur            w1, [x0, #0x43]
    // 0x14eda18: r2 = Instance_BoxShape
    //     0x14eda18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14eda1c: ldr             x2, [x2, #0x80]
    // 0x14eda20: StoreField: r0->field_47 = r2
    //     0x14eda20: stur            w2, [x0, #0x47]
    // 0x14eda24: StoreField: r0->field_6f = r1
    //     0x14eda24: stur            w1, [x0, #0x6f]
    // 0x14eda28: r2 = false
    //     0x14eda28: add             x2, NULL, #0x30  ; false
    // 0x14eda2c: StoreField: r0->field_73 = r2
    //     0x14eda2c: stur            w2, [x0, #0x73]
    // 0x14eda30: StoreField: r0->field_83 = r1
    //     0x14eda30: stur            w1, [x0, #0x83]
    // 0x14eda34: StoreField: r0->field_7b = r2
    //     0x14eda34: stur            w2, [x0, #0x7b]
    // 0x14eda38: r0 = Align()
    //     0x14eda38: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x14eda3c: mov             x1, x0
    // 0x14eda40: r0 = Instance_Alignment
    //     0x14eda40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x14eda44: ldr             x0, [x0, #0x950]
    // 0x14eda48: stur            x1, [fp, #-0x10]
    // 0x14eda4c: StoreField: r1->field_f = r0
    //     0x14eda4c: stur            w0, [x1, #0xf]
    // 0x14eda50: ldur            x0, [fp, #-0x18]
    // 0x14eda54: StoreField: r1->field_b = r0
    //     0x14eda54: stur            w0, [x1, #0xb]
    // 0x14eda58: r0 = Padding()
    //     0x14eda58: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14eda5c: mov             x2, x0
    // 0x14eda60: r0 = Instance_EdgeInsets
    //     0x14eda60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14eda64: ldr             x0, [x0, #0x980]
    // 0x14eda68: stur            x2, [fp, #-0x18]
    // 0x14eda6c: StoreField: r2->field_f = r0
    //     0x14eda6c: stur            w0, [x2, #0xf]
    // 0x14eda70: ldur            x0, [fp, #-0x10]
    // 0x14eda74: StoreField: r2->field_b = r0
    //     0x14eda74: stur            w0, [x2, #0xb]
    // 0x14eda78: ldur            x0, [fp, #-8]
    // 0x14eda7c: LoadField: r1 = r0->field_f
    //     0x14eda7c: ldur            w1, [x0, #0xf]
    // 0x14eda80: DecompressPointer r1
    //     0x14eda80: add             x1, x1, HEAP, lsl #32
    // 0x14eda84: r0 = controller()
    //     0x14eda84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14eda88: LoadField: r1 = r0->field_4b
    //     0x14eda88: ldur            w1, [x0, #0x4b]
    // 0x14eda8c: DecompressPointer r1
    //     0x14eda8c: add             x1, x1, HEAP, lsl #32
    // 0x14eda90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14eda90: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14eda94: r0 = toList()
    //     0x14eda94: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14eda98: LoadField: r3 = r0->field_b
    //     0x14eda98: ldur            w3, [x0, #0xb]
    // 0x14eda9c: ldur            x2, [fp, #-8]
    // 0x14edaa0: stur            x3, [fp, #-0x10]
    // 0x14edaa4: r1 = Function '<anonymous closure>':.
    //     0x14edaa4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40368] AnonymousClosure: (0x14ede34), in [package:customer_app/app/presentation/views/glass/image/enlarge_image_view.dart] EnlargeImageView::body (0x14ed8dc)
    //     0x14edaa8: ldr             x1, [x1, #0x368]
    // 0x14edaac: r0 = AllocateClosure()
    //     0x14edaac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14edab0: stur            x0, [fp, #-0x20]
    // 0x14edab4: r0 = ListView()
    //     0x14edab4: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14edab8: stur            x0, [fp, #-0x28]
    // 0x14edabc: r16 = true
    //     0x14edabc: add             x16, NULL, #0x20  ; true
    // 0x14edac0: r30 = Instance_Axis
    //     0x14edac0: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14edac4: stp             lr, x16, [SP]
    // 0x14edac8: mov             x1, x0
    // 0x14edacc: ldur            x2, [fp, #-0x20]
    // 0x14edad0: ldur            x3, [fp, #-0x10]
    // 0x14edad4: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0x14edad4: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0x14edad8: ldr             x4, [x4, #0x2d0]
    // 0x14edadc: r0 = ListView.builder()
    //     0x14edadc: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14edae0: r0 = Center()
    //     0x14edae0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x14edae4: mov             x2, x0
    // 0x14edae8: r0 = Instance_Alignment
    //     0x14edae8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x14edaec: ldr             x0, [x0, #0xb10]
    // 0x14edaf0: stur            x2, [fp, #-0x10]
    // 0x14edaf4: StoreField: r2->field_f = r0
    //     0x14edaf4: stur            w0, [x2, #0xf]
    // 0x14edaf8: ldur            x0, [fp, #-0x28]
    // 0x14edafc: StoreField: r2->field_b = r0
    //     0x14edafc: stur            w0, [x2, #0xb]
    // 0x14edb00: r1 = <FlexParentData>
    //     0x14edb00: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14edb04: ldr             x1, [x1, #0xe00]
    // 0x14edb08: r0 = Expanded()
    //     0x14edb08: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x14edb0c: mov             x2, x0
    // 0x14edb10: r0 = 5
    //     0x14edb10: movz            x0, #0x5
    // 0x14edb14: stur            x2, [fp, #-0x20]
    // 0x14edb18: StoreField: r2->field_13 = r0
    //     0x14edb18: stur            x0, [x2, #0x13]
    // 0x14edb1c: r0 = Instance_FlexFit
    //     0x14edb1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x14edb20: ldr             x0, [x0, #0xe08]
    // 0x14edb24: StoreField: r2->field_1b = r0
    //     0x14edb24: stur            w0, [x2, #0x1b]
    // 0x14edb28: ldur            x0, [fp, #-0x10]
    // 0x14edb2c: StoreField: r2->field_b = r0
    //     0x14edb2c: stur            w0, [x2, #0xb]
    // 0x14edb30: ldur            x0, [fp, #-8]
    // 0x14edb34: LoadField: r1 = r0->field_f
    //     0x14edb34: ldur            w1, [x0, #0xf]
    // 0x14edb38: DecompressPointer r1
    //     0x14edb38: add             x1, x1, HEAP, lsl #32
    // 0x14edb3c: r0 = controller()
    //     0x14edb3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14edb40: LoadField: r1 = r0->field_4b
    //     0x14edb40: ldur            w1, [x0, #0x4b]
    // 0x14edb44: DecompressPointer r1
    //     0x14edb44: add             x1, x1, HEAP, lsl #32
    // 0x14edb48: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14edb48: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14edb4c: r0 = toList()
    //     0x14edb4c: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14edb50: LoadField: r3 = r0->field_b
    //     0x14edb50: ldur            w3, [x0, #0xb]
    // 0x14edb54: ldur            x2, [fp, #-8]
    // 0x14edb58: stur            x3, [fp, #-0x10]
    // 0x14edb5c: r1 = Function '<anonymous closure>':.
    //     0x14edb5c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40370] AnonymousClosure: (0x14edc88), in [package:customer_app/app/presentation/views/glass/image/enlarge_image_view.dart] EnlargeImageView::body (0x14ed8dc)
    //     0x14edb60: ldr             x1, [x1, #0x370]
    // 0x14edb64: r0 = AllocateClosure()
    //     0x14edb64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14edb68: stur            x0, [fp, #-8]
    // 0x14edb6c: r0 = ListView()
    //     0x14edb6c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14edb70: stur            x0, [fp, #-0x28]
    // 0x14edb74: r16 = true
    //     0x14edb74: add             x16, NULL, #0x20  ; true
    // 0x14edb78: r30 = Instance_Axis
    //     0x14edb78: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14edb7c: stp             lr, x16, [SP]
    // 0x14edb80: mov             x1, x0
    // 0x14edb84: ldur            x2, [fp, #-8]
    // 0x14edb88: ldur            x3, [fp, #-0x10]
    // 0x14edb8c: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0x14edb8c: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0x14edb90: ldr             x4, [x4, #0x2d0]
    // 0x14edb94: r0 = ListView.builder()
    //     0x14edb94: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x14edb98: r1 = <FlexParentData>
    //     0x14edb98: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x14edb9c: ldr             x1, [x1, #0xe00]
    // 0x14edba0: r0 = Flexible()
    //     0x14edba0: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x14edba4: mov             x3, x0
    // 0x14edba8: r0 = 1
    //     0x14edba8: movz            x0, #0x1
    // 0x14edbac: stur            x3, [fp, #-8]
    // 0x14edbb0: StoreField: r3->field_13 = r0
    //     0x14edbb0: stur            x0, [x3, #0x13]
    // 0x14edbb4: r0 = Instance_FlexFit
    //     0x14edbb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x14edbb8: ldr             x0, [x0, #0xe20]
    // 0x14edbbc: StoreField: r3->field_1b = r0
    //     0x14edbbc: stur            w0, [x3, #0x1b]
    // 0x14edbc0: ldur            x0, [fp, #-0x28]
    // 0x14edbc4: StoreField: r3->field_b = r0
    //     0x14edbc4: stur            w0, [x3, #0xb]
    // 0x14edbc8: r1 = Null
    //     0x14edbc8: mov             x1, NULL
    // 0x14edbcc: r2 = 8
    //     0x14edbcc: movz            x2, #0x8
    // 0x14edbd0: r0 = AllocateArray()
    //     0x14edbd0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14edbd4: mov             x2, x0
    // 0x14edbd8: ldur            x0, [fp, #-0x18]
    // 0x14edbdc: stur            x2, [fp, #-0x10]
    // 0x14edbe0: StoreField: r2->field_f = r0
    //     0x14edbe0: stur            w0, [x2, #0xf]
    // 0x14edbe4: ldur            x0, [fp, #-0x20]
    // 0x14edbe8: StoreField: r2->field_13 = r0
    //     0x14edbe8: stur            w0, [x2, #0x13]
    // 0x14edbec: r16 = Instance_Divider
    //     0x14edbec: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0x14edbf0: ldr             x16, [x16, #0x2e0]
    // 0x14edbf4: ArrayStore: r2[0] = r16  ; List_4
    //     0x14edbf4: stur            w16, [x2, #0x17]
    // 0x14edbf8: ldur            x0, [fp, #-8]
    // 0x14edbfc: StoreField: r2->field_1b = r0
    //     0x14edbfc: stur            w0, [x2, #0x1b]
    // 0x14edc00: r1 = <Widget>
    //     0x14edc00: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14edc04: r0 = AllocateGrowableArray()
    //     0x14edc04: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14edc08: mov             x1, x0
    // 0x14edc0c: ldur            x0, [fp, #-0x10]
    // 0x14edc10: stur            x1, [fp, #-8]
    // 0x14edc14: StoreField: r1->field_f = r0
    //     0x14edc14: stur            w0, [x1, #0xf]
    // 0x14edc18: r0 = 8
    //     0x14edc18: movz            x0, #0x8
    // 0x14edc1c: StoreField: r1->field_b = r0
    //     0x14edc1c: stur            w0, [x1, #0xb]
    // 0x14edc20: r0 = Column()
    //     0x14edc20: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14edc24: r1 = Instance_Axis
    //     0x14edc24: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14edc28: StoreField: r0->field_f = r1
    //     0x14edc28: stur            w1, [x0, #0xf]
    // 0x14edc2c: r1 = Instance_MainAxisAlignment
    //     0x14edc2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14edc30: ldr             x1, [x1, #0xa08]
    // 0x14edc34: StoreField: r0->field_13 = r1
    //     0x14edc34: stur            w1, [x0, #0x13]
    // 0x14edc38: r1 = Instance_MainAxisSize
    //     0x14edc38: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14edc3c: ldr             x1, [x1, #0xa10]
    // 0x14edc40: ArrayStore: r0[0] = r1  ; List_4
    //     0x14edc40: stur            w1, [x0, #0x17]
    // 0x14edc44: r1 = Instance_CrossAxisAlignment
    //     0x14edc44: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x14edc48: ldr             x1, [x1, #0x890]
    // 0x14edc4c: StoreField: r0->field_1b = r1
    //     0x14edc4c: stur            w1, [x0, #0x1b]
    // 0x14edc50: r1 = Instance_VerticalDirection
    //     0x14edc50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14edc54: ldr             x1, [x1, #0xa20]
    // 0x14edc58: StoreField: r0->field_23 = r1
    //     0x14edc58: stur            w1, [x0, #0x23]
    // 0x14edc5c: r1 = Instance_Clip
    //     0x14edc5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14edc60: ldr             x1, [x1, #0x38]
    // 0x14edc64: StoreField: r0->field_2b = r1
    //     0x14edc64: stur            w1, [x0, #0x2b]
    // 0x14edc68: StoreField: r0->field_2f = rZR
    //     0x14edc68: stur            xzr, [x0, #0x2f]
    // 0x14edc6c: ldur            x1, [fp, #-8]
    // 0x14edc70: StoreField: r0->field_b = r1
    //     0x14edc70: stur            w1, [x0, #0xb]
    // 0x14edc74: LeaveFrame
    //     0x14edc74: mov             SP, fp
    //     0x14edc78: ldp             fp, lr, [SP], #0x10
    // 0x14edc7c: ret
    //     0x14edc7c: ret             
    // 0x14edc80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14edc80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14edc84: b               #0x14ed95c
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14edc88, size: 0x1ac
    // 0x14edc88: EnterFrame
    //     0x14edc88: stp             fp, lr, [SP, #-0x10]!
    //     0x14edc8c: mov             fp, SP
    // 0x14edc90: AllocStack(0x40)
    //     0x14edc90: sub             SP, SP, #0x40
    // 0x14edc94: SetupParameters()
    //     0x14edc94: ldr             x0, [fp, #0x20]
    //     0x14edc98: ldur            w1, [x0, #0x17]
    //     0x14edc9c: add             x1, x1, HEAP, lsl #32
    // 0x14edca0: CheckStackOverflow
    //     0x14edca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14edca4: cmp             SP, x16
    //     0x14edca8: b.ls            #0x14ede24
    // 0x14edcac: LoadField: r0 = r1->field_f
    //     0x14edcac: ldur            w0, [x1, #0xf]
    // 0x14edcb0: DecompressPointer r0
    //     0x14edcb0: add             x0, x0, HEAP, lsl #32
    // 0x14edcb4: mov             x1, x0
    // 0x14edcb8: r0 = controller()
    //     0x14edcb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14edcbc: LoadField: r1 = r0->field_4b
    //     0x14edcbc: ldur            w1, [x0, #0x4b]
    // 0x14edcc0: DecompressPointer r1
    //     0x14edcc0: add             x1, x1, HEAP, lsl #32
    // 0x14edcc4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14edcc4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14edcc8: r0 = toList()
    //     0x14edcc8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14edccc: mov             x2, x0
    // 0x14edcd0: LoadField: r0 = r2->field_b
    //     0x14edcd0: ldur            w0, [x2, #0xb]
    // 0x14edcd4: ldr             x1, [fp, #0x10]
    // 0x14edcd8: r3 = LoadInt32Instr(r1)
    //     0x14edcd8: sbfx            x3, x1, #1, #0x1f
    //     0x14edcdc: tbz             w1, #0, #0x14edce4
    //     0x14edce0: ldur            x3, [x1, #7]
    // 0x14edce4: r1 = LoadInt32Instr(r0)
    //     0x14edce4: sbfx            x1, x0, #1, #0x1f
    // 0x14edce8: mov             x0, x1
    // 0x14edcec: mov             x1, x3
    // 0x14edcf0: cmp             x1, x0
    // 0x14edcf4: b.hs            #0x14ede2c
    // 0x14edcf8: LoadField: r0 = r2->field_f
    //     0x14edcf8: ldur            w0, [x2, #0xf]
    // 0x14edcfc: DecompressPointer r0
    //     0x14edcfc: add             x0, x0, HEAP, lsl #32
    // 0x14edd00: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14edd00: add             x16, x0, x3, lsl #2
    //     0x14edd04: ldur            w1, [x16, #0xf]
    // 0x14edd08: DecompressPointer r1
    //     0x14edd08: add             x1, x1, HEAP, lsl #32
    // 0x14edd0c: cmp             w1, NULL
    // 0x14edd10: b.eq            #0x14ede30
    // 0x14edd14: LoadField: r0 = r1->field_2b
    //     0x14edd14: ldur            w0, [x1, #0x2b]
    // 0x14edd18: DecompressPointer r0
    //     0x14edd18: add             x0, x0, HEAP, lsl #32
    // 0x14edd1c: cmp             w0, NULL
    // 0x14edd20: b.ne            #0x14edd2c
    // 0x14edd24: r0 = Null
    //     0x14edd24: mov             x0, NULL
    // 0x14edd28: b               #0x14edd38
    // 0x14edd2c: LoadField: r1 = r0->field_b
    //     0x14edd2c: ldur            w1, [x0, #0xb]
    // 0x14edd30: DecompressPointer r1
    //     0x14edd30: add             x1, x1, HEAP, lsl #32
    // 0x14edd34: mov             x0, x1
    // 0x14edd38: cmp             w0, NULL
    // 0x14edd3c: b.ne            #0x14edd44
    // 0x14edd40: r0 = ""
    //     0x14edd40: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14edd44: stur            x0, [fp, #-8]
    // 0x14edd48: r1 = Function '<anonymous closure>':.
    //     0x14edd48: add             x1, PP, #0x40, lsl #12  ; [pp+0x40378] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14edd4c: ldr             x1, [x1, #0x378]
    // 0x14edd50: r2 = Null
    //     0x14edd50: mov             x2, NULL
    // 0x14edd54: r0 = AllocateClosure()
    //     0x14edd54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14edd58: r1 = Function '<anonymous closure>':.
    //     0x14edd58: add             x1, PP, #0x40, lsl #12  ; [pp+0x40380] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14edd5c: ldr             x1, [x1, #0x380]
    // 0x14edd60: r2 = Null
    //     0x14edd60: mov             x2, NULL
    // 0x14edd64: stur            x0, [fp, #-0x10]
    // 0x14edd68: r0 = AllocateClosure()
    //     0x14edd68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14edd6c: stur            x0, [fp, #-0x18]
    // 0x14edd70: r0 = CachedNetworkImage()
    //     0x14edd70: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14edd74: stur            x0, [fp, #-0x20]
    // 0x14edd78: r16 = 100.000000
    //     0x14edd78: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x14edd7c: r30 = 80.000000
    //     0x14edd7c: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x14edd80: ldr             lr, [lr, #0x2f8]
    // 0x14edd84: stp             lr, x16, [SP, #0x10]
    // 0x14edd88: ldur            x16, [fp, #-0x10]
    // 0x14edd8c: ldur            lr, [fp, #-0x18]
    // 0x14edd90: stp             lr, x16, [SP]
    // 0x14edd94: mov             x1, x0
    // 0x14edd98: ldur            x2, [fp, #-8]
    // 0x14edd9c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0x14edd9c: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0x14edda0: ldr             x4, [x4, #0x388]
    // 0x14edda4: r0 = CachedNetworkImage()
    //     0x14edda4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14edda8: r0 = InkWell()
    //     0x14edda8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14eddac: mov             x3, x0
    // 0x14eddb0: ldur            x0, [fp, #-0x20]
    // 0x14eddb4: stur            x3, [fp, #-8]
    // 0x14eddb8: StoreField: r3->field_b = r0
    //     0x14eddb8: stur            w0, [x3, #0xb]
    // 0x14eddbc: r1 = Function '<anonymous closure>':.
    //     0x14eddbc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40390] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x14eddc0: ldr             x1, [x1, #0x390]
    // 0x14eddc4: r2 = Null
    //     0x14eddc4: mov             x2, NULL
    // 0x14eddc8: r0 = AllocateClosure()
    //     0x14eddc8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14eddcc: mov             x1, x0
    // 0x14eddd0: ldur            x0, [fp, #-8]
    // 0x14eddd4: StoreField: r0->field_f = r1
    //     0x14eddd4: stur            w1, [x0, #0xf]
    // 0x14eddd8: r1 = true
    //     0x14eddd8: add             x1, NULL, #0x20  ; true
    // 0x14edddc: StoreField: r0->field_43 = r1
    //     0x14edddc: stur            w1, [x0, #0x43]
    // 0x14edde0: r2 = Instance_BoxShape
    //     0x14edde0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14edde4: ldr             x2, [x2, #0x80]
    // 0x14edde8: StoreField: r0->field_47 = r2
    //     0x14edde8: stur            w2, [x0, #0x47]
    // 0x14eddec: StoreField: r0->field_6f = r1
    //     0x14eddec: stur            w1, [x0, #0x6f]
    // 0x14eddf0: r2 = false
    //     0x14eddf0: add             x2, NULL, #0x30  ; false
    // 0x14eddf4: StoreField: r0->field_73 = r2
    //     0x14eddf4: stur            w2, [x0, #0x73]
    // 0x14eddf8: StoreField: r0->field_83 = r1
    //     0x14eddf8: stur            w1, [x0, #0x83]
    // 0x14eddfc: StoreField: r0->field_7b = r2
    //     0x14eddfc: stur            w2, [x0, #0x7b]
    // 0x14ede00: r0 = Padding()
    //     0x14ede00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14ede04: r1 = Instance_EdgeInsets
    //     0x14ede04: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14ede08: ldr             x1, [x1, #0x980]
    // 0x14ede0c: StoreField: r0->field_f = r1
    //     0x14ede0c: stur            w1, [x0, #0xf]
    // 0x14ede10: ldur            x1, [fp, #-8]
    // 0x14ede14: StoreField: r0->field_b = r1
    //     0x14ede14: stur            w1, [x0, #0xb]
    // 0x14ede18: LeaveFrame
    //     0x14ede18: mov             SP, fp
    //     0x14ede1c: ldp             fp, lr, [SP], #0x10
    // 0x14ede20: ret
    //     0x14ede20: ret             
    // 0x14ede24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ede24: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ede28: b               #0x14edcac
    // 0x14ede2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ede2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ede30: r0 = NullErrorSharedWithoutFPURegs()
    //     0x14ede30: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] InteractiveViewer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14ede34, size: 0x238
    // 0x14ede34: EnterFrame
    //     0x14ede34: stp             fp, lr, [SP, #-0x10]!
    //     0x14ede38: mov             fp, SP
    // 0x14ede3c: AllocStack(0x60)
    //     0x14ede3c: sub             SP, SP, #0x60
    // 0x14ede40: SetupParameters()
    //     0x14ede40: ldr             x0, [fp, #0x20]
    //     0x14ede44: ldur            w1, [x0, #0x17]
    //     0x14ede48: add             x1, x1, HEAP, lsl #32
    //     0x14ede4c: stur            x1, [fp, #-8]
    // 0x14ede50: CheckStackOverflow
    //     0x14ede50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14ede54: cmp             SP, x16
    //     0x14ede58: b.ls            #0x14ee024
    // 0x14ede5c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14ede5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14ede60: ldr             x0, [x0, #0x1c80]
    //     0x14ede64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14ede68: cmp             w0, w16
    //     0x14ede6c: b.ne            #0x14ede78
    //     0x14ede70: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14ede74: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14ede78: r0 = GetNavigation.size()
    //     0x14ede78: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14ede7c: LoadField: d0 = r0->field_f
    //     0x14ede7c: ldur            d0, [x0, #0xf]
    // 0x14ede80: stur            d0, [fp, #-0x38]
    // 0x14ede84: r0 = GetNavigation.size()
    //     0x14ede84: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x14ede88: LoadField: d0 = r0->field_7
    //     0x14ede88: ldur            d0, [x0, #7]
    // 0x14ede8c: ldur            x0, [fp, #-8]
    // 0x14ede90: stur            d0, [fp, #-0x40]
    // 0x14ede94: LoadField: r1 = r0->field_f
    //     0x14ede94: ldur            w1, [x0, #0xf]
    // 0x14ede98: DecompressPointer r1
    //     0x14ede98: add             x1, x1, HEAP, lsl #32
    // 0x14ede9c: r0 = controller()
    //     0x14ede9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14edea0: LoadField: r1 = r0->field_4b
    //     0x14edea0: ldur            w1, [x0, #0x4b]
    // 0x14edea4: DecompressPointer r1
    //     0x14edea4: add             x1, x1, HEAP, lsl #32
    // 0x14edea8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14edea8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14edeac: r0 = toList()
    //     0x14edeac: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14edeb0: mov             x2, x0
    // 0x14edeb4: LoadField: r0 = r2->field_b
    //     0x14edeb4: ldur            w0, [x2, #0xb]
    // 0x14edeb8: ldr             x1, [fp, #0x10]
    // 0x14edebc: r3 = LoadInt32Instr(r1)
    //     0x14edebc: sbfx            x3, x1, #1, #0x1f
    //     0x14edec0: tbz             w1, #0, #0x14edec8
    //     0x14edec4: ldur            x3, [x1, #7]
    // 0x14edec8: r1 = LoadInt32Instr(r0)
    //     0x14edec8: sbfx            x1, x0, #1, #0x1f
    // 0x14edecc: mov             x0, x1
    // 0x14eded0: mov             x1, x3
    // 0x14eded4: cmp             x1, x0
    // 0x14eded8: b.hs            #0x14ee02c
    // 0x14ededc: LoadField: r0 = r2->field_f
    //     0x14ededc: ldur            w0, [x2, #0xf]
    // 0x14edee0: DecompressPointer r0
    //     0x14edee0: add             x0, x0, HEAP, lsl #32
    // 0x14edee4: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14edee4: add             x16, x0, x3, lsl #2
    //     0x14edee8: ldur            w1, [x16, #0xf]
    // 0x14edeec: DecompressPointer r1
    //     0x14edeec: add             x1, x1, HEAP, lsl #32
    // 0x14edef0: cmp             w1, NULL
    // 0x14edef4: b.eq            #0x14ee030
    // 0x14edef8: LoadField: r0 = r1->field_2b
    //     0x14edef8: ldur            w0, [x1, #0x2b]
    // 0x14edefc: DecompressPointer r0
    //     0x14edefc: add             x0, x0, HEAP, lsl #32
    // 0x14edf00: cmp             w0, NULL
    // 0x14edf04: b.ne            #0x14edf10
    // 0x14edf08: r0 = Null
    //     0x14edf08: mov             x0, NULL
    // 0x14edf0c: b               #0x14edf1c
    // 0x14edf10: LoadField: r1 = r0->field_b
    //     0x14edf10: ldur            w1, [x0, #0xb]
    // 0x14edf14: DecompressPointer r1
    //     0x14edf14: add             x1, x1, HEAP, lsl #32
    // 0x14edf18: mov             x0, x1
    // 0x14edf1c: cmp             w0, NULL
    // 0x14edf20: b.ne            #0x14edf28
    // 0x14edf24: r0 = ""
    //     0x14edf24: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14edf28: ldur            d1, [fp, #-0x38]
    // 0x14edf2c: ldur            d0, [fp, #-0x40]
    // 0x14edf30: stur            x0, [fp, #-0x18]
    // 0x14edf34: r3 = inline_Allocate_Double()
    //     0x14edf34: ldp             x3, x1, [THR, #0x50]  ; THR::top
    //     0x14edf38: add             x3, x3, #0x10
    //     0x14edf3c: cmp             x1, x3
    //     0x14edf40: b.ls            #0x14ee034
    //     0x14edf44: str             x3, [THR, #0x50]  ; THR::top
    //     0x14edf48: sub             x3, x3, #0xf
    //     0x14edf4c: movz            x1, #0xe15c
    //     0x14edf50: movk            x1, #0x3, lsl #16
    //     0x14edf54: stur            x1, [x3, #-1]
    // 0x14edf58: StoreField: r3->field_7 = d1
    //     0x14edf58: stur            d1, [x3, #7]
    // 0x14edf5c: stur            x3, [fp, #-0x10]
    // 0x14edf60: r4 = inline_Allocate_Double()
    //     0x14edf60: ldp             x4, x1, [THR, #0x50]  ; THR::top
    //     0x14edf64: add             x4, x4, #0x10
    //     0x14edf68: cmp             x1, x4
    //     0x14edf6c: b.ls            #0x14ee050
    //     0x14edf70: str             x4, [THR, #0x50]  ; THR::top
    //     0x14edf74: sub             x4, x4, #0xf
    //     0x14edf78: movz            x1, #0xe15c
    //     0x14edf7c: movk            x1, #0x3, lsl #16
    //     0x14edf80: stur            x1, [x4, #-1]
    // 0x14edf84: StoreField: r4->field_7 = d0
    //     0x14edf84: stur            d0, [x4, #7]
    // 0x14edf88: stur            x4, [fp, #-8]
    // 0x14edf8c: r1 = Function '<anonymous closure>':.
    //     0x14edf8c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40398] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14edf90: ldr             x1, [x1, #0x398]
    // 0x14edf94: r2 = Null
    //     0x14edf94: mov             x2, NULL
    // 0x14edf98: r0 = AllocateClosure()
    //     0x14edf98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14edf9c: r1 = Function '<anonymous closure>':.
    //     0x14edf9c: add             x1, PP, #0x40, lsl #12  ; [pp+0x403a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x14edfa0: ldr             x1, [x1, #0x3a0]
    // 0x14edfa4: r2 = Null
    //     0x14edfa4: mov             x2, NULL
    // 0x14edfa8: stur            x0, [fp, #-0x20]
    // 0x14edfac: r0 = AllocateClosure()
    //     0x14edfac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14edfb0: stur            x0, [fp, #-0x28]
    // 0x14edfb4: r0 = CachedNetworkImage()
    //     0x14edfb4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14edfb8: stur            x0, [fp, #-0x30]
    // 0x14edfbc: ldur            x16, [fp, #-0x10]
    // 0x14edfc0: ldur            lr, [fp, #-8]
    // 0x14edfc4: stp             lr, x16, [SP, #0x10]
    // 0x14edfc8: ldur            x16, [fp, #-0x20]
    // 0x14edfcc: ldur            lr, [fp, #-0x28]
    // 0x14edfd0: stp             lr, x16, [SP]
    // 0x14edfd4: mov             x1, x0
    // 0x14edfd8: ldur            x2, [fp, #-0x18]
    // 0x14edfdc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0x14edfdc: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0x14edfe0: ldr             x4, [x4, #0x388]
    // 0x14edfe4: r0 = CachedNetworkImage()
    //     0x14edfe4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14edfe8: r0 = InteractiveViewer()
    //     0x14edfe8: bl              #0xc92d1c  ; AllocateInteractiveViewerStub -> InteractiveViewer (size=0x64)
    // 0x14edfec: stur            x0, [fp, #-8]
    // 0x14edff0: r16 = Instance_EdgeInsets
    //     0x14edff0: ldr             x16, [PP, #0x6d10]  ; [pp+0x6d10] Obj!EdgeInsets@d56cf1
    // 0x14edff4: r30 = 0.500000
    //     0x14edff4: ldr             lr, [PP, #0x47c0]  ; [pp+0x47c0] 0.5
    // 0x14edff8: stp             lr, x16, [SP]
    // 0x14edffc: mov             x1, x0
    // 0x14ee000: ldur            x2, [fp, #-0x30]
    // 0x14ee004: d0 = 2.000000
    //     0x14ee004: fmov            d0, #2.00000000
    // 0x14ee008: r4 = const [0, 0x5, 0x2, 0x3, boundaryMargin, 0x3, minScale, 0x4, null]
    //     0x14ee008: add             x4, PP, #0x37, lsl #12  ; [pp+0x37328] List(9) [0, 0x5, 0x2, 0x3, "boundaryMargin", 0x3, "minScale", 0x4, Null]
    //     0x14ee00c: ldr             x4, [x4, #0x328]
    // 0x14ee010: r0 = InteractiveViewer()
    //     0x14ee010: bl              #0xc92b1c  ; [package:flutter/src/widgets/interactive_viewer.dart] InteractiveViewer::InteractiveViewer
    // 0x14ee014: ldur            x0, [fp, #-8]
    // 0x14ee018: LeaveFrame
    //     0x14ee018: mov             SP, fp
    //     0x14ee01c: ldp             fp, lr, [SP], #0x10
    // 0x14ee020: ret
    //     0x14ee020: ret             
    // 0x14ee024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14ee024: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14ee028: b               #0x14ede5c
    // 0x14ee02c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14ee02c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14ee030: r0 = NullErrorSharedWithoutFPURegs()
    //     0x14ee030: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x14ee034: stp             q0, q1, [SP, #-0x20]!
    // 0x14ee038: SaveReg r0
    //     0x14ee038: str             x0, [SP, #-8]!
    // 0x14ee03c: r0 = AllocateDouble()
    //     0x14ee03c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14ee040: mov             x3, x0
    // 0x14ee044: RestoreReg r0
    //     0x14ee044: ldr             x0, [SP], #8
    // 0x14ee048: ldp             q0, q1, [SP], #0x20
    // 0x14ee04c: b               #0x14edf58
    // 0x14ee050: SaveReg d0
    //     0x14ee050: str             q0, [SP, #-0x10]!
    // 0x14ee054: stp             x0, x3, [SP, #-0x10]!
    // 0x14ee058: r0 = AllocateDouble()
    //     0x14ee058: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x14ee05c: mov             x4, x0
    // 0x14ee060: ldp             x0, x3, [SP], #0x10
    // 0x14ee064: RestoreReg d0
    //     0x14ee064: ldr             q0, [SP], #0x10
    // 0x14ee068: b               #0x14edf84
  }
}
