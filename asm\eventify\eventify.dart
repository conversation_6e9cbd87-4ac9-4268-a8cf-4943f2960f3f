// lib: eventify, url: package:eventify/eventify.dart

// class id: 1049634, size: 0x8
class :: {
}

// class id: 4955, size: 0x14, field offset: 0x8
class Listener extends Object {
}

// class id: 4956, size: 0xc, field offset: 0x8
class EventEmitter extends Object {

  _ emit(/* No info */) {
    // ** addr: 0x12d4760, size: 0x198
    // 0x12d4760: EnterFrame
    //     0x12d4760: stp             fp, lr, [SP, #-0x10]!
    //     0x12d4764: mov             fp, SP
    // 0x12d4768: AllocStack(0x40)
    //     0x12d4768: sub             SP, SP, #0x40
    // 0x12d476c: SetupParameters(EventEmitter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x12d476c: mov             x0, x2
    //     0x12d4770: stur            x2, [fp, #-0x10]
    //     0x12d4774: mov             x2, x1
    //     0x12d4778: stur            x1, [fp, #-8]
    //     0x12d477c: stur            x3, [fp, #-0x18]
    // 0x12d4780: CheckStackOverflow
    //     0x12d4780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12d4784: cmp             SP, x16
    //     0x12d4788: b.ls            #0x12d48e4
    // 0x12d478c: mov             x1, x0
    // 0x12d4790: r0 = trim()
    //     0x12d4790: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x12d4794: LoadField: r1 = r0->field_7
    //     0x12d4794: ldur            w1, [x0, #7]
    // 0x12d4798: cbz             w1, #0x12d48ac
    // 0x12d479c: ldur            x0, [fp, #-8]
    // 0x12d47a0: LoadField: r3 = r0->field_7
    //     0x12d47a0: ldur            w3, [x0, #7]
    // 0x12d47a4: DecompressPointer r3
    //     0x12d47a4: add             x3, x3, HEAP, lsl #32
    // 0x12d47a8: mov             x1, x3
    // 0x12d47ac: ldur            x2, [fp, #-0x10]
    // 0x12d47b0: stur            x3, [fp, #-0x20]
    // 0x12d47b4: r0 = containsKey()
    //     0x12d47b4: bl              #0x1698fd8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x12d47b8: tbnz            w0, #4, #0x12d489c
    // 0x12d47bc: ldur            x0, [fp, #-0x18]
    // 0x12d47c0: ldur            x1, [fp, #-0x20]
    // 0x12d47c4: r0 = Event()
    //     0x12d47c4: bl              #0x12d48f8  ; AllocateEventStub -> Event (size=0x10)
    // 0x12d47c8: mov             x3, x0
    // 0x12d47cc: r0 = false
    //     0x12d47cc: add             x0, NULL, #0x30  ; false
    // 0x12d47d0: stur            x3, [fp, #-8]
    // 0x12d47d4: StoreField: r3->field_b = r0
    //     0x12d47d4: stur            w0, [x3, #0xb]
    // 0x12d47d8: ldur            x0, [fp, #-0x18]
    // 0x12d47dc: StoreField: r3->field_7 = r0
    //     0x12d47dc: stur            w0, [x3, #7]
    // 0x12d47e0: ldur            x1, [fp, #-0x20]
    // 0x12d47e4: ldur            x2, [fp, #-0x10]
    // 0x12d47e8: r0 = _getValueOrData()
    //     0x12d47e8: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x12d47ec: mov             x1, x0
    // 0x12d47f0: ldur            x0, [fp, #-0x20]
    // 0x12d47f4: LoadField: r2 = r0->field_f
    //     0x12d47f4: ldur            w2, [x0, #0xf]
    // 0x12d47f8: DecompressPointer r2
    //     0x12d47f8: add             x2, x2, HEAP, lsl #32
    // 0x12d47fc: cmp             w2, w1
    // 0x12d4800: b.ne            #0x12d4808
    // 0x12d4804: r1 = Null
    //     0x12d4804: mov             x1, NULL
    // 0x12d4808: cmp             w1, NULL
    // 0x12d480c: b.eq            #0x12d48ec
    // 0x12d4810: r0 = LoadClassIdInstr(r1)
    //     0x12d4810: ldur            x0, [x1, #-1]
    //     0x12d4814: ubfx            x0, x0, #0xc, #0x14
    // 0x12d4818: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12d4818: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12d481c: r0 = GDT[cid_x0 + 0xe4a6]()
    //     0x12d481c: movz            x17, #0xe4a6
    //     0x12d4820: add             lr, x0, x17
    //     0x12d4824: ldr             lr, [x21, lr, lsl #3]
    //     0x12d4828: blr             lr
    // 0x12d482c: mov             x1, x0
    // 0x12d4830: stur            x1, [fp, #-0x10]
    // 0x12d4834: r2 = 0
    //     0x12d4834: movz            x2, #0
    // 0x12d4838: stur            x2, [fp, #-0x28]
    // 0x12d483c: CheckStackOverflow
    //     0x12d483c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12d4840: cmp             SP, x16
    //     0x12d4844: b.ls            #0x12d48f0
    // 0x12d4848: LoadField: r0 = r1->field_b
    //     0x12d4848: ldur            w0, [x1, #0xb]
    // 0x12d484c: r3 = LoadInt32Instr(r0)
    //     0x12d484c: sbfx            x3, x0, #1, #0x1f
    // 0x12d4850: cmp             x2, x3
    // 0x12d4854: b.ge            #0x12d489c
    // 0x12d4858: LoadField: r0 = r1->field_f
    //     0x12d4858: ldur            w0, [x1, #0xf]
    // 0x12d485c: DecompressPointer r0
    //     0x12d485c: add             x0, x0, HEAP, lsl #32
    // 0x12d4860: ArrayLoad: r3 = r0[r2]  ; Unknown_4
    //     0x12d4860: add             x16, x0, x2, lsl #2
    //     0x12d4864: ldur            w3, [x16, #0xf]
    // 0x12d4868: DecompressPointer r3
    //     0x12d4868: add             x3, x3, HEAP, lsl #32
    // 0x12d486c: LoadField: r0 = r3->field_f
    //     0x12d486c: ldur            w0, [x3, #0xf]
    // 0x12d4870: DecompressPointer r0
    //     0x12d4870: add             x0, x0, HEAP, lsl #32
    // 0x12d4874: ldur            x16, [fp, #-8]
    // 0x12d4878: stp             x16, x0, [SP, #8]
    // 0x12d487c: str             NULL, [SP]
    // 0x12d4880: ClosureCall
    //     0x12d4880: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x12d4884: ldur            x2, [x0, #0x1f]
    //     0x12d4888: blr             x2
    // 0x12d488c: ldur            x0, [fp, #-0x28]
    // 0x12d4890: add             x2, x0, #1
    // 0x12d4894: ldur            x1, [fp, #-0x10]
    // 0x12d4898: b               #0x12d4838
    // 0x12d489c: r0 = Null
    //     0x12d489c: mov             x0, NULL
    // 0x12d48a0: LeaveFrame
    //     0x12d48a0: mov             SP, fp
    //     0x12d48a4: ldp             fp, lr, [SP], #0x10
    // 0x12d48a8: ret
    //     0x12d48a8: ret             
    // 0x12d48ac: r0 = false
    //     0x12d48ac: add             x0, NULL, #0x30  ; false
    // 0x12d48b0: r0 = ArgumentError()
    //     0x12d48b0: bl              #0x61ba20  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x12d48b4: mov             x1, x0
    // 0x12d48b8: r0 = "event"
    //     0x12d48b8: add             x0, PP, #0x39, lsl #12  ; [pp+0x39938] "event"
    //     0x12d48bc: ldr             x0, [x0, #0x938]
    // 0x12d48c0: StoreField: r1->field_13 = r0
    //     0x12d48c0: stur            w0, [x1, #0x13]
    // 0x12d48c4: r0 = false
    //     0x12d48c4: add             x0, NULL, #0x30  ; false
    // 0x12d48c8: StoreField: r1->field_b = r0
    //     0x12d48c8: stur            w0, [x1, #0xb]
    // 0x12d48cc: r0 = "Must not be null"
    //     0x12d48cc: add             x0, PP, #0x29, lsl #12  ; [pp+0x297a8] "Must not be null"
    //     0x12d48d0: ldr             x0, [x0, #0x7a8]
    // 0x12d48d4: ArrayStore: r1[0] = r0  ; List_4
    //     0x12d48d4: stur            w0, [x1, #0x17]
    // 0x12d48d8: mov             x0, x1
    // 0x12d48dc: r0 = Throw()
    //     0x12d48dc: bl              #0x16f5420  ; ThrowStub
    // 0x12d48e0: brk             #0
    // 0x12d48e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12d48e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12d48e8: b               #0x12d478c
    // 0x12d48ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x12d48ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x12d48f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12d48f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12d48f4: b               #0x12d4848
  }
  _ on(/* No info */) {
    // ** addr: 0x150b8dc, size: 0xfc
    // 0x150b8dc: EnterFrame
    //     0x150b8dc: stp             fp, lr, [SP, #-0x10]!
    //     0x150b8e0: mov             fp, SP
    // 0x150b8e4: AllocStack(0x20)
    //     0x150b8e4: sub             SP, SP, #0x20
    // 0x150b8e8: SetupParameters(EventEmitter this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x150b8e8: mov             x0, x2
    //     0x150b8ec: stur            x2, [fp, #-0x10]
    //     0x150b8f0: mov             x2, x1
    //     0x150b8f4: stur            x1, [fp, #-8]
    //     0x150b8f8: stur            x3, [fp, #-0x18]
    // 0x150b8fc: CheckStackOverflow
    //     0x150b8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x150b900: cmp             SP, x16
    //     0x150b904: b.ls            #0x150b9d0
    // 0x150b908: mov             x1, x0
    // 0x150b90c: r0 = trim()
    //     0x150b90c: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x150b910: LoadField: r1 = r0->field_7
    //     0x150b910: ldur            w1, [x0, #7]
    // 0x150b914: cbz             w1, #0x150b99c
    // 0x150b918: ldur            x1, [fp, #-8]
    // 0x150b91c: ldur            x3, [fp, #-0x10]
    // 0x150b920: ldur            x0, [fp, #-0x18]
    // 0x150b924: LoadField: r4 = r1->field_7
    //     0x150b924: ldur            w4, [x1, #7]
    // 0x150b928: DecompressPointer r4
    //     0x150b928: add             x4, x4, HEAP, lsl #32
    // 0x150b92c: stur            x4, [fp, #-0x20]
    // 0x150b930: r1 = Function '<anonymous closure>':.
    //     0x150b930: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d4a8] AnonymousClosure: (0x150ba08), in [package:eventify/eventify.dart] EventEmitter::on (0x150b8dc)
    //     0x150b934: ldr             x1, [x1, #0x4a8]
    // 0x150b938: r2 = Null
    //     0x150b938: mov             x2, NULL
    // 0x150b93c: r0 = AllocateClosure()
    //     0x150b93c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150b940: ldur            x1, [fp, #-0x20]
    // 0x150b944: ldur            x2, [fp, #-0x10]
    // 0x150b948: mov             x3, x0
    // 0x150b94c: r0 = putIfAbsent()
    //     0x150b94c: bl              #0x75242c  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0x150b950: stur            x0, [fp, #-8]
    // 0x150b954: r0 = Listener()
    //     0x150b954: bl              #0x150b9d8  ; AllocateListenerStub -> Listener (size=0x14)
    // 0x150b958: mov             x3, x0
    // 0x150b95c: ldur            x0, [fp, #-0x10]
    // 0x150b960: stur            x3, [fp, #-0x20]
    // 0x150b964: StoreField: r3->field_7 = r0
    //     0x150b964: stur            w0, [x3, #7]
    // 0x150b968: ldur            x0, [fp, #-0x18]
    // 0x150b96c: StoreField: r3->field_f = r0
    //     0x150b96c: stur            w0, [x3, #0xf]
    // 0x150b970: ldur            x1, [fp, #-8]
    // 0x150b974: r0 = LoadClassIdInstr(r1)
    //     0x150b974: ldur            x0, [x1, #-1]
    //     0x150b978: ubfx            x0, x0, #0xc, #0x14
    // 0x150b97c: mov             x2, x3
    // 0x150b980: r0 = GDT[cid_x0 + -0x11f]()
    //     0x150b980: sub             lr, x0, #0x11f
    //     0x150b984: ldr             lr, [x21, lr, lsl #3]
    //     0x150b988: blr             lr
    // 0x150b98c: ldur            x0, [fp, #-0x20]
    // 0x150b990: LeaveFrame
    //     0x150b990: mov             SP, fp
    //     0x150b994: ldp             fp, lr, [SP], #0x10
    // 0x150b998: ret
    //     0x150b998: ret             
    // 0x150b99c: r0 = ArgumentError()
    //     0x150b99c: bl              #0x61ba20  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x150b9a0: mov             x1, x0
    // 0x150b9a4: r0 = "event"
    //     0x150b9a4: add             x0, PP, #0x39, lsl #12  ; [pp+0x39938] "event"
    //     0x150b9a8: ldr             x0, [x0, #0x938]
    // 0x150b9ac: StoreField: r1->field_13 = r0
    //     0x150b9ac: stur            w0, [x1, #0x13]
    // 0x150b9b0: r0 = false
    //     0x150b9b0: add             x0, NULL, #0x30  ; false
    // 0x150b9b4: StoreField: r1->field_b = r0
    //     0x150b9b4: stur            w0, [x1, #0xb]
    // 0x150b9b8: r0 = "Must not be null"
    //     0x150b9b8: add             x0, PP, #0x29, lsl #12  ; [pp+0x297a8] "Must not be null"
    //     0x150b9bc: ldr             x0, [x0, #0x7a8]
    // 0x150b9c0: ArrayStore: r1[0] = r0  ; List_4
    //     0x150b9c0: stur            w0, [x1, #0x17]
    // 0x150b9c4: mov             x0, x1
    // 0x150b9c8: r0 = Throw()
    //     0x150b9c8: bl              #0x16f5420  ; ThrowStub
    // 0x150b9cc: brk             #0
    // 0x150b9d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150b9d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x150b9d4: b               #0x150b908
  }
  [closure] Set<Listener> <anonymous closure>(dynamic) {
    // ** addr: 0x150ba08, size: 0x3c
    // 0x150ba08: EnterFrame
    //     0x150ba08: stp             fp, lr, [SP, #-0x10]!
    //     0x150ba0c: mov             fp, SP
    // 0x150ba10: r1 = <Listener>
    //     0x150ba10: add             x1, PP, #0x5d, lsl #12  ; [pp+0x5d4b0] TypeArguments: <Listener>
    //     0x150ba14: ldr             x1, [x1, #0x4b0]
    // 0x150ba18: r0 = _Set()
    //     0x150ba18: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x150ba1c: r1 = _Uint32List
    //     0x150ba1c: ldr             x1, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x150ba20: StoreField: r0->field_1b = r1
    //     0x150ba20: stur            w1, [x0, #0x1b]
    // 0x150ba24: StoreField: r0->field_b = rZR
    //     0x150ba24: stur            wzr, [x0, #0xb]
    // 0x150ba28: r1 = const []
    //     0x150ba28: ldr             x1, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x150ba2c: StoreField: r0->field_f = r1
    //     0x150ba2c: stur            w1, [x0, #0xf]
    // 0x150ba30: StoreField: r0->field_13 = rZR
    //     0x150ba30: stur            wzr, [x0, #0x13]
    // 0x150ba34: ArrayStore: r0[0] = rZR  ; List_4
    //     0x150ba34: stur            wzr, [x0, #0x17]
    // 0x150ba38: LeaveFrame
    //     0x150ba38: mov             SP, fp
    //     0x150ba3c: ldp             fp, lr, [SP], #0x10
    // 0x150ba40: ret
    //     0x150ba40: ret             
  }
  _ clear(/* No info */) {
    // ** addr: 0x15292ac, size: 0x3c
    // 0x15292ac: EnterFrame
    //     0x15292ac: stp             fp, lr, [SP, #-0x10]!
    //     0x15292b0: mov             fp, SP
    // 0x15292b4: CheckStackOverflow
    //     0x15292b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15292b8: cmp             SP, x16
    //     0x15292bc: b.ls            #0x15292e0
    // 0x15292c0: LoadField: r0 = r1->field_7
    //     0x15292c0: ldur            w0, [x1, #7]
    // 0x15292c4: DecompressPointer r0
    //     0x15292c4: add             x0, x0, HEAP, lsl #32
    // 0x15292c8: mov             x1, x0
    // 0x15292cc: r0 = clear()
    //     0x15292cc: bl              #0x649b54  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x15292d0: r0 = Null
    //     0x15292d0: mov             x0, NULL
    // 0x15292d4: LeaveFrame
    //     0x15292d4: mov             SP, fp
    //     0x15292d8: ldp             fp, lr, [SP], #0x10
    // 0x15292dc: ret
    //     0x15292dc: ret             
    // 0x15292e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15292e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15292e4: b               #0x15292c0
  }
}

// class id: 4957, size: 0x10, field offset: 0x8
class Event extends Object {
}
