// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/color_picker_detail.dart

// class id: 1049430, size: 0x8
class :: {
}

// class id: 4491, size: 0x18, field offset: 0xc
//   const constructor, 
class ColorPickerDetail extends StatelessWidget {

  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0x1289e68, size: 0xfc
    // 0x1289e68: EnterFrame
    //     0x1289e68: stp             fp, lr, [SP, #-0x10]!
    //     0x1289e6c: mov             fp, SP
    // 0x1289e70: AllocStack(0x10)
    //     0x1289e70: sub             SP, SP, #0x10
    // 0x1289e74: ldr             x0, [fp, #0x10]
    // 0x1289e78: LoadField: r1 = r0->field_b
    //     0x1289e78: ldur            w1, [x0, #0xb]
    // 0x1289e7c: DecompressPointer r1
    //     0x1289e7c: add             x1, x1, HEAP, lsl #32
    // 0x1289e80: cmp             w1, NULL
    // 0x1289e84: b.eq            #0x1289ea0
    // 0x1289e88: LoadField: r2 = r0->field_f
    //     0x1289e88: ldur            x2, [x0, #0xf]
    // 0x1289e8c: r0 = LoadInt32Instr(r1)
    //     0x1289e8c: sbfx            x0, x1, #1, #0x1f
    //     0x1289e90: tbz             w1, #0, #0x1289e98
    //     0x1289e94: ldur            x0, [x1, #7]
    // 0x1289e98: cmp             x2, x0
    // 0x1289e9c: b.le            #0x1289ea8
    // 0x1289ea0: r0 = Null
    //     0x1289ea0: mov             x0, NULL
    // 0x1289ea4: b               #0x1289edc
    // 0x1289ea8: scvtf           d0, x2
    // 0x1289eac: scvtf           d1, x0
    // 0x1289eb0: fdiv            d2, d0, d1
    // 0x1289eb4: r0 = inline_Allocate_Double()
    //     0x1289eb4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1289eb8: add             x0, x0, #0x10
    //     0x1289ebc: cmp             x1, x0
    //     0x1289ec0: b.ls            #0x1289f54
    //     0x1289ec4: str             x0, [THR, #0x50]  ; THR::top
    //     0x1289ec8: sub             x0, x0, #0xf
    //     0x1289ecc: movz            x1, #0xe15c
    //     0x1289ed0: movk            x1, #0x3, lsl #16
    //     0x1289ed4: stur            x1, [x0, #-1]
    // 0x1289ed8: StoreField: r0->field_7 = d2
    //     0x1289ed8: stur            d2, [x0, #7]
    // 0x1289edc: stur            x0, [fp, #-8]
    // 0x1289ee0: r0 = CircularProgressIndicator()
    //     0x1289ee0: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0x1289ee4: mov             x1, x0
    // 0x1289ee8: r0 = Instance__ActivityIndicatorType
    //     0x1289ee8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0x1289eec: ldr             x0, [x0, #0x1b0]
    // 0x1289ef0: stur            x1, [fp, #-0x10]
    // 0x1289ef4: StoreField: r1->field_23 = r0
    //     0x1289ef4: stur            w0, [x1, #0x23]
    // 0x1289ef8: ldur            x0, [fp, #-8]
    // 0x1289efc: StoreField: r1->field_b = r0
    //     0x1289efc: stur            w0, [x1, #0xb]
    // 0x1289f00: r0 = Instance_Color
    //     0x1289f00: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0x1289f04: ldr             x0, [x0, #0xb18]
    // 0x1289f08: StoreField: r1->field_13 = r0
    //     0x1289f08: stur            w0, [x1, #0x13]
    // 0x1289f0c: r0 = SizedBox()
    //     0x1289f0c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1289f10: mov             x1, x0
    // 0x1289f14: r0 = 20.000000
    //     0x1289f14: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x1289f18: ldr             x0, [x0, #0xac8]
    // 0x1289f1c: stur            x1, [fp, #-8]
    // 0x1289f20: StoreField: r1->field_f = r0
    //     0x1289f20: stur            w0, [x1, #0xf]
    // 0x1289f24: StoreField: r1->field_13 = r0
    //     0x1289f24: stur            w0, [x1, #0x13]
    // 0x1289f28: ldur            x0, [fp, #-0x10]
    // 0x1289f2c: StoreField: r1->field_b = r0
    //     0x1289f2c: stur            w0, [x1, #0xb]
    // 0x1289f30: r0 = Center()
    //     0x1289f30: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1289f34: r1 = Instance_Alignment
    //     0x1289f34: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1289f38: ldr             x1, [x1, #0xb10]
    // 0x1289f3c: StoreField: r0->field_f = r1
    //     0x1289f3c: stur            w1, [x0, #0xf]
    // 0x1289f40: ldur            x1, [fp, #-8]
    // 0x1289f44: StoreField: r0->field_b = r1
    //     0x1289f44: stur            w1, [x0, #0xb]
    // 0x1289f48: LeaveFrame
    //     0x1289f48: mov             SP, fp
    //     0x1289f4c: ldp             fp, lr, [SP], #0x10
    // 0x1289f50: ret
    //     0x1289f50: ret             
    // 0x1289f54: SaveReg d2
    //     0x1289f54: str             q2, [SP, #-0x10]!
    // 0x1289f58: r0 = AllocateDouble()
    //     0x1289f58: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1289f5c: RestoreReg d2
    //     0x1289f5c: ldr             q2, [SP], #0x10
    // 0x1289f60: b               #0x1289ed8
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x1289f64, size: 0x528
    // 0x1289f64: EnterFrame
    //     0x1289f64: stp             fp, lr, [SP, #-0x10]!
    //     0x1289f68: mov             fp, SP
    // 0x1289f6c: AllocStack(0x78)
    //     0x1289f6c: sub             SP, SP, #0x78
    // 0x1289f70: SetupParameters()
    //     0x1289f70: ldr             x0, [fp, #0x20]
    //     0x1289f74: ldur            w1, [x0, #0x17]
    //     0x1289f78: add             x1, x1, HEAP, lsl #32
    //     0x1289f7c: stur            x1, [fp, #-8]
    // 0x1289f80: CheckStackOverflow
    //     0x1289f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1289f84: cmp             SP, x16
    //     0x1289f88: b.ls            #0x128a47c
    // 0x1289f8c: r1 = 2
    //     0x1289f8c: movz            x1, #0x2
    // 0x1289f90: r0 = AllocateContext()
    //     0x1289f90: bl              #0x16f6108  ; AllocateContextStub
    // 0x1289f94: mov             x2, x0
    // 0x1289f98: ldur            x0, [fp, #-8]
    // 0x1289f9c: stur            x2, [fp, #-0x10]
    // 0x1289fa0: StoreField: r2->field_b = r0
    //     0x1289fa0: stur            w0, [x2, #0xb]
    // 0x1289fa4: LoadField: r3 = r0->field_f
    //     0x1289fa4: ldur            w3, [x0, #0xf]
    // 0x1289fa8: DecompressPointer r3
    //     0x1289fa8: add             x3, x3, HEAP, lsl #32
    // 0x1289fac: LoadField: r0 = r3->field_b
    //     0x1289fac: ldur            w0, [x3, #0xb]
    // 0x1289fb0: DecompressPointer r0
    //     0x1289fb0: add             x0, x0, HEAP, lsl #32
    // 0x1289fb4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x1289fb4: ldur            w4, [x0, #0x17]
    // 0x1289fb8: DecompressPointer r4
    //     0x1289fb8: add             x4, x4, HEAP, lsl #32
    // 0x1289fbc: cmp             w4, NULL
    // 0x1289fc0: b.eq            #0x128a484
    // 0x1289fc4: LoadField: r0 = r4->field_b
    //     0x1289fc4: ldur            w0, [x4, #0xb]
    // 0x1289fc8: ldr             x1, [fp, #0x10]
    // 0x1289fcc: r5 = LoadInt32Instr(r1)
    //     0x1289fcc: sbfx            x5, x1, #1, #0x1f
    //     0x1289fd0: tbz             w1, #0, #0x1289fd8
    //     0x1289fd4: ldur            x5, [x1, #7]
    // 0x1289fd8: r1 = LoadInt32Instr(r0)
    //     0x1289fd8: sbfx            x1, x0, #1, #0x1f
    // 0x1289fdc: mov             x0, x1
    // 0x1289fe0: mov             x1, x5
    // 0x1289fe4: cmp             x1, x0
    // 0x1289fe8: b.hs            #0x128a488
    // 0x1289fec: LoadField: r0 = r4->field_f
    //     0x1289fec: ldur            w0, [x4, #0xf]
    // 0x1289ff0: DecompressPointer r0
    //     0x1289ff0: add             x0, x0, HEAP, lsl #32
    // 0x1289ff4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x1289ff4: add             x16, x0, x5, lsl #2
    //     0x1289ff8: ldur            w1, [x16, #0xf]
    // 0x1289ffc: DecompressPointer r1
    //     0x1289ffc: add             x1, x1, HEAP, lsl #32
    // 0x128a000: stur            x1, [fp, #-8]
    // 0x128a004: StoreField: r2->field_f = r1
    //     0x128a004: stur            w1, [x2, #0xf]
    // 0x128a008: LoadField: r0 = r1->field_37
    //     0x128a008: ldur            w0, [x1, #0x37]
    // 0x128a00c: DecompressPointer r0
    //     0x128a00c: add             x0, x0, HEAP, lsl #32
    // 0x128a010: LoadField: r4 = r3->field_f
    //     0x128a010: ldur            w4, [x3, #0xf]
    // 0x128a014: DecompressPointer r4
    //     0x128a014: add             x4, x4, HEAP, lsl #32
    // 0x128a018: r3 = LoadClassIdInstr(r0)
    //     0x128a018: ldur            x3, [x0, #-1]
    //     0x128a01c: ubfx            x3, x3, #0xc, #0x14
    // 0x128a020: stp             x4, x0, [SP]
    // 0x128a024: mov             x0, x3
    // 0x128a028: mov             lr, x0
    // 0x128a02c: ldr             lr, [x21, lr, lsl #3]
    // 0x128a030: blr             lr
    // 0x128a034: mov             x3, x0
    // 0x128a038: ldur            x0, [fp, #-0x10]
    // 0x128a03c: stur            x3, [fp, #-0x18]
    // 0x128a040: StoreField: r0->field_13 = r3
    //     0x128a040: stur            w3, [x0, #0x13]
    // 0x128a044: tbnz            w3, #4, #0x128a050
    // 0x128a048: r2 = Instance_Color
    //     0x128a048: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128a04c: b               #0x128a058
    // 0x128a050: r2 = Instance_Color
    //     0x128a050: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x128a054: ldr             x2, [x2, #0xf88]
    // 0x128a058: ldur            x4, [fp, #-8]
    // 0x128a05c: r16 = 2.000000
    //     0x128a05c: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0x128a060: ldr             x16, [x16, #0xdf8]
    // 0x128a064: str             x16, [SP]
    // 0x128a068: r1 = Null
    //     0x128a068: mov             x1, NULL
    // 0x128a06c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x128a06c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x128a070: ldr             x4, [x4, #0x108]
    // 0x128a074: r0 = Border.all()
    //     0x128a074: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x128a078: stur            x0, [fp, #-0x20]
    // 0x128a07c: r0 = Radius()
    //     0x128a07c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128a080: d0 = 10.000000
    //     0x128a080: fmov            d0, #10.00000000
    // 0x128a084: stur            x0, [fp, #-0x28]
    // 0x128a088: StoreField: r0->field_7 = d0
    //     0x128a088: stur            d0, [x0, #7]
    // 0x128a08c: StoreField: r0->field_f = d0
    //     0x128a08c: stur            d0, [x0, #0xf]
    // 0x128a090: r0 = BorderRadius()
    //     0x128a090: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128a094: mov             x2, x0
    // 0x128a098: ldur            x0, [fp, #-0x28]
    // 0x128a09c: stur            x2, [fp, #-0x30]
    // 0x128a0a0: StoreField: r2->field_7 = r0
    //     0x128a0a0: stur            w0, [x2, #7]
    // 0x128a0a4: StoreField: r2->field_b = r0
    //     0x128a0a4: stur            w0, [x2, #0xb]
    // 0x128a0a8: StoreField: r2->field_f = r0
    //     0x128a0a8: stur            w0, [x2, #0xf]
    // 0x128a0ac: StoreField: r2->field_13 = r0
    //     0x128a0ac: stur            w0, [x2, #0x13]
    // 0x128a0b0: ldr             x1, [fp, #0x18]
    // 0x128a0b4: r0 = of()
    //     0x128a0b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128a0b8: LoadField: r1 = r0->field_5b
    //     0x128a0b8: ldur            w1, [x0, #0x5b]
    // 0x128a0bc: DecompressPointer r1
    //     0x128a0bc: add             x1, x1, HEAP, lsl #32
    // 0x128a0c0: r0 = LoadClassIdInstr(r1)
    //     0x128a0c0: ldur            x0, [x1, #-1]
    //     0x128a0c4: ubfx            x0, x0, #0xc, #0x14
    // 0x128a0c8: d0 = 0.030000
    //     0x128a0c8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x128a0cc: ldr             d0, [x17, #0x238]
    // 0x128a0d0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x128a0d0: sub             lr, x0, #0xffa
    //     0x128a0d4: ldr             lr, [x21, lr, lsl #3]
    //     0x128a0d8: blr             lr
    // 0x128a0dc: stur            x0, [fp, #-0x28]
    // 0x128a0e0: r0 = BoxDecoration()
    //     0x128a0e0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128a0e4: mov             x1, x0
    // 0x128a0e8: ldur            x0, [fp, #-0x28]
    // 0x128a0ec: stur            x1, [fp, #-0x38]
    // 0x128a0f0: StoreField: r1->field_7 = r0
    //     0x128a0f0: stur            w0, [x1, #7]
    // 0x128a0f4: ldur            x0, [fp, #-0x20]
    // 0x128a0f8: StoreField: r1->field_f = r0
    //     0x128a0f8: stur            w0, [x1, #0xf]
    // 0x128a0fc: ldur            x0, [fp, #-0x30]
    // 0x128a100: StoreField: r1->field_13 = r0
    //     0x128a100: stur            w0, [x1, #0x13]
    // 0x128a104: r0 = Instance_BoxShape
    //     0x128a104: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128a108: ldr             x0, [x0, #0x80]
    // 0x128a10c: StoreField: r1->field_23 = r0
    //     0x128a10c: stur            w0, [x1, #0x23]
    // 0x128a110: r0 = Radius()
    //     0x128a110: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128a114: d0 = 10.000000
    //     0x128a114: fmov            d0, #10.00000000
    // 0x128a118: stur            x0, [fp, #-0x20]
    // 0x128a11c: StoreField: r0->field_7 = d0
    //     0x128a11c: stur            d0, [x0, #7]
    // 0x128a120: StoreField: r0->field_f = d0
    //     0x128a120: stur            d0, [x0, #0xf]
    // 0x128a124: r0 = BorderRadius()
    //     0x128a124: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128a128: mov             x3, x0
    // 0x128a12c: ldur            x0, [fp, #-0x20]
    // 0x128a130: stur            x3, [fp, #-0x28]
    // 0x128a134: StoreField: r3->field_7 = r0
    //     0x128a134: stur            w0, [x3, #7]
    // 0x128a138: StoreField: r3->field_b = r0
    //     0x128a138: stur            w0, [x3, #0xb]
    // 0x128a13c: StoreField: r3->field_f = r0
    //     0x128a13c: stur            w0, [x3, #0xf]
    // 0x128a140: StoreField: r3->field_13 = r0
    //     0x128a140: stur            w0, [x3, #0x13]
    // 0x128a144: ldur            x0, [fp, #-8]
    // 0x128a148: LoadField: r1 = r0->field_13
    //     0x128a148: ldur            w1, [x0, #0x13]
    // 0x128a14c: DecompressPointer r1
    //     0x128a14c: add             x1, x1, HEAP, lsl #32
    // 0x128a150: cmp             w1, NULL
    // 0x128a154: b.ne            #0x128a160
    // 0x128a158: r4 = ""
    //     0x128a158: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128a15c: b               #0x128a164
    // 0x128a160: mov             x4, x1
    // 0x128a164: stur            x4, [fp, #-0x20]
    // 0x128a168: r1 = Function '<anonymous closure>':.
    //     0x128a168: add             x1, PP, #0x48, lsl #12  ; [pp+0x487c8] AnonymousClosure: (0x1289e68), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x12933b0)
    //     0x128a16c: ldr             x1, [x1, #0x7c8]
    // 0x128a170: r2 = Null
    //     0x128a170: mov             x2, NULL
    // 0x128a174: r0 = AllocateClosure()
    //     0x128a174: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128a178: r1 = Function '<anonymous closure>':.
    //     0x128a178: add             x1, PP, #0x48, lsl #12  ; [pp+0x487d0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x128a17c: ldr             x1, [x1, #0x7d0]
    // 0x128a180: r2 = Null
    //     0x128a180: mov             x2, NULL
    // 0x128a184: stur            x0, [fp, #-0x30]
    // 0x128a188: r0 = AllocateClosure()
    //     0x128a188: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128a18c: stur            x0, [fp, #-0x40]
    // 0x128a190: r0 = CachedNetworkImage()
    //     0x128a190: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x128a194: stur            x0, [fp, #-0x48]
    // 0x128a198: r16 = 60.000000
    //     0x128a198: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x128a19c: ldr             x16, [x16, #0x110]
    // 0x128a1a0: r30 = 60.000000
    //     0x128a1a0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x128a1a4: ldr             lr, [lr, #0x110]
    // 0x128a1a8: stp             lr, x16, [SP, #0x18]
    // 0x128a1ac: r16 = Instance_BoxFit
    //     0x128a1ac: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x128a1b0: ldr             x16, [x16, #0xb18]
    // 0x128a1b4: ldur            lr, [fp, #-0x30]
    // 0x128a1b8: stp             lr, x16, [SP, #8]
    // 0x128a1bc: ldur            x16, [fp, #-0x40]
    // 0x128a1c0: str             x16, [SP]
    // 0x128a1c4: mov             x1, x0
    // 0x128a1c8: ldur            x2, [fp, #-0x20]
    // 0x128a1cc: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x128a1cc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x128a1d0: ldr             x4, [x4, #0xc28]
    // 0x128a1d4: r0 = CachedNetworkImage()
    //     0x128a1d4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x128a1d8: r0 = ClipRRect()
    //     0x128a1d8: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x128a1dc: mov             x1, x0
    // 0x128a1e0: ldur            x0, [fp, #-0x28]
    // 0x128a1e4: stur            x1, [fp, #-0x20]
    // 0x128a1e8: StoreField: r1->field_f = r0
    //     0x128a1e8: stur            w0, [x1, #0xf]
    // 0x128a1ec: r0 = Instance_Clip
    //     0x128a1ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x128a1f0: ldr             x0, [x0, #0x138]
    // 0x128a1f4: ArrayStore: r1[0] = r0  ; List_4
    //     0x128a1f4: stur            w0, [x1, #0x17]
    // 0x128a1f8: ldur            x0, [fp, #-0x48]
    // 0x128a1fc: StoreField: r1->field_b = r0
    //     0x128a1fc: stur            w0, [x1, #0xb]
    // 0x128a200: r0 = Container()
    //     0x128a200: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128a204: stur            x0, [fp, #-0x28]
    // 0x128a208: ldur            x16, [fp, #-0x38]
    // 0x128a20c: ldur            lr, [fp, #-0x20]
    // 0x128a210: stp             lr, x16, [SP]
    // 0x128a214: mov             x1, x0
    // 0x128a218: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x128a218: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x128a21c: ldr             x4, [x4, #0x88]
    // 0x128a220: r0 = Container()
    //     0x128a220: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128a224: r1 = Null
    //     0x128a224: mov             x1, NULL
    // 0x128a228: r2 = 2
    //     0x128a228: movz            x2, #0x2
    // 0x128a22c: r0 = AllocateArray()
    //     0x128a22c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128a230: mov             x2, x0
    // 0x128a234: ldur            x0, [fp, #-0x28]
    // 0x128a238: stur            x2, [fp, #-0x20]
    // 0x128a23c: StoreField: r2->field_f = r0
    //     0x128a23c: stur            w0, [x2, #0xf]
    // 0x128a240: r1 = <Widget>
    //     0x128a240: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128a244: r0 = AllocateGrowableArray()
    //     0x128a244: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128a248: mov             x2, x0
    // 0x128a24c: ldur            x0, [fp, #-0x20]
    // 0x128a250: stur            x2, [fp, #-0x28]
    // 0x128a254: StoreField: r2->field_f = r0
    //     0x128a254: stur            w0, [x2, #0xf]
    // 0x128a258: r0 = 2
    //     0x128a258: movz            x0, #0x2
    // 0x128a25c: StoreField: r2->field_b = r0
    //     0x128a25c: stur            w0, [x2, #0xb]
    // 0x128a260: ldur            x0, [fp, #-8]
    // 0x128a264: r17 = 279
    //     0x128a264: movz            x17, #0x117
    // 0x128a268: ldr             w1, [x0, x17]
    // 0x128a26c: DecompressPointer r1
    //     0x128a26c: add             x1, x1, HEAP, lsl #32
    // 0x128a270: cmp             w1, NULL
    // 0x128a274: b.eq            #0x128a3a4
    // 0x128a278: LoadField: r0 = r1->field_7
    //     0x128a278: ldur            w0, [x1, #7]
    // 0x128a27c: cbz             w0, #0x128a3a4
    // 0x128a280: ldur            x0, [fp, #-0x18]
    // 0x128a284: r0 = StringExtension.toTitleCase()
    //     0x128a284: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0x128a288: ldr             x1, [fp, #0x18]
    // 0x128a28c: stur            x0, [fp, #-8]
    // 0x128a290: r0 = of()
    //     0x128a290: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x128a294: LoadField: r1 = r0->field_87
    //     0x128a294: ldur            w1, [x0, #0x87]
    // 0x128a298: DecompressPointer r1
    //     0x128a298: add             x1, x1, HEAP, lsl #32
    // 0x128a29c: LoadField: r0 = r1->field_2b
    //     0x128a29c: ldur            w0, [x1, #0x2b]
    // 0x128a2a0: DecompressPointer r0
    //     0x128a2a0: add             x0, x0, HEAP, lsl #32
    // 0x128a2a4: ldur            x1, [fp, #-0x18]
    // 0x128a2a8: stur            x0, [fp, #-0x20]
    // 0x128a2ac: tbnz            w1, #4, #0x128a2b8
    // 0x128a2b0: r1 = Instance_Color
    //     0x128a2b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128a2b4: b               #0x128a2c8
    // 0x128a2b8: r1 = Instance_Color
    //     0x128a2b8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128a2bc: d0 = 0.400000
    //     0x128a2bc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x128a2c0: r0 = withOpacity()
    //     0x128a2c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x128a2c4: mov             x1, x0
    // 0x128a2c8: ldur            x0, [fp, #-8]
    // 0x128a2cc: ldur            x2, [fp, #-0x28]
    // 0x128a2d0: r16 = 12.000000
    //     0x128a2d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x128a2d4: ldr             x16, [x16, #0x9e8]
    // 0x128a2d8: stp             x1, x16, [SP]
    // 0x128a2dc: ldur            x1, [fp, #-0x20]
    // 0x128a2e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x128a2e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x128a2e4: ldr             x4, [x4, #0xaa0]
    // 0x128a2e8: r0 = copyWith()
    //     0x128a2e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x128a2ec: stur            x0, [fp, #-0x18]
    // 0x128a2f0: r0 = Text()
    //     0x128a2f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128a2f4: mov             x1, x0
    // 0x128a2f8: ldur            x0, [fp, #-8]
    // 0x128a2fc: stur            x1, [fp, #-0x20]
    // 0x128a300: StoreField: r1->field_b = r0
    //     0x128a300: stur            w0, [x1, #0xb]
    // 0x128a304: ldur            x0, [fp, #-0x18]
    // 0x128a308: StoreField: r1->field_13 = r0
    //     0x128a308: stur            w0, [x1, #0x13]
    // 0x128a30c: r0 = Padding()
    //     0x128a30c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128a310: mov             x2, x0
    // 0x128a314: r0 = Instance_EdgeInsets
    //     0x128a314: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x128a318: ldr             x0, [x0, #0x770]
    // 0x128a31c: stur            x2, [fp, #-8]
    // 0x128a320: StoreField: r2->field_f = r0
    //     0x128a320: stur            w0, [x2, #0xf]
    // 0x128a324: ldur            x0, [fp, #-0x20]
    // 0x128a328: StoreField: r2->field_b = r0
    //     0x128a328: stur            w0, [x2, #0xb]
    // 0x128a32c: ldur            x0, [fp, #-0x28]
    // 0x128a330: LoadField: r1 = r0->field_b
    //     0x128a330: ldur            w1, [x0, #0xb]
    // 0x128a334: LoadField: r3 = r0->field_f
    //     0x128a334: ldur            w3, [x0, #0xf]
    // 0x128a338: DecompressPointer r3
    //     0x128a338: add             x3, x3, HEAP, lsl #32
    // 0x128a33c: LoadField: r4 = r3->field_b
    //     0x128a33c: ldur            w4, [x3, #0xb]
    // 0x128a340: r3 = LoadInt32Instr(r1)
    //     0x128a340: sbfx            x3, x1, #1, #0x1f
    // 0x128a344: stur            x3, [fp, #-0x50]
    // 0x128a348: r1 = LoadInt32Instr(r4)
    //     0x128a348: sbfx            x1, x4, #1, #0x1f
    // 0x128a34c: cmp             x3, x1
    // 0x128a350: b.ne            #0x128a35c
    // 0x128a354: mov             x1, x0
    // 0x128a358: r0 = _growToNextCapacity()
    //     0x128a358: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128a35c: ldur            x2, [fp, #-0x28]
    // 0x128a360: ldur            x3, [fp, #-0x50]
    // 0x128a364: add             x0, x3, #1
    // 0x128a368: lsl             x1, x0, #1
    // 0x128a36c: StoreField: r2->field_b = r1
    //     0x128a36c: stur            w1, [x2, #0xb]
    // 0x128a370: LoadField: r1 = r2->field_f
    //     0x128a370: ldur            w1, [x2, #0xf]
    // 0x128a374: DecompressPointer r1
    //     0x128a374: add             x1, x1, HEAP, lsl #32
    // 0x128a378: ldur            x0, [fp, #-8]
    // 0x128a37c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128a37c: add             x25, x1, x3, lsl #2
    //     0x128a380: add             x25, x25, #0xf
    //     0x128a384: str             w0, [x25]
    //     0x128a388: tbz             w0, #0, #0x128a3a4
    //     0x128a38c: ldurb           w16, [x1, #-1]
    //     0x128a390: ldurb           w17, [x0, #-1]
    //     0x128a394: and             x16, x17, x16, lsr #2
    //     0x128a398: tst             x16, HEAP, lsr #32
    //     0x128a39c: b.eq            #0x128a3a4
    //     0x128a3a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128a3a4: r0 = Column()
    //     0x128a3a4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128a3a8: mov             x1, x0
    // 0x128a3ac: r0 = Instance_Axis
    //     0x128a3ac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128a3b0: stur            x1, [fp, #-8]
    // 0x128a3b4: StoreField: r1->field_f = r0
    //     0x128a3b4: stur            w0, [x1, #0xf]
    // 0x128a3b8: r0 = Instance_MainAxisAlignment
    //     0x128a3b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128a3bc: ldr             x0, [x0, #0xa08]
    // 0x128a3c0: StoreField: r1->field_13 = r0
    //     0x128a3c0: stur            w0, [x1, #0x13]
    // 0x128a3c4: r0 = Instance_MainAxisSize
    //     0x128a3c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x128a3c8: ldr             x0, [x0, #0xdd0]
    // 0x128a3cc: ArrayStore: r1[0] = r0  ; List_4
    //     0x128a3cc: stur            w0, [x1, #0x17]
    // 0x128a3d0: r0 = Instance_CrossAxisAlignment
    //     0x128a3d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128a3d4: ldr             x0, [x0, #0xa18]
    // 0x128a3d8: StoreField: r1->field_1b = r0
    //     0x128a3d8: stur            w0, [x1, #0x1b]
    // 0x128a3dc: r0 = Instance_VerticalDirection
    //     0x128a3dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128a3e0: ldr             x0, [x0, #0xa20]
    // 0x128a3e4: StoreField: r1->field_23 = r0
    //     0x128a3e4: stur            w0, [x1, #0x23]
    // 0x128a3e8: r0 = Instance_Clip
    //     0x128a3e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128a3ec: ldr             x0, [x0, #0x38]
    // 0x128a3f0: StoreField: r1->field_2b = r0
    //     0x128a3f0: stur            w0, [x1, #0x2b]
    // 0x128a3f4: StoreField: r1->field_2f = rZR
    //     0x128a3f4: stur            xzr, [x1, #0x2f]
    // 0x128a3f8: ldur            x0, [fp, #-0x28]
    // 0x128a3fc: StoreField: r1->field_b = r0
    //     0x128a3fc: stur            w0, [x1, #0xb]
    // 0x128a400: r0 = InkWell()
    //     0x128a400: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x128a404: mov             x3, x0
    // 0x128a408: ldur            x0, [fp, #-8]
    // 0x128a40c: stur            x3, [fp, #-0x18]
    // 0x128a410: StoreField: r3->field_b = r0
    //     0x128a410: stur            w0, [x3, #0xb]
    // 0x128a414: ldur            x2, [fp, #-0x10]
    // 0x128a418: r1 = Function '<anonymous closure>':.
    //     0x128a418: add             x1, PP, #0x48, lsl #12  ; [pp+0x487d8] AnonymousClosure: (0xa85e58), in [package:customer_app/app/presentation/views/line/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x1298224)
    //     0x128a41c: ldr             x1, [x1, #0x7d8]
    // 0x128a420: r0 = AllocateClosure()
    //     0x128a420: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128a424: mov             x1, x0
    // 0x128a428: ldur            x0, [fp, #-0x18]
    // 0x128a42c: StoreField: r0->field_f = r1
    //     0x128a42c: stur            w1, [x0, #0xf]
    // 0x128a430: r1 = true
    //     0x128a430: add             x1, NULL, #0x20  ; true
    // 0x128a434: StoreField: r0->field_43 = r1
    //     0x128a434: stur            w1, [x0, #0x43]
    // 0x128a438: r2 = Instance_BoxShape
    //     0x128a438: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128a43c: ldr             x2, [x2, #0x80]
    // 0x128a440: StoreField: r0->field_47 = r2
    //     0x128a440: stur            w2, [x0, #0x47]
    // 0x128a444: StoreField: r0->field_6f = r1
    //     0x128a444: stur            w1, [x0, #0x6f]
    // 0x128a448: r2 = false
    //     0x128a448: add             x2, NULL, #0x30  ; false
    // 0x128a44c: StoreField: r0->field_73 = r2
    //     0x128a44c: stur            w2, [x0, #0x73]
    // 0x128a450: StoreField: r0->field_83 = r1
    //     0x128a450: stur            w1, [x0, #0x83]
    // 0x128a454: StoreField: r0->field_7b = r2
    //     0x128a454: stur            w2, [x0, #0x7b]
    // 0x128a458: r0 = Padding()
    //     0x128a458: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128a45c: r1 = Instance_EdgeInsets
    //     0x128a45c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0x128a460: ldr             x1, [x1, #0xd48]
    // 0x128a464: StoreField: r0->field_f = r1
    //     0x128a464: stur            w1, [x0, #0xf]
    // 0x128a468: ldur            x1, [fp, #-0x18]
    // 0x128a46c: StoreField: r0->field_b = r1
    //     0x128a46c: stur            w1, [x0, #0xb]
    // 0x128a470: LeaveFrame
    //     0x128a470: mov             SP, fp
    //     0x128a474: ldp             fp, lr, [SP], #0x10
    // 0x128a478: ret
    //     0x128a478: ret             
    // 0x128a47c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128a47c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128a480: b               #0x1289f8c
    // 0x128a484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x128a484: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x128a488: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x128a488: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0x12933b0, size: 0xec
    // 0x12933b0: EnterFrame
    //     0x12933b0: stp             fp, lr, [SP, #-0x10]!
    //     0x12933b4: mov             fp, SP
    // 0x12933b8: AllocStack(0x28)
    //     0x12933b8: sub             SP, SP, #0x28
    // 0x12933bc: SetupParameters(ColorPickerDetail this /* r1 => r1, fp-0x8 */)
    //     0x12933bc: stur            x1, [fp, #-8]
    // 0x12933c0: CheckStackOverflow
    //     0x12933c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12933c4: cmp             SP, x16
    //     0x12933c8: b.ls            #0x1293494
    // 0x12933cc: r1 = 1
    //     0x12933cc: movz            x1, #0x1
    // 0x12933d0: r0 = AllocateContext()
    //     0x12933d0: bl              #0x16f6108  ; AllocateContextStub
    // 0x12933d4: mov             x1, x0
    // 0x12933d8: ldur            x0, [fp, #-8]
    // 0x12933dc: StoreField: r1->field_f = r0
    //     0x12933dc: stur            w0, [x1, #0xf]
    // 0x12933e0: LoadField: r2 = r0->field_b
    //     0x12933e0: ldur            w2, [x0, #0xb]
    // 0x12933e4: DecompressPointer r2
    //     0x12933e4: add             x2, x2, HEAP, lsl #32
    // 0x12933e8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x12933e8: ldur            w0, [x2, #0x17]
    // 0x12933ec: DecompressPointer r0
    //     0x12933ec: add             x0, x0, HEAP, lsl #32
    // 0x12933f0: cmp             w0, NULL
    // 0x12933f4: b.eq            #0x1293404
    // 0x12933f8: LoadField: r3 = r0->field_b
    //     0x12933f8: ldur            w3, [x0, #0xb]
    // 0x12933fc: stur            x3, [fp, #-8]
    // 0x1293400: cbnz            w3, #0x1293414
    // 0x1293404: r0 = Instance_SizedBox
    //     0x1293404: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1293408: LeaveFrame
    //     0x1293408: mov             SP, fp
    //     0x129340c: ldp             fp, lr, [SP], #0x10
    // 0x1293410: ret
    //     0x1293410: ret             
    // 0x1293414: mov             x2, x1
    // 0x1293418: r1 = Function '<anonymous closure>':.
    //     0x1293418: add             x1, PP, #0x48, lsl #12  ; [pp+0x487b8] AnonymousClosure: (0x1289f64), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x12933b0)
    //     0x129341c: ldr             x1, [x1, #0x7b8]
    // 0x1293420: r0 = AllocateClosure()
    //     0x1293420: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1293424: stur            x0, [fp, #-0x10]
    // 0x1293428: r0 = ListView()
    //     0x1293428: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x129342c: stur            x0, [fp, #-0x18]
    // 0x1293430: r16 = Instance_Axis
    //     0x1293430: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1293434: r30 = Instance_EdgeInsets
    //     0x1293434: ldr             lr, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1293438: stp             lr, x16, [SP]
    // 0x129343c: mov             x1, x0
    // 0x1293440: ldur            x2, [fp, #-0x10]
    // 0x1293444: ldur            x3, [fp, #-8]
    // 0x1293448: r4 = const [0, 0x5, 0x2, 0x3, padding, 0x4, scrollDirection, 0x3, null]
    //     0x1293448: add             x4, PP, #0x48, lsl #12  ; [pp+0x487c0] List(9) [0, 0x5, 0x2, 0x3, "padding", 0x4, "scrollDirection", 0x3, Null]
    //     0x129344c: ldr             x4, [x4, #0x7c0]
    // 0x1293450: r0 = ListView.builder()
    //     0x1293450: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x1293454: r0 = SizedBox()
    //     0x1293454: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1293458: mov             x1, x0
    // 0x129345c: r0 = 100.000000
    //     0x129345c: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x1293460: stur            x1, [fp, #-8]
    // 0x1293464: StoreField: r1->field_13 = r0
    //     0x1293464: stur            w0, [x1, #0x13]
    // 0x1293468: ldur            x0, [fp, #-0x18]
    // 0x129346c: StoreField: r1->field_b = r0
    //     0x129346c: stur            w0, [x1, #0xb]
    // 0x1293470: r0 = Padding()
    //     0x1293470: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1293474: r1 = Instance_EdgeInsets
    //     0x1293474: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0x1293478: ldr             x1, [x1, #0x110]
    // 0x129347c: StoreField: r0->field_f = r1
    //     0x129347c: stur            w1, [x0, #0xf]
    // 0x1293480: ldur            x1, [fp, #-8]
    // 0x1293484: StoreField: r0->field_b = r1
    //     0x1293484: stur            w1, [x0, #0xb]
    // 0x1293488: LeaveFrame
    //     0x1293488: mov             SP, fp
    //     0x129348c: ldp             fp, lr, [SP], #0x10
    // 0x1293490: ret
    //     0x1293490: ret             
    // 0x1293494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1293494: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1293498: b               #0x12933cc
  }
}
