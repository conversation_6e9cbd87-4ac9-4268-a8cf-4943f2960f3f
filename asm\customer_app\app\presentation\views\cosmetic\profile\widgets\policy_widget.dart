// lib: , url: package:customer_app/app/presentation/views/cosmetic/profile/widgets/policy_widget.dart

// class id: 1049332, size: 0x8
class :: {
}

// class id: 4585, size: 0x14, field offset: 0x14
//   const constructor, 
class PolicyWidget extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d1744, size: 0x64
    // 0x14d1744: EnterFrame
    //     0x14d1744: stp             fp, lr, [SP, #-0x10]!
    //     0x14d1748: mov             fp, SP
    // 0x14d174c: AllocStack(0x18)
    //     0x14d174c: sub             SP, SP, #0x18
    // 0x14d1750: SetupParameters(PolicyWidget this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d1750: stur            x1, [fp, #-8]
    //     0x14d1754: stur            x2, [fp, #-0x10]
    // 0x14d1758: r1 = 2
    //     0x14d1758: movz            x1, #0x2
    // 0x14d175c: r0 = AllocateContext()
    //     0x14d175c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d1760: mov             x1, x0
    // 0x14d1764: ldur            x0, [fp, #-8]
    // 0x14d1768: stur            x1, [fp, #-0x18]
    // 0x14d176c: StoreField: r1->field_f = r0
    //     0x14d176c: stur            w0, [x1, #0xf]
    // 0x14d1770: ldur            x0, [fp, #-0x10]
    // 0x14d1774: StoreField: r1->field_13 = r0
    //     0x14d1774: stur            w0, [x1, #0x13]
    // 0x14d1778: r0 = Obx()
    //     0x14d1778: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d177c: ldur            x2, [fp, #-0x18]
    // 0x14d1780: r1 = Function '<anonymous closure>':.
    //     0x14d1780: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d18] AnonymousClosure: (0x147899c), in [package:customer_app/app/presentation/views/line/profile/widgets/policy_widget.dart] PolicyWidget::body (0x15094e8)
    //     0x14d1784: ldr             x1, [x1, #0xd18]
    // 0x14d1788: stur            x0, [fp, #-8]
    // 0x14d178c: r0 = AllocateClosure()
    //     0x14d178c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d1790: mov             x1, x0
    // 0x14d1794: ldur            x0, [fp, #-8]
    // 0x14d1798: StoreField: r0->field_b = r1
    //     0x14d1798: stur            w1, [x0, #0xb]
    // 0x14d179c: LeaveFrame
    //     0x14d179c: mov             SP, fp
    //     0x14d17a0: ldp             fp, lr, [SP], #0x10
    // 0x14d17a4: ret
    //     0x14d17a4: ret             
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15deb78, size: 0x130
    // 0x15deb78: EnterFrame
    //     0x15deb78: stp             fp, lr, [SP, #-0x10]!
    //     0x15deb7c: mov             fp, SP
    // 0x15deb80: AllocStack(0x18)
    //     0x15deb80: sub             SP, SP, #0x18
    // 0x15deb84: SetupParameters(PolicyWidget this /* r1 => r0 */, dynamic _ /* r2 => r1 */)
    //     0x15deb84: mov             x0, x1
    //     0x15deb88: mov             x1, x2
    // 0x15deb8c: CheckStackOverflow
    //     0x15deb8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15deb90: cmp             SP, x16
    //     0x15deb94: b.ls            #0x15deca0
    // 0x15deb98: r0 = of()
    //     0x15deb98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15deb9c: LoadField: r1 = r0->field_5b
    //     0x15deb9c: ldur            w1, [x0, #0x5b]
    // 0x15deba0: DecompressPointer r1
    //     0x15deba0: add             x1, x1, HEAP, lsl #32
    // 0x15deba4: stur            x1, [fp, #-8]
    // 0x15deba8: r0 = ColorFilter()
    //     0x15deba8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15debac: mov             x1, x0
    // 0x15debb0: ldur            x0, [fp, #-8]
    // 0x15debb4: stur            x1, [fp, #-0x10]
    // 0x15debb8: StoreField: r1->field_7 = r0
    //     0x15debb8: stur            w0, [x1, #7]
    // 0x15debbc: r0 = Instance_BlendMode
    //     0x15debbc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15debc0: ldr             x0, [x0, #0xb30]
    // 0x15debc4: StoreField: r1->field_b = r0
    //     0x15debc4: stur            w0, [x1, #0xb]
    // 0x15debc8: r0 = 1
    //     0x15debc8: movz            x0, #0x1
    // 0x15debcc: StoreField: r1->field_13 = r0
    //     0x15debcc: stur            x0, [x1, #0x13]
    // 0x15debd0: r0 = SvgPicture()
    //     0x15debd0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15debd4: stur            x0, [fp, #-8]
    // 0x15debd8: ldur            x16, [fp, #-0x10]
    // 0x15debdc: str             x16, [SP]
    // 0x15debe0: mov             x1, x0
    // 0x15debe4: r2 = "assets/images/appbar_arrow.svg"
    //     0x15debe4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15debe8: ldr             x2, [x2, #0xa40]
    // 0x15debec: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15debec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15debf0: ldr             x4, [x4, #0xa38]
    // 0x15debf4: r0 = SvgPicture.asset()
    //     0x15debf4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15debf8: r0 = Align()
    //     0x15debf8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15debfc: mov             x1, x0
    // 0x15dec00: r0 = Instance_Alignment
    //     0x15dec00: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dec04: ldr             x0, [x0, #0xb10]
    // 0x15dec08: stur            x1, [fp, #-0x10]
    // 0x15dec0c: StoreField: r1->field_f = r0
    //     0x15dec0c: stur            w0, [x1, #0xf]
    // 0x15dec10: r0 = 1.000000
    //     0x15dec10: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dec14: StoreField: r1->field_13 = r0
    //     0x15dec14: stur            w0, [x1, #0x13]
    // 0x15dec18: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dec18: stur            w0, [x1, #0x17]
    // 0x15dec1c: ldur            x0, [fp, #-8]
    // 0x15dec20: StoreField: r1->field_b = r0
    //     0x15dec20: stur            w0, [x1, #0xb]
    // 0x15dec24: r0 = InkWell()
    //     0x15dec24: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dec28: mov             x3, x0
    // 0x15dec2c: ldur            x0, [fp, #-0x10]
    // 0x15dec30: stur            x3, [fp, #-8]
    // 0x15dec34: StoreField: r3->field_b = r0
    //     0x15dec34: stur            w0, [x3, #0xb]
    // 0x15dec38: r1 = Function '<anonymous closure>':.
    //     0x15dec38: add             x1, PP, #0x41, lsl #12  ; [pp+0x41d20] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x15dec3c: ldr             x1, [x1, #0xd20]
    // 0x15dec40: r2 = Null
    //     0x15dec40: mov             x2, NULL
    // 0x15dec44: r0 = AllocateClosure()
    //     0x15dec44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dec48: ldur            x2, [fp, #-8]
    // 0x15dec4c: StoreField: r2->field_f = r0
    //     0x15dec4c: stur            w0, [x2, #0xf]
    // 0x15dec50: r0 = true
    //     0x15dec50: add             x0, NULL, #0x20  ; true
    // 0x15dec54: StoreField: r2->field_43 = r0
    //     0x15dec54: stur            w0, [x2, #0x43]
    // 0x15dec58: r1 = Instance_BoxShape
    //     0x15dec58: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dec5c: ldr             x1, [x1, #0x80]
    // 0x15dec60: StoreField: r2->field_47 = r1
    //     0x15dec60: stur            w1, [x2, #0x47]
    // 0x15dec64: StoreField: r2->field_6f = r0
    //     0x15dec64: stur            w0, [x2, #0x6f]
    // 0x15dec68: r1 = false
    //     0x15dec68: add             x1, NULL, #0x30  ; false
    // 0x15dec6c: StoreField: r2->field_73 = r1
    //     0x15dec6c: stur            w1, [x2, #0x73]
    // 0x15dec70: StoreField: r2->field_83 = r0
    //     0x15dec70: stur            w0, [x2, #0x83]
    // 0x15dec74: StoreField: r2->field_7b = r1
    //     0x15dec74: stur            w1, [x2, #0x7b]
    // 0x15dec78: r0 = AppBar()
    //     0x15dec78: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dec7c: mov             x1, x0
    // 0x15dec80: ldur            x2, [fp, #-8]
    // 0x15dec84: stur            x0, [fp, #-8]
    // 0x15dec88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15dec88: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15dec8c: r0 = AppBar()
    //     0x15dec8c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dec90: ldur            x0, [fp, #-8]
    // 0x15dec94: LeaveFrame
    //     0x15dec94: mov             SP, fp
    //     0x15dec98: ldp             fp, lr, [SP], #0x10
    // 0x15dec9c: ret
    //     0x15dec9c: ret             
    // 0x15deca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15deca0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15deca4: b               #0x15deb98
  }
}
