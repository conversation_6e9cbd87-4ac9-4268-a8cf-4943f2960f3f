// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/offers_bottom_sheet.dart

// class id: 1049226, size: 0x8
class :: {
}

// class id: 3466, size: 0x14, field offset: 0x14
class _OffersBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xab789c, size: 0x1150
    // 0xab789c: EnterFrame
    //     0xab789c: stp             fp, lr, [SP, #-0x10]!
    //     0xab78a0: mov             fp, SP
    // 0xab78a4: AllocStack(0x60)
    //     0xab78a4: sub             SP, SP, #0x60
    // 0xab78a8: SetupParameters(_OffersBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xab78a8: mov             x0, x1
    //     0xab78ac: stur            x1, [fp, #-8]
    //     0xab78b0: mov             x1, x2
    //     0xab78b4: stur            x2, [fp, #-0x10]
    // 0xab78b8: CheckStackOverflow
    //     0xab78b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab78bc: cmp             SP, x16
    //     0xab78c0: b.ls            #0xab89ac
    // 0xab78c4: r1 = 2
    //     0xab78c4: movz            x1, #0x2
    // 0xab78c8: r0 = AllocateContext()
    //     0xab78c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xab78cc: mov             x2, x0
    // 0xab78d0: ldur            x0, [fp, #-8]
    // 0xab78d4: stur            x2, [fp, #-0x18]
    // 0xab78d8: StoreField: r2->field_f = r0
    //     0xab78d8: stur            w0, [x2, #0xf]
    // 0xab78dc: ldur            x1, [fp, #-0x10]
    // 0xab78e0: StoreField: r2->field_13 = r1
    //     0xab78e0: stur            w1, [x2, #0x13]
    // 0xab78e4: r0 = of()
    //     0xab78e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab78e8: LoadField: r1 = r0->field_87
    //     0xab78e8: ldur            w1, [x0, #0x87]
    // 0xab78ec: DecompressPointer r1
    //     0xab78ec: add             x1, x1, HEAP, lsl #32
    // 0xab78f0: LoadField: r0 = r1->field_23
    //     0xab78f0: ldur            w0, [x1, #0x23]
    // 0xab78f4: DecompressPointer r0
    //     0xab78f4: add             x0, x0, HEAP, lsl #32
    // 0xab78f8: r16 = Instance_Color
    //     0xab78f8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab78fc: r30 = 32.000000
    //     0xab78fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xab7900: ldr             lr, [lr, #0x848]
    // 0xab7904: stp             lr, x16, [SP]
    // 0xab7908: mov             x1, x0
    // 0xab790c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab790c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab7910: ldr             x4, [x4, #0x9b8]
    // 0xab7914: r0 = copyWith()
    //     0xab7914: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab7918: stur            x0, [fp, #-0x10]
    // 0xab791c: r0 = Text()
    //     0xab791c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab7920: mov             x1, x0
    // 0xab7924: r0 = "Offer"
    //     0xab7924: add             x0, PP, #0x71, lsl #12  ; [pp+0x71460] "Offer"
    //     0xab7928: ldr             x0, [x0, #0x460]
    // 0xab792c: stur            x1, [fp, #-0x20]
    // 0xab7930: StoreField: r1->field_b = r0
    //     0xab7930: stur            w0, [x1, #0xb]
    // 0xab7934: ldur            x0, [fp, #-0x10]
    // 0xab7938: StoreField: r1->field_13 = r0
    //     0xab7938: stur            w0, [x1, #0x13]
    // 0xab793c: r0 = Padding()
    //     0xab793c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab7940: mov             x2, x0
    // 0xab7944: r0 = Instance_EdgeInsets
    //     0xab7944: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xab7948: ldr             x0, [x0, #0xf70]
    // 0xab794c: stur            x2, [fp, #-0x10]
    // 0xab7950: StoreField: r2->field_f = r0
    //     0xab7950: stur            w0, [x2, #0xf]
    // 0xab7954: ldur            x1, [fp, #-0x20]
    // 0xab7958: StoreField: r2->field_b = r1
    //     0xab7958: stur            w1, [x2, #0xb]
    // 0xab795c: r1 = <FlexParentData>
    //     0xab795c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xab7960: ldr             x1, [x1, #0xe00]
    // 0xab7964: r0 = Expanded()
    //     0xab7964: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xab7968: stur            x0, [fp, #-0x20]
    // 0xab796c: StoreField: r0->field_13 = rZR
    //     0xab796c: stur            xzr, [x0, #0x13]
    // 0xab7970: r1 = Instance_FlexFit
    //     0xab7970: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xab7974: ldr             x1, [x1, #0xe08]
    // 0xab7978: StoreField: r0->field_1b = r1
    //     0xab7978: stur            w1, [x0, #0x1b]
    // 0xab797c: ldur            x2, [fp, #-0x10]
    // 0xab7980: StoreField: r0->field_b = r2
    //     0xab7980: stur            w2, [x0, #0xb]
    // 0xab7984: r0 = InkWell()
    //     0xab7984: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xab7988: mov             x3, x0
    // 0xab798c: r0 = Instance_Padding
    //     0xab798c: add             x0, PP, #0x71, lsl #12  ; [pp+0x71468] Obj!Padding@d68441
    //     0xab7990: ldr             x0, [x0, #0x468]
    // 0xab7994: stur            x3, [fp, #-0x10]
    // 0xab7998: StoreField: r3->field_b = r0
    //     0xab7998: stur            w0, [x3, #0xb]
    // 0xab799c: ldur            x2, [fp, #-0x18]
    // 0xab79a0: r1 = Function '<anonymous closure>':.
    //     0xab79a0: add             x1, PP, #0x71, lsl #12  ; [pp+0x71550] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xab79a4: ldr             x1, [x1, #0x550]
    // 0xab79a8: r0 = AllocateClosure()
    //     0xab79a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab79ac: mov             x1, x0
    // 0xab79b0: ldur            x0, [fp, #-0x10]
    // 0xab79b4: StoreField: r0->field_f = r1
    //     0xab79b4: stur            w1, [x0, #0xf]
    // 0xab79b8: r2 = true
    //     0xab79b8: add             x2, NULL, #0x20  ; true
    // 0xab79bc: StoreField: r0->field_43 = r2
    //     0xab79bc: stur            w2, [x0, #0x43]
    // 0xab79c0: r3 = Instance_BoxShape
    //     0xab79c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xab79c4: ldr             x3, [x3, #0x80]
    // 0xab79c8: StoreField: r0->field_47 = r3
    //     0xab79c8: stur            w3, [x0, #0x47]
    // 0xab79cc: StoreField: r0->field_6f = r2
    //     0xab79cc: stur            w2, [x0, #0x6f]
    // 0xab79d0: r4 = false
    //     0xab79d0: add             x4, NULL, #0x30  ; false
    // 0xab79d4: StoreField: r0->field_73 = r4
    //     0xab79d4: stur            w4, [x0, #0x73]
    // 0xab79d8: StoreField: r0->field_83 = r2
    //     0xab79d8: stur            w2, [x0, #0x83]
    // 0xab79dc: StoreField: r0->field_7b = r4
    //     0xab79dc: stur            w4, [x0, #0x7b]
    // 0xab79e0: r1 = <FlexParentData>
    //     0xab79e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xab79e4: ldr             x1, [x1, #0xe00]
    // 0xab79e8: r0 = Expanded()
    //     0xab79e8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xab79ec: stur            x0, [fp, #-0x28]
    // 0xab79f0: StoreField: r0->field_13 = rZR
    //     0xab79f0: stur            xzr, [x0, #0x13]
    // 0xab79f4: r3 = Instance_FlexFit
    //     0xab79f4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xab79f8: ldr             x3, [x3, #0xe08]
    // 0xab79fc: StoreField: r0->field_1b = r3
    //     0xab79fc: stur            w3, [x0, #0x1b]
    // 0xab7a00: ldur            x1, [fp, #-0x10]
    // 0xab7a04: StoreField: r0->field_b = r1
    //     0xab7a04: stur            w1, [x0, #0xb]
    // 0xab7a08: r1 = Null
    //     0xab7a08: mov             x1, NULL
    // 0xab7a0c: r2 = 6
    //     0xab7a0c: movz            x2, #0x6
    // 0xab7a10: r0 = AllocateArray()
    //     0xab7a10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab7a14: mov             x2, x0
    // 0xab7a18: ldur            x0, [fp, #-0x20]
    // 0xab7a1c: stur            x2, [fp, #-0x10]
    // 0xab7a20: StoreField: r2->field_f = r0
    //     0xab7a20: stur            w0, [x2, #0xf]
    // 0xab7a24: r16 = Instance_Spacer
    //     0xab7a24: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab7a28: ldr             x16, [x16, #0xf0]
    // 0xab7a2c: StoreField: r2->field_13 = r16
    //     0xab7a2c: stur            w16, [x2, #0x13]
    // 0xab7a30: ldur            x0, [fp, #-0x28]
    // 0xab7a34: ArrayStore: r2[0] = r0  ; List_4
    //     0xab7a34: stur            w0, [x2, #0x17]
    // 0xab7a38: r1 = <Widget>
    //     0xab7a38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab7a3c: r0 = AllocateGrowableArray()
    //     0xab7a3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab7a40: mov             x1, x0
    // 0xab7a44: ldur            x0, [fp, #-0x10]
    // 0xab7a48: stur            x1, [fp, #-0x20]
    // 0xab7a4c: StoreField: r1->field_f = r0
    //     0xab7a4c: stur            w0, [x1, #0xf]
    // 0xab7a50: r0 = 6
    //     0xab7a50: movz            x0, #0x6
    // 0xab7a54: StoreField: r1->field_b = r0
    //     0xab7a54: stur            w0, [x1, #0xb]
    // 0xab7a58: r0 = Row()
    //     0xab7a58: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab7a5c: mov             x3, x0
    // 0xab7a60: r0 = Instance_Axis
    //     0xab7a60: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab7a64: stur            x3, [fp, #-0x10]
    // 0xab7a68: StoreField: r3->field_f = r0
    //     0xab7a68: stur            w0, [x3, #0xf]
    // 0xab7a6c: r0 = Instance_MainAxisAlignment
    //     0xab7a6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab7a70: ldr             x0, [x0, #0xa08]
    // 0xab7a74: StoreField: r3->field_13 = r0
    //     0xab7a74: stur            w0, [x3, #0x13]
    // 0xab7a78: r1 = Instance_MainAxisSize
    //     0xab7a78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab7a7c: ldr             x1, [x1, #0xa10]
    // 0xab7a80: ArrayStore: r3[0] = r1  ; List_4
    //     0xab7a80: stur            w1, [x3, #0x17]
    // 0xab7a84: r1 = Instance_CrossAxisAlignment
    //     0xab7a84: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab7a88: ldr             x1, [x1, #0xa18]
    // 0xab7a8c: StoreField: r3->field_1b = r1
    //     0xab7a8c: stur            w1, [x3, #0x1b]
    // 0xab7a90: r4 = Instance_VerticalDirection
    //     0xab7a90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab7a94: ldr             x4, [x4, #0xa20]
    // 0xab7a98: StoreField: r3->field_23 = r4
    //     0xab7a98: stur            w4, [x3, #0x23]
    // 0xab7a9c: r5 = Instance_Clip
    //     0xab7a9c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab7aa0: ldr             x5, [x5, #0x38]
    // 0xab7aa4: StoreField: r3->field_2b = r5
    //     0xab7aa4: stur            w5, [x3, #0x2b]
    // 0xab7aa8: StoreField: r3->field_2f = rZR
    //     0xab7aa8: stur            xzr, [x3, #0x2f]
    // 0xab7aac: ldur            x1, [fp, #-0x20]
    // 0xab7ab0: StoreField: r3->field_b = r1
    //     0xab7ab0: stur            w1, [x3, #0xb]
    // 0xab7ab4: r1 = <Widget>
    //     0xab7ab4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab7ab8: r2 = 18
    //     0xab7ab8: movz            x2, #0x12
    // 0xab7abc: r0 = AllocateArray()
    //     0xab7abc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab7ac0: mov             x2, x0
    // 0xab7ac4: ldur            x0, [fp, #-0x10]
    // 0xab7ac8: stur            x2, [fp, #-0x20]
    // 0xab7acc: StoreField: r2->field_f = r0
    //     0xab7acc: stur            w0, [x2, #0xf]
    // 0xab7ad0: r16 = Instance_SizedBox
    //     0xab7ad0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xab7ad4: ldr             x16, [x16, #0x8f0]
    // 0xab7ad8: StoreField: r2->field_13 = r16
    //     0xab7ad8: stur            w16, [x2, #0x13]
    // 0xab7adc: r1 = Instance_MaterialColor
    //     0xab7adc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xab7ae0: ldr             x1, [x1, #0xdc0]
    // 0xab7ae4: d0 = 0.300000
    //     0xab7ae4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xab7ae8: ldr             d0, [x17, #0x658]
    // 0xab7aec: r0 = withOpacity()
    //     0xab7aec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xab7af0: stur            x0, [fp, #-0x10]
    // 0xab7af4: r0 = Container()
    //     0xab7af4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab7af8: stur            x0, [fp, #-0x28]
    // 0xab7afc: ldur            x16, [fp, #-0x10]
    // 0xab7b00: r30 = 50.000000
    //     0xab7b00: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xab7b04: ldr             lr, [lr, #0xa90]
    // 0xab7b08: stp             lr, x16, [SP, #0x10]
    // 0xab7b0c: r16 = 50.000000
    //     0xab7b0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xab7b10: ldr             x16, [x16, #0xa90]
    // 0xab7b14: r30 = Instance_Icon
    //     0xab7b14: add             lr, PP, #0x71, lsl #12  ; [pp+0x71478] Obj!Icon@d66231
    //     0xab7b18: ldr             lr, [lr, #0x478]
    // 0xab7b1c: stp             lr, x16, [SP]
    // 0xab7b20: mov             x1, x0
    // 0xab7b24: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, height, 0x2, width, 0x3, null]
    //     0xab7b24: add             x4, PP, #0x71, lsl #12  ; [pp+0x71480] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "height", 0x2, "width", 0x3, Null]
    //     0xab7b28: ldr             x4, [x4, #0x480]
    // 0xab7b2c: r0 = Container()
    //     0xab7b2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab7b30: r0 = Padding()
    //     0xab7b30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab7b34: mov             x1, x0
    // 0xab7b38: r0 = Instance_EdgeInsets
    //     0xab7b38: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xab7b3c: ldr             x0, [x0, #0xf70]
    // 0xab7b40: StoreField: r1->field_f = r0
    //     0xab7b40: stur            w0, [x1, #0xf]
    // 0xab7b44: ldur            x0, [fp, #-0x28]
    // 0xab7b48: StoreField: r1->field_b = r0
    //     0xab7b48: stur            w0, [x1, #0xb]
    // 0xab7b4c: mov             x0, x1
    // 0xab7b50: ldur            x1, [fp, #-0x20]
    // 0xab7b54: ArrayStore: r1[2] = r0  ; List_4
    //     0xab7b54: add             x25, x1, #0x17
    //     0xab7b58: str             w0, [x25]
    //     0xab7b5c: tbz             w0, #0, #0xab7b78
    //     0xab7b60: ldurb           w16, [x1, #-1]
    //     0xab7b64: ldurb           w17, [x0, #-1]
    //     0xab7b68: and             x16, x17, x16, lsr #2
    //     0xab7b6c: tst             x16, HEAP, lsr #32
    //     0xab7b70: b.eq            #0xab7b78
    //     0xab7b74: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xab7b78: ldur            x0, [fp, #-0x20]
    // 0xab7b7c: r16 = Instance_SizedBox
    //     0xab7b7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xab7b80: ldr             x16, [x16, #0x8b8]
    // 0xab7b84: StoreField: r0->field_1b = r16
    //     0xab7b84: stur            w16, [x0, #0x1b]
    // 0xab7b88: ldur            x2, [fp, #-8]
    // 0xab7b8c: LoadField: r1 = r2->field_b
    //     0xab7b8c: ldur            w1, [x2, #0xb]
    // 0xab7b90: DecompressPointer r1
    //     0xab7b90: add             x1, x1, HEAP, lsl #32
    // 0xab7b94: cmp             w1, NULL
    // 0xab7b98: b.eq            #0xab89b4
    // 0xab7b9c: LoadField: r3 = r1->field_b
    //     0xab7b9c: ldur            w3, [x1, #0xb]
    // 0xab7ba0: DecompressPointer r3
    //     0xab7ba0: add             x3, x3, HEAP, lsl #32
    // 0xab7ba4: cmp             w3, NULL
    // 0xab7ba8: b.ne            #0xab7bb4
    // 0xab7bac: r1 = Null
    //     0xab7bac: mov             x1, NULL
    // 0xab7bb0: b               #0xab7bbc
    // 0xab7bb4: LoadField: r1 = r3->field_13
    //     0xab7bb4: ldur            w1, [x3, #0x13]
    // 0xab7bb8: DecompressPointer r1
    //     0xab7bb8: add             x1, x1, HEAP, lsl #32
    // 0xab7bbc: cmp             w1, NULL
    // 0xab7bc0: b.ne            #0xab7bcc
    // 0xab7bc4: r4 = ""
    //     0xab7bc4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab7bc8: b               #0xab7bd0
    // 0xab7bcc: mov             x4, x1
    // 0xab7bd0: ldur            x3, [fp, #-0x18]
    // 0xab7bd4: stur            x4, [fp, #-0x10]
    // 0xab7bd8: LoadField: r1 = r3->field_13
    //     0xab7bd8: ldur            w1, [x3, #0x13]
    // 0xab7bdc: DecompressPointer r1
    //     0xab7bdc: add             x1, x1, HEAP, lsl #32
    // 0xab7be0: r0 = of()
    //     0xab7be0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab7be4: LoadField: r1 = r0->field_87
    //     0xab7be4: ldur            w1, [x0, #0x87]
    // 0xab7be8: DecompressPointer r1
    //     0xab7be8: add             x1, x1, HEAP, lsl #32
    // 0xab7bec: LoadField: r0 = r1->field_2b
    //     0xab7bec: ldur            w0, [x1, #0x2b]
    // 0xab7bf0: DecompressPointer r0
    //     0xab7bf0: add             x0, x0, HEAP, lsl #32
    // 0xab7bf4: r16 = Instance_Color
    //     0xab7bf4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab7bf8: r30 = 14.000000
    //     0xab7bf8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab7bfc: ldr             lr, [lr, #0x1d8]
    // 0xab7c00: stp             lr, x16, [SP]
    // 0xab7c04: mov             x1, x0
    // 0xab7c08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab7c08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab7c0c: ldr             x4, [x4, #0x9b8]
    // 0xab7c10: r0 = copyWith()
    //     0xab7c10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab7c14: stur            x0, [fp, #-0x28]
    // 0xab7c18: r0 = Text()
    //     0xab7c18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab7c1c: mov             x1, x0
    // 0xab7c20: ldur            x0, [fp, #-0x10]
    // 0xab7c24: stur            x1, [fp, #-0x30]
    // 0xab7c28: StoreField: r1->field_b = r0
    //     0xab7c28: stur            w0, [x1, #0xb]
    // 0xab7c2c: ldur            x0, [fp, #-0x28]
    // 0xab7c30: StoreField: r1->field_13 = r0
    //     0xab7c30: stur            w0, [x1, #0x13]
    // 0xab7c34: r0 = Padding()
    //     0xab7c34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab7c38: mov             x2, x0
    // 0xab7c3c: r0 = Instance_EdgeInsets
    //     0xab7c3c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xab7c40: ldr             x0, [x0, #0xc40]
    // 0xab7c44: stur            x2, [fp, #-0x10]
    // 0xab7c48: StoreField: r2->field_f = r0
    //     0xab7c48: stur            w0, [x2, #0xf]
    // 0xab7c4c: ldur            x0, [fp, #-0x30]
    // 0xab7c50: StoreField: r2->field_b = r0
    //     0xab7c50: stur            w0, [x2, #0xb]
    // 0xab7c54: r1 = <FlexParentData>
    //     0xab7c54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xab7c58: ldr             x1, [x1, #0xe00]
    // 0xab7c5c: r0 = Expanded()
    //     0xab7c5c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xab7c60: StoreField: r0->field_13 = rZR
    //     0xab7c60: stur            xzr, [x0, #0x13]
    // 0xab7c64: r1 = Instance_FlexFit
    //     0xab7c64: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xab7c68: ldr             x1, [x1, #0xe08]
    // 0xab7c6c: StoreField: r0->field_1b = r1
    //     0xab7c6c: stur            w1, [x0, #0x1b]
    // 0xab7c70: ldur            x1, [fp, #-0x10]
    // 0xab7c74: StoreField: r0->field_b = r1
    //     0xab7c74: stur            w1, [x0, #0xb]
    // 0xab7c78: ldur            x1, [fp, #-0x20]
    // 0xab7c7c: ArrayStore: r1[4] = r0  ; List_4
    //     0xab7c7c: add             x25, x1, #0x1f
    //     0xab7c80: str             w0, [x25]
    //     0xab7c84: tbz             w0, #0, #0xab7ca0
    //     0xab7c88: ldurb           w16, [x1, #-1]
    //     0xab7c8c: ldurb           w17, [x0, #-1]
    //     0xab7c90: and             x16, x17, x16, lsr #2
    //     0xab7c94: tst             x16, HEAP, lsr #32
    //     0xab7c98: b.eq            #0xab7ca0
    //     0xab7c9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xab7ca0: ldur            x0, [fp, #-8]
    // 0xab7ca4: LoadField: r1 = r0->field_b
    //     0xab7ca4: ldur            w1, [x0, #0xb]
    // 0xab7ca8: DecompressPointer r1
    //     0xab7ca8: add             x1, x1, HEAP, lsl #32
    // 0xab7cac: cmp             w1, NULL
    // 0xab7cb0: b.eq            #0xab89b8
    // 0xab7cb4: LoadField: r2 = r1->field_b
    //     0xab7cb4: ldur            w2, [x1, #0xb]
    // 0xab7cb8: DecompressPointer r2
    //     0xab7cb8: add             x2, x2, HEAP, lsl #32
    // 0xab7cbc: cmp             w2, NULL
    // 0xab7cc0: b.ne            #0xab7ccc
    // 0xab7cc4: r3 = Null
    //     0xab7cc4: mov             x3, NULL
    // 0xab7cc8: b               #0xab7cf0
    // 0xab7ccc: LoadField: r1 = r2->field_1f
    //     0xab7ccc: ldur            w1, [x2, #0x1f]
    // 0xab7cd0: DecompressPointer r1
    //     0xab7cd0: add             x1, x1, HEAP, lsl #32
    // 0xab7cd4: cmp             w1, NULL
    // 0xab7cd8: b.ne            #0xab7ce4
    // 0xab7cdc: r1 = Null
    //     0xab7cdc: mov             x1, NULL
    // 0xab7ce0: b               #0xab7cec
    // 0xab7ce4: LoadField: r2 = r1->field_b
    //     0xab7ce4: ldur            w2, [x1, #0xb]
    // 0xab7ce8: mov             x1, x2
    // 0xab7cec: mov             x3, x1
    // 0xab7cf0: ldur            x2, [fp, #-0x18]
    // 0xab7cf4: stur            x3, [fp, #-0x10]
    // 0xab7cf8: r1 = Function '<anonymous closure>':.
    //     0xab7cf8: add             x1, PP, #0x71, lsl #12  ; [pp+0x71558] AnonymousClosure: (0xab8c54), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_bottom_sheet.dart] _OffersBottomSheetState::build (0xab789c)
    //     0xab7cfc: ldr             x1, [x1, #0x558]
    // 0xab7d00: r0 = AllocateClosure()
    //     0xab7d00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab7d04: stur            x0, [fp, #-0x28]
    // 0xab7d08: r0 = ListView()
    //     0xab7d08: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xab7d0c: stur            x0, [fp, #-0x30]
    // 0xab7d10: r16 = Instance_NeverScrollableScrollPhysics
    //     0xab7d10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xab7d14: ldr             x16, [x16, #0x1c8]
    // 0xab7d18: r30 = true
    //     0xab7d18: add             lr, NULL, #0x20  ; true
    // 0xab7d1c: stp             lr, x16, [SP]
    // 0xab7d20: mov             x1, x0
    // 0xab7d24: ldur            x2, [fp, #-0x28]
    // 0xab7d28: ldur            x3, [fp, #-0x10]
    // 0xab7d2c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xab7d2c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xab7d30: ldr             x4, [x4, #0xd18]
    // 0xab7d34: r0 = ListView.builder()
    //     0xab7d34: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xab7d38: ldur            x1, [fp, #-0x20]
    // 0xab7d3c: ldur            x0, [fp, #-0x30]
    // 0xab7d40: ArrayStore: r1[5] = r0  ; List_4
    //     0xab7d40: add             x25, x1, #0x23
    //     0xab7d44: str             w0, [x25]
    //     0xab7d48: tbz             w0, #0, #0xab7d64
    //     0xab7d4c: ldurb           w16, [x1, #-1]
    //     0xab7d50: ldurb           w17, [x0, #-1]
    //     0xab7d54: and             x16, x17, x16, lsr #2
    //     0xab7d58: tst             x16, HEAP, lsr #32
    //     0xab7d5c: b.eq            #0xab7d64
    //     0xab7d60: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xab7d64: ldur            x0, [fp, #-8]
    // 0xab7d68: LoadField: r1 = r0->field_b
    //     0xab7d68: ldur            w1, [x0, #0xb]
    // 0xab7d6c: DecompressPointer r1
    //     0xab7d6c: add             x1, x1, HEAP, lsl #32
    // 0xab7d70: cmp             w1, NULL
    // 0xab7d74: b.eq            #0xab89bc
    // 0xab7d78: LoadField: r2 = r1->field_b
    //     0xab7d78: ldur            w2, [x1, #0xb]
    // 0xab7d7c: DecompressPointer r2
    //     0xab7d7c: add             x2, x2, HEAP, lsl #32
    // 0xab7d80: stur            x2, [fp, #-0x28]
    // 0xab7d84: cmp             w2, NULL
    // 0xab7d88: b.ne            #0xab7d94
    // 0xab7d8c: r1 = Null
    //     0xab7d8c: mov             x1, NULL
    // 0xab7d90: b               #0xab7d9c
    // 0xab7d94: LoadField: r1 = r2->field_27
    //     0xab7d94: ldur            w1, [x2, #0x27]
    // 0xab7d98: DecompressPointer r1
    //     0xab7d98: add             x1, x1, HEAP, lsl #32
    // 0xab7d9c: cmp             w1, NULL
    // 0xab7da0: b.eq            #0xab7db4
    // 0xab7da4: tbnz            w1, #4, #0xab7db4
    // 0xab7da8: r1 = Instance_Color
    //     0xab7da8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0xab7dac: ldr             x1, [x1, #0xb18]
    // 0xab7db0: b               #0xab7db8
    // 0xab7db4: r1 = Instance_Color
    //     0xab7db4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab7db8: stur            x1, [fp, #-0x10]
    // 0xab7dbc: r0 = Radius()
    //     0xab7dbc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xab7dc0: d0 = 12.000000
    //     0xab7dc0: fmov            d0, #12.00000000
    // 0xab7dc4: stur            x0, [fp, #-0x30]
    // 0xab7dc8: StoreField: r0->field_7 = d0
    //     0xab7dc8: stur            d0, [x0, #7]
    // 0xab7dcc: StoreField: r0->field_f = d0
    //     0xab7dcc: stur            d0, [x0, #0xf]
    // 0xab7dd0: r0 = BorderRadius()
    //     0xab7dd0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xab7dd4: mov             x1, x0
    // 0xab7dd8: ldur            x0, [fp, #-0x30]
    // 0xab7ddc: stur            x1, [fp, #-0x38]
    // 0xab7de0: StoreField: r1->field_7 = r0
    //     0xab7de0: stur            w0, [x1, #7]
    // 0xab7de4: StoreField: r1->field_b = r0
    //     0xab7de4: stur            w0, [x1, #0xb]
    // 0xab7de8: StoreField: r1->field_f = r0
    //     0xab7de8: stur            w0, [x1, #0xf]
    // 0xab7dec: StoreField: r1->field_13 = r0
    //     0xab7dec: stur            w0, [x1, #0x13]
    // 0xab7df0: r0 = BoxDecoration()
    //     0xab7df0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xab7df4: mov             x2, x0
    // 0xab7df8: ldur            x0, [fp, #-0x10]
    // 0xab7dfc: stur            x2, [fp, #-0x30]
    // 0xab7e00: StoreField: r2->field_7 = r0
    //     0xab7e00: stur            w0, [x2, #7]
    // 0xab7e04: ldur            x0, [fp, #-0x38]
    // 0xab7e08: StoreField: r2->field_13 = r0
    //     0xab7e08: stur            w0, [x2, #0x13]
    // 0xab7e0c: r0 = Instance_BoxShape
    //     0xab7e0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xab7e10: ldr             x0, [x0, #0x80]
    // 0xab7e14: StoreField: r2->field_23 = r0
    //     0xab7e14: stur            w0, [x2, #0x23]
    // 0xab7e18: ldur            x0, [fp, #-0x28]
    // 0xab7e1c: cmp             w0, NULL
    // 0xab7e20: b.ne            #0xab7e2c
    // 0xab7e24: r0 = Null
    //     0xab7e24: mov             x0, NULL
    // 0xab7e28: b               #0xab7e38
    // 0xab7e2c: LoadField: r1 = r0->field_1b
    //     0xab7e2c: ldur            w1, [x0, #0x1b]
    // 0xab7e30: DecompressPointer r1
    //     0xab7e30: add             x1, x1, HEAP, lsl #32
    // 0xab7e34: mov             x0, x1
    // 0xab7e38: cmp             w0, NULL
    // 0xab7e3c: b.ne            #0xab7e48
    // 0xab7e40: r4 = ""
    //     0xab7e40: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab7e44: b               #0xab7e4c
    // 0xab7e48: mov             x4, x0
    // 0xab7e4c: ldur            x0, [fp, #-8]
    // 0xab7e50: ldur            x3, [fp, #-0x18]
    // 0xab7e54: stur            x4, [fp, #-0x10]
    // 0xab7e58: LoadField: r1 = r3->field_13
    //     0xab7e58: ldur            w1, [x3, #0x13]
    // 0xab7e5c: DecompressPointer r1
    //     0xab7e5c: add             x1, x1, HEAP, lsl #32
    // 0xab7e60: r0 = of()
    //     0xab7e60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab7e64: LoadField: r1 = r0->field_87
    //     0xab7e64: ldur            w1, [x0, #0x87]
    // 0xab7e68: DecompressPointer r1
    //     0xab7e68: add             x1, x1, HEAP, lsl #32
    // 0xab7e6c: LoadField: r0 = r1->field_2b
    //     0xab7e6c: ldur            w0, [x1, #0x2b]
    // 0xab7e70: DecompressPointer r0
    //     0xab7e70: add             x0, x0, HEAP, lsl #32
    // 0xab7e74: r16 = 12.000000
    //     0xab7e74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xab7e78: ldr             x16, [x16, #0x9e8]
    // 0xab7e7c: r30 = Instance_Color
    //     0xab7e7c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab7e80: stp             lr, x16, [SP]
    // 0xab7e84: mov             x1, x0
    // 0xab7e88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab7e88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab7e8c: ldr             x4, [x4, #0xaa0]
    // 0xab7e90: r0 = copyWith()
    //     0xab7e90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab7e94: stur            x0, [fp, #-0x28]
    // 0xab7e98: r0 = Text()
    //     0xab7e98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab7e9c: mov             x1, x0
    // 0xab7ea0: ldur            x0, [fp, #-0x10]
    // 0xab7ea4: stur            x1, [fp, #-0x38]
    // 0xab7ea8: StoreField: r1->field_b = r0
    //     0xab7ea8: stur            w0, [x1, #0xb]
    // 0xab7eac: ldur            x0, [fp, #-0x28]
    // 0xab7eb0: StoreField: r1->field_13 = r0
    //     0xab7eb0: stur            w0, [x1, #0x13]
    // 0xab7eb4: r0 = Align()
    //     0xab7eb4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xab7eb8: mov             x1, x0
    // 0xab7ebc: r0 = Instance_Alignment
    //     0xab7ebc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xab7ec0: ldr             x0, [x0, #0xb10]
    // 0xab7ec4: stur            x1, [fp, #-0x10]
    // 0xab7ec8: StoreField: r1->field_f = r0
    //     0xab7ec8: stur            w0, [x1, #0xf]
    // 0xab7ecc: ldur            x2, [fp, #-0x38]
    // 0xab7ed0: StoreField: r1->field_b = r2
    //     0xab7ed0: stur            w2, [x1, #0xb]
    // 0xab7ed4: r0 = Container()
    //     0xab7ed4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab7ed8: stur            x0, [fp, #-0x28]
    // 0xab7edc: r16 = inf
    //     0xab7edc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xab7ee0: ldr             x16, [x16, #0x9f8]
    // 0xab7ee4: ldur            lr, [fp, #-0x30]
    // 0xab7ee8: stp             lr, x16, [SP, #8]
    // 0xab7eec: ldur            x16, [fp, #-0x10]
    // 0xab7ef0: str             x16, [SP]
    // 0xab7ef4: mov             x1, x0
    // 0xab7ef8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xab7ef8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xab7efc: ldr             x4, [x4, #0x830]
    // 0xab7f00: r0 = Container()
    //     0xab7f00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab7f04: r0 = SizedBox()
    //     0xab7f04: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xab7f08: mov             x1, x0
    // 0xab7f0c: r0 = 20.000000
    //     0xab7f0c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xab7f10: ldr             x0, [x0, #0xac8]
    // 0xab7f14: stur            x1, [fp, #-0x10]
    // 0xab7f18: StoreField: r1->field_13 = r0
    //     0xab7f18: stur            w0, [x1, #0x13]
    // 0xab7f1c: ldur            x0, [fp, #-0x28]
    // 0xab7f20: StoreField: r1->field_b = r0
    //     0xab7f20: stur            w0, [x1, #0xb]
    // 0xab7f24: r0 = Padding()
    //     0xab7f24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab7f28: mov             x1, x0
    // 0xab7f2c: r0 = Instance_EdgeInsets
    //     0xab7f2c: add             x0, PP, #0x71, lsl #12  ; [pp+0x71560] Obj!EdgeInsets@d58cd1
    //     0xab7f30: ldr             x0, [x0, #0x560]
    // 0xab7f34: StoreField: r1->field_f = r0
    //     0xab7f34: stur            w0, [x1, #0xf]
    // 0xab7f38: ldur            x0, [fp, #-0x10]
    // 0xab7f3c: StoreField: r1->field_b = r0
    //     0xab7f3c: stur            w0, [x1, #0xb]
    // 0xab7f40: mov             x0, x1
    // 0xab7f44: ldur            x1, [fp, #-0x20]
    // 0xab7f48: ArrayStore: r1[6] = r0  ; List_4
    //     0xab7f48: add             x25, x1, #0x27
    //     0xab7f4c: str             w0, [x25]
    //     0xab7f50: tbz             w0, #0, #0xab7f6c
    //     0xab7f54: ldurb           w16, [x1, #-1]
    //     0xab7f58: ldurb           w17, [x0, #-1]
    //     0xab7f5c: and             x16, x17, x16, lsr #2
    //     0xab7f60: tst             x16, HEAP, lsr #32
    //     0xab7f64: b.eq            #0xab7f6c
    //     0xab7f68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xab7f6c: ldur            x1, [fp, #-8]
    // 0xab7f70: LoadField: r0 = r1->field_b
    //     0xab7f70: ldur            w0, [x1, #0xb]
    // 0xab7f74: DecompressPointer r0
    //     0xab7f74: add             x0, x0, HEAP, lsl #32
    // 0xab7f78: cmp             w0, NULL
    // 0xab7f7c: b.eq            #0xab89c0
    // 0xab7f80: LoadField: r2 = r0->field_13
    //     0xab7f80: ldur            w2, [x0, #0x13]
    // 0xab7f84: DecompressPointer r2
    //     0xab7f84: add             x2, x2, HEAP, lsl #32
    // 0xab7f88: r0 = LoadClassIdInstr(r2)
    //     0xab7f88: ldur            x0, [x2, #-1]
    //     0xab7f8c: ubfx            x0, x0, #0xc, #0x14
    // 0xab7f90: r16 = "checkout_offers"
    //     0xab7f90: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xab7f94: ldr             x16, [x16, #0x1c8]
    // 0xab7f98: stp             x16, x2, [SP]
    // 0xab7f9c: mov             lr, x0
    // 0xab7fa0: ldr             lr, [x21, lr, lsl #3]
    // 0xab7fa4: blr             lr
    // 0xab7fa8: tbz             w0, #4, #0xab7fd0
    // 0xab7fac: ldur            x0, [fp, #-8]
    // 0xab7fb0: LoadField: r1 = r0->field_b
    //     0xab7fb0: ldur            w1, [x0, #0xb]
    // 0xab7fb4: DecompressPointer r1
    //     0xab7fb4: add             x1, x1, HEAP, lsl #32
    // 0xab7fb8: cmp             w1, NULL
    // 0xab7fbc: b.eq            #0xab89c4
    // 0xab7fc0: LoadField: r2 = r1->field_1f
    //     0xab7fc0: ldur            w2, [x1, #0x1f]
    // 0xab7fc4: DecompressPointer r2
    //     0xab7fc4: add             x2, x2, HEAP, lsl #32
    // 0xab7fc8: mov             x1, x2
    // 0xab7fcc: b               #0xab7fd8
    // 0xab7fd0: ldur            x0, [fp, #-8]
    // 0xab7fd4: r1 = false
    //     0xab7fd4: add             x1, NULL, #0x30  ; false
    // 0xab7fd8: ldur            x2, [fp, #-0x18]
    // 0xab7fdc: stur            x1, [fp, #-0x10]
    // 0xab7fe0: r16 = <EdgeInsets>
    //     0xab7fe0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xab7fe4: ldr             x16, [x16, #0xda0]
    // 0xab7fe8: r30 = Instance_EdgeInsets
    //     0xab7fe8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xab7fec: ldr             lr, [lr, #0x1f0]
    // 0xab7ff0: stp             lr, x16, [SP]
    // 0xab7ff4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab7ff4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab7ff8: r0 = all()
    //     0xab7ff8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab7ffc: stur            x0, [fp, #-0x28]
    // 0xab8000: r0 = Radius()
    //     0xab8000: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xab8004: d0 = 30.000000
    //     0xab8004: fmov            d0, #30.00000000
    // 0xab8008: stur            x0, [fp, #-0x30]
    // 0xab800c: StoreField: r0->field_7 = d0
    //     0xab800c: stur            d0, [x0, #7]
    // 0xab8010: StoreField: r0->field_f = d0
    //     0xab8010: stur            d0, [x0, #0xf]
    // 0xab8014: r0 = BorderRadius()
    //     0xab8014: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xab8018: mov             x2, x0
    // 0xab801c: ldur            x0, [fp, #-0x30]
    // 0xab8020: stur            x2, [fp, #-0x38]
    // 0xab8024: StoreField: r2->field_7 = r0
    //     0xab8024: stur            w0, [x2, #7]
    // 0xab8028: StoreField: r2->field_b = r0
    //     0xab8028: stur            w0, [x2, #0xb]
    // 0xab802c: StoreField: r2->field_f = r0
    //     0xab802c: stur            w0, [x2, #0xf]
    // 0xab8030: StoreField: r2->field_13 = r0
    //     0xab8030: stur            w0, [x2, #0x13]
    // 0xab8034: ldur            x0, [fp, #-0x18]
    // 0xab8038: LoadField: r1 = r0->field_13
    //     0xab8038: ldur            w1, [x0, #0x13]
    // 0xab803c: DecompressPointer r1
    //     0xab803c: add             x1, x1, HEAP, lsl #32
    // 0xab8040: r0 = of()
    //     0xab8040: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab8044: LoadField: r1 = r0->field_5b
    //     0xab8044: ldur            w1, [x0, #0x5b]
    // 0xab8048: DecompressPointer r1
    //     0xab8048: add             x1, x1, HEAP, lsl #32
    // 0xab804c: r0 = LoadClassIdInstr(r1)
    //     0xab804c: ldur            x0, [x1, #-1]
    //     0xab8050: ubfx            x0, x0, #0xc, #0x14
    // 0xab8054: d0 = 0.400000
    //     0xab8054: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xab8058: r0 = GDT[cid_x0 + -0xffa]()
    //     0xab8058: sub             lr, x0, #0xffa
    //     0xab805c: ldr             lr, [x21, lr, lsl #3]
    //     0xab8060: blr             lr
    // 0xab8064: stur            x0, [fp, #-0x30]
    // 0xab8068: r0 = BorderSide()
    //     0xab8068: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xab806c: mov             x1, x0
    // 0xab8070: ldur            x0, [fp, #-0x30]
    // 0xab8074: stur            x1, [fp, #-0x40]
    // 0xab8078: StoreField: r1->field_7 = r0
    //     0xab8078: stur            w0, [x1, #7]
    // 0xab807c: d0 = 1.000000
    //     0xab807c: fmov            d0, #1.00000000
    // 0xab8080: StoreField: r1->field_b = d0
    //     0xab8080: stur            d0, [x1, #0xb]
    // 0xab8084: r0 = Instance_BorderStyle
    //     0xab8084: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xab8088: ldr             x0, [x0, #0xf68]
    // 0xab808c: StoreField: r1->field_13 = r0
    //     0xab808c: stur            w0, [x1, #0x13]
    // 0xab8090: d0 = -1.000000
    //     0xab8090: fmov            d0, #-1.00000000
    // 0xab8094: ArrayStore: r1[0] = d0  ; List_8
    //     0xab8094: stur            d0, [x1, #0x17]
    // 0xab8098: r0 = RoundedRectangleBorder()
    //     0xab8098: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xab809c: mov             x1, x0
    // 0xab80a0: ldur            x0, [fp, #-0x38]
    // 0xab80a4: StoreField: r1->field_b = r0
    //     0xab80a4: stur            w0, [x1, #0xb]
    // 0xab80a8: ldur            x0, [fp, #-0x40]
    // 0xab80ac: StoreField: r1->field_7 = r0
    //     0xab80ac: stur            w0, [x1, #7]
    // 0xab80b0: r16 = <RoundedRectangleBorder>
    //     0xab80b0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xab80b4: ldr             x16, [x16, #0xf78]
    // 0xab80b8: stp             x1, x16, [SP]
    // 0xab80bc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab80bc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab80c0: r0 = all()
    //     0xab80c0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab80c4: stur            x0, [fp, #-0x30]
    // 0xab80c8: r0 = ButtonStyle()
    //     0xab80c8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xab80cc: mov             x1, x0
    // 0xab80d0: ldur            x0, [fp, #-0x28]
    // 0xab80d4: stur            x1, [fp, #-0x38]
    // 0xab80d8: StoreField: r1->field_23 = r0
    //     0xab80d8: stur            w0, [x1, #0x23]
    // 0xab80dc: ldur            x0, [fp, #-0x30]
    // 0xab80e0: StoreField: r1->field_43 = r0
    //     0xab80e0: stur            w0, [x1, #0x43]
    // 0xab80e4: r0 = TextButtonThemeData()
    //     0xab80e4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xab80e8: mov             x2, x0
    // 0xab80ec: ldur            x0, [fp, #-0x38]
    // 0xab80f0: stur            x2, [fp, #-0x28]
    // 0xab80f4: StoreField: r2->field_7 = r0
    //     0xab80f4: stur            w0, [x2, #7]
    // 0xab80f8: r1 = "other offers"
    //     0xab80f8: add             x1, PP, #0x71, lsl #12  ; [pp+0x714f8] "other offers"
    //     0xab80fc: ldr             x1, [x1, #0x4f8]
    // 0xab8100: r0 = capitalizeFirstWord()
    //     0xab8100: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab8104: ldur            x2, [fp, #-0x18]
    // 0xab8108: stur            x0, [fp, #-0x30]
    // 0xab810c: LoadField: r1 = r2->field_13
    //     0xab810c: ldur            w1, [x2, #0x13]
    // 0xab8110: DecompressPointer r1
    //     0xab8110: add             x1, x1, HEAP, lsl #32
    // 0xab8114: r0 = of()
    //     0xab8114: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab8118: LoadField: r1 = r0->field_87
    //     0xab8118: ldur            w1, [x0, #0x87]
    // 0xab811c: DecompressPointer r1
    //     0xab811c: add             x1, x1, HEAP, lsl #32
    // 0xab8120: LoadField: r0 = r1->field_7
    //     0xab8120: ldur            w0, [x1, #7]
    // 0xab8124: DecompressPointer r0
    //     0xab8124: add             x0, x0, HEAP, lsl #32
    // 0xab8128: r16 = 14.000000
    //     0xab8128: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab812c: ldr             x16, [x16, #0x1d8]
    // 0xab8130: r30 = Instance_Color
    //     0xab8130: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab8134: stp             lr, x16, [SP]
    // 0xab8138: mov             x1, x0
    // 0xab813c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab813c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab8140: ldr             x4, [x4, #0xaa0]
    // 0xab8144: r0 = copyWith()
    //     0xab8144: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab8148: stur            x0, [fp, #-0x38]
    // 0xab814c: r0 = Text()
    //     0xab814c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab8150: mov             x3, x0
    // 0xab8154: ldur            x0, [fp, #-0x30]
    // 0xab8158: stur            x3, [fp, #-0x40]
    // 0xab815c: StoreField: r3->field_b = r0
    //     0xab815c: stur            w0, [x3, #0xb]
    // 0xab8160: ldur            x0, [fp, #-0x38]
    // 0xab8164: StoreField: r3->field_13 = r0
    //     0xab8164: stur            w0, [x3, #0x13]
    // 0xab8168: ldur            x2, [fp, #-0x18]
    // 0xab816c: r1 = Function '<anonymous closure>':.
    //     0xab816c: add             x1, PP, #0x71, lsl #12  ; [pp+0x71568] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xab8170: ldr             x1, [x1, #0x568]
    // 0xab8174: r0 = AllocateClosure()
    //     0xab8174: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab8178: stur            x0, [fp, #-0x30]
    // 0xab817c: r0 = TextButton()
    //     0xab817c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xab8180: mov             x1, x0
    // 0xab8184: ldur            x0, [fp, #-0x30]
    // 0xab8188: stur            x1, [fp, #-0x38]
    // 0xab818c: StoreField: r1->field_b = r0
    //     0xab818c: stur            w0, [x1, #0xb]
    // 0xab8190: r0 = false
    //     0xab8190: add             x0, NULL, #0x30  ; false
    // 0xab8194: StoreField: r1->field_27 = r0
    //     0xab8194: stur            w0, [x1, #0x27]
    // 0xab8198: r2 = true
    //     0xab8198: add             x2, NULL, #0x20  ; true
    // 0xab819c: StoreField: r1->field_2f = r2
    //     0xab819c: stur            w2, [x1, #0x2f]
    // 0xab81a0: ldur            x3, [fp, #-0x40]
    // 0xab81a4: StoreField: r1->field_37 = r3
    //     0xab81a4: stur            w3, [x1, #0x37]
    // 0xab81a8: r0 = TextButtonTheme()
    //     0xab81a8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xab81ac: mov             x1, x0
    // 0xab81b0: ldur            x0, [fp, #-0x28]
    // 0xab81b4: stur            x1, [fp, #-0x30]
    // 0xab81b8: StoreField: r1->field_f = r0
    //     0xab81b8: stur            w0, [x1, #0xf]
    // 0xab81bc: ldur            x0, [fp, #-0x38]
    // 0xab81c0: StoreField: r1->field_b = r0
    //     0xab81c0: stur            w0, [x1, #0xb]
    // 0xab81c4: r0 = SizedBox()
    //     0xab81c4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xab81c8: mov             x1, x0
    // 0xab81cc: r0 = inf
    //     0xab81cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xab81d0: ldr             x0, [x0, #0x9f8]
    // 0xab81d4: stur            x1, [fp, #-0x28]
    // 0xab81d8: StoreField: r1->field_f = r0
    //     0xab81d8: stur            w0, [x1, #0xf]
    // 0xab81dc: ldur            x2, [fp, #-0x30]
    // 0xab81e0: StoreField: r1->field_b = r2
    //     0xab81e0: stur            w2, [x1, #0xb]
    // 0xab81e4: r0 = Align()
    //     0xab81e4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xab81e8: mov             x1, x0
    // 0xab81ec: r0 = Instance_Alignment
    //     0xab81ec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xab81f0: ldr             x0, [x0, #0xb10]
    // 0xab81f4: stur            x1, [fp, #-0x30]
    // 0xab81f8: StoreField: r1->field_f = r0
    //     0xab81f8: stur            w0, [x1, #0xf]
    // 0xab81fc: ldur            x2, [fp, #-0x28]
    // 0xab8200: StoreField: r1->field_b = r2
    //     0xab8200: stur            w2, [x1, #0xb]
    // 0xab8204: r0 = Padding()
    //     0xab8204: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab8208: mov             x1, x0
    // 0xab820c: r0 = Instance_EdgeInsets
    //     0xab820c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xab8210: ldr             x0, [x0, #0x980]
    // 0xab8214: stur            x1, [fp, #-0x28]
    // 0xab8218: StoreField: r1->field_f = r0
    //     0xab8218: stur            w0, [x1, #0xf]
    // 0xab821c: ldur            x0, [fp, #-0x30]
    // 0xab8220: StoreField: r1->field_b = r0
    //     0xab8220: stur            w0, [x1, #0xb]
    // 0xab8224: r0 = Visibility()
    //     0xab8224: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xab8228: mov             x1, x0
    // 0xab822c: ldur            x0, [fp, #-0x28]
    // 0xab8230: StoreField: r1->field_b = r0
    //     0xab8230: stur            w0, [x1, #0xb]
    // 0xab8234: r2 = Instance_SizedBox
    //     0xab8234: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xab8238: StoreField: r1->field_f = r2
    //     0xab8238: stur            w2, [x1, #0xf]
    // 0xab823c: ldur            x0, [fp, #-0x10]
    // 0xab8240: StoreField: r1->field_13 = r0
    //     0xab8240: stur            w0, [x1, #0x13]
    // 0xab8244: r3 = false
    //     0xab8244: add             x3, NULL, #0x30  ; false
    // 0xab8248: ArrayStore: r1[0] = r3  ; List_4
    //     0xab8248: stur            w3, [x1, #0x17]
    // 0xab824c: StoreField: r1->field_1b = r3
    //     0xab824c: stur            w3, [x1, #0x1b]
    // 0xab8250: StoreField: r1->field_1f = r3
    //     0xab8250: stur            w3, [x1, #0x1f]
    // 0xab8254: StoreField: r1->field_23 = r3
    //     0xab8254: stur            w3, [x1, #0x23]
    // 0xab8258: StoreField: r1->field_27 = r3
    //     0xab8258: stur            w3, [x1, #0x27]
    // 0xab825c: StoreField: r1->field_2b = r3
    //     0xab825c: stur            w3, [x1, #0x2b]
    // 0xab8260: mov             x0, x1
    // 0xab8264: ldur            x1, [fp, #-0x20]
    // 0xab8268: ArrayStore: r1[7] = r0  ; List_4
    //     0xab8268: add             x25, x1, #0x2b
    //     0xab826c: str             w0, [x25]
    //     0xab8270: tbz             w0, #0, #0xab828c
    //     0xab8274: ldurb           w16, [x1, #-1]
    //     0xab8278: ldurb           w17, [x0, #-1]
    //     0xab827c: and             x16, x17, x16, lsr #2
    //     0xab8280: tst             x16, HEAP, lsr #32
    //     0xab8284: b.eq            #0xab828c
    //     0xab8288: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xab828c: ldur            x1, [fp, #-8]
    // 0xab8290: LoadField: r0 = r1->field_b
    //     0xab8290: ldur            w0, [x1, #0xb]
    // 0xab8294: DecompressPointer r0
    //     0xab8294: add             x0, x0, HEAP, lsl #32
    // 0xab8298: cmp             w0, NULL
    // 0xab829c: b.eq            #0xab89c8
    // 0xab82a0: LoadField: r4 = r0->field_13
    //     0xab82a0: ldur            w4, [x0, #0x13]
    // 0xab82a4: DecompressPointer r4
    //     0xab82a4: add             x4, x4, HEAP, lsl #32
    // 0xab82a8: r0 = LoadClassIdInstr(r4)
    //     0xab82a8: ldur            x0, [x4, #-1]
    //     0xab82ac: ubfx            x0, x0, #0xc, #0x14
    // 0xab82b0: r16 = "checkout_offers"
    //     0xab82b0: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xab82b4: ldr             x16, [x16, #0x1c8]
    // 0xab82b8: stp             x16, x4, [SP]
    // 0xab82bc: mov             lr, x0
    // 0xab82c0: ldr             lr, [x21, lr, lsl #3]
    // 0xab82c4: blr             lr
    // 0xab82c8: tbz             w0, #4, #0xab82f0
    // 0xab82cc: ldur            x0, [fp, #-8]
    // 0xab82d0: LoadField: r1 = r0->field_b
    //     0xab82d0: ldur            w1, [x0, #0xb]
    // 0xab82d4: DecompressPointer r1
    //     0xab82d4: add             x1, x1, HEAP, lsl #32
    // 0xab82d8: cmp             w1, NULL
    // 0xab82dc: b.eq            #0xab89cc
    // 0xab82e0: LoadField: r2 = r1->field_1f
    //     0xab82e0: ldur            w2, [x1, #0x1f]
    // 0xab82e4: DecompressPointer r2
    //     0xab82e4: add             x2, x2, HEAP, lsl #32
    // 0xab82e8: mov             x1, x2
    // 0xab82ec: b               #0xab82f8
    // 0xab82f0: ldur            x0, [fp, #-8]
    // 0xab82f4: r1 = false
    //     0xab82f4: add             x1, NULL, #0x30  ; false
    // 0xab82f8: stur            x1, [fp, #-0x10]
    // 0xab82fc: r16 = <EdgeInsets>
    //     0xab82fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xab8300: ldr             x16, [x16, #0xda0]
    // 0xab8304: r30 = Instance_EdgeInsets
    //     0xab8304: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xab8308: ldr             lr, [lr, #0x1f0]
    // 0xab830c: stp             lr, x16, [SP]
    // 0xab8310: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab8310: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab8314: r0 = all()
    //     0xab8314: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab8318: mov             x2, x0
    // 0xab831c: ldur            x1, [fp, #-8]
    // 0xab8320: stur            x2, [fp, #-0x28]
    // 0xab8324: LoadField: r0 = r1->field_b
    //     0xab8324: ldur            w0, [x1, #0xb]
    // 0xab8328: DecompressPointer r0
    //     0xab8328: add             x0, x0, HEAP, lsl #32
    // 0xab832c: cmp             w0, NULL
    // 0xab8330: b.eq            #0xab89d0
    // 0xab8334: LoadField: r3 = r0->field_f
    //     0xab8334: ldur            w3, [x0, #0xf]
    // 0xab8338: DecompressPointer r3
    //     0xab8338: add             x3, x3, HEAP, lsl #32
    // 0xab833c: LoadField: r4 = r0->field_b
    //     0xab833c: ldur            w4, [x0, #0xb]
    // 0xab8340: DecompressPointer r4
    //     0xab8340: add             x4, x4, HEAP, lsl #32
    // 0xab8344: cmp             w4, NULL
    // 0xab8348: b.ne            #0xab8354
    // 0xab834c: r0 = Null
    //     0xab834c: mov             x0, NULL
    // 0xab8350: b               #0xab835c
    // 0xab8354: LoadField: r0 = r4->field_7
    //     0xab8354: ldur            w0, [x4, #7]
    // 0xab8358: DecompressPointer r0
    //     0xab8358: add             x0, x0, HEAP, lsl #32
    // 0xab835c: r4 = LoadClassIdInstr(r3)
    //     0xab835c: ldur            x4, [x3, #-1]
    //     0xab8360: ubfx            x4, x4, #0xc, #0x14
    // 0xab8364: stp             x0, x3, [SP]
    // 0xab8368: mov             x0, x4
    // 0xab836c: mov             lr, x0
    // 0xab8370: ldr             lr, [x21, lr, lsl #3]
    // 0xab8374: blr             lr
    // 0xab8378: tbnz            w0, #4, #0xab8384
    // 0xab837c: r1 = Instance_Color
    //     0xab837c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab8380: b               #0xab83f8
    // 0xab8384: ldur            x0, [fp, #-8]
    // 0xab8388: LoadField: r1 = r0->field_b
    //     0xab8388: ldur            w1, [x0, #0xb]
    // 0xab838c: DecompressPointer r1
    //     0xab838c: add             x1, x1, HEAP, lsl #32
    // 0xab8390: cmp             w1, NULL
    // 0xab8394: b.eq            #0xab89d4
    // 0xab8398: LoadField: r2 = r1->field_b
    //     0xab8398: ldur            w2, [x1, #0xb]
    // 0xab839c: DecompressPointer r2
    //     0xab839c: add             x2, x2, HEAP, lsl #32
    // 0xab83a0: cmp             w2, NULL
    // 0xab83a4: b.ne            #0xab83b0
    // 0xab83a8: r1 = Null
    //     0xab83a8: mov             x1, NULL
    // 0xab83ac: b               #0xab83b8
    // 0xab83b0: LoadField: r1 = r2->field_27
    //     0xab83b0: ldur            w1, [x2, #0x27]
    // 0xab83b4: DecompressPointer r1
    //     0xab83b4: add             x1, x1, HEAP, lsl #32
    // 0xab83b8: cmp             w1, NULL
    // 0xab83bc: b.eq            #0xab83e4
    // 0xab83c0: tbnz            w1, #4, #0xab83e4
    // 0xab83c4: ldur            x2, [fp, #-0x18]
    // 0xab83c8: LoadField: r1 = r2->field_13
    //     0xab83c8: ldur            w1, [x2, #0x13]
    // 0xab83cc: DecompressPointer r1
    //     0xab83cc: add             x1, x1, HEAP, lsl #32
    // 0xab83d0: r0 = of()
    //     0xab83d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab83d4: LoadField: r1 = r0->field_5b
    //     0xab83d4: ldur            w1, [x0, #0x5b]
    // 0xab83d8: DecompressPointer r1
    //     0xab83d8: add             x1, x1, HEAP, lsl #32
    // 0xab83dc: mov             x0, x1
    // 0xab83e0: b               #0xab83f4
    // 0xab83e4: r1 = Instance_MaterialColor
    //     0xab83e4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xab83e8: ldr             x1, [x1, #0xdc0]
    // 0xab83ec: d0 = 0.200000
    //     0xab83ec: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xab83f0: r0 = withOpacity()
    //     0xab83f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xab83f4: mov             x1, x0
    // 0xab83f8: ldur            x0, [fp, #-8]
    // 0xab83fc: r16 = <Color>
    //     0xab83fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xab8400: ldr             x16, [x16, #0xf80]
    // 0xab8404: stp             x1, x16, [SP]
    // 0xab8408: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab8408: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab840c: r0 = all()
    //     0xab840c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab8410: mov             x2, x0
    // 0xab8414: ldur            x1, [fp, #-8]
    // 0xab8418: stur            x2, [fp, #-0x30]
    // 0xab841c: LoadField: r0 = r1->field_b
    //     0xab841c: ldur            w0, [x1, #0xb]
    // 0xab8420: DecompressPointer r0
    //     0xab8420: add             x0, x0, HEAP, lsl #32
    // 0xab8424: cmp             w0, NULL
    // 0xab8428: b.eq            #0xab89d8
    // 0xab842c: LoadField: r3 = r0->field_f
    //     0xab842c: ldur            w3, [x0, #0xf]
    // 0xab8430: DecompressPointer r3
    //     0xab8430: add             x3, x3, HEAP, lsl #32
    // 0xab8434: LoadField: r4 = r0->field_b
    //     0xab8434: ldur            w4, [x0, #0xb]
    // 0xab8438: DecompressPointer r4
    //     0xab8438: add             x4, x4, HEAP, lsl #32
    // 0xab843c: cmp             w4, NULL
    // 0xab8440: b.ne            #0xab844c
    // 0xab8444: r0 = Null
    //     0xab8444: mov             x0, NULL
    // 0xab8448: b               #0xab8454
    // 0xab844c: LoadField: r0 = r4->field_7
    //     0xab844c: ldur            w0, [x4, #7]
    // 0xab8450: DecompressPointer r0
    //     0xab8450: add             x0, x0, HEAP, lsl #32
    // 0xab8454: r4 = LoadClassIdInstr(r3)
    //     0xab8454: ldur            x4, [x3, #-1]
    //     0xab8458: ubfx            x4, x4, #0xc, #0x14
    // 0xab845c: stp             x0, x3, [SP]
    // 0xab8460: mov             x0, x4
    // 0xab8464: mov             lr, x0
    // 0xab8468: ldr             lr, [x21, lr, lsl #3]
    // 0xab846c: blr             lr
    // 0xab8470: tbnz            w0, #4, #0xab84cc
    // 0xab8474: r0 = Radius()
    //     0xab8474: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xab8478: d0 = 30.000000
    //     0xab8478: fmov            d0, #30.00000000
    // 0xab847c: stur            x0, [fp, #-0x38]
    // 0xab8480: StoreField: r0->field_7 = d0
    //     0xab8480: stur            d0, [x0, #7]
    // 0xab8484: StoreField: r0->field_f = d0
    //     0xab8484: stur            d0, [x0, #0xf]
    // 0xab8488: r0 = BorderRadius()
    //     0xab8488: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xab848c: mov             x1, x0
    // 0xab8490: ldur            x0, [fp, #-0x38]
    // 0xab8494: stur            x1, [fp, #-0x40]
    // 0xab8498: StoreField: r1->field_7 = r0
    //     0xab8498: stur            w0, [x1, #7]
    // 0xab849c: StoreField: r1->field_b = r0
    //     0xab849c: stur            w0, [x1, #0xb]
    // 0xab84a0: StoreField: r1->field_f = r0
    //     0xab84a0: stur            w0, [x1, #0xf]
    // 0xab84a4: StoreField: r1->field_13 = r0
    //     0xab84a4: stur            w0, [x1, #0x13]
    // 0xab84a8: r0 = RoundedRectangleBorder()
    //     0xab84a8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xab84ac: mov             x1, x0
    // 0xab84b0: ldur            x0, [fp, #-0x40]
    // 0xab84b4: StoreField: r1->field_b = r0
    //     0xab84b4: stur            w0, [x1, #0xb]
    // 0xab84b8: r0 = Instance_BorderSide
    //     0xab84b8: add             x0, PP, #0x71, lsl #12  ; [pp+0x71570] Obj!BorderSide@d62f11
    //     0xab84bc: ldr             x0, [x0, #0x570]
    // 0xab84c0: StoreField: r1->field_7 = r0
    //     0xab84c0: stur            w0, [x1, #7]
    // 0xab84c4: mov             x3, x1
    // 0xab84c8: b               #0xab8524
    // 0xab84cc: d0 = 30.000000
    //     0xab84cc: fmov            d0, #30.00000000
    // 0xab84d0: r0 = Radius()
    //     0xab84d0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xab84d4: d0 = 30.000000
    //     0xab84d4: fmov            d0, #30.00000000
    // 0xab84d8: stur            x0, [fp, #-0x38]
    // 0xab84dc: StoreField: r0->field_7 = d0
    //     0xab84dc: stur            d0, [x0, #7]
    // 0xab84e0: StoreField: r0->field_f = d0
    //     0xab84e0: stur            d0, [x0, #0xf]
    // 0xab84e4: r0 = BorderRadius()
    //     0xab84e4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xab84e8: mov             x1, x0
    // 0xab84ec: ldur            x0, [fp, #-0x38]
    // 0xab84f0: stur            x1, [fp, #-0x40]
    // 0xab84f4: StoreField: r1->field_7 = r0
    //     0xab84f4: stur            w0, [x1, #7]
    // 0xab84f8: StoreField: r1->field_b = r0
    //     0xab84f8: stur            w0, [x1, #0xb]
    // 0xab84fc: StoreField: r1->field_f = r0
    //     0xab84fc: stur            w0, [x1, #0xf]
    // 0xab8500: StoreField: r1->field_13 = r0
    //     0xab8500: stur            w0, [x1, #0x13]
    // 0xab8504: r0 = RoundedRectangleBorder()
    //     0xab8504: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xab8508: mov             x1, x0
    // 0xab850c: ldur            x0, [fp, #-0x40]
    // 0xab8510: StoreField: r1->field_b = r0
    //     0xab8510: stur            w0, [x1, #0xb]
    // 0xab8514: r0 = Instance_BorderSide
    //     0xab8514: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xab8518: ldr             x0, [x0, #0xe20]
    // 0xab851c: StoreField: r1->field_7 = r0
    //     0xab851c: stur            w0, [x1, #7]
    // 0xab8520: mov             x3, x1
    // 0xab8524: ldur            x0, [fp, #-8]
    // 0xab8528: ldur            x2, [fp, #-0x28]
    // 0xab852c: ldur            x1, [fp, #-0x30]
    // 0xab8530: r16 = <RoundedRectangleBorder>
    //     0xab8530: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xab8534: ldr             x16, [x16, #0xf78]
    // 0xab8538: stp             x3, x16, [SP]
    // 0xab853c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab853c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab8540: r0 = all()
    //     0xab8540: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xab8544: stur            x0, [fp, #-0x38]
    // 0xab8548: r0 = ButtonStyle()
    //     0xab8548: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xab854c: mov             x1, x0
    // 0xab8550: ldur            x0, [fp, #-0x30]
    // 0xab8554: stur            x1, [fp, #-0x40]
    // 0xab8558: StoreField: r1->field_b = r0
    //     0xab8558: stur            w0, [x1, #0xb]
    // 0xab855c: ldur            x0, [fp, #-0x28]
    // 0xab8560: StoreField: r1->field_23 = r0
    //     0xab8560: stur            w0, [x1, #0x23]
    // 0xab8564: ldur            x0, [fp, #-0x38]
    // 0xab8568: StoreField: r1->field_43 = r0
    //     0xab8568: stur            w0, [x1, #0x43]
    // 0xab856c: r0 = TextButtonThemeData()
    //     0xab856c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xab8570: mov             x1, x0
    // 0xab8574: ldur            x0, [fp, #-0x40]
    // 0xab8578: stur            x1, [fp, #-0x28]
    // 0xab857c: StoreField: r1->field_7 = r0
    //     0xab857c: stur            w0, [x1, #7]
    // 0xab8580: ldur            x2, [fp, #-8]
    // 0xab8584: LoadField: r0 = r2->field_b
    //     0xab8584: ldur            w0, [x2, #0xb]
    // 0xab8588: DecompressPointer r0
    //     0xab8588: add             x0, x0, HEAP, lsl #32
    // 0xab858c: cmp             w0, NULL
    // 0xab8590: b.eq            #0xab89dc
    // 0xab8594: LoadField: r3 = r0->field_f
    //     0xab8594: ldur            w3, [x0, #0xf]
    // 0xab8598: DecompressPointer r3
    //     0xab8598: add             x3, x3, HEAP, lsl #32
    // 0xab859c: LoadField: r4 = r0->field_b
    //     0xab859c: ldur            w4, [x0, #0xb]
    // 0xab85a0: DecompressPointer r4
    //     0xab85a0: add             x4, x4, HEAP, lsl #32
    // 0xab85a4: cmp             w4, NULL
    // 0xab85a8: b.ne            #0xab85b4
    // 0xab85ac: r0 = Null
    //     0xab85ac: mov             x0, NULL
    // 0xab85b0: b               #0xab85bc
    // 0xab85b4: LoadField: r0 = r4->field_7
    //     0xab85b4: ldur            w0, [x4, #7]
    // 0xab85b8: DecompressPointer r0
    //     0xab85b8: add             x0, x0, HEAP, lsl #32
    // 0xab85bc: r4 = LoadClassIdInstr(r3)
    //     0xab85bc: ldur            x4, [x3, #-1]
    //     0xab85c0: ubfx            x4, x4, #0xc, #0x14
    // 0xab85c4: stp             x0, x3, [SP]
    // 0xab85c8: mov             x0, x4
    // 0xab85cc: mov             lr, x0
    // 0xab85d0: ldr             lr, [x21, lr, lsl #3]
    // 0xab85d4: blr             lr
    // 0xab85d8: tbnz            w0, #4, #0xab871c
    // 0xab85dc: ldur            x0, [fp, #-8]
    // 0xab85e0: ldur            x2, [fp, #-0x18]
    // 0xab85e4: r1 = "remove offer"
    //     0xab85e4: add             x1, PP, #0x71, lsl #12  ; [pp+0x71508] "remove offer"
    //     0xab85e8: ldr             x1, [x1, #0x508]
    // 0xab85ec: r0 = capitalizeFirstWord()
    //     0xab85ec: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab85f0: ldur            x2, [fp, #-0x18]
    // 0xab85f4: stur            x0, [fp, #-0x30]
    // 0xab85f8: LoadField: r1 = r2->field_13
    //     0xab85f8: ldur            w1, [x2, #0x13]
    // 0xab85fc: DecompressPointer r1
    //     0xab85fc: add             x1, x1, HEAP, lsl #32
    // 0xab8600: r0 = of()
    //     0xab8600: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab8604: LoadField: r1 = r0->field_87
    //     0xab8604: ldur            w1, [x0, #0x87]
    // 0xab8608: DecompressPointer r1
    //     0xab8608: add             x1, x1, HEAP, lsl #32
    // 0xab860c: LoadField: r2 = r1->field_7
    //     0xab860c: ldur            w2, [x1, #7]
    // 0xab8610: DecompressPointer r2
    //     0xab8610: add             x2, x2, HEAP, lsl #32
    // 0xab8614: ldur            x1, [fp, #-8]
    // 0xab8618: stur            x2, [fp, #-0x38]
    // 0xab861c: LoadField: r0 = r1->field_b
    //     0xab861c: ldur            w0, [x1, #0xb]
    // 0xab8620: DecompressPointer r0
    //     0xab8620: add             x0, x0, HEAP, lsl #32
    // 0xab8624: cmp             w0, NULL
    // 0xab8628: b.eq            #0xab89e0
    // 0xab862c: LoadField: r3 = r0->field_f
    //     0xab862c: ldur            w3, [x0, #0xf]
    // 0xab8630: DecompressPointer r3
    //     0xab8630: add             x3, x3, HEAP, lsl #32
    // 0xab8634: LoadField: r4 = r0->field_b
    //     0xab8634: ldur            w4, [x0, #0xb]
    // 0xab8638: DecompressPointer r4
    //     0xab8638: add             x4, x4, HEAP, lsl #32
    // 0xab863c: cmp             w4, NULL
    // 0xab8640: b.ne            #0xab864c
    // 0xab8644: r0 = Null
    //     0xab8644: mov             x0, NULL
    // 0xab8648: b               #0xab8654
    // 0xab864c: LoadField: r0 = r4->field_7
    //     0xab864c: ldur            w0, [x4, #7]
    // 0xab8650: DecompressPointer r0
    //     0xab8650: add             x0, x0, HEAP, lsl #32
    // 0xab8654: r4 = LoadClassIdInstr(r3)
    //     0xab8654: ldur            x4, [x3, #-1]
    //     0xab8658: ubfx            x4, x4, #0xc, #0x14
    // 0xab865c: stp             x0, x3, [SP]
    // 0xab8660: mov             x0, x4
    // 0xab8664: mov             lr, x0
    // 0xab8668: ldr             lr, [x21, lr, lsl #3]
    // 0xab866c: blr             lr
    // 0xab8670: tbnz            w0, #4, #0xab8680
    // 0xab8674: r1 = Instance_Color
    //     0xab8674: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xab8678: ldr             x1, [x1, #0x858]
    // 0xab867c: b               #0xab86d8
    // 0xab8680: ldur            x0, [fp, #-8]
    // 0xab8684: LoadField: r1 = r0->field_b
    //     0xab8684: ldur            w1, [x0, #0xb]
    // 0xab8688: DecompressPointer r1
    //     0xab8688: add             x1, x1, HEAP, lsl #32
    // 0xab868c: cmp             w1, NULL
    // 0xab8690: b.eq            #0xab89e4
    // 0xab8694: LoadField: r0 = r1->field_b
    //     0xab8694: ldur            w0, [x1, #0xb]
    // 0xab8698: DecompressPointer r0
    //     0xab8698: add             x0, x0, HEAP, lsl #32
    // 0xab869c: cmp             w0, NULL
    // 0xab86a0: b.ne            #0xab86ac
    // 0xab86a4: r0 = Null
    //     0xab86a4: mov             x0, NULL
    // 0xab86a8: b               #0xab86b8
    // 0xab86ac: LoadField: r1 = r0->field_27
    //     0xab86ac: ldur            w1, [x0, #0x27]
    // 0xab86b0: DecompressPointer r1
    //     0xab86b0: add             x1, x1, HEAP, lsl #32
    // 0xab86b4: mov             x0, x1
    // 0xab86b8: cmp             w0, NULL
    // 0xab86bc: b.eq            #0xab86cc
    // 0xab86c0: tbnz            w0, #4, #0xab86cc
    // 0xab86c4: r0 = Instance_Color
    //     0xab86c4: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab86c8: b               #0xab86d4
    // 0xab86cc: r0 = Instance_MaterialColor
    //     0xab86cc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xab86d0: ldr             x0, [x0, #0xdc0]
    // 0xab86d4: mov             x1, x0
    // 0xab86d8: ldur            x0, [fp, #-0x30]
    // 0xab86dc: r16 = 14.000000
    //     0xab86dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab86e0: ldr             x16, [x16, #0x1d8]
    // 0xab86e4: stp             x1, x16, [SP]
    // 0xab86e8: ldur            x1, [fp, #-0x38]
    // 0xab86ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab86ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab86f0: ldr             x4, [x4, #0xaa0]
    // 0xab86f4: r0 = copyWith()
    //     0xab86f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab86f8: stur            x0, [fp, #-0x38]
    // 0xab86fc: r0 = Text()
    //     0xab86fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab8700: mov             x1, x0
    // 0xab8704: ldur            x0, [fp, #-0x30]
    // 0xab8708: StoreField: r1->field_b = r0
    //     0xab8708: stur            w0, [x1, #0xb]
    // 0xab870c: ldur            x0, [fp, #-0x38]
    // 0xab8710: StoreField: r1->field_13 = r0
    //     0xab8710: stur            w0, [x1, #0x13]
    // 0xab8714: mov             x5, x1
    // 0xab8718: b               #0xab87f8
    // 0xab871c: ldur            x0, [fp, #-8]
    // 0xab8720: ldur            x2, [fp, #-0x18]
    // 0xab8724: r1 = "apply now"
    //     0xab8724: add             x1, PP, #0x71, lsl #12  ; [pp+0x71510] "apply now"
    //     0xab8728: ldr             x1, [x1, #0x510]
    // 0xab872c: r0 = capitalizeFirstWord()
    //     0xab872c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xab8730: ldur            x2, [fp, #-0x18]
    // 0xab8734: stur            x0, [fp, #-0x30]
    // 0xab8738: LoadField: r1 = r2->field_13
    //     0xab8738: ldur            w1, [x2, #0x13]
    // 0xab873c: DecompressPointer r1
    //     0xab873c: add             x1, x1, HEAP, lsl #32
    // 0xab8740: r0 = of()
    //     0xab8740: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab8744: LoadField: r1 = r0->field_87
    //     0xab8744: ldur            w1, [x0, #0x87]
    // 0xab8748: DecompressPointer r1
    //     0xab8748: add             x1, x1, HEAP, lsl #32
    // 0xab874c: LoadField: r0 = r1->field_7
    //     0xab874c: ldur            w0, [x1, #7]
    // 0xab8750: DecompressPointer r0
    //     0xab8750: add             x0, x0, HEAP, lsl #32
    // 0xab8754: ldur            x1, [fp, #-8]
    // 0xab8758: stur            x0, [fp, #-0x38]
    // 0xab875c: LoadField: r2 = r1->field_b
    //     0xab875c: ldur            w2, [x1, #0xb]
    // 0xab8760: DecompressPointer r2
    //     0xab8760: add             x2, x2, HEAP, lsl #32
    // 0xab8764: cmp             w2, NULL
    // 0xab8768: b.eq            #0xab89e8
    // 0xab876c: LoadField: r1 = r2->field_b
    //     0xab876c: ldur            w1, [x2, #0xb]
    // 0xab8770: DecompressPointer r1
    //     0xab8770: add             x1, x1, HEAP, lsl #32
    // 0xab8774: cmp             w1, NULL
    // 0xab8778: b.ne            #0xab8784
    // 0xab877c: r1 = Null
    //     0xab877c: mov             x1, NULL
    // 0xab8780: b               #0xab8790
    // 0xab8784: LoadField: r2 = r1->field_27
    //     0xab8784: ldur            w2, [x1, #0x27]
    // 0xab8788: DecompressPointer r2
    //     0xab8788: add             x2, x2, HEAP, lsl #32
    // 0xab878c: mov             x1, x2
    // 0xab8790: cmp             w1, NULL
    // 0xab8794: b.eq            #0xab87a4
    // 0xab8798: tbnz            w1, #4, #0xab87a4
    // 0xab879c: r1 = Instance_Color
    //     0xab879c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xab87a0: b               #0xab87b8
    // 0xab87a4: r1 = Instance_MaterialColor
    //     0xab87a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xab87a8: ldr             x1, [x1, #0xdc0]
    // 0xab87ac: d0 = 0.600000
    //     0xab87ac: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xab87b0: r0 = withOpacity()
    //     0xab87b0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xab87b4: mov             x1, x0
    // 0xab87b8: ldur            x0, [fp, #-0x30]
    // 0xab87bc: r16 = 14.000000
    //     0xab87bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab87c0: ldr             x16, [x16, #0x1d8]
    // 0xab87c4: stp             x1, x16, [SP]
    // 0xab87c8: ldur            x1, [fp, #-0x38]
    // 0xab87cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab87cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab87d0: ldr             x4, [x4, #0xaa0]
    // 0xab87d4: r0 = copyWith()
    //     0xab87d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab87d8: stur            x0, [fp, #-8]
    // 0xab87dc: r0 = Text()
    //     0xab87dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab87e0: mov             x1, x0
    // 0xab87e4: ldur            x0, [fp, #-0x30]
    // 0xab87e8: StoreField: r1->field_b = r0
    //     0xab87e8: stur            w0, [x1, #0xb]
    // 0xab87ec: ldur            x0, [fp, #-8]
    // 0xab87f0: StoreField: r1->field_13 = r0
    //     0xab87f0: stur            w0, [x1, #0x13]
    // 0xab87f4: mov             x5, x1
    // 0xab87f8: ldur            x4, [fp, #-0x20]
    // 0xab87fc: ldur            x3, [fp, #-0x10]
    // 0xab8800: ldur            x0, [fp, #-0x28]
    // 0xab8804: ldur            x2, [fp, #-0x18]
    // 0xab8808: stur            x5, [fp, #-8]
    // 0xab880c: r1 = Function '<anonymous closure>':.
    //     0xab880c: add             x1, PP, #0x71, lsl #12  ; [pp+0x71578] AnonymousClosure: (0xab8a10), in [package:customer_app/app/presentation/views/cosmetic/bag/offers_bottom_sheet.dart] _OffersBottomSheetState::build (0xab789c)
    //     0xab8810: ldr             x1, [x1, #0x578]
    // 0xab8814: r0 = AllocateClosure()
    //     0xab8814: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab8818: stur            x0, [fp, #-0x18]
    // 0xab881c: r0 = TextButton()
    //     0xab881c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xab8820: mov             x1, x0
    // 0xab8824: ldur            x0, [fp, #-0x18]
    // 0xab8828: stur            x1, [fp, #-0x30]
    // 0xab882c: StoreField: r1->field_b = r0
    //     0xab882c: stur            w0, [x1, #0xb]
    // 0xab8830: r0 = false
    //     0xab8830: add             x0, NULL, #0x30  ; false
    // 0xab8834: StoreField: r1->field_27 = r0
    //     0xab8834: stur            w0, [x1, #0x27]
    // 0xab8838: r2 = true
    //     0xab8838: add             x2, NULL, #0x20  ; true
    // 0xab883c: StoreField: r1->field_2f = r2
    //     0xab883c: stur            w2, [x1, #0x2f]
    // 0xab8840: ldur            x2, [fp, #-8]
    // 0xab8844: StoreField: r1->field_37 = r2
    //     0xab8844: stur            w2, [x1, #0x37]
    // 0xab8848: r0 = TextButtonTheme()
    //     0xab8848: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xab884c: mov             x1, x0
    // 0xab8850: ldur            x0, [fp, #-0x28]
    // 0xab8854: stur            x1, [fp, #-8]
    // 0xab8858: StoreField: r1->field_f = r0
    //     0xab8858: stur            w0, [x1, #0xf]
    // 0xab885c: ldur            x0, [fp, #-0x30]
    // 0xab8860: StoreField: r1->field_b = r0
    //     0xab8860: stur            w0, [x1, #0xb]
    // 0xab8864: r0 = SizedBox()
    //     0xab8864: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xab8868: mov             x1, x0
    // 0xab886c: r0 = inf
    //     0xab886c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xab8870: ldr             x0, [x0, #0x9f8]
    // 0xab8874: stur            x1, [fp, #-0x18]
    // 0xab8878: StoreField: r1->field_f = r0
    //     0xab8878: stur            w0, [x1, #0xf]
    // 0xab887c: ldur            x0, [fp, #-8]
    // 0xab8880: StoreField: r1->field_b = r0
    //     0xab8880: stur            w0, [x1, #0xb]
    // 0xab8884: r0 = Align()
    //     0xab8884: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xab8888: mov             x1, x0
    // 0xab888c: r0 = Instance_Alignment
    //     0xab888c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xab8890: ldr             x0, [x0, #0xb10]
    // 0xab8894: stur            x1, [fp, #-8]
    // 0xab8898: StoreField: r1->field_f = r0
    //     0xab8898: stur            w0, [x1, #0xf]
    // 0xab889c: ldur            x0, [fp, #-0x18]
    // 0xab88a0: StoreField: r1->field_b = r0
    //     0xab88a0: stur            w0, [x1, #0xb]
    // 0xab88a4: r0 = Padding()
    //     0xab88a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab88a8: mov             x1, x0
    // 0xab88ac: r0 = Instance_EdgeInsets
    //     0xab88ac: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xab88b0: ldr             x0, [x0, #0xf30]
    // 0xab88b4: stur            x1, [fp, #-0x18]
    // 0xab88b8: StoreField: r1->field_f = r0
    //     0xab88b8: stur            w0, [x1, #0xf]
    // 0xab88bc: ldur            x0, [fp, #-8]
    // 0xab88c0: StoreField: r1->field_b = r0
    //     0xab88c0: stur            w0, [x1, #0xb]
    // 0xab88c4: r0 = Visibility()
    //     0xab88c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xab88c8: mov             x1, x0
    // 0xab88cc: ldur            x0, [fp, #-0x18]
    // 0xab88d0: StoreField: r1->field_b = r0
    //     0xab88d0: stur            w0, [x1, #0xb]
    // 0xab88d4: r0 = Instance_SizedBox
    //     0xab88d4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xab88d8: StoreField: r1->field_f = r0
    //     0xab88d8: stur            w0, [x1, #0xf]
    // 0xab88dc: ldur            x0, [fp, #-0x10]
    // 0xab88e0: StoreField: r1->field_13 = r0
    //     0xab88e0: stur            w0, [x1, #0x13]
    // 0xab88e4: r0 = false
    //     0xab88e4: add             x0, NULL, #0x30  ; false
    // 0xab88e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xab88e8: stur            w0, [x1, #0x17]
    // 0xab88ec: StoreField: r1->field_1b = r0
    //     0xab88ec: stur            w0, [x1, #0x1b]
    // 0xab88f0: StoreField: r1->field_1f = r0
    //     0xab88f0: stur            w0, [x1, #0x1f]
    // 0xab88f4: StoreField: r1->field_23 = r0
    //     0xab88f4: stur            w0, [x1, #0x23]
    // 0xab88f8: StoreField: r1->field_27 = r0
    //     0xab88f8: stur            w0, [x1, #0x27]
    // 0xab88fc: StoreField: r1->field_2b = r0
    //     0xab88fc: stur            w0, [x1, #0x2b]
    // 0xab8900: mov             x0, x1
    // 0xab8904: ldur            x1, [fp, #-0x20]
    // 0xab8908: ArrayStore: r1[8] = r0  ; List_4
    //     0xab8908: add             x25, x1, #0x2f
    //     0xab890c: str             w0, [x25]
    //     0xab8910: tbz             w0, #0, #0xab892c
    //     0xab8914: ldurb           w16, [x1, #-1]
    //     0xab8918: ldurb           w17, [x0, #-1]
    //     0xab891c: and             x16, x17, x16, lsr #2
    //     0xab8920: tst             x16, HEAP, lsr #32
    //     0xab8924: b.eq            #0xab892c
    //     0xab8928: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xab892c: r1 = <Widget>
    //     0xab892c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab8930: r0 = AllocateGrowableArray()
    //     0xab8930: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab8934: mov             x1, x0
    // 0xab8938: ldur            x0, [fp, #-0x20]
    // 0xab893c: stur            x1, [fp, #-8]
    // 0xab8940: StoreField: r1->field_f = r0
    //     0xab8940: stur            w0, [x1, #0xf]
    // 0xab8944: r0 = 18
    //     0xab8944: movz            x0, #0x12
    // 0xab8948: StoreField: r1->field_b = r0
    //     0xab8948: stur            w0, [x1, #0xb]
    // 0xab894c: r0 = Column()
    //     0xab894c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xab8950: r1 = Instance_Axis
    //     0xab8950: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xab8954: StoreField: r0->field_f = r1
    //     0xab8954: stur            w1, [x0, #0xf]
    // 0xab8958: r1 = Instance_MainAxisAlignment
    //     0xab8958: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab895c: ldr             x1, [x1, #0xa08]
    // 0xab8960: StoreField: r0->field_13 = r1
    //     0xab8960: stur            w1, [x0, #0x13]
    // 0xab8964: r1 = Instance_MainAxisSize
    //     0xab8964: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xab8968: ldr             x1, [x1, #0xdd0]
    // 0xab896c: ArrayStore: r0[0] = r1  ; List_4
    //     0xab896c: stur            w1, [x0, #0x17]
    // 0xab8970: r1 = Instance_CrossAxisAlignment
    //     0xab8970: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xab8974: ldr             x1, [x1, #0x890]
    // 0xab8978: StoreField: r0->field_1b = r1
    //     0xab8978: stur            w1, [x0, #0x1b]
    // 0xab897c: r1 = Instance_VerticalDirection
    //     0xab897c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab8980: ldr             x1, [x1, #0xa20]
    // 0xab8984: StoreField: r0->field_23 = r1
    //     0xab8984: stur            w1, [x0, #0x23]
    // 0xab8988: r1 = Instance_Clip
    //     0xab8988: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab898c: ldr             x1, [x1, #0x38]
    // 0xab8990: StoreField: r0->field_2b = r1
    //     0xab8990: stur            w1, [x0, #0x2b]
    // 0xab8994: StoreField: r0->field_2f = rZR
    //     0xab8994: stur            xzr, [x0, #0x2f]
    // 0xab8998: ldur            x1, [fp, #-8]
    // 0xab899c: StoreField: r0->field_b = r1
    //     0xab899c: stur            w1, [x0, #0xb]
    // 0xab89a0: LeaveFrame
    //     0xab89a0: mov             SP, fp
    //     0xab89a4: ldp             fp, lr, [SP], #0x10
    // 0xab89a8: ret
    //     0xab89a8: ret             
    // 0xab89ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab89ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab89b0: b               #0xab78c4
    // 0xab89b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab89e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab89e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xab8a10, size: 0x244
    // 0xab8a10: EnterFrame
    //     0xab8a10: stp             fp, lr, [SP, #-0x10]!
    //     0xab8a14: mov             fp, SP
    // 0xab8a18: AllocStack(0x28)
    //     0xab8a18: sub             SP, SP, #0x28
    // 0xab8a1c: SetupParameters()
    //     0xab8a1c: ldr             x0, [fp, #0x10]
    //     0xab8a20: ldur            w1, [x0, #0x17]
    //     0xab8a24: add             x1, x1, HEAP, lsl #32
    //     0xab8a28: stur            x1, [fp, #-8]
    // 0xab8a2c: CheckStackOverflow
    //     0xab8a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab8a30: cmp             SP, x16
    //     0xab8a34: b.ls            #0xab8c38
    // 0xab8a38: LoadField: r0 = r1->field_f
    //     0xab8a38: ldur            w0, [x1, #0xf]
    // 0xab8a3c: DecompressPointer r0
    //     0xab8a3c: add             x0, x0, HEAP, lsl #32
    // 0xab8a40: LoadField: r2 = r0->field_b
    //     0xab8a40: ldur            w2, [x0, #0xb]
    // 0xab8a44: DecompressPointer r2
    //     0xab8a44: add             x2, x2, HEAP, lsl #32
    // 0xab8a48: cmp             w2, NULL
    // 0xab8a4c: b.eq            #0xab8c40
    // 0xab8a50: LoadField: r0 = r2->field_b
    //     0xab8a50: ldur            w0, [x2, #0xb]
    // 0xab8a54: DecompressPointer r0
    //     0xab8a54: add             x0, x0, HEAP, lsl #32
    // 0xab8a58: cmp             w0, NULL
    // 0xab8a5c: b.ne            #0xab8a68
    // 0xab8a60: r3 = Null
    //     0xab8a60: mov             x3, NULL
    // 0xab8a64: b               #0xab8a70
    // 0xab8a68: LoadField: r3 = r0->field_27
    //     0xab8a68: ldur            w3, [x0, #0x27]
    // 0xab8a6c: DecompressPointer r3
    //     0xab8a6c: add             x3, x3, HEAP, lsl #32
    // 0xab8a70: cmp             w3, NULL
    // 0xab8a74: b.eq            #0xab8c28
    // 0xab8a78: tbnz            w3, #4, #0xab8c28
    // 0xab8a7c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xab8a7c: ldur            w3, [x2, #0x17]
    // 0xab8a80: DecompressPointer r3
    //     0xab8a80: add             x3, x3, HEAP, lsl #32
    // 0xab8a84: cmp             w3, NULL
    // 0xab8a88: b.eq            #0xab8b70
    // 0xab8a8c: LoadField: r3 = r2->field_f
    //     0xab8a8c: ldur            w3, [x2, #0xf]
    // 0xab8a90: DecompressPointer r3
    //     0xab8a90: add             x3, x3, HEAP, lsl #32
    // 0xab8a94: cmp             w0, NULL
    // 0xab8a98: b.ne            #0xab8aa4
    // 0xab8a9c: r0 = Null
    //     0xab8a9c: mov             x0, NULL
    // 0xab8aa0: b               #0xab8ab0
    // 0xab8aa4: LoadField: r2 = r0->field_7
    //     0xab8aa4: ldur            w2, [x0, #7]
    // 0xab8aa8: DecompressPointer r2
    //     0xab8aa8: add             x2, x2, HEAP, lsl #32
    // 0xab8aac: mov             x0, x2
    // 0xab8ab0: r2 = LoadClassIdInstr(r3)
    //     0xab8ab0: ldur            x2, [x3, #-1]
    //     0xab8ab4: ubfx            x2, x2, #0xc, #0x14
    // 0xab8ab8: stp             x0, x3, [SP]
    // 0xab8abc: mov             x0, x2
    // 0xab8ac0: mov             lr, x0
    // 0xab8ac4: ldr             lr, [x21, lr, lsl #3]
    // 0xab8ac8: blr             lr
    // 0xab8acc: tbz             w0, #4, #0xab8b70
    // 0xab8ad0: ldur            x0, [fp, #-8]
    // 0xab8ad4: LoadField: r1 = r0->field_f
    //     0xab8ad4: ldur            w1, [x0, #0xf]
    // 0xab8ad8: DecompressPointer r1
    //     0xab8ad8: add             x1, x1, HEAP, lsl #32
    // 0xab8adc: LoadField: r2 = r1->field_b
    //     0xab8adc: ldur            w2, [x1, #0xb]
    // 0xab8ae0: DecompressPointer r2
    //     0xab8ae0: add             x2, x2, HEAP, lsl #32
    // 0xab8ae4: cmp             w2, NULL
    // 0xab8ae8: b.eq            #0xab8c44
    // 0xab8aec: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xab8aec: ldur            w1, [x2, #0x17]
    // 0xab8af0: DecompressPointer r1
    //     0xab8af0: add             x1, x1, HEAP, lsl #32
    // 0xab8af4: cmp             w1, NULL
    // 0xab8af8: b.eq            #0xab8c48
    // 0xab8afc: LoadField: r3 = r2->field_b
    //     0xab8afc: ldur            w3, [x2, #0xb]
    // 0xab8b00: DecompressPointer r3
    //     0xab8b00: add             x3, x3, HEAP, lsl #32
    // 0xab8b04: cmp             w3, NULL
    // 0xab8b08: b.ne            #0xab8b14
    // 0xab8b0c: r2 = Null
    //     0xab8b0c: mov             x2, NULL
    // 0xab8b10: b               #0xab8b1c
    // 0xab8b14: LoadField: r2 = r3->field_7
    //     0xab8b14: ldur            w2, [x3, #7]
    // 0xab8b18: DecompressPointer r2
    //     0xab8b18: add             x2, x2, HEAP, lsl #32
    // 0xab8b1c: cmp             w2, NULL
    // 0xab8b20: b.ne            #0xab8b28
    // 0xab8b24: r2 = ""
    //     0xab8b24: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab8b28: cmp             w3, NULL
    // 0xab8b2c: b.ne            #0xab8b38
    // 0xab8b30: r3 = Null
    //     0xab8b30: mov             x3, NULL
    // 0xab8b34: b               #0xab8b44
    // 0xab8b38: LoadField: r4 = r3->field_b
    //     0xab8b38: ldur            w4, [x3, #0xb]
    // 0xab8b3c: DecompressPointer r4
    //     0xab8b3c: add             x4, x4, HEAP, lsl #32
    // 0xab8b40: mov             x3, x4
    // 0xab8b44: stp             x2, x1, [SP, #0x10]
    // 0xab8b48: r16 = "apply"
    //     0xab8b48: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xab8b4c: ldr             x16, [x16, #0xfe8]
    // 0xab8b50: stp             x16, x3, [SP]
    // 0xab8b54: r4 = 0
    //     0xab8b54: movz            x4, #0
    // 0xab8b58: ldr             x0, [SP, #0x18]
    // 0xab8b5c: r16 = UnlinkedCall_0x613b5c
    //     0xab8b5c: add             x16, PP, #0x71, lsl #12  ; [pp+0x71580] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xab8b60: add             x16, x16, #0x580
    // 0xab8b64: ldp             x5, lr, [x16]
    // 0xab8b68: blr             lr
    // 0xab8b6c: b               #0xab8c0c
    // 0xab8b70: ldur            x0, [fp, #-8]
    // 0xab8b74: LoadField: r1 = r0->field_f
    //     0xab8b74: ldur            w1, [x0, #0xf]
    // 0xab8b78: DecompressPointer r1
    //     0xab8b78: add             x1, x1, HEAP, lsl #32
    // 0xab8b7c: LoadField: r2 = r1->field_b
    //     0xab8b7c: ldur            w2, [x1, #0xb]
    // 0xab8b80: DecompressPointer r2
    //     0xab8b80: add             x2, x2, HEAP, lsl #32
    // 0xab8b84: cmp             w2, NULL
    // 0xab8b88: b.eq            #0xab8c4c
    // 0xab8b8c: LoadField: r1 = r2->field_1b
    //     0xab8b8c: ldur            w1, [x2, #0x1b]
    // 0xab8b90: DecompressPointer r1
    //     0xab8b90: add             x1, x1, HEAP, lsl #32
    // 0xab8b94: cmp             w1, NULL
    // 0xab8b98: b.eq            #0xab8c50
    // 0xab8b9c: LoadField: r3 = r2->field_b
    //     0xab8b9c: ldur            w3, [x2, #0xb]
    // 0xab8ba0: DecompressPointer r3
    //     0xab8ba0: add             x3, x3, HEAP, lsl #32
    // 0xab8ba4: cmp             w3, NULL
    // 0xab8ba8: b.ne            #0xab8bb4
    // 0xab8bac: r2 = Null
    //     0xab8bac: mov             x2, NULL
    // 0xab8bb0: b               #0xab8bbc
    // 0xab8bb4: LoadField: r2 = r3->field_7
    //     0xab8bb4: ldur            w2, [x3, #7]
    // 0xab8bb8: DecompressPointer r2
    //     0xab8bb8: add             x2, x2, HEAP, lsl #32
    // 0xab8bbc: cmp             w2, NULL
    // 0xab8bc0: b.ne            #0xab8bc8
    // 0xab8bc4: r2 = ""
    //     0xab8bc4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab8bc8: cmp             w3, NULL
    // 0xab8bcc: b.ne            #0xab8bd8
    // 0xab8bd0: r3 = Null
    //     0xab8bd0: mov             x3, NULL
    // 0xab8bd4: b               #0xab8be4
    // 0xab8bd8: LoadField: r4 = r3->field_b
    //     0xab8bd8: ldur            w4, [x3, #0xb]
    // 0xab8bdc: DecompressPointer r4
    //     0xab8bdc: add             x4, x4, HEAP, lsl #32
    // 0xab8be0: mov             x3, x4
    // 0xab8be4: stp             x2, x1, [SP, #0x10]
    // 0xab8be8: r16 = "remove"
    //     0xab8be8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xab8bec: ldr             x16, [x16, #0xff0]
    // 0xab8bf0: stp             x16, x3, [SP]
    // 0xab8bf4: r4 = 0
    //     0xab8bf4: movz            x4, #0
    // 0xab8bf8: ldr             x0, [SP, #0x18]
    // 0xab8bfc: r16 = UnlinkedCall_0x613b5c
    //     0xab8bfc: add             x16, PP, #0x71, lsl #12  ; [pp+0x71590] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xab8c00: add             x16, x16, #0x590
    // 0xab8c04: ldp             x5, lr, [x16]
    // 0xab8c08: blr             lr
    // 0xab8c0c: ldur            x0, [fp, #-8]
    // 0xab8c10: LoadField: r1 = r0->field_13
    //     0xab8c10: ldur            w1, [x0, #0x13]
    // 0xab8c14: DecompressPointer r1
    //     0xab8c14: add             x1, x1, HEAP, lsl #32
    // 0xab8c18: r16 = <Object?>
    //     0xab8c18: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xab8c1c: stp             x1, x16, [SP]
    // 0xab8c20: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xab8c20: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xab8c24: r0 = pop()
    //     0xab8c24: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xab8c28: r0 = Null
    //     0xab8c28: mov             x0, NULL
    // 0xab8c2c: LeaveFrame
    //     0xab8c2c: mov             SP, fp
    //     0xab8c30: ldp             fp, lr, [SP], #0x10
    // 0xab8c34: ret
    //     0xab8c34: ret             
    // 0xab8c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab8c38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab8c3c: b               #0xab8a38
    // 0xab8c40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab8c40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab8c44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab8c44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab8c48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab8c48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab8c4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab8c4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab8c50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab8c50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xab8c54, size: 0x210
    // 0xab8c54: EnterFrame
    //     0xab8c54: stp             fp, lr, [SP, #-0x10]!
    //     0xab8c58: mov             fp, SP
    // 0xab8c5c: AllocStack(0x28)
    //     0xab8c5c: sub             SP, SP, #0x28
    // 0xab8c60: SetupParameters()
    //     0xab8c60: ldr             x0, [fp, #0x20]
    //     0xab8c64: ldur            w1, [x0, #0x17]
    //     0xab8c68: add             x1, x1, HEAP, lsl #32
    // 0xab8c6c: CheckStackOverflow
    //     0xab8c6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab8c70: cmp             SP, x16
    //     0xab8c74: b.ls            #0xab8e54
    // 0xab8c78: LoadField: r0 = r1->field_f
    //     0xab8c78: ldur            w0, [x1, #0xf]
    // 0xab8c7c: DecompressPointer r0
    //     0xab8c7c: add             x0, x0, HEAP, lsl #32
    // 0xab8c80: LoadField: r1 = r0->field_b
    //     0xab8c80: ldur            w1, [x0, #0xb]
    // 0xab8c84: DecompressPointer r1
    //     0xab8c84: add             x1, x1, HEAP, lsl #32
    // 0xab8c88: cmp             w1, NULL
    // 0xab8c8c: b.eq            #0xab8e5c
    // 0xab8c90: LoadField: r0 = r1->field_b
    //     0xab8c90: ldur            w0, [x1, #0xb]
    // 0xab8c94: DecompressPointer r0
    //     0xab8c94: add             x0, x0, HEAP, lsl #32
    // 0xab8c98: cmp             w0, NULL
    // 0xab8c9c: b.ne            #0xab8ca8
    // 0xab8ca0: r0 = Null
    //     0xab8ca0: mov             x0, NULL
    // 0xab8ca4: b               #0xab8cfc
    // 0xab8ca8: LoadField: r2 = r0->field_1f
    //     0xab8ca8: ldur            w2, [x0, #0x1f]
    // 0xab8cac: DecompressPointer r2
    //     0xab8cac: add             x2, x2, HEAP, lsl #32
    // 0xab8cb0: cmp             w2, NULL
    // 0xab8cb4: b.ne            #0xab8cc0
    // 0xab8cb8: r0 = Null
    //     0xab8cb8: mov             x0, NULL
    // 0xab8cbc: b               #0xab8cfc
    // 0xab8cc0: ldr             x0, [fp, #0x10]
    // 0xab8cc4: LoadField: r1 = r2->field_b
    //     0xab8cc4: ldur            w1, [x2, #0xb]
    // 0xab8cc8: r3 = LoadInt32Instr(r0)
    //     0xab8cc8: sbfx            x3, x0, #1, #0x1f
    //     0xab8ccc: tbz             w0, #0, #0xab8cd4
    //     0xab8cd0: ldur            x3, [x0, #7]
    // 0xab8cd4: r0 = LoadInt32Instr(r1)
    //     0xab8cd4: sbfx            x0, x1, #1, #0x1f
    // 0xab8cd8: mov             x1, x3
    // 0xab8cdc: cmp             x1, x0
    // 0xab8ce0: b.hs            #0xab8e60
    // 0xab8ce4: LoadField: r0 = r2->field_f
    //     0xab8ce4: ldur            w0, [x2, #0xf]
    // 0xab8ce8: DecompressPointer r0
    //     0xab8ce8: add             x0, x0, HEAP, lsl #32
    // 0xab8cec: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xab8cec: add             x16, x0, x3, lsl #2
    //     0xab8cf0: ldur            w1, [x16, #0xf]
    // 0xab8cf4: DecompressPointer r1
    //     0xab8cf4: add             x1, x1, HEAP, lsl #32
    // 0xab8cf8: mov             x0, x1
    // 0xab8cfc: cmp             w0, NULL
    // 0xab8d00: b.ne            #0xab8d08
    // 0xab8d04: r0 = ""
    //     0xab8d04: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xab8d08: ldr             x1, [fp, #0x18]
    // 0xab8d0c: stur            x0, [fp, #-8]
    // 0xab8d10: r0 = of()
    //     0xab8d10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab8d14: LoadField: r1 = r0->field_87
    //     0xab8d14: ldur            w1, [x0, #0x87]
    // 0xab8d18: DecompressPointer r1
    //     0xab8d18: add             x1, x1, HEAP, lsl #32
    // 0xab8d1c: LoadField: r0 = r1->field_2b
    //     0xab8d1c: ldur            w0, [x1, #0x2b]
    // 0xab8d20: DecompressPointer r0
    //     0xab8d20: add             x0, x0, HEAP, lsl #32
    // 0xab8d24: stur            x0, [fp, #-0x10]
    // 0xab8d28: r1 = Instance_Color
    //     0xab8d28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab8d2c: d0 = 0.400000
    //     0xab8d2c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xab8d30: r0 = withOpacity()
    //     0xab8d30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xab8d34: r16 = 12.000000
    //     0xab8d34: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xab8d38: ldr             x16, [x16, #0x9e8]
    // 0xab8d3c: stp             x0, x16, [SP]
    // 0xab8d40: ldur            x1, [fp, #-0x10]
    // 0xab8d44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab8d44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab8d48: ldr             x4, [x4, #0xaa0]
    // 0xab8d4c: r0 = copyWith()
    //     0xab8d4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab8d50: stur            x0, [fp, #-0x10]
    // 0xab8d54: r0 = Text()
    //     0xab8d54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab8d58: mov             x1, x0
    // 0xab8d5c: ldur            x0, [fp, #-8]
    // 0xab8d60: stur            x1, [fp, #-0x18]
    // 0xab8d64: StoreField: r1->field_b = r0
    //     0xab8d64: stur            w0, [x1, #0xb]
    // 0xab8d68: ldur            x0, [fp, #-0x10]
    // 0xab8d6c: StoreField: r1->field_13 = r0
    //     0xab8d6c: stur            w0, [x1, #0x13]
    // 0xab8d70: r0 = Padding()
    //     0xab8d70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab8d74: mov             x2, x0
    // 0xab8d78: r0 = Instance_EdgeInsets
    //     0xab8d78: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xab8d7c: ldr             x0, [x0, #0xf70]
    // 0xab8d80: stur            x2, [fp, #-8]
    // 0xab8d84: StoreField: r2->field_f = r0
    //     0xab8d84: stur            w0, [x2, #0xf]
    // 0xab8d88: ldur            x0, [fp, #-0x18]
    // 0xab8d8c: StoreField: r2->field_b = r0
    //     0xab8d8c: stur            w0, [x2, #0xb]
    // 0xab8d90: r1 = <FlexParentData>
    //     0xab8d90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xab8d94: ldr             x1, [x1, #0xe00]
    // 0xab8d98: r0 = Expanded()
    //     0xab8d98: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xab8d9c: stur            x0, [fp, #-0x10]
    // 0xab8da0: StoreField: r0->field_13 = rZR
    //     0xab8da0: stur            xzr, [x0, #0x13]
    // 0xab8da4: r1 = Instance_FlexFit
    //     0xab8da4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xab8da8: ldr             x1, [x1, #0xe08]
    // 0xab8dac: StoreField: r0->field_1b = r1
    //     0xab8dac: stur            w1, [x0, #0x1b]
    // 0xab8db0: ldur            x1, [fp, #-8]
    // 0xab8db4: StoreField: r0->field_b = r1
    //     0xab8db4: stur            w1, [x0, #0xb]
    // 0xab8db8: r1 = Null
    //     0xab8db8: mov             x1, NULL
    // 0xab8dbc: r2 = 2
    //     0xab8dbc: movz            x2, #0x2
    // 0xab8dc0: r0 = AllocateArray()
    //     0xab8dc0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab8dc4: mov             x2, x0
    // 0xab8dc8: ldur            x0, [fp, #-0x10]
    // 0xab8dcc: stur            x2, [fp, #-8]
    // 0xab8dd0: StoreField: r2->field_f = r0
    //     0xab8dd0: stur            w0, [x2, #0xf]
    // 0xab8dd4: r1 = <Widget>
    //     0xab8dd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab8dd8: r0 = AllocateGrowableArray()
    //     0xab8dd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab8ddc: mov             x1, x0
    // 0xab8de0: ldur            x0, [fp, #-8]
    // 0xab8de4: stur            x1, [fp, #-0x10]
    // 0xab8de8: StoreField: r1->field_f = r0
    //     0xab8de8: stur            w0, [x1, #0xf]
    // 0xab8dec: r0 = 2
    //     0xab8dec: movz            x0, #0x2
    // 0xab8df0: StoreField: r1->field_b = r0
    //     0xab8df0: stur            w0, [x1, #0xb]
    // 0xab8df4: r0 = Row()
    //     0xab8df4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab8df8: r1 = Instance_Axis
    //     0xab8df8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab8dfc: StoreField: r0->field_f = r1
    //     0xab8dfc: stur            w1, [x0, #0xf]
    // 0xab8e00: r1 = Instance_MainAxisAlignment
    //     0xab8e00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab8e04: ldr             x1, [x1, #0xa08]
    // 0xab8e08: StoreField: r0->field_13 = r1
    //     0xab8e08: stur            w1, [x0, #0x13]
    // 0xab8e0c: r1 = Instance_MainAxisSize
    //     0xab8e0c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab8e10: ldr             x1, [x1, #0xa10]
    // 0xab8e14: ArrayStore: r0[0] = r1  ; List_4
    //     0xab8e14: stur            w1, [x0, #0x17]
    // 0xab8e18: r1 = Instance_CrossAxisAlignment
    //     0xab8e18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab8e1c: ldr             x1, [x1, #0xa18]
    // 0xab8e20: StoreField: r0->field_1b = r1
    //     0xab8e20: stur            w1, [x0, #0x1b]
    // 0xab8e24: r1 = Instance_VerticalDirection
    //     0xab8e24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab8e28: ldr             x1, [x1, #0xa20]
    // 0xab8e2c: StoreField: r0->field_23 = r1
    //     0xab8e2c: stur            w1, [x0, #0x23]
    // 0xab8e30: r1 = Instance_Clip
    //     0xab8e30: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab8e34: ldr             x1, [x1, #0x38]
    // 0xab8e38: StoreField: r0->field_2b = r1
    //     0xab8e38: stur            w1, [x0, #0x2b]
    // 0xab8e3c: StoreField: r0->field_2f = rZR
    //     0xab8e3c: stur            xzr, [x0, #0x2f]
    // 0xab8e40: ldur            x1, [fp, #-0x10]
    // 0xab8e44: StoreField: r0->field_b = r1
    //     0xab8e44: stur            w1, [x0, #0xb]
    // 0xab8e48: LeaveFrame
    //     0xab8e48: mov             SP, fp
    //     0xab8e4c: ldp             fp, lr, [SP], #0x10
    // 0xab8e50: ret
    //     0xab8e50: ret             
    // 0xab8e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab8e54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab8e58: b               #0xab8c78
    // 0xab8e5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab8e5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab8e60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab8e60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4199, size: 0x24, field offset: 0xc
//   const constructor, 
class OffersBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7cfc8, size: 0x24
    // 0xc7cfc8: EnterFrame
    //     0xc7cfc8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7cfcc: mov             fp, SP
    // 0xc7cfd0: mov             x0, x1
    // 0xc7cfd4: r1 = <OffersBottomSheet>
    //     0xc7cfd4: add             x1, PP, #0x6e, lsl #12  ; [pp+0x6ec98] TypeArguments: <OffersBottomSheet>
    //     0xc7cfd8: ldr             x1, [x1, #0xc98]
    // 0xc7cfdc: r0 = _OffersBottomSheetState()
    //     0xc7cfdc: bl              #0xc7cfec  ; Allocate_OffersBottomSheetStateStub -> _OffersBottomSheetState (size=0x14)
    // 0xc7cfe0: LeaveFrame
    //     0xc7cfe0: mov             SP, fp
    //     0xc7cfe4: ldp             fp, lr, [SP], #0x10
    // 0xc7cfe8: ret
    //     0xc7cfe8: ret             
  }
}
