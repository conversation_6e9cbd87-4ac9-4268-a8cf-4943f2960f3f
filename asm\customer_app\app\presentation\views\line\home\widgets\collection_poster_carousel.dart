// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/collection_poster_carousel.dart

// class id: 1049522, size: 0x8
class :: {
}

// class id: 3249, size: 0x20, field offset: 0x14
class _CollectionPosterCarouselState extends State<dynamic> {

  late PageController _pageLineController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xbdf03c, size: 0x700
    // 0xbdf03c: EnterFrame
    //     0xbdf03c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdf040: mov             fp, SP
    // 0xbdf044: AllocStack(0x68)
    //     0xbdf044: sub             SP, SP, #0x68
    // 0xbdf048: SetupParameters(_CollectionPosterCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbdf048: mov             x0, x1
    //     0xbdf04c: stur            x1, [fp, #-8]
    //     0xbdf050: mov             x1, x2
    //     0xbdf054: stur            x2, [fp, #-0x10]
    // 0xbdf058: CheckStackOverflow
    //     0xbdf058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdf05c: cmp             SP, x16
    //     0xbdf060: b.ls            #0xbdf714
    // 0xbdf064: r1 = 1
    //     0xbdf064: movz            x1, #0x1
    // 0xbdf068: r0 = AllocateContext()
    //     0xbdf068: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdf06c: mov             x3, x0
    // 0xbdf070: ldur            x0, [fp, #-8]
    // 0xbdf074: stur            x3, [fp, #-0x20]
    // 0xbdf078: StoreField: r3->field_f = r0
    //     0xbdf078: stur            w0, [x3, #0xf]
    // 0xbdf07c: LoadField: r1 = r0->field_b
    //     0xbdf07c: ldur            w1, [x0, #0xb]
    // 0xbdf080: DecompressPointer r1
    //     0xbdf080: add             x1, x1, HEAP, lsl #32
    // 0xbdf084: cmp             w1, NULL
    // 0xbdf088: b.eq            #0xbdf71c
    // 0xbdf08c: LoadField: r2 = r1->field_13
    //     0xbdf08c: ldur            w2, [x1, #0x13]
    // 0xbdf090: DecompressPointer r2
    //     0xbdf090: add             x2, x2, HEAP, lsl #32
    // 0xbdf094: LoadField: r1 = r2->field_7
    //     0xbdf094: ldur            w1, [x2, #7]
    // 0xbdf098: DecompressPointer r1
    //     0xbdf098: add             x1, x1, HEAP, lsl #32
    // 0xbdf09c: cmp             w1, NULL
    // 0xbdf0a0: b.ne            #0xbdf0ac
    // 0xbdf0a4: r1 = Instance_TitleAlignment
    //     0xbdf0a4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbdf0a8: ldr             x1, [x1, #0x518]
    // 0xbdf0ac: r16 = Instance_TitleAlignment
    //     0xbdf0ac: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbdf0b0: ldr             x16, [x16, #0x520]
    // 0xbdf0b4: cmp             w1, w16
    // 0xbdf0b8: b.ne            #0xbdf0c8
    // 0xbdf0bc: r4 = Instance_CrossAxisAlignment
    //     0xbdf0bc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xbdf0c0: ldr             x4, [x4, #0xc68]
    // 0xbdf0c4: b               #0xbdf0ec
    // 0xbdf0c8: r16 = Instance_TitleAlignment
    //     0xbdf0c8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbdf0cc: ldr             x16, [x16, #0x518]
    // 0xbdf0d0: cmp             w1, w16
    // 0xbdf0d4: b.ne            #0xbdf0e4
    // 0xbdf0d8: r4 = Instance_CrossAxisAlignment
    //     0xbdf0d8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbdf0dc: ldr             x4, [x4, #0x890]
    // 0xbdf0e0: b               #0xbdf0ec
    // 0xbdf0e4: r4 = Instance_CrossAxisAlignment
    //     0xbdf0e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdf0e8: ldr             x4, [x4, #0xa18]
    // 0xbdf0ec: stur            x4, [fp, #-0x18]
    // 0xbdf0f0: r1 = <Widget>
    //     0xbdf0f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdf0f4: r2 = 0
    //     0xbdf0f4: movz            x2, #0
    // 0xbdf0f8: r0 = _GrowableList()
    //     0xbdf0f8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbdf0fc: mov             x2, x0
    // 0xbdf100: ldur            x1, [fp, #-8]
    // 0xbdf104: stur            x2, [fp, #-0x28]
    // 0xbdf108: LoadField: r0 = r1->field_b
    //     0xbdf108: ldur            w0, [x1, #0xb]
    // 0xbdf10c: DecompressPointer r0
    //     0xbdf10c: add             x0, x0, HEAP, lsl #32
    // 0xbdf110: cmp             w0, NULL
    // 0xbdf114: b.eq            #0xbdf720
    // 0xbdf118: LoadField: r3 = r0->field_f
    //     0xbdf118: ldur            w3, [x0, #0xf]
    // 0xbdf11c: DecompressPointer r3
    //     0xbdf11c: add             x3, x3, HEAP, lsl #32
    // 0xbdf120: LoadField: r0 = r3->field_7
    //     0xbdf120: ldur            w0, [x3, #7]
    // 0xbdf124: cbz             w0, #0xbdf358
    // 0xbdf128: r0 = LoadClassIdInstr(r3)
    //     0xbdf128: ldur            x0, [x3, #-1]
    //     0xbdf12c: ubfx            x0, x0, #0xc, #0x14
    // 0xbdf130: str             x3, [SP]
    // 0xbdf134: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbdf134: sub             lr, x0, #1, lsl #12
    //     0xbdf138: ldr             lr, [x21, lr, lsl #3]
    //     0xbdf13c: blr             lr
    // 0xbdf140: mov             x2, x0
    // 0xbdf144: ldur            x0, [fp, #-8]
    // 0xbdf148: stur            x2, [fp, #-0x38]
    // 0xbdf14c: LoadField: r1 = r0->field_b
    //     0xbdf14c: ldur            w1, [x0, #0xb]
    // 0xbdf150: DecompressPointer r1
    //     0xbdf150: add             x1, x1, HEAP, lsl #32
    // 0xbdf154: cmp             w1, NULL
    // 0xbdf158: b.eq            #0xbdf724
    // 0xbdf15c: LoadField: r3 = r1->field_13
    //     0xbdf15c: ldur            w3, [x1, #0x13]
    // 0xbdf160: DecompressPointer r3
    //     0xbdf160: add             x3, x3, HEAP, lsl #32
    // 0xbdf164: LoadField: r1 = r3->field_7
    //     0xbdf164: ldur            w1, [x3, #7]
    // 0xbdf168: DecompressPointer r1
    //     0xbdf168: add             x1, x1, HEAP, lsl #32
    // 0xbdf16c: cmp             w1, NULL
    // 0xbdf170: b.ne            #0xbdf17c
    // 0xbdf174: r1 = Instance_TitleAlignment
    //     0xbdf174: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbdf178: ldr             x1, [x1, #0x518]
    // 0xbdf17c: r16 = Instance_TitleAlignment
    //     0xbdf17c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbdf180: ldr             x16, [x16, #0x520]
    // 0xbdf184: cmp             w1, w16
    // 0xbdf188: b.ne            #0xbdf194
    // 0xbdf18c: r4 = Instance_TextAlign
    //     0xbdf18c: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xbdf190: b               #0xbdf1b0
    // 0xbdf194: r16 = Instance_TitleAlignment
    //     0xbdf194: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbdf198: ldr             x16, [x16, #0x518]
    // 0xbdf19c: cmp             w1, w16
    // 0xbdf1a0: b.ne            #0xbdf1ac
    // 0xbdf1a4: r4 = Instance_TextAlign
    //     0xbdf1a4: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbdf1a8: b               #0xbdf1b0
    // 0xbdf1ac: r4 = Instance_TextAlign
    //     0xbdf1ac: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbdf1b0: ldur            x3, [fp, #-0x28]
    // 0xbdf1b4: ldur            x1, [fp, #-0x10]
    // 0xbdf1b8: stur            x4, [fp, #-0x30]
    // 0xbdf1bc: r0 = of()
    //     0xbdf1bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdf1c0: LoadField: r1 = r0->field_87
    //     0xbdf1c0: ldur            w1, [x0, #0x87]
    // 0xbdf1c4: DecompressPointer r1
    //     0xbdf1c4: add             x1, x1, HEAP, lsl #32
    // 0xbdf1c8: LoadField: r0 = r1->field_7
    //     0xbdf1c8: ldur            w0, [x1, #7]
    // 0xbdf1cc: DecompressPointer r0
    //     0xbdf1cc: add             x0, x0, HEAP, lsl #32
    // 0xbdf1d0: r16 = Instance_Color
    //     0xbdf1d0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdf1d4: r30 = 21.000000
    //     0xbdf1d4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbdf1d8: ldr             lr, [lr, #0x9b0]
    // 0xbdf1dc: stp             lr, x16, [SP]
    // 0xbdf1e0: mov             x1, x0
    // 0xbdf1e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbdf1e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbdf1e8: ldr             x4, [x4, #0x9b8]
    // 0xbdf1ec: r0 = copyWith()
    //     0xbdf1ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdf1f0: stur            x0, [fp, #-0x40]
    // 0xbdf1f4: r0 = Text()
    //     0xbdf1f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdf1f8: mov             x1, x0
    // 0xbdf1fc: ldur            x0, [fp, #-0x38]
    // 0xbdf200: stur            x1, [fp, #-0x48]
    // 0xbdf204: StoreField: r1->field_b = r0
    //     0xbdf204: stur            w0, [x1, #0xb]
    // 0xbdf208: ldur            x0, [fp, #-0x40]
    // 0xbdf20c: StoreField: r1->field_13 = r0
    //     0xbdf20c: stur            w0, [x1, #0x13]
    // 0xbdf210: ldur            x0, [fp, #-0x30]
    // 0xbdf214: StoreField: r1->field_1b = r0
    //     0xbdf214: stur            w0, [x1, #0x1b]
    // 0xbdf218: r0 = Padding()
    //     0xbdf218: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdf21c: mov             x3, x0
    // 0xbdf220: r0 = Instance_EdgeInsets
    //     0xbdf220: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbdf224: ldr             x0, [x0, #0x668]
    // 0xbdf228: stur            x3, [fp, #-0x30]
    // 0xbdf22c: StoreField: r3->field_f = r0
    //     0xbdf22c: stur            w0, [x3, #0xf]
    // 0xbdf230: ldur            x0, [fp, #-0x48]
    // 0xbdf234: StoreField: r3->field_b = r0
    //     0xbdf234: stur            w0, [x3, #0xb]
    // 0xbdf238: r1 = Null
    //     0xbdf238: mov             x1, NULL
    // 0xbdf23c: r2 = 4
    //     0xbdf23c: movz            x2, #0x4
    // 0xbdf240: r0 = AllocateArray()
    //     0xbdf240: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdf244: mov             x2, x0
    // 0xbdf248: ldur            x0, [fp, #-0x30]
    // 0xbdf24c: stur            x2, [fp, #-0x38]
    // 0xbdf250: StoreField: r2->field_f = r0
    //     0xbdf250: stur            w0, [x2, #0xf]
    // 0xbdf254: r16 = Instance_SizedBox
    //     0xbdf254: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xbdf258: ldr             x16, [x16, #0x9f0]
    // 0xbdf25c: StoreField: r2->field_13 = r16
    //     0xbdf25c: stur            w16, [x2, #0x13]
    // 0xbdf260: r1 = <Widget>
    //     0xbdf260: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdf264: r0 = AllocateGrowableArray()
    //     0xbdf264: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdf268: mov             x1, x0
    // 0xbdf26c: ldur            x0, [fp, #-0x38]
    // 0xbdf270: stur            x1, [fp, #-0x30]
    // 0xbdf274: StoreField: r1->field_f = r0
    //     0xbdf274: stur            w0, [x1, #0xf]
    // 0xbdf278: r0 = 4
    //     0xbdf278: movz            x0, #0x4
    // 0xbdf27c: StoreField: r1->field_b = r0
    //     0xbdf27c: stur            w0, [x1, #0xb]
    // 0xbdf280: r0 = Column()
    //     0xbdf280: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdf284: mov             x2, x0
    // 0xbdf288: r0 = Instance_Axis
    //     0xbdf288: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdf28c: stur            x2, [fp, #-0x38]
    // 0xbdf290: StoreField: r2->field_f = r0
    //     0xbdf290: stur            w0, [x2, #0xf]
    // 0xbdf294: r3 = Instance_MainAxisAlignment
    //     0xbdf294: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdf298: ldr             x3, [x3, #0xa08]
    // 0xbdf29c: StoreField: r2->field_13 = r3
    //     0xbdf29c: stur            w3, [x2, #0x13]
    // 0xbdf2a0: r4 = Instance_MainAxisSize
    //     0xbdf2a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdf2a4: ldr             x4, [x4, #0xa10]
    // 0xbdf2a8: ArrayStore: r2[0] = r4  ; List_4
    //     0xbdf2a8: stur            w4, [x2, #0x17]
    // 0xbdf2ac: r1 = Instance_CrossAxisAlignment
    //     0xbdf2ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbdf2b0: ldr             x1, [x1, #0x890]
    // 0xbdf2b4: StoreField: r2->field_1b = r1
    //     0xbdf2b4: stur            w1, [x2, #0x1b]
    // 0xbdf2b8: r5 = Instance_VerticalDirection
    //     0xbdf2b8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdf2bc: ldr             x5, [x5, #0xa20]
    // 0xbdf2c0: StoreField: r2->field_23 = r5
    //     0xbdf2c0: stur            w5, [x2, #0x23]
    // 0xbdf2c4: r6 = Instance_Clip
    //     0xbdf2c4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdf2c8: ldr             x6, [x6, #0x38]
    // 0xbdf2cc: StoreField: r2->field_2b = r6
    //     0xbdf2cc: stur            w6, [x2, #0x2b]
    // 0xbdf2d0: StoreField: r2->field_2f = rZR
    //     0xbdf2d0: stur            xzr, [x2, #0x2f]
    // 0xbdf2d4: ldur            x1, [fp, #-0x30]
    // 0xbdf2d8: StoreField: r2->field_b = r1
    //     0xbdf2d8: stur            w1, [x2, #0xb]
    // 0xbdf2dc: ldur            x7, [fp, #-0x28]
    // 0xbdf2e0: LoadField: r1 = r7->field_b
    //     0xbdf2e0: ldur            w1, [x7, #0xb]
    // 0xbdf2e4: LoadField: r8 = r7->field_f
    //     0xbdf2e4: ldur            w8, [x7, #0xf]
    // 0xbdf2e8: DecompressPointer r8
    //     0xbdf2e8: add             x8, x8, HEAP, lsl #32
    // 0xbdf2ec: LoadField: r9 = r8->field_b
    //     0xbdf2ec: ldur            w9, [x8, #0xb]
    // 0xbdf2f0: r8 = LoadInt32Instr(r1)
    //     0xbdf2f0: sbfx            x8, x1, #1, #0x1f
    // 0xbdf2f4: stur            x8, [fp, #-0x50]
    // 0xbdf2f8: r1 = LoadInt32Instr(r9)
    //     0xbdf2f8: sbfx            x1, x9, #1, #0x1f
    // 0xbdf2fc: cmp             x8, x1
    // 0xbdf300: b.ne            #0xbdf30c
    // 0xbdf304: mov             x1, x7
    // 0xbdf308: r0 = _growToNextCapacity()
    //     0xbdf308: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbdf30c: ldur            x3, [fp, #-0x28]
    // 0xbdf310: ldur            x2, [fp, #-0x50]
    // 0xbdf314: add             x0, x2, #1
    // 0xbdf318: lsl             x1, x0, #1
    // 0xbdf31c: StoreField: r3->field_b = r1
    //     0xbdf31c: stur            w1, [x3, #0xb]
    // 0xbdf320: LoadField: r1 = r3->field_f
    //     0xbdf320: ldur            w1, [x3, #0xf]
    // 0xbdf324: DecompressPointer r1
    //     0xbdf324: add             x1, x1, HEAP, lsl #32
    // 0xbdf328: ldur            x0, [fp, #-0x38]
    // 0xbdf32c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbdf32c: add             x25, x1, x2, lsl #2
    //     0xbdf330: add             x25, x25, #0xf
    //     0xbdf334: str             w0, [x25]
    //     0xbdf338: tbz             w0, #0, #0xbdf354
    //     0xbdf33c: ldurb           w16, [x1, #-1]
    //     0xbdf340: ldurb           w17, [x0, #-1]
    //     0xbdf344: and             x16, x17, x16, lsr #2
    //     0xbdf348: tst             x16, HEAP, lsr #32
    //     0xbdf34c: b.eq            #0xbdf354
    //     0xbdf350: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbdf354: b               #0xbdf35c
    // 0xbdf358: mov             x3, x2
    // 0xbdf35c: ldur            x0, [fp, #-8]
    // 0xbdf360: LoadField: r1 = r0->field_b
    //     0xbdf360: ldur            w1, [x0, #0xb]
    // 0xbdf364: DecompressPointer r1
    //     0xbdf364: add             x1, x1, HEAP, lsl #32
    // 0xbdf368: cmp             w1, NULL
    // 0xbdf36c: b.eq            #0xbdf728
    // 0xbdf370: LoadField: r2 = r1->field_b
    //     0xbdf370: ldur            w2, [x1, #0xb]
    // 0xbdf374: DecompressPointer r2
    //     0xbdf374: add             x2, x2, HEAP, lsl #32
    // 0xbdf378: cmp             w2, NULL
    // 0xbdf37c: b.ne            #0xbdf388
    // 0xbdf380: r4 = Null
    //     0xbdf380: mov             x4, NULL
    // 0xbdf384: b               #0xbdf390
    // 0xbdf388: LoadField: r1 = r2->field_b
    //     0xbdf388: ldur            w1, [x2, #0xb]
    // 0xbdf38c: mov             x4, x1
    // 0xbdf390: stur            x4, [fp, #-0x38]
    // 0xbdf394: LoadField: r5 = r0->field_13
    //     0xbdf394: ldur            w5, [x0, #0x13]
    // 0xbdf398: DecompressPointer r5
    //     0xbdf398: add             x5, x5, HEAP, lsl #32
    // 0xbdf39c: r16 = Sentinel
    //     0xbdf39c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbdf3a0: cmp             w5, w16
    // 0xbdf3a4: b.eq            #0xbdf72c
    // 0xbdf3a8: ldur            x2, [fp, #-0x20]
    // 0xbdf3ac: stur            x5, [fp, #-0x30]
    // 0xbdf3b0: r1 = Function '<anonymous closure>':.
    //     0xbdf3b0: add             x1, PP, #0x53, lsl #12  ; [pp+0x537a8] AnonymousClosure: (0xbe006c), in [package:customer_app/app/presentation/views/line/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::build (0xbdf03c)
    //     0xbdf3b4: ldr             x1, [x1, #0x7a8]
    // 0xbdf3b8: r0 = AllocateClosure()
    //     0xbdf3b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdf3bc: ldur            x2, [fp, #-0x20]
    // 0xbdf3c0: r1 = Function '<anonymous closure>':.
    //     0xbdf3c0: add             x1, PP, #0x53, lsl #12  ; [pp+0x537b0] AnonymousClosure: (0xbdf73c), in [package:customer_app/app/presentation/views/line/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::build (0xbdf03c)
    //     0xbdf3c4: ldr             x1, [x1, #0x7b0]
    // 0xbdf3c8: stur            x0, [fp, #-0x20]
    // 0xbdf3cc: r0 = AllocateClosure()
    //     0xbdf3cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdf3d0: stur            x0, [fp, #-0x40]
    // 0xbdf3d4: r0 = PageView()
    //     0xbdf3d4: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbdf3d8: stur            x0, [fp, #-0x48]
    // 0xbdf3dc: r16 = Instance_BouncingScrollPhysics
    //     0xbdf3dc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbdf3e0: ldr             x16, [x16, #0x890]
    // 0xbdf3e4: ldur            lr, [fp, #-0x30]
    // 0xbdf3e8: stp             lr, x16, [SP]
    // 0xbdf3ec: mov             x1, x0
    // 0xbdf3f0: ldur            x2, [fp, #-0x40]
    // 0xbdf3f4: ldur            x3, [fp, #-0x38]
    // 0xbdf3f8: ldur            x5, [fp, #-0x20]
    // 0xbdf3fc: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xbdf3fc: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xbdf400: ldr             x4, [x4, #0xe40]
    // 0xbdf404: r0 = PageView.builder()
    //     0xbdf404: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbdf408: r0 = AspectRatio()
    //     0xbdf408: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xbdf40c: d0 = 1.000000
    //     0xbdf40c: fmov            d0, #1.00000000
    // 0xbdf410: stur            x0, [fp, #-0x20]
    // 0xbdf414: StoreField: r0->field_f = d0
    //     0xbdf414: stur            d0, [x0, #0xf]
    // 0xbdf418: ldur            x1, [fp, #-0x48]
    // 0xbdf41c: StoreField: r0->field_b = r1
    //     0xbdf41c: stur            w1, [x0, #0xb]
    // 0xbdf420: ldur            x2, [fp, #-0x28]
    // 0xbdf424: LoadField: r1 = r2->field_b
    //     0xbdf424: ldur            w1, [x2, #0xb]
    // 0xbdf428: LoadField: r3 = r2->field_f
    //     0xbdf428: ldur            w3, [x2, #0xf]
    // 0xbdf42c: DecompressPointer r3
    //     0xbdf42c: add             x3, x3, HEAP, lsl #32
    // 0xbdf430: LoadField: r4 = r3->field_b
    //     0xbdf430: ldur            w4, [x3, #0xb]
    // 0xbdf434: r3 = LoadInt32Instr(r1)
    //     0xbdf434: sbfx            x3, x1, #1, #0x1f
    // 0xbdf438: stur            x3, [fp, #-0x50]
    // 0xbdf43c: r1 = LoadInt32Instr(r4)
    //     0xbdf43c: sbfx            x1, x4, #1, #0x1f
    // 0xbdf440: cmp             x3, x1
    // 0xbdf444: b.ne            #0xbdf450
    // 0xbdf448: mov             x1, x2
    // 0xbdf44c: r0 = _growToNextCapacity()
    //     0xbdf44c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbdf450: ldur            x4, [fp, #-8]
    // 0xbdf454: ldur            x2, [fp, #-0x28]
    // 0xbdf458: ldur            x3, [fp, #-0x50]
    // 0xbdf45c: add             x0, x3, #1
    // 0xbdf460: lsl             x1, x0, #1
    // 0xbdf464: StoreField: r2->field_b = r1
    //     0xbdf464: stur            w1, [x2, #0xb]
    // 0xbdf468: LoadField: r1 = r2->field_f
    //     0xbdf468: ldur            w1, [x2, #0xf]
    // 0xbdf46c: DecompressPointer r1
    //     0xbdf46c: add             x1, x1, HEAP, lsl #32
    // 0xbdf470: ldur            x0, [fp, #-0x20]
    // 0xbdf474: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbdf474: add             x25, x1, x3, lsl #2
    //     0xbdf478: add             x25, x25, #0xf
    //     0xbdf47c: str             w0, [x25]
    //     0xbdf480: tbz             w0, #0, #0xbdf49c
    //     0xbdf484: ldurb           w16, [x1, #-1]
    //     0xbdf488: ldurb           w17, [x0, #-1]
    //     0xbdf48c: and             x16, x17, x16, lsr #2
    //     0xbdf490: tst             x16, HEAP, lsr #32
    //     0xbdf494: b.eq            #0xbdf49c
    //     0xbdf498: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbdf49c: LoadField: r0 = r4->field_b
    //     0xbdf49c: ldur            w0, [x4, #0xb]
    // 0xbdf4a0: DecompressPointer r0
    //     0xbdf4a0: add             x0, x0, HEAP, lsl #32
    // 0xbdf4a4: cmp             w0, NULL
    // 0xbdf4a8: b.eq            #0xbdf738
    // 0xbdf4ac: LoadField: r1 = r0->field_b
    //     0xbdf4ac: ldur            w1, [x0, #0xb]
    // 0xbdf4b0: DecompressPointer r1
    //     0xbdf4b0: add             x1, x1, HEAP, lsl #32
    // 0xbdf4b4: cmp             w1, NULL
    // 0xbdf4b8: b.ne            #0xbdf4c4
    // 0xbdf4bc: r0 = Null
    //     0xbdf4bc: mov             x0, NULL
    // 0xbdf4c0: b               #0xbdf4c8
    // 0xbdf4c4: LoadField: r0 = r1->field_b
    //     0xbdf4c4: ldur            w0, [x1, #0xb]
    // 0xbdf4c8: cmp             w0, NULL
    // 0xbdf4cc: b.ne            #0xbdf4d8
    // 0xbdf4d0: r0 = 0
    //     0xbdf4d0: movz            x0, #0
    // 0xbdf4d4: b               #0xbdf4e0
    // 0xbdf4d8: r3 = LoadInt32Instr(r0)
    //     0xbdf4d8: sbfx            x3, x0, #1, #0x1f
    // 0xbdf4dc: mov             x0, x3
    // 0xbdf4e0: cmp             x0, #1
    // 0xbdf4e4: b.le            #0xbdf694
    // 0xbdf4e8: cmp             w1, NULL
    // 0xbdf4ec: b.ne            #0xbdf4f8
    // 0xbdf4f0: r0 = Null
    //     0xbdf4f0: mov             x0, NULL
    // 0xbdf4f4: b               #0xbdf4fc
    // 0xbdf4f8: LoadField: r0 = r1->field_b
    //     0xbdf4f8: ldur            w0, [x1, #0xb]
    // 0xbdf4fc: cmp             w0, NULL
    // 0xbdf500: b.ne            #0xbdf50c
    // 0xbdf504: r0 = 0
    //     0xbdf504: movz            x0, #0
    // 0xbdf508: b               #0xbdf514
    // 0xbdf50c: r1 = LoadInt32Instr(r0)
    //     0xbdf50c: sbfx            x1, x0, #1, #0x1f
    // 0xbdf510: mov             x0, x1
    // 0xbdf514: stur            x0, [fp, #-0x58]
    // 0xbdf518: ArrayLoad: r3 = r4[0]  ; List_8
    //     0xbdf518: ldur            x3, [x4, #0x17]
    // 0xbdf51c: ldur            x1, [fp, #-0x10]
    // 0xbdf520: stur            x3, [fp, #-0x50]
    // 0xbdf524: r0 = of()
    //     0xbdf524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdf528: LoadField: r1 = r0->field_5b
    //     0xbdf528: ldur            w1, [x0, #0x5b]
    // 0xbdf52c: DecompressPointer r1
    //     0xbdf52c: add             x1, x1, HEAP, lsl #32
    // 0xbdf530: stur            x1, [fp, #-8]
    // 0xbdf534: r0 = CarouselIndicator()
    //     0xbdf534: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xbdf538: mov             x3, x0
    // 0xbdf53c: ldur            x0, [fp, #-0x58]
    // 0xbdf540: stur            x3, [fp, #-0x10]
    // 0xbdf544: StoreField: r3->field_b = r0
    //     0xbdf544: stur            x0, [x3, #0xb]
    // 0xbdf548: ldur            x0, [fp, #-0x50]
    // 0xbdf54c: StoreField: r3->field_13 = r0
    //     0xbdf54c: stur            x0, [x3, #0x13]
    // 0xbdf550: ldur            x0, [fp, #-8]
    // 0xbdf554: StoreField: r3->field_1b = r0
    //     0xbdf554: stur            w0, [x3, #0x1b]
    // 0xbdf558: r0 = Instance_Color
    //     0xbdf558: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbdf55c: ldr             x0, [x0, #0x90]
    // 0xbdf560: StoreField: r3->field_1f = r0
    //     0xbdf560: stur            w0, [x3, #0x1f]
    // 0xbdf564: r1 = Null
    //     0xbdf564: mov             x1, NULL
    // 0xbdf568: r2 = 2
    //     0xbdf568: movz            x2, #0x2
    // 0xbdf56c: r0 = AllocateArray()
    //     0xbdf56c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdf570: mov             x2, x0
    // 0xbdf574: ldur            x0, [fp, #-0x10]
    // 0xbdf578: stur            x2, [fp, #-8]
    // 0xbdf57c: StoreField: r2->field_f = r0
    //     0xbdf57c: stur            w0, [x2, #0xf]
    // 0xbdf580: r1 = <Widget>
    //     0xbdf580: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdf584: r0 = AllocateGrowableArray()
    //     0xbdf584: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdf588: mov             x1, x0
    // 0xbdf58c: ldur            x0, [fp, #-8]
    // 0xbdf590: stur            x1, [fp, #-0x10]
    // 0xbdf594: StoreField: r1->field_f = r0
    //     0xbdf594: stur            w0, [x1, #0xf]
    // 0xbdf598: r0 = 2
    //     0xbdf598: movz            x0, #0x2
    // 0xbdf59c: StoreField: r1->field_b = r0
    //     0xbdf59c: stur            w0, [x1, #0xb]
    // 0xbdf5a0: r0 = Row()
    //     0xbdf5a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbdf5a4: mov             x1, x0
    // 0xbdf5a8: r0 = Instance_Axis
    //     0xbdf5a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbdf5ac: stur            x1, [fp, #-8]
    // 0xbdf5b0: StoreField: r1->field_f = r0
    //     0xbdf5b0: stur            w0, [x1, #0xf]
    // 0xbdf5b4: r0 = Instance_MainAxisAlignment
    //     0xbdf5b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbdf5b8: ldr             x0, [x0, #0xab0]
    // 0xbdf5bc: StoreField: r1->field_13 = r0
    //     0xbdf5bc: stur            w0, [x1, #0x13]
    // 0xbdf5c0: r0 = Instance_MainAxisSize
    //     0xbdf5c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdf5c4: ldr             x0, [x0, #0xa10]
    // 0xbdf5c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdf5c8: stur            w0, [x1, #0x17]
    // 0xbdf5cc: r0 = Instance_CrossAxisAlignment
    //     0xbdf5cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdf5d0: ldr             x0, [x0, #0xa18]
    // 0xbdf5d4: StoreField: r1->field_1b = r0
    //     0xbdf5d4: stur            w0, [x1, #0x1b]
    // 0xbdf5d8: r0 = Instance_VerticalDirection
    //     0xbdf5d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdf5dc: ldr             x0, [x0, #0xa20]
    // 0xbdf5e0: StoreField: r1->field_23 = r0
    //     0xbdf5e0: stur            w0, [x1, #0x23]
    // 0xbdf5e4: r2 = Instance_Clip
    //     0xbdf5e4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdf5e8: ldr             x2, [x2, #0x38]
    // 0xbdf5ec: StoreField: r1->field_2b = r2
    //     0xbdf5ec: stur            w2, [x1, #0x2b]
    // 0xbdf5f0: StoreField: r1->field_2f = rZR
    //     0xbdf5f0: stur            xzr, [x1, #0x2f]
    // 0xbdf5f4: ldur            x3, [fp, #-0x10]
    // 0xbdf5f8: StoreField: r1->field_b = r3
    //     0xbdf5f8: stur            w3, [x1, #0xb]
    // 0xbdf5fc: r0 = Padding()
    //     0xbdf5fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdf600: mov             x2, x0
    // 0xbdf604: r0 = Instance_EdgeInsets
    //     0xbdf604: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbdf608: ldr             x0, [x0, #0xa00]
    // 0xbdf60c: stur            x2, [fp, #-0x10]
    // 0xbdf610: StoreField: r2->field_f = r0
    //     0xbdf610: stur            w0, [x2, #0xf]
    // 0xbdf614: ldur            x0, [fp, #-8]
    // 0xbdf618: StoreField: r2->field_b = r0
    //     0xbdf618: stur            w0, [x2, #0xb]
    // 0xbdf61c: ldur            x0, [fp, #-0x28]
    // 0xbdf620: LoadField: r1 = r0->field_b
    //     0xbdf620: ldur            w1, [x0, #0xb]
    // 0xbdf624: LoadField: r3 = r0->field_f
    //     0xbdf624: ldur            w3, [x0, #0xf]
    // 0xbdf628: DecompressPointer r3
    //     0xbdf628: add             x3, x3, HEAP, lsl #32
    // 0xbdf62c: LoadField: r4 = r3->field_b
    //     0xbdf62c: ldur            w4, [x3, #0xb]
    // 0xbdf630: r3 = LoadInt32Instr(r1)
    //     0xbdf630: sbfx            x3, x1, #1, #0x1f
    // 0xbdf634: stur            x3, [fp, #-0x50]
    // 0xbdf638: r1 = LoadInt32Instr(r4)
    //     0xbdf638: sbfx            x1, x4, #1, #0x1f
    // 0xbdf63c: cmp             x3, x1
    // 0xbdf640: b.ne            #0xbdf64c
    // 0xbdf644: mov             x1, x0
    // 0xbdf648: r0 = _growToNextCapacity()
    //     0xbdf648: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbdf64c: ldur            x2, [fp, #-0x28]
    // 0xbdf650: ldur            x3, [fp, #-0x50]
    // 0xbdf654: add             x0, x3, #1
    // 0xbdf658: lsl             x1, x0, #1
    // 0xbdf65c: StoreField: r2->field_b = r1
    //     0xbdf65c: stur            w1, [x2, #0xb]
    // 0xbdf660: LoadField: r1 = r2->field_f
    //     0xbdf660: ldur            w1, [x2, #0xf]
    // 0xbdf664: DecompressPointer r1
    //     0xbdf664: add             x1, x1, HEAP, lsl #32
    // 0xbdf668: ldur            x0, [fp, #-0x10]
    // 0xbdf66c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbdf66c: add             x25, x1, x3, lsl #2
    //     0xbdf670: add             x25, x25, #0xf
    //     0xbdf674: str             w0, [x25]
    //     0xbdf678: tbz             w0, #0, #0xbdf694
    //     0xbdf67c: ldurb           w16, [x1, #-1]
    //     0xbdf680: ldurb           w17, [x0, #-1]
    //     0xbdf684: and             x16, x17, x16, lsr #2
    //     0xbdf688: tst             x16, HEAP, lsr #32
    //     0xbdf68c: b.eq            #0xbdf694
    //     0xbdf690: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbdf694: ldur            x0, [fp, #-0x18]
    // 0xbdf698: r0 = Column()
    //     0xbdf698: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdf69c: mov             x1, x0
    // 0xbdf6a0: r0 = Instance_Axis
    //     0xbdf6a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdf6a4: stur            x1, [fp, #-8]
    // 0xbdf6a8: StoreField: r1->field_f = r0
    //     0xbdf6a8: stur            w0, [x1, #0xf]
    // 0xbdf6ac: r0 = Instance_MainAxisAlignment
    //     0xbdf6ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdf6b0: ldr             x0, [x0, #0xa08]
    // 0xbdf6b4: StoreField: r1->field_13 = r0
    //     0xbdf6b4: stur            w0, [x1, #0x13]
    // 0xbdf6b8: r0 = Instance_MainAxisSize
    //     0xbdf6b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbdf6bc: ldr             x0, [x0, #0xdd0]
    // 0xbdf6c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdf6c0: stur            w0, [x1, #0x17]
    // 0xbdf6c4: ldur            x0, [fp, #-0x18]
    // 0xbdf6c8: StoreField: r1->field_1b = r0
    //     0xbdf6c8: stur            w0, [x1, #0x1b]
    // 0xbdf6cc: r0 = Instance_VerticalDirection
    //     0xbdf6cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdf6d0: ldr             x0, [x0, #0xa20]
    // 0xbdf6d4: StoreField: r1->field_23 = r0
    //     0xbdf6d4: stur            w0, [x1, #0x23]
    // 0xbdf6d8: r0 = Instance_Clip
    //     0xbdf6d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdf6dc: ldr             x0, [x0, #0x38]
    // 0xbdf6e0: StoreField: r1->field_2b = r0
    //     0xbdf6e0: stur            w0, [x1, #0x2b]
    // 0xbdf6e4: StoreField: r1->field_2f = rZR
    //     0xbdf6e4: stur            xzr, [x1, #0x2f]
    // 0xbdf6e8: ldur            x0, [fp, #-0x28]
    // 0xbdf6ec: StoreField: r1->field_b = r0
    //     0xbdf6ec: stur            w0, [x1, #0xb]
    // 0xbdf6f0: r0 = Padding()
    //     0xbdf6f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdf6f4: r1 = Instance_EdgeInsets
    //     0xbdf6f4: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xbdf6f8: ldr             x1, [x1, #0x240]
    // 0xbdf6fc: StoreField: r0->field_f = r1
    //     0xbdf6fc: stur            w1, [x0, #0xf]
    // 0xbdf700: ldur            x1, [fp, #-8]
    // 0xbdf704: StoreField: r0->field_b = r1
    //     0xbdf704: stur            w1, [x0, #0xb]
    // 0xbdf708: LeaveFrame
    //     0xbdf708: mov             SP, fp
    //     0xbdf70c: ldp             fp, lr, [SP], #0x10
    // 0xbdf710: ret
    //     0xbdf710: ret             
    // 0xbdf714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdf714: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdf718: b               #0xbdf064
    // 0xbdf71c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdf71c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdf720: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdf720: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdf724: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdf724: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdf728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdf728: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdf72c: r9 = _pageLineController
    //     0xbdf72c: add             x9, PP, #0x53, lsl #12  ; [pp+0x537b8] Field <_CollectionPosterCarouselState@1704230187._pageLineController@1704230187>: late (offset: 0x14)
    //     0xbdf730: ldr             x9, [x9, #0x7b8]
    // 0xbdf734: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbdf734: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbdf738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdf738: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbdf73c, size: 0x94
    // 0xbdf73c: EnterFrame
    //     0xbdf73c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdf740: mov             fp, SP
    // 0xbdf744: AllocStack(0x8)
    //     0xbdf744: sub             SP, SP, #8
    // 0xbdf748: SetupParameters()
    //     0xbdf748: ldr             x0, [fp, #0x20]
    //     0xbdf74c: ldur            w1, [x0, #0x17]
    //     0xbdf750: add             x1, x1, HEAP, lsl #32
    // 0xbdf754: CheckStackOverflow
    //     0xbdf754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdf758: cmp             SP, x16
    //     0xbdf75c: b.ls            #0xbdf7c4
    // 0xbdf760: LoadField: r0 = r1->field_f
    //     0xbdf760: ldur            w0, [x1, #0xf]
    // 0xbdf764: DecompressPointer r0
    //     0xbdf764: add             x0, x0, HEAP, lsl #32
    // 0xbdf768: stur            x0, [fp, #-8]
    // 0xbdf76c: LoadField: r1 = r0->field_b
    //     0xbdf76c: ldur            w1, [x0, #0xb]
    // 0xbdf770: DecompressPointer r1
    //     0xbdf770: add             x1, x1, HEAP, lsl #32
    // 0xbdf774: cmp             w1, NULL
    // 0xbdf778: b.eq            #0xbdf7cc
    // 0xbdf77c: LoadField: r2 = r1->field_b
    //     0xbdf77c: ldur            w2, [x1, #0xb]
    // 0xbdf780: DecompressPointer r2
    //     0xbdf780: add             x2, x2, HEAP, lsl #32
    // 0xbdf784: cmp             w2, NULL
    // 0xbdf788: b.ne            #0xbdf7a0
    // 0xbdf78c: r1 = <Entity>
    //     0xbdf78c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xbdf790: ldr             x1, [x1, #0xb68]
    // 0xbdf794: r2 = 0
    //     0xbdf794: movz            x2, #0
    // 0xbdf798: r0 = AllocateArray()
    //     0xbdf798: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdf79c: mov             x2, x0
    // 0xbdf7a0: ldr             x0, [fp, #0x10]
    // 0xbdf7a4: r3 = LoadInt32Instr(r0)
    //     0xbdf7a4: sbfx            x3, x0, #1, #0x1f
    //     0xbdf7a8: tbz             w0, #0, #0xbdf7b0
    //     0xbdf7ac: ldur            x3, [x0, #7]
    // 0xbdf7b0: ldur            x1, [fp, #-8]
    // 0xbdf7b4: r0 = lineThemeSlider()
    //     0xbdf7b4: bl              #0xbdf7d0  ; [package:customer_app/app/presentation/views/line/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::lineThemeSlider
    // 0xbdf7b8: LeaveFrame
    //     0xbdf7b8: mov             SP, fp
    //     0xbdf7bc: ldp             fp, lr, [SP], #0x10
    // 0xbdf7c0: ret
    //     0xbdf7c0: ret             
    // 0xbdf7c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdf7c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdf7c8: b               #0xbdf760
    // 0xbdf7cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdf7cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ lineThemeSlider(/* No info */) {
    // ** addr: 0xbdf7d0, size: 0x588
    // 0xbdf7d0: EnterFrame
    //     0xbdf7d0: stp             fp, lr, [SP, #-0x10]!
    //     0xbdf7d4: mov             fp, SP
    // 0xbdf7d8: AllocStack(0x60)
    //     0xbdf7d8: sub             SP, SP, #0x60
    // 0xbdf7dc: SetupParameters(_CollectionPosterCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbdf7dc: stur            x1, [fp, #-8]
    //     0xbdf7e0: stur            x2, [fp, #-0x10]
    //     0xbdf7e4: stur            x3, [fp, #-0x18]
    // 0xbdf7e8: CheckStackOverflow
    //     0xbdf7e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdf7ec: cmp             SP, x16
    //     0xbdf7f0: b.ls            #0xbdfd44
    // 0xbdf7f4: r1 = 3
    //     0xbdf7f4: movz            x1, #0x3
    // 0xbdf7f8: r0 = AllocateContext()
    //     0xbdf7f8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdf7fc: mov             x3, x0
    // 0xbdf800: ldur            x2, [fp, #-8]
    // 0xbdf804: stur            x3, [fp, #-0x20]
    // 0xbdf808: StoreField: r3->field_f = r2
    //     0xbdf808: stur            w2, [x3, #0xf]
    // 0xbdf80c: ldur            x4, [fp, #-0x10]
    // 0xbdf810: StoreField: r3->field_13 = r4
    //     0xbdf810: stur            w4, [x3, #0x13]
    // 0xbdf814: ldur            x5, [fp, #-0x18]
    // 0xbdf818: r0 = BoxInt64Instr(r5)
    //     0xbdf818: sbfiz           x0, x5, #1, #0x1f
    //     0xbdf81c: cmp             x5, x0, asr #1
    //     0xbdf820: b.eq            #0xbdf82c
    //     0xbdf824: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbdf828: stur            x5, [x0, #7]
    // 0xbdf82c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbdf82c: stur            w0, [x3, #0x17]
    // 0xbdf830: stp             x0, x4, [SP]
    // 0xbdf834: r4 = 0
    //     0xbdf834: movz            x4, #0
    // 0xbdf838: ldr             x0, [SP, #8]
    // 0xbdf83c: r16 = UnlinkedCall_0x613b5c
    //     0xbdf83c: add             x16, PP, #0x53, lsl #12  ; [pp+0x537c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdf840: add             x16, x16, #0x7c0
    // 0xbdf844: ldp             x5, lr, [x16]
    // 0xbdf848: blr             lr
    // 0xbdf84c: LoadField: r1 = r0->field_13
    //     0xbdf84c: ldur            w1, [x0, #0x13]
    // 0xbdf850: DecompressPointer r1
    //     0xbdf850: add             x1, x1, HEAP, lsl #32
    // 0xbdf854: cmp             w1, NULL
    // 0xbdf858: b.ne            #0xbdf864
    // 0xbdf85c: r0 = ""
    //     0xbdf85c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdf860: b               #0xbdf868
    // 0xbdf864: mov             x0, x1
    // 0xbdf868: ldur            x2, [fp, #-0x20]
    // 0xbdf86c: stur            x0, [fp, #-0x10]
    // 0xbdf870: r0 = ImageHeaders.forImages()
    //     0xbdf870: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbdf874: r1 = Function '<anonymous closure>':.
    //     0xbdf874: add             x1, PP, #0x53, lsl #12  ; [pp+0x537d0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdf878: ldr             x1, [x1, #0x7d0]
    // 0xbdf87c: r2 = Null
    //     0xbdf87c: mov             x2, NULL
    // 0xbdf880: stur            x0, [fp, #-0x28]
    // 0xbdf884: r0 = AllocateClosure()
    //     0xbdf884: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdf888: r1 = Function '<anonymous closure>':.
    //     0xbdf888: add             x1, PP, #0x53, lsl #12  ; [pp+0x537d8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdf88c: ldr             x1, [x1, #0x7d8]
    // 0xbdf890: r2 = Null
    //     0xbdf890: mov             x2, NULL
    // 0xbdf894: stur            x0, [fp, #-0x30]
    // 0xbdf898: r0 = AllocateClosure()
    //     0xbdf898: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdf89c: stur            x0, [fp, #-0x38]
    // 0xbdf8a0: r0 = CachedNetworkImage()
    //     0xbdf8a0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbdf8a4: stur            x0, [fp, #-0x40]
    // 0xbdf8a8: ldur            x16, [fp, #-0x28]
    // 0xbdf8ac: ldur            lr, [fp, #-0x30]
    // 0xbdf8b0: stp             lr, x16, [SP, #0x10]
    // 0xbdf8b4: ldur            x16, [fp, #-0x38]
    // 0xbdf8b8: r30 = Instance_BoxFit
    //     0xbdf8b8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbdf8bc: ldr             lr, [lr, #0x118]
    // 0xbdf8c0: stp             lr, x16, [SP]
    // 0xbdf8c4: mov             x1, x0
    // 0xbdf8c8: ldur            x2, [fp, #-0x10]
    // 0xbdf8cc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, httpHeaders, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xbdf8cc: add             x4, PP, #0x52, lsl #12  ; [pp+0x522b0] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xbdf8d0: ldr             x4, [x4, #0x2b0]
    // 0xbdf8d4: r0 = CachedNetworkImage()
    //     0xbdf8d4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbdf8d8: r0 = AspectRatio()
    //     0xbdf8d8: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xbdf8dc: d0 = 1.000000
    //     0xbdf8dc: fmov            d0, #1.00000000
    // 0xbdf8e0: stur            x0, [fp, #-0x10]
    // 0xbdf8e4: StoreField: r0->field_f = d0
    //     0xbdf8e4: stur            d0, [x0, #0xf]
    // 0xbdf8e8: ldur            x1, [fp, #-0x40]
    // 0xbdf8ec: StoreField: r0->field_b = r1
    //     0xbdf8ec: stur            w1, [x0, #0xb]
    // 0xbdf8f0: ldur            x2, [fp, #-0x20]
    // 0xbdf8f4: LoadField: r1 = r2->field_13
    //     0xbdf8f4: ldur            w1, [x2, #0x13]
    // 0xbdf8f8: DecompressPointer r1
    //     0xbdf8f8: add             x1, x1, HEAP, lsl #32
    // 0xbdf8fc: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbdf8fc: ldur            w3, [x2, #0x17]
    // 0xbdf900: DecompressPointer r3
    //     0xbdf900: add             x3, x3, HEAP, lsl #32
    // 0xbdf904: stp             x3, x1, [SP]
    // 0xbdf908: r4 = 0
    //     0xbdf908: movz            x4, #0
    // 0xbdf90c: ldr             x0, [SP, #8]
    // 0xbdf910: r16 = UnlinkedCall_0x613b5c
    //     0xbdf910: add             x16, PP, #0x53, lsl #12  ; [pp+0x537e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdf914: add             x16, x16, #0x7e0
    // 0xbdf918: ldp             x5, lr, [x16]
    // 0xbdf91c: blr             lr
    // 0xbdf920: LoadField: r1 = r0->field_f
    //     0xbdf920: ldur            w1, [x0, #0xf]
    // 0xbdf924: DecompressPointer r1
    //     0xbdf924: add             x1, x1, HEAP, lsl #32
    // 0xbdf928: cmp             w1, NULL
    // 0xbdf92c: b.eq            #0xbdfd4c
    // 0xbdf930: LoadField: r0 = r1->field_7
    //     0xbdf930: ldur            w0, [x1, #7]
    // 0xbdf934: cbz             w0, #0xbdfb3c
    // 0xbdf938: ldur            x0, [fp, #-8]
    // 0xbdf93c: ldur            x2, [fp, #-0x20]
    // 0xbdf940: LoadField: r1 = r0->field_f
    //     0xbdf940: ldur            w1, [x0, #0xf]
    // 0xbdf944: DecompressPointer r1
    //     0xbdf944: add             x1, x1, HEAP, lsl #32
    // 0xbdf948: cmp             w1, NULL
    // 0xbdf94c: b.eq            #0xbdfd50
    // 0xbdf950: r0 = of()
    //     0xbdf950: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdf954: LoadField: r1 = r0->field_5b
    //     0xbdf954: ldur            w1, [x0, #0x5b]
    // 0xbdf958: DecompressPointer r1
    //     0xbdf958: add             x1, x1, HEAP, lsl #32
    // 0xbdf95c: r16 = <Color>
    //     0xbdf95c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbdf960: ldr             x16, [x16, #0xf80]
    // 0xbdf964: stp             x1, x16, [SP]
    // 0xbdf968: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdf968: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdf96c: r0 = all()
    //     0xbdf96c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdf970: stur            x0, [fp, #-0x28]
    // 0xbdf974: r16 = <EdgeInsets>
    //     0xbdf974: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbdf978: ldr             x16, [x16, #0xda0]
    // 0xbdf97c: r30 = Instance_EdgeInsets
    //     0xbdf97c: add             lr, PP, #0x52, lsl #12  ; [pp+0x52ba0] Obj!EdgeInsets@d58341
    //     0xbdf980: ldr             lr, [lr, #0xba0]
    // 0xbdf984: stp             lr, x16, [SP]
    // 0xbdf988: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdf988: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdf98c: r0 = all()
    //     0xbdf98c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdf990: stur            x0, [fp, #-0x30]
    // 0xbdf994: r16 = <RoundedRectangleBorder>
    //     0xbdf994: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbdf998: ldr             x16, [x16, #0xf78]
    // 0xbdf99c: r30 = Instance_RoundedRectangleBorder
    //     0xbdf99c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbdf9a0: ldr             lr, [lr, #0xd68]
    // 0xbdf9a4: stp             lr, x16, [SP]
    // 0xbdf9a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdf9a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdf9ac: r0 = all()
    //     0xbdf9ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdf9b0: stur            x0, [fp, #-0x38]
    // 0xbdf9b4: r0 = ButtonStyle()
    //     0xbdf9b4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbdf9b8: mov             x1, x0
    // 0xbdf9bc: ldur            x0, [fp, #-0x28]
    // 0xbdf9c0: stur            x1, [fp, #-0x40]
    // 0xbdf9c4: StoreField: r1->field_b = r0
    //     0xbdf9c4: stur            w0, [x1, #0xb]
    // 0xbdf9c8: ldur            x0, [fp, #-0x30]
    // 0xbdf9cc: StoreField: r1->field_23 = r0
    //     0xbdf9cc: stur            w0, [x1, #0x23]
    // 0xbdf9d0: ldur            x0, [fp, #-0x38]
    // 0xbdf9d4: StoreField: r1->field_43 = r0
    //     0xbdf9d4: stur            w0, [x1, #0x43]
    // 0xbdf9d8: r0 = TextButtonThemeData()
    //     0xbdf9d8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbdf9dc: mov             x1, x0
    // 0xbdf9e0: ldur            x0, [fp, #-0x40]
    // 0xbdf9e4: stur            x1, [fp, #-0x28]
    // 0xbdf9e8: StoreField: r1->field_7 = r0
    //     0xbdf9e8: stur            w0, [x1, #7]
    // 0xbdf9ec: ldur            x2, [fp, #-0x20]
    // 0xbdf9f0: LoadField: r0 = r2->field_13
    //     0xbdf9f0: ldur            w0, [x2, #0x13]
    // 0xbdf9f4: DecompressPointer r0
    //     0xbdf9f4: add             x0, x0, HEAP, lsl #32
    // 0xbdf9f8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbdf9f8: ldur            w3, [x2, #0x17]
    // 0xbdf9fc: DecompressPointer r3
    //     0xbdf9fc: add             x3, x3, HEAP, lsl #32
    // 0xbdfa00: stp             x3, x0, [SP]
    // 0xbdfa04: r4 = 0
    //     0xbdfa04: movz            x4, #0
    // 0xbdfa08: ldr             x0, [SP, #8]
    // 0xbdfa0c: r16 = UnlinkedCall_0x613b5c
    //     0xbdfa0c: add             x16, PP, #0x53, lsl #12  ; [pp+0x537f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdfa10: add             x16, x16, #0x7f0
    // 0xbdfa14: ldp             x5, lr, [x16]
    // 0xbdfa18: blr             lr
    // 0xbdfa1c: LoadField: r3 = r0->field_f
    //     0xbdfa1c: ldur            w3, [x0, #0xf]
    // 0xbdfa20: DecompressPointer r3
    //     0xbdfa20: add             x3, x3, HEAP, lsl #32
    // 0xbdfa24: mov             x0, x3
    // 0xbdfa28: stur            x3, [fp, #-0x30]
    // 0xbdfa2c: r2 = Null
    //     0xbdfa2c: mov             x2, NULL
    // 0xbdfa30: r1 = Null
    //     0xbdfa30: mov             x1, NULL
    // 0xbdfa34: r4 = LoadClassIdInstr(r0)
    //     0xbdfa34: ldur            x4, [x0, #-1]
    //     0xbdfa38: ubfx            x4, x4, #0xc, #0x14
    // 0xbdfa3c: sub             x4, x4, #0x5e
    // 0xbdfa40: cmp             x4, #1
    // 0xbdfa44: b.ls            #0xbdfa58
    // 0xbdfa48: r8 = String
    //     0xbdfa48: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xbdfa4c: r3 = Null
    //     0xbdfa4c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53800] Null
    //     0xbdfa50: ldr             x3, [x3, #0x800]
    // 0xbdfa54: r0 = String()
    //     0xbdfa54: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xbdfa58: ldur            x0, [fp, #-8]
    // 0xbdfa5c: LoadField: r1 = r0->field_f
    //     0xbdfa5c: ldur            w1, [x0, #0xf]
    // 0xbdfa60: DecompressPointer r1
    //     0xbdfa60: add             x1, x1, HEAP, lsl #32
    // 0xbdfa64: cmp             w1, NULL
    // 0xbdfa68: b.eq            #0xbdfd54
    // 0xbdfa6c: r0 = of()
    //     0xbdfa6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdfa70: LoadField: r1 = r0->field_87
    //     0xbdfa70: ldur            w1, [x0, #0x87]
    // 0xbdfa74: DecompressPointer r1
    //     0xbdfa74: add             x1, x1, HEAP, lsl #32
    // 0xbdfa78: LoadField: r0 = r1->field_7
    //     0xbdfa78: ldur            w0, [x1, #7]
    // 0xbdfa7c: DecompressPointer r0
    //     0xbdfa7c: add             x0, x0, HEAP, lsl #32
    // 0xbdfa80: r16 = Instance_Color
    //     0xbdfa80: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbdfa84: r30 = 14.000000
    //     0xbdfa84: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbdfa88: ldr             lr, [lr, #0x1d8]
    // 0xbdfa8c: stp             lr, x16, [SP]
    // 0xbdfa90: mov             x1, x0
    // 0xbdfa94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbdfa94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbdfa98: ldr             x4, [x4, #0x9b8]
    // 0xbdfa9c: r0 = copyWith()
    //     0xbdfa9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdfaa0: stur            x0, [fp, #-8]
    // 0xbdfaa4: r0 = Text()
    //     0xbdfaa4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdfaa8: mov             x3, x0
    // 0xbdfaac: ldur            x0, [fp, #-0x30]
    // 0xbdfab0: stur            x3, [fp, #-0x38]
    // 0xbdfab4: StoreField: r3->field_b = r0
    //     0xbdfab4: stur            w0, [x3, #0xb]
    // 0xbdfab8: ldur            x0, [fp, #-8]
    // 0xbdfabc: StoreField: r3->field_13 = r0
    //     0xbdfabc: stur            w0, [x3, #0x13]
    // 0xbdfac0: ldur            x2, [fp, #-0x20]
    // 0xbdfac4: r1 = Function '<anonymous closure>':.
    //     0xbdfac4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53810] AnonymousClosure: (0xbdfe7c), in [package:customer_app/app/presentation/views/line/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::lineThemeSlider (0xbdf7d0)
    //     0xbdfac8: ldr             x1, [x1, #0x810]
    // 0xbdfacc: r0 = AllocateClosure()
    //     0xbdfacc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdfad0: stur            x0, [fp, #-8]
    // 0xbdfad4: r0 = TextButton()
    //     0xbdfad4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbdfad8: mov             x1, x0
    // 0xbdfadc: ldur            x0, [fp, #-8]
    // 0xbdfae0: stur            x1, [fp, #-0x30]
    // 0xbdfae4: StoreField: r1->field_b = r0
    //     0xbdfae4: stur            w0, [x1, #0xb]
    // 0xbdfae8: r0 = false
    //     0xbdfae8: add             x0, NULL, #0x30  ; false
    // 0xbdfaec: StoreField: r1->field_27 = r0
    //     0xbdfaec: stur            w0, [x1, #0x27]
    // 0xbdfaf0: r2 = true
    //     0xbdfaf0: add             x2, NULL, #0x20  ; true
    // 0xbdfaf4: StoreField: r1->field_2f = r2
    //     0xbdfaf4: stur            w2, [x1, #0x2f]
    // 0xbdfaf8: ldur            x3, [fp, #-0x38]
    // 0xbdfafc: StoreField: r1->field_37 = r3
    //     0xbdfafc: stur            w3, [x1, #0x37]
    // 0xbdfb00: r0 = TextButtonTheme()
    //     0xbdfb00: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbdfb04: mov             x1, x0
    // 0xbdfb08: ldur            x0, [fp, #-0x28]
    // 0xbdfb0c: stur            x1, [fp, #-8]
    // 0xbdfb10: StoreField: r1->field_f = r0
    //     0xbdfb10: stur            w0, [x1, #0xf]
    // 0xbdfb14: ldur            x0, [fp, #-0x30]
    // 0xbdfb18: StoreField: r1->field_b = r0
    //     0xbdfb18: stur            w0, [x1, #0xb]
    // 0xbdfb1c: r0 = SizedBox()
    //     0xbdfb1c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbdfb20: mov             x1, x0
    // 0xbdfb24: r0 = 40.000000
    //     0xbdfb24: add             x0, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xbdfb28: ldr             x0, [x0, #8]
    // 0xbdfb2c: StoreField: r1->field_13 = r0
    //     0xbdfb2c: stur            w0, [x1, #0x13]
    // 0xbdfb30: ldur            x0, [fp, #-8]
    // 0xbdfb34: StoreField: r1->field_b = r0
    //     0xbdfb34: stur            w0, [x1, #0xb]
    // 0xbdfb38: b               #0xbdfb54
    // 0xbdfb3c: r0 = Container()
    //     0xbdfb3c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdfb40: mov             x1, x0
    // 0xbdfb44: stur            x0, [fp, #-8]
    // 0xbdfb48: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbdfb48: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbdfb4c: r0 = Container()
    //     0xbdfb4c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdfb50: ldur            x1, [fp, #-8]
    // 0xbdfb54: ldur            x0, [fp, #-0x10]
    // 0xbdfb58: stur            x1, [fp, #-8]
    // 0xbdfb5c: r0 = Padding()
    //     0xbdfb5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdfb60: mov             x3, x0
    // 0xbdfb64: r0 = Instance_EdgeInsets
    //     0xbdfb64: add             x0, PP, #0x52, lsl #12  ; [pp+0x52bc0] Obj!EdgeInsets@d574d1
    //     0xbdfb68: ldr             x0, [x0, #0xbc0]
    // 0xbdfb6c: stur            x3, [fp, #-0x28]
    // 0xbdfb70: StoreField: r3->field_f = r0
    //     0xbdfb70: stur            w0, [x3, #0xf]
    // 0xbdfb74: ldur            x0, [fp, #-8]
    // 0xbdfb78: StoreField: r3->field_b = r0
    //     0xbdfb78: stur            w0, [x3, #0xb]
    // 0xbdfb7c: r1 = Null
    //     0xbdfb7c: mov             x1, NULL
    // 0xbdfb80: r2 = 4
    //     0xbdfb80: movz            x2, #0x4
    // 0xbdfb84: r0 = AllocateArray()
    //     0xbdfb84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdfb88: mov             x2, x0
    // 0xbdfb8c: ldur            x0, [fp, #-0x10]
    // 0xbdfb90: stur            x2, [fp, #-8]
    // 0xbdfb94: StoreField: r2->field_f = r0
    //     0xbdfb94: stur            w0, [x2, #0xf]
    // 0xbdfb98: ldur            x0, [fp, #-0x28]
    // 0xbdfb9c: StoreField: r2->field_13 = r0
    //     0xbdfb9c: stur            w0, [x2, #0x13]
    // 0xbdfba0: r1 = <Widget>
    //     0xbdfba0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdfba4: r0 = AllocateGrowableArray()
    //     0xbdfba4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdfba8: mov             x1, x0
    // 0xbdfbac: ldur            x0, [fp, #-8]
    // 0xbdfbb0: stur            x1, [fp, #-0x10]
    // 0xbdfbb4: StoreField: r1->field_f = r0
    //     0xbdfbb4: stur            w0, [x1, #0xf]
    // 0xbdfbb8: r0 = 4
    //     0xbdfbb8: movz            x0, #0x4
    // 0xbdfbbc: StoreField: r1->field_b = r0
    //     0xbdfbbc: stur            w0, [x1, #0xb]
    // 0xbdfbc0: r0 = Stack()
    //     0xbdfbc0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbdfbc4: mov             x1, x0
    // 0xbdfbc8: r0 = Instance_Alignment
    //     0xbdfbc8: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xbdfbcc: ldr             x0, [x0, #0x5b8]
    // 0xbdfbd0: stur            x1, [fp, #-8]
    // 0xbdfbd4: StoreField: r1->field_f = r0
    //     0xbdfbd4: stur            w0, [x1, #0xf]
    // 0xbdfbd8: r0 = Instance_StackFit
    //     0xbdfbd8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbdfbdc: ldr             x0, [x0, #0xfa8]
    // 0xbdfbe0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdfbe0: stur            w0, [x1, #0x17]
    // 0xbdfbe4: r0 = Instance_Clip
    //     0xbdfbe4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbdfbe8: ldr             x0, [x0, #0x7e0]
    // 0xbdfbec: StoreField: r1->field_1b = r0
    //     0xbdfbec: stur            w0, [x1, #0x1b]
    // 0xbdfbf0: ldur            x0, [fp, #-0x10]
    // 0xbdfbf4: StoreField: r1->field_b = r0
    //     0xbdfbf4: stur            w0, [x1, #0xb]
    // 0xbdfbf8: r0 = InkWell()
    //     0xbdfbf8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbdfbfc: mov             x3, x0
    // 0xbdfc00: ldur            x0, [fp, #-8]
    // 0xbdfc04: stur            x3, [fp, #-0x10]
    // 0xbdfc08: StoreField: r3->field_b = r0
    //     0xbdfc08: stur            w0, [x3, #0xb]
    // 0xbdfc0c: ldur            x2, [fp, #-0x20]
    // 0xbdfc10: r1 = Function '<anonymous closure>':.
    //     0xbdfc10: add             x1, PP, #0x53, lsl #12  ; [pp+0x53818] AnonymousClosure: (0xbdfd58), in [package:customer_app/app/presentation/views/line/home/<USER>/collection_poster_carousel.dart] _CollectionPosterCarouselState::lineThemeSlider (0xbdf7d0)
    //     0xbdfc14: ldr             x1, [x1, #0x818]
    // 0xbdfc18: r0 = AllocateClosure()
    //     0xbdfc18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdfc1c: ldur            x2, [fp, #-0x10]
    // 0xbdfc20: StoreField: r2->field_f = r0
    //     0xbdfc20: stur            w0, [x2, #0xf]
    // 0xbdfc24: r0 = true
    //     0xbdfc24: add             x0, NULL, #0x20  ; true
    // 0xbdfc28: StoreField: r2->field_43 = r0
    //     0xbdfc28: stur            w0, [x2, #0x43]
    // 0xbdfc2c: r1 = Instance_BoxShape
    //     0xbdfc2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbdfc30: ldr             x1, [x1, #0x80]
    // 0xbdfc34: StoreField: r2->field_47 = r1
    //     0xbdfc34: stur            w1, [x2, #0x47]
    // 0xbdfc38: StoreField: r2->field_6f = r0
    //     0xbdfc38: stur            w0, [x2, #0x6f]
    // 0xbdfc3c: r1 = false
    //     0xbdfc3c: add             x1, NULL, #0x30  ; false
    // 0xbdfc40: StoreField: r2->field_73 = r1
    //     0xbdfc40: stur            w1, [x2, #0x73]
    // 0xbdfc44: StoreField: r2->field_83 = r0
    //     0xbdfc44: stur            w0, [x2, #0x83]
    // 0xbdfc48: StoreField: r2->field_7b = r1
    //     0xbdfc48: stur            w1, [x2, #0x7b]
    // 0xbdfc4c: r0 = AnimatedContainer()
    //     0xbdfc4c: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xbdfc50: stur            x0, [fp, #-8]
    // 0xbdfc54: r16 = Instance_Cubic
    //     0xbdfc54: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xbdfc58: ldr             x16, [x16, #0xaf8]
    // 0xbdfc5c: str             x16, [SP]
    // 0xbdfc60: mov             x1, x0
    // 0xbdfc64: ldur            x2, [fp, #-0x10]
    // 0xbdfc68: r3 = Instance_Duration
    //     0xbdfc68: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xbdfc6c: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xbdfc6c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xbdfc70: ldr             x4, [x4, #0xbc8]
    // 0xbdfc74: r0 = AnimatedContainer()
    //     0xbdfc74: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xbdfc78: r1 = <FlexParentData>
    //     0xbdfc78: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbdfc7c: ldr             x1, [x1, #0xe00]
    // 0xbdfc80: r0 = Expanded()
    //     0xbdfc80: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbdfc84: mov             x3, x0
    // 0xbdfc88: r0 = 1
    //     0xbdfc88: movz            x0, #0x1
    // 0xbdfc8c: stur            x3, [fp, #-0x10]
    // 0xbdfc90: StoreField: r3->field_13 = r0
    //     0xbdfc90: stur            x0, [x3, #0x13]
    // 0xbdfc94: r0 = Instance_FlexFit
    //     0xbdfc94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbdfc98: ldr             x0, [x0, #0xe08]
    // 0xbdfc9c: StoreField: r3->field_1b = r0
    //     0xbdfc9c: stur            w0, [x3, #0x1b]
    // 0xbdfca0: ldur            x0, [fp, #-8]
    // 0xbdfca4: StoreField: r3->field_b = r0
    //     0xbdfca4: stur            w0, [x3, #0xb]
    // 0xbdfca8: r1 = Null
    //     0xbdfca8: mov             x1, NULL
    // 0xbdfcac: r2 = 2
    //     0xbdfcac: movz            x2, #0x2
    // 0xbdfcb0: r0 = AllocateArray()
    //     0xbdfcb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdfcb4: mov             x2, x0
    // 0xbdfcb8: ldur            x0, [fp, #-0x10]
    // 0xbdfcbc: stur            x2, [fp, #-8]
    // 0xbdfcc0: StoreField: r2->field_f = r0
    //     0xbdfcc0: stur            w0, [x2, #0xf]
    // 0xbdfcc4: r1 = <Widget>
    //     0xbdfcc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdfcc8: r0 = AllocateGrowableArray()
    //     0xbdfcc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdfccc: mov             x1, x0
    // 0xbdfcd0: ldur            x0, [fp, #-8]
    // 0xbdfcd4: stur            x1, [fp, #-0x10]
    // 0xbdfcd8: StoreField: r1->field_f = r0
    //     0xbdfcd8: stur            w0, [x1, #0xf]
    // 0xbdfcdc: r0 = 2
    //     0xbdfcdc: movz            x0, #0x2
    // 0xbdfce0: StoreField: r1->field_b = r0
    //     0xbdfce0: stur            w0, [x1, #0xb]
    // 0xbdfce4: r0 = Row()
    //     0xbdfce4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbdfce8: r1 = Instance_Axis
    //     0xbdfce8: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbdfcec: StoreField: r0->field_f = r1
    //     0xbdfcec: stur            w1, [x0, #0xf]
    // 0xbdfcf0: r1 = Instance_MainAxisAlignment
    //     0xbdfcf0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdfcf4: ldr             x1, [x1, #0xa08]
    // 0xbdfcf8: StoreField: r0->field_13 = r1
    //     0xbdfcf8: stur            w1, [x0, #0x13]
    // 0xbdfcfc: r1 = Instance_MainAxisSize
    //     0xbdfcfc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdfd00: ldr             x1, [x1, #0xa10]
    // 0xbdfd04: ArrayStore: r0[0] = r1  ; List_4
    //     0xbdfd04: stur            w1, [x0, #0x17]
    // 0xbdfd08: r1 = Instance_CrossAxisAlignment
    //     0xbdfd08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdfd0c: ldr             x1, [x1, #0xa18]
    // 0xbdfd10: StoreField: r0->field_1b = r1
    //     0xbdfd10: stur            w1, [x0, #0x1b]
    // 0xbdfd14: r1 = Instance_VerticalDirection
    //     0xbdfd14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdfd18: ldr             x1, [x1, #0xa20]
    // 0xbdfd1c: StoreField: r0->field_23 = r1
    //     0xbdfd1c: stur            w1, [x0, #0x23]
    // 0xbdfd20: r1 = Instance_Clip
    //     0xbdfd20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdfd24: ldr             x1, [x1, #0x38]
    // 0xbdfd28: StoreField: r0->field_2b = r1
    //     0xbdfd28: stur            w1, [x0, #0x2b]
    // 0xbdfd2c: StoreField: r0->field_2f = rZR
    //     0xbdfd2c: stur            xzr, [x0, #0x2f]
    // 0xbdfd30: ldur            x1, [fp, #-0x10]
    // 0xbdfd34: StoreField: r0->field_b = r1
    //     0xbdfd34: stur            w1, [x0, #0xb]
    // 0xbdfd38: LeaveFrame
    //     0xbdfd38: mov             SP, fp
    //     0xbdfd3c: ldp             fp, lr, [SP], #0x10
    // 0xbdfd40: ret
    //     0xbdfd40: ret             
    // 0xbdfd44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdfd44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdfd48: b               #0xbdf7f4
    // 0xbdfd4c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbdfd4c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xbdfd50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdfd50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdfd54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdfd54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbdfd58, size: 0x124
    // 0xbdfd58: EnterFrame
    //     0xbdfd58: stp             fp, lr, [SP, #-0x10]!
    //     0xbdfd5c: mov             fp, SP
    // 0xbdfd60: AllocStack(0x78)
    //     0xbdfd60: sub             SP, SP, #0x78
    // 0xbdfd64: SetupParameters()
    //     0xbdfd64: ldr             x0, [fp, #0x10]
    //     0xbdfd68: ldur            w1, [x0, #0x17]
    //     0xbdfd6c: add             x1, x1, HEAP, lsl #32
    // 0xbdfd70: CheckStackOverflow
    //     0xbdfd70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdfd74: cmp             SP, x16
    //     0xbdfd78: b.ls            #0xbdfe70
    // 0xbdfd7c: LoadField: r0 = r1->field_f
    //     0xbdfd7c: ldur            w0, [x1, #0xf]
    // 0xbdfd80: DecompressPointer r0
    //     0xbdfd80: add             x0, x0, HEAP, lsl #32
    // 0xbdfd84: LoadField: r2 = r0->field_b
    //     0xbdfd84: ldur            w2, [x0, #0xb]
    // 0xbdfd88: DecompressPointer r2
    //     0xbdfd88: add             x2, x2, HEAP, lsl #32
    // 0xbdfd8c: stur            x2, [fp, #-0x38]
    // 0xbdfd90: cmp             w2, NULL
    // 0xbdfd94: b.eq            #0xbdfe78
    // 0xbdfd98: LoadField: r0 = r2->field_1b
    //     0xbdfd98: ldur            w0, [x2, #0x1b]
    // 0xbdfd9c: DecompressPointer r0
    //     0xbdfd9c: add             x0, x0, HEAP, lsl #32
    // 0xbdfda0: stur            x0, [fp, #-0x30]
    // 0xbdfda4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbdfda4: ldur            w3, [x2, #0x17]
    // 0xbdfda8: DecompressPointer r3
    //     0xbdfda8: add             x3, x3, HEAP, lsl #32
    // 0xbdfdac: stur            x3, [fp, #-0x28]
    // 0xbdfdb0: LoadField: r4 = r2->field_f
    //     0xbdfdb0: ldur            w4, [x2, #0xf]
    // 0xbdfdb4: DecompressPointer r4
    //     0xbdfdb4: add             x4, x4, HEAP, lsl #32
    // 0xbdfdb8: stur            x4, [fp, #-0x20]
    // 0xbdfdbc: LoadField: r5 = r2->field_23
    //     0xbdfdbc: ldur            w5, [x2, #0x23]
    // 0xbdfdc0: DecompressPointer r5
    //     0xbdfdc0: add             x5, x5, HEAP, lsl #32
    // 0xbdfdc4: stur            x5, [fp, #-0x18]
    // 0xbdfdc8: LoadField: r6 = r2->field_1f
    //     0xbdfdc8: ldur            w6, [x2, #0x1f]
    // 0xbdfdcc: DecompressPointer r6
    //     0xbdfdcc: add             x6, x6, HEAP, lsl #32
    // 0xbdfdd0: stur            x6, [fp, #-0x10]
    // 0xbdfdd4: LoadField: r7 = r2->field_2b
    //     0xbdfdd4: ldur            w7, [x2, #0x2b]
    // 0xbdfdd8: DecompressPointer r7
    //     0xbdfdd8: add             x7, x7, HEAP, lsl #32
    // 0xbdfddc: stur            x7, [fp, #-8]
    // 0xbdfde0: LoadField: r8 = r1->field_13
    //     0xbdfde0: ldur            w8, [x1, #0x13]
    // 0xbdfde4: DecompressPointer r8
    //     0xbdfde4: add             x8, x8, HEAP, lsl #32
    // 0xbdfde8: ArrayLoad: r9 = r1[0]  ; List_4
    //     0xbdfde8: ldur            w9, [x1, #0x17]
    // 0xbdfdec: DecompressPointer r9
    //     0xbdfdec: add             x9, x9, HEAP, lsl #32
    // 0xbdfdf0: stp             x9, x8, [SP]
    // 0xbdfdf4: r4 = 0
    //     0xbdfdf4: movz            x4, #0
    // 0xbdfdf8: ldr             x0, [SP, #8]
    // 0xbdfdfc: r16 = UnlinkedCall_0x613b5c
    //     0xbdfdfc: add             x16, PP, #0x53, lsl #12  ; [pp+0x53820] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdfe00: add             x16, x16, #0x820
    // 0xbdfe04: ldp             x5, lr, [x16]
    // 0xbdfe08: blr             lr
    // 0xbdfe0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbdfe0c: ldur            w1, [x0, #0x17]
    // 0xbdfe10: DecompressPointer r1
    //     0xbdfe10: add             x1, x1, HEAP, lsl #32
    // 0xbdfe14: ldur            x0, [fp, #-0x38]
    // 0xbdfe18: LoadField: r2 = r0->field_27
    //     0xbdfe18: ldur            w2, [x0, #0x27]
    // 0xbdfe1c: DecompressPointer r2
    //     0xbdfe1c: add             x2, x2, HEAP, lsl #32
    // 0xbdfe20: ldur            x16, [fp, #-0x30]
    // 0xbdfe24: stp             x16, x2, [SP, #0x30]
    // 0xbdfe28: ldur            x16, [fp, #-0x28]
    // 0xbdfe2c: ldur            lr, [fp, #-0x20]
    // 0xbdfe30: stp             lr, x16, [SP, #0x20]
    // 0xbdfe34: ldur            x16, [fp, #-0x18]
    // 0xbdfe38: ldur            lr, [fp, #-0x10]
    // 0xbdfe3c: stp             lr, x16, [SP, #0x10]
    // 0xbdfe40: ldur            x16, [fp, #-8]
    // 0xbdfe44: stp             x1, x16, [SP]
    // 0xbdfe48: r4 = 0
    //     0xbdfe48: movz            x4, #0
    // 0xbdfe4c: ldr             x0, [SP, #0x38]
    // 0xbdfe50: r16 = UnlinkedCall_0x613b5c
    //     0xbdfe50: add             x16, PP, #0x53, lsl #12  ; [pp+0x53830] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdfe54: add             x16, x16, #0x830
    // 0xbdfe58: ldp             x5, lr, [x16]
    // 0xbdfe5c: blr             lr
    // 0xbdfe60: r0 = Null
    //     0xbdfe60: mov             x0, NULL
    // 0xbdfe64: LeaveFrame
    //     0xbdfe64: mov             SP, fp
    //     0xbdfe68: ldp             fp, lr, [SP], #0x10
    // 0xbdfe6c: ret
    //     0xbdfe6c: ret             
    // 0xbdfe70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdfe70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdfe74: b               #0xbdfd7c
    // 0xbdfe78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdfe78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbdfe7c, size: 0x1f0
    // 0xbdfe7c: EnterFrame
    //     0xbdfe7c: stp             fp, lr, [SP, #-0x10]!
    //     0xbdfe80: mov             fp, SP
    // 0xbdfe84: AllocStack(0x28)
    //     0xbdfe84: sub             SP, SP, #0x28
    // 0xbdfe88: SetupParameters()
    //     0xbdfe88: ldr             x0, [fp, #0x10]
    //     0xbdfe8c: ldur            w1, [x0, #0x17]
    //     0xbdfe90: add             x1, x1, HEAP, lsl #32
    //     0xbdfe94: stur            x1, [fp, #-8]
    // 0xbdfe98: CheckStackOverflow
    //     0xbdfe98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdfe9c: cmp             SP, x16
    //     0xbdfea0: b.ls            #0xbe005c
    // 0xbdfea4: LoadField: r0 = r1->field_13
    //     0xbdfea4: ldur            w0, [x1, #0x13]
    // 0xbdfea8: DecompressPointer r0
    //     0xbdfea8: add             x0, x0, HEAP, lsl #32
    // 0xbdfeac: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbdfeac: ldur            w2, [x1, #0x17]
    // 0xbdfeb0: DecompressPointer r2
    //     0xbdfeb0: add             x2, x2, HEAP, lsl #32
    // 0xbdfeb4: stp             x2, x0, [SP]
    // 0xbdfeb8: r4 = 0
    //     0xbdfeb8: movz            x4, #0
    // 0xbdfebc: ldr             x0, [SP, #8]
    // 0xbdfec0: r16 = UnlinkedCall_0x613b5c
    //     0xbdfec0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53840] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdfec4: add             x16, x16, #0x840
    // 0xbdfec8: ldp             x5, lr, [x16]
    // 0xbdfecc: blr             lr
    // 0xbdfed0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbdfed0: ldur            w1, [x0, #0x17]
    // 0xbdfed4: DecompressPointer r1
    //     0xbdfed4: add             x1, x1, HEAP, lsl #32
    // 0xbdfed8: cmp             w1, NULL
    // 0xbdfedc: b.eq            #0xbe0064
    // 0xbdfee0: LoadField: r0 = r1->field_7
    //     0xbdfee0: ldur            w0, [x1, #7]
    // 0xbdfee4: cbz             w0, #0xbe004c
    // 0xbdfee8: ldur            x0, [fp, #-8]
    // 0xbdfeec: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbdfeec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbdfef0: ldr             x0, [x0, #0x1c80]
    //     0xbdfef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbdfef8: cmp             w0, w16
    //     0xbdfefc: b.ne            #0xbdff08
    //     0xbdff00: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbdff04: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbdff08: r1 = Null
    //     0xbdff08: mov             x1, NULL
    // 0xbdff0c: r2 = 12
    //     0xbdff0c: movz            x2, #0xc
    // 0xbdff10: r0 = AllocateArray()
    //     0xbdff10: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdff14: stur            x0, [fp, #-0x10]
    // 0xbdff18: r16 = "link"
    //     0xbdff18: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0xbdff1c: StoreField: r0->field_f = r16
    //     0xbdff1c: stur            w16, [x0, #0xf]
    // 0xbdff20: ldur            x1, [fp, #-8]
    // 0xbdff24: LoadField: r2 = r1->field_13
    //     0xbdff24: ldur            w2, [x1, #0x13]
    // 0xbdff28: DecompressPointer r2
    //     0xbdff28: add             x2, x2, HEAP, lsl #32
    // 0xbdff2c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbdff2c: ldur            w3, [x1, #0x17]
    // 0xbdff30: DecompressPointer r3
    //     0xbdff30: add             x3, x3, HEAP, lsl #32
    // 0xbdff34: stp             x3, x2, [SP]
    // 0xbdff38: r4 = 0
    //     0xbdff38: movz            x4, #0
    // 0xbdff3c: ldr             x0, [SP, #8]
    // 0xbdff40: r16 = UnlinkedCall_0x613b5c
    //     0xbdff40: add             x16, PP, #0x53, lsl #12  ; [pp+0x53850] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdff44: add             x16, x16, #0x850
    // 0xbdff48: ldp             x5, lr, [x16]
    // 0xbdff4c: blr             lr
    // 0xbdff50: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbdff50: ldur            w1, [x0, #0x17]
    // 0xbdff54: DecompressPointer r1
    //     0xbdff54: add             x1, x1, HEAP, lsl #32
    // 0xbdff58: str             x1, [SP]
    // 0xbdff5c: r0 = _interpolateSingle()
    //     0xbdff5c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbdff60: ldur            x1, [fp, #-0x10]
    // 0xbdff64: ArrayStore: r1[1] = r0  ; List_4
    //     0xbdff64: add             x25, x1, #0x13
    //     0xbdff68: str             w0, [x25]
    //     0xbdff6c: tbz             w0, #0, #0xbdff88
    //     0xbdff70: ldurb           w16, [x1, #-1]
    //     0xbdff74: ldurb           w17, [x0, #-1]
    //     0xbdff78: and             x16, x17, x16, lsr #2
    //     0xbdff7c: tst             x16, HEAP, lsr #32
    //     0xbdff80: b.eq            #0xbdff88
    //     0xbdff84: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbdff88: ldur            x2, [fp, #-0x10]
    // 0xbdff8c: r16 = "previousScreenSource"
    //     0xbdff8c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbdff90: ldr             x16, [x16, #0x448]
    // 0xbdff94: ArrayStore: r2[0] = r16  ; List_4
    //     0xbdff94: stur            w16, [x2, #0x17]
    // 0xbdff98: ldur            x0, [fp, #-8]
    // 0xbdff9c: LoadField: r1 = r0->field_f
    //     0xbdff9c: ldur            w1, [x0, #0xf]
    // 0xbdffa0: DecompressPointer r1
    //     0xbdffa0: add             x1, x1, HEAP, lsl #32
    // 0xbdffa4: LoadField: r3 = r1->field_b
    //     0xbdffa4: ldur            w3, [x1, #0xb]
    // 0xbdffa8: DecompressPointer r3
    //     0xbdffa8: add             x3, x3, HEAP, lsl #32
    // 0xbdffac: cmp             w3, NULL
    // 0xbdffb0: b.eq            #0xbe0068
    // 0xbdffb4: LoadField: r0 = r3->field_23
    //     0xbdffb4: ldur            w0, [x3, #0x23]
    // 0xbdffb8: DecompressPointer r0
    //     0xbdffb8: add             x0, x0, HEAP, lsl #32
    // 0xbdffbc: mov             x1, x2
    // 0xbdffc0: ArrayStore: r1[3] = r0  ; List_4
    //     0xbdffc0: add             x25, x1, #0x1b
    //     0xbdffc4: str             w0, [x25]
    //     0xbdffc8: tbz             w0, #0, #0xbdffe4
    //     0xbdffcc: ldurb           w16, [x1, #-1]
    //     0xbdffd0: ldurb           w17, [x0, #-1]
    //     0xbdffd4: and             x16, x17, x16, lsr #2
    //     0xbdffd8: tst             x16, HEAP, lsr #32
    //     0xbdffdc: b.eq            #0xbdffe4
    //     0xbdffe0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbdffe4: r16 = "screenSource"
    //     0xbdffe4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0xbdffe8: ldr             x16, [x16, #0x450]
    // 0xbdffec: StoreField: r2->field_1f = r16
    //     0xbdffec: stur            w16, [x2, #0x1f]
    // 0xbdfff0: LoadField: r0 = r3->field_1f
    //     0xbdfff0: ldur            w0, [x3, #0x1f]
    // 0xbdfff4: DecompressPointer r0
    //     0xbdfff4: add             x0, x0, HEAP, lsl #32
    // 0xbdfff8: mov             x1, x2
    // 0xbdfffc: ArrayStore: r1[5] = r0  ; List_4
    //     0xbdfffc: add             x25, x1, #0x23
    //     0xbe0000: str             w0, [x25]
    //     0xbe0004: tbz             w0, #0, #0xbe0020
    //     0xbe0008: ldurb           w16, [x1, #-1]
    //     0xbe000c: ldurb           w17, [x0, #-1]
    //     0xbe0010: and             x16, x17, x16, lsr #2
    //     0xbe0014: tst             x16, HEAP, lsr #32
    //     0xbe0018: b.eq            #0xbe0020
    //     0xbe001c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe0020: r16 = <String, String?>
    //     0xbe0020: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0xbe0024: ldr             x16, [x16, #0x3c8]
    // 0xbe0028: stp             x2, x16, [SP]
    // 0xbe002c: r0 = Map._fromLiteral()
    //     0xbe002c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe0030: r16 = "/collection"
    //     0xbe0030: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0xbe0034: ldr             x16, [x16, #0x458]
    // 0xbe0038: stp             x16, NULL, [SP, #8]
    // 0xbe003c: str             x0, [SP]
    // 0xbe0040: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe0040: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe0044: ldr             x4, [x4, #0x438]
    // 0xbe0048: r0 = GetNavigation.toNamed()
    //     0xbe0048: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe004c: r0 = Null
    //     0xbe004c: mov             x0, NULL
    // 0xbe0050: LeaveFrame
    //     0xbe0050: mov             SP, fp
    //     0xbe0054: ldp             fp, lr, [SP], #0x10
    // 0xbe0058: ret
    //     0xbe0058: ret             
    // 0xbe005c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe005c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe0060: b               #0xbdfea4
    // 0xbe0064: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbe0064: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xbe0068: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe0068: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbe006c, size: 0x84
    // 0xbe006c: EnterFrame
    //     0xbe006c: stp             fp, lr, [SP, #-0x10]!
    //     0xbe0070: mov             fp, SP
    // 0xbe0074: AllocStack(0x10)
    //     0xbe0074: sub             SP, SP, #0x10
    // 0xbe0078: SetupParameters()
    //     0xbe0078: ldr             x0, [fp, #0x18]
    //     0xbe007c: ldur            w1, [x0, #0x17]
    //     0xbe0080: add             x1, x1, HEAP, lsl #32
    //     0xbe0084: stur            x1, [fp, #-8]
    // 0xbe0088: CheckStackOverflow
    //     0xbe0088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe008c: cmp             SP, x16
    //     0xbe0090: b.ls            #0xbe00e8
    // 0xbe0094: r1 = 1
    //     0xbe0094: movz            x1, #0x1
    // 0xbe0098: r0 = AllocateContext()
    //     0xbe0098: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe009c: mov             x1, x0
    // 0xbe00a0: ldur            x0, [fp, #-8]
    // 0xbe00a4: StoreField: r1->field_b = r0
    //     0xbe00a4: stur            w0, [x1, #0xb]
    // 0xbe00a8: ldr             x2, [fp, #0x10]
    // 0xbe00ac: StoreField: r1->field_f = r2
    //     0xbe00ac: stur            w2, [x1, #0xf]
    // 0xbe00b0: LoadField: r3 = r0->field_f
    //     0xbe00b0: ldur            w3, [x0, #0xf]
    // 0xbe00b4: DecompressPointer r3
    //     0xbe00b4: add             x3, x3, HEAP, lsl #32
    // 0xbe00b8: mov             x2, x1
    // 0xbe00bc: stur            x3, [fp, #-0x10]
    // 0xbe00c0: r1 = Function '<anonymous closure>':.
    //     0xbe00c0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53860] AnonymousClosure: (0x98e9d8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/testimonial_carousal.dart] _TestimonialCarouselState::build (0xc0f5ac)
    //     0xbe00c4: ldr             x1, [x1, #0x860]
    // 0xbe00c8: r0 = AllocateClosure()
    //     0xbe00c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe00cc: ldur            x1, [fp, #-0x10]
    // 0xbe00d0: mov             x2, x0
    // 0xbe00d4: r0 = setState()
    //     0xbe00d4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbe00d8: r0 = Null
    //     0xbe00d8: mov             x0, NULL
    // 0xbe00dc: LeaveFrame
    //     0xbe00dc: mov             SP, fp
    //     0xbe00e0: ldp             fp, lr, [SP], #0x10
    // 0xbe00e4: ret
    //     0xbe00e4: ret             
    // 0xbe00e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe00e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe00ec: b               #0xbe0094
  }
}

// class id: 3995, size: 0x30, field offset: 0xc
//   const constructor, 
class CollectionPosterCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80a04, size: 0x30
    // 0xc80a04: EnterFrame
    //     0xc80a04: stp             fp, lr, [SP, #-0x10]!
    //     0xc80a08: mov             fp, SP
    // 0xc80a0c: mov             x0, x1
    // 0xc80a10: r1 = <CollectionPosterCarousel>
    //     0xc80a10: add             x1, PP, #0x48, lsl #12  ; [pp+0x48420] TypeArguments: <CollectionPosterCarousel>
    //     0xc80a14: ldr             x1, [x1, #0x420]
    // 0xc80a18: r0 = _CollectionPosterCarouselState()
    //     0xc80a18: bl              #0xc80a34  ; Allocate_CollectionPosterCarouselStateStub -> _CollectionPosterCarouselState (size=0x20)
    // 0xc80a1c: r1 = Sentinel
    //     0xc80a1c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80a20: StoreField: r0->field_13 = r1
    //     0xc80a20: stur            w1, [x0, #0x13]
    // 0xc80a24: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc80a24: stur            xzr, [x0, #0x17]
    // 0xc80a28: LeaveFrame
    //     0xc80a28: mov             SP, fp
    //     0xc80a2c: ldp             fp, lr, [SP], #0x10
    // 0xc80a30: ret
    //     0xc80a30: ret             
  }
}
