// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart

// class id: 1049488, size: 0x8
class :: {
}

// class id: 3277, size: 0x28, field offset: 0x14
class _CheckoutNumberWidgetState extends State<dynamic> {

  late final TextEditingController _numberController; // offset: 0x14

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x805fa0, size: 0x180
    // 0x805fa0: EnterFrame
    //     0x805fa0: stp             fp, lr, [SP, #-0x10]!
    //     0x805fa4: mov             fp, SP
    // 0x805fa8: AllocStack(0x20)
    //     0x805fa8: sub             SP, SP, #0x20
    // 0x805fac: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x805fac: mov             x4, x1
    //     0x805fb0: mov             x3, x2
    //     0x805fb4: stur            x1, [fp, #-8]
    //     0x805fb8: stur            x2, [fp, #-0x10]
    // 0x805fbc: CheckStackOverflow
    //     0x805fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x805fc0: cmp             SP, x16
    //     0x805fc4: b.ls            #0x806100
    // 0x805fc8: mov             x0, x3
    // 0x805fcc: r2 = Null
    //     0x805fcc: mov             x2, NULL
    // 0x805fd0: r1 = Null
    //     0x805fd0: mov             x1, NULL
    // 0x805fd4: r4 = 60
    //     0x805fd4: movz            x4, #0x3c
    // 0x805fd8: branchIfSmi(r0, 0x805fe4)
    //     0x805fd8: tbz             w0, #0, #0x805fe4
    // 0x805fdc: r4 = LoadClassIdInstr(r0)
    //     0x805fdc: ldur            x4, [x0, #-1]
    //     0x805fe0: ubfx            x4, x4, #0xc, #0x14
    // 0x805fe4: cmp             x4, #0xfb4
    // 0x805fe8: b.eq            #0x806000
    // 0x805fec: r8 = CheckoutNumberWidget
    //     0x805fec: add             x8, PP, #0x53, lsl #12  ; [pp+0x53fe8] Type: CheckoutNumberWidget
    //     0x805ff0: ldr             x8, [x8, #0xfe8]
    // 0x805ff4: r3 = Null
    //     0x805ff4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53ff0] Null
    //     0x805ff8: ldr             x3, [x3, #0xff0]
    // 0x805ffc: r0 = CheckoutNumberWidget()
    //     0x805ffc: bl              #0x806294  ; IsType_CheckoutNumberWidget_Stub
    // 0x806000: ldur            x3, [fp, #-8]
    // 0x806004: LoadField: r2 = r3->field_7
    //     0x806004: ldur            w2, [x3, #7]
    // 0x806008: DecompressPointer r2
    //     0x806008: add             x2, x2, HEAP, lsl #32
    // 0x80600c: ldur            x0, [fp, #-0x10]
    // 0x806010: r1 = Null
    //     0x806010: mov             x1, NULL
    // 0x806014: cmp             w2, NULL
    // 0x806018: b.eq            #0x80603c
    // 0x80601c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x80601c: ldur            w4, [x2, #0x17]
    // 0x806020: DecompressPointer r4
    //     0x806020: add             x4, x4, HEAP, lsl #32
    // 0x806024: r8 = X0 bound StatefulWidget
    //     0x806024: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x806028: ldr             x8, [x8, #0x7a0]
    // 0x80602c: LoadField: r9 = r4->field_7
    //     0x80602c: ldur            x9, [x4, #7]
    // 0x806030: r3 = Null
    //     0x806030: add             x3, PP, #0x54, lsl #12  ; [pp+0x54000] Null
    //     0x806034: ldr             x3, [x3]
    // 0x806038: blr             x9
    // 0x80603c: ldur            x1, [fp, #-8]
    // 0x806040: LoadField: r0 = r1->field_b
    //     0x806040: ldur            w0, [x1, #0xb]
    // 0x806044: DecompressPointer r0
    //     0x806044: add             x0, x0, HEAP, lsl #32
    // 0x806048: cmp             w0, NULL
    // 0x80604c: b.eq            #0x806108
    // 0x806050: LoadField: r2 = r0->field_13
    //     0x806050: ldur            w2, [x0, #0x13]
    // 0x806054: DecompressPointer r2
    //     0x806054: add             x2, x2, HEAP, lsl #32
    // 0x806058: ldur            x0, [fp, #-0x10]
    // 0x80605c: LoadField: r3 = r0->field_13
    //     0x80605c: ldur            w3, [x0, #0x13]
    // 0x806060: DecompressPointer r3
    //     0x806060: add             x3, x3, HEAP, lsl #32
    // 0x806064: r0 = LoadClassIdInstr(r2)
    //     0x806064: ldur            x0, [x2, #-1]
    //     0x806068: ubfx            x0, x0, #0xc, #0x14
    // 0x80606c: stp             x3, x2, [SP]
    // 0x806070: mov             lr, x0
    // 0x806074: ldr             lr, [x21, lr, lsl #3]
    // 0x806078: blr             lr
    // 0x80607c: tbz             w0, #4, #0x8060f0
    // 0x806080: ldur            x0, [fp, #-8]
    // 0x806084: LoadField: r1 = r0->field_13
    //     0x806084: ldur            w1, [x0, #0x13]
    // 0x806088: DecompressPointer r1
    //     0x806088: add             x1, x1, HEAP, lsl #32
    // 0x80608c: r16 = Sentinel
    //     0x80608c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806090: cmp             w1, w16
    // 0x806094: b.eq            #0x80610c
    // 0x806098: LoadField: r2 = r0->field_b
    //     0x806098: ldur            w2, [x0, #0xb]
    // 0x80609c: DecompressPointer r2
    //     0x80609c: add             x2, x2, HEAP, lsl #32
    // 0x8060a0: cmp             w2, NULL
    // 0x8060a4: b.eq            #0x806118
    // 0x8060a8: LoadField: r3 = r2->field_13
    //     0x8060a8: ldur            w3, [x2, #0x13]
    // 0x8060ac: DecompressPointer r3
    //     0x8060ac: add             x3, x3, HEAP, lsl #32
    // 0x8060b0: mov             x2, x3
    // 0x8060b4: r0 = text=()
    //     0x8060b4: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0x8060b8: ldur            x1, [fp, #-8]
    // 0x8060bc: LoadField: r0 = r1->field_b
    //     0x8060bc: ldur            w0, [x1, #0xb]
    // 0x8060c0: DecompressPointer r0
    //     0x8060c0: add             x0, x0, HEAP, lsl #32
    // 0x8060c4: cmp             w0, NULL
    // 0x8060c8: b.eq            #0x80611c
    // 0x8060cc: LoadField: r2 = r0->field_13
    //     0x8060cc: ldur            w2, [x0, #0x13]
    // 0x8060d0: DecompressPointer r2
    //     0x8060d0: add             x2, x2, HEAP, lsl #32
    // 0x8060d4: LoadField: r0 = r2->field_7
    //     0x8060d4: ldur            w0, [x2, #7]
    // 0x8060d8: cbz             w0, #0x8060e4
    // 0x8060dc: r2 = false
    //     0x8060dc: add             x2, NULL, #0x30  ; false
    // 0x8060e0: b               #0x8060e8
    // 0x8060e4: r2 = true
    //     0x8060e4: add             x2, NULL, #0x20  ; true
    // 0x8060e8: StoreField: r1->field_23 = r2
    //     0x8060e8: stur            w2, [x1, #0x23]
    // 0x8060ec: r0 = _initializeFromExistingNumber()
    //     0x8060ec: bl              #0x806120  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_initializeFromExistingNumber
    // 0x8060f0: r0 = Null
    //     0x8060f0: mov             x0, NULL
    // 0x8060f4: LeaveFrame
    //     0x8060f4: mov             SP, fp
    //     0x8060f8: ldp             fp, lr, [SP], #0x10
    // 0x8060fc: ret
    //     0x8060fc: ret             
    // 0x806100: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806100: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806104: b               #0x805fc8
    // 0x806108: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806108: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x80610c: r9 = _numberController
    //     0x80610c: add             x9, PP, #0x53, lsl #12  ; [pp+0x53fd8] Field <_CheckoutNumberWidgetState@1672254242._numberController@1672254242>: late final (offset: 0x14)
    //     0x806110: ldr             x9, [x9, #0xfd8]
    // 0x806114: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806114: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806118: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806118: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x80611c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80611c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _initializeFromExistingNumber(/* No info */) {
    // ** addr: 0x806120, size: 0xc4
    // 0x806120: EnterFrame
    //     0x806120: stp             fp, lr, [SP, #-0x10]!
    //     0x806124: mov             fp, SP
    // 0x806128: AllocStack(0x10)
    //     0x806128: sub             SP, SP, #0x10
    // 0x80612c: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r0, fp-0x8 */)
    //     0x80612c: mov             x0, x1
    //     0x806130: stur            x1, [fp, #-8]
    // 0x806134: CheckStackOverflow
    //     0x806134: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806138: cmp             SP, x16
    //     0x80613c: b.ls            #0x8061cc
    // 0x806140: LoadField: r1 = r0->field_13
    //     0x806140: ldur            w1, [x0, #0x13]
    // 0x806144: DecompressPointer r1
    //     0x806144: add             x1, x1, HEAP, lsl #32
    // 0x806148: r16 = Sentinel
    //     0x806148: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80614c: cmp             w1, w16
    // 0x806150: b.eq            #0x8061d4
    // 0x806154: LoadField: r2 = r1->field_27
    //     0x806154: ldur            w2, [x1, #0x27]
    // 0x806158: DecompressPointer r2
    //     0x806158: add             x2, x2, HEAP, lsl #32
    // 0x80615c: LoadField: r1 = r2->field_7
    //     0x80615c: ldur            w1, [x2, #7]
    // 0x806160: DecompressPointer r1
    //     0x806160: add             x1, x1, HEAP, lsl #32
    // 0x806164: LoadField: r2 = r1->field_7
    //     0x806164: ldur            w2, [x1, #7]
    // 0x806168: cbz             w2, #0x8061b4
    // 0x80616c: r2 = true
    //     0x80616c: add             x2, NULL, #0x20  ; true
    // 0x806170: StoreField: r0->field_1f = r2
    //     0x806170: stur            w2, [x0, #0x1f]
    // 0x806174: mov             x2, x1
    // 0x806178: mov             x1, x0
    // 0x80617c: r0 = _isValidPhoneNumber()
    //     0x80617c: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x806180: ldur            x1, [fp, #-8]
    // 0x806184: StoreField: r1->field_1b = r0
    //     0x806184: stur            w0, [x1, #0x1b]
    // 0x806188: LoadField: r0 = r1->field_b
    //     0x806188: ldur            w0, [x1, #0xb]
    // 0x80618c: DecompressPointer r0
    //     0x80618c: add             x0, x0, HEAP, lsl #32
    // 0x806190: cmp             w0, NULL
    // 0x806194: b.eq            #0x8061e0
    // 0x806198: LoadField: r2 = r0->field_f
    //     0x806198: ldur            w2, [x0, #0xf]
    // 0x80619c: DecompressPointer r2
    //     0x80619c: add             x2, x2, HEAP, lsl #32
    // 0x8061a0: str             x2, [SP]
    // 0x8061a4: mov             x0, x2
    // 0x8061a8: ClosureCall
    //     0x8061a8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8061ac: ldur            x2, [x0, #0x1f]
    //     0x8061b0: blr             x2
    // 0x8061b4: ldur            x1, [fp, #-8]
    // 0x8061b8: r0 = _notifyParent()
    //     0x8061b8: bl              #0x8061e4  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_notifyParent
    // 0x8061bc: r0 = Null
    //     0x8061bc: mov             x0, NULL
    // 0x8061c0: LeaveFrame
    //     0x8061c0: mov             SP, fp
    //     0x8061c4: ldp             fp, lr, [SP], #0x10
    // 0x8061c8: ret
    //     0x8061c8: ret             
    // 0x8061cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8061cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8061d0: b               #0x806140
    // 0x8061d4: r9 = _numberController
    //     0x8061d4: add             x9, PP, #0x53, lsl #12  ; [pp+0x53fd8] Field <_CheckoutNumberWidgetState@1672254242._numberController@1672254242>: late final (offset: 0x14)
    //     0x8061d8: ldr             x9, [x9, #0xfd8]
    // 0x8061dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8061dc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8061e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8061e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _notifyParent(/* No info */) {
    // ** addr: 0x8061e4, size: 0xb0
    // 0x8061e4: EnterFrame
    //     0x8061e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8061e8: mov             fp, SP
    // 0x8061ec: AllocStack(0x28)
    //     0x8061ec: sub             SP, SP, #0x28
    // 0x8061f0: CheckStackOverflow
    //     0x8061f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8061f4: cmp             SP, x16
    //     0x8061f8: b.ls            #0x80627c
    // 0x8061fc: LoadField: r0 = r1->field_b
    //     0x8061fc: ldur            w0, [x1, #0xb]
    // 0x806200: DecompressPointer r0
    //     0x806200: add             x0, x0, HEAP, lsl #32
    // 0x806204: stur            x0, [fp, #-0x10]
    // 0x806208: cmp             w0, NULL
    // 0x80620c: b.eq            #0x806284
    // 0x806210: LoadField: r2 = r1->field_13
    //     0x806210: ldur            w2, [x1, #0x13]
    // 0x806214: DecompressPointer r2
    //     0x806214: add             x2, x2, HEAP, lsl #32
    // 0x806218: r16 = Sentinel
    //     0x806218: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80621c: cmp             w2, w16
    // 0x806220: b.eq            #0x806288
    // 0x806224: LoadField: r3 = r2->field_27
    //     0x806224: ldur            w3, [x2, #0x27]
    // 0x806228: DecompressPointer r3
    //     0x806228: add             x3, x3, HEAP, lsl #32
    // 0x80622c: LoadField: r4 = r3->field_7
    //     0x80622c: ldur            w4, [x3, #7]
    // 0x806230: DecompressPointer r4
    //     0x806230: add             x4, x4, HEAP, lsl #32
    // 0x806234: mov             x2, x4
    // 0x806238: stur            x4, [fp, #-8]
    // 0x80623c: r0 = _isValidPhoneNumber()
    //     0x80623c: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x806240: mov             x1, x0
    // 0x806244: ldur            x0, [fp, #-0x10]
    // 0x806248: LoadField: r2 = r0->field_b
    //     0x806248: ldur            w2, [x0, #0xb]
    // 0x80624c: DecompressPointer r2
    //     0x80624c: add             x2, x2, HEAP, lsl #32
    // 0x806250: ldur            x16, [fp, #-8]
    // 0x806254: stp             x16, x2, [SP, #8]
    // 0x806258: str             x1, [SP]
    // 0x80625c: mov             x0, x2
    // 0x806260: ClosureCall
    //     0x806260: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x806264: ldur            x2, [x0, #0x1f]
    //     0x806268: blr             x2
    // 0x80626c: r0 = Null
    //     0x80626c: mov             x0, NULL
    // 0x806270: LeaveFrame
    //     0x806270: mov             SP, fp
    //     0x806274: ldp             fp, lr, [SP], #0x10
    // 0x806278: ret
    //     0x806278: ret             
    // 0x80627c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x80627c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806280: b               #0x8061fc
    // 0x806284: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806284: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806288: r9 = _numberController
    //     0x806288: add             x9, PP, #0x53, lsl #12  ; [pp+0x53fd8] Field <_CheckoutNumberWidgetState@1672254242._numberController@1672254242>: late final (offset: 0x14)
    //     0x80628c: ldr             x9, [x9, #0xfd8]
    // 0x806290: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806290: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x947d54, size: 0x100
    // 0x947d54: EnterFrame
    //     0x947d54: stp             fp, lr, [SP, #-0x10]!
    //     0x947d58: mov             fp, SP
    // 0x947d5c: AllocStack(0x20)
    //     0x947d5c: sub             SP, SP, #0x20
    // 0x947d60: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r0, fp-0x10 */)
    //     0x947d60: mov             x0, x1
    //     0x947d64: stur            x1, [fp, #-0x10]
    // 0x947d68: CheckStackOverflow
    //     0x947d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x947d6c: cmp             SP, x16
    //     0x947d70: b.ls            #0x947e44
    // 0x947d74: LoadField: r1 = r0->field_b
    //     0x947d74: ldur            w1, [x0, #0xb]
    // 0x947d78: DecompressPointer r1
    //     0x947d78: add             x1, x1, HEAP, lsl #32
    // 0x947d7c: cmp             w1, NULL
    // 0x947d80: b.eq            #0x947e4c
    // 0x947d84: LoadField: r2 = r1->field_13
    //     0x947d84: ldur            w2, [x1, #0x13]
    // 0x947d88: DecompressPointer r2
    //     0x947d88: add             x2, x2, HEAP, lsl #32
    // 0x947d8c: stur            x2, [fp, #-8]
    // 0x947d90: r1 = <TextEditingValue>
    //     0x947d90: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0x947d94: r0 = TextEditingController()
    //     0x947d94: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x947d98: stur            x0, [fp, #-0x18]
    // 0x947d9c: ldur            x16, [fp, #-8]
    // 0x947da0: str             x16, [SP]
    // 0x947da4: mov             x1, x0
    // 0x947da8: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0x947da8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0x947dac: ldr             x4, [x4, #0xc40]
    // 0x947db0: r0 = TextEditingController()
    //     0x947db0: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x947db4: ldur            x1, [fp, #-0x10]
    // 0x947db8: LoadField: r0 = r1->field_13
    //     0x947db8: ldur            w0, [x1, #0x13]
    // 0x947dbc: DecompressPointer r0
    //     0x947dbc: add             x0, x0, HEAP, lsl #32
    // 0x947dc0: r16 = Sentinel
    //     0x947dc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x947dc4: cmp             w0, w16
    // 0x947dc8: b.ne            #0x947e30
    // 0x947dcc: ldur            x0, [fp, #-0x18]
    // 0x947dd0: StoreField: r1->field_13 = r0
    //     0x947dd0: stur            w0, [x1, #0x13]
    //     0x947dd4: ldurb           w16, [x1, #-1]
    //     0x947dd8: ldurb           w17, [x0, #-1]
    //     0x947ddc: and             x16, x17, x16, lsr #2
    //     0x947de0: tst             x16, HEAP, lsr #32
    //     0x947de4: b.eq            #0x947dec
    //     0x947de8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x947dec: LoadField: r0 = r1->field_b
    //     0x947dec: ldur            w0, [x1, #0xb]
    // 0x947df0: DecompressPointer r0
    //     0x947df0: add             x0, x0, HEAP, lsl #32
    // 0x947df4: cmp             w0, NULL
    // 0x947df8: b.eq            #0x947e50
    // 0x947dfc: LoadField: r2 = r0->field_13
    //     0x947dfc: ldur            w2, [x0, #0x13]
    // 0x947e00: DecompressPointer r2
    //     0x947e00: add             x2, x2, HEAP, lsl #32
    // 0x947e04: LoadField: r0 = r2->field_7
    //     0x947e04: ldur            w0, [x2, #7]
    // 0x947e08: cbz             w0, #0x947e14
    // 0x947e0c: r2 = false
    //     0x947e0c: add             x2, NULL, #0x30  ; false
    // 0x947e10: b               #0x947e18
    // 0x947e14: r2 = true
    //     0x947e14: add             x2, NULL, #0x20  ; true
    // 0x947e18: StoreField: r1->field_23 = r2
    //     0x947e18: stur            w2, [x1, #0x23]
    // 0x947e1c: r0 = _initializeFromExistingNumber()
    //     0x947e1c: bl              #0x806120  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_initializeFromExistingNumber
    // 0x947e20: r0 = Null
    //     0x947e20: mov             x0, NULL
    // 0x947e24: LeaveFrame
    //     0x947e24: mov             SP, fp
    //     0x947e28: ldp             fp, lr, [SP], #0x10
    // 0x947e2c: ret
    //     0x947e2c: ret             
    // 0x947e30: r16 = "_numberController@1672254242"
    //     0x947e30: add             x16, PP, #0x54, lsl #12  ; [pp+0x54010] "_numberController@1672254242"
    //     0x947e34: ldr             x16, [x16, #0x10]
    // 0x947e38: str             x16, [SP]
    // 0x947e3c: r0 = _throwFieldAlreadyInitialized()
    //     0x947e3c: bl              #0x6388d8  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x947e40: brk             #0
    // 0x947e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x947e44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x947e48: b               #0x947d74
    // 0x947e4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947e4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x947e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x947e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa08dbc, size: 0x24
    // 0xa08dbc: ldr             x1, [SP]
    // 0xa08dc0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa08dc0: ldur            w2, [x1, #0x17]
    // 0xa08dc4: DecompressPointer r2
    //     0xa08dc4: add             x2, x2, HEAP, lsl #32
    // 0xa08dc8: LoadField: r1 = r2->field_f
    //     0xa08dc8: ldur            w1, [x2, #0xf]
    // 0xa08dcc: DecompressPointer r1
    //     0xa08dcc: add             x1, x1, HEAP, lsl #32
    // 0xa08dd0: LoadField: r0 = r2->field_13
    //     0xa08dd0: ldur            w0, [x2, #0x13]
    // 0xa08dd4: DecompressPointer r0
    //     0xa08dd4: add             x0, x0, HEAP, lsl #32
    // 0xa08dd8: StoreField: r1->field_1b = r0
    //     0xa08dd8: stur            w0, [x1, #0x1b]
    // 0xa08ddc: ret
    //     0xa08ddc: ret             
  }
  _ _handlePhoneNumberChanged(/* No info */) {
    // ** addr: 0xa08de0, size: 0xe4
    // 0xa08de0: EnterFrame
    //     0xa08de0: stp             fp, lr, [SP, #-0x10]!
    //     0xa08de4: mov             fp, SP
    // 0xa08de8: AllocStack(0x20)
    //     0xa08de8: sub             SP, SP, #0x20
    // 0xa08dec: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa08dec: stur            x1, [fp, #-8]
    //     0xa08df0: stur            x2, [fp, #-0x10]
    // 0xa08df4: CheckStackOverflow
    //     0xa08df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa08df8: cmp             SP, x16
    //     0xa08dfc: b.ls            #0xa08eb8
    // 0xa08e00: r1 = 2
    //     0xa08e00: movz            x1, #0x2
    // 0xa08e04: r0 = AllocateContext()
    //     0xa08e04: bl              #0x16f6108  ; AllocateContextStub
    // 0xa08e08: mov             x3, x0
    // 0xa08e0c: ldur            x0, [fp, #-8]
    // 0xa08e10: stur            x3, [fp, #-0x18]
    // 0xa08e14: StoreField: r3->field_f = r0
    //     0xa08e14: stur            w0, [x3, #0xf]
    // 0xa08e18: r1 = true
    //     0xa08e18: add             x1, NULL, #0x20  ; true
    // 0xa08e1c: StoreField: r0->field_1f = r1
    //     0xa08e1c: stur            w1, [x0, #0x1f]
    // 0xa08e20: mov             x1, x0
    // 0xa08e24: ldur            x2, [fp, #-0x10]
    // 0xa08e28: r0 = _isValidPhoneNumber()
    //     0xa08e28: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0xa08e2c: ldur            x2, [fp, #-0x18]
    // 0xa08e30: StoreField: r2->field_13 = r0
    //     0xa08e30: stur            w0, [x2, #0x13]
    // 0xa08e34: ldur            x3, [fp, #-8]
    // 0xa08e38: LoadField: r1 = r3->field_1b
    //     0xa08e38: ldur            w1, [x3, #0x1b]
    // 0xa08e3c: DecompressPointer r1
    //     0xa08e3c: add             x1, x1, HEAP, lsl #32
    // 0xa08e40: cmp             w1, w0
    // 0xa08e44: b.eq            #0xa08e60
    // 0xa08e48: r1 = Function '<anonymous closure>':.
    //     0xa08e48: add             x1, PP, #0x53, lsl #12  ; [pp+0x53fe0] AnonymousClosure: (0xa08dbc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_handlePhoneNumberChanged (0xa08de0)
    //     0xa08e4c: ldr             x1, [x1, #0xfe0]
    // 0xa08e50: r0 = AllocateClosure()
    //     0xa08e50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa08e54: ldur            x1, [fp, #-8]
    // 0xa08e58: mov             x2, x0
    // 0xa08e5c: r0 = setState()
    //     0xa08e5c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa08e60: ldur            x0, [fp, #-0x10]
    // 0xa08e64: ldur            x1, [fp, #-8]
    // 0xa08e68: r0 = _notifyParent()
    //     0xa08e68: bl              #0x8061e4  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_notifyParent
    // 0xa08e6c: ldur            x0, [fp, #-0x10]
    // 0xa08e70: LoadField: r1 = r0->field_7
    //     0xa08e70: ldur            w1, [x0, #7]
    // 0xa08e74: cmp             w1, #2
    // 0xa08e78: b.ne            #0xa08ea8
    // 0xa08e7c: ldur            x0, [fp, #-8]
    // 0xa08e80: LoadField: r1 = r0->field_b
    //     0xa08e80: ldur            w1, [x0, #0xb]
    // 0xa08e84: DecompressPointer r1
    //     0xa08e84: add             x1, x1, HEAP, lsl #32
    // 0xa08e88: cmp             w1, NULL
    // 0xa08e8c: b.eq            #0xa08ec0
    // 0xa08e90: LoadField: r0 = r1->field_f
    //     0xa08e90: ldur            w0, [x1, #0xf]
    // 0xa08e94: DecompressPointer r0
    //     0xa08e94: add             x0, x0, HEAP, lsl #32
    // 0xa08e98: str             x0, [SP]
    // 0xa08e9c: ClosureCall
    //     0xa08e9c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xa08ea0: ldur            x2, [x0, #0x1f]
    //     0xa08ea4: blr             x2
    // 0xa08ea8: r0 = Null
    //     0xa08ea8: mov             x0, NULL
    // 0xa08eac: LeaveFrame
    //     0xa08eac: mov             SP, fp
    //     0xa08eb0: ldp             fp, lr, [SP], #0x10
    // 0xa08eb4: ret
    //     0xa08eb4: ret             
    // 0xa08eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa08eb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa08ebc: b               #0xa08e00
    // 0xa08ec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa08ec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _handlePhoneNumberChanged(dynamic, String) {
    // ** addr: 0xa08ec4, size: 0x3c
    // 0xa08ec4: EnterFrame
    //     0xa08ec4: stp             fp, lr, [SP, #-0x10]!
    //     0xa08ec8: mov             fp, SP
    // 0xa08ecc: ldr             x0, [fp, #0x18]
    // 0xa08ed0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa08ed0: ldur            w1, [x0, #0x17]
    // 0xa08ed4: DecompressPointer r1
    //     0xa08ed4: add             x1, x1, HEAP, lsl #32
    // 0xa08ed8: CheckStackOverflow
    //     0xa08ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa08edc: cmp             SP, x16
    //     0xa08ee0: b.ls            #0xa08ef8
    // 0xa08ee4: ldr             x2, [fp, #0x10]
    // 0xa08ee8: r0 = _handlePhoneNumberChanged()
    //     0xa08ee8: bl              #0xa08de0  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_handlePhoneNumberChanged
    // 0xa08eec: LeaveFrame
    //     0xa08eec: mov             SP, fp
    //     0xa08ef0: ldp             fp, lr, [SP], #0x10
    // 0xa08ef4: ret
    //     0xa08ef4: ret             
    // 0xa08ef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa08ef8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa08efc: b               #0xa08ee4
  }
  _ build(/* No info */) {
    // ** addr: 0xbb58c0, size: 0x358
    // 0xbb58c0: EnterFrame
    //     0xbb58c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbb58c4: mov             fp, SP
    // 0xbb58c8: AllocStack(0xb0)
    //     0xbb58c8: sub             SP, SP, #0xb0
    // 0xbb58cc: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbb58cc: mov             x0, x2
    //     0xbb58d0: stur            x2, [fp, #-0x10]
    //     0xbb58d4: mov             x2, x1
    //     0xbb58d8: stur            x1, [fp, #-8]
    // 0xbb58dc: CheckStackOverflow
    //     0xbb58dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb58e0: cmp             SP, x16
    //     0xbb58e4: b.ls            #0xbb5c00
    // 0xbb58e8: mov             x1, x0
    // 0xbb58ec: r0 = of()
    //     0xbb58ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb58f0: stur            x0, [fp, #-0x20]
    // 0xbb58f4: LoadField: r1 = r0->field_87
    //     0xbb58f4: ldur            w1, [x0, #0x87]
    // 0xbb58f8: DecompressPointer r1
    //     0xbb58f8: add             x1, x1, HEAP, lsl #32
    // 0xbb58fc: LoadField: r2 = r1->field_2b
    //     0xbb58fc: ldur            w2, [x1, #0x2b]
    // 0xbb5900: DecompressPointer r2
    //     0xbb5900: add             x2, x2, HEAP, lsl #32
    // 0xbb5904: stur            x2, [fp, #-0x18]
    // 0xbb5908: r16 = 12.000000
    //     0xbb5908: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb590c: ldr             x16, [x16, #0x9e8]
    // 0xbb5910: r30 = Instance_Color
    //     0xbb5910: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5914: stp             lr, x16, [SP]
    // 0xbb5918: mov             x1, x2
    // 0xbb591c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb591c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb5920: ldr             x4, [x4, #0xaa0]
    // 0xbb5924: r0 = copyWith()
    //     0xbb5924: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb5928: stur            x0, [fp, #-0x28]
    // 0xbb592c: r0 = Text()
    //     0xbb592c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb5930: mov             x1, x0
    // 0xbb5934: r0 = "Enter WhatsApp no.*"
    //     0xbb5934: add             x0, PP, #0x53, lsl #12  ; [pp+0x53fb8] "Enter WhatsApp no.*"
    //     0xbb5938: ldr             x0, [x0, #0xfb8]
    // 0xbb593c: stur            x1, [fp, #-0x30]
    // 0xbb5940: StoreField: r1->field_b = r0
    //     0xbb5940: stur            w0, [x1, #0xb]
    // 0xbb5944: ldur            x0, [fp, #-0x28]
    // 0xbb5948: StoreField: r1->field_13 = r0
    //     0xbb5948: stur            w0, [x1, #0x13]
    // 0xbb594c: r0 = Padding()
    //     0xbb594c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb5950: mov             x1, x0
    // 0xbb5954: r0 = Instance_EdgeInsets
    //     0xbb5954: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xbb5958: ldr             x0, [x0, #0x4c0]
    // 0xbb595c: stur            x1, [fp, #-0x40]
    // 0xbb5960: StoreField: r1->field_f = r0
    //     0xbb5960: stur            w0, [x1, #0xf]
    // 0xbb5964: ldur            x0, [fp, #-0x30]
    // 0xbb5968: StoreField: r1->field_b = r0
    //     0xbb5968: stur            w0, [x1, #0xb]
    // 0xbb596c: ldur            x0, [fp, #-8]
    // 0xbb5970: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbb5970: ldur            w2, [x0, #0x17]
    // 0xbb5974: DecompressPointer r2
    //     0xbb5974: add             x2, x2, HEAP, lsl #32
    // 0xbb5978: stur            x2, [fp, #-0x38]
    // 0xbb597c: LoadField: r3 = r0->field_b
    //     0xbb597c: ldur            w3, [x0, #0xb]
    // 0xbb5980: DecompressPointer r3
    //     0xbb5980: add             x3, x3, HEAP, lsl #32
    // 0xbb5984: cmp             w3, NULL
    // 0xbb5988: b.eq            #0xbb5c08
    // 0xbb598c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xbb598c: ldur            w4, [x3, #0x17]
    // 0xbb5990: DecompressPointer r4
    //     0xbb5990: add             x4, x4, HEAP, lsl #32
    // 0xbb5994: stur            x4, [fp, #-0x30]
    // 0xbb5998: LoadField: r3 = r0->field_23
    //     0xbb5998: ldur            w3, [x0, #0x23]
    // 0xbb599c: DecompressPointer r3
    //     0xbb599c: add             x3, x3, HEAP, lsl #32
    // 0xbb59a0: stur            x3, [fp, #-0x28]
    // 0xbb59a4: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbb59a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbb59a8: ldr             x0, [x0, #0x1530]
    //     0xbb59ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbb59b0: cmp             w0, w16
    //     0xbb59b4: b.ne            #0xbb59c4
    //     0xbb59b8: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbb59bc: ldr             x2, [x2, #0x120]
    //     0xbb59c0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbb59c4: stur            x0, [fp, #-0x48]
    // 0xbb59c8: r0 = LengthLimitingTextInputFormatter()
    //     0xbb59c8: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbb59cc: mov             x3, x0
    // 0xbb59d0: r0 = 20
    //     0xbb59d0: movz            x0, #0x14
    // 0xbb59d4: stur            x3, [fp, #-0x50]
    // 0xbb59d8: StoreField: r3->field_7 = r0
    //     0xbb59d8: stur            w0, [x3, #7]
    // 0xbb59dc: r1 = Null
    //     0xbb59dc: mov             x1, NULL
    // 0xbb59e0: r2 = 4
    //     0xbb59e0: movz            x2, #0x4
    // 0xbb59e4: r0 = AllocateArray()
    //     0xbb59e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb59e8: mov             x2, x0
    // 0xbb59ec: ldur            x0, [fp, #-0x48]
    // 0xbb59f0: stur            x2, [fp, #-0x58]
    // 0xbb59f4: StoreField: r2->field_f = r0
    //     0xbb59f4: stur            w0, [x2, #0xf]
    // 0xbb59f8: ldur            x0, [fp, #-0x50]
    // 0xbb59fc: StoreField: r2->field_13 = r0
    //     0xbb59fc: stur            w0, [x2, #0x13]
    // 0xbb5a00: r1 = <TextInputFormatter>
    //     0xbb5a00: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbb5a04: ldr             x1, [x1, #0x7b0]
    // 0xbb5a08: r0 = AllocateGrowableArray()
    //     0xbb5a08: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb5a0c: mov             x2, x0
    // 0xbb5a10: ldur            x0, [fp, #-0x58]
    // 0xbb5a14: stur            x2, [fp, #-0x48]
    // 0xbb5a18: StoreField: r2->field_f = r0
    //     0xbb5a18: stur            w0, [x2, #0xf]
    // 0xbb5a1c: r0 = 4
    //     0xbb5a1c: movz            x0, #0x4
    // 0xbb5a20: StoreField: r2->field_b = r0
    //     0xbb5a20: stur            w0, [x2, #0xb]
    // 0xbb5a24: ldur            x1, [fp, #-0x20]
    // 0xbb5a28: LoadField: r3 = r1->field_5b
    //     0xbb5a28: ldur            w3, [x1, #0x5b]
    // 0xbb5a2c: DecompressPointer r3
    //     0xbb5a2c: add             x3, x3, HEAP, lsl #32
    // 0xbb5a30: r16 = 14.000000
    //     0xbb5a30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb5a34: ldr             x16, [x16, #0x1d8]
    // 0xbb5a38: stp             x3, x16, [SP]
    // 0xbb5a3c: ldur            x1, [fp, #-0x18]
    // 0xbb5a40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb5a40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb5a44: ldr             x4, [x4, #0xaa0]
    // 0xbb5a48: r0 = copyWith()
    //     0xbb5a48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb5a4c: mov             x3, x0
    // 0xbb5a50: ldur            x0, [fp, #-8]
    // 0xbb5a54: stur            x3, [fp, #-0x20]
    // 0xbb5a58: LoadField: r4 = r0->field_13
    //     0xbb5a58: ldur            w4, [x0, #0x13]
    // 0xbb5a5c: DecompressPointer r4
    //     0xbb5a5c: add             x4, x4, HEAP, lsl #32
    // 0xbb5a60: r16 = Sentinel
    //     0xbb5a60: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbb5a64: cmp             w4, w16
    // 0xbb5a68: b.eq            #0xbb5c0c
    // 0xbb5a6c: mov             x1, x0
    // 0xbb5a70: ldur            x2, [fp, #-0x10]
    // 0xbb5a74: stur            x4, [fp, #-0x18]
    // 0xbb5a78: r0 = _buildInputDecoration()
    //     0xbb5a78: bl              #0xbb5c18  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_buildInputDecoration
    // 0xbb5a7c: ldur            x2, [fp, #-8]
    // 0xbb5a80: r1 = Function '_validatePhoneNumber@1672254242':.
    //     0xbb5a80: add             x1, PP, #0x53, lsl #12  ; [pp+0x53fc0] AnonymousClosure: (0xbb5e10), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber (0xa08f3c)
    //     0xbb5a84: ldr             x1, [x1, #0xfc0]
    // 0xbb5a88: stur            x0, [fp, #-0x10]
    // 0xbb5a8c: r0 = AllocateClosure()
    //     0xbb5a8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb5a90: ldur            x2, [fp, #-8]
    // 0xbb5a94: r1 = Function '_handlePhoneNumberChanged@1672254242':.
    //     0xbb5a94: add             x1, PP, #0x53, lsl #12  ; [pp+0x53fc8] AnonymousClosure: (0xa08ec4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_handlePhoneNumberChanged (0xa08de0)
    //     0xbb5a98: ldr             x1, [x1, #0xfc8]
    // 0xbb5a9c: stur            x0, [fp, #-8]
    // 0xbb5aa0: r0 = AllocateClosure()
    //     0xbb5aa0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb5aa4: r1 = <String>
    //     0xbb5aa4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbb5aa8: stur            x0, [fp, #-0x50]
    // 0xbb5aac: r0 = TextFormField()
    //     0xbb5aac: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbb5ab0: stur            x0, [fp, #-0x58]
    // 0xbb5ab4: ldur            x16, [fp, #-0x30]
    // 0xbb5ab8: ldur            lr, [fp, #-8]
    // 0xbb5abc: stp             lr, x16, [SP, #0x48]
    // 0xbb5ac0: r16 = true
    //     0xbb5ac0: add             x16, NULL, #0x20  ; true
    // 0xbb5ac4: ldur            lr, [fp, #-0x28]
    // 0xbb5ac8: stp             lr, x16, [SP, #0x38]
    // 0xbb5acc: r16 = Instance_AutovalidateMode
    //     0xbb5acc: add             x16, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbb5ad0: ldr             x16, [x16, #0x7e8]
    // 0xbb5ad4: ldur            lr, [fp, #-0x48]
    // 0xbb5ad8: stp             lr, x16, [SP, #0x28]
    // 0xbb5adc: r16 = Instance_TextInputType
    //     0xbb5adc: add             x16, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbb5ae0: ldr             x16, [x16, #0x1a0]
    // 0xbb5ae4: ldur            lr, [fp, #-0x20]
    // 0xbb5ae8: stp             lr, x16, [SP, #0x18]
    // 0xbb5aec: r16 = 2
    //     0xbb5aec: movz            x16, #0x2
    // 0xbb5af0: ldur            lr, [fp, #-0x18]
    // 0xbb5af4: stp             lr, x16, [SP, #8]
    // 0xbb5af8: ldur            x16, [fp, #-0x50]
    // 0xbb5afc: str             x16, [SP]
    // 0xbb5b00: mov             x1, x0
    // 0xbb5b04: ldur            x2, [fp, #-0x10]
    // 0xbb5b08: r4 = const [0, 0xd, 0xb, 0x2, autofocus, 0x5, autovalidateMode, 0x6, controller, 0xb, enableSuggestions, 0x4, inputFormatters, 0x7, keyboardType, 0x8, maxLines, 0xa, onChanged, 0xc, readOnly, 0x2, style, 0x9, validator, 0x3, null]
    //     0xbb5b08: add             x4, PP, #0x53, lsl #12  ; [pp+0x53fd0] List(27) [0, 0xd, 0xb, 0x2, "autofocus", 0x5, "autovalidateMode", 0x6, "controller", 0xb, "enableSuggestions", 0x4, "inputFormatters", 0x7, "keyboardType", 0x8, "maxLines", 0xa, "onChanged", 0xc, "readOnly", 0x2, "style", 0x9, "validator", 0x3, Null]
    //     0xbb5b0c: ldr             x4, [x4, #0xfd0]
    // 0xbb5b10: r0 = TextFormField()
    //     0xbb5b10: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbb5b14: r0 = Form()
    //     0xbb5b14: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbb5b18: mov             x3, x0
    // 0xbb5b1c: ldur            x0, [fp, #-0x58]
    // 0xbb5b20: stur            x3, [fp, #-8]
    // 0xbb5b24: StoreField: r3->field_b = r0
    //     0xbb5b24: stur            w0, [x3, #0xb]
    // 0xbb5b28: r0 = Instance_AutovalidateMode
    //     0xbb5b28: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbb5b2c: ldr             x0, [x0, #0x800]
    // 0xbb5b30: StoreField: r3->field_23 = r0
    //     0xbb5b30: stur            w0, [x3, #0x23]
    // 0xbb5b34: ldur            x0, [fp, #-0x38]
    // 0xbb5b38: StoreField: r3->field_7 = r0
    //     0xbb5b38: stur            w0, [x3, #7]
    // 0xbb5b3c: r1 = Null
    //     0xbb5b3c: mov             x1, NULL
    // 0xbb5b40: r2 = 4
    //     0xbb5b40: movz            x2, #0x4
    // 0xbb5b44: r0 = AllocateArray()
    //     0xbb5b44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb5b48: mov             x2, x0
    // 0xbb5b4c: ldur            x0, [fp, #-0x40]
    // 0xbb5b50: stur            x2, [fp, #-0x10]
    // 0xbb5b54: StoreField: r2->field_f = r0
    //     0xbb5b54: stur            w0, [x2, #0xf]
    // 0xbb5b58: ldur            x0, [fp, #-8]
    // 0xbb5b5c: StoreField: r2->field_13 = r0
    //     0xbb5b5c: stur            w0, [x2, #0x13]
    // 0xbb5b60: r1 = <Widget>
    //     0xbb5b60: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb5b64: r0 = AllocateGrowableArray()
    //     0xbb5b64: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb5b68: mov             x1, x0
    // 0xbb5b6c: ldur            x0, [fp, #-0x10]
    // 0xbb5b70: stur            x1, [fp, #-8]
    // 0xbb5b74: StoreField: r1->field_f = r0
    //     0xbb5b74: stur            w0, [x1, #0xf]
    // 0xbb5b78: r0 = 4
    //     0xbb5b78: movz            x0, #0x4
    // 0xbb5b7c: StoreField: r1->field_b = r0
    //     0xbb5b7c: stur            w0, [x1, #0xb]
    // 0xbb5b80: r0 = Column()
    //     0xbb5b80: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb5b84: mov             x1, x0
    // 0xbb5b88: r0 = Instance_Axis
    //     0xbb5b88: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb5b8c: stur            x1, [fp, #-0x10]
    // 0xbb5b90: StoreField: r1->field_f = r0
    //     0xbb5b90: stur            w0, [x1, #0xf]
    // 0xbb5b94: r0 = Instance_MainAxisAlignment
    //     0xbb5b94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb5b98: ldr             x0, [x0, #0xa08]
    // 0xbb5b9c: StoreField: r1->field_13 = r0
    //     0xbb5b9c: stur            w0, [x1, #0x13]
    // 0xbb5ba0: r0 = Instance_MainAxisSize
    //     0xbb5ba0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb5ba4: ldr             x0, [x0, #0xa10]
    // 0xbb5ba8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbb5ba8: stur            w0, [x1, #0x17]
    // 0xbb5bac: r0 = Instance_CrossAxisAlignment
    //     0xbb5bac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb5bb0: ldr             x0, [x0, #0x890]
    // 0xbb5bb4: StoreField: r1->field_1b = r0
    //     0xbb5bb4: stur            w0, [x1, #0x1b]
    // 0xbb5bb8: r0 = Instance_VerticalDirection
    //     0xbb5bb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb5bbc: ldr             x0, [x0, #0xa20]
    // 0xbb5bc0: StoreField: r1->field_23 = r0
    //     0xbb5bc0: stur            w0, [x1, #0x23]
    // 0xbb5bc4: r0 = Instance_Clip
    //     0xbb5bc4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb5bc8: ldr             x0, [x0, #0x38]
    // 0xbb5bcc: StoreField: r1->field_2b = r0
    //     0xbb5bcc: stur            w0, [x1, #0x2b]
    // 0xbb5bd0: StoreField: r1->field_2f = rZR
    //     0xbb5bd0: stur            xzr, [x1, #0x2f]
    // 0xbb5bd4: ldur            x0, [fp, #-8]
    // 0xbb5bd8: StoreField: r1->field_b = r0
    //     0xbb5bd8: stur            w0, [x1, #0xb]
    // 0xbb5bdc: r0 = Padding()
    //     0xbb5bdc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb5be0: r1 = Instance_EdgeInsets
    //     0xbb5be0: add             x1, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xbb5be4: ldr             x1, [x1, #0x50]
    // 0xbb5be8: StoreField: r0->field_f = r1
    //     0xbb5be8: stur            w1, [x0, #0xf]
    // 0xbb5bec: ldur            x1, [fp, #-0x10]
    // 0xbb5bf0: StoreField: r0->field_b = r1
    //     0xbb5bf0: stur            w1, [x0, #0xb]
    // 0xbb5bf4: LeaveFrame
    //     0xbb5bf4: mov             SP, fp
    //     0xbb5bf8: ldp             fp, lr, [SP], #0x10
    // 0xbb5bfc: ret
    //     0xbb5bfc: ret             
    // 0xbb5c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb5c00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb5c04: b               #0xbb58e8
    // 0xbb5c08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbb5c08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbb5c0c: r9 = _numberController
    //     0xbb5c0c: add             x9, PP, #0x53, lsl #12  ; [pp+0x53fd8] Field <_CheckoutNumberWidgetState@1672254242._numberController@1672254242>: late final (offset: 0x14)
    //     0xbb5c10: ldr             x9, [x9, #0xfd8]
    // 0xbb5c14: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbb5c14: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildInputDecoration(/* No info */) {
    // ** addr: 0xbb5c18, size: 0x1f8
    // 0xbb5c18: EnterFrame
    //     0xbb5c18: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5c1c: mov             fp, SP
    // 0xbb5c20: AllocStack(0x78)
    //     0xbb5c20: sub             SP, SP, #0x78
    // 0xbb5c24: SetupParameters(_CheckoutNumberWidgetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbb5c24: mov             x0, x2
    //     0xbb5c28: stur            x2, [fp, #-0x10]
    //     0xbb5c2c: mov             x2, x1
    //     0xbb5c30: stur            x1, [fp, #-8]
    // 0xbb5c34: CheckStackOverflow
    //     0xbb5c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb5c38: cmp             SP, x16
    //     0xbb5c3c: b.ls            #0xbb5e08
    // 0xbb5c40: mov             x1, x0
    // 0xbb5c44: r0 = of()
    //     0xbb5c44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb5c48: ldur            x1, [fp, #-0x10]
    // 0xbb5c4c: stur            x0, [fp, #-0x10]
    // 0xbb5c50: r0 = getTextFormFieldInputDecoration()
    //     0xbb5c50: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbb5c54: ldur            x1, [fp, #-8]
    // 0xbb5c58: ldur            x2, [fp, #-0x10]
    // 0xbb5c5c: stur            x0, [fp, #-0x18]
    // 0xbb5c60: r0 = _buildPrefixIcon()
    //     0xbb5c60: bl              #0xa08aec  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_buildPrefixIcon
    // 0xbb5c64: mov             x1, x0
    // 0xbb5c68: ldur            x0, [fp, #-8]
    // 0xbb5c6c: stur            x1, [fp, #-0x28]
    // 0xbb5c70: LoadField: r2 = r0->field_1f
    //     0xbb5c70: ldur            w2, [x0, #0x1f]
    // 0xbb5c74: DecompressPointer r2
    //     0xbb5c74: add             x2, x2, HEAP, lsl #32
    // 0xbb5c78: tbnz            w2, #4, #0xbb5cdc
    // 0xbb5c7c: LoadField: r2 = r0->field_1b
    //     0xbb5c7c: ldur            w2, [x0, #0x1b]
    // 0xbb5c80: DecompressPointer r2
    //     0xbb5c80: add             x2, x2, HEAP, lsl #32
    // 0xbb5c84: tbnz            w2, #4, #0xbb5c94
    // 0xbb5c88: r0 = Instance_IconData
    //     0xbb5c88: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbb5c8c: ldr             x0, [x0, #0x130]
    // 0xbb5c90: b               #0xbb5c9c
    // 0xbb5c94: r0 = Instance_IconData
    //     0xbb5c94: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbb5c98: ldr             x0, [x0, #0x138]
    // 0xbb5c9c: stur            x0, [fp, #-0x20]
    // 0xbb5ca0: tbnz            w2, #4, #0xbb5cb0
    // 0xbb5ca4: r2 = Instance_Color
    //     0xbb5ca4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbb5ca8: ldr             x2, [x2, #0x858]
    // 0xbb5cac: b               #0xbb5cb8
    // 0xbb5cb0: r2 = Instance_Color
    //     0xbb5cb0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbb5cb4: ldr             x2, [x2, #0x50]
    // 0xbb5cb8: stur            x2, [fp, #-8]
    // 0xbb5cbc: r0 = Icon()
    //     0xbb5cbc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbb5cc0: mov             x1, x0
    // 0xbb5cc4: ldur            x0, [fp, #-0x20]
    // 0xbb5cc8: StoreField: r1->field_b = r0
    //     0xbb5cc8: stur            w0, [x1, #0xb]
    // 0xbb5ccc: ldur            x0, [fp, #-8]
    // 0xbb5cd0: StoreField: r1->field_23 = r0
    //     0xbb5cd0: stur            w0, [x1, #0x23]
    // 0xbb5cd4: mov             x2, x1
    // 0xbb5cd8: b               #0xbb5ce0
    // 0xbb5cdc: r2 = Null
    //     0xbb5cdc: mov             x2, NULL
    // 0xbb5ce0: ldur            x0, [fp, #-0x10]
    // 0xbb5ce4: stur            x2, [fp, #-0x20]
    // 0xbb5ce8: LoadField: r1 = r0->field_87
    //     0xbb5ce8: ldur            w1, [x0, #0x87]
    // 0xbb5cec: DecompressPointer r1
    //     0xbb5cec: add             x1, x1, HEAP, lsl #32
    // 0xbb5cf0: LoadField: r3 = r1->field_2b
    //     0xbb5cf0: ldur            w3, [x1, #0x2b]
    // 0xbb5cf4: DecompressPointer r3
    //     0xbb5cf4: add             x3, x3, HEAP, lsl #32
    // 0xbb5cf8: stur            x3, [fp, #-8]
    // 0xbb5cfc: r1 = Instance_Color
    //     0xbb5cfc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb5d00: d0 = 0.400000
    //     0xbb5d00: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbb5d04: r0 = withOpacity()
    //     0xbb5d04: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb5d08: r16 = 14.000000
    //     0xbb5d08: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbb5d0c: ldr             x16, [x16, #0x1d8]
    // 0xbb5d10: stp             x0, x16, [SP]
    // 0xbb5d14: ldur            x1, [fp, #-8]
    // 0xbb5d18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb5d18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb5d1c: ldr             x4, [x4, #0xaa0]
    // 0xbb5d20: r0 = copyWith()
    //     0xbb5d20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb5d24: stur            x0, [fp, #-0x30]
    // 0xbb5d28: r16 = 12.000000
    //     0xbb5d28: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb5d2c: ldr             x16, [x16, #0x9e8]
    // 0xbb5d30: r30 = Instance_MaterialColor
    //     0xbb5d30: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbb5d34: ldr             lr, [lr, #0x180]
    // 0xbb5d38: stp             lr, x16, [SP]
    // 0xbb5d3c: ldur            x1, [fp, #-8]
    // 0xbb5d40: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb5d40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb5d44: ldr             x4, [x4, #0xaa0]
    // 0xbb5d48: r0 = copyWith()
    //     0xbb5d48: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb5d4c: mov             x1, x0
    // 0xbb5d50: ldur            x0, [fp, #-0x10]
    // 0xbb5d54: stur            x1, [fp, #-0x38]
    // 0xbb5d58: LoadField: r2 = r0->field_5b
    //     0xbb5d58: ldur            w2, [x0, #0x5b]
    // 0xbb5d5c: DecompressPointer r2
    //     0xbb5d5c: add             x2, x2, HEAP, lsl #32
    // 0xbb5d60: stur            x2, [fp, #-8]
    // 0xbb5d64: r0 = BorderSide()
    //     0xbb5d64: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbb5d68: mov             x1, x0
    // 0xbb5d6c: ldur            x0, [fp, #-8]
    // 0xbb5d70: stur            x1, [fp, #-0x10]
    // 0xbb5d74: StoreField: r1->field_7 = r0
    //     0xbb5d74: stur            w0, [x1, #7]
    // 0xbb5d78: d0 = 1.000000
    //     0xbb5d78: fmov            d0, #1.00000000
    // 0xbb5d7c: StoreField: r1->field_b = d0
    //     0xbb5d7c: stur            d0, [x1, #0xb]
    // 0xbb5d80: r0 = Instance_BorderStyle
    //     0xbb5d80: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbb5d84: ldr             x0, [x0, #0xf68]
    // 0xbb5d88: StoreField: r1->field_13 = r0
    //     0xbb5d88: stur            w0, [x1, #0x13]
    // 0xbb5d8c: d0 = -1.000000
    //     0xbb5d8c: fmov            d0, #-1.00000000
    // 0xbb5d90: ArrayStore: r1[0] = d0  ; List_8
    //     0xbb5d90: stur            d0, [x1, #0x17]
    // 0xbb5d94: r0 = OutlineInputBorder()
    //     0xbb5d94: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbb5d98: mov             x1, x0
    // 0xbb5d9c: r0 = Instance_BorderRadius
    //     0xbb5d9c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbb5da0: ldr             x0, [x0, #0xf70]
    // 0xbb5da4: StoreField: r1->field_13 = r0
    //     0xbb5da4: stur            w0, [x1, #0x13]
    // 0xbb5da8: d0 = 4.000000
    //     0xbb5da8: fmov            d0, #4.00000000
    // 0xbb5dac: StoreField: r1->field_b = d0
    //     0xbb5dac: stur            d0, [x1, #0xb]
    // 0xbb5db0: ldur            x0, [fp, #-0x10]
    // 0xbb5db4: StoreField: r1->field_7 = r0
    //     0xbb5db4: stur            w0, [x1, #7]
    // 0xbb5db8: ldur            x16, [fp, #-0x28]
    // 0xbb5dbc: ldur            lr, [fp, #-0x20]
    // 0xbb5dc0: stp             lr, x16, [SP, #0x30]
    // 0xbb5dc4: r16 = Instance_EdgeInsets
    //     0xbb5dc4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbb5dc8: ldr             x16, [x16, #0xc40]
    // 0xbb5dcc: r30 = "Enter WhatsApp no."
    //     0xbb5dcc: add             lr, PP, #0x53, lsl #12  ; [pp+0x53ec8] "Enter WhatsApp no."
    //     0xbb5dd0: ldr             lr, [lr, #0xec8]
    // 0xbb5dd4: stp             lr, x16, [SP, #0x20]
    // 0xbb5dd8: ldur            x16, [fp, #-0x30]
    // 0xbb5ddc: ldur            lr, [fp, #-0x38]
    // 0xbb5de0: stp             lr, x16, [SP, #0x10]
    // 0xbb5de4: r16 = 4
    //     0xbb5de4: movz            x16, #0x4
    // 0xbb5de8: stp             x1, x16, [SP]
    // 0xbb5dec: ldur            x1, [fp, #-0x18]
    // 0xbb5df0: r4 = const [0, 0x9, 0x8, 0x1, contentPadding, 0x3, errorMaxLines, 0x7, errorStyle, 0x6, focusedBorder, 0x8, hintStyle, 0x5, hintText, 0x4, prefixIcon, 0x1, suffixIcon, 0x2, null]
    //     0xbb5df0: add             x4, PP, #0x53, lsl #12  ; [pp+0x53ed0] List(21) [0, 0x9, 0x8, 0x1, "contentPadding", 0x3, "errorMaxLines", 0x7, "errorStyle", 0x6, "focusedBorder", 0x8, "hintStyle", 0x5, "hintText", 0x4, "prefixIcon", 0x1, "suffixIcon", 0x2, Null]
    //     0xbb5df4: ldr             x4, [x4, #0xed0]
    // 0xbb5df8: r0 = copyWith()
    //     0xbb5df8: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbb5dfc: LeaveFrame
    //     0xbb5dfc: mov             SP, fp
    //     0xbb5e00: ldp             fp, lr, [SP], #0x10
    // 0xbb5e04: ret
    //     0xbb5e04: ret             
    // 0xbb5e08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb5e08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb5e0c: b               #0xbb5c40
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0xbb5e10, size: 0x3c
    // 0xbb5e10: EnterFrame
    //     0xbb5e10: stp             fp, lr, [SP, #-0x10]!
    //     0xbb5e14: mov             fp, SP
    // 0xbb5e18: ldr             x0, [fp, #0x18]
    // 0xbb5e1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbb5e1c: ldur            w1, [x0, #0x17]
    // 0xbb5e20: DecompressPointer r1
    //     0xbb5e20: add             x1, x1, HEAP, lsl #32
    // 0xbb5e24: CheckStackOverflow
    //     0xbb5e24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb5e28: cmp             SP, x16
    //     0xbb5e2c: b.ls            #0xbb5e44
    // 0xbb5e30: ldr             x2, [fp, #0x10]
    // 0xbb5e34: r0 = _validatePhoneNumber()
    //     0xbb5e34: bl              #0xa08f3c  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber
    // 0xbb5e38: LeaveFrame
    //     0xbb5e38: mov             SP, fp
    //     0xbb5e3c: ldp             fp, lr, [SP], #0x10
    // 0xbb5e40: ret
    //     0xbb5e40: ret             
    // 0xbb5e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbb5e44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbb5e48: b               #0xbb5e30
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87fa4, size: 0x54
    // 0xc87fa4: EnterFrame
    //     0xc87fa4: stp             fp, lr, [SP, #-0x10]!
    //     0xc87fa8: mov             fp, SP
    // 0xc87fac: CheckStackOverflow
    //     0xc87fac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87fb0: cmp             SP, x16
    //     0xc87fb4: b.ls            #0xc87fe4
    // 0xc87fb8: LoadField: r0 = r1->field_13
    //     0xc87fb8: ldur            w0, [x1, #0x13]
    // 0xc87fbc: DecompressPointer r0
    //     0xc87fbc: add             x0, x0, HEAP, lsl #32
    // 0xc87fc0: r16 = Sentinel
    //     0xc87fc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87fc4: cmp             w0, w16
    // 0xc87fc8: b.eq            #0xc87fec
    // 0xc87fcc: mov             x1, x0
    // 0xc87fd0: r0 = dispose()
    //     0xc87fd0: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87fd4: r0 = Null
    //     0xc87fd4: mov             x0, NULL
    // 0xc87fd8: LeaveFrame
    //     0xc87fd8: mov             SP, fp
    //     0xc87fdc: ldp             fp, lr, [SP], #0x10
    // 0xc87fe0: ret
    //     0xc87fe0: ret             
    // 0xc87fe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87fe4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87fe8: b               #0xc87fb8
    // 0xc87fec: r9 = _numberController
    //     0xc87fec: add             x9, PP, #0x53, lsl #12  ; [pp+0x53fd8] Field <_CheckoutNumberWidgetState@1672254242._numberController@1672254242>: late final (offset: 0x14)
    //     0xc87ff0: ldr             x9, [x9, #0xfd8]
    // 0xc87ff4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87ff4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4020, size: 0x1c, field offset: 0xc
//   const constructor, 
class CheckoutNumberWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8026c, size: 0x60
    // 0xc8026c: EnterFrame
    //     0xc8026c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80270: mov             fp, SP
    // 0xc80274: AllocStack(0x8)
    //     0xc80274: sub             SP, SP, #8
    // 0xc80278: SetupParameters(CheckoutNumberWidget this /* r1 => r0 */)
    //     0xc80278: mov             x0, x1
    // 0xc8027c: r1 = <CheckoutNumberWidget>
    //     0xc8027c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48658] TypeArguments: <CheckoutNumberWidget>
    //     0xc80280: ldr             x1, [x1, #0x658]
    // 0xc80284: r0 = _CheckoutNumberWidgetState()
    //     0xc80284: bl              #0xc802cc  ; Allocate_CheckoutNumberWidgetStateStub -> _CheckoutNumberWidgetState (size=0x28)
    // 0xc80288: mov             x2, x0
    // 0xc8028c: r0 = Sentinel
    //     0xc8028c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80290: stur            x2, [fp, #-8]
    // 0xc80294: StoreField: r2->field_13 = r0
    //     0xc80294: stur            w0, [x2, #0x13]
    // 0xc80298: r0 = false
    //     0xc80298: add             x0, NULL, #0x30  ; false
    // 0xc8029c: StoreField: r2->field_1b = r0
    //     0xc8029c: stur            w0, [x2, #0x1b]
    // 0xc802a0: StoreField: r2->field_1f = r0
    //     0xc802a0: stur            w0, [x2, #0x1f]
    // 0xc802a4: StoreField: r2->field_23 = r0
    //     0xc802a4: stur            w0, [x2, #0x23]
    // 0xc802a8: r1 = <FormState>
    //     0xc802a8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0xc802ac: ldr             x1, [x1, #0xad8]
    // 0xc802b0: r0 = LabeledGlobalKey()
    //     0xc802b0: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xc802b4: mov             x1, x0
    // 0xc802b8: ldur            x0, [fp, #-8]
    // 0xc802bc: ArrayStore: r0[0] = r1  ; List_4
    //     0xc802bc: stur            w1, [x0, #0x17]
    // 0xc802c0: LeaveFrame
    //     0xc802c0: mov             SP, fp
    //     0xc802c4: ldp             fp, lr, [SP], #0x10
    // 0xc802c8: ret
    //     0xc802c8: ret             
  }
}
