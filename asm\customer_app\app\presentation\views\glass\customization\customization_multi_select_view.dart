// lib: , url: package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart

// class id: 1049385, size: 0x8
class :: {
}

// class id: 3347, size: 0x1c, field offset: 0x14
class _CustomizationMultiSelectState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x941d80, size: 0x30
    // 0x941d80: EnterFrame
    //     0x941d80: stp             fp, lr, [SP, #-0x10]!
    //     0x941d84: mov             fp, SP
    // 0x941d88: CheckStackOverflow
    //     0x941d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941d8c: cmp             SP, x16
    //     0x941d90: b.ls            #0x941da8
    // 0x941d94: r0 = _initializeProductCustomisation()
    //     0x941d94: bl              #0x941dd0  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_initializeProductCustomisation
    // 0x941d98: r0 = Null
    //     0x941d98: mov             x0, NULL
    // 0x941d9c: LeaveFrame
    //     0x941d9c: mov             SP, fp
    //     0x941da0: ldp             fp, lr, [SP], #0x10
    // 0x941da4: ret
    //     0x941da4: ret             
    // 0x941da8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941da8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941dac: b               #0x941d94
  }
  _ _initializeProductCustomisation(/* No info */) {
    // ** addr: 0x941dd0, size: 0x1a0
    // 0x941dd0: EnterFrame
    //     0x941dd0: stp             fp, lr, [SP, #-0x10]!
    //     0x941dd4: mov             fp, SP
    // 0x941dd8: AllocStack(0x40)
    //     0x941dd8: sub             SP, SP, #0x40
    // 0x941ddc: SetupParameters(_CustomizationMultiSelectState this /* r1 => r0, fp-0x18 */)
    //     0x941ddc: mov             x0, x1
    //     0x941de0: stur            x1, [fp, #-0x18]
    // 0x941de4: CheckStackOverflow
    //     0x941de4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x941de8: cmp             SP, x16
    //     0x941dec: b.ls            #0x941f60
    // 0x941df0: LoadField: r1 = r0->field_b
    //     0x941df0: ldur            w1, [x0, #0xb]
    // 0x941df4: DecompressPointer r1
    //     0x941df4: add             x1, x1, HEAP, lsl #32
    // 0x941df8: cmp             w1, NULL
    // 0x941dfc: b.eq            #0x941f68
    // 0x941e00: LoadField: r2 = r1->field_b
    //     0x941e00: ldur            w2, [x1, #0xb]
    // 0x941e04: DecompressPointer r2
    //     0x941e04: add             x2, x2, HEAP, lsl #32
    // 0x941e08: cmp             w2, NULL
    // 0x941e0c: b.ne            #0x941e18
    // 0x941e10: r3 = Null
    //     0x941e10: mov             x3, NULL
    // 0x941e14: b               #0x941e24
    // 0x941e18: LoadField: r1 = r2->field_7
    //     0x941e18: ldur            w1, [x2, #7]
    // 0x941e1c: DecompressPointer r1
    //     0x941e1c: add             x1, x1, HEAP, lsl #32
    // 0x941e20: mov             x3, x1
    // 0x941e24: stur            x3, [fp, #-0x10]
    // 0x941e28: cmp             w2, NULL
    // 0x941e2c: b.ne            #0x941e38
    // 0x941e30: r4 = Null
    //     0x941e30: mov             x4, NULL
    // 0x941e34: b               #0x941e44
    // 0x941e38: LoadField: r1 = r2->field_b
    //     0x941e38: ldur            w1, [x2, #0xb]
    // 0x941e3c: DecompressPointer r1
    //     0x941e3c: add             x1, x1, HEAP, lsl #32
    // 0x941e40: mov             x4, x1
    // 0x941e44: stur            x4, [fp, #-8]
    // 0x941e48: r1 = <CustomerResponse>
    //     0x941e48: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0x941e4c: ldr             x1, [x1, #0x5a8]
    // 0x941e50: r2 = 0
    //     0x941e50: movz            x2, #0
    // 0x941e54: r0 = _GrowableList()
    //     0x941e54: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x941e58: mov             x1, x0
    // 0x941e5c: ldur            x0, [fp, #-0x18]
    // 0x941e60: stur            x1, [fp, #-0x30]
    // 0x941e64: LoadField: r2 = r0->field_b
    //     0x941e64: ldur            w2, [x0, #0xb]
    // 0x941e68: DecompressPointer r2
    //     0x941e68: add             x2, x2, HEAP, lsl #32
    // 0x941e6c: stur            x2, [fp, #-0x28]
    // 0x941e70: cmp             w2, NULL
    // 0x941e74: b.eq            #0x941f6c
    // 0x941e78: LoadField: r0 = r2->field_b
    //     0x941e78: ldur            w0, [x2, #0xb]
    // 0x941e7c: DecompressPointer r0
    //     0x941e7c: add             x0, x0, HEAP, lsl #32
    // 0x941e80: cmp             w0, NULL
    // 0x941e84: b.ne            #0x941e90
    // 0x941e88: r3 = Null
    //     0x941e88: mov             x3, NULL
    // 0x941e8c: b               #0x941e98
    // 0x941e90: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x941e90: ldur            w3, [x0, #0x17]
    // 0x941e94: DecompressPointer r3
    //     0x941e94: add             x3, x3, HEAP, lsl #32
    // 0x941e98: stur            x3, [fp, #-0x18]
    // 0x941e9c: cmp             w0, NULL
    // 0x941ea0: b.ne            #0x941eac
    // 0x941ea4: r0 = Null
    //     0x941ea4: mov             x0, NULL
    // 0x941ea8: b               #0x941eb8
    // 0x941eac: LoadField: r4 = r0->field_23
    //     0x941eac: ldur            w4, [x0, #0x23]
    // 0x941eb0: DecompressPointer r4
    //     0x941eb0: add             x4, x4, HEAP, lsl #32
    // 0x941eb4: mov             x0, x4
    // 0x941eb8: cmp             w0, NULL
    // 0x941ebc: b.ne            #0x941ec8
    // 0x941ec0: r5 = 0
    //     0x941ec0: movz            x5, #0
    // 0x941ec4: b               #0x941ed8
    // 0x941ec8: r4 = LoadInt32Instr(r0)
    //     0x941ec8: sbfx            x4, x0, #1, #0x1f
    //     0x941ecc: tbz             w0, #0, #0x941ed4
    //     0x941ed0: ldur            x4, [x0, #7]
    // 0x941ed4: mov             x5, x4
    // 0x941ed8: ldur            x0, [fp, #-0x10]
    // 0x941edc: ldur            x4, [fp, #-8]
    // 0x941ee0: stur            x5, [fp, #-0x20]
    // 0x941ee4: r0 = ProductCustomisation()
    //     0x941ee4: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x941ee8: mov             x2, x0
    // 0x941eec: ldur            x0, [fp, #-0x10]
    // 0x941ef0: StoreField: r2->field_b = r0
    //     0x941ef0: stur            w0, [x2, #0xb]
    // 0x941ef4: ldur            x0, [fp, #-8]
    // 0x941ef8: StoreField: r2->field_f = r0
    //     0x941ef8: stur            w0, [x2, #0xf]
    // 0x941efc: ldur            x0, [fp, #-0x18]
    // 0x941f00: ArrayStore: r2[0] = r0  ; List_4
    //     0x941f00: stur            w0, [x2, #0x17]
    // 0x941f04: ldur            x0, [fp, #-0x30]
    // 0x941f08: StoreField: r2->field_23 = r0
    //     0x941f08: stur            w0, [x2, #0x23]
    // 0x941f0c: ldur            x3, [fp, #-0x20]
    // 0x941f10: r0 = BoxInt64Instr(r3)
    //     0x941f10: sbfiz           x0, x3, #1, #0x1f
    //     0x941f14: cmp             x3, x0, asr #1
    //     0x941f18: b.eq            #0x941f24
    //     0x941f1c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x941f20: stur            x3, [x0, #7]
    // 0x941f24: StoreField: r2->field_27 = r0
    //     0x941f24: stur            w0, [x2, #0x27]
    // 0x941f28: ldur            x0, [fp, #-0x28]
    // 0x941f2c: LoadField: r1 = r0->field_f
    //     0x941f2c: ldur            w1, [x0, #0xf]
    // 0x941f30: DecompressPointer r1
    //     0x941f30: add             x1, x1, HEAP, lsl #32
    // 0x941f34: stp             x2, x1, [SP]
    // 0x941f38: r4 = 0
    //     0x941f38: movz            x4, #0
    // 0x941f3c: ldr             x0, [SP, #8]
    // 0x941f40: r16 = UnlinkedCall_0x613b5c
    //     0x941f40: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a9e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x941f44: add             x16, x16, #0x9e8
    // 0x941f48: ldp             x5, lr, [x16]
    // 0x941f4c: blr             lr
    // 0x941f50: r0 = Null
    //     0x941f50: mov             x0, NULL
    // 0x941f54: LeaveFrame
    //     0x941f54: mov             SP, fp
    //     0x941f58: ldp             fp, lr, [SP], #0x10
    // 0x941f5c: ret
    //     0x941f5c: ret             
    // 0x941f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x941f60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x941f64: b               #0x941df0
    // 0x941f68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941f68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x941f6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x941f6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb5826c, size: 0x184
    // 0xb5826c: EnterFrame
    //     0xb5826c: stp             fp, lr, [SP, #-0x10]!
    //     0xb58270: mov             fp, SP
    // 0xb58274: AllocStack(0x18)
    //     0xb58274: sub             SP, SP, #0x18
    // 0xb58278: SetupParameters(_CustomizationMultiSelectState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xb58278: mov             x3, x1
    //     0xb5827c: mov             x0, x2
    //     0xb58280: stur            x1, [fp, #-8]
    //     0xb58284: stur            x2, [fp, #-0x10]
    // 0xb58288: CheckStackOverflow
    //     0xb58288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5828c: cmp             SP, x16
    //     0xb58290: b.ls            #0xb583e4
    // 0xb58294: LoadField: r1 = r3->field_b
    //     0xb58294: ldur            w1, [x3, #0xb]
    // 0xb58298: DecompressPointer r1
    //     0xb58298: add             x1, x1, HEAP, lsl #32
    // 0xb5829c: cmp             w1, NULL
    // 0xb582a0: b.eq            #0xb583ec
    // 0xb582a4: LoadField: r2 = r1->field_b
    //     0xb582a4: ldur            w2, [x1, #0xb]
    // 0xb582a8: DecompressPointer r2
    //     0xb582a8: add             x2, x2, HEAP, lsl #32
    // 0xb582ac: cmp             w2, NULL
    // 0xb582b0: b.ne            #0xb582bc
    // 0xb582b4: r1 = Null
    //     0xb582b4: mov             x1, NULL
    // 0xb582b8: b               #0xb582c4
    // 0xb582bc: LoadField: r1 = r2->field_2f
    //     0xb582bc: ldur            w1, [x2, #0x2f]
    // 0xb582c0: DecompressPointer r1
    //     0xb582c0: add             x1, x1, HEAP, lsl #32
    // 0xb582c4: cmp             w1, NULL
    // 0xb582c8: b.ne            #0xb582e0
    // 0xb582cc: r1 = <CustomizedEntity>
    //     0xb582cc: add             x1, PP, #0x30, lsl #12  ; [pp+0x30240] TypeArguments: <CustomizedEntity>
    //     0xb582d0: ldr             x1, [x1, #0x240]
    // 0xb582d4: r2 = 0
    //     0xb582d4: movz            x2, #0
    // 0xb582d8: r0 = _GrowableList()
    //     0xb582d8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb582dc: b               #0xb582e4
    // 0xb582e0: mov             x0, x1
    // 0xb582e4: stur            x0, [fp, #-0x18]
    // 0xb582e8: LoadField: r1 = r0->field_b
    //     0xb582e8: ldur            w1, [x0, #0xb]
    // 0xb582ec: cbnz            w1, #0xb58300
    // 0xb582f0: r0 = Instance_SizedBox
    //     0xb582f0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb582f4: LeaveFrame
    //     0xb582f4: mov             SP, fp
    //     0xb582f8: ldp             fp, lr, [SP], #0x10
    // 0xb582fc: ret
    //     0xb582fc: ret             
    // 0xb58300: ldur            x1, [fp, #-8]
    // 0xb58304: ldur            x2, [fp, #-0x10]
    // 0xb58308: r0 = _buildHeader()
    //     0xb58308: bl              #0xadee8c  ; [package:customer_app/app/presentation/views/cosmetic/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildHeader
    // 0xb5830c: ldur            x1, [fp, #-8]
    // 0xb58310: ldur            x2, [fp, #-0x18]
    // 0xb58314: stur            x0, [fp, #-8]
    // 0xb58318: r0 = _buildItemsList()
    //     0xb58318: bl              #0xb583f0  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildItemsList
    // 0xb5831c: stur            x0, [fp, #-0x10]
    // 0xb58320: r0 = Padding()
    //     0xb58320: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb58324: mov             x3, x0
    // 0xb58328: r0 = Instance_EdgeInsets
    //     0xb58328: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb5832c: ldr             x0, [x0, #0x1f0]
    // 0xb58330: stur            x3, [fp, #-0x18]
    // 0xb58334: StoreField: r3->field_f = r0
    //     0xb58334: stur            w0, [x3, #0xf]
    // 0xb58338: ldur            x0, [fp, #-0x10]
    // 0xb5833c: StoreField: r3->field_b = r0
    //     0xb5833c: stur            w0, [x3, #0xb]
    // 0xb58340: r1 = Null
    //     0xb58340: mov             x1, NULL
    // 0xb58344: r2 = 4
    //     0xb58344: movz            x2, #0x4
    // 0xb58348: r0 = AllocateArray()
    //     0xb58348: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5834c: mov             x2, x0
    // 0xb58350: ldur            x0, [fp, #-8]
    // 0xb58354: stur            x2, [fp, #-0x10]
    // 0xb58358: StoreField: r2->field_f = r0
    //     0xb58358: stur            w0, [x2, #0xf]
    // 0xb5835c: ldur            x0, [fp, #-0x18]
    // 0xb58360: StoreField: r2->field_13 = r0
    //     0xb58360: stur            w0, [x2, #0x13]
    // 0xb58364: r1 = <Widget>
    //     0xb58364: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb58368: r0 = AllocateGrowableArray()
    //     0xb58368: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5836c: mov             x1, x0
    // 0xb58370: ldur            x0, [fp, #-0x10]
    // 0xb58374: stur            x1, [fp, #-8]
    // 0xb58378: StoreField: r1->field_f = r0
    //     0xb58378: stur            w0, [x1, #0xf]
    // 0xb5837c: r0 = 4
    //     0xb5837c: movz            x0, #0x4
    // 0xb58380: StoreField: r1->field_b = r0
    //     0xb58380: stur            w0, [x1, #0xb]
    // 0xb58384: r0 = Column()
    //     0xb58384: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb58388: r1 = Instance_Axis
    //     0xb58388: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5838c: StoreField: r0->field_f = r1
    //     0xb5838c: stur            w1, [x0, #0xf]
    // 0xb58390: r1 = Instance_MainAxisAlignment
    //     0xb58390: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb58394: ldr             x1, [x1, #0xa08]
    // 0xb58398: StoreField: r0->field_13 = r1
    //     0xb58398: stur            w1, [x0, #0x13]
    // 0xb5839c: r1 = Instance_MainAxisSize
    //     0xb5839c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb583a0: ldr             x1, [x1, #0xa10]
    // 0xb583a4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb583a4: stur            w1, [x0, #0x17]
    // 0xb583a8: r1 = Instance_CrossAxisAlignment
    //     0xb583a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb583ac: ldr             x1, [x1, #0x890]
    // 0xb583b0: StoreField: r0->field_1b = r1
    //     0xb583b0: stur            w1, [x0, #0x1b]
    // 0xb583b4: r1 = Instance_VerticalDirection
    //     0xb583b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb583b8: ldr             x1, [x1, #0xa20]
    // 0xb583bc: StoreField: r0->field_23 = r1
    //     0xb583bc: stur            w1, [x0, #0x23]
    // 0xb583c0: r1 = Instance_Clip
    //     0xb583c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb583c4: ldr             x1, [x1, #0x38]
    // 0xb583c8: StoreField: r0->field_2b = r1
    //     0xb583c8: stur            w1, [x0, #0x2b]
    // 0xb583cc: StoreField: r0->field_2f = rZR
    //     0xb583cc: stur            xzr, [x0, #0x2f]
    // 0xb583d0: ldur            x1, [fp, #-8]
    // 0xb583d4: StoreField: r0->field_b = r1
    //     0xb583d4: stur            w1, [x0, #0xb]
    // 0xb583d8: LeaveFrame
    //     0xb583d8: mov             SP, fp
    //     0xb583dc: ldp             fp, lr, [SP], #0x10
    // 0xb583e0: ret
    //     0xb583e0: ret             
    // 0xb583e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb583e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb583e8: b               #0xb58294
    // 0xb583ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb583ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildItemsList(/* No info */) {
    // ** addr: 0xb583f0, size: 0xa0
    // 0xb583f0: EnterFrame
    //     0xb583f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb583f4: mov             fp, SP
    // 0xb583f8: AllocStack(0x28)
    //     0xb583f8: sub             SP, SP, #0x28
    // 0xb583fc: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb583fc: stur            x1, [fp, #-8]
    //     0xb58400: stur            x2, [fp, #-0x10]
    // 0xb58404: CheckStackOverflow
    //     0xb58404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58408: cmp             SP, x16
    //     0xb5840c: b.ls            #0xb58488
    // 0xb58410: r1 = 2
    //     0xb58410: movz            x1, #0x2
    // 0xb58414: r0 = AllocateContext()
    //     0xb58414: bl              #0x16f6108  ; AllocateContextStub
    // 0xb58418: mov             x1, x0
    // 0xb5841c: ldur            x0, [fp, #-8]
    // 0xb58420: StoreField: r1->field_f = r0
    //     0xb58420: stur            w0, [x1, #0xf]
    // 0xb58424: ldur            x0, [fp, #-0x10]
    // 0xb58428: StoreField: r1->field_13 = r0
    //     0xb58428: stur            w0, [x1, #0x13]
    // 0xb5842c: LoadField: r3 = r0->field_b
    //     0xb5842c: ldur            w3, [x0, #0xb]
    // 0xb58430: mov             x2, x1
    // 0xb58434: stur            x3, [fp, #-8]
    // 0xb58438: r1 = Function '<anonymous closure>':.
    //     0xb58438: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a998] AnonymousClosure: (0xb58490), in [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildItemsList (0xb583f0)
    //     0xb5843c: ldr             x1, [x1, #0x998]
    // 0xb58440: r0 = AllocateClosure()
    //     0xb58440: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb58444: stur            x0, [fp, #-0x10]
    // 0xb58448: r0 = ListView()
    //     0xb58448: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb5844c: stur            x0, [fp, #-0x18]
    // 0xb58450: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb58450: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb58454: ldr             x16, [x16, #0x1c8]
    // 0xb58458: r30 = true
    //     0xb58458: add             lr, NULL, #0x20  ; true
    // 0xb5845c: stp             lr, x16, [SP]
    // 0xb58460: mov             x1, x0
    // 0xb58464: ldur            x2, [fp, #-0x10]
    // 0xb58468: ldur            x3, [fp, #-8]
    // 0xb5846c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xb5846c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb58470: ldr             x4, [x4, #0xd18]
    // 0xb58474: r0 = ListView.builder()
    //     0xb58474: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb58478: ldur            x0, [fp, #-0x18]
    // 0xb5847c: LeaveFrame
    //     0xb5847c: mov             SP, fp
    //     0xb58480: ldp             fp, lr, [SP], #0x10
    // 0xb58484: ret
    //     0xb58484: ret             
    // 0xb58488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58488: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5848c: b               #0xb58410
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb58490, size: 0x90
    // 0xb58490: EnterFrame
    //     0xb58490: stp             fp, lr, [SP, #-0x10]!
    //     0xb58494: mov             fp, SP
    // 0xb58498: ldr             x0, [fp, #0x20]
    // 0xb5849c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb5849c: ldur            w1, [x0, #0x17]
    // 0xb584a0: DecompressPointer r1
    //     0xb584a0: add             x1, x1, HEAP, lsl #32
    // 0xb584a4: CheckStackOverflow
    //     0xb584a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb584a8: cmp             SP, x16
    //     0xb584ac: b.ls            #0xb58514
    // 0xb584b0: LoadField: r2 = r1->field_f
    //     0xb584b0: ldur            w2, [x1, #0xf]
    // 0xb584b4: DecompressPointer r2
    //     0xb584b4: add             x2, x2, HEAP, lsl #32
    // 0xb584b8: LoadField: r3 = r1->field_13
    //     0xb584b8: ldur            w3, [x1, #0x13]
    // 0xb584bc: DecompressPointer r3
    //     0xb584bc: add             x3, x3, HEAP, lsl #32
    // 0xb584c0: LoadField: r0 = r3->field_b
    //     0xb584c0: ldur            w0, [x3, #0xb]
    // 0xb584c4: ldr             x1, [fp, #0x10]
    // 0xb584c8: r4 = LoadInt32Instr(r1)
    //     0xb584c8: sbfx            x4, x1, #1, #0x1f
    //     0xb584cc: tbz             w1, #0, #0xb584d4
    //     0xb584d0: ldur            x4, [x1, #7]
    // 0xb584d4: r1 = LoadInt32Instr(r0)
    //     0xb584d4: sbfx            x1, x0, #1, #0x1f
    // 0xb584d8: mov             x0, x1
    // 0xb584dc: mov             x1, x4
    // 0xb584e0: cmp             x1, x0
    // 0xb584e4: b.hs            #0xb5851c
    // 0xb584e8: LoadField: r0 = r3->field_f
    //     0xb584e8: ldur            w0, [x3, #0xf]
    // 0xb584ec: DecompressPointer r0
    //     0xb584ec: add             x0, x0, HEAP, lsl #32
    // 0xb584f0: ArrayLoad: r3 = r0[r4]  ; Unknown_4
    //     0xb584f0: add             x16, x0, x4, lsl #2
    //     0xb584f4: ldur            w3, [x16, #0xf]
    // 0xb584f8: DecompressPointer r3
    //     0xb584f8: add             x3, x3, HEAP, lsl #32
    // 0xb584fc: mov             x1, x2
    // 0xb58500: ldr             x2, [fp, #0x18]
    // 0xb58504: r0 = _buildListItem()
    //     0xb58504: bl              #0xb58520  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildListItem
    // 0xb58508: LeaveFrame
    //     0xb58508: mov             SP, fp
    //     0xb5850c: ldp             fp, lr, [SP], #0x10
    // 0xb58510: ret
    //     0xb58510: ret             
    // 0xb58514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58518: b               #0xb584b0
    // 0xb5851c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5851c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildListItem(/* No info */) {
    // ** addr: 0xb58520, size: 0x364
    // 0xb58520: EnterFrame
    //     0xb58520: stp             fp, lr, [SP, #-0x10]!
    //     0xb58524: mov             fp, SP
    // 0xb58528: AllocStack(0x50)
    //     0xb58528: sub             SP, SP, #0x50
    // 0xb5852c: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xb5852c: mov             x0, x3
    //     0xb58530: stur            x3, [fp, #-0x10]
    //     0xb58534: mov             x3, x2
    //     0xb58538: stur            x2, [fp, #-8]
    // 0xb5853c: CheckStackOverflow
    //     0xb5853c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58540: cmp             SP, x16
    //     0xb58544: b.ls            #0xb5887c
    // 0xb58548: mov             x2, x0
    // 0xb5854c: r0 = _buildCheckbox()
    //     0xb5854c: bl              #0xb58884  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildCheckbox
    // 0xb58550: stur            x0, [fp, #-0x18]
    // 0xb58554: r0 = Padding()
    //     0xb58554: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb58558: mov             x2, x0
    // 0xb5855c: r0 = Instance_EdgeInsets
    //     0xb5855c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xb58560: ldr             x0, [x0, #0x268]
    // 0xb58564: stur            x2, [fp, #-0x20]
    // 0xb58568: StoreField: r2->field_f = r0
    //     0xb58568: stur            w0, [x2, #0xf]
    // 0xb5856c: ldur            x0, [fp, #-0x18]
    // 0xb58570: StoreField: r2->field_b = r0
    //     0xb58570: stur            w0, [x2, #0xb]
    // 0xb58574: ldur            x0, [fp, #-0x10]
    // 0xb58578: LoadField: r1 = r0->field_f
    //     0xb58578: ldur            w1, [x0, #0xf]
    // 0xb5857c: DecompressPointer r1
    //     0xb5857c: add             x1, x1, HEAP, lsl #32
    // 0xb58580: cmp             w1, NULL
    // 0xb58584: b.ne            #0xb58590
    // 0xb58588: r3 = ""
    //     0xb58588: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5858c: b               #0xb58594
    // 0xb58590: mov             x3, x1
    // 0xb58594: ldur            x1, [fp, #-8]
    // 0xb58598: stur            x3, [fp, #-0x18]
    // 0xb5859c: r0 = of()
    //     0xb5859c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb585a0: LoadField: r1 = r0->field_87
    //     0xb585a0: ldur            w1, [x0, #0x87]
    // 0xb585a4: DecompressPointer r1
    //     0xb585a4: add             x1, x1, HEAP, lsl #32
    // 0xb585a8: LoadField: r0 = r1->field_2b
    //     0xb585a8: ldur            w0, [x1, #0x2b]
    // 0xb585ac: DecompressPointer r0
    //     0xb585ac: add             x0, x0, HEAP, lsl #32
    // 0xb585b0: stur            x0, [fp, #-0x28]
    // 0xb585b4: r1 = Instance_Color
    //     0xb585b4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb585b8: d0 = 0.700000
    //     0xb585b8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb585bc: ldr             d0, [x17, #0xf48]
    // 0xb585c0: r0 = withOpacity()
    //     0xb585c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb585c4: r16 = 12.000000
    //     0xb585c4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb585c8: ldr             x16, [x16, #0x9e8]
    // 0xb585cc: stp             x0, x16, [SP]
    // 0xb585d0: ldur            x1, [fp, #-0x28]
    // 0xb585d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb585d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb585d8: ldr             x4, [x4, #0xaa0]
    // 0xb585dc: r0 = copyWith()
    //     0xb585dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb585e0: stur            x0, [fp, #-0x28]
    // 0xb585e4: r0 = Text()
    //     0xb585e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb585e8: mov             x1, x0
    // 0xb585ec: ldur            x0, [fp, #-0x18]
    // 0xb585f0: stur            x1, [fp, #-0x30]
    // 0xb585f4: StoreField: r1->field_b = r0
    //     0xb585f4: stur            w0, [x1, #0xb]
    // 0xb585f8: ldur            x0, [fp, #-0x28]
    // 0xb585fc: StoreField: r1->field_13 = r0
    //     0xb585fc: stur            w0, [x1, #0x13]
    // 0xb58600: r0 = Padding()
    //     0xb58600: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb58604: mov             x2, x0
    // 0xb58608: r0 = Instance_EdgeInsets
    //     0xb58608: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb5860c: ldr             x0, [x0, #0xf30]
    // 0xb58610: stur            x2, [fp, #-0x18]
    // 0xb58614: StoreField: r2->field_f = r0
    //     0xb58614: stur            w0, [x2, #0xf]
    // 0xb58618: ldur            x0, [fp, #-0x30]
    // 0xb5861c: StoreField: r2->field_b = r0
    //     0xb5861c: stur            w0, [x2, #0xb]
    // 0xb58620: r1 = <FlexParentData>
    //     0xb58620: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb58624: ldr             x1, [x1, #0xe00]
    // 0xb58628: r0 = Expanded()
    //     0xb58628: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb5862c: mov             x3, x0
    // 0xb58630: r0 = 1
    //     0xb58630: movz            x0, #0x1
    // 0xb58634: stur            x3, [fp, #-0x28]
    // 0xb58638: StoreField: r3->field_13 = r0
    //     0xb58638: stur            x0, [x3, #0x13]
    // 0xb5863c: r0 = Instance_FlexFit
    //     0xb5863c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb58640: ldr             x0, [x0, #0xe08]
    // 0xb58644: StoreField: r3->field_1b = r0
    //     0xb58644: stur            w0, [x3, #0x1b]
    // 0xb58648: ldur            x0, [fp, #-0x18]
    // 0xb5864c: StoreField: r3->field_b = r0
    //     0xb5864c: stur            w0, [x3, #0xb]
    // 0xb58650: r1 = Null
    //     0xb58650: mov             x1, NULL
    // 0xb58654: r2 = 4
    //     0xb58654: movz            x2, #0x4
    // 0xb58658: r0 = AllocateArray()
    //     0xb58658: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5865c: mov             x2, x0
    // 0xb58660: ldur            x0, [fp, #-0x20]
    // 0xb58664: stur            x2, [fp, #-0x18]
    // 0xb58668: StoreField: r2->field_f = r0
    //     0xb58668: stur            w0, [x2, #0xf]
    // 0xb5866c: ldur            x0, [fp, #-0x28]
    // 0xb58670: StoreField: r2->field_13 = r0
    //     0xb58670: stur            w0, [x2, #0x13]
    // 0xb58674: r1 = <Widget>
    //     0xb58674: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb58678: r0 = AllocateGrowableArray()
    //     0xb58678: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5867c: mov             x3, x0
    // 0xb58680: ldur            x0, [fp, #-0x18]
    // 0xb58684: stur            x3, [fp, #-0x20]
    // 0xb58688: StoreField: r3->field_f = r0
    //     0xb58688: stur            w0, [x3, #0xf]
    // 0xb5868c: r2 = 4
    //     0xb5868c: movz            x2, #0x4
    // 0xb58690: StoreField: r3->field_b = r2
    //     0xb58690: stur            w2, [x3, #0xb]
    // 0xb58694: ldur            x4, [fp, #-0x10]
    // 0xb58698: LoadField: r0 = r4->field_13
    //     0xb58698: ldur            w0, [x4, #0x13]
    // 0xb5869c: DecompressPointer r0
    //     0xb5869c: add             x0, x0, HEAP, lsl #32
    // 0xb586a0: cmp             w0, NULL
    // 0xb586a4: b.ne            #0xb586b0
    // 0xb586a8: r5 = 0
    //     0xb586a8: movz            x5, #0
    // 0xb586ac: b               #0xb586c0
    // 0xb586b0: r1 = LoadInt32Instr(r0)
    //     0xb586b0: sbfx            x1, x0, #1, #0x1f
    //     0xb586b4: tbz             w0, #0, #0xb586bc
    //     0xb586b8: ldur            x1, [x0, #7]
    // 0xb586bc: mov             x5, x1
    // 0xb586c0: r0 = BoxInt64Instr(r5)
    //     0xb586c0: sbfiz           x0, x5, #1, #0x1f
    //     0xb586c4: cmp             x5, x0, asr #1
    //     0xb586c8: b.eq            #0xb586d4
    //     0xb586cc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb586d0: stur            x5, [x0, #7]
    // 0xb586d4: cbz             w0, #0xb58818
    // 0xb586d8: r1 = Null
    //     0xb586d8: mov             x1, NULL
    // 0xb586dc: r0 = AllocateArray()
    //     0xb586dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb586e0: r16 = "+ "
    //     0xb586e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xb586e4: ldr             x16, [x16, #0xc30]
    // 0xb586e8: StoreField: r0->field_f = r16
    //     0xb586e8: stur            w16, [x0, #0xf]
    // 0xb586ec: ldur            x1, [fp, #-0x10]
    // 0xb586f0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb586f0: ldur            w2, [x1, #0x17]
    // 0xb586f4: DecompressPointer r2
    //     0xb586f4: add             x2, x2, HEAP, lsl #32
    // 0xb586f8: cmp             w2, NULL
    // 0xb586fc: b.ne            #0xb58704
    // 0xb58700: r2 = ""
    //     0xb58700: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb58704: ldur            x1, [fp, #-0x20]
    // 0xb58708: StoreField: r0->field_13 = r2
    //     0xb58708: stur            w2, [x0, #0x13]
    // 0xb5870c: str             x0, [SP]
    // 0xb58710: r0 = _interpolate()
    //     0xb58710: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb58714: ldur            x1, [fp, #-8]
    // 0xb58718: stur            x0, [fp, #-8]
    // 0xb5871c: r0 = of()
    //     0xb5871c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb58720: LoadField: r1 = r0->field_87
    //     0xb58720: ldur            w1, [x0, #0x87]
    // 0xb58724: DecompressPointer r1
    //     0xb58724: add             x1, x1, HEAP, lsl #32
    // 0xb58728: LoadField: r0 = r1->field_27
    //     0xb58728: ldur            w0, [x1, #0x27]
    // 0xb5872c: DecompressPointer r0
    //     0xb5872c: add             x0, x0, HEAP, lsl #32
    // 0xb58730: r16 = 14.000000
    //     0xb58730: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb58734: ldr             x16, [x16, #0x1d8]
    // 0xb58738: r30 = Instance_FontWeight
    //     0xb58738: add             lr, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb5873c: ldr             lr, [lr, #0x20]
    // 0xb58740: stp             lr, x16, [SP, #8]
    // 0xb58744: r16 = Instance_Color
    //     0xb58744: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb58748: str             x16, [SP]
    // 0xb5874c: mov             x1, x0
    // 0xb58750: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, fontSize, 0x1, fontWeight, 0x2, null]
    //     0xb58750: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a9a0] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "fontSize", 0x1, "fontWeight", 0x2, Null]
    //     0xb58754: ldr             x4, [x4, #0x9a0]
    // 0xb58758: r0 = copyWith()
    //     0xb58758: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5875c: stur            x0, [fp, #-0x10]
    // 0xb58760: r0 = Text()
    //     0xb58760: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb58764: mov             x1, x0
    // 0xb58768: ldur            x0, [fp, #-8]
    // 0xb5876c: stur            x1, [fp, #-0x18]
    // 0xb58770: StoreField: r1->field_b = r0
    //     0xb58770: stur            w0, [x1, #0xb]
    // 0xb58774: ldur            x0, [fp, #-0x10]
    // 0xb58778: StoreField: r1->field_13 = r0
    //     0xb58778: stur            w0, [x1, #0x13]
    // 0xb5877c: r0 = Padding()
    //     0xb5877c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb58780: mov             x2, x0
    // 0xb58784: r0 = Instance_EdgeInsets
    //     0xb58784: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb58788: ldr             x0, [x0, #0x980]
    // 0xb5878c: stur            x2, [fp, #-8]
    // 0xb58790: StoreField: r2->field_f = r0
    //     0xb58790: stur            w0, [x2, #0xf]
    // 0xb58794: ldur            x0, [fp, #-0x18]
    // 0xb58798: StoreField: r2->field_b = r0
    //     0xb58798: stur            w0, [x2, #0xb]
    // 0xb5879c: ldur            x0, [fp, #-0x20]
    // 0xb587a0: LoadField: r1 = r0->field_b
    //     0xb587a0: ldur            w1, [x0, #0xb]
    // 0xb587a4: LoadField: r3 = r0->field_f
    //     0xb587a4: ldur            w3, [x0, #0xf]
    // 0xb587a8: DecompressPointer r3
    //     0xb587a8: add             x3, x3, HEAP, lsl #32
    // 0xb587ac: LoadField: r4 = r3->field_b
    //     0xb587ac: ldur            w4, [x3, #0xb]
    // 0xb587b0: r3 = LoadInt32Instr(r1)
    //     0xb587b0: sbfx            x3, x1, #1, #0x1f
    // 0xb587b4: stur            x3, [fp, #-0x38]
    // 0xb587b8: r1 = LoadInt32Instr(r4)
    //     0xb587b8: sbfx            x1, x4, #1, #0x1f
    // 0xb587bc: cmp             x3, x1
    // 0xb587c0: b.ne            #0xb587cc
    // 0xb587c4: mov             x1, x0
    // 0xb587c8: r0 = _growToNextCapacity()
    //     0xb587c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb587cc: ldur            x2, [fp, #-0x20]
    // 0xb587d0: ldur            x3, [fp, #-0x38]
    // 0xb587d4: add             x0, x3, #1
    // 0xb587d8: lsl             x1, x0, #1
    // 0xb587dc: StoreField: r2->field_b = r1
    //     0xb587dc: stur            w1, [x2, #0xb]
    // 0xb587e0: LoadField: r1 = r2->field_f
    //     0xb587e0: ldur            w1, [x2, #0xf]
    // 0xb587e4: DecompressPointer r1
    //     0xb587e4: add             x1, x1, HEAP, lsl #32
    // 0xb587e8: ldur            x0, [fp, #-8]
    // 0xb587ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb587ec: add             x25, x1, x3, lsl #2
    //     0xb587f0: add             x25, x25, #0xf
    //     0xb587f4: str             w0, [x25]
    //     0xb587f8: tbz             w0, #0, #0xb58814
    //     0xb587fc: ldurb           w16, [x1, #-1]
    //     0xb58800: ldurb           w17, [x0, #-1]
    //     0xb58804: and             x16, x17, x16, lsr #2
    //     0xb58808: tst             x16, HEAP, lsr #32
    //     0xb5880c: b.eq            #0xb58814
    //     0xb58810: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb58814: b               #0xb5881c
    // 0xb58818: mov             x2, x3
    // 0xb5881c: r0 = Row()
    //     0xb5881c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb58820: r1 = Instance_Axis
    //     0xb58820: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb58824: StoreField: r0->field_f = r1
    //     0xb58824: stur            w1, [x0, #0xf]
    // 0xb58828: r1 = Instance_MainAxisAlignment
    //     0xb58828: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5882c: ldr             x1, [x1, #0xa08]
    // 0xb58830: StoreField: r0->field_13 = r1
    //     0xb58830: stur            w1, [x0, #0x13]
    // 0xb58834: r1 = Instance_MainAxisSize
    //     0xb58834: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb58838: ldr             x1, [x1, #0xa10]
    // 0xb5883c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5883c: stur            w1, [x0, #0x17]
    // 0xb58840: r1 = Instance_CrossAxisAlignment
    //     0xb58840: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb58844: ldr             x1, [x1, #0xa18]
    // 0xb58848: StoreField: r0->field_1b = r1
    //     0xb58848: stur            w1, [x0, #0x1b]
    // 0xb5884c: r1 = Instance_VerticalDirection
    //     0xb5884c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb58850: ldr             x1, [x1, #0xa20]
    // 0xb58854: StoreField: r0->field_23 = r1
    //     0xb58854: stur            w1, [x0, #0x23]
    // 0xb58858: r1 = Instance_Clip
    //     0xb58858: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5885c: ldr             x1, [x1, #0x38]
    // 0xb58860: StoreField: r0->field_2b = r1
    //     0xb58860: stur            w1, [x0, #0x2b]
    // 0xb58864: StoreField: r0->field_2f = rZR
    //     0xb58864: stur            xzr, [x0, #0x2f]
    // 0xb58868: ldur            x1, [fp, #-0x20]
    // 0xb5886c: StoreField: r0->field_b = r1
    //     0xb5886c: stur            w1, [x0, #0xb]
    // 0xb58870: LeaveFrame
    //     0xb58870: mov             SP, fp
    //     0xb58874: ldp             fp, lr, [SP], #0x10
    // 0xb58878: ret
    //     0xb58878: ret             
    // 0xb5887c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5887c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58880: b               #0xb58548
  }
  _ _buildCheckbox(/* No info */) {
    // ** addr: 0xb58884, size: 0x100
    // 0xb58884: EnterFrame
    //     0xb58884: stp             fp, lr, [SP, #-0x10]!
    //     0xb58888: mov             fp, SP
    // 0xb5888c: AllocStack(0x20)
    //     0xb5888c: sub             SP, SP, #0x20
    // 0xb58890: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb58890: stur            x1, [fp, #-8]
    //     0xb58894: stur            x2, [fp, #-0x10]
    // 0xb58898: CheckStackOverflow
    //     0xb58898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5889c: cmp             SP, x16
    //     0xb588a0: b.ls            #0xb58978
    // 0xb588a4: r1 = 3
    //     0xb588a4: movz            x1, #0x3
    // 0xb588a8: r0 = AllocateContext()
    //     0xb588a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb588ac: mov             x3, x0
    // 0xb588b0: ldur            x0, [fp, #-8]
    // 0xb588b4: stur            x3, [fp, #-0x18]
    // 0xb588b8: StoreField: r3->field_f = r0
    //     0xb588b8: stur            w0, [x3, #0xf]
    // 0xb588bc: ldur            x1, [fp, #-0x10]
    // 0xb588c0: StoreField: r3->field_13 = r1
    //     0xb588c0: stur            w1, [x3, #0x13]
    // 0xb588c4: LoadField: r2 = r1->field_b
    //     0xb588c4: ldur            w2, [x1, #0xb]
    // 0xb588c8: DecompressPointer r2
    //     0xb588c8: add             x2, x2, HEAP, lsl #32
    // 0xb588cc: cmp             w2, NULL
    // 0xb588d0: b.ne            #0xb588d8
    // 0xb588d4: r2 = ""
    //     0xb588d4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb588d8: ArrayStore: r3[0] = r2  ; List_4
    //     0xb588d8: stur            w2, [x3, #0x17]
    // 0xb588dc: LoadField: r1 = r0->field_13
    //     0xb588dc: ldur            w1, [x0, #0x13]
    // 0xb588e0: DecompressPointer r1
    //     0xb588e0: add             x1, x1, HEAP, lsl #32
    // 0xb588e4: r0 = contains()
    //     0xb588e4: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xb588e8: mov             x2, x0
    // 0xb588ec: ldur            x0, [fp, #-8]
    // 0xb588f0: stur            x2, [fp, #-0x10]
    // 0xb588f4: LoadField: r1 = r0->field_f
    //     0xb588f4: ldur            w1, [x0, #0xf]
    // 0xb588f8: DecompressPointer r1
    //     0xb588f8: add             x1, x1, HEAP, lsl #32
    // 0xb588fc: cmp             w1, NULL
    // 0xb58900: b.eq            #0xb58980
    // 0xb58904: r0 = of()
    //     0xb58904: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb58908: LoadField: r1 = r0->field_5b
    //     0xb58908: ldur            w1, [x0, #0x5b]
    // 0xb5890c: DecompressPointer r1
    //     0xb5890c: add             x1, x1, HEAP, lsl #32
    // 0xb58910: stur            x1, [fp, #-8]
    // 0xb58914: r0 = Checkbox()
    //     0xb58914: bl              #0xa2e5f0  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xb58918: mov             x3, x0
    // 0xb5891c: ldur            x0, [fp, #-0x10]
    // 0xb58920: stur            x3, [fp, #-0x20]
    // 0xb58924: StoreField: r3->field_b = r0
    //     0xb58924: stur            w0, [x3, #0xb]
    // 0xb58928: r0 = false
    //     0xb58928: add             x0, NULL, #0x30  ; false
    // 0xb5892c: StoreField: r3->field_23 = r0
    //     0xb5892c: stur            w0, [x3, #0x23]
    // 0xb58930: ldur            x2, [fp, #-0x18]
    // 0xb58934: r1 = Function '<anonymous closure>':.
    //     0xb58934: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a9a8] AnonymousClosure: (0xb58984), in [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildCheckbox (0xb58884)
    //     0xb58938: ldr             x1, [x1, #0x9a8]
    // 0xb5893c: r0 = AllocateClosure()
    //     0xb5893c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb58940: mov             x1, x0
    // 0xb58944: ldur            x0, [fp, #-0x20]
    // 0xb58948: StoreField: r0->field_f = r1
    //     0xb58948: stur            w1, [x0, #0xf]
    // 0xb5894c: ldur            x1, [fp, #-8]
    // 0xb58950: ArrayStore: r0[0] = r1  ; List_4
    //     0xb58950: stur            w1, [x0, #0x17]
    // 0xb58954: r1 = false
    //     0xb58954: add             x1, NULL, #0x30  ; false
    // 0xb58958: StoreField: r0->field_43 = r1
    //     0xb58958: stur            w1, [x0, #0x43]
    // 0xb5895c: StoreField: r0->field_4f = r1
    //     0xb5895c: stur            w1, [x0, #0x4f]
    // 0xb58960: r1 = Instance__CheckboxType
    //     0xb58960: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d80] Obj!_CheckboxType@d74601
    //     0xb58964: ldr             x1, [x1, #0xd80]
    // 0xb58968: StoreField: r0->field_57 = r1
    //     0xb58968: stur            w1, [x0, #0x57]
    // 0xb5896c: LeaveFrame
    //     0xb5896c: mov             SP, fp
    //     0xb58970: ldp             fp, lr, [SP], #0x10
    // 0xb58974: ret
    //     0xb58974: ret             
    // 0xb58978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58978: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5897c: b               #0xb588a4
    // 0xb58980: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58980: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool?) {
    // ** addr: 0xb58984, size: 0x68
    // 0xb58984: EnterFrame
    //     0xb58984: stp             fp, lr, [SP, #-0x10]!
    //     0xb58988: mov             fp, SP
    // 0xb5898c: ldr             x0, [fp, #0x18]
    // 0xb58990: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb58990: ldur            w1, [x0, #0x17]
    // 0xb58994: DecompressPointer r1
    //     0xb58994: add             x1, x1, HEAP, lsl #32
    // 0xb58998: CheckStackOverflow
    //     0xb58998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5899c: cmp             SP, x16
    //     0xb589a0: b.ls            #0xb589e4
    // 0xb589a4: LoadField: r0 = r1->field_f
    //     0xb589a4: ldur            w0, [x1, #0xf]
    // 0xb589a8: DecompressPointer r0
    //     0xb589a8: add             x0, x0, HEAP, lsl #32
    // 0xb589ac: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb589ac: ldur            w2, [x1, #0x17]
    // 0xb589b0: DecompressPointer r2
    //     0xb589b0: add             x2, x2, HEAP, lsl #32
    // 0xb589b4: ldr             x3, [fp, #0x10]
    // 0xb589b8: cmp             w3, NULL
    // 0xb589bc: b.ne            #0xb589c4
    // 0xb589c0: r3 = false
    //     0xb589c0: add             x3, NULL, #0x30  ; false
    // 0xb589c4: LoadField: r5 = r1->field_13
    //     0xb589c4: ldur            w5, [x1, #0x13]
    // 0xb589c8: DecompressPointer r5
    //     0xb589c8: add             x5, x5, HEAP, lsl #32
    // 0xb589cc: mov             x1, x0
    // 0xb589d0: r0 = _handleCheckboxChange()
    //     0xb589d0: bl              #0xb589ec  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_handleCheckboxChange
    // 0xb589d4: r0 = Null
    //     0xb589d4: mov             x0, NULL
    // 0xb589d8: LeaveFrame
    //     0xb589d8: mov             SP, fp
    //     0xb589dc: ldp             fp, lr, [SP], #0x10
    // 0xb589e0: ret
    //     0xb589e0: ret             
    // 0xb589e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb589e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb589e8: b               #0xb589a4
  }
  _ _handleCheckboxChange(/* No info */) {
    // ** addr: 0xb589ec, size: 0x44
    // 0xb589ec: EnterFrame
    //     0xb589ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb589f0: mov             fp, SP
    // 0xb589f4: mov             x0, x3
    // 0xb589f8: mov             x3, x5
    // 0xb589fc: CheckStackOverflow
    //     0xb589fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58a00: cmp             SP, x16
    //     0xb58a04: b.ls            #0xb58a28
    // 0xb58a08: tbnz            w0, #4, #0xb58a14
    // 0xb58a0c: r0 = _selectItem()
    //     0xb58a0c: bl              #0xb58cc8  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_selectItem
    // 0xb58a10: b               #0xb58a18
    // 0xb58a14: r0 = _deselectItem()
    //     0xb58a14: bl              #0xb58a30  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_deselectItem
    // 0xb58a18: r0 = Null
    //     0xb58a18: mov             x0, NULL
    // 0xb58a1c: LeaveFrame
    //     0xb58a1c: mov             SP, fp
    //     0xb58a20: ldp             fp, lr, [SP], #0x10
    // 0xb58a24: ret
    //     0xb58a24: ret             
    // 0xb58a28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58a28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58a2c: b               #0xb58a08
  }
  _ _deselectItem(/* No info */) {
    // ** addr: 0xb58a30, size: 0x7c
    // 0xb58a30: EnterFrame
    //     0xb58a30: stp             fp, lr, [SP, #-0x10]!
    //     0xb58a34: mov             fp, SP
    // 0xb58a38: AllocStack(0x18)
    //     0xb58a38: sub             SP, SP, #0x18
    // 0xb58a3c: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb58a3c: stur            x1, [fp, #-8]
    //     0xb58a40: stur            x2, [fp, #-0x10]
    //     0xb58a44: stur            x3, [fp, #-0x18]
    // 0xb58a48: CheckStackOverflow
    //     0xb58a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58a4c: cmp             SP, x16
    //     0xb58a50: b.ls            #0xb58aa4
    // 0xb58a54: r1 = 3
    //     0xb58a54: movz            x1, #0x3
    // 0xb58a58: r0 = AllocateContext()
    //     0xb58a58: bl              #0x16f6108  ; AllocateContextStub
    // 0xb58a5c: mov             x1, x0
    // 0xb58a60: ldur            x0, [fp, #-8]
    // 0xb58a64: StoreField: r1->field_f = r0
    //     0xb58a64: stur            w0, [x1, #0xf]
    // 0xb58a68: ldur            x2, [fp, #-0x10]
    // 0xb58a6c: StoreField: r1->field_13 = r2
    //     0xb58a6c: stur            w2, [x1, #0x13]
    // 0xb58a70: ldur            x2, [fp, #-0x18]
    // 0xb58a74: ArrayStore: r1[0] = r2  ; List_4
    //     0xb58a74: stur            w2, [x1, #0x17]
    // 0xb58a78: mov             x2, x1
    // 0xb58a7c: r1 = Function '<anonymous closure>':.
    //     0xb58a7c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a9b0] AnonymousClosure: (0xb58aac), in [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_deselectItem (0xb58a30)
    //     0xb58a80: ldr             x1, [x1, #0x9b0]
    // 0xb58a84: r0 = AllocateClosure()
    //     0xb58a84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb58a88: ldur            x1, [fp, #-8]
    // 0xb58a8c: mov             x2, x0
    // 0xb58a90: r0 = setState()
    //     0xb58a90: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb58a94: r0 = Null
    //     0xb58a94: mov             x0, NULL
    // 0xb58a98: LeaveFrame
    //     0xb58a98: mov             SP, fp
    //     0xb58a9c: ldp             fp, lr, [SP], #0x10
    // 0xb58aa0: ret
    //     0xb58aa0: ret             
    // 0xb58aa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58aa4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58aa8: b               #0xb58a54
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb58aac, size: 0x168
    // 0xb58aac: EnterFrame
    //     0xb58aac: stp             fp, lr, [SP, #-0x10]!
    //     0xb58ab0: mov             fp, SP
    // 0xb58ab4: AllocStack(0x30)
    //     0xb58ab4: sub             SP, SP, #0x30
    // 0xb58ab8: SetupParameters()
    //     0xb58ab8: ldr             x0, [fp, #0x10]
    //     0xb58abc: ldur            w3, [x0, #0x17]
    //     0xb58ac0: add             x3, x3, HEAP, lsl #32
    //     0xb58ac4: stur            x3, [fp, #-8]
    // 0xb58ac8: CheckStackOverflow
    //     0xb58ac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58acc: cmp             SP, x16
    //     0xb58ad0: b.ls            #0xb58c08
    // 0xb58ad4: LoadField: r0 = r3->field_f
    //     0xb58ad4: ldur            w0, [x3, #0xf]
    // 0xb58ad8: DecompressPointer r0
    //     0xb58ad8: add             x0, x0, HEAP, lsl #32
    // 0xb58adc: LoadField: r1 = r0->field_13
    //     0xb58adc: ldur            w1, [x0, #0x13]
    // 0xb58ae0: DecompressPointer r1
    //     0xb58ae0: add             x1, x1, HEAP, lsl #32
    // 0xb58ae4: LoadField: r2 = r3->field_13
    //     0xb58ae4: ldur            w2, [x3, #0x13]
    // 0xb58ae8: DecompressPointer r2
    //     0xb58ae8: add             x2, x2, HEAP, lsl #32
    // 0xb58aec: r0 = remove()
    //     0xb58aec: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xb58af0: ldur            x0, [fp, #-8]
    // 0xb58af4: LoadField: r1 = r0->field_f
    //     0xb58af4: ldur            w1, [x0, #0xf]
    // 0xb58af8: DecompressPointer r1
    //     0xb58af8: add             x1, x1, HEAP, lsl #32
    // 0xb58afc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb58afc: ldur            w2, [x0, #0x17]
    // 0xb58b00: DecompressPointer r2
    //     0xb58b00: add             x2, x2, HEAP, lsl #32
    // 0xb58b04: LoadField: r3 = r2->field_b
    //     0xb58b04: ldur            w3, [x2, #0xb]
    // 0xb58b08: DecompressPointer r3
    //     0xb58b08: add             x3, x3, HEAP, lsl #32
    // 0xb58b0c: mov             x2, x3
    // 0xb58b10: r0 = _findAndRemoveCustomerResponse()
    //     0xb58b10: bl              #0xb58c14  ; [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_findAndRemoveCustomerResponse
    // 0xb58b14: mov             x2, x0
    // 0xb58b18: cmp             w2, NULL
    // 0xb58b1c: b.eq            #0xb58bf8
    // 0xb58b20: ldur            x0, [fp, #-8]
    // 0xb58b24: LoadField: r1 = r0->field_f
    //     0xb58b24: ldur            w1, [x0, #0xf]
    // 0xb58b28: DecompressPointer r1
    //     0xb58b28: add             x1, x1, HEAP, lsl #32
    // 0xb58b2c: LoadField: r3 = r1->field_b
    //     0xb58b2c: ldur            w3, [x1, #0xb]
    // 0xb58b30: DecompressPointer r3
    //     0xb58b30: add             x3, x3, HEAP, lsl #32
    // 0xb58b34: cmp             w3, NULL
    // 0xb58b38: b.eq            #0xb58c10
    // 0xb58b3c: LoadField: r4 = r3->field_b
    //     0xb58b3c: ldur            w4, [x3, #0xb]
    // 0xb58b40: DecompressPointer r4
    //     0xb58b40: add             x4, x4, HEAP, lsl #32
    // 0xb58b44: cmp             w4, NULL
    // 0xb58b48: b.ne            #0xb58b54
    // 0xb58b4c: r4 = Null
    //     0xb58b4c: mov             x4, NULL
    // 0xb58b50: b               #0xb58b60
    // 0xb58b54: LoadField: r5 = r4->field_2b
    //     0xb58b54: ldur            w5, [x4, #0x2b]
    // 0xb58b58: DecompressPointer r5
    //     0xb58b58: add             x5, x5, HEAP, lsl #32
    // 0xb58b5c: mov             x4, x5
    // 0xb58b60: cmp             w4, NULL
    // 0xb58b64: b.ne            #0xb58b6c
    // 0xb58b68: r4 = false
    //     0xb58b68: add             x4, NULL, #0x30  ; false
    // 0xb58b6c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb58b6c: ldur            w5, [x1, #0x17]
    // 0xb58b70: DecompressPointer r5
    //     0xb58b70: add             x5, x5, HEAP, lsl #32
    // 0xb58b74: LoadField: r1 = r5->field_b
    //     0xb58b74: ldur            w1, [x5, #0xb]
    // 0xb58b78: cbnz            w1, #0xb58b84
    // 0xb58b7c: r5 = false
    //     0xb58b7c: add             x5, NULL, #0x30  ; false
    // 0xb58b80: b               #0xb58b88
    // 0xb58b84: r5 = true
    //     0xb58b84: add             x5, NULL, #0x20  ; true
    // 0xb58b88: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb58b88: ldur            w1, [x0, #0x17]
    // 0xb58b8c: DecompressPointer r1
    //     0xb58b8c: add             x1, x1, HEAP, lsl #32
    // 0xb58b90: LoadField: r0 = r1->field_13
    //     0xb58b90: ldur            w0, [x1, #0x13]
    // 0xb58b94: DecompressPointer r0
    //     0xb58b94: add             x0, x0, HEAP, lsl #32
    // 0xb58b98: cmp             w0, NULL
    // 0xb58b9c: b.ne            #0xb58ba8
    // 0xb58ba0: r6 = 0
    //     0xb58ba0: movz            x6, #0
    // 0xb58ba4: b               #0xb58bb8
    // 0xb58ba8: r1 = LoadInt32Instr(r0)
    //     0xb58ba8: sbfx            x1, x0, #1, #0x1f
    //     0xb58bac: tbz             w0, #0, #0xb58bb4
    //     0xb58bb0: ldur            x1, [x0, #7]
    // 0xb58bb4: mov             x6, x1
    // 0xb58bb8: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xb58bb8: ldur            w7, [x3, #0x17]
    // 0xb58bbc: DecompressPointer r7
    //     0xb58bbc: add             x7, x7, HEAP, lsl #32
    // 0xb58bc0: r0 = BoxInt64Instr(r6)
    //     0xb58bc0: sbfiz           x0, x6, #1, #0x1f
    //     0xb58bc4: cmp             x6, x0, asr #1
    //     0xb58bc8: b.eq            #0xb58bd4
    //     0xb58bcc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb58bd0: stur            x6, [x0, #7]
    // 0xb58bd4: stp             x0, x7, [SP, #0x18]
    // 0xb58bd8: stp             x4, x5, [SP, #8]
    // 0xb58bdc: str             x2, [SP]
    // 0xb58be0: r4 = 0
    //     0xb58be0: movz            x4, #0
    // 0xb58be4: ldr             x0, [SP, #0x20]
    // 0xb58be8: r16 = UnlinkedCall_0x613b5c
    //     0xb58be8: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a9b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb58bec: add             x16, x16, #0x9b8
    // 0xb58bf0: ldp             x5, lr, [x16]
    // 0xb58bf4: blr             lr
    // 0xb58bf8: r0 = Null
    //     0xb58bf8: mov             x0, NULL
    // 0xb58bfc: LeaveFrame
    //     0xb58bfc: mov             SP, fp
    //     0xb58c00: ldp             fp, lr, [SP], #0x10
    // 0xb58c04: ret
    //     0xb58c04: ret             
    // 0xb58c08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58c08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58c0c: b               #0xb58ad4
    // 0xb58c10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58c10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _findAndRemoveCustomerResponse(/* No info */) {
    // ** addr: 0xb58c14, size: 0xb4
    // 0xb58c14: EnterFrame
    //     0xb58c14: stp             fp, lr, [SP, #-0x10]!
    //     0xb58c18: mov             fp, SP
    // 0xb58c1c: AllocStack(0x10)
    //     0xb58c1c: sub             SP, SP, #0x10
    // 0xb58c20: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb58c20: stur            x1, [fp, #-8]
    //     0xb58c24: stur            x2, [fp, #-0x10]
    // 0xb58c28: CheckStackOverflow
    //     0xb58c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58c2c: cmp             SP, x16
    //     0xb58c30: b.ls            #0xb58cc0
    // 0xb58c34: r1 = 1
    //     0xb58c34: movz            x1, #0x1
    // 0xb58c38: r0 = AllocateContext()
    //     0xb58c38: bl              #0x16f6108  ; AllocateContextStub
    // 0xb58c3c: mov             x1, x0
    // 0xb58c40: ldur            x0, [fp, #-0x10]
    // 0xb58c44: StoreField: r1->field_f = r0
    //     0xb58c44: stur            w0, [x1, #0xf]
    // 0xb58c48: cmp             w0, NULL
    // 0xb58c4c: b.ne            #0xb58c60
    // 0xb58c50: r0 = Null
    //     0xb58c50: mov             x0, NULL
    // 0xb58c54: LeaveFrame
    //     0xb58c54: mov             SP, fp
    //     0xb58c58: ldp             fp, lr, [SP], #0x10
    // 0xb58c5c: ret
    //     0xb58c5c: ret             
    // 0xb58c60: ldur            x0, [fp, #-8]
    // 0xb58c64: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb58c64: ldur            w3, [x0, #0x17]
    // 0xb58c68: DecompressPointer r3
    //     0xb58c68: add             x3, x3, HEAP, lsl #32
    // 0xb58c6c: mov             x2, x1
    // 0xb58c70: stur            x3, [fp, #-0x10]
    // 0xb58c74: r1 = Function '<anonymous closure>':.
    //     0xb58c74: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a9c8] AnonymousClosure: (0xa3ceb0), in [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_findAndRemoveCustomerResponse (0xa3cf18)
    //     0xb58c78: ldr             x1, [x1, #0x9c8]
    // 0xb58c7c: r0 = AllocateClosure()
    //     0xb58c7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb58c80: ldur            x1, [fp, #-0x10]
    // 0xb58c84: mov             x2, x0
    // 0xb58c88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb58c88: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb58c8c: r0 = indexWhere()
    //     0xb58c8c: bl              #0x806984  ; [dart:collection] ListBase::indexWhere
    // 0xb58c90: cmn             x0, #1
    // 0xb58c94: b.eq            #0xb58cb0
    // 0xb58c98: ldur            x1, [fp, #-0x10]
    // 0xb58c9c: mov             x2, x0
    // 0xb58ca0: r0 = removeAt()
    //     0xb58ca0: bl              #0x7145c0  ; [dart:core] _GrowableList::removeAt
    // 0xb58ca4: LeaveFrame
    //     0xb58ca4: mov             SP, fp
    //     0xb58ca8: ldp             fp, lr, [SP], #0x10
    // 0xb58cac: ret
    //     0xb58cac: ret             
    // 0xb58cb0: r0 = Null
    //     0xb58cb0: mov             x0, NULL
    // 0xb58cb4: LeaveFrame
    //     0xb58cb4: mov             SP, fp
    //     0xb58cb8: ldp             fp, lr, [SP], #0x10
    // 0xb58cbc: ret
    //     0xb58cbc: ret             
    // 0xb58cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58cc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58cc4: b               #0xb58c34
  }
  _ _selectItem(/* No info */) {
    // ** addr: 0xb58cc8, size: 0x7c
    // 0xb58cc8: EnterFrame
    //     0xb58cc8: stp             fp, lr, [SP, #-0x10]!
    //     0xb58ccc: mov             fp, SP
    // 0xb58cd0: AllocStack(0x18)
    //     0xb58cd0: sub             SP, SP, #0x18
    // 0xb58cd4: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb58cd4: stur            x1, [fp, #-8]
    //     0xb58cd8: stur            x2, [fp, #-0x10]
    //     0xb58cdc: stur            x3, [fp, #-0x18]
    // 0xb58ce0: CheckStackOverflow
    //     0xb58ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58ce4: cmp             SP, x16
    //     0xb58ce8: b.ls            #0xb58d3c
    // 0xb58cec: r1 = 3
    //     0xb58cec: movz            x1, #0x3
    // 0xb58cf0: r0 = AllocateContext()
    //     0xb58cf0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb58cf4: mov             x1, x0
    // 0xb58cf8: ldur            x0, [fp, #-8]
    // 0xb58cfc: StoreField: r1->field_f = r0
    //     0xb58cfc: stur            w0, [x1, #0xf]
    // 0xb58d00: ldur            x2, [fp, #-0x10]
    // 0xb58d04: StoreField: r1->field_13 = r2
    //     0xb58d04: stur            w2, [x1, #0x13]
    // 0xb58d08: ldur            x2, [fp, #-0x18]
    // 0xb58d0c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb58d0c: stur            w2, [x1, #0x17]
    // 0xb58d10: mov             x2, x1
    // 0xb58d14: r1 = Function '<anonymous closure>':.
    //     0xb58d14: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a9d0] AnonymousClosure: (0xb58d44), in [package:customer_app/app/presentation/views/glass/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_selectItem (0xb58cc8)
    //     0xb58d18: ldr             x1, [x1, #0x9d0]
    // 0xb58d1c: r0 = AllocateClosure()
    //     0xb58d1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb58d20: ldur            x1, [fp, #-8]
    // 0xb58d24: mov             x2, x0
    // 0xb58d28: r0 = setState()
    //     0xb58d28: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb58d2c: r0 = Null
    //     0xb58d2c: mov             x0, NULL
    // 0xb58d30: LeaveFrame
    //     0xb58d30: mov             SP, fp
    //     0xb58d34: ldp             fp, lr, [SP], #0x10
    // 0xb58d38: ret
    //     0xb58d38: ret             
    // 0xb58d3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58d3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58d40: b               #0xb58cec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb58d44, size: 0x198
    // 0xb58d44: EnterFrame
    //     0xb58d44: stp             fp, lr, [SP, #-0x10]!
    //     0xb58d48: mov             fp, SP
    // 0xb58d4c: AllocStack(0x38)
    //     0xb58d4c: sub             SP, SP, #0x38
    // 0xb58d50: SetupParameters()
    //     0xb58d50: ldr             x0, [fp, #0x10]
    //     0xb58d54: ldur            w3, [x0, #0x17]
    //     0xb58d58: add             x3, x3, HEAP, lsl #32
    //     0xb58d5c: stur            x3, [fp, #-8]
    // 0xb58d60: CheckStackOverflow
    //     0xb58d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb58d64: cmp             SP, x16
    //     0xb58d68: b.ls            #0xb58ed0
    // 0xb58d6c: LoadField: r0 = r3->field_f
    //     0xb58d6c: ldur            w0, [x3, #0xf]
    // 0xb58d70: DecompressPointer r0
    //     0xb58d70: add             x0, x0, HEAP, lsl #32
    // 0xb58d74: LoadField: r1 = r0->field_13
    //     0xb58d74: ldur            w1, [x0, #0x13]
    // 0xb58d78: DecompressPointer r1
    //     0xb58d78: add             x1, x1, HEAP, lsl #32
    // 0xb58d7c: LoadField: r2 = r3->field_13
    //     0xb58d7c: ldur            w2, [x3, #0x13]
    // 0xb58d80: DecompressPointer r2
    //     0xb58d80: add             x2, x2, HEAP, lsl #32
    // 0xb58d84: r0 = add()
    //     0xb58d84: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xb58d88: ldur            x0, [fp, #-8]
    // 0xb58d8c: LoadField: r1 = r0->field_f
    //     0xb58d8c: ldur            w1, [x0, #0xf]
    // 0xb58d90: DecompressPointer r1
    //     0xb58d90: add             x1, x1, HEAP, lsl #32
    // 0xb58d94: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb58d94: ldur            w2, [x0, #0x17]
    // 0xb58d98: DecompressPointer r2
    //     0xb58d98: add             x2, x2, HEAP, lsl #32
    // 0xb58d9c: r0 = _createCustomerResponse()
    //     0xb58d9c: bl              #0xa3d1e0  ; [package:customer_app/app/presentation/views/basic/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_createCustomerResponse
    // 0xb58da0: mov             x2, x0
    // 0xb58da4: ldur            x0, [fp, #-8]
    // 0xb58da8: stur            x2, [fp, #-0x20]
    // 0xb58dac: LoadField: r1 = r0->field_f
    //     0xb58dac: ldur            w1, [x0, #0xf]
    // 0xb58db0: DecompressPointer r1
    //     0xb58db0: add             x1, x1, HEAP, lsl #32
    // 0xb58db4: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb58db4: ldur            w3, [x1, #0x17]
    // 0xb58db8: DecompressPointer r3
    //     0xb58db8: add             x3, x3, HEAP, lsl #32
    // 0xb58dbc: stur            x3, [fp, #-0x18]
    // 0xb58dc0: LoadField: r1 = r3->field_b
    //     0xb58dc0: ldur            w1, [x3, #0xb]
    // 0xb58dc4: LoadField: r4 = r3->field_f
    //     0xb58dc4: ldur            w4, [x3, #0xf]
    // 0xb58dc8: DecompressPointer r4
    //     0xb58dc8: add             x4, x4, HEAP, lsl #32
    // 0xb58dcc: LoadField: r5 = r4->field_b
    //     0xb58dcc: ldur            w5, [x4, #0xb]
    // 0xb58dd0: r4 = LoadInt32Instr(r1)
    //     0xb58dd0: sbfx            x4, x1, #1, #0x1f
    // 0xb58dd4: stur            x4, [fp, #-0x10]
    // 0xb58dd8: r1 = LoadInt32Instr(r5)
    //     0xb58dd8: sbfx            x1, x5, #1, #0x1f
    // 0xb58ddc: cmp             x4, x1
    // 0xb58de0: b.ne            #0xb58dec
    // 0xb58de4: mov             x1, x3
    // 0xb58de8: r0 = _growToNextCapacity()
    //     0xb58de8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb58dec: ldur            x2, [fp, #-8]
    // 0xb58df0: ldur            x0, [fp, #-0x18]
    // 0xb58df4: ldur            x3, [fp, #-0x10]
    // 0xb58df8: add             x1, x3, #1
    // 0xb58dfc: lsl             x4, x1, #1
    // 0xb58e00: StoreField: r0->field_b = r4
    //     0xb58e00: stur            w4, [x0, #0xb]
    // 0xb58e04: LoadField: r1 = r0->field_f
    //     0xb58e04: ldur            w1, [x0, #0xf]
    // 0xb58e08: DecompressPointer r1
    //     0xb58e08: add             x1, x1, HEAP, lsl #32
    // 0xb58e0c: ldur            x0, [fp, #-0x20]
    // 0xb58e10: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb58e10: add             x25, x1, x3, lsl #2
    //     0xb58e14: add             x25, x25, #0xf
    //     0xb58e18: str             w0, [x25]
    //     0xb58e1c: tbz             w0, #0, #0xb58e38
    //     0xb58e20: ldurb           w16, [x1, #-1]
    //     0xb58e24: ldurb           w17, [x0, #-1]
    //     0xb58e28: and             x16, x17, x16, lsr #2
    //     0xb58e2c: tst             x16, HEAP, lsr #32
    //     0xb58e30: b.eq            #0xb58e38
    //     0xb58e34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb58e38: LoadField: r0 = r2->field_f
    //     0xb58e38: ldur            w0, [x2, #0xf]
    // 0xb58e3c: DecompressPointer r0
    //     0xb58e3c: add             x0, x0, HEAP, lsl #32
    // 0xb58e40: LoadField: r1 = r0->field_b
    //     0xb58e40: ldur            w1, [x0, #0xb]
    // 0xb58e44: DecompressPointer r1
    //     0xb58e44: add             x1, x1, HEAP, lsl #32
    // 0xb58e48: cmp             w1, NULL
    // 0xb58e4c: b.eq            #0xb58ed8
    // 0xb58e50: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xb58e50: ldur            w0, [x2, #0x17]
    // 0xb58e54: DecompressPointer r0
    //     0xb58e54: add             x0, x0, HEAP, lsl #32
    // 0xb58e58: LoadField: r2 = r0->field_13
    //     0xb58e58: ldur            w2, [x0, #0x13]
    // 0xb58e5c: DecompressPointer r2
    //     0xb58e5c: add             x2, x2, HEAP, lsl #32
    // 0xb58e60: cmp             w2, NULL
    // 0xb58e64: b.ne            #0xb58e70
    // 0xb58e68: r2 = 0
    //     0xb58e68: movz            x2, #0
    // 0xb58e6c: b               #0xb58e80
    // 0xb58e70: r0 = LoadInt32Instr(r2)
    //     0xb58e70: sbfx            x0, x2, #1, #0x1f
    //     0xb58e74: tbz             w2, #0, #0xb58e7c
    //     0xb58e78: ldur            x0, [x2, #7]
    // 0xb58e7c: mov             x2, x0
    // 0xb58e80: LoadField: r3 = r1->field_13
    //     0xb58e80: ldur            w3, [x1, #0x13]
    // 0xb58e84: DecompressPointer r3
    //     0xb58e84: add             x3, x3, HEAP, lsl #32
    // 0xb58e88: r0 = BoxInt64Instr(r2)
    //     0xb58e88: sbfiz           x0, x2, #1, #0x1f
    //     0xb58e8c: cmp             x2, x0, asr #1
    //     0xb58e90: b.eq            #0xb58e9c
    //     0xb58e94: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb58e98: stur            x2, [x0, #7]
    // 0xb58e9c: stp             x0, x3, [SP, #8]
    // 0xb58ea0: ldur            x16, [fp, #-0x20]
    // 0xb58ea4: str             x16, [SP]
    // 0xb58ea8: r4 = 0
    //     0xb58ea8: movz            x4, #0
    // 0xb58eac: ldr             x0, [SP, #0x10]
    // 0xb58eb0: r16 = UnlinkedCall_0x613b5c
    //     0xb58eb0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a9d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb58eb4: add             x16, x16, #0x9d8
    // 0xb58eb8: ldp             x5, lr, [x16]
    // 0xb58ebc: blr             lr
    // 0xb58ec0: r0 = Null
    //     0xb58ec0: mov             x0, NULL
    // 0xb58ec4: LeaveFrame
    //     0xb58ec4: mov             SP, fp
    //     0xb58ec8: ldp             fp, lr, [SP], #0x10
    // 0xb58ecc: ret
    //     0xb58ecc: ret             
    // 0xb58ed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58ed0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb58ed4: b               #0xb58d6c
    // 0xb58ed8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58ed8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4087, size: 0x1c, field offset: 0xc
//   const constructor, 
class CustomizationMultiSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f0a0, size: 0x48
    // 0xc7f0a0: EnterFrame
    //     0xc7f0a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f0a4: mov             fp, SP
    // 0xc7f0a8: AllocStack(0x8)
    //     0xc7f0a8: sub             SP, SP, #8
    // 0xc7f0ac: CheckStackOverflow
    //     0xc7f0ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f0b0: cmp             SP, x16
    //     0xc7f0b4: b.ls            #0xc7f0e0
    // 0xc7f0b8: r1 = <CustomizationMultiSelect>
    //     0xc7f0b8: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e20] TypeArguments: <CustomizationMultiSelect>
    //     0xc7f0bc: ldr             x1, [x1, #0xe20]
    // 0xc7f0c0: r0 = _CustomizationMultiSelectState()
    //     0xc7f0c0: bl              #0xc7f0e8  ; Allocate_CustomizationMultiSelectStateStub -> _CustomizationMultiSelectState (size=0x1c)
    // 0xc7f0c4: mov             x1, x0
    // 0xc7f0c8: stur            x0, [fp, #-8]
    // 0xc7f0cc: r0 = _CustomizationMultiSelectState()
    //     0xc7f0cc: bl              #0xc7b854  ; [package:customer_app/app/presentation/views/basic/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_CustomizationMultiSelectState
    // 0xc7f0d0: ldur            x0, [fp, #-8]
    // 0xc7f0d4: LeaveFrame
    //     0xc7f0d4: mov             SP, fp
    //     0xc7f0d8: ldp             fp, lr, [SP], #0x10
    // 0xc7f0dc: ret
    //     0xc7f0dc: ret             
    // 0xc7f0e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f0e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f0e4: b               #0xc7f0b8
  }
}
