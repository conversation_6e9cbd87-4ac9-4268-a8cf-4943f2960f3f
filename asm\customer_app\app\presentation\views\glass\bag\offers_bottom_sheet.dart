// lib: , url: package:customer_app/app/presentation/views/glass/bag/offers_bottom_sheet.dart

// class id: 1049348, size: 0x8
class :: {
}

// class id: 3377, size: 0x14, field offset: 0x14
class _OffersBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb34490, size: 0x11a4
    // 0xb34490: EnterFrame
    //     0xb34490: stp             fp, lr, [SP, #-0x10]!
    //     0xb34494: mov             fp, SP
    // 0xb34498: AllocStack(0x90)
    //     0xb34498: sub             SP, SP, #0x90
    // 0xb3449c: SetupParameters(_OffersBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb3449c: mov             x0, x1
    //     0xb344a0: stur            x1, [fp, #-8]
    //     0xb344a4: mov             x1, x2
    //     0xb344a8: stur            x2, [fp, #-0x10]
    // 0xb344ac: CheckStackOverflow
    //     0xb344ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb344b0: cmp             SP, x16
    //     0xb344b4: b.ls            #0xb355f4
    // 0xb344b8: r1 = 2
    //     0xb344b8: movz            x1, #0x2
    // 0xb344bc: r0 = AllocateContext()
    //     0xb344bc: bl              #0x16f6108  ; AllocateContextStub
    // 0xb344c0: mov             x1, x0
    // 0xb344c4: ldur            x0, [fp, #-8]
    // 0xb344c8: stur            x1, [fp, #-0x18]
    // 0xb344cc: StoreField: r1->field_f = r0
    //     0xb344cc: stur            w0, [x1, #0xf]
    // 0xb344d0: ldur            x2, [fp, #-0x10]
    // 0xb344d4: StoreField: r1->field_13 = r2
    //     0xb344d4: stur            w2, [x1, #0x13]
    // 0xb344d8: r0 = InkWell()
    //     0xb344d8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb344dc: mov             x3, x0
    // 0xb344e0: r0 = Instance_Icon
    //     0xb344e0: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb344e4: ldr             x0, [x0, #0x2b8]
    // 0xb344e8: stur            x3, [fp, #-0x20]
    // 0xb344ec: StoreField: r3->field_b = r0
    //     0xb344ec: stur            w0, [x3, #0xb]
    // 0xb344f0: ldur            x2, [fp, #-0x18]
    // 0xb344f4: r1 = Function '<anonymous closure>':.
    //     0xb344f4: add             x1, PP, #0x71, lsl #12  ; [pp+0x714e0] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xb344f8: ldr             x1, [x1, #0x4e0]
    // 0xb344fc: r0 = AllocateClosure()
    //     0xb344fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb34500: mov             x1, x0
    // 0xb34504: ldur            x0, [fp, #-0x20]
    // 0xb34508: StoreField: r0->field_f = r1
    //     0xb34508: stur            w1, [x0, #0xf]
    // 0xb3450c: r2 = true
    //     0xb3450c: add             x2, NULL, #0x20  ; true
    // 0xb34510: StoreField: r0->field_43 = r2
    //     0xb34510: stur            w2, [x0, #0x43]
    // 0xb34514: r3 = Instance_BoxShape
    //     0xb34514: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb34518: ldr             x3, [x3, #0x80]
    // 0xb3451c: StoreField: r0->field_47 = r3
    //     0xb3451c: stur            w3, [x0, #0x47]
    // 0xb34520: StoreField: r0->field_6f = r2
    //     0xb34520: stur            w2, [x0, #0x6f]
    // 0xb34524: r4 = false
    //     0xb34524: add             x4, NULL, #0x30  ; false
    // 0xb34528: StoreField: r0->field_73 = r4
    //     0xb34528: stur            w4, [x0, #0x73]
    // 0xb3452c: StoreField: r0->field_83 = r2
    //     0xb3452c: stur            w2, [x0, #0x83]
    // 0xb34530: StoreField: r0->field_7b = r4
    //     0xb34530: stur            w4, [x0, #0x7b]
    // 0xb34534: r1 = <FlexParentData>
    //     0xb34534: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb34538: ldr             x1, [x1, #0xe00]
    // 0xb3453c: r0 = Expanded()
    //     0xb3453c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb34540: stur            x0, [fp, #-0x28]
    // 0xb34544: StoreField: r0->field_13 = rZR
    //     0xb34544: stur            xzr, [x0, #0x13]
    // 0xb34548: r2 = Instance_FlexFit
    //     0xb34548: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb3454c: ldr             x2, [x2, #0xe08]
    // 0xb34550: StoreField: r0->field_1b = r2
    //     0xb34550: stur            w2, [x0, #0x1b]
    // 0xb34554: ldur            x1, [fp, #-0x20]
    // 0xb34558: StoreField: r0->field_b = r1
    //     0xb34558: stur            w1, [x0, #0xb]
    // 0xb3455c: ldur            x1, [fp, #-0x10]
    // 0xb34560: r0 = of()
    //     0xb34560: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34564: LoadField: r1 = r0->field_87
    //     0xb34564: ldur            w1, [x0, #0x87]
    // 0xb34568: DecompressPointer r1
    //     0xb34568: add             x1, x1, HEAP, lsl #32
    // 0xb3456c: LoadField: r0 = r1->field_23
    //     0xb3456c: ldur            w0, [x1, #0x23]
    // 0xb34570: DecompressPointer r0
    //     0xb34570: add             x0, x0, HEAP, lsl #32
    // 0xb34574: r16 = Instance_Color
    //     0xb34574: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb34578: r30 = 21.000000
    //     0xb34578: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb3457c: ldr             lr, [lr, #0x9b0]
    // 0xb34580: stp             lr, x16, [SP]
    // 0xb34584: mov             x1, x0
    // 0xb34588: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb34588: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb3458c: ldr             x4, [x4, #0x9b8]
    // 0xb34590: r0 = copyWith()
    //     0xb34590: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb34594: stur            x0, [fp, #-0x10]
    // 0xb34598: r0 = Text()
    //     0xb34598: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3459c: mov             x2, x0
    // 0xb345a0: r0 = "Offer"
    //     0xb345a0: add             x0, PP, #0x71, lsl #12  ; [pp+0x71460] "Offer"
    //     0xb345a4: ldr             x0, [x0, #0x460]
    // 0xb345a8: stur            x2, [fp, #-0x20]
    // 0xb345ac: StoreField: r2->field_b = r0
    //     0xb345ac: stur            w0, [x2, #0xb]
    // 0xb345b0: ldur            x0, [fp, #-0x10]
    // 0xb345b4: StoreField: r2->field_13 = r0
    //     0xb345b4: stur            w0, [x2, #0x13]
    // 0xb345b8: r1 = <FlexParentData>
    //     0xb345b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb345bc: ldr             x1, [x1, #0xe00]
    // 0xb345c0: r0 = Expanded()
    //     0xb345c0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb345c4: stur            x0, [fp, #-0x10]
    // 0xb345c8: StoreField: r0->field_13 = rZR
    //     0xb345c8: stur            xzr, [x0, #0x13]
    // 0xb345cc: r3 = Instance_FlexFit
    //     0xb345cc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb345d0: ldr             x3, [x3, #0xe08]
    // 0xb345d4: StoreField: r0->field_1b = r3
    //     0xb345d4: stur            w3, [x0, #0x1b]
    // 0xb345d8: ldur            x1, [fp, #-0x20]
    // 0xb345dc: StoreField: r0->field_b = r1
    //     0xb345dc: stur            w1, [x0, #0xb]
    // 0xb345e0: r1 = Null
    //     0xb345e0: mov             x1, NULL
    // 0xb345e4: r2 = 4
    //     0xb345e4: movz            x2, #0x4
    // 0xb345e8: r0 = AllocateArray()
    //     0xb345e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb345ec: mov             x2, x0
    // 0xb345f0: ldur            x0, [fp, #-0x28]
    // 0xb345f4: stur            x2, [fp, #-0x20]
    // 0xb345f8: StoreField: r2->field_f = r0
    //     0xb345f8: stur            w0, [x2, #0xf]
    // 0xb345fc: ldur            x0, [fp, #-0x10]
    // 0xb34600: StoreField: r2->field_13 = r0
    //     0xb34600: stur            w0, [x2, #0x13]
    // 0xb34604: r1 = <Widget>
    //     0xb34604: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb34608: r0 = AllocateGrowableArray()
    //     0xb34608: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3460c: mov             x1, x0
    // 0xb34610: ldur            x0, [fp, #-0x20]
    // 0xb34614: stur            x1, [fp, #-0x10]
    // 0xb34618: StoreField: r1->field_f = r0
    //     0xb34618: stur            w0, [x1, #0xf]
    // 0xb3461c: r0 = 4
    //     0xb3461c: movz            x0, #0x4
    // 0xb34620: StoreField: r1->field_b = r0
    //     0xb34620: stur            w0, [x1, #0xb]
    // 0xb34624: r0 = Row()
    //     0xb34624: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb34628: mov             x1, x0
    // 0xb3462c: r0 = Instance_Axis
    //     0xb3462c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb34630: stur            x1, [fp, #-0x20]
    // 0xb34634: StoreField: r1->field_f = r0
    //     0xb34634: stur            w0, [x1, #0xf]
    // 0xb34638: r2 = Instance_MainAxisAlignment
    //     0xb34638: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb3463c: ldr             x2, [x2, #0xa08]
    // 0xb34640: StoreField: r1->field_13 = r2
    //     0xb34640: stur            w2, [x1, #0x13]
    // 0xb34644: r3 = Instance_MainAxisSize
    //     0xb34644: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb34648: ldr             x3, [x3, #0xa10]
    // 0xb3464c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb3464c: stur            w3, [x1, #0x17]
    // 0xb34650: r4 = Instance_CrossAxisAlignment
    //     0xb34650: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb34654: ldr             x4, [x4, #0xa18]
    // 0xb34658: StoreField: r1->field_1b = r4
    //     0xb34658: stur            w4, [x1, #0x1b]
    // 0xb3465c: r5 = Instance_VerticalDirection
    //     0xb3465c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb34660: ldr             x5, [x5, #0xa20]
    // 0xb34664: StoreField: r1->field_23 = r5
    //     0xb34664: stur            w5, [x1, #0x23]
    // 0xb34668: r6 = Instance_Clip
    //     0xb34668: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb3466c: ldr             x6, [x6, #0x38]
    // 0xb34670: StoreField: r1->field_2b = r6
    //     0xb34670: stur            w6, [x1, #0x2b]
    // 0xb34674: StoreField: r1->field_2f = rZR
    //     0xb34674: stur            xzr, [x1, #0x2f]
    // 0xb34678: ldur            x7, [fp, #-0x10]
    // 0xb3467c: StoreField: r1->field_b = r7
    //     0xb3467c: stur            w7, [x1, #0xb]
    // 0xb34680: r0 = Padding()
    //     0xb34680: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34684: mov             x2, x0
    // 0xb34688: r0 = Instance_EdgeInsets
    //     0xb34688: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb3468c: ldr             x0, [x0, #0x1f0]
    // 0xb34690: stur            x2, [fp, #-0x10]
    // 0xb34694: StoreField: r2->field_f = r0
    //     0xb34694: stur            w0, [x2, #0xf]
    // 0xb34698: ldur            x0, [fp, #-0x20]
    // 0xb3469c: StoreField: r2->field_b = r0
    //     0xb3469c: stur            w0, [x2, #0xb]
    // 0xb346a0: ldur            x0, [fp, #-0x18]
    // 0xb346a4: LoadField: r1 = r0->field_13
    //     0xb346a4: ldur            w1, [x0, #0x13]
    // 0xb346a8: DecompressPointer r1
    //     0xb346a8: add             x1, x1, HEAP, lsl #32
    // 0xb346ac: r0 = of()
    //     0xb346ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb346b0: LoadField: r1 = r0->field_5b
    //     0xb346b0: ldur            w1, [x0, #0x5b]
    // 0xb346b4: DecompressPointer r1
    //     0xb346b4: add             x1, x1, HEAP, lsl #32
    // 0xb346b8: r0 = LoadClassIdInstr(r1)
    //     0xb346b8: ldur            x0, [x1, #-1]
    //     0xb346bc: ubfx            x0, x0, #0xc, #0x14
    // 0xb346c0: d0 = 0.030000
    //     0xb346c0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb346c4: ldr             d0, [x17, #0x238]
    // 0xb346c8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb346c8: sub             lr, x0, #0xffa
    //     0xb346cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb346d0: blr             lr
    // 0xb346d4: stur            x0, [fp, #-0x20]
    // 0xb346d8: r0 = Radius()
    //     0xb346d8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb346dc: d0 = 50.000000
    //     0xb346dc: ldr             d0, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb346e0: stur            x0, [fp, #-0x28]
    // 0xb346e4: StoreField: r0->field_7 = d0
    //     0xb346e4: stur            d0, [x0, #7]
    // 0xb346e8: StoreField: r0->field_f = d0
    //     0xb346e8: stur            d0, [x0, #0xf]
    // 0xb346ec: r0 = BorderRadius()
    //     0xb346ec: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb346f0: mov             x1, x0
    // 0xb346f4: ldur            x0, [fp, #-0x28]
    // 0xb346f8: stur            x1, [fp, #-0x30]
    // 0xb346fc: StoreField: r1->field_7 = r0
    //     0xb346fc: stur            w0, [x1, #7]
    // 0xb34700: StoreField: r1->field_b = r0
    //     0xb34700: stur            w0, [x1, #0xb]
    // 0xb34704: StoreField: r1->field_f = r0
    //     0xb34704: stur            w0, [x1, #0xf]
    // 0xb34708: StoreField: r1->field_13 = r0
    //     0xb34708: stur            w0, [x1, #0x13]
    // 0xb3470c: r0 = BoxDecoration()
    //     0xb3470c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb34710: mov             x1, x0
    // 0xb34714: ldur            x0, [fp, #-0x20]
    // 0xb34718: stur            x1, [fp, #-0x28]
    // 0xb3471c: StoreField: r1->field_7 = r0
    //     0xb3471c: stur            w0, [x1, #7]
    // 0xb34720: ldur            x0, [fp, #-0x30]
    // 0xb34724: StoreField: r1->field_13 = r0
    //     0xb34724: stur            w0, [x1, #0x13]
    // 0xb34728: r0 = Instance_BoxShape
    //     0xb34728: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3472c: ldr             x0, [x0, #0x80]
    // 0xb34730: StoreField: r1->field_23 = r0
    //     0xb34730: stur            w0, [x1, #0x23]
    // 0xb34734: r0 = Container()
    //     0xb34734: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb34738: stur            x0, [fp, #-0x20]
    // 0xb3473c: ldur            x16, [fp, #-0x28]
    // 0xb34740: r30 = 50.000000
    //     0xb34740: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xb34744: ldr             lr, [lr, #0xa90]
    // 0xb34748: stp             lr, x16, [SP, #0x10]
    // 0xb3474c: r16 = 50.000000
    //     0xb3474c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0xb34750: ldr             x16, [x16, #0xa90]
    // 0xb34754: r30 = Instance_Icon
    //     0xb34754: add             lr, PP, #0x71, lsl #12  ; [pp+0x71478] Obj!Icon@d66231
    //     0xb34758: ldr             lr, [lr, #0x478]
    // 0xb3475c: stp             lr, x16, [SP]
    // 0xb34760: mov             x1, x0
    // 0xb34764: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x2, width, 0x3, null]
    //     0xb34764: add             x4, PP, #0x71, lsl #12  ; [pp+0x714e8] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x2, "width", 0x3, Null]
    //     0xb34768: ldr             x4, [x4, #0x4e8]
    // 0xb3476c: r0 = Container()
    //     0xb3476c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb34770: r0 = Padding()
    //     0xb34770: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34774: mov             x2, x0
    // 0xb34778: r0 = Instance_EdgeInsets
    //     0xb34778: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0xb3477c: ldr             x0, [x0, #0xe18]
    // 0xb34780: stur            x2, [fp, #-0x28]
    // 0xb34784: StoreField: r2->field_f = r0
    //     0xb34784: stur            w0, [x2, #0xf]
    // 0xb34788: ldur            x0, [fp, #-0x20]
    // 0xb3478c: StoreField: r2->field_b = r0
    //     0xb3478c: stur            w0, [x2, #0xb]
    // 0xb34790: ldur            x0, [fp, #-8]
    // 0xb34794: LoadField: r1 = r0->field_b
    //     0xb34794: ldur            w1, [x0, #0xb]
    // 0xb34798: DecompressPointer r1
    //     0xb34798: add             x1, x1, HEAP, lsl #32
    // 0xb3479c: cmp             w1, NULL
    // 0xb347a0: b.eq            #0xb355fc
    // 0xb347a4: LoadField: r3 = r1->field_b
    //     0xb347a4: ldur            w3, [x1, #0xb]
    // 0xb347a8: DecompressPointer r3
    //     0xb347a8: add             x3, x3, HEAP, lsl #32
    // 0xb347ac: cmp             w3, NULL
    // 0xb347b0: b.ne            #0xb347bc
    // 0xb347b4: r1 = Null
    //     0xb347b4: mov             x1, NULL
    // 0xb347b8: b               #0xb347c4
    // 0xb347bc: LoadField: r1 = r3->field_13
    //     0xb347bc: ldur            w1, [x3, #0x13]
    // 0xb347c0: DecompressPointer r1
    //     0xb347c0: add             x1, x1, HEAP, lsl #32
    // 0xb347c4: cmp             w1, NULL
    // 0xb347c8: b.ne            #0xb347d4
    // 0xb347cc: r4 = ""
    //     0xb347cc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb347d0: b               #0xb347d8
    // 0xb347d4: mov             x4, x1
    // 0xb347d8: ldur            x3, [fp, #-0x18]
    // 0xb347dc: stur            x4, [fp, #-0x20]
    // 0xb347e0: LoadField: r1 = r3->field_13
    //     0xb347e0: ldur            w1, [x3, #0x13]
    // 0xb347e4: DecompressPointer r1
    //     0xb347e4: add             x1, x1, HEAP, lsl #32
    // 0xb347e8: r0 = of()
    //     0xb347e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb347ec: LoadField: r1 = r0->field_87
    //     0xb347ec: ldur            w1, [x0, #0x87]
    // 0xb347f0: DecompressPointer r1
    //     0xb347f0: add             x1, x1, HEAP, lsl #32
    // 0xb347f4: LoadField: r0 = r1->field_2b
    //     0xb347f4: ldur            w0, [x1, #0x2b]
    // 0xb347f8: DecompressPointer r0
    //     0xb347f8: add             x0, x0, HEAP, lsl #32
    // 0xb347fc: r16 = Instance_Color
    //     0xb347fc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb34800: r30 = 14.000000
    //     0xb34800: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb34804: ldr             lr, [lr, #0x1d8]
    // 0xb34808: stp             lr, x16, [SP]
    // 0xb3480c: mov             x1, x0
    // 0xb34810: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb34810: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb34814: ldr             x4, [x4, #0x9b8]
    // 0xb34818: r0 = copyWith()
    //     0xb34818: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb3481c: stur            x0, [fp, #-0x30]
    // 0xb34820: r0 = Text()
    //     0xb34820: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb34824: mov             x1, x0
    // 0xb34828: ldur            x0, [fp, #-0x20]
    // 0xb3482c: stur            x1, [fp, #-0x38]
    // 0xb34830: StoreField: r1->field_b = r0
    //     0xb34830: stur            w0, [x1, #0xb]
    // 0xb34834: ldur            x0, [fp, #-0x30]
    // 0xb34838: StoreField: r1->field_13 = r0
    //     0xb34838: stur            w0, [x1, #0x13]
    // 0xb3483c: r0 = Padding()
    //     0xb3483c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34840: mov             x2, x0
    // 0xb34844: r0 = Instance_EdgeInsets
    //     0xb34844: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb34848: ldr             x0, [x0, #0xd0]
    // 0xb3484c: stur            x2, [fp, #-0x20]
    // 0xb34850: StoreField: r2->field_f = r0
    //     0xb34850: stur            w0, [x2, #0xf]
    // 0xb34854: ldur            x1, [fp, #-0x38]
    // 0xb34858: StoreField: r2->field_b = r1
    //     0xb34858: stur            w1, [x2, #0xb]
    // 0xb3485c: r1 = <FlexParentData>
    //     0xb3485c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb34860: ldr             x1, [x1, #0xe00]
    // 0xb34864: r0 = Expanded()
    //     0xb34864: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb34868: stur            x0, [fp, #-0x30]
    // 0xb3486c: StoreField: r0->field_13 = rZR
    //     0xb3486c: stur            xzr, [x0, #0x13]
    // 0xb34870: r3 = Instance_FlexFit
    //     0xb34870: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb34874: ldr             x3, [x3, #0xe08]
    // 0xb34878: StoreField: r0->field_1b = r3
    //     0xb34878: stur            w3, [x0, #0x1b]
    // 0xb3487c: ldur            x1, [fp, #-0x20]
    // 0xb34880: StoreField: r0->field_b = r1
    //     0xb34880: stur            w1, [x0, #0xb]
    // 0xb34884: ldur            x4, [fp, #-8]
    // 0xb34888: LoadField: r1 = r4->field_b
    //     0xb34888: ldur            w1, [x4, #0xb]
    // 0xb3488c: DecompressPointer r1
    //     0xb3488c: add             x1, x1, HEAP, lsl #32
    // 0xb34890: cmp             w1, NULL
    // 0xb34894: b.eq            #0xb35600
    // 0xb34898: LoadField: r2 = r1->field_b
    //     0xb34898: ldur            w2, [x1, #0xb]
    // 0xb3489c: DecompressPointer r2
    //     0xb3489c: add             x2, x2, HEAP, lsl #32
    // 0xb348a0: cmp             w2, NULL
    // 0xb348a4: b.ne            #0xb348b0
    // 0xb348a8: r5 = Null
    //     0xb348a8: mov             x5, NULL
    // 0xb348ac: b               #0xb348d4
    // 0xb348b0: LoadField: r1 = r2->field_1f
    //     0xb348b0: ldur            w1, [x2, #0x1f]
    // 0xb348b4: DecompressPointer r1
    //     0xb348b4: add             x1, x1, HEAP, lsl #32
    // 0xb348b8: cmp             w1, NULL
    // 0xb348bc: b.ne            #0xb348c8
    // 0xb348c0: r1 = Null
    //     0xb348c0: mov             x1, NULL
    // 0xb348c4: b               #0xb348d0
    // 0xb348c8: LoadField: r2 = r1->field_b
    //     0xb348c8: ldur            w2, [x1, #0xb]
    // 0xb348cc: mov             x1, x2
    // 0xb348d0: mov             x5, x1
    // 0xb348d4: ldur            x2, [fp, #-0x18]
    // 0xb348d8: stur            x5, [fp, #-0x20]
    // 0xb348dc: r1 = Function '<anonymous closure>':.
    //     0xb348dc: add             x1, PP, #0x71, lsl #12  ; [pp+0x714f0] AnonymousClosure: (0xb3589c), in [package:customer_app/app/presentation/views/glass/bag/offers_bottom_sheet.dart] _OffersBottomSheetState::build (0xb34490)
    //     0xb348e0: ldr             x1, [x1, #0x4f0]
    // 0xb348e4: r0 = AllocateClosure()
    //     0xb348e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb348e8: stur            x0, [fp, #-0x38]
    // 0xb348ec: r0 = ListView()
    //     0xb348ec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb348f0: stur            x0, [fp, #-0x40]
    // 0xb348f4: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb348f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb348f8: ldr             x16, [x16, #0x1c8]
    // 0xb348fc: r30 = true
    //     0xb348fc: add             lr, NULL, #0x20  ; true
    // 0xb34900: stp             lr, x16, [SP]
    // 0xb34904: mov             x1, x0
    // 0xb34908: ldur            x2, [fp, #-0x38]
    // 0xb3490c: ldur            x3, [fp, #-0x20]
    // 0xb34910: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xb34910: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb34914: ldr             x4, [x4, #0xd18]
    // 0xb34918: r0 = ListView.builder()
    //     0xb34918: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb3491c: r0 = Padding()
    //     0xb3491c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34920: mov             x1, x0
    // 0xb34924: r0 = Instance_EdgeInsets
    //     0xb34924: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb34928: ldr             x0, [x0, #0xd0]
    // 0xb3492c: stur            x1, [fp, #-0x48]
    // 0xb34930: StoreField: r1->field_f = r0
    //     0xb34930: stur            w0, [x1, #0xf]
    // 0xb34934: ldur            x0, [fp, #-0x40]
    // 0xb34938: StoreField: r1->field_b = r0
    //     0xb34938: stur            w0, [x1, #0xb]
    // 0xb3493c: ldur            x0, [fp, #-8]
    // 0xb34940: LoadField: r2 = r0->field_b
    //     0xb34940: ldur            w2, [x0, #0xb]
    // 0xb34944: DecompressPointer r2
    //     0xb34944: add             x2, x2, HEAP, lsl #32
    // 0xb34948: cmp             w2, NULL
    // 0xb3494c: b.eq            #0xb35604
    // 0xb34950: LoadField: r3 = r2->field_b
    //     0xb34950: ldur            w3, [x2, #0xb]
    // 0xb34954: DecompressPointer r3
    //     0xb34954: add             x3, x3, HEAP, lsl #32
    // 0xb34958: stur            x3, [fp, #-0x38]
    // 0xb3495c: cmp             w3, NULL
    // 0xb34960: b.ne            #0xb3496c
    // 0xb34964: r2 = Null
    //     0xb34964: mov             x2, NULL
    // 0xb34968: b               #0xb34974
    // 0xb3496c: LoadField: r2 = r3->field_27
    //     0xb3496c: ldur            w2, [x3, #0x27]
    // 0xb34970: DecompressPointer r2
    //     0xb34970: add             x2, x2, HEAP, lsl #32
    // 0xb34974: cmp             w2, NULL
    // 0xb34978: b.eq            #0xb3498c
    // 0xb3497c: tbnz            w2, #4, #0xb3498c
    // 0xb34980: r2 = Instance_Color
    //     0xb34980: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb34984: ldr             x2, [x2, #0x858]
    // 0xb34988: b               #0xb34990
    // 0xb3498c: r2 = Instance_Color
    //     0xb3498c: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb34990: stur            x2, [fp, #-0x20]
    // 0xb34994: r0 = BoxDecoration()
    //     0xb34994: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb34998: mov             x2, x0
    // 0xb3499c: ldur            x0, [fp, #-0x20]
    // 0xb349a0: stur            x2, [fp, #-0x40]
    // 0xb349a4: StoreField: r2->field_7 = r0
    //     0xb349a4: stur            w0, [x2, #7]
    // 0xb349a8: r0 = Instance_BoxShape
    //     0xb349a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb349ac: ldr             x0, [x0, #0x80]
    // 0xb349b0: StoreField: r2->field_23 = r0
    //     0xb349b0: stur            w0, [x2, #0x23]
    // 0xb349b4: ldur            x0, [fp, #-0x38]
    // 0xb349b8: cmp             w0, NULL
    // 0xb349bc: b.ne            #0xb349c8
    // 0xb349c0: r0 = Null
    //     0xb349c0: mov             x0, NULL
    // 0xb349c4: b               #0xb349d4
    // 0xb349c8: LoadField: r1 = r0->field_1b
    //     0xb349c8: ldur            w1, [x0, #0x1b]
    // 0xb349cc: DecompressPointer r1
    //     0xb349cc: add             x1, x1, HEAP, lsl #32
    // 0xb349d0: mov             x0, x1
    // 0xb349d4: cmp             w0, NULL
    // 0xb349d8: b.ne            #0xb349e4
    // 0xb349dc: r4 = ""
    //     0xb349dc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb349e0: b               #0xb349e8
    // 0xb349e4: mov             x4, x0
    // 0xb349e8: ldur            x0, [fp, #-8]
    // 0xb349ec: ldur            x3, [fp, #-0x18]
    // 0xb349f0: stur            x4, [fp, #-0x20]
    // 0xb349f4: LoadField: r1 = r3->field_13
    //     0xb349f4: ldur            w1, [x3, #0x13]
    // 0xb349f8: DecompressPointer r1
    //     0xb349f8: add             x1, x1, HEAP, lsl #32
    // 0xb349fc: r0 = of()
    //     0xb349fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34a00: LoadField: r1 = r0->field_87
    //     0xb34a00: ldur            w1, [x0, #0x87]
    // 0xb34a04: DecompressPointer r1
    //     0xb34a04: add             x1, x1, HEAP, lsl #32
    // 0xb34a08: LoadField: r0 = r1->field_2b
    //     0xb34a08: ldur            w0, [x1, #0x2b]
    // 0xb34a0c: DecompressPointer r0
    //     0xb34a0c: add             x0, x0, HEAP, lsl #32
    // 0xb34a10: r16 = 12.000000
    //     0xb34a10: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb34a14: ldr             x16, [x16, #0x9e8]
    // 0xb34a18: r30 = Instance_Color
    //     0xb34a18: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb34a1c: stp             lr, x16, [SP]
    // 0xb34a20: mov             x1, x0
    // 0xb34a24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb34a24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb34a28: ldr             x4, [x4, #0xaa0]
    // 0xb34a2c: r0 = copyWith()
    //     0xb34a2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb34a30: stur            x0, [fp, #-0x38]
    // 0xb34a34: r0 = Text()
    //     0xb34a34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb34a38: mov             x1, x0
    // 0xb34a3c: ldur            x0, [fp, #-0x20]
    // 0xb34a40: stur            x1, [fp, #-0x50]
    // 0xb34a44: StoreField: r1->field_b = r0
    //     0xb34a44: stur            w0, [x1, #0xb]
    // 0xb34a48: ldur            x0, [fp, #-0x38]
    // 0xb34a4c: StoreField: r1->field_13 = r0
    //     0xb34a4c: stur            w0, [x1, #0x13]
    // 0xb34a50: r0 = Align()
    //     0xb34a50: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb34a54: mov             x1, x0
    // 0xb34a58: r0 = Instance_Alignment
    //     0xb34a58: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb34a5c: ldr             x0, [x0, #0xb10]
    // 0xb34a60: stur            x1, [fp, #-0x20]
    // 0xb34a64: StoreField: r1->field_f = r0
    //     0xb34a64: stur            w0, [x1, #0xf]
    // 0xb34a68: ldur            x0, [fp, #-0x50]
    // 0xb34a6c: StoreField: r1->field_b = r0
    //     0xb34a6c: stur            w0, [x1, #0xb]
    // 0xb34a70: r0 = Container()
    //     0xb34a70: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb34a74: stur            x0, [fp, #-0x38]
    // 0xb34a78: r16 = inf
    //     0xb34a78: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb34a7c: ldr             x16, [x16, #0x9f8]
    // 0xb34a80: ldur            lr, [fp, #-0x40]
    // 0xb34a84: stp             lr, x16, [SP, #8]
    // 0xb34a88: ldur            x16, [fp, #-0x20]
    // 0xb34a8c: str             x16, [SP]
    // 0xb34a90: mov             x1, x0
    // 0xb34a94: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, width, 0x1, null]
    //     0xb34a94: add             x4, PP, #0x33, lsl #12  ; [pp+0x33830] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "width", 0x1, Null]
    //     0xb34a98: ldr             x4, [x4, #0x830]
    // 0xb34a9c: r0 = Container()
    //     0xb34a9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb34aa0: r0 = SizedBox()
    //     0xb34aa0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb34aa4: mov             x1, x0
    // 0xb34aa8: r0 = 20.000000
    //     0xb34aa8: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb34aac: ldr             x0, [x0, #0xac8]
    // 0xb34ab0: stur            x1, [fp, #-0x20]
    // 0xb34ab4: StoreField: r1->field_13 = r0
    //     0xb34ab4: stur            w0, [x1, #0x13]
    // 0xb34ab8: ldur            x0, [fp, #-0x38]
    // 0xb34abc: StoreField: r1->field_b = r0
    //     0xb34abc: stur            w0, [x1, #0xb]
    // 0xb34ac0: r0 = Padding()
    //     0xb34ac0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34ac4: mov             x1, x0
    // 0xb34ac8: r0 = Instance_EdgeInsets
    //     0xb34ac8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38dc0] Obj!EdgeInsets@d58221
    //     0xb34acc: ldr             x0, [x0, #0xdc0]
    // 0xb34ad0: stur            x1, [fp, #-0x38]
    // 0xb34ad4: StoreField: r1->field_f = r0
    //     0xb34ad4: stur            w0, [x1, #0xf]
    // 0xb34ad8: ldur            x0, [fp, #-0x20]
    // 0xb34adc: StoreField: r1->field_b = r0
    //     0xb34adc: stur            w0, [x1, #0xb]
    // 0xb34ae0: ldur            x2, [fp, #-8]
    // 0xb34ae4: LoadField: r0 = r2->field_b
    //     0xb34ae4: ldur            w0, [x2, #0xb]
    // 0xb34ae8: DecompressPointer r0
    //     0xb34ae8: add             x0, x0, HEAP, lsl #32
    // 0xb34aec: cmp             w0, NULL
    // 0xb34af0: b.eq            #0xb35608
    // 0xb34af4: LoadField: r3 = r0->field_13
    //     0xb34af4: ldur            w3, [x0, #0x13]
    // 0xb34af8: DecompressPointer r3
    //     0xb34af8: add             x3, x3, HEAP, lsl #32
    // 0xb34afc: r0 = LoadClassIdInstr(r3)
    //     0xb34afc: ldur            x0, [x3, #-1]
    //     0xb34b00: ubfx            x0, x0, #0xc, #0x14
    // 0xb34b04: r16 = "checkout_offers"
    //     0xb34b04: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xb34b08: ldr             x16, [x16, #0x1c8]
    // 0xb34b0c: stp             x16, x3, [SP]
    // 0xb34b10: mov             lr, x0
    // 0xb34b14: ldr             lr, [x21, lr, lsl #3]
    // 0xb34b18: blr             lr
    // 0xb34b1c: tbz             w0, #4, #0xb34b44
    // 0xb34b20: ldur            x0, [fp, #-8]
    // 0xb34b24: LoadField: r1 = r0->field_b
    //     0xb34b24: ldur            w1, [x0, #0xb]
    // 0xb34b28: DecompressPointer r1
    //     0xb34b28: add             x1, x1, HEAP, lsl #32
    // 0xb34b2c: cmp             w1, NULL
    // 0xb34b30: b.eq            #0xb3560c
    // 0xb34b34: LoadField: r2 = r1->field_1f
    //     0xb34b34: ldur            w2, [x1, #0x1f]
    // 0xb34b38: DecompressPointer r2
    //     0xb34b38: add             x2, x2, HEAP, lsl #32
    // 0xb34b3c: mov             x3, x2
    // 0xb34b40: b               #0xb34b4c
    // 0xb34b44: ldur            x0, [fp, #-8]
    // 0xb34b48: r3 = false
    //     0xb34b48: add             x3, NULL, #0x30  ; false
    // 0xb34b4c: ldur            x2, [fp, #-0x18]
    // 0xb34b50: stur            x3, [fp, #-0x20]
    // 0xb34b54: LoadField: r1 = r2->field_13
    //     0xb34b54: ldur            w1, [x2, #0x13]
    // 0xb34b58: DecompressPointer r1
    //     0xb34b58: add             x1, x1, HEAP, lsl #32
    // 0xb34b5c: r0 = of()
    //     0xb34b5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34b60: LoadField: r1 = r0->field_5b
    //     0xb34b60: ldur            w1, [x0, #0x5b]
    // 0xb34b64: DecompressPointer r1
    //     0xb34b64: add             x1, x1, HEAP, lsl #32
    // 0xb34b68: r0 = LoadClassIdInstr(r1)
    //     0xb34b68: ldur            x0, [x1, #-1]
    //     0xb34b6c: ubfx            x0, x0, #0xc, #0x14
    // 0xb34b70: d0 = 0.030000
    //     0xb34b70: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb34b74: ldr             d0, [x17, #0x238]
    // 0xb34b78: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb34b78: sub             lr, x0, #0xffa
    //     0xb34b7c: ldr             lr, [x21, lr, lsl #3]
    //     0xb34b80: blr             lr
    // 0xb34b84: r16 = <Color>
    //     0xb34b84: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb34b88: ldr             x16, [x16, #0xf80]
    // 0xb34b8c: stp             x0, x16, [SP]
    // 0xb34b90: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb34b90: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb34b94: r0 = all()
    //     0xb34b94: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb34b98: stur            x0, [fp, #-0x40]
    // 0xb34b9c: r16 = <EdgeInsets>
    //     0xb34b9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb34ba0: ldr             x16, [x16, #0xda0]
    // 0xb34ba4: r30 = Instance_EdgeInsets
    //     0xb34ba4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb34ba8: ldr             lr, [lr, #0x1f0]
    // 0xb34bac: stp             lr, x16, [SP]
    // 0xb34bb0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb34bb0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb34bb4: r0 = all()
    //     0xb34bb4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb34bb8: stur            x0, [fp, #-0x50]
    // 0xb34bbc: r0 = Radius()
    //     0xb34bbc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb34bc0: d0 = 30.000000
    //     0xb34bc0: fmov            d0, #30.00000000
    // 0xb34bc4: stur            x0, [fp, #-0x58]
    // 0xb34bc8: StoreField: r0->field_7 = d0
    //     0xb34bc8: stur            d0, [x0, #7]
    // 0xb34bcc: StoreField: r0->field_f = d0
    //     0xb34bcc: stur            d0, [x0, #0xf]
    // 0xb34bd0: r0 = BorderRadius()
    //     0xb34bd0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb34bd4: mov             x1, x0
    // 0xb34bd8: ldur            x0, [fp, #-0x58]
    // 0xb34bdc: stur            x1, [fp, #-0x60]
    // 0xb34be0: StoreField: r1->field_7 = r0
    //     0xb34be0: stur            w0, [x1, #7]
    // 0xb34be4: StoreField: r1->field_b = r0
    //     0xb34be4: stur            w0, [x1, #0xb]
    // 0xb34be8: StoreField: r1->field_f = r0
    //     0xb34be8: stur            w0, [x1, #0xf]
    // 0xb34bec: StoreField: r1->field_13 = r0
    //     0xb34bec: stur            w0, [x1, #0x13]
    // 0xb34bf0: r0 = RoundedRectangleBorder()
    //     0xb34bf0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb34bf4: mov             x1, x0
    // 0xb34bf8: ldur            x0, [fp, #-0x60]
    // 0xb34bfc: StoreField: r1->field_b = r0
    //     0xb34bfc: stur            w0, [x1, #0xb]
    // 0xb34c00: r0 = Instance_BorderSide
    //     0xb34c00: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb34c04: ldr             x0, [x0, #0xe20]
    // 0xb34c08: StoreField: r1->field_7 = r0
    //     0xb34c08: stur            w0, [x1, #7]
    // 0xb34c0c: r16 = <RoundedRectangleBorder>
    //     0xb34c0c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb34c10: ldr             x16, [x16, #0xf78]
    // 0xb34c14: stp             x1, x16, [SP]
    // 0xb34c18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb34c18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb34c1c: r0 = all()
    //     0xb34c1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb34c20: stur            x0, [fp, #-0x58]
    // 0xb34c24: r0 = ButtonStyle()
    //     0xb34c24: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb34c28: mov             x1, x0
    // 0xb34c2c: ldur            x0, [fp, #-0x40]
    // 0xb34c30: stur            x1, [fp, #-0x60]
    // 0xb34c34: StoreField: r1->field_b = r0
    //     0xb34c34: stur            w0, [x1, #0xb]
    // 0xb34c38: ldur            x0, [fp, #-0x50]
    // 0xb34c3c: StoreField: r1->field_23 = r0
    //     0xb34c3c: stur            w0, [x1, #0x23]
    // 0xb34c40: ldur            x0, [fp, #-0x58]
    // 0xb34c44: StoreField: r1->field_43 = r0
    //     0xb34c44: stur            w0, [x1, #0x43]
    // 0xb34c48: r0 = TextButtonThemeData()
    //     0xb34c48: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb34c4c: mov             x2, x0
    // 0xb34c50: ldur            x0, [fp, #-0x60]
    // 0xb34c54: stur            x2, [fp, #-0x40]
    // 0xb34c58: StoreField: r2->field_7 = r0
    //     0xb34c58: stur            w0, [x2, #7]
    // 0xb34c5c: r1 = "other offers"
    //     0xb34c5c: add             x1, PP, #0x71, lsl #12  ; [pp+0x714f8] "other offers"
    //     0xb34c60: ldr             x1, [x1, #0x4f8]
    // 0xb34c64: r0 = capitalizeFirstWord()
    //     0xb34c64: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb34c68: ldur            x2, [fp, #-0x18]
    // 0xb34c6c: stur            x0, [fp, #-0x50]
    // 0xb34c70: LoadField: r1 = r2->field_13
    //     0xb34c70: ldur            w1, [x2, #0x13]
    // 0xb34c74: DecompressPointer r1
    //     0xb34c74: add             x1, x1, HEAP, lsl #32
    // 0xb34c78: r0 = of()
    //     0xb34c78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34c7c: LoadField: r1 = r0->field_87
    //     0xb34c7c: ldur            w1, [x0, #0x87]
    // 0xb34c80: DecompressPointer r1
    //     0xb34c80: add             x1, x1, HEAP, lsl #32
    // 0xb34c84: LoadField: r0 = r1->field_7
    //     0xb34c84: ldur            w0, [x1, #7]
    // 0xb34c88: DecompressPointer r0
    //     0xb34c88: add             x0, x0, HEAP, lsl #32
    // 0xb34c8c: ldur            x2, [fp, #-0x18]
    // 0xb34c90: stur            x0, [fp, #-0x58]
    // 0xb34c94: LoadField: r1 = r2->field_13
    //     0xb34c94: ldur            w1, [x2, #0x13]
    // 0xb34c98: DecompressPointer r1
    //     0xb34c98: add             x1, x1, HEAP, lsl #32
    // 0xb34c9c: r0 = of()
    //     0xb34c9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34ca0: LoadField: r1 = r0->field_5b
    //     0xb34ca0: ldur            w1, [x0, #0x5b]
    // 0xb34ca4: DecompressPointer r1
    //     0xb34ca4: add             x1, x1, HEAP, lsl #32
    // 0xb34ca8: r16 = 14.000000
    //     0xb34ca8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb34cac: ldr             x16, [x16, #0x1d8]
    // 0xb34cb0: stp             x1, x16, [SP]
    // 0xb34cb4: ldur            x1, [fp, #-0x58]
    // 0xb34cb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb34cb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb34cbc: ldr             x4, [x4, #0xaa0]
    // 0xb34cc0: r0 = copyWith()
    //     0xb34cc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb34cc4: stur            x0, [fp, #-0x58]
    // 0xb34cc8: r0 = Text()
    //     0xb34cc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb34ccc: mov             x3, x0
    // 0xb34cd0: ldur            x0, [fp, #-0x50]
    // 0xb34cd4: stur            x3, [fp, #-0x60]
    // 0xb34cd8: StoreField: r3->field_b = r0
    //     0xb34cd8: stur            w0, [x3, #0xb]
    // 0xb34cdc: ldur            x0, [fp, #-0x58]
    // 0xb34ce0: StoreField: r3->field_13 = r0
    //     0xb34ce0: stur            w0, [x3, #0x13]
    // 0xb34ce4: ldur            x2, [fp, #-0x18]
    // 0xb34ce8: r1 = Function '<anonymous closure>':.
    //     0xb34ce8: add             x1, PP, #0x71, lsl #12  ; [pp+0x71500] AnonymousClosure: (0x98bce4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xb34cec: ldr             x1, [x1, #0x500]
    // 0xb34cf0: r0 = AllocateClosure()
    //     0xb34cf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb34cf4: stur            x0, [fp, #-0x50]
    // 0xb34cf8: r0 = TextButton()
    //     0xb34cf8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb34cfc: mov             x1, x0
    // 0xb34d00: ldur            x0, [fp, #-0x50]
    // 0xb34d04: stur            x1, [fp, #-0x58]
    // 0xb34d08: StoreField: r1->field_b = r0
    //     0xb34d08: stur            w0, [x1, #0xb]
    // 0xb34d0c: r0 = false
    //     0xb34d0c: add             x0, NULL, #0x30  ; false
    // 0xb34d10: StoreField: r1->field_27 = r0
    //     0xb34d10: stur            w0, [x1, #0x27]
    // 0xb34d14: r2 = true
    //     0xb34d14: add             x2, NULL, #0x20  ; true
    // 0xb34d18: StoreField: r1->field_2f = r2
    //     0xb34d18: stur            w2, [x1, #0x2f]
    // 0xb34d1c: ldur            x3, [fp, #-0x60]
    // 0xb34d20: StoreField: r1->field_37 = r3
    //     0xb34d20: stur            w3, [x1, #0x37]
    // 0xb34d24: r0 = TextButtonTheme()
    //     0xb34d24: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb34d28: mov             x2, x0
    // 0xb34d2c: ldur            x0, [fp, #-0x40]
    // 0xb34d30: stur            x2, [fp, #-0x50]
    // 0xb34d34: StoreField: r2->field_f = r0
    //     0xb34d34: stur            w0, [x2, #0xf]
    // 0xb34d38: ldur            x0, [fp, #-0x58]
    // 0xb34d3c: StoreField: r2->field_b = r0
    //     0xb34d3c: stur            w0, [x2, #0xb]
    // 0xb34d40: r1 = <FlexParentData>
    //     0xb34d40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb34d44: ldr             x1, [x1, #0xe00]
    // 0xb34d48: r0 = Expanded()
    //     0xb34d48: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb34d4c: mov             x1, x0
    // 0xb34d50: r0 = 1
    //     0xb34d50: movz            x0, #0x1
    // 0xb34d54: stur            x1, [fp, #-0x40]
    // 0xb34d58: StoreField: r1->field_13 = r0
    //     0xb34d58: stur            x0, [x1, #0x13]
    // 0xb34d5c: r2 = Instance_FlexFit
    //     0xb34d5c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb34d60: ldr             x2, [x2, #0xe08]
    // 0xb34d64: StoreField: r1->field_1b = r2
    //     0xb34d64: stur            w2, [x1, #0x1b]
    // 0xb34d68: ldur            x3, [fp, #-0x50]
    // 0xb34d6c: StoreField: r1->field_b = r3
    //     0xb34d6c: stur            w3, [x1, #0xb]
    // 0xb34d70: r0 = Visibility()
    //     0xb34d70: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb34d74: mov             x1, x0
    // 0xb34d78: ldur            x0, [fp, #-0x40]
    // 0xb34d7c: stur            x1, [fp, #-0x50]
    // 0xb34d80: StoreField: r1->field_b = r0
    //     0xb34d80: stur            w0, [x1, #0xb]
    // 0xb34d84: r2 = Instance_SizedBox
    //     0xb34d84: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb34d88: StoreField: r1->field_f = r2
    //     0xb34d88: stur            w2, [x1, #0xf]
    // 0xb34d8c: ldur            x0, [fp, #-0x20]
    // 0xb34d90: StoreField: r1->field_13 = r0
    //     0xb34d90: stur            w0, [x1, #0x13]
    // 0xb34d94: r3 = false
    //     0xb34d94: add             x3, NULL, #0x30  ; false
    // 0xb34d98: ArrayStore: r1[0] = r3  ; List_4
    //     0xb34d98: stur            w3, [x1, #0x17]
    // 0xb34d9c: StoreField: r1->field_1b = r3
    //     0xb34d9c: stur            w3, [x1, #0x1b]
    // 0xb34da0: StoreField: r1->field_1f = r3
    //     0xb34da0: stur            w3, [x1, #0x1f]
    // 0xb34da4: StoreField: r1->field_23 = r3
    //     0xb34da4: stur            w3, [x1, #0x23]
    // 0xb34da8: StoreField: r1->field_27 = r3
    //     0xb34da8: stur            w3, [x1, #0x27]
    // 0xb34dac: StoreField: r1->field_2b = r3
    //     0xb34dac: stur            w3, [x1, #0x2b]
    // 0xb34db0: ldur            x4, [fp, #-8]
    // 0xb34db4: LoadField: r0 = r4->field_b
    //     0xb34db4: ldur            w0, [x4, #0xb]
    // 0xb34db8: DecompressPointer r0
    //     0xb34db8: add             x0, x0, HEAP, lsl #32
    // 0xb34dbc: cmp             w0, NULL
    // 0xb34dc0: b.eq            #0xb35610
    // 0xb34dc4: LoadField: r5 = r0->field_13
    //     0xb34dc4: ldur            w5, [x0, #0x13]
    // 0xb34dc8: DecompressPointer r5
    //     0xb34dc8: add             x5, x5, HEAP, lsl #32
    // 0xb34dcc: r0 = LoadClassIdInstr(r5)
    //     0xb34dcc: ldur            x0, [x5, #-1]
    //     0xb34dd0: ubfx            x0, x0, #0xc, #0x14
    // 0xb34dd4: r16 = "checkout_offers"
    //     0xb34dd4: add             x16, PP, #0x57, lsl #12  ; [pp+0x571c8] "checkout_offers"
    //     0xb34dd8: ldr             x16, [x16, #0x1c8]
    // 0xb34ddc: stp             x16, x5, [SP]
    // 0xb34de0: mov             lr, x0
    // 0xb34de4: ldr             lr, [x21, lr, lsl #3]
    // 0xb34de8: blr             lr
    // 0xb34dec: tbz             w0, #4, #0xb34e14
    // 0xb34df0: ldur            x0, [fp, #-8]
    // 0xb34df4: LoadField: r1 = r0->field_b
    //     0xb34df4: ldur            w1, [x0, #0xb]
    // 0xb34df8: DecompressPointer r1
    //     0xb34df8: add             x1, x1, HEAP, lsl #32
    // 0xb34dfc: cmp             w1, NULL
    // 0xb34e00: b.eq            #0xb35614
    // 0xb34e04: LoadField: r2 = r1->field_1f
    //     0xb34e04: ldur            w2, [x1, #0x1f]
    // 0xb34e08: DecompressPointer r2
    //     0xb34e08: add             x2, x2, HEAP, lsl #32
    // 0xb34e0c: mov             x1, x2
    // 0xb34e10: b               #0xb34e1c
    // 0xb34e14: ldur            x0, [fp, #-8]
    // 0xb34e18: r1 = false
    //     0xb34e18: add             x1, NULL, #0x30  ; false
    // 0xb34e1c: stur            x1, [fp, #-0x20]
    // 0xb34e20: r16 = <EdgeInsets>
    //     0xb34e20: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb34e24: ldr             x16, [x16, #0xda0]
    // 0xb34e28: r30 = Instance_EdgeInsets
    //     0xb34e28: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb34e2c: ldr             lr, [lr, #0x1f0]
    // 0xb34e30: stp             lr, x16, [SP]
    // 0xb34e34: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb34e34: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb34e38: r0 = all()
    //     0xb34e38: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb34e3c: mov             x2, x0
    // 0xb34e40: ldur            x1, [fp, #-8]
    // 0xb34e44: stur            x2, [fp, #-0x40]
    // 0xb34e48: LoadField: r0 = r1->field_b
    //     0xb34e48: ldur            w0, [x1, #0xb]
    // 0xb34e4c: DecompressPointer r0
    //     0xb34e4c: add             x0, x0, HEAP, lsl #32
    // 0xb34e50: cmp             w0, NULL
    // 0xb34e54: b.eq            #0xb35618
    // 0xb34e58: LoadField: r3 = r0->field_f
    //     0xb34e58: ldur            w3, [x0, #0xf]
    // 0xb34e5c: DecompressPointer r3
    //     0xb34e5c: add             x3, x3, HEAP, lsl #32
    // 0xb34e60: LoadField: r4 = r0->field_b
    //     0xb34e60: ldur            w4, [x0, #0xb]
    // 0xb34e64: DecompressPointer r4
    //     0xb34e64: add             x4, x4, HEAP, lsl #32
    // 0xb34e68: cmp             w4, NULL
    // 0xb34e6c: b.ne            #0xb34e78
    // 0xb34e70: r0 = Null
    //     0xb34e70: mov             x0, NULL
    // 0xb34e74: b               #0xb34e80
    // 0xb34e78: LoadField: r0 = r4->field_7
    //     0xb34e78: ldur            w0, [x4, #7]
    // 0xb34e7c: DecompressPointer r0
    //     0xb34e7c: add             x0, x0, HEAP, lsl #32
    // 0xb34e80: r4 = LoadClassIdInstr(r3)
    //     0xb34e80: ldur            x4, [x3, #-1]
    //     0xb34e84: ubfx            x4, x4, #0xc, #0x14
    // 0xb34e88: stp             x0, x3, [SP]
    // 0xb34e8c: mov             x0, x4
    // 0xb34e90: mov             lr, x0
    // 0xb34e94: ldr             lr, [x21, lr, lsl #3]
    // 0xb34e98: blr             lr
    // 0xb34e9c: tbnz            w0, #4, #0xb34ea8
    // 0xb34ea0: r1 = Instance_Color
    //     0xb34ea0: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb34ea4: b               #0xb34f1c
    // 0xb34ea8: ldur            x0, [fp, #-8]
    // 0xb34eac: LoadField: r1 = r0->field_b
    //     0xb34eac: ldur            w1, [x0, #0xb]
    // 0xb34eb0: DecompressPointer r1
    //     0xb34eb0: add             x1, x1, HEAP, lsl #32
    // 0xb34eb4: cmp             w1, NULL
    // 0xb34eb8: b.eq            #0xb3561c
    // 0xb34ebc: LoadField: r2 = r1->field_b
    //     0xb34ebc: ldur            w2, [x1, #0xb]
    // 0xb34ec0: DecompressPointer r2
    //     0xb34ec0: add             x2, x2, HEAP, lsl #32
    // 0xb34ec4: cmp             w2, NULL
    // 0xb34ec8: b.ne            #0xb34ed4
    // 0xb34ecc: r1 = Null
    //     0xb34ecc: mov             x1, NULL
    // 0xb34ed0: b               #0xb34edc
    // 0xb34ed4: LoadField: r1 = r2->field_27
    //     0xb34ed4: ldur            w1, [x2, #0x27]
    // 0xb34ed8: DecompressPointer r1
    //     0xb34ed8: add             x1, x1, HEAP, lsl #32
    // 0xb34edc: cmp             w1, NULL
    // 0xb34ee0: b.eq            #0xb34f08
    // 0xb34ee4: tbnz            w1, #4, #0xb34f08
    // 0xb34ee8: ldur            x2, [fp, #-0x18]
    // 0xb34eec: LoadField: r1 = r2->field_13
    //     0xb34eec: ldur            w1, [x2, #0x13]
    // 0xb34ef0: DecompressPointer r1
    //     0xb34ef0: add             x1, x1, HEAP, lsl #32
    // 0xb34ef4: r0 = of()
    //     0xb34ef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34ef8: LoadField: r1 = r0->field_5b
    //     0xb34ef8: ldur            w1, [x0, #0x5b]
    // 0xb34efc: DecompressPointer r1
    //     0xb34efc: add             x1, x1, HEAP, lsl #32
    // 0xb34f00: mov             x0, x1
    // 0xb34f04: b               #0xb34f18
    // 0xb34f08: r1 = Instance_MaterialColor
    //     0xb34f08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb34f0c: ldr             x1, [x1, #0xdc0]
    // 0xb34f10: d0 = 0.200000
    //     0xb34f10: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb34f14: r0 = withOpacity()
    //     0xb34f14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb34f18: mov             x1, x0
    // 0xb34f1c: ldur            x0, [fp, #-8]
    // 0xb34f20: r16 = <Color>
    //     0xb34f20: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb34f24: ldr             x16, [x16, #0xf80]
    // 0xb34f28: stp             x1, x16, [SP]
    // 0xb34f2c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb34f2c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb34f30: r0 = all()
    //     0xb34f30: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb34f34: mov             x2, x0
    // 0xb34f38: ldur            x1, [fp, #-8]
    // 0xb34f3c: stur            x2, [fp, #-0x58]
    // 0xb34f40: LoadField: r0 = r1->field_b
    //     0xb34f40: ldur            w0, [x1, #0xb]
    // 0xb34f44: DecompressPointer r0
    //     0xb34f44: add             x0, x0, HEAP, lsl #32
    // 0xb34f48: cmp             w0, NULL
    // 0xb34f4c: b.eq            #0xb35620
    // 0xb34f50: LoadField: r3 = r0->field_f
    //     0xb34f50: ldur            w3, [x0, #0xf]
    // 0xb34f54: DecompressPointer r3
    //     0xb34f54: add             x3, x3, HEAP, lsl #32
    // 0xb34f58: LoadField: r4 = r0->field_b
    //     0xb34f58: ldur            w4, [x0, #0xb]
    // 0xb34f5c: DecompressPointer r4
    //     0xb34f5c: add             x4, x4, HEAP, lsl #32
    // 0xb34f60: cmp             w4, NULL
    // 0xb34f64: b.ne            #0xb34f70
    // 0xb34f68: r0 = Null
    //     0xb34f68: mov             x0, NULL
    // 0xb34f6c: b               #0xb34f78
    // 0xb34f70: LoadField: r0 = r4->field_7
    //     0xb34f70: ldur            w0, [x4, #7]
    // 0xb34f74: DecompressPointer r0
    //     0xb34f74: add             x0, x0, HEAP, lsl #32
    // 0xb34f78: r4 = LoadClassIdInstr(r3)
    //     0xb34f78: ldur            x4, [x3, #-1]
    //     0xb34f7c: ubfx            x4, x4, #0xc, #0x14
    // 0xb34f80: stp             x0, x3, [SP]
    // 0xb34f84: mov             x0, x4
    // 0xb34f88: mov             lr, x0
    // 0xb34f8c: ldr             lr, [x21, lr, lsl #3]
    // 0xb34f90: blr             lr
    // 0xb34f94: tbnz            w0, #4, #0xb35038
    // 0xb34f98: ldur            x2, [fp, #-0x18]
    // 0xb34f9c: LoadField: r1 = r2->field_13
    //     0xb34f9c: ldur            w1, [x2, #0x13]
    // 0xb34fa0: DecompressPointer r1
    //     0xb34fa0: add             x1, x1, HEAP, lsl #32
    // 0xb34fa4: r0 = of()
    //     0xb34fa4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb34fa8: LoadField: r1 = r0->field_5b
    //     0xb34fa8: ldur            w1, [x0, #0x5b]
    // 0xb34fac: DecompressPointer r1
    //     0xb34fac: add             x1, x1, HEAP, lsl #32
    // 0xb34fb0: stur            x1, [fp, #-0x60]
    // 0xb34fb4: r0 = BorderSide()
    //     0xb34fb4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb34fb8: mov             x1, x0
    // 0xb34fbc: ldur            x0, [fp, #-0x60]
    // 0xb34fc0: stur            x1, [fp, #-0x68]
    // 0xb34fc4: StoreField: r1->field_7 = r0
    //     0xb34fc4: stur            w0, [x1, #7]
    // 0xb34fc8: d0 = 1.000000
    //     0xb34fc8: fmov            d0, #1.00000000
    // 0xb34fcc: StoreField: r1->field_b = d0
    //     0xb34fcc: stur            d0, [x1, #0xb]
    // 0xb34fd0: r0 = Instance_BorderStyle
    //     0xb34fd0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb34fd4: ldr             x0, [x0, #0xf68]
    // 0xb34fd8: StoreField: r1->field_13 = r0
    //     0xb34fd8: stur            w0, [x1, #0x13]
    // 0xb34fdc: d0 = -1.000000
    //     0xb34fdc: fmov            d0, #-1.00000000
    // 0xb34fe0: ArrayStore: r1[0] = d0  ; List_8
    //     0xb34fe0: stur            d0, [x1, #0x17]
    // 0xb34fe4: r0 = Radius()
    //     0xb34fe4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb34fe8: d0 = 30.000000
    //     0xb34fe8: fmov            d0, #30.00000000
    // 0xb34fec: stur            x0, [fp, #-0x60]
    // 0xb34ff0: StoreField: r0->field_7 = d0
    //     0xb34ff0: stur            d0, [x0, #7]
    // 0xb34ff4: StoreField: r0->field_f = d0
    //     0xb34ff4: stur            d0, [x0, #0xf]
    // 0xb34ff8: r0 = BorderRadius()
    //     0xb34ff8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb34ffc: mov             x1, x0
    // 0xb35000: ldur            x0, [fp, #-0x60]
    // 0xb35004: stur            x1, [fp, #-0x70]
    // 0xb35008: StoreField: r1->field_7 = r0
    //     0xb35008: stur            w0, [x1, #7]
    // 0xb3500c: StoreField: r1->field_b = r0
    //     0xb3500c: stur            w0, [x1, #0xb]
    // 0xb35010: StoreField: r1->field_f = r0
    //     0xb35010: stur            w0, [x1, #0xf]
    // 0xb35014: StoreField: r1->field_13 = r0
    //     0xb35014: stur            w0, [x1, #0x13]
    // 0xb35018: r0 = RoundedRectangleBorder()
    //     0xb35018: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb3501c: mov             x1, x0
    // 0xb35020: ldur            x0, [fp, #-0x70]
    // 0xb35024: StoreField: r1->field_b = r0
    //     0xb35024: stur            w0, [x1, #0xb]
    // 0xb35028: ldur            x0, [fp, #-0x68]
    // 0xb3502c: StoreField: r1->field_7 = r0
    //     0xb3502c: stur            w0, [x1, #7]
    // 0xb35030: mov             x3, x1
    // 0xb35034: b               #0xb35090
    // 0xb35038: d0 = 30.000000
    //     0xb35038: fmov            d0, #30.00000000
    // 0xb3503c: r0 = Radius()
    //     0xb3503c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb35040: d0 = 30.000000
    //     0xb35040: fmov            d0, #30.00000000
    // 0xb35044: stur            x0, [fp, #-0x60]
    // 0xb35048: StoreField: r0->field_7 = d0
    //     0xb35048: stur            d0, [x0, #7]
    // 0xb3504c: StoreField: r0->field_f = d0
    //     0xb3504c: stur            d0, [x0, #0xf]
    // 0xb35050: r0 = BorderRadius()
    //     0xb35050: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb35054: mov             x1, x0
    // 0xb35058: ldur            x0, [fp, #-0x60]
    // 0xb3505c: stur            x1, [fp, #-0x68]
    // 0xb35060: StoreField: r1->field_7 = r0
    //     0xb35060: stur            w0, [x1, #7]
    // 0xb35064: StoreField: r1->field_b = r0
    //     0xb35064: stur            w0, [x1, #0xb]
    // 0xb35068: StoreField: r1->field_f = r0
    //     0xb35068: stur            w0, [x1, #0xf]
    // 0xb3506c: StoreField: r1->field_13 = r0
    //     0xb3506c: stur            w0, [x1, #0x13]
    // 0xb35070: r0 = RoundedRectangleBorder()
    //     0xb35070: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb35074: mov             x1, x0
    // 0xb35078: ldur            x0, [fp, #-0x68]
    // 0xb3507c: StoreField: r1->field_b = r0
    //     0xb3507c: stur            w0, [x1, #0xb]
    // 0xb35080: r0 = Instance_BorderSide
    //     0xb35080: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb35084: ldr             x0, [x0, #0xe20]
    // 0xb35088: StoreField: r1->field_7 = r0
    //     0xb35088: stur            w0, [x1, #7]
    // 0xb3508c: mov             x3, x1
    // 0xb35090: ldur            x0, [fp, #-8]
    // 0xb35094: ldur            x2, [fp, #-0x40]
    // 0xb35098: ldur            x1, [fp, #-0x58]
    // 0xb3509c: r16 = <RoundedRectangleBorder>
    //     0xb3509c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb350a0: ldr             x16, [x16, #0xf78]
    // 0xb350a4: stp             x3, x16, [SP]
    // 0xb350a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb350a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb350ac: r0 = all()
    //     0xb350ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb350b0: stur            x0, [fp, #-0x60]
    // 0xb350b4: r0 = ButtonStyle()
    //     0xb350b4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb350b8: mov             x1, x0
    // 0xb350bc: ldur            x0, [fp, #-0x58]
    // 0xb350c0: stur            x1, [fp, #-0x68]
    // 0xb350c4: StoreField: r1->field_b = r0
    //     0xb350c4: stur            w0, [x1, #0xb]
    // 0xb350c8: ldur            x0, [fp, #-0x40]
    // 0xb350cc: StoreField: r1->field_23 = r0
    //     0xb350cc: stur            w0, [x1, #0x23]
    // 0xb350d0: ldur            x0, [fp, #-0x60]
    // 0xb350d4: StoreField: r1->field_43 = r0
    //     0xb350d4: stur            w0, [x1, #0x43]
    // 0xb350d8: r0 = TextButtonThemeData()
    //     0xb350d8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb350dc: mov             x1, x0
    // 0xb350e0: ldur            x0, [fp, #-0x68]
    // 0xb350e4: stur            x1, [fp, #-0x40]
    // 0xb350e8: StoreField: r1->field_7 = r0
    //     0xb350e8: stur            w0, [x1, #7]
    // 0xb350ec: ldur            x2, [fp, #-8]
    // 0xb350f0: LoadField: r0 = r2->field_b
    //     0xb350f0: ldur            w0, [x2, #0xb]
    // 0xb350f4: DecompressPointer r0
    //     0xb350f4: add             x0, x0, HEAP, lsl #32
    // 0xb350f8: cmp             w0, NULL
    // 0xb350fc: b.eq            #0xb35624
    // 0xb35100: LoadField: r3 = r0->field_f
    //     0xb35100: ldur            w3, [x0, #0xf]
    // 0xb35104: DecompressPointer r3
    //     0xb35104: add             x3, x3, HEAP, lsl #32
    // 0xb35108: LoadField: r4 = r0->field_b
    //     0xb35108: ldur            w4, [x0, #0xb]
    // 0xb3510c: DecompressPointer r4
    //     0xb3510c: add             x4, x4, HEAP, lsl #32
    // 0xb35110: cmp             w4, NULL
    // 0xb35114: b.ne            #0xb35120
    // 0xb35118: r0 = Null
    //     0xb35118: mov             x0, NULL
    // 0xb3511c: b               #0xb35128
    // 0xb35120: LoadField: r0 = r4->field_7
    //     0xb35120: ldur            w0, [x4, #7]
    // 0xb35124: DecompressPointer r0
    //     0xb35124: add             x0, x0, HEAP, lsl #32
    // 0xb35128: r4 = LoadClassIdInstr(r3)
    //     0xb35128: ldur            x4, [x3, #-1]
    //     0xb3512c: ubfx            x4, x4, #0xc, #0x14
    // 0xb35130: stp             x0, x3, [SP]
    // 0xb35134: mov             x0, x4
    // 0xb35138: mov             lr, x0
    // 0xb3513c: ldr             lr, [x21, lr, lsl #3]
    // 0xb35140: blr             lr
    // 0xb35144: tbnz            w0, #4, #0xb35298
    // 0xb35148: ldur            x0, [fp, #-8]
    // 0xb3514c: ldur            x2, [fp, #-0x18]
    // 0xb35150: r1 = "remove offer"
    //     0xb35150: add             x1, PP, #0x71, lsl #12  ; [pp+0x71508] "remove offer"
    //     0xb35154: ldr             x1, [x1, #0x508]
    // 0xb35158: r0 = capitalizeFirstWord()
    //     0xb35158: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb3515c: ldur            x2, [fp, #-0x18]
    // 0xb35160: stur            x0, [fp, #-0x58]
    // 0xb35164: LoadField: r1 = r2->field_13
    //     0xb35164: ldur            w1, [x2, #0x13]
    // 0xb35168: DecompressPointer r1
    //     0xb35168: add             x1, x1, HEAP, lsl #32
    // 0xb3516c: r0 = of()
    //     0xb3516c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb35170: LoadField: r1 = r0->field_87
    //     0xb35170: ldur            w1, [x0, #0x87]
    // 0xb35174: DecompressPointer r1
    //     0xb35174: add             x1, x1, HEAP, lsl #32
    // 0xb35178: LoadField: r2 = r1->field_7
    //     0xb35178: ldur            w2, [x1, #7]
    // 0xb3517c: DecompressPointer r2
    //     0xb3517c: add             x2, x2, HEAP, lsl #32
    // 0xb35180: ldur            x1, [fp, #-8]
    // 0xb35184: stur            x2, [fp, #-0x60]
    // 0xb35188: LoadField: r0 = r1->field_b
    //     0xb35188: ldur            w0, [x1, #0xb]
    // 0xb3518c: DecompressPointer r0
    //     0xb3518c: add             x0, x0, HEAP, lsl #32
    // 0xb35190: cmp             w0, NULL
    // 0xb35194: b.eq            #0xb35628
    // 0xb35198: LoadField: r3 = r0->field_f
    //     0xb35198: ldur            w3, [x0, #0xf]
    // 0xb3519c: DecompressPointer r3
    //     0xb3519c: add             x3, x3, HEAP, lsl #32
    // 0xb351a0: LoadField: r4 = r0->field_b
    //     0xb351a0: ldur            w4, [x0, #0xb]
    // 0xb351a4: DecompressPointer r4
    //     0xb351a4: add             x4, x4, HEAP, lsl #32
    // 0xb351a8: cmp             w4, NULL
    // 0xb351ac: b.ne            #0xb351b8
    // 0xb351b0: r0 = Null
    //     0xb351b0: mov             x0, NULL
    // 0xb351b4: b               #0xb351c0
    // 0xb351b8: LoadField: r0 = r4->field_7
    //     0xb351b8: ldur            w0, [x4, #7]
    // 0xb351bc: DecompressPointer r0
    //     0xb351bc: add             x0, x0, HEAP, lsl #32
    // 0xb351c0: r4 = LoadClassIdInstr(r3)
    //     0xb351c0: ldur            x4, [x3, #-1]
    //     0xb351c4: ubfx            x4, x4, #0xc, #0x14
    // 0xb351c8: stp             x0, x3, [SP]
    // 0xb351cc: mov             x0, x4
    // 0xb351d0: mov             lr, x0
    // 0xb351d4: ldr             lr, [x21, lr, lsl #3]
    // 0xb351d8: blr             lr
    // 0xb351dc: tbnz            w0, #4, #0xb351fc
    // 0xb351e0: ldur            x2, [fp, #-0x18]
    // 0xb351e4: LoadField: r1 = r2->field_13
    //     0xb351e4: ldur            w1, [x2, #0x13]
    // 0xb351e8: DecompressPointer r1
    //     0xb351e8: add             x1, x1, HEAP, lsl #32
    // 0xb351ec: r0 = of()
    //     0xb351ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb351f0: LoadField: r1 = r0->field_5b
    //     0xb351f0: ldur            w1, [x0, #0x5b]
    // 0xb351f4: DecompressPointer r1
    //     0xb351f4: add             x1, x1, HEAP, lsl #32
    // 0xb351f8: b               #0xb35254
    // 0xb351fc: ldur            x0, [fp, #-8]
    // 0xb35200: LoadField: r1 = r0->field_b
    //     0xb35200: ldur            w1, [x0, #0xb]
    // 0xb35204: DecompressPointer r1
    //     0xb35204: add             x1, x1, HEAP, lsl #32
    // 0xb35208: cmp             w1, NULL
    // 0xb3520c: b.eq            #0xb3562c
    // 0xb35210: LoadField: r0 = r1->field_b
    //     0xb35210: ldur            w0, [x1, #0xb]
    // 0xb35214: DecompressPointer r0
    //     0xb35214: add             x0, x0, HEAP, lsl #32
    // 0xb35218: cmp             w0, NULL
    // 0xb3521c: b.ne            #0xb35228
    // 0xb35220: r0 = Null
    //     0xb35220: mov             x0, NULL
    // 0xb35224: b               #0xb35234
    // 0xb35228: LoadField: r1 = r0->field_27
    //     0xb35228: ldur            w1, [x0, #0x27]
    // 0xb3522c: DecompressPointer r1
    //     0xb3522c: add             x1, x1, HEAP, lsl #32
    // 0xb35230: mov             x0, x1
    // 0xb35234: cmp             w0, NULL
    // 0xb35238: b.eq            #0xb35248
    // 0xb3523c: tbnz            w0, #4, #0xb35248
    // 0xb35240: r0 = Instance_Color
    //     0xb35240: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb35244: b               #0xb35250
    // 0xb35248: r0 = Instance_MaterialColor
    //     0xb35248: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb3524c: ldr             x0, [x0, #0xdc0]
    // 0xb35250: mov             x1, x0
    // 0xb35254: ldur            x0, [fp, #-0x58]
    // 0xb35258: r16 = 14.000000
    //     0xb35258: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3525c: ldr             x16, [x16, #0x1d8]
    // 0xb35260: stp             x1, x16, [SP]
    // 0xb35264: ldur            x1, [fp, #-0x60]
    // 0xb35268: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb35268: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3526c: ldr             x4, [x4, #0xaa0]
    // 0xb35270: r0 = copyWith()
    //     0xb35270: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb35274: stur            x0, [fp, #-0x60]
    // 0xb35278: r0 = Text()
    //     0xb35278: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3527c: mov             x1, x0
    // 0xb35280: ldur            x0, [fp, #-0x58]
    // 0xb35284: StoreField: r1->field_b = r0
    //     0xb35284: stur            w0, [x1, #0xb]
    // 0xb35288: ldur            x0, [fp, #-0x60]
    // 0xb3528c: StoreField: r1->field_13 = r0
    //     0xb3528c: stur            w0, [x1, #0x13]
    // 0xb35290: mov             x10, x1
    // 0xb35294: b               #0xb35374
    // 0xb35298: ldur            x0, [fp, #-8]
    // 0xb3529c: ldur            x2, [fp, #-0x18]
    // 0xb352a0: r1 = "apply now"
    //     0xb352a0: add             x1, PP, #0x71, lsl #12  ; [pp+0x71510] "apply now"
    //     0xb352a4: ldr             x1, [x1, #0x510]
    // 0xb352a8: r0 = capitalizeFirstWord()
    //     0xb352a8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb352ac: ldur            x2, [fp, #-0x18]
    // 0xb352b0: stur            x0, [fp, #-0x58]
    // 0xb352b4: LoadField: r1 = r2->field_13
    //     0xb352b4: ldur            w1, [x2, #0x13]
    // 0xb352b8: DecompressPointer r1
    //     0xb352b8: add             x1, x1, HEAP, lsl #32
    // 0xb352bc: r0 = of()
    //     0xb352bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb352c0: LoadField: r1 = r0->field_87
    //     0xb352c0: ldur            w1, [x0, #0x87]
    // 0xb352c4: DecompressPointer r1
    //     0xb352c4: add             x1, x1, HEAP, lsl #32
    // 0xb352c8: LoadField: r0 = r1->field_7
    //     0xb352c8: ldur            w0, [x1, #7]
    // 0xb352cc: DecompressPointer r0
    //     0xb352cc: add             x0, x0, HEAP, lsl #32
    // 0xb352d0: ldur            x1, [fp, #-8]
    // 0xb352d4: stur            x0, [fp, #-0x60]
    // 0xb352d8: LoadField: r2 = r1->field_b
    //     0xb352d8: ldur            w2, [x1, #0xb]
    // 0xb352dc: DecompressPointer r2
    //     0xb352dc: add             x2, x2, HEAP, lsl #32
    // 0xb352e0: cmp             w2, NULL
    // 0xb352e4: b.eq            #0xb35630
    // 0xb352e8: LoadField: r1 = r2->field_b
    //     0xb352e8: ldur            w1, [x2, #0xb]
    // 0xb352ec: DecompressPointer r1
    //     0xb352ec: add             x1, x1, HEAP, lsl #32
    // 0xb352f0: cmp             w1, NULL
    // 0xb352f4: b.ne            #0xb35300
    // 0xb352f8: r1 = Null
    //     0xb352f8: mov             x1, NULL
    // 0xb352fc: b               #0xb3530c
    // 0xb35300: LoadField: r2 = r1->field_27
    //     0xb35300: ldur            w2, [x1, #0x27]
    // 0xb35304: DecompressPointer r2
    //     0xb35304: add             x2, x2, HEAP, lsl #32
    // 0xb35308: mov             x1, x2
    // 0xb3530c: cmp             w1, NULL
    // 0xb35310: b.eq            #0xb35320
    // 0xb35314: tbnz            w1, #4, #0xb35320
    // 0xb35318: r1 = Instance_Color
    //     0xb35318: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb3531c: b               #0xb35334
    // 0xb35320: r1 = Instance_MaterialColor
    //     0xb35320: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb35324: ldr             x1, [x1, #0xdc0]
    // 0xb35328: d0 = 0.600000
    //     0xb35328: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xb3532c: r0 = withOpacity()
    //     0xb3532c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb35330: mov             x1, x0
    // 0xb35334: ldur            x0, [fp, #-0x58]
    // 0xb35338: r16 = 14.000000
    //     0xb35338: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3533c: ldr             x16, [x16, #0x1d8]
    // 0xb35340: stp             x1, x16, [SP]
    // 0xb35344: ldur            x1, [fp, #-0x60]
    // 0xb35348: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb35348: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb3534c: ldr             x4, [x4, #0xaa0]
    // 0xb35350: r0 = copyWith()
    //     0xb35350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb35354: stur            x0, [fp, #-8]
    // 0xb35358: r0 = Text()
    //     0xb35358: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb3535c: mov             x1, x0
    // 0xb35360: ldur            x0, [fp, #-0x58]
    // 0xb35364: StoreField: r1->field_b = r0
    //     0xb35364: stur            w0, [x1, #0xb]
    // 0xb35368: ldur            x0, [fp, #-8]
    // 0xb3536c: StoreField: r1->field_13 = r0
    //     0xb3536c: stur            w0, [x1, #0x13]
    // 0xb35370: mov             x10, x1
    // 0xb35374: ldur            x9, [fp, #-0x10]
    // 0xb35378: ldur            x8, [fp, #-0x28]
    // 0xb3537c: ldur            x7, [fp, #-0x30]
    // 0xb35380: ldur            x6, [fp, #-0x48]
    // 0xb35384: ldur            x5, [fp, #-0x38]
    // 0xb35388: ldur            x4, [fp, #-0x50]
    // 0xb3538c: ldur            x3, [fp, #-0x20]
    // 0xb35390: ldur            x0, [fp, #-0x40]
    // 0xb35394: ldur            x2, [fp, #-0x18]
    // 0xb35398: stur            x10, [fp, #-8]
    // 0xb3539c: r1 = Function '<anonymous closure>':.
    //     0xb3539c: add             x1, PP, #0x71, lsl #12  ; [pp+0x71518] AnonymousClosure: (0xb35658), in [package:customer_app/app/presentation/views/glass/bag/offers_bottom_sheet.dart] _OffersBottomSheetState::build (0xb34490)
    //     0xb353a0: ldr             x1, [x1, #0x518]
    // 0xb353a4: r0 = AllocateClosure()
    //     0xb353a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb353a8: stur            x0, [fp, #-0x18]
    // 0xb353ac: r0 = TextButton()
    //     0xb353ac: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb353b0: mov             x1, x0
    // 0xb353b4: ldur            x0, [fp, #-0x18]
    // 0xb353b8: stur            x1, [fp, #-0x58]
    // 0xb353bc: StoreField: r1->field_b = r0
    //     0xb353bc: stur            w0, [x1, #0xb]
    // 0xb353c0: r0 = false
    //     0xb353c0: add             x0, NULL, #0x30  ; false
    // 0xb353c4: StoreField: r1->field_27 = r0
    //     0xb353c4: stur            w0, [x1, #0x27]
    // 0xb353c8: r2 = true
    //     0xb353c8: add             x2, NULL, #0x20  ; true
    // 0xb353cc: StoreField: r1->field_2f = r2
    //     0xb353cc: stur            w2, [x1, #0x2f]
    // 0xb353d0: ldur            x2, [fp, #-8]
    // 0xb353d4: StoreField: r1->field_37 = r2
    //     0xb353d4: stur            w2, [x1, #0x37]
    // 0xb353d8: r0 = TextButtonTheme()
    //     0xb353d8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb353dc: mov             x2, x0
    // 0xb353e0: ldur            x0, [fp, #-0x40]
    // 0xb353e4: stur            x2, [fp, #-8]
    // 0xb353e8: StoreField: r2->field_f = r0
    //     0xb353e8: stur            w0, [x2, #0xf]
    // 0xb353ec: ldur            x0, [fp, #-0x58]
    // 0xb353f0: StoreField: r2->field_b = r0
    //     0xb353f0: stur            w0, [x2, #0xb]
    // 0xb353f4: r1 = <FlexParentData>
    //     0xb353f4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb353f8: ldr             x1, [x1, #0xe00]
    // 0xb353fc: r0 = Expanded()
    //     0xb353fc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb35400: mov             x1, x0
    // 0xb35404: r0 = 1
    //     0xb35404: movz            x0, #0x1
    // 0xb35408: stur            x1, [fp, #-0x18]
    // 0xb3540c: StoreField: r1->field_13 = r0
    //     0xb3540c: stur            x0, [x1, #0x13]
    // 0xb35410: r0 = Instance_FlexFit
    //     0xb35410: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb35414: ldr             x0, [x0, #0xe08]
    // 0xb35418: StoreField: r1->field_1b = r0
    //     0xb35418: stur            w0, [x1, #0x1b]
    // 0xb3541c: ldur            x0, [fp, #-8]
    // 0xb35420: StoreField: r1->field_b = r0
    //     0xb35420: stur            w0, [x1, #0xb]
    // 0xb35424: r0 = Visibility()
    //     0xb35424: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb35428: mov             x3, x0
    // 0xb3542c: ldur            x0, [fp, #-0x18]
    // 0xb35430: stur            x3, [fp, #-8]
    // 0xb35434: StoreField: r3->field_b = r0
    //     0xb35434: stur            w0, [x3, #0xb]
    // 0xb35438: r0 = Instance_SizedBox
    //     0xb35438: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb3543c: StoreField: r3->field_f = r0
    //     0xb3543c: stur            w0, [x3, #0xf]
    // 0xb35440: ldur            x0, [fp, #-0x20]
    // 0xb35444: StoreField: r3->field_13 = r0
    //     0xb35444: stur            w0, [x3, #0x13]
    // 0xb35448: r0 = false
    //     0xb35448: add             x0, NULL, #0x30  ; false
    // 0xb3544c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb3544c: stur            w0, [x3, #0x17]
    // 0xb35450: StoreField: r3->field_1b = r0
    //     0xb35450: stur            w0, [x3, #0x1b]
    // 0xb35454: StoreField: r3->field_1f = r0
    //     0xb35454: stur            w0, [x3, #0x1f]
    // 0xb35458: StoreField: r3->field_23 = r0
    //     0xb35458: stur            w0, [x3, #0x23]
    // 0xb3545c: StoreField: r3->field_27 = r0
    //     0xb3545c: stur            w0, [x3, #0x27]
    // 0xb35460: StoreField: r3->field_2b = r0
    //     0xb35460: stur            w0, [x3, #0x2b]
    // 0xb35464: r1 = Null
    //     0xb35464: mov             x1, NULL
    // 0xb35468: r2 = 6
    //     0xb35468: movz            x2, #0x6
    // 0xb3546c: r0 = AllocateArray()
    //     0xb3546c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb35470: mov             x2, x0
    // 0xb35474: ldur            x0, [fp, #-0x50]
    // 0xb35478: stur            x2, [fp, #-0x18]
    // 0xb3547c: StoreField: r2->field_f = r0
    //     0xb3547c: stur            w0, [x2, #0xf]
    // 0xb35480: r16 = Instance_SizedBox
    //     0xb35480: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb35484: ldr             x16, [x16, #0xb20]
    // 0xb35488: StoreField: r2->field_13 = r16
    //     0xb35488: stur            w16, [x2, #0x13]
    // 0xb3548c: ldur            x0, [fp, #-8]
    // 0xb35490: ArrayStore: r2[0] = r0  ; List_4
    //     0xb35490: stur            w0, [x2, #0x17]
    // 0xb35494: r1 = <Widget>
    //     0xb35494: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb35498: r0 = AllocateGrowableArray()
    //     0xb35498: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3549c: mov             x1, x0
    // 0xb354a0: ldur            x0, [fp, #-0x18]
    // 0xb354a4: stur            x1, [fp, #-8]
    // 0xb354a8: StoreField: r1->field_f = r0
    //     0xb354a8: stur            w0, [x1, #0xf]
    // 0xb354ac: r0 = 6
    //     0xb354ac: movz            x0, #0x6
    // 0xb354b0: StoreField: r1->field_b = r0
    //     0xb354b0: stur            w0, [x1, #0xb]
    // 0xb354b4: r0 = Row()
    //     0xb354b4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb354b8: mov             x1, x0
    // 0xb354bc: r0 = Instance_Axis
    //     0xb354bc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb354c0: stur            x1, [fp, #-0x18]
    // 0xb354c4: StoreField: r1->field_f = r0
    //     0xb354c4: stur            w0, [x1, #0xf]
    // 0xb354c8: r0 = Instance_MainAxisAlignment
    //     0xb354c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb354cc: ldr             x0, [x0, #0xa08]
    // 0xb354d0: StoreField: r1->field_13 = r0
    //     0xb354d0: stur            w0, [x1, #0x13]
    // 0xb354d4: r2 = Instance_MainAxisSize
    //     0xb354d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb354d8: ldr             x2, [x2, #0xa10]
    // 0xb354dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb354dc: stur            w2, [x1, #0x17]
    // 0xb354e0: r2 = Instance_CrossAxisAlignment
    //     0xb354e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb354e4: ldr             x2, [x2, #0xa18]
    // 0xb354e8: StoreField: r1->field_1b = r2
    //     0xb354e8: stur            w2, [x1, #0x1b]
    // 0xb354ec: r2 = Instance_VerticalDirection
    //     0xb354ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb354f0: ldr             x2, [x2, #0xa20]
    // 0xb354f4: StoreField: r1->field_23 = r2
    //     0xb354f4: stur            w2, [x1, #0x23]
    // 0xb354f8: r3 = Instance_Clip
    //     0xb354f8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb354fc: ldr             x3, [x3, #0x38]
    // 0xb35500: StoreField: r1->field_2b = r3
    //     0xb35500: stur            w3, [x1, #0x2b]
    // 0xb35504: StoreField: r1->field_2f = rZR
    //     0xb35504: stur            xzr, [x1, #0x2f]
    // 0xb35508: ldur            x4, [fp, #-8]
    // 0xb3550c: StoreField: r1->field_b = r4
    //     0xb3550c: stur            w4, [x1, #0xb]
    // 0xb35510: r0 = Padding()
    //     0xb35510: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb35514: mov             x3, x0
    // 0xb35518: r0 = Instance_EdgeInsets
    //     0xb35518: add             x0, PP, #0x55, lsl #12  ; [pp+0x55fc0] Obj!EdgeInsets@d58d91
    //     0xb3551c: ldr             x0, [x0, #0xfc0]
    // 0xb35520: stur            x3, [fp, #-8]
    // 0xb35524: StoreField: r3->field_f = r0
    //     0xb35524: stur            w0, [x3, #0xf]
    // 0xb35528: ldur            x0, [fp, #-0x18]
    // 0xb3552c: StoreField: r3->field_b = r0
    //     0xb3552c: stur            w0, [x3, #0xb]
    // 0xb35530: r1 = Null
    //     0xb35530: mov             x1, NULL
    // 0xb35534: r2 = 12
    //     0xb35534: movz            x2, #0xc
    // 0xb35538: r0 = AllocateArray()
    //     0xb35538: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb3553c: mov             x2, x0
    // 0xb35540: ldur            x0, [fp, #-0x10]
    // 0xb35544: stur            x2, [fp, #-0x18]
    // 0xb35548: StoreField: r2->field_f = r0
    //     0xb35548: stur            w0, [x2, #0xf]
    // 0xb3554c: ldur            x0, [fp, #-0x28]
    // 0xb35550: StoreField: r2->field_13 = r0
    //     0xb35550: stur            w0, [x2, #0x13]
    // 0xb35554: ldur            x0, [fp, #-0x30]
    // 0xb35558: ArrayStore: r2[0] = r0  ; List_4
    //     0xb35558: stur            w0, [x2, #0x17]
    // 0xb3555c: ldur            x0, [fp, #-0x48]
    // 0xb35560: StoreField: r2->field_1b = r0
    //     0xb35560: stur            w0, [x2, #0x1b]
    // 0xb35564: ldur            x0, [fp, #-0x38]
    // 0xb35568: StoreField: r2->field_1f = r0
    //     0xb35568: stur            w0, [x2, #0x1f]
    // 0xb3556c: ldur            x0, [fp, #-8]
    // 0xb35570: StoreField: r2->field_23 = r0
    //     0xb35570: stur            w0, [x2, #0x23]
    // 0xb35574: r1 = <Widget>
    //     0xb35574: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb35578: r0 = AllocateGrowableArray()
    //     0xb35578: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb3557c: mov             x1, x0
    // 0xb35580: ldur            x0, [fp, #-0x18]
    // 0xb35584: stur            x1, [fp, #-8]
    // 0xb35588: StoreField: r1->field_f = r0
    //     0xb35588: stur            w0, [x1, #0xf]
    // 0xb3558c: r0 = 12
    //     0xb3558c: movz            x0, #0xc
    // 0xb35590: StoreField: r1->field_b = r0
    //     0xb35590: stur            w0, [x1, #0xb]
    // 0xb35594: r0 = Column()
    //     0xb35594: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb35598: r1 = Instance_Axis
    //     0xb35598: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb3559c: StoreField: r0->field_f = r1
    //     0xb3559c: stur            w1, [x0, #0xf]
    // 0xb355a0: r1 = Instance_MainAxisAlignment
    //     0xb355a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb355a4: ldr             x1, [x1, #0xa08]
    // 0xb355a8: StoreField: r0->field_13 = r1
    //     0xb355a8: stur            w1, [x0, #0x13]
    // 0xb355ac: r1 = Instance_MainAxisSize
    //     0xb355ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb355b0: ldr             x1, [x1, #0xdd0]
    // 0xb355b4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb355b4: stur            w1, [x0, #0x17]
    // 0xb355b8: r1 = Instance_CrossAxisAlignment
    //     0xb355b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb355bc: ldr             x1, [x1, #0x890]
    // 0xb355c0: StoreField: r0->field_1b = r1
    //     0xb355c0: stur            w1, [x0, #0x1b]
    // 0xb355c4: r1 = Instance_VerticalDirection
    //     0xb355c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb355c8: ldr             x1, [x1, #0xa20]
    // 0xb355cc: StoreField: r0->field_23 = r1
    //     0xb355cc: stur            w1, [x0, #0x23]
    // 0xb355d0: r1 = Instance_Clip
    //     0xb355d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb355d4: ldr             x1, [x1, #0x38]
    // 0xb355d8: StoreField: r0->field_2b = r1
    //     0xb355d8: stur            w1, [x0, #0x2b]
    // 0xb355dc: StoreField: r0->field_2f = rZR
    //     0xb355dc: stur            xzr, [x0, #0x2f]
    // 0xb355e0: ldur            x1, [fp, #-8]
    // 0xb355e4: StoreField: r0->field_b = r1
    //     0xb355e4: stur            w1, [x0, #0xb]
    // 0xb355e8: LeaveFrame
    //     0xb355e8: mov             SP, fp
    //     0xb355ec: ldp             fp, lr, [SP], #0x10
    // 0xb355f0: ret
    //     0xb355f0: ret             
    // 0xb355f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb355f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb355f8: b               #0xb344b8
    // 0xb355fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb355fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35600: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35600: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35604: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35604: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35608: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35608: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3560c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3560c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35610: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35614: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35618: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3561c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3561c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35620: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35624: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35628: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35628: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3562c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3562c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35630: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb35658, size: 0x244
    // 0xb35658: EnterFrame
    //     0xb35658: stp             fp, lr, [SP, #-0x10]!
    //     0xb3565c: mov             fp, SP
    // 0xb35660: AllocStack(0x28)
    //     0xb35660: sub             SP, SP, #0x28
    // 0xb35664: SetupParameters()
    //     0xb35664: ldr             x0, [fp, #0x10]
    //     0xb35668: ldur            w1, [x0, #0x17]
    //     0xb3566c: add             x1, x1, HEAP, lsl #32
    //     0xb35670: stur            x1, [fp, #-8]
    // 0xb35674: CheckStackOverflow
    //     0xb35674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb35678: cmp             SP, x16
    //     0xb3567c: b.ls            #0xb35880
    // 0xb35680: LoadField: r0 = r1->field_f
    //     0xb35680: ldur            w0, [x1, #0xf]
    // 0xb35684: DecompressPointer r0
    //     0xb35684: add             x0, x0, HEAP, lsl #32
    // 0xb35688: LoadField: r2 = r0->field_b
    //     0xb35688: ldur            w2, [x0, #0xb]
    // 0xb3568c: DecompressPointer r2
    //     0xb3568c: add             x2, x2, HEAP, lsl #32
    // 0xb35690: cmp             w2, NULL
    // 0xb35694: b.eq            #0xb35888
    // 0xb35698: LoadField: r0 = r2->field_b
    //     0xb35698: ldur            w0, [x2, #0xb]
    // 0xb3569c: DecompressPointer r0
    //     0xb3569c: add             x0, x0, HEAP, lsl #32
    // 0xb356a0: cmp             w0, NULL
    // 0xb356a4: b.ne            #0xb356b0
    // 0xb356a8: r3 = Null
    //     0xb356a8: mov             x3, NULL
    // 0xb356ac: b               #0xb356b8
    // 0xb356b0: LoadField: r3 = r0->field_27
    //     0xb356b0: ldur            w3, [x0, #0x27]
    // 0xb356b4: DecompressPointer r3
    //     0xb356b4: add             x3, x3, HEAP, lsl #32
    // 0xb356b8: cmp             w3, NULL
    // 0xb356bc: b.eq            #0xb35870
    // 0xb356c0: tbnz            w3, #4, #0xb35870
    // 0xb356c4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb356c4: ldur            w3, [x2, #0x17]
    // 0xb356c8: DecompressPointer r3
    //     0xb356c8: add             x3, x3, HEAP, lsl #32
    // 0xb356cc: cmp             w3, NULL
    // 0xb356d0: b.eq            #0xb357b8
    // 0xb356d4: LoadField: r3 = r2->field_f
    //     0xb356d4: ldur            w3, [x2, #0xf]
    // 0xb356d8: DecompressPointer r3
    //     0xb356d8: add             x3, x3, HEAP, lsl #32
    // 0xb356dc: cmp             w0, NULL
    // 0xb356e0: b.ne            #0xb356ec
    // 0xb356e4: r0 = Null
    //     0xb356e4: mov             x0, NULL
    // 0xb356e8: b               #0xb356f8
    // 0xb356ec: LoadField: r2 = r0->field_7
    //     0xb356ec: ldur            w2, [x0, #7]
    // 0xb356f0: DecompressPointer r2
    //     0xb356f0: add             x2, x2, HEAP, lsl #32
    // 0xb356f4: mov             x0, x2
    // 0xb356f8: r2 = LoadClassIdInstr(r3)
    //     0xb356f8: ldur            x2, [x3, #-1]
    //     0xb356fc: ubfx            x2, x2, #0xc, #0x14
    // 0xb35700: stp             x0, x3, [SP]
    // 0xb35704: mov             x0, x2
    // 0xb35708: mov             lr, x0
    // 0xb3570c: ldr             lr, [x21, lr, lsl #3]
    // 0xb35710: blr             lr
    // 0xb35714: tbz             w0, #4, #0xb357b8
    // 0xb35718: ldur            x0, [fp, #-8]
    // 0xb3571c: LoadField: r1 = r0->field_f
    //     0xb3571c: ldur            w1, [x0, #0xf]
    // 0xb35720: DecompressPointer r1
    //     0xb35720: add             x1, x1, HEAP, lsl #32
    // 0xb35724: LoadField: r2 = r1->field_b
    //     0xb35724: ldur            w2, [x1, #0xb]
    // 0xb35728: DecompressPointer r2
    //     0xb35728: add             x2, x2, HEAP, lsl #32
    // 0xb3572c: cmp             w2, NULL
    // 0xb35730: b.eq            #0xb3588c
    // 0xb35734: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb35734: ldur            w1, [x2, #0x17]
    // 0xb35738: DecompressPointer r1
    //     0xb35738: add             x1, x1, HEAP, lsl #32
    // 0xb3573c: cmp             w1, NULL
    // 0xb35740: b.eq            #0xb35890
    // 0xb35744: LoadField: r3 = r2->field_b
    //     0xb35744: ldur            w3, [x2, #0xb]
    // 0xb35748: DecompressPointer r3
    //     0xb35748: add             x3, x3, HEAP, lsl #32
    // 0xb3574c: cmp             w3, NULL
    // 0xb35750: b.ne            #0xb3575c
    // 0xb35754: r2 = Null
    //     0xb35754: mov             x2, NULL
    // 0xb35758: b               #0xb35764
    // 0xb3575c: LoadField: r2 = r3->field_7
    //     0xb3575c: ldur            w2, [x3, #7]
    // 0xb35760: DecompressPointer r2
    //     0xb35760: add             x2, x2, HEAP, lsl #32
    // 0xb35764: cmp             w2, NULL
    // 0xb35768: b.ne            #0xb35770
    // 0xb3576c: r2 = ""
    //     0xb3576c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb35770: cmp             w3, NULL
    // 0xb35774: b.ne            #0xb35780
    // 0xb35778: r3 = Null
    //     0xb35778: mov             x3, NULL
    // 0xb3577c: b               #0xb3578c
    // 0xb35780: LoadField: r4 = r3->field_b
    //     0xb35780: ldur            w4, [x3, #0xb]
    // 0xb35784: DecompressPointer r4
    //     0xb35784: add             x4, x4, HEAP, lsl #32
    // 0xb35788: mov             x3, x4
    // 0xb3578c: stp             x2, x1, [SP, #0x10]
    // 0xb35790: r16 = "apply"
    //     0xb35790: add             x16, PP, #0x24, lsl #12  ; [pp+0x24fe8] "apply"
    //     0xb35794: ldr             x16, [x16, #0xfe8]
    // 0xb35798: stp             x16, x3, [SP]
    // 0xb3579c: r4 = 0
    //     0xb3579c: movz            x4, #0
    // 0xb357a0: ldr             x0, [SP, #0x18]
    // 0xb357a4: r16 = UnlinkedCall_0x613b5c
    //     0xb357a4: add             x16, PP, #0x71, lsl #12  ; [pp+0x71520] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb357a8: add             x16, x16, #0x520
    // 0xb357ac: ldp             x5, lr, [x16]
    // 0xb357b0: blr             lr
    // 0xb357b4: b               #0xb35854
    // 0xb357b8: ldur            x0, [fp, #-8]
    // 0xb357bc: LoadField: r1 = r0->field_f
    //     0xb357bc: ldur            w1, [x0, #0xf]
    // 0xb357c0: DecompressPointer r1
    //     0xb357c0: add             x1, x1, HEAP, lsl #32
    // 0xb357c4: LoadField: r2 = r1->field_b
    //     0xb357c4: ldur            w2, [x1, #0xb]
    // 0xb357c8: DecompressPointer r2
    //     0xb357c8: add             x2, x2, HEAP, lsl #32
    // 0xb357cc: cmp             w2, NULL
    // 0xb357d0: b.eq            #0xb35894
    // 0xb357d4: LoadField: r1 = r2->field_1b
    //     0xb357d4: ldur            w1, [x2, #0x1b]
    // 0xb357d8: DecompressPointer r1
    //     0xb357d8: add             x1, x1, HEAP, lsl #32
    // 0xb357dc: cmp             w1, NULL
    // 0xb357e0: b.eq            #0xb35898
    // 0xb357e4: LoadField: r3 = r2->field_b
    //     0xb357e4: ldur            w3, [x2, #0xb]
    // 0xb357e8: DecompressPointer r3
    //     0xb357e8: add             x3, x3, HEAP, lsl #32
    // 0xb357ec: cmp             w3, NULL
    // 0xb357f0: b.ne            #0xb357fc
    // 0xb357f4: r2 = Null
    //     0xb357f4: mov             x2, NULL
    // 0xb357f8: b               #0xb35804
    // 0xb357fc: LoadField: r2 = r3->field_7
    //     0xb357fc: ldur            w2, [x3, #7]
    // 0xb35800: DecompressPointer r2
    //     0xb35800: add             x2, x2, HEAP, lsl #32
    // 0xb35804: cmp             w2, NULL
    // 0xb35808: b.ne            #0xb35810
    // 0xb3580c: r2 = ""
    //     0xb3580c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb35810: cmp             w3, NULL
    // 0xb35814: b.ne            #0xb35820
    // 0xb35818: r3 = Null
    //     0xb35818: mov             x3, NULL
    // 0xb3581c: b               #0xb3582c
    // 0xb35820: LoadField: r4 = r3->field_b
    //     0xb35820: ldur            w4, [x3, #0xb]
    // 0xb35824: DecompressPointer r4
    //     0xb35824: add             x4, x4, HEAP, lsl #32
    // 0xb35828: mov             x3, x4
    // 0xb3582c: stp             x2, x1, [SP, #0x10]
    // 0xb35830: r16 = "remove"
    //     0xb35830: add             x16, PP, #0x24, lsl #12  ; [pp+0x24ff0] "remove"
    //     0xb35834: ldr             x16, [x16, #0xff0]
    // 0xb35838: stp             x16, x3, [SP]
    // 0xb3583c: r4 = 0
    //     0xb3583c: movz            x4, #0
    // 0xb35840: ldr             x0, [SP, #0x18]
    // 0xb35844: r16 = UnlinkedCall_0x613b5c
    //     0xb35844: add             x16, PP, #0x71, lsl #12  ; [pp+0x71530] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb35848: add             x16, x16, #0x530
    // 0xb3584c: ldp             x5, lr, [x16]
    // 0xb35850: blr             lr
    // 0xb35854: ldur            x0, [fp, #-8]
    // 0xb35858: LoadField: r1 = r0->field_13
    //     0xb35858: ldur            w1, [x0, #0x13]
    // 0xb3585c: DecompressPointer r1
    //     0xb3585c: add             x1, x1, HEAP, lsl #32
    // 0xb35860: r16 = <Object?>
    //     0xb35860: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xb35864: stp             x1, x16, [SP]
    // 0xb35868: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb35868: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb3586c: r0 = pop()
    //     0xb3586c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb35870: r0 = Null
    //     0xb35870: mov             x0, NULL
    // 0xb35874: LeaveFrame
    //     0xb35874: mov             SP, fp
    //     0xb35878: ldp             fp, lr, [SP], #0x10
    // 0xb3587c: ret
    //     0xb3587c: ret             
    // 0xb35880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb35880: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb35884: b               #0xb35680
    // 0xb35888: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35888: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3588c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3588c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35894: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35898: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35898: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb3589c, size: 0x304
    // 0xb3589c: EnterFrame
    //     0xb3589c: stp             fp, lr, [SP, #-0x10]!
    //     0xb358a0: mov             fp, SP
    // 0xb358a4: AllocStack(0x30)
    //     0xb358a4: sub             SP, SP, #0x30
    // 0xb358a8: SetupParameters()
    //     0xb358a8: ldr             x0, [fp, #0x20]
    //     0xb358ac: ldur            w2, [x0, #0x17]
    //     0xb358b0: add             x2, x2, HEAP, lsl #32
    //     0xb358b4: stur            x2, [fp, #-8]
    // 0xb358b8: CheckStackOverflow
    //     0xb358b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb358bc: cmp             SP, x16
    //     0xb358c0: b.ls            #0xb35b90
    // 0xb358c4: r1 = Instance_Color
    //     0xb358c4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb358c8: d0 = 0.300000
    //     0xb358c8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb358cc: ldr             d0, [x17, #0x658]
    // 0xb358d0: r0 = withOpacity()
    //     0xb358d0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb358d4: stur            x0, [fp, #-0x10]
    // 0xb358d8: r0 = Icon()
    //     0xb358d8: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb358dc: mov             x1, x0
    // 0xb358e0: r0 = Instance_IconData
    //     0xb358e0: add             x0, PP, #0x71, lsl #12  ; [pp+0x71540] Obj!IconData@d55521
    //     0xb358e4: ldr             x0, [x0, #0x540]
    // 0xb358e8: stur            x1, [fp, #-0x18]
    // 0xb358ec: StoreField: r1->field_b = r0
    //     0xb358ec: stur            w0, [x1, #0xb]
    // 0xb358f0: r0 = 20.000000
    //     0xb358f0: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb358f4: ldr             x0, [x0, #0xac8]
    // 0xb358f8: StoreField: r1->field_f = r0
    //     0xb358f8: stur            w0, [x1, #0xf]
    // 0xb358fc: ldur            x0, [fp, #-0x10]
    // 0xb35900: StoreField: r1->field_23 = r0
    //     0xb35900: stur            w0, [x1, #0x23]
    // 0xb35904: r0 = InkWell()
    //     0xb35904: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb35908: mov             x3, x0
    // 0xb3590c: ldur            x0, [fp, #-0x18]
    // 0xb35910: stur            x3, [fp, #-0x10]
    // 0xb35914: StoreField: r3->field_b = r0
    //     0xb35914: stur            w0, [x3, #0xb]
    // 0xb35918: r1 = Function '<anonymous closure>':.
    //     0xb35918: add             x1, PP, #0x71, lsl #12  ; [pp+0x71548] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb3591c: ldr             x1, [x1, #0x548]
    // 0xb35920: r2 = Null
    //     0xb35920: mov             x2, NULL
    // 0xb35924: r0 = AllocateClosure()
    //     0xb35924: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb35928: mov             x1, x0
    // 0xb3592c: ldur            x0, [fp, #-0x10]
    // 0xb35930: StoreField: r0->field_f = r1
    //     0xb35930: stur            w1, [x0, #0xf]
    // 0xb35934: r1 = true
    //     0xb35934: add             x1, NULL, #0x20  ; true
    // 0xb35938: StoreField: r0->field_43 = r1
    //     0xb35938: stur            w1, [x0, #0x43]
    // 0xb3593c: r2 = Instance_BoxShape
    //     0xb3593c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb35940: ldr             x2, [x2, #0x80]
    // 0xb35944: StoreField: r0->field_47 = r2
    //     0xb35944: stur            w2, [x0, #0x47]
    // 0xb35948: StoreField: r0->field_6f = r1
    //     0xb35948: stur            w1, [x0, #0x6f]
    // 0xb3594c: r2 = false
    //     0xb3594c: add             x2, NULL, #0x30  ; false
    // 0xb35950: StoreField: r0->field_73 = r2
    //     0xb35950: stur            w2, [x0, #0x73]
    // 0xb35954: StoreField: r0->field_83 = r1
    //     0xb35954: stur            w1, [x0, #0x83]
    // 0xb35958: StoreField: r0->field_7b = r2
    //     0xb35958: stur            w2, [x0, #0x7b]
    // 0xb3595c: r1 = <FlexParentData>
    //     0xb3595c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb35960: ldr             x1, [x1, #0xe00]
    // 0xb35964: r0 = Expanded()
    //     0xb35964: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb35968: mov             x2, x0
    // 0xb3596c: stur            x2, [fp, #-0x18]
    // 0xb35970: StoreField: r2->field_13 = rZR
    //     0xb35970: stur            xzr, [x2, #0x13]
    // 0xb35974: r3 = Instance_FlexFit
    //     0xb35974: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb35978: ldr             x3, [x3, #0xe08]
    // 0xb3597c: StoreField: r2->field_1b = r3
    //     0xb3597c: stur            w3, [x2, #0x1b]
    // 0xb35980: ldur            x0, [fp, #-0x10]
    // 0xb35984: StoreField: r2->field_b = r0
    //     0xb35984: stur            w0, [x2, #0xb]
    // 0xb35988: ldur            x0, [fp, #-8]
    // 0xb3598c: LoadField: r1 = r0->field_f
    //     0xb3598c: ldur            w1, [x0, #0xf]
    // 0xb35990: DecompressPointer r1
    //     0xb35990: add             x1, x1, HEAP, lsl #32
    // 0xb35994: LoadField: r0 = r1->field_b
    //     0xb35994: ldur            w0, [x1, #0xb]
    // 0xb35998: DecompressPointer r0
    //     0xb35998: add             x0, x0, HEAP, lsl #32
    // 0xb3599c: cmp             w0, NULL
    // 0xb359a0: b.eq            #0xb35b98
    // 0xb359a4: LoadField: r1 = r0->field_b
    //     0xb359a4: ldur            w1, [x0, #0xb]
    // 0xb359a8: DecompressPointer r1
    //     0xb359a8: add             x1, x1, HEAP, lsl #32
    // 0xb359ac: cmp             w1, NULL
    // 0xb359b0: b.ne            #0xb359bc
    // 0xb359b4: r0 = Null
    //     0xb359b4: mov             x0, NULL
    // 0xb359b8: b               #0xb35a10
    // 0xb359bc: LoadField: r4 = r1->field_1f
    //     0xb359bc: ldur            w4, [x1, #0x1f]
    // 0xb359c0: DecompressPointer r4
    //     0xb359c0: add             x4, x4, HEAP, lsl #32
    // 0xb359c4: cmp             w4, NULL
    // 0xb359c8: b.ne            #0xb359d4
    // 0xb359cc: r0 = Null
    //     0xb359cc: mov             x0, NULL
    // 0xb359d0: b               #0xb35a10
    // 0xb359d4: ldr             x0, [fp, #0x10]
    // 0xb359d8: LoadField: r1 = r4->field_b
    //     0xb359d8: ldur            w1, [x4, #0xb]
    // 0xb359dc: r5 = LoadInt32Instr(r0)
    //     0xb359dc: sbfx            x5, x0, #1, #0x1f
    //     0xb359e0: tbz             w0, #0, #0xb359e8
    //     0xb359e4: ldur            x5, [x0, #7]
    // 0xb359e8: r0 = LoadInt32Instr(r1)
    //     0xb359e8: sbfx            x0, x1, #1, #0x1f
    // 0xb359ec: mov             x1, x5
    // 0xb359f0: cmp             x1, x0
    // 0xb359f4: b.hs            #0xb35b9c
    // 0xb359f8: LoadField: r0 = r4->field_f
    //     0xb359f8: ldur            w0, [x4, #0xf]
    // 0xb359fc: DecompressPointer r0
    //     0xb359fc: add             x0, x0, HEAP, lsl #32
    // 0xb35a00: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb35a00: add             x16, x0, x5, lsl #2
    //     0xb35a04: ldur            w1, [x16, #0xf]
    // 0xb35a08: DecompressPointer r1
    //     0xb35a08: add             x1, x1, HEAP, lsl #32
    // 0xb35a0c: mov             x0, x1
    // 0xb35a10: cmp             w0, NULL
    // 0xb35a14: b.ne            #0xb35a1c
    // 0xb35a18: r0 = ""
    //     0xb35a18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb35a1c: ldr             x1, [fp, #0x18]
    // 0xb35a20: stur            x0, [fp, #-8]
    // 0xb35a24: r0 = of()
    //     0xb35a24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb35a28: LoadField: r1 = r0->field_87
    //     0xb35a28: ldur            w1, [x0, #0x87]
    // 0xb35a2c: DecompressPointer r1
    //     0xb35a2c: add             x1, x1, HEAP, lsl #32
    // 0xb35a30: LoadField: r0 = r1->field_2b
    //     0xb35a30: ldur            w0, [x1, #0x2b]
    // 0xb35a34: DecompressPointer r0
    //     0xb35a34: add             x0, x0, HEAP, lsl #32
    // 0xb35a38: stur            x0, [fp, #-0x10]
    // 0xb35a3c: r1 = Instance_Color
    //     0xb35a3c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb35a40: d0 = 0.400000
    //     0xb35a40: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb35a44: r0 = withOpacity()
    //     0xb35a44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb35a48: r16 = 12.000000
    //     0xb35a48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb35a4c: ldr             x16, [x16, #0x9e8]
    // 0xb35a50: stp             x0, x16, [SP]
    // 0xb35a54: ldur            x1, [fp, #-0x10]
    // 0xb35a58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb35a58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb35a5c: ldr             x4, [x4, #0xaa0]
    // 0xb35a60: r0 = copyWith()
    //     0xb35a60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb35a64: stur            x0, [fp, #-0x10]
    // 0xb35a68: r0 = Text()
    //     0xb35a68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb35a6c: mov             x1, x0
    // 0xb35a70: ldur            x0, [fp, #-8]
    // 0xb35a74: stur            x1, [fp, #-0x20]
    // 0xb35a78: StoreField: r1->field_b = r0
    //     0xb35a78: stur            w0, [x1, #0xb]
    // 0xb35a7c: ldur            x0, [fp, #-0x10]
    // 0xb35a80: StoreField: r1->field_13 = r0
    //     0xb35a80: stur            w0, [x1, #0x13]
    // 0xb35a84: r0 = Padding()
    //     0xb35a84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb35a88: mov             x2, x0
    // 0xb35a8c: r0 = Instance_EdgeInsets
    //     0xb35a8c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb35a90: ldr             x0, [x0, #0xf70]
    // 0xb35a94: stur            x2, [fp, #-8]
    // 0xb35a98: StoreField: r2->field_f = r0
    //     0xb35a98: stur            w0, [x2, #0xf]
    // 0xb35a9c: ldur            x0, [fp, #-0x20]
    // 0xb35aa0: StoreField: r2->field_b = r0
    //     0xb35aa0: stur            w0, [x2, #0xb]
    // 0xb35aa4: r1 = <FlexParentData>
    //     0xb35aa4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb35aa8: ldr             x1, [x1, #0xe00]
    // 0xb35aac: r0 = Expanded()
    //     0xb35aac: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb35ab0: stur            x0, [fp, #-0x10]
    // 0xb35ab4: StoreField: r0->field_13 = rZR
    //     0xb35ab4: stur            xzr, [x0, #0x13]
    // 0xb35ab8: r1 = Instance_FlexFit
    //     0xb35ab8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb35abc: ldr             x1, [x1, #0xe08]
    // 0xb35ac0: StoreField: r0->field_1b = r1
    //     0xb35ac0: stur            w1, [x0, #0x1b]
    // 0xb35ac4: ldur            x1, [fp, #-8]
    // 0xb35ac8: StoreField: r0->field_b = r1
    //     0xb35ac8: stur            w1, [x0, #0xb]
    // 0xb35acc: r1 = Null
    //     0xb35acc: mov             x1, NULL
    // 0xb35ad0: r2 = 4
    //     0xb35ad0: movz            x2, #0x4
    // 0xb35ad4: r0 = AllocateArray()
    //     0xb35ad4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb35ad8: mov             x2, x0
    // 0xb35adc: ldur            x0, [fp, #-0x18]
    // 0xb35ae0: stur            x2, [fp, #-8]
    // 0xb35ae4: StoreField: r2->field_f = r0
    //     0xb35ae4: stur            w0, [x2, #0xf]
    // 0xb35ae8: ldur            x0, [fp, #-0x10]
    // 0xb35aec: StoreField: r2->field_13 = r0
    //     0xb35aec: stur            w0, [x2, #0x13]
    // 0xb35af0: r1 = <Widget>
    //     0xb35af0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb35af4: r0 = AllocateGrowableArray()
    //     0xb35af4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb35af8: mov             x1, x0
    // 0xb35afc: ldur            x0, [fp, #-8]
    // 0xb35b00: stur            x1, [fp, #-0x10]
    // 0xb35b04: StoreField: r1->field_f = r0
    //     0xb35b04: stur            w0, [x1, #0xf]
    // 0xb35b08: r0 = 4
    //     0xb35b08: movz            x0, #0x4
    // 0xb35b0c: StoreField: r1->field_b = r0
    //     0xb35b0c: stur            w0, [x1, #0xb]
    // 0xb35b10: r0 = Row()
    //     0xb35b10: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb35b14: mov             x1, x0
    // 0xb35b18: r0 = Instance_Axis
    //     0xb35b18: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb35b1c: stur            x1, [fp, #-8]
    // 0xb35b20: StoreField: r1->field_f = r0
    //     0xb35b20: stur            w0, [x1, #0xf]
    // 0xb35b24: r0 = Instance_MainAxisAlignment
    //     0xb35b24: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb35b28: ldr             x0, [x0, #0xa08]
    // 0xb35b2c: StoreField: r1->field_13 = r0
    //     0xb35b2c: stur            w0, [x1, #0x13]
    // 0xb35b30: r0 = Instance_MainAxisSize
    //     0xb35b30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb35b34: ldr             x0, [x0, #0xa10]
    // 0xb35b38: ArrayStore: r1[0] = r0  ; List_4
    //     0xb35b38: stur            w0, [x1, #0x17]
    // 0xb35b3c: r0 = Instance_CrossAxisAlignment
    //     0xb35b3c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb35b40: ldr             x0, [x0, #0xc68]
    // 0xb35b44: StoreField: r1->field_1b = r0
    //     0xb35b44: stur            w0, [x1, #0x1b]
    // 0xb35b48: r0 = Instance_VerticalDirection
    //     0xb35b48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb35b4c: ldr             x0, [x0, #0xa20]
    // 0xb35b50: StoreField: r1->field_23 = r0
    //     0xb35b50: stur            w0, [x1, #0x23]
    // 0xb35b54: r0 = Instance_Clip
    //     0xb35b54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb35b58: ldr             x0, [x0, #0x38]
    // 0xb35b5c: StoreField: r1->field_2b = r0
    //     0xb35b5c: stur            w0, [x1, #0x2b]
    // 0xb35b60: StoreField: r1->field_2f = rZR
    //     0xb35b60: stur            xzr, [x1, #0x2f]
    // 0xb35b64: ldur            x0, [fp, #-0x10]
    // 0xb35b68: StoreField: r1->field_b = r0
    //     0xb35b68: stur            w0, [x1, #0xb]
    // 0xb35b6c: r0 = Padding()
    //     0xb35b6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb35b70: r1 = Instance_EdgeInsets
    //     0xb35b70: add             x1, PP, #0x54, lsl #12  ; [pp+0x54a20] Obj!EdgeInsets@d59241
    //     0xb35b74: ldr             x1, [x1, #0xa20]
    // 0xb35b78: StoreField: r0->field_f = r1
    //     0xb35b78: stur            w1, [x0, #0xf]
    // 0xb35b7c: ldur            x1, [fp, #-8]
    // 0xb35b80: StoreField: r0->field_b = r1
    //     0xb35b80: stur            w1, [x0, #0xb]
    // 0xb35b84: LeaveFrame
    //     0xb35b84: mov             SP, fp
    //     0xb35b88: ldp             fp, lr, [SP], #0x10
    // 0xb35b8c: ret
    //     0xb35b8c: ret             
    // 0xb35b90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb35b90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb35b94: b               #0xb358c4
    // 0xb35b98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb35b98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb35b9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb35b9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4115, size: 0x24, field offset: 0xc
//   const constructor, 
class OffersBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e778, size: 0x24
    // 0xc7e778: EnterFrame
    //     0xc7e778: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e77c: mov             fp, SP
    // 0xc7e780: mov             x0, x1
    // 0xc7e784: r1 = <OffersBottomSheet>
    //     0xc7e784: add             x1, PP, #0x6e, lsl #12  ; [pp+0x6ec90] TypeArguments: <OffersBottomSheet>
    //     0xc7e788: ldr             x1, [x1, #0xc90]
    // 0xc7e78c: r0 = _OffersBottomSheetState()
    //     0xc7e78c: bl              #0xc7e79c  ; Allocate_OffersBottomSheetStateStub -> _OffersBottomSheetState (size=0x14)
    // 0xc7e790: LeaveFrame
    //     0xc7e790: mov             SP, fp
    //     0xc7e794: ldp             fp, lr, [SP], #0x10
    // 0xc7e798: ret
    //     0xc7e798: ret             
  }
}
