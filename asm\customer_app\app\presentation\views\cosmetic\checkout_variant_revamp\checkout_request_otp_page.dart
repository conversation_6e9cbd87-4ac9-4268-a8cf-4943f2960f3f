// lib: , url: package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart

// class id: 1049234, size: 0x8
class :: {
}

// class id: 4609, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestOtpPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x134a01c, size: 0x64
    // 0x134a01c: EnterFrame
    //     0x134a01c: stp             fp, lr, [SP, #-0x10]!
    //     0x134a020: mov             fp, SP
    // 0x134a024: AllocStack(0x18)
    //     0x134a024: sub             SP, SP, #0x18
    // 0x134a028: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x134a028: stur            x1, [fp, #-8]
    //     0x134a02c: stur            x2, [fp, #-0x10]
    // 0x134a030: r1 = 2
    //     0x134a030: movz            x1, #0x2
    // 0x134a034: r0 = AllocateContext()
    //     0x134a034: bl              #0x16f6108  ; AllocateContextStub
    // 0x134a038: mov             x1, x0
    // 0x134a03c: ldur            x0, [fp, #-8]
    // 0x134a040: stur            x1, [fp, #-0x18]
    // 0x134a044: StoreField: r1->field_f = r0
    //     0x134a044: stur            w0, [x1, #0xf]
    // 0x134a048: ldur            x0, [fp, #-0x10]
    // 0x134a04c: StoreField: r1->field_13 = r0
    //     0x134a04c: stur            w0, [x1, #0x13]
    // 0x134a050: r0 = Obx()
    //     0x134a050: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x134a054: ldur            x2, [fp, #-0x18]
    // 0x134a058: r1 = Function '<anonymous closure>':.
    //     0x134a058: add             x1, PP, #0x44, lsl #12  ; [pp+0x44120] AnonymousClosure: (0x134a080), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::bottomNavigationBar (0x134a01c)
    //     0x134a05c: ldr             x1, [x1, #0x120]
    // 0x134a060: stur            x0, [fp, #-8]
    // 0x134a064: r0 = AllocateClosure()
    //     0x134a064: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x134a068: mov             x1, x0
    // 0x134a06c: ldur            x0, [fp, #-8]
    // 0x134a070: StoreField: r0->field_b = r1
    //     0x134a070: stur            w1, [x0, #0xb]
    // 0x134a074: LeaveFrame
    //     0x134a074: mov             SP, fp
    //     0x134a078: ldp             fp, lr, [SP], #0x10
    // 0x134a07c: ret
    //     0x134a07c: ret             
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x134a080, size: 0x660
    // 0x134a080: EnterFrame
    //     0x134a080: stp             fp, lr, [SP, #-0x10]!
    //     0x134a084: mov             fp, SP
    // 0x134a088: AllocStack(0x60)
    //     0x134a088: sub             SP, SP, #0x60
    // 0x134a08c: SetupParameters()
    //     0x134a08c: ldr             x0, [fp, #0x10]
    //     0x134a090: ldur            w2, [x0, #0x17]
    //     0x134a094: add             x2, x2, HEAP, lsl #32
    //     0x134a098: stur            x2, [fp, #-8]
    // 0x134a09c: CheckStackOverflow
    //     0x134a09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x134a0a0: cmp             SP, x16
    //     0x134a0a4: b.ls            #0x134a694
    // 0x134a0a8: LoadField: r1 = r2->field_13
    //     0x134a0a8: ldur            w1, [x2, #0x13]
    // 0x134a0ac: DecompressPointer r1
    //     0x134a0ac: add             x1, x1, HEAP, lsl #32
    // 0x134a0b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x134a0b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x134a0b4: r0 = _of()
    //     0x134a0b4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x134a0b8: LoadField: r1 = r0->field_23
    //     0x134a0b8: ldur            w1, [x0, #0x23]
    // 0x134a0bc: DecompressPointer r1
    //     0x134a0bc: add             x1, x1, HEAP, lsl #32
    // 0x134a0c0: LoadField: d0 = r1->field_1f
    //     0x134a0c0: ldur            d0, [x1, #0x1f]
    // 0x134a0c4: stur            d0, [fp, #-0x40]
    // 0x134a0c8: r0 = EdgeInsets()
    //     0x134a0c8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x134a0cc: stur            x0, [fp, #-0x10]
    // 0x134a0d0: StoreField: r0->field_7 = rZR
    //     0x134a0d0: stur            xzr, [x0, #7]
    // 0x134a0d4: StoreField: r0->field_f = rZR
    //     0x134a0d4: stur            xzr, [x0, #0xf]
    // 0x134a0d8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x134a0d8: stur            xzr, [x0, #0x17]
    // 0x134a0dc: ldur            d0, [fp, #-0x40]
    // 0x134a0e0: StoreField: r0->field_1f = d0
    //     0x134a0e0: stur            d0, [x0, #0x1f]
    // 0x134a0e4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x134a0e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x134a0e8: ldr             x0, [x0, #0x1c80]
    //     0x134a0ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x134a0f0: cmp             w0, w16
    //     0x134a0f4: b.ne            #0x134a100
    //     0x134a0f8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x134a0fc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x134a100: r0 = GetNavigation.width()
    //     0x134a100: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x134a104: ldur            x2, [fp, #-8]
    // 0x134a108: stur            d0, [fp, #-0x40]
    // 0x134a10c: LoadField: r1 = r2->field_13
    //     0x134a10c: ldur            w1, [x2, #0x13]
    // 0x134a110: DecompressPointer r1
    //     0x134a110: add             x1, x1, HEAP, lsl #32
    // 0x134a114: r0 = of()
    //     0x134a114: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134a118: LoadField: r1 = r0->field_5b
    //     0x134a118: ldur            w1, [x0, #0x5b]
    // 0x134a11c: DecompressPointer r1
    //     0x134a11c: add             x1, x1, HEAP, lsl #32
    // 0x134a120: r0 = LoadClassIdInstr(r1)
    //     0x134a120: ldur            x0, [x1, #-1]
    //     0x134a124: ubfx            x0, x0, #0xc, #0x14
    // 0x134a128: r2 = 40
    //     0x134a128: movz            x2, #0x28
    // 0x134a12c: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x134a12c: sub             lr, x0, #0xfe7
    //     0x134a130: ldr             lr, [x21, lr, lsl #3]
    //     0x134a134: blr             lr
    // 0x134a138: stur            x0, [fp, #-0x18]
    // 0x134a13c: r0 = BorderSide()
    //     0x134a13c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x134a140: mov             x1, x0
    // 0x134a144: ldur            x0, [fp, #-0x18]
    // 0x134a148: stur            x1, [fp, #-0x20]
    // 0x134a14c: StoreField: r1->field_7 = r0
    //     0x134a14c: stur            w0, [x1, #7]
    // 0x134a150: d0 = 2.000000
    //     0x134a150: fmov            d0, #2.00000000
    // 0x134a154: StoreField: r1->field_b = d0
    //     0x134a154: stur            d0, [x1, #0xb]
    // 0x134a158: r0 = Instance_BorderStyle
    //     0x134a158: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x134a15c: ldr             x0, [x0, #0xf68]
    // 0x134a160: StoreField: r1->field_13 = r0
    //     0x134a160: stur            w0, [x1, #0x13]
    // 0x134a164: d0 = -1.000000
    //     0x134a164: fmov            d0, #-1.00000000
    // 0x134a168: ArrayStore: r1[0] = d0  ; List_8
    //     0x134a168: stur            d0, [x1, #0x17]
    // 0x134a16c: r0 = Border()
    //     0x134a16c: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x134a170: mov             x1, x0
    // 0x134a174: ldur            x0, [fp, #-0x20]
    // 0x134a178: stur            x1, [fp, #-0x18]
    // 0x134a17c: StoreField: r1->field_7 = r0
    //     0x134a17c: stur            w0, [x1, #7]
    // 0x134a180: r0 = Instance_BorderSide
    //     0x134a180: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x134a184: ldr             x0, [x0, #0xe20]
    // 0x134a188: StoreField: r1->field_b = r0
    //     0x134a188: stur            w0, [x1, #0xb]
    // 0x134a18c: StoreField: r1->field_f = r0
    //     0x134a18c: stur            w0, [x1, #0xf]
    // 0x134a190: StoreField: r1->field_13 = r0
    //     0x134a190: stur            w0, [x1, #0x13]
    // 0x134a194: r0 = BoxDecoration()
    //     0x134a194: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x134a198: mov             x1, x0
    // 0x134a19c: ldur            x0, [fp, #-0x18]
    // 0x134a1a0: stur            x1, [fp, #-0x20]
    // 0x134a1a4: StoreField: r1->field_f = r0
    //     0x134a1a4: stur            w0, [x1, #0xf]
    // 0x134a1a8: r0 = Instance_BoxShape
    //     0x134a1a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x134a1ac: ldr             x0, [x0, #0x80]
    // 0x134a1b0: StoreField: r1->field_23 = r0
    //     0x134a1b0: stur            w0, [x1, #0x23]
    // 0x134a1b4: r16 = <EdgeInsets>
    //     0x134a1b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x134a1b8: ldr             x16, [x16, #0xda0]
    // 0x134a1bc: r30 = Instance_EdgeInsets
    //     0x134a1bc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x134a1c0: ldr             lr, [lr, #0x1f0]
    // 0x134a1c4: stp             lr, x16, [SP]
    // 0x134a1c8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134a1c8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x134a1cc: r0 = all()
    //     0x134a1cc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x134a1d0: ldur            x2, [fp, #-8]
    // 0x134a1d4: stur            x0, [fp, #-0x18]
    // 0x134a1d8: LoadField: r1 = r2->field_f
    //     0x134a1d8: ldur            w1, [x2, #0xf]
    // 0x134a1dc: DecompressPointer r1
    //     0x134a1dc: add             x1, x1, HEAP, lsl #32
    // 0x134a1e0: r0 = controller()
    //     0x134a1e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x134a1e4: LoadField: r1 = r0->field_d3
    //     0x134a1e4: ldur            w1, [x0, #0xd3]
    // 0x134a1e8: DecompressPointer r1
    //     0x134a1e8: add             x1, x1, HEAP, lsl #32
    // 0x134a1ec: r0 = value()
    //     0x134a1ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x134a1f0: tbnz            w0, #4, #0x134a210
    // 0x134a1f4: ldur            x2, [fp, #-8]
    // 0x134a1f8: LoadField: r1 = r2->field_13
    //     0x134a1f8: ldur            w1, [x2, #0x13]
    // 0x134a1fc: DecompressPointer r1
    //     0x134a1fc: add             x1, x1, HEAP, lsl #32
    // 0x134a200: r0 = of()
    //     0x134a200: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134a204: LoadField: r1 = r0->field_5b
    //     0x134a204: ldur            w1, [x0, #0x5b]
    // 0x134a208: DecompressPointer r1
    //     0x134a208: add             x1, x1, HEAP, lsl #32
    // 0x134a20c: b               #0x134a218
    // 0x134a210: r1 = Instance_MaterialColor
    //     0x134a210: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x134a214: ldr             x1, [x1, #0xdc0]
    // 0x134a218: ldur            x2, [fp, #-8]
    // 0x134a21c: ldur            x0, [fp, #-0x18]
    // 0x134a220: r16 = <Color>
    //     0x134a220: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x134a224: ldr             x16, [x16, #0xf80]
    // 0x134a228: stp             x1, x16, [SP]
    // 0x134a22c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134a22c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x134a230: r0 = all()
    //     0x134a230: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x134a234: stur            x0, [fp, #-0x28]
    // 0x134a238: r16 = <RoundedRectangleBorder>
    //     0x134a238: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x134a23c: ldr             x16, [x16, #0xf78]
    // 0x134a240: r30 = Instance_RoundedRectangleBorder
    //     0x134a240: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x134a244: ldr             lr, [lr, #0x888]
    // 0x134a248: stp             lr, x16, [SP]
    // 0x134a24c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134a24c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x134a250: r0 = all()
    //     0x134a250: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x134a254: stur            x0, [fp, #-0x30]
    // 0x134a258: r0 = ButtonStyle()
    //     0x134a258: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x134a25c: mov             x1, x0
    // 0x134a260: ldur            x0, [fp, #-0x28]
    // 0x134a264: stur            x1, [fp, #-0x38]
    // 0x134a268: StoreField: r1->field_b = r0
    //     0x134a268: stur            w0, [x1, #0xb]
    // 0x134a26c: ldur            x0, [fp, #-0x18]
    // 0x134a270: StoreField: r1->field_23 = r0
    //     0x134a270: stur            w0, [x1, #0x23]
    // 0x134a274: ldur            x0, [fp, #-0x30]
    // 0x134a278: StoreField: r1->field_43 = r0
    //     0x134a278: stur            w0, [x1, #0x43]
    // 0x134a27c: r0 = TextButtonThemeData()
    //     0x134a27c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x134a280: mov             x2, x0
    // 0x134a284: ldur            x0, [fp, #-0x38]
    // 0x134a288: stur            x2, [fp, #-0x18]
    // 0x134a28c: StoreField: r2->field_7 = r0
    //     0x134a28c: stur            w0, [x2, #7]
    // 0x134a290: ldur            x0, [fp, #-8]
    // 0x134a294: LoadField: r1 = r0->field_f
    //     0x134a294: ldur            w1, [x0, #0xf]
    // 0x134a298: DecompressPointer r1
    //     0x134a298: add             x1, x1, HEAP, lsl #32
    // 0x134a29c: r0 = controller()
    //     0x134a29c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x134a2a0: LoadField: r1 = r0->field_d3
    //     0x134a2a0: ldur            w1, [x0, #0xd3]
    // 0x134a2a4: DecompressPointer r1
    //     0x134a2a4: add             x1, x1, HEAP, lsl #32
    // 0x134a2a8: r0 = value()
    //     0x134a2a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x134a2ac: tbnz            w0, #4, #0x134a2c8
    // 0x134a2b0: ldur            x2, [fp, #-8]
    // 0x134a2b4: r1 = Function '<anonymous closure>':.
    //     0x134a2b4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44128] AnonymousClosure: (0x12eaccc), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::bottomNavigationBar (0x135dbc0)
    //     0x134a2b8: ldr             x1, [x1, #0x128]
    // 0x134a2bc: r0 = AllocateClosure()
    //     0x134a2bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x134a2c0: mov             x4, x0
    // 0x134a2c4: b               #0x134a2cc
    // 0x134a2c8: r4 = Null
    //     0x134a2c8: mov             x4, NULL
    // 0x134a2cc: ldur            x2, [fp, #-8]
    // 0x134a2d0: ldur            x3, [fp, #-0x10]
    // 0x134a2d4: ldur            d0, [fp, #-0x40]
    // 0x134a2d8: ldur            x0, [fp, #-0x18]
    // 0x134a2dc: stur            x4, [fp, #-0x28]
    // 0x134a2e0: LoadField: r1 = r2->field_13
    //     0x134a2e0: ldur            w1, [x2, #0x13]
    // 0x134a2e4: DecompressPointer r1
    //     0x134a2e4: add             x1, x1, HEAP, lsl #32
    // 0x134a2e8: r0 = of()
    //     0x134a2e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134a2ec: LoadField: r1 = r0->field_87
    //     0x134a2ec: ldur            w1, [x0, #0x87]
    // 0x134a2f0: DecompressPointer r1
    //     0x134a2f0: add             x1, x1, HEAP, lsl #32
    // 0x134a2f4: LoadField: r0 = r1->field_7
    //     0x134a2f4: ldur            w0, [x1, #7]
    // 0x134a2f8: DecompressPointer r0
    //     0x134a2f8: add             x0, x0, HEAP, lsl #32
    // 0x134a2fc: r16 = 14.000000
    //     0x134a2fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x134a300: ldr             x16, [x16, #0x1d8]
    // 0x134a304: r30 = Instance_Color
    //     0x134a304: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x134a308: stp             lr, x16, [SP]
    // 0x134a30c: mov             x1, x0
    // 0x134a310: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x134a310: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x134a314: ldr             x4, [x4, #0xaa0]
    // 0x134a318: r0 = copyWith()
    //     0x134a318: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x134a31c: stur            x0, [fp, #-0x30]
    // 0x134a320: r0 = Text()
    //     0x134a320: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x134a324: mov             x1, x0
    // 0x134a328: r0 = "CONTINUE"
    //     0x134a328: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x134a32c: ldr             x0, [x0, #0xac8]
    // 0x134a330: stur            x1, [fp, #-0x38]
    // 0x134a334: StoreField: r1->field_b = r0
    //     0x134a334: stur            w0, [x1, #0xb]
    // 0x134a338: ldur            x0, [fp, #-0x30]
    // 0x134a33c: StoreField: r1->field_13 = r0
    //     0x134a33c: stur            w0, [x1, #0x13]
    // 0x134a340: r0 = TextButton()
    //     0x134a340: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x134a344: mov             x1, x0
    // 0x134a348: ldur            x0, [fp, #-0x28]
    // 0x134a34c: stur            x1, [fp, #-0x30]
    // 0x134a350: StoreField: r1->field_b = r0
    //     0x134a350: stur            w0, [x1, #0xb]
    // 0x134a354: r0 = false
    //     0x134a354: add             x0, NULL, #0x30  ; false
    // 0x134a358: StoreField: r1->field_27 = r0
    //     0x134a358: stur            w0, [x1, #0x27]
    // 0x134a35c: r2 = true
    //     0x134a35c: add             x2, NULL, #0x20  ; true
    // 0x134a360: StoreField: r1->field_2f = r2
    //     0x134a360: stur            w2, [x1, #0x2f]
    // 0x134a364: ldur            x3, [fp, #-0x38]
    // 0x134a368: StoreField: r1->field_37 = r3
    //     0x134a368: stur            w3, [x1, #0x37]
    // 0x134a36c: r0 = TextButtonTheme()
    //     0x134a36c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x134a370: mov             x1, x0
    // 0x134a374: ldur            x0, [fp, #-0x18]
    // 0x134a378: stur            x1, [fp, #-0x28]
    // 0x134a37c: StoreField: r1->field_f = r0
    //     0x134a37c: stur            w0, [x1, #0xf]
    // 0x134a380: ldur            x0, [fp, #-0x30]
    // 0x134a384: StoreField: r1->field_b = r0
    //     0x134a384: stur            w0, [x1, #0xb]
    // 0x134a388: ldur            d0, [fp, #-0x40]
    // 0x134a38c: r0 = inline_Allocate_Double()
    //     0x134a38c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x134a390: add             x0, x0, #0x10
    //     0x134a394: cmp             x2, x0
    //     0x134a398: b.ls            #0x134a69c
    //     0x134a39c: str             x0, [THR, #0x50]  ; THR::top
    //     0x134a3a0: sub             x0, x0, #0xf
    //     0x134a3a4: movz            x2, #0xe15c
    //     0x134a3a8: movk            x2, #0x3, lsl #16
    //     0x134a3ac: stur            x2, [x0, #-1]
    // 0x134a3b0: StoreField: r0->field_7 = d0
    //     0x134a3b0: stur            d0, [x0, #7]
    // 0x134a3b4: stur            x0, [fp, #-0x18]
    // 0x134a3b8: r0 = Container()
    //     0x134a3b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x134a3bc: stur            x0, [fp, #-0x30]
    // 0x134a3c0: r16 = Instance_EdgeInsets
    //     0x134a3c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x134a3c4: ldr             x16, [x16, #0xe48]
    // 0x134a3c8: ldur            lr, [fp, #-0x18]
    // 0x134a3cc: stp             lr, x16, [SP, #0x10]
    // 0x134a3d0: ldur            x16, [fp, #-0x20]
    // 0x134a3d4: ldur            lr, [fp, #-0x28]
    // 0x134a3d8: stp             lr, x16, [SP]
    // 0x134a3dc: mov             x1, x0
    // 0x134a3e0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x134a3e0: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x134a3e4: ldr             x4, [x4, #0x18]
    // 0x134a3e8: r0 = Container()
    //     0x134a3e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x134a3ec: r0 = GetNavigation.size()
    //     0x134a3ec: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x134a3f0: LoadField: d0 = r0->field_7
    //     0x134a3f0: ldur            d0, [x0, #7]
    // 0x134a3f4: ldur            x0, [fp, #-8]
    // 0x134a3f8: stur            d0, [fp, #-0x40]
    // 0x134a3fc: LoadField: r1 = r0->field_13
    //     0x134a3fc: ldur            w1, [x0, #0x13]
    // 0x134a400: DecompressPointer r1
    //     0x134a400: add             x1, x1, HEAP, lsl #32
    // 0x134a404: r0 = of()
    //     0x134a404: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x134a408: LoadField: r1 = r0->field_87
    //     0x134a408: ldur            w1, [x0, #0x87]
    // 0x134a40c: DecompressPointer r1
    //     0x134a40c: add             x1, x1, HEAP, lsl #32
    // 0x134a410: LoadField: r0 = r1->field_2b
    //     0x134a410: ldur            w0, [x1, #0x2b]
    // 0x134a414: DecompressPointer r0
    //     0x134a414: add             x0, x0, HEAP, lsl #32
    // 0x134a418: stur            x0, [fp, #-8]
    // 0x134a41c: r1 = Instance_Color
    //     0x134a41c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x134a420: d0 = 0.700000
    //     0x134a420: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x134a424: ldr             d0, [x17, #0xf48]
    // 0x134a428: r0 = withOpacity()
    //     0x134a428: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x134a42c: r16 = 10.000000
    //     0x134a42c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x134a430: stp             x0, x16, [SP]
    // 0x134a434: ldur            x1, [fp, #-8]
    // 0x134a438: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x134a438: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x134a43c: ldr             x4, [x4, #0xaa0]
    // 0x134a440: r0 = copyWith()
    //     0x134a440: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x134a444: stur            x0, [fp, #-8]
    // 0x134a448: r0 = Text()
    //     0x134a448: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x134a44c: mov             x1, x0
    // 0x134a450: r0 = "Powered By"
    //     0x134a450: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x134a454: ldr             x0, [x0, #0x750]
    // 0x134a458: stur            x1, [fp, #-0x18]
    // 0x134a45c: StoreField: r1->field_b = r0
    //     0x134a45c: stur            w0, [x1, #0xb]
    // 0x134a460: ldur            x0, [fp, #-8]
    // 0x134a464: StoreField: r1->field_13 = r0
    //     0x134a464: stur            w0, [x1, #0x13]
    // 0x134a468: r0 = SvgPicture()
    //     0x134a468: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x134a46c: stur            x0, [fp, #-8]
    // 0x134a470: r16 = 20.000000
    //     0x134a470: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x134a474: ldr             x16, [x16, #0xac8]
    // 0x134a478: str             x16, [SP]
    // 0x134a47c: mov             x1, x0
    // 0x134a480: r2 = "assets/images/shopdeck.svg"
    //     0x134a480: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x134a484: ldr             x2, [x2, #0x758]
    // 0x134a488: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x134a488: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x134a48c: ldr             x4, [x4, #0x760]
    // 0x134a490: r0 = SvgPicture.asset()
    //     0x134a490: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x134a494: r1 = Null
    //     0x134a494: mov             x1, NULL
    // 0x134a498: r2 = 4
    //     0x134a498: movz            x2, #0x4
    // 0x134a49c: r0 = AllocateArray()
    //     0x134a49c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x134a4a0: mov             x2, x0
    // 0x134a4a4: ldur            x0, [fp, #-0x18]
    // 0x134a4a8: stur            x2, [fp, #-0x20]
    // 0x134a4ac: StoreField: r2->field_f = r0
    //     0x134a4ac: stur            w0, [x2, #0xf]
    // 0x134a4b0: ldur            x0, [fp, #-8]
    // 0x134a4b4: StoreField: r2->field_13 = r0
    //     0x134a4b4: stur            w0, [x2, #0x13]
    // 0x134a4b8: r1 = <Widget>
    //     0x134a4b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x134a4bc: r0 = AllocateGrowableArray()
    //     0x134a4bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x134a4c0: mov             x1, x0
    // 0x134a4c4: ldur            x0, [fp, #-0x20]
    // 0x134a4c8: stur            x1, [fp, #-8]
    // 0x134a4cc: StoreField: r1->field_f = r0
    //     0x134a4cc: stur            w0, [x1, #0xf]
    // 0x134a4d0: r2 = 4
    //     0x134a4d0: movz            x2, #0x4
    // 0x134a4d4: StoreField: r1->field_b = r2
    //     0x134a4d4: stur            w2, [x1, #0xb]
    // 0x134a4d8: r0 = Row()
    //     0x134a4d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x134a4dc: mov             x1, x0
    // 0x134a4e0: r0 = Instance_Axis
    //     0x134a4e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x134a4e4: stur            x1, [fp, #-0x18]
    // 0x134a4e8: StoreField: r1->field_f = r0
    //     0x134a4e8: stur            w0, [x1, #0xf]
    // 0x134a4ec: r0 = Instance_MainAxisAlignment
    //     0x134a4ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x134a4f0: ldr             x0, [x0, #0xa08]
    // 0x134a4f4: StoreField: r1->field_13 = r0
    //     0x134a4f4: stur            w0, [x1, #0x13]
    // 0x134a4f8: r2 = Instance_MainAxisSize
    //     0x134a4f8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x134a4fc: ldr             x2, [x2, #0xdd0]
    // 0x134a500: ArrayStore: r1[0] = r2  ; List_4
    //     0x134a500: stur            w2, [x1, #0x17]
    // 0x134a504: r3 = Instance_CrossAxisAlignment
    //     0x134a504: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x134a508: ldr             x3, [x3, #0xa18]
    // 0x134a50c: StoreField: r1->field_1b = r3
    //     0x134a50c: stur            w3, [x1, #0x1b]
    // 0x134a510: r4 = Instance_VerticalDirection
    //     0x134a510: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x134a514: ldr             x4, [x4, #0xa20]
    // 0x134a518: StoreField: r1->field_23 = r4
    //     0x134a518: stur            w4, [x1, #0x23]
    // 0x134a51c: r5 = Instance_Clip
    //     0x134a51c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x134a520: ldr             x5, [x5, #0x38]
    // 0x134a524: StoreField: r1->field_2b = r5
    //     0x134a524: stur            w5, [x1, #0x2b]
    // 0x134a528: StoreField: r1->field_2f = rZR
    //     0x134a528: stur            xzr, [x1, #0x2f]
    // 0x134a52c: ldur            x6, [fp, #-8]
    // 0x134a530: StoreField: r1->field_b = r6
    //     0x134a530: stur            w6, [x1, #0xb]
    // 0x134a534: ldur            d0, [fp, #-0x40]
    // 0x134a538: r6 = inline_Allocate_Double()
    //     0x134a538: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x134a53c: add             x6, x6, #0x10
    //     0x134a540: cmp             x7, x6
    //     0x134a544: b.ls            #0x134a6b4
    //     0x134a548: str             x6, [THR, #0x50]  ; THR::top
    //     0x134a54c: sub             x6, x6, #0xf
    //     0x134a550: movz            x7, #0xe15c
    //     0x134a554: movk            x7, #0x3, lsl #16
    //     0x134a558: stur            x7, [x6, #-1]
    // 0x134a55c: StoreField: r6->field_7 = d0
    //     0x134a55c: stur            d0, [x6, #7]
    // 0x134a560: stur            x6, [fp, #-8]
    // 0x134a564: r0 = Container()
    //     0x134a564: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x134a568: stur            x0, [fp, #-0x20]
    // 0x134a56c: r16 = Instance_Alignment
    //     0x134a56c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x134a570: ldr             x16, [x16, #0xb10]
    // 0x134a574: ldur            lr, [fp, #-8]
    // 0x134a578: stp             lr, x16, [SP, #0x10]
    // 0x134a57c: r16 = Instance_BoxDecoration
    //     0x134a57c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x134a580: ldr             x16, [x16, #0x768]
    // 0x134a584: ldur            lr, [fp, #-0x18]
    // 0x134a588: stp             lr, x16, [SP]
    // 0x134a58c: mov             x1, x0
    // 0x134a590: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x134a590: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x134a594: ldr             x4, [x4, #0x770]
    // 0x134a598: r0 = Container()
    //     0x134a598: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x134a59c: r1 = Null
    //     0x134a59c: mov             x1, NULL
    // 0x134a5a0: r2 = 4
    //     0x134a5a0: movz            x2, #0x4
    // 0x134a5a4: r0 = AllocateArray()
    //     0x134a5a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x134a5a8: mov             x2, x0
    // 0x134a5ac: ldur            x0, [fp, #-0x30]
    // 0x134a5b0: stur            x2, [fp, #-8]
    // 0x134a5b4: StoreField: r2->field_f = r0
    //     0x134a5b4: stur            w0, [x2, #0xf]
    // 0x134a5b8: ldur            x0, [fp, #-0x20]
    // 0x134a5bc: StoreField: r2->field_13 = r0
    //     0x134a5bc: stur            w0, [x2, #0x13]
    // 0x134a5c0: r1 = <Widget>
    //     0x134a5c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x134a5c4: r0 = AllocateGrowableArray()
    //     0x134a5c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x134a5c8: mov             x1, x0
    // 0x134a5cc: ldur            x0, [fp, #-8]
    // 0x134a5d0: stur            x1, [fp, #-0x18]
    // 0x134a5d4: StoreField: r1->field_f = r0
    //     0x134a5d4: stur            w0, [x1, #0xf]
    // 0x134a5d8: r0 = 4
    //     0x134a5d8: movz            x0, #0x4
    // 0x134a5dc: StoreField: r1->field_b = r0
    //     0x134a5dc: stur            w0, [x1, #0xb]
    // 0x134a5e0: r0 = Column()
    //     0x134a5e0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x134a5e4: mov             x1, x0
    // 0x134a5e8: r0 = Instance_Axis
    //     0x134a5e8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x134a5ec: stur            x1, [fp, #-8]
    // 0x134a5f0: StoreField: r1->field_f = r0
    //     0x134a5f0: stur            w0, [x1, #0xf]
    // 0x134a5f4: r0 = Instance_MainAxisAlignment
    //     0x134a5f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x134a5f8: ldr             x0, [x0, #0xa08]
    // 0x134a5fc: StoreField: r1->field_13 = r0
    //     0x134a5fc: stur            w0, [x1, #0x13]
    // 0x134a600: r0 = Instance_MainAxisSize
    //     0x134a600: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x134a604: ldr             x0, [x0, #0xdd0]
    // 0x134a608: ArrayStore: r1[0] = r0  ; List_4
    //     0x134a608: stur            w0, [x1, #0x17]
    // 0x134a60c: r0 = Instance_CrossAxisAlignment
    //     0x134a60c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x134a610: ldr             x0, [x0, #0xa18]
    // 0x134a614: StoreField: r1->field_1b = r0
    //     0x134a614: stur            w0, [x1, #0x1b]
    // 0x134a618: r0 = Instance_VerticalDirection
    //     0x134a618: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x134a61c: ldr             x0, [x0, #0xa20]
    // 0x134a620: StoreField: r1->field_23 = r0
    //     0x134a620: stur            w0, [x1, #0x23]
    // 0x134a624: r0 = Instance_Clip
    //     0x134a624: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x134a628: ldr             x0, [x0, #0x38]
    // 0x134a62c: StoreField: r1->field_2b = r0
    //     0x134a62c: stur            w0, [x1, #0x2b]
    // 0x134a630: StoreField: r1->field_2f = rZR
    //     0x134a630: stur            xzr, [x1, #0x2f]
    // 0x134a634: ldur            x0, [fp, #-0x18]
    // 0x134a638: StoreField: r1->field_b = r0
    //     0x134a638: stur            w0, [x1, #0xb]
    // 0x134a63c: r0 = Padding()
    //     0x134a63c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x134a640: mov             x1, x0
    // 0x134a644: ldur            x0, [fp, #-0x10]
    // 0x134a648: stur            x1, [fp, #-0x18]
    // 0x134a64c: StoreField: r1->field_f = r0
    //     0x134a64c: stur            w0, [x1, #0xf]
    // 0x134a650: ldur            x0, [fp, #-8]
    // 0x134a654: StoreField: r1->field_b = r0
    //     0x134a654: stur            w0, [x1, #0xb]
    // 0x134a658: r0 = SafeArea()
    //     0x134a658: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x134a65c: r1 = true
    //     0x134a65c: add             x1, NULL, #0x20  ; true
    // 0x134a660: StoreField: r0->field_b = r1
    //     0x134a660: stur            w1, [x0, #0xb]
    // 0x134a664: StoreField: r0->field_f = r1
    //     0x134a664: stur            w1, [x0, #0xf]
    // 0x134a668: StoreField: r0->field_13 = r1
    //     0x134a668: stur            w1, [x0, #0x13]
    // 0x134a66c: ArrayStore: r0[0] = r1  ; List_4
    //     0x134a66c: stur            w1, [x0, #0x17]
    // 0x134a670: r1 = Instance_EdgeInsets
    //     0x134a670: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x134a674: StoreField: r0->field_1b = r1
    //     0x134a674: stur            w1, [x0, #0x1b]
    // 0x134a678: r1 = false
    //     0x134a678: add             x1, NULL, #0x30  ; false
    // 0x134a67c: StoreField: r0->field_1f = r1
    //     0x134a67c: stur            w1, [x0, #0x1f]
    // 0x134a680: ldur            x1, [fp, #-0x18]
    // 0x134a684: StoreField: r0->field_23 = r1
    //     0x134a684: stur            w1, [x0, #0x23]
    // 0x134a688: LeaveFrame
    //     0x134a688: mov             SP, fp
    //     0x134a68c: ldp             fp, lr, [SP], #0x10
    // 0x134a690: ret
    //     0x134a690: ret             
    // 0x134a694: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x134a694: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x134a698: b               #0x134a0a8
    // 0x134a69c: SaveReg d0
    //     0x134a69c: str             q0, [SP, #-0x10]!
    // 0x134a6a0: SaveReg r1
    //     0x134a6a0: str             x1, [SP, #-8]!
    // 0x134a6a4: r0 = AllocateDouble()
    //     0x134a6a4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x134a6a8: RestoreReg r1
    //     0x134a6a8: ldr             x1, [SP], #8
    // 0x134a6ac: RestoreReg d0
    //     0x134a6ac: ldr             q0, [SP], #0x10
    // 0x134a6b0: b               #0x134a3b0
    // 0x134a6b4: SaveReg d0
    //     0x134a6b4: str             q0, [SP, #-0x10]!
    // 0x134a6b8: stp             x4, x5, [SP, #-0x10]!
    // 0x134a6bc: stp             x2, x3, [SP, #-0x10]!
    // 0x134a6c0: stp             x0, x1, [SP, #-0x10]!
    // 0x134a6c4: r0 = AllocateDouble()
    //     0x134a6c4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x134a6c8: mov             x6, x0
    // 0x134a6cc: ldp             x0, x1, [SP], #0x10
    // 0x134a6d0: ldp             x2, x3, [SP], #0x10
    // 0x134a6d4: ldp             x4, x5, [SP], #0x10
    // 0x134a6d8: RestoreReg d0
    //     0x134a6d8: ldr             q0, [SP], #0x10
    // 0x134a6dc: b               #0x134a55c
  }
  _ body(/* No info */) {
    // ** addr: 0x1486308, size: 0x218
    // 0x1486308: EnterFrame
    //     0x1486308: stp             fp, lr, [SP, #-0x10]!
    //     0x148630c: mov             fp, SP
    // 0x1486310: AllocStack(0x30)
    //     0x1486310: sub             SP, SP, #0x30
    // 0x1486314: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1486314: stur            x1, [fp, #-8]
    //     0x1486318: stur            x2, [fp, #-0x10]
    // 0x148631c: CheckStackOverflow
    //     0x148631c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1486320: cmp             SP, x16
    //     0x1486324: b.ls            #0x1486518
    // 0x1486328: r1 = 2
    //     0x1486328: movz            x1, #0x2
    // 0x148632c: r0 = AllocateContext()
    //     0x148632c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1486330: ldur            x1, [fp, #-8]
    // 0x1486334: stur            x0, [fp, #-0x18]
    // 0x1486338: StoreField: r0->field_f = r1
    //     0x1486338: stur            w1, [x0, #0xf]
    // 0x148633c: ldur            x2, [fp, #-0x10]
    // 0x1486340: StoreField: r0->field_13 = r2
    //     0x1486340: stur            w2, [x0, #0x13]
    // 0x1486344: r0 = Obx()
    //     0x1486344: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1486348: ldur            x2, [fp, #-0x18]
    // 0x148634c: r1 = Function '<anonymous closure>':.
    //     0x148634c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44130] AnonymousClosure: (0x148681c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1486308)
    //     0x1486350: ldr             x1, [x1, #0x130]
    // 0x1486354: stur            x0, [fp, #-0x10]
    // 0x1486358: r0 = AllocateClosure()
    //     0x1486358: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148635c: mov             x1, x0
    // 0x1486360: ldur            x0, [fp, #-0x10]
    // 0x1486364: StoreField: r0->field_b = r1
    //     0x1486364: stur            w1, [x0, #0xb]
    // 0x1486368: ldur            x1, [fp, #-8]
    // 0x148636c: r0 = controller()
    //     0x148636c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1486370: LoadField: r1 = r0->field_ab
    //     0x1486370: ldur            w1, [x0, #0xab]
    // 0x1486374: DecompressPointer r1
    //     0x1486374: add             x1, x1, HEAP, lsl #32
    // 0x1486378: r0 = value()
    //     0x1486378: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148637c: ldur            x2, [fp, #-0x18]
    // 0x1486380: r1 = Function '<anonymous closure>':.
    //     0x1486380: add             x1, PP, #0x44, lsl #12  ; [pp+0x44138] AnonymousClosure: (0x13954a8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x1486384: ldr             x1, [x1, #0x138]
    // 0x1486388: stur            x0, [fp, #-0x20]
    // 0x148638c: r0 = AllocateClosure()
    //     0x148638c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1486390: stur            x0, [fp, #-0x28]
    // 0x1486394: r0 = CheckoutOtpWidget()
    //     0x1486394: bl              #0x1486520  ; AllocateCheckoutOtpWidgetStub -> CheckoutOtpWidget (size=0x20)
    // 0x1486398: mov             x3, x0
    // 0x148639c: ldur            x0, [fp, #-0x28]
    // 0x14863a0: stur            x3, [fp, #-0x30]
    // 0x14863a4: StoreField: r3->field_b = r0
    //     0x14863a4: stur            w0, [x3, #0xb]
    // 0x14863a8: ldur            x2, [fp, #-0x18]
    // 0x14863ac: r1 = Function '<anonymous closure>':.
    //     0x14863ac: add             x1, PP, #0x44, lsl #12  ; [pp+0x44140] AnonymousClosure: (0x13951a4), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x14863b0: ldr             x1, [x1, #0x140]
    // 0x14863b4: r0 = AllocateClosure()
    //     0x14863b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14863b8: mov             x1, x0
    // 0x14863bc: ldur            x0, [fp, #-0x30]
    // 0x14863c0: StoreField: r0->field_f = r1
    //     0x14863c0: stur            w1, [x0, #0xf]
    // 0x14863c4: r1 = Function '<anonymous closure>':.
    //     0x14863c4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44148] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x14863c8: ldr             x1, [x1, #0x148]
    // 0x14863cc: r2 = Null
    //     0x14863cc: mov             x2, NULL
    // 0x14863d0: r0 = AllocateClosure()
    //     0x14863d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14863d4: mov             x1, x0
    // 0x14863d8: ldur            x0, [fp, #-0x30]
    // 0x14863dc: StoreField: r0->field_13 = r1
    //     0x14863dc: stur            w1, [x0, #0x13]
    // 0x14863e0: ldur            x1, [fp, #-0x20]
    // 0x14863e4: StoreField: r0->field_1b = r1
    //     0x14863e4: stur            w1, [x0, #0x1b]
    // 0x14863e8: ldur            x2, [fp, #-0x18]
    // 0x14863ec: r1 = Function '<anonymous closure>':.
    //     0x14863ec: add             x1, PP, #0x44, lsl #12  ; [pp+0x44150] AnonymousClosure: (0x1486614), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1486308)
    //     0x14863f0: ldr             x1, [x1, #0x150]
    // 0x14863f4: r0 = AllocateClosure()
    //     0x14863f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14863f8: mov             x1, x0
    // 0x14863fc: ldur            x0, [fp, #-0x30]
    // 0x1486400: ArrayStore: r0[0] = r1  ; List_4
    //     0x1486400: stur            w1, [x0, #0x17]
    // 0x1486404: r1 = Null
    //     0x1486404: mov             x1, NULL
    // 0x1486408: r2 = 4
    //     0x1486408: movz            x2, #0x4
    // 0x148640c: r0 = AllocateArray()
    //     0x148640c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1486410: mov             x2, x0
    // 0x1486414: ldur            x0, [fp, #-0x10]
    // 0x1486418: stur            x2, [fp, #-0x18]
    // 0x148641c: StoreField: r2->field_f = r0
    //     0x148641c: stur            w0, [x2, #0xf]
    // 0x1486420: ldur            x0, [fp, #-0x30]
    // 0x1486424: StoreField: r2->field_13 = r0
    //     0x1486424: stur            w0, [x2, #0x13]
    // 0x1486428: r1 = <Widget>
    //     0x1486428: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x148642c: r0 = AllocateGrowableArray()
    //     0x148642c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1486430: mov             x1, x0
    // 0x1486434: ldur            x0, [fp, #-0x18]
    // 0x1486438: stur            x1, [fp, #-0x10]
    // 0x148643c: StoreField: r1->field_f = r0
    //     0x148643c: stur            w0, [x1, #0xf]
    // 0x1486440: r0 = 4
    //     0x1486440: movz            x0, #0x4
    // 0x1486444: StoreField: r1->field_b = r0
    //     0x1486444: stur            w0, [x1, #0xb]
    // 0x1486448: r0 = Column()
    //     0x1486448: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x148644c: mov             x1, x0
    // 0x1486450: r0 = Instance_Axis
    //     0x1486450: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1486454: stur            x1, [fp, #-0x18]
    // 0x1486458: StoreField: r1->field_f = r0
    //     0x1486458: stur            w0, [x1, #0xf]
    // 0x148645c: r0 = Instance_MainAxisAlignment
    //     0x148645c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1486460: ldr             x0, [x0, #0xa08]
    // 0x1486464: StoreField: r1->field_13 = r0
    //     0x1486464: stur            w0, [x1, #0x13]
    // 0x1486468: r0 = Instance_MainAxisSize
    //     0x1486468: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x148646c: ldr             x0, [x0, #0xa10]
    // 0x1486470: ArrayStore: r1[0] = r0  ; List_4
    //     0x1486470: stur            w0, [x1, #0x17]
    // 0x1486474: r0 = Instance_CrossAxisAlignment
    //     0x1486474: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1486478: ldr             x0, [x0, #0xa18]
    // 0x148647c: StoreField: r1->field_1b = r0
    //     0x148647c: stur            w0, [x1, #0x1b]
    // 0x1486480: r0 = Instance_VerticalDirection
    //     0x1486480: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1486484: ldr             x0, [x0, #0xa20]
    // 0x1486488: StoreField: r1->field_23 = r0
    //     0x1486488: stur            w0, [x1, #0x23]
    // 0x148648c: r0 = Instance_Clip
    //     0x148648c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1486490: ldr             x0, [x0, #0x38]
    // 0x1486494: StoreField: r1->field_2b = r0
    //     0x1486494: stur            w0, [x1, #0x2b]
    // 0x1486498: StoreField: r1->field_2f = rZR
    //     0x1486498: stur            xzr, [x1, #0x2f]
    // 0x148649c: ldur            x0, [fp, #-0x10]
    // 0x14864a0: StoreField: r1->field_b = r0
    //     0x14864a0: stur            w0, [x1, #0xb]
    // 0x14864a4: r0 = SafeArea()
    //     0x14864a4: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14864a8: mov             x1, x0
    // 0x14864ac: r0 = true
    //     0x14864ac: add             x0, NULL, #0x20  ; true
    // 0x14864b0: stur            x1, [fp, #-0x10]
    // 0x14864b4: StoreField: r1->field_b = r0
    //     0x14864b4: stur            w0, [x1, #0xb]
    // 0x14864b8: StoreField: r1->field_f = r0
    //     0x14864b8: stur            w0, [x1, #0xf]
    // 0x14864bc: StoreField: r1->field_13 = r0
    //     0x14864bc: stur            w0, [x1, #0x13]
    // 0x14864c0: ArrayStore: r1[0] = r0  ; List_4
    //     0x14864c0: stur            w0, [x1, #0x17]
    // 0x14864c4: r0 = Instance_EdgeInsets
    //     0x14864c4: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14864c8: StoreField: r1->field_1b = r0
    //     0x14864c8: stur            w0, [x1, #0x1b]
    // 0x14864cc: r0 = false
    //     0x14864cc: add             x0, NULL, #0x30  ; false
    // 0x14864d0: StoreField: r1->field_1f = r0
    //     0x14864d0: stur            w0, [x1, #0x1f]
    // 0x14864d4: ldur            x0, [fp, #-0x18]
    // 0x14864d8: StoreField: r1->field_23 = r0
    //     0x14864d8: stur            w0, [x1, #0x23]
    // 0x14864dc: r0 = WillPopScope()
    //     0x14864dc: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14864e0: mov             x3, x0
    // 0x14864e4: ldur            x0, [fp, #-0x10]
    // 0x14864e8: stur            x3, [fp, #-0x18]
    // 0x14864ec: StoreField: r3->field_b = r0
    //     0x14864ec: stur            w0, [x3, #0xb]
    // 0x14864f0: ldur            x2, [fp, #-8]
    // 0x14864f4: r1 = Function 'getBack':.
    //     0x14864f4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44158] AnonymousClosure: (0x148652c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack (0x1486564)
    //     0x14864f8: ldr             x1, [x1, #0x158]
    // 0x14864fc: r0 = AllocateClosure()
    //     0x14864fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1486500: mov             x1, x0
    // 0x1486504: ldur            x0, [fp, #-0x18]
    // 0x1486508: StoreField: r0->field_f = r1
    //     0x1486508: stur            w1, [x0, #0xf]
    // 0x148650c: LeaveFrame
    //     0x148650c: mov             SP, fp
    //     0x1486510: ldp             fp, lr, [SP], #0x10
    // 0x1486514: ret
    //     0x1486514: ret             
    // 0x1486518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486518: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148651c: b               #0x1486328
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x148652c, size: 0x38
    // 0x148652c: EnterFrame
    //     0x148652c: stp             fp, lr, [SP, #-0x10]!
    //     0x1486530: mov             fp, SP
    // 0x1486534: ldr             x0, [fp, #0x10]
    // 0x1486538: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1486538: ldur            w1, [x0, #0x17]
    // 0x148653c: DecompressPointer r1
    //     0x148653c: add             x1, x1, HEAP, lsl #32
    // 0x1486540: CheckStackOverflow
    //     0x1486540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1486544: cmp             SP, x16
    //     0x1486548: b.ls            #0x148655c
    // 0x148654c: r0 = getBack()
    //     0x148654c: bl              #0x1486564  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack
    // 0x1486550: LeaveFrame
    //     0x1486550: mov             SP, fp
    //     0x1486554: ldp             fp, lr, [SP], #0x10
    // 0x1486558: ret
    //     0x1486558: ret             
    // 0x148655c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148655c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486560: b               #0x148654c
  }
  _ getBack(/* No info */) {
    // ** addr: 0x1486564, size: 0xb0
    // 0x1486564: EnterFrame
    //     0x1486564: stp             fp, lr, [SP, #-0x10]!
    //     0x1486568: mov             fp, SP
    // 0x148656c: AllocStack(0x8)
    //     0x148656c: sub             SP, SP, #8
    // 0x1486570: CheckStackOverflow
    //     0x1486570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1486574: cmp             SP, x16
    //     0x1486578: b.ls            #0x148660c
    // 0x148657c: r2 = false
    //     0x148657c: add             x2, NULL, #0x30  ; false
    // 0x1486580: r0 = showLoading()
    //     0x1486580: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x1486584: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1486584: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1486588: ldr             x0, [x0, #0x1c80]
    //     0x148658c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1486590: cmp             w0, w16
    //     0x1486594: b.ne            #0x14865a0
    //     0x1486598: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x148659c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14865a0: r1 = Function '<anonymous closure>':.
    //     0x14865a0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44160] AnonymousClosure: (0x1394804), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack (0x13949e8)
    //     0x14865a4: ldr             x1, [x1, #0x160]
    // 0x14865a8: r2 = Null
    //     0x14865a8: mov             x2, NULL
    // 0x14865ac: r0 = AllocateClosure()
    //     0x14865ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14865b0: mov             x1, x0
    // 0x14865b4: r0 = GetNavigation.until()
    //     0x14865b4: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x14865b8: r1 = <bool>
    //     0x14865b8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x14865bc: r0 = _Future()
    //     0x14865bc: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x14865c0: stur            x0, [fp, #-8]
    // 0x14865c4: StoreField: r0->field_b = rZR
    //     0x14865c4: stur            xzr, [x0, #0xb]
    // 0x14865c8: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x14865c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14865cc: ldr             x0, [x0, #0x778]
    //     0x14865d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14865d4: cmp             w0, w16
    //     0x14865d8: b.ne            #0x14865e4
    //     0x14865dc: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x14865e0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x14865e4: mov             x1, x0
    // 0x14865e8: ldur            x0, [fp, #-8]
    // 0x14865ec: StoreField: r0->field_13 = r1
    //     0x14865ec: stur            w1, [x0, #0x13]
    // 0x14865f0: mov             x1, x0
    // 0x14865f4: r2 = false
    //     0x14865f4: add             x2, NULL, #0x30  ; false
    // 0x14865f8: r0 = _asyncComplete()
    //     0x14865f8: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x14865fc: ldur            x0, [fp, #-8]
    // 0x1486600: LeaveFrame
    //     0x1486600: mov             SP, fp
    //     0x1486604: ldp             fp, lr, [SP], #0x10
    // 0x1486608: ret
    //     0x1486608: ret             
    // 0x148660c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148660c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486610: b               #0x148657c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1486614, size: 0x84
    // 0x1486614: EnterFrame
    //     0x1486614: stp             fp, lr, [SP, #-0x10]!
    //     0x1486618: mov             fp, SP
    // 0x148661c: AllocStack(0x10)
    //     0x148661c: sub             SP, SP, #0x10
    // 0x1486620: SetupParameters()
    //     0x1486620: ldr             x0, [fp, #0x10]
    //     0x1486624: ldur            w2, [x0, #0x17]
    //     0x1486628: add             x2, x2, HEAP, lsl #32
    //     0x148662c: stur            x2, [fp, #-0x10]
    // 0x1486630: CheckStackOverflow
    //     0x1486630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1486634: cmp             SP, x16
    //     0x1486638: b.ls            #0x1486690
    // 0x148663c: LoadField: r0 = r2->field_13
    //     0x148663c: ldur            w0, [x2, #0x13]
    // 0x1486640: DecompressPointer r0
    //     0x1486640: add             x0, x0, HEAP, lsl #32
    // 0x1486644: stur            x0, [fp, #-8]
    // 0x1486648: LoadField: r1 = r2->field_f
    //     0x1486648: ldur            w1, [x2, #0xf]
    // 0x148664c: DecompressPointer r1
    //     0x148664c: add             x1, x1, HEAP, lsl #32
    // 0x1486650: r0 = controller()
    //     0x1486650: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1486654: LoadField: r1 = r0->field_ab
    //     0x1486654: ldur            w1, [x0, #0xab]
    // 0x1486658: DecompressPointer r1
    //     0x1486658: add             x1, x1, HEAP, lsl #32
    // 0x148665c: r0 = value()
    //     0x148665c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1486660: mov             x1, x0
    // 0x1486664: ldur            x0, [fp, #-0x10]
    // 0x1486668: LoadField: r2 = r0->field_f
    //     0x1486668: ldur            w2, [x0, #0xf]
    // 0x148666c: DecompressPointer r2
    //     0x148666c: add             x2, x2, HEAP, lsl #32
    // 0x1486670: mov             x3, x1
    // 0x1486674: mov             x1, x2
    // 0x1486678: ldur            x2, [fp, #-8]
    // 0x148667c: r0 = _showEditPhoneBottomSheet()
    //     0x148667c: bl              #0x1486698  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet
    // 0x1486680: r0 = Null
    //     0x1486680: mov             x0, NULL
    // 0x1486684: LeaveFrame
    //     0x1486684: mov             SP, fp
    //     0x1486688: ldp             fp, lr, [SP], #0x10
    // 0x148668c: ret
    //     0x148668c: ret             
    // 0x1486690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486690: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486694: b               #0x148663c
  }
  _ _showEditPhoneBottomSheet(/* No info */) {
    // ** addr: 0x1486698, size: 0x98
    // 0x1486698: EnterFrame
    //     0x1486698: stp             fp, lr, [SP, #-0x10]!
    //     0x148669c: mov             fp, SP
    // 0x14866a0: AllocStack(0x48)
    //     0x14866a0: sub             SP, SP, #0x48
    // 0x14866a4: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x14866a4: stur            x1, [fp, #-8]
    //     0x14866a8: stur            x2, [fp, #-0x10]
    //     0x14866ac: stur            x3, [fp, #-0x18]
    // 0x14866b0: CheckStackOverflow
    //     0x14866b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14866b4: cmp             SP, x16
    //     0x14866b8: b.ls            #0x1486728
    // 0x14866bc: r1 = 2
    //     0x14866bc: movz            x1, #0x2
    // 0x14866c0: r0 = AllocateContext()
    //     0x14866c0: bl              #0x16f6108  ; AllocateContextStub
    // 0x14866c4: mov             x1, x0
    // 0x14866c8: ldur            x0, [fp, #-8]
    // 0x14866cc: StoreField: r1->field_f = r0
    //     0x14866cc: stur            w0, [x1, #0xf]
    // 0x14866d0: ldur            x0, [fp, #-0x18]
    // 0x14866d4: StoreField: r1->field_13 = r0
    //     0x14866d4: stur            w0, [x1, #0x13]
    // 0x14866d8: mov             x2, x1
    // 0x14866dc: r1 = Function '<anonymous closure>':.
    //     0x14866dc: add             x1, PP, #0x44, lsl #12  ; [pp+0x44168] AnonymousClosure: (0x1486730), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet (0x1486698)
    //     0x14866e0: ldr             x1, [x1, #0x168]
    // 0x14866e4: r0 = AllocateClosure()
    //     0x14866e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14866e8: stp             x0, NULL, [SP, #0x20]
    // 0x14866ec: ldur            x16, [fp, #-0x10]
    // 0x14866f0: r30 = true
    //     0x14866f0: add             lr, NULL, #0x20  ; true
    // 0x14866f4: stp             lr, x16, [SP, #0x10]
    // 0x14866f8: r16 = Instance_Color
    //     0x14866f8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14866fc: ldr             x16, [x16, #0x90]
    // 0x1486700: r30 = Instance_RoundedRectangleBorder
    //     0x1486700: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1486704: ldr             lr, [lr, #0xc78]
    // 0x1486708: stp             lr, x16, [SP]
    // 0x148670c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, shape, 0x4, null]
    //     0x148670c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d308] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, "shape", 0x4, Null]
    //     0x1486710: ldr             x4, [x4, #0x308]
    // 0x1486714: r0 = showModalBottomSheet()
    //     0x1486714: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1486718: r0 = Null
    //     0x1486718: mov             x0, NULL
    // 0x148671c: LeaveFrame
    //     0x148671c: mov             SP, fp
    //     0x1486720: ldp             fp, lr, [SP], #0x10
    // 0x1486724: ret
    //     0x1486724: ret             
    // 0x1486728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486728: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148672c: b               #0x14866bc
  }
  [closure] AnimatedPadding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1486730, size: 0xe0
    // 0x1486730: EnterFrame
    //     0x1486730: stp             fp, lr, [SP, #-0x10]!
    //     0x1486734: mov             fp, SP
    // 0x1486738: AllocStack(0x28)
    //     0x1486738: sub             SP, SP, #0x28
    // 0x148673c: SetupParameters()
    //     0x148673c: ldr             x0, [fp, #0x18]
    //     0x1486740: ldur            w2, [x0, #0x17]
    //     0x1486744: add             x2, x2, HEAP, lsl #32
    //     0x1486748: stur            x2, [fp, #-8]
    // 0x148674c: CheckStackOverflow
    //     0x148674c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1486750: cmp             SP, x16
    //     0x1486754: b.ls            #0x1486808
    // 0x1486758: ldr             x1, [fp, #0x10]
    // 0x148675c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x148675c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1486760: r0 = _of()
    //     0x1486760: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1486764: LoadField: r1 = r0->field_23
    //     0x1486764: ldur            w1, [x0, #0x23]
    // 0x1486768: DecompressPointer r1
    //     0x1486768: add             x1, x1, HEAP, lsl #32
    // 0x148676c: LoadField: d0 = r1->field_1f
    //     0x148676c: ldur            d0, [x1, #0x1f]
    // 0x1486770: stur            d0, [fp, #-0x28]
    // 0x1486774: r0 = EdgeInsets()
    //     0x1486774: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1486778: stur            x0, [fp, #-0x18]
    // 0x148677c: StoreField: r0->field_7 = rZR
    //     0x148677c: stur            xzr, [x0, #7]
    // 0x1486780: StoreField: r0->field_f = rZR
    //     0x1486780: stur            xzr, [x0, #0xf]
    // 0x1486784: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1486784: stur            xzr, [x0, #0x17]
    // 0x1486788: ldur            d0, [fp, #-0x28]
    // 0x148678c: StoreField: r0->field_1f = d0
    //     0x148678c: stur            d0, [x0, #0x1f]
    // 0x1486790: ldur            x2, [fp, #-8]
    // 0x1486794: LoadField: r1 = r2->field_13
    //     0x1486794: ldur            w1, [x2, #0x13]
    // 0x1486798: DecompressPointer r1
    //     0x1486798: add             x1, x1, HEAP, lsl #32
    // 0x148679c: stur            x1, [fp, #-0x10]
    // 0x14867a0: r0 = EditPhoneBottomSheet()
    //     0x14867a0: bl              #0x1486810  ; AllocateEditPhoneBottomSheetStub -> EditPhoneBottomSheet (size=0x14)
    // 0x14867a4: mov             x3, x0
    // 0x14867a8: ldur            x0, [fp, #-0x10]
    // 0x14867ac: stur            x3, [fp, #-0x20]
    // 0x14867b0: StoreField: r3->field_b = r0
    //     0x14867b0: stur            w0, [x3, #0xb]
    // 0x14867b4: ldur            x2, [fp, #-8]
    // 0x14867b8: r1 = Function '<anonymous closure>':.
    //     0x14867b8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44170] AnonymousClosure: (0x1394cd0), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet (0x1394ed4)
    //     0x14867bc: ldr             x1, [x1, #0x170]
    // 0x14867c0: r0 = AllocateClosure()
    //     0x14867c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14867c4: mov             x1, x0
    // 0x14867c8: ldur            x0, [fp, #-0x20]
    // 0x14867cc: StoreField: r0->field_f = r1
    //     0x14867cc: stur            w1, [x0, #0xf]
    // 0x14867d0: r0 = AnimatedPadding()
    //     0x14867d0: bl              #0x12a22b8  ; AllocateAnimatedPaddingStub -> AnimatedPadding (size=0x20)
    // 0x14867d4: ldur            x1, [fp, #-0x18]
    // 0x14867d8: ArrayStore: r0[0] = r1  ; List_4
    //     0x14867d8: stur            w1, [x0, #0x17]
    // 0x14867dc: ldur            x1, [fp, #-0x20]
    // 0x14867e0: StoreField: r0->field_1b = r1
    //     0x14867e0: stur            w1, [x0, #0x1b]
    // 0x14867e4: r1 = Instance_Cubic
    //     0x14867e4: add             x1, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0x14867e8: ldr             x1, [x1, #0x2b0]
    // 0x14867ec: StoreField: r0->field_b = r1
    //     0x14867ec: stur            w1, [x0, #0xb]
    // 0x14867f0: r1 = Instance_Duration
    //     0x14867f0: add             x1, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x14867f4: ldr             x1, [x1, #0xf00]
    // 0x14867f8: StoreField: r0->field_f = r1
    //     0x14867f8: stur            w1, [x0, #0xf]
    // 0x14867fc: LeaveFrame
    //     0x14867fc: mov             SP, fp
    //     0x1486800: ldp             fp, lr, [SP], #0x10
    // 0x1486804: ret
    //     0x1486804: ret             
    // 0x1486808: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1486808: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148680c: b               #0x1486758
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x148681c, size: 0x68
    // 0x148681c: EnterFrame
    //     0x148681c: stp             fp, lr, [SP, #-0x10]!
    //     0x1486820: mov             fp, SP
    // 0x1486824: AllocStack(0x8)
    //     0x1486824: sub             SP, SP, #8
    // 0x1486828: SetupParameters()
    //     0x1486828: ldr             x0, [fp, #0x10]
    //     0x148682c: ldur            w1, [x0, #0x17]
    //     0x1486830: add             x1, x1, HEAP, lsl #32
    // 0x1486834: CheckStackOverflow
    //     0x1486834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1486838: cmp             SP, x16
    //     0x148683c: b.ls            #0x148687c
    // 0x1486840: LoadField: r0 = r1->field_f
    //     0x1486840: ldur            w0, [x1, #0xf]
    // 0x1486844: DecompressPointer r0
    //     0x1486844: add             x0, x0, HEAP, lsl #32
    // 0x1486848: mov             x1, x0
    // 0x148684c: r0 = controller()
    //     0x148684c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1486850: LoadField: r1 = r0->field_97
    //     0x1486850: ldur            w1, [x0, #0x97]
    // 0x1486854: DecompressPointer r1
    //     0x1486854: add             x1, x1, HEAP, lsl #32
    // 0x1486858: r0 = value()
    //     0x1486858: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148685c: stur            x0, [fp, #-8]
    // 0x1486860: r0 = CheckoutBreadCrumb()
    //     0x1486860: bl              #0x1484fc8  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x1486864: ldur            x1, [fp, #-8]
    // 0x1486868: StoreField: r0->field_b = r1
    //     0x1486868: stur            w1, [x0, #0xb]
    // 0x148686c: StoreField: r0->field_f = rZR
    //     0x148686c: stur            xzr, [x0, #0xf]
    // 0x1486870: LeaveFrame
    //     0x1486870: mov             SP, fp
    //     0x1486874: ldp             fp, lr, [SP], #0x10
    // 0x1486878: ret
    //     0x1486878: ret             
    // 0x148687c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x148687c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1486880: b               #0x1486840
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d98b8, size: 0x250
    // 0x15d98b8: EnterFrame
    //     0x15d98b8: stp             fp, lr, [SP, #-0x10]!
    //     0x15d98bc: mov             fp, SP
    // 0x15d98c0: AllocStack(0x28)
    //     0x15d98c0: sub             SP, SP, #0x28
    // 0x15d98c4: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15d98c4: stur            x1, [fp, #-8]
    //     0x15d98c8: stur            x2, [fp, #-0x10]
    // 0x15d98cc: CheckStackOverflow
    //     0x15d98cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d98d0: cmp             SP, x16
    //     0x15d98d4: b.ls            #0x15d9b00
    // 0x15d98d8: r1 = 2
    //     0x15d98d8: movz            x1, #0x2
    // 0x15d98dc: r0 = AllocateContext()
    //     0x15d98dc: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d98e0: ldur            x1, [fp, #-8]
    // 0x15d98e4: stur            x0, [fp, #-0x18]
    // 0x15d98e8: StoreField: r0->field_f = r1
    //     0x15d98e8: stur            w1, [x0, #0xf]
    // 0x15d98ec: ldur            x2, [fp, #-0x10]
    // 0x15d98f0: StoreField: r0->field_13 = r2
    //     0x15d98f0: stur            w2, [x0, #0x13]
    // 0x15d98f4: r0 = Obx()
    //     0x15d98f4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d98f8: ldur            x2, [fp, #-0x18]
    // 0x15d98fc: r1 = Function '<anonymous closure>':.
    //     0x15d98fc: add             x1, PP, #0x44, lsl #12  ; [pp+0x44178] AnonymousClosure: (0x15cd5f8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::appBar (0x15e8698)
    //     0x15d9900: ldr             x1, [x1, #0x178]
    // 0x15d9904: stur            x0, [fp, #-0x10]
    // 0x15d9908: r0 = AllocateClosure()
    //     0x15d9908: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d990c: mov             x1, x0
    // 0x15d9910: ldur            x0, [fp, #-0x10]
    // 0x15d9914: StoreField: r0->field_b = r1
    //     0x15d9914: stur            w1, [x0, #0xb]
    // 0x15d9918: ldur            x1, [fp, #-8]
    // 0x15d991c: r0 = controller()
    //     0x15d991c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d9920: LoadField: r1 = r0->field_a3
    //     0x15d9920: ldur            w1, [x0, #0xa3]
    // 0x15d9924: DecompressPointer r1
    //     0x15d9924: add             x1, x1, HEAP, lsl #32
    // 0x15d9928: r0 = value()
    //     0x15d9928: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d992c: tbnz            w0, #4, #0x15d99c4
    // 0x15d9930: ldur            x2, [fp, #-0x18]
    // 0x15d9934: LoadField: r1 = r2->field_13
    //     0x15d9934: ldur            w1, [x2, #0x13]
    // 0x15d9938: DecompressPointer r1
    //     0x15d9938: add             x1, x1, HEAP, lsl #32
    // 0x15d993c: r0 = of()
    //     0x15d993c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d9940: LoadField: r1 = r0->field_5b
    //     0x15d9940: ldur            w1, [x0, #0x5b]
    // 0x15d9944: DecompressPointer r1
    //     0x15d9944: add             x1, x1, HEAP, lsl #32
    // 0x15d9948: stur            x1, [fp, #-8]
    // 0x15d994c: r0 = ColorFilter()
    //     0x15d994c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d9950: mov             x1, x0
    // 0x15d9954: ldur            x0, [fp, #-8]
    // 0x15d9958: stur            x1, [fp, #-0x20]
    // 0x15d995c: StoreField: r1->field_7 = r0
    //     0x15d995c: stur            w0, [x1, #7]
    // 0x15d9960: r0 = Instance_BlendMode
    //     0x15d9960: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d9964: ldr             x0, [x0, #0xb30]
    // 0x15d9968: StoreField: r1->field_b = r0
    //     0x15d9968: stur            w0, [x1, #0xb]
    // 0x15d996c: r2 = 1
    //     0x15d996c: movz            x2, #0x1
    // 0x15d9970: StoreField: r1->field_13 = r2
    //     0x15d9970: stur            x2, [x1, #0x13]
    // 0x15d9974: r0 = SvgPicture()
    //     0x15d9974: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d9978: stur            x0, [fp, #-8]
    // 0x15d997c: ldur            x16, [fp, #-0x20]
    // 0x15d9980: str             x16, [SP]
    // 0x15d9984: mov             x1, x0
    // 0x15d9988: r2 = "assets/images/search.svg"
    //     0x15d9988: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15d998c: ldr             x2, [x2, #0xa30]
    // 0x15d9990: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d9990: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d9994: ldr             x4, [x4, #0xa38]
    // 0x15d9998: r0 = SvgPicture.asset()
    //     0x15d9998: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d999c: r0 = Align()
    //     0x15d999c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d99a0: r3 = Instance_Alignment
    //     0x15d99a0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d99a4: ldr             x3, [x3, #0xb10]
    // 0x15d99a8: StoreField: r0->field_f = r3
    //     0x15d99a8: stur            w3, [x0, #0xf]
    // 0x15d99ac: r4 = 1.000000
    //     0x15d99ac: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d99b0: StoreField: r0->field_13 = r4
    //     0x15d99b0: stur            w4, [x0, #0x13]
    // 0x15d99b4: ArrayStore: r0[0] = r4  ; List_4
    //     0x15d99b4: stur            w4, [x0, #0x17]
    // 0x15d99b8: ldur            x1, [fp, #-8]
    // 0x15d99bc: StoreField: r0->field_b = r1
    //     0x15d99bc: stur            w1, [x0, #0xb]
    // 0x15d99c0: b               #0x15d9a74
    // 0x15d99c4: ldur            x5, [fp, #-0x18]
    // 0x15d99c8: r4 = 1.000000
    //     0x15d99c8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d99cc: r0 = Instance_BlendMode
    //     0x15d99cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d99d0: ldr             x0, [x0, #0xb30]
    // 0x15d99d4: r3 = Instance_Alignment
    //     0x15d99d4: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d99d8: ldr             x3, [x3, #0xb10]
    // 0x15d99dc: r2 = 1
    //     0x15d99dc: movz            x2, #0x1
    // 0x15d99e0: LoadField: r1 = r5->field_13
    //     0x15d99e0: ldur            w1, [x5, #0x13]
    // 0x15d99e4: DecompressPointer r1
    //     0x15d99e4: add             x1, x1, HEAP, lsl #32
    // 0x15d99e8: r0 = of()
    //     0x15d99e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d99ec: LoadField: r1 = r0->field_5b
    //     0x15d99ec: ldur            w1, [x0, #0x5b]
    // 0x15d99f0: DecompressPointer r1
    //     0x15d99f0: add             x1, x1, HEAP, lsl #32
    // 0x15d99f4: stur            x1, [fp, #-8]
    // 0x15d99f8: r0 = ColorFilter()
    //     0x15d99f8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d99fc: mov             x1, x0
    // 0x15d9a00: ldur            x0, [fp, #-8]
    // 0x15d9a04: stur            x1, [fp, #-0x20]
    // 0x15d9a08: StoreField: r1->field_7 = r0
    //     0x15d9a08: stur            w0, [x1, #7]
    // 0x15d9a0c: r0 = Instance_BlendMode
    //     0x15d9a0c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d9a10: ldr             x0, [x0, #0xb30]
    // 0x15d9a14: StoreField: r1->field_b = r0
    //     0x15d9a14: stur            w0, [x1, #0xb]
    // 0x15d9a18: r0 = 1
    //     0x15d9a18: movz            x0, #0x1
    // 0x15d9a1c: StoreField: r1->field_13 = r0
    //     0x15d9a1c: stur            x0, [x1, #0x13]
    // 0x15d9a20: r0 = SvgPicture()
    //     0x15d9a20: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d9a24: stur            x0, [fp, #-8]
    // 0x15d9a28: ldur            x16, [fp, #-0x20]
    // 0x15d9a2c: str             x16, [SP]
    // 0x15d9a30: mov             x1, x0
    // 0x15d9a34: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d9a34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d9a38: ldr             x2, [x2, #0xa40]
    // 0x15d9a3c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d9a3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d9a40: ldr             x4, [x4, #0xa38]
    // 0x15d9a44: r0 = SvgPicture.asset()
    //     0x15d9a44: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d9a48: r0 = Align()
    //     0x15d9a48: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d9a4c: mov             x1, x0
    // 0x15d9a50: r0 = Instance_Alignment
    //     0x15d9a50: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d9a54: ldr             x0, [x0, #0xb10]
    // 0x15d9a58: StoreField: r1->field_f = r0
    //     0x15d9a58: stur            w0, [x1, #0xf]
    // 0x15d9a5c: r0 = 1.000000
    //     0x15d9a5c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9a60: StoreField: r1->field_13 = r0
    //     0x15d9a60: stur            w0, [x1, #0x13]
    // 0x15d9a64: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d9a64: stur            w0, [x1, #0x17]
    // 0x15d9a68: ldur            x0, [fp, #-8]
    // 0x15d9a6c: StoreField: r1->field_b = r0
    //     0x15d9a6c: stur            w0, [x1, #0xb]
    // 0x15d9a70: mov             x0, x1
    // 0x15d9a74: stur            x0, [fp, #-8]
    // 0x15d9a78: r0 = InkWell()
    //     0x15d9a78: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d9a7c: mov             x3, x0
    // 0x15d9a80: ldur            x0, [fp, #-8]
    // 0x15d9a84: stur            x3, [fp, #-0x20]
    // 0x15d9a88: StoreField: r3->field_b = r0
    //     0x15d9a88: stur            w0, [x3, #0xb]
    // 0x15d9a8c: ldur            x2, [fp, #-0x18]
    // 0x15d9a90: r1 = Function '<anonymous closure>':.
    //     0x15d9a90: add             x1, PP, #0x44, lsl #12  ; [pp+0x44180] AnonymousClosure: (0x15d9b08), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::appBar (0x15d98b8)
    //     0x15d9a94: ldr             x1, [x1, #0x180]
    // 0x15d9a98: r0 = AllocateClosure()
    //     0x15d9a98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d9a9c: ldur            x2, [fp, #-0x20]
    // 0x15d9aa0: StoreField: r2->field_f = r0
    //     0x15d9aa0: stur            w0, [x2, #0xf]
    // 0x15d9aa4: r0 = true
    //     0x15d9aa4: add             x0, NULL, #0x20  ; true
    // 0x15d9aa8: StoreField: r2->field_43 = r0
    //     0x15d9aa8: stur            w0, [x2, #0x43]
    // 0x15d9aac: r1 = Instance_BoxShape
    //     0x15d9aac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d9ab0: ldr             x1, [x1, #0x80]
    // 0x15d9ab4: StoreField: r2->field_47 = r1
    //     0x15d9ab4: stur            w1, [x2, #0x47]
    // 0x15d9ab8: StoreField: r2->field_6f = r0
    //     0x15d9ab8: stur            w0, [x2, #0x6f]
    // 0x15d9abc: r1 = false
    //     0x15d9abc: add             x1, NULL, #0x30  ; false
    // 0x15d9ac0: StoreField: r2->field_73 = r1
    //     0x15d9ac0: stur            w1, [x2, #0x73]
    // 0x15d9ac4: StoreField: r2->field_83 = r0
    //     0x15d9ac4: stur            w0, [x2, #0x83]
    // 0x15d9ac8: StoreField: r2->field_7b = r1
    //     0x15d9ac8: stur            w1, [x2, #0x7b]
    // 0x15d9acc: r0 = AppBar()
    //     0x15d9acc: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d9ad0: stur            x0, [fp, #-8]
    // 0x15d9ad4: ldur            x16, [fp, #-0x10]
    // 0x15d9ad8: str             x16, [SP]
    // 0x15d9adc: mov             x1, x0
    // 0x15d9ae0: ldur            x2, [fp, #-0x20]
    // 0x15d9ae4: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15d9ae4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15d9ae8: ldr             x4, [x4, #0xf00]
    // 0x15d9aec: r0 = AppBar()
    //     0x15d9aec: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d9af0: ldur            x0, [fp, #-8]
    // 0x15d9af4: LeaveFrame
    //     0x15d9af4: mov             SP, fp
    //     0x15d9af8: ldp             fp, lr, [SP], #0x10
    // 0x15d9afc: ret
    //     0x15d9afc: ret             
    // 0x15d9b00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d9b00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d9b04: b               #0x15d98d8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d9b08, size: 0xc8
    // 0x15d9b08: EnterFrame
    //     0x15d9b08: stp             fp, lr, [SP, #-0x10]!
    //     0x15d9b0c: mov             fp, SP
    // 0x15d9b10: AllocStack(0x18)
    //     0x15d9b10: sub             SP, SP, #0x18
    // 0x15d9b14: SetupParameters()
    //     0x15d9b14: ldr             x0, [fp, #0x10]
    //     0x15d9b18: ldur            w3, [x0, #0x17]
    //     0x15d9b1c: add             x3, x3, HEAP, lsl #32
    //     0x15d9b20: stur            x3, [fp, #-8]
    // 0x15d9b24: CheckStackOverflow
    //     0x15d9b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d9b28: cmp             SP, x16
    //     0x15d9b2c: b.ls            #0x15d9bc8
    // 0x15d9b30: LoadField: r1 = r3->field_f
    //     0x15d9b30: ldur            w1, [x3, #0xf]
    // 0x15d9b34: DecompressPointer r1
    //     0x15d9b34: add             x1, x1, HEAP, lsl #32
    // 0x15d9b38: r2 = false
    //     0x15d9b38: add             x2, NULL, #0x30  ; false
    // 0x15d9b3c: r0 = showLoading()
    //     0x15d9b3c: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d9b40: ldur            x0, [fp, #-8]
    // 0x15d9b44: LoadField: r1 = r0->field_f
    //     0x15d9b44: ldur            w1, [x0, #0xf]
    // 0x15d9b48: DecompressPointer r1
    //     0x15d9b48: add             x1, x1, HEAP, lsl #32
    // 0x15d9b4c: r0 = controller()
    //     0x15d9b4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d9b50: LoadField: r1 = r0->field_a3
    //     0x15d9b50: ldur            w1, [x0, #0xa3]
    // 0x15d9b54: DecompressPointer r1
    //     0x15d9b54: add             x1, x1, HEAP, lsl #32
    // 0x15d9b58: r0 = value()
    //     0x15d9b58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d9b5c: tbnz            w0, #4, #0x15d9b94
    // 0x15d9b60: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d9b60: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d9b64: ldr             x0, [x0, #0x1c80]
    //     0x15d9b68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d9b6c: cmp             w0, w16
    //     0x15d9b70: b.ne            #0x15d9b7c
    //     0x15d9b74: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d9b78: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d9b7c: r16 = "/search"
    //     0x15d9b7c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15d9b80: ldr             x16, [x16, #0x838]
    // 0x15d9b84: stp             x16, NULL, [SP]
    // 0x15d9b88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15d9b88: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15d9b8c: r0 = GetNavigation.toNamed()
    //     0x15d9b8c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d9b90: b               #0x15d9bb8
    // 0x15d9b94: ldur            x0, [fp, #-8]
    // 0x15d9b98: LoadField: r1 = r0->field_f
    //     0x15d9b98: ldur            w1, [x0, #0xf]
    // 0x15d9b9c: DecompressPointer r1
    //     0x15d9b9c: add             x1, x1, HEAP, lsl #32
    // 0x15d9ba0: r2 = false
    //     0x15d9ba0: add             x2, NULL, #0x30  ; false
    // 0x15d9ba4: r0 = showLoading()
    //     0x15d9ba4: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d9ba8: ldur            x0, [fp, #-8]
    // 0x15d9bac: LoadField: r1 = r0->field_f
    //     0x15d9bac: ldur            w1, [x0, #0xf]
    // 0x15d9bb0: DecompressPointer r1
    //     0x15d9bb0: add             x1, x1, HEAP, lsl #32
    // 0x15d9bb4: r0 = getBack()
    //     0x15d9bb4: bl              #0x1486564  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack
    // 0x15d9bb8: r0 = Null
    //     0x15d9bb8: mov             x0, NULL
    // 0x15d9bbc: LeaveFrame
    //     0x15d9bbc: mov             SP, fp
    //     0x15d9bc0: ldp             fp, lr, [SP], #0x10
    // 0x15d9bc4: ret
    //     0x15d9bc4: ret             
    // 0x15d9bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d9bc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d9bcc: b               #0x15d9b30
  }
}
