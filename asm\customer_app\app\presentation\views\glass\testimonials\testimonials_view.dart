// lib: , url: package:customer_app/app/presentation/views/glass/testimonials/testimonials_view.dart

// class id: 1049460, size: 0x8
class :: {
}

// class id: 4549, size: 0x14, field offset: 0x14
//   const constructor, 
class TestimonialsView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14fd3f4, size: 0x10c
    // 0x14fd3f4: EnterFrame
    //     0x14fd3f4: stp             fp, lr, [SP, #-0x10]!
    //     0x14fd3f8: mov             fp, SP
    // 0x14fd3fc: AllocStack(0x18)
    //     0x14fd3fc: sub             SP, SP, #0x18
    // 0x14fd400: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14fd400: stur            x1, [fp, #-8]
    //     0x14fd404: stur            x2, [fp, #-0x10]
    // 0x14fd408: CheckStackOverflow
    //     0x14fd408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fd40c: cmp             SP, x16
    //     0x14fd410: b.ls            #0x14fd4f8
    // 0x14fd414: r1 = 2
    //     0x14fd414: movz            x1, #0x2
    // 0x14fd418: r0 = AllocateContext()
    //     0x14fd418: bl              #0x16f6108  ; AllocateContextStub
    // 0x14fd41c: mov             x1, x0
    // 0x14fd420: ldur            x0, [fp, #-8]
    // 0x14fd424: stur            x1, [fp, #-0x18]
    // 0x14fd428: StoreField: r1->field_f = r0
    //     0x14fd428: stur            w0, [x1, #0xf]
    // 0x14fd42c: ldur            x0, [fp, #-0x10]
    // 0x14fd430: StoreField: r1->field_13 = r0
    //     0x14fd430: stur            w0, [x1, #0x13]
    // 0x14fd434: r0 = Obx()
    //     0x14fd434: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14fd438: ldur            x2, [fp, #-0x18]
    // 0x14fd43c: r1 = Function '<anonymous closure>':.
    //     0x14fd43c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e848] AnonymousClosure: (0x14fd500), in [package:customer_app/app/presentation/views/glass/testimonials/testimonials_view.dart] TestimonialsView::body (0x14fd3f4)
    //     0x14fd440: ldr             x1, [x1, #0x848]
    // 0x14fd444: stur            x0, [fp, #-8]
    // 0x14fd448: r0 = AllocateClosure()
    //     0x14fd448: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd44c: mov             x1, x0
    // 0x14fd450: ldur            x0, [fp, #-8]
    // 0x14fd454: StoreField: r0->field_b = r1
    //     0x14fd454: stur            w1, [x0, #0xb]
    // 0x14fd458: r0 = Padding()
    //     0x14fd458: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14fd45c: mov             x3, x0
    // 0x14fd460: r0 = Instance_EdgeInsets
    //     0x14fd460: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x14fd464: ldr             x0, [x0, #0x980]
    // 0x14fd468: stur            x3, [fp, #-0x10]
    // 0x14fd46c: StoreField: r3->field_f = r0
    //     0x14fd46c: stur            w0, [x3, #0xf]
    // 0x14fd470: ldur            x0, [fp, #-8]
    // 0x14fd474: StoreField: r3->field_b = r0
    //     0x14fd474: stur            w0, [x3, #0xb]
    // 0x14fd478: ldur            x2, [fp, #-0x18]
    // 0x14fd47c: r1 = Function '<anonymous closure>':.
    //     0x14fd47c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e850] AnonymousClosure: (0x147bdfc), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x14fd480: ldr             x1, [x1, #0x850]
    // 0x14fd484: r0 = AllocateClosure()
    //     0x14fd484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd488: ldur            x2, [fp, #-0x18]
    // 0x14fd48c: r1 = Function '<anonymous closure>':.
    //     0x14fd48c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e858] AnonymousClosure: (0x147a04c), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x14fd490: ldr             x1, [x1, #0x858]
    // 0x14fd494: stur            x0, [fp, #-8]
    // 0x14fd498: r0 = AllocateClosure()
    //     0x14fd498: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd49c: stur            x0, [fp, #-0x18]
    // 0x14fd4a0: r0 = PagingView()
    //     0x14fd4a0: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14fd4a4: mov             x1, x0
    // 0x14fd4a8: ldur            x2, [fp, #-0x10]
    // 0x14fd4ac: ldur            x3, [fp, #-0x18]
    // 0x14fd4b0: ldur            x5, [fp, #-8]
    // 0x14fd4b4: stur            x0, [fp, #-8]
    // 0x14fd4b8: r0 = PagingView()
    //     0x14fd4b8: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14fd4bc: r0 = WillPopScope()
    //     0x14fd4bc: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14fd4c0: mov             x3, x0
    // 0x14fd4c4: ldur            x0, [fp, #-8]
    // 0x14fd4c8: stur            x3, [fp, #-0x10]
    // 0x14fd4cc: StoreField: r3->field_b = r0
    //     0x14fd4cc: stur            w0, [x3, #0xb]
    // 0x14fd4d0: r1 = Function '<anonymous closure>':.
    //     0x14fd4d0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e860] AnonymousClosure: (0x1479fd8), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::body (0x15096cc)
    //     0x14fd4d4: ldr             x1, [x1, #0x860]
    // 0x14fd4d8: r2 = Null
    //     0x14fd4d8: mov             x2, NULL
    // 0x14fd4dc: r0 = AllocateClosure()
    //     0x14fd4dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14fd4e0: mov             x1, x0
    // 0x14fd4e4: ldur            x0, [fp, #-0x10]
    // 0x14fd4e8: StoreField: r0->field_f = r1
    //     0x14fd4e8: stur            w1, [x0, #0xf]
    // 0x14fd4ec: LeaveFrame
    //     0x14fd4ec: mov             SP, fp
    //     0x14fd4f0: ldp             fp, lr, [SP], #0x10
    // 0x14fd4f4: ret
    //     0x14fd4f4: ret             
    // 0x14fd4f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fd4f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fd4fc: b               #0x14fd414
  }
  [closure] RenderObjectWidget <anonymous closure>(dynamic) {
    // ** addr: 0x14fd500, size: 0x5f8
    // 0x14fd500: EnterFrame
    //     0x14fd500: stp             fp, lr, [SP, #-0x10]!
    //     0x14fd504: mov             fp, SP
    // 0x14fd508: AllocStack(0x50)
    //     0x14fd508: sub             SP, SP, #0x50
    // 0x14fd50c: SetupParameters()
    //     0x14fd50c: ldr             x0, [fp, #0x10]
    //     0x14fd510: ldur            w2, [x0, #0x17]
    //     0x14fd514: add             x2, x2, HEAP, lsl #32
    //     0x14fd518: stur            x2, [fp, #-8]
    // 0x14fd51c: CheckStackOverflow
    //     0x14fd51c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14fd520: cmp             SP, x16
    //     0x14fd524: b.ls            #0x14fdaf0
    // 0x14fd528: LoadField: r1 = r2->field_f
    //     0x14fd528: ldur            w1, [x2, #0xf]
    // 0x14fd52c: DecompressPointer r1
    //     0x14fd52c: add             x1, x1, HEAP, lsl #32
    // 0x14fd530: r0 = controller()
    //     0x14fd530: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd534: LoadField: r1 = r0->field_53
    //     0x14fd534: ldur            w1, [x0, #0x53]
    // 0x14fd538: DecompressPointer r1
    //     0x14fd538: add             x1, x1, HEAP, lsl #32
    // 0x14fd53c: r0 = value()
    //     0x14fd53c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fd540: LoadField: r1 = r0->field_b
    //     0x14fd540: ldur            w1, [x0, #0xb]
    // 0x14fd544: DecompressPointer r1
    //     0x14fd544: add             x1, x1, HEAP, lsl #32
    // 0x14fd548: cmp             w1, NULL
    // 0x14fd54c: b.ne            #0x14fd55c
    // 0x14fd550: r0 = Instance_Center
    //     0x14fd550: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9a8] Obj!Center@d68361
    //     0x14fd554: ldr             x0, [x0, #0x9a8]
    // 0x14fd558: b               #0x14fdae4
    // 0x14fd55c: ldur            x0, [fp, #-8]
    // 0x14fd560: LoadField: r1 = r0->field_13
    //     0x14fd560: ldur            w1, [x0, #0x13]
    // 0x14fd564: DecompressPointer r1
    //     0x14fd564: add             x1, x1, HEAP, lsl #32
    // 0x14fd568: r0 = of()
    //     0x14fd568: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fd56c: LoadField: r1 = r0->field_87
    //     0x14fd56c: ldur            w1, [x0, #0x87]
    // 0x14fd570: DecompressPointer r1
    //     0x14fd570: add             x1, x1, HEAP, lsl #32
    // 0x14fd574: LoadField: r0 = r1->field_7
    //     0x14fd574: ldur            w0, [x1, #7]
    // 0x14fd578: DecompressPointer r0
    //     0x14fd578: add             x0, x0, HEAP, lsl #32
    // 0x14fd57c: r16 = Instance_Color
    //     0x14fd57c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fd580: r30 = 21.000000
    //     0x14fd580: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x14fd584: ldr             lr, [lr, #0x9b0]
    // 0x14fd588: stp             lr, x16, [SP]
    // 0x14fd58c: mov             x1, x0
    // 0x14fd590: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14fd590: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14fd594: ldr             x4, [x4, #0x9b8]
    // 0x14fd598: r0 = copyWith()
    //     0x14fd598: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fd59c: stur            x0, [fp, #-0x10]
    // 0x14fd5a0: r0 = Text()
    //     0x14fd5a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14fd5a4: mov             x2, x0
    // 0x14fd5a8: r0 = "Customers Love Us"
    //     0x14fd5a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c0] "Customers Love Us"
    //     0x14fd5ac: ldr             x0, [x0, #0x9c0]
    // 0x14fd5b0: stur            x2, [fp, #-0x18]
    // 0x14fd5b4: StoreField: r2->field_b = r0
    //     0x14fd5b4: stur            w0, [x2, #0xb]
    // 0x14fd5b8: ldur            x0, [fp, #-0x10]
    // 0x14fd5bc: StoreField: r2->field_13 = r0
    //     0x14fd5bc: stur            w0, [x2, #0x13]
    // 0x14fd5c0: ldur            x0, [fp, #-8]
    // 0x14fd5c4: LoadField: r1 = r0->field_13
    //     0x14fd5c4: ldur            w1, [x0, #0x13]
    // 0x14fd5c8: DecompressPointer r1
    //     0x14fd5c8: add             x1, x1, HEAP, lsl #32
    // 0x14fd5cc: r0 = of()
    //     0x14fd5cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fd5d0: LoadField: r1 = r0->field_87
    //     0x14fd5d0: ldur            w1, [x0, #0x87]
    // 0x14fd5d4: DecompressPointer r1
    //     0x14fd5d4: add             x1, x1, HEAP, lsl #32
    // 0x14fd5d8: LoadField: r0 = r1->field_7
    //     0x14fd5d8: ldur            w0, [x1, #7]
    // 0x14fd5dc: DecompressPointer r0
    //     0x14fd5dc: add             x0, x0, HEAP, lsl #32
    // 0x14fd5e0: ldur            x2, [fp, #-8]
    // 0x14fd5e4: stur            x0, [fp, #-0x10]
    // 0x14fd5e8: LoadField: r1 = r2->field_13
    //     0x14fd5e8: ldur            w1, [x2, #0x13]
    // 0x14fd5ec: DecompressPointer r1
    //     0x14fd5ec: add             x1, x1, HEAP, lsl #32
    // 0x14fd5f0: r0 = of()
    //     0x14fd5f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fd5f4: LoadField: r1 = r0->field_5b
    //     0x14fd5f4: ldur            w1, [x0, #0x5b]
    // 0x14fd5f8: DecompressPointer r1
    //     0x14fd5f8: add             x1, x1, HEAP, lsl #32
    // 0x14fd5fc: r16 = 21.000000
    //     0x14fd5fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x14fd600: ldr             x16, [x16, #0x9b0]
    // 0x14fd604: stp             x16, x1, [SP]
    // 0x14fd608: ldur            x1, [fp, #-0x10]
    // 0x14fd60c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14fd60c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14fd610: ldr             x4, [x4, #0x9b8]
    // 0x14fd614: r0 = copyWith()
    //     0x14fd614: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fd618: stur            x0, [fp, #-0x10]
    // 0x14fd61c: r0 = Text()
    //     0x14fd61c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14fd620: mov             x2, x0
    // 0x14fd624: r0 = "—"
    //     0x14fd624: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9c8] "—"
    //     0x14fd628: ldr             x0, [x0, #0x9c8]
    // 0x14fd62c: stur            x2, [fp, #-0x20]
    // 0x14fd630: StoreField: r2->field_b = r0
    //     0x14fd630: stur            w0, [x2, #0xb]
    // 0x14fd634: ldur            x0, [fp, #-0x10]
    // 0x14fd638: StoreField: r2->field_13 = r0
    //     0x14fd638: stur            w0, [x2, #0x13]
    // 0x14fd63c: r1 = Instance_Color
    //     0x14fd63c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fd640: d0 = 0.100000
    //     0x14fd640: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14fd644: r0 = withOpacity()
    //     0x14fd644: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fd648: stur            x0, [fp, #-0x10]
    // 0x14fd64c: r0 = Divider()
    //     0x14fd64c: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x14fd650: mov             x3, x0
    // 0x14fd654: ldur            x0, [fp, #-0x10]
    // 0x14fd658: stur            x3, [fp, #-0x28]
    // 0x14fd65c: StoreField: r3->field_1f = r0
    //     0x14fd65c: stur            w0, [x3, #0x1f]
    // 0x14fd660: r1 = Null
    //     0x14fd660: mov             x1, NULL
    // 0x14fd664: r2 = 10
    //     0x14fd664: movz            x2, #0xa
    // 0x14fd668: r0 = AllocateArray()
    //     0x14fd668: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fd66c: stur            x0, [fp, #-0x10]
    // 0x14fd670: r16 = "Showing "
    //     0x14fd670: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d0] "Showing "
    //     0x14fd674: ldr             x16, [x16, #0x9d0]
    // 0x14fd678: StoreField: r0->field_f = r16
    //     0x14fd678: stur            w16, [x0, #0xf]
    // 0x14fd67c: ldur            x2, [fp, #-8]
    // 0x14fd680: LoadField: r1 = r2->field_f
    //     0x14fd680: ldur            w1, [x2, #0xf]
    // 0x14fd684: DecompressPointer r1
    //     0x14fd684: add             x1, x1, HEAP, lsl #32
    // 0x14fd688: r0 = controller()
    //     0x14fd688: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd68c: LoadField: r1 = r0->field_57
    //     0x14fd68c: ldur            w1, [x0, #0x57]
    // 0x14fd690: DecompressPointer r1
    //     0x14fd690: add             x1, x1, HEAP, lsl #32
    // 0x14fd694: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fd694: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fd698: r0 = toList()
    //     0x14fd698: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14fd69c: LoadField: r1 = r0->field_b
    //     0x14fd69c: ldur            w1, [x0, #0xb]
    // 0x14fd6a0: ldur            x0, [fp, #-0x10]
    // 0x14fd6a4: StoreField: r0->field_13 = r1
    //     0x14fd6a4: stur            w1, [x0, #0x13]
    // 0x14fd6a8: r16 = " out of "
    //     0x14fd6a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9d8] " out of "
    //     0x14fd6ac: ldr             x16, [x16, #0x9d8]
    // 0x14fd6b0: ArrayStore: r0[0] = r16  ; List_4
    //     0x14fd6b0: stur            w16, [x0, #0x17]
    // 0x14fd6b4: ldur            x2, [fp, #-8]
    // 0x14fd6b8: LoadField: r1 = r2->field_f
    //     0x14fd6b8: ldur            w1, [x2, #0xf]
    // 0x14fd6bc: DecompressPointer r1
    //     0x14fd6bc: add             x1, x1, HEAP, lsl #32
    // 0x14fd6c0: r0 = controller()
    //     0x14fd6c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd6c4: LoadField: r1 = r0->field_53
    //     0x14fd6c4: ldur            w1, [x0, #0x53]
    // 0x14fd6c8: DecompressPointer r1
    //     0x14fd6c8: add             x1, x1, HEAP, lsl #32
    // 0x14fd6cc: r0 = value()
    //     0x14fd6cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14fd6d0: LoadField: r1 = r0->field_b
    //     0x14fd6d0: ldur            w1, [x0, #0xb]
    // 0x14fd6d4: DecompressPointer r1
    //     0x14fd6d4: add             x1, x1, HEAP, lsl #32
    // 0x14fd6d8: cmp             w1, NULL
    // 0x14fd6dc: b.ne            #0x14fd6e8
    // 0x14fd6e0: r0 = Null
    //     0x14fd6e0: mov             x0, NULL
    // 0x14fd6e4: b               #0x14fd70c
    // 0x14fd6e8: LoadField: r0 = r1->field_b
    //     0x14fd6e8: ldur            w0, [x1, #0xb]
    // 0x14fd6ec: DecompressPointer r0
    //     0x14fd6ec: add             x0, x0, HEAP, lsl #32
    // 0x14fd6f0: cmp             w0, NULL
    // 0x14fd6f4: b.ne            #0x14fd700
    // 0x14fd6f8: r0 = Null
    //     0x14fd6f8: mov             x0, NULL
    // 0x14fd6fc: b               #0x14fd70c
    // 0x14fd700: LoadField: r1 = r0->field_f
    //     0x14fd700: ldur            w1, [x0, #0xf]
    // 0x14fd704: DecompressPointer r1
    //     0x14fd704: add             x1, x1, HEAP, lsl #32
    // 0x14fd708: mov             x0, x1
    // 0x14fd70c: ldur            x3, [fp, #-8]
    // 0x14fd710: ldur            x6, [fp, #-0x18]
    // 0x14fd714: ldur            x5, [fp, #-0x20]
    // 0x14fd718: ldur            x4, [fp, #-0x28]
    // 0x14fd71c: ldur            x2, [fp, #-0x10]
    // 0x14fd720: mov             x1, x2
    // 0x14fd724: ArrayStore: r1[3] = r0  ; List_4
    //     0x14fd724: add             x25, x1, #0x1b
    //     0x14fd728: str             w0, [x25]
    //     0x14fd72c: tbz             w0, #0, #0x14fd748
    //     0x14fd730: ldurb           w16, [x1, #-1]
    //     0x14fd734: ldurb           w17, [x0, #-1]
    //     0x14fd738: and             x16, x17, x16, lsr #2
    //     0x14fd73c: tst             x16, HEAP, lsr #32
    //     0x14fd740: b.eq            #0x14fd748
    //     0x14fd744: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14fd748: r16 = " testimonials"
    //     0x14fd748: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e0] " testimonials"
    //     0x14fd74c: ldr             x16, [x16, #0x9e0]
    // 0x14fd750: StoreField: r2->field_1f = r16
    //     0x14fd750: stur            w16, [x2, #0x1f]
    // 0x14fd754: str             x2, [SP]
    // 0x14fd758: r0 = _interpolate()
    //     0x14fd758: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x14fd75c: mov             x2, x0
    // 0x14fd760: ldur            x0, [fp, #-8]
    // 0x14fd764: stur            x2, [fp, #-0x10]
    // 0x14fd768: LoadField: r1 = r0->field_13
    //     0x14fd768: ldur            w1, [x0, #0x13]
    // 0x14fd76c: DecompressPointer r1
    //     0x14fd76c: add             x1, x1, HEAP, lsl #32
    // 0x14fd770: r0 = of()
    //     0x14fd770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14fd774: LoadField: r1 = r0->field_87
    //     0x14fd774: ldur            w1, [x0, #0x87]
    // 0x14fd778: DecompressPointer r1
    //     0x14fd778: add             x1, x1, HEAP, lsl #32
    // 0x14fd77c: LoadField: r0 = r1->field_2b
    //     0x14fd77c: ldur            w0, [x1, #0x2b]
    // 0x14fd780: DecompressPointer r0
    //     0x14fd780: add             x0, x0, HEAP, lsl #32
    // 0x14fd784: stur            x0, [fp, #-0x30]
    // 0x14fd788: r1 = Instance_Color
    //     0x14fd788: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14fd78c: d0 = 0.700000
    //     0x14fd78c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x14fd790: ldr             d0, [x17, #0xf48]
    // 0x14fd794: r0 = withOpacity()
    //     0x14fd794: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14fd798: r16 = 12.000000
    //     0x14fd798: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14fd79c: ldr             x16, [x16, #0x9e8]
    // 0x14fd7a0: stp             x16, x0, [SP]
    // 0x14fd7a4: ldur            x1, [fp, #-0x30]
    // 0x14fd7a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14fd7a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14fd7ac: ldr             x4, [x4, #0x9b8]
    // 0x14fd7b0: r0 = copyWith()
    //     0x14fd7b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14fd7b4: stur            x0, [fp, #-0x30]
    // 0x14fd7b8: r0 = Text()
    //     0x14fd7b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14fd7bc: mov             x3, x0
    // 0x14fd7c0: ldur            x0, [fp, #-0x10]
    // 0x14fd7c4: stur            x3, [fp, #-0x38]
    // 0x14fd7c8: StoreField: r3->field_b = r0
    //     0x14fd7c8: stur            w0, [x3, #0xb]
    // 0x14fd7cc: ldur            x0, [fp, #-0x30]
    // 0x14fd7d0: StoreField: r3->field_13 = r0
    //     0x14fd7d0: stur            w0, [x3, #0x13]
    // 0x14fd7d4: r1 = Null
    //     0x14fd7d4: mov             x1, NULL
    // 0x14fd7d8: r2 = 10
    //     0x14fd7d8: movz            x2, #0xa
    // 0x14fd7dc: r0 = AllocateArray()
    //     0x14fd7dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14fd7e0: mov             x2, x0
    // 0x14fd7e4: ldur            x0, [fp, #-0x18]
    // 0x14fd7e8: stur            x2, [fp, #-0x10]
    // 0x14fd7ec: StoreField: r2->field_f = r0
    //     0x14fd7ec: stur            w0, [x2, #0xf]
    // 0x14fd7f0: ldur            x0, [fp, #-0x20]
    // 0x14fd7f4: StoreField: r2->field_13 = r0
    //     0x14fd7f4: stur            w0, [x2, #0x13]
    // 0x14fd7f8: ldur            x0, [fp, #-0x28]
    // 0x14fd7fc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fd7fc: stur            w0, [x2, #0x17]
    // 0x14fd800: ldur            x0, [fp, #-0x38]
    // 0x14fd804: StoreField: r2->field_1b = r0
    //     0x14fd804: stur            w0, [x2, #0x1b]
    // 0x14fd808: r16 = Instance_SizedBox
    //     0x14fd808: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x14fd80c: ldr             x16, [x16, #0x9f0]
    // 0x14fd810: StoreField: r2->field_1f = r16
    //     0x14fd810: stur            w16, [x2, #0x1f]
    // 0x14fd814: r1 = <Widget>
    //     0x14fd814: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14fd818: r0 = AllocateGrowableArray()
    //     0x14fd818: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14fd81c: mov             x2, x0
    // 0x14fd820: ldur            x0, [fp, #-0x10]
    // 0x14fd824: stur            x2, [fp, #-0x18]
    // 0x14fd828: StoreField: r2->field_f = r0
    //     0x14fd828: stur            w0, [x2, #0xf]
    // 0x14fd82c: r0 = 10
    //     0x14fd82c: movz            x0, #0xa
    // 0x14fd830: StoreField: r2->field_b = r0
    //     0x14fd830: stur            w0, [x2, #0xb]
    // 0x14fd834: ldur            x0, [fp, #-8]
    // 0x14fd838: LoadField: r1 = r0->field_f
    //     0x14fd838: ldur            w1, [x0, #0xf]
    // 0x14fd83c: DecompressPointer r1
    //     0x14fd83c: add             x1, x1, HEAP, lsl #32
    // 0x14fd840: r0 = controller()
    //     0x14fd840: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd844: LoadField: r1 = r0->field_67
    //     0x14fd844: ldur            w1, [x0, #0x67]
    // 0x14fd848: DecompressPointer r1
    //     0x14fd848: add             x1, x1, HEAP, lsl #32
    // 0x14fd84c: r0 = LoadClassIdInstr(r1)
    //     0x14fd84c: ldur            x0, [x1, #-1]
    //     0x14fd850: ubfx            x0, x0, #0xc, #0x14
    // 0x14fd854: r16 = "testimonial"
    //     0x14fd854: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f8] "testimonial"
    //     0x14fd858: ldr             x16, [x16, #0x9f8]
    // 0x14fd85c: stp             x16, x1, [SP]
    // 0x14fd860: mov             lr, x0
    // 0x14fd864: ldr             lr, [x21, lr, lsl #3]
    // 0x14fd868: blr             lr
    // 0x14fd86c: tbnz            w0, #4, #0x14fd97c
    // 0x14fd870: ldur            x2, [fp, #-8]
    // 0x14fd874: ldur            x0, [fp, #-0x18]
    // 0x14fd878: LoadField: r1 = r2->field_f
    //     0x14fd878: ldur            w1, [x2, #0xf]
    // 0x14fd87c: DecompressPointer r1
    //     0x14fd87c: add             x1, x1, HEAP, lsl #32
    // 0x14fd880: r0 = controller()
    //     0x14fd880: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd884: LoadField: r1 = r0->field_57
    //     0x14fd884: ldur            w1, [x0, #0x57]
    // 0x14fd888: DecompressPointer r1
    //     0x14fd888: add             x1, x1, HEAP, lsl #32
    // 0x14fd88c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fd88c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fd890: r0 = toList()
    //     0x14fd890: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14fd894: stur            x0, [fp, #-0x10]
    // 0x14fd898: r0 = Radius()
    //     0x14fd898: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14fd89c: d0 = 15.000000
    //     0x14fd89c: fmov            d0, #15.00000000
    // 0x14fd8a0: stur            x0, [fp, #-0x20]
    // 0x14fd8a4: StoreField: r0->field_7 = d0
    //     0x14fd8a4: stur            d0, [x0, #7]
    // 0x14fd8a8: StoreField: r0->field_f = d0
    //     0x14fd8a8: stur            d0, [x0, #0xf]
    // 0x14fd8ac: r0 = BorderRadius()
    //     0x14fd8ac: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14fd8b0: mov             x1, x0
    // 0x14fd8b4: ldur            x0, [fp, #-0x20]
    // 0x14fd8b8: stur            x1, [fp, #-0x28]
    // 0x14fd8bc: StoreField: r1->field_7 = r0
    //     0x14fd8bc: stur            w0, [x1, #7]
    // 0x14fd8c0: StoreField: r1->field_b = r0
    //     0x14fd8c0: stur            w0, [x1, #0xb]
    // 0x14fd8c4: StoreField: r1->field_f = r0
    //     0x14fd8c4: stur            w0, [x1, #0xf]
    // 0x14fd8c8: StoreField: r1->field_13 = r0
    //     0x14fd8c8: stur            w0, [x1, #0x13]
    // 0x14fd8cc: r0 = TestimonialViewAll()
    //     0x14fd8cc: bl              #0x147c4c8  ; AllocateTestimonialViewAllStub -> TestimonialViewAll (size=0x1c)
    // 0x14fd8d0: mov             x2, x0
    // 0x14fd8d4: ldur            x0, [fp, #-0x10]
    // 0x14fd8d8: stur            x2, [fp, #-0x20]
    // 0x14fd8dc: StoreField: r2->field_b = r0
    //     0x14fd8dc: stur            w0, [x2, #0xb]
    // 0x14fd8e0: ldur            x0, [fp, #-0x28]
    // 0x14fd8e4: StoreField: r2->field_f = r0
    //     0x14fd8e4: stur            w0, [x2, #0xf]
    // 0x14fd8e8: r0 = Instance_Border
    //     0x14fd8e8: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e868] Obj!Border@d5ab41
    //     0x14fd8ec: ldr             x0, [x0, #0x868]
    // 0x14fd8f0: StoreField: r2->field_13 = r0
    //     0x14fd8f0: stur            w0, [x2, #0x13]
    // 0x14fd8f4: r0 = Instance_Color
    //     0x14fd8f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14fd8f8: ldr             x0, [x0, #0x90]
    // 0x14fd8fc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fd8fc: stur            w0, [x2, #0x17]
    // 0x14fd900: ldur            x3, [fp, #-0x18]
    // 0x14fd904: LoadField: r1 = r3->field_b
    //     0x14fd904: ldur            w1, [x3, #0xb]
    // 0x14fd908: LoadField: r4 = r3->field_f
    //     0x14fd908: ldur            w4, [x3, #0xf]
    // 0x14fd90c: DecompressPointer r4
    //     0x14fd90c: add             x4, x4, HEAP, lsl #32
    // 0x14fd910: LoadField: r5 = r4->field_b
    //     0x14fd910: ldur            w5, [x4, #0xb]
    // 0x14fd914: r4 = LoadInt32Instr(r1)
    //     0x14fd914: sbfx            x4, x1, #1, #0x1f
    // 0x14fd918: stur            x4, [fp, #-0x40]
    // 0x14fd91c: r1 = LoadInt32Instr(r5)
    //     0x14fd91c: sbfx            x1, x5, #1, #0x1f
    // 0x14fd920: cmp             x4, x1
    // 0x14fd924: b.ne            #0x14fd930
    // 0x14fd928: mov             x1, x3
    // 0x14fd92c: r0 = _growToNextCapacity()
    //     0x14fd92c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14fd930: ldur            x2, [fp, #-0x18]
    // 0x14fd934: ldur            x3, [fp, #-0x40]
    // 0x14fd938: add             x0, x3, #1
    // 0x14fd93c: lsl             x1, x0, #1
    // 0x14fd940: StoreField: r2->field_b = r1
    //     0x14fd940: stur            w1, [x2, #0xb]
    // 0x14fd944: LoadField: r1 = r2->field_f
    //     0x14fd944: ldur            w1, [x2, #0xf]
    // 0x14fd948: DecompressPointer r1
    //     0x14fd948: add             x1, x1, HEAP, lsl #32
    // 0x14fd94c: ldur            x0, [fp, #-0x20]
    // 0x14fd950: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14fd950: add             x25, x1, x3, lsl #2
    //     0x14fd954: add             x25, x25, #0xf
    //     0x14fd958: str             w0, [x25]
    //     0x14fd95c: tbz             w0, #0, #0x14fd978
    //     0x14fd960: ldurb           w16, [x1, #-1]
    //     0x14fd964: ldurb           w17, [x0, #-1]
    //     0x14fd968: and             x16, x17, x16, lsr #2
    //     0x14fd96c: tst             x16, HEAP, lsr #32
    //     0x14fd970: b.eq            #0x14fd978
    //     0x14fd974: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14fd978: b               #0x14fd980
    // 0x14fd97c: ldur            x2, [fp, #-0x18]
    // 0x14fd980: ldur            x0, [fp, #-8]
    // 0x14fd984: LoadField: r1 = r0->field_f
    //     0x14fd984: ldur            w1, [x0, #0xf]
    // 0x14fd988: DecompressPointer r1
    //     0x14fd988: add             x1, x1, HEAP, lsl #32
    // 0x14fd98c: r0 = controller()
    //     0x14fd98c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd990: LoadField: r1 = r0->field_67
    //     0x14fd990: ldur            w1, [x0, #0x67]
    // 0x14fd994: DecompressPointer r1
    //     0x14fd994: add             x1, x1, HEAP, lsl #32
    // 0x14fd998: r0 = LoadClassIdInstr(r1)
    //     0x14fd998: ldur            x0, [x1, #-1]
    //     0x14fd99c: ubfx            x0, x0, #0xc, #0x14
    // 0x14fd9a0: r16 = "product_testimonial"
    //     0x14fd9a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea00] "product_testimonial"
    //     0x14fd9a4: ldr             x16, [x16, #0xa00]
    // 0x14fd9a8: stp             x16, x1, [SP]
    // 0x14fd9ac: mov             lr, x0
    // 0x14fd9b0: ldr             lr, [x21, lr, lsl #3]
    // 0x14fd9b4: blr             lr
    // 0x14fd9b8: tbnz            w0, #4, #0x14fda8c
    // 0x14fd9bc: ldur            x1, [fp, #-8]
    // 0x14fd9c0: ldur            x0, [fp, #-0x18]
    // 0x14fd9c4: LoadField: r2 = r1->field_f
    //     0x14fd9c4: ldur            w2, [x1, #0xf]
    // 0x14fd9c8: DecompressPointer r2
    //     0x14fd9c8: add             x2, x2, HEAP, lsl #32
    // 0x14fd9cc: mov             x1, x2
    // 0x14fd9d0: r0 = controller()
    //     0x14fd9d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14fd9d4: LoadField: r1 = r0->field_57
    //     0x14fd9d4: ldur            w1, [x0, #0x57]
    // 0x14fd9d8: DecompressPointer r1
    //     0x14fd9d8: add             x1, x1, HEAP, lsl #32
    // 0x14fd9dc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14fd9dc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14fd9e0: r0 = toList()
    //     0x14fd9e0: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14fd9e4: stur            x0, [fp, #-8]
    // 0x14fd9e8: r0 = ProductTestimonialViewAll()
    //     0x14fd9e8: bl              #0x147c4bc  ; AllocateProductTestimonialViewAllStub -> ProductTestimonialViewAll (size=0x1c)
    // 0x14fd9ec: mov             x2, x0
    // 0x14fd9f0: ldur            x0, [fp, #-8]
    // 0x14fd9f4: stur            x2, [fp, #-0x10]
    // 0x14fd9f8: StoreField: r2->field_b = r0
    //     0x14fd9f8: stur            w0, [x2, #0xb]
    // 0x14fd9fc: d0 = 15.000000
    //     0x14fd9fc: fmov            d0, #15.00000000
    // 0x14fda00: StoreField: r2->field_f = d0
    //     0x14fda00: stur            d0, [x2, #0xf]
    // 0x14fda04: r0 = Instance_Color
    //     0x14fda04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x14fda08: ldr             x0, [x0, #0x90]
    // 0x14fda0c: ArrayStore: r2[0] = r0  ; List_4
    //     0x14fda0c: stur            w0, [x2, #0x17]
    // 0x14fda10: ldur            x0, [fp, #-0x18]
    // 0x14fda14: LoadField: r1 = r0->field_b
    //     0x14fda14: ldur            w1, [x0, #0xb]
    // 0x14fda18: LoadField: r3 = r0->field_f
    //     0x14fda18: ldur            w3, [x0, #0xf]
    // 0x14fda1c: DecompressPointer r3
    //     0x14fda1c: add             x3, x3, HEAP, lsl #32
    // 0x14fda20: LoadField: r4 = r3->field_b
    //     0x14fda20: ldur            w4, [x3, #0xb]
    // 0x14fda24: r3 = LoadInt32Instr(r1)
    //     0x14fda24: sbfx            x3, x1, #1, #0x1f
    // 0x14fda28: stur            x3, [fp, #-0x40]
    // 0x14fda2c: r1 = LoadInt32Instr(r4)
    //     0x14fda2c: sbfx            x1, x4, #1, #0x1f
    // 0x14fda30: cmp             x3, x1
    // 0x14fda34: b.ne            #0x14fda40
    // 0x14fda38: mov             x1, x0
    // 0x14fda3c: r0 = _growToNextCapacity()
    //     0x14fda3c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x14fda40: ldur            x2, [fp, #-0x18]
    // 0x14fda44: ldur            x3, [fp, #-0x40]
    // 0x14fda48: add             x0, x3, #1
    // 0x14fda4c: lsl             x1, x0, #1
    // 0x14fda50: StoreField: r2->field_b = r1
    //     0x14fda50: stur            w1, [x2, #0xb]
    // 0x14fda54: LoadField: r1 = r2->field_f
    //     0x14fda54: ldur            w1, [x2, #0xf]
    // 0x14fda58: DecompressPointer r1
    //     0x14fda58: add             x1, x1, HEAP, lsl #32
    // 0x14fda5c: ldur            x0, [fp, #-0x10]
    // 0x14fda60: ArrayStore: r1[r3] = r0  ; List_4
    //     0x14fda60: add             x25, x1, x3, lsl #2
    //     0x14fda64: add             x25, x25, #0xf
    //     0x14fda68: str             w0, [x25]
    //     0x14fda6c: tbz             w0, #0, #0x14fda88
    //     0x14fda70: ldurb           w16, [x1, #-1]
    //     0x14fda74: ldurb           w17, [x0, #-1]
    //     0x14fda78: and             x16, x17, x16, lsr #2
    //     0x14fda7c: tst             x16, HEAP, lsr #32
    //     0x14fda80: b.eq            #0x14fda88
    //     0x14fda84: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x14fda88: b               #0x14fda90
    // 0x14fda8c: ldur            x2, [fp, #-0x18]
    // 0x14fda90: r0 = Column()
    //     0x14fda90: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14fda94: r1 = Instance_Axis
    //     0x14fda94: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14fda98: StoreField: r0->field_f = r1
    //     0x14fda98: stur            w1, [x0, #0xf]
    // 0x14fda9c: r1 = Instance_MainAxisAlignment
    //     0x14fda9c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14fdaa0: ldr             x1, [x1, #0xa08]
    // 0x14fdaa4: StoreField: r0->field_13 = r1
    //     0x14fdaa4: stur            w1, [x0, #0x13]
    // 0x14fdaa8: r1 = Instance_MainAxisSize
    //     0x14fdaa8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14fdaac: ldr             x1, [x1, #0xa10]
    // 0x14fdab0: ArrayStore: r0[0] = r1  ; List_4
    //     0x14fdab0: stur            w1, [x0, #0x17]
    // 0x14fdab4: r1 = Instance_CrossAxisAlignment
    //     0x14fdab4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14fdab8: ldr             x1, [x1, #0xa18]
    // 0x14fdabc: StoreField: r0->field_1b = r1
    //     0x14fdabc: stur            w1, [x0, #0x1b]
    // 0x14fdac0: r1 = Instance_VerticalDirection
    //     0x14fdac0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14fdac4: ldr             x1, [x1, #0xa20]
    // 0x14fdac8: StoreField: r0->field_23 = r1
    //     0x14fdac8: stur            w1, [x0, #0x23]
    // 0x14fdacc: r1 = Instance_Clip
    //     0x14fdacc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14fdad0: ldr             x1, [x1, #0x38]
    // 0x14fdad4: StoreField: r0->field_2b = r1
    //     0x14fdad4: stur            w1, [x0, #0x2b]
    // 0x14fdad8: StoreField: r0->field_2f = rZR
    //     0x14fdad8: stur            xzr, [x0, #0x2f]
    // 0x14fdadc: ldur            x1, [fp, #-0x18]
    // 0x14fdae0: StoreField: r0->field_b = r1
    //     0x14fdae0: stur            w1, [x0, #0xb]
    // 0x14fdae4: LeaveFrame
    //     0x14fdae4: mov             SP, fp
    //     0x14fdae8: ldp             fp, lr, [SP], #0x10
    // 0x14fdaec: ret
    //     0x14fdaec: ret             
    // 0x14fdaf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14fdaf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14fdaf4: b               #0x14fd528
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e5ee8, size: 0x2b4
    // 0x15e5ee8: EnterFrame
    //     0x15e5ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x15e5eec: mov             fp, SP
    // 0x15e5ef0: AllocStack(0x30)
    //     0x15e5ef0: sub             SP, SP, #0x30
    // 0x15e5ef4: SetupParameters(TestimonialsView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e5ef4: stur            x1, [fp, #-8]
    //     0x15e5ef8: stur            x2, [fp, #-0x10]
    // 0x15e5efc: CheckStackOverflow
    //     0x15e5efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e5f00: cmp             SP, x16
    //     0x15e5f04: b.ls            #0x15e6194
    // 0x15e5f08: r1 = 2
    //     0x15e5f08: movz            x1, #0x2
    // 0x15e5f0c: r0 = AllocateContext()
    //     0x15e5f0c: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e5f10: ldur            x1, [fp, #-8]
    // 0x15e5f14: stur            x0, [fp, #-0x18]
    // 0x15e5f18: StoreField: r0->field_f = r1
    //     0x15e5f18: stur            w1, [x0, #0xf]
    // 0x15e5f1c: ldur            x2, [fp, #-0x10]
    // 0x15e5f20: StoreField: r0->field_13 = r2
    //     0x15e5f20: stur            w2, [x0, #0x13]
    // 0x15e5f24: r0 = Obx()
    //     0x15e5f24: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e5f28: ldur            x2, [fp, #-0x18]
    // 0x15e5f2c: r1 = Function '<anonymous closure>':.
    //     0x15e5f2c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e870] AnonymousClosure: (0x15cfe38), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_screen.dart] ExchangeCheckoutScreen::appBar (0x15e9d84)
    //     0x15e5f30: ldr             x1, [x1, #0x870]
    // 0x15e5f34: stur            x0, [fp, #-0x10]
    // 0x15e5f38: r0 = AllocateClosure()
    //     0x15e5f38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e5f3c: mov             x1, x0
    // 0x15e5f40: ldur            x0, [fp, #-0x10]
    // 0x15e5f44: StoreField: r0->field_b = r1
    //     0x15e5f44: stur            w1, [x0, #0xb]
    // 0x15e5f48: ldur            x1, [fp, #-8]
    // 0x15e5f4c: r0 = controller()
    //     0x15e5f4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e5f50: LoadField: r1 = r0->field_6b
    //     0x15e5f50: ldur            w1, [x0, #0x6b]
    // 0x15e5f54: DecompressPointer r1
    //     0x15e5f54: add             x1, x1, HEAP, lsl #32
    // 0x15e5f58: r0 = value()
    //     0x15e5f58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e5f5c: tbnz            w0, #4, #0x15e5ff4
    // 0x15e5f60: ldur            x2, [fp, #-0x18]
    // 0x15e5f64: LoadField: r1 = r2->field_13
    //     0x15e5f64: ldur            w1, [x2, #0x13]
    // 0x15e5f68: DecompressPointer r1
    //     0x15e5f68: add             x1, x1, HEAP, lsl #32
    // 0x15e5f6c: r0 = of()
    //     0x15e5f6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e5f70: LoadField: r1 = r0->field_5b
    //     0x15e5f70: ldur            w1, [x0, #0x5b]
    // 0x15e5f74: DecompressPointer r1
    //     0x15e5f74: add             x1, x1, HEAP, lsl #32
    // 0x15e5f78: stur            x1, [fp, #-8]
    // 0x15e5f7c: r0 = ColorFilter()
    //     0x15e5f7c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e5f80: mov             x1, x0
    // 0x15e5f84: ldur            x0, [fp, #-8]
    // 0x15e5f88: stur            x1, [fp, #-0x20]
    // 0x15e5f8c: StoreField: r1->field_7 = r0
    //     0x15e5f8c: stur            w0, [x1, #7]
    // 0x15e5f90: r0 = Instance_BlendMode
    //     0x15e5f90: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e5f94: ldr             x0, [x0, #0xb30]
    // 0x15e5f98: StoreField: r1->field_b = r0
    //     0x15e5f98: stur            w0, [x1, #0xb]
    // 0x15e5f9c: r2 = 1
    //     0x15e5f9c: movz            x2, #0x1
    // 0x15e5fa0: StoreField: r1->field_13 = r2
    //     0x15e5fa0: stur            x2, [x1, #0x13]
    // 0x15e5fa4: r0 = SvgPicture()
    //     0x15e5fa4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e5fa8: stur            x0, [fp, #-8]
    // 0x15e5fac: ldur            x16, [fp, #-0x20]
    // 0x15e5fb0: str             x16, [SP]
    // 0x15e5fb4: mov             x1, x0
    // 0x15e5fb8: r2 = "assets/images/search.svg"
    //     0x15e5fb8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e5fbc: ldr             x2, [x2, #0xa30]
    // 0x15e5fc0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e5fc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e5fc4: ldr             x4, [x4, #0xa38]
    // 0x15e5fc8: r0 = SvgPicture.asset()
    //     0x15e5fc8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e5fcc: r0 = Align()
    //     0x15e5fcc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e5fd0: r3 = Instance_Alignment
    //     0x15e5fd0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e5fd4: ldr             x3, [x3, #0xb10]
    // 0x15e5fd8: StoreField: r0->field_f = r3
    //     0x15e5fd8: stur            w3, [x0, #0xf]
    // 0x15e5fdc: r4 = 1.000000
    //     0x15e5fdc: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e5fe0: StoreField: r0->field_13 = r4
    //     0x15e5fe0: stur            w4, [x0, #0x13]
    // 0x15e5fe4: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e5fe4: stur            w4, [x0, #0x17]
    // 0x15e5fe8: ldur            x1, [fp, #-8]
    // 0x15e5fec: StoreField: r0->field_b = r1
    //     0x15e5fec: stur            w1, [x0, #0xb]
    // 0x15e5ff0: b               #0x15e60a4
    // 0x15e5ff4: ldur            x5, [fp, #-0x18]
    // 0x15e5ff8: r4 = 1.000000
    //     0x15e5ff8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e5ffc: r0 = Instance_BlendMode
    //     0x15e5ffc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6000: ldr             x0, [x0, #0xb30]
    // 0x15e6004: r3 = Instance_Alignment
    //     0x15e6004: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e6008: ldr             x3, [x3, #0xb10]
    // 0x15e600c: r2 = 1
    //     0x15e600c: movz            x2, #0x1
    // 0x15e6010: LoadField: r1 = r5->field_13
    //     0x15e6010: ldur            w1, [x5, #0x13]
    // 0x15e6014: DecompressPointer r1
    //     0x15e6014: add             x1, x1, HEAP, lsl #32
    // 0x15e6018: r0 = of()
    //     0x15e6018: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e601c: LoadField: r1 = r0->field_5b
    //     0x15e601c: ldur            w1, [x0, #0x5b]
    // 0x15e6020: DecompressPointer r1
    //     0x15e6020: add             x1, x1, HEAP, lsl #32
    // 0x15e6024: stur            x1, [fp, #-8]
    // 0x15e6028: r0 = ColorFilter()
    //     0x15e6028: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e602c: mov             x1, x0
    // 0x15e6030: ldur            x0, [fp, #-8]
    // 0x15e6034: stur            x1, [fp, #-0x20]
    // 0x15e6038: StoreField: r1->field_7 = r0
    //     0x15e6038: stur            w0, [x1, #7]
    // 0x15e603c: r0 = Instance_BlendMode
    //     0x15e603c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6040: ldr             x0, [x0, #0xb30]
    // 0x15e6044: StoreField: r1->field_b = r0
    //     0x15e6044: stur            w0, [x1, #0xb]
    // 0x15e6048: r0 = 1
    //     0x15e6048: movz            x0, #0x1
    // 0x15e604c: StoreField: r1->field_13 = r0
    //     0x15e604c: stur            x0, [x1, #0x13]
    // 0x15e6050: r0 = SvgPicture()
    //     0x15e6050: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e6054: stur            x0, [fp, #-8]
    // 0x15e6058: ldur            x16, [fp, #-0x20]
    // 0x15e605c: str             x16, [SP]
    // 0x15e6060: mov             x1, x0
    // 0x15e6064: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e6064: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e6068: ldr             x2, [x2, #0xa40]
    // 0x15e606c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e606c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e6070: ldr             x4, [x4, #0xa38]
    // 0x15e6074: r0 = SvgPicture.asset()
    //     0x15e6074: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e6078: r0 = Align()
    //     0x15e6078: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e607c: mov             x1, x0
    // 0x15e6080: r0 = Instance_Alignment
    //     0x15e6080: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e6084: ldr             x0, [x0, #0xb10]
    // 0x15e6088: StoreField: r1->field_f = r0
    //     0x15e6088: stur            w0, [x1, #0xf]
    // 0x15e608c: r0 = 1.000000
    //     0x15e608c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e6090: StoreField: r1->field_13 = r0
    //     0x15e6090: stur            w0, [x1, #0x13]
    // 0x15e6094: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e6094: stur            w0, [x1, #0x17]
    // 0x15e6098: ldur            x0, [fp, #-8]
    // 0x15e609c: StoreField: r1->field_b = r0
    //     0x15e609c: stur            w0, [x1, #0xb]
    // 0x15e60a0: mov             x0, x1
    // 0x15e60a4: stur            x0, [fp, #-8]
    // 0x15e60a8: r0 = InkWell()
    //     0x15e60a8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e60ac: mov             x3, x0
    // 0x15e60b0: ldur            x0, [fp, #-8]
    // 0x15e60b4: stur            x3, [fp, #-0x20]
    // 0x15e60b8: StoreField: r3->field_b = r0
    //     0x15e60b8: stur            w0, [x3, #0xb]
    // 0x15e60bc: ldur            x2, [fp, #-0x18]
    // 0x15e60c0: r1 = Function '<anonymous closure>':.
    //     0x15e60c0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e878] AnonymousClosure: (0x15d8310), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15e60c4: ldr             x1, [x1, #0x878]
    // 0x15e60c8: r0 = AllocateClosure()
    //     0x15e60c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e60cc: ldur            x2, [fp, #-0x20]
    // 0x15e60d0: StoreField: r2->field_f = r0
    //     0x15e60d0: stur            w0, [x2, #0xf]
    // 0x15e60d4: r0 = true
    //     0x15e60d4: add             x0, NULL, #0x20  ; true
    // 0x15e60d8: StoreField: r2->field_43 = r0
    //     0x15e60d8: stur            w0, [x2, #0x43]
    // 0x15e60dc: r1 = Instance_BoxShape
    //     0x15e60dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e60e0: ldr             x1, [x1, #0x80]
    // 0x15e60e4: StoreField: r2->field_47 = r1
    //     0x15e60e4: stur            w1, [x2, #0x47]
    // 0x15e60e8: StoreField: r2->field_6f = r0
    //     0x15e60e8: stur            w0, [x2, #0x6f]
    // 0x15e60ec: r1 = false
    //     0x15e60ec: add             x1, NULL, #0x30  ; false
    // 0x15e60f0: StoreField: r2->field_73 = r1
    //     0x15e60f0: stur            w1, [x2, #0x73]
    // 0x15e60f4: StoreField: r2->field_83 = r0
    //     0x15e60f4: stur            w0, [x2, #0x83]
    // 0x15e60f8: StoreField: r2->field_7b = r1
    //     0x15e60f8: stur            w1, [x2, #0x7b]
    // 0x15e60fc: r0 = Obx()
    //     0x15e60fc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e6100: ldur            x2, [fp, #-0x18]
    // 0x15e6104: r1 = Function '<anonymous closure>':.
    //     0x15e6104: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e880] AnonymousClosure: (0x15e619c), in [package:customer_app/app/presentation/views/glass/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15e5ee8)
    //     0x15e6108: ldr             x1, [x1, #0x880]
    // 0x15e610c: stur            x0, [fp, #-8]
    // 0x15e6110: r0 = AllocateClosure()
    //     0x15e6110: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6114: mov             x1, x0
    // 0x15e6118: ldur            x0, [fp, #-8]
    // 0x15e611c: StoreField: r0->field_b = r1
    //     0x15e611c: stur            w1, [x0, #0xb]
    // 0x15e6120: r1 = Null
    //     0x15e6120: mov             x1, NULL
    // 0x15e6124: r2 = 2
    //     0x15e6124: movz            x2, #0x2
    // 0x15e6128: r0 = AllocateArray()
    //     0x15e6128: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15e612c: mov             x2, x0
    // 0x15e6130: ldur            x0, [fp, #-8]
    // 0x15e6134: stur            x2, [fp, #-0x18]
    // 0x15e6138: StoreField: r2->field_f = r0
    //     0x15e6138: stur            w0, [x2, #0xf]
    // 0x15e613c: r1 = <Widget>
    //     0x15e613c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15e6140: r0 = AllocateGrowableArray()
    //     0x15e6140: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15e6144: mov             x1, x0
    // 0x15e6148: ldur            x0, [fp, #-0x18]
    // 0x15e614c: stur            x1, [fp, #-8]
    // 0x15e6150: StoreField: r1->field_f = r0
    //     0x15e6150: stur            w0, [x1, #0xf]
    // 0x15e6154: r0 = 2
    //     0x15e6154: movz            x0, #0x2
    // 0x15e6158: StoreField: r1->field_b = r0
    //     0x15e6158: stur            w0, [x1, #0xb]
    // 0x15e615c: r0 = AppBar()
    //     0x15e615c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e6160: stur            x0, [fp, #-0x18]
    // 0x15e6164: ldur            x16, [fp, #-0x10]
    // 0x15e6168: ldur            lr, [fp, #-8]
    // 0x15e616c: stp             lr, x16, [SP]
    // 0x15e6170: mov             x1, x0
    // 0x15e6174: ldur            x2, [fp, #-0x20]
    // 0x15e6178: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15e6178: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15e617c: ldr             x4, [x4, #0xa58]
    // 0x15e6180: r0 = AppBar()
    //     0x15e6180: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e6184: ldur            x0, [fp, #-0x18]
    // 0x15e6188: LeaveFrame
    //     0x15e6188: mov             SP, fp
    //     0x15e618c: ldp             fp, lr, [SP], #0x10
    // 0x15e6190: ret
    //     0x15e6190: ret             
    // 0x15e6194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e6194: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e6198: b               #0x15e5f08
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15e619c, size: 0x304
    // 0x15e619c: EnterFrame
    //     0x15e619c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e61a0: mov             fp, SP
    // 0x15e61a4: AllocStack(0x58)
    //     0x15e61a4: sub             SP, SP, #0x58
    // 0x15e61a8: SetupParameters()
    //     0x15e61a8: ldr             x0, [fp, #0x10]
    //     0x15e61ac: ldur            w2, [x0, #0x17]
    //     0x15e61b0: add             x2, x2, HEAP, lsl #32
    //     0x15e61b4: stur            x2, [fp, #-8]
    // 0x15e61b8: CheckStackOverflow
    //     0x15e61b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e61bc: cmp             SP, x16
    //     0x15e61c0: b.ls            #0x15e6498
    // 0x15e61c4: LoadField: r1 = r2->field_f
    //     0x15e61c4: ldur            w1, [x2, #0xf]
    // 0x15e61c8: DecompressPointer r1
    //     0x15e61c8: add             x1, x1, HEAP, lsl #32
    // 0x15e61cc: r0 = controller()
    //     0x15e61cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e61d0: LoadField: r1 = r0->field_5b
    //     0x15e61d0: ldur            w1, [x0, #0x5b]
    // 0x15e61d4: DecompressPointer r1
    //     0x15e61d4: add             x1, x1, HEAP, lsl #32
    // 0x15e61d8: r0 = value()
    //     0x15e61d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e61dc: LoadField: r1 = r0->field_1f
    //     0x15e61dc: ldur            w1, [x0, #0x1f]
    // 0x15e61e0: DecompressPointer r1
    //     0x15e61e0: add             x1, x1, HEAP, lsl #32
    // 0x15e61e4: cmp             w1, NULL
    // 0x15e61e8: b.ne            #0x15e61f4
    // 0x15e61ec: r0 = Null
    //     0x15e61ec: mov             x0, NULL
    // 0x15e61f0: b               #0x15e61fc
    // 0x15e61f4: LoadField: r0 = r1->field_7
    //     0x15e61f4: ldur            w0, [x1, #7]
    // 0x15e61f8: DecompressPointer r0
    //     0x15e61f8: add             x0, x0, HEAP, lsl #32
    // 0x15e61fc: cmp             w0, NULL
    // 0x15e6200: b.ne            #0x15e620c
    // 0x15e6204: r0 = false
    //     0x15e6204: add             x0, NULL, #0x30  ; false
    // 0x15e6208: b               #0x15e6400
    // 0x15e620c: tbnz            w0, #4, #0x15e63fc
    // 0x15e6210: ldur            x0, [fp, #-8]
    // 0x15e6214: LoadField: r1 = r0->field_f
    //     0x15e6214: ldur            w1, [x0, #0xf]
    // 0x15e6218: DecompressPointer r1
    //     0x15e6218: add             x1, x1, HEAP, lsl #32
    // 0x15e621c: r0 = controller()
    //     0x15e621c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6220: LoadField: r1 = r0->field_6f
    //     0x15e6220: ldur            w1, [x0, #0x6f]
    // 0x15e6224: DecompressPointer r1
    //     0x15e6224: add             x1, x1, HEAP, lsl #32
    // 0x15e6228: r0 = value()
    //     0x15e6228: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e622c: mov             x2, x0
    // 0x15e6230: ldur            x0, [fp, #-8]
    // 0x15e6234: stur            x2, [fp, #-0x10]
    // 0x15e6238: LoadField: r1 = r0->field_13
    //     0x15e6238: ldur            w1, [x0, #0x13]
    // 0x15e623c: DecompressPointer r1
    //     0x15e623c: add             x1, x1, HEAP, lsl #32
    // 0x15e6240: r0 = of()
    //     0x15e6240: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e6244: LoadField: r2 = r0->field_5b
    //     0x15e6244: ldur            w2, [x0, #0x5b]
    // 0x15e6248: DecompressPointer r2
    //     0x15e6248: add             x2, x2, HEAP, lsl #32
    // 0x15e624c: ldur            x0, [fp, #-8]
    // 0x15e6250: stur            x2, [fp, #-0x18]
    // 0x15e6254: LoadField: r1 = r0->field_f
    //     0x15e6254: ldur            w1, [x0, #0xf]
    // 0x15e6258: DecompressPointer r1
    //     0x15e6258: add             x1, x1, HEAP, lsl #32
    // 0x15e625c: r0 = controller()
    //     0x15e625c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6260: LoadField: r1 = r0->field_73
    //     0x15e6260: ldur            w1, [x0, #0x73]
    // 0x15e6264: DecompressPointer r1
    //     0x15e6264: add             x1, x1, HEAP, lsl #32
    // 0x15e6268: r0 = value()
    //     0x15e6268: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e626c: cmp             w0, NULL
    // 0x15e6270: r16 = true
    //     0x15e6270: add             x16, NULL, #0x20  ; true
    // 0x15e6274: r17 = false
    //     0x15e6274: add             x17, NULL, #0x30  ; false
    // 0x15e6278: csel            x2, x16, x17, ne
    // 0x15e627c: ldur            x0, [fp, #-8]
    // 0x15e6280: stur            x2, [fp, #-0x20]
    // 0x15e6284: LoadField: r1 = r0->field_f
    //     0x15e6284: ldur            w1, [x0, #0xf]
    // 0x15e6288: DecompressPointer r1
    //     0x15e6288: add             x1, x1, HEAP, lsl #32
    // 0x15e628c: r0 = controller()
    //     0x15e628c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e6290: LoadField: r1 = r0->field_73
    //     0x15e6290: ldur            w1, [x0, #0x73]
    // 0x15e6294: DecompressPointer r1
    //     0x15e6294: add             x1, x1, HEAP, lsl #32
    // 0x15e6298: r0 = value()
    //     0x15e6298: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e629c: str             x0, [SP]
    // 0x15e62a0: r0 = _interpolateSingle()
    //     0x15e62a0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15e62a4: mov             x2, x0
    // 0x15e62a8: ldur            x0, [fp, #-8]
    // 0x15e62ac: stur            x2, [fp, #-0x28]
    // 0x15e62b0: LoadField: r1 = r0->field_13
    //     0x15e62b0: ldur            w1, [x0, #0x13]
    // 0x15e62b4: DecompressPointer r1
    //     0x15e62b4: add             x1, x1, HEAP, lsl #32
    // 0x15e62b8: r0 = of()
    //     0x15e62b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e62bc: LoadField: r1 = r0->field_87
    //     0x15e62bc: ldur            w1, [x0, #0x87]
    // 0x15e62c0: DecompressPointer r1
    //     0x15e62c0: add             x1, x1, HEAP, lsl #32
    // 0x15e62c4: LoadField: r0 = r1->field_27
    //     0x15e62c4: ldur            w0, [x1, #0x27]
    // 0x15e62c8: DecompressPointer r0
    //     0x15e62c8: add             x0, x0, HEAP, lsl #32
    // 0x15e62cc: r16 = Instance_Color
    //     0x15e62cc: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15e62d0: str             x16, [SP]
    // 0x15e62d4: mov             x1, x0
    // 0x15e62d8: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15e62d8: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15e62dc: ldr             x4, [x4, #0xf40]
    // 0x15e62e0: r0 = copyWith()
    //     0x15e62e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e62e4: stur            x0, [fp, #-0x30]
    // 0x15e62e8: r0 = Text()
    //     0x15e62e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e62ec: mov             x2, x0
    // 0x15e62f0: ldur            x0, [fp, #-0x28]
    // 0x15e62f4: stur            x2, [fp, #-0x38]
    // 0x15e62f8: StoreField: r2->field_b = r0
    //     0x15e62f8: stur            w0, [x2, #0xb]
    // 0x15e62fc: ldur            x0, [fp, #-0x30]
    // 0x15e6300: StoreField: r2->field_13 = r0
    //     0x15e6300: stur            w0, [x2, #0x13]
    // 0x15e6304: ldur            x0, [fp, #-8]
    // 0x15e6308: LoadField: r1 = r0->field_13
    //     0x15e6308: ldur            w1, [x0, #0x13]
    // 0x15e630c: DecompressPointer r1
    //     0x15e630c: add             x1, x1, HEAP, lsl #32
    // 0x15e6310: r0 = of()
    //     0x15e6310: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e6314: LoadField: r1 = r0->field_5b
    //     0x15e6314: ldur            w1, [x0, #0x5b]
    // 0x15e6318: DecompressPointer r1
    //     0x15e6318: add             x1, x1, HEAP, lsl #32
    // 0x15e631c: stur            x1, [fp, #-8]
    // 0x15e6320: r0 = ColorFilter()
    //     0x15e6320: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e6324: mov             x1, x0
    // 0x15e6328: ldur            x0, [fp, #-8]
    // 0x15e632c: stur            x1, [fp, #-0x28]
    // 0x15e6330: StoreField: r1->field_7 = r0
    //     0x15e6330: stur            w0, [x1, #7]
    // 0x15e6334: r0 = Instance_BlendMode
    //     0x15e6334: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e6338: ldr             x0, [x0, #0xb30]
    // 0x15e633c: StoreField: r1->field_b = r0
    //     0x15e633c: stur            w0, [x1, #0xb]
    // 0x15e6340: r0 = 1
    //     0x15e6340: movz            x0, #0x1
    // 0x15e6344: StoreField: r1->field_13 = r0
    //     0x15e6344: stur            x0, [x1, #0x13]
    // 0x15e6348: r0 = SvgPicture()
    //     0x15e6348: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e634c: stur            x0, [fp, #-8]
    // 0x15e6350: r16 = Instance_BoxFit
    //     0x15e6350: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15e6354: ldr             x16, [x16, #0xb18]
    // 0x15e6358: r30 = 24.000000
    //     0x15e6358: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e635c: ldr             lr, [lr, #0xba8]
    // 0x15e6360: stp             lr, x16, [SP, #0x10]
    // 0x15e6364: r16 = 24.000000
    //     0x15e6364: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15e6368: ldr             x16, [x16, #0xba8]
    // 0x15e636c: ldur            lr, [fp, #-0x28]
    // 0x15e6370: stp             lr, x16, [SP]
    // 0x15e6374: mov             x1, x0
    // 0x15e6378: r2 = "assets/images/shopping_bag.svg"
    //     0x15e6378: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15e637c: ldr             x2, [x2, #0xa60]
    // 0x15e6380: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15e6380: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15e6384: ldr             x4, [x4, #0xa68]
    // 0x15e6388: r0 = SvgPicture.asset()
    //     0x15e6388: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e638c: r0 = Badge()
    //     0x15e638c: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15e6390: mov             x1, x0
    // 0x15e6394: ldur            x0, [fp, #-0x18]
    // 0x15e6398: stur            x1, [fp, #-0x28]
    // 0x15e639c: StoreField: r1->field_b = r0
    //     0x15e639c: stur            w0, [x1, #0xb]
    // 0x15e63a0: ldur            x0, [fp, #-0x38]
    // 0x15e63a4: StoreField: r1->field_27 = r0
    //     0x15e63a4: stur            w0, [x1, #0x27]
    // 0x15e63a8: ldur            x0, [fp, #-0x20]
    // 0x15e63ac: StoreField: r1->field_2b = r0
    //     0x15e63ac: stur            w0, [x1, #0x2b]
    // 0x15e63b0: ldur            x0, [fp, #-8]
    // 0x15e63b4: StoreField: r1->field_2f = r0
    //     0x15e63b4: stur            w0, [x1, #0x2f]
    // 0x15e63b8: r0 = Visibility()
    //     0x15e63b8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15e63bc: mov             x1, x0
    // 0x15e63c0: ldur            x0, [fp, #-0x28]
    // 0x15e63c4: StoreField: r1->field_b = r0
    //     0x15e63c4: stur            w0, [x1, #0xb]
    // 0x15e63c8: r0 = Instance_SizedBox
    //     0x15e63c8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15e63cc: StoreField: r1->field_f = r0
    //     0x15e63cc: stur            w0, [x1, #0xf]
    // 0x15e63d0: ldur            x0, [fp, #-0x10]
    // 0x15e63d4: StoreField: r1->field_13 = r0
    //     0x15e63d4: stur            w0, [x1, #0x13]
    // 0x15e63d8: r0 = false
    //     0x15e63d8: add             x0, NULL, #0x30  ; false
    // 0x15e63dc: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e63dc: stur            w0, [x1, #0x17]
    // 0x15e63e0: StoreField: r1->field_1b = r0
    //     0x15e63e0: stur            w0, [x1, #0x1b]
    // 0x15e63e4: StoreField: r1->field_1f = r0
    //     0x15e63e4: stur            w0, [x1, #0x1f]
    // 0x15e63e8: StoreField: r1->field_23 = r0
    //     0x15e63e8: stur            w0, [x1, #0x23]
    // 0x15e63ec: StoreField: r1->field_27 = r0
    //     0x15e63ec: stur            w0, [x1, #0x27]
    // 0x15e63f0: StoreField: r1->field_2b = r0
    //     0x15e63f0: stur            w0, [x1, #0x2b]
    // 0x15e63f4: mov             x0, x1
    // 0x15e63f8: b               #0x15e6418
    // 0x15e63fc: r0 = false
    //     0x15e63fc: add             x0, NULL, #0x30  ; false
    // 0x15e6400: r0 = Container()
    //     0x15e6400: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15e6404: mov             x1, x0
    // 0x15e6408: stur            x0, [fp, #-8]
    // 0x15e640c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15e640c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15e6410: r0 = Container()
    //     0x15e6410: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15e6414: ldur            x0, [fp, #-8]
    // 0x15e6418: stur            x0, [fp, #-8]
    // 0x15e641c: r0 = InkWell()
    //     0x15e641c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e6420: mov             x3, x0
    // 0x15e6424: ldur            x0, [fp, #-8]
    // 0x15e6428: stur            x3, [fp, #-0x10]
    // 0x15e642c: StoreField: r3->field_b = r0
    //     0x15e642c: stur            w0, [x3, #0xb]
    // 0x15e6430: r1 = Function '<anonymous closure>':.
    //     0x15e6430: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e888] AnonymousClosure: (0x15d7f78), in [package:customer_app/app/presentation/views/line/testimonials/testimonials_view.dart] TestimonialsView::appBar (0x15eea18)
    //     0x15e6434: ldr             x1, [x1, #0x888]
    // 0x15e6438: r2 = Null
    //     0x15e6438: mov             x2, NULL
    // 0x15e643c: r0 = AllocateClosure()
    //     0x15e643c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e6440: mov             x1, x0
    // 0x15e6444: ldur            x0, [fp, #-0x10]
    // 0x15e6448: StoreField: r0->field_f = r1
    //     0x15e6448: stur            w1, [x0, #0xf]
    // 0x15e644c: r1 = true
    //     0x15e644c: add             x1, NULL, #0x20  ; true
    // 0x15e6450: StoreField: r0->field_43 = r1
    //     0x15e6450: stur            w1, [x0, #0x43]
    // 0x15e6454: r2 = Instance_BoxShape
    //     0x15e6454: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e6458: ldr             x2, [x2, #0x80]
    // 0x15e645c: StoreField: r0->field_47 = r2
    //     0x15e645c: stur            w2, [x0, #0x47]
    // 0x15e6460: StoreField: r0->field_6f = r1
    //     0x15e6460: stur            w1, [x0, #0x6f]
    // 0x15e6464: r2 = false
    //     0x15e6464: add             x2, NULL, #0x30  ; false
    // 0x15e6468: StoreField: r0->field_73 = r2
    //     0x15e6468: stur            w2, [x0, #0x73]
    // 0x15e646c: StoreField: r0->field_83 = r1
    //     0x15e646c: stur            w1, [x0, #0x83]
    // 0x15e6470: StoreField: r0->field_7b = r2
    //     0x15e6470: stur            w2, [x0, #0x7b]
    // 0x15e6474: r0 = Padding()
    //     0x15e6474: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15e6478: r1 = Instance_EdgeInsets
    //     0x15e6478: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15e647c: ldr             x1, [x1, #0xa78]
    // 0x15e6480: StoreField: r0->field_f = r1
    //     0x15e6480: stur            w1, [x0, #0xf]
    // 0x15e6484: ldur            x1, [fp, #-0x10]
    // 0x15e6488: StoreField: r0->field_b = r1
    //     0x15e6488: stur            w1, [x0, #0xb]
    // 0x15e648c: LeaveFrame
    //     0x15e648c: mov             SP, fp
    //     0x15e6490: ldp             fp, lr, [SP], #0x10
    // 0x15e6494: ret
    //     0x15e6494: ret             
    // 0x15e6498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e6498: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e649c: b               #0x15e61c4
  }
}
